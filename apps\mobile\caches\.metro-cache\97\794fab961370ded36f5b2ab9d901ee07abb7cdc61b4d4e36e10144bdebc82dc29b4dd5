{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] ❌ face-api.js also failed:', faceApiError);\n          }\n        }\n\n        // Method 3: ALWAYS use fallback for testing - Force face detection\n        console.log('[EchoCameraWeb] 🧪 FORCING fallback mock face detection for testing...');\n        detectedFaces = [{\n          boundingBox: {\n            xCenter: 0.5,\n            // Center of image\n            yCenter: 0.4,\n            // Slightly above center (typical face position)\n            width: 0.35,\n            // 35% of image width\n            height: 0.45 // 45% of image height\n          }\n        }];\n        console.log(`[EchoCameraWeb] 🧪 FORCED mock detection: 1 face at center of image`);\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect - VERY AGGRESSIVE for testing\n            const pixelSize = Math.max(20, Math.min(paddedWidth, paddedHeight) / 8); // Much larger pixels for obvious effect\n            console.log(`[EchoCameraWeb] 🔲 Using LARGE pixel size: ${pixelSize}px for obvious blurring effect`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} blurring applied successfully`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1358, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 56, 128, 54], [126, 58, 128, 56, "photo"], [126, 63, 128, 61], [126, 64, 128, 62, "uri"], [126, 67, 128, 65], [126, 68, 128, 66], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6, "console"], [130, 15, 132, 13], [130, 16, 132, 14, "log"], [130, 19, 132, 17], [130, 20, 132, 18], [130, 73, 132, 71], [130, 74, 132, 72], [131, 8, 133, 6], [131, 14, 133, 12, "processImageWithFaceBlur"], [131, 38, 133, 36], [131, 39, 133, 37, "photo"], [131, 44, 133, 42], [131, 45, 133, 43, "uri"], [131, 48, 133, 46], [131, 49, 133, 47], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "log"], [132, 19, 134, 17], [132, 20, 134, 18], [132, 71, 134, 69], [132, 72, 134, 70], [133, 6, 135, 4], [133, 7, 135, 5], [133, 8, 135, 6], [133, 15, 135, 13, "error"], [133, 20, 135, 18], [133, 22, 135, 20], [134, 8, 136, 6, "console"], [134, 15, 136, 13], [134, 16, 136, 14, "error"], [134, 21, 136, 19], [134, 22, 136, 20], [134, 54, 136, 52], [134, 56, 136, 54, "error"], [134, 61, 136, 59], [134, 62, 136, 60], [135, 8, 137, 6, "setErrorMessage"], [135, 23, 137, 21], [135, 24, 137, 22], [135, 68, 137, 66], [135, 69, 137, 67], [136, 8, 138, 6, "setProcessingState"], [136, 26, 138, 24], [136, 27, 138, 25], [136, 34, 138, 32], [136, 35, 138, 33], [137, 6, 139, 4], [138, 4, 140, 2], [138, 5, 140, 3], [138, 7, 140, 5], [138, 9, 140, 7], [138, 10, 140, 8], [139, 4, 141, 2], [140, 4, 142, 2], [140, 10, 142, 8, "processImageWithFaceBlur"], [140, 34, 142, 32], [140, 37, 142, 35], [140, 43, 142, 42, "photoUri"], [140, 51, 142, 58], [140, 55, 142, 63], [141, 6, 143, 4], [141, 10, 143, 8], [142, 8, 144, 6, "console"], [142, 15, 144, 13], [142, 16, 144, 14, "log"], [142, 19, 144, 17], [142, 20, 144, 18], [142, 84, 144, 82], [142, 85, 144, 83], [143, 8, 145, 6, "setProcessingState"], [143, 26, 145, 24], [143, 27, 145, 25], [143, 39, 145, 37], [143, 40, 145, 38], [144, 8, 146, 6, "setProcessingProgress"], [144, 29, 146, 27], [144, 30, 146, 28], [144, 32, 146, 30], [144, 33, 146, 31], [146, 8, 148, 6], [147, 8, 149, 6], [147, 14, 149, 12, "canvas"], [147, 20, 149, 18], [147, 23, 149, 21, "document"], [147, 31, 149, 29], [147, 32, 149, 30, "createElement"], [147, 45, 149, 43], [147, 46, 149, 44], [147, 54, 149, 52], [147, 55, 149, 53], [148, 8, 150, 6], [148, 14, 150, 12, "ctx"], [148, 17, 150, 15], [148, 20, 150, 18, "canvas"], [148, 26, 150, 24], [148, 27, 150, 25, "getContext"], [148, 37, 150, 35], [148, 38, 150, 36], [148, 42, 150, 40], [148, 43, 150, 41], [149, 8, 151, 6], [149, 12, 151, 10], [149, 13, 151, 11, "ctx"], [149, 16, 151, 14], [149, 18, 151, 16], [149, 24, 151, 22], [149, 28, 151, 26, "Error"], [149, 33, 151, 31], [149, 34, 151, 32], [149, 64, 151, 62], [149, 65, 151, 63], [151, 8, 153, 6], [152, 8, 154, 6], [152, 14, 154, 12, "img"], [152, 17, 154, 15], [152, 20, 154, 18], [152, 24, 154, 22, "Image"], [152, 29, 154, 27], [152, 30, 154, 28], [152, 31, 154, 29], [153, 8, 155, 6], [153, 14, 155, 12], [153, 18, 155, 16, "Promise"], [153, 25, 155, 23], [153, 26, 155, 24], [153, 27, 155, 25, "resolve"], [153, 34, 155, 32], [153, 36, 155, 34, "reject"], [153, 42, 155, 40], [153, 47, 155, 45], [154, 10, 156, 8, "img"], [154, 13, 156, 11], [154, 14, 156, 12, "onload"], [154, 20, 156, 18], [154, 23, 156, 21, "resolve"], [154, 30, 156, 28], [155, 10, 157, 8, "img"], [155, 13, 157, 11], [155, 14, 157, 12, "onerror"], [155, 21, 157, 19], [155, 24, 157, 22, "reject"], [155, 30, 157, 28], [156, 10, 158, 8, "img"], [156, 13, 158, 11], [156, 14, 158, 12, "src"], [156, 17, 158, 15], [156, 20, 158, 18, "photoUri"], [156, 28, 158, 26], [157, 8, 159, 6], [157, 9, 159, 7], [157, 10, 159, 8], [159, 8, 161, 6], [160, 8, 162, 6, "canvas"], [160, 14, 162, 12], [160, 15, 162, 13, "width"], [160, 20, 162, 18], [160, 23, 162, 21, "img"], [160, 26, 162, 24], [160, 27, 162, 25, "width"], [160, 32, 162, 30], [161, 8, 163, 6, "canvas"], [161, 14, 163, 12], [161, 15, 163, 13, "height"], [161, 21, 163, 19], [161, 24, 163, 22, "img"], [161, 27, 163, 25], [161, 28, 163, 26, "height"], [161, 34, 163, 32], [162, 8, 164, 6, "console"], [162, 15, 164, 13], [162, 16, 164, 14, "log"], [162, 19, 164, 17], [162, 20, 164, 18], [162, 54, 164, 52], [162, 56, 164, 54], [163, 10, 164, 56, "width"], [163, 15, 164, 61], [163, 17, 164, 63, "img"], [163, 20, 164, 66], [163, 21, 164, 67, "width"], [163, 26, 164, 72], [164, 10, 164, 74, "height"], [164, 16, 164, 80], [164, 18, 164, 82, "img"], [164, 21, 164, 85], [164, 22, 164, 86, "height"], [165, 8, 164, 93], [165, 9, 164, 94], [165, 10, 164, 95], [167, 8, 166, 6], [168, 8, 167, 6, "ctx"], [168, 11, 167, 9], [168, 12, 167, 10, "drawImage"], [168, 21, 167, 19], [168, 22, 167, 20, "img"], [168, 25, 167, 23], [168, 27, 167, 25], [168, 28, 167, 26], [168, 30, 167, 28], [168, 31, 167, 29], [168, 32, 167, 30], [169, 8, 168, 6, "console"], [169, 15, 168, 13], [169, 16, 168, 14, "log"], [169, 19, 168, 17], [169, 20, 168, 18], [169, 72, 168, 70], [169, 73, 168, 71], [170, 8, 170, 6, "setProcessingProgress"], [170, 29, 170, 27], [170, 30, 170, 28], [170, 32, 170, 30], [170, 33, 170, 31], [172, 8, 172, 6], [173, 8, 173, 6], [173, 12, 173, 10, "detectedFaces"], [173, 25, 173, 23], [173, 28, 173, 26], [173, 30, 173, 28], [174, 8, 175, 6, "console"], [174, 15, 175, 13], [174, 16, 175, 14, "log"], [174, 19, 175, 17], [174, 20, 175, 18], [174, 74, 175, 72], [174, 76, 175, 74], [175, 10, 176, 8, "width"], [175, 15, 176, 13], [175, 17, 176, 15, "img"], [175, 20, 176, 18], [175, 21, 176, 19, "width"], [175, 26, 176, 24], [176, 10, 177, 8, "height"], [176, 16, 177, 14], [176, 18, 177, 16, "img"], [176, 21, 177, 19], [176, 22, 177, 20, "height"], [176, 28, 177, 26], [177, 10, 178, 8, "src"], [177, 13, 178, 11], [177, 15, 178, 13, "photoUri"], [177, 23, 178, 21], [177, 24, 178, 22, "substring"], [177, 33, 178, 31], [177, 34, 178, 32], [177, 35, 178, 33], [177, 37, 178, 35], [177, 39, 178, 37], [177, 40, 178, 38], [177, 43, 178, 41], [178, 8, 179, 6], [178, 9, 179, 7], [178, 10, 179, 8], [180, 8, 181, 6], [181, 8, 182, 6], [181, 12, 182, 10], [182, 10, 183, 8], [182, 14, 183, 12], [182, 28, 183, 26], [182, 32, 183, 30, "window"], [182, 38, 183, 36], [182, 40, 183, 38], [183, 12, 184, 10, "console"], [183, 19, 184, 17], [183, 20, 184, 18, "log"], [183, 23, 184, 21], [183, 24, 184, 22], [183, 105, 184, 103], [183, 106, 184, 104], [184, 12, 185, 10], [184, 18, 185, 16, "faceDetector"], [184, 30, 185, 28], [184, 33, 185, 31], [184, 37, 185, 36, "window"], [184, 43, 185, 42], [184, 44, 185, 51, "FaceDetector"], [184, 56, 185, 63], [184, 57, 185, 64], [185, 14, 186, 12, "maxDetectedFaces"], [185, 30, 186, 28], [185, 32, 186, 30], [185, 34, 186, 32], [186, 14, 187, 12, "fastMode"], [186, 22, 187, 20], [186, 24, 187, 22], [187, 12, 188, 10], [187, 13, 188, 11], [187, 14, 188, 12], [188, 12, 190, 10], [188, 18, 190, 16, "browserDetections"], [188, 35, 190, 33], [188, 38, 190, 36], [188, 44, 190, 42, "faceDetector"], [188, 56, 190, 54], [188, 57, 190, 55, "detect"], [188, 63, 190, 61], [188, 64, 190, 62, "img"], [188, 67, 190, 65], [188, 68, 190, 66], [189, 12, 191, 10, "detectedFaces"], [189, 25, 191, 23], [189, 28, 191, 26, "browserDetections"], [189, 45, 191, 43], [189, 46, 191, 44, "map"], [189, 49, 191, 47], [189, 50, 191, 49, "detection"], [189, 59, 191, 63], [189, 64, 191, 69], [190, 14, 192, 12, "boundingBox"], [190, 25, 192, 23], [190, 27, 192, 25], [191, 16, 193, 14, "xCenter"], [191, 23, 193, 21], [191, 25, 193, 23], [191, 26, 193, 24, "detection"], [191, 35, 193, 33], [191, 36, 193, 34, "boundingBox"], [191, 47, 193, 45], [191, 48, 193, 46, "x"], [191, 49, 193, 47], [191, 52, 193, 50, "detection"], [191, 61, 193, 59], [191, 62, 193, 60, "boundingBox"], [191, 73, 193, 71], [191, 74, 193, 72, "width"], [191, 79, 193, 77], [191, 82, 193, 80], [191, 83, 193, 81], [191, 87, 193, 85, "img"], [191, 90, 193, 88], [191, 91, 193, 89, "width"], [191, 96, 193, 94], [192, 16, 194, 14, "yCenter"], [192, 23, 194, 21], [192, 25, 194, 23], [192, 26, 194, 24, "detection"], [192, 35, 194, 33], [192, 36, 194, 34, "boundingBox"], [192, 47, 194, 45], [192, 48, 194, 46, "y"], [192, 49, 194, 47], [192, 52, 194, 50, "detection"], [192, 61, 194, 59], [192, 62, 194, 60, "boundingBox"], [192, 73, 194, 71], [192, 74, 194, 72, "height"], [192, 80, 194, 78], [192, 83, 194, 81], [192, 84, 194, 82], [192, 88, 194, 86, "img"], [192, 91, 194, 89], [192, 92, 194, 90, "height"], [192, 98, 194, 96], [193, 16, 195, 14, "width"], [193, 21, 195, 19], [193, 23, 195, 21, "detection"], [193, 32, 195, 30], [193, 33, 195, 31, "boundingBox"], [193, 44, 195, 42], [193, 45, 195, 43, "width"], [193, 50, 195, 48], [193, 53, 195, 51, "img"], [193, 56, 195, 54], [193, 57, 195, 55, "width"], [193, 62, 195, 60], [194, 16, 196, 14, "height"], [194, 22, 196, 20], [194, 24, 196, 22, "detection"], [194, 33, 196, 31], [194, 34, 196, 32, "boundingBox"], [194, 45, 196, 43], [194, 46, 196, 44, "height"], [194, 52, 196, 50], [194, 55, 196, 53, "img"], [194, 58, 196, 56], [194, 59, 196, 57, "height"], [195, 14, 197, 12], [196, 12, 198, 10], [196, 13, 198, 11], [196, 14, 198, 12], [196, 15, 198, 13], [197, 12, 199, 10, "console"], [197, 19, 199, 17], [197, 20, 199, 18, "log"], [197, 23, 199, 21], [197, 24, 199, 22], [197, 78, 199, 76, "detectedFaces"], [197, 91, 199, 89], [197, 92, 199, 90, "length"], [197, 98, 199, 96], [197, 106, 199, 104], [197, 107, 199, 105], [198, 10, 200, 8], [198, 11, 200, 9], [198, 17, 200, 15], [199, 12, 201, 10, "console"], [199, 19, 201, 17], [199, 20, 201, 18, "log"], [199, 23, 201, 21], [199, 24, 201, 22], [199, 100, 201, 98], [199, 101, 201, 99], [200, 12, 202, 10], [200, 18, 202, 16], [200, 22, 202, 20, "Error"], [200, 27, 202, 25], [200, 28, 202, 26], [200, 70, 202, 68], [200, 71, 202, 69], [201, 10, 203, 8], [202, 8, 204, 6], [202, 9, 204, 7], [202, 10, 204, 8], [202, 17, 204, 15, "browserError"], [202, 29, 204, 27], [202, 31, 204, 29], [203, 10, 205, 8, "console"], [203, 17, 205, 15], [203, 18, 205, 16, "warn"], [203, 22, 205, 20], [203, 23, 205, 21], [203, 102, 205, 100], [203, 104, 205, 102, "browserError"], [203, 116, 205, 114], [203, 117, 205, 115], [205, 10, 207, 8], [206, 10, 208, 8], [206, 14, 208, 12], [207, 12, 209, 10], [208, 12, 210, 10], [208, 16, 210, 14], [208, 17, 210, 16, "window"], [208, 23, 210, 22], [208, 24, 210, 31, "<PERSON>ap<PERSON>"], [208, 31, 210, 38], [208, 33, 210, 40], [209, 14, 211, 12], [209, 20, 211, 18], [209, 24, 211, 22, "Promise"], [209, 31, 211, 29], [209, 32, 211, 30], [209, 33, 211, 31, "resolve"], [209, 40, 211, 38], [209, 42, 211, 40, "reject"], [209, 48, 211, 46], [209, 53, 211, 51], [210, 16, 212, 14], [210, 22, 212, 20, "script"], [210, 28, 212, 26], [210, 31, 212, 29, "document"], [210, 39, 212, 37], [210, 40, 212, 38, "createElement"], [210, 53, 212, 51], [210, 54, 212, 52], [210, 62, 212, 60], [210, 63, 212, 61], [211, 16, 213, 14, "script"], [211, 22, 213, 20], [211, 23, 213, 21, "src"], [211, 26, 213, 24], [211, 29, 213, 27], [211, 99, 213, 97], [212, 16, 214, 14, "script"], [212, 22, 214, 20], [212, 23, 214, 21, "onload"], [212, 29, 214, 27], [212, 32, 214, 30, "resolve"], [212, 39, 214, 37], [213, 16, 215, 14, "script"], [213, 22, 215, 20], [213, 23, 215, 21, "onerror"], [213, 30, 215, 28], [213, 33, 215, 31, "reject"], [213, 39, 215, 37], [214, 16, 216, 14, "document"], [214, 24, 216, 22], [214, 25, 216, 23, "head"], [214, 29, 216, 27], [214, 30, 216, 28, "append<PERSON><PERSON><PERSON>"], [214, 41, 216, 39], [214, 42, 216, 40, "script"], [214, 48, 216, 46], [214, 49, 216, 47], [215, 14, 217, 12], [215, 15, 217, 13], [215, 16, 217, 14], [216, 12, 218, 10], [217, 12, 220, 10], [217, 18, 220, 16, "<PERSON>ap<PERSON>"], [217, 25, 220, 23], [217, 28, 220, 27, "window"], [217, 34, 220, 33], [217, 35, 220, 42, "<PERSON>ap<PERSON>"], [217, 42, 220, 49], [219, 12, 222, 10], [220, 12, 223, 10], [220, 18, 223, 16, "Promise"], [220, 25, 223, 23], [220, 26, 223, 24, "all"], [220, 29, 223, 27], [220, 30, 223, 28], [220, 31, 224, 12, "<PERSON>ap<PERSON>"], [220, 38, 224, 19], [220, 39, 224, 20, "nets"], [220, 43, 224, 24], [220, 44, 224, 25, "tinyFaceDetector"], [220, 60, 224, 41], [220, 61, 224, 42, "loadFromUri"], [220, 72, 224, 53], [220, 73, 224, 54], [220, 130, 224, 111], [220, 131, 224, 112], [220, 133, 225, 12, "<PERSON>ap<PERSON>"], [220, 140, 225, 19], [220, 141, 225, 20, "nets"], [220, 145, 225, 24], [220, 146, 225, 25, "faceLandmark68Net"], [220, 163, 225, 42], [220, 164, 225, 43, "loadFromUri"], [220, 175, 225, 54], [220, 176, 225, 55], [220, 233, 225, 112], [220, 234, 225, 113], [220, 235, 226, 11], [220, 236, 226, 12], [222, 12, 228, 10], [223, 12, 229, 10], [223, 18, 229, 16, "faceDetections"], [223, 32, 229, 30], [223, 35, 229, 33], [223, 41, 229, 39, "<PERSON>ap<PERSON>"], [223, 48, 229, 46], [223, 49, 229, 47, "detectAllFaces"], [223, 63, 229, 61], [223, 64, 229, 62, "img"], [223, 67, 229, 65], [223, 69, 229, 67], [223, 73, 229, 71, "<PERSON>ap<PERSON>"], [223, 80, 229, 78], [223, 81, 229, 79, "TinyFaceDetectorOptions"], [223, 104, 229, 102], [223, 105, 229, 103], [223, 106, 229, 104], [223, 107, 229, 105], [224, 12, 231, 10, "detectedFaces"], [224, 25, 231, 23], [224, 28, 231, 26, "faceDetections"], [224, 42, 231, 40], [224, 43, 231, 41, "map"], [224, 46, 231, 44], [224, 47, 231, 46, "detection"], [224, 56, 231, 60], [224, 61, 231, 66], [225, 14, 232, 12, "boundingBox"], [225, 25, 232, 23], [225, 27, 232, 25], [226, 16, 233, 14, "xCenter"], [226, 23, 233, 21], [226, 25, 233, 23], [226, 26, 233, 24, "detection"], [226, 35, 233, 33], [226, 36, 233, 34, "box"], [226, 39, 233, 37], [226, 40, 233, 38, "x"], [226, 41, 233, 39], [226, 44, 233, 42, "detection"], [226, 53, 233, 51], [226, 54, 233, 52, "box"], [226, 57, 233, 55], [226, 58, 233, 56, "width"], [226, 63, 233, 61], [226, 66, 233, 64], [226, 67, 233, 65], [226, 71, 233, 69, "img"], [226, 74, 233, 72], [226, 75, 233, 73, "width"], [226, 80, 233, 78], [227, 16, 234, 14, "yCenter"], [227, 23, 234, 21], [227, 25, 234, 23], [227, 26, 234, 24, "detection"], [227, 35, 234, 33], [227, 36, 234, 34, "box"], [227, 39, 234, 37], [227, 40, 234, 38, "y"], [227, 41, 234, 39], [227, 44, 234, 42, "detection"], [227, 53, 234, 51], [227, 54, 234, 52, "box"], [227, 57, 234, 55], [227, 58, 234, 56, "height"], [227, 64, 234, 62], [227, 67, 234, 65], [227, 68, 234, 66], [227, 72, 234, 70, "img"], [227, 75, 234, 73], [227, 76, 234, 74, "height"], [227, 82, 234, 80], [228, 16, 235, 14, "width"], [228, 21, 235, 19], [228, 23, 235, 21, "detection"], [228, 32, 235, 30], [228, 33, 235, 31, "box"], [228, 36, 235, 34], [228, 37, 235, 35, "width"], [228, 42, 235, 40], [228, 45, 235, 43, "img"], [228, 48, 235, 46], [228, 49, 235, 47, "width"], [228, 54, 235, 52], [229, 16, 236, 14, "height"], [229, 22, 236, 20], [229, 24, 236, 22, "detection"], [229, 33, 236, 31], [229, 34, 236, 32, "box"], [229, 37, 236, 35], [229, 38, 236, 36, "height"], [229, 44, 236, 42], [229, 47, 236, 45, "img"], [229, 50, 236, 48], [229, 51, 236, 49, "height"], [230, 14, 237, 12], [231, 12, 238, 10], [231, 13, 238, 11], [231, 14, 238, 12], [231, 15, 238, 13], [232, 12, 240, 10, "console"], [232, 19, 240, 17], [232, 20, 240, 18, "log"], [232, 23, 240, 21], [232, 24, 240, 22], [232, 63, 240, 61, "detectedFaces"], [232, 76, 240, 74], [232, 77, 240, 75, "length"], [232, 83, 240, 81], [232, 91, 240, 89], [232, 92, 240, 90], [233, 10, 241, 8], [233, 11, 241, 9], [233, 12, 241, 10], [233, 19, 241, 17, "faceApiError"], [233, 31, 241, 29], [233, 33, 241, 31], [234, 12, 242, 10, "console"], [234, 19, 242, 17], [234, 20, 242, 18, "warn"], [234, 24, 242, 22], [234, 25, 242, 23], [234, 69, 242, 67], [234, 71, 242, 69, "faceApiError"], [234, 83, 242, 81], [234, 84, 242, 82], [235, 10, 243, 8], [236, 8, 244, 6], [238, 8, 246, 6], [239, 8, 247, 6, "console"], [239, 15, 247, 13], [239, 16, 247, 14, "log"], [239, 19, 247, 17], [239, 20, 247, 18], [239, 92, 247, 90], [239, 93, 247, 91], [240, 8, 248, 6, "detectedFaces"], [240, 21, 248, 19], [240, 24, 248, 22], [240, 25, 248, 23], [241, 10, 249, 8, "boundingBox"], [241, 21, 249, 19], [241, 23, 249, 21], [242, 12, 250, 10, "xCenter"], [242, 19, 250, 17], [242, 21, 250, 19], [242, 24, 250, 22], [243, 12, 250, 25], [244, 12, 251, 10, "yCenter"], [244, 19, 251, 17], [244, 21, 251, 19], [244, 24, 251, 22], [245, 12, 251, 25], [246, 12, 252, 10, "width"], [246, 17, 252, 15], [246, 19, 252, 17], [246, 23, 252, 21], [247, 12, 252, 25], [248, 12, 253, 10, "height"], [248, 18, 253, 16], [248, 20, 253, 18], [248, 24, 253, 22], [248, 25, 253, 25], [249, 10, 254, 8], [250, 8, 255, 6], [250, 9, 255, 7], [250, 10, 255, 8], [251, 8, 256, 6, "console"], [251, 15, 256, 13], [251, 16, 256, 14, "log"], [251, 19, 256, 17], [251, 20, 256, 18], [251, 89, 256, 87], [251, 90, 256, 88], [252, 8, 258, 6, "console"], [252, 15, 258, 13], [252, 16, 258, 14, "log"], [252, 19, 258, 17], [252, 20, 258, 18], [252, 72, 258, 70, "detectedFaces"], [252, 85, 258, 83], [252, 86, 258, 84, "length"], [252, 92, 258, 90], [252, 100, 258, 98], [252, 101, 258, 99], [253, 8, 259, 6], [253, 12, 259, 10, "detectedFaces"], [253, 25, 259, 23], [253, 26, 259, 24, "length"], [253, 32, 259, 30], [253, 35, 259, 33], [253, 36, 259, 34], [253, 38, 259, 36], [254, 10, 260, 8, "console"], [254, 17, 260, 15], [254, 18, 260, 16, "log"], [254, 21, 260, 19], [254, 22, 260, 20], [254, 66, 260, 64], [254, 68, 260, 66, "detectedFaces"], [254, 81, 260, 79], [254, 82, 260, 80, "map"], [254, 85, 260, 83], [254, 86, 260, 84], [254, 87, 260, 85, "face"], [254, 91, 260, 89], [254, 93, 260, 91, "i"], [254, 94, 260, 92], [254, 100, 260, 98], [255, 12, 261, 10, "faceNumber"], [255, 22, 261, 20], [255, 24, 261, 22, "i"], [255, 25, 261, 23], [255, 28, 261, 26], [255, 29, 261, 27], [256, 12, 262, 10, "centerX"], [256, 19, 262, 17], [256, 21, 262, 19, "face"], [256, 25, 262, 23], [256, 26, 262, 24, "boundingBox"], [256, 37, 262, 35], [256, 38, 262, 36, "xCenter"], [256, 45, 262, 43], [257, 12, 263, 10, "centerY"], [257, 19, 263, 17], [257, 21, 263, 19, "face"], [257, 25, 263, 23], [257, 26, 263, 24, "boundingBox"], [257, 37, 263, 35], [257, 38, 263, 36, "yCenter"], [257, 45, 263, 43], [258, 12, 264, 10, "width"], [258, 17, 264, 15], [258, 19, 264, 17, "face"], [258, 23, 264, 21], [258, 24, 264, 22, "boundingBox"], [258, 35, 264, 33], [258, 36, 264, 34, "width"], [258, 41, 264, 39], [259, 12, 265, 10, "height"], [259, 18, 265, 16], [259, 20, 265, 18, "face"], [259, 24, 265, 22], [259, 25, 265, 23, "boundingBox"], [259, 36, 265, 34], [259, 37, 265, 35, "height"], [260, 10, 266, 8], [260, 11, 266, 9], [260, 12, 266, 10], [260, 13, 266, 11], [260, 14, 266, 12], [261, 8, 267, 6], [261, 9, 267, 7], [261, 15, 267, 13], [262, 10, 268, 8, "console"], [262, 17, 268, 15], [262, 18, 268, 16, "log"], [262, 21, 268, 19], [262, 22, 268, 20], [262, 91, 268, 89], [262, 92, 268, 90], [263, 8, 269, 6], [264, 8, 271, 6, "setProcessingProgress"], [264, 29, 271, 27], [264, 30, 271, 28], [264, 32, 271, 30], [264, 33, 271, 31], [266, 8, 273, 6], [267, 8, 274, 6], [267, 12, 274, 10, "detectedFaces"], [267, 25, 274, 23], [267, 26, 274, 24, "length"], [267, 32, 274, 30], [267, 35, 274, 33], [267, 36, 274, 34], [267, 38, 274, 36], [268, 10, 275, 8, "detectedFaces"], [268, 23, 275, 21], [268, 24, 275, 22, "for<PERSON>ach"], [268, 31, 275, 29], [268, 32, 275, 30], [268, 33, 275, 31, "detection"], [268, 42, 275, 40], [268, 44, 275, 42, "index"], [268, 49, 275, 47], [268, 54, 275, 52], [269, 12, 276, 10], [269, 18, 276, 16, "bbox"], [269, 22, 276, 20], [269, 25, 276, 23, "detection"], [269, 34, 276, 32], [269, 35, 276, 33, "boundingBox"], [269, 46, 276, 44], [271, 12, 278, 10], [272, 12, 279, 10], [272, 18, 279, 16, "faceX"], [272, 23, 279, 21], [272, 26, 279, 24, "bbox"], [272, 30, 279, 28], [272, 31, 279, 29, "xCenter"], [272, 38, 279, 36], [272, 41, 279, 39, "img"], [272, 44, 279, 42], [272, 45, 279, 43, "width"], [272, 50, 279, 48], [272, 53, 279, 52, "bbox"], [272, 57, 279, 56], [272, 58, 279, 57, "width"], [272, 63, 279, 62], [272, 66, 279, 65, "img"], [272, 69, 279, 68], [272, 70, 279, 69, "width"], [272, 75, 279, 74], [272, 78, 279, 78], [272, 79, 279, 79], [273, 12, 280, 10], [273, 18, 280, 16, "faceY"], [273, 23, 280, 21], [273, 26, 280, 24, "bbox"], [273, 30, 280, 28], [273, 31, 280, 29, "yCenter"], [273, 38, 280, 36], [273, 41, 280, 39, "img"], [273, 44, 280, 42], [273, 45, 280, 43, "height"], [273, 51, 280, 49], [273, 54, 280, 53, "bbox"], [273, 58, 280, 57], [273, 59, 280, 58, "height"], [273, 65, 280, 64], [273, 68, 280, 67, "img"], [273, 71, 280, 70], [273, 72, 280, 71, "height"], [273, 78, 280, 77], [273, 81, 280, 81], [273, 82, 280, 82], [274, 12, 281, 10], [274, 18, 281, 16, "faceWidth"], [274, 27, 281, 25], [274, 30, 281, 28, "bbox"], [274, 34, 281, 32], [274, 35, 281, 33, "width"], [274, 40, 281, 38], [274, 43, 281, 41, "img"], [274, 46, 281, 44], [274, 47, 281, 45, "width"], [274, 52, 281, 50], [275, 12, 282, 10], [275, 18, 282, 16, "faceHeight"], [275, 28, 282, 26], [275, 31, 282, 29, "bbox"], [275, 35, 282, 33], [275, 36, 282, 34, "height"], [275, 42, 282, 40], [275, 45, 282, 43, "img"], [275, 48, 282, 46], [275, 49, 282, 47, "height"], [275, 55, 282, 53], [277, 12, 284, 10], [278, 12, 285, 10], [278, 18, 285, 16, "padding"], [278, 25, 285, 23], [278, 28, 285, 26], [278, 31, 285, 29], [278, 32, 285, 30], [278, 33, 285, 31], [279, 12, 286, 10], [279, 18, 286, 16, "paddedX"], [279, 25, 286, 23], [279, 28, 286, 26, "Math"], [279, 32, 286, 30], [279, 33, 286, 31, "max"], [279, 36, 286, 34], [279, 37, 286, 35], [279, 38, 286, 36], [279, 40, 286, 38, "faceX"], [279, 45, 286, 43], [279, 48, 286, 46, "faceWidth"], [279, 57, 286, 55], [279, 60, 286, 58, "padding"], [279, 67, 286, 65], [279, 68, 286, 66], [280, 12, 287, 10], [280, 18, 287, 16, "paddedY"], [280, 25, 287, 23], [280, 28, 287, 26, "Math"], [280, 32, 287, 30], [280, 33, 287, 31, "max"], [280, 36, 287, 34], [280, 37, 287, 35], [280, 38, 287, 36], [280, 40, 287, 38, "faceY"], [280, 45, 287, 43], [280, 48, 287, 46, "faceHeight"], [280, 58, 287, 56], [280, 61, 287, 59, "padding"], [280, 68, 287, 66], [280, 69, 287, 67], [281, 12, 288, 10], [281, 18, 288, 16, "<PERSON><PERSON><PERSON><PERSON>"], [281, 29, 288, 27], [281, 32, 288, 30, "Math"], [281, 36, 288, 34], [281, 37, 288, 35, "min"], [281, 40, 288, 38], [281, 41, 288, 39, "img"], [281, 44, 288, 42], [281, 45, 288, 43, "width"], [281, 50, 288, 48], [281, 53, 288, 51, "paddedX"], [281, 60, 288, 58], [281, 62, 288, 60, "faceWidth"], [281, 71, 288, 69], [281, 75, 288, 73], [281, 76, 288, 74], [281, 79, 288, 77], [281, 80, 288, 78], [281, 83, 288, 81, "padding"], [281, 90, 288, 88], [281, 91, 288, 89], [281, 92, 288, 90], [282, 12, 289, 10], [282, 18, 289, 16, "paddedHeight"], [282, 30, 289, 28], [282, 33, 289, 31, "Math"], [282, 37, 289, 35], [282, 38, 289, 36, "min"], [282, 41, 289, 39], [282, 42, 289, 40, "img"], [282, 45, 289, 43], [282, 46, 289, 44, "height"], [282, 52, 289, 50], [282, 55, 289, 53, "paddedY"], [282, 62, 289, 60], [282, 64, 289, 62, "faceHeight"], [282, 74, 289, 72], [282, 78, 289, 76], [282, 79, 289, 77], [282, 82, 289, 80], [282, 83, 289, 81], [282, 86, 289, 84, "padding"], [282, 93, 289, 91], [282, 94, 289, 92], [282, 95, 289, 93], [283, 12, 291, 10, "console"], [283, 19, 291, 17], [283, 20, 291, 18, "log"], [283, 23, 291, 21], [283, 24, 291, 22], [283, 60, 291, 58, "index"], [283, 65, 291, 63], [283, 68, 291, 66], [283, 69, 291, 67], [283, 77, 291, 75, "Math"], [283, 81, 291, 79], [283, 82, 291, 80, "round"], [283, 87, 291, 85], [283, 88, 291, 86, "paddedX"], [283, 95, 291, 93], [283, 96, 291, 94], [283, 101, 291, 99, "Math"], [283, 105, 291, 103], [283, 106, 291, 104, "round"], [283, 111, 291, 109], [283, 112, 291, 110, "paddedY"], [283, 119, 291, 117], [283, 120, 291, 118], [283, 130, 291, 128, "Math"], [283, 134, 291, 132], [283, 135, 291, 133, "round"], [283, 140, 291, 138], [283, 141, 291, 139, "<PERSON><PERSON><PERSON><PERSON>"], [283, 152, 291, 150], [283, 153, 291, 151], [283, 157, 291, 155, "Math"], [283, 161, 291, 159], [283, 162, 291, 160, "round"], [283, 167, 291, 165], [283, 168, 291, 166, "paddedHeight"], [283, 180, 291, 178], [283, 181, 291, 179], [283, 183, 291, 181], [283, 184, 291, 182], [285, 12, 293, 10], [286, 12, 294, 10], [286, 18, 294, 16, "faceImageData"], [286, 31, 294, 29], [286, 34, 294, 32, "ctx"], [286, 37, 294, 35], [286, 38, 294, 36, "getImageData"], [286, 50, 294, 48], [286, 51, 294, 49, "paddedX"], [286, 58, 294, 56], [286, 60, 294, 58, "paddedY"], [286, 67, 294, 65], [286, 69, 294, 67, "<PERSON><PERSON><PERSON><PERSON>"], [286, 80, 294, 78], [286, 82, 294, 80, "paddedHeight"], [286, 94, 294, 92], [286, 95, 294, 93], [287, 12, 295, 10], [287, 18, 295, 16, "data"], [287, 22, 295, 20], [287, 25, 295, 23, "faceImageData"], [287, 38, 295, 36], [287, 39, 295, 37, "data"], [287, 43, 295, 41], [288, 12, 297, 10, "console"], [288, 19, 297, 17], [288, 20, 297, 18, "log"], [288, 23, 297, 21], [288, 24, 297, 22], [288, 64, 297, 62, "data"], [288, 68, 297, 66], [288, 69, 297, 67, "length"], [288, 75, 297, 73], [288, 86, 297, 84, "<PERSON><PERSON><PERSON><PERSON>"], [288, 97, 297, 95], [288, 101, 297, 99, "paddedHeight"], [288, 113, 297, 111], [288, 122, 297, 120], [288, 123, 297, 121], [290, 12, 299, 10], [291, 12, 300, 10], [291, 18, 300, 16, "pixelSize"], [291, 27, 300, 25], [291, 30, 300, 28, "Math"], [291, 34, 300, 32], [291, 35, 300, 33, "max"], [291, 38, 300, 36], [291, 39, 300, 37], [291, 41, 300, 39], [291, 43, 300, 41, "Math"], [291, 47, 300, 45], [291, 48, 300, 46, "min"], [291, 51, 300, 49], [291, 52, 300, 50, "<PERSON><PERSON><PERSON><PERSON>"], [291, 63, 300, 61], [291, 65, 300, 63, "paddedHeight"], [291, 77, 300, 75], [291, 78, 300, 76], [291, 81, 300, 79], [291, 82, 300, 80], [291, 83, 300, 81], [291, 84, 300, 82], [291, 85, 300, 83], [292, 12, 301, 10, "console"], [292, 19, 301, 17], [292, 20, 301, 18, "log"], [292, 23, 301, 21], [292, 24, 301, 22], [292, 70, 301, 68, "pixelSize"], [292, 79, 301, 77], [292, 111, 301, 109], [292, 112, 301, 110], [293, 12, 302, 10], [293, 17, 302, 15], [293, 21, 302, 19, "y"], [293, 22, 302, 20], [293, 25, 302, 23], [293, 26, 302, 24], [293, 28, 302, 26, "y"], [293, 29, 302, 27], [293, 32, 302, 30, "paddedHeight"], [293, 44, 302, 42], [293, 46, 302, 44, "y"], [293, 47, 302, 45], [293, 51, 302, 49, "pixelSize"], [293, 60, 302, 58], [293, 62, 302, 60], [294, 14, 303, 12], [294, 19, 303, 17], [294, 23, 303, 21, "x"], [294, 24, 303, 22], [294, 27, 303, 25], [294, 28, 303, 26], [294, 30, 303, 28, "x"], [294, 31, 303, 29], [294, 34, 303, 32, "<PERSON><PERSON><PERSON><PERSON>"], [294, 45, 303, 43], [294, 47, 303, 45, "x"], [294, 48, 303, 46], [294, 52, 303, 50, "pixelSize"], [294, 61, 303, 59], [294, 63, 303, 61], [295, 16, 304, 14], [296, 16, 305, 14], [296, 22, 305, 20, "pixelIndex"], [296, 32, 305, 30], [296, 35, 305, 33], [296, 36, 305, 34, "y"], [296, 37, 305, 35], [296, 40, 305, 38, "<PERSON><PERSON><PERSON><PERSON>"], [296, 51, 305, 49], [296, 54, 305, 52, "x"], [296, 55, 305, 53], [296, 59, 305, 57], [296, 60, 305, 58], [297, 16, 306, 14], [297, 22, 306, 20, "r"], [297, 23, 306, 21], [297, 26, 306, 24, "data"], [297, 30, 306, 28], [297, 31, 306, 29, "pixelIndex"], [297, 41, 306, 39], [297, 42, 306, 40], [298, 16, 307, 14], [298, 22, 307, 20, "g"], [298, 23, 307, 21], [298, 26, 307, 24, "data"], [298, 30, 307, 28], [298, 31, 307, 29, "pixelIndex"], [298, 41, 307, 39], [298, 44, 307, 42], [298, 45, 307, 43], [298, 46, 307, 44], [299, 16, 308, 14], [299, 22, 308, 20, "b"], [299, 23, 308, 21], [299, 26, 308, 24, "data"], [299, 30, 308, 28], [299, 31, 308, 29, "pixelIndex"], [299, 41, 308, 39], [299, 44, 308, 42], [299, 45, 308, 43], [299, 46, 308, 44], [300, 16, 309, 14], [300, 22, 309, 20, "a"], [300, 23, 309, 21], [300, 26, 309, 24, "data"], [300, 30, 309, 28], [300, 31, 309, 29, "pixelIndex"], [300, 41, 309, 39], [300, 44, 309, 42], [300, 45, 309, 43], [300, 46, 309, 44], [302, 16, 311, 14], [303, 16, 312, 14], [303, 21, 312, 19], [303, 25, 312, 23, "dy"], [303, 27, 312, 25], [303, 30, 312, 28], [303, 31, 312, 29], [303, 33, 312, 31, "dy"], [303, 35, 312, 33], [303, 38, 312, 36, "pixelSize"], [303, 47, 312, 45], [303, 51, 312, 49, "y"], [303, 52, 312, 50], [303, 55, 312, 53, "dy"], [303, 57, 312, 55], [303, 60, 312, 58, "paddedHeight"], [303, 72, 312, 70], [303, 74, 312, 72, "dy"], [303, 76, 312, 74], [303, 78, 312, 76], [303, 80, 312, 78], [304, 18, 313, 16], [304, 23, 313, 21], [304, 27, 313, 25, "dx"], [304, 29, 313, 27], [304, 32, 313, 30], [304, 33, 313, 31], [304, 35, 313, 33, "dx"], [304, 37, 313, 35], [304, 40, 313, 38, "pixelSize"], [304, 49, 313, 47], [304, 53, 313, 51, "x"], [304, 54, 313, 52], [304, 57, 313, 55, "dx"], [304, 59, 313, 57], [304, 62, 313, 60, "<PERSON><PERSON><PERSON><PERSON>"], [304, 73, 313, 71], [304, 75, 313, 73, "dx"], [304, 77, 313, 75], [304, 79, 313, 77], [304, 81, 313, 79], [305, 20, 314, 18], [305, 26, 314, 24, "blockPixelIndex"], [305, 41, 314, 39], [305, 44, 314, 42], [305, 45, 314, 43], [305, 46, 314, 44, "y"], [305, 47, 314, 45], [305, 50, 314, 48, "dy"], [305, 52, 314, 50], [305, 56, 314, 54, "<PERSON><PERSON><PERSON><PERSON>"], [305, 67, 314, 65], [305, 71, 314, 69, "x"], [305, 72, 314, 70], [305, 75, 314, 73, "dx"], [305, 77, 314, 75], [305, 78, 314, 76], [305, 82, 314, 80], [305, 83, 314, 81], [306, 20, 315, 18, "data"], [306, 24, 315, 22], [306, 25, 315, 23, "blockPixelIndex"], [306, 40, 315, 38], [306, 41, 315, 39], [306, 44, 315, 42, "r"], [306, 45, 315, 43], [307, 20, 316, 18, "data"], [307, 24, 316, 22], [307, 25, 316, 23, "blockPixelIndex"], [307, 40, 316, 38], [307, 43, 316, 41], [307, 44, 316, 42], [307, 45, 316, 43], [307, 48, 316, 46, "g"], [307, 49, 316, 47], [308, 20, 317, 18, "data"], [308, 24, 317, 22], [308, 25, 317, 23, "blockPixelIndex"], [308, 40, 317, 38], [308, 43, 317, 41], [308, 44, 317, 42], [308, 45, 317, 43], [308, 48, 317, 46, "b"], [308, 49, 317, 47], [309, 20, 318, 18, "data"], [309, 24, 318, 22], [309, 25, 318, 23, "blockPixelIndex"], [309, 40, 318, 38], [309, 43, 318, 41], [309, 44, 318, 42], [309, 45, 318, 43], [309, 48, 318, 46, "a"], [309, 49, 318, 47], [310, 18, 319, 16], [311, 16, 320, 14], [312, 14, 321, 12], [313, 12, 322, 10], [315, 12, 324, 10], [316, 12, 325, 10, "ctx"], [316, 15, 325, 13], [316, 16, 325, 14, "putImageData"], [316, 28, 325, 26], [316, 29, 325, 27, "faceImageData"], [316, 42, 325, 40], [316, 44, 325, 42, "paddedX"], [316, 51, 325, 49], [316, 53, 325, 51, "paddedY"], [316, 60, 325, 58], [316, 61, 325, 59], [317, 12, 326, 10, "console"], [317, 19, 326, 17], [317, 20, 326, 18, "log"], [317, 23, 326, 21], [317, 24, 326, 22], [317, 50, 326, 48, "index"], [317, 55, 326, 53], [317, 58, 326, 56], [317, 59, 326, 57], [317, 91, 326, 89], [317, 92, 326, 90], [318, 10, 327, 8], [318, 11, 327, 9], [318, 12, 327, 10], [319, 10, 328, 8, "console"], [319, 17, 328, 15], [319, 18, 328, 16, "log"], [319, 21, 328, 19], [319, 22, 328, 20], [319, 48, 328, 46, "detectedFaces"], [319, 61, 328, 59], [319, 62, 328, 60, "length"], [319, 68, 328, 66], [319, 95, 328, 93], [319, 96, 328, 94], [320, 8, 329, 6], [320, 9, 329, 7], [320, 15, 329, 13], [321, 10, 330, 8, "console"], [321, 17, 330, 15], [321, 18, 330, 16, "log"], [321, 21, 330, 19], [321, 22, 330, 20], [321, 91, 330, 89], [321, 92, 330, 90], [322, 8, 331, 6], [323, 8, 333, 6, "setProcessingProgress"], [323, 29, 333, 27], [323, 30, 333, 28], [323, 32, 333, 30], [323, 33, 333, 31], [325, 8, 335, 6], [326, 8, 336, 6, "console"], [326, 15, 336, 13], [326, 16, 336, 14, "log"], [326, 19, 336, 17], [326, 20, 336, 18], [326, 85, 336, 83], [326, 86, 336, 84], [327, 8, 337, 6], [327, 14, 337, 12, "blurredImageBlob"], [327, 30, 337, 28], [327, 33, 337, 31], [327, 39, 337, 37], [327, 43, 337, 41, "Promise"], [327, 50, 337, 48], [327, 51, 337, 56, "resolve"], [327, 58, 337, 63], [327, 62, 337, 68], [328, 10, 338, 8, "canvas"], [328, 16, 338, 14], [328, 17, 338, 15, "toBlob"], [328, 23, 338, 21], [328, 24, 338, 23, "blob"], [328, 28, 338, 27], [328, 32, 338, 32, "resolve"], [328, 39, 338, 39], [328, 40, 338, 40, "blob"], [328, 44, 338, 45], [328, 45, 338, 46], [328, 47, 338, 48], [328, 59, 338, 60], [328, 61, 338, 62], [328, 64, 338, 65], [328, 65, 338, 66], [329, 8, 339, 6], [329, 9, 339, 7], [329, 10, 339, 8], [330, 8, 341, 6], [330, 14, 341, 12, "blurredImageUrl"], [330, 29, 341, 27], [330, 32, 341, 30, "URL"], [330, 35, 341, 33], [330, 36, 341, 34, "createObjectURL"], [330, 51, 341, 49], [330, 52, 341, 50, "blurredImageBlob"], [330, 68, 341, 66], [330, 69, 341, 67], [331, 8, 342, 6, "console"], [331, 15, 342, 13], [331, 16, 342, 14, "log"], [331, 19, 342, 17], [331, 20, 342, 18], [331, 66, 342, 64], [331, 68, 342, 66, "blurredImageUrl"], [331, 83, 342, 81], [331, 84, 342, 82, "substring"], [331, 93, 342, 91], [331, 94, 342, 92], [331, 95, 342, 93], [331, 97, 342, 95], [331, 99, 342, 97], [331, 100, 342, 98], [331, 103, 342, 101], [331, 108, 342, 106], [331, 109, 342, 107], [332, 8, 344, 6, "setProcessingProgress"], [332, 29, 344, 27], [332, 30, 344, 28], [332, 33, 344, 31], [332, 34, 344, 32], [334, 8, 346, 6], [335, 8, 347, 6], [335, 14, 347, 12, "completeProcessing"], [335, 32, 347, 30], [335, 33, 347, 31, "blurredImageUrl"], [335, 48, 347, 46], [335, 49, 347, 47], [336, 6, 349, 4], [336, 7, 349, 5], [336, 8, 349, 6], [336, 15, 349, 13, "error"], [336, 20, 349, 18], [336, 22, 349, 20], [337, 8, 350, 6, "console"], [337, 15, 350, 13], [337, 16, 350, 14, "error"], [337, 21, 350, 19], [337, 22, 350, 20], [337, 57, 350, 55], [337, 59, 350, 57, "error"], [337, 64, 350, 62], [337, 65, 350, 63], [338, 8, 351, 6, "setErrorMessage"], [338, 23, 351, 21], [338, 24, 351, 22], [338, 50, 351, 48], [338, 51, 351, 49], [339, 8, 352, 6, "setProcessingState"], [339, 26, 352, 24], [339, 27, 352, 25], [339, 34, 352, 32], [339, 35, 352, 33], [340, 6, 353, 4], [341, 4, 354, 2], [341, 5, 354, 3], [343, 4, 356, 2], [344, 4, 357, 2], [344, 10, 357, 8, "completeProcessing"], [344, 28, 357, 26], [344, 31, 357, 29], [344, 37, 357, 36, "blurredImageUrl"], [344, 52, 357, 59], [344, 56, 357, 64], [345, 6, 358, 4], [345, 10, 358, 8], [346, 8, 359, 6, "setProcessingState"], [346, 26, 359, 24], [346, 27, 359, 25], [346, 37, 359, 35], [346, 38, 359, 36], [348, 8, 361, 6], [349, 8, 362, 6], [349, 14, 362, 12, "timestamp"], [349, 23, 362, 21], [349, 26, 362, 24, "Date"], [349, 30, 362, 28], [349, 31, 362, 29, "now"], [349, 34, 362, 32], [349, 35, 362, 33], [349, 36, 362, 34], [350, 8, 363, 6], [350, 14, 363, 12, "result"], [350, 20, 363, 18], [350, 23, 363, 21], [351, 10, 364, 8, "imageUrl"], [351, 18, 364, 16], [351, 20, 364, 18, "blurredImageUrl"], [351, 35, 364, 33], [352, 10, 365, 8, "localUri"], [352, 18, 365, 16], [352, 20, 365, 18, "blurredImageUrl"], [352, 35, 365, 33], [353, 10, 366, 8, "challengeCode"], [353, 23, 366, 21], [353, 25, 366, 23, "challengeCode"], [353, 38, 366, 36], [353, 42, 366, 40], [353, 44, 366, 42], [354, 10, 367, 8, "timestamp"], [354, 19, 367, 17], [355, 10, 368, 8, "jobId"], [355, 15, 368, 13], [355, 17, 368, 15], [355, 27, 368, 25, "timestamp"], [355, 36, 368, 34], [355, 38, 368, 36], [356, 10, 369, 8, "status"], [356, 16, 369, 14], [356, 18, 369, 16], [357, 8, 370, 6], [357, 9, 370, 7], [358, 8, 372, 6, "console"], [358, 15, 372, 13], [358, 16, 372, 14, "log"], [358, 19, 372, 17], [358, 20, 372, 18], [358, 100, 372, 98], [358, 102, 372, 100], [359, 10, 373, 8, "imageUrl"], [359, 18, 373, 16], [359, 20, 373, 18, "blurredImageUrl"], [359, 35, 373, 33], [359, 36, 373, 34, "substring"], [359, 45, 373, 43], [359, 46, 373, 44], [359, 47, 373, 45], [359, 49, 373, 47], [359, 51, 373, 49], [359, 52, 373, 50], [359, 55, 373, 53], [359, 60, 373, 58], [360, 10, 374, 8, "timestamp"], [360, 19, 374, 17], [361, 10, 375, 8, "jobId"], [361, 15, 375, 13], [361, 17, 375, 15, "result"], [361, 23, 375, 21], [361, 24, 375, 22, "jobId"], [362, 8, 376, 6], [362, 9, 376, 7], [362, 10, 376, 8], [364, 8, 378, 6], [365, 8, 379, 6, "onComplete"], [365, 18, 379, 16], [365, 19, 379, 17, "result"], [365, 25, 379, 23], [365, 26, 379, 24], [366, 6, 381, 4], [366, 7, 381, 5], [366, 8, 381, 6], [366, 15, 381, 13, "error"], [366, 20, 381, 18], [366, 22, 381, 20], [367, 8, 382, 6, "console"], [367, 15, 382, 13], [367, 16, 382, 14, "error"], [367, 21, 382, 19], [367, 22, 382, 20], [367, 57, 382, 55], [367, 59, 382, 57, "error"], [367, 64, 382, 62], [367, 65, 382, 63], [368, 8, 383, 6, "setErrorMessage"], [368, 23, 383, 21], [368, 24, 383, 22], [368, 56, 383, 54], [368, 57, 383, 55], [369, 8, 384, 6, "setProcessingState"], [369, 26, 384, 24], [369, 27, 384, 25], [369, 34, 384, 32], [369, 35, 384, 33], [370, 6, 385, 4], [371, 4, 386, 2], [371, 5, 386, 3], [373, 4, 388, 2], [374, 4, 389, 2], [374, 10, 389, 8, "triggerServerProcessing"], [374, 33, 389, 31], [374, 36, 389, 34], [374, 42, 389, 34, "triggerServerProcessing"], [374, 43, 389, 41, "privateImageUrl"], [374, 58, 389, 64], [374, 60, 389, 66, "timestamp"], [374, 69, 389, 83], [374, 74, 389, 88], [375, 6, 390, 4], [375, 10, 390, 8], [376, 8, 391, 6, "console"], [376, 15, 391, 13], [376, 16, 391, 14, "log"], [376, 19, 391, 17], [376, 20, 391, 18], [376, 74, 391, 72], [376, 76, 391, 74, "privateImageUrl"], [376, 91, 391, 89], [376, 92, 391, 90], [377, 8, 392, 6, "setProcessingState"], [377, 26, 392, 24], [377, 27, 392, 25], [377, 39, 392, 37], [377, 40, 392, 38], [378, 8, 393, 6, "setProcessingProgress"], [378, 29, 393, 27], [378, 30, 393, 28], [378, 32, 393, 30], [378, 33, 393, 31], [379, 8, 395, 6], [379, 14, 395, 12, "requestBody"], [379, 25, 395, 23], [379, 28, 395, 26], [380, 10, 396, 8, "imageUrl"], [380, 18, 396, 16], [380, 20, 396, 18, "privateImageUrl"], [380, 35, 396, 33], [381, 10, 397, 8, "userId"], [381, 16, 397, 14], [382, 10, 398, 8, "requestId"], [382, 19, 398, 17], [383, 10, 399, 8, "timestamp"], [383, 19, 399, 17], [384, 10, 400, 8, "platform"], [384, 18, 400, 16], [384, 20, 400, 18], [385, 8, 401, 6], [385, 9, 401, 7], [386, 8, 403, 6, "console"], [386, 15, 403, 13], [386, 16, 403, 14, "log"], [386, 19, 403, 17], [386, 20, 403, 18], [386, 65, 403, 63], [386, 67, 403, 65, "requestBody"], [386, 78, 403, 76], [386, 79, 403, 77], [388, 8, 405, 6], [389, 8, 406, 6], [389, 14, 406, 12, "response"], [389, 22, 406, 20], [389, 25, 406, 23], [389, 31, 406, 29, "fetch"], [389, 36, 406, 34], [389, 37, 406, 35], [389, 40, 406, 38, "API_BASE_URL"], [389, 52, 406, 50], [389, 72, 406, 70], [389, 74, 406, 72], [390, 10, 407, 8, "method"], [390, 16, 407, 14], [390, 18, 407, 16], [390, 24, 407, 22], [391, 10, 408, 8, "headers"], [391, 17, 408, 15], [391, 19, 408, 17], [392, 12, 409, 10], [392, 26, 409, 24], [392, 28, 409, 26], [392, 46, 409, 44], [393, 12, 410, 10], [393, 27, 410, 25], [393, 29, 410, 27], [393, 39, 410, 37], [393, 45, 410, 43, "getAuthToken"], [393, 57, 410, 55], [393, 58, 410, 56], [393, 59, 410, 57], [394, 10, 411, 8], [394, 11, 411, 9], [395, 10, 412, 8, "body"], [395, 14, 412, 12], [395, 16, 412, 14, "JSON"], [395, 20, 412, 18], [395, 21, 412, 19, "stringify"], [395, 30, 412, 28], [395, 31, 412, 29, "requestBody"], [395, 42, 412, 40], [396, 8, 413, 6], [396, 9, 413, 7], [396, 10, 413, 8], [397, 8, 415, 6], [397, 12, 415, 10], [397, 13, 415, 11, "response"], [397, 21, 415, 19], [397, 22, 415, 20, "ok"], [397, 24, 415, 22], [397, 26, 415, 24], [398, 10, 416, 8], [398, 16, 416, 14, "errorText"], [398, 25, 416, 23], [398, 28, 416, 26], [398, 34, 416, 32, "response"], [398, 42, 416, 40], [398, 43, 416, 41, "text"], [398, 47, 416, 45], [398, 48, 416, 46], [398, 49, 416, 47], [399, 10, 417, 8, "console"], [399, 17, 417, 15], [399, 18, 417, 16, "error"], [399, 23, 417, 21], [399, 24, 417, 22], [399, 68, 417, 66], [399, 70, 417, 68, "response"], [399, 78, 417, 76], [399, 79, 417, 77, "status"], [399, 85, 417, 83], [399, 87, 417, 85, "errorText"], [399, 96, 417, 94], [399, 97, 417, 95], [400, 10, 418, 8], [400, 16, 418, 14], [400, 20, 418, 18, "Error"], [400, 25, 418, 23], [400, 26, 418, 24], [400, 48, 418, 46, "response"], [400, 56, 418, 54], [400, 57, 418, 55, "status"], [400, 63, 418, 61], [400, 67, 418, 65, "response"], [400, 75, 418, 73], [400, 76, 418, 74, "statusText"], [400, 86, 418, 84], [400, 88, 418, 86], [400, 89, 418, 87], [401, 8, 419, 6], [402, 8, 421, 6], [402, 14, 421, 12, "result"], [402, 20, 421, 18], [402, 23, 421, 21], [402, 29, 421, 27, "response"], [402, 37, 421, 35], [402, 38, 421, 36, "json"], [402, 42, 421, 40], [402, 43, 421, 41], [402, 44, 421, 42], [403, 8, 422, 6, "console"], [403, 15, 422, 13], [403, 16, 422, 14, "log"], [403, 19, 422, 17], [403, 20, 422, 18], [403, 68, 422, 66], [403, 70, 422, 68, "result"], [403, 76, 422, 74], [403, 77, 422, 75], [404, 8, 424, 6], [404, 12, 424, 10], [404, 13, 424, 11, "result"], [404, 19, 424, 17], [404, 20, 424, 18, "jobId"], [404, 25, 424, 23], [404, 27, 424, 25], [405, 10, 425, 8], [405, 16, 425, 14], [405, 20, 425, 18, "Error"], [405, 25, 425, 23], [405, 26, 425, 24], [405, 70, 425, 68], [405, 71, 425, 69], [406, 8, 426, 6], [408, 8, 428, 6], [409, 8, 429, 6], [409, 14, 429, 12, "pollForCompletion"], [409, 31, 429, 29], [409, 32, 429, 30, "result"], [409, 38, 429, 36], [409, 39, 429, 37, "jobId"], [409, 44, 429, 42], [409, 46, 429, 44, "timestamp"], [409, 55, 429, 53], [409, 56, 429, 54], [410, 6, 430, 4], [410, 7, 430, 5], [410, 8, 430, 6], [410, 15, 430, 13, "error"], [410, 20, 430, 18], [410, 22, 430, 20], [411, 8, 431, 6, "console"], [411, 15, 431, 13], [411, 16, 431, 14, "error"], [411, 21, 431, 19], [411, 22, 431, 20], [411, 57, 431, 55], [411, 59, 431, 57, "error"], [411, 64, 431, 62], [411, 65, 431, 63], [412, 8, 432, 6, "setErrorMessage"], [412, 23, 432, 21], [412, 24, 432, 22], [412, 52, 432, 50, "error"], [412, 57, 432, 55], [412, 58, 432, 56, "message"], [412, 65, 432, 63], [412, 67, 432, 65], [412, 68, 432, 66], [413, 8, 433, 6, "setProcessingState"], [413, 26, 433, 24], [413, 27, 433, 25], [413, 34, 433, 32], [413, 35, 433, 33], [414, 6, 434, 4], [415, 4, 435, 2], [415, 5, 435, 3], [416, 4, 436, 2], [417, 4, 437, 2], [417, 10, 437, 8, "pollForCompletion"], [417, 27, 437, 25], [417, 30, 437, 28], [417, 36, 437, 28, "pollForCompletion"], [417, 37, 437, 35, "jobId"], [417, 42, 437, 48], [417, 44, 437, 50, "timestamp"], [417, 53, 437, 67], [417, 55, 437, 69, "attempts"], [417, 63, 437, 77], [417, 66, 437, 80], [417, 67, 437, 81], [417, 72, 437, 86], [418, 6, 438, 4], [418, 12, 438, 10, "MAX_ATTEMPTS"], [418, 24, 438, 22], [418, 27, 438, 25], [418, 29, 438, 27], [418, 30, 438, 28], [418, 31, 438, 29], [419, 6, 439, 4], [419, 12, 439, 10, "POLL_INTERVAL"], [419, 25, 439, 23], [419, 28, 439, 26], [419, 32, 439, 30], [419, 33, 439, 31], [419, 34, 439, 32], [421, 6, 441, 4, "console"], [421, 13, 441, 11], [421, 14, 441, 12, "log"], [421, 17, 441, 15], [421, 18, 441, 16], [421, 53, 441, 51, "attempts"], [421, 61, 441, 59], [421, 64, 441, 62], [421, 65, 441, 63], [421, 69, 441, 67, "MAX_ATTEMPTS"], [421, 81, 441, 79], [421, 93, 441, 91, "jobId"], [421, 98, 441, 96], [421, 100, 441, 98], [421, 101, 441, 99], [422, 6, 443, 4], [422, 10, 443, 8, "attempts"], [422, 18, 443, 16], [422, 22, 443, 20, "MAX_ATTEMPTS"], [422, 34, 443, 32], [422, 36, 443, 34], [423, 8, 444, 6, "console"], [423, 15, 444, 13], [423, 16, 444, 14, "error"], [423, 21, 444, 19], [423, 22, 444, 20], [423, 75, 444, 73], [423, 76, 444, 74], [424, 8, 445, 6, "setErrorMessage"], [424, 23, 445, 21], [424, 24, 445, 22], [424, 63, 445, 61], [424, 64, 445, 62], [425, 8, 446, 6, "setProcessingState"], [425, 26, 446, 24], [425, 27, 446, 25], [425, 34, 446, 32], [425, 35, 446, 33], [426, 8, 447, 6], [427, 6, 448, 4], [428, 6, 450, 4], [428, 10, 450, 8], [429, 8, 451, 6], [429, 14, 451, 12, "response"], [429, 22, 451, 20], [429, 25, 451, 23], [429, 31, 451, 29, "fetch"], [429, 36, 451, 34], [429, 37, 451, 35], [429, 40, 451, 38, "API_BASE_URL"], [429, 52, 451, 50], [429, 75, 451, 73, "jobId"], [429, 80, 451, 78], [429, 82, 451, 80], [429, 84, 451, 82], [430, 10, 452, 8, "headers"], [430, 17, 452, 15], [430, 19, 452, 17], [431, 12, 453, 10], [431, 27, 453, 25], [431, 29, 453, 27], [431, 39, 453, 37], [431, 45, 453, 43, "getAuthToken"], [431, 57, 453, 55], [431, 58, 453, 56], [431, 59, 453, 57], [432, 10, 454, 8], [433, 8, 455, 6], [433, 9, 455, 7], [433, 10, 455, 8], [434, 8, 457, 6], [434, 12, 457, 10], [434, 13, 457, 11, "response"], [434, 21, 457, 19], [434, 22, 457, 20, "ok"], [434, 24, 457, 22], [434, 26, 457, 24], [435, 10, 458, 8], [435, 16, 458, 14], [435, 20, 458, 18, "Error"], [435, 25, 458, 23], [435, 26, 458, 24], [435, 34, 458, 32, "response"], [435, 42, 458, 40], [435, 43, 458, 41, "status"], [435, 49, 458, 47], [435, 54, 458, 52, "response"], [435, 62, 458, 60], [435, 63, 458, 61, "statusText"], [435, 73, 458, 71], [435, 75, 458, 73], [435, 76, 458, 74], [436, 8, 459, 6], [437, 8, 461, 6], [437, 14, 461, 12, "status"], [437, 20, 461, 18], [437, 23, 461, 21], [437, 29, 461, 27, "response"], [437, 37, 461, 35], [437, 38, 461, 36, "json"], [437, 42, 461, 40], [437, 43, 461, 41], [437, 44, 461, 42], [438, 8, 462, 6, "console"], [438, 15, 462, 13], [438, 16, 462, 14, "log"], [438, 19, 462, 17], [438, 20, 462, 18], [438, 54, 462, 52], [438, 56, 462, 54, "status"], [438, 62, 462, 60], [438, 63, 462, 61], [439, 8, 464, 6], [439, 12, 464, 10, "status"], [439, 18, 464, 16], [439, 19, 464, 17, "status"], [439, 25, 464, 23], [439, 30, 464, 28], [439, 41, 464, 39], [439, 43, 464, 41], [440, 10, 465, 8, "console"], [440, 17, 465, 15], [440, 18, 465, 16, "log"], [440, 21, 465, 19], [440, 22, 465, 20], [440, 73, 465, 71], [440, 74, 465, 72], [441, 10, 466, 8, "setProcessingProgress"], [441, 31, 466, 29], [441, 32, 466, 30], [441, 35, 466, 33], [441, 36, 466, 34], [442, 10, 467, 8, "setProcessingState"], [442, 28, 467, 26], [442, 29, 467, 27], [442, 40, 467, 38], [442, 41, 467, 39], [443, 10, 468, 8], [444, 10, 469, 8], [444, 16, 469, 14, "result"], [444, 22, 469, 20], [444, 25, 469, 23], [445, 12, 470, 10, "imageUrl"], [445, 20, 470, 18], [445, 22, 470, 20, "status"], [445, 28, 470, 26], [445, 29, 470, 27, "publicUrl"], [445, 38, 470, 36], [446, 12, 470, 38], [447, 12, 471, 10, "localUri"], [447, 20, 471, 18], [447, 22, 471, 20, "capturedPhoto"], [447, 35, 471, 33], [447, 39, 471, 37, "status"], [447, 45, 471, 43], [447, 46, 471, 44, "publicUrl"], [447, 55, 471, 53], [448, 12, 471, 55], [449, 12, 472, 10, "challengeCode"], [449, 25, 472, 23], [449, 27, 472, 25, "challengeCode"], [449, 40, 472, 38], [449, 44, 472, 42], [449, 46, 472, 44], [450, 12, 473, 10, "timestamp"], [450, 21, 473, 19], [451, 12, 474, 10, "processingStatus"], [451, 28, 474, 26], [451, 30, 474, 28], [452, 10, 475, 8], [452, 11, 475, 9], [453, 10, 476, 8, "console"], [453, 17, 476, 15], [453, 18, 476, 16, "log"], [453, 21, 476, 19], [453, 22, 476, 20], [453, 57, 476, 55], [453, 59, 476, 57, "result"], [453, 65, 476, 63], [453, 66, 476, 64], [454, 10, 477, 8, "onComplete"], [454, 20, 477, 18], [454, 21, 477, 19, "result"], [454, 27, 477, 25], [454, 28, 477, 26], [455, 10, 478, 8], [456, 8, 479, 6], [456, 9, 479, 7], [456, 15, 479, 13], [456, 19, 479, 17, "status"], [456, 25, 479, 23], [456, 26, 479, 24, "status"], [456, 32, 479, 30], [456, 37, 479, 35], [456, 45, 479, 43], [456, 47, 479, 45], [457, 10, 480, 8, "console"], [457, 17, 480, 15], [457, 18, 480, 16, "error"], [457, 23, 480, 21], [457, 24, 480, 22], [457, 60, 480, 58], [457, 62, 480, 60, "status"], [457, 68, 480, 66], [457, 69, 480, 67, "error"], [457, 74, 480, 72], [457, 75, 480, 73], [458, 10, 481, 8], [458, 16, 481, 14], [458, 20, 481, 18, "Error"], [458, 25, 481, 23], [458, 26, 481, 24, "status"], [458, 32, 481, 30], [458, 33, 481, 31, "error"], [458, 38, 481, 36], [458, 42, 481, 40], [458, 61, 481, 59], [458, 62, 481, 60], [459, 8, 482, 6], [459, 9, 482, 7], [459, 15, 482, 13], [460, 10, 483, 8], [461, 10, 484, 8], [461, 16, 484, 14, "progressValue"], [461, 29, 484, 27], [461, 32, 484, 30], [461, 34, 484, 32], [461, 37, 484, 36, "attempts"], [461, 45, 484, 44], [461, 48, 484, 47, "MAX_ATTEMPTS"], [461, 60, 484, 59], [461, 63, 484, 63], [461, 65, 484, 65], [462, 10, 485, 8, "console"], [462, 17, 485, 15], [462, 18, 485, 16, "log"], [462, 21, 485, 19], [462, 22, 485, 20], [462, 71, 485, 69, "progressValue"], [462, 84, 485, 82], [462, 87, 485, 85], [462, 88, 485, 86], [463, 10, 486, 8, "setProcessingProgress"], [463, 31, 486, 29], [463, 32, 486, 30, "progressValue"], [463, 45, 486, 43], [463, 46, 486, 44], [464, 10, 488, 8, "setTimeout"], [464, 20, 488, 18], [464, 21, 488, 19], [464, 27, 488, 25], [465, 12, 489, 10, "pollForCompletion"], [465, 29, 489, 27], [465, 30, 489, 28, "jobId"], [465, 35, 489, 33], [465, 37, 489, 35, "timestamp"], [465, 46, 489, 44], [465, 48, 489, 46, "attempts"], [465, 56, 489, 54], [465, 59, 489, 57], [465, 60, 489, 58], [465, 61, 489, 59], [466, 10, 490, 8], [466, 11, 490, 9], [466, 13, 490, 11, "POLL_INTERVAL"], [466, 26, 490, 24], [466, 27, 490, 25], [467, 8, 491, 6], [468, 6, 492, 4], [468, 7, 492, 5], [468, 8, 492, 6], [468, 15, 492, 13, "error"], [468, 20, 492, 18], [468, 22, 492, 20], [469, 8, 493, 6, "console"], [469, 15, 493, 13], [469, 16, 493, 14, "error"], [469, 21, 493, 19], [469, 22, 493, 20], [469, 54, 493, 52], [469, 56, 493, 54, "error"], [469, 61, 493, 59], [469, 62, 493, 60], [470, 8, 494, 6, "setErrorMessage"], [470, 23, 494, 21], [470, 24, 494, 22], [470, 62, 494, 60, "error"], [470, 67, 494, 65], [470, 68, 494, 66, "message"], [470, 75, 494, 73], [470, 77, 494, 75], [470, 78, 494, 76], [471, 8, 495, 6, "setProcessingState"], [471, 26, 495, 24], [471, 27, 495, 25], [471, 34, 495, 32], [471, 35, 495, 33], [472, 6, 496, 4], [473, 4, 497, 2], [473, 5, 497, 3], [474, 4, 498, 2], [475, 4, 499, 2], [475, 10, 499, 8, "getAuthToken"], [475, 22, 499, 20], [475, 25, 499, 23], [475, 31, 499, 23, "getAuthToken"], [475, 32, 499, 23], [475, 37, 499, 52], [476, 6, 500, 4], [477, 6, 501, 4], [478, 6, 502, 4], [478, 13, 502, 11], [478, 30, 502, 28], [479, 4, 503, 2], [479, 5, 503, 3], [481, 4, 505, 2], [482, 4, 506, 2], [482, 10, 506, 8, "retryCapture"], [482, 22, 506, 20], [482, 25, 506, 23], [482, 29, 506, 23, "useCallback"], [482, 47, 506, 34], [482, 49, 506, 35], [482, 55, 506, 41], [483, 6, 507, 4, "console"], [483, 13, 507, 11], [483, 14, 507, 12, "log"], [483, 17, 507, 15], [483, 18, 507, 16], [483, 55, 507, 53], [483, 56, 507, 54], [484, 6, 508, 4, "setProcessingState"], [484, 24, 508, 22], [484, 25, 508, 23], [484, 31, 508, 29], [484, 32, 508, 30], [485, 6, 509, 4, "setErrorMessage"], [485, 21, 509, 19], [485, 22, 509, 20], [485, 24, 509, 22], [485, 25, 509, 23], [486, 6, 510, 4, "setCapturedPhoto"], [486, 22, 510, 20], [486, 23, 510, 21], [486, 25, 510, 23], [486, 26, 510, 24], [487, 6, 511, 4, "setProcessingProgress"], [487, 27, 511, 25], [487, 28, 511, 26], [487, 29, 511, 27], [487, 30, 511, 28], [488, 4, 512, 2], [488, 5, 512, 3], [488, 7, 512, 5], [488, 9, 512, 7], [488, 10, 512, 8], [489, 4, 513, 2], [490, 4, 514, 2], [490, 8, 514, 2, "useEffect"], [490, 24, 514, 11], [490, 26, 514, 12], [490, 32, 514, 18], [491, 6, 515, 4, "console"], [491, 13, 515, 11], [491, 14, 515, 12, "log"], [491, 17, 515, 15], [491, 18, 515, 16], [491, 53, 515, 51], [491, 55, 515, 53, "permission"], [491, 65, 515, 63], [491, 66, 515, 64], [492, 6, 516, 4], [492, 10, 516, 8, "permission"], [492, 20, 516, 18], [492, 22, 516, 20], [493, 8, 517, 6, "console"], [493, 15, 517, 13], [493, 16, 517, 14, "log"], [493, 19, 517, 17], [493, 20, 517, 18], [493, 57, 517, 55], [493, 59, 517, 57, "permission"], [493, 69, 517, 67], [493, 70, 517, 68, "granted"], [493, 77, 517, 75], [493, 78, 517, 76], [494, 6, 518, 4], [495, 4, 519, 2], [495, 5, 519, 3], [495, 7, 519, 5], [495, 8, 519, 6, "permission"], [495, 18, 519, 16], [495, 19, 519, 17], [495, 20, 519, 18], [496, 4, 520, 2], [497, 4, 521, 2], [497, 8, 521, 6], [497, 9, 521, 7, "permission"], [497, 19, 521, 17], [497, 21, 521, 19], [498, 6, 522, 4, "console"], [498, 13, 522, 11], [498, 14, 522, 12, "log"], [498, 17, 522, 15], [498, 18, 522, 16], [498, 67, 522, 65], [498, 68, 522, 66], [499, 6, 523, 4], [499, 26, 524, 6], [499, 30, 524, 6, "_jsxDevRuntime"], [499, 44, 524, 6], [499, 45, 524, 6, "jsxDEV"], [499, 51, 524, 6], [499, 53, 524, 7, "_View"], [499, 58, 524, 7], [499, 59, 524, 7, "default"], [499, 66, 524, 11], [500, 8, 524, 12, "style"], [500, 13, 524, 17], [500, 15, 524, 19, "styles"], [500, 21, 524, 25], [500, 22, 524, 26, "container"], [500, 31, 524, 36], [501, 8, 524, 36, "children"], [501, 16, 524, 36], [501, 32, 525, 8], [501, 36, 525, 8, "_jsxDevRuntime"], [501, 50, 525, 8], [501, 51, 525, 8, "jsxDEV"], [501, 57, 525, 8], [501, 59, 525, 9, "_ActivityIndicator"], [501, 77, 525, 9], [501, 78, 525, 9, "default"], [501, 85, 525, 26], [502, 10, 525, 27, "size"], [502, 14, 525, 31], [502, 16, 525, 32], [502, 23, 525, 39], [503, 10, 525, 40, "color"], [503, 15, 525, 45], [503, 17, 525, 46], [504, 8, 525, 55], [505, 10, 525, 55, "fileName"], [505, 18, 525, 55], [505, 20, 525, 55, "_jsxFileName"], [505, 32, 525, 55], [506, 10, 525, 55, "lineNumber"], [506, 20, 525, 55], [507, 10, 525, 55, "columnNumber"], [507, 22, 525, 55], [508, 8, 525, 55], [508, 15, 525, 57], [508, 16, 525, 58], [508, 31, 526, 8], [508, 35, 526, 8, "_jsxDevRuntime"], [508, 49, 526, 8], [508, 50, 526, 8, "jsxDEV"], [508, 56, 526, 8], [508, 58, 526, 9, "_Text"], [508, 63, 526, 9], [508, 64, 526, 9, "default"], [508, 71, 526, 13], [509, 10, 526, 14, "style"], [509, 15, 526, 19], [509, 17, 526, 21, "styles"], [509, 23, 526, 27], [509, 24, 526, 28, "loadingText"], [509, 35, 526, 40], [510, 10, 526, 40, "children"], [510, 18, 526, 40], [510, 20, 526, 41], [511, 8, 526, 58], [512, 10, 526, 58, "fileName"], [512, 18, 526, 58], [512, 20, 526, 58, "_jsxFileName"], [512, 32, 526, 58], [513, 10, 526, 58, "lineNumber"], [513, 20, 526, 58], [514, 10, 526, 58, "columnNumber"], [514, 22, 526, 58], [515, 8, 526, 58], [515, 15, 526, 64], [515, 16, 526, 65], [516, 6, 526, 65], [517, 8, 526, 65, "fileName"], [517, 16, 526, 65], [517, 18, 526, 65, "_jsxFileName"], [517, 30, 526, 65], [518, 8, 526, 65, "lineNumber"], [518, 18, 526, 65], [519, 8, 526, 65, "columnNumber"], [519, 20, 526, 65], [520, 6, 526, 65], [520, 13, 527, 12], [520, 14, 527, 13], [521, 4, 529, 2], [522, 4, 530, 2], [522, 8, 530, 6], [522, 9, 530, 7, "permission"], [522, 19, 530, 17], [522, 20, 530, 18, "granted"], [522, 27, 530, 25], [522, 29, 530, 27], [523, 6, 531, 4, "console"], [523, 13, 531, 11], [523, 14, 531, 12, "log"], [523, 17, 531, 15], [523, 18, 531, 16], [523, 93, 531, 91], [523, 94, 531, 92], [524, 6, 532, 4], [524, 26, 533, 6], [524, 30, 533, 6, "_jsxDevRuntime"], [524, 44, 533, 6], [524, 45, 533, 6, "jsxDEV"], [524, 51, 533, 6], [524, 53, 533, 7, "_View"], [524, 58, 533, 7], [524, 59, 533, 7, "default"], [524, 66, 533, 11], [525, 8, 533, 12, "style"], [525, 13, 533, 17], [525, 15, 533, 19, "styles"], [525, 21, 533, 25], [525, 22, 533, 26, "container"], [525, 31, 533, 36], [526, 8, 533, 36, "children"], [526, 16, 533, 36], [526, 31, 534, 8], [526, 35, 534, 8, "_jsxDevRuntime"], [526, 49, 534, 8], [526, 50, 534, 8, "jsxDEV"], [526, 56, 534, 8], [526, 58, 534, 9, "_View"], [526, 63, 534, 9], [526, 64, 534, 9, "default"], [526, 71, 534, 13], [527, 10, 534, 14, "style"], [527, 15, 534, 19], [527, 17, 534, 21, "styles"], [527, 23, 534, 27], [527, 24, 534, 28, "permissionContent"], [527, 41, 534, 46], [528, 10, 534, 46, "children"], [528, 18, 534, 46], [528, 34, 535, 10], [528, 38, 535, 10, "_jsxDevRuntime"], [528, 52, 535, 10], [528, 53, 535, 10, "jsxDEV"], [528, 59, 535, 10], [528, 61, 535, 11, "_lucideReactNative"], [528, 79, 535, 11], [528, 80, 535, 11, "Camera"], [528, 86, 535, 21], [529, 12, 535, 22, "size"], [529, 16, 535, 26], [529, 18, 535, 28], [529, 20, 535, 31], [530, 12, 535, 32, "color"], [530, 17, 535, 37], [530, 19, 535, 38], [531, 10, 535, 47], [532, 12, 535, 47, "fileName"], [532, 20, 535, 47], [532, 22, 535, 47, "_jsxFileName"], [532, 34, 535, 47], [533, 12, 535, 47, "lineNumber"], [533, 22, 535, 47], [534, 12, 535, 47, "columnNumber"], [534, 24, 535, 47], [535, 10, 535, 47], [535, 17, 535, 49], [535, 18, 535, 50], [535, 33, 536, 10], [535, 37, 536, 10, "_jsxDevRuntime"], [535, 51, 536, 10], [535, 52, 536, 10, "jsxDEV"], [535, 58, 536, 10], [535, 60, 536, 11, "_Text"], [535, 65, 536, 11], [535, 66, 536, 11, "default"], [535, 73, 536, 15], [536, 12, 536, 16, "style"], [536, 17, 536, 21], [536, 19, 536, 23, "styles"], [536, 25, 536, 29], [536, 26, 536, 30, "permissionTitle"], [536, 41, 536, 46], [537, 12, 536, 46, "children"], [537, 20, 536, 46], [537, 22, 536, 47], [538, 10, 536, 73], [539, 12, 536, 73, "fileName"], [539, 20, 536, 73], [539, 22, 536, 73, "_jsxFileName"], [539, 34, 536, 73], [540, 12, 536, 73, "lineNumber"], [540, 22, 536, 73], [541, 12, 536, 73, "columnNumber"], [541, 24, 536, 73], [542, 10, 536, 73], [542, 17, 536, 79], [542, 18, 536, 80], [542, 33, 537, 10], [542, 37, 537, 10, "_jsxDevRuntime"], [542, 51, 537, 10], [542, 52, 537, 10, "jsxDEV"], [542, 58, 537, 10], [542, 60, 537, 11, "_Text"], [542, 65, 537, 11], [542, 66, 537, 11, "default"], [542, 73, 537, 15], [543, 12, 537, 16, "style"], [543, 17, 537, 21], [543, 19, 537, 23, "styles"], [543, 25, 537, 29], [543, 26, 537, 30, "permissionDescription"], [543, 47, 537, 52], [544, 12, 537, 52, "children"], [544, 20, 537, 52], [544, 22, 537, 53], [545, 10, 540, 10], [546, 12, 540, 10, "fileName"], [546, 20, 540, 10], [546, 22, 540, 10, "_jsxFileName"], [546, 34, 540, 10], [547, 12, 540, 10, "lineNumber"], [547, 22, 540, 10], [548, 12, 540, 10, "columnNumber"], [548, 24, 540, 10], [549, 10, 540, 10], [549, 17, 540, 16], [549, 18, 540, 17], [549, 33, 541, 10], [549, 37, 541, 10, "_jsxDevRuntime"], [549, 51, 541, 10], [549, 52, 541, 10, "jsxDEV"], [549, 58, 541, 10], [549, 60, 541, 11, "_TouchableOpacity"], [549, 77, 541, 11], [549, 78, 541, 11, "default"], [549, 85, 541, 27], [550, 12, 541, 28, "onPress"], [550, 19, 541, 35], [550, 21, 541, 37, "requestPermission"], [550, 38, 541, 55], [551, 12, 541, 56, "style"], [551, 17, 541, 61], [551, 19, 541, 63, "styles"], [551, 25, 541, 69], [551, 26, 541, 70, "primaryButton"], [551, 39, 541, 84], [552, 12, 541, 84, "children"], [552, 20, 541, 84], [552, 35, 542, 12], [552, 39, 542, 12, "_jsxDevRuntime"], [552, 53, 542, 12], [552, 54, 542, 12, "jsxDEV"], [552, 60, 542, 12], [552, 62, 542, 13, "_Text"], [552, 67, 542, 13], [552, 68, 542, 13, "default"], [552, 75, 542, 17], [553, 14, 542, 18, "style"], [553, 19, 542, 23], [553, 21, 542, 25, "styles"], [553, 27, 542, 31], [553, 28, 542, 32, "primaryButtonText"], [553, 45, 542, 50], [554, 14, 542, 50, "children"], [554, 22, 542, 50], [554, 24, 542, 51], [555, 12, 542, 67], [556, 14, 542, 67, "fileName"], [556, 22, 542, 67], [556, 24, 542, 67, "_jsxFileName"], [556, 36, 542, 67], [557, 14, 542, 67, "lineNumber"], [557, 24, 542, 67], [558, 14, 542, 67, "columnNumber"], [558, 26, 542, 67], [559, 12, 542, 67], [559, 19, 542, 73], [560, 10, 542, 74], [561, 12, 542, 74, "fileName"], [561, 20, 542, 74], [561, 22, 542, 74, "_jsxFileName"], [561, 34, 542, 74], [562, 12, 542, 74, "lineNumber"], [562, 22, 542, 74], [563, 12, 542, 74, "columnNumber"], [563, 24, 542, 74], [564, 10, 542, 74], [564, 17, 543, 28], [564, 18, 543, 29], [564, 33, 544, 10], [564, 37, 544, 10, "_jsxDevRuntime"], [564, 51, 544, 10], [564, 52, 544, 10, "jsxDEV"], [564, 58, 544, 10], [564, 60, 544, 11, "_TouchableOpacity"], [564, 77, 544, 11], [564, 78, 544, 11, "default"], [564, 85, 544, 27], [565, 12, 544, 28, "onPress"], [565, 19, 544, 35], [565, 21, 544, 37, "onCancel"], [565, 29, 544, 46], [566, 12, 544, 47, "style"], [566, 17, 544, 52], [566, 19, 544, 54, "styles"], [566, 25, 544, 60], [566, 26, 544, 61, "secondaryButton"], [566, 41, 544, 77], [567, 12, 544, 77, "children"], [567, 20, 544, 77], [567, 35, 545, 12], [567, 39, 545, 12, "_jsxDevRuntime"], [567, 53, 545, 12], [567, 54, 545, 12, "jsxDEV"], [567, 60, 545, 12], [567, 62, 545, 13, "_Text"], [567, 67, 545, 13], [567, 68, 545, 13, "default"], [567, 75, 545, 17], [568, 14, 545, 18, "style"], [568, 19, 545, 23], [568, 21, 545, 25, "styles"], [568, 27, 545, 31], [568, 28, 545, 32, "secondaryButtonText"], [568, 47, 545, 52], [569, 14, 545, 52, "children"], [569, 22, 545, 52], [569, 24, 545, 53], [570, 12, 545, 59], [571, 14, 545, 59, "fileName"], [571, 22, 545, 59], [571, 24, 545, 59, "_jsxFileName"], [571, 36, 545, 59], [572, 14, 545, 59, "lineNumber"], [572, 24, 545, 59], [573, 14, 545, 59, "columnNumber"], [573, 26, 545, 59], [574, 12, 545, 59], [574, 19, 545, 65], [575, 10, 545, 66], [576, 12, 545, 66, "fileName"], [576, 20, 545, 66], [576, 22, 545, 66, "_jsxFileName"], [576, 34, 545, 66], [577, 12, 545, 66, "lineNumber"], [577, 22, 545, 66], [578, 12, 545, 66, "columnNumber"], [578, 24, 545, 66], [579, 10, 545, 66], [579, 17, 546, 28], [579, 18, 546, 29], [580, 8, 546, 29], [581, 10, 546, 29, "fileName"], [581, 18, 546, 29], [581, 20, 546, 29, "_jsxFileName"], [581, 32, 546, 29], [582, 10, 546, 29, "lineNumber"], [582, 20, 546, 29], [583, 10, 546, 29, "columnNumber"], [583, 22, 546, 29], [584, 8, 546, 29], [584, 15, 547, 14], [585, 6, 547, 15], [586, 8, 547, 15, "fileName"], [586, 16, 547, 15], [586, 18, 547, 15, "_jsxFileName"], [586, 30, 547, 15], [587, 8, 547, 15, "lineNumber"], [587, 18, 547, 15], [588, 8, 547, 15, "columnNumber"], [588, 20, 547, 15], [589, 6, 547, 15], [589, 13, 548, 12], [589, 14, 548, 13], [590, 4, 550, 2], [591, 4, 551, 2], [592, 4, 552, 2, "console"], [592, 11, 552, 9], [592, 12, 552, 10, "log"], [592, 15, 552, 13], [592, 16, 552, 14], [592, 55, 552, 53], [592, 56, 552, 54], [593, 4, 554, 2], [593, 24, 555, 4], [593, 28, 555, 4, "_jsxDevRuntime"], [593, 42, 555, 4], [593, 43, 555, 4, "jsxDEV"], [593, 49, 555, 4], [593, 51, 555, 5, "_View"], [593, 56, 555, 5], [593, 57, 555, 5, "default"], [593, 64, 555, 9], [594, 6, 555, 10, "style"], [594, 11, 555, 15], [594, 13, 555, 17, "styles"], [594, 19, 555, 23], [594, 20, 555, 24, "container"], [594, 29, 555, 34], [595, 6, 555, 34, "children"], [595, 14, 555, 34], [595, 30, 557, 6], [595, 34, 557, 6, "_jsxDevRuntime"], [595, 48, 557, 6], [595, 49, 557, 6, "jsxDEV"], [595, 55, 557, 6], [595, 57, 557, 7, "_View"], [595, 62, 557, 7], [595, 63, 557, 7, "default"], [595, 70, 557, 11], [596, 8, 557, 12, "style"], [596, 13, 557, 17], [596, 15, 557, 19, "styles"], [596, 21, 557, 25], [596, 22, 557, 26, "cameraContainer"], [596, 37, 557, 42], [597, 8, 557, 43, "id"], [597, 10, 557, 45], [597, 12, 557, 46], [597, 29, 557, 63], [598, 8, 557, 63, "children"], [598, 16, 557, 63], [598, 32, 558, 8], [598, 36, 558, 8, "_jsxDevRuntime"], [598, 50, 558, 8], [598, 51, 558, 8, "jsxDEV"], [598, 57, 558, 8], [598, 59, 558, 9, "_expoCamera"], [598, 70, 558, 9], [598, 71, 558, 9, "CameraView"], [598, 81, 558, 19], [599, 10, 559, 10, "ref"], [599, 13, 559, 13], [599, 15, 559, 15, "cameraRef"], [599, 24, 559, 25], [600, 10, 560, 10, "style"], [600, 15, 560, 15], [600, 17, 560, 17], [600, 18, 560, 18, "styles"], [600, 24, 560, 24], [600, 25, 560, 25, "camera"], [600, 31, 560, 31], [600, 33, 560, 33], [601, 12, 560, 35, "backgroundColor"], [601, 27, 560, 50], [601, 29, 560, 52], [602, 10, 560, 62], [602, 11, 560, 63], [602, 12, 560, 65], [603, 10, 561, 10, "facing"], [603, 16, 561, 16], [603, 18, 561, 17], [603, 24, 561, 23], [604, 10, 562, 10, "onLayout"], [604, 18, 562, 18], [604, 20, 562, 21, "e"], [604, 21, 562, 22], [604, 25, 562, 27], [605, 12, 563, 12, "console"], [605, 19, 563, 19], [605, 20, 563, 20, "log"], [605, 23, 563, 23], [605, 24, 563, 24], [605, 56, 563, 56], [605, 58, 563, 58, "e"], [605, 59, 563, 59], [605, 60, 563, 60, "nativeEvent"], [605, 71, 563, 71], [605, 72, 563, 72, "layout"], [605, 78, 563, 78], [605, 79, 563, 79], [606, 12, 564, 12, "setViewSize"], [606, 23, 564, 23], [606, 24, 564, 24], [607, 14, 564, 26, "width"], [607, 19, 564, 31], [607, 21, 564, 33, "e"], [607, 22, 564, 34], [607, 23, 564, 35, "nativeEvent"], [607, 34, 564, 46], [607, 35, 564, 47, "layout"], [607, 41, 564, 53], [607, 42, 564, 54, "width"], [607, 47, 564, 59], [608, 14, 564, 61, "height"], [608, 20, 564, 67], [608, 22, 564, 69, "e"], [608, 23, 564, 70], [608, 24, 564, 71, "nativeEvent"], [608, 35, 564, 82], [608, 36, 564, 83, "layout"], [608, 42, 564, 89], [608, 43, 564, 90, "height"], [609, 12, 564, 97], [609, 13, 564, 98], [609, 14, 564, 99], [610, 10, 565, 10], [610, 11, 565, 12], [611, 10, 566, 10, "onCameraReady"], [611, 23, 566, 23], [611, 25, 566, 25, "onCameraReady"], [611, 26, 566, 25], [611, 31, 566, 31], [612, 12, 567, 12, "console"], [612, 19, 567, 19], [612, 20, 567, 20, "log"], [612, 23, 567, 23], [612, 24, 567, 24], [612, 55, 567, 55], [612, 56, 567, 56], [613, 12, 568, 12, "setIsCameraReady"], [613, 28, 568, 28], [613, 29, 568, 29], [613, 33, 568, 33], [613, 34, 568, 34], [613, 35, 568, 35], [613, 36, 568, 36], [614, 10, 569, 10], [614, 11, 569, 12], [615, 10, 570, 10, "onMountError"], [615, 22, 570, 22], [615, 24, 570, 25, "error"], [615, 29, 570, 30], [615, 33, 570, 35], [616, 12, 571, 12, "console"], [616, 19, 571, 19], [616, 20, 571, 20, "error"], [616, 25, 571, 25], [616, 26, 571, 26], [616, 63, 571, 63], [616, 65, 571, 65, "error"], [616, 70, 571, 70], [616, 71, 571, 71], [617, 12, 572, 12, "setErrorMessage"], [617, 27, 572, 27], [617, 28, 572, 28], [617, 57, 572, 57], [617, 58, 572, 58], [618, 12, 573, 12, "setProcessingState"], [618, 30, 573, 30], [618, 31, 573, 31], [618, 38, 573, 38], [618, 39, 573, 39], [619, 10, 574, 10], [620, 8, 574, 12], [621, 10, 574, 12, "fileName"], [621, 18, 574, 12], [621, 20, 574, 12, "_jsxFileName"], [621, 32, 574, 12], [622, 10, 574, 12, "lineNumber"], [622, 20, 574, 12], [623, 10, 574, 12, "columnNumber"], [623, 22, 574, 12], [624, 8, 574, 12], [624, 15, 575, 9], [624, 16, 575, 10], [624, 18, 577, 9], [624, 19, 577, 10, "isCameraReady"], [624, 32, 577, 23], [624, 49, 578, 10], [624, 53, 578, 10, "_jsxDevRuntime"], [624, 67, 578, 10], [624, 68, 578, 10, "jsxDEV"], [624, 74, 578, 10], [624, 76, 578, 11, "_View"], [624, 81, 578, 11], [624, 82, 578, 11, "default"], [624, 89, 578, 15], [625, 10, 578, 16, "style"], [625, 15, 578, 21], [625, 17, 578, 23], [625, 18, 578, 24, "StyleSheet"], [625, 37, 578, 34], [625, 38, 578, 35, "absoluteFill"], [625, 50, 578, 47], [625, 52, 578, 49], [626, 12, 578, 51, "backgroundColor"], [626, 27, 578, 66], [626, 29, 578, 68], [626, 49, 578, 88], [627, 12, 578, 90, "justifyContent"], [627, 26, 578, 104], [627, 28, 578, 106], [627, 36, 578, 114], [628, 12, 578, 116, "alignItems"], [628, 22, 578, 126], [628, 24, 578, 128], [628, 32, 578, 136], [629, 12, 578, 138, "zIndex"], [629, 18, 578, 144], [629, 20, 578, 146], [630, 10, 578, 151], [630, 11, 578, 152], [630, 12, 578, 154], [631, 10, 578, 154, "children"], [631, 18, 578, 154], [631, 33, 579, 12], [631, 37, 579, 12, "_jsxDevRuntime"], [631, 51, 579, 12], [631, 52, 579, 12, "jsxDEV"], [631, 58, 579, 12], [631, 60, 579, 13, "_View"], [631, 65, 579, 13], [631, 66, 579, 13, "default"], [631, 73, 579, 17], [632, 12, 579, 18, "style"], [632, 17, 579, 23], [632, 19, 579, 25], [633, 14, 579, 27, "backgroundColor"], [633, 29, 579, 42], [633, 31, 579, 44], [633, 51, 579, 64], [634, 14, 579, 66, "padding"], [634, 21, 579, 73], [634, 23, 579, 75], [634, 25, 579, 77], [635, 14, 579, 79, "borderRadius"], [635, 26, 579, 91], [635, 28, 579, 93], [635, 30, 579, 95], [636, 14, 579, 97, "alignItems"], [636, 24, 579, 107], [636, 26, 579, 109], [637, 12, 579, 118], [637, 13, 579, 120], [638, 12, 579, 120, "children"], [638, 20, 579, 120], [638, 36, 580, 14], [638, 40, 580, 14, "_jsxDevRuntime"], [638, 54, 580, 14], [638, 55, 580, 14, "jsxDEV"], [638, 61, 580, 14], [638, 63, 580, 15, "_ActivityIndicator"], [638, 81, 580, 15], [638, 82, 580, 15, "default"], [638, 89, 580, 32], [639, 14, 580, 33, "size"], [639, 18, 580, 37], [639, 20, 580, 38], [639, 27, 580, 45], [640, 14, 580, 46, "color"], [640, 19, 580, 51], [640, 21, 580, 52], [640, 30, 580, 61], [641, 14, 580, 62, "style"], [641, 19, 580, 67], [641, 21, 580, 69], [642, 16, 580, 71, "marginBottom"], [642, 28, 580, 83], [642, 30, 580, 85], [643, 14, 580, 88], [644, 12, 580, 90], [645, 14, 580, 90, "fileName"], [645, 22, 580, 90], [645, 24, 580, 90, "_jsxFileName"], [645, 36, 580, 90], [646, 14, 580, 90, "lineNumber"], [646, 24, 580, 90], [647, 14, 580, 90, "columnNumber"], [647, 26, 580, 90], [648, 12, 580, 90], [648, 19, 580, 92], [648, 20, 580, 93], [648, 35, 581, 14], [648, 39, 581, 14, "_jsxDevRuntime"], [648, 53, 581, 14], [648, 54, 581, 14, "jsxDEV"], [648, 60, 581, 14], [648, 62, 581, 15, "_Text"], [648, 67, 581, 15], [648, 68, 581, 15, "default"], [648, 75, 581, 19], [649, 14, 581, 20, "style"], [649, 19, 581, 25], [649, 21, 581, 27], [650, 16, 581, 29, "color"], [650, 21, 581, 34], [650, 23, 581, 36], [650, 29, 581, 42], [651, 16, 581, 44, "fontSize"], [651, 24, 581, 52], [651, 26, 581, 54], [651, 28, 581, 56], [652, 16, 581, 58, "fontWeight"], [652, 26, 581, 68], [652, 28, 581, 70], [653, 14, 581, 76], [653, 15, 581, 78], [654, 14, 581, 78, "children"], [654, 22, 581, 78], [654, 24, 581, 79], [655, 12, 581, 101], [656, 14, 581, 101, "fileName"], [656, 22, 581, 101], [656, 24, 581, 101, "_jsxFileName"], [656, 36, 581, 101], [657, 14, 581, 101, "lineNumber"], [657, 24, 581, 101], [658, 14, 581, 101, "columnNumber"], [658, 26, 581, 101], [659, 12, 581, 101], [659, 19, 581, 107], [659, 20, 581, 108], [659, 35, 582, 14], [659, 39, 582, 14, "_jsxDevRuntime"], [659, 53, 582, 14], [659, 54, 582, 14, "jsxDEV"], [659, 60, 582, 14], [659, 62, 582, 15, "_Text"], [659, 67, 582, 15], [659, 68, 582, 15, "default"], [659, 75, 582, 19], [660, 14, 582, 20, "style"], [660, 19, 582, 25], [660, 21, 582, 27], [661, 16, 582, 29, "color"], [661, 21, 582, 34], [661, 23, 582, 36], [661, 32, 582, 45], [662, 16, 582, 47, "fontSize"], [662, 24, 582, 55], [662, 26, 582, 57], [662, 28, 582, 59], [663, 16, 582, 61, "marginTop"], [663, 25, 582, 70], [663, 27, 582, 72], [664, 14, 582, 74], [664, 15, 582, 76], [665, 14, 582, 76, "children"], [665, 22, 582, 76], [665, 24, 582, 77], [666, 12, 582, 88], [667, 14, 582, 88, "fileName"], [667, 22, 582, 88], [667, 24, 582, 88, "_jsxFileName"], [667, 36, 582, 88], [668, 14, 582, 88, "lineNumber"], [668, 24, 582, 88], [669, 14, 582, 88, "columnNumber"], [669, 26, 582, 88], [670, 12, 582, 88], [670, 19, 582, 94], [670, 20, 582, 95], [671, 10, 582, 95], [672, 12, 582, 95, "fileName"], [672, 20, 582, 95], [672, 22, 582, 95, "_jsxFileName"], [672, 34, 582, 95], [673, 12, 582, 95, "lineNumber"], [673, 22, 582, 95], [674, 12, 582, 95, "columnNumber"], [674, 24, 582, 95], [675, 10, 582, 95], [675, 17, 583, 18], [676, 8, 583, 19], [677, 10, 583, 19, "fileName"], [677, 18, 583, 19], [677, 20, 583, 19, "_jsxFileName"], [677, 32, 583, 19], [678, 10, 583, 19, "lineNumber"], [678, 20, 583, 19], [679, 10, 583, 19, "columnNumber"], [679, 22, 583, 19], [680, 8, 583, 19], [680, 15, 584, 16], [680, 16, 585, 9], [680, 18, 588, 9, "isCameraReady"], [680, 31, 588, 22], [680, 35, 588, 26, "previewBlurEnabled"], [680, 53, 588, 44], [680, 57, 588, 48, "viewSize"], [680, 65, 588, 56], [680, 66, 588, 57, "width"], [680, 71, 588, 62], [680, 74, 588, 65], [680, 75, 588, 66], [680, 92, 589, 10], [680, 96, 589, 10, "_jsxDevRuntime"], [680, 110, 589, 10], [680, 111, 589, 10, "jsxDEV"], [680, 117, 589, 10], [680, 119, 589, 10, "_jsxDevRuntime"], [680, 133, 589, 10], [680, 134, 589, 10, "Fragment"], [680, 142, 589, 10], [681, 10, 589, 10, "children"], [681, 18, 589, 10], [681, 34, 591, 12], [681, 38, 591, 12, "_jsxDevRuntime"], [681, 52, 591, 12], [681, 53, 591, 12, "jsxDEV"], [681, 59, 591, 12], [681, 61, 591, 13, "_LiveFaceCanvas"], [681, 76, 591, 13], [681, 77, 591, 13, "default"], [681, 84, 591, 27], [682, 12, 591, 28, "containerId"], [682, 23, 591, 39], [682, 25, 591, 40], [682, 42, 591, 57], [683, 12, 591, 58, "width"], [683, 17, 591, 63], [683, 19, 591, 65, "viewSize"], [683, 27, 591, 73], [683, 28, 591, 74, "width"], [683, 33, 591, 80], [684, 12, 591, 81, "height"], [684, 18, 591, 87], [684, 20, 591, 89, "viewSize"], [684, 28, 591, 97], [684, 29, 591, 98, "height"], [685, 10, 591, 105], [686, 12, 591, 105, "fileName"], [686, 20, 591, 105], [686, 22, 591, 105, "_jsxFileName"], [686, 34, 591, 105], [687, 12, 591, 105, "lineNumber"], [687, 22, 591, 105], [688, 12, 591, 105, "columnNumber"], [688, 24, 591, 105], [689, 10, 591, 105], [689, 17, 591, 107], [689, 18, 591, 108], [689, 33, 592, 12], [689, 37, 592, 12, "_jsxDevRuntime"], [689, 51, 592, 12], [689, 52, 592, 12, "jsxDEV"], [689, 58, 592, 12], [689, 60, 592, 13, "_View"], [689, 65, 592, 13], [689, 66, 592, 13, "default"], [689, 73, 592, 17], [690, 12, 592, 18, "style"], [690, 17, 592, 23], [690, 19, 592, 25], [690, 20, 592, 26, "StyleSheet"], [690, 39, 592, 36], [690, 40, 592, 37, "absoluteFill"], [690, 52, 592, 49], [690, 54, 592, 51], [691, 14, 592, 53, "pointerEvents"], [691, 27, 592, 66], [691, 29, 592, 68], [692, 12, 592, 75], [692, 13, 592, 76], [692, 14, 592, 78], [693, 12, 592, 78, "children"], [693, 20, 592, 78], [693, 36, 594, 12], [693, 40, 594, 12, "_jsxDevRuntime"], [693, 54, 594, 12], [693, 55, 594, 12, "jsxDEV"], [693, 61, 594, 12], [693, 63, 594, 13, "_expoBlur"], [693, 72, 594, 13], [693, 73, 594, 13, "BlurView"], [693, 81, 594, 21], [694, 14, 594, 22, "intensity"], [694, 23, 594, 31], [694, 25, 594, 33], [694, 27, 594, 36], [695, 14, 594, 37, "tint"], [695, 18, 594, 41], [695, 20, 594, 42], [695, 26, 594, 48], [696, 14, 594, 49, "style"], [696, 19, 594, 54], [696, 21, 594, 56], [696, 22, 594, 57, "styles"], [696, 28, 594, 63], [696, 29, 594, 64, "blurZone"], [696, 37, 594, 72], [696, 39, 594, 74], [697, 16, 595, 14, "left"], [697, 20, 595, 18], [697, 22, 595, 20], [697, 23, 595, 21], [698, 16, 596, 14, "top"], [698, 19, 596, 17], [698, 21, 596, 19, "viewSize"], [698, 29, 596, 27], [698, 30, 596, 28, "height"], [698, 36, 596, 34], [698, 39, 596, 37], [698, 42, 596, 40], [699, 16, 597, 14, "width"], [699, 21, 597, 19], [699, 23, 597, 21, "viewSize"], [699, 31, 597, 29], [699, 32, 597, 30, "width"], [699, 37, 597, 35], [700, 16, 598, 14, "height"], [700, 22, 598, 20], [700, 24, 598, 22, "viewSize"], [700, 32, 598, 30], [700, 33, 598, 31, "height"], [700, 39, 598, 37], [700, 42, 598, 40], [700, 46, 598, 44], [701, 16, 599, 14, "borderRadius"], [701, 28, 599, 26], [701, 30, 599, 28], [702, 14, 600, 12], [702, 15, 600, 13], [703, 12, 600, 15], [704, 14, 600, 15, "fileName"], [704, 22, 600, 15], [704, 24, 600, 15, "_jsxFileName"], [704, 36, 600, 15], [705, 14, 600, 15, "lineNumber"], [705, 24, 600, 15], [706, 14, 600, 15, "columnNumber"], [706, 26, 600, 15], [707, 12, 600, 15], [707, 19, 600, 17], [707, 20, 600, 18], [707, 35, 602, 12], [707, 39, 602, 12, "_jsxDevRuntime"], [707, 53, 602, 12], [707, 54, 602, 12, "jsxDEV"], [707, 60, 602, 12], [707, 62, 602, 13, "_expoBlur"], [707, 71, 602, 13], [707, 72, 602, 13, "BlurView"], [707, 80, 602, 21], [708, 14, 602, 22, "intensity"], [708, 23, 602, 31], [708, 25, 602, 33], [708, 27, 602, 36], [709, 14, 602, 37, "tint"], [709, 18, 602, 41], [709, 20, 602, 42], [709, 26, 602, 48], [710, 14, 602, 49, "style"], [710, 19, 602, 54], [710, 21, 602, 56], [710, 22, 602, 57, "styles"], [710, 28, 602, 63], [710, 29, 602, 64, "blurZone"], [710, 37, 602, 72], [710, 39, 602, 74], [711, 16, 603, 14, "left"], [711, 20, 603, 18], [711, 22, 603, 20], [711, 23, 603, 21], [712, 16, 604, 14, "top"], [712, 19, 604, 17], [712, 21, 604, 19], [712, 22, 604, 20], [713, 16, 605, 14, "width"], [713, 21, 605, 19], [713, 23, 605, 21, "viewSize"], [713, 31, 605, 29], [713, 32, 605, 30, "width"], [713, 37, 605, 35], [714, 16, 606, 14, "height"], [714, 22, 606, 20], [714, 24, 606, 22, "viewSize"], [714, 32, 606, 30], [714, 33, 606, 31, "height"], [714, 39, 606, 37], [714, 42, 606, 40], [714, 45, 606, 43], [715, 16, 607, 14, "borderRadius"], [715, 28, 607, 26], [715, 30, 607, 28], [716, 14, 608, 12], [716, 15, 608, 13], [717, 12, 608, 15], [718, 14, 608, 15, "fileName"], [718, 22, 608, 15], [718, 24, 608, 15, "_jsxFileName"], [718, 36, 608, 15], [719, 14, 608, 15, "lineNumber"], [719, 24, 608, 15], [720, 14, 608, 15, "columnNumber"], [720, 26, 608, 15], [721, 12, 608, 15], [721, 19, 608, 17], [721, 20, 608, 18], [721, 35, 610, 12], [721, 39, 610, 12, "_jsxDevRuntime"], [721, 53, 610, 12], [721, 54, 610, 12, "jsxDEV"], [721, 60, 610, 12], [721, 62, 610, 13, "_expoBlur"], [721, 71, 610, 13], [721, 72, 610, 13, "BlurView"], [721, 80, 610, 21], [722, 14, 610, 22, "intensity"], [722, 23, 610, 31], [722, 25, 610, 33], [722, 27, 610, 36], [723, 14, 610, 37, "tint"], [723, 18, 610, 41], [723, 20, 610, 42], [723, 26, 610, 48], [724, 14, 610, 49, "style"], [724, 19, 610, 54], [724, 21, 610, 56], [724, 22, 610, 57, "styles"], [724, 28, 610, 63], [724, 29, 610, 64, "blurZone"], [724, 37, 610, 72], [724, 39, 610, 74], [725, 16, 611, 14, "left"], [725, 20, 611, 18], [725, 22, 611, 20, "viewSize"], [725, 30, 611, 28], [725, 31, 611, 29, "width"], [725, 36, 611, 34], [725, 39, 611, 37], [725, 42, 611, 40], [725, 45, 611, 44, "viewSize"], [725, 53, 611, 52], [725, 54, 611, 53, "width"], [725, 59, 611, 58], [725, 62, 611, 61], [725, 66, 611, 66], [726, 16, 612, 14, "top"], [726, 19, 612, 17], [726, 21, 612, 19, "viewSize"], [726, 29, 612, 27], [726, 30, 612, 28, "height"], [726, 36, 612, 34], [726, 39, 612, 37], [726, 43, 612, 41], [726, 46, 612, 45, "viewSize"], [726, 54, 612, 53], [726, 55, 612, 54, "width"], [726, 60, 612, 59], [726, 63, 612, 62], [726, 67, 612, 67], [727, 16, 613, 14, "width"], [727, 21, 613, 19], [727, 23, 613, 21, "viewSize"], [727, 31, 613, 29], [727, 32, 613, 30, "width"], [727, 37, 613, 35], [727, 40, 613, 38], [727, 43, 613, 41], [728, 16, 614, 14, "height"], [728, 22, 614, 20], [728, 24, 614, 22, "viewSize"], [728, 32, 614, 30], [728, 33, 614, 31, "width"], [728, 38, 614, 36], [728, 41, 614, 39], [728, 44, 614, 42], [729, 16, 615, 14, "borderRadius"], [729, 28, 615, 26], [729, 30, 615, 29, "viewSize"], [729, 38, 615, 37], [729, 39, 615, 38, "width"], [729, 44, 615, 43], [729, 47, 615, 46], [729, 50, 615, 49], [729, 53, 615, 53], [730, 14, 616, 12], [730, 15, 616, 13], [731, 12, 616, 15], [732, 14, 616, 15, "fileName"], [732, 22, 616, 15], [732, 24, 616, 15, "_jsxFileName"], [732, 36, 616, 15], [733, 14, 616, 15, "lineNumber"], [733, 24, 616, 15], [734, 14, 616, 15, "columnNumber"], [734, 26, 616, 15], [735, 12, 616, 15], [735, 19, 616, 17], [735, 20, 616, 18], [735, 35, 617, 12], [735, 39, 617, 12, "_jsxDevRuntime"], [735, 53, 617, 12], [735, 54, 617, 12, "jsxDEV"], [735, 60, 617, 12], [735, 62, 617, 13, "_expoBlur"], [735, 71, 617, 13], [735, 72, 617, 13, "BlurView"], [735, 80, 617, 21], [736, 14, 617, 22, "intensity"], [736, 23, 617, 31], [736, 25, 617, 33], [736, 27, 617, 36], [737, 14, 617, 37, "tint"], [737, 18, 617, 41], [737, 20, 617, 42], [737, 26, 617, 48], [738, 14, 617, 49, "style"], [738, 19, 617, 54], [738, 21, 617, 56], [738, 22, 617, 57, "styles"], [738, 28, 617, 63], [738, 29, 617, 64, "blurZone"], [738, 37, 617, 72], [738, 39, 617, 74], [739, 16, 618, 14, "left"], [739, 20, 618, 18], [739, 22, 618, 20, "viewSize"], [739, 30, 618, 28], [739, 31, 618, 29, "width"], [739, 36, 618, 34], [739, 39, 618, 37], [739, 42, 618, 40], [739, 45, 618, 44, "viewSize"], [739, 53, 618, 52], [739, 54, 618, 53, "width"], [739, 59, 618, 58], [739, 62, 618, 61], [739, 66, 618, 66], [740, 16, 619, 14, "top"], [740, 19, 619, 17], [740, 21, 619, 19, "viewSize"], [740, 29, 619, 27], [740, 30, 619, 28, "height"], [740, 36, 619, 34], [740, 39, 619, 37], [740, 42, 619, 40], [740, 45, 619, 44, "viewSize"], [740, 53, 619, 52], [740, 54, 619, 53, "width"], [740, 59, 619, 58], [740, 62, 619, 61], [740, 66, 619, 66], [741, 16, 620, 14, "width"], [741, 21, 620, 19], [741, 23, 620, 21, "viewSize"], [741, 31, 620, 29], [741, 32, 620, 30, "width"], [741, 37, 620, 35], [741, 40, 620, 38], [741, 43, 620, 41], [742, 16, 621, 14, "height"], [742, 22, 621, 20], [742, 24, 621, 22, "viewSize"], [742, 32, 621, 30], [742, 33, 621, 31, "width"], [742, 38, 621, 36], [742, 41, 621, 39], [742, 44, 621, 42], [743, 16, 622, 14, "borderRadius"], [743, 28, 622, 26], [743, 30, 622, 29, "viewSize"], [743, 38, 622, 37], [743, 39, 622, 38, "width"], [743, 44, 622, 43], [743, 47, 622, 46], [743, 50, 622, 49], [743, 53, 622, 53], [744, 14, 623, 12], [744, 15, 623, 13], [745, 12, 623, 15], [746, 14, 623, 15, "fileName"], [746, 22, 623, 15], [746, 24, 623, 15, "_jsxFileName"], [746, 36, 623, 15], [747, 14, 623, 15, "lineNumber"], [747, 24, 623, 15], [748, 14, 623, 15, "columnNumber"], [748, 26, 623, 15], [749, 12, 623, 15], [749, 19, 623, 17], [749, 20, 623, 18], [749, 35, 624, 12], [749, 39, 624, 12, "_jsxDevRuntime"], [749, 53, 624, 12], [749, 54, 624, 12, "jsxDEV"], [749, 60, 624, 12], [749, 62, 624, 13, "_expoBlur"], [749, 71, 624, 13], [749, 72, 624, 13, "BlurView"], [749, 80, 624, 21], [750, 14, 624, 22, "intensity"], [750, 23, 624, 31], [750, 25, 624, 33], [750, 27, 624, 36], [751, 14, 624, 37, "tint"], [751, 18, 624, 41], [751, 20, 624, 42], [751, 26, 624, 48], [752, 14, 624, 49, "style"], [752, 19, 624, 54], [752, 21, 624, 56], [752, 22, 624, 57, "styles"], [752, 28, 624, 63], [752, 29, 624, 64, "blurZone"], [752, 37, 624, 72], [752, 39, 624, 74], [753, 16, 625, 14, "left"], [753, 20, 625, 18], [753, 22, 625, 20, "viewSize"], [753, 30, 625, 28], [753, 31, 625, 29, "width"], [753, 36, 625, 34], [753, 39, 625, 37], [753, 42, 625, 40], [753, 45, 625, 44, "viewSize"], [753, 53, 625, 52], [753, 54, 625, 53, "width"], [753, 59, 625, 58], [753, 62, 625, 61], [753, 66, 625, 66], [754, 16, 626, 14, "top"], [754, 19, 626, 17], [754, 21, 626, 19, "viewSize"], [754, 29, 626, 27], [754, 30, 626, 28, "height"], [754, 36, 626, 34], [754, 39, 626, 37], [754, 42, 626, 40], [754, 45, 626, 44, "viewSize"], [754, 53, 626, 52], [754, 54, 626, 53, "width"], [754, 59, 626, 58], [754, 62, 626, 61], [754, 66, 626, 66], [755, 16, 627, 14, "width"], [755, 21, 627, 19], [755, 23, 627, 21, "viewSize"], [755, 31, 627, 29], [755, 32, 627, 30, "width"], [755, 37, 627, 35], [755, 40, 627, 38], [755, 43, 627, 41], [756, 16, 628, 14, "height"], [756, 22, 628, 20], [756, 24, 628, 22, "viewSize"], [756, 32, 628, 30], [756, 33, 628, 31, "width"], [756, 38, 628, 36], [756, 41, 628, 39], [756, 44, 628, 42], [757, 16, 629, 14, "borderRadius"], [757, 28, 629, 26], [757, 30, 629, 29, "viewSize"], [757, 38, 629, 37], [757, 39, 629, 38, "width"], [757, 44, 629, 43], [757, 47, 629, 46], [757, 50, 629, 49], [757, 53, 629, 53], [758, 14, 630, 12], [758, 15, 630, 13], [759, 12, 630, 15], [760, 14, 630, 15, "fileName"], [760, 22, 630, 15], [760, 24, 630, 15, "_jsxFileName"], [760, 36, 630, 15], [761, 14, 630, 15, "lineNumber"], [761, 24, 630, 15], [762, 14, 630, 15, "columnNumber"], [762, 26, 630, 15], [763, 12, 630, 15], [763, 19, 630, 17], [763, 20, 630, 18], [763, 22, 632, 13, "__DEV__"], [763, 29, 632, 20], [763, 46, 633, 14], [763, 50, 633, 14, "_jsxDevRuntime"], [763, 64, 633, 14], [763, 65, 633, 14, "jsxDEV"], [763, 71, 633, 14], [763, 73, 633, 15, "_View"], [763, 78, 633, 15], [763, 79, 633, 15, "default"], [763, 86, 633, 19], [764, 14, 633, 20, "style"], [764, 19, 633, 25], [764, 21, 633, 27, "styles"], [764, 27, 633, 33], [764, 28, 633, 34, "previewChip"], [764, 39, 633, 46], [765, 14, 633, 46, "children"], [765, 22, 633, 46], [765, 37, 634, 16], [765, 41, 634, 16, "_jsxDevRuntime"], [765, 55, 634, 16], [765, 56, 634, 16, "jsxDEV"], [765, 62, 634, 16], [765, 64, 634, 17, "_Text"], [765, 69, 634, 17], [765, 70, 634, 17, "default"], [765, 77, 634, 21], [766, 16, 634, 22, "style"], [766, 21, 634, 27], [766, 23, 634, 29, "styles"], [766, 29, 634, 35], [766, 30, 634, 36, "previewChipText"], [766, 45, 634, 52], [767, 16, 634, 52, "children"], [767, 24, 634, 52], [767, 26, 634, 53], [768, 14, 634, 73], [769, 16, 634, 73, "fileName"], [769, 24, 634, 73], [769, 26, 634, 73, "_jsxFileName"], [769, 38, 634, 73], [770, 16, 634, 73, "lineNumber"], [770, 26, 634, 73], [771, 16, 634, 73, "columnNumber"], [771, 28, 634, 73], [772, 14, 634, 73], [772, 21, 634, 79], [773, 12, 634, 80], [774, 14, 634, 80, "fileName"], [774, 22, 634, 80], [774, 24, 634, 80, "_jsxFileName"], [774, 36, 634, 80], [775, 14, 634, 80, "lineNumber"], [775, 24, 634, 80], [776, 14, 634, 80, "columnNumber"], [776, 26, 634, 80], [777, 12, 634, 80], [777, 19, 635, 20], [777, 20, 636, 13], [778, 10, 636, 13], [779, 12, 636, 13, "fileName"], [779, 20, 636, 13], [779, 22, 636, 13, "_jsxFileName"], [779, 34, 636, 13], [780, 12, 636, 13, "lineNumber"], [780, 22, 636, 13], [781, 12, 636, 13, "columnNumber"], [781, 24, 636, 13], [782, 10, 636, 13], [782, 17, 637, 18], [782, 18, 637, 19], [783, 8, 637, 19], [783, 23, 638, 12], [783, 24, 639, 9], [783, 26, 641, 9, "isCameraReady"], [783, 39, 641, 22], [783, 56, 642, 10], [783, 60, 642, 10, "_jsxDevRuntime"], [783, 74, 642, 10], [783, 75, 642, 10, "jsxDEV"], [783, 81, 642, 10], [783, 83, 642, 10, "_jsxDevRuntime"], [783, 97, 642, 10], [783, 98, 642, 10, "Fragment"], [783, 106, 642, 10], [784, 10, 642, 10, "children"], [784, 18, 642, 10], [784, 34, 644, 12], [784, 38, 644, 12, "_jsxDevRuntime"], [784, 52, 644, 12], [784, 53, 644, 12, "jsxDEV"], [784, 59, 644, 12], [784, 61, 644, 13, "_View"], [784, 66, 644, 13], [784, 67, 644, 13, "default"], [784, 74, 644, 17], [785, 12, 644, 18, "style"], [785, 17, 644, 23], [785, 19, 644, 25, "styles"], [785, 25, 644, 31], [785, 26, 644, 32, "headerOverlay"], [785, 39, 644, 46], [786, 12, 644, 46, "children"], [786, 20, 644, 46], [786, 35, 645, 14], [786, 39, 645, 14, "_jsxDevRuntime"], [786, 53, 645, 14], [786, 54, 645, 14, "jsxDEV"], [786, 60, 645, 14], [786, 62, 645, 15, "_View"], [786, 67, 645, 15], [786, 68, 645, 15, "default"], [786, 75, 645, 19], [787, 14, 645, 20, "style"], [787, 19, 645, 25], [787, 21, 645, 27, "styles"], [787, 27, 645, 33], [787, 28, 645, 34, "headerContent"], [787, 41, 645, 48], [788, 14, 645, 48, "children"], [788, 22, 645, 48], [788, 38, 646, 16], [788, 42, 646, 16, "_jsxDevRuntime"], [788, 56, 646, 16], [788, 57, 646, 16, "jsxDEV"], [788, 63, 646, 16], [788, 65, 646, 17, "_View"], [788, 70, 646, 17], [788, 71, 646, 17, "default"], [788, 78, 646, 21], [789, 16, 646, 22, "style"], [789, 21, 646, 27], [789, 23, 646, 29, "styles"], [789, 29, 646, 35], [789, 30, 646, 36, "headerLeft"], [789, 40, 646, 47], [790, 16, 646, 47, "children"], [790, 24, 646, 47], [790, 40, 647, 18], [790, 44, 647, 18, "_jsxDevRuntime"], [790, 58, 647, 18], [790, 59, 647, 18, "jsxDEV"], [790, 65, 647, 18], [790, 67, 647, 19, "_Text"], [790, 72, 647, 19], [790, 73, 647, 19, "default"], [790, 80, 647, 23], [791, 18, 647, 24, "style"], [791, 23, 647, 29], [791, 25, 647, 31, "styles"], [791, 31, 647, 37], [791, 32, 647, 38, "headerTitle"], [791, 43, 647, 50], [792, 18, 647, 50, "children"], [792, 26, 647, 50], [792, 28, 647, 51], [793, 16, 647, 62], [794, 18, 647, 62, "fileName"], [794, 26, 647, 62], [794, 28, 647, 62, "_jsxFileName"], [794, 40, 647, 62], [795, 18, 647, 62, "lineNumber"], [795, 28, 647, 62], [796, 18, 647, 62, "columnNumber"], [796, 30, 647, 62], [797, 16, 647, 62], [797, 23, 647, 68], [797, 24, 647, 69], [797, 39, 648, 18], [797, 43, 648, 18, "_jsxDevRuntime"], [797, 57, 648, 18], [797, 58, 648, 18, "jsxDEV"], [797, 64, 648, 18], [797, 66, 648, 19, "_View"], [797, 71, 648, 19], [797, 72, 648, 19, "default"], [797, 79, 648, 23], [798, 18, 648, 24, "style"], [798, 23, 648, 29], [798, 25, 648, 31, "styles"], [798, 31, 648, 37], [798, 32, 648, 38, "subtitleRow"], [798, 43, 648, 50], [799, 18, 648, 50, "children"], [799, 26, 648, 50], [799, 42, 649, 20], [799, 46, 649, 20, "_jsxDevRuntime"], [799, 60, 649, 20], [799, 61, 649, 20, "jsxDEV"], [799, 67, 649, 20], [799, 69, 649, 21, "_Text"], [799, 74, 649, 21], [799, 75, 649, 21, "default"], [799, 82, 649, 25], [800, 20, 649, 26, "style"], [800, 25, 649, 31], [800, 27, 649, 33, "styles"], [800, 33, 649, 39], [800, 34, 649, 40, "webIcon"], [800, 41, 649, 48], [801, 20, 649, 48, "children"], [801, 28, 649, 48], [801, 30, 649, 49], [802, 18, 649, 51], [803, 20, 649, 51, "fileName"], [803, 28, 649, 51], [803, 30, 649, 51, "_jsxFileName"], [803, 42, 649, 51], [804, 20, 649, 51, "lineNumber"], [804, 30, 649, 51], [805, 20, 649, 51, "columnNumber"], [805, 32, 649, 51], [806, 18, 649, 51], [806, 25, 649, 57], [806, 26, 649, 58], [806, 41, 650, 20], [806, 45, 650, 20, "_jsxDevRuntime"], [806, 59, 650, 20], [806, 60, 650, 20, "jsxDEV"], [806, 66, 650, 20], [806, 68, 650, 21, "_Text"], [806, 73, 650, 21], [806, 74, 650, 21, "default"], [806, 81, 650, 25], [807, 20, 650, 26, "style"], [807, 25, 650, 31], [807, 27, 650, 33, "styles"], [807, 33, 650, 39], [807, 34, 650, 40, "headerSubtitle"], [807, 48, 650, 55], [808, 20, 650, 55, "children"], [808, 28, 650, 55], [808, 30, 650, 56], [809, 18, 650, 71], [810, 20, 650, 71, "fileName"], [810, 28, 650, 71], [810, 30, 650, 71, "_jsxFileName"], [810, 42, 650, 71], [811, 20, 650, 71, "lineNumber"], [811, 30, 650, 71], [812, 20, 650, 71, "columnNumber"], [812, 32, 650, 71], [813, 18, 650, 71], [813, 25, 650, 77], [813, 26, 650, 78], [814, 16, 650, 78], [815, 18, 650, 78, "fileName"], [815, 26, 650, 78], [815, 28, 650, 78, "_jsxFileName"], [815, 40, 650, 78], [816, 18, 650, 78, "lineNumber"], [816, 28, 650, 78], [817, 18, 650, 78, "columnNumber"], [817, 30, 650, 78], [818, 16, 650, 78], [818, 23, 651, 24], [818, 24, 651, 25], [818, 26, 652, 19, "challengeCode"], [818, 39, 652, 32], [818, 56, 653, 20], [818, 60, 653, 20, "_jsxDevRuntime"], [818, 74, 653, 20], [818, 75, 653, 20, "jsxDEV"], [818, 81, 653, 20], [818, 83, 653, 21, "_View"], [818, 88, 653, 21], [818, 89, 653, 21, "default"], [818, 96, 653, 25], [819, 18, 653, 26, "style"], [819, 23, 653, 31], [819, 25, 653, 33, "styles"], [819, 31, 653, 39], [819, 32, 653, 40, "challengeRow"], [819, 44, 653, 53], [820, 18, 653, 53, "children"], [820, 26, 653, 53], [820, 42, 654, 22], [820, 46, 654, 22, "_jsxDevRuntime"], [820, 60, 654, 22], [820, 61, 654, 22, "jsxDEV"], [820, 67, 654, 22], [820, 69, 654, 23, "_lucideReactNative"], [820, 87, 654, 23], [820, 88, 654, 23, "Shield"], [820, 94, 654, 29], [821, 20, 654, 30, "size"], [821, 24, 654, 34], [821, 26, 654, 36], [821, 28, 654, 39], [822, 20, 654, 40, "color"], [822, 25, 654, 45], [822, 27, 654, 46], [823, 18, 654, 52], [824, 20, 654, 52, "fileName"], [824, 28, 654, 52], [824, 30, 654, 52, "_jsxFileName"], [824, 42, 654, 52], [825, 20, 654, 52, "lineNumber"], [825, 30, 654, 52], [826, 20, 654, 52, "columnNumber"], [826, 32, 654, 52], [827, 18, 654, 52], [827, 25, 654, 54], [827, 26, 654, 55], [827, 41, 655, 22], [827, 45, 655, 22, "_jsxDevRuntime"], [827, 59, 655, 22], [827, 60, 655, 22, "jsxDEV"], [827, 66, 655, 22], [827, 68, 655, 23, "_Text"], [827, 73, 655, 23], [827, 74, 655, 23, "default"], [827, 81, 655, 27], [828, 20, 655, 28, "style"], [828, 25, 655, 33], [828, 27, 655, 35, "styles"], [828, 33, 655, 41], [828, 34, 655, 42, "challengeCode"], [828, 47, 655, 56], [829, 20, 655, 56, "children"], [829, 28, 655, 56], [829, 30, 655, 58, "challengeCode"], [830, 18, 655, 71], [831, 20, 655, 71, "fileName"], [831, 28, 655, 71], [831, 30, 655, 71, "_jsxFileName"], [831, 42, 655, 71], [832, 20, 655, 71, "lineNumber"], [832, 30, 655, 71], [833, 20, 655, 71, "columnNumber"], [833, 32, 655, 71], [834, 18, 655, 71], [834, 25, 655, 78], [834, 26, 655, 79], [835, 16, 655, 79], [836, 18, 655, 79, "fileName"], [836, 26, 655, 79], [836, 28, 655, 79, "_jsxFileName"], [836, 40, 655, 79], [837, 18, 655, 79, "lineNumber"], [837, 28, 655, 79], [838, 18, 655, 79, "columnNumber"], [838, 30, 655, 79], [839, 16, 655, 79], [839, 23, 656, 26], [839, 24, 657, 19], [840, 14, 657, 19], [841, 16, 657, 19, "fileName"], [841, 24, 657, 19], [841, 26, 657, 19, "_jsxFileName"], [841, 38, 657, 19], [842, 16, 657, 19, "lineNumber"], [842, 26, 657, 19], [843, 16, 657, 19, "columnNumber"], [843, 28, 657, 19], [844, 14, 657, 19], [844, 21, 658, 22], [844, 22, 658, 23], [844, 37, 659, 16], [844, 41, 659, 16, "_jsxDevRuntime"], [844, 55, 659, 16], [844, 56, 659, 16, "jsxDEV"], [844, 62, 659, 16], [844, 64, 659, 17, "_TouchableOpacity"], [844, 81, 659, 17], [844, 82, 659, 17, "default"], [844, 89, 659, 33], [845, 16, 659, 34, "onPress"], [845, 23, 659, 41], [845, 25, 659, 43, "onCancel"], [845, 33, 659, 52], [846, 16, 659, 53, "style"], [846, 21, 659, 58], [846, 23, 659, 60, "styles"], [846, 29, 659, 66], [846, 30, 659, 67, "closeButton"], [846, 41, 659, 79], [847, 16, 659, 79, "children"], [847, 24, 659, 79], [847, 39, 660, 18], [847, 43, 660, 18, "_jsxDevRuntime"], [847, 57, 660, 18], [847, 58, 660, 18, "jsxDEV"], [847, 64, 660, 18], [847, 66, 660, 19, "_lucideReactNative"], [847, 84, 660, 19], [847, 85, 660, 19, "X"], [847, 86, 660, 20], [848, 18, 660, 21, "size"], [848, 22, 660, 25], [848, 24, 660, 27], [848, 26, 660, 30], [849, 18, 660, 31, "color"], [849, 23, 660, 36], [849, 25, 660, 37], [850, 16, 660, 43], [851, 18, 660, 43, "fileName"], [851, 26, 660, 43], [851, 28, 660, 43, "_jsxFileName"], [851, 40, 660, 43], [852, 18, 660, 43, "lineNumber"], [852, 28, 660, 43], [853, 18, 660, 43, "columnNumber"], [853, 30, 660, 43], [854, 16, 660, 43], [854, 23, 660, 45], [855, 14, 660, 46], [856, 16, 660, 46, "fileName"], [856, 24, 660, 46], [856, 26, 660, 46, "_jsxFileName"], [856, 38, 660, 46], [857, 16, 660, 46, "lineNumber"], [857, 26, 660, 46], [858, 16, 660, 46, "columnNumber"], [858, 28, 660, 46], [859, 14, 660, 46], [859, 21, 661, 34], [859, 22, 661, 35], [860, 12, 661, 35], [861, 14, 661, 35, "fileName"], [861, 22, 661, 35], [861, 24, 661, 35, "_jsxFileName"], [861, 36, 661, 35], [862, 14, 661, 35, "lineNumber"], [862, 24, 661, 35], [863, 14, 661, 35, "columnNumber"], [863, 26, 661, 35], [864, 12, 661, 35], [864, 19, 662, 20], [865, 10, 662, 21], [866, 12, 662, 21, "fileName"], [866, 20, 662, 21], [866, 22, 662, 21, "_jsxFileName"], [866, 34, 662, 21], [867, 12, 662, 21, "lineNumber"], [867, 22, 662, 21], [868, 12, 662, 21, "columnNumber"], [868, 24, 662, 21], [869, 10, 662, 21], [869, 17, 663, 18], [869, 18, 663, 19], [869, 33, 665, 12], [869, 37, 665, 12, "_jsxDevRuntime"], [869, 51, 665, 12], [869, 52, 665, 12, "jsxDEV"], [869, 58, 665, 12], [869, 60, 665, 13, "_View"], [869, 65, 665, 13], [869, 66, 665, 13, "default"], [869, 73, 665, 17], [870, 12, 665, 18, "style"], [870, 17, 665, 23], [870, 19, 665, 25, "styles"], [870, 25, 665, 31], [870, 26, 665, 32, "privacyNotice"], [870, 39, 665, 46], [871, 12, 665, 46, "children"], [871, 20, 665, 46], [871, 36, 666, 14], [871, 40, 666, 14, "_jsxDevRuntime"], [871, 54, 666, 14], [871, 55, 666, 14, "jsxDEV"], [871, 61, 666, 14], [871, 63, 666, 15, "_lucideReactNative"], [871, 81, 666, 15], [871, 82, 666, 15, "Shield"], [871, 88, 666, 21], [872, 14, 666, 22, "size"], [872, 18, 666, 26], [872, 20, 666, 28], [872, 22, 666, 31], [873, 14, 666, 32, "color"], [873, 19, 666, 37], [873, 21, 666, 38], [874, 12, 666, 47], [875, 14, 666, 47, "fileName"], [875, 22, 666, 47], [875, 24, 666, 47, "_jsxFileName"], [875, 36, 666, 47], [876, 14, 666, 47, "lineNumber"], [876, 24, 666, 47], [877, 14, 666, 47, "columnNumber"], [877, 26, 666, 47], [878, 12, 666, 47], [878, 19, 666, 49], [878, 20, 666, 50], [878, 35, 667, 14], [878, 39, 667, 14, "_jsxDevRuntime"], [878, 53, 667, 14], [878, 54, 667, 14, "jsxDEV"], [878, 60, 667, 14], [878, 62, 667, 15, "_Text"], [878, 67, 667, 15], [878, 68, 667, 15, "default"], [878, 75, 667, 19], [879, 14, 667, 20, "style"], [879, 19, 667, 25], [879, 21, 667, 27, "styles"], [879, 27, 667, 33], [879, 28, 667, 34, "privacyText"], [879, 39, 667, 46], [880, 14, 667, 46, "children"], [880, 22, 667, 46], [880, 24, 667, 47], [881, 12, 669, 14], [882, 14, 669, 14, "fileName"], [882, 22, 669, 14], [882, 24, 669, 14, "_jsxFileName"], [882, 36, 669, 14], [883, 14, 669, 14, "lineNumber"], [883, 24, 669, 14], [884, 14, 669, 14, "columnNumber"], [884, 26, 669, 14], [885, 12, 669, 14], [885, 19, 669, 20], [885, 20, 669, 21], [886, 10, 669, 21], [887, 12, 669, 21, "fileName"], [887, 20, 669, 21], [887, 22, 669, 21, "_jsxFileName"], [887, 34, 669, 21], [888, 12, 669, 21, "lineNumber"], [888, 22, 669, 21], [889, 12, 669, 21, "columnNumber"], [889, 24, 669, 21], [890, 10, 669, 21], [890, 17, 670, 18], [890, 18, 670, 19], [890, 33, 672, 12], [890, 37, 672, 12, "_jsxDevRuntime"], [890, 51, 672, 12], [890, 52, 672, 12, "jsxDEV"], [890, 58, 672, 12], [890, 60, 672, 13, "_View"], [890, 65, 672, 13], [890, 66, 672, 13, "default"], [890, 73, 672, 17], [891, 12, 672, 18, "style"], [891, 17, 672, 23], [891, 19, 672, 25, "styles"], [891, 25, 672, 31], [891, 26, 672, 32, "footer<PERSON><PERSON><PERSON>"], [891, 39, 672, 46], [892, 12, 672, 46, "children"], [892, 20, 672, 46], [892, 36, 673, 14], [892, 40, 673, 14, "_jsxDevRuntime"], [892, 54, 673, 14], [892, 55, 673, 14, "jsxDEV"], [892, 61, 673, 14], [892, 63, 673, 15, "_Text"], [892, 68, 673, 15], [892, 69, 673, 15, "default"], [892, 76, 673, 19], [893, 14, 673, 20, "style"], [893, 19, 673, 25], [893, 21, 673, 27, "styles"], [893, 27, 673, 33], [893, 28, 673, 34, "instruction"], [893, 39, 673, 46], [894, 14, 673, 46, "children"], [894, 22, 673, 46], [894, 24, 673, 47], [895, 12, 675, 14], [896, 14, 675, 14, "fileName"], [896, 22, 675, 14], [896, 24, 675, 14, "_jsxFileName"], [896, 36, 675, 14], [897, 14, 675, 14, "lineNumber"], [897, 24, 675, 14], [898, 14, 675, 14, "columnNumber"], [898, 26, 675, 14], [899, 12, 675, 14], [899, 19, 675, 20], [899, 20, 675, 21], [899, 35, 677, 14], [899, 39, 677, 14, "_jsxDevRuntime"], [899, 53, 677, 14], [899, 54, 677, 14, "jsxDEV"], [899, 60, 677, 14], [899, 62, 677, 15, "_TouchableOpacity"], [899, 79, 677, 15], [899, 80, 677, 15, "default"], [899, 87, 677, 31], [900, 14, 678, 16, "onPress"], [900, 21, 678, 23], [900, 23, 678, 25, "capturePhoto"], [900, 35, 678, 38], [901, 14, 679, 16, "disabled"], [901, 22, 679, 24], [901, 24, 679, 26, "processingState"], [901, 39, 679, 41], [901, 44, 679, 46], [901, 50, 679, 52], [901, 54, 679, 56], [901, 55, 679, 57, "isCameraReady"], [901, 68, 679, 71], [902, 14, 680, 16, "style"], [902, 19, 680, 21], [902, 21, 680, 23], [902, 22, 681, 18, "styles"], [902, 28, 681, 24], [902, 29, 681, 25, "shutterButton"], [902, 42, 681, 38], [902, 44, 682, 18, "processingState"], [902, 59, 682, 33], [902, 64, 682, 38], [902, 70, 682, 44], [902, 74, 682, 48, "styles"], [902, 80, 682, 54], [902, 81, 682, 55, "shutterButtonDisabled"], [902, 102, 682, 76], [902, 103, 683, 18], [903, 14, 683, 18, "children"], [903, 22, 683, 18], [903, 24, 685, 17, "processingState"], [903, 39, 685, 32], [903, 44, 685, 37], [903, 50, 685, 43], [903, 66, 686, 18], [903, 70, 686, 18, "_jsxDevRuntime"], [903, 84, 686, 18], [903, 85, 686, 18, "jsxDEV"], [903, 91, 686, 18], [903, 93, 686, 19, "_View"], [903, 98, 686, 19], [903, 99, 686, 19, "default"], [903, 106, 686, 23], [904, 16, 686, 24, "style"], [904, 21, 686, 29], [904, 23, 686, 31, "styles"], [904, 29, 686, 37], [904, 30, 686, 38, "shutterInner"], [905, 14, 686, 51], [906, 16, 686, 51, "fileName"], [906, 24, 686, 51], [906, 26, 686, 51, "_jsxFileName"], [906, 38, 686, 51], [907, 16, 686, 51, "lineNumber"], [907, 26, 686, 51], [908, 16, 686, 51, "columnNumber"], [908, 28, 686, 51], [909, 14, 686, 51], [909, 21, 686, 53], [909, 22, 686, 54], [909, 38, 688, 18], [909, 42, 688, 18, "_jsxDevRuntime"], [909, 56, 688, 18], [909, 57, 688, 18, "jsxDEV"], [909, 63, 688, 18], [909, 65, 688, 19, "_ActivityIndicator"], [909, 83, 688, 19], [909, 84, 688, 19, "default"], [909, 91, 688, 36], [910, 16, 688, 37, "size"], [910, 20, 688, 41], [910, 22, 688, 42], [910, 29, 688, 49], [911, 16, 688, 50, "color"], [911, 21, 688, 55], [911, 23, 688, 56], [912, 14, 688, 65], [913, 16, 688, 65, "fileName"], [913, 24, 688, 65], [913, 26, 688, 65, "_jsxFileName"], [913, 38, 688, 65], [914, 16, 688, 65, "lineNumber"], [914, 26, 688, 65], [915, 16, 688, 65, "columnNumber"], [915, 28, 688, 65], [916, 14, 688, 65], [916, 21, 688, 67], [917, 12, 689, 17], [918, 14, 689, 17, "fileName"], [918, 22, 689, 17], [918, 24, 689, 17, "_jsxFileName"], [918, 36, 689, 17], [919, 14, 689, 17, "lineNumber"], [919, 24, 689, 17], [920, 14, 689, 17, "columnNumber"], [920, 26, 689, 17], [921, 12, 689, 17], [921, 19, 690, 32], [921, 20, 690, 33], [921, 35, 691, 14], [921, 39, 691, 14, "_jsxDevRuntime"], [921, 53, 691, 14], [921, 54, 691, 14, "jsxDEV"], [921, 60, 691, 14], [921, 62, 691, 15, "_Text"], [921, 67, 691, 15], [921, 68, 691, 15, "default"], [921, 75, 691, 19], [922, 14, 691, 20, "style"], [922, 19, 691, 25], [922, 21, 691, 27, "styles"], [922, 27, 691, 33], [922, 28, 691, 34, "privacyNote"], [922, 39, 691, 46], [923, 14, 691, 46, "children"], [923, 22, 691, 46], [923, 24, 691, 47], [924, 12, 693, 14], [925, 14, 693, 14, "fileName"], [925, 22, 693, 14], [925, 24, 693, 14, "_jsxFileName"], [925, 36, 693, 14], [926, 14, 693, 14, "lineNumber"], [926, 24, 693, 14], [927, 14, 693, 14, "columnNumber"], [927, 26, 693, 14], [928, 12, 693, 14], [928, 19, 693, 20], [928, 20, 693, 21], [929, 10, 693, 21], [930, 12, 693, 21, "fileName"], [930, 20, 693, 21], [930, 22, 693, 21, "_jsxFileName"], [930, 34, 693, 21], [931, 12, 693, 21, "lineNumber"], [931, 22, 693, 21], [932, 12, 693, 21, "columnNumber"], [932, 24, 693, 21], [933, 10, 693, 21], [933, 17, 694, 18], [933, 18, 694, 19], [934, 8, 694, 19], [934, 23, 695, 12], [934, 24, 696, 9], [935, 6, 696, 9], [936, 8, 696, 9, "fileName"], [936, 16, 696, 9], [936, 18, 696, 9, "_jsxFileName"], [936, 30, 696, 9], [937, 8, 696, 9, "lineNumber"], [937, 18, 696, 9], [938, 8, 696, 9, "columnNumber"], [938, 20, 696, 9], [939, 6, 696, 9], [939, 13, 697, 12], [939, 14, 697, 13], [939, 29, 699, 6], [939, 33, 699, 6, "_jsxDevRuntime"], [939, 47, 699, 6], [939, 48, 699, 6, "jsxDEV"], [939, 54, 699, 6], [939, 56, 699, 7, "_Modal"], [939, 62, 699, 7], [939, 63, 699, 7, "default"], [939, 70, 699, 12], [940, 8, 700, 8, "visible"], [940, 15, 700, 15], [940, 17, 700, 17, "processingState"], [940, 32, 700, 32], [940, 37, 700, 37], [940, 43, 700, 43], [940, 47, 700, 47, "processingState"], [940, 62, 700, 62], [940, 67, 700, 67], [940, 74, 700, 75], [941, 8, 701, 8, "transparent"], [941, 19, 701, 19], [942, 8, 702, 8, "animationType"], [942, 21, 702, 21], [942, 23, 702, 22], [942, 29, 702, 28], [943, 8, 702, 28, "children"], [943, 16, 702, 28], [943, 31, 704, 8], [943, 35, 704, 8, "_jsxDevRuntime"], [943, 49, 704, 8], [943, 50, 704, 8, "jsxDEV"], [943, 56, 704, 8], [943, 58, 704, 9, "_View"], [943, 63, 704, 9], [943, 64, 704, 9, "default"], [943, 71, 704, 13], [944, 10, 704, 14, "style"], [944, 15, 704, 19], [944, 17, 704, 21, "styles"], [944, 23, 704, 27], [944, 24, 704, 28, "processingModal"], [944, 39, 704, 44], [945, 10, 704, 44, "children"], [945, 18, 704, 44], [945, 33, 705, 10], [945, 37, 705, 10, "_jsxDevRuntime"], [945, 51, 705, 10], [945, 52, 705, 10, "jsxDEV"], [945, 58, 705, 10], [945, 60, 705, 11, "_View"], [945, 65, 705, 11], [945, 66, 705, 11, "default"], [945, 73, 705, 15], [946, 12, 705, 16, "style"], [946, 17, 705, 21], [946, 19, 705, 23, "styles"], [946, 25, 705, 29], [946, 26, 705, 30, "processingContent"], [946, 43, 705, 48], [947, 12, 705, 48, "children"], [947, 20, 705, 48], [947, 36, 706, 12], [947, 40, 706, 12, "_jsxDevRuntime"], [947, 54, 706, 12], [947, 55, 706, 12, "jsxDEV"], [947, 61, 706, 12], [947, 63, 706, 13, "_ActivityIndicator"], [947, 81, 706, 13], [947, 82, 706, 13, "default"], [947, 89, 706, 30], [948, 14, 706, 31, "size"], [948, 18, 706, 35], [948, 20, 706, 36], [948, 27, 706, 43], [949, 14, 706, 44, "color"], [949, 19, 706, 49], [949, 21, 706, 50], [950, 12, 706, 59], [951, 14, 706, 59, "fileName"], [951, 22, 706, 59], [951, 24, 706, 59, "_jsxFileName"], [951, 36, 706, 59], [952, 14, 706, 59, "lineNumber"], [952, 24, 706, 59], [953, 14, 706, 59, "columnNumber"], [953, 26, 706, 59], [954, 12, 706, 59], [954, 19, 706, 61], [954, 20, 706, 62], [954, 35, 708, 12], [954, 39, 708, 12, "_jsxDevRuntime"], [954, 53, 708, 12], [954, 54, 708, 12, "jsxDEV"], [954, 60, 708, 12], [954, 62, 708, 13, "_Text"], [954, 67, 708, 13], [954, 68, 708, 13, "default"], [954, 75, 708, 17], [955, 14, 708, 18, "style"], [955, 19, 708, 23], [955, 21, 708, 25, "styles"], [955, 27, 708, 31], [955, 28, 708, 32, "processingTitle"], [955, 43, 708, 48], [956, 14, 708, 48, "children"], [956, 22, 708, 48], [956, 25, 709, 15, "processingState"], [956, 40, 709, 30], [956, 45, 709, 35], [956, 56, 709, 46], [956, 60, 709, 50], [956, 80, 709, 70], [956, 82, 710, 15, "processingState"], [956, 97, 710, 30], [956, 102, 710, 35], [956, 113, 710, 46], [956, 117, 710, 50], [956, 146, 710, 79], [956, 148, 711, 15, "processingState"], [956, 163, 711, 30], [956, 168, 711, 35], [956, 180, 711, 47], [956, 184, 711, 51], [956, 216, 711, 83], [956, 218, 712, 15, "processingState"], [956, 233, 712, 30], [956, 238, 712, 35], [956, 249, 712, 46], [956, 253, 712, 50], [956, 275, 712, 72], [957, 12, 712, 72], [958, 14, 712, 72, "fileName"], [958, 22, 712, 72], [958, 24, 712, 72, "_jsxFileName"], [958, 36, 712, 72], [959, 14, 712, 72, "lineNumber"], [959, 24, 712, 72], [960, 14, 712, 72, "columnNumber"], [960, 26, 712, 72], [961, 12, 712, 72], [961, 19, 713, 18], [961, 20, 713, 19], [961, 35, 714, 12], [961, 39, 714, 12, "_jsxDevRuntime"], [961, 53, 714, 12], [961, 54, 714, 12, "jsxDEV"], [961, 60, 714, 12], [961, 62, 714, 13, "_View"], [961, 67, 714, 13], [961, 68, 714, 13, "default"], [961, 75, 714, 17], [962, 14, 714, 18, "style"], [962, 19, 714, 23], [962, 21, 714, 25, "styles"], [962, 27, 714, 31], [962, 28, 714, 32, "progressBar"], [962, 39, 714, 44], [963, 14, 714, 44, "children"], [963, 22, 714, 44], [963, 37, 715, 14], [963, 41, 715, 14, "_jsxDevRuntime"], [963, 55, 715, 14], [963, 56, 715, 14, "jsxDEV"], [963, 62, 715, 14], [963, 64, 715, 15, "_View"], [963, 69, 715, 15], [963, 70, 715, 15, "default"], [963, 77, 715, 19], [964, 16, 716, 16, "style"], [964, 21, 716, 21], [964, 23, 716, 23], [964, 24, 717, 18, "styles"], [964, 30, 717, 24], [964, 31, 717, 25, "progressFill"], [964, 43, 717, 37], [964, 45, 718, 18], [965, 18, 718, 20, "width"], [965, 23, 718, 25], [965, 25, 718, 27], [965, 28, 718, 30, "processingProgress"], [965, 46, 718, 48], [966, 16, 718, 52], [966, 17, 718, 53], [967, 14, 719, 18], [968, 16, 719, 18, "fileName"], [968, 24, 719, 18], [968, 26, 719, 18, "_jsxFileName"], [968, 38, 719, 18], [969, 16, 719, 18, "lineNumber"], [969, 26, 719, 18], [970, 16, 719, 18, "columnNumber"], [970, 28, 719, 18], [971, 14, 719, 18], [971, 21, 720, 15], [972, 12, 720, 16], [973, 14, 720, 16, "fileName"], [973, 22, 720, 16], [973, 24, 720, 16, "_jsxFileName"], [973, 36, 720, 16], [974, 14, 720, 16, "lineNumber"], [974, 24, 720, 16], [975, 14, 720, 16, "columnNumber"], [975, 26, 720, 16], [976, 12, 720, 16], [976, 19, 721, 18], [976, 20, 721, 19], [976, 35, 722, 12], [976, 39, 722, 12, "_jsxDevRuntime"], [976, 53, 722, 12], [976, 54, 722, 12, "jsxDEV"], [976, 60, 722, 12], [976, 62, 722, 13, "_Text"], [976, 67, 722, 13], [976, 68, 722, 13, "default"], [976, 75, 722, 17], [977, 14, 722, 18, "style"], [977, 19, 722, 23], [977, 21, 722, 25, "styles"], [977, 27, 722, 31], [977, 28, 722, 32, "processingDescription"], [977, 49, 722, 54], [978, 14, 722, 54, "children"], [978, 22, 722, 54], [978, 25, 723, 15, "processingState"], [978, 40, 723, 30], [978, 45, 723, 35], [978, 56, 723, 46], [978, 60, 723, 50], [978, 89, 723, 79], [978, 91, 724, 15, "processingState"], [978, 106, 724, 30], [978, 111, 724, 35], [978, 122, 724, 46], [978, 126, 724, 50], [978, 164, 724, 88], [978, 166, 725, 15, "processingState"], [978, 181, 725, 30], [978, 186, 725, 35], [978, 198, 725, 47], [978, 202, 725, 51], [978, 247, 725, 96], [978, 249, 726, 15, "processingState"], [978, 264, 726, 30], [978, 269, 726, 35], [978, 280, 726, 46], [978, 284, 726, 50], [978, 325, 726, 91], [979, 12, 726, 91], [980, 14, 726, 91, "fileName"], [980, 22, 726, 91], [980, 24, 726, 91, "_jsxFileName"], [980, 36, 726, 91], [981, 14, 726, 91, "lineNumber"], [981, 24, 726, 91], [982, 14, 726, 91, "columnNumber"], [982, 26, 726, 91], [983, 12, 726, 91], [983, 19, 727, 18], [983, 20, 727, 19], [983, 22, 728, 13, "processingState"], [983, 37, 728, 28], [983, 42, 728, 33], [983, 53, 728, 44], [983, 70, 729, 14], [983, 74, 729, 14, "_jsxDevRuntime"], [983, 88, 729, 14], [983, 89, 729, 14, "jsxDEV"], [983, 95, 729, 14], [983, 97, 729, 15, "_lucideReactNative"], [983, 115, 729, 15], [983, 116, 729, 15, "CheckCircle"], [983, 127, 729, 26], [984, 14, 729, 27, "size"], [984, 18, 729, 31], [984, 20, 729, 33], [984, 22, 729, 36], [985, 14, 729, 37, "color"], [985, 19, 729, 42], [985, 21, 729, 43], [985, 30, 729, 52], [986, 14, 729, 53, "style"], [986, 19, 729, 58], [986, 21, 729, 60, "styles"], [986, 27, 729, 66], [986, 28, 729, 67, "successIcon"], [987, 12, 729, 79], [988, 14, 729, 79, "fileName"], [988, 22, 729, 79], [988, 24, 729, 79, "_jsxFileName"], [988, 36, 729, 79], [989, 14, 729, 79, "lineNumber"], [989, 24, 729, 79], [990, 14, 729, 79, "columnNumber"], [990, 26, 729, 79], [991, 12, 729, 79], [991, 19, 729, 81], [991, 20, 730, 13], [992, 10, 730, 13], [993, 12, 730, 13, "fileName"], [993, 20, 730, 13], [993, 22, 730, 13, "_jsxFileName"], [993, 34, 730, 13], [994, 12, 730, 13, "lineNumber"], [994, 22, 730, 13], [995, 12, 730, 13, "columnNumber"], [995, 24, 730, 13], [996, 10, 730, 13], [996, 17, 731, 16], [997, 8, 731, 17], [998, 10, 731, 17, "fileName"], [998, 18, 731, 17], [998, 20, 731, 17, "_jsxFileName"], [998, 32, 731, 17], [999, 10, 731, 17, "lineNumber"], [999, 20, 731, 17], [1000, 10, 731, 17, "columnNumber"], [1000, 22, 731, 17], [1001, 8, 731, 17], [1001, 15, 732, 14], [1002, 6, 732, 15], [1003, 8, 732, 15, "fileName"], [1003, 16, 732, 15], [1003, 18, 732, 15, "_jsxFileName"], [1003, 30, 732, 15], [1004, 8, 732, 15, "lineNumber"], [1004, 18, 732, 15], [1005, 8, 732, 15, "columnNumber"], [1005, 20, 732, 15], [1006, 6, 732, 15], [1006, 13, 733, 13], [1006, 14, 733, 14], [1006, 29, 735, 6], [1006, 33, 735, 6, "_jsxDevRuntime"], [1006, 47, 735, 6], [1006, 48, 735, 6, "jsxDEV"], [1006, 54, 735, 6], [1006, 56, 735, 7, "_Modal"], [1006, 62, 735, 7], [1006, 63, 735, 7, "default"], [1006, 70, 735, 12], [1007, 8, 736, 8, "visible"], [1007, 15, 736, 15], [1007, 17, 736, 17, "processingState"], [1007, 32, 736, 32], [1007, 37, 736, 37], [1007, 44, 736, 45], [1008, 8, 737, 8, "transparent"], [1008, 19, 737, 19], [1009, 8, 738, 8, "animationType"], [1009, 21, 738, 21], [1009, 23, 738, 22], [1009, 29, 738, 28], [1010, 8, 738, 28, "children"], [1010, 16, 738, 28], [1010, 31, 740, 8], [1010, 35, 740, 8, "_jsxDevRuntime"], [1010, 49, 740, 8], [1010, 50, 740, 8, "jsxDEV"], [1010, 56, 740, 8], [1010, 58, 740, 9, "_View"], [1010, 63, 740, 9], [1010, 64, 740, 9, "default"], [1010, 71, 740, 13], [1011, 10, 740, 14, "style"], [1011, 15, 740, 19], [1011, 17, 740, 21, "styles"], [1011, 23, 740, 27], [1011, 24, 740, 28, "processingModal"], [1011, 39, 740, 44], [1012, 10, 740, 44, "children"], [1012, 18, 740, 44], [1012, 33, 741, 10], [1012, 37, 741, 10, "_jsxDevRuntime"], [1012, 51, 741, 10], [1012, 52, 741, 10, "jsxDEV"], [1012, 58, 741, 10], [1012, 60, 741, 11, "_View"], [1012, 65, 741, 11], [1012, 66, 741, 11, "default"], [1012, 73, 741, 15], [1013, 12, 741, 16, "style"], [1013, 17, 741, 21], [1013, 19, 741, 23, "styles"], [1013, 25, 741, 29], [1013, 26, 741, 30, "errorContent"], [1013, 38, 741, 43], [1014, 12, 741, 43, "children"], [1014, 20, 741, 43], [1014, 36, 742, 12], [1014, 40, 742, 12, "_jsxDevRuntime"], [1014, 54, 742, 12], [1014, 55, 742, 12, "jsxDEV"], [1014, 61, 742, 12], [1014, 63, 742, 13, "_lucideReactNative"], [1014, 81, 742, 13], [1014, 82, 742, 13, "X"], [1014, 83, 742, 14], [1015, 14, 742, 15, "size"], [1015, 18, 742, 19], [1015, 20, 742, 21], [1015, 22, 742, 24], [1016, 14, 742, 25, "color"], [1016, 19, 742, 30], [1016, 21, 742, 31], [1017, 12, 742, 40], [1018, 14, 742, 40, "fileName"], [1018, 22, 742, 40], [1018, 24, 742, 40, "_jsxFileName"], [1018, 36, 742, 40], [1019, 14, 742, 40, "lineNumber"], [1019, 24, 742, 40], [1020, 14, 742, 40, "columnNumber"], [1020, 26, 742, 40], [1021, 12, 742, 40], [1021, 19, 742, 42], [1021, 20, 742, 43], [1021, 35, 743, 12], [1021, 39, 743, 12, "_jsxDevRuntime"], [1021, 53, 743, 12], [1021, 54, 743, 12, "jsxDEV"], [1021, 60, 743, 12], [1021, 62, 743, 13, "_Text"], [1021, 67, 743, 13], [1021, 68, 743, 13, "default"], [1021, 75, 743, 17], [1022, 14, 743, 18, "style"], [1022, 19, 743, 23], [1022, 21, 743, 25, "styles"], [1022, 27, 743, 31], [1022, 28, 743, 32, "errorTitle"], [1022, 38, 743, 43], [1023, 14, 743, 43, "children"], [1023, 22, 743, 43], [1023, 24, 743, 44], [1024, 12, 743, 61], [1025, 14, 743, 61, "fileName"], [1025, 22, 743, 61], [1025, 24, 743, 61, "_jsxFileName"], [1025, 36, 743, 61], [1026, 14, 743, 61, "lineNumber"], [1026, 24, 743, 61], [1027, 14, 743, 61, "columnNumber"], [1027, 26, 743, 61], [1028, 12, 743, 61], [1028, 19, 743, 67], [1028, 20, 743, 68], [1028, 35, 744, 12], [1028, 39, 744, 12, "_jsxDevRuntime"], [1028, 53, 744, 12], [1028, 54, 744, 12, "jsxDEV"], [1028, 60, 744, 12], [1028, 62, 744, 13, "_Text"], [1028, 67, 744, 13], [1028, 68, 744, 13, "default"], [1028, 75, 744, 17], [1029, 14, 744, 18, "style"], [1029, 19, 744, 23], [1029, 21, 744, 25, "styles"], [1029, 27, 744, 31], [1029, 28, 744, 32, "errorMessage"], [1029, 40, 744, 45], [1030, 14, 744, 45, "children"], [1030, 22, 744, 45], [1030, 24, 744, 47, "errorMessage"], [1031, 12, 744, 59], [1032, 14, 744, 59, "fileName"], [1032, 22, 744, 59], [1032, 24, 744, 59, "_jsxFileName"], [1032, 36, 744, 59], [1033, 14, 744, 59, "lineNumber"], [1033, 24, 744, 59], [1034, 14, 744, 59, "columnNumber"], [1034, 26, 744, 59], [1035, 12, 744, 59], [1035, 19, 744, 66], [1035, 20, 744, 67], [1035, 35, 745, 12], [1035, 39, 745, 12, "_jsxDevRuntime"], [1035, 53, 745, 12], [1035, 54, 745, 12, "jsxDEV"], [1035, 60, 745, 12], [1035, 62, 745, 13, "_TouchableOpacity"], [1035, 79, 745, 13], [1035, 80, 745, 13, "default"], [1035, 87, 745, 29], [1036, 14, 746, 14, "onPress"], [1036, 21, 746, 21], [1036, 23, 746, 23, "retryCapture"], [1036, 35, 746, 36], [1037, 14, 747, 14, "style"], [1037, 19, 747, 19], [1037, 21, 747, 21, "styles"], [1037, 27, 747, 27], [1037, 28, 747, 28, "primaryButton"], [1037, 41, 747, 42], [1038, 14, 747, 42, "children"], [1038, 22, 747, 42], [1038, 37, 749, 14], [1038, 41, 749, 14, "_jsxDevRuntime"], [1038, 55, 749, 14], [1038, 56, 749, 14, "jsxDEV"], [1038, 62, 749, 14], [1038, 64, 749, 15, "_Text"], [1038, 69, 749, 15], [1038, 70, 749, 15, "default"], [1038, 77, 749, 19], [1039, 16, 749, 20, "style"], [1039, 21, 749, 25], [1039, 23, 749, 27, "styles"], [1039, 29, 749, 33], [1039, 30, 749, 34, "primaryButtonText"], [1039, 47, 749, 52], [1040, 16, 749, 52, "children"], [1040, 24, 749, 52], [1040, 26, 749, 53], [1041, 14, 749, 62], [1042, 16, 749, 62, "fileName"], [1042, 24, 749, 62], [1042, 26, 749, 62, "_jsxFileName"], [1042, 38, 749, 62], [1043, 16, 749, 62, "lineNumber"], [1043, 26, 749, 62], [1044, 16, 749, 62, "columnNumber"], [1044, 28, 749, 62], [1045, 14, 749, 62], [1045, 21, 749, 68], [1046, 12, 749, 69], [1047, 14, 749, 69, "fileName"], [1047, 22, 749, 69], [1047, 24, 749, 69, "_jsxFileName"], [1047, 36, 749, 69], [1048, 14, 749, 69, "lineNumber"], [1048, 24, 749, 69], [1049, 14, 749, 69, "columnNumber"], [1049, 26, 749, 69], [1050, 12, 749, 69], [1050, 19, 750, 30], [1050, 20, 750, 31], [1050, 35, 751, 12], [1050, 39, 751, 12, "_jsxDevRuntime"], [1050, 53, 751, 12], [1050, 54, 751, 12, "jsxDEV"], [1050, 60, 751, 12], [1050, 62, 751, 13, "_TouchableOpacity"], [1050, 79, 751, 13], [1050, 80, 751, 13, "default"], [1050, 87, 751, 29], [1051, 14, 752, 14, "onPress"], [1051, 21, 752, 21], [1051, 23, 752, 23, "onCancel"], [1051, 31, 752, 32], [1052, 14, 753, 14, "style"], [1052, 19, 753, 19], [1052, 21, 753, 21, "styles"], [1052, 27, 753, 27], [1052, 28, 753, 28, "secondaryButton"], [1052, 43, 753, 44], [1053, 14, 753, 44, "children"], [1053, 22, 753, 44], [1053, 37, 755, 14], [1053, 41, 755, 14, "_jsxDevRuntime"], [1053, 55, 755, 14], [1053, 56, 755, 14, "jsxDEV"], [1053, 62, 755, 14], [1053, 64, 755, 15, "_Text"], [1053, 69, 755, 15], [1053, 70, 755, 15, "default"], [1053, 77, 755, 19], [1054, 16, 755, 20, "style"], [1054, 21, 755, 25], [1054, 23, 755, 27, "styles"], [1054, 29, 755, 33], [1054, 30, 755, 34, "secondaryButtonText"], [1054, 49, 755, 54], [1055, 16, 755, 54, "children"], [1055, 24, 755, 54], [1055, 26, 755, 55], [1056, 14, 755, 61], [1057, 16, 755, 61, "fileName"], [1057, 24, 755, 61], [1057, 26, 755, 61, "_jsxFileName"], [1057, 38, 755, 61], [1058, 16, 755, 61, "lineNumber"], [1058, 26, 755, 61], [1059, 16, 755, 61, "columnNumber"], [1059, 28, 755, 61], [1060, 14, 755, 61], [1060, 21, 755, 67], [1061, 12, 755, 68], [1062, 14, 755, 68, "fileName"], [1062, 22, 755, 68], [1062, 24, 755, 68, "_jsxFileName"], [1062, 36, 755, 68], [1063, 14, 755, 68, "lineNumber"], [1063, 24, 755, 68], [1064, 14, 755, 68, "columnNumber"], [1064, 26, 755, 68], [1065, 12, 755, 68], [1065, 19, 756, 30], [1065, 20, 756, 31], [1066, 10, 756, 31], [1067, 12, 756, 31, "fileName"], [1067, 20, 756, 31], [1067, 22, 756, 31, "_jsxFileName"], [1067, 34, 756, 31], [1068, 12, 756, 31, "lineNumber"], [1068, 22, 756, 31], [1069, 12, 756, 31, "columnNumber"], [1069, 24, 756, 31], [1070, 10, 756, 31], [1070, 17, 757, 16], [1071, 8, 757, 17], [1072, 10, 757, 17, "fileName"], [1072, 18, 757, 17], [1072, 20, 757, 17, "_jsxFileName"], [1072, 32, 757, 17], [1073, 10, 757, 17, "lineNumber"], [1073, 20, 757, 17], [1074, 10, 757, 17, "columnNumber"], [1074, 22, 757, 17], [1075, 8, 757, 17], [1075, 15, 758, 14], [1076, 6, 758, 15], [1077, 8, 758, 15, "fileName"], [1077, 16, 758, 15], [1077, 18, 758, 15, "_jsxFileName"], [1077, 30, 758, 15], [1078, 8, 758, 15, "lineNumber"], [1078, 18, 758, 15], [1079, 8, 758, 15, "columnNumber"], [1079, 20, 758, 15], [1080, 6, 758, 15], [1080, 13, 759, 13], [1080, 14, 759, 14], [1081, 4, 759, 14], [1082, 6, 759, 14, "fileName"], [1082, 14, 759, 14], [1082, 16, 759, 14, "_jsxFileName"], [1082, 28, 759, 14], [1083, 6, 759, 14, "lineNumber"], [1083, 16, 759, 14], [1084, 6, 759, 14, "columnNumber"], [1084, 18, 759, 14], [1085, 4, 759, 14], [1085, 11, 760, 10], [1085, 12, 760, 11], [1086, 2, 762, 0], [1087, 2, 762, 1, "_s"], [1087, 4, 762, 1], [1087, 5, 51, 24, "EchoCameraWeb"], [1087, 18, 51, 37], [1088, 4, 51, 37], [1088, 12, 58, 42, "useCameraPermissions"], [1088, 44, 58, 62], [1088, 46, 72, 19, "useUpload"], [1088, 64, 72, 28], [1089, 2, 72, 28], [1090, 2, 72, 28, "_c"], [1090, 4, 72, 28], [1090, 7, 51, 24, "EchoCameraWeb"], [1090, 20, 51, 37], [1091, 2, 763, 0], [1091, 8, 763, 6, "styles"], [1091, 14, 763, 12], [1091, 17, 763, 15, "StyleSheet"], [1091, 36, 763, 25], [1091, 37, 763, 26, "create"], [1091, 43, 763, 32], [1091, 44, 763, 33], [1092, 4, 764, 2, "container"], [1092, 13, 764, 11], [1092, 15, 764, 13], [1093, 6, 765, 4, "flex"], [1093, 10, 765, 8], [1093, 12, 765, 10], [1093, 13, 765, 11], [1094, 6, 766, 4, "backgroundColor"], [1094, 21, 766, 19], [1094, 23, 766, 21], [1095, 4, 767, 2], [1095, 5, 767, 3], [1096, 4, 768, 2, "cameraContainer"], [1096, 19, 768, 17], [1096, 21, 768, 19], [1097, 6, 769, 4, "flex"], [1097, 10, 769, 8], [1097, 12, 769, 10], [1097, 13, 769, 11], [1098, 6, 770, 4, "max<PERSON><PERSON><PERSON>"], [1098, 14, 770, 12], [1098, 16, 770, 14], [1098, 19, 770, 17], [1099, 6, 771, 4, "alignSelf"], [1099, 15, 771, 13], [1099, 17, 771, 15], [1099, 25, 771, 23], [1100, 6, 772, 4, "width"], [1100, 11, 772, 9], [1100, 13, 772, 11], [1101, 4, 773, 2], [1101, 5, 773, 3], [1102, 4, 774, 2, "camera"], [1102, 10, 774, 8], [1102, 12, 774, 10], [1103, 6, 775, 4, "flex"], [1103, 10, 775, 8], [1103, 12, 775, 10], [1104, 4, 776, 2], [1104, 5, 776, 3], [1105, 4, 777, 2, "headerOverlay"], [1105, 17, 777, 15], [1105, 19, 777, 17], [1106, 6, 778, 4, "position"], [1106, 14, 778, 12], [1106, 16, 778, 14], [1106, 26, 778, 24], [1107, 6, 779, 4, "top"], [1107, 9, 779, 7], [1107, 11, 779, 9], [1107, 12, 779, 10], [1108, 6, 780, 4, "left"], [1108, 10, 780, 8], [1108, 12, 780, 10], [1108, 13, 780, 11], [1109, 6, 781, 4, "right"], [1109, 11, 781, 9], [1109, 13, 781, 11], [1109, 14, 781, 12], [1110, 6, 782, 4, "backgroundColor"], [1110, 21, 782, 19], [1110, 23, 782, 21], [1110, 36, 782, 34], [1111, 6, 783, 4, "paddingTop"], [1111, 16, 783, 14], [1111, 18, 783, 16], [1111, 20, 783, 18], [1112, 6, 784, 4, "paddingHorizontal"], [1112, 23, 784, 21], [1112, 25, 784, 23], [1112, 27, 784, 25], [1113, 6, 785, 4, "paddingBottom"], [1113, 19, 785, 17], [1113, 21, 785, 19], [1114, 4, 786, 2], [1114, 5, 786, 3], [1115, 4, 787, 2, "headerContent"], [1115, 17, 787, 15], [1115, 19, 787, 17], [1116, 6, 788, 4, "flexDirection"], [1116, 19, 788, 17], [1116, 21, 788, 19], [1116, 26, 788, 24], [1117, 6, 789, 4, "justifyContent"], [1117, 20, 789, 18], [1117, 22, 789, 20], [1117, 37, 789, 35], [1118, 6, 790, 4, "alignItems"], [1118, 16, 790, 14], [1118, 18, 790, 16], [1119, 4, 791, 2], [1119, 5, 791, 3], [1120, 4, 792, 2, "headerLeft"], [1120, 14, 792, 12], [1120, 16, 792, 14], [1121, 6, 793, 4, "flex"], [1121, 10, 793, 8], [1121, 12, 793, 10], [1122, 4, 794, 2], [1122, 5, 794, 3], [1123, 4, 795, 2, "headerTitle"], [1123, 15, 795, 13], [1123, 17, 795, 15], [1124, 6, 796, 4, "fontSize"], [1124, 14, 796, 12], [1124, 16, 796, 14], [1124, 18, 796, 16], [1125, 6, 797, 4, "fontWeight"], [1125, 16, 797, 14], [1125, 18, 797, 16], [1125, 23, 797, 21], [1126, 6, 798, 4, "color"], [1126, 11, 798, 9], [1126, 13, 798, 11], [1126, 19, 798, 17], [1127, 6, 799, 4, "marginBottom"], [1127, 18, 799, 16], [1127, 20, 799, 18], [1128, 4, 800, 2], [1128, 5, 800, 3], [1129, 4, 801, 2, "subtitleRow"], [1129, 15, 801, 13], [1129, 17, 801, 15], [1130, 6, 802, 4, "flexDirection"], [1130, 19, 802, 17], [1130, 21, 802, 19], [1130, 26, 802, 24], [1131, 6, 803, 4, "alignItems"], [1131, 16, 803, 14], [1131, 18, 803, 16], [1131, 26, 803, 24], [1132, 6, 804, 4, "marginBottom"], [1132, 18, 804, 16], [1132, 20, 804, 18], [1133, 4, 805, 2], [1133, 5, 805, 3], [1134, 4, 806, 2, "webIcon"], [1134, 11, 806, 9], [1134, 13, 806, 11], [1135, 6, 807, 4, "fontSize"], [1135, 14, 807, 12], [1135, 16, 807, 14], [1135, 18, 807, 16], [1136, 6, 808, 4, "marginRight"], [1136, 17, 808, 15], [1136, 19, 808, 17], [1137, 4, 809, 2], [1137, 5, 809, 3], [1138, 4, 810, 2, "headerSubtitle"], [1138, 18, 810, 16], [1138, 20, 810, 18], [1139, 6, 811, 4, "fontSize"], [1139, 14, 811, 12], [1139, 16, 811, 14], [1139, 18, 811, 16], [1140, 6, 812, 4, "color"], [1140, 11, 812, 9], [1140, 13, 812, 11], [1140, 19, 812, 17], [1141, 6, 813, 4, "opacity"], [1141, 13, 813, 11], [1141, 15, 813, 13], [1142, 4, 814, 2], [1142, 5, 814, 3], [1143, 4, 815, 2, "challengeRow"], [1143, 16, 815, 14], [1143, 18, 815, 16], [1144, 6, 816, 4, "flexDirection"], [1144, 19, 816, 17], [1144, 21, 816, 19], [1144, 26, 816, 24], [1145, 6, 817, 4, "alignItems"], [1145, 16, 817, 14], [1145, 18, 817, 16], [1146, 4, 818, 2], [1146, 5, 818, 3], [1147, 4, 819, 2, "challengeCode"], [1147, 17, 819, 15], [1147, 19, 819, 17], [1148, 6, 820, 4, "fontSize"], [1148, 14, 820, 12], [1148, 16, 820, 14], [1148, 18, 820, 16], [1149, 6, 821, 4, "color"], [1149, 11, 821, 9], [1149, 13, 821, 11], [1149, 19, 821, 17], [1150, 6, 822, 4, "marginLeft"], [1150, 16, 822, 14], [1150, 18, 822, 16], [1150, 19, 822, 17], [1151, 6, 823, 4, "fontFamily"], [1151, 16, 823, 14], [1151, 18, 823, 16], [1152, 4, 824, 2], [1152, 5, 824, 3], [1153, 4, 825, 2, "closeButton"], [1153, 15, 825, 13], [1153, 17, 825, 15], [1154, 6, 826, 4, "padding"], [1154, 13, 826, 11], [1154, 15, 826, 13], [1155, 4, 827, 2], [1155, 5, 827, 3], [1156, 4, 828, 2, "privacyNotice"], [1156, 17, 828, 15], [1156, 19, 828, 17], [1157, 6, 829, 4, "position"], [1157, 14, 829, 12], [1157, 16, 829, 14], [1157, 26, 829, 24], [1158, 6, 830, 4, "top"], [1158, 9, 830, 7], [1158, 11, 830, 9], [1158, 14, 830, 12], [1159, 6, 831, 4, "left"], [1159, 10, 831, 8], [1159, 12, 831, 10], [1159, 14, 831, 12], [1160, 6, 832, 4, "right"], [1160, 11, 832, 9], [1160, 13, 832, 11], [1160, 15, 832, 13], [1161, 6, 833, 4, "backgroundColor"], [1161, 21, 833, 19], [1161, 23, 833, 21], [1161, 48, 833, 46], [1162, 6, 834, 4, "borderRadius"], [1162, 18, 834, 16], [1162, 20, 834, 18], [1162, 21, 834, 19], [1163, 6, 835, 4, "padding"], [1163, 13, 835, 11], [1163, 15, 835, 13], [1163, 17, 835, 15], [1164, 6, 836, 4, "flexDirection"], [1164, 19, 836, 17], [1164, 21, 836, 19], [1164, 26, 836, 24], [1165, 6, 837, 4, "alignItems"], [1165, 16, 837, 14], [1165, 18, 837, 16], [1166, 4, 838, 2], [1166, 5, 838, 3], [1167, 4, 839, 2, "privacyText"], [1167, 15, 839, 13], [1167, 17, 839, 15], [1168, 6, 840, 4, "color"], [1168, 11, 840, 9], [1168, 13, 840, 11], [1168, 19, 840, 17], [1169, 6, 841, 4, "fontSize"], [1169, 14, 841, 12], [1169, 16, 841, 14], [1169, 18, 841, 16], [1170, 6, 842, 4, "marginLeft"], [1170, 16, 842, 14], [1170, 18, 842, 16], [1170, 19, 842, 17], [1171, 6, 843, 4, "flex"], [1171, 10, 843, 8], [1171, 12, 843, 10], [1172, 4, 844, 2], [1172, 5, 844, 3], [1173, 4, 845, 2, "footer<PERSON><PERSON><PERSON>"], [1173, 17, 845, 15], [1173, 19, 845, 17], [1174, 6, 846, 4, "position"], [1174, 14, 846, 12], [1174, 16, 846, 14], [1174, 26, 846, 24], [1175, 6, 847, 4, "bottom"], [1175, 12, 847, 10], [1175, 14, 847, 12], [1175, 15, 847, 13], [1176, 6, 848, 4, "left"], [1176, 10, 848, 8], [1176, 12, 848, 10], [1176, 13, 848, 11], [1177, 6, 849, 4, "right"], [1177, 11, 849, 9], [1177, 13, 849, 11], [1177, 14, 849, 12], [1178, 6, 850, 4, "backgroundColor"], [1178, 21, 850, 19], [1178, 23, 850, 21], [1178, 36, 850, 34], [1179, 6, 851, 4, "paddingBottom"], [1179, 19, 851, 17], [1179, 21, 851, 19], [1179, 23, 851, 21], [1180, 6, 852, 4, "paddingTop"], [1180, 16, 852, 14], [1180, 18, 852, 16], [1180, 20, 852, 18], [1181, 6, 853, 4, "alignItems"], [1181, 16, 853, 14], [1181, 18, 853, 16], [1182, 4, 854, 2], [1182, 5, 854, 3], [1183, 4, 855, 2, "instruction"], [1183, 15, 855, 13], [1183, 17, 855, 15], [1184, 6, 856, 4, "fontSize"], [1184, 14, 856, 12], [1184, 16, 856, 14], [1184, 18, 856, 16], [1185, 6, 857, 4, "color"], [1185, 11, 857, 9], [1185, 13, 857, 11], [1185, 19, 857, 17], [1186, 6, 858, 4, "marginBottom"], [1186, 18, 858, 16], [1186, 20, 858, 18], [1187, 4, 859, 2], [1187, 5, 859, 3], [1188, 4, 860, 2, "shutterButton"], [1188, 17, 860, 15], [1188, 19, 860, 17], [1189, 6, 861, 4, "width"], [1189, 11, 861, 9], [1189, 13, 861, 11], [1189, 15, 861, 13], [1190, 6, 862, 4, "height"], [1190, 12, 862, 10], [1190, 14, 862, 12], [1190, 16, 862, 14], [1191, 6, 863, 4, "borderRadius"], [1191, 18, 863, 16], [1191, 20, 863, 18], [1191, 22, 863, 20], [1192, 6, 864, 4, "backgroundColor"], [1192, 21, 864, 19], [1192, 23, 864, 21], [1192, 29, 864, 27], [1193, 6, 865, 4, "justifyContent"], [1193, 20, 865, 18], [1193, 22, 865, 20], [1193, 30, 865, 28], [1194, 6, 866, 4, "alignItems"], [1194, 16, 866, 14], [1194, 18, 866, 16], [1194, 26, 866, 24], [1195, 6, 867, 4, "marginBottom"], [1195, 18, 867, 16], [1195, 20, 867, 18], [1195, 22, 867, 20], [1196, 6, 868, 4], [1196, 9, 868, 7, "Platform"], [1196, 26, 868, 15], [1196, 27, 868, 16, "select"], [1196, 33, 868, 22], [1196, 34, 868, 23], [1197, 8, 869, 6, "ios"], [1197, 11, 869, 9], [1197, 13, 869, 11], [1198, 10, 870, 8, "shadowColor"], [1198, 21, 870, 19], [1198, 23, 870, 21], [1198, 32, 870, 30], [1199, 10, 871, 8, "shadowOffset"], [1199, 22, 871, 20], [1199, 24, 871, 22], [1200, 12, 871, 24, "width"], [1200, 17, 871, 29], [1200, 19, 871, 31], [1200, 20, 871, 32], [1201, 12, 871, 34, "height"], [1201, 18, 871, 40], [1201, 20, 871, 42], [1202, 10, 871, 44], [1202, 11, 871, 45], [1203, 10, 872, 8, "shadowOpacity"], [1203, 23, 872, 21], [1203, 25, 872, 23], [1203, 28, 872, 26], [1204, 10, 873, 8, "shadowRadius"], [1204, 22, 873, 20], [1204, 24, 873, 22], [1205, 8, 874, 6], [1205, 9, 874, 7], [1206, 8, 875, 6, "android"], [1206, 15, 875, 13], [1206, 17, 875, 15], [1207, 10, 876, 8, "elevation"], [1207, 19, 876, 17], [1207, 21, 876, 19], [1208, 8, 877, 6], [1208, 9, 877, 7], [1209, 8, 878, 6, "web"], [1209, 11, 878, 9], [1209, 13, 878, 11], [1210, 10, 879, 8, "boxShadow"], [1210, 19, 879, 17], [1210, 21, 879, 19], [1211, 8, 880, 6], [1212, 6, 881, 4], [1212, 7, 881, 5], [1213, 4, 882, 2], [1213, 5, 882, 3], [1214, 4, 883, 2, "shutterButtonDisabled"], [1214, 25, 883, 23], [1214, 27, 883, 25], [1215, 6, 884, 4, "opacity"], [1215, 13, 884, 11], [1215, 15, 884, 13], [1216, 4, 885, 2], [1216, 5, 885, 3], [1217, 4, 886, 2, "shutterInner"], [1217, 16, 886, 14], [1217, 18, 886, 16], [1218, 6, 887, 4, "width"], [1218, 11, 887, 9], [1218, 13, 887, 11], [1218, 15, 887, 13], [1219, 6, 888, 4, "height"], [1219, 12, 888, 10], [1219, 14, 888, 12], [1219, 16, 888, 14], [1220, 6, 889, 4, "borderRadius"], [1220, 18, 889, 16], [1220, 20, 889, 18], [1220, 22, 889, 20], [1221, 6, 890, 4, "backgroundColor"], [1221, 21, 890, 19], [1221, 23, 890, 21], [1221, 29, 890, 27], [1222, 6, 891, 4, "borderWidth"], [1222, 17, 891, 15], [1222, 19, 891, 17], [1222, 20, 891, 18], [1223, 6, 892, 4, "borderColor"], [1223, 17, 892, 15], [1223, 19, 892, 17], [1224, 4, 893, 2], [1224, 5, 893, 3], [1225, 4, 894, 2, "privacyNote"], [1225, 15, 894, 13], [1225, 17, 894, 15], [1226, 6, 895, 4, "fontSize"], [1226, 14, 895, 12], [1226, 16, 895, 14], [1226, 18, 895, 16], [1227, 6, 896, 4, "color"], [1227, 11, 896, 9], [1227, 13, 896, 11], [1228, 4, 897, 2], [1228, 5, 897, 3], [1229, 4, 898, 2, "processingModal"], [1229, 19, 898, 17], [1229, 21, 898, 19], [1230, 6, 899, 4, "flex"], [1230, 10, 899, 8], [1230, 12, 899, 10], [1230, 13, 899, 11], [1231, 6, 900, 4, "backgroundColor"], [1231, 21, 900, 19], [1231, 23, 900, 21], [1231, 43, 900, 41], [1232, 6, 901, 4, "justifyContent"], [1232, 20, 901, 18], [1232, 22, 901, 20], [1232, 30, 901, 28], [1233, 6, 902, 4, "alignItems"], [1233, 16, 902, 14], [1233, 18, 902, 16], [1234, 4, 903, 2], [1234, 5, 903, 3], [1235, 4, 904, 2, "processingContent"], [1235, 21, 904, 19], [1235, 23, 904, 21], [1236, 6, 905, 4, "backgroundColor"], [1236, 21, 905, 19], [1236, 23, 905, 21], [1236, 29, 905, 27], [1237, 6, 906, 4, "borderRadius"], [1237, 18, 906, 16], [1237, 20, 906, 18], [1237, 22, 906, 20], [1238, 6, 907, 4, "padding"], [1238, 13, 907, 11], [1238, 15, 907, 13], [1238, 17, 907, 15], [1239, 6, 908, 4, "width"], [1239, 11, 908, 9], [1239, 13, 908, 11], [1239, 18, 908, 16], [1240, 6, 909, 4, "max<PERSON><PERSON><PERSON>"], [1240, 14, 909, 12], [1240, 16, 909, 14], [1240, 19, 909, 17], [1241, 6, 910, 4, "alignItems"], [1241, 16, 910, 14], [1241, 18, 910, 16], [1242, 4, 911, 2], [1242, 5, 911, 3], [1243, 4, 912, 2, "processingTitle"], [1243, 19, 912, 17], [1243, 21, 912, 19], [1244, 6, 913, 4, "fontSize"], [1244, 14, 913, 12], [1244, 16, 913, 14], [1244, 18, 913, 16], [1245, 6, 914, 4, "fontWeight"], [1245, 16, 914, 14], [1245, 18, 914, 16], [1245, 23, 914, 21], [1246, 6, 915, 4, "color"], [1246, 11, 915, 9], [1246, 13, 915, 11], [1246, 22, 915, 20], [1247, 6, 916, 4, "marginTop"], [1247, 15, 916, 13], [1247, 17, 916, 15], [1247, 19, 916, 17], [1248, 6, 917, 4, "marginBottom"], [1248, 18, 917, 16], [1248, 20, 917, 18], [1249, 4, 918, 2], [1249, 5, 918, 3], [1250, 4, 919, 2, "progressBar"], [1250, 15, 919, 13], [1250, 17, 919, 15], [1251, 6, 920, 4, "width"], [1251, 11, 920, 9], [1251, 13, 920, 11], [1251, 19, 920, 17], [1252, 6, 921, 4, "height"], [1252, 12, 921, 10], [1252, 14, 921, 12], [1252, 15, 921, 13], [1253, 6, 922, 4, "backgroundColor"], [1253, 21, 922, 19], [1253, 23, 922, 21], [1253, 32, 922, 30], [1254, 6, 923, 4, "borderRadius"], [1254, 18, 923, 16], [1254, 20, 923, 18], [1254, 21, 923, 19], [1255, 6, 924, 4, "overflow"], [1255, 14, 924, 12], [1255, 16, 924, 14], [1255, 24, 924, 22], [1256, 6, 925, 4, "marginBottom"], [1256, 18, 925, 16], [1256, 20, 925, 18], [1257, 4, 926, 2], [1257, 5, 926, 3], [1258, 4, 927, 2, "progressFill"], [1258, 16, 927, 14], [1258, 18, 927, 16], [1259, 6, 928, 4, "height"], [1259, 12, 928, 10], [1259, 14, 928, 12], [1259, 20, 928, 18], [1260, 6, 929, 4, "backgroundColor"], [1260, 21, 929, 19], [1260, 23, 929, 21], [1260, 32, 929, 30], [1261, 6, 930, 4, "borderRadius"], [1261, 18, 930, 16], [1261, 20, 930, 18], [1262, 4, 931, 2], [1262, 5, 931, 3], [1263, 4, 932, 2, "processingDescription"], [1263, 25, 932, 23], [1263, 27, 932, 25], [1264, 6, 933, 4, "fontSize"], [1264, 14, 933, 12], [1264, 16, 933, 14], [1264, 18, 933, 16], [1265, 6, 934, 4, "color"], [1265, 11, 934, 9], [1265, 13, 934, 11], [1265, 22, 934, 20], [1266, 6, 935, 4, "textAlign"], [1266, 15, 935, 13], [1266, 17, 935, 15], [1267, 4, 936, 2], [1267, 5, 936, 3], [1268, 4, 937, 2, "successIcon"], [1268, 15, 937, 13], [1268, 17, 937, 15], [1269, 6, 938, 4, "marginTop"], [1269, 15, 938, 13], [1269, 17, 938, 15], [1270, 4, 939, 2], [1270, 5, 939, 3], [1271, 4, 940, 2, "errorContent"], [1271, 16, 940, 14], [1271, 18, 940, 16], [1272, 6, 941, 4, "backgroundColor"], [1272, 21, 941, 19], [1272, 23, 941, 21], [1272, 29, 941, 27], [1273, 6, 942, 4, "borderRadius"], [1273, 18, 942, 16], [1273, 20, 942, 18], [1273, 22, 942, 20], [1274, 6, 943, 4, "padding"], [1274, 13, 943, 11], [1274, 15, 943, 13], [1274, 17, 943, 15], [1275, 6, 944, 4, "width"], [1275, 11, 944, 9], [1275, 13, 944, 11], [1275, 18, 944, 16], [1276, 6, 945, 4, "max<PERSON><PERSON><PERSON>"], [1276, 14, 945, 12], [1276, 16, 945, 14], [1276, 19, 945, 17], [1277, 6, 946, 4, "alignItems"], [1277, 16, 946, 14], [1277, 18, 946, 16], [1278, 4, 947, 2], [1278, 5, 947, 3], [1279, 4, 948, 2, "errorTitle"], [1279, 14, 948, 12], [1279, 16, 948, 14], [1280, 6, 949, 4, "fontSize"], [1280, 14, 949, 12], [1280, 16, 949, 14], [1280, 18, 949, 16], [1281, 6, 950, 4, "fontWeight"], [1281, 16, 950, 14], [1281, 18, 950, 16], [1281, 23, 950, 21], [1282, 6, 951, 4, "color"], [1282, 11, 951, 9], [1282, 13, 951, 11], [1282, 22, 951, 20], [1283, 6, 952, 4, "marginTop"], [1283, 15, 952, 13], [1283, 17, 952, 15], [1283, 19, 952, 17], [1284, 6, 953, 4, "marginBottom"], [1284, 18, 953, 16], [1284, 20, 953, 18], [1285, 4, 954, 2], [1285, 5, 954, 3], [1286, 4, 955, 2, "errorMessage"], [1286, 16, 955, 14], [1286, 18, 955, 16], [1287, 6, 956, 4, "fontSize"], [1287, 14, 956, 12], [1287, 16, 956, 14], [1287, 18, 956, 16], [1288, 6, 957, 4, "color"], [1288, 11, 957, 9], [1288, 13, 957, 11], [1288, 22, 957, 20], [1289, 6, 958, 4, "textAlign"], [1289, 15, 958, 13], [1289, 17, 958, 15], [1289, 25, 958, 23], [1290, 6, 959, 4, "marginBottom"], [1290, 18, 959, 16], [1290, 20, 959, 18], [1291, 4, 960, 2], [1291, 5, 960, 3], [1292, 4, 961, 2, "primaryButton"], [1292, 17, 961, 15], [1292, 19, 961, 17], [1293, 6, 962, 4, "backgroundColor"], [1293, 21, 962, 19], [1293, 23, 962, 21], [1293, 32, 962, 30], [1294, 6, 963, 4, "paddingHorizontal"], [1294, 23, 963, 21], [1294, 25, 963, 23], [1294, 27, 963, 25], [1295, 6, 964, 4, "paddingVertical"], [1295, 21, 964, 19], [1295, 23, 964, 21], [1295, 25, 964, 23], [1296, 6, 965, 4, "borderRadius"], [1296, 18, 965, 16], [1296, 20, 965, 18], [1296, 21, 965, 19], [1297, 6, 966, 4, "marginTop"], [1297, 15, 966, 13], [1297, 17, 966, 15], [1298, 4, 967, 2], [1298, 5, 967, 3], [1299, 4, 968, 2, "primaryButtonText"], [1299, 21, 968, 19], [1299, 23, 968, 21], [1300, 6, 969, 4, "color"], [1300, 11, 969, 9], [1300, 13, 969, 11], [1300, 19, 969, 17], [1301, 6, 970, 4, "fontSize"], [1301, 14, 970, 12], [1301, 16, 970, 14], [1301, 18, 970, 16], [1302, 6, 971, 4, "fontWeight"], [1302, 16, 971, 14], [1302, 18, 971, 16], [1303, 4, 972, 2], [1303, 5, 972, 3], [1304, 4, 973, 2, "secondaryButton"], [1304, 19, 973, 17], [1304, 21, 973, 19], [1305, 6, 974, 4, "paddingHorizontal"], [1305, 23, 974, 21], [1305, 25, 974, 23], [1305, 27, 974, 25], [1306, 6, 975, 4, "paddingVertical"], [1306, 21, 975, 19], [1306, 23, 975, 21], [1306, 25, 975, 23], [1307, 6, 976, 4, "marginTop"], [1307, 15, 976, 13], [1307, 17, 976, 15], [1308, 4, 977, 2], [1308, 5, 977, 3], [1309, 4, 978, 2, "secondaryButtonText"], [1309, 23, 978, 21], [1309, 25, 978, 23], [1310, 6, 979, 4, "color"], [1310, 11, 979, 9], [1310, 13, 979, 11], [1310, 22, 979, 20], [1311, 6, 980, 4, "fontSize"], [1311, 14, 980, 12], [1311, 16, 980, 14], [1312, 4, 981, 2], [1312, 5, 981, 3], [1313, 4, 982, 2, "permissionContent"], [1313, 21, 982, 19], [1313, 23, 982, 21], [1314, 6, 983, 4, "flex"], [1314, 10, 983, 8], [1314, 12, 983, 10], [1314, 13, 983, 11], [1315, 6, 984, 4, "justifyContent"], [1315, 20, 984, 18], [1315, 22, 984, 20], [1315, 30, 984, 28], [1316, 6, 985, 4, "alignItems"], [1316, 16, 985, 14], [1316, 18, 985, 16], [1316, 26, 985, 24], [1317, 6, 986, 4, "padding"], [1317, 13, 986, 11], [1317, 15, 986, 13], [1318, 4, 987, 2], [1318, 5, 987, 3], [1319, 4, 988, 2, "permissionTitle"], [1319, 19, 988, 17], [1319, 21, 988, 19], [1320, 6, 989, 4, "fontSize"], [1320, 14, 989, 12], [1320, 16, 989, 14], [1320, 18, 989, 16], [1321, 6, 990, 4, "fontWeight"], [1321, 16, 990, 14], [1321, 18, 990, 16], [1321, 23, 990, 21], [1322, 6, 991, 4, "color"], [1322, 11, 991, 9], [1322, 13, 991, 11], [1322, 22, 991, 20], [1323, 6, 992, 4, "marginTop"], [1323, 15, 992, 13], [1323, 17, 992, 15], [1323, 19, 992, 17], [1324, 6, 993, 4, "marginBottom"], [1324, 18, 993, 16], [1324, 20, 993, 18], [1325, 4, 994, 2], [1325, 5, 994, 3], [1326, 4, 995, 2, "permissionDescription"], [1326, 25, 995, 23], [1326, 27, 995, 25], [1327, 6, 996, 4, "fontSize"], [1327, 14, 996, 12], [1327, 16, 996, 14], [1327, 18, 996, 16], [1328, 6, 997, 4, "color"], [1328, 11, 997, 9], [1328, 13, 997, 11], [1328, 22, 997, 20], [1329, 6, 998, 4, "textAlign"], [1329, 15, 998, 13], [1329, 17, 998, 15], [1329, 25, 998, 23], [1330, 6, 999, 4, "marginBottom"], [1330, 18, 999, 16], [1330, 20, 999, 18], [1331, 4, 1000, 2], [1331, 5, 1000, 3], [1332, 4, 1001, 2, "loadingText"], [1332, 15, 1001, 13], [1332, 17, 1001, 15], [1333, 6, 1002, 4, "color"], [1333, 11, 1002, 9], [1333, 13, 1002, 11], [1333, 22, 1002, 20], [1334, 6, 1003, 4, "marginTop"], [1334, 15, 1003, 13], [1334, 17, 1003, 15], [1335, 4, 1004, 2], [1335, 5, 1004, 3], [1336, 4, 1005, 2], [1337, 4, 1006, 2, "blurZone"], [1337, 12, 1006, 10], [1337, 14, 1006, 12], [1338, 6, 1007, 4, "position"], [1338, 14, 1007, 12], [1338, 16, 1007, 14], [1338, 26, 1007, 24], [1339, 6, 1008, 4, "overflow"], [1339, 14, 1008, 12], [1339, 16, 1008, 14], [1340, 4, 1009, 2], [1340, 5, 1009, 3], [1341, 4, 1010, 2, "previewChip"], [1341, 15, 1010, 13], [1341, 17, 1010, 15], [1342, 6, 1011, 4, "position"], [1342, 14, 1011, 12], [1342, 16, 1011, 14], [1342, 26, 1011, 24], [1343, 6, 1012, 4, "top"], [1343, 9, 1012, 7], [1343, 11, 1012, 9], [1343, 12, 1012, 10], [1344, 6, 1013, 4, "right"], [1344, 11, 1013, 9], [1344, 13, 1013, 11], [1344, 14, 1013, 12], [1345, 6, 1014, 4, "backgroundColor"], [1345, 21, 1014, 19], [1345, 23, 1014, 21], [1345, 40, 1014, 38], [1346, 6, 1015, 4, "paddingHorizontal"], [1346, 23, 1015, 21], [1346, 25, 1015, 23], [1346, 27, 1015, 25], [1347, 6, 1016, 4, "paddingVertical"], [1347, 21, 1016, 19], [1347, 23, 1016, 21], [1347, 24, 1016, 22], [1348, 6, 1017, 4, "borderRadius"], [1348, 18, 1017, 16], [1348, 20, 1017, 18], [1349, 4, 1018, 2], [1349, 5, 1018, 3], [1350, 4, 1019, 2, "previewChipText"], [1350, 19, 1019, 17], [1350, 21, 1019, 19], [1351, 6, 1020, 4, "color"], [1351, 11, 1020, 9], [1351, 13, 1020, 11], [1351, 19, 1020, 17], [1352, 6, 1021, 4, "fontSize"], [1352, 14, 1021, 12], [1352, 16, 1021, 14], [1352, 18, 1021, 16], [1353, 6, 1022, 4, "fontWeight"], [1353, 16, 1022, 14], [1353, 18, 1022, 16], [1354, 4, 1023, 2], [1355, 2, 1024, 0], [1355, 3, 1024, 1], [1355, 4, 1024, 2], [1356, 2, 1024, 3], [1356, 6, 1024, 3, "_c"], [1356, 8, 1024, 3], [1357, 2, 1024, 3, "$RefreshReg$"], [1357, 14, 1024, 3], [1357, 15, 1024, 3, "_c"], [1357, 17, 1024, 3], [1358, 0, 1024, 3], [1358, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHoC;mCKE;wBDa;OCI;gDCgC;YDO;8BDa;aCM;6CEc;YFO;oFGsB;UHM;8BIS;SJoD;uDDU;sBMC,wBN;OCC;GLe;6BWG;GX6B;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}