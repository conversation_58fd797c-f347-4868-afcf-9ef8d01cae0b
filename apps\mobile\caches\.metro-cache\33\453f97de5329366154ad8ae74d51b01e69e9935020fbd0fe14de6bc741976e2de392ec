{"dependencies": [], "output": [{"data": {"code": "__d(function(global, require, _importDefaultUnused, _importAllUnused, module, exports, _dependencyMapUnused) {\n  module.exports = {\n  \"name\": \"react-native-reanimated\",\n  \"version\": \"3.17.5\",\n  \"description\": \"More powerful alternative to Animated library for React Native.\",\n  \"scripts\": {\n    \"test\": \"jest\",\n    \"lint\": \"yarn lint:js && yarn lint:plugin && yarn lint:common && yarn lint:android && yarn lint:apple\",\n    \"lint:js\": \"eslint src __tests__ __typetests__ && yarn prettier --check src __tests__ __typetests__\",\n    \"lint:plugin\": \"cd plugin && yarn lint\",\n    \"lint:android\": \"./scripts/validate-android.sh && ./android/gradlew -p android spotlessCheck -q && ./scripts/cpplint.sh android/src && yarn format:android:cpp --dry-run -Werror && yarn lint:cmake\",\n    \"lint:common\": \"./scripts/validate-common.sh && ./scripts/cpplint.sh Common && yarn format:common --dry-run -Werror\",\n    \"lint:apple\": \"./scripts/validate-apple.sh && yarn format:apple --dry-run -Werror\",\n    \"lint:cmake\": \"find ./android -type d \\\\( -name build -o -name .cxx \\\\) -prune -o -type f -name 'CMakeLists.txt' -print | xargs ./scripts/lint-cmake.sh\",\n    \"format\": \"yarn format:js && yarn format:plugin && yarn format:apple && yarn format:android:java && yarn format:android:cpp && yarn format:android:cmake && yarn format:common\",\n    \"format:js\": \"prettier --write --list-different src __tests__ __typetests__\",\n    \"format:plugin\": \"cd plugin && yarn format\",\n    \"format:apple\": \"find apple -iname \\\"*.h\\\" -o -iname \\\"*.m\\\" -o -iname \\\"*.mm\\\" -o -iname \\\"*.cpp\\\" | xargs clang-format -i\",\n    \"format:android:java\": \"node ./scripts/format-java.js\",\n    \"format:android:cpp\": \"find android/src -iname \\\"*.h\\\" -o -iname \\\"*.cpp\\\" | xargs clang-format -i\",\n    \"format:android:cmake\": \"find ./android -type d \\\\( -name build -o -name .cxx \\\\) -prune -o -type f -name 'CMakeLists.txt' -print | xargs ./scripts/format-cmake.sh\",\n    \"format:common\": \"find Common -iname \\\"*.h\\\" -o -iname \\\"*.cpp\\\" | xargs clang-format -i\",\n    \"find-unused-code:js\": \"yarn ts-prune --ignore \\\"index|.web.\\\" --error\",\n    \"type:check\": \"yarn type:check:src && yarn type:check:plugin && ./scripts/test-ts.sh ../../apps/common-app/src/App.tsx __typetests__/common\",\n    \"type:check:src\": \"yarn tsc --noEmit\",\n    \"type:check:plugin\": \"cd plugin && yarn type:check:src\",\n    \"type:check:app\": \"./scripts/test-ts.sh ../../apps/common-app/src/App.tsx\",\n    \"type:check:tests:common\": \"./scripts/test-ts.sh __typetests__/common\",\n    \"build\": \"yarn build:plugin && bob build\",\n    \"build:plugin\": \"cd plugin && yarn build\",\n    \"circular-dependency-check\": \"yarn madge --extensions js,ts,tsx --circular src lib\",\n    \"use-strict-check\": \"node ./scripts/validate-use-strict.js\",\n    \"prepack\": \"cp ../../README.md ./README.md\",\n    \"postpack\": \"rm ./README.md\"\n  },\n  \"main\": \"lib/module/index\",\n  \"module\": \"lib/module/index\",\n  \"react-native\": \"src/index\",\n  \"source\": \"src/index\",\n  \"types\": \"lib/typescript/index.d.ts\",\n  \"files\": [\n    \"Common/\",\n    \"src/\",\n    \"lib/\",\n    \"android/src/main/AndroidManifest.xml\",\n    \"android/src/main/java/\",\n    \"android/build.gradle\",\n    \"android/\",\n    \"apple/\",\n    \"RNReanimated.podspec\",\n    \"scripts/reanimated_utils.rb\",\n    \"mock.js\",\n    \"plugin/index.js\",\n    \"metro-config\",\n    \"!**/__tests__\",\n    \"!**/__fixtures__\",\n    \"!**/__mocks__\",\n    \"!apple/build/\",\n    \"!android/build/\",\n    \"!android/.cxx/\",\n    \"!android/.gradle/\",\n    \"!__snapshots__\",\n    \"!*.test.js\",\n    \"!*.test.js.map\",\n    \"!**/node_modules\"\n  ],\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/software-mansion/react-native-reanimated.git\",\n    \"directory\": \"packages/react-native-reanimated\"\n  },\n  \"author\": {\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Krzysztof Magiera\"\n  },\n  \"license\": \"MIT\",\n  \"readmeFilename\": \"README.md\",\n  \"bugs\": {\n    \"url\": \"https://github.com/software-mansion/react-native-reanimated/issues\"\n  },\n  \"homepage\": \"https://docs.swmansion.com/react-native-reanimated\",\n  \"dependencies\": {\n    \"@babel/plugin-transform-arrow-functions\": \"^7.0.0-0\",\n    \"@babel/plugin-transform-class-properties\": \"^7.0.0-0\",\n    \"@babel/plugin-transform-classes\": \"^7.0.0-0\",\n    \"@babel/plugin-transform-nullish-coalescing-operator\": \"^7.0.0-0\",\n    \"@babel/plugin-transform-optional-chaining\": \"^7.0.0-0\",\n    \"@babel/plugin-transform-shorthand-properties\": \"^7.0.0-0\",\n    \"@babel/plugin-transform-template-literals\": \"^7.0.0-0\",\n    \"@babel/plugin-transform-unicode-regex\": \"^7.0.0-0\",\n    \"@babel/preset-typescript\": \"^7.16.7\",\n    \"convert-source-map\": \"^2.0.0\",\n    \"invariant\": \"^2.2.4\",\n    \"react-native-is-edge-to-edge\": \"1.1.7\"\n  },\n  \"peerDependencies\": {\n    \"@babel/core\": \"^7.0.0-0\",\n    \"react\": \"*\",\n    \"react-native\": \"*\"\n  },\n  \"devDependencies\": {\n    \"@babel/cli\": \"^7.20.0\",\n    \"@babel/core\": \"^7.25.2\",\n    \"@babel/preset-env\": \"^7.25.3\",\n    \"@babel/types\": \"^7.20.0\",\n    \"@react-native/babel-preset\": \"0.79.0-rc.4\",\n    \"@react-native/eslint-config\": \"0.79.0-rc.4\",\n    \"@react-native/metro-config\": \"0.79.0-rc.4\",\n    \"@react-native/typescript-config\": \"0.79.0-rc.4\",\n    \"@testing-library/jest-native\": \"^4.0.4\",\n    \"@testing-library/react-hooks\": \"^8.0.0\",\n    \"@testing-library/react-native\": \"^13.0.1\",\n    \"@types/babel__core\": \"^7.20.0\",\n    \"@types/babel__generator\": \"^7.6.4\",\n    \"@types/babel__traverse\": \"^7.14.2\",\n    \"@types/convert-source-map\": \"^2.0.0\",\n    \"@types/invariant\": \"^2.2.35\",\n    \"@types/jest\": \"^29.5.13\",\n    \"@types/node\": \"^18.0.0\",\n    \"@types/react\": \"^19.0.10\",\n    \"@types/react-test-renderer\": \"^19.0.0\",\n    \"@typescript-eslint/eslint-plugin\": \"^6.19.0\",\n    \"@typescript-eslint/parser\": \"^6.19.0\",\n    \"@typescript-eslint/rule-tester\": \"^6.21.0\",\n    \"axios\": \"^1.7.4\",\n    \"babel-eslint\": \"^10.1.0\",\n    \"babel-plugin-module-resolver\": \"^5.0.0\",\n    \"clang-format\": \"^1.6.0\",\n    \"code-tag\": \"^1.1.0\",\n    \"cspell\": \"^8.8.0\",\n    \"eslint\": \"^8.57.0\",\n    \"eslint-config-prettier\": \"^8.3.0\",\n    \"eslint-config-standard\": \"^17.1.0\",\n    \"eslint-import-resolver-babel-module\": \"^5.3.1\",\n    \"eslint-plugin-import\": \"^2.25.4\",\n    \"eslint-plugin-jest\": \"^27.2.1\",\n    \"eslint-plugin-n\": \"^16.4.0\",\n    \"eslint-plugin-no-inline-styles\": \"^1.0.5\",\n    \"eslint-plugin-promise\": \"^6.0.0\",\n    \"eslint-plugin-react-hooks\": \"^4.6.0\",\n    \"eslint-plugin-reanimated\": \"workspace:*\",\n    \"eslint-plugin-standard\": \"^5.0.0\",\n    \"eslint-plugin-tsdoc\": \"^0.2.17\",\n    \"jest\": \"^29.0.0\",\n    \"madge\": \"^5.0.1\",\n    \"prettier\": \"^3.3.3\",\n    \"react\": \"19.0.0\",\n    \"react-native\": \"0.79.0-rc.4\",\n    \"react-native-builder-bob\": \"patch:react-native-builder-bob@npm%3A0.33.1#~/.yarn/patches/react-native-builder-bob-npm-0.33.1-383d9e23a5.patch\",\n    \"react-native-gesture-handler\": \"2.25.0\",\n    \"react-native-web\": \"0.19.13\",\n    \"react-test-renderer\": \"19.0.0\",\n    \"shelljs\": \"^0.8.5\",\n    \"ts-prune\": \"^0.10.3\",\n    \"typescript\": \"~5.3.0\"\n  },\n  \"react-native-builder-bob\": {\n    \"source\": \"src\",\n    \"output\": \"lib\",\n    \"targets\": [\n      [\n        \"module\",\n        {\n          \"esm\": true,\n          \"jsxRuntime\": \"classic\"\n        }\n      ],\n      \"typescript\"\n    ]\n  },\n  \"codegenConfig\": {\n    \"name\": \"rnreanimated\",\n    \"type\": \"modules\",\n    \"jsSrcsDir\": \"./src/specs\",\n    \"android\": {\n      \"javaPackageName\": \"com.swmansion.reanimated\"\n    }\n  },\n  \"sideEffects\": [\n    \"./src/layoutReanimation/animationsManager.ts\",\n    \"./lib/module/layoutReanimation/animationsManager.js\",\n    \"./src/core.ts\",\n    \"./lib/module/core.js\",\n    \"./src/initializers.ts\",\n    \"./lib/module/initializers.js\",\n    \"./src/index.ts\",\n    \"./lib/module/index.js\"\n  ]\n}\n;\n});", "lineCount": 191, "map": [[191, 3]], "functionMap": null}, "type": "js/module"}]}