{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getFileExtension = getFileExtension;\n  exports.getFilename = getFilename;\n  exports.getManifestBaseUrl = getManifestBaseUrl;\n  function getFilename(url) {\n    const {\n      pathname,\n      searchParams\n    } = new URL(url, 'https://e');\n    // When attached to a dev server, we use `unstable_path` to represent the file path. This ensures\n    // the file name is not canonicalized by the browser.\n    // NOTE(EvanBacon): This is technically not tied to `__DEV__` as it's possible to use this while bundling in production\n    // mode.\n    if (__DEV__) {\n      if (searchParams.has('unstable_path')) {\n        const encodedFilePath = decodeURIComponent(searchParams.get('unstable_path'));\n        return getBasename(encodedFilePath);\n      }\n    }\n    return getBasename(pathname);\n  }\n  function getBasename(pathname) {\n    return pathname.substring(pathname.lastIndexOf('/') + 1);\n  }\n  function getFileExtension(url) {\n    const filename = getFilename(url);\n    const dotIndex = filename.lastIndexOf('.');\n    // Ignore leading dots for hidden files\n    return dotIndex > 0 ? filename.substring(dotIndex) : '';\n  }\n  /**\n   * Returns the base URL from a manifest's URL. For example, given a manifest hosted at\n   * https://example.com/app/manifest.json, the base URL would be https://example.com/app/. Query\n   * parameters and fragments also are removed.\n   *\n   * For an Expo-hosted project with a manifest hosted at https://exp.host/@user/project/index.exp, the\n   * base URL would be https://exp.host/@user/project.\n   *\n   * We also normalize the \"exp\" protocol to \"http\" to handle internal URLs with the Expo schemes used\n   * to tell the OS to open the URLs in the the Expo client.\n   */\n  function getManifestBaseUrl(manifestUrl) {\n    const urlObject = new URL(manifestUrl);\n    let nextProtocol = urlObject.protocol;\n    // Change the scheme to http(s) if it is exp(s)\n    if (nextProtocol === 'exp:') {\n      nextProtocol = 'http:';\n    } else if (nextProtocol === 'exps:') {\n      nextProtocol = 'https:';\n    }\n    urlObject.protocol = nextProtocol;\n    // Trim filename, query parameters, and fragment, if any\n    const directory = urlObject.pathname.substring(0, urlObject.pathname.lastIndexOf('/') + 1);\n    urlObject.pathname = directory;\n    urlObject.search = '';\n    urlObject.hash = '';\n    // The URL spec doesn't allow for changing the protocol to `http` or `https`\n    // without a port set so instead, we'll just swap the protocol manually.\n    return urlObject.protocol !== nextProtocol ? urlObject.href.replace(urlObject.protocol, nextProtocol) : urlObject.href;\n  }\n});", "lineCount": 64, "map": [[8, 2, 1, 7], [8, 11, 1, 16, "getFilename"], [8, 22, 1, 27, "getFilename"], [8, 23, 1, 28, "url"], [8, 26, 1, 31], [8, 28, 1, 33], [9, 4, 2, 4], [9, 10, 2, 10], [10, 6, 2, 12, "pathname"], [10, 14, 2, 20], [11, 6, 2, 22, "searchParams"], [12, 4, 2, 35], [12, 5, 2, 36], [12, 8, 2, 39], [12, 12, 2, 43, "URL"], [12, 15, 2, 46], [12, 16, 2, 47, "url"], [12, 19, 2, 50], [12, 21, 2, 52], [12, 32, 2, 63], [12, 33, 2, 64], [13, 4, 3, 4], [14, 4, 4, 4], [15, 4, 5, 4], [16, 4, 6, 4], [17, 4, 7, 4], [17, 8, 7, 8, "__DEV__"], [17, 15, 7, 15], [17, 17, 7, 17], [18, 6, 8, 8], [18, 10, 8, 12, "searchParams"], [18, 22, 8, 24], [18, 23, 8, 25, "has"], [18, 26, 8, 28], [18, 27, 8, 29], [18, 42, 8, 44], [18, 43, 8, 45], [18, 45, 8, 47], [19, 8, 9, 12], [19, 14, 9, 18, "encodedFilePath"], [19, 29, 9, 33], [19, 32, 9, 36, "decodeURIComponent"], [19, 50, 9, 54], [19, 51, 9, 55, "searchParams"], [19, 63, 9, 67], [19, 64, 9, 68, "get"], [19, 67, 9, 71], [19, 68, 9, 72], [19, 83, 9, 87], [19, 84, 9, 88], [19, 85, 9, 89], [20, 8, 10, 12], [20, 15, 10, 19, "getBasename"], [20, 26, 10, 30], [20, 27, 10, 31, "encodedFilePath"], [20, 42, 10, 46], [20, 43, 10, 47], [21, 6, 11, 8], [22, 4, 12, 4], [23, 4, 13, 4], [23, 11, 13, 11, "getBasename"], [23, 22, 13, 22], [23, 23, 13, 23, "pathname"], [23, 31, 13, 31], [23, 32, 13, 32], [24, 2, 14, 0], [25, 2, 15, 0], [25, 11, 15, 9, "getBasename"], [25, 22, 15, 20, "getBasename"], [25, 23, 15, 21, "pathname"], [25, 31, 15, 29], [25, 33, 15, 31], [26, 4, 16, 4], [26, 11, 16, 11, "pathname"], [26, 19, 16, 19], [26, 20, 16, 20, "substring"], [26, 29, 16, 29], [26, 30, 16, 30, "pathname"], [26, 38, 16, 38], [26, 39, 16, 39, "lastIndexOf"], [26, 50, 16, 50], [26, 51, 16, 51], [26, 54, 16, 54], [26, 55, 16, 55], [26, 58, 16, 58], [26, 59, 16, 59], [26, 60, 16, 60], [27, 2, 17, 0], [28, 2, 18, 7], [28, 11, 18, 16, "getFileExtension"], [28, 27, 18, 32, "getFileExtension"], [28, 28, 18, 33, "url"], [28, 31, 18, 36], [28, 33, 18, 38], [29, 4, 19, 4], [29, 10, 19, 10, "filename"], [29, 18, 19, 18], [29, 21, 19, 21, "getFilename"], [29, 32, 19, 32], [29, 33, 19, 33, "url"], [29, 36, 19, 36], [29, 37, 19, 37], [30, 4, 20, 4], [30, 10, 20, 10, "dotIndex"], [30, 18, 20, 18], [30, 21, 20, 21, "filename"], [30, 29, 20, 29], [30, 30, 20, 30, "lastIndexOf"], [30, 41, 20, 41], [30, 42, 20, 42], [30, 45, 20, 45], [30, 46, 20, 46], [31, 4, 21, 4], [32, 4, 22, 4], [32, 11, 22, 11, "dotIndex"], [32, 19, 22, 19], [32, 22, 22, 22], [32, 23, 22, 23], [32, 26, 22, 26, "filename"], [32, 34, 22, 34], [32, 35, 22, 35, "substring"], [32, 44, 22, 44], [32, 45, 22, 45, "dotIndex"], [32, 53, 22, 53], [32, 54, 22, 54], [32, 57, 22, 57], [32, 59, 22, 59], [33, 2, 23, 0], [34, 2, 24, 0], [35, 0, 25, 0], [36, 0, 26, 0], [37, 0, 27, 0], [38, 0, 28, 0], [39, 0, 29, 0], [40, 0, 30, 0], [41, 0, 31, 0], [42, 0, 32, 0], [43, 0, 33, 0], [44, 0, 34, 0], [45, 2, 35, 7], [45, 11, 35, 16, "getManifestBaseUrl"], [45, 29, 35, 34, "getManifestBaseUrl"], [45, 30, 35, 35, "manifestUrl"], [45, 41, 35, 46], [45, 43, 35, 48], [46, 4, 36, 4], [46, 10, 36, 10, "urlObject"], [46, 19, 36, 19], [46, 22, 36, 22], [46, 26, 36, 26, "URL"], [46, 29, 36, 29], [46, 30, 36, 30, "manifestUrl"], [46, 41, 36, 41], [46, 42, 36, 42], [47, 4, 37, 4], [47, 8, 37, 8, "nextProtocol"], [47, 20, 37, 20], [47, 23, 37, 23, "urlObject"], [47, 32, 37, 32], [47, 33, 37, 33, "protocol"], [47, 41, 37, 41], [48, 4, 38, 4], [49, 4, 39, 4], [49, 8, 39, 8, "nextProtocol"], [49, 20, 39, 20], [49, 25, 39, 25], [49, 31, 39, 31], [49, 33, 39, 33], [50, 6, 40, 8, "nextProtocol"], [50, 18, 40, 20], [50, 21, 40, 23], [50, 28, 40, 30], [51, 4, 41, 4], [51, 5, 41, 5], [51, 11, 42, 9], [51, 15, 42, 13, "nextProtocol"], [51, 27, 42, 25], [51, 32, 42, 30], [51, 39, 42, 37], [51, 41, 42, 39], [52, 6, 43, 8, "nextProtocol"], [52, 18, 43, 20], [52, 21, 43, 23], [52, 29, 43, 31], [53, 4, 44, 4], [54, 4, 45, 4, "urlObject"], [54, 13, 45, 13], [54, 14, 45, 14, "protocol"], [54, 22, 45, 22], [54, 25, 45, 25, "nextProtocol"], [54, 37, 45, 37], [55, 4, 46, 4], [56, 4, 47, 4], [56, 10, 47, 10, "directory"], [56, 19, 47, 19], [56, 22, 47, 22, "urlObject"], [56, 31, 47, 31], [56, 32, 47, 32, "pathname"], [56, 40, 47, 40], [56, 41, 47, 41, "substring"], [56, 50, 47, 50], [56, 51, 47, 51], [56, 52, 47, 52], [56, 54, 47, 54, "urlObject"], [56, 63, 47, 63], [56, 64, 47, 64, "pathname"], [56, 72, 47, 72], [56, 73, 47, 73, "lastIndexOf"], [56, 84, 47, 84], [56, 85, 47, 85], [56, 88, 47, 88], [56, 89, 47, 89], [56, 92, 47, 92], [56, 93, 47, 93], [56, 94, 47, 94], [57, 4, 48, 4, "urlObject"], [57, 13, 48, 13], [57, 14, 48, 14, "pathname"], [57, 22, 48, 22], [57, 25, 48, 25, "directory"], [57, 34, 48, 34], [58, 4, 49, 4, "urlObject"], [58, 13, 49, 13], [58, 14, 49, 14, "search"], [58, 20, 49, 20], [58, 23, 49, 23], [58, 25, 49, 25], [59, 4, 50, 4, "urlObject"], [59, 13, 50, 13], [59, 14, 50, 14, "hash"], [59, 18, 50, 18], [59, 21, 50, 21], [59, 23, 50, 23], [60, 4, 51, 4], [61, 4, 52, 4], [62, 4, 53, 4], [62, 11, 53, 11, "urlObject"], [62, 20, 53, 20], [62, 21, 53, 21, "protocol"], [62, 29, 53, 29], [62, 34, 53, 34, "nextProtocol"], [62, 46, 53, 46], [62, 49, 54, 10, "urlObject"], [62, 58, 54, 19], [62, 59, 54, 20, "href"], [62, 63, 54, 24], [62, 64, 54, 25, "replace"], [62, 71, 54, 32], [62, 72, 54, 33, "urlObject"], [62, 81, 54, 42], [62, 82, 54, 43, "protocol"], [62, 90, 54, 51], [62, 92, 54, 53, "nextProtocol"], [62, 104, 54, 65], [62, 105, 54, 66], [62, 108, 55, 10, "urlObject"], [62, 117, 55, 19], [62, 118, 55, 20, "href"], [62, 122, 55, 24], [63, 2, 56, 0], [64, 0, 56, 1], [64, 3]], "functionMap": {"names": ["<global>", "getFilename", "getBasename", "getFileExtension", "getManifestBaseUrl"], "mappings": "AAA,OC;CDa;AEC;CFE;OGC;CHK;OIY;CJqB"}}, "type": "js/module"}]}