{"dependencies": [{"name": "./animationParser.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 73, "index": 88}}], "key": "G6/XgN9fzw3pSxSZt9dSVEHCFGo=", "exportNames": ["*"]}}, {"name": "./config.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 89}, "end": {"line": 4, "column": 61, "index": 150}}], "key": "bUPXFgGH+XQHosI76NH2QbmMaAI=", "exportNames": ["*"]}}, {"name": "./domUtils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 151}, "end": {"line": 5, "column": 51, "index": 202}}], "key": "0duQh0EQU3LifQ3CyaK4iQBtJH0=", "exportNames": ["*"]}}, {"name": "./transition/Curved.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 203}, "end": {"line": 6, "column": 62, "index": 265}}], "key": "RSHjiT09o1SpvAI4cU3nqoL+Y4U=", "exportNames": ["*"]}}, {"name": "./transition/EntryExit.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 266}, "end": {"line": 7, "column": 68, "index": 334}}], "key": "UhWD0Z1miPlKgl4qrRFfzBmIiLE=", "exportNames": ["*"]}}, {"name": "./transition/Fading.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 335}, "end": {"line": 8, "column": 62, "index": 397}}], "key": "H8+wAYUjOISf3yNNGraYpk70y3Y=", "exportNames": ["*"]}}, {"name": "./transition/Jumping.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 398}, "end": {"line": 9, "column": 64, "index": 462}}], "key": "GkTynPn2aPHoQXwyhBXcJn/nwsg=", "exportNames": ["*"]}}, {"name": "./transition/Linear.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 463}, "end": {"line": 10, "column": 62, "index": 525}}], "key": "ZW4kj1upU3vhm7SQZhV15GEA/P0=", "exportNames": ["*"]}}, {"name": "./transition/Sequenced.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 526}, "end": {"line": 11, "column": 68, "index": 594}}], "key": "HO2wj8gO+GXiwHSdODqGmpYQlss=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TransitionGenerator = TransitionGenerator;\n  exports.createAnimationWithInitialValues = createAnimationWithInitialValues;\n  exports.createCustomKeyFrameAnimation = createCustomKeyFrameAnimation;\n  var _animationParser = require(_dependencyMap[0], \"./animationParser.js\");\n  var _config = require(_dependencyMap[1], \"./config.js\");\n  var _domUtils = require(_dependencyMap[2], \"./domUtils.js\");\n  var _CurvedWeb = require(_dependencyMap[3], \"./transition/Curved.web.js\");\n  var _EntryExitWeb = require(_dependencyMap[4], \"./transition/EntryExit.web.js\");\n  var _FadingWeb = require(_dependencyMap[5], \"./transition/Fading.web.js\");\n  var _JumpingWeb = require(_dependencyMap[6], \"./transition/Jumping.web.js\");\n  var _LinearWeb = require(_dependencyMap[7], \"./transition/Linear.web.js\");\n  var _SequencedWeb = require(_dependencyMap[8], \"./transition/Sequenced.web.js\");\n  // Translate values are passed as numbers. However, if `translate` property receives number, it will not automatically\n  // convert it to `px`. Therefore if we want to keep transform we have to add 'px' suffix to each of translate values\n  // that are present inside transform.\n  //\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  function addPxToTransform(transform) {\n    // @ts-ignore `existingTransform` cannot be string because in that case\n    // we throw error in `extractTransformFromStyle`\n    const newTransform = transform.map(transformProp => {\n      const newTransformProp = {};\n      for (const [key, value] of Object.entries(transformProp)) {\n        if ((key.includes('translate') || key.includes('perspective')) && typeof value === 'number') {\n          // @ts-ignore After many trials we decided to ignore this error - it says that we cannot use 'key' to index this object.\n          // Sadly it doesn't go away after using cast `key as keyof TransformProperties`.\n          newTransformProp[key] = `${value}px`;\n        } else {\n          // @ts-ignore same as above.\n          newTransformProp[key] = value;\n        }\n      }\n      return newTransformProp;\n    });\n    return newTransform;\n  }\n  function createCustomKeyFrameAnimation(keyframeDefinitions) {\n    for (const value of Object.values(keyframeDefinitions)) {\n      if (value.transform) {\n        value.transform = addPxToTransform(value.transform);\n      }\n    }\n    const animationData = {\n      name: '',\n      style: keyframeDefinitions,\n      duration: -1\n    };\n    animationData.name = generateNextCustomKeyframeName();\n    const parsedKeyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(animationData);\n    (0, _domUtils.insertWebAnimation)(animationData.name, parsedKeyframe);\n    return animationData.name;\n  }\n  function createAnimationWithInitialValues(animationName, initialValues) {\n    const animationStyle = structuredClone(_config.AnimationsData[animationName].style);\n    const firstAnimationStep = animationStyle['0'];\n    const {\n      transform,\n      ...rest\n    } = initialValues;\n    if (transform) {\n      const transformWithPx = addPxToTransform(transform);\n      // If there was no predefined transform, we can simply assign transform from `initialValues`.\n      if (!firstAnimationStep.transform) {\n        firstAnimationStep.transform = transformWithPx;\n      } else {\n        // Othwerwise we have to merge predefined transform with the one provided in `initialValues`.\n        // To do that, we create `Map` that will contain final transform.\n        const transformStyle = new Map();\n\n        // First we assign all of the predefined rules\n        for (const rule of firstAnimationStep.transform) {\n          // In most cases there will be just one iteration\n          for (const [property, value] of Object.entries(rule)) {\n            transformStyle.set(property, value);\n          }\n        }\n\n        // Then we either add new rule, or override one that already exists.\n        for (const rule of transformWithPx) {\n          for (const [property, value] of Object.entries(rule)) {\n            transformStyle.set(property, value);\n          }\n        }\n\n        // Finally, we convert `Map` with final transform back into array of objects.\n        firstAnimationStep.transform = Array.from(transformStyle, ([property, value]) => ({\n          [property]: value\n        }));\n      }\n    }\n    animationStyle['0'] = {\n      ...animationStyle['0'],\n      ...rest\n    };\n\n    // TODO: Maybe we can extract the logic below into separate function\n    const keyframeName = generateNextCustomKeyframeName();\n    const animationObject = {\n      name: keyframeName,\n      style: animationStyle,\n      duration: _config.AnimationsData[animationName].duration\n    };\n    const keyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(animationObject);\n    (0, _domUtils.insertWebAnimation)(keyframeName, keyframe);\n    return keyframeName;\n  }\n  let customKeyframeCounter = 0;\n  function generateNextCustomKeyframeName() {\n    return `REA${customKeyframeCounter++}`;\n  }\n\n  /**\n   * Creates transition of given type, appends it to stylesheet and returns\n   * keyframe name.\n   *\n   * @param transitionType - Type of transition (e.g. LINEAR).\n   * @param transitionData - Object containing data for transforms (translateX,\n   *   scaleX,...).\n   * @returns Keyframe name that represents transition.\n   */\n  function TransitionGenerator(transitionType, transitionData) {\n    const transitionKeyframeName = generateNextCustomKeyframeName();\n    let dummyTransitionKeyframeName;\n    let transitionObject;\n    switch (transitionType) {\n      case _config.TransitionType.LINEAR:\n        transitionObject = (0, _LinearWeb.LinearTransition)(transitionKeyframeName, transitionData);\n        break;\n      case _config.TransitionType.SEQUENCED:\n        transitionObject = (0, _SequencedWeb.SequencedTransition)(transitionKeyframeName, transitionData);\n        break;\n      case _config.TransitionType.FADING:\n        transitionObject = (0, _FadingWeb.FadingTransition)(transitionKeyframeName, transitionData);\n        break;\n      case _config.TransitionType.JUMPING:\n        transitionObject = (0, _JumpingWeb.JumpingTransition)(transitionKeyframeName, transitionData);\n        break;\n\n      // Here code block with {} is necessary because of eslint\n      case _config.TransitionType.CURVED:\n        {\n          dummyTransitionKeyframeName = generateNextCustomKeyframeName();\n          const {\n            firstKeyframeObj,\n            secondKeyframeObj\n          } = (0, _CurvedWeb.CurvedTransition)(transitionKeyframeName, dummyTransitionKeyframeName, transitionData);\n          transitionObject = firstKeyframeObj;\n          const dummyKeyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(secondKeyframeObj);\n          (0, _domUtils.insertWebAnimation)(dummyTransitionKeyframeName, dummyKeyframe);\n          break;\n        }\n      case _config.TransitionType.ENTRY_EXIT:\n        transitionObject = (0, _EntryExitWeb.EntryExitTransition)(transitionKeyframeName, transitionData);\n        break;\n    }\n    const transitionKeyframe = (0, _animationParser.convertAnimationObjectToKeyframes)(transitionObject);\n    (0, _domUtils.insertWebAnimation)(transitionKeyframeName, transitionKeyframe);\n    return {\n      transitionKeyframeName,\n      dummyTransitionKeyframeName\n    };\n  }\n});", "lineCount": 169, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "TransitionGenerator"], [7, 29, 1, 13], [7, 32, 1, 13, "TransitionGenerator"], [7, 51, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "createAnimationWithInitialValues"], [8, 42, 1, 13], [8, 45, 1, 13, "createAnimationWithInitialValues"], [8, 77, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "createCustomKeyFrameAnimation"], [9, 39, 1, 13], [9, 42, 1, 13, "createCustomKeyFrameAnimation"], [9, 71, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_animation<PERSON><PERSON>er"], [10, 22, 3, 0], [10, 25, 3, 0, "require"], [10, 32, 3, 0], [10, 33, 3, 0, "_dependencyMap"], [10, 47, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_config"], [11, 13, 4, 0], [11, 16, 4, 0, "require"], [11, 23, 4, 0], [11, 24, 4, 0, "_dependencyMap"], [11, 38, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_domUtils"], [12, 15, 5, 0], [12, 18, 5, 0, "require"], [12, 25, 5, 0], [12, 26, 5, 0, "_dependencyMap"], [12, 40, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_<PERSON>ur<PERSON><PERSON><PERSON>"], [13, 16, 6, 0], [13, 19, 6, 0, "require"], [13, 26, 6, 0], [13, 27, 6, 0, "_dependencyMap"], [13, 41, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_EntryExitWeb"], [14, 19, 7, 0], [14, 22, 7, 0, "require"], [14, 29, 7, 0], [14, 30, 7, 0, "_dependencyMap"], [14, 44, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_FadingWeb"], [15, 16, 8, 0], [15, 19, 8, 0, "require"], [15, 26, 8, 0], [15, 27, 8, 0, "_dependencyMap"], [15, 41, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_JumpingWeb"], [16, 17, 9, 0], [16, 20, 9, 0, "require"], [16, 27, 9, 0], [16, 28, 9, 0, "_dependencyMap"], [16, 42, 9, 0], [17, 2, 10, 0], [17, 6, 10, 0, "_LinearWeb"], [17, 16, 10, 0], [17, 19, 10, 0, "require"], [17, 26, 10, 0], [17, 27, 10, 0, "_dependencyMap"], [17, 41, 10, 0], [18, 2, 11, 0], [18, 6, 11, 0, "_SequencedWeb"], [18, 19, 11, 0], [18, 22, 11, 0, "require"], [18, 29, 11, 0], [18, 30, 11, 0, "_dependencyMap"], [18, 44, 11, 0], [19, 2, 12, 0], [20, 2, 13, 0], [21, 2, 14, 0], [22, 2, 15, 0], [23, 2, 16, 0], [24, 2, 17, 0], [24, 11, 17, 9, "addPxToTransform"], [24, 27, 17, 25, "addPxToTransform"], [24, 28, 17, 26, "transform"], [24, 37, 17, 35], [24, 39, 17, 37], [25, 4, 18, 2], [26, 4, 19, 2], [27, 4, 20, 2], [27, 10, 20, 8, "newTransform"], [27, 22, 20, 20], [27, 25, 20, 23, "transform"], [27, 34, 20, 32], [27, 35, 20, 33, "map"], [27, 38, 20, 36], [27, 39, 20, 37, "transformProp"], [27, 52, 20, 50], [27, 56, 20, 54], [28, 6, 21, 4], [28, 12, 21, 10, "newTransformProp"], [28, 28, 21, 26], [28, 31, 21, 29], [28, 32, 21, 30], [28, 33, 21, 31], [29, 6, 22, 4], [29, 11, 22, 9], [29, 17, 22, 15], [29, 18, 22, 16, "key"], [29, 21, 22, 19], [29, 23, 22, 21, "value"], [29, 28, 22, 26], [29, 29, 22, 27], [29, 33, 22, 31, "Object"], [29, 39, 22, 37], [29, 40, 22, 38, "entries"], [29, 47, 22, 45], [29, 48, 22, 46, "transformProp"], [29, 61, 22, 59], [29, 62, 22, 60], [29, 64, 22, 62], [30, 8, 23, 6], [30, 12, 23, 10], [30, 13, 23, 11, "key"], [30, 16, 23, 14], [30, 17, 23, 15, "includes"], [30, 25, 23, 23], [30, 26, 23, 24], [30, 37, 23, 35], [30, 38, 23, 36], [30, 42, 23, 40, "key"], [30, 45, 23, 43], [30, 46, 23, 44, "includes"], [30, 54, 23, 52], [30, 55, 23, 53], [30, 68, 23, 66], [30, 69, 23, 67], [30, 74, 23, 72], [30, 81, 23, 79, "value"], [30, 86, 23, 84], [30, 91, 23, 89], [30, 99, 23, 97], [30, 101, 23, 99], [31, 10, 24, 8], [32, 10, 25, 8], [33, 10, 26, 8, "newTransformProp"], [33, 26, 26, 24], [33, 27, 26, 25, "key"], [33, 30, 26, 28], [33, 31, 26, 29], [33, 34, 26, 32], [33, 37, 26, 35, "value"], [33, 42, 26, 40], [33, 46, 26, 44], [34, 8, 27, 6], [34, 9, 27, 7], [34, 15, 27, 13], [35, 10, 28, 8], [36, 10, 29, 8, "newTransformProp"], [36, 26, 29, 24], [36, 27, 29, 25, "key"], [36, 30, 29, 28], [36, 31, 29, 29], [36, 34, 29, 32, "value"], [36, 39, 29, 37], [37, 8, 30, 6], [38, 6, 31, 4], [39, 6, 32, 4], [39, 13, 32, 11, "newTransformProp"], [39, 29, 32, 27], [40, 4, 33, 2], [40, 5, 33, 3], [40, 6, 33, 4], [41, 4, 34, 2], [41, 11, 34, 9, "newTransform"], [41, 23, 34, 21], [42, 2, 35, 0], [43, 2, 36, 7], [43, 11, 36, 16, "createCustomKeyFrameAnimation"], [43, 40, 36, 45, "createCustomKeyFrameAnimation"], [43, 41, 36, 46, "keyframeDefinitions"], [43, 60, 36, 65], [43, 62, 36, 67], [44, 4, 37, 2], [44, 9, 37, 7], [44, 15, 37, 13, "value"], [44, 20, 37, 18], [44, 24, 37, 22, "Object"], [44, 30, 37, 28], [44, 31, 37, 29, "values"], [44, 37, 37, 35], [44, 38, 37, 36, "keyframeDefinitions"], [44, 57, 37, 55], [44, 58, 37, 56], [44, 60, 37, 58], [45, 6, 38, 4], [45, 10, 38, 8, "value"], [45, 15, 38, 13], [45, 16, 38, 14, "transform"], [45, 25, 38, 23], [45, 27, 38, 25], [46, 8, 39, 6, "value"], [46, 13, 39, 11], [46, 14, 39, 12, "transform"], [46, 23, 39, 21], [46, 26, 39, 24, "addPxToTransform"], [46, 42, 39, 40], [46, 43, 39, 41, "value"], [46, 48, 39, 46], [46, 49, 39, 47, "transform"], [46, 58, 39, 56], [46, 59, 39, 57], [47, 6, 40, 4], [48, 4, 41, 2], [49, 4, 42, 2], [49, 10, 42, 8, "animationData"], [49, 23, 42, 21], [49, 26, 42, 24], [50, 6, 43, 4, "name"], [50, 10, 43, 8], [50, 12, 43, 10], [50, 14, 43, 12], [51, 6, 44, 4, "style"], [51, 11, 44, 9], [51, 13, 44, 11, "keyframeDefinitions"], [51, 32, 44, 30], [52, 6, 45, 4, "duration"], [52, 14, 45, 12], [52, 16, 45, 14], [52, 17, 45, 15], [53, 4, 46, 2], [53, 5, 46, 3], [54, 4, 47, 2, "animationData"], [54, 17, 47, 15], [54, 18, 47, 16, "name"], [54, 22, 47, 20], [54, 25, 47, 23, "generateNextCustomKeyframeName"], [54, 55, 47, 53], [54, 56, 47, 54], [54, 57, 47, 55], [55, 4, 48, 2], [55, 10, 48, 8, "parsedKeyframe"], [55, 24, 48, 22], [55, 27, 48, 25], [55, 31, 48, 25, "convertAnimationObjectToKeyframes"], [55, 81, 48, 58], [55, 83, 48, 59, "animationData"], [55, 96, 48, 72], [55, 97, 48, 73], [56, 4, 49, 2], [56, 8, 49, 2, "insertWebAnimation"], [56, 36, 49, 20], [56, 38, 49, 21, "animationData"], [56, 51, 49, 34], [56, 52, 49, 35, "name"], [56, 56, 49, 39], [56, 58, 49, 41, "parsedKeyframe"], [56, 72, 49, 55], [56, 73, 49, 56], [57, 4, 50, 2], [57, 11, 50, 9, "animationData"], [57, 24, 50, 22], [57, 25, 50, 23, "name"], [57, 29, 50, 27], [58, 2, 51, 0], [59, 2, 52, 7], [59, 11, 52, 16, "createAnimationWithInitialValues"], [59, 43, 52, 48, "createAnimationWithInitialValues"], [59, 44, 52, 49, "animationName"], [59, 57, 52, 62], [59, 59, 52, 64, "initialValues"], [59, 72, 52, 77], [59, 74, 52, 79], [60, 4, 53, 2], [60, 10, 53, 8, "animationStyle"], [60, 24, 53, 22], [60, 27, 53, 25, "structuredClone"], [60, 42, 53, 40], [60, 43, 53, 41, "AnimationsData"], [60, 65, 53, 55], [60, 66, 53, 56, "animationName"], [60, 79, 53, 69], [60, 80, 53, 70], [60, 81, 53, 71, "style"], [60, 86, 53, 76], [60, 87, 53, 77], [61, 4, 54, 2], [61, 10, 54, 8, "firstAnimationStep"], [61, 28, 54, 26], [61, 31, 54, 29, "animationStyle"], [61, 45, 54, 43], [61, 46, 54, 44], [61, 49, 54, 47], [61, 50, 54, 48], [62, 4, 55, 2], [62, 10, 55, 8], [63, 6, 56, 4, "transform"], [63, 15, 56, 13], [64, 6, 57, 4], [64, 9, 57, 7, "rest"], [65, 4, 58, 2], [65, 5, 58, 3], [65, 8, 58, 6, "initialValues"], [65, 21, 58, 19], [66, 4, 59, 2], [66, 8, 59, 6, "transform"], [66, 17, 59, 15], [66, 19, 59, 17], [67, 6, 60, 4], [67, 12, 60, 10, "transformWithPx"], [67, 27, 60, 25], [67, 30, 60, 28, "addPxToTransform"], [67, 46, 60, 44], [67, 47, 60, 45, "transform"], [67, 56, 60, 54], [67, 57, 60, 55], [68, 6, 61, 4], [69, 6, 62, 4], [69, 10, 62, 8], [69, 11, 62, 9, "firstAnimationStep"], [69, 29, 62, 27], [69, 30, 62, 28, "transform"], [69, 39, 62, 37], [69, 41, 62, 39], [70, 8, 63, 6, "firstAnimationStep"], [70, 26, 63, 24], [70, 27, 63, 25, "transform"], [70, 36, 63, 34], [70, 39, 63, 37, "transformWithPx"], [70, 54, 63, 52], [71, 6, 64, 4], [71, 7, 64, 5], [71, 13, 64, 11], [72, 8, 65, 6], [73, 8, 66, 6], [74, 8, 67, 6], [74, 14, 67, 12, "transformStyle"], [74, 28, 67, 26], [74, 31, 67, 29], [74, 35, 67, 33, "Map"], [74, 38, 67, 36], [74, 39, 67, 37], [74, 40, 67, 38], [76, 8, 69, 6], [77, 8, 70, 6], [77, 13, 70, 11], [77, 19, 70, 17, "rule"], [77, 23, 70, 21], [77, 27, 70, 25, "firstAnimationStep"], [77, 45, 70, 43], [77, 46, 70, 44, "transform"], [77, 55, 70, 53], [77, 57, 70, 55], [78, 10, 71, 8], [79, 10, 72, 8], [79, 15, 72, 13], [79, 21, 72, 19], [79, 22, 72, 20, "property"], [79, 30, 72, 28], [79, 32, 72, 30, "value"], [79, 37, 72, 35], [79, 38, 72, 36], [79, 42, 72, 40, "Object"], [79, 48, 72, 46], [79, 49, 72, 47, "entries"], [79, 56, 72, 54], [79, 57, 72, 55, "rule"], [79, 61, 72, 59], [79, 62, 72, 60], [79, 64, 72, 62], [80, 12, 73, 10, "transformStyle"], [80, 26, 73, 24], [80, 27, 73, 25, "set"], [80, 30, 73, 28], [80, 31, 73, 29, "property"], [80, 39, 73, 37], [80, 41, 73, 39, "value"], [80, 46, 73, 44], [80, 47, 73, 45], [81, 10, 74, 8], [82, 8, 75, 6], [84, 8, 77, 6], [85, 8, 78, 6], [85, 13, 78, 11], [85, 19, 78, 17, "rule"], [85, 23, 78, 21], [85, 27, 78, 25, "transformWithPx"], [85, 42, 78, 40], [85, 44, 78, 42], [86, 10, 79, 8], [86, 15, 79, 13], [86, 21, 79, 19], [86, 22, 79, 20, "property"], [86, 30, 79, 28], [86, 32, 79, 30, "value"], [86, 37, 79, 35], [86, 38, 79, 36], [86, 42, 79, 40, "Object"], [86, 48, 79, 46], [86, 49, 79, 47, "entries"], [86, 56, 79, 54], [86, 57, 79, 55, "rule"], [86, 61, 79, 59], [86, 62, 79, 60], [86, 64, 79, 62], [87, 12, 80, 10, "transformStyle"], [87, 26, 80, 24], [87, 27, 80, 25, "set"], [87, 30, 80, 28], [87, 31, 80, 29, "property"], [87, 39, 80, 37], [87, 41, 80, 39, "value"], [87, 46, 80, 44], [87, 47, 80, 45], [88, 10, 81, 8], [89, 8, 82, 6], [91, 8, 84, 6], [92, 8, 85, 6, "firstAnimationStep"], [92, 26, 85, 24], [92, 27, 85, 25, "transform"], [92, 36, 85, 34], [92, 39, 85, 37, "Array"], [92, 44, 85, 42], [92, 45, 85, 43, "from"], [92, 49, 85, 47], [92, 50, 85, 48, "transformStyle"], [92, 64, 85, 62], [92, 66, 85, 64], [92, 67, 85, 65], [92, 68, 85, 66, "property"], [92, 76, 85, 74], [92, 78, 85, 76, "value"], [92, 83, 85, 81], [92, 84, 85, 82], [92, 90, 85, 88], [93, 10, 86, 8], [93, 11, 86, 9, "property"], [93, 19, 86, 17], [93, 22, 86, 20, "value"], [94, 8, 87, 6], [94, 9, 87, 7], [94, 10, 87, 8], [94, 11, 87, 9], [95, 6, 88, 4], [96, 4, 89, 2], [97, 4, 90, 2, "animationStyle"], [97, 18, 90, 16], [97, 19, 90, 17], [97, 22, 90, 20], [97, 23, 90, 21], [97, 26, 90, 24], [98, 6, 91, 4], [98, 9, 91, 7, "animationStyle"], [98, 23, 91, 21], [98, 24, 91, 22], [98, 27, 91, 25], [98, 28, 91, 26], [99, 6, 92, 4], [99, 9, 92, 7, "rest"], [100, 4, 93, 2], [100, 5, 93, 3], [102, 4, 95, 2], [103, 4, 96, 2], [103, 10, 96, 8, "keyframeName"], [103, 22, 96, 20], [103, 25, 96, 23, "generateNextCustomKeyframeName"], [103, 55, 96, 53], [103, 56, 96, 54], [103, 57, 96, 55], [104, 4, 97, 2], [104, 10, 97, 8, "animationObject"], [104, 25, 97, 23], [104, 28, 97, 26], [105, 6, 98, 4, "name"], [105, 10, 98, 8], [105, 12, 98, 10, "keyframeName"], [105, 24, 98, 22], [106, 6, 99, 4, "style"], [106, 11, 99, 9], [106, 13, 99, 11, "animationStyle"], [106, 27, 99, 25], [107, 6, 100, 4, "duration"], [107, 14, 100, 12], [107, 16, 100, 14, "AnimationsData"], [107, 38, 100, 28], [107, 39, 100, 29, "animationName"], [107, 52, 100, 42], [107, 53, 100, 43], [107, 54, 100, 44, "duration"], [108, 4, 101, 2], [108, 5, 101, 3], [109, 4, 102, 2], [109, 10, 102, 8, "keyframe"], [109, 18, 102, 16], [109, 21, 102, 19], [109, 25, 102, 19, "convertAnimationObjectToKeyframes"], [109, 75, 102, 52], [109, 77, 102, 53, "animationObject"], [109, 92, 102, 68], [109, 93, 102, 69], [110, 4, 103, 2], [110, 8, 103, 2, "insertWebAnimation"], [110, 36, 103, 20], [110, 38, 103, 21, "keyframeName"], [110, 50, 103, 33], [110, 52, 103, 35, "keyframe"], [110, 60, 103, 43], [110, 61, 103, 44], [111, 4, 104, 2], [111, 11, 104, 9, "keyframeName"], [111, 23, 104, 21], [112, 2, 105, 0], [113, 2, 106, 0], [113, 6, 106, 4, "customKeyframeCounter"], [113, 27, 106, 25], [113, 30, 106, 28], [113, 31, 106, 29], [114, 2, 107, 0], [114, 11, 107, 9, "generateNextCustomKeyframeName"], [114, 41, 107, 39, "generateNextCustomKeyframeName"], [114, 42, 107, 39], [114, 44, 107, 42], [115, 4, 108, 2], [115, 11, 108, 9], [115, 17, 108, 15, "customKeyframeCounter"], [115, 38, 108, 36], [115, 40, 108, 38], [115, 42, 108, 40], [116, 2, 109, 0], [118, 2, 111, 0], [119, 0, 112, 0], [120, 0, 113, 0], [121, 0, 114, 0], [122, 0, 115, 0], [123, 0, 116, 0], [124, 0, 117, 0], [125, 0, 118, 0], [126, 0, 119, 0], [127, 2, 120, 7], [127, 11, 120, 16, "TransitionGenerator"], [127, 30, 120, 35, "TransitionGenerator"], [127, 31, 120, 36, "transitionType"], [127, 45, 120, 50], [127, 47, 120, 52, "transitionData"], [127, 61, 120, 66], [127, 63, 120, 68], [128, 4, 121, 2], [128, 10, 121, 8, "transitionKeyframeName"], [128, 32, 121, 30], [128, 35, 121, 33, "generateNextCustomKeyframeName"], [128, 65, 121, 63], [128, 66, 121, 64], [128, 67, 121, 65], [129, 4, 122, 2], [129, 8, 122, 6, "dummyTransitionKeyframeName"], [129, 35, 122, 33], [130, 4, 123, 2], [130, 8, 123, 6, "transitionObject"], [130, 24, 123, 22], [131, 4, 124, 2], [131, 12, 124, 10, "transitionType"], [131, 26, 124, 24], [132, 6, 125, 4], [132, 11, 125, 9, "TransitionType"], [132, 33, 125, 23], [132, 34, 125, 24, "LINEAR"], [132, 40, 125, 30], [133, 8, 126, 6, "transitionObject"], [133, 24, 126, 22], [133, 27, 126, 25], [133, 31, 126, 25, "LinearTransition"], [133, 58, 126, 41], [133, 60, 126, 42, "transitionKeyframeName"], [133, 82, 126, 64], [133, 84, 126, 66, "transitionData"], [133, 98, 126, 80], [133, 99, 126, 81], [134, 8, 127, 6], [135, 6, 128, 4], [135, 11, 128, 9, "TransitionType"], [135, 33, 128, 23], [135, 34, 128, 24, "SEQUENCED"], [135, 43, 128, 33], [136, 8, 129, 6, "transitionObject"], [136, 24, 129, 22], [136, 27, 129, 25], [136, 31, 129, 25, "SequencedTransition"], [136, 64, 129, 44], [136, 66, 129, 45, "transitionKeyframeName"], [136, 88, 129, 67], [136, 90, 129, 69, "transitionData"], [136, 104, 129, 83], [136, 105, 129, 84], [137, 8, 130, 6], [138, 6, 131, 4], [138, 11, 131, 9, "TransitionType"], [138, 33, 131, 23], [138, 34, 131, 24, "FADING"], [138, 40, 131, 30], [139, 8, 132, 6, "transitionObject"], [139, 24, 132, 22], [139, 27, 132, 25], [139, 31, 132, 25, "FadingTransition"], [139, 58, 132, 41], [139, 60, 132, 42, "transitionKeyframeName"], [139, 82, 132, 64], [139, 84, 132, 66, "transitionData"], [139, 98, 132, 80], [139, 99, 132, 81], [140, 8, 133, 6], [141, 6, 134, 4], [141, 11, 134, 9, "TransitionType"], [141, 33, 134, 23], [141, 34, 134, 24, "JUMPING"], [141, 41, 134, 31], [142, 8, 135, 6, "transitionObject"], [142, 24, 135, 22], [142, 27, 135, 25], [142, 31, 135, 25, "JumpingTransition"], [142, 60, 135, 42], [142, 62, 135, 43, "transitionKeyframeName"], [142, 84, 135, 65], [142, 86, 135, 67, "transitionData"], [142, 100, 135, 81], [142, 101, 135, 82], [143, 8, 136, 6], [145, 6, 138, 4], [146, 6, 139, 4], [146, 11, 139, 9, "TransitionType"], [146, 33, 139, 23], [146, 34, 139, 24, "CURVED"], [146, 40, 139, 30], [147, 8, 140, 6], [148, 10, 141, 8, "dummyTransitionKeyframeName"], [148, 37, 141, 35], [148, 40, 141, 38, "generateNextCustomKeyframeName"], [148, 70, 141, 68], [148, 71, 141, 69], [148, 72, 141, 70], [149, 10, 142, 8], [149, 16, 142, 14], [150, 12, 143, 10, "firstKeyframeObj"], [150, 28, 143, 26], [151, 12, 144, 10, "secondKeyframeObj"], [152, 10, 145, 8], [152, 11, 145, 9], [152, 14, 145, 12], [152, 18, 145, 12, "CurvedTransition"], [152, 45, 145, 28], [152, 47, 145, 29, "transitionKeyframeName"], [152, 69, 145, 51], [152, 71, 145, 53, "dummyTransitionKeyframeName"], [152, 98, 145, 80], [152, 100, 145, 82, "transitionData"], [152, 114, 145, 96], [152, 115, 145, 97], [153, 10, 146, 8, "transitionObject"], [153, 26, 146, 24], [153, 29, 146, 27, "firstKeyframeObj"], [153, 45, 146, 43], [154, 10, 147, 8], [154, 16, 147, 14, "dummy<PERSON><PERSON><PERSON>"], [154, 29, 147, 27], [154, 32, 147, 30], [154, 36, 147, 30, "convertAnimationObjectToKeyframes"], [154, 86, 147, 63], [154, 88, 147, 64, "secondKeyframeObj"], [154, 105, 147, 81], [154, 106, 147, 82], [155, 10, 148, 8], [155, 14, 148, 8, "insertWebAnimation"], [155, 42, 148, 26], [155, 44, 148, 27, "dummyTransitionKeyframeName"], [155, 71, 148, 54], [155, 73, 148, 56, "dummy<PERSON><PERSON><PERSON>"], [155, 86, 148, 69], [155, 87, 148, 70], [156, 10, 149, 8], [157, 8, 150, 6], [158, 6, 151, 4], [158, 11, 151, 9, "TransitionType"], [158, 33, 151, 23], [158, 34, 151, 24, "ENTRY_EXIT"], [158, 44, 151, 34], [159, 8, 152, 6, "transitionObject"], [159, 24, 152, 22], [159, 27, 152, 25], [159, 31, 152, 25, "EntryExitTransition"], [159, 64, 152, 44], [159, 66, 152, 45, "transitionKeyframeName"], [159, 88, 152, 67], [159, 90, 152, 69, "transitionData"], [159, 104, 152, 83], [159, 105, 152, 84], [160, 8, 153, 6], [161, 4, 154, 2], [162, 4, 155, 2], [162, 10, 155, 8, "transitionKeyframe"], [162, 28, 155, 26], [162, 31, 155, 29], [162, 35, 155, 29, "convertAnimationObjectToKeyframes"], [162, 85, 155, 62], [162, 87, 155, 63, "transitionObject"], [162, 103, 155, 79], [162, 104, 155, 80], [163, 4, 156, 2], [163, 8, 156, 2, "insertWebAnimation"], [163, 36, 156, 20], [163, 38, 156, 21, "transitionKeyframeName"], [163, 60, 156, 43], [163, 62, 156, 45, "transitionKeyframe"], [163, 80, 156, 63], [163, 81, 156, 64], [164, 4, 157, 2], [164, 11, 157, 9], [165, 6, 158, 4, "transitionKeyframeName"], [165, 28, 158, 26], [166, 6, 159, 4, "dummyTransitionKeyframeName"], [167, 4, 160, 2], [167, 5, 160, 3], [168, 2, 161, 0], [169, 0, 161, 1], [169, 3]], "functionMap": {"names": ["<global>", "addPxToTransform", "transform.map$argument_0", "createCustomKeyFrameAnimation", "createAnimationWithInitialValues", "Array.from$argument_1", "generateNextCustomKeyframeName", "TransitionGenerator"], "mappings": "AAA;ACgB;qCCG;GDa;CDE;OGC;CHe;OIC;gECiC;QDE;CJkB;AME;CNE;OOW;CPyC"}}, "type": "js/module"}]}