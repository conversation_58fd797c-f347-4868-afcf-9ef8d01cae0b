{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = sortKD;\n  function sortKD(ids, coords, nodeSize, left, right, depth) {\n    if (right - left <= nodeSize) return;\n    const m = left + right >> 1;\n    select(ids, coords, m, left, right, depth % 2);\n    sortKD(ids, coords, nodeSize, left, m - 1, depth + 1);\n    sortKD(ids, coords, nodeSize, m + 1, right, depth + 1);\n  }\n  function select(ids, coords, k, left, right, inc) {\n    while (right > left) {\n      if (right - left > 600) {\n        const n = right - left + 1;\n        const m = k - left + 1;\n        const z = Math.log(n);\n        const s = 0.5 * Math.exp(2 * z / 3);\n        const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n        const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n        const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n        select(ids, coords, k, newLeft, newRight, inc);\n      }\n      const t = coords[2 * k + inc];\n      let i = left;\n      let j = right;\n      swapItem(ids, coords, left, k);\n      if (coords[2 * right + inc] > t) swapItem(ids, coords, left, right);\n      while (i < j) {\n        swapItem(ids, coords, i, j);\n        i++;\n        j--;\n        while (coords[2 * i + inc] < t) i++;\n        while (coords[2 * j + inc] > t) j--;\n      }\n      if (coords[2 * left + inc] === t) swapItem(ids, coords, left, j);else {\n        j++;\n        swapItem(ids, coords, j, right);\n      }\n      if (j <= k) left = j + 1;\n      if (k <= j) right = j - 1;\n    }\n  }\n  function swapItem(ids, coords, i, j) {\n    swap(ids, i, j);\n    swap(coords, 2 * i, 2 * j);\n    swap(coords, 2 * i + 1, 2 * j + 1);\n  }\n  function swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n  }\n});", "lineCount": 55, "map": [[6, 2, 2, 15], [6, 11, 2, 24, "sortKD"], [6, 17, 2, 30, "sortKD"], [6, 18, 2, 31, "ids"], [6, 21, 2, 34], [6, 23, 2, 36, "coords"], [6, 29, 2, 42], [6, 31, 2, 44, "nodeSize"], [6, 39, 2, 52], [6, 41, 2, 54, "left"], [6, 45, 2, 58], [6, 47, 2, 60, "right"], [6, 52, 2, 65], [6, 54, 2, 67, "depth"], [6, 59, 2, 72], [6, 61, 2, 74], [7, 4, 3, 4], [7, 8, 3, 8, "right"], [7, 13, 3, 13], [7, 16, 3, 16, "left"], [7, 20, 3, 20], [7, 24, 3, 24, "nodeSize"], [7, 32, 3, 32], [7, 34, 3, 34], [8, 4, 5, 4], [8, 10, 5, 10, "m"], [8, 11, 5, 11], [8, 14, 5, 15, "left"], [8, 18, 5, 19], [8, 21, 5, 22, "right"], [8, 26, 5, 27], [8, 30, 5, 32], [8, 31, 5, 33], [9, 4, 7, 4, "select"], [9, 10, 7, 10], [9, 11, 7, 11, "ids"], [9, 14, 7, 14], [9, 16, 7, 16, "coords"], [9, 22, 7, 22], [9, 24, 7, 24, "m"], [9, 25, 7, 25], [9, 27, 7, 27, "left"], [9, 31, 7, 31], [9, 33, 7, 33, "right"], [9, 38, 7, 38], [9, 40, 7, 40, "depth"], [9, 45, 7, 45], [9, 48, 7, 48], [9, 49, 7, 49], [9, 50, 7, 50], [10, 4, 9, 4, "sortKD"], [10, 10, 9, 10], [10, 11, 9, 11, "ids"], [10, 14, 9, 14], [10, 16, 9, 16, "coords"], [10, 22, 9, 22], [10, 24, 9, 24, "nodeSize"], [10, 32, 9, 32], [10, 34, 9, 34, "left"], [10, 38, 9, 38], [10, 40, 9, 40, "m"], [10, 41, 9, 41], [10, 44, 9, 44], [10, 45, 9, 45], [10, 47, 9, 47, "depth"], [10, 52, 9, 52], [10, 55, 9, 55], [10, 56, 9, 56], [10, 57, 9, 57], [11, 4, 10, 4, "sortKD"], [11, 10, 10, 10], [11, 11, 10, 11, "ids"], [11, 14, 10, 14], [11, 16, 10, 16, "coords"], [11, 22, 10, 22], [11, 24, 10, 24, "nodeSize"], [11, 32, 10, 32], [11, 34, 10, 34, "m"], [11, 35, 10, 35], [11, 38, 10, 38], [11, 39, 10, 39], [11, 41, 10, 41, "right"], [11, 46, 10, 46], [11, 48, 10, 48, "depth"], [11, 53, 10, 53], [11, 56, 10, 56], [11, 57, 10, 57], [11, 58, 10, 58], [12, 2, 11, 0], [13, 2, 13, 0], [13, 11, 13, 9, "select"], [13, 17, 13, 15, "select"], [13, 18, 13, 16, "ids"], [13, 21, 13, 19], [13, 23, 13, 21, "coords"], [13, 29, 13, 27], [13, 31, 13, 29, "k"], [13, 32, 13, 30], [13, 34, 13, 32, "left"], [13, 38, 13, 36], [13, 40, 13, 38, "right"], [13, 45, 13, 43], [13, 47, 13, 45, "inc"], [13, 50, 13, 48], [13, 52, 13, 50], [14, 4, 15, 4], [14, 11, 15, 11, "right"], [14, 16, 15, 16], [14, 19, 15, 19, "left"], [14, 23, 15, 23], [14, 25, 15, 25], [15, 6, 16, 8], [15, 10, 16, 12, "right"], [15, 15, 16, 17], [15, 18, 16, 20, "left"], [15, 22, 16, 24], [15, 25, 16, 27], [15, 28, 16, 30], [15, 30, 16, 32], [16, 8, 17, 12], [16, 14, 17, 18, "n"], [16, 15, 17, 19], [16, 18, 17, 22, "right"], [16, 23, 17, 27], [16, 26, 17, 30, "left"], [16, 30, 17, 34], [16, 33, 17, 37], [16, 34, 17, 38], [17, 8, 18, 12], [17, 14, 18, 18, "m"], [17, 15, 18, 19], [17, 18, 18, 22, "k"], [17, 19, 18, 23], [17, 22, 18, 26, "left"], [17, 26, 18, 30], [17, 29, 18, 33], [17, 30, 18, 34], [18, 8, 19, 12], [18, 14, 19, 18, "z"], [18, 15, 19, 19], [18, 18, 19, 22, "Math"], [18, 22, 19, 26], [18, 23, 19, 27, "log"], [18, 26, 19, 30], [18, 27, 19, 31, "n"], [18, 28, 19, 32], [18, 29, 19, 33], [19, 8, 20, 12], [19, 14, 20, 18, "s"], [19, 15, 20, 19], [19, 18, 20, 22], [19, 21, 20, 25], [19, 24, 20, 28, "Math"], [19, 28, 20, 32], [19, 29, 20, 33, "exp"], [19, 32, 20, 36], [19, 33, 20, 37], [19, 34, 20, 38], [19, 37, 20, 41, "z"], [19, 38, 20, 42], [19, 41, 20, 45], [19, 42, 20, 46], [19, 43, 20, 47], [20, 8, 21, 12], [20, 14, 21, 18, "sd"], [20, 16, 21, 20], [20, 19, 21, 23], [20, 22, 21, 26], [20, 25, 21, 29, "Math"], [20, 29, 21, 33], [20, 30, 21, 34, "sqrt"], [20, 34, 21, 38], [20, 35, 21, 39, "z"], [20, 36, 21, 40], [20, 39, 21, 43, "s"], [20, 40, 21, 44], [20, 44, 21, 48, "n"], [20, 45, 21, 49], [20, 48, 21, 52, "s"], [20, 49, 21, 53], [20, 50, 21, 54], [20, 53, 21, 57, "n"], [20, 54, 21, 58], [20, 55, 21, 59], [20, 59, 21, 63, "m"], [20, 60, 21, 64], [20, 63, 21, 67, "n"], [20, 64, 21, 68], [20, 67, 21, 71], [20, 68, 21, 72], [20, 71, 21, 75], [20, 72, 21, 76], [20, 75, 21, 79], [20, 76, 21, 80], [20, 77, 21, 81], [20, 80, 21, 84], [20, 81, 21, 85], [20, 82, 21, 86], [21, 8, 22, 12], [21, 14, 22, 18, "newLeft"], [21, 21, 22, 25], [21, 24, 22, 28, "Math"], [21, 28, 22, 32], [21, 29, 22, 33, "max"], [21, 32, 22, 36], [21, 33, 22, 37, "left"], [21, 37, 22, 41], [21, 39, 22, 43, "Math"], [21, 43, 22, 47], [21, 44, 22, 48, "floor"], [21, 49, 22, 53], [21, 50, 22, 54, "k"], [21, 51, 22, 55], [21, 54, 22, 58, "m"], [21, 55, 22, 59], [21, 58, 22, 62, "s"], [21, 59, 22, 63], [21, 62, 22, 66, "n"], [21, 63, 22, 67], [21, 66, 22, 70, "sd"], [21, 68, 22, 72], [21, 69, 22, 73], [21, 70, 22, 74], [22, 8, 23, 12], [22, 14, 23, 18, "newRight"], [22, 22, 23, 26], [22, 25, 23, 29, "Math"], [22, 29, 23, 33], [22, 30, 23, 34, "min"], [22, 33, 23, 37], [22, 34, 23, 38, "right"], [22, 39, 23, 43], [22, 41, 23, 45, "Math"], [22, 45, 23, 49], [22, 46, 23, 50, "floor"], [22, 51, 23, 55], [22, 52, 23, 56, "k"], [22, 53, 23, 57], [22, 56, 23, 60], [22, 57, 23, 61, "n"], [22, 58, 23, 62], [22, 61, 23, 65, "m"], [22, 62, 23, 66], [22, 66, 23, 70, "s"], [22, 67, 23, 71], [22, 70, 23, 74, "n"], [22, 71, 23, 75], [22, 74, 23, 78, "sd"], [22, 76, 23, 80], [22, 77, 23, 81], [22, 78, 23, 82], [23, 8, 24, 12, "select"], [23, 14, 24, 18], [23, 15, 24, 19, "ids"], [23, 18, 24, 22], [23, 20, 24, 24, "coords"], [23, 26, 24, 30], [23, 28, 24, 32, "k"], [23, 29, 24, 33], [23, 31, 24, 35, "newLeft"], [23, 38, 24, 42], [23, 40, 24, 44, "newRight"], [23, 48, 24, 52], [23, 50, 24, 54, "inc"], [23, 53, 24, 57], [23, 54, 24, 58], [24, 6, 25, 8], [25, 6, 27, 8], [25, 12, 27, 14, "t"], [25, 13, 27, 15], [25, 16, 27, 18, "coords"], [25, 22, 27, 24], [25, 23, 27, 25], [25, 24, 27, 26], [25, 27, 27, 29, "k"], [25, 28, 27, 30], [25, 31, 27, 33, "inc"], [25, 34, 27, 36], [25, 35, 27, 37], [26, 6, 28, 8], [26, 10, 28, 12, "i"], [26, 11, 28, 13], [26, 14, 28, 16, "left"], [26, 18, 28, 20], [27, 6, 29, 8], [27, 10, 29, 12, "j"], [27, 11, 29, 13], [27, 14, 29, 16, "right"], [27, 19, 29, 21], [28, 6, 31, 8, "swapItem"], [28, 14, 31, 16], [28, 15, 31, 17, "ids"], [28, 18, 31, 20], [28, 20, 31, 22, "coords"], [28, 26, 31, 28], [28, 28, 31, 30, "left"], [28, 32, 31, 34], [28, 34, 31, 36, "k"], [28, 35, 31, 37], [28, 36, 31, 38], [29, 6, 32, 8], [29, 10, 32, 12, "coords"], [29, 16, 32, 18], [29, 17, 32, 19], [29, 18, 32, 20], [29, 21, 32, 23, "right"], [29, 26, 32, 28], [29, 29, 32, 31, "inc"], [29, 32, 32, 34], [29, 33, 32, 35], [29, 36, 32, 38, "t"], [29, 37, 32, 39], [29, 39, 32, 41, "swapItem"], [29, 47, 32, 49], [29, 48, 32, 50, "ids"], [29, 51, 32, 53], [29, 53, 32, 55, "coords"], [29, 59, 32, 61], [29, 61, 32, 63, "left"], [29, 65, 32, 67], [29, 67, 32, 69, "right"], [29, 72, 32, 74], [29, 73, 32, 75], [30, 6, 34, 8], [30, 13, 34, 15, "i"], [30, 14, 34, 16], [30, 17, 34, 19, "j"], [30, 18, 34, 20], [30, 20, 34, 22], [31, 8, 35, 12, "swapItem"], [31, 16, 35, 20], [31, 17, 35, 21, "ids"], [31, 20, 35, 24], [31, 22, 35, 26, "coords"], [31, 28, 35, 32], [31, 30, 35, 34, "i"], [31, 31, 35, 35], [31, 33, 35, 37, "j"], [31, 34, 35, 38], [31, 35, 35, 39], [32, 8, 36, 12, "i"], [32, 9, 36, 13], [32, 11, 36, 15], [33, 8, 37, 12, "j"], [33, 9, 37, 13], [33, 11, 37, 15], [34, 8, 38, 12], [34, 15, 38, 19, "coords"], [34, 21, 38, 25], [34, 22, 38, 26], [34, 23, 38, 27], [34, 26, 38, 30, "i"], [34, 27, 38, 31], [34, 30, 38, 34, "inc"], [34, 33, 38, 37], [34, 34, 38, 38], [34, 37, 38, 41, "t"], [34, 38, 38, 42], [34, 40, 38, 44, "i"], [34, 41, 38, 45], [34, 43, 38, 47], [35, 8, 39, 12], [35, 15, 39, 19, "coords"], [35, 21, 39, 25], [35, 22, 39, 26], [35, 23, 39, 27], [35, 26, 39, 30, "j"], [35, 27, 39, 31], [35, 30, 39, 34, "inc"], [35, 33, 39, 37], [35, 34, 39, 38], [35, 37, 39, 41, "t"], [35, 38, 39, 42], [35, 40, 39, 44, "j"], [35, 41, 39, 45], [35, 43, 39, 47], [36, 6, 40, 8], [37, 6, 42, 8], [37, 10, 42, 12, "coords"], [37, 16, 42, 18], [37, 17, 42, 19], [37, 18, 42, 20], [37, 21, 42, 23, "left"], [37, 25, 42, 27], [37, 28, 42, 30, "inc"], [37, 31, 42, 33], [37, 32, 42, 34], [37, 37, 42, 39, "t"], [37, 38, 42, 40], [37, 40, 42, 42, "swapItem"], [37, 48, 42, 50], [37, 49, 42, 51, "ids"], [37, 52, 42, 54], [37, 54, 42, 56, "coords"], [37, 60, 42, 62], [37, 62, 42, 64, "left"], [37, 66, 42, 68], [37, 68, 42, 70, "j"], [37, 69, 42, 71], [37, 70, 42, 72], [37, 71, 42, 73], [37, 76, 43, 13], [38, 8, 44, 12, "j"], [38, 9, 44, 13], [38, 11, 44, 15], [39, 8, 45, 12, "swapItem"], [39, 16, 45, 20], [39, 17, 45, 21, "ids"], [39, 20, 45, 24], [39, 22, 45, 26, "coords"], [39, 28, 45, 32], [39, 30, 45, 34, "j"], [39, 31, 45, 35], [39, 33, 45, 37, "right"], [39, 38, 45, 42], [39, 39, 45, 43], [40, 6, 46, 8], [41, 6, 48, 8], [41, 10, 48, 12, "j"], [41, 11, 48, 13], [41, 15, 48, 17, "k"], [41, 16, 48, 18], [41, 18, 48, 20, "left"], [41, 22, 48, 24], [41, 25, 48, 27, "j"], [41, 26, 48, 28], [41, 29, 48, 31], [41, 30, 48, 32], [42, 6, 49, 8], [42, 10, 49, 12, "k"], [42, 11, 49, 13], [42, 15, 49, 17, "j"], [42, 16, 49, 18], [42, 18, 49, 20, "right"], [42, 23, 49, 25], [42, 26, 49, 28, "j"], [42, 27, 49, 29], [42, 30, 49, 32], [42, 31, 49, 33], [43, 4, 50, 4], [44, 2, 51, 0], [45, 2, 53, 0], [45, 11, 53, 9, "swapItem"], [45, 19, 53, 17, "swapItem"], [45, 20, 53, 18, "ids"], [45, 23, 53, 21], [45, 25, 53, 23, "coords"], [45, 31, 53, 29], [45, 33, 53, 31, "i"], [45, 34, 53, 32], [45, 36, 53, 34, "j"], [45, 37, 53, 35], [45, 39, 53, 37], [46, 4, 54, 4, "swap"], [46, 8, 54, 8], [46, 9, 54, 9, "ids"], [46, 12, 54, 12], [46, 14, 54, 14, "i"], [46, 15, 54, 15], [46, 17, 54, 17, "j"], [46, 18, 54, 18], [46, 19, 54, 19], [47, 4, 55, 4, "swap"], [47, 8, 55, 8], [47, 9, 55, 9, "coords"], [47, 15, 55, 15], [47, 17, 55, 17], [47, 18, 55, 18], [47, 21, 55, 21, "i"], [47, 22, 55, 22], [47, 24, 55, 24], [47, 25, 55, 25], [47, 28, 55, 28, "j"], [47, 29, 55, 29], [47, 30, 55, 30], [48, 4, 56, 4, "swap"], [48, 8, 56, 8], [48, 9, 56, 9, "coords"], [48, 15, 56, 15], [48, 17, 56, 17], [48, 18, 56, 18], [48, 21, 56, 21, "i"], [48, 22, 56, 22], [48, 25, 56, 25], [48, 26, 56, 26], [48, 28, 56, 28], [48, 29, 56, 29], [48, 32, 56, 32, "j"], [48, 33, 56, 33], [48, 36, 56, 36], [48, 37, 56, 37], [48, 38, 56, 38], [49, 2, 57, 0], [50, 2, 59, 0], [50, 11, 59, 9, "swap"], [50, 15, 59, 13, "swap"], [50, 16, 59, 14, "arr"], [50, 19, 59, 17], [50, 21, 59, 19, "i"], [50, 22, 59, 20], [50, 24, 59, 22, "j"], [50, 25, 59, 23], [50, 27, 59, 25], [51, 4, 60, 4], [51, 10, 60, 10, "tmp"], [51, 13, 60, 13], [51, 16, 60, 16, "arr"], [51, 19, 60, 19], [51, 20, 60, 20, "i"], [51, 21, 60, 21], [51, 22, 60, 22], [52, 4, 61, 4, "arr"], [52, 7, 61, 7], [52, 8, 61, 8, "i"], [52, 9, 61, 9], [52, 10, 61, 10], [52, 13, 61, 13, "arr"], [52, 16, 61, 16], [52, 17, 61, 17, "j"], [52, 18, 61, 18], [52, 19, 61, 19], [53, 4, 62, 4, "arr"], [53, 7, 62, 7], [53, 8, 62, 8, "j"], [53, 9, 62, 9], [53, 10, 62, 10], [53, 13, 62, 13, "tmp"], [53, 16, 62, 16], [54, 2, 63, 0], [55, 0, 63, 1], [55, 3]], "functionMap": {"names": ["<global>", "sortKD", "select", "swapItem", "swap"], "mappings": "AAA;eCC;CDS;AEE;CFsC;AGE;CHI;AIE;CJI"}}, "type": "js/module"}]}