{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 43, "index": 43}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 44}, "end": {"line": 2, "column": 42, "index": 86}}], "key": "AXHAxFjlDdeq1JxYZnWn+aHYhYU=", "exportNames": ["*"]}}, {"name": "./ReanimatedProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 87}, "end": {"line": 3, "column": 36, "index": 123}}], "key": "9/hMKtQD5ZrdSt9i8tblq1v6X3Y=", "exportNames": ["*"]}}, {"name": "./useVideoLoading", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 124}, "end": {"line": 4, "column": 52, "index": 176}}], "key": "QeeV4stkOkmpLIlN+ySkTHT6foA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useVideo = void 0;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _Platform = require(_dependencyMap[2], \"../../Platform\");\n  var _ReanimatedProxy = _interopRequireDefault(require(_dependencyMap[3], \"./ReanimatedProxy\"));\n  var _useVideoLoading = require(_dependencyMap[4], \"./useVideoLoading\");\n  const _worklet_12211124293784_init_data = {\n    code: \"function useVideoJs1(currentFrame){const{Platform}=this.__closure;if(Platform.OS===\\\"android\\\"){const tex=currentFrame.value;if(tex){currentFrame.value=tex.makeNonTextureImage();tex.dispose();}}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs1\\\",\\\"currentFrame\\\",\\\"Platform\\\",\\\"__closure\\\",\\\"OS\\\",\\\"tex\\\",\\\"value\\\",\\\"makeNonTextureImage\\\",\\\"dispose\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AAI2B,SAAAA,YAAAC,YAAgB,QAAAC,QAAA,OAAAC,SAAA,CAIzC,GAAID,QAAQ,CAACE,EAAE,GAAK,SAAS,CAAE,CAC7B,KAAM,CAAAC,GAAG,CAAGJ,YAAY,CAACK,KAAK,CAC9B,GAAID,GAAG,CAAE,CACPJ,YAAY,CAACK,KAAK,CAAGD,GAAG,CAACE,mBAAmB,CAAC,CAAC,CAC9CF,GAAG,CAACG,OAAO,CAAC,CAAC,CACf,CACF,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const copyFrameOnAndroid = function () {\n    const _e = [new global.Error(), -2, -27];\n    const useVideoJs1 = function (currentFrame) {\n      // on android we need to copy the texture before it's invalidated\n      if (_Platform.Platform.OS === \"android\") {\n        const tex = currentFrame.value;\n        if (tex) {\n          currentFrame.value = tex.makeNonTextureImage();\n          tex.dispose();\n        }\n      }\n    };\n    useVideoJs1.__closure = {\n      Platform: _Platform.Platform\n    };\n    useVideoJs1.__workletHash = 12211124293784;\n    useVideoJs1.__initData = _worklet_12211124293784_init_data;\n    useVideoJs1.__stackDetails = _e;\n    return useVideoJs1;\n  }();\n  const _worklet_14376721059946_init_data = {\n    code: \"function useVideoJs2(video,currentFrame){const{copyFrameOnAndroid}=this.__closure;const img=video.nextImage();if(img){if(currentFrame.value){currentFrame.value.dispose();}currentFrame.value=img;copyFrameOnAndroid(currentFrame);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs2\\\",\\\"video\\\",\\\"currentFrame\\\",\\\"copyFrameOnAndroid\\\",\\\"__closure\\\",\\\"img\\\",\\\"nextImage\\\",\\\"value\\\",\\\"dispose\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AAgBiB,QAAC,CAAAA,WAAOA,CAAAC,KAAA,CAAAC,YAAiB,QAAAC,kBAAA,OAAAC,SAAA,CAGxC,KAAM,CAAAC,GAAG,CAAGJ,KAAK,CAACK,SAAS,CAAC,CAAC,CAC7B,GAAID,GAAG,CAAE,CACP,GAAIH,YAAY,CAACK,KAAK,CAAE,CACtBL,YAAY,CAACK,KAAK,CAACC,OAAO,CAAC,CAAC,CAC9B,CACAN,YAAY,CAACK,KAAK,CAAGF,GAAG,CACxBF,kBAAkB,CAACD,YAAY,CAAC,CAClC,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const setFrame = function () {\n    const _e = [new global.Error(), -2, -27];\n    const useVideoJs2 = function (video, currentFrame) {\n      const img = video.nextImage();\n      if (img) {\n        if (currentFrame.value) {\n          currentFrame.value.dispose();\n        }\n        currentFrame.value = img;\n        copyFrameOnAndroid(currentFrame);\n      }\n    };\n    useVideoJs2.__closure = {\n      copyFrameOnAndroid\n    };\n    useVideoJs2.__workletHash = 14376721059946;\n    useVideoJs2.__initData = _worklet_14376721059946_init_data;\n    useVideoJs2.__stackDetails = _e;\n    return useVideoJs2;\n  }();\n  const defaultOptions = {\n    looping: true,\n    paused: false,\n    seek: null,\n    currentTime: 0,\n    volume: 0\n  };\n  const _worklet_1683212240703_init_data = {\n    code: \"function useVideoJs3(value){const{Rea}=this.__closure;const defaultValue=Rea.useSharedValue(Rea.isSharedValue(value)?value.value:value);return Rea.isSharedValue(value)?value:defaultValue;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs3\\\",\\\"value\\\",\\\"Rea\\\",\\\"__closure\\\",\\\"defaultValue\\\",\\\"useSharedValue\\\",\\\"isSharedValue\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AAmCkB,SAAAA,WAASA,CAAAC,KAAA,QAAAC,GAAA,OAAAC,SAAA,CAIzB,KAAM,CAAAC,YAAY,CAAGF,GAAG,CAACG,cAAc,CAACH,GAAG,CAACI,aAAa,CAACL,KAAK,CAAC,CAAGA,KAAK,CAACA,KAAK,CAAGA,KAAK,CAAC,CACvF,MAAO,CAAAC,GAAG,CAACI,aAAa,CAACL,KAAK,CAAC,CAAGA,KAAK,CAAGG,YAAY,CACxD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const useOption = function () {\n    const _e = [new global.Error(), -2, -27];\n    const useVideoJs3 = function (value) {\n      // TODO: only create defaultValue is needed (via makeMutable)\n      const defaultValue = _ReanimatedProxy.default.useSharedValue(_ReanimatedProxy.default.isSharedValue(value) ? value.value : value);\n      return _ReanimatedProxy.default.isSharedValue(value) ? value : defaultValue;\n    };\n    useVideoJs3.__closure = {\n      Rea: _ReanimatedProxy.default\n    };\n    useVideoJs3.__workletHash = 1683212240703;\n    useVideoJs3.__initData = _worklet_1683212240703_init_data;\n    useVideoJs3.__stackDetails = _e;\n    return useVideoJs3;\n  }();\n  const _worklet_16672310892193_init_data = {\n    code: \"function useVideoJs4(video){video===null||video===void 0||video.dispose();}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs4\\\",\\\"video\\\",\\\"dispose\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AA0CqB,SAAAA,WAASA,CAAAC,KAAA,EAG5BA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAK,EAAC,EAAIA,KAAK,CAACC,OAAO,CAAC,CAAC,CACvD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const disposeVideo = function () {\n    const _e = [new global.Error(), 1, -27];\n    const useVideoJs4 = function (video) {\n      video === null || video === void 0 || video.dispose();\n    };\n    useVideoJs4.__closure = {};\n    useVideoJs4.__workletHash = 16672310892193;\n    useVideoJs4.__initData = _worklet_16672310892193_init_data;\n    useVideoJs4.__stackDetails = _e;\n    return useVideoJs4;\n  }();\n  const _worklet_15834071128564_init_data = {\n    code: \"function useVideoJs5(){const{isPaused}=this.__closure;return isPaused.value;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs5\\\",\\\"isPaused\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AA8E0B,SAAAA,YAAA,QAAAC,QAAA,OAAAC,SAAA,OAAM,CAAAD,QAAS,CAAAE,KAAA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_10841070018278_init_data = {\n    code: \"function useVideoJs6(paused){const{video,lastTimestamp}=this.__closure;if(paused){video===null||video===void 0||video.pause();}else{lastTimestamp.value=-1;video===null||video===void 0||video.play();}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs6\\\",\\\"paused\\\",\\\"video\\\",\\\"lastTimestamp\\\",\\\"__closure\\\",\\\"pause\\\",\\\"value\\\",\\\"play\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AA8EgD,SAAAA,WAAUA,CAAAC,MAAA,QAAAC,KAAA,CAAAC,aAAA,OAAAC,SAAA,CACtD,GAAIH,MAAM,CAAE,CACVC,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAK,EAAC,EAAIA,KAAK,CAACG,KAAK,CAAC,CAAC,CACrD,CAAC,IAAM,CACLF,aAAa,CAACG,KAAK,CAAG,CAAC,CAAC,CACxBJ,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAK,EAAC,EAAIA,KAAK,CAACK,IAAI,CAAC,CAAC,CACpD,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_16080591650550_init_data = {\n    code: \"function useVideoJs7(){const{seek}=this.__closure;return seek.value;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs7\\\",\\\"seek\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AAsF0B,SAAAA,YAAA,QAAAC,IAAA,OAAAC,SAAA,OAAM,CAAAD,IAAK,CAAAE,KAAA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_10793983663447_init_data = {\n    code: \"function useVideoJs8(value){const{video,currentTime,seek}=this.__closure;if(value!==null){video===null||video===void 0||video.seek(value);currentTime.value=value;seek.value=null;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs8\\\",\\\"value\\\",\\\"video\\\",\\\"currentTime\\\",\\\"seek\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AAsF4C,SAAAA,WAASA,CAAAC,KAAA,QAAAC,KAAA,CAAAC,WAAA,CAAAC,IAAA,OAAAC,SAAA,CACjD,GAAIJ,KAAK,GAAK,IAAI,CAAE,CAClBC,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAK,EAAC,EAAIA,KAAK,CAACE,IAAI,CAACH,KAAK,CAAC,CACvDE,WAAW,CAACF,KAAK,CAAGA,KAAK,CACzBG,IAAI,CAACH,KAAK,CAAG,IAAI,CACnB,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_11821695268984_init_data = {\n    code: \"function useVideoJs9(){const{volume}=this.__closure;return volume.value;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs9\\\",\\\"volume\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AA6F0B,SAAAA,YAAA,QAAAC,MAAA,OAAAC,SAAA,OAAM,CAAAD,MAAO,CAAAE,KAAA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_6997409722965_init_data = {\n    code: \"function useVideoJs10(value){const{video}=this.__closure;video===null||video===void 0||video.setVolume(value);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs10\\\",\\\"value\\\",\\\"video\\\",\\\"__closure\\\",\\\"setVolume\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AA6F8C,SAAAA,YAASA,CAAAC,KAAA,QAAAC,KAAA,OAAAC,SAAA,CACnDD,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAK,EAAC,EAAIA,KAAK,CAACE,SAAS,CAACH,KAAK,CAAC,CAC9D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_7146928661804_init_data = {\n    code: \"function useVideoJs11(frameInfo){const{video,isPaused,lastTimestamp,currentTime,duration,looping,seek,currentFrameDuration,Platform,setFrame,currentFrame}=this.__closure;if(!video){return;}if(isPaused.value){return;}const currentTimestamp=frameInfo.timestamp;if(lastTimestamp.value===-1){lastTimestamp.value=currentTimestamp;}const delta=currentTimestamp-lastTimestamp.value;const isOver=currentTime.value+delta>duration;if(isOver&&looping.value){seek.value=0;currentTime.value=seek.value;lastTimestamp.value=currentTimestamp;}if(delta>=currentFrameDuration&&!isOver||Platform.OS===\\\"web\\\"){setFrame(video,currentFrame);currentTime.value+=delta;lastTimestamp.value=currentTimestamp;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useVideo.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useVideoJs11\\\",\\\"frameInfo\\\",\\\"video\\\",\\\"isPaused\\\",\\\"lastTimestamp\\\",\\\"currentTime\\\",\\\"duration\\\",\\\"looping\\\",\\\"seek\\\",\\\"currentFrameDuration\\\",\\\"Platform\\\",\\\"setFrame\\\",\\\"currentFrame\\\",\\\"__closure\\\",\\\"value\\\",\\\"currentTimestamp\\\",\\\"timestamp\\\",\\\"delta\\\",\\\"isOver\\\",\\\"OS\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useVideo.js\\\"],\\\"mappings\\\":\\\"AAgGuB,SAAAA,YAASA,CAAIC,SAAA,QAAAC,KAAA,CAAAC,QAAA,CAAAC,aAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAC,oBAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,YAAA,OAAAC,SAAA,CAGhC,GAAI,CAACX,KAAK,CAAE,CACV,OACF,CACA,GAAIC,QAAQ,CAACW,KAAK,CAAE,CAClB,OACF,CACA,KAAM,CAAAC,gBAAgB,CAAGd,SAAS,CAACe,SAAS,CAC5C,GAAIZ,aAAa,CAACU,KAAK,GAAK,CAAC,CAAC,CAAE,CAC9BV,aAAa,CAACU,KAAK,CAAGC,gBAAgB,CACxC,CACA,KAAM,CAAAE,KAAK,CAAGF,gBAAgB,CAAGX,aAAa,CAACU,KAAK,CACpD,KAAM,CAAAI,MAAM,CAAGb,WAAW,CAACS,KAAK,CAAGG,KAAK,CAAGX,QAAQ,CACnD,GAAIY,MAAM,EAAIX,OAAO,CAACO,KAAK,CAAE,CAC3BN,IAAI,CAACM,KAAK,CAAG,CAAC,CACdT,WAAW,CAACS,KAAK,CAAGN,IAAI,CAACM,KAAK,CAC9BV,aAAa,CAACU,KAAK,CAAGC,gBAAgB,CACxC,CAGA,GAAIE,KAAK,EAAIR,oBAAoB,EAAI,CAACS,MAAM,EAAIR,QAAQ,CAACS,EAAE,GAAK,KAAK,CAAE,CACrER,QAAQ,CAACT,KAAK,CAAEU,YAAY,CAAC,CAC7BP,WAAW,CAACS,KAAK,EAAIG,KAAK,CAC1Bb,aAAa,CAACU,KAAK,CAAGC,gBAAgB,CACxC,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const useVideo = (source, userOptions) => {\n    var _userOptions$paused, _userOptions$looping, _userOptions$seek, _userOptions$volume;\n    const video = (0, _useVideoLoading.useVideoLoading)(source);\n    const isPaused = useOption((_userOptions$paused = userOptions === null || userOptions === void 0 ? void 0 : userOptions.paused) !== null && _userOptions$paused !== void 0 ? _userOptions$paused : defaultOptions.paused);\n    const looping = useOption((_userOptions$looping = userOptions === null || userOptions === void 0 ? void 0 : userOptions.looping) !== null && _userOptions$looping !== void 0 ? _userOptions$looping : defaultOptions.looping);\n    const seek = useOption((_userOptions$seek = userOptions === null || userOptions === void 0 ? void 0 : userOptions.seek) !== null && _userOptions$seek !== void 0 ? _userOptions$seek : defaultOptions.seek);\n    const volume = useOption((_userOptions$volume = userOptions === null || userOptions === void 0 ? void 0 : userOptions.volume) !== null && _userOptions$volume !== void 0 ? _userOptions$volume : defaultOptions.volume);\n    const currentFrame = _ReanimatedProxy.default.useSharedValue(null);\n    const currentTime = _ReanimatedProxy.default.useSharedValue(0);\n    const lastTimestamp = _ReanimatedProxy.default.useSharedValue(-1);\n    const duration = (0, _react.useMemo)(() => {\n      var _video$duration;\n      return (_video$duration = video === null || video === void 0 ? void 0 : video.duration()) !== null && _video$duration !== void 0 ? _video$duration : 0;\n    }, [video]);\n    const framerate = (0, _react.useMemo)(() => {\n      var _video$framerate;\n      return _Platform.Platform.OS === \"web\" ? -1 : (_video$framerate = video === null || video === void 0 ? void 0 : video.framerate()) !== null && _video$framerate !== void 0 ? _video$framerate : 0;\n    }, [video]);\n    const size = (0, _react.useMemo)(() => {\n      var _video$size;\n      return (_video$size = video === null || video === void 0 ? void 0 : video.size()) !== null && _video$size !== void 0 ? _video$size : {\n        width: 0,\n        height: 0\n      };\n    }, [video]);\n    const rotation = (0, _react.useMemo)(() => {\n      var _video$rotation;\n      return (_video$rotation = video === null || video === void 0 ? void 0 : video.rotation()) !== null && _video$rotation !== void 0 ? _video$rotation : 0;\n    }, [video]);\n    const frameDuration = 1000 / framerate;\n    const currentFrameDuration = Math.floor(frameDuration);\n    _ReanimatedProxy.default.useAnimatedReaction(function () {\n      const _e = [new global.Error(), -2, -27];\n      const useVideoJs5 = () => isPaused.value;\n      useVideoJs5.__closure = {\n        isPaused\n      };\n      useVideoJs5.__workletHash = 15834071128564;\n      useVideoJs5.__initData = _worklet_15834071128564_init_data;\n      useVideoJs5.__stackDetails = _e;\n      return useVideoJs5;\n    }(), function () {\n      const _e = [new global.Error(), -3, -27];\n      const useVideoJs6 = function (paused) {\n        if (paused) {\n          video === null || video === void 0 || video.pause();\n        } else {\n          lastTimestamp.value = -1;\n          video === null || video === void 0 || video.play();\n        }\n      };\n      useVideoJs6.__closure = {\n        video,\n        lastTimestamp\n      };\n      useVideoJs6.__workletHash = 10841070018278;\n      useVideoJs6.__initData = _worklet_10841070018278_init_data;\n      useVideoJs6.__stackDetails = _e;\n      return useVideoJs6;\n    }());\n    _ReanimatedProxy.default.useAnimatedReaction(function () {\n      const _e = [new global.Error(), -2, -27];\n      const useVideoJs7 = () => seek.value;\n      useVideoJs7.__closure = {\n        seek\n      };\n      useVideoJs7.__workletHash = 16080591650550;\n      useVideoJs7.__initData = _worklet_16080591650550_init_data;\n      useVideoJs7.__stackDetails = _e;\n      return useVideoJs7;\n    }(), function () {\n      const _e = [new global.Error(), -4, -27];\n      const useVideoJs8 = function (value) {\n        if (value !== null) {\n          video === null || video === void 0 || video.seek(value);\n          currentTime.value = value;\n          seek.value = null;\n        }\n      };\n      useVideoJs8.__closure = {\n        video,\n        currentTime,\n        seek\n      };\n      useVideoJs8.__workletHash = 10793983663447;\n      useVideoJs8.__initData = _worklet_10793983663447_init_data;\n      useVideoJs8.__stackDetails = _e;\n      return useVideoJs8;\n    }());\n    _ReanimatedProxy.default.useAnimatedReaction(function () {\n      const _e = [new global.Error(), -2, -27];\n      const useVideoJs9 = () => volume.value;\n      useVideoJs9.__closure = {\n        volume\n      };\n      useVideoJs9.__workletHash = 11821695268984;\n      useVideoJs9.__initData = _worklet_11821695268984_init_data;\n      useVideoJs9.__stackDetails = _e;\n      return useVideoJs9;\n    }(), function () {\n      const _e = [new global.Error(), -2, -27];\n      const useVideoJs10 = function (value) {\n        video === null || video === void 0 || video.setVolume(value);\n      };\n      useVideoJs10.__closure = {\n        video\n      };\n      useVideoJs10.__workletHash = 6997409722965;\n      useVideoJs10.__initData = _worklet_6997409722965_init_data;\n      useVideoJs10.__stackDetails = _e;\n      return useVideoJs10;\n    }());\n    _ReanimatedProxy.default.useFrameCallback(function () {\n      const _e = [new global.Error(), -12, -27];\n      const useVideoJs11 = function (frameInfo) {\n        if (!video) {\n          return;\n        }\n        if (isPaused.value) {\n          return;\n        }\n        const currentTimestamp = frameInfo.timestamp;\n        if (lastTimestamp.value === -1) {\n          lastTimestamp.value = currentTimestamp;\n        }\n        const delta = currentTimestamp - lastTimestamp.value;\n        const isOver = currentTime.value + delta > duration;\n        if (isOver && looping.value) {\n          seek.value = 0;\n          currentTime.value = seek.value;\n          lastTimestamp.value = currentTimestamp;\n        }\n        // On Web the framerate is uknown.\n        // This could be optimized by using requestVideoFrameCallback (Chrome only)\n        if (delta >= currentFrameDuration && !isOver || _Platform.Platform.OS === \"web\") {\n          setFrame(video, currentFrame);\n          currentTime.value += delta;\n          lastTimestamp.value = currentTimestamp;\n        }\n      };\n      useVideoJs11.__closure = {\n        video,\n        isPaused,\n        lastTimestamp,\n        currentTime,\n        duration,\n        looping,\n        seek,\n        currentFrameDuration,\n        Platform: _Platform.Platform,\n        setFrame,\n        currentFrame\n      };\n      useVideoJs11.__workletHash = 7146928661804;\n      useVideoJs11.__initData = _worklet_7146928661804_init_data;\n      useVideoJs11.__stackDetails = _e;\n      return useVideoJs11;\n    }());\n    (0, _react.useEffect)(() => {\n      return () => {\n        // TODO: should video simply be a shared value instead?\n        _ReanimatedProxy.default.runOnUI(disposeVideo)(video);\n      };\n    }, [video]);\n    return {\n      currentFrame,\n      currentTime,\n      duration,\n      framerate,\n      rotation,\n      size\n    };\n  };\n  exports.useVideo = useVideo;\n});", "lineCount": 324, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "require"], [7, 22, 1, 0], [7, 23, 1, 0, "_dependencyMap"], [7, 37, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_Platform"], [8, 15, 2, 0], [8, 18, 2, 0, "require"], [8, 25, 2, 0], [8, 26, 2, 0, "_dependencyMap"], [8, 40, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_ReanimatedProxy"], [9, 22, 3, 0], [9, 25, 3, 0, "_interopRequireDefault"], [9, 47, 3, 0], [9, 48, 3, 0, "require"], [9, 55, 3, 0], [9, 56, 3, 0, "_dependencyMap"], [9, 70, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_useVideoLoading"], [10, 22, 4, 0], [10, 25, 4, 0, "require"], [10, 32, 4, 0], [10, 33, 4, 0, "_dependencyMap"], [10, 47, 4, 0], [11, 2, 4, 52], [11, 8, 4, 52, "_worklet_12211124293784_init_data"], [11, 41, 4, 52], [12, 4, 4, 52, "code"], [12, 8, 4, 52], [13, 4, 4, 52, "location"], [13, 12, 4, 52], [14, 4, 4, 52, "sourceMap"], [14, 13, 4, 52], [15, 4, 4, 52, "version"], [15, 11, 4, 52], [16, 2, 4, 52], [17, 2, 5, 0], [17, 8, 5, 6, "copyFrameOnAndroid"], [17, 26, 5, 24], [17, 29, 5, 27], [18, 4, 5, 27], [18, 10, 5, 27, "_e"], [18, 12, 5, 27], [18, 20, 5, 27, "global"], [18, 26, 5, 27], [18, 27, 5, 27, "Error"], [18, 32, 5, 27], [19, 4, 5, 27], [19, 10, 5, 27, "useVideoJs1"], [19, 21, 5, 27], [19, 33, 5, 27, "useVideoJs1"], [19, 34, 5, 27, "currentFrame"], [19, 46, 5, 39], [19, 48, 5, 43], [20, 6, 8, 2], [21, 6, 9, 2], [21, 10, 9, 6, "Platform"], [21, 28, 9, 14], [21, 29, 9, 15, "OS"], [21, 31, 9, 17], [21, 36, 9, 22], [21, 45, 9, 31], [21, 47, 9, 33], [22, 8, 10, 4], [22, 14, 10, 10, "tex"], [22, 17, 10, 13], [22, 20, 10, 16, "currentFrame"], [22, 32, 10, 28], [22, 33, 10, 29, "value"], [22, 38, 10, 34], [23, 8, 11, 4], [23, 12, 11, 8, "tex"], [23, 15, 11, 11], [23, 17, 11, 13], [24, 10, 12, 6, "currentFrame"], [24, 22, 12, 18], [24, 23, 12, 19, "value"], [24, 28, 12, 24], [24, 31, 12, 27, "tex"], [24, 34, 12, 30], [24, 35, 12, 31, "makeNonTextureImage"], [24, 54, 12, 50], [24, 55, 12, 51], [24, 56, 12, 52], [25, 10, 13, 6, "tex"], [25, 13, 13, 9], [25, 14, 13, 10, "dispose"], [25, 21, 13, 17], [25, 22, 13, 18], [25, 23, 13, 19], [26, 8, 14, 4], [27, 6, 15, 2], [28, 4, 16, 0], [28, 5, 16, 1], [29, 4, 16, 1, "useVideoJs1"], [29, 15, 16, 1], [29, 16, 16, 1, "__closure"], [29, 25, 16, 1], [30, 6, 16, 1, "Platform"], [30, 14, 16, 1], [30, 16, 9, 6, "Platform"], [31, 4, 9, 14], [32, 4, 9, 14, "useVideoJs1"], [32, 15, 9, 14], [32, 16, 9, 14, "__workletHash"], [32, 29, 9, 14], [33, 4, 9, 14, "useVideoJs1"], [33, 15, 9, 14], [33, 16, 9, 14, "__initData"], [33, 26, 9, 14], [33, 29, 9, 14, "_worklet_12211124293784_init_data"], [33, 62, 9, 14], [34, 4, 9, 14, "useVideoJs1"], [34, 15, 9, 14], [34, 16, 9, 14, "__stackDetails"], [34, 30, 9, 14], [34, 33, 9, 14, "_e"], [34, 35, 9, 14], [35, 4, 9, 14], [35, 11, 9, 14, "useVideoJs1"], [35, 22, 9, 14], [36, 2, 9, 14], [36, 3, 5, 27], [36, 5, 16, 1], [37, 2, 16, 2], [37, 8, 16, 2, "_worklet_14376721059946_init_data"], [37, 41, 16, 2], [38, 4, 16, 2, "code"], [38, 8, 16, 2], [39, 4, 16, 2, "location"], [39, 12, 16, 2], [40, 4, 16, 2, "sourceMap"], [40, 13, 16, 2], [41, 4, 16, 2, "version"], [41, 11, 16, 2], [42, 2, 16, 2], [43, 2, 17, 0], [43, 8, 17, 6, "set<PERSON>rame"], [43, 16, 17, 14], [43, 19, 17, 17], [44, 4, 17, 17], [44, 10, 17, 17, "_e"], [44, 12, 17, 17], [44, 20, 17, 17, "global"], [44, 26, 17, 17], [44, 27, 17, 17, "Error"], [44, 32, 17, 17], [45, 4, 17, 17], [45, 10, 17, 17, "useVideoJs2"], [45, 21, 17, 17], [45, 33, 17, 17, "useVideoJs2"], [45, 34, 17, 18, "video"], [45, 39, 17, 23], [45, 41, 17, 25, "currentFrame"], [45, 53, 17, 37], [45, 55, 17, 42], [46, 6, 20, 2], [46, 12, 20, 8, "img"], [46, 15, 20, 11], [46, 18, 20, 14, "video"], [46, 23, 20, 19], [46, 24, 20, 20, "nextImage"], [46, 33, 20, 29], [46, 34, 20, 30], [46, 35, 20, 31], [47, 6, 21, 2], [47, 10, 21, 6, "img"], [47, 13, 21, 9], [47, 15, 21, 11], [48, 8, 22, 4], [48, 12, 22, 8, "currentFrame"], [48, 24, 22, 20], [48, 25, 22, 21, "value"], [48, 30, 22, 26], [48, 32, 22, 28], [49, 10, 23, 6, "currentFrame"], [49, 22, 23, 18], [49, 23, 23, 19, "value"], [49, 28, 23, 24], [49, 29, 23, 25, "dispose"], [49, 36, 23, 32], [49, 37, 23, 33], [49, 38, 23, 34], [50, 8, 24, 4], [51, 8, 25, 4, "currentFrame"], [51, 20, 25, 16], [51, 21, 25, 17, "value"], [51, 26, 25, 22], [51, 29, 25, 25, "img"], [51, 32, 25, 28], [52, 8, 26, 4, "copyFrameOnAndroid"], [52, 26, 26, 22], [52, 27, 26, 23, "currentFrame"], [52, 39, 26, 35], [52, 40, 26, 36], [53, 6, 27, 2], [54, 4, 28, 0], [54, 5, 28, 1], [55, 4, 28, 1, "useVideoJs2"], [55, 15, 28, 1], [55, 16, 28, 1, "__closure"], [55, 25, 28, 1], [56, 6, 28, 1, "copyFrameOnAndroid"], [57, 4, 28, 1], [58, 4, 28, 1, "useVideoJs2"], [58, 15, 28, 1], [58, 16, 28, 1, "__workletHash"], [58, 29, 28, 1], [59, 4, 28, 1, "useVideoJs2"], [59, 15, 28, 1], [59, 16, 28, 1, "__initData"], [59, 26, 28, 1], [59, 29, 28, 1, "_worklet_14376721059946_init_data"], [59, 62, 28, 1], [60, 4, 28, 1, "useVideoJs2"], [60, 15, 28, 1], [60, 16, 28, 1, "__stackDetails"], [60, 30, 28, 1], [60, 33, 28, 1, "_e"], [60, 35, 28, 1], [61, 4, 28, 1], [61, 11, 28, 1, "useVideoJs2"], [61, 22, 28, 1], [62, 2, 28, 1], [62, 3, 17, 17], [62, 5, 28, 1], [63, 2, 29, 0], [63, 8, 29, 6, "defaultOptions"], [63, 22, 29, 20], [63, 25, 29, 23], [64, 4, 30, 2, "looping"], [64, 11, 30, 9], [64, 13, 30, 11], [64, 17, 30, 15], [65, 4, 31, 2, "paused"], [65, 10, 31, 8], [65, 12, 31, 10], [65, 17, 31, 15], [66, 4, 32, 2, "seek"], [66, 8, 32, 6], [66, 10, 32, 8], [66, 14, 32, 12], [67, 4, 33, 2, "currentTime"], [67, 15, 33, 13], [67, 17, 33, 15], [67, 18, 33, 16], [68, 4, 34, 2, "volume"], [68, 10, 34, 8], [68, 12, 34, 10], [69, 2, 35, 0], [69, 3, 35, 1], [70, 2, 35, 2], [70, 8, 35, 2, "_worklet_1683212240703_init_data"], [70, 40, 35, 2], [71, 4, 35, 2, "code"], [71, 8, 35, 2], [72, 4, 35, 2, "location"], [72, 12, 35, 2], [73, 4, 35, 2, "sourceMap"], [73, 13, 35, 2], [74, 4, 35, 2, "version"], [74, 11, 35, 2], [75, 2, 35, 2], [76, 2, 36, 0], [76, 8, 36, 6, "useOption"], [76, 17, 36, 15], [76, 20, 36, 18], [77, 4, 36, 18], [77, 10, 36, 18, "_e"], [77, 12, 36, 18], [77, 20, 36, 18, "global"], [77, 26, 36, 18], [77, 27, 36, 18, "Error"], [77, 32, 36, 18], [78, 4, 36, 18], [78, 10, 36, 18, "useVideoJs3"], [78, 21, 36, 18], [78, 33, 36, 18, "useVideoJs3"], [78, 34, 36, 18, "value"], [78, 39, 36, 23], [78, 41, 36, 27], [79, 6, 39, 2], [80, 6, 40, 2], [80, 12, 40, 8, "defaultValue"], [80, 24, 40, 20], [80, 27, 40, 23, "<PERSON><PERSON>"], [80, 51, 40, 26], [80, 52, 40, 27, "useSharedValue"], [80, 66, 40, 41], [80, 67, 40, 42, "<PERSON><PERSON>"], [80, 91, 40, 45], [80, 92, 40, 46, "isSharedValue"], [80, 105, 40, 59], [80, 106, 40, 60, "value"], [80, 111, 40, 65], [80, 112, 40, 66], [80, 115, 40, 69, "value"], [80, 120, 40, 74], [80, 121, 40, 75, "value"], [80, 126, 40, 80], [80, 129, 40, 83, "value"], [80, 134, 40, 88], [80, 135, 40, 89], [81, 6, 41, 2], [81, 13, 41, 9, "<PERSON><PERSON>"], [81, 37, 41, 12], [81, 38, 41, 13, "isSharedValue"], [81, 51, 41, 26], [81, 52, 41, 27, "value"], [81, 57, 41, 32], [81, 58, 41, 33], [81, 61, 41, 36, "value"], [81, 66, 41, 41], [81, 69, 41, 44, "defaultValue"], [81, 81, 41, 56], [82, 4, 42, 0], [82, 5, 42, 1], [83, 4, 42, 1, "useVideoJs3"], [83, 15, 42, 1], [83, 16, 42, 1, "__closure"], [83, 25, 42, 1], [84, 6, 42, 1, "<PERSON><PERSON>"], [84, 9, 42, 1], [84, 11, 40, 23, "<PERSON><PERSON>"], [85, 4, 40, 26], [86, 4, 40, 26, "useVideoJs3"], [86, 15, 40, 26], [86, 16, 40, 26, "__workletHash"], [86, 29, 40, 26], [87, 4, 40, 26, "useVideoJs3"], [87, 15, 40, 26], [87, 16, 40, 26, "__initData"], [87, 26, 40, 26], [87, 29, 40, 26, "_worklet_1683212240703_init_data"], [87, 61, 40, 26], [88, 4, 40, 26, "useVideoJs3"], [88, 15, 40, 26], [88, 16, 40, 26, "__stackDetails"], [88, 30, 40, 26], [88, 33, 40, 26, "_e"], [88, 35, 40, 26], [89, 4, 40, 26], [89, 11, 40, 26, "useVideoJs3"], [89, 22, 40, 26], [90, 2, 40, 26], [90, 3, 36, 18], [90, 5, 42, 1], [91, 2, 42, 2], [91, 8, 42, 2, "_worklet_16672310892193_init_data"], [91, 41, 42, 2], [92, 4, 42, 2, "code"], [92, 8, 42, 2], [93, 4, 42, 2, "location"], [93, 12, 42, 2], [94, 4, 42, 2, "sourceMap"], [94, 13, 42, 2], [95, 4, 42, 2, "version"], [95, 11, 42, 2], [96, 2, 42, 2], [97, 2, 43, 0], [97, 8, 43, 6, "disposeVideo"], [97, 20, 43, 18], [97, 23, 43, 21], [98, 4, 43, 21], [98, 10, 43, 21, "_e"], [98, 12, 43, 21], [98, 20, 43, 21, "global"], [98, 26, 43, 21], [98, 27, 43, 21, "Error"], [98, 32, 43, 21], [99, 4, 43, 21], [99, 10, 43, 21, "useVideoJs4"], [99, 21, 43, 21], [99, 33, 43, 21, "useVideoJs4"], [99, 34, 43, 21, "video"], [99, 39, 43, 26], [99, 41, 43, 30], [100, 6, 46, 2, "video"], [100, 11, 46, 7], [100, 16, 46, 12], [100, 20, 46, 16], [100, 24, 46, 20, "video"], [100, 29, 46, 25], [100, 34, 46, 30], [100, 39, 46, 35], [100, 40, 46, 36], [100, 44, 46, 40, "video"], [100, 49, 46, 45], [100, 50, 46, 46, "dispose"], [100, 57, 46, 53], [100, 58, 46, 54], [100, 59, 46, 55], [101, 4, 47, 0], [101, 5, 47, 1], [102, 4, 47, 1, "useVideoJs4"], [102, 15, 47, 1], [102, 16, 47, 1, "__closure"], [102, 25, 47, 1], [103, 4, 47, 1, "useVideoJs4"], [103, 15, 47, 1], [103, 16, 47, 1, "__workletHash"], [103, 29, 47, 1], [104, 4, 47, 1, "useVideoJs4"], [104, 15, 47, 1], [104, 16, 47, 1, "__initData"], [104, 26, 47, 1], [104, 29, 47, 1, "_worklet_16672310892193_init_data"], [104, 62, 47, 1], [105, 4, 47, 1, "useVideoJs4"], [105, 15, 47, 1], [105, 16, 47, 1, "__stackDetails"], [105, 30, 47, 1], [105, 33, 47, 1, "_e"], [105, 35, 47, 1], [106, 4, 47, 1], [106, 11, 47, 1, "useVideoJs4"], [106, 22, 47, 1], [107, 2, 47, 1], [107, 3, 43, 21], [107, 5, 47, 1], [108, 2, 47, 2], [108, 8, 47, 2, "_worklet_15834071128564_init_data"], [108, 41, 47, 2], [109, 4, 47, 2, "code"], [109, 8, 47, 2], [110, 4, 47, 2, "location"], [110, 12, 47, 2], [111, 4, 47, 2, "sourceMap"], [111, 13, 47, 2], [112, 4, 47, 2, "version"], [112, 11, 47, 2], [113, 2, 47, 2], [114, 2, 47, 2], [114, 8, 47, 2, "_worklet_10841070018278_init_data"], [114, 41, 47, 2], [115, 4, 47, 2, "code"], [115, 8, 47, 2], [116, 4, 47, 2, "location"], [116, 12, 47, 2], [117, 4, 47, 2, "sourceMap"], [117, 13, 47, 2], [118, 4, 47, 2, "version"], [118, 11, 47, 2], [119, 2, 47, 2], [120, 2, 47, 2], [120, 8, 47, 2, "_worklet_16080591650550_init_data"], [120, 41, 47, 2], [121, 4, 47, 2, "code"], [121, 8, 47, 2], [122, 4, 47, 2, "location"], [122, 12, 47, 2], [123, 4, 47, 2, "sourceMap"], [123, 13, 47, 2], [124, 4, 47, 2, "version"], [124, 11, 47, 2], [125, 2, 47, 2], [126, 2, 47, 2], [126, 8, 47, 2, "_worklet_10793983663447_init_data"], [126, 41, 47, 2], [127, 4, 47, 2, "code"], [127, 8, 47, 2], [128, 4, 47, 2, "location"], [128, 12, 47, 2], [129, 4, 47, 2, "sourceMap"], [129, 13, 47, 2], [130, 4, 47, 2, "version"], [130, 11, 47, 2], [131, 2, 47, 2], [132, 2, 47, 2], [132, 8, 47, 2, "_worklet_11821695268984_init_data"], [132, 41, 47, 2], [133, 4, 47, 2, "code"], [133, 8, 47, 2], [134, 4, 47, 2, "location"], [134, 12, 47, 2], [135, 4, 47, 2, "sourceMap"], [135, 13, 47, 2], [136, 4, 47, 2, "version"], [136, 11, 47, 2], [137, 2, 47, 2], [138, 2, 47, 2], [138, 8, 47, 2, "_worklet_6997409722965_init_data"], [138, 40, 47, 2], [139, 4, 47, 2, "code"], [139, 8, 47, 2], [140, 4, 47, 2, "location"], [140, 12, 47, 2], [141, 4, 47, 2, "sourceMap"], [141, 13, 47, 2], [142, 4, 47, 2, "version"], [142, 11, 47, 2], [143, 2, 47, 2], [144, 2, 47, 2], [144, 8, 47, 2, "_worklet_7146928661804_init_data"], [144, 40, 47, 2], [145, 4, 47, 2, "code"], [145, 8, 47, 2], [146, 4, 47, 2, "location"], [146, 12, 47, 2], [147, 4, 47, 2, "sourceMap"], [147, 13, 47, 2], [148, 4, 47, 2, "version"], [148, 11, 47, 2], [149, 2, 47, 2], [150, 2, 48, 7], [150, 8, 48, 13, "useVideo"], [150, 16, 48, 21], [150, 19, 48, 24, "useVideo"], [150, 20, 48, 25, "source"], [150, 26, 48, 31], [150, 28, 48, 33, "userOptions"], [150, 39, 48, 44], [150, 44, 48, 49], [151, 4, 49, 2], [151, 8, 49, 6, "_userOptions$paused"], [151, 27, 49, 25], [151, 29, 49, 27, "_userOptions$looping"], [151, 49, 49, 47], [151, 51, 49, 49, "_userOptions$seek"], [151, 68, 49, 66], [151, 70, 49, 68, "_userOptions$volume"], [151, 89, 49, 87], [152, 4, 50, 2], [152, 10, 50, 8, "video"], [152, 15, 50, 13], [152, 18, 50, 16], [152, 22, 50, 16, "useVideoLoading"], [152, 54, 50, 31], [152, 56, 50, 32, "source"], [152, 62, 50, 38], [152, 63, 50, 39], [153, 4, 51, 2], [153, 10, 51, 8, "isPaused"], [153, 18, 51, 16], [153, 21, 51, 19, "useOption"], [153, 30, 51, 28], [153, 31, 51, 29], [153, 32, 51, 30, "_userOptions$paused"], [153, 51, 51, 49], [153, 54, 51, 52, "userOptions"], [153, 65, 51, 63], [153, 70, 51, 68], [153, 74, 51, 72], [153, 78, 51, 76, "userOptions"], [153, 89, 51, 87], [153, 94, 51, 92], [153, 99, 51, 97], [153, 100, 51, 98], [153, 103, 51, 101], [153, 108, 51, 106], [153, 109, 51, 107], [153, 112, 51, 110, "userOptions"], [153, 123, 51, 121], [153, 124, 51, 122, "paused"], [153, 130, 51, 128], [153, 136, 51, 134], [153, 140, 51, 138], [153, 144, 51, 142, "_userOptions$paused"], [153, 163, 51, 161], [153, 168, 51, 166], [153, 173, 51, 171], [153, 174, 51, 172], [153, 177, 51, 175, "_userOptions$paused"], [153, 196, 51, 194], [153, 199, 51, 197, "defaultOptions"], [153, 213, 51, 211], [153, 214, 51, 212, "paused"], [153, 220, 51, 218], [153, 221, 51, 219], [154, 4, 52, 2], [154, 10, 52, 8, "looping"], [154, 17, 52, 15], [154, 20, 52, 18, "useOption"], [154, 29, 52, 27], [154, 30, 52, 28], [154, 31, 52, 29, "_userOptions$looping"], [154, 51, 52, 49], [154, 54, 52, 52, "userOptions"], [154, 65, 52, 63], [154, 70, 52, 68], [154, 74, 52, 72], [154, 78, 52, 76, "userOptions"], [154, 89, 52, 87], [154, 94, 52, 92], [154, 99, 52, 97], [154, 100, 52, 98], [154, 103, 52, 101], [154, 108, 52, 106], [154, 109, 52, 107], [154, 112, 52, 110, "userOptions"], [154, 123, 52, 121], [154, 124, 52, 122, "looping"], [154, 131, 52, 129], [154, 137, 52, 135], [154, 141, 52, 139], [154, 145, 52, 143, "_userOptions$looping"], [154, 165, 52, 163], [154, 170, 52, 168], [154, 175, 52, 173], [154, 176, 52, 174], [154, 179, 52, 177, "_userOptions$looping"], [154, 199, 52, 197], [154, 202, 52, 200, "defaultOptions"], [154, 216, 52, 214], [154, 217, 52, 215, "looping"], [154, 224, 52, 222], [154, 225, 52, 223], [155, 4, 53, 2], [155, 10, 53, 8, "seek"], [155, 14, 53, 12], [155, 17, 53, 15, "useOption"], [155, 26, 53, 24], [155, 27, 53, 25], [155, 28, 53, 26, "_userOptions$seek"], [155, 45, 53, 43], [155, 48, 53, 46, "userOptions"], [155, 59, 53, 57], [155, 64, 53, 62], [155, 68, 53, 66], [155, 72, 53, 70, "userOptions"], [155, 83, 53, 81], [155, 88, 53, 86], [155, 93, 53, 91], [155, 94, 53, 92], [155, 97, 53, 95], [155, 102, 53, 100], [155, 103, 53, 101], [155, 106, 53, 104, "userOptions"], [155, 117, 53, 115], [155, 118, 53, 116, "seek"], [155, 122, 53, 120], [155, 128, 53, 126], [155, 132, 53, 130], [155, 136, 53, 134, "_userOptions$seek"], [155, 153, 53, 151], [155, 158, 53, 156], [155, 163, 53, 161], [155, 164, 53, 162], [155, 167, 53, 165, "_userOptions$seek"], [155, 184, 53, 182], [155, 187, 53, 185, "defaultOptions"], [155, 201, 53, 199], [155, 202, 53, 200, "seek"], [155, 206, 53, 204], [155, 207, 53, 205], [156, 4, 54, 2], [156, 10, 54, 8, "volume"], [156, 16, 54, 14], [156, 19, 54, 17, "useOption"], [156, 28, 54, 26], [156, 29, 54, 27], [156, 30, 54, 28, "_userOptions$volume"], [156, 49, 54, 47], [156, 52, 54, 50, "userOptions"], [156, 63, 54, 61], [156, 68, 54, 66], [156, 72, 54, 70], [156, 76, 54, 74, "userOptions"], [156, 87, 54, 85], [156, 92, 54, 90], [156, 97, 54, 95], [156, 98, 54, 96], [156, 101, 54, 99], [156, 106, 54, 104], [156, 107, 54, 105], [156, 110, 54, 108, "userOptions"], [156, 121, 54, 119], [156, 122, 54, 120, "volume"], [156, 128, 54, 126], [156, 134, 54, 132], [156, 138, 54, 136], [156, 142, 54, 140, "_userOptions$volume"], [156, 161, 54, 159], [156, 166, 54, 164], [156, 171, 54, 169], [156, 172, 54, 170], [156, 175, 54, 173, "_userOptions$volume"], [156, 194, 54, 192], [156, 197, 54, 195, "defaultOptions"], [156, 211, 54, 209], [156, 212, 54, 210, "volume"], [156, 218, 54, 216], [156, 219, 54, 217], [157, 4, 55, 2], [157, 10, 55, 8, "currentFrame"], [157, 22, 55, 20], [157, 25, 55, 23, "<PERSON><PERSON>"], [157, 49, 55, 26], [157, 50, 55, 27, "useSharedValue"], [157, 64, 55, 41], [157, 65, 55, 42], [157, 69, 55, 46], [157, 70, 55, 47], [158, 4, 56, 2], [158, 10, 56, 8, "currentTime"], [158, 21, 56, 19], [158, 24, 56, 22, "<PERSON><PERSON>"], [158, 48, 56, 25], [158, 49, 56, 26, "useSharedValue"], [158, 63, 56, 40], [158, 64, 56, 41], [158, 65, 56, 42], [158, 66, 56, 43], [159, 4, 57, 2], [159, 10, 57, 8, "lastTimestamp"], [159, 23, 57, 21], [159, 26, 57, 24, "<PERSON><PERSON>"], [159, 50, 57, 27], [159, 51, 57, 28, "useSharedValue"], [159, 65, 57, 42], [159, 66, 57, 43], [159, 67, 57, 44], [159, 68, 57, 45], [159, 69, 57, 46], [160, 4, 58, 2], [160, 10, 58, 8, "duration"], [160, 18, 58, 16], [160, 21, 58, 19], [160, 25, 58, 19, "useMemo"], [160, 39, 58, 26], [160, 41, 58, 27], [160, 47, 58, 33], [161, 6, 59, 4], [161, 10, 59, 8, "_video$duration"], [161, 25, 59, 23], [162, 6, 60, 4], [162, 13, 60, 11], [162, 14, 60, 12, "_video$duration"], [162, 29, 60, 27], [162, 32, 60, 30, "video"], [162, 37, 60, 35], [162, 42, 60, 40], [162, 46, 60, 44], [162, 50, 60, 48, "video"], [162, 55, 60, 53], [162, 60, 60, 58], [162, 65, 60, 63], [162, 66, 60, 64], [162, 69, 60, 67], [162, 74, 60, 72], [162, 75, 60, 73], [162, 78, 60, 76, "video"], [162, 83, 60, 81], [162, 84, 60, 82, "duration"], [162, 92, 60, 90], [162, 93, 60, 91], [162, 94, 60, 92], [162, 100, 60, 98], [162, 104, 60, 102], [162, 108, 60, 106, "_video$duration"], [162, 123, 60, 121], [162, 128, 60, 126], [162, 133, 60, 131], [162, 134, 60, 132], [162, 137, 60, 135, "_video$duration"], [162, 152, 60, 150], [162, 155, 60, 153], [162, 156, 60, 154], [163, 4, 61, 2], [163, 5, 61, 3], [163, 7, 61, 5], [163, 8, 61, 6, "video"], [163, 13, 61, 11], [163, 14, 61, 12], [163, 15, 61, 13], [164, 4, 62, 2], [164, 10, 62, 8, "framerate"], [164, 19, 62, 17], [164, 22, 62, 20], [164, 26, 62, 20, "useMemo"], [164, 40, 62, 27], [164, 42, 62, 28], [164, 48, 62, 34], [165, 6, 63, 4], [165, 10, 63, 8, "_video$framerate"], [165, 26, 63, 24], [166, 6, 64, 4], [166, 13, 64, 11, "Platform"], [166, 31, 64, 19], [166, 32, 64, 20, "OS"], [166, 34, 64, 22], [166, 39, 64, 27], [166, 44, 64, 32], [166, 47, 64, 35], [166, 48, 64, 36], [166, 49, 64, 37], [166, 52, 64, 40], [166, 53, 64, 41, "_video$framerate"], [166, 69, 64, 57], [166, 72, 64, 60, "video"], [166, 77, 64, 65], [166, 82, 64, 70], [166, 86, 64, 74], [166, 90, 64, 78, "video"], [166, 95, 64, 83], [166, 100, 64, 88], [166, 105, 64, 93], [166, 106, 64, 94], [166, 109, 64, 97], [166, 114, 64, 102], [166, 115, 64, 103], [166, 118, 64, 106, "video"], [166, 123, 64, 111], [166, 124, 64, 112, "framerate"], [166, 133, 64, 121], [166, 134, 64, 122], [166, 135, 64, 123], [166, 141, 64, 129], [166, 145, 64, 133], [166, 149, 64, 137, "_video$framerate"], [166, 165, 64, 153], [166, 170, 64, 158], [166, 175, 64, 163], [166, 176, 64, 164], [166, 179, 64, 167, "_video$framerate"], [166, 195, 64, 183], [166, 198, 64, 186], [166, 199, 64, 187], [167, 4, 65, 2], [167, 5, 65, 3], [167, 7, 65, 5], [167, 8, 65, 6, "video"], [167, 13, 65, 11], [167, 14, 65, 12], [167, 15, 65, 13], [168, 4, 66, 2], [168, 10, 66, 8, "size"], [168, 14, 66, 12], [168, 17, 66, 15], [168, 21, 66, 15, "useMemo"], [168, 35, 66, 22], [168, 37, 66, 23], [168, 43, 66, 29], [169, 6, 67, 4], [169, 10, 67, 8, "_video$size"], [169, 21, 67, 19], [170, 6, 68, 4], [170, 13, 68, 11], [170, 14, 68, 12, "_video$size"], [170, 25, 68, 23], [170, 28, 68, 26, "video"], [170, 33, 68, 31], [170, 38, 68, 36], [170, 42, 68, 40], [170, 46, 68, 44, "video"], [170, 51, 68, 49], [170, 56, 68, 54], [170, 61, 68, 59], [170, 62, 68, 60], [170, 65, 68, 63], [170, 70, 68, 68], [170, 71, 68, 69], [170, 74, 68, 72, "video"], [170, 79, 68, 77], [170, 80, 68, 78, "size"], [170, 84, 68, 82], [170, 85, 68, 83], [170, 86, 68, 84], [170, 92, 68, 90], [170, 96, 68, 94], [170, 100, 68, 98, "_video$size"], [170, 111, 68, 109], [170, 116, 68, 114], [170, 121, 68, 119], [170, 122, 68, 120], [170, 125, 68, 123, "_video$size"], [170, 136, 68, 134], [170, 139, 68, 137], [171, 8, 69, 6, "width"], [171, 13, 69, 11], [171, 15, 69, 13], [171, 16, 69, 14], [172, 8, 70, 6, "height"], [172, 14, 70, 12], [172, 16, 70, 14], [173, 6, 71, 4], [173, 7, 71, 5], [174, 4, 72, 2], [174, 5, 72, 3], [174, 7, 72, 5], [174, 8, 72, 6, "video"], [174, 13, 72, 11], [174, 14, 72, 12], [174, 15, 72, 13], [175, 4, 73, 2], [175, 10, 73, 8, "rotation"], [175, 18, 73, 16], [175, 21, 73, 19], [175, 25, 73, 19, "useMemo"], [175, 39, 73, 26], [175, 41, 73, 27], [175, 47, 73, 33], [176, 6, 74, 4], [176, 10, 74, 8, "_video$rotation"], [176, 25, 74, 23], [177, 6, 75, 4], [177, 13, 75, 11], [177, 14, 75, 12, "_video$rotation"], [177, 29, 75, 27], [177, 32, 75, 30, "video"], [177, 37, 75, 35], [177, 42, 75, 40], [177, 46, 75, 44], [177, 50, 75, 48, "video"], [177, 55, 75, 53], [177, 60, 75, 58], [177, 65, 75, 63], [177, 66, 75, 64], [177, 69, 75, 67], [177, 74, 75, 72], [177, 75, 75, 73], [177, 78, 75, 76, "video"], [177, 83, 75, 81], [177, 84, 75, 82, "rotation"], [177, 92, 75, 90], [177, 93, 75, 91], [177, 94, 75, 92], [177, 100, 75, 98], [177, 104, 75, 102], [177, 108, 75, 106, "_video$rotation"], [177, 123, 75, 121], [177, 128, 75, 126], [177, 133, 75, 131], [177, 134, 75, 132], [177, 137, 75, 135, "_video$rotation"], [177, 152, 75, 150], [177, 155, 75, 153], [177, 156, 75, 154], [178, 4, 76, 2], [178, 5, 76, 3], [178, 7, 76, 5], [178, 8, 76, 6, "video"], [178, 13, 76, 11], [178, 14, 76, 12], [178, 15, 76, 13], [179, 4, 77, 2], [179, 10, 77, 8, "frameDuration"], [179, 23, 77, 21], [179, 26, 77, 24], [179, 30, 77, 28], [179, 33, 77, 31, "framerate"], [179, 42, 77, 40], [180, 4, 78, 2], [180, 10, 78, 8, "currentFrameDuration"], [180, 30, 78, 28], [180, 33, 78, 31, "Math"], [180, 37, 78, 35], [180, 38, 78, 36, "floor"], [180, 43, 78, 41], [180, 44, 78, 42, "frameDuration"], [180, 57, 78, 55], [180, 58, 78, 56], [181, 4, 79, 2, "<PERSON><PERSON>"], [181, 28, 79, 5], [181, 29, 79, 6, "useAnimatedReaction"], [181, 48, 79, 25], [181, 49, 79, 26], [182, 6, 79, 26], [182, 12, 79, 26, "_e"], [182, 14, 79, 26], [182, 22, 79, 26, "global"], [182, 28, 79, 26], [182, 29, 79, 26, "Error"], [182, 34, 79, 26], [183, 6, 79, 26], [183, 12, 79, 26, "useVideoJs5"], [183, 23, 79, 26], [183, 26, 79, 26, "useVideoJs5"], [183, 27, 79, 26], [183, 32, 79, 32, "isPaused"], [183, 40, 79, 40], [183, 41, 79, 41, "value"], [183, 46, 79, 46], [184, 6, 79, 46, "useVideoJs5"], [184, 17, 79, 46], [184, 18, 79, 46, "__closure"], [184, 27, 79, 46], [185, 8, 79, 46, "isPaused"], [186, 6, 79, 46], [187, 6, 79, 46, "useVideoJs5"], [187, 17, 79, 46], [187, 18, 79, 46, "__workletHash"], [187, 31, 79, 46], [188, 6, 79, 46, "useVideoJs5"], [188, 17, 79, 46], [188, 18, 79, 46, "__initData"], [188, 28, 79, 46], [188, 31, 79, 46, "_worklet_15834071128564_init_data"], [188, 64, 79, 46], [189, 6, 79, 46, "useVideoJs5"], [189, 17, 79, 46], [189, 18, 79, 46, "__stackDetails"], [189, 32, 79, 46], [189, 35, 79, 46, "_e"], [189, 37, 79, 46], [190, 6, 79, 46], [190, 13, 79, 46, "useVideoJs5"], [190, 24, 79, 46], [191, 4, 79, 46], [191, 5, 79, 26], [191, 9, 79, 48], [192, 6, 79, 48], [192, 12, 79, 48, "_e"], [192, 14, 79, 48], [192, 22, 79, 48, "global"], [192, 28, 79, 48], [192, 29, 79, 48, "Error"], [192, 34, 79, 48], [193, 6, 79, 48], [193, 12, 79, 48, "useVideoJs6"], [193, 23, 79, 48], [193, 35, 79, 48, "useVideoJs6"], [193, 36, 79, 48, "paused"], [193, 42, 79, 54], [193, 44, 79, 58], [194, 8, 80, 4], [194, 12, 80, 8, "paused"], [194, 18, 80, 14], [194, 20, 80, 16], [195, 10, 81, 6, "video"], [195, 15, 81, 11], [195, 20, 81, 16], [195, 24, 81, 20], [195, 28, 81, 24, "video"], [195, 33, 81, 29], [195, 38, 81, 34], [195, 43, 81, 39], [195, 44, 81, 40], [195, 48, 81, 44, "video"], [195, 53, 81, 49], [195, 54, 81, 50, "pause"], [195, 59, 81, 55], [195, 60, 81, 56], [195, 61, 81, 57], [196, 8, 82, 4], [196, 9, 82, 5], [196, 15, 82, 11], [197, 10, 83, 6, "lastTimestamp"], [197, 23, 83, 19], [197, 24, 83, 20, "value"], [197, 29, 83, 25], [197, 32, 83, 28], [197, 33, 83, 29], [197, 34, 83, 30], [198, 10, 84, 6, "video"], [198, 15, 84, 11], [198, 20, 84, 16], [198, 24, 84, 20], [198, 28, 84, 24, "video"], [198, 33, 84, 29], [198, 38, 84, 34], [198, 43, 84, 39], [198, 44, 84, 40], [198, 48, 84, 44, "video"], [198, 53, 84, 49], [198, 54, 84, 50, "play"], [198, 58, 84, 54], [198, 59, 84, 55], [198, 60, 84, 56], [199, 8, 85, 4], [200, 6, 86, 2], [200, 7, 86, 3], [201, 6, 86, 3, "useVideoJs6"], [201, 17, 86, 3], [201, 18, 86, 3, "__closure"], [201, 27, 86, 3], [202, 8, 86, 3, "video"], [202, 13, 86, 3], [203, 8, 86, 3, "lastTimestamp"], [204, 6, 86, 3], [205, 6, 86, 3, "useVideoJs6"], [205, 17, 86, 3], [205, 18, 86, 3, "__workletHash"], [205, 31, 86, 3], [206, 6, 86, 3, "useVideoJs6"], [206, 17, 86, 3], [206, 18, 86, 3, "__initData"], [206, 28, 86, 3], [206, 31, 86, 3, "_worklet_10841070018278_init_data"], [206, 64, 86, 3], [207, 6, 86, 3, "useVideoJs6"], [207, 17, 86, 3], [207, 18, 86, 3, "__stackDetails"], [207, 32, 86, 3], [207, 35, 86, 3, "_e"], [207, 37, 86, 3], [208, 6, 86, 3], [208, 13, 86, 3, "useVideoJs6"], [208, 24, 86, 3], [209, 4, 86, 3], [209, 5, 79, 48], [209, 7, 86, 3], [209, 8, 86, 4], [210, 4, 87, 2, "<PERSON><PERSON>"], [210, 28, 87, 5], [210, 29, 87, 6, "useAnimatedReaction"], [210, 48, 87, 25], [210, 49, 87, 26], [211, 6, 87, 26], [211, 12, 87, 26, "_e"], [211, 14, 87, 26], [211, 22, 87, 26, "global"], [211, 28, 87, 26], [211, 29, 87, 26, "Error"], [211, 34, 87, 26], [212, 6, 87, 26], [212, 12, 87, 26, "useVideoJs7"], [212, 23, 87, 26], [212, 26, 87, 26, "useVideoJs7"], [212, 27, 87, 26], [212, 32, 87, 32, "seek"], [212, 36, 87, 36], [212, 37, 87, 37, "value"], [212, 42, 87, 42], [213, 6, 87, 42, "useVideoJs7"], [213, 17, 87, 42], [213, 18, 87, 42, "__closure"], [213, 27, 87, 42], [214, 8, 87, 42, "seek"], [215, 6, 87, 42], [216, 6, 87, 42, "useVideoJs7"], [216, 17, 87, 42], [216, 18, 87, 42, "__workletHash"], [216, 31, 87, 42], [217, 6, 87, 42, "useVideoJs7"], [217, 17, 87, 42], [217, 18, 87, 42, "__initData"], [217, 28, 87, 42], [217, 31, 87, 42, "_worklet_16080591650550_init_data"], [217, 64, 87, 42], [218, 6, 87, 42, "useVideoJs7"], [218, 17, 87, 42], [218, 18, 87, 42, "__stackDetails"], [218, 32, 87, 42], [218, 35, 87, 42, "_e"], [218, 37, 87, 42], [219, 6, 87, 42], [219, 13, 87, 42, "useVideoJs7"], [219, 24, 87, 42], [220, 4, 87, 42], [220, 5, 87, 26], [220, 9, 87, 44], [221, 6, 87, 44], [221, 12, 87, 44, "_e"], [221, 14, 87, 44], [221, 22, 87, 44, "global"], [221, 28, 87, 44], [221, 29, 87, 44, "Error"], [221, 34, 87, 44], [222, 6, 87, 44], [222, 12, 87, 44, "useVideoJs8"], [222, 23, 87, 44], [222, 35, 87, 44, "useVideoJs8"], [222, 36, 87, 44, "value"], [222, 41, 87, 49], [222, 43, 87, 53], [223, 8, 88, 4], [223, 12, 88, 8, "value"], [223, 17, 88, 13], [223, 22, 88, 18], [223, 26, 88, 22], [223, 28, 88, 24], [224, 10, 89, 6, "video"], [224, 15, 89, 11], [224, 20, 89, 16], [224, 24, 89, 20], [224, 28, 89, 24, "video"], [224, 33, 89, 29], [224, 38, 89, 34], [224, 43, 89, 39], [224, 44, 89, 40], [224, 48, 89, 44, "video"], [224, 53, 89, 49], [224, 54, 89, 50, "seek"], [224, 58, 89, 54], [224, 59, 89, 55, "value"], [224, 64, 89, 60], [224, 65, 89, 61], [225, 10, 90, 6, "currentTime"], [225, 21, 90, 17], [225, 22, 90, 18, "value"], [225, 27, 90, 23], [225, 30, 90, 26, "value"], [225, 35, 90, 31], [226, 10, 91, 6, "seek"], [226, 14, 91, 10], [226, 15, 91, 11, "value"], [226, 20, 91, 16], [226, 23, 91, 19], [226, 27, 91, 23], [227, 8, 92, 4], [228, 6, 93, 2], [228, 7, 93, 3], [229, 6, 93, 3, "useVideoJs8"], [229, 17, 93, 3], [229, 18, 93, 3, "__closure"], [229, 27, 93, 3], [230, 8, 93, 3, "video"], [230, 13, 93, 3], [231, 8, 93, 3, "currentTime"], [231, 19, 93, 3], [232, 8, 93, 3, "seek"], [233, 6, 93, 3], [234, 6, 93, 3, "useVideoJs8"], [234, 17, 93, 3], [234, 18, 93, 3, "__workletHash"], [234, 31, 93, 3], [235, 6, 93, 3, "useVideoJs8"], [235, 17, 93, 3], [235, 18, 93, 3, "__initData"], [235, 28, 93, 3], [235, 31, 93, 3, "_worklet_10793983663447_init_data"], [235, 64, 93, 3], [236, 6, 93, 3, "useVideoJs8"], [236, 17, 93, 3], [236, 18, 93, 3, "__stackDetails"], [236, 32, 93, 3], [236, 35, 93, 3, "_e"], [236, 37, 93, 3], [237, 6, 93, 3], [237, 13, 93, 3, "useVideoJs8"], [237, 24, 93, 3], [238, 4, 93, 3], [238, 5, 87, 44], [238, 7, 93, 3], [238, 8, 93, 4], [239, 4, 94, 2, "<PERSON><PERSON>"], [239, 28, 94, 5], [239, 29, 94, 6, "useAnimatedReaction"], [239, 48, 94, 25], [239, 49, 94, 26], [240, 6, 94, 26], [240, 12, 94, 26, "_e"], [240, 14, 94, 26], [240, 22, 94, 26, "global"], [240, 28, 94, 26], [240, 29, 94, 26, "Error"], [240, 34, 94, 26], [241, 6, 94, 26], [241, 12, 94, 26, "useVideoJs9"], [241, 23, 94, 26], [241, 26, 94, 26, "useVideoJs9"], [241, 27, 94, 26], [241, 32, 94, 32, "volume"], [241, 38, 94, 38], [241, 39, 94, 39, "value"], [241, 44, 94, 44], [242, 6, 94, 44, "useVideoJs9"], [242, 17, 94, 44], [242, 18, 94, 44, "__closure"], [242, 27, 94, 44], [243, 8, 94, 44, "volume"], [244, 6, 94, 44], [245, 6, 94, 44, "useVideoJs9"], [245, 17, 94, 44], [245, 18, 94, 44, "__workletHash"], [245, 31, 94, 44], [246, 6, 94, 44, "useVideoJs9"], [246, 17, 94, 44], [246, 18, 94, 44, "__initData"], [246, 28, 94, 44], [246, 31, 94, 44, "_worklet_11821695268984_init_data"], [246, 64, 94, 44], [247, 6, 94, 44, "useVideoJs9"], [247, 17, 94, 44], [247, 18, 94, 44, "__stackDetails"], [247, 32, 94, 44], [247, 35, 94, 44, "_e"], [247, 37, 94, 44], [248, 6, 94, 44], [248, 13, 94, 44, "useVideoJs9"], [248, 24, 94, 44], [249, 4, 94, 44], [249, 5, 94, 26], [249, 9, 94, 46], [250, 6, 94, 46], [250, 12, 94, 46, "_e"], [250, 14, 94, 46], [250, 22, 94, 46, "global"], [250, 28, 94, 46], [250, 29, 94, 46, "Error"], [250, 34, 94, 46], [251, 6, 94, 46], [251, 12, 94, 46, "useVideoJs10"], [251, 24, 94, 46], [251, 36, 94, 46, "useVideoJs10"], [251, 37, 94, 46, "value"], [251, 42, 94, 51], [251, 44, 94, 55], [252, 8, 95, 4, "video"], [252, 13, 95, 9], [252, 18, 95, 14], [252, 22, 95, 18], [252, 26, 95, 22, "video"], [252, 31, 95, 27], [252, 36, 95, 32], [252, 41, 95, 37], [252, 42, 95, 38], [252, 46, 95, 42, "video"], [252, 51, 95, 47], [252, 52, 95, 48, "setVolume"], [252, 61, 95, 57], [252, 62, 95, 58, "value"], [252, 67, 95, 63], [252, 68, 95, 64], [253, 6, 96, 2], [253, 7, 96, 3], [254, 6, 96, 3, "useVideoJs10"], [254, 18, 96, 3], [254, 19, 96, 3, "__closure"], [254, 28, 96, 3], [255, 8, 96, 3, "video"], [256, 6, 96, 3], [257, 6, 96, 3, "useVideoJs10"], [257, 18, 96, 3], [257, 19, 96, 3, "__workletHash"], [257, 32, 96, 3], [258, 6, 96, 3, "useVideoJs10"], [258, 18, 96, 3], [258, 19, 96, 3, "__initData"], [258, 29, 96, 3], [258, 32, 96, 3, "_worklet_6997409722965_init_data"], [258, 64, 96, 3], [259, 6, 96, 3, "useVideoJs10"], [259, 18, 96, 3], [259, 19, 96, 3, "__stackDetails"], [259, 33, 96, 3], [259, 36, 96, 3, "_e"], [259, 38, 96, 3], [260, 6, 96, 3], [260, 13, 96, 3, "useVideoJs10"], [260, 25, 96, 3], [261, 4, 96, 3], [261, 5, 94, 46], [261, 7, 96, 3], [261, 8, 96, 4], [262, 4, 97, 2, "<PERSON><PERSON>"], [262, 28, 97, 5], [262, 29, 97, 6, "useFrameCallback"], [262, 45, 97, 22], [262, 46, 97, 23], [263, 6, 97, 23], [263, 12, 97, 23, "_e"], [263, 14, 97, 23], [263, 22, 97, 23, "global"], [263, 28, 97, 23], [263, 29, 97, 23, "Error"], [263, 34, 97, 23], [264, 6, 97, 23], [264, 12, 97, 23, "useVideoJs11"], [264, 24, 97, 23], [264, 36, 97, 23, "useVideoJs11"], [264, 37, 97, 23, "frameInfo"], [264, 46, 97, 32], [264, 48, 97, 36], [265, 8, 100, 4], [265, 12, 100, 8], [265, 13, 100, 9, "video"], [265, 18, 100, 14], [265, 20, 100, 16], [266, 10, 101, 6], [267, 8, 102, 4], [268, 8, 103, 4], [268, 12, 103, 8, "isPaused"], [268, 20, 103, 16], [268, 21, 103, 17, "value"], [268, 26, 103, 22], [268, 28, 103, 24], [269, 10, 104, 6], [270, 8, 105, 4], [271, 8, 106, 4], [271, 14, 106, 10, "currentTimestamp"], [271, 30, 106, 26], [271, 33, 106, 29, "frameInfo"], [271, 42, 106, 38], [271, 43, 106, 39, "timestamp"], [271, 52, 106, 48], [272, 8, 107, 4], [272, 12, 107, 8, "lastTimestamp"], [272, 25, 107, 21], [272, 26, 107, 22, "value"], [272, 31, 107, 27], [272, 36, 107, 32], [272, 37, 107, 33], [272, 38, 107, 34], [272, 40, 107, 36], [273, 10, 108, 6, "lastTimestamp"], [273, 23, 108, 19], [273, 24, 108, 20, "value"], [273, 29, 108, 25], [273, 32, 108, 28, "currentTimestamp"], [273, 48, 108, 44], [274, 8, 109, 4], [275, 8, 110, 4], [275, 14, 110, 10, "delta"], [275, 19, 110, 15], [275, 22, 110, 18, "currentTimestamp"], [275, 38, 110, 34], [275, 41, 110, 37, "lastTimestamp"], [275, 54, 110, 50], [275, 55, 110, 51, "value"], [275, 60, 110, 56], [276, 8, 111, 4], [276, 14, 111, 10, "isOver"], [276, 20, 111, 16], [276, 23, 111, 19, "currentTime"], [276, 34, 111, 30], [276, 35, 111, 31, "value"], [276, 40, 111, 36], [276, 43, 111, 39, "delta"], [276, 48, 111, 44], [276, 51, 111, 47, "duration"], [276, 59, 111, 55], [277, 8, 112, 4], [277, 12, 112, 8, "isOver"], [277, 18, 112, 14], [277, 22, 112, 18, "looping"], [277, 29, 112, 25], [277, 30, 112, 26, "value"], [277, 35, 112, 31], [277, 37, 112, 33], [278, 10, 113, 6, "seek"], [278, 14, 113, 10], [278, 15, 113, 11, "value"], [278, 20, 113, 16], [278, 23, 113, 19], [278, 24, 113, 20], [279, 10, 114, 6, "currentTime"], [279, 21, 114, 17], [279, 22, 114, 18, "value"], [279, 27, 114, 23], [279, 30, 114, 26, "seek"], [279, 34, 114, 30], [279, 35, 114, 31, "value"], [279, 40, 114, 36], [280, 10, 115, 6, "lastTimestamp"], [280, 23, 115, 19], [280, 24, 115, 20, "value"], [280, 29, 115, 25], [280, 32, 115, 28, "currentTimestamp"], [280, 48, 115, 44], [281, 8, 116, 4], [282, 8, 117, 4], [283, 8, 118, 4], [284, 8, 119, 4], [284, 12, 119, 8, "delta"], [284, 17, 119, 13], [284, 21, 119, 17, "currentFrameDuration"], [284, 41, 119, 37], [284, 45, 119, 41], [284, 46, 119, 42, "isOver"], [284, 52, 119, 48], [284, 56, 119, 52, "Platform"], [284, 74, 119, 60], [284, 75, 119, 61, "OS"], [284, 77, 119, 63], [284, 82, 119, 68], [284, 87, 119, 73], [284, 89, 119, 75], [285, 10, 120, 6, "set<PERSON>rame"], [285, 18, 120, 14], [285, 19, 120, 15, "video"], [285, 24, 120, 20], [285, 26, 120, 22, "currentFrame"], [285, 38, 120, 34], [285, 39, 120, 35], [286, 10, 121, 6, "currentTime"], [286, 21, 121, 17], [286, 22, 121, 18, "value"], [286, 27, 121, 23], [286, 31, 121, 27, "delta"], [286, 36, 121, 32], [287, 10, 122, 6, "lastTimestamp"], [287, 23, 122, 19], [287, 24, 122, 20, "value"], [287, 29, 122, 25], [287, 32, 122, 28, "currentTimestamp"], [287, 48, 122, 44], [288, 8, 123, 4], [289, 6, 124, 2], [289, 7, 124, 3], [290, 6, 124, 3, "useVideoJs11"], [290, 18, 124, 3], [290, 19, 124, 3, "__closure"], [290, 28, 124, 3], [291, 8, 124, 3, "video"], [291, 13, 124, 3], [292, 8, 124, 3, "isPaused"], [292, 16, 124, 3], [293, 8, 124, 3, "lastTimestamp"], [293, 21, 124, 3], [294, 8, 124, 3, "currentTime"], [294, 19, 124, 3], [295, 8, 124, 3, "duration"], [295, 16, 124, 3], [296, 8, 124, 3, "looping"], [296, 15, 124, 3], [297, 8, 124, 3, "seek"], [297, 12, 124, 3], [298, 8, 124, 3, "currentFrameDuration"], [298, 28, 124, 3], [299, 8, 124, 3, "Platform"], [299, 16, 124, 3], [299, 18, 119, 52, "Platform"], [299, 36, 119, 60], [300, 8, 119, 60, "set<PERSON>rame"], [300, 16, 119, 60], [301, 8, 119, 60, "currentFrame"], [302, 6, 119, 60], [303, 6, 119, 60, "useVideoJs11"], [303, 18, 119, 60], [303, 19, 119, 60, "__workletHash"], [303, 32, 119, 60], [304, 6, 119, 60, "useVideoJs11"], [304, 18, 119, 60], [304, 19, 119, 60, "__initData"], [304, 29, 119, 60], [304, 32, 119, 60, "_worklet_7146928661804_init_data"], [304, 64, 119, 60], [305, 6, 119, 60, "useVideoJs11"], [305, 18, 119, 60], [305, 19, 119, 60, "__stackDetails"], [305, 33, 119, 60], [305, 36, 119, 60, "_e"], [305, 38, 119, 60], [306, 6, 119, 60], [306, 13, 119, 60, "useVideoJs11"], [306, 25, 119, 60], [307, 4, 119, 60], [307, 5, 97, 23], [307, 7, 124, 3], [307, 8, 124, 4], [308, 4, 125, 2], [308, 8, 125, 2, "useEffect"], [308, 24, 125, 11], [308, 26, 125, 12], [308, 32, 125, 18], [309, 6, 126, 4], [309, 13, 126, 11], [309, 19, 126, 17], [310, 8, 127, 6], [311, 8, 128, 6, "<PERSON><PERSON>"], [311, 32, 128, 9], [311, 33, 128, 10, "runOnUI"], [311, 40, 128, 17], [311, 41, 128, 18, "disposeVideo"], [311, 53, 128, 30], [311, 54, 128, 31], [311, 55, 128, 32, "video"], [311, 60, 128, 37], [311, 61, 128, 38], [312, 6, 129, 4], [312, 7, 129, 5], [313, 4, 130, 2], [313, 5, 130, 3], [313, 7, 130, 5], [313, 8, 130, 6, "video"], [313, 13, 130, 11], [313, 14, 130, 12], [313, 15, 130, 13], [314, 4, 131, 2], [314, 11, 131, 9], [315, 6, 132, 4, "currentFrame"], [315, 18, 132, 16], [316, 6, 133, 4, "currentTime"], [316, 17, 133, 15], [317, 6, 134, 4, "duration"], [317, 14, 134, 12], [318, 6, 135, 4, "framerate"], [318, 15, 135, 13], [319, 6, 136, 4, "rotation"], [319, 14, 136, 12], [320, 6, 137, 4, "size"], [321, 4, 138, 2], [321, 5, 138, 3], [322, 2, 139, 0], [322, 3, 139, 1], [323, 2, 139, 2, "exports"], [323, 9, 139, 2], [323, 10, 139, 2, "useVideo"], [323, 18, 139, 2], [323, 21, 139, 2, "useVideo"], [323, 29, 139, 2], [324, 0, 139, 2], [324, 3]], "functionMap": {"names": ["<global>", "copyFrameOnAndroid", "set<PERSON>rame", "useOption", "disposeVideo", "useVideo", "useMemo$argument_0", "Rea.useAnimatedReaction$argument_0", "Rea.useAnimatedReaction$argument_1", "Rea.useFrameCallback$argument_0", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;2BCI;CDW;iBEC;CFW;kBGQ;CHM;qBIC;CJI;wBKC;2BCU;GDG;4BCC;GDG;uBCC;GDM;2BCC;GDG;0BEG,oBF,EG;GHO;0BEC,gBF,EG;GHM;0BEC,kBF,EG;GHE;uBIC;GJ2B;YKC;WCC;KDG;GLC;CLS"}}, "type": "js/module"}]}