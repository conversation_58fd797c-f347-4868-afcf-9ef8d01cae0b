{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 53, "index": 53}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../renderer/Offscreen", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 54}, "end": {"line": 2, "column": 81, "index": 135}}], "key": "KdT5Yxc0ye6AbkgBqacWSBftbgk=", "exportNames": ["*"]}}, {"name": "../../skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 136}, "end": {"line": 3, "column": 44, "index": 180}}], "key": "+q0qwmVtgReRJ1JJKJleyyIYxCs=", "exportNames": ["*"]}}, {"name": "./ReanimatedProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 181}, "end": {"line": 4, "column": 36, "index": 217}}], "key": "9/hMKtQD5ZrdSt9i8tblq1v6X3Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useTexture = exports.usePictureAsTexture = exports.useImageAsTexture = void 0;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _Offscreen = require(_dependencyMap[2], \"../../renderer/Offscreen\");\n  var _skia = require(_dependencyMap[3], \"../../skia\");\n  var _ReanimatedProxy = _interopRequireDefault(require(_dependencyMap[4], \"./ReanimatedProxy\"));\n  const _worklet_11149833792852_init_data = {\n    code: \"function texturesJs1(texture,picture,size){const{drawAsImageFromPicture}=this.__closure;texture.value=drawAsImageFromPicture(picture,size);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\textures.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"texturesJs1\\\",\\\"texture\\\",\\\"picture\\\",\\\"size\\\",\\\"drawAsImageFromPicture\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/textures.js\\\"],\\\"mappings\\\":\\\"AAIsB,QAAC,CAAAA,WAASA,CAAAC,OAAO,CAAEC,OAAS,CAAAC,IAAA,QAAAC,sBAAA,OAAAC,SAAA,CAGhDJ,OAAO,CAACK,KAAK,CAAGF,sBAAsB,CAACF,OAAO,CAAEC,IAAI,CAAC,CACvD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const createTexture = function () {\n    const _e = [new global.Error(), -2, -27];\n    const texturesJs1 = function (texture, picture, size) {\n      texture.value = (0, _Offscreen.drawAsImageFromPicture)(picture, size);\n    };\n    texturesJs1.__closure = {\n      drawAsImageFromPicture: _Offscreen.drawAsImageFromPicture\n    };\n    texturesJs1.__workletHash = 11149833792852;\n    texturesJs1.__initData = _worklet_11149833792852_init_data;\n    texturesJs1.__stackDetails = _e;\n    return texturesJs1;\n  }();\n  const useTexture = (element, size, deps) => {\n    const {\n      width,\n      height\n    } = size;\n    const [picture, setPicture] = (0, _react.useState)(null);\n    (0, _react.useEffect)(() => {\n      (0, _Offscreen.drawAsPicture)(element, {\n        x: 0,\n        y: 0,\n        width,\n        height\n      }).then(pic => {\n        setPicture(pic);\n      });\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, deps !== null && deps !== void 0 ? deps : []);\n    return usePictureAsTexture(picture, size);\n  };\n  exports.useTexture = useTexture;\n  const usePictureAsTexture = (picture, size) => {\n    const texture = _ReanimatedProxy.default.useSharedValue(null);\n    (0, _react.useEffect)(() => {\n      if (picture !== null) {\n        _ReanimatedProxy.default.runOnUI(createTexture)(texture, picture, size);\n      }\n    }, [picture, size, texture]);\n    return texture;\n  };\n  exports.usePictureAsTexture = usePictureAsTexture;\n  const useImageAsTexture = source => {\n    const image = (0, _skia.useImage)(source);\n    const size = (0, _react.useMemo)(() => {\n      if (image) {\n        return {\n          width: image.width(),\n          height: image.height()\n        };\n      }\n      return {\n        width: 0,\n        height: 0\n      };\n    }, [image]);\n    const picture = (0, _react.useMemo)(() => {\n      if (image) {\n        const recorder = _skia.Skia.PictureRecorder();\n        const canvas = recorder.beginRecording({\n          x: 0,\n          y: 0,\n          width: size.width,\n          height: size.height\n        });\n        canvas.drawImage(image, 0, 0);\n        return recorder.finishRecordingAsPicture();\n      } else {\n        return null;\n      }\n    }, [size, image]);\n    return usePictureAsTexture(picture, size);\n  };\n  exports.useImageAsTexture = useImageAsTexture;\n});", "lineCount": 92, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "require"], [7, 22, 1, 0], [7, 23, 1, 0, "_dependencyMap"], [7, 37, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_Offscreen"], [8, 16, 2, 0], [8, 19, 2, 0, "require"], [8, 26, 2, 0], [8, 27, 2, 0, "_dependencyMap"], [8, 41, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_skia"], [9, 11, 3, 0], [9, 14, 3, 0, "require"], [9, 21, 3, 0], [9, 22, 3, 0, "_dependencyMap"], [9, 36, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_ReanimatedProxy"], [10, 22, 4, 0], [10, 25, 4, 0, "_interopRequireDefault"], [10, 47, 4, 0], [10, 48, 4, 0, "require"], [10, 55, 4, 0], [10, 56, 4, 0, "_dependencyMap"], [10, 70, 4, 0], [11, 2, 4, 36], [11, 8, 4, 36, "_worklet_11149833792852_init_data"], [11, 41, 4, 36], [12, 4, 4, 36, "code"], [12, 8, 4, 36], [13, 4, 4, 36, "location"], [13, 12, 4, 36], [14, 4, 4, 36, "sourceMap"], [14, 13, 4, 36], [15, 4, 4, 36, "version"], [15, 11, 4, 36], [16, 2, 4, 36], [17, 2, 5, 0], [17, 8, 5, 6, "createTexture"], [17, 21, 5, 19], [17, 24, 5, 22], [18, 4, 5, 22], [18, 10, 5, 22, "_e"], [18, 12, 5, 22], [18, 20, 5, 22, "global"], [18, 26, 5, 22], [18, 27, 5, 22, "Error"], [18, 32, 5, 22], [19, 4, 5, 22], [19, 10, 5, 22, "texturesJs1"], [19, 21, 5, 22], [19, 33, 5, 22, "texturesJs1"], [19, 34, 5, 23, "texture"], [19, 41, 5, 30], [19, 43, 5, 32, "picture"], [19, 50, 5, 39], [19, 52, 5, 41, "size"], [19, 56, 5, 45], [19, 58, 5, 50], [20, 6, 8, 2, "texture"], [20, 13, 8, 9], [20, 14, 8, 10, "value"], [20, 19, 8, 15], [20, 22, 8, 18], [20, 26, 8, 18, "drawAsImageFromPicture"], [20, 59, 8, 40], [20, 61, 8, 41, "picture"], [20, 68, 8, 48], [20, 70, 8, 50, "size"], [20, 74, 8, 54], [20, 75, 8, 55], [21, 4, 9, 0], [21, 5, 9, 1], [22, 4, 9, 1, "texturesJs1"], [22, 15, 9, 1], [22, 16, 9, 1, "__closure"], [22, 25, 9, 1], [23, 6, 9, 1, "drawAsImageFromPicture"], [23, 28, 9, 1], [23, 30, 8, 18, "drawAsImageFromPicture"], [24, 4, 8, 40], [25, 4, 8, 40, "texturesJs1"], [25, 15, 8, 40], [25, 16, 8, 40, "__workletHash"], [25, 29, 8, 40], [26, 4, 8, 40, "texturesJs1"], [26, 15, 8, 40], [26, 16, 8, 40, "__initData"], [26, 26, 8, 40], [26, 29, 8, 40, "_worklet_11149833792852_init_data"], [26, 62, 8, 40], [27, 4, 8, 40, "texturesJs1"], [27, 15, 8, 40], [27, 16, 8, 40, "__stackDetails"], [27, 30, 8, 40], [27, 33, 8, 40, "_e"], [27, 35, 8, 40], [28, 4, 8, 40], [28, 11, 8, 40, "texturesJs1"], [28, 22, 8, 40], [29, 2, 8, 40], [29, 3, 5, 22], [29, 5, 9, 1], [30, 2, 10, 7], [30, 8, 10, 13, "useTexture"], [30, 18, 10, 23], [30, 21, 10, 26, "useTexture"], [30, 22, 10, 27, "element"], [30, 29, 10, 34], [30, 31, 10, 36, "size"], [30, 35, 10, 40], [30, 37, 10, 42, "deps"], [30, 41, 10, 46], [30, 46, 10, 51], [31, 4, 11, 2], [31, 10, 11, 8], [32, 6, 12, 4, "width"], [32, 11, 12, 9], [33, 6, 13, 4, "height"], [34, 4, 14, 2], [34, 5, 14, 3], [34, 8, 14, 6, "size"], [34, 12, 14, 10], [35, 4, 15, 2], [35, 10, 15, 8], [35, 11, 15, 9, "picture"], [35, 18, 15, 16], [35, 20, 15, 18, "setPicture"], [35, 30, 15, 28], [35, 31, 15, 29], [35, 34, 15, 32], [35, 38, 15, 32, "useState"], [35, 53, 15, 40], [35, 55, 15, 41], [35, 59, 15, 45], [35, 60, 15, 46], [36, 4, 16, 2], [36, 8, 16, 2, "useEffect"], [36, 24, 16, 11], [36, 26, 16, 12], [36, 32, 16, 18], [37, 6, 17, 4], [37, 10, 17, 4, "drawAsPicture"], [37, 34, 17, 17], [37, 36, 17, 18, "element"], [37, 43, 17, 25], [37, 45, 17, 27], [38, 8, 18, 6, "x"], [38, 9, 18, 7], [38, 11, 18, 9], [38, 12, 18, 10], [39, 8, 19, 6, "y"], [39, 9, 19, 7], [39, 11, 19, 9], [39, 12, 19, 10], [40, 8, 20, 6, "width"], [40, 13, 20, 11], [41, 8, 21, 6, "height"], [42, 6, 22, 4], [42, 7, 22, 5], [42, 8, 22, 6], [42, 9, 22, 7, "then"], [42, 13, 22, 11], [42, 14, 22, 12, "pic"], [42, 17, 22, 15], [42, 21, 22, 19], [43, 8, 23, 6, "setPicture"], [43, 18, 23, 16], [43, 19, 23, 17, "pic"], [43, 22, 23, 20], [43, 23, 23, 21], [44, 6, 24, 4], [44, 7, 24, 5], [44, 8, 24, 6], [45, 6, 25, 4], [46, 4, 26, 2], [46, 5, 26, 3], [46, 7, 26, 5, "deps"], [46, 11, 26, 9], [46, 16, 26, 14], [46, 20, 26, 18], [46, 24, 26, 22, "deps"], [46, 28, 26, 26], [46, 33, 26, 31], [46, 38, 26, 36], [46, 39, 26, 37], [46, 42, 26, 40, "deps"], [46, 46, 26, 44], [46, 49, 26, 47], [46, 51, 26, 49], [46, 52, 26, 50], [47, 4, 27, 2], [47, 11, 27, 9, "usePictureAsTexture"], [47, 30, 27, 28], [47, 31, 27, 29, "picture"], [47, 38, 27, 36], [47, 40, 27, 38, "size"], [47, 44, 27, 42], [47, 45, 27, 43], [48, 2, 28, 0], [48, 3, 28, 1], [49, 2, 28, 2, "exports"], [49, 9, 28, 2], [49, 10, 28, 2, "useTexture"], [49, 20, 28, 2], [49, 23, 28, 2, "useTexture"], [49, 33, 28, 2], [50, 2, 29, 7], [50, 8, 29, 13, "usePictureAsTexture"], [50, 27, 29, 32], [50, 30, 29, 35, "usePictureAsTexture"], [50, 31, 29, 36, "picture"], [50, 38, 29, 43], [50, 40, 29, 45, "size"], [50, 44, 29, 49], [50, 49, 29, 54], [51, 4, 30, 2], [51, 10, 30, 8, "texture"], [51, 17, 30, 15], [51, 20, 30, 18, "<PERSON><PERSON>"], [51, 44, 30, 21], [51, 45, 30, 22, "useSharedValue"], [51, 59, 30, 36], [51, 60, 30, 37], [51, 64, 30, 41], [51, 65, 30, 42], [52, 4, 31, 2], [52, 8, 31, 2, "useEffect"], [52, 24, 31, 11], [52, 26, 31, 12], [52, 32, 31, 18], [53, 6, 32, 4], [53, 10, 32, 8, "picture"], [53, 17, 32, 15], [53, 22, 32, 20], [53, 26, 32, 24], [53, 28, 32, 26], [54, 8, 33, 6, "<PERSON><PERSON>"], [54, 32, 33, 9], [54, 33, 33, 10, "runOnUI"], [54, 40, 33, 17], [54, 41, 33, 18, "createTexture"], [54, 54, 33, 31], [54, 55, 33, 32], [54, 56, 33, 33, "texture"], [54, 63, 33, 40], [54, 65, 33, 42, "picture"], [54, 72, 33, 49], [54, 74, 33, 51, "size"], [54, 78, 33, 55], [54, 79, 33, 56], [55, 6, 34, 4], [56, 4, 35, 2], [56, 5, 35, 3], [56, 7, 35, 5], [56, 8, 35, 6, "picture"], [56, 15, 35, 13], [56, 17, 35, 15, "size"], [56, 21, 35, 19], [56, 23, 35, 21, "texture"], [56, 30, 35, 28], [56, 31, 35, 29], [56, 32, 35, 30], [57, 4, 36, 2], [57, 11, 36, 9, "texture"], [57, 18, 36, 16], [58, 2, 37, 0], [58, 3, 37, 1], [59, 2, 37, 2, "exports"], [59, 9, 37, 2], [59, 10, 37, 2, "usePictureAsTexture"], [59, 29, 37, 2], [59, 32, 37, 2, "usePictureAsTexture"], [59, 51, 37, 2], [60, 2, 38, 7], [60, 8, 38, 13, "useImageAsTexture"], [60, 25, 38, 30], [60, 28, 38, 33, "source"], [60, 34, 38, 39], [60, 38, 38, 43], [61, 4, 39, 2], [61, 10, 39, 8, "image"], [61, 15, 39, 13], [61, 18, 39, 16], [61, 22, 39, 16, "useImage"], [61, 36, 39, 24], [61, 38, 39, 25, "source"], [61, 44, 39, 31], [61, 45, 39, 32], [62, 4, 40, 2], [62, 10, 40, 8, "size"], [62, 14, 40, 12], [62, 17, 40, 15], [62, 21, 40, 15, "useMemo"], [62, 35, 40, 22], [62, 37, 40, 23], [62, 43, 40, 29], [63, 6, 41, 4], [63, 10, 41, 8, "image"], [63, 15, 41, 13], [63, 17, 41, 15], [64, 8, 42, 6], [64, 15, 42, 13], [65, 10, 43, 8, "width"], [65, 15, 43, 13], [65, 17, 43, 15, "image"], [65, 22, 43, 20], [65, 23, 43, 21, "width"], [65, 28, 43, 26], [65, 29, 43, 27], [65, 30, 43, 28], [66, 10, 44, 8, "height"], [66, 16, 44, 14], [66, 18, 44, 16, "image"], [66, 23, 44, 21], [66, 24, 44, 22, "height"], [66, 30, 44, 28], [66, 31, 44, 29], [67, 8, 45, 6], [67, 9, 45, 7], [68, 6, 46, 4], [69, 6, 47, 4], [69, 13, 47, 11], [70, 8, 48, 6, "width"], [70, 13, 48, 11], [70, 15, 48, 13], [70, 16, 48, 14], [71, 8, 49, 6, "height"], [71, 14, 49, 12], [71, 16, 49, 14], [72, 6, 50, 4], [72, 7, 50, 5], [73, 4, 51, 2], [73, 5, 51, 3], [73, 7, 51, 5], [73, 8, 51, 6, "image"], [73, 13, 51, 11], [73, 14, 51, 12], [73, 15, 51, 13], [74, 4, 52, 2], [74, 10, 52, 8, "picture"], [74, 17, 52, 15], [74, 20, 52, 18], [74, 24, 52, 18, "useMemo"], [74, 38, 52, 25], [74, 40, 52, 26], [74, 46, 52, 32], [75, 6, 53, 4], [75, 10, 53, 8, "image"], [75, 15, 53, 13], [75, 17, 53, 15], [76, 8, 54, 6], [76, 14, 54, 12, "recorder"], [76, 22, 54, 20], [76, 25, 54, 23, "Skia"], [76, 35, 54, 27], [76, 36, 54, 28, "PictureRecorder"], [76, 51, 54, 43], [76, 52, 54, 44], [76, 53, 54, 45], [77, 8, 55, 6], [77, 14, 55, 12, "canvas"], [77, 20, 55, 18], [77, 23, 55, 21, "recorder"], [77, 31, 55, 29], [77, 32, 55, 30, "beginRecording"], [77, 46, 55, 44], [77, 47, 55, 45], [78, 10, 56, 8, "x"], [78, 11, 56, 9], [78, 13, 56, 11], [78, 14, 56, 12], [79, 10, 57, 8, "y"], [79, 11, 57, 9], [79, 13, 57, 11], [79, 14, 57, 12], [80, 10, 58, 8, "width"], [80, 15, 58, 13], [80, 17, 58, 15, "size"], [80, 21, 58, 19], [80, 22, 58, 20, "width"], [80, 27, 58, 25], [81, 10, 59, 8, "height"], [81, 16, 59, 14], [81, 18, 59, 16, "size"], [81, 22, 59, 20], [81, 23, 59, 21, "height"], [82, 8, 60, 6], [82, 9, 60, 7], [82, 10, 60, 8], [83, 8, 61, 6, "canvas"], [83, 14, 61, 12], [83, 15, 61, 13, "drawImage"], [83, 24, 61, 22], [83, 25, 61, 23, "image"], [83, 30, 61, 28], [83, 32, 61, 30], [83, 33, 61, 31], [83, 35, 61, 33], [83, 36, 61, 34], [83, 37, 61, 35], [84, 8, 62, 6], [84, 15, 62, 13, "recorder"], [84, 23, 62, 21], [84, 24, 62, 22, "finishRecordingAsPicture"], [84, 48, 62, 46], [84, 49, 62, 47], [84, 50, 62, 48], [85, 6, 63, 4], [85, 7, 63, 5], [85, 13, 63, 11], [86, 8, 64, 6], [86, 15, 64, 13], [86, 19, 64, 17], [87, 6, 65, 4], [88, 4, 66, 2], [88, 5, 66, 3], [88, 7, 66, 5], [88, 8, 66, 6, "size"], [88, 12, 66, 10], [88, 14, 66, 12, "image"], [88, 19, 66, 17], [88, 20, 66, 18], [88, 21, 66, 19], [89, 4, 67, 2], [89, 11, 67, 9, "usePictureAsTexture"], [89, 30, 67, 28], [89, 31, 67, 29, "picture"], [89, 38, 67, 36], [89, 40, 67, 38, "size"], [89, 44, 67, 42], [89, 45, 67, 43], [90, 2, 68, 0], [90, 3, 68, 1], [91, 2, 68, 2, "exports"], [91, 9, 68, 2], [91, 10, 68, 2, "useImageAsTexture"], [91, 27, 68, 2], [91, 30, 68, 2, "useImageAsTexture"], [91, 47, 68, 2], [92, 0, 68, 2], [92, 3]], "functionMap": {"names": ["<global>", "createTexture", "useTexture", "useEffect$argument_0", "drawAsPicture.then$argument_0", "usePictureAsTexture", "useImageAsTexture", "useMemo$argument_0"], "mappings": "AAA;sBCI;CDI;0BEC;YCM;YCM;KDE;GDE;CFE;mCKC;YFE;GEI;CLE;iCMC;uBCE;GDW;0BCC;GDc;CNE"}}, "type": "js/module"}]}