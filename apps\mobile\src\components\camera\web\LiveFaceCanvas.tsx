import React, { useEffect, useRef } from 'react';

interface LiveFaceCanvasProps {
  containerId: string;
  width: number;
  height: number;
}

// Web-only live preview face detection using the built-in FaceDetector API (Chromium)
// Falls back to MediaPipe Tasks Vision in non-Chromium browsers.
// Draws blurred patches over detected faces on a canvas overlay.
export default function LiveFaceCanvas({ containerId, width, height }: LiveFaceCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const rafRef = useRef<number | null>(null);
  const detectorRef = useRef<any | null>(null);
  const mpRef = useRef<any | null>(null);
  const isMediapipeRef = useRef<boolean>(false);
  const debugCounterRef = useRef<number>(0);

  useEffect(() => {
    console.log('[LiveFaceCanvas] Starting initialization...', { containerId, width, height });

    const container = document.getElementById(containerId);
    if (!container) {
      console.error('[LiveFaceCanvas] Container not found:', containerId);
      return;
    }
    console.log('[LiveFaceCanvas] Container found:', container);

    const video: HTMLVideoElement | null = container.querySelector('video');
    if (!video) {
      console.error('[LiveFaceCanvas] Video element not found in container');
      return;
    }
    console.log('[LiveFaceCanvas] Video element found:', video);

    // Resize canvas
    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('[LiveFaceCanvas] Canvas ref not available');
      return;
    }
    canvas.width = width;
    canvas.height = height;
    console.log('[LiveFaceCanvas] Canvas resized to:', width, 'x', height);

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('[LiveFaceCanvas] Canvas context not available');
      return;
    }

    let running = true;
    debugCounterRef.current = 0;

    const loadScript = (src: string) => new Promise<void>((resolve, reject) => {
      const s = document.createElement('script');
      s.src = src; s.async = true; s.onload = () => resolve(); s.onerror = () => reject(new Error('script load failed'));
      document.head.appendChild(s);
    });

    const initDetector = async () => {
      try {
        console.log('[LiveFaceCanvas] Initializing face detector...');

        // Prefer built-in FaceDetector (fast, Chromium)
        // @ts-ignore
        if ('FaceDetector' in window) {
          console.log('[LiveFaceCanvas] Native FaceDetector API available');
          try {
            // @ts-ignore
            detectorRef.current = new (window as any).FaceDetector({ fastMode: true, maxDetectedFaces: 10 });
            isMediapipeRef.current = false;
            console.log('[LiveFaceCanvas] ✅ Native FaceDetector initialized successfully');
            return;
          } catch (error) {
            console.error('[LiveFaceCanvas] ❌ Failed to create native FaceDetector:', error);
          }
        } else {
          console.log('[LiveFaceCanvas] Native FaceDetector API not available, trying MediaPipe...');
        }

        // Fallback: MediaPipe Tasks Vision
        console.log('[LiveFaceCanvas] Loading MediaPipe script...');
        await loadScript('https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/vision_bundle.js');
        console.log('[LiveFaceCanvas] MediaPipe script loaded');

        // @ts-ignore
        const tv = (window as any);
        if (tv && tv.FilesetResolver && tv.FaceDetector) {
          console.log('[LiveFaceCanvas] MediaPipe APIs available, initializing...');
          // @ts-ignore
          const vision = await tv.FilesetResolver.forVisionTasks('https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm');
          console.log('[LiveFaceCanvas] MediaPipe vision initialized');

          // @ts-ignore
          detectorRef.current = await tv.FaceDetector.createFromOptions(vision, {
            baseOptions: {
              // Public model asset via CDN
              modelAssetPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm/face_detection_short_range.tflite',
            },
            runningMode: 'VIDEO',
          });
          mpRef.current = tv;
          isMediapipeRef.current = true;
          console.log('[LiveFaceCanvas] ✅ MediaPipe detector initialized successfully');
        } else {
          console.warn('[LiveFaceCanvas] ❌ MediaPipe APIs not available after script load');
        }
      } catch (e) {
        console.error('[LiveFaceCanvas] ❌ Detector initialization failed:', e);
      }
    };

    const expandBox = (box: DOMRectReadOnly) => {
      const cx = box.x + box.width / 2;
      const cy = box.y + box.height / 2;
      const w = box.width * 1.35;
      const h = box.height * 1.5;
      return { x: cx - w / 2, y: cy - h / 2, width: w, height: h };
    };

    const applyFallbackBlur = (ctx: CanvasRenderingContext2D, videoW: number, videoH: number, canvasW: number, canvasH: number) => {
      // Apply blur to common face areas when no faces are detected
      const scale = Math.max(canvasW / videoW, canvasH / videoH);
      const scaledW = videoW * scale;
      const scaledH = videoH * scale;
      const offsetX = (canvasW - scaledW) / 2;
      const offsetY = (canvasH - scaledH) / 2;

      // Common face areas (center-upper region for selfies)
      const faceAreas = [
        { x: 0.25, y: 0.15, w: 0.5, h: 0.5 }, // Center face
        { x: 0.1, y: 0.2, w: 0.35, h: 0.4 },  // Left side
        { x: 0.55, y: 0.2, w: 0.35, h: 0.4 }  // Right side
      ];

      faceAreas.forEach(area => {
        const x = area.x * scaledW + offsetX;
        const y = area.y * scaledH + offsetY;
        const w = area.w * scaledW;
        const h = area.h * scaledH;

        ctx.save();
        ctx.beginPath();
        ctx.ellipse(x + w / 2, y + h / 2, w / 2, h / 2, 0, 0, Math.PI * 2);
        ctx.clip();
        ctx.filter = 'blur(20px)';
        ctx.drawImage(video, 0, 0, canvasW, canvasH);
        ctx.restore();
      });
    };

    const loop = async () => {
      if (!running) return;
      rafRef.current = requestAnimationFrame(loop);

      if (!detectorRef.current) {
        // Log occasionally that detector is not ready
        debugCounterRef.current++;
        if (debugCounterRef.current % 60 === 0) { // Every ~1 second at 60fps
          console.log('[LiveFaceCanvas] Waiting for detector to initialize...');
        }
        return;
      }

      try {
        let faces: any[] = [];
        if (isMediapipeRef.current) {
          // @ts-ignore
          const res = await detectorRef.current.detectForVideo(video, performance.now());
          faces = (res?.detections || []).map((d: any) => {
            const box = d.boundingBox || d.box || d; // normalize
            return { boundingBox: { x: box.originX ?? box.x ?? 0, y: box.originY ?? box.y ?? 0, width: box.width ?? box.width ?? 0, height: box.height ?? box.height ?? 0 } };
          });
        } else {
          faces = await detectorRef.current.detect(video);
        }

        // Debug logging for face detection
        debugCounterRef.current++;
        if (faces.length > 0) {
          console.log(`[LiveFaceCanvas] 🎯 Detected ${faces.length} face(s) at frame ${debugCounterRef.current}`);
        } else if (debugCounterRef.current % 120 === 0) { // Every ~2 seconds
          console.log(`[LiveFaceCanvas] No faces detected (frame ${debugCounterRef.current})`);
        }

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Compute scale from video to canvas
        const videoW = video.videoWidth || width;
        const videoH = video.videoHeight || height;

        // Log video status occasionally
        if (debugCounterRef.current % 300 === 0) { // Every ~5 seconds
          console.log('[LiveFaceCanvas] Video status:', {
            dimensions: `${videoW}x${videoH}`,
            readyState: video.readyState,
            paused: video.paused,
            currentTime: video.currentTime,
            facesDetected: faces.length
          });
        }

        if (!faces || faces.length === 0) {
          // If no faces detected but detector is working, apply fallback blur to common face areas
          if (debugCounterRef.current > 60) { // Only after detector has had time to initialize
            applyFallbackBlur(ctx, videoW, videoH, width, height);
          }
          return;
        }
        const scale = Math.max(width / videoW, height / videoH);
        const scaledW = videoW * scale;
        const scaledH = videoH * scale;
        const offsetX = (width - scaledW) / 2;
        const offsetY = (height - scaledH) / 2;

        faces.forEach((f: any) => {
          const b = expandBox(f.boundingBox);

          // Map to canvas coordinates
          const x = b.x * scale + offsetX;
          const y = b.y * scale + offsetY;
          const w = b.width * scale;
          const h = b.height * scale;

          // Draw blurred oval patch from the video
          ctx.save();
          ctx.beginPath();
          // Elliptical clip
          ctx.ellipse(x + w / 2, y + h / 2, w / 2, h / 2, 0, 0, Math.PI * 2);
          ctx.clip();
          ctx.filter = 'blur(24px)';
          // Draw from video using same mapping as preview
          ctx.drawImage(
            video,
            // Source rect in video space
            Math.max(0, b.x),
            Math.max(0, b.y),
            Math.min(videoW - b.x, b.width),
            Math.min(videoH - b.y, b.height),
            // Destination in canvas
            x,
            y,
            w,
            h
          );
          ctx.restore();
        });
      } catch (e) {
        // Ignore frame errors to keep loop running
      }
    };

    initDetector().then(() => {
      rafRef.current = requestAnimationFrame(loop);
    });

    return () => {
      running = false;
      if (rafRef.current) cancelAnimationFrame(rafRef.current);
    };
  }, [containerId, width, height]);

  return (
    <canvas
      ref={canvasRef}
      style={{ position: 'absolute', left: 0, top: 0, width, height, pointerEvents: 'none' }}
    />
  );
}
