{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 73, "index": 73}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TextInput", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "DmXc1F5dPYWntVgqRwh73w0VngA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 53, "column": 0, "index": 320}, "end": {"line": 53, "column": 67, "index": 387}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "expo-status-bar", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 57, "column": 0, "index": 395}, "end": {"line": 57, "column": 44, "index": 439}}], "key": "tlkgvZrxUMG8C7vDDJbsBGIlvhs=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 61, "column": 0, "index": 447}, "end": {"line": 61, "column": 59, "index": 506}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 65, "column": 0, "index": 514}, "end": {"line": 113, "column": 29, "index": 774}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-location", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 117, "column": 0, "index": 782}, "end": {"line": 117, "column": 42, "index": 824}}], "key": "GNP7AGCKsBRUhlnTZ4lIPpbkT9E=", "exportNames": ["*"]}}, {"name": "@/components/EchoCameraUnified", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 121, "column": 0, "index": 832}, "end": {"line": 121, "column": 63, "index": 895}}], "key": "wA7DUOcNir1rKeEVRofkWVobA04=", "exportNames": ["*"]}}, {"name": "@/components/camera/EchoCameraWeb", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 125, "column": 0, "index": 903}, "end": {"line": 125, "column": 62, "index": 965}}], "key": "a5L7e3cPb+NheECyYdST63CXrdc=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 129, "column": 0, "index": 973}, "end": {"line": 129, "column": 85, "index": 1058}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = RespondScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/ScrollView\"));\n  var _TextInput = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/TextInput\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Modal\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/Platform\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Image\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[11], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[12], \"react-native-safe-area-context\");\n  var _expoStatusBar = require(_dependencyMap[13], \"expo-status-bar\");\n  var _expoRouter = require(_dependencyMap[14], \"expo-router\");\n  var _lucideReactNative = require(_dependencyMap[15], \"lucide-react-native\");\n  var Location = _interopRequireWildcard(require(_dependencyMap[16], \"expo-location\"));\n  var _EchoCameraUnified = _interopRequireDefault(require(_dependencyMap[17], \"@/components/EchoCameraUnified\"));\n  var _EchoCameraWeb = _interopRequireDefault(require(_dependencyMap[18], \"@/components/camera/EchoCameraWeb\"));\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[19], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _jsxDevRuntime = require(_dependencyMap[20], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\app\\\\respond\\\\[id].jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function RespondScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const {\n      id\n    } = (0, _expoRouter.useLocalSearchParams)();\n    const [response, setResponse] = (0, _react.useState)(\"\");\n    const [showCamera, setShowCamera] = (0, _react.useState)(false);\n    const [cameraResult, setCameraResult] = (0, _react.useState)(null);\n    const [submitting, setSubmitting] = (0, _react.useState)(false);\n    const [capturedPhotoUri, setCapturedPhotoUri] = (0, _react.useState)(null);\n    const defaultTestingMode = (0, _react.useMemo)(() => {\n      if (_Platform.default.OS !== \"web\" || typeof window === \"undefined\") {\n        return false;\n      }\n      const {\n        protocol,\n        hostname\n      } = window.location;\n      const localHosts = [\"localhost\", \"127.0.0.1\", \"::1\"];\n      return protocol !== \"https:\" || localHosts.includes(hostname);\n    }, []);\n    const [testingMode, setTestingMode] = (0, _react.useState)(defaultTestingMode);\n\n    // Location verification state\n\n    const [locationStatus, setLocationStatus] = (0, _react.useState)(\"checking\"); // 'checking', 'verified', 'too_far', 'error'\n\n    const [currentLocation, setCurrentLocation] = (0, _react.useState)(null);\n    const [distance, setDistance] = (0, _react.useState)(null);\n    const [gettingLocation, setGettingLocation] = (0, _react.useState)(false);\n    const [locationError, setLocationError] = (0, _react.useState)(null);\n\n    // Mock question data - in real app, fetch based on id\n\n    const question = {\n      id: id,\n      question: \"Is the coffee shop on Main Street currently open? I need to know if they have seating available.\",\n      location: \"123 Main Street, Downtown\",\n      coordinates: {\n        // FOR TESTING: Updated to Amadora, Portugal coordinates for testing\n\n        latitude: 38.7555,\n        // Amadora, Portugal\n\n        longitude: -9.2337\n      },\n      reward: 2.5,\n      postedAt: \"2 hours ago\",\n      userId: \"user123\"\n    };\n    const questionLatitude = question.coordinates.latitude;\n    const questionLongitude = question.coordinates.longitude;\n\n    // Calculate distance between two coordinates in meters\n\n    const calculateDistance = (lat1, lon1, lat2, lon2) => {\n      const R = 6371e3; // Earth's radius in meters\n\n      const lat1Rad = lat1 * Math.PI / 180;\n      const lat2Rad = lat2 * Math.PI / 180;\n      const deltaLat = (lat2 - lat1) * Math.PI / 180;\n      const deltaLon = (lon2 - lon1) * Math.PI / 180;\n      const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);\n      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n      return R * c; // Distance in meters\n    };\n\n    // Verify user location\n\n    const verifyLocation = (0, _react.useCallback)(async () => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      try {\n        setGettingLocation(true);\n        setLocationError(null);\n        setLocationStatus(\"checking\");\n\n        // Request location permission\n\n        const {\n          status\n        } = await Location.requestForegroundPermissionsAsync();\n        if (status !== \"granted\") {\n          const message = _Platform.default.OS === \"web\" ? \"Allow location access in your browser settings or enable Testing Mode to continue without verification.\" : \"We need your location to verify you're at the question location.\";\n          setLocationError(message);\n          setLocationStatus(\"error\");\n          _Alert.default.alert(\"Location Required\", message);\n          return;\n        }\n\n        // Get current location\n\n        const locationData = await Location.getCurrentPositionAsync({\n          accuracy: Location.Accuracy.High,\n          timeout: 15000,\n          maximumAge: 60000\n        });\n        const userLat = locationData.coords.latitude;\n        const userLon = locationData.coords.longitude;\n        const questionLat = questionLatitude;\n        const questionLon = questionLongitude;\n\n        // Calculate distance\n\n        const distanceInMeters = calculateDistance(userLat, userLon, questionLat, questionLon);\n        setDistance(Math.round(distanceInMeters));\n        setCurrentLocation({\n          latitude: userLat,\n          longitude: userLon\n        });\n\n        // Check if user is within acceptable range (200 meters)\n\n        const maxDistance = 200;\n        if (distanceInMeters <= maxDistance) {\n          setLocationStatus(\"verified\");\n        } else {\n          setLocationStatus(\"too_far\");\n        }\n      } catch (error) {\n        console.error(\"Error verifying location:\", error);\n        let message = \"Could not verify your location. Please check your GPS and try again.\";\n        if (error?.code === 1) {\n          message = \"Location permission was denied. Enable access in your device or browser settings, or turn on Testing Mode.\";\n        } else if (error?.code === 2) {\n          message = \"We couldn't determine your position. Try moving to an open area or toggling airplane mode.\";\n        } else if (error?.code === 3) {\n          message = \"Location request timed out. Please try again.\";\n        } else if (_Platform.default.OS === \"web\" && typeof error?.message === \"string\" && error.message.toLowerCase().includes(\"secure\")) {\n          message = \"The browser blocked location services on this connection. Use https:// or enable Testing Mode for manual capture.\";\n        }\n        setLocationError(message);\n        setLocationStatus(\"error\");\n        _Alert.default.alert(\"Location Error\", message);\n      } finally {\n        setGettingLocation(false);\n      }\n    }, [questionLatitude, questionLongitude, testingMode]);\n\n    // Verify location on mount or when testing mode changes\n\n    (0, _react.useEffect)(() => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      verifyLocation();\n    }, [testingMode, verifyLocation]);\n    const handleStartCamera = () => {\n      console.log(\"Camera button pressed:\", {\n        locationStatus,\n        testingMode,\n        disabled: locationStatus !== \"verified\" && !testingMode,\n        shouldEnable: locationStatus === \"verified\" || testingMode,\n        existingCameraResult: cameraResult // Log existing result\n      });\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", locationStatus === \"too_far\" ? `You are ${distance || 0}m away from the question location. You need to be within 200m to respond.` : \"Please verify your location first.\");\n        return;\n      }\n      setShowCamera(true);\n    };\n    const handleCameraComplete = (0, _react.useCallback)(result => {\n      console.log('Camera result received:', result); // Debug log\n\n      // Extract the URI from various possible sources\n\n      let imageUri = result.imageUrl || result.localUri || result.uri || result.publicUrl;\n\n      // Handle data URIs that might be malformed\n\n      if (imageUri && imageUri.startsWith('data:image')) {\n        // Ensure data URI is properly formatted\n\n        if (!imageUri.includes('base64,')) {\n          console.error('Invalid data URI format:', imageUri.substring(0, 50));\n          imageUri = null;\n        }\n      }\n\n      // For development, use a placeholder if no valid URI\n\n      if (!imageUri && __DEV__) {\n        console.warn('No valid image URI, using placeholder');\n        imageUri = 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Photo+Captured';\n      }\n      setCapturedPhotoUri(imageUri);\n\n      // Normalize the result to ensure we have the correct URI property\n\n      const normalizedResult = {\n        ...result,\n        imageUrl: imageUri,\n        localUri: imageUri,\n        // Store original URI for debugging\n\n        originalUri: result.imageUrl || result.localUri || result.uri || result.publicUrl\n      };\n      console.log('Normalized result with URI:', imageUri);\n      console.log('Full normalized result:', normalizedResult);\n      setCameraResult(normalizedResult);\n      setShowCamera(false);\n\n      // Removed redundant Alert - the UI will show the success state with image\n    }, []);\n    const handleCameraCancel = (0, _react.useCallback)(() => {\n      setShowCamera(false);\n      setCameraResult(null);\n      setCapturedPhotoUri(null);\n    }, []);\n    const submitResponse = async () => {\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", \"Please verify your location first.\");\n        return;\n      }\n      if (!cameraResult) {\n        _Alert.default.alert(\"Missing Photo\", \"Please take the required photo first.\");\n        return;\n      }\n      if (!response.trim()) {\n        _Alert.default.alert(\"Missing Text\", \"Please provide a text explanation with your photo.\");\n        return;\n      }\n      setSubmitting(true);\n      try {\n        // TODO: Submit to API\n\n        const responseData = {\n          questionId: id,\n          textResponse: response.trim(),\n          imageUrl: cameraResult.imageUrl,\n          challengeCode: cameraResult.challengeCode,\n          timestamp: cameraResult.timestamp,\n          userLocation: currentLocation,\n          distanceFromQuestion: distance,\n          testingMode: testingMode // Include testing mode flag\n        };\n        console.log(\"Submitting response:\", responseData);\n\n        // Simulate API call\n\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        _Alert.default.alert(\"Response Submitted!\", testingMode ? \"Test response submitted successfully! This was in testing mode.\" : `You'll receive $${question.reward.toFixed(2)} once the questioner confirms your response.`, [{\n          text: \"OK\",\n          onPress: () => _expoRouter.router.back()\n        }]);\n      } catch (error) {\n        console.error(\"Error submitting response:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to submit response. Please try again.\");\n      } finally {\n        setSubmitting(false);\n      }\n    };\n\n    // Location status component\n\n    const LocationStatus = () => {\n      const getStatusConfig = () => {\n        switch (locationStatus) {\n          case \"checking\":\n            return {\n              color: \"#F59E0B\",\n              bgColor: \"#FEF3C7\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#D97706\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 19\n              }, this),\n              title: \"Checking Location\",\n              message: gettingLocation ? \"Getting your current location\" : \"Verifying position\"\n            };\n          case \"verified\":\n            return {\n              color: \"#10B981\",\n              bgColor: \"#D1FAE5\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                size: 16,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1193,\n                columnNumber: 19\n              }, this),\n              title: \"Location Verified\",\n              message: `You're ${distance || 0}m from the question location`\n            };\n          case \"too_far\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1225,\n                columnNumber: 19\n              }, this),\n              title: \"Too Far Away\",\n              message: `You're ${distance || 0}m away (max 200m allowed)`\n            };\n          case \"error\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1257,\n                columnNumber: 19\n              }, this),\n              title: \"Location Error\",\n              message: \"Could not verify your location\"\n            };\n          default:\n            return {\n              color: \"#6B7280\",\n              bgColor: \"#F3F4F6\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1289,\n                columnNumber: 19\n              }, this),\n              title: \"Unknown Status\",\n              message: \"Please try again\"\n            };\n        }\n      };\n      const config = getStatusConfig();\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: config.bgColor,\n          borderRadius: 12,\n          padding: 16,\n          marginBottom: 24,\n          borderWidth: 1,\n          borderColor: config.color + \"40\"\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 8\n          },\n          children: [config.icon, /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontWeight: \"600\",\n              color: config.color,\n              marginLeft: 8\n            },\n            children: config.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1393,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1361,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: {\n            fontSize: 14,\n            color: config.color,\n            lineHeight: 20\n          },\n          children: config.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1437,\n          columnNumber: 9\n        }, this), (locationStatus === \"too_far\" || locationStatus === \"error\") && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            marginTop: 12\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: verifyLocation,\n            disabled: gettingLocation,\n            style: {\n              backgroundColor: config.color,\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              opacity: gettingLocation ? 0.6 : 1\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: gettingLocation ? \"Checking\" : \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1501,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => {\n              console.log(\"Toggle testing mode:\", {\n                before: testingMode,\n                after: !testingMode\n              });\n              setTestingMode(!testingMode);\n            },\n            style: {\n              backgroundColor: testingMode ? \"#10B981\" : \"#6B7280\",\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              marginLeft: 12\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: testingMode ? \"Testing ON\" : \"Enable Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1581,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1453,\n          columnNumber: 11\n        }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#FEF3C7\",\n            borderRadius: 8,\n            padding: 12,\n            marginTop: 12,\n            borderWidth: 1,\n            borderColor: \"#F59E0B\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 14,\n              fontWeight: \"600\",\n              color: \"#D97706\",\n              marginBottom: 4\n            },\n            children: \"Testing Mode Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1649,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 13,\n              color: \"#D97706\",\n              lineHeight: 18\n            },\n            children: \"Location verification bypassed for testing. You can now use the camera regardless of your location.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1689,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1609,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1321,\n        columnNumber: 7\n      }, this);\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: \"#F9FAFB\"\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoStatusBar.StatusBar, {\n        style: \"dark\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1733,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: \"#fff\",\n          paddingTop: insets.top + 8,\n          paddingHorizontal: 20,\n          paddingBottom: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: \"#E5E7EB\",\n          zIndex: 1000\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => _expoRouter.router.back(),\n            style: {\n              marginRight: 16\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.ArrowLeft, {\n              size: 24,\n              color: \"#111827\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1829,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1813,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 20,\n              fontWeight: \"bold\",\n              color: \"#111827\",\n              flex: 1\n            },\n            children: \"Respond to Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1837,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 10,\n              color: \"#EF4444\"\n            },\n            children: `DEBUG: testing=${testingMode ? \"ON\" : \"OFF\"}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1881,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1785,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#F0F9FF\",\n            borderRadius: 12,\n            padding: 16,\n            borderLeftWidth: 4,\n            borderLeftColor: \"#3B82F6\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              color: \"#1E40AF\",\n              fontWeight: \"500\",\n              marginBottom: 12\n            },\n            children: question.question\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1937,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MapPin, {\n              size: 14,\n              color: \"#6B7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2005,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#6B7280\",\n                marginLeft: 6,\n                flex: 1\n              },\n              children: question.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2009,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1977,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.DollarSign, {\n                size: 14,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2065,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#059669\",\n                  fontWeight: \"600\",\n                  marginLeft: 2\n                },\n                children: `$${question.reward.toFixed(2)} reward`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2069,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2061,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Clock, {\n                size: 14,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2117,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 12,\n                  color: \"#6B7280\",\n                  marginLeft: 4\n                },\n                children: question.postedAt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2121,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2113,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2033,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1901,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1741,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        behavior: \"padding\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingBottom: insets.bottom + 100\n          },\n          showsVerticalScrollIndicator: false,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              padding: 20\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(LocationStatus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2181,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: capturedPhotoUri ? '#10B981' : locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    marginRight: 12\n                  },\n                  children: capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2235,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 12,\n                      fontWeight: '600'\n                    },\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2239,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2203,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Capture Privacy-Safe Photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2245,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2189,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: '#6B7280',\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Take a privacy-safe photo using our real-time face blurring camera. Faces are automatically blurred before the photo is captured.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2265,\n                columnNumber: 15\n              }, this), capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: '#fff',\n                  borderRadius: 16,\n                  overflow: 'hidden',\n                  borderWidth: 1,\n                  borderColor: '#E5E7EB',\n                  ..._Platform.default.select({\n                    ios: {\n                      shadowColor: '#000',\n                      shadowOffset: {\n                        width: 0,\n                        height: 2\n                      },\n                      shadowOpacity: 0.05,\n                      shadowRadius: 8\n                    },\n                    android: {\n                      elevation: 3\n                    },\n                    web: {\n                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\n                    }\n                  })\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Image.default, {\n                    source: {\n                      uri: capturedPhotoUri\n                    },\n                    style: {\n                      width: '100%',\n                      height: 400,\n                      // Increased from 240 to 400 for better visibility\n                      backgroundColor: '#F3F4F6',\n                      borderRadius: 12 // Add slight rounding for better appearance\n                    },\n                    resizeMode: \"contain\" // Changed from \"cover\" to \"contain\" to show full image\n                    ,\n\n                    onError: e => {\n                      console.error('[IMAGE ERROR] Failed to load image:', {\n                        error: e.nativeEvent?.error,\n                        uri: capturedPhotoUri,\n                        cameraResult: JSON.stringify(cameraResult, null, 2)\n                      });\n                    },\n                    onLoad: () => {\n                      console.log('[IMAGE SUCCESS] Image loaded successfully:', capturedPhotoUri);\n                    },\n                    onLoadStart: () => {\n                      console.log('[IMAGE START] Loading image from:', capturedPhotoUri);\n                    },\n                    onLoadEnd: () => {\n                      console.log('[IMAGE END] Image loading finished');\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2335,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      position: 'absolute',\n                      top: 12,\n                      right: 12,\n                      backgroundColor: 'rgba(16, 185, 129, 0.95)',\n                      paddingHorizontal: 12,\n                      paddingVertical: 6,\n                      borderRadius: 20,\n                      flexDirection: 'row',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                      size: 14,\n                      color: \"#fff\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2408,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 12,\n                        fontWeight: '600',\n                        marginLeft: 4\n                      },\n                      children: \"Privacy Protected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2410,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2382,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    padding: 16\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'center',\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        width: 32,\n                        height: 32,\n                        borderRadius: 16,\n                        backgroundColor: '#D1FAE5',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 20,\n                        color: \"#10B981\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2456,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2436,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        marginLeft: 12,\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 18,\n                          fontWeight: '700',\n                          color: '#111827'\n                        },\n                        children: \"Photo Captured Successfully\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2462,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 13,\n                          color: '#6B7280',\n                          marginTop: 2\n                        },\n                        children: \"Ready to submit with your response\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2468,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2460,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2422,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      backgroundColor: '#F0FDF4',\n                      borderRadius: 12,\n                      padding: 12,\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        fontSize: 12,\n                        fontWeight: '600',\n                        color: '#15803D',\n                        marginBottom: 8,\n                        textTransform: 'uppercase',\n                        letterSpacing: 0.5\n                      },\n                      children: \"Protection Applied\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2494,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2520,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: \"Faces automatically blurred in real-time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2522,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2518,\n                      columnNumber: 23\n                    }, this), cameraResult?.challengeCode && cameraResult.challengeCode.trim() && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2534,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Challenge verified: ${cameraResult.challengeCode || 'N/A'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2536,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2532,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2548,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Location confirmed: ${distance || 0}m away`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2550,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2546,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2478,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row'\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                      onPress: () => {\n                        setCameraResult(null);\n                        setCapturedPhotoUri(null);\n                        handleStartCamera();\n                      },\n                      style: {\n                        flex: 1,\n                        backgroundColor: '#fff',\n                        borderWidth: 1,\n                        borderColor: '#D1D5DB',\n                        borderRadius: 12,\n                        paddingVertical: 12,\n                        paddingHorizontal: 16,\n                        flexDirection: 'row',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                        size: 18,\n                        color: \"#6B7280\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2600,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 15,\n                          color: '#6B7280',\n                          fontWeight: '600',\n                          marginLeft: 8\n                        },\n                        children: \"Retake Photo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2602,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2562,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2560,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2420,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2287,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: handleStartCamera,\n                disabled: locationStatus !== 'verified' && !testingMode,\n                style: {\n                  backgroundColor: locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                  borderRadius: 12,\n                  padding: 16,\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                  size: 20,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2644,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 16,\n                    fontWeight: '600',\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: locationStatus === 'verified' || testingMode ? 'Start Camera' : 'Verify Location First'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2646,\n                  columnNumber: 19\n                }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 10,\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: \"TEST\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2672,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2618,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2187,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: \"row\",\n                  alignItems: \"center\",\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: response.trim() ? \"#10B981\" : cameraResult ? \"#3B82F6\" : \"#9CA3AF\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    marginRight: 12\n                  },\n                  children: response.trim() ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2707,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: \"#fff\",\n                      fontSize: 12,\n                      fontWeight: \"600\"\n                    },\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2709,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2691,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: \"600\",\n                    color: \"#111827\"\n                  },\n                  children: \"Add Text Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2717,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2684,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#6B7280\",\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Describe what your photo shows. Be specific and helpful to answer the question completely.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2728,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: \"#fff\",\n                  borderRadius: 12,\n                  borderWidth: 1,\n                  borderColor: \"#E5E7EB\",\n                  padding: 4\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    alignItems: \"flex-start\",\n                    padding: 12\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageCircle, {\n                    size: 20,\n                    color: \"#6B7280\",\n                    style: {\n                      marginTop: 2,\n                      marginRight: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2755,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextInput.default, {\n                    style: {\n                      flex: 1,\n                      fontSize: 16,\n                      color: \"#111827\",\n                      minHeight: 100,\n                      textAlignVertical: \"top\"\n                    },\n                    placeholder: \"Describe what you can see that answers their question\",\n                    placeholderTextColor: \"#9CA3AF\",\n                    value: response,\n                    onChangeText: setResponse,\n                    multiline: true,\n                    maxLength: 500\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2760,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2748,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    paddingHorizontal: 16,\n                    paddingBottom: 8\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#6B7280\"\n                    },\n                    children: \"Be specific and helpful\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2786,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#9CA3AF\"\n                    },\n                    children: `${response.length}/500`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2789,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2777,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2739,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2683,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: \"#EBF5FF\",\n                borderRadius: 12,\n                padding: 16,\n                marginBottom: 24\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontWeight: \"600\",\n                  color: \"#1E40AF\",\n                  marginBottom: 8\n                },\n                children: \"Privacy Protection Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2831,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 13,\n                  color: \"#1E40AF\",\n                  lineHeight: 18\n                },\n                children: `Your photo is processed on-device with real-time face blurring. All faces are automatically blurred before the photo is captured, ensuring complete privacy protection.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2871,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2799,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2173,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2153,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0,\n            backgroundColor: \"#fff\",\n            borderTopWidth: 1,\n            borderTopColor: \"#E5E7EB\",\n            padding: 20,\n            paddingBottom: insets.bottom + 20\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: submitResponse,\n            disabled: submitting || !cameraResult || !response.trim(),\n            style: {\n              backgroundColor: submitting || !cameraResult || !response.trim() ? \"#9CA3AF\" : \"#10B981\",\n              borderRadius: 12,\n              padding: 16,\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: submitting ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  width: 16,\n                  height: 16,\n                  borderRadius: 8,\n                  borderWidth: 2,\n                  borderColor: \"#fff\",\n                  borderTopColor: \"transparent\",\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3043,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\"\n                },\n                children: \"Submitting Response\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3087,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Send, {\n                size: 20,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\",\n                  marginLeft: 8\n                },\n                children: `Submit Response ($${question.reward.toFixed(2)})`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2975,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2923,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2149,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: showCamera,\n        animationType: \"slide\",\n        presentationStyle: \"fullScreen\",\n        children: _Platform.default.OS === 'web' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraWeb.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3211,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraUnified.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 3187,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1729,\n      columnNumber: 5\n    }, this);\n  }\n  _s(RespondScreen, \"gw2Imk4A9BoQq1d4k9rSymf1PEo=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _expoRouter.useLocalSearchParams];\n  });\n  _c = RespondScreen;\n  var _c;\n  $RefreshReg$(_c, \"RespondScreen\");\n});", "lineCount": 1564, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 73], [8, 6, 1, 73, "_View"], [8, 11, 1, 73], [8, 14, 1, 73, "_interopRequireDefault"], [8, 36, 1, 73], [8, 37, 1, 73, "require"], [8, 44, 1, 73], [8, 45, 1, 73, "_dependencyMap"], [8, 59, 1, 73], [9, 2, 1, 73], [9, 6, 1, 73, "_Text"], [9, 11, 1, 73], [9, 14, 1, 73, "_interopRequireDefault"], [9, 36, 1, 73], [9, 37, 1, 73, "require"], [9, 44, 1, 73], [9, 45, 1, 73, "_dependencyMap"], [9, 59, 1, 73], [10, 2, 1, 73], [10, 6, 1, 73, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 17, 1, 73], [10, 20, 1, 73, "_interopRequireDefault"], [10, 42, 1, 73], [10, 43, 1, 73, "require"], [10, 50, 1, 73], [10, 51, 1, 73, "_dependencyMap"], [10, 65, 1, 73], [11, 2, 1, 73], [11, 6, 1, 73, "_TextInput"], [11, 16, 1, 73], [11, 19, 1, 73, "_interopRequireDefault"], [11, 41, 1, 73], [11, 42, 1, 73, "require"], [11, 49, 1, 73], [11, 50, 1, 73, "_dependencyMap"], [11, 64, 1, 73], [12, 2, 1, 73], [12, 6, 1, 73, "_TouchableOpacity"], [12, 23, 1, 73], [12, 26, 1, 73, "_interopRequireDefault"], [12, 48, 1, 73], [12, 49, 1, 73, "require"], [12, 56, 1, 73], [12, 57, 1, 73, "_dependencyMap"], [12, 71, 1, 73], [13, 2, 1, 73], [13, 6, 1, 73, "_<PERSON><PERSON>"], [13, 12, 1, 73], [13, 15, 1, 73, "_interopRequireDefault"], [13, 37, 1, 73], [13, 38, 1, 73, "require"], [13, 45, 1, 73], [13, 46, 1, 73, "_dependencyMap"], [13, 60, 1, 73], [14, 2, 1, 73], [14, 6, 1, 73, "_Modal"], [14, 12, 1, 73], [14, 15, 1, 73, "_interopRequireDefault"], [14, 37, 1, 73], [14, 38, 1, 73, "require"], [14, 45, 1, 73], [14, 46, 1, 73, "_dependencyMap"], [14, 60, 1, 73], [15, 2, 1, 73], [15, 6, 1, 73, "_Platform"], [15, 15, 1, 73], [15, 18, 1, 73, "_interopRequireDefault"], [15, 40, 1, 73], [15, 41, 1, 73, "require"], [15, 48, 1, 73], [15, 49, 1, 73, "_dependencyMap"], [15, 63, 1, 73], [16, 2, 1, 73], [16, 6, 1, 73, "_Image"], [16, 12, 1, 73], [16, 15, 1, 73, "_interopRequireDefault"], [16, 37, 1, 73], [16, 38, 1, 73, "require"], [16, 45, 1, 73], [16, 46, 1, 73, "_dependencyMap"], [16, 60, 1, 73], [17, 2, 1, 73], [17, 6, 1, 73, "_ActivityIndicator"], [17, 24, 1, 73], [17, 27, 1, 73, "_interopRequireDefault"], [17, 49, 1, 73], [17, 50, 1, 73, "require"], [17, 57, 1, 73], [17, 58, 1, 73, "_dependencyMap"], [17, 72, 1, 73], [18, 2, 53, 0], [18, 6, 53, 0, "_reactNativeSafeAreaContext"], [18, 33, 53, 0], [18, 36, 53, 0, "require"], [18, 43, 53, 0], [18, 44, 53, 0, "_dependencyMap"], [18, 58, 53, 0], [19, 2, 57, 0], [19, 6, 57, 0, "_expoStatusBar"], [19, 20, 57, 0], [19, 23, 57, 0, "require"], [19, 30, 57, 0], [19, 31, 57, 0, "_dependencyMap"], [19, 45, 57, 0], [20, 2, 61, 0], [20, 6, 61, 0, "_expoRouter"], [20, 17, 61, 0], [20, 20, 61, 0, "require"], [20, 27, 61, 0], [20, 28, 61, 0, "_dependencyMap"], [20, 42, 61, 0], [21, 2, 65, 0], [21, 6, 65, 0, "_lucideReactNative"], [21, 24, 65, 0], [21, 27, 65, 0, "require"], [21, 34, 65, 0], [21, 35, 65, 0, "_dependencyMap"], [21, 49, 65, 0], [22, 2, 117, 0], [22, 6, 117, 0, "Location"], [22, 14, 117, 0], [22, 17, 117, 0, "_interopRequireWildcard"], [22, 40, 117, 0], [22, 41, 117, 0, "require"], [22, 48, 117, 0], [22, 49, 117, 0, "_dependencyMap"], [22, 63, 117, 0], [23, 2, 121, 0], [23, 6, 121, 0, "_EchoCameraUnified"], [23, 24, 121, 0], [23, 27, 121, 0, "_interopRequireDefault"], [23, 49, 121, 0], [23, 50, 121, 0, "require"], [23, 57, 121, 0], [23, 58, 121, 0, "_dependencyMap"], [23, 72, 121, 0], [24, 2, 125, 0], [24, 6, 125, 0, "_EchoCameraWeb"], [24, 20, 125, 0], [24, 23, 125, 0, "_interopRequireDefault"], [24, 45, 125, 0], [24, 46, 125, 0, "require"], [24, 53, 125, 0], [24, 54, 125, 0, "_dependencyMap"], [24, 68, 125, 0], [25, 2, 129, 0], [25, 6, 129, 0, "_KeyboardAvoidingAnimatedView"], [25, 35, 129, 0], [25, 38, 129, 0, "_interopRequireDefault"], [25, 60, 129, 0], [25, 61, 129, 0, "require"], [25, 68, 129, 0], [25, 69, 129, 0, "_dependencyMap"], [25, 83, 129, 0], [26, 2, 129, 85], [26, 6, 129, 85, "_jsxDevRuntime"], [26, 20, 129, 85], [26, 23, 129, 85, "require"], [26, 30, 129, 85], [26, 31, 129, 85, "_dependencyMap"], [26, 45, 129, 85], [27, 2, 129, 85], [27, 6, 129, 85, "_jsxFileName"], [27, 18, 129, 85], [28, 4, 129, 85, "_s"], [28, 6, 129, 85], [28, 9, 129, 85, "$RefreshSig$"], [28, 21, 129, 85], [29, 2, 129, 85], [29, 11, 129, 85, "_interopRequireWildcard"], [29, 35, 129, 85, "e"], [29, 36, 129, 85], [29, 38, 129, 85, "t"], [29, 39, 129, 85], [29, 68, 129, 85, "WeakMap"], [29, 75, 129, 85], [29, 81, 129, 85, "r"], [29, 82, 129, 85], [29, 89, 129, 85, "WeakMap"], [29, 96, 129, 85], [29, 100, 129, 85, "n"], [29, 101, 129, 85], [29, 108, 129, 85, "WeakMap"], [29, 115, 129, 85], [29, 127, 129, 85, "_interopRequireWildcard"], [29, 150, 129, 85], [29, 162, 129, 85, "_interopRequireWildcard"], [29, 163, 129, 85, "e"], [29, 164, 129, 85], [29, 166, 129, 85, "t"], [29, 167, 129, 85], [29, 176, 129, 85, "t"], [29, 177, 129, 85], [29, 181, 129, 85, "e"], [29, 182, 129, 85], [29, 186, 129, 85, "e"], [29, 187, 129, 85], [29, 188, 129, 85, "__esModule"], [29, 198, 129, 85], [29, 207, 129, 85, "e"], [29, 208, 129, 85], [29, 214, 129, 85, "o"], [29, 215, 129, 85], [29, 217, 129, 85, "i"], [29, 218, 129, 85], [29, 220, 129, 85, "f"], [29, 221, 129, 85], [29, 226, 129, 85, "__proto__"], [29, 235, 129, 85], [29, 243, 129, 85, "default"], [29, 250, 129, 85], [29, 252, 129, 85, "e"], [29, 253, 129, 85], [29, 270, 129, 85, "e"], [29, 271, 129, 85], [29, 294, 129, 85, "e"], [29, 295, 129, 85], [29, 320, 129, 85, "e"], [29, 321, 129, 85], [29, 330, 129, 85, "f"], [29, 331, 129, 85], [29, 337, 129, 85, "o"], [29, 338, 129, 85], [29, 341, 129, 85, "t"], [29, 342, 129, 85], [29, 345, 129, 85, "n"], [29, 346, 129, 85], [29, 349, 129, 85, "r"], [29, 350, 129, 85], [29, 358, 129, 85, "o"], [29, 359, 129, 85], [29, 360, 129, 85, "has"], [29, 363, 129, 85], [29, 364, 129, 85, "e"], [29, 365, 129, 85], [29, 375, 129, 85, "o"], [29, 376, 129, 85], [29, 377, 129, 85, "get"], [29, 380, 129, 85], [29, 381, 129, 85, "e"], [29, 382, 129, 85], [29, 385, 129, 85, "o"], [29, 386, 129, 85], [29, 387, 129, 85, "set"], [29, 390, 129, 85], [29, 391, 129, 85, "e"], [29, 392, 129, 85], [29, 394, 129, 85, "f"], [29, 395, 129, 85], [29, 411, 129, 85, "t"], [29, 412, 129, 85], [29, 416, 129, 85, "e"], [29, 417, 129, 85], [29, 433, 129, 85, "t"], [29, 434, 129, 85], [29, 441, 129, 85, "hasOwnProperty"], [29, 455, 129, 85], [29, 456, 129, 85, "call"], [29, 460, 129, 85], [29, 461, 129, 85, "e"], [29, 462, 129, 85], [29, 464, 129, 85, "t"], [29, 465, 129, 85], [29, 472, 129, 85, "i"], [29, 473, 129, 85], [29, 477, 129, 85, "o"], [29, 478, 129, 85], [29, 481, 129, 85, "Object"], [29, 487, 129, 85], [29, 488, 129, 85, "defineProperty"], [29, 502, 129, 85], [29, 507, 129, 85, "Object"], [29, 513, 129, 85], [29, 514, 129, 85, "getOwnPropertyDescriptor"], [29, 538, 129, 85], [29, 539, 129, 85, "e"], [29, 540, 129, 85], [29, 542, 129, 85, "t"], [29, 543, 129, 85], [29, 550, 129, 85, "i"], [29, 551, 129, 85], [29, 552, 129, 85, "get"], [29, 555, 129, 85], [29, 559, 129, 85, "i"], [29, 560, 129, 85], [29, 561, 129, 85, "set"], [29, 564, 129, 85], [29, 568, 129, 85, "o"], [29, 569, 129, 85], [29, 570, 129, 85, "f"], [29, 571, 129, 85], [29, 573, 129, 85, "t"], [29, 574, 129, 85], [29, 576, 129, 85, "i"], [29, 577, 129, 85], [29, 581, 129, 85, "f"], [29, 582, 129, 85], [29, 583, 129, 85, "t"], [29, 584, 129, 85], [29, 588, 129, 85, "e"], [29, 589, 129, 85], [29, 590, 129, 85, "t"], [29, 591, 129, 85], [29, 602, 129, 85, "f"], [29, 603, 129, 85], [29, 608, 129, 85, "e"], [29, 609, 129, 85], [29, 611, 129, 85, "t"], [29, 612, 129, 85], [30, 2, 133, 15], [30, 11, 133, 24, "RespondScreen"], [30, 24, 133, 37, "RespondScreen"], [30, 25, 133, 37], [30, 27, 133, 40], [31, 4, 133, 40, "_s"], [31, 6, 133, 40], [32, 4, 137, 2], [32, 10, 137, 8, "insets"], [32, 16, 137, 14], [32, 19, 137, 17], [32, 23, 137, 17, "useSafeAreaInsets"], [32, 68, 137, 34], [32, 70, 137, 35], [32, 71, 137, 36], [33, 4, 141, 2], [33, 10, 141, 8], [34, 6, 141, 10, "id"], [35, 4, 141, 13], [35, 5, 141, 14], [35, 8, 141, 17], [35, 12, 141, 17, "useLocalSearchParams"], [35, 44, 141, 37], [35, 46, 141, 38], [35, 47, 141, 39], [36, 4, 145, 2], [36, 10, 145, 8], [36, 11, 145, 9, "response"], [36, 19, 145, 17], [36, 21, 145, 19, "setResponse"], [36, 32, 145, 30], [36, 33, 145, 31], [36, 36, 145, 34], [36, 40, 145, 34, "useState"], [36, 55, 145, 42], [36, 57, 145, 43], [36, 59, 145, 45], [36, 60, 145, 46], [37, 4, 149, 2], [37, 10, 149, 8], [37, 11, 149, 9, "showCamera"], [37, 21, 149, 19], [37, 23, 149, 21, "setShowCamera"], [37, 36, 149, 34], [37, 37, 149, 35], [37, 40, 149, 38], [37, 44, 149, 38, "useState"], [37, 59, 149, 46], [37, 61, 149, 47], [37, 66, 149, 52], [37, 67, 149, 53], [38, 4, 153, 2], [38, 10, 153, 8], [38, 11, 153, 9, "cameraResult"], [38, 23, 153, 21], [38, 25, 153, 23, "setCameraResult"], [38, 40, 153, 38], [38, 41, 153, 39], [38, 44, 153, 42], [38, 48, 153, 42, "useState"], [38, 63, 153, 50], [38, 65, 153, 51], [38, 69, 153, 55], [38, 70, 153, 56], [39, 4, 157, 2], [39, 10, 157, 8], [39, 11, 157, 9, "submitting"], [39, 21, 157, 19], [39, 23, 157, 21, "setSubmitting"], [39, 36, 157, 34], [39, 37, 157, 35], [39, 40, 157, 38], [39, 44, 157, 38, "useState"], [39, 59, 157, 46], [39, 61, 157, 47], [39, 66, 157, 52], [39, 67, 157, 53], [40, 4, 161, 2], [40, 10, 161, 8], [40, 11, 161, 9, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [40, 27, 161, 25], [40, 29, 161, 27, "setCapturedPhotoUri"], [40, 48, 161, 46], [40, 49, 161, 47], [40, 52, 161, 50], [40, 56, 161, 50, "useState"], [40, 71, 161, 58], [40, 73, 161, 59], [40, 77, 161, 63], [40, 78, 161, 64], [41, 4, 165, 2], [41, 10, 165, 8, "defaultTestingMode"], [41, 28, 165, 26], [41, 31, 165, 29], [41, 35, 165, 29, "useMemo"], [41, 49, 165, 36], [41, 51, 165, 37], [41, 57, 165, 43], [42, 6, 169, 4], [42, 10, 169, 8, "Platform"], [42, 27, 169, 16], [42, 28, 169, 17, "OS"], [42, 30, 169, 19], [42, 35, 169, 24], [42, 40, 169, 29], [42, 44, 169, 33], [42, 51, 169, 40, "window"], [42, 57, 169, 46], [42, 62, 169, 51], [42, 73, 169, 62], [42, 75, 169, 64], [43, 8, 173, 6], [43, 15, 173, 13], [43, 20, 173, 18], [44, 6, 177, 4], [45, 6, 181, 4], [45, 12, 181, 10], [46, 8, 181, 12, "protocol"], [46, 16, 181, 20], [47, 8, 181, 22, "hostname"], [48, 6, 181, 31], [48, 7, 181, 32], [48, 10, 181, 35, "window"], [48, 16, 181, 41], [48, 17, 181, 42, "location"], [48, 25, 181, 50], [49, 6, 185, 4], [49, 12, 185, 10, "localHosts"], [49, 22, 185, 20], [49, 25, 185, 23], [49, 26, 185, 24], [49, 37, 185, 35], [49, 39, 185, 37], [49, 50, 185, 48], [49, 52, 185, 50], [49, 57, 185, 55], [49, 58, 185, 56], [50, 6, 189, 4], [50, 13, 189, 11, "protocol"], [50, 21, 189, 19], [50, 26, 189, 24], [50, 34, 189, 32], [50, 38, 189, 36, "localHosts"], [50, 48, 189, 46], [50, 49, 189, 47, "includes"], [50, 57, 189, 55], [50, 58, 189, 56, "hostname"], [50, 66, 189, 64], [50, 67, 189, 65], [51, 4, 193, 2], [51, 5, 193, 3], [51, 7, 193, 5], [51, 9, 193, 7], [51, 10, 193, 8], [52, 4, 197, 2], [52, 10, 197, 8], [52, 11, 197, 9, "testingMode"], [52, 22, 197, 20], [52, 24, 197, 22, "setTestingMode"], [52, 38, 197, 36], [52, 39, 197, 37], [52, 42, 197, 40], [52, 46, 197, 40, "useState"], [52, 61, 197, 48], [52, 63, 197, 49, "defaultTestingMode"], [52, 81, 197, 67], [52, 82, 197, 68], [54, 4, 201, 2], [56, 4, 205, 2], [56, 10, 205, 8], [56, 11, 205, 9, "locationStatus"], [56, 25, 205, 23], [56, 27, 205, 25, "setLocationStatus"], [56, 44, 205, 42], [56, 45, 205, 43], [56, 48, 205, 46], [56, 52, 205, 46, "useState"], [56, 67, 205, 54], [56, 69, 205, 55], [56, 79, 205, 65], [56, 80, 205, 66], [56, 81, 205, 67], [56, 82, 205, 68], [58, 4, 209, 2], [58, 10, 209, 8], [58, 11, 209, 9, "currentLocation"], [58, 26, 209, 24], [58, 28, 209, 26, "setCurrentLocation"], [58, 46, 209, 44], [58, 47, 209, 45], [58, 50, 209, 48], [58, 54, 209, 48, "useState"], [58, 69, 209, 56], [58, 71, 209, 57], [58, 75, 209, 61], [58, 76, 209, 62], [59, 4, 213, 2], [59, 10, 213, 8], [59, 11, 213, 9, "distance"], [59, 19, 213, 17], [59, 21, 213, 19, "setDistance"], [59, 32, 213, 30], [59, 33, 213, 31], [59, 36, 213, 34], [59, 40, 213, 34, "useState"], [59, 55, 213, 42], [59, 57, 213, 43], [59, 61, 213, 47], [59, 62, 213, 48], [60, 4, 217, 2], [60, 10, 217, 8], [60, 11, 217, 9, "gettingLocation"], [60, 26, 217, 24], [60, 28, 217, 26, "setGettingLocation"], [60, 46, 217, 44], [60, 47, 217, 45], [60, 50, 217, 48], [60, 54, 217, 48, "useState"], [60, 69, 217, 56], [60, 71, 217, 57], [60, 76, 217, 62], [60, 77, 217, 63], [61, 4, 221, 2], [61, 10, 221, 8], [61, 11, 221, 9, "locationError"], [61, 24, 221, 22], [61, 26, 221, 24, "setLocationError"], [61, 42, 221, 40], [61, 43, 221, 41], [61, 46, 221, 44], [61, 50, 221, 44, "useState"], [61, 65, 221, 52], [61, 67, 221, 53], [61, 71, 221, 57], [61, 72, 221, 58], [63, 4, 225, 2], [65, 4, 229, 2], [65, 10, 229, 8, "question"], [65, 18, 229, 16], [65, 21, 229, 19], [66, 6, 233, 4, "id"], [66, 8, 233, 6], [66, 10, 233, 8, "id"], [66, 12, 233, 10], [67, 6, 237, 4, "question"], [67, 14, 237, 12], [67, 16, 241, 6], [67, 114, 241, 104], [68, 6, 245, 4, "location"], [68, 14, 245, 12], [68, 16, 245, 14], [68, 43, 245, 41], [69, 6, 249, 4, "coordinates"], [69, 17, 249, 15], [69, 19, 249, 17], [70, 8, 253, 6], [72, 8, 257, 6, "latitude"], [72, 16, 257, 14], [72, 18, 257, 16], [72, 25, 257, 23], [73, 8, 257, 25], [75, 8, 261, 6, "longitude"], [75, 17, 261, 15], [75, 19, 261, 17], [75, 20, 261, 18], [76, 6, 265, 4], [76, 7, 265, 5], [77, 6, 269, 4, "reward"], [77, 12, 269, 10], [77, 14, 269, 12], [77, 17, 269, 15], [78, 6, 273, 4, "postedAt"], [78, 14, 273, 12], [78, 16, 273, 14], [78, 29, 273, 27], [79, 6, 277, 4, "userId"], [79, 12, 277, 10], [79, 14, 277, 12], [80, 4, 281, 0], [80, 5, 281, 1], [81, 4, 285, 2], [81, 10, 285, 8, "questionLatitude"], [81, 26, 285, 24], [81, 29, 285, 27, "question"], [81, 37, 285, 35], [81, 38, 285, 36, "coordinates"], [81, 49, 285, 47], [81, 50, 285, 48, "latitude"], [81, 58, 285, 56], [82, 4, 289, 2], [82, 10, 289, 8, "questionLongitude"], [82, 27, 289, 25], [82, 30, 289, 28, "question"], [82, 38, 289, 36], [82, 39, 289, 37, "coordinates"], [82, 50, 289, 48], [82, 51, 289, 49, "longitude"], [82, 60, 289, 58], [84, 4, 293, 2], [86, 4, 297, 2], [86, 10, 297, 8, "calculateDistance"], [86, 27, 297, 25], [86, 30, 297, 28, "calculateDistance"], [86, 31, 297, 29, "lat1"], [86, 35, 297, 33], [86, 37, 297, 35, "lon1"], [86, 41, 297, 39], [86, 43, 297, 41, "lat2"], [86, 47, 297, 45], [86, 49, 297, 47, "lon2"], [86, 53, 297, 51], [86, 58, 297, 56], [87, 6, 301, 4], [87, 12, 301, 10, "R"], [87, 13, 301, 11], [87, 16, 301, 14], [87, 22, 301, 20], [87, 23, 301, 21], [87, 24, 301, 22], [89, 6, 305, 4], [89, 12, 305, 10, "lat1Rad"], [89, 19, 305, 17], [89, 22, 305, 21, "lat1"], [89, 26, 305, 25], [89, 29, 305, 28, "Math"], [89, 33, 305, 32], [89, 34, 305, 33, "PI"], [89, 36, 305, 35], [89, 39, 305, 39], [89, 42, 305, 42], [90, 6, 309, 4], [90, 12, 309, 10, "lat2Rad"], [90, 19, 309, 17], [90, 22, 309, 21, "lat2"], [90, 26, 309, 25], [90, 29, 309, 28, "Math"], [90, 33, 309, 32], [90, 34, 309, 33, "PI"], [90, 36, 309, 35], [90, 39, 309, 39], [90, 42, 309, 42], [91, 6, 313, 4], [91, 12, 313, 10, "deltaLat"], [91, 20, 313, 18], [91, 23, 313, 22], [91, 24, 313, 23, "lat2"], [91, 28, 313, 27], [91, 31, 313, 30, "lat1"], [91, 35, 313, 34], [91, 39, 313, 38, "Math"], [91, 43, 313, 42], [91, 44, 313, 43, "PI"], [91, 46, 313, 45], [91, 49, 313, 49], [91, 52, 313, 52], [92, 6, 317, 4], [92, 12, 317, 10, "deltaLon"], [92, 20, 317, 18], [92, 23, 317, 22], [92, 24, 317, 23, "lon2"], [92, 28, 317, 27], [92, 31, 317, 30, "lon1"], [92, 35, 317, 34], [92, 39, 317, 38, "Math"], [92, 43, 317, 42], [92, 44, 317, 43, "PI"], [92, 46, 317, 45], [92, 49, 317, 49], [92, 52, 317, 52], [93, 6, 321, 4], [93, 12, 321, 10, "a"], [93, 13, 321, 11], [93, 16, 325, 6, "Math"], [93, 20, 325, 10], [93, 21, 325, 11, "sin"], [93, 24, 325, 14], [93, 25, 325, 15, "deltaLat"], [93, 33, 325, 23], [93, 36, 325, 26], [93, 37, 325, 27], [93, 38, 325, 28], [93, 41, 325, 31, "Math"], [93, 45, 325, 35], [93, 46, 325, 36, "sin"], [93, 49, 325, 39], [93, 50, 325, 40, "deltaLat"], [93, 58, 325, 48], [93, 61, 325, 51], [93, 62, 325, 52], [93, 63, 325, 53], [93, 66, 329, 6, "Math"], [93, 70, 329, 10], [93, 71, 329, 11, "cos"], [93, 74, 329, 14], [93, 75, 329, 15, "lat1Rad"], [93, 82, 329, 22], [93, 83, 329, 23], [93, 86, 329, 26, "Math"], [93, 90, 329, 30], [93, 91, 329, 31, "cos"], [93, 94, 329, 34], [93, 95, 329, 35, "lat2Rad"], [93, 102, 329, 42], [93, 103, 329, 43], [93, 106, 333, 8, "Math"], [93, 110, 333, 12], [93, 111, 333, 13, "sin"], [93, 114, 333, 16], [93, 115, 333, 17, "deltaLon"], [93, 123, 333, 25], [93, 126, 333, 28], [93, 127, 333, 29], [93, 128, 333, 30], [93, 131, 333, 33, "Math"], [93, 135, 333, 37], [93, 136, 333, 38, "sin"], [93, 139, 333, 41], [93, 140, 333, 42, "deltaLon"], [93, 148, 333, 50], [93, 151, 333, 53], [93, 152, 333, 54], [93, 153, 333, 55], [94, 6, 337, 4], [94, 12, 337, 10, "c"], [94, 13, 337, 11], [94, 16, 337, 14], [94, 17, 337, 15], [94, 20, 337, 18, "Math"], [94, 24, 337, 22], [94, 25, 337, 23, "atan2"], [94, 30, 337, 28], [94, 31, 337, 29, "Math"], [94, 35, 337, 33], [94, 36, 337, 34, "sqrt"], [94, 40, 337, 38], [94, 41, 337, 39, "a"], [94, 42, 337, 40], [94, 43, 337, 41], [94, 45, 337, 43, "Math"], [94, 49, 337, 47], [94, 50, 337, 48, "sqrt"], [94, 54, 337, 52], [94, 55, 337, 53], [94, 56, 337, 54], [94, 59, 337, 57, "a"], [94, 60, 337, 58], [94, 61, 337, 59], [94, 62, 337, 60], [95, 6, 341, 4], [95, 13, 341, 11, "R"], [95, 14, 341, 12], [95, 17, 341, 15, "c"], [95, 18, 341, 16], [95, 19, 341, 17], [95, 20, 341, 18], [96, 4, 345, 2], [96, 5, 345, 3], [98, 4, 349, 2], [100, 4, 353, 2], [100, 10, 353, 8, "verifyLocation"], [100, 24, 353, 22], [100, 27, 353, 25], [100, 31, 353, 25, "useCallback"], [100, 49, 353, 36], [100, 51, 353, 37], [100, 63, 353, 49], [101, 6, 357, 4], [101, 10, 357, 8, "testingMode"], [101, 21, 357, 19], [101, 23, 357, 21], [102, 8, 361, 6, "setLocationStatus"], [102, 25, 361, 23], [102, 26, 361, 24], [102, 36, 361, 34], [102, 37, 361, 35], [103, 8, 365, 6, "setLocationError"], [103, 24, 365, 22], [103, 25, 365, 23], [103, 29, 365, 27], [103, 30, 365, 28], [104, 8, 369, 6, "setDistance"], [104, 19, 369, 17], [104, 20, 369, 18], [104, 21, 369, 19], [104, 22, 369, 20], [105, 8, 373, 6, "setCurrentLocation"], [105, 26, 373, 24], [105, 27, 373, 25], [105, 31, 373, 29], [105, 32, 373, 30], [106, 8, 377, 6, "setGettingLocation"], [106, 26, 377, 24], [106, 27, 377, 25], [106, 32, 377, 30], [106, 33, 377, 31], [107, 8, 381, 6], [108, 6, 385, 4], [109, 6, 389, 4], [109, 10, 389, 8], [110, 8, 393, 6, "setGettingLocation"], [110, 26, 393, 24], [110, 27, 393, 25], [110, 31, 393, 29], [110, 32, 393, 30], [111, 8, 397, 6, "setLocationError"], [111, 24, 397, 22], [111, 25, 397, 23], [111, 29, 397, 27], [111, 30, 397, 28], [112, 8, 401, 6, "setLocationStatus"], [112, 25, 401, 23], [112, 26, 401, 24], [112, 36, 401, 34], [112, 37, 401, 35], [114, 8, 405, 6], [116, 8, 409, 6], [116, 14, 409, 12], [117, 10, 409, 14, "status"], [118, 8, 409, 21], [118, 9, 409, 22], [118, 12, 409, 25], [118, 18, 409, 31, "Location"], [118, 26, 409, 39], [118, 27, 409, 40, "requestForegroundPermissionsAsync"], [118, 60, 409, 73], [118, 61, 409, 74], [118, 62, 409, 75], [119, 8, 413, 6], [119, 12, 413, 10, "status"], [119, 18, 413, 16], [119, 23, 413, 21], [119, 32, 413, 30], [119, 34, 413, 32], [120, 10, 417, 8], [120, 16, 417, 14, "message"], [120, 23, 417, 21], [120, 26, 421, 10, "Platform"], [120, 43, 421, 18], [120, 44, 421, 19, "OS"], [120, 46, 421, 21], [120, 51, 421, 26], [120, 56, 421, 31], [120, 59, 425, 14], [120, 164, 425, 119], [120, 167, 429, 14], [120, 233, 429, 80], [121, 10, 433, 8, "setLocationError"], [121, 26, 433, 24], [121, 27, 433, 25, "message"], [121, 34, 433, 32], [121, 35, 433, 33], [122, 10, 437, 8, "setLocationStatus"], [122, 27, 437, 25], [122, 28, 437, 26], [122, 35, 437, 33], [122, 36, 437, 34], [123, 10, 441, 8, "<PERSON><PERSON>"], [123, 24, 441, 13], [123, 25, 441, 14, "alert"], [123, 30, 441, 19], [123, 31, 441, 20], [123, 50, 441, 39], [123, 52, 441, 41, "message"], [123, 59, 441, 48], [123, 60, 441, 49], [124, 10, 445, 8], [125, 8, 449, 6], [127, 8, 453, 6], [129, 8, 457, 6], [129, 14, 457, 12, "locationData"], [129, 26, 457, 24], [129, 29, 457, 27], [129, 35, 457, 33, "Location"], [129, 43, 457, 41], [129, 44, 457, 42, "getCurrentPositionAsync"], [129, 67, 457, 65], [129, 68, 457, 66], [130, 10, 461, 8, "accuracy"], [130, 18, 461, 16], [130, 20, 461, 18, "Location"], [130, 28, 461, 26], [130, 29, 461, 27, "Accuracy"], [130, 37, 461, 35], [130, 38, 461, 36, "High"], [130, 42, 461, 40], [131, 10, 465, 8, "timeout"], [131, 17, 465, 15], [131, 19, 465, 17], [131, 24, 465, 22], [132, 10, 469, 8, "maximumAge"], [132, 20, 469, 18], [132, 22, 469, 20], [133, 8, 473, 6], [133, 9, 473, 7], [133, 10, 473, 8], [134, 8, 477, 6], [134, 14, 477, 12, "userLat"], [134, 21, 477, 19], [134, 24, 477, 22, "locationData"], [134, 36, 477, 34], [134, 37, 477, 35, "coords"], [134, 43, 477, 41], [134, 44, 477, 42, "latitude"], [134, 52, 477, 50], [135, 8, 481, 6], [135, 14, 481, 12, "userLon"], [135, 21, 481, 19], [135, 24, 481, 22, "locationData"], [135, 36, 481, 34], [135, 37, 481, 35, "coords"], [135, 43, 481, 41], [135, 44, 481, 42, "longitude"], [135, 53, 481, 51], [136, 8, 485, 6], [136, 14, 485, 12, "questionLat"], [136, 25, 485, 23], [136, 28, 485, 26, "questionLatitude"], [136, 44, 485, 42], [137, 8, 489, 6], [137, 14, 489, 12, "questionLon"], [137, 25, 489, 23], [137, 28, 489, 26, "questionLongitude"], [137, 45, 489, 43], [139, 8, 493, 6], [141, 8, 497, 6], [141, 14, 497, 12, "distanceInMeters"], [141, 30, 497, 28], [141, 33, 497, 31, "calculateDistance"], [141, 50, 497, 48], [141, 51, 501, 8, "userLat"], [141, 58, 501, 15], [141, 60, 505, 8, "userLon"], [141, 67, 505, 15], [141, 69, 509, 8, "questionLat"], [141, 80, 509, 19], [141, 82, 513, 8, "questionLon"], [141, 93, 517, 6], [141, 94, 517, 7], [142, 8, 521, 6, "setDistance"], [142, 19, 521, 17], [142, 20, 521, 18, "Math"], [142, 24, 521, 22], [142, 25, 521, 23, "round"], [142, 30, 521, 28], [142, 31, 521, 29, "distanceInMeters"], [142, 47, 521, 45], [142, 48, 521, 46], [142, 49, 521, 47], [143, 8, 525, 6, "setCurrentLocation"], [143, 26, 525, 24], [143, 27, 525, 25], [144, 10, 529, 8, "latitude"], [144, 18, 529, 16], [144, 20, 529, 18, "userLat"], [144, 27, 529, 25], [145, 10, 533, 8, "longitude"], [145, 19, 533, 17], [145, 21, 533, 19, "userLon"], [146, 8, 537, 6], [146, 9, 537, 7], [146, 10, 537, 8], [148, 8, 541, 6], [150, 8, 545, 6], [150, 14, 545, 12, "maxDistance"], [150, 25, 545, 23], [150, 28, 545, 26], [150, 31, 545, 29], [151, 8, 549, 6], [151, 12, 549, 10, "distanceInMeters"], [151, 28, 549, 26], [151, 32, 549, 30, "maxDistance"], [151, 43, 549, 41], [151, 45, 549, 43], [152, 10, 553, 8, "setLocationStatus"], [152, 27, 553, 25], [152, 28, 553, 26], [152, 38, 553, 36], [152, 39, 553, 37], [153, 8, 557, 6], [153, 9, 557, 7], [153, 15, 557, 13], [154, 10, 561, 8, "setLocationStatus"], [154, 27, 561, 25], [154, 28, 561, 26], [154, 37, 561, 35], [154, 38, 561, 36], [155, 8, 565, 6], [156, 6, 569, 4], [156, 7, 569, 5], [156, 8, 569, 6], [156, 15, 569, 13, "error"], [156, 20, 569, 18], [156, 22, 569, 20], [157, 8, 573, 6, "console"], [157, 15, 573, 13], [157, 16, 573, 14, "error"], [157, 21, 573, 19], [157, 22, 573, 20], [157, 49, 573, 47], [157, 51, 573, 49, "error"], [157, 56, 573, 54], [157, 57, 573, 55], [158, 8, 577, 6], [158, 12, 577, 10, "message"], [158, 19, 577, 17], [158, 22, 577, 20], [158, 92, 577, 90], [159, 8, 581, 6], [159, 12, 581, 10, "error"], [159, 17, 581, 15], [159, 19, 581, 17, "code"], [159, 23, 581, 21], [159, 28, 581, 26], [159, 29, 581, 27], [159, 31, 581, 29], [160, 10, 585, 8, "message"], [160, 17, 585, 15], [160, 20, 585, 18], [160, 128, 585, 126], [161, 8, 589, 6], [161, 9, 589, 7], [161, 15, 589, 13], [161, 19, 589, 17, "error"], [161, 24, 589, 22], [161, 26, 589, 24, "code"], [161, 30, 589, 28], [161, 35, 589, 33], [161, 36, 589, 34], [161, 38, 589, 36], [162, 10, 593, 8, "message"], [162, 17, 593, 15], [162, 20, 593, 18], [162, 112, 593, 110], [163, 8, 597, 6], [163, 9, 597, 7], [163, 15, 597, 13], [163, 19, 597, 17, "error"], [163, 24, 597, 22], [163, 26, 597, 24, "code"], [163, 30, 597, 28], [163, 35, 597, 33], [163, 36, 597, 34], [163, 38, 597, 36], [164, 10, 601, 8, "message"], [164, 17, 601, 15], [164, 20, 601, 18], [164, 67, 601, 65], [165, 8, 605, 6], [165, 9, 605, 7], [165, 15, 605, 13], [165, 19, 605, 17, "Platform"], [165, 36, 605, 25], [165, 37, 605, 26, "OS"], [165, 39, 605, 28], [165, 44, 605, 33], [165, 49, 605, 38], [165, 53, 605, 42], [165, 60, 605, 49, "error"], [165, 65, 605, 54], [165, 67, 605, 56, "message"], [165, 74, 605, 63], [165, 79, 605, 68], [165, 87, 605, 76], [165, 91, 605, 80, "error"], [165, 96, 605, 85], [165, 97, 605, 86, "message"], [165, 104, 605, 93], [165, 105, 605, 94, "toLowerCase"], [165, 116, 605, 105], [165, 117, 605, 106], [165, 118, 605, 107], [165, 119, 605, 108, "includes"], [165, 127, 605, 116], [165, 128, 605, 117], [165, 136, 605, 125], [165, 137, 605, 126], [165, 139, 605, 128], [166, 10, 609, 8, "message"], [166, 17, 609, 15], [166, 20, 609, 18], [166, 135, 609, 133], [167, 8, 613, 6], [168, 8, 617, 6, "setLocationError"], [168, 24, 617, 22], [168, 25, 617, 23, "message"], [168, 32, 617, 30], [168, 33, 617, 31], [169, 8, 621, 6, "setLocationStatus"], [169, 25, 621, 23], [169, 26, 621, 24], [169, 33, 621, 31], [169, 34, 621, 32], [170, 8, 625, 6, "<PERSON><PERSON>"], [170, 22, 625, 11], [170, 23, 625, 12, "alert"], [170, 28, 625, 17], [170, 29, 625, 18], [170, 45, 625, 34], [170, 47, 625, 36, "message"], [170, 54, 625, 43], [170, 55, 625, 44], [171, 6, 629, 4], [171, 7, 629, 5], [171, 16, 629, 14], [172, 8, 633, 6, "setGettingLocation"], [172, 26, 633, 24], [172, 27, 633, 25], [172, 32, 633, 30], [172, 33, 633, 31], [173, 6, 637, 4], [174, 4, 641, 2], [174, 5, 641, 3], [174, 7, 641, 5], [174, 8, 641, 6, "questionLatitude"], [174, 24, 641, 22], [174, 26, 641, 24, "questionLongitude"], [174, 43, 641, 41], [174, 45, 641, 43, "testingMode"], [174, 56, 641, 54], [174, 57, 641, 55], [174, 58, 641, 56], [176, 4, 645, 2], [178, 4, 649, 2], [178, 8, 649, 2, "useEffect"], [178, 24, 649, 11], [178, 26, 649, 12], [178, 32, 649, 18], [179, 6, 653, 4], [179, 10, 653, 8, "testingMode"], [179, 21, 653, 19], [179, 23, 653, 21], [180, 8, 657, 6, "setLocationStatus"], [180, 25, 657, 23], [180, 26, 657, 24], [180, 36, 657, 34], [180, 37, 657, 35], [181, 8, 661, 6, "setLocationError"], [181, 24, 661, 22], [181, 25, 661, 23], [181, 29, 661, 27], [181, 30, 661, 28], [182, 8, 665, 6, "setDistance"], [182, 19, 665, 17], [182, 20, 665, 18], [182, 21, 665, 19], [182, 22, 665, 20], [183, 8, 669, 6, "setCurrentLocation"], [183, 26, 669, 24], [183, 27, 669, 25], [183, 31, 669, 29], [183, 32, 669, 30], [184, 8, 673, 6, "setGettingLocation"], [184, 26, 673, 24], [184, 27, 673, 25], [184, 32, 673, 30], [184, 33, 673, 31], [185, 8, 677, 6], [186, 6, 681, 4], [187, 6, 685, 4, "verifyLocation"], [187, 20, 685, 18], [187, 21, 685, 19], [187, 22, 685, 20], [188, 4, 689, 2], [188, 5, 689, 3], [188, 7, 689, 5], [188, 8, 689, 6, "testingMode"], [188, 19, 689, 17], [188, 21, 689, 19, "verifyLocation"], [188, 35, 689, 33], [188, 36, 689, 34], [188, 37, 689, 35], [189, 4, 693, 2], [189, 10, 693, 8, "handleStartCamera"], [189, 27, 693, 25], [189, 30, 693, 28, "handleStartCamera"], [189, 31, 693, 28], [189, 36, 693, 34], [190, 6, 697, 4, "console"], [190, 13, 697, 11], [190, 14, 697, 12, "log"], [190, 17, 697, 15], [190, 18, 697, 16], [190, 42, 697, 40], [190, 44, 697, 42], [191, 8, 701, 6, "locationStatus"], [191, 22, 701, 20], [192, 8, 705, 6, "testingMode"], [192, 19, 705, 17], [193, 8, 709, 6, "disabled"], [193, 16, 709, 14], [193, 18, 709, 16, "locationStatus"], [193, 32, 709, 30], [193, 37, 709, 35], [193, 47, 709, 45], [193, 51, 709, 49], [193, 52, 709, 50, "testingMode"], [193, 63, 709, 61], [194, 8, 713, 6, "shouldEnable"], [194, 20, 713, 18], [194, 22, 713, 20, "locationStatus"], [194, 36, 713, 34], [194, 41, 713, 39], [194, 51, 713, 49], [194, 55, 713, 53, "testingMode"], [194, 66, 713, 64], [195, 8, 717, 6, "existingCameraResult"], [195, 28, 717, 26], [195, 30, 717, 28, "cameraResult"], [195, 42, 717, 40], [195, 43, 717, 42], [196, 6, 721, 4], [196, 7, 721, 5], [196, 8, 721, 6], [197, 6, 725, 4], [197, 10, 725, 8, "locationStatus"], [197, 24, 725, 22], [197, 29, 725, 27], [197, 39, 725, 37], [197, 43, 725, 41], [197, 44, 725, 42, "testingMode"], [197, 55, 725, 53], [197, 57, 725, 55], [198, 8, 729, 6, "<PERSON><PERSON>"], [198, 22, 729, 11], [198, 23, 729, 12, "alert"], [198, 28, 729, 17], [198, 29, 733, 8], [198, 48, 733, 27], [198, 50, 737, 8, "locationStatus"], [198, 64, 737, 22], [198, 69, 737, 27], [198, 78, 737, 36], [198, 81, 741, 12], [198, 92, 741, 23, "distance"], [198, 100, 741, 31], [198, 104, 741, 35], [198, 105, 741, 36], [198, 180, 741, 111], [198, 183, 745, 12], [198, 219, 749, 6], [198, 220, 749, 7], [199, 8, 753, 6], [200, 6, 757, 4], [201, 6, 761, 4, "setShowCamera"], [201, 19, 761, 17], [201, 20, 761, 18], [201, 24, 761, 22], [201, 25, 761, 23], [202, 4, 765, 2], [202, 5, 765, 3], [203, 4, 769, 2], [203, 10, 769, 8, "handleCameraComplete"], [203, 30, 769, 28], [203, 33, 769, 31], [203, 37, 769, 31, "useCallback"], [203, 55, 769, 42], [203, 57, 769, 44, "result"], [203, 63, 769, 50], [203, 67, 769, 55], [204, 6, 773, 4, "console"], [204, 13, 773, 11], [204, 14, 773, 12, "log"], [204, 17, 773, 15], [204, 18, 773, 16], [204, 43, 773, 41], [204, 45, 773, 43, "result"], [204, 51, 773, 49], [204, 52, 773, 50], [204, 53, 773, 51], [204, 54, 773, 52], [206, 6, 777, 4], [208, 6, 781, 4], [208, 10, 781, 8, "imageUri"], [208, 18, 781, 16], [208, 21, 781, 19, "result"], [208, 27, 781, 25], [208, 28, 781, 26, "imageUrl"], [208, 36, 781, 34], [208, 40, 781, 38, "result"], [208, 46, 781, 44], [208, 47, 781, 45, "localUri"], [208, 55, 781, 53], [208, 59, 781, 57, "result"], [208, 65, 781, 63], [208, 66, 781, 64, "uri"], [208, 69, 781, 67], [208, 73, 781, 71, "result"], [208, 79, 781, 77], [208, 80, 781, 78, "publicUrl"], [208, 89, 781, 87], [210, 6, 785, 4], [212, 6, 789, 4], [212, 10, 789, 8, "imageUri"], [212, 18, 789, 16], [212, 22, 789, 20, "imageUri"], [212, 30, 789, 28], [212, 31, 789, 29, "startsWith"], [212, 41, 789, 39], [212, 42, 789, 40], [212, 54, 789, 52], [212, 55, 789, 53], [212, 57, 789, 55], [213, 8, 793, 6], [215, 8, 797, 6], [215, 12, 797, 10], [215, 13, 797, 11, "imageUri"], [215, 21, 797, 19], [215, 22, 797, 20, "includes"], [215, 30, 797, 28], [215, 31, 797, 29], [215, 40, 797, 38], [215, 41, 797, 39], [215, 43, 797, 41], [216, 10, 801, 8, "console"], [216, 17, 801, 15], [216, 18, 801, 16, "error"], [216, 23, 801, 21], [216, 24, 801, 22], [216, 50, 801, 48], [216, 52, 801, 50, "imageUri"], [216, 60, 801, 58], [216, 61, 801, 59, "substring"], [216, 70, 801, 68], [216, 71, 801, 69], [216, 72, 801, 70], [216, 74, 801, 72], [216, 76, 801, 74], [216, 77, 801, 75], [216, 78, 801, 76], [217, 10, 805, 8, "imageUri"], [217, 18, 805, 16], [217, 21, 805, 19], [217, 25, 805, 23], [218, 8, 809, 6], [219, 6, 813, 4], [221, 6, 817, 4], [223, 6, 821, 4], [223, 10, 821, 8], [223, 11, 821, 9, "imageUri"], [223, 19, 821, 17], [223, 23, 821, 21, "__DEV__"], [223, 30, 821, 28], [223, 32, 821, 30], [224, 8, 825, 6, "console"], [224, 15, 825, 13], [224, 16, 825, 14, "warn"], [224, 20, 825, 18], [224, 21, 825, 19], [224, 60, 825, 58], [224, 61, 825, 59], [225, 8, 829, 6, "imageUri"], [225, 16, 829, 14], [225, 19, 829, 17], [225, 90, 829, 88], [226, 6, 833, 4], [227, 6, 837, 4, "setCapturedPhotoUri"], [227, 25, 837, 23], [227, 26, 837, 24, "imageUri"], [227, 34, 837, 32], [227, 35, 837, 33], [229, 6, 841, 4], [231, 6, 845, 4], [231, 12, 845, 10, "normalizedResult"], [231, 28, 845, 26], [231, 31, 845, 29], [232, 8, 849, 6], [232, 11, 849, 9, "result"], [232, 17, 849, 15], [233, 8, 853, 6, "imageUrl"], [233, 16, 853, 14], [233, 18, 853, 16, "imageUri"], [233, 26, 853, 24], [234, 8, 857, 6, "localUri"], [234, 16, 857, 14], [234, 18, 857, 16, "imageUri"], [234, 26, 857, 24], [235, 8, 861, 6], [237, 8, 865, 6, "originalUri"], [237, 19, 865, 17], [237, 21, 865, 19, "result"], [237, 27, 865, 25], [237, 28, 865, 26, "imageUrl"], [237, 36, 865, 34], [237, 40, 865, 38, "result"], [237, 46, 865, 44], [237, 47, 865, 45, "localUri"], [237, 55, 865, 53], [237, 59, 865, 57, "result"], [237, 65, 865, 63], [237, 66, 865, 64, "uri"], [237, 69, 865, 67], [237, 73, 865, 71, "result"], [237, 79, 865, 77], [237, 80, 865, 78, "publicUrl"], [238, 6, 869, 4], [238, 7, 869, 5], [239, 6, 873, 4, "console"], [239, 13, 873, 11], [239, 14, 873, 12, "log"], [239, 17, 873, 15], [239, 18, 873, 16], [239, 47, 873, 45], [239, 49, 873, 47, "imageUri"], [239, 57, 873, 55], [239, 58, 873, 56], [240, 6, 877, 4, "console"], [240, 13, 877, 11], [240, 14, 877, 12, "log"], [240, 17, 877, 15], [240, 18, 877, 16], [240, 43, 877, 41], [240, 45, 877, 43, "normalizedResult"], [240, 61, 877, 59], [240, 62, 877, 60], [241, 6, 881, 4, "setCameraResult"], [241, 21, 881, 19], [241, 22, 881, 20, "normalizedResult"], [241, 38, 881, 36], [241, 39, 881, 37], [242, 6, 885, 4, "setShowCamera"], [242, 19, 885, 17], [242, 20, 885, 18], [242, 25, 885, 23], [242, 26, 885, 24], [244, 6, 889, 4], [245, 4, 893, 2], [245, 5, 893, 3], [245, 7, 893, 5], [245, 9, 893, 7], [245, 10, 893, 8], [246, 4, 897, 2], [246, 10, 897, 8, "handleCameraCancel"], [246, 28, 897, 26], [246, 31, 897, 29], [246, 35, 897, 29, "useCallback"], [246, 53, 897, 40], [246, 55, 897, 41], [246, 61, 897, 47], [247, 6, 901, 4, "setShowCamera"], [247, 19, 901, 17], [247, 20, 901, 18], [247, 25, 901, 23], [247, 26, 901, 24], [248, 6, 905, 4, "setCameraResult"], [248, 21, 905, 19], [248, 22, 905, 20], [248, 26, 905, 24], [248, 27, 905, 25], [249, 6, 909, 4, "setCapturedPhotoUri"], [249, 25, 909, 23], [249, 26, 909, 24], [249, 30, 909, 28], [249, 31, 909, 29], [250, 4, 913, 2], [250, 5, 913, 3], [250, 7, 913, 5], [250, 9, 913, 7], [250, 10, 913, 8], [251, 4, 917, 2], [251, 10, 917, 8, "submitResponse"], [251, 24, 917, 22], [251, 27, 917, 25], [251, 33, 917, 25, "submitResponse"], [251, 34, 917, 25], [251, 39, 917, 37], [252, 6, 921, 4], [252, 10, 921, 8, "locationStatus"], [252, 24, 921, 22], [252, 29, 921, 27], [252, 39, 921, 37], [252, 43, 921, 41], [252, 44, 921, 42, "testingMode"], [252, 55, 921, 53], [252, 57, 921, 55], [253, 8, 925, 6, "<PERSON><PERSON>"], [253, 22, 925, 11], [253, 23, 925, 12, "alert"], [253, 28, 925, 17], [253, 29, 925, 18], [253, 48, 925, 37], [253, 50, 925, 39], [253, 86, 925, 75], [253, 87, 925, 76], [254, 8, 929, 6], [255, 6, 933, 4], [256, 6, 937, 4], [256, 10, 937, 8], [256, 11, 937, 9, "cameraResult"], [256, 23, 937, 21], [256, 25, 937, 23], [257, 8, 941, 6, "<PERSON><PERSON>"], [257, 22, 941, 11], [257, 23, 941, 12, "alert"], [257, 28, 941, 17], [257, 29, 941, 18], [257, 44, 941, 33], [257, 46, 941, 35], [257, 85, 941, 74], [257, 86, 941, 75], [258, 8, 945, 6], [259, 6, 949, 4], [260, 6, 953, 4], [260, 10, 953, 8], [260, 11, 953, 9, "response"], [260, 19, 953, 17], [260, 20, 953, 18, "trim"], [260, 24, 953, 22], [260, 25, 953, 23], [260, 26, 953, 24], [260, 28, 953, 26], [261, 8, 957, 6, "<PERSON><PERSON>"], [261, 22, 957, 11], [261, 23, 957, 12, "alert"], [261, 28, 957, 17], [261, 29, 961, 8], [261, 43, 961, 22], [261, 45, 965, 8], [261, 97, 969, 6], [261, 98, 969, 7], [262, 8, 973, 6], [263, 6, 977, 4], [264, 6, 981, 4, "setSubmitting"], [264, 19, 981, 17], [264, 20, 981, 18], [264, 24, 981, 22], [264, 25, 981, 23], [265, 6, 985, 4], [265, 10, 985, 8], [266, 8, 989, 6], [268, 8, 993, 6], [268, 14, 993, 12, "responseData"], [268, 26, 993, 24], [268, 29, 993, 27], [269, 10, 997, 8, "questionId"], [269, 20, 997, 18], [269, 22, 997, 20, "id"], [269, 24, 997, 22], [270, 10, 1001, 8, "textResponse"], [270, 22, 1001, 20], [270, 24, 1001, 22, "response"], [270, 32, 1001, 30], [270, 33, 1001, 31, "trim"], [270, 37, 1001, 35], [270, 38, 1001, 36], [270, 39, 1001, 37], [271, 10, 1005, 8, "imageUrl"], [271, 18, 1005, 16], [271, 20, 1005, 18, "cameraResult"], [271, 32, 1005, 30], [271, 33, 1005, 31, "imageUrl"], [271, 41, 1005, 39], [272, 10, 1009, 8, "challengeCode"], [272, 23, 1009, 21], [272, 25, 1009, 23, "cameraResult"], [272, 37, 1009, 35], [272, 38, 1009, 36, "challengeCode"], [272, 51, 1009, 49], [273, 10, 1013, 8, "timestamp"], [273, 19, 1013, 17], [273, 21, 1013, 19, "cameraResult"], [273, 33, 1013, 31], [273, 34, 1013, 32, "timestamp"], [273, 43, 1013, 41], [274, 10, 1017, 8, "userLocation"], [274, 22, 1017, 20], [274, 24, 1017, 22, "currentLocation"], [274, 39, 1017, 37], [275, 10, 1021, 8, "distanceFromQuestion"], [275, 30, 1021, 28], [275, 32, 1021, 30, "distance"], [275, 40, 1021, 38], [276, 10, 1025, 8, "testingMode"], [276, 21, 1025, 19], [276, 23, 1025, 21, "testingMode"], [276, 34, 1025, 32], [276, 35, 1025, 34], [277, 8, 1029, 6], [277, 9, 1029, 7], [278, 8, 1033, 6, "console"], [278, 15, 1033, 13], [278, 16, 1033, 14, "log"], [278, 19, 1033, 17], [278, 20, 1033, 18], [278, 42, 1033, 40], [278, 44, 1033, 42, "responseData"], [278, 56, 1033, 54], [278, 57, 1033, 55], [280, 8, 1037, 6], [282, 8, 1041, 6], [282, 14, 1041, 12], [282, 18, 1041, 16, "Promise"], [282, 25, 1041, 23], [282, 26, 1041, 25, "resolve"], [282, 33, 1041, 32], [282, 37, 1041, 37, "setTimeout"], [282, 47, 1041, 47], [282, 48, 1041, 48, "resolve"], [282, 55, 1041, 55], [282, 57, 1041, 57], [282, 61, 1041, 61], [282, 62, 1041, 62], [282, 63, 1041, 63], [283, 8, 1045, 6, "<PERSON><PERSON>"], [283, 22, 1045, 11], [283, 23, 1045, 12, "alert"], [283, 28, 1045, 17], [283, 29, 1049, 8], [283, 50, 1049, 29], [283, 52, 1053, 8, "testingMode"], [283, 63, 1053, 19], [283, 66, 1057, 12], [283, 131, 1057, 77], [283, 134, 1061, 12], [283, 153, 1061, 31, "question"], [283, 161, 1061, 39], [283, 162, 1061, 40, "reward"], [283, 168, 1061, 46], [283, 169, 1061, 47, "toFixed"], [283, 176, 1061, 54], [283, 177, 1061, 55], [283, 178, 1061, 56], [283, 179, 1061, 57], [283, 225, 1061, 103], [283, 227, 1065, 8], [283, 228, 1069, 10], [284, 10, 1073, 12, "text"], [284, 14, 1073, 16], [284, 16, 1073, 18], [284, 20, 1073, 22], [285, 10, 1077, 12, "onPress"], [285, 17, 1077, 19], [285, 19, 1077, 21, "onPress"], [285, 20, 1077, 21], [285, 25, 1077, 27, "router"], [285, 43, 1077, 33], [285, 44, 1077, 34, "back"], [285, 48, 1077, 38], [285, 49, 1077, 39], [286, 8, 1081, 10], [286, 9, 1081, 11], [286, 10, 1089, 6], [286, 11, 1089, 7], [287, 6, 1093, 4], [287, 7, 1093, 5], [287, 8, 1093, 6], [287, 15, 1093, 13, "error"], [287, 20, 1093, 18], [287, 22, 1093, 20], [288, 8, 1097, 6, "console"], [288, 15, 1097, 13], [288, 16, 1097, 14, "error"], [288, 21, 1097, 19], [288, 22, 1097, 20], [288, 50, 1097, 48], [288, 52, 1097, 50, "error"], [288, 57, 1097, 55], [288, 58, 1097, 56], [289, 8, 1101, 6, "<PERSON><PERSON>"], [289, 22, 1101, 11], [289, 23, 1101, 12, "alert"], [289, 28, 1101, 17], [289, 29, 1101, 18], [289, 36, 1101, 25], [289, 38, 1101, 27], [289, 84, 1101, 73], [289, 85, 1101, 74], [290, 6, 1105, 4], [290, 7, 1105, 5], [290, 16, 1105, 14], [291, 8, 1109, 6, "setSubmitting"], [291, 21, 1109, 19], [291, 22, 1109, 20], [291, 27, 1109, 25], [291, 28, 1109, 26], [292, 6, 1113, 4], [293, 4, 1117, 2], [293, 5, 1117, 3], [295, 4, 1121, 2], [297, 4, 1125, 2], [297, 10, 1125, 8, "LocationStatus"], [297, 24, 1125, 22], [297, 27, 1125, 25, "LocationStatus"], [297, 28, 1125, 25], [297, 33, 1125, 31], [298, 6, 1129, 4], [298, 12, 1129, 10, "getStatusConfig"], [298, 27, 1129, 25], [298, 30, 1129, 28, "getStatusConfig"], [298, 31, 1129, 28], [298, 36, 1129, 34], [299, 8, 1133, 6], [299, 16, 1133, 14, "locationStatus"], [299, 30, 1133, 28], [300, 10, 1137, 8], [300, 15, 1137, 13], [300, 25, 1137, 23], [301, 12, 1141, 10], [301, 19, 1141, 17], [302, 14, 1145, 12, "color"], [302, 19, 1145, 17], [302, 21, 1145, 19], [302, 30, 1145, 28], [303, 14, 1149, 12, "bgColor"], [303, 21, 1149, 19], [303, 23, 1149, 21], [303, 32, 1149, 30], [304, 14, 1153, 12, "icon"], [304, 18, 1153, 16], [304, 33, 1153, 18], [304, 37, 1153, 18, "_jsxDevRuntime"], [304, 51, 1153, 18], [304, 52, 1153, 18, "jsxDEV"], [304, 58, 1153, 18], [304, 60, 1153, 19, "_lucideReactNative"], [304, 78, 1153, 19], [304, 79, 1153, 19, "Navigation"], [304, 89, 1153, 29], [305, 16, 1153, 30, "size"], [305, 20, 1153, 34], [305, 22, 1153, 36], [305, 24, 1153, 39], [306, 16, 1153, 40, "color"], [306, 21, 1153, 45], [306, 23, 1153, 46], [307, 14, 1153, 55], [308, 16, 1153, 55, "fileName"], [308, 24, 1153, 55], [308, 26, 1153, 55, "_jsxFileName"], [308, 38, 1153, 55], [309, 16, 1153, 55, "lineNumber"], [309, 26, 1153, 55], [310, 16, 1153, 55, "columnNumber"], [310, 28, 1153, 55], [311, 14, 1153, 55], [311, 21, 1153, 57], [311, 22, 1153, 58], [312, 14, 1157, 12, "title"], [312, 19, 1157, 17], [312, 21, 1157, 19], [312, 40, 1157, 38], [313, 14, 1161, 12, "message"], [313, 21, 1161, 19], [313, 23, 1161, 21, "gettingLocation"], [313, 38, 1161, 36], [313, 41, 1165, 16], [313, 72, 1165, 47], [313, 75, 1169, 16], [314, 12, 1173, 10], [314, 13, 1173, 11], [315, 10, 1177, 8], [315, 15, 1177, 13], [315, 25, 1177, 23], [316, 12, 1181, 10], [316, 19, 1181, 17], [317, 14, 1185, 12, "color"], [317, 19, 1185, 17], [317, 21, 1185, 19], [317, 30, 1185, 28], [318, 14, 1189, 12, "bgColor"], [318, 21, 1189, 19], [318, 23, 1189, 21], [318, 32, 1189, 30], [319, 14, 1193, 12, "icon"], [319, 18, 1193, 16], [319, 33, 1193, 18], [319, 37, 1193, 18, "_jsxDevRuntime"], [319, 51, 1193, 18], [319, 52, 1193, 18, "jsxDEV"], [319, 58, 1193, 18], [319, 60, 1193, 19, "_lucideReactNative"], [319, 78, 1193, 19], [319, 79, 1193, 19, "CheckCircle2"], [319, 91, 1193, 31], [320, 16, 1193, 32, "size"], [320, 20, 1193, 36], [320, 22, 1193, 38], [320, 24, 1193, 41], [321, 16, 1193, 42, "color"], [321, 21, 1193, 47], [321, 23, 1193, 48], [322, 14, 1193, 57], [323, 16, 1193, 57, "fileName"], [323, 24, 1193, 57], [323, 26, 1193, 57, "_jsxFileName"], [323, 38, 1193, 57], [324, 16, 1193, 57, "lineNumber"], [324, 26, 1193, 57], [325, 16, 1193, 57, "columnNumber"], [325, 28, 1193, 57], [326, 14, 1193, 57], [326, 21, 1193, 59], [326, 22, 1193, 60], [327, 14, 1197, 12, "title"], [327, 19, 1197, 17], [327, 21, 1197, 19], [327, 40, 1197, 38], [328, 14, 1201, 12, "message"], [328, 21, 1201, 19], [328, 23, 1201, 21], [328, 33, 1201, 31, "distance"], [328, 41, 1201, 39], [328, 45, 1201, 43], [328, 46, 1201, 44], [329, 12, 1205, 10], [329, 13, 1205, 11], [330, 10, 1209, 8], [330, 15, 1209, 13], [330, 24, 1209, 22], [331, 12, 1213, 10], [331, 19, 1213, 17], [332, 14, 1217, 12, "color"], [332, 19, 1217, 17], [332, 21, 1217, 19], [332, 30, 1217, 28], [333, 14, 1221, 12, "bgColor"], [333, 21, 1221, 19], [333, 23, 1221, 21], [333, 32, 1221, 30], [334, 14, 1225, 12, "icon"], [334, 18, 1225, 16], [334, 33, 1225, 18], [334, 37, 1225, 18, "_jsxDevRuntime"], [334, 51, 1225, 18], [334, 52, 1225, 18, "jsxDEV"], [334, 58, 1225, 18], [334, 60, 1225, 19, "_lucideReactNative"], [334, 78, 1225, 19], [334, 79, 1225, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [334, 92, 1225, 32], [335, 16, 1225, 33, "size"], [335, 20, 1225, 37], [335, 22, 1225, 39], [335, 24, 1225, 42], [336, 16, 1225, 43, "color"], [336, 21, 1225, 48], [336, 23, 1225, 49], [337, 14, 1225, 58], [338, 16, 1225, 58, "fileName"], [338, 24, 1225, 58], [338, 26, 1225, 58, "_jsxFileName"], [338, 38, 1225, 58], [339, 16, 1225, 58, "lineNumber"], [339, 26, 1225, 58], [340, 16, 1225, 58, "columnNumber"], [340, 28, 1225, 58], [341, 14, 1225, 58], [341, 21, 1225, 60], [341, 22, 1225, 61], [342, 14, 1229, 12, "title"], [342, 19, 1229, 17], [342, 21, 1229, 19], [342, 35, 1229, 33], [343, 14, 1233, 12, "message"], [343, 21, 1233, 19], [343, 23, 1233, 21], [343, 33, 1233, 31, "distance"], [343, 41, 1233, 39], [343, 45, 1233, 43], [343, 46, 1233, 44], [344, 12, 1237, 10], [344, 13, 1237, 11], [345, 10, 1241, 8], [345, 15, 1241, 13], [345, 22, 1241, 20], [346, 12, 1245, 10], [346, 19, 1245, 17], [347, 14, 1249, 12, "color"], [347, 19, 1249, 17], [347, 21, 1249, 19], [347, 30, 1249, 28], [348, 14, 1253, 12, "bgColor"], [348, 21, 1253, 19], [348, 23, 1253, 21], [348, 32, 1253, 30], [349, 14, 1257, 12, "icon"], [349, 18, 1257, 16], [349, 33, 1257, 18], [349, 37, 1257, 18, "_jsxDevRuntime"], [349, 51, 1257, 18], [349, 52, 1257, 18, "jsxDEV"], [349, 58, 1257, 18], [349, 60, 1257, 19, "_lucideReactNative"], [349, 78, 1257, 19], [349, 79, 1257, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [349, 92, 1257, 32], [350, 16, 1257, 33, "size"], [350, 20, 1257, 37], [350, 22, 1257, 39], [350, 24, 1257, 42], [351, 16, 1257, 43, "color"], [351, 21, 1257, 48], [351, 23, 1257, 49], [352, 14, 1257, 58], [353, 16, 1257, 58, "fileName"], [353, 24, 1257, 58], [353, 26, 1257, 58, "_jsxFileName"], [353, 38, 1257, 58], [354, 16, 1257, 58, "lineNumber"], [354, 26, 1257, 58], [355, 16, 1257, 58, "columnNumber"], [355, 28, 1257, 58], [356, 14, 1257, 58], [356, 21, 1257, 60], [356, 22, 1257, 61], [357, 14, 1261, 12, "title"], [357, 19, 1261, 17], [357, 21, 1261, 19], [357, 37, 1261, 35], [358, 14, 1265, 12, "message"], [358, 21, 1265, 19], [358, 23, 1265, 21], [359, 12, 1269, 10], [359, 13, 1269, 11], [360, 10, 1273, 8], [361, 12, 1277, 10], [361, 19, 1277, 17], [362, 14, 1281, 12, "color"], [362, 19, 1281, 17], [362, 21, 1281, 19], [362, 30, 1281, 28], [363, 14, 1285, 12, "bgColor"], [363, 21, 1285, 19], [363, 23, 1285, 21], [363, 32, 1285, 30], [364, 14, 1289, 12, "icon"], [364, 18, 1289, 16], [364, 33, 1289, 18], [364, 37, 1289, 18, "_jsxDevRuntime"], [364, 51, 1289, 18], [364, 52, 1289, 18, "jsxDEV"], [364, 58, 1289, 18], [364, 60, 1289, 19, "_lucideReactNative"], [364, 78, 1289, 19], [364, 79, 1289, 19, "Navigation"], [364, 89, 1289, 29], [365, 16, 1289, 30, "size"], [365, 20, 1289, 34], [365, 22, 1289, 36], [365, 24, 1289, 39], [366, 16, 1289, 40, "color"], [366, 21, 1289, 45], [366, 23, 1289, 46], [367, 14, 1289, 55], [368, 16, 1289, 55, "fileName"], [368, 24, 1289, 55], [368, 26, 1289, 55, "_jsxFileName"], [368, 38, 1289, 55], [369, 16, 1289, 55, "lineNumber"], [369, 26, 1289, 55], [370, 16, 1289, 55, "columnNumber"], [370, 28, 1289, 55], [371, 14, 1289, 55], [371, 21, 1289, 57], [371, 22, 1289, 58], [372, 14, 1293, 12, "title"], [372, 19, 1293, 17], [372, 21, 1293, 19], [372, 37, 1293, 35], [373, 14, 1297, 12, "message"], [373, 21, 1297, 19], [373, 23, 1297, 21], [374, 12, 1301, 10], [374, 13, 1301, 11], [375, 8, 1305, 6], [376, 6, 1309, 4], [376, 7, 1309, 5], [377, 6, 1313, 4], [377, 12, 1313, 10, "config"], [377, 18, 1313, 16], [377, 21, 1313, 19, "getStatusConfig"], [377, 36, 1313, 34], [377, 37, 1313, 35], [377, 38, 1313, 36], [378, 6, 1317, 4], [378, 26, 1321, 6], [378, 30, 1321, 6, "_jsxDevRuntime"], [378, 44, 1321, 6], [378, 45, 1321, 6, "jsxDEV"], [378, 51, 1321, 6], [378, 53, 1321, 7, "_View"], [378, 58, 1321, 7], [378, 59, 1321, 7, "default"], [378, 66, 1321, 11], [379, 8, 1325, 8, "style"], [379, 13, 1325, 13], [379, 15, 1325, 15], [380, 10, 1329, 10, "backgroundColor"], [380, 25, 1329, 25], [380, 27, 1329, 27, "config"], [380, 33, 1329, 33], [380, 34, 1329, 34, "bgColor"], [380, 41, 1329, 41], [381, 10, 1333, 10, "borderRadius"], [381, 22, 1333, 22], [381, 24, 1333, 24], [381, 26, 1333, 26], [382, 10, 1337, 10, "padding"], [382, 17, 1337, 17], [382, 19, 1337, 19], [382, 21, 1337, 21], [383, 10, 1341, 10, "marginBottom"], [383, 22, 1341, 22], [383, 24, 1341, 24], [383, 26, 1341, 26], [384, 10, 1345, 10, "borderWidth"], [384, 21, 1345, 21], [384, 23, 1345, 23], [384, 24, 1345, 24], [385, 10, 1349, 10, "borderColor"], [385, 21, 1349, 21], [385, 23, 1349, 23, "config"], [385, 29, 1349, 29], [385, 30, 1349, 30, "color"], [385, 35, 1349, 35], [385, 38, 1349, 38], [386, 8, 1353, 8], [386, 9, 1353, 10], [387, 8, 1353, 10, "children"], [387, 16, 1353, 10], [387, 32, 1361, 8], [387, 36, 1361, 8, "_jsxDevRuntime"], [387, 50, 1361, 8], [387, 51, 1361, 8, "jsxDEV"], [387, 57, 1361, 8], [387, 59, 1361, 9, "_View"], [387, 64, 1361, 9], [387, 65, 1361, 9, "default"], [387, 72, 1361, 13], [388, 10, 1365, 10, "style"], [388, 15, 1365, 15], [388, 17, 1365, 17], [389, 12, 1369, 12, "flexDirection"], [389, 25, 1369, 25], [389, 27, 1369, 27], [389, 32, 1369, 32], [390, 12, 1373, 12, "alignItems"], [390, 22, 1373, 22], [390, 24, 1373, 24], [390, 32, 1373, 32], [391, 12, 1377, 12, "marginBottom"], [391, 24, 1377, 24], [391, 26, 1377, 26], [392, 10, 1381, 10], [392, 11, 1381, 12], [393, 10, 1381, 12, "children"], [393, 18, 1381, 12], [393, 21, 1389, 11, "config"], [393, 27, 1389, 17], [393, 28, 1389, 18, "icon"], [393, 32, 1389, 22], [393, 47, 1393, 10], [393, 51, 1393, 10, "_jsxDevRuntime"], [393, 65, 1393, 10], [393, 66, 1393, 10, "jsxDEV"], [393, 72, 1393, 10], [393, 74, 1393, 11, "_Text"], [393, 79, 1393, 11], [393, 80, 1393, 11, "default"], [393, 87, 1393, 15], [394, 12, 1397, 12, "style"], [394, 17, 1397, 17], [394, 19, 1397, 19], [395, 14, 1401, 14, "fontSize"], [395, 22, 1401, 22], [395, 24, 1401, 24], [395, 26, 1401, 26], [396, 14, 1405, 14, "fontWeight"], [396, 24, 1405, 24], [396, 26, 1405, 26], [396, 31, 1405, 31], [397, 14, 1409, 14, "color"], [397, 19, 1409, 19], [397, 21, 1409, 21, "config"], [397, 27, 1409, 27], [397, 28, 1409, 28, "color"], [397, 33, 1409, 33], [398, 14, 1413, 14, "marginLeft"], [398, 24, 1413, 24], [398, 26, 1413, 26], [399, 12, 1417, 12], [399, 13, 1417, 14], [400, 12, 1417, 14, "children"], [400, 20, 1417, 14], [400, 22, 1425, 13, "config"], [400, 28, 1425, 19], [400, 29, 1425, 20, "title"], [401, 10, 1425, 25], [402, 12, 1425, 25, "fileName"], [402, 20, 1425, 25], [402, 22, 1425, 25, "_jsxFileName"], [402, 34, 1425, 25], [403, 12, 1425, 25, "lineNumber"], [403, 22, 1425, 25], [404, 12, 1425, 25, "columnNumber"], [404, 24, 1425, 25], [405, 10, 1425, 25], [405, 17, 1429, 16], [405, 18, 1429, 17], [406, 8, 1429, 17], [407, 10, 1429, 17, "fileName"], [407, 18, 1429, 17], [407, 20, 1429, 17, "_jsxFileName"], [407, 32, 1429, 17], [408, 10, 1429, 17, "lineNumber"], [408, 20, 1429, 17], [409, 10, 1429, 17, "columnNumber"], [409, 22, 1429, 17], [410, 8, 1429, 17], [410, 15, 1433, 14], [410, 16, 1433, 15], [410, 31, 1437, 8], [410, 35, 1437, 8, "_jsxDevRuntime"], [410, 49, 1437, 8], [410, 50, 1437, 8, "jsxDEV"], [410, 56, 1437, 8], [410, 58, 1437, 9, "_Text"], [410, 63, 1437, 9], [410, 64, 1437, 9, "default"], [410, 71, 1437, 13], [411, 10, 1437, 14, "style"], [411, 15, 1437, 19], [411, 17, 1437, 21], [412, 12, 1437, 23, "fontSize"], [412, 20, 1437, 31], [412, 22, 1437, 33], [412, 24, 1437, 35], [413, 12, 1437, 37, "color"], [413, 17, 1437, 42], [413, 19, 1437, 44, "config"], [413, 25, 1437, 50], [413, 26, 1437, 51, "color"], [413, 31, 1437, 56], [414, 12, 1437, 58, "lineHeight"], [414, 22, 1437, 68], [414, 24, 1437, 70], [415, 10, 1437, 73], [415, 11, 1437, 75], [416, 10, 1437, 75, "children"], [416, 18, 1437, 75], [416, 20, 1441, 11, "config"], [416, 26, 1441, 17], [416, 27, 1441, 18, "message"], [417, 8, 1441, 25], [418, 10, 1441, 25, "fileName"], [418, 18, 1441, 25], [418, 20, 1441, 25, "_jsxFileName"], [418, 32, 1441, 25], [419, 10, 1441, 25, "lineNumber"], [419, 20, 1441, 25], [420, 10, 1441, 25, "columnNumber"], [420, 22, 1441, 25], [421, 8, 1441, 25], [421, 15, 1445, 14], [421, 16, 1445, 15], [421, 18, 1449, 9], [421, 19, 1449, 10, "locationStatus"], [421, 33, 1449, 24], [421, 38, 1449, 29], [421, 47, 1449, 38], [421, 51, 1449, 42, "locationStatus"], [421, 65, 1449, 56], [421, 70, 1449, 61], [421, 77, 1449, 68], [421, 95, 1453, 10], [421, 99, 1453, 10, "_jsxDevRuntime"], [421, 113, 1453, 10], [421, 114, 1453, 10, "jsxDEV"], [421, 120, 1453, 10], [421, 122, 1453, 11, "_View"], [421, 127, 1453, 11], [421, 128, 1453, 11, "default"], [421, 135, 1453, 15], [422, 10, 1453, 16, "style"], [422, 15, 1453, 21], [422, 17, 1453, 23], [423, 12, 1453, 25, "flexDirection"], [423, 25, 1453, 38], [423, 27, 1453, 40], [423, 32, 1453, 45], [424, 12, 1453, 47, "marginTop"], [424, 21, 1453, 56], [424, 23, 1453, 58], [425, 10, 1453, 61], [425, 11, 1453, 63], [426, 10, 1453, 63, "children"], [426, 18, 1453, 63], [426, 34, 1457, 12], [426, 38, 1457, 12, "_jsxDevRuntime"], [426, 52, 1457, 12], [426, 53, 1457, 12, "jsxDEV"], [426, 59, 1457, 12], [426, 61, 1457, 13, "_TouchableOpacity"], [426, 78, 1457, 13], [426, 79, 1457, 13, "default"], [426, 86, 1457, 29], [427, 12, 1461, 14, "onPress"], [427, 19, 1461, 21], [427, 21, 1461, 23, "verifyLocation"], [427, 35, 1461, 38], [428, 12, 1465, 14, "disabled"], [428, 20, 1465, 22], [428, 22, 1465, 24, "gettingLocation"], [428, 37, 1465, 40], [429, 12, 1469, 14, "style"], [429, 17, 1469, 19], [429, 19, 1469, 21], [430, 14, 1473, 16, "backgroundColor"], [430, 29, 1473, 31], [430, 31, 1473, 33, "config"], [430, 37, 1473, 39], [430, 38, 1473, 40, "color"], [430, 43, 1473, 45], [431, 14, 1477, 16, "borderRadius"], [431, 26, 1477, 28], [431, 28, 1477, 30], [431, 29, 1477, 31], [432, 14, 1481, 16, "paddingVertical"], [432, 29, 1481, 31], [432, 31, 1481, 33], [432, 32, 1481, 34], [433, 14, 1485, 16, "paddingHorizontal"], [433, 31, 1485, 33], [433, 33, 1485, 35], [433, 35, 1485, 37], [434, 14, 1489, 16, "opacity"], [434, 21, 1489, 23], [434, 23, 1489, 25, "gettingLocation"], [434, 38, 1489, 40], [434, 41, 1489, 43], [434, 44, 1489, 46], [434, 47, 1489, 49], [435, 12, 1493, 14], [435, 13, 1493, 16], [436, 12, 1493, 16, "children"], [436, 20, 1493, 16], [436, 35, 1501, 14], [436, 39, 1501, 14, "_jsxDevRuntime"], [436, 53, 1501, 14], [436, 54, 1501, 14, "jsxDEV"], [436, 60, 1501, 14], [436, 62, 1501, 15, "_Text"], [436, 67, 1501, 15], [436, 68, 1501, 15, "default"], [436, 75, 1501, 19], [437, 14, 1501, 20, "style"], [437, 19, 1501, 25], [437, 21, 1501, 27], [438, 16, 1501, 29, "fontSize"], [438, 24, 1501, 37], [438, 26, 1501, 39], [438, 28, 1501, 41], [439, 16, 1501, 43, "color"], [439, 21, 1501, 48], [439, 23, 1501, 50], [439, 29, 1501, 56], [440, 16, 1501, 58, "fontWeight"], [440, 26, 1501, 68], [440, 28, 1501, 70], [441, 14, 1501, 76], [441, 15, 1501, 78], [442, 14, 1501, 78, "children"], [442, 22, 1501, 78], [442, 24, 1505, 17, "gettingLocation"], [442, 39, 1505, 32], [442, 42, 1505, 35], [442, 52, 1505, 45], [442, 55, 1505, 48], [443, 12, 1505, 59], [444, 14, 1505, 59, "fileName"], [444, 22, 1505, 59], [444, 24, 1505, 59, "_jsxFileName"], [444, 36, 1505, 59], [445, 14, 1505, 59, "lineNumber"], [445, 24, 1505, 59], [446, 14, 1505, 59, "columnNumber"], [446, 26, 1505, 59], [447, 12, 1505, 59], [447, 19, 1509, 20], [448, 10, 1509, 21], [449, 12, 1509, 21, "fileName"], [449, 20, 1509, 21], [449, 22, 1509, 21, "_jsxFileName"], [449, 34, 1509, 21], [450, 12, 1509, 21, "lineNumber"], [450, 22, 1509, 21], [451, 12, 1509, 21, "columnNumber"], [451, 24, 1509, 21], [452, 10, 1509, 21], [452, 17, 1513, 30], [452, 18, 1513, 31], [452, 33, 1517, 12], [452, 37, 1517, 12, "_jsxDevRuntime"], [452, 51, 1517, 12], [452, 52, 1517, 12, "jsxDEV"], [452, 58, 1517, 12], [452, 60, 1517, 13, "_TouchableOpacity"], [452, 77, 1517, 13], [452, 78, 1517, 13, "default"], [452, 85, 1517, 29], [453, 12, 1521, 14, "onPress"], [453, 19, 1521, 21], [453, 21, 1521, 23, "onPress"], [453, 22, 1521, 23], [453, 27, 1521, 29], [454, 14, 1525, 16, "console"], [454, 21, 1525, 23], [454, 22, 1525, 24, "log"], [454, 25, 1525, 27], [454, 26, 1525, 28], [454, 48, 1525, 50], [454, 50, 1525, 52], [455, 16, 1529, 18, "before"], [455, 22, 1529, 24], [455, 24, 1529, 26, "testingMode"], [455, 35, 1529, 37], [456, 16, 1533, 18, "after"], [456, 21, 1533, 23], [456, 23, 1533, 25], [456, 24, 1533, 26, "testingMode"], [457, 14, 1537, 16], [457, 15, 1537, 17], [457, 16, 1537, 18], [458, 14, 1541, 16, "setTestingMode"], [458, 28, 1541, 30], [458, 29, 1541, 31], [458, 30, 1541, 32, "testingMode"], [458, 41, 1541, 43], [458, 42, 1541, 44], [459, 12, 1545, 14], [459, 13, 1545, 16], [460, 12, 1549, 14, "style"], [460, 17, 1549, 19], [460, 19, 1549, 21], [461, 14, 1553, 16, "backgroundColor"], [461, 29, 1553, 31], [461, 31, 1553, 33, "testingMode"], [461, 42, 1553, 44], [461, 45, 1553, 47], [461, 54, 1553, 56], [461, 57, 1553, 59], [461, 66, 1553, 68], [462, 14, 1557, 16, "borderRadius"], [462, 26, 1557, 28], [462, 28, 1557, 30], [462, 29, 1557, 31], [463, 14, 1561, 16, "paddingVertical"], [463, 29, 1561, 31], [463, 31, 1561, 33], [463, 32, 1561, 34], [464, 14, 1565, 16, "paddingHorizontal"], [464, 31, 1565, 33], [464, 33, 1565, 35], [464, 35, 1565, 37], [465, 14, 1569, 16, "marginLeft"], [465, 24, 1569, 26], [465, 26, 1569, 28], [466, 12, 1573, 14], [466, 13, 1573, 16], [467, 12, 1573, 16, "children"], [467, 20, 1573, 16], [467, 35, 1581, 14], [467, 39, 1581, 14, "_jsxDevRuntime"], [467, 53, 1581, 14], [467, 54, 1581, 14, "jsxDEV"], [467, 60, 1581, 14], [467, 62, 1581, 15, "_Text"], [467, 67, 1581, 15], [467, 68, 1581, 15, "default"], [467, 75, 1581, 19], [468, 14, 1581, 20, "style"], [468, 19, 1581, 25], [468, 21, 1581, 27], [469, 16, 1581, 29, "fontSize"], [469, 24, 1581, 37], [469, 26, 1581, 39], [469, 28, 1581, 41], [470, 16, 1581, 43, "color"], [470, 21, 1581, 48], [470, 23, 1581, 50], [470, 29, 1581, 56], [471, 16, 1581, 58, "fontWeight"], [471, 26, 1581, 68], [471, 28, 1581, 70], [472, 14, 1581, 76], [472, 15, 1581, 78], [473, 14, 1581, 78, "children"], [473, 22, 1581, 78], [473, 24, 1585, 17, "testingMode"], [473, 35, 1585, 28], [473, 38, 1585, 31], [473, 50, 1585, 43], [473, 53, 1585, 46], [474, 12, 1585, 62], [475, 14, 1585, 62, "fileName"], [475, 22, 1585, 62], [475, 24, 1585, 62, "_jsxFileName"], [475, 36, 1585, 62], [476, 14, 1585, 62, "lineNumber"], [476, 24, 1585, 62], [477, 14, 1585, 62, "columnNumber"], [477, 26, 1585, 62], [478, 12, 1585, 62], [478, 19, 1589, 20], [479, 10, 1589, 21], [480, 12, 1589, 21, "fileName"], [480, 20, 1589, 21], [480, 22, 1589, 21, "_jsxFileName"], [480, 34, 1589, 21], [481, 12, 1589, 21, "lineNumber"], [481, 22, 1589, 21], [482, 12, 1589, 21, "columnNumber"], [482, 24, 1589, 21], [483, 10, 1589, 21], [483, 17, 1593, 30], [483, 18, 1593, 31], [484, 8, 1593, 31], [485, 10, 1593, 31, "fileName"], [485, 18, 1593, 31], [485, 20, 1593, 31, "_jsxFileName"], [485, 32, 1593, 31], [486, 10, 1593, 31, "lineNumber"], [486, 20, 1593, 31], [487, 10, 1593, 31, "columnNumber"], [487, 22, 1593, 31], [488, 8, 1593, 31], [488, 15, 1597, 16], [488, 16, 1601, 9], [488, 18, 1605, 9, "testingMode"], [488, 29, 1605, 20], [488, 46, 1609, 10], [488, 50, 1609, 10, "_jsxDevRuntime"], [488, 64, 1609, 10], [488, 65, 1609, 10, "jsxDEV"], [488, 71, 1609, 10], [488, 73, 1609, 11, "_View"], [488, 78, 1609, 11], [488, 79, 1609, 11, "default"], [488, 86, 1609, 15], [489, 10, 1613, 12, "style"], [489, 15, 1613, 17], [489, 17, 1613, 19], [490, 12, 1617, 14, "backgroundColor"], [490, 27, 1617, 29], [490, 29, 1617, 31], [490, 38, 1617, 40], [491, 12, 1621, 14, "borderRadius"], [491, 24, 1621, 26], [491, 26, 1621, 28], [491, 27, 1621, 29], [492, 12, 1625, 14, "padding"], [492, 19, 1625, 21], [492, 21, 1625, 23], [492, 23, 1625, 25], [493, 12, 1629, 14, "marginTop"], [493, 21, 1629, 23], [493, 23, 1629, 25], [493, 25, 1629, 27], [494, 12, 1633, 14, "borderWidth"], [494, 23, 1633, 25], [494, 25, 1633, 27], [494, 26, 1633, 28], [495, 12, 1637, 14, "borderColor"], [495, 23, 1637, 25], [495, 25, 1637, 27], [496, 10, 1641, 12], [496, 11, 1641, 14], [497, 10, 1641, 14, "children"], [497, 18, 1641, 14], [497, 34, 1649, 12], [497, 38, 1649, 12, "_jsxDevRuntime"], [497, 52, 1649, 12], [497, 53, 1649, 12, "jsxDEV"], [497, 59, 1649, 12], [497, 61, 1649, 13, "_Text"], [497, 66, 1649, 13], [497, 67, 1649, 13, "default"], [497, 74, 1649, 17], [498, 12, 1653, 14, "style"], [498, 17, 1653, 19], [498, 19, 1653, 21], [499, 14, 1657, 16, "fontSize"], [499, 22, 1657, 24], [499, 24, 1657, 26], [499, 26, 1657, 28], [500, 14, 1661, 16, "fontWeight"], [500, 24, 1661, 26], [500, 26, 1661, 28], [500, 31, 1661, 33], [501, 14, 1665, 16, "color"], [501, 19, 1665, 21], [501, 21, 1665, 23], [501, 30, 1665, 32], [502, 14, 1669, 16, "marginBottom"], [502, 26, 1669, 28], [502, 28, 1669, 30], [503, 12, 1673, 14], [503, 13, 1673, 16], [504, 12, 1673, 16, "children"], [504, 20, 1673, 16], [504, 22, 1677, 13], [505, 10, 1685, 12], [506, 12, 1685, 12, "fileName"], [506, 20, 1685, 12], [506, 22, 1685, 12, "_jsxFileName"], [506, 34, 1685, 12], [507, 12, 1685, 12, "lineNumber"], [507, 22, 1685, 12], [508, 12, 1685, 12, "columnNumber"], [508, 24, 1685, 12], [509, 10, 1685, 12], [509, 17, 1685, 18], [509, 18, 1685, 19], [509, 33, 1689, 12], [509, 37, 1689, 12, "_jsxDevRuntime"], [509, 51, 1689, 12], [509, 52, 1689, 12, "jsxDEV"], [509, 58, 1689, 12], [509, 60, 1689, 13, "_Text"], [509, 65, 1689, 13], [509, 66, 1689, 13, "default"], [509, 73, 1689, 17], [510, 12, 1689, 18, "style"], [510, 17, 1689, 23], [510, 19, 1689, 25], [511, 14, 1689, 27, "fontSize"], [511, 22, 1689, 35], [511, 24, 1689, 37], [511, 26, 1689, 39], [512, 14, 1689, 41, "color"], [512, 19, 1689, 46], [512, 21, 1689, 48], [512, 30, 1689, 57], [513, 14, 1689, 59, "lineHeight"], [513, 24, 1689, 69], [513, 26, 1689, 71], [514, 12, 1689, 74], [514, 13, 1689, 76], [515, 12, 1689, 76, "children"], [515, 20, 1689, 76], [515, 22, 1689, 77], [516, 10, 1701, 12], [517, 12, 1701, 12, "fileName"], [517, 20, 1701, 12], [517, 22, 1701, 12, "_jsxFileName"], [517, 34, 1701, 12], [518, 12, 1701, 12, "lineNumber"], [518, 22, 1701, 12], [519, 12, 1701, 12, "columnNumber"], [519, 24, 1701, 12], [520, 10, 1701, 12], [520, 17, 1701, 18], [520, 18, 1701, 19], [521, 8, 1701, 19], [522, 10, 1701, 19, "fileName"], [522, 18, 1701, 19], [522, 20, 1701, 19, "_jsxFileName"], [522, 32, 1701, 19], [523, 10, 1701, 19, "lineNumber"], [523, 20, 1701, 19], [524, 10, 1701, 19, "columnNumber"], [524, 22, 1701, 19], [525, 8, 1701, 19], [525, 15, 1705, 16], [525, 16, 1709, 9], [526, 6, 1709, 9], [527, 8, 1709, 9, "fileName"], [527, 16, 1709, 9], [527, 18, 1709, 9, "_jsxFileName"], [527, 30, 1709, 9], [528, 8, 1709, 9, "lineNumber"], [528, 18, 1709, 9], [529, 8, 1709, 9, "columnNumber"], [529, 20, 1709, 9], [530, 6, 1709, 9], [530, 13, 1713, 12], [530, 14, 1713, 13], [531, 4, 1721, 2], [531, 5, 1721, 3], [532, 4, 1725, 2], [532, 24, 1729, 4], [532, 28, 1729, 4, "_jsxDevRuntime"], [532, 42, 1729, 4], [532, 43, 1729, 4, "jsxDEV"], [532, 49, 1729, 4], [532, 51, 1729, 5, "_View"], [532, 56, 1729, 5], [532, 57, 1729, 5, "default"], [532, 64, 1729, 9], [533, 6, 1729, 10, "style"], [533, 11, 1729, 15], [533, 13, 1729, 17], [534, 8, 1729, 19, "flex"], [534, 12, 1729, 23], [534, 14, 1729, 25], [534, 15, 1729, 26], [535, 8, 1729, 28, "backgroundColor"], [535, 23, 1729, 43], [535, 25, 1729, 45], [536, 6, 1729, 55], [536, 7, 1729, 57], [537, 6, 1729, 57, "children"], [537, 14, 1729, 57], [537, 30, 1733, 6], [537, 34, 1733, 6, "_jsxDevRuntime"], [537, 48, 1733, 6], [537, 49, 1733, 6, "jsxDEV"], [537, 55, 1733, 6], [537, 57, 1733, 7, "_expoStatusBar"], [537, 71, 1733, 7], [537, 72, 1733, 7, "StatusBar"], [537, 81, 1733, 16], [538, 8, 1733, 17, "style"], [538, 13, 1733, 22], [538, 15, 1733, 23], [539, 6, 1733, 29], [540, 8, 1733, 29, "fileName"], [540, 16, 1733, 29], [540, 18, 1733, 29, "_jsxFileName"], [540, 30, 1733, 29], [541, 8, 1733, 29, "lineNumber"], [541, 18, 1733, 29], [542, 8, 1733, 29, "columnNumber"], [542, 20, 1733, 29], [543, 6, 1733, 29], [543, 13, 1733, 31], [543, 14, 1733, 32], [543, 29, 1741, 6], [543, 33, 1741, 6, "_jsxDevRuntime"], [543, 47, 1741, 6], [543, 48, 1741, 6, "jsxDEV"], [543, 54, 1741, 6], [543, 56, 1741, 7, "_View"], [543, 61, 1741, 7], [543, 62, 1741, 7, "default"], [543, 69, 1741, 11], [544, 8, 1745, 8, "style"], [544, 13, 1745, 13], [544, 15, 1745, 15], [545, 10, 1749, 10, "backgroundColor"], [545, 25, 1749, 25], [545, 27, 1749, 27], [545, 33, 1749, 33], [546, 10, 1753, 10, "paddingTop"], [546, 20, 1753, 20], [546, 22, 1753, 22, "insets"], [546, 28, 1753, 28], [546, 29, 1753, 29, "top"], [546, 32, 1753, 32], [546, 35, 1753, 35], [546, 36, 1753, 36], [547, 10, 1757, 10, "paddingHorizontal"], [547, 27, 1757, 27], [547, 29, 1757, 29], [547, 31, 1757, 31], [548, 10, 1761, 10, "paddingBottom"], [548, 23, 1761, 23], [548, 25, 1761, 25], [548, 27, 1761, 27], [549, 10, 1765, 10, "borderBottomWidth"], [549, 27, 1765, 27], [549, 29, 1765, 29], [549, 30, 1765, 30], [550, 10, 1769, 10, "borderBottomColor"], [550, 27, 1769, 27], [550, 29, 1769, 29], [550, 38, 1769, 38], [551, 10, 1773, 10, "zIndex"], [551, 16, 1773, 16], [551, 18, 1773, 18], [552, 8, 1777, 8], [552, 9, 1777, 10], [553, 8, 1777, 10, "children"], [553, 16, 1777, 10], [553, 32, 1785, 8], [553, 36, 1785, 8, "_jsxDevRuntime"], [553, 50, 1785, 8], [553, 51, 1785, 8, "jsxDEV"], [553, 57, 1785, 8], [553, 59, 1785, 9, "_View"], [553, 64, 1785, 9], [553, 65, 1785, 9, "default"], [553, 72, 1785, 13], [554, 10, 1789, 10, "style"], [554, 15, 1789, 15], [554, 17, 1789, 17], [555, 12, 1793, 12, "flexDirection"], [555, 25, 1793, 25], [555, 27, 1793, 27], [555, 32, 1793, 32], [556, 12, 1797, 12, "alignItems"], [556, 22, 1797, 22], [556, 24, 1797, 24], [556, 32, 1797, 32], [557, 12, 1801, 12, "marginBottom"], [557, 24, 1801, 24], [557, 26, 1801, 26], [558, 10, 1805, 10], [558, 11, 1805, 12], [559, 10, 1805, 12, "children"], [559, 18, 1805, 12], [559, 34, 1813, 10], [559, 38, 1813, 10, "_jsxDevRuntime"], [559, 52, 1813, 10], [559, 53, 1813, 10, "jsxDEV"], [559, 59, 1813, 10], [559, 61, 1813, 11, "_TouchableOpacity"], [559, 78, 1813, 11], [559, 79, 1813, 11, "default"], [559, 86, 1813, 27], [560, 12, 1817, 12, "onPress"], [560, 19, 1817, 19], [560, 21, 1817, 21, "onPress"], [560, 22, 1817, 21], [560, 27, 1817, 27, "router"], [560, 45, 1817, 33], [560, 46, 1817, 34, "back"], [560, 50, 1817, 38], [560, 51, 1817, 39], [560, 52, 1817, 41], [561, 12, 1821, 12, "style"], [561, 17, 1821, 17], [561, 19, 1821, 19], [562, 14, 1821, 21, "marginRight"], [562, 25, 1821, 32], [562, 27, 1821, 34], [563, 12, 1821, 37], [563, 13, 1821, 39], [564, 12, 1821, 39, "children"], [564, 20, 1821, 39], [564, 35, 1829, 12], [564, 39, 1829, 12, "_jsxDevRuntime"], [564, 53, 1829, 12], [564, 54, 1829, 12, "jsxDEV"], [564, 60, 1829, 12], [564, 62, 1829, 13, "_lucideReactNative"], [564, 80, 1829, 13], [564, 81, 1829, 13, "ArrowLeft"], [564, 90, 1829, 22], [565, 14, 1829, 23, "size"], [565, 18, 1829, 27], [565, 20, 1829, 29], [565, 22, 1829, 32], [566, 14, 1829, 33, "color"], [566, 19, 1829, 38], [566, 21, 1829, 39], [567, 12, 1829, 48], [568, 14, 1829, 48, "fileName"], [568, 22, 1829, 48], [568, 24, 1829, 48, "_jsxFileName"], [568, 36, 1829, 48], [569, 14, 1829, 48, "lineNumber"], [569, 24, 1829, 48], [570, 14, 1829, 48, "columnNumber"], [570, 26, 1829, 48], [571, 12, 1829, 48], [571, 19, 1829, 50], [572, 10, 1829, 51], [573, 12, 1829, 51, "fileName"], [573, 20, 1829, 51], [573, 22, 1829, 51, "_jsxFileName"], [573, 34, 1829, 51], [574, 12, 1829, 51, "lineNumber"], [574, 22, 1829, 51], [575, 12, 1829, 51, "columnNumber"], [575, 24, 1829, 51], [576, 10, 1829, 51], [576, 17, 1833, 28], [576, 18, 1833, 29], [576, 33, 1837, 10], [576, 37, 1837, 10, "_jsxDevRuntime"], [576, 51, 1837, 10], [576, 52, 1837, 10, "jsxDEV"], [576, 58, 1837, 10], [576, 60, 1837, 11, "_Text"], [576, 65, 1837, 11], [576, 66, 1837, 11, "default"], [576, 73, 1837, 15], [577, 12, 1841, 12, "style"], [577, 17, 1841, 17], [577, 19, 1841, 19], [578, 14, 1845, 14, "fontSize"], [578, 22, 1845, 22], [578, 24, 1845, 24], [578, 26, 1845, 26], [579, 14, 1849, 14, "fontWeight"], [579, 24, 1849, 24], [579, 26, 1849, 26], [579, 32, 1849, 32], [580, 14, 1853, 14, "color"], [580, 19, 1853, 19], [580, 21, 1853, 21], [580, 30, 1853, 30], [581, 14, 1857, 14, "flex"], [581, 18, 1857, 18], [581, 20, 1857, 20], [582, 12, 1861, 12], [582, 13, 1861, 14], [583, 12, 1861, 14, "children"], [583, 20, 1861, 14], [583, 22, 1865, 11], [584, 10, 1873, 10], [585, 12, 1873, 10, "fileName"], [585, 20, 1873, 10], [585, 22, 1873, 10, "_jsxFileName"], [585, 34, 1873, 10], [586, 12, 1873, 10, "lineNumber"], [586, 22, 1873, 10], [587, 12, 1873, 10, "columnNumber"], [587, 24, 1873, 10], [588, 10, 1873, 10], [588, 17, 1873, 16], [588, 18, 1873, 17], [588, 33, 1881, 10], [588, 37, 1881, 10, "_jsxDevRuntime"], [588, 51, 1881, 10], [588, 52, 1881, 10, "jsxDEV"], [588, 58, 1881, 10], [588, 60, 1881, 11, "_Text"], [588, 65, 1881, 11], [588, 66, 1881, 11, "default"], [588, 73, 1881, 15], [589, 12, 1881, 16, "style"], [589, 17, 1881, 21], [589, 19, 1881, 23], [590, 14, 1881, 25, "fontSize"], [590, 22, 1881, 33], [590, 24, 1881, 35], [590, 26, 1881, 37], [591, 14, 1881, 39, "color"], [591, 19, 1881, 44], [591, 21, 1881, 46], [592, 12, 1881, 56], [592, 13, 1881, 58], [593, 12, 1881, 58, "children"], [593, 20, 1881, 58], [593, 22, 1885, 13], [593, 40, 1885, 31, "testingMode"], [593, 51, 1885, 42], [593, 54, 1885, 45], [593, 58, 1885, 49], [593, 61, 1885, 52], [593, 66, 1885, 57], [594, 10, 1885, 59], [595, 12, 1885, 59, "fileName"], [595, 20, 1885, 59], [595, 22, 1885, 59, "_jsxFileName"], [595, 34, 1885, 59], [596, 12, 1885, 59, "lineNumber"], [596, 22, 1885, 59], [597, 12, 1885, 59, "columnNumber"], [597, 24, 1885, 59], [598, 10, 1885, 59], [598, 17, 1889, 16], [598, 18, 1889, 17], [599, 8, 1889, 17], [600, 10, 1889, 17, "fileName"], [600, 18, 1889, 17], [600, 20, 1889, 17, "_jsxFileName"], [600, 32, 1889, 17], [601, 10, 1889, 17, "lineNumber"], [601, 20, 1889, 17], [602, 10, 1889, 17, "columnNumber"], [602, 22, 1889, 17], [603, 8, 1889, 17], [603, 15, 1893, 14], [603, 16, 1893, 15], [603, 31, 1901, 8], [603, 35, 1901, 8, "_jsxDevRuntime"], [603, 49, 1901, 8], [603, 50, 1901, 8, "jsxDEV"], [603, 56, 1901, 8], [603, 58, 1901, 9, "_View"], [603, 63, 1901, 9], [603, 64, 1901, 9, "default"], [603, 71, 1901, 13], [604, 10, 1905, 10, "style"], [604, 15, 1905, 15], [604, 17, 1905, 17], [605, 12, 1909, 12, "backgroundColor"], [605, 27, 1909, 27], [605, 29, 1909, 29], [605, 38, 1909, 38], [606, 12, 1913, 12, "borderRadius"], [606, 24, 1913, 24], [606, 26, 1913, 26], [606, 28, 1913, 28], [607, 12, 1917, 12, "padding"], [607, 19, 1917, 19], [607, 21, 1917, 21], [607, 23, 1917, 23], [608, 12, 1921, 12, "borderLeftWidth"], [608, 27, 1921, 27], [608, 29, 1921, 29], [608, 30, 1921, 30], [609, 12, 1925, 12, "borderLeftColor"], [609, 27, 1925, 27], [609, 29, 1925, 29], [610, 10, 1929, 10], [610, 11, 1929, 12], [611, 10, 1929, 12, "children"], [611, 18, 1929, 12], [611, 34, 1937, 10], [611, 38, 1937, 10, "_jsxDevRuntime"], [611, 52, 1937, 10], [611, 53, 1937, 10, "jsxDEV"], [611, 59, 1937, 10], [611, 61, 1937, 11, "_Text"], [611, 66, 1937, 11], [611, 67, 1937, 11, "default"], [611, 74, 1937, 15], [612, 12, 1941, 12, "style"], [612, 17, 1941, 17], [612, 19, 1941, 19], [613, 14, 1945, 14, "fontSize"], [613, 22, 1945, 22], [613, 24, 1945, 24], [613, 26, 1945, 26], [614, 14, 1949, 14, "color"], [614, 19, 1949, 19], [614, 21, 1949, 21], [614, 30, 1949, 30], [615, 14, 1953, 14, "fontWeight"], [615, 24, 1953, 24], [615, 26, 1953, 26], [615, 31, 1953, 31], [616, 14, 1957, 14, "marginBottom"], [616, 26, 1957, 26], [616, 28, 1957, 28], [617, 12, 1961, 12], [617, 13, 1961, 14], [618, 12, 1961, 14, "children"], [618, 20, 1961, 14], [618, 22, 1969, 13, "question"], [618, 30, 1969, 21], [618, 31, 1969, 22, "question"], [619, 10, 1969, 30], [620, 12, 1969, 30, "fileName"], [620, 20, 1969, 30], [620, 22, 1969, 30, "_jsxFileName"], [620, 34, 1969, 30], [621, 12, 1969, 30, "lineNumber"], [621, 22, 1969, 30], [622, 12, 1969, 30, "columnNumber"], [622, 24, 1969, 30], [623, 10, 1969, 30], [623, 17, 1973, 16], [623, 18, 1973, 17], [623, 33, 1977, 10], [623, 37, 1977, 10, "_jsxDevRuntime"], [623, 51, 1977, 10], [623, 52, 1977, 10, "jsxDEV"], [623, 58, 1977, 10], [623, 60, 1977, 11, "_View"], [623, 65, 1977, 11], [623, 66, 1977, 11, "default"], [623, 73, 1977, 15], [624, 12, 1981, 12, "style"], [624, 17, 1981, 17], [624, 19, 1981, 19], [625, 14, 1985, 14, "flexDirection"], [625, 27, 1985, 27], [625, 29, 1985, 29], [625, 34, 1985, 34], [626, 14, 1989, 14, "alignItems"], [626, 24, 1989, 24], [626, 26, 1989, 26], [626, 34, 1989, 34], [627, 14, 1993, 14, "marginBottom"], [627, 26, 1993, 26], [627, 28, 1993, 28], [628, 12, 1997, 12], [628, 13, 1997, 14], [629, 12, 1997, 14, "children"], [629, 20, 1997, 14], [629, 36, 2005, 12], [629, 40, 2005, 12, "_jsxDevRuntime"], [629, 54, 2005, 12], [629, 55, 2005, 12, "jsxDEV"], [629, 61, 2005, 12], [629, 63, 2005, 13, "_lucideReactNative"], [629, 81, 2005, 13], [629, 82, 2005, 13, "MapPin"], [629, 88, 2005, 19], [630, 14, 2005, 20, "size"], [630, 18, 2005, 24], [630, 20, 2005, 26], [630, 22, 2005, 29], [631, 14, 2005, 30, "color"], [631, 19, 2005, 35], [631, 21, 2005, 36], [632, 12, 2005, 45], [633, 14, 2005, 45, "fileName"], [633, 22, 2005, 45], [633, 24, 2005, 45, "_jsxFileName"], [633, 36, 2005, 45], [634, 14, 2005, 45, "lineNumber"], [634, 24, 2005, 45], [635, 14, 2005, 45, "columnNumber"], [635, 26, 2005, 45], [636, 12, 2005, 45], [636, 19, 2005, 47], [636, 20, 2005, 48], [636, 35, 2009, 12], [636, 39, 2009, 12, "_jsxDevRuntime"], [636, 53, 2009, 12], [636, 54, 2009, 12, "jsxDEV"], [636, 60, 2009, 12], [636, 62, 2009, 13, "_Text"], [636, 67, 2009, 13], [636, 68, 2009, 13, "default"], [636, 75, 2009, 17], [637, 14, 2013, 14, "style"], [637, 19, 2013, 19], [637, 21, 2013, 21], [638, 16, 2013, 23, "fontSize"], [638, 24, 2013, 31], [638, 26, 2013, 33], [638, 28, 2013, 35], [639, 16, 2013, 37, "color"], [639, 21, 2013, 42], [639, 23, 2013, 44], [639, 32, 2013, 53], [640, 16, 2013, 55, "marginLeft"], [640, 26, 2013, 65], [640, 28, 2013, 67], [640, 29, 2013, 68], [641, 16, 2013, 70, "flex"], [641, 20, 2013, 74], [641, 22, 2013, 76], [642, 14, 2013, 78], [642, 15, 2013, 80], [643, 14, 2013, 80, "children"], [643, 22, 2013, 80], [643, 24, 2021, 15, "question"], [643, 32, 2021, 23], [643, 33, 2021, 24, "location"], [644, 12, 2021, 32], [645, 14, 2021, 32, "fileName"], [645, 22, 2021, 32], [645, 24, 2021, 32, "_jsxFileName"], [645, 36, 2021, 32], [646, 14, 2021, 32, "lineNumber"], [646, 24, 2021, 32], [647, 14, 2021, 32, "columnNumber"], [647, 26, 2021, 32], [648, 12, 2021, 32], [648, 19, 2025, 18], [648, 20, 2025, 19], [649, 10, 2025, 19], [650, 12, 2025, 19, "fileName"], [650, 20, 2025, 19], [650, 22, 2025, 19, "_jsxFileName"], [650, 34, 2025, 19], [651, 12, 2025, 19, "lineNumber"], [651, 22, 2025, 19], [652, 12, 2025, 19, "columnNumber"], [652, 24, 2025, 19], [653, 10, 2025, 19], [653, 17, 2029, 16], [653, 18, 2029, 17], [653, 33, 2033, 10], [653, 37, 2033, 10, "_jsxDevRuntime"], [653, 51, 2033, 10], [653, 52, 2033, 10, "jsxDEV"], [653, 58, 2033, 10], [653, 60, 2033, 11, "_View"], [653, 65, 2033, 11], [653, 66, 2033, 11, "default"], [653, 73, 2033, 15], [654, 12, 2037, 12, "style"], [654, 17, 2037, 17], [654, 19, 2037, 19], [655, 14, 2041, 14, "flexDirection"], [655, 27, 2041, 27], [655, 29, 2041, 29], [655, 34, 2041, 34], [656, 14, 2045, 14, "justifyContent"], [656, 28, 2045, 28], [656, 30, 2045, 30], [656, 45, 2045, 45], [657, 14, 2049, 14, "alignItems"], [657, 24, 2049, 24], [657, 26, 2049, 26], [658, 12, 2053, 12], [658, 13, 2053, 14], [659, 12, 2053, 14, "children"], [659, 20, 2053, 14], [659, 36, 2061, 12], [659, 40, 2061, 12, "_jsxDevRuntime"], [659, 54, 2061, 12], [659, 55, 2061, 12, "jsxDEV"], [659, 61, 2061, 12], [659, 63, 2061, 13, "_View"], [659, 68, 2061, 13], [659, 69, 2061, 13, "default"], [659, 76, 2061, 17], [660, 14, 2061, 18, "style"], [660, 19, 2061, 23], [660, 21, 2061, 25], [661, 16, 2061, 27, "flexDirection"], [661, 29, 2061, 40], [661, 31, 2061, 42], [661, 36, 2061, 47], [662, 16, 2061, 49, "alignItems"], [662, 26, 2061, 59], [662, 28, 2061, 61], [663, 14, 2061, 70], [663, 15, 2061, 72], [664, 14, 2061, 72, "children"], [664, 22, 2061, 72], [664, 38, 2065, 14], [664, 42, 2065, 14, "_jsxDevRuntime"], [664, 56, 2065, 14], [664, 57, 2065, 14, "jsxDEV"], [664, 63, 2065, 14], [664, 65, 2065, 15, "_lucideReactNative"], [664, 83, 2065, 15], [664, 84, 2065, 15, "DollarSign"], [664, 94, 2065, 25], [665, 16, 2065, 26, "size"], [665, 20, 2065, 30], [665, 22, 2065, 32], [665, 24, 2065, 35], [666, 16, 2065, 36, "color"], [666, 21, 2065, 41], [666, 23, 2065, 42], [667, 14, 2065, 51], [668, 16, 2065, 51, "fileName"], [668, 24, 2065, 51], [668, 26, 2065, 51, "_jsxFileName"], [668, 38, 2065, 51], [669, 16, 2065, 51, "lineNumber"], [669, 26, 2065, 51], [670, 16, 2065, 51, "columnNumber"], [670, 28, 2065, 51], [671, 14, 2065, 51], [671, 21, 2065, 53], [671, 22, 2065, 54], [671, 37, 2069, 14], [671, 41, 2069, 14, "_jsxDevRuntime"], [671, 55, 2069, 14], [671, 56, 2069, 14, "jsxDEV"], [671, 62, 2069, 14], [671, 64, 2069, 15, "_Text"], [671, 69, 2069, 15], [671, 70, 2069, 15, "default"], [671, 77, 2069, 19], [672, 16, 2073, 16, "style"], [672, 21, 2073, 21], [672, 23, 2073, 23], [673, 18, 2077, 18, "fontSize"], [673, 26, 2077, 26], [673, 28, 2077, 28], [673, 30, 2077, 30], [674, 18, 2081, 18, "color"], [674, 23, 2081, 23], [674, 25, 2081, 25], [674, 34, 2081, 34], [675, 18, 2085, 18, "fontWeight"], [675, 28, 2085, 28], [675, 30, 2085, 30], [675, 35, 2085, 35], [676, 18, 2089, 18, "marginLeft"], [676, 28, 2089, 28], [676, 30, 2089, 30], [677, 16, 2093, 16], [677, 17, 2093, 18], [678, 16, 2093, 18, "children"], [678, 24, 2093, 18], [678, 26, 2101, 17], [678, 30, 2101, 21, "question"], [678, 38, 2101, 29], [678, 39, 2101, 30, "reward"], [678, 45, 2101, 36], [678, 46, 2101, 37, "toFixed"], [678, 53, 2101, 44], [678, 54, 2101, 45], [678, 55, 2101, 46], [678, 56, 2101, 47], [679, 14, 2101, 56], [680, 16, 2101, 56, "fileName"], [680, 24, 2101, 56], [680, 26, 2101, 56, "_jsxFileName"], [680, 38, 2101, 56], [681, 16, 2101, 56, "lineNumber"], [681, 26, 2101, 56], [682, 16, 2101, 56, "columnNumber"], [682, 28, 2101, 56], [683, 14, 2101, 56], [683, 21, 2105, 20], [683, 22, 2105, 21], [684, 12, 2105, 21], [685, 14, 2105, 21, "fileName"], [685, 22, 2105, 21], [685, 24, 2105, 21, "_jsxFileName"], [685, 36, 2105, 21], [686, 14, 2105, 21, "lineNumber"], [686, 24, 2105, 21], [687, 14, 2105, 21, "columnNumber"], [687, 26, 2105, 21], [688, 12, 2105, 21], [688, 19, 2109, 18], [688, 20, 2109, 19], [688, 35, 2113, 12], [688, 39, 2113, 12, "_jsxDevRuntime"], [688, 53, 2113, 12], [688, 54, 2113, 12, "jsxDEV"], [688, 60, 2113, 12], [688, 62, 2113, 13, "_View"], [688, 67, 2113, 13], [688, 68, 2113, 13, "default"], [688, 75, 2113, 17], [689, 14, 2113, 18, "style"], [689, 19, 2113, 23], [689, 21, 2113, 25], [690, 16, 2113, 27, "flexDirection"], [690, 29, 2113, 40], [690, 31, 2113, 42], [690, 36, 2113, 47], [691, 16, 2113, 49, "alignItems"], [691, 26, 2113, 59], [691, 28, 2113, 61], [692, 14, 2113, 70], [692, 15, 2113, 72], [693, 14, 2113, 72, "children"], [693, 22, 2113, 72], [693, 38, 2117, 14], [693, 42, 2117, 14, "_jsxDevRuntime"], [693, 56, 2117, 14], [693, 57, 2117, 14, "jsxDEV"], [693, 63, 2117, 14], [693, 65, 2117, 15, "_lucideReactNative"], [693, 83, 2117, 15], [693, 84, 2117, 15, "Clock"], [693, 89, 2117, 20], [694, 16, 2117, 21, "size"], [694, 20, 2117, 25], [694, 22, 2117, 27], [694, 24, 2117, 30], [695, 16, 2117, 31, "color"], [695, 21, 2117, 36], [695, 23, 2117, 37], [696, 14, 2117, 46], [697, 16, 2117, 46, "fileName"], [697, 24, 2117, 46], [697, 26, 2117, 46, "_jsxFileName"], [697, 38, 2117, 46], [698, 16, 2117, 46, "lineNumber"], [698, 26, 2117, 46], [699, 16, 2117, 46, "columnNumber"], [699, 28, 2117, 46], [700, 14, 2117, 46], [700, 21, 2117, 48], [700, 22, 2117, 49], [700, 37, 2121, 14], [700, 41, 2121, 14, "_jsxDevRuntime"], [700, 55, 2121, 14], [700, 56, 2121, 14, "jsxDEV"], [700, 62, 2121, 14], [700, 64, 2121, 15, "_Text"], [700, 69, 2121, 15], [700, 70, 2121, 15, "default"], [700, 77, 2121, 19], [701, 16, 2121, 20, "style"], [701, 21, 2121, 25], [701, 23, 2121, 27], [702, 18, 2121, 29, "fontSize"], [702, 26, 2121, 37], [702, 28, 2121, 39], [702, 30, 2121, 41], [703, 18, 2121, 43, "color"], [703, 23, 2121, 48], [703, 25, 2121, 50], [703, 34, 2121, 59], [704, 18, 2121, 61, "marginLeft"], [704, 28, 2121, 71], [704, 30, 2121, 73], [705, 16, 2121, 75], [705, 17, 2121, 77], [706, 16, 2121, 77, "children"], [706, 24, 2121, 77], [706, 26, 2125, 17, "question"], [706, 34, 2125, 25], [706, 35, 2125, 26, "postedAt"], [707, 14, 2125, 34], [708, 16, 2125, 34, "fileName"], [708, 24, 2125, 34], [708, 26, 2125, 34, "_jsxFileName"], [708, 38, 2125, 34], [709, 16, 2125, 34, "lineNumber"], [709, 26, 2125, 34], [710, 16, 2125, 34, "columnNumber"], [710, 28, 2125, 34], [711, 14, 2125, 34], [711, 21, 2129, 20], [711, 22, 2129, 21], [712, 12, 2129, 21], [713, 14, 2129, 21, "fileName"], [713, 22, 2129, 21], [713, 24, 2129, 21, "_jsxFileName"], [713, 36, 2129, 21], [714, 14, 2129, 21, "lineNumber"], [714, 24, 2129, 21], [715, 14, 2129, 21, "columnNumber"], [715, 26, 2129, 21], [716, 12, 2129, 21], [716, 19, 2133, 18], [716, 20, 2133, 19], [717, 10, 2133, 19], [718, 12, 2133, 19, "fileName"], [718, 20, 2133, 19], [718, 22, 2133, 19, "_jsxFileName"], [718, 34, 2133, 19], [719, 12, 2133, 19, "lineNumber"], [719, 22, 2133, 19], [720, 12, 2133, 19, "columnNumber"], [720, 24, 2133, 19], [721, 10, 2133, 19], [721, 17, 2137, 16], [721, 18, 2137, 17], [722, 8, 2137, 17], [723, 10, 2137, 17, "fileName"], [723, 18, 2137, 17], [723, 20, 2137, 17, "_jsxFileName"], [723, 32, 2137, 17], [724, 10, 2137, 17, "lineNumber"], [724, 20, 2137, 17], [725, 10, 2137, 17, "columnNumber"], [725, 22, 2137, 17], [726, 8, 2137, 17], [726, 15, 2141, 14], [726, 16, 2141, 15], [727, 6, 2141, 15], [728, 8, 2141, 15, "fileName"], [728, 16, 2141, 15], [728, 18, 2141, 15, "_jsxFileName"], [728, 30, 2141, 15], [729, 8, 2141, 15, "lineNumber"], [729, 18, 2141, 15], [730, 8, 2141, 15, "columnNumber"], [730, 20, 2141, 15], [731, 6, 2141, 15], [731, 13, 2145, 12], [731, 14, 2145, 13], [731, 29, 2149, 6], [731, 33, 2149, 6, "_jsxDevRuntime"], [731, 47, 2149, 6], [731, 48, 2149, 6, "jsxDEV"], [731, 54, 2149, 6], [731, 56, 2149, 7, "_KeyboardAvoidingAnimatedView"], [731, 85, 2149, 7], [731, 86, 2149, 7, "default"], [731, 93, 2149, 35], [732, 8, 2149, 36, "style"], [732, 13, 2149, 41], [732, 15, 2149, 43], [733, 10, 2149, 45, "flex"], [733, 14, 2149, 49], [733, 16, 2149, 51], [734, 8, 2149, 53], [734, 9, 2149, 55], [735, 8, 2149, 56, "behavior"], [735, 16, 2149, 64], [735, 18, 2149, 65], [735, 27, 2149, 74], [736, 8, 2149, 74, "children"], [736, 16, 2149, 74], [736, 32, 2153, 8], [736, 36, 2153, 8, "_jsxDevRuntime"], [736, 50, 2153, 8], [736, 51, 2153, 8, "jsxDEV"], [736, 57, 2153, 8], [736, 59, 2153, 9, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [736, 70, 2153, 9], [736, 71, 2153, 9, "default"], [736, 78, 2153, 19], [737, 10, 2157, 10, "style"], [737, 15, 2157, 15], [737, 17, 2157, 17], [738, 12, 2157, 19, "flex"], [738, 16, 2157, 23], [738, 18, 2157, 25], [739, 10, 2157, 27], [739, 11, 2157, 29], [740, 10, 2161, 10, "contentContainerStyle"], [740, 31, 2161, 31], [740, 33, 2161, 33], [741, 12, 2161, 35, "paddingBottom"], [741, 25, 2161, 48], [741, 27, 2161, 50, "insets"], [741, 33, 2161, 56], [741, 34, 2161, 57, "bottom"], [741, 40, 2161, 63], [741, 43, 2161, 66], [742, 10, 2161, 70], [742, 11, 2161, 72], [743, 10, 2165, 10, "showsVerticalScrollIndicator"], [743, 38, 2165, 38], [743, 40, 2165, 40], [743, 45, 2165, 46], [744, 10, 2165, 46, "children"], [744, 18, 2165, 46], [744, 33, 2173, 10], [744, 37, 2173, 10, "_jsxDevRuntime"], [744, 51, 2173, 10], [744, 52, 2173, 10, "jsxDEV"], [744, 58, 2173, 10], [744, 60, 2173, 11, "_View"], [744, 65, 2173, 11], [744, 66, 2173, 11, "default"], [744, 73, 2173, 15], [745, 12, 2173, 16, "style"], [745, 17, 2173, 21], [745, 19, 2173, 23], [746, 14, 2173, 25, "padding"], [746, 21, 2173, 32], [746, 23, 2173, 34], [747, 12, 2173, 37], [747, 13, 2173, 39], [748, 12, 2173, 39, "children"], [748, 20, 2173, 39], [748, 36, 2181, 12], [748, 40, 2181, 12, "_jsxDevRuntime"], [748, 54, 2181, 12], [748, 55, 2181, 12, "jsxDEV"], [748, 61, 2181, 12], [748, 63, 2181, 13, "LocationStatus"], [748, 77, 2181, 27], [749, 14, 2181, 27, "fileName"], [749, 22, 2181, 27], [749, 24, 2181, 27, "_jsxFileName"], [749, 36, 2181, 27], [750, 14, 2181, 27, "lineNumber"], [750, 24, 2181, 27], [751, 14, 2181, 27, "columnNumber"], [751, 26, 2181, 27], [752, 12, 2181, 27], [752, 19, 2181, 29], [752, 20, 2181, 30], [752, 35, 2187, 12], [752, 39, 2187, 12, "_jsxDevRuntime"], [752, 53, 2187, 12], [752, 54, 2187, 12, "jsxDEV"], [752, 60, 2187, 12], [752, 62, 2187, 13, "_View"], [752, 67, 2187, 13], [752, 68, 2187, 13, "default"], [752, 75, 2187, 17], [753, 14, 2187, 18, "style"], [753, 19, 2187, 23], [753, 21, 2187, 25], [754, 16, 2187, 27, "marginBottom"], [754, 28, 2187, 39], [754, 30, 2187, 41], [755, 14, 2187, 44], [755, 15, 2187, 46], [756, 14, 2187, 46, "children"], [756, 22, 2187, 46], [756, 38, 2189, 14], [756, 42, 2189, 14, "_jsxDevRuntime"], [756, 56, 2189, 14], [756, 57, 2189, 14, "jsxDEV"], [756, 63, 2189, 14], [756, 65, 2189, 15, "_View"], [756, 70, 2189, 15], [756, 71, 2189, 15, "default"], [756, 78, 2189, 19], [757, 16, 2191, 16, "style"], [757, 21, 2191, 21], [757, 23, 2191, 23], [758, 18, 2193, 18, "flexDirection"], [758, 31, 2193, 31], [758, 33, 2193, 33], [758, 38, 2193, 38], [759, 18, 2195, 18, "alignItems"], [759, 28, 2195, 28], [759, 30, 2195, 30], [759, 38, 2195, 38], [760, 18, 2197, 18, "marginBottom"], [760, 30, 2197, 30], [760, 32, 2197, 32], [761, 16, 2199, 16], [761, 17, 2199, 18], [762, 16, 2199, 18, "children"], [762, 24, 2199, 18], [762, 40, 2203, 16], [762, 44, 2203, 16, "_jsxDevRuntime"], [762, 58, 2203, 16], [762, 59, 2203, 16, "jsxDEV"], [762, 65, 2203, 16], [762, 67, 2203, 17, "_View"], [762, 72, 2203, 17], [762, 73, 2203, 17, "default"], [762, 80, 2203, 21], [763, 18, 2205, 18, "style"], [763, 23, 2205, 23], [763, 25, 2205, 25], [764, 20, 2207, 20, "width"], [764, 25, 2207, 25], [764, 27, 2207, 27], [764, 29, 2207, 29], [765, 20, 2209, 20, "height"], [765, 26, 2209, 26], [765, 28, 2209, 28], [765, 30, 2209, 30], [766, 20, 2211, 20, "borderRadius"], [766, 32, 2211, 32], [766, 34, 2211, 34], [766, 36, 2211, 36], [767, 20, 2213, 20, "backgroundColor"], [767, 35, 2213, 35], [767, 37, 2213, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [767, 53, 2213, 53], [767, 56, 2215, 24], [767, 65, 2215, 33], [767, 68, 2217, 24, "locationStatus"], [767, 82, 2217, 38], [767, 87, 2217, 43], [767, 97, 2217, 53], [767, 101, 2217, 57, "testingMode"], [767, 112, 2217, 68], [767, 115, 2219, 26], [767, 124, 2219, 35], [767, 127, 2221, 26], [767, 136, 2221, 35], [768, 20, 2223, 20, "alignItems"], [768, 30, 2223, 30], [768, 32, 2223, 32], [768, 40, 2223, 40], [769, 20, 2225, 20, "justifyContent"], [769, 34, 2225, 34], [769, 36, 2225, 36], [769, 44, 2225, 44], [770, 20, 2227, 20, "marginRight"], [770, 31, 2227, 31], [770, 33, 2227, 33], [771, 18, 2229, 18], [771, 19, 2229, 20], [772, 18, 2229, 20, "children"], [772, 26, 2229, 20], [772, 28, 2233, 19, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [772, 44, 2233, 35], [772, 60, 2235, 20], [772, 64, 2235, 20, "_jsxDevRuntime"], [772, 78, 2235, 20], [772, 79, 2235, 20, "jsxDEV"], [772, 85, 2235, 20], [772, 87, 2235, 21, "_lucideReactNative"], [772, 105, 2235, 21], [772, 106, 2235, 21, "CheckCircle2"], [772, 118, 2235, 33], [773, 20, 2235, 34, "size"], [773, 24, 2235, 38], [773, 26, 2235, 40], [773, 28, 2235, 43], [774, 20, 2235, 44, "color"], [774, 25, 2235, 49], [774, 27, 2235, 50], [775, 18, 2235, 56], [776, 20, 2235, 56, "fileName"], [776, 28, 2235, 56], [776, 30, 2235, 56, "_jsxFileName"], [776, 42, 2235, 56], [777, 20, 2235, 56, "lineNumber"], [777, 30, 2235, 56], [778, 20, 2235, 56, "columnNumber"], [778, 32, 2235, 56], [779, 18, 2235, 56], [779, 25, 2235, 58], [779, 26, 2235, 59], [779, 42, 2239, 20], [779, 46, 2239, 20, "_jsxDevRuntime"], [779, 60, 2239, 20], [779, 61, 2239, 20, "jsxDEV"], [779, 67, 2239, 20], [779, 69, 2239, 21, "_Text"], [779, 74, 2239, 21], [779, 75, 2239, 21, "default"], [779, 82, 2239, 25], [780, 20, 2239, 26, "style"], [780, 25, 2239, 31], [780, 27, 2239, 33], [781, 22, 2239, 35, "color"], [781, 27, 2239, 40], [781, 29, 2239, 42], [781, 35, 2239, 48], [782, 22, 2239, 50, "fontSize"], [782, 30, 2239, 58], [782, 32, 2239, 60], [782, 34, 2239, 62], [783, 22, 2239, 64, "fontWeight"], [783, 32, 2239, 74], [783, 34, 2239, 76], [784, 20, 2239, 82], [784, 21, 2239, 84], [785, 20, 2239, 84, "children"], [785, 28, 2239, 84], [785, 30, 2239, 85], [786, 18, 2239, 86], [787, 20, 2239, 86, "fileName"], [787, 28, 2239, 86], [787, 30, 2239, 86, "_jsxFileName"], [787, 42, 2239, 86], [788, 20, 2239, 86, "lineNumber"], [788, 30, 2239, 86], [789, 20, 2239, 86, "columnNumber"], [789, 32, 2239, 86], [790, 18, 2239, 86], [790, 25, 2239, 92], [791, 16, 2241, 19], [792, 18, 2241, 19, "fileName"], [792, 26, 2241, 19], [792, 28, 2241, 19, "_jsxFileName"], [792, 40, 2241, 19], [793, 18, 2241, 19, "lineNumber"], [793, 28, 2241, 19], [794, 18, 2241, 19, "columnNumber"], [794, 30, 2241, 19], [795, 16, 2241, 19], [795, 23, 2243, 22], [795, 24, 2243, 23], [795, 39, 2245, 16], [795, 43, 2245, 16, "_jsxDevRuntime"], [795, 57, 2245, 16], [795, 58, 2245, 16, "jsxDEV"], [795, 64, 2245, 16], [795, 66, 2245, 17, "_Text"], [795, 71, 2245, 17], [795, 72, 2245, 17, "default"], [795, 79, 2245, 21], [796, 18, 2247, 18, "style"], [796, 23, 2247, 23], [796, 25, 2247, 25], [797, 20, 2249, 20, "fontSize"], [797, 28, 2249, 28], [797, 30, 2249, 30], [797, 32, 2249, 32], [798, 20, 2251, 20, "fontWeight"], [798, 30, 2251, 30], [798, 32, 2251, 32], [798, 37, 2251, 37], [799, 20, 2253, 20, "color"], [799, 25, 2253, 25], [799, 27, 2253, 27], [800, 18, 2255, 18], [800, 19, 2255, 20], [801, 18, 2255, 20, "children"], [801, 26, 2255, 20], [801, 28, 2257, 17], [802, 16, 2261, 16], [803, 18, 2261, 16, "fileName"], [803, 26, 2261, 16], [803, 28, 2261, 16, "_jsxFileName"], [803, 40, 2261, 16], [804, 18, 2261, 16, "lineNumber"], [804, 28, 2261, 16], [805, 18, 2261, 16, "columnNumber"], [805, 30, 2261, 16], [806, 16, 2261, 16], [806, 23, 2261, 22], [806, 24, 2261, 23], [807, 14, 2261, 23], [808, 16, 2261, 23, "fileName"], [808, 24, 2261, 23], [808, 26, 2261, 23, "_jsxFileName"], [808, 38, 2261, 23], [809, 16, 2261, 23, "lineNumber"], [809, 26, 2261, 23], [810, 16, 2261, 23, "columnNumber"], [810, 28, 2261, 23], [811, 14, 2261, 23], [811, 21, 2263, 20], [811, 22, 2263, 21], [811, 37, 2265, 14], [811, 41, 2265, 14, "_jsxDevRuntime"], [811, 55, 2265, 14], [811, 56, 2265, 14, "jsxDEV"], [811, 62, 2265, 14], [811, 64, 2265, 15, "_Text"], [811, 69, 2265, 15], [811, 70, 2265, 15, "default"], [811, 77, 2265, 19], [812, 16, 2267, 16, "style"], [812, 21, 2267, 21], [812, 23, 2267, 23], [813, 18, 2269, 18, "fontSize"], [813, 26, 2269, 26], [813, 28, 2269, 28], [813, 30, 2269, 30], [814, 18, 2271, 18, "color"], [814, 23, 2271, 23], [814, 25, 2271, 25], [814, 34, 2271, 34], [815, 18, 2273, 18, "marginBottom"], [815, 30, 2273, 30], [815, 32, 2273, 32], [815, 34, 2273, 34], [816, 18, 2275, 18, "lineHeight"], [816, 28, 2275, 28], [816, 30, 2275, 30], [817, 16, 2277, 16], [817, 17, 2277, 18], [818, 16, 2277, 18, "children"], [818, 24, 2277, 18], [818, 26, 2281, 17], [819, 14, 2281, 148], [820, 16, 2281, 148, "fileName"], [820, 24, 2281, 148], [820, 26, 2281, 148, "_jsxFileName"], [820, 38, 2281, 148], [821, 16, 2281, 148, "lineNumber"], [821, 26, 2281, 148], [822, 16, 2281, 148, "columnNumber"], [822, 28, 2281, 148], [823, 14, 2281, 148], [823, 21, 2283, 20], [823, 22, 2283, 21], [823, 24, 2285, 15, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [823, 40, 2285, 31], [823, 56, 2287, 16], [823, 60, 2287, 16, "_jsxDevRuntime"], [823, 74, 2287, 16], [823, 75, 2287, 16, "jsxDEV"], [823, 81, 2287, 16], [823, 83, 2287, 17, "_View"], [823, 88, 2287, 17], [823, 89, 2287, 17, "default"], [823, 96, 2287, 21], [824, 16, 2289, 18, "style"], [824, 21, 2289, 23], [824, 23, 2289, 25], [825, 18, 2291, 20, "backgroundColor"], [825, 33, 2291, 35], [825, 35, 2291, 37], [825, 41, 2291, 43], [826, 18, 2293, 20, "borderRadius"], [826, 30, 2293, 32], [826, 32, 2293, 34], [826, 34, 2293, 36], [827, 18, 2295, 20, "overflow"], [827, 26, 2295, 28], [827, 28, 2295, 30], [827, 36, 2295, 38], [828, 18, 2297, 20, "borderWidth"], [828, 29, 2297, 31], [828, 31, 2297, 33], [828, 32, 2297, 34], [829, 18, 2299, 20, "borderColor"], [829, 29, 2299, 31], [829, 31, 2299, 33], [829, 40, 2299, 42], [830, 18, 2301, 20], [830, 21, 2301, 23, "Platform"], [830, 38, 2301, 31], [830, 39, 2301, 32, "select"], [830, 45, 2301, 38], [830, 46, 2301, 39], [831, 20, 2303, 22, "ios"], [831, 23, 2303, 25], [831, 25, 2303, 27], [832, 22, 2305, 24, "shadowColor"], [832, 33, 2305, 35], [832, 35, 2305, 37], [832, 41, 2305, 43], [833, 22, 2307, 24, "shadowOffset"], [833, 34, 2307, 36], [833, 36, 2307, 38], [834, 24, 2307, 40, "width"], [834, 29, 2307, 45], [834, 31, 2307, 47], [834, 32, 2307, 48], [835, 24, 2307, 50, "height"], [835, 30, 2307, 56], [835, 32, 2307, 58], [836, 22, 2307, 60], [836, 23, 2307, 61], [837, 22, 2309, 24, "shadowOpacity"], [837, 35, 2309, 37], [837, 37, 2309, 39], [837, 41, 2309, 43], [838, 22, 2311, 24, "shadowRadius"], [838, 34, 2311, 36], [838, 36, 2311, 38], [839, 20, 2313, 22], [839, 21, 2313, 23], [840, 20, 2315, 22, "android"], [840, 27, 2315, 29], [840, 29, 2315, 31], [841, 22, 2317, 24, "elevation"], [841, 31, 2317, 33], [841, 33, 2317, 35], [842, 20, 2319, 22], [842, 21, 2319, 23], [843, 20, 2321, 22, "web"], [843, 23, 2321, 25], [843, 25, 2321, 27], [844, 22, 2323, 24, "boxShadow"], [844, 31, 2323, 33], [844, 33, 2323, 35], [845, 20, 2325, 22], [846, 18, 2327, 20], [846, 19, 2327, 21], [847, 16, 2329, 18], [847, 17, 2329, 20], [848, 16, 2329, 20, "children"], [848, 24, 2329, 20], [848, 40, 2333, 18], [848, 44, 2333, 18, "_jsxDevRuntime"], [848, 58, 2333, 18], [848, 59, 2333, 18, "jsxDEV"], [848, 65, 2333, 18], [848, 67, 2333, 19, "_View"], [848, 72, 2333, 19], [848, 73, 2333, 19, "default"], [848, 80, 2333, 23], [849, 18, 2333, 24, "style"], [849, 23, 2333, 29], [849, 25, 2333, 31], [850, 20, 2333, 33, "position"], [850, 28, 2333, 41], [850, 30, 2333, 43], [851, 18, 2333, 54], [851, 19, 2333, 56], [852, 18, 2333, 56, "children"], [852, 26, 2333, 56], [852, 42, 2335, 20], [852, 46, 2335, 20, "_jsxDevRuntime"], [852, 60, 2335, 20], [852, 61, 2335, 20, "jsxDEV"], [852, 67, 2335, 20], [852, 69, 2335, 21, "_Image"], [852, 75, 2335, 21], [852, 76, 2335, 21, "default"], [852, 83, 2335, 26], [853, 20, 2337, 22, "source"], [853, 26, 2337, 28], [853, 28, 2337, 30], [854, 22, 2337, 32, "uri"], [854, 25, 2337, 35], [854, 27, 2337, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [855, 20, 2337, 54], [855, 21, 2337, 56], [856, 20, 2339, 22, "style"], [856, 25, 2339, 27], [856, 27, 2339, 29], [857, 22, 2340, 24, "width"], [857, 27, 2340, 29], [857, 29, 2340, 31], [857, 35, 2340, 37], [858, 22, 2341, 24, "height"], [858, 28, 2341, 30], [858, 30, 2341, 32], [858, 33, 2341, 35], [859, 22, 2341, 37], [860, 22, 2342, 24, "backgroundColor"], [860, 37, 2342, 39], [860, 39, 2342, 41], [860, 48, 2342, 50], [861, 22, 2343, 24, "borderRadius"], [861, 34, 2343, 36], [861, 36, 2343, 38], [861, 38, 2343, 40], [861, 39, 2343, 42], [862, 20, 2344, 22], [862, 21, 2344, 24], [863, 20, 2346, 22, "resizeMode"], [863, 30, 2346, 32], [863, 32, 2346, 33], [863, 41, 2346, 42], [863, 42, 2346, 43], [864, 20, 2346, 43], [866, 20, 2348, 22, "onError"], [866, 27, 2348, 29], [866, 29, 2348, 32, "e"], [866, 30, 2348, 33], [866, 34, 2348, 38], [867, 22, 2350, 24, "console"], [867, 29, 2350, 31], [867, 30, 2350, 32, "error"], [867, 35, 2350, 37], [867, 36, 2350, 38], [867, 73, 2350, 75], [867, 75, 2350, 77], [868, 24, 2352, 26, "error"], [868, 29, 2352, 31], [868, 31, 2352, 33, "e"], [868, 32, 2352, 34], [868, 33, 2352, 35, "nativeEvent"], [868, 44, 2352, 46], [868, 46, 2352, 48, "error"], [868, 51, 2352, 53], [869, 24, 2354, 26, "uri"], [869, 27, 2354, 29], [869, 29, 2354, 31, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [869, 45, 2354, 47], [870, 24, 2356, 26, "cameraResult"], [870, 36, 2356, 38], [870, 38, 2356, 40, "JSON"], [870, 42, 2356, 44], [870, 43, 2356, 45, "stringify"], [870, 52, 2356, 54], [870, 53, 2356, 55, "cameraResult"], [870, 65, 2356, 67], [870, 67, 2356, 69], [870, 71, 2356, 73], [870, 73, 2356, 75], [870, 74, 2356, 76], [871, 22, 2358, 24], [871, 23, 2358, 25], [871, 24, 2358, 26], [872, 20, 2360, 22], [872, 21, 2360, 24], [873, 20, 2362, 22, "onLoad"], [873, 26, 2362, 28], [873, 28, 2362, 30, "onLoad"], [873, 29, 2362, 30], [873, 34, 2362, 36], [874, 22, 2364, 24, "console"], [874, 29, 2364, 31], [874, 30, 2364, 32, "log"], [874, 33, 2364, 35], [874, 34, 2364, 36], [874, 78, 2364, 80], [874, 80, 2364, 82, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [874, 96, 2364, 98], [874, 97, 2364, 99], [875, 20, 2366, 22], [875, 21, 2366, 24], [876, 20, 2368, 22, "onLoadStart"], [876, 31, 2368, 33], [876, 33, 2368, 35, "onLoadStart"], [876, 34, 2368, 35], [876, 39, 2368, 41], [877, 22, 2370, 24, "console"], [877, 29, 2370, 31], [877, 30, 2370, 32, "log"], [877, 33, 2370, 35], [877, 34, 2370, 36], [877, 69, 2370, 71], [877, 71, 2370, 73, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [877, 87, 2370, 89], [877, 88, 2370, 90], [878, 20, 2372, 22], [878, 21, 2372, 24], [879, 20, 2374, 22, "onLoadEnd"], [879, 29, 2374, 31], [879, 31, 2374, 33, "onLoadEnd"], [879, 32, 2374, 33], [879, 37, 2374, 39], [880, 22, 2376, 24, "console"], [880, 29, 2376, 31], [880, 30, 2376, 32, "log"], [880, 33, 2376, 35], [880, 34, 2376, 36], [880, 70, 2376, 72], [880, 71, 2376, 73], [881, 20, 2378, 22], [882, 18, 2378, 24], [883, 20, 2378, 24, "fileName"], [883, 28, 2378, 24], [883, 30, 2378, 24, "_jsxFileName"], [883, 42, 2378, 24], [884, 20, 2378, 24, "lineNumber"], [884, 30, 2378, 24], [885, 20, 2378, 24, "columnNumber"], [885, 32, 2378, 24], [886, 18, 2378, 24], [886, 25, 2380, 21], [886, 26, 2380, 22], [886, 41, 2382, 20], [886, 45, 2382, 20, "_jsxDevRuntime"], [886, 59, 2382, 20], [886, 60, 2382, 20, "jsxDEV"], [886, 66, 2382, 20], [886, 68, 2382, 21, "_View"], [886, 73, 2382, 21], [886, 74, 2382, 21, "default"], [886, 81, 2382, 25], [887, 20, 2384, 22, "style"], [887, 25, 2384, 27], [887, 27, 2384, 29], [888, 22, 2386, 24, "position"], [888, 30, 2386, 32], [888, 32, 2386, 34], [888, 42, 2386, 44], [889, 22, 2388, 24, "top"], [889, 25, 2388, 27], [889, 27, 2388, 29], [889, 29, 2388, 31], [890, 22, 2390, 24, "right"], [890, 27, 2390, 29], [890, 29, 2390, 31], [890, 31, 2390, 33], [891, 22, 2392, 24, "backgroundColor"], [891, 37, 2392, 39], [891, 39, 2392, 41], [891, 65, 2392, 67], [892, 22, 2394, 24, "paddingHorizontal"], [892, 39, 2394, 41], [892, 41, 2394, 43], [892, 43, 2394, 45], [893, 22, 2396, 24, "paddingVertical"], [893, 37, 2396, 39], [893, 39, 2396, 41], [893, 40, 2396, 42], [894, 22, 2398, 24, "borderRadius"], [894, 34, 2398, 36], [894, 36, 2398, 38], [894, 38, 2398, 40], [895, 22, 2400, 24, "flexDirection"], [895, 35, 2400, 37], [895, 37, 2400, 39], [895, 42, 2400, 44], [896, 22, 2402, 24, "alignItems"], [896, 32, 2402, 34], [896, 34, 2402, 36], [897, 20, 2404, 22], [897, 21, 2404, 24], [898, 20, 2404, 24, "children"], [898, 28, 2404, 24], [898, 44, 2408, 22], [898, 48, 2408, 22, "_jsxDevRuntime"], [898, 62, 2408, 22], [898, 63, 2408, 22, "jsxDEV"], [898, 69, 2408, 22], [898, 71, 2408, 23, "_lucideReactNative"], [898, 89, 2408, 23], [898, 90, 2408, 23, "Shield"], [898, 96, 2408, 29], [899, 22, 2408, 30, "size"], [899, 26, 2408, 34], [899, 28, 2408, 36], [899, 30, 2408, 39], [900, 22, 2408, 40, "color"], [900, 27, 2408, 45], [900, 29, 2408, 46], [901, 20, 2408, 52], [902, 22, 2408, 52, "fileName"], [902, 30, 2408, 52], [902, 32, 2408, 52, "_jsxFileName"], [902, 44, 2408, 52], [903, 22, 2408, 52, "lineNumber"], [903, 32, 2408, 52], [904, 22, 2408, 52, "columnNumber"], [904, 34, 2408, 52], [905, 20, 2408, 52], [905, 27, 2408, 54], [905, 28, 2408, 55], [905, 43, 2410, 22], [905, 47, 2410, 22, "_jsxDevRuntime"], [905, 61, 2410, 22], [905, 62, 2410, 22, "jsxDEV"], [905, 68, 2410, 22], [905, 70, 2410, 23, "_Text"], [905, 75, 2410, 23], [905, 76, 2410, 23, "default"], [905, 83, 2410, 27], [906, 22, 2410, 28, "style"], [906, 27, 2410, 33], [906, 29, 2410, 35], [907, 24, 2410, 37, "color"], [907, 29, 2410, 42], [907, 31, 2410, 44], [907, 37, 2410, 50], [908, 24, 2410, 52, "fontSize"], [908, 32, 2410, 60], [908, 34, 2410, 62], [908, 36, 2410, 64], [909, 24, 2410, 66, "fontWeight"], [909, 34, 2410, 76], [909, 36, 2410, 78], [909, 41, 2410, 83], [910, 24, 2410, 85, "marginLeft"], [910, 34, 2410, 95], [910, 36, 2410, 97], [911, 22, 2410, 99], [911, 23, 2410, 101], [912, 22, 2410, 101, "children"], [912, 30, 2410, 101], [912, 32, 2410, 102], [913, 20, 2414, 22], [914, 22, 2414, 22, "fileName"], [914, 30, 2414, 22], [914, 32, 2414, 22, "_jsxFileName"], [914, 44, 2414, 22], [915, 22, 2414, 22, "lineNumber"], [915, 32, 2414, 22], [916, 22, 2414, 22, "columnNumber"], [916, 34, 2414, 22], [917, 20, 2414, 22], [917, 27, 2414, 28], [917, 28, 2414, 29], [918, 18, 2414, 29], [919, 20, 2414, 29, "fileName"], [919, 28, 2414, 29], [919, 30, 2414, 29, "_jsxFileName"], [919, 42, 2414, 29], [920, 20, 2414, 29, "lineNumber"], [920, 30, 2414, 29], [921, 20, 2414, 29, "columnNumber"], [921, 32, 2414, 29], [922, 18, 2414, 29], [922, 25, 2416, 26], [922, 26, 2416, 27], [923, 16, 2416, 27], [924, 18, 2416, 27, "fileName"], [924, 26, 2416, 27], [924, 28, 2416, 27, "_jsxFileName"], [924, 40, 2416, 27], [925, 18, 2416, 27, "lineNumber"], [925, 28, 2416, 27], [926, 18, 2416, 27, "columnNumber"], [926, 30, 2416, 27], [927, 16, 2416, 27], [927, 23, 2418, 24], [927, 24, 2418, 25], [927, 39, 2420, 18], [927, 43, 2420, 18, "_jsxDevRuntime"], [927, 57, 2420, 18], [927, 58, 2420, 18, "jsxDEV"], [927, 64, 2420, 18], [927, 66, 2420, 19, "_View"], [927, 71, 2420, 19], [927, 72, 2420, 19, "default"], [927, 79, 2420, 23], [928, 18, 2420, 24, "style"], [928, 23, 2420, 29], [928, 25, 2420, 31], [929, 20, 2420, 33, "padding"], [929, 27, 2420, 40], [929, 29, 2420, 42], [930, 18, 2420, 45], [930, 19, 2420, 47], [931, 18, 2420, 47, "children"], [931, 26, 2420, 47], [931, 42, 2422, 20], [931, 46, 2422, 20, "_jsxDevRuntime"], [931, 60, 2422, 20], [931, 61, 2422, 20, "jsxDEV"], [931, 67, 2422, 20], [931, 69, 2422, 21, "_View"], [931, 74, 2422, 21], [931, 75, 2422, 21, "default"], [931, 82, 2422, 25], [932, 20, 2424, 22, "style"], [932, 25, 2424, 27], [932, 27, 2424, 29], [933, 22, 2426, 24, "flexDirection"], [933, 35, 2426, 37], [933, 37, 2426, 39], [933, 42, 2426, 44], [934, 22, 2428, 24, "alignItems"], [934, 32, 2428, 34], [934, 34, 2428, 36], [934, 42, 2428, 44], [935, 22, 2430, 24, "marginBottom"], [935, 34, 2430, 36], [935, 36, 2430, 38], [936, 20, 2432, 22], [936, 21, 2432, 24], [937, 20, 2432, 24, "children"], [937, 28, 2432, 24], [937, 44, 2436, 22], [937, 48, 2436, 22, "_jsxDevRuntime"], [937, 62, 2436, 22], [937, 63, 2436, 22, "jsxDEV"], [937, 69, 2436, 22], [937, 71, 2436, 23, "_View"], [937, 76, 2436, 23], [937, 77, 2436, 23, "default"], [937, 84, 2436, 27], [938, 22, 2438, 24, "style"], [938, 27, 2438, 29], [938, 29, 2438, 31], [939, 24, 2440, 26, "width"], [939, 29, 2440, 31], [939, 31, 2440, 33], [939, 33, 2440, 35], [940, 24, 2442, 26, "height"], [940, 30, 2442, 32], [940, 32, 2442, 34], [940, 34, 2442, 36], [941, 24, 2444, 26, "borderRadius"], [941, 36, 2444, 38], [941, 38, 2444, 40], [941, 40, 2444, 42], [942, 24, 2446, 26, "backgroundColor"], [942, 39, 2446, 41], [942, 41, 2446, 43], [942, 50, 2446, 52], [943, 24, 2448, 26, "alignItems"], [943, 34, 2448, 36], [943, 36, 2448, 38], [943, 44, 2448, 46], [944, 24, 2450, 26, "justifyContent"], [944, 38, 2450, 40], [944, 40, 2450, 42], [945, 22, 2452, 24], [945, 23, 2452, 26], [946, 22, 2452, 26, "children"], [946, 30, 2452, 26], [946, 45, 2456, 24], [946, 49, 2456, 24, "_jsxDevRuntime"], [946, 63, 2456, 24], [946, 64, 2456, 24, "jsxDEV"], [946, 70, 2456, 24], [946, 72, 2456, 25, "_lucideReactNative"], [946, 90, 2456, 25], [946, 91, 2456, 25, "CheckCircle2"], [946, 103, 2456, 37], [947, 24, 2456, 38, "size"], [947, 28, 2456, 42], [947, 30, 2456, 44], [947, 32, 2456, 47], [948, 24, 2456, 48, "color"], [948, 29, 2456, 53], [948, 31, 2456, 54], [949, 22, 2456, 63], [950, 24, 2456, 63, "fileName"], [950, 32, 2456, 63], [950, 34, 2456, 63, "_jsxFileName"], [950, 46, 2456, 63], [951, 24, 2456, 63, "lineNumber"], [951, 34, 2456, 63], [952, 24, 2456, 63, "columnNumber"], [952, 36, 2456, 63], [953, 22, 2456, 63], [953, 29, 2456, 65], [954, 20, 2456, 66], [955, 22, 2456, 66, "fileName"], [955, 30, 2456, 66], [955, 32, 2456, 66, "_jsxFileName"], [955, 44, 2456, 66], [956, 22, 2456, 66, "lineNumber"], [956, 32, 2456, 66], [957, 22, 2456, 66, "columnNumber"], [957, 34, 2456, 66], [958, 20, 2456, 66], [958, 27, 2458, 28], [958, 28, 2458, 29], [958, 43, 2460, 22], [958, 47, 2460, 22, "_jsxDevRuntime"], [958, 61, 2460, 22], [958, 62, 2460, 22, "jsxDEV"], [958, 68, 2460, 22], [958, 70, 2460, 23, "_View"], [958, 75, 2460, 23], [958, 76, 2460, 23, "default"], [958, 83, 2460, 27], [959, 22, 2460, 28, "style"], [959, 27, 2460, 33], [959, 29, 2460, 35], [960, 24, 2460, 37, "marginLeft"], [960, 34, 2460, 47], [960, 36, 2460, 49], [960, 38, 2460, 51], [961, 24, 2460, 53, "flex"], [961, 28, 2460, 57], [961, 30, 2460, 59], [962, 22, 2460, 61], [962, 23, 2460, 63], [963, 22, 2460, 63, "children"], [963, 30, 2460, 63], [963, 46, 2462, 24], [963, 50, 2462, 24, "_jsxDevRuntime"], [963, 64, 2462, 24], [963, 65, 2462, 24, "jsxDEV"], [963, 71, 2462, 24], [963, 73, 2462, 25, "_Text"], [963, 78, 2462, 25], [963, 79, 2462, 25, "default"], [963, 86, 2462, 29], [964, 24, 2462, 30, "style"], [964, 29, 2462, 35], [964, 31, 2462, 37], [965, 26, 2462, 39, "fontSize"], [965, 34, 2462, 47], [965, 36, 2462, 49], [965, 38, 2462, 51], [966, 26, 2462, 53, "fontWeight"], [966, 36, 2462, 63], [966, 38, 2462, 65], [966, 43, 2462, 70], [967, 26, 2462, 72, "color"], [967, 31, 2462, 77], [967, 33, 2462, 79], [968, 24, 2462, 89], [968, 25, 2462, 91], [969, 24, 2462, 91, "children"], [969, 32, 2462, 91], [969, 34, 2462, 92], [970, 22, 2466, 24], [971, 24, 2466, 24, "fileName"], [971, 32, 2466, 24], [971, 34, 2466, 24, "_jsxFileName"], [971, 46, 2466, 24], [972, 24, 2466, 24, "lineNumber"], [972, 34, 2466, 24], [973, 24, 2466, 24, "columnNumber"], [973, 36, 2466, 24], [974, 22, 2466, 24], [974, 29, 2466, 30], [974, 30, 2466, 31], [974, 45, 2468, 24], [974, 49, 2468, 24, "_jsxDevRuntime"], [974, 63, 2468, 24], [974, 64, 2468, 24, "jsxDEV"], [974, 70, 2468, 24], [974, 72, 2468, 25, "_Text"], [974, 77, 2468, 25], [974, 78, 2468, 25, "default"], [974, 85, 2468, 29], [975, 24, 2468, 30, "style"], [975, 29, 2468, 35], [975, 31, 2468, 37], [976, 26, 2468, 39, "fontSize"], [976, 34, 2468, 47], [976, 36, 2468, 49], [976, 38, 2468, 51], [977, 26, 2468, 53, "color"], [977, 31, 2468, 58], [977, 33, 2468, 60], [977, 42, 2468, 69], [978, 26, 2468, 71, "marginTop"], [978, 35, 2468, 80], [978, 37, 2468, 82], [979, 24, 2468, 84], [979, 25, 2468, 86], [980, 24, 2468, 86, "children"], [980, 32, 2468, 86], [980, 34, 2468, 87], [981, 22, 2472, 24], [982, 24, 2472, 24, "fileName"], [982, 32, 2472, 24], [982, 34, 2472, 24, "_jsxFileName"], [982, 46, 2472, 24], [983, 24, 2472, 24, "lineNumber"], [983, 34, 2472, 24], [984, 24, 2472, 24, "columnNumber"], [984, 36, 2472, 24], [985, 22, 2472, 24], [985, 29, 2472, 30], [985, 30, 2472, 31], [986, 20, 2472, 31], [987, 22, 2472, 31, "fileName"], [987, 30, 2472, 31], [987, 32, 2472, 31, "_jsxFileName"], [987, 44, 2472, 31], [988, 22, 2472, 31, "lineNumber"], [988, 32, 2472, 31], [989, 22, 2472, 31, "columnNumber"], [989, 34, 2472, 31], [990, 20, 2472, 31], [990, 27, 2474, 28], [990, 28, 2474, 29], [991, 18, 2474, 29], [992, 20, 2474, 29, "fileName"], [992, 28, 2474, 29], [992, 30, 2474, 29, "_jsxFileName"], [992, 42, 2474, 29], [993, 20, 2474, 29, "lineNumber"], [993, 30, 2474, 29], [994, 20, 2474, 29, "columnNumber"], [994, 32, 2474, 29], [995, 18, 2474, 29], [995, 25, 2476, 26], [995, 26, 2476, 27], [995, 41, 2478, 20], [995, 45, 2478, 20, "_jsxDevRuntime"], [995, 59, 2478, 20], [995, 60, 2478, 20, "jsxDEV"], [995, 66, 2478, 20], [995, 68, 2478, 21, "_View"], [995, 73, 2478, 21], [995, 74, 2478, 21, "default"], [995, 81, 2478, 25], [996, 20, 2480, 22, "style"], [996, 25, 2480, 27], [996, 27, 2480, 29], [997, 22, 2482, 24, "backgroundColor"], [997, 37, 2482, 39], [997, 39, 2482, 41], [997, 48, 2482, 50], [998, 22, 2484, 24, "borderRadius"], [998, 34, 2484, 36], [998, 36, 2484, 38], [998, 38, 2484, 40], [999, 22, 2486, 24, "padding"], [999, 29, 2486, 31], [999, 31, 2486, 33], [999, 33, 2486, 35], [1000, 22, 2488, 24, "marginBottom"], [1000, 34, 2488, 36], [1000, 36, 2488, 38], [1001, 20, 2490, 22], [1001, 21, 2490, 24], [1002, 20, 2490, 24, "children"], [1002, 28, 2490, 24], [1002, 44, 2494, 22], [1002, 48, 2494, 22, "_jsxDevRuntime"], [1002, 62, 2494, 22], [1002, 63, 2494, 22, "jsxDEV"], [1002, 69, 2494, 22], [1002, 71, 2494, 23, "_Text"], [1002, 76, 2494, 23], [1002, 77, 2494, 23, "default"], [1002, 84, 2494, 27], [1003, 22, 2496, 24, "style"], [1003, 27, 2496, 29], [1003, 29, 2496, 31], [1004, 24, 2498, 26, "fontSize"], [1004, 32, 2498, 34], [1004, 34, 2498, 36], [1004, 36, 2498, 38], [1005, 24, 2500, 26, "fontWeight"], [1005, 34, 2500, 36], [1005, 36, 2500, 38], [1005, 41, 2500, 43], [1006, 24, 2502, 26, "color"], [1006, 29, 2502, 31], [1006, 31, 2502, 33], [1006, 40, 2502, 42], [1007, 24, 2504, 26, "marginBottom"], [1007, 36, 2504, 38], [1007, 38, 2504, 40], [1007, 39, 2504, 41], [1008, 24, 2506, 26, "textTransform"], [1008, 37, 2506, 39], [1008, 39, 2506, 41], [1008, 50, 2506, 52], [1009, 24, 2508, 26, "letterSpacing"], [1009, 37, 2508, 39], [1009, 39, 2508, 41], [1010, 22, 2510, 24], [1010, 23, 2510, 26], [1011, 22, 2510, 26, "children"], [1011, 30, 2510, 26], [1011, 32, 2512, 23], [1012, 20, 2516, 22], [1013, 22, 2516, 22, "fileName"], [1013, 30, 2516, 22], [1013, 32, 2516, 22, "_jsxFileName"], [1013, 44, 2516, 22], [1014, 22, 2516, 22, "lineNumber"], [1014, 32, 2516, 22], [1015, 22, 2516, 22, "columnNumber"], [1015, 34, 2516, 22], [1016, 20, 2516, 22], [1016, 27, 2516, 28], [1016, 28, 2516, 29], [1016, 43, 2518, 22], [1016, 47, 2518, 22, "_jsxDevRuntime"], [1016, 61, 2518, 22], [1016, 62, 2518, 22, "jsxDEV"], [1016, 68, 2518, 22], [1016, 70, 2518, 23, "_View"], [1016, 75, 2518, 23], [1016, 76, 2518, 23, "default"], [1016, 83, 2518, 27], [1017, 22, 2518, 28, "style"], [1017, 27, 2518, 33], [1017, 29, 2518, 35], [1018, 24, 2518, 37, "flexDirection"], [1018, 37, 2518, 50], [1018, 39, 2518, 52], [1018, 44, 2518, 57], [1019, 24, 2518, 59, "marginBottom"], [1019, 36, 2518, 71], [1019, 38, 2518, 73], [1019, 39, 2518, 74], [1020, 24, 2518, 76, "alignItems"], [1020, 34, 2518, 86], [1020, 36, 2518, 88], [1021, 22, 2518, 101], [1021, 23, 2518, 103], [1022, 22, 2518, 103, "children"], [1022, 30, 2518, 103], [1022, 46, 2520, 24], [1022, 50, 2520, 24, "_jsxDevRuntime"], [1022, 64, 2520, 24], [1022, 65, 2520, 24, "jsxDEV"], [1022, 71, 2520, 24], [1022, 73, 2520, 25, "_lucideReactNative"], [1022, 91, 2520, 25], [1022, 92, 2520, 25, "CheckCircle2"], [1022, 104, 2520, 37], [1023, 24, 2520, 38, "size"], [1023, 28, 2520, 42], [1023, 30, 2520, 44], [1023, 32, 2520, 47], [1024, 24, 2520, 48, "color"], [1024, 29, 2520, 53], [1024, 31, 2520, 54], [1024, 40, 2520, 63], [1025, 24, 2520, 64, "style"], [1025, 29, 2520, 69], [1025, 31, 2520, 71], [1026, 26, 2520, 73, "marginTop"], [1026, 35, 2520, 82], [1026, 37, 2520, 84], [1027, 24, 2520, 86], [1028, 22, 2520, 88], [1029, 24, 2520, 88, "fileName"], [1029, 32, 2520, 88], [1029, 34, 2520, 88, "_jsxFileName"], [1029, 46, 2520, 88], [1030, 24, 2520, 88, "lineNumber"], [1030, 34, 2520, 88], [1031, 24, 2520, 88, "columnNumber"], [1031, 36, 2520, 88], [1032, 22, 2520, 88], [1032, 29, 2520, 90], [1032, 30, 2520, 91], [1032, 45, 2522, 24], [1032, 49, 2522, 24, "_jsxDevRuntime"], [1032, 63, 2522, 24], [1032, 64, 2522, 24, "jsxDEV"], [1032, 70, 2522, 24], [1032, 72, 2522, 25, "_Text"], [1032, 77, 2522, 25], [1032, 78, 2522, 25, "default"], [1032, 85, 2522, 29], [1033, 24, 2522, 30, "style"], [1033, 29, 2522, 35], [1033, 31, 2522, 37], [1034, 26, 2522, 39, "fontSize"], [1034, 34, 2522, 47], [1034, 36, 2522, 49], [1034, 38, 2522, 51], [1035, 26, 2522, 53, "color"], [1035, 31, 2522, 58], [1035, 33, 2522, 60], [1035, 42, 2522, 69], [1036, 26, 2522, 71, "marginLeft"], [1036, 36, 2522, 81], [1036, 38, 2522, 83], [1036, 39, 2522, 84], [1037, 26, 2522, 86, "flex"], [1037, 30, 2522, 90], [1037, 32, 2522, 92], [1038, 24, 2522, 94], [1038, 25, 2522, 96], [1039, 24, 2522, 96, "children"], [1039, 32, 2522, 96], [1039, 34, 2522, 97], [1040, 22, 2526, 24], [1041, 24, 2526, 24, "fileName"], [1041, 32, 2526, 24], [1041, 34, 2526, 24, "_jsxFileName"], [1041, 46, 2526, 24], [1042, 24, 2526, 24, "lineNumber"], [1042, 34, 2526, 24], [1043, 24, 2526, 24, "columnNumber"], [1043, 36, 2526, 24], [1044, 22, 2526, 24], [1044, 29, 2526, 30], [1044, 30, 2526, 31], [1045, 20, 2526, 31], [1046, 22, 2526, 31, "fileName"], [1046, 30, 2526, 31], [1046, 32, 2526, 31, "_jsxFileName"], [1046, 44, 2526, 31], [1047, 22, 2526, 31, "lineNumber"], [1047, 32, 2526, 31], [1048, 22, 2526, 31, "columnNumber"], [1048, 34, 2526, 31], [1049, 20, 2526, 31], [1049, 27, 2528, 28], [1049, 28, 2528, 29], [1049, 30, 2530, 23, "cameraResult"], [1049, 42, 2530, 35], [1049, 44, 2530, 37, "challengeCode"], [1049, 57, 2530, 50], [1049, 61, 2530, 54, "cameraResult"], [1049, 73, 2530, 66], [1049, 74, 2530, 67, "challengeCode"], [1049, 87, 2530, 80], [1049, 88, 2530, 81, "trim"], [1049, 92, 2530, 85], [1049, 93, 2530, 86], [1049, 94, 2530, 87], [1049, 111, 2532, 24], [1049, 115, 2532, 24, "_jsxDevRuntime"], [1049, 129, 2532, 24], [1049, 130, 2532, 24, "jsxDEV"], [1049, 136, 2532, 24], [1049, 138, 2532, 25, "_View"], [1049, 143, 2532, 25], [1049, 144, 2532, 25, "default"], [1049, 151, 2532, 29], [1050, 22, 2532, 30, "style"], [1050, 27, 2532, 35], [1050, 29, 2532, 37], [1051, 24, 2532, 39, "flexDirection"], [1051, 37, 2532, 52], [1051, 39, 2532, 54], [1051, 44, 2532, 59], [1052, 24, 2532, 61, "marginBottom"], [1052, 36, 2532, 73], [1052, 38, 2532, 75], [1052, 39, 2532, 76], [1053, 24, 2532, 78, "alignItems"], [1053, 34, 2532, 88], [1053, 36, 2532, 90], [1054, 22, 2532, 103], [1054, 23, 2532, 105], [1055, 22, 2532, 105, "children"], [1055, 30, 2532, 105], [1055, 46, 2534, 26], [1055, 50, 2534, 26, "_jsxDevRuntime"], [1055, 64, 2534, 26], [1055, 65, 2534, 26, "jsxDEV"], [1055, 71, 2534, 26], [1055, 73, 2534, 27, "_lucideReactNative"], [1055, 91, 2534, 27], [1055, 92, 2534, 27, "CheckCircle2"], [1055, 104, 2534, 39], [1056, 24, 2534, 40, "size"], [1056, 28, 2534, 44], [1056, 30, 2534, 46], [1056, 32, 2534, 49], [1057, 24, 2534, 50, "color"], [1057, 29, 2534, 55], [1057, 31, 2534, 56], [1057, 40, 2534, 65], [1058, 24, 2534, 66, "style"], [1058, 29, 2534, 71], [1058, 31, 2534, 73], [1059, 26, 2534, 75, "marginTop"], [1059, 35, 2534, 84], [1059, 37, 2534, 86], [1060, 24, 2534, 88], [1061, 22, 2534, 90], [1062, 24, 2534, 90, "fileName"], [1062, 32, 2534, 90], [1062, 34, 2534, 90, "_jsxFileName"], [1062, 46, 2534, 90], [1063, 24, 2534, 90, "lineNumber"], [1063, 34, 2534, 90], [1064, 24, 2534, 90, "columnNumber"], [1064, 36, 2534, 90], [1065, 22, 2534, 90], [1065, 29, 2534, 92], [1065, 30, 2534, 93], [1065, 45, 2536, 26], [1065, 49, 2536, 26, "_jsxDevRuntime"], [1065, 63, 2536, 26], [1065, 64, 2536, 26, "jsxDEV"], [1065, 70, 2536, 26], [1065, 72, 2536, 27, "_Text"], [1065, 77, 2536, 27], [1065, 78, 2536, 27, "default"], [1065, 85, 2536, 31], [1066, 24, 2536, 32, "style"], [1066, 29, 2536, 37], [1066, 31, 2536, 39], [1067, 26, 2536, 41, "fontSize"], [1067, 34, 2536, 49], [1067, 36, 2536, 51], [1067, 38, 2536, 53], [1068, 26, 2536, 55, "color"], [1068, 31, 2536, 60], [1068, 33, 2536, 62], [1068, 42, 2536, 71], [1069, 26, 2536, 73, "marginLeft"], [1069, 36, 2536, 83], [1069, 38, 2536, 85], [1069, 39, 2536, 86], [1070, 26, 2536, 88, "flex"], [1070, 30, 2536, 92], [1070, 32, 2536, 94], [1071, 24, 2536, 96], [1071, 25, 2536, 98], [1072, 24, 2536, 98, "children"], [1072, 32, 2536, 98], [1072, 34, 2538, 29], [1072, 57, 2538, 52, "cameraResult"], [1072, 69, 2538, 64], [1072, 70, 2538, 65, "challengeCode"], [1072, 83, 2538, 78], [1072, 87, 2538, 82], [1072, 92, 2538, 87], [1073, 22, 2538, 89], [1074, 24, 2538, 89, "fileName"], [1074, 32, 2538, 89], [1074, 34, 2538, 89, "_jsxFileName"], [1074, 46, 2538, 89], [1075, 24, 2538, 89, "lineNumber"], [1075, 34, 2538, 89], [1076, 24, 2538, 89, "columnNumber"], [1076, 36, 2538, 89], [1077, 22, 2538, 89], [1077, 29, 2540, 32], [1077, 30, 2540, 33], [1078, 20, 2540, 33], [1079, 22, 2540, 33, "fileName"], [1079, 30, 2540, 33], [1079, 32, 2540, 33, "_jsxFileName"], [1079, 44, 2540, 33], [1080, 22, 2540, 33, "lineNumber"], [1080, 32, 2540, 33], [1081, 22, 2540, 33, "columnNumber"], [1081, 34, 2540, 33], [1082, 20, 2540, 33], [1082, 27, 2542, 30], [1082, 28, 2544, 23], [1082, 43, 2546, 22], [1082, 47, 2546, 22, "_jsxDevRuntime"], [1082, 61, 2546, 22], [1082, 62, 2546, 22, "jsxDEV"], [1082, 68, 2546, 22], [1082, 70, 2546, 23, "_View"], [1082, 75, 2546, 23], [1082, 76, 2546, 23, "default"], [1082, 83, 2546, 27], [1083, 22, 2546, 28, "style"], [1083, 27, 2546, 33], [1083, 29, 2546, 35], [1084, 24, 2546, 37, "flexDirection"], [1084, 37, 2546, 50], [1084, 39, 2546, 52], [1084, 44, 2546, 57], [1085, 24, 2546, 59, "alignItems"], [1085, 34, 2546, 69], [1085, 36, 2546, 71], [1086, 22, 2546, 84], [1086, 23, 2546, 86], [1087, 22, 2546, 86, "children"], [1087, 30, 2546, 86], [1087, 46, 2548, 24], [1087, 50, 2548, 24, "_jsxDevRuntime"], [1087, 64, 2548, 24], [1087, 65, 2548, 24, "jsxDEV"], [1087, 71, 2548, 24], [1087, 73, 2548, 25, "_lucideReactNative"], [1087, 91, 2548, 25], [1087, 92, 2548, 25, "CheckCircle2"], [1087, 104, 2548, 37], [1088, 24, 2548, 38, "size"], [1088, 28, 2548, 42], [1088, 30, 2548, 44], [1088, 32, 2548, 47], [1089, 24, 2548, 48, "color"], [1089, 29, 2548, 53], [1089, 31, 2548, 54], [1089, 40, 2548, 63], [1090, 24, 2548, 64, "style"], [1090, 29, 2548, 69], [1090, 31, 2548, 71], [1091, 26, 2548, 73, "marginTop"], [1091, 35, 2548, 82], [1091, 37, 2548, 84], [1092, 24, 2548, 86], [1093, 22, 2548, 88], [1094, 24, 2548, 88, "fileName"], [1094, 32, 2548, 88], [1094, 34, 2548, 88, "_jsxFileName"], [1094, 46, 2548, 88], [1095, 24, 2548, 88, "lineNumber"], [1095, 34, 2548, 88], [1096, 24, 2548, 88, "columnNumber"], [1096, 36, 2548, 88], [1097, 22, 2548, 88], [1097, 29, 2548, 90], [1097, 30, 2548, 91], [1097, 45, 2550, 24], [1097, 49, 2550, 24, "_jsxDevRuntime"], [1097, 63, 2550, 24], [1097, 64, 2550, 24, "jsxDEV"], [1097, 70, 2550, 24], [1097, 72, 2550, 25, "_Text"], [1097, 77, 2550, 25], [1097, 78, 2550, 25, "default"], [1097, 85, 2550, 29], [1098, 24, 2550, 30, "style"], [1098, 29, 2550, 35], [1098, 31, 2550, 37], [1099, 26, 2550, 39, "fontSize"], [1099, 34, 2550, 47], [1099, 36, 2550, 49], [1099, 38, 2550, 51], [1100, 26, 2550, 53, "color"], [1100, 31, 2550, 58], [1100, 33, 2550, 60], [1100, 42, 2550, 69], [1101, 26, 2550, 71, "marginLeft"], [1101, 36, 2550, 81], [1101, 38, 2550, 83], [1101, 39, 2550, 84], [1102, 26, 2550, 86, "flex"], [1102, 30, 2550, 90], [1102, 32, 2550, 92], [1103, 24, 2550, 94], [1103, 25, 2550, 96], [1104, 24, 2550, 96, "children"], [1104, 32, 2550, 96], [1104, 34, 2552, 27], [1104, 57, 2552, 50, "distance"], [1104, 65, 2552, 58], [1104, 69, 2552, 62], [1104, 70, 2552, 63], [1105, 22, 2552, 71], [1106, 24, 2552, 71, "fileName"], [1106, 32, 2552, 71], [1106, 34, 2552, 71, "_jsxFileName"], [1106, 46, 2552, 71], [1107, 24, 2552, 71, "lineNumber"], [1107, 34, 2552, 71], [1108, 24, 2552, 71, "columnNumber"], [1108, 36, 2552, 71], [1109, 22, 2552, 71], [1109, 29, 2554, 30], [1109, 30, 2554, 31], [1110, 20, 2554, 31], [1111, 22, 2554, 31, "fileName"], [1111, 30, 2554, 31], [1111, 32, 2554, 31, "_jsxFileName"], [1111, 44, 2554, 31], [1112, 22, 2554, 31, "lineNumber"], [1112, 32, 2554, 31], [1113, 22, 2554, 31, "columnNumber"], [1113, 34, 2554, 31], [1114, 20, 2554, 31], [1114, 27, 2556, 28], [1114, 28, 2556, 29], [1115, 18, 2556, 29], [1116, 20, 2556, 29, "fileName"], [1116, 28, 2556, 29], [1116, 30, 2556, 29, "_jsxFileName"], [1116, 42, 2556, 29], [1117, 20, 2556, 29, "lineNumber"], [1117, 30, 2556, 29], [1118, 20, 2556, 29, "columnNumber"], [1118, 32, 2556, 29], [1119, 18, 2556, 29], [1119, 25, 2558, 26], [1119, 26, 2558, 27], [1119, 41, 2560, 20], [1119, 45, 2560, 20, "_jsxDevRuntime"], [1119, 59, 2560, 20], [1119, 60, 2560, 20, "jsxDEV"], [1119, 66, 2560, 20], [1119, 68, 2560, 21, "_View"], [1119, 73, 2560, 21], [1119, 74, 2560, 21, "default"], [1119, 81, 2560, 25], [1120, 20, 2560, 26, "style"], [1120, 25, 2560, 31], [1120, 27, 2560, 33], [1121, 22, 2560, 35, "flexDirection"], [1121, 35, 2560, 48], [1121, 37, 2560, 50], [1122, 20, 2560, 56], [1122, 21, 2560, 58], [1123, 20, 2560, 58, "children"], [1123, 28, 2560, 58], [1123, 43, 2562, 22], [1123, 47, 2562, 22, "_jsxDevRuntime"], [1123, 61, 2562, 22], [1123, 62, 2562, 22, "jsxDEV"], [1123, 68, 2562, 22], [1123, 70, 2562, 23, "_TouchableOpacity"], [1123, 87, 2562, 23], [1123, 88, 2562, 23, "default"], [1123, 95, 2562, 39], [1124, 22, 2564, 24, "onPress"], [1124, 29, 2564, 31], [1124, 31, 2564, 33, "onPress"], [1124, 32, 2564, 33], [1124, 37, 2564, 39], [1125, 24, 2566, 26, "setCameraResult"], [1125, 39, 2566, 41], [1125, 40, 2566, 42], [1125, 44, 2566, 46], [1125, 45, 2566, 47], [1126, 24, 2568, 26, "setCapturedPhotoUri"], [1126, 43, 2568, 45], [1126, 44, 2568, 46], [1126, 48, 2568, 50], [1126, 49, 2568, 51], [1127, 24, 2570, 26, "handleStartCamera"], [1127, 41, 2570, 43], [1127, 42, 2570, 44], [1127, 43, 2570, 45], [1128, 22, 2572, 24], [1128, 23, 2572, 26], [1129, 22, 2574, 24, "style"], [1129, 27, 2574, 29], [1129, 29, 2574, 31], [1130, 24, 2576, 26, "flex"], [1130, 28, 2576, 30], [1130, 30, 2576, 32], [1130, 31, 2576, 33], [1131, 24, 2578, 26, "backgroundColor"], [1131, 39, 2578, 41], [1131, 41, 2578, 43], [1131, 47, 2578, 49], [1132, 24, 2580, 26, "borderWidth"], [1132, 35, 2580, 37], [1132, 37, 2580, 39], [1132, 38, 2580, 40], [1133, 24, 2582, 26, "borderColor"], [1133, 35, 2582, 37], [1133, 37, 2582, 39], [1133, 46, 2582, 48], [1134, 24, 2584, 26, "borderRadius"], [1134, 36, 2584, 38], [1134, 38, 2584, 40], [1134, 40, 2584, 42], [1135, 24, 2586, 26, "paddingVertical"], [1135, 39, 2586, 41], [1135, 41, 2586, 43], [1135, 43, 2586, 45], [1136, 24, 2588, 26, "paddingHorizontal"], [1136, 41, 2588, 43], [1136, 43, 2588, 45], [1136, 45, 2588, 47], [1137, 24, 2590, 26, "flexDirection"], [1137, 37, 2590, 39], [1137, 39, 2590, 41], [1137, 44, 2590, 46], [1138, 24, 2592, 26, "alignItems"], [1138, 34, 2592, 36], [1138, 36, 2592, 38], [1138, 44, 2592, 46], [1139, 24, 2594, 26, "justifyContent"], [1139, 38, 2594, 40], [1139, 40, 2594, 42], [1140, 22, 2596, 24], [1140, 23, 2596, 26], [1141, 22, 2596, 26, "children"], [1141, 30, 2596, 26], [1141, 46, 2600, 24], [1141, 50, 2600, 24, "_jsxDevRuntime"], [1141, 64, 2600, 24], [1141, 65, 2600, 24, "jsxDEV"], [1141, 71, 2600, 24], [1141, 73, 2600, 25, "_lucideReactNative"], [1141, 91, 2600, 25], [1141, 92, 2600, 25, "Camera"], [1141, 98, 2600, 31], [1142, 24, 2600, 32, "size"], [1142, 28, 2600, 36], [1142, 30, 2600, 38], [1142, 32, 2600, 41], [1143, 24, 2600, 42, "color"], [1143, 29, 2600, 47], [1143, 31, 2600, 48], [1144, 22, 2600, 57], [1145, 24, 2600, 57, "fileName"], [1145, 32, 2600, 57], [1145, 34, 2600, 57, "_jsxFileName"], [1145, 46, 2600, 57], [1146, 24, 2600, 57, "lineNumber"], [1146, 34, 2600, 57], [1147, 24, 2600, 57, "columnNumber"], [1147, 36, 2600, 57], [1148, 22, 2600, 57], [1148, 29, 2600, 59], [1148, 30, 2600, 60], [1148, 45, 2602, 24], [1148, 49, 2602, 24, "_jsxDevRuntime"], [1148, 63, 2602, 24], [1148, 64, 2602, 24, "jsxDEV"], [1148, 70, 2602, 24], [1148, 72, 2602, 25, "_Text"], [1148, 77, 2602, 25], [1148, 78, 2602, 25, "default"], [1148, 85, 2602, 29], [1149, 24, 2602, 30, "style"], [1149, 29, 2602, 35], [1149, 31, 2602, 37], [1150, 26, 2602, 39, "fontSize"], [1150, 34, 2602, 47], [1150, 36, 2602, 49], [1150, 38, 2602, 51], [1151, 26, 2602, 53, "color"], [1151, 31, 2602, 58], [1151, 33, 2602, 60], [1151, 42, 2602, 69], [1152, 26, 2602, 71, "fontWeight"], [1152, 36, 2602, 81], [1152, 38, 2602, 83], [1152, 43, 2602, 88], [1153, 26, 2602, 90, "marginLeft"], [1153, 36, 2602, 100], [1153, 38, 2602, 102], [1154, 24, 2602, 104], [1154, 25, 2602, 106], [1155, 24, 2602, 106, "children"], [1155, 32, 2602, 106], [1155, 34, 2602, 107], [1156, 22, 2606, 24], [1157, 24, 2606, 24, "fileName"], [1157, 32, 2606, 24], [1157, 34, 2606, 24, "_jsxFileName"], [1157, 46, 2606, 24], [1158, 24, 2606, 24, "lineNumber"], [1158, 34, 2606, 24], [1159, 24, 2606, 24, "columnNumber"], [1159, 36, 2606, 24], [1160, 22, 2606, 24], [1160, 29, 2606, 30], [1160, 30, 2606, 31], [1161, 20, 2606, 31], [1162, 22, 2606, 31, "fileName"], [1162, 30, 2606, 31], [1162, 32, 2606, 31, "_jsxFileName"], [1162, 44, 2606, 31], [1163, 22, 2606, 31, "lineNumber"], [1163, 32, 2606, 31], [1164, 22, 2606, 31, "columnNumber"], [1164, 34, 2606, 31], [1165, 20, 2606, 31], [1165, 27, 2608, 40], [1166, 18, 2608, 41], [1167, 20, 2608, 41, "fileName"], [1167, 28, 2608, 41], [1167, 30, 2608, 41, "_jsxFileName"], [1167, 42, 2608, 41], [1168, 20, 2608, 41, "lineNumber"], [1168, 30, 2608, 41], [1169, 20, 2608, 41, "columnNumber"], [1169, 32, 2608, 41], [1170, 18, 2608, 41], [1170, 25, 2610, 26], [1170, 26, 2610, 27], [1171, 16, 2610, 27], [1172, 18, 2610, 27, "fileName"], [1172, 26, 2610, 27], [1172, 28, 2610, 27, "_jsxFileName"], [1172, 40, 2610, 27], [1173, 18, 2610, 27, "lineNumber"], [1173, 28, 2610, 27], [1174, 18, 2610, 27, "columnNumber"], [1174, 30, 2610, 27], [1175, 16, 2610, 27], [1175, 23, 2612, 24], [1175, 24, 2612, 25], [1176, 14, 2612, 25], [1177, 16, 2612, 25, "fileName"], [1177, 24, 2612, 25], [1177, 26, 2612, 25, "_jsxFileName"], [1177, 38, 2612, 25], [1178, 16, 2612, 25, "lineNumber"], [1178, 26, 2612, 25], [1179, 16, 2612, 25, "columnNumber"], [1179, 28, 2612, 25], [1180, 14, 2612, 25], [1180, 21, 2614, 22], [1180, 22, 2614, 23], [1180, 38, 2618, 16], [1180, 42, 2618, 16, "_jsxDevRuntime"], [1180, 56, 2618, 16], [1180, 57, 2618, 16, "jsxDEV"], [1180, 63, 2618, 16], [1180, 65, 2618, 17, "_TouchableOpacity"], [1180, 82, 2618, 17], [1180, 83, 2618, 17, "default"], [1180, 90, 2618, 33], [1181, 16, 2620, 18, "onPress"], [1181, 23, 2620, 25], [1181, 25, 2620, 27, "handleStartCamera"], [1181, 42, 2620, 45], [1182, 16, 2622, 18, "disabled"], [1182, 24, 2622, 26], [1182, 26, 2622, 28, "locationStatus"], [1182, 40, 2622, 42], [1182, 45, 2622, 47], [1182, 55, 2622, 57], [1182, 59, 2622, 61], [1182, 60, 2622, 62, "testingMode"], [1182, 71, 2622, 74], [1183, 16, 2624, 18, "style"], [1183, 21, 2624, 23], [1183, 23, 2624, 25], [1184, 18, 2626, 20, "backgroundColor"], [1184, 33, 2626, 35], [1184, 35, 2628, 22, "locationStatus"], [1184, 49, 2628, 36], [1184, 54, 2628, 41], [1184, 64, 2628, 51], [1184, 68, 2628, 55, "testingMode"], [1184, 79, 2628, 66], [1184, 82, 2628, 69], [1184, 91, 2628, 78], [1184, 94, 2628, 81], [1184, 103, 2628, 90], [1185, 18, 2630, 20, "borderRadius"], [1185, 30, 2630, 32], [1185, 32, 2630, 34], [1185, 34, 2630, 36], [1186, 18, 2632, 20, "padding"], [1186, 25, 2632, 27], [1186, 27, 2632, 29], [1186, 29, 2632, 31], [1187, 18, 2634, 20, "flexDirection"], [1187, 31, 2634, 33], [1187, 33, 2634, 35], [1187, 38, 2634, 40], [1188, 18, 2636, 20, "alignItems"], [1188, 28, 2636, 30], [1188, 30, 2636, 32], [1188, 38, 2636, 40], [1189, 18, 2638, 20, "justifyContent"], [1189, 32, 2638, 34], [1189, 34, 2638, 36], [1190, 16, 2640, 18], [1190, 17, 2640, 20], [1191, 16, 2640, 20, "children"], [1191, 24, 2640, 20], [1191, 40, 2644, 18], [1191, 44, 2644, 18, "_jsxDevRuntime"], [1191, 58, 2644, 18], [1191, 59, 2644, 18, "jsxDEV"], [1191, 65, 2644, 18], [1191, 67, 2644, 19, "_lucideReactNative"], [1191, 85, 2644, 19], [1191, 86, 2644, 19, "Camera"], [1191, 92, 2644, 25], [1192, 18, 2644, 26, "size"], [1192, 22, 2644, 30], [1192, 24, 2644, 32], [1192, 26, 2644, 35], [1193, 18, 2644, 36, "color"], [1193, 23, 2644, 41], [1193, 25, 2644, 42], [1194, 16, 2644, 48], [1195, 18, 2644, 48, "fileName"], [1195, 26, 2644, 48], [1195, 28, 2644, 48, "_jsxFileName"], [1195, 40, 2644, 48], [1196, 18, 2644, 48, "lineNumber"], [1196, 28, 2644, 48], [1197, 18, 2644, 48, "columnNumber"], [1197, 30, 2644, 48], [1198, 16, 2644, 48], [1198, 23, 2644, 50], [1198, 24, 2644, 51], [1198, 39, 2646, 18], [1198, 43, 2646, 18, "_jsxDevRuntime"], [1198, 57, 2646, 18], [1198, 58, 2646, 18, "jsxDEV"], [1198, 64, 2646, 18], [1198, 66, 2646, 19, "_Text"], [1198, 71, 2646, 19], [1198, 72, 2646, 19, "default"], [1198, 79, 2646, 23], [1199, 18, 2648, 20, "style"], [1199, 23, 2648, 25], [1199, 25, 2648, 27], [1200, 20, 2650, 22, "fontSize"], [1200, 28, 2650, 30], [1200, 30, 2650, 32], [1200, 32, 2650, 34], [1201, 20, 2652, 22, "fontWeight"], [1201, 30, 2652, 32], [1201, 32, 2652, 34], [1201, 37, 2652, 39], [1202, 20, 2654, 22, "color"], [1202, 25, 2654, 27], [1202, 27, 2654, 29], [1202, 33, 2654, 35], [1203, 20, 2656, 22, "marginLeft"], [1203, 30, 2656, 32], [1203, 32, 2656, 34], [1204, 18, 2658, 20], [1204, 19, 2658, 22], [1205, 18, 2658, 22, "children"], [1205, 26, 2658, 22], [1205, 28, 2662, 21, "locationStatus"], [1205, 42, 2662, 35], [1205, 47, 2662, 40], [1205, 57, 2662, 50], [1205, 61, 2662, 54, "testingMode"], [1205, 72, 2662, 65], [1205, 75, 2664, 24], [1205, 89, 2664, 38], [1205, 92, 2666, 24], [1206, 16, 2666, 47], [1207, 18, 2666, 47, "fileName"], [1207, 26, 2666, 47], [1207, 28, 2666, 47, "_jsxFileName"], [1207, 40, 2666, 47], [1208, 18, 2666, 47, "lineNumber"], [1208, 28, 2666, 47], [1209, 18, 2666, 47, "columnNumber"], [1209, 30, 2666, 47], [1210, 16, 2666, 47], [1210, 23, 2668, 24], [1210, 24, 2668, 25], [1210, 26, 2670, 19, "testingMode"], [1210, 37, 2670, 30], [1210, 54, 2672, 20], [1210, 58, 2672, 20, "_jsxDevRuntime"], [1210, 72, 2672, 20], [1210, 73, 2672, 20, "jsxDEV"], [1210, 79, 2672, 20], [1210, 81, 2672, 21, "_Text"], [1210, 86, 2672, 21], [1210, 87, 2672, 21, "default"], [1210, 94, 2672, 25], [1211, 18, 2672, 26, "style"], [1211, 23, 2672, 31], [1211, 25, 2672, 33], [1212, 20, 2672, 35, "fontSize"], [1212, 28, 2672, 43], [1212, 30, 2672, 45], [1212, 32, 2672, 47], [1213, 20, 2672, 49, "color"], [1213, 25, 2672, 54], [1213, 27, 2672, 56], [1213, 33, 2672, 62], [1214, 20, 2672, 64, "marginLeft"], [1214, 30, 2672, 74], [1214, 32, 2672, 76], [1215, 18, 2672, 78], [1215, 19, 2672, 80], [1216, 18, 2672, 80, "children"], [1216, 26, 2672, 80], [1216, 28, 2672, 81], [1217, 16, 2672, 85], [1218, 18, 2672, 85, "fileName"], [1218, 26, 2672, 85], [1218, 28, 2672, 85, "_jsxFileName"], [1218, 40, 2672, 85], [1219, 18, 2672, 85, "lineNumber"], [1219, 28, 2672, 85], [1220, 18, 2672, 85, "columnNumber"], [1220, 30, 2672, 85], [1221, 16, 2672, 85], [1221, 23, 2672, 91], [1221, 24, 2674, 19], [1222, 14, 2674, 19], [1223, 16, 2674, 19, "fileName"], [1223, 24, 2674, 19], [1223, 26, 2674, 19, "_jsxFileName"], [1223, 38, 2674, 19], [1224, 16, 2674, 19, "lineNumber"], [1224, 26, 2674, 19], [1225, 16, 2674, 19, "columnNumber"], [1225, 28, 2674, 19], [1226, 14, 2674, 19], [1226, 21, 2676, 34], [1226, 22, 2678, 15], [1227, 12, 2678, 15], [1228, 14, 2678, 15, "fileName"], [1228, 22, 2678, 15], [1228, 24, 2678, 15, "_jsxFileName"], [1228, 36, 2678, 15], [1229, 14, 2678, 15, "lineNumber"], [1229, 24, 2678, 15], [1230, 14, 2678, 15, "columnNumber"], [1230, 26, 2678, 15], [1231, 12, 2678, 15], [1231, 19, 2680, 18], [1231, 20, 2680, 19], [1231, 35, 2683, 12], [1231, 39, 2683, 12, "_jsxDevRuntime"], [1231, 53, 2683, 12], [1231, 54, 2683, 12, "jsxDEV"], [1231, 60, 2683, 12], [1231, 62, 2683, 13, "_View"], [1231, 67, 2683, 13], [1231, 68, 2683, 13, "default"], [1231, 75, 2683, 17], [1232, 14, 2683, 18, "style"], [1232, 19, 2683, 23], [1232, 21, 2683, 25], [1233, 16, 2683, 27, "marginBottom"], [1233, 28, 2683, 39], [1233, 30, 2683, 41], [1234, 14, 2683, 44], [1234, 15, 2683, 46], [1235, 14, 2683, 46, "children"], [1235, 22, 2683, 46], [1235, 38, 2684, 14], [1235, 42, 2684, 14, "_jsxDevRuntime"], [1235, 56, 2684, 14], [1235, 57, 2684, 14, "jsxDEV"], [1235, 63, 2684, 14], [1235, 65, 2684, 15, "_View"], [1235, 70, 2684, 15], [1235, 71, 2684, 15, "default"], [1235, 78, 2684, 19], [1236, 16, 2685, 16, "style"], [1236, 21, 2685, 21], [1236, 23, 2685, 23], [1237, 18, 2686, 18, "flexDirection"], [1237, 31, 2686, 31], [1237, 33, 2686, 33], [1237, 38, 2686, 38], [1238, 18, 2687, 18, "alignItems"], [1238, 28, 2687, 28], [1238, 30, 2687, 30], [1238, 38, 2687, 38], [1239, 18, 2688, 18, "marginBottom"], [1239, 30, 2688, 30], [1239, 32, 2688, 32], [1240, 16, 2689, 16], [1240, 17, 2689, 18], [1241, 16, 2689, 18, "children"], [1241, 24, 2689, 18], [1241, 40, 2691, 16], [1241, 44, 2691, 16, "_jsxDevRuntime"], [1241, 58, 2691, 16], [1241, 59, 2691, 16, "jsxDEV"], [1241, 65, 2691, 16], [1241, 67, 2691, 17, "_View"], [1241, 72, 2691, 17], [1241, 73, 2691, 17, "default"], [1241, 80, 2691, 21], [1242, 18, 2692, 18, "style"], [1242, 23, 2692, 23], [1242, 25, 2692, 25], [1243, 20, 2693, 20, "width"], [1243, 25, 2693, 25], [1243, 27, 2693, 27], [1243, 29, 2693, 29], [1244, 20, 2694, 20, "height"], [1244, 26, 2694, 26], [1244, 28, 2694, 28], [1244, 30, 2694, 30], [1245, 20, 2695, 20, "borderRadius"], [1245, 32, 2695, 32], [1245, 34, 2695, 34], [1245, 36, 2695, 36], [1246, 20, 2696, 20, "backgroundColor"], [1246, 35, 2696, 35], [1246, 37, 2696, 37, "response"], [1246, 45, 2696, 45], [1246, 46, 2696, 46, "trim"], [1246, 50, 2696, 50], [1246, 51, 2696, 51], [1246, 52, 2696, 52], [1246, 55, 2697, 24], [1246, 64, 2697, 33], [1246, 67, 2698, 24, "cameraResult"], [1246, 79, 2698, 36], [1246, 82, 2699, 26], [1246, 91, 2699, 35], [1246, 94, 2700, 26], [1246, 103, 2700, 35], [1247, 20, 2701, 20, "alignItems"], [1247, 30, 2701, 30], [1247, 32, 2701, 32], [1247, 40, 2701, 40], [1248, 20, 2702, 20, "justifyContent"], [1248, 34, 2702, 34], [1248, 36, 2702, 36], [1248, 44, 2702, 44], [1249, 20, 2703, 20, "marginRight"], [1249, 31, 2703, 31], [1249, 33, 2703, 33], [1250, 18, 2704, 18], [1250, 19, 2704, 20], [1251, 18, 2704, 20, "children"], [1251, 26, 2704, 20], [1251, 28, 2706, 19, "response"], [1251, 36, 2706, 27], [1251, 37, 2706, 28, "trim"], [1251, 41, 2706, 32], [1251, 42, 2706, 33], [1251, 43, 2706, 34], [1251, 59, 2707, 20], [1251, 63, 2707, 20, "_jsxDevRuntime"], [1251, 77, 2707, 20], [1251, 78, 2707, 20, "jsxDEV"], [1251, 84, 2707, 20], [1251, 86, 2707, 21, "_lucideReactNative"], [1251, 104, 2707, 21], [1251, 105, 2707, 21, "CheckCircle2"], [1251, 117, 2707, 33], [1252, 20, 2707, 34, "size"], [1252, 24, 2707, 38], [1252, 26, 2707, 40], [1252, 28, 2707, 43], [1253, 20, 2707, 44, "color"], [1253, 25, 2707, 49], [1253, 27, 2707, 50], [1254, 18, 2707, 56], [1255, 20, 2707, 56, "fileName"], [1255, 28, 2707, 56], [1255, 30, 2707, 56, "_jsxFileName"], [1255, 42, 2707, 56], [1256, 20, 2707, 56, "lineNumber"], [1256, 30, 2707, 56], [1257, 20, 2707, 56, "columnNumber"], [1257, 32, 2707, 56], [1258, 18, 2707, 56], [1258, 25, 2707, 58], [1258, 26, 2707, 59], [1258, 42, 2709, 20], [1258, 46, 2709, 20, "_jsxDevRuntime"], [1258, 60, 2709, 20], [1258, 61, 2709, 20, "jsxDEV"], [1258, 67, 2709, 20], [1258, 69, 2709, 21, "_Text"], [1258, 74, 2709, 21], [1258, 75, 2709, 21, "default"], [1258, 82, 2709, 25], [1259, 20, 2710, 22, "style"], [1259, 25, 2710, 27], [1259, 27, 2710, 29], [1260, 22, 2710, 31, "color"], [1260, 27, 2710, 36], [1260, 29, 2710, 38], [1260, 35, 2710, 44], [1261, 22, 2710, 46, "fontSize"], [1261, 30, 2710, 54], [1261, 32, 2710, 56], [1261, 34, 2710, 58], [1262, 22, 2710, 60, "fontWeight"], [1262, 32, 2710, 70], [1262, 34, 2710, 72], [1263, 20, 2710, 78], [1263, 21, 2710, 80], [1264, 20, 2710, 80, "children"], [1264, 28, 2710, 80], [1264, 30, 2711, 21], [1265, 18, 2713, 20], [1266, 20, 2713, 20, "fileName"], [1266, 28, 2713, 20], [1266, 30, 2713, 20, "_jsxFileName"], [1266, 42, 2713, 20], [1267, 20, 2713, 20, "lineNumber"], [1267, 30, 2713, 20], [1268, 20, 2713, 20, "columnNumber"], [1268, 32, 2713, 20], [1269, 18, 2713, 20], [1269, 25, 2713, 26], [1270, 16, 2714, 19], [1271, 18, 2714, 19, "fileName"], [1271, 26, 2714, 19], [1271, 28, 2714, 19, "_jsxFileName"], [1271, 40, 2714, 19], [1272, 18, 2714, 19, "lineNumber"], [1272, 28, 2714, 19], [1273, 18, 2714, 19, "columnNumber"], [1273, 30, 2714, 19], [1274, 16, 2714, 19], [1274, 23, 2715, 22], [1274, 24, 2715, 23], [1274, 39, 2717, 16], [1274, 43, 2717, 16, "_jsxDevRuntime"], [1274, 57, 2717, 16], [1274, 58, 2717, 16, "jsxDEV"], [1274, 64, 2717, 16], [1274, 66, 2717, 17, "_Text"], [1274, 71, 2717, 17], [1274, 72, 2717, 17, "default"], [1274, 79, 2717, 21], [1275, 18, 2718, 18, "style"], [1275, 23, 2718, 23], [1275, 25, 2718, 25], [1276, 20, 2719, 20, "fontSize"], [1276, 28, 2719, 28], [1276, 30, 2719, 30], [1276, 32, 2719, 32], [1277, 20, 2720, 20, "fontWeight"], [1277, 30, 2720, 30], [1277, 32, 2720, 32], [1277, 37, 2720, 37], [1278, 20, 2721, 20, "color"], [1278, 25, 2721, 25], [1278, 27, 2721, 27], [1279, 18, 2722, 18], [1279, 19, 2722, 20], [1280, 18, 2722, 20, "children"], [1280, 26, 2722, 20], [1280, 28, 2723, 17], [1281, 16, 2725, 16], [1282, 18, 2725, 16, "fileName"], [1282, 26, 2725, 16], [1282, 28, 2725, 16, "_jsxFileName"], [1282, 40, 2725, 16], [1283, 18, 2725, 16, "lineNumber"], [1283, 28, 2725, 16], [1284, 18, 2725, 16, "columnNumber"], [1284, 30, 2725, 16], [1285, 16, 2725, 16], [1285, 23, 2725, 22], [1285, 24, 2725, 23], [1286, 14, 2725, 23], [1287, 16, 2725, 23, "fileName"], [1287, 24, 2725, 23], [1287, 26, 2725, 23, "_jsxFileName"], [1287, 38, 2725, 23], [1288, 16, 2725, 23, "lineNumber"], [1288, 26, 2725, 23], [1289, 16, 2725, 23, "columnNumber"], [1289, 28, 2725, 23], [1290, 14, 2725, 23], [1290, 21, 2726, 20], [1290, 22, 2726, 21], [1290, 37, 2728, 14], [1290, 41, 2728, 14, "_jsxDevRuntime"], [1290, 55, 2728, 14], [1290, 56, 2728, 14, "jsxDEV"], [1290, 62, 2728, 14], [1290, 64, 2728, 15, "_Text"], [1290, 69, 2728, 15], [1290, 70, 2728, 15, "default"], [1290, 77, 2728, 19], [1291, 16, 2729, 16, "style"], [1291, 21, 2729, 21], [1291, 23, 2729, 23], [1292, 18, 2730, 18, "fontSize"], [1292, 26, 2730, 26], [1292, 28, 2730, 28], [1292, 30, 2730, 30], [1293, 18, 2731, 18, "color"], [1293, 23, 2731, 23], [1293, 25, 2731, 25], [1293, 34, 2731, 34], [1294, 18, 2732, 18, "marginBottom"], [1294, 30, 2732, 30], [1294, 32, 2732, 32], [1294, 34, 2732, 34], [1295, 18, 2733, 18, "lineHeight"], [1295, 28, 2733, 28], [1295, 30, 2733, 30], [1296, 16, 2734, 16], [1296, 17, 2734, 18], [1297, 16, 2734, 18, "children"], [1297, 24, 2734, 18], [1297, 26, 2736, 17], [1298, 14, 2736, 109], [1299, 16, 2736, 109, "fileName"], [1299, 24, 2736, 109], [1299, 26, 2736, 109, "_jsxFileName"], [1299, 38, 2736, 109], [1300, 16, 2736, 109, "lineNumber"], [1300, 26, 2736, 109], [1301, 16, 2736, 109, "columnNumber"], [1301, 28, 2736, 109], [1302, 14, 2736, 109], [1302, 21, 2737, 20], [1302, 22, 2737, 21], [1302, 37, 2739, 14], [1302, 41, 2739, 14, "_jsxDevRuntime"], [1302, 55, 2739, 14], [1302, 56, 2739, 14, "jsxDEV"], [1302, 62, 2739, 14], [1302, 64, 2739, 15, "_View"], [1302, 69, 2739, 15], [1302, 70, 2739, 15, "default"], [1302, 77, 2739, 19], [1303, 16, 2740, 16, "style"], [1303, 21, 2740, 21], [1303, 23, 2740, 23], [1304, 18, 2741, 18, "backgroundColor"], [1304, 33, 2741, 33], [1304, 35, 2741, 35], [1304, 41, 2741, 41], [1305, 18, 2742, 18, "borderRadius"], [1305, 30, 2742, 30], [1305, 32, 2742, 32], [1305, 34, 2742, 34], [1306, 18, 2743, 18, "borderWidth"], [1306, 29, 2743, 29], [1306, 31, 2743, 31], [1306, 32, 2743, 32], [1307, 18, 2744, 18, "borderColor"], [1307, 29, 2744, 29], [1307, 31, 2744, 31], [1307, 40, 2744, 40], [1308, 18, 2745, 18, "padding"], [1308, 25, 2745, 25], [1308, 27, 2745, 27], [1309, 16, 2746, 16], [1309, 17, 2746, 18], [1310, 16, 2746, 18, "children"], [1310, 24, 2746, 18], [1310, 40, 2748, 16], [1310, 44, 2748, 16, "_jsxDevRuntime"], [1310, 58, 2748, 16], [1310, 59, 2748, 16, "jsxDEV"], [1310, 65, 2748, 16], [1310, 67, 2748, 17, "_View"], [1310, 72, 2748, 17], [1310, 73, 2748, 17, "default"], [1310, 80, 2748, 21], [1311, 18, 2749, 18, "style"], [1311, 23, 2749, 23], [1311, 25, 2749, 25], [1312, 20, 2750, 20, "flexDirection"], [1312, 33, 2750, 33], [1312, 35, 2750, 35], [1312, 40, 2750, 40], [1313, 20, 2751, 20, "alignItems"], [1313, 30, 2751, 30], [1313, 32, 2751, 32], [1313, 44, 2751, 44], [1314, 20, 2752, 20, "padding"], [1314, 27, 2752, 27], [1314, 29, 2752, 29], [1315, 18, 2753, 18], [1315, 19, 2753, 20], [1316, 18, 2753, 20, "children"], [1316, 26, 2753, 20], [1316, 42, 2755, 18], [1316, 46, 2755, 18, "_jsxDevRuntime"], [1316, 60, 2755, 18], [1316, 61, 2755, 18, "jsxDEV"], [1316, 67, 2755, 18], [1316, 69, 2755, 19, "_lucideReactNative"], [1316, 87, 2755, 19], [1316, 88, 2755, 19, "MessageCircle"], [1316, 101, 2755, 32], [1317, 20, 2756, 20, "size"], [1317, 24, 2756, 24], [1317, 26, 2756, 26], [1317, 28, 2756, 29], [1318, 20, 2757, 20, "color"], [1318, 25, 2757, 25], [1318, 27, 2757, 26], [1318, 36, 2757, 35], [1319, 20, 2758, 20, "style"], [1319, 25, 2758, 25], [1319, 27, 2758, 27], [1320, 22, 2758, 29, "marginTop"], [1320, 31, 2758, 38], [1320, 33, 2758, 40], [1320, 34, 2758, 41], [1321, 22, 2758, 43, "marginRight"], [1321, 33, 2758, 54], [1321, 35, 2758, 56], [1322, 20, 2758, 59], [1323, 18, 2758, 61], [1324, 20, 2758, 61, "fileName"], [1324, 28, 2758, 61], [1324, 30, 2758, 61, "_jsxFileName"], [1324, 42, 2758, 61], [1325, 20, 2758, 61, "lineNumber"], [1325, 30, 2758, 61], [1326, 20, 2758, 61, "columnNumber"], [1326, 32, 2758, 61], [1327, 18, 2758, 61], [1327, 25, 2759, 19], [1327, 26, 2759, 20], [1327, 41, 2760, 18], [1327, 45, 2760, 18, "_jsxDevRuntime"], [1327, 59, 2760, 18], [1327, 60, 2760, 18, "jsxDEV"], [1327, 66, 2760, 18], [1327, 68, 2760, 19, "_TextInput"], [1327, 78, 2760, 19], [1327, 79, 2760, 19, "default"], [1327, 86, 2760, 28], [1328, 20, 2761, 20, "style"], [1328, 25, 2761, 25], [1328, 27, 2761, 27], [1329, 22, 2762, 22, "flex"], [1329, 26, 2762, 26], [1329, 28, 2762, 28], [1329, 29, 2762, 29], [1330, 22, 2763, 22, "fontSize"], [1330, 30, 2763, 30], [1330, 32, 2763, 32], [1330, 34, 2763, 34], [1331, 22, 2764, 22, "color"], [1331, 27, 2764, 27], [1331, 29, 2764, 29], [1331, 38, 2764, 38], [1332, 22, 2765, 22, "minHeight"], [1332, 31, 2765, 31], [1332, 33, 2765, 33], [1332, 36, 2765, 36], [1333, 22, 2766, 22, "textAlignVertical"], [1333, 39, 2766, 39], [1333, 41, 2766, 41], [1334, 20, 2767, 20], [1334, 21, 2767, 22], [1335, 20, 2768, 20, "placeholder"], [1335, 31, 2768, 31], [1335, 33, 2768, 32], [1335, 88, 2768, 87], [1336, 20, 2769, 20, "placeholderTextColor"], [1336, 40, 2769, 40], [1336, 42, 2769, 41], [1336, 51, 2769, 50], [1337, 20, 2770, 20, "value"], [1337, 25, 2770, 25], [1337, 27, 2770, 27, "response"], [1337, 35, 2770, 36], [1338, 20, 2771, 20, "onChangeText"], [1338, 32, 2771, 32], [1338, 34, 2771, 34, "setResponse"], [1338, 45, 2771, 46], [1339, 20, 2772, 20, "multiline"], [1339, 29, 2772, 29], [1340, 20, 2773, 20, "max<PERSON><PERSON><PERSON>"], [1340, 29, 2773, 29], [1340, 31, 2773, 31], [1341, 18, 2773, 35], [1342, 20, 2773, 35, "fileName"], [1342, 28, 2773, 35], [1342, 30, 2773, 35, "_jsxFileName"], [1342, 42, 2773, 35], [1343, 20, 2773, 35, "lineNumber"], [1343, 30, 2773, 35], [1344, 20, 2773, 35, "columnNumber"], [1344, 32, 2773, 35], [1345, 18, 2773, 35], [1345, 25, 2774, 19], [1345, 26, 2774, 20], [1346, 16, 2774, 20], [1347, 18, 2774, 20, "fileName"], [1347, 26, 2774, 20], [1347, 28, 2774, 20, "_jsxFileName"], [1347, 40, 2774, 20], [1348, 18, 2774, 20, "lineNumber"], [1348, 28, 2774, 20], [1349, 18, 2774, 20, "columnNumber"], [1349, 30, 2774, 20], [1350, 16, 2774, 20], [1350, 23, 2775, 22], [1350, 24, 2775, 23], [1350, 39, 2777, 16], [1350, 43, 2777, 16, "_jsxDevRuntime"], [1350, 57, 2777, 16], [1350, 58, 2777, 16, "jsxDEV"], [1350, 64, 2777, 16], [1350, 66, 2777, 17, "_View"], [1350, 71, 2777, 17], [1350, 72, 2777, 17, "default"], [1350, 79, 2777, 21], [1351, 18, 2778, 18, "style"], [1351, 23, 2778, 23], [1351, 25, 2778, 25], [1352, 20, 2779, 20, "flexDirection"], [1352, 33, 2779, 33], [1352, 35, 2779, 35], [1352, 40, 2779, 40], [1353, 20, 2780, 20, "justifyContent"], [1353, 34, 2780, 34], [1353, 36, 2780, 36], [1353, 51, 2780, 51], [1354, 20, 2781, 20, "alignItems"], [1354, 30, 2781, 30], [1354, 32, 2781, 32], [1354, 40, 2781, 40], [1355, 20, 2782, 20, "paddingHorizontal"], [1355, 37, 2782, 37], [1355, 39, 2782, 39], [1355, 41, 2782, 41], [1356, 20, 2783, 20, "paddingBottom"], [1356, 33, 2783, 33], [1356, 35, 2783, 35], [1357, 18, 2784, 18], [1357, 19, 2784, 20], [1358, 18, 2784, 20, "children"], [1358, 26, 2784, 20], [1358, 42, 2786, 18], [1358, 46, 2786, 18, "_jsxDevRuntime"], [1358, 60, 2786, 18], [1358, 61, 2786, 18, "jsxDEV"], [1358, 67, 2786, 18], [1358, 69, 2786, 19, "_Text"], [1358, 74, 2786, 19], [1358, 75, 2786, 19, "default"], [1358, 82, 2786, 23], [1359, 20, 2786, 24, "style"], [1359, 25, 2786, 29], [1359, 27, 2786, 31], [1360, 22, 2786, 33, "fontSize"], [1360, 30, 2786, 41], [1360, 32, 2786, 43], [1360, 34, 2786, 45], [1361, 22, 2786, 47, "color"], [1361, 27, 2786, 52], [1361, 29, 2786, 54], [1362, 20, 2786, 64], [1362, 21, 2786, 66], [1363, 20, 2786, 66, "children"], [1363, 28, 2786, 66], [1363, 30, 2786, 67], [1364, 18, 2788, 18], [1365, 20, 2788, 18, "fileName"], [1365, 28, 2788, 18], [1365, 30, 2788, 18, "_jsxFileName"], [1365, 42, 2788, 18], [1366, 20, 2788, 18, "lineNumber"], [1366, 30, 2788, 18], [1367, 20, 2788, 18, "columnNumber"], [1367, 32, 2788, 18], [1368, 18, 2788, 18], [1368, 25, 2788, 24], [1368, 26, 2788, 25], [1368, 41, 2789, 18], [1368, 45, 2789, 18, "_jsxDevRuntime"], [1368, 59, 2789, 18], [1368, 60, 2789, 18, "jsxDEV"], [1368, 66, 2789, 18], [1368, 68, 2789, 19, "_Text"], [1368, 73, 2789, 19], [1368, 74, 2789, 19, "default"], [1368, 81, 2789, 23], [1369, 20, 2789, 24, "style"], [1369, 25, 2789, 29], [1369, 27, 2789, 31], [1370, 22, 2789, 33, "fontSize"], [1370, 30, 2789, 41], [1370, 32, 2789, 43], [1370, 34, 2789, 45], [1371, 22, 2789, 47, "color"], [1371, 27, 2789, 52], [1371, 29, 2789, 54], [1372, 20, 2789, 64], [1372, 21, 2789, 66], [1373, 20, 2789, 66, "children"], [1373, 28, 2789, 66], [1373, 30, 2790, 21], [1373, 33, 2790, 24, "response"], [1373, 41, 2790, 32], [1373, 42, 2790, 33, "length"], [1373, 48, 2790, 39], [1374, 18, 2790, 45], [1375, 20, 2790, 45, "fileName"], [1375, 28, 2790, 45], [1375, 30, 2790, 45, "_jsxFileName"], [1375, 42, 2790, 45], [1376, 20, 2790, 45, "lineNumber"], [1376, 30, 2790, 45], [1377, 20, 2790, 45, "columnNumber"], [1377, 32, 2790, 45], [1378, 18, 2790, 45], [1378, 25, 2791, 24], [1378, 26, 2791, 25], [1379, 16, 2791, 25], [1380, 18, 2791, 25, "fileName"], [1380, 26, 2791, 25], [1380, 28, 2791, 25, "_jsxFileName"], [1380, 40, 2791, 25], [1381, 18, 2791, 25, "lineNumber"], [1381, 28, 2791, 25], [1382, 18, 2791, 25, "columnNumber"], [1382, 30, 2791, 25], [1383, 16, 2791, 25], [1383, 23, 2792, 22], [1383, 24, 2792, 23], [1384, 14, 2792, 23], [1385, 16, 2792, 23, "fileName"], [1385, 24, 2792, 23], [1385, 26, 2792, 23, "_jsxFileName"], [1385, 38, 2792, 23], [1386, 16, 2792, 23, "lineNumber"], [1386, 26, 2792, 23], [1387, 16, 2792, 23, "columnNumber"], [1387, 28, 2792, 23], [1388, 14, 2792, 23], [1388, 21, 2793, 20], [1388, 22, 2793, 21], [1389, 12, 2793, 21], [1390, 14, 2793, 21, "fileName"], [1390, 22, 2793, 21], [1390, 24, 2793, 21, "_jsxFileName"], [1390, 36, 2793, 21], [1391, 14, 2793, 21, "lineNumber"], [1391, 24, 2793, 21], [1392, 14, 2793, 21, "columnNumber"], [1392, 26, 2793, 21], [1393, 12, 2793, 21], [1393, 19, 2794, 18], [1393, 20, 2794, 19], [1393, 35, 2799, 12], [1393, 39, 2799, 12, "_jsxDevRuntime"], [1393, 53, 2799, 12], [1393, 54, 2799, 12, "jsxDEV"], [1393, 60, 2799, 12], [1393, 62, 2799, 13, "_View"], [1393, 67, 2799, 13], [1393, 68, 2799, 13, "default"], [1393, 75, 2799, 17], [1394, 14, 2803, 14, "style"], [1394, 19, 2803, 19], [1394, 21, 2803, 21], [1395, 16, 2807, 16, "backgroundColor"], [1395, 31, 2807, 31], [1395, 33, 2807, 33], [1395, 42, 2807, 42], [1396, 16, 2811, 16, "borderRadius"], [1396, 28, 2811, 28], [1396, 30, 2811, 30], [1396, 32, 2811, 32], [1397, 16, 2815, 16, "padding"], [1397, 23, 2815, 23], [1397, 25, 2815, 25], [1397, 27, 2815, 27], [1398, 16, 2819, 16, "marginBottom"], [1398, 28, 2819, 28], [1398, 30, 2819, 30], [1399, 14, 2823, 14], [1399, 15, 2823, 16], [1400, 14, 2823, 16, "children"], [1400, 22, 2823, 16], [1400, 38, 2831, 14], [1400, 42, 2831, 14, "_jsxDevRuntime"], [1400, 56, 2831, 14], [1400, 57, 2831, 14, "jsxDEV"], [1400, 63, 2831, 14], [1400, 65, 2831, 15, "_Text"], [1400, 70, 2831, 15], [1400, 71, 2831, 15, "default"], [1400, 78, 2831, 19], [1401, 16, 2835, 16, "style"], [1401, 21, 2835, 21], [1401, 23, 2835, 23], [1402, 18, 2839, 18, "fontSize"], [1402, 26, 2839, 26], [1402, 28, 2839, 28], [1402, 30, 2839, 30], [1403, 18, 2843, 18, "fontWeight"], [1403, 28, 2843, 28], [1403, 30, 2843, 30], [1403, 35, 2843, 35], [1404, 18, 2847, 18, "color"], [1404, 23, 2847, 23], [1404, 25, 2847, 25], [1404, 34, 2847, 34], [1405, 18, 2851, 18, "marginBottom"], [1405, 30, 2851, 30], [1405, 32, 2851, 32], [1406, 16, 2855, 16], [1406, 17, 2855, 18], [1407, 16, 2855, 18, "children"], [1407, 24, 2855, 18], [1407, 26, 2859, 15], [1408, 14, 2867, 14], [1409, 16, 2867, 14, "fileName"], [1409, 24, 2867, 14], [1409, 26, 2867, 14, "_jsxFileName"], [1409, 38, 2867, 14], [1410, 16, 2867, 14, "lineNumber"], [1410, 26, 2867, 14], [1411, 16, 2867, 14, "columnNumber"], [1411, 28, 2867, 14], [1412, 14, 2867, 14], [1412, 21, 2867, 20], [1412, 22, 2867, 21], [1412, 37, 2871, 14], [1412, 41, 2871, 14, "_jsxDevRuntime"], [1412, 55, 2871, 14], [1412, 56, 2871, 14, "jsxDEV"], [1412, 62, 2871, 14], [1412, 64, 2871, 15, "_Text"], [1412, 69, 2871, 15], [1412, 70, 2871, 15, "default"], [1412, 77, 2871, 19], [1413, 16, 2875, 16, "style"], [1413, 21, 2875, 21], [1413, 23, 2875, 23], [1414, 18, 2879, 18, "fontSize"], [1414, 26, 2879, 26], [1414, 28, 2879, 28], [1414, 30, 2879, 30], [1415, 18, 2883, 18, "color"], [1415, 23, 2883, 23], [1415, 25, 2883, 25], [1415, 34, 2883, 34], [1416, 18, 2887, 18, "lineHeight"], [1416, 28, 2887, 28], [1416, 30, 2887, 30], [1417, 16, 2891, 16], [1417, 17, 2891, 18], [1418, 16, 2891, 18, "children"], [1418, 24, 2891, 18], [1418, 26, 2899, 17], [1419, 14, 2899, 186], [1420, 16, 2899, 186, "fileName"], [1420, 24, 2899, 186], [1420, 26, 2899, 186, "_jsxFileName"], [1420, 38, 2899, 186], [1421, 16, 2899, 186, "lineNumber"], [1421, 26, 2899, 186], [1422, 16, 2899, 186, "columnNumber"], [1422, 28, 2899, 186], [1423, 14, 2899, 186], [1423, 21, 2903, 20], [1423, 22, 2903, 21], [1424, 12, 2903, 21], [1425, 14, 2903, 21, "fileName"], [1425, 22, 2903, 21], [1425, 24, 2903, 21, "_jsxFileName"], [1425, 36, 2903, 21], [1426, 14, 2903, 21, "lineNumber"], [1426, 24, 2903, 21], [1427, 14, 2903, 21, "columnNumber"], [1427, 26, 2903, 21], [1428, 12, 2903, 21], [1428, 19, 2907, 18], [1428, 20, 2907, 19], [1429, 10, 2907, 19], [1430, 12, 2907, 19, "fileName"], [1430, 20, 2907, 19], [1430, 22, 2907, 19, "_jsxFileName"], [1430, 34, 2907, 19], [1431, 12, 2907, 19, "lineNumber"], [1431, 22, 2907, 19], [1432, 12, 2907, 19, "columnNumber"], [1432, 24, 2907, 19], [1433, 10, 2907, 19], [1433, 17, 2911, 16], [1434, 8, 2911, 17], [1435, 10, 2911, 17, "fileName"], [1435, 18, 2911, 17], [1435, 20, 2911, 17, "_jsxFileName"], [1435, 32, 2911, 17], [1436, 10, 2911, 17, "lineNumber"], [1436, 20, 2911, 17], [1437, 10, 2911, 17, "columnNumber"], [1437, 22, 2911, 17], [1438, 8, 2911, 17], [1438, 15, 2915, 20], [1438, 16, 2915, 21], [1438, 31, 2923, 8], [1438, 35, 2923, 8, "_jsxDevRuntime"], [1438, 49, 2923, 8], [1438, 50, 2923, 8, "jsxDEV"], [1438, 56, 2923, 8], [1438, 58, 2923, 9, "_View"], [1438, 63, 2923, 9], [1438, 64, 2923, 9, "default"], [1438, 71, 2923, 13], [1439, 10, 2927, 10, "style"], [1439, 15, 2927, 15], [1439, 17, 2927, 17], [1440, 12, 2931, 12, "position"], [1440, 20, 2931, 20], [1440, 22, 2931, 22], [1440, 32, 2931, 32], [1441, 12, 2935, 12, "bottom"], [1441, 18, 2935, 18], [1441, 20, 2935, 20], [1441, 21, 2935, 21], [1442, 12, 2939, 12, "left"], [1442, 16, 2939, 16], [1442, 18, 2939, 18], [1442, 19, 2939, 19], [1443, 12, 2943, 12, "right"], [1443, 17, 2943, 17], [1443, 19, 2943, 19], [1443, 20, 2943, 20], [1444, 12, 2947, 12, "backgroundColor"], [1444, 27, 2947, 27], [1444, 29, 2947, 29], [1444, 35, 2947, 35], [1445, 12, 2951, 12, "borderTopWidth"], [1445, 26, 2951, 26], [1445, 28, 2951, 28], [1445, 29, 2951, 29], [1446, 12, 2955, 12, "borderTopColor"], [1446, 26, 2955, 26], [1446, 28, 2955, 28], [1446, 37, 2955, 37], [1447, 12, 2959, 12, "padding"], [1447, 19, 2959, 19], [1447, 21, 2959, 21], [1447, 23, 2959, 23], [1448, 12, 2963, 12, "paddingBottom"], [1448, 25, 2963, 25], [1448, 27, 2963, 27, "insets"], [1448, 33, 2963, 33], [1448, 34, 2963, 34, "bottom"], [1448, 40, 2963, 40], [1448, 43, 2963, 43], [1449, 10, 2967, 10], [1449, 11, 2967, 12], [1450, 10, 2967, 12, "children"], [1450, 18, 2967, 12], [1450, 33, 2975, 10], [1450, 37, 2975, 10, "_jsxDevRuntime"], [1450, 51, 2975, 10], [1450, 52, 2975, 10, "jsxDEV"], [1450, 58, 2975, 10], [1450, 60, 2975, 11, "_TouchableOpacity"], [1450, 77, 2975, 11], [1450, 78, 2975, 11, "default"], [1450, 85, 2975, 27], [1451, 12, 2979, 12, "onPress"], [1451, 19, 2979, 19], [1451, 21, 2979, 21, "submitResponse"], [1451, 35, 2979, 36], [1452, 12, 2983, 12, "disabled"], [1452, 20, 2983, 20], [1452, 22, 2983, 22, "submitting"], [1452, 32, 2983, 32], [1452, 36, 2983, 36], [1452, 37, 2983, 37, "cameraResult"], [1452, 49, 2983, 49], [1452, 53, 2983, 53], [1452, 54, 2983, 54, "response"], [1452, 62, 2983, 62], [1452, 63, 2983, 63, "trim"], [1452, 67, 2983, 67], [1452, 68, 2983, 68], [1452, 69, 2983, 70], [1453, 12, 2987, 12, "style"], [1453, 17, 2987, 17], [1453, 19, 2987, 19], [1454, 14, 2991, 14, "backgroundColor"], [1454, 29, 2991, 29], [1454, 31, 2995, 16, "submitting"], [1454, 41, 2995, 26], [1454, 45, 2995, 30], [1454, 46, 2995, 31, "cameraResult"], [1454, 58, 2995, 43], [1454, 62, 2995, 47], [1454, 63, 2995, 48, "response"], [1454, 71, 2995, 56], [1454, 72, 2995, 57, "trim"], [1454, 76, 2995, 61], [1454, 77, 2995, 62], [1454, 78, 2995, 63], [1454, 81, 2999, 20], [1454, 90, 2999, 29], [1454, 93, 3003, 20], [1454, 102, 3003, 29], [1455, 14, 3007, 14, "borderRadius"], [1455, 26, 3007, 26], [1455, 28, 3007, 28], [1455, 30, 3007, 30], [1456, 14, 3011, 14, "padding"], [1456, 21, 3011, 21], [1456, 23, 3011, 23], [1456, 25, 3011, 25], [1457, 14, 3015, 14, "flexDirection"], [1457, 27, 3015, 27], [1457, 29, 3015, 29], [1457, 34, 3015, 34], [1458, 14, 3019, 14, "alignItems"], [1458, 24, 3019, 24], [1458, 26, 3019, 26], [1458, 34, 3019, 34], [1459, 14, 3023, 14, "justifyContent"], [1459, 28, 3023, 28], [1459, 30, 3023, 30], [1460, 12, 3027, 12], [1460, 13, 3027, 14], [1461, 12, 3027, 14, "children"], [1461, 20, 3027, 14], [1461, 22, 3035, 13, "submitting"], [1461, 32, 3035, 23], [1461, 48, 3039, 14], [1461, 52, 3039, 14, "_jsxDevRuntime"], [1461, 66, 3039, 14], [1461, 67, 3039, 14, "jsxDEV"], [1461, 73, 3039, 14], [1461, 75, 3039, 14, "_jsxDevRuntime"], [1461, 89, 3039, 14], [1461, 90, 3039, 14, "Fragment"], [1461, 98, 3039, 14], [1462, 14, 3039, 14, "children"], [1462, 22, 3039, 14], [1462, 38, 3043, 16], [1462, 42, 3043, 16, "_jsxDevRuntime"], [1462, 56, 3043, 16], [1462, 57, 3043, 16, "jsxDEV"], [1462, 63, 3043, 16], [1462, 65, 3043, 17, "_View"], [1462, 70, 3043, 17], [1462, 71, 3043, 17, "default"], [1462, 78, 3043, 21], [1463, 16, 3047, 18, "style"], [1463, 21, 3047, 23], [1463, 23, 3047, 25], [1464, 18, 3051, 20, "width"], [1464, 23, 3051, 25], [1464, 25, 3051, 27], [1464, 27, 3051, 29], [1465, 18, 3055, 20, "height"], [1465, 24, 3055, 26], [1465, 26, 3055, 28], [1465, 28, 3055, 30], [1466, 18, 3059, 20, "borderRadius"], [1466, 30, 3059, 32], [1466, 32, 3059, 34], [1466, 33, 3059, 35], [1467, 18, 3063, 20, "borderWidth"], [1467, 29, 3063, 31], [1467, 31, 3063, 33], [1467, 32, 3063, 34], [1468, 18, 3067, 20, "borderColor"], [1468, 29, 3067, 31], [1468, 31, 3067, 33], [1468, 37, 3067, 39], [1469, 18, 3071, 20, "borderTopColor"], [1469, 32, 3071, 34], [1469, 34, 3071, 36], [1469, 47, 3071, 49], [1470, 18, 3075, 20, "marginRight"], [1470, 29, 3075, 31], [1470, 31, 3075, 33], [1471, 16, 3079, 18], [1472, 14, 3079, 20], [1473, 16, 3079, 20, "fileName"], [1473, 24, 3079, 20], [1473, 26, 3079, 20, "_jsxFileName"], [1473, 38, 3079, 20], [1474, 16, 3079, 20, "lineNumber"], [1474, 26, 3079, 20], [1475, 16, 3079, 20, "columnNumber"], [1475, 28, 3079, 20], [1476, 14, 3079, 20], [1476, 21, 3083, 17], [1476, 22, 3083, 18], [1476, 37, 3087, 16], [1476, 41, 3087, 16, "_jsxDevRuntime"], [1476, 55, 3087, 16], [1476, 56, 3087, 16, "jsxDEV"], [1476, 62, 3087, 16], [1476, 64, 3087, 17, "_Text"], [1476, 69, 3087, 17], [1476, 70, 3087, 17, "default"], [1476, 77, 3087, 21], [1477, 16, 3091, 18, "style"], [1477, 21, 3091, 23], [1477, 23, 3091, 25], [1478, 18, 3091, 27, "fontSize"], [1478, 26, 3091, 35], [1478, 28, 3091, 37], [1478, 30, 3091, 39], [1479, 18, 3091, 41, "fontWeight"], [1479, 28, 3091, 51], [1479, 30, 3091, 53], [1479, 35, 3091, 58], [1480, 18, 3091, 60, "color"], [1480, 23, 3091, 65], [1480, 25, 3091, 67], [1481, 16, 3091, 74], [1481, 17, 3091, 76], [1482, 16, 3091, 76, "children"], [1482, 24, 3091, 76], [1482, 26, 3095, 17], [1483, 14, 3103, 16], [1484, 16, 3103, 16, "fileName"], [1484, 24, 3103, 16], [1484, 26, 3103, 16, "_jsxFileName"], [1484, 38, 3103, 16], [1485, 16, 3103, 16, "lineNumber"], [1485, 26, 3103, 16], [1486, 16, 3103, 16, "columnNumber"], [1486, 28, 3103, 16], [1487, 14, 3103, 16], [1487, 21, 3103, 22], [1487, 22, 3103, 23], [1488, 12, 3103, 23], [1488, 27, 3107, 16], [1488, 28, 3107, 17], [1488, 44, 3115, 14], [1488, 48, 3115, 14, "_jsxDevRuntime"], [1488, 62, 3115, 14], [1488, 63, 3115, 14, "jsxDEV"], [1488, 69, 3115, 14], [1488, 71, 3115, 14, "_jsxDevRuntime"], [1488, 85, 3115, 14], [1488, 86, 3115, 14, "Fragment"], [1488, 94, 3115, 14], [1489, 14, 3115, 14, "children"], [1489, 22, 3115, 14], [1489, 38, 3119, 16], [1489, 42, 3119, 16, "_jsxDevRuntime"], [1489, 56, 3119, 16], [1489, 57, 3119, 16, "jsxDEV"], [1489, 63, 3119, 16], [1489, 65, 3119, 17, "_lucideReactNative"], [1489, 83, 3119, 17], [1489, 84, 3119, 17, "Send"], [1489, 88, 3119, 21], [1490, 16, 3119, 22, "size"], [1490, 20, 3119, 26], [1490, 22, 3119, 28], [1490, 24, 3119, 31], [1491, 16, 3119, 32, "color"], [1491, 21, 3119, 37], [1491, 23, 3119, 38], [1492, 14, 3119, 44], [1493, 16, 3119, 44, "fileName"], [1493, 24, 3119, 44], [1493, 26, 3119, 44, "_jsxFileName"], [1493, 38, 3119, 44], [1494, 16, 3119, 44, "lineNumber"], [1494, 26, 3119, 44], [1495, 16, 3119, 44, "columnNumber"], [1495, 28, 3119, 44], [1496, 14, 3119, 44], [1496, 21, 3119, 46], [1496, 22, 3119, 47], [1496, 37, 3123, 16], [1496, 41, 3123, 16, "_jsxDevRuntime"], [1496, 55, 3123, 16], [1496, 56, 3123, 16, "jsxDEV"], [1496, 62, 3123, 16], [1496, 64, 3123, 17, "_Text"], [1496, 69, 3123, 17], [1496, 70, 3123, 17, "default"], [1496, 77, 3123, 21], [1497, 16, 3127, 18, "style"], [1497, 21, 3127, 23], [1497, 23, 3127, 25], [1498, 18, 3131, 20, "fontSize"], [1498, 26, 3131, 28], [1498, 28, 3131, 30], [1498, 30, 3131, 32], [1499, 18, 3135, 20, "fontWeight"], [1499, 28, 3135, 30], [1499, 30, 3135, 32], [1499, 35, 3135, 37], [1500, 18, 3139, 20, "color"], [1500, 23, 3139, 25], [1500, 25, 3139, 27], [1500, 31, 3139, 33], [1501, 18, 3143, 20, "marginLeft"], [1501, 28, 3143, 30], [1501, 30, 3143, 32], [1502, 16, 3147, 18], [1502, 17, 3147, 20], [1503, 16, 3147, 20, "children"], [1503, 24, 3147, 20], [1503, 26, 3155, 19], [1503, 47, 3155, 40, "question"], [1503, 55, 3155, 48], [1503, 56, 3155, 49, "reward"], [1503, 62, 3155, 55], [1503, 63, 3155, 56, "toFixed"], [1503, 70, 3155, 63], [1503, 71, 3155, 64], [1503, 72, 3155, 65], [1503, 73, 3155, 66], [1504, 14, 3155, 69], [1505, 16, 3155, 69, "fileName"], [1505, 24, 3155, 69], [1505, 26, 3155, 69, "_jsxFileName"], [1505, 38, 3155, 69], [1506, 16, 3155, 69, "lineNumber"], [1506, 26, 3155, 69], [1507, 16, 3155, 69, "columnNumber"], [1507, 28, 3155, 69], [1508, 14, 3155, 69], [1508, 21, 3159, 22], [1508, 22, 3159, 23], [1509, 12, 3159, 23], [1509, 27, 3163, 16], [1510, 10, 3167, 13], [1511, 12, 3167, 13, "fileName"], [1511, 20, 3167, 13], [1511, 22, 3167, 13, "_jsxFileName"], [1511, 34, 3167, 13], [1512, 12, 3167, 13, "lineNumber"], [1512, 22, 3167, 13], [1513, 12, 3167, 13, "columnNumber"], [1513, 24, 3167, 13], [1514, 10, 3167, 13], [1514, 17, 3171, 28], [1515, 8, 3171, 29], [1516, 10, 3171, 29, "fileName"], [1516, 18, 3171, 29], [1516, 20, 3171, 29, "_jsxFileName"], [1516, 32, 3171, 29], [1517, 10, 3171, 29, "lineNumber"], [1517, 20, 3171, 29], [1518, 10, 3171, 29, "columnNumber"], [1518, 22, 3171, 29], [1519, 8, 3171, 29], [1519, 15, 3175, 14], [1519, 16, 3175, 15], [1520, 6, 3175, 15], [1521, 8, 3175, 15, "fileName"], [1521, 16, 3175, 15], [1521, 18, 3175, 15, "_jsxFileName"], [1521, 30, 3175, 15], [1522, 8, 3175, 15, "lineNumber"], [1522, 18, 3175, 15], [1523, 8, 3175, 15, "columnNumber"], [1523, 20, 3175, 15], [1524, 6, 3175, 15], [1524, 13, 3179, 36], [1524, 14, 3179, 37], [1524, 29, 3187, 6], [1524, 33, 3187, 6, "_jsxDevRuntime"], [1524, 47, 3187, 6], [1524, 48, 3187, 6, "jsxDEV"], [1524, 54, 3187, 6], [1524, 56, 3187, 7, "_Modal"], [1524, 62, 3187, 7], [1524, 63, 3187, 7, "default"], [1524, 70, 3187, 12], [1525, 8, 3191, 8, "visible"], [1525, 15, 3191, 15], [1525, 17, 3191, 17, "showCamera"], [1525, 27, 3191, 28], [1526, 8, 3195, 8, "animationType"], [1526, 21, 3195, 21], [1526, 23, 3195, 22], [1526, 30, 3195, 29], [1527, 8, 3199, 8, "presentationStyle"], [1527, 25, 3199, 25], [1527, 27, 3199, 26], [1527, 39, 3199, 38], [1528, 8, 3199, 38, "children"], [1528, 16, 3199, 38], [1528, 18, 3207, 9, "Platform"], [1528, 35, 3207, 17], [1528, 36, 3207, 18, "OS"], [1528, 38, 3207, 20], [1528, 43, 3207, 25], [1528, 48, 3207, 30], [1528, 64, 3211, 10], [1528, 68, 3211, 10, "_jsxDevRuntime"], [1528, 82, 3211, 10], [1528, 83, 3211, 10, "jsxDEV"], [1528, 89, 3211, 10], [1528, 91, 3211, 11, "_EchoCameraWeb"], [1528, 105, 3211, 11], [1528, 106, 3211, 11, "default"], [1528, 113, 3211, 24], [1529, 10, 3215, 12, "userId"], [1529, 16, 3215, 18], [1529, 18, 3215, 19], [1529, 32, 3215, 33], [1530, 10, 3219, 12, "requestId"], [1530, 19, 3219, 21], [1530, 21, 3219, 23, "id"], [1530, 23, 3219, 26], [1531, 10, 3223, 12, "onComplete"], [1531, 20, 3223, 22], [1531, 22, 3223, 24, "handleCameraComplete"], [1531, 42, 3223, 45], [1532, 10, 3227, 12, "onCancel"], [1532, 18, 3227, 20], [1532, 20, 3227, 22, "handleCameraCancel"], [1533, 8, 3227, 41], [1534, 10, 3227, 41, "fileName"], [1534, 18, 3227, 41], [1534, 20, 3227, 41, "_jsxFileName"], [1534, 32, 3227, 41], [1535, 10, 3227, 41, "lineNumber"], [1535, 20, 3227, 41], [1536, 10, 3227, 41, "columnNumber"], [1536, 22, 3227, 41], [1537, 8, 3227, 41], [1537, 15, 3231, 11], [1537, 16, 3231, 12], [1537, 32, 3239, 10], [1537, 36, 3239, 10, "_jsxDevRuntime"], [1537, 50, 3239, 10], [1537, 51, 3239, 10, "jsxDEV"], [1537, 57, 3239, 10], [1537, 59, 3239, 11, "_EchoCameraUnified"], [1537, 77, 3239, 11], [1537, 78, 3239, 11, "default"], [1537, 85, 3239, 28], [1538, 10, 3243, 12, "userId"], [1538, 16, 3243, 18], [1538, 18, 3243, 19], [1538, 32, 3243, 33], [1539, 10, 3247, 12, "requestId"], [1539, 19, 3247, 21], [1539, 21, 3247, 23, "id"], [1539, 23, 3247, 26], [1540, 10, 3251, 12, "onComplete"], [1540, 20, 3251, 22], [1540, 22, 3251, 24, "handleCameraComplete"], [1540, 42, 3251, 45], [1541, 10, 3255, 12, "onCancel"], [1541, 18, 3255, 20], [1541, 20, 3255, 22, "handleCameraCancel"], [1542, 8, 3255, 41], [1543, 10, 3255, 41, "fileName"], [1543, 18, 3255, 41], [1543, 20, 3255, 41, "_jsxFileName"], [1543, 32, 3255, 41], [1544, 10, 3255, 41, "lineNumber"], [1544, 20, 3255, 41], [1545, 10, 3255, 41, "columnNumber"], [1545, 22, 3255, 41], [1546, 8, 3255, 41], [1546, 15, 3259, 11], [1547, 6, 3263, 9], [1548, 8, 3263, 9, "fileName"], [1548, 16, 3263, 9], [1548, 18, 3263, 9, "_jsxFileName"], [1548, 30, 3263, 9], [1549, 8, 3263, 9, "lineNumber"], [1549, 18, 3263, 9], [1550, 8, 3263, 9, "columnNumber"], [1550, 20, 3263, 9], [1551, 6, 3263, 9], [1551, 13, 3267, 13], [1551, 14, 3267, 14], [1552, 4, 3267, 14], [1553, 6, 3267, 14, "fileName"], [1553, 14, 3267, 14], [1553, 16, 3267, 14, "_jsxFileName"], [1553, 28, 3267, 14], [1554, 6, 3267, 14, "lineNumber"], [1554, 16, 3267, 14], [1555, 6, 3267, 14, "columnNumber"], [1555, 18, 3267, 14], [1556, 4, 3267, 14], [1556, 11, 3271, 10], [1556, 12, 3271, 11], [1557, 2, 3279, 0], [1558, 2, 3279, 1, "_s"], [1558, 4, 3279, 1], [1558, 5, 133, 24, "RespondScreen"], [1558, 18, 133, 37], [1559, 4, 133, 37], [1559, 12, 137, 17, "useSafeAreaInsets"], [1559, 57, 137, 34], [1559, 59, 141, 17, "useLocalSearchParams"], [1559, 91, 141, 37], [1560, 2, 141, 37], [1561, 2, 141, 37, "_c"], [1561, 4, 141, 37], [1561, 7, 133, 24, "RespondScreen"], [1561, 20, 133, 37], [1562, 2, 133, 37], [1562, 6, 133, 37, "_c"], [1562, 8, 133, 37], [1563, 2, 133, 37, "$RefreshReg$"], [1563, 14, 133, 37], [1563, 15, 133, 37, "_c"], [1563, 17, 133, 37], [1564, 0, 133, 37], [1564, 3]], "functionMap": {"names": ["<global>", "RespondScreen", "useMemo$argument_0", "calculateDistance", "verifyLocation", "useEffect$argument_0", "handleStartCamera", "handleCameraComplete", "handleCameraCancel", "submitResponse", "Promise$argument_0", "onPress", "LocationStatus", "getStatusConfig", "TouchableOpacity.props.onPress", "Image.props.onError", "Image.props.onLoad", "Image.props.onLoadStart", "Image.props.onLoadEnd"], "mappings": "AAA;eCoI;qCCgC;GD4B;4BEwG;GFgD;qCGQ;GHgS;YIQ;GJwC;4BKI;GLwE;2CMI;GN4H;yCOI;GPgB;yBQI;wBC4H,sCD;qBEoC,mBF;GRwC;yBWQ;4BCI;KDoL;uBEoN;eFwB;GXgL;qBagG,mBb;+BcmhB;uBdY;8BeE;uBfI;mCgBE;uBhBI;iCiBE;uBjBI;iCa0L;yBbQ;CDmsB"}}, "type": "js/module"}]}