<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlazeFace Integration Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .camera-container {
            position: relative;
            width: 100%;
            max-width: 640px;
            margin: 20px auto;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 16px;
        }
        button:hover {
            background: #2563EB;
        }
        button:disabled {
            background: #6B7280;
            cursor: not-allowed;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 8px;
            border: 1px solid #3B82F6;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10B981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #EF4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 BlazeFace Integration Test</h1>
        <p>Testing the BlazeFace component integration from the main ECHO app</p>
        
        <div class="controls">
            <button onclick="startCamera()">Start Camera</button>
            <button onclick="stopCamera()">Stop Camera</button>
            <button onclick="testBlazeFace()">Test BlazeFace</button>
        </div>
        
        <div id="status" class="status">
            Ready to test BlazeFace integration
        </div>
        
        <div class="camera-container" id="echo-web-camera">
            <video id="video" autoplay muted playsinline></video>
            <canvas id="overlay"></canvas>
        </div>
        
        <div id="results">
            <h3>Test Results:</h3>
            <ul id="test-list"></ul>
        </div>
    </div>

    <script>
        let video, canvas, ctx;
        let stream = null;
        let blazeFaceComponent = null;

        // Simulate the BlazeFaceCanvas component functionality
        class BlazeFaceCanvasTest {
            constructor(containerId, width, height) {
                this.containerId = containerId;
                this.width = width;
                this.height = height;
                this.canvas = document.getElementById('overlay');
                this.ctx = this.canvas.getContext('2d');
                this.model = null;
                this.isDetecting = false;
                this.faceCount = 0;
                
                this.init();
            }

            async init() {
                console.log('[BlazeFaceTest] Starting initialization...');
                updateStatus('Loading BlazeFace model...', 'info');
                
                try {
                    // Load TensorFlow.js
                    if (!window.tf) {
                        await this.loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');
                    }

                    // Load BlazeFace model
                    if (!window.blazeface) {
                        await this.loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');
                    }

                    // Initialize BlazeFace model
                    console.log('[BlazeFaceTest] Initializing BlazeFace model...');
                    this.model = await window.blazeface.load();
                    console.log('[BlazeFaceTest] ✅ BlazeFace model loaded successfully');
                    
                    updateStatus('✅ BlazeFace model loaded successfully!', 'success');
                    addTestResult('BlazeFace model loading', 'PASS', 'Model loaded without errors');
                    
                    this.startDetection();
                    
                } catch (error) {
                    console.error('[BlazeFaceTest] ❌ Failed to load model:', error);
                    updateStatus('❌ Failed to load BlazeFace model', 'error');
                    addTestResult('BlazeFace model loading', 'FAIL', error.message);
                }
            }

            loadScript(src) {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = src;
                    script.onload = () => resolve();
                    script.onerror = () => reject(new Error(`Failed to load ${src}`));
                    document.head.appendChild(script);
                });
            }

            startDetection() {
                this.isDetecting = true;
                this.detectLoop();
            }

            async detectLoop() {
                if (!this.isDetecting || !this.model) return;

                try {
                    const video = document.getElementById('video');
                    if (!video.videoWidth || !video.videoHeight) {
                        requestAnimationFrame(() => this.detectLoop());
                        return;
                    }

                    // Create tensor from video
                    const tensor = window.tf.browser.fromPixels(video);
                    
                    // Detect faces
                    const predictions = await this.model.estimateFaces(tensor, false, 0.6);
                    tensor.dispose();

                    // Clear canvas
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                    if (predictions.length > 0) {
                        this.faceCount = predictions.length;
                        updateStatus(`🛡️ Protecting ${this.faceCount} face${this.faceCount > 1 ? 's' : ''}`, 'success');
                        
                        // Process each detected face
                        predictions.forEach((prediction, index) => {
                            const [x1, y1] = prediction.topLeft;
                            const [x2, y2] = prediction.bottomRight;

                            // Fix coordinate order
                            let minX = Math.min(x1, x2);
                            let maxX = Math.max(x1, x2);
                            const minY = Math.min(y1, y2);
                            const maxY = Math.max(y1, y2);
                            
                            // Account for horizontal flip (mirror effect)
                            const canvasWidth = this.canvas.width;
                            const flippedMinX = canvasWidth - maxX;
                            const flippedMaxX = canvasWidth - minX;
                            minX = flippedMinX;
                            maxX = flippedMaxX;

                            // Calculate face dimensions
                            const faceWidth = maxX - minX;
                            const faceHeight = maxY - minY;

                            if (faceWidth <= 0 || faceHeight <= 0) {
                                return;
                            }

                            // Expand the bounding box for better coverage
                            const centerX = (minX + maxX) / 2;
                            const centerY = (minY + maxY) / 2;
                            const expandedWidth = faceWidth * 1.5;
                            const expandedHeight = faceHeight * 1.8;

                            // Ensure positive radii
                            const radiusX = Math.max(expandedWidth / 2, 10);
                            const radiusY = Math.max(expandedHeight / 2, 10);

                            // Apply elliptical blur
                            this.ctx.save();
                            this.ctx.beginPath();
                            this.ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);
                            this.ctx.clip();
                            this.ctx.filter = 'blur(20px)';
                            this.ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
                            this.ctx.restore();
                        });

                        if (this.faceCount > 0) {
                            addTestResult('Face detection', 'PASS', `Detected and blurred ${this.faceCount} face(s)`);
                        }
                    } else {
                        this.faceCount = 0;
                        updateStatus('👀 Looking for faces...', 'info');
                    }

                } catch (error) {
                    console.error('[BlazeFaceTest] Detection error:', error);
                    addTestResult('Face detection', 'FAIL', error.message);
                }

                // Continue detection loop
                if (this.isDetecting) {
                    requestAnimationFrame(() => this.detectLoop());
                }
            }

            stop() {
                this.isDetecting = false;
            }
        }

        async function startCamera() {
            try {
                updateStatus('Requesting camera access...', 'info');
                
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 1280 }, 
                        height: { ideal: 720 },
                        facingMode: 'user'
                    } 
                });
                
                video = document.getElementById('video');
                canvas = document.getElementById('overlay');
                ctx = canvas.getContext('2d');
                
                video.srcObject = stream;
                video.style.display = 'block';
                
                video.onloadedmetadata = () => {
                    // Set overlay canvas size to match video
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    canvas.style.width = '100%';
                    canvas.style.height = '100%';
                    canvas.style.display = 'block';
                    
                    updateStatus('✅ Camera started successfully!', 'success');
                    addTestResult('Camera access', 'PASS', 'Camera stream obtained and video displayed');
                };
                
            } catch (error) {
                console.error('Camera error:', error);
                updateStatus('❌ Failed to access camera', 'error');
                addTestResult('Camera access', 'FAIL', error.message);
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
                video.style.display = 'none';
                canvas.style.display = 'none';
                updateStatus('🛑 Camera stopped', 'info');
                addTestResult('Camera control', 'PASS', 'Camera stopped successfully');
            }
            
            if (blazeFaceComponent) {
                blazeFaceComponent.stop();
                blazeFaceComponent = null;
            }
        }

        function testBlazeFace() {
            if (!video || !video.videoWidth) {
                updateStatus('❌ Please start camera first', 'error');
                return;
            }

            if (blazeFaceComponent) {
                blazeFaceComponent.stop();
            }

            blazeFaceComponent = new BlazeFaceCanvasTest('echo-web-camera', canvas.width, canvas.height);
            addTestResult('BlazeFace integration', 'PASS', 'BlazeFace component initialized successfully');
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addTestResult(testName, result, details) {
            const testList = document.getElementById('test-list');
            const li = document.createElement('li');
            li.innerHTML = `
                <strong>${testName}:</strong> 
                <span style="color: ${result === 'PASS' ? '#10B981' : '#EF4444'}">${result}</span> 
                - ${details}
            `;
            testList.appendChild(li);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Ready to test BlazeFace integration', 'info');
            addTestResult('Page load', 'PASS', 'Test page loaded successfully');
        });
    </script>
</body>
</html>
