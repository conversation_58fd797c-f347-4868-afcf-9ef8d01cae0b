{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 57, "index": 57}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./ExpoCameraManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 58}, "end": {"line": 2, "column": 48, "index": 106}}], "key": "ncVp/2U6oYCljIxCrL01g7ykEIk=", "exportNames": ["*"]}}, {"name": "./CameraView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 107}, "end": {"line": 3, "column": 53, "index": 160}}], "key": "hpN6bjeXHg+dJJuE/rdNMd+R0y8=", "exportNames": ["*"]}}, {"name": "./Camera.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 79, "column": 0, "index": 3131}, "end": {"line": 79, "column": 31, "index": 3162}}], "key": "YcvES77PsJEiJoknRXvymJVPk40=", "exportNames": ["*"]}}, {"name": "./PictureRef", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 80, "column": 0, "index": 3163}, "end": {"line": 80, "column": 29, "index": 3192}}], "key": "zwNiHbCcllrky6ywgVgLV/oCxQM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    useCameraPermissions: true,\n    useMicrophonePermissions: true,\n    scanFromURLAsync: true,\n    Camera: true,\n    CameraView: true\n  };\n  exports.Camera = void 0;\n  Object.defineProperty(exports, \"CameraView\", {\n    enumerable: true,\n    get: function () {\n      return _CameraView.default;\n    }\n  });\n  exports.scanFromURLAsync = scanFromURLAsync;\n  exports.useMicrophonePermissions = exports.useCameraPermissions = void 0;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _ExpoCameraManager = _interopRequireDefault(require(_dependencyMap[2], \"./ExpoCameraManager\"));\n  var _CameraView = _interopRequireDefault(require(_dependencyMap[3], \"./CameraView\"));\n  var _Camera = require(_dependencyMap[4], \"./Camera.types\");\n  Object.keys(_Camera).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _Camera[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Camera[key];\n      }\n    });\n  });\n  var _PictureRef = require(_dependencyMap[5], \"./PictureRef\");\n  Object.keys(_PictureRef).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _PictureRef[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _PictureRef[key];\n      }\n    });\n  });\n  // @needsAudit\n  /**\n   * Checks user's permissions for accessing camera.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  async function getCameraPermissionsAsync() {\n    return _ExpoCameraManager.default.getCameraPermissionsAsync();\n  }\n  // @needsAudit\n  /**\n   * Asks the user to grant permissions for accessing camera.\n   * On iOS this will require apps to specify an `NSCameraUsageDescription` entry in the **Info.plist**.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  async function requestCameraPermissionsAsync() {\n    return _ExpoCameraManager.default.requestCameraPermissionsAsync();\n  }\n  // @needsAudit\n  /**\n   * Check or request permissions to access the camera.\n   * This uses both `requestCameraPermissionsAsync` and `getCameraPermissionsAsync` to interact with the permissions.\n   *\n   * @example\n   * ```ts\n   * const [status, requestPermission] = useCameraPermissions();\n   * ```\n   */\n  const useCameraPermissions = exports.useCameraPermissions = (0, _expoModulesCore.createPermissionHook)({\n    getMethod: getCameraPermissionsAsync,\n    requestMethod: requestCameraPermissionsAsync\n  });\n  // @needsAudit\n  /**\n   * Checks user's permissions for accessing microphone.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  async function getMicrophonePermissionsAsync() {\n    return _ExpoCameraManager.default.getMicrophonePermissionsAsync();\n  }\n  // @needsAudit\n  /**\n   * Asks the user to grant permissions for accessing the microphone.\n   * On iOS this will require apps to specify an `NSMicrophoneUsageDescription` entry in the **Info.plist**.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  async function requestMicrophonePermissionsAsync() {\n    return _ExpoCameraManager.default.requestMicrophonePermissionsAsync();\n  }\n  // @needsAudit\n  /**\n   * Check or request permissions to access the microphone.\n   * This uses both `requestMicrophonePermissionsAsync` and `getMicrophonePermissionsAsync` to interact with the permissions.\n   *\n   * @example\n   * ```ts\n   * const [status, requestPermission] = Camera.useMicrophonePermissions();\n   * ```\n   */\n  const useMicrophonePermissions = exports.useMicrophonePermissions = (0, _expoModulesCore.createPermissionHook)({\n    getMethod: getMicrophonePermissionsAsync,\n    requestMethod: requestMicrophonePermissionsAsync\n  });\n  /**\n   * Scan bar codes from the image at the given URL.\n   * @param url URL to get the image from.\n   * @param barcodeTypes An array of bar code types. Defaults to all supported bar code types on\n   * the platform.\n   * > __Note:__ Only QR codes are supported on iOS.\n   * On android, the barcode should take up the majority of the image for best results.\n   * @return A possibly empty array of objects of the `BarcodeScanningResult` shape, where the type\n   * refers to the barcode type that was scanned and the data is the information encoded in the barcode.\n   */\n  async function scanFromURLAsync(url, barcodeTypes = ['qr']) {\n    return _ExpoCameraManager.default.scanFromURLAsync(url, barcodeTypes);\n  }\n  /**\n   * @hidden\n   */\n  const Camera = exports.Camera = {\n    getCameraPermissionsAsync,\n    requestCameraPermissionsAsync,\n    getMicrophonePermissionsAsync,\n    requestMicrophonePermissionsAsync,\n    scanFromURLAsync\n  };\n});", "lineCount": 134, "map": [[22, 2, 1, 0], [22, 6, 1, 0, "_expoModulesCore"], [22, 22, 1, 0], [22, 25, 1, 0, "require"], [22, 32, 1, 0], [22, 33, 1, 0, "_dependencyMap"], [22, 47, 1, 0], [23, 2, 2, 0], [23, 6, 2, 0, "_ExpoCameraManager"], [23, 24, 2, 0], [23, 27, 2, 0, "_interopRequireDefault"], [23, 49, 2, 0], [23, 50, 2, 0, "require"], [23, 57, 2, 0], [23, 58, 2, 0, "_dependencyMap"], [23, 72, 2, 0], [24, 2, 3, 0], [24, 6, 3, 0, "_CameraView"], [24, 17, 3, 0], [24, 20, 3, 0, "_interopRequireDefault"], [24, 42, 3, 0], [24, 43, 3, 0, "require"], [24, 50, 3, 0], [24, 51, 3, 0, "_dependencyMap"], [24, 65, 3, 0], [25, 2, 79, 0], [25, 6, 79, 0, "_Camera"], [25, 13, 79, 0], [25, 16, 79, 0, "require"], [25, 23, 79, 0], [25, 24, 79, 0, "_dependencyMap"], [25, 38, 79, 0], [26, 2, 79, 0, "Object"], [26, 8, 79, 0], [26, 9, 79, 0, "keys"], [26, 13, 79, 0], [26, 14, 79, 0, "_Camera"], [26, 21, 79, 0], [26, 23, 79, 0, "for<PERSON>ach"], [26, 30, 79, 0], [26, 41, 79, 0, "key"], [26, 44, 79, 0], [27, 4, 79, 0], [27, 8, 79, 0, "key"], [27, 11, 79, 0], [27, 29, 79, 0, "key"], [27, 32, 79, 0], [28, 4, 79, 0], [28, 8, 79, 0, "Object"], [28, 14, 79, 0], [28, 15, 79, 0, "prototype"], [28, 24, 79, 0], [28, 25, 79, 0, "hasOwnProperty"], [28, 39, 79, 0], [28, 40, 79, 0, "call"], [28, 44, 79, 0], [28, 45, 79, 0, "_exportNames"], [28, 57, 79, 0], [28, 59, 79, 0, "key"], [28, 62, 79, 0], [29, 4, 79, 0], [29, 8, 79, 0, "key"], [29, 11, 79, 0], [29, 15, 79, 0, "exports"], [29, 22, 79, 0], [29, 26, 79, 0, "exports"], [29, 33, 79, 0], [29, 34, 79, 0, "key"], [29, 37, 79, 0], [29, 43, 79, 0, "_Camera"], [29, 50, 79, 0], [29, 51, 79, 0, "key"], [29, 54, 79, 0], [30, 4, 79, 0, "Object"], [30, 10, 79, 0], [30, 11, 79, 0, "defineProperty"], [30, 25, 79, 0], [30, 26, 79, 0, "exports"], [30, 33, 79, 0], [30, 35, 79, 0, "key"], [30, 38, 79, 0], [31, 6, 79, 0, "enumerable"], [31, 16, 79, 0], [32, 6, 79, 0, "get"], [32, 9, 79, 0], [32, 20, 79, 0, "get"], [32, 21, 79, 0], [33, 8, 79, 0], [33, 15, 79, 0, "_Camera"], [33, 22, 79, 0], [33, 23, 79, 0, "key"], [33, 26, 79, 0], [34, 6, 79, 0], [35, 4, 79, 0], [36, 2, 79, 0], [37, 2, 80, 0], [37, 6, 80, 0, "_PictureRef"], [37, 17, 80, 0], [37, 20, 80, 0, "require"], [37, 27, 80, 0], [37, 28, 80, 0, "_dependencyMap"], [37, 42, 80, 0], [38, 2, 80, 0, "Object"], [38, 8, 80, 0], [38, 9, 80, 0, "keys"], [38, 13, 80, 0], [38, 14, 80, 0, "_PictureRef"], [38, 25, 80, 0], [38, 27, 80, 0, "for<PERSON>ach"], [38, 34, 80, 0], [38, 45, 80, 0, "key"], [38, 48, 80, 0], [39, 4, 80, 0], [39, 8, 80, 0, "key"], [39, 11, 80, 0], [39, 29, 80, 0, "key"], [39, 32, 80, 0], [40, 4, 80, 0], [40, 8, 80, 0, "Object"], [40, 14, 80, 0], [40, 15, 80, 0, "prototype"], [40, 24, 80, 0], [40, 25, 80, 0, "hasOwnProperty"], [40, 39, 80, 0], [40, 40, 80, 0, "call"], [40, 44, 80, 0], [40, 45, 80, 0, "_exportNames"], [40, 57, 80, 0], [40, 59, 80, 0, "key"], [40, 62, 80, 0], [41, 4, 80, 0], [41, 8, 80, 0, "key"], [41, 11, 80, 0], [41, 15, 80, 0, "exports"], [41, 22, 80, 0], [41, 26, 80, 0, "exports"], [41, 33, 80, 0], [41, 34, 80, 0, "key"], [41, 37, 80, 0], [41, 43, 80, 0, "_PictureRef"], [41, 54, 80, 0], [41, 55, 80, 0, "key"], [41, 58, 80, 0], [42, 4, 80, 0, "Object"], [42, 10, 80, 0], [42, 11, 80, 0, "defineProperty"], [42, 25, 80, 0], [42, 26, 80, 0, "exports"], [42, 33, 80, 0], [42, 35, 80, 0, "key"], [42, 38, 80, 0], [43, 6, 80, 0, "enumerable"], [43, 16, 80, 0], [44, 6, 80, 0, "get"], [44, 9, 80, 0], [44, 20, 80, 0, "get"], [44, 21, 80, 0], [45, 8, 80, 0], [45, 15, 80, 0, "_PictureRef"], [45, 26, 80, 0], [45, 27, 80, 0, "key"], [45, 30, 80, 0], [46, 6, 80, 0], [47, 4, 80, 0], [48, 2, 80, 0], [49, 2, 4, 0], [50, 2, 5, 0], [51, 0, 6, 0], [52, 0, 7, 0], [53, 0, 8, 0], [54, 2, 9, 0], [54, 17, 9, 15, "getCameraPermissionsAsync"], [54, 42, 9, 40, "getCameraPermissionsAsync"], [54, 43, 9, 40], [54, 45, 9, 43], [55, 4, 10, 4], [55, 11, 10, 11, "CameraManager"], [55, 37, 10, 24], [55, 38, 10, 25, "getCameraPermissionsAsync"], [55, 63, 10, 50], [55, 64, 10, 51], [55, 65, 10, 52], [56, 2, 11, 0], [57, 2, 12, 0], [58, 2, 13, 0], [59, 0, 14, 0], [60, 0, 15, 0], [61, 0, 16, 0], [62, 0, 17, 0], [63, 2, 18, 0], [63, 17, 18, 15, "requestCameraPermissionsAsync"], [63, 46, 18, 44, "requestCameraPermissionsAsync"], [63, 47, 18, 44], [63, 49, 18, 47], [64, 4, 19, 4], [64, 11, 19, 11, "CameraManager"], [64, 37, 19, 24], [64, 38, 19, 25, "requestCameraPermissionsAsync"], [64, 67, 19, 54], [64, 68, 19, 55], [64, 69, 19, 56], [65, 2, 20, 0], [66, 2, 21, 0], [67, 2, 22, 0], [68, 0, 23, 0], [69, 0, 24, 0], [70, 0, 25, 0], [71, 0, 26, 0], [72, 0, 27, 0], [73, 0, 28, 0], [74, 0, 29, 0], [75, 0, 30, 0], [76, 2, 31, 7], [76, 8, 31, 13, "useCameraPermissions"], [76, 28, 31, 33], [76, 31, 31, 33, "exports"], [76, 38, 31, 33], [76, 39, 31, 33, "useCameraPermissions"], [76, 59, 31, 33], [76, 62, 31, 36], [76, 66, 31, 36, "createPermissionHook"], [76, 103, 31, 56], [76, 105, 31, 57], [77, 4, 32, 4, "getMethod"], [77, 13, 32, 13], [77, 15, 32, 15, "getCameraPermissionsAsync"], [77, 40, 32, 40], [78, 4, 33, 4, "requestMethod"], [78, 17, 33, 17], [78, 19, 33, 19, "requestCameraPermissionsAsync"], [79, 2, 34, 0], [79, 3, 34, 1], [79, 4, 34, 2], [80, 2, 35, 0], [81, 2, 36, 0], [82, 0, 37, 0], [83, 0, 38, 0], [84, 0, 39, 0], [85, 2, 40, 0], [85, 17, 40, 15, "getMicrophonePermissionsAsync"], [85, 46, 40, 44, "getMicrophonePermissionsAsync"], [85, 47, 40, 44], [85, 49, 40, 47], [86, 4, 41, 4], [86, 11, 41, 11, "CameraManager"], [86, 37, 41, 24], [86, 38, 41, 25, "getMicrophonePermissionsAsync"], [86, 67, 41, 54], [86, 68, 41, 55], [86, 69, 41, 56], [87, 2, 42, 0], [88, 2, 43, 0], [89, 2, 44, 0], [90, 0, 45, 0], [91, 0, 46, 0], [92, 0, 47, 0], [93, 0, 48, 0], [94, 2, 49, 0], [94, 17, 49, 15, "requestMicrophonePermissionsAsync"], [94, 50, 49, 48, "requestMicrophonePermissionsAsync"], [94, 51, 49, 48], [94, 53, 49, 51], [95, 4, 50, 4], [95, 11, 50, 11, "CameraManager"], [95, 37, 50, 24], [95, 38, 50, 25, "requestMicrophonePermissionsAsync"], [95, 71, 50, 58], [95, 72, 50, 59], [95, 73, 50, 60], [96, 2, 51, 0], [97, 2, 52, 0], [98, 2, 53, 0], [99, 0, 54, 0], [100, 0, 55, 0], [101, 0, 56, 0], [102, 0, 57, 0], [103, 0, 58, 0], [104, 0, 59, 0], [105, 0, 60, 0], [106, 0, 61, 0], [107, 2, 62, 7], [107, 8, 62, 13, "useMicrophonePermissions"], [107, 32, 62, 37], [107, 35, 62, 37, "exports"], [107, 42, 62, 37], [107, 43, 62, 37, "useMicrophonePermissions"], [107, 67, 62, 37], [107, 70, 62, 40], [107, 74, 62, 40, "createPermissionHook"], [107, 111, 62, 60], [107, 113, 62, 61], [108, 4, 63, 4, "getMethod"], [108, 13, 63, 13], [108, 15, 63, 15, "getMicrophonePermissionsAsync"], [108, 44, 63, 44], [109, 4, 64, 4, "requestMethod"], [109, 17, 64, 17], [109, 19, 64, 19, "requestMicrophonePermissionsAsync"], [110, 2, 65, 0], [110, 3, 65, 1], [110, 4, 65, 2], [111, 2, 66, 0], [112, 0, 67, 0], [113, 0, 68, 0], [114, 0, 69, 0], [115, 0, 70, 0], [116, 0, 71, 0], [117, 0, 72, 0], [118, 0, 73, 0], [119, 0, 74, 0], [120, 0, 75, 0], [121, 2, 76, 7], [121, 17, 76, 22, "scanFromURLAsync"], [121, 33, 76, 38, "scanFromURLAsync"], [121, 34, 76, 39, "url"], [121, 37, 76, 42], [121, 39, 76, 44, "barcodeTypes"], [121, 51, 76, 56], [121, 54, 76, 59], [121, 55, 76, 60], [121, 59, 76, 64], [121, 60, 76, 65], [121, 62, 76, 67], [122, 4, 77, 4], [122, 11, 77, 11, "CameraManager"], [122, 37, 77, 24], [122, 38, 77, 25, "scanFromURLAsync"], [122, 54, 77, 41], [122, 55, 77, 42, "url"], [122, 58, 77, 45], [122, 60, 77, 47, "barcodeTypes"], [122, 72, 77, 59], [122, 73, 77, 60], [123, 2, 78, 0], [124, 2, 81, 0], [125, 0, 82, 0], [126, 0, 83, 0], [127, 2, 84, 7], [127, 8, 84, 13, "Camera"], [127, 14, 84, 19], [127, 17, 84, 19, "exports"], [127, 24, 84, 19], [127, 25, 84, 19, "Camera"], [127, 31, 84, 19], [127, 34, 84, 22], [128, 4, 85, 4, "getCameraPermissionsAsync"], [128, 29, 85, 29], [129, 4, 86, 4, "requestCameraPermissionsAsync"], [129, 33, 86, 33], [130, 4, 87, 4, "getMicrophonePermissionsAsync"], [130, 33, 87, 33], [131, 4, 88, 4, "requestMicrophonePermissionsAsync"], [131, 37, 88, 37], [132, 4, 89, 4, "scanFromURLAsync"], [133, 2, 90, 0], [133, 3, 90, 1], [134, 0, 90, 2], [134, 3]], "functionMap": {"names": ["<global>", "getCameraPermissionsAsync", "requestCameraPermissionsAsync", "getMicrophonePermissionsAsync", "requestMicrophonePermissionsAsync", "scanFromURLAsync"], "mappings": "AAA;ACQ;CDE;AEO;CFE;AGoB;CHE;AIO;CJE;OKyB;CLE"}}, "type": "js/module"}]}