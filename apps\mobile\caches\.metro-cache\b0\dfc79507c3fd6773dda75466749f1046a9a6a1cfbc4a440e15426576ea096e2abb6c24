{"dependencies": [{"name": "./Easing.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 45, "index": 60}}], "key": "uSvk+4oeQs+GbPQOjyPSGvS9fQg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.convertAnimationObjectToKeyframes = convertAnimationObjectToKeyframes;\n  var _EasingWeb = require(_dependencyMap[0], \"./Easing.web.js\");\n  function convertAnimationObjectToKeyframes(animationObject) {\n    let keyframe = `@keyframes ${animationObject.name} { `;\n    for (const [timestamp, style] of Object.entries(animationObject.style)) {\n      const step = timestamp === 'from' ? 0 : timestamp === 'to' ? 100 : timestamp;\n      keyframe += `${step}% { `;\n      for (const [property, values] of Object.entries(style)) {\n        if (property === 'easing') {\n          let easingName = 'linear';\n          if (values in _EasingWeb.WebEasings) {\n            easingName = values;\n          } else if (values.name in _EasingWeb.WebEasings) {\n            easingName = values.name;\n          }\n          keyframe += `animation-timing-function: cubic-bezier(${_EasingWeb.WebEasings[easingName].toString()});`;\n          continue;\n        }\n        if (property === 'originX') {\n          keyframe += `left: ${values}px; `;\n          continue;\n        }\n        if (property === 'originY') {\n          keyframe += `top: ${values}px; `;\n          continue;\n        }\n        if (property !== 'transform') {\n          keyframe += `${property}: ${values}; `;\n          continue;\n        }\n        keyframe += `transform:`;\n        values.forEach(value => {\n          for (const [transformProperty, transformPropertyValue] of Object.entries(value)) {\n            keyframe += ` ${transformProperty}(${transformPropertyValue})`;\n          }\n        });\n        keyframe += `; `; // Property end\n      }\n      keyframe += `} `; // Timestamp end\n    }\n    keyframe += `} `; // Keyframe end\n\n    return keyframe;\n  }\n});", "lineCount": 51, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "convertAnimationObjectToKeyframes"], [7, 43, 1, 13], [7, 46, 1, 13, "convertAnimationObjectToKeyframes"], [7, 79, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_EasingWeb"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 4, 7], [9, 11, 4, 16, "convertAnimationObjectToKeyframes"], [9, 44, 4, 49, "convertAnimationObjectToKeyframes"], [9, 45, 4, 50, "animationObject"], [9, 60, 4, 65], [9, 62, 4, 67], [10, 4, 5, 2], [10, 8, 5, 6, "keyframe"], [10, 16, 5, 14], [10, 19, 5, 17], [10, 33, 5, 31, "animationObject"], [10, 48, 5, 46], [10, 49, 5, 47, "name"], [10, 53, 5, 51], [10, 58, 5, 56], [11, 4, 6, 2], [11, 9, 6, 7], [11, 15, 6, 13], [11, 16, 6, 14, "timestamp"], [11, 25, 6, 23], [11, 27, 6, 25, "style"], [11, 32, 6, 30], [11, 33, 6, 31], [11, 37, 6, 35, "Object"], [11, 43, 6, 41], [11, 44, 6, 42, "entries"], [11, 51, 6, 49], [11, 52, 6, 50, "animationObject"], [11, 67, 6, 65], [11, 68, 6, 66, "style"], [11, 73, 6, 71], [11, 74, 6, 72], [11, 76, 6, 74], [12, 6, 7, 4], [12, 12, 7, 10, "step"], [12, 16, 7, 14], [12, 19, 7, 17, "timestamp"], [12, 28, 7, 26], [12, 33, 7, 31], [12, 39, 7, 37], [12, 42, 7, 40], [12, 43, 7, 41], [12, 46, 7, 44, "timestamp"], [12, 55, 7, 53], [12, 60, 7, 58], [12, 64, 7, 62], [12, 67, 7, 65], [12, 70, 7, 68], [12, 73, 7, 71, "timestamp"], [12, 82, 7, 80], [13, 6, 8, 4, "keyframe"], [13, 14, 8, 12], [13, 18, 8, 16], [13, 21, 8, 19, "step"], [13, 25, 8, 23], [13, 31, 8, 29], [14, 6, 9, 4], [14, 11, 9, 9], [14, 17, 9, 15], [14, 18, 9, 16, "property"], [14, 26, 9, 24], [14, 28, 9, 26, "values"], [14, 34, 9, 32], [14, 35, 9, 33], [14, 39, 9, 37, "Object"], [14, 45, 9, 43], [14, 46, 9, 44, "entries"], [14, 53, 9, 51], [14, 54, 9, 52, "style"], [14, 59, 9, 57], [14, 60, 9, 58], [14, 62, 9, 60], [15, 8, 10, 6], [15, 12, 10, 10, "property"], [15, 20, 10, 18], [15, 25, 10, 23], [15, 33, 10, 31], [15, 35, 10, 33], [16, 10, 11, 8], [16, 14, 11, 12, "easingName"], [16, 24, 11, 22], [16, 27, 11, 25], [16, 35, 11, 33], [17, 10, 12, 8], [17, 14, 12, 12, "values"], [17, 20, 12, 18], [17, 24, 12, 22, "WebEasings"], [17, 45, 12, 32], [17, 47, 12, 34], [18, 12, 13, 10, "easingName"], [18, 22, 13, 20], [18, 25, 13, 23, "values"], [18, 31, 13, 29], [19, 10, 14, 8], [19, 11, 14, 9], [19, 17, 14, 15], [19, 21, 14, 19, "values"], [19, 27, 14, 25], [19, 28, 14, 26, "name"], [19, 32, 14, 30], [19, 36, 14, 34, "WebEasings"], [19, 57, 14, 44], [19, 59, 14, 46], [20, 12, 15, 10, "easingName"], [20, 22, 15, 20], [20, 25, 15, 23, "values"], [20, 31, 15, 29], [20, 32, 15, 30, "name"], [20, 36, 15, 34], [21, 10, 16, 8], [22, 10, 17, 8, "keyframe"], [22, 18, 17, 16], [22, 22, 17, 20], [22, 65, 17, 63, "WebEasings"], [22, 86, 17, 73], [22, 87, 17, 74, "easingName"], [22, 97, 17, 84], [22, 98, 17, 85], [22, 99, 17, 86, "toString"], [22, 107, 17, 94], [22, 108, 17, 95], [22, 109, 17, 96], [22, 113, 17, 100], [23, 10, 18, 8], [24, 8, 19, 6], [25, 8, 20, 6], [25, 12, 20, 10, "property"], [25, 20, 20, 18], [25, 25, 20, 23], [25, 34, 20, 32], [25, 36, 20, 34], [26, 10, 21, 8, "keyframe"], [26, 18, 21, 16], [26, 22, 21, 20], [26, 31, 21, 29, "values"], [26, 37, 21, 35], [26, 43, 21, 41], [27, 10, 22, 8], [28, 8, 23, 6], [29, 8, 24, 6], [29, 12, 24, 10, "property"], [29, 20, 24, 18], [29, 25, 24, 23], [29, 34, 24, 32], [29, 36, 24, 34], [30, 10, 25, 8, "keyframe"], [30, 18, 25, 16], [30, 22, 25, 20], [30, 30, 25, 28, "values"], [30, 36, 25, 34], [30, 42, 25, 40], [31, 10, 26, 8], [32, 8, 27, 6], [33, 8, 28, 6], [33, 12, 28, 10, "property"], [33, 20, 28, 18], [33, 25, 28, 23], [33, 36, 28, 34], [33, 38, 28, 36], [34, 10, 29, 8, "keyframe"], [34, 18, 29, 16], [34, 22, 29, 20], [34, 25, 29, 23, "property"], [34, 33, 29, 31], [34, 38, 29, 36, "values"], [34, 44, 29, 42], [34, 48, 29, 46], [35, 10, 30, 8], [36, 8, 31, 6], [37, 8, 32, 6, "keyframe"], [37, 16, 32, 14], [37, 20, 32, 18], [37, 32, 32, 30], [38, 8, 33, 6, "values"], [38, 14, 33, 12], [38, 15, 33, 13, "for<PERSON>ach"], [38, 22, 33, 20], [38, 23, 33, 21, "value"], [38, 28, 33, 26], [38, 32, 33, 30], [39, 10, 34, 8], [39, 15, 34, 13], [39, 21, 34, 19], [39, 22, 34, 20, "transformProperty"], [39, 39, 34, 37], [39, 41, 34, 39, "transformPropertyValue"], [39, 63, 34, 61], [39, 64, 34, 62], [39, 68, 34, 66, "Object"], [39, 74, 34, 72], [39, 75, 34, 73, "entries"], [39, 82, 34, 80], [39, 83, 34, 81, "value"], [39, 88, 34, 86], [39, 89, 34, 87], [39, 91, 34, 89], [40, 12, 35, 10, "keyframe"], [40, 20, 35, 18], [40, 24, 35, 22], [40, 28, 35, 26, "transformProperty"], [40, 45, 35, 43], [40, 49, 35, 47, "transformPropertyValue"], [40, 71, 35, 69], [40, 74, 35, 72], [41, 10, 36, 8], [42, 8, 37, 6], [42, 9, 37, 7], [42, 10, 37, 8], [43, 8, 38, 6, "keyframe"], [43, 16, 38, 14], [43, 20, 38, 18], [43, 24, 38, 22], [43, 25, 38, 23], [43, 26, 38, 24], [44, 6, 39, 4], [45, 6, 40, 4, "keyframe"], [45, 14, 40, 12], [45, 18, 40, 16], [45, 22, 40, 20], [45, 23, 40, 21], [45, 24, 40, 22], [46, 4, 41, 2], [47, 4, 42, 2, "keyframe"], [47, 12, 42, 10], [47, 16, 42, 14], [47, 20, 42, 18], [47, 21, 42, 19], [47, 22, 42, 20], [49, 4, 44, 2], [49, 11, 44, 9, "keyframe"], [49, 19, 44, 17], [50, 2, 45, 0], [51, 0, 45, 1], [51, 3]], "functionMap": {"names": ["<global>", "convertAnimationObjectToKeyframes", "values.forEach$argument_0"], "mappings": "AAA;OCG;qBC6B;ODI;CDQ"}}, "type": "js/module"}]}