{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./ExpoCameraManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 48, "index": 48}}], "key": "ncVp/2U6oYCljIxCrL01g7ykEIk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ExpoCameraManager = _interopRequireDefault(require(_dependencyMap[1], \"./ExpoCameraManager\"));\n  var _default = exports.default = _ExpoCameraManager.default.Picture;\n});", "lineCount": 9, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_ExpoCameraManager"], [7, 24, 1, 0], [7, 27, 1, 0, "_interopRequireDefault"], [7, 49, 1, 0], [7, 50, 1, 0, "require"], [7, 57, 1, 0], [7, 58, 1, 0, "_dependencyMap"], [7, 72, 1, 0], [8, 2, 1, 48], [8, 6, 1, 48, "_default"], [8, 14, 1, 48], [8, 17, 1, 48, "exports"], [8, 24, 1, 48], [8, 25, 1, 48, "default"], [8, 32, 1, 48], [8, 35, 2, 15, "CameraManager"], [8, 61, 2, 28], [8, 62, 2, 29, "Picture"], [8, 69, 2, 36], [9, 0, 2, 36], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}