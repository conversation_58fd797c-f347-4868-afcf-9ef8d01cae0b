{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 66, "index": 66}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 67}, "end": {"line": 2, "column": 45, "index": 112}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./ExpoCamera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 113}, "end": {"line": 3, "column": 38, "index": 151}}], "key": "7VL16dFFp4b+wyYwZ4leyXXwJPs=", "exportNames": ["*"]}}, {"name": "./ExpoCameraManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 152}, "end": {"line": 4, "column": 48, "index": 200}}], "key": "ncVp/2U6oYCljIxCrL01g7ykEIk=", "exportNames": ["*"]}}, {"name": "./utils/props", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 201}, "end": {"line": 5, "column": 68, "index": 269}}], "key": "3YYapxwMFrnsJM/mAdwZWAGGiEw=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _react = require(_dependencyMap[2], \"react\");\n  var _ExpoCamera = _interopRequireDefault(require(_dependencyMap[3], \"./ExpoCamera\"));\n  var _ExpoCameraManager = _interopRequireDefault(require(_dependencyMap[4], \"./ExpoCameraManager\"));\n  var _props = require(_dependencyMap[5], \"./utils/props\");\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\expo-camera\\\\build\\\\CameraView.js\";\n  const EventThrottleMs = 500;\n  const _PICTURE_SAVED_CALLBACKS = {};\n  let loggedRenderingChildrenWarning = false;\n  let _GLOBAL_PICTURE_ID = 1;\n  function ensurePictureOptions(options) {\n    if (!options || typeof options !== 'object') {\n      return {};\n    }\n    if (options.quality === undefined) {\n      options.quality = 1;\n    }\n    if (options.mirror) {\n      console.warn('The `mirror` option is deprecated. Please use the `mirror` prop on the `CameraView` instead.');\n    }\n    if (options.onPictureSaved) {\n      const id = _GLOBAL_PICTURE_ID++;\n      _PICTURE_SAVED_CALLBACKS[id] = options.onPictureSaved;\n      options.id = id;\n      options.fastMode = true;\n    }\n    return options;\n  }\n  function ensureRecordingOptions(options = {}) {\n    if (!options || typeof options !== 'object') {\n      return {};\n    }\n    if (options.mirror) {\n      console.warn('The `mirror` option is deprecated. Please use the `mirror` prop on the `CameraView` instead.');\n    }\n    return options;\n  }\n  function _onPictureSaved({\n    nativeEvent\n  }) {\n    const {\n      id,\n      data\n    } = nativeEvent;\n    const callback = _PICTURE_SAVED_CALLBACKS[id];\n    if (callback) {\n      callback(data);\n      delete _PICTURE_SAVED_CALLBACKS[id];\n    }\n  }\n  class CameraView extends _react.Component {\n    /**\n     * Property that determines if the current device has the ability to use `DataScannerViewController` (iOS 16+).\n     */\n    static isModernBarcodeScannerAvailable = _ExpoCameraManager.default.isModernBarcodeScannerAvailable;\n    /**\n     * Check whether the current device has a camera. This is useful for web and simulators cases.\n     * This isn't influenced by the Permissions API (all platforms), or HTTP usage (in the browser).\n     * You will still need to check if the native permission has been accepted.\n     * @platform web\n     */\n    static async isAvailableAsync() {\n      if (!_ExpoCameraManager.default.isAvailableAsync) {\n        throw new _expoModulesCore.UnavailabilityError('expo-camera', 'isAvailableAsync');\n      }\n      return _ExpoCameraManager.default.isAvailableAsync();\n    }\n    // @needsAudit\n    /**\n     * Queries the device for the available video codecs that can be used in video recording.\n     * @return A promise that resolves to a list of strings that represents available codecs.\n     * @platform ios\n     */\n    static async getAvailableVideoCodecsAsync() {\n      if (!_ExpoCameraManager.default.getAvailableVideoCodecsAsync) {\n        throw new _expoModulesCore.UnavailabilityError('Camera', 'getAvailableVideoCodecsAsync');\n      }\n      return _ExpoCameraManager.default.getAvailableVideoCodecsAsync();\n    }\n    /**\n     * Get picture sizes that are supported by the device.\n     * @return Returns a Promise that resolves to an array of strings representing picture sizes that can be passed to `pictureSize` prop.\n     * The list varies across Android devices but is the same for every iOS.\n     */\n    async getAvailablePictureSizesAsync() {\n      return (await this._cameraRef.current?.getAvailablePictureSizes()) ?? [];\n    }\n    /**\n     * Returns the available lenses for the currently selected camera.\n     *\n     * @return Returns a Promise that resolves to an array of strings representing the lens type that can be passed to `selectedLens` prop.\n     * @platform ios\n     */\n    async getAvailableLensesAsync() {\n      return (await this._cameraRef.current?.getAvailableLenses()) ?? [];\n    }\n    /**\n     * Returns an object with the supported features of the camera on the current device.\n     */\n    getSupportedFeatures() {\n      return {\n        isModernBarcodeScannerAvailable: _ExpoCameraManager.default.isModernBarcodeScannerAvailable,\n        toggleRecordingAsyncAvailable: _ExpoCameraManager.default.toggleRecordingAsyncAvailable\n      };\n    }\n    /**\n     * Resumes the camera preview.\n     */\n    async resumePreview() {\n      return this._cameraRef.current?.resumePreview();\n    }\n    /**\n     * Pauses the camera preview. It is not recommended to use `takePictureAsync` when preview is paused.\n     */\n    async pausePreview() {\n      return this._cameraRef.current?.pausePreview();\n    }\n    // Values under keys from this object will be transformed to native options\n    static ConversionTables = _props.ConversionTables;\n    static defaultProps = {\n      zoom: 0,\n      facing: 'back',\n      enableTorch: false,\n      mode: 'picture',\n      flash: 'off'\n    };\n    _cameraRef = /*#__PURE__*/(0, _react.createRef)();\n    _lastEvents = {};\n    _lastEventsTimes = {};\n    async takePictureAsync(options) {\n      const pictureOptions = ensurePictureOptions(options);\n      if (_expoModulesCore.Platform.OS === 'ios' && options?.pictureRef) {\n        return this._cameraRef.current?.takePictureRef?.(options);\n      }\n      return this._cameraRef.current?.takePicture(pictureOptions);\n    }\n    /**\n     * On Android, we will use the [Google code scanner](https://developers.google.com/ml-kit/vision/barcode-scanning/code-scanner).\n     * On iOS, presents a modal view controller that uses the [`DataScannerViewController`](https://developer.apple.com/documentation/visionkit/scanning_data_with_the_camera) available on iOS 16+.\n     * @platform android\n     * @platform ios\n     */\n    static async launchScanner(options) {\n      if (!options) {\n        options = {\n          barcodeTypes: []\n        };\n      }\n      if (_expoModulesCore.Platform.OS !== 'web' && CameraView.isModernBarcodeScannerAvailable) {\n        await _ExpoCameraManager.default.launchScanner(options);\n      }\n    }\n    /**\n     * Dismiss the scanner presented by `launchScanner`.\n     * > **info** On Android, the scanner is dismissed automatically when a barcode is scanned.\n     * @platform ios\n     */\n    static async dismissScanner() {\n      if (_expoModulesCore.Platform.OS !== 'web' && CameraView.isModernBarcodeScannerAvailable) {\n        await _ExpoCameraManager.default.dismissScanner();\n      }\n    }\n    /**\n     * Invokes the `listener` function when a bar code has been successfully scanned. The callback is provided with\n     * an object of the `ScanningResult` shape, where the `type` refers to the bar code type that was scanned and the `data` is the information encoded in the bar code\n     * (in this case of QR codes, this is often a URL). See [`BarcodeType`](#barcodetype) for supported values.\n     * @param listener Invoked with the [ScanningResult](#scanningresult) when a bar code has been successfully scanned.\n     *\n     * @platform ios\n     * @platform android\n     */\n    static onModernBarcodeScanned(listener) {\n      return _ExpoCameraManager.default.addListener('onModernBarcodeScanned', listener);\n    }\n    /**\n     * Starts recording a video that will be saved to cache directory. Videos are rotated to match device's orientation.\n     * Flipping camera during a recording results in stopping it.\n     * @param options A map of `CameraRecordingOptions` type.\n     * @return Returns a Promise that resolves to an object containing video file `uri` property and a `codec` property on iOS.\n     * The Promise is returned if `stopRecording` was invoked, one of `maxDuration` and `maxFileSize` is reached or camera preview is stopped.\n     * @platform android\n     * @platform ios\n     */\n    async recordAsync(options) {\n      const recordingOptions = ensureRecordingOptions(options);\n      return this._cameraRef.current?.record(recordingOptions);\n    }\n    /**\n     * Pauses or resumes the video recording. Only has an effect if there is an active recording. On `iOS`, this method only supported on `iOS` 18.\n     *\n     * @example\n     * ```ts\n     * const { toggleRecordingAsyncAvailable } = getSupportedFeatures()\n     *\n     * return (\n     *  {toggleRecordingAsyncAvailable && (\n     *    <Button title=\"Toggle Recording\" onPress={toggleRecordingAsync} />\n     *  )}\n     * )\n     * ```\n     */\n    async toggleRecordingAsync() {\n      return this._cameraRef.current?.toggleRecording();\n    }\n    /**\n     * Stops recording if any is in progress.\n     * @platform android\n     * @platform ios\n     */\n    stopRecording() {\n      this._cameraRef.current?.stopRecording();\n    }\n    _onCameraReady = () => {\n      if (this.props.onCameraReady) {\n        this.props.onCameraReady();\n      }\n    };\n    _onAvailableLensesChanged = ({\n      nativeEvent\n    }) => {\n      if (this.props.onAvailableLensesChanged) {\n        this.props.onAvailableLensesChanged(nativeEvent);\n      }\n    };\n    _onMountError = ({\n      nativeEvent\n    }) => {\n      if (this.props.onMountError) {\n        this.props.onMountError(nativeEvent);\n      }\n    };\n    _onResponsiveOrientationChanged = ({\n      nativeEvent\n    }) => {\n      if (this.props.onResponsiveOrientationChanged) {\n        this.props.onResponsiveOrientationChanged(nativeEvent);\n      }\n    };\n    _onObjectDetected = callback => ({\n      nativeEvent\n    }) => {\n      const {\n        type\n      } = nativeEvent;\n      if (this._lastEvents[type] && this._lastEventsTimes[type] && JSON.stringify(nativeEvent) === this._lastEvents[type] && new Date().getTime() - this._lastEventsTimes[type].getTime() < EventThrottleMs) {\n        return;\n      }\n      if (callback) {\n        callback(nativeEvent);\n        this._lastEventsTimes[type] = new Date();\n        this._lastEvents[type] = JSON.stringify(nativeEvent);\n      }\n    };\n    _setReference = ref => {\n      if (ref) {\n        // TODO(Bacon): Unify these - perhaps with hooks?\n        if (_expoModulesCore.Platform.OS === 'web') {\n          this._cameraHandle = ref;\n        }\n      }\n    };\n    render() {\n      const nativeProps = (0, _props.ensureNativeProps)(this.props);\n      const onBarcodeScanned = this.props.onBarcodeScanned ? this._onObjectDetected(this.props.onBarcodeScanned) : undefined;\n      // @ts-expect-error\n      if (nativeProps.children && !loggedRenderingChildrenWarning) {\n        console.warn('The <CameraView> component does not support children. This may lead to inconsistent behaviour or crashes. If you want to render content on top of the Camera, consider using absolute positioning.');\n        loggedRenderingChildrenWarning = true;\n      }\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ExpoCamera.default, {\n        ...nativeProps,\n        ref: this._cameraRef,\n        onCameraReady: this._onCameraReady,\n        onMountError: this._onMountError,\n        onBarcodeScanned: onBarcodeScanned,\n        onAvailableLensesChanged: this._onAvailableLensesChanged,\n        onPictureSaved: _onPictureSaved,\n        onResponsiveOrientationChanged: this._onResponsiveOrientationChanged\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 17\n      }, this);\n    }\n  }\n  exports.default = CameraView;\n});", "lineCount": 294, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_expoModulesCore"], [7, 22, 1, 0], [7, 25, 1, 0, "require"], [7, 32, 1, 0], [7, 33, 1, 0, "_dependencyMap"], [7, 47, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_ExpoCamera"], [9, 17, 3, 0], [9, 20, 3, 0, "_interopRequireDefault"], [9, 42, 3, 0], [9, 43, 3, 0, "require"], [9, 50, 3, 0], [9, 51, 3, 0, "_dependencyMap"], [9, 65, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_ExpoCameraManager"], [10, 24, 4, 0], [10, 27, 4, 0, "_interopRequireDefault"], [10, 49, 4, 0], [10, 50, 4, 0, "require"], [10, 57, 4, 0], [10, 58, 4, 0, "_dependencyMap"], [10, 72, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_props"], [11, 12, 5, 0], [11, 15, 5, 0, "require"], [11, 22, 5, 0], [11, 23, 5, 0, "_dependencyMap"], [11, 37, 5, 0], [12, 2, 5, 68], [12, 6, 5, 68, "_jsxDevRuntime"], [12, 20, 5, 68], [12, 23, 5, 68, "require"], [12, 30, 5, 68], [12, 31, 5, 68, "_dependencyMap"], [12, 45, 5, 68], [13, 2, 5, 68], [13, 6, 5, 68, "_jsxFileName"], [13, 18, 5, 68], [14, 2, 6, 0], [14, 8, 6, 6, "EventThrottleMs"], [14, 23, 6, 21], [14, 26, 6, 24], [14, 29, 6, 27], [15, 2, 7, 0], [15, 8, 7, 6, "_PICTURE_SAVED_CALLBACKS"], [15, 32, 7, 30], [15, 35, 7, 33], [15, 36, 7, 34], [15, 37, 7, 35], [16, 2, 8, 0], [16, 6, 8, 4, "loggedRenderingChildrenWarning"], [16, 36, 8, 34], [16, 39, 8, 37], [16, 44, 8, 42], [17, 2, 9, 0], [17, 6, 9, 4, "_GLOBAL_PICTURE_ID"], [17, 24, 9, 22], [17, 27, 9, 25], [17, 28, 9, 26], [18, 2, 10, 0], [18, 11, 10, 9, "ensurePictureOptions"], [18, 31, 10, 29, "ensurePictureOptions"], [18, 32, 10, 30, "options"], [18, 39, 10, 37], [18, 41, 10, 39], [19, 4, 11, 4], [19, 8, 11, 8], [19, 9, 11, 9, "options"], [19, 16, 11, 16], [19, 20, 11, 20], [19, 27, 11, 27, "options"], [19, 34, 11, 34], [19, 39, 11, 39], [19, 47, 11, 47], [19, 49, 11, 49], [20, 6, 12, 8], [20, 13, 12, 15], [20, 14, 12, 16], [20, 15, 12, 17], [21, 4, 13, 4], [22, 4, 14, 4], [22, 8, 14, 8, "options"], [22, 15, 14, 15], [22, 16, 14, 16, "quality"], [22, 23, 14, 23], [22, 28, 14, 28, "undefined"], [22, 37, 14, 37], [22, 39, 14, 39], [23, 6, 15, 8, "options"], [23, 13, 15, 15], [23, 14, 15, 16, "quality"], [23, 21, 15, 23], [23, 24, 15, 26], [23, 25, 15, 27], [24, 4, 16, 4], [25, 4, 17, 4], [25, 8, 17, 8, "options"], [25, 15, 17, 15], [25, 16, 17, 16, "mirror"], [25, 22, 17, 22], [25, 24, 17, 24], [26, 6, 18, 8, "console"], [26, 13, 18, 15], [26, 14, 18, 16, "warn"], [26, 18, 18, 20], [26, 19, 18, 21], [26, 113, 18, 115], [26, 114, 18, 116], [27, 4, 19, 4], [28, 4, 20, 4], [28, 8, 20, 8, "options"], [28, 15, 20, 15], [28, 16, 20, 16, "onPictureSaved"], [28, 30, 20, 30], [28, 32, 20, 32], [29, 6, 21, 8], [29, 12, 21, 14, "id"], [29, 14, 21, 16], [29, 17, 21, 19, "_GLOBAL_PICTURE_ID"], [29, 35, 21, 37], [29, 37, 21, 39], [30, 6, 22, 8, "_PICTURE_SAVED_CALLBACKS"], [30, 30, 22, 32], [30, 31, 22, 33, "id"], [30, 33, 22, 35], [30, 34, 22, 36], [30, 37, 22, 39, "options"], [30, 44, 22, 46], [30, 45, 22, 47, "onPictureSaved"], [30, 59, 22, 61], [31, 6, 23, 8, "options"], [31, 13, 23, 15], [31, 14, 23, 16, "id"], [31, 16, 23, 18], [31, 19, 23, 21, "id"], [31, 21, 23, 23], [32, 6, 24, 8, "options"], [32, 13, 24, 15], [32, 14, 24, 16, "fastMode"], [32, 22, 24, 24], [32, 25, 24, 27], [32, 29, 24, 31], [33, 4, 25, 4], [34, 4, 26, 4], [34, 11, 26, 11, "options"], [34, 18, 26, 18], [35, 2, 27, 0], [36, 2, 28, 0], [36, 11, 28, 9, "ensureRecordingOptions"], [36, 33, 28, 31, "ensureRecordingOptions"], [36, 34, 28, 32, "options"], [36, 41, 28, 39], [36, 44, 28, 42], [36, 45, 28, 43], [36, 46, 28, 44], [36, 48, 28, 46], [37, 4, 29, 4], [37, 8, 29, 8], [37, 9, 29, 9, "options"], [37, 16, 29, 16], [37, 20, 29, 20], [37, 27, 29, 27, "options"], [37, 34, 29, 34], [37, 39, 29, 39], [37, 47, 29, 47], [37, 49, 29, 49], [38, 6, 30, 8], [38, 13, 30, 15], [38, 14, 30, 16], [38, 15, 30, 17], [39, 4, 31, 4], [40, 4, 32, 4], [40, 8, 32, 8, "options"], [40, 15, 32, 15], [40, 16, 32, 16, "mirror"], [40, 22, 32, 22], [40, 24, 32, 24], [41, 6, 33, 8, "console"], [41, 13, 33, 15], [41, 14, 33, 16, "warn"], [41, 18, 33, 20], [41, 19, 33, 21], [41, 113, 33, 115], [41, 114, 33, 116], [42, 4, 34, 4], [43, 4, 35, 4], [43, 11, 35, 11, "options"], [43, 18, 35, 18], [44, 2, 36, 0], [45, 2, 37, 0], [45, 11, 37, 9, "_onPictureSaved"], [45, 26, 37, 24, "_onPictureSaved"], [45, 27, 37, 25], [46, 4, 37, 27, "nativeEvent"], [47, 2, 37, 40], [47, 3, 37, 41], [47, 5, 37, 43], [48, 4, 38, 4], [48, 10, 38, 10], [49, 6, 38, 12, "id"], [49, 8, 38, 14], [50, 6, 38, 16, "data"], [51, 4, 38, 21], [51, 5, 38, 22], [51, 8, 38, 25, "nativeEvent"], [51, 19, 38, 36], [52, 4, 39, 4], [52, 10, 39, 10, "callback"], [52, 18, 39, 18], [52, 21, 39, 21, "_PICTURE_SAVED_CALLBACKS"], [52, 45, 39, 45], [52, 46, 39, 46, "id"], [52, 48, 39, 48], [52, 49, 39, 49], [53, 4, 40, 4], [53, 8, 40, 8, "callback"], [53, 16, 40, 16], [53, 18, 40, 18], [54, 6, 41, 8, "callback"], [54, 14, 41, 16], [54, 15, 41, 17, "data"], [54, 19, 41, 21], [54, 20, 41, 22], [55, 6, 42, 8], [55, 13, 42, 15, "_PICTURE_SAVED_CALLBACKS"], [55, 37, 42, 39], [55, 38, 42, 40, "id"], [55, 40, 42, 42], [55, 41, 42, 43], [56, 4, 43, 4], [57, 2, 44, 0], [58, 2, 45, 15], [58, 8, 45, 21, "CameraView"], [58, 18, 45, 31], [58, 27, 45, 40, "Component"], [58, 43, 45, 49], [58, 44, 45, 50], [59, 4, 46, 4], [60, 0, 47, 0], [61, 0, 48, 0], [62, 4, 49, 4], [62, 11, 49, 11, "isModernBarcodeScannerAvailable"], [62, 42, 49, 42], [62, 45, 49, 45, "CameraManager"], [62, 71, 49, 58], [62, 72, 49, 59, "isModernBarcodeScannerAvailable"], [62, 103, 49, 90], [63, 4, 50, 4], [64, 0, 51, 0], [65, 0, 52, 0], [66, 0, 53, 0], [67, 0, 54, 0], [68, 0, 55, 0], [69, 4, 56, 4], [69, 17, 56, 17, "isAvailableAsync"], [69, 33, 56, 33, "isAvailableAsync"], [69, 34, 56, 33], [69, 36, 56, 36], [70, 6, 57, 8], [70, 10, 57, 12], [70, 11, 57, 13, "CameraManager"], [70, 37, 57, 26], [70, 38, 57, 27, "isAvailableAsync"], [70, 54, 57, 43], [70, 56, 57, 45], [71, 8, 58, 12], [71, 14, 58, 18], [71, 18, 58, 22, "UnavailabilityError"], [71, 54, 58, 41], [71, 55, 58, 42], [71, 68, 58, 55], [71, 70, 58, 57], [71, 88, 58, 75], [71, 89, 58, 76], [72, 6, 59, 8], [73, 6, 60, 8], [73, 13, 60, 15, "CameraManager"], [73, 39, 60, 28], [73, 40, 60, 29, "isAvailableAsync"], [73, 56, 60, 45], [73, 57, 60, 46], [73, 58, 60, 47], [74, 4, 61, 4], [75, 4, 62, 4], [76, 4, 63, 4], [77, 0, 64, 0], [78, 0, 65, 0], [79, 0, 66, 0], [80, 0, 67, 0], [81, 4, 68, 4], [81, 17, 68, 17, "getAvailableVideoCodecsAsync"], [81, 45, 68, 45, "getAvailableVideoCodecsAsync"], [81, 46, 68, 45], [81, 48, 68, 48], [82, 6, 69, 8], [82, 10, 69, 12], [82, 11, 69, 13, "CameraManager"], [82, 37, 69, 26], [82, 38, 69, 27, "getAvailableVideoCodecsAsync"], [82, 66, 69, 55], [82, 68, 69, 57], [83, 8, 70, 12], [83, 14, 70, 18], [83, 18, 70, 22, "UnavailabilityError"], [83, 54, 70, 41], [83, 55, 70, 42], [83, 63, 70, 50], [83, 65, 70, 52], [83, 95, 70, 82], [83, 96, 70, 83], [84, 6, 71, 8], [85, 6, 72, 8], [85, 13, 72, 15, "CameraManager"], [85, 39, 72, 28], [85, 40, 72, 29, "getAvailableVideoCodecsAsync"], [85, 68, 72, 57], [85, 69, 72, 58], [85, 70, 72, 59], [86, 4, 73, 4], [87, 4, 74, 4], [88, 0, 75, 0], [89, 0, 76, 0], [90, 0, 77, 0], [91, 0, 78, 0], [92, 4, 79, 4], [92, 10, 79, 10, "getAvailablePictureSizesAsync"], [92, 39, 79, 39, "getAvailablePictureSizesAsync"], [92, 40, 79, 39], [92, 42, 79, 42], [93, 6, 80, 8], [93, 13, 80, 15], [93, 14, 80, 16], [93, 20, 80, 22], [93, 24, 80, 26], [93, 25, 80, 27, "_cameraRef"], [93, 35, 80, 37], [93, 36, 80, 38, "current"], [93, 43, 80, 45], [93, 45, 80, 47, "getAvailablePictureSizes"], [93, 69, 80, 71], [93, 70, 80, 72], [93, 71, 80, 73], [93, 76, 80, 78], [93, 78, 80, 80], [94, 4, 81, 4], [95, 4, 82, 4], [96, 0, 83, 0], [97, 0, 84, 0], [98, 0, 85, 0], [99, 0, 86, 0], [100, 0, 87, 0], [101, 4, 88, 4], [101, 10, 88, 10, "getAvailableLensesAsync"], [101, 33, 88, 33, "getAvailableLensesAsync"], [101, 34, 88, 33], [101, 36, 88, 36], [102, 6, 89, 8], [102, 13, 89, 15], [102, 14, 89, 16], [102, 20, 89, 22], [102, 24, 89, 26], [102, 25, 89, 27, "_cameraRef"], [102, 35, 89, 37], [102, 36, 89, 38, "current"], [102, 43, 89, 45], [102, 45, 89, 47, "getAvailableLenses"], [102, 63, 89, 65], [102, 64, 89, 66], [102, 65, 89, 67], [102, 70, 89, 72], [102, 72, 89, 74], [103, 4, 90, 4], [104, 4, 91, 4], [105, 0, 92, 0], [106, 0, 93, 0], [107, 4, 94, 4, "getSupportedFeatures"], [107, 24, 94, 24, "getSupportedFeatures"], [107, 25, 94, 24], [107, 27, 94, 27], [108, 6, 95, 8], [108, 13, 95, 15], [109, 8, 96, 12, "isModernBarcodeScannerAvailable"], [109, 39, 96, 43], [109, 41, 96, 45, "CameraManager"], [109, 67, 96, 58], [109, 68, 96, 59, "isModernBarcodeScannerAvailable"], [109, 99, 96, 90], [110, 8, 97, 12, "toggleRecordingAsyncAvailable"], [110, 37, 97, 41], [110, 39, 97, 43, "CameraManager"], [110, 65, 97, 56], [110, 66, 97, 57, "toggleRecordingAsyncAvailable"], [111, 6, 98, 8], [111, 7, 98, 9], [112, 4, 99, 4], [113, 4, 100, 4], [114, 0, 101, 0], [115, 0, 102, 0], [116, 4, 103, 4], [116, 10, 103, 10, "resumePreview"], [116, 23, 103, 23, "resumePreview"], [116, 24, 103, 23], [116, 26, 103, 26], [117, 6, 104, 8], [117, 13, 104, 15], [117, 17, 104, 19], [117, 18, 104, 20, "_cameraRef"], [117, 28, 104, 30], [117, 29, 104, 31, "current"], [117, 36, 104, 38], [117, 38, 104, 40, "resumePreview"], [117, 51, 104, 53], [117, 52, 104, 54], [117, 53, 104, 55], [118, 4, 105, 4], [119, 4, 106, 4], [120, 0, 107, 0], [121, 0, 108, 0], [122, 4, 109, 4], [122, 10, 109, 10, "pausePreview"], [122, 22, 109, 22, "pausePreview"], [122, 23, 109, 22], [122, 25, 109, 25], [123, 6, 110, 8], [123, 13, 110, 15], [123, 17, 110, 19], [123, 18, 110, 20, "_cameraRef"], [123, 28, 110, 30], [123, 29, 110, 31, "current"], [123, 36, 110, 38], [123, 38, 110, 40, "pausePreview"], [123, 50, 110, 52], [123, 51, 110, 53], [123, 52, 110, 54], [124, 4, 111, 4], [125, 4, 112, 4], [126, 4, 113, 4], [126, 11, 113, 11, "ConversionTables"], [126, 27, 113, 27], [126, 30, 113, 30, "ConversionTables"], [126, 53, 113, 46], [127, 4, 114, 4], [127, 11, 114, 11, "defaultProps"], [127, 23, 114, 23], [127, 26, 114, 26], [128, 6, 115, 8, "zoom"], [128, 10, 115, 12], [128, 12, 115, 14], [128, 13, 115, 15], [129, 6, 116, 8, "facing"], [129, 12, 116, 14], [129, 14, 116, 16], [129, 20, 116, 22], [130, 6, 117, 8, "enableTorch"], [130, 17, 117, 19], [130, 19, 117, 21], [130, 24, 117, 26], [131, 6, 118, 8, "mode"], [131, 10, 118, 12], [131, 12, 118, 14], [131, 21, 118, 23], [132, 6, 119, 8, "flash"], [132, 11, 119, 13], [132, 13, 119, 15], [133, 4, 120, 4], [133, 5, 120, 5], [134, 4, 122, 4, "_cameraRef"], [134, 14, 122, 14], [134, 30, 122, 17], [134, 34, 122, 17, "createRef"], [134, 50, 122, 26], [134, 52, 122, 27], [134, 53, 122, 28], [135, 4, 123, 4, "_lastEvents"], [135, 15, 123, 15], [135, 18, 123, 18], [135, 19, 123, 19], [135, 20, 123, 20], [136, 4, 124, 4, "_lastEventsTimes"], [136, 20, 124, 20], [136, 23, 124, 23], [136, 24, 124, 24], [136, 25, 124, 25], [137, 4, 125, 4], [137, 10, 125, 10, "takePictureAsync"], [137, 26, 125, 26, "takePictureAsync"], [137, 27, 125, 27, "options"], [137, 34, 125, 34], [137, 36, 125, 36], [138, 6, 126, 8], [138, 12, 126, 14, "pictureOptions"], [138, 26, 126, 28], [138, 29, 126, 31, "ensurePictureOptions"], [138, 49, 126, 51], [138, 50, 126, 52, "options"], [138, 57, 126, 59], [138, 58, 126, 60], [139, 6, 127, 8], [139, 10, 127, 12, "Platform"], [139, 35, 127, 20], [139, 36, 127, 21, "OS"], [139, 38, 127, 23], [139, 43, 127, 28], [139, 48, 127, 33], [139, 52, 127, 37, "options"], [139, 59, 127, 44], [139, 61, 127, 46, "pictureRef"], [139, 71, 127, 56], [139, 73, 127, 58], [140, 8, 128, 12], [140, 15, 128, 19], [140, 19, 128, 23], [140, 20, 128, 24, "_cameraRef"], [140, 30, 128, 34], [140, 31, 128, 35, "current"], [140, 38, 128, 42], [140, 40, 128, 44, "takePictureRef"], [140, 54, 128, 58], [140, 57, 128, 61, "options"], [140, 64, 128, 68], [140, 65, 128, 69], [141, 6, 129, 8], [142, 6, 130, 8], [142, 13, 130, 15], [142, 17, 130, 19], [142, 18, 130, 20, "_cameraRef"], [142, 28, 130, 30], [142, 29, 130, 31, "current"], [142, 36, 130, 38], [142, 38, 130, 40, "takePicture"], [142, 49, 130, 51], [142, 50, 130, 52, "pictureOptions"], [142, 64, 130, 66], [142, 65, 130, 67], [143, 4, 131, 4], [144, 4, 132, 4], [145, 0, 133, 0], [146, 0, 134, 0], [147, 0, 135, 0], [148, 0, 136, 0], [149, 0, 137, 0], [150, 4, 138, 4], [150, 17, 138, 17, "launchScanner"], [150, 30, 138, 30, "launchScanner"], [150, 31, 138, 31, "options"], [150, 38, 138, 38], [150, 40, 138, 40], [151, 6, 139, 8], [151, 10, 139, 12], [151, 11, 139, 13, "options"], [151, 18, 139, 20], [151, 20, 139, 22], [152, 8, 140, 12, "options"], [152, 15, 140, 19], [152, 18, 140, 22], [153, 10, 140, 24, "barcodeTypes"], [153, 22, 140, 36], [153, 24, 140, 38], [154, 8, 140, 41], [154, 9, 140, 42], [155, 6, 141, 8], [156, 6, 142, 8], [156, 10, 142, 12, "Platform"], [156, 35, 142, 20], [156, 36, 142, 21, "OS"], [156, 38, 142, 23], [156, 43, 142, 28], [156, 48, 142, 33], [156, 52, 142, 37, "CameraView"], [156, 62, 142, 47], [156, 63, 142, 48, "isModernBarcodeScannerAvailable"], [156, 94, 142, 79], [156, 96, 142, 81], [157, 8, 143, 12], [157, 14, 143, 18, "CameraManager"], [157, 40, 143, 31], [157, 41, 143, 32, "launchScanner"], [157, 54, 143, 45], [157, 55, 143, 46, "options"], [157, 62, 143, 53], [157, 63, 143, 54], [158, 6, 144, 8], [159, 4, 145, 4], [160, 4, 146, 4], [161, 0, 147, 0], [162, 0, 148, 0], [163, 0, 149, 0], [164, 0, 150, 0], [165, 4, 151, 4], [165, 17, 151, 17, "dismissScanner"], [165, 31, 151, 31, "dismissScanner"], [165, 32, 151, 31], [165, 34, 151, 34], [166, 6, 152, 8], [166, 10, 152, 12, "Platform"], [166, 35, 152, 20], [166, 36, 152, 21, "OS"], [166, 38, 152, 23], [166, 43, 152, 28], [166, 48, 152, 33], [166, 52, 152, 37, "CameraView"], [166, 62, 152, 47], [166, 63, 152, 48, "isModernBarcodeScannerAvailable"], [166, 94, 152, 79], [166, 96, 152, 81], [167, 8, 153, 12], [167, 14, 153, 18, "CameraManager"], [167, 40, 153, 31], [167, 41, 153, 32, "dismissScanner"], [167, 55, 153, 46], [167, 56, 153, 47], [167, 57, 153, 48], [168, 6, 154, 8], [169, 4, 155, 4], [170, 4, 156, 4], [171, 0, 157, 0], [172, 0, 158, 0], [173, 0, 159, 0], [174, 0, 160, 0], [175, 0, 161, 0], [176, 0, 162, 0], [177, 0, 163, 0], [178, 0, 164, 0], [179, 4, 165, 4], [179, 11, 165, 11, "onModernBarcodeScanned"], [179, 33, 165, 33, "onModernBarcodeScanned"], [179, 34, 165, 34, "listener"], [179, 42, 165, 42], [179, 44, 165, 44], [180, 6, 166, 8], [180, 13, 166, 15, "CameraManager"], [180, 39, 166, 28], [180, 40, 166, 29, "addListener"], [180, 51, 166, 40], [180, 52, 166, 41], [180, 76, 166, 65], [180, 78, 166, 67, "listener"], [180, 86, 166, 75], [180, 87, 166, 76], [181, 4, 167, 4], [182, 4, 168, 4], [183, 0, 169, 0], [184, 0, 170, 0], [185, 0, 171, 0], [186, 0, 172, 0], [187, 0, 173, 0], [188, 0, 174, 0], [189, 0, 175, 0], [190, 0, 176, 0], [191, 4, 177, 4], [191, 10, 177, 10, "recordAsync"], [191, 21, 177, 21, "recordAsync"], [191, 22, 177, 22, "options"], [191, 29, 177, 29], [191, 31, 177, 31], [192, 6, 178, 8], [192, 12, 178, 14, "recordingOptions"], [192, 28, 178, 30], [192, 31, 178, 33, "ensureRecordingOptions"], [192, 53, 178, 55], [192, 54, 178, 56, "options"], [192, 61, 178, 63], [192, 62, 178, 64], [193, 6, 179, 8], [193, 13, 179, 15], [193, 17, 179, 19], [193, 18, 179, 20, "_cameraRef"], [193, 28, 179, 30], [193, 29, 179, 31, "current"], [193, 36, 179, 38], [193, 38, 179, 40, "record"], [193, 44, 179, 46], [193, 45, 179, 47, "recordingOptions"], [193, 61, 179, 63], [193, 62, 179, 64], [194, 4, 180, 4], [195, 4, 181, 4], [196, 0, 182, 0], [197, 0, 183, 0], [198, 0, 184, 0], [199, 0, 185, 0], [200, 0, 186, 0], [201, 0, 187, 0], [202, 0, 188, 0], [203, 0, 189, 0], [204, 0, 190, 0], [205, 0, 191, 0], [206, 0, 192, 0], [207, 0, 193, 0], [208, 0, 194, 0], [209, 4, 195, 4], [209, 10, 195, 10, "toggleRecordingAsync"], [209, 30, 195, 30, "toggleRecordingAsync"], [209, 31, 195, 30], [209, 33, 195, 33], [210, 6, 196, 8], [210, 13, 196, 15], [210, 17, 196, 19], [210, 18, 196, 20, "_cameraRef"], [210, 28, 196, 30], [210, 29, 196, 31, "current"], [210, 36, 196, 38], [210, 38, 196, 40, "toggleRecording"], [210, 53, 196, 55], [210, 54, 196, 56], [210, 55, 196, 57], [211, 4, 197, 4], [212, 4, 198, 4], [213, 0, 199, 0], [214, 0, 200, 0], [215, 0, 201, 0], [216, 0, 202, 0], [217, 4, 203, 4, "stopRecording"], [217, 17, 203, 17, "stopRecording"], [217, 18, 203, 17], [217, 20, 203, 20], [218, 6, 204, 8], [218, 10, 204, 12], [218, 11, 204, 13, "_cameraRef"], [218, 21, 204, 23], [218, 22, 204, 24, "current"], [218, 29, 204, 31], [218, 31, 204, 33, "stopRecording"], [218, 44, 204, 46], [218, 45, 204, 47], [218, 46, 204, 48], [219, 4, 205, 4], [220, 4, 206, 4, "_onCameraReady"], [220, 18, 206, 18], [220, 21, 206, 21, "_onCameraReady"], [220, 22, 206, 21], [220, 27, 206, 27], [221, 6, 207, 8], [221, 10, 207, 12], [221, 14, 207, 16], [221, 15, 207, 17, "props"], [221, 20, 207, 22], [221, 21, 207, 23, "onCameraReady"], [221, 34, 207, 36], [221, 36, 207, 38], [222, 8, 208, 12], [222, 12, 208, 16], [222, 13, 208, 17, "props"], [222, 18, 208, 22], [222, 19, 208, 23, "onCameraReady"], [222, 32, 208, 36], [222, 33, 208, 37], [222, 34, 208, 38], [223, 6, 209, 8], [224, 4, 210, 4], [224, 5, 210, 5], [225, 4, 211, 4, "_onAvailableLensesChanged"], [225, 29, 211, 29], [225, 32, 211, 32, "_onAvailableLensesChanged"], [225, 33, 211, 33], [226, 6, 211, 35, "nativeEvent"], [227, 4, 211, 47], [227, 5, 211, 48], [227, 10, 211, 53], [228, 6, 212, 8], [228, 10, 212, 12], [228, 14, 212, 16], [228, 15, 212, 17, "props"], [228, 20, 212, 22], [228, 21, 212, 23, "onAvailableLensesChanged"], [228, 45, 212, 47], [228, 47, 212, 49], [229, 8, 213, 12], [229, 12, 213, 16], [229, 13, 213, 17, "props"], [229, 18, 213, 22], [229, 19, 213, 23, "onAvailableLensesChanged"], [229, 43, 213, 47], [229, 44, 213, 48, "nativeEvent"], [229, 55, 213, 59], [229, 56, 213, 60], [230, 6, 214, 8], [231, 4, 215, 4], [231, 5, 215, 5], [232, 4, 216, 4, "_onMountError"], [232, 17, 216, 17], [232, 20, 216, 20, "_onMountError"], [232, 21, 216, 21], [233, 6, 216, 23, "nativeEvent"], [234, 4, 216, 35], [234, 5, 216, 36], [234, 10, 216, 41], [235, 6, 217, 8], [235, 10, 217, 12], [235, 14, 217, 16], [235, 15, 217, 17, "props"], [235, 20, 217, 22], [235, 21, 217, 23, "onMountError"], [235, 33, 217, 35], [235, 35, 217, 37], [236, 8, 218, 12], [236, 12, 218, 16], [236, 13, 218, 17, "props"], [236, 18, 218, 22], [236, 19, 218, 23, "onMountError"], [236, 31, 218, 35], [236, 32, 218, 36, "nativeEvent"], [236, 43, 218, 47], [236, 44, 218, 48], [237, 6, 219, 8], [238, 4, 220, 4], [238, 5, 220, 5], [239, 4, 221, 4, "_onResponsiveOrientationChanged"], [239, 35, 221, 35], [239, 38, 221, 38, "_onResponsiveOrientationChanged"], [239, 39, 221, 39], [240, 6, 221, 41, "nativeEvent"], [241, 4, 221, 54], [241, 5, 221, 55], [241, 10, 221, 60], [242, 6, 222, 8], [242, 10, 222, 12], [242, 14, 222, 16], [242, 15, 222, 17, "props"], [242, 20, 222, 22], [242, 21, 222, 23, "onResponsiveOrientationChanged"], [242, 51, 222, 53], [242, 53, 222, 55], [243, 8, 223, 12], [243, 12, 223, 16], [243, 13, 223, 17, "props"], [243, 18, 223, 22], [243, 19, 223, 23, "onResponsiveOrientationChanged"], [243, 49, 223, 53], [243, 50, 223, 54, "nativeEvent"], [243, 61, 223, 65], [243, 62, 223, 66], [244, 6, 224, 8], [245, 4, 225, 4], [245, 5, 225, 5], [246, 4, 226, 4, "_onObjectDetected"], [246, 21, 226, 21], [246, 24, 226, 25, "callback"], [246, 32, 226, 33], [246, 36, 226, 38], [246, 37, 226, 39], [247, 6, 226, 41, "nativeEvent"], [248, 4, 226, 53], [248, 5, 226, 54], [248, 10, 226, 59], [249, 6, 227, 8], [249, 12, 227, 14], [250, 8, 227, 16, "type"], [251, 6, 227, 21], [251, 7, 227, 22], [251, 10, 227, 25, "nativeEvent"], [251, 21, 227, 36], [252, 6, 228, 8], [252, 10, 228, 12], [252, 14, 228, 16], [252, 15, 228, 17, "_lastEvents"], [252, 26, 228, 28], [252, 27, 228, 29, "type"], [252, 31, 228, 33], [252, 32, 228, 34], [252, 36, 229, 12], [252, 40, 229, 16], [252, 41, 229, 17, "_lastEventsTimes"], [252, 57, 229, 33], [252, 58, 229, 34, "type"], [252, 62, 229, 38], [252, 63, 229, 39], [252, 67, 230, 12, "JSON"], [252, 71, 230, 16], [252, 72, 230, 17, "stringify"], [252, 81, 230, 26], [252, 82, 230, 27, "nativeEvent"], [252, 93, 230, 38], [252, 94, 230, 39], [252, 99, 230, 44], [252, 103, 230, 48], [252, 104, 230, 49, "_lastEvents"], [252, 115, 230, 60], [252, 116, 230, 61, "type"], [252, 120, 230, 65], [252, 121, 230, 66], [252, 125, 231, 12], [252, 129, 231, 16, "Date"], [252, 133, 231, 20], [252, 134, 231, 21], [252, 135, 231, 22], [252, 136, 231, 23, "getTime"], [252, 143, 231, 30], [252, 144, 231, 31], [252, 145, 231, 32], [252, 148, 231, 35], [252, 152, 231, 39], [252, 153, 231, 40, "_lastEventsTimes"], [252, 169, 231, 56], [252, 170, 231, 57, "type"], [252, 174, 231, 61], [252, 175, 231, 62], [252, 176, 231, 63, "getTime"], [252, 183, 231, 70], [252, 184, 231, 71], [252, 185, 231, 72], [252, 188, 231, 75, "EventThrottleMs"], [252, 203, 231, 90], [252, 205, 231, 92], [253, 8, 232, 12], [254, 6, 233, 8], [255, 6, 234, 8], [255, 10, 234, 12, "callback"], [255, 18, 234, 20], [255, 20, 234, 22], [256, 8, 235, 12, "callback"], [256, 16, 235, 20], [256, 17, 235, 21, "nativeEvent"], [256, 28, 235, 32], [256, 29, 235, 33], [257, 8, 236, 12], [257, 12, 236, 16], [257, 13, 236, 17, "_lastEventsTimes"], [257, 29, 236, 33], [257, 30, 236, 34, "type"], [257, 34, 236, 38], [257, 35, 236, 39], [257, 38, 236, 42], [257, 42, 236, 46, "Date"], [257, 46, 236, 50], [257, 47, 236, 51], [257, 48, 236, 52], [258, 8, 237, 12], [258, 12, 237, 16], [258, 13, 237, 17, "_lastEvents"], [258, 24, 237, 28], [258, 25, 237, 29, "type"], [258, 29, 237, 33], [258, 30, 237, 34], [258, 33, 237, 37, "JSON"], [258, 37, 237, 41], [258, 38, 237, 42, "stringify"], [258, 47, 237, 51], [258, 48, 237, 52, "nativeEvent"], [258, 59, 237, 63], [258, 60, 237, 64], [259, 6, 238, 8], [260, 4, 239, 4], [260, 5, 239, 5], [261, 4, 240, 4, "_setReference"], [261, 17, 240, 17], [261, 20, 240, 21, "ref"], [261, 23, 240, 24], [261, 27, 240, 29], [262, 6, 241, 8], [262, 10, 241, 12, "ref"], [262, 13, 241, 15], [262, 15, 241, 17], [263, 8, 242, 12], [264, 8, 243, 12], [264, 12, 243, 16, "Platform"], [264, 37, 243, 24], [264, 38, 243, 25, "OS"], [264, 40, 243, 27], [264, 45, 243, 32], [264, 50, 243, 37], [264, 52, 243, 39], [265, 10, 244, 16], [265, 14, 244, 20], [265, 15, 244, 21, "_camera<PERSON><PERSON>le"], [265, 28, 244, 34], [265, 31, 244, 37, "ref"], [265, 34, 244, 40], [266, 8, 245, 12], [267, 6, 246, 8], [268, 4, 247, 4], [268, 5, 247, 5], [269, 4, 248, 4, "render"], [269, 10, 248, 10, "render"], [269, 11, 248, 10], [269, 13, 248, 13], [270, 6, 249, 8], [270, 12, 249, 14, "nativeProps"], [270, 23, 249, 25], [270, 26, 249, 28], [270, 30, 249, 28, "ensureNativeProps"], [270, 54, 249, 45], [270, 56, 249, 46], [270, 60, 249, 50], [270, 61, 249, 51, "props"], [270, 66, 249, 56], [270, 67, 249, 57], [271, 6, 250, 8], [271, 12, 250, 14, "onBarcodeScanned"], [271, 28, 250, 30], [271, 31, 250, 33], [271, 35, 250, 37], [271, 36, 250, 38, "props"], [271, 41, 250, 43], [271, 42, 250, 44, "onBarcodeScanned"], [271, 58, 250, 60], [271, 61, 251, 14], [271, 65, 251, 18], [271, 66, 251, 19, "_onObjectDetected"], [271, 83, 251, 36], [271, 84, 251, 37], [271, 88, 251, 41], [271, 89, 251, 42, "props"], [271, 94, 251, 47], [271, 95, 251, 48, "onBarcodeScanned"], [271, 111, 251, 64], [271, 112, 251, 65], [271, 115, 252, 14, "undefined"], [271, 124, 252, 23], [272, 6, 253, 8], [273, 6, 254, 8], [273, 10, 254, 12, "nativeProps"], [273, 21, 254, 23], [273, 22, 254, 24, "children"], [273, 30, 254, 32], [273, 34, 254, 36], [273, 35, 254, 37, "loggedRenderingChildrenWarning"], [273, 65, 254, 67], [273, 67, 254, 69], [274, 8, 255, 12, "console"], [274, 15, 255, 19], [274, 16, 255, 20, "warn"], [274, 20, 255, 24], [274, 21, 255, 25], [274, 217, 255, 221], [274, 218, 255, 222], [275, 8, 256, 12, "loggedRenderingChildrenWarning"], [275, 38, 256, 42], [275, 41, 256, 45], [275, 45, 256, 49], [276, 6, 257, 8], [277, 6, 258, 8], [277, 26, 258, 16], [277, 30, 258, 16, "_jsxDevRuntime"], [277, 44, 258, 16], [277, 45, 258, 16, "jsxDEV"], [277, 51, 258, 16], [277, 53, 258, 17, "_ExpoCamera"], [277, 64, 258, 17], [277, 65, 258, 17, "default"], [277, 72, 258, 27], [278, 8, 258, 27], [278, 11, 258, 32, "nativeProps"], [278, 22, 258, 43], [279, 8, 258, 45, "ref"], [279, 11, 258, 48], [279, 13, 258, 50], [279, 17, 258, 54], [279, 18, 258, 55, "_cameraRef"], [279, 28, 258, 66], [280, 8, 258, 67, "onCameraReady"], [280, 21, 258, 80], [280, 23, 258, 82], [280, 27, 258, 86], [280, 28, 258, 87, "_onCameraReady"], [280, 42, 258, 102], [281, 8, 258, 103, "onMountError"], [281, 20, 258, 115], [281, 22, 258, 117], [281, 26, 258, 121], [281, 27, 258, 122, "_onMountError"], [281, 40, 258, 136], [282, 8, 258, 137, "onBarcodeScanned"], [282, 24, 258, 153], [282, 26, 258, 155, "onBarcodeScanned"], [282, 42, 258, 172], [283, 8, 258, 173, "onAvailableLensesChanged"], [283, 32, 258, 197], [283, 34, 258, 199], [283, 38, 258, 203], [283, 39, 258, 204, "_onAvailableLensesChanged"], [283, 64, 258, 230], [284, 8, 258, 231, "onPictureSaved"], [284, 22, 258, 245], [284, 24, 258, 247, "_onPictureSaved"], [284, 39, 258, 263], [285, 8, 258, 264, "onResponsiveOrientationChanged"], [285, 38, 258, 294], [285, 40, 258, 296], [285, 44, 258, 300], [285, 45, 258, 301, "_onResponsiveOrientationChanged"], [286, 6, 258, 333], [287, 8, 258, 333, "fileName"], [287, 16, 258, 333], [287, 18, 258, 333, "_jsxFileName"], [287, 30, 258, 333], [288, 8, 258, 333, "lineNumber"], [288, 18, 258, 333], [289, 8, 258, 333, "columnNumber"], [289, 20, 258, 333], [290, 6, 258, 333], [290, 13, 258, 334], [290, 14, 258, 335], [291, 4, 259, 4], [292, 2, 260, 0], [293, 2, 260, 1, "exports"], [293, 9, 260, 1], [293, 10, 260, 1, "default"], [293, 17, 260, 1], [293, 20, 260, 1, "CameraView"], [293, 30, 260, 1], [294, 0, 260, 1], [294, 3]], "functionMap": {"names": ["<global>", "ensurePictureOptions", "ensureRecordingOptions", "_onPictureSaved", "CameraView", "isAvailableAsync", "getAvailableVideoCodecsAsync", "getAvailablePictureSizesAsync", "getAvailableLensesAsync", "getSupportedFeatures", "resumePreview", "pausePreview", "takePictureAsync", "launchScanner", "dismissScanner", "onModernBarcodeScanned", "recordAsync", "toggleRecordingAsync", "stopRecording", "_onCameraReady", "_onAvailableLensesChanged", "_onMountError", "_onResponsiveOrientationChanged", "_onObjectDetected", "<anonymous>", "_setReference", "render"], "mappings": "AAA;ACS;CDiB;AEC;CFQ;AGC;CHO;eIC;ICW;KDK;IEO;KFK;IGM;KHE;IIO;KJE;IKI;KLK;IMI;KNE;IOI;KPE;IQc;KRM;ISO;KTO;IUM;KVI;IWU;KXE;IYU;KZG;Iae;KbE;IcM;KdE;qBeC;KfI;gCgBC;KhBI;oBiBC;KjBI;sCkBC;KlBI;wBmBC,cC;KpBa;oBqBC;KrBO;IsBC;KtBW;CJC"}}, "type": "js/module"}]}