{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {\n          original: {\n            x,\n            y,\n            width,\n            height\n          },\n          canvas: {\n            width: canvasWidth,\n            height: canvasHeight\n          },\n          clamped: {\n            x: clampedX,\n            y: clampedY,\n            width: clampedWidth,\n            height: clampedHeight\n          }\n        });\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: About to call processImageWithFaceBlur with:', photo.uri);\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: Function exists?', typeof processImageWithFaceBlur);\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      console.log('[EchoCameraWeb] 🚀 ENTRY: Starting face blur processing system...');\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {\n              bbox,\n              imageSize: {\n                width: img.width,\n                height: img.height\n              }\n            });\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {\n              faceX,\n              faceY,\n              faceWidth,\n              faceHeight,\n              isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0\n            });\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] 🚨 CRITICAL ERROR in processImageWithFaceBlur:', error);\n        console.error('[EchoCameraWeb] 🚨 Error stack:', error.stack);\n        console.error('[EchoCameraWeb] 🚨 Error details:', {\n          name: error.name,\n          message: error.message,\n          photoUri\n        });\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 924,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 917,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 993,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1038,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1042,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 940,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1091,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1097,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1105,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1088,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1087,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1082,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1132,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1128,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1138,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1124,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1123,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1118,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1753, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 73, 383, 71], [357, 75, 383, 73], [358, 10, 384, 8, "original"], [358, 18, 384, 16], [358, 20, 384, 18], [359, 12, 384, 20, "x"], [359, 13, 384, 21], [360, 12, 384, 23, "y"], [360, 13, 384, 24], [361, 12, 384, 26, "width"], [361, 17, 384, 31], [362, 12, 384, 33, "height"], [363, 10, 384, 40], [363, 11, 384, 41], [364, 10, 385, 8, "canvas"], [364, 16, 385, 14], [364, 18, 385, 16], [365, 12, 385, 18, "width"], [365, 17, 385, 23], [365, 19, 385, 25, "canvasWidth"], [365, 30, 385, 36], [366, 12, 385, 38, "height"], [366, 18, 385, 44], [366, 20, 385, 46, "canvasHeight"], [367, 10, 385, 59], [367, 11, 385, 60], [368, 10, 386, 8, "clamped"], [368, 17, 386, 15], [368, 19, 386, 17], [369, 12, 386, 19, "x"], [369, 13, 386, 20], [369, 15, 386, 22, "clampedX"], [369, 23, 386, 30], [370, 12, 386, 32, "y"], [370, 13, 386, 33], [370, 15, 386, 35, "clampedY"], [370, 23, 386, 43], [371, 12, 386, 45, "width"], [371, 17, 386, 50], [371, 19, 386, 52, "<PERSON><PERSON><PERSON><PERSON>"], [371, 31, 386, 64], [372, 12, 386, 66, "height"], [372, 18, 386, 72], [372, 20, 386, 74, "clampedHeight"], [373, 10, 386, 88], [374, 8, 387, 6], [374, 9, 387, 7], [374, 10, 387, 8], [375, 8, 388, 6], [376, 6, 389, 4], [378, 6, 391, 4], [379, 6, 392, 4], [379, 12, 392, 10, "imageData"], [379, 21, 392, 19], [379, 24, 392, 22, "ctx"], [379, 27, 392, 25], [379, 28, 392, 26, "getImageData"], [379, 40, 392, 38], [379, 41, 392, 39, "clampedX"], [379, 49, 392, 47], [379, 51, 392, 49, "clampedY"], [379, 59, 392, 57], [379, 61, 392, 59, "<PERSON><PERSON><PERSON><PERSON>"], [379, 73, 392, 71], [379, 75, 392, 73, "clampedHeight"], [379, 88, 392, 86], [379, 89, 392, 87], [380, 6, 393, 4], [380, 12, 393, 10, "data"], [380, 16, 393, 14], [380, 19, 393, 17, "imageData"], [380, 28, 393, 26], [380, 29, 393, 27, "data"], [380, 33, 393, 31], [382, 6, 395, 4], [383, 6, 396, 4], [383, 12, 396, 10, "pixelSize"], [383, 21, 396, 19], [383, 24, 396, 22, "Math"], [383, 28, 396, 26], [383, 29, 396, 27, "max"], [383, 32, 396, 30], [383, 33, 396, 31], [383, 35, 396, 33], [383, 37, 396, 35, "Math"], [383, 41, 396, 39], [383, 42, 396, 40, "min"], [383, 45, 396, 43], [383, 46, 396, 44, "<PERSON><PERSON><PERSON><PERSON>"], [383, 58, 396, 56], [383, 60, 396, 58, "clampedHeight"], [383, 73, 396, 71], [383, 74, 396, 72], [383, 77, 396, 75], [383, 78, 396, 76], [383, 79, 396, 77], [384, 6, 398, 4], [384, 11, 398, 9], [384, 15, 398, 13, "py"], [384, 17, 398, 15], [384, 20, 398, 18], [384, 21, 398, 19], [384, 23, 398, 21, "py"], [384, 25, 398, 23], [384, 28, 398, 26, "clampedHeight"], [384, 41, 398, 39], [384, 43, 398, 41, "py"], [384, 45, 398, 43], [384, 49, 398, 47, "pixelSize"], [384, 58, 398, 56], [384, 60, 398, 58], [385, 8, 399, 6], [385, 13, 399, 11], [385, 17, 399, 15, "px"], [385, 19, 399, 17], [385, 22, 399, 20], [385, 23, 399, 21], [385, 25, 399, 23, "px"], [385, 27, 399, 25], [385, 30, 399, 28, "<PERSON><PERSON><PERSON><PERSON>"], [385, 42, 399, 40], [385, 44, 399, 42, "px"], [385, 46, 399, 44], [385, 50, 399, 48, "pixelSize"], [385, 59, 399, 57], [385, 61, 399, 59], [386, 10, 400, 8], [387, 10, 401, 8], [387, 14, 401, 12, "r"], [387, 15, 401, 13], [387, 18, 401, 16], [387, 19, 401, 17], [388, 12, 401, 19, "g"], [388, 13, 401, 20], [388, 16, 401, 23], [388, 17, 401, 24], [389, 12, 401, 26, "b"], [389, 13, 401, 27], [389, 16, 401, 30], [389, 17, 401, 31], [390, 12, 401, 33, "count"], [390, 17, 401, 38], [390, 20, 401, 41], [390, 21, 401, 42], [391, 10, 403, 8], [391, 15, 403, 13], [391, 19, 403, 17, "dy"], [391, 21, 403, 19], [391, 24, 403, 22], [391, 25, 403, 23], [391, 27, 403, 25, "dy"], [391, 29, 403, 27], [391, 32, 403, 30, "pixelSize"], [391, 41, 403, 39], [391, 45, 403, 43, "py"], [391, 47, 403, 45], [391, 50, 403, 48, "dy"], [391, 52, 403, 50], [391, 55, 403, 53, "clampedHeight"], [391, 68, 403, 66], [391, 70, 403, 68, "dy"], [391, 72, 403, 70], [391, 74, 403, 72], [391, 76, 403, 74], [392, 12, 404, 10], [392, 17, 404, 15], [392, 21, 404, 19, "dx"], [392, 23, 404, 21], [392, 26, 404, 24], [392, 27, 404, 25], [392, 29, 404, 27, "dx"], [392, 31, 404, 29], [392, 34, 404, 32, "pixelSize"], [392, 43, 404, 41], [392, 47, 404, 45, "px"], [392, 49, 404, 47], [392, 52, 404, 50, "dx"], [392, 54, 404, 52], [392, 57, 404, 55, "<PERSON><PERSON><PERSON><PERSON>"], [392, 69, 404, 67], [392, 71, 404, 69, "dx"], [392, 73, 404, 71], [392, 75, 404, 73], [392, 77, 404, 75], [393, 14, 405, 12], [393, 20, 405, 18, "index"], [393, 25, 405, 23], [393, 28, 405, 26], [393, 29, 405, 27], [393, 30, 405, 28, "py"], [393, 32, 405, 30], [393, 35, 405, 33, "dy"], [393, 37, 405, 35], [393, 41, 405, 39, "<PERSON><PERSON><PERSON><PERSON>"], [393, 53, 405, 51], [393, 57, 405, 55, "px"], [393, 59, 405, 57], [393, 62, 405, 60, "dx"], [393, 64, 405, 62], [393, 65, 405, 63], [393, 69, 405, 67], [393, 70, 405, 68], [394, 14, 406, 12, "r"], [394, 15, 406, 13], [394, 19, 406, 17, "data"], [394, 23, 406, 21], [394, 24, 406, 22, "index"], [394, 29, 406, 27], [394, 30, 406, 28], [395, 14, 407, 12, "g"], [395, 15, 407, 13], [395, 19, 407, 17, "data"], [395, 23, 407, 21], [395, 24, 407, 22, "index"], [395, 29, 407, 27], [395, 32, 407, 30], [395, 33, 407, 31], [395, 34, 407, 32], [396, 14, 408, 12, "b"], [396, 15, 408, 13], [396, 19, 408, 17, "data"], [396, 23, 408, 21], [396, 24, 408, 22, "index"], [396, 29, 408, 27], [396, 32, 408, 30], [396, 33, 408, 31], [396, 34, 408, 32], [397, 14, 409, 12, "count"], [397, 19, 409, 17], [397, 21, 409, 19], [398, 12, 410, 10], [399, 10, 411, 8], [400, 10, 413, 8], [400, 14, 413, 12, "count"], [400, 19, 413, 17], [400, 22, 413, 20], [400, 23, 413, 21], [400, 25, 413, 23], [401, 12, 414, 10, "r"], [401, 13, 414, 11], [401, 16, 414, 14, "Math"], [401, 20, 414, 18], [401, 21, 414, 19, "floor"], [401, 26, 414, 24], [401, 27, 414, 25, "r"], [401, 28, 414, 26], [401, 31, 414, 29, "count"], [401, 36, 414, 34], [401, 37, 414, 35], [402, 12, 415, 10, "g"], [402, 13, 415, 11], [402, 16, 415, 14, "Math"], [402, 20, 415, 18], [402, 21, 415, 19, "floor"], [402, 26, 415, 24], [402, 27, 415, 25, "g"], [402, 28, 415, 26], [402, 31, 415, 29, "count"], [402, 36, 415, 34], [402, 37, 415, 35], [403, 12, 416, 10, "b"], [403, 13, 416, 11], [403, 16, 416, 14, "Math"], [403, 20, 416, 18], [403, 21, 416, 19, "floor"], [403, 26, 416, 24], [403, 27, 416, 25, "b"], [403, 28, 416, 26], [403, 31, 416, 29, "count"], [403, 36, 416, 34], [403, 37, 416, 35], [405, 12, 418, 10], [406, 12, 419, 10], [406, 17, 419, 15], [406, 21, 419, 19, "dy"], [406, 23, 419, 21], [406, 26, 419, 24], [406, 27, 419, 25], [406, 29, 419, 27, "dy"], [406, 31, 419, 29], [406, 34, 419, 32, "pixelSize"], [406, 43, 419, 41], [406, 47, 419, 45, "py"], [406, 49, 419, 47], [406, 52, 419, 50, "dy"], [406, 54, 419, 52], [406, 57, 419, 55, "clampedHeight"], [406, 70, 419, 68], [406, 72, 419, 70, "dy"], [406, 74, 419, 72], [406, 76, 419, 74], [406, 78, 419, 76], [407, 14, 420, 12], [407, 19, 420, 17], [407, 23, 420, 21, "dx"], [407, 25, 420, 23], [407, 28, 420, 26], [407, 29, 420, 27], [407, 31, 420, 29, "dx"], [407, 33, 420, 31], [407, 36, 420, 34, "pixelSize"], [407, 45, 420, 43], [407, 49, 420, 47, "px"], [407, 51, 420, 49], [407, 54, 420, 52, "dx"], [407, 56, 420, 54], [407, 59, 420, 57, "<PERSON><PERSON><PERSON><PERSON>"], [407, 71, 420, 69], [407, 73, 420, 71, "dx"], [407, 75, 420, 73], [407, 77, 420, 75], [407, 79, 420, 77], [408, 16, 421, 14], [408, 22, 421, 20, "index"], [408, 27, 421, 25], [408, 30, 421, 28], [408, 31, 421, 29], [408, 32, 421, 30, "py"], [408, 34, 421, 32], [408, 37, 421, 35, "dy"], [408, 39, 421, 37], [408, 43, 421, 41, "<PERSON><PERSON><PERSON><PERSON>"], [408, 55, 421, 53], [408, 59, 421, 57, "px"], [408, 61, 421, 59], [408, 64, 421, 62, "dx"], [408, 66, 421, 64], [408, 67, 421, 65], [408, 71, 421, 69], [408, 72, 421, 70], [409, 16, 422, 14, "data"], [409, 20, 422, 18], [409, 21, 422, 19, "index"], [409, 26, 422, 24], [409, 27, 422, 25], [409, 30, 422, 28, "r"], [409, 31, 422, 29], [410, 16, 423, 14, "data"], [410, 20, 423, 18], [410, 21, 423, 19, "index"], [410, 26, 423, 24], [410, 29, 423, 27], [410, 30, 423, 28], [410, 31, 423, 29], [410, 34, 423, 32, "g"], [410, 35, 423, 33], [411, 16, 424, 14, "data"], [411, 20, 424, 18], [411, 21, 424, 19, "index"], [411, 26, 424, 24], [411, 29, 424, 27], [411, 30, 424, 28], [411, 31, 424, 29], [411, 34, 424, 32, "b"], [411, 35, 424, 33], [412, 16, 425, 14], [413, 14, 426, 12], [414, 12, 427, 10], [415, 10, 428, 8], [416, 8, 429, 6], [417, 6, 430, 4], [419, 6, 432, 4], [420, 6, 433, 4], [420, 11, 433, 9], [420, 15, 433, 13, "i"], [420, 16, 433, 14], [420, 19, 433, 17], [420, 20, 433, 18], [420, 22, 433, 20, "i"], [420, 23, 433, 21], [420, 26, 433, 24], [420, 27, 433, 25], [420, 29, 433, 27, "i"], [420, 30, 433, 28], [420, 32, 433, 30], [420, 34, 433, 32], [421, 8, 434, 6, "applySimpleBlur"], [421, 23, 434, 21], [421, 24, 434, 22, "data"], [421, 28, 434, 26], [421, 30, 434, 28, "<PERSON><PERSON><PERSON><PERSON>"], [421, 42, 434, 40], [421, 44, 434, 42, "clampedHeight"], [421, 57, 434, 55], [421, 58, 434, 56], [422, 6, 435, 4], [424, 6, 437, 4], [425, 6, 438, 4, "ctx"], [425, 9, 438, 7], [425, 10, 438, 8, "putImageData"], [425, 22, 438, 20], [425, 23, 438, 21, "imageData"], [425, 32, 438, 30], [425, 34, 438, 32, "clampedX"], [425, 42, 438, 40], [425, 44, 438, 42, "clampedY"], [425, 52, 438, 50], [425, 53, 438, 51], [427, 6, 440, 4], [428, 6, 441, 4, "ctx"], [428, 9, 441, 7], [428, 10, 441, 8, "fillStyle"], [428, 19, 441, 17], [428, 22, 441, 20], [428, 48, 441, 46], [429, 6, 442, 4, "ctx"], [429, 9, 442, 7], [429, 10, 442, 8, "fillRect"], [429, 18, 442, 16], [429, 19, 442, 17, "clampedX"], [429, 27, 442, 25], [429, 29, 442, 27, "clampedY"], [429, 37, 442, 35], [429, 39, 442, 37, "<PERSON><PERSON><PERSON><PERSON>"], [429, 51, 442, 49], [429, 53, 442, 51, "clampedHeight"], [429, 66, 442, 64], [429, 67, 442, 65], [430, 4, 443, 2], [430, 5, 443, 3], [431, 4, 445, 2], [431, 10, 445, 8, "applySimpleBlur"], [431, 25, 445, 23], [431, 28, 445, 26, "applySimpleBlur"], [431, 29, 445, 27, "data"], [431, 33, 445, 50], [431, 35, 445, 52, "width"], [431, 40, 445, 65], [431, 42, 445, 67, "height"], [431, 48, 445, 81], [431, 53, 445, 86], [432, 6, 446, 4], [432, 12, 446, 10, "original"], [432, 20, 446, 18], [432, 23, 446, 21], [432, 27, 446, 25, "Uint8ClampedArray"], [432, 44, 446, 42], [432, 45, 446, 43, "data"], [432, 49, 446, 47], [432, 50, 446, 48], [433, 6, 448, 4], [433, 11, 448, 9], [433, 15, 448, 13, "y"], [433, 16, 448, 14], [433, 19, 448, 17], [433, 20, 448, 18], [433, 22, 448, 20, "y"], [433, 23, 448, 21], [433, 26, 448, 24, "height"], [433, 32, 448, 30], [433, 35, 448, 33], [433, 36, 448, 34], [433, 38, 448, 36, "y"], [433, 39, 448, 37], [433, 41, 448, 39], [433, 43, 448, 41], [434, 8, 449, 6], [434, 13, 449, 11], [434, 17, 449, 15, "x"], [434, 18, 449, 16], [434, 21, 449, 19], [434, 22, 449, 20], [434, 24, 449, 22, "x"], [434, 25, 449, 23], [434, 28, 449, 26, "width"], [434, 33, 449, 31], [434, 36, 449, 34], [434, 37, 449, 35], [434, 39, 449, 37, "x"], [434, 40, 449, 38], [434, 42, 449, 40], [434, 44, 449, 42], [435, 10, 450, 8], [435, 16, 450, 14, "index"], [435, 21, 450, 19], [435, 24, 450, 22], [435, 25, 450, 23, "y"], [435, 26, 450, 24], [435, 29, 450, 27, "width"], [435, 34, 450, 32], [435, 37, 450, 35, "x"], [435, 38, 450, 36], [435, 42, 450, 40], [435, 43, 450, 41], [437, 10, 452, 8], [438, 10, 453, 8], [438, 14, 453, 12, "r"], [438, 15, 453, 13], [438, 18, 453, 16], [438, 19, 453, 17], [439, 12, 453, 19, "g"], [439, 13, 453, 20], [439, 16, 453, 23], [439, 17, 453, 24], [440, 12, 453, 26, "b"], [440, 13, 453, 27], [440, 16, 453, 30], [440, 17, 453, 31], [441, 10, 454, 8], [441, 15, 454, 13], [441, 19, 454, 17, "dy"], [441, 21, 454, 19], [441, 24, 454, 22], [441, 25, 454, 23], [441, 26, 454, 24], [441, 28, 454, 26, "dy"], [441, 30, 454, 28], [441, 34, 454, 32], [441, 35, 454, 33], [441, 37, 454, 35, "dy"], [441, 39, 454, 37], [441, 41, 454, 39], [441, 43, 454, 41], [442, 12, 455, 10], [442, 17, 455, 15], [442, 21, 455, 19, "dx"], [442, 23, 455, 21], [442, 26, 455, 24], [442, 27, 455, 25], [442, 28, 455, 26], [442, 30, 455, 28, "dx"], [442, 32, 455, 30], [442, 36, 455, 34], [442, 37, 455, 35], [442, 39, 455, 37, "dx"], [442, 41, 455, 39], [442, 43, 455, 41], [442, 45, 455, 43], [443, 14, 456, 12], [443, 20, 456, 18, "neighborIndex"], [443, 33, 456, 31], [443, 36, 456, 34], [443, 37, 456, 35], [443, 38, 456, 36, "y"], [443, 39, 456, 37], [443, 42, 456, 40, "dy"], [443, 44, 456, 42], [443, 48, 456, 46, "width"], [443, 53, 456, 51], [443, 57, 456, 55, "x"], [443, 58, 456, 56], [443, 61, 456, 59, "dx"], [443, 63, 456, 61], [443, 64, 456, 62], [443, 68, 456, 66], [443, 69, 456, 67], [444, 14, 457, 12, "r"], [444, 15, 457, 13], [444, 19, 457, 17, "original"], [444, 27, 457, 25], [444, 28, 457, 26, "neighborIndex"], [444, 41, 457, 39], [444, 42, 457, 40], [445, 14, 458, 12, "g"], [445, 15, 458, 13], [445, 19, 458, 17, "original"], [445, 27, 458, 25], [445, 28, 458, 26, "neighborIndex"], [445, 41, 458, 39], [445, 44, 458, 42], [445, 45, 458, 43], [445, 46, 458, 44], [446, 14, 459, 12, "b"], [446, 15, 459, 13], [446, 19, 459, 17, "original"], [446, 27, 459, 25], [446, 28, 459, 26, "neighborIndex"], [446, 41, 459, 39], [446, 44, 459, 42], [446, 45, 459, 43], [446, 46, 459, 44], [447, 12, 460, 10], [448, 10, 461, 8], [449, 10, 463, 8, "data"], [449, 14, 463, 12], [449, 15, 463, 13, "index"], [449, 20, 463, 18], [449, 21, 463, 19], [449, 24, 463, 22, "r"], [449, 25, 463, 23], [449, 28, 463, 26], [449, 29, 463, 27], [450, 10, 464, 8, "data"], [450, 14, 464, 12], [450, 15, 464, 13, "index"], [450, 20, 464, 18], [450, 23, 464, 21], [450, 24, 464, 22], [450, 25, 464, 23], [450, 28, 464, 26, "g"], [450, 29, 464, 27], [450, 32, 464, 30], [450, 33, 464, 31], [451, 10, 465, 8, "data"], [451, 14, 465, 12], [451, 15, 465, 13, "index"], [451, 20, 465, 18], [451, 23, 465, 21], [451, 24, 465, 22], [451, 25, 465, 23], [451, 28, 465, 26, "b"], [451, 29, 465, 27], [451, 32, 465, 30], [451, 33, 465, 31], [452, 8, 466, 6], [453, 6, 467, 4], [454, 4, 468, 2], [454, 5, 468, 3], [455, 4, 470, 2], [455, 10, 470, 8, "applyFallbackFaceBlur"], [455, 31, 470, 29], [455, 34, 470, 32, "applyFallbackFaceBlur"], [455, 35, 470, 33, "ctx"], [455, 38, 470, 62], [455, 40, 470, 64, "imgWidth"], [455, 48, 470, 80], [455, 50, 470, 82, "imgHeight"], [455, 59, 470, 99], [455, 64, 470, 104], [456, 6, 471, 4, "console"], [456, 13, 471, 11], [456, 14, 471, 12, "log"], [456, 17, 471, 15], [456, 18, 471, 16], [456, 90, 471, 88], [456, 91, 471, 89], [458, 6, 473, 4], [459, 6, 474, 4], [459, 12, 474, 10, "areas"], [459, 17, 474, 15], [459, 20, 474, 18], [460, 6, 475, 6], [461, 6, 476, 6], [462, 8, 476, 8, "x"], [462, 9, 476, 9], [462, 11, 476, 11, "imgWidth"], [462, 19, 476, 19], [462, 22, 476, 22], [462, 26, 476, 26], [463, 8, 476, 28, "y"], [463, 9, 476, 29], [463, 11, 476, 31, "imgHeight"], [463, 20, 476, 40], [463, 23, 476, 43], [463, 27, 476, 47], [464, 8, 476, 49, "w"], [464, 9, 476, 50], [464, 11, 476, 52, "imgWidth"], [464, 19, 476, 60], [464, 22, 476, 63], [464, 25, 476, 66], [465, 8, 476, 68, "h"], [465, 9, 476, 69], [465, 11, 476, 71, "imgHeight"], [465, 20, 476, 80], [465, 23, 476, 83], [466, 6, 476, 87], [466, 7, 476, 88], [467, 6, 477, 6], [468, 6, 478, 6], [469, 8, 478, 8, "x"], [469, 9, 478, 9], [469, 11, 478, 11, "imgWidth"], [469, 19, 478, 19], [469, 22, 478, 22], [469, 25, 478, 25], [470, 8, 478, 27, "y"], [470, 9, 478, 28], [470, 11, 478, 30, "imgHeight"], [470, 20, 478, 39], [470, 23, 478, 42], [470, 26, 478, 45], [471, 8, 478, 47, "w"], [471, 9, 478, 48], [471, 11, 478, 50, "imgWidth"], [471, 19, 478, 58], [471, 22, 478, 61], [471, 26, 478, 65], [472, 8, 478, 67, "h"], [472, 9, 478, 68], [472, 11, 478, 70, "imgHeight"], [472, 20, 478, 79], [472, 23, 478, 82], [473, 6, 478, 86], [473, 7, 478, 87], [474, 6, 479, 6], [475, 6, 480, 6], [476, 8, 480, 8, "x"], [476, 9, 480, 9], [476, 11, 480, 11, "imgWidth"], [476, 19, 480, 19], [476, 22, 480, 22], [476, 26, 480, 26], [477, 8, 480, 28, "y"], [477, 9, 480, 29], [477, 11, 480, 31, "imgHeight"], [477, 20, 480, 40], [477, 23, 480, 43], [477, 26, 480, 46], [478, 8, 480, 48, "w"], [478, 9, 480, 49], [478, 11, 480, 51, "imgWidth"], [478, 19, 480, 59], [478, 22, 480, 62], [478, 26, 480, 66], [479, 8, 480, 68, "h"], [479, 9, 480, 69], [479, 11, 480, 71, "imgHeight"], [479, 20, 480, 80], [479, 23, 480, 83], [480, 6, 480, 87], [480, 7, 480, 88], [480, 8, 481, 5], [481, 6, 483, 4, "areas"], [481, 11, 483, 9], [481, 12, 483, 10, "for<PERSON>ach"], [481, 19, 483, 17], [481, 20, 483, 18], [481, 21, 483, 19, "area"], [481, 25, 483, 23], [481, 27, 483, 25, "index"], [481, 32, 483, 30], [481, 37, 483, 35], [482, 8, 484, 6, "console"], [482, 15, 484, 13], [482, 16, 484, 14, "log"], [482, 19, 484, 17], [482, 20, 484, 18], [482, 65, 484, 63, "index"], [482, 70, 484, 68], [482, 73, 484, 71], [482, 74, 484, 72], [482, 77, 484, 75], [482, 79, 484, 77, "area"], [482, 83, 484, 81], [482, 84, 484, 82], [483, 8, 485, 6, "applyStrongBlur"], [483, 23, 485, 21], [483, 24, 485, 22, "ctx"], [483, 27, 485, 25], [483, 29, 485, 27, "area"], [483, 33, 485, 31], [483, 34, 485, 32, "x"], [483, 35, 485, 33], [483, 37, 485, 35, "area"], [483, 41, 485, 39], [483, 42, 485, 40, "y"], [483, 43, 485, 41], [483, 45, 485, 43, "area"], [483, 49, 485, 47], [483, 50, 485, 48, "w"], [483, 51, 485, 49], [483, 53, 485, 51, "area"], [483, 57, 485, 55], [483, 58, 485, 56, "h"], [483, 59, 485, 57], [483, 60, 485, 58], [484, 6, 486, 4], [484, 7, 486, 5], [484, 8, 486, 6], [485, 4, 487, 2], [485, 5, 487, 3], [487, 4, 489, 2], [488, 4, 490, 2], [488, 10, 490, 8, "capturePhoto"], [488, 22, 490, 20], [488, 25, 490, 23], [488, 29, 490, 23, "useCallback"], [488, 47, 490, 34], [488, 49, 490, 35], [488, 61, 490, 47], [489, 6, 491, 4], [490, 6, 492, 4], [490, 12, 492, 10, "isDev"], [490, 17, 492, 15], [490, 20, 492, 18, "process"], [490, 27, 492, 25], [490, 28, 492, 26, "env"], [490, 31, 492, 29], [490, 32, 492, 30, "NODE_ENV"], [490, 40, 492, 38], [490, 45, 492, 43], [490, 58, 492, 56], [490, 62, 492, 60, "__DEV__"], [490, 69, 492, 67], [491, 6, 494, 4], [491, 10, 494, 8], [491, 11, 494, 9, "cameraRef"], [491, 20, 494, 18], [491, 21, 494, 19, "current"], [491, 28, 494, 26], [491, 32, 494, 30], [491, 33, 494, 31, "isDev"], [491, 38, 494, 36], [491, 40, 494, 38], [492, 8, 495, 6, "<PERSON><PERSON>"], [492, 22, 495, 11], [492, 23, 495, 12, "alert"], [492, 28, 495, 17], [492, 29, 495, 18], [492, 36, 495, 25], [492, 38, 495, 27], [492, 56, 495, 45], [492, 57, 495, 46], [493, 8, 496, 6], [494, 6, 497, 4], [495, 6, 498, 4], [495, 10, 498, 8], [496, 8, 499, 6, "setProcessingState"], [496, 26, 499, 24], [496, 27, 499, 25], [496, 38, 499, 36], [496, 39, 499, 37], [497, 8, 500, 6, "setProcessingProgress"], [497, 29, 500, 27], [497, 30, 500, 28], [497, 32, 500, 30], [497, 33, 500, 31], [498, 8, 501, 6], [499, 8, 502, 6], [500, 8, 503, 6], [501, 8, 504, 6], [501, 14, 504, 12], [501, 18, 504, 16, "Promise"], [501, 25, 504, 23], [501, 26, 504, 24, "resolve"], [501, 33, 504, 31], [501, 37, 504, 35, "setTimeout"], [501, 47, 504, 45], [501, 48, 504, 46, "resolve"], [501, 55, 504, 53], [501, 57, 504, 55], [501, 59, 504, 57], [501, 60, 504, 58], [501, 61, 504, 59], [502, 8, 505, 6], [503, 8, 506, 6], [503, 12, 506, 10, "photo"], [503, 17, 506, 15], [504, 8, 508, 6], [504, 12, 508, 10], [505, 10, 509, 8, "photo"], [505, 15, 509, 13], [505, 18, 509, 16], [505, 24, 509, 22, "cameraRef"], [505, 33, 509, 31], [505, 34, 509, 32, "current"], [505, 41, 509, 39], [505, 42, 509, 40, "takePictureAsync"], [505, 58, 509, 56], [505, 59, 509, 57], [506, 12, 510, 10, "quality"], [506, 19, 510, 17], [506, 21, 510, 19], [506, 24, 510, 22], [507, 12, 511, 10, "base64"], [507, 18, 511, 16], [507, 20, 511, 18], [507, 25, 511, 23], [508, 12, 512, 10, "skipProcessing"], [508, 26, 512, 24], [508, 28, 512, 26], [508, 32, 512, 30], [508, 33, 512, 32], [509, 10, 513, 8], [509, 11, 513, 9], [509, 12, 513, 10], [510, 8, 514, 6], [510, 9, 514, 7], [510, 10, 514, 8], [510, 17, 514, 15, "cameraError"], [510, 28, 514, 26], [510, 30, 514, 28], [511, 10, 515, 8, "console"], [511, 17, 515, 15], [511, 18, 515, 16, "log"], [511, 21, 515, 19], [511, 22, 515, 20], [511, 82, 515, 80], [511, 84, 515, 82, "cameraError"], [511, 95, 515, 93], [511, 96, 515, 94], [512, 10, 516, 8], [513, 10, 517, 8], [513, 14, 517, 12, "isDev"], [513, 19, 517, 17], [513, 21, 517, 19], [514, 12, 518, 10, "photo"], [514, 17, 518, 15], [514, 20, 518, 18], [515, 14, 519, 12, "uri"], [515, 17, 519, 15], [515, 19, 519, 17], [516, 12, 520, 10], [516, 13, 520, 11], [517, 10, 521, 8], [517, 11, 521, 9], [517, 17, 521, 15], [518, 12, 522, 10], [518, 18, 522, 16, "cameraError"], [518, 29, 522, 27], [519, 10, 523, 8], [520, 8, 524, 6], [521, 8, 525, 6], [521, 12, 525, 10], [521, 13, 525, 11, "photo"], [521, 18, 525, 16], [521, 20, 525, 18], [522, 10, 526, 8], [522, 16, 526, 14], [522, 20, 526, 18, "Error"], [522, 25, 526, 23], [522, 26, 526, 24], [522, 51, 526, 49], [522, 52, 526, 50], [523, 8, 527, 6], [524, 8, 528, 6, "console"], [524, 15, 528, 13], [524, 16, 528, 14, "log"], [524, 19, 528, 17], [524, 20, 528, 18], [524, 56, 528, 54], [524, 58, 528, 56, "photo"], [524, 63, 528, 61], [524, 64, 528, 62, "uri"], [524, 67, 528, 65], [524, 68, 528, 66], [525, 8, 529, 6, "setCapturedPhoto"], [525, 24, 529, 22], [525, 25, 529, 23, "photo"], [525, 30, 529, 28], [525, 31, 529, 29, "uri"], [525, 34, 529, 32], [525, 35, 529, 33], [526, 8, 530, 6, "setProcessingProgress"], [526, 29, 530, 27], [526, 30, 530, 28], [526, 32, 530, 30], [526, 33, 530, 31], [527, 8, 531, 6], [528, 8, 532, 6, "console"], [528, 15, 532, 13], [528, 16, 532, 14, "log"], [528, 19, 532, 17], [528, 20, 532, 18], [528, 73, 532, 71], [528, 74, 532, 72], [529, 8, 533, 6, "console"], [529, 15, 533, 13], [529, 16, 533, 14, "log"], [529, 19, 533, 17], [529, 20, 533, 18], [529, 96, 533, 94], [529, 98, 533, 96, "photo"], [529, 103, 533, 101], [529, 104, 533, 102, "uri"], [529, 107, 533, 105], [529, 108, 533, 106], [530, 8, 534, 6, "console"], [530, 15, 534, 13], [530, 16, 534, 14, "log"], [530, 19, 534, 17], [530, 20, 534, 18], [530, 68, 534, 66], [530, 70, 534, 68], [530, 77, 534, 75, "processImageWithFaceBlur"], [530, 101, 534, 99], [530, 102, 534, 100], [531, 8, 535, 6], [531, 14, 535, 12, "processImageWithFaceBlur"], [531, 38, 535, 36], [531, 39, 535, 37, "photo"], [531, 44, 535, 42], [531, 45, 535, 43, "uri"], [531, 48, 535, 46], [531, 49, 535, 47], [532, 8, 536, 6, "console"], [532, 15, 536, 13], [532, 16, 536, 14, "log"], [532, 19, 536, 17], [532, 20, 536, 18], [532, 71, 536, 69], [532, 72, 536, 70], [533, 6, 537, 4], [533, 7, 537, 5], [533, 8, 537, 6], [533, 15, 537, 13, "error"], [533, 20, 537, 18], [533, 22, 537, 20], [534, 8, 538, 6, "console"], [534, 15, 538, 13], [534, 16, 538, 14, "error"], [534, 21, 538, 19], [534, 22, 538, 20], [534, 54, 538, 52], [534, 56, 538, 54, "error"], [534, 61, 538, 59], [534, 62, 538, 60], [535, 8, 539, 6, "setErrorMessage"], [535, 23, 539, 21], [535, 24, 539, 22], [535, 68, 539, 66], [535, 69, 539, 67], [536, 8, 540, 6, "setProcessingState"], [536, 26, 540, 24], [536, 27, 540, 25], [536, 34, 540, 32], [536, 35, 540, 33], [537, 6, 541, 4], [538, 4, 542, 2], [538, 5, 542, 3], [538, 7, 542, 5], [538, 9, 542, 7], [538, 10, 542, 8], [539, 4, 543, 2], [540, 4, 544, 2], [540, 10, 544, 8, "processImageWithFaceBlur"], [540, 34, 544, 32], [540, 37, 544, 35], [540, 43, 544, 42, "photoUri"], [540, 51, 544, 58], [540, 55, 544, 63], [541, 6, 545, 4, "console"], [541, 13, 545, 11], [541, 14, 545, 12, "log"], [541, 17, 545, 15], [541, 18, 545, 16], [541, 85, 545, 83], [541, 86, 545, 84], [542, 6, 546, 4], [542, 10, 546, 8], [543, 8, 547, 6, "console"], [543, 15, 547, 13], [543, 16, 547, 14, "log"], [543, 19, 547, 17], [543, 20, 547, 18], [543, 84, 547, 82], [543, 85, 547, 83], [544, 8, 548, 6, "setProcessingState"], [544, 26, 548, 24], [544, 27, 548, 25], [544, 39, 548, 37], [544, 40, 548, 38], [545, 8, 549, 6, "setProcessingProgress"], [545, 29, 549, 27], [545, 30, 549, 28], [545, 32, 549, 30], [545, 33, 549, 31], [547, 8, 551, 6], [548, 8, 552, 6], [548, 14, 552, 12, "canvas"], [548, 20, 552, 18], [548, 23, 552, 21, "document"], [548, 31, 552, 29], [548, 32, 552, 30, "createElement"], [548, 45, 552, 43], [548, 46, 552, 44], [548, 54, 552, 52], [548, 55, 552, 53], [549, 8, 553, 6], [549, 14, 553, 12, "ctx"], [549, 17, 553, 15], [549, 20, 553, 18, "canvas"], [549, 26, 553, 24], [549, 27, 553, 25, "getContext"], [549, 37, 553, 35], [549, 38, 553, 36], [549, 42, 553, 40], [549, 43, 553, 41], [550, 8, 554, 6], [550, 12, 554, 10], [550, 13, 554, 11, "ctx"], [550, 16, 554, 14], [550, 18, 554, 16], [550, 24, 554, 22], [550, 28, 554, 26, "Error"], [550, 33, 554, 31], [550, 34, 554, 32], [550, 64, 554, 62], [550, 65, 554, 63], [552, 8, 556, 6], [553, 8, 557, 6], [553, 14, 557, 12, "img"], [553, 17, 557, 15], [553, 20, 557, 18], [553, 24, 557, 22, "Image"], [553, 29, 557, 27], [553, 30, 557, 28], [553, 31, 557, 29], [554, 8, 558, 6], [554, 14, 558, 12], [554, 18, 558, 16, "Promise"], [554, 25, 558, 23], [554, 26, 558, 24], [554, 27, 558, 25, "resolve"], [554, 34, 558, 32], [554, 36, 558, 34, "reject"], [554, 42, 558, 40], [554, 47, 558, 45], [555, 10, 559, 8, "img"], [555, 13, 559, 11], [555, 14, 559, 12, "onload"], [555, 20, 559, 18], [555, 23, 559, 21, "resolve"], [555, 30, 559, 28], [556, 10, 560, 8, "img"], [556, 13, 560, 11], [556, 14, 560, 12, "onerror"], [556, 21, 560, 19], [556, 24, 560, 22, "reject"], [556, 30, 560, 28], [557, 10, 561, 8, "img"], [557, 13, 561, 11], [557, 14, 561, 12, "src"], [557, 17, 561, 15], [557, 20, 561, 18, "photoUri"], [557, 28, 561, 26], [558, 8, 562, 6], [558, 9, 562, 7], [558, 10, 562, 8], [560, 8, 564, 6], [561, 8, 565, 6, "canvas"], [561, 14, 565, 12], [561, 15, 565, 13, "width"], [561, 20, 565, 18], [561, 23, 565, 21, "img"], [561, 26, 565, 24], [561, 27, 565, 25, "width"], [561, 32, 565, 30], [562, 8, 566, 6, "canvas"], [562, 14, 566, 12], [562, 15, 566, 13, "height"], [562, 21, 566, 19], [562, 24, 566, 22, "img"], [562, 27, 566, 25], [562, 28, 566, 26, "height"], [562, 34, 566, 32], [563, 8, 567, 6, "console"], [563, 15, 567, 13], [563, 16, 567, 14, "log"], [563, 19, 567, 17], [563, 20, 567, 18], [563, 54, 567, 52], [563, 56, 567, 54], [564, 10, 567, 56, "width"], [564, 15, 567, 61], [564, 17, 567, 63, "img"], [564, 20, 567, 66], [564, 21, 567, 67, "width"], [564, 26, 567, 72], [565, 10, 567, 74, "height"], [565, 16, 567, 80], [565, 18, 567, 82, "img"], [565, 21, 567, 85], [565, 22, 567, 86, "height"], [566, 8, 567, 93], [566, 9, 567, 94], [566, 10, 567, 95], [568, 8, 569, 6], [569, 8, 570, 6, "ctx"], [569, 11, 570, 9], [569, 12, 570, 10, "drawImage"], [569, 21, 570, 19], [569, 22, 570, 20, "img"], [569, 25, 570, 23], [569, 27, 570, 25], [569, 28, 570, 26], [569, 30, 570, 28], [569, 31, 570, 29], [569, 32, 570, 30], [570, 8, 571, 6, "console"], [570, 15, 571, 13], [570, 16, 571, 14, "log"], [570, 19, 571, 17], [570, 20, 571, 18], [570, 72, 571, 70], [570, 73, 571, 71], [571, 8, 573, 6, "setProcessingProgress"], [571, 29, 573, 27], [571, 30, 573, 28], [571, 32, 573, 30], [571, 33, 573, 31], [573, 8, 575, 6], [574, 8, 576, 6], [574, 12, 576, 10, "detectedFaces"], [574, 25, 576, 23], [574, 28, 576, 26], [574, 30, 576, 28], [575, 8, 578, 6, "console"], [575, 15, 578, 13], [575, 16, 578, 14, "log"], [575, 19, 578, 17], [575, 20, 578, 18], [575, 81, 578, 79], [575, 82, 578, 80], [577, 8, 580, 6], [578, 8, 581, 6], [578, 12, 581, 10], [579, 10, 582, 8, "console"], [579, 17, 582, 15], [579, 18, 582, 16, "log"], [579, 21, 582, 19], [579, 22, 582, 20], [579, 81, 582, 79], [579, 82, 582, 80], [580, 10, 583, 8], [580, 16, 583, 14, "loadTensorFlowFaceDetection"], [580, 43, 583, 41], [580, 44, 583, 42], [580, 45, 583, 43], [581, 10, 584, 8, "console"], [581, 17, 584, 15], [581, 18, 584, 16, "log"], [581, 21, 584, 19], [581, 22, 584, 20], [581, 90, 584, 88], [581, 91, 584, 89], [582, 10, 585, 8, "detectedFaces"], [582, 23, 585, 21], [582, 26, 585, 24], [582, 32, 585, 30, "detectFacesWithTensorFlow"], [582, 57, 585, 55], [582, 58, 585, 56, "img"], [582, 61, 585, 59], [582, 62, 585, 60], [583, 10, 586, 8, "console"], [583, 17, 586, 15], [583, 18, 586, 16, "log"], [583, 21, 586, 19], [583, 22, 586, 20], [583, 70, 586, 68, "detectedFaces"], [583, 83, 586, 81], [583, 84, 586, 82, "length"], [583, 90, 586, 88], [583, 98, 586, 96], [583, 99, 586, 97], [584, 8, 587, 6], [584, 9, 587, 7], [584, 10, 587, 8], [584, 17, 587, 15, "tensorFlowError"], [584, 32, 587, 30], [584, 34, 587, 32], [585, 10, 588, 8, "console"], [585, 17, 588, 15], [585, 18, 588, 16, "warn"], [585, 22, 588, 20], [585, 23, 588, 21], [585, 61, 588, 59], [585, 63, 588, 61, "tensorFlowError"], [585, 78, 588, 76], [585, 79, 588, 77], [586, 10, 589, 8, "console"], [586, 17, 589, 15], [586, 18, 589, 16, "warn"], [586, 22, 589, 20], [586, 23, 589, 21], [586, 68, 589, 66], [586, 70, 589, 68], [587, 12, 590, 10, "message"], [587, 19, 590, 17], [587, 21, 590, 19, "tensorFlowError"], [587, 36, 590, 34], [587, 37, 590, 35, "message"], [587, 44, 590, 42], [588, 12, 591, 10, "stack"], [588, 17, 591, 15], [588, 19, 591, 17, "tensorFlowError"], [588, 34, 591, 32], [588, 35, 591, 33, "stack"], [589, 10, 592, 8], [589, 11, 592, 9], [589, 12, 592, 10], [591, 10, 594, 8], [592, 10, 595, 8, "console"], [592, 17, 595, 15], [592, 18, 595, 16, "log"], [592, 21, 595, 19], [592, 22, 595, 20], [592, 86, 595, 84], [592, 87, 595, 85], [593, 10, 596, 8, "detectedFaces"], [593, 23, 596, 21], [593, 26, 596, 24, "detectFacesHeuristic"], [593, 46, 596, 44], [593, 47, 596, 45, "img"], [593, 50, 596, 48], [593, 52, 596, 50, "ctx"], [593, 55, 596, 53], [593, 56, 596, 54], [594, 10, 597, 8, "console"], [594, 17, 597, 15], [594, 18, 597, 16, "log"], [594, 21, 597, 19], [594, 22, 597, 20], [594, 70, 597, 68, "detectedFaces"], [594, 83, 597, 81], [594, 84, 597, 82, "length"], [594, 90, 597, 88], [594, 98, 597, 96], [594, 99, 597, 97], [595, 8, 598, 6], [597, 8, 600, 6], [598, 8, 601, 6], [598, 12, 601, 10, "detectedFaces"], [598, 25, 601, 23], [598, 26, 601, 24, "length"], [598, 32, 601, 30], [598, 37, 601, 35], [598, 38, 601, 36], [598, 40, 601, 38], [599, 10, 602, 8, "console"], [599, 17, 602, 15], [599, 18, 602, 16, "log"], [599, 21, 602, 19], [599, 22, 602, 20], [599, 89, 602, 87], [599, 90, 602, 88], [600, 10, 603, 8, "detectedFaces"], [600, 23, 603, 21], [600, 26, 603, 24, "detectFacesAggressive"], [600, 47, 603, 45], [600, 48, 603, 46, "img"], [600, 51, 603, 49], [600, 53, 603, 51, "ctx"], [600, 56, 603, 54], [600, 57, 603, 55], [601, 10, 604, 8, "console"], [601, 17, 604, 15], [601, 18, 604, 16, "log"], [601, 21, 604, 19], [601, 22, 604, 20], [601, 71, 604, 69, "detectedFaces"], [601, 84, 604, 82], [601, 85, 604, 83, "length"], [601, 91, 604, 89], [601, 99, 604, 97], [601, 100, 604, 98], [602, 8, 605, 6], [603, 8, 607, 6, "console"], [603, 15, 607, 13], [603, 16, 607, 14, "log"], [603, 19, 607, 17], [603, 20, 607, 18], [603, 72, 607, 70, "detectedFaces"], [603, 85, 607, 83], [603, 86, 607, 84, "length"], [603, 92, 607, 90], [603, 100, 607, 98], [603, 101, 607, 99], [604, 8, 608, 6], [604, 12, 608, 10, "detectedFaces"], [604, 25, 608, 23], [604, 26, 608, 24, "length"], [604, 32, 608, 30], [604, 35, 608, 33], [604, 36, 608, 34], [604, 38, 608, 36], [605, 10, 609, 8, "console"], [605, 17, 609, 15], [605, 18, 609, 16, "log"], [605, 21, 609, 19], [605, 22, 609, 20], [605, 66, 609, 64], [605, 68, 609, 66, "detectedFaces"], [605, 81, 609, 79], [605, 82, 609, 80, "map"], [605, 85, 609, 83], [605, 86, 609, 84], [605, 87, 609, 85, "face"], [605, 91, 609, 89], [605, 93, 609, 91, "i"], [605, 94, 609, 92], [605, 100, 609, 98], [606, 12, 610, 10, "faceNumber"], [606, 22, 610, 20], [606, 24, 610, 22, "i"], [606, 25, 610, 23], [606, 28, 610, 26], [606, 29, 610, 27], [607, 12, 611, 10, "centerX"], [607, 19, 611, 17], [607, 21, 611, 19, "face"], [607, 25, 611, 23], [607, 26, 611, 24, "boundingBox"], [607, 37, 611, 35], [607, 38, 611, 36, "xCenter"], [607, 45, 611, 43], [608, 12, 612, 10, "centerY"], [608, 19, 612, 17], [608, 21, 612, 19, "face"], [608, 25, 612, 23], [608, 26, 612, 24, "boundingBox"], [608, 37, 612, 35], [608, 38, 612, 36, "yCenter"], [608, 45, 612, 43], [609, 12, 613, 10, "width"], [609, 17, 613, 15], [609, 19, 613, 17, "face"], [609, 23, 613, 21], [609, 24, 613, 22, "boundingBox"], [609, 35, 613, 33], [609, 36, 613, 34, "width"], [609, 41, 613, 39], [610, 12, 614, 10, "height"], [610, 18, 614, 16], [610, 20, 614, 18, "face"], [610, 24, 614, 22], [610, 25, 614, 23, "boundingBox"], [610, 36, 614, 34], [610, 37, 614, 35, "height"], [611, 10, 615, 8], [611, 11, 615, 9], [611, 12, 615, 10], [611, 13, 615, 11], [611, 14, 615, 12], [612, 8, 616, 6], [612, 9, 616, 7], [612, 15, 616, 13], [613, 10, 617, 8, "console"], [613, 17, 617, 15], [613, 18, 617, 16, "log"], [613, 21, 617, 19], [613, 22, 617, 20], [613, 74, 617, 72], [613, 75, 617, 73], [614, 10, 618, 8, "console"], [614, 17, 618, 15], [614, 18, 618, 16, "log"], [614, 21, 618, 19], [614, 22, 618, 20], [614, 95, 618, 93], [614, 96, 618, 94], [616, 10, 620, 8], [617, 10, 621, 8], [617, 16, 621, 14, "centerX"], [617, 23, 621, 21], [617, 26, 621, 24, "img"], [617, 29, 621, 27], [617, 30, 621, 28, "width"], [617, 35, 621, 33], [617, 38, 621, 36], [617, 41, 621, 39], [618, 10, 622, 8], [618, 16, 622, 14, "centerY"], [618, 23, 622, 21], [618, 26, 622, 24, "img"], [618, 29, 622, 27], [618, 30, 622, 28, "height"], [618, 36, 622, 34], [618, 39, 622, 37], [618, 42, 622, 40], [619, 10, 623, 8], [619, 16, 623, 14, "centerWidth"], [619, 27, 623, 25], [619, 30, 623, 28, "img"], [619, 33, 623, 31], [619, 34, 623, 32, "width"], [619, 39, 623, 37], [619, 42, 623, 40], [619, 45, 623, 43], [620, 10, 624, 8], [620, 16, 624, 14, "centerHeight"], [620, 28, 624, 26], [620, 31, 624, 29, "img"], [620, 34, 624, 32], [620, 35, 624, 33, "height"], [620, 41, 624, 39], [620, 44, 624, 42], [620, 47, 624, 45], [621, 10, 626, 8, "detectedFaces"], [621, 23, 626, 21], [621, 26, 626, 24], [621, 27, 626, 25], [622, 12, 627, 10, "boundingBox"], [622, 23, 627, 21], [622, 25, 627, 23], [623, 14, 628, 12, "xCenter"], [623, 21, 628, 19], [623, 23, 628, 21], [623, 26, 628, 24], [624, 14, 629, 12, "yCenter"], [624, 21, 629, 19], [624, 23, 629, 21], [624, 26, 629, 24], [625, 14, 630, 12, "width"], [625, 19, 630, 17], [625, 21, 630, 19], [625, 24, 630, 22], [626, 14, 631, 12, "height"], [626, 20, 631, 18], [626, 22, 631, 20], [627, 12, 632, 10], [627, 13, 632, 11], [628, 12, 633, 10, "confidence"], [628, 22, 633, 20], [628, 24, 633, 22], [629, 10, 634, 8], [629, 11, 634, 9], [629, 12, 634, 10], [630, 10, 636, 8, "console"], [630, 17, 636, 15], [630, 18, 636, 16, "log"], [630, 21, 636, 19], [630, 22, 636, 20], [630, 100, 636, 98], [630, 101, 636, 99], [631, 8, 637, 6], [632, 8, 639, 6, "setProcessingProgress"], [632, 29, 639, 27], [632, 30, 639, 28], [632, 32, 639, 30], [632, 33, 639, 31], [634, 8, 641, 6], [635, 8, 642, 6], [635, 12, 642, 10, "detectedFaces"], [635, 25, 642, 23], [635, 26, 642, 24, "length"], [635, 32, 642, 30], [635, 35, 642, 33], [635, 36, 642, 34], [635, 38, 642, 36], [636, 10, 643, 8, "console"], [636, 17, 643, 15], [636, 18, 643, 16, "log"], [636, 21, 643, 19], [636, 22, 643, 20], [636, 61, 643, 59, "detectedFaces"], [636, 74, 643, 72], [636, 75, 643, 73, "length"], [636, 81, 643, 79], [636, 101, 643, 99], [636, 102, 643, 100], [637, 10, 645, 8, "detectedFaces"], [637, 23, 645, 21], [637, 24, 645, 22, "for<PERSON>ach"], [637, 31, 645, 29], [637, 32, 645, 30], [637, 33, 645, 31, "detection"], [637, 42, 645, 40], [637, 44, 645, 42, "index"], [637, 49, 645, 47], [637, 54, 645, 52], [638, 12, 646, 10], [638, 18, 646, 16, "bbox"], [638, 22, 646, 20], [638, 25, 646, 23, "detection"], [638, 34, 646, 32], [638, 35, 646, 33, "boundingBox"], [638, 46, 646, 44], [639, 12, 648, 10, "console"], [639, 19, 648, 17], [639, 20, 648, 18, "log"], [639, 23, 648, 21], [639, 24, 648, 22], [639, 85, 648, 83, "index"], [639, 90, 648, 88], [639, 93, 648, 91], [639, 94, 648, 92], [639, 97, 648, 95], [639, 99, 648, 97], [640, 14, 649, 12, "bbox"], [640, 18, 649, 16], [641, 14, 650, 12, "imageSize"], [641, 23, 650, 21], [641, 25, 650, 23], [642, 16, 650, 25, "width"], [642, 21, 650, 30], [642, 23, 650, 32, "img"], [642, 26, 650, 35], [642, 27, 650, 36, "width"], [642, 32, 650, 41], [643, 16, 650, 43, "height"], [643, 22, 650, 49], [643, 24, 650, 51, "img"], [643, 27, 650, 54], [643, 28, 650, 55, "height"], [644, 14, 650, 62], [645, 12, 651, 10], [645, 13, 651, 11], [645, 14, 651, 12], [647, 12, 653, 10], [648, 12, 654, 10], [648, 18, 654, 16, "faceX"], [648, 23, 654, 21], [648, 26, 654, 24, "bbox"], [648, 30, 654, 28], [648, 31, 654, 29, "xCenter"], [648, 38, 654, 36], [648, 41, 654, 39, "img"], [648, 44, 654, 42], [648, 45, 654, 43, "width"], [648, 50, 654, 48], [648, 53, 654, 52, "bbox"], [648, 57, 654, 56], [648, 58, 654, 57, "width"], [648, 63, 654, 62], [648, 66, 654, 65, "img"], [648, 69, 654, 68], [648, 70, 654, 69, "width"], [648, 75, 654, 74], [648, 78, 654, 78], [648, 79, 654, 79], [649, 12, 655, 10], [649, 18, 655, 16, "faceY"], [649, 23, 655, 21], [649, 26, 655, 24, "bbox"], [649, 30, 655, 28], [649, 31, 655, 29, "yCenter"], [649, 38, 655, 36], [649, 41, 655, 39, "img"], [649, 44, 655, 42], [649, 45, 655, 43, "height"], [649, 51, 655, 49], [649, 54, 655, 53, "bbox"], [649, 58, 655, 57], [649, 59, 655, 58, "height"], [649, 65, 655, 64], [649, 68, 655, 67, "img"], [649, 71, 655, 70], [649, 72, 655, 71, "height"], [649, 78, 655, 77], [649, 81, 655, 81], [649, 82, 655, 82], [650, 12, 656, 10], [650, 18, 656, 16, "faceWidth"], [650, 27, 656, 25], [650, 30, 656, 28, "bbox"], [650, 34, 656, 32], [650, 35, 656, 33, "width"], [650, 40, 656, 38], [650, 43, 656, 41, "img"], [650, 46, 656, 44], [650, 47, 656, 45, "width"], [650, 52, 656, 50], [651, 12, 657, 10], [651, 18, 657, 16, "faceHeight"], [651, 28, 657, 26], [651, 31, 657, 29, "bbox"], [651, 35, 657, 33], [651, 36, 657, 34, "height"], [651, 42, 657, 40], [651, 45, 657, 43, "img"], [651, 48, 657, 46], [651, 49, 657, 47, "height"], [651, 55, 657, 53], [652, 12, 659, 10, "console"], [652, 19, 659, 17], [652, 20, 659, 18, "log"], [652, 23, 659, 21], [652, 24, 659, 22], [652, 84, 659, 82], [652, 86, 659, 84], [653, 14, 660, 12, "faceX"], [653, 19, 660, 17], [654, 14, 660, 19, "faceY"], [654, 19, 660, 24], [655, 14, 660, 26, "faceWidth"], [655, 23, 660, 35], [656, 14, 660, 37, "faceHeight"], [656, 24, 660, 47], [657, 14, 661, 12, "<PERSON><PERSON><PERSON><PERSON>"], [657, 21, 661, 19], [657, 23, 661, 21, "faceX"], [657, 28, 661, 26], [657, 32, 661, 30], [657, 33, 661, 31], [657, 37, 661, 35, "faceY"], [657, 42, 661, 40], [657, 46, 661, 44], [657, 47, 661, 45], [657, 51, 661, 49, "faceWidth"], [657, 60, 661, 58], [657, 63, 661, 61], [657, 64, 661, 62], [657, 68, 661, 66, "faceHeight"], [657, 78, 661, 76], [657, 81, 661, 79], [658, 12, 662, 10], [658, 13, 662, 11], [658, 14, 662, 12], [660, 12, 664, 10], [661, 12, 665, 10], [661, 18, 665, 16, "padding"], [661, 25, 665, 23], [661, 28, 665, 26], [661, 31, 665, 29], [662, 12, 666, 10], [662, 18, 666, 16, "paddedX"], [662, 25, 666, 23], [662, 28, 666, 26, "Math"], [662, 32, 666, 30], [662, 33, 666, 31, "max"], [662, 36, 666, 34], [662, 37, 666, 35], [662, 38, 666, 36], [662, 40, 666, 38, "faceX"], [662, 45, 666, 43], [662, 48, 666, 46, "faceWidth"], [662, 57, 666, 55], [662, 60, 666, 58, "padding"], [662, 67, 666, 65], [662, 68, 666, 66], [663, 12, 667, 10], [663, 18, 667, 16, "paddedY"], [663, 25, 667, 23], [663, 28, 667, 26, "Math"], [663, 32, 667, 30], [663, 33, 667, 31, "max"], [663, 36, 667, 34], [663, 37, 667, 35], [663, 38, 667, 36], [663, 40, 667, 38, "faceY"], [663, 45, 667, 43], [663, 48, 667, 46, "faceHeight"], [663, 58, 667, 56], [663, 61, 667, 59, "padding"], [663, 68, 667, 66], [663, 69, 667, 67], [664, 12, 668, 10], [664, 18, 668, 16, "<PERSON><PERSON><PERSON><PERSON>"], [664, 29, 668, 27], [664, 32, 668, 30, "Math"], [664, 36, 668, 34], [664, 37, 668, 35, "min"], [664, 40, 668, 38], [664, 41, 668, 39, "img"], [664, 44, 668, 42], [664, 45, 668, 43, "width"], [664, 50, 668, 48], [664, 53, 668, 51, "paddedX"], [664, 60, 668, 58], [664, 62, 668, 60, "faceWidth"], [664, 71, 668, 69], [664, 75, 668, 73], [664, 76, 668, 74], [664, 79, 668, 77], [664, 80, 668, 78], [664, 83, 668, 81, "padding"], [664, 90, 668, 88], [664, 91, 668, 89], [664, 92, 668, 90], [665, 12, 669, 10], [665, 18, 669, 16, "paddedHeight"], [665, 30, 669, 28], [665, 33, 669, 31, "Math"], [665, 37, 669, 35], [665, 38, 669, 36, "min"], [665, 41, 669, 39], [665, 42, 669, 40, "img"], [665, 45, 669, 43], [665, 46, 669, 44, "height"], [665, 52, 669, 50], [665, 55, 669, 53, "paddedY"], [665, 62, 669, 60], [665, 64, 669, 62, "faceHeight"], [665, 74, 669, 72], [665, 78, 669, 76], [665, 79, 669, 77], [665, 82, 669, 80], [665, 83, 669, 81], [665, 86, 669, 84, "padding"], [665, 93, 669, 91], [665, 94, 669, 92], [665, 95, 669, 93], [666, 12, 671, 10, "console"], [666, 19, 671, 17], [666, 20, 671, 18, "log"], [666, 23, 671, 21], [666, 24, 671, 22], [666, 60, 671, 58, "index"], [666, 65, 671, 63], [666, 68, 671, 66], [666, 69, 671, 67], [666, 72, 671, 70], [666, 74, 671, 72], [667, 14, 672, 12, "original"], [667, 22, 672, 20], [667, 24, 672, 22], [668, 16, 672, 24, "x"], [668, 17, 672, 25], [668, 19, 672, 27, "Math"], [668, 23, 672, 31], [668, 24, 672, 32, "round"], [668, 29, 672, 37], [668, 30, 672, 38, "faceX"], [668, 35, 672, 43], [668, 36, 672, 44], [669, 16, 672, 46, "y"], [669, 17, 672, 47], [669, 19, 672, 49, "Math"], [669, 23, 672, 53], [669, 24, 672, 54, "round"], [669, 29, 672, 59], [669, 30, 672, 60, "faceY"], [669, 35, 672, 65], [669, 36, 672, 66], [670, 16, 672, 68, "w"], [670, 17, 672, 69], [670, 19, 672, 71, "Math"], [670, 23, 672, 75], [670, 24, 672, 76, "round"], [670, 29, 672, 81], [670, 30, 672, 82, "faceWidth"], [670, 39, 672, 91], [670, 40, 672, 92], [671, 16, 672, 94, "h"], [671, 17, 672, 95], [671, 19, 672, 97, "Math"], [671, 23, 672, 101], [671, 24, 672, 102, "round"], [671, 29, 672, 107], [671, 30, 672, 108, "faceHeight"], [671, 40, 672, 118], [672, 14, 672, 120], [672, 15, 672, 121], [673, 14, 673, 12, "padded"], [673, 20, 673, 18], [673, 22, 673, 20], [674, 16, 673, 22, "x"], [674, 17, 673, 23], [674, 19, 673, 25, "Math"], [674, 23, 673, 29], [674, 24, 673, 30, "round"], [674, 29, 673, 35], [674, 30, 673, 36, "paddedX"], [674, 37, 673, 43], [674, 38, 673, 44], [675, 16, 673, 46, "y"], [675, 17, 673, 47], [675, 19, 673, 49, "Math"], [675, 23, 673, 53], [675, 24, 673, 54, "round"], [675, 29, 673, 59], [675, 30, 673, 60, "paddedY"], [675, 37, 673, 67], [675, 38, 673, 68], [676, 16, 673, 70, "w"], [676, 17, 673, 71], [676, 19, 673, 73, "Math"], [676, 23, 673, 77], [676, 24, 673, 78, "round"], [676, 29, 673, 83], [676, 30, 673, 84, "<PERSON><PERSON><PERSON><PERSON>"], [676, 41, 673, 95], [676, 42, 673, 96], [677, 16, 673, 98, "h"], [677, 17, 673, 99], [677, 19, 673, 101, "Math"], [677, 23, 673, 105], [677, 24, 673, 106, "round"], [677, 29, 673, 111], [677, 30, 673, 112, "paddedHeight"], [677, 42, 673, 124], [678, 14, 673, 126], [679, 12, 674, 10], [679, 13, 674, 11], [679, 14, 674, 12], [681, 12, 676, 10], [682, 12, 677, 10, "console"], [682, 19, 677, 17], [682, 20, 677, 18, "log"], [682, 23, 677, 21], [682, 24, 677, 22], [682, 70, 677, 68], [682, 72, 677, 70], [683, 14, 678, 12, "width"], [683, 19, 678, 17], [683, 21, 678, 19, "canvas"], [683, 27, 678, 25], [683, 28, 678, 26, "width"], [683, 33, 678, 31], [684, 14, 679, 12, "height"], [684, 20, 679, 18], [684, 22, 679, 20, "canvas"], [684, 28, 679, 26], [684, 29, 679, 27, "height"], [684, 35, 679, 33], [685, 14, 680, 12, "contextValid"], [685, 26, 680, 24], [685, 28, 680, 26], [685, 29, 680, 27], [685, 30, 680, 28, "ctx"], [686, 12, 681, 10], [686, 13, 681, 11], [686, 14, 681, 12], [688, 12, 683, 10], [689, 12, 684, 10, "applyStrongBlur"], [689, 27, 684, 25], [689, 28, 684, 26, "ctx"], [689, 31, 684, 29], [689, 33, 684, 31, "paddedX"], [689, 40, 684, 38], [689, 42, 684, 40, "paddedY"], [689, 49, 684, 47], [689, 51, 684, 49, "<PERSON><PERSON><PERSON><PERSON>"], [689, 62, 684, 60], [689, 64, 684, 62, "paddedHeight"], [689, 76, 684, 74], [689, 77, 684, 75], [691, 12, 686, 10], [692, 12, 687, 10, "console"], [692, 19, 687, 17], [692, 20, 687, 18, "log"], [692, 23, 687, 21], [692, 24, 687, 22], [692, 102, 687, 100], [692, 103, 687, 101], [694, 12, 689, 10], [695, 12, 690, 10], [695, 18, 690, 16, "testImageData"], [695, 31, 690, 29], [695, 34, 690, 32, "ctx"], [695, 37, 690, 35], [695, 38, 690, 36, "getImageData"], [695, 50, 690, 48], [695, 51, 690, 49, "paddedX"], [695, 58, 690, 56], [695, 61, 690, 59], [695, 63, 690, 61], [695, 65, 690, 63, "paddedY"], [695, 72, 690, 70], [695, 75, 690, 73], [695, 77, 690, 75], [695, 79, 690, 77], [695, 81, 690, 79], [695, 83, 690, 81], [695, 85, 690, 83], [695, 86, 690, 84], [696, 12, 691, 10, "console"], [696, 19, 691, 17], [696, 20, 691, 18, "log"], [696, 23, 691, 21], [696, 24, 691, 22], [696, 70, 691, 68], [696, 72, 691, 70], [697, 14, 692, 12, "firstPixel"], [697, 24, 692, 22], [697, 26, 692, 24], [697, 27, 692, 25, "testImageData"], [697, 40, 692, 38], [697, 41, 692, 39, "data"], [697, 45, 692, 43], [697, 46, 692, 44], [697, 47, 692, 45], [697, 48, 692, 46], [697, 50, 692, 48, "testImageData"], [697, 63, 692, 61], [697, 64, 692, 62, "data"], [697, 68, 692, 66], [697, 69, 692, 67], [697, 70, 692, 68], [697, 71, 692, 69], [697, 73, 692, 71, "testImageData"], [697, 86, 692, 84], [697, 87, 692, 85, "data"], [697, 91, 692, 89], [697, 92, 692, 90], [697, 93, 692, 91], [697, 94, 692, 92], [697, 95, 692, 93], [698, 14, 693, 12, "secondPixel"], [698, 25, 693, 23], [698, 27, 693, 25], [698, 28, 693, 26, "testImageData"], [698, 41, 693, 39], [698, 42, 693, 40, "data"], [698, 46, 693, 44], [698, 47, 693, 45], [698, 48, 693, 46], [698, 49, 693, 47], [698, 51, 693, 49, "testImageData"], [698, 64, 693, 62], [698, 65, 693, 63, "data"], [698, 69, 693, 67], [698, 70, 693, 68], [698, 71, 693, 69], [698, 72, 693, 70], [698, 74, 693, 72, "testImageData"], [698, 87, 693, 85], [698, 88, 693, 86, "data"], [698, 92, 693, 90], [698, 93, 693, 91], [698, 94, 693, 92], [698, 95, 693, 93], [699, 12, 694, 10], [699, 13, 694, 11], [699, 14, 694, 12], [700, 12, 696, 10, "console"], [700, 19, 696, 17], [700, 20, 696, 18, "log"], [700, 23, 696, 21], [700, 24, 696, 22], [700, 50, 696, 48, "index"], [700, 55, 696, 53], [700, 58, 696, 56], [700, 59, 696, 57], [700, 79, 696, 77], [700, 80, 696, 78], [701, 10, 697, 8], [701, 11, 697, 9], [701, 12, 697, 10], [702, 10, 699, 8, "console"], [702, 17, 699, 15], [702, 18, 699, 16, "log"], [702, 21, 699, 19], [702, 22, 699, 20], [702, 48, 699, 46, "detectedFaces"], [702, 61, 699, 59], [702, 62, 699, 60, "length"], [702, 68, 699, 66], [702, 104, 699, 102], [702, 105, 699, 103], [703, 8, 700, 6], [703, 9, 700, 7], [703, 15, 700, 13], [704, 10, 701, 8, "console"], [704, 17, 701, 15], [704, 18, 701, 16, "log"], [704, 21, 701, 19], [704, 22, 701, 20], [704, 109, 701, 107], [704, 110, 701, 108], [705, 10, 702, 8], [706, 10, 703, 8, "applyFallbackFaceBlur"], [706, 31, 703, 29], [706, 32, 703, 30, "ctx"], [706, 35, 703, 33], [706, 37, 703, 35, "img"], [706, 40, 703, 38], [706, 41, 703, 39, "width"], [706, 46, 703, 44], [706, 48, 703, 46, "img"], [706, 51, 703, 49], [706, 52, 703, 50, "height"], [706, 58, 703, 56], [706, 59, 703, 57], [707, 8, 704, 6], [708, 8, 706, 6, "setProcessingProgress"], [708, 29, 706, 27], [708, 30, 706, 28], [708, 32, 706, 30], [708, 33, 706, 31], [710, 8, 708, 6], [711, 8, 709, 6, "console"], [711, 15, 709, 13], [711, 16, 709, 14, "log"], [711, 19, 709, 17], [711, 20, 709, 18], [711, 85, 709, 83], [711, 86, 709, 84], [712, 8, 710, 6], [712, 14, 710, 12, "blurredImageBlob"], [712, 30, 710, 28], [712, 33, 710, 31], [712, 39, 710, 37], [712, 43, 710, 41, "Promise"], [712, 50, 710, 48], [712, 51, 710, 56, "resolve"], [712, 58, 710, 63], [712, 62, 710, 68], [713, 10, 711, 8, "canvas"], [713, 16, 711, 14], [713, 17, 711, 15, "toBlob"], [713, 23, 711, 21], [713, 24, 711, 23, "blob"], [713, 28, 711, 27], [713, 32, 711, 32, "resolve"], [713, 39, 711, 39], [713, 40, 711, 40, "blob"], [713, 44, 711, 45], [713, 45, 711, 46], [713, 47, 711, 48], [713, 59, 711, 60], [713, 61, 711, 62], [713, 64, 711, 65], [713, 65, 711, 66], [714, 8, 712, 6], [714, 9, 712, 7], [714, 10, 712, 8], [715, 8, 714, 6], [715, 14, 714, 12, "blurredImageUrl"], [715, 29, 714, 27], [715, 32, 714, 30, "URL"], [715, 35, 714, 33], [715, 36, 714, 34, "createObjectURL"], [715, 51, 714, 49], [715, 52, 714, 50, "blurredImageBlob"], [715, 68, 714, 66], [715, 69, 714, 67], [716, 8, 715, 6, "console"], [716, 15, 715, 13], [716, 16, 715, 14, "log"], [716, 19, 715, 17], [716, 20, 715, 18], [716, 66, 715, 64], [716, 68, 715, 66, "blurredImageUrl"], [716, 83, 715, 81], [716, 84, 715, 82, "substring"], [716, 93, 715, 91], [716, 94, 715, 92], [716, 95, 715, 93], [716, 97, 715, 95], [716, 99, 715, 97], [716, 100, 715, 98], [716, 103, 715, 101], [716, 108, 715, 106], [716, 109, 715, 107], [718, 8, 717, 6], [719, 8, 718, 6, "setCapturedPhoto"], [719, 24, 718, 22], [719, 25, 718, 23, "blurredImageUrl"], [719, 40, 718, 38], [719, 41, 718, 39], [720, 8, 719, 6, "console"], [720, 15, 719, 13], [720, 16, 719, 14, "log"], [720, 19, 719, 17], [720, 20, 719, 18], [720, 87, 719, 85], [720, 88, 719, 86], [721, 8, 721, 6, "setProcessingProgress"], [721, 29, 721, 27], [721, 30, 721, 28], [721, 33, 721, 31], [721, 34, 721, 32], [723, 8, 723, 6], [724, 8, 724, 6], [724, 14, 724, 12, "completeProcessing"], [724, 32, 724, 30], [724, 33, 724, 31, "blurredImageUrl"], [724, 48, 724, 46], [724, 49, 724, 47], [725, 6, 726, 4], [725, 7, 726, 5], [725, 8, 726, 6], [725, 15, 726, 13, "error"], [725, 20, 726, 18], [725, 22, 726, 20], [726, 8, 727, 6, "console"], [726, 15, 727, 13], [726, 16, 727, 14, "error"], [726, 21, 727, 19], [726, 22, 727, 20], [726, 86, 727, 84], [726, 88, 727, 86, "error"], [726, 93, 727, 91], [726, 94, 727, 92], [727, 8, 728, 6, "console"], [727, 15, 728, 13], [727, 16, 728, 14, "error"], [727, 21, 728, 19], [727, 22, 728, 20], [727, 55, 728, 53], [727, 57, 728, 55, "error"], [727, 62, 728, 60], [727, 63, 728, 61, "stack"], [727, 68, 728, 66], [727, 69, 728, 67], [728, 8, 729, 6, "console"], [728, 15, 729, 13], [728, 16, 729, 14, "error"], [728, 21, 729, 19], [728, 22, 729, 20], [728, 57, 729, 55], [728, 59, 729, 57], [729, 10, 730, 8, "name"], [729, 14, 730, 12], [729, 16, 730, 14, "error"], [729, 21, 730, 19], [729, 22, 730, 20, "name"], [729, 26, 730, 24], [730, 10, 731, 8, "message"], [730, 17, 731, 15], [730, 19, 731, 17, "error"], [730, 24, 731, 22], [730, 25, 731, 23, "message"], [730, 32, 731, 30], [731, 10, 732, 8, "photoUri"], [732, 8, 733, 6], [732, 9, 733, 7], [732, 10, 733, 8], [733, 8, 734, 6, "setErrorMessage"], [733, 23, 734, 21], [733, 24, 734, 22], [733, 50, 734, 48], [733, 51, 734, 49], [734, 8, 735, 6, "setProcessingState"], [734, 26, 735, 24], [734, 27, 735, 25], [734, 34, 735, 32], [734, 35, 735, 33], [735, 6, 736, 4], [736, 4, 737, 2], [736, 5, 737, 3], [738, 4, 739, 2], [739, 4, 740, 2], [739, 10, 740, 8, "completeProcessing"], [739, 28, 740, 26], [739, 31, 740, 29], [739, 37, 740, 36, "blurredImageUrl"], [739, 52, 740, 59], [739, 56, 740, 64], [740, 6, 741, 4], [740, 10, 741, 8], [741, 8, 742, 6, "setProcessingState"], [741, 26, 742, 24], [741, 27, 742, 25], [741, 37, 742, 35], [741, 38, 742, 36], [743, 8, 744, 6], [744, 8, 745, 6], [744, 14, 745, 12, "timestamp"], [744, 23, 745, 21], [744, 26, 745, 24, "Date"], [744, 30, 745, 28], [744, 31, 745, 29, "now"], [744, 34, 745, 32], [744, 35, 745, 33], [744, 36, 745, 34], [745, 8, 746, 6], [745, 14, 746, 12, "result"], [745, 20, 746, 18], [745, 23, 746, 21], [746, 10, 747, 8, "imageUrl"], [746, 18, 747, 16], [746, 20, 747, 18, "blurredImageUrl"], [746, 35, 747, 33], [747, 10, 748, 8, "localUri"], [747, 18, 748, 16], [747, 20, 748, 18, "blurredImageUrl"], [747, 35, 748, 33], [748, 10, 749, 8, "challengeCode"], [748, 23, 749, 21], [748, 25, 749, 23, "challengeCode"], [748, 38, 749, 36], [748, 42, 749, 40], [748, 44, 749, 42], [749, 10, 750, 8, "timestamp"], [749, 19, 750, 17], [750, 10, 751, 8, "jobId"], [750, 15, 751, 13], [750, 17, 751, 15], [750, 27, 751, 25, "timestamp"], [750, 36, 751, 34], [750, 38, 751, 36], [751, 10, 752, 8, "status"], [751, 16, 752, 14], [751, 18, 752, 16], [752, 8, 753, 6], [752, 9, 753, 7], [753, 8, 755, 6, "console"], [753, 15, 755, 13], [753, 16, 755, 14, "log"], [753, 19, 755, 17], [753, 20, 755, 18], [753, 100, 755, 98], [753, 102, 755, 100], [754, 10, 756, 8, "imageUrl"], [754, 18, 756, 16], [754, 20, 756, 18, "blurredImageUrl"], [754, 35, 756, 33], [754, 36, 756, 34, "substring"], [754, 45, 756, 43], [754, 46, 756, 44], [754, 47, 756, 45], [754, 49, 756, 47], [754, 51, 756, 49], [754, 52, 756, 50], [754, 55, 756, 53], [754, 60, 756, 58], [755, 10, 757, 8, "timestamp"], [755, 19, 757, 17], [756, 10, 758, 8, "jobId"], [756, 15, 758, 13], [756, 17, 758, 15, "result"], [756, 23, 758, 21], [756, 24, 758, 22, "jobId"], [757, 8, 759, 6], [757, 9, 759, 7], [757, 10, 759, 8], [759, 8, 761, 6], [760, 8, 762, 6, "onComplete"], [760, 18, 762, 16], [760, 19, 762, 17, "result"], [760, 25, 762, 23], [760, 26, 762, 24], [761, 6, 764, 4], [761, 7, 764, 5], [761, 8, 764, 6], [761, 15, 764, 13, "error"], [761, 20, 764, 18], [761, 22, 764, 20], [762, 8, 765, 6, "console"], [762, 15, 765, 13], [762, 16, 765, 14, "error"], [762, 21, 765, 19], [762, 22, 765, 20], [762, 57, 765, 55], [762, 59, 765, 57, "error"], [762, 64, 765, 62], [762, 65, 765, 63], [763, 8, 766, 6, "setErrorMessage"], [763, 23, 766, 21], [763, 24, 766, 22], [763, 56, 766, 54], [763, 57, 766, 55], [764, 8, 767, 6, "setProcessingState"], [764, 26, 767, 24], [764, 27, 767, 25], [764, 34, 767, 32], [764, 35, 767, 33], [765, 6, 768, 4], [766, 4, 769, 2], [766, 5, 769, 3], [768, 4, 771, 2], [769, 4, 772, 2], [769, 10, 772, 8, "triggerServerProcessing"], [769, 33, 772, 31], [769, 36, 772, 34], [769, 42, 772, 34, "triggerServerProcessing"], [769, 43, 772, 41, "privateImageUrl"], [769, 58, 772, 64], [769, 60, 772, 66, "timestamp"], [769, 69, 772, 83], [769, 74, 772, 88], [770, 6, 773, 4], [770, 10, 773, 8], [771, 8, 774, 6, "console"], [771, 15, 774, 13], [771, 16, 774, 14, "log"], [771, 19, 774, 17], [771, 20, 774, 18], [771, 74, 774, 72], [771, 76, 774, 74, "privateImageUrl"], [771, 91, 774, 89], [771, 92, 774, 90], [772, 8, 775, 6, "setProcessingState"], [772, 26, 775, 24], [772, 27, 775, 25], [772, 39, 775, 37], [772, 40, 775, 38], [773, 8, 776, 6, "setProcessingProgress"], [773, 29, 776, 27], [773, 30, 776, 28], [773, 32, 776, 30], [773, 33, 776, 31], [774, 8, 778, 6], [774, 14, 778, 12, "requestBody"], [774, 25, 778, 23], [774, 28, 778, 26], [775, 10, 779, 8, "imageUrl"], [775, 18, 779, 16], [775, 20, 779, 18, "privateImageUrl"], [775, 35, 779, 33], [776, 10, 780, 8, "userId"], [776, 16, 780, 14], [777, 10, 781, 8, "requestId"], [777, 19, 781, 17], [778, 10, 782, 8, "timestamp"], [778, 19, 782, 17], [779, 10, 783, 8, "platform"], [779, 18, 783, 16], [779, 20, 783, 18], [780, 8, 784, 6], [780, 9, 784, 7], [781, 8, 786, 6, "console"], [781, 15, 786, 13], [781, 16, 786, 14, "log"], [781, 19, 786, 17], [781, 20, 786, 18], [781, 65, 786, 63], [781, 67, 786, 65, "requestBody"], [781, 78, 786, 76], [781, 79, 786, 77], [783, 8, 788, 6], [784, 8, 789, 6], [784, 14, 789, 12, "response"], [784, 22, 789, 20], [784, 25, 789, 23], [784, 31, 789, 29, "fetch"], [784, 36, 789, 34], [784, 37, 789, 35], [784, 40, 789, 38, "API_BASE_URL"], [784, 52, 789, 50], [784, 72, 789, 70], [784, 74, 789, 72], [785, 10, 790, 8, "method"], [785, 16, 790, 14], [785, 18, 790, 16], [785, 24, 790, 22], [786, 10, 791, 8, "headers"], [786, 17, 791, 15], [786, 19, 791, 17], [787, 12, 792, 10], [787, 26, 792, 24], [787, 28, 792, 26], [787, 46, 792, 44], [788, 12, 793, 10], [788, 27, 793, 25], [788, 29, 793, 27], [788, 39, 793, 37], [788, 45, 793, 43, "getAuthToken"], [788, 57, 793, 55], [788, 58, 793, 56], [788, 59, 793, 57], [789, 10, 794, 8], [789, 11, 794, 9], [790, 10, 795, 8, "body"], [790, 14, 795, 12], [790, 16, 795, 14, "JSON"], [790, 20, 795, 18], [790, 21, 795, 19, "stringify"], [790, 30, 795, 28], [790, 31, 795, 29, "requestBody"], [790, 42, 795, 40], [791, 8, 796, 6], [791, 9, 796, 7], [791, 10, 796, 8], [792, 8, 798, 6], [792, 12, 798, 10], [792, 13, 798, 11, "response"], [792, 21, 798, 19], [792, 22, 798, 20, "ok"], [792, 24, 798, 22], [792, 26, 798, 24], [793, 10, 799, 8], [793, 16, 799, 14, "errorText"], [793, 25, 799, 23], [793, 28, 799, 26], [793, 34, 799, 32, "response"], [793, 42, 799, 40], [793, 43, 799, 41, "text"], [793, 47, 799, 45], [793, 48, 799, 46], [793, 49, 799, 47], [794, 10, 800, 8, "console"], [794, 17, 800, 15], [794, 18, 800, 16, "error"], [794, 23, 800, 21], [794, 24, 800, 22], [794, 68, 800, 66], [794, 70, 800, 68, "response"], [794, 78, 800, 76], [794, 79, 800, 77, "status"], [794, 85, 800, 83], [794, 87, 800, 85, "errorText"], [794, 96, 800, 94], [794, 97, 800, 95], [795, 10, 801, 8], [795, 16, 801, 14], [795, 20, 801, 18, "Error"], [795, 25, 801, 23], [795, 26, 801, 24], [795, 48, 801, 46, "response"], [795, 56, 801, 54], [795, 57, 801, 55, "status"], [795, 63, 801, 61], [795, 67, 801, 65, "response"], [795, 75, 801, 73], [795, 76, 801, 74, "statusText"], [795, 86, 801, 84], [795, 88, 801, 86], [795, 89, 801, 87], [796, 8, 802, 6], [797, 8, 804, 6], [797, 14, 804, 12, "result"], [797, 20, 804, 18], [797, 23, 804, 21], [797, 29, 804, 27, "response"], [797, 37, 804, 35], [797, 38, 804, 36, "json"], [797, 42, 804, 40], [797, 43, 804, 41], [797, 44, 804, 42], [798, 8, 805, 6, "console"], [798, 15, 805, 13], [798, 16, 805, 14, "log"], [798, 19, 805, 17], [798, 20, 805, 18], [798, 68, 805, 66], [798, 70, 805, 68, "result"], [798, 76, 805, 74], [798, 77, 805, 75], [799, 8, 807, 6], [799, 12, 807, 10], [799, 13, 807, 11, "result"], [799, 19, 807, 17], [799, 20, 807, 18, "jobId"], [799, 25, 807, 23], [799, 27, 807, 25], [800, 10, 808, 8], [800, 16, 808, 14], [800, 20, 808, 18, "Error"], [800, 25, 808, 23], [800, 26, 808, 24], [800, 70, 808, 68], [800, 71, 808, 69], [801, 8, 809, 6], [803, 8, 811, 6], [804, 8, 812, 6], [804, 14, 812, 12, "pollForCompletion"], [804, 31, 812, 29], [804, 32, 812, 30, "result"], [804, 38, 812, 36], [804, 39, 812, 37, "jobId"], [804, 44, 812, 42], [804, 46, 812, 44, "timestamp"], [804, 55, 812, 53], [804, 56, 812, 54], [805, 6, 813, 4], [805, 7, 813, 5], [805, 8, 813, 6], [805, 15, 813, 13, "error"], [805, 20, 813, 18], [805, 22, 813, 20], [806, 8, 814, 6, "console"], [806, 15, 814, 13], [806, 16, 814, 14, "error"], [806, 21, 814, 19], [806, 22, 814, 20], [806, 57, 814, 55], [806, 59, 814, 57, "error"], [806, 64, 814, 62], [806, 65, 814, 63], [807, 8, 815, 6, "setErrorMessage"], [807, 23, 815, 21], [807, 24, 815, 22], [807, 52, 815, 50, "error"], [807, 57, 815, 55], [807, 58, 815, 56, "message"], [807, 65, 815, 63], [807, 67, 815, 65], [807, 68, 815, 66], [808, 8, 816, 6, "setProcessingState"], [808, 26, 816, 24], [808, 27, 816, 25], [808, 34, 816, 32], [808, 35, 816, 33], [809, 6, 817, 4], [810, 4, 818, 2], [810, 5, 818, 3], [811, 4, 819, 2], [812, 4, 820, 2], [812, 10, 820, 8, "pollForCompletion"], [812, 27, 820, 25], [812, 30, 820, 28], [812, 36, 820, 28, "pollForCompletion"], [812, 37, 820, 35, "jobId"], [812, 42, 820, 48], [812, 44, 820, 50, "timestamp"], [812, 53, 820, 67], [812, 55, 820, 69, "attempts"], [812, 63, 820, 77], [812, 66, 820, 80], [812, 67, 820, 81], [812, 72, 820, 86], [813, 6, 821, 4], [813, 12, 821, 10, "MAX_ATTEMPTS"], [813, 24, 821, 22], [813, 27, 821, 25], [813, 29, 821, 27], [813, 30, 821, 28], [813, 31, 821, 29], [814, 6, 822, 4], [814, 12, 822, 10, "POLL_INTERVAL"], [814, 25, 822, 23], [814, 28, 822, 26], [814, 32, 822, 30], [814, 33, 822, 31], [814, 34, 822, 32], [816, 6, 824, 4, "console"], [816, 13, 824, 11], [816, 14, 824, 12, "log"], [816, 17, 824, 15], [816, 18, 824, 16], [816, 53, 824, 51, "attempts"], [816, 61, 824, 59], [816, 64, 824, 62], [816, 65, 824, 63], [816, 69, 824, 67, "MAX_ATTEMPTS"], [816, 81, 824, 79], [816, 93, 824, 91, "jobId"], [816, 98, 824, 96], [816, 100, 824, 98], [816, 101, 824, 99], [817, 6, 826, 4], [817, 10, 826, 8, "attempts"], [817, 18, 826, 16], [817, 22, 826, 20, "MAX_ATTEMPTS"], [817, 34, 826, 32], [817, 36, 826, 34], [818, 8, 827, 6, "console"], [818, 15, 827, 13], [818, 16, 827, 14, "error"], [818, 21, 827, 19], [818, 22, 827, 20], [818, 75, 827, 73], [818, 76, 827, 74], [819, 8, 828, 6, "setErrorMessage"], [819, 23, 828, 21], [819, 24, 828, 22], [819, 63, 828, 61], [819, 64, 828, 62], [820, 8, 829, 6, "setProcessingState"], [820, 26, 829, 24], [820, 27, 829, 25], [820, 34, 829, 32], [820, 35, 829, 33], [821, 8, 830, 6], [822, 6, 831, 4], [823, 6, 833, 4], [823, 10, 833, 8], [824, 8, 834, 6], [824, 14, 834, 12, "response"], [824, 22, 834, 20], [824, 25, 834, 23], [824, 31, 834, 29, "fetch"], [824, 36, 834, 34], [824, 37, 834, 35], [824, 40, 834, 38, "API_BASE_URL"], [824, 52, 834, 50], [824, 75, 834, 73, "jobId"], [824, 80, 834, 78], [824, 82, 834, 80], [824, 84, 834, 82], [825, 10, 835, 8, "headers"], [825, 17, 835, 15], [825, 19, 835, 17], [826, 12, 836, 10], [826, 27, 836, 25], [826, 29, 836, 27], [826, 39, 836, 37], [826, 45, 836, 43, "getAuthToken"], [826, 57, 836, 55], [826, 58, 836, 56], [826, 59, 836, 57], [827, 10, 837, 8], [828, 8, 838, 6], [828, 9, 838, 7], [828, 10, 838, 8], [829, 8, 840, 6], [829, 12, 840, 10], [829, 13, 840, 11, "response"], [829, 21, 840, 19], [829, 22, 840, 20, "ok"], [829, 24, 840, 22], [829, 26, 840, 24], [830, 10, 841, 8], [830, 16, 841, 14], [830, 20, 841, 18, "Error"], [830, 25, 841, 23], [830, 26, 841, 24], [830, 34, 841, 32, "response"], [830, 42, 841, 40], [830, 43, 841, 41, "status"], [830, 49, 841, 47], [830, 54, 841, 52, "response"], [830, 62, 841, 60], [830, 63, 841, 61, "statusText"], [830, 73, 841, 71], [830, 75, 841, 73], [830, 76, 841, 74], [831, 8, 842, 6], [832, 8, 844, 6], [832, 14, 844, 12, "status"], [832, 20, 844, 18], [832, 23, 844, 21], [832, 29, 844, 27, "response"], [832, 37, 844, 35], [832, 38, 844, 36, "json"], [832, 42, 844, 40], [832, 43, 844, 41], [832, 44, 844, 42], [833, 8, 845, 6, "console"], [833, 15, 845, 13], [833, 16, 845, 14, "log"], [833, 19, 845, 17], [833, 20, 845, 18], [833, 54, 845, 52], [833, 56, 845, 54, "status"], [833, 62, 845, 60], [833, 63, 845, 61], [834, 8, 847, 6], [834, 12, 847, 10, "status"], [834, 18, 847, 16], [834, 19, 847, 17, "status"], [834, 25, 847, 23], [834, 30, 847, 28], [834, 41, 847, 39], [834, 43, 847, 41], [835, 10, 848, 8, "console"], [835, 17, 848, 15], [835, 18, 848, 16, "log"], [835, 21, 848, 19], [835, 22, 848, 20], [835, 73, 848, 71], [835, 74, 848, 72], [836, 10, 849, 8, "setProcessingProgress"], [836, 31, 849, 29], [836, 32, 849, 30], [836, 35, 849, 33], [836, 36, 849, 34], [837, 10, 850, 8, "setProcessingState"], [837, 28, 850, 26], [837, 29, 850, 27], [837, 40, 850, 38], [837, 41, 850, 39], [838, 10, 851, 8], [839, 10, 852, 8], [839, 16, 852, 14, "result"], [839, 22, 852, 20], [839, 25, 852, 23], [840, 12, 853, 10, "imageUrl"], [840, 20, 853, 18], [840, 22, 853, 20, "status"], [840, 28, 853, 26], [840, 29, 853, 27, "publicUrl"], [840, 38, 853, 36], [841, 12, 853, 38], [842, 12, 854, 10, "localUri"], [842, 20, 854, 18], [842, 22, 854, 20, "capturedPhoto"], [842, 35, 854, 33], [842, 39, 854, 37, "status"], [842, 45, 854, 43], [842, 46, 854, 44, "publicUrl"], [842, 55, 854, 53], [843, 12, 854, 55], [844, 12, 855, 10, "challengeCode"], [844, 25, 855, 23], [844, 27, 855, 25, "challengeCode"], [844, 40, 855, 38], [844, 44, 855, 42], [844, 46, 855, 44], [845, 12, 856, 10, "timestamp"], [845, 21, 856, 19], [846, 12, 857, 10, "processingStatus"], [846, 28, 857, 26], [846, 30, 857, 28], [847, 10, 858, 8], [847, 11, 858, 9], [848, 10, 859, 8, "console"], [848, 17, 859, 15], [848, 18, 859, 16, "log"], [848, 21, 859, 19], [848, 22, 859, 20], [848, 57, 859, 55], [848, 59, 859, 57, "result"], [848, 65, 859, 63], [848, 66, 859, 64], [849, 10, 860, 8, "onComplete"], [849, 20, 860, 18], [849, 21, 860, 19, "result"], [849, 27, 860, 25], [849, 28, 860, 26], [850, 10, 861, 8], [851, 8, 862, 6], [851, 9, 862, 7], [851, 15, 862, 13], [851, 19, 862, 17, "status"], [851, 25, 862, 23], [851, 26, 862, 24, "status"], [851, 32, 862, 30], [851, 37, 862, 35], [851, 45, 862, 43], [851, 47, 862, 45], [852, 10, 863, 8, "console"], [852, 17, 863, 15], [852, 18, 863, 16, "error"], [852, 23, 863, 21], [852, 24, 863, 22], [852, 60, 863, 58], [852, 62, 863, 60, "status"], [852, 68, 863, 66], [852, 69, 863, 67, "error"], [852, 74, 863, 72], [852, 75, 863, 73], [853, 10, 864, 8], [853, 16, 864, 14], [853, 20, 864, 18, "Error"], [853, 25, 864, 23], [853, 26, 864, 24, "status"], [853, 32, 864, 30], [853, 33, 864, 31, "error"], [853, 38, 864, 36], [853, 42, 864, 40], [853, 61, 864, 59], [853, 62, 864, 60], [854, 8, 865, 6], [854, 9, 865, 7], [854, 15, 865, 13], [855, 10, 866, 8], [856, 10, 867, 8], [856, 16, 867, 14, "progressValue"], [856, 29, 867, 27], [856, 32, 867, 30], [856, 34, 867, 32], [856, 37, 867, 36, "attempts"], [856, 45, 867, 44], [856, 48, 867, 47, "MAX_ATTEMPTS"], [856, 60, 867, 59], [856, 63, 867, 63], [856, 65, 867, 65], [857, 10, 868, 8, "console"], [857, 17, 868, 15], [857, 18, 868, 16, "log"], [857, 21, 868, 19], [857, 22, 868, 20], [857, 71, 868, 69, "progressValue"], [857, 84, 868, 82], [857, 87, 868, 85], [857, 88, 868, 86], [858, 10, 869, 8, "setProcessingProgress"], [858, 31, 869, 29], [858, 32, 869, 30, "progressValue"], [858, 45, 869, 43], [858, 46, 869, 44], [859, 10, 871, 8, "setTimeout"], [859, 20, 871, 18], [859, 21, 871, 19], [859, 27, 871, 25], [860, 12, 872, 10, "pollForCompletion"], [860, 29, 872, 27], [860, 30, 872, 28, "jobId"], [860, 35, 872, 33], [860, 37, 872, 35, "timestamp"], [860, 46, 872, 44], [860, 48, 872, 46, "attempts"], [860, 56, 872, 54], [860, 59, 872, 57], [860, 60, 872, 58], [860, 61, 872, 59], [861, 10, 873, 8], [861, 11, 873, 9], [861, 13, 873, 11, "POLL_INTERVAL"], [861, 26, 873, 24], [861, 27, 873, 25], [862, 8, 874, 6], [863, 6, 875, 4], [863, 7, 875, 5], [863, 8, 875, 6], [863, 15, 875, 13, "error"], [863, 20, 875, 18], [863, 22, 875, 20], [864, 8, 876, 6, "console"], [864, 15, 876, 13], [864, 16, 876, 14, "error"], [864, 21, 876, 19], [864, 22, 876, 20], [864, 54, 876, 52], [864, 56, 876, 54, "error"], [864, 61, 876, 59], [864, 62, 876, 60], [865, 8, 877, 6, "setErrorMessage"], [865, 23, 877, 21], [865, 24, 877, 22], [865, 62, 877, 60, "error"], [865, 67, 877, 65], [865, 68, 877, 66, "message"], [865, 75, 877, 73], [865, 77, 877, 75], [865, 78, 877, 76], [866, 8, 878, 6, "setProcessingState"], [866, 26, 878, 24], [866, 27, 878, 25], [866, 34, 878, 32], [866, 35, 878, 33], [867, 6, 879, 4], [868, 4, 880, 2], [868, 5, 880, 3], [869, 4, 881, 2], [870, 4, 882, 2], [870, 10, 882, 8, "getAuthToken"], [870, 22, 882, 20], [870, 25, 882, 23], [870, 31, 882, 23, "getAuthToken"], [870, 32, 882, 23], [870, 37, 882, 52], [871, 6, 883, 4], [872, 6, 884, 4], [873, 6, 885, 4], [873, 13, 885, 11], [873, 30, 885, 28], [874, 4, 886, 2], [874, 5, 886, 3], [876, 4, 888, 2], [877, 4, 889, 2], [877, 10, 889, 8, "retryCapture"], [877, 22, 889, 20], [877, 25, 889, 23], [877, 29, 889, 23, "useCallback"], [877, 47, 889, 34], [877, 49, 889, 35], [877, 55, 889, 41], [878, 6, 890, 4, "console"], [878, 13, 890, 11], [878, 14, 890, 12, "log"], [878, 17, 890, 15], [878, 18, 890, 16], [878, 55, 890, 53], [878, 56, 890, 54], [879, 6, 891, 4, "setProcessingState"], [879, 24, 891, 22], [879, 25, 891, 23], [879, 31, 891, 29], [879, 32, 891, 30], [880, 6, 892, 4, "setErrorMessage"], [880, 21, 892, 19], [880, 22, 892, 20], [880, 24, 892, 22], [880, 25, 892, 23], [881, 6, 893, 4, "setCapturedPhoto"], [881, 22, 893, 20], [881, 23, 893, 21], [881, 25, 893, 23], [881, 26, 893, 24], [882, 6, 894, 4, "setProcessingProgress"], [882, 27, 894, 25], [882, 28, 894, 26], [882, 29, 894, 27], [882, 30, 894, 28], [883, 4, 895, 2], [883, 5, 895, 3], [883, 7, 895, 5], [883, 9, 895, 7], [883, 10, 895, 8], [884, 4, 896, 2], [885, 4, 897, 2], [885, 8, 897, 2, "useEffect"], [885, 24, 897, 11], [885, 26, 897, 12], [885, 32, 897, 18], [886, 6, 898, 4, "console"], [886, 13, 898, 11], [886, 14, 898, 12, "log"], [886, 17, 898, 15], [886, 18, 898, 16], [886, 53, 898, 51], [886, 55, 898, 53, "permission"], [886, 65, 898, 63], [886, 66, 898, 64], [887, 6, 899, 4], [887, 10, 899, 8, "permission"], [887, 20, 899, 18], [887, 22, 899, 20], [888, 8, 900, 6, "console"], [888, 15, 900, 13], [888, 16, 900, 14, "log"], [888, 19, 900, 17], [888, 20, 900, 18], [888, 57, 900, 55], [888, 59, 900, 57, "permission"], [888, 69, 900, 67], [888, 70, 900, 68, "granted"], [888, 77, 900, 75], [888, 78, 900, 76], [889, 6, 901, 4], [890, 4, 902, 2], [890, 5, 902, 3], [890, 7, 902, 5], [890, 8, 902, 6, "permission"], [890, 18, 902, 16], [890, 19, 902, 17], [890, 20, 902, 18], [891, 4, 903, 2], [892, 4, 904, 2], [892, 8, 904, 6], [892, 9, 904, 7, "permission"], [892, 19, 904, 17], [892, 21, 904, 19], [893, 6, 905, 4, "console"], [893, 13, 905, 11], [893, 14, 905, 12, "log"], [893, 17, 905, 15], [893, 18, 905, 16], [893, 67, 905, 65], [893, 68, 905, 66], [894, 6, 906, 4], [894, 26, 907, 6], [894, 30, 907, 6, "_jsxDevRuntime"], [894, 44, 907, 6], [894, 45, 907, 6, "jsxDEV"], [894, 51, 907, 6], [894, 53, 907, 7, "_View"], [894, 58, 907, 7], [894, 59, 907, 7, "default"], [894, 66, 907, 11], [895, 8, 907, 12, "style"], [895, 13, 907, 17], [895, 15, 907, 19, "styles"], [895, 21, 907, 25], [895, 22, 907, 26, "container"], [895, 31, 907, 36], [896, 8, 907, 36, "children"], [896, 16, 907, 36], [896, 32, 908, 8], [896, 36, 908, 8, "_jsxDevRuntime"], [896, 50, 908, 8], [896, 51, 908, 8, "jsxDEV"], [896, 57, 908, 8], [896, 59, 908, 9, "_ActivityIndicator"], [896, 77, 908, 9], [896, 78, 908, 9, "default"], [896, 85, 908, 26], [897, 10, 908, 27, "size"], [897, 14, 908, 31], [897, 16, 908, 32], [897, 23, 908, 39], [898, 10, 908, 40, "color"], [898, 15, 908, 45], [898, 17, 908, 46], [899, 8, 908, 55], [900, 10, 908, 55, "fileName"], [900, 18, 908, 55], [900, 20, 908, 55, "_jsxFileName"], [900, 32, 908, 55], [901, 10, 908, 55, "lineNumber"], [901, 20, 908, 55], [902, 10, 908, 55, "columnNumber"], [902, 22, 908, 55], [903, 8, 908, 55], [903, 15, 908, 57], [903, 16, 908, 58], [903, 31, 909, 8], [903, 35, 909, 8, "_jsxDevRuntime"], [903, 49, 909, 8], [903, 50, 909, 8, "jsxDEV"], [903, 56, 909, 8], [903, 58, 909, 9, "_Text"], [903, 63, 909, 9], [903, 64, 909, 9, "default"], [903, 71, 909, 13], [904, 10, 909, 14, "style"], [904, 15, 909, 19], [904, 17, 909, 21, "styles"], [904, 23, 909, 27], [904, 24, 909, 28, "loadingText"], [904, 35, 909, 40], [905, 10, 909, 40, "children"], [905, 18, 909, 40], [905, 20, 909, 41], [906, 8, 909, 58], [907, 10, 909, 58, "fileName"], [907, 18, 909, 58], [907, 20, 909, 58, "_jsxFileName"], [907, 32, 909, 58], [908, 10, 909, 58, "lineNumber"], [908, 20, 909, 58], [909, 10, 909, 58, "columnNumber"], [909, 22, 909, 58], [910, 8, 909, 58], [910, 15, 909, 64], [910, 16, 909, 65], [911, 6, 909, 65], [912, 8, 909, 65, "fileName"], [912, 16, 909, 65], [912, 18, 909, 65, "_jsxFileName"], [912, 30, 909, 65], [913, 8, 909, 65, "lineNumber"], [913, 18, 909, 65], [914, 8, 909, 65, "columnNumber"], [914, 20, 909, 65], [915, 6, 909, 65], [915, 13, 910, 12], [915, 14, 910, 13], [916, 4, 912, 2], [917, 4, 913, 2], [917, 8, 913, 6], [917, 9, 913, 7, "permission"], [917, 19, 913, 17], [917, 20, 913, 18, "granted"], [917, 27, 913, 25], [917, 29, 913, 27], [918, 6, 914, 4, "console"], [918, 13, 914, 11], [918, 14, 914, 12, "log"], [918, 17, 914, 15], [918, 18, 914, 16], [918, 93, 914, 91], [918, 94, 914, 92], [919, 6, 915, 4], [919, 26, 916, 6], [919, 30, 916, 6, "_jsxDevRuntime"], [919, 44, 916, 6], [919, 45, 916, 6, "jsxDEV"], [919, 51, 916, 6], [919, 53, 916, 7, "_View"], [919, 58, 916, 7], [919, 59, 916, 7, "default"], [919, 66, 916, 11], [920, 8, 916, 12, "style"], [920, 13, 916, 17], [920, 15, 916, 19, "styles"], [920, 21, 916, 25], [920, 22, 916, 26, "container"], [920, 31, 916, 36], [921, 8, 916, 36, "children"], [921, 16, 916, 36], [921, 31, 917, 8], [921, 35, 917, 8, "_jsxDevRuntime"], [921, 49, 917, 8], [921, 50, 917, 8, "jsxDEV"], [921, 56, 917, 8], [921, 58, 917, 9, "_View"], [921, 63, 917, 9], [921, 64, 917, 9, "default"], [921, 71, 917, 13], [922, 10, 917, 14, "style"], [922, 15, 917, 19], [922, 17, 917, 21, "styles"], [922, 23, 917, 27], [922, 24, 917, 28, "permissionContent"], [922, 41, 917, 46], [923, 10, 917, 46, "children"], [923, 18, 917, 46], [923, 34, 918, 10], [923, 38, 918, 10, "_jsxDevRuntime"], [923, 52, 918, 10], [923, 53, 918, 10, "jsxDEV"], [923, 59, 918, 10], [923, 61, 918, 11, "_lucideReactNative"], [923, 79, 918, 11], [923, 80, 918, 11, "Camera"], [923, 86, 918, 21], [924, 12, 918, 22, "size"], [924, 16, 918, 26], [924, 18, 918, 28], [924, 20, 918, 31], [925, 12, 918, 32, "color"], [925, 17, 918, 37], [925, 19, 918, 38], [926, 10, 918, 47], [927, 12, 918, 47, "fileName"], [927, 20, 918, 47], [927, 22, 918, 47, "_jsxFileName"], [927, 34, 918, 47], [928, 12, 918, 47, "lineNumber"], [928, 22, 918, 47], [929, 12, 918, 47, "columnNumber"], [929, 24, 918, 47], [930, 10, 918, 47], [930, 17, 918, 49], [930, 18, 918, 50], [930, 33, 919, 10], [930, 37, 919, 10, "_jsxDevRuntime"], [930, 51, 919, 10], [930, 52, 919, 10, "jsxDEV"], [930, 58, 919, 10], [930, 60, 919, 11, "_Text"], [930, 65, 919, 11], [930, 66, 919, 11, "default"], [930, 73, 919, 15], [931, 12, 919, 16, "style"], [931, 17, 919, 21], [931, 19, 919, 23, "styles"], [931, 25, 919, 29], [931, 26, 919, 30, "permissionTitle"], [931, 41, 919, 46], [932, 12, 919, 46, "children"], [932, 20, 919, 46], [932, 22, 919, 47], [933, 10, 919, 73], [934, 12, 919, 73, "fileName"], [934, 20, 919, 73], [934, 22, 919, 73, "_jsxFileName"], [934, 34, 919, 73], [935, 12, 919, 73, "lineNumber"], [935, 22, 919, 73], [936, 12, 919, 73, "columnNumber"], [936, 24, 919, 73], [937, 10, 919, 73], [937, 17, 919, 79], [937, 18, 919, 80], [937, 33, 920, 10], [937, 37, 920, 10, "_jsxDevRuntime"], [937, 51, 920, 10], [937, 52, 920, 10, "jsxDEV"], [937, 58, 920, 10], [937, 60, 920, 11, "_Text"], [937, 65, 920, 11], [937, 66, 920, 11, "default"], [937, 73, 920, 15], [938, 12, 920, 16, "style"], [938, 17, 920, 21], [938, 19, 920, 23, "styles"], [938, 25, 920, 29], [938, 26, 920, 30, "permissionDescription"], [938, 47, 920, 52], [939, 12, 920, 52, "children"], [939, 20, 920, 52], [939, 22, 920, 53], [940, 10, 923, 10], [941, 12, 923, 10, "fileName"], [941, 20, 923, 10], [941, 22, 923, 10, "_jsxFileName"], [941, 34, 923, 10], [942, 12, 923, 10, "lineNumber"], [942, 22, 923, 10], [943, 12, 923, 10, "columnNumber"], [943, 24, 923, 10], [944, 10, 923, 10], [944, 17, 923, 16], [944, 18, 923, 17], [944, 33, 924, 10], [944, 37, 924, 10, "_jsxDevRuntime"], [944, 51, 924, 10], [944, 52, 924, 10, "jsxDEV"], [944, 58, 924, 10], [944, 60, 924, 11, "_TouchableOpacity"], [944, 77, 924, 11], [944, 78, 924, 11, "default"], [944, 85, 924, 27], [945, 12, 924, 28, "onPress"], [945, 19, 924, 35], [945, 21, 924, 37, "requestPermission"], [945, 38, 924, 55], [946, 12, 924, 56, "style"], [946, 17, 924, 61], [946, 19, 924, 63, "styles"], [946, 25, 924, 69], [946, 26, 924, 70, "primaryButton"], [946, 39, 924, 84], [947, 12, 924, 84, "children"], [947, 20, 924, 84], [947, 35, 925, 12], [947, 39, 925, 12, "_jsxDevRuntime"], [947, 53, 925, 12], [947, 54, 925, 12, "jsxDEV"], [947, 60, 925, 12], [947, 62, 925, 13, "_Text"], [947, 67, 925, 13], [947, 68, 925, 13, "default"], [947, 75, 925, 17], [948, 14, 925, 18, "style"], [948, 19, 925, 23], [948, 21, 925, 25, "styles"], [948, 27, 925, 31], [948, 28, 925, 32, "primaryButtonText"], [948, 45, 925, 50], [949, 14, 925, 50, "children"], [949, 22, 925, 50], [949, 24, 925, 51], [950, 12, 925, 67], [951, 14, 925, 67, "fileName"], [951, 22, 925, 67], [951, 24, 925, 67, "_jsxFileName"], [951, 36, 925, 67], [952, 14, 925, 67, "lineNumber"], [952, 24, 925, 67], [953, 14, 925, 67, "columnNumber"], [953, 26, 925, 67], [954, 12, 925, 67], [954, 19, 925, 73], [955, 10, 925, 74], [956, 12, 925, 74, "fileName"], [956, 20, 925, 74], [956, 22, 925, 74, "_jsxFileName"], [956, 34, 925, 74], [957, 12, 925, 74, "lineNumber"], [957, 22, 925, 74], [958, 12, 925, 74, "columnNumber"], [958, 24, 925, 74], [959, 10, 925, 74], [959, 17, 926, 28], [959, 18, 926, 29], [959, 33, 927, 10], [959, 37, 927, 10, "_jsxDevRuntime"], [959, 51, 927, 10], [959, 52, 927, 10, "jsxDEV"], [959, 58, 927, 10], [959, 60, 927, 11, "_TouchableOpacity"], [959, 77, 927, 11], [959, 78, 927, 11, "default"], [959, 85, 927, 27], [960, 12, 927, 28, "onPress"], [960, 19, 927, 35], [960, 21, 927, 37, "onCancel"], [960, 29, 927, 46], [961, 12, 927, 47, "style"], [961, 17, 927, 52], [961, 19, 927, 54, "styles"], [961, 25, 927, 60], [961, 26, 927, 61, "secondaryButton"], [961, 41, 927, 77], [962, 12, 927, 77, "children"], [962, 20, 927, 77], [962, 35, 928, 12], [962, 39, 928, 12, "_jsxDevRuntime"], [962, 53, 928, 12], [962, 54, 928, 12, "jsxDEV"], [962, 60, 928, 12], [962, 62, 928, 13, "_Text"], [962, 67, 928, 13], [962, 68, 928, 13, "default"], [962, 75, 928, 17], [963, 14, 928, 18, "style"], [963, 19, 928, 23], [963, 21, 928, 25, "styles"], [963, 27, 928, 31], [963, 28, 928, 32, "secondaryButtonText"], [963, 47, 928, 52], [964, 14, 928, 52, "children"], [964, 22, 928, 52], [964, 24, 928, 53], [965, 12, 928, 59], [966, 14, 928, 59, "fileName"], [966, 22, 928, 59], [966, 24, 928, 59, "_jsxFileName"], [966, 36, 928, 59], [967, 14, 928, 59, "lineNumber"], [967, 24, 928, 59], [968, 14, 928, 59, "columnNumber"], [968, 26, 928, 59], [969, 12, 928, 59], [969, 19, 928, 65], [970, 10, 928, 66], [971, 12, 928, 66, "fileName"], [971, 20, 928, 66], [971, 22, 928, 66, "_jsxFileName"], [971, 34, 928, 66], [972, 12, 928, 66, "lineNumber"], [972, 22, 928, 66], [973, 12, 928, 66, "columnNumber"], [973, 24, 928, 66], [974, 10, 928, 66], [974, 17, 929, 28], [974, 18, 929, 29], [975, 8, 929, 29], [976, 10, 929, 29, "fileName"], [976, 18, 929, 29], [976, 20, 929, 29, "_jsxFileName"], [976, 32, 929, 29], [977, 10, 929, 29, "lineNumber"], [977, 20, 929, 29], [978, 10, 929, 29, "columnNumber"], [978, 22, 929, 29], [979, 8, 929, 29], [979, 15, 930, 14], [980, 6, 930, 15], [981, 8, 930, 15, "fileName"], [981, 16, 930, 15], [981, 18, 930, 15, "_jsxFileName"], [981, 30, 930, 15], [982, 8, 930, 15, "lineNumber"], [982, 18, 930, 15], [983, 8, 930, 15, "columnNumber"], [983, 20, 930, 15], [984, 6, 930, 15], [984, 13, 931, 12], [984, 14, 931, 13], [985, 4, 933, 2], [986, 4, 934, 2], [987, 4, 935, 2, "console"], [987, 11, 935, 9], [987, 12, 935, 10, "log"], [987, 15, 935, 13], [987, 16, 935, 14], [987, 55, 935, 53], [987, 56, 935, 54], [988, 4, 937, 2], [988, 24, 938, 4], [988, 28, 938, 4, "_jsxDevRuntime"], [988, 42, 938, 4], [988, 43, 938, 4, "jsxDEV"], [988, 49, 938, 4], [988, 51, 938, 5, "_View"], [988, 56, 938, 5], [988, 57, 938, 5, "default"], [988, 64, 938, 9], [989, 6, 938, 10, "style"], [989, 11, 938, 15], [989, 13, 938, 17, "styles"], [989, 19, 938, 23], [989, 20, 938, 24, "container"], [989, 29, 938, 34], [990, 6, 938, 34, "children"], [990, 14, 938, 34], [990, 30, 940, 6], [990, 34, 940, 6, "_jsxDevRuntime"], [990, 48, 940, 6], [990, 49, 940, 6, "jsxDEV"], [990, 55, 940, 6], [990, 57, 940, 7, "_View"], [990, 62, 940, 7], [990, 63, 940, 7, "default"], [990, 70, 940, 11], [991, 8, 940, 12, "style"], [991, 13, 940, 17], [991, 15, 940, 19, "styles"], [991, 21, 940, 25], [991, 22, 940, 26, "cameraContainer"], [991, 37, 940, 42], [992, 8, 940, 43, "id"], [992, 10, 940, 45], [992, 12, 940, 46], [992, 29, 940, 63], [993, 8, 940, 63, "children"], [993, 16, 940, 63], [993, 32, 941, 8], [993, 36, 941, 8, "_jsxDevRuntime"], [993, 50, 941, 8], [993, 51, 941, 8, "jsxDEV"], [993, 57, 941, 8], [993, 59, 941, 9, "_expoCamera"], [993, 70, 941, 9], [993, 71, 941, 9, "CameraView"], [993, 81, 941, 19], [994, 10, 942, 10, "ref"], [994, 13, 942, 13], [994, 15, 942, 15, "cameraRef"], [994, 24, 942, 25], [995, 10, 943, 10, "style"], [995, 15, 943, 15], [995, 17, 943, 17], [995, 18, 943, 18, "styles"], [995, 24, 943, 24], [995, 25, 943, 25, "camera"], [995, 31, 943, 31], [995, 33, 943, 33], [996, 12, 943, 35, "backgroundColor"], [996, 27, 943, 50], [996, 29, 943, 52], [997, 10, 943, 62], [997, 11, 943, 63], [997, 12, 943, 65], [998, 10, 944, 10, "facing"], [998, 16, 944, 16], [998, 18, 944, 17], [998, 24, 944, 23], [999, 10, 945, 10, "onLayout"], [999, 18, 945, 18], [999, 20, 945, 21, "e"], [999, 21, 945, 22], [999, 25, 945, 27], [1000, 12, 946, 12, "console"], [1000, 19, 946, 19], [1000, 20, 946, 20, "log"], [1000, 23, 946, 23], [1000, 24, 946, 24], [1000, 56, 946, 56], [1000, 58, 946, 58, "e"], [1000, 59, 946, 59], [1000, 60, 946, 60, "nativeEvent"], [1000, 71, 946, 71], [1000, 72, 946, 72, "layout"], [1000, 78, 946, 78], [1000, 79, 946, 79], [1001, 12, 947, 12, "setViewSize"], [1001, 23, 947, 23], [1001, 24, 947, 24], [1002, 14, 947, 26, "width"], [1002, 19, 947, 31], [1002, 21, 947, 33, "e"], [1002, 22, 947, 34], [1002, 23, 947, 35, "nativeEvent"], [1002, 34, 947, 46], [1002, 35, 947, 47, "layout"], [1002, 41, 947, 53], [1002, 42, 947, 54, "width"], [1002, 47, 947, 59], [1003, 14, 947, 61, "height"], [1003, 20, 947, 67], [1003, 22, 947, 69, "e"], [1003, 23, 947, 70], [1003, 24, 947, 71, "nativeEvent"], [1003, 35, 947, 82], [1003, 36, 947, 83, "layout"], [1003, 42, 947, 89], [1003, 43, 947, 90, "height"], [1004, 12, 947, 97], [1004, 13, 947, 98], [1004, 14, 947, 99], [1005, 10, 948, 10], [1005, 11, 948, 12], [1006, 10, 949, 10, "onCameraReady"], [1006, 23, 949, 23], [1006, 25, 949, 25, "onCameraReady"], [1006, 26, 949, 25], [1006, 31, 949, 31], [1007, 12, 950, 12, "console"], [1007, 19, 950, 19], [1007, 20, 950, 20, "log"], [1007, 23, 950, 23], [1007, 24, 950, 24], [1007, 55, 950, 55], [1007, 56, 950, 56], [1008, 12, 951, 12, "setIsCameraReady"], [1008, 28, 951, 28], [1008, 29, 951, 29], [1008, 33, 951, 33], [1008, 34, 951, 34], [1008, 35, 951, 35], [1008, 36, 951, 36], [1009, 10, 952, 10], [1009, 11, 952, 12], [1010, 10, 953, 10, "onMountError"], [1010, 22, 953, 22], [1010, 24, 953, 25, "error"], [1010, 29, 953, 30], [1010, 33, 953, 35], [1011, 12, 954, 12, "console"], [1011, 19, 954, 19], [1011, 20, 954, 20, "error"], [1011, 25, 954, 25], [1011, 26, 954, 26], [1011, 63, 954, 63], [1011, 65, 954, 65, "error"], [1011, 70, 954, 70], [1011, 71, 954, 71], [1012, 12, 955, 12, "setErrorMessage"], [1012, 27, 955, 27], [1012, 28, 955, 28], [1012, 57, 955, 57], [1012, 58, 955, 58], [1013, 12, 956, 12, "setProcessingState"], [1013, 30, 956, 30], [1013, 31, 956, 31], [1013, 38, 956, 38], [1013, 39, 956, 39], [1014, 10, 957, 10], [1015, 8, 957, 12], [1016, 10, 957, 12, "fileName"], [1016, 18, 957, 12], [1016, 20, 957, 12, "_jsxFileName"], [1016, 32, 957, 12], [1017, 10, 957, 12, "lineNumber"], [1017, 20, 957, 12], [1018, 10, 957, 12, "columnNumber"], [1018, 22, 957, 12], [1019, 8, 957, 12], [1019, 15, 958, 9], [1019, 16, 958, 10], [1019, 18, 960, 9], [1019, 19, 960, 10, "isCameraReady"], [1019, 32, 960, 23], [1019, 49, 961, 10], [1019, 53, 961, 10, "_jsxDevRuntime"], [1019, 67, 961, 10], [1019, 68, 961, 10, "jsxDEV"], [1019, 74, 961, 10], [1019, 76, 961, 11, "_View"], [1019, 81, 961, 11], [1019, 82, 961, 11, "default"], [1019, 89, 961, 15], [1020, 10, 961, 16, "style"], [1020, 15, 961, 21], [1020, 17, 961, 23], [1020, 18, 961, 24, "StyleSheet"], [1020, 37, 961, 34], [1020, 38, 961, 35, "absoluteFill"], [1020, 50, 961, 47], [1020, 52, 961, 49], [1021, 12, 961, 51, "backgroundColor"], [1021, 27, 961, 66], [1021, 29, 961, 68], [1021, 49, 961, 88], [1022, 12, 961, 90, "justifyContent"], [1022, 26, 961, 104], [1022, 28, 961, 106], [1022, 36, 961, 114], [1023, 12, 961, 116, "alignItems"], [1023, 22, 961, 126], [1023, 24, 961, 128], [1023, 32, 961, 136], [1024, 12, 961, 138, "zIndex"], [1024, 18, 961, 144], [1024, 20, 961, 146], [1025, 10, 961, 151], [1025, 11, 961, 152], [1025, 12, 961, 154], [1026, 10, 961, 154, "children"], [1026, 18, 961, 154], [1026, 33, 962, 12], [1026, 37, 962, 12, "_jsxDevRuntime"], [1026, 51, 962, 12], [1026, 52, 962, 12, "jsxDEV"], [1026, 58, 962, 12], [1026, 60, 962, 13, "_View"], [1026, 65, 962, 13], [1026, 66, 962, 13, "default"], [1026, 73, 962, 17], [1027, 12, 962, 18, "style"], [1027, 17, 962, 23], [1027, 19, 962, 25], [1028, 14, 962, 27, "backgroundColor"], [1028, 29, 962, 42], [1028, 31, 962, 44], [1028, 51, 962, 64], [1029, 14, 962, 66, "padding"], [1029, 21, 962, 73], [1029, 23, 962, 75], [1029, 25, 962, 77], [1030, 14, 962, 79, "borderRadius"], [1030, 26, 962, 91], [1030, 28, 962, 93], [1030, 30, 962, 95], [1031, 14, 962, 97, "alignItems"], [1031, 24, 962, 107], [1031, 26, 962, 109], [1032, 12, 962, 118], [1032, 13, 962, 120], [1033, 12, 962, 120, "children"], [1033, 20, 962, 120], [1033, 36, 963, 14], [1033, 40, 963, 14, "_jsxDevRuntime"], [1033, 54, 963, 14], [1033, 55, 963, 14, "jsxDEV"], [1033, 61, 963, 14], [1033, 63, 963, 15, "_ActivityIndicator"], [1033, 81, 963, 15], [1033, 82, 963, 15, "default"], [1033, 89, 963, 32], [1034, 14, 963, 33, "size"], [1034, 18, 963, 37], [1034, 20, 963, 38], [1034, 27, 963, 45], [1035, 14, 963, 46, "color"], [1035, 19, 963, 51], [1035, 21, 963, 52], [1035, 30, 963, 61], [1036, 14, 963, 62, "style"], [1036, 19, 963, 67], [1036, 21, 963, 69], [1037, 16, 963, 71, "marginBottom"], [1037, 28, 963, 83], [1037, 30, 963, 85], [1038, 14, 963, 88], [1039, 12, 963, 90], [1040, 14, 963, 90, "fileName"], [1040, 22, 963, 90], [1040, 24, 963, 90, "_jsxFileName"], [1040, 36, 963, 90], [1041, 14, 963, 90, "lineNumber"], [1041, 24, 963, 90], [1042, 14, 963, 90, "columnNumber"], [1042, 26, 963, 90], [1043, 12, 963, 90], [1043, 19, 963, 92], [1043, 20, 963, 93], [1043, 35, 964, 14], [1043, 39, 964, 14, "_jsxDevRuntime"], [1043, 53, 964, 14], [1043, 54, 964, 14, "jsxDEV"], [1043, 60, 964, 14], [1043, 62, 964, 15, "_Text"], [1043, 67, 964, 15], [1043, 68, 964, 15, "default"], [1043, 75, 964, 19], [1044, 14, 964, 20, "style"], [1044, 19, 964, 25], [1044, 21, 964, 27], [1045, 16, 964, 29, "color"], [1045, 21, 964, 34], [1045, 23, 964, 36], [1045, 29, 964, 42], [1046, 16, 964, 44, "fontSize"], [1046, 24, 964, 52], [1046, 26, 964, 54], [1046, 28, 964, 56], [1047, 16, 964, 58, "fontWeight"], [1047, 26, 964, 68], [1047, 28, 964, 70], [1048, 14, 964, 76], [1048, 15, 964, 78], [1049, 14, 964, 78, "children"], [1049, 22, 964, 78], [1049, 24, 964, 79], [1050, 12, 964, 101], [1051, 14, 964, 101, "fileName"], [1051, 22, 964, 101], [1051, 24, 964, 101, "_jsxFileName"], [1051, 36, 964, 101], [1052, 14, 964, 101, "lineNumber"], [1052, 24, 964, 101], [1053, 14, 964, 101, "columnNumber"], [1053, 26, 964, 101], [1054, 12, 964, 101], [1054, 19, 964, 107], [1054, 20, 964, 108], [1054, 35, 965, 14], [1054, 39, 965, 14, "_jsxDevRuntime"], [1054, 53, 965, 14], [1054, 54, 965, 14, "jsxDEV"], [1054, 60, 965, 14], [1054, 62, 965, 15, "_Text"], [1054, 67, 965, 15], [1054, 68, 965, 15, "default"], [1054, 75, 965, 19], [1055, 14, 965, 20, "style"], [1055, 19, 965, 25], [1055, 21, 965, 27], [1056, 16, 965, 29, "color"], [1056, 21, 965, 34], [1056, 23, 965, 36], [1056, 32, 965, 45], [1057, 16, 965, 47, "fontSize"], [1057, 24, 965, 55], [1057, 26, 965, 57], [1057, 28, 965, 59], [1058, 16, 965, 61, "marginTop"], [1058, 25, 965, 70], [1058, 27, 965, 72], [1059, 14, 965, 74], [1059, 15, 965, 76], [1060, 14, 965, 76, "children"], [1060, 22, 965, 76], [1060, 24, 965, 77], [1061, 12, 965, 88], [1062, 14, 965, 88, "fileName"], [1062, 22, 965, 88], [1062, 24, 965, 88, "_jsxFileName"], [1062, 36, 965, 88], [1063, 14, 965, 88, "lineNumber"], [1063, 24, 965, 88], [1064, 14, 965, 88, "columnNumber"], [1064, 26, 965, 88], [1065, 12, 965, 88], [1065, 19, 965, 94], [1065, 20, 965, 95], [1066, 10, 965, 95], [1067, 12, 965, 95, "fileName"], [1067, 20, 965, 95], [1067, 22, 965, 95, "_jsxFileName"], [1067, 34, 965, 95], [1068, 12, 965, 95, "lineNumber"], [1068, 22, 965, 95], [1069, 12, 965, 95, "columnNumber"], [1069, 24, 965, 95], [1070, 10, 965, 95], [1070, 17, 966, 18], [1071, 8, 966, 19], [1072, 10, 966, 19, "fileName"], [1072, 18, 966, 19], [1072, 20, 966, 19, "_jsxFileName"], [1072, 32, 966, 19], [1073, 10, 966, 19, "lineNumber"], [1073, 20, 966, 19], [1074, 10, 966, 19, "columnNumber"], [1074, 22, 966, 19], [1075, 8, 966, 19], [1075, 15, 967, 16], [1075, 16, 968, 9], [1075, 18, 971, 9, "isCameraReady"], [1075, 31, 971, 22], [1075, 35, 971, 26, "previewBlurEnabled"], [1075, 53, 971, 44], [1075, 57, 971, 48, "viewSize"], [1075, 65, 971, 56], [1075, 66, 971, 57, "width"], [1075, 71, 971, 62], [1075, 74, 971, 65], [1075, 75, 971, 66], [1075, 92, 972, 10], [1075, 96, 972, 10, "_jsxDevRuntime"], [1075, 110, 972, 10], [1075, 111, 972, 10, "jsxDEV"], [1075, 117, 972, 10], [1075, 119, 972, 10, "_jsxDevRuntime"], [1075, 133, 972, 10], [1075, 134, 972, 10, "Fragment"], [1075, 142, 972, 10], [1076, 10, 972, 10, "children"], [1076, 18, 972, 10], [1076, 34, 974, 12], [1076, 38, 974, 12, "_jsxDevRuntime"], [1076, 52, 974, 12], [1076, 53, 974, 12, "jsxDEV"], [1076, 59, 974, 12], [1076, 61, 974, 13, "_LiveFaceCanvas"], [1076, 76, 974, 13], [1076, 77, 974, 13, "default"], [1076, 84, 974, 27], [1077, 12, 974, 28, "containerId"], [1077, 23, 974, 39], [1077, 25, 974, 40], [1077, 42, 974, 57], [1078, 12, 974, 58, "width"], [1078, 17, 974, 63], [1078, 19, 974, 65, "viewSize"], [1078, 27, 974, 73], [1078, 28, 974, 74, "width"], [1078, 33, 974, 80], [1079, 12, 974, 81, "height"], [1079, 18, 974, 87], [1079, 20, 974, 89, "viewSize"], [1079, 28, 974, 97], [1079, 29, 974, 98, "height"], [1080, 10, 974, 105], [1081, 12, 974, 105, "fileName"], [1081, 20, 974, 105], [1081, 22, 974, 105, "_jsxFileName"], [1081, 34, 974, 105], [1082, 12, 974, 105, "lineNumber"], [1082, 22, 974, 105], [1083, 12, 974, 105, "columnNumber"], [1083, 24, 974, 105], [1084, 10, 974, 105], [1084, 17, 974, 107], [1084, 18, 974, 108], [1084, 33, 975, 12], [1084, 37, 975, 12, "_jsxDevRuntime"], [1084, 51, 975, 12], [1084, 52, 975, 12, "jsxDEV"], [1084, 58, 975, 12], [1084, 60, 975, 13, "_View"], [1084, 65, 975, 13], [1084, 66, 975, 13, "default"], [1084, 73, 975, 17], [1085, 12, 975, 18, "style"], [1085, 17, 975, 23], [1085, 19, 975, 25], [1085, 20, 975, 26, "StyleSheet"], [1085, 39, 975, 36], [1085, 40, 975, 37, "absoluteFill"], [1085, 52, 975, 49], [1085, 54, 975, 51], [1086, 14, 975, 53, "pointerEvents"], [1086, 27, 975, 66], [1086, 29, 975, 68], [1087, 12, 975, 75], [1087, 13, 975, 76], [1087, 14, 975, 78], [1088, 12, 975, 78, "children"], [1088, 20, 975, 78], [1088, 36, 977, 12], [1088, 40, 977, 12, "_jsxDevRuntime"], [1088, 54, 977, 12], [1088, 55, 977, 12, "jsxDEV"], [1088, 61, 977, 12], [1088, 63, 977, 13, "_expoBlur"], [1088, 72, 977, 13], [1088, 73, 977, 13, "BlurView"], [1088, 81, 977, 21], [1089, 14, 977, 22, "intensity"], [1089, 23, 977, 31], [1089, 25, 977, 33], [1089, 27, 977, 36], [1090, 14, 977, 37, "tint"], [1090, 18, 977, 41], [1090, 20, 977, 42], [1090, 26, 977, 48], [1091, 14, 977, 49, "style"], [1091, 19, 977, 54], [1091, 21, 977, 56], [1091, 22, 977, 57, "styles"], [1091, 28, 977, 63], [1091, 29, 977, 64, "blurZone"], [1091, 37, 977, 72], [1091, 39, 977, 74], [1092, 16, 978, 14, "left"], [1092, 20, 978, 18], [1092, 22, 978, 20], [1092, 23, 978, 21], [1093, 16, 979, 14, "top"], [1093, 19, 979, 17], [1093, 21, 979, 19, "viewSize"], [1093, 29, 979, 27], [1093, 30, 979, 28, "height"], [1093, 36, 979, 34], [1093, 39, 979, 37], [1093, 42, 979, 40], [1094, 16, 980, 14, "width"], [1094, 21, 980, 19], [1094, 23, 980, 21, "viewSize"], [1094, 31, 980, 29], [1094, 32, 980, 30, "width"], [1094, 37, 980, 35], [1095, 16, 981, 14, "height"], [1095, 22, 981, 20], [1095, 24, 981, 22, "viewSize"], [1095, 32, 981, 30], [1095, 33, 981, 31, "height"], [1095, 39, 981, 37], [1095, 42, 981, 40], [1095, 46, 981, 44], [1096, 16, 982, 14, "borderRadius"], [1096, 28, 982, 26], [1096, 30, 982, 28], [1097, 14, 983, 12], [1097, 15, 983, 13], [1098, 12, 983, 15], [1099, 14, 983, 15, "fileName"], [1099, 22, 983, 15], [1099, 24, 983, 15, "_jsxFileName"], [1099, 36, 983, 15], [1100, 14, 983, 15, "lineNumber"], [1100, 24, 983, 15], [1101, 14, 983, 15, "columnNumber"], [1101, 26, 983, 15], [1102, 12, 983, 15], [1102, 19, 983, 17], [1102, 20, 983, 18], [1102, 35, 985, 12], [1102, 39, 985, 12, "_jsxDevRuntime"], [1102, 53, 985, 12], [1102, 54, 985, 12, "jsxDEV"], [1102, 60, 985, 12], [1102, 62, 985, 13, "_expoBlur"], [1102, 71, 985, 13], [1102, 72, 985, 13, "BlurView"], [1102, 80, 985, 21], [1103, 14, 985, 22, "intensity"], [1103, 23, 985, 31], [1103, 25, 985, 33], [1103, 27, 985, 36], [1104, 14, 985, 37, "tint"], [1104, 18, 985, 41], [1104, 20, 985, 42], [1104, 26, 985, 48], [1105, 14, 985, 49, "style"], [1105, 19, 985, 54], [1105, 21, 985, 56], [1105, 22, 985, 57, "styles"], [1105, 28, 985, 63], [1105, 29, 985, 64, "blurZone"], [1105, 37, 985, 72], [1105, 39, 985, 74], [1106, 16, 986, 14, "left"], [1106, 20, 986, 18], [1106, 22, 986, 20], [1106, 23, 986, 21], [1107, 16, 987, 14, "top"], [1107, 19, 987, 17], [1107, 21, 987, 19], [1107, 22, 987, 20], [1108, 16, 988, 14, "width"], [1108, 21, 988, 19], [1108, 23, 988, 21, "viewSize"], [1108, 31, 988, 29], [1108, 32, 988, 30, "width"], [1108, 37, 988, 35], [1109, 16, 989, 14, "height"], [1109, 22, 989, 20], [1109, 24, 989, 22, "viewSize"], [1109, 32, 989, 30], [1109, 33, 989, 31, "height"], [1109, 39, 989, 37], [1109, 42, 989, 40], [1109, 45, 989, 43], [1110, 16, 990, 14, "borderRadius"], [1110, 28, 990, 26], [1110, 30, 990, 28], [1111, 14, 991, 12], [1111, 15, 991, 13], [1112, 12, 991, 15], [1113, 14, 991, 15, "fileName"], [1113, 22, 991, 15], [1113, 24, 991, 15, "_jsxFileName"], [1113, 36, 991, 15], [1114, 14, 991, 15, "lineNumber"], [1114, 24, 991, 15], [1115, 14, 991, 15, "columnNumber"], [1115, 26, 991, 15], [1116, 12, 991, 15], [1116, 19, 991, 17], [1116, 20, 991, 18], [1116, 35, 993, 12], [1116, 39, 993, 12, "_jsxDevRuntime"], [1116, 53, 993, 12], [1116, 54, 993, 12, "jsxDEV"], [1116, 60, 993, 12], [1116, 62, 993, 13, "_expoBlur"], [1116, 71, 993, 13], [1116, 72, 993, 13, "BlurView"], [1116, 80, 993, 21], [1117, 14, 993, 22, "intensity"], [1117, 23, 993, 31], [1117, 25, 993, 33], [1117, 27, 993, 36], [1118, 14, 993, 37, "tint"], [1118, 18, 993, 41], [1118, 20, 993, 42], [1118, 26, 993, 48], [1119, 14, 993, 49, "style"], [1119, 19, 993, 54], [1119, 21, 993, 56], [1119, 22, 993, 57, "styles"], [1119, 28, 993, 63], [1119, 29, 993, 64, "blurZone"], [1119, 37, 993, 72], [1119, 39, 993, 74], [1120, 16, 994, 14, "left"], [1120, 20, 994, 18], [1120, 22, 994, 20, "viewSize"], [1120, 30, 994, 28], [1120, 31, 994, 29, "width"], [1120, 36, 994, 34], [1120, 39, 994, 37], [1120, 42, 994, 40], [1120, 45, 994, 44, "viewSize"], [1120, 53, 994, 52], [1120, 54, 994, 53, "width"], [1120, 59, 994, 58], [1120, 62, 994, 61], [1120, 66, 994, 66], [1121, 16, 995, 14, "top"], [1121, 19, 995, 17], [1121, 21, 995, 19, "viewSize"], [1121, 29, 995, 27], [1121, 30, 995, 28, "height"], [1121, 36, 995, 34], [1121, 39, 995, 37], [1121, 43, 995, 41], [1121, 46, 995, 45, "viewSize"], [1121, 54, 995, 53], [1121, 55, 995, 54, "width"], [1121, 60, 995, 59], [1121, 63, 995, 62], [1121, 67, 995, 67], [1122, 16, 996, 14, "width"], [1122, 21, 996, 19], [1122, 23, 996, 21, "viewSize"], [1122, 31, 996, 29], [1122, 32, 996, 30, "width"], [1122, 37, 996, 35], [1122, 40, 996, 38], [1122, 43, 996, 41], [1123, 16, 997, 14, "height"], [1123, 22, 997, 20], [1123, 24, 997, 22, "viewSize"], [1123, 32, 997, 30], [1123, 33, 997, 31, "width"], [1123, 38, 997, 36], [1123, 41, 997, 39], [1123, 44, 997, 42], [1124, 16, 998, 14, "borderRadius"], [1124, 28, 998, 26], [1124, 30, 998, 29, "viewSize"], [1124, 38, 998, 37], [1124, 39, 998, 38, "width"], [1124, 44, 998, 43], [1124, 47, 998, 46], [1124, 50, 998, 49], [1124, 53, 998, 53], [1125, 14, 999, 12], [1125, 15, 999, 13], [1126, 12, 999, 15], [1127, 14, 999, 15, "fileName"], [1127, 22, 999, 15], [1127, 24, 999, 15, "_jsxFileName"], [1127, 36, 999, 15], [1128, 14, 999, 15, "lineNumber"], [1128, 24, 999, 15], [1129, 14, 999, 15, "columnNumber"], [1129, 26, 999, 15], [1130, 12, 999, 15], [1130, 19, 999, 17], [1130, 20, 999, 18], [1130, 35, 1000, 12], [1130, 39, 1000, 12, "_jsxDevRuntime"], [1130, 53, 1000, 12], [1130, 54, 1000, 12, "jsxDEV"], [1130, 60, 1000, 12], [1130, 62, 1000, 13, "_expoBlur"], [1130, 71, 1000, 13], [1130, 72, 1000, 13, "BlurView"], [1130, 80, 1000, 21], [1131, 14, 1000, 22, "intensity"], [1131, 23, 1000, 31], [1131, 25, 1000, 33], [1131, 27, 1000, 36], [1132, 14, 1000, 37, "tint"], [1132, 18, 1000, 41], [1132, 20, 1000, 42], [1132, 26, 1000, 48], [1133, 14, 1000, 49, "style"], [1133, 19, 1000, 54], [1133, 21, 1000, 56], [1133, 22, 1000, 57, "styles"], [1133, 28, 1000, 63], [1133, 29, 1000, 64, "blurZone"], [1133, 37, 1000, 72], [1133, 39, 1000, 74], [1134, 16, 1001, 14, "left"], [1134, 20, 1001, 18], [1134, 22, 1001, 20, "viewSize"], [1134, 30, 1001, 28], [1134, 31, 1001, 29, "width"], [1134, 36, 1001, 34], [1134, 39, 1001, 37], [1134, 42, 1001, 40], [1134, 45, 1001, 44, "viewSize"], [1134, 53, 1001, 52], [1134, 54, 1001, 53, "width"], [1134, 59, 1001, 58], [1134, 62, 1001, 61], [1134, 66, 1001, 66], [1135, 16, 1002, 14, "top"], [1135, 19, 1002, 17], [1135, 21, 1002, 19, "viewSize"], [1135, 29, 1002, 27], [1135, 30, 1002, 28, "height"], [1135, 36, 1002, 34], [1135, 39, 1002, 37], [1135, 42, 1002, 40], [1135, 45, 1002, 44, "viewSize"], [1135, 53, 1002, 52], [1135, 54, 1002, 53, "width"], [1135, 59, 1002, 58], [1135, 62, 1002, 61], [1135, 66, 1002, 66], [1136, 16, 1003, 14, "width"], [1136, 21, 1003, 19], [1136, 23, 1003, 21, "viewSize"], [1136, 31, 1003, 29], [1136, 32, 1003, 30, "width"], [1136, 37, 1003, 35], [1136, 40, 1003, 38], [1136, 43, 1003, 41], [1137, 16, 1004, 14, "height"], [1137, 22, 1004, 20], [1137, 24, 1004, 22, "viewSize"], [1137, 32, 1004, 30], [1137, 33, 1004, 31, "width"], [1137, 38, 1004, 36], [1137, 41, 1004, 39], [1137, 44, 1004, 42], [1138, 16, 1005, 14, "borderRadius"], [1138, 28, 1005, 26], [1138, 30, 1005, 29, "viewSize"], [1138, 38, 1005, 37], [1138, 39, 1005, 38, "width"], [1138, 44, 1005, 43], [1138, 47, 1005, 46], [1138, 50, 1005, 49], [1138, 53, 1005, 53], [1139, 14, 1006, 12], [1139, 15, 1006, 13], [1140, 12, 1006, 15], [1141, 14, 1006, 15, "fileName"], [1141, 22, 1006, 15], [1141, 24, 1006, 15, "_jsxFileName"], [1141, 36, 1006, 15], [1142, 14, 1006, 15, "lineNumber"], [1142, 24, 1006, 15], [1143, 14, 1006, 15, "columnNumber"], [1143, 26, 1006, 15], [1144, 12, 1006, 15], [1144, 19, 1006, 17], [1144, 20, 1006, 18], [1144, 35, 1007, 12], [1144, 39, 1007, 12, "_jsxDevRuntime"], [1144, 53, 1007, 12], [1144, 54, 1007, 12, "jsxDEV"], [1144, 60, 1007, 12], [1144, 62, 1007, 13, "_expoBlur"], [1144, 71, 1007, 13], [1144, 72, 1007, 13, "BlurView"], [1144, 80, 1007, 21], [1145, 14, 1007, 22, "intensity"], [1145, 23, 1007, 31], [1145, 25, 1007, 33], [1145, 27, 1007, 36], [1146, 14, 1007, 37, "tint"], [1146, 18, 1007, 41], [1146, 20, 1007, 42], [1146, 26, 1007, 48], [1147, 14, 1007, 49, "style"], [1147, 19, 1007, 54], [1147, 21, 1007, 56], [1147, 22, 1007, 57, "styles"], [1147, 28, 1007, 63], [1147, 29, 1007, 64, "blurZone"], [1147, 37, 1007, 72], [1147, 39, 1007, 74], [1148, 16, 1008, 14, "left"], [1148, 20, 1008, 18], [1148, 22, 1008, 20, "viewSize"], [1148, 30, 1008, 28], [1148, 31, 1008, 29, "width"], [1148, 36, 1008, 34], [1148, 39, 1008, 37], [1148, 42, 1008, 40], [1148, 45, 1008, 44, "viewSize"], [1148, 53, 1008, 52], [1148, 54, 1008, 53, "width"], [1148, 59, 1008, 58], [1148, 62, 1008, 61], [1148, 66, 1008, 66], [1149, 16, 1009, 14, "top"], [1149, 19, 1009, 17], [1149, 21, 1009, 19, "viewSize"], [1149, 29, 1009, 27], [1149, 30, 1009, 28, "height"], [1149, 36, 1009, 34], [1149, 39, 1009, 37], [1149, 42, 1009, 40], [1149, 45, 1009, 44, "viewSize"], [1149, 53, 1009, 52], [1149, 54, 1009, 53, "width"], [1149, 59, 1009, 58], [1149, 62, 1009, 61], [1149, 66, 1009, 66], [1150, 16, 1010, 14, "width"], [1150, 21, 1010, 19], [1150, 23, 1010, 21, "viewSize"], [1150, 31, 1010, 29], [1150, 32, 1010, 30, "width"], [1150, 37, 1010, 35], [1150, 40, 1010, 38], [1150, 43, 1010, 41], [1151, 16, 1011, 14, "height"], [1151, 22, 1011, 20], [1151, 24, 1011, 22, "viewSize"], [1151, 32, 1011, 30], [1151, 33, 1011, 31, "width"], [1151, 38, 1011, 36], [1151, 41, 1011, 39], [1151, 44, 1011, 42], [1152, 16, 1012, 14, "borderRadius"], [1152, 28, 1012, 26], [1152, 30, 1012, 29, "viewSize"], [1152, 38, 1012, 37], [1152, 39, 1012, 38, "width"], [1152, 44, 1012, 43], [1152, 47, 1012, 46], [1152, 50, 1012, 49], [1152, 53, 1012, 53], [1153, 14, 1013, 12], [1153, 15, 1013, 13], [1154, 12, 1013, 15], [1155, 14, 1013, 15, "fileName"], [1155, 22, 1013, 15], [1155, 24, 1013, 15, "_jsxFileName"], [1155, 36, 1013, 15], [1156, 14, 1013, 15, "lineNumber"], [1156, 24, 1013, 15], [1157, 14, 1013, 15, "columnNumber"], [1157, 26, 1013, 15], [1158, 12, 1013, 15], [1158, 19, 1013, 17], [1158, 20, 1013, 18], [1158, 22, 1015, 13, "__DEV__"], [1158, 29, 1015, 20], [1158, 46, 1016, 14], [1158, 50, 1016, 14, "_jsxDevRuntime"], [1158, 64, 1016, 14], [1158, 65, 1016, 14, "jsxDEV"], [1158, 71, 1016, 14], [1158, 73, 1016, 15, "_View"], [1158, 78, 1016, 15], [1158, 79, 1016, 15, "default"], [1158, 86, 1016, 19], [1159, 14, 1016, 20, "style"], [1159, 19, 1016, 25], [1159, 21, 1016, 27, "styles"], [1159, 27, 1016, 33], [1159, 28, 1016, 34, "previewChip"], [1159, 39, 1016, 46], [1160, 14, 1016, 46, "children"], [1160, 22, 1016, 46], [1160, 37, 1017, 16], [1160, 41, 1017, 16, "_jsxDevRuntime"], [1160, 55, 1017, 16], [1160, 56, 1017, 16, "jsxDEV"], [1160, 62, 1017, 16], [1160, 64, 1017, 17, "_Text"], [1160, 69, 1017, 17], [1160, 70, 1017, 17, "default"], [1160, 77, 1017, 21], [1161, 16, 1017, 22, "style"], [1161, 21, 1017, 27], [1161, 23, 1017, 29, "styles"], [1161, 29, 1017, 35], [1161, 30, 1017, 36, "previewChipText"], [1161, 45, 1017, 52], [1162, 16, 1017, 52, "children"], [1162, 24, 1017, 52], [1162, 26, 1017, 53], [1163, 14, 1017, 73], [1164, 16, 1017, 73, "fileName"], [1164, 24, 1017, 73], [1164, 26, 1017, 73, "_jsxFileName"], [1164, 38, 1017, 73], [1165, 16, 1017, 73, "lineNumber"], [1165, 26, 1017, 73], [1166, 16, 1017, 73, "columnNumber"], [1166, 28, 1017, 73], [1167, 14, 1017, 73], [1167, 21, 1017, 79], [1168, 12, 1017, 80], [1169, 14, 1017, 80, "fileName"], [1169, 22, 1017, 80], [1169, 24, 1017, 80, "_jsxFileName"], [1169, 36, 1017, 80], [1170, 14, 1017, 80, "lineNumber"], [1170, 24, 1017, 80], [1171, 14, 1017, 80, "columnNumber"], [1171, 26, 1017, 80], [1172, 12, 1017, 80], [1172, 19, 1018, 20], [1172, 20, 1019, 13], [1173, 10, 1019, 13], [1174, 12, 1019, 13, "fileName"], [1174, 20, 1019, 13], [1174, 22, 1019, 13, "_jsxFileName"], [1174, 34, 1019, 13], [1175, 12, 1019, 13, "lineNumber"], [1175, 22, 1019, 13], [1176, 12, 1019, 13, "columnNumber"], [1176, 24, 1019, 13], [1177, 10, 1019, 13], [1177, 17, 1020, 18], [1177, 18, 1020, 19], [1178, 8, 1020, 19], [1178, 23, 1021, 12], [1178, 24, 1022, 9], [1178, 26, 1024, 9, "isCameraReady"], [1178, 39, 1024, 22], [1178, 56, 1025, 10], [1178, 60, 1025, 10, "_jsxDevRuntime"], [1178, 74, 1025, 10], [1178, 75, 1025, 10, "jsxDEV"], [1178, 81, 1025, 10], [1178, 83, 1025, 10, "_jsxDevRuntime"], [1178, 97, 1025, 10], [1178, 98, 1025, 10, "Fragment"], [1178, 106, 1025, 10], [1179, 10, 1025, 10, "children"], [1179, 18, 1025, 10], [1179, 34, 1027, 12], [1179, 38, 1027, 12, "_jsxDevRuntime"], [1179, 52, 1027, 12], [1179, 53, 1027, 12, "jsxDEV"], [1179, 59, 1027, 12], [1179, 61, 1027, 13, "_View"], [1179, 66, 1027, 13], [1179, 67, 1027, 13, "default"], [1179, 74, 1027, 17], [1180, 12, 1027, 18, "style"], [1180, 17, 1027, 23], [1180, 19, 1027, 25, "styles"], [1180, 25, 1027, 31], [1180, 26, 1027, 32, "headerOverlay"], [1180, 39, 1027, 46], [1181, 12, 1027, 46, "children"], [1181, 20, 1027, 46], [1181, 35, 1028, 14], [1181, 39, 1028, 14, "_jsxDevRuntime"], [1181, 53, 1028, 14], [1181, 54, 1028, 14, "jsxDEV"], [1181, 60, 1028, 14], [1181, 62, 1028, 15, "_View"], [1181, 67, 1028, 15], [1181, 68, 1028, 15, "default"], [1181, 75, 1028, 19], [1182, 14, 1028, 20, "style"], [1182, 19, 1028, 25], [1182, 21, 1028, 27, "styles"], [1182, 27, 1028, 33], [1182, 28, 1028, 34, "headerContent"], [1182, 41, 1028, 48], [1183, 14, 1028, 48, "children"], [1183, 22, 1028, 48], [1183, 38, 1029, 16], [1183, 42, 1029, 16, "_jsxDevRuntime"], [1183, 56, 1029, 16], [1183, 57, 1029, 16, "jsxDEV"], [1183, 63, 1029, 16], [1183, 65, 1029, 17, "_View"], [1183, 70, 1029, 17], [1183, 71, 1029, 17, "default"], [1183, 78, 1029, 21], [1184, 16, 1029, 22, "style"], [1184, 21, 1029, 27], [1184, 23, 1029, 29, "styles"], [1184, 29, 1029, 35], [1184, 30, 1029, 36, "headerLeft"], [1184, 40, 1029, 47], [1185, 16, 1029, 47, "children"], [1185, 24, 1029, 47], [1185, 40, 1030, 18], [1185, 44, 1030, 18, "_jsxDevRuntime"], [1185, 58, 1030, 18], [1185, 59, 1030, 18, "jsxDEV"], [1185, 65, 1030, 18], [1185, 67, 1030, 19, "_Text"], [1185, 72, 1030, 19], [1185, 73, 1030, 19, "default"], [1185, 80, 1030, 23], [1186, 18, 1030, 24, "style"], [1186, 23, 1030, 29], [1186, 25, 1030, 31, "styles"], [1186, 31, 1030, 37], [1186, 32, 1030, 38, "headerTitle"], [1186, 43, 1030, 50], [1187, 18, 1030, 50, "children"], [1187, 26, 1030, 50], [1187, 28, 1030, 51], [1188, 16, 1030, 62], [1189, 18, 1030, 62, "fileName"], [1189, 26, 1030, 62], [1189, 28, 1030, 62, "_jsxFileName"], [1189, 40, 1030, 62], [1190, 18, 1030, 62, "lineNumber"], [1190, 28, 1030, 62], [1191, 18, 1030, 62, "columnNumber"], [1191, 30, 1030, 62], [1192, 16, 1030, 62], [1192, 23, 1030, 68], [1192, 24, 1030, 69], [1192, 39, 1031, 18], [1192, 43, 1031, 18, "_jsxDevRuntime"], [1192, 57, 1031, 18], [1192, 58, 1031, 18, "jsxDEV"], [1192, 64, 1031, 18], [1192, 66, 1031, 19, "_View"], [1192, 71, 1031, 19], [1192, 72, 1031, 19, "default"], [1192, 79, 1031, 23], [1193, 18, 1031, 24, "style"], [1193, 23, 1031, 29], [1193, 25, 1031, 31, "styles"], [1193, 31, 1031, 37], [1193, 32, 1031, 38, "subtitleRow"], [1193, 43, 1031, 50], [1194, 18, 1031, 50, "children"], [1194, 26, 1031, 50], [1194, 42, 1032, 20], [1194, 46, 1032, 20, "_jsxDevRuntime"], [1194, 60, 1032, 20], [1194, 61, 1032, 20, "jsxDEV"], [1194, 67, 1032, 20], [1194, 69, 1032, 21, "_Text"], [1194, 74, 1032, 21], [1194, 75, 1032, 21, "default"], [1194, 82, 1032, 25], [1195, 20, 1032, 26, "style"], [1195, 25, 1032, 31], [1195, 27, 1032, 33, "styles"], [1195, 33, 1032, 39], [1195, 34, 1032, 40, "webIcon"], [1195, 41, 1032, 48], [1196, 20, 1032, 48, "children"], [1196, 28, 1032, 48], [1196, 30, 1032, 49], [1197, 18, 1032, 51], [1198, 20, 1032, 51, "fileName"], [1198, 28, 1032, 51], [1198, 30, 1032, 51, "_jsxFileName"], [1198, 42, 1032, 51], [1199, 20, 1032, 51, "lineNumber"], [1199, 30, 1032, 51], [1200, 20, 1032, 51, "columnNumber"], [1200, 32, 1032, 51], [1201, 18, 1032, 51], [1201, 25, 1032, 57], [1201, 26, 1032, 58], [1201, 41, 1033, 20], [1201, 45, 1033, 20, "_jsxDevRuntime"], [1201, 59, 1033, 20], [1201, 60, 1033, 20, "jsxDEV"], [1201, 66, 1033, 20], [1201, 68, 1033, 21, "_Text"], [1201, 73, 1033, 21], [1201, 74, 1033, 21, "default"], [1201, 81, 1033, 25], [1202, 20, 1033, 26, "style"], [1202, 25, 1033, 31], [1202, 27, 1033, 33, "styles"], [1202, 33, 1033, 39], [1202, 34, 1033, 40, "headerSubtitle"], [1202, 48, 1033, 55], [1203, 20, 1033, 55, "children"], [1203, 28, 1033, 55], [1203, 30, 1033, 56], [1204, 18, 1033, 71], [1205, 20, 1033, 71, "fileName"], [1205, 28, 1033, 71], [1205, 30, 1033, 71, "_jsxFileName"], [1205, 42, 1033, 71], [1206, 20, 1033, 71, "lineNumber"], [1206, 30, 1033, 71], [1207, 20, 1033, 71, "columnNumber"], [1207, 32, 1033, 71], [1208, 18, 1033, 71], [1208, 25, 1033, 77], [1208, 26, 1033, 78], [1209, 16, 1033, 78], [1210, 18, 1033, 78, "fileName"], [1210, 26, 1033, 78], [1210, 28, 1033, 78, "_jsxFileName"], [1210, 40, 1033, 78], [1211, 18, 1033, 78, "lineNumber"], [1211, 28, 1033, 78], [1212, 18, 1033, 78, "columnNumber"], [1212, 30, 1033, 78], [1213, 16, 1033, 78], [1213, 23, 1034, 24], [1213, 24, 1034, 25], [1213, 26, 1035, 19, "challengeCode"], [1213, 39, 1035, 32], [1213, 56, 1036, 20], [1213, 60, 1036, 20, "_jsxDevRuntime"], [1213, 74, 1036, 20], [1213, 75, 1036, 20, "jsxDEV"], [1213, 81, 1036, 20], [1213, 83, 1036, 21, "_View"], [1213, 88, 1036, 21], [1213, 89, 1036, 21, "default"], [1213, 96, 1036, 25], [1214, 18, 1036, 26, "style"], [1214, 23, 1036, 31], [1214, 25, 1036, 33, "styles"], [1214, 31, 1036, 39], [1214, 32, 1036, 40, "challengeRow"], [1214, 44, 1036, 53], [1215, 18, 1036, 53, "children"], [1215, 26, 1036, 53], [1215, 42, 1037, 22], [1215, 46, 1037, 22, "_jsxDevRuntime"], [1215, 60, 1037, 22], [1215, 61, 1037, 22, "jsxDEV"], [1215, 67, 1037, 22], [1215, 69, 1037, 23, "_lucideReactNative"], [1215, 87, 1037, 23], [1215, 88, 1037, 23, "Shield"], [1215, 94, 1037, 29], [1216, 20, 1037, 30, "size"], [1216, 24, 1037, 34], [1216, 26, 1037, 36], [1216, 28, 1037, 39], [1217, 20, 1037, 40, "color"], [1217, 25, 1037, 45], [1217, 27, 1037, 46], [1218, 18, 1037, 52], [1219, 20, 1037, 52, "fileName"], [1219, 28, 1037, 52], [1219, 30, 1037, 52, "_jsxFileName"], [1219, 42, 1037, 52], [1220, 20, 1037, 52, "lineNumber"], [1220, 30, 1037, 52], [1221, 20, 1037, 52, "columnNumber"], [1221, 32, 1037, 52], [1222, 18, 1037, 52], [1222, 25, 1037, 54], [1222, 26, 1037, 55], [1222, 41, 1038, 22], [1222, 45, 1038, 22, "_jsxDevRuntime"], [1222, 59, 1038, 22], [1222, 60, 1038, 22, "jsxDEV"], [1222, 66, 1038, 22], [1222, 68, 1038, 23, "_Text"], [1222, 73, 1038, 23], [1222, 74, 1038, 23, "default"], [1222, 81, 1038, 27], [1223, 20, 1038, 28, "style"], [1223, 25, 1038, 33], [1223, 27, 1038, 35, "styles"], [1223, 33, 1038, 41], [1223, 34, 1038, 42, "challengeCode"], [1223, 47, 1038, 56], [1224, 20, 1038, 56, "children"], [1224, 28, 1038, 56], [1224, 30, 1038, 58, "challengeCode"], [1225, 18, 1038, 71], [1226, 20, 1038, 71, "fileName"], [1226, 28, 1038, 71], [1226, 30, 1038, 71, "_jsxFileName"], [1226, 42, 1038, 71], [1227, 20, 1038, 71, "lineNumber"], [1227, 30, 1038, 71], [1228, 20, 1038, 71, "columnNumber"], [1228, 32, 1038, 71], [1229, 18, 1038, 71], [1229, 25, 1038, 78], [1229, 26, 1038, 79], [1230, 16, 1038, 79], [1231, 18, 1038, 79, "fileName"], [1231, 26, 1038, 79], [1231, 28, 1038, 79, "_jsxFileName"], [1231, 40, 1038, 79], [1232, 18, 1038, 79, "lineNumber"], [1232, 28, 1038, 79], [1233, 18, 1038, 79, "columnNumber"], [1233, 30, 1038, 79], [1234, 16, 1038, 79], [1234, 23, 1039, 26], [1234, 24, 1040, 19], [1235, 14, 1040, 19], [1236, 16, 1040, 19, "fileName"], [1236, 24, 1040, 19], [1236, 26, 1040, 19, "_jsxFileName"], [1236, 38, 1040, 19], [1237, 16, 1040, 19, "lineNumber"], [1237, 26, 1040, 19], [1238, 16, 1040, 19, "columnNumber"], [1238, 28, 1040, 19], [1239, 14, 1040, 19], [1239, 21, 1041, 22], [1239, 22, 1041, 23], [1239, 37, 1042, 16], [1239, 41, 1042, 16, "_jsxDevRuntime"], [1239, 55, 1042, 16], [1239, 56, 1042, 16, "jsxDEV"], [1239, 62, 1042, 16], [1239, 64, 1042, 17, "_TouchableOpacity"], [1239, 81, 1042, 17], [1239, 82, 1042, 17, "default"], [1239, 89, 1042, 33], [1240, 16, 1042, 34, "onPress"], [1240, 23, 1042, 41], [1240, 25, 1042, 43, "onCancel"], [1240, 33, 1042, 52], [1241, 16, 1042, 53, "style"], [1241, 21, 1042, 58], [1241, 23, 1042, 60, "styles"], [1241, 29, 1042, 66], [1241, 30, 1042, 67, "closeButton"], [1241, 41, 1042, 79], [1242, 16, 1042, 79, "children"], [1242, 24, 1042, 79], [1242, 39, 1043, 18], [1242, 43, 1043, 18, "_jsxDevRuntime"], [1242, 57, 1043, 18], [1242, 58, 1043, 18, "jsxDEV"], [1242, 64, 1043, 18], [1242, 66, 1043, 19, "_lucideReactNative"], [1242, 84, 1043, 19], [1242, 85, 1043, 19, "X"], [1242, 86, 1043, 20], [1243, 18, 1043, 21, "size"], [1243, 22, 1043, 25], [1243, 24, 1043, 27], [1243, 26, 1043, 30], [1244, 18, 1043, 31, "color"], [1244, 23, 1043, 36], [1244, 25, 1043, 37], [1245, 16, 1043, 43], [1246, 18, 1043, 43, "fileName"], [1246, 26, 1043, 43], [1246, 28, 1043, 43, "_jsxFileName"], [1246, 40, 1043, 43], [1247, 18, 1043, 43, "lineNumber"], [1247, 28, 1043, 43], [1248, 18, 1043, 43, "columnNumber"], [1248, 30, 1043, 43], [1249, 16, 1043, 43], [1249, 23, 1043, 45], [1250, 14, 1043, 46], [1251, 16, 1043, 46, "fileName"], [1251, 24, 1043, 46], [1251, 26, 1043, 46, "_jsxFileName"], [1251, 38, 1043, 46], [1252, 16, 1043, 46, "lineNumber"], [1252, 26, 1043, 46], [1253, 16, 1043, 46, "columnNumber"], [1253, 28, 1043, 46], [1254, 14, 1043, 46], [1254, 21, 1044, 34], [1254, 22, 1044, 35], [1255, 12, 1044, 35], [1256, 14, 1044, 35, "fileName"], [1256, 22, 1044, 35], [1256, 24, 1044, 35, "_jsxFileName"], [1256, 36, 1044, 35], [1257, 14, 1044, 35, "lineNumber"], [1257, 24, 1044, 35], [1258, 14, 1044, 35, "columnNumber"], [1258, 26, 1044, 35], [1259, 12, 1044, 35], [1259, 19, 1045, 20], [1260, 10, 1045, 21], [1261, 12, 1045, 21, "fileName"], [1261, 20, 1045, 21], [1261, 22, 1045, 21, "_jsxFileName"], [1261, 34, 1045, 21], [1262, 12, 1045, 21, "lineNumber"], [1262, 22, 1045, 21], [1263, 12, 1045, 21, "columnNumber"], [1263, 24, 1045, 21], [1264, 10, 1045, 21], [1264, 17, 1046, 18], [1264, 18, 1046, 19], [1264, 33, 1048, 12], [1264, 37, 1048, 12, "_jsxDevRuntime"], [1264, 51, 1048, 12], [1264, 52, 1048, 12, "jsxDEV"], [1264, 58, 1048, 12], [1264, 60, 1048, 13, "_View"], [1264, 65, 1048, 13], [1264, 66, 1048, 13, "default"], [1264, 73, 1048, 17], [1265, 12, 1048, 18, "style"], [1265, 17, 1048, 23], [1265, 19, 1048, 25, "styles"], [1265, 25, 1048, 31], [1265, 26, 1048, 32, "privacyNotice"], [1265, 39, 1048, 46], [1266, 12, 1048, 46, "children"], [1266, 20, 1048, 46], [1266, 36, 1049, 14], [1266, 40, 1049, 14, "_jsxDevRuntime"], [1266, 54, 1049, 14], [1266, 55, 1049, 14, "jsxDEV"], [1266, 61, 1049, 14], [1266, 63, 1049, 15, "_lucideReactNative"], [1266, 81, 1049, 15], [1266, 82, 1049, 15, "Shield"], [1266, 88, 1049, 21], [1267, 14, 1049, 22, "size"], [1267, 18, 1049, 26], [1267, 20, 1049, 28], [1267, 22, 1049, 31], [1268, 14, 1049, 32, "color"], [1268, 19, 1049, 37], [1268, 21, 1049, 38], [1269, 12, 1049, 47], [1270, 14, 1049, 47, "fileName"], [1270, 22, 1049, 47], [1270, 24, 1049, 47, "_jsxFileName"], [1270, 36, 1049, 47], [1271, 14, 1049, 47, "lineNumber"], [1271, 24, 1049, 47], [1272, 14, 1049, 47, "columnNumber"], [1272, 26, 1049, 47], [1273, 12, 1049, 47], [1273, 19, 1049, 49], [1273, 20, 1049, 50], [1273, 35, 1050, 14], [1273, 39, 1050, 14, "_jsxDevRuntime"], [1273, 53, 1050, 14], [1273, 54, 1050, 14, "jsxDEV"], [1273, 60, 1050, 14], [1273, 62, 1050, 15, "_Text"], [1273, 67, 1050, 15], [1273, 68, 1050, 15, "default"], [1273, 75, 1050, 19], [1274, 14, 1050, 20, "style"], [1274, 19, 1050, 25], [1274, 21, 1050, 27, "styles"], [1274, 27, 1050, 33], [1274, 28, 1050, 34, "privacyText"], [1274, 39, 1050, 46], [1275, 14, 1050, 46, "children"], [1275, 22, 1050, 46], [1275, 24, 1050, 47], [1276, 12, 1052, 14], [1277, 14, 1052, 14, "fileName"], [1277, 22, 1052, 14], [1277, 24, 1052, 14, "_jsxFileName"], [1277, 36, 1052, 14], [1278, 14, 1052, 14, "lineNumber"], [1278, 24, 1052, 14], [1279, 14, 1052, 14, "columnNumber"], [1279, 26, 1052, 14], [1280, 12, 1052, 14], [1280, 19, 1052, 20], [1280, 20, 1052, 21], [1281, 10, 1052, 21], [1282, 12, 1052, 21, "fileName"], [1282, 20, 1052, 21], [1282, 22, 1052, 21, "_jsxFileName"], [1282, 34, 1052, 21], [1283, 12, 1052, 21, "lineNumber"], [1283, 22, 1052, 21], [1284, 12, 1052, 21, "columnNumber"], [1284, 24, 1052, 21], [1285, 10, 1052, 21], [1285, 17, 1053, 18], [1285, 18, 1053, 19], [1285, 33, 1055, 12], [1285, 37, 1055, 12, "_jsxDevRuntime"], [1285, 51, 1055, 12], [1285, 52, 1055, 12, "jsxDEV"], [1285, 58, 1055, 12], [1285, 60, 1055, 13, "_View"], [1285, 65, 1055, 13], [1285, 66, 1055, 13, "default"], [1285, 73, 1055, 17], [1286, 12, 1055, 18, "style"], [1286, 17, 1055, 23], [1286, 19, 1055, 25, "styles"], [1286, 25, 1055, 31], [1286, 26, 1055, 32, "footer<PERSON><PERSON><PERSON>"], [1286, 39, 1055, 46], [1287, 12, 1055, 46, "children"], [1287, 20, 1055, 46], [1287, 36, 1056, 14], [1287, 40, 1056, 14, "_jsxDevRuntime"], [1287, 54, 1056, 14], [1287, 55, 1056, 14, "jsxDEV"], [1287, 61, 1056, 14], [1287, 63, 1056, 15, "_Text"], [1287, 68, 1056, 15], [1287, 69, 1056, 15, "default"], [1287, 76, 1056, 19], [1288, 14, 1056, 20, "style"], [1288, 19, 1056, 25], [1288, 21, 1056, 27, "styles"], [1288, 27, 1056, 33], [1288, 28, 1056, 34, "instruction"], [1288, 39, 1056, 46], [1289, 14, 1056, 46, "children"], [1289, 22, 1056, 46], [1289, 24, 1056, 47], [1290, 12, 1058, 14], [1291, 14, 1058, 14, "fileName"], [1291, 22, 1058, 14], [1291, 24, 1058, 14, "_jsxFileName"], [1291, 36, 1058, 14], [1292, 14, 1058, 14, "lineNumber"], [1292, 24, 1058, 14], [1293, 14, 1058, 14, "columnNumber"], [1293, 26, 1058, 14], [1294, 12, 1058, 14], [1294, 19, 1058, 20], [1294, 20, 1058, 21], [1294, 35, 1060, 14], [1294, 39, 1060, 14, "_jsxDevRuntime"], [1294, 53, 1060, 14], [1294, 54, 1060, 14, "jsxDEV"], [1294, 60, 1060, 14], [1294, 62, 1060, 15, "_TouchableOpacity"], [1294, 79, 1060, 15], [1294, 80, 1060, 15, "default"], [1294, 87, 1060, 31], [1295, 14, 1061, 16, "onPress"], [1295, 21, 1061, 23], [1295, 23, 1061, 25, "capturePhoto"], [1295, 35, 1061, 38], [1296, 14, 1062, 16, "disabled"], [1296, 22, 1062, 24], [1296, 24, 1062, 26, "processingState"], [1296, 39, 1062, 41], [1296, 44, 1062, 46], [1296, 50, 1062, 52], [1296, 54, 1062, 56], [1296, 55, 1062, 57, "isCameraReady"], [1296, 68, 1062, 71], [1297, 14, 1063, 16, "style"], [1297, 19, 1063, 21], [1297, 21, 1063, 23], [1297, 22, 1064, 18, "styles"], [1297, 28, 1064, 24], [1297, 29, 1064, 25, "shutterButton"], [1297, 42, 1064, 38], [1297, 44, 1065, 18, "processingState"], [1297, 59, 1065, 33], [1297, 64, 1065, 38], [1297, 70, 1065, 44], [1297, 74, 1065, 48, "styles"], [1297, 80, 1065, 54], [1297, 81, 1065, 55, "shutterButtonDisabled"], [1297, 102, 1065, 76], [1297, 103, 1066, 18], [1298, 14, 1066, 18, "children"], [1298, 22, 1066, 18], [1298, 24, 1068, 17, "processingState"], [1298, 39, 1068, 32], [1298, 44, 1068, 37], [1298, 50, 1068, 43], [1298, 66, 1069, 18], [1298, 70, 1069, 18, "_jsxDevRuntime"], [1298, 84, 1069, 18], [1298, 85, 1069, 18, "jsxDEV"], [1298, 91, 1069, 18], [1298, 93, 1069, 19, "_View"], [1298, 98, 1069, 19], [1298, 99, 1069, 19, "default"], [1298, 106, 1069, 23], [1299, 16, 1069, 24, "style"], [1299, 21, 1069, 29], [1299, 23, 1069, 31, "styles"], [1299, 29, 1069, 37], [1299, 30, 1069, 38, "shutterInner"], [1300, 14, 1069, 51], [1301, 16, 1069, 51, "fileName"], [1301, 24, 1069, 51], [1301, 26, 1069, 51, "_jsxFileName"], [1301, 38, 1069, 51], [1302, 16, 1069, 51, "lineNumber"], [1302, 26, 1069, 51], [1303, 16, 1069, 51, "columnNumber"], [1303, 28, 1069, 51], [1304, 14, 1069, 51], [1304, 21, 1069, 53], [1304, 22, 1069, 54], [1304, 38, 1071, 18], [1304, 42, 1071, 18, "_jsxDevRuntime"], [1304, 56, 1071, 18], [1304, 57, 1071, 18, "jsxDEV"], [1304, 63, 1071, 18], [1304, 65, 1071, 19, "_ActivityIndicator"], [1304, 83, 1071, 19], [1304, 84, 1071, 19, "default"], [1304, 91, 1071, 36], [1305, 16, 1071, 37, "size"], [1305, 20, 1071, 41], [1305, 22, 1071, 42], [1305, 29, 1071, 49], [1306, 16, 1071, 50, "color"], [1306, 21, 1071, 55], [1306, 23, 1071, 56], [1307, 14, 1071, 65], [1308, 16, 1071, 65, "fileName"], [1308, 24, 1071, 65], [1308, 26, 1071, 65, "_jsxFileName"], [1308, 38, 1071, 65], [1309, 16, 1071, 65, "lineNumber"], [1309, 26, 1071, 65], [1310, 16, 1071, 65, "columnNumber"], [1310, 28, 1071, 65], [1311, 14, 1071, 65], [1311, 21, 1071, 67], [1312, 12, 1072, 17], [1313, 14, 1072, 17, "fileName"], [1313, 22, 1072, 17], [1313, 24, 1072, 17, "_jsxFileName"], [1313, 36, 1072, 17], [1314, 14, 1072, 17, "lineNumber"], [1314, 24, 1072, 17], [1315, 14, 1072, 17, "columnNumber"], [1315, 26, 1072, 17], [1316, 12, 1072, 17], [1316, 19, 1073, 32], [1316, 20, 1073, 33], [1316, 35, 1074, 14], [1316, 39, 1074, 14, "_jsxDevRuntime"], [1316, 53, 1074, 14], [1316, 54, 1074, 14, "jsxDEV"], [1316, 60, 1074, 14], [1316, 62, 1074, 15, "_Text"], [1316, 67, 1074, 15], [1316, 68, 1074, 15, "default"], [1316, 75, 1074, 19], [1317, 14, 1074, 20, "style"], [1317, 19, 1074, 25], [1317, 21, 1074, 27, "styles"], [1317, 27, 1074, 33], [1317, 28, 1074, 34, "privacyNote"], [1317, 39, 1074, 46], [1318, 14, 1074, 46, "children"], [1318, 22, 1074, 46], [1318, 24, 1074, 47], [1319, 12, 1076, 14], [1320, 14, 1076, 14, "fileName"], [1320, 22, 1076, 14], [1320, 24, 1076, 14, "_jsxFileName"], [1320, 36, 1076, 14], [1321, 14, 1076, 14, "lineNumber"], [1321, 24, 1076, 14], [1322, 14, 1076, 14, "columnNumber"], [1322, 26, 1076, 14], [1323, 12, 1076, 14], [1323, 19, 1076, 20], [1323, 20, 1076, 21], [1324, 10, 1076, 21], [1325, 12, 1076, 21, "fileName"], [1325, 20, 1076, 21], [1325, 22, 1076, 21, "_jsxFileName"], [1325, 34, 1076, 21], [1326, 12, 1076, 21, "lineNumber"], [1326, 22, 1076, 21], [1327, 12, 1076, 21, "columnNumber"], [1327, 24, 1076, 21], [1328, 10, 1076, 21], [1328, 17, 1077, 18], [1328, 18, 1077, 19], [1329, 8, 1077, 19], [1329, 23, 1078, 12], [1329, 24, 1079, 9], [1330, 6, 1079, 9], [1331, 8, 1079, 9, "fileName"], [1331, 16, 1079, 9], [1331, 18, 1079, 9, "_jsxFileName"], [1331, 30, 1079, 9], [1332, 8, 1079, 9, "lineNumber"], [1332, 18, 1079, 9], [1333, 8, 1079, 9, "columnNumber"], [1333, 20, 1079, 9], [1334, 6, 1079, 9], [1334, 13, 1080, 12], [1334, 14, 1080, 13], [1334, 29, 1082, 6], [1334, 33, 1082, 6, "_jsxDevRuntime"], [1334, 47, 1082, 6], [1334, 48, 1082, 6, "jsxDEV"], [1334, 54, 1082, 6], [1334, 56, 1082, 7, "_Modal"], [1334, 62, 1082, 7], [1334, 63, 1082, 7, "default"], [1334, 70, 1082, 12], [1335, 8, 1083, 8, "visible"], [1335, 15, 1083, 15], [1335, 17, 1083, 17, "processingState"], [1335, 32, 1083, 32], [1335, 37, 1083, 37], [1335, 43, 1083, 43], [1335, 47, 1083, 47, "processingState"], [1335, 62, 1083, 62], [1335, 67, 1083, 67], [1335, 74, 1083, 75], [1336, 8, 1084, 8, "transparent"], [1336, 19, 1084, 19], [1337, 8, 1085, 8, "animationType"], [1337, 21, 1085, 21], [1337, 23, 1085, 22], [1337, 29, 1085, 28], [1338, 8, 1085, 28, "children"], [1338, 16, 1085, 28], [1338, 31, 1087, 8], [1338, 35, 1087, 8, "_jsxDevRuntime"], [1338, 49, 1087, 8], [1338, 50, 1087, 8, "jsxDEV"], [1338, 56, 1087, 8], [1338, 58, 1087, 9, "_View"], [1338, 63, 1087, 9], [1338, 64, 1087, 9, "default"], [1338, 71, 1087, 13], [1339, 10, 1087, 14, "style"], [1339, 15, 1087, 19], [1339, 17, 1087, 21, "styles"], [1339, 23, 1087, 27], [1339, 24, 1087, 28, "processingModal"], [1339, 39, 1087, 44], [1340, 10, 1087, 44, "children"], [1340, 18, 1087, 44], [1340, 33, 1088, 10], [1340, 37, 1088, 10, "_jsxDevRuntime"], [1340, 51, 1088, 10], [1340, 52, 1088, 10, "jsxDEV"], [1340, 58, 1088, 10], [1340, 60, 1088, 11, "_View"], [1340, 65, 1088, 11], [1340, 66, 1088, 11, "default"], [1340, 73, 1088, 15], [1341, 12, 1088, 16, "style"], [1341, 17, 1088, 21], [1341, 19, 1088, 23, "styles"], [1341, 25, 1088, 29], [1341, 26, 1088, 30, "processingContent"], [1341, 43, 1088, 48], [1342, 12, 1088, 48, "children"], [1342, 20, 1088, 48], [1342, 36, 1089, 12], [1342, 40, 1089, 12, "_jsxDevRuntime"], [1342, 54, 1089, 12], [1342, 55, 1089, 12, "jsxDEV"], [1342, 61, 1089, 12], [1342, 63, 1089, 13, "_ActivityIndicator"], [1342, 81, 1089, 13], [1342, 82, 1089, 13, "default"], [1342, 89, 1089, 30], [1343, 14, 1089, 31, "size"], [1343, 18, 1089, 35], [1343, 20, 1089, 36], [1343, 27, 1089, 43], [1344, 14, 1089, 44, "color"], [1344, 19, 1089, 49], [1344, 21, 1089, 50], [1345, 12, 1089, 59], [1346, 14, 1089, 59, "fileName"], [1346, 22, 1089, 59], [1346, 24, 1089, 59, "_jsxFileName"], [1346, 36, 1089, 59], [1347, 14, 1089, 59, "lineNumber"], [1347, 24, 1089, 59], [1348, 14, 1089, 59, "columnNumber"], [1348, 26, 1089, 59], [1349, 12, 1089, 59], [1349, 19, 1089, 61], [1349, 20, 1089, 62], [1349, 35, 1091, 12], [1349, 39, 1091, 12, "_jsxDevRuntime"], [1349, 53, 1091, 12], [1349, 54, 1091, 12, "jsxDEV"], [1349, 60, 1091, 12], [1349, 62, 1091, 13, "_Text"], [1349, 67, 1091, 13], [1349, 68, 1091, 13, "default"], [1349, 75, 1091, 17], [1350, 14, 1091, 18, "style"], [1350, 19, 1091, 23], [1350, 21, 1091, 25, "styles"], [1350, 27, 1091, 31], [1350, 28, 1091, 32, "processingTitle"], [1350, 43, 1091, 48], [1351, 14, 1091, 48, "children"], [1351, 22, 1091, 48], [1351, 25, 1092, 15, "processingState"], [1351, 40, 1092, 30], [1351, 45, 1092, 35], [1351, 56, 1092, 46], [1351, 60, 1092, 50], [1351, 80, 1092, 70], [1351, 82, 1093, 15, "processingState"], [1351, 97, 1093, 30], [1351, 102, 1093, 35], [1351, 113, 1093, 46], [1351, 117, 1093, 50], [1351, 146, 1093, 79], [1351, 148, 1094, 15, "processingState"], [1351, 163, 1094, 30], [1351, 168, 1094, 35], [1351, 180, 1094, 47], [1351, 184, 1094, 51], [1351, 216, 1094, 83], [1351, 218, 1095, 15, "processingState"], [1351, 233, 1095, 30], [1351, 238, 1095, 35], [1351, 249, 1095, 46], [1351, 253, 1095, 50], [1351, 275, 1095, 72], [1352, 12, 1095, 72], [1353, 14, 1095, 72, "fileName"], [1353, 22, 1095, 72], [1353, 24, 1095, 72, "_jsxFileName"], [1353, 36, 1095, 72], [1354, 14, 1095, 72, "lineNumber"], [1354, 24, 1095, 72], [1355, 14, 1095, 72, "columnNumber"], [1355, 26, 1095, 72], [1356, 12, 1095, 72], [1356, 19, 1096, 18], [1356, 20, 1096, 19], [1356, 35, 1097, 12], [1356, 39, 1097, 12, "_jsxDevRuntime"], [1356, 53, 1097, 12], [1356, 54, 1097, 12, "jsxDEV"], [1356, 60, 1097, 12], [1356, 62, 1097, 13, "_View"], [1356, 67, 1097, 13], [1356, 68, 1097, 13, "default"], [1356, 75, 1097, 17], [1357, 14, 1097, 18, "style"], [1357, 19, 1097, 23], [1357, 21, 1097, 25, "styles"], [1357, 27, 1097, 31], [1357, 28, 1097, 32, "progressBar"], [1357, 39, 1097, 44], [1358, 14, 1097, 44, "children"], [1358, 22, 1097, 44], [1358, 37, 1098, 14], [1358, 41, 1098, 14, "_jsxDevRuntime"], [1358, 55, 1098, 14], [1358, 56, 1098, 14, "jsxDEV"], [1358, 62, 1098, 14], [1358, 64, 1098, 15, "_View"], [1358, 69, 1098, 15], [1358, 70, 1098, 15, "default"], [1358, 77, 1098, 19], [1359, 16, 1099, 16, "style"], [1359, 21, 1099, 21], [1359, 23, 1099, 23], [1359, 24, 1100, 18, "styles"], [1359, 30, 1100, 24], [1359, 31, 1100, 25, "progressFill"], [1359, 43, 1100, 37], [1359, 45, 1101, 18], [1360, 18, 1101, 20, "width"], [1360, 23, 1101, 25], [1360, 25, 1101, 27], [1360, 28, 1101, 30, "processingProgress"], [1360, 46, 1101, 48], [1361, 16, 1101, 52], [1361, 17, 1101, 53], [1362, 14, 1102, 18], [1363, 16, 1102, 18, "fileName"], [1363, 24, 1102, 18], [1363, 26, 1102, 18, "_jsxFileName"], [1363, 38, 1102, 18], [1364, 16, 1102, 18, "lineNumber"], [1364, 26, 1102, 18], [1365, 16, 1102, 18, "columnNumber"], [1365, 28, 1102, 18], [1366, 14, 1102, 18], [1366, 21, 1103, 15], [1367, 12, 1103, 16], [1368, 14, 1103, 16, "fileName"], [1368, 22, 1103, 16], [1368, 24, 1103, 16, "_jsxFileName"], [1368, 36, 1103, 16], [1369, 14, 1103, 16, "lineNumber"], [1369, 24, 1103, 16], [1370, 14, 1103, 16, "columnNumber"], [1370, 26, 1103, 16], [1371, 12, 1103, 16], [1371, 19, 1104, 18], [1371, 20, 1104, 19], [1371, 35, 1105, 12], [1371, 39, 1105, 12, "_jsxDevRuntime"], [1371, 53, 1105, 12], [1371, 54, 1105, 12, "jsxDEV"], [1371, 60, 1105, 12], [1371, 62, 1105, 13, "_Text"], [1371, 67, 1105, 13], [1371, 68, 1105, 13, "default"], [1371, 75, 1105, 17], [1372, 14, 1105, 18, "style"], [1372, 19, 1105, 23], [1372, 21, 1105, 25, "styles"], [1372, 27, 1105, 31], [1372, 28, 1105, 32, "processingDescription"], [1372, 49, 1105, 54], [1373, 14, 1105, 54, "children"], [1373, 22, 1105, 54], [1373, 25, 1106, 15, "processingState"], [1373, 40, 1106, 30], [1373, 45, 1106, 35], [1373, 56, 1106, 46], [1373, 60, 1106, 50], [1373, 89, 1106, 79], [1373, 91, 1107, 15, "processingState"], [1373, 106, 1107, 30], [1373, 111, 1107, 35], [1373, 122, 1107, 46], [1373, 126, 1107, 50], [1373, 164, 1107, 88], [1373, 166, 1108, 15, "processingState"], [1373, 181, 1108, 30], [1373, 186, 1108, 35], [1373, 198, 1108, 47], [1373, 202, 1108, 51], [1373, 247, 1108, 96], [1373, 249, 1109, 15, "processingState"], [1373, 264, 1109, 30], [1373, 269, 1109, 35], [1373, 280, 1109, 46], [1373, 284, 1109, 50], [1373, 325, 1109, 91], [1374, 12, 1109, 91], [1375, 14, 1109, 91, "fileName"], [1375, 22, 1109, 91], [1375, 24, 1109, 91, "_jsxFileName"], [1375, 36, 1109, 91], [1376, 14, 1109, 91, "lineNumber"], [1376, 24, 1109, 91], [1377, 14, 1109, 91, "columnNumber"], [1377, 26, 1109, 91], [1378, 12, 1109, 91], [1378, 19, 1110, 18], [1378, 20, 1110, 19], [1378, 22, 1111, 13, "processingState"], [1378, 37, 1111, 28], [1378, 42, 1111, 33], [1378, 53, 1111, 44], [1378, 70, 1112, 14], [1378, 74, 1112, 14, "_jsxDevRuntime"], [1378, 88, 1112, 14], [1378, 89, 1112, 14, "jsxDEV"], [1378, 95, 1112, 14], [1378, 97, 1112, 15, "_lucideReactNative"], [1378, 115, 1112, 15], [1378, 116, 1112, 15, "CheckCircle"], [1378, 127, 1112, 26], [1379, 14, 1112, 27, "size"], [1379, 18, 1112, 31], [1379, 20, 1112, 33], [1379, 22, 1112, 36], [1380, 14, 1112, 37, "color"], [1380, 19, 1112, 42], [1380, 21, 1112, 43], [1380, 30, 1112, 52], [1381, 14, 1112, 53, "style"], [1381, 19, 1112, 58], [1381, 21, 1112, 60, "styles"], [1381, 27, 1112, 66], [1381, 28, 1112, 67, "successIcon"], [1382, 12, 1112, 79], [1383, 14, 1112, 79, "fileName"], [1383, 22, 1112, 79], [1383, 24, 1112, 79, "_jsxFileName"], [1383, 36, 1112, 79], [1384, 14, 1112, 79, "lineNumber"], [1384, 24, 1112, 79], [1385, 14, 1112, 79, "columnNumber"], [1385, 26, 1112, 79], [1386, 12, 1112, 79], [1386, 19, 1112, 81], [1386, 20, 1113, 13], [1387, 10, 1113, 13], [1388, 12, 1113, 13, "fileName"], [1388, 20, 1113, 13], [1388, 22, 1113, 13, "_jsxFileName"], [1388, 34, 1113, 13], [1389, 12, 1113, 13, "lineNumber"], [1389, 22, 1113, 13], [1390, 12, 1113, 13, "columnNumber"], [1390, 24, 1113, 13], [1391, 10, 1113, 13], [1391, 17, 1114, 16], [1392, 8, 1114, 17], [1393, 10, 1114, 17, "fileName"], [1393, 18, 1114, 17], [1393, 20, 1114, 17, "_jsxFileName"], [1393, 32, 1114, 17], [1394, 10, 1114, 17, "lineNumber"], [1394, 20, 1114, 17], [1395, 10, 1114, 17, "columnNumber"], [1395, 22, 1114, 17], [1396, 8, 1114, 17], [1396, 15, 1115, 14], [1397, 6, 1115, 15], [1398, 8, 1115, 15, "fileName"], [1398, 16, 1115, 15], [1398, 18, 1115, 15, "_jsxFileName"], [1398, 30, 1115, 15], [1399, 8, 1115, 15, "lineNumber"], [1399, 18, 1115, 15], [1400, 8, 1115, 15, "columnNumber"], [1400, 20, 1115, 15], [1401, 6, 1115, 15], [1401, 13, 1116, 13], [1401, 14, 1116, 14], [1401, 29, 1118, 6], [1401, 33, 1118, 6, "_jsxDevRuntime"], [1401, 47, 1118, 6], [1401, 48, 1118, 6, "jsxDEV"], [1401, 54, 1118, 6], [1401, 56, 1118, 7, "_Modal"], [1401, 62, 1118, 7], [1401, 63, 1118, 7, "default"], [1401, 70, 1118, 12], [1402, 8, 1119, 8, "visible"], [1402, 15, 1119, 15], [1402, 17, 1119, 17, "processingState"], [1402, 32, 1119, 32], [1402, 37, 1119, 37], [1402, 44, 1119, 45], [1403, 8, 1120, 8, "transparent"], [1403, 19, 1120, 19], [1404, 8, 1121, 8, "animationType"], [1404, 21, 1121, 21], [1404, 23, 1121, 22], [1404, 29, 1121, 28], [1405, 8, 1121, 28, "children"], [1405, 16, 1121, 28], [1405, 31, 1123, 8], [1405, 35, 1123, 8, "_jsxDevRuntime"], [1405, 49, 1123, 8], [1405, 50, 1123, 8, "jsxDEV"], [1405, 56, 1123, 8], [1405, 58, 1123, 9, "_View"], [1405, 63, 1123, 9], [1405, 64, 1123, 9, "default"], [1405, 71, 1123, 13], [1406, 10, 1123, 14, "style"], [1406, 15, 1123, 19], [1406, 17, 1123, 21, "styles"], [1406, 23, 1123, 27], [1406, 24, 1123, 28, "processingModal"], [1406, 39, 1123, 44], [1407, 10, 1123, 44, "children"], [1407, 18, 1123, 44], [1407, 33, 1124, 10], [1407, 37, 1124, 10, "_jsxDevRuntime"], [1407, 51, 1124, 10], [1407, 52, 1124, 10, "jsxDEV"], [1407, 58, 1124, 10], [1407, 60, 1124, 11, "_View"], [1407, 65, 1124, 11], [1407, 66, 1124, 11, "default"], [1407, 73, 1124, 15], [1408, 12, 1124, 16, "style"], [1408, 17, 1124, 21], [1408, 19, 1124, 23, "styles"], [1408, 25, 1124, 29], [1408, 26, 1124, 30, "errorContent"], [1408, 38, 1124, 43], [1409, 12, 1124, 43, "children"], [1409, 20, 1124, 43], [1409, 36, 1125, 12], [1409, 40, 1125, 12, "_jsxDevRuntime"], [1409, 54, 1125, 12], [1409, 55, 1125, 12, "jsxDEV"], [1409, 61, 1125, 12], [1409, 63, 1125, 13, "_lucideReactNative"], [1409, 81, 1125, 13], [1409, 82, 1125, 13, "X"], [1409, 83, 1125, 14], [1410, 14, 1125, 15, "size"], [1410, 18, 1125, 19], [1410, 20, 1125, 21], [1410, 22, 1125, 24], [1411, 14, 1125, 25, "color"], [1411, 19, 1125, 30], [1411, 21, 1125, 31], [1412, 12, 1125, 40], [1413, 14, 1125, 40, "fileName"], [1413, 22, 1125, 40], [1413, 24, 1125, 40, "_jsxFileName"], [1413, 36, 1125, 40], [1414, 14, 1125, 40, "lineNumber"], [1414, 24, 1125, 40], [1415, 14, 1125, 40, "columnNumber"], [1415, 26, 1125, 40], [1416, 12, 1125, 40], [1416, 19, 1125, 42], [1416, 20, 1125, 43], [1416, 35, 1126, 12], [1416, 39, 1126, 12, "_jsxDevRuntime"], [1416, 53, 1126, 12], [1416, 54, 1126, 12, "jsxDEV"], [1416, 60, 1126, 12], [1416, 62, 1126, 13, "_Text"], [1416, 67, 1126, 13], [1416, 68, 1126, 13, "default"], [1416, 75, 1126, 17], [1417, 14, 1126, 18, "style"], [1417, 19, 1126, 23], [1417, 21, 1126, 25, "styles"], [1417, 27, 1126, 31], [1417, 28, 1126, 32, "errorTitle"], [1417, 38, 1126, 43], [1418, 14, 1126, 43, "children"], [1418, 22, 1126, 43], [1418, 24, 1126, 44], [1419, 12, 1126, 61], [1420, 14, 1126, 61, "fileName"], [1420, 22, 1126, 61], [1420, 24, 1126, 61, "_jsxFileName"], [1420, 36, 1126, 61], [1421, 14, 1126, 61, "lineNumber"], [1421, 24, 1126, 61], [1422, 14, 1126, 61, "columnNumber"], [1422, 26, 1126, 61], [1423, 12, 1126, 61], [1423, 19, 1126, 67], [1423, 20, 1126, 68], [1423, 35, 1127, 12], [1423, 39, 1127, 12, "_jsxDevRuntime"], [1423, 53, 1127, 12], [1423, 54, 1127, 12, "jsxDEV"], [1423, 60, 1127, 12], [1423, 62, 1127, 13, "_Text"], [1423, 67, 1127, 13], [1423, 68, 1127, 13, "default"], [1423, 75, 1127, 17], [1424, 14, 1127, 18, "style"], [1424, 19, 1127, 23], [1424, 21, 1127, 25, "styles"], [1424, 27, 1127, 31], [1424, 28, 1127, 32, "errorMessage"], [1424, 40, 1127, 45], [1425, 14, 1127, 45, "children"], [1425, 22, 1127, 45], [1425, 24, 1127, 47, "errorMessage"], [1426, 12, 1127, 59], [1427, 14, 1127, 59, "fileName"], [1427, 22, 1127, 59], [1427, 24, 1127, 59, "_jsxFileName"], [1427, 36, 1127, 59], [1428, 14, 1127, 59, "lineNumber"], [1428, 24, 1127, 59], [1429, 14, 1127, 59, "columnNumber"], [1429, 26, 1127, 59], [1430, 12, 1127, 59], [1430, 19, 1127, 66], [1430, 20, 1127, 67], [1430, 35, 1128, 12], [1430, 39, 1128, 12, "_jsxDevRuntime"], [1430, 53, 1128, 12], [1430, 54, 1128, 12, "jsxDEV"], [1430, 60, 1128, 12], [1430, 62, 1128, 13, "_TouchableOpacity"], [1430, 79, 1128, 13], [1430, 80, 1128, 13, "default"], [1430, 87, 1128, 29], [1431, 14, 1129, 14, "onPress"], [1431, 21, 1129, 21], [1431, 23, 1129, 23, "retryCapture"], [1431, 35, 1129, 36], [1432, 14, 1130, 14, "style"], [1432, 19, 1130, 19], [1432, 21, 1130, 21, "styles"], [1432, 27, 1130, 27], [1432, 28, 1130, 28, "primaryButton"], [1432, 41, 1130, 42], [1433, 14, 1130, 42, "children"], [1433, 22, 1130, 42], [1433, 37, 1132, 14], [1433, 41, 1132, 14, "_jsxDevRuntime"], [1433, 55, 1132, 14], [1433, 56, 1132, 14, "jsxDEV"], [1433, 62, 1132, 14], [1433, 64, 1132, 15, "_Text"], [1433, 69, 1132, 15], [1433, 70, 1132, 15, "default"], [1433, 77, 1132, 19], [1434, 16, 1132, 20, "style"], [1434, 21, 1132, 25], [1434, 23, 1132, 27, "styles"], [1434, 29, 1132, 33], [1434, 30, 1132, 34, "primaryButtonText"], [1434, 47, 1132, 52], [1435, 16, 1132, 52, "children"], [1435, 24, 1132, 52], [1435, 26, 1132, 53], [1436, 14, 1132, 62], [1437, 16, 1132, 62, "fileName"], [1437, 24, 1132, 62], [1437, 26, 1132, 62, "_jsxFileName"], [1437, 38, 1132, 62], [1438, 16, 1132, 62, "lineNumber"], [1438, 26, 1132, 62], [1439, 16, 1132, 62, "columnNumber"], [1439, 28, 1132, 62], [1440, 14, 1132, 62], [1440, 21, 1132, 68], [1441, 12, 1132, 69], [1442, 14, 1132, 69, "fileName"], [1442, 22, 1132, 69], [1442, 24, 1132, 69, "_jsxFileName"], [1442, 36, 1132, 69], [1443, 14, 1132, 69, "lineNumber"], [1443, 24, 1132, 69], [1444, 14, 1132, 69, "columnNumber"], [1444, 26, 1132, 69], [1445, 12, 1132, 69], [1445, 19, 1133, 30], [1445, 20, 1133, 31], [1445, 35, 1134, 12], [1445, 39, 1134, 12, "_jsxDevRuntime"], [1445, 53, 1134, 12], [1445, 54, 1134, 12, "jsxDEV"], [1445, 60, 1134, 12], [1445, 62, 1134, 13, "_TouchableOpacity"], [1445, 79, 1134, 13], [1445, 80, 1134, 13, "default"], [1445, 87, 1134, 29], [1446, 14, 1135, 14, "onPress"], [1446, 21, 1135, 21], [1446, 23, 1135, 23, "onCancel"], [1446, 31, 1135, 32], [1447, 14, 1136, 14, "style"], [1447, 19, 1136, 19], [1447, 21, 1136, 21, "styles"], [1447, 27, 1136, 27], [1447, 28, 1136, 28, "secondaryButton"], [1447, 43, 1136, 44], [1448, 14, 1136, 44, "children"], [1448, 22, 1136, 44], [1448, 37, 1138, 14], [1448, 41, 1138, 14, "_jsxDevRuntime"], [1448, 55, 1138, 14], [1448, 56, 1138, 14, "jsxDEV"], [1448, 62, 1138, 14], [1448, 64, 1138, 15, "_Text"], [1448, 69, 1138, 15], [1448, 70, 1138, 15, "default"], [1448, 77, 1138, 19], [1449, 16, 1138, 20, "style"], [1449, 21, 1138, 25], [1449, 23, 1138, 27, "styles"], [1449, 29, 1138, 33], [1449, 30, 1138, 34, "secondaryButtonText"], [1449, 49, 1138, 54], [1450, 16, 1138, 54, "children"], [1450, 24, 1138, 54], [1450, 26, 1138, 55], [1451, 14, 1138, 61], [1452, 16, 1138, 61, "fileName"], [1452, 24, 1138, 61], [1452, 26, 1138, 61, "_jsxFileName"], [1452, 38, 1138, 61], [1453, 16, 1138, 61, "lineNumber"], [1453, 26, 1138, 61], [1454, 16, 1138, 61, "columnNumber"], [1454, 28, 1138, 61], [1455, 14, 1138, 61], [1455, 21, 1138, 67], [1456, 12, 1138, 68], [1457, 14, 1138, 68, "fileName"], [1457, 22, 1138, 68], [1457, 24, 1138, 68, "_jsxFileName"], [1457, 36, 1138, 68], [1458, 14, 1138, 68, "lineNumber"], [1458, 24, 1138, 68], [1459, 14, 1138, 68, "columnNumber"], [1459, 26, 1138, 68], [1460, 12, 1138, 68], [1460, 19, 1139, 30], [1460, 20, 1139, 31], [1461, 10, 1139, 31], [1462, 12, 1139, 31, "fileName"], [1462, 20, 1139, 31], [1462, 22, 1139, 31, "_jsxFileName"], [1462, 34, 1139, 31], [1463, 12, 1139, 31, "lineNumber"], [1463, 22, 1139, 31], [1464, 12, 1139, 31, "columnNumber"], [1464, 24, 1139, 31], [1465, 10, 1139, 31], [1465, 17, 1140, 16], [1466, 8, 1140, 17], [1467, 10, 1140, 17, "fileName"], [1467, 18, 1140, 17], [1467, 20, 1140, 17, "_jsxFileName"], [1467, 32, 1140, 17], [1468, 10, 1140, 17, "lineNumber"], [1468, 20, 1140, 17], [1469, 10, 1140, 17, "columnNumber"], [1469, 22, 1140, 17], [1470, 8, 1140, 17], [1470, 15, 1141, 14], [1471, 6, 1141, 15], [1472, 8, 1141, 15, "fileName"], [1472, 16, 1141, 15], [1472, 18, 1141, 15, "_jsxFileName"], [1472, 30, 1141, 15], [1473, 8, 1141, 15, "lineNumber"], [1473, 18, 1141, 15], [1474, 8, 1141, 15, "columnNumber"], [1474, 20, 1141, 15], [1475, 6, 1141, 15], [1475, 13, 1142, 13], [1475, 14, 1142, 14], [1476, 4, 1142, 14], [1477, 6, 1142, 14, "fileName"], [1477, 14, 1142, 14], [1477, 16, 1142, 14, "_jsxFileName"], [1477, 28, 1142, 14], [1478, 6, 1142, 14, "lineNumber"], [1478, 16, 1142, 14], [1479, 6, 1142, 14, "columnNumber"], [1479, 18, 1142, 14], [1480, 4, 1142, 14], [1480, 11, 1143, 10], [1480, 12, 1143, 11], [1481, 2, 1145, 0], [1482, 2, 1145, 1, "_s"], [1482, 4, 1145, 1], [1482, 5, 51, 24, "EchoCameraWeb"], [1482, 18, 51, 37], [1483, 4, 51, 37], [1483, 12, 58, 42, "useCameraPermissions"], [1483, 44, 58, 62], [1483, 46, 72, 19, "useUpload"], [1483, 64, 72, 28], [1484, 2, 72, 28], [1485, 2, 72, 28, "_c"], [1485, 4, 72, 28], [1485, 7, 51, 24, "EchoCameraWeb"], [1485, 20, 51, 37], [1486, 2, 1146, 0], [1486, 8, 1146, 6, "styles"], [1486, 14, 1146, 12], [1486, 17, 1146, 15, "StyleSheet"], [1486, 36, 1146, 25], [1486, 37, 1146, 26, "create"], [1486, 43, 1146, 32], [1486, 44, 1146, 33], [1487, 4, 1147, 2, "container"], [1487, 13, 1147, 11], [1487, 15, 1147, 13], [1488, 6, 1148, 4, "flex"], [1488, 10, 1148, 8], [1488, 12, 1148, 10], [1488, 13, 1148, 11], [1489, 6, 1149, 4, "backgroundColor"], [1489, 21, 1149, 19], [1489, 23, 1149, 21], [1490, 4, 1150, 2], [1490, 5, 1150, 3], [1491, 4, 1151, 2, "cameraContainer"], [1491, 19, 1151, 17], [1491, 21, 1151, 19], [1492, 6, 1152, 4, "flex"], [1492, 10, 1152, 8], [1492, 12, 1152, 10], [1492, 13, 1152, 11], [1493, 6, 1153, 4, "max<PERSON><PERSON><PERSON>"], [1493, 14, 1153, 12], [1493, 16, 1153, 14], [1493, 19, 1153, 17], [1494, 6, 1154, 4, "alignSelf"], [1494, 15, 1154, 13], [1494, 17, 1154, 15], [1494, 25, 1154, 23], [1495, 6, 1155, 4, "width"], [1495, 11, 1155, 9], [1495, 13, 1155, 11], [1496, 4, 1156, 2], [1496, 5, 1156, 3], [1497, 4, 1157, 2, "camera"], [1497, 10, 1157, 8], [1497, 12, 1157, 10], [1498, 6, 1158, 4, "flex"], [1498, 10, 1158, 8], [1498, 12, 1158, 10], [1499, 4, 1159, 2], [1499, 5, 1159, 3], [1500, 4, 1160, 2, "headerOverlay"], [1500, 17, 1160, 15], [1500, 19, 1160, 17], [1501, 6, 1161, 4, "position"], [1501, 14, 1161, 12], [1501, 16, 1161, 14], [1501, 26, 1161, 24], [1502, 6, 1162, 4, "top"], [1502, 9, 1162, 7], [1502, 11, 1162, 9], [1502, 12, 1162, 10], [1503, 6, 1163, 4, "left"], [1503, 10, 1163, 8], [1503, 12, 1163, 10], [1503, 13, 1163, 11], [1504, 6, 1164, 4, "right"], [1504, 11, 1164, 9], [1504, 13, 1164, 11], [1504, 14, 1164, 12], [1505, 6, 1165, 4, "backgroundColor"], [1505, 21, 1165, 19], [1505, 23, 1165, 21], [1505, 36, 1165, 34], [1506, 6, 1166, 4, "paddingTop"], [1506, 16, 1166, 14], [1506, 18, 1166, 16], [1506, 20, 1166, 18], [1507, 6, 1167, 4, "paddingHorizontal"], [1507, 23, 1167, 21], [1507, 25, 1167, 23], [1507, 27, 1167, 25], [1508, 6, 1168, 4, "paddingBottom"], [1508, 19, 1168, 17], [1508, 21, 1168, 19], [1509, 4, 1169, 2], [1509, 5, 1169, 3], [1510, 4, 1170, 2, "headerContent"], [1510, 17, 1170, 15], [1510, 19, 1170, 17], [1511, 6, 1171, 4, "flexDirection"], [1511, 19, 1171, 17], [1511, 21, 1171, 19], [1511, 26, 1171, 24], [1512, 6, 1172, 4, "justifyContent"], [1512, 20, 1172, 18], [1512, 22, 1172, 20], [1512, 37, 1172, 35], [1513, 6, 1173, 4, "alignItems"], [1513, 16, 1173, 14], [1513, 18, 1173, 16], [1514, 4, 1174, 2], [1514, 5, 1174, 3], [1515, 4, 1175, 2, "headerLeft"], [1515, 14, 1175, 12], [1515, 16, 1175, 14], [1516, 6, 1176, 4, "flex"], [1516, 10, 1176, 8], [1516, 12, 1176, 10], [1517, 4, 1177, 2], [1517, 5, 1177, 3], [1518, 4, 1178, 2, "headerTitle"], [1518, 15, 1178, 13], [1518, 17, 1178, 15], [1519, 6, 1179, 4, "fontSize"], [1519, 14, 1179, 12], [1519, 16, 1179, 14], [1519, 18, 1179, 16], [1520, 6, 1180, 4, "fontWeight"], [1520, 16, 1180, 14], [1520, 18, 1180, 16], [1520, 23, 1180, 21], [1521, 6, 1181, 4, "color"], [1521, 11, 1181, 9], [1521, 13, 1181, 11], [1521, 19, 1181, 17], [1522, 6, 1182, 4, "marginBottom"], [1522, 18, 1182, 16], [1522, 20, 1182, 18], [1523, 4, 1183, 2], [1523, 5, 1183, 3], [1524, 4, 1184, 2, "subtitleRow"], [1524, 15, 1184, 13], [1524, 17, 1184, 15], [1525, 6, 1185, 4, "flexDirection"], [1525, 19, 1185, 17], [1525, 21, 1185, 19], [1525, 26, 1185, 24], [1526, 6, 1186, 4, "alignItems"], [1526, 16, 1186, 14], [1526, 18, 1186, 16], [1526, 26, 1186, 24], [1527, 6, 1187, 4, "marginBottom"], [1527, 18, 1187, 16], [1527, 20, 1187, 18], [1528, 4, 1188, 2], [1528, 5, 1188, 3], [1529, 4, 1189, 2, "webIcon"], [1529, 11, 1189, 9], [1529, 13, 1189, 11], [1530, 6, 1190, 4, "fontSize"], [1530, 14, 1190, 12], [1530, 16, 1190, 14], [1530, 18, 1190, 16], [1531, 6, 1191, 4, "marginRight"], [1531, 17, 1191, 15], [1531, 19, 1191, 17], [1532, 4, 1192, 2], [1532, 5, 1192, 3], [1533, 4, 1193, 2, "headerSubtitle"], [1533, 18, 1193, 16], [1533, 20, 1193, 18], [1534, 6, 1194, 4, "fontSize"], [1534, 14, 1194, 12], [1534, 16, 1194, 14], [1534, 18, 1194, 16], [1535, 6, 1195, 4, "color"], [1535, 11, 1195, 9], [1535, 13, 1195, 11], [1535, 19, 1195, 17], [1536, 6, 1196, 4, "opacity"], [1536, 13, 1196, 11], [1536, 15, 1196, 13], [1537, 4, 1197, 2], [1537, 5, 1197, 3], [1538, 4, 1198, 2, "challengeRow"], [1538, 16, 1198, 14], [1538, 18, 1198, 16], [1539, 6, 1199, 4, "flexDirection"], [1539, 19, 1199, 17], [1539, 21, 1199, 19], [1539, 26, 1199, 24], [1540, 6, 1200, 4, "alignItems"], [1540, 16, 1200, 14], [1540, 18, 1200, 16], [1541, 4, 1201, 2], [1541, 5, 1201, 3], [1542, 4, 1202, 2, "challengeCode"], [1542, 17, 1202, 15], [1542, 19, 1202, 17], [1543, 6, 1203, 4, "fontSize"], [1543, 14, 1203, 12], [1543, 16, 1203, 14], [1543, 18, 1203, 16], [1544, 6, 1204, 4, "color"], [1544, 11, 1204, 9], [1544, 13, 1204, 11], [1544, 19, 1204, 17], [1545, 6, 1205, 4, "marginLeft"], [1545, 16, 1205, 14], [1545, 18, 1205, 16], [1545, 19, 1205, 17], [1546, 6, 1206, 4, "fontFamily"], [1546, 16, 1206, 14], [1546, 18, 1206, 16], [1547, 4, 1207, 2], [1547, 5, 1207, 3], [1548, 4, 1208, 2, "closeButton"], [1548, 15, 1208, 13], [1548, 17, 1208, 15], [1549, 6, 1209, 4, "padding"], [1549, 13, 1209, 11], [1549, 15, 1209, 13], [1550, 4, 1210, 2], [1550, 5, 1210, 3], [1551, 4, 1211, 2, "privacyNotice"], [1551, 17, 1211, 15], [1551, 19, 1211, 17], [1552, 6, 1212, 4, "position"], [1552, 14, 1212, 12], [1552, 16, 1212, 14], [1552, 26, 1212, 24], [1553, 6, 1213, 4, "top"], [1553, 9, 1213, 7], [1553, 11, 1213, 9], [1553, 14, 1213, 12], [1554, 6, 1214, 4, "left"], [1554, 10, 1214, 8], [1554, 12, 1214, 10], [1554, 14, 1214, 12], [1555, 6, 1215, 4, "right"], [1555, 11, 1215, 9], [1555, 13, 1215, 11], [1555, 15, 1215, 13], [1556, 6, 1216, 4, "backgroundColor"], [1556, 21, 1216, 19], [1556, 23, 1216, 21], [1556, 48, 1216, 46], [1557, 6, 1217, 4, "borderRadius"], [1557, 18, 1217, 16], [1557, 20, 1217, 18], [1557, 21, 1217, 19], [1558, 6, 1218, 4, "padding"], [1558, 13, 1218, 11], [1558, 15, 1218, 13], [1558, 17, 1218, 15], [1559, 6, 1219, 4, "flexDirection"], [1559, 19, 1219, 17], [1559, 21, 1219, 19], [1559, 26, 1219, 24], [1560, 6, 1220, 4, "alignItems"], [1560, 16, 1220, 14], [1560, 18, 1220, 16], [1561, 4, 1221, 2], [1561, 5, 1221, 3], [1562, 4, 1222, 2, "privacyText"], [1562, 15, 1222, 13], [1562, 17, 1222, 15], [1563, 6, 1223, 4, "color"], [1563, 11, 1223, 9], [1563, 13, 1223, 11], [1563, 19, 1223, 17], [1564, 6, 1224, 4, "fontSize"], [1564, 14, 1224, 12], [1564, 16, 1224, 14], [1564, 18, 1224, 16], [1565, 6, 1225, 4, "marginLeft"], [1565, 16, 1225, 14], [1565, 18, 1225, 16], [1565, 19, 1225, 17], [1566, 6, 1226, 4, "flex"], [1566, 10, 1226, 8], [1566, 12, 1226, 10], [1567, 4, 1227, 2], [1567, 5, 1227, 3], [1568, 4, 1228, 2, "footer<PERSON><PERSON><PERSON>"], [1568, 17, 1228, 15], [1568, 19, 1228, 17], [1569, 6, 1229, 4, "position"], [1569, 14, 1229, 12], [1569, 16, 1229, 14], [1569, 26, 1229, 24], [1570, 6, 1230, 4, "bottom"], [1570, 12, 1230, 10], [1570, 14, 1230, 12], [1570, 15, 1230, 13], [1571, 6, 1231, 4, "left"], [1571, 10, 1231, 8], [1571, 12, 1231, 10], [1571, 13, 1231, 11], [1572, 6, 1232, 4, "right"], [1572, 11, 1232, 9], [1572, 13, 1232, 11], [1572, 14, 1232, 12], [1573, 6, 1233, 4, "backgroundColor"], [1573, 21, 1233, 19], [1573, 23, 1233, 21], [1573, 36, 1233, 34], [1574, 6, 1234, 4, "paddingBottom"], [1574, 19, 1234, 17], [1574, 21, 1234, 19], [1574, 23, 1234, 21], [1575, 6, 1235, 4, "paddingTop"], [1575, 16, 1235, 14], [1575, 18, 1235, 16], [1575, 20, 1235, 18], [1576, 6, 1236, 4, "alignItems"], [1576, 16, 1236, 14], [1576, 18, 1236, 16], [1577, 4, 1237, 2], [1577, 5, 1237, 3], [1578, 4, 1238, 2, "instruction"], [1578, 15, 1238, 13], [1578, 17, 1238, 15], [1579, 6, 1239, 4, "fontSize"], [1579, 14, 1239, 12], [1579, 16, 1239, 14], [1579, 18, 1239, 16], [1580, 6, 1240, 4, "color"], [1580, 11, 1240, 9], [1580, 13, 1240, 11], [1580, 19, 1240, 17], [1581, 6, 1241, 4, "marginBottom"], [1581, 18, 1241, 16], [1581, 20, 1241, 18], [1582, 4, 1242, 2], [1582, 5, 1242, 3], [1583, 4, 1243, 2, "shutterButton"], [1583, 17, 1243, 15], [1583, 19, 1243, 17], [1584, 6, 1244, 4, "width"], [1584, 11, 1244, 9], [1584, 13, 1244, 11], [1584, 15, 1244, 13], [1585, 6, 1245, 4, "height"], [1585, 12, 1245, 10], [1585, 14, 1245, 12], [1585, 16, 1245, 14], [1586, 6, 1246, 4, "borderRadius"], [1586, 18, 1246, 16], [1586, 20, 1246, 18], [1586, 22, 1246, 20], [1587, 6, 1247, 4, "backgroundColor"], [1587, 21, 1247, 19], [1587, 23, 1247, 21], [1587, 29, 1247, 27], [1588, 6, 1248, 4, "justifyContent"], [1588, 20, 1248, 18], [1588, 22, 1248, 20], [1588, 30, 1248, 28], [1589, 6, 1249, 4, "alignItems"], [1589, 16, 1249, 14], [1589, 18, 1249, 16], [1589, 26, 1249, 24], [1590, 6, 1250, 4, "marginBottom"], [1590, 18, 1250, 16], [1590, 20, 1250, 18], [1590, 22, 1250, 20], [1591, 6, 1251, 4], [1591, 9, 1251, 7, "Platform"], [1591, 26, 1251, 15], [1591, 27, 1251, 16, "select"], [1591, 33, 1251, 22], [1591, 34, 1251, 23], [1592, 8, 1252, 6, "ios"], [1592, 11, 1252, 9], [1592, 13, 1252, 11], [1593, 10, 1253, 8, "shadowColor"], [1593, 21, 1253, 19], [1593, 23, 1253, 21], [1593, 32, 1253, 30], [1594, 10, 1254, 8, "shadowOffset"], [1594, 22, 1254, 20], [1594, 24, 1254, 22], [1595, 12, 1254, 24, "width"], [1595, 17, 1254, 29], [1595, 19, 1254, 31], [1595, 20, 1254, 32], [1596, 12, 1254, 34, "height"], [1596, 18, 1254, 40], [1596, 20, 1254, 42], [1597, 10, 1254, 44], [1597, 11, 1254, 45], [1598, 10, 1255, 8, "shadowOpacity"], [1598, 23, 1255, 21], [1598, 25, 1255, 23], [1598, 28, 1255, 26], [1599, 10, 1256, 8, "shadowRadius"], [1599, 22, 1256, 20], [1599, 24, 1256, 22], [1600, 8, 1257, 6], [1600, 9, 1257, 7], [1601, 8, 1258, 6, "android"], [1601, 15, 1258, 13], [1601, 17, 1258, 15], [1602, 10, 1259, 8, "elevation"], [1602, 19, 1259, 17], [1602, 21, 1259, 19], [1603, 8, 1260, 6], [1603, 9, 1260, 7], [1604, 8, 1261, 6, "web"], [1604, 11, 1261, 9], [1604, 13, 1261, 11], [1605, 10, 1262, 8, "boxShadow"], [1605, 19, 1262, 17], [1605, 21, 1262, 19], [1606, 8, 1263, 6], [1607, 6, 1264, 4], [1607, 7, 1264, 5], [1608, 4, 1265, 2], [1608, 5, 1265, 3], [1609, 4, 1266, 2, "shutterButtonDisabled"], [1609, 25, 1266, 23], [1609, 27, 1266, 25], [1610, 6, 1267, 4, "opacity"], [1610, 13, 1267, 11], [1610, 15, 1267, 13], [1611, 4, 1268, 2], [1611, 5, 1268, 3], [1612, 4, 1269, 2, "shutterInner"], [1612, 16, 1269, 14], [1612, 18, 1269, 16], [1613, 6, 1270, 4, "width"], [1613, 11, 1270, 9], [1613, 13, 1270, 11], [1613, 15, 1270, 13], [1614, 6, 1271, 4, "height"], [1614, 12, 1271, 10], [1614, 14, 1271, 12], [1614, 16, 1271, 14], [1615, 6, 1272, 4, "borderRadius"], [1615, 18, 1272, 16], [1615, 20, 1272, 18], [1615, 22, 1272, 20], [1616, 6, 1273, 4, "backgroundColor"], [1616, 21, 1273, 19], [1616, 23, 1273, 21], [1616, 29, 1273, 27], [1617, 6, 1274, 4, "borderWidth"], [1617, 17, 1274, 15], [1617, 19, 1274, 17], [1617, 20, 1274, 18], [1618, 6, 1275, 4, "borderColor"], [1618, 17, 1275, 15], [1618, 19, 1275, 17], [1619, 4, 1276, 2], [1619, 5, 1276, 3], [1620, 4, 1277, 2, "privacyNote"], [1620, 15, 1277, 13], [1620, 17, 1277, 15], [1621, 6, 1278, 4, "fontSize"], [1621, 14, 1278, 12], [1621, 16, 1278, 14], [1621, 18, 1278, 16], [1622, 6, 1279, 4, "color"], [1622, 11, 1279, 9], [1622, 13, 1279, 11], [1623, 4, 1280, 2], [1623, 5, 1280, 3], [1624, 4, 1281, 2, "processingModal"], [1624, 19, 1281, 17], [1624, 21, 1281, 19], [1625, 6, 1282, 4, "flex"], [1625, 10, 1282, 8], [1625, 12, 1282, 10], [1625, 13, 1282, 11], [1626, 6, 1283, 4, "backgroundColor"], [1626, 21, 1283, 19], [1626, 23, 1283, 21], [1626, 43, 1283, 41], [1627, 6, 1284, 4, "justifyContent"], [1627, 20, 1284, 18], [1627, 22, 1284, 20], [1627, 30, 1284, 28], [1628, 6, 1285, 4, "alignItems"], [1628, 16, 1285, 14], [1628, 18, 1285, 16], [1629, 4, 1286, 2], [1629, 5, 1286, 3], [1630, 4, 1287, 2, "processingContent"], [1630, 21, 1287, 19], [1630, 23, 1287, 21], [1631, 6, 1288, 4, "backgroundColor"], [1631, 21, 1288, 19], [1631, 23, 1288, 21], [1631, 29, 1288, 27], [1632, 6, 1289, 4, "borderRadius"], [1632, 18, 1289, 16], [1632, 20, 1289, 18], [1632, 22, 1289, 20], [1633, 6, 1290, 4, "padding"], [1633, 13, 1290, 11], [1633, 15, 1290, 13], [1633, 17, 1290, 15], [1634, 6, 1291, 4, "width"], [1634, 11, 1291, 9], [1634, 13, 1291, 11], [1634, 18, 1291, 16], [1635, 6, 1292, 4, "max<PERSON><PERSON><PERSON>"], [1635, 14, 1292, 12], [1635, 16, 1292, 14], [1635, 19, 1292, 17], [1636, 6, 1293, 4, "alignItems"], [1636, 16, 1293, 14], [1636, 18, 1293, 16], [1637, 4, 1294, 2], [1637, 5, 1294, 3], [1638, 4, 1295, 2, "processingTitle"], [1638, 19, 1295, 17], [1638, 21, 1295, 19], [1639, 6, 1296, 4, "fontSize"], [1639, 14, 1296, 12], [1639, 16, 1296, 14], [1639, 18, 1296, 16], [1640, 6, 1297, 4, "fontWeight"], [1640, 16, 1297, 14], [1640, 18, 1297, 16], [1640, 23, 1297, 21], [1641, 6, 1298, 4, "color"], [1641, 11, 1298, 9], [1641, 13, 1298, 11], [1641, 22, 1298, 20], [1642, 6, 1299, 4, "marginTop"], [1642, 15, 1299, 13], [1642, 17, 1299, 15], [1642, 19, 1299, 17], [1643, 6, 1300, 4, "marginBottom"], [1643, 18, 1300, 16], [1643, 20, 1300, 18], [1644, 4, 1301, 2], [1644, 5, 1301, 3], [1645, 4, 1302, 2, "progressBar"], [1645, 15, 1302, 13], [1645, 17, 1302, 15], [1646, 6, 1303, 4, "width"], [1646, 11, 1303, 9], [1646, 13, 1303, 11], [1646, 19, 1303, 17], [1647, 6, 1304, 4, "height"], [1647, 12, 1304, 10], [1647, 14, 1304, 12], [1647, 15, 1304, 13], [1648, 6, 1305, 4, "backgroundColor"], [1648, 21, 1305, 19], [1648, 23, 1305, 21], [1648, 32, 1305, 30], [1649, 6, 1306, 4, "borderRadius"], [1649, 18, 1306, 16], [1649, 20, 1306, 18], [1649, 21, 1306, 19], [1650, 6, 1307, 4, "overflow"], [1650, 14, 1307, 12], [1650, 16, 1307, 14], [1650, 24, 1307, 22], [1651, 6, 1308, 4, "marginBottom"], [1651, 18, 1308, 16], [1651, 20, 1308, 18], [1652, 4, 1309, 2], [1652, 5, 1309, 3], [1653, 4, 1310, 2, "progressFill"], [1653, 16, 1310, 14], [1653, 18, 1310, 16], [1654, 6, 1311, 4, "height"], [1654, 12, 1311, 10], [1654, 14, 1311, 12], [1654, 20, 1311, 18], [1655, 6, 1312, 4, "backgroundColor"], [1655, 21, 1312, 19], [1655, 23, 1312, 21], [1655, 32, 1312, 30], [1656, 6, 1313, 4, "borderRadius"], [1656, 18, 1313, 16], [1656, 20, 1313, 18], [1657, 4, 1314, 2], [1657, 5, 1314, 3], [1658, 4, 1315, 2, "processingDescription"], [1658, 25, 1315, 23], [1658, 27, 1315, 25], [1659, 6, 1316, 4, "fontSize"], [1659, 14, 1316, 12], [1659, 16, 1316, 14], [1659, 18, 1316, 16], [1660, 6, 1317, 4, "color"], [1660, 11, 1317, 9], [1660, 13, 1317, 11], [1660, 22, 1317, 20], [1661, 6, 1318, 4, "textAlign"], [1661, 15, 1318, 13], [1661, 17, 1318, 15], [1662, 4, 1319, 2], [1662, 5, 1319, 3], [1663, 4, 1320, 2, "successIcon"], [1663, 15, 1320, 13], [1663, 17, 1320, 15], [1664, 6, 1321, 4, "marginTop"], [1664, 15, 1321, 13], [1664, 17, 1321, 15], [1665, 4, 1322, 2], [1665, 5, 1322, 3], [1666, 4, 1323, 2, "errorContent"], [1666, 16, 1323, 14], [1666, 18, 1323, 16], [1667, 6, 1324, 4, "backgroundColor"], [1667, 21, 1324, 19], [1667, 23, 1324, 21], [1667, 29, 1324, 27], [1668, 6, 1325, 4, "borderRadius"], [1668, 18, 1325, 16], [1668, 20, 1325, 18], [1668, 22, 1325, 20], [1669, 6, 1326, 4, "padding"], [1669, 13, 1326, 11], [1669, 15, 1326, 13], [1669, 17, 1326, 15], [1670, 6, 1327, 4, "width"], [1670, 11, 1327, 9], [1670, 13, 1327, 11], [1670, 18, 1327, 16], [1671, 6, 1328, 4, "max<PERSON><PERSON><PERSON>"], [1671, 14, 1328, 12], [1671, 16, 1328, 14], [1671, 19, 1328, 17], [1672, 6, 1329, 4, "alignItems"], [1672, 16, 1329, 14], [1672, 18, 1329, 16], [1673, 4, 1330, 2], [1673, 5, 1330, 3], [1674, 4, 1331, 2, "errorTitle"], [1674, 14, 1331, 12], [1674, 16, 1331, 14], [1675, 6, 1332, 4, "fontSize"], [1675, 14, 1332, 12], [1675, 16, 1332, 14], [1675, 18, 1332, 16], [1676, 6, 1333, 4, "fontWeight"], [1676, 16, 1333, 14], [1676, 18, 1333, 16], [1676, 23, 1333, 21], [1677, 6, 1334, 4, "color"], [1677, 11, 1334, 9], [1677, 13, 1334, 11], [1677, 22, 1334, 20], [1678, 6, 1335, 4, "marginTop"], [1678, 15, 1335, 13], [1678, 17, 1335, 15], [1678, 19, 1335, 17], [1679, 6, 1336, 4, "marginBottom"], [1679, 18, 1336, 16], [1679, 20, 1336, 18], [1680, 4, 1337, 2], [1680, 5, 1337, 3], [1681, 4, 1338, 2, "errorMessage"], [1681, 16, 1338, 14], [1681, 18, 1338, 16], [1682, 6, 1339, 4, "fontSize"], [1682, 14, 1339, 12], [1682, 16, 1339, 14], [1682, 18, 1339, 16], [1683, 6, 1340, 4, "color"], [1683, 11, 1340, 9], [1683, 13, 1340, 11], [1683, 22, 1340, 20], [1684, 6, 1341, 4, "textAlign"], [1684, 15, 1341, 13], [1684, 17, 1341, 15], [1684, 25, 1341, 23], [1685, 6, 1342, 4, "marginBottom"], [1685, 18, 1342, 16], [1685, 20, 1342, 18], [1686, 4, 1343, 2], [1686, 5, 1343, 3], [1687, 4, 1344, 2, "primaryButton"], [1687, 17, 1344, 15], [1687, 19, 1344, 17], [1688, 6, 1345, 4, "backgroundColor"], [1688, 21, 1345, 19], [1688, 23, 1345, 21], [1688, 32, 1345, 30], [1689, 6, 1346, 4, "paddingHorizontal"], [1689, 23, 1346, 21], [1689, 25, 1346, 23], [1689, 27, 1346, 25], [1690, 6, 1347, 4, "paddingVertical"], [1690, 21, 1347, 19], [1690, 23, 1347, 21], [1690, 25, 1347, 23], [1691, 6, 1348, 4, "borderRadius"], [1691, 18, 1348, 16], [1691, 20, 1348, 18], [1691, 21, 1348, 19], [1692, 6, 1349, 4, "marginTop"], [1692, 15, 1349, 13], [1692, 17, 1349, 15], [1693, 4, 1350, 2], [1693, 5, 1350, 3], [1694, 4, 1351, 2, "primaryButtonText"], [1694, 21, 1351, 19], [1694, 23, 1351, 21], [1695, 6, 1352, 4, "color"], [1695, 11, 1352, 9], [1695, 13, 1352, 11], [1695, 19, 1352, 17], [1696, 6, 1353, 4, "fontSize"], [1696, 14, 1353, 12], [1696, 16, 1353, 14], [1696, 18, 1353, 16], [1697, 6, 1354, 4, "fontWeight"], [1697, 16, 1354, 14], [1697, 18, 1354, 16], [1698, 4, 1355, 2], [1698, 5, 1355, 3], [1699, 4, 1356, 2, "secondaryButton"], [1699, 19, 1356, 17], [1699, 21, 1356, 19], [1700, 6, 1357, 4, "paddingHorizontal"], [1700, 23, 1357, 21], [1700, 25, 1357, 23], [1700, 27, 1357, 25], [1701, 6, 1358, 4, "paddingVertical"], [1701, 21, 1358, 19], [1701, 23, 1358, 21], [1701, 25, 1358, 23], [1702, 6, 1359, 4, "marginTop"], [1702, 15, 1359, 13], [1702, 17, 1359, 15], [1703, 4, 1360, 2], [1703, 5, 1360, 3], [1704, 4, 1361, 2, "secondaryButtonText"], [1704, 23, 1361, 21], [1704, 25, 1361, 23], [1705, 6, 1362, 4, "color"], [1705, 11, 1362, 9], [1705, 13, 1362, 11], [1705, 22, 1362, 20], [1706, 6, 1363, 4, "fontSize"], [1706, 14, 1363, 12], [1706, 16, 1363, 14], [1707, 4, 1364, 2], [1707, 5, 1364, 3], [1708, 4, 1365, 2, "permissionContent"], [1708, 21, 1365, 19], [1708, 23, 1365, 21], [1709, 6, 1366, 4, "flex"], [1709, 10, 1366, 8], [1709, 12, 1366, 10], [1709, 13, 1366, 11], [1710, 6, 1367, 4, "justifyContent"], [1710, 20, 1367, 18], [1710, 22, 1367, 20], [1710, 30, 1367, 28], [1711, 6, 1368, 4, "alignItems"], [1711, 16, 1368, 14], [1711, 18, 1368, 16], [1711, 26, 1368, 24], [1712, 6, 1369, 4, "padding"], [1712, 13, 1369, 11], [1712, 15, 1369, 13], [1713, 4, 1370, 2], [1713, 5, 1370, 3], [1714, 4, 1371, 2, "permissionTitle"], [1714, 19, 1371, 17], [1714, 21, 1371, 19], [1715, 6, 1372, 4, "fontSize"], [1715, 14, 1372, 12], [1715, 16, 1372, 14], [1715, 18, 1372, 16], [1716, 6, 1373, 4, "fontWeight"], [1716, 16, 1373, 14], [1716, 18, 1373, 16], [1716, 23, 1373, 21], [1717, 6, 1374, 4, "color"], [1717, 11, 1374, 9], [1717, 13, 1374, 11], [1717, 22, 1374, 20], [1718, 6, 1375, 4, "marginTop"], [1718, 15, 1375, 13], [1718, 17, 1375, 15], [1718, 19, 1375, 17], [1719, 6, 1376, 4, "marginBottom"], [1719, 18, 1376, 16], [1719, 20, 1376, 18], [1720, 4, 1377, 2], [1720, 5, 1377, 3], [1721, 4, 1378, 2, "permissionDescription"], [1721, 25, 1378, 23], [1721, 27, 1378, 25], [1722, 6, 1379, 4, "fontSize"], [1722, 14, 1379, 12], [1722, 16, 1379, 14], [1722, 18, 1379, 16], [1723, 6, 1380, 4, "color"], [1723, 11, 1380, 9], [1723, 13, 1380, 11], [1723, 22, 1380, 20], [1724, 6, 1381, 4, "textAlign"], [1724, 15, 1381, 13], [1724, 17, 1381, 15], [1724, 25, 1381, 23], [1725, 6, 1382, 4, "marginBottom"], [1725, 18, 1382, 16], [1725, 20, 1382, 18], [1726, 4, 1383, 2], [1726, 5, 1383, 3], [1727, 4, 1384, 2, "loadingText"], [1727, 15, 1384, 13], [1727, 17, 1384, 15], [1728, 6, 1385, 4, "color"], [1728, 11, 1385, 9], [1728, 13, 1385, 11], [1728, 22, 1385, 20], [1729, 6, 1386, 4, "marginTop"], [1729, 15, 1386, 13], [1729, 17, 1386, 15], [1730, 4, 1387, 2], [1730, 5, 1387, 3], [1731, 4, 1388, 2], [1732, 4, 1389, 2, "blurZone"], [1732, 12, 1389, 10], [1732, 14, 1389, 12], [1733, 6, 1390, 4, "position"], [1733, 14, 1390, 12], [1733, 16, 1390, 14], [1733, 26, 1390, 24], [1734, 6, 1391, 4, "overflow"], [1734, 14, 1391, 12], [1734, 16, 1391, 14], [1735, 4, 1392, 2], [1735, 5, 1392, 3], [1736, 4, 1393, 2, "previewChip"], [1736, 15, 1393, 13], [1736, 17, 1393, 15], [1737, 6, 1394, 4, "position"], [1737, 14, 1394, 12], [1737, 16, 1394, 14], [1737, 26, 1394, 24], [1738, 6, 1395, 4, "top"], [1738, 9, 1395, 7], [1738, 11, 1395, 9], [1738, 12, 1395, 10], [1739, 6, 1396, 4, "right"], [1739, 11, 1396, 9], [1739, 13, 1396, 11], [1739, 14, 1396, 12], [1740, 6, 1397, 4, "backgroundColor"], [1740, 21, 1397, 19], [1740, 23, 1397, 21], [1740, 40, 1397, 38], [1741, 6, 1398, 4, "paddingHorizontal"], [1741, 23, 1398, 21], [1741, 25, 1398, 23], [1741, 27, 1398, 25], [1742, 6, 1399, 4, "paddingVertical"], [1742, 21, 1399, 19], [1742, 23, 1399, 21], [1742, 24, 1399, 22], [1743, 6, 1400, 4, "borderRadius"], [1743, 18, 1400, 16], [1743, 20, 1400, 18], [1744, 4, 1401, 2], [1744, 5, 1401, 3], [1745, 4, 1402, 2, "previewChipText"], [1745, 19, 1402, 17], [1745, 21, 1402, 19], [1746, 6, 1403, 4, "color"], [1746, 11, 1403, 9], [1746, 13, 1403, 11], [1746, 19, 1403, 17], [1747, 6, 1404, 4, "fontSize"], [1747, 14, 1404, 12], [1747, 16, 1404, 14], [1747, 18, 1404, 16], [1748, 6, 1405, 4, "fontWeight"], [1748, 16, 1405, 14], [1748, 18, 1405, 16], [1749, 4, 1406, 2], [1750, 2, 1407, 0], [1750, 3, 1407, 1], [1750, 4, 1407, 2], [1751, 2, 1407, 3], [1751, 6, 1407, 3, "_c"], [1751, 8, 1407, 3], [1752, 2, 1407, 3, "$RefreshReg$"], [1752, 14, 1407, 3], [1752, 15, 1407, 3, "_c"], [1752, 17, 1407, 3], [1753, 0, 1407, 3], [1753, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfuE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBsC;mCoBE;wBhBc;OgBI;oFC+C;UDM;8BE8B;SFoD;uDhBa;sBmBC,wBnB;OgBC;GpByB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}