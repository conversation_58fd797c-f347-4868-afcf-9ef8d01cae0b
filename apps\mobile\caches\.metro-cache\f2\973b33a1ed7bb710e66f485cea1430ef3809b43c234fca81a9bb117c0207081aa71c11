{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 910}, "end": {"line": 6, "column": 26, "index": 936}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../skia/web/JsiSkSurface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 937}, "end": {"line": 7, "column": 56, "index": 993}}], "key": "Rzl2stYJw20ym59ZvWEfWE+zKsE=", "exportNames": ["*"]}}, {"name": "../Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 994}, "end": {"line": 8, "column": 39, "index": 1033}}], "key": "SkcN7Zi2IL0pUxWZCaWeI65icek=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SkiaBaseWebView = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _JsiSkSurface = require(_dependencyMap[2], \"../skia/web/JsiSkSurface\");\n  var _Platform = require(_dependencyMap[3], \"../Platform\");\n  function _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function (n) {\n      for (var e = 1; e < arguments.length; e++) {\n        var t = arguments[e];\n        for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n      }\n      return n;\n    }, _extends.apply(null, arguments);\n  }\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  /* global HTMLCanvasElement */\n\n  const pd = _Platform.Platform.PixelRatio;\n  class SkiaBaseWebView extends _react.default.Component {\n    constructor(props) {\n      super(props);\n      _defineProperty(this, \"_surface\", null);\n      _defineProperty(this, \"_unsubscriptions\", []);\n      _defineProperty(this, \"_canvas\", null);\n      _defineProperty(this, \"_canvasRef\", /*#__PURE__*/_react.default.createRef());\n      _defineProperty(this, \"_redrawRequests\", 0);\n      _defineProperty(this, \"requestId\", 0);\n      _defineProperty(this, \"width\", 0);\n      _defineProperty(this, \"height\", 0);\n      _defineProperty(this, \"onLayout\", this.onLayoutEvent.bind(this));\n    }\n    unsubscribeAll() {\n      this._unsubscriptions.forEach(u => u());\n      this._unsubscriptions = [];\n    }\n    onLayoutEvent(evt) {\n      const {\n        CanvasKit\n      } = global;\n      // Reset canvas / surface on layout change\n      const canvas = this._canvasRef.current;\n      if (canvas) {\n        this.width = canvas.clientWidth;\n        this.height = canvas.clientHeight;\n        canvas.width = this.width * pd;\n        canvas.height = this.height * pd;\n        const surface = CanvasKit.MakeWebGLCanvasSurface(canvas);\n        if (!surface) {\n          throw new Error(\"Could not create surface\");\n        }\n        this._surface = new _JsiSkSurface.JsiSkSurface(CanvasKit, surface);\n        this._canvas = this._surface.getCanvas();\n        this.redraw();\n      }\n      // Call onLayout callback if it exists\n      if (this.props.onLayout) {\n        this.props.onLayout(evt);\n      }\n    }\n    getSize() {\n      return {\n        width: this.width,\n        height: this.height\n      };\n    }\n    componentDidMount() {\n      // Start render loop\n      this.tick();\n    }\n    componentDidUpdate() {\n      this.redraw();\n    }\n    componentWillUnmount() {\n      this.unsubscribeAll();\n      cancelAnimationFrame(this.requestId);\n      // eslint-disable-next-line max-len\n      // https://stackoverflow.com/questions/23598471/how-do-i-clean-up-and-unload-a-webgl-canvas-context-from-gpu-after-use\n      // https://developer.mozilla.org/en-US/docs/Web/API/WEBGL_lose_context\n      // We delete the context, only if the context has been intialized\n      if (this._surface) {\n        var _this$_canvasRef$curr;\n        (_this$_canvasRef$curr = this._canvasRef.current) === null || _this$_canvasRef$curr === void 0 || (_this$_canvasRef$curr = _this$_canvasRef$curr.getContext(\"webgl2\")) === null || _this$_canvasRef$curr === void 0 || (_this$_canvasRef$curr = _this$_canvasRef$curr.getExtension(\"WEBGL_lose_context\")) === null || _this$_canvasRef$curr === void 0 || _this$_canvasRef$curr.loseContext();\n      }\n    }\n\n    /**\n     * Creates a snapshot from the canvas in the surface\n     * @param rect Rect to use as bounds. Optional.\n     * @returns An Image object.\n     */\n    makeImageSnapshot(rect) {\n      var _this$_surface, _this$_surface2;\n      this._canvas.clear(CanvasKit.TRANSPARENT);\n      this.renderInCanvas(this._canvas);\n      (_this$_surface = this._surface) === null || _this$_surface === void 0 || _this$_surface.ref.flush();\n      return (_this$_surface2 = this._surface) === null || _this$_surface2 === void 0 ? void 0 : _this$_surface2.makeImageSnapshot(rect);\n    }\n\n    /**\n     * Override to render\n     */\n\n    /**\n     * Sends a redraw request to the native SkiaView.\n     */\n    tick() {\n      if (this._redrawRequests > 0) {\n        this._redrawRequests = 0;\n        if (this._canvas) {\n          var _this$_surface3;\n          const canvas = this._canvas;\n          canvas.clear(Float32Array.of(0, 0, 0, 0));\n          canvas.save();\n          canvas.scale(pd, pd);\n          this.renderInCanvas(canvas);\n          canvas.restore();\n          (_this$_surface3 = this._surface) === null || _this$_surface3 === void 0 || _this$_surface3.ref.flush();\n        }\n      }\n      this.requestId = requestAnimationFrame(this.tick.bind(this));\n    }\n    redraw() {\n      this._redrawRequests++;\n    }\n    render() {\n      const {\n        debug = false,\n        ...viewProps\n      } = this.props;\n      return /*#__PURE__*/_react.default.createElement(_Platform.Platform.View, _extends({}, viewProps, {\n        onLayout: this.onLayout\n      }), /*#__PURE__*/_react.default.createElement(\"canvas\", {\n        ref: this._canvasRef,\n        style: {\n          display: \"flex\",\n          flex: 1\n        }\n      }));\n    }\n  }\n  exports.SkiaBaseWebView = SkiaBaseWebView;\n});", "lineCount": 167, "map": [[7, 2, 6, 0], [7, 6, 6, 0, "_react"], [7, 12, 6, 0], [7, 15, 6, 0, "_interopRequireDefault"], [7, 37, 6, 0], [7, 38, 6, 0, "require"], [7, 45, 6, 0], [7, 46, 6, 0, "_dependencyMap"], [7, 60, 6, 0], [8, 2, 7, 0], [8, 6, 7, 0, "_JsiSkSurface"], [8, 19, 7, 0], [8, 22, 7, 0, "require"], [8, 29, 7, 0], [8, 30, 7, 0, "_dependencyMap"], [8, 44, 7, 0], [9, 2, 8, 0], [9, 6, 8, 0, "_Platform"], [9, 15, 8, 0], [9, 18, 8, 0, "require"], [9, 25, 8, 0], [9, 26, 8, 0, "_dependencyMap"], [9, 40, 8, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_extends"], [10, 19, 1, 17, "_extends"], [10, 20, 1, 17], [10, 22, 1, 20], [11, 4, 1, 22], [11, 11, 1, 29, "_extends"], [11, 19, 1, 37], [11, 22, 1, 40, "Object"], [11, 28, 1, 46], [11, 29, 1, 47, "assign"], [11, 35, 1, 53], [11, 38, 1, 56, "Object"], [11, 44, 1, 62], [11, 45, 1, 63, "assign"], [11, 51, 1, 69], [11, 52, 1, 70, "bind"], [11, 56, 1, 74], [11, 57, 1, 75], [11, 58, 1, 76], [11, 61, 1, 79], [11, 71, 1, 89, "n"], [11, 72, 1, 90], [11, 74, 1, 92], [12, 6, 1, 94], [12, 11, 1, 99], [12, 15, 1, 103, "e"], [12, 16, 1, 104], [12, 19, 1, 107], [12, 20, 1, 108], [12, 22, 1, 110, "e"], [12, 23, 1, 111], [12, 26, 1, 114, "arguments"], [12, 35, 1, 123], [12, 36, 1, 124, "length"], [12, 42, 1, 130], [12, 44, 1, 132, "e"], [12, 45, 1, 133], [12, 47, 1, 135], [12, 49, 1, 137], [13, 8, 1, 139], [13, 12, 1, 143, "t"], [13, 13, 1, 144], [13, 16, 1, 147, "arguments"], [13, 25, 1, 156], [13, 26, 1, 157, "e"], [13, 27, 1, 158], [13, 28, 1, 159], [14, 8, 1, 161], [14, 13, 1, 166], [14, 17, 1, 170, "r"], [14, 18, 1, 171], [14, 22, 1, 175, "t"], [14, 23, 1, 176], [14, 25, 1, 178], [14, 26, 1, 179], [14, 27, 1, 180], [14, 28, 1, 181], [14, 30, 1, 183, "hasOwnProperty"], [14, 44, 1, 197], [14, 45, 1, 198, "call"], [14, 49, 1, 202], [14, 50, 1, 203, "t"], [14, 51, 1, 204], [14, 53, 1, 206, "r"], [14, 54, 1, 207], [14, 55, 1, 208], [14, 60, 1, 213, "n"], [14, 61, 1, 214], [14, 62, 1, 215, "r"], [14, 63, 1, 216], [14, 64, 1, 217], [14, 67, 1, 220, "t"], [14, 68, 1, 221], [14, 69, 1, 222, "r"], [14, 70, 1, 223], [14, 71, 1, 224], [14, 72, 1, 225], [15, 6, 1, 227], [16, 6, 1, 229], [16, 13, 1, 236, "n"], [16, 14, 1, 237], [17, 4, 1, 239], [17, 5, 1, 240], [17, 7, 1, 242, "_extends"], [17, 15, 1, 250], [17, 16, 1, 251, "apply"], [17, 21, 1, 256], [17, 22, 1, 257], [17, 26, 1, 261], [17, 28, 1, 263, "arguments"], [17, 37, 1, 272], [17, 38, 1, 273], [18, 2, 1, 275], [19, 2, 2, 0], [19, 11, 2, 9, "_defineProperty"], [19, 26, 2, 24, "_defineProperty"], [19, 27, 2, 25, "e"], [19, 28, 2, 26], [19, 30, 2, 28, "r"], [19, 31, 2, 29], [19, 33, 2, 31, "t"], [19, 34, 2, 32], [19, 36, 2, 34], [20, 4, 2, 36], [20, 11, 2, 43], [20, 12, 2, 44, "r"], [20, 13, 2, 45], [20, 16, 2, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 30, 2, 62], [20, 31, 2, 63, "r"], [20, 32, 2, 64], [20, 33, 2, 65], [20, 38, 2, 70, "e"], [20, 39, 2, 71], [20, 42, 2, 74, "Object"], [20, 48, 2, 80], [20, 49, 2, 81, "defineProperty"], [20, 63, 2, 95], [20, 64, 2, 96, "e"], [20, 65, 2, 97], [20, 67, 2, 99, "r"], [20, 68, 2, 100], [20, 70, 2, 102], [21, 6, 2, 104, "value"], [21, 11, 2, 109], [21, 13, 2, 111, "t"], [21, 14, 2, 112], [22, 6, 2, 114, "enumerable"], [22, 16, 2, 124], [22, 18, 2, 126], [22, 19, 2, 127], [22, 20, 2, 128], [23, 6, 2, 130, "configurable"], [23, 18, 2, 142], [23, 20, 2, 144], [23, 21, 2, 145], [23, 22, 2, 146], [24, 6, 2, 148, "writable"], [24, 14, 2, 156], [24, 16, 2, 158], [24, 17, 2, 159], [25, 4, 2, 161], [25, 5, 2, 162], [25, 6, 2, 163], [25, 9, 2, 166, "e"], [25, 10, 2, 167], [25, 11, 2, 168, "r"], [25, 12, 2, 169], [25, 13, 2, 170], [25, 16, 2, 173, "t"], [25, 17, 2, 174], [25, 19, 2, 176, "e"], [25, 20, 2, 177], [26, 2, 2, 179], [27, 2, 3, 0], [27, 11, 3, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [27, 25, 3, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [27, 26, 3, 24, "t"], [27, 27, 3, 25], [27, 29, 3, 27], [28, 4, 3, 29], [28, 8, 3, 33, "i"], [28, 9, 3, 34], [28, 12, 3, 37, "_toPrimitive"], [28, 24, 3, 49], [28, 25, 3, 50, "t"], [28, 26, 3, 51], [28, 28, 3, 53], [28, 36, 3, 61], [28, 37, 3, 62], [29, 4, 3, 64], [29, 11, 3, 71], [29, 19, 3, 79], [29, 23, 3, 83], [29, 30, 3, 90, "i"], [29, 31, 3, 91], [29, 34, 3, 94, "i"], [29, 35, 3, 95], [29, 38, 3, 98, "i"], [29, 39, 3, 99], [29, 42, 3, 102], [29, 44, 3, 104], [30, 2, 3, 106], [31, 2, 4, 0], [31, 11, 4, 9, "_toPrimitive"], [31, 23, 4, 21, "_toPrimitive"], [31, 24, 4, 22, "t"], [31, 25, 4, 23], [31, 27, 4, 25, "r"], [31, 28, 4, 26], [31, 30, 4, 28], [32, 4, 4, 30], [32, 8, 4, 34], [32, 16, 4, 42], [32, 20, 4, 46], [32, 27, 4, 53, "t"], [32, 28, 4, 54], [32, 32, 4, 58], [32, 33, 4, 59, "t"], [32, 34, 4, 60], [32, 36, 4, 62], [32, 43, 4, 69, "t"], [32, 44, 4, 70], [33, 4, 4, 72], [33, 8, 4, 76, "e"], [33, 9, 4, 77], [33, 12, 4, 80, "t"], [33, 13, 4, 81], [33, 14, 4, 82, "Symbol"], [33, 20, 4, 88], [33, 21, 4, 89, "toPrimitive"], [33, 32, 4, 100], [33, 33, 4, 101], [34, 4, 4, 103], [34, 8, 4, 107], [34, 13, 4, 112], [34, 14, 4, 113], [34, 19, 4, 118, "e"], [34, 20, 4, 119], [34, 22, 4, 121], [35, 6, 4, 123], [35, 10, 4, 127, "i"], [35, 11, 4, 128], [35, 14, 4, 131, "e"], [35, 15, 4, 132], [35, 16, 4, 133, "call"], [35, 20, 4, 137], [35, 21, 4, 138, "t"], [35, 22, 4, 139], [35, 24, 4, 141, "r"], [35, 25, 4, 142], [35, 29, 4, 146], [35, 38, 4, 155], [35, 39, 4, 156], [36, 6, 4, 158], [36, 10, 4, 162], [36, 18, 4, 170], [36, 22, 4, 174], [36, 29, 4, 181, "i"], [36, 30, 4, 182], [36, 32, 4, 184], [36, 39, 4, 191, "i"], [36, 40, 4, 192], [37, 6, 4, 194], [37, 12, 4, 200], [37, 16, 4, 204, "TypeError"], [37, 25, 4, 213], [37, 26, 4, 214], [37, 72, 4, 260], [37, 73, 4, 261], [38, 4, 4, 263], [39, 4, 4, 265], [39, 11, 4, 272], [39, 12, 4, 273], [39, 20, 4, 281], [39, 25, 4, 286, "r"], [39, 26, 4, 287], [39, 29, 4, 290, "String"], [39, 35, 4, 296], [39, 38, 4, 299, "Number"], [39, 44, 4, 305], [39, 46, 4, 307, "t"], [39, 47, 4, 308], [39, 48, 4, 309], [40, 2, 4, 311], [41, 2, 5, 0], [43, 2, 9, 0], [43, 8, 9, 6, "pd"], [43, 10, 9, 8], [43, 13, 9, 11, "Platform"], [43, 31, 9, 19], [43, 32, 9, 20, "PixelRatio"], [43, 42, 9, 30], [44, 2, 10, 7], [44, 8, 10, 13, "SkiaBaseWebView"], [44, 23, 10, 28], [44, 32, 10, 37, "React"], [44, 46, 10, 42], [44, 47, 10, 43, "Component"], [44, 56, 10, 52], [44, 57, 10, 53], [45, 4, 11, 2, "constructor"], [45, 15, 11, 13, "constructor"], [45, 16, 11, 14, "props"], [45, 21, 11, 19], [45, 23, 11, 21], [46, 6, 12, 4], [46, 11, 12, 9], [46, 12, 12, 10, "props"], [46, 17, 12, 15], [46, 18, 12, 16], [47, 6, 13, 4, "_defineProperty"], [47, 21, 13, 19], [47, 22, 13, 20], [47, 26, 13, 24], [47, 28, 13, 26], [47, 38, 13, 36], [47, 40, 13, 38], [47, 44, 13, 42], [47, 45, 13, 43], [48, 6, 14, 4, "_defineProperty"], [48, 21, 14, 19], [48, 22, 14, 20], [48, 26, 14, 24], [48, 28, 14, 26], [48, 46, 14, 44], [48, 48, 14, 46], [48, 50, 14, 48], [48, 51, 14, 49], [49, 6, 15, 4, "_defineProperty"], [49, 21, 15, 19], [49, 22, 15, 20], [49, 26, 15, 24], [49, 28, 15, 26], [49, 37, 15, 35], [49, 39, 15, 37], [49, 43, 15, 41], [49, 44, 15, 42], [50, 6, 16, 4, "_defineProperty"], [50, 21, 16, 19], [50, 22, 16, 20], [50, 26, 16, 24], [50, 28, 16, 26], [50, 40, 16, 38], [50, 42, 16, 40], [50, 55, 16, 53, "React"], [50, 69, 16, 58], [50, 70, 16, 59, "createRef"], [50, 79, 16, 68], [50, 80, 16, 69], [50, 81, 16, 70], [50, 82, 16, 71], [51, 6, 17, 4, "_defineProperty"], [51, 21, 17, 19], [51, 22, 17, 20], [51, 26, 17, 24], [51, 28, 17, 26], [51, 45, 17, 43], [51, 47, 17, 45], [51, 48, 17, 46], [51, 49, 17, 47], [52, 6, 18, 4, "_defineProperty"], [52, 21, 18, 19], [52, 22, 18, 20], [52, 26, 18, 24], [52, 28, 18, 26], [52, 39, 18, 37], [52, 41, 18, 39], [52, 42, 18, 40], [52, 43, 18, 41], [53, 6, 19, 4, "_defineProperty"], [53, 21, 19, 19], [53, 22, 19, 20], [53, 26, 19, 24], [53, 28, 19, 26], [53, 35, 19, 33], [53, 37, 19, 35], [53, 38, 19, 36], [53, 39, 19, 37], [54, 6, 20, 4, "_defineProperty"], [54, 21, 20, 19], [54, 22, 20, 20], [54, 26, 20, 24], [54, 28, 20, 26], [54, 36, 20, 34], [54, 38, 20, 36], [54, 39, 20, 37], [54, 40, 20, 38], [55, 6, 21, 4, "_defineProperty"], [55, 21, 21, 19], [55, 22, 21, 20], [55, 26, 21, 24], [55, 28, 21, 26], [55, 38, 21, 36], [55, 40, 21, 38], [55, 44, 21, 42], [55, 45, 21, 43, "onLayoutEvent"], [55, 58, 21, 56], [55, 59, 21, 57, "bind"], [55, 63, 21, 61], [55, 64, 21, 62], [55, 68, 21, 66], [55, 69, 21, 67], [55, 70, 21, 68], [56, 4, 22, 2], [57, 4, 23, 2, "unsubscribeAll"], [57, 18, 23, 16, "unsubscribeAll"], [57, 19, 23, 16], [57, 21, 23, 19], [58, 6, 24, 4], [58, 10, 24, 8], [58, 11, 24, 9, "_unsubscriptions"], [58, 27, 24, 25], [58, 28, 24, 26, "for<PERSON>ach"], [58, 35, 24, 33], [58, 36, 24, 34, "u"], [58, 37, 24, 35], [58, 41, 24, 39, "u"], [58, 42, 24, 40], [58, 43, 24, 41], [58, 44, 24, 42], [58, 45, 24, 43], [59, 6, 25, 4], [59, 10, 25, 8], [59, 11, 25, 9, "_unsubscriptions"], [59, 27, 25, 25], [59, 30, 25, 28], [59, 32, 25, 30], [60, 4, 26, 2], [61, 4, 27, 2, "onLayoutEvent"], [61, 17, 27, 15, "onLayoutEvent"], [61, 18, 27, 16, "evt"], [61, 21, 27, 19], [61, 23, 27, 21], [62, 6, 28, 4], [62, 12, 28, 10], [63, 8, 29, 6, "CanvasKit"], [64, 6, 30, 4], [64, 7, 30, 5], [64, 10, 30, 8, "global"], [64, 16, 30, 14], [65, 6, 31, 4], [66, 6, 32, 4], [66, 12, 32, 10, "canvas"], [66, 18, 32, 16], [66, 21, 32, 19], [66, 25, 32, 23], [66, 26, 32, 24, "_canvasRef"], [66, 36, 32, 34], [66, 37, 32, 35, "current"], [66, 44, 32, 42], [67, 6, 33, 4], [67, 10, 33, 8, "canvas"], [67, 16, 33, 14], [67, 18, 33, 16], [68, 8, 34, 6], [68, 12, 34, 10], [68, 13, 34, 11, "width"], [68, 18, 34, 16], [68, 21, 34, 19, "canvas"], [68, 27, 34, 25], [68, 28, 34, 26, "clientWidth"], [68, 39, 34, 37], [69, 8, 35, 6], [69, 12, 35, 10], [69, 13, 35, 11, "height"], [69, 19, 35, 17], [69, 22, 35, 20, "canvas"], [69, 28, 35, 26], [69, 29, 35, 27, "clientHeight"], [69, 41, 35, 39], [70, 8, 36, 6, "canvas"], [70, 14, 36, 12], [70, 15, 36, 13, "width"], [70, 20, 36, 18], [70, 23, 36, 21], [70, 27, 36, 25], [70, 28, 36, 26, "width"], [70, 33, 36, 31], [70, 36, 36, 34, "pd"], [70, 38, 36, 36], [71, 8, 37, 6, "canvas"], [71, 14, 37, 12], [71, 15, 37, 13, "height"], [71, 21, 37, 19], [71, 24, 37, 22], [71, 28, 37, 26], [71, 29, 37, 27, "height"], [71, 35, 37, 33], [71, 38, 37, 36, "pd"], [71, 40, 37, 38], [72, 8, 38, 6], [72, 14, 38, 12, "surface"], [72, 21, 38, 19], [72, 24, 38, 22, "CanvasKit"], [72, 33, 38, 31], [72, 34, 38, 32, "MakeWebGLCanvasSurface"], [72, 56, 38, 54], [72, 57, 38, 55, "canvas"], [72, 63, 38, 61], [72, 64, 38, 62], [73, 8, 39, 6], [73, 12, 39, 10], [73, 13, 39, 11, "surface"], [73, 20, 39, 18], [73, 22, 39, 20], [74, 10, 40, 8], [74, 16, 40, 14], [74, 20, 40, 18, "Error"], [74, 25, 40, 23], [74, 26, 40, 24], [74, 52, 40, 50], [74, 53, 40, 51], [75, 8, 41, 6], [76, 8, 42, 6], [76, 12, 42, 10], [76, 13, 42, 11, "_surface"], [76, 21, 42, 19], [76, 24, 42, 22], [76, 28, 42, 26, "JsiSkSurface"], [76, 54, 42, 38], [76, 55, 42, 39, "CanvasKit"], [76, 64, 42, 48], [76, 66, 42, 50, "surface"], [76, 73, 42, 57], [76, 74, 42, 58], [77, 8, 43, 6], [77, 12, 43, 10], [77, 13, 43, 11, "_canvas"], [77, 20, 43, 18], [77, 23, 43, 21], [77, 27, 43, 25], [77, 28, 43, 26, "_surface"], [77, 36, 43, 34], [77, 37, 43, 35, "get<PERSON>anvas"], [77, 46, 43, 44], [77, 47, 43, 45], [77, 48, 43, 46], [78, 8, 44, 6], [78, 12, 44, 10], [78, 13, 44, 11, "redraw"], [78, 19, 44, 17], [78, 20, 44, 18], [78, 21, 44, 19], [79, 6, 45, 4], [80, 6, 46, 4], [81, 6, 47, 4], [81, 10, 47, 8], [81, 14, 47, 12], [81, 15, 47, 13, "props"], [81, 20, 47, 18], [81, 21, 47, 19, "onLayout"], [81, 29, 47, 27], [81, 31, 47, 29], [82, 8, 48, 6], [82, 12, 48, 10], [82, 13, 48, 11, "props"], [82, 18, 48, 16], [82, 19, 48, 17, "onLayout"], [82, 27, 48, 25], [82, 28, 48, 26, "evt"], [82, 31, 48, 29], [82, 32, 48, 30], [83, 6, 49, 4], [84, 4, 50, 2], [85, 4, 51, 2, "getSize"], [85, 11, 51, 9, "getSize"], [85, 12, 51, 9], [85, 14, 51, 12], [86, 6, 52, 4], [86, 13, 52, 11], [87, 8, 53, 6, "width"], [87, 13, 53, 11], [87, 15, 53, 13], [87, 19, 53, 17], [87, 20, 53, 18, "width"], [87, 25, 53, 23], [88, 8, 54, 6, "height"], [88, 14, 54, 12], [88, 16, 54, 14], [88, 20, 54, 18], [88, 21, 54, 19, "height"], [89, 6, 55, 4], [89, 7, 55, 5], [90, 4, 56, 2], [91, 4, 57, 2, "componentDidMount"], [91, 21, 57, 19, "componentDidMount"], [91, 22, 57, 19], [91, 24, 57, 22], [92, 6, 58, 4], [93, 6, 59, 4], [93, 10, 59, 8], [93, 11, 59, 9, "tick"], [93, 15, 59, 13], [93, 16, 59, 14], [93, 17, 59, 15], [94, 4, 60, 2], [95, 4, 61, 2, "componentDidUpdate"], [95, 22, 61, 20, "componentDidUpdate"], [95, 23, 61, 20], [95, 25, 61, 23], [96, 6, 62, 4], [96, 10, 62, 8], [96, 11, 62, 9, "redraw"], [96, 17, 62, 15], [96, 18, 62, 16], [96, 19, 62, 17], [97, 4, 63, 2], [98, 4, 64, 2, "componentWillUnmount"], [98, 24, 64, 22, "componentWillUnmount"], [98, 25, 64, 22], [98, 27, 64, 25], [99, 6, 65, 4], [99, 10, 65, 8], [99, 11, 65, 9, "unsubscribeAll"], [99, 25, 65, 23], [99, 26, 65, 24], [99, 27, 65, 25], [100, 6, 66, 4, "cancelAnimationFrame"], [100, 26, 66, 24], [100, 27, 66, 25], [100, 31, 66, 29], [100, 32, 66, 30, "requestId"], [100, 41, 66, 39], [100, 42, 66, 40], [101, 6, 67, 4], [102, 6, 68, 4], [103, 6, 69, 4], [104, 6, 70, 4], [105, 6, 71, 4], [105, 10, 71, 8], [105, 14, 71, 12], [105, 15, 71, 13, "_surface"], [105, 23, 71, 21], [105, 25, 71, 23], [106, 8, 72, 6], [106, 12, 72, 10, "_this$_canvasRef$curr"], [106, 33, 72, 31], [107, 8, 73, 6], [107, 9, 73, 7, "_this$_canvasRef$curr"], [107, 30, 73, 28], [107, 33, 73, 31], [107, 37, 73, 35], [107, 38, 73, 36, "_canvasRef"], [107, 48, 73, 46], [107, 49, 73, 47, "current"], [107, 56, 73, 54], [107, 62, 73, 60], [107, 66, 73, 64], [107, 70, 73, 68, "_this$_canvasRef$curr"], [107, 91, 73, 89], [107, 96, 73, 94], [107, 101, 73, 99], [107, 102, 73, 100], [107, 106, 73, 104], [107, 107, 73, 105, "_this$_canvasRef$curr"], [107, 128, 73, 126], [107, 131, 73, 129, "_this$_canvasRef$curr"], [107, 152, 73, 150], [107, 153, 73, 151, "getContext"], [107, 163, 73, 161], [107, 164, 73, 162], [107, 172, 73, 170], [107, 173, 73, 171], [107, 179, 73, 177], [107, 183, 73, 181], [107, 187, 73, 185, "_this$_canvasRef$curr"], [107, 208, 73, 206], [107, 213, 73, 211], [107, 218, 73, 216], [107, 219, 73, 217], [107, 223, 73, 221], [107, 224, 73, 222, "_this$_canvasRef$curr"], [107, 245, 73, 243], [107, 248, 73, 246, "_this$_canvasRef$curr"], [107, 269, 73, 267], [107, 270, 73, 268, "getExtension"], [107, 282, 73, 280], [107, 283, 73, 281], [107, 303, 73, 301], [107, 304, 73, 302], [107, 310, 73, 308], [107, 314, 73, 312], [107, 318, 73, 316, "_this$_canvasRef$curr"], [107, 339, 73, 337], [107, 344, 73, 342], [107, 349, 73, 347], [107, 350, 73, 348], [107, 354, 73, 352, "_this$_canvasRef$curr"], [107, 375, 73, 373], [107, 376, 73, 374, "loseContext"], [107, 387, 73, 385], [107, 388, 73, 386], [107, 389, 73, 387], [108, 6, 74, 4], [109, 4, 75, 2], [111, 4, 77, 2], [112, 0, 78, 0], [113, 0, 79, 0], [114, 0, 80, 0], [115, 0, 81, 0], [116, 4, 82, 2, "makeImageSnapshot"], [116, 21, 82, 19, "makeImageSnapshot"], [116, 22, 82, 20, "rect"], [116, 26, 82, 24], [116, 28, 82, 26], [117, 6, 83, 4], [117, 10, 83, 8, "_this$_surface"], [117, 24, 83, 22], [117, 26, 83, 24, "_this$_surface2"], [117, 41, 83, 39], [118, 6, 84, 4], [118, 10, 84, 8], [118, 11, 84, 9, "_canvas"], [118, 18, 84, 16], [118, 19, 84, 17, "clear"], [118, 24, 84, 22], [118, 25, 84, 23, "CanvasKit"], [118, 34, 84, 32], [118, 35, 84, 33, "TRANSPARENT"], [118, 46, 84, 44], [118, 47, 84, 45], [119, 6, 85, 4], [119, 10, 85, 8], [119, 11, 85, 9, "renderInCanvas"], [119, 25, 85, 23], [119, 26, 85, 24], [119, 30, 85, 28], [119, 31, 85, 29, "_canvas"], [119, 38, 85, 36], [119, 39, 85, 37], [120, 6, 86, 4], [120, 7, 86, 5, "_this$_surface"], [120, 21, 86, 19], [120, 24, 86, 22], [120, 28, 86, 26], [120, 29, 86, 27, "_surface"], [120, 37, 86, 35], [120, 43, 86, 41], [120, 47, 86, 45], [120, 51, 86, 49, "_this$_surface"], [120, 65, 86, 63], [120, 70, 86, 68], [120, 75, 86, 73], [120, 76, 86, 74], [120, 80, 86, 78, "_this$_surface"], [120, 94, 86, 92], [120, 95, 86, 93, "ref"], [120, 98, 86, 96], [120, 99, 86, 97, "flush"], [120, 104, 86, 102], [120, 105, 86, 103], [120, 106, 86, 104], [121, 6, 87, 4], [121, 13, 87, 11], [121, 14, 87, 12, "_this$_surface2"], [121, 29, 87, 27], [121, 32, 87, 30], [121, 36, 87, 34], [121, 37, 87, 35, "_surface"], [121, 45, 87, 43], [121, 51, 87, 49], [121, 55, 87, 53], [121, 59, 87, 57, "_this$_surface2"], [121, 74, 87, 72], [121, 79, 87, 77], [121, 84, 87, 82], [121, 85, 87, 83], [121, 88, 87, 86], [121, 93, 87, 91], [121, 94, 87, 92], [121, 97, 87, 95, "_this$_surface2"], [121, 112, 87, 110], [121, 113, 87, 111, "makeImageSnapshot"], [121, 130, 87, 128], [121, 131, 87, 129, "rect"], [121, 135, 87, 133], [121, 136, 87, 134], [122, 4, 88, 2], [124, 4, 90, 2], [125, 0, 91, 0], [126, 0, 92, 0], [128, 4, 94, 2], [129, 0, 95, 0], [130, 0, 96, 0], [131, 4, 97, 2, "tick"], [131, 8, 97, 6, "tick"], [131, 9, 97, 6], [131, 11, 97, 9], [132, 6, 98, 4], [132, 10, 98, 8], [132, 14, 98, 12], [132, 15, 98, 13, "_redrawRequests"], [132, 30, 98, 28], [132, 33, 98, 31], [132, 34, 98, 32], [132, 36, 98, 34], [133, 8, 99, 6], [133, 12, 99, 10], [133, 13, 99, 11, "_redrawRequests"], [133, 28, 99, 26], [133, 31, 99, 29], [133, 32, 99, 30], [134, 8, 100, 6], [134, 12, 100, 10], [134, 16, 100, 14], [134, 17, 100, 15, "_canvas"], [134, 24, 100, 22], [134, 26, 100, 24], [135, 10, 101, 8], [135, 14, 101, 12, "_this$_surface3"], [135, 29, 101, 27], [136, 10, 102, 8], [136, 16, 102, 14, "canvas"], [136, 22, 102, 20], [136, 25, 102, 23], [136, 29, 102, 27], [136, 30, 102, 28, "_canvas"], [136, 37, 102, 35], [137, 10, 103, 8, "canvas"], [137, 16, 103, 14], [137, 17, 103, 15, "clear"], [137, 22, 103, 20], [137, 23, 103, 21, "Float32Array"], [137, 35, 103, 33], [137, 36, 103, 34, "of"], [137, 38, 103, 36], [137, 39, 103, 37], [137, 40, 103, 38], [137, 42, 103, 40], [137, 43, 103, 41], [137, 45, 103, 43], [137, 46, 103, 44], [137, 48, 103, 46], [137, 49, 103, 47], [137, 50, 103, 48], [137, 51, 103, 49], [138, 10, 104, 8, "canvas"], [138, 16, 104, 14], [138, 17, 104, 15, "save"], [138, 21, 104, 19], [138, 22, 104, 20], [138, 23, 104, 21], [139, 10, 105, 8, "canvas"], [139, 16, 105, 14], [139, 17, 105, 15, "scale"], [139, 22, 105, 20], [139, 23, 105, 21, "pd"], [139, 25, 105, 23], [139, 27, 105, 25, "pd"], [139, 29, 105, 27], [139, 30, 105, 28], [140, 10, 106, 8], [140, 14, 106, 12], [140, 15, 106, 13, "renderInCanvas"], [140, 29, 106, 27], [140, 30, 106, 28, "canvas"], [140, 36, 106, 34], [140, 37, 106, 35], [141, 10, 107, 8, "canvas"], [141, 16, 107, 14], [141, 17, 107, 15, "restore"], [141, 24, 107, 22], [141, 25, 107, 23], [141, 26, 107, 24], [142, 10, 108, 8], [142, 11, 108, 9, "_this$_surface3"], [142, 26, 108, 24], [142, 29, 108, 27], [142, 33, 108, 31], [142, 34, 108, 32, "_surface"], [142, 42, 108, 40], [142, 48, 108, 46], [142, 52, 108, 50], [142, 56, 108, 54, "_this$_surface3"], [142, 71, 108, 69], [142, 76, 108, 74], [142, 81, 108, 79], [142, 82, 108, 80], [142, 86, 108, 84, "_this$_surface3"], [142, 101, 108, 99], [142, 102, 108, 100, "ref"], [142, 105, 108, 103], [142, 106, 108, 104, "flush"], [142, 111, 108, 109], [142, 112, 108, 110], [142, 113, 108, 111], [143, 8, 109, 6], [144, 6, 110, 4], [145, 6, 111, 4], [145, 10, 111, 8], [145, 11, 111, 9, "requestId"], [145, 20, 111, 18], [145, 23, 111, 21, "requestAnimationFrame"], [145, 44, 111, 42], [145, 45, 111, 43], [145, 49, 111, 47], [145, 50, 111, 48, "tick"], [145, 54, 111, 52], [145, 55, 111, 53, "bind"], [145, 59, 111, 57], [145, 60, 111, 58], [145, 64, 111, 62], [145, 65, 111, 63], [145, 66, 111, 64], [146, 4, 112, 2], [147, 4, 113, 2, "redraw"], [147, 10, 113, 8, "redraw"], [147, 11, 113, 8], [147, 13, 113, 11], [148, 6, 114, 4], [148, 10, 114, 8], [148, 11, 114, 9, "_redrawRequests"], [148, 26, 114, 24], [148, 28, 114, 26], [149, 4, 115, 2], [150, 4, 116, 2, "render"], [150, 10, 116, 8, "render"], [150, 11, 116, 8], [150, 13, 116, 11], [151, 6, 117, 4], [151, 12, 117, 10], [152, 8, 118, 6, "debug"], [152, 13, 118, 11], [152, 16, 118, 14], [152, 21, 118, 19], [153, 8, 119, 6], [153, 11, 119, 9, "viewProps"], [154, 6, 120, 4], [154, 7, 120, 5], [154, 10, 120, 8], [154, 14, 120, 12], [154, 15, 120, 13, "props"], [154, 20, 120, 18], [155, 6, 121, 4], [155, 13, 121, 11], [155, 26, 121, 24, "React"], [155, 40, 121, 29], [155, 41, 121, 30, "createElement"], [155, 54, 121, 43], [155, 55, 121, 44, "Platform"], [155, 73, 121, 52], [155, 74, 121, 53, "View"], [155, 78, 121, 57], [155, 80, 121, 59, "_extends"], [155, 88, 121, 67], [155, 89, 121, 68], [155, 90, 121, 69], [155, 91, 121, 70], [155, 93, 121, 72, "viewProps"], [155, 102, 121, 81], [155, 104, 121, 83], [156, 8, 122, 6, "onLayout"], [156, 16, 122, 14], [156, 18, 122, 16], [156, 22, 122, 20], [156, 23, 122, 21, "onLayout"], [157, 6, 123, 4], [157, 7, 123, 5], [157, 8, 123, 6], [157, 10, 123, 8], [157, 23, 123, 21, "React"], [157, 37, 123, 26], [157, 38, 123, 27, "createElement"], [157, 51, 123, 40], [157, 52, 123, 41], [157, 60, 123, 49], [157, 62, 123, 51], [158, 8, 124, 6, "ref"], [158, 11, 124, 9], [158, 13, 124, 11], [158, 17, 124, 15], [158, 18, 124, 16, "_canvasRef"], [158, 28, 124, 26], [159, 8, 125, 6, "style"], [159, 13, 125, 11], [159, 15, 125, 13], [160, 10, 126, 8, "display"], [160, 17, 126, 15], [160, 19, 126, 17], [160, 25, 126, 23], [161, 10, 127, 8, "flex"], [161, 14, 127, 12], [161, 16, 127, 14], [162, 8, 128, 6], [163, 6, 129, 4], [163, 7, 129, 5], [163, 8, 129, 6], [163, 9, 129, 7], [164, 4, 130, 2], [165, 2, 131, 0], [166, 2, 131, 1, "exports"], [166, 9, 131, 1], [166, 10, 131, 1, "SkiaBaseWebView"], [166, 25, 131, 1], [166, 28, 131, 1, "SkiaBaseWebView"], [166, 43, 131, 1], [167, 0, 131, 1], [167, 3]], "functionMap": {"names": ["_extends", "<anonymous>", "<global>", "_defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "SkiaBaseWebView", "constructor", "unsubscribeAll", "_unsubscriptions.forEach$argument_0", "onLayoutEvent", "getSize", "componentDidMount", "componentDidUpdate", "componentWillUnmount", "makeImageSnapshot", "tick", "redraw", "render"], "mappings": "AAA,+EC,iKD,oCE;ACC,oLD;AEC,2GF;AGC,wTH;OIM;ECC;GDW;EEC;kCCC,QD;GFE;EIC;GJuB;EKC;GLK;EMC;GNG;EOC;GPE;EQC;GRW;ESO;GTM;EUS;GVe;EWC;GXE;EYC;GZc;CJC"}}, "type": "js/module"}]}