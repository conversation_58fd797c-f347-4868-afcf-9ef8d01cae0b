{"dependencies": [{"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 48, "index": 650}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CanvasKitWebGLBufferImpl = void 0;\n  var _types = require(_dependencyMap[0], \"../types\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class CanvasKitWebGLBufferImpl extends _types.CanvasKitWebGLBuffer {\n    constructor(surface, source) {\n      super();\n      this.surface = surface;\n      this.source = source;\n      _defineProperty(this, \"image\", null);\n    }\n    toImage() {\n      if (this.image === null) {\n        this.image = this.surface.makeImageFromTextureSource(this.source);\n      }\n      if (this.image === null) {\n        throw new Error(\"Failed to create image from texture source\");\n      }\n      this.surface.updateTextureFromSource(this.image, this.source);\n      return this.image;\n    }\n  }\n  exports.CanvasKitWebGLBufferImpl = CanvasKitWebGLBufferImpl;\n});", "lineCount": 48, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_types"], [6, 12, 4, 0], [6, 15, 4, 0, "require"], [6, 22, 4, 0], [6, 23, 4, 0, "_dependencyMap"], [6, 37, 4, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "e"], [7, 28, 1, 26], [7, 30, 1, 28, "r"], [7, 31, 1, 29], [7, 33, 1, 31, "t"], [7, 34, 1, 32], [7, 36, 1, 34], [8, 4, 1, 36], [8, 11, 1, 43], [8, 12, 1, 44, "r"], [8, 13, 1, 45], [8, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 30, 1, 62], [8, 31, 1, 63, "r"], [8, 32, 1, 64], [8, 33, 1, 65], [8, 38, 1, 70, "e"], [8, 39, 1, 71], [8, 42, 1, 74, "Object"], [8, 48, 1, 80], [8, 49, 1, 81, "defineProperty"], [8, 63, 1, 95], [8, 64, 1, 96, "e"], [8, 65, 1, 97], [8, 67, 1, 99, "r"], [8, 68, 1, 100], [8, 70, 1, 102], [9, 6, 1, 104, "value"], [9, 11, 1, 109], [9, 13, 1, 111, "t"], [9, 14, 1, 112], [10, 6, 1, 114, "enumerable"], [10, 16, 1, 124], [10, 18, 1, 126], [10, 19, 1, 127], [10, 20, 1, 128], [11, 6, 1, 130, "configurable"], [11, 18, 1, 142], [11, 20, 1, 144], [11, 21, 1, 145], [11, 22, 1, 146], [12, 6, 1, 148, "writable"], [12, 14, 1, 156], [12, 16, 1, 158], [12, 17, 1, 159], [13, 4, 1, 161], [13, 5, 1, 162], [13, 6, 1, 163], [13, 9, 1, 166, "e"], [13, 10, 1, 167], [13, 11, 1, 168, "r"], [13, 12, 1, 169], [13, 13, 1, 170], [13, 16, 1, 173, "t"], [13, 17, 1, 174], [13, 19, 1, 176, "e"], [13, 20, 1, 177], [14, 2, 1, 179], [15, 2, 2, 0], [15, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 26, 2, 24, "t"], [15, 27, 2, 25], [15, 29, 2, 27], [16, 4, 2, 29], [16, 8, 2, 33, "i"], [16, 9, 2, 34], [16, 12, 2, 37, "_toPrimitive"], [16, 24, 2, 49], [16, 25, 2, 50, "t"], [16, 26, 2, 51], [16, 28, 2, 53], [16, 36, 2, 61], [16, 37, 2, 62], [17, 4, 2, 64], [17, 11, 2, 71], [17, 19, 2, 79], [17, 23, 2, 83], [17, 30, 2, 90, "i"], [17, 31, 2, 91], [17, 34, 2, 94, "i"], [17, 35, 2, 95], [17, 38, 2, 98, "i"], [17, 39, 2, 99], [17, 42, 2, 102], [17, 44, 2, 104], [18, 2, 2, 106], [19, 2, 3, 0], [19, 11, 3, 9, "_toPrimitive"], [19, 23, 3, 21, "_toPrimitive"], [19, 24, 3, 22, "t"], [19, 25, 3, 23], [19, 27, 3, 25, "r"], [19, 28, 3, 26], [19, 30, 3, 28], [20, 4, 3, 30], [20, 8, 3, 34], [20, 16, 3, 42], [20, 20, 3, 46], [20, 27, 3, 53, "t"], [20, 28, 3, 54], [20, 32, 3, 58], [20, 33, 3, 59, "t"], [20, 34, 3, 60], [20, 36, 3, 62], [20, 43, 3, 69, "t"], [20, 44, 3, 70], [21, 4, 3, 72], [21, 8, 3, 76, "e"], [21, 9, 3, 77], [21, 12, 3, 80, "t"], [21, 13, 3, 81], [21, 14, 3, 82, "Symbol"], [21, 20, 3, 88], [21, 21, 3, 89, "toPrimitive"], [21, 32, 3, 100], [21, 33, 3, 101], [22, 4, 3, 103], [22, 8, 3, 107], [22, 13, 3, 112], [22, 14, 3, 113], [22, 19, 3, 118, "e"], [22, 20, 3, 119], [22, 22, 3, 121], [23, 6, 3, 123], [23, 10, 3, 127, "i"], [23, 11, 3, 128], [23, 14, 3, 131, "e"], [23, 15, 3, 132], [23, 16, 3, 133, "call"], [23, 20, 3, 137], [23, 21, 3, 138, "t"], [23, 22, 3, 139], [23, 24, 3, 141, "r"], [23, 25, 3, 142], [23, 29, 3, 146], [23, 38, 3, 155], [23, 39, 3, 156], [24, 6, 3, 158], [24, 10, 3, 162], [24, 18, 3, 170], [24, 22, 3, 174], [24, 29, 3, 181, "i"], [24, 30, 3, 182], [24, 32, 3, 184], [24, 39, 3, 191, "i"], [24, 40, 3, 192], [25, 6, 3, 194], [25, 12, 3, 200], [25, 16, 3, 204, "TypeError"], [25, 25, 3, 213], [25, 26, 3, 214], [25, 72, 3, 260], [25, 73, 3, 261], [26, 4, 3, 263], [27, 4, 3, 265], [27, 11, 3, 272], [27, 12, 3, 273], [27, 20, 3, 281], [27, 25, 3, 286, "r"], [27, 26, 3, 287], [27, 29, 3, 290, "String"], [27, 35, 3, 296], [27, 38, 3, 299, "Number"], [27, 44, 3, 305], [27, 46, 3, 307, "t"], [27, 47, 3, 308], [27, 48, 3, 309], [28, 2, 3, 311], [29, 2, 5, 7], [29, 8, 5, 13, "CanvasKitWebGLBufferImpl"], [29, 32, 5, 37], [29, 41, 5, 46, "CanvasKitWebGLBuffer"], [29, 68, 5, 66], [29, 69, 5, 67], [30, 4, 6, 2, "constructor"], [30, 15, 6, 13, "constructor"], [30, 16, 6, 14, "surface"], [30, 23, 6, 21], [30, 25, 6, 23, "source"], [30, 31, 6, 29], [30, 33, 6, 31], [31, 6, 7, 4], [31, 11, 7, 9], [31, 12, 7, 10], [31, 13, 7, 11], [32, 6, 8, 4], [32, 10, 8, 8], [32, 11, 8, 9, "surface"], [32, 18, 8, 16], [32, 21, 8, 19, "surface"], [32, 28, 8, 26], [33, 6, 9, 4], [33, 10, 9, 8], [33, 11, 9, 9, "source"], [33, 17, 9, 15], [33, 20, 9, 18, "source"], [33, 26, 9, 24], [34, 6, 10, 4, "_defineProperty"], [34, 21, 10, 19], [34, 22, 10, 20], [34, 26, 10, 24], [34, 28, 10, 26], [34, 35, 10, 33], [34, 37, 10, 35], [34, 41, 10, 39], [34, 42, 10, 40], [35, 4, 11, 2], [36, 4, 12, 2, "toImage"], [36, 11, 12, 9, "toImage"], [36, 12, 12, 9], [36, 14, 12, 12], [37, 6, 13, 4], [37, 10, 13, 8], [37, 14, 13, 12], [37, 15, 13, 13, "image"], [37, 20, 13, 18], [37, 25, 13, 23], [37, 29, 13, 27], [37, 31, 13, 29], [38, 8, 14, 6], [38, 12, 14, 10], [38, 13, 14, 11, "image"], [38, 18, 14, 16], [38, 21, 14, 19], [38, 25, 14, 23], [38, 26, 14, 24, "surface"], [38, 33, 14, 31], [38, 34, 14, 32, "makeImageFromTextureSource"], [38, 60, 14, 58], [38, 61, 14, 59], [38, 65, 14, 63], [38, 66, 14, 64, "source"], [38, 72, 14, 70], [38, 73, 14, 71], [39, 6, 15, 4], [40, 6, 16, 4], [40, 10, 16, 8], [40, 14, 16, 12], [40, 15, 16, 13, "image"], [40, 20, 16, 18], [40, 25, 16, 23], [40, 29, 16, 27], [40, 31, 16, 29], [41, 8, 17, 6], [41, 14, 17, 12], [41, 18, 17, 16, "Error"], [41, 23, 17, 21], [41, 24, 17, 22], [41, 68, 17, 66], [41, 69, 17, 67], [42, 6, 18, 4], [43, 6, 19, 4], [43, 10, 19, 8], [43, 11, 19, 9, "surface"], [43, 18, 19, 16], [43, 19, 19, 17, "updateTextureFromSource"], [43, 42, 19, 40], [43, 43, 19, 41], [43, 47, 19, 45], [43, 48, 19, 46, "image"], [43, 53, 19, 51], [43, 55, 19, 53], [43, 59, 19, 57], [43, 60, 19, 58, "source"], [43, 66, 19, 64], [43, 67, 19, 65], [44, 6, 20, 4], [44, 13, 20, 11], [44, 17, 20, 15], [44, 18, 20, 16, "image"], [44, 23, 20, 21], [45, 4, 21, 2], [46, 2, 22, 0], [47, 2, 22, 1, "exports"], [47, 9, 22, 1], [47, 10, 22, 1, "CanvasKitWebGLBufferImpl"], [47, 34, 22, 1], [47, 37, 22, 1, "CanvasKitWebGLBufferImpl"], [47, 61, 22, 1], [48, 0, 22, 1], [48, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "CanvasKitWebGLBufferImpl", "constructor", "toImage"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGE;ECC;GDK;EEC;GFS;CHC"}}, "type": "js/module"}]}