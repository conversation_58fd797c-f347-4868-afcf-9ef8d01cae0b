{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 639}, "end": {"line": 5, "column": 44, "index": 683}}], "key": "dcm9+mDiNyWsKSxtz4x9+jafYQw=", "exportNames": ["*"]}}, {"name": "./JsiSkPicture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 684}, "end": {"line": 6, "column": 46, "index": 730}}], "key": "/AN6zwESuLHK8aJTwDD/mo1rd2w=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 731}, "end": {"line": 7, "column": 40, "index": 771}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPictureRecorder = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkCanvas = require(_dependencyMap[1], \"./JsiSkCanvas\");\n  var _JsiSkPicture = require(_dependencyMap[2], \"./JsiSkPicture\");\n  var _JsiSkRect = require(_dependencyMap[3], \"./JsiSkRect\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkPictureRecorder extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"PictureRecorder\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    beginRecording(bounds) {\n      return new _JsiSkCanvas.JsiSkCanvas(this.CanvasKit, this.ref.beginRecording(bounds ? _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, bounds) : Float32Array.of(0, 0, 2_000_000, 2_000_000)));\n    }\n    finishRecordingAsPicture() {\n      return new _JsiSkPicture.JsiSkPicture(this.CanvasKit, this.ref.finishRecordingAsPicture());\n    }\n  }\n  exports.JsiSkPictureRecorder = JsiSkPictureRecorder;\n});", "lineCount": 47, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkCanvas"], [7, 18, 5, 0], [7, 21, 5, 0, "require"], [7, 28, 5, 0], [7, 29, 5, 0, "_dependencyMap"], [7, 43, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkPicture"], [8, 19, 6, 0], [8, 22, 6, 0, "require"], [8, 29, 6, 0], [8, 30, 6, 0, "_dependencyMap"], [8, 44, 6, 0], [9, 2, 7, 0], [9, 6, 7, 0, "_JsiSkRect"], [9, 16, 7, 0], [9, 19, 7, 0, "require"], [9, 26, 7, 0], [9, 27, 7, 0, "_dependencyMap"], [9, 41, 7, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "e"], [10, 28, 1, 26], [10, 30, 1, 28, "r"], [10, 31, 1, 29], [10, 33, 1, 31, "t"], [10, 34, 1, 32], [10, 36, 1, 34], [11, 4, 1, 36], [11, 11, 1, 43], [11, 12, 1, 44, "r"], [11, 13, 1, 45], [11, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 30, 1, 62], [11, 31, 1, 63, "r"], [11, 32, 1, 64], [11, 33, 1, 65], [11, 38, 1, 70, "e"], [11, 39, 1, 71], [11, 42, 1, 74, "Object"], [11, 48, 1, 80], [11, 49, 1, 81, "defineProperty"], [11, 63, 1, 95], [11, 64, 1, 96, "e"], [11, 65, 1, 97], [11, 67, 1, 99, "r"], [11, 68, 1, 100], [11, 70, 1, 102], [12, 6, 1, 104, "value"], [12, 11, 1, 109], [12, 13, 1, 111, "t"], [12, 14, 1, 112], [13, 6, 1, 114, "enumerable"], [13, 16, 1, 124], [13, 18, 1, 126], [13, 19, 1, 127], [13, 20, 1, 128], [14, 6, 1, 130, "configurable"], [14, 18, 1, 142], [14, 20, 1, 144], [14, 21, 1, 145], [14, 22, 1, 146], [15, 6, 1, 148, "writable"], [15, 14, 1, 156], [15, 16, 1, 158], [15, 17, 1, 159], [16, 4, 1, 161], [16, 5, 1, 162], [16, 6, 1, 163], [16, 9, 1, 166, "e"], [16, 10, 1, 167], [16, 11, 1, 168, "r"], [16, 12, 1, 169], [16, 13, 1, 170], [16, 16, 1, 173, "t"], [16, 17, 1, 174], [16, 19, 1, 176, "e"], [16, 20, 1, 177], [17, 2, 1, 179], [18, 2, 2, 0], [18, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 26, 2, 24, "t"], [18, 27, 2, 25], [18, 29, 2, 27], [19, 4, 2, 29], [19, 8, 2, 33, "i"], [19, 9, 2, 34], [19, 12, 2, 37, "_toPrimitive"], [19, 24, 2, 49], [19, 25, 2, 50, "t"], [19, 26, 2, 51], [19, 28, 2, 53], [19, 36, 2, 61], [19, 37, 2, 62], [20, 4, 2, 64], [20, 11, 2, 71], [20, 19, 2, 79], [20, 23, 2, 83], [20, 30, 2, 90, "i"], [20, 31, 2, 91], [20, 34, 2, 94, "i"], [20, 35, 2, 95], [20, 38, 2, 98, "i"], [20, 39, 2, 99], [20, 42, 2, 102], [20, 44, 2, 104], [21, 2, 2, 106], [22, 2, 3, 0], [22, 11, 3, 9, "_toPrimitive"], [22, 23, 3, 21, "_toPrimitive"], [22, 24, 3, 22, "t"], [22, 25, 3, 23], [22, 27, 3, 25, "r"], [22, 28, 3, 26], [22, 30, 3, 28], [23, 4, 3, 30], [23, 8, 3, 34], [23, 16, 3, 42], [23, 20, 3, 46], [23, 27, 3, 53, "t"], [23, 28, 3, 54], [23, 32, 3, 58], [23, 33, 3, 59, "t"], [23, 34, 3, 60], [23, 36, 3, 62], [23, 43, 3, 69, "t"], [23, 44, 3, 70], [24, 4, 3, 72], [24, 8, 3, 76, "e"], [24, 9, 3, 77], [24, 12, 3, 80, "t"], [24, 13, 3, 81], [24, 14, 3, 82, "Symbol"], [24, 20, 3, 88], [24, 21, 3, 89, "toPrimitive"], [24, 32, 3, 100], [24, 33, 3, 101], [25, 4, 3, 103], [25, 8, 3, 107], [25, 13, 3, 112], [25, 14, 3, 113], [25, 19, 3, 118, "e"], [25, 20, 3, 119], [25, 22, 3, 121], [26, 6, 3, 123], [26, 10, 3, 127, "i"], [26, 11, 3, 128], [26, 14, 3, 131, "e"], [26, 15, 3, 132], [26, 16, 3, 133, "call"], [26, 20, 3, 137], [26, 21, 3, 138, "t"], [26, 22, 3, 139], [26, 24, 3, 141, "r"], [26, 25, 3, 142], [26, 29, 3, 146], [26, 38, 3, 155], [26, 39, 3, 156], [27, 6, 3, 158], [27, 10, 3, 162], [27, 18, 3, 170], [27, 22, 3, 174], [27, 29, 3, 181, "i"], [27, 30, 3, 182], [27, 32, 3, 184], [27, 39, 3, 191, "i"], [27, 40, 3, 192], [28, 6, 3, 194], [28, 12, 3, 200], [28, 16, 3, 204, "TypeError"], [28, 25, 3, 213], [28, 26, 3, 214], [28, 72, 3, 260], [28, 73, 3, 261], [29, 4, 3, 263], [30, 4, 3, 265], [30, 11, 3, 272], [30, 12, 3, 273], [30, 20, 3, 281], [30, 25, 3, 286, "r"], [30, 26, 3, 287], [30, 29, 3, 290, "String"], [30, 35, 3, 296], [30, 38, 3, 299, "Number"], [30, 44, 3, 305], [30, 46, 3, 307, "t"], [30, 47, 3, 308], [30, 48, 3, 309], [31, 2, 3, 311], [32, 2, 8, 7], [32, 8, 8, 13, "JsiSkPictureRecorder"], [32, 28, 8, 33], [32, 37, 8, 42, "HostObject"], [32, 53, 8, 52], [32, 54, 8, 53], [33, 4, 9, 2, "constructor"], [33, 15, 9, 13, "constructor"], [33, 16, 9, 14, "CanvasKit"], [33, 25, 9, 23], [33, 27, 9, 25, "ref"], [33, 30, 9, 28], [33, 32, 9, 30], [34, 6, 10, 4], [34, 11, 10, 9], [34, 12, 10, 10, "CanvasKit"], [34, 21, 10, 19], [34, 23, 10, 21, "ref"], [34, 26, 10, 24], [34, 28, 10, 26], [34, 45, 10, 43], [34, 46, 10, 44], [35, 6, 11, 4, "_defineProperty"], [35, 21, 11, 19], [35, 22, 11, 20], [35, 26, 11, 24], [35, 28, 11, 26], [35, 37, 11, 35], [35, 39, 11, 37], [35, 45, 11, 43], [36, 8, 12, 6], [36, 12, 12, 10], [36, 13, 12, 11, "ref"], [36, 16, 12, 14], [36, 17, 12, 15, "delete"], [36, 23, 12, 21], [36, 24, 12, 22], [36, 25, 12, 23], [37, 6, 13, 4], [37, 7, 13, 5], [37, 8, 13, 6], [38, 4, 14, 2], [39, 4, 15, 2, "beginRecording"], [39, 18, 15, 16, "beginRecording"], [39, 19, 15, 17, "bounds"], [39, 25, 15, 23], [39, 27, 15, 25], [40, 6, 16, 4], [40, 13, 16, 11], [40, 17, 16, 15, "JsiSkCanvas"], [40, 41, 16, 26], [40, 42, 16, 27], [40, 46, 16, 31], [40, 47, 16, 32, "CanvasKit"], [40, 56, 16, 41], [40, 58, 16, 43], [40, 62, 16, 47], [40, 63, 16, 48, "ref"], [40, 66, 16, 51], [40, 67, 16, 52, "beginRecording"], [40, 81, 16, 66], [40, 82, 16, 67, "bounds"], [40, 88, 16, 73], [40, 91, 16, 76, "JsiSkRect"], [40, 111, 16, 85], [40, 112, 16, 86, "fromValue"], [40, 121, 16, 95], [40, 122, 16, 96], [40, 126, 16, 100], [40, 127, 16, 101, "CanvasKit"], [40, 136, 16, 110], [40, 138, 16, 112, "bounds"], [40, 144, 16, 118], [40, 145, 16, 119], [40, 148, 16, 122, "Float32Array"], [40, 160, 16, 134], [40, 161, 16, 135, "of"], [40, 163, 16, 137], [40, 164, 16, 138], [40, 165, 16, 139], [40, 167, 16, 141], [40, 168, 16, 142], [40, 170, 16, 144], [40, 179, 16, 153], [40, 181, 16, 155], [40, 190, 16, 164], [40, 191, 16, 165], [40, 192, 16, 166], [40, 193, 16, 167], [41, 4, 17, 2], [42, 4, 18, 2, "finishRecordingAsPicture"], [42, 28, 18, 26, "finishRecordingAsPicture"], [42, 29, 18, 26], [42, 31, 18, 29], [43, 6, 19, 4], [43, 13, 19, 11], [43, 17, 19, 15, "JsiSkPicture"], [43, 43, 19, 27], [43, 44, 19, 28], [43, 48, 19, 32], [43, 49, 19, 33, "CanvasKit"], [43, 58, 19, 42], [43, 60, 19, 44], [43, 64, 19, 48], [43, 65, 19, 49, "ref"], [43, 68, 19, 52], [43, 69, 19, 53, "finishRecordingAsPicture"], [43, 93, 19, 77], [43, 94, 19, 78], [43, 95, 19, 79], [43, 96, 19, 80], [44, 4, 20, 2], [45, 2, 21, 0], [46, 2, 21, 1, "exports"], [46, 9, 21, 1], [46, 10, 21, 1, "JsiSkPictureRecorder"], [46, 30, 21, 1], [46, 33, 21, 1, "JsiSkPictureRecorder"], [46, 53, 21, 1], [47, 0, 21, 1], [47, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkPictureRecorder", "constructor", "_defineProperty$argument_2", "beginRecording", "finishRecordingAsPicture"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGK;ECC;qCCE;KDE;GDC;EGC;GHE;EIC;GJE;CHC"}}, "type": "js/module"}]}