{"dependencies": [{"name": "./commands/Drawing", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 264, "index": 264}}], "key": "An53xFVGeI9Q9Fm3B+OKdVWvZkk=", "exportNames": ["*"]}}, {"name": "./commands/Box", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 265}, "end": {"line": 2, "column": 55, "index": 320}}], "key": "kIeDzx1bIoywcxUx0A8GnKHRg1U=", "exportNames": ["*"]}}, {"name": "./commands/ColorFilters", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 321}, "end": {"line": 3, "column": 98, "index": 419}}], "key": "nZckydFCDLbK1DqGedDivGgMlN8=", "exportNames": ["*"]}}, {"name": "./commands/CTM", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 420}, "end": {"line": 4, "column": 41, "index": 461}}], "key": "eTzL/L9R9yfMDH707YvUrsGP17w=", "exportNames": ["*"]}}, {"name": "./commands/ImageFilters", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 462}, "end": {"line": 5, "column": 117, "index": 579}}], "key": "e9ITr6rZ5qZFQ3n4GebIFw8HFbs=", "exportNames": ["*"]}}, {"name": "./commands/Paint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 580}, "end": {"line": 6, "column": 54, "index": 634}}], "key": "tXEUJqBirJwqqW17EZUuejrynKY=", "exportNames": ["*"]}}, {"name": "./commands/PathEffects", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 635}, "end": {"line": 7, "column": 94, "index": 729}}], "key": "dJZjBYLA/hq/nsy93J4mSl0CtRQ=", "exportNames": ["*"]}}, {"name": "./commands/Shaders", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 730}, "end": {"line": 8, "column": 62, "index": 792}}], "key": "LWa0D4Uo0eVTV+fFKdTCpFdCTng=", "exportNames": ["*"]}}, {"name": "./Core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 793}, "end": {"line": 9, "column": 92, "index": 885}}], "key": "dny1ljGekIf+viLKNae+kH0CgzE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.replay = void 0;\n  var _Drawing = require(_dependencyMap[0], \"./commands/Drawing\");\n  var _Box = require(_dependencyMap[1], \"./commands/Box\");\n  var _ColorFilters = require(_dependencyMap[2], \"./commands/ColorFilters\");\n  var _CTM = require(_dependencyMap[3], \"./commands/CTM\");\n  var _ImageFilters = require(_dependencyMap[4], \"./commands/ImageFilters\");\n  var _Paint = require(_dependencyMap[5], \"./commands/Paint\");\n  var _PathEffects = require(_dependencyMap[6], \"./commands/PathEffects\");\n  var _Shaders = require(_dependencyMap[7], \"./commands/Shaders\");\n  var _Core = require(_dependencyMap[8], \"./Core\");\n  const _worklet_6951022178641_init_data = {\n    code: \"function play_PlayerJs1(ctx,_command){const play_PlayerJs1=this._recur;const{isGroup,materializeCommand,isCommand,CommandType,isDrawCommand,setPaintProperties,composeColorFilters,isPushColorFilter,pushColorFilter,isPushShader,pushShader,isPushImageFilter,pushImageFilter,isPushPathEffect,pushPathEffect,composePathEffects,composeImageFilters,setBlurMaskFilter,saveCTM,isBoxCommand,drawBox,drawImage,drawCircle,drawPoints,drawPath,drawRect,drawRRect,drawOval,drawLine,drawPatch,drawVertices,drawDiffRect,drawText,drawTextPath,drawTextBlob,drawGlyphs,drawPicture,drawImageSVG,drawParagraph,drawAtlas}=this.__closure;if(isGroup(_command)){_command.children.forEach(function(child){return play_PlayerJs1(ctx,child);});return;}const command=materializeCommand(_command);if(isCommand(command,CommandType.SaveBackdropFilter)){ctx.saveBackdropFilter();}else if(isCommand(command,CommandType.SaveLayer)){ctx.materializePaint();const paint=ctx.paintDeclarations.pop();ctx.canvas.saveLayer(paint);}else if(isDrawCommand(command,CommandType.SavePaint)){if(command.props.paint){ctx.paints.push(command.props.paint);}else{ctx.savePaint();setPaintProperties(ctx.Skia,ctx.paint,command.props);}}else if(isCommand(command,CommandType.RestorePaint)){ctx.restorePaint();}else if(isCommand(command,CommandType.ComposeColorFilter)){composeColorFilters(ctx);}else if(isCommand(command,CommandType.RestorePaintDeclaration)){ctx.materializePaint();const paint=ctx.restorePaint();if(!paint){throw new Error(\\\"No paint declaration to push\\\");}ctx.paintDeclarations.push(paint);}else if(isCommand(command,CommandType.MaterializePaint)){ctx.materializePaint();}else if(isPushColorFilter(command)){pushColorFilter(ctx,command);}else if(isPushShader(command)){pushShader(ctx,command);}else if(isPushImageFilter(command)){pushImageFilter(ctx,command);}else if(isPushPathEffect(command)){pushPathEffect(ctx,command);}else if(isCommand(command,CommandType.ComposePathEffect)){composePathEffects(ctx);}else if(isCommand(command,CommandType.ComposeImageFilter)){composeImageFilters(ctx);}else if(isDrawCommand(command,CommandType.PushBlurMaskFilter)){setBlurMaskFilter(ctx,command.props);}else if(isDrawCommand(command,CommandType.SaveCTM)){saveCTM(ctx,command.props);}else if(isCommand(command,CommandType.RestoreCTM)){ctx.canvas.restore();}else{const paints=[ctx.paint,...ctx.paintDeclarations];ctx.paintDeclarations=[];paints.forEach(function(p){ctx.paints.push(p);if(isBoxCommand(command)){drawBox(ctx,command);}else if(isCommand(command,CommandType.DrawPaint)){ctx.canvas.drawPaint(ctx.paint);}else if(isDrawCommand(command,CommandType.DrawImage)){drawImage(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawCircle)){drawCircle(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawPoints)){drawPoints(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawPath)){drawPath(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawRect)){drawRect(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawRRect)){drawRRect(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawOval)){drawOval(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawLine)){drawLine(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawPatch)){drawPatch(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawVertices)){drawVertices(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawDiffRect)){drawDiffRect(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawText)){drawText(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawTextPath)){drawTextPath(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawTextBlob)){drawTextBlob(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawGlyphs)){drawGlyphs(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawPicture)){drawPicture(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawImageSVG)){drawImageSVG(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawParagraph)){drawParagraph(ctx,command.props);}else if(isDrawCommand(command,CommandType.DrawAtlas)){drawAtlas(ctx,command.props);}else{console.warn(\\\"Unknown command: \\\"+command.type);}ctx.paints.pop();});}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Recorder\\\\Player.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"play_PlayerJs1\\\",\\\"ctx\\\",\\\"_command\\\",\\\"_recur\\\",\\\"isGroup\\\",\\\"materializeCommand\\\",\\\"isCommand\\\",\\\"CommandType\\\",\\\"isDrawCommand\\\",\\\"setPaintProperties\\\",\\\"composeColorFilters\\\",\\\"isPushColorFilter\\\",\\\"pushColorFilter\\\",\\\"isPushShader\\\",\\\"pushShader\\\",\\\"isPushImageFilter\\\",\\\"pushImageFilter\\\",\\\"isPushPathEffect\\\",\\\"pushPathEffect\\\",\\\"composePathEffects\\\",\\\"composeImageFilters\\\",\\\"setBlurMaskFilter\\\",\\\"saveCTM\\\",\\\"isBoxCommand\\\",\\\"drawBox\\\",\\\"drawImage\\\",\\\"drawCircle\\\",\\\"drawPoints\\\",\\\"drawPath\\\",\\\"drawRect\\\",\\\"drawRRect\\\",\\\"drawOval\\\",\\\"drawLine\\\",\\\"drawPatch\\\",\\\"drawVertices\\\",\\\"drawDiffRect\\\",\\\"drawText\\\",\\\"drawTextPath\\\",\\\"drawTextBlob\\\",\\\"drawGlyphs\\\",\\\"drawPicture\\\",\\\"drawImageSVG\\\",\\\"drawParagraph\\\",\\\"drawAtlas\\\",\\\"__closure\\\",\\\"children\\\",\\\"forEach\\\",\\\"child\\\",\\\"command\\\",\\\"SaveBackdropFilter\\\",\\\"saveBackdropFilter\\\",\\\"SaveLayer\\\",\\\"materializePaint\\\",\\\"paint\\\",\\\"paintDeclarations\\\",\\\"pop\\\",\\\"canvas\\\",\\\"saveLayer\\\",\\\"SavePaint\\\",\\\"props\\\",\\\"paints\\\",\\\"push\\\",\\\"savePaint\\\",\\\"Skia\\\",\\\"RestorePaint\\\",\\\"restorePaint\\\",\\\"ComposeColorFilter\\\",\\\"RestorePaintDeclaration\\\",\\\"Error\\\",\\\"MaterializePaint\\\",\\\"ComposePathEffect\\\",\\\"ComposeImageFilter\\\",\\\"PushBlurMaskFilter\\\",\\\"SaveCTM\\\",\\\"RestoreCTM\\\",\\\"restore\\\",\\\"p\\\",\\\"DrawPaint\\\",\\\"drawPaint\\\",\\\"DrawImage\\\",\\\"DrawCircle\\\",\\\"DrawPoints\\\",\\\"DrawPath\\\",\\\"DrawRect\\\",\\\"DrawRRect\\\",\\\"DrawOval\\\",\\\"DrawLine\\\",\\\"DrawPatch\\\",\\\"DrawVertices\\\",\\\"DrawDiffRect\\\",\\\"DrawText\\\",\\\"DrawTextPath\\\",\\\"DrawTextBlob\\\",\\\"DrawGlyphs\\\",\\\"DrawPicture\\\",\\\"DrawImageSVG\\\",\\\"DrawParagraph\\\",\\\"DrawAtlas\\\",\\\"console\\\",\\\"warn\\\",\\\"type\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Recorder/Player.js\\\"],\\\"mappings\\\":\\\"AASA,SAAAA,cAAmBA,CAAAC,GAAA,CAAQC,QAAE,QAAAF,cAAA,MAAAG,MAAA,OAAAC,OAAA,CAAAC,kBAAA,CAAAC,SAAA,CAAAC,WAAA,CAAAC,aAAA,CAAAC,kBAAA,CAAAC,mBAAA,CAAAC,iBAAA,CAAAC,eAAA,CAAAC,YAAA,CAAAC,UAAA,CAAAC,iBAAA,CAAAC,eAAA,CAAAC,gBAAA,CAAAC,cAAA,CAAAC,kBAAA,CAAAC,mBAAA,CAAAC,iBAAA,CAAAC,OAAA,CAAAC,YAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAC,UAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,SAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,QAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,UAAA,CAAAC,WAAA,CAAAC,YAAA,CAAAC,aAAA,CAAAC,SAAA,OAAAC,SAAA,CAG3B,GAAIxC,OAAO,CAACF,QAAQ,CAAC,CAAE,CACrBA,QAAQ,CAAC2C,QAAQ,CAACC,OAAO,CAAC,SAAAC,KAAK,QAAI,CAAA/C,cAAU,CAAKC,GAAC,CAAA8C,KAAA,IAAC,CACpD,OACF,CACA,KAAM,CAAAC,OAAO,CAAG3C,kBAAkB,CAACH,QAAQ,CAAC,CAC5C,GAAII,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAAC0C,kBAAkB,CAAC,CAAE,CACtDhD,GAAG,CAACiD,kBAAkB,CAAC,CAAC,CAC1B,CAAC,IAAM,IAAI5C,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAAC4C,SAAS,CAAC,CAAE,CACpDlD,GAAG,CAACmD,gBAAgB,CAAC,CAAC,CACtB,KAAM,CAAAC,KAAK,CAAGpD,GAAG,CAACqD,iBAAiB,CAACC,GAAG,CAAC,CAAC,CACzCtD,GAAG,CAACuD,MAAM,CAACC,SAAS,CAACJ,KAAK,CAAC,CAC7B,CAAC,IAAM,IAAI7C,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACmD,SAAS,CAAC,CAAE,CACxD,GAAIV,OAAO,CAACW,KAAK,CAACN,KAAK,CAAE,CACvBpD,GAAG,CAAC2D,MAAM,CAACC,IAAI,CAACb,OAAO,CAACW,KAAK,CAACN,KAAK,CAAC,CACtC,CAAC,IAAM,CACLpD,GAAG,CAAC6D,SAAS,CAAC,CAAC,CACfrD,kBAAkB,CAACR,GAAG,CAAC8D,IAAI,CAAE9D,GAAG,CAACoD,KAAK,CAAEL,OAAO,CAACW,KAAK,CAAC,CACxD,CACF,CAAC,IAAM,IAAIrD,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAACyD,YAAY,CAAC,CAAE,CACvD/D,GAAG,CAACgE,YAAY,CAAC,CAAC,CACpB,CAAC,IAAM,IAAI3D,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAAC2D,kBAAkB,CAAC,CAAE,CAC7DxD,mBAAmB,CAACT,GAAG,CAAC,CAC1B,CAAC,IAAM,IAAIK,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAAC4D,uBAAuB,CAAC,CAAE,CAClElE,GAAG,CAACmD,gBAAgB,CAAC,CAAC,CACtB,KAAM,CAAAC,KAAK,CAAGpD,GAAG,CAACgE,YAAY,CAAC,CAAC,CAChC,GAAI,CAACZ,KAAK,CAAE,CACV,KAAM,IAAI,CAAAe,KAAK,CAAC,8BAA8B,CAAC,CACjD,CACAnE,GAAG,CAACqD,iBAAiB,CAACO,IAAI,CAACR,KAAK,CAAC,CACnC,CAAC,IAAM,IAAI/C,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAAC8D,gBAAgB,CAAC,CAAE,CAC3DpE,GAAG,CAACmD,gBAAgB,CAAC,CAAC,CACxB,CAAC,IAAM,IAAIzC,iBAAiB,CAACqC,OAAO,CAAC,CAAE,CACrCpC,eAAe,CAACX,GAAG,CAAE+C,OAAO,CAAC,CAC/B,CAAC,IAAM,IAAInC,YAAY,CAACmC,OAAO,CAAC,CAAE,CAChClC,UAAU,CAACb,GAAG,CAAE+C,OAAO,CAAC,CAC1B,CAAC,IAAM,IAAIjC,iBAAiB,CAACiC,OAAO,CAAC,CAAE,CACrChC,eAAe,CAACf,GAAG,CAAE+C,OAAO,CAAC,CAC/B,CAAC,IAAM,IAAI/B,gBAAgB,CAAC+B,OAAO,CAAC,CAAE,CACpC9B,cAAc,CAACjB,GAAG,CAAE+C,OAAO,CAAC,CAC9B,CAAC,IAAM,IAAI1C,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAAC+D,iBAAiB,CAAC,CAAE,CAC5DnD,kBAAkB,CAAClB,GAAG,CAAC,CACzB,CAAC,IAAM,IAAIK,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAACgE,kBAAkB,CAAC,CAAE,CAC7DnD,mBAAmB,CAACnB,GAAG,CAAC,CAC1B,CAAC,IAAM,IAAIO,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACiE,kBAAkB,CAAC,CAAE,CACjEnD,iBAAiB,CAACpB,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CACvC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACkE,OAAO,CAAC,CAAE,CACtDnD,OAAO,CAACrB,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC7B,CAAC,IAAM,IAAIrD,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAACmE,UAAU,CAAC,CAAE,CACrDzE,GAAG,CAACuD,MAAM,CAACmB,OAAO,CAAC,CAAC,CACtB,CAAC,IAAM,CACL,KAAM,CAAAf,MAAM,CAAG,CAAC3D,GAAG,CAACoD,KAAK,CAAE,GAAGpD,GAAG,CAACqD,iBAAiB,CAAC,CACpDrD,GAAG,CAACqD,iBAAiB,CAAG,EAAE,CAC1BM,MAAM,CAACd,OAAO,CAAC,SAAA8B,CAAC,CAAI,CAClB3E,GAAG,CAAC2D,MAAM,CAACC,IAAI,CAACe,CAAC,CAAC,CAClB,GAAIrD,YAAY,CAACyB,OAAO,CAAC,CAAE,CACzBxB,OAAO,CAACvB,GAAG,CAAE+C,OAAO,CAAC,CACvB,CAAC,IAAM,IAAI1C,SAAS,CAAC0C,OAAO,CAAEzC,WAAW,CAACsE,SAAS,CAAC,CAAE,CACpD5E,GAAG,CAACuD,MAAM,CAACsB,SAAS,CAAC7E,GAAG,CAACoD,KAAK,CAAC,CACjC,CAAC,IAAM,IAAI7C,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACwE,SAAS,CAAC,CAAE,CACxDtD,SAAS,CAACxB,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC/B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACyE,UAAU,CAAC,CAAE,CACzDtD,UAAU,CAACzB,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAChC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAAC0E,UAAU,CAAC,CAAE,CACzDtD,UAAU,CAAC1B,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAChC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAAC2E,QAAQ,CAAC,CAAE,CACvDtD,QAAQ,CAAC3B,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC9B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAAC4E,QAAQ,CAAC,CAAE,CACvDtD,QAAQ,CAAC5B,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC9B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAAC6E,SAAS,CAAC,CAAE,CACxDtD,SAAS,CAAC7B,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC/B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAAC8E,QAAQ,CAAC,CAAE,CACvDtD,QAAQ,CAAC9B,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC9B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAAC+E,QAAQ,CAAC,CAAE,CACvDtD,QAAQ,CAAC/B,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC9B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACgF,SAAS,CAAC,CAAE,CACxDtD,SAAS,CAAChC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC/B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACiF,YAAY,CAAC,CAAE,CAC3DtD,YAAY,CAACjC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAClC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACkF,YAAY,CAAC,CAAE,CAC3DtD,YAAY,CAAClC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAClC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACmF,QAAQ,CAAC,CAAE,CACvDtD,QAAQ,CAACnC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC9B,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACoF,YAAY,CAAC,CAAE,CAC3DtD,YAAY,CAACpC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAClC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACqF,YAAY,CAAC,CAAE,CAC3DtD,YAAY,CAACrC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAClC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACsF,UAAU,CAAC,CAAE,CACzDtD,UAAU,CAACtC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAChC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACuF,WAAW,CAAC,CAAE,CAC1DtD,WAAW,CAACvC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CACjC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACwF,YAAY,CAAC,CAAE,CAC3DtD,YAAY,CAACxC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAClC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAACyF,aAAa,CAAC,CAAE,CAC5DtD,aAAa,CAACzC,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CACnC,CAAC,IAAM,IAAInD,aAAa,CAACwC,OAAO,CAAEzC,WAAW,CAAC0F,SAAS,CAAC,CAAE,CACxDtD,SAAS,CAAC1C,GAAG,CAAE+C,OAAO,CAACW,KAAK,CAAC,CAC/B,CAAC,IAAM,CACLuC,OAAO,CAACC,IAAI,qBAAqBnD,OAAO,CAACoD,IAAM,CAAC,CAClD,CACAnG,GAAG,CAAC2D,MAAM,CAACL,GAAG,CAAC,CAAC,CAClB,CAAC,CAAC,CACJ,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const play = function () {\n    const _e = [new global.Error(), -41, -27];\n    const play = function (ctx, _command) {\n      if ((0, _Core.isGroup)(_command)) {\n        _command.children.forEach(child => play(ctx, child));\n        return;\n      }\n      const command = (0, _Core.materializeCommand)(_command);\n      if ((0, _Core.isCommand)(command, _Core.CommandType.SaveBackdropFilter)) {\n        ctx.saveBackdropFilter();\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.SaveLayer)) {\n        ctx.materializePaint();\n        const paint = ctx.paintDeclarations.pop();\n        ctx.canvas.saveLayer(paint);\n      } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.SavePaint)) {\n        if (command.props.paint) {\n          ctx.paints.push(command.props.paint);\n        } else {\n          ctx.savePaint();\n          (0, _Paint.setPaintProperties)(ctx.Skia, ctx.paint, command.props);\n        }\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.RestorePaint)) {\n        ctx.restorePaint();\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.ComposeColorFilter)) {\n        (0, _ColorFilters.composeColorFilters)(ctx);\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.RestorePaintDeclaration)) {\n        ctx.materializePaint();\n        const paint = ctx.restorePaint();\n        if (!paint) {\n          throw new Error(\"No paint declaration to push\");\n        }\n        ctx.paintDeclarations.push(paint);\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.MaterializePaint)) {\n        ctx.materializePaint();\n      } else if ((0, _ColorFilters.isPushColorFilter)(command)) {\n        (0, _ColorFilters.pushColorFilter)(ctx, command);\n      } else if ((0, _Shaders.isPushShader)(command)) {\n        (0, _Shaders.pushShader)(ctx, command);\n      } else if ((0, _ImageFilters.isPushImageFilter)(command)) {\n        (0, _ImageFilters.pushImageFilter)(ctx, command);\n      } else if ((0, _PathEffects.isPushPathEffect)(command)) {\n        (0, _PathEffects.pushPathEffect)(ctx, command);\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.ComposePathEffect)) {\n        (0, _PathEffects.composePathEffects)(ctx);\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.ComposeImageFilter)) {\n        (0, _ImageFilters.composeImageFilters)(ctx);\n      } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.PushBlurMaskFilter)) {\n        (0, _ImageFilters.setBlurMaskFilter)(ctx, command.props);\n      } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.SaveCTM)) {\n        (0, _CTM.saveCTM)(ctx, command.props);\n      } else if ((0, _Core.isCommand)(command, _Core.CommandType.RestoreCTM)) {\n        ctx.canvas.restore();\n      } else {\n        const paints = [ctx.paint, ...ctx.paintDeclarations];\n        ctx.paintDeclarations = [];\n        paints.forEach(p => {\n          ctx.paints.push(p);\n          if ((0, _Box.isBoxCommand)(command)) {\n            (0, _Box.drawBox)(ctx, command);\n          } else if ((0, _Core.isCommand)(command, _Core.CommandType.DrawPaint)) {\n            ctx.canvas.drawPaint(ctx.paint);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawImage)) {\n            (0, _Drawing.drawImage)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawCircle)) {\n            (0, _Drawing.drawCircle)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawPoints)) {\n            (0, _Drawing.drawPoints)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawPath)) {\n            (0, _Drawing.drawPath)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawRect)) {\n            (0, _Drawing.drawRect)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawRRect)) {\n            (0, _Drawing.drawRRect)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawOval)) {\n            (0, _Drawing.drawOval)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawLine)) {\n            (0, _Drawing.drawLine)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawPatch)) {\n            (0, _Drawing.drawPatch)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawVertices)) {\n            (0, _Drawing.drawVertices)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawDiffRect)) {\n            (0, _Drawing.drawDiffRect)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawText)) {\n            (0, _Drawing.drawText)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawTextPath)) {\n            (0, _Drawing.drawTextPath)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawTextBlob)) {\n            (0, _Drawing.drawTextBlob)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawGlyphs)) {\n            (0, _Drawing.drawGlyphs)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawPicture)) {\n            (0, _Drawing.drawPicture)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawImageSVG)) {\n            (0, _Drawing.drawImageSVG)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawParagraph)) {\n            (0, _Drawing.drawParagraph)(ctx, command.props);\n          } else if ((0, _Core.isDrawCommand)(command, _Core.CommandType.DrawAtlas)) {\n            (0, _Drawing.drawAtlas)(ctx, command.props);\n          } else {\n            console.warn(`Unknown command: ${command.type}`);\n          }\n          ctx.paints.pop();\n        });\n      }\n    };\n    play.__closure = {\n      isGroup: _Core.isGroup,\n      materializeCommand: _Core.materializeCommand,\n      isCommand: _Core.isCommand,\n      CommandType: _Core.CommandType,\n      isDrawCommand: _Core.isDrawCommand,\n      setPaintProperties: _Paint.setPaintProperties,\n      composeColorFilters: _ColorFilters.composeColorFilters,\n      isPushColorFilter: _ColorFilters.isPushColorFilter,\n      pushColorFilter: _ColorFilters.pushColorFilter,\n      isPushShader: _Shaders.isPushShader,\n      pushShader: _Shaders.pushShader,\n      isPushImageFilter: _ImageFilters.isPushImageFilter,\n      pushImageFilter: _ImageFilters.pushImageFilter,\n      isPushPathEffect: _PathEffects.isPushPathEffect,\n      pushPathEffect: _PathEffects.pushPathEffect,\n      composePathEffects: _PathEffects.composePathEffects,\n      composeImageFilters: _ImageFilters.composeImageFilters,\n      setBlurMaskFilter: _ImageFilters.setBlurMaskFilter,\n      saveCTM: _CTM.saveCTM,\n      isBoxCommand: _Box.isBoxCommand,\n      drawBox: _Box.drawBox,\n      drawImage: _Drawing.drawImage,\n      drawCircle: _Drawing.drawCircle,\n      drawPoints: _Drawing.drawPoints,\n      drawPath: _Drawing.drawPath,\n      drawRect: _Drawing.drawRect,\n      drawRRect: _Drawing.drawRRect,\n      drawOval: _Drawing.drawOval,\n      drawLine: _Drawing.drawLine,\n      drawPatch: _Drawing.drawPatch,\n      drawVertices: _Drawing.drawVertices,\n      drawDiffRect: _Drawing.drawDiffRect,\n      drawText: _Drawing.drawText,\n      drawTextPath: _Drawing.drawTextPath,\n      drawTextBlob: _Drawing.drawTextBlob,\n      drawGlyphs: _Drawing.drawGlyphs,\n      drawPicture: _Drawing.drawPicture,\n      drawImageSVG: _Drawing.drawImageSVG,\n      drawParagraph: _Drawing.drawParagraph,\n      drawAtlas: _Drawing.drawAtlas\n    };\n    play.__workletHash = 6951022178641;\n    play.__initData = _worklet_6951022178641_init_data;\n    play.__stackDetails = _e;\n    return play;\n  }();\n  const _worklet_9915340751545_init_data = {\n    code: \"function PlayerJs2(ctx,commands){const{play}=this.__closure;commands.forEach(function(command){play(ctx,command);});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Recorder\\\\Player.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PlayerJs2\\\",\\\"ctx\\\",\\\"commands\\\",\\\"play\\\",\\\"__closure\\\",\\\"forEach\\\",\\\"command\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Recorder/Player.js\\\"],\\\"mappings\\\":\\\"AAmHsB,QAAC,CAAAA,SAAKA,CAAAC,GAAA,CAAQC,QAAK,QAAAC,IAAA,OAAAC,SAAA,CAGvCF,QAAQ,CAACG,OAAO,CAAC,SAAAC,OAAO,CAAI,CAC1BH,IAAI,CAACF,GAAG,CAAEK,OAAO,CAAC,CACpB,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const replay = exports.replay = function () {\n    const _e = [new global.Error(), -2, -27];\n    const PlayerJs2 = function (ctx, commands) {\n      commands.forEach(command => {\n        play(ctx, command);\n      });\n    };\n    PlayerJs2.__closure = {\n      play\n    };\n    PlayerJs2.__workletHash = 9915340751545;\n    PlayerJs2.__initData = _worklet_9915340751545_init_data;\n    PlayerJs2.__stackDetails = _e;\n    return PlayerJs2;\n  }();\n});", "lineCount": 195, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Drawing"], [6, 14, 1, 0], [6, 17, 1, 0, "require"], [6, 24, 1, 0], [6, 25, 1, 0, "_dependencyMap"], [6, 39, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Box"], [7, 10, 2, 0], [7, 13, 2, 0, "require"], [7, 20, 2, 0], [7, 21, 2, 0, "_dependencyMap"], [7, 35, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_ColorFilters"], [8, 19, 3, 0], [8, 22, 3, 0, "require"], [8, 29, 3, 0], [8, 30, 3, 0, "_dependencyMap"], [8, 44, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_CTM"], [9, 10, 4, 0], [9, 13, 4, 0, "require"], [9, 20, 4, 0], [9, 21, 4, 0, "_dependencyMap"], [9, 35, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_ImageFilters"], [10, 19, 5, 0], [10, 22, 5, 0, "require"], [10, 29, 5, 0], [10, 30, 5, 0, "_dependencyMap"], [10, 44, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_Paint"], [11, 12, 6, 0], [11, 15, 6, 0, "require"], [11, 22, 6, 0], [11, 23, 6, 0, "_dependencyMap"], [11, 37, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_PathEffects"], [12, 18, 7, 0], [12, 21, 7, 0, "require"], [12, 28, 7, 0], [12, 29, 7, 0, "_dependencyMap"], [12, 43, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_Shaders"], [13, 14, 8, 0], [13, 17, 8, 0, "require"], [13, 24, 8, 0], [13, 25, 8, 0, "_dependencyMap"], [13, 39, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_Core"], [14, 11, 9, 0], [14, 14, 9, 0, "require"], [14, 21, 9, 0], [14, 22, 9, 0, "_dependencyMap"], [14, 36, 9, 0], [15, 2, 9, 92], [15, 8, 9, 92, "_worklet_6951022178641_init_data"], [15, 40, 9, 92], [16, 4, 9, 92, "code"], [16, 8, 9, 92], [17, 4, 9, 92, "location"], [17, 12, 9, 92], [18, 4, 9, 92, "sourceMap"], [18, 13, 9, 92], [19, 4, 9, 92, "version"], [19, 11, 9, 92], [20, 2, 9, 92], [21, 2, 9, 92], [21, 8, 9, 92, "play"], [21, 12, 9, 92], [21, 15, 10, 0], [22, 4, 10, 0], [22, 10, 10, 0, "_e"], [22, 12, 10, 0], [22, 20, 10, 0, "global"], [22, 26, 10, 0], [22, 27, 10, 0, "Error"], [22, 32, 10, 0], [23, 4, 10, 0], [23, 10, 10, 0, "play"], [23, 14, 10, 0], [23, 26, 10, 0, "play"], [23, 27, 10, 14, "ctx"], [23, 30, 10, 17], [23, 32, 10, 19, "_command"], [23, 40, 10, 27], [23, 42, 10, 29], [24, 6, 13, 2], [24, 10, 13, 6], [24, 14, 13, 6, "isGroup"], [24, 27, 13, 13], [24, 29, 13, 14, "_command"], [24, 37, 13, 22], [24, 38, 13, 23], [24, 40, 13, 25], [25, 8, 14, 4, "_command"], [25, 16, 14, 12], [25, 17, 14, 13, "children"], [25, 25, 14, 21], [25, 26, 14, 22, "for<PERSON>ach"], [25, 33, 14, 29], [25, 34, 14, 30, "child"], [25, 39, 14, 35], [25, 43, 14, 39, "play"], [25, 47, 14, 43], [25, 48, 14, 44, "ctx"], [25, 51, 14, 47], [25, 53, 14, 49, "child"], [25, 58, 14, 54], [25, 59, 14, 55], [25, 60, 14, 56], [26, 8, 15, 4], [27, 6, 16, 2], [28, 6, 17, 2], [28, 12, 17, 8, "command"], [28, 19, 17, 15], [28, 22, 17, 18], [28, 26, 17, 18, "materializeCommand"], [28, 50, 17, 36], [28, 52, 17, 37, "_command"], [28, 60, 17, 45], [28, 61, 17, 46], [29, 6, 18, 2], [29, 10, 18, 6], [29, 14, 18, 6, "isCommand"], [29, 29, 18, 15], [29, 31, 18, 16, "command"], [29, 38, 18, 23], [29, 40, 18, 25, "CommandType"], [29, 57, 18, 36], [29, 58, 18, 37, "SaveBackdropFilter"], [29, 76, 18, 55], [29, 77, 18, 56], [29, 79, 18, 58], [30, 8, 19, 4, "ctx"], [30, 11, 19, 7], [30, 12, 19, 8, "saveBackdropFilter"], [30, 30, 19, 26], [30, 31, 19, 27], [30, 32, 19, 28], [31, 6, 20, 2], [31, 7, 20, 3], [31, 13, 20, 9], [31, 17, 20, 13], [31, 21, 20, 13, "isCommand"], [31, 36, 20, 22], [31, 38, 20, 23, "command"], [31, 45, 20, 30], [31, 47, 20, 32, "CommandType"], [31, 64, 20, 43], [31, 65, 20, 44, "<PERSON><PERSON><PERSON><PERSON>"], [31, 74, 20, 53], [31, 75, 20, 54], [31, 77, 20, 56], [32, 8, 21, 4, "ctx"], [32, 11, 21, 7], [32, 12, 21, 8, "materialize<PERSON><PERSON><PERSON>"], [32, 28, 21, 24], [32, 29, 21, 25], [32, 30, 21, 26], [33, 8, 22, 4], [33, 14, 22, 10, "paint"], [33, 19, 22, 15], [33, 22, 22, 18, "ctx"], [33, 25, 22, 21], [33, 26, 22, 22, "paintDeclarations"], [33, 43, 22, 39], [33, 44, 22, 40, "pop"], [33, 47, 22, 43], [33, 48, 22, 44], [33, 49, 22, 45], [34, 8, 23, 4, "ctx"], [34, 11, 23, 7], [34, 12, 23, 8, "canvas"], [34, 18, 23, 14], [34, 19, 23, 15, "save<PERSON><PERSON><PERSON>"], [34, 28, 23, 24], [34, 29, 23, 25, "paint"], [34, 34, 23, 30], [34, 35, 23, 31], [35, 6, 24, 2], [35, 7, 24, 3], [35, 13, 24, 9], [35, 17, 24, 13], [35, 21, 24, 13, "isDrawCommand"], [35, 40, 24, 26], [35, 42, 24, 27, "command"], [35, 49, 24, 34], [35, 51, 24, 36, "CommandType"], [35, 68, 24, 47], [35, 69, 24, 48, "<PERSON><PERSON><PERSON><PERSON>"], [35, 78, 24, 57], [35, 79, 24, 58], [35, 81, 24, 60], [36, 8, 25, 4], [36, 12, 25, 8, "command"], [36, 19, 25, 15], [36, 20, 25, 16, "props"], [36, 25, 25, 21], [36, 26, 25, 22, "paint"], [36, 31, 25, 27], [36, 33, 25, 29], [37, 10, 26, 6, "ctx"], [37, 13, 26, 9], [37, 14, 26, 10, "paints"], [37, 20, 26, 16], [37, 21, 26, 17, "push"], [37, 25, 26, 21], [37, 26, 26, 22, "command"], [37, 33, 26, 29], [37, 34, 26, 30, "props"], [37, 39, 26, 35], [37, 40, 26, 36, "paint"], [37, 45, 26, 41], [37, 46, 26, 42], [38, 8, 27, 4], [38, 9, 27, 5], [38, 15, 27, 11], [39, 10, 28, 6, "ctx"], [39, 13, 28, 9], [39, 14, 28, 10, "save<PERSON><PERSON>t"], [39, 23, 28, 19], [39, 24, 28, 20], [39, 25, 28, 21], [40, 10, 29, 6], [40, 14, 29, 6, "setPaintProperties"], [40, 39, 29, 24], [40, 41, 29, 25, "ctx"], [40, 44, 29, 28], [40, 45, 29, 29, "Skia"], [40, 49, 29, 33], [40, 51, 29, 35, "ctx"], [40, 54, 29, 38], [40, 55, 29, 39, "paint"], [40, 60, 29, 44], [40, 62, 29, 46, "command"], [40, 69, 29, 53], [40, 70, 29, 54, "props"], [40, 75, 29, 59], [40, 76, 29, 60], [41, 8, 30, 4], [42, 6, 31, 2], [42, 7, 31, 3], [42, 13, 31, 9], [42, 17, 31, 13], [42, 21, 31, 13, "isCommand"], [42, 36, 31, 22], [42, 38, 31, 23, "command"], [42, 45, 31, 30], [42, 47, 31, 32, "CommandType"], [42, 64, 31, 43], [42, 65, 31, 44, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [42, 77, 31, 56], [42, 78, 31, 57], [42, 80, 31, 59], [43, 8, 32, 4, "ctx"], [43, 11, 32, 7], [43, 12, 32, 8, "<PERSON><PERSON><PERSON><PERSON>"], [43, 24, 32, 20], [43, 25, 32, 21], [43, 26, 32, 22], [44, 6, 33, 2], [44, 7, 33, 3], [44, 13, 33, 9], [44, 17, 33, 13], [44, 21, 33, 13, "isCommand"], [44, 36, 33, 22], [44, 38, 33, 23, "command"], [44, 45, 33, 30], [44, 47, 33, 32, "CommandType"], [44, 64, 33, 43], [44, 65, 33, 44, "ComposeColorFilter"], [44, 83, 33, 62], [44, 84, 33, 63], [44, 86, 33, 65], [45, 8, 34, 4], [45, 12, 34, 4, "composeColorFilters"], [45, 45, 34, 23], [45, 47, 34, 24, "ctx"], [45, 50, 34, 27], [45, 51, 34, 28], [46, 6, 35, 2], [46, 7, 35, 3], [46, 13, 35, 9], [46, 17, 35, 13], [46, 21, 35, 13, "isCommand"], [46, 36, 35, 22], [46, 38, 35, 23, "command"], [46, 45, 35, 30], [46, 47, 35, 32, "CommandType"], [46, 64, 35, 43], [46, 65, 35, 44, "RestorePaintDeclaration"], [46, 88, 35, 67], [46, 89, 35, 68], [46, 91, 35, 70], [47, 8, 36, 4, "ctx"], [47, 11, 36, 7], [47, 12, 36, 8, "materialize<PERSON><PERSON><PERSON>"], [47, 28, 36, 24], [47, 29, 36, 25], [47, 30, 36, 26], [48, 8, 37, 4], [48, 14, 37, 10, "paint"], [48, 19, 37, 15], [48, 22, 37, 18, "ctx"], [48, 25, 37, 21], [48, 26, 37, 22, "<PERSON><PERSON><PERSON><PERSON>"], [48, 38, 37, 34], [48, 39, 37, 35], [48, 40, 37, 36], [49, 8, 38, 4], [49, 12, 38, 8], [49, 13, 38, 9, "paint"], [49, 18, 38, 14], [49, 20, 38, 16], [50, 10, 39, 6], [50, 16, 39, 12], [50, 20, 39, 16, "Error"], [50, 25, 39, 21], [50, 26, 39, 22], [50, 56, 39, 52], [50, 57, 39, 53], [51, 8, 40, 4], [52, 8, 41, 4, "ctx"], [52, 11, 41, 7], [52, 12, 41, 8, "paintDeclarations"], [52, 29, 41, 25], [52, 30, 41, 26, "push"], [52, 34, 41, 30], [52, 35, 41, 31, "paint"], [52, 40, 41, 36], [52, 41, 41, 37], [53, 6, 42, 2], [53, 7, 42, 3], [53, 13, 42, 9], [53, 17, 42, 13], [53, 21, 42, 13, "isCommand"], [53, 36, 42, 22], [53, 38, 42, 23, "command"], [53, 45, 42, 30], [53, 47, 42, 32, "CommandType"], [53, 64, 42, 43], [53, 65, 42, 44, "MaterializePaint"], [53, 81, 42, 60], [53, 82, 42, 61], [53, 84, 42, 63], [54, 8, 43, 4, "ctx"], [54, 11, 43, 7], [54, 12, 43, 8, "materialize<PERSON><PERSON><PERSON>"], [54, 28, 43, 24], [54, 29, 43, 25], [54, 30, 43, 26], [55, 6, 44, 2], [55, 7, 44, 3], [55, 13, 44, 9], [55, 17, 44, 13], [55, 21, 44, 13, "isPushColorFilter"], [55, 52, 44, 30], [55, 54, 44, 31, "command"], [55, 61, 44, 38], [55, 62, 44, 39], [55, 64, 44, 41], [56, 8, 45, 4], [56, 12, 45, 4, "pushColorFilter"], [56, 41, 45, 19], [56, 43, 45, 20, "ctx"], [56, 46, 45, 23], [56, 48, 45, 25, "command"], [56, 55, 45, 32], [56, 56, 45, 33], [57, 6, 46, 2], [57, 7, 46, 3], [57, 13, 46, 9], [57, 17, 46, 13], [57, 21, 46, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [57, 42, 46, 25], [57, 44, 46, 26, "command"], [57, 51, 46, 33], [57, 52, 46, 34], [57, 54, 46, 36], [58, 8, 47, 4], [58, 12, 47, 4, "push<PERSON><PERSON>er"], [58, 31, 47, 14], [58, 33, 47, 15, "ctx"], [58, 36, 47, 18], [58, 38, 47, 20, "command"], [58, 45, 47, 27], [58, 46, 47, 28], [59, 6, 48, 2], [59, 7, 48, 3], [59, 13, 48, 9], [59, 17, 48, 13], [59, 21, 48, 13, "isPushImageFilter"], [59, 52, 48, 30], [59, 54, 48, 31, "command"], [59, 61, 48, 38], [59, 62, 48, 39], [59, 64, 48, 41], [60, 8, 49, 4], [60, 12, 49, 4, "pushImageFilter"], [60, 41, 49, 19], [60, 43, 49, 20, "ctx"], [60, 46, 49, 23], [60, 48, 49, 25, "command"], [60, 55, 49, 32], [60, 56, 49, 33], [61, 6, 50, 2], [61, 7, 50, 3], [61, 13, 50, 9], [61, 17, 50, 13], [61, 21, 50, 13, "isPushPathEffect"], [61, 50, 50, 29], [61, 52, 50, 30, "command"], [61, 59, 50, 37], [61, 60, 50, 38], [61, 62, 50, 40], [62, 8, 51, 4], [62, 12, 51, 4, "pushPathEffect"], [62, 39, 51, 18], [62, 41, 51, 19, "ctx"], [62, 44, 51, 22], [62, 46, 51, 24, "command"], [62, 53, 51, 31], [62, 54, 51, 32], [63, 6, 52, 2], [63, 7, 52, 3], [63, 13, 52, 9], [63, 17, 52, 13], [63, 21, 52, 13, "isCommand"], [63, 36, 52, 22], [63, 38, 52, 23, "command"], [63, 45, 52, 30], [63, 47, 52, 32, "CommandType"], [63, 64, 52, 43], [63, 65, 52, 44, "ComposePathEffect"], [63, 82, 52, 61], [63, 83, 52, 62], [63, 85, 52, 64], [64, 8, 53, 4], [64, 12, 53, 4, "composePathEffects"], [64, 43, 53, 22], [64, 45, 53, 23, "ctx"], [64, 48, 53, 26], [64, 49, 53, 27], [65, 6, 54, 2], [65, 7, 54, 3], [65, 13, 54, 9], [65, 17, 54, 13], [65, 21, 54, 13, "isCommand"], [65, 36, 54, 22], [65, 38, 54, 23, "command"], [65, 45, 54, 30], [65, 47, 54, 32, "CommandType"], [65, 64, 54, 43], [65, 65, 54, 44, "ComposeImageFilter"], [65, 83, 54, 62], [65, 84, 54, 63], [65, 86, 54, 65], [66, 8, 55, 4], [66, 12, 55, 4, "composeImageFilters"], [66, 45, 55, 23], [66, 47, 55, 24, "ctx"], [66, 50, 55, 27], [66, 51, 55, 28], [67, 6, 56, 2], [67, 7, 56, 3], [67, 13, 56, 9], [67, 17, 56, 13], [67, 21, 56, 13, "isDrawCommand"], [67, 40, 56, 26], [67, 42, 56, 27, "command"], [67, 49, 56, 34], [67, 51, 56, 36, "CommandType"], [67, 68, 56, 47], [67, 69, 56, 48, "<PERSON>ush<PERSON><PERSON>rMask<PERSON><PERSON><PERSON>"], [67, 87, 56, 66], [67, 88, 56, 67], [67, 90, 56, 69], [68, 8, 57, 4], [68, 12, 57, 4, "setBlurMaskFilter"], [68, 43, 57, 21], [68, 45, 57, 22, "ctx"], [68, 48, 57, 25], [68, 50, 57, 27, "command"], [68, 57, 57, 34], [68, 58, 57, 35, "props"], [68, 63, 57, 40], [68, 64, 57, 41], [69, 6, 58, 2], [69, 7, 58, 3], [69, 13, 58, 9], [69, 17, 58, 13], [69, 21, 58, 13, "isDrawCommand"], [69, 40, 58, 26], [69, 42, 58, 27, "command"], [69, 49, 58, 34], [69, 51, 58, 36, "CommandType"], [69, 68, 58, 47], [69, 69, 58, 48, "SaveCTM"], [69, 76, 58, 55], [69, 77, 58, 56], [69, 79, 58, 58], [70, 8, 59, 4], [70, 12, 59, 4, "saveCTM"], [70, 24, 59, 11], [70, 26, 59, 12, "ctx"], [70, 29, 59, 15], [70, 31, 59, 17, "command"], [70, 38, 59, 24], [70, 39, 59, 25, "props"], [70, 44, 59, 30], [70, 45, 59, 31], [71, 6, 60, 2], [71, 7, 60, 3], [71, 13, 60, 9], [71, 17, 60, 13], [71, 21, 60, 13, "isCommand"], [71, 36, 60, 22], [71, 38, 60, 23, "command"], [71, 45, 60, 30], [71, 47, 60, 32, "CommandType"], [71, 64, 60, 43], [71, 65, 60, 44, "RestoreCTM"], [71, 75, 60, 54], [71, 76, 60, 55], [71, 78, 60, 57], [72, 8, 61, 4, "ctx"], [72, 11, 61, 7], [72, 12, 61, 8, "canvas"], [72, 18, 61, 14], [72, 19, 61, 15, "restore"], [72, 26, 61, 22], [72, 27, 61, 23], [72, 28, 61, 24], [73, 6, 62, 2], [73, 7, 62, 3], [73, 13, 62, 9], [74, 8, 63, 4], [74, 14, 63, 10, "paints"], [74, 20, 63, 16], [74, 23, 63, 19], [74, 24, 63, 20, "ctx"], [74, 27, 63, 23], [74, 28, 63, 24, "paint"], [74, 33, 63, 29], [74, 35, 63, 31], [74, 38, 63, 34, "ctx"], [74, 41, 63, 37], [74, 42, 63, 38, "paintDeclarations"], [74, 59, 63, 55], [74, 60, 63, 56], [75, 8, 64, 4, "ctx"], [75, 11, 64, 7], [75, 12, 64, 8, "paintDeclarations"], [75, 29, 64, 25], [75, 32, 64, 28], [75, 34, 64, 30], [76, 8, 65, 4, "paints"], [76, 14, 65, 10], [76, 15, 65, 11, "for<PERSON>ach"], [76, 22, 65, 18], [76, 23, 65, 19, "p"], [76, 24, 65, 20], [76, 28, 65, 24], [77, 10, 66, 6, "ctx"], [77, 13, 66, 9], [77, 14, 66, 10, "paints"], [77, 20, 66, 16], [77, 21, 66, 17, "push"], [77, 25, 66, 21], [77, 26, 66, 22, "p"], [77, 27, 66, 23], [77, 28, 66, 24], [78, 10, 67, 6], [78, 14, 67, 10], [78, 18, 67, 10, "isBoxCommand"], [78, 35, 67, 22], [78, 37, 67, 23, "command"], [78, 44, 67, 30], [78, 45, 67, 31], [78, 47, 67, 33], [79, 12, 68, 8], [79, 16, 68, 8, "drawBox"], [79, 28, 68, 15], [79, 30, 68, 16, "ctx"], [79, 33, 68, 19], [79, 35, 68, 21, "command"], [79, 42, 68, 28], [79, 43, 68, 29], [80, 10, 69, 6], [80, 11, 69, 7], [80, 17, 69, 13], [80, 21, 69, 17], [80, 25, 69, 17, "isCommand"], [80, 40, 69, 26], [80, 42, 69, 27, "command"], [80, 49, 69, 34], [80, 51, 69, 36, "CommandType"], [80, 68, 69, 47], [80, 69, 69, 48, "DrawPaint"], [80, 78, 69, 57], [80, 79, 69, 58], [80, 81, 69, 60], [81, 12, 70, 8, "ctx"], [81, 15, 70, 11], [81, 16, 70, 12, "canvas"], [81, 22, 70, 18], [81, 23, 70, 19, "<PERSON><PERSON><PERSON><PERSON>"], [81, 32, 70, 28], [81, 33, 70, 29, "ctx"], [81, 36, 70, 32], [81, 37, 70, 33, "paint"], [81, 42, 70, 38], [81, 43, 70, 39], [82, 10, 71, 6], [82, 11, 71, 7], [82, 17, 71, 13], [82, 21, 71, 17], [82, 25, 71, 17, "isDrawCommand"], [82, 44, 71, 30], [82, 46, 71, 31, "command"], [82, 53, 71, 38], [82, 55, 71, 40, "CommandType"], [82, 72, 71, 51], [82, 73, 71, 52, "DrawImage"], [82, 82, 71, 61], [82, 83, 71, 62], [82, 85, 71, 64], [83, 12, 72, 8], [83, 16, 72, 8, "drawImage"], [83, 34, 72, 17], [83, 36, 72, 18, "ctx"], [83, 39, 72, 21], [83, 41, 72, 23, "command"], [83, 48, 72, 30], [83, 49, 72, 31, "props"], [83, 54, 72, 36], [83, 55, 72, 37], [84, 10, 73, 6], [84, 11, 73, 7], [84, 17, 73, 13], [84, 21, 73, 17], [84, 25, 73, 17, "isDrawCommand"], [84, 44, 73, 30], [84, 46, 73, 31, "command"], [84, 53, 73, 38], [84, 55, 73, 40, "CommandType"], [84, 72, 73, 51], [84, 73, 73, 52, "DrawCircle"], [84, 83, 73, 62], [84, 84, 73, 63], [84, 86, 73, 65], [85, 12, 74, 8], [85, 16, 74, 8, "drawCircle"], [85, 35, 74, 18], [85, 37, 74, 19, "ctx"], [85, 40, 74, 22], [85, 42, 74, 24, "command"], [85, 49, 74, 31], [85, 50, 74, 32, "props"], [85, 55, 74, 37], [85, 56, 74, 38], [86, 10, 75, 6], [86, 11, 75, 7], [86, 17, 75, 13], [86, 21, 75, 17], [86, 25, 75, 17, "isDrawCommand"], [86, 44, 75, 30], [86, 46, 75, 31, "command"], [86, 53, 75, 38], [86, 55, 75, 40, "CommandType"], [86, 72, 75, 51], [86, 73, 75, 52, "DrawPoints"], [86, 83, 75, 62], [86, 84, 75, 63], [86, 86, 75, 65], [87, 12, 76, 8], [87, 16, 76, 8, "drawPoints"], [87, 35, 76, 18], [87, 37, 76, 19, "ctx"], [87, 40, 76, 22], [87, 42, 76, 24, "command"], [87, 49, 76, 31], [87, 50, 76, 32, "props"], [87, 55, 76, 37], [87, 56, 76, 38], [88, 10, 77, 6], [88, 11, 77, 7], [88, 17, 77, 13], [88, 21, 77, 17], [88, 25, 77, 17, "isDrawCommand"], [88, 44, 77, 30], [88, 46, 77, 31, "command"], [88, 53, 77, 38], [88, 55, 77, 40, "CommandType"], [88, 72, 77, 51], [88, 73, 77, 52, "DrawPath"], [88, 81, 77, 60], [88, 82, 77, 61], [88, 84, 77, 63], [89, 12, 78, 8], [89, 16, 78, 8, "drawPath"], [89, 33, 78, 16], [89, 35, 78, 17, "ctx"], [89, 38, 78, 20], [89, 40, 78, 22, "command"], [89, 47, 78, 29], [89, 48, 78, 30, "props"], [89, 53, 78, 35], [89, 54, 78, 36], [90, 10, 79, 6], [90, 11, 79, 7], [90, 17, 79, 13], [90, 21, 79, 17], [90, 25, 79, 17, "isDrawCommand"], [90, 44, 79, 30], [90, 46, 79, 31, "command"], [90, 53, 79, 38], [90, 55, 79, 40, "CommandType"], [90, 72, 79, 51], [90, 73, 79, 52, "DrawRect"], [90, 81, 79, 60], [90, 82, 79, 61], [90, 84, 79, 63], [91, 12, 80, 8], [91, 16, 80, 8, "drawRect"], [91, 33, 80, 16], [91, 35, 80, 17, "ctx"], [91, 38, 80, 20], [91, 40, 80, 22, "command"], [91, 47, 80, 29], [91, 48, 80, 30, "props"], [91, 53, 80, 35], [91, 54, 80, 36], [92, 10, 81, 6], [92, 11, 81, 7], [92, 17, 81, 13], [92, 21, 81, 17], [92, 25, 81, 17, "isDrawCommand"], [92, 44, 81, 30], [92, 46, 81, 31, "command"], [92, 53, 81, 38], [92, 55, 81, 40, "CommandType"], [92, 72, 81, 51], [92, 73, 81, 52, "DrawRRect"], [92, 82, 81, 61], [92, 83, 81, 62], [92, 85, 81, 64], [93, 12, 82, 8], [93, 16, 82, 8, "drawRRect"], [93, 34, 82, 17], [93, 36, 82, 18, "ctx"], [93, 39, 82, 21], [93, 41, 82, 23, "command"], [93, 48, 82, 30], [93, 49, 82, 31, "props"], [93, 54, 82, 36], [93, 55, 82, 37], [94, 10, 83, 6], [94, 11, 83, 7], [94, 17, 83, 13], [94, 21, 83, 17], [94, 25, 83, 17, "isDrawCommand"], [94, 44, 83, 30], [94, 46, 83, 31, "command"], [94, 53, 83, 38], [94, 55, 83, 40, "CommandType"], [94, 72, 83, 51], [94, 73, 83, 52, "DrawOval"], [94, 81, 83, 60], [94, 82, 83, 61], [94, 84, 83, 63], [95, 12, 84, 8], [95, 16, 84, 8, "drawOval"], [95, 33, 84, 16], [95, 35, 84, 17, "ctx"], [95, 38, 84, 20], [95, 40, 84, 22, "command"], [95, 47, 84, 29], [95, 48, 84, 30, "props"], [95, 53, 84, 35], [95, 54, 84, 36], [96, 10, 85, 6], [96, 11, 85, 7], [96, 17, 85, 13], [96, 21, 85, 17], [96, 25, 85, 17, "isDrawCommand"], [96, 44, 85, 30], [96, 46, 85, 31, "command"], [96, 53, 85, 38], [96, 55, 85, 40, "CommandType"], [96, 72, 85, 51], [96, 73, 85, 52, "DrawLine"], [96, 81, 85, 60], [96, 82, 85, 61], [96, 84, 85, 63], [97, 12, 86, 8], [97, 16, 86, 8, "drawLine"], [97, 33, 86, 16], [97, 35, 86, 17, "ctx"], [97, 38, 86, 20], [97, 40, 86, 22, "command"], [97, 47, 86, 29], [97, 48, 86, 30, "props"], [97, 53, 86, 35], [97, 54, 86, 36], [98, 10, 87, 6], [98, 11, 87, 7], [98, 17, 87, 13], [98, 21, 87, 17], [98, 25, 87, 17, "isDrawCommand"], [98, 44, 87, 30], [98, 46, 87, 31, "command"], [98, 53, 87, 38], [98, 55, 87, 40, "CommandType"], [98, 72, 87, 51], [98, 73, 87, 52, "DrawPatch"], [98, 82, 87, 61], [98, 83, 87, 62], [98, 85, 87, 64], [99, 12, 88, 8], [99, 16, 88, 8, "drawPatch"], [99, 34, 88, 17], [99, 36, 88, 18, "ctx"], [99, 39, 88, 21], [99, 41, 88, 23, "command"], [99, 48, 88, 30], [99, 49, 88, 31, "props"], [99, 54, 88, 36], [99, 55, 88, 37], [100, 10, 89, 6], [100, 11, 89, 7], [100, 17, 89, 13], [100, 21, 89, 17], [100, 25, 89, 17, "isDrawCommand"], [100, 44, 89, 30], [100, 46, 89, 31, "command"], [100, 53, 89, 38], [100, 55, 89, 40, "CommandType"], [100, 72, 89, 51], [100, 73, 89, 52, "DrawVertices"], [100, 85, 89, 64], [100, 86, 89, 65], [100, 88, 89, 67], [101, 12, 90, 8], [101, 16, 90, 8, "drawVertices"], [101, 37, 90, 20], [101, 39, 90, 21, "ctx"], [101, 42, 90, 24], [101, 44, 90, 26, "command"], [101, 51, 90, 33], [101, 52, 90, 34, "props"], [101, 57, 90, 39], [101, 58, 90, 40], [102, 10, 91, 6], [102, 11, 91, 7], [102, 17, 91, 13], [102, 21, 91, 17], [102, 25, 91, 17, "isDrawCommand"], [102, 44, 91, 30], [102, 46, 91, 31, "command"], [102, 53, 91, 38], [102, 55, 91, 40, "CommandType"], [102, 72, 91, 51], [102, 73, 91, 52, "DrawDiffRect"], [102, 85, 91, 64], [102, 86, 91, 65], [102, 88, 91, 67], [103, 12, 92, 8], [103, 16, 92, 8, "drawDiffRect"], [103, 37, 92, 20], [103, 39, 92, 21, "ctx"], [103, 42, 92, 24], [103, 44, 92, 26, "command"], [103, 51, 92, 33], [103, 52, 92, 34, "props"], [103, 57, 92, 39], [103, 58, 92, 40], [104, 10, 93, 6], [104, 11, 93, 7], [104, 17, 93, 13], [104, 21, 93, 17], [104, 25, 93, 17, "isDrawCommand"], [104, 44, 93, 30], [104, 46, 93, 31, "command"], [104, 53, 93, 38], [104, 55, 93, 40, "CommandType"], [104, 72, 93, 51], [104, 73, 93, 52, "DrawText"], [104, 81, 93, 60], [104, 82, 93, 61], [104, 84, 93, 63], [105, 12, 94, 8], [105, 16, 94, 8, "drawText"], [105, 33, 94, 16], [105, 35, 94, 17, "ctx"], [105, 38, 94, 20], [105, 40, 94, 22, "command"], [105, 47, 94, 29], [105, 48, 94, 30, "props"], [105, 53, 94, 35], [105, 54, 94, 36], [106, 10, 95, 6], [106, 11, 95, 7], [106, 17, 95, 13], [106, 21, 95, 17], [106, 25, 95, 17, "isDrawCommand"], [106, 44, 95, 30], [106, 46, 95, 31, "command"], [106, 53, 95, 38], [106, 55, 95, 40, "CommandType"], [106, 72, 95, 51], [106, 73, 95, 52, "DrawTextPath"], [106, 85, 95, 64], [106, 86, 95, 65], [106, 88, 95, 67], [107, 12, 96, 8], [107, 16, 96, 8, "drawTextPath"], [107, 37, 96, 20], [107, 39, 96, 21, "ctx"], [107, 42, 96, 24], [107, 44, 96, 26, "command"], [107, 51, 96, 33], [107, 52, 96, 34, "props"], [107, 57, 96, 39], [107, 58, 96, 40], [108, 10, 97, 6], [108, 11, 97, 7], [108, 17, 97, 13], [108, 21, 97, 17], [108, 25, 97, 17, "isDrawCommand"], [108, 44, 97, 30], [108, 46, 97, 31, "command"], [108, 53, 97, 38], [108, 55, 97, 40, "CommandType"], [108, 72, 97, 51], [108, 73, 97, 52, "DrawTextBlob"], [108, 85, 97, 64], [108, 86, 97, 65], [108, 88, 97, 67], [109, 12, 98, 8], [109, 16, 98, 8, "drawTextBlob"], [109, 37, 98, 20], [109, 39, 98, 21, "ctx"], [109, 42, 98, 24], [109, 44, 98, 26, "command"], [109, 51, 98, 33], [109, 52, 98, 34, "props"], [109, 57, 98, 39], [109, 58, 98, 40], [110, 10, 99, 6], [110, 11, 99, 7], [110, 17, 99, 13], [110, 21, 99, 17], [110, 25, 99, 17, "isDrawCommand"], [110, 44, 99, 30], [110, 46, 99, 31, "command"], [110, 53, 99, 38], [110, 55, 99, 40, "CommandType"], [110, 72, 99, 51], [110, 73, 99, 52, "DrawGlyphs"], [110, 83, 99, 62], [110, 84, 99, 63], [110, 86, 99, 65], [111, 12, 100, 8], [111, 16, 100, 8, "drawGlyphs"], [111, 35, 100, 18], [111, 37, 100, 19, "ctx"], [111, 40, 100, 22], [111, 42, 100, 24, "command"], [111, 49, 100, 31], [111, 50, 100, 32, "props"], [111, 55, 100, 37], [111, 56, 100, 38], [112, 10, 101, 6], [112, 11, 101, 7], [112, 17, 101, 13], [112, 21, 101, 17], [112, 25, 101, 17, "isDrawCommand"], [112, 44, 101, 30], [112, 46, 101, 31, "command"], [112, 53, 101, 38], [112, 55, 101, 40, "CommandType"], [112, 72, 101, 51], [112, 73, 101, 52, "DrawPicture"], [112, 84, 101, 63], [112, 85, 101, 64], [112, 87, 101, 66], [113, 12, 102, 8], [113, 16, 102, 8, "drawPicture"], [113, 36, 102, 19], [113, 38, 102, 20, "ctx"], [113, 41, 102, 23], [113, 43, 102, 25, "command"], [113, 50, 102, 32], [113, 51, 102, 33, "props"], [113, 56, 102, 38], [113, 57, 102, 39], [114, 10, 103, 6], [114, 11, 103, 7], [114, 17, 103, 13], [114, 21, 103, 17], [114, 25, 103, 17, "isDrawCommand"], [114, 44, 103, 30], [114, 46, 103, 31, "command"], [114, 53, 103, 38], [114, 55, 103, 40, "CommandType"], [114, 72, 103, 51], [114, 73, 103, 52, "DrawImageSVG"], [114, 85, 103, 64], [114, 86, 103, 65], [114, 88, 103, 67], [115, 12, 104, 8], [115, 16, 104, 8, "drawImageSVG"], [115, 37, 104, 20], [115, 39, 104, 21, "ctx"], [115, 42, 104, 24], [115, 44, 104, 26, "command"], [115, 51, 104, 33], [115, 52, 104, 34, "props"], [115, 57, 104, 39], [115, 58, 104, 40], [116, 10, 105, 6], [116, 11, 105, 7], [116, 17, 105, 13], [116, 21, 105, 17], [116, 25, 105, 17, "isDrawCommand"], [116, 44, 105, 30], [116, 46, 105, 31, "command"], [116, 53, 105, 38], [116, 55, 105, 40, "CommandType"], [116, 72, 105, 51], [116, 73, 105, 52, "DrawParagraph"], [116, 86, 105, 65], [116, 87, 105, 66], [116, 89, 105, 68], [117, 12, 106, 8], [117, 16, 106, 8, "drawParagraph"], [117, 38, 106, 21], [117, 40, 106, 22, "ctx"], [117, 43, 106, 25], [117, 45, 106, 27, "command"], [117, 52, 106, 34], [117, 53, 106, 35, "props"], [117, 58, 106, 40], [117, 59, 106, 41], [118, 10, 107, 6], [118, 11, 107, 7], [118, 17, 107, 13], [118, 21, 107, 17], [118, 25, 107, 17, "isDrawCommand"], [118, 44, 107, 30], [118, 46, 107, 31, "command"], [118, 53, 107, 38], [118, 55, 107, 40, "CommandType"], [118, 72, 107, 51], [118, 73, 107, 52, "DrawAtlas"], [118, 82, 107, 61], [118, 83, 107, 62], [118, 85, 107, 64], [119, 12, 108, 8], [119, 16, 108, 8, "drawAtlas"], [119, 34, 108, 17], [119, 36, 108, 18, "ctx"], [119, 39, 108, 21], [119, 41, 108, 23, "command"], [119, 48, 108, 30], [119, 49, 108, 31, "props"], [119, 54, 108, 36], [119, 55, 108, 37], [120, 10, 109, 6], [120, 11, 109, 7], [120, 17, 109, 13], [121, 12, 110, 8, "console"], [121, 19, 110, 15], [121, 20, 110, 16, "warn"], [121, 24, 110, 20], [121, 25, 110, 21], [121, 45, 110, 41, "command"], [121, 52, 110, 48], [121, 53, 110, 49, "type"], [121, 57, 110, 53], [121, 59, 110, 55], [121, 60, 110, 56], [122, 10, 111, 6], [123, 10, 112, 6, "ctx"], [123, 13, 112, 9], [123, 14, 112, 10, "paints"], [123, 20, 112, 16], [123, 21, 112, 17, "pop"], [123, 24, 112, 20], [123, 25, 112, 21], [123, 26, 112, 22], [124, 8, 113, 4], [124, 9, 113, 5], [124, 10, 113, 6], [125, 6, 114, 2], [126, 4, 115, 0], [126, 5, 115, 1], [127, 4, 115, 1, "play"], [127, 8, 115, 1], [127, 9, 115, 1, "__closure"], [127, 18, 115, 1], [128, 6, 115, 1, "isGroup"], [128, 13, 115, 1], [128, 15, 13, 6, "isGroup"], [128, 28, 13, 13], [129, 6, 13, 13, "materializeCommand"], [129, 24, 13, 13], [129, 26, 17, 18, "materializeCommand"], [129, 50, 17, 36], [130, 6, 17, 36, "isCommand"], [130, 15, 17, 36], [130, 17, 18, 6, "isCommand"], [130, 32, 18, 15], [131, 6, 18, 15, "CommandType"], [131, 17, 18, 15], [131, 19, 18, 25, "CommandType"], [131, 36, 18, 36], [132, 6, 18, 36, "isDrawCommand"], [132, 19, 18, 36], [132, 21, 24, 13, "isDrawCommand"], [132, 40, 24, 26], [133, 6, 24, 26, "setPaintProperties"], [133, 24, 24, 26], [133, 26, 29, 6, "setPaintProperties"], [133, 51, 29, 24], [134, 6, 29, 24, "composeColorFilters"], [134, 25, 29, 24], [134, 27, 34, 4, "composeColorFilters"], [134, 60, 34, 23], [135, 6, 34, 23, "isPushColorFilter"], [135, 23, 34, 23], [135, 25, 44, 13, "isPushColorFilter"], [135, 56, 44, 30], [136, 6, 44, 30, "pushColorFilter"], [136, 21, 44, 30], [136, 23, 45, 4, "pushColorFilter"], [136, 52, 45, 19], [137, 6, 45, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [137, 18, 45, 19], [137, 20, 46, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [137, 41, 46, 25], [138, 6, 46, 25, "push<PERSON><PERSON>er"], [138, 16, 46, 25], [138, 18, 47, 4, "push<PERSON><PERSON>er"], [138, 37, 47, 14], [139, 6, 47, 14, "isPushImageFilter"], [139, 23, 47, 14], [139, 25, 48, 13, "isPushImageFilter"], [139, 56, 48, 30], [140, 6, 48, 30, "pushImageFilter"], [140, 21, 48, 30], [140, 23, 49, 4, "pushImageFilter"], [140, 52, 49, 19], [141, 6, 49, 19, "isPushPathEffect"], [141, 22, 49, 19], [141, 24, 50, 13, "isPushPathEffect"], [141, 53, 50, 29], [142, 6, 50, 29, "pushPathEffect"], [142, 20, 50, 29], [142, 22, 51, 4, "pushPathEffect"], [142, 49, 51, 18], [143, 6, 51, 18, "composePathEffects"], [143, 24, 51, 18], [143, 26, 53, 4, "composePathEffects"], [143, 57, 53, 22], [144, 6, 53, 22, "composeImageFilters"], [144, 25, 53, 22], [144, 27, 55, 4, "composeImageFilters"], [144, 60, 55, 23], [145, 6, 55, 23, "setBlurMaskFilter"], [145, 23, 55, 23], [145, 25, 57, 4, "setBlurMaskFilter"], [145, 56, 57, 21], [146, 6, 57, 21, "saveCTM"], [146, 13, 57, 21], [146, 15, 59, 4, "saveCTM"], [146, 27, 59, 11], [147, 6, 59, 11, "isBoxCommand"], [147, 18, 59, 11], [147, 20, 67, 10, "isBoxCommand"], [147, 37, 67, 22], [148, 6, 67, 22, "drawBox"], [148, 13, 67, 22], [148, 15, 68, 8, "drawBox"], [148, 27, 68, 15], [149, 6, 68, 15, "drawImage"], [149, 15, 68, 15], [149, 17, 72, 8, "drawImage"], [149, 35, 72, 17], [150, 6, 72, 17, "drawCircle"], [150, 16, 72, 17], [150, 18, 74, 8, "drawCircle"], [150, 37, 74, 18], [151, 6, 74, 18, "drawPoints"], [151, 16, 74, 18], [151, 18, 76, 8, "drawPoints"], [151, 37, 76, 18], [152, 6, 76, 18, "drawPath"], [152, 14, 76, 18], [152, 16, 78, 8, "drawPath"], [152, 33, 78, 16], [153, 6, 78, 16, "drawRect"], [153, 14, 78, 16], [153, 16, 80, 8, "drawRect"], [153, 33, 80, 16], [154, 6, 80, 16, "drawRRect"], [154, 15, 80, 16], [154, 17, 82, 8, "drawRRect"], [154, 35, 82, 17], [155, 6, 82, 17, "drawOval"], [155, 14, 82, 17], [155, 16, 84, 8, "drawOval"], [155, 33, 84, 16], [156, 6, 84, 16, "drawLine"], [156, 14, 84, 16], [156, 16, 86, 8, "drawLine"], [156, 33, 86, 16], [157, 6, 86, 16, "drawPatch"], [157, 15, 86, 16], [157, 17, 88, 8, "drawPatch"], [157, 35, 88, 17], [158, 6, 88, 17, "drawVertices"], [158, 18, 88, 17], [158, 20, 90, 8, "drawVertices"], [158, 41, 90, 20], [159, 6, 90, 20, "drawDiffRect"], [159, 18, 90, 20], [159, 20, 92, 8, "drawDiffRect"], [159, 41, 92, 20], [160, 6, 92, 20, "drawText"], [160, 14, 92, 20], [160, 16, 94, 8, "drawText"], [160, 33, 94, 16], [161, 6, 94, 16, "drawTextPath"], [161, 18, 94, 16], [161, 20, 96, 8, "drawTextPath"], [161, 41, 96, 20], [162, 6, 96, 20, "drawTextBlob"], [162, 18, 96, 20], [162, 20, 98, 8, "drawTextBlob"], [162, 41, 98, 20], [163, 6, 98, 20, "drawGlyphs"], [163, 16, 98, 20], [163, 18, 100, 8, "drawGlyphs"], [163, 37, 100, 18], [164, 6, 100, 18, "drawPicture"], [164, 17, 100, 18], [164, 19, 102, 8, "drawPicture"], [164, 39, 102, 19], [165, 6, 102, 19, "drawImageSVG"], [165, 18, 102, 19], [165, 20, 104, 8, "drawImageSVG"], [165, 41, 104, 20], [166, 6, 104, 20, "drawParagraph"], [166, 19, 104, 20], [166, 21, 106, 8, "drawParagraph"], [166, 43, 106, 21], [167, 6, 106, 21, "drawAtlas"], [167, 15, 106, 21], [167, 17, 108, 8, "drawAtlas"], [168, 4, 108, 17], [169, 4, 108, 17, "play"], [169, 8, 108, 17], [169, 9, 108, 17, "__workletHash"], [169, 22, 108, 17], [170, 4, 108, 17, "play"], [170, 8, 108, 17], [170, 9, 108, 17, "__initData"], [170, 19, 108, 17], [170, 22, 108, 17, "_worklet_6951022178641_init_data"], [170, 54, 108, 17], [171, 4, 108, 17, "play"], [171, 8, 108, 17], [171, 9, 108, 17, "__stackDetails"], [171, 23, 108, 17], [171, 26, 108, 17, "_e"], [171, 28, 108, 17], [172, 4, 108, 17], [172, 11, 108, 17, "play"], [172, 15, 108, 17], [173, 2, 108, 17], [173, 3, 10, 0], [174, 2, 10, 0], [174, 8, 10, 0, "_worklet_9915340751545_init_data"], [174, 40, 10, 0], [175, 4, 10, 0, "code"], [175, 8, 10, 0], [176, 4, 10, 0, "location"], [176, 12, 10, 0], [177, 4, 10, 0, "sourceMap"], [177, 13, 10, 0], [178, 4, 10, 0, "version"], [178, 11, 10, 0], [179, 2, 10, 0], [180, 2, 116, 7], [180, 8, 116, 13, "replay"], [180, 14, 116, 19], [180, 17, 116, 19, "exports"], [180, 24, 116, 19], [180, 25, 116, 19, "replay"], [180, 31, 116, 19], [180, 34, 116, 22], [181, 4, 116, 22], [181, 10, 116, 22, "_e"], [181, 12, 116, 22], [181, 20, 116, 22, "global"], [181, 26, 116, 22], [181, 27, 116, 22, "Error"], [181, 32, 116, 22], [182, 4, 116, 22], [182, 10, 116, 22, "PlayerJs2"], [182, 19, 116, 22], [182, 31, 116, 22, "PlayerJs2"], [182, 32, 116, 23, "ctx"], [182, 35, 116, 26], [182, 37, 116, 28, "commands"], [182, 45, 116, 36], [182, 47, 116, 41], [183, 6, 119, 2, "commands"], [183, 14, 119, 10], [183, 15, 119, 11, "for<PERSON>ach"], [183, 22, 119, 18], [183, 23, 119, 19, "command"], [183, 30, 119, 26], [183, 34, 119, 30], [184, 8, 120, 4, "play"], [184, 12, 120, 8], [184, 13, 120, 9, "ctx"], [184, 16, 120, 12], [184, 18, 120, 14, "command"], [184, 25, 120, 21], [184, 26, 120, 22], [185, 6, 121, 2], [185, 7, 121, 3], [185, 8, 121, 4], [186, 4, 122, 0], [186, 5, 122, 1], [187, 4, 122, 1, "PlayerJs2"], [187, 13, 122, 1], [187, 14, 122, 1, "__closure"], [187, 23, 122, 1], [188, 6, 122, 1, "play"], [189, 4, 122, 1], [190, 4, 122, 1, "PlayerJs2"], [190, 13, 122, 1], [190, 14, 122, 1, "__workletHash"], [190, 27, 122, 1], [191, 4, 122, 1, "PlayerJs2"], [191, 13, 122, 1], [191, 14, 122, 1, "__initData"], [191, 24, 122, 1], [191, 27, 122, 1, "_worklet_9915340751545_init_data"], [191, 59, 122, 1], [192, 4, 122, 1, "PlayerJs2"], [192, 13, 122, 1], [192, 14, 122, 1, "__stackDetails"], [192, 28, 122, 1], [192, 31, 122, 1, "_e"], [192, 33, 122, 1], [193, 4, 122, 1], [193, 11, 122, 1, "PlayerJs2"], [193, 20, 122, 1], [194, 2, 122, 1], [194, 3, 116, 22], [194, 5, 122, 1], [195, 0, 122, 2], [195, 3]], "functionMap": {"names": ["<global>", "play", "_command.children.forEach$argument_0", "paints.forEach$argument_0", "replay", "commands.forEach$argument_0"], "mappings": "AAA;ACS;8BCI,yBD;mBEmD;KFgD;CDE;sBIC;mBCG;GDE;CJC"}}, "type": "js/module"}]}