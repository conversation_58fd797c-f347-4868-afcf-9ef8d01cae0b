{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.IS_ENV_WITH_LOCAL_ASSETS = void 0;\n  exports.getLocalAssets = getLocalAssets;\n  exports.getManifest2 = getManifest2;\n  exports.manifestBaseUrl = void 0;\n  const IS_ENV_WITH_LOCAL_ASSETS = exports.IS_ENV_WITH_LOCAL_ASSETS = false;\n  function getLocalAssets() {\n    return {};\n  }\n  function getManifest2() {\n    return {};\n  }\n  // Compute manifest base URL if available\n  const manifestBaseUrl = exports.manifestBaseUrl = null;\n});", "lineCount": 18, "map": [[9, 2, 1, 7], [9, 8, 1, 13, "IS_ENV_WITH_LOCAL_ASSETS"], [9, 32, 1, 37], [9, 35, 1, 37, "exports"], [9, 42, 1, 37], [9, 43, 1, 37, "IS_ENV_WITH_LOCAL_ASSETS"], [9, 67, 1, 37], [9, 70, 1, 40], [9, 75, 1, 45], [10, 2, 2, 7], [10, 11, 2, 16, "getLocalAssets"], [10, 25, 2, 30, "getLocalAssets"], [10, 26, 2, 30], [10, 28, 2, 33], [11, 4, 3, 4], [11, 11, 3, 11], [11, 12, 3, 12], [11, 13, 3, 13], [12, 2, 4, 0], [13, 2, 5, 7], [13, 11, 5, 16, "getManifest2"], [13, 23, 5, 28, "getManifest2"], [13, 24, 5, 28], [13, 26, 5, 31], [14, 4, 6, 4], [14, 11, 6, 11], [14, 12, 6, 12], [14, 13, 6, 13], [15, 2, 7, 0], [16, 2, 8, 0], [17, 2, 9, 7], [17, 8, 9, 13, "manifestBaseUrl"], [17, 23, 9, 28], [17, 26, 9, 28, "exports"], [17, 33, 9, 28], [17, 34, 9, 28, "manifestBaseUrl"], [17, 49, 9, 28], [17, 52, 9, 31], [17, 56, 9, 35], [18, 0, 9, 36], [18, 3]], "functionMap": {"names": ["<global>", "getLocalAssets", "getManifest2"], "mappings": "AAA;OCC;CDE;OEC;CFE"}}, "type": "js/module"}]}