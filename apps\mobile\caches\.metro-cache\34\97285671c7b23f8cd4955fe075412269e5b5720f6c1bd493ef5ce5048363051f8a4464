{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BoxShadow = exports.Box = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  const BoxShadow = props => {\n    return /*#__PURE__*/_react.default.createElement(\"skBoxShadow\", props);\n  };\n  exports.BoxShadow = BoxShadow;\n  const Box = props => {\n    return /*#__PURE__*/_react.default.createElement(\"skBox\", props);\n  };\n  exports.Box = Box;\n});", "lineCount": 16, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 7], [8, 8, 2, 13, "BoxShadow"], [8, 17, 2, 22], [8, 20, 2, 25, "props"], [8, 25, 2, 30], [8, 29, 2, 34], [9, 4, 3, 2], [9, 11, 3, 9], [9, 24, 3, 22, "React"], [9, 38, 3, 27], [9, 39, 3, 28, "createElement"], [9, 52, 3, 41], [9, 53, 3, 42], [9, 66, 3, 55], [9, 68, 3, 57, "props"], [9, 73, 3, 62], [9, 74, 3, 63], [10, 2, 4, 0], [10, 3, 4, 1], [11, 2, 4, 2, "exports"], [11, 9, 4, 2], [11, 10, 4, 2, "BoxShadow"], [11, 19, 4, 2], [11, 22, 4, 2, "BoxShadow"], [11, 31, 4, 2], [12, 2, 5, 7], [12, 8, 5, 13, "Box"], [12, 11, 5, 16], [12, 14, 5, 19, "props"], [12, 19, 5, 24], [12, 23, 5, 28], [13, 4, 6, 2], [13, 11, 6, 9], [13, 24, 6, 22, "React"], [13, 38, 6, 27], [13, 39, 6, 28, "createElement"], [13, 52, 6, 41], [13, 53, 6, 42], [13, 60, 6, 49], [13, 62, 6, 51, "props"], [13, 67, 6, 56], [13, 68, 6, 57], [14, 2, 7, 0], [14, 3, 7, 1], [15, 2, 7, 2, "exports"], [15, 9, 7, 2], [15, 10, 7, 2, "Box"], [15, 13, 7, 2], [15, 16, 7, 2, "Box"], [15, 19, 7, 2], [16, 0, 7, 2], [16, 3]], "functionMap": {"names": ["<global>", "BoxShadow", "Box"], "mappings": "AAA;yBCC;CDE;mBEC;CFE"}}, "type": "js/module"}]}