{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkRuntimeEffect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 58, "index": 89}}], "key": "29pi6saR4HLGVGPVFhqja1+OrGg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkRuntimeEffectFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkRuntimeEffect = require(_dependencyMap[1], \"./JsiSkRuntimeEffect\");\n  class JsiSkRuntimeEffectFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    Make(sksl) {\n      const re = this.CanvasKit.RuntimeEffect.Make(sksl);\n      if (re === null) {\n        return null;\n      }\n      return new _JsiSkRuntimeEffect.JsiSkRuntimeEffect(this.CanvasKit, re, sksl);\n    }\n  }\n  exports.JsiSkRuntimeEffectFactory = JsiSkRuntimeEffectFactory;\n});", "lineCount": 21, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkRuntimeEffect"], [7, 25, 2, 0], [7, 28, 2, 0, "require"], [7, 35, 2, 0], [7, 36, 2, 0, "_dependencyMap"], [7, 50, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkRuntimeEffectFactory"], [8, 33, 3, 38], [8, 42, 3, 47, "Host"], [8, 52, 3, 51], [8, 53, 3, 52], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "Make"], [12, 8, 7, 6, "Make"], [12, 9, 7, 7, "sksl"], [12, 13, 7, 11], [12, 15, 7, 13], [13, 6, 8, 4], [13, 12, 8, 10, "re"], [13, 14, 8, 12], [13, 17, 8, 15], [13, 21, 8, 19], [13, 22, 8, 20, "CanvasKit"], [13, 31, 8, 29], [13, 32, 8, 30, "RuntimeEffect"], [13, 45, 8, 43], [13, 46, 8, 44, "Make"], [13, 50, 8, 48], [13, 51, 8, 49, "sksl"], [13, 55, 8, 53], [13, 56, 8, 54], [14, 6, 9, 4], [14, 10, 9, 8, "re"], [14, 12, 9, 10], [14, 17, 9, 15], [14, 21, 9, 19], [14, 23, 9, 21], [15, 8, 10, 6], [15, 15, 10, 13], [15, 19, 10, 17], [16, 6, 11, 4], [17, 6, 12, 4], [17, 13, 12, 11], [17, 17, 12, 15, "JsiSkRuntimeEffect"], [17, 55, 12, 33], [17, 56, 12, 34], [17, 60, 12, 38], [17, 61, 12, 39, "CanvasKit"], [17, 70, 12, 48], [17, 72, 12, 50, "re"], [17, 74, 12, 52], [17, 76, 12, 54, "sksl"], [17, 80, 12, 58], [17, 81, 12, 59], [18, 4, 13, 2], [19, 2, 14, 0], [20, 2, 14, 1, "exports"], [20, 9, 14, 1], [20, 10, 14, 1, "JsiSkRuntimeEffectFactory"], [20, 35, 14, 1], [20, 38, 14, 1, "JsiSkRuntimeEffectFactory"], [20, 63, 14, 1], [21, 0, 14, 1], [21, 3]], "functionMap": {"names": ["<global>", "JsiSkRuntimeEffectFactory", "constructor", "Make"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFM;CDC"}}, "type": "js/module"}]}