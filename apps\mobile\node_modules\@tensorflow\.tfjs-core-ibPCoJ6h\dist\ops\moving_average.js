"use strict";
/**
 * @license
 * Copyright 2018 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
var tensor_util_1 = require("../tensor_util");
var tensor_util_env_1 = require("../tensor_util_env");
var util = require("../util");
var binary_ops_1 = require("./binary_ops");
var operation_1 = require("./operation");
var tensor_ops_1 = require("./tensor_ops");
/**
 * Compute the moving average of a variable.
 *
 * Without zeroDebias, the moving average operation is defined by:
 *   `v += delta`
 * where
 *   `delta = (1 - decay) * (x - v)`
 *
 * With zeroDebias (default), the `delta` term is scaled to debias the
 * effect of the (assumed) zero-initialization of `v`.
 *   `delta /= (1 - decay ^ step)`
 *
 * For more details on the zero-debiasing algorithm, see:
 *   https://arxiv.org/abs/1412.6980
 *
 * Note that this function is completely stateless and does not keep track of
 * step count. The step count needs to be maintained by the caller and passed
 * in as `step`.
 *
 * @param v The current moving average value.
 * @param x New input value, must have the same shape and dtype as `v`.
 * @param decay The decay factor. Typical values are 0.95 and 0.99.
 * @param step Step count.
 * @param zeroDebias: Whether zeroDebias is to be performed (default: `true`).
 * @returns The new moving average value.
 */
/** @doc {heading: 'Operations', subheading: 'Moving Average'} */
function movingAverage_(v, x, decay, step, zeroDebias) {
    if (zeroDebias === void 0) { zeroDebias = true; }
    var $v = tensor_util_env_1.convertToTensor(v, 'v', 'movingAverage');
    var $x = tensor_util_env_1.convertToTensor(x, 'x', 'movingAverage');
    var $decay = tensor_util_env_1.convertToTensor(decay, 'decay', 'movingAverage');
    tensor_util_1.assertTypesMatch($v, $x);
    util.assert(util.arraysEqual($v.shape, $x.shape), function () { return 'Shape mismatch in v and x'; });
    var one = tensor_ops_1.scalar(1);
    var oneMinusDecay = one.sub($decay);
    var update = $x.sub($v).mul(oneMinusDecay);
    if (zeroDebias) {
        util.assert(step != null, function () { return 'When using zeroDebias: true, step is required.'; });
        var $step = tensor_util_env_1.convertToTensor(step, 'step', 'movingAverage');
        update = update.div(one.sub(binary_ops_1.pow($decay, $step)));
    }
    return $v.add(update);
}
exports.movingAverage = operation_1.op({ movingAverage_: movingAverage_ });
//# sourceMappingURL=moving_average.js.map