{"dependencies": [{"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}, {"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 44, "index": 76}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.processTransform2d = void 0;\n  var _Skia = require(_dependencyMap[0], \"../Skia\");\n  var _types = require(_dependencyMap[1], \"../types\");\n  const _worklet_16113527941833_init_data = {\n    code: \"function MatrixJs1(transforms){const{processTransform,Skia}=this.__closure;return processTransform(Skia.Matrix(),transforms);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\skia\\\\core\\\\Matrix.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"MatrixJs1\\\",\\\"transforms\\\",\\\"processTransform\\\",\\\"Skia\\\",\\\"__closure\\\",\\\"Matrix\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/core/Matrix.js\\\"],\\\"mappings\\\":\\\"AAEkC,SAAAA,UAAAC,UAAc,QAAAC,gBAAA,CAAAC,IAAA,OAAAC,SAAA,CAG9C,MAAO,CAAAF,gBAAgB,CAACC,IAAI,CAACE,MAAM,CAAC,CAAC,CAAEJ,UAAU,CAAC,CACpD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processTransform2d = exports.processTransform2d = function () {\n    const _e = [new global.Error(), -3, -27];\n    const MatrixJs1 = function (transforms) {\n      return (0, _types.processTransform)(_Skia.Skia.Matrix(), transforms);\n    };\n    MatrixJs1.__closure = {\n      processTransform: _types.processTransform,\n      Skia: _Skia.Skia\n    };\n    MatrixJs1.__workletHash = 16113527941833;\n    MatrixJs1.__initData = _worklet_16113527941833_init_data;\n    MatrixJs1.__stackDetails = _e;\n    return MatrixJs1;\n  }();\n});", "lineCount": 28, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Skia"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_types"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 2, 44], [8, 8, 2, 44, "_worklet_16113527941833_init_data"], [8, 41, 2, 44], [9, 4, 2, 44, "code"], [9, 8, 2, 44], [10, 4, 2, 44, "location"], [10, 12, 2, 44], [11, 4, 2, 44, "sourceMap"], [11, 13, 2, 44], [12, 4, 2, 44, "version"], [12, 11, 2, 44], [13, 2, 2, 44], [14, 2, 3, 7], [14, 8, 3, 13, "processTransform2d"], [14, 26, 3, 31], [14, 29, 3, 31, "exports"], [14, 36, 3, 31], [14, 37, 3, 31, "processTransform2d"], [14, 55, 3, 31], [14, 58, 3, 34], [15, 4, 3, 34], [15, 10, 3, 34, "_e"], [15, 12, 3, 34], [15, 20, 3, 34, "global"], [15, 26, 3, 34], [15, 27, 3, 34, "Error"], [15, 32, 3, 34], [16, 4, 3, 34], [16, 10, 3, 34, "MatrixJs1"], [16, 19, 3, 34], [16, 31, 3, 34, "MatrixJs1"], [16, 32, 3, 34, "transforms"], [16, 42, 3, 44], [16, 44, 3, 48], [17, 6, 6, 2], [17, 13, 6, 9], [17, 17, 6, 9, "processTransform"], [17, 40, 6, 25], [17, 42, 6, 26, "Skia"], [17, 52, 6, 30], [17, 53, 6, 31, "Matrix"], [17, 59, 6, 37], [17, 60, 6, 38], [17, 61, 6, 39], [17, 63, 6, 41, "transforms"], [17, 73, 6, 51], [17, 74, 6, 52], [18, 4, 7, 0], [18, 5, 7, 1], [19, 4, 7, 1, "MatrixJs1"], [19, 13, 7, 1], [19, 14, 7, 1, "__closure"], [19, 23, 7, 1], [20, 6, 7, 1, "processTransform"], [20, 22, 7, 1], [20, 24, 6, 9, "processTransform"], [20, 47, 6, 25], [21, 6, 6, 25, "Skia"], [21, 10, 6, 25], [21, 12, 6, 26, "Skia"], [22, 4, 6, 30], [23, 4, 6, 30, "MatrixJs1"], [23, 13, 6, 30], [23, 14, 6, 30, "__workletHash"], [23, 27, 6, 30], [24, 4, 6, 30, "MatrixJs1"], [24, 13, 6, 30], [24, 14, 6, 30, "__initData"], [24, 24, 6, 30], [24, 27, 6, 30, "_worklet_16113527941833_init_data"], [24, 60, 6, 30], [25, 4, 6, 30, "MatrixJs1"], [25, 13, 6, 30], [25, 14, 6, 30, "__stackDetails"], [25, 28, 6, 30], [25, 31, 6, 30, "_e"], [25, 33, 6, 30], [26, 4, 6, 30], [26, 11, 6, 30, "MatrixJs1"], [26, 20, 6, 30], [27, 2, 6, 30], [27, 3, 3, 34], [27, 5, 7, 1], [28, 0, 7, 2], [28, 3]], "functionMap": {"names": ["<global>", "processTransform2d"], "mappings": "AAA;kCCE;CDI"}}, "type": "js/module"}]}