{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 25}, "end": {"line": 2, "column": 34, "index": 59}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "./WebCapabilityUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 60}, "end": {"line": 3, "column": 56, "index": 116}}], "key": "QbGAcPe6ps2J4vvOHxE4NHI5sfY=", "exportNames": ["*"]}}, {"name": "./WebConstants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 117}, "end": {"line": 4, "column": 93, "index": 210}}], "key": "VYBjMDeNhUYVRZIl4m+mFrGQcXE=", "exportNames": ["*"]}}, {"name": "./WebUserMediaManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 211}, "end": {"line": 5, "column": 62, "index": 273}}], "key": "3rDgd/gjl+fuboOhAcsvaqlX7vw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.capture = capture;\n  exports.captureImage = captureImage;\n  exports.captureImageContext = captureImageContext;\n  exports.captureImageData = captureImageData;\n  exports.compareStreams = compareStreams;\n  exports.getIdealConstraints = getIdealConstraints;\n  exports.getImageSize = getImageSize;\n  exports.getPreferredStreamDevice = getPreferredStreamDevice;\n  exports.getStreamDevice = getStreamDevice;\n  exports.hasValidConstraints = hasValidConstraints;\n  exports.isCapabilityAvailable = isCapabilityAvailable;\n  exports.isWebKit = isWebKit;\n  exports.setVideoSource = setVideoSource;\n  exports.stopMediaStream = stopMediaStream;\n  exports.syncTrackCapabilities = syncTrackCapabilities;\n  exports.toDataURL = toDataURL;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[1], \"invariant\"));\n  var CapabilityUtils = _interopRequireWildcard(require(_dependencyMap[2], \"./WebCapabilityUtils\"));\n  var _WebConstants = require(_dependencyMap[3], \"./WebConstants\");\n  var _WebUserMediaManager = require(_dependencyMap[4], \"./WebUserMediaManager\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /* eslint-env browser */\n\n  function getImageSize(videoWidth, videoHeight, scale) {\n    const width = videoWidth * scale;\n    const ratio = videoWidth / width;\n    const height = videoHeight / ratio;\n    return {\n      width,\n      height\n    };\n  }\n  function toDataURL(canvas, imageType, quality) {\n    const types = ['png', 'jpg'];\n    (0, _invariant.default)(types.includes(imageType), `expo-camera: ${imageType} is not a valid ImageType. Expected a string from: ${types.join(', ')}`);\n    const format = _WebConstants.ImageTypeFormat[imageType];\n    if (imageType === 'jpg') {\n      (0, _invariant.default)(quality <= 1 && quality >= 0, `expo-camera: ${quality} is not a valid image quality. Expected a number from 0...1`);\n      return canvas.toDataURL(format, quality);\n    } else {\n      return canvas.toDataURL(format);\n    }\n  }\n  function hasValidConstraints(preferredCameraType, width, height) {\n    return preferredCameraType !== undefined && width !== undefined && height !== undefined;\n  }\n  function ensureCameraPictureOptions(config) {\n    const captureOptions = {\n      scale: 1,\n      imageType: 'png',\n      isImageMirror: false\n    };\n    for (const key in config) {\n      const prop = key;\n      if (prop in config && config[prop] !== undefined && prop in captureOptions) {\n        captureOptions[prop] = config[prop];\n      }\n    }\n    return captureOptions;\n  }\n  const DEFAULT_QUALITY = 0.92;\n  function captureImageData(video, pictureOptions = {}) {\n    if (!video || video.readyState !== video.HAVE_ENOUGH_DATA) {\n      return null;\n    }\n    const canvas = captureImageContext(video, pictureOptions);\n    const context = canvas.getContext('2d', {\n      alpha: false\n    });\n    if (!context || !canvas.width || !canvas.height) {\n      return null;\n    }\n    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);\n    return imageData;\n  }\n  function captureImageContext(video, {\n    scale = 1,\n    isImageMirror = false\n  }) {\n    const {\n      videoWidth,\n      videoHeight\n    } = video;\n    const {\n      width,\n      height\n    } = getImageSize(videoWidth, videoHeight, scale);\n    // Build the canvas size and draw the camera image to the context from video\n    const canvas = document.createElement('canvas');\n    canvas.width = width;\n    canvas.height = height;\n    const context = canvas.getContext('2d', {\n      alpha: false\n    });\n    if (!context) {\n      // Should never be called\n      throw new Error('Context is not defined');\n    }\n    // sharp image details\n    // context.imageSmoothingEnabled = false;\n    // Flip horizontally (as css transform: rotateY(180deg))\n    if (isImageMirror) {\n      context.setTransform(-1, 0, 0, 1, canvas.width, 0);\n    }\n    context.drawImage(video, 0, 0, width, height);\n    return canvas;\n  }\n  function captureImage(video, pictureOptions) {\n    const config = ensureCameraPictureOptions(pictureOptions);\n    const canvas = captureImageContext(video, config);\n    const {\n      imageType,\n      quality = DEFAULT_QUALITY\n    } = config;\n    return toDataURL(canvas, imageType, quality);\n  }\n  function getSupportedConstraints() {\n    if (navigator.mediaDevices && navigator.mediaDevices.getSupportedConstraints) {\n      return navigator.mediaDevices.getSupportedConstraints();\n    }\n    return null;\n  }\n  function getIdealConstraints(preferredCameraType, width, height) {\n    const preferredConstraints = {\n      audio: false,\n      video: {}\n    };\n    if (hasValidConstraints(preferredCameraType, width, height)) {\n      return _WebConstants.MinimumConstraints;\n    }\n    const supports = getSupportedConstraints();\n    // TODO(Bacon): Test this\n    if (!supports || !supports.facingMode || !supports.width || !supports.height) {\n      return _WebConstants.MinimumConstraints;\n    }\n    const types = ['front', 'back'];\n    if (preferredCameraType && types.includes(preferredCameraType)) {\n      const facingMode = _WebConstants.CameraTypeToFacingMode[preferredCameraType];\n      if (isWebKit()) {\n        const key = facingMode === 'user' ? 'exact' : 'ideal';\n        preferredConstraints.video.facingMode = {\n          [key]: facingMode\n        };\n      } else {\n        preferredConstraints.video.facingMode = {\n          ideal: _WebConstants.CameraTypeToFacingMode[preferredCameraType]\n        };\n      }\n    }\n    if (isMediaTrackConstraints(preferredConstraints.video)) {\n      preferredConstraints.video.width = width;\n      preferredConstraints.video.height = height;\n    }\n    return preferredConstraints;\n  }\n  function isMediaTrackConstraints(input) {\n    return input && typeof input.video !== 'boolean';\n  }\n  /**\n   * Invoke getStreamDevice a second time with the opposing camera type if the preferred type cannot be retrieved.\n   *\n   * @param preferredCameraType\n   * @param preferredWidth\n   * @param preferredHeight\n   */\n  async function getPreferredStreamDevice(preferredCameraType, preferredWidth, preferredHeight) {\n    try {\n      return await getStreamDevice(preferredCameraType, preferredWidth, preferredHeight);\n    } catch (error) {\n      // A hack on desktop browsers to ensure any camera is used.\n      // eslint-disable-next-line no-undef\n      if (error instanceof OverconstrainedError && error.constraint === 'facingMode') {\n        const nextCameraType = preferredCameraType === 'back' ? 'front' : 'back';\n        return await getStreamDevice(nextCameraType, preferredWidth, preferredHeight);\n      }\n      throw error;\n    }\n  }\n  async function getStreamDevice(preferredCameraType, preferredWidth, preferredHeight) {\n    const constraints = getIdealConstraints(preferredCameraType, preferredWidth, preferredHeight);\n    const stream = await (0, _WebUserMediaManager.requestUserMediaAsync)(constraints);\n    return stream;\n  }\n  function isWebKit() {\n    return /WebKit/.test(navigator.userAgent) && !/Edg/.test(navigator.userAgent);\n  }\n  function compareStreams(a, b) {\n    if (!a || !b) {\n      return false;\n    }\n    const settingsA = a.getTracks()[0].getSettings();\n    const settingsB = b.getTracks()[0].getSettings();\n    return settingsA.deviceId === settingsB.deviceId;\n  }\n  function capture(video, settings, config) {\n    const base64 = captureImage(video, config);\n    const capturedPicture = {\n      uri: base64,\n      base64,\n      width: 0,\n      height: 0,\n      format: config.imageType ?? 'jpg'\n    };\n    if (settings) {\n      const {\n        width = 0,\n        height = 0\n      } = settings;\n      capturedPicture.width = width;\n      capturedPicture.height = height;\n      capturedPicture.exif = settings;\n    }\n    if (config.onPictureSaved) {\n      config.onPictureSaved(capturedPicture);\n    }\n    return capturedPicture;\n  }\n  async function syncTrackCapabilities(cameraType, stream, settings = {}) {\n    if (stream?.getVideoTracks) {\n      await Promise.all(stream.getVideoTracks().map(track => onCapabilitiesReady(cameraType, track, settings)));\n    }\n  }\n  // https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints\n  async function onCapabilitiesReady(cameraType, track, settings = {}) {\n    if (typeof track.getCapabilities !== 'function') {\n      return;\n    }\n    const capabilities = track.getCapabilities();\n    // Create an empty object because if you set a constraint that isn't available an error will be thrown.\n    const constraints = {};\n    // TODO(Bacon): Add `pointsOfInterest` support\n    const clampedValues = ['exposureCompensation', 'colorTemperature', 'iso', 'brightness', 'contrast', 'saturation', 'sharpness', 'focusDistance', 'zoom'];\n    for (const property of clampedValues) {\n      if (capabilities[property]) {\n        constraints[property] = convertNormalizedSetting(capabilities[property], settings[property]);\n      }\n    }\n    function validatedInternalConstrainedValue(constraintKey, settingsKey, converter) {\n      const convertedSetting = converter(settings[settingsKey]);\n      return validatedConstrainedValue({\n        constraintKey,\n        settingsKey,\n        convertedSetting,\n        capabilities,\n        settings,\n        cameraType\n      });\n    }\n    if (capabilities.focusMode && settings.autoFocus !== undefined) {\n      constraints.focusMode = validatedInternalConstrainedValue('focusMode', 'autoFocus', CapabilityUtils.convertAutoFocusJSONToNative);\n    }\n    if (capabilities.torch && settings.flashMode !== undefined) {\n      constraints.torch = validatedInternalConstrainedValue('torch', 'flashMode', CapabilityUtils.convertFlashModeJSONToNative);\n    }\n    if (capabilities.whiteBalanceMode && settings.whiteBalance !== undefined) {\n      constraints.whiteBalanceMode = validatedInternalConstrainedValue('whiteBalanceMode', 'whiteBalance', CapabilityUtils.convertWhiteBalanceJSONToNative);\n    }\n    try {\n      await track.applyConstraints({\n        advanced: [constraints]\n      });\n    } catch (error) {\n      if (__DEV__) console.warn('Failed to apply constraints', error);\n    }\n  }\n  function stopMediaStream(stream) {\n    if (!stream) {\n      return;\n    }\n    if (stream.getAudioTracks) {\n      stream.getAudioTracks().forEach(track => track.stop());\n    }\n    if (stream.getVideoTracks) {\n      stream.getVideoTracks().forEach(track => track.stop());\n    }\n    if (isMediaStreamTrack(stream)) {\n      stream.stop();\n    }\n  }\n  function setVideoSource(video, stream) {\n    const createObjectURL = window.URL.createObjectURL ?? window.webkitURL.createObjectURL;\n    if (typeof video.srcObject !== 'undefined') {\n      video.srcObject = stream;\n    } else if (typeof video.mozSrcObject !== 'undefined') {\n      video.mozSrcObject = stream;\n    } else if (stream && createObjectURL) {\n      video.src = createObjectURL(stream);\n    }\n    if (!stream) {\n      const revokeObjectURL = window.URL.revokeObjectURL ?? window.webkitURL.revokeObjectURL;\n      const source = video.src ?? video.srcObject ?? video.mozSrcObject;\n      if (revokeObjectURL && typeof source === 'string') {\n        revokeObjectURL(source);\n      }\n    }\n  }\n  function isCapabilityAvailable(video, keyName) {\n    const stream = video.srcObject;\n    if (stream instanceof MediaStream) {\n      const videoTrack = stream.getVideoTracks()[0];\n      return !!videoTrack.getCapabilities?.()?.[keyName];\n    }\n    return false;\n  }\n  function isMediaStreamTrack(input) {\n    return typeof input.stop === 'function';\n  }\n  function convertNormalizedSetting(range, value) {\n    if (!value) {\n      return;\n    }\n    // convert the normalized incoming setting to the native camera zoom range\n    const converted = convertRange(value, [range.min, range.max]);\n    // clamp value so we don't get an error\n    return Math.min(range.max, Math.max(range.min, converted));\n  }\n  function convertRange(value, r2, r1 = [0, 1]) {\n    return (value - r1[0]) * (r2[1] - r2[0]) / (r1[1] - r1[0]) + r2[0];\n  }\n  function validatedConstrainedValue(props) {\n    const {\n      constraintKey,\n      settingsKey,\n      convertedSetting,\n      capabilities,\n      settings,\n      cameraType\n    } = props;\n    const setting = settings[settingsKey];\n    if (Array.isArray(capabilities[constraintKey]) && convertedSetting && !capabilities[constraintKey].includes(convertedSetting)) {\n      if (__DEV__) {\n        // Only warn in dev mode.\n        console.warn(` { ${settingsKey}: \"${setting}\" } (converted to \"${convertedSetting}\" in the browser) is not supported for camera type \"${cameraType}\" in your browser. Using the default value instead.`);\n      }\n      return undefined;\n    }\n    return convertedSetting;\n  }\n});", "lineCount": 344, "map": [[22, 2, 2, 0], [22, 6, 2, 0, "_invariant"], [22, 16, 2, 0], [22, 19, 2, 0, "_interopRequireDefault"], [22, 41, 2, 0], [22, 42, 2, 0, "require"], [22, 49, 2, 0], [22, 50, 2, 0, "_dependencyMap"], [22, 64, 2, 0], [23, 2, 3, 0], [23, 6, 3, 0, "CapabilityUtils"], [23, 21, 3, 0], [23, 24, 3, 0, "_interopRequireWildcard"], [23, 47, 3, 0], [23, 48, 3, 0, "require"], [23, 55, 3, 0], [23, 56, 3, 0, "_dependencyMap"], [23, 70, 3, 0], [24, 2, 4, 0], [24, 6, 4, 0, "_WebConstants"], [24, 19, 4, 0], [24, 22, 4, 0, "require"], [24, 29, 4, 0], [24, 30, 4, 0, "_dependencyMap"], [24, 44, 4, 0], [25, 2, 5, 0], [25, 6, 5, 0, "_WebUserMediaManager"], [25, 26, 5, 0], [25, 29, 5, 0, "require"], [25, 36, 5, 0], [25, 37, 5, 0, "_dependencyMap"], [25, 51, 5, 0], [26, 2, 5, 62], [26, 11, 5, 62, "_interopRequireWildcard"], [26, 35, 5, 62, "e"], [26, 36, 5, 62], [26, 38, 5, 62, "t"], [26, 39, 5, 62], [26, 68, 5, 62, "WeakMap"], [26, 75, 5, 62], [26, 81, 5, 62, "r"], [26, 82, 5, 62], [26, 89, 5, 62, "WeakMap"], [26, 96, 5, 62], [26, 100, 5, 62, "n"], [26, 101, 5, 62], [26, 108, 5, 62, "WeakMap"], [26, 115, 5, 62], [26, 127, 5, 62, "_interopRequireWildcard"], [26, 150, 5, 62], [26, 162, 5, 62, "_interopRequireWildcard"], [26, 163, 5, 62, "e"], [26, 164, 5, 62], [26, 166, 5, 62, "t"], [26, 167, 5, 62], [26, 176, 5, 62, "t"], [26, 177, 5, 62], [26, 181, 5, 62, "e"], [26, 182, 5, 62], [26, 186, 5, 62, "e"], [26, 187, 5, 62], [26, 188, 5, 62, "__esModule"], [26, 198, 5, 62], [26, 207, 5, 62, "e"], [26, 208, 5, 62], [26, 214, 5, 62, "o"], [26, 215, 5, 62], [26, 217, 5, 62, "i"], [26, 218, 5, 62], [26, 220, 5, 62, "f"], [26, 221, 5, 62], [26, 226, 5, 62, "__proto__"], [26, 235, 5, 62], [26, 243, 5, 62, "default"], [26, 250, 5, 62], [26, 252, 5, 62, "e"], [26, 253, 5, 62], [26, 270, 5, 62, "e"], [26, 271, 5, 62], [26, 294, 5, 62, "e"], [26, 295, 5, 62], [26, 320, 5, 62, "e"], [26, 321, 5, 62], [26, 330, 5, 62, "f"], [26, 331, 5, 62], [26, 337, 5, 62, "o"], [26, 338, 5, 62], [26, 341, 5, 62, "t"], [26, 342, 5, 62], [26, 345, 5, 62, "n"], [26, 346, 5, 62], [26, 349, 5, 62, "r"], [26, 350, 5, 62], [26, 358, 5, 62, "o"], [26, 359, 5, 62], [26, 360, 5, 62, "has"], [26, 363, 5, 62], [26, 364, 5, 62, "e"], [26, 365, 5, 62], [26, 375, 5, 62, "o"], [26, 376, 5, 62], [26, 377, 5, 62, "get"], [26, 380, 5, 62], [26, 381, 5, 62, "e"], [26, 382, 5, 62], [26, 385, 5, 62, "o"], [26, 386, 5, 62], [26, 387, 5, 62, "set"], [26, 390, 5, 62], [26, 391, 5, 62, "e"], [26, 392, 5, 62], [26, 394, 5, 62, "f"], [26, 395, 5, 62], [26, 411, 5, 62, "t"], [26, 412, 5, 62], [26, 416, 5, 62, "e"], [26, 417, 5, 62], [26, 433, 5, 62, "t"], [26, 434, 5, 62], [26, 441, 5, 62, "hasOwnProperty"], [26, 455, 5, 62], [26, 456, 5, 62, "call"], [26, 460, 5, 62], [26, 461, 5, 62, "e"], [26, 462, 5, 62], [26, 464, 5, 62, "t"], [26, 465, 5, 62], [26, 472, 5, 62, "i"], [26, 473, 5, 62], [26, 477, 5, 62, "o"], [26, 478, 5, 62], [26, 481, 5, 62, "Object"], [26, 487, 5, 62], [26, 488, 5, 62, "defineProperty"], [26, 502, 5, 62], [26, 507, 5, 62, "Object"], [26, 513, 5, 62], [26, 514, 5, 62, "getOwnPropertyDescriptor"], [26, 538, 5, 62], [26, 539, 5, 62, "e"], [26, 540, 5, 62], [26, 542, 5, 62, "t"], [26, 543, 5, 62], [26, 550, 5, 62, "i"], [26, 551, 5, 62], [26, 552, 5, 62, "get"], [26, 555, 5, 62], [26, 559, 5, 62, "i"], [26, 560, 5, 62], [26, 561, 5, 62, "set"], [26, 564, 5, 62], [26, 568, 5, 62, "o"], [26, 569, 5, 62], [26, 570, 5, 62, "f"], [26, 571, 5, 62], [26, 573, 5, 62, "t"], [26, 574, 5, 62], [26, 576, 5, 62, "i"], [26, 577, 5, 62], [26, 581, 5, 62, "f"], [26, 582, 5, 62], [26, 583, 5, 62, "t"], [26, 584, 5, 62], [26, 588, 5, 62, "e"], [26, 589, 5, 62], [26, 590, 5, 62, "t"], [26, 591, 5, 62], [26, 602, 5, 62, "f"], [26, 603, 5, 62], [26, 608, 5, 62, "e"], [26, 609, 5, 62], [26, 611, 5, 62, "t"], [26, 612, 5, 62], [27, 2, 1, 0], [29, 2, 6, 7], [29, 11, 6, 16, "getImageSize"], [29, 23, 6, 28, "getImageSize"], [29, 24, 6, 29, "videoWidth"], [29, 34, 6, 39], [29, 36, 6, 41, "videoHeight"], [29, 47, 6, 52], [29, 49, 6, 54, "scale"], [29, 54, 6, 59], [29, 56, 6, 61], [30, 4, 7, 4], [30, 10, 7, 10, "width"], [30, 15, 7, 15], [30, 18, 7, 18, "videoWidth"], [30, 28, 7, 28], [30, 31, 7, 31, "scale"], [30, 36, 7, 36], [31, 4, 8, 4], [31, 10, 8, 10, "ratio"], [31, 15, 8, 15], [31, 18, 8, 18, "videoWidth"], [31, 28, 8, 28], [31, 31, 8, 31, "width"], [31, 36, 8, 36], [32, 4, 9, 4], [32, 10, 9, 10, "height"], [32, 16, 9, 16], [32, 19, 9, 19, "videoHeight"], [32, 30, 9, 30], [32, 33, 9, 33, "ratio"], [32, 38, 9, 38], [33, 4, 10, 4], [33, 11, 10, 11], [34, 6, 11, 8, "width"], [34, 11, 11, 13], [35, 6, 12, 8, "height"], [36, 4, 13, 4], [36, 5, 13, 5], [37, 2, 14, 0], [38, 2, 15, 7], [38, 11, 15, 16, "toDataURL"], [38, 20, 15, 25, "toDataURL"], [38, 21, 15, 26, "canvas"], [38, 27, 15, 32], [38, 29, 15, 34, "imageType"], [38, 38, 15, 43], [38, 40, 15, 45, "quality"], [38, 47, 15, 52], [38, 49, 15, 54], [39, 4, 16, 4], [39, 10, 16, 10, "types"], [39, 15, 16, 15], [39, 18, 16, 18], [39, 19, 16, 19], [39, 24, 16, 24], [39, 26, 16, 26], [39, 31, 16, 31], [39, 32, 16, 32], [40, 4, 17, 4], [40, 8, 17, 4, "invariant"], [40, 26, 17, 13], [40, 28, 17, 14, "types"], [40, 33, 17, 19], [40, 34, 17, 20, "includes"], [40, 42, 17, 28], [40, 43, 17, 29, "imageType"], [40, 52, 17, 38], [40, 53, 17, 39], [40, 55, 17, 41], [40, 71, 17, 57, "imageType"], [40, 80, 17, 66], [40, 134, 17, 120, "types"], [40, 139, 17, 125], [40, 140, 17, 126, "join"], [40, 144, 17, 130], [40, 145, 17, 131], [40, 149, 17, 135], [40, 150, 17, 136], [40, 152, 17, 138], [40, 153, 17, 139], [41, 4, 18, 4], [41, 10, 18, 10, "format"], [41, 16, 18, 16], [41, 19, 18, 19, "ImageTypeFormat"], [41, 48, 18, 34], [41, 49, 18, 35, "imageType"], [41, 58, 18, 44], [41, 59, 18, 45], [42, 4, 19, 4], [42, 8, 19, 8, "imageType"], [42, 17, 19, 17], [42, 22, 19, 22], [42, 27, 19, 27], [42, 29, 19, 29], [43, 6, 20, 8], [43, 10, 20, 8, "invariant"], [43, 28, 20, 17], [43, 30, 20, 18, "quality"], [43, 37, 20, 25], [43, 41, 20, 29], [43, 42, 20, 30], [43, 46, 20, 34, "quality"], [43, 53, 20, 41], [43, 57, 20, 45], [43, 58, 20, 46], [43, 60, 20, 48], [43, 76, 20, 64, "quality"], [43, 83, 20, 71], [43, 144, 20, 132], [43, 145, 20, 133], [44, 6, 21, 8], [44, 13, 21, 15, "canvas"], [44, 19, 21, 21], [44, 20, 21, 22, "toDataURL"], [44, 29, 21, 31], [44, 30, 21, 32, "format"], [44, 36, 21, 38], [44, 38, 21, 40, "quality"], [44, 45, 21, 47], [44, 46, 21, 48], [45, 4, 22, 4], [45, 5, 22, 5], [45, 11, 23, 9], [46, 6, 24, 8], [46, 13, 24, 15, "canvas"], [46, 19, 24, 21], [46, 20, 24, 22, "toDataURL"], [46, 29, 24, 31], [46, 30, 24, 32, "format"], [46, 36, 24, 38], [46, 37, 24, 39], [47, 4, 25, 4], [48, 2, 26, 0], [49, 2, 27, 7], [49, 11, 27, 16, "hasValidConstraints"], [49, 30, 27, 35, "hasValidConstraints"], [49, 31, 27, 36, "preferredCameraType"], [49, 50, 27, 55], [49, 52, 27, 57, "width"], [49, 57, 27, 62], [49, 59, 27, 64, "height"], [49, 65, 27, 70], [49, 67, 27, 72], [50, 4, 28, 4], [50, 11, 28, 11, "preferredCameraType"], [50, 30, 28, 30], [50, 35, 28, 35, "undefined"], [50, 44, 28, 44], [50, 48, 28, 48, "width"], [50, 53, 28, 53], [50, 58, 28, 58, "undefined"], [50, 67, 28, 67], [50, 71, 28, 71, "height"], [50, 77, 28, 77], [50, 82, 28, 82, "undefined"], [50, 91, 28, 91], [51, 2, 29, 0], [52, 2, 30, 0], [52, 11, 30, 9, "ensureCameraPictureOptions"], [52, 37, 30, 35, "ensureCameraPictureOptions"], [52, 38, 30, 36, "config"], [52, 44, 30, 42], [52, 46, 30, 44], [53, 4, 31, 4], [53, 10, 31, 10, "captureOptions"], [53, 24, 31, 24], [53, 27, 31, 27], [54, 6, 32, 8, "scale"], [54, 11, 32, 13], [54, 13, 32, 15], [54, 14, 32, 16], [55, 6, 33, 8, "imageType"], [55, 15, 33, 17], [55, 17, 33, 19], [55, 22, 33, 24], [56, 6, 34, 8, "isImageMirror"], [56, 19, 34, 21], [56, 21, 34, 23], [57, 4, 35, 4], [57, 5, 35, 5], [58, 4, 36, 4], [58, 9, 36, 9], [58, 15, 36, 15, "key"], [58, 18, 36, 18], [58, 22, 36, 22, "config"], [58, 28, 36, 28], [58, 30, 36, 30], [59, 6, 37, 8], [59, 12, 37, 14, "prop"], [59, 16, 37, 18], [59, 19, 37, 21, "key"], [59, 22, 37, 24], [60, 6, 38, 8], [60, 10, 38, 12, "prop"], [60, 14, 38, 16], [60, 18, 38, 20, "config"], [60, 24, 38, 26], [60, 28, 38, 30, "config"], [60, 34, 38, 36], [60, 35, 38, 37, "prop"], [60, 39, 38, 41], [60, 40, 38, 42], [60, 45, 38, 47, "undefined"], [60, 54, 38, 56], [60, 58, 38, 60, "prop"], [60, 62, 38, 64], [60, 66, 38, 68, "captureOptions"], [60, 80, 38, 82], [60, 82, 38, 84], [61, 8, 39, 12, "captureOptions"], [61, 22, 39, 26], [61, 23, 39, 27, "prop"], [61, 27, 39, 31], [61, 28, 39, 32], [61, 31, 39, 35, "config"], [61, 37, 39, 41], [61, 38, 39, 42, "prop"], [61, 42, 39, 46], [61, 43, 39, 47], [62, 6, 40, 8], [63, 4, 41, 4], [64, 4, 42, 4], [64, 11, 42, 11, "captureOptions"], [64, 25, 42, 25], [65, 2, 43, 0], [66, 2, 44, 0], [66, 8, 44, 6, "DEFAULT_QUALITY"], [66, 23, 44, 21], [66, 26, 44, 24], [66, 30, 44, 28], [67, 2, 45, 7], [67, 11, 45, 16, "captureImageData"], [67, 27, 45, 32, "captureImageData"], [67, 28, 45, 33, "video"], [67, 33, 45, 38], [67, 35, 45, 40, "pictureOptions"], [67, 49, 45, 54], [67, 52, 45, 57], [67, 53, 45, 58], [67, 54, 45, 59], [67, 56, 45, 61], [68, 4, 46, 4], [68, 8, 46, 8], [68, 9, 46, 9, "video"], [68, 14, 46, 14], [68, 18, 46, 18, "video"], [68, 23, 46, 23], [68, 24, 46, 24, "readyState"], [68, 34, 46, 34], [68, 39, 46, 39, "video"], [68, 44, 46, 44], [68, 45, 46, 45, "HAVE_ENOUGH_DATA"], [68, 61, 46, 61], [68, 63, 46, 63], [69, 6, 47, 8], [69, 13, 47, 15], [69, 17, 47, 19], [70, 4, 48, 4], [71, 4, 49, 4], [71, 10, 49, 10, "canvas"], [71, 16, 49, 16], [71, 19, 49, 19, "captureImageContext"], [71, 38, 49, 38], [71, 39, 49, 39, "video"], [71, 44, 49, 44], [71, 46, 49, 46, "pictureOptions"], [71, 60, 49, 60], [71, 61, 49, 61], [72, 4, 50, 4], [72, 10, 50, 10, "context"], [72, 17, 50, 17], [72, 20, 50, 20, "canvas"], [72, 26, 50, 26], [72, 27, 50, 27, "getContext"], [72, 37, 50, 37], [72, 38, 50, 38], [72, 42, 50, 42], [72, 44, 50, 44], [73, 6, 50, 46, "alpha"], [73, 11, 50, 51], [73, 13, 50, 53], [74, 4, 50, 59], [74, 5, 50, 60], [74, 6, 50, 61], [75, 4, 51, 4], [75, 8, 51, 8], [75, 9, 51, 9, "context"], [75, 16, 51, 16], [75, 20, 51, 20], [75, 21, 51, 21, "canvas"], [75, 27, 51, 27], [75, 28, 51, 28, "width"], [75, 33, 51, 33], [75, 37, 51, 37], [75, 38, 51, 38, "canvas"], [75, 44, 51, 44], [75, 45, 51, 45, "height"], [75, 51, 51, 51], [75, 53, 51, 53], [76, 6, 52, 8], [76, 13, 52, 15], [76, 17, 52, 19], [77, 4, 53, 4], [78, 4, 54, 4], [78, 10, 54, 10, "imageData"], [78, 19, 54, 19], [78, 22, 54, 22, "context"], [78, 29, 54, 29], [78, 30, 54, 30, "getImageData"], [78, 42, 54, 42], [78, 43, 54, 43], [78, 44, 54, 44], [78, 46, 54, 46], [78, 47, 54, 47], [78, 49, 54, 49, "canvas"], [78, 55, 54, 55], [78, 56, 54, 56, "width"], [78, 61, 54, 61], [78, 63, 54, 63, "canvas"], [78, 69, 54, 69], [78, 70, 54, 70, "height"], [78, 76, 54, 76], [78, 77, 54, 77], [79, 4, 55, 4], [79, 11, 55, 11, "imageData"], [79, 20, 55, 20], [80, 2, 56, 0], [81, 2, 57, 7], [81, 11, 57, 16, "captureImageContext"], [81, 30, 57, 35, "captureImageContext"], [81, 31, 57, 36, "video"], [81, 36, 57, 41], [81, 38, 57, 43], [82, 4, 57, 45, "scale"], [82, 9, 57, 50], [82, 12, 57, 53], [82, 13, 57, 54], [83, 4, 57, 56, "isImageMirror"], [83, 17, 57, 69], [83, 20, 57, 72], [84, 2, 57, 78], [84, 3, 57, 79], [84, 5, 57, 81], [85, 4, 58, 4], [85, 10, 58, 10], [86, 6, 58, 12, "videoWidth"], [86, 16, 58, 22], [87, 6, 58, 24, "videoHeight"], [88, 4, 58, 36], [88, 5, 58, 37], [88, 8, 58, 40, "video"], [88, 13, 58, 45], [89, 4, 59, 4], [89, 10, 59, 10], [90, 6, 59, 12, "width"], [90, 11, 59, 17], [91, 6, 59, 19, "height"], [92, 4, 59, 26], [92, 5, 59, 27], [92, 8, 59, 30, "getImageSize"], [92, 20, 59, 42], [92, 21, 59, 43, "videoWidth"], [92, 31, 59, 53], [92, 33, 59, 55, "videoHeight"], [92, 44, 59, 66], [92, 46, 59, 68, "scale"], [92, 51, 59, 73], [92, 52, 59, 74], [93, 4, 60, 4], [94, 4, 61, 4], [94, 10, 61, 10, "canvas"], [94, 16, 61, 16], [94, 19, 61, 19, "document"], [94, 27, 61, 27], [94, 28, 61, 28, "createElement"], [94, 41, 61, 41], [94, 42, 61, 42], [94, 50, 61, 50], [94, 51, 61, 51], [95, 4, 62, 4, "canvas"], [95, 10, 62, 10], [95, 11, 62, 11, "width"], [95, 16, 62, 16], [95, 19, 62, 19, "width"], [95, 24, 62, 24], [96, 4, 63, 4, "canvas"], [96, 10, 63, 10], [96, 11, 63, 11, "height"], [96, 17, 63, 17], [96, 20, 63, 20, "height"], [96, 26, 63, 26], [97, 4, 64, 4], [97, 10, 64, 10, "context"], [97, 17, 64, 17], [97, 20, 64, 20, "canvas"], [97, 26, 64, 26], [97, 27, 64, 27, "getContext"], [97, 37, 64, 37], [97, 38, 64, 38], [97, 42, 64, 42], [97, 44, 64, 44], [98, 6, 64, 46, "alpha"], [98, 11, 64, 51], [98, 13, 64, 53], [99, 4, 64, 59], [99, 5, 64, 60], [99, 6, 64, 61], [100, 4, 65, 4], [100, 8, 65, 8], [100, 9, 65, 9, "context"], [100, 16, 65, 16], [100, 18, 65, 18], [101, 6, 66, 8], [102, 6, 67, 8], [102, 12, 67, 14], [102, 16, 67, 18, "Error"], [102, 21, 67, 23], [102, 22, 67, 24], [102, 46, 67, 48], [102, 47, 67, 49], [103, 4, 68, 4], [104, 4, 69, 4], [105, 4, 70, 4], [106, 4, 71, 4], [107, 4, 72, 4], [107, 8, 72, 8, "isImageMirror"], [107, 21, 72, 21], [107, 23, 72, 23], [108, 6, 73, 8, "context"], [108, 13, 73, 15], [108, 14, 73, 16, "setTransform"], [108, 26, 73, 28], [108, 27, 73, 29], [108, 28, 73, 30], [108, 29, 73, 31], [108, 31, 73, 33], [108, 32, 73, 34], [108, 34, 73, 36], [108, 35, 73, 37], [108, 37, 73, 39], [108, 38, 73, 40], [108, 40, 73, 42, "canvas"], [108, 46, 73, 48], [108, 47, 73, 49, "width"], [108, 52, 73, 54], [108, 54, 73, 56], [108, 55, 73, 57], [108, 56, 73, 58], [109, 4, 74, 4], [110, 4, 75, 4, "context"], [110, 11, 75, 11], [110, 12, 75, 12, "drawImage"], [110, 21, 75, 21], [110, 22, 75, 22, "video"], [110, 27, 75, 27], [110, 29, 75, 29], [110, 30, 75, 30], [110, 32, 75, 32], [110, 33, 75, 33], [110, 35, 75, 35, "width"], [110, 40, 75, 40], [110, 42, 75, 42, "height"], [110, 48, 75, 48], [110, 49, 75, 49], [111, 4, 76, 4], [111, 11, 76, 11, "canvas"], [111, 17, 76, 17], [112, 2, 77, 0], [113, 2, 78, 7], [113, 11, 78, 16, "captureImage"], [113, 23, 78, 28, "captureImage"], [113, 24, 78, 29, "video"], [113, 29, 78, 34], [113, 31, 78, 36, "pictureOptions"], [113, 45, 78, 50], [113, 47, 78, 52], [114, 4, 79, 4], [114, 10, 79, 10, "config"], [114, 16, 79, 16], [114, 19, 79, 19, "ensureCameraPictureOptions"], [114, 45, 79, 45], [114, 46, 79, 46, "pictureOptions"], [114, 60, 79, 60], [114, 61, 79, 61], [115, 4, 80, 4], [115, 10, 80, 10, "canvas"], [115, 16, 80, 16], [115, 19, 80, 19, "captureImageContext"], [115, 38, 80, 38], [115, 39, 80, 39, "video"], [115, 44, 80, 44], [115, 46, 80, 46, "config"], [115, 52, 80, 52], [115, 53, 80, 53], [116, 4, 81, 4], [116, 10, 81, 10], [117, 6, 81, 12, "imageType"], [117, 15, 81, 21], [118, 6, 81, 23, "quality"], [118, 13, 81, 30], [118, 16, 81, 33, "DEFAULT_QUALITY"], [119, 4, 81, 49], [119, 5, 81, 50], [119, 8, 81, 53, "config"], [119, 14, 81, 59], [120, 4, 82, 4], [120, 11, 82, 11, "toDataURL"], [120, 20, 82, 20], [120, 21, 82, 21, "canvas"], [120, 27, 82, 27], [120, 29, 82, 29, "imageType"], [120, 38, 82, 38], [120, 40, 82, 40, "quality"], [120, 47, 82, 47], [120, 48, 82, 48], [121, 2, 83, 0], [122, 2, 84, 0], [122, 11, 84, 9, "getSupportedConstraints"], [122, 34, 84, 32, "getSupportedConstraints"], [122, 35, 84, 32], [122, 37, 84, 35], [123, 4, 85, 4], [123, 8, 85, 8, "navigator"], [123, 17, 85, 17], [123, 18, 85, 18, "mediaDevices"], [123, 30, 85, 30], [123, 34, 85, 34, "navigator"], [123, 43, 85, 43], [123, 44, 85, 44, "mediaDevices"], [123, 56, 85, 56], [123, 57, 85, 57, "getSupportedConstraints"], [123, 80, 85, 80], [123, 82, 85, 82], [124, 6, 86, 8], [124, 13, 86, 15, "navigator"], [124, 22, 86, 24], [124, 23, 86, 25, "mediaDevices"], [124, 35, 86, 37], [124, 36, 86, 38, "getSupportedConstraints"], [124, 59, 86, 61], [124, 60, 86, 62], [124, 61, 86, 63], [125, 4, 87, 4], [126, 4, 88, 4], [126, 11, 88, 11], [126, 15, 88, 15], [127, 2, 89, 0], [128, 2, 90, 7], [128, 11, 90, 16, "getIdealConstraints"], [128, 30, 90, 35, "getIdealConstraints"], [128, 31, 90, 36, "preferredCameraType"], [128, 50, 90, 55], [128, 52, 90, 57, "width"], [128, 57, 90, 62], [128, 59, 90, 64, "height"], [128, 65, 90, 70], [128, 67, 90, 72], [129, 4, 91, 4], [129, 10, 91, 10, "preferredConstraints"], [129, 30, 91, 30], [129, 33, 91, 33], [130, 6, 92, 8, "audio"], [130, 11, 92, 13], [130, 13, 92, 15], [130, 18, 92, 20], [131, 6, 93, 8, "video"], [131, 11, 93, 13], [131, 13, 93, 15], [131, 14, 93, 16], [132, 4, 94, 4], [132, 5, 94, 5], [133, 4, 95, 4], [133, 8, 95, 8, "hasValidConstraints"], [133, 27, 95, 27], [133, 28, 95, 28, "preferredCameraType"], [133, 47, 95, 47], [133, 49, 95, 49, "width"], [133, 54, 95, 54], [133, 56, 95, 56, "height"], [133, 62, 95, 62], [133, 63, 95, 63], [133, 65, 95, 65], [134, 6, 96, 8], [134, 13, 96, 15, "MinimumConstraints"], [134, 45, 96, 33], [135, 4, 97, 4], [136, 4, 98, 4], [136, 10, 98, 10, "supports"], [136, 18, 98, 18], [136, 21, 98, 21, "getSupportedConstraints"], [136, 44, 98, 44], [136, 45, 98, 45], [136, 46, 98, 46], [137, 4, 99, 4], [138, 4, 100, 4], [138, 8, 100, 8], [138, 9, 100, 9, "supports"], [138, 17, 100, 17], [138, 21, 100, 21], [138, 22, 100, 22, "supports"], [138, 30, 100, 30], [138, 31, 100, 31, "facingMode"], [138, 41, 100, 41], [138, 45, 100, 45], [138, 46, 100, 46, "supports"], [138, 54, 100, 54], [138, 55, 100, 55, "width"], [138, 60, 100, 60], [138, 64, 100, 64], [138, 65, 100, 65, "supports"], [138, 73, 100, 73], [138, 74, 100, 74, "height"], [138, 80, 100, 80], [138, 82, 100, 82], [139, 6, 101, 8], [139, 13, 101, 15, "MinimumConstraints"], [139, 45, 101, 33], [140, 4, 102, 4], [141, 4, 103, 4], [141, 10, 103, 10, "types"], [141, 15, 103, 15], [141, 18, 103, 18], [141, 19, 103, 19], [141, 26, 103, 26], [141, 28, 103, 28], [141, 34, 103, 34], [141, 35, 103, 35], [142, 4, 104, 4], [142, 8, 104, 8, "preferredCameraType"], [142, 27, 104, 27], [142, 31, 104, 31, "types"], [142, 36, 104, 36], [142, 37, 104, 37, "includes"], [142, 45, 104, 45], [142, 46, 104, 46, "preferredCameraType"], [142, 65, 104, 65], [142, 66, 104, 66], [142, 68, 104, 68], [143, 6, 105, 8], [143, 12, 105, 14, "facingMode"], [143, 22, 105, 24], [143, 25, 105, 27, "CameraTypeToFacingMode"], [143, 61, 105, 49], [143, 62, 105, 50, "preferredCameraType"], [143, 81, 105, 69], [143, 82, 105, 70], [144, 6, 106, 8], [144, 10, 106, 12, "isWebKit"], [144, 18, 106, 20], [144, 19, 106, 21], [144, 20, 106, 22], [144, 22, 106, 24], [145, 8, 107, 12], [145, 14, 107, 18, "key"], [145, 17, 107, 21], [145, 20, 107, 24, "facingMode"], [145, 30, 107, 34], [145, 35, 107, 39], [145, 41, 107, 45], [145, 44, 107, 48], [145, 51, 107, 55], [145, 54, 107, 58], [145, 61, 107, 65], [146, 8, 108, 12, "preferredConstraints"], [146, 28, 108, 32], [146, 29, 108, 33, "video"], [146, 34, 108, 38], [146, 35, 108, 39, "facingMode"], [146, 45, 108, 49], [146, 48, 108, 52], [147, 10, 109, 16], [147, 11, 109, 17, "key"], [147, 14, 109, 20], [147, 17, 109, 23, "facingMode"], [148, 8, 110, 12], [148, 9, 110, 13], [149, 6, 111, 8], [149, 7, 111, 9], [149, 13, 112, 13], [150, 8, 113, 12, "preferredConstraints"], [150, 28, 113, 32], [150, 29, 113, 33, "video"], [150, 34, 113, 38], [150, 35, 113, 39, "facingMode"], [150, 45, 113, 49], [150, 48, 113, 52], [151, 10, 114, 16, "ideal"], [151, 15, 114, 21], [151, 17, 114, 23, "CameraTypeToFacingMode"], [151, 53, 114, 45], [151, 54, 114, 46, "preferredCameraType"], [151, 73, 114, 65], [152, 8, 115, 12], [152, 9, 115, 13], [153, 6, 116, 8], [154, 4, 117, 4], [155, 4, 118, 4], [155, 8, 118, 8, "isMediaTrackConstraints"], [155, 31, 118, 31], [155, 32, 118, 32, "preferredConstraints"], [155, 52, 118, 52], [155, 53, 118, 53, "video"], [155, 58, 118, 58], [155, 59, 118, 59], [155, 61, 118, 61], [156, 6, 119, 8, "preferredConstraints"], [156, 26, 119, 28], [156, 27, 119, 29, "video"], [156, 32, 119, 34], [156, 33, 119, 35, "width"], [156, 38, 119, 40], [156, 41, 119, 43, "width"], [156, 46, 119, 48], [157, 6, 120, 8, "preferredConstraints"], [157, 26, 120, 28], [157, 27, 120, 29, "video"], [157, 32, 120, 34], [157, 33, 120, 35, "height"], [157, 39, 120, 41], [157, 42, 120, 44, "height"], [157, 48, 120, 50], [158, 4, 121, 4], [159, 4, 122, 4], [159, 11, 122, 11, "preferredConstraints"], [159, 31, 122, 31], [160, 2, 123, 0], [161, 2, 124, 0], [161, 11, 124, 9, "isMediaTrackConstraints"], [161, 34, 124, 32, "isMediaTrackConstraints"], [161, 35, 124, 33, "input"], [161, 40, 124, 38], [161, 42, 124, 40], [162, 4, 125, 4], [162, 11, 125, 11, "input"], [162, 16, 125, 16], [162, 20, 125, 20], [162, 27, 125, 27, "input"], [162, 32, 125, 32], [162, 33, 125, 33, "video"], [162, 38, 125, 38], [162, 43, 125, 43], [162, 52, 125, 52], [163, 2, 126, 0], [164, 2, 127, 0], [165, 0, 128, 0], [166, 0, 129, 0], [167, 0, 130, 0], [168, 0, 131, 0], [169, 0, 132, 0], [170, 0, 133, 0], [171, 2, 134, 7], [171, 17, 134, 22, "getPreferredStreamDevice"], [171, 41, 134, 46, "getPreferredStreamDevice"], [171, 42, 134, 47, "preferredCameraType"], [171, 61, 134, 66], [171, 63, 134, 68, "preferredWidth"], [171, 77, 134, 82], [171, 79, 134, 84, "preferredHeight"], [171, 94, 134, 99], [171, 96, 134, 101], [172, 4, 135, 4], [172, 8, 135, 8], [173, 6, 136, 8], [173, 13, 136, 15], [173, 19, 136, 21, "getStreamDevice"], [173, 34, 136, 36], [173, 35, 136, 37, "preferredCameraType"], [173, 54, 136, 56], [173, 56, 136, 58, "preferredWidth"], [173, 70, 136, 72], [173, 72, 136, 74, "preferredHeight"], [173, 87, 136, 89], [173, 88, 136, 90], [174, 4, 137, 4], [174, 5, 137, 5], [174, 6, 138, 4], [174, 13, 138, 11, "error"], [174, 18, 138, 16], [174, 20, 138, 18], [175, 6, 139, 8], [176, 6, 140, 8], [177, 6, 141, 8], [177, 10, 141, 12, "error"], [177, 15, 141, 17], [177, 27, 141, 29, "OverconstrainedError"], [177, 47, 141, 49], [177, 51, 141, 53, "error"], [177, 56, 141, 58], [177, 57, 141, 59, "constraint"], [177, 67, 141, 69], [177, 72, 141, 74], [177, 84, 141, 86], [177, 86, 141, 88], [178, 8, 142, 12], [178, 14, 142, 18, "nextCameraType"], [178, 28, 142, 32], [178, 31, 142, 35, "preferredCameraType"], [178, 50, 142, 54], [178, 55, 142, 59], [178, 61, 142, 65], [178, 64, 142, 68], [178, 71, 142, 75], [178, 74, 142, 78], [178, 80, 142, 84], [179, 8, 143, 12], [179, 15, 143, 19], [179, 21, 143, 25, "getStreamDevice"], [179, 36, 143, 40], [179, 37, 143, 41, "nextCameraType"], [179, 51, 143, 55], [179, 53, 143, 57, "preferredWidth"], [179, 67, 143, 71], [179, 69, 143, 73, "preferredHeight"], [179, 84, 143, 88], [179, 85, 143, 89], [180, 6, 144, 8], [181, 6, 145, 8], [181, 12, 145, 14, "error"], [181, 17, 145, 19], [182, 4, 146, 4], [183, 2, 147, 0], [184, 2, 148, 7], [184, 17, 148, 22, "getStreamDevice"], [184, 32, 148, 37, "getStreamDevice"], [184, 33, 148, 38, "preferredCameraType"], [184, 52, 148, 57], [184, 54, 148, 59, "preferredWidth"], [184, 68, 148, 73], [184, 70, 148, 75, "preferredHeight"], [184, 85, 148, 90], [184, 87, 148, 92], [185, 4, 149, 4], [185, 10, 149, 10, "constraints"], [185, 21, 149, 21], [185, 24, 149, 24, "getIdealConstraints"], [185, 43, 149, 43], [185, 44, 149, 44, "preferredCameraType"], [185, 63, 149, 63], [185, 65, 149, 65, "preferredWidth"], [185, 79, 149, 79], [185, 81, 149, 81, "preferredHeight"], [185, 96, 149, 96], [185, 97, 149, 97], [186, 4, 150, 4], [186, 10, 150, 10, "stream"], [186, 16, 150, 16], [186, 19, 150, 19], [186, 25, 150, 25], [186, 29, 150, 25, "requestUserMediaAsync"], [186, 71, 150, 46], [186, 73, 150, 47, "constraints"], [186, 84, 150, 58], [186, 85, 150, 59], [187, 4, 151, 4], [187, 11, 151, 11, "stream"], [187, 17, 151, 17], [188, 2, 152, 0], [189, 2, 153, 7], [189, 11, 153, 16, "isWebKit"], [189, 19, 153, 24, "isWebKit"], [189, 20, 153, 24], [189, 22, 153, 27], [190, 4, 154, 4], [190, 11, 154, 11], [190, 19, 154, 19], [190, 20, 154, 20, "test"], [190, 24, 154, 24], [190, 25, 154, 25, "navigator"], [190, 34, 154, 34], [190, 35, 154, 35, "userAgent"], [190, 44, 154, 44], [190, 45, 154, 45], [190, 49, 154, 49], [190, 50, 154, 50], [190, 55, 154, 55], [190, 56, 154, 56, "test"], [190, 60, 154, 60], [190, 61, 154, 61, "navigator"], [190, 70, 154, 70], [190, 71, 154, 71, "userAgent"], [190, 80, 154, 80], [190, 81, 154, 81], [191, 2, 155, 0], [192, 2, 156, 7], [192, 11, 156, 16, "compareStreams"], [192, 25, 156, 30, "compareStreams"], [192, 26, 156, 31, "a"], [192, 27, 156, 32], [192, 29, 156, 34, "b"], [192, 30, 156, 35], [192, 32, 156, 37], [193, 4, 157, 4], [193, 8, 157, 8], [193, 9, 157, 9, "a"], [193, 10, 157, 10], [193, 14, 157, 14], [193, 15, 157, 15, "b"], [193, 16, 157, 16], [193, 18, 157, 18], [194, 6, 158, 8], [194, 13, 158, 15], [194, 18, 158, 20], [195, 4, 159, 4], [196, 4, 160, 4], [196, 10, 160, 10, "settingsA"], [196, 19, 160, 19], [196, 22, 160, 22, "a"], [196, 23, 160, 23], [196, 24, 160, 24, "getTracks"], [196, 33, 160, 33], [196, 34, 160, 34], [196, 35, 160, 35], [196, 36, 160, 36], [196, 37, 160, 37], [196, 38, 160, 38], [196, 39, 160, 39, "getSettings"], [196, 50, 160, 50], [196, 51, 160, 51], [196, 52, 160, 52], [197, 4, 161, 4], [197, 10, 161, 10, "settingsB"], [197, 19, 161, 19], [197, 22, 161, 22, "b"], [197, 23, 161, 23], [197, 24, 161, 24, "getTracks"], [197, 33, 161, 33], [197, 34, 161, 34], [197, 35, 161, 35], [197, 36, 161, 36], [197, 37, 161, 37], [197, 38, 161, 38], [197, 39, 161, 39, "getSettings"], [197, 50, 161, 50], [197, 51, 161, 51], [197, 52, 161, 52], [198, 4, 162, 4], [198, 11, 162, 11, "settingsA"], [198, 20, 162, 20], [198, 21, 162, 21, "deviceId"], [198, 29, 162, 29], [198, 34, 162, 34, "settingsB"], [198, 43, 162, 43], [198, 44, 162, 44, "deviceId"], [198, 52, 162, 52], [199, 2, 163, 0], [200, 2, 164, 7], [200, 11, 164, 16, "capture"], [200, 18, 164, 23, "capture"], [200, 19, 164, 24, "video"], [200, 24, 164, 29], [200, 26, 164, 31, "settings"], [200, 34, 164, 39], [200, 36, 164, 41, "config"], [200, 42, 164, 47], [200, 44, 164, 49], [201, 4, 165, 4], [201, 10, 165, 10, "base64"], [201, 16, 165, 16], [201, 19, 165, 19, "captureImage"], [201, 31, 165, 31], [201, 32, 165, 32, "video"], [201, 37, 165, 37], [201, 39, 165, 39, "config"], [201, 45, 165, 45], [201, 46, 165, 46], [202, 4, 166, 4], [202, 10, 166, 10, "capturedPicture"], [202, 25, 166, 25], [202, 28, 166, 28], [203, 6, 167, 8, "uri"], [203, 9, 167, 11], [203, 11, 167, 13, "base64"], [203, 17, 167, 19], [204, 6, 168, 8, "base64"], [204, 12, 168, 14], [205, 6, 169, 8, "width"], [205, 11, 169, 13], [205, 13, 169, 15], [205, 14, 169, 16], [206, 6, 170, 8, "height"], [206, 12, 170, 14], [206, 14, 170, 16], [206, 15, 170, 17], [207, 6, 171, 8, "format"], [207, 12, 171, 14], [207, 14, 171, 16, "config"], [207, 20, 171, 22], [207, 21, 171, 23, "imageType"], [207, 30, 171, 32], [207, 34, 171, 36], [208, 4, 172, 4], [208, 5, 172, 5], [209, 4, 173, 4], [209, 8, 173, 8, "settings"], [209, 16, 173, 16], [209, 18, 173, 18], [210, 6, 174, 8], [210, 12, 174, 14], [211, 8, 174, 16, "width"], [211, 13, 174, 21], [211, 16, 174, 24], [211, 17, 174, 25], [212, 8, 174, 27, "height"], [212, 14, 174, 33], [212, 17, 174, 36], [213, 6, 174, 38], [213, 7, 174, 39], [213, 10, 174, 42, "settings"], [213, 18, 174, 50], [214, 6, 175, 8, "capturedPicture"], [214, 21, 175, 23], [214, 22, 175, 24, "width"], [214, 27, 175, 29], [214, 30, 175, 32, "width"], [214, 35, 175, 37], [215, 6, 176, 8, "capturedPicture"], [215, 21, 176, 23], [215, 22, 176, 24, "height"], [215, 28, 176, 30], [215, 31, 176, 33, "height"], [215, 37, 176, 39], [216, 6, 177, 8, "capturedPicture"], [216, 21, 177, 23], [216, 22, 177, 24, "exif"], [216, 26, 177, 28], [216, 29, 177, 31, "settings"], [216, 37, 177, 39], [217, 4, 178, 4], [218, 4, 179, 4], [218, 8, 179, 8, "config"], [218, 14, 179, 14], [218, 15, 179, 15, "onPictureSaved"], [218, 29, 179, 29], [218, 31, 179, 31], [219, 6, 180, 8, "config"], [219, 12, 180, 14], [219, 13, 180, 15, "onPictureSaved"], [219, 27, 180, 29], [219, 28, 180, 30, "capturedPicture"], [219, 43, 180, 45], [219, 44, 180, 46], [220, 4, 181, 4], [221, 4, 182, 4], [221, 11, 182, 11, "capturedPicture"], [221, 26, 182, 26], [222, 2, 183, 0], [223, 2, 184, 7], [223, 17, 184, 22, "syncTrackCapabilities"], [223, 38, 184, 43, "syncTrackCapabilities"], [223, 39, 184, 44, "cameraType"], [223, 49, 184, 54], [223, 51, 184, 56, "stream"], [223, 57, 184, 62], [223, 59, 184, 64, "settings"], [223, 67, 184, 72], [223, 70, 184, 75], [223, 71, 184, 76], [223, 72, 184, 77], [223, 74, 184, 79], [224, 4, 185, 4], [224, 8, 185, 8, "stream"], [224, 14, 185, 14], [224, 16, 185, 16, "getVideoTracks"], [224, 30, 185, 30], [224, 32, 185, 32], [225, 6, 186, 8], [225, 12, 186, 14, "Promise"], [225, 19, 186, 21], [225, 20, 186, 22, "all"], [225, 23, 186, 25], [225, 24, 186, 26, "stream"], [225, 30, 186, 32], [225, 31, 186, 33, "getVideoTracks"], [225, 45, 186, 47], [225, 46, 186, 48], [225, 47, 186, 49], [225, 48, 186, 50, "map"], [225, 51, 186, 53], [225, 52, 186, 55, "track"], [225, 57, 186, 60], [225, 61, 186, 65, "onCapabilitiesReady"], [225, 80, 186, 84], [225, 81, 186, 85, "cameraType"], [225, 91, 186, 95], [225, 93, 186, 97, "track"], [225, 98, 186, 102], [225, 100, 186, 104, "settings"], [225, 108, 186, 112], [225, 109, 186, 113], [225, 110, 186, 114], [225, 111, 186, 115], [226, 4, 187, 4], [227, 2, 188, 0], [228, 2, 189, 0], [229, 2, 190, 0], [229, 17, 190, 15, "onCapabilitiesReady"], [229, 36, 190, 34, "onCapabilitiesReady"], [229, 37, 190, 35, "cameraType"], [229, 47, 190, 45], [229, 49, 190, 47, "track"], [229, 54, 190, 52], [229, 56, 190, 54, "settings"], [229, 64, 190, 62], [229, 67, 190, 65], [229, 68, 190, 66], [229, 69, 190, 67], [229, 71, 190, 69], [230, 4, 191, 4], [230, 8, 191, 8], [230, 15, 191, 15, "track"], [230, 20, 191, 20], [230, 21, 191, 21, "getCapabilities"], [230, 36, 191, 36], [230, 41, 191, 41], [230, 51, 191, 51], [230, 53, 191, 53], [231, 6, 192, 8], [232, 4, 193, 4], [233, 4, 194, 4], [233, 10, 194, 10, "capabilities"], [233, 22, 194, 22], [233, 25, 194, 25, "track"], [233, 30, 194, 30], [233, 31, 194, 31, "getCapabilities"], [233, 46, 194, 46], [233, 47, 194, 47], [233, 48, 194, 48], [234, 4, 195, 4], [235, 4, 196, 4], [235, 10, 196, 10, "constraints"], [235, 21, 196, 21], [235, 24, 196, 24], [235, 25, 196, 25], [235, 26, 196, 26], [236, 4, 197, 4], [237, 4, 198, 4], [237, 10, 198, 10, "<PERSON><PERSON><PERSON><PERSON>"], [237, 23, 198, 23], [237, 26, 198, 26], [237, 27, 199, 8], [237, 49, 199, 30], [237, 51, 200, 8], [237, 69, 200, 26], [237, 71, 201, 8], [237, 76, 201, 13], [237, 78, 202, 8], [237, 90, 202, 20], [237, 92, 203, 8], [237, 102, 203, 18], [237, 104, 204, 8], [237, 116, 204, 20], [237, 118, 205, 8], [237, 129, 205, 19], [237, 131, 206, 8], [237, 146, 206, 23], [237, 148, 207, 8], [237, 154, 207, 14], [237, 155, 208, 5], [238, 4, 209, 4], [238, 9, 209, 9], [238, 15, 209, 15, "property"], [238, 23, 209, 23], [238, 27, 209, 27, "<PERSON><PERSON><PERSON><PERSON>"], [238, 40, 209, 40], [238, 42, 209, 42], [239, 6, 210, 8], [239, 10, 210, 12, "capabilities"], [239, 22, 210, 24], [239, 23, 210, 25, "property"], [239, 31, 210, 33], [239, 32, 210, 34], [239, 34, 210, 36], [240, 8, 211, 12, "constraints"], [240, 19, 211, 23], [240, 20, 211, 24, "property"], [240, 28, 211, 32], [240, 29, 211, 33], [240, 32, 211, 36, "convertNormalizedSetting"], [240, 56, 211, 60], [240, 57, 211, 61, "capabilities"], [240, 69, 211, 73], [240, 70, 211, 74, "property"], [240, 78, 211, 82], [240, 79, 211, 83], [240, 81, 211, 85, "settings"], [240, 89, 211, 93], [240, 90, 211, 94, "property"], [240, 98, 211, 102], [240, 99, 211, 103], [240, 100, 211, 104], [241, 6, 212, 8], [242, 4, 213, 4], [243, 4, 214, 4], [243, 13, 214, 13, "validatedInternalConstrainedValue"], [243, 46, 214, 46, "validatedInternalConstrainedValue"], [243, 47, 214, 47, "constraintKey"], [243, 60, 214, 60], [243, 62, 214, 62, "<PERSON><PERSON><PERSON>"], [243, 73, 214, 73], [243, 75, 214, 75, "converter"], [243, 84, 214, 84], [243, 86, 214, 86], [244, 6, 215, 8], [244, 12, 215, 14, "convertedSetting"], [244, 28, 215, 30], [244, 31, 215, 33, "converter"], [244, 40, 215, 42], [244, 41, 215, 43, "settings"], [244, 49, 215, 51], [244, 50, 215, 52, "<PERSON><PERSON><PERSON>"], [244, 61, 215, 63], [244, 62, 215, 64], [244, 63, 215, 65], [245, 6, 216, 8], [245, 13, 216, 15, "validatedConstrainedValue"], [245, 38, 216, 40], [245, 39, 216, 41], [246, 8, 217, 12, "constraintKey"], [246, 21, 217, 25], [247, 8, 218, 12, "<PERSON><PERSON><PERSON>"], [247, 19, 218, 23], [248, 8, 219, 12, "convertedSetting"], [248, 24, 219, 28], [249, 8, 220, 12, "capabilities"], [249, 20, 220, 24], [250, 8, 221, 12, "settings"], [250, 16, 221, 20], [251, 8, 222, 12, "cameraType"], [252, 6, 223, 8], [252, 7, 223, 9], [252, 8, 223, 10], [253, 4, 224, 4], [254, 4, 225, 4], [254, 8, 225, 8, "capabilities"], [254, 20, 225, 20], [254, 21, 225, 21, "focusMode"], [254, 30, 225, 30], [254, 34, 225, 34, "settings"], [254, 42, 225, 42], [254, 43, 225, 43, "autoFocus"], [254, 52, 225, 52], [254, 57, 225, 57, "undefined"], [254, 66, 225, 66], [254, 68, 225, 68], [255, 6, 226, 8, "constraints"], [255, 17, 226, 19], [255, 18, 226, 20, "focusMode"], [255, 27, 226, 29], [255, 30, 226, 32, "validatedInternalConstrainedValue"], [255, 63, 226, 65], [255, 64, 226, 66], [255, 75, 226, 77], [255, 77, 226, 79], [255, 88, 226, 90], [255, 90, 226, 92, "CapabilityUtils"], [255, 105, 226, 107], [255, 106, 226, 108, "convertAutoFocusJSONToNative"], [255, 134, 226, 136], [255, 135, 226, 137], [256, 4, 227, 4], [257, 4, 228, 4], [257, 8, 228, 8, "capabilities"], [257, 20, 228, 20], [257, 21, 228, 21, "torch"], [257, 26, 228, 26], [257, 30, 228, 30, "settings"], [257, 38, 228, 38], [257, 39, 228, 39, "flashMode"], [257, 48, 228, 48], [257, 53, 228, 53, "undefined"], [257, 62, 228, 62], [257, 64, 228, 64], [258, 6, 229, 8, "constraints"], [258, 17, 229, 19], [258, 18, 229, 20, "torch"], [258, 23, 229, 25], [258, 26, 229, 28, "validatedInternalConstrainedValue"], [258, 59, 229, 61], [258, 60, 229, 62], [258, 67, 229, 69], [258, 69, 229, 71], [258, 80, 229, 82], [258, 82, 229, 84, "CapabilityUtils"], [258, 97, 229, 99], [258, 98, 229, 100, "convertFlashModeJSONToNative"], [258, 126, 229, 128], [258, 127, 229, 129], [259, 4, 230, 4], [260, 4, 231, 4], [260, 8, 231, 8, "capabilities"], [260, 20, 231, 20], [260, 21, 231, 21, "whiteBalanceMode"], [260, 37, 231, 37], [260, 41, 231, 41, "settings"], [260, 49, 231, 49], [260, 50, 231, 50, "whiteBalance"], [260, 62, 231, 62], [260, 67, 231, 67, "undefined"], [260, 76, 231, 76], [260, 78, 231, 78], [261, 6, 232, 8, "constraints"], [261, 17, 232, 19], [261, 18, 232, 20, "whiteBalanceMode"], [261, 34, 232, 36], [261, 37, 232, 39, "validatedInternalConstrainedValue"], [261, 70, 232, 72], [261, 71, 232, 73], [261, 89, 232, 91], [261, 91, 232, 93], [261, 105, 232, 107], [261, 107, 232, 109, "CapabilityUtils"], [261, 122, 232, 124], [261, 123, 232, 125, "convertWhiteBalanceJSONToNative"], [261, 154, 232, 156], [261, 155, 232, 157], [262, 4, 233, 4], [263, 4, 234, 4], [263, 8, 234, 8], [264, 6, 235, 8], [264, 12, 235, 14, "track"], [264, 17, 235, 19], [264, 18, 235, 20, "applyConstraints"], [264, 34, 235, 36], [264, 35, 235, 37], [265, 8, 235, 39, "advanced"], [265, 16, 235, 47], [265, 18, 235, 49], [265, 19, 235, 50, "constraints"], [265, 30, 235, 61], [266, 6, 235, 63], [266, 7, 235, 64], [266, 8, 235, 65], [267, 4, 236, 4], [267, 5, 236, 5], [267, 6, 237, 4], [267, 13, 237, 11, "error"], [267, 18, 237, 16], [267, 20, 237, 18], [268, 6, 238, 8], [268, 10, 238, 12, "__DEV__"], [268, 17, 238, 19], [268, 19, 239, 12, "console"], [268, 26, 239, 19], [268, 27, 239, 20, "warn"], [268, 31, 239, 24], [268, 32, 239, 25], [268, 61, 239, 54], [268, 63, 239, 56, "error"], [268, 68, 239, 61], [268, 69, 239, 62], [269, 4, 240, 4], [270, 2, 241, 0], [271, 2, 242, 7], [271, 11, 242, 16, "stopMediaStream"], [271, 26, 242, 31, "stopMediaStream"], [271, 27, 242, 32, "stream"], [271, 33, 242, 38], [271, 35, 242, 40], [272, 4, 243, 4], [272, 8, 243, 8], [272, 9, 243, 9, "stream"], [272, 15, 243, 15], [272, 17, 243, 17], [273, 6, 244, 8], [274, 4, 245, 4], [275, 4, 246, 4], [275, 8, 246, 8, "stream"], [275, 14, 246, 14], [275, 15, 246, 15, "getAudioTracks"], [275, 29, 246, 29], [275, 31, 246, 31], [276, 6, 247, 8, "stream"], [276, 12, 247, 14], [276, 13, 247, 15, "getAudioTracks"], [276, 27, 247, 29], [276, 28, 247, 30], [276, 29, 247, 31], [276, 30, 247, 32, "for<PERSON>ach"], [276, 37, 247, 39], [276, 38, 247, 41, "track"], [276, 43, 247, 46], [276, 47, 247, 51, "track"], [276, 52, 247, 56], [276, 53, 247, 57, "stop"], [276, 57, 247, 61], [276, 58, 247, 62], [276, 59, 247, 63], [276, 60, 247, 64], [277, 4, 248, 4], [278, 4, 249, 4], [278, 8, 249, 8, "stream"], [278, 14, 249, 14], [278, 15, 249, 15, "getVideoTracks"], [278, 29, 249, 29], [278, 31, 249, 31], [279, 6, 250, 8, "stream"], [279, 12, 250, 14], [279, 13, 250, 15, "getVideoTracks"], [279, 27, 250, 29], [279, 28, 250, 30], [279, 29, 250, 31], [279, 30, 250, 32, "for<PERSON>ach"], [279, 37, 250, 39], [279, 38, 250, 41, "track"], [279, 43, 250, 46], [279, 47, 250, 51, "track"], [279, 52, 250, 56], [279, 53, 250, 57, "stop"], [279, 57, 250, 61], [279, 58, 250, 62], [279, 59, 250, 63], [279, 60, 250, 64], [280, 4, 251, 4], [281, 4, 252, 4], [281, 8, 252, 8, "isMediaStreamTrack"], [281, 26, 252, 26], [281, 27, 252, 27, "stream"], [281, 33, 252, 33], [281, 34, 252, 34], [281, 36, 252, 36], [282, 6, 253, 8, "stream"], [282, 12, 253, 14], [282, 13, 253, 15, "stop"], [282, 17, 253, 19], [282, 18, 253, 20], [282, 19, 253, 21], [283, 4, 254, 4], [284, 2, 255, 0], [285, 2, 256, 7], [285, 11, 256, 16, "setVideoSource"], [285, 25, 256, 30, "setVideoSource"], [285, 26, 256, 31, "video"], [285, 31, 256, 36], [285, 33, 256, 38, "stream"], [285, 39, 256, 44], [285, 41, 256, 46], [286, 4, 257, 4], [286, 10, 257, 10, "createObjectURL"], [286, 25, 257, 25], [286, 28, 257, 28, "window"], [286, 34, 257, 34], [286, 35, 257, 35, "URL"], [286, 38, 257, 38], [286, 39, 257, 39, "createObjectURL"], [286, 54, 257, 54], [286, 58, 257, 58, "window"], [286, 64, 257, 64], [286, 65, 257, 65, "webkitURL"], [286, 74, 257, 74], [286, 75, 257, 75, "createObjectURL"], [286, 90, 257, 90], [287, 4, 258, 4], [287, 8, 258, 8], [287, 15, 258, 15, "video"], [287, 20, 258, 20], [287, 21, 258, 21, "srcObject"], [287, 30, 258, 30], [287, 35, 258, 35], [287, 46, 258, 46], [287, 48, 258, 48], [288, 6, 259, 8, "video"], [288, 11, 259, 13], [288, 12, 259, 14, "srcObject"], [288, 21, 259, 23], [288, 24, 259, 26, "stream"], [288, 30, 259, 32], [289, 4, 260, 4], [289, 5, 260, 5], [289, 11, 261, 9], [289, 15, 261, 13], [289, 22, 261, 20, "video"], [289, 27, 261, 25], [289, 28, 261, 26, "mozSrcObject"], [289, 40, 261, 38], [289, 45, 261, 43], [289, 56, 261, 54], [289, 58, 261, 56], [290, 6, 262, 8, "video"], [290, 11, 262, 13], [290, 12, 262, 14, "mozSrcObject"], [290, 24, 262, 26], [290, 27, 262, 29, "stream"], [290, 33, 262, 35], [291, 4, 263, 4], [291, 5, 263, 5], [291, 11, 264, 9], [291, 15, 264, 13, "stream"], [291, 21, 264, 19], [291, 25, 264, 23, "createObjectURL"], [291, 40, 264, 38], [291, 42, 264, 40], [292, 6, 265, 8, "video"], [292, 11, 265, 13], [292, 12, 265, 14, "src"], [292, 15, 265, 17], [292, 18, 265, 20, "createObjectURL"], [292, 33, 265, 35], [292, 34, 265, 36, "stream"], [292, 40, 265, 42], [292, 41, 265, 43], [293, 4, 266, 4], [294, 4, 267, 4], [294, 8, 267, 8], [294, 9, 267, 9, "stream"], [294, 15, 267, 15], [294, 17, 267, 17], [295, 6, 268, 8], [295, 12, 268, 14, "revokeObjectURL"], [295, 27, 268, 29], [295, 30, 268, 32, "window"], [295, 36, 268, 38], [295, 37, 268, 39, "URL"], [295, 40, 268, 42], [295, 41, 268, 43, "revokeObjectURL"], [295, 56, 268, 58], [295, 60, 268, 62, "window"], [295, 66, 268, 68], [295, 67, 268, 69, "webkitURL"], [295, 76, 268, 78], [295, 77, 268, 79, "revokeObjectURL"], [295, 92, 268, 94], [296, 6, 269, 8], [296, 12, 269, 14, "source"], [296, 18, 269, 20], [296, 21, 269, 23, "video"], [296, 26, 269, 28], [296, 27, 269, 29, "src"], [296, 30, 269, 32], [296, 34, 269, 36, "video"], [296, 39, 269, 41], [296, 40, 269, 42, "srcObject"], [296, 49, 269, 51], [296, 53, 269, 55, "video"], [296, 58, 269, 60], [296, 59, 269, 61, "mozSrcObject"], [296, 71, 269, 73], [297, 6, 270, 8], [297, 10, 270, 12, "revokeObjectURL"], [297, 25, 270, 27], [297, 29, 270, 31], [297, 36, 270, 38, "source"], [297, 42, 270, 44], [297, 47, 270, 49], [297, 55, 270, 57], [297, 57, 270, 59], [298, 8, 271, 12, "revokeObjectURL"], [298, 23, 271, 27], [298, 24, 271, 28, "source"], [298, 30, 271, 34], [298, 31, 271, 35], [299, 6, 272, 8], [300, 4, 273, 4], [301, 2, 274, 0], [302, 2, 275, 7], [302, 11, 275, 16, "isCapabilityAvailable"], [302, 32, 275, 37, "isCapabilityAvailable"], [302, 33, 275, 38, "video"], [302, 38, 275, 43], [302, 40, 275, 45, "keyName"], [302, 47, 275, 52], [302, 49, 275, 54], [303, 4, 276, 4], [303, 10, 276, 10, "stream"], [303, 16, 276, 16], [303, 19, 276, 19, "video"], [303, 24, 276, 24], [303, 25, 276, 25, "srcObject"], [303, 34, 276, 34], [304, 4, 277, 4], [304, 8, 277, 8, "stream"], [304, 14, 277, 14], [304, 26, 277, 26, "MediaStream"], [304, 37, 277, 37], [304, 39, 277, 39], [305, 6, 278, 8], [305, 12, 278, 14, "videoTrack"], [305, 22, 278, 24], [305, 25, 278, 27, "stream"], [305, 31, 278, 33], [305, 32, 278, 34, "getVideoTracks"], [305, 46, 278, 48], [305, 47, 278, 49], [305, 48, 278, 50], [305, 49, 278, 51], [305, 50, 278, 52], [305, 51, 278, 53], [306, 6, 279, 8], [306, 13, 279, 15], [306, 14, 279, 16], [306, 15, 279, 17, "videoTrack"], [306, 25, 279, 27], [306, 26, 279, 28, "getCapabilities"], [306, 41, 279, 43], [306, 44, 279, 46], [306, 45, 279, 47], [306, 48, 279, 50, "keyName"], [306, 55, 279, 57], [306, 56, 279, 58], [307, 4, 280, 4], [308, 4, 281, 4], [308, 11, 281, 11], [308, 16, 281, 16], [309, 2, 282, 0], [310, 2, 283, 0], [310, 11, 283, 9, "isMediaStreamTrack"], [310, 29, 283, 27, "isMediaStreamTrack"], [310, 30, 283, 28, "input"], [310, 35, 283, 33], [310, 37, 283, 35], [311, 4, 284, 4], [311, 11, 284, 11], [311, 18, 284, 18, "input"], [311, 23, 284, 23], [311, 24, 284, 24, "stop"], [311, 28, 284, 28], [311, 33, 284, 33], [311, 43, 284, 43], [312, 2, 285, 0], [313, 2, 286, 0], [313, 11, 286, 9, "convertNormalizedSetting"], [313, 35, 286, 33, "convertNormalizedSetting"], [313, 36, 286, 34, "range"], [313, 41, 286, 39], [313, 43, 286, 41, "value"], [313, 48, 286, 46], [313, 50, 286, 48], [314, 4, 287, 4], [314, 8, 287, 8], [314, 9, 287, 9, "value"], [314, 14, 287, 14], [314, 16, 287, 16], [315, 6, 288, 8], [316, 4, 289, 4], [317, 4, 290, 4], [318, 4, 291, 4], [318, 10, 291, 10, "converted"], [318, 19, 291, 19], [318, 22, 291, 22, "convertRange"], [318, 34, 291, 34], [318, 35, 291, 35, "value"], [318, 40, 291, 40], [318, 42, 291, 42], [318, 43, 291, 43, "range"], [318, 48, 291, 48], [318, 49, 291, 49, "min"], [318, 52, 291, 52], [318, 54, 291, 54, "range"], [318, 59, 291, 59], [318, 60, 291, 60, "max"], [318, 63, 291, 63], [318, 64, 291, 64], [318, 65, 291, 65], [319, 4, 292, 4], [320, 4, 293, 4], [320, 11, 293, 11, "Math"], [320, 15, 293, 15], [320, 16, 293, 16, "min"], [320, 19, 293, 19], [320, 20, 293, 20, "range"], [320, 25, 293, 25], [320, 26, 293, 26, "max"], [320, 29, 293, 29], [320, 31, 293, 31, "Math"], [320, 35, 293, 35], [320, 36, 293, 36, "max"], [320, 39, 293, 39], [320, 40, 293, 40, "range"], [320, 45, 293, 45], [320, 46, 293, 46, "min"], [320, 49, 293, 49], [320, 51, 293, 51, "converted"], [320, 60, 293, 60], [320, 61, 293, 61], [320, 62, 293, 62], [321, 2, 294, 0], [322, 2, 295, 0], [322, 11, 295, 9, "convertRange"], [322, 23, 295, 21, "convertRange"], [322, 24, 295, 22, "value"], [322, 29, 295, 27], [322, 31, 295, 29, "r2"], [322, 33, 295, 31], [322, 35, 295, 33, "r1"], [322, 37, 295, 35], [322, 40, 295, 38], [322, 41, 295, 39], [322, 42, 295, 40], [322, 44, 295, 42], [322, 45, 295, 43], [322, 46, 295, 44], [322, 48, 295, 46], [323, 4, 296, 4], [323, 11, 296, 12], [323, 12, 296, 13, "value"], [323, 17, 296, 18], [323, 20, 296, 21, "r1"], [323, 22, 296, 23], [323, 23, 296, 24], [323, 24, 296, 25], [323, 25, 296, 26], [323, 30, 296, 31, "r2"], [323, 32, 296, 33], [323, 33, 296, 34], [323, 34, 296, 35], [323, 35, 296, 36], [323, 38, 296, 39, "r2"], [323, 40, 296, 41], [323, 41, 296, 42], [323, 42, 296, 43], [323, 43, 296, 44], [323, 44, 296, 45], [323, 48, 296, 50, "r1"], [323, 50, 296, 52], [323, 51, 296, 53], [323, 52, 296, 54], [323, 53, 296, 55], [323, 56, 296, 58, "r1"], [323, 58, 296, 60], [323, 59, 296, 61], [323, 60, 296, 62], [323, 61, 296, 63], [323, 62, 296, 64], [323, 65, 296, 67, "r2"], [323, 67, 296, 69], [323, 68, 296, 70], [323, 69, 296, 71], [323, 70, 296, 72], [324, 2, 297, 0], [325, 2, 298, 0], [325, 11, 298, 9, "validatedConstrainedValue"], [325, 36, 298, 34, "validatedConstrainedValue"], [325, 37, 298, 35, "props"], [325, 42, 298, 40], [325, 44, 298, 42], [326, 4, 299, 4], [326, 10, 299, 10], [327, 6, 299, 12, "constraintKey"], [327, 19, 299, 25], [328, 6, 299, 27, "<PERSON><PERSON><PERSON>"], [328, 17, 299, 38], [329, 6, 299, 40, "convertedSetting"], [329, 22, 299, 56], [330, 6, 299, 58, "capabilities"], [330, 18, 299, 70], [331, 6, 299, 72, "settings"], [331, 14, 299, 80], [332, 6, 299, 82, "cameraType"], [333, 4, 299, 93], [333, 5, 299, 94], [333, 8, 299, 97, "props"], [333, 13, 299, 102], [334, 4, 300, 4], [334, 10, 300, 10, "setting"], [334, 17, 300, 17], [334, 20, 300, 20, "settings"], [334, 28, 300, 28], [334, 29, 300, 29, "<PERSON><PERSON><PERSON>"], [334, 40, 300, 40], [334, 41, 300, 41], [335, 4, 301, 4], [335, 8, 301, 8, "Array"], [335, 13, 301, 13], [335, 14, 301, 14, "isArray"], [335, 21, 301, 21], [335, 22, 301, 22, "capabilities"], [335, 34, 301, 34], [335, 35, 301, 35, "constraintKey"], [335, 48, 301, 48], [335, 49, 301, 49], [335, 50, 301, 50], [335, 54, 302, 8, "convertedSetting"], [335, 70, 302, 24], [335, 74, 303, 8], [335, 75, 303, 9, "capabilities"], [335, 87, 303, 21], [335, 88, 303, 22, "constraintKey"], [335, 101, 303, 35], [335, 102, 303, 36], [335, 103, 303, 37, "includes"], [335, 111, 303, 45], [335, 112, 303, 46, "convertedSetting"], [335, 128, 303, 62], [335, 129, 303, 63], [335, 131, 303, 65], [336, 6, 304, 8], [336, 10, 304, 12, "__DEV__"], [336, 17, 304, 19], [336, 19, 304, 21], [337, 8, 305, 12], [338, 8, 306, 12, "console"], [338, 15, 306, 19], [338, 16, 306, 20, "warn"], [338, 20, 306, 24], [338, 21, 306, 25], [338, 27, 306, 31, "<PERSON><PERSON><PERSON>"], [338, 38, 306, 42], [338, 44, 306, 48, "setting"], [338, 51, 306, 55], [338, 73, 306, 77, "convertedSetting"], [338, 89, 306, 93], [338, 144, 306, 148, "cameraType"], [338, 154, 306, 158], [338, 207, 306, 211], [338, 208, 306, 212], [339, 6, 307, 8], [340, 6, 308, 8], [340, 13, 308, 15, "undefined"], [340, 22, 308, 24], [341, 4, 309, 4], [342, 4, 310, 4], [342, 11, 310, 11, "convertedSetting"], [342, 27, 310, 27], [343, 2, 311, 0], [344, 0, 311, 1], [344, 3]], "functionMap": {"names": ["<global>", "getImageSize", "toDataURL", "hasValidConstraints", "ensureCameraPictureOptions", "captureImageData", "captureImageContext", "captureImage", "getSupportedConstraints", "getIdealConstraints", "isMediaTrackConstraints", "getPreferredStreamDevice", "getStreamDevice", "isWebKit", "compareStreams", "capture", "syncTrackCapabilities", "stream.getVideoTracks.map$argument_0", "onCapabilitiesReady", "validatedInternalConstrainedValue", "stopMediaStream", "stream.getAudioTracks.forEach$argument_0", "stream.getVideoTracks.forEach$argument_0", "setVideoSource", "isCapabilityAvailable", "isMediaStreamTrack", "convertNormalizedSetting", "convertRange", "validatedConstrainedValue"], "mappings": "AAA;OCK;CDQ;OEC;CFW;OGC;CHE;AIC;CJa;OKE;CLW;OMC;CNoB;OOC;CPK;AQC;CRK;OSC;CTiC;AUC;CVE;OWQ;CXa;OYC;CZI;OaC;CbE;OcC;CdO;OeC;CfmB;OgBC;sDCE,2DD;ChBE;AkBE;ICwB;KDU;ClBiB;OoBC;wCCK,uBD;wCEG,uBF;CpBK;OuBC;CvBkB;OwBC;CxBO;AyBC;CzBE;A0BC;C1BQ;A2BC;C3BE;A4BC;C5Ba"}}, "type": "js/module"}]}