{"dependencies": [{"name": "../../../dom/nodes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "Z+GW5Ist+DDyIe4BLHPS68wWHKY=", "exportNames": ["*"]}}, {"name": "../../../skia/types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 60}, "end": {"line": 2, "column": 83, "index": 143}}], "key": "hnxlDT1tba4gQfvf2h/i6nte9KM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.setPaintProperties = void 0;\n  var _nodes = require(_dependencyMap[0], \"../../../dom/nodes\");\n  var _types = require(_dependencyMap[1], \"../../../skia/types\");\n  const _worklet_15442789420762_init_data = {\n    code: \"function PaintJs1(Skia,paint,{opacity:opacity,color:color,blendMode:blendMode,strokeWidth:strokeWidth,style:style,strokeJoin:strokeJoin,strokeCap:strokeCap,strokeMiter:strokeMiter,antiAlias:antiAlias,dither:dither}){const{processColor,BlendMode,enumKey,PaintStyle,StrokeJoin,StrokeCap}=this.__closure;if(opacity!==undefined){paint.setAlphaf(paint.getAlphaf()*opacity);}if(color!==undefined){const currentOpacity=paint.getAlphaf();paint.setShader(null);paint.setColor(processColor(Skia,color));paint.setAlphaf(currentOpacity*paint.getAlphaf());}if(blendMode!==undefined){paint.setBlendMode(BlendMode[enumKey(blendMode)]);}if(strokeWidth!==undefined){paint.setStrokeWidth(strokeWidth);}if(style!==undefined){paint.setStyle(PaintStyle[enumKey(style)]);}if(strokeJoin!==undefined){paint.setStrokeJoin(StrokeJoin[enumKey(strokeJoin)]);}if(strokeCap!==undefined){paint.setStrokeCap(StrokeCap[enumKey(strokeCap)]);}if(strokeMiter!==undefined){paint.setStrokeMiter(strokeMiter);}if(antiAlias!==undefined){paint.setAntiAlias(antiAlias);}if(dither!==undefined){paint.setDither(dither);}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Recorder\\\\commands\\\\Paint.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PaintJs1\\\",\\\"Skia\\\",\\\"paint\\\",\\\"opacity\\\",\\\"color\\\",\\\"blendMode\\\",\\\"strokeWidth\\\",\\\"style\\\",\\\"strokeJoin\\\",\\\"strokeCap\\\",\\\"strokeMiter\\\",\\\"antiAlias\\\",\\\"dither\\\",\\\"processColor\\\",\\\"BlendMode\\\",\\\"enumKey\\\",\\\"PaintStyle\\\",\\\"StrokeJoin\\\",\\\"StrokeCap\\\",\\\"__closure\\\",\\\"undefined\\\",\\\"setAlphaf\\\",\\\"getAlphaf\\\",\\\"currentOpacity\\\",\\\"setShader\\\",\\\"setColor\\\",\\\"setBlendMode\\\",\\\"setStrokeWidth\\\",\\\"setStyle\\\",\\\"setStrokeJoin\\\",\\\"setStrokeCap\\\",\\\"setStrokeMiter\\\",\\\"setAntiAlias\\\",\\\"setDither\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Recorder/commands/Paint.js\\\"],\\\"mappings\\\":\\\"AAEkC,QAAC,CAAAA,QAAMA,CAAAC,IAAK,CAAEC,KAAA,EAC9CC,OAAO,CAAPA,OAAO,CACPC,KAAK,CAALA,KAAK,CACLC,SAAS,CAATA,SAAS,CACTC,WAAW,CAAXA,WAAW,CACXC,KAAK,CAALA,KAAK,CACLC,UAAU,CAAVA,UAAU,CACVC,SAAS,CAATA,SAAS,CACTC,WAAW,CAAXA,WAAW,CACXC,SAAS,CAATA,SAAS,CACTC,MAAA,CAAAA,MACF,CAAC,CAAK,OAAAC,YAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,UAAA,CAAAC,UAAA,CAAAC,SAAA,OAAAC,SAAA,CAGJ,GAAIhB,OAAO,GAAKiB,SAAS,CAAE,CACzBlB,KAAK,CAACmB,SAAS,CAACnB,KAAK,CAACoB,SAAS,CAAC,CAAC,CAAGnB,OAAO,CAAC,CAC9C,CACA,GAAIC,KAAK,GAAKgB,SAAS,CAAE,CACvB,KAAM,CAAAG,cAAc,CAAGrB,KAAK,CAACoB,SAAS,CAAC,CAAC,CACxCpB,KAAK,CAACsB,SAAS,CAAC,IAAI,CAAC,CACrBtB,KAAK,CAACuB,QAAQ,CAACZ,YAAY,CAACZ,IAAI,CAAEG,KAAK,CAAC,CAAC,CACzCF,KAAK,CAACmB,SAAS,CAACE,cAAc,CAAGrB,KAAK,CAACoB,SAAS,CAAC,CAAC,CAAC,CACrD,CACA,GAAIjB,SAAS,GAAKe,SAAS,CAAE,CAC3BlB,KAAK,CAACwB,YAAY,CAACZ,SAAS,CAACC,OAAO,CAACV,SAAS,CAAC,CAAC,CAAC,CACnD,CACA,GAAIC,WAAW,GAAKc,SAAS,CAAE,CAC7BlB,KAAK,CAACyB,cAAc,CAACrB,WAAW,CAAC,CACnC,CACA,GAAIC,KAAK,GAAKa,SAAS,CAAE,CACvBlB,KAAK,CAAC0B,QAAQ,CAACZ,UAAU,CAACD,OAAO,CAACR,KAAK,CAAC,CAAC,CAAC,CAC5C,CACA,GAAIC,UAAU,GAAKY,SAAS,CAAE,CAC5BlB,KAAK,CAAC2B,aAAa,CAACZ,UAAU,CAACF,OAAO,CAACP,UAAU,CAAC,CAAC,CAAC,CACtD,CACA,GAAIC,SAAS,GAAKW,SAAS,CAAE,CAC3BlB,KAAK,CAAC4B,YAAY,CAACZ,SAAS,CAACH,OAAO,CAACN,SAAS,CAAC,CAAC,CAAC,CACnD,CACA,GAAIC,WAAW,GAAKU,SAAS,CAAE,CAC7BlB,KAAK,CAAC6B,cAAc,CAACrB,WAAW,CAAC,CACnC,CACA,GAAIC,SAAS,GAAKS,SAAS,CAAE,CAC3BlB,KAAK,CAAC8B,YAAY,CAACrB,SAAS,CAAC,CAC/B,CACA,GAAIC,MAAM,GAAKQ,SAAS,CAAE,CACxBlB,KAAK,CAAC+B,SAAS,CAACrB,MAAM,CAAC,CACzB,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const setPaintProperties = exports.setPaintProperties = function () {\n    const _e = [new global.Error(), -7, -27];\n    const PaintJs1 = function (Skia, paint, {\n      opacity,\n      color,\n      blendMode,\n      strokeWidth,\n      style,\n      strokeJoin,\n      strokeCap,\n      strokeMiter,\n      antiAlias,\n      dither\n    }) {\n      if (opacity !== undefined) {\n        paint.setAlphaf(paint.getAlphaf() * opacity);\n      }\n      if (color !== undefined) {\n        const currentOpacity = paint.getAlphaf();\n        paint.setShader(null);\n        paint.setColor((0, _nodes.processColor)(Skia, color));\n        paint.setAlphaf(currentOpacity * paint.getAlphaf());\n      }\n      if (blendMode !== undefined) {\n        paint.setBlendMode(_types.BlendMode[(0, _nodes.enumKey)(blendMode)]);\n      }\n      if (strokeWidth !== undefined) {\n        paint.setStrokeWidth(strokeWidth);\n      }\n      if (style !== undefined) {\n        paint.setStyle(_types.PaintStyle[(0, _nodes.enumKey)(style)]);\n      }\n      if (strokeJoin !== undefined) {\n        paint.setStrokeJoin(_types.StrokeJoin[(0, _nodes.enumKey)(strokeJoin)]);\n      }\n      if (strokeCap !== undefined) {\n        paint.setStrokeCap(_types.StrokeCap[(0, _nodes.enumKey)(strokeCap)]);\n      }\n      if (strokeMiter !== undefined) {\n        paint.setStrokeMiter(strokeMiter);\n      }\n      if (antiAlias !== undefined) {\n        paint.setAntiAlias(antiAlias);\n      }\n      if (dither !== undefined) {\n        paint.setDither(dither);\n      }\n    };\n    PaintJs1.__closure = {\n      processColor: _nodes.processColor,\n      BlendMode: _types.BlendMode,\n      enumKey: _nodes.enumKey,\n      PaintStyle: _types.PaintStyle,\n      StrokeJoin: _types.StrokeJoin,\n      StrokeCap: _types.StrokeCap\n    };\n    PaintJs1.__workletHash = 15442789420762;\n    PaintJs1.__initData = _worklet_15442789420762_init_data;\n    PaintJs1.__stackDetails = _e;\n    return PaintJs1;\n  }();\n});", "lineCount": 75, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_nodes"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_types"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 2, 83], [8, 8, 2, 83, "_worklet_15442789420762_init_data"], [8, 41, 2, 83], [9, 4, 2, 83, "code"], [9, 8, 2, 83], [10, 4, 2, 83, "location"], [10, 12, 2, 83], [11, 4, 2, 83, "sourceMap"], [11, 13, 2, 83], [12, 4, 2, 83, "version"], [12, 11, 2, 83], [13, 2, 2, 83], [14, 2, 3, 7], [14, 8, 3, 13, "setPaintProperties"], [14, 26, 3, 31], [14, 29, 3, 31, "exports"], [14, 36, 3, 31], [14, 37, 3, 31, "setPaintProperties"], [14, 55, 3, 31], [14, 58, 3, 34], [15, 4, 3, 34], [15, 10, 3, 34, "_e"], [15, 12, 3, 34], [15, 20, 3, 34, "global"], [15, 26, 3, 34], [15, 27, 3, 34, "Error"], [15, 32, 3, 34], [16, 4, 3, 34], [16, 10, 3, 34, "PaintJs1"], [16, 18, 3, 34], [16, 30, 3, 34, "PaintJs1"], [16, 31, 3, 35, "Skia"], [16, 35, 3, 39], [16, 37, 3, 41, "paint"], [16, 42, 3, 46], [16, 44, 3, 48], [17, 6, 4, 2, "opacity"], [17, 13, 4, 9], [18, 6, 5, 2, "color"], [18, 11, 5, 7], [19, 6, 6, 2, "blendMode"], [19, 15, 6, 11], [20, 6, 7, 2, "strokeWidth"], [20, 17, 7, 13], [21, 6, 8, 2, "style"], [21, 11, 8, 7], [22, 6, 9, 2, "<PERSON><PERSON><PERSON><PERSON>"], [22, 16, 9, 12], [23, 6, 10, 2, "strokeCap"], [23, 15, 10, 11], [24, 6, 11, 2, "strokeMiter"], [24, 17, 11, 13], [25, 6, 12, 2, "antiAlias"], [25, 15, 12, 11], [26, 6, 13, 2, "dither"], [27, 4, 14, 0], [27, 5, 14, 1], [27, 7, 14, 6], [28, 6, 17, 2], [28, 10, 17, 6, "opacity"], [28, 17, 17, 13], [28, 22, 17, 18, "undefined"], [28, 31, 17, 27], [28, 33, 17, 29], [29, 8, 18, 4, "paint"], [29, 13, 18, 9], [29, 14, 18, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [29, 23, 18, 19], [29, 24, 18, 20, "paint"], [29, 29, 18, 25], [29, 30, 18, 26, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [29, 39, 18, 35], [29, 40, 18, 36], [29, 41, 18, 37], [29, 44, 18, 40, "opacity"], [29, 51, 18, 47], [29, 52, 18, 48], [30, 6, 19, 2], [31, 6, 20, 2], [31, 10, 20, 6, "color"], [31, 15, 20, 11], [31, 20, 20, 16, "undefined"], [31, 29, 20, 25], [31, 31, 20, 27], [32, 8, 21, 4], [32, 14, 21, 10, "currentOpacity"], [32, 28, 21, 24], [32, 31, 21, 27, "paint"], [32, 36, 21, 32], [32, 37, 21, 33, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [32, 46, 21, 42], [32, 47, 21, 43], [32, 48, 21, 44], [33, 8, 22, 4, "paint"], [33, 13, 22, 9], [33, 14, 22, 10, "<PERSON><PERSON><PERSON><PERSON>"], [33, 23, 22, 19], [33, 24, 22, 20], [33, 28, 22, 24], [33, 29, 22, 25], [34, 8, 23, 4, "paint"], [34, 13, 23, 9], [34, 14, 23, 10, "setColor"], [34, 22, 23, 18], [34, 23, 23, 19], [34, 27, 23, 19, "processColor"], [34, 46, 23, 31], [34, 48, 23, 32, "Skia"], [34, 52, 23, 36], [34, 54, 23, 38, "color"], [34, 59, 23, 43], [34, 60, 23, 44], [34, 61, 23, 45], [35, 8, 24, 4, "paint"], [35, 13, 24, 9], [35, 14, 24, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [35, 23, 24, 19], [35, 24, 24, 20, "currentOpacity"], [35, 38, 24, 34], [35, 41, 24, 37, "paint"], [35, 46, 24, 42], [35, 47, 24, 43, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [35, 56, 24, 52], [35, 57, 24, 53], [35, 58, 24, 54], [35, 59, 24, 55], [36, 6, 25, 2], [37, 6, 26, 2], [37, 10, 26, 6, "blendMode"], [37, 19, 26, 15], [37, 24, 26, 20, "undefined"], [37, 33, 26, 29], [37, 35, 26, 31], [38, 8, 27, 4, "paint"], [38, 13, 27, 9], [38, 14, 27, 10, "setBlendMode"], [38, 26, 27, 22], [38, 27, 27, 23, "BlendMode"], [38, 43, 27, 32], [38, 44, 27, 33], [38, 48, 27, 33, "<PERSON><PERSON><PERSON><PERSON>"], [38, 62, 27, 40], [38, 64, 27, 41, "blendMode"], [38, 73, 27, 50], [38, 74, 27, 51], [38, 75, 27, 52], [38, 76, 27, 53], [39, 6, 28, 2], [40, 6, 29, 2], [40, 10, 29, 6, "strokeWidth"], [40, 21, 29, 17], [40, 26, 29, 22, "undefined"], [40, 35, 29, 31], [40, 37, 29, 33], [41, 8, 30, 4, "paint"], [41, 13, 30, 9], [41, 14, 30, 10, "setStrokeWidth"], [41, 28, 30, 24], [41, 29, 30, 25, "strokeWidth"], [41, 40, 30, 36], [41, 41, 30, 37], [42, 6, 31, 2], [43, 6, 32, 2], [43, 10, 32, 6, "style"], [43, 15, 32, 11], [43, 20, 32, 16, "undefined"], [43, 29, 32, 25], [43, 31, 32, 27], [44, 8, 33, 4, "paint"], [44, 13, 33, 9], [44, 14, 33, 10, "setStyle"], [44, 22, 33, 18], [44, 23, 33, 19, "PaintStyle"], [44, 40, 33, 29], [44, 41, 33, 30], [44, 45, 33, 30, "<PERSON><PERSON><PERSON><PERSON>"], [44, 59, 33, 37], [44, 61, 33, 38, "style"], [44, 66, 33, 43], [44, 67, 33, 44], [44, 68, 33, 45], [44, 69, 33, 46], [45, 6, 34, 2], [46, 6, 35, 2], [46, 10, 35, 6, "<PERSON><PERSON><PERSON><PERSON>"], [46, 20, 35, 16], [46, 25, 35, 21, "undefined"], [46, 34, 35, 30], [46, 36, 35, 32], [47, 8, 36, 4, "paint"], [47, 13, 36, 9], [47, 14, 36, 10, "set<PERSON><PERSON><PERSON><PERSON><PERSON>"], [47, 27, 36, 23], [47, 28, 36, 24, "StrokeJ<PERSON><PERSON>"], [47, 45, 36, 34], [47, 46, 36, 35], [47, 50, 36, 35, "<PERSON><PERSON><PERSON><PERSON>"], [47, 64, 36, 42], [47, 66, 36, 43, "<PERSON><PERSON><PERSON><PERSON>"], [47, 76, 36, 53], [47, 77, 36, 54], [47, 78, 36, 55], [47, 79, 36, 56], [48, 6, 37, 2], [49, 6, 38, 2], [49, 10, 38, 6, "strokeCap"], [49, 19, 38, 15], [49, 24, 38, 20, "undefined"], [49, 33, 38, 29], [49, 35, 38, 31], [50, 8, 39, 4, "paint"], [50, 13, 39, 9], [50, 14, 39, 10, "setStrokeCap"], [50, 26, 39, 22], [50, 27, 39, 23, "StrokeCap"], [50, 43, 39, 32], [50, 44, 39, 33], [50, 48, 39, 33, "<PERSON><PERSON><PERSON><PERSON>"], [50, 62, 39, 40], [50, 64, 39, 41, "strokeCap"], [50, 73, 39, 50], [50, 74, 39, 51], [50, 75, 39, 52], [50, 76, 39, 53], [51, 6, 40, 2], [52, 6, 41, 2], [52, 10, 41, 6, "strokeMiter"], [52, 21, 41, 17], [52, 26, 41, 22, "undefined"], [52, 35, 41, 31], [52, 37, 41, 33], [53, 8, 42, 4, "paint"], [53, 13, 42, 9], [53, 14, 42, 10, "setStrokeMiter"], [53, 28, 42, 24], [53, 29, 42, 25, "strokeMiter"], [53, 40, 42, 36], [53, 41, 42, 37], [54, 6, 43, 2], [55, 6, 44, 2], [55, 10, 44, 6, "antiAlias"], [55, 19, 44, 15], [55, 24, 44, 20, "undefined"], [55, 33, 44, 29], [55, 35, 44, 31], [56, 8, 45, 4, "paint"], [56, 13, 45, 9], [56, 14, 45, 10, "set<PERSON>nti<PERSON><PERSON><PERSON>"], [56, 26, 45, 22], [56, 27, 45, 23, "antiAlias"], [56, 36, 45, 32], [56, 37, 45, 33], [57, 6, 46, 2], [58, 6, 47, 2], [58, 10, 47, 6, "dither"], [58, 16, 47, 12], [58, 21, 47, 17, "undefined"], [58, 30, 47, 26], [58, 32, 47, 28], [59, 8, 48, 4, "paint"], [59, 13, 48, 9], [59, 14, 48, 10, "<PERSON><PERSON><PERSON><PERSON>"], [59, 23, 48, 19], [59, 24, 48, 20, "dither"], [59, 30, 48, 26], [59, 31, 48, 27], [60, 6, 49, 2], [61, 4, 50, 0], [61, 5, 50, 1], [62, 4, 50, 1, "PaintJs1"], [62, 12, 50, 1], [62, 13, 50, 1, "__closure"], [62, 22, 50, 1], [63, 6, 50, 1, "processColor"], [63, 18, 50, 1], [63, 20, 23, 19, "processColor"], [63, 39, 23, 31], [64, 6, 23, 31, "BlendMode"], [64, 15, 23, 31], [64, 17, 27, 23, "BlendMode"], [64, 33, 27, 32], [65, 6, 27, 32, "<PERSON><PERSON><PERSON><PERSON>"], [65, 13, 27, 32], [65, 15, 27, 33, "<PERSON><PERSON><PERSON><PERSON>"], [65, 29, 27, 40], [66, 6, 27, 40, "PaintStyle"], [66, 16, 27, 40], [66, 18, 33, 19, "PaintStyle"], [66, 35, 33, 29], [67, 6, 33, 29, "StrokeJ<PERSON><PERSON>"], [67, 16, 33, 29], [67, 18, 36, 24, "StrokeJ<PERSON><PERSON>"], [67, 35, 36, 34], [68, 6, 36, 34, "StrokeCap"], [68, 15, 36, 34], [68, 17, 39, 23, "StrokeCap"], [69, 4, 39, 32], [70, 4, 39, 32, "PaintJs1"], [70, 12, 39, 32], [70, 13, 39, 32, "__workletHash"], [70, 26, 39, 32], [71, 4, 39, 32, "PaintJs1"], [71, 12, 39, 32], [71, 13, 39, 32, "__initData"], [71, 23, 39, 32], [71, 26, 39, 32, "_worklet_15442789420762_init_data"], [71, 59, 39, 32], [72, 4, 39, 32, "PaintJs1"], [72, 12, 39, 32], [72, 13, 39, 32, "__stackDetails"], [72, 27, 39, 32], [72, 30, 39, 32, "_e"], [72, 32, 39, 32], [73, 4, 39, 32], [73, 11, 39, 32, "PaintJs1"], [73, 19, 39, 32], [74, 2, 39, 32], [74, 3, 3, 34], [74, 5, 50, 1], [75, 0, 50, 2], [75, 3]], "functionMap": {"names": ["<global>", "setPaintProperties"], "mappings": "AAA;kCCE;CD+C"}}, "type": "js/module"}]}