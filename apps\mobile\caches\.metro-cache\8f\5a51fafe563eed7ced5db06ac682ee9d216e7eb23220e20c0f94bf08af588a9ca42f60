{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 18}, "end": {"line": 2, "column": 38, "index": 56}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-reconciler/constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 57}, "end": {"line": 3, "column": 66, "index": 123}}], "key": "/30Lu+LTNpP9eCS/hYUee7D0cGM=", "exportNames": ["*"]}}, {"name": "../renderer/typeddash", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 124}, "end": {"line": 4, "column": 50, "index": 174}}], "key": "vgGalf47jbvzGv0xbcZeWzlKG3c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.sksgHostConfig = exports.debug = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _constants = require(_dependencyMap[1], \"react-reconciler/constants\");\n  var _typeddash = require(_dependencyMap[2], \"../renderer/typeddash\");\n  /*global NodeJS*/\n\n  const NoEventPriority = 0;\n  const DEBUG = false;\n  const debug = (...args) => {\n    if (DEBUG) {\n      console.log(...args);\n    }\n  };\n  exports.debug = debug;\n  let currentUpdatePriority = NoEventPriority;\n  const sksgHostConfig = exports.sksgHostConfig = {\n    /**\n     * This function is used by the reconciler in order to calculate current time for prioritising work.\n     */\n    supportsMutation: false,\n    isPrimaryRenderer: false,\n    supportsPersistence: true,\n    supportsHydration: false,\n    //supportsMicrotask: true,\n    scheduleTimeout: setTimeout,\n    cancelTimeout: clearTimeout,\n    noTimeout: -1,\n    getRootHostContext: _rootContainerInstance => {\n      debug(\"getRootHostContext\");\n      return {};\n    },\n    getChildHostContext(_parentHostContext, _type, _rootContainerInstance) {\n      debug(\"getChildHostContext\");\n      return {};\n    },\n    shouldSetTextContent(_type, _props) {\n      return false;\n    },\n    createTextInstance(_text, _rootContainerInstance, _hostContext, _internalInstanceHandle) {\n      debug(\"createTextInstance\");\n      // return SpanNode({}, text) as SkNode;\n      throw new Error(\"Text nodes are not supported yet\");\n    },\n    createInstance(type, propsWithChildren, _container, _hostContext, _internalInstanceHandle) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const {\n        children,\n        ...props\n      } = propsWithChildren;\n      debug(\"createInstance\", type);\n      const instance = {\n        type,\n        props,\n        children: []\n      };\n      return instance;\n    },\n    appendInitialChild(parentInstance, child) {\n      parentInstance.children.push(child);\n    },\n    finalizeInitialChildren(parentInstance, _type, _props, _rootContainerInstance, _hostContext) {\n      debug(\"finalizeInitialChildren\", parentInstance);\n      return false;\n    },\n    commitMount() {\n      // if finalizeInitialChildren = true\n      debug(\"commitMount\");\n    },\n    prepareForCommit(_container) {\n      debug(\"prepareForCommit\");\n      return null;\n    },\n    resetAfterCommit(container) {\n      debug(\"resetAfterCommit\");\n      container.redraw();\n    },\n    getPublicInstance(node) {\n      debug(\"getPublicInstance\");\n      return node;\n    },\n    commitTextUpdate: (_textInstance, _oldText, _newText) => {\n      //  textInstance.instance = newText;\n    },\n    clearContainer: _container => {\n      debug(\"clearContainer\");\n    },\n    prepareUpdate(_instance, _type, oldProps, newProps, container, _hostContext) {\n      debug(\"prepareUpdate\");\n      const propsAreEqual = (0, _typeddash.shallowEq)(oldProps, newProps);\n      if (propsAreEqual) {\n        return null;\n      }\n      return container;\n    },\n    preparePortalMount: () => {\n      debug(\"preparePortalMount\");\n    },\n    cloneInstance(instance, _type, _oldProps, newProps, _updatePayload, _internalInstanceHandle, keepChildren, _recyclableInstance) {\n      debug(\"cloneInstance\");\n      return {\n        type: instance.type,\n        props: {\n          ...newProps\n        },\n        children: keepChildren ? [...instance.children] : []\n      };\n    },\n    createContainerChildSet() {\n      debug(\"createContainerChildSet\");\n      return [];\n    },\n    appendChildToContainerChildSet(childSet, child) {\n      childSet.push(child);\n    },\n    finalizeContainerChildren(container, newChildren) {\n      debug(\"finalizeContainerChildren\");\n      container.root = newChildren;\n    },\n    replaceContainerChildren(container, newChildren) {\n      container.root = newChildren;\n    },\n    cloneHiddenInstance(_instance, _type, _props) {\n      debug(\"cloneHiddenInstance\");\n      throw new Error(\"Not yet implemented.\");\n    },\n    cloneHiddenTextInstance(_instance, _text) {\n      debug(\"cloneHiddenTextInstance\");\n      throw new Error(\"Not yet implemented.\");\n    },\n    // see https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n    getCurrentEventPriority: () => _constants.DefaultEventPriority,\n    beforeActiveInstanceBlur: () => {},\n    afterActiveInstanceBlur: () => {},\n    detachDeletedInstance: _node => {},\n    getInstanceFromNode: function (_node) {\n      throw new Error(\"Function not implemented.\");\n    },\n    prepareScopeUpdate: function (_scopeInstance, _instance) {\n      throw new Error(\"Function not implemented.\");\n    },\n    getInstanceFromScope: function (_scopeInstance) {\n      throw new Error(\"Function not implemented.\");\n    },\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-expect-error\n    shouldAttemptEagerTransition: () => false,\n    trackSchedulerEvent: () => {},\n    resolveEventType: () => null,\n    resolveEventTimeStamp: () => -1.1,\n    requestPostPaintCallback() {},\n    maySuspendCommit: () => false,\n    preloadInstance: () => true,\n    // true indicates already loaded\n    startSuspendingCommit() {},\n    suspendInstance() {},\n    waitForCommitToBeReady: () => null,\n    NotPendingTransition: null,\n    HostTransitionContext: /*#__PURE__*/(0, _react.createContext)(null),\n    setCurrentUpdatePriority(newPriority) {\n      currentUpdatePriority = newPriority;\n    },\n    getCurrentUpdatePriority() {\n      return currentUpdatePriority;\n    },\n    resolveUpdatePriority() {\n      if (currentUpdatePriority !== NoEventPriority) {\n        return currentUpdatePriority;\n      }\n      return _constants.DefaultEventPriority;\n    },\n    resetFormInstance() {}\n  };\n});", "lineCount": 177, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "_react"], [6, 12, 2, 0], [6, 15, 2, 0, "require"], [6, 22, 2, 0], [6, 23, 2, 0, "_dependencyMap"], [6, 37, 2, 0], [7, 2, 3, 0], [7, 6, 3, 0, "_constants"], [7, 16, 3, 0], [7, 19, 3, 0, "require"], [7, 26, 3, 0], [7, 27, 3, 0, "_dependencyMap"], [7, 41, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_typeddash"], [8, 16, 4, 0], [8, 19, 4, 0, "require"], [8, 26, 4, 0], [8, 27, 4, 0, "_dependencyMap"], [8, 41, 4, 0], [9, 2, 1, 0], [11, 2, 5, 0], [11, 8, 5, 6, "NoEventPriority"], [11, 23, 5, 21], [11, 26, 5, 24], [11, 27, 5, 25], [12, 2, 6, 0], [12, 8, 6, 6, "DEBUG"], [12, 13, 6, 11], [12, 16, 6, 14], [12, 21, 6, 19], [13, 2, 7, 7], [13, 8, 7, 13, "debug"], [13, 13, 7, 18], [13, 16, 7, 21, "debug"], [13, 17, 7, 22], [13, 20, 7, 25, "args"], [13, 24, 7, 29], [13, 29, 7, 34], [14, 4, 8, 2], [14, 8, 8, 6, "DEBUG"], [14, 13, 8, 11], [14, 15, 8, 13], [15, 6, 9, 4, "console"], [15, 13, 9, 11], [15, 14, 9, 12, "log"], [15, 17, 9, 15], [15, 18, 9, 16], [15, 21, 9, 19, "args"], [15, 25, 9, 23], [15, 26, 9, 24], [16, 4, 10, 2], [17, 2, 11, 0], [17, 3, 11, 1], [18, 2, 11, 2, "exports"], [18, 9, 11, 2], [18, 10, 11, 2, "debug"], [18, 15, 11, 2], [18, 18, 11, 2, "debug"], [18, 23, 11, 2], [19, 2, 12, 0], [19, 6, 12, 4, "currentUpdatePriority"], [19, 27, 12, 25], [19, 30, 12, 28, "NoEventPriority"], [19, 45, 12, 43], [20, 2, 13, 7], [20, 8, 13, 13, "sksgHostConfig"], [20, 22, 13, 27], [20, 25, 13, 27, "exports"], [20, 32, 13, 27], [20, 33, 13, 27, "sksgHostConfig"], [20, 47, 13, 27], [20, 50, 13, 30], [21, 4, 14, 2], [22, 0, 15, 0], [23, 0, 16, 0], [24, 4, 17, 2, "supportsMutation"], [24, 20, 17, 18], [24, 22, 17, 20], [24, 27, 17, 25], [25, 4, 18, 2, "isPrimary<PERSON><PERSON><PERSON>"], [25, 21, 18, 19], [25, 23, 18, 21], [25, 28, 18, 26], [26, 4, 19, 2, "supportsPersistence"], [26, 23, 19, 21], [26, 25, 19, 23], [26, 29, 19, 27], [27, 4, 20, 2, "supportsHydration"], [27, 21, 20, 19], [27, 23, 20, 21], [27, 28, 20, 26], [28, 4, 21, 2], [29, 4, 22, 2, "scheduleTimeout"], [29, 19, 22, 17], [29, 21, 22, 19, "setTimeout"], [29, 31, 22, 29], [30, 4, 23, 2, "cancelTimeout"], [30, 17, 23, 15], [30, 19, 23, 17, "clearTimeout"], [30, 31, 23, 29], [31, 4, 24, 2, "noTimeout"], [31, 13, 24, 11], [31, 15, 24, 13], [31, 16, 24, 14], [31, 17, 24, 15], [32, 4, 25, 2, "getRootHostContext"], [32, 22, 25, 20], [32, 24, 25, 22, "_rootContainerInstance"], [32, 46, 25, 44], [32, 50, 25, 48], [33, 6, 26, 4, "debug"], [33, 11, 26, 9], [33, 12, 26, 10], [33, 32, 26, 30], [33, 33, 26, 31], [34, 6, 27, 4], [34, 13, 27, 11], [34, 14, 27, 12], [34, 15, 27, 13], [35, 4, 28, 2], [35, 5, 28, 3], [36, 4, 29, 2, "getChildHostContext"], [36, 23, 29, 21, "getChildHostContext"], [36, 24, 29, 22, "_parentHostContext"], [36, 42, 29, 40], [36, 44, 29, 42, "_type"], [36, 49, 29, 47], [36, 51, 29, 49, "_rootContainerInstance"], [36, 73, 29, 71], [36, 75, 29, 73], [37, 6, 30, 4, "debug"], [37, 11, 30, 9], [37, 12, 30, 10], [37, 33, 30, 31], [37, 34, 30, 32], [38, 6, 31, 4], [38, 13, 31, 11], [38, 14, 31, 12], [38, 15, 31, 13], [39, 4, 32, 2], [39, 5, 32, 3], [40, 4, 33, 2, "shouldSetTextContent"], [40, 24, 33, 22, "shouldSetTextContent"], [40, 25, 33, 23, "_type"], [40, 30, 33, 28], [40, 32, 33, 30, "_props"], [40, 38, 33, 36], [40, 40, 33, 38], [41, 6, 34, 4], [41, 13, 34, 11], [41, 18, 34, 16], [42, 4, 35, 2], [42, 5, 35, 3], [43, 4, 36, 2, "createTextInstance"], [43, 22, 36, 20, "createTextInstance"], [43, 23, 36, 21, "_text"], [43, 28, 36, 26], [43, 30, 36, 28, "_rootContainerInstance"], [43, 52, 36, 50], [43, 54, 36, 52, "_hostContext"], [43, 66, 36, 64], [43, 68, 36, 66, "_internalInstanceHandle"], [43, 91, 36, 89], [43, 93, 36, 91], [44, 6, 37, 4, "debug"], [44, 11, 37, 9], [44, 12, 37, 10], [44, 32, 37, 30], [44, 33, 37, 31], [45, 6, 38, 4], [46, 6, 39, 4], [46, 12, 39, 10], [46, 16, 39, 14, "Error"], [46, 21, 39, 19], [46, 22, 39, 20], [46, 56, 39, 54], [46, 57, 39, 55], [47, 4, 40, 2], [47, 5, 40, 3], [48, 4, 41, 2, "createInstance"], [48, 18, 41, 16, "createInstance"], [48, 19, 41, 17, "type"], [48, 23, 41, 21], [48, 25, 41, 23, "props<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [48, 42, 41, 40], [48, 44, 41, 42, "_container"], [48, 54, 41, 52], [48, 56, 41, 54, "_hostContext"], [48, 68, 41, 66], [48, 70, 41, 68, "_internalInstanceHandle"], [48, 93, 41, 91], [48, 95, 41, 93], [49, 6, 42, 4], [50, 6, 43, 4], [50, 12, 43, 10], [51, 8, 44, 6, "children"], [51, 16, 44, 14], [52, 8, 45, 6], [52, 11, 45, 9, "props"], [53, 6, 46, 4], [53, 7, 46, 5], [53, 10, 46, 8, "props<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [53, 27, 46, 25], [54, 6, 47, 4, "debug"], [54, 11, 47, 9], [54, 12, 47, 10], [54, 28, 47, 26], [54, 30, 47, 28, "type"], [54, 34, 47, 32], [54, 35, 47, 33], [55, 6, 48, 4], [55, 12, 48, 10, "instance"], [55, 20, 48, 18], [55, 23, 48, 21], [56, 8, 49, 6, "type"], [56, 12, 49, 10], [57, 8, 50, 6, "props"], [57, 13, 50, 11], [58, 8, 51, 6, "children"], [58, 16, 51, 14], [58, 18, 51, 16], [59, 6, 52, 4], [59, 7, 52, 5], [60, 6, 53, 4], [60, 13, 53, 11, "instance"], [60, 21, 53, 19], [61, 4, 54, 2], [61, 5, 54, 3], [62, 4, 55, 2, "appendInitialChild"], [62, 22, 55, 20, "appendInitialChild"], [62, 23, 55, 21, "parentInstance"], [62, 37, 55, 35], [62, 39, 55, 37, "child"], [62, 44, 55, 42], [62, 46, 55, 44], [63, 6, 56, 4, "parentInstance"], [63, 20, 56, 18], [63, 21, 56, 19, "children"], [63, 29, 56, 27], [63, 30, 56, 28, "push"], [63, 34, 56, 32], [63, 35, 56, 33, "child"], [63, 40, 56, 38], [63, 41, 56, 39], [64, 4, 57, 2], [64, 5, 57, 3], [65, 4, 58, 2, "finalizeInitialChildren"], [65, 27, 58, 25, "finalizeInitialChildren"], [65, 28, 58, 26, "parentInstance"], [65, 42, 58, 40], [65, 44, 58, 42, "_type"], [65, 49, 58, 47], [65, 51, 58, 49, "_props"], [65, 57, 58, 55], [65, 59, 58, 57, "_rootContainerInstance"], [65, 81, 58, 79], [65, 83, 58, 81, "_hostContext"], [65, 95, 58, 93], [65, 97, 58, 95], [66, 6, 59, 4, "debug"], [66, 11, 59, 9], [66, 12, 59, 10], [66, 37, 59, 35], [66, 39, 59, 37, "parentInstance"], [66, 53, 59, 51], [66, 54, 59, 52], [67, 6, 60, 4], [67, 13, 60, 11], [67, 18, 60, 16], [68, 4, 61, 2], [68, 5, 61, 3], [69, 4, 62, 2, "commitMount"], [69, 15, 62, 13, "commitMount"], [69, 16, 62, 13], [69, 18, 62, 16], [70, 6, 63, 4], [71, 6, 64, 4, "debug"], [71, 11, 64, 9], [71, 12, 64, 10], [71, 25, 64, 23], [71, 26, 64, 24], [72, 4, 65, 2], [72, 5, 65, 3], [73, 4, 66, 2, "prepareForCommit"], [73, 20, 66, 18, "prepareForCommit"], [73, 21, 66, 19, "_container"], [73, 31, 66, 29], [73, 33, 66, 31], [74, 6, 67, 4, "debug"], [74, 11, 67, 9], [74, 12, 67, 10], [74, 30, 67, 28], [74, 31, 67, 29], [75, 6, 68, 4], [75, 13, 68, 11], [75, 17, 68, 15], [76, 4, 69, 2], [76, 5, 69, 3], [77, 4, 70, 2, "resetAfterCommit"], [77, 20, 70, 18, "resetAfterCommit"], [77, 21, 70, 19, "container"], [77, 30, 70, 28], [77, 32, 70, 30], [78, 6, 71, 4, "debug"], [78, 11, 71, 9], [78, 12, 71, 10], [78, 30, 71, 28], [78, 31, 71, 29], [79, 6, 72, 4, "container"], [79, 15, 72, 13], [79, 16, 72, 14, "redraw"], [79, 22, 72, 20], [79, 23, 72, 21], [79, 24, 72, 22], [80, 4, 73, 2], [80, 5, 73, 3], [81, 4, 74, 2, "getPublicInstance"], [81, 21, 74, 19, "getPublicInstance"], [81, 22, 74, 20, "node"], [81, 26, 74, 24], [81, 28, 74, 26], [82, 6, 75, 4, "debug"], [82, 11, 75, 9], [82, 12, 75, 10], [82, 31, 75, 29], [82, 32, 75, 30], [83, 6, 76, 4], [83, 13, 76, 11, "node"], [83, 17, 76, 15], [84, 4, 77, 2], [84, 5, 77, 3], [85, 4, 78, 2, "commitTextUpdate"], [85, 20, 78, 18], [85, 22, 78, 20, "commitTextUpdate"], [85, 23, 78, 21, "_textInstance"], [85, 36, 78, 34], [85, 38, 78, 36, "_oldText"], [85, 46, 78, 44], [85, 48, 78, 46, "_newText"], [85, 56, 78, 54], [85, 61, 78, 59], [86, 6, 79, 4], [87, 4, 79, 4], [87, 5, 80, 3], [88, 4, 81, 2, "clearContainer"], [88, 18, 81, 16], [88, 20, 81, 18, "_container"], [88, 30, 81, 28], [88, 34, 81, 32], [89, 6, 82, 4, "debug"], [89, 11, 82, 9], [89, 12, 82, 10], [89, 28, 82, 26], [89, 29, 82, 27], [90, 4, 83, 2], [90, 5, 83, 3], [91, 4, 84, 2, "prepareUpdate"], [91, 17, 84, 15, "prepareUpdate"], [91, 18, 84, 16, "_instance"], [91, 27, 84, 25], [91, 29, 84, 27, "_type"], [91, 34, 84, 32], [91, 36, 84, 34, "oldProps"], [91, 44, 84, 42], [91, 46, 84, 44, "newProps"], [91, 54, 84, 52], [91, 56, 84, 54, "container"], [91, 65, 84, 63], [91, 67, 84, 65, "_hostContext"], [91, 79, 84, 77], [91, 81, 84, 79], [92, 6, 85, 4, "debug"], [92, 11, 85, 9], [92, 12, 85, 10], [92, 27, 85, 25], [92, 28, 85, 26], [93, 6, 86, 4], [93, 12, 86, 10, "propsAreEqual"], [93, 25, 86, 23], [93, 28, 86, 26], [93, 32, 86, 26, "shallowEq"], [93, 52, 86, 35], [93, 54, 86, 36, "oldProps"], [93, 62, 86, 44], [93, 64, 86, 46, "newProps"], [93, 72, 86, 54], [93, 73, 86, 55], [94, 6, 87, 4], [94, 10, 87, 8, "propsAreEqual"], [94, 23, 87, 21], [94, 25, 87, 23], [95, 8, 88, 6], [95, 15, 88, 13], [95, 19, 88, 17], [96, 6, 89, 4], [97, 6, 90, 4], [97, 13, 90, 11, "container"], [97, 22, 90, 20], [98, 4, 91, 2], [98, 5, 91, 3], [99, 4, 92, 2, "preparePortalMount"], [99, 22, 92, 20], [99, 24, 92, 22, "preparePortalMount"], [99, 25, 92, 22], [99, 30, 92, 28], [100, 6, 93, 4, "debug"], [100, 11, 93, 9], [100, 12, 93, 10], [100, 32, 93, 30], [100, 33, 93, 31], [101, 4, 94, 2], [101, 5, 94, 3], [102, 4, 95, 2, "cloneInstance"], [102, 17, 95, 15, "cloneInstance"], [102, 18, 95, 16, "instance"], [102, 26, 95, 24], [102, 28, 95, 26, "_type"], [102, 33, 95, 31], [102, 35, 95, 33, "_oldProps"], [102, 44, 95, 42], [102, 46, 95, 44, "newProps"], [102, 54, 95, 52], [102, 56, 95, 54, "_updatePayload"], [102, 70, 95, 68], [102, 72, 95, 70, "_internalInstanceHandle"], [102, 95, 95, 93], [102, 97, 95, 95, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [102, 109, 95, 107], [102, 111, 95, 109, "_recyclableInstance"], [102, 130, 95, 128], [102, 132, 95, 130], [103, 6, 96, 4, "debug"], [103, 11, 96, 9], [103, 12, 96, 10], [103, 27, 96, 25], [103, 28, 96, 26], [104, 6, 97, 4], [104, 13, 97, 11], [105, 8, 98, 6, "type"], [105, 12, 98, 10], [105, 14, 98, 12, "instance"], [105, 22, 98, 20], [105, 23, 98, 21, "type"], [105, 27, 98, 25], [106, 8, 99, 6, "props"], [106, 13, 99, 11], [106, 15, 99, 13], [107, 10, 100, 8], [107, 13, 100, 11, "newProps"], [108, 8, 101, 6], [108, 9, 101, 7], [109, 8, 102, 6, "children"], [109, 16, 102, 14], [109, 18, 102, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [109, 30, 102, 28], [109, 33, 102, 31], [109, 34, 102, 32], [109, 37, 102, 35, "instance"], [109, 45, 102, 43], [109, 46, 102, 44, "children"], [109, 54, 102, 52], [109, 55, 102, 53], [109, 58, 102, 56], [110, 6, 103, 4], [110, 7, 103, 5], [111, 4, 104, 2], [111, 5, 104, 3], [112, 4, 105, 2, "createContainerChildSet"], [112, 27, 105, 25, "createContainerChildSet"], [112, 28, 105, 25], [112, 30, 105, 28], [113, 6, 106, 4, "debug"], [113, 11, 106, 9], [113, 12, 106, 10], [113, 37, 106, 35], [113, 38, 106, 36], [114, 6, 107, 4], [114, 13, 107, 11], [114, 15, 107, 13], [115, 4, 108, 2], [115, 5, 108, 3], [116, 4, 109, 2, "appendChildToContainerChildSet"], [116, 34, 109, 32, "appendChildToContainerChildSet"], [116, 35, 109, 33, "childSet"], [116, 43, 109, 41], [116, 45, 109, 43, "child"], [116, 50, 109, 48], [116, 52, 109, 50], [117, 6, 110, 4, "childSet"], [117, 14, 110, 12], [117, 15, 110, 13, "push"], [117, 19, 110, 17], [117, 20, 110, 18, "child"], [117, 25, 110, 23], [117, 26, 110, 24], [118, 4, 111, 2], [118, 5, 111, 3], [119, 4, 112, 2, "finalize<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [119, 29, 112, 27, "finalize<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [119, 30, 112, 28, "container"], [119, 39, 112, 37], [119, 41, 112, 39, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [119, 52, 112, 50], [119, 54, 112, 52], [120, 6, 113, 4, "debug"], [120, 11, 113, 9], [120, 12, 113, 10], [120, 39, 113, 37], [120, 40, 113, 38], [121, 6, 114, 4, "container"], [121, 15, 114, 13], [121, 16, 114, 14, "root"], [121, 20, 114, 18], [121, 23, 114, 21, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [121, 34, 114, 32], [122, 4, 115, 2], [122, 5, 115, 3], [123, 4, 116, 2, "replaceC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [123, 28, 116, 26, "replaceC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [123, 29, 116, 27, "container"], [123, 38, 116, 36], [123, 40, 116, 38, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [123, 51, 116, 49], [123, 53, 116, 51], [124, 6, 117, 4, "container"], [124, 15, 117, 13], [124, 16, 117, 14, "root"], [124, 20, 117, 18], [124, 23, 117, 21, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [124, 34, 117, 32], [125, 4, 118, 2], [125, 5, 118, 3], [126, 4, 119, 2, "cloneHiddenInstance"], [126, 23, 119, 21, "cloneHiddenInstance"], [126, 24, 119, 22, "_instance"], [126, 33, 119, 31], [126, 35, 119, 33, "_type"], [126, 40, 119, 38], [126, 42, 119, 40, "_props"], [126, 48, 119, 46], [126, 50, 119, 48], [127, 6, 120, 4, "debug"], [127, 11, 120, 9], [127, 12, 120, 10], [127, 33, 120, 31], [127, 34, 120, 32], [128, 6, 121, 4], [128, 12, 121, 10], [128, 16, 121, 14, "Error"], [128, 21, 121, 19], [128, 22, 121, 20], [128, 44, 121, 42], [128, 45, 121, 43], [129, 4, 122, 2], [129, 5, 122, 3], [130, 4, 123, 2, "cloneHiddenTextInstance"], [130, 27, 123, 25, "cloneHiddenTextInstance"], [130, 28, 123, 26, "_instance"], [130, 37, 123, 35], [130, 39, 123, 37, "_text"], [130, 44, 123, 42], [130, 46, 123, 44], [131, 6, 124, 4, "debug"], [131, 11, 124, 9], [131, 12, 124, 10], [131, 37, 124, 35], [131, 38, 124, 36], [132, 6, 125, 4], [132, 12, 125, 10], [132, 16, 125, 14, "Error"], [132, 21, 125, 19], [132, 22, 125, 20], [132, 44, 125, 42], [132, 45, 125, 43], [133, 4, 126, 2], [133, 5, 126, 3], [134, 4, 127, 2], [135, 4, 128, 2, "getCurrentEventPriority"], [135, 27, 128, 25], [135, 29, 128, 27, "getCurrentEventPriority"], [135, 30, 128, 27], [135, 35, 128, 33, "DefaultEventPriority"], [135, 66, 128, 53], [136, 4, 129, 2, "beforeActiveInstanceBlur"], [136, 28, 129, 26], [136, 30, 129, 28, "beforeActiveInstanceBlur"], [136, 31, 129, 28], [136, 36, 129, 34], [136, 37, 129, 35], [136, 38, 129, 36], [137, 4, 130, 2, "afterActiveInstanceBlur"], [137, 27, 130, 25], [137, 29, 130, 27, "afterActiveInstanceBlur"], [137, 30, 130, 27], [137, 35, 130, 33], [137, 36, 130, 34], [137, 37, 130, 35], [138, 4, 131, 2, "detachDeletedInstance"], [138, 25, 131, 23], [138, 27, 131, 25, "_node"], [138, 32, 131, 30], [138, 36, 131, 34], [138, 37, 131, 35], [138, 38, 131, 36], [139, 4, 132, 2, "getInstanceFromNode"], [139, 23, 132, 21], [139, 25, 132, 23], [139, 34, 132, 23, "getInstanceFromNode"], [139, 35, 132, 33, "_node"], [139, 40, 132, 38], [139, 42, 132, 40], [140, 6, 133, 4], [140, 12, 133, 10], [140, 16, 133, 14, "Error"], [140, 21, 133, 19], [140, 22, 133, 20], [140, 49, 133, 47], [140, 50, 133, 48], [141, 4, 134, 2], [141, 5, 134, 3], [142, 4, 135, 2, "prepareScopeUpdate"], [142, 22, 135, 20], [142, 24, 135, 22], [142, 33, 135, 22, "prepareScopeUpdate"], [142, 34, 135, 32, "_scopeInstance"], [142, 48, 135, 46], [142, 50, 135, 48, "_instance"], [142, 59, 135, 57], [142, 61, 135, 59], [143, 6, 136, 4], [143, 12, 136, 10], [143, 16, 136, 14, "Error"], [143, 21, 136, 19], [143, 22, 136, 20], [143, 49, 136, 47], [143, 50, 136, 48], [144, 4, 137, 2], [144, 5, 137, 3], [145, 4, 138, 2, "getInstanceFromScope"], [145, 24, 138, 22], [145, 26, 138, 24], [145, 35, 138, 24, "getInstanceFromScope"], [145, 36, 138, 34, "_scopeInstance"], [145, 50, 138, 48], [145, 52, 138, 50], [146, 6, 139, 4], [146, 12, 139, 10], [146, 16, 139, 14, "Error"], [146, 21, 139, 19], [146, 22, 139, 20], [146, 49, 139, 47], [146, 50, 139, 48], [147, 4, 140, 2], [147, 5, 140, 3], [148, 4, 141, 2], [149, 4, 142, 2], [150, 4, 143, 2, "shouldAttemptEagerTransition"], [150, 32, 143, 30], [150, 34, 143, 32, "shouldAttemptEagerTransition"], [150, 35, 143, 32], [150, 40, 143, 38], [150, 45, 143, 43], [151, 4, 144, 2, "trackSchedulerEvent"], [151, 23, 144, 21], [151, 25, 144, 23, "trackSchedulerEvent"], [151, 26, 144, 23], [151, 31, 144, 29], [151, 32, 144, 30], [151, 33, 144, 31], [152, 4, 145, 2, "resolveEventType"], [152, 20, 145, 18], [152, 22, 145, 20, "resolveEventType"], [152, 23, 145, 20], [152, 28, 145, 26], [152, 32, 145, 30], [153, 4, 146, 2, "resolveEventTimeStamp"], [153, 25, 146, 23], [153, 27, 146, 25, "resolveEventTimeStamp"], [153, 28, 146, 25], [153, 33, 146, 31], [153, 34, 146, 32], [153, 37, 146, 35], [154, 4, 147, 2, "requestPostPaintCallback"], [154, 28, 147, 26, "requestPostPaintCallback"], [154, 29, 147, 26], [154, 31, 147, 29], [154, 32, 147, 30], [154, 33, 147, 31], [155, 4, 148, 2, "maySuspendCommit"], [155, 20, 148, 18], [155, 22, 148, 20, "maySuspendCommit"], [155, 23, 148, 20], [155, 28, 148, 26], [155, 33, 148, 31], [156, 4, 149, 2, "preloadInstance"], [156, 19, 149, 17], [156, 21, 149, 19, "preloadInstance"], [156, 22, 149, 19], [156, 27, 149, 25], [156, 31, 149, 29], [157, 4, 150, 2], [158, 4, 151, 2, "startSuspendingCommit"], [158, 25, 151, 23, "startSuspendingCommit"], [158, 26, 151, 23], [158, 28, 151, 26], [158, 29, 151, 27], [158, 30, 151, 28], [159, 4, 152, 2, "suspendInstance"], [159, 19, 152, 17, "suspendInstance"], [159, 20, 152, 17], [159, 22, 152, 20], [159, 23, 152, 21], [159, 24, 152, 22], [160, 4, 153, 2, "waitForCommitToBeReady"], [160, 26, 153, 24], [160, 28, 153, 26, "waitForCommitToBeReady"], [160, 29, 153, 26], [160, 34, 153, 32], [160, 38, 153, 36], [161, 4, 154, 2, "NotPendingTransition"], [161, 24, 154, 22], [161, 26, 154, 24], [161, 30, 154, 28], [162, 4, 155, 2, "HostTransitionContext"], [162, 25, 155, 23], [162, 27, 155, 25], [162, 40, 155, 38], [162, 44, 155, 38, "createContext"], [162, 64, 155, 51], [162, 66, 155, 52], [162, 70, 155, 56], [162, 71, 155, 57], [163, 4, 156, 2, "setCurrentUpdatePriority"], [163, 28, 156, 26, "setCurrentUpdatePriority"], [163, 29, 156, 27, "newPriority"], [163, 40, 156, 38], [163, 42, 156, 40], [164, 6, 157, 4, "currentUpdatePriority"], [164, 27, 157, 25], [164, 30, 157, 28, "newPriority"], [164, 41, 157, 39], [165, 4, 158, 2], [165, 5, 158, 3], [166, 4, 159, 2, "getCurrentUpdatePriority"], [166, 28, 159, 26, "getCurrentUpdatePriority"], [166, 29, 159, 26], [166, 31, 159, 29], [167, 6, 160, 4], [167, 13, 160, 11, "currentUpdatePriority"], [167, 34, 160, 32], [168, 4, 161, 2], [168, 5, 161, 3], [169, 4, 162, 2, "resolveUpdatePriority"], [169, 25, 162, 23, "resolveUpdatePriority"], [169, 26, 162, 23], [169, 28, 162, 26], [170, 6, 163, 4], [170, 10, 163, 8, "currentUpdatePriority"], [170, 31, 163, 29], [170, 36, 163, 34, "NoEventPriority"], [170, 51, 163, 49], [170, 53, 163, 51], [171, 8, 164, 6], [171, 15, 164, 13, "currentUpdatePriority"], [171, 36, 164, 34], [172, 6, 165, 4], [173, 6, 166, 4], [173, 13, 166, 11, "DefaultEventPriority"], [173, 44, 166, 31], [174, 4, 167, 2], [174, 5, 167, 3], [175, 4, 168, 2, "resetFormInstance"], [175, 21, 168, 19, "resetFormInstance"], [175, 22, 168, 19], [175, 24, 168, 22], [175, 25, 168, 23], [176, 2, 169, 0], [176, 3, 169, 1], [177, 0, 169, 2], [177, 3]], "functionMap": {"names": ["<global>", "debug", "sksgHostConfig.getRootHostContext", "sksgHostConfig.getChildHostContext", "sksgHostConfig.shouldSetTextContent", "sksgHostConfig.createTextInstance", "sksgHostConfig.createInstance", "sksgHostConfig.appendInitialChild", "sksgHostConfig.finalizeInitialChildren", "sksgHostConfig.commitMount", "sksgHostConfig.prepareForCommit", "sksgHostConfig.resetAfterCommit", "sksgHostConfig.getPublicInstance", "sksgHostConfig.commitTextUpdate", "sksgHostConfig.clearContainer", "sksgHostConfig.prepareUpdate", "sksgHostConfig.preparePortalMount", "sksgHostConfig.cloneInstance", "sksgHostConfig.createContainerChildSet", "sksgHostConfig.appendChildToContainerChildSet", "sksgHostConfig.finalizeContainerChildren", "sksgHostConfig.replaceContainerChildren", "sksgHostConfig.cloneHiddenInstance", "sksgHostConfig.cloneHiddenTextInstance", "sksgHostConfig.getCurrentEventPriority", "sksgHostConfig.beforeActiveInstanceBlur", "sksgHostConfig.afterActiveInstanceBlur", "sksgHostConfig.detachDeletedInstance", "sksgHostConfig.getInstanceFromNode", "sksgHostConfig.prepareScopeUpdate", "sksgHostConfig.getInstanceFromScope", "sksgHostConfig.shouldAttemptEagerTransition", "sksgHostConfig.trackSchedulerEvent", "sksgHostConfig.resolveEventType", "sksgHostConfig.resolveEventTimeStamp", "sksgHostConfig.requestPostPaintCallback", "sksgHostConfig.maySuspendCommit", "sksgHostConfig.preloadInstance", "sksgHostConfig.startSuspendingCommit", "sksgHostConfig.suspendInstance", "sksgHostConfig.waitForCommitToBeReady", "sksgHostConfig.setCurrentUpdatePriority", "sksgHostConfig.getCurrentUpdatePriority", "sksgHostConfig.resolveUpdatePriority", "sksgHostConfig.resetFormInstance"], "mappings": "AAA;qBCM;CDI;sBEc;GFG;EGC;GHG;EIC;GJE;EKC;GLI;EMC;GNa;EOC;GPE;EQC;GRG;ESC;GTG;EUC;GVG;EWC;GXG;EYC;GZG;oBaC;GbE;kBcC;GdE;EeC;GfO;sBgBC;GhBE;EiBC;GjBS;EkBC;GlBG;EmBC;GnBE;EoBC;GpBG;EqBC;GrBE;EsBC;GtBG;EuBC;GvBG;2BwBE,0BxB;4ByBC,QzB;2B0BC,Q1B;yB2BC,W3B;uB4BC;G5BE;sB6BC;G7BE;wB8BC;G9BE;gC+BG,W/B;uBgCC,QhC;oBiCC,UjC;yBkCC,UlC;EmCC,6BnC;oBoCC,WpC;mBqCC,UrC;EsCE,0BtC;EuCC,oBvC;0BwCC,UxC;EyCG;GzCE;E0CC;G1CE;E2CC;G3CK;E4CC,sB5C"}}, "type": "js/module"}]}