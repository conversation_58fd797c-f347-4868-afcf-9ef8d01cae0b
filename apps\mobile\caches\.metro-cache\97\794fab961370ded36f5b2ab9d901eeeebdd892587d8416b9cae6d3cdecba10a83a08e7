{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {\n          original: {\n            x,\n            y,\n            width,\n            height\n          },\n          canvas: {\n            width: canvasWidth,\n            height: canvasHeight\n          },\n          clamped: {\n            x: clampedX,\n            y: clampedY,\n            width: clampedWidth,\n            height: clampedHeight\n          }\n        });\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      console.log('[EchoCameraWeb] 🚀 ENTRY: Starting face blur processing system...');\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {\n              bbox,\n              imageSize: {\n                width: img.width,\n                height: img.height\n              }\n            });\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {\n              faceX,\n              faceY,\n              faceWidth,\n              faceHeight,\n              isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0\n            });\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] 🚨 CRITICAL ERROR in processImageWithFaceBlur:', error);\n        console.error('[EchoCameraWeb] 🚨 Error stack:', error.stack);\n        console.error('[EchoCameraWeb] 🚨 Error details:', {\n          name: error.name,\n          message: error.message,\n          photoUri\n        });\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 907,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 905,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 917,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 915,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 914,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 972,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 973,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1029,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1041,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1040,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1067,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 938,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1095,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1085,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1080,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1124,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1132,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1121,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1116,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 936,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1751, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 73, 383, 71], [357, 75, 383, 73], [358, 10, 384, 8, "original"], [358, 18, 384, 16], [358, 20, 384, 18], [359, 12, 384, 20, "x"], [359, 13, 384, 21], [360, 12, 384, 23, "y"], [360, 13, 384, 24], [361, 12, 384, 26, "width"], [361, 17, 384, 31], [362, 12, 384, 33, "height"], [363, 10, 384, 40], [363, 11, 384, 41], [364, 10, 385, 8, "canvas"], [364, 16, 385, 14], [364, 18, 385, 16], [365, 12, 385, 18, "width"], [365, 17, 385, 23], [365, 19, 385, 25, "canvasWidth"], [365, 30, 385, 36], [366, 12, 385, 38, "height"], [366, 18, 385, 44], [366, 20, 385, 46, "canvasHeight"], [367, 10, 385, 59], [367, 11, 385, 60], [368, 10, 386, 8, "clamped"], [368, 17, 386, 15], [368, 19, 386, 17], [369, 12, 386, 19, "x"], [369, 13, 386, 20], [369, 15, 386, 22, "clampedX"], [369, 23, 386, 30], [370, 12, 386, 32, "y"], [370, 13, 386, 33], [370, 15, 386, 35, "clampedY"], [370, 23, 386, 43], [371, 12, 386, 45, "width"], [371, 17, 386, 50], [371, 19, 386, 52, "<PERSON><PERSON><PERSON><PERSON>"], [371, 31, 386, 64], [372, 12, 386, 66, "height"], [372, 18, 386, 72], [372, 20, 386, 74, "clampedHeight"], [373, 10, 386, 88], [374, 8, 387, 6], [374, 9, 387, 7], [374, 10, 387, 8], [375, 8, 388, 6], [376, 6, 389, 4], [378, 6, 391, 4], [379, 6, 392, 4], [379, 12, 392, 10, "imageData"], [379, 21, 392, 19], [379, 24, 392, 22, "ctx"], [379, 27, 392, 25], [379, 28, 392, 26, "getImageData"], [379, 40, 392, 38], [379, 41, 392, 39, "clampedX"], [379, 49, 392, 47], [379, 51, 392, 49, "clampedY"], [379, 59, 392, 57], [379, 61, 392, 59, "<PERSON><PERSON><PERSON><PERSON>"], [379, 73, 392, 71], [379, 75, 392, 73, "clampedHeight"], [379, 88, 392, 86], [379, 89, 392, 87], [380, 6, 393, 4], [380, 12, 393, 10, "data"], [380, 16, 393, 14], [380, 19, 393, 17, "imageData"], [380, 28, 393, 26], [380, 29, 393, 27, "data"], [380, 33, 393, 31], [382, 6, 395, 4], [383, 6, 396, 4], [383, 12, 396, 10, "pixelSize"], [383, 21, 396, 19], [383, 24, 396, 22, "Math"], [383, 28, 396, 26], [383, 29, 396, 27, "max"], [383, 32, 396, 30], [383, 33, 396, 31], [383, 35, 396, 33], [383, 37, 396, 35, "Math"], [383, 41, 396, 39], [383, 42, 396, 40, "min"], [383, 45, 396, 43], [383, 46, 396, 44, "<PERSON><PERSON><PERSON><PERSON>"], [383, 58, 396, 56], [383, 60, 396, 58, "clampedHeight"], [383, 73, 396, 71], [383, 74, 396, 72], [383, 77, 396, 75], [383, 78, 396, 76], [383, 79, 396, 77], [384, 6, 398, 4], [384, 11, 398, 9], [384, 15, 398, 13, "py"], [384, 17, 398, 15], [384, 20, 398, 18], [384, 21, 398, 19], [384, 23, 398, 21, "py"], [384, 25, 398, 23], [384, 28, 398, 26, "clampedHeight"], [384, 41, 398, 39], [384, 43, 398, 41, "py"], [384, 45, 398, 43], [384, 49, 398, 47, "pixelSize"], [384, 58, 398, 56], [384, 60, 398, 58], [385, 8, 399, 6], [385, 13, 399, 11], [385, 17, 399, 15, "px"], [385, 19, 399, 17], [385, 22, 399, 20], [385, 23, 399, 21], [385, 25, 399, 23, "px"], [385, 27, 399, 25], [385, 30, 399, 28, "<PERSON><PERSON><PERSON><PERSON>"], [385, 42, 399, 40], [385, 44, 399, 42, "px"], [385, 46, 399, 44], [385, 50, 399, 48, "pixelSize"], [385, 59, 399, 57], [385, 61, 399, 59], [386, 10, 400, 8], [387, 10, 401, 8], [387, 14, 401, 12, "r"], [387, 15, 401, 13], [387, 18, 401, 16], [387, 19, 401, 17], [388, 12, 401, 19, "g"], [388, 13, 401, 20], [388, 16, 401, 23], [388, 17, 401, 24], [389, 12, 401, 26, "b"], [389, 13, 401, 27], [389, 16, 401, 30], [389, 17, 401, 31], [390, 12, 401, 33, "count"], [390, 17, 401, 38], [390, 20, 401, 41], [390, 21, 401, 42], [391, 10, 403, 8], [391, 15, 403, 13], [391, 19, 403, 17, "dy"], [391, 21, 403, 19], [391, 24, 403, 22], [391, 25, 403, 23], [391, 27, 403, 25, "dy"], [391, 29, 403, 27], [391, 32, 403, 30, "pixelSize"], [391, 41, 403, 39], [391, 45, 403, 43, "py"], [391, 47, 403, 45], [391, 50, 403, 48, "dy"], [391, 52, 403, 50], [391, 55, 403, 53, "clampedHeight"], [391, 68, 403, 66], [391, 70, 403, 68, "dy"], [391, 72, 403, 70], [391, 74, 403, 72], [391, 76, 403, 74], [392, 12, 404, 10], [392, 17, 404, 15], [392, 21, 404, 19, "dx"], [392, 23, 404, 21], [392, 26, 404, 24], [392, 27, 404, 25], [392, 29, 404, 27, "dx"], [392, 31, 404, 29], [392, 34, 404, 32, "pixelSize"], [392, 43, 404, 41], [392, 47, 404, 45, "px"], [392, 49, 404, 47], [392, 52, 404, 50, "dx"], [392, 54, 404, 52], [392, 57, 404, 55, "<PERSON><PERSON><PERSON><PERSON>"], [392, 69, 404, 67], [392, 71, 404, 69, "dx"], [392, 73, 404, 71], [392, 75, 404, 73], [392, 77, 404, 75], [393, 14, 405, 12], [393, 20, 405, 18, "index"], [393, 25, 405, 23], [393, 28, 405, 26], [393, 29, 405, 27], [393, 30, 405, 28, "py"], [393, 32, 405, 30], [393, 35, 405, 33, "dy"], [393, 37, 405, 35], [393, 41, 405, 39, "<PERSON><PERSON><PERSON><PERSON>"], [393, 53, 405, 51], [393, 57, 405, 55, "px"], [393, 59, 405, 57], [393, 62, 405, 60, "dx"], [393, 64, 405, 62], [393, 65, 405, 63], [393, 69, 405, 67], [393, 70, 405, 68], [394, 14, 406, 12, "r"], [394, 15, 406, 13], [394, 19, 406, 17, "data"], [394, 23, 406, 21], [394, 24, 406, 22, "index"], [394, 29, 406, 27], [394, 30, 406, 28], [395, 14, 407, 12, "g"], [395, 15, 407, 13], [395, 19, 407, 17, "data"], [395, 23, 407, 21], [395, 24, 407, 22, "index"], [395, 29, 407, 27], [395, 32, 407, 30], [395, 33, 407, 31], [395, 34, 407, 32], [396, 14, 408, 12, "b"], [396, 15, 408, 13], [396, 19, 408, 17, "data"], [396, 23, 408, 21], [396, 24, 408, 22, "index"], [396, 29, 408, 27], [396, 32, 408, 30], [396, 33, 408, 31], [396, 34, 408, 32], [397, 14, 409, 12, "count"], [397, 19, 409, 17], [397, 21, 409, 19], [398, 12, 410, 10], [399, 10, 411, 8], [400, 10, 413, 8], [400, 14, 413, 12, "count"], [400, 19, 413, 17], [400, 22, 413, 20], [400, 23, 413, 21], [400, 25, 413, 23], [401, 12, 414, 10, "r"], [401, 13, 414, 11], [401, 16, 414, 14, "Math"], [401, 20, 414, 18], [401, 21, 414, 19, "floor"], [401, 26, 414, 24], [401, 27, 414, 25, "r"], [401, 28, 414, 26], [401, 31, 414, 29, "count"], [401, 36, 414, 34], [401, 37, 414, 35], [402, 12, 415, 10, "g"], [402, 13, 415, 11], [402, 16, 415, 14, "Math"], [402, 20, 415, 18], [402, 21, 415, 19, "floor"], [402, 26, 415, 24], [402, 27, 415, 25, "g"], [402, 28, 415, 26], [402, 31, 415, 29, "count"], [402, 36, 415, 34], [402, 37, 415, 35], [403, 12, 416, 10, "b"], [403, 13, 416, 11], [403, 16, 416, 14, "Math"], [403, 20, 416, 18], [403, 21, 416, 19, "floor"], [403, 26, 416, 24], [403, 27, 416, 25, "b"], [403, 28, 416, 26], [403, 31, 416, 29, "count"], [403, 36, 416, 34], [403, 37, 416, 35], [405, 12, 418, 10], [406, 12, 419, 10], [406, 17, 419, 15], [406, 21, 419, 19, "dy"], [406, 23, 419, 21], [406, 26, 419, 24], [406, 27, 419, 25], [406, 29, 419, 27, "dy"], [406, 31, 419, 29], [406, 34, 419, 32, "pixelSize"], [406, 43, 419, 41], [406, 47, 419, 45, "py"], [406, 49, 419, 47], [406, 52, 419, 50, "dy"], [406, 54, 419, 52], [406, 57, 419, 55, "clampedHeight"], [406, 70, 419, 68], [406, 72, 419, 70, "dy"], [406, 74, 419, 72], [406, 76, 419, 74], [406, 78, 419, 76], [407, 14, 420, 12], [407, 19, 420, 17], [407, 23, 420, 21, "dx"], [407, 25, 420, 23], [407, 28, 420, 26], [407, 29, 420, 27], [407, 31, 420, 29, "dx"], [407, 33, 420, 31], [407, 36, 420, 34, "pixelSize"], [407, 45, 420, 43], [407, 49, 420, 47, "px"], [407, 51, 420, 49], [407, 54, 420, 52, "dx"], [407, 56, 420, 54], [407, 59, 420, 57, "<PERSON><PERSON><PERSON><PERSON>"], [407, 71, 420, 69], [407, 73, 420, 71, "dx"], [407, 75, 420, 73], [407, 77, 420, 75], [407, 79, 420, 77], [408, 16, 421, 14], [408, 22, 421, 20, "index"], [408, 27, 421, 25], [408, 30, 421, 28], [408, 31, 421, 29], [408, 32, 421, 30, "py"], [408, 34, 421, 32], [408, 37, 421, 35, "dy"], [408, 39, 421, 37], [408, 43, 421, 41, "<PERSON><PERSON><PERSON><PERSON>"], [408, 55, 421, 53], [408, 59, 421, 57, "px"], [408, 61, 421, 59], [408, 64, 421, 62, "dx"], [408, 66, 421, 64], [408, 67, 421, 65], [408, 71, 421, 69], [408, 72, 421, 70], [409, 16, 422, 14, "data"], [409, 20, 422, 18], [409, 21, 422, 19, "index"], [409, 26, 422, 24], [409, 27, 422, 25], [409, 30, 422, 28, "r"], [409, 31, 422, 29], [410, 16, 423, 14, "data"], [410, 20, 423, 18], [410, 21, 423, 19, "index"], [410, 26, 423, 24], [410, 29, 423, 27], [410, 30, 423, 28], [410, 31, 423, 29], [410, 34, 423, 32, "g"], [410, 35, 423, 33], [411, 16, 424, 14, "data"], [411, 20, 424, 18], [411, 21, 424, 19, "index"], [411, 26, 424, 24], [411, 29, 424, 27], [411, 30, 424, 28], [411, 31, 424, 29], [411, 34, 424, 32, "b"], [411, 35, 424, 33], [412, 16, 425, 14], [413, 14, 426, 12], [414, 12, 427, 10], [415, 10, 428, 8], [416, 8, 429, 6], [417, 6, 430, 4], [419, 6, 432, 4], [420, 6, 433, 4], [420, 11, 433, 9], [420, 15, 433, 13, "i"], [420, 16, 433, 14], [420, 19, 433, 17], [420, 20, 433, 18], [420, 22, 433, 20, "i"], [420, 23, 433, 21], [420, 26, 433, 24], [420, 27, 433, 25], [420, 29, 433, 27, "i"], [420, 30, 433, 28], [420, 32, 433, 30], [420, 34, 433, 32], [421, 8, 434, 6, "applySimpleBlur"], [421, 23, 434, 21], [421, 24, 434, 22, "data"], [421, 28, 434, 26], [421, 30, 434, 28, "<PERSON><PERSON><PERSON><PERSON>"], [421, 42, 434, 40], [421, 44, 434, 42, "clampedHeight"], [421, 57, 434, 55], [421, 58, 434, 56], [422, 6, 435, 4], [424, 6, 437, 4], [425, 6, 438, 4, "ctx"], [425, 9, 438, 7], [425, 10, 438, 8, "putImageData"], [425, 22, 438, 20], [425, 23, 438, 21, "imageData"], [425, 32, 438, 30], [425, 34, 438, 32, "clampedX"], [425, 42, 438, 40], [425, 44, 438, 42, "clampedY"], [425, 52, 438, 50], [425, 53, 438, 51], [427, 6, 440, 4], [428, 6, 441, 4, "ctx"], [428, 9, 441, 7], [428, 10, 441, 8, "fillStyle"], [428, 19, 441, 17], [428, 22, 441, 20], [428, 48, 441, 46], [429, 6, 442, 4, "ctx"], [429, 9, 442, 7], [429, 10, 442, 8, "fillRect"], [429, 18, 442, 16], [429, 19, 442, 17, "clampedX"], [429, 27, 442, 25], [429, 29, 442, 27, "clampedY"], [429, 37, 442, 35], [429, 39, 442, 37, "<PERSON><PERSON><PERSON><PERSON>"], [429, 51, 442, 49], [429, 53, 442, 51, "clampedHeight"], [429, 66, 442, 64], [429, 67, 442, 65], [430, 4, 443, 2], [430, 5, 443, 3], [431, 4, 445, 2], [431, 10, 445, 8, "applySimpleBlur"], [431, 25, 445, 23], [431, 28, 445, 26, "applySimpleBlur"], [431, 29, 445, 27, "data"], [431, 33, 445, 50], [431, 35, 445, 52, "width"], [431, 40, 445, 65], [431, 42, 445, 67, "height"], [431, 48, 445, 81], [431, 53, 445, 86], [432, 6, 446, 4], [432, 12, 446, 10, "original"], [432, 20, 446, 18], [432, 23, 446, 21], [432, 27, 446, 25, "Uint8ClampedArray"], [432, 44, 446, 42], [432, 45, 446, 43, "data"], [432, 49, 446, 47], [432, 50, 446, 48], [433, 6, 448, 4], [433, 11, 448, 9], [433, 15, 448, 13, "y"], [433, 16, 448, 14], [433, 19, 448, 17], [433, 20, 448, 18], [433, 22, 448, 20, "y"], [433, 23, 448, 21], [433, 26, 448, 24, "height"], [433, 32, 448, 30], [433, 35, 448, 33], [433, 36, 448, 34], [433, 38, 448, 36, "y"], [433, 39, 448, 37], [433, 41, 448, 39], [433, 43, 448, 41], [434, 8, 449, 6], [434, 13, 449, 11], [434, 17, 449, 15, "x"], [434, 18, 449, 16], [434, 21, 449, 19], [434, 22, 449, 20], [434, 24, 449, 22, "x"], [434, 25, 449, 23], [434, 28, 449, 26, "width"], [434, 33, 449, 31], [434, 36, 449, 34], [434, 37, 449, 35], [434, 39, 449, 37, "x"], [434, 40, 449, 38], [434, 42, 449, 40], [434, 44, 449, 42], [435, 10, 450, 8], [435, 16, 450, 14, "index"], [435, 21, 450, 19], [435, 24, 450, 22], [435, 25, 450, 23, "y"], [435, 26, 450, 24], [435, 29, 450, 27, "width"], [435, 34, 450, 32], [435, 37, 450, 35, "x"], [435, 38, 450, 36], [435, 42, 450, 40], [435, 43, 450, 41], [437, 10, 452, 8], [438, 10, 453, 8], [438, 14, 453, 12, "r"], [438, 15, 453, 13], [438, 18, 453, 16], [438, 19, 453, 17], [439, 12, 453, 19, "g"], [439, 13, 453, 20], [439, 16, 453, 23], [439, 17, 453, 24], [440, 12, 453, 26, "b"], [440, 13, 453, 27], [440, 16, 453, 30], [440, 17, 453, 31], [441, 10, 454, 8], [441, 15, 454, 13], [441, 19, 454, 17, "dy"], [441, 21, 454, 19], [441, 24, 454, 22], [441, 25, 454, 23], [441, 26, 454, 24], [441, 28, 454, 26, "dy"], [441, 30, 454, 28], [441, 34, 454, 32], [441, 35, 454, 33], [441, 37, 454, 35, "dy"], [441, 39, 454, 37], [441, 41, 454, 39], [441, 43, 454, 41], [442, 12, 455, 10], [442, 17, 455, 15], [442, 21, 455, 19, "dx"], [442, 23, 455, 21], [442, 26, 455, 24], [442, 27, 455, 25], [442, 28, 455, 26], [442, 30, 455, 28, "dx"], [442, 32, 455, 30], [442, 36, 455, 34], [442, 37, 455, 35], [442, 39, 455, 37, "dx"], [442, 41, 455, 39], [442, 43, 455, 41], [442, 45, 455, 43], [443, 14, 456, 12], [443, 20, 456, 18, "neighborIndex"], [443, 33, 456, 31], [443, 36, 456, 34], [443, 37, 456, 35], [443, 38, 456, 36, "y"], [443, 39, 456, 37], [443, 42, 456, 40, "dy"], [443, 44, 456, 42], [443, 48, 456, 46, "width"], [443, 53, 456, 51], [443, 57, 456, 55, "x"], [443, 58, 456, 56], [443, 61, 456, 59, "dx"], [443, 63, 456, 61], [443, 64, 456, 62], [443, 68, 456, 66], [443, 69, 456, 67], [444, 14, 457, 12, "r"], [444, 15, 457, 13], [444, 19, 457, 17, "original"], [444, 27, 457, 25], [444, 28, 457, 26, "neighborIndex"], [444, 41, 457, 39], [444, 42, 457, 40], [445, 14, 458, 12, "g"], [445, 15, 458, 13], [445, 19, 458, 17, "original"], [445, 27, 458, 25], [445, 28, 458, 26, "neighborIndex"], [445, 41, 458, 39], [445, 44, 458, 42], [445, 45, 458, 43], [445, 46, 458, 44], [446, 14, 459, 12, "b"], [446, 15, 459, 13], [446, 19, 459, 17, "original"], [446, 27, 459, 25], [446, 28, 459, 26, "neighborIndex"], [446, 41, 459, 39], [446, 44, 459, 42], [446, 45, 459, 43], [446, 46, 459, 44], [447, 12, 460, 10], [448, 10, 461, 8], [449, 10, 463, 8, "data"], [449, 14, 463, 12], [449, 15, 463, 13, "index"], [449, 20, 463, 18], [449, 21, 463, 19], [449, 24, 463, 22, "r"], [449, 25, 463, 23], [449, 28, 463, 26], [449, 29, 463, 27], [450, 10, 464, 8, "data"], [450, 14, 464, 12], [450, 15, 464, 13, "index"], [450, 20, 464, 18], [450, 23, 464, 21], [450, 24, 464, 22], [450, 25, 464, 23], [450, 28, 464, 26, "g"], [450, 29, 464, 27], [450, 32, 464, 30], [450, 33, 464, 31], [451, 10, 465, 8, "data"], [451, 14, 465, 12], [451, 15, 465, 13, "index"], [451, 20, 465, 18], [451, 23, 465, 21], [451, 24, 465, 22], [451, 25, 465, 23], [451, 28, 465, 26, "b"], [451, 29, 465, 27], [451, 32, 465, 30], [451, 33, 465, 31], [452, 8, 466, 6], [453, 6, 467, 4], [454, 4, 468, 2], [454, 5, 468, 3], [455, 4, 470, 2], [455, 10, 470, 8, "applyFallbackFaceBlur"], [455, 31, 470, 29], [455, 34, 470, 32, "applyFallbackFaceBlur"], [455, 35, 470, 33, "ctx"], [455, 38, 470, 62], [455, 40, 470, 64, "imgWidth"], [455, 48, 470, 80], [455, 50, 470, 82, "imgHeight"], [455, 59, 470, 99], [455, 64, 470, 104], [456, 6, 471, 4, "console"], [456, 13, 471, 11], [456, 14, 471, 12, "log"], [456, 17, 471, 15], [456, 18, 471, 16], [456, 90, 471, 88], [456, 91, 471, 89], [458, 6, 473, 4], [459, 6, 474, 4], [459, 12, 474, 10, "areas"], [459, 17, 474, 15], [459, 20, 474, 18], [460, 6, 475, 6], [461, 6, 476, 6], [462, 8, 476, 8, "x"], [462, 9, 476, 9], [462, 11, 476, 11, "imgWidth"], [462, 19, 476, 19], [462, 22, 476, 22], [462, 26, 476, 26], [463, 8, 476, 28, "y"], [463, 9, 476, 29], [463, 11, 476, 31, "imgHeight"], [463, 20, 476, 40], [463, 23, 476, 43], [463, 27, 476, 47], [464, 8, 476, 49, "w"], [464, 9, 476, 50], [464, 11, 476, 52, "imgWidth"], [464, 19, 476, 60], [464, 22, 476, 63], [464, 25, 476, 66], [465, 8, 476, 68, "h"], [465, 9, 476, 69], [465, 11, 476, 71, "imgHeight"], [465, 20, 476, 80], [465, 23, 476, 83], [466, 6, 476, 87], [466, 7, 476, 88], [467, 6, 477, 6], [468, 6, 478, 6], [469, 8, 478, 8, "x"], [469, 9, 478, 9], [469, 11, 478, 11, "imgWidth"], [469, 19, 478, 19], [469, 22, 478, 22], [469, 25, 478, 25], [470, 8, 478, 27, "y"], [470, 9, 478, 28], [470, 11, 478, 30, "imgHeight"], [470, 20, 478, 39], [470, 23, 478, 42], [470, 26, 478, 45], [471, 8, 478, 47, "w"], [471, 9, 478, 48], [471, 11, 478, 50, "imgWidth"], [471, 19, 478, 58], [471, 22, 478, 61], [471, 26, 478, 65], [472, 8, 478, 67, "h"], [472, 9, 478, 68], [472, 11, 478, 70, "imgHeight"], [472, 20, 478, 79], [472, 23, 478, 82], [473, 6, 478, 86], [473, 7, 478, 87], [474, 6, 479, 6], [475, 6, 480, 6], [476, 8, 480, 8, "x"], [476, 9, 480, 9], [476, 11, 480, 11, "imgWidth"], [476, 19, 480, 19], [476, 22, 480, 22], [476, 26, 480, 26], [477, 8, 480, 28, "y"], [477, 9, 480, 29], [477, 11, 480, 31, "imgHeight"], [477, 20, 480, 40], [477, 23, 480, 43], [477, 26, 480, 46], [478, 8, 480, 48, "w"], [478, 9, 480, 49], [478, 11, 480, 51, "imgWidth"], [478, 19, 480, 59], [478, 22, 480, 62], [478, 26, 480, 66], [479, 8, 480, 68, "h"], [479, 9, 480, 69], [479, 11, 480, 71, "imgHeight"], [479, 20, 480, 80], [479, 23, 480, 83], [480, 6, 480, 87], [480, 7, 480, 88], [480, 8, 481, 5], [481, 6, 483, 4, "areas"], [481, 11, 483, 9], [481, 12, 483, 10, "for<PERSON>ach"], [481, 19, 483, 17], [481, 20, 483, 18], [481, 21, 483, 19, "area"], [481, 25, 483, 23], [481, 27, 483, 25, "index"], [481, 32, 483, 30], [481, 37, 483, 35], [482, 8, 484, 6, "console"], [482, 15, 484, 13], [482, 16, 484, 14, "log"], [482, 19, 484, 17], [482, 20, 484, 18], [482, 65, 484, 63, "index"], [482, 70, 484, 68], [482, 73, 484, 71], [482, 74, 484, 72], [482, 77, 484, 75], [482, 79, 484, 77, "area"], [482, 83, 484, 81], [482, 84, 484, 82], [483, 8, 485, 6, "applyStrongBlur"], [483, 23, 485, 21], [483, 24, 485, 22, "ctx"], [483, 27, 485, 25], [483, 29, 485, 27, "area"], [483, 33, 485, 31], [483, 34, 485, 32, "x"], [483, 35, 485, 33], [483, 37, 485, 35, "area"], [483, 41, 485, 39], [483, 42, 485, 40, "y"], [483, 43, 485, 41], [483, 45, 485, 43, "area"], [483, 49, 485, 47], [483, 50, 485, 48, "w"], [483, 51, 485, 49], [483, 53, 485, 51, "area"], [483, 57, 485, 55], [483, 58, 485, 56, "h"], [483, 59, 485, 57], [483, 60, 485, 58], [484, 6, 486, 4], [484, 7, 486, 5], [484, 8, 486, 6], [485, 4, 487, 2], [485, 5, 487, 3], [487, 4, 489, 2], [488, 4, 490, 2], [488, 10, 490, 8, "capturePhoto"], [488, 22, 490, 20], [488, 25, 490, 23], [488, 29, 490, 23, "useCallback"], [488, 47, 490, 34], [488, 49, 490, 35], [488, 61, 490, 47], [489, 6, 491, 4], [490, 6, 492, 4], [490, 12, 492, 10, "isDev"], [490, 17, 492, 15], [490, 20, 492, 18, "process"], [490, 27, 492, 25], [490, 28, 492, 26, "env"], [490, 31, 492, 29], [490, 32, 492, 30, "NODE_ENV"], [490, 40, 492, 38], [490, 45, 492, 43], [490, 58, 492, 56], [490, 62, 492, 60, "__DEV__"], [490, 69, 492, 67], [491, 6, 494, 4], [491, 10, 494, 8], [491, 11, 494, 9, "cameraRef"], [491, 20, 494, 18], [491, 21, 494, 19, "current"], [491, 28, 494, 26], [491, 32, 494, 30], [491, 33, 494, 31, "isDev"], [491, 38, 494, 36], [491, 40, 494, 38], [492, 8, 495, 6, "<PERSON><PERSON>"], [492, 22, 495, 11], [492, 23, 495, 12, "alert"], [492, 28, 495, 17], [492, 29, 495, 18], [492, 36, 495, 25], [492, 38, 495, 27], [492, 56, 495, 45], [492, 57, 495, 46], [493, 8, 496, 6], [494, 6, 497, 4], [495, 6, 498, 4], [495, 10, 498, 8], [496, 8, 499, 6, "setProcessingState"], [496, 26, 499, 24], [496, 27, 499, 25], [496, 38, 499, 36], [496, 39, 499, 37], [497, 8, 500, 6, "setProcessingProgress"], [497, 29, 500, 27], [497, 30, 500, 28], [497, 32, 500, 30], [497, 33, 500, 31], [498, 8, 501, 6], [499, 8, 502, 6], [500, 8, 503, 6], [501, 8, 504, 6], [501, 14, 504, 12], [501, 18, 504, 16, "Promise"], [501, 25, 504, 23], [501, 26, 504, 24, "resolve"], [501, 33, 504, 31], [501, 37, 504, 35, "setTimeout"], [501, 47, 504, 45], [501, 48, 504, 46, "resolve"], [501, 55, 504, 53], [501, 57, 504, 55], [501, 59, 504, 57], [501, 60, 504, 58], [501, 61, 504, 59], [502, 8, 505, 6], [503, 8, 506, 6], [503, 12, 506, 10, "photo"], [503, 17, 506, 15], [504, 8, 508, 6], [504, 12, 508, 10], [505, 10, 509, 8, "photo"], [505, 15, 509, 13], [505, 18, 509, 16], [505, 24, 509, 22, "cameraRef"], [505, 33, 509, 31], [505, 34, 509, 32, "current"], [505, 41, 509, 39], [505, 42, 509, 40, "takePictureAsync"], [505, 58, 509, 56], [505, 59, 509, 57], [506, 12, 510, 10, "quality"], [506, 19, 510, 17], [506, 21, 510, 19], [506, 24, 510, 22], [507, 12, 511, 10, "base64"], [507, 18, 511, 16], [507, 20, 511, 18], [507, 25, 511, 23], [508, 12, 512, 10, "skipProcessing"], [508, 26, 512, 24], [508, 28, 512, 26], [508, 32, 512, 30], [508, 33, 512, 32], [509, 10, 513, 8], [509, 11, 513, 9], [509, 12, 513, 10], [510, 8, 514, 6], [510, 9, 514, 7], [510, 10, 514, 8], [510, 17, 514, 15, "cameraError"], [510, 28, 514, 26], [510, 30, 514, 28], [511, 10, 515, 8, "console"], [511, 17, 515, 15], [511, 18, 515, 16, "log"], [511, 21, 515, 19], [511, 22, 515, 20], [511, 82, 515, 80], [511, 84, 515, 82, "cameraError"], [511, 95, 515, 93], [511, 96, 515, 94], [512, 10, 516, 8], [513, 10, 517, 8], [513, 14, 517, 12, "isDev"], [513, 19, 517, 17], [513, 21, 517, 19], [514, 12, 518, 10, "photo"], [514, 17, 518, 15], [514, 20, 518, 18], [515, 14, 519, 12, "uri"], [515, 17, 519, 15], [515, 19, 519, 17], [516, 12, 520, 10], [516, 13, 520, 11], [517, 10, 521, 8], [517, 11, 521, 9], [517, 17, 521, 15], [518, 12, 522, 10], [518, 18, 522, 16, "cameraError"], [518, 29, 522, 27], [519, 10, 523, 8], [520, 8, 524, 6], [521, 8, 525, 6], [521, 12, 525, 10], [521, 13, 525, 11, "photo"], [521, 18, 525, 16], [521, 20, 525, 18], [522, 10, 526, 8], [522, 16, 526, 14], [522, 20, 526, 18, "Error"], [522, 25, 526, 23], [522, 26, 526, 24], [522, 51, 526, 49], [522, 52, 526, 50], [523, 8, 527, 6], [524, 8, 528, 6, "console"], [524, 15, 528, 13], [524, 16, 528, 14, "log"], [524, 19, 528, 17], [524, 20, 528, 18], [524, 56, 528, 54], [524, 58, 528, 56, "photo"], [524, 63, 528, 61], [524, 64, 528, 62, "uri"], [524, 67, 528, 65], [524, 68, 528, 66], [525, 8, 529, 6, "setCapturedPhoto"], [525, 24, 529, 22], [525, 25, 529, 23, "photo"], [525, 30, 529, 28], [525, 31, 529, 29, "uri"], [525, 34, 529, 32], [525, 35, 529, 33], [526, 8, 530, 6, "setProcessingProgress"], [526, 29, 530, 27], [526, 30, 530, 28], [526, 32, 530, 30], [526, 33, 530, 31], [527, 8, 531, 6], [528, 8, 532, 6, "console"], [528, 15, 532, 13], [528, 16, 532, 14, "log"], [528, 19, 532, 17], [528, 20, 532, 18], [528, 73, 532, 71], [528, 74, 532, 72], [529, 8, 533, 6], [529, 14, 533, 12, "processImageWithFaceBlur"], [529, 38, 533, 36], [529, 39, 533, 37, "photo"], [529, 44, 533, 42], [529, 45, 533, 43, "uri"], [529, 48, 533, 46], [529, 49, 533, 47], [530, 8, 534, 6, "console"], [530, 15, 534, 13], [530, 16, 534, 14, "log"], [530, 19, 534, 17], [530, 20, 534, 18], [530, 71, 534, 69], [530, 72, 534, 70], [531, 6, 535, 4], [531, 7, 535, 5], [531, 8, 535, 6], [531, 15, 535, 13, "error"], [531, 20, 535, 18], [531, 22, 535, 20], [532, 8, 536, 6, "console"], [532, 15, 536, 13], [532, 16, 536, 14, "error"], [532, 21, 536, 19], [532, 22, 536, 20], [532, 54, 536, 52], [532, 56, 536, 54, "error"], [532, 61, 536, 59], [532, 62, 536, 60], [533, 8, 537, 6, "setErrorMessage"], [533, 23, 537, 21], [533, 24, 537, 22], [533, 68, 537, 66], [533, 69, 537, 67], [534, 8, 538, 6, "setProcessingState"], [534, 26, 538, 24], [534, 27, 538, 25], [534, 34, 538, 32], [534, 35, 538, 33], [535, 6, 539, 4], [536, 4, 540, 2], [536, 5, 540, 3], [536, 7, 540, 5], [536, 9, 540, 7], [536, 10, 540, 8], [537, 4, 541, 2], [538, 4, 542, 2], [538, 10, 542, 8, "processImageWithFaceBlur"], [538, 34, 542, 32], [538, 37, 542, 35], [538, 43, 542, 42, "photoUri"], [538, 51, 542, 58], [538, 55, 542, 63], [539, 6, 543, 4, "console"], [539, 13, 543, 11], [539, 14, 543, 12, "log"], [539, 17, 543, 15], [539, 18, 543, 16], [539, 85, 543, 83], [539, 86, 543, 84], [540, 6, 544, 4], [540, 10, 544, 8], [541, 8, 545, 6, "console"], [541, 15, 545, 13], [541, 16, 545, 14, "log"], [541, 19, 545, 17], [541, 20, 545, 18], [541, 84, 545, 82], [541, 85, 545, 83], [542, 8, 546, 6, "setProcessingState"], [542, 26, 546, 24], [542, 27, 546, 25], [542, 39, 546, 37], [542, 40, 546, 38], [543, 8, 547, 6, "setProcessingProgress"], [543, 29, 547, 27], [543, 30, 547, 28], [543, 32, 547, 30], [543, 33, 547, 31], [545, 8, 549, 6], [546, 8, 550, 6], [546, 14, 550, 12, "canvas"], [546, 20, 550, 18], [546, 23, 550, 21, "document"], [546, 31, 550, 29], [546, 32, 550, 30, "createElement"], [546, 45, 550, 43], [546, 46, 550, 44], [546, 54, 550, 52], [546, 55, 550, 53], [547, 8, 551, 6], [547, 14, 551, 12, "ctx"], [547, 17, 551, 15], [547, 20, 551, 18, "canvas"], [547, 26, 551, 24], [547, 27, 551, 25, "getContext"], [547, 37, 551, 35], [547, 38, 551, 36], [547, 42, 551, 40], [547, 43, 551, 41], [548, 8, 552, 6], [548, 12, 552, 10], [548, 13, 552, 11, "ctx"], [548, 16, 552, 14], [548, 18, 552, 16], [548, 24, 552, 22], [548, 28, 552, 26, "Error"], [548, 33, 552, 31], [548, 34, 552, 32], [548, 64, 552, 62], [548, 65, 552, 63], [550, 8, 554, 6], [551, 8, 555, 6], [551, 14, 555, 12, "img"], [551, 17, 555, 15], [551, 20, 555, 18], [551, 24, 555, 22, "Image"], [551, 29, 555, 27], [551, 30, 555, 28], [551, 31, 555, 29], [552, 8, 556, 6], [552, 14, 556, 12], [552, 18, 556, 16, "Promise"], [552, 25, 556, 23], [552, 26, 556, 24], [552, 27, 556, 25, "resolve"], [552, 34, 556, 32], [552, 36, 556, 34, "reject"], [552, 42, 556, 40], [552, 47, 556, 45], [553, 10, 557, 8, "img"], [553, 13, 557, 11], [553, 14, 557, 12, "onload"], [553, 20, 557, 18], [553, 23, 557, 21, "resolve"], [553, 30, 557, 28], [554, 10, 558, 8, "img"], [554, 13, 558, 11], [554, 14, 558, 12, "onerror"], [554, 21, 558, 19], [554, 24, 558, 22, "reject"], [554, 30, 558, 28], [555, 10, 559, 8, "img"], [555, 13, 559, 11], [555, 14, 559, 12, "src"], [555, 17, 559, 15], [555, 20, 559, 18, "photoUri"], [555, 28, 559, 26], [556, 8, 560, 6], [556, 9, 560, 7], [556, 10, 560, 8], [558, 8, 562, 6], [559, 8, 563, 6, "canvas"], [559, 14, 563, 12], [559, 15, 563, 13, "width"], [559, 20, 563, 18], [559, 23, 563, 21, "img"], [559, 26, 563, 24], [559, 27, 563, 25, "width"], [559, 32, 563, 30], [560, 8, 564, 6, "canvas"], [560, 14, 564, 12], [560, 15, 564, 13, "height"], [560, 21, 564, 19], [560, 24, 564, 22, "img"], [560, 27, 564, 25], [560, 28, 564, 26, "height"], [560, 34, 564, 32], [561, 8, 565, 6, "console"], [561, 15, 565, 13], [561, 16, 565, 14, "log"], [561, 19, 565, 17], [561, 20, 565, 18], [561, 54, 565, 52], [561, 56, 565, 54], [562, 10, 565, 56, "width"], [562, 15, 565, 61], [562, 17, 565, 63, "img"], [562, 20, 565, 66], [562, 21, 565, 67, "width"], [562, 26, 565, 72], [563, 10, 565, 74, "height"], [563, 16, 565, 80], [563, 18, 565, 82, "img"], [563, 21, 565, 85], [563, 22, 565, 86, "height"], [564, 8, 565, 93], [564, 9, 565, 94], [564, 10, 565, 95], [566, 8, 567, 6], [567, 8, 568, 6, "ctx"], [567, 11, 568, 9], [567, 12, 568, 10, "drawImage"], [567, 21, 568, 19], [567, 22, 568, 20, "img"], [567, 25, 568, 23], [567, 27, 568, 25], [567, 28, 568, 26], [567, 30, 568, 28], [567, 31, 568, 29], [567, 32, 568, 30], [568, 8, 569, 6, "console"], [568, 15, 569, 13], [568, 16, 569, 14, "log"], [568, 19, 569, 17], [568, 20, 569, 18], [568, 72, 569, 70], [568, 73, 569, 71], [569, 8, 571, 6, "setProcessingProgress"], [569, 29, 571, 27], [569, 30, 571, 28], [569, 32, 571, 30], [569, 33, 571, 31], [571, 8, 573, 6], [572, 8, 574, 6], [572, 12, 574, 10, "detectedFaces"], [572, 25, 574, 23], [572, 28, 574, 26], [572, 30, 574, 28], [573, 8, 576, 6, "console"], [573, 15, 576, 13], [573, 16, 576, 14, "log"], [573, 19, 576, 17], [573, 20, 576, 18], [573, 81, 576, 79], [573, 82, 576, 80], [575, 8, 578, 6], [576, 8, 579, 6], [576, 12, 579, 10], [577, 10, 580, 8, "console"], [577, 17, 580, 15], [577, 18, 580, 16, "log"], [577, 21, 580, 19], [577, 22, 580, 20], [577, 81, 580, 79], [577, 82, 580, 80], [578, 10, 581, 8], [578, 16, 581, 14, "loadTensorFlowFaceDetection"], [578, 43, 581, 41], [578, 44, 581, 42], [578, 45, 581, 43], [579, 10, 582, 8, "console"], [579, 17, 582, 15], [579, 18, 582, 16, "log"], [579, 21, 582, 19], [579, 22, 582, 20], [579, 90, 582, 88], [579, 91, 582, 89], [580, 10, 583, 8, "detectedFaces"], [580, 23, 583, 21], [580, 26, 583, 24], [580, 32, 583, 30, "detectFacesWithTensorFlow"], [580, 57, 583, 55], [580, 58, 583, 56, "img"], [580, 61, 583, 59], [580, 62, 583, 60], [581, 10, 584, 8, "console"], [581, 17, 584, 15], [581, 18, 584, 16, "log"], [581, 21, 584, 19], [581, 22, 584, 20], [581, 70, 584, 68, "detectedFaces"], [581, 83, 584, 81], [581, 84, 584, 82, "length"], [581, 90, 584, 88], [581, 98, 584, 96], [581, 99, 584, 97], [582, 8, 585, 6], [582, 9, 585, 7], [582, 10, 585, 8], [582, 17, 585, 15, "tensorFlowError"], [582, 32, 585, 30], [582, 34, 585, 32], [583, 10, 586, 8, "console"], [583, 17, 586, 15], [583, 18, 586, 16, "warn"], [583, 22, 586, 20], [583, 23, 586, 21], [583, 61, 586, 59], [583, 63, 586, 61, "tensorFlowError"], [583, 78, 586, 76], [583, 79, 586, 77], [584, 10, 587, 8, "console"], [584, 17, 587, 15], [584, 18, 587, 16, "warn"], [584, 22, 587, 20], [584, 23, 587, 21], [584, 68, 587, 66], [584, 70, 587, 68], [585, 12, 588, 10, "message"], [585, 19, 588, 17], [585, 21, 588, 19, "tensorFlowError"], [585, 36, 588, 34], [585, 37, 588, 35, "message"], [585, 44, 588, 42], [586, 12, 589, 10, "stack"], [586, 17, 589, 15], [586, 19, 589, 17, "tensorFlowError"], [586, 34, 589, 32], [586, 35, 589, 33, "stack"], [587, 10, 590, 8], [587, 11, 590, 9], [587, 12, 590, 10], [589, 10, 592, 8], [590, 10, 593, 8, "console"], [590, 17, 593, 15], [590, 18, 593, 16, "log"], [590, 21, 593, 19], [590, 22, 593, 20], [590, 86, 593, 84], [590, 87, 593, 85], [591, 10, 594, 8, "detectedFaces"], [591, 23, 594, 21], [591, 26, 594, 24, "detectFacesHeuristic"], [591, 46, 594, 44], [591, 47, 594, 45, "img"], [591, 50, 594, 48], [591, 52, 594, 50, "ctx"], [591, 55, 594, 53], [591, 56, 594, 54], [592, 10, 595, 8, "console"], [592, 17, 595, 15], [592, 18, 595, 16, "log"], [592, 21, 595, 19], [592, 22, 595, 20], [592, 70, 595, 68, "detectedFaces"], [592, 83, 595, 81], [592, 84, 595, 82, "length"], [592, 90, 595, 88], [592, 98, 595, 96], [592, 99, 595, 97], [593, 8, 596, 6], [595, 8, 598, 6], [596, 8, 599, 6], [596, 12, 599, 10, "detectedFaces"], [596, 25, 599, 23], [596, 26, 599, 24, "length"], [596, 32, 599, 30], [596, 37, 599, 35], [596, 38, 599, 36], [596, 40, 599, 38], [597, 10, 600, 8, "console"], [597, 17, 600, 15], [597, 18, 600, 16, "log"], [597, 21, 600, 19], [597, 22, 600, 20], [597, 89, 600, 87], [597, 90, 600, 88], [598, 10, 601, 8, "detectedFaces"], [598, 23, 601, 21], [598, 26, 601, 24, "detectFacesAggressive"], [598, 47, 601, 45], [598, 48, 601, 46, "img"], [598, 51, 601, 49], [598, 53, 601, 51, "ctx"], [598, 56, 601, 54], [598, 57, 601, 55], [599, 10, 602, 8, "console"], [599, 17, 602, 15], [599, 18, 602, 16, "log"], [599, 21, 602, 19], [599, 22, 602, 20], [599, 71, 602, 69, "detectedFaces"], [599, 84, 602, 82], [599, 85, 602, 83, "length"], [599, 91, 602, 89], [599, 99, 602, 97], [599, 100, 602, 98], [600, 8, 603, 6], [601, 8, 605, 6, "console"], [601, 15, 605, 13], [601, 16, 605, 14, "log"], [601, 19, 605, 17], [601, 20, 605, 18], [601, 72, 605, 70, "detectedFaces"], [601, 85, 605, 83], [601, 86, 605, 84, "length"], [601, 92, 605, 90], [601, 100, 605, 98], [601, 101, 605, 99], [602, 8, 606, 6], [602, 12, 606, 10, "detectedFaces"], [602, 25, 606, 23], [602, 26, 606, 24, "length"], [602, 32, 606, 30], [602, 35, 606, 33], [602, 36, 606, 34], [602, 38, 606, 36], [603, 10, 607, 8, "console"], [603, 17, 607, 15], [603, 18, 607, 16, "log"], [603, 21, 607, 19], [603, 22, 607, 20], [603, 66, 607, 64], [603, 68, 607, 66, "detectedFaces"], [603, 81, 607, 79], [603, 82, 607, 80, "map"], [603, 85, 607, 83], [603, 86, 607, 84], [603, 87, 607, 85, "face"], [603, 91, 607, 89], [603, 93, 607, 91, "i"], [603, 94, 607, 92], [603, 100, 607, 98], [604, 12, 608, 10, "faceNumber"], [604, 22, 608, 20], [604, 24, 608, 22, "i"], [604, 25, 608, 23], [604, 28, 608, 26], [604, 29, 608, 27], [605, 12, 609, 10, "centerX"], [605, 19, 609, 17], [605, 21, 609, 19, "face"], [605, 25, 609, 23], [605, 26, 609, 24, "boundingBox"], [605, 37, 609, 35], [605, 38, 609, 36, "xCenter"], [605, 45, 609, 43], [606, 12, 610, 10, "centerY"], [606, 19, 610, 17], [606, 21, 610, 19, "face"], [606, 25, 610, 23], [606, 26, 610, 24, "boundingBox"], [606, 37, 610, 35], [606, 38, 610, 36, "yCenter"], [606, 45, 610, 43], [607, 12, 611, 10, "width"], [607, 17, 611, 15], [607, 19, 611, 17, "face"], [607, 23, 611, 21], [607, 24, 611, 22, "boundingBox"], [607, 35, 611, 33], [607, 36, 611, 34, "width"], [607, 41, 611, 39], [608, 12, 612, 10, "height"], [608, 18, 612, 16], [608, 20, 612, 18, "face"], [608, 24, 612, 22], [608, 25, 612, 23, "boundingBox"], [608, 36, 612, 34], [608, 37, 612, 35, "height"], [609, 10, 613, 8], [609, 11, 613, 9], [609, 12, 613, 10], [609, 13, 613, 11], [609, 14, 613, 12], [610, 8, 614, 6], [610, 9, 614, 7], [610, 15, 614, 13], [611, 10, 615, 8, "console"], [611, 17, 615, 15], [611, 18, 615, 16, "log"], [611, 21, 615, 19], [611, 22, 615, 20], [611, 74, 615, 72], [611, 75, 615, 73], [612, 10, 616, 8, "console"], [612, 17, 616, 15], [612, 18, 616, 16, "log"], [612, 21, 616, 19], [612, 22, 616, 20], [612, 95, 616, 93], [612, 96, 616, 94], [614, 10, 618, 8], [615, 10, 619, 8], [615, 16, 619, 14, "centerX"], [615, 23, 619, 21], [615, 26, 619, 24, "img"], [615, 29, 619, 27], [615, 30, 619, 28, "width"], [615, 35, 619, 33], [615, 38, 619, 36], [615, 41, 619, 39], [616, 10, 620, 8], [616, 16, 620, 14, "centerY"], [616, 23, 620, 21], [616, 26, 620, 24, "img"], [616, 29, 620, 27], [616, 30, 620, 28, "height"], [616, 36, 620, 34], [616, 39, 620, 37], [616, 42, 620, 40], [617, 10, 621, 8], [617, 16, 621, 14, "centerWidth"], [617, 27, 621, 25], [617, 30, 621, 28, "img"], [617, 33, 621, 31], [617, 34, 621, 32, "width"], [617, 39, 621, 37], [617, 42, 621, 40], [617, 45, 621, 43], [618, 10, 622, 8], [618, 16, 622, 14, "centerHeight"], [618, 28, 622, 26], [618, 31, 622, 29, "img"], [618, 34, 622, 32], [618, 35, 622, 33, "height"], [618, 41, 622, 39], [618, 44, 622, 42], [618, 47, 622, 45], [619, 10, 624, 8, "detectedFaces"], [619, 23, 624, 21], [619, 26, 624, 24], [619, 27, 624, 25], [620, 12, 625, 10, "boundingBox"], [620, 23, 625, 21], [620, 25, 625, 23], [621, 14, 626, 12, "xCenter"], [621, 21, 626, 19], [621, 23, 626, 21], [621, 26, 626, 24], [622, 14, 627, 12, "yCenter"], [622, 21, 627, 19], [622, 23, 627, 21], [622, 26, 627, 24], [623, 14, 628, 12, "width"], [623, 19, 628, 17], [623, 21, 628, 19], [623, 24, 628, 22], [624, 14, 629, 12, "height"], [624, 20, 629, 18], [624, 22, 629, 20], [625, 12, 630, 10], [625, 13, 630, 11], [626, 12, 631, 10, "confidence"], [626, 22, 631, 20], [626, 24, 631, 22], [627, 10, 632, 8], [627, 11, 632, 9], [627, 12, 632, 10], [628, 10, 634, 8, "console"], [628, 17, 634, 15], [628, 18, 634, 16, "log"], [628, 21, 634, 19], [628, 22, 634, 20], [628, 100, 634, 98], [628, 101, 634, 99], [629, 8, 635, 6], [630, 8, 637, 6, "setProcessingProgress"], [630, 29, 637, 27], [630, 30, 637, 28], [630, 32, 637, 30], [630, 33, 637, 31], [632, 8, 639, 6], [633, 8, 640, 6], [633, 12, 640, 10, "detectedFaces"], [633, 25, 640, 23], [633, 26, 640, 24, "length"], [633, 32, 640, 30], [633, 35, 640, 33], [633, 36, 640, 34], [633, 38, 640, 36], [634, 10, 641, 8, "console"], [634, 17, 641, 15], [634, 18, 641, 16, "log"], [634, 21, 641, 19], [634, 22, 641, 20], [634, 61, 641, 59, "detectedFaces"], [634, 74, 641, 72], [634, 75, 641, 73, "length"], [634, 81, 641, 79], [634, 101, 641, 99], [634, 102, 641, 100], [635, 10, 643, 8, "detectedFaces"], [635, 23, 643, 21], [635, 24, 643, 22, "for<PERSON>ach"], [635, 31, 643, 29], [635, 32, 643, 30], [635, 33, 643, 31, "detection"], [635, 42, 643, 40], [635, 44, 643, 42, "index"], [635, 49, 643, 47], [635, 54, 643, 52], [636, 12, 644, 10], [636, 18, 644, 16, "bbox"], [636, 22, 644, 20], [636, 25, 644, 23, "detection"], [636, 34, 644, 32], [636, 35, 644, 33, "boundingBox"], [636, 46, 644, 44], [637, 12, 646, 10, "console"], [637, 19, 646, 17], [637, 20, 646, 18, "log"], [637, 23, 646, 21], [637, 24, 646, 22], [637, 85, 646, 83, "index"], [637, 90, 646, 88], [637, 93, 646, 91], [637, 94, 646, 92], [637, 97, 646, 95], [637, 99, 646, 97], [638, 14, 647, 12, "bbox"], [638, 18, 647, 16], [639, 14, 648, 12, "imageSize"], [639, 23, 648, 21], [639, 25, 648, 23], [640, 16, 648, 25, "width"], [640, 21, 648, 30], [640, 23, 648, 32, "img"], [640, 26, 648, 35], [640, 27, 648, 36, "width"], [640, 32, 648, 41], [641, 16, 648, 43, "height"], [641, 22, 648, 49], [641, 24, 648, 51, "img"], [641, 27, 648, 54], [641, 28, 648, 55, "height"], [642, 14, 648, 62], [643, 12, 649, 10], [643, 13, 649, 11], [643, 14, 649, 12], [645, 12, 651, 10], [646, 12, 652, 10], [646, 18, 652, 16, "faceX"], [646, 23, 652, 21], [646, 26, 652, 24, "bbox"], [646, 30, 652, 28], [646, 31, 652, 29, "xCenter"], [646, 38, 652, 36], [646, 41, 652, 39, "img"], [646, 44, 652, 42], [646, 45, 652, 43, "width"], [646, 50, 652, 48], [646, 53, 652, 52, "bbox"], [646, 57, 652, 56], [646, 58, 652, 57, "width"], [646, 63, 652, 62], [646, 66, 652, 65, "img"], [646, 69, 652, 68], [646, 70, 652, 69, "width"], [646, 75, 652, 74], [646, 78, 652, 78], [646, 79, 652, 79], [647, 12, 653, 10], [647, 18, 653, 16, "faceY"], [647, 23, 653, 21], [647, 26, 653, 24, "bbox"], [647, 30, 653, 28], [647, 31, 653, 29, "yCenter"], [647, 38, 653, 36], [647, 41, 653, 39, "img"], [647, 44, 653, 42], [647, 45, 653, 43, "height"], [647, 51, 653, 49], [647, 54, 653, 53, "bbox"], [647, 58, 653, 57], [647, 59, 653, 58, "height"], [647, 65, 653, 64], [647, 68, 653, 67, "img"], [647, 71, 653, 70], [647, 72, 653, 71, "height"], [647, 78, 653, 77], [647, 81, 653, 81], [647, 82, 653, 82], [648, 12, 654, 10], [648, 18, 654, 16, "faceWidth"], [648, 27, 654, 25], [648, 30, 654, 28, "bbox"], [648, 34, 654, 32], [648, 35, 654, 33, "width"], [648, 40, 654, 38], [648, 43, 654, 41, "img"], [648, 46, 654, 44], [648, 47, 654, 45, "width"], [648, 52, 654, 50], [649, 12, 655, 10], [649, 18, 655, 16, "faceHeight"], [649, 28, 655, 26], [649, 31, 655, 29, "bbox"], [649, 35, 655, 33], [649, 36, 655, 34, "height"], [649, 42, 655, 40], [649, 45, 655, 43, "img"], [649, 48, 655, 46], [649, 49, 655, 47, "height"], [649, 55, 655, 53], [650, 12, 657, 10, "console"], [650, 19, 657, 17], [650, 20, 657, 18, "log"], [650, 23, 657, 21], [650, 24, 657, 22], [650, 84, 657, 82], [650, 86, 657, 84], [651, 14, 658, 12, "faceX"], [651, 19, 658, 17], [652, 14, 658, 19, "faceY"], [652, 19, 658, 24], [653, 14, 658, 26, "faceWidth"], [653, 23, 658, 35], [654, 14, 658, 37, "faceHeight"], [654, 24, 658, 47], [655, 14, 659, 12, "<PERSON><PERSON><PERSON><PERSON>"], [655, 21, 659, 19], [655, 23, 659, 21, "faceX"], [655, 28, 659, 26], [655, 32, 659, 30], [655, 33, 659, 31], [655, 37, 659, 35, "faceY"], [655, 42, 659, 40], [655, 46, 659, 44], [655, 47, 659, 45], [655, 51, 659, 49, "faceWidth"], [655, 60, 659, 58], [655, 63, 659, 61], [655, 64, 659, 62], [655, 68, 659, 66, "faceHeight"], [655, 78, 659, 76], [655, 81, 659, 79], [656, 12, 660, 10], [656, 13, 660, 11], [656, 14, 660, 12], [658, 12, 662, 10], [659, 12, 663, 10], [659, 18, 663, 16, "padding"], [659, 25, 663, 23], [659, 28, 663, 26], [659, 31, 663, 29], [660, 12, 664, 10], [660, 18, 664, 16, "paddedX"], [660, 25, 664, 23], [660, 28, 664, 26, "Math"], [660, 32, 664, 30], [660, 33, 664, 31, "max"], [660, 36, 664, 34], [660, 37, 664, 35], [660, 38, 664, 36], [660, 40, 664, 38, "faceX"], [660, 45, 664, 43], [660, 48, 664, 46, "faceWidth"], [660, 57, 664, 55], [660, 60, 664, 58, "padding"], [660, 67, 664, 65], [660, 68, 664, 66], [661, 12, 665, 10], [661, 18, 665, 16, "paddedY"], [661, 25, 665, 23], [661, 28, 665, 26, "Math"], [661, 32, 665, 30], [661, 33, 665, 31, "max"], [661, 36, 665, 34], [661, 37, 665, 35], [661, 38, 665, 36], [661, 40, 665, 38, "faceY"], [661, 45, 665, 43], [661, 48, 665, 46, "faceHeight"], [661, 58, 665, 56], [661, 61, 665, 59, "padding"], [661, 68, 665, 66], [661, 69, 665, 67], [662, 12, 666, 10], [662, 18, 666, 16, "<PERSON><PERSON><PERSON><PERSON>"], [662, 29, 666, 27], [662, 32, 666, 30, "Math"], [662, 36, 666, 34], [662, 37, 666, 35, "min"], [662, 40, 666, 38], [662, 41, 666, 39, "img"], [662, 44, 666, 42], [662, 45, 666, 43, "width"], [662, 50, 666, 48], [662, 53, 666, 51, "paddedX"], [662, 60, 666, 58], [662, 62, 666, 60, "faceWidth"], [662, 71, 666, 69], [662, 75, 666, 73], [662, 76, 666, 74], [662, 79, 666, 77], [662, 80, 666, 78], [662, 83, 666, 81, "padding"], [662, 90, 666, 88], [662, 91, 666, 89], [662, 92, 666, 90], [663, 12, 667, 10], [663, 18, 667, 16, "paddedHeight"], [663, 30, 667, 28], [663, 33, 667, 31, "Math"], [663, 37, 667, 35], [663, 38, 667, 36, "min"], [663, 41, 667, 39], [663, 42, 667, 40, "img"], [663, 45, 667, 43], [663, 46, 667, 44, "height"], [663, 52, 667, 50], [663, 55, 667, 53, "paddedY"], [663, 62, 667, 60], [663, 64, 667, 62, "faceHeight"], [663, 74, 667, 72], [663, 78, 667, 76], [663, 79, 667, 77], [663, 82, 667, 80], [663, 83, 667, 81], [663, 86, 667, 84, "padding"], [663, 93, 667, 91], [663, 94, 667, 92], [663, 95, 667, 93], [664, 12, 669, 10, "console"], [664, 19, 669, 17], [664, 20, 669, 18, "log"], [664, 23, 669, 21], [664, 24, 669, 22], [664, 60, 669, 58, "index"], [664, 65, 669, 63], [664, 68, 669, 66], [664, 69, 669, 67], [664, 72, 669, 70], [664, 74, 669, 72], [665, 14, 670, 12, "original"], [665, 22, 670, 20], [665, 24, 670, 22], [666, 16, 670, 24, "x"], [666, 17, 670, 25], [666, 19, 670, 27, "Math"], [666, 23, 670, 31], [666, 24, 670, 32, "round"], [666, 29, 670, 37], [666, 30, 670, 38, "faceX"], [666, 35, 670, 43], [666, 36, 670, 44], [667, 16, 670, 46, "y"], [667, 17, 670, 47], [667, 19, 670, 49, "Math"], [667, 23, 670, 53], [667, 24, 670, 54, "round"], [667, 29, 670, 59], [667, 30, 670, 60, "faceY"], [667, 35, 670, 65], [667, 36, 670, 66], [668, 16, 670, 68, "w"], [668, 17, 670, 69], [668, 19, 670, 71, "Math"], [668, 23, 670, 75], [668, 24, 670, 76, "round"], [668, 29, 670, 81], [668, 30, 670, 82, "faceWidth"], [668, 39, 670, 91], [668, 40, 670, 92], [669, 16, 670, 94, "h"], [669, 17, 670, 95], [669, 19, 670, 97, "Math"], [669, 23, 670, 101], [669, 24, 670, 102, "round"], [669, 29, 670, 107], [669, 30, 670, 108, "faceHeight"], [669, 40, 670, 118], [670, 14, 670, 120], [670, 15, 670, 121], [671, 14, 671, 12, "padded"], [671, 20, 671, 18], [671, 22, 671, 20], [672, 16, 671, 22, "x"], [672, 17, 671, 23], [672, 19, 671, 25, "Math"], [672, 23, 671, 29], [672, 24, 671, 30, "round"], [672, 29, 671, 35], [672, 30, 671, 36, "paddedX"], [672, 37, 671, 43], [672, 38, 671, 44], [673, 16, 671, 46, "y"], [673, 17, 671, 47], [673, 19, 671, 49, "Math"], [673, 23, 671, 53], [673, 24, 671, 54, "round"], [673, 29, 671, 59], [673, 30, 671, 60, "paddedY"], [673, 37, 671, 67], [673, 38, 671, 68], [674, 16, 671, 70, "w"], [674, 17, 671, 71], [674, 19, 671, 73, "Math"], [674, 23, 671, 77], [674, 24, 671, 78, "round"], [674, 29, 671, 83], [674, 30, 671, 84, "<PERSON><PERSON><PERSON><PERSON>"], [674, 41, 671, 95], [674, 42, 671, 96], [675, 16, 671, 98, "h"], [675, 17, 671, 99], [675, 19, 671, 101, "Math"], [675, 23, 671, 105], [675, 24, 671, 106, "round"], [675, 29, 671, 111], [675, 30, 671, 112, "paddedHeight"], [675, 42, 671, 124], [676, 14, 671, 126], [677, 12, 672, 10], [677, 13, 672, 11], [677, 14, 672, 12], [679, 12, 674, 10], [680, 12, 675, 10, "console"], [680, 19, 675, 17], [680, 20, 675, 18, "log"], [680, 23, 675, 21], [680, 24, 675, 22], [680, 70, 675, 68], [680, 72, 675, 70], [681, 14, 676, 12, "width"], [681, 19, 676, 17], [681, 21, 676, 19, "canvas"], [681, 27, 676, 25], [681, 28, 676, 26, "width"], [681, 33, 676, 31], [682, 14, 677, 12, "height"], [682, 20, 677, 18], [682, 22, 677, 20, "canvas"], [682, 28, 677, 26], [682, 29, 677, 27, "height"], [682, 35, 677, 33], [683, 14, 678, 12, "contextValid"], [683, 26, 678, 24], [683, 28, 678, 26], [683, 29, 678, 27], [683, 30, 678, 28, "ctx"], [684, 12, 679, 10], [684, 13, 679, 11], [684, 14, 679, 12], [686, 12, 681, 10], [687, 12, 682, 10, "applyStrongBlur"], [687, 27, 682, 25], [687, 28, 682, 26, "ctx"], [687, 31, 682, 29], [687, 33, 682, 31, "paddedX"], [687, 40, 682, 38], [687, 42, 682, 40, "paddedY"], [687, 49, 682, 47], [687, 51, 682, 49, "<PERSON><PERSON><PERSON><PERSON>"], [687, 62, 682, 60], [687, 64, 682, 62, "paddedHeight"], [687, 76, 682, 74], [687, 77, 682, 75], [689, 12, 684, 10], [690, 12, 685, 10, "console"], [690, 19, 685, 17], [690, 20, 685, 18, "log"], [690, 23, 685, 21], [690, 24, 685, 22], [690, 102, 685, 100], [690, 103, 685, 101], [692, 12, 687, 10], [693, 12, 688, 10], [693, 18, 688, 16, "testImageData"], [693, 31, 688, 29], [693, 34, 688, 32, "ctx"], [693, 37, 688, 35], [693, 38, 688, 36, "getImageData"], [693, 50, 688, 48], [693, 51, 688, 49, "paddedX"], [693, 58, 688, 56], [693, 61, 688, 59], [693, 63, 688, 61], [693, 65, 688, 63, "paddedY"], [693, 72, 688, 70], [693, 75, 688, 73], [693, 77, 688, 75], [693, 79, 688, 77], [693, 81, 688, 79], [693, 83, 688, 81], [693, 85, 688, 83], [693, 86, 688, 84], [694, 12, 689, 10, "console"], [694, 19, 689, 17], [694, 20, 689, 18, "log"], [694, 23, 689, 21], [694, 24, 689, 22], [694, 70, 689, 68], [694, 72, 689, 70], [695, 14, 690, 12, "firstPixel"], [695, 24, 690, 22], [695, 26, 690, 24], [695, 27, 690, 25, "testImageData"], [695, 40, 690, 38], [695, 41, 690, 39, "data"], [695, 45, 690, 43], [695, 46, 690, 44], [695, 47, 690, 45], [695, 48, 690, 46], [695, 50, 690, 48, "testImageData"], [695, 63, 690, 61], [695, 64, 690, 62, "data"], [695, 68, 690, 66], [695, 69, 690, 67], [695, 70, 690, 68], [695, 71, 690, 69], [695, 73, 690, 71, "testImageData"], [695, 86, 690, 84], [695, 87, 690, 85, "data"], [695, 91, 690, 89], [695, 92, 690, 90], [695, 93, 690, 91], [695, 94, 690, 92], [695, 95, 690, 93], [696, 14, 691, 12, "secondPixel"], [696, 25, 691, 23], [696, 27, 691, 25], [696, 28, 691, 26, "testImageData"], [696, 41, 691, 39], [696, 42, 691, 40, "data"], [696, 46, 691, 44], [696, 47, 691, 45], [696, 48, 691, 46], [696, 49, 691, 47], [696, 51, 691, 49, "testImageData"], [696, 64, 691, 62], [696, 65, 691, 63, "data"], [696, 69, 691, 67], [696, 70, 691, 68], [696, 71, 691, 69], [696, 72, 691, 70], [696, 74, 691, 72, "testImageData"], [696, 87, 691, 85], [696, 88, 691, 86, "data"], [696, 92, 691, 90], [696, 93, 691, 91], [696, 94, 691, 92], [696, 95, 691, 93], [697, 12, 692, 10], [697, 13, 692, 11], [697, 14, 692, 12], [698, 12, 694, 10, "console"], [698, 19, 694, 17], [698, 20, 694, 18, "log"], [698, 23, 694, 21], [698, 24, 694, 22], [698, 50, 694, 48, "index"], [698, 55, 694, 53], [698, 58, 694, 56], [698, 59, 694, 57], [698, 79, 694, 77], [698, 80, 694, 78], [699, 10, 695, 8], [699, 11, 695, 9], [699, 12, 695, 10], [700, 10, 697, 8, "console"], [700, 17, 697, 15], [700, 18, 697, 16, "log"], [700, 21, 697, 19], [700, 22, 697, 20], [700, 48, 697, 46, "detectedFaces"], [700, 61, 697, 59], [700, 62, 697, 60, "length"], [700, 68, 697, 66], [700, 104, 697, 102], [700, 105, 697, 103], [701, 8, 698, 6], [701, 9, 698, 7], [701, 15, 698, 13], [702, 10, 699, 8, "console"], [702, 17, 699, 15], [702, 18, 699, 16, "log"], [702, 21, 699, 19], [702, 22, 699, 20], [702, 109, 699, 107], [702, 110, 699, 108], [703, 10, 700, 8], [704, 10, 701, 8, "applyFallbackFaceBlur"], [704, 31, 701, 29], [704, 32, 701, 30, "ctx"], [704, 35, 701, 33], [704, 37, 701, 35, "img"], [704, 40, 701, 38], [704, 41, 701, 39, "width"], [704, 46, 701, 44], [704, 48, 701, 46, "img"], [704, 51, 701, 49], [704, 52, 701, 50, "height"], [704, 58, 701, 56], [704, 59, 701, 57], [705, 8, 702, 6], [706, 8, 704, 6, "setProcessingProgress"], [706, 29, 704, 27], [706, 30, 704, 28], [706, 32, 704, 30], [706, 33, 704, 31], [708, 8, 706, 6], [709, 8, 707, 6, "console"], [709, 15, 707, 13], [709, 16, 707, 14, "log"], [709, 19, 707, 17], [709, 20, 707, 18], [709, 85, 707, 83], [709, 86, 707, 84], [710, 8, 708, 6], [710, 14, 708, 12, "blurredImageBlob"], [710, 30, 708, 28], [710, 33, 708, 31], [710, 39, 708, 37], [710, 43, 708, 41, "Promise"], [710, 50, 708, 48], [710, 51, 708, 56, "resolve"], [710, 58, 708, 63], [710, 62, 708, 68], [711, 10, 709, 8, "canvas"], [711, 16, 709, 14], [711, 17, 709, 15, "toBlob"], [711, 23, 709, 21], [711, 24, 709, 23, "blob"], [711, 28, 709, 27], [711, 32, 709, 32, "resolve"], [711, 39, 709, 39], [711, 40, 709, 40, "blob"], [711, 44, 709, 45], [711, 45, 709, 46], [711, 47, 709, 48], [711, 59, 709, 60], [711, 61, 709, 62], [711, 64, 709, 65], [711, 65, 709, 66], [712, 8, 710, 6], [712, 9, 710, 7], [712, 10, 710, 8], [713, 8, 712, 6], [713, 14, 712, 12, "blurredImageUrl"], [713, 29, 712, 27], [713, 32, 712, 30, "URL"], [713, 35, 712, 33], [713, 36, 712, 34, "createObjectURL"], [713, 51, 712, 49], [713, 52, 712, 50, "blurredImageBlob"], [713, 68, 712, 66], [713, 69, 712, 67], [714, 8, 713, 6, "console"], [714, 15, 713, 13], [714, 16, 713, 14, "log"], [714, 19, 713, 17], [714, 20, 713, 18], [714, 66, 713, 64], [714, 68, 713, 66, "blurredImageUrl"], [714, 83, 713, 81], [714, 84, 713, 82, "substring"], [714, 93, 713, 91], [714, 94, 713, 92], [714, 95, 713, 93], [714, 97, 713, 95], [714, 99, 713, 97], [714, 100, 713, 98], [714, 103, 713, 101], [714, 108, 713, 106], [714, 109, 713, 107], [716, 8, 715, 6], [717, 8, 716, 6, "setCapturedPhoto"], [717, 24, 716, 22], [717, 25, 716, 23, "blurredImageUrl"], [717, 40, 716, 38], [717, 41, 716, 39], [718, 8, 717, 6, "console"], [718, 15, 717, 13], [718, 16, 717, 14, "log"], [718, 19, 717, 17], [718, 20, 717, 18], [718, 87, 717, 85], [718, 88, 717, 86], [719, 8, 719, 6, "setProcessingProgress"], [719, 29, 719, 27], [719, 30, 719, 28], [719, 33, 719, 31], [719, 34, 719, 32], [721, 8, 721, 6], [722, 8, 722, 6], [722, 14, 722, 12, "completeProcessing"], [722, 32, 722, 30], [722, 33, 722, 31, "blurredImageUrl"], [722, 48, 722, 46], [722, 49, 722, 47], [723, 6, 724, 4], [723, 7, 724, 5], [723, 8, 724, 6], [723, 15, 724, 13, "error"], [723, 20, 724, 18], [723, 22, 724, 20], [724, 8, 725, 6, "console"], [724, 15, 725, 13], [724, 16, 725, 14, "error"], [724, 21, 725, 19], [724, 22, 725, 20], [724, 86, 725, 84], [724, 88, 725, 86, "error"], [724, 93, 725, 91], [724, 94, 725, 92], [725, 8, 726, 6, "console"], [725, 15, 726, 13], [725, 16, 726, 14, "error"], [725, 21, 726, 19], [725, 22, 726, 20], [725, 55, 726, 53], [725, 57, 726, 55, "error"], [725, 62, 726, 60], [725, 63, 726, 61, "stack"], [725, 68, 726, 66], [725, 69, 726, 67], [726, 8, 727, 6, "console"], [726, 15, 727, 13], [726, 16, 727, 14, "error"], [726, 21, 727, 19], [726, 22, 727, 20], [726, 57, 727, 55], [726, 59, 727, 57], [727, 10, 728, 8, "name"], [727, 14, 728, 12], [727, 16, 728, 14, "error"], [727, 21, 728, 19], [727, 22, 728, 20, "name"], [727, 26, 728, 24], [728, 10, 729, 8, "message"], [728, 17, 729, 15], [728, 19, 729, 17, "error"], [728, 24, 729, 22], [728, 25, 729, 23, "message"], [728, 32, 729, 30], [729, 10, 730, 8, "photoUri"], [730, 8, 731, 6], [730, 9, 731, 7], [730, 10, 731, 8], [731, 8, 732, 6, "setErrorMessage"], [731, 23, 732, 21], [731, 24, 732, 22], [731, 50, 732, 48], [731, 51, 732, 49], [732, 8, 733, 6, "setProcessingState"], [732, 26, 733, 24], [732, 27, 733, 25], [732, 34, 733, 32], [732, 35, 733, 33], [733, 6, 734, 4], [734, 4, 735, 2], [734, 5, 735, 3], [736, 4, 737, 2], [737, 4, 738, 2], [737, 10, 738, 8, "completeProcessing"], [737, 28, 738, 26], [737, 31, 738, 29], [737, 37, 738, 36, "blurredImageUrl"], [737, 52, 738, 59], [737, 56, 738, 64], [738, 6, 739, 4], [738, 10, 739, 8], [739, 8, 740, 6, "setProcessingState"], [739, 26, 740, 24], [739, 27, 740, 25], [739, 37, 740, 35], [739, 38, 740, 36], [741, 8, 742, 6], [742, 8, 743, 6], [742, 14, 743, 12, "timestamp"], [742, 23, 743, 21], [742, 26, 743, 24, "Date"], [742, 30, 743, 28], [742, 31, 743, 29, "now"], [742, 34, 743, 32], [742, 35, 743, 33], [742, 36, 743, 34], [743, 8, 744, 6], [743, 14, 744, 12, "result"], [743, 20, 744, 18], [743, 23, 744, 21], [744, 10, 745, 8, "imageUrl"], [744, 18, 745, 16], [744, 20, 745, 18, "blurredImageUrl"], [744, 35, 745, 33], [745, 10, 746, 8, "localUri"], [745, 18, 746, 16], [745, 20, 746, 18, "blurredImageUrl"], [745, 35, 746, 33], [746, 10, 747, 8, "challengeCode"], [746, 23, 747, 21], [746, 25, 747, 23, "challengeCode"], [746, 38, 747, 36], [746, 42, 747, 40], [746, 44, 747, 42], [747, 10, 748, 8, "timestamp"], [747, 19, 748, 17], [748, 10, 749, 8, "jobId"], [748, 15, 749, 13], [748, 17, 749, 15], [748, 27, 749, 25, "timestamp"], [748, 36, 749, 34], [748, 38, 749, 36], [749, 10, 750, 8, "status"], [749, 16, 750, 14], [749, 18, 750, 16], [750, 8, 751, 6], [750, 9, 751, 7], [751, 8, 753, 6, "console"], [751, 15, 753, 13], [751, 16, 753, 14, "log"], [751, 19, 753, 17], [751, 20, 753, 18], [751, 100, 753, 98], [751, 102, 753, 100], [752, 10, 754, 8, "imageUrl"], [752, 18, 754, 16], [752, 20, 754, 18, "blurredImageUrl"], [752, 35, 754, 33], [752, 36, 754, 34, "substring"], [752, 45, 754, 43], [752, 46, 754, 44], [752, 47, 754, 45], [752, 49, 754, 47], [752, 51, 754, 49], [752, 52, 754, 50], [752, 55, 754, 53], [752, 60, 754, 58], [753, 10, 755, 8, "timestamp"], [753, 19, 755, 17], [754, 10, 756, 8, "jobId"], [754, 15, 756, 13], [754, 17, 756, 15, "result"], [754, 23, 756, 21], [754, 24, 756, 22, "jobId"], [755, 8, 757, 6], [755, 9, 757, 7], [755, 10, 757, 8], [757, 8, 759, 6], [758, 8, 760, 6, "onComplete"], [758, 18, 760, 16], [758, 19, 760, 17, "result"], [758, 25, 760, 23], [758, 26, 760, 24], [759, 6, 762, 4], [759, 7, 762, 5], [759, 8, 762, 6], [759, 15, 762, 13, "error"], [759, 20, 762, 18], [759, 22, 762, 20], [760, 8, 763, 6, "console"], [760, 15, 763, 13], [760, 16, 763, 14, "error"], [760, 21, 763, 19], [760, 22, 763, 20], [760, 57, 763, 55], [760, 59, 763, 57, "error"], [760, 64, 763, 62], [760, 65, 763, 63], [761, 8, 764, 6, "setErrorMessage"], [761, 23, 764, 21], [761, 24, 764, 22], [761, 56, 764, 54], [761, 57, 764, 55], [762, 8, 765, 6, "setProcessingState"], [762, 26, 765, 24], [762, 27, 765, 25], [762, 34, 765, 32], [762, 35, 765, 33], [763, 6, 766, 4], [764, 4, 767, 2], [764, 5, 767, 3], [766, 4, 769, 2], [767, 4, 770, 2], [767, 10, 770, 8, "triggerServerProcessing"], [767, 33, 770, 31], [767, 36, 770, 34], [767, 42, 770, 34, "triggerServerProcessing"], [767, 43, 770, 41, "privateImageUrl"], [767, 58, 770, 64], [767, 60, 770, 66, "timestamp"], [767, 69, 770, 83], [767, 74, 770, 88], [768, 6, 771, 4], [768, 10, 771, 8], [769, 8, 772, 6, "console"], [769, 15, 772, 13], [769, 16, 772, 14, "log"], [769, 19, 772, 17], [769, 20, 772, 18], [769, 74, 772, 72], [769, 76, 772, 74, "privateImageUrl"], [769, 91, 772, 89], [769, 92, 772, 90], [770, 8, 773, 6, "setProcessingState"], [770, 26, 773, 24], [770, 27, 773, 25], [770, 39, 773, 37], [770, 40, 773, 38], [771, 8, 774, 6, "setProcessingProgress"], [771, 29, 774, 27], [771, 30, 774, 28], [771, 32, 774, 30], [771, 33, 774, 31], [772, 8, 776, 6], [772, 14, 776, 12, "requestBody"], [772, 25, 776, 23], [772, 28, 776, 26], [773, 10, 777, 8, "imageUrl"], [773, 18, 777, 16], [773, 20, 777, 18, "privateImageUrl"], [773, 35, 777, 33], [774, 10, 778, 8, "userId"], [774, 16, 778, 14], [775, 10, 779, 8, "requestId"], [775, 19, 779, 17], [776, 10, 780, 8, "timestamp"], [776, 19, 780, 17], [777, 10, 781, 8, "platform"], [777, 18, 781, 16], [777, 20, 781, 18], [778, 8, 782, 6], [778, 9, 782, 7], [779, 8, 784, 6, "console"], [779, 15, 784, 13], [779, 16, 784, 14, "log"], [779, 19, 784, 17], [779, 20, 784, 18], [779, 65, 784, 63], [779, 67, 784, 65, "requestBody"], [779, 78, 784, 76], [779, 79, 784, 77], [781, 8, 786, 6], [782, 8, 787, 6], [782, 14, 787, 12, "response"], [782, 22, 787, 20], [782, 25, 787, 23], [782, 31, 787, 29, "fetch"], [782, 36, 787, 34], [782, 37, 787, 35], [782, 40, 787, 38, "API_BASE_URL"], [782, 52, 787, 50], [782, 72, 787, 70], [782, 74, 787, 72], [783, 10, 788, 8, "method"], [783, 16, 788, 14], [783, 18, 788, 16], [783, 24, 788, 22], [784, 10, 789, 8, "headers"], [784, 17, 789, 15], [784, 19, 789, 17], [785, 12, 790, 10], [785, 26, 790, 24], [785, 28, 790, 26], [785, 46, 790, 44], [786, 12, 791, 10], [786, 27, 791, 25], [786, 29, 791, 27], [786, 39, 791, 37], [786, 45, 791, 43, "getAuthToken"], [786, 57, 791, 55], [786, 58, 791, 56], [786, 59, 791, 57], [787, 10, 792, 8], [787, 11, 792, 9], [788, 10, 793, 8, "body"], [788, 14, 793, 12], [788, 16, 793, 14, "JSON"], [788, 20, 793, 18], [788, 21, 793, 19, "stringify"], [788, 30, 793, 28], [788, 31, 793, 29, "requestBody"], [788, 42, 793, 40], [789, 8, 794, 6], [789, 9, 794, 7], [789, 10, 794, 8], [790, 8, 796, 6], [790, 12, 796, 10], [790, 13, 796, 11, "response"], [790, 21, 796, 19], [790, 22, 796, 20, "ok"], [790, 24, 796, 22], [790, 26, 796, 24], [791, 10, 797, 8], [791, 16, 797, 14, "errorText"], [791, 25, 797, 23], [791, 28, 797, 26], [791, 34, 797, 32, "response"], [791, 42, 797, 40], [791, 43, 797, 41, "text"], [791, 47, 797, 45], [791, 48, 797, 46], [791, 49, 797, 47], [792, 10, 798, 8, "console"], [792, 17, 798, 15], [792, 18, 798, 16, "error"], [792, 23, 798, 21], [792, 24, 798, 22], [792, 68, 798, 66], [792, 70, 798, 68, "response"], [792, 78, 798, 76], [792, 79, 798, 77, "status"], [792, 85, 798, 83], [792, 87, 798, 85, "errorText"], [792, 96, 798, 94], [792, 97, 798, 95], [793, 10, 799, 8], [793, 16, 799, 14], [793, 20, 799, 18, "Error"], [793, 25, 799, 23], [793, 26, 799, 24], [793, 48, 799, 46, "response"], [793, 56, 799, 54], [793, 57, 799, 55, "status"], [793, 63, 799, 61], [793, 67, 799, 65, "response"], [793, 75, 799, 73], [793, 76, 799, 74, "statusText"], [793, 86, 799, 84], [793, 88, 799, 86], [793, 89, 799, 87], [794, 8, 800, 6], [795, 8, 802, 6], [795, 14, 802, 12, "result"], [795, 20, 802, 18], [795, 23, 802, 21], [795, 29, 802, 27, "response"], [795, 37, 802, 35], [795, 38, 802, 36, "json"], [795, 42, 802, 40], [795, 43, 802, 41], [795, 44, 802, 42], [796, 8, 803, 6, "console"], [796, 15, 803, 13], [796, 16, 803, 14, "log"], [796, 19, 803, 17], [796, 20, 803, 18], [796, 68, 803, 66], [796, 70, 803, 68, "result"], [796, 76, 803, 74], [796, 77, 803, 75], [797, 8, 805, 6], [797, 12, 805, 10], [797, 13, 805, 11, "result"], [797, 19, 805, 17], [797, 20, 805, 18, "jobId"], [797, 25, 805, 23], [797, 27, 805, 25], [798, 10, 806, 8], [798, 16, 806, 14], [798, 20, 806, 18, "Error"], [798, 25, 806, 23], [798, 26, 806, 24], [798, 70, 806, 68], [798, 71, 806, 69], [799, 8, 807, 6], [801, 8, 809, 6], [802, 8, 810, 6], [802, 14, 810, 12, "pollForCompletion"], [802, 31, 810, 29], [802, 32, 810, 30, "result"], [802, 38, 810, 36], [802, 39, 810, 37, "jobId"], [802, 44, 810, 42], [802, 46, 810, 44, "timestamp"], [802, 55, 810, 53], [802, 56, 810, 54], [803, 6, 811, 4], [803, 7, 811, 5], [803, 8, 811, 6], [803, 15, 811, 13, "error"], [803, 20, 811, 18], [803, 22, 811, 20], [804, 8, 812, 6, "console"], [804, 15, 812, 13], [804, 16, 812, 14, "error"], [804, 21, 812, 19], [804, 22, 812, 20], [804, 57, 812, 55], [804, 59, 812, 57, "error"], [804, 64, 812, 62], [804, 65, 812, 63], [805, 8, 813, 6, "setErrorMessage"], [805, 23, 813, 21], [805, 24, 813, 22], [805, 52, 813, 50, "error"], [805, 57, 813, 55], [805, 58, 813, 56, "message"], [805, 65, 813, 63], [805, 67, 813, 65], [805, 68, 813, 66], [806, 8, 814, 6, "setProcessingState"], [806, 26, 814, 24], [806, 27, 814, 25], [806, 34, 814, 32], [806, 35, 814, 33], [807, 6, 815, 4], [808, 4, 816, 2], [808, 5, 816, 3], [809, 4, 817, 2], [810, 4, 818, 2], [810, 10, 818, 8, "pollForCompletion"], [810, 27, 818, 25], [810, 30, 818, 28], [810, 36, 818, 28, "pollForCompletion"], [810, 37, 818, 35, "jobId"], [810, 42, 818, 48], [810, 44, 818, 50, "timestamp"], [810, 53, 818, 67], [810, 55, 818, 69, "attempts"], [810, 63, 818, 77], [810, 66, 818, 80], [810, 67, 818, 81], [810, 72, 818, 86], [811, 6, 819, 4], [811, 12, 819, 10, "MAX_ATTEMPTS"], [811, 24, 819, 22], [811, 27, 819, 25], [811, 29, 819, 27], [811, 30, 819, 28], [811, 31, 819, 29], [812, 6, 820, 4], [812, 12, 820, 10, "POLL_INTERVAL"], [812, 25, 820, 23], [812, 28, 820, 26], [812, 32, 820, 30], [812, 33, 820, 31], [812, 34, 820, 32], [814, 6, 822, 4, "console"], [814, 13, 822, 11], [814, 14, 822, 12, "log"], [814, 17, 822, 15], [814, 18, 822, 16], [814, 53, 822, 51, "attempts"], [814, 61, 822, 59], [814, 64, 822, 62], [814, 65, 822, 63], [814, 69, 822, 67, "MAX_ATTEMPTS"], [814, 81, 822, 79], [814, 93, 822, 91, "jobId"], [814, 98, 822, 96], [814, 100, 822, 98], [814, 101, 822, 99], [815, 6, 824, 4], [815, 10, 824, 8, "attempts"], [815, 18, 824, 16], [815, 22, 824, 20, "MAX_ATTEMPTS"], [815, 34, 824, 32], [815, 36, 824, 34], [816, 8, 825, 6, "console"], [816, 15, 825, 13], [816, 16, 825, 14, "error"], [816, 21, 825, 19], [816, 22, 825, 20], [816, 75, 825, 73], [816, 76, 825, 74], [817, 8, 826, 6, "setErrorMessage"], [817, 23, 826, 21], [817, 24, 826, 22], [817, 63, 826, 61], [817, 64, 826, 62], [818, 8, 827, 6, "setProcessingState"], [818, 26, 827, 24], [818, 27, 827, 25], [818, 34, 827, 32], [818, 35, 827, 33], [819, 8, 828, 6], [820, 6, 829, 4], [821, 6, 831, 4], [821, 10, 831, 8], [822, 8, 832, 6], [822, 14, 832, 12, "response"], [822, 22, 832, 20], [822, 25, 832, 23], [822, 31, 832, 29, "fetch"], [822, 36, 832, 34], [822, 37, 832, 35], [822, 40, 832, 38, "API_BASE_URL"], [822, 52, 832, 50], [822, 75, 832, 73, "jobId"], [822, 80, 832, 78], [822, 82, 832, 80], [822, 84, 832, 82], [823, 10, 833, 8, "headers"], [823, 17, 833, 15], [823, 19, 833, 17], [824, 12, 834, 10], [824, 27, 834, 25], [824, 29, 834, 27], [824, 39, 834, 37], [824, 45, 834, 43, "getAuthToken"], [824, 57, 834, 55], [824, 58, 834, 56], [824, 59, 834, 57], [825, 10, 835, 8], [826, 8, 836, 6], [826, 9, 836, 7], [826, 10, 836, 8], [827, 8, 838, 6], [827, 12, 838, 10], [827, 13, 838, 11, "response"], [827, 21, 838, 19], [827, 22, 838, 20, "ok"], [827, 24, 838, 22], [827, 26, 838, 24], [828, 10, 839, 8], [828, 16, 839, 14], [828, 20, 839, 18, "Error"], [828, 25, 839, 23], [828, 26, 839, 24], [828, 34, 839, 32, "response"], [828, 42, 839, 40], [828, 43, 839, 41, "status"], [828, 49, 839, 47], [828, 54, 839, 52, "response"], [828, 62, 839, 60], [828, 63, 839, 61, "statusText"], [828, 73, 839, 71], [828, 75, 839, 73], [828, 76, 839, 74], [829, 8, 840, 6], [830, 8, 842, 6], [830, 14, 842, 12, "status"], [830, 20, 842, 18], [830, 23, 842, 21], [830, 29, 842, 27, "response"], [830, 37, 842, 35], [830, 38, 842, 36, "json"], [830, 42, 842, 40], [830, 43, 842, 41], [830, 44, 842, 42], [831, 8, 843, 6, "console"], [831, 15, 843, 13], [831, 16, 843, 14, "log"], [831, 19, 843, 17], [831, 20, 843, 18], [831, 54, 843, 52], [831, 56, 843, 54, "status"], [831, 62, 843, 60], [831, 63, 843, 61], [832, 8, 845, 6], [832, 12, 845, 10, "status"], [832, 18, 845, 16], [832, 19, 845, 17, "status"], [832, 25, 845, 23], [832, 30, 845, 28], [832, 41, 845, 39], [832, 43, 845, 41], [833, 10, 846, 8, "console"], [833, 17, 846, 15], [833, 18, 846, 16, "log"], [833, 21, 846, 19], [833, 22, 846, 20], [833, 73, 846, 71], [833, 74, 846, 72], [834, 10, 847, 8, "setProcessingProgress"], [834, 31, 847, 29], [834, 32, 847, 30], [834, 35, 847, 33], [834, 36, 847, 34], [835, 10, 848, 8, "setProcessingState"], [835, 28, 848, 26], [835, 29, 848, 27], [835, 40, 848, 38], [835, 41, 848, 39], [836, 10, 849, 8], [837, 10, 850, 8], [837, 16, 850, 14, "result"], [837, 22, 850, 20], [837, 25, 850, 23], [838, 12, 851, 10, "imageUrl"], [838, 20, 851, 18], [838, 22, 851, 20, "status"], [838, 28, 851, 26], [838, 29, 851, 27, "publicUrl"], [838, 38, 851, 36], [839, 12, 851, 38], [840, 12, 852, 10, "localUri"], [840, 20, 852, 18], [840, 22, 852, 20, "capturedPhoto"], [840, 35, 852, 33], [840, 39, 852, 37, "status"], [840, 45, 852, 43], [840, 46, 852, 44, "publicUrl"], [840, 55, 852, 53], [841, 12, 852, 55], [842, 12, 853, 10, "challengeCode"], [842, 25, 853, 23], [842, 27, 853, 25, "challengeCode"], [842, 40, 853, 38], [842, 44, 853, 42], [842, 46, 853, 44], [843, 12, 854, 10, "timestamp"], [843, 21, 854, 19], [844, 12, 855, 10, "processingStatus"], [844, 28, 855, 26], [844, 30, 855, 28], [845, 10, 856, 8], [845, 11, 856, 9], [846, 10, 857, 8, "console"], [846, 17, 857, 15], [846, 18, 857, 16, "log"], [846, 21, 857, 19], [846, 22, 857, 20], [846, 57, 857, 55], [846, 59, 857, 57, "result"], [846, 65, 857, 63], [846, 66, 857, 64], [847, 10, 858, 8, "onComplete"], [847, 20, 858, 18], [847, 21, 858, 19, "result"], [847, 27, 858, 25], [847, 28, 858, 26], [848, 10, 859, 8], [849, 8, 860, 6], [849, 9, 860, 7], [849, 15, 860, 13], [849, 19, 860, 17, "status"], [849, 25, 860, 23], [849, 26, 860, 24, "status"], [849, 32, 860, 30], [849, 37, 860, 35], [849, 45, 860, 43], [849, 47, 860, 45], [850, 10, 861, 8, "console"], [850, 17, 861, 15], [850, 18, 861, 16, "error"], [850, 23, 861, 21], [850, 24, 861, 22], [850, 60, 861, 58], [850, 62, 861, 60, "status"], [850, 68, 861, 66], [850, 69, 861, 67, "error"], [850, 74, 861, 72], [850, 75, 861, 73], [851, 10, 862, 8], [851, 16, 862, 14], [851, 20, 862, 18, "Error"], [851, 25, 862, 23], [851, 26, 862, 24, "status"], [851, 32, 862, 30], [851, 33, 862, 31, "error"], [851, 38, 862, 36], [851, 42, 862, 40], [851, 61, 862, 59], [851, 62, 862, 60], [852, 8, 863, 6], [852, 9, 863, 7], [852, 15, 863, 13], [853, 10, 864, 8], [854, 10, 865, 8], [854, 16, 865, 14, "progressValue"], [854, 29, 865, 27], [854, 32, 865, 30], [854, 34, 865, 32], [854, 37, 865, 36, "attempts"], [854, 45, 865, 44], [854, 48, 865, 47, "MAX_ATTEMPTS"], [854, 60, 865, 59], [854, 63, 865, 63], [854, 65, 865, 65], [855, 10, 866, 8, "console"], [855, 17, 866, 15], [855, 18, 866, 16, "log"], [855, 21, 866, 19], [855, 22, 866, 20], [855, 71, 866, 69, "progressValue"], [855, 84, 866, 82], [855, 87, 866, 85], [855, 88, 866, 86], [856, 10, 867, 8, "setProcessingProgress"], [856, 31, 867, 29], [856, 32, 867, 30, "progressValue"], [856, 45, 867, 43], [856, 46, 867, 44], [857, 10, 869, 8, "setTimeout"], [857, 20, 869, 18], [857, 21, 869, 19], [857, 27, 869, 25], [858, 12, 870, 10, "pollForCompletion"], [858, 29, 870, 27], [858, 30, 870, 28, "jobId"], [858, 35, 870, 33], [858, 37, 870, 35, "timestamp"], [858, 46, 870, 44], [858, 48, 870, 46, "attempts"], [858, 56, 870, 54], [858, 59, 870, 57], [858, 60, 870, 58], [858, 61, 870, 59], [859, 10, 871, 8], [859, 11, 871, 9], [859, 13, 871, 11, "POLL_INTERVAL"], [859, 26, 871, 24], [859, 27, 871, 25], [860, 8, 872, 6], [861, 6, 873, 4], [861, 7, 873, 5], [861, 8, 873, 6], [861, 15, 873, 13, "error"], [861, 20, 873, 18], [861, 22, 873, 20], [862, 8, 874, 6, "console"], [862, 15, 874, 13], [862, 16, 874, 14, "error"], [862, 21, 874, 19], [862, 22, 874, 20], [862, 54, 874, 52], [862, 56, 874, 54, "error"], [862, 61, 874, 59], [862, 62, 874, 60], [863, 8, 875, 6, "setErrorMessage"], [863, 23, 875, 21], [863, 24, 875, 22], [863, 62, 875, 60, "error"], [863, 67, 875, 65], [863, 68, 875, 66, "message"], [863, 75, 875, 73], [863, 77, 875, 75], [863, 78, 875, 76], [864, 8, 876, 6, "setProcessingState"], [864, 26, 876, 24], [864, 27, 876, 25], [864, 34, 876, 32], [864, 35, 876, 33], [865, 6, 877, 4], [866, 4, 878, 2], [866, 5, 878, 3], [867, 4, 879, 2], [868, 4, 880, 2], [868, 10, 880, 8, "getAuthToken"], [868, 22, 880, 20], [868, 25, 880, 23], [868, 31, 880, 23, "getAuthToken"], [868, 32, 880, 23], [868, 37, 880, 52], [869, 6, 881, 4], [870, 6, 882, 4], [871, 6, 883, 4], [871, 13, 883, 11], [871, 30, 883, 28], [872, 4, 884, 2], [872, 5, 884, 3], [874, 4, 886, 2], [875, 4, 887, 2], [875, 10, 887, 8, "retryCapture"], [875, 22, 887, 20], [875, 25, 887, 23], [875, 29, 887, 23, "useCallback"], [875, 47, 887, 34], [875, 49, 887, 35], [875, 55, 887, 41], [876, 6, 888, 4, "console"], [876, 13, 888, 11], [876, 14, 888, 12, "log"], [876, 17, 888, 15], [876, 18, 888, 16], [876, 55, 888, 53], [876, 56, 888, 54], [877, 6, 889, 4, "setProcessingState"], [877, 24, 889, 22], [877, 25, 889, 23], [877, 31, 889, 29], [877, 32, 889, 30], [878, 6, 890, 4, "setErrorMessage"], [878, 21, 890, 19], [878, 22, 890, 20], [878, 24, 890, 22], [878, 25, 890, 23], [879, 6, 891, 4, "setCapturedPhoto"], [879, 22, 891, 20], [879, 23, 891, 21], [879, 25, 891, 23], [879, 26, 891, 24], [880, 6, 892, 4, "setProcessingProgress"], [880, 27, 892, 25], [880, 28, 892, 26], [880, 29, 892, 27], [880, 30, 892, 28], [881, 4, 893, 2], [881, 5, 893, 3], [881, 7, 893, 5], [881, 9, 893, 7], [881, 10, 893, 8], [882, 4, 894, 2], [883, 4, 895, 2], [883, 8, 895, 2, "useEffect"], [883, 24, 895, 11], [883, 26, 895, 12], [883, 32, 895, 18], [884, 6, 896, 4, "console"], [884, 13, 896, 11], [884, 14, 896, 12, "log"], [884, 17, 896, 15], [884, 18, 896, 16], [884, 53, 896, 51], [884, 55, 896, 53, "permission"], [884, 65, 896, 63], [884, 66, 896, 64], [885, 6, 897, 4], [885, 10, 897, 8, "permission"], [885, 20, 897, 18], [885, 22, 897, 20], [886, 8, 898, 6, "console"], [886, 15, 898, 13], [886, 16, 898, 14, "log"], [886, 19, 898, 17], [886, 20, 898, 18], [886, 57, 898, 55], [886, 59, 898, 57, "permission"], [886, 69, 898, 67], [886, 70, 898, 68, "granted"], [886, 77, 898, 75], [886, 78, 898, 76], [887, 6, 899, 4], [888, 4, 900, 2], [888, 5, 900, 3], [888, 7, 900, 5], [888, 8, 900, 6, "permission"], [888, 18, 900, 16], [888, 19, 900, 17], [888, 20, 900, 18], [889, 4, 901, 2], [890, 4, 902, 2], [890, 8, 902, 6], [890, 9, 902, 7, "permission"], [890, 19, 902, 17], [890, 21, 902, 19], [891, 6, 903, 4, "console"], [891, 13, 903, 11], [891, 14, 903, 12, "log"], [891, 17, 903, 15], [891, 18, 903, 16], [891, 67, 903, 65], [891, 68, 903, 66], [892, 6, 904, 4], [892, 26, 905, 6], [892, 30, 905, 6, "_jsxDevRuntime"], [892, 44, 905, 6], [892, 45, 905, 6, "jsxDEV"], [892, 51, 905, 6], [892, 53, 905, 7, "_View"], [892, 58, 905, 7], [892, 59, 905, 7, "default"], [892, 66, 905, 11], [893, 8, 905, 12, "style"], [893, 13, 905, 17], [893, 15, 905, 19, "styles"], [893, 21, 905, 25], [893, 22, 905, 26, "container"], [893, 31, 905, 36], [894, 8, 905, 36, "children"], [894, 16, 905, 36], [894, 32, 906, 8], [894, 36, 906, 8, "_jsxDevRuntime"], [894, 50, 906, 8], [894, 51, 906, 8, "jsxDEV"], [894, 57, 906, 8], [894, 59, 906, 9, "_ActivityIndicator"], [894, 77, 906, 9], [894, 78, 906, 9, "default"], [894, 85, 906, 26], [895, 10, 906, 27, "size"], [895, 14, 906, 31], [895, 16, 906, 32], [895, 23, 906, 39], [896, 10, 906, 40, "color"], [896, 15, 906, 45], [896, 17, 906, 46], [897, 8, 906, 55], [898, 10, 906, 55, "fileName"], [898, 18, 906, 55], [898, 20, 906, 55, "_jsxFileName"], [898, 32, 906, 55], [899, 10, 906, 55, "lineNumber"], [899, 20, 906, 55], [900, 10, 906, 55, "columnNumber"], [900, 22, 906, 55], [901, 8, 906, 55], [901, 15, 906, 57], [901, 16, 906, 58], [901, 31, 907, 8], [901, 35, 907, 8, "_jsxDevRuntime"], [901, 49, 907, 8], [901, 50, 907, 8, "jsxDEV"], [901, 56, 907, 8], [901, 58, 907, 9, "_Text"], [901, 63, 907, 9], [901, 64, 907, 9, "default"], [901, 71, 907, 13], [902, 10, 907, 14, "style"], [902, 15, 907, 19], [902, 17, 907, 21, "styles"], [902, 23, 907, 27], [902, 24, 907, 28, "loadingText"], [902, 35, 907, 40], [903, 10, 907, 40, "children"], [903, 18, 907, 40], [903, 20, 907, 41], [904, 8, 907, 58], [905, 10, 907, 58, "fileName"], [905, 18, 907, 58], [905, 20, 907, 58, "_jsxFileName"], [905, 32, 907, 58], [906, 10, 907, 58, "lineNumber"], [906, 20, 907, 58], [907, 10, 907, 58, "columnNumber"], [907, 22, 907, 58], [908, 8, 907, 58], [908, 15, 907, 64], [908, 16, 907, 65], [909, 6, 907, 65], [910, 8, 907, 65, "fileName"], [910, 16, 907, 65], [910, 18, 907, 65, "_jsxFileName"], [910, 30, 907, 65], [911, 8, 907, 65, "lineNumber"], [911, 18, 907, 65], [912, 8, 907, 65, "columnNumber"], [912, 20, 907, 65], [913, 6, 907, 65], [913, 13, 908, 12], [913, 14, 908, 13], [914, 4, 910, 2], [915, 4, 911, 2], [915, 8, 911, 6], [915, 9, 911, 7, "permission"], [915, 19, 911, 17], [915, 20, 911, 18, "granted"], [915, 27, 911, 25], [915, 29, 911, 27], [916, 6, 912, 4, "console"], [916, 13, 912, 11], [916, 14, 912, 12, "log"], [916, 17, 912, 15], [916, 18, 912, 16], [916, 93, 912, 91], [916, 94, 912, 92], [917, 6, 913, 4], [917, 26, 914, 6], [917, 30, 914, 6, "_jsxDevRuntime"], [917, 44, 914, 6], [917, 45, 914, 6, "jsxDEV"], [917, 51, 914, 6], [917, 53, 914, 7, "_View"], [917, 58, 914, 7], [917, 59, 914, 7, "default"], [917, 66, 914, 11], [918, 8, 914, 12, "style"], [918, 13, 914, 17], [918, 15, 914, 19, "styles"], [918, 21, 914, 25], [918, 22, 914, 26, "container"], [918, 31, 914, 36], [919, 8, 914, 36, "children"], [919, 16, 914, 36], [919, 31, 915, 8], [919, 35, 915, 8, "_jsxDevRuntime"], [919, 49, 915, 8], [919, 50, 915, 8, "jsxDEV"], [919, 56, 915, 8], [919, 58, 915, 9, "_View"], [919, 63, 915, 9], [919, 64, 915, 9, "default"], [919, 71, 915, 13], [920, 10, 915, 14, "style"], [920, 15, 915, 19], [920, 17, 915, 21, "styles"], [920, 23, 915, 27], [920, 24, 915, 28, "permissionContent"], [920, 41, 915, 46], [921, 10, 915, 46, "children"], [921, 18, 915, 46], [921, 34, 916, 10], [921, 38, 916, 10, "_jsxDevRuntime"], [921, 52, 916, 10], [921, 53, 916, 10, "jsxDEV"], [921, 59, 916, 10], [921, 61, 916, 11, "_lucideReactNative"], [921, 79, 916, 11], [921, 80, 916, 11, "Camera"], [921, 86, 916, 21], [922, 12, 916, 22, "size"], [922, 16, 916, 26], [922, 18, 916, 28], [922, 20, 916, 31], [923, 12, 916, 32, "color"], [923, 17, 916, 37], [923, 19, 916, 38], [924, 10, 916, 47], [925, 12, 916, 47, "fileName"], [925, 20, 916, 47], [925, 22, 916, 47, "_jsxFileName"], [925, 34, 916, 47], [926, 12, 916, 47, "lineNumber"], [926, 22, 916, 47], [927, 12, 916, 47, "columnNumber"], [927, 24, 916, 47], [928, 10, 916, 47], [928, 17, 916, 49], [928, 18, 916, 50], [928, 33, 917, 10], [928, 37, 917, 10, "_jsxDevRuntime"], [928, 51, 917, 10], [928, 52, 917, 10, "jsxDEV"], [928, 58, 917, 10], [928, 60, 917, 11, "_Text"], [928, 65, 917, 11], [928, 66, 917, 11, "default"], [928, 73, 917, 15], [929, 12, 917, 16, "style"], [929, 17, 917, 21], [929, 19, 917, 23, "styles"], [929, 25, 917, 29], [929, 26, 917, 30, "permissionTitle"], [929, 41, 917, 46], [930, 12, 917, 46, "children"], [930, 20, 917, 46], [930, 22, 917, 47], [931, 10, 917, 73], [932, 12, 917, 73, "fileName"], [932, 20, 917, 73], [932, 22, 917, 73, "_jsxFileName"], [932, 34, 917, 73], [933, 12, 917, 73, "lineNumber"], [933, 22, 917, 73], [934, 12, 917, 73, "columnNumber"], [934, 24, 917, 73], [935, 10, 917, 73], [935, 17, 917, 79], [935, 18, 917, 80], [935, 33, 918, 10], [935, 37, 918, 10, "_jsxDevRuntime"], [935, 51, 918, 10], [935, 52, 918, 10, "jsxDEV"], [935, 58, 918, 10], [935, 60, 918, 11, "_Text"], [935, 65, 918, 11], [935, 66, 918, 11, "default"], [935, 73, 918, 15], [936, 12, 918, 16, "style"], [936, 17, 918, 21], [936, 19, 918, 23, "styles"], [936, 25, 918, 29], [936, 26, 918, 30, "permissionDescription"], [936, 47, 918, 52], [937, 12, 918, 52, "children"], [937, 20, 918, 52], [937, 22, 918, 53], [938, 10, 921, 10], [939, 12, 921, 10, "fileName"], [939, 20, 921, 10], [939, 22, 921, 10, "_jsxFileName"], [939, 34, 921, 10], [940, 12, 921, 10, "lineNumber"], [940, 22, 921, 10], [941, 12, 921, 10, "columnNumber"], [941, 24, 921, 10], [942, 10, 921, 10], [942, 17, 921, 16], [942, 18, 921, 17], [942, 33, 922, 10], [942, 37, 922, 10, "_jsxDevRuntime"], [942, 51, 922, 10], [942, 52, 922, 10, "jsxDEV"], [942, 58, 922, 10], [942, 60, 922, 11, "_TouchableOpacity"], [942, 77, 922, 11], [942, 78, 922, 11, "default"], [942, 85, 922, 27], [943, 12, 922, 28, "onPress"], [943, 19, 922, 35], [943, 21, 922, 37, "requestPermission"], [943, 38, 922, 55], [944, 12, 922, 56, "style"], [944, 17, 922, 61], [944, 19, 922, 63, "styles"], [944, 25, 922, 69], [944, 26, 922, 70, "primaryButton"], [944, 39, 922, 84], [945, 12, 922, 84, "children"], [945, 20, 922, 84], [945, 35, 923, 12], [945, 39, 923, 12, "_jsxDevRuntime"], [945, 53, 923, 12], [945, 54, 923, 12, "jsxDEV"], [945, 60, 923, 12], [945, 62, 923, 13, "_Text"], [945, 67, 923, 13], [945, 68, 923, 13, "default"], [945, 75, 923, 17], [946, 14, 923, 18, "style"], [946, 19, 923, 23], [946, 21, 923, 25, "styles"], [946, 27, 923, 31], [946, 28, 923, 32, "primaryButtonText"], [946, 45, 923, 50], [947, 14, 923, 50, "children"], [947, 22, 923, 50], [947, 24, 923, 51], [948, 12, 923, 67], [949, 14, 923, 67, "fileName"], [949, 22, 923, 67], [949, 24, 923, 67, "_jsxFileName"], [949, 36, 923, 67], [950, 14, 923, 67, "lineNumber"], [950, 24, 923, 67], [951, 14, 923, 67, "columnNumber"], [951, 26, 923, 67], [952, 12, 923, 67], [952, 19, 923, 73], [953, 10, 923, 74], [954, 12, 923, 74, "fileName"], [954, 20, 923, 74], [954, 22, 923, 74, "_jsxFileName"], [954, 34, 923, 74], [955, 12, 923, 74, "lineNumber"], [955, 22, 923, 74], [956, 12, 923, 74, "columnNumber"], [956, 24, 923, 74], [957, 10, 923, 74], [957, 17, 924, 28], [957, 18, 924, 29], [957, 33, 925, 10], [957, 37, 925, 10, "_jsxDevRuntime"], [957, 51, 925, 10], [957, 52, 925, 10, "jsxDEV"], [957, 58, 925, 10], [957, 60, 925, 11, "_TouchableOpacity"], [957, 77, 925, 11], [957, 78, 925, 11, "default"], [957, 85, 925, 27], [958, 12, 925, 28, "onPress"], [958, 19, 925, 35], [958, 21, 925, 37, "onCancel"], [958, 29, 925, 46], [959, 12, 925, 47, "style"], [959, 17, 925, 52], [959, 19, 925, 54, "styles"], [959, 25, 925, 60], [959, 26, 925, 61, "secondaryButton"], [959, 41, 925, 77], [960, 12, 925, 77, "children"], [960, 20, 925, 77], [960, 35, 926, 12], [960, 39, 926, 12, "_jsxDevRuntime"], [960, 53, 926, 12], [960, 54, 926, 12, "jsxDEV"], [960, 60, 926, 12], [960, 62, 926, 13, "_Text"], [960, 67, 926, 13], [960, 68, 926, 13, "default"], [960, 75, 926, 17], [961, 14, 926, 18, "style"], [961, 19, 926, 23], [961, 21, 926, 25, "styles"], [961, 27, 926, 31], [961, 28, 926, 32, "secondaryButtonText"], [961, 47, 926, 52], [962, 14, 926, 52, "children"], [962, 22, 926, 52], [962, 24, 926, 53], [963, 12, 926, 59], [964, 14, 926, 59, "fileName"], [964, 22, 926, 59], [964, 24, 926, 59, "_jsxFileName"], [964, 36, 926, 59], [965, 14, 926, 59, "lineNumber"], [965, 24, 926, 59], [966, 14, 926, 59, "columnNumber"], [966, 26, 926, 59], [967, 12, 926, 59], [967, 19, 926, 65], [968, 10, 926, 66], [969, 12, 926, 66, "fileName"], [969, 20, 926, 66], [969, 22, 926, 66, "_jsxFileName"], [969, 34, 926, 66], [970, 12, 926, 66, "lineNumber"], [970, 22, 926, 66], [971, 12, 926, 66, "columnNumber"], [971, 24, 926, 66], [972, 10, 926, 66], [972, 17, 927, 28], [972, 18, 927, 29], [973, 8, 927, 29], [974, 10, 927, 29, "fileName"], [974, 18, 927, 29], [974, 20, 927, 29, "_jsxFileName"], [974, 32, 927, 29], [975, 10, 927, 29, "lineNumber"], [975, 20, 927, 29], [976, 10, 927, 29, "columnNumber"], [976, 22, 927, 29], [977, 8, 927, 29], [977, 15, 928, 14], [978, 6, 928, 15], [979, 8, 928, 15, "fileName"], [979, 16, 928, 15], [979, 18, 928, 15, "_jsxFileName"], [979, 30, 928, 15], [980, 8, 928, 15, "lineNumber"], [980, 18, 928, 15], [981, 8, 928, 15, "columnNumber"], [981, 20, 928, 15], [982, 6, 928, 15], [982, 13, 929, 12], [982, 14, 929, 13], [983, 4, 931, 2], [984, 4, 932, 2], [985, 4, 933, 2, "console"], [985, 11, 933, 9], [985, 12, 933, 10, "log"], [985, 15, 933, 13], [985, 16, 933, 14], [985, 55, 933, 53], [985, 56, 933, 54], [986, 4, 935, 2], [986, 24, 936, 4], [986, 28, 936, 4, "_jsxDevRuntime"], [986, 42, 936, 4], [986, 43, 936, 4, "jsxDEV"], [986, 49, 936, 4], [986, 51, 936, 5, "_View"], [986, 56, 936, 5], [986, 57, 936, 5, "default"], [986, 64, 936, 9], [987, 6, 936, 10, "style"], [987, 11, 936, 15], [987, 13, 936, 17, "styles"], [987, 19, 936, 23], [987, 20, 936, 24, "container"], [987, 29, 936, 34], [988, 6, 936, 34, "children"], [988, 14, 936, 34], [988, 30, 938, 6], [988, 34, 938, 6, "_jsxDevRuntime"], [988, 48, 938, 6], [988, 49, 938, 6, "jsxDEV"], [988, 55, 938, 6], [988, 57, 938, 7, "_View"], [988, 62, 938, 7], [988, 63, 938, 7, "default"], [988, 70, 938, 11], [989, 8, 938, 12, "style"], [989, 13, 938, 17], [989, 15, 938, 19, "styles"], [989, 21, 938, 25], [989, 22, 938, 26, "cameraContainer"], [989, 37, 938, 42], [990, 8, 938, 43, "id"], [990, 10, 938, 45], [990, 12, 938, 46], [990, 29, 938, 63], [991, 8, 938, 63, "children"], [991, 16, 938, 63], [991, 32, 939, 8], [991, 36, 939, 8, "_jsxDevRuntime"], [991, 50, 939, 8], [991, 51, 939, 8, "jsxDEV"], [991, 57, 939, 8], [991, 59, 939, 9, "_expoCamera"], [991, 70, 939, 9], [991, 71, 939, 9, "CameraView"], [991, 81, 939, 19], [992, 10, 940, 10, "ref"], [992, 13, 940, 13], [992, 15, 940, 15, "cameraRef"], [992, 24, 940, 25], [993, 10, 941, 10, "style"], [993, 15, 941, 15], [993, 17, 941, 17], [993, 18, 941, 18, "styles"], [993, 24, 941, 24], [993, 25, 941, 25, "camera"], [993, 31, 941, 31], [993, 33, 941, 33], [994, 12, 941, 35, "backgroundColor"], [994, 27, 941, 50], [994, 29, 941, 52], [995, 10, 941, 62], [995, 11, 941, 63], [995, 12, 941, 65], [996, 10, 942, 10, "facing"], [996, 16, 942, 16], [996, 18, 942, 17], [996, 24, 942, 23], [997, 10, 943, 10, "onLayout"], [997, 18, 943, 18], [997, 20, 943, 21, "e"], [997, 21, 943, 22], [997, 25, 943, 27], [998, 12, 944, 12, "console"], [998, 19, 944, 19], [998, 20, 944, 20, "log"], [998, 23, 944, 23], [998, 24, 944, 24], [998, 56, 944, 56], [998, 58, 944, 58, "e"], [998, 59, 944, 59], [998, 60, 944, 60, "nativeEvent"], [998, 71, 944, 71], [998, 72, 944, 72, "layout"], [998, 78, 944, 78], [998, 79, 944, 79], [999, 12, 945, 12, "setViewSize"], [999, 23, 945, 23], [999, 24, 945, 24], [1000, 14, 945, 26, "width"], [1000, 19, 945, 31], [1000, 21, 945, 33, "e"], [1000, 22, 945, 34], [1000, 23, 945, 35, "nativeEvent"], [1000, 34, 945, 46], [1000, 35, 945, 47, "layout"], [1000, 41, 945, 53], [1000, 42, 945, 54, "width"], [1000, 47, 945, 59], [1001, 14, 945, 61, "height"], [1001, 20, 945, 67], [1001, 22, 945, 69, "e"], [1001, 23, 945, 70], [1001, 24, 945, 71, "nativeEvent"], [1001, 35, 945, 82], [1001, 36, 945, 83, "layout"], [1001, 42, 945, 89], [1001, 43, 945, 90, "height"], [1002, 12, 945, 97], [1002, 13, 945, 98], [1002, 14, 945, 99], [1003, 10, 946, 10], [1003, 11, 946, 12], [1004, 10, 947, 10, "onCameraReady"], [1004, 23, 947, 23], [1004, 25, 947, 25, "onCameraReady"], [1004, 26, 947, 25], [1004, 31, 947, 31], [1005, 12, 948, 12, "console"], [1005, 19, 948, 19], [1005, 20, 948, 20, "log"], [1005, 23, 948, 23], [1005, 24, 948, 24], [1005, 55, 948, 55], [1005, 56, 948, 56], [1006, 12, 949, 12, "setIsCameraReady"], [1006, 28, 949, 28], [1006, 29, 949, 29], [1006, 33, 949, 33], [1006, 34, 949, 34], [1006, 35, 949, 35], [1006, 36, 949, 36], [1007, 10, 950, 10], [1007, 11, 950, 12], [1008, 10, 951, 10, "onMountError"], [1008, 22, 951, 22], [1008, 24, 951, 25, "error"], [1008, 29, 951, 30], [1008, 33, 951, 35], [1009, 12, 952, 12, "console"], [1009, 19, 952, 19], [1009, 20, 952, 20, "error"], [1009, 25, 952, 25], [1009, 26, 952, 26], [1009, 63, 952, 63], [1009, 65, 952, 65, "error"], [1009, 70, 952, 70], [1009, 71, 952, 71], [1010, 12, 953, 12, "setErrorMessage"], [1010, 27, 953, 27], [1010, 28, 953, 28], [1010, 57, 953, 57], [1010, 58, 953, 58], [1011, 12, 954, 12, "setProcessingState"], [1011, 30, 954, 30], [1011, 31, 954, 31], [1011, 38, 954, 38], [1011, 39, 954, 39], [1012, 10, 955, 10], [1013, 8, 955, 12], [1014, 10, 955, 12, "fileName"], [1014, 18, 955, 12], [1014, 20, 955, 12, "_jsxFileName"], [1014, 32, 955, 12], [1015, 10, 955, 12, "lineNumber"], [1015, 20, 955, 12], [1016, 10, 955, 12, "columnNumber"], [1016, 22, 955, 12], [1017, 8, 955, 12], [1017, 15, 956, 9], [1017, 16, 956, 10], [1017, 18, 958, 9], [1017, 19, 958, 10, "isCameraReady"], [1017, 32, 958, 23], [1017, 49, 959, 10], [1017, 53, 959, 10, "_jsxDevRuntime"], [1017, 67, 959, 10], [1017, 68, 959, 10, "jsxDEV"], [1017, 74, 959, 10], [1017, 76, 959, 11, "_View"], [1017, 81, 959, 11], [1017, 82, 959, 11, "default"], [1017, 89, 959, 15], [1018, 10, 959, 16, "style"], [1018, 15, 959, 21], [1018, 17, 959, 23], [1018, 18, 959, 24, "StyleSheet"], [1018, 37, 959, 34], [1018, 38, 959, 35, "absoluteFill"], [1018, 50, 959, 47], [1018, 52, 959, 49], [1019, 12, 959, 51, "backgroundColor"], [1019, 27, 959, 66], [1019, 29, 959, 68], [1019, 49, 959, 88], [1020, 12, 959, 90, "justifyContent"], [1020, 26, 959, 104], [1020, 28, 959, 106], [1020, 36, 959, 114], [1021, 12, 959, 116, "alignItems"], [1021, 22, 959, 126], [1021, 24, 959, 128], [1021, 32, 959, 136], [1022, 12, 959, 138, "zIndex"], [1022, 18, 959, 144], [1022, 20, 959, 146], [1023, 10, 959, 151], [1023, 11, 959, 152], [1023, 12, 959, 154], [1024, 10, 959, 154, "children"], [1024, 18, 959, 154], [1024, 33, 960, 12], [1024, 37, 960, 12, "_jsxDevRuntime"], [1024, 51, 960, 12], [1024, 52, 960, 12, "jsxDEV"], [1024, 58, 960, 12], [1024, 60, 960, 13, "_View"], [1024, 65, 960, 13], [1024, 66, 960, 13, "default"], [1024, 73, 960, 17], [1025, 12, 960, 18, "style"], [1025, 17, 960, 23], [1025, 19, 960, 25], [1026, 14, 960, 27, "backgroundColor"], [1026, 29, 960, 42], [1026, 31, 960, 44], [1026, 51, 960, 64], [1027, 14, 960, 66, "padding"], [1027, 21, 960, 73], [1027, 23, 960, 75], [1027, 25, 960, 77], [1028, 14, 960, 79, "borderRadius"], [1028, 26, 960, 91], [1028, 28, 960, 93], [1028, 30, 960, 95], [1029, 14, 960, 97, "alignItems"], [1029, 24, 960, 107], [1029, 26, 960, 109], [1030, 12, 960, 118], [1030, 13, 960, 120], [1031, 12, 960, 120, "children"], [1031, 20, 960, 120], [1031, 36, 961, 14], [1031, 40, 961, 14, "_jsxDevRuntime"], [1031, 54, 961, 14], [1031, 55, 961, 14, "jsxDEV"], [1031, 61, 961, 14], [1031, 63, 961, 15, "_ActivityIndicator"], [1031, 81, 961, 15], [1031, 82, 961, 15, "default"], [1031, 89, 961, 32], [1032, 14, 961, 33, "size"], [1032, 18, 961, 37], [1032, 20, 961, 38], [1032, 27, 961, 45], [1033, 14, 961, 46, "color"], [1033, 19, 961, 51], [1033, 21, 961, 52], [1033, 30, 961, 61], [1034, 14, 961, 62, "style"], [1034, 19, 961, 67], [1034, 21, 961, 69], [1035, 16, 961, 71, "marginBottom"], [1035, 28, 961, 83], [1035, 30, 961, 85], [1036, 14, 961, 88], [1037, 12, 961, 90], [1038, 14, 961, 90, "fileName"], [1038, 22, 961, 90], [1038, 24, 961, 90, "_jsxFileName"], [1038, 36, 961, 90], [1039, 14, 961, 90, "lineNumber"], [1039, 24, 961, 90], [1040, 14, 961, 90, "columnNumber"], [1040, 26, 961, 90], [1041, 12, 961, 90], [1041, 19, 961, 92], [1041, 20, 961, 93], [1041, 35, 962, 14], [1041, 39, 962, 14, "_jsxDevRuntime"], [1041, 53, 962, 14], [1041, 54, 962, 14, "jsxDEV"], [1041, 60, 962, 14], [1041, 62, 962, 15, "_Text"], [1041, 67, 962, 15], [1041, 68, 962, 15, "default"], [1041, 75, 962, 19], [1042, 14, 962, 20, "style"], [1042, 19, 962, 25], [1042, 21, 962, 27], [1043, 16, 962, 29, "color"], [1043, 21, 962, 34], [1043, 23, 962, 36], [1043, 29, 962, 42], [1044, 16, 962, 44, "fontSize"], [1044, 24, 962, 52], [1044, 26, 962, 54], [1044, 28, 962, 56], [1045, 16, 962, 58, "fontWeight"], [1045, 26, 962, 68], [1045, 28, 962, 70], [1046, 14, 962, 76], [1046, 15, 962, 78], [1047, 14, 962, 78, "children"], [1047, 22, 962, 78], [1047, 24, 962, 79], [1048, 12, 962, 101], [1049, 14, 962, 101, "fileName"], [1049, 22, 962, 101], [1049, 24, 962, 101, "_jsxFileName"], [1049, 36, 962, 101], [1050, 14, 962, 101, "lineNumber"], [1050, 24, 962, 101], [1051, 14, 962, 101, "columnNumber"], [1051, 26, 962, 101], [1052, 12, 962, 101], [1052, 19, 962, 107], [1052, 20, 962, 108], [1052, 35, 963, 14], [1052, 39, 963, 14, "_jsxDevRuntime"], [1052, 53, 963, 14], [1052, 54, 963, 14, "jsxDEV"], [1052, 60, 963, 14], [1052, 62, 963, 15, "_Text"], [1052, 67, 963, 15], [1052, 68, 963, 15, "default"], [1052, 75, 963, 19], [1053, 14, 963, 20, "style"], [1053, 19, 963, 25], [1053, 21, 963, 27], [1054, 16, 963, 29, "color"], [1054, 21, 963, 34], [1054, 23, 963, 36], [1054, 32, 963, 45], [1055, 16, 963, 47, "fontSize"], [1055, 24, 963, 55], [1055, 26, 963, 57], [1055, 28, 963, 59], [1056, 16, 963, 61, "marginTop"], [1056, 25, 963, 70], [1056, 27, 963, 72], [1057, 14, 963, 74], [1057, 15, 963, 76], [1058, 14, 963, 76, "children"], [1058, 22, 963, 76], [1058, 24, 963, 77], [1059, 12, 963, 88], [1060, 14, 963, 88, "fileName"], [1060, 22, 963, 88], [1060, 24, 963, 88, "_jsxFileName"], [1060, 36, 963, 88], [1061, 14, 963, 88, "lineNumber"], [1061, 24, 963, 88], [1062, 14, 963, 88, "columnNumber"], [1062, 26, 963, 88], [1063, 12, 963, 88], [1063, 19, 963, 94], [1063, 20, 963, 95], [1064, 10, 963, 95], [1065, 12, 963, 95, "fileName"], [1065, 20, 963, 95], [1065, 22, 963, 95, "_jsxFileName"], [1065, 34, 963, 95], [1066, 12, 963, 95, "lineNumber"], [1066, 22, 963, 95], [1067, 12, 963, 95, "columnNumber"], [1067, 24, 963, 95], [1068, 10, 963, 95], [1068, 17, 964, 18], [1069, 8, 964, 19], [1070, 10, 964, 19, "fileName"], [1070, 18, 964, 19], [1070, 20, 964, 19, "_jsxFileName"], [1070, 32, 964, 19], [1071, 10, 964, 19, "lineNumber"], [1071, 20, 964, 19], [1072, 10, 964, 19, "columnNumber"], [1072, 22, 964, 19], [1073, 8, 964, 19], [1073, 15, 965, 16], [1073, 16, 966, 9], [1073, 18, 969, 9, "isCameraReady"], [1073, 31, 969, 22], [1073, 35, 969, 26, "previewBlurEnabled"], [1073, 53, 969, 44], [1073, 57, 969, 48, "viewSize"], [1073, 65, 969, 56], [1073, 66, 969, 57, "width"], [1073, 71, 969, 62], [1073, 74, 969, 65], [1073, 75, 969, 66], [1073, 92, 970, 10], [1073, 96, 970, 10, "_jsxDevRuntime"], [1073, 110, 970, 10], [1073, 111, 970, 10, "jsxDEV"], [1073, 117, 970, 10], [1073, 119, 970, 10, "_jsxDevRuntime"], [1073, 133, 970, 10], [1073, 134, 970, 10, "Fragment"], [1073, 142, 970, 10], [1074, 10, 970, 10, "children"], [1074, 18, 970, 10], [1074, 34, 972, 12], [1074, 38, 972, 12, "_jsxDevRuntime"], [1074, 52, 972, 12], [1074, 53, 972, 12, "jsxDEV"], [1074, 59, 972, 12], [1074, 61, 972, 13, "_LiveFaceCanvas"], [1074, 76, 972, 13], [1074, 77, 972, 13, "default"], [1074, 84, 972, 27], [1075, 12, 972, 28, "containerId"], [1075, 23, 972, 39], [1075, 25, 972, 40], [1075, 42, 972, 57], [1076, 12, 972, 58, "width"], [1076, 17, 972, 63], [1076, 19, 972, 65, "viewSize"], [1076, 27, 972, 73], [1076, 28, 972, 74, "width"], [1076, 33, 972, 80], [1077, 12, 972, 81, "height"], [1077, 18, 972, 87], [1077, 20, 972, 89, "viewSize"], [1077, 28, 972, 97], [1077, 29, 972, 98, "height"], [1078, 10, 972, 105], [1079, 12, 972, 105, "fileName"], [1079, 20, 972, 105], [1079, 22, 972, 105, "_jsxFileName"], [1079, 34, 972, 105], [1080, 12, 972, 105, "lineNumber"], [1080, 22, 972, 105], [1081, 12, 972, 105, "columnNumber"], [1081, 24, 972, 105], [1082, 10, 972, 105], [1082, 17, 972, 107], [1082, 18, 972, 108], [1082, 33, 973, 12], [1082, 37, 973, 12, "_jsxDevRuntime"], [1082, 51, 973, 12], [1082, 52, 973, 12, "jsxDEV"], [1082, 58, 973, 12], [1082, 60, 973, 13, "_View"], [1082, 65, 973, 13], [1082, 66, 973, 13, "default"], [1082, 73, 973, 17], [1083, 12, 973, 18, "style"], [1083, 17, 973, 23], [1083, 19, 973, 25], [1083, 20, 973, 26, "StyleSheet"], [1083, 39, 973, 36], [1083, 40, 973, 37, "absoluteFill"], [1083, 52, 973, 49], [1083, 54, 973, 51], [1084, 14, 973, 53, "pointerEvents"], [1084, 27, 973, 66], [1084, 29, 973, 68], [1085, 12, 973, 75], [1085, 13, 973, 76], [1085, 14, 973, 78], [1086, 12, 973, 78, "children"], [1086, 20, 973, 78], [1086, 36, 975, 12], [1086, 40, 975, 12, "_jsxDevRuntime"], [1086, 54, 975, 12], [1086, 55, 975, 12, "jsxDEV"], [1086, 61, 975, 12], [1086, 63, 975, 13, "_expoBlur"], [1086, 72, 975, 13], [1086, 73, 975, 13, "BlurView"], [1086, 81, 975, 21], [1087, 14, 975, 22, "intensity"], [1087, 23, 975, 31], [1087, 25, 975, 33], [1087, 27, 975, 36], [1088, 14, 975, 37, "tint"], [1088, 18, 975, 41], [1088, 20, 975, 42], [1088, 26, 975, 48], [1089, 14, 975, 49, "style"], [1089, 19, 975, 54], [1089, 21, 975, 56], [1089, 22, 975, 57, "styles"], [1089, 28, 975, 63], [1089, 29, 975, 64, "blurZone"], [1089, 37, 975, 72], [1089, 39, 975, 74], [1090, 16, 976, 14, "left"], [1090, 20, 976, 18], [1090, 22, 976, 20], [1090, 23, 976, 21], [1091, 16, 977, 14, "top"], [1091, 19, 977, 17], [1091, 21, 977, 19, "viewSize"], [1091, 29, 977, 27], [1091, 30, 977, 28, "height"], [1091, 36, 977, 34], [1091, 39, 977, 37], [1091, 42, 977, 40], [1092, 16, 978, 14, "width"], [1092, 21, 978, 19], [1092, 23, 978, 21, "viewSize"], [1092, 31, 978, 29], [1092, 32, 978, 30, "width"], [1092, 37, 978, 35], [1093, 16, 979, 14, "height"], [1093, 22, 979, 20], [1093, 24, 979, 22, "viewSize"], [1093, 32, 979, 30], [1093, 33, 979, 31, "height"], [1093, 39, 979, 37], [1093, 42, 979, 40], [1093, 46, 979, 44], [1094, 16, 980, 14, "borderRadius"], [1094, 28, 980, 26], [1094, 30, 980, 28], [1095, 14, 981, 12], [1095, 15, 981, 13], [1096, 12, 981, 15], [1097, 14, 981, 15, "fileName"], [1097, 22, 981, 15], [1097, 24, 981, 15, "_jsxFileName"], [1097, 36, 981, 15], [1098, 14, 981, 15, "lineNumber"], [1098, 24, 981, 15], [1099, 14, 981, 15, "columnNumber"], [1099, 26, 981, 15], [1100, 12, 981, 15], [1100, 19, 981, 17], [1100, 20, 981, 18], [1100, 35, 983, 12], [1100, 39, 983, 12, "_jsxDevRuntime"], [1100, 53, 983, 12], [1100, 54, 983, 12, "jsxDEV"], [1100, 60, 983, 12], [1100, 62, 983, 13, "_expoBlur"], [1100, 71, 983, 13], [1100, 72, 983, 13, "BlurView"], [1100, 80, 983, 21], [1101, 14, 983, 22, "intensity"], [1101, 23, 983, 31], [1101, 25, 983, 33], [1101, 27, 983, 36], [1102, 14, 983, 37, "tint"], [1102, 18, 983, 41], [1102, 20, 983, 42], [1102, 26, 983, 48], [1103, 14, 983, 49, "style"], [1103, 19, 983, 54], [1103, 21, 983, 56], [1103, 22, 983, 57, "styles"], [1103, 28, 983, 63], [1103, 29, 983, 64, "blurZone"], [1103, 37, 983, 72], [1103, 39, 983, 74], [1104, 16, 984, 14, "left"], [1104, 20, 984, 18], [1104, 22, 984, 20], [1104, 23, 984, 21], [1105, 16, 985, 14, "top"], [1105, 19, 985, 17], [1105, 21, 985, 19], [1105, 22, 985, 20], [1106, 16, 986, 14, "width"], [1106, 21, 986, 19], [1106, 23, 986, 21, "viewSize"], [1106, 31, 986, 29], [1106, 32, 986, 30, "width"], [1106, 37, 986, 35], [1107, 16, 987, 14, "height"], [1107, 22, 987, 20], [1107, 24, 987, 22, "viewSize"], [1107, 32, 987, 30], [1107, 33, 987, 31, "height"], [1107, 39, 987, 37], [1107, 42, 987, 40], [1107, 45, 987, 43], [1108, 16, 988, 14, "borderRadius"], [1108, 28, 988, 26], [1108, 30, 988, 28], [1109, 14, 989, 12], [1109, 15, 989, 13], [1110, 12, 989, 15], [1111, 14, 989, 15, "fileName"], [1111, 22, 989, 15], [1111, 24, 989, 15, "_jsxFileName"], [1111, 36, 989, 15], [1112, 14, 989, 15, "lineNumber"], [1112, 24, 989, 15], [1113, 14, 989, 15, "columnNumber"], [1113, 26, 989, 15], [1114, 12, 989, 15], [1114, 19, 989, 17], [1114, 20, 989, 18], [1114, 35, 991, 12], [1114, 39, 991, 12, "_jsxDevRuntime"], [1114, 53, 991, 12], [1114, 54, 991, 12, "jsxDEV"], [1114, 60, 991, 12], [1114, 62, 991, 13, "_expoBlur"], [1114, 71, 991, 13], [1114, 72, 991, 13, "BlurView"], [1114, 80, 991, 21], [1115, 14, 991, 22, "intensity"], [1115, 23, 991, 31], [1115, 25, 991, 33], [1115, 27, 991, 36], [1116, 14, 991, 37, "tint"], [1116, 18, 991, 41], [1116, 20, 991, 42], [1116, 26, 991, 48], [1117, 14, 991, 49, "style"], [1117, 19, 991, 54], [1117, 21, 991, 56], [1117, 22, 991, 57, "styles"], [1117, 28, 991, 63], [1117, 29, 991, 64, "blurZone"], [1117, 37, 991, 72], [1117, 39, 991, 74], [1118, 16, 992, 14, "left"], [1118, 20, 992, 18], [1118, 22, 992, 20, "viewSize"], [1118, 30, 992, 28], [1118, 31, 992, 29, "width"], [1118, 36, 992, 34], [1118, 39, 992, 37], [1118, 42, 992, 40], [1118, 45, 992, 44, "viewSize"], [1118, 53, 992, 52], [1118, 54, 992, 53, "width"], [1118, 59, 992, 58], [1118, 62, 992, 61], [1118, 66, 992, 66], [1119, 16, 993, 14, "top"], [1119, 19, 993, 17], [1119, 21, 993, 19, "viewSize"], [1119, 29, 993, 27], [1119, 30, 993, 28, "height"], [1119, 36, 993, 34], [1119, 39, 993, 37], [1119, 43, 993, 41], [1119, 46, 993, 45, "viewSize"], [1119, 54, 993, 53], [1119, 55, 993, 54, "width"], [1119, 60, 993, 59], [1119, 63, 993, 62], [1119, 67, 993, 67], [1120, 16, 994, 14, "width"], [1120, 21, 994, 19], [1120, 23, 994, 21, "viewSize"], [1120, 31, 994, 29], [1120, 32, 994, 30, "width"], [1120, 37, 994, 35], [1120, 40, 994, 38], [1120, 43, 994, 41], [1121, 16, 995, 14, "height"], [1121, 22, 995, 20], [1121, 24, 995, 22, "viewSize"], [1121, 32, 995, 30], [1121, 33, 995, 31, "width"], [1121, 38, 995, 36], [1121, 41, 995, 39], [1121, 44, 995, 42], [1122, 16, 996, 14, "borderRadius"], [1122, 28, 996, 26], [1122, 30, 996, 29, "viewSize"], [1122, 38, 996, 37], [1122, 39, 996, 38, "width"], [1122, 44, 996, 43], [1122, 47, 996, 46], [1122, 50, 996, 49], [1122, 53, 996, 53], [1123, 14, 997, 12], [1123, 15, 997, 13], [1124, 12, 997, 15], [1125, 14, 997, 15, "fileName"], [1125, 22, 997, 15], [1125, 24, 997, 15, "_jsxFileName"], [1125, 36, 997, 15], [1126, 14, 997, 15, "lineNumber"], [1126, 24, 997, 15], [1127, 14, 997, 15, "columnNumber"], [1127, 26, 997, 15], [1128, 12, 997, 15], [1128, 19, 997, 17], [1128, 20, 997, 18], [1128, 35, 998, 12], [1128, 39, 998, 12, "_jsxDevRuntime"], [1128, 53, 998, 12], [1128, 54, 998, 12, "jsxDEV"], [1128, 60, 998, 12], [1128, 62, 998, 13, "_expoBlur"], [1128, 71, 998, 13], [1128, 72, 998, 13, "BlurView"], [1128, 80, 998, 21], [1129, 14, 998, 22, "intensity"], [1129, 23, 998, 31], [1129, 25, 998, 33], [1129, 27, 998, 36], [1130, 14, 998, 37, "tint"], [1130, 18, 998, 41], [1130, 20, 998, 42], [1130, 26, 998, 48], [1131, 14, 998, 49, "style"], [1131, 19, 998, 54], [1131, 21, 998, 56], [1131, 22, 998, 57, "styles"], [1131, 28, 998, 63], [1131, 29, 998, 64, "blurZone"], [1131, 37, 998, 72], [1131, 39, 998, 74], [1132, 16, 999, 14, "left"], [1132, 20, 999, 18], [1132, 22, 999, 20, "viewSize"], [1132, 30, 999, 28], [1132, 31, 999, 29, "width"], [1132, 36, 999, 34], [1132, 39, 999, 37], [1132, 42, 999, 40], [1132, 45, 999, 44, "viewSize"], [1132, 53, 999, 52], [1132, 54, 999, 53, "width"], [1132, 59, 999, 58], [1132, 62, 999, 61], [1132, 66, 999, 66], [1133, 16, 1000, 14, "top"], [1133, 19, 1000, 17], [1133, 21, 1000, 19, "viewSize"], [1133, 29, 1000, 27], [1133, 30, 1000, 28, "height"], [1133, 36, 1000, 34], [1133, 39, 1000, 37], [1133, 42, 1000, 40], [1133, 45, 1000, 44, "viewSize"], [1133, 53, 1000, 52], [1133, 54, 1000, 53, "width"], [1133, 59, 1000, 58], [1133, 62, 1000, 61], [1133, 66, 1000, 66], [1134, 16, 1001, 14, "width"], [1134, 21, 1001, 19], [1134, 23, 1001, 21, "viewSize"], [1134, 31, 1001, 29], [1134, 32, 1001, 30, "width"], [1134, 37, 1001, 35], [1134, 40, 1001, 38], [1134, 43, 1001, 41], [1135, 16, 1002, 14, "height"], [1135, 22, 1002, 20], [1135, 24, 1002, 22, "viewSize"], [1135, 32, 1002, 30], [1135, 33, 1002, 31, "width"], [1135, 38, 1002, 36], [1135, 41, 1002, 39], [1135, 44, 1002, 42], [1136, 16, 1003, 14, "borderRadius"], [1136, 28, 1003, 26], [1136, 30, 1003, 29, "viewSize"], [1136, 38, 1003, 37], [1136, 39, 1003, 38, "width"], [1136, 44, 1003, 43], [1136, 47, 1003, 46], [1136, 50, 1003, 49], [1136, 53, 1003, 53], [1137, 14, 1004, 12], [1137, 15, 1004, 13], [1138, 12, 1004, 15], [1139, 14, 1004, 15, "fileName"], [1139, 22, 1004, 15], [1139, 24, 1004, 15, "_jsxFileName"], [1139, 36, 1004, 15], [1140, 14, 1004, 15, "lineNumber"], [1140, 24, 1004, 15], [1141, 14, 1004, 15, "columnNumber"], [1141, 26, 1004, 15], [1142, 12, 1004, 15], [1142, 19, 1004, 17], [1142, 20, 1004, 18], [1142, 35, 1005, 12], [1142, 39, 1005, 12, "_jsxDevRuntime"], [1142, 53, 1005, 12], [1142, 54, 1005, 12, "jsxDEV"], [1142, 60, 1005, 12], [1142, 62, 1005, 13, "_expoBlur"], [1142, 71, 1005, 13], [1142, 72, 1005, 13, "BlurView"], [1142, 80, 1005, 21], [1143, 14, 1005, 22, "intensity"], [1143, 23, 1005, 31], [1143, 25, 1005, 33], [1143, 27, 1005, 36], [1144, 14, 1005, 37, "tint"], [1144, 18, 1005, 41], [1144, 20, 1005, 42], [1144, 26, 1005, 48], [1145, 14, 1005, 49, "style"], [1145, 19, 1005, 54], [1145, 21, 1005, 56], [1145, 22, 1005, 57, "styles"], [1145, 28, 1005, 63], [1145, 29, 1005, 64, "blurZone"], [1145, 37, 1005, 72], [1145, 39, 1005, 74], [1146, 16, 1006, 14, "left"], [1146, 20, 1006, 18], [1146, 22, 1006, 20, "viewSize"], [1146, 30, 1006, 28], [1146, 31, 1006, 29, "width"], [1146, 36, 1006, 34], [1146, 39, 1006, 37], [1146, 42, 1006, 40], [1146, 45, 1006, 44, "viewSize"], [1146, 53, 1006, 52], [1146, 54, 1006, 53, "width"], [1146, 59, 1006, 58], [1146, 62, 1006, 61], [1146, 66, 1006, 66], [1147, 16, 1007, 14, "top"], [1147, 19, 1007, 17], [1147, 21, 1007, 19, "viewSize"], [1147, 29, 1007, 27], [1147, 30, 1007, 28, "height"], [1147, 36, 1007, 34], [1147, 39, 1007, 37], [1147, 42, 1007, 40], [1147, 45, 1007, 44, "viewSize"], [1147, 53, 1007, 52], [1147, 54, 1007, 53, "width"], [1147, 59, 1007, 58], [1147, 62, 1007, 61], [1147, 66, 1007, 66], [1148, 16, 1008, 14, "width"], [1148, 21, 1008, 19], [1148, 23, 1008, 21, "viewSize"], [1148, 31, 1008, 29], [1148, 32, 1008, 30, "width"], [1148, 37, 1008, 35], [1148, 40, 1008, 38], [1148, 43, 1008, 41], [1149, 16, 1009, 14, "height"], [1149, 22, 1009, 20], [1149, 24, 1009, 22, "viewSize"], [1149, 32, 1009, 30], [1149, 33, 1009, 31, "width"], [1149, 38, 1009, 36], [1149, 41, 1009, 39], [1149, 44, 1009, 42], [1150, 16, 1010, 14, "borderRadius"], [1150, 28, 1010, 26], [1150, 30, 1010, 29, "viewSize"], [1150, 38, 1010, 37], [1150, 39, 1010, 38, "width"], [1150, 44, 1010, 43], [1150, 47, 1010, 46], [1150, 50, 1010, 49], [1150, 53, 1010, 53], [1151, 14, 1011, 12], [1151, 15, 1011, 13], [1152, 12, 1011, 15], [1153, 14, 1011, 15, "fileName"], [1153, 22, 1011, 15], [1153, 24, 1011, 15, "_jsxFileName"], [1153, 36, 1011, 15], [1154, 14, 1011, 15, "lineNumber"], [1154, 24, 1011, 15], [1155, 14, 1011, 15, "columnNumber"], [1155, 26, 1011, 15], [1156, 12, 1011, 15], [1156, 19, 1011, 17], [1156, 20, 1011, 18], [1156, 22, 1013, 13, "__DEV__"], [1156, 29, 1013, 20], [1156, 46, 1014, 14], [1156, 50, 1014, 14, "_jsxDevRuntime"], [1156, 64, 1014, 14], [1156, 65, 1014, 14, "jsxDEV"], [1156, 71, 1014, 14], [1156, 73, 1014, 15, "_View"], [1156, 78, 1014, 15], [1156, 79, 1014, 15, "default"], [1156, 86, 1014, 19], [1157, 14, 1014, 20, "style"], [1157, 19, 1014, 25], [1157, 21, 1014, 27, "styles"], [1157, 27, 1014, 33], [1157, 28, 1014, 34, "previewChip"], [1157, 39, 1014, 46], [1158, 14, 1014, 46, "children"], [1158, 22, 1014, 46], [1158, 37, 1015, 16], [1158, 41, 1015, 16, "_jsxDevRuntime"], [1158, 55, 1015, 16], [1158, 56, 1015, 16, "jsxDEV"], [1158, 62, 1015, 16], [1158, 64, 1015, 17, "_Text"], [1158, 69, 1015, 17], [1158, 70, 1015, 17, "default"], [1158, 77, 1015, 21], [1159, 16, 1015, 22, "style"], [1159, 21, 1015, 27], [1159, 23, 1015, 29, "styles"], [1159, 29, 1015, 35], [1159, 30, 1015, 36, "previewChipText"], [1159, 45, 1015, 52], [1160, 16, 1015, 52, "children"], [1160, 24, 1015, 52], [1160, 26, 1015, 53], [1161, 14, 1015, 73], [1162, 16, 1015, 73, "fileName"], [1162, 24, 1015, 73], [1162, 26, 1015, 73, "_jsxFileName"], [1162, 38, 1015, 73], [1163, 16, 1015, 73, "lineNumber"], [1163, 26, 1015, 73], [1164, 16, 1015, 73, "columnNumber"], [1164, 28, 1015, 73], [1165, 14, 1015, 73], [1165, 21, 1015, 79], [1166, 12, 1015, 80], [1167, 14, 1015, 80, "fileName"], [1167, 22, 1015, 80], [1167, 24, 1015, 80, "_jsxFileName"], [1167, 36, 1015, 80], [1168, 14, 1015, 80, "lineNumber"], [1168, 24, 1015, 80], [1169, 14, 1015, 80, "columnNumber"], [1169, 26, 1015, 80], [1170, 12, 1015, 80], [1170, 19, 1016, 20], [1170, 20, 1017, 13], [1171, 10, 1017, 13], [1172, 12, 1017, 13, "fileName"], [1172, 20, 1017, 13], [1172, 22, 1017, 13, "_jsxFileName"], [1172, 34, 1017, 13], [1173, 12, 1017, 13, "lineNumber"], [1173, 22, 1017, 13], [1174, 12, 1017, 13, "columnNumber"], [1174, 24, 1017, 13], [1175, 10, 1017, 13], [1175, 17, 1018, 18], [1175, 18, 1018, 19], [1176, 8, 1018, 19], [1176, 23, 1019, 12], [1176, 24, 1020, 9], [1176, 26, 1022, 9, "isCameraReady"], [1176, 39, 1022, 22], [1176, 56, 1023, 10], [1176, 60, 1023, 10, "_jsxDevRuntime"], [1176, 74, 1023, 10], [1176, 75, 1023, 10, "jsxDEV"], [1176, 81, 1023, 10], [1176, 83, 1023, 10, "_jsxDevRuntime"], [1176, 97, 1023, 10], [1176, 98, 1023, 10, "Fragment"], [1176, 106, 1023, 10], [1177, 10, 1023, 10, "children"], [1177, 18, 1023, 10], [1177, 34, 1025, 12], [1177, 38, 1025, 12, "_jsxDevRuntime"], [1177, 52, 1025, 12], [1177, 53, 1025, 12, "jsxDEV"], [1177, 59, 1025, 12], [1177, 61, 1025, 13, "_View"], [1177, 66, 1025, 13], [1177, 67, 1025, 13, "default"], [1177, 74, 1025, 17], [1178, 12, 1025, 18, "style"], [1178, 17, 1025, 23], [1178, 19, 1025, 25, "styles"], [1178, 25, 1025, 31], [1178, 26, 1025, 32, "headerOverlay"], [1178, 39, 1025, 46], [1179, 12, 1025, 46, "children"], [1179, 20, 1025, 46], [1179, 35, 1026, 14], [1179, 39, 1026, 14, "_jsxDevRuntime"], [1179, 53, 1026, 14], [1179, 54, 1026, 14, "jsxDEV"], [1179, 60, 1026, 14], [1179, 62, 1026, 15, "_View"], [1179, 67, 1026, 15], [1179, 68, 1026, 15, "default"], [1179, 75, 1026, 19], [1180, 14, 1026, 20, "style"], [1180, 19, 1026, 25], [1180, 21, 1026, 27, "styles"], [1180, 27, 1026, 33], [1180, 28, 1026, 34, "headerContent"], [1180, 41, 1026, 48], [1181, 14, 1026, 48, "children"], [1181, 22, 1026, 48], [1181, 38, 1027, 16], [1181, 42, 1027, 16, "_jsxDevRuntime"], [1181, 56, 1027, 16], [1181, 57, 1027, 16, "jsxDEV"], [1181, 63, 1027, 16], [1181, 65, 1027, 17, "_View"], [1181, 70, 1027, 17], [1181, 71, 1027, 17, "default"], [1181, 78, 1027, 21], [1182, 16, 1027, 22, "style"], [1182, 21, 1027, 27], [1182, 23, 1027, 29, "styles"], [1182, 29, 1027, 35], [1182, 30, 1027, 36, "headerLeft"], [1182, 40, 1027, 47], [1183, 16, 1027, 47, "children"], [1183, 24, 1027, 47], [1183, 40, 1028, 18], [1183, 44, 1028, 18, "_jsxDevRuntime"], [1183, 58, 1028, 18], [1183, 59, 1028, 18, "jsxDEV"], [1183, 65, 1028, 18], [1183, 67, 1028, 19, "_Text"], [1183, 72, 1028, 19], [1183, 73, 1028, 19, "default"], [1183, 80, 1028, 23], [1184, 18, 1028, 24, "style"], [1184, 23, 1028, 29], [1184, 25, 1028, 31, "styles"], [1184, 31, 1028, 37], [1184, 32, 1028, 38, "headerTitle"], [1184, 43, 1028, 50], [1185, 18, 1028, 50, "children"], [1185, 26, 1028, 50], [1185, 28, 1028, 51], [1186, 16, 1028, 62], [1187, 18, 1028, 62, "fileName"], [1187, 26, 1028, 62], [1187, 28, 1028, 62, "_jsxFileName"], [1187, 40, 1028, 62], [1188, 18, 1028, 62, "lineNumber"], [1188, 28, 1028, 62], [1189, 18, 1028, 62, "columnNumber"], [1189, 30, 1028, 62], [1190, 16, 1028, 62], [1190, 23, 1028, 68], [1190, 24, 1028, 69], [1190, 39, 1029, 18], [1190, 43, 1029, 18, "_jsxDevRuntime"], [1190, 57, 1029, 18], [1190, 58, 1029, 18, "jsxDEV"], [1190, 64, 1029, 18], [1190, 66, 1029, 19, "_View"], [1190, 71, 1029, 19], [1190, 72, 1029, 19, "default"], [1190, 79, 1029, 23], [1191, 18, 1029, 24, "style"], [1191, 23, 1029, 29], [1191, 25, 1029, 31, "styles"], [1191, 31, 1029, 37], [1191, 32, 1029, 38, "subtitleRow"], [1191, 43, 1029, 50], [1192, 18, 1029, 50, "children"], [1192, 26, 1029, 50], [1192, 42, 1030, 20], [1192, 46, 1030, 20, "_jsxDevRuntime"], [1192, 60, 1030, 20], [1192, 61, 1030, 20, "jsxDEV"], [1192, 67, 1030, 20], [1192, 69, 1030, 21, "_Text"], [1192, 74, 1030, 21], [1192, 75, 1030, 21, "default"], [1192, 82, 1030, 25], [1193, 20, 1030, 26, "style"], [1193, 25, 1030, 31], [1193, 27, 1030, 33, "styles"], [1193, 33, 1030, 39], [1193, 34, 1030, 40, "webIcon"], [1193, 41, 1030, 48], [1194, 20, 1030, 48, "children"], [1194, 28, 1030, 48], [1194, 30, 1030, 49], [1195, 18, 1030, 51], [1196, 20, 1030, 51, "fileName"], [1196, 28, 1030, 51], [1196, 30, 1030, 51, "_jsxFileName"], [1196, 42, 1030, 51], [1197, 20, 1030, 51, "lineNumber"], [1197, 30, 1030, 51], [1198, 20, 1030, 51, "columnNumber"], [1198, 32, 1030, 51], [1199, 18, 1030, 51], [1199, 25, 1030, 57], [1199, 26, 1030, 58], [1199, 41, 1031, 20], [1199, 45, 1031, 20, "_jsxDevRuntime"], [1199, 59, 1031, 20], [1199, 60, 1031, 20, "jsxDEV"], [1199, 66, 1031, 20], [1199, 68, 1031, 21, "_Text"], [1199, 73, 1031, 21], [1199, 74, 1031, 21, "default"], [1199, 81, 1031, 25], [1200, 20, 1031, 26, "style"], [1200, 25, 1031, 31], [1200, 27, 1031, 33, "styles"], [1200, 33, 1031, 39], [1200, 34, 1031, 40, "headerSubtitle"], [1200, 48, 1031, 55], [1201, 20, 1031, 55, "children"], [1201, 28, 1031, 55], [1201, 30, 1031, 56], [1202, 18, 1031, 71], [1203, 20, 1031, 71, "fileName"], [1203, 28, 1031, 71], [1203, 30, 1031, 71, "_jsxFileName"], [1203, 42, 1031, 71], [1204, 20, 1031, 71, "lineNumber"], [1204, 30, 1031, 71], [1205, 20, 1031, 71, "columnNumber"], [1205, 32, 1031, 71], [1206, 18, 1031, 71], [1206, 25, 1031, 77], [1206, 26, 1031, 78], [1207, 16, 1031, 78], [1208, 18, 1031, 78, "fileName"], [1208, 26, 1031, 78], [1208, 28, 1031, 78, "_jsxFileName"], [1208, 40, 1031, 78], [1209, 18, 1031, 78, "lineNumber"], [1209, 28, 1031, 78], [1210, 18, 1031, 78, "columnNumber"], [1210, 30, 1031, 78], [1211, 16, 1031, 78], [1211, 23, 1032, 24], [1211, 24, 1032, 25], [1211, 26, 1033, 19, "challengeCode"], [1211, 39, 1033, 32], [1211, 56, 1034, 20], [1211, 60, 1034, 20, "_jsxDevRuntime"], [1211, 74, 1034, 20], [1211, 75, 1034, 20, "jsxDEV"], [1211, 81, 1034, 20], [1211, 83, 1034, 21, "_View"], [1211, 88, 1034, 21], [1211, 89, 1034, 21, "default"], [1211, 96, 1034, 25], [1212, 18, 1034, 26, "style"], [1212, 23, 1034, 31], [1212, 25, 1034, 33, "styles"], [1212, 31, 1034, 39], [1212, 32, 1034, 40, "challengeRow"], [1212, 44, 1034, 53], [1213, 18, 1034, 53, "children"], [1213, 26, 1034, 53], [1213, 42, 1035, 22], [1213, 46, 1035, 22, "_jsxDevRuntime"], [1213, 60, 1035, 22], [1213, 61, 1035, 22, "jsxDEV"], [1213, 67, 1035, 22], [1213, 69, 1035, 23, "_lucideReactNative"], [1213, 87, 1035, 23], [1213, 88, 1035, 23, "Shield"], [1213, 94, 1035, 29], [1214, 20, 1035, 30, "size"], [1214, 24, 1035, 34], [1214, 26, 1035, 36], [1214, 28, 1035, 39], [1215, 20, 1035, 40, "color"], [1215, 25, 1035, 45], [1215, 27, 1035, 46], [1216, 18, 1035, 52], [1217, 20, 1035, 52, "fileName"], [1217, 28, 1035, 52], [1217, 30, 1035, 52, "_jsxFileName"], [1217, 42, 1035, 52], [1218, 20, 1035, 52, "lineNumber"], [1218, 30, 1035, 52], [1219, 20, 1035, 52, "columnNumber"], [1219, 32, 1035, 52], [1220, 18, 1035, 52], [1220, 25, 1035, 54], [1220, 26, 1035, 55], [1220, 41, 1036, 22], [1220, 45, 1036, 22, "_jsxDevRuntime"], [1220, 59, 1036, 22], [1220, 60, 1036, 22, "jsxDEV"], [1220, 66, 1036, 22], [1220, 68, 1036, 23, "_Text"], [1220, 73, 1036, 23], [1220, 74, 1036, 23, "default"], [1220, 81, 1036, 27], [1221, 20, 1036, 28, "style"], [1221, 25, 1036, 33], [1221, 27, 1036, 35, "styles"], [1221, 33, 1036, 41], [1221, 34, 1036, 42, "challengeCode"], [1221, 47, 1036, 56], [1222, 20, 1036, 56, "children"], [1222, 28, 1036, 56], [1222, 30, 1036, 58, "challengeCode"], [1223, 18, 1036, 71], [1224, 20, 1036, 71, "fileName"], [1224, 28, 1036, 71], [1224, 30, 1036, 71, "_jsxFileName"], [1224, 42, 1036, 71], [1225, 20, 1036, 71, "lineNumber"], [1225, 30, 1036, 71], [1226, 20, 1036, 71, "columnNumber"], [1226, 32, 1036, 71], [1227, 18, 1036, 71], [1227, 25, 1036, 78], [1227, 26, 1036, 79], [1228, 16, 1036, 79], [1229, 18, 1036, 79, "fileName"], [1229, 26, 1036, 79], [1229, 28, 1036, 79, "_jsxFileName"], [1229, 40, 1036, 79], [1230, 18, 1036, 79, "lineNumber"], [1230, 28, 1036, 79], [1231, 18, 1036, 79, "columnNumber"], [1231, 30, 1036, 79], [1232, 16, 1036, 79], [1232, 23, 1037, 26], [1232, 24, 1038, 19], [1233, 14, 1038, 19], [1234, 16, 1038, 19, "fileName"], [1234, 24, 1038, 19], [1234, 26, 1038, 19, "_jsxFileName"], [1234, 38, 1038, 19], [1235, 16, 1038, 19, "lineNumber"], [1235, 26, 1038, 19], [1236, 16, 1038, 19, "columnNumber"], [1236, 28, 1038, 19], [1237, 14, 1038, 19], [1237, 21, 1039, 22], [1237, 22, 1039, 23], [1237, 37, 1040, 16], [1237, 41, 1040, 16, "_jsxDevRuntime"], [1237, 55, 1040, 16], [1237, 56, 1040, 16, "jsxDEV"], [1237, 62, 1040, 16], [1237, 64, 1040, 17, "_TouchableOpacity"], [1237, 81, 1040, 17], [1237, 82, 1040, 17, "default"], [1237, 89, 1040, 33], [1238, 16, 1040, 34, "onPress"], [1238, 23, 1040, 41], [1238, 25, 1040, 43, "onCancel"], [1238, 33, 1040, 52], [1239, 16, 1040, 53, "style"], [1239, 21, 1040, 58], [1239, 23, 1040, 60, "styles"], [1239, 29, 1040, 66], [1239, 30, 1040, 67, "closeButton"], [1239, 41, 1040, 79], [1240, 16, 1040, 79, "children"], [1240, 24, 1040, 79], [1240, 39, 1041, 18], [1240, 43, 1041, 18, "_jsxDevRuntime"], [1240, 57, 1041, 18], [1240, 58, 1041, 18, "jsxDEV"], [1240, 64, 1041, 18], [1240, 66, 1041, 19, "_lucideReactNative"], [1240, 84, 1041, 19], [1240, 85, 1041, 19, "X"], [1240, 86, 1041, 20], [1241, 18, 1041, 21, "size"], [1241, 22, 1041, 25], [1241, 24, 1041, 27], [1241, 26, 1041, 30], [1242, 18, 1041, 31, "color"], [1242, 23, 1041, 36], [1242, 25, 1041, 37], [1243, 16, 1041, 43], [1244, 18, 1041, 43, "fileName"], [1244, 26, 1041, 43], [1244, 28, 1041, 43, "_jsxFileName"], [1244, 40, 1041, 43], [1245, 18, 1041, 43, "lineNumber"], [1245, 28, 1041, 43], [1246, 18, 1041, 43, "columnNumber"], [1246, 30, 1041, 43], [1247, 16, 1041, 43], [1247, 23, 1041, 45], [1248, 14, 1041, 46], [1249, 16, 1041, 46, "fileName"], [1249, 24, 1041, 46], [1249, 26, 1041, 46, "_jsxFileName"], [1249, 38, 1041, 46], [1250, 16, 1041, 46, "lineNumber"], [1250, 26, 1041, 46], [1251, 16, 1041, 46, "columnNumber"], [1251, 28, 1041, 46], [1252, 14, 1041, 46], [1252, 21, 1042, 34], [1252, 22, 1042, 35], [1253, 12, 1042, 35], [1254, 14, 1042, 35, "fileName"], [1254, 22, 1042, 35], [1254, 24, 1042, 35, "_jsxFileName"], [1254, 36, 1042, 35], [1255, 14, 1042, 35, "lineNumber"], [1255, 24, 1042, 35], [1256, 14, 1042, 35, "columnNumber"], [1256, 26, 1042, 35], [1257, 12, 1042, 35], [1257, 19, 1043, 20], [1258, 10, 1043, 21], [1259, 12, 1043, 21, "fileName"], [1259, 20, 1043, 21], [1259, 22, 1043, 21, "_jsxFileName"], [1259, 34, 1043, 21], [1260, 12, 1043, 21, "lineNumber"], [1260, 22, 1043, 21], [1261, 12, 1043, 21, "columnNumber"], [1261, 24, 1043, 21], [1262, 10, 1043, 21], [1262, 17, 1044, 18], [1262, 18, 1044, 19], [1262, 33, 1046, 12], [1262, 37, 1046, 12, "_jsxDevRuntime"], [1262, 51, 1046, 12], [1262, 52, 1046, 12, "jsxDEV"], [1262, 58, 1046, 12], [1262, 60, 1046, 13, "_View"], [1262, 65, 1046, 13], [1262, 66, 1046, 13, "default"], [1262, 73, 1046, 17], [1263, 12, 1046, 18, "style"], [1263, 17, 1046, 23], [1263, 19, 1046, 25, "styles"], [1263, 25, 1046, 31], [1263, 26, 1046, 32, "privacyNotice"], [1263, 39, 1046, 46], [1264, 12, 1046, 46, "children"], [1264, 20, 1046, 46], [1264, 36, 1047, 14], [1264, 40, 1047, 14, "_jsxDevRuntime"], [1264, 54, 1047, 14], [1264, 55, 1047, 14, "jsxDEV"], [1264, 61, 1047, 14], [1264, 63, 1047, 15, "_lucideReactNative"], [1264, 81, 1047, 15], [1264, 82, 1047, 15, "Shield"], [1264, 88, 1047, 21], [1265, 14, 1047, 22, "size"], [1265, 18, 1047, 26], [1265, 20, 1047, 28], [1265, 22, 1047, 31], [1266, 14, 1047, 32, "color"], [1266, 19, 1047, 37], [1266, 21, 1047, 38], [1267, 12, 1047, 47], [1268, 14, 1047, 47, "fileName"], [1268, 22, 1047, 47], [1268, 24, 1047, 47, "_jsxFileName"], [1268, 36, 1047, 47], [1269, 14, 1047, 47, "lineNumber"], [1269, 24, 1047, 47], [1270, 14, 1047, 47, "columnNumber"], [1270, 26, 1047, 47], [1271, 12, 1047, 47], [1271, 19, 1047, 49], [1271, 20, 1047, 50], [1271, 35, 1048, 14], [1271, 39, 1048, 14, "_jsxDevRuntime"], [1271, 53, 1048, 14], [1271, 54, 1048, 14, "jsxDEV"], [1271, 60, 1048, 14], [1271, 62, 1048, 15, "_Text"], [1271, 67, 1048, 15], [1271, 68, 1048, 15, "default"], [1271, 75, 1048, 19], [1272, 14, 1048, 20, "style"], [1272, 19, 1048, 25], [1272, 21, 1048, 27, "styles"], [1272, 27, 1048, 33], [1272, 28, 1048, 34, "privacyText"], [1272, 39, 1048, 46], [1273, 14, 1048, 46, "children"], [1273, 22, 1048, 46], [1273, 24, 1048, 47], [1274, 12, 1050, 14], [1275, 14, 1050, 14, "fileName"], [1275, 22, 1050, 14], [1275, 24, 1050, 14, "_jsxFileName"], [1275, 36, 1050, 14], [1276, 14, 1050, 14, "lineNumber"], [1276, 24, 1050, 14], [1277, 14, 1050, 14, "columnNumber"], [1277, 26, 1050, 14], [1278, 12, 1050, 14], [1278, 19, 1050, 20], [1278, 20, 1050, 21], [1279, 10, 1050, 21], [1280, 12, 1050, 21, "fileName"], [1280, 20, 1050, 21], [1280, 22, 1050, 21, "_jsxFileName"], [1280, 34, 1050, 21], [1281, 12, 1050, 21, "lineNumber"], [1281, 22, 1050, 21], [1282, 12, 1050, 21, "columnNumber"], [1282, 24, 1050, 21], [1283, 10, 1050, 21], [1283, 17, 1051, 18], [1283, 18, 1051, 19], [1283, 33, 1053, 12], [1283, 37, 1053, 12, "_jsxDevRuntime"], [1283, 51, 1053, 12], [1283, 52, 1053, 12, "jsxDEV"], [1283, 58, 1053, 12], [1283, 60, 1053, 13, "_View"], [1283, 65, 1053, 13], [1283, 66, 1053, 13, "default"], [1283, 73, 1053, 17], [1284, 12, 1053, 18, "style"], [1284, 17, 1053, 23], [1284, 19, 1053, 25, "styles"], [1284, 25, 1053, 31], [1284, 26, 1053, 32, "footer<PERSON><PERSON><PERSON>"], [1284, 39, 1053, 46], [1285, 12, 1053, 46, "children"], [1285, 20, 1053, 46], [1285, 36, 1054, 14], [1285, 40, 1054, 14, "_jsxDevRuntime"], [1285, 54, 1054, 14], [1285, 55, 1054, 14, "jsxDEV"], [1285, 61, 1054, 14], [1285, 63, 1054, 15, "_Text"], [1285, 68, 1054, 15], [1285, 69, 1054, 15, "default"], [1285, 76, 1054, 19], [1286, 14, 1054, 20, "style"], [1286, 19, 1054, 25], [1286, 21, 1054, 27, "styles"], [1286, 27, 1054, 33], [1286, 28, 1054, 34, "instruction"], [1286, 39, 1054, 46], [1287, 14, 1054, 46, "children"], [1287, 22, 1054, 46], [1287, 24, 1054, 47], [1288, 12, 1056, 14], [1289, 14, 1056, 14, "fileName"], [1289, 22, 1056, 14], [1289, 24, 1056, 14, "_jsxFileName"], [1289, 36, 1056, 14], [1290, 14, 1056, 14, "lineNumber"], [1290, 24, 1056, 14], [1291, 14, 1056, 14, "columnNumber"], [1291, 26, 1056, 14], [1292, 12, 1056, 14], [1292, 19, 1056, 20], [1292, 20, 1056, 21], [1292, 35, 1058, 14], [1292, 39, 1058, 14, "_jsxDevRuntime"], [1292, 53, 1058, 14], [1292, 54, 1058, 14, "jsxDEV"], [1292, 60, 1058, 14], [1292, 62, 1058, 15, "_TouchableOpacity"], [1292, 79, 1058, 15], [1292, 80, 1058, 15, "default"], [1292, 87, 1058, 31], [1293, 14, 1059, 16, "onPress"], [1293, 21, 1059, 23], [1293, 23, 1059, 25, "capturePhoto"], [1293, 35, 1059, 38], [1294, 14, 1060, 16, "disabled"], [1294, 22, 1060, 24], [1294, 24, 1060, 26, "processingState"], [1294, 39, 1060, 41], [1294, 44, 1060, 46], [1294, 50, 1060, 52], [1294, 54, 1060, 56], [1294, 55, 1060, 57, "isCameraReady"], [1294, 68, 1060, 71], [1295, 14, 1061, 16, "style"], [1295, 19, 1061, 21], [1295, 21, 1061, 23], [1295, 22, 1062, 18, "styles"], [1295, 28, 1062, 24], [1295, 29, 1062, 25, "shutterButton"], [1295, 42, 1062, 38], [1295, 44, 1063, 18, "processingState"], [1295, 59, 1063, 33], [1295, 64, 1063, 38], [1295, 70, 1063, 44], [1295, 74, 1063, 48, "styles"], [1295, 80, 1063, 54], [1295, 81, 1063, 55, "shutterButtonDisabled"], [1295, 102, 1063, 76], [1295, 103, 1064, 18], [1296, 14, 1064, 18, "children"], [1296, 22, 1064, 18], [1296, 24, 1066, 17, "processingState"], [1296, 39, 1066, 32], [1296, 44, 1066, 37], [1296, 50, 1066, 43], [1296, 66, 1067, 18], [1296, 70, 1067, 18, "_jsxDevRuntime"], [1296, 84, 1067, 18], [1296, 85, 1067, 18, "jsxDEV"], [1296, 91, 1067, 18], [1296, 93, 1067, 19, "_View"], [1296, 98, 1067, 19], [1296, 99, 1067, 19, "default"], [1296, 106, 1067, 23], [1297, 16, 1067, 24, "style"], [1297, 21, 1067, 29], [1297, 23, 1067, 31, "styles"], [1297, 29, 1067, 37], [1297, 30, 1067, 38, "shutterInner"], [1298, 14, 1067, 51], [1299, 16, 1067, 51, "fileName"], [1299, 24, 1067, 51], [1299, 26, 1067, 51, "_jsxFileName"], [1299, 38, 1067, 51], [1300, 16, 1067, 51, "lineNumber"], [1300, 26, 1067, 51], [1301, 16, 1067, 51, "columnNumber"], [1301, 28, 1067, 51], [1302, 14, 1067, 51], [1302, 21, 1067, 53], [1302, 22, 1067, 54], [1302, 38, 1069, 18], [1302, 42, 1069, 18, "_jsxDevRuntime"], [1302, 56, 1069, 18], [1302, 57, 1069, 18, "jsxDEV"], [1302, 63, 1069, 18], [1302, 65, 1069, 19, "_ActivityIndicator"], [1302, 83, 1069, 19], [1302, 84, 1069, 19, "default"], [1302, 91, 1069, 36], [1303, 16, 1069, 37, "size"], [1303, 20, 1069, 41], [1303, 22, 1069, 42], [1303, 29, 1069, 49], [1304, 16, 1069, 50, "color"], [1304, 21, 1069, 55], [1304, 23, 1069, 56], [1305, 14, 1069, 65], [1306, 16, 1069, 65, "fileName"], [1306, 24, 1069, 65], [1306, 26, 1069, 65, "_jsxFileName"], [1306, 38, 1069, 65], [1307, 16, 1069, 65, "lineNumber"], [1307, 26, 1069, 65], [1308, 16, 1069, 65, "columnNumber"], [1308, 28, 1069, 65], [1309, 14, 1069, 65], [1309, 21, 1069, 67], [1310, 12, 1070, 17], [1311, 14, 1070, 17, "fileName"], [1311, 22, 1070, 17], [1311, 24, 1070, 17, "_jsxFileName"], [1311, 36, 1070, 17], [1312, 14, 1070, 17, "lineNumber"], [1312, 24, 1070, 17], [1313, 14, 1070, 17, "columnNumber"], [1313, 26, 1070, 17], [1314, 12, 1070, 17], [1314, 19, 1071, 32], [1314, 20, 1071, 33], [1314, 35, 1072, 14], [1314, 39, 1072, 14, "_jsxDevRuntime"], [1314, 53, 1072, 14], [1314, 54, 1072, 14, "jsxDEV"], [1314, 60, 1072, 14], [1314, 62, 1072, 15, "_Text"], [1314, 67, 1072, 15], [1314, 68, 1072, 15, "default"], [1314, 75, 1072, 19], [1315, 14, 1072, 20, "style"], [1315, 19, 1072, 25], [1315, 21, 1072, 27, "styles"], [1315, 27, 1072, 33], [1315, 28, 1072, 34, "privacyNote"], [1315, 39, 1072, 46], [1316, 14, 1072, 46, "children"], [1316, 22, 1072, 46], [1316, 24, 1072, 47], [1317, 12, 1074, 14], [1318, 14, 1074, 14, "fileName"], [1318, 22, 1074, 14], [1318, 24, 1074, 14, "_jsxFileName"], [1318, 36, 1074, 14], [1319, 14, 1074, 14, "lineNumber"], [1319, 24, 1074, 14], [1320, 14, 1074, 14, "columnNumber"], [1320, 26, 1074, 14], [1321, 12, 1074, 14], [1321, 19, 1074, 20], [1321, 20, 1074, 21], [1322, 10, 1074, 21], [1323, 12, 1074, 21, "fileName"], [1323, 20, 1074, 21], [1323, 22, 1074, 21, "_jsxFileName"], [1323, 34, 1074, 21], [1324, 12, 1074, 21, "lineNumber"], [1324, 22, 1074, 21], [1325, 12, 1074, 21, "columnNumber"], [1325, 24, 1074, 21], [1326, 10, 1074, 21], [1326, 17, 1075, 18], [1326, 18, 1075, 19], [1327, 8, 1075, 19], [1327, 23, 1076, 12], [1327, 24, 1077, 9], [1328, 6, 1077, 9], [1329, 8, 1077, 9, "fileName"], [1329, 16, 1077, 9], [1329, 18, 1077, 9, "_jsxFileName"], [1329, 30, 1077, 9], [1330, 8, 1077, 9, "lineNumber"], [1330, 18, 1077, 9], [1331, 8, 1077, 9, "columnNumber"], [1331, 20, 1077, 9], [1332, 6, 1077, 9], [1332, 13, 1078, 12], [1332, 14, 1078, 13], [1332, 29, 1080, 6], [1332, 33, 1080, 6, "_jsxDevRuntime"], [1332, 47, 1080, 6], [1332, 48, 1080, 6, "jsxDEV"], [1332, 54, 1080, 6], [1332, 56, 1080, 7, "_Modal"], [1332, 62, 1080, 7], [1332, 63, 1080, 7, "default"], [1332, 70, 1080, 12], [1333, 8, 1081, 8, "visible"], [1333, 15, 1081, 15], [1333, 17, 1081, 17, "processingState"], [1333, 32, 1081, 32], [1333, 37, 1081, 37], [1333, 43, 1081, 43], [1333, 47, 1081, 47, "processingState"], [1333, 62, 1081, 62], [1333, 67, 1081, 67], [1333, 74, 1081, 75], [1334, 8, 1082, 8, "transparent"], [1334, 19, 1082, 19], [1335, 8, 1083, 8, "animationType"], [1335, 21, 1083, 21], [1335, 23, 1083, 22], [1335, 29, 1083, 28], [1336, 8, 1083, 28, "children"], [1336, 16, 1083, 28], [1336, 31, 1085, 8], [1336, 35, 1085, 8, "_jsxDevRuntime"], [1336, 49, 1085, 8], [1336, 50, 1085, 8, "jsxDEV"], [1336, 56, 1085, 8], [1336, 58, 1085, 9, "_View"], [1336, 63, 1085, 9], [1336, 64, 1085, 9, "default"], [1336, 71, 1085, 13], [1337, 10, 1085, 14, "style"], [1337, 15, 1085, 19], [1337, 17, 1085, 21, "styles"], [1337, 23, 1085, 27], [1337, 24, 1085, 28, "processingModal"], [1337, 39, 1085, 44], [1338, 10, 1085, 44, "children"], [1338, 18, 1085, 44], [1338, 33, 1086, 10], [1338, 37, 1086, 10, "_jsxDevRuntime"], [1338, 51, 1086, 10], [1338, 52, 1086, 10, "jsxDEV"], [1338, 58, 1086, 10], [1338, 60, 1086, 11, "_View"], [1338, 65, 1086, 11], [1338, 66, 1086, 11, "default"], [1338, 73, 1086, 15], [1339, 12, 1086, 16, "style"], [1339, 17, 1086, 21], [1339, 19, 1086, 23, "styles"], [1339, 25, 1086, 29], [1339, 26, 1086, 30, "processingContent"], [1339, 43, 1086, 48], [1340, 12, 1086, 48, "children"], [1340, 20, 1086, 48], [1340, 36, 1087, 12], [1340, 40, 1087, 12, "_jsxDevRuntime"], [1340, 54, 1087, 12], [1340, 55, 1087, 12, "jsxDEV"], [1340, 61, 1087, 12], [1340, 63, 1087, 13, "_ActivityIndicator"], [1340, 81, 1087, 13], [1340, 82, 1087, 13, "default"], [1340, 89, 1087, 30], [1341, 14, 1087, 31, "size"], [1341, 18, 1087, 35], [1341, 20, 1087, 36], [1341, 27, 1087, 43], [1342, 14, 1087, 44, "color"], [1342, 19, 1087, 49], [1342, 21, 1087, 50], [1343, 12, 1087, 59], [1344, 14, 1087, 59, "fileName"], [1344, 22, 1087, 59], [1344, 24, 1087, 59, "_jsxFileName"], [1344, 36, 1087, 59], [1345, 14, 1087, 59, "lineNumber"], [1345, 24, 1087, 59], [1346, 14, 1087, 59, "columnNumber"], [1346, 26, 1087, 59], [1347, 12, 1087, 59], [1347, 19, 1087, 61], [1347, 20, 1087, 62], [1347, 35, 1089, 12], [1347, 39, 1089, 12, "_jsxDevRuntime"], [1347, 53, 1089, 12], [1347, 54, 1089, 12, "jsxDEV"], [1347, 60, 1089, 12], [1347, 62, 1089, 13, "_Text"], [1347, 67, 1089, 13], [1347, 68, 1089, 13, "default"], [1347, 75, 1089, 17], [1348, 14, 1089, 18, "style"], [1348, 19, 1089, 23], [1348, 21, 1089, 25, "styles"], [1348, 27, 1089, 31], [1348, 28, 1089, 32, "processingTitle"], [1348, 43, 1089, 48], [1349, 14, 1089, 48, "children"], [1349, 22, 1089, 48], [1349, 25, 1090, 15, "processingState"], [1349, 40, 1090, 30], [1349, 45, 1090, 35], [1349, 56, 1090, 46], [1349, 60, 1090, 50], [1349, 80, 1090, 70], [1349, 82, 1091, 15, "processingState"], [1349, 97, 1091, 30], [1349, 102, 1091, 35], [1349, 113, 1091, 46], [1349, 117, 1091, 50], [1349, 146, 1091, 79], [1349, 148, 1092, 15, "processingState"], [1349, 163, 1092, 30], [1349, 168, 1092, 35], [1349, 180, 1092, 47], [1349, 184, 1092, 51], [1349, 216, 1092, 83], [1349, 218, 1093, 15, "processingState"], [1349, 233, 1093, 30], [1349, 238, 1093, 35], [1349, 249, 1093, 46], [1349, 253, 1093, 50], [1349, 275, 1093, 72], [1350, 12, 1093, 72], [1351, 14, 1093, 72, "fileName"], [1351, 22, 1093, 72], [1351, 24, 1093, 72, "_jsxFileName"], [1351, 36, 1093, 72], [1352, 14, 1093, 72, "lineNumber"], [1352, 24, 1093, 72], [1353, 14, 1093, 72, "columnNumber"], [1353, 26, 1093, 72], [1354, 12, 1093, 72], [1354, 19, 1094, 18], [1354, 20, 1094, 19], [1354, 35, 1095, 12], [1354, 39, 1095, 12, "_jsxDevRuntime"], [1354, 53, 1095, 12], [1354, 54, 1095, 12, "jsxDEV"], [1354, 60, 1095, 12], [1354, 62, 1095, 13, "_View"], [1354, 67, 1095, 13], [1354, 68, 1095, 13, "default"], [1354, 75, 1095, 17], [1355, 14, 1095, 18, "style"], [1355, 19, 1095, 23], [1355, 21, 1095, 25, "styles"], [1355, 27, 1095, 31], [1355, 28, 1095, 32, "progressBar"], [1355, 39, 1095, 44], [1356, 14, 1095, 44, "children"], [1356, 22, 1095, 44], [1356, 37, 1096, 14], [1356, 41, 1096, 14, "_jsxDevRuntime"], [1356, 55, 1096, 14], [1356, 56, 1096, 14, "jsxDEV"], [1356, 62, 1096, 14], [1356, 64, 1096, 15, "_View"], [1356, 69, 1096, 15], [1356, 70, 1096, 15, "default"], [1356, 77, 1096, 19], [1357, 16, 1097, 16, "style"], [1357, 21, 1097, 21], [1357, 23, 1097, 23], [1357, 24, 1098, 18, "styles"], [1357, 30, 1098, 24], [1357, 31, 1098, 25, "progressFill"], [1357, 43, 1098, 37], [1357, 45, 1099, 18], [1358, 18, 1099, 20, "width"], [1358, 23, 1099, 25], [1358, 25, 1099, 27], [1358, 28, 1099, 30, "processingProgress"], [1358, 46, 1099, 48], [1359, 16, 1099, 52], [1359, 17, 1099, 53], [1360, 14, 1100, 18], [1361, 16, 1100, 18, "fileName"], [1361, 24, 1100, 18], [1361, 26, 1100, 18, "_jsxFileName"], [1361, 38, 1100, 18], [1362, 16, 1100, 18, "lineNumber"], [1362, 26, 1100, 18], [1363, 16, 1100, 18, "columnNumber"], [1363, 28, 1100, 18], [1364, 14, 1100, 18], [1364, 21, 1101, 15], [1365, 12, 1101, 16], [1366, 14, 1101, 16, "fileName"], [1366, 22, 1101, 16], [1366, 24, 1101, 16, "_jsxFileName"], [1366, 36, 1101, 16], [1367, 14, 1101, 16, "lineNumber"], [1367, 24, 1101, 16], [1368, 14, 1101, 16, "columnNumber"], [1368, 26, 1101, 16], [1369, 12, 1101, 16], [1369, 19, 1102, 18], [1369, 20, 1102, 19], [1369, 35, 1103, 12], [1369, 39, 1103, 12, "_jsxDevRuntime"], [1369, 53, 1103, 12], [1369, 54, 1103, 12, "jsxDEV"], [1369, 60, 1103, 12], [1369, 62, 1103, 13, "_Text"], [1369, 67, 1103, 13], [1369, 68, 1103, 13, "default"], [1369, 75, 1103, 17], [1370, 14, 1103, 18, "style"], [1370, 19, 1103, 23], [1370, 21, 1103, 25, "styles"], [1370, 27, 1103, 31], [1370, 28, 1103, 32, "processingDescription"], [1370, 49, 1103, 54], [1371, 14, 1103, 54, "children"], [1371, 22, 1103, 54], [1371, 25, 1104, 15, "processingState"], [1371, 40, 1104, 30], [1371, 45, 1104, 35], [1371, 56, 1104, 46], [1371, 60, 1104, 50], [1371, 89, 1104, 79], [1371, 91, 1105, 15, "processingState"], [1371, 106, 1105, 30], [1371, 111, 1105, 35], [1371, 122, 1105, 46], [1371, 126, 1105, 50], [1371, 164, 1105, 88], [1371, 166, 1106, 15, "processingState"], [1371, 181, 1106, 30], [1371, 186, 1106, 35], [1371, 198, 1106, 47], [1371, 202, 1106, 51], [1371, 247, 1106, 96], [1371, 249, 1107, 15, "processingState"], [1371, 264, 1107, 30], [1371, 269, 1107, 35], [1371, 280, 1107, 46], [1371, 284, 1107, 50], [1371, 325, 1107, 91], [1372, 12, 1107, 91], [1373, 14, 1107, 91, "fileName"], [1373, 22, 1107, 91], [1373, 24, 1107, 91, "_jsxFileName"], [1373, 36, 1107, 91], [1374, 14, 1107, 91, "lineNumber"], [1374, 24, 1107, 91], [1375, 14, 1107, 91, "columnNumber"], [1375, 26, 1107, 91], [1376, 12, 1107, 91], [1376, 19, 1108, 18], [1376, 20, 1108, 19], [1376, 22, 1109, 13, "processingState"], [1376, 37, 1109, 28], [1376, 42, 1109, 33], [1376, 53, 1109, 44], [1376, 70, 1110, 14], [1376, 74, 1110, 14, "_jsxDevRuntime"], [1376, 88, 1110, 14], [1376, 89, 1110, 14, "jsxDEV"], [1376, 95, 1110, 14], [1376, 97, 1110, 15, "_lucideReactNative"], [1376, 115, 1110, 15], [1376, 116, 1110, 15, "CheckCircle"], [1376, 127, 1110, 26], [1377, 14, 1110, 27, "size"], [1377, 18, 1110, 31], [1377, 20, 1110, 33], [1377, 22, 1110, 36], [1378, 14, 1110, 37, "color"], [1378, 19, 1110, 42], [1378, 21, 1110, 43], [1378, 30, 1110, 52], [1379, 14, 1110, 53, "style"], [1379, 19, 1110, 58], [1379, 21, 1110, 60, "styles"], [1379, 27, 1110, 66], [1379, 28, 1110, 67, "successIcon"], [1380, 12, 1110, 79], [1381, 14, 1110, 79, "fileName"], [1381, 22, 1110, 79], [1381, 24, 1110, 79, "_jsxFileName"], [1381, 36, 1110, 79], [1382, 14, 1110, 79, "lineNumber"], [1382, 24, 1110, 79], [1383, 14, 1110, 79, "columnNumber"], [1383, 26, 1110, 79], [1384, 12, 1110, 79], [1384, 19, 1110, 81], [1384, 20, 1111, 13], [1385, 10, 1111, 13], [1386, 12, 1111, 13, "fileName"], [1386, 20, 1111, 13], [1386, 22, 1111, 13, "_jsxFileName"], [1386, 34, 1111, 13], [1387, 12, 1111, 13, "lineNumber"], [1387, 22, 1111, 13], [1388, 12, 1111, 13, "columnNumber"], [1388, 24, 1111, 13], [1389, 10, 1111, 13], [1389, 17, 1112, 16], [1390, 8, 1112, 17], [1391, 10, 1112, 17, "fileName"], [1391, 18, 1112, 17], [1391, 20, 1112, 17, "_jsxFileName"], [1391, 32, 1112, 17], [1392, 10, 1112, 17, "lineNumber"], [1392, 20, 1112, 17], [1393, 10, 1112, 17, "columnNumber"], [1393, 22, 1112, 17], [1394, 8, 1112, 17], [1394, 15, 1113, 14], [1395, 6, 1113, 15], [1396, 8, 1113, 15, "fileName"], [1396, 16, 1113, 15], [1396, 18, 1113, 15, "_jsxFileName"], [1396, 30, 1113, 15], [1397, 8, 1113, 15, "lineNumber"], [1397, 18, 1113, 15], [1398, 8, 1113, 15, "columnNumber"], [1398, 20, 1113, 15], [1399, 6, 1113, 15], [1399, 13, 1114, 13], [1399, 14, 1114, 14], [1399, 29, 1116, 6], [1399, 33, 1116, 6, "_jsxDevRuntime"], [1399, 47, 1116, 6], [1399, 48, 1116, 6, "jsxDEV"], [1399, 54, 1116, 6], [1399, 56, 1116, 7, "_Modal"], [1399, 62, 1116, 7], [1399, 63, 1116, 7, "default"], [1399, 70, 1116, 12], [1400, 8, 1117, 8, "visible"], [1400, 15, 1117, 15], [1400, 17, 1117, 17, "processingState"], [1400, 32, 1117, 32], [1400, 37, 1117, 37], [1400, 44, 1117, 45], [1401, 8, 1118, 8, "transparent"], [1401, 19, 1118, 19], [1402, 8, 1119, 8, "animationType"], [1402, 21, 1119, 21], [1402, 23, 1119, 22], [1402, 29, 1119, 28], [1403, 8, 1119, 28, "children"], [1403, 16, 1119, 28], [1403, 31, 1121, 8], [1403, 35, 1121, 8, "_jsxDevRuntime"], [1403, 49, 1121, 8], [1403, 50, 1121, 8, "jsxDEV"], [1403, 56, 1121, 8], [1403, 58, 1121, 9, "_View"], [1403, 63, 1121, 9], [1403, 64, 1121, 9, "default"], [1403, 71, 1121, 13], [1404, 10, 1121, 14, "style"], [1404, 15, 1121, 19], [1404, 17, 1121, 21, "styles"], [1404, 23, 1121, 27], [1404, 24, 1121, 28, "processingModal"], [1404, 39, 1121, 44], [1405, 10, 1121, 44, "children"], [1405, 18, 1121, 44], [1405, 33, 1122, 10], [1405, 37, 1122, 10, "_jsxDevRuntime"], [1405, 51, 1122, 10], [1405, 52, 1122, 10, "jsxDEV"], [1405, 58, 1122, 10], [1405, 60, 1122, 11, "_View"], [1405, 65, 1122, 11], [1405, 66, 1122, 11, "default"], [1405, 73, 1122, 15], [1406, 12, 1122, 16, "style"], [1406, 17, 1122, 21], [1406, 19, 1122, 23, "styles"], [1406, 25, 1122, 29], [1406, 26, 1122, 30, "errorContent"], [1406, 38, 1122, 43], [1407, 12, 1122, 43, "children"], [1407, 20, 1122, 43], [1407, 36, 1123, 12], [1407, 40, 1123, 12, "_jsxDevRuntime"], [1407, 54, 1123, 12], [1407, 55, 1123, 12, "jsxDEV"], [1407, 61, 1123, 12], [1407, 63, 1123, 13, "_lucideReactNative"], [1407, 81, 1123, 13], [1407, 82, 1123, 13, "X"], [1407, 83, 1123, 14], [1408, 14, 1123, 15, "size"], [1408, 18, 1123, 19], [1408, 20, 1123, 21], [1408, 22, 1123, 24], [1409, 14, 1123, 25, "color"], [1409, 19, 1123, 30], [1409, 21, 1123, 31], [1410, 12, 1123, 40], [1411, 14, 1123, 40, "fileName"], [1411, 22, 1123, 40], [1411, 24, 1123, 40, "_jsxFileName"], [1411, 36, 1123, 40], [1412, 14, 1123, 40, "lineNumber"], [1412, 24, 1123, 40], [1413, 14, 1123, 40, "columnNumber"], [1413, 26, 1123, 40], [1414, 12, 1123, 40], [1414, 19, 1123, 42], [1414, 20, 1123, 43], [1414, 35, 1124, 12], [1414, 39, 1124, 12, "_jsxDevRuntime"], [1414, 53, 1124, 12], [1414, 54, 1124, 12, "jsxDEV"], [1414, 60, 1124, 12], [1414, 62, 1124, 13, "_Text"], [1414, 67, 1124, 13], [1414, 68, 1124, 13, "default"], [1414, 75, 1124, 17], [1415, 14, 1124, 18, "style"], [1415, 19, 1124, 23], [1415, 21, 1124, 25, "styles"], [1415, 27, 1124, 31], [1415, 28, 1124, 32, "errorTitle"], [1415, 38, 1124, 43], [1416, 14, 1124, 43, "children"], [1416, 22, 1124, 43], [1416, 24, 1124, 44], [1417, 12, 1124, 61], [1418, 14, 1124, 61, "fileName"], [1418, 22, 1124, 61], [1418, 24, 1124, 61, "_jsxFileName"], [1418, 36, 1124, 61], [1419, 14, 1124, 61, "lineNumber"], [1419, 24, 1124, 61], [1420, 14, 1124, 61, "columnNumber"], [1420, 26, 1124, 61], [1421, 12, 1124, 61], [1421, 19, 1124, 67], [1421, 20, 1124, 68], [1421, 35, 1125, 12], [1421, 39, 1125, 12, "_jsxDevRuntime"], [1421, 53, 1125, 12], [1421, 54, 1125, 12, "jsxDEV"], [1421, 60, 1125, 12], [1421, 62, 1125, 13, "_Text"], [1421, 67, 1125, 13], [1421, 68, 1125, 13, "default"], [1421, 75, 1125, 17], [1422, 14, 1125, 18, "style"], [1422, 19, 1125, 23], [1422, 21, 1125, 25, "styles"], [1422, 27, 1125, 31], [1422, 28, 1125, 32, "errorMessage"], [1422, 40, 1125, 45], [1423, 14, 1125, 45, "children"], [1423, 22, 1125, 45], [1423, 24, 1125, 47, "errorMessage"], [1424, 12, 1125, 59], [1425, 14, 1125, 59, "fileName"], [1425, 22, 1125, 59], [1425, 24, 1125, 59, "_jsxFileName"], [1425, 36, 1125, 59], [1426, 14, 1125, 59, "lineNumber"], [1426, 24, 1125, 59], [1427, 14, 1125, 59, "columnNumber"], [1427, 26, 1125, 59], [1428, 12, 1125, 59], [1428, 19, 1125, 66], [1428, 20, 1125, 67], [1428, 35, 1126, 12], [1428, 39, 1126, 12, "_jsxDevRuntime"], [1428, 53, 1126, 12], [1428, 54, 1126, 12, "jsxDEV"], [1428, 60, 1126, 12], [1428, 62, 1126, 13, "_TouchableOpacity"], [1428, 79, 1126, 13], [1428, 80, 1126, 13, "default"], [1428, 87, 1126, 29], [1429, 14, 1127, 14, "onPress"], [1429, 21, 1127, 21], [1429, 23, 1127, 23, "retryCapture"], [1429, 35, 1127, 36], [1430, 14, 1128, 14, "style"], [1430, 19, 1128, 19], [1430, 21, 1128, 21, "styles"], [1430, 27, 1128, 27], [1430, 28, 1128, 28, "primaryButton"], [1430, 41, 1128, 42], [1431, 14, 1128, 42, "children"], [1431, 22, 1128, 42], [1431, 37, 1130, 14], [1431, 41, 1130, 14, "_jsxDevRuntime"], [1431, 55, 1130, 14], [1431, 56, 1130, 14, "jsxDEV"], [1431, 62, 1130, 14], [1431, 64, 1130, 15, "_Text"], [1431, 69, 1130, 15], [1431, 70, 1130, 15, "default"], [1431, 77, 1130, 19], [1432, 16, 1130, 20, "style"], [1432, 21, 1130, 25], [1432, 23, 1130, 27, "styles"], [1432, 29, 1130, 33], [1432, 30, 1130, 34, "primaryButtonText"], [1432, 47, 1130, 52], [1433, 16, 1130, 52, "children"], [1433, 24, 1130, 52], [1433, 26, 1130, 53], [1434, 14, 1130, 62], [1435, 16, 1130, 62, "fileName"], [1435, 24, 1130, 62], [1435, 26, 1130, 62, "_jsxFileName"], [1435, 38, 1130, 62], [1436, 16, 1130, 62, "lineNumber"], [1436, 26, 1130, 62], [1437, 16, 1130, 62, "columnNumber"], [1437, 28, 1130, 62], [1438, 14, 1130, 62], [1438, 21, 1130, 68], [1439, 12, 1130, 69], [1440, 14, 1130, 69, "fileName"], [1440, 22, 1130, 69], [1440, 24, 1130, 69, "_jsxFileName"], [1440, 36, 1130, 69], [1441, 14, 1130, 69, "lineNumber"], [1441, 24, 1130, 69], [1442, 14, 1130, 69, "columnNumber"], [1442, 26, 1130, 69], [1443, 12, 1130, 69], [1443, 19, 1131, 30], [1443, 20, 1131, 31], [1443, 35, 1132, 12], [1443, 39, 1132, 12, "_jsxDevRuntime"], [1443, 53, 1132, 12], [1443, 54, 1132, 12, "jsxDEV"], [1443, 60, 1132, 12], [1443, 62, 1132, 13, "_TouchableOpacity"], [1443, 79, 1132, 13], [1443, 80, 1132, 13, "default"], [1443, 87, 1132, 29], [1444, 14, 1133, 14, "onPress"], [1444, 21, 1133, 21], [1444, 23, 1133, 23, "onCancel"], [1444, 31, 1133, 32], [1445, 14, 1134, 14, "style"], [1445, 19, 1134, 19], [1445, 21, 1134, 21, "styles"], [1445, 27, 1134, 27], [1445, 28, 1134, 28, "secondaryButton"], [1445, 43, 1134, 44], [1446, 14, 1134, 44, "children"], [1446, 22, 1134, 44], [1446, 37, 1136, 14], [1446, 41, 1136, 14, "_jsxDevRuntime"], [1446, 55, 1136, 14], [1446, 56, 1136, 14, "jsxDEV"], [1446, 62, 1136, 14], [1446, 64, 1136, 15, "_Text"], [1446, 69, 1136, 15], [1446, 70, 1136, 15, "default"], [1446, 77, 1136, 19], [1447, 16, 1136, 20, "style"], [1447, 21, 1136, 25], [1447, 23, 1136, 27, "styles"], [1447, 29, 1136, 33], [1447, 30, 1136, 34, "secondaryButtonText"], [1447, 49, 1136, 54], [1448, 16, 1136, 54, "children"], [1448, 24, 1136, 54], [1448, 26, 1136, 55], [1449, 14, 1136, 61], [1450, 16, 1136, 61, "fileName"], [1450, 24, 1136, 61], [1450, 26, 1136, 61, "_jsxFileName"], [1450, 38, 1136, 61], [1451, 16, 1136, 61, "lineNumber"], [1451, 26, 1136, 61], [1452, 16, 1136, 61, "columnNumber"], [1452, 28, 1136, 61], [1453, 14, 1136, 61], [1453, 21, 1136, 67], [1454, 12, 1136, 68], [1455, 14, 1136, 68, "fileName"], [1455, 22, 1136, 68], [1455, 24, 1136, 68, "_jsxFileName"], [1455, 36, 1136, 68], [1456, 14, 1136, 68, "lineNumber"], [1456, 24, 1136, 68], [1457, 14, 1136, 68, "columnNumber"], [1457, 26, 1136, 68], [1458, 12, 1136, 68], [1458, 19, 1137, 30], [1458, 20, 1137, 31], [1459, 10, 1137, 31], [1460, 12, 1137, 31, "fileName"], [1460, 20, 1137, 31], [1460, 22, 1137, 31, "_jsxFileName"], [1460, 34, 1137, 31], [1461, 12, 1137, 31, "lineNumber"], [1461, 22, 1137, 31], [1462, 12, 1137, 31, "columnNumber"], [1462, 24, 1137, 31], [1463, 10, 1137, 31], [1463, 17, 1138, 16], [1464, 8, 1138, 17], [1465, 10, 1138, 17, "fileName"], [1465, 18, 1138, 17], [1465, 20, 1138, 17, "_jsxFileName"], [1465, 32, 1138, 17], [1466, 10, 1138, 17, "lineNumber"], [1466, 20, 1138, 17], [1467, 10, 1138, 17, "columnNumber"], [1467, 22, 1138, 17], [1468, 8, 1138, 17], [1468, 15, 1139, 14], [1469, 6, 1139, 15], [1470, 8, 1139, 15, "fileName"], [1470, 16, 1139, 15], [1470, 18, 1139, 15, "_jsxFileName"], [1470, 30, 1139, 15], [1471, 8, 1139, 15, "lineNumber"], [1471, 18, 1139, 15], [1472, 8, 1139, 15, "columnNumber"], [1472, 20, 1139, 15], [1473, 6, 1139, 15], [1473, 13, 1140, 13], [1473, 14, 1140, 14], [1474, 4, 1140, 14], [1475, 6, 1140, 14, "fileName"], [1475, 14, 1140, 14], [1475, 16, 1140, 14, "_jsxFileName"], [1475, 28, 1140, 14], [1476, 6, 1140, 14, "lineNumber"], [1476, 16, 1140, 14], [1477, 6, 1140, 14, "columnNumber"], [1477, 18, 1140, 14], [1478, 4, 1140, 14], [1478, 11, 1141, 10], [1478, 12, 1141, 11], [1479, 2, 1143, 0], [1480, 2, 1143, 1, "_s"], [1480, 4, 1143, 1], [1480, 5, 51, 24, "EchoCameraWeb"], [1480, 18, 51, 37], [1481, 4, 51, 37], [1481, 12, 58, 42, "useCameraPermissions"], [1481, 44, 58, 62], [1481, 46, 72, 19, "useUpload"], [1481, 64, 72, 28], [1482, 2, 72, 28], [1483, 2, 72, 28, "_c"], [1483, 4, 72, 28], [1483, 7, 51, 24, "EchoCameraWeb"], [1483, 20, 51, 37], [1484, 2, 1144, 0], [1484, 8, 1144, 6, "styles"], [1484, 14, 1144, 12], [1484, 17, 1144, 15, "StyleSheet"], [1484, 36, 1144, 25], [1484, 37, 1144, 26, "create"], [1484, 43, 1144, 32], [1484, 44, 1144, 33], [1485, 4, 1145, 2, "container"], [1485, 13, 1145, 11], [1485, 15, 1145, 13], [1486, 6, 1146, 4, "flex"], [1486, 10, 1146, 8], [1486, 12, 1146, 10], [1486, 13, 1146, 11], [1487, 6, 1147, 4, "backgroundColor"], [1487, 21, 1147, 19], [1487, 23, 1147, 21], [1488, 4, 1148, 2], [1488, 5, 1148, 3], [1489, 4, 1149, 2, "cameraContainer"], [1489, 19, 1149, 17], [1489, 21, 1149, 19], [1490, 6, 1150, 4, "flex"], [1490, 10, 1150, 8], [1490, 12, 1150, 10], [1490, 13, 1150, 11], [1491, 6, 1151, 4, "max<PERSON><PERSON><PERSON>"], [1491, 14, 1151, 12], [1491, 16, 1151, 14], [1491, 19, 1151, 17], [1492, 6, 1152, 4, "alignSelf"], [1492, 15, 1152, 13], [1492, 17, 1152, 15], [1492, 25, 1152, 23], [1493, 6, 1153, 4, "width"], [1493, 11, 1153, 9], [1493, 13, 1153, 11], [1494, 4, 1154, 2], [1494, 5, 1154, 3], [1495, 4, 1155, 2, "camera"], [1495, 10, 1155, 8], [1495, 12, 1155, 10], [1496, 6, 1156, 4, "flex"], [1496, 10, 1156, 8], [1496, 12, 1156, 10], [1497, 4, 1157, 2], [1497, 5, 1157, 3], [1498, 4, 1158, 2, "headerOverlay"], [1498, 17, 1158, 15], [1498, 19, 1158, 17], [1499, 6, 1159, 4, "position"], [1499, 14, 1159, 12], [1499, 16, 1159, 14], [1499, 26, 1159, 24], [1500, 6, 1160, 4, "top"], [1500, 9, 1160, 7], [1500, 11, 1160, 9], [1500, 12, 1160, 10], [1501, 6, 1161, 4, "left"], [1501, 10, 1161, 8], [1501, 12, 1161, 10], [1501, 13, 1161, 11], [1502, 6, 1162, 4, "right"], [1502, 11, 1162, 9], [1502, 13, 1162, 11], [1502, 14, 1162, 12], [1503, 6, 1163, 4, "backgroundColor"], [1503, 21, 1163, 19], [1503, 23, 1163, 21], [1503, 36, 1163, 34], [1504, 6, 1164, 4, "paddingTop"], [1504, 16, 1164, 14], [1504, 18, 1164, 16], [1504, 20, 1164, 18], [1505, 6, 1165, 4, "paddingHorizontal"], [1505, 23, 1165, 21], [1505, 25, 1165, 23], [1505, 27, 1165, 25], [1506, 6, 1166, 4, "paddingBottom"], [1506, 19, 1166, 17], [1506, 21, 1166, 19], [1507, 4, 1167, 2], [1507, 5, 1167, 3], [1508, 4, 1168, 2, "headerContent"], [1508, 17, 1168, 15], [1508, 19, 1168, 17], [1509, 6, 1169, 4, "flexDirection"], [1509, 19, 1169, 17], [1509, 21, 1169, 19], [1509, 26, 1169, 24], [1510, 6, 1170, 4, "justifyContent"], [1510, 20, 1170, 18], [1510, 22, 1170, 20], [1510, 37, 1170, 35], [1511, 6, 1171, 4, "alignItems"], [1511, 16, 1171, 14], [1511, 18, 1171, 16], [1512, 4, 1172, 2], [1512, 5, 1172, 3], [1513, 4, 1173, 2, "headerLeft"], [1513, 14, 1173, 12], [1513, 16, 1173, 14], [1514, 6, 1174, 4, "flex"], [1514, 10, 1174, 8], [1514, 12, 1174, 10], [1515, 4, 1175, 2], [1515, 5, 1175, 3], [1516, 4, 1176, 2, "headerTitle"], [1516, 15, 1176, 13], [1516, 17, 1176, 15], [1517, 6, 1177, 4, "fontSize"], [1517, 14, 1177, 12], [1517, 16, 1177, 14], [1517, 18, 1177, 16], [1518, 6, 1178, 4, "fontWeight"], [1518, 16, 1178, 14], [1518, 18, 1178, 16], [1518, 23, 1178, 21], [1519, 6, 1179, 4, "color"], [1519, 11, 1179, 9], [1519, 13, 1179, 11], [1519, 19, 1179, 17], [1520, 6, 1180, 4, "marginBottom"], [1520, 18, 1180, 16], [1520, 20, 1180, 18], [1521, 4, 1181, 2], [1521, 5, 1181, 3], [1522, 4, 1182, 2, "subtitleRow"], [1522, 15, 1182, 13], [1522, 17, 1182, 15], [1523, 6, 1183, 4, "flexDirection"], [1523, 19, 1183, 17], [1523, 21, 1183, 19], [1523, 26, 1183, 24], [1524, 6, 1184, 4, "alignItems"], [1524, 16, 1184, 14], [1524, 18, 1184, 16], [1524, 26, 1184, 24], [1525, 6, 1185, 4, "marginBottom"], [1525, 18, 1185, 16], [1525, 20, 1185, 18], [1526, 4, 1186, 2], [1526, 5, 1186, 3], [1527, 4, 1187, 2, "webIcon"], [1527, 11, 1187, 9], [1527, 13, 1187, 11], [1528, 6, 1188, 4, "fontSize"], [1528, 14, 1188, 12], [1528, 16, 1188, 14], [1528, 18, 1188, 16], [1529, 6, 1189, 4, "marginRight"], [1529, 17, 1189, 15], [1529, 19, 1189, 17], [1530, 4, 1190, 2], [1530, 5, 1190, 3], [1531, 4, 1191, 2, "headerSubtitle"], [1531, 18, 1191, 16], [1531, 20, 1191, 18], [1532, 6, 1192, 4, "fontSize"], [1532, 14, 1192, 12], [1532, 16, 1192, 14], [1532, 18, 1192, 16], [1533, 6, 1193, 4, "color"], [1533, 11, 1193, 9], [1533, 13, 1193, 11], [1533, 19, 1193, 17], [1534, 6, 1194, 4, "opacity"], [1534, 13, 1194, 11], [1534, 15, 1194, 13], [1535, 4, 1195, 2], [1535, 5, 1195, 3], [1536, 4, 1196, 2, "challengeRow"], [1536, 16, 1196, 14], [1536, 18, 1196, 16], [1537, 6, 1197, 4, "flexDirection"], [1537, 19, 1197, 17], [1537, 21, 1197, 19], [1537, 26, 1197, 24], [1538, 6, 1198, 4, "alignItems"], [1538, 16, 1198, 14], [1538, 18, 1198, 16], [1539, 4, 1199, 2], [1539, 5, 1199, 3], [1540, 4, 1200, 2, "challengeCode"], [1540, 17, 1200, 15], [1540, 19, 1200, 17], [1541, 6, 1201, 4, "fontSize"], [1541, 14, 1201, 12], [1541, 16, 1201, 14], [1541, 18, 1201, 16], [1542, 6, 1202, 4, "color"], [1542, 11, 1202, 9], [1542, 13, 1202, 11], [1542, 19, 1202, 17], [1543, 6, 1203, 4, "marginLeft"], [1543, 16, 1203, 14], [1543, 18, 1203, 16], [1543, 19, 1203, 17], [1544, 6, 1204, 4, "fontFamily"], [1544, 16, 1204, 14], [1544, 18, 1204, 16], [1545, 4, 1205, 2], [1545, 5, 1205, 3], [1546, 4, 1206, 2, "closeButton"], [1546, 15, 1206, 13], [1546, 17, 1206, 15], [1547, 6, 1207, 4, "padding"], [1547, 13, 1207, 11], [1547, 15, 1207, 13], [1548, 4, 1208, 2], [1548, 5, 1208, 3], [1549, 4, 1209, 2, "privacyNotice"], [1549, 17, 1209, 15], [1549, 19, 1209, 17], [1550, 6, 1210, 4, "position"], [1550, 14, 1210, 12], [1550, 16, 1210, 14], [1550, 26, 1210, 24], [1551, 6, 1211, 4, "top"], [1551, 9, 1211, 7], [1551, 11, 1211, 9], [1551, 14, 1211, 12], [1552, 6, 1212, 4, "left"], [1552, 10, 1212, 8], [1552, 12, 1212, 10], [1552, 14, 1212, 12], [1553, 6, 1213, 4, "right"], [1553, 11, 1213, 9], [1553, 13, 1213, 11], [1553, 15, 1213, 13], [1554, 6, 1214, 4, "backgroundColor"], [1554, 21, 1214, 19], [1554, 23, 1214, 21], [1554, 48, 1214, 46], [1555, 6, 1215, 4, "borderRadius"], [1555, 18, 1215, 16], [1555, 20, 1215, 18], [1555, 21, 1215, 19], [1556, 6, 1216, 4, "padding"], [1556, 13, 1216, 11], [1556, 15, 1216, 13], [1556, 17, 1216, 15], [1557, 6, 1217, 4, "flexDirection"], [1557, 19, 1217, 17], [1557, 21, 1217, 19], [1557, 26, 1217, 24], [1558, 6, 1218, 4, "alignItems"], [1558, 16, 1218, 14], [1558, 18, 1218, 16], [1559, 4, 1219, 2], [1559, 5, 1219, 3], [1560, 4, 1220, 2, "privacyText"], [1560, 15, 1220, 13], [1560, 17, 1220, 15], [1561, 6, 1221, 4, "color"], [1561, 11, 1221, 9], [1561, 13, 1221, 11], [1561, 19, 1221, 17], [1562, 6, 1222, 4, "fontSize"], [1562, 14, 1222, 12], [1562, 16, 1222, 14], [1562, 18, 1222, 16], [1563, 6, 1223, 4, "marginLeft"], [1563, 16, 1223, 14], [1563, 18, 1223, 16], [1563, 19, 1223, 17], [1564, 6, 1224, 4, "flex"], [1564, 10, 1224, 8], [1564, 12, 1224, 10], [1565, 4, 1225, 2], [1565, 5, 1225, 3], [1566, 4, 1226, 2, "footer<PERSON><PERSON><PERSON>"], [1566, 17, 1226, 15], [1566, 19, 1226, 17], [1567, 6, 1227, 4, "position"], [1567, 14, 1227, 12], [1567, 16, 1227, 14], [1567, 26, 1227, 24], [1568, 6, 1228, 4, "bottom"], [1568, 12, 1228, 10], [1568, 14, 1228, 12], [1568, 15, 1228, 13], [1569, 6, 1229, 4, "left"], [1569, 10, 1229, 8], [1569, 12, 1229, 10], [1569, 13, 1229, 11], [1570, 6, 1230, 4, "right"], [1570, 11, 1230, 9], [1570, 13, 1230, 11], [1570, 14, 1230, 12], [1571, 6, 1231, 4, "backgroundColor"], [1571, 21, 1231, 19], [1571, 23, 1231, 21], [1571, 36, 1231, 34], [1572, 6, 1232, 4, "paddingBottom"], [1572, 19, 1232, 17], [1572, 21, 1232, 19], [1572, 23, 1232, 21], [1573, 6, 1233, 4, "paddingTop"], [1573, 16, 1233, 14], [1573, 18, 1233, 16], [1573, 20, 1233, 18], [1574, 6, 1234, 4, "alignItems"], [1574, 16, 1234, 14], [1574, 18, 1234, 16], [1575, 4, 1235, 2], [1575, 5, 1235, 3], [1576, 4, 1236, 2, "instruction"], [1576, 15, 1236, 13], [1576, 17, 1236, 15], [1577, 6, 1237, 4, "fontSize"], [1577, 14, 1237, 12], [1577, 16, 1237, 14], [1577, 18, 1237, 16], [1578, 6, 1238, 4, "color"], [1578, 11, 1238, 9], [1578, 13, 1238, 11], [1578, 19, 1238, 17], [1579, 6, 1239, 4, "marginBottom"], [1579, 18, 1239, 16], [1579, 20, 1239, 18], [1580, 4, 1240, 2], [1580, 5, 1240, 3], [1581, 4, 1241, 2, "shutterButton"], [1581, 17, 1241, 15], [1581, 19, 1241, 17], [1582, 6, 1242, 4, "width"], [1582, 11, 1242, 9], [1582, 13, 1242, 11], [1582, 15, 1242, 13], [1583, 6, 1243, 4, "height"], [1583, 12, 1243, 10], [1583, 14, 1243, 12], [1583, 16, 1243, 14], [1584, 6, 1244, 4, "borderRadius"], [1584, 18, 1244, 16], [1584, 20, 1244, 18], [1584, 22, 1244, 20], [1585, 6, 1245, 4, "backgroundColor"], [1585, 21, 1245, 19], [1585, 23, 1245, 21], [1585, 29, 1245, 27], [1586, 6, 1246, 4, "justifyContent"], [1586, 20, 1246, 18], [1586, 22, 1246, 20], [1586, 30, 1246, 28], [1587, 6, 1247, 4, "alignItems"], [1587, 16, 1247, 14], [1587, 18, 1247, 16], [1587, 26, 1247, 24], [1588, 6, 1248, 4, "marginBottom"], [1588, 18, 1248, 16], [1588, 20, 1248, 18], [1588, 22, 1248, 20], [1589, 6, 1249, 4], [1589, 9, 1249, 7, "Platform"], [1589, 26, 1249, 15], [1589, 27, 1249, 16, "select"], [1589, 33, 1249, 22], [1589, 34, 1249, 23], [1590, 8, 1250, 6, "ios"], [1590, 11, 1250, 9], [1590, 13, 1250, 11], [1591, 10, 1251, 8, "shadowColor"], [1591, 21, 1251, 19], [1591, 23, 1251, 21], [1591, 32, 1251, 30], [1592, 10, 1252, 8, "shadowOffset"], [1592, 22, 1252, 20], [1592, 24, 1252, 22], [1593, 12, 1252, 24, "width"], [1593, 17, 1252, 29], [1593, 19, 1252, 31], [1593, 20, 1252, 32], [1594, 12, 1252, 34, "height"], [1594, 18, 1252, 40], [1594, 20, 1252, 42], [1595, 10, 1252, 44], [1595, 11, 1252, 45], [1596, 10, 1253, 8, "shadowOpacity"], [1596, 23, 1253, 21], [1596, 25, 1253, 23], [1596, 28, 1253, 26], [1597, 10, 1254, 8, "shadowRadius"], [1597, 22, 1254, 20], [1597, 24, 1254, 22], [1598, 8, 1255, 6], [1598, 9, 1255, 7], [1599, 8, 1256, 6, "android"], [1599, 15, 1256, 13], [1599, 17, 1256, 15], [1600, 10, 1257, 8, "elevation"], [1600, 19, 1257, 17], [1600, 21, 1257, 19], [1601, 8, 1258, 6], [1601, 9, 1258, 7], [1602, 8, 1259, 6, "web"], [1602, 11, 1259, 9], [1602, 13, 1259, 11], [1603, 10, 1260, 8, "boxShadow"], [1603, 19, 1260, 17], [1603, 21, 1260, 19], [1604, 8, 1261, 6], [1605, 6, 1262, 4], [1605, 7, 1262, 5], [1606, 4, 1263, 2], [1606, 5, 1263, 3], [1607, 4, 1264, 2, "shutterButtonDisabled"], [1607, 25, 1264, 23], [1607, 27, 1264, 25], [1608, 6, 1265, 4, "opacity"], [1608, 13, 1265, 11], [1608, 15, 1265, 13], [1609, 4, 1266, 2], [1609, 5, 1266, 3], [1610, 4, 1267, 2, "shutterInner"], [1610, 16, 1267, 14], [1610, 18, 1267, 16], [1611, 6, 1268, 4, "width"], [1611, 11, 1268, 9], [1611, 13, 1268, 11], [1611, 15, 1268, 13], [1612, 6, 1269, 4, "height"], [1612, 12, 1269, 10], [1612, 14, 1269, 12], [1612, 16, 1269, 14], [1613, 6, 1270, 4, "borderRadius"], [1613, 18, 1270, 16], [1613, 20, 1270, 18], [1613, 22, 1270, 20], [1614, 6, 1271, 4, "backgroundColor"], [1614, 21, 1271, 19], [1614, 23, 1271, 21], [1614, 29, 1271, 27], [1615, 6, 1272, 4, "borderWidth"], [1615, 17, 1272, 15], [1615, 19, 1272, 17], [1615, 20, 1272, 18], [1616, 6, 1273, 4, "borderColor"], [1616, 17, 1273, 15], [1616, 19, 1273, 17], [1617, 4, 1274, 2], [1617, 5, 1274, 3], [1618, 4, 1275, 2, "privacyNote"], [1618, 15, 1275, 13], [1618, 17, 1275, 15], [1619, 6, 1276, 4, "fontSize"], [1619, 14, 1276, 12], [1619, 16, 1276, 14], [1619, 18, 1276, 16], [1620, 6, 1277, 4, "color"], [1620, 11, 1277, 9], [1620, 13, 1277, 11], [1621, 4, 1278, 2], [1621, 5, 1278, 3], [1622, 4, 1279, 2, "processingModal"], [1622, 19, 1279, 17], [1622, 21, 1279, 19], [1623, 6, 1280, 4, "flex"], [1623, 10, 1280, 8], [1623, 12, 1280, 10], [1623, 13, 1280, 11], [1624, 6, 1281, 4, "backgroundColor"], [1624, 21, 1281, 19], [1624, 23, 1281, 21], [1624, 43, 1281, 41], [1625, 6, 1282, 4, "justifyContent"], [1625, 20, 1282, 18], [1625, 22, 1282, 20], [1625, 30, 1282, 28], [1626, 6, 1283, 4, "alignItems"], [1626, 16, 1283, 14], [1626, 18, 1283, 16], [1627, 4, 1284, 2], [1627, 5, 1284, 3], [1628, 4, 1285, 2, "processingContent"], [1628, 21, 1285, 19], [1628, 23, 1285, 21], [1629, 6, 1286, 4, "backgroundColor"], [1629, 21, 1286, 19], [1629, 23, 1286, 21], [1629, 29, 1286, 27], [1630, 6, 1287, 4, "borderRadius"], [1630, 18, 1287, 16], [1630, 20, 1287, 18], [1630, 22, 1287, 20], [1631, 6, 1288, 4, "padding"], [1631, 13, 1288, 11], [1631, 15, 1288, 13], [1631, 17, 1288, 15], [1632, 6, 1289, 4, "width"], [1632, 11, 1289, 9], [1632, 13, 1289, 11], [1632, 18, 1289, 16], [1633, 6, 1290, 4, "max<PERSON><PERSON><PERSON>"], [1633, 14, 1290, 12], [1633, 16, 1290, 14], [1633, 19, 1290, 17], [1634, 6, 1291, 4, "alignItems"], [1634, 16, 1291, 14], [1634, 18, 1291, 16], [1635, 4, 1292, 2], [1635, 5, 1292, 3], [1636, 4, 1293, 2, "processingTitle"], [1636, 19, 1293, 17], [1636, 21, 1293, 19], [1637, 6, 1294, 4, "fontSize"], [1637, 14, 1294, 12], [1637, 16, 1294, 14], [1637, 18, 1294, 16], [1638, 6, 1295, 4, "fontWeight"], [1638, 16, 1295, 14], [1638, 18, 1295, 16], [1638, 23, 1295, 21], [1639, 6, 1296, 4, "color"], [1639, 11, 1296, 9], [1639, 13, 1296, 11], [1639, 22, 1296, 20], [1640, 6, 1297, 4, "marginTop"], [1640, 15, 1297, 13], [1640, 17, 1297, 15], [1640, 19, 1297, 17], [1641, 6, 1298, 4, "marginBottom"], [1641, 18, 1298, 16], [1641, 20, 1298, 18], [1642, 4, 1299, 2], [1642, 5, 1299, 3], [1643, 4, 1300, 2, "progressBar"], [1643, 15, 1300, 13], [1643, 17, 1300, 15], [1644, 6, 1301, 4, "width"], [1644, 11, 1301, 9], [1644, 13, 1301, 11], [1644, 19, 1301, 17], [1645, 6, 1302, 4, "height"], [1645, 12, 1302, 10], [1645, 14, 1302, 12], [1645, 15, 1302, 13], [1646, 6, 1303, 4, "backgroundColor"], [1646, 21, 1303, 19], [1646, 23, 1303, 21], [1646, 32, 1303, 30], [1647, 6, 1304, 4, "borderRadius"], [1647, 18, 1304, 16], [1647, 20, 1304, 18], [1647, 21, 1304, 19], [1648, 6, 1305, 4, "overflow"], [1648, 14, 1305, 12], [1648, 16, 1305, 14], [1648, 24, 1305, 22], [1649, 6, 1306, 4, "marginBottom"], [1649, 18, 1306, 16], [1649, 20, 1306, 18], [1650, 4, 1307, 2], [1650, 5, 1307, 3], [1651, 4, 1308, 2, "progressFill"], [1651, 16, 1308, 14], [1651, 18, 1308, 16], [1652, 6, 1309, 4, "height"], [1652, 12, 1309, 10], [1652, 14, 1309, 12], [1652, 20, 1309, 18], [1653, 6, 1310, 4, "backgroundColor"], [1653, 21, 1310, 19], [1653, 23, 1310, 21], [1653, 32, 1310, 30], [1654, 6, 1311, 4, "borderRadius"], [1654, 18, 1311, 16], [1654, 20, 1311, 18], [1655, 4, 1312, 2], [1655, 5, 1312, 3], [1656, 4, 1313, 2, "processingDescription"], [1656, 25, 1313, 23], [1656, 27, 1313, 25], [1657, 6, 1314, 4, "fontSize"], [1657, 14, 1314, 12], [1657, 16, 1314, 14], [1657, 18, 1314, 16], [1658, 6, 1315, 4, "color"], [1658, 11, 1315, 9], [1658, 13, 1315, 11], [1658, 22, 1315, 20], [1659, 6, 1316, 4, "textAlign"], [1659, 15, 1316, 13], [1659, 17, 1316, 15], [1660, 4, 1317, 2], [1660, 5, 1317, 3], [1661, 4, 1318, 2, "successIcon"], [1661, 15, 1318, 13], [1661, 17, 1318, 15], [1662, 6, 1319, 4, "marginTop"], [1662, 15, 1319, 13], [1662, 17, 1319, 15], [1663, 4, 1320, 2], [1663, 5, 1320, 3], [1664, 4, 1321, 2, "errorContent"], [1664, 16, 1321, 14], [1664, 18, 1321, 16], [1665, 6, 1322, 4, "backgroundColor"], [1665, 21, 1322, 19], [1665, 23, 1322, 21], [1665, 29, 1322, 27], [1666, 6, 1323, 4, "borderRadius"], [1666, 18, 1323, 16], [1666, 20, 1323, 18], [1666, 22, 1323, 20], [1667, 6, 1324, 4, "padding"], [1667, 13, 1324, 11], [1667, 15, 1324, 13], [1667, 17, 1324, 15], [1668, 6, 1325, 4, "width"], [1668, 11, 1325, 9], [1668, 13, 1325, 11], [1668, 18, 1325, 16], [1669, 6, 1326, 4, "max<PERSON><PERSON><PERSON>"], [1669, 14, 1326, 12], [1669, 16, 1326, 14], [1669, 19, 1326, 17], [1670, 6, 1327, 4, "alignItems"], [1670, 16, 1327, 14], [1670, 18, 1327, 16], [1671, 4, 1328, 2], [1671, 5, 1328, 3], [1672, 4, 1329, 2, "errorTitle"], [1672, 14, 1329, 12], [1672, 16, 1329, 14], [1673, 6, 1330, 4, "fontSize"], [1673, 14, 1330, 12], [1673, 16, 1330, 14], [1673, 18, 1330, 16], [1674, 6, 1331, 4, "fontWeight"], [1674, 16, 1331, 14], [1674, 18, 1331, 16], [1674, 23, 1331, 21], [1675, 6, 1332, 4, "color"], [1675, 11, 1332, 9], [1675, 13, 1332, 11], [1675, 22, 1332, 20], [1676, 6, 1333, 4, "marginTop"], [1676, 15, 1333, 13], [1676, 17, 1333, 15], [1676, 19, 1333, 17], [1677, 6, 1334, 4, "marginBottom"], [1677, 18, 1334, 16], [1677, 20, 1334, 18], [1678, 4, 1335, 2], [1678, 5, 1335, 3], [1679, 4, 1336, 2, "errorMessage"], [1679, 16, 1336, 14], [1679, 18, 1336, 16], [1680, 6, 1337, 4, "fontSize"], [1680, 14, 1337, 12], [1680, 16, 1337, 14], [1680, 18, 1337, 16], [1681, 6, 1338, 4, "color"], [1681, 11, 1338, 9], [1681, 13, 1338, 11], [1681, 22, 1338, 20], [1682, 6, 1339, 4, "textAlign"], [1682, 15, 1339, 13], [1682, 17, 1339, 15], [1682, 25, 1339, 23], [1683, 6, 1340, 4, "marginBottom"], [1683, 18, 1340, 16], [1683, 20, 1340, 18], [1684, 4, 1341, 2], [1684, 5, 1341, 3], [1685, 4, 1342, 2, "primaryButton"], [1685, 17, 1342, 15], [1685, 19, 1342, 17], [1686, 6, 1343, 4, "backgroundColor"], [1686, 21, 1343, 19], [1686, 23, 1343, 21], [1686, 32, 1343, 30], [1687, 6, 1344, 4, "paddingHorizontal"], [1687, 23, 1344, 21], [1687, 25, 1344, 23], [1687, 27, 1344, 25], [1688, 6, 1345, 4, "paddingVertical"], [1688, 21, 1345, 19], [1688, 23, 1345, 21], [1688, 25, 1345, 23], [1689, 6, 1346, 4, "borderRadius"], [1689, 18, 1346, 16], [1689, 20, 1346, 18], [1689, 21, 1346, 19], [1690, 6, 1347, 4, "marginTop"], [1690, 15, 1347, 13], [1690, 17, 1347, 15], [1691, 4, 1348, 2], [1691, 5, 1348, 3], [1692, 4, 1349, 2, "primaryButtonText"], [1692, 21, 1349, 19], [1692, 23, 1349, 21], [1693, 6, 1350, 4, "color"], [1693, 11, 1350, 9], [1693, 13, 1350, 11], [1693, 19, 1350, 17], [1694, 6, 1351, 4, "fontSize"], [1694, 14, 1351, 12], [1694, 16, 1351, 14], [1694, 18, 1351, 16], [1695, 6, 1352, 4, "fontWeight"], [1695, 16, 1352, 14], [1695, 18, 1352, 16], [1696, 4, 1353, 2], [1696, 5, 1353, 3], [1697, 4, 1354, 2, "secondaryButton"], [1697, 19, 1354, 17], [1697, 21, 1354, 19], [1698, 6, 1355, 4, "paddingHorizontal"], [1698, 23, 1355, 21], [1698, 25, 1355, 23], [1698, 27, 1355, 25], [1699, 6, 1356, 4, "paddingVertical"], [1699, 21, 1356, 19], [1699, 23, 1356, 21], [1699, 25, 1356, 23], [1700, 6, 1357, 4, "marginTop"], [1700, 15, 1357, 13], [1700, 17, 1357, 15], [1701, 4, 1358, 2], [1701, 5, 1358, 3], [1702, 4, 1359, 2, "secondaryButtonText"], [1702, 23, 1359, 21], [1702, 25, 1359, 23], [1703, 6, 1360, 4, "color"], [1703, 11, 1360, 9], [1703, 13, 1360, 11], [1703, 22, 1360, 20], [1704, 6, 1361, 4, "fontSize"], [1704, 14, 1361, 12], [1704, 16, 1361, 14], [1705, 4, 1362, 2], [1705, 5, 1362, 3], [1706, 4, 1363, 2, "permissionContent"], [1706, 21, 1363, 19], [1706, 23, 1363, 21], [1707, 6, 1364, 4, "flex"], [1707, 10, 1364, 8], [1707, 12, 1364, 10], [1707, 13, 1364, 11], [1708, 6, 1365, 4, "justifyContent"], [1708, 20, 1365, 18], [1708, 22, 1365, 20], [1708, 30, 1365, 28], [1709, 6, 1366, 4, "alignItems"], [1709, 16, 1366, 14], [1709, 18, 1366, 16], [1709, 26, 1366, 24], [1710, 6, 1367, 4, "padding"], [1710, 13, 1367, 11], [1710, 15, 1367, 13], [1711, 4, 1368, 2], [1711, 5, 1368, 3], [1712, 4, 1369, 2, "permissionTitle"], [1712, 19, 1369, 17], [1712, 21, 1369, 19], [1713, 6, 1370, 4, "fontSize"], [1713, 14, 1370, 12], [1713, 16, 1370, 14], [1713, 18, 1370, 16], [1714, 6, 1371, 4, "fontWeight"], [1714, 16, 1371, 14], [1714, 18, 1371, 16], [1714, 23, 1371, 21], [1715, 6, 1372, 4, "color"], [1715, 11, 1372, 9], [1715, 13, 1372, 11], [1715, 22, 1372, 20], [1716, 6, 1373, 4, "marginTop"], [1716, 15, 1373, 13], [1716, 17, 1373, 15], [1716, 19, 1373, 17], [1717, 6, 1374, 4, "marginBottom"], [1717, 18, 1374, 16], [1717, 20, 1374, 18], [1718, 4, 1375, 2], [1718, 5, 1375, 3], [1719, 4, 1376, 2, "permissionDescription"], [1719, 25, 1376, 23], [1719, 27, 1376, 25], [1720, 6, 1377, 4, "fontSize"], [1720, 14, 1377, 12], [1720, 16, 1377, 14], [1720, 18, 1377, 16], [1721, 6, 1378, 4, "color"], [1721, 11, 1378, 9], [1721, 13, 1378, 11], [1721, 22, 1378, 20], [1722, 6, 1379, 4, "textAlign"], [1722, 15, 1379, 13], [1722, 17, 1379, 15], [1722, 25, 1379, 23], [1723, 6, 1380, 4, "marginBottom"], [1723, 18, 1380, 16], [1723, 20, 1380, 18], [1724, 4, 1381, 2], [1724, 5, 1381, 3], [1725, 4, 1382, 2, "loadingText"], [1725, 15, 1382, 13], [1725, 17, 1382, 15], [1726, 6, 1383, 4, "color"], [1726, 11, 1383, 9], [1726, 13, 1383, 11], [1726, 22, 1383, 20], [1727, 6, 1384, 4, "marginTop"], [1727, 15, 1384, 13], [1727, 17, 1384, 15], [1728, 4, 1385, 2], [1728, 5, 1385, 3], [1729, 4, 1386, 2], [1730, 4, 1387, 2, "blurZone"], [1730, 12, 1387, 10], [1730, 14, 1387, 12], [1731, 6, 1388, 4, "position"], [1731, 14, 1388, 12], [1731, 16, 1388, 14], [1731, 26, 1388, 24], [1732, 6, 1389, 4, "overflow"], [1732, 14, 1389, 12], [1732, 16, 1389, 14], [1733, 4, 1390, 2], [1733, 5, 1390, 3], [1734, 4, 1391, 2, "previewChip"], [1734, 15, 1391, 13], [1734, 17, 1391, 15], [1735, 6, 1392, 4, "position"], [1735, 14, 1392, 12], [1735, 16, 1392, 14], [1735, 26, 1392, 24], [1736, 6, 1393, 4, "top"], [1736, 9, 1393, 7], [1736, 11, 1393, 9], [1736, 12, 1393, 10], [1737, 6, 1394, 4, "right"], [1737, 11, 1394, 9], [1737, 13, 1394, 11], [1737, 14, 1394, 12], [1738, 6, 1395, 4, "backgroundColor"], [1738, 21, 1395, 19], [1738, 23, 1395, 21], [1738, 40, 1395, 38], [1739, 6, 1396, 4, "paddingHorizontal"], [1739, 23, 1396, 21], [1739, 25, 1396, 23], [1739, 27, 1396, 25], [1740, 6, 1397, 4, "paddingVertical"], [1740, 21, 1397, 19], [1740, 23, 1397, 21], [1740, 24, 1397, 22], [1741, 6, 1398, 4, "borderRadius"], [1741, 18, 1398, 16], [1741, 20, 1398, 18], [1742, 4, 1399, 2], [1742, 5, 1399, 3], [1743, 4, 1400, 2, "previewChipText"], [1743, 19, 1400, 17], [1743, 21, 1400, 19], [1744, 6, 1401, 4, "color"], [1744, 11, 1401, 9], [1744, 13, 1401, 11], [1744, 19, 1401, 17], [1745, 6, 1402, 4, "fontSize"], [1745, 14, 1402, 12], [1745, 16, 1402, 14], [1745, 18, 1402, 16], [1746, 6, 1403, 4, "fontWeight"], [1746, 16, 1403, 14], [1746, 18, 1403, 16], [1747, 4, 1404, 2], [1748, 2, 1405, 0], [1748, 3, 1405, 1], [1748, 4, 1405, 2], [1749, 2, 1405, 3], [1749, 6, 1405, 3, "_c"], [1749, 8, 1405, 3], [1750, 2, 1405, 3, "$RefreshReg$"], [1750, 14, 1405, 3], [1750, 15, 1405, 3, "_c"], [1750, 17, 1405, 3], [1751, 0, 1405, 3], [1751, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfuE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBc;OgBI;oFC+C;UDM;8BE8B;SFoD;uDhBa;sBmBC,wBnB;OgBC;GpByB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}