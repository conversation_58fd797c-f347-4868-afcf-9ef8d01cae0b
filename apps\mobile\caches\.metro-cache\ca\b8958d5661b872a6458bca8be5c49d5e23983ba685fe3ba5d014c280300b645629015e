{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkTypeface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 48, "index": 79}}], "key": "oqqQaxz4M2TUiGCEbbh7Ll+rY0E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkTypefaceFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkTypeface = require(_dependencyMap[1], \"./JsiSkTypeface\");\n  class JsiSkTypefaceFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeFreeTypeFaceFromData(data) {\n      const tf = this.CanvasKit.Typeface.MakeFreeTypeFaceFromData(_JsiSkTypeface.JsiSkTypeface.fromValue(data));\n      if (tf === null) {\n        return null;\n      }\n      return new _JsiSkTypeface.JsiSkTypeface(this.CanvasKit, tf);\n    }\n  }\n  exports.JsiSkTypefaceFactory = JsiSkTypefaceFactory;\n});", "lineCount": 21, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkTypeface"], [7, 20, 2, 0], [7, 23, 2, 0, "require"], [7, 30, 2, 0], [7, 31, 2, 0, "_dependencyMap"], [7, 45, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkTypefaceFactory"], [8, 28, 3, 33], [8, 37, 3, 42, "Host"], [8, 47, 3, 46], [8, 48, 3, 47], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "MakeFreeTypeFaceFromData"], [12, 28, 7, 26, "MakeFreeTypeFaceFromData"], [12, 29, 7, 27, "data"], [12, 33, 7, 31], [12, 35, 7, 33], [13, 6, 8, 4], [13, 12, 8, 10, "tf"], [13, 14, 8, 12], [13, 17, 8, 15], [13, 21, 8, 19], [13, 22, 8, 20, "CanvasKit"], [13, 31, 8, 29], [13, 32, 8, 30, "Typeface"], [13, 40, 8, 38], [13, 41, 8, 39, "MakeFreeTypeFaceFromData"], [13, 65, 8, 63], [13, 66, 8, 64, "JsiSkTypeface"], [13, 94, 8, 77], [13, 95, 8, 78, "fromValue"], [13, 104, 8, 87], [13, 105, 8, 88, "data"], [13, 109, 8, 92], [13, 110, 8, 93], [13, 111, 8, 94], [14, 6, 9, 4], [14, 10, 9, 8, "tf"], [14, 12, 9, 10], [14, 17, 9, 15], [14, 21, 9, 19], [14, 23, 9, 21], [15, 8, 10, 6], [15, 15, 10, 13], [15, 19, 10, 17], [16, 6, 11, 4], [17, 6, 12, 4], [17, 13, 12, 11], [17, 17, 12, 15, "JsiSkTypeface"], [17, 45, 12, 28], [17, 46, 12, 29], [17, 50, 12, 33], [17, 51, 12, 34, "CanvasKit"], [17, 60, 12, 43], [17, 62, 12, 45, "tf"], [17, 64, 12, 47], [17, 65, 12, 48], [18, 4, 13, 2], [19, 2, 14, 0], [20, 2, 14, 1, "exports"], [20, 9, 14, 1], [20, 10, 14, 1, "JsiSkTypefaceFactory"], [20, 30, 14, 1], [20, 33, 14, 1, "JsiSkTypefaceFactory"], [20, 53, 14, 1], [21, 0, 14, 1], [21, 3]], "functionMap": {"names": ["<global>", "JsiSkTypefaceFactory", "constructor", "MakeFreeTypeFaceFromData"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFM;CDC"}}, "type": "js/module"}]}