{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createModuleProxy = exports.OptionalDependencyNotInstalledError = void 0;\n  // https://github.com/mrousavy/react-native-vision-camera/blob/main/package/src/dependencies/ModuleProxy.ts\n\n  /**\n   * Create a lazily-imported module proxy.\n   * This is useful for lazily requiring optional dependencies.\n   */\n  const createModuleProxy = getModule => {\n    const holder = {\n      module: undefined\n    };\n    const proxy = new Proxy(holder, {\n      get: (target, property) => {\n        if (target.module == null) {\n          // lazy initialize module via require()\n          // caller needs to make sure the require() call is wrapped in a try/catch\n          target.module = getModule();\n        }\n        return target.module[property];\n      }\n    });\n    return proxy;\n  };\n  exports.createModuleProxy = createModuleProxy;\n  class OptionalDependencyNotInstalledError extends Error {\n    constructor(name) {\n      super(`${name} is not installed!`);\n    }\n  }\n  exports.OptionalDependencyNotInstalledError = OptionalDependencyNotInstalledError;\n});", "lineCount": 35, "map": [[6, 2, 1, 0], [8, 2, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 2, 7, 7], [12, 8, 7, 13, "createModuleProxy"], [12, 25, 7, 30], [12, 28, 7, 33, "getModule"], [12, 37, 7, 42], [12, 41, 7, 46], [13, 4, 8, 2], [13, 10, 8, 8, "holder"], [13, 16, 8, 14], [13, 19, 8, 17], [14, 6, 9, 4, "module"], [14, 12, 9, 10], [14, 14, 9, 12, "undefined"], [15, 4, 10, 2], [15, 5, 10, 3], [16, 4, 11, 2], [16, 10, 11, 8, "proxy"], [16, 15, 11, 13], [16, 18, 11, 16], [16, 22, 11, 20, "Proxy"], [16, 27, 11, 25], [16, 28, 11, 26, "holder"], [16, 34, 11, 32], [16, 36, 11, 34], [17, 6, 12, 4, "get"], [17, 9, 12, 7], [17, 11, 12, 9, "get"], [17, 12, 12, 10, "target"], [17, 18, 12, 16], [17, 20, 12, 18, "property"], [17, 28, 12, 26], [17, 33, 12, 31], [18, 8, 13, 6], [18, 12, 13, 10, "target"], [18, 18, 13, 16], [18, 19, 13, 17, "module"], [18, 25, 13, 23], [18, 29, 13, 27], [18, 33, 13, 31], [18, 35, 13, 33], [19, 10, 14, 8], [20, 10, 15, 8], [21, 10, 16, 8, "target"], [21, 16, 16, 14], [21, 17, 16, 15, "module"], [21, 23, 16, 21], [21, 26, 16, 24, "getModule"], [21, 35, 16, 33], [21, 36, 16, 34], [21, 37, 16, 35], [22, 8, 17, 6], [23, 8, 18, 6], [23, 15, 18, 13, "target"], [23, 21, 18, 19], [23, 22, 18, 20, "module"], [23, 28, 18, 26], [23, 29, 18, 27, "property"], [23, 37, 18, 35], [23, 38, 18, 36], [24, 6, 19, 4], [25, 4, 20, 2], [25, 5, 20, 3], [25, 6, 20, 4], [26, 4, 21, 2], [26, 11, 21, 9, "proxy"], [26, 16, 21, 14], [27, 2, 22, 0], [27, 3, 22, 1], [28, 2, 22, 2, "exports"], [28, 9, 22, 2], [28, 10, 22, 2, "createModuleProxy"], [28, 27, 22, 2], [28, 30, 22, 2, "createModuleProxy"], [28, 47, 22, 2], [29, 2, 23, 7], [29, 8, 23, 13, "OptionalDependencyNotInstalledError"], [29, 43, 23, 48], [29, 52, 23, 57, "Error"], [29, 57, 23, 62], [29, 58, 23, 63], [30, 4, 24, 2, "constructor"], [30, 15, 24, 13, "constructor"], [30, 16, 24, 14, "name"], [30, 20, 24, 18], [30, 22, 24, 20], [31, 6, 25, 4], [31, 11, 25, 9], [31, 12, 25, 10], [31, 15, 25, 13, "name"], [31, 19, 25, 17], [31, 39, 25, 37], [31, 40, 25, 38], [32, 4, 26, 2], [33, 2, 27, 0], [34, 2, 27, 1, "exports"], [34, 9, 27, 1], [34, 10, 27, 1, "OptionalDependencyNotInstalledError"], [34, 45, 27, 1], [34, 48, 27, 1, "OptionalDependencyNotInstalledError"], [34, 83, 27, 1], [35, 0, 27, 1], [35, 3]], "functionMap": {"names": ["<global>", "createModuleProxy", "Proxy$argument_1.get", "OptionalDependencyNotInstalledError", "OptionalDependencyNotInstalledError#constructor"], "mappings": "AAA;iCCM;SCK;KDO;CDG;OGC;ECC;GDE;CHC"}}, "type": "js/module"}]}