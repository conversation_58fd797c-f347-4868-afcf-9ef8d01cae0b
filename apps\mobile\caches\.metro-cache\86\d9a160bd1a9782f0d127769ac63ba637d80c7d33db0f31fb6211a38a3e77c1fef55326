{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 50}, "end": {"line": 3, "column": 64, "index": 114}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "./getBackgroundColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 152}, "end": {"line": 5, "column": 54, "index": 206}}], "key": "nqjAGqam56vTmyU64Qnrg7Ihn8U=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  // Copyright © 2024 650 Industries.\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _getBackgroundColor = _interopRequireDefault(require(_dependencyMap[3], \"./getBackgroundColor\"));\n  var _jsxDevRuntime = require(_dependencyMap[4], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\expo-blur\\\\build\\\\BlurView.web.js\";\n  const BlurView = /*#__PURE__*/(0, _react.forwardRef)(({\n    tint = 'default',\n    intensity = 50,\n    style,\n    ...props\n  }, ref) => {\n    const blurViewRef = (0, _react.useRef)(null);\n    const blurStyle = getBlurStyle({\n      tint,\n      intensity\n    });\n    (0, _react.useImperativeHandle)(ref, () => ({\n      setNativeProps: nativeProps => {\n        if (!blurViewRef.current?.style) {\n          return;\n        }\n        // @ts-expect-error: `style.intensity` is not defined in the types\n        const nextIntensity = nativeProps.style?.intensity ?? intensity;\n        const blurStyle = getBlurStyle({\n          intensity: nextIntensity,\n          tint: tint ?? 'default'\n        });\n        if (nativeProps.style) {\n          for (const key in nativeProps.style) {\n            if (key !== 'intensity') {\n              blurViewRef.current.style[key] = nativeProps.style[key];\n            }\n          }\n        }\n        blurViewRef.current.style.backgroundColor = blurStyle.backgroundColor;\n        blurViewRef.current.style.backdropFilter = blurStyle.backdropFilter;\n        // @ts-expect-error: Webkit-specific legacy property (let's not type this, since it's deprecated)\n        blurViewRef.current.style['webkitBackdropFilter'] = blurStyle.WebkitBackdropFilter;\n      }\n    }), [intensity, tint]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      ...props,\n      style: [style, blurStyle]\n      /** @ts-expect-error: mismatch in ref type to support manually setting style props. */,\n      ref: blurViewRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this);\n  });\n  function getBlurStyle({\n    intensity,\n    tint\n  }) {\n    const blur = `saturate(180%) blur(${Math.min(intensity, 100) * 0.2}px)`;\n    return {\n      backgroundColor: (0, _getBackgroundColor.default)(Math.min(intensity, 100), tint),\n      backdropFilter: blur,\n      WebkitBackdropFilter: blur\n    };\n  }\n  var _default = exports.default = BlurView;\n});", "lineCount": 73, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_interopRequireDefault"], [5, 28, 2, 13], [5, 31, 2, 13, "require"], [5, 38, 2, 13], [5, 39, 2, 13, "_dependencyMap"], [5, 53, 2, 13], [6, 2, 2, 13, "Object"], [6, 8, 2, 13], [6, 9, 2, 13, "defineProperty"], [6, 23, 2, 13], [6, 24, 2, 13, "exports"], [6, 31, 2, 13], [7, 4, 2, 13, "value"], [7, 9, 2, 13], [8, 2, 2, 13], [9, 2, 2, 13, "exports"], [9, 9, 2, 13], [9, 10, 2, 13, "default"], [9, 17, 2, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_react"], [10, 12, 3, 0], [10, 15, 3, 0, "require"], [10, 22, 3, 0], [10, 23, 3, 0, "_dependencyMap"], [10, 37, 3, 0], [11, 2, 3, 64], [11, 6, 3, 64, "_View"], [11, 11, 3, 64], [11, 14, 3, 64, "_interopRequireDefault"], [11, 36, 3, 64], [11, 37, 3, 64, "require"], [11, 44, 3, 64], [11, 45, 3, 64, "_dependencyMap"], [11, 59, 3, 64], [12, 2, 5, 0], [12, 6, 5, 0, "_getBackgroundColor"], [12, 25, 5, 0], [12, 28, 5, 0, "_interopRequireDefault"], [12, 50, 5, 0], [12, 51, 5, 0, "require"], [12, 58, 5, 0], [12, 59, 5, 0, "_dependencyMap"], [12, 73, 5, 0], [13, 2, 5, 54], [13, 6, 5, 54, "_jsxDevRuntime"], [13, 20, 5, 54], [13, 23, 5, 54, "require"], [13, 30, 5, 54], [13, 31, 5, 54, "_dependencyMap"], [13, 45, 5, 54], [14, 2, 5, 54], [14, 6, 5, 54, "_jsxFileName"], [14, 18, 5, 54], [15, 2, 6, 0], [15, 8, 6, 6, "BlurView"], [15, 16, 6, 14], [15, 32, 6, 17], [15, 36, 6, 17, "forwardRef"], [15, 53, 6, 27], [15, 55, 6, 28], [15, 56, 6, 29], [16, 4, 6, 31, "tint"], [16, 8, 6, 35], [16, 11, 6, 38], [16, 20, 6, 47], [17, 4, 6, 49, "intensity"], [17, 13, 6, 58], [17, 16, 6, 61], [17, 18, 6, 63], [18, 4, 6, 65, "style"], [18, 9, 6, 70], [19, 4, 6, 72], [19, 7, 6, 75, "props"], [20, 2, 6, 81], [20, 3, 6, 82], [20, 5, 6, 84, "ref"], [20, 8, 6, 87], [20, 13, 6, 92], [21, 4, 7, 4], [21, 10, 7, 10, "blurViewRef"], [21, 21, 7, 21], [21, 24, 7, 24], [21, 28, 7, 24, "useRef"], [21, 41, 7, 30], [21, 43, 7, 31], [21, 47, 7, 35], [21, 48, 7, 36], [22, 4, 8, 4], [22, 10, 8, 10, "blurStyle"], [22, 19, 8, 19], [22, 22, 8, 22, "getBlurStyle"], [22, 34, 8, 34], [22, 35, 8, 35], [23, 6, 8, 37, "tint"], [23, 10, 8, 41], [24, 6, 8, 43, "intensity"], [25, 4, 8, 53], [25, 5, 8, 54], [25, 6, 8, 55], [26, 4, 9, 4], [26, 8, 9, 4, "useImperativeHandle"], [26, 34, 9, 23], [26, 36, 9, 24, "ref"], [26, 39, 9, 27], [26, 41, 9, 29], [26, 48, 9, 36], [27, 6, 10, 8, "setNativeProps"], [27, 20, 10, 22], [27, 22, 10, 25, "nativeProps"], [27, 33, 10, 36], [27, 37, 10, 41], [28, 8, 11, 12], [28, 12, 11, 16], [28, 13, 11, 17, "blurViewRef"], [28, 24, 11, 28], [28, 25, 11, 29, "current"], [28, 32, 11, 36], [28, 34, 11, 38, "style"], [28, 39, 11, 43], [28, 41, 11, 45], [29, 10, 12, 16], [30, 8, 13, 12], [31, 8, 14, 12], [32, 8, 15, 12], [32, 14, 15, 18, "nextIntensity"], [32, 27, 15, 31], [32, 30, 15, 34, "nativeProps"], [32, 41, 15, 45], [32, 42, 15, 46, "style"], [32, 47, 15, 51], [32, 49, 15, 53, "intensity"], [32, 58, 15, 62], [32, 62, 15, 66, "intensity"], [32, 71, 15, 75], [33, 8, 16, 12], [33, 14, 16, 18, "blurStyle"], [33, 23, 16, 27], [33, 26, 16, 30, "getBlurStyle"], [33, 38, 16, 42], [33, 39, 16, 43], [34, 10, 16, 45, "intensity"], [34, 19, 16, 54], [34, 21, 16, 56, "nextIntensity"], [34, 34, 16, 69], [35, 10, 16, 71, "tint"], [35, 14, 16, 75], [35, 16, 16, 77, "tint"], [35, 20, 16, 81], [35, 24, 16, 85], [36, 8, 16, 95], [36, 9, 16, 96], [36, 10, 16, 97], [37, 8, 17, 12], [37, 12, 17, 16, "nativeProps"], [37, 23, 17, 27], [37, 24, 17, 28, "style"], [37, 29, 17, 33], [37, 31, 17, 35], [38, 10, 18, 16], [38, 15, 18, 21], [38, 21, 18, 27, "key"], [38, 24, 18, 30], [38, 28, 18, 34, "nativeProps"], [38, 39, 18, 45], [38, 40, 18, 46, "style"], [38, 45, 18, 51], [38, 47, 18, 53], [39, 12, 19, 20], [39, 16, 19, 24, "key"], [39, 19, 19, 27], [39, 24, 19, 32], [39, 35, 19, 43], [39, 37, 19, 45], [40, 14, 20, 24, "blurViewRef"], [40, 25, 20, 35], [40, 26, 20, 36, "current"], [40, 33, 20, 43], [40, 34, 20, 44, "style"], [40, 39, 20, 49], [40, 40, 20, 50, "key"], [40, 43, 20, 53], [40, 44, 20, 54], [40, 47, 21, 28, "nativeProps"], [40, 58, 21, 39], [40, 59, 21, 40, "style"], [40, 64, 21, 45], [40, 65, 21, 46, "key"], [40, 68, 21, 49], [40, 69, 21, 50], [41, 12, 22, 20], [42, 10, 23, 16], [43, 8, 24, 12], [44, 8, 25, 12, "blurViewRef"], [44, 19, 25, 23], [44, 20, 25, 24, "current"], [44, 27, 25, 31], [44, 28, 25, 32, "style"], [44, 33, 25, 37], [44, 34, 25, 38, "backgroundColor"], [44, 49, 25, 53], [44, 52, 25, 56, "blurStyle"], [44, 61, 25, 65], [44, 62, 25, 66, "backgroundColor"], [44, 77, 25, 81], [45, 8, 26, 12, "blurViewRef"], [45, 19, 26, 23], [45, 20, 26, 24, "current"], [45, 27, 26, 31], [45, 28, 26, 32, "style"], [45, 33, 26, 37], [45, 34, 26, 38, "<PERSON><PERSON>ilter"], [45, 48, 26, 52], [45, 51, 26, 55, "blurStyle"], [45, 60, 26, 64], [45, 61, 26, 65, "<PERSON><PERSON>ilter"], [45, 75, 26, 79], [46, 8, 27, 12], [47, 8, 28, 12, "blurViewRef"], [47, 19, 28, 23], [47, 20, 28, 24, "current"], [47, 27, 28, 31], [47, 28, 28, 32, "style"], [47, 33, 28, 37], [47, 34, 28, 38], [47, 56, 28, 60], [47, 57, 28, 61], [47, 60, 28, 64, "blurStyle"], [47, 69, 28, 73], [47, 70, 28, 74, "WebkitBackdropFilter"], [47, 90, 28, 94], [48, 6, 29, 8], [49, 4, 30, 4], [49, 5, 30, 5], [49, 6, 30, 6], [49, 8, 30, 8], [49, 9, 30, 9, "intensity"], [49, 18, 30, 18], [49, 20, 30, 20, "tint"], [49, 24, 30, 24], [49, 25, 30, 25], [49, 26, 30, 26], [50, 4, 31, 4], [50, 24, 31, 12], [50, 28, 31, 12, "_jsxDevRuntime"], [50, 42, 31, 12], [50, 43, 31, 12, "jsxDEV"], [50, 49, 31, 12], [50, 51, 31, 13, "_View"], [50, 56, 31, 13], [50, 57, 31, 13, "default"], [50, 64, 31, 17], [51, 6, 31, 17], [51, 9, 31, 22, "props"], [51, 14, 31, 27], [52, 6, 31, 29, "style"], [52, 11, 31, 34], [52, 13, 31, 36], [52, 14, 31, 37, "style"], [52, 19, 31, 42], [52, 21, 31, 44, "blurStyle"], [52, 30, 31, 53], [53, 6, 32, 4], [54, 6, 33, 4, "ref"], [54, 9, 33, 7], [54, 11, 33, 9, "blurViewRef"], [55, 4, 33, 21], [56, 6, 33, 21, "fileName"], [56, 14, 33, 21], [56, 16, 33, 21, "_jsxFileName"], [56, 28, 33, 21], [57, 6, 33, 21, "lineNumber"], [57, 16, 33, 21], [58, 6, 33, 21, "columnNumber"], [58, 18, 33, 21], [59, 4, 33, 21], [59, 11, 33, 22], [59, 12, 33, 23], [60, 2, 34, 0], [60, 3, 34, 1], [60, 4, 34, 2], [61, 2, 35, 0], [61, 11, 35, 9, "getBlurStyle"], [61, 23, 35, 21, "getBlurStyle"], [61, 24, 35, 22], [62, 4, 35, 24, "intensity"], [62, 13, 35, 33], [63, 4, 35, 35, "tint"], [64, 2, 35, 41], [64, 3, 35, 42], [64, 5, 35, 44], [65, 4, 36, 4], [65, 10, 36, 10, "blur"], [65, 14, 36, 14], [65, 17, 36, 17], [65, 40, 36, 40, "Math"], [65, 44, 36, 44], [65, 45, 36, 45, "min"], [65, 48, 36, 48], [65, 49, 36, 49, "intensity"], [65, 58, 36, 58], [65, 60, 36, 60], [65, 63, 36, 63], [65, 64, 36, 64], [65, 67, 36, 67], [65, 70, 36, 70], [65, 75, 36, 75], [66, 4, 37, 4], [66, 11, 37, 11], [67, 6, 38, 8, "backgroundColor"], [67, 21, 38, 23], [67, 23, 38, 25], [67, 27, 38, 25, "getBackgroundColor"], [67, 54, 38, 43], [67, 56, 38, 44, "Math"], [67, 60, 38, 48], [67, 61, 38, 49, "min"], [67, 64, 38, 52], [67, 65, 38, 53, "intensity"], [67, 74, 38, 62], [67, 76, 38, 64], [67, 79, 38, 67], [67, 80, 38, 68], [67, 82, 38, 70, "tint"], [67, 86, 38, 74], [67, 87, 38, 75], [68, 6, 39, 8, "<PERSON><PERSON>ilter"], [68, 20, 39, 22], [68, 22, 39, 24, "blur"], [68, 26, 39, 28], [69, 6, 40, 8, "WebkitBackdropFilter"], [69, 26, 40, 28], [69, 28, 40, 30, "blur"], [70, 4, 41, 4], [70, 5, 41, 5], [71, 2, 42, 0], [72, 2, 42, 1], [72, 6, 42, 1, "_default"], [72, 14, 42, 1], [72, 17, 42, 1, "exports"], [72, 24, 42, 1], [72, 25, 42, 1, "default"], [72, 32, 42, 1], [72, 35, 43, 15, "BlurView"], [72, 43, 43, 23], [73, 0, 43, 23], [73, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0", "useImperativeHandle$argument_1", "setNativeProps", "getBlurStyle"], "mappings": "AAA;4BCK;6BCG;wBCC;SDmB;MDC;CDI;AIC;CJO"}}, "type": "js/module"}]}