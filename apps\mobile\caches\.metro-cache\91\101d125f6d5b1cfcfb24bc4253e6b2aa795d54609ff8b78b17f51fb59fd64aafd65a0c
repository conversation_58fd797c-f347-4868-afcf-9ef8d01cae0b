{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Line = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  const Line = props => {\n    return /*#__PURE__*/_react.default.createElement(\"skLine\", props);\n  };\n  exports.Line = Line;\n});", "lineCount": 12, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 7], [8, 8, 2, 13, "Line"], [8, 12, 2, 17], [8, 15, 2, 20, "props"], [8, 20, 2, 25], [8, 24, 2, 29], [9, 4, 3, 2], [9, 11, 3, 9], [9, 24, 3, 22, "React"], [9, 38, 3, 27], [9, 39, 3, 28, "createElement"], [9, 52, 3, 41], [9, 53, 3, 42], [9, 61, 3, 50], [9, 63, 3, 52, "props"], [9, 68, 3, 57], [9, 69, 3, 58], [10, 2, 4, 0], [10, 3, 4, 1], [11, 2, 4, 2, "exports"], [11, 9, 4, 2], [11, 10, 4, 2, "Line"], [11, 14, 4, 2], [11, 17, 4, 2, "Line"], [11, 21, 4, 2], [12, 0, 4, 2], [12, 3]], "functionMap": {"names": ["<global>", "Line"], "mappings": "AAA;oBCC;CDE"}}, "type": "js/module"}]}