"use strict";
/**
 * @license
 * Copyright 2018 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var _this = this;
Object.defineProperty(exports, "__esModule", { value: true });
var tf = require("../index");
var jasmine_util_1 = require("../jasmine_util");
var test_util_1 = require("../test_util");
var util = require("../util");
var rand_util_1 = require("./rand_util");
jasmine_util_1.describeWithFlags('broadcastTo', jasmine_util_1.ALL_ENVS, function () {
    it('[] -> [3,2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, A, _a, _b, w, f, h, df, dh, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    a = tf.scalar(4.2);
                    A = tf.tensor2d([[4.2, 4.2],
                        [4.2, 4.2],
                        [4.2, 4.2]]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, A.array()];
                case 1:
                    _b = [_e.sent()];
                    return [4 /*yield*/, tf.broadcastTo(a, A.shape).array()];
                case 2:
                    _a.apply(void 0, _b.concat([_e.sent()]));
                    w = tf.tensor2d([[4.7, 4.5],
                        [-6.1, -6.6],
                        [-8.1, -3.4]]), f = function (a) { return tf.broadcastTo(a, A.shape).mul(w).mean().asScalar(); }, h = function (a) { return a.mul(w).mean().asScalar(); };
                    df = tf.grad(f), dh = tf.grad(h);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, df(a).array()];
                case 3:
                    _d = [_e.sent()];
                    return [4 /*yield*/, dh(a).array()];
                case 4:
                    _c.apply(void 0, _d.concat([_e.sent()]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('[2] -> [3,2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, A, _a, _b, w, f, h, df, dh, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    a = tf.tensor1d([1, 2]);
                    A = tf.tensor2d([[1, 2],
                        [1, 2],
                        [1, 2]]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, A.array()];
                case 1:
                    _b = [_e.sent()];
                    return [4 /*yield*/, tf.broadcastTo(a, A.shape).array()];
                case 2:
                    _a.apply(void 0, _b.concat([_e.sent()]));
                    w = tf.tensor2d([[4.7, 4.5],
                        [-6.1, -6.6],
                        [-8.1, -3.4]]), f = function (a) { return tf.broadcastTo(a, A.shape).mul(w).mean().asScalar(); }, h = function (a) { return a.mul(w).mean().asScalar(); };
                    df = tf.grad(f), dh = tf.grad(h);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, df(a).array()];
                case 3:
                    _d = [_e.sent()];
                    return [4 /*yield*/, dh(a).array()];
                case 4:
                    _c.apply(void 0, _d.concat([_e.sent()]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('[3,1] -> [3,2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, A, _a, _b, w, f, h, df, dh, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    a = tf.tensor2d([[1],
                        [2],
                        [3]]);
                    A = tf.tensor2d([[1, 1],
                        [2, 2],
                        [3, 3]]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, A.array()];
                case 1:
                    _b = [_e.sent()];
                    return [4 /*yield*/, tf.broadcastTo(a, A.shape).array()];
                case 2:
                    _a.apply(void 0, _b.concat([_e.sent()]));
                    w = tf.tensor2d([[4.7, 4.5],
                        [-6.1, -6.6],
                        [-8.1, -3.4]]), f = function (a) { return tf.broadcastTo(a, A.shape).mul(w).mean().asScalar(); }, h = function (a) { return a.mul(w).mean().asScalar(); };
                    df = tf.grad(f), dh = tf.grad(h);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, df(a).array()];
                case 3:
                    _d = [_e.sent()];
                    return [4 /*yield*/, dh(a).array()];
                case 4:
                    _c.apply(void 0, _d.concat([_e.sent()]));
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('zeros', jasmine_util_1.ALL_ENVS, function () {
    it('1D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([2, 2, 2]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([2, 2, 2], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([2, 2, 2], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([2, 2, 2], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2, 1, 1]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2, 1, 1], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2, 1, 1], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.zeros([3, 2, 1, 1], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('ones', jasmine_util_1.ALL_ENVS, function () {
    it('1D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([2, 2, 2]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([2, 2, 2], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([2, 2, 2], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([2, 2, 2], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2, 1, 1]);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2, 1, 1], 'float32');
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2, 1, 1], 'int32');
                    expect(a.dtype).toBe('int32');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.ones([3, 2, 1, 1], 'bool');
                    expect(a.dtype).toBe('bool');
                    expect(a.shape).toEqual([3, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('zerosLike', jasmine_util_1.ALL_ENVS, function () {
    it('1D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('chainable 1D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    b = a.zerosLike();
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'float32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'int32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'bool');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2]);
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'float32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'int32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'bool');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'float32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'int32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'bool');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1]);
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'float32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'int32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'bool');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1]);
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'float32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'int32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'bool');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1]);
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'float32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'int32');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'bool');
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1]);
                    b = tf.zerosLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('zerosLike gradient', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor2d([[0, 1, 2], [4, 5, 6]]);
                    gradients = tf.grad(function (x) { return tf.zerosLike(x); })(x);
                    expect(gradients.shape).toEqual([2, 3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.zerosLike({}); })
            .toThrowError(/Argument 'x' passed to 'zerosLike' must be a Tensor/);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.zerosLike([[1, 2], [3, 4]]);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('onesLike', jasmine_util_1.ALL_ENVS, function () {
    it('1D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('chainable 1D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    b = a.onesLike();
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'float32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'int32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'bool');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D complex dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var real, imag, a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    real = tf.tensor1d([1, 2, 3], 'float32');
                    imag = tf.tensor1d([1, 2, 3], 'float32');
                    a = tf.complex(real, imag);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2]);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'float32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'int32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'bool');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D complex dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var real, imag, a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    real = tf.tensor2d([1, 2, 3, 4], [2, 2], 'float32');
                    imag = tf.tensor2d([1, 2, 3, 4], [2, 2], 'float32');
                    a = tf.complex(real, imag);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 0, 1, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'float32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'int32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'bool');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D complex dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var real, imag, a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    real = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'float32');
                    imag = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'float32');
                    a = tf.complex(real, imag);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 0, 1, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1]);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'float32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'int32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'bool');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1]);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D complex dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var real, imag, a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    real = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'float32');
                    imag = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'float32');
                    a = tf.complex(real, imag);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 0, 1, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'float32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'int32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'bool');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1]);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D complex dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var real, imag, a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    real = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'float32');
                    imag = tf.tensor5d([1, 2, 3, 4], [1, 2, 2, 1, 1], 'float32');
                    a = tf.complex(real, imag);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 0, 1, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'int32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'bool');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1]);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'float32');
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('6D complex dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var real, imag, a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    real = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'float32');
                    imag = tf.tensor6d([1, 2, 3, 4], [1, 2, 2, 1, 1, 1], 'float32');
                    a = tf.complex(real, imag);
                    b = tf.onesLike(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([1, 2, 2, 1, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 0, 1, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.onesLike({}); })
            .toThrowError(/Argument 'x' passed to 'onesLike' must be a Tensor/);
    });
    it('onesLike gradient', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor2d([[0, 1, 2], [4, 5, 6]]);
                    gradients = tf.grad(function (x) { return tf.onesLike(x); })(x);
                    expect(gradients.shape).toEqual([2, 3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.onesLike([[1, 2], [3, 4]]);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('rand', jasmine_util_1.ALL_ENVS, function () {
    it('should return a random 1D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [10];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2); });
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2]);
                    result = tf.rand(shape, function () { return util.randUniform(0, 1.5); });
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 1D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [10];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2); }, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 1D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [10];
                    result = tf.rand(shape, function () { return util.randUniform(0, 1); }, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2.5); });
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.rand(shape, function () { return util.randUniform(0, 1.5); }, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2); }, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.rand(shape, function () { return util.randUniform(0, 1); }, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2.5); });
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.rand(shape, function () { return util.randUniform(0, 1.5); }, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2); }, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.rand(shape, function () { return util.randUniform(0, 1); }, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2.5); });
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.rand(shape, function () { return util.randUniform(0, 1.5); });
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.rand(shape, function () { return util.randUniform(0, 2); }, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.rand(shape, function () { return util.randUniform(0, 1); }, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('eye', jasmine_util_1.ALL_ENVS, function () {
    it('1x1', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(1);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1]]);
                    expect(r.shape).toEqual([1, 1]);
                    expect(r.dtype).toBe('float32');
                    return [2 /*return*/];
            }
        });
    }); });
    it('2x2', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(2);
                    expect(r.shape).toEqual([2, 2]);
                    expect(r.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3x3', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(3);
                    expect(r.shape).toEqual([3, 3]);
                    expect(r.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 0, 1, 0, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3x4', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(3, 4);
                    expect(r.shape).toEqual([3, 4]);
                    expect(r.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4x3', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(4, 3);
                    expect(r.shape).toEqual([4, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('with 1D batchShape', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(2, 2, [3]);
                    expect(r.shape).toEqual([3, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('with 2D batchShape', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(2, 2, [2, 3]);
                    expect(r.shape).toEqual([2, 3, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [
                            1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('with 3D batchShape', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(2, 2, [2, 2, 3]);
                    expect(r.shape).toEqual([2, 2, 3, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [
                            1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1,
                            1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3x3, int32', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(3, 3, null, 'int32');
                    expect(r.dtype).toBe('int32');
                    expect(r.shape).toEqual([3, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 0, 1, 0, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3x3, bool', function () { return __awaiter(_this, void 0, void 0, function () {
        var r, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    r = tf.eye(3, 3, null, 'bool');
                    expect(r.dtype).toBe('bool');
                    expect(r.shape).toEqual([3, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, r.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 0, 1, 0, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('randomNormal', jasmine_util_1.ALL_ENVS, function () {
    var SEED = 2002;
    var EPSILON = 0.05;
    it('should return a float32 1D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    SAMPLES = 10000;
                    result = tf.randomNormal([SAMPLES], 0, 0.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual([SAMPLES]);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 0.5, EPSILON]);
                    result = tf.randomNormal([SAMPLES], 0, 1.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual([SAMPLES]);
                    _c = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent()]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 1.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a int32 1D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    SAMPLES = 10000;
                    result = tf.randomNormal([SAMPLES], 0, 2, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    expect(result.shape).toEqual([SAMPLES]);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 2, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a float32 2D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    SAMPLES = 100;
                    result = tf.randomNormal([SAMPLES, SAMPLES], 0, 2.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual([SAMPLES, SAMPLES]);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 2.5, EPSILON]);
                    result = tf.randomNormal([SAMPLES, SAMPLES], 0, 3.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual([SAMPLES, SAMPLES]);
                    _c = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent()]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 3.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a int32 2D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    SAMPLES = 100;
                    result = tf.randomNormal([SAMPLES, SAMPLES], 0, 2, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    expect(result.shape).toEqual([SAMPLES, SAMPLES]);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 2, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a float32 3D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES_SHAPE, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    SAMPLES_SHAPE = [20, 20, 20];
                    result = tf.randomNormal(SAMPLES_SHAPE, 0, 0.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual(SAMPLES_SHAPE);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 0.5, EPSILON]);
                    result = tf.randomNormal(SAMPLES_SHAPE, 0, 1.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual(SAMPLES_SHAPE);
                    _c = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent()]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 1.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a int32 3D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES_SHAPE, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    SAMPLES_SHAPE = [20, 20, 20];
                    result = tf.randomNormal(SAMPLES_SHAPE, 0, 2, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    expect(result.shape).toEqual(SAMPLES_SHAPE);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 2, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a float32 4D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES_SHAPE, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    SAMPLES_SHAPE = [10, 10, 10, 10];
                    result = tf.randomNormal(SAMPLES_SHAPE, 0, 0.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual(SAMPLES_SHAPE);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 0.5, EPSILON]);
                    result = tf.randomNormal(SAMPLES_SHAPE, 0, 1.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    expect(result.shape).toEqual(SAMPLES_SHAPE);
                    _c = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent()]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 1.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a int32 4D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES_SHAPE, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    SAMPLES_SHAPE = [10, 10, 10, 10];
                    result = tf.randomNormal(SAMPLES_SHAPE, 0, 2, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    expect(result.shape).toEqual(SAMPLES_SHAPE);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 2, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a int32 5D of random normal values', function () { return __awaiter(_this, void 0, void 0, function () {
        var SAMPLES_SHAPE, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    SAMPLES_SHAPE = [10, 10, 10, 10, 10];
                    result = tf.randomNormal(SAMPLES_SHAPE, 0, 2, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    expect(result.shape).toEqual(SAMPLES_SHAPE);
                    _a = rand_util_1.jarqueBeraNormalityTest;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent()]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 2, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('truncatedNormal', jasmine_util_1.ALL_ENVS, function () {
    // Expect slightly higher variances for truncated values.
    var EPSILON = 0.60;
    var SEED = 2002;
    function assertTruncatedValues(values, mean, stdv) {
        var bounds = mean + stdv * 2;
        for (var i = 0; i < values.length; i++) {
            expect(Math.abs(values[i])).toBeLessThanOrEqual(bounds);
        }
    }
    it('should return a random 1D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    shape = [1000];
                    result = tf.truncatedNormal(shape, 0, 3.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), 0, 3.5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 3.5, EPSILON]);
                    result = tf.truncatedNormal(shape, 0, 4.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    _c = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), 0, 4.5]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 4.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a randon 1D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [1000];
                    result = tf.truncatedNormal(shape, 0, 5, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a 2D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    shape = [50, 50];
                    result = tf.truncatedNormal(shape, 0, 3.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), 0, 3.5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 3.5, EPSILON]);
                    result = tf.truncatedNormal(shape, 0, 4.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    _c = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), 0, 4.5]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 4.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a 2D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [50, 50];
                    result = tf.truncatedNormal(shape, 0, 5, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a 3D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    shape = [10, 10, 10];
                    result = tf.truncatedNormal(shape, 0, 3.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), 0, 3.5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 3.5, EPSILON]);
                    result = tf.truncatedNormal(shape, 0, 4.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    _c = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), 0, 4.5]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 4.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a 3D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [10, 10, 10];
                    result = tf.truncatedNormal(shape, 0, 5, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a 4D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    shape = [5, 5, 5, 5];
                    result = tf.truncatedNormal(shape, 0, 3.5, null, SEED);
                    expect(result.dtype).toBe('float32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), 0, 3.5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), 0, 3.5, EPSILON]);
                    result = tf.truncatedNormal(shape, 0, 4.5, 'float32', SEED);
                    expect(result.dtype).toBe('float32');
                    _c = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), 0, 4.5]);
                    _d = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), 0, 4.5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a 4D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [5, 5, 5, 5];
                    result = tf.truncatedNormal(shape, 0, 5, 'int32', SEED);
                    expect(result.dtype).toBe('int32');
                    _a = assertTruncatedValues;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 5]);
                    _b = rand_util_1.expectArrayInMeanStdRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 5, EPSILON]);
                    return [2 /*return*/];
            }
        });
    }); });
});
var GAMMA_MIN = 0;
var GAMMA_MAX = 40;
jasmine_util_1.describeWithFlags('randomGamma', jasmine_util_1.ALL_ENVS, function () {
    it('should return a random 1D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [10];
                    result = tf.randomGamma(shape, 2, 2);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    result = tf.randomGamma(shape, 2, 2, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 1D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [10];
                    result = tf.randomGamma(shape, 2, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.randomGamma(shape, 2, 2);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    result = tf.randomGamma(shape, 2, 2, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.randomGamma(shape, 2, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.randomGamma(shape, 2, 2);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    result = tf.randomGamma(shape, 2, 2, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.randomGamma(shape, 2, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.randomGamma(shape, 2, 2);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    result = tf.randomGamma(shape, 2, 2, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.randomGamma(shape, 2, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 5D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [2, 3, 4, 5, 6];
                    result = tf.randomGamma(shape, 2, 2);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    result = tf.randomGamma(shape, 2, 2, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 5D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [2, 3, 4, 5, 6];
                    result = tf.randomGamma(shape, 2, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), GAMMA_MIN, GAMMA_MAX]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('randomUniform', jasmine_util_1.ALL_ENVS, function () {
    it('should return a random 1D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [10];
                    result = tf.randomUniform(shape, 0, 2.5);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.randomUniform(shape, 0, 1.5, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 1D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [10];
                    result = tf.randomUniform(shape, 0, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 1D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [10];
                    result = tf.randomUniform(shape, 0, 1, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.randomUniform(shape, 0, 2.5);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.randomUniform(shape, 0, 1.5, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.randomUniform(shape, 0, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 2D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4];
                    result = tf.randomUniform(shape, 0, 1, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.randomUniform(shape, 0, 2.5);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.randomUniform(shape, 0, 1.5, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.randomUniform(shape, 0, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 3D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5];
                    result = tf.randomUniform(shape, 0, 1, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.randomUniform(shape, 0, 2.5);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.randomUniform(shape, 0, 1.5, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.randomUniform(shape, 0, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 4D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [3, 4, 5, 6];
                    result = tf.randomUniform(shape, 0, 1, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 5D float32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    shape = [2, 3, 4, 5, 6];
                    result = tf.randomUniform(shape, 0, 2.5);
                    expect(result.dtype).toBe('float32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), 0, 2.5]);
                    result = tf.randomUniform(shape, 0, 1.5, 'float32');
                    expect(result.dtype).toBe('float32');
                    _b = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), 0, 1.5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 5D int32 array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [2, 3, 4, 5, 6];
                    result = tf.randomUniform(shape, 0, 2, 'int32');
                    expect(result.dtype).toBe('int32');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should return a random 5D bool array', function () { return __awaiter(_this, void 0, void 0, function () {
        var shape, result, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    shape = [2, 3, 4, 5, 6];
                    result = tf.randomUniform(shape, 0, 1, 'bool');
                    expect(result.dtype).toBe('bool');
                    _a = test_util_1.expectValuesInRange;
                    return [4 /*yield*/, result.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), 0, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
});
var MockContext = /** @class */ (function () {
    function MockContext() {
    }
    MockContext.prototype.getImageData = function (x, y, width, height) {
        var data = new Uint8ClampedArray(width * height * 4);
        for (var i = 0; i < data.length; ++i) {
            data[i] = i + 1;
        }
        return { data: data };
    };
    return MockContext;
}());
var MockCanvas = /** @class */ (function () {
    function MockCanvas(width, height) {
        this.width = width;
        this.height = height;
    }
    MockCanvas.prototype.getContext = function (type) {
        return new MockContext();
    };
    return MockCanvas;
}());
jasmine_util_1.describeWithFlags('fromPixels, mock canvas', jasmine_util_1.NODE_ENVS, function () {
    it('accepts a canvas-like element', function () { return __awaiter(_this, void 0, void 0, function () {
        var c, t, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    c = new MockCanvas(2, 2);
                    t = tf.browser.fromPixels(c);
                    expect(t.dtype).toBe('int32');
                    expect(t.shape).toEqual([2, 2, 3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 15]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('accepts a canvas-like element, numChannels=4', function () { return __awaiter(_this, void 0, void 0, function () {
        var c, t, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    c = new MockCanvas(2, 2);
                    t = tf.browser.fromPixels(c, 4);
                    expect(t.dtype).toBe('int32');
                    expect(t.shape).toEqual([2, 2, 4]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('errors when passed a non-canvas object', function () {
        // tslint:disable-next-line:no-any
        expect(function () { return tf.browser.fromPixels(5); }).toThrowError();
    });
});
jasmine_util_1.describeWithFlags('fromPixels', jasmine_util_1.BROWSER_ENVS, function () {
    it('ImageData 1x1x3', function () { return __awaiter(_this, void 0, void 0, function () {
        var pixels, array, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    pixels = new ImageData(1, 1);
                    pixels.data[0] = 0;
                    pixels.data[1] = 80;
                    pixels.data[2] = 160;
                    pixels.data[3] = 240;
                    array = tf.browser.fromPixels(pixels, 3);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, array.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 80, 160]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('ImageData 1x1x4', function () { return __awaiter(_this, void 0, void 0, function () {
        var pixels, array, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    pixels = new ImageData(1, 1);
                    pixels.data[0] = 0;
                    pixels.data[1] = 80;
                    pixels.data[2] = 160;
                    pixels.data[3] = 240;
                    array = tf.browser.fromPixels(pixels, 4);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, array.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 80, 160, 240]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('ImageData 2x2x3', function () { return __awaiter(_this, void 0, void 0, function () {
        var pixels, i, i, array, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    pixels = new ImageData(2, 2);
                    for (i = 0; i < 8; i++) {
                        pixels.data[i] = i * 2;
                    }
                    for (i = 8; i < 16; i++) {
                        pixels.data[i] = i * 2;
                    }
                    array = tf.browser.fromPixels(pixels, 3);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, array.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 2, 4, 8, 10, 12, 16, 18, 20, 24, 26, 28]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('ImageData 2x2x4', function () { return __awaiter(_this, void 0, void 0, function () {
        var pixels, i, i, array, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    pixels = new ImageData(2, 2);
                    for (i = 0; i < 8; i++) {
                        pixels.data[i] = i * 2;
                    }
                    for (i = 8; i < 16; i++) {
                        pixels.data[i] = i * 2;
                    }
                    array = tf.browser.fromPixels(pixels, 4);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, array.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        new Int32Array([0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30])]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels, 3 channels', function () { return __awaiter(_this, void 0, void 0, function () {
        var pixels, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    pixels = new ImageData(1, 2);
                    pixels.data[0] = 2;
                    pixels.data[1] = 3;
                    pixels.data[2] = 4;
                    pixels.data[3] = 255; // Not used.
                    pixels.data[4] = 5;
                    pixels.data[5] = 6;
                    pixels.data[6] = 7;
                    pixels.data[7] = 255; // Not used.
                    res = tf.browser.fromPixels(pixels, 3);
                    expect(res.shape).toEqual([2, 1, 3]);
                    expect(res.dtype).toBe('int32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 3, 4, 5, 6, 7]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels, reshape, then do tf.add()', function () { return __awaiter(_this, void 0, void 0, function () {
        var pixels, a, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    pixels = new ImageData(1, 1);
                    pixels.data[0] = 2;
                    pixels.data[1] = 3;
                    pixels.data[2] = 4;
                    pixels.data[3] = 255; // Not used.
                    a = tf.browser.fromPixels(pixels, 3).reshape([1, 1, 1, 3]);
                    res = a.add(tf.scalar(2, 'int32'));
                    expect(res.shape).toEqual([1, 1, 1, 3]);
                    expect(res.dtype).toBe('int32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels + fromPixels', function () { return __awaiter(_this, void 0, void 0, function () {
        var pixelsA, pixelsB, a, b, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    pixelsA = new ImageData(1, 1);
                    pixelsA.data[0] = 255;
                    pixelsA.data[1] = 3;
                    pixelsA.data[2] = 4;
                    pixelsA.data[3] = 255; // Not used.
                    pixelsB = new ImageData(1, 1);
                    pixelsB.data[0] = 5;
                    pixelsB.data[1] = 6;
                    pixelsB.data[2] = 7;
                    pixelsB.data[3] = 255; // Not used.
                    a = tf.browser.fromPixels(pixelsA, 3).toFloat();
                    b = tf.browser.fromPixels(pixelsB, 3).toFloat();
                    res = a.add(b);
                    expect(res.shape).toEqual([1, 1, 3]);
                    expect(res.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [260, 9, 11]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels for PixelData type', function () { return __awaiter(_this, void 0, void 0, function () {
        var dataA, pixelsA, dataB, pixelsB, a, b, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    dataA = new Uint8Array([255, 3, 4, 255]);
                    pixelsA = { width: 1, height: 1, data: dataA };
                    dataB = new Uint8Array([5, 6, 7, 255]);
                    pixelsB = { width: 1, height: 1, data: dataB };
                    a = tf.browser.fromPixels(pixelsA, 3).toFloat();
                    b = tf.browser.fromPixels(pixelsB, 3).toFloat();
                    res = a.add(b);
                    expect(res.shape).toEqual([1, 1, 3]);
                    expect(res.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [260, 9, 11]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels for HTMLCanvasElement', function () { return __awaiter(_this, void 0, void 0, function () {
        var canvas, ctx, pixels, res, data;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    canvas = document.createElement('canvas');
                    canvas.width = 1;
                    canvas.height = 1;
                    ctx = canvas.getContext('2d');
                    pixels = new ImageData(1, 1);
                    pixels.data[0] = 0;
                    pixels.data[1] = 80;
                    pixels.data[2] = 160;
                    pixels.data[3] = 240;
                    ctx.putImageData(pixels, 1, 1);
                    res = tf.browser.fromPixels(canvas);
                    expect(res.shape).toEqual([1, 1, 3]);
                    return [4 /*yield*/, res.data()];
                case 1:
                    data = _a.sent();
                    expect(data.length).toEqual(1 * 1 * 3);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels for HTMLImageElement', function () { return __awaiter(_this, void 0, void 0, function () {
        var img, res, data;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    img = new Image(10, 10);
                    img.src = 'data:image/gif;base64' +
                        ',R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';
                    res = tf.browser.fromPixels(img);
                    expect(res.shape).toEqual([10, 10, 3]);
                    return [4 /*yield*/, res.data()];
                case 1:
                    data = _a.sent();
                    expect(data.length).toEqual(10 * 10 * 3);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels for HTMLVideolement', function () { return __awaiter(_this, void 0, void 0, function () {
        var video, source, res, data;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    video = document.createElement('video');
                    video.autoplay = true;
                    source = document.createElement('source');
                    // tslint:disable:max-line-length
                    source.src = 'data:video/mp4;base64,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';
                    source.type = 'video/mp4';
                    video.appendChild(source);
                    document.body.appendChild(video);
                    if (!(video.readyState < 2)) return [3 /*break*/, 2];
                    return [4 /*yield*/, new Promise(function (resolve) {
                            video.addEventListener('loadeddata', function () { return resolve(); });
                        })];
                case 1:
                    _a.sent();
                    _a.label = 2;
                case 2:
                    res = tf.browser.fromPixels(video);
                    expect(res.shape).toEqual([90, 160, 3]);
                    return [4 /*yield*/, res.data()];
                case 3:
                    data = _a.sent();
                    expect(data.length).toEqual(90 * 160 * 3);
                    document.body.removeChild(video);
                    return [2 /*return*/];
            }
        });
    }); });
    it('fromPixels for HTMLVideolement throws without loadeddata', function () { return __awaiter(_this, void 0, void 0, function () {
        var video;
        return __generator(this, function (_a) {
            video = document.createElement('video');
            video.width = 1;
            video.height = 1;
            video.src = 'data:image/gif;base64' +
                ',R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';
            expect(function () { return tf.browser.fromPixels(video); }).toThrowError();
            return [2 /*return*/];
        });
    }); });
    it('throws when passed a primitive number', function () {
        var msg = /pixels passed to tf.browser.fromPixels\(\) must be either/;
        // tslint:disable-next-line:no-any
        expect(function () { return tf.browser.fromPixels(3); }).toThrowError(msg);
    });
    it('throws when passed a string', function () {
        var msg = /pixels passed to tf.browser.fromPixels\(\) must be either/;
        // tslint:disable-next-line:no-any
        expect(function () { return tf.browser.fromPixels('test'); }).toThrowError(msg);
    });
    it('canvas and image match', function () { return __awaiter(_this, void 0, void 0, function () {
        var img, size, pixels, canvas, ctx, actual, actualInt32, pixelsData;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    img = new Image();
                    size = 80;
                    // tslint:disable:max-line-length
                    img.src =
                        'data:image/jpeg;base64,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';
                    // tslint:enable:max-line-length
                    return [4 /*yield*/, new Promise(function (resolve) {
                            img.onload = function () { return resolve(img); };
                        })];
                case 1:
                    // tslint:enable:max-line-length
                    _a.sent();
                    img.width = size;
                    img.height = size;
                    return [4 /*yield*/, tf.browser.fromPixels(img, 4)];
                case 2:
                    pixels = _a.sent();
                    canvas = document.createElement('canvas');
                    canvas.width = size;
                    canvas.height = size;
                    ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, size, size);
                    actual = ctx.getImageData(0, 0, size, size).data;
                    actualInt32 = Int32Array.from(actual);
                    return [4 /*yield*/, pixels.data()];
                case 3:
                    pixelsData = _a.sent();
                    test_util_1.expectArraysClose(pixelsData, actualInt32, 10);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('toPixels no canvas', jasmine_util_1.ALL_ENVS, function () {
    it('draws a rank-2 float32 tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor2d([.15, .2], [2, 1], 'float32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([
                        Math.round(.15 * 255), Math.round(.15 * 255), Math.round(.15 * 255), 255,
                        Math.round(.2 * 255), Math.round(.2 * 255), Math.round(.2 * 255), 255
                    ]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('draws a rank-2 int32 tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor2d([10, 20], [2, 1], 'int32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([10, 10, 10, 255, 20, 20, 20, 255]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('draws a rank-3 float32 tensor, 1 channel', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor3d([.15, .2], [2, 1, 1], 'float32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([
                        Math.round(.15 * 255), Math.round(.15 * 255), Math.round(.15 * 255), 255,
                        Math.round(.2 * 255), Math.round(.2 * 255), Math.round(.2 * 255), 255
                    ]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('draws a rank-3 int32 tensor, 1 channel', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor3d([10, 20], [2, 1, 1], 'int32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([10, 10, 10, 255, 20, 20, 20, 255]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('draws a rank-3 float32 tensor, 3 channel', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor3d([.05, .1001, .15, .2, .25, .3001], [2, 1, 3], 'float32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([
                        Math.round(.05 * 255), Math.round(.1001 * 255), Math.round(.15 * 255),
                        255, Math.round(.2 * 255), Math.round(.25 * 255), Math.round(.3001 * 255),
                        255
                    ]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('draws a rank-3 int32 tensor, 3 channel', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor3d([10, 20, 30, 40, 50, 60], [2, 1, 3], 'int32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([10, 20, 30, 255, 40, 50, 60, 255]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('draws a rank-3 float32 tensor, 4 channel', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor3d([.05, .1001, .15, .2, .25, .3001, .35, .4], [2, 1, 4], 'float32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([
                        Math.round(.05 * 255), Math.round(.1001 * 255), Math.round(.15 * 255),
                        Math.round(.20 * 255), Math.round(.25 * 255), Math.round(.3001 * 255),
                        Math.round(.35 * 255), Math.round(.4 * 255)
                    ]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('draws a rank-3 int32 tensor, 4 channel', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor3d([10, 20, 30, 40, 50, 60, 70, 80], [2, 1, 4], 'int32');
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([10, 20, 30, 40, 50, 60, 70, 80]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws for scalars', function (done) {
        // tslint:disable-next-line:no-any
        test_util_1.expectPromiseToFail(function () { return tf.browser.toPixels(tf.scalar(1)); }, done);
    });
    it('throws for rank-1 tensors', function (done) {
        test_util_1.expectPromiseToFail(
        // tslint:disable-next-line:no-any
        function () { return tf.browser.toPixels(tf.tensor1d([1])); }, done);
    });
    it('throws for rank-4 tensors', function (done) {
        test_util_1.expectPromiseToFail(
        // tslint:disable-next-line:no-any
        function () { return tf.browser.toPixels(tf.tensor4d([1], [1, 1, 1, 1])); }, done);
    });
    it('throws for bool dtype', function (done) {
        test_util_1.expectPromiseToFail(function () { return tf.browser.toPixels(tf.tensor2d([1], [1, 1], 'bool')); }, done);
    });
    it('throws for rank-3 depth = 2', function (done) {
        test_util_1.expectPromiseToFail(function () { return tf.browser.toPixels(tf.tensor3d([1, 2], [1, 1, 2])); }, done);
    });
    it('throws for rank-3 depth = 5', function (done) {
        test_util_1.expectPromiseToFail(function () { return tf.browser.toPixels(tf.tensor3d([1, 2, 3, 4, 5], [1, 1, 5])); }, done);
    });
    it('throws for float32 tensor with values not in [0 - 1]', function (done) {
        test_util_1.expectPromiseToFail(function () { return tf.browser.toPixels(tf.tensor2d([-1, .5], [1, 2])); }, done);
    });
    it('throws for int32 tensor with values not in [0 - 255]', function (done) {
        test_util_1.expectPromiseToFail(function () { return tf.browser.toPixels(tf.tensor2d([-1, 100], [1, 2], 'int32')); }, done);
    });
    it('throws when passed a non-tensor', function (done) {
        // tslint:disable-next-line:no-any
        test_util_1.expectPromiseToFail(function () { return tf.browser.toPixels({}); }, done);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, data, expected;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = [[10], [20]];
                    return [4 /*yield*/, tf.browser.toPixels(x)];
                case 1:
                    data = _a.sent();
                    expected = new Uint8ClampedArray([10, 10, 10, 255, 20, 20, 20, 255]);
                    expect(data).toEqual(expected);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('clone', jasmine_util_1.ALL_ENVS, function () {
    it('1D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'float32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3], 'int32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 1, 0], 'bool');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D complex64 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.complex([1], [1]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D string dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d(['a', 'b', 'c'], 'string');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('string');
                    expect(b.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['a', 'b', 'c']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'float32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'int32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 1, 1, 0], [2, 2], 'bool');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D complex64 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.complex([[1, 3], [5, 7]], [[2, 4], [6, 8]]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D string dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d(['a', 'b', 'c', 'd'], [2, 2], 'string');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('string');
                    expect(b.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['a', 'b', 'c', 'd']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'float32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [2, 2, 1], 'int32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 1, 1, 0], [2, 2, 1], 'bool');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D complex64 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.complex([[[1], [3]], [[5], [7]]], [[[2], [4]], [[6], [8]]]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D string dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d(['a', 'b', 'c', 'd'], [2, 2, 1], 'string');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('string');
                    expect(b.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['a', 'b', 'c', 'd']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'float32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('float32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 2, 3, 4], [2, 2, 1, 1], 'int32');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('int32');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D bool dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 1, 1, 0], [2, 2, 1, 1], 'bool');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('bool');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 1, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D complex64 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.complex([[[[1]], [[3]]], [[[5]], [[7]]]], [[[[2]], [[4]]], [[[6]], [[8]]]]);
                    b = tf.clone(a);
                    expect(b.dtype).toBe('complex64');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D string dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d(['a', 'b', 'c', 'd'], [2, 2, 1, 1], 'string');
                    b = tf.clone(a);
                    expect(b.dtype).toBe('string');
                    expect(b.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['a', 'b', 'c', 'd']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient: 1D', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    dy = tf.tensor1d([4, 5, 6]);
                    da = tf.grad(function (x) { return tf.clone(x); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    dy = tf.tensor1d([4, 5, 6]);
                    da = tf.grad(function (x) { return tf.clone(x.clone()).clone(); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient: 1D string throws error with string dy', function () {
        var a = tf.tensor1d(['a', 'b', 'c'], 'string');
        var dy = tf.tensor1d(['d', 'e', 'f']);
        expect(function () { return tf.grad(function (x) { return tf.clone(x); })(a, dy); }).toThrowError();
    });
    it('gradient: 1D string throws error with bool dy', function () {
        var a = tf.tensor1d(['a', 'b', 'c'], 'string');
        var dy = tf.tensor1d([false, true, false], 'bool');
        expect(function () { return tf.grad(function (x) { return tf.clone(x); })(a, dy); }).toThrowError();
    });
    it('gradient: 1D string throws error with int32 dy', function () {
        var a = tf.tensor1d(['a', 'b', 'c'], 'string');
        var dy = tf.tensor1d([4, 5, 6], 'int32');
        expect(function () { return tf.grad(function (x) { return tf.clone(x); })(a, dy); }).toThrowError();
    });
    it('gradient: 1D string works with float32 dy', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d(['a', 'b', 'c'], 'string');
                    dy = tf.tensor1d([4, 5, 6]);
                    da = tf.grad(function (x) { return tf.clone(x); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient: 2D int32', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([1, 2, 3, 4], [2, 2], 'int32');
                    dy = tf.tensor2d([5, 6, 7, 8], [2, 2], 'float32');
                    da = tf.grad(function (x) { return tf.clone(x); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient: 4D bool', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor4d([1, 1, 1, 0], [2, 2, 1, 1], 'bool');
                    dy = tf.tensor4d([5, 6, 7, 8], [2, 2, 1, 1], 'float32');
                    da = tf.grad(function (x) { return tf.clone(x); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual([2, 2, 1, 1]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.clone({}); })
            .toThrowError(/Argument 'x' passed to 'clone' must be a Tensor/);
    });
});
jasmine_util_1.describeWithFlags('tile', jasmine_util_1.ALL_ENVS, function () {
    it('1D (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 3]);
                    t2 = tf.tile(t, [2]);
                    expect(t2.shape).toEqual([6]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    t2 = tf.tile(t, [1, 2]);
                    expect(t2.shape).toEqual([2, 4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1, 11, 1, 11, 2, 22, 2, 22]]);
                    t2 = tf.tile(t, [2, 1]);
                    expect(t2.shape).toEqual([4, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [1, 11, 2, 22, 1, 11, 2, 22]]);
                    t2 = tf.tile(t, [2, 2]);
                    expect(t2.shape).toEqual([4, 4]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(),
                        [1, 11, 1, 11, 2, 22, 2, 22, 1, 11, 1, 11, 2, 22, 2, 22]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
                    t2 = tf.tile(t, [1, 2, 1]);
                    expect(t2.shape).toEqual([2, 4, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 1, 2, 3, 4, 5, 6, 7, 8, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [1, 2, 2, 2]);
                    t2 = tf.tile(t, [1, 2, 1, 1]);
                    expect(t2.shape).toEqual([1, 4, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor5d([1, 2, 3, 4, 5, 6, 7, 8], [1, 1, 2, 2, 2]);
                    t2 = tf.tile(t, [1, 2, 1, 1, 1]);
                    expect(t2.shape).toEqual([1, 2, 2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1d string tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor(['a', 'b', 'c']);
                    res = tf.tile(a, [2]);
                    expect(res.shape).toEqual([6]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['a', 'b', 'c', 'a', 'b', 'c']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d string tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor([['a', 'b'], ['c', 'd']]);
                    res = tf.tile(a, [2, 3]);
                    expect(res.shape).toEqual([4, 6]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [
                            'a', 'b', 'a', 'b', 'a', 'b', 'c', 'd', 'c', 'd', 'c', 'd',
                            'a', 'b', 'a', 'b', 'a', 'b', 'c', 'd', 'c', 'd', 'c', 'd'
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('propagates NaNs', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, NaN]);
                    t2 = tf.tile(t, [2]);
                    expect(t2.shape).toEqual([6]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, NaN, 1, 2, NaN]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D bool (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([true, false, true], 'bool');
                    t2 = tf.tile(t, [2]);
                    expect(t2.shape).toEqual([6]);
                    expect(t2.dtype).toBe('bool');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 1, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D bool (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    t = tf.tensor2d([true, false, true, true], [2, 2], 'bool');
                    t2 = tf.tile(t, [1, 2]);
                    expect(t2.shape).toEqual([2, 4]);
                    expect(t2.dtype).toBe('bool');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1, 0, 1, 0, 1, 1, 1, 1]]);
                    t2 = tf.tile(t, [2, 1]);
                    expect(t2.shape).toEqual([4, 2]);
                    expect(t2.dtype).toBe('bool');
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [1, 0, 1, 1, 1, 0, 1, 1]]);
                    t2 = tf.tile(t, [2, 2]);
                    expect(t2.shape).toEqual([4, 4]);
                    expect(t2.dtype).toBe('bool');
                    _c = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D bool (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([true, false, true, false, true, false, true, false], [2, 2, 2], 'bool');
                    t2 = tf.tile(t, [1, 2, 1]);
                    expect(t2.shape).toEqual([2, 4, 2]);
                    expect(t2.dtype).toBe('bool');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D int32 (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 5], 'int32');
                    t2 = tf.tile(t, [2]);
                    expect(t2.shape).toEqual([6]);
                    expect(t2.dtype).toBe('int32');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 5, 1, 2, 5]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D int32 (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    t = tf.tensor2d([1, 2, 3, 4], [2, 2], 'int32');
                    t2 = tf.tile(t, [1, 2]);
                    expect(t2.shape).toEqual([2, 4]);
                    expect(t2.dtype).toBe('int32');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1, 2, 1, 2, 3, 4, 3, 4]]);
                    t2 = tf.tile(t, [2, 1]);
                    expect(t2.shape).toEqual([4, 2]);
                    expect(t2.dtype).toBe('int32');
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [1, 2, 3, 4, 1, 2, 3, 4]]);
                    t2 = tf.tile(t, [2, 2]);
                    expect(t2.shape).toEqual([4, 4]);
                    expect(t2.dtype).toBe('int32');
                    _c = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [1, 2, 1, 2, 3, 4, 3, 4, 1, 2, 1, 2, 3, 4, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D int32 (tile)', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2], 'int32');
                    t2 = tf.tile(t, [1, 2, 1]);
                    expect(t2.shape).toEqual([2, 4, 2]);
                    expect(t2.dtype).toBe('int32');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 1, 2, 3, 4, 5, 6, 7, 8, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D (tile) gradient', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, dy, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor1d([1, 2, 3]);
                    dy = tf.tensor1d([0.1, 0.2, 0.3, 1, 2, 3, 10, 20, 30]);
                    gradients = tf.grad(function (x) { return tf.tile(x, [3]); })(x, dy);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [11.1, 22.2, 33.3]]);
                    expect(gradients.shape).toEqual([3]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, dy, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor1d([1, 2, 3]);
                    dy = tf.tensor1d([0.1, 0.2, 0.3, 1, 2, 3, 10, 20, 30]);
                    gradients = tf.grad(function (x) { return tf.tile(x.clone(), [3]).clone(); })(x, dy);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [11.1, 22.2, 33.3]]);
                    expect(gradients.shape).toEqual([3]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D (tile) gradient', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, dy, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor2d([[1, 2], [3, 4]], [2, 2]);
                    dy = tf.tensor2d([[1, 2, 10, 20], [3, 4, 30, 40]], [2, 4]);
                    gradients = tf.grad(function (x) { return tf.tile(x, [1, 2]); })(x, dy);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [11, 22, 33, 44]]);
                    expect(gradients.shape).toEqual([2, 2]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D (tile) gradient', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, dy, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor3d([[[1], [2]], [[3], [4]]], [2, 2, 1]);
                    dy = tf.tensor3d([[[1, 10], [2, 20]], [[3, 30], [4, 40]]], [2, 2, 2]);
                    gradients = tf.grad(function (x) { return tf.tile(x, [1, 1, 2]); })(x, dy);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [11, 22, 33, 44]]);
                    expect(gradients.shape).toEqual([2, 2, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D (tile) gradient', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, dy, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor4d([[[[1]], [[2]]], [[[3]], [[4]]]], [2, 2, 1, 1]);
                    dy = tf.tensor4d([
                        [[[.01, .1], [1, 10]], [[.02, .2], [2, 20]]],
                        [[[.03, .3], [3, 30]], [[.04, .4], [4, 40]]]
                    ], [2, 2, 2, 2]);
                    gradients = tf.grad(function (x) { return tf.tile(x, [1, 1, 2, 2]); })(x, dy);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [11.11, 22.22, 33.33, 44.44]]);
                    expect(gradients.shape).toEqual([2, 2, 1, 1]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.tile({}, [1]); })
            .toThrowError(/Argument 'x' passed to 'tile' must be a Tensor/);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tile([1, 2, 3], [2]);
                    expect(res.shape).toEqual([6]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('gather', jasmine_util_1.ALL_ENVS, function () {
    it('1D (gather), scalar indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 3]);
                    t2 = tf.gather(t, tf.scalar(1, 'int32'), 0);
                    expect(t2.shape).toEqual([]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D (gather), 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 3]);
                    t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
                    expect(t2.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 1, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D (gather), 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 3]);
                    t2 = tf.gather(t, tf.tensor2d([0, 2, 0, 1], [1, 4], 'int32'), 0);
                    expect(t2.shape).toEqual([1, 4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 1, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D (gather), scalar indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    t2 = tf.gather(t, tf.scalar(1, 'int32'), 0);
                    expect(t2.shape).toEqual([2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [2, 22]]);
                    t2 = tf.gather(t, tf.scalar(1, 'int32'), 1);
                    expect(t2.shape).toEqual([2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [11, 22]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D (gather), 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    t2 = tf.gather(t, tf.tensor1d([1, 0, 0, 1], 'int32'), 0);
                    expect(t2.shape).toEqual([4, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [2, 22, 1, 11, 1, 11, 2, 22]]);
                    t2 = tf.gather(t, tf.tensor1d([1, 0, 0, 1], 'int32'), 1);
                    expect(t2.shape).toEqual([2, 4]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [11, 1, 1, 11, 22, 2, 2, 22]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D (gather), 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 0);
                    expect(t2.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [2, 22, 1, 11, 1, 11, 2, 22]]);
                    t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 1);
                    expect(t2.shape).toEqual([2, 2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [11, 1, 1, 11, 22, 2, 2, 22]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D (gather), 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
                    t2 = tf.gather(t, tf.tensor1d([1, 0, 0, 1], 'int32'), 2);
                    expect(t2.shape).toEqual([2, 2, 4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 1, 1, 2, 4, 3, 3, 4, 6, 5, 5, 6, 8, 7, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D (gather), 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
                    t2 = tf.gather(t, tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32'), 2);
                    expect(t2.shape).toEqual([2, 2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 1, 1, 2, 4, 3, 3, 4, 6, 5, 5, 6, 8, 7, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('bool (gather), 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([true, false, true], 'bool');
                    t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
                    expect(t2.shape).toEqual([4]);
                    expect(t2.dtype).toBe('bool');
                    _a = expect;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toEqual(new Uint8Array([1, 1, 1, 0]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('bool (gather), 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([true, false, true], 'bool');
                    t2 = tf.gather(t, tf.tensor2d([0, 2, 0, 1], [2, 2], 'int32'), 0);
                    expect(t2.shape).toEqual([2, 2]);
                    expect(t2.dtype).toBe('bool');
                    _a = expect;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toEqual(new Uint8Array([1, 1, 1, 0]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('int32 (gather), 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 5], 'int32');
                    t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
                    expect(t2.shape).toEqual([4]);
                    expect(t2.dtype).toBe('int32');
                    _a = expect;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toEqual(new Int32Array([1, 5, 1, 2]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('int32 (gather), 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 5], 'int32');
                    t2 = tf.gather(t, tf.tensor2d([0, 2, 0, 1], [2, 2], 'int32'), 0);
                    expect(t2.shape).toEqual([2, 2]);
                    expect(t2.dtype).toBe('int32');
                    _a = expect;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toEqual(new Int32Array([1, 5, 1, 2]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('propagates NaNs', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, t2, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, NaN]);
                    t2 = tf.gather(t, tf.tensor1d([0, 2, 0, 1], 'int32'), 0);
                    expect(t2.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, t2.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, NaN, 1, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('chaining, axis=1', function () {
        var x = tf.zeros([2, 4, 6]);
        // [0, 2, 4]
        var indices = tf.range(0, 6, 2, 'int32');
        var axis = 2;
        expect(x.gather(indices, axis).shape).toEqual([2, 4, 3]);
    });
    it('indices not int32 throws error', function () {
        var x = tf.zeros([2, 4, 6]);
        // [0, 2, 4]
        var indices = tf.range(0, 6, 2);
        var axis = 2;
        expect(function () { return x.gather(indices, axis); }).toThrowError();
    });
    it('throws when passed x as a non-tensor', function () {
        expect(function () { return tf.gather({}, tf.tensor1d([1])); })
            .toThrowError(/Argument 'x' passed to 'gather' must be a Tensor/);
    });
    it('throws when passed indices as a non-tensor', function () {
        // tslint:disable-next-line:no-any
        expect(function () { return tf.gather(tf.tensor1d([1]), {}); })
            .toThrowError(/Argument 'indices' passed to 'gather' must be a Tensor/);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.gather([1, 2, 3], [0, 2, 0, 1], 0);
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 1, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 1D (gather), 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 3]);
                    indices = tf.tensor1d([0, 2, 0, 1], 'int32');
                    dy = tf.tensor([3, 4, 5, 6]);
                    gradients = tf.grad(function (t) { return tf.gather(t, indices); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [8, 6, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones', function () {
        var t = tf.tensor1d([1, 2, 3]);
        var indices = tf.tensor1d([0, 2, 0, 1], 'int32');
        var gradF = tf.grad(function (t) { return tf.gather(t.clone(), indices.clone()).clone(); });
        var dt = gradF(t);
        expect(dt.shape).toEqual(t.shape);
    });
    it('gradient 1D (gather), 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor1d([1, 2, 3]);
                    indices = tf.tensor2d([0, 2, 0, 1], [2, 2], 'int32');
                    dy = tf.tensor2d([3, 4, 5, 6], [2, 2]);
                    gradients = tf.grad(function (t) { return tf.gather(t, indices); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [8, 6, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=0 shape=[2, 2] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    indices = tf.tensor1d([1, 0, 0, 1], 'int32');
                    dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [4, 2]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [12, 14, 12, 14]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=0 shape=[2, 2] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
                    dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [2, 2, 2]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [12, 14, 12, 14]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=0 shape=[4, 1] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
                    indices = tf.tensor1d([1, 0, 0, 1], 'int32');
                    dy = tf.tensor([23, 7, 19, 13], [4, 1]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [26, 36, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=0 shape=[4, 1] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
                    indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
                    dy = tf.tensor([23, 7, 19, 13], [2, 2, 1]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [26, 36, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=1 shape=[2, 2] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    indices = tf.tensor1d([1, 0, 0, 1], 'int32');
                    dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [2, 4]);
                    axis = 1;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [9, 9, 17, 17]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=1 shape=[2, 2] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [2, 2]);
                    indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
                    dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10], [2, 2, 2]);
                    axis = 1;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [9, 9, 17, 17]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=1 shape=[4, 1] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
                    indices = tf.tensor1d([0, 0, 0, 0], 'int32');
                    dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], [4, 4]);
                    axis = 1;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [18, 34, 50, 66]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 2D (gather) axis=1 shape=[4, 1] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 11, 2, 22], [4, 1]);
                    indices = tf.tensor2d([0, 0, 0, 0], [2, 2], 'int32');
                    dy = tf.tensor([3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], [4, 2, 2]);
                    axis = 1;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [18, 34, 50, 66]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=0 shape=[2, 3, 2] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
                    indices = tf.tensor1d([1, 0, 0, 1], 'int32');
                    dy = tf.tensor([
                        2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
                        4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
                    ], [4, 3, 2]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [5, 33, 12.01, -7, 30, 32, 4, 18, 10, 38, 30, 25.7]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=0 shape=[2, 3, 2] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
                    indices = tf.tensor2d([1, 0, 0, 1], [2, 2], 'int32');
                    dy = tf.tensor([
                        2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
                        4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
                    ], [2, 2, 3, 2]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [5, 33, 12.01, -7, 30, 32, 4, 18, 10, 38, 30, 25.7]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=0 shape=[1, 4, 4]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
                    indices = tf.tensor1d([0, 0], 'int32');
                    dy = tf.tensor([
                        2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7,
                        18, 19, 2, 21, 6, 23, 24, 25, 101, 31, 34, 54, 1, 0, -3, -4
                    ], [2, 4, 4]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [20, 16, 6, 36, 12, 23.7, 25, 43, 101.01, 31, 46, 67, 5, 15, 9, -11]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=0 shape=[1, 4, 4] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
                    indices = tf.tensor1d([0, 0], 'int32');
                    dy = tf.tensor([
                        2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7,
                        18, 19, 2, 21, 6, 23, 24, 25, 101, 31, 34, 54, 1, 0, -3, -4
                    ], [2, 4, 4]);
                    axis = 0;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [20, 16, 6, 36, 12, 23.7, 25, 43, 101.01, 31, 46, 67, 5, 15, 9, -11]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=1 shape=[2, 3, 2] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
                    indices = tf.tensor2d([1, 2, 2, 1], [2, 2], 'int32');
                    dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7], [2, 2, 2, 2]);
                    axis = 1;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [0, 0, 3, 15, 10, 15.7, 0, 0, 12.01, -7, 16, 28]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=1 shape=[1, 4, 4] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
                    indices = tf.tensor1d([1, 2, 2, 1], 'int32');
                    dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7], [1, 4, 4]);
                    axis = 1;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [0, 0, 0, 0, 6, 12, 16, 8, 6.01, .7, 13, 31, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=1 shape=[1, 4, 4] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4]);
                    indices = tf.tensor2d([1, 2, 2, 1], [2, 2], 'int32');
                    dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13, 4, 15, 12, -7], [1, 2, 2, 4]);
                    axis = 1;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [0, 0, 0, 0, 6, 12, 16, 8, 6.01, .7, 13, 31, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=2 shape=[2, 3, 2] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
                    indices = tf.tensor1d([1, 0, 1, 0], 'int32');
                    dy = tf.tensor([
                        2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
                        4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
                    ], [2, 3, 4]);
                    axis = 2;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [12, 6, 18.7, 7, 13, 12.01, 8, 16, 40, 20, 48, 30]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=2 shape=[2, 3, 2] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
                    indices = tf.tensor2d([1, 0, 1, 0], [2, 2], 'int32');
                    dy = tf.tensor([
                        2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 12, 13,
                        4, 15, 12, -7, 18, 19, 2, 21, 6, 23, 24, 25
                    ], [2, 3, 2, 2]);
                    axis = 2;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [12, 6, 18.7, 7, 13, 12.01, 8, 16, 40, 20, 48, 30]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=2 shape=[4, 1, 4] 1D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [4, 1, 4]);
                    indices = tf.tensor1d([1, 3, 1], 'int32');
                    dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 4, 15], [4, 1, 3]);
                    axis = 2;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [0, 6, 0, -3, 0, 15.7, 0, 6, 0, 1.01, 0, 18, 0, 15, 0, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient 3D (gather) axis=2 shape=[4, 1, 4] 2D indices', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, indices, dy, axis, gradients, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [4, 1, 4]);
                    indices = tf.tensor2d([1, 3, 1], [1, 3], 'int32');
                    dy = tf.tensor([2, -3, 4, 15, 6, 0.7, 1, 18, 0.01, 0, 4, 15], [4, 1, 1, 3]);
                    axis = 2;
                    gradients = tf.grad(function (t) { return tf.gather(t, indices, axis); })(t, dy);
                    expect(gradients.shape).toEqual(t.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradients.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [0, 6, 0, -3, 0, 15.7, 0, 6, 0, 1.01, 0, 18, 0, 15, 0, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('oneHot', jasmine_util_1.ALL_ENVS, function () {
    it('Depth 1 throws error', function () {
        var indices = tf.tensor1d([0, 0, 0], 'int32');
        expect(function () { return tf.oneHot(indices, 1); }).toThrowError();
    });
    it('Depth 2, diagonal', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.tensor1d([0, 1], 'int32');
                    res = tf.oneHot(indices, 2);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Scalar input as Tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.scalar(2, 'int32');
                    res = tf.oneHot(indices, 4);
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Scalar input as number', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = 2;
                    res = tf.oneHot(indices, 4);
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('oneHot with chaining compiles', function () {
        var indices = 2;
        // Asserts that there is no compiler error.
        tf.oneHot(indices, 4).toFloat();
    });
    it('Depth 2, transposed diagonal', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.tensor1d([1, 0], 'int32');
                    res = tf.oneHot(indices, 2);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 1, 1, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Depth 3, 4 events', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.tensor1d([2, 1, 2, 0], 'int32');
                    res = tf.oneHot(indices, 3);
                    expect(res.shape).toEqual([4, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Out of range events do not trigger onValue', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.tensor1d([-1, 5, 12345], 'int32');
                    res = tf.oneHot(indices, 5);
                    expect(res.shape).toEqual([3, 5]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Depth 2 onValue=3, offValue=-2', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.tensor1d([0, 1], 'int32');
                    res = tf.oneHot(indices, 2, 3, -2);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [3, -2, -2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('indices not int32 throws error', function () {
        var indices = tf.tensor1d([0, 1], 'float32');
        expect(function () { return tf.oneHot(indices, 2); }).toThrowError();
    });
    it('check output dtype', function () {
        var expectedType = 'int32';
        var indices = tf.tensor1d([0, 1], 'int32');
        var res = tf.oneHot(indices, 2);
        expect(res.dtype).toEqual(expectedType);
    });
    it('oneHot accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.oneHot([0, 1], 2);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('has gradient', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([0, 1, 2], 'int32');
                    dy = tf.ones([3, 3], 'float32');
                    da = tf.grad(function (x) { return tf.oneHot(x, 3); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([0, 1, 2], 'int32');
                    dy = tf.ones([3, 3], 'float32');
                    da = tf.grad(function (x) { return tf.oneHot(x.clone(), 3).clone(); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient when indices is 3d', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, depth, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor3d([1, 2, 3, 4], [1, 2, 2], 'int32');
                    dy = tf.ones([1, 2, 2, 3], 'float32');
                    depth = 3;
                    da = tf.grad(function (x) { return tf.oneHot(x, depth); })(a, dy);
                    expect(da.dtype).toBe('float32');
                    expect(da.shape).toEqual(a.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 0, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('oneHot with indices as 2d', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, depth, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.tensor2d([[1, 3], [2, 3]], [2, 2], 'int32');
                    depth = 4;
                    res = tf.oneHot(indices, depth);
                    expect(res.shape).toEqual([2, 2, depth]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Supports chaining', function () { return __awaiter(_this, void 0, void 0, function () {
        var indices, depth, onValue, offValue, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    indices = tf.tensor2d([[1, 2, 3], [2, 3, 1], [4, 5, 6]], [3, 3], 'int32');
                    depth = 6;
                    onValue = 3;
                    offValue = 7;
                    res = indices.oneHot(depth, onValue, offValue);
                    expect(res.shape).toEqual([3, 3, 6]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [
                            7, 3, 7, 7, 7, 7, 7, 7, 3, 7, 7, 7, 7, 7, 7, 3, 7, 7,
                            7, 7, 3, 7, 7, 7, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 7, 7,
                            7, 7, 7, 7, 3, 7, 7, 7, 7, 7, 7, 3, 7, 7, 7, 7, 7, 7
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('linspace', jasmine_util_1.ALL_ENVS, function () {
    it('start stop', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b, c, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    a = tf.linspace(1, 10, 10);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1., 2., 3., 4., 5., 6., 7., 8., 9., 10.]]);
                    expect(a.shape).toEqual([10]);
                    b = tf.linspace(12, 17, 8);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [
                            12., 12.71428571, 13.42857143, 14.14285714, 14.85714286, 15.57142857,
                            16.28571429, 17.
                        ]]);
                    expect(b.shape).toEqual([8]);
                    c = tf.linspace(9, 0, 6);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, c.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [9., 7.2, 5.4, 3.6, 1.8, 0.]]);
                    expect(c.shape).toEqual([6]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('negative start stop', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.linspace(-4, 5, 6);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [-4., -2.2, -0.4, 1.4, 3.2, 5.]]);
                    expect(a.shape).toEqual([6]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('start negative stop', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.linspace(4, -5, 6);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [4., 2.2, 0.4, -1.4, -3.2, -5.]]);
                    expect(a.shape).toEqual([6]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('negative start negative stop', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    a = tf.linspace(-4, -5, 6);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [-4., -4.2, -4.4, -4.6, -4.8, -5.]]);
                    expect(a.shape).toEqual([6]);
                    b = tf.linspace(-9, -4, 5);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [-9., -7.75, -6.5, -5.25, -4.]]);
                    expect(b.shape).toEqual([5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should throw with no samples', function () {
        expect(function () { return tf.linspace(2, 10, 0); }).toThrow();
    });
});
jasmine_util_1.describeWithFlags('range', jasmine_util_1.ALL_ENVS, function () {
    it('start stop', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    a = tf.range(0, 3);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [0, 1, 2]]);
                    expect(a.shape).toEqual([3]);
                    b = tf.range(3, 8);
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [3, 4, 5, 6, 7]]);
                    expect(b.shape).toEqual([5]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('start stop negative', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    a = tf.range(-2, 3);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [-2, -1, 0, 1, 2]]);
                    expect(a.shape).toEqual([5]);
                    b = tf.range(4, -2);
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [4, 3, 2, 1, 0, -1]]);
                    expect(b.shape).toEqual([6]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('start stop step', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b, c, _c, d, _d, e, _e, f, _f, g, _g, h, _h, i, _j;
        return __generator(this, function (_k) {
            switch (_k.label) {
                case 0:
                    a = tf.range(4, 15, 4);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_k.sent(), [4, 8, 12]]);
                    expect(a.shape).toEqual([3]);
                    b = tf.range(4, 11, 4);
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_k.sent(), [4, 8]]);
                    expect(b.shape).toEqual([2]);
                    c = tf.range(4, 17, 4);
                    _c = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, c.data()];
                case 3:
                    _c.apply(void 0, [_k.sent(), [4, 8, 12, 16]]);
                    expect(c.shape).toEqual([4]);
                    d = tf.range(0, 30, 5);
                    _d = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, d.data()];
                case 4:
                    _d.apply(void 0, [_k.sent(), [0, 5, 10, 15, 20, 25]]);
                    expect(d.shape).toEqual([6]);
                    e = tf.range(-3, 9, 2);
                    _e = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, e.data()];
                case 5:
                    _e.apply(void 0, [_k.sent(), [-3, -1, 1, 3, 5, 7]]);
                    expect(e.shape).toEqual([6]);
                    f = tf.range(3, 3);
                    _f = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, f.data()];
                case 6:
                    _f.apply(void 0, [_k.sent(), new Float32Array(0)]);
                    expect(f.shape).toEqual([0]);
                    g = tf.range(3, 3, 1);
                    _g = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, g.data()];
                case 7:
                    _g.apply(void 0, [_k.sent(), new Float32Array(0)]);
                    expect(g.shape).toEqual([0]);
                    h = tf.range(3, 3, 4);
                    _h = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, h.data()];
                case 8:
                    _h.apply(void 0, [_k.sent(), new Float32Array(0)]);
                    expect(h.shape).toEqual([0]);
                    i = tf.range(-18, -2, 5);
                    _j = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, i.data()];
                case 9:
                    _j.apply(void 0, [_k.sent(), [-18, -13, -8, -3]]);
                    expect(i.shape).toEqual([4]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('start stop large step', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b, c, _c, d, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    a = tf.range(3, 10, 150);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), [3]]);
                    expect(a.shape).toEqual([1]);
                    b = tf.range(10, 500, 205);
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), [10, 215, 420]]);
                    expect(b.shape).toEqual([3]);
                    c = tf.range(3, -10, -150);
                    _c = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, c.data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), [3]]);
                    expect(c.shape).toEqual([1]);
                    d = tf.range(-10, -500, -205);
                    _d = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, d.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), [-10, -215, -420]]);
                    expect(d.shape).toEqual([3]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('start stop negative step', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b, c, _c, d, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    a = tf.range(0, -10, -1);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), [0, -1, -2, -3, -4, -5, -6, -7, -8, -9]]);
                    expect(a.shape).toEqual([10]);
                    b = tf.range(0, -10);
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), [0, -1, -2, -3, -4, -5, -6, -7, -8, -9]]);
                    expect(b.shape).toEqual([10]);
                    c = tf.range(3, -4, -2);
                    _c = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, c.data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), [3, 1, -1, -3]]);
                    expect(c.shape).toEqual([4]);
                    d = tf.range(-3, -18, -5);
                    _d = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, d.data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), [-3, -8, -13]]);
                    expect(d.shape).toEqual([3]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('start stop incompatible step', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a, b, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    a = tf.range(3, 10, -2);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), new Float32Array(0)]);
                    expect(a.shape).toEqual([0]);
                    b = tf.range(40, 3, 2);
                    _b = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, b.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), new Float32Array(0)]);
                    expect(b.shape).toEqual([0]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('zero step', function () {
        expect(function () { return tf.range(2, 10, 0); }).toThrow();
    });
    it('should have default dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.range(1, 4);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    expect(a.dtype).toEqual('float32');
                    expect(a.shape).toEqual([3]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should have float32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.range(1, 4, undefined, 'float32');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    expect(a.dtype).toEqual('float32');
                    expect(a.shape).toEqual([3]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should have int32 dtype', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.range(1, 4, undefined, 'int32');
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    expect(a.dtype).toEqual('int32');
                    expect(a.shape).toEqual([3]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('fill', jasmine_util_1.ALL_ENVS, function () {
    it('1D fill', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.fill([3], 2);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 2, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D fill string', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.fill([3], 'aa');
                    expect(a.dtype).toBe('string');
                    expect(a.shape).toEqual([3]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['aa', 'aa', 'aa']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D fill', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.fill([3, 2], 2);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 2, 2, 2, 2, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D fill string', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.fill([3, 2], 'a');
                    expect(a.dtype).toBe('string');
                    expect(a.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysEqual;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['a', 'a', 'a', 'a', 'a', 'a']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D fill', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.fill([3, 2, 1], 2);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 2, 2, 2, 2, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4D fill', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.fill([3, 2, 1, 2], 2);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([3, 2, 1, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('5D fill', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.fill([2, 1, 2, 1, 2], 2);
                    expect(a.dtype).toBe('float32');
                    expect(a.shape).toEqual([2, 1, 2, 1, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, a.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 2, 2, 2, 2, 2, 2, 2]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('stack', jasmine_util_1.ALL_ENVS, function () {
    it('scalars 3, 5 and 7', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, c, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.scalar(3);
                    b = tf.scalar(5);
                    c = tf.scalar(7);
                    res = tf.stack([a, b, c]);
                    expect(res.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [3, 5, 7]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('scalars 3, 5 and 7 along axis=1 throws error', function () {
        var a = tf.scalar(3);
        var b = tf.scalar(5);
        var c = tf.scalar(7);
        var f = function () { return tf.stack([a, b, c], 1); };
        expect(f).toThrowError();
    });
    it('non matching shapes throws error', function () {
        var a = tf.scalar(3);
        var b = tf.tensor1d([5]);
        var f = function () { return tf.stack([a, b]); };
        expect(f).toThrowError();
    });
    it('non matching dtypes throws error', function () {
        var a = tf.scalar(3);
        var b = tf.scalar(5, 'bool');
        var f = function () { return tf.stack([a, b]); };
        expect(f).toThrowError();
    });
    it('2d but axis=3 throws error', function () {
        var a = tf.zeros([2, 2]);
        var b = tf.zeros([2, 2]);
        var f = function () { return tf.stack([a, b], 3 /* axis */); };
        expect(f).toThrowError();
    });
    it('[1,2], [3,4] and [5,6], axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, c, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2]);
                    b = tf.tensor1d([3, 4]);
                    c = tf.tensor1d([5, 6]);
                    res = tf.stack([a, b, c], 0 /* axis */);
                    expect(res.shape).toEqual([3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('[1,2], [3,4] and [5,6], axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, c, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2]);
                    b = tf.tensor1d([3, 4]);
                    c = tf.tensor1d([5, 6]);
                    res = tf.stack([a, b, c], 1 /* axis */);
                    expect(res.shape).toEqual([2, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 5, 2, 4, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('[[1,2],[3,4]] and [[5, 6], [7, 8]], axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([[1, 2], [3, 4]]);
                    b = tf.tensor2d([[5, 6], [7, 8]]);
                    res = tf.stack([a, b], 0 /* axis */);
                    expect(res.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('[[1,2],[3,4]] and [[5, 6], [7, 8]], axis=2', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, b, c, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([[1, 2], [3, 4]]);
                    b = tf.tensor2d([[5, 6], [7, 8]]);
                    c = tf.tensor2d([[9, 10], [11, 12]]);
                    res = tf.stack([a, b, c], 2 /* axis */);
                    expect(res.shape).toEqual([2, 2, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 5, 9, 2, 6, 10, 3, 7, 11, 4, 8, 12]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('single tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor2d([[1, 2], [3, 4]]);
                    res = tf.stack([a], 2 /* axis */);
                    expect(res.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.stack([{}]); })
            .toThrowError(/Argument 'tensors\[0\]' passed to 'stack' must be a Tensor/);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = [[1, 2], [3, 4]];
                    res = tf.stack([a], 2 /* axis */);
                    expect(res.shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('chain api', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor([1, 2]);
                    res = a.stack(tf.tensor([3, 4]));
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('unstack', jasmine_util_1.ALL_ENVS, function () {
    it('unstack by default', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
                    res = tf.unstack(x);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(1);
                    expect(res[0].shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 3, 4]]);
                    expect(res[1].rank).toEqual(1);
                    expect(res[1].shape).toEqual([4]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('chain api', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
                    res = x.unstack();
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(1);
                    expect(res[0].shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 3, 4]]);
                    expect(res[1].rank).toEqual(1);
                    expect(res[1].shape).toEqual([4]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack with negative integer axis', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b, _c, _d, _e, _f;
        return __generator(this, function (_g) {
            switch (_g.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
                    res = tf.unstack(x, -1);
                    expect(res.length).toEqual(4);
                    expect(res[0].rank).toEqual(1);
                    expect(res[0].shape).toEqual([2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_g.sent(), [1, 5]]);
                    expect(res[1].rank).toEqual(1);
                    expect(res[1].shape).toEqual([2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_g.sent(), [2, 6]]);
                    expect(res[2].rank).toEqual(1);
                    expect(res[2].shape).toEqual([2]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[2].data()];
                case 3:
                    _c.apply(void 0, [_g.sent(), [3, 7]]);
                    expect(res[3].rank).toEqual(1);
                    expect(res[3].shape).toEqual([2]);
                    _d = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[3].data()];
                case 4:
                    _d.apply(void 0, [_g.sent(), [4, 8]]);
                    res = tf.unstack(x, -2);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(1);
                    expect(res[0].shape).toEqual([4]);
                    _e = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 5:
                    _e.apply(void 0, [_g.sent(), [1, 2, 3, 4]]);
                    expect(res[1].rank).toEqual(1);
                    expect(res[1].shape).toEqual([4]);
                    _f = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 6:
                    _f.apply(void 0, [_g.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack into 3 tensors', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6], [3, 2]);
                    res = tf.unstack(x, 0);
                    expect(res.length).toEqual(3);
                    expect(res[0].rank).toEqual(1);
                    expect(res[0].shape).toEqual([2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1, 2]]);
                    expect(res[1].rank).toEqual(1);
                    expect(res[1].shape).toEqual([2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [3, 4]]);
                    expect(res[2].rank).toEqual(1);
                    expect(res[2].shape).toEqual([2]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[2].data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack by axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
                    res = tf.unstack(x, 1);
                    expect(res.length).toEqual(4);
                    expect(res[0].rank).toEqual(1);
                    expect(res[0].shape).toEqual([2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), [1, 5]]);
                    expect(res[1].rank).toEqual(1);
                    expect(res[1].shape).toEqual([2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), [2, 6]]);
                    expect(res[2].rank).toEqual(1);
                    expect(res[2].shape).toEqual([2]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[2].data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), [3, 7]]);
                    expect(res[3].rank).toEqual(1);
                    expect(res[3].shape).toEqual([2]);
                    _d = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[3].data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), [4, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack rank 3 tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
                    res = tf.unstack(x);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(2);
                    expect(res[0].shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 3, 4]]);
                    expect(res[1].rank).toEqual(2);
                    expect(res[1].shape).toEqual([2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack rank 3 tensor with axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
                    res = tf.unstack(x, 1);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(2);
                    expect(res[0].shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 5, 6]]);
                    expect(res[1].rank).toEqual(2);
                    expect(res[1].shape).toEqual([2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [3, 4, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack rank 3 tensor with axis=2', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
                    res = tf.unstack(x, 2);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(2);
                    expect(res[0].shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 3, 5, 7]]);
                    expect(res[1].rank).toEqual(2);
                    expect(res[1].shape).toEqual([2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [2, 4, 6, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack rank 4 tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1]);
                    res = tf.unstack(x);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(3);
                    expect(res[0].shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 3, 4]]);
                    expect(res[1].rank).toEqual(3);
                    expect(res[1].shape).toEqual([2, 2, 1]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack rank 4 tensor with axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1]);
                    res = tf.unstack(x, 1);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(3);
                    expect(res[0].shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 5, 6]]);
                    expect(res[1].rank).toEqual(3);
                    expect(res[1].shape).toEqual([2, 2, 1]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [3, 4, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack rank 4 tensor with axis=2', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1]);
                    res = tf.unstack(x, 2);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(3);
                    expect(res[0].shape).toEqual([2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 3, 5, 7]]);
                    expect(res[1].rank).toEqual(3);
                    expect(res[1].shape).toEqual([2, 2, 1]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [2, 4, 6, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('unstack rank 4 tensor with axis=3', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1]);
                    res = tf.unstack(x, 3);
                    expect(res.length).toEqual(1);
                    expect(res[0].rank).toEqual(3);
                    expect(res[0].shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.unstack({}); })
            .toThrowError(/Argument 'x' passed to 'unstack' must be a Tensor/);
    });
    it('throws when passed an invalid axis', function () {
        expect(function () {
            var x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
            tf.unstack(x, 3);
        }).toThrowError('Axis = 3 is not in [-2, 2)');
        expect(function () {
            var x = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
            tf.unstack(x, 3);
        }).toThrowError('Axis = 3 is not in [-3, 3)');
        expect(function () {
            var x = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2, 1]);
            tf.unstack(x, 5);
        }).toThrowError('Axis = 5 is not in [-4, 4)');
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = [[1, 2, 3, 4], [5, 6, 7, 8]];
                    res = tf.unstack(x);
                    expect(res.length).toEqual(2);
                    expect(res[0].rank).toEqual(1);
                    expect(res[0].shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 3, 4]]);
                    expect(res[1].rank).toEqual(1);
                    expect(res[1].shape).toEqual([4]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [5, 6, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('grad of unstack axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, dx1, _a, dx2, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor([[1, 2, 3], [4, 5, 6]]);
                    dx1 = tf.grad(function (x) { return tf.unstack(x)[0]; })(x);
                    expect(dx1.shape).toEqual([2, 3]);
                    expect(dx1.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, dx1.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 1, 1, 0, 0, 0]]);
                    dx2 = tf.grad(function (x) { return tf.unstack(x)[1]; })(x);
                    expect(dx2.shape).toEqual([2, 3]);
                    expect(dx2.dtype).toBe('float32');
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, dx2.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [0, 0, 0, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, dx1, _a, dx2, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor([[1, 2, 3], [4, 5, 6]]);
                    dx1 = tf.grad(function (x) { return tf.unstack(x.clone())[0].clone(); })(x);
                    expect(dx1.shape).toEqual([2, 3]);
                    expect(dx1.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, dx1.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 1, 1, 0, 0, 0]]);
                    dx2 = tf.grad(function (x) { return tf.unstack(x.clone())[1].clone(); })(x);
                    expect(dx2.shape).toEqual([2, 3]);
                    expect(dx2.dtype).toBe('float32');
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, dx2.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [0, 0, 0, 1, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('grad of unstack axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, axis, dx1, _a, dx2, _b, dx3, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = tf.tensor([[1, 2, 3], [4, 5, 6]]);
                    axis = 1;
                    dx1 = tf.grad(function (x) { return tf.unstack(x, axis)[0]; })(x);
                    expect(dx1.shape).toEqual([2, 3]);
                    expect(dx1.dtype).toBe('float32');
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, dx1.data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1, 0, 0, 1, 0, 0]]);
                    dx2 = tf.grad(function (x) { return tf.unstack(x, axis)[1]; })(x);
                    expect(dx2.shape).toEqual([2, 3]);
                    expect(dx2.dtype).toBe('float32');
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, dx2.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [0, 1, 0, 0, 1, 0]]);
                    dx3 = tf.grad(function (x) { return tf.unstack(x, axis)[2]; })(x);
                    expect(dx3.shape).toEqual([2, 3]);
                    expect(dx3.dtype).toBe('float32');
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, dx3.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [0, 0, 1, 0, 0, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('split', jasmine_util_1.ALL_ENVS, function () {
    it('split by number', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
                    res = tf.split(x, 2, 1);
                    expect(res.length).toEqual(2);
                    expect(res[0].shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 5, 6]]);
                    expect(res[1].shape).toEqual([2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [3, 4, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('split by sizes', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
                    res = tf.split(x, [1, 2, 1], 1);
                    expect(res.length).toEqual(3);
                    expect(res[0].shape).toEqual([2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1, 5]]);
                    expect(res[1].shape).toEqual([2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [2, 3, 6, 7]]);
                    expect(res[2].shape).toEqual([2, 1]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[2].data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [4, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('chainable split by sizes', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
                    res = x.split([1, 2, 1], 1);
                    expect(res.length).toEqual(3);
                    expect(res[0].shape).toEqual([2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), [1, 5]]);
                    expect(res[1].shape).toEqual([2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [2, 3, 6, 7]]);
                    expect(res[2].shape).toEqual([2, 1]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[2].data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [4, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('sizes to not sum to axis size throws error', function () {
        var x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
        var f = function () { return tf.split(x, [1, 2], 1); };
        expect(f).toThrowError();
    });
    it('number of splits does not evenly divide axis', function () {
        var x = tf.tensor2d([1, 2, 3, 4, 5, 6, 7, 8], [2, 4]);
        var f = function () { return tf.split(x, 3, 1); };
        expect(f).toThrowError();
    });
    it('can split a zero-sized tensor, axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, numSplits, axis, res, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    a = tf.zeros([4, 0]);
                    numSplits = 4;
                    axis = 0;
                    res = tf.split(a, numSplits, axis);
                    expect(res.length).toBe(4);
                    expect(res[0].shape).toEqual([1, 0]);
                    expect(res[1].shape).toEqual([1, 0]);
                    expect(res[2].shape).toEqual([1, 0]);
                    expect(res[3].shape).toEqual([1, 0]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), []]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), []]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[2].data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), []]);
                    _d = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[3].data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), []]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('can split a zero-sized tensor, axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, numSplits, axis, res, _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    a = tf.zeros([0, 4]);
                    numSplits = 4;
                    axis = 1;
                    res = tf.split(a, numSplits, axis);
                    expect(res.length).toBe(4);
                    expect(res[0].shape).toEqual([0, 1]);
                    expect(res[1].shape).toEqual([0, 1]);
                    expect(res[2].shape).toEqual([0, 1]);
                    expect(res[3].shape).toEqual([0, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_e.sent(), []]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_e.sent(), []]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[2].data()];
                case 3:
                    _c.apply(void 0, [_e.sent(), []]);
                    _d = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[3].data()];
                case 4:
                    _d.apply(void 0, [_e.sent(), []]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.split({}, 1); })
            .toThrowError(/Argument 'x' passed to 'split' must be a Tensor/);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, res, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = [[1, 2, 3, 4], [5, 6, 7, 8]];
                    res = tf.split(x, 2, 1);
                    expect(res.length).toEqual(2);
                    expect(res[0].shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[0].data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 5, 6]]);
                    expect(res[1].shape).toEqual([2, 2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res[1].data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [3, 4, 7, 8]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient of 1st output', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    da = tf.grad(function (x) { return tf.split(x, [1, 2])[0]; })(a);
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    da = tf.grad(function (x) { return tf.split(x.clone(), [1, 2])[0].clone(); })(a);
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 0, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient of 2nd output', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    da = tf.grad(function (x) { return tf.split(x, [1, 2])[1]; })(a);
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 1, 1]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('expandDims', jasmine_util_1.ALL_ENVS, function () {
    it('scalar, default axis is 0', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.scalar(1).expandDims();
                    expect(res.shape).toEqual([1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('scalar, axis is out of bounds throws error', function () {
        var f = function () { return tf.scalar(1).expandDims(1); };
        expect(f).toThrowError();
    });
    it('1d, axis=-3', function () {
        expect(function () {
            tf.tensor1d([1, 2, 3]).expandDims(-3);
        }).toThrowError('Axis must be in the interval [-2, 1]');
    });
    it('1d, axis=-2', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor1d([1, 2, 3]).expandDims(-2 /* axis */);
                    expect(res.shape).toEqual([1, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1d, axis=-1', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor1d([1, 2, 3]).expandDims(-1 /* axis */);
                    expect(res.shape).toEqual([3, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1d, axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor1d([1, 2, 3]).expandDims(0 /* axis */);
                    expect(res.shape).toEqual([1, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1d, axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor1d([1, 2, 3]).expandDims(1 /* axis */);
                    expect(res.shape).toEqual([3, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d, axis=-4', function () {
        expect(function () {
            tf.tensor2d([[1, 2], [3, 4], [5, 6]]).expandDims(-4 /* axis */);
        }).toThrowError('Axis must be in the interval [-3, 2]');
    });
    it('2d, axis=-3', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4], [5, 6]]).expandDims(-3 /* axis */);
                    expect(res.shape).toEqual([1, 3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d, axis=-2', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4], [5, 6]]).expandDims(-2 /* axis */);
                    expect(res.shape).toEqual([3, 1, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d, axis=-1', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4], [5, 6]]).expandDims(-1 /* axis */);
                    expect(res.shape).toEqual([3, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d, axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4], [5, 6]]).expandDims(0 /* axis */);
                    expect(res.shape).toEqual([1, 3, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d, axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4], [5, 6]]).expandDims(1 /* axis */);
                    expect(res.shape).toEqual([3, 1, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d, axis=2', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4], [5, 6]]).expandDims(2 /* axis */);
                    expect(res.shape).toEqual([3, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('4d, axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor4d([[[[4]]]]).expandDims();
                    expect(res.shape).toEqual([1, 1, 1, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1d string tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor(['hello', 'world']);
                    res = t.expandDims();
                    expect(res.shape).toEqual([1, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['hello', 'world']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2d string tensor, axis=1', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor([['a', 'b'], ['c', 'd']]);
                    res = t.expandDims(1);
                    expect(res.shape).toEqual([2, 1, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), ['a', 'b', 'c', 'd']]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.expandDims({}); })
            .toThrowError(/Argument 'x' passed to 'expandDims' must be a Tensor/);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.expandDims(7);
                    expect(res.shape).toEqual([1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [7]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('works with 0 in shape', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, res, _a, res2, _b, res3, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    a = tf.tensor2d([], [0, 3]);
                    res = a.expandDims();
                    expect(res.shape).toEqual([1, 0, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_d.sent(), []]);
                    res2 = a.expandDims(1);
                    expect(res2.shape).toEqual([0, 1, 3]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res2.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), []]);
                    res3 = a.expandDims(2);
                    expect(res3.shape).toEqual([0, 3, 1]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res3.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), []]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('cumsum', jasmine_util_1.ALL_ENVS, function () {
    it('1D standard', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor1d([1, 2, 3, 4]).cumsum();
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 6, 10]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D reverse', function () { return __awaiter(_this, void 0, void 0, function () {
        var reverse, exclusive, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    reverse = true;
                    exclusive = false;
                    res = tf.tensor1d([1, 2, 3, 4]).cumsum(0, exclusive, reverse);
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [10, 9, 7, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D exclusive', function () { return __awaiter(_this, void 0, void 0, function () {
        var exclusive, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    exclusive = true;
                    res = tf.tensor1d([1, 2, 3, 4]).cumsum(0, exclusive);
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 1, 3, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1D exclusive reverse', function () { return __awaiter(_this, void 0, void 0, function () {
        var reverse, exclusive, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    reverse = true;
                    exclusive = true;
                    res = tf.tensor1d([1, 2, 3, 4]).cumsum(0, exclusive, reverse);
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [9, 7, 4, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient: 1D', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    dy = tf.tensor1d([4, 5, 6]);
                    da = tf.grad(function (x) { return tf.cumsum(x); })(a, dy);
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [15, 11, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones', function () { return __awaiter(_this, void 0, void 0, function () {
        var a, dy, da, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    a = tf.tensor1d([1, 2, 3]);
                    dy = tf.tensor1d([4, 5, 6]);
                    da = tf.grad(function (x) { return tf.cumsum(x.clone()).clone(); })(a, dy);
                    expect(da.shape).toEqual([3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, da.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [15, 11, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D standard', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4]]).cumsum(1);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 3, 7]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D reverse exclusive', function () { return __awaiter(_this, void 0, void 0, function () {
        var reverse, exclusive, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    reverse = true;
                    exclusive = true;
                    res = tf.tensor2d([[1, 2], [3, 4]]).cumsum(1, exclusive, reverse);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [2, 0, 4, 0]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('2D axis=0', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor2d([[1, 2], [3, 4]]).cumsum();
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 4, 6]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('3D standard', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.tensor3d([[[0, 1], [2, 3]], [[4, 5], [6, 7]]]).cumsum(2);
                    expect(res.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [0, 1, 2, 5, 4, 9, 6, 13]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when passed a non-tensor', function () {
        expect(function () { return tf.cumsum({}); })
            .toThrowError(/Argument 'x' passed to 'cumsum' must be a Tensor/);
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    res = tf.cumsum([1, 2, 3, 4]);
                    expect(res.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 6, 10]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws error for string tensor', function () {
        expect(function () { return tf.cumsum([
            'a', 'b', 'c'
        ]); }).toThrowError(/Argument 'x' passed to 'cumsum' must be numeric tensor/);
    });
});
jasmine_util_1.describeWithFlags('batchToSpaceND', jasmine_util_1.ALL_ENVS, function () {
    it('tensor4d, input shape=[4, 1, 1, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([1, 2, 3, 4], [4, 1, 1, 1]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([1, 2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[4, 1, 1, 3], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [4, 1, 1, 3]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([1, 2, 2, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[4, 2, 2, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([1, 3, 9, 11, 2, 4, 10, 12, 5, 7, 13, 15, 6, 8, 14, 16], [4, 2, 2, 1]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([1, 4, 4, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[8, 1, 3, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([
                        0, 1, 3, 0, 9, 11, 0, 2, 4, 0, 10, 12,
                        0, 5, 7, 0, 13, 15, 0, 6, 8, 0, 14, 16
                    ], [8, 1, 3, 1]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [2, 0]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([2, 2, 4, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor2d, blockShape [1]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 2, 3, 4], [2, 2]);
                    blockShape = [2];
                    crops = [[0, 0]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([1, 4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 3, 2, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor3d,  blockSHape [1]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor([
                        -61, 37, -68, 72, 31, 62, 0, -13, 28, 54, 96,
                        44, -55, -64, -88, -94, 65, -32, -96, -73, -2, -77,
                        -14, 47, 33, 15, 70, 20, 75, 28, 84, -13
                    ], [8, 2, 2]);
                    blockShape = [2];
                    crops = [[0, 2]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([4, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [-61, 37, 65, -32, 31, 62, -2, -77, 28, 54, 33, 15, -55, -64, 75, 28]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor3d, blockShape [2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor([
                        -61, 37, -68, 72, 31, 62, 0, -13, 28, 54, 96,
                        44, -55, -64, -88, -94, 65, -32, -96, -73, -2, -77,
                        -14, 47, 33, 15, 70, 20, 75, 28, 84, -13
                    ], [8, 2, 2]);
                    blockShape = [2, 2];
                    crops = [[2, 0], [2, 0]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([2, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [72, 44, -73, 20, -13, -94, 47, -13]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when blockShape equal to input rank', function () {
        var t = tf.tensor4d([1, 2, 3, 4], [4, 1, 1, 1]);
        var blockShape = [2, 2, 2, 2];
        var crops = [[0, 0], [0, 0], [0, 0], [0, 0]];
        expect(function () { return tf.batchToSpaceND(t, blockShape, crops); })
            .toThrowError("input rank is " + t.rank + " but should be > than blockShape.length " + blockShape.length);
    });
    it('throws when crops row dimension not equal to blockshape', function () {
        var t = tf.tensor4d([1, 2, 3, 4], [4, 1, 1, 1]);
        var blockShape = [2, 2];
        var crops = [[0, 0]];
        expect(function () { return tf.batchToSpaceND(t, blockShape, crops); })
            .toThrowError("crops.length is " + crops.length + " but should be equal to blockShape.length  " + blockShape.length);
    });
    it('throws when input tensor batch not divisible by prod(blockShape)', function () {
        var t = tf.tensor4d([1, 2, 3, 4, 5], [5, 1, 1, 1]);
        var blockShape = [2, 2];
        var crops = [[0, 0], [0, 0]];
        var prod = blockShape.reduce(function (a, b) { return a * b; });
        expect(function () { return tf.batchToSpaceND(t, blockShape, crops); })
            .toThrowError("input tensor batch is " + t.shape[0] + " but is not divisible by the " +
            ("product of the elements of blockShape " + blockShape.join(' * ') + " === " + prod));
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = [[[[1]]], [[[2]]], [[[3]]], [[[4]]]];
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    res = tf.batchToSpaceND(t, blockShape, crops);
                    expect(res.shape).toEqual([1, 2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradients,  input shape=[4, 2, 2], block shape=[2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, dy, gradient, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor([-61, 37, -68, 72, 31, 62, 0, -13, 28, 54, 96, 44, -55, -64, -88, -94], [4, 2, 2]);
                    blockShape = [2];
                    crops = [[0, 2]];
                    dy = tf.tensor([.01, .02, .03, .04, .05, .06, .07, .08], [2, 2, 2]);
                    gradient = tf.grad(function (t) { return tf.batchToSpaceND(t, blockShape, crops); })(t, dy);
                    expect(gradient.shape).toEqual([4, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradient.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [
                            0.01, 0.02, 0, 0, 0.05, 0.06, 0, 0, 0.03, 0.04, 0, 0, 0.07, 0.08, 0, 0
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradients, input shape=[4, 2, 2, 1], block shape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, dy, gradient, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([1, 3, 9, 11, 2, 4, 10, 12, 5, 7, 13, 15, 6, 8, 14, 16], [4, 2, 2, 1]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    dy = tf.tensor([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4, 1]);
                    gradient = tf.grad(function (t) { return tf.batchToSpaceND(t, blockShape, crops); })(t, dy);
                    expect(gradient.shape).toEqual([4, 2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradient.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 3, 9, 11, 2, 4, 10, 12, 5, 7, 13, 15, 6, 8, 14, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones, input=[4, 2, 2, 1], block shape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, dy, gradient, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([1, 3, 9, 11, 2, 4, 10, 12, 5, 7, 13, 15, 6, 8, 14, 16], [4, 2, 2, 1]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    dy = tf.tensor([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [1, 4, 4, 1]);
                    gradient = tf.grad(function (t) { return tf.batchToSpaceND(t.clone(), blockShape, crops).clone(); })(t, dy);
                    expect(gradient.shape).toEqual([4, 2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradient.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 3, 9, 11, 2, 4, 10, 12, 5, 7, 13, 15, 6, 8, 14, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('spaceToBatchND', jasmine_util_1.ALL_ENVS, function () {
    it('tensor4d, input shape=[1, 2, 2, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([[[[1], [2]], [[3], [4]]]], [1, 2, 2, 1]);
                    blockShape = [2, 2];
                    paddings = [[0, 0], [0, 0]];
                    res = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(res.shape).toEqual([4, 1, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[1, 2, 2, 3], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([[[[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]]], [1, 2, 2, 3]);
                    blockShape = [2, 2];
                    paddings = [[0, 0], [0, 0]];
                    res = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(res.shape).toEqual([4, 1, 1, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[1, 4, 4, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([[
                            [[1], [2], [3], [4]], [[5], [6], [7], [8]], [[9], [10], [11], [12]],
                            [[13], [14], [15], [16]]
                        ]], [1, 4, 4, 1]);
                    blockShape = [2, 2];
                    paddings = [[0, 0], [0, 0]];
                    res = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(res.shape).toEqual([4, 2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 3, 9, 11, 2, 4, 10, 12, 5, 7, 13, 15, 6, 8, 14, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[2, 6, 6, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([
                        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
                        16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
                        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45,
                        46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60,
                        61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72
                    ], [2, 6, 6, 1]);
                    blockShape = [2, 2];
                    paddings = [[0, 0], [0, 0]];
                    res = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(res.shape).toEqual([8, 3, 3, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [
                            1, 3, 5, 13, 15, 17, 25, 27, 29, 37, 39, 41, 49, 51, 53, 61, 63, 65,
                            2, 4, 6, 14, 16, 18, 26, 28, 30, 38, 40, 42, 50, 52, 54, 62, 64, 66,
                            7, 9, 11, 19, 21, 23, 31, 33, 35, 43, 45, 47, 55, 57, 59, 67, 69, 71,
                            8, 10, 12, 20, 22, 24, 32, 34, 36, 44, 46, 48, 56, 58, 60, 68, 70, 72
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[2, 2, 4, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([
                        [[[1], [2], [3], [4]], [[5], [6], [7], [8]]],
                        [[[9], [10], [11], [12]], [[13], [14], [15], [16]]]
                    ], [2, 2, 4, 1]);
                    blockShape = [2, 2];
                    paddings = [[0, 0], [2, 0]];
                    res = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(res.shape).toEqual([8, 1, 3, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [
                            0, 1, 3, 0, 9, 11, 0, 2, 4, 0, 10, 12,
                            0, 5, 7, 0, 13, 15, 0, 6, 8, 0, 14, 16
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor2d, blockShape [2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor2d([1, 3, 2, 4], [1, 4]);
                    blockShape = [2];
                    paddings = [[0, 0]];
                    res = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(res.shape).toEqual([2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when blockShape equal to input rank', function () {
        var t = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        var blockShape = [2, 2, 2, 2];
        var paddings = [[0, 0], [0, 0], [0, 0], [0, 0]];
        expect(function () { return tf.spaceToBatchND(t, blockShape, paddings); })
            .toThrowError('input rank 4 should be > than [blockShape] 4');
    });
    it('throws when paddings row dimension not equal to blockshape', function () {
        var t = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);
        var blockShape = [2, 2];
        var paddings = [[0, 0]];
        expect(function () { return tf.spaceToBatchND(t, blockShape, paddings); })
            .toThrowError('paddings.shape[0] 1 must be equal to [blockShape] 2');
    });
    it('throws when input tensor spatial dimension not divisible by blockshapes', function () {
        var t = tf.tensor4d([1, 2, 3, 4, 5, 6], [1, 2, 3, 1]);
        var blockShape = [2, 2];
        var paddings = [[0, 0], [0, 0]];
        expect(function () { return tf.spaceToBatchND(t, blockShape, paddings); })
            .toThrowError('input spatial dimensions 2,3,1 with paddings 0,0,0,0 must be ' +
            'divisible by blockShapes 2,2');
    });
    it('accepts a tensor-like object', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = [[[[1], [2]], [[3], [4]]]];
                    blockShape = [2, 2];
                    paddings = [[0, 0], [0, 0]];
                    res = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(res.shape).toEqual([4, 1, 1, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('batchToSpaceND X spaceToBatchND', jasmine_util_1.ALL_ENVS, function () {
    it('tensor4d, input shape=[4, 1, 1, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, paddings, b2s, _a, s2b, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    t = tf.tensor4d([1, 2, 3, 4], [4, 1, 1, 1]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    paddings = [[0, 0], [0, 0]];
                    b2s = tf.batchToSpaceND(t, blockShape, crops);
                    expect(b2s.shape).toEqual([1, 2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b2s.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [1, 2, 3, 4]]);
                    s2b = tf.spaceToBatchND(b2s, blockShape, paddings);
                    expect(s2b.shape).toEqual([4, 1, 1, 1]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, s2b.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[2, 6, 6, 1], blockShape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, crops, paddings, s2b, _a, b2s, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    t = tf.tensor4d([
                        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
                        16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
                        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45,
                        46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60,
                        61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72
                    ], [2, 6, 6, 1]);
                    blockShape = [2, 2];
                    crops = [[0, 0], [0, 0]];
                    paddings = [[0, 0], [0, 0]];
                    s2b = tf.spaceToBatchND(t, blockShape, paddings);
                    expect(s2b.shape).toEqual([8, 3, 3, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, s2b.data()];
                case 1:
                    _a.apply(void 0, [_c.sent(), [
                            1, 3, 5, 13, 15, 17, 25, 27, 29, 37, 39, 41, 49, 51, 53, 61, 63, 65,
                            2, 4, 6, 14, 16, 18, 26, 28, 30, 38, 40, 42, 50, 52, 54, 62, 64, 66,
                            7, 9, 11, 19, 21, 23, 31, 33, 35, 43, 45, 47, 55, 57, 59, 67, 69, 71,
                            8, 10, 12, 20, 22, 24, 32, 34, 36, 44, 46, 48, 56, 58, 60, 68, 70, 72
                        ]]);
                    b2s = tf.batchToSpaceND(s2b, blockShape, crops);
                    expect(b2s.shape).toEqual([2, 6, 6, 1]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, b2s.data()];
                case 2:
                    _b.apply(void 0, [_c.sent(), [
                            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
                            19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,
                            37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54,
                            55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72
                        ]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradients,  input shape=[4, 2, 2], block shape=[2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, dy, gradient, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor([-61, 37, -68, 72, 31, 62, 0, -13, 28, 54, 96, 44, -55, -64, -88, -94], [4, 2, 2]);
                    blockShape = [2];
                    paddings = [[0, 2]];
                    dy = tf.tensor([
                        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
                        17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32
                    ], [8, 2, 2]);
                    gradient = tf.grad(function (t) { return tf.spaceToBatchND(t, blockShape, paddings); })(t, dy);
                    expect(gradient.shape).toEqual([4, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradient.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 2, 17, 18, 5, 6, 21, 22, 9, 10, 25, 26, 13, 14, 29, 30]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradient with clones input=[4, 2, 2], block shape=[2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, dy, gradient, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor([-61, 37, -68, 72, 31, 62, 0, -13, 28, 54, 96, 44, -55, -64, -88, -94], [4, 2, 2]);
                    blockShape = [2];
                    paddings = [[0, 2]];
                    dy = tf.tensor([
                        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
                        17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32
                    ], [8, 2, 2]);
                    gradient = tf.grad(function (t) { return tf.spaceToBatchND(t.clone(), blockShape, paddings).clone(); })(t, dy);
                    expect(gradient.shape).toEqual([4, 2, 2]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradient.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 2, 17, 18, 5, 6, 21, 22, 9, 10, 25, 26, 13, 14, 29, 30]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('gradients, input shape=[2, 2, 4, 1], block shape=[2, 2]', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockShape, paddings, dy, gradient, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([
                        [[[1], [2], [3], [4]], [[5], [6], [7], [8]]],
                        [[[9], [10], [11], [12]], [[13], [14], [15], [16]]]
                    ], [2, 2, 4, 1]);
                    blockShape = [2, 2];
                    paddings = [[0, 0], [2, 0]];
                    dy = tf.tensor([
                        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
                        13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24
                    ], [8, 1, 3, 1]);
                    gradient = tf.grad(function (t) { return tf.spaceToBatchND(t, blockShape, paddings); })(t, dy);
                    expect(gradient.shape).toEqual([2, 2, 4, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, gradient.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [2, 8, 3, 9, 14, 20, 15, 21, 5, 11, 6, 12, 17, 23, 18, 24]]);
                    return [2 /*return*/];
            }
        });
    }); });
});
jasmine_util_1.describeWithFlags('depthToSpace', jasmine_util_1.ALL_ENVS, function () {
    it('tensor4d, input shape=[1, 1, 1, 4], blockSize=2, format=NHWC', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockSize, dataFormat, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([[[[1, 2, 3, 4]]]]);
                    blockSize = 2;
                    dataFormat = 'NHWC';
                    res = tf.depthToSpace(t, blockSize, dataFormat);
                    expect(res.shape).toEqual([1, 2, 2, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[1, 1, 1, 12], blockSize=2, format=NHWC', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockSize, dataFormat, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([[[[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]]]);
                    blockSize = 2;
                    dataFormat = 'NHWC';
                    res = tf.depthToSpace(t, blockSize, dataFormat);
                    expect(res.shape).toEqual([1, 2, 2, 3]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor4d, input shape=[1, 2, 2, 4], blockSize=2, format=NHWC', function () { return __awaiter(_this, void 0, void 0, function () {
        var t, blockSize, dataFormat, res, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    t = tf.tensor4d([
                        [[[1, 2, 3, 4], [5, 6, 7, 8]], [[9, 10, 11, 12], [13, 14, 15, 16]]]
                    ]);
                    blockSize = 2;
                    dataFormat = 'NHWC';
                    res = tf.depthToSpace(t, blockSize, dataFormat);
                    expect(res.shape).toEqual([1, 4, 4, 1]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, res.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(),
                        [1, 2, 5, 6, 3, 4, 7, 8, 9, 10, 13, 14, 11, 12, 15, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when depth not divisible by blockSize * blockSize', function () {
        var t = tf.tensor4d([1, 2, 3, 4], [1, 1, 1, 4]);
        var blockSize = 3;
        expect(function () { return tf.depthToSpace(t, blockSize); })
            .toThrowError("Dimension size must be evenly divisible by " + blockSize * blockSize + " but is " + t.shape[3] + " for depthToSpace with input shape " + t.shape);
    });
});
jasmine_util_1.describeWithFlags('depthToSpace', jasmine_util_1.BROWSER_ENVS, function () {
    it('throws when blocksize < 2', function () {
        var t = tf.tensor4d([1, 2, 3, 4], [1, 1, 1, 4]);
        var blockSize = 1;
        expect(function () { return tf.depthToSpace(t, blockSize); })
            .toThrowError("blockSize should be > 1 for depthToSpace, but was: " + blockSize);
    });
});
jasmine_util_1.describeWithFlags('setdiff1dAsync', jasmine_util_1.ALL_ENVS, function () {
    it('1d int32 tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, y, _a, out, indices, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = tf.tensor1d([1, 2, 3, 4], 'int32');
                    y = tf.tensor1d([1, 2], 'int32');
                    return [4 /*yield*/, tf.setdiff1dAsync(x, y)];
                case 1:
                    _a = _d.sent(), out = _a[0], indices = _a[1];
                    expect(out.dtype).toBe('int32');
                    expect(indices.dtype).toBe('int32');
                    expect(out.shape).toEqual([2]);
                    expect(indices.shape).toEqual([2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, out.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [3, 4]]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, indices.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [2, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('1d float32 tensor', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, y, _a, out, indices, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = tf.tensor1d([1, 2, 3, 4], 'float32');
                    y = tf.tensor1d([1, 3], 'float32');
                    return [4 /*yield*/, tf.setdiff1dAsync(x, y)];
                case 1:
                    _a = _d.sent(), out = _a[0], indices = _a[1];
                    expect(out.dtype).toBe('float32');
                    expect(indices.dtype).toBe('int32');
                    expect(out.shape).toEqual([2]);
                    expect(indices.shape).toEqual([2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, out.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [2, 4]]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, indices.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [1, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('empty output', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, y, _a, out, indices, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = tf.tensor1d([1, 2, 3, 4], 'float32');
                    y = tf.tensor1d([1, 2, 3, 4], 'float32');
                    return [4 /*yield*/, tf.setdiff1dAsync(x, y)];
                case 1:
                    _a = _d.sent(), out = _a[0], indices = _a[1];
                    expect(out.dtype).toBe('float32');
                    expect(indices.dtype).toBe('int32');
                    expect(out.shape).toEqual([0]);
                    expect(indices.shape).toEqual([0]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, out.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), []]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, indices.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), []]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('tensor like', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, y, _a, out, indices, _b, _c;
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    x = [1, 2, 3, 4];
                    y = [1, 3];
                    return [4 /*yield*/, tf.setdiff1dAsync(x, y)];
                case 1:
                    _a = _d.sent(), out = _a[0], indices = _a[1];
                    expect(out.dtype).toBe('float32');
                    expect(indices.dtype).toBe('int32');
                    expect(out.shape).toEqual([2]);
                    expect(indices.shape).toEqual([2]);
                    _b = test_util_1.expectArraysClose;
                    return [4 /*yield*/, out.data()];
                case 2:
                    _b.apply(void 0, [_d.sent(), [2, 4]]);
                    _c = test_util_1.expectArraysClose;
                    return [4 /*yield*/, indices.data()];
                case 3:
                    _c.apply(void 0, [_d.sent(), [1, 3]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('should throw if x is not 1d', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, y, ex_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor2d([1, 2, 3, 4], [4, 1], 'float32');
                    y = tf.tensor1d([1, 2, 3, 4], 'float32');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, , 4]);
                    return [4 /*yield*/, tf.setdiff1dAsync(x, y)];
                case 2:
                    _a.sent();
                    throw new Error('The line above should have thrown an error');
                case 3:
                    ex_1 = _a.sent();
                    expect(ex_1.message).toBe('x should be 1D tensor, but got x (4,1).');
                    return [3 /*break*/, 4];
                case 4: return [2 /*return*/];
            }
        });
    }); });
    it('should throw if y is not 1d', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, y, ex_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor1d([1, 2, 3, 4], 'float32');
                    y = tf.tensor2d([1, 2, 3, 4], [4, 1], 'float32');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, , 4]);
                    return [4 /*yield*/, tf.setdiff1dAsync(x, y)];
                case 2:
                    _a.sent();
                    throw new Error('The line above should have thrown an error');
                case 3:
                    ex_2 = _a.sent();
                    expect(ex_2.message).toBe('y should be 1D tensor, but got y (4,1).');
                    return [3 /*break*/, 4];
                case 4: return [2 /*return*/];
            }
        });
    }); });
    it('should throw if x and y dtype mismatch', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, y, ex_3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tf.tensor1d([1, 2, 3, 4], 'float32');
                    y = tf.tensor1d([1, 2, 3, 4], 'int32');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, , 4]);
                    return [4 /*yield*/, tf.setdiff1dAsync(x, y)];
                case 2:
                    _a.sent();
                    throw new Error('The line above should have thrown an error');
                case 3:
                    ex_3 = _a.sent();
                    expect(ex_3.message)
                        .toBe('x and y should have the same dtype,' +
                        ' but got x (float32) and y (int32).');
                    return [3 /*break*/, 4];
                case 4: return [2 /*return*/];
            }
        });
    }); });
});
//# sourceMappingURL=array_ops_test.js.map