{"dependencies": [{"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 43, "index": 645}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}, {"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 646}, "end": {"line": 5, "column": 45, "index": 691}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkPaint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 692}, "end": {"line": 6, "column": 42, "index": 734}}], "key": "tZL5XO67L1lBKN/ngQFuxeWOGxA=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 735}, "end": {"line": 7, "column": 40, "index": 775}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}, {"name": "./JsiSkRRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 776}, "end": {"line": 8, "column": 42, "index": 818}}], "key": "n4Z2DW77BVppQ2PhsKnE4j8f2qY=", "exportNames": ["*"]}}, {"name": "./JsiSkImage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 819}, "end": {"line": 9, "column": 42, "index": 861}}], "key": "5HhJKBZpkVLC59VDnsBNlnwB3DE=", "exportNames": ["*"]}}, {"name": "./JsiSkVertices", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 862}, "end": {"line": 10, "column": 48, "index": 910}}], "key": "v7t1sO52wRZjHCJe3y6WxuPRi1Q=", "exportNames": ["*"]}}, {"name": "./JsiSkPath", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 911}, "end": {"line": 11, "column": 40, "index": 951}}], "key": "h12LvMRBvFyvLJVrX3awiGHZPFU=", "exportNames": ["*"]}}, {"name": "./JsiSkFont", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 952}, "end": {"line": 12, "column": 40, "index": 992}}], "key": "s9s7ketQ537BYUKlzBosFu+c4vc=", "exportNames": ["*"]}}, {"name": "./JsiSkTextBlob", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 993}, "end": {"line": 13, "column": 48, "index": 1041}}], "key": "E6PYnUo+68irrsyJGMkO/cUOor4=", "exportNames": ["*"]}}, {"name": "./JsiSkPicture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 1042}, "end": {"line": 14, "column": 46, "index": 1088}}], "key": "/AN6zwESuLHK8aJTwDD/mo1rd2w=", "exportNames": ["*"]}}, {"name": "./JsiSkMatrix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 1089}, "end": {"line": 15, "column": 44, "index": 1133}}], "key": "aOVfjZgmz4R2ci39pV6HZujK8og=", "exportNames": ["*"]}}, {"name": "./JsiSkImageFilter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 1134}, "end": {"line": 16, "column": 54, "index": 1188}}], "key": "wZakwk1fGNWxYRxu1UpAHXjAB8M=", "exportNames": ["*"]}}, {"name": "./JsiSkPoint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 1189}, "end": {"line": 17, "column": 42, "index": 1231}}], "key": "t00LaVO/wJ2FJvJQ0krGRNiisCQ=", "exportNames": ["*"]}}, {"name": "./JsiSkRSXform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 1232}, "end": {"line": 18, "column": 46, "index": 1278}}], "key": "HY7rKwrek3L50sQmwciH/W0T7ew=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkCanvas = void 0;\n  var _types = require(_dependencyMap[0], \"../types\");\n  var _Host = require(_dependencyMap[1], \"./Host\");\n  var _JsiSkPaint = require(_dependencyMap[2], \"./JsiSkPaint\");\n  var _JsiSkRect = require(_dependencyMap[3], \"./JsiSkRect\");\n  var _JsiSkRRect = require(_dependencyMap[4], \"./JsiSkRRect\");\n  var _JsiSkImage = require(_dependencyMap[5], \"./JsiSkImage\");\n  var _JsiSkVertices = require(_dependencyMap[6], \"./JsiSkVertices\");\n  var _JsiSkPath = require(_dependencyMap[7], \"./JsiSkPath\");\n  var _JsiSkFont = require(_dependencyMap[8], \"./JsiSkFont\");\n  var _JsiSkTextBlob = require(_dependencyMap[9], \"./JsiSkTextBlob\");\n  var _JsiSkPicture = require(_dependencyMap[10], \"./JsiSkPicture\");\n  var _JsiSkMatrix = require(_dependencyMap[11], \"./JsiSkMatrix\");\n  var _JsiSkImageFilter = require(_dependencyMap[12], \"./JsiSkImageFilter\");\n  var _JsiSkPoint = require(_dependencyMap[13], \"./JsiSkPoint\");\n  var _JsiSkRSXform = require(_dependencyMap[14], \"./JsiSkRSXform\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkCanvas extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Canvas\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    drawRect(rect, paint) {\n      this.ref.drawRect(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, rect), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawImage(image, x, y, paint) {\n      this.ref.drawImage(_JsiSkImage.JsiSkImage.fromValue(image), x, y, paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : paint);\n    }\n    drawImageRect(img, src, dest, paint, fastSample) {\n      this.ref.drawImageRect(_JsiSkImage.JsiSkImage.fromValue(img), _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, src), _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, dest), _JsiSkPaint.JsiSkPaint.fromValue(paint), fastSample);\n    }\n    drawImageCubic(img, left, top, B, C, paint) {\n      this.ref.drawImageCubic(_JsiSkImage.JsiSkImage.fromValue(img), left, top, B, C, paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : paint);\n    }\n    drawImageOptions(img, left, top, fm, mm, paint) {\n      this.ref.drawImageOptions(_JsiSkImage.JsiSkImage.fromValue(img), left, top, (0, _Host.getEnum)(this.CanvasKit, \"FilterMode\", fm), (0, _Host.getEnum)(this.CanvasKit, \"MipmapMode\", mm), paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : paint);\n    }\n    drawImageNine(img, center, dest, filter, paint) {\n      this.ref.drawImageNine(_JsiSkImage.JsiSkImage.fromValue(img), Array.from(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, center)), _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, dest), (0, _Host.getEnum)(this.CanvasKit, \"FilterMode\", filter), paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : paint);\n    }\n    drawImageRectCubic(img, src, dest, B, C, paint) {\n      this.ref.drawImageRectCubic(_JsiSkImage.JsiSkImage.fromValue(img), _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, src), _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, dest), B, C, paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : paint);\n    }\n    drawImageRectOptions(img, src, dest, fm, mm, paint) {\n      this.ref.drawImageRectOptions(_JsiSkImage.JsiSkImage.fromValue(img), _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, src), _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, dest), (0, _Host.getEnum)(this.CanvasKit, \"FilterMode\", fm), (0, _Host.getEnum)(this.CanvasKit, \"MipmapMode\", mm), paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : paint);\n    }\n    drawPaint(paint) {\n      this.ref.drawPaint(_JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawLine(x0, y0, x1, y1, paint) {\n      this.ref.drawLine(x0, y0, x1, y1, _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawCircle(cx, cy, radius, paint) {\n      this.ref.drawCircle(cx, cy, radius, _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawVertices(verts, mode, paint) {\n      this.ref.drawVertices(_JsiSkVertices.JsiSkVertices.fromValue(verts), (0, _Host.getEnum)(this.CanvasKit, \"BlendMode\", mode), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawPatch(cubics, colors, texs, mode, paint) {\n      this.ref.drawPatch(cubics.map(({\n        x,\n        y\n      }) => [x, y]).flat(), colors, texs ? texs.flatMap(p => Array.from(_JsiSkPoint.JsiSkPoint.fromValue(p))) : texs, mode ? (0, _Host.getEnum)(this.CanvasKit, \"BlendMode\", mode) : null, paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : undefined);\n    }\n    restoreToCount(saveCount) {\n      this.ref.restoreToCount(saveCount);\n    }\n    drawPoints(mode, points, paint) {\n      this.ref.drawPoints((0, _Host.getEnum)(this.CanvasKit, \"PointMode\", mode), points.map(({\n        x,\n        y\n      }) => [x, y]).flat(), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawArc(oval, startAngle, sweepAngle, useCenter, paint) {\n      this.ref.drawArc(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, oval), startAngle, sweepAngle, useCenter, _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawRRect(rrect, paint) {\n      this.ref.drawRRect(_JsiSkRRect.JsiSkRRect.fromValue(this.CanvasKit, rrect), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawDRRect(outer, inner, paint) {\n      this.ref.drawDRRect(_JsiSkRRect.JsiSkRRect.fromValue(this.CanvasKit, outer), _JsiSkRRect.JsiSkRRect.fromValue(this.CanvasKit, inner), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawOval(oval, paint) {\n      this.ref.drawOval(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, oval), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawPath(path, paint) {\n      this.ref.drawPath(_JsiSkPath.JsiSkPath.fromValue(path), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawText(str, x, y, paint, font) {\n      this.ref.drawText(str, x, y, _JsiSkPaint.JsiSkPaint.fromValue(paint), _JsiSkFont.JsiSkFont.fromValue(font));\n    }\n    drawTextBlob(blob, x, y, paint) {\n      this.ref.drawTextBlob(_JsiSkTextBlob.JsiSkTextBlob.fromValue(blob), x, y, _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawGlyphs(glyphs, positions, x, y, font, paint) {\n      this.ref.drawGlyphs(glyphs, positions.map(p => [p.x, p.y]).flat(), x, y, _JsiSkFont.JsiSkFont.fromValue(font), _JsiSkPaint.JsiSkPaint.fromValue(paint));\n    }\n    drawSvg(svg, _width, _height) {\n      const image = this.CanvasKit.MakeImageFromCanvasImageSource(svg.ref);\n      this.ref.drawImage(image, 0, 0);\n    }\n    save() {\n      return this.ref.save();\n    }\n    saveLayer(paint, bounds, backdrop, flags) {\n      return this.ref.saveLayer(paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : undefined, bounds ? _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, bounds) : bounds, backdrop ? _JsiSkImageFilter.JsiSkImageFilter.fromValue(backdrop) : backdrop, flags);\n    }\n    restore() {\n      this.ref.restore();\n    }\n    rotate(rotationInDegrees, rx, ry) {\n      this.ref.rotate(rotationInDegrees, rx, ry);\n    }\n    scale(sx, sy) {\n      this.ref.scale(sx, sy);\n    }\n    skew(sx, sy) {\n      this.ref.skew(sx, sy);\n    }\n    translate(dx, dy) {\n      this.ref.translate(dx, dy);\n    }\n    drawColor(color, blendMode) {\n      this.ref.drawColor(color, blendMode ? (0, _Host.getEnum)(this.CanvasKit, \"BlendMode\", blendMode) : undefined);\n    }\n    clear(color) {\n      this.ref.clear(color);\n    }\n    clipPath(path, op, doAntiAlias) {\n      this.ref.clipPath(_JsiSkPath.JsiSkPath.fromValue(path), (0, _Host.getEnum)(this.CanvasKit, \"PathOp\", op), doAntiAlias);\n    }\n    clipRect(rect, op, doAntiAlias) {\n      this.ref.clipRect(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, rect), (0, _Host.getEnum)(this.CanvasKit, \"PathOp\", op), doAntiAlias);\n    }\n    clipRRect(rrect, op, doAntiAlias) {\n      this.ref.clipRRect(_JsiSkRRect.JsiSkRRect.fromValue(this.CanvasKit, rrect), (0, _Host.getEnum)(this.CanvasKit, \"PathOp\", op), doAntiAlias);\n    }\n    concat(m) {\n      this.ref.concat(Array.isArray(m) ? m : _JsiSkMatrix.JsiSkMatrix.fromValue(m));\n    }\n    drawPicture(skp) {\n      this.ref.drawPicture(_JsiSkPicture.JsiSkPicture.fromValue(skp));\n    }\n    drawAtlas(atlas, srcs, dsts, paint, blendMode, colors, sampling) {\n      const src = srcs.flatMap(s => Array.from(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, s)));\n      const dst = dsts.flatMap(s => Array.from(_JsiSkRSXform.JsiSkRSXform.fromValue(s)));\n      let cls;\n      if (colors) {\n        cls = new Uint32Array(colors.length);\n        for (let i = 0; i < colors.length; i++) {\n          const [r, g, b, a] = colors[i];\n          cls[i] = this.CanvasKit.ColorAsInt(r * 255, g * 255, b * 255, a * 255);\n        }\n      }\n      let ckSampling = {\n        filter: this.CanvasKit.FilterMode.Linear,\n        mipmap: this.CanvasKit.MipmapMode.None\n      };\n      if (sampling && (0, _types.isCubicSampling)(sampling)) {\n        ckSampling = sampling;\n      } else if (sampling) {\n        ckSampling = {\n          filter: (0, _Host.getEnum)(this.CanvasKit, \"FilterMode\", sampling.filter),\n          mipmap: sampling.mipmap ? (0, _Host.getEnum)(this.CanvasKit, \"MipmapMode\", sampling.mipmap) : this.CanvasKit.MipmapMode.None\n        };\n      }\n      this.ref.drawAtlas(_JsiSkImage.JsiSkImage.fromValue(atlas), src, dst, _JsiSkPaint.JsiSkPaint.fromValue(paint), blendMode ? (0, _Host.getEnum)(this.CanvasKit, \"BlendMode\", blendMode) : this.CanvasKit.BlendMode.DstOver, cls, ckSampling);\n    }\n    readPixels(srcX, srcY, imageInfo) {\n      const pxInfo = {\n        width: imageInfo.width,\n        height: imageInfo.height,\n        colorSpace: this.CanvasKit.ColorSpace.SRGB,\n        alphaType: (0, _Host.getEnum)(this.CanvasKit, \"AlphaType\", imageInfo.alphaType),\n        colorType: (0, _Host.getEnum)(this.CanvasKit, \"ColorType\", imageInfo.colorType)\n      };\n      return this.ref.readPixels(srcX, srcY, pxInfo);\n    }\n  }\n  exports.JsiSkCanvas = JsiSkCanvas;\n});", "lineCount": 208, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_types"], [6, 12, 4, 0], [6, 15, 4, 0, "require"], [6, 22, 4, 0], [6, 23, 4, 0, "_dependencyMap"], [6, 37, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_Host"], [7, 11, 5, 0], [7, 14, 5, 0, "require"], [7, 21, 5, 0], [7, 22, 5, 0, "_dependencyMap"], [7, 36, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkPaint"], [8, 17, 6, 0], [8, 20, 6, 0, "require"], [8, 27, 6, 0], [8, 28, 6, 0, "_dependencyMap"], [8, 42, 6, 0], [9, 2, 7, 0], [9, 6, 7, 0, "_JsiSkRect"], [9, 16, 7, 0], [9, 19, 7, 0, "require"], [9, 26, 7, 0], [9, 27, 7, 0, "_dependencyMap"], [9, 41, 7, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_JsiSkRRect"], [10, 17, 8, 0], [10, 20, 8, 0, "require"], [10, 27, 8, 0], [10, 28, 8, 0, "_dependencyMap"], [10, 42, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_JsiSkImage"], [11, 17, 9, 0], [11, 20, 9, 0, "require"], [11, 27, 9, 0], [11, 28, 9, 0, "_dependencyMap"], [11, 42, 9, 0], [12, 2, 10, 0], [12, 6, 10, 0, "_JsiSkVertices"], [12, 20, 10, 0], [12, 23, 10, 0, "require"], [12, 30, 10, 0], [12, 31, 10, 0, "_dependencyMap"], [12, 45, 10, 0], [13, 2, 11, 0], [13, 6, 11, 0, "_JsiSkPath"], [13, 16, 11, 0], [13, 19, 11, 0, "require"], [13, 26, 11, 0], [13, 27, 11, 0, "_dependencyMap"], [13, 41, 11, 0], [14, 2, 12, 0], [14, 6, 12, 0, "_JsiSkFont"], [14, 16, 12, 0], [14, 19, 12, 0, "require"], [14, 26, 12, 0], [14, 27, 12, 0, "_dependencyMap"], [14, 41, 12, 0], [15, 2, 13, 0], [15, 6, 13, 0, "_JsiSkTextBlob"], [15, 20, 13, 0], [15, 23, 13, 0, "require"], [15, 30, 13, 0], [15, 31, 13, 0, "_dependencyMap"], [15, 45, 13, 0], [16, 2, 14, 0], [16, 6, 14, 0, "_JsiSkPicture"], [16, 19, 14, 0], [16, 22, 14, 0, "require"], [16, 29, 14, 0], [16, 30, 14, 0, "_dependencyMap"], [16, 44, 14, 0], [17, 2, 15, 0], [17, 6, 15, 0, "_JsiSkMatrix"], [17, 18, 15, 0], [17, 21, 15, 0, "require"], [17, 28, 15, 0], [17, 29, 15, 0, "_dependencyMap"], [17, 43, 15, 0], [18, 2, 16, 0], [18, 6, 16, 0, "_JsiSkImageFilter"], [18, 23, 16, 0], [18, 26, 16, 0, "require"], [18, 33, 16, 0], [18, 34, 16, 0, "_dependencyMap"], [18, 48, 16, 0], [19, 2, 17, 0], [19, 6, 17, 0, "_JsiSkPoint"], [19, 17, 17, 0], [19, 20, 17, 0, "require"], [19, 27, 17, 0], [19, 28, 17, 0, "_dependencyMap"], [19, 42, 17, 0], [20, 2, 18, 0], [20, 6, 18, 0, "_JsiSkRSXform"], [20, 19, 18, 0], [20, 22, 18, 0, "require"], [20, 29, 18, 0], [20, 30, 18, 0, "_dependencyMap"], [20, 44, 18, 0], [21, 2, 1, 0], [21, 11, 1, 9, "_defineProperty"], [21, 26, 1, 24, "_defineProperty"], [21, 27, 1, 25, "e"], [21, 28, 1, 26], [21, 30, 1, 28, "r"], [21, 31, 1, 29], [21, 33, 1, 31, "t"], [21, 34, 1, 32], [21, 36, 1, 34], [22, 4, 1, 36], [22, 11, 1, 43], [22, 12, 1, 44, "r"], [22, 13, 1, 45], [22, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [22, 30, 1, 62], [22, 31, 1, 63, "r"], [22, 32, 1, 64], [22, 33, 1, 65], [22, 38, 1, 70, "e"], [22, 39, 1, 71], [22, 42, 1, 74, "Object"], [22, 48, 1, 80], [22, 49, 1, 81, "defineProperty"], [22, 63, 1, 95], [22, 64, 1, 96, "e"], [22, 65, 1, 97], [22, 67, 1, 99, "r"], [22, 68, 1, 100], [22, 70, 1, 102], [23, 6, 1, 104, "value"], [23, 11, 1, 109], [23, 13, 1, 111, "t"], [23, 14, 1, 112], [24, 6, 1, 114, "enumerable"], [24, 16, 1, 124], [24, 18, 1, 126], [24, 19, 1, 127], [24, 20, 1, 128], [25, 6, 1, 130, "configurable"], [25, 18, 1, 142], [25, 20, 1, 144], [25, 21, 1, 145], [25, 22, 1, 146], [26, 6, 1, 148, "writable"], [26, 14, 1, 156], [26, 16, 1, 158], [26, 17, 1, 159], [27, 4, 1, 161], [27, 5, 1, 162], [27, 6, 1, 163], [27, 9, 1, 166, "e"], [27, 10, 1, 167], [27, 11, 1, 168, "r"], [27, 12, 1, 169], [27, 13, 1, 170], [27, 16, 1, 173, "t"], [27, 17, 1, 174], [27, 19, 1, 176, "e"], [27, 20, 1, 177], [28, 2, 1, 179], [29, 2, 2, 0], [29, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [29, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [29, 26, 2, 24, "t"], [29, 27, 2, 25], [29, 29, 2, 27], [30, 4, 2, 29], [30, 8, 2, 33, "i"], [30, 9, 2, 34], [30, 12, 2, 37, "_toPrimitive"], [30, 24, 2, 49], [30, 25, 2, 50, "t"], [30, 26, 2, 51], [30, 28, 2, 53], [30, 36, 2, 61], [30, 37, 2, 62], [31, 4, 2, 64], [31, 11, 2, 71], [31, 19, 2, 79], [31, 23, 2, 83], [31, 30, 2, 90, "i"], [31, 31, 2, 91], [31, 34, 2, 94, "i"], [31, 35, 2, 95], [31, 38, 2, 98, "i"], [31, 39, 2, 99], [31, 42, 2, 102], [31, 44, 2, 104], [32, 2, 2, 106], [33, 2, 3, 0], [33, 11, 3, 9, "_toPrimitive"], [33, 23, 3, 21, "_toPrimitive"], [33, 24, 3, 22, "t"], [33, 25, 3, 23], [33, 27, 3, 25, "r"], [33, 28, 3, 26], [33, 30, 3, 28], [34, 4, 3, 30], [34, 8, 3, 34], [34, 16, 3, 42], [34, 20, 3, 46], [34, 27, 3, 53, "t"], [34, 28, 3, 54], [34, 32, 3, 58], [34, 33, 3, 59, "t"], [34, 34, 3, 60], [34, 36, 3, 62], [34, 43, 3, 69, "t"], [34, 44, 3, 70], [35, 4, 3, 72], [35, 8, 3, 76, "e"], [35, 9, 3, 77], [35, 12, 3, 80, "t"], [35, 13, 3, 81], [35, 14, 3, 82, "Symbol"], [35, 20, 3, 88], [35, 21, 3, 89, "toPrimitive"], [35, 32, 3, 100], [35, 33, 3, 101], [36, 4, 3, 103], [36, 8, 3, 107], [36, 13, 3, 112], [36, 14, 3, 113], [36, 19, 3, 118, "e"], [36, 20, 3, 119], [36, 22, 3, 121], [37, 6, 3, 123], [37, 10, 3, 127, "i"], [37, 11, 3, 128], [37, 14, 3, 131, "e"], [37, 15, 3, 132], [37, 16, 3, 133, "call"], [37, 20, 3, 137], [37, 21, 3, 138, "t"], [37, 22, 3, 139], [37, 24, 3, 141, "r"], [37, 25, 3, 142], [37, 29, 3, 146], [37, 38, 3, 155], [37, 39, 3, 156], [38, 6, 3, 158], [38, 10, 3, 162], [38, 18, 3, 170], [38, 22, 3, 174], [38, 29, 3, 181, "i"], [38, 30, 3, 182], [38, 32, 3, 184], [38, 39, 3, 191, "i"], [38, 40, 3, 192], [39, 6, 3, 194], [39, 12, 3, 200], [39, 16, 3, 204, "TypeError"], [39, 25, 3, 213], [39, 26, 3, 214], [39, 72, 3, 260], [39, 73, 3, 261], [40, 4, 3, 263], [41, 4, 3, 265], [41, 11, 3, 272], [41, 12, 3, 273], [41, 20, 3, 281], [41, 25, 3, 286, "r"], [41, 26, 3, 287], [41, 29, 3, 290, "String"], [41, 35, 3, 296], [41, 38, 3, 299, "Number"], [41, 44, 3, 305], [41, 46, 3, 307, "t"], [41, 47, 3, 308], [41, 48, 3, 309], [42, 2, 3, 311], [43, 2, 19, 7], [43, 8, 19, 13, "JsiSkCanvas"], [43, 19, 19, 24], [43, 28, 19, 33, "HostObject"], [43, 44, 19, 43], [43, 45, 19, 44], [44, 4, 20, 2, "constructor"], [44, 15, 20, 13, "constructor"], [44, 16, 20, 14, "CanvasKit"], [44, 25, 20, 23], [44, 27, 20, 25, "ref"], [44, 30, 20, 28], [44, 32, 20, 30], [45, 6, 21, 4], [45, 11, 21, 9], [45, 12, 21, 10, "CanvasKit"], [45, 21, 21, 19], [45, 23, 21, 21, "ref"], [45, 26, 21, 24], [45, 28, 21, 26], [45, 36, 21, 34], [45, 37, 21, 35], [46, 6, 22, 4, "_defineProperty"], [46, 21, 22, 19], [46, 22, 22, 20], [46, 26, 22, 24], [46, 28, 22, 26], [46, 37, 22, 35], [46, 39, 22, 37], [46, 45, 22, 43], [47, 8, 23, 6], [47, 12, 23, 10], [47, 13, 23, 11, "ref"], [47, 16, 23, 14], [47, 17, 23, 15, "delete"], [47, 23, 23, 21], [47, 24, 23, 22], [47, 25, 23, 23], [48, 6, 24, 4], [48, 7, 24, 5], [48, 8, 24, 6], [49, 4, 25, 2], [50, 4, 26, 2, "drawRect"], [50, 12, 26, 10, "drawRect"], [50, 13, 26, 11, "rect"], [50, 17, 26, 15], [50, 19, 26, 17, "paint"], [50, 24, 26, 22], [50, 26, 26, 24], [51, 6, 27, 4], [51, 10, 27, 8], [51, 11, 27, 9, "ref"], [51, 14, 27, 12], [51, 15, 27, 13, "drawRect"], [51, 23, 27, 21], [51, 24, 27, 22, "JsiSkRect"], [51, 44, 27, 31], [51, 45, 27, 32, "fromValue"], [51, 54, 27, 41], [51, 55, 27, 42], [51, 59, 27, 46], [51, 60, 27, 47, "CanvasKit"], [51, 69, 27, 56], [51, 71, 27, 58, "rect"], [51, 75, 27, 62], [51, 76, 27, 63], [51, 78, 27, 65, "JsiSkPaint"], [51, 100, 27, 75], [51, 101, 27, 76, "fromValue"], [51, 110, 27, 85], [51, 111, 27, 86, "paint"], [51, 116, 27, 91], [51, 117, 27, 92], [51, 118, 27, 93], [52, 4, 28, 2], [53, 4, 29, 2, "drawImage"], [53, 13, 29, 11, "drawImage"], [53, 14, 29, 12, "image"], [53, 19, 29, 17], [53, 21, 29, 19, "x"], [53, 22, 29, 20], [53, 24, 29, 22, "y"], [53, 25, 29, 23], [53, 27, 29, 25, "paint"], [53, 32, 29, 30], [53, 34, 29, 32], [54, 6, 30, 4], [54, 10, 30, 8], [54, 11, 30, 9, "ref"], [54, 14, 30, 12], [54, 15, 30, 13, "drawImage"], [54, 24, 30, 22], [54, 25, 30, 23, "JsiSkImage"], [54, 47, 30, 33], [54, 48, 30, 34, "fromValue"], [54, 57, 30, 43], [54, 58, 30, 44, "image"], [54, 63, 30, 49], [54, 64, 30, 50], [54, 66, 30, 52, "x"], [54, 67, 30, 53], [54, 69, 30, 55, "y"], [54, 70, 30, 56], [54, 72, 30, 58, "paint"], [54, 77, 30, 63], [54, 80, 30, 66, "JsiSkPaint"], [54, 102, 30, 76], [54, 103, 30, 77, "fromValue"], [54, 112, 30, 86], [54, 113, 30, 87, "paint"], [54, 118, 30, 92], [54, 119, 30, 93], [54, 122, 30, 96, "paint"], [54, 127, 30, 101], [54, 128, 30, 102], [55, 4, 31, 2], [56, 4, 32, 2, "drawImageRect"], [56, 17, 32, 15, "drawImageRect"], [56, 18, 32, 16, "img"], [56, 21, 32, 19], [56, 23, 32, 21, "src"], [56, 26, 32, 24], [56, 28, 32, 26, "dest"], [56, 32, 32, 30], [56, 34, 32, 32, "paint"], [56, 39, 32, 37], [56, 41, 32, 39, "fastSample"], [56, 51, 32, 49], [56, 53, 32, 51], [57, 6, 33, 4], [57, 10, 33, 8], [57, 11, 33, 9, "ref"], [57, 14, 33, 12], [57, 15, 33, 13, "drawImageRect"], [57, 28, 33, 26], [57, 29, 33, 27, "JsiSkImage"], [57, 51, 33, 37], [57, 52, 33, 38, "fromValue"], [57, 61, 33, 47], [57, 62, 33, 48, "img"], [57, 65, 33, 51], [57, 66, 33, 52], [57, 68, 33, 54, "JsiSkRect"], [57, 88, 33, 63], [57, 89, 33, 64, "fromValue"], [57, 98, 33, 73], [57, 99, 33, 74], [57, 103, 33, 78], [57, 104, 33, 79, "CanvasKit"], [57, 113, 33, 88], [57, 115, 33, 90, "src"], [57, 118, 33, 93], [57, 119, 33, 94], [57, 121, 33, 96, "JsiSkRect"], [57, 141, 33, 105], [57, 142, 33, 106, "fromValue"], [57, 151, 33, 115], [57, 152, 33, 116], [57, 156, 33, 120], [57, 157, 33, 121, "CanvasKit"], [57, 166, 33, 130], [57, 168, 33, 132, "dest"], [57, 172, 33, 136], [57, 173, 33, 137], [57, 175, 33, 139, "JsiSkPaint"], [57, 197, 33, 149], [57, 198, 33, 150, "fromValue"], [57, 207, 33, 159], [57, 208, 33, 160, "paint"], [57, 213, 33, 165], [57, 214, 33, 166], [57, 216, 33, 168, "fastSample"], [57, 226, 33, 178], [57, 227, 33, 179], [58, 4, 34, 2], [59, 4, 35, 2, "drawImageCubic"], [59, 18, 35, 16, "drawImageCubic"], [59, 19, 35, 17, "img"], [59, 22, 35, 20], [59, 24, 35, 22, "left"], [59, 28, 35, 26], [59, 30, 35, 28, "top"], [59, 33, 35, 31], [59, 35, 35, 33, "B"], [59, 36, 35, 34], [59, 38, 35, 36, "C"], [59, 39, 35, 37], [59, 41, 35, 39, "paint"], [59, 46, 35, 44], [59, 48, 35, 46], [60, 6, 36, 4], [60, 10, 36, 8], [60, 11, 36, 9, "ref"], [60, 14, 36, 12], [60, 15, 36, 13, "drawImageCubic"], [60, 29, 36, 27], [60, 30, 36, 28, "JsiSkImage"], [60, 52, 36, 38], [60, 53, 36, 39, "fromValue"], [60, 62, 36, 48], [60, 63, 36, 49, "img"], [60, 66, 36, 52], [60, 67, 36, 53], [60, 69, 36, 55, "left"], [60, 73, 36, 59], [60, 75, 36, 61, "top"], [60, 78, 36, 64], [60, 80, 36, 66, "B"], [60, 81, 36, 67], [60, 83, 36, 69, "C"], [60, 84, 36, 70], [60, 86, 36, 72, "paint"], [60, 91, 36, 77], [60, 94, 36, 80, "JsiSkPaint"], [60, 116, 36, 90], [60, 117, 36, 91, "fromValue"], [60, 126, 36, 100], [60, 127, 36, 101, "paint"], [60, 132, 36, 106], [60, 133, 36, 107], [60, 136, 36, 110, "paint"], [60, 141, 36, 115], [60, 142, 36, 116], [61, 4, 37, 2], [62, 4, 38, 2, "drawImageOptions"], [62, 20, 38, 18, "drawImageOptions"], [62, 21, 38, 19, "img"], [62, 24, 38, 22], [62, 26, 38, 24, "left"], [62, 30, 38, 28], [62, 32, 38, 30, "top"], [62, 35, 38, 33], [62, 37, 38, 35, "fm"], [62, 39, 38, 37], [62, 41, 38, 39, "mm"], [62, 43, 38, 41], [62, 45, 38, 43, "paint"], [62, 50, 38, 48], [62, 52, 38, 50], [63, 6, 39, 4], [63, 10, 39, 8], [63, 11, 39, 9, "ref"], [63, 14, 39, 12], [63, 15, 39, 13, "drawImageOptions"], [63, 31, 39, 29], [63, 32, 39, 30, "JsiSkImage"], [63, 54, 39, 40], [63, 55, 39, 41, "fromValue"], [63, 64, 39, 50], [63, 65, 39, 51, "img"], [63, 68, 39, 54], [63, 69, 39, 55], [63, 71, 39, 57, "left"], [63, 75, 39, 61], [63, 77, 39, 63, "top"], [63, 80, 39, 66], [63, 82, 39, 68], [63, 86, 39, 68, "getEnum"], [63, 99, 39, 75], [63, 101, 39, 76], [63, 105, 39, 80], [63, 106, 39, 81, "CanvasKit"], [63, 115, 39, 90], [63, 117, 39, 92], [63, 129, 39, 104], [63, 131, 39, 106, "fm"], [63, 133, 39, 108], [63, 134, 39, 109], [63, 136, 39, 111], [63, 140, 39, 111, "getEnum"], [63, 153, 39, 118], [63, 155, 39, 119], [63, 159, 39, 123], [63, 160, 39, 124, "CanvasKit"], [63, 169, 39, 133], [63, 171, 39, 135], [63, 183, 39, 147], [63, 185, 39, 149, "mm"], [63, 187, 39, 151], [63, 188, 39, 152], [63, 190, 39, 154, "paint"], [63, 195, 39, 159], [63, 198, 39, 162, "JsiSkPaint"], [63, 220, 39, 172], [63, 221, 39, 173, "fromValue"], [63, 230, 39, 182], [63, 231, 39, 183, "paint"], [63, 236, 39, 188], [63, 237, 39, 189], [63, 240, 39, 192, "paint"], [63, 245, 39, 197], [63, 246, 39, 198], [64, 4, 40, 2], [65, 4, 41, 2, "drawImageNine"], [65, 17, 41, 15, "drawImageNine"], [65, 18, 41, 16, "img"], [65, 21, 41, 19], [65, 23, 41, 21, "center"], [65, 29, 41, 27], [65, 31, 41, 29, "dest"], [65, 35, 41, 33], [65, 37, 41, 35, "filter"], [65, 43, 41, 41], [65, 45, 41, 43, "paint"], [65, 50, 41, 48], [65, 52, 41, 50], [66, 6, 42, 4], [66, 10, 42, 8], [66, 11, 42, 9, "ref"], [66, 14, 42, 12], [66, 15, 42, 13, "drawImageNine"], [66, 28, 42, 26], [66, 29, 42, 27, "JsiSkImage"], [66, 51, 42, 37], [66, 52, 42, 38, "fromValue"], [66, 61, 42, 47], [66, 62, 42, 48, "img"], [66, 65, 42, 51], [66, 66, 42, 52], [66, 68, 42, 54, "Array"], [66, 73, 42, 59], [66, 74, 42, 60, "from"], [66, 78, 42, 64], [66, 79, 42, 65, "JsiSkRect"], [66, 99, 42, 74], [66, 100, 42, 75, "fromValue"], [66, 109, 42, 84], [66, 110, 42, 85], [66, 114, 42, 89], [66, 115, 42, 90, "CanvasKit"], [66, 124, 42, 99], [66, 126, 42, 101, "center"], [66, 132, 42, 107], [66, 133, 42, 108], [66, 134, 42, 109], [66, 136, 42, 111, "JsiSkRect"], [66, 156, 42, 120], [66, 157, 42, 121, "fromValue"], [66, 166, 42, 130], [66, 167, 42, 131], [66, 171, 42, 135], [66, 172, 42, 136, "CanvasKit"], [66, 181, 42, 145], [66, 183, 42, 147, "dest"], [66, 187, 42, 151], [66, 188, 42, 152], [66, 190, 42, 154], [66, 194, 42, 154, "getEnum"], [66, 207, 42, 161], [66, 209, 42, 162], [66, 213, 42, 166], [66, 214, 42, 167, "CanvasKit"], [66, 223, 42, 176], [66, 225, 42, 178], [66, 237, 42, 190], [66, 239, 42, 192, "filter"], [66, 245, 42, 198], [66, 246, 42, 199], [66, 248, 42, 201, "paint"], [66, 253, 42, 206], [66, 256, 42, 209, "JsiSkPaint"], [66, 278, 42, 219], [66, 279, 42, 220, "fromValue"], [66, 288, 42, 229], [66, 289, 42, 230, "paint"], [66, 294, 42, 235], [66, 295, 42, 236], [66, 298, 42, 239, "paint"], [66, 303, 42, 244], [66, 304, 42, 245], [67, 4, 43, 2], [68, 4, 44, 2, "drawImageRectCubic"], [68, 22, 44, 20, "drawImageRectCubic"], [68, 23, 44, 21, "img"], [68, 26, 44, 24], [68, 28, 44, 26, "src"], [68, 31, 44, 29], [68, 33, 44, 31, "dest"], [68, 37, 44, 35], [68, 39, 44, 37, "B"], [68, 40, 44, 38], [68, 42, 44, 40, "C"], [68, 43, 44, 41], [68, 45, 44, 43, "paint"], [68, 50, 44, 48], [68, 52, 44, 50], [69, 6, 45, 4], [69, 10, 45, 8], [69, 11, 45, 9, "ref"], [69, 14, 45, 12], [69, 15, 45, 13, "drawImageRectCubic"], [69, 33, 45, 31], [69, 34, 45, 32, "JsiSkImage"], [69, 56, 45, 42], [69, 57, 45, 43, "fromValue"], [69, 66, 45, 52], [69, 67, 45, 53, "img"], [69, 70, 45, 56], [69, 71, 45, 57], [69, 73, 45, 59, "JsiSkRect"], [69, 93, 45, 68], [69, 94, 45, 69, "fromValue"], [69, 103, 45, 78], [69, 104, 45, 79], [69, 108, 45, 83], [69, 109, 45, 84, "CanvasKit"], [69, 118, 45, 93], [69, 120, 45, 95, "src"], [69, 123, 45, 98], [69, 124, 45, 99], [69, 126, 45, 101, "JsiSkRect"], [69, 146, 45, 110], [69, 147, 45, 111, "fromValue"], [69, 156, 45, 120], [69, 157, 45, 121], [69, 161, 45, 125], [69, 162, 45, 126, "CanvasKit"], [69, 171, 45, 135], [69, 173, 45, 137, "dest"], [69, 177, 45, 141], [69, 178, 45, 142], [69, 180, 45, 144, "B"], [69, 181, 45, 145], [69, 183, 45, 147, "C"], [69, 184, 45, 148], [69, 186, 45, 150, "paint"], [69, 191, 45, 155], [69, 194, 45, 158, "JsiSkPaint"], [69, 216, 45, 168], [69, 217, 45, 169, "fromValue"], [69, 226, 45, 178], [69, 227, 45, 179, "paint"], [69, 232, 45, 184], [69, 233, 45, 185], [69, 236, 45, 188, "paint"], [69, 241, 45, 193], [69, 242, 45, 194], [70, 4, 46, 2], [71, 4, 47, 2, "drawImageRectOptions"], [71, 24, 47, 22, "drawImageRectOptions"], [71, 25, 47, 23, "img"], [71, 28, 47, 26], [71, 30, 47, 28, "src"], [71, 33, 47, 31], [71, 35, 47, 33, "dest"], [71, 39, 47, 37], [71, 41, 47, 39, "fm"], [71, 43, 47, 41], [71, 45, 47, 43, "mm"], [71, 47, 47, 45], [71, 49, 47, 47, "paint"], [71, 54, 47, 52], [71, 56, 47, 54], [72, 6, 48, 4], [72, 10, 48, 8], [72, 11, 48, 9, "ref"], [72, 14, 48, 12], [72, 15, 48, 13, "drawImageRectOptions"], [72, 35, 48, 33], [72, 36, 48, 34, "JsiSkImage"], [72, 58, 48, 44], [72, 59, 48, 45, "fromValue"], [72, 68, 48, 54], [72, 69, 48, 55, "img"], [72, 72, 48, 58], [72, 73, 48, 59], [72, 75, 48, 61, "JsiSkRect"], [72, 95, 48, 70], [72, 96, 48, 71, "fromValue"], [72, 105, 48, 80], [72, 106, 48, 81], [72, 110, 48, 85], [72, 111, 48, 86, "CanvasKit"], [72, 120, 48, 95], [72, 122, 48, 97, "src"], [72, 125, 48, 100], [72, 126, 48, 101], [72, 128, 48, 103, "JsiSkRect"], [72, 148, 48, 112], [72, 149, 48, 113, "fromValue"], [72, 158, 48, 122], [72, 159, 48, 123], [72, 163, 48, 127], [72, 164, 48, 128, "CanvasKit"], [72, 173, 48, 137], [72, 175, 48, 139, "dest"], [72, 179, 48, 143], [72, 180, 48, 144], [72, 182, 48, 146], [72, 186, 48, 146, "getEnum"], [72, 199, 48, 153], [72, 201, 48, 154], [72, 205, 48, 158], [72, 206, 48, 159, "CanvasKit"], [72, 215, 48, 168], [72, 217, 48, 170], [72, 229, 48, 182], [72, 231, 48, 184, "fm"], [72, 233, 48, 186], [72, 234, 48, 187], [72, 236, 48, 189], [72, 240, 48, 189, "getEnum"], [72, 253, 48, 196], [72, 255, 48, 197], [72, 259, 48, 201], [72, 260, 48, 202, "CanvasKit"], [72, 269, 48, 211], [72, 271, 48, 213], [72, 283, 48, 225], [72, 285, 48, 227, "mm"], [72, 287, 48, 229], [72, 288, 48, 230], [72, 290, 48, 232, "paint"], [72, 295, 48, 237], [72, 298, 48, 240, "JsiSkPaint"], [72, 320, 48, 250], [72, 321, 48, 251, "fromValue"], [72, 330, 48, 260], [72, 331, 48, 261, "paint"], [72, 336, 48, 266], [72, 337, 48, 267], [72, 340, 48, 270, "paint"], [72, 345, 48, 275], [72, 346, 48, 276], [73, 4, 49, 2], [74, 4, 50, 2, "<PERSON><PERSON><PERSON><PERSON>"], [74, 13, 50, 11, "<PERSON><PERSON><PERSON><PERSON>"], [74, 14, 50, 12, "paint"], [74, 19, 50, 17], [74, 21, 50, 19], [75, 6, 51, 4], [75, 10, 51, 8], [75, 11, 51, 9, "ref"], [75, 14, 51, 12], [75, 15, 51, 13, "<PERSON><PERSON><PERSON><PERSON>"], [75, 24, 51, 22], [75, 25, 51, 23, "JsiSkPaint"], [75, 47, 51, 33], [75, 48, 51, 34, "fromValue"], [75, 57, 51, 43], [75, 58, 51, 44, "paint"], [75, 63, 51, 49], [75, 64, 51, 50], [75, 65, 51, 51], [76, 4, 52, 2], [77, 4, 53, 2, "drawLine"], [77, 12, 53, 10, "drawLine"], [77, 13, 53, 11, "x0"], [77, 15, 53, 13], [77, 17, 53, 15, "y0"], [77, 19, 53, 17], [77, 21, 53, 19, "x1"], [77, 23, 53, 21], [77, 25, 53, 23, "y1"], [77, 27, 53, 25], [77, 29, 53, 27, "paint"], [77, 34, 53, 32], [77, 36, 53, 34], [78, 6, 54, 4], [78, 10, 54, 8], [78, 11, 54, 9, "ref"], [78, 14, 54, 12], [78, 15, 54, 13, "drawLine"], [78, 23, 54, 21], [78, 24, 54, 22, "x0"], [78, 26, 54, 24], [78, 28, 54, 26, "y0"], [78, 30, 54, 28], [78, 32, 54, 30, "x1"], [78, 34, 54, 32], [78, 36, 54, 34, "y1"], [78, 38, 54, 36], [78, 40, 54, 38, "JsiSkPaint"], [78, 62, 54, 48], [78, 63, 54, 49, "fromValue"], [78, 72, 54, 58], [78, 73, 54, 59, "paint"], [78, 78, 54, 64], [78, 79, 54, 65], [78, 80, 54, 66], [79, 4, 55, 2], [80, 4, 56, 2, "drawCircle"], [80, 14, 56, 12, "drawCircle"], [80, 15, 56, 13, "cx"], [80, 17, 56, 15], [80, 19, 56, 17, "cy"], [80, 21, 56, 19], [80, 23, 56, 21, "radius"], [80, 29, 56, 27], [80, 31, 56, 29, "paint"], [80, 36, 56, 34], [80, 38, 56, 36], [81, 6, 57, 4], [81, 10, 57, 8], [81, 11, 57, 9, "ref"], [81, 14, 57, 12], [81, 15, 57, 13, "drawCircle"], [81, 25, 57, 23], [81, 26, 57, 24, "cx"], [81, 28, 57, 26], [81, 30, 57, 28, "cy"], [81, 32, 57, 30], [81, 34, 57, 32, "radius"], [81, 40, 57, 38], [81, 42, 57, 40, "JsiSkPaint"], [81, 64, 57, 50], [81, 65, 57, 51, "fromValue"], [81, 74, 57, 60], [81, 75, 57, 61, "paint"], [81, 80, 57, 66], [81, 81, 57, 67], [81, 82, 57, 68], [82, 4, 58, 2], [83, 4, 59, 2, "drawVertices"], [83, 16, 59, 14, "drawVertices"], [83, 17, 59, 15, "verts"], [83, 22, 59, 20], [83, 24, 59, 22, "mode"], [83, 28, 59, 26], [83, 30, 59, 28, "paint"], [83, 35, 59, 33], [83, 37, 59, 35], [84, 6, 60, 4], [84, 10, 60, 8], [84, 11, 60, 9, "ref"], [84, 14, 60, 12], [84, 15, 60, 13, "drawVertices"], [84, 27, 60, 25], [84, 28, 60, 26, "JsiSkVertices"], [84, 56, 60, 39], [84, 57, 60, 40, "fromValue"], [84, 66, 60, 49], [84, 67, 60, 50, "verts"], [84, 72, 60, 55], [84, 73, 60, 56], [84, 75, 60, 58], [84, 79, 60, 58, "getEnum"], [84, 92, 60, 65], [84, 94, 60, 66], [84, 98, 60, 70], [84, 99, 60, 71, "CanvasKit"], [84, 108, 60, 80], [84, 110, 60, 82], [84, 121, 60, 93], [84, 123, 60, 95, "mode"], [84, 127, 60, 99], [84, 128, 60, 100], [84, 130, 60, 102, "JsiSkPaint"], [84, 152, 60, 112], [84, 153, 60, 113, "fromValue"], [84, 162, 60, 122], [84, 163, 60, 123, "paint"], [84, 168, 60, 128], [84, 169, 60, 129], [84, 170, 60, 130], [85, 4, 61, 2], [86, 4, 62, 2, "drawPatch"], [86, 13, 62, 11, "drawPatch"], [86, 14, 62, 12, "cubics"], [86, 20, 62, 18], [86, 22, 62, 20, "colors"], [86, 28, 62, 26], [86, 30, 62, 28, "texs"], [86, 34, 62, 32], [86, 36, 62, 34, "mode"], [86, 40, 62, 38], [86, 42, 62, 40, "paint"], [86, 47, 62, 45], [86, 49, 62, 47], [87, 6, 63, 4], [87, 10, 63, 8], [87, 11, 63, 9, "ref"], [87, 14, 63, 12], [87, 15, 63, 13, "drawPatch"], [87, 24, 63, 22], [87, 25, 63, 23, "cubics"], [87, 31, 63, 29], [87, 32, 63, 30, "map"], [87, 35, 63, 33], [87, 36, 63, 34], [87, 37, 63, 35], [88, 8, 64, 6, "x"], [88, 9, 64, 7], [89, 8, 65, 6, "y"], [90, 6, 66, 4], [90, 7, 66, 5], [90, 12, 66, 10], [90, 13, 66, 11, "x"], [90, 14, 66, 12], [90, 16, 66, 14, "y"], [90, 17, 66, 15], [90, 18, 66, 16], [90, 19, 66, 17], [90, 20, 66, 18, "flat"], [90, 24, 66, 22], [90, 25, 66, 23], [90, 26, 66, 24], [90, 28, 66, 26, "colors"], [90, 34, 66, 32], [90, 36, 66, 34, "texs"], [90, 40, 66, 38], [90, 43, 66, 41, "texs"], [90, 47, 66, 45], [90, 48, 66, 46, "flatMap"], [90, 55, 66, 53], [90, 56, 66, 54, "p"], [90, 57, 66, 55], [90, 61, 66, 59, "Array"], [90, 66, 66, 64], [90, 67, 66, 65, "from"], [90, 71, 66, 69], [90, 72, 66, 70, "JsiSkPoint"], [90, 94, 66, 80], [90, 95, 66, 81, "fromValue"], [90, 104, 66, 90], [90, 105, 66, 91, "p"], [90, 106, 66, 92], [90, 107, 66, 93], [90, 108, 66, 94], [90, 109, 66, 95], [90, 112, 66, 98, "texs"], [90, 116, 66, 102], [90, 118, 66, 104, "mode"], [90, 122, 66, 108], [90, 125, 66, 111], [90, 129, 66, 111, "getEnum"], [90, 142, 66, 118], [90, 144, 66, 119], [90, 148, 66, 123], [90, 149, 66, 124, "CanvasKit"], [90, 158, 66, 133], [90, 160, 66, 135], [90, 171, 66, 146], [90, 173, 66, 148, "mode"], [90, 177, 66, 152], [90, 178, 66, 153], [90, 181, 66, 156], [90, 185, 66, 160], [90, 187, 66, 162, "paint"], [90, 192, 66, 167], [90, 195, 66, 170, "JsiSkPaint"], [90, 217, 66, 180], [90, 218, 66, 181, "fromValue"], [90, 227, 66, 190], [90, 228, 66, 191, "paint"], [90, 233, 66, 196], [90, 234, 66, 197], [90, 237, 66, 200, "undefined"], [90, 246, 66, 209], [90, 247, 66, 210], [91, 4, 67, 2], [92, 4, 68, 2, "restoreToCount"], [92, 18, 68, 16, "restoreToCount"], [92, 19, 68, 17, "saveCount"], [92, 28, 68, 26], [92, 30, 68, 28], [93, 6, 69, 4], [93, 10, 69, 8], [93, 11, 69, 9, "ref"], [93, 14, 69, 12], [93, 15, 69, 13, "restoreToCount"], [93, 29, 69, 27], [93, 30, 69, 28, "saveCount"], [93, 39, 69, 37], [93, 40, 69, 38], [94, 4, 70, 2], [95, 4, 71, 2, "drawPoints"], [95, 14, 71, 12, "drawPoints"], [95, 15, 71, 13, "mode"], [95, 19, 71, 17], [95, 21, 71, 19, "points"], [95, 27, 71, 25], [95, 29, 71, 27, "paint"], [95, 34, 71, 32], [95, 36, 71, 34], [96, 6, 72, 4], [96, 10, 72, 8], [96, 11, 72, 9, "ref"], [96, 14, 72, 12], [96, 15, 72, 13, "drawPoints"], [96, 25, 72, 23], [96, 26, 72, 24], [96, 30, 72, 24, "getEnum"], [96, 43, 72, 31], [96, 45, 72, 32], [96, 49, 72, 36], [96, 50, 72, 37, "CanvasKit"], [96, 59, 72, 46], [96, 61, 72, 48], [96, 72, 72, 59], [96, 74, 72, 61, "mode"], [96, 78, 72, 65], [96, 79, 72, 66], [96, 81, 72, 68, "points"], [96, 87, 72, 74], [96, 88, 72, 75, "map"], [96, 91, 72, 78], [96, 92, 72, 79], [96, 93, 72, 80], [97, 8, 73, 6, "x"], [97, 9, 73, 7], [98, 8, 74, 6, "y"], [99, 6, 75, 4], [99, 7, 75, 5], [99, 12, 75, 10], [99, 13, 75, 11, "x"], [99, 14, 75, 12], [99, 16, 75, 14, "y"], [99, 17, 75, 15], [99, 18, 75, 16], [99, 19, 75, 17], [99, 20, 75, 18, "flat"], [99, 24, 75, 22], [99, 25, 75, 23], [99, 26, 75, 24], [99, 28, 75, 26, "JsiSkPaint"], [99, 50, 75, 36], [99, 51, 75, 37, "fromValue"], [99, 60, 75, 46], [99, 61, 75, 47, "paint"], [99, 66, 75, 52], [99, 67, 75, 53], [99, 68, 75, 54], [100, 4, 76, 2], [101, 4, 77, 2, "drawArc"], [101, 11, 77, 9, "drawArc"], [101, 12, 77, 10, "oval"], [101, 16, 77, 14], [101, 18, 77, 16, "startAngle"], [101, 28, 77, 26], [101, 30, 77, 28, "sweepAngle"], [101, 40, 77, 38], [101, 42, 77, 40, "useCenter"], [101, 51, 77, 49], [101, 53, 77, 51, "paint"], [101, 58, 77, 56], [101, 60, 77, 58], [102, 6, 78, 4], [102, 10, 78, 8], [102, 11, 78, 9, "ref"], [102, 14, 78, 12], [102, 15, 78, 13, "drawArc"], [102, 22, 78, 20], [102, 23, 78, 21, "JsiSkRect"], [102, 43, 78, 30], [102, 44, 78, 31, "fromValue"], [102, 53, 78, 40], [102, 54, 78, 41], [102, 58, 78, 45], [102, 59, 78, 46, "CanvasKit"], [102, 68, 78, 55], [102, 70, 78, 57, "oval"], [102, 74, 78, 61], [102, 75, 78, 62], [102, 77, 78, 64, "startAngle"], [102, 87, 78, 74], [102, 89, 78, 76, "sweepAngle"], [102, 99, 78, 86], [102, 101, 78, 88, "useCenter"], [102, 110, 78, 97], [102, 112, 78, 99, "JsiSkPaint"], [102, 134, 78, 109], [102, 135, 78, 110, "fromValue"], [102, 144, 78, 119], [102, 145, 78, 120, "paint"], [102, 150, 78, 125], [102, 151, 78, 126], [102, 152, 78, 127], [103, 4, 79, 2], [104, 4, 80, 2, "drawRRect"], [104, 13, 80, 11, "drawRRect"], [104, 14, 80, 12, "rrect"], [104, 19, 80, 17], [104, 21, 80, 19, "paint"], [104, 26, 80, 24], [104, 28, 80, 26], [105, 6, 81, 4], [105, 10, 81, 8], [105, 11, 81, 9, "ref"], [105, 14, 81, 12], [105, 15, 81, 13, "drawRRect"], [105, 24, 81, 22], [105, 25, 81, 23, "JsiSkRRect"], [105, 47, 81, 33], [105, 48, 81, 34, "fromValue"], [105, 57, 81, 43], [105, 58, 81, 44], [105, 62, 81, 48], [105, 63, 81, 49, "CanvasKit"], [105, 72, 81, 58], [105, 74, 81, 60, "rrect"], [105, 79, 81, 65], [105, 80, 81, 66], [105, 82, 81, 68, "JsiSkPaint"], [105, 104, 81, 78], [105, 105, 81, 79, "fromValue"], [105, 114, 81, 88], [105, 115, 81, 89, "paint"], [105, 120, 81, 94], [105, 121, 81, 95], [105, 122, 81, 96], [106, 4, 82, 2], [107, 4, 83, 2, "drawDRRect"], [107, 14, 83, 12, "drawDRRect"], [107, 15, 83, 13, "outer"], [107, 20, 83, 18], [107, 22, 83, 20, "inner"], [107, 27, 83, 25], [107, 29, 83, 27, "paint"], [107, 34, 83, 32], [107, 36, 83, 34], [108, 6, 84, 4], [108, 10, 84, 8], [108, 11, 84, 9, "ref"], [108, 14, 84, 12], [108, 15, 84, 13, "drawDRRect"], [108, 25, 84, 23], [108, 26, 84, 24, "JsiSkRRect"], [108, 48, 84, 34], [108, 49, 84, 35, "fromValue"], [108, 58, 84, 44], [108, 59, 84, 45], [108, 63, 84, 49], [108, 64, 84, 50, "CanvasKit"], [108, 73, 84, 59], [108, 75, 84, 61, "outer"], [108, 80, 84, 66], [108, 81, 84, 67], [108, 83, 84, 69, "JsiSkRRect"], [108, 105, 84, 79], [108, 106, 84, 80, "fromValue"], [108, 115, 84, 89], [108, 116, 84, 90], [108, 120, 84, 94], [108, 121, 84, 95, "CanvasKit"], [108, 130, 84, 104], [108, 132, 84, 106, "inner"], [108, 137, 84, 111], [108, 138, 84, 112], [108, 140, 84, 114, "JsiSkPaint"], [108, 162, 84, 124], [108, 163, 84, 125, "fromValue"], [108, 172, 84, 134], [108, 173, 84, 135, "paint"], [108, 178, 84, 140], [108, 179, 84, 141], [108, 180, 84, 142], [109, 4, 85, 2], [110, 4, 86, 2, "drawOval"], [110, 12, 86, 10, "drawOval"], [110, 13, 86, 11, "oval"], [110, 17, 86, 15], [110, 19, 86, 17, "paint"], [110, 24, 86, 22], [110, 26, 86, 24], [111, 6, 87, 4], [111, 10, 87, 8], [111, 11, 87, 9, "ref"], [111, 14, 87, 12], [111, 15, 87, 13, "drawOval"], [111, 23, 87, 21], [111, 24, 87, 22, "JsiSkRect"], [111, 44, 87, 31], [111, 45, 87, 32, "fromValue"], [111, 54, 87, 41], [111, 55, 87, 42], [111, 59, 87, 46], [111, 60, 87, 47, "CanvasKit"], [111, 69, 87, 56], [111, 71, 87, 58, "oval"], [111, 75, 87, 62], [111, 76, 87, 63], [111, 78, 87, 65, "JsiSkPaint"], [111, 100, 87, 75], [111, 101, 87, 76, "fromValue"], [111, 110, 87, 85], [111, 111, 87, 86, "paint"], [111, 116, 87, 91], [111, 117, 87, 92], [111, 118, 87, 93], [112, 4, 88, 2], [113, 4, 89, 2, "drawPath"], [113, 12, 89, 10, "drawPath"], [113, 13, 89, 11, "path"], [113, 17, 89, 15], [113, 19, 89, 17, "paint"], [113, 24, 89, 22], [113, 26, 89, 24], [114, 6, 90, 4], [114, 10, 90, 8], [114, 11, 90, 9, "ref"], [114, 14, 90, 12], [114, 15, 90, 13, "drawPath"], [114, 23, 90, 21], [114, 24, 90, 22, "JsiSkPath"], [114, 44, 90, 31], [114, 45, 90, 32, "fromValue"], [114, 54, 90, 41], [114, 55, 90, 42, "path"], [114, 59, 90, 46], [114, 60, 90, 47], [114, 62, 90, 49, "JsiSkPaint"], [114, 84, 90, 59], [114, 85, 90, 60, "fromValue"], [114, 94, 90, 69], [114, 95, 90, 70, "paint"], [114, 100, 90, 75], [114, 101, 90, 76], [114, 102, 90, 77], [115, 4, 91, 2], [116, 4, 92, 2, "drawText"], [116, 12, 92, 10, "drawText"], [116, 13, 92, 11, "str"], [116, 16, 92, 14], [116, 18, 92, 16, "x"], [116, 19, 92, 17], [116, 21, 92, 19, "y"], [116, 22, 92, 20], [116, 24, 92, 22, "paint"], [116, 29, 92, 27], [116, 31, 92, 29, "font"], [116, 35, 92, 33], [116, 37, 92, 35], [117, 6, 93, 4], [117, 10, 93, 8], [117, 11, 93, 9, "ref"], [117, 14, 93, 12], [117, 15, 93, 13, "drawText"], [117, 23, 93, 21], [117, 24, 93, 22, "str"], [117, 27, 93, 25], [117, 29, 93, 27, "x"], [117, 30, 93, 28], [117, 32, 93, 30, "y"], [117, 33, 93, 31], [117, 35, 93, 33, "JsiSkPaint"], [117, 57, 93, 43], [117, 58, 93, 44, "fromValue"], [117, 67, 93, 53], [117, 68, 93, 54, "paint"], [117, 73, 93, 59], [117, 74, 93, 60], [117, 76, 93, 62, "JsiSkFont"], [117, 96, 93, 71], [117, 97, 93, 72, "fromValue"], [117, 106, 93, 81], [117, 107, 93, 82, "font"], [117, 111, 93, 86], [117, 112, 93, 87], [117, 113, 93, 88], [118, 4, 94, 2], [119, 4, 95, 2, "drawTextBlob"], [119, 16, 95, 14, "drawTextBlob"], [119, 17, 95, 15, "blob"], [119, 21, 95, 19], [119, 23, 95, 21, "x"], [119, 24, 95, 22], [119, 26, 95, 24, "y"], [119, 27, 95, 25], [119, 29, 95, 27, "paint"], [119, 34, 95, 32], [119, 36, 95, 34], [120, 6, 96, 4], [120, 10, 96, 8], [120, 11, 96, 9, "ref"], [120, 14, 96, 12], [120, 15, 96, 13, "drawTextBlob"], [120, 27, 96, 25], [120, 28, 96, 26, "JsiSkTextBlob"], [120, 56, 96, 39], [120, 57, 96, 40, "fromValue"], [120, 66, 96, 49], [120, 67, 96, 50, "blob"], [120, 71, 96, 54], [120, 72, 96, 55], [120, 74, 96, 57, "x"], [120, 75, 96, 58], [120, 77, 96, 60, "y"], [120, 78, 96, 61], [120, 80, 96, 63, "JsiSkPaint"], [120, 102, 96, 73], [120, 103, 96, 74, "fromValue"], [120, 112, 96, 83], [120, 113, 96, 84, "paint"], [120, 118, 96, 89], [120, 119, 96, 90], [120, 120, 96, 91], [121, 4, 97, 2], [122, 4, 98, 2, "drawGlyphs"], [122, 14, 98, 12, "drawGlyphs"], [122, 15, 98, 13, "glyphs"], [122, 21, 98, 19], [122, 23, 98, 21, "positions"], [122, 32, 98, 30], [122, 34, 98, 32, "x"], [122, 35, 98, 33], [122, 37, 98, 35, "y"], [122, 38, 98, 36], [122, 40, 98, 38, "font"], [122, 44, 98, 42], [122, 46, 98, 44, "paint"], [122, 51, 98, 49], [122, 53, 98, 51], [123, 6, 99, 4], [123, 10, 99, 8], [123, 11, 99, 9, "ref"], [123, 14, 99, 12], [123, 15, 99, 13, "drawGlyphs"], [123, 25, 99, 23], [123, 26, 99, 24, "glyphs"], [123, 32, 99, 30], [123, 34, 99, 32, "positions"], [123, 43, 99, 41], [123, 44, 99, 42, "map"], [123, 47, 99, 45], [123, 48, 99, 46, "p"], [123, 49, 99, 47], [123, 53, 99, 51], [123, 54, 99, 52, "p"], [123, 55, 99, 53], [123, 56, 99, 54, "x"], [123, 57, 99, 55], [123, 59, 99, 57, "p"], [123, 60, 99, 58], [123, 61, 99, 59, "y"], [123, 62, 99, 60], [123, 63, 99, 61], [123, 64, 99, 62], [123, 65, 99, 63, "flat"], [123, 69, 99, 67], [123, 70, 99, 68], [123, 71, 99, 69], [123, 73, 99, 71, "x"], [123, 74, 99, 72], [123, 76, 99, 74, "y"], [123, 77, 99, 75], [123, 79, 99, 77, "JsiSkFont"], [123, 99, 99, 86], [123, 100, 99, 87, "fromValue"], [123, 109, 99, 96], [123, 110, 99, 97, "font"], [123, 114, 99, 101], [123, 115, 99, 102], [123, 117, 99, 104, "JsiSkPaint"], [123, 139, 99, 114], [123, 140, 99, 115, "fromValue"], [123, 149, 99, 124], [123, 150, 99, 125, "paint"], [123, 155, 99, 130], [123, 156, 99, 131], [123, 157, 99, 132], [124, 4, 100, 2], [125, 4, 101, 2, "drawSvg"], [125, 11, 101, 9, "drawSvg"], [125, 12, 101, 10, "svg"], [125, 15, 101, 13], [125, 17, 101, 15, "_width"], [125, 23, 101, 21], [125, 25, 101, 23, "_height"], [125, 32, 101, 30], [125, 34, 101, 32], [126, 6, 102, 4], [126, 12, 102, 10, "image"], [126, 17, 102, 15], [126, 20, 102, 18], [126, 24, 102, 22], [126, 25, 102, 23, "CanvasKit"], [126, 34, 102, 32], [126, 35, 102, 33, "MakeImageFromCanvasImageSource"], [126, 65, 102, 63], [126, 66, 102, 64, "svg"], [126, 69, 102, 67], [126, 70, 102, 68, "ref"], [126, 73, 102, 71], [126, 74, 102, 72], [127, 6, 103, 4], [127, 10, 103, 8], [127, 11, 103, 9, "ref"], [127, 14, 103, 12], [127, 15, 103, 13, "drawImage"], [127, 24, 103, 22], [127, 25, 103, 23, "image"], [127, 30, 103, 28], [127, 32, 103, 30], [127, 33, 103, 31], [127, 35, 103, 33], [127, 36, 103, 34], [127, 37, 103, 35], [128, 4, 104, 2], [129, 4, 105, 2, "save"], [129, 8, 105, 6, "save"], [129, 9, 105, 6], [129, 11, 105, 9], [130, 6, 106, 4], [130, 13, 106, 11], [130, 17, 106, 15], [130, 18, 106, 16, "ref"], [130, 21, 106, 19], [130, 22, 106, 20, "save"], [130, 26, 106, 24], [130, 27, 106, 25], [130, 28, 106, 26], [131, 4, 107, 2], [132, 4, 108, 2, "save<PERSON><PERSON><PERSON>"], [132, 13, 108, 11, "save<PERSON><PERSON><PERSON>"], [132, 14, 108, 12, "paint"], [132, 19, 108, 17], [132, 21, 108, 19, "bounds"], [132, 27, 108, 25], [132, 29, 108, 27, "backdrop"], [132, 37, 108, 35], [132, 39, 108, 37, "flags"], [132, 44, 108, 42], [132, 46, 108, 44], [133, 6, 109, 4], [133, 13, 109, 11], [133, 17, 109, 15], [133, 18, 109, 16, "ref"], [133, 21, 109, 19], [133, 22, 109, 20, "save<PERSON><PERSON><PERSON>"], [133, 31, 109, 29], [133, 32, 109, 30, "paint"], [133, 37, 109, 35], [133, 40, 109, 38, "JsiSkPaint"], [133, 62, 109, 48], [133, 63, 109, 49, "fromValue"], [133, 72, 109, 58], [133, 73, 109, 59, "paint"], [133, 78, 109, 64], [133, 79, 109, 65], [133, 82, 109, 68, "undefined"], [133, 91, 109, 77], [133, 93, 109, 79, "bounds"], [133, 99, 109, 85], [133, 102, 109, 88, "JsiSkRect"], [133, 122, 109, 97], [133, 123, 109, 98, "fromValue"], [133, 132, 109, 107], [133, 133, 109, 108], [133, 137, 109, 112], [133, 138, 109, 113, "CanvasKit"], [133, 147, 109, 122], [133, 149, 109, 124, "bounds"], [133, 155, 109, 130], [133, 156, 109, 131], [133, 159, 109, 134, "bounds"], [133, 165, 109, 140], [133, 167, 109, 142, "backdrop"], [133, 175, 109, 150], [133, 178, 109, 153, "JsiSkImageFilter"], [133, 212, 109, 169], [133, 213, 109, 170, "fromValue"], [133, 222, 109, 179], [133, 223, 109, 180, "backdrop"], [133, 231, 109, 188], [133, 232, 109, 189], [133, 235, 109, 192, "backdrop"], [133, 243, 109, 200], [133, 245, 109, 202, "flags"], [133, 250, 109, 207], [133, 251, 109, 208], [134, 4, 110, 2], [135, 4, 111, 2, "restore"], [135, 11, 111, 9, "restore"], [135, 12, 111, 9], [135, 14, 111, 12], [136, 6, 112, 4], [136, 10, 112, 8], [136, 11, 112, 9, "ref"], [136, 14, 112, 12], [136, 15, 112, 13, "restore"], [136, 22, 112, 20], [136, 23, 112, 21], [136, 24, 112, 22], [137, 4, 113, 2], [138, 4, 114, 2, "rotate"], [138, 10, 114, 8, "rotate"], [138, 11, 114, 9, "rotationInDegrees"], [138, 28, 114, 26], [138, 30, 114, 28, "rx"], [138, 32, 114, 30], [138, 34, 114, 32, "ry"], [138, 36, 114, 34], [138, 38, 114, 36], [139, 6, 115, 4], [139, 10, 115, 8], [139, 11, 115, 9, "ref"], [139, 14, 115, 12], [139, 15, 115, 13, "rotate"], [139, 21, 115, 19], [139, 22, 115, 20, "rotationInDegrees"], [139, 39, 115, 37], [139, 41, 115, 39, "rx"], [139, 43, 115, 41], [139, 45, 115, 43, "ry"], [139, 47, 115, 45], [139, 48, 115, 46], [140, 4, 116, 2], [141, 4, 117, 2, "scale"], [141, 9, 117, 7, "scale"], [141, 10, 117, 8, "sx"], [141, 12, 117, 10], [141, 14, 117, 12, "sy"], [141, 16, 117, 14], [141, 18, 117, 16], [142, 6, 118, 4], [142, 10, 118, 8], [142, 11, 118, 9, "ref"], [142, 14, 118, 12], [142, 15, 118, 13, "scale"], [142, 20, 118, 18], [142, 21, 118, 19, "sx"], [142, 23, 118, 21], [142, 25, 118, 23, "sy"], [142, 27, 118, 25], [142, 28, 118, 26], [143, 4, 119, 2], [144, 4, 120, 2, "skew"], [144, 8, 120, 6, "skew"], [144, 9, 120, 7, "sx"], [144, 11, 120, 9], [144, 13, 120, 11, "sy"], [144, 15, 120, 13], [144, 17, 120, 15], [145, 6, 121, 4], [145, 10, 121, 8], [145, 11, 121, 9, "ref"], [145, 14, 121, 12], [145, 15, 121, 13, "skew"], [145, 19, 121, 17], [145, 20, 121, 18, "sx"], [145, 22, 121, 20], [145, 24, 121, 22, "sy"], [145, 26, 121, 24], [145, 27, 121, 25], [146, 4, 122, 2], [147, 4, 123, 2, "translate"], [147, 13, 123, 11, "translate"], [147, 14, 123, 12, "dx"], [147, 16, 123, 14], [147, 18, 123, 16, "dy"], [147, 20, 123, 18], [147, 22, 123, 20], [148, 6, 124, 4], [148, 10, 124, 8], [148, 11, 124, 9, "ref"], [148, 14, 124, 12], [148, 15, 124, 13, "translate"], [148, 24, 124, 22], [148, 25, 124, 23, "dx"], [148, 27, 124, 25], [148, 29, 124, 27, "dy"], [148, 31, 124, 29], [148, 32, 124, 30], [149, 4, 125, 2], [150, 4, 126, 2, "drawColor"], [150, 13, 126, 11, "drawColor"], [150, 14, 126, 12, "color"], [150, 19, 126, 17], [150, 21, 126, 19, "blendMode"], [150, 30, 126, 28], [150, 32, 126, 30], [151, 6, 127, 4], [151, 10, 127, 8], [151, 11, 127, 9, "ref"], [151, 14, 127, 12], [151, 15, 127, 13, "drawColor"], [151, 24, 127, 22], [151, 25, 127, 23, "color"], [151, 30, 127, 28], [151, 32, 127, 30, "blendMode"], [151, 41, 127, 39], [151, 44, 127, 42], [151, 48, 127, 42, "getEnum"], [151, 61, 127, 49], [151, 63, 127, 50], [151, 67, 127, 54], [151, 68, 127, 55, "CanvasKit"], [151, 77, 127, 64], [151, 79, 127, 66], [151, 90, 127, 77], [151, 92, 127, 79, "blendMode"], [151, 101, 127, 88], [151, 102, 127, 89], [151, 105, 127, 92, "undefined"], [151, 114, 127, 101], [151, 115, 127, 102], [152, 4, 128, 2], [153, 4, 129, 2, "clear"], [153, 9, 129, 7, "clear"], [153, 10, 129, 8, "color"], [153, 15, 129, 13], [153, 17, 129, 15], [154, 6, 130, 4], [154, 10, 130, 8], [154, 11, 130, 9, "ref"], [154, 14, 130, 12], [154, 15, 130, 13, "clear"], [154, 20, 130, 18], [154, 21, 130, 19, "color"], [154, 26, 130, 24], [154, 27, 130, 25], [155, 4, 131, 2], [156, 4, 132, 2, "clipPath"], [156, 12, 132, 10, "clipPath"], [156, 13, 132, 11, "path"], [156, 17, 132, 15], [156, 19, 132, 17, "op"], [156, 21, 132, 19], [156, 23, 132, 21, "doAnti<PERSON>lias"], [156, 34, 132, 32], [156, 36, 132, 34], [157, 6, 133, 4], [157, 10, 133, 8], [157, 11, 133, 9, "ref"], [157, 14, 133, 12], [157, 15, 133, 13, "clipPath"], [157, 23, 133, 21], [157, 24, 133, 22, "JsiSkPath"], [157, 44, 133, 31], [157, 45, 133, 32, "fromValue"], [157, 54, 133, 41], [157, 55, 133, 42, "path"], [157, 59, 133, 46], [157, 60, 133, 47], [157, 62, 133, 49], [157, 66, 133, 49, "getEnum"], [157, 79, 133, 56], [157, 81, 133, 57], [157, 85, 133, 61], [157, 86, 133, 62, "CanvasKit"], [157, 95, 133, 71], [157, 97, 133, 73], [157, 105, 133, 81], [157, 107, 133, 83, "op"], [157, 109, 133, 85], [157, 110, 133, 86], [157, 112, 133, 88, "doAnti<PERSON>lias"], [157, 123, 133, 99], [157, 124, 133, 100], [158, 4, 134, 2], [159, 4, 135, 2, "clipRect"], [159, 12, 135, 10, "clipRect"], [159, 13, 135, 11, "rect"], [159, 17, 135, 15], [159, 19, 135, 17, "op"], [159, 21, 135, 19], [159, 23, 135, 21, "doAnti<PERSON>lias"], [159, 34, 135, 32], [159, 36, 135, 34], [160, 6, 136, 4], [160, 10, 136, 8], [160, 11, 136, 9, "ref"], [160, 14, 136, 12], [160, 15, 136, 13, "clipRect"], [160, 23, 136, 21], [160, 24, 136, 22, "JsiSkRect"], [160, 44, 136, 31], [160, 45, 136, 32, "fromValue"], [160, 54, 136, 41], [160, 55, 136, 42], [160, 59, 136, 46], [160, 60, 136, 47, "CanvasKit"], [160, 69, 136, 56], [160, 71, 136, 58, "rect"], [160, 75, 136, 62], [160, 76, 136, 63], [160, 78, 136, 65], [160, 82, 136, 65, "getEnum"], [160, 95, 136, 72], [160, 97, 136, 73], [160, 101, 136, 77], [160, 102, 136, 78, "CanvasKit"], [160, 111, 136, 87], [160, 113, 136, 89], [160, 121, 136, 97], [160, 123, 136, 99, "op"], [160, 125, 136, 101], [160, 126, 136, 102], [160, 128, 136, 104, "doAnti<PERSON>lias"], [160, 139, 136, 115], [160, 140, 136, 116], [161, 4, 137, 2], [162, 4, 138, 2, "clipRRect"], [162, 13, 138, 11, "clipRRect"], [162, 14, 138, 12, "rrect"], [162, 19, 138, 17], [162, 21, 138, 19, "op"], [162, 23, 138, 21], [162, 25, 138, 23, "doAnti<PERSON>lias"], [162, 36, 138, 34], [162, 38, 138, 36], [163, 6, 139, 4], [163, 10, 139, 8], [163, 11, 139, 9, "ref"], [163, 14, 139, 12], [163, 15, 139, 13, "clipRRect"], [163, 24, 139, 22], [163, 25, 139, 23, "JsiSkRRect"], [163, 47, 139, 33], [163, 48, 139, 34, "fromValue"], [163, 57, 139, 43], [163, 58, 139, 44], [163, 62, 139, 48], [163, 63, 139, 49, "CanvasKit"], [163, 72, 139, 58], [163, 74, 139, 60, "rrect"], [163, 79, 139, 65], [163, 80, 139, 66], [163, 82, 139, 68], [163, 86, 139, 68, "getEnum"], [163, 99, 139, 75], [163, 101, 139, 76], [163, 105, 139, 80], [163, 106, 139, 81, "CanvasKit"], [163, 115, 139, 90], [163, 117, 139, 92], [163, 125, 139, 100], [163, 127, 139, 102, "op"], [163, 129, 139, 104], [163, 130, 139, 105], [163, 132, 139, 107, "doAnti<PERSON>lias"], [163, 143, 139, 118], [163, 144, 139, 119], [164, 4, 140, 2], [165, 4, 141, 2, "concat"], [165, 10, 141, 8, "concat"], [165, 11, 141, 9, "m"], [165, 12, 141, 10], [165, 14, 141, 12], [166, 6, 142, 4], [166, 10, 142, 8], [166, 11, 142, 9, "ref"], [166, 14, 142, 12], [166, 15, 142, 13, "concat"], [166, 21, 142, 19], [166, 22, 142, 20, "Array"], [166, 27, 142, 25], [166, 28, 142, 26, "isArray"], [166, 35, 142, 33], [166, 36, 142, 34, "m"], [166, 37, 142, 35], [166, 38, 142, 36], [166, 41, 142, 39, "m"], [166, 42, 142, 40], [166, 45, 142, 43, "JsiSkMatrix"], [166, 69, 142, 54], [166, 70, 142, 55, "fromValue"], [166, 79, 142, 64], [166, 80, 142, 65, "m"], [166, 81, 142, 66], [166, 82, 142, 67], [166, 83, 142, 68], [167, 4, 143, 2], [168, 4, 144, 2, "drawPicture"], [168, 15, 144, 13, "drawPicture"], [168, 16, 144, 14, "skp"], [168, 19, 144, 17], [168, 21, 144, 19], [169, 6, 145, 4], [169, 10, 145, 8], [169, 11, 145, 9, "ref"], [169, 14, 145, 12], [169, 15, 145, 13, "drawPicture"], [169, 26, 145, 24], [169, 27, 145, 25, "JsiSkPicture"], [169, 53, 145, 37], [169, 54, 145, 38, "fromValue"], [169, 63, 145, 47], [169, 64, 145, 48, "skp"], [169, 67, 145, 51], [169, 68, 145, 52], [169, 69, 145, 53], [170, 4, 146, 2], [171, 4, 147, 2, "drawAtlas"], [171, 13, 147, 11, "drawAtlas"], [171, 14, 147, 12, "atlas"], [171, 19, 147, 17], [171, 21, 147, 19, "srcs"], [171, 25, 147, 23], [171, 27, 147, 25, "dsts"], [171, 31, 147, 29], [171, 33, 147, 31, "paint"], [171, 38, 147, 36], [171, 40, 147, 38, "blendMode"], [171, 49, 147, 47], [171, 51, 147, 49, "colors"], [171, 57, 147, 55], [171, 59, 147, 57, "sampling"], [171, 67, 147, 65], [171, 69, 147, 67], [172, 6, 148, 4], [172, 12, 148, 10, "src"], [172, 15, 148, 13], [172, 18, 148, 16, "srcs"], [172, 22, 148, 20], [172, 23, 148, 21, "flatMap"], [172, 30, 148, 28], [172, 31, 148, 29, "s"], [172, 32, 148, 30], [172, 36, 148, 34, "Array"], [172, 41, 148, 39], [172, 42, 148, 40, "from"], [172, 46, 148, 44], [172, 47, 148, 45, "JsiSkRect"], [172, 67, 148, 54], [172, 68, 148, 55, "fromValue"], [172, 77, 148, 64], [172, 78, 148, 65], [172, 82, 148, 69], [172, 83, 148, 70, "CanvasKit"], [172, 92, 148, 79], [172, 94, 148, 81, "s"], [172, 95, 148, 82], [172, 96, 148, 83], [172, 97, 148, 84], [172, 98, 148, 85], [173, 6, 149, 4], [173, 12, 149, 10, "dst"], [173, 15, 149, 13], [173, 18, 149, 16, "dsts"], [173, 22, 149, 20], [173, 23, 149, 21, "flatMap"], [173, 30, 149, 28], [173, 31, 149, 29, "s"], [173, 32, 149, 30], [173, 36, 149, 34, "Array"], [173, 41, 149, 39], [173, 42, 149, 40, "from"], [173, 46, 149, 44], [173, 47, 149, 45, "JsiSkRSXform"], [173, 73, 149, 57], [173, 74, 149, 58, "fromValue"], [173, 83, 149, 67], [173, 84, 149, 68, "s"], [173, 85, 149, 69], [173, 86, 149, 70], [173, 87, 149, 71], [173, 88, 149, 72], [174, 6, 150, 4], [174, 10, 150, 8, "cls"], [174, 13, 150, 11], [175, 6, 151, 4], [175, 10, 151, 8, "colors"], [175, 16, 151, 14], [175, 18, 151, 16], [176, 8, 152, 6, "cls"], [176, 11, 152, 9], [176, 14, 152, 12], [176, 18, 152, 16, "Uint32Array"], [176, 29, 152, 27], [176, 30, 152, 28, "colors"], [176, 36, 152, 34], [176, 37, 152, 35, "length"], [176, 43, 152, 41], [176, 44, 152, 42], [177, 8, 153, 6], [177, 13, 153, 11], [177, 17, 153, 15, "i"], [177, 18, 153, 16], [177, 21, 153, 19], [177, 22, 153, 20], [177, 24, 153, 22, "i"], [177, 25, 153, 23], [177, 28, 153, 26, "colors"], [177, 34, 153, 32], [177, 35, 153, 33, "length"], [177, 41, 153, 39], [177, 43, 153, 41, "i"], [177, 44, 153, 42], [177, 46, 153, 44], [177, 48, 153, 46], [178, 10, 154, 8], [178, 16, 154, 14], [178, 17, 154, 15, "r"], [178, 18, 154, 16], [178, 20, 154, 18, "g"], [178, 21, 154, 19], [178, 23, 154, 21, "b"], [178, 24, 154, 22], [178, 26, 154, 24, "a"], [178, 27, 154, 25], [178, 28, 154, 26], [178, 31, 154, 29, "colors"], [178, 37, 154, 35], [178, 38, 154, 36, "i"], [178, 39, 154, 37], [178, 40, 154, 38], [179, 10, 155, 8, "cls"], [179, 13, 155, 11], [179, 14, 155, 12, "i"], [179, 15, 155, 13], [179, 16, 155, 14], [179, 19, 155, 17], [179, 23, 155, 21], [179, 24, 155, 22, "CanvasKit"], [179, 33, 155, 31], [179, 34, 155, 32, "ColorAsInt"], [179, 44, 155, 42], [179, 45, 155, 43, "r"], [179, 46, 155, 44], [179, 49, 155, 47], [179, 52, 155, 50], [179, 54, 155, 52, "g"], [179, 55, 155, 53], [179, 58, 155, 56], [179, 61, 155, 59], [179, 63, 155, 61, "b"], [179, 64, 155, 62], [179, 67, 155, 65], [179, 70, 155, 68], [179, 72, 155, 70, "a"], [179, 73, 155, 71], [179, 76, 155, 74], [179, 79, 155, 77], [179, 80, 155, 78], [180, 8, 156, 6], [181, 6, 157, 4], [182, 6, 158, 4], [182, 10, 158, 8, "ckSampling"], [182, 20, 158, 18], [182, 23, 158, 21], [183, 8, 159, 6, "filter"], [183, 14, 159, 12], [183, 16, 159, 14], [183, 20, 159, 18], [183, 21, 159, 19, "CanvasKit"], [183, 30, 159, 28], [183, 31, 159, 29, "FilterMode"], [183, 41, 159, 39], [183, 42, 159, 40, "Linear"], [183, 48, 159, 46], [184, 8, 160, 6, "mipmap"], [184, 14, 160, 12], [184, 16, 160, 14], [184, 20, 160, 18], [184, 21, 160, 19, "CanvasKit"], [184, 30, 160, 28], [184, 31, 160, 29, "MipmapMode"], [184, 41, 160, 39], [184, 42, 160, 40, "None"], [185, 6, 161, 4], [185, 7, 161, 5], [186, 6, 162, 4], [186, 10, 162, 8, "sampling"], [186, 18, 162, 16], [186, 22, 162, 20], [186, 26, 162, 20, "isCubicSampling"], [186, 48, 162, 35], [186, 50, 162, 36, "sampling"], [186, 58, 162, 44], [186, 59, 162, 45], [186, 61, 162, 47], [187, 8, 163, 6, "ckSampling"], [187, 18, 163, 16], [187, 21, 163, 19, "sampling"], [187, 29, 163, 27], [188, 6, 164, 4], [188, 7, 164, 5], [188, 13, 164, 11], [188, 17, 164, 15, "sampling"], [188, 25, 164, 23], [188, 27, 164, 25], [189, 8, 165, 6, "ckSampling"], [189, 18, 165, 16], [189, 21, 165, 19], [190, 10, 166, 8, "filter"], [190, 16, 166, 14], [190, 18, 166, 16], [190, 22, 166, 16, "getEnum"], [190, 35, 166, 23], [190, 37, 166, 24], [190, 41, 166, 28], [190, 42, 166, 29, "CanvasKit"], [190, 51, 166, 38], [190, 53, 166, 40], [190, 65, 166, 52], [190, 67, 166, 54, "sampling"], [190, 75, 166, 62], [190, 76, 166, 63, "filter"], [190, 82, 166, 69], [190, 83, 166, 70], [191, 10, 167, 8, "mipmap"], [191, 16, 167, 14], [191, 18, 167, 16, "sampling"], [191, 26, 167, 24], [191, 27, 167, 25, "mipmap"], [191, 33, 167, 31], [191, 36, 167, 34], [191, 40, 167, 34, "getEnum"], [191, 53, 167, 41], [191, 55, 167, 42], [191, 59, 167, 46], [191, 60, 167, 47, "CanvasKit"], [191, 69, 167, 56], [191, 71, 167, 58], [191, 83, 167, 70], [191, 85, 167, 72, "sampling"], [191, 93, 167, 80], [191, 94, 167, 81, "mipmap"], [191, 100, 167, 87], [191, 101, 167, 88], [191, 104, 167, 91], [191, 108, 167, 95], [191, 109, 167, 96, "CanvasKit"], [191, 118, 167, 105], [191, 119, 167, 106, "MipmapMode"], [191, 129, 167, 116], [191, 130, 167, 117, "None"], [192, 8, 168, 6], [192, 9, 168, 7], [193, 6, 169, 4], [194, 6, 170, 4], [194, 10, 170, 8], [194, 11, 170, 9, "ref"], [194, 14, 170, 12], [194, 15, 170, 13, "drawAtlas"], [194, 24, 170, 22], [194, 25, 170, 23, "JsiSkImage"], [194, 47, 170, 33], [194, 48, 170, 34, "fromValue"], [194, 57, 170, 43], [194, 58, 170, 44, "atlas"], [194, 63, 170, 49], [194, 64, 170, 50], [194, 66, 170, 52, "src"], [194, 69, 170, 55], [194, 71, 170, 57, "dst"], [194, 74, 170, 60], [194, 76, 170, 62, "JsiSkPaint"], [194, 98, 170, 72], [194, 99, 170, 73, "fromValue"], [194, 108, 170, 82], [194, 109, 170, 83, "paint"], [194, 114, 170, 88], [194, 115, 170, 89], [194, 117, 170, 91, "blendMode"], [194, 126, 170, 100], [194, 129, 170, 103], [194, 133, 170, 103, "getEnum"], [194, 146, 170, 110], [194, 148, 170, 111], [194, 152, 170, 115], [194, 153, 170, 116, "CanvasKit"], [194, 162, 170, 125], [194, 164, 170, 127], [194, 175, 170, 138], [194, 177, 170, 140, "blendMode"], [194, 186, 170, 149], [194, 187, 170, 150], [194, 190, 170, 153], [194, 194, 170, 157], [194, 195, 170, 158, "CanvasKit"], [194, 204, 170, 167], [194, 205, 170, 168, "BlendMode"], [194, 214, 170, 177], [194, 215, 170, 178, "DstOver"], [194, 222, 170, 185], [194, 224, 170, 187, "cls"], [194, 227, 170, 190], [194, 229, 170, 192, "ckSampling"], [194, 239, 170, 202], [194, 240, 170, 203], [195, 4, 171, 2], [196, 4, 172, 2, "readPixels"], [196, 14, 172, 12, "readPixels"], [196, 15, 172, 13, "srcX"], [196, 19, 172, 17], [196, 21, 172, 19, "srcY"], [196, 25, 172, 23], [196, 27, 172, 25, "imageInfo"], [196, 36, 172, 34], [196, 38, 172, 36], [197, 6, 173, 4], [197, 12, 173, 10, "pxInfo"], [197, 18, 173, 16], [197, 21, 173, 19], [198, 8, 174, 6, "width"], [198, 13, 174, 11], [198, 15, 174, 13, "imageInfo"], [198, 24, 174, 22], [198, 25, 174, 23, "width"], [198, 30, 174, 28], [199, 8, 175, 6, "height"], [199, 14, 175, 12], [199, 16, 175, 14, "imageInfo"], [199, 25, 175, 23], [199, 26, 175, 24, "height"], [199, 32, 175, 30], [200, 8, 176, 6, "colorSpace"], [200, 18, 176, 16], [200, 20, 176, 18], [200, 24, 176, 22], [200, 25, 176, 23, "CanvasKit"], [200, 34, 176, 32], [200, 35, 176, 33, "ColorSpace"], [200, 45, 176, 43], [200, 46, 176, 44, "SRGB"], [200, 50, 176, 48], [201, 8, 177, 6, "alphaType"], [201, 17, 177, 15], [201, 19, 177, 17], [201, 23, 177, 17, "getEnum"], [201, 36, 177, 24], [201, 38, 177, 25], [201, 42, 177, 29], [201, 43, 177, 30, "CanvasKit"], [201, 52, 177, 39], [201, 54, 177, 41], [201, 65, 177, 52], [201, 67, 177, 54, "imageInfo"], [201, 76, 177, 63], [201, 77, 177, 64, "alphaType"], [201, 86, 177, 73], [201, 87, 177, 74], [202, 8, 178, 6, "colorType"], [202, 17, 178, 15], [202, 19, 178, 17], [202, 23, 178, 17, "getEnum"], [202, 36, 178, 24], [202, 38, 178, 25], [202, 42, 178, 29], [202, 43, 178, 30, "CanvasKit"], [202, 52, 178, 39], [202, 54, 178, 41], [202, 65, 178, 52], [202, 67, 178, 54, "imageInfo"], [202, 76, 178, 63], [202, 77, 178, 64, "colorType"], [202, 86, 178, 73], [203, 6, 179, 4], [203, 7, 179, 5], [204, 6, 180, 4], [204, 13, 180, 11], [204, 17, 180, 15], [204, 18, 180, 16, "ref"], [204, 21, 180, 19], [204, 22, 180, 20, "readPixels"], [204, 32, 180, 30], [204, 33, 180, 31, "srcX"], [204, 37, 180, 35], [204, 39, 180, 37, "srcY"], [204, 43, 180, 41], [204, 45, 180, 43, "pxInfo"], [204, 51, 180, 49], [204, 52, 180, 50], [205, 4, 181, 2], [206, 2, 182, 0], [207, 2, 182, 1, "exports"], [207, 9, 182, 1], [207, 10, 182, 1, "JsiSkCanvas"], [207, 21, 182, 1], [207, 24, 182, 1, "JsiSkCanvas"], [207, 35, 182, 1], [208, 0, 182, 1], [208, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkCanvas", "constructor", "_defineProperty$argument_2", "drawRect", "drawImage", "drawImageRect", "drawImageCubic", "drawImageOptions", "drawImageNine", "drawImageRectCubic", "drawImageRectOptions", "<PERSON><PERSON><PERSON><PERSON>", "drawLine", "drawCircle", "drawVertices", "drawPatch", "cubics.map$argument_0", "texs.flatMap$argument_0", "restoreToCount", "drawPoints", "points.map$argument_0", "drawArc", "drawRRect", "drawDRRect", "drawOval", "drawPath", "drawText", "drawTextBlob", "drawGlyphs", "positions.map$argument_0", "drawSvg", "save", "save<PERSON><PERSON><PERSON>", "restore", "rotate", "scale", "skew", "translate", "drawColor", "clear", "clipPath", "clipRect", "clipRRect", "concat", "drawPicture", "drawAtlas", "srcs.flatMap$argument_0", "dsts.flatMap$argument_0", "readPixels"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGgB;ECC;qCCE;KDE;GDC;EGC;GHE;EIC;GJE;EKC;GLE;EMC;GNE;EOC;GPE;EQC;GRE;ESC;GTE;EUC;GVE;EWC;GXE;EYC;GZE;EaC;GbE;EcC;GdE;EeC;kCCC;gBDG,sCE,wCF;GfC;EkBC;GlBE;EmBC;+ECC;gBDG;GnBC;EqBC;GrBE;EsBC;GtBE;EuBC;GvBE;EwBC;GxBE;EyBC;GzBE;E0BC;G1BE;E2BC;G3BE;E4BC;8CCC,eD;G5BC;E8BC;G9BG;E+BC;G/BE;EgCC;GhCE;EiCC;GjCE;EkCC;GlCE;EmCC;GnCE;EoCC;GpCE;EqCC;GrCE;EsCC;GtCE;EuCC;GvCE;EwCC;GxCE;EyCC;GzCE;E0CC;G1CE;E2CC;G3CE;E4CC;G5CE;E6CC;6BCC,uDD;6BEC,0CF;G7CsB;EgDC;GhDS;CHC"}}, "type": "js/module"}]}