{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const countSkinPixelsInRegion = (data, startX, startY, size, imageWidth) => {\n      let skinPixels = 0;\n      for (let y = startY; y < startY + size; y++) {\n        for (let x = startX; x < startX + size; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Simple skin tone detection\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n        }\n      }\n      return skinPixels;\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Get the region to blur\n      const imageData = ctx.getImageData(x, y, width, height);\n      const data = imageData.data;\n\n      // Apply multiple blur effects\n\n      // 1. Heavy pixelation\n      const pixelSize = Math.max(25, Math.min(width, height) / 6);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < height; py += pixelSize) {\n        for (let px = 0; px < width; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n              const index = ((py + dy) * width + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n                const index = ((py + dy) * width + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // 2. Additional gaussian-like blur\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur effect`);\n      for (let i = 0; i < 3; i++) {\n        // Multiple passes\n        applySimpleBlur(data, width, height);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, x, y);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 752,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 927,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 968,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 963,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1577, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "countSkinPixelsInRegion"], [200, 33, 215, 31], [200, 36, 215, 34, "countSkinPixelsInRegion"], [200, 37, 215, 35, "data"], [200, 41, 215, 58], [200, 43, 215, 60, "startX"], [200, 49, 215, 74], [200, 51, 215, 76, "startY"], [200, 57, 215, 90], [200, 59, 215, 92, "size"], [200, 63, 215, 104], [200, 65, 215, 106, "imageWidth"], [200, 75, 215, 124], [200, 80, 215, 129], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 11, 217, 9], [202, 15, 217, 13, "y"], [202, 16, 217, 14], [202, 19, 217, 17, "startY"], [202, 25, 217, 23], [202, 27, 217, 25, "y"], [202, 28, 217, 26], [202, 31, 217, 29, "startY"], [202, 37, 217, 35], [202, 40, 217, 38, "size"], [202, 44, 217, 42], [202, 46, 217, 44, "y"], [202, 47, 217, 45], [202, 49, 217, 47], [202, 51, 217, 49], [203, 8, 218, 6], [203, 13, 218, 11], [203, 17, 218, 15, "x"], [203, 18, 218, 16], [203, 21, 218, 19, "startX"], [203, 27, 218, 25], [203, 29, 218, 27, "x"], [203, 30, 218, 28], [203, 33, 218, 31, "startX"], [203, 39, 218, 37], [203, 42, 218, 40, "size"], [203, 46, 218, 44], [203, 48, 218, 46, "x"], [203, 49, 218, 47], [203, 51, 218, 49], [203, 53, 218, 51], [204, 10, 219, 8], [204, 16, 219, 14, "index"], [204, 21, 219, 19], [204, 24, 219, 22], [204, 25, 219, 23, "y"], [204, 26, 219, 24], [204, 29, 219, 27, "imageWidth"], [204, 39, 219, 37], [204, 42, 219, 40, "x"], [204, 43, 219, 41], [204, 47, 219, 45], [204, 48, 219, 46], [205, 10, 220, 8], [205, 16, 220, 14, "r"], [205, 17, 220, 15], [205, 20, 220, 18, "data"], [205, 24, 220, 22], [205, 25, 220, 23, "index"], [205, 30, 220, 28], [205, 31, 220, 29], [206, 10, 221, 8], [206, 16, 221, 14, "g"], [206, 17, 221, 15], [206, 20, 221, 18, "data"], [206, 24, 221, 22], [206, 25, 221, 23, "index"], [206, 30, 221, 28], [206, 33, 221, 31], [206, 34, 221, 32], [206, 35, 221, 33], [207, 10, 222, 8], [207, 16, 222, 14, "b"], [207, 17, 222, 15], [207, 20, 222, 18, "data"], [207, 24, 222, 22], [207, 25, 222, 23, "index"], [207, 30, 222, 28], [207, 33, 222, 31], [207, 34, 222, 32], [207, 35, 222, 33], [209, 10, 224, 8], [210, 10, 225, 8], [210, 14, 225, 12, "isSkinTone"], [210, 24, 225, 22], [210, 25, 225, 23, "r"], [210, 26, 225, 24], [210, 28, 225, 26, "g"], [210, 29, 225, 27], [210, 31, 225, 29, "b"], [210, 32, 225, 30], [210, 33, 225, 31], [210, 35, 225, 33], [211, 12, 226, 10, "skinPixels"], [211, 22, 226, 20], [211, 24, 226, 22], [212, 10, 227, 8], [213, 8, 228, 6], [214, 6, 229, 4], [215, 6, 230, 4], [215, 13, 230, 11, "skinPixels"], [215, 23, 230, 21], [216, 4, 231, 2], [216, 5, 231, 3], [217, 4, 233, 2], [217, 10, 233, 8, "isSkinTone"], [217, 20, 233, 18], [217, 23, 233, 21, "isSkinTone"], [217, 24, 233, 22, "r"], [217, 25, 233, 31], [217, 27, 233, 33, "g"], [217, 28, 233, 42], [217, 30, 233, 44, "b"], [217, 31, 233, 53], [217, 36, 233, 58], [218, 6, 234, 4], [219, 6, 235, 4], [219, 13, 236, 6, "r"], [219, 14, 236, 7], [219, 17, 236, 10], [219, 19, 236, 12], [219, 23, 236, 16, "g"], [219, 24, 236, 17], [219, 27, 236, 20], [219, 29, 236, 22], [219, 33, 236, 26, "b"], [219, 34, 236, 27], [219, 37, 236, 30], [219, 39, 236, 32], [219, 43, 237, 6, "r"], [219, 44, 237, 7], [219, 47, 237, 10, "g"], [219, 48, 237, 11], [219, 52, 237, 15, "r"], [219, 53, 237, 16], [219, 56, 237, 19, "b"], [219, 57, 237, 20], [219, 61, 238, 6, "Math"], [219, 65, 238, 10], [219, 66, 238, 11, "abs"], [219, 69, 238, 14], [219, 70, 238, 15, "r"], [219, 71, 238, 16], [219, 74, 238, 19, "g"], [219, 75, 238, 20], [219, 76, 238, 21], [219, 79, 238, 24], [219, 81, 238, 26], [219, 85, 239, 6, "Math"], [219, 89, 239, 10], [219, 90, 239, 11, "max"], [219, 93, 239, 14], [219, 94, 239, 15, "r"], [219, 95, 239, 16], [219, 97, 239, 18, "g"], [219, 98, 239, 19], [219, 100, 239, 21, "b"], [219, 101, 239, 22], [219, 102, 239, 23], [219, 105, 239, 26, "Math"], [219, 109, 239, 30], [219, 110, 239, 31, "min"], [219, 113, 239, 34], [219, 114, 239, 35, "r"], [219, 115, 239, 36], [219, 117, 239, 38, "g"], [219, 118, 239, 39], [219, 120, 239, 41, "b"], [219, 121, 239, 42], [219, 122, 239, 43], [219, 125, 239, 46], [219, 127, 239, 48], [220, 4, 241, 2], [220, 5, 241, 3], [221, 4, 243, 2], [221, 10, 243, 8, "mergeFaceDetections"], [221, 29, 243, 27], [221, 32, 243, 31, "faces"], [221, 37, 243, 43], [221, 41, 243, 48], [222, 6, 244, 4], [222, 10, 244, 8, "faces"], [222, 15, 244, 13], [222, 16, 244, 14, "length"], [222, 22, 244, 20], [222, 26, 244, 24], [222, 27, 244, 25], [222, 29, 244, 27], [222, 36, 244, 34, "faces"], [222, 41, 244, 39], [223, 6, 246, 4], [223, 12, 246, 10, "merged"], [223, 18, 246, 16], [223, 21, 246, 19], [223, 23, 246, 21], [224, 6, 247, 4], [224, 12, 247, 10, "used"], [224, 16, 247, 14], [224, 19, 247, 17], [224, 23, 247, 21, "Set"], [224, 26, 247, 24], [224, 27, 247, 25], [224, 28, 247, 26], [225, 6, 249, 4], [225, 11, 249, 9], [225, 15, 249, 13, "i"], [225, 16, 249, 14], [225, 19, 249, 17], [225, 20, 249, 18], [225, 22, 249, 20, "i"], [225, 23, 249, 21], [225, 26, 249, 24, "faces"], [225, 31, 249, 29], [225, 32, 249, 30, "length"], [225, 38, 249, 36], [225, 40, 249, 38, "i"], [225, 41, 249, 39], [225, 43, 249, 41], [225, 45, 249, 43], [226, 8, 250, 6], [226, 12, 250, 10, "used"], [226, 16, 250, 14], [226, 17, 250, 15, "has"], [226, 20, 250, 18], [226, 21, 250, 19, "i"], [226, 22, 250, 20], [226, 23, 250, 21], [226, 25, 250, 23], [227, 8, 252, 6], [227, 12, 252, 10, "currentFace"], [227, 23, 252, 21], [227, 26, 252, 24, "faces"], [227, 31, 252, 29], [227, 32, 252, 30, "i"], [227, 33, 252, 31], [227, 34, 252, 32], [228, 8, 253, 6, "used"], [228, 12, 253, 10], [228, 13, 253, 11, "add"], [228, 16, 253, 14], [228, 17, 253, 15, "i"], [228, 18, 253, 16], [228, 19, 253, 17], [230, 8, 255, 6], [231, 8, 256, 6], [231, 13, 256, 11], [231, 17, 256, 15, "j"], [231, 18, 256, 16], [231, 21, 256, 19, "i"], [231, 22, 256, 20], [231, 25, 256, 23], [231, 26, 256, 24], [231, 28, 256, 26, "j"], [231, 29, 256, 27], [231, 32, 256, 30, "faces"], [231, 37, 256, 35], [231, 38, 256, 36, "length"], [231, 44, 256, 42], [231, 46, 256, 44, "j"], [231, 47, 256, 45], [231, 49, 256, 47], [231, 51, 256, 49], [232, 10, 257, 8], [232, 14, 257, 12, "used"], [232, 18, 257, 16], [232, 19, 257, 17, "has"], [232, 22, 257, 20], [232, 23, 257, 21, "j"], [232, 24, 257, 22], [232, 25, 257, 23], [232, 27, 257, 25], [233, 10, 259, 8], [233, 16, 259, 14, "overlap"], [233, 23, 259, 21], [233, 26, 259, 24, "calculateOverlap"], [233, 42, 259, 40], [233, 43, 259, 41, "currentFace"], [233, 54, 259, 52], [233, 55, 259, 53, "boundingBox"], [233, 66, 259, 64], [233, 68, 259, 66, "faces"], [233, 73, 259, 71], [233, 74, 259, 72, "j"], [233, 75, 259, 73], [233, 76, 259, 74], [233, 77, 259, 75, "boundingBox"], [233, 88, 259, 86], [233, 89, 259, 87], [234, 10, 260, 8], [234, 14, 260, 12, "overlap"], [234, 21, 260, 19], [234, 24, 260, 22], [234, 27, 260, 25], [234, 29, 260, 27], [235, 12, 260, 29], [236, 12, 261, 10], [237, 12, 262, 10, "currentFace"], [237, 23, 262, 21], [237, 26, 262, 24, "mergeTwoFaces"], [237, 39, 262, 37], [237, 40, 262, 38, "currentFace"], [237, 51, 262, 49], [237, 53, 262, 51, "faces"], [237, 58, 262, 56], [237, 59, 262, 57, "j"], [237, 60, 262, 58], [237, 61, 262, 59], [237, 62, 262, 60], [238, 12, 263, 10, "used"], [238, 16, 263, 14], [238, 17, 263, 15, "add"], [238, 20, 263, 18], [238, 21, 263, 19, "j"], [238, 22, 263, 20], [238, 23, 263, 21], [239, 10, 264, 8], [240, 8, 265, 6], [241, 8, 267, 6, "merged"], [241, 14, 267, 12], [241, 15, 267, 13, "push"], [241, 19, 267, 17], [241, 20, 267, 18, "currentFace"], [241, 31, 267, 29], [241, 32, 267, 30], [242, 6, 268, 4], [243, 6, 270, 4], [243, 13, 270, 11, "merged"], [243, 19, 270, 17], [244, 4, 271, 2], [244, 5, 271, 3], [245, 4, 273, 2], [245, 10, 273, 8, "calculateOverlap"], [245, 26, 273, 24], [245, 29, 273, 27, "calculateOverlap"], [245, 30, 273, 28, "box1"], [245, 34, 273, 37], [245, 36, 273, 39, "box2"], [245, 40, 273, 48], [245, 45, 273, 53], [246, 6, 274, 4], [246, 12, 274, 10, "x1"], [246, 14, 274, 12], [246, 17, 274, 15, "Math"], [246, 21, 274, 19], [246, 22, 274, 20, "max"], [246, 25, 274, 23], [246, 26, 274, 24, "box1"], [246, 30, 274, 28], [246, 31, 274, 29, "xCenter"], [246, 38, 274, 36], [246, 41, 274, 39, "box1"], [246, 45, 274, 43], [246, 46, 274, 44, "width"], [246, 51, 274, 49], [246, 54, 274, 50], [246, 55, 274, 51], [246, 57, 274, 53, "box2"], [246, 61, 274, 57], [246, 62, 274, 58, "xCenter"], [246, 69, 274, 65], [246, 72, 274, 68, "box2"], [246, 76, 274, 72], [246, 77, 274, 73, "width"], [246, 82, 274, 78], [246, 85, 274, 79], [246, 86, 274, 80], [246, 87, 274, 81], [247, 6, 275, 4], [247, 12, 275, 10, "y1"], [247, 14, 275, 12], [247, 17, 275, 15, "Math"], [247, 21, 275, 19], [247, 22, 275, 20, "max"], [247, 25, 275, 23], [247, 26, 275, 24, "box1"], [247, 30, 275, 28], [247, 31, 275, 29, "yCenter"], [247, 38, 275, 36], [247, 41, 275, 39, "box1"], [247, 45, 275, 43], [247, 46, 275, 44, "height"], [247, 52, 275, 50], [247, 55, 275, 51], [247, 56, 275, 52], [247, 58, 275, 54, "box2"], [247, 62, 275, 58], [247, 63, 275, 59, "yCenter"], [247, 70, 275, 66], [247, 73, 275, 69, "box2"], [247, 77, 275, 73], [247, 78, 275, 74, "height"], [247, 84, 275, 80], [247, 87, 275, 81], [247, 88, 275, 82], [247, 89, 275, 83], [248, 6, 276, 4], [248, 12, 276, 10, "x2"], [248, 14, 276, 12], [248, 17, 276, 15, "Math"], [248, 21, 276, 19], [248, 22, 276, 20, "min"], [248, 25, 276, 23], [248, 26, 276, 24, "box1"], [248, 30, 276, 28], [248, 31, 276, 29, "xCenter"], [248, 38, 276, 36], [248, 41, 276, 39, "box1"], [248, 45, 276, 43], [248, 46, 276, 44, "width"], [248, 51, 276, 49], [248, 54, 276, 50], [248, 55, 276, 51], [248, 57, 276, 53, "box2"], [248, 61, 276, 57], [248, 62, 276, 58, "xCenter"], [248, 69, 276, 65], [248, 72, 276, 68, "box2"], [248, 76, 276, 72], [248, 77, 276, 73, "width"], [248, 82, 276, 78], [248, 85, 276, 79], [248, 86, 276, 80], [248, 87, 276, 81], [249, 6, 277, 4], [249, 12, 277, 10, "y2"], [249, 14, 277, 12], [249, 17, 277, 15, "Math"], [249, 21, 277, 19], [249, 22, 277, 20, "min"], [249, 25, 277, 23], [249, 26, 277, 24, "box1"], [249, 30, 277, 28], [249, 31, 277, 29, "yCenter"], [249, 38, 277, 36], [249, 41, 277, 39, "box1"], [249, 45, 277, 43], [249, 46, 277, 44, "height"], [249, 52, 277, 50], [249, 55, 277, 51], [249, 56, 277, 52], [249, 58, 277, 54, "box2"], [249, 62, 277, 58], [249, 63, 277, 59, "yCenter"], [249, 70, 277, 66], [249, 73, 277, 69, "box2"], [249, 77, 277, 73], [249, 78, 277, 74, "height"], [249, 84, 277, 80], [249, 87, 277, 81], [249, 88, 277, 82], [249, 89, 277, 83], [250, 6, 279, 4], [250, 10, 279, 8, "x2"], [250, 12, 279, 10], [250, 16, 279, 14, "x1"], [250, 18, 279, 16], [250, 22, 279, 20, "y2"], [250, 24, 279, 22], [250, 28, 279, 26, "y1"], [250, 30, 279, 28], [250, 32, 279, 30], [250, 39, 279, 37], [250, 40, 279, 38], [251, 6, 281, 4], [251, 12, 281, 10, "overlapArea"], [251, 23, 281, 21], [251, 26, 281, 24], [251, 27, 281, 25, "x2"], [251, 29, 281, 27], [251, 32, 281, 30, "x1"], [251, 34, 281, 32], [251, 39, 281, 37, "y2"], [251, 41, 281, 39], [251, 44, 281, 42, "y1"], [251, 46, 281, 44], [251, 47, 281, 45], [252, 6, 282, 4], [252, 12, 282, 10, "box1Area"], [252, 20, 282, 18], [252, 23, 282, 21, "box1"], [252, 27, 282, 25], [252, 28, 282, 26, "width"], [252, 33, 282, 31], [252, 36, 282, 34, "box1"], [252, 40, 282, 38], [252, 41, 282, 39, "height"], [252, 47, 282, 45], [253, 6, 283, 4], [253, 12, 283, 10, "box2Area"], [253, 20, 283, 18], [253, 23, 283, 21, "box2"], [253, 27, 283, 25], [253, 28, 283, 26, "width"], [253, 33, 283, 31], [253, 36, 283, 34, "box2"], [253, 40, 283, 38], [253, 41, 283, 39, "height"], [253, 47, 283, 45], [254, 6, 285, 4], [254, 13, 285, 11, "overlapArea"], [254, 24, 285, 22], [254, 27, 285, 25, "Math"], [254, 31, 285, 29], [254, 32, 285, 30, "min"], [254, 35, 285, 33], [254, 36, 285, 34, "box1Area"], [254, 44, 285, 42], [254, 46, 285, 44, "box2Area"], [254, 54, 285, 52], [254, 55, 285, 53], [255, 4, 286, 2], [255, 5, 286, 3], [256, 4, 288, 2], [256, 10, 288, 8, "mergeTwoFaces"], [256, 23, 288, 21], [256, 26, 288, 24, "mergeTwoFaces"], [256, 27, 288, 25, "face1"], [256, 32, 288, 35], [256, 34, 288, 37, "face2"], [256, 39, 288, 47], [256, 44, 288, 52], [257, 6, 289, 4], [257, 12, 289, 10, "box1"], [257, 16, 289, 14], [257, 19, 289, 17, "face1"], [257, 24, 289, 22], [257, 25, 289, 23, "boundingBox"], [257, 36, 289, 34], [258, 6, 290, 4], [258, 12, 290, 10, "box2"], [258, 16, 290, 14], [258, 19, 290, 17, "face2"], [258, 24, 290, 22], [258, 25, 290, 23, "boundingBox"], [258, 36, 290, 34], [259, 6, 292, 4], [259, 12, 292, 10, "left"], [259, 16, 292, 14], [259, 19, 292, 17, "Math"], [259, 23, 292, 21], [259, 24, 292, 22, "min"], [259, 27, 292, 25], [259, 28, 292, 26, "box1"], [259, 32, 292, 30], [259, 33, 292, 31, "xCenter"], [259, 40, 292, 38], [259, 43, 292, 41, "box1"], [259, 47, 292, 45], [259, 48, 292, 46, "width"], [259, 53, 292, 51], [259, 56, 292, 52], [259, 57, 292, 53], [259, 59, 292, 55, "box2"], [259, 63, 292, 59], [259, 64, 292, 60, "xCenter"], [259, 71, 292, 67], [259, 74, 292, 70, "box2"], [259, 78, 292, 74], [259, 79, 292, 75, "width"], [259, 84, 292, 80], [259, 87, 292, 81], [259, 88, 292, 82], [259, 89, 292, 83], [260, 6, 293, 4], [260, 12, 293, 10, "right"], [260, 17, 293, 15], [260, 20, 293, 18, "Math"], [260, 24, 293, 22], [260, 25, 293, 23, "max"], [260, 28, 293, 26], [260, 29, 293, 27, "box1"], [260, 33, 293, 31], [260, 34, 293, 32, "xCenter"], [260, 41, 293, 39], [260, 44, 293, 42, "box1"], [260, 48, 293, 46], [260, 49, 293, 47, "width"], [260, 54, 293, 52], [260, 57, 293, 53], [260, 58, 293, 54], [260, 60, 293, 56, "box2"], [260, 64, 293, 60], [260, 65, 293, 61, "xCenter"], [260, 72, 293, 68], [260, 75, 293, 71, "box2"], [260, 79, 293, 75], [260, 80, 293, 76, "width"], [260, 85, 293, 81], [260, 88, 293, 82], [260, 89, 293, 83], [260, 90, 293, 84], [261, 6, 294, 4], [261, 12, 294, 10, "top"], [261, 15, 294, 13], [261, 18, 294, 16, "Math"], [261, 22, 294, 20], [261, 23, 294, 21, "min"], [261, 26, 294, 24], [261, 27, 294, 25, "box1"], [261, 31, 294, 29], [261, 32, 294, 30, "yCenter"], [261, 39, 294, 37], [261, 42, 294, 40, "box1"], [261, 46, 294, 44], [261, 47, 294, 45, "height"], [261, 53, 294, 51], [261, 56, 294, 52], [261, 57, 294, 53], [261, 59, 294, 55, "box2"], [261, 63, 294, 59], [261, 64, 294, 60, "yCenter"], [261, 71, 294, 67], [261, 74, 294, 70, "box2"], [261, 78, 294, 74], [261, 79, 294, 75, "height"], [261, 85, 294, 81], [261, 88, 294, 82], [261, 89, 294, 83], [261, 90, 294, 84], [262, 6, 295, 4], [262, 12, 295, 10, "bottom"], [262, 18, 295, 16], [262, 21, 295, 19, "Math"], [262, 25, 295, 23], [262, 26, 295, 24, "max"], [262, 29, 295, 27], [262, 30, 295, 28, "box1"], [262, 34, 295, 32], [262, 35, 295, 33, "yCenter"], [262, 42, 295, 40], [262, 45, 295, 43, "box1"], [262, 49, 295, 47], [262, 50, 295, 48, "height"], [262, 56, 295, 54], [262, 59, 295, 55], [262, 60, 295, 56], [262, 62, 295, 58, "box2"], [262, 66, 295, 62], [262, 67, 295, 63, "yCenter"], [262, 74, 295, 70], [262, 77, 295, 73, "box2"], [262, 81, 295, 77], [262, 82, 295, 78, "height"], [262, 88, 295, 84], [262, 91, 295, 85], [262, 92, 295, 86], [262, 93, 295, 87], [263, 6, 297, 4], [263, 13, 297, 11], [264, 8, 298, 6, "boundingBox"], [264, 19, 298, 17], [264, 21, 298, 19], [265, 10, 299, 8, "xCenter"], [265, 17, 299, 15], [265, 19, 299, 17], [265, 20, 299, 18, "left"], [265, 24, 299, 22], [265, 27, 299, 25, "right"], [265, 32, 299, 30], [265, 36, 299, 34], [265, 37, 299, 35], [266, 10, 300, 8, "yCenter"], [266, 17, 300, 15], [266, 19, 300, 17], [266, 20, 300, 18, "top"], [266, 23, 300, 21], [266, 26, 300, 24, "bottom"], [266, 32, 300, 30], [266, 36, 300, 34], [266, 37, 300, 35], [267, 10, 301, 8, "width"], [267, 15, 301, 13], [267, 17, 301, 15, "right"], [267, 22, 301, 20], [267, 25, 301, 23, "left"], [267, 29, 301, 27], [268, 10, 302, 8, "height"], [268, 16, 302, 14], [268, 18, 302, 16, "bottom"], [268, 24, 302, 22], [268, 27, 302, 25, "top"], [269, 8, 303, 6], [270, 6, 304, 4], [270, 7, 304, 5], [271, 4, 305, 2], [271, 5, 305, 3], [273, 4, 307, 2], [274, 4, 308, 2], [274, 10, 308, 8, "applyStrongBlur"], [274, 25, 308, 23], [274, 28, 308, 26, "applyStrongBlur"], [274, 29, 308, 27, "ctx"], [274, 32, 308, 56], [274, 34, 308, 58, "x"], [274, 35, 308, 67], [274, 37, 308, 69, "y"], [274, 38, 308, 78], [274, 40, 308, 80, "width"], [274, 45, 308, 93], [274, 47, 308, 95, "height"], [274, 53, 308, 109], [274, 58, 308, 114], [275, 6, 309, 4], [276, 6, 310, 4], [276, 12, 310, 10, "imageData"], [276, 21, 310, 19], [276, 24, 310, 22, "ctx"], [276, 27, 310, 25], [276, 28, 310, 26, "getImageData"], [276, 40, 310, 38], [276, 41, 310, 39, "x"], [276, 42, 310, 40], [276, 44, 310, 42, "y"], [276, 45, 310, 43], [276, 47, 310, 45, "width"], [276, 52, 310, 50], [276, 54, 310, 52, "height"], [276, 60, 310, 58], [276, 61, 310, 59], [277, 6, 311, 4], [277, 12, 311, 10, "data"], [277, 16, 311, 14], [277, 19, 311, 17, "imageData"], [277, 28, 311, 26], [277, 29, 311, 27, "data"], [277, 33, 311, 31], [279, 6, 313, 4], [281, 6, 315, 4], [282, 6, 316, 4], [282, 12, 316, 10, "pixelSize"], [282, 21, 316, 19], [282, 24, 316, 22, "Math"], [282, 28, 316, 26], [282, 29, 316, 27, "max"], [282, 32, 316, 30], [282, 33, 316, 31], [282, 35, 316, 33], [282, 37, 316, 35, "Math"], [282, 41, 316, 39], [282, 42, 316, 40, "min"], [282, 45, 316, 43], [282, 46, 316, 44, "width"], [282, 51, 316, 49], [282, 53, 316, 51, "height"], [282, 59, 316, 57], [282, 60, 316, 58], [282, 63, 316, 61], [282, 64, 316, 62], [282, 65, 316, 63], [283, 6, 317, 4, "console"], [283, 13, 317, 11], [283, 14, 317, 12, "log"], [283, 17, 317, 15], [283, 18, 317, 16], [283, 77, 317, 75, "pixelSize"], [283, 86, 317, 84], [283, 90, 317, 88], [283, 91, 317, 89], [284, 6, 319, 4], [284, 11, 319, 9], [284, 15, 319, 13, "py"], [284, 17, 319, 15], [284, 20, 319, 18], [284, 21, 319, 19], [284, 23, 319, 21, "py"], [284, 25, 319, 23], [284, 28, 319, 26, "height"], [284, 34, 319, 32], [284, 36, 319, 34, "py"], [284, 38, 319, 36], [284, 42, 319, 40, "pixelSize"], [284, 51, 319, 49], [284, 53, 319, 51], [285, 8, 320, 6], [285, 13, 320, 11], [285, 17, 320, 15, "px"], [285, 19, 320, 17], [285, 22, 320, 20], [285, 23, 320, 21], [285, 25, 320, 23, "px"], [285, 27, 320, 25], [285, 30, 320, 28, "width"], [285, 35, 320, 33], [285, 37, 320, 35, "px"], [285, 39, 320, 37], [285, 43, 320, 41, "pixelSize"], [285, 52, 320, 50], [285, 54, 320, 52], [286, 10, 321, 8], [287, 10, 322, 8], [287, 14, 322, 12, "r"], [287, 15, 322, 13], [287, 18, 322, 16], [287, 19, 322, 17], [288, 12, 322, 19, "g"], [288, 13, 322, 20], [288, 16, 322, 23], [288, 17, 322, 24], [289, 12, 322, 26, "b"], [289, 13, 322, 27], [289, 16, 322, 30], [289, 17, 322, 31], [290, 12, 322, 33, "count"], [290, 17, 322, 38], [290, 20, 322, 41], [290, 21, 322, 42], [291, 10, 324, 8], [291, 15, 324, 13], [291, 19, 324, 17, "dy"], [291, 21, 324, 19], [291, 24, 324, 22], [291, 25, 324, 23], [291, 27, 324, 25, "dy"], [291, 29, 324, 27], [291, 32, 324, 30, "pixelSize"], [291, 41, 324, 39], [291, 45, 324, 43, "py"], [291, 47, 324, 45], [291, 50, 324, 48, "dy"], [291, 52, 324, 50], [291, 55, 324, 53, "height"], [291, 61, 324, 59], [291, 63, 324, 61, "dy"], [291, 65, 324, 63], [291, 67, 324, 65], [291, 69, 324, 67], [292, 12, 325, 10], [292, 17, 325, 15], [292, 21, 325, 19, "dx"], [292, 23, 325, 21], [292, 26, 325, 24], [292, 27, 325, 25], [292, 29, 325, 27, "dx"], [292, 31, 325, 29], [292, 34, 325, 32, "pixelSize"], [292, 43, 325, 41], [292, 47, 325, 45, "px"], [292, 49, 325, 47], [292, 52, 325, 50, "dx"], [292, 54, 325, 52], [292, 57, 325, 55, "width"], [292, 62, 325, 60], [292, 64, 325, 62, "dx"], [292, 66, 325, 64], [292, 68, 325, 66], [292, 70, 325, 68], [293, 14, 326, 12], [293, 20, 326, 18, "index"], [293, 25, 326, 23], [293, 28, 326, 26], [293, 29, 326, 27], [293, 30, 326, 28, "py"], [293, 32, 326, 30], [293, 35, 326, 33, "dy"], [293, 37, 326, 35], [293, 41, 326, 39, "width"], [293, 46, 326, 44], [293, 50, 326, 48, "px"], [293, 52, 326, 50], [293, 55, 326, 53, "dx"], [293, 57, 326, 55], [293, 58, 326, 56], [293, 62, 326, 60], [293, 63, 326, 61], [294, 14, 327, 12, "r"], [294, 15, 327, 13], [294, 19, 327, 17, "data"], [294, 23, 327, 21], [294, 24, 327, 22, "index"], [294, 29, 327, 27], [294, 30, 327, 28], [295, 14, 328, 12, "g"], [295, 15, 328, 13], [295, 19, 328, 17, "data"], [295, 23, 328, 21], [295, 24, 328, 22, "index"], [295, 29, 328, 27], [295, 32, 328, 30], [295, 33, 328, 31], [295, 34, 328, 32], [296, 14, 329, 12, "b"], [296, 15, 329, 13], [296, 19, 329, 17, "data"], [296, 23, 329, 21], [296, 24, 329, 22, "index"], [296, 29, 329, 27], [296, 32, 329, 30], [296, 33, 329, 31], [296, 34, 329, 32], [297, 14, 330, 12, "count"], [297, 19, 330, 17], [297, 21, 330, 19], [298, 12, 331, 10], [299, 10, 332, 8], [300, 10, 334, 8], [300, 14, 334, 12, "count"], [300, 19, 334, 17], [300, 22, 334, 20], [300, 23, 334, 21], [300, 25, 334, 23], [301, 12, 335, 10, "r"], [301, 13, 335, 11], [301, 16, 335, 14, "Math"], [301, 20, 335, 18], [301, 21, 335, 19, "floor"], [301, 26, 335, 24], [301, 27, 335, 25, "r"], [301, 28, 335, 26], [301, 31, 335, 29, "count"], [301, 36, 335, 34], [301, 37, 335, 35], [302, 12, 336, 10, "g"], [302, 13, 336, 11], [302, 16, 336, 14, "Math"], [302, 20, 336, 18], [302, 21, 336, 19, "floor"], [302, 26, 336, 24], [302, 27, 336, 25, "g"], [302, 28, 336, 26], [302, 31, 336, 29, "count"], [302, 36, 336, 34], [302, 37, 336, 35], [303, 12, 337, 10, "b"], [303, 13, 337, 11], [303, 16, 337, 14, "Math"], [303, 20, 337, 18], [303, 21, 337, 19, "floor"], [303, 26, 337, 24], [303, 27, 337, 25, "b"], [303, 28, 337, 26], [303, 31, 337, 29, "count"], [303, 36, 337, 34], [303, 37, 337, 35], [305, 12, 339, 10], [306, 12, 340, 10], [306, 17, 340, 15], [306, 21, 340, 19, "dy"], [306, 23, 340, 21], [306, 26, 340, 24], [306, 27, 340, 25], [306, 29, 340, 27, "dy"], [306, 31, 340, 29], [306, 34, 340, 32, "pixelSize"], [306, 43, 340, 41], [306, 47, 340, 45, "py"], [306, 49, 340, 47], [306, 52, 340, 50, "dy"], [306, 54, 340, 52], [306, 57, 340, 55, "height"], [306, 63, 340, 61], [306, 65, 340, 63, "dy"], [306, 67, 340, 65], [306, 69, 340, 67], [306, 71, 340, 69], [307, 14, 341, 12], [307, 19, 341, 17], [307, 23, 341, 21, "dx"], [307, 25, 341, 23], [307, 28, 341, 26], [307, 29, 341, 27], [307, 31, 341, 29, "dx"], [307, 33, 341, 31], [307, 36, 341, 34, "pixelSize"], [307, 45, 341, 43], [307, 49, 341, 47, "px"], [307, 51, 341, 49], [307, 54, 341, 52, "dx"], [307, 56, 341, 54], [307, 59, 341, 57, "width"], [307, 64, 341, 62], [307, 66, 341, 64, "dx"], [307, 68, 341, 66], [307, 70, 341, 68], [307, 72, 341, 70], [308, 16, 342, 14], [308, 22, 342, 20, "index"], [308, 27, 342, 25], [308, 30, 342, 28], [308, 31, 342, 29], [308, 32, 342, 30, "py"], [308, 34, 342, 32], [308, 37, 342, 35, "dy"], [308, 39, 342, 37], [308, 43, 342, 41, "width"], [308, 48, 342, 46], [308, 52, 342, 50, "px"], [308, 54, 342, 52], [308, 57, 342, 55, "dx"], [308, 59, 342, 57], [308, 60, 342, 58], [308, 64, 342, 62], [308, 65, 342, 63], [309, 16, 343, 14, "data"], [309, 20, 343, 18], [309, 21, 343, 19, "index"], [309, 26, 343, 24], [309, 27, 343, 25], [309, 30, 343, 28, "r"], [309, 31, 343, 29], [310, 16, 344, 14, "data"], [310, 20, 344, 18], [310, 21, 344, 19, "index"], [310, 26, 344, 24], [310, 29, 344, 27], [310, 30, 344, 28], [310, 31, 344, 29], [310, 34, 344, 32, "g"], [310, 35, 344, 33], [311, 16, 345, 14, "data"], [311, 20, 345, 18], [311, 21, 345, 19, "index"], [311, 26, 345, 24], [311, 29, 345, 27], [311, 30, 345, 28], [311, 31, 345, 29], [311, 34, 345, 32, "b"], [311, 35, 345, 33], [312, 16, 346, 14], [313, 14, 347, 12], [314, 12, 348, 10], [315, 10, 349, 8], [316, 8, 350, 6], [317, 6, 351, 4], [319, 6, 353, 4], [320, 6, 354, 4, "console"], [320, 13, 354, 11], [320, 14, 354, 12, "log"], [320, 17, 354, 15], [320, 18, 354, 16], [320, 71, 354, 69], [320, 72, 354, 70], [321, 6, 355, 4], [321, 11, 355, 9], [321, 15, 355, 13, "i"], [321, 16, 355, 14], [321, 19, 355, 17], [321, 20, 355, 18], [321, 22, 355, 20, "i"], [321, 23, 355, 21], [321, 26, 355, 24], [321, 27, 355, 25], [321, 29, 355, 27, "i"], [321, 30, 355, 28], [321, 32, 355, 30], [321, 34, 355, 32], [322, 8, 355, 34], [323, 8, 356, 6, "applySimpleBlur"], [323, 23, 356, 21], [323, 24, 356, 22, "data"], [323, 28, 356, 26], [323, 30, 356, 28, "width"], [323, 35, 356, 33], [323, 37, 356, 35, "height"], [323, 43, 356, 41], [323, 44, 356, 42], [324, 6, 357, 4], [326, 6, 359, 4], [327, 6, 360, 4, "ctx"], [327, 9, 360, 7], [327, 10, 360, 8, "putImageData"], [327, 22, 360, 20], [327, 23, 360, 21, "imageData"], [327, 32, 360, 30], [327, 34, 360, 32, "x"], [327, 35, 360, 33], [327, 37, 360, 35, "y"], [327, 38, 360, 36], [327, 39, 360, 37], [328, 4, 361, 2], [328, 5, 361, 3], [329, 4, 363, 2], [329, 10, 363, 8, "applySimpleBlur"], [329, 25, 363, 23], [329, 28, 363, 26, "applySimpleBlur"], [329, 29, 363, 27, "data"], [329, 33, 363, 50], [329, 35, 363, 52, "width"], [329, 40, 363, 65], [329, 42, 363, 67, "height"], [329, 48, 363, 81], [329, 53, 363, 86], [330, 6, 364, 4], [330, 12, 364, 10, "original"], [330, 20, 364, 18], [330, 23, 364, 21], [330, 27, 364, 25, "Uint8ClampedArray"], [330, 44, 364, 42], [330, 45, 364, 43, "data"], [330, 49, 364, 47], [330, 50, 364, 48], [331, 6, 366, 4], [331, 11, 366, 9], [331, 15, 366, 13, "y"], [331, 16, 366, 14], [331, 19, 366, 17], [331, 20, 366, 18], [331, 22, 366, 20, "y"], [331, 23, 366, 21], [331, 26, 366, 24, "height"], [331, 32, 366, 30], [331, 35, 366, 33], [331, 36, 366, 34], [331, 38, 366, 36, "y"], [331, 39, 366, 37], [331, 41, 366, 39], [331, 43, 366, 41], [332, 8, 367, 6], [332, 13, 367, 11], [332, 17, 367, 15, "x"], [332, 18, 367, 16], [332, 21, 367, 19], [332, 22, 367, 20], [332, 24, 367, 22, "x"], [332, 25, 367, 23], [332, 28, 367, 26, "width"], [332, 33, 367, 31], [332, 36, 367, 34], [332, 37, 367, 35], [332, 39, 367, 37, "x"], [332, 40, 367, 38], [332, 42, 367, 40], [332, 44, 367, 42], [333, 10, 368, 8], [333, 16, 368, 14, "index"], [333, 21, 368, 19], [333, 24, 368, 22], [333, 25, 368, 23, "y"], [333, 26, 368, 24], [333, 29, 368, 27, "width"], [333, 34, 368, 32], [333, 37, 368, 35, "x"], [333, 38, 368, 36], [333, 42, 368, 40], [333, 43, 368, 41], [335, 10, 370, 8], [336, 10, 371, 8], [336, 14, 371, 12, "r"], [336, 15, 371, 13], [336, 18, 371, 16], [336, 19, 371, 17], [337, 12, 371, 19, "g"], [337, 13, 371, 20], [337, 16, 371, 23], [337, 17, 371, 24], [338, 12, 371, 26, "b"], [338, 13, 371, 27], [338, 16, 371, 30], [338, 17, 371, 31], [339, 10, 372, 8], [339, 15, 372, 13], [339, 19, 372, 17, "dy"], [339, 21, 372, 19], [339, 24, 372, 22], [339, 25, 372, 23], [339, 26, 372, 24], [339, 28, 372, 26, "dy"], [339, 30, 372, 28], [339, 34, 372, 32], [339, 35, 372, 33], [339, 37, 372, 35, "dy"], [339, 39, 372, 37], [339, 41, 372, 39], [339, 43, 372, 41], [340, 12, 373, 10], [340, 17, 373, 15], [340, 21, 373, 19, "dx"], [340, 23, 373, 21], [340, 26, 373, 24], [340, 27, 373, 25], [340, 28, 373, 26], [340, 30, 373, 28, "dx"], [340, 32, 373, 30], [340, 36, 373, 34], [340, 37, 373, 35], [340, 39, 373, 37, "dx"], [340, 41, 373, 39], [340, 43, 373, 41], [340, 45, 373, 43], [341, 14, 374, 12], [341, 20, 374, 18, "neighborIndex"], [341, 33, 374, 31], [341, 36, 374, 34], [341, 37, 374, 35], [341, 38, 374, 36, "y"], [341, 39, 374, 37], [341, 42, 374, 40, "dy"], [341, 44, 374, 42], [341, 48, 374, 46, "width"], [341, 53, 374, 51], [341, 57, 374, 55, "x"], [341, 58, 374, 56], [341, 61, 374, 59, "dx"], [341, 63, 374, 61], [341, 64, 374, 62], [341, 68, 374, 66], [341, 69, 374, 67], [342, 14, 375, 12, "r"], [342, 15, 375, 13], [342, 19, 375, 17, "original"], [342, 27, 375, 25], [342, 28, 375, 26, "neighborIndex"], [342, 41, 375, 39], [342, 42, 375, 40], [343, 14, 376, 12, "g"], [343, 15, 376, 13], [343, 19, 376, 17, "original"], [343, 27, 376, 25], [343, 28, 376, 26, "neighborIndex"], [343, 41, 376, 39], [343, 44, 376, 42], [343, 45, 376, 43], [343, 46, 376, 44], [344, 14, 377, 12, "b"], [344, 15, 377, 13], [344, 19, 377, 17, "original"], [344, 27, 377, 25], [344, 28, 377, 26, "neighborIndex"], [344, 41, 377, 39], [344, 44, 377, 42], [344, 45, 377, 43], [344, 46, 377, 44], [345, 12, 378, 10], [346, 10, 379, 8], [347, 10, 381, 8, "data"], [347, 14, 381, 12], [347, 15, 381, 13, "index"], [347, 20, 381, 18], [347, 21, 381, 19], [347, 24, 381, 22, "r"], [347, 25, 381, 23], [347, 28, 381, 26], [347, 29, 381, 27], [348, 10, 382, 8, "data"], [348, 14, 382, 12], [348, 15, 382, 13, "index"], [348, 20, 382, 18], [348, 23, 382, 21], [348, 24, 382, 22], [348, 25, 382, 23], [348, 28, 382, 26, "g"], [348, 29, 382, 27], [348, 32, 382, 30], [348, 33, 382, 31], [349, 10, 383, 8, "data"], [349, 14, 383, 12], [349, 15, 383, 13, "index"], [349, 20, 383, 18], [349, 23, 383, 21], [349, 24, 383, 22], [349, 25, 383, 23], [349, 28, 383, 26, "b"], [349, 29, 383, 27], [349, 32, 383, 30], [349, 33, 383, 31], [350, 8, 384, 6], [351, 6, 385, 4], [352, 4, 386, 2], [352, 5, 386, 3], [353, 4, 388, 2], [353, 10, 388, 8, "applyFallbackFaceBlur"], [353, 31, 388, 29], [353, 34, 388, 32, "applyFallbackFaceBlur"], [353, 35, 388, 33, "ctx"], [353, 38, 388, 62], [353, 40, 388, 64, "imgWidth"], [353, 48, 388, 80], [353, 50, 388, 82, "imgHeight"], [353, 59, 388, 99], [353, 64, 388, 104], [354, 6, 389, 4, "console"], [354, 13, 389, 11], [354, 14, 389, 12, "log"], [354, 17, 389, 15], [354, 18, 389, 16], [354, 90, 389, 88], [354, 91, 389, 89], [356, 6, 391, 4], [357, 6, 392, 4], [357, 12, 392, 10, "areas"], [357, 17, 392, 15], [357, 20, 392, 18], [358, 6, 393, 6], [359, 6, 394, 6], [360, 8, 394, 8, "x"], [360, 9, 394, 9], [360, 11, 394, 11, "imgWidth"], [360, 19, 394, 19], [360, 22, 394, 22], [360, 26, 394, 26], [361, 8, 394, 28, "y"], [361, 9, 394, 29], [361, 11, 394, 31, "imgHeight"], [361, 20, 394, 40], [361, 23, 394, 43], [361, 27, 394, 47], [362, 8, 394, 49, "w"], [362, 9, 394, 50], [362, 11, 394, 52, "imgWidth"], [362, 19, 394, 60], [362, 22, 394, 63], [362, 25, 394, 66], [363, 8, 394, 68, "h"], [363, 9, 394, 69], [363, 11, 394, 71, "imgHeight"], [363, 20, 394, 80], [363, 23, 394, 83], [364, 6, 394, 87], [364, 7, 394, 88], [365, 6, 395, 6], [366, 6, 396, 6], [367, 8, 396, 8, "x"], [367, 9, 396, 9], [367, 11, 396, 11, "imgWidth"], [367, 19, 396, 19], [367, 22, 396, 22], [367, 25, 396, 25], [368, 8, 396, 27, "y"], [368, 9, 396, 28], [368, 11, 396, 30, "imgHeight"], [368, 20, 396, 39], [368, 23, 396, 42], [368, 26, 396, 45], [369, 8, 396, 47, "w"], [369, 9, 396, 48], [369, 11, 396, 50, "imgWidth"], [369, 19, 396, 58], [369, 22, 396, 61], [369, 26, 396, 65], [370, 8, 396, 67, "h"], [370, 9, 396, 68], [370, 11, 396, 70, "imgHeight"], [370, 20, 396, 79], [370, 23, 396, 82], [371, 6, 396, 86], [371, 7, 396, 87], [372, 6, 397, 6], [373, 6, 398, 6], [374, 8, 398, 8, "x"], [374, 9, 398, 9], [374, 11, 398, 11, "imgWidth"], [374, 19, 398, 19], [374, 22, 398, 22], [374, 26, 398, 26], [375, 8, 398, 28, "y"], [375, 9, 398, 29], [375, 11, 398, 31, "imgHeight"], [375, 20, 398, 40], [375, 23, 398, 43], [375, 26, 398, 46], [376, 8, 398, 48, "w"], [376, 9, 398, 49], [376, 11, 398, 51, "imgWidth"], [376, 19, 398, 59], [376, 22, 398, 62], [376, 26, 398, 66], [377, 8, 398, 68, "h"], [377, 9, 398, 69], [377, 11, 398, 71, "imgHeight"], [377, 20, 398, 80], [377, 23, 398, 83], [378, 6, 398, 87], [378, 7, 398, 88], [378, 8, 399, 5], [379, 6, 401, 4, "areas"], [379, 11, 401, 9], [379, 12, 401, 10, "for<PERSON>ach"], [379, 19, 401, 17], [379, 20, 401, 18], [379, 21, 401, 19, "area"], [379, 25, 401, 23], [379, 27, 401, 25, "index"], [379, 32, 401, 30], [379, 37, 401, 35], [380, 8, 402, 6, "console"], [380, 15, 402, 13], [380, 16, 402, 14, "log"], [380, 19, 402, 17], [380, 20, 402, 18], [380, 65, 402, 63, "index"], [380, 70, 402, 68], [380, 73, 402, 71], [380, 74, 402, 72], [380, 77, 402, 75], [380, 79, 402, 77, "area"], [380, 83, 402, 81], [380, 84, 402, 82], [381, 8, 403, 6, "applyStrongBlur"], [381, 23, 403, 21], [381, 24, 403, 22, "ctx"], [381, 27, 403, 25], [381, 29, 403, 27, "area"], [381, 33, 403, 31], [381, 34, 403, 32, "x"], [381, 35, 403, 33], [381, 37, 403, 35, "area"], [381, 41, 403, 39], [381, 42, 403, 40, "y"], [381, 43, 403, 41], [381, 45, 403, 43, "area"], [381, 49, 403, 47], [381, 50, 403, 48, "w"], [381, 51, 403, 49], [381, 53, 403, 51, "area"], [381, 57, 403, 55], [381, 58, 403, 56, "h"], [381, 59, 403, 57], [381, 60, 403, 58], [382, 6, 404, 4], [382, 7, 404, 5], [382, 8, 404, 6], [383, 4, 405, 2], [383, 5, 405, 3], [385, 4, 407, 2], [386, 4, 408, 2], [386, 10, 408, 8, "capturePhoto"], [386, 22, 408, 20], [386, 25, 408, 23], [386, 29, 408, 23, "useCallback"], [386, 47, 408, 34], [386, 49, 408, 35], [386, 61, 408, 47], [387, 6, 409, 4], [388, 6, 410, 4], [388, 12, 410, 10, "isDev"], [388, 17, 410, 15], [388, 20, 410, 18, "process"], [388, 27, 410, 25], [388, 28, 410, 26, "env"], [388, 31, 410, 29], [388, 32, 410, 30, "NODE_ENV"], [388, 40, 410, 38], [388, 45, 410, 43], [388, 58, 410, 56], [388, 62, 410, 60, "__DEV__"], [388, 69, 410, 67], [389, 6, 412, 4], [389, 10, 412, 8], [389, 11, 412, 9, "cameraRef"], [389, 20, 412, 18], [389, 21, 412, 19, "current"], [389, 28, 412, 26], [389, 32, 412, 30], [389, 33, 412, 31, "isDev"], [389, 38, 412, 36], [389, 40, 412, 38], [390, 8, 413, 6, "<PERSON><PERSON>"], [390, 22, 413, 11], [390, 23, 413, 12, "alert"], [390, 28, 413, 17], [390, 29, 413, 18], [390, 36, 413, 25], [390, 38, 413, 27], [390, 56, 413, 45], [390, 57, 413, 46], [391, 8, 414, 6], [392, 6, 415, 4], [393, 6, 416, 4], [393, 10, 416, 8], [394, 8, 417, 6, "setProcessingState"], [394, 26, 417, 24], [394, 27, 417, 25], [394, 38, 417, 36], [394, 39, 417, 37], [395, 8, 418, 6, "setProcessingProgress"], [395, 29, 418, 27], [395, 30, 418, 28], [395, 32, 418, 30], [395, 33, 418, 31], [396, 8, 419, 6], [397, 8, 420, 6], [398, 8, 421, 6], [399, 8, 422, 6], [399, 14, 422, 12], [399, 18, 422, 16, "Promise"], [399, 25, 422, 23], [399, 26, 422, 24, "resolve"], [399, 33, 422, 31], [399, 37, 422, 35, "setTimeout"], [399, 47, 422, 45], [399, 48, 422, 46, "resolve"], [399, 55, 422, 53], [399, 57, 422, 55], [399, 59, 422, 57], [399, 60, 422, 58], [399, 61, 422, 59], [400, 8, 423, 6], [401, 8, 424, 6], [401, 12, 424, 10, "photo"], [401, 17, 424, 15], [402, 8, 426, 6], [402, 12, 426, 10], [403, 10, 427, 8, "photo"], [403, 15, 427, 13], [403, 18, 427, 16], [403, 24, 427, 22, "cameraRef"], [403, 33, 427, 31], [403, 34, 427, 32, "current"], [403, 41, 427, 39], [403, 42, 427, 40, "takePictureAsync"], [403, 58, 427, 56], [403, 59, 427, 57], [404, 12, 428, 10, "quality"], [404, 19, 428, 17], [404, 21, 428, 19], [404, 24, 428, 22], [405, 12, 429, 10, "base64"], [405, 18, 429, 16], [405, 20, 429, 18], [405, 25, 429, 23], [406, 12, 430, 10, "skipProcessing"], [406, 26, 430, 24], [406, 28, 430, 26], [406, 32, 430, 30], [406, 33, 430, 32], [407, 10, 431, 8], [407, 11, 431, 9], [407, 12, 431, 10], [408, 8, 432, 6], [408, 9, 432, 7], [408, 10, 432, 8], [408, 17, 432, 15, "cameraError"], [408, 28, 432, 26], [408, 30, 432, 28], [409, 10, 433, 8, "console"], [409, 17, 433, 15], [409, 18, 433, 16, "log"], [409, 21, 433, 19], [409, 22, 433, 20], [409, 82, 433, 80], [409, 84, 433, 82, "cameraError"], [409, 95, 433, 93], [409, 96, 433, 94], [410, 10, 434, 8], [411, 10, 435, 8], [411, 14, 435, 12, "isDev"], [411, 19, 435, 17], [411, 21, 435, 19], [412, 12, 436, 10, "photo"], [412, 17, 436, 15], [412, 20, 436, 18], [413, 14, 437, 12, "uri"], [413, 17, 437, 15], [413, 19, 437, 17], [414, 12, 438, 10], [414, 13, 438, 11], [415, 10, 439, 8], [415, 11, 439, 9], [415, 17, 439, 15], [416, 12, 440, 10], [416, 18, 440, 16, "cameraError"], [416, 29, 440, 27], [417, 10, 441, 8], [418, 8, 442, 6], [419, 8, 443, 6], [419, 12, 443, 10], [419, 13, 443, 11, "photo"], [419, 18, 443, 16], [419, 20, 443, 18], [420, 10, 444, 8], [420, 16, 444, 14], [420, 20, 444, 18, "Error"], [420, 25, 444, 23], [420, 26, 444, 24], [420, 51, 444, 49], [420, 52, 444, 50], [421, 8, 445, 6], [422, 8, 446, 6, "console"], [422, 15, 446, 13], [422, 16, 446, 14, "log"], [422, 19, 446, 17], [422, 20, 446, 18], [422, 56, 446, 54], [422, 58, 446, 56, "photo"], [422, 63, 446, 61], [422, 64, 446, 62, "uri"], [422, 67, 446, 65], [422, 68, 446, 66], [423, 8, 447, 6, "setCapturedPhoto"], [423, 24, 447, 22], [423, 25, 447, 23, "photo"], [423, 30, 447, 28], [423, 31, 447, 29, "uri"], [423, 34, 447, 32], [423, 35, 447, 33], [424, 8, 448, 6, "setProcessingProgress"], [424, 29, 448, 27], [424, 30, 448, 28], [424, 32, 448, 30], [424, 33, 448, 31], [425, 8, 449, 6], [426, 8, 450, 6, "console"], [426, 15, 450, 13], [426, 16, 450, 14, "log"], [426, 19, 450, 17], [426, 20, 450, 18], [426, 73, 450, 71], [426, 74, 450, 72], [427, 8, 451, 6], [427, 14, 451, 12, "processImageWithFaceBlur"], [427, 38, 451, 36], [427, 39, 451, 37, "photo"], [427, 44, 451, 42], [427, 45, 451, 43, "uri"], [427, 48, 451, 46], [427, 49, 451, 47], [428, 8, 452, 6, "console"], [428, 15, 452, 13], [428, 16, 452, 14, "log"], [428, 19, 452, 17], [428, 20, 452, 18], [428, 71, 452, 69], [428, 72, 452, 70], [429, 6, 453, 4], [429, 7, 453, 5], [429, 8, 453, 6], [429, 15, 453, 13, "error"], [429, 20, 453, 18], [429, 22, 453, 20], [430, 8, 454, 6, "console"], [430, 15, 454, 13], [430, 16, 454, 14, "error"], [430, 21, 454, 19], [430, 22, 454, 20], [430, 54, 454, 52], [430, 56, 454, 54, "error"], [430, 61, 454, 59], [430, 62, 454, 60], [431, 8, 455, 6, "setErrorMessage"], [431, 23, 455, 21], [431, 24, 455, 22], [431, 68, 455, 66], [431, 69, 455, 67], [432, 8, 456, 6, "setProcessingState"], [432, 26, 456, 24], [432, 27, 456, 25], [432, 34, 456, 32], [432, 35, 456, 33], [433, 6, 457, 4], [434, 4, 458, 2], [434, 5, 458, 3], [434, 7, 458, 5], [434, 9, 458, 7], [434, 10, 458, 8], [435, 4, 459, 2], [436, 4, 460, 2], [436, 10, 460, 8, "processImageWithFaceBlur"], [436, 34, 460, 32], [436, 37, 460, 35], [436, 43, 460, 42, "photoUri"], [436, 51, 460, 58], [436, 55, 460, 63], [437, 6, 461, 4], [437, 10, 461, 8], [438, 8, 462, 6, "console"], [438, 15, 462, 13], [438, 16, 462, 14, "log"], [438, 19, 462, 17], [438, 20, 462, 18], [438, 84, 462, 82], [438, 85, 462, 83], [439, 8, 463, 6, "setProcessingState"], [439, 26, 463, 24], [439, 27, 463, 25], [439, 39, 463, 37], [439, 40, 463, 38], [440, 8, 464, 6, "setProcessingProgress"], [440, 29, 464, 27], [440, 30, 464, 28], [440, 32, 464, 30], [440, 33, 464, 31], [442, 8, 466, 6], [443, 8, 467, 6], [443, 14, 467, 12, "canvas"], [443, 20, 467, 18], [443, 23, 467, 21, "document"], [443, 31, 467, 29], [443, 32, 467, 30, "createElement"], [443, 45, 467, 43], [443, 46, 467, 44], [443, 54, 467, 52], [443, 55, 467, 53], [444, 8, 468, 6], [444, 14, 468, 12, "ctx"], [444, 17, 468, 15], [444, 20, 468, 18, "canvas"], [444, 26, 468, 24], [444, 27, 468, 25, "getContext"], [444, 37, 468, 35], [444, 38, 468, 36], [444, 42, 468, 40], [444, 43, 468, 41], [445, 8, 469, 6], [445, 12, 469, 10], [445, 13, 469, 11, "ctx"], [445, 16, 469, 14], [445, 18, 469, 16], [445, 24, 469, 22], [445, 28, 469, 26, "Error"], [445, 33, 469, 31], [445, 34, 469, 32], [445, 64, 469, 62], [445, 65, 469, 63], [447, 8, 471, 6], [448, 8, 472, 6], [448, 14, 472, 12, "img"], [448, 17, 472, 15], [448, 20, 472, 18], [448, 24, 472, 22, "Image"], [448, 29, 472, 27], [448, 30, 472, 28], [448, 31, 472, 29], [449, 8, 473, 6], [449, 14, 473, 12], [449, 18, 473, 16, "Promise"], [449, 25, 473, 23], [449, 26, 473, 24], [449, 27, 473, 25, "resolve"], [449, 34, 473, 32], [449, 36, 473, 34, "reject"], [449, 42, 473, 40], [449, 47, 473, 45], [450, 10, 474, 8, "img"], [450, 13, 474, 11], [450, 14, 474, 12, "onload"], [450, 20, 474, 18], [450, 23, 474, 21, "resolve"], [450, 30, 474, 28], [451, 10, 475, 8, "img"], [451, 13, 475, 11], [451, 14, 475, 12, "onerror"], [451, 21, 475, 19], [451, 24, 475, 22, "reject"], [451, 30, 475, 28], [452, 10, 476, 8, "img"], [452, 13, 476, 11], [452, 14, 476, 12, "src"], [452, 17, 476, 15], [452, 20, 476, 18, "photoUri"], [452, 28, 476, 26], [453, 8, 477, 6], [453, 9, 477, 7], [453, 10, 477, 8], [455, 8, 479, 6], [456, 8, 480, 6, "canvas"], [456, 14, 480, 12], [456, 15, 480, 13, "width"], [456, 20, 480, 18], [456, 23, 480, 21, "img"], [456, 26, 480, 24], [456, 27, 480, 25, "width"], [456, 32, 480, 30], [457, 8, 481, 6, "canvas"], [457, 14, 481, 12], [457, 15, 481, 13, "height"], [457, 21, 481, 19], [457, 24, 481, 22, "img"], [457, 27, 481, 25], [457, 28, 481, 26, "height"], [457, 34, 481, 32], [458, 8, 482, 6, "console"], [458, 15, 482, 13], [458, 16, 482, 14, "log"], [458, 19, 482, 17], [458, 20, 482, 18], [458, 54, 482, 52], [458, 56, 482, 54], [459, 10, 482, 56, "width"], [459, 15, 482, 61], [459, 17, 482, 63, "img"], [459, 20, 482, 66], [459, 21, 482, 67, "width"], [459, 26, 482, 72], [460, 10, 482, 74, "height"], [460, 16, 482, 80], [460, 18, 482, 82, "img"], [460, 21, 482, 85], [460, 22, 482, 86, "height"], [461, 8, 482, 93], [461, 9, 482, 94], [461, 10, 482, 95], [463, 8, 484, 6], [464, 8, 485, 6, "ctx"], [464, 11, 485, 9], [464, 12, 485, 10, "drawImage"], [464, 21, 485, 19], [464, 22, 485, 20, "img"], [464, 25, 485, 23], [464, 27, 485, 25], [464, 28, 485, 26], [464, 30, 485, 28], [464, 31, 485, 29], [464, 32, 485, 30], [465, 8, 486, 6, "console"], [465, 15, 486, 13], [465, 16, 486, 14, "log"], [465, 19, 486, 17], [465, 20, 486, 18], [465, 72, 486, 70], [465, 73, 486, 71], [466, 8, 488, 6, "setProcessingProgress"], [466, 29, 488, 27], [466, 30, 488, 28], [466, 32, 488, 30], [466, 33, 488, 31], [468, 8, 490, 6], [469, 8, 491, 6], [469, 12, 491, 10, "detectedFaces"], [469, 25, 491, 23], [469, 28, 491, 26], [469, 30, 491, 28], [470, 8, 493, 6, "console"], [470, 15, 493, 13], [470, 16, 493, 14, "log"], [470, 19, 493, 17], [470, 20, 493, 18], [470, 81, 493, 79], [470, 82, 493, 80], [472, 8, 495, 6], [473, 8, 496, 6], [473, 12, 496, 10], [474, 10, 497, 8], [474, 16, 497, 14, "loadTensorFlowFaceDetection"], [474, 43, 497, 41], [474, 44, 497, 42], [474, 45, 497, 43], [475, 10, 498, 8, "detectedFaces"], [475, 23, 498, 21], [475, 26, 498, 24], [475, 32, 498, 30, "detectFacesWithTensorFlow"], [475, 57, 498, 55], [475, 58, 498, 56, "img"], [475, 61, 498, 59], [475, 62, 498, 60], [476, 10, 499, 8, "console"], [476, 17, 499, 15], [476, 18, 499, 16, "log"], [476, 21, 499, 19], [476, 22, 499, 20], [476, 70, 499, 68, "detectedFaces"], [476, 83, 499, 81], [476, 84, 499, 82, "length"], [476, 90, 499, 88], [476, 98, 499, 96], [476, 99, 499, 97], [477, 8, 500, 6], [477, 9, 500, 7], [477, 10, 500, 8], [477, 17, 500, 15, "tensorFlowError"], [477, 32, 500, 30], [477, 34, 500, 32], [478, 10, 501, 8, "console"], [478, 17, 501, 15], [478, 18, 501, 16, "warn"], [478, 22, 501, 20], [478, 23, 501, 21], [478, 61, 501, 59], [478, 63, 501, 61, "tensorFlowError"], [478, 78, 501, 76], [478, 79, 501, 77], [480, 10, 503, 8], [481, 10, 504, 8, "console"], [481, 17, 504, 15], [481, 18, 504, 16, "log"], [481, 21, 504, 19], [481, 22, 504, 20], [481, 86, 504, 84], [481, 87, 504, 85], [482, 10, 505, 8, "detectedFaces"], [482, 23, 505, 21], [482, 26, 505, 24, "detectFacesHeuristic"], [482, 46, 505, 44], [482, 47, 505, 45, "img"], [482, 50, 505, 48], [482, 52, 505, 50, "ctx"], [482, 55, 505, 53], [482, 56, 505, 54], [483, 10, 506, 8, "console"], [483, 17, 506, 15], [483, 18, 506, 16, "log"], [483, 21, 506, 19], [483, 22, 506, 20], [483, 70, 506, 68, "detectedFaces"], [483, 83, 506, 81], [483, 84, 506, 82, "length"], [483, 90, 506, 88], [483, 98, 506, 96], [483, 99, 506, 97], [484, 8, 507, 6], [485, 8, 509, 6, "console"], [485, 15, 509, 13], [485, 16, 509, 14, "log"], [485, 19, 509, 17], [485, 20, 509, 18], [485, 72, 509, 70, "detectedFaces"], [485, 85, 509, 83], [485, 86, 509, 84, "length"], [485, 92, 509, 90], [485, 100, 509, 98], [485, 101, 509, 99], [486, 8, 510, 6], [486, 12, 510, 10, "detectedFaces"], [486, 25, 510, 23], [486, 26, 510, 24, "length"], [486, 32, 510, 30], [486, 35, 510, 33], [486, 36, 510, 34], [486, 38, 510, 36], [487, 10, 511, 8, "console"], [487, 17, 511, 15], [487, 18, 511, 16, "log"], [487, 21, 511, 19], [487, 22, 511, 20], [487, 66, 511, 64], [487, 68, 511, 66, "detectedFaces"], [487, 81, 511, 79], [487, 82, 511, 80, "map"], [487, 85, 511, 83], [487, 86, 511, 84], [487, 87, 511, 85, "face"], [487, 91, 511, 89], [487, 93, 511, 91, "i"], [487, 94, 511, 92], [487, 100, 511, 98], [488, 12, 512, 10, "faceNumber"], [488, 22, 512, 20], [488, 24, 512, 22, "i"], [488, 25, 512, 23], [488, 28, 512, 26], [488, 29, 512, 27], [489, 12, 513, 10, "centerX"], [489, 19, 513, 17], [489, 21, 513, 19, "face"], [489, 25, 513, 23], [489, 26, 513, 24, "boundingBox"], [489, 37, 513, 35], [489, 38, 513, 36, "xCenter"], [489, 45, 513, 43], [490, 12, 514, 10, "centerY"], [490, 19, 514, 17], [490, 21, 514, 19, "face"], [490, 25, 514, 23], [490, 26, 514, 24, "boundingBox"], [490, 37, 514, 35], [490, 38, 514, 36, "yCenter"], [490, 45, 514, 43], [491, 12, 515, 10, "width"], [491, 17, 515, 15], [491, 19, 515, 17, "face"], [491, 23, 515, 21], [491, 24, 515, 22, "boundingBox"], [491, 35, 515, 33], [491, 36, 515, 34, "width"], [491, 41, 515, 39], [492, 12, 516, 10, "height"], [492, 18, 516, 16], [492, 20, 516, 18, "face"], [492, 24, 516, 22], [492, 25, 516, 23, "boundingBox"], [492, 36, 516, 34], [492, 37, 516, 35, "height"], [493, 10, 517, 8], [493, 11, 517, 9], [493, 12, 517, 10], [493, 13, 517, 11], [493, 14, 517, 12], [494, 8, 518, 6], [494, 9, 518, 7], [494, 15, 518, 13], [495, 10, 519, 8, "console"], [495, 17, 519, 15], [495, 18, 519, 16, "log"], [495, 21, 519, 19], [495, 22, 519, 20], [495, 91, 519, 89], [495, 92, 519, 90], [496, 8, 520, 6], [497, 8, 522, 6, "setProcessingProgress"], [497, 29, 522, 27], [497, 30, 522, 28], [497, 32, 522, 30], [497, 33, 522, 31], [499, 8, 524, 6], [500, 8, 525, 6], [500, 12, 525, 10, "detectedFaces"], [500, 25, 525, 23], [500, 26, 525, 24, "length"], [500, 32, 525, 30], [500, 35, 525, 33], [500, 36, 525, 34], [500, 38, 525, 36], [501, 10, 526, 8, "console"], [501, 17, 526, 15], [501, 18, 526, 16, "log"], [501, 21, 526, 19], [501, 22, 526, 20], [501, 61, 526, 59, "detectedFaces"], [501, 74, 526, 72], [501, 75, 526, 73, "length"], [501, 81, 526, 79], [501, 101, 526, 99], [501, 102, 526, 100], [502, 10, 528, 8, "detectedFaces"], [502, 23, 528, 21], [502, 24, 528, 22, "for<PERSON>ach"], [502, 31, 528, 29], [502, 32, 528, 30], [502, 33, 528, 31, "detection"], [502, 42, 528, 40], [502, 44, 528, 42, "index"], [502, 49, 528, 47], [502, 54, 528, 52], [503, 12, 529, 10], [503, 18, 529, 16, "bbox"], [503, 22, 529, 20], [503, 25, 529, 23, "detection"], [503, 34, 529, 32], [503, 35, 529, 33, "boundingBox"], [503, 46, 529, 44], [505, 12, 531, 10], [506, 12, 532, 10], [506, 18, 532, 16, "faceX"], [506, 23, 532, 21], [506, 26, 532, 24, "bbox"], [506, 30, 532, 28], [506, 31, 532, 29, "xCenter"], [506, 38, 532, 36], [506, 41, 532, 39, "img"], [506, 44, 532, 42], [506, 45, 532, 43, "width"], [506, 50, 532, 48], [506, 53, 532, 52, "bbox"], [506, 57, 532, 56], [506, 58, 532, 57, "width"], [506, 63, 532, 62], [506, 66, 532, 65, "img"], [506, 69, 532, 68], [506, 70, 532, 69, "width"], [506, 75, 532, 74], [506, 78, 532, 78], [506, 79, 532, 79], [507, 12, 533, 10], [507, 18, 533, 16, "faceY"], [507, 23, 533, 21], [507, 26, 533, 24, "bbox"], [507, 30, 533, 28], [507, 31, 533, 29, "yCenter"], [507, 38, 533, 36], [507, 41, 533, 39, "img"], [507, 44, 533, 42], [507, 45, 533, 43, "height"], [507, 51, 533, 49], [507, 54, 533, 53, "bbox"], [507, 58, 533, 57], [507, 59, 533, 58, "height"], [507, 65, 533, 64], [507, 68, 533, 67, "img"], [507, 71, 533, 70], [507, 72, 533, 71, "height"], [507, 78, 533, 77], [507, 81, 533, 81], [507, 82, 533, 82], [508, 12, 534, 10], [508, 18, 534, 16, "faceWidth"], [508, 27, 534, 25], [508, 30, 534, 28, "bbox"], [508, 34, 534, 32], [508, 35, 534, 33, "width"], [508, 40, 534, 38], [508, 43, 534, 41, "img"], [508, 46, 534, 44], [508, 47, 534, 45, "width"], [508, 52, 534, 50], [509, 12, 535, 10], [509, 18, 535, 16, "faceHeight"], [509, 28, 535, 26], [509, 31, 535, 29, "bbox"], [509, 35, 535, 33], [509, 36, 535, 34, "height"], [509, 42, 535, 40], [509, 45, 535, 43, "img"], [509, 48, 535, 46], [509, 49, 535, 47, "height"], [509, 55, 535, 53], [511, 12, 537, 10], [512, 12, 538, 10], [512, 18, 538, 16, "padding"], [512, 25, 538, 23], [512, 28, 538, 26], [512, 31, 538, 29], [513, 12, 539, 10], [513, 18, 539, 16, "paddedX"], [513, 25, 539, 23], [513, 28, 539, 26, "Math"], [513, 32, 539, 30], [513, 33, 539, 31, "max"], [513, 36, 539, 34], [513, 37, 539, 35], [513, 38, 539, 36], [513, 40, 539, 38, "faceX"], [513, 45, 539, 43], [513, 48, 539, 46, "faceWidth"], [513, 57, 539, 55], [513, 60, 539, 58, "padding"], [513, 67, 539, 65], [513, 68, 539, 66], [514, 12, 540, 10], [514, 18, 540, 16, "paddedY"], [514, 25, 540, 23], [514, 28, 540, 26, "Math"], [514, 32, 540, 30], [514, 33, 540, 31, "max"], [514, 36, 540, 34], [514, 37, 540, 35], [514, 38, 540, 36], [514, 40, 540, 38, "faceY"], [514, 45, 540, 43], [514, 48, 540, 46, "faceHeight"], [514, 58, 540, 56], [514, 61, 540, 59, "padding"], [514, 68, 540, 66], [514, 69, 540, 67], [515, 12, 541, 10], [515, 18, 541, 16, "<PERSON><PERSON><PERSON><PERSON>"], [515, 29, 541, 27], [515, 32, 541, 30, "Math"], [515, 36, 541, 34], [515, 37, 541, 35, "min"], [515, 40, 541, 38], [515, 41, 541, 39, "img"], [515, 44, 541, 42], [515, 45, 541, 43, "width"], [515, 50, 541, 48], [515, 53, 541, 51, "paddedX"], [515, 60, 541, 58], [515, 62, 541, 60, "faceWidth"], [515, 71, 541, 69], [515, 75, 541, 73], [515, 76, 541, 74], [515, 79, 541, 77], [515, 80, 541, 78], [515, 83, 541, 81, "padding"], [515, 90, 541, 88], [515, 91, 541, 89], [515, 92, 541, 90], [516, 12, 542, 10], [516, 18, 542, 16, "paddedHeight"], [516, 30, 542, 28], [516, 33, 542, 31, "Math"], [516, 37, 542, 35], [516, 38, 542, 36, "min"], [516, 41, 542, 39], [516, 42, 542, 40, "img"], [516, 45, 542, 43], [516, 46, 542, 44, "height"], [516, 52, 542, 50], [516, 55, 542, 53, "paddedY"], [516, 62, 542, 60], [516, 64, 542, 62, "faceHeight"], [516, 74, 542, 72], [516, 78, 542, 76], [516, 79, 542, 77], [516, 82, 542, 80], [516, 83, 542, 81], [516, 86, 542, 84, "padding"], [516, 93, 542, 91], [516, 94, 542, 92], [516, 95, 542, 93], [517, 12, 544, 10, "console"], [517, 19, 544, 17], [517, 20, 544, 18, "log"], [517, 23, 544, 21], [517, 24, 544, 22], [517, 60, 544, 58, "index"], [517, 65, 544, 63], [517, 68, 544, 66], [517, 69, 544, 67], [517, 72, 544, 70], [517, 74, 544, 72], [518, 14, 545, 12, "original"], [518, 22, 545, 20], [518, 24, 545, 22], [519, 16, 545, 24, "x"], [519, 17, 545, 25], [519, 19, 545, 27, "Math"], [519, 23, 545, 31], [519, 24, 545, 32, "round"], [519, 29, 545, 37], [519, 30, 545, 38, "faceX"], [519, 35, 545, 43], [519, 36, 545, 44], [520, 16, 545, 46, "y"], [520, 17, 545, 47], [520, 19, 545, 49, "Math"], [520, 23, 545, 53], [520, 24, 545, 54, "round"], [520, 29, 545, 59], [520, 30, 545, 60, "faceY"], [520, 35, 545, 65], [520, 36, 545, 66], [521, 16, 545, 68, "w"], [521, 17, 545, 69], [521, 19, 545, 71, "Math"], [521, 23, 545, 75], [521, 24, 545, 76, "round"], [521, 29, 545, 81], [521, 30, 545, 82, "faceWidth"], [521, 39, 545, 91], [521, 40, 545, 92], [522, 16, 545, 94, "h"], [522, 17, 545, 95], [522, 19, 545, 97, "Math"], [522, 23, 545, 101], [522, 24, 545, 102, "round"], [522, 29, 545, 107], [522, 30, 545, 108, "faceHeight"], [522, 40, 545, 118], [523, 14, 545, 120], [523, 15, 545, 121], [524, 14, 546, 12, "padded"], [524, 20, 546, 18], [524, 22, 546, 20], [525, 16, 546, 22, "x"], [525, 17, 546, 23], [525, 19, 546, 25, "Math"], [525, 23, 546, 29], [525, 24, 546, 30, "round"], [525, 29, 546, 35], [525, 30, 546, 36, "paddedX"], [525, 37, 546, 43], [525, 38, 546, 44], [526, 16, 546, 46, "y"], [526, 17, 546, 47], [526, 19, 546, 49, "Math"], [526, 23, 546, 53], [526, 24, 546, 54, "round"], [526, 29, 546, 59], [526, 30, 546, 60, "paddedY"], [526, 37, 546, 67], [526, 38, 546, 68], [527, 16, 546, 70, "w"], [527, 17, 546, 71], [527, 19, 546, 73, "Math"], [527, 23, 546, 77], [527, 24, 546, 78, "round"], [527, 29, 546, 83], [527, 30, 546, 84, "<PERSON><PERSON><PERSON><PERSON>"], [527, 41, 546, 95], [527, 42, 546, 96], [528, 16, 546, 98, "h"], [528, 17, 546, 99], [528, 19, 546, 101, "Math"], [528, 23, 546, 105], [528, 24, 546, 106, "round"], [528, 29, 546, 111], [528, 30, 546, 112, "paddedHeight"], [528, 42, 546, 124], [529, 14, 546, 126], [530, 12, 547, 10], [530, 13, 547, 11], [530, 14, 547, 12], [532, 12, 549, 10], [533, 12, 550, 10, "applyStrongBlur"], [533, 27, 550, 25], [533, 28, 550, 26, "ctx"], [533, 31, 550, 29], [533, 33, 550, 31, "paddedX"], [533, 40, 550, 38], [533, 42, 550, 40, "paddedY"], [533, 49, 550, 47], [533, 51, 550, 49, "<PERSON><PERSON><PERSON><PERSON>"], [533, 62, 550, 60], [533, 64, 550, 62, "paddedHeight"], [533, 76, 550, 74], [533, 77, 550, 75], [534, 12, 551, 10, "console"], [534, 19, 551, 17], [534, 20, 551, 18, "log"], [534, 23, 551, 21], [534, 24, 551, 22], [534, 50, 551, 48, "index"], [534, 55, 551, 53], [534, 58, 551, 56], [534, 59, 551, 57], [534, 79, 551, 77], [534, 80, 551, 78], [535, 10, 552, 8], [535, 11, 552, 9], [535, 12, 552, 10], [536, 10, 554, 8, "console"], [536, 17, 554, 15], [536, 18, 554, 16, "log"], [536, 21, 554, 19], [536, 22, 554, 20], [536, 48, 554, 46, "detectedFaces"], [536, 61, 554, 59], [536, 62, 554, 60, "length"], [536, 68, 554, 66], [536, 104, 554, 102], [536, 105, 554, 103], [537, 8, 555, 6], [537, 9, 555, 7], [537, 15, 555, 13], [538, 10, 556, 8, "console"], [538, 17, 556, 15], [538, 18, 556, 16, "log"], [538, 21, 556, 19], [538, 22, 556, 20], [538, 109, 556, 107], [538, 110, 556, 108], [539, 10, 557, 8], [540, 10, 558, 8, "applyFallbackFaceBlur"], [540, 31, 558, 29], [540, 32, 558, 30, "ctx"], [540, 35, 558, 33], [540, 37, 558, 35, "img"], [540, 40, 558, 38], [540, 41, 558, 39, "width"], [540, 46, 558, 44], [540, 48, 558, 46, "img"], [540, 51, 558, 49], [540, 52, 558, 50, "height"], [540, 58, 558, 56], [540, 59, 558, 57], [541, 8, 559, 6], [542, 8, 561, 6, "setProcessingProgress"], [542, 29, 561, 27], [542, 30, 561, 28], [542, 32, 561, 30], [542, 33, 561, 31], [544, 8, 563, 6], [545, 8, 564, 6, "console"], [545, 15, 564, 13], [545, 16, 564, 14, "log"], [545, 19, 564, 17], [545, 20, 564, 18], [545, 85, 564, 83], [545, 86, 564, 84], [546, 8, 565, 6], [546, 14, 565, 12, "blurredImageBlob"], [546, 30, 565, 28], [546, 33, 565, 31], [546, 39, 565, 37], [546, 43, 565, 41, "Promise"], [546, 50, 565, 48], [546, 51, 565, 56, "resolve"], [546, 58, 565, 63], [546, 62, 565, 68], [547, 10, 566, 8, "canvas"], [547, 16, 566, 14], [547, 17, 566, 15, "toBlob"], [547, 23, 566, 21], [547, 24, 566, 23, "blob"], [547, 28, 566, 27], [547, 32, 566, 32, "resolve"], [547, 39, 566, 39], [547, 40, 566, 40, "blob"], [547, 44, 566, 45], [547, 45, 566, 46], [547, 47, 566, 48], [547, 59, 566, 60], [547, 61, 566, 62], [547, 64, 566, 65], [547, 65, 566, 66], [548, 8, 567, 6], [548, 9, 567, 7], [548, 10, 567, 8], [549, 8, 569, 6], [549, 14, 569, 12, "blurredImageUrl"], [549, 29, 569, 27], [549, 32, 569, 30, "URL"], [549, 35, 569, 33], [549, 36, 569, 34, "createObjectURL"], [549, 51, 569, 49], [549, 52, 569, 50, "blurredImageBlob"], [549, 68, 569, 66], [549, 69, 569, 67], [550, 8, 570, 6, "console"], [550, 15, 570, 13], [550, 16, 570, 14, "log"], [550, 19, 570, 17], [550, 20, 570, 18], [550, 66, 570, 64], [550, 68, 570, 66, "blurredImageUrl"], [550, 83, 570, 81], [550, 84, 570, 82, "substring"], [550, 93, 570, 91], [550, 94, 570, 92], [550, 95, 570, 93], [550, 97, 570, 95], [550, 99, 570, 97], [550, 100, 570, 98], [550, 103, 570, 101], [550, 108, 570, 106], [550, 109, 570, 107], [551, 8, 572, 6, "setProcessingProgress"], [551, 29, 572, 27], [551, 30, 572, 28], [551, 33, 572, 31], [551, 34, 572, 32], [553, 8, 574, 6], [554, 8, 575, 6], [554, 14, 575, 12, "completeProcessing"], [554, 32, 575, 30], [554, 33, 575, 31, "blurredImageUrl"], [554, 48, 575, 46], [554, 49, 575, 47], [555, 6, 577, 4], [555, 7, 577, 5], [555, 8, 577, 6], [555, 15, 577, 13, "error"], [555, 20, 577, 18], [555, 22, 577, 20], [556, 8, 578, 6, "console"], [556, 15, 578, 13], [556, 16, 578, 14, "error"], [556, 21, 578, 19], [556, 22, 578, 20], [556, 57, 578, 55], [556, 59, 578, 57, "error"], [556, 64, 578, 62], [556, 65, 578, 63], [557, 8, 579, 6, "setErrorMessage"], [557, 23, 579, 21], [557, 24, 579, 22], [557, 50, 579, 48], [557, 51, 579, 49], [558, 8, 580, 6, "setProcessingState"], [558, 26, 580, 24], [558, 27, 580, 25], [558, 34, 580, 32], [558, 35, 580, 33], [559, 6, 581, 4], [560, 4, 582, 2], [560, 5, 582, 3], [562, 4, 584, 2], [563, 4, 585, 2], [563, 10, 585, 8, "completeProcessing"], [563, 28, 585, 26], [563, 31, 585, 29], [563, 37, 585, 36, "blurredImageUrl"], [563, 52, 585, 59], [563, 56, 585, 64], [564, 6, 586, 4], [564, 10, 586, 8], [565, 8, 587, 6, "setProcessingState"], [565, 26, 587, 24], [565, 27, 587, 25], [565, 37, 587, 35], [565, 38, 587, 36], [567, 8, 589, 6], [568, 8, 590, 6], [568, 14, 590, 12, "timestamp"], [568, 23, 590, 21], [568, 26, 590, 24, "Date"], [568, 30, 590, 28], [568, 31, 590, 29, "now"], [568, 34, 590, 32], [568, 35, 590, 33], [568, 36, 590, 34], [569, 8, 591, 6], [569, 14, 591, 12, "result"], [569, 20, 591, 18], [569, 23, 591, 21], [570, 10, 592, 8, "imageUrl"], [570, 18, 592, 16], [570, 20, 592, 18, "blurredImageUrl"], [570, 35, 592, 33], [571, 10, 593, 8, "localUri"], [571, 18, 593, 16], [571, 20, 593, 18, "blurredImageUrl"], [571, 35, 593, 33], [572, 10, 594, 8, "challengeCode"], [572, 23, 594, 21], [572, 25, 594, 23, "challengeCode"], [572, 38, 594, 36], [572, 42, 594, 40], [572, 44, 594, 42], [573, 10, 595, 8, "timestamp"], [573, 19, 595, 17], [574, 10, 596, 8, "jobId"], [574, 15, 596, 13], [574, 17, 596, 15], [574, 27, 596, 25, "timestamp"], [574, 36, 596, 34], [574, 38, 596, 36], [575, 10, 597, 8, "status"], [575, 16, 597, 14], [575, 18, 597, 16], [576, 8, 598, 6], [576, 9, 598, 7], [577, 8, 600, 6, "console"], [577, 15, 600, 13], [577, 16, 600, 14, "log"], [577, 19, 600, 17], [577, 20, 600, 18], [577, 100, 600, 98], [577, 102, 600, 100], [578, 10, 601, 8, "imageUrl"], [578, 18, 601, 16], [578, 20, 601, 18, "blurredImageUrl"], [578, 35, 601, 33], [578, 36, 601, 34, "substring"], [578, 45, 601, 43], [578, 46, 601, 44], [578, 47, 601, 45], [578, 49, 601, 47], [578, 51, 601, 49], [578, 52, 601, 50], [578, 55, 601, 53], [578, 60, 601, 58], [579, 10, 602, 8, "timestamp"], [579, 19, 602, 17], [580, 10, 603, 8, "jobId"], [580, 15, 603, 13], [580, 17, 603, 15, "result"], [580, 23, 603, 21], [580, 24, 603, 22, "jobId"], [581, 8, 604, 6], [581, 9, 604, 7], [581, 10, 604, 8], [583, 8, 606, 6], [584, 8, 607, 6, "onComplete"], [584, 18, 607, 16], [584, 19, 607, 17, "result"], [584, 25, 607, 23], [584, 26, 607, 24], [585, 6, 609, 4], [585, 7, 609, 5], [585, 8, 609, 6], [585, 15, 609, 13, "error"], [585, 20, 609, 18], [585, 22, 609, 20], [586, 8, 610, 6, "console"], [586, 15, 610, 13], [586, 16, 610, 14, "error"], [586, 21, 610, 19], [586, 22, 610, 20], [586, 57, 610, 55], [586, 59, 610, 57, "error"], [586, 64, 610, 62], [586, 65, 610, 63], [587, 8, 611, 6, "setErrorMessage"], [587, 23, 611, 21], [587, 24, 611, 22], [587, 56, 611, 54], [587, 57, 611, 55], [588, 8, 612, 6, "setProcessingState"], [588, 26, 612, 24], [588, 27, 612, 25], [588, 34, 612, 32], [588, 35, 612, 33], [589, 6, 613, 4], [590, 4, 614, 2], [590, 5, 614, 3], [592, 4, 616, 2], [593, 4, 617, 2], [593, 10, 617, 8, "triggerServerProcessing"], [593, 33, 617, 31], [593, 36, 617, 34], [593, 42, 617, 34, "triggerServerProcessing"], [593, 43, 617, 41, "privateImageUrl"], [593, 58, 617, 64], [593, 60, 617, 66, "timestamp"], [593, 69, 617, 83], [593, 74, 617, 88], [594, 6, 618, 4], [594, 10, 618, 8], [595, 8, 619, 6, "console"], [595, 15, 619, 13], [595, 16, 619, 14, "log"], [595, 19, 619, 17], [595, 20, 619, 18], [595, 74, 619, 72], [595, 76, 619, 74, "privateImageUrl"], [595, 91, 619, 89], [595, 92, 619, 90], [596, 8, 620, 6, "setProcessingState"], [596, 26, 620, 24], [596, 27, 620, 25], [596, 39, 620, 37], [596, 40, 620, 38], [597, 8, 621, 6, "setProcessingProgress"], [597, 29, 621, 27], [597, 30, 621, 28], [597, 32, 621, 30], [597, 33, 621, 31], [598, 8, 623, 6], [598, 14, 623, 12, "requestBody"], [598, 25, 623, 23], [598, 28, 623, 26], [599, 10, 624, 8, "imageUrl"], [599, 18, 624, 16], [599, 20, 624, 18, "privateImageUrl"], [599, 35, 624, 33], [600, 10, 625, 8, "userId"], [600, 16, 625, 14], [601, 10, 626, 8, "requestId"], [601, 19, 626, 17], [602, 10, 627, 8, "timestamp"], [602, 19, 627, 17], [603, 10, 628, 8, "platform"], [603, 18, 628, 16], [603, 20, 628, 18], [604, 8, 629, 6], [604, 9, 629, 7], [605, 8, 631, 6, "console"], [605, 15, 631, 13], [605, 16, 631, 14, "log"], [605, 19, 631, 17], [605, 20, 631, 18], [605, 65, 631, 63], [605, 67, 631, 65, "requestBody"], [605, 78, 631, 76], [605, 79, 631, 77], [607, 8, 633, 6], [608, 8, 634, 6], [608, 14, 634, 12, "response"], [608, 22, 634, 20], [608, 25, 634, 23], [608, 31, 634, 29, "fetch"], [608, 36, 634, 34], [608, 37, 634, 35], [608, 40, 634, 38, "API_BASE_URL"], [608, 52, 634, 50], [608, 72, 634, 70], [608, 74, 634, 72], [609, 10, 635, 8, "method"], [609, 16, 635, 14], [609, 18, 635, 16], [609, 24, 635, 22], [610, 10, 636, 8, "headers"], [610, 17, 636, 15], [610, 19, 636, 17], [611, 12, 637, 10], [611, 26, 637, 24], [611, 28, 637, 26], [611, 46, 637, 44], [612, 12, 638, 10], [612, 27, 638, 25], [612, 29, 638, 27], [612, 39, 638, 37], [612, 45, 638, 43, "getAuthToken"], [612, 57, 638, 55], [612, 58, 638, 56], [612, 59, 638, 57], [613, 10, 639, 8], [613, 11, 639, 9], [614, 10, 640, 8, "body"], [614, 14, 640, 12], [614, 16, 640, 14, "JSON"], [614, 20, 640, 18], [614, 21, 640, 19, "stringify"], [614, 30, 640, 28], [614, 31, 640, 29, "requestBody"], [614, 42, 640, 40], [615, 8, 641, 6], [615, 9, 641, 7], [615, 10, 641, 8], [616, 8, 643, 6], [616, 12, 643, 10], [616, 13, 643, 11, "response"], [616, 21, 643, 19], [616, 22, 643, 20, "ok"], [616, 24, 643, 22], [616, 26, 643, 24], [617, 10, 644, 8], [617, 16, 644, 14, "errorText"], [617, 25, 644, 23], [617, 28, 644, 26], [617, 34, 644, 32, "response"], [617, 42, 644, 40], [617, 43, 644, 41, "text"], [617, 47, 644, 45], [617, 48, 644, 46], [617, 49, 644, 47], [618, 10, 645, 8, "console"], [618, 17, 645, 15], [618, 18, 645, 16, "error"], [618, 23, 645, 21], [618, 24, 645, 22], [618, 68, 645, 66], [618, 70, 645, 68, "response"], [618, 78, 645, 76], [618, 79, 645, 77, "status"], [618, 85, 645, 83], [618, 87, 645, 85, "errorText"], [618, 96, 645, 94], [618, 97, 645, 95], [619, 10, 646, 8], [619, 16, 646, 14], [619, 20, 646, 18, "Error"], [619, 25, 646, 23], [619, 26, 646, 24], [619, 48, 646, 46, "response"], [619, 56, 646, 54], [619, 57, 646, 55, "status"], [619, 63, 646, 61], [619, 67, 646, 65, "response"], [619, 75, 646, 73], [619, 76, 646, 74, "statusText"], [619, 86, 646, 84], [619, 88, 646, 86], [619, 89, 646, 87], [620, 8, 647, 6], [621, 8, 649, 6], [621, 14, 649, 12, "result"], [621, 20, 649, 18], [621, 23, 649, 21], [621, 29, 649, 27, "response"], [621, 37, 649, 35], [621, 38, 649, 36, "json"], [621, 42, 649, 40], [621, 43, 649, 41], [621, 44, 649, 42], [622, 8, 650, 6, "console"], [622, 15, 650, 13], [622, 16, 650, 14, "log"], [622, 19, 650, 17], [622, 20, 650, 18], [622, 68, 650, 66], [622, 70, 650, 68, "result"], [622, 76, 650, 74], [622, 77, 650, 75], [623, 8, 652, 6], [623, 12, 652, 10], [623, 13, 652, 11, "result"], [623, 19, 652, 17], [623, 20, 652, 18, "jobId"], [623, 25, 652, 23], [623, 27, 652, 25], [624, 10, 653, 8], [624, 16, 653, 14], [624, 20, 653, 18, "Error"], [624, 25, 653, 23], [624, 26, 653, 24], [624, 70, 653, 68], [624, 71, 653, 69], [625, 8, 654, 6], [627, 8, 656, 6], [628, 8, 657, 6], [628, 14, 657, 12, "pollForCompletion"], [628, 31, 657, 29], [628, 32, 657, 30, "result"], [628, 38, 657, 36], [628, 39, 657, 37, "jobId"], [628, 44, 657, 42], [628, 46, 657, 44, "timestamp"], [628, 55, 657, 53], [628, 56, 657, 54], [629, 6, 658, 4], [629, 7, 658, 5], [629, 8, 658, 6], [629, 15, 658, 13, "error"], [629, 20, 658, 18], [629, 22, 658, 20], [630, 8, 659, 6, "console"], [630, 15, 659, 13], [630, 16, 659, 14, "error"], [630, 21, 659, 19], [630, 22, 659, 20], [630, 57, 659, 55], [630, 59, 659, 57, "error"], [630, 64, 659, 62], [630, 65, 659, 63], [631, 8, 660, 6, "setErrorMessage"], [631, 23, 660, 21], [631, 24, 660, 22], [631, 52, 660, 50, "error"], [631, 57, 660, 55], [631, 58, 660, 56, "message"], [631, 65, 660, 63], [631, 67, 660, 65], [631, 68, 660, 66], [632, 8, 661, 6, "setProcessingState"], [632, 26, 661, 24], [632, 27, 661, 25], [632, 34, 661, 32], [632, 35, 661, 33], [633, 6, 662, 4], [634, 4, 663, 2], [634, 5, 663, 3], [635, 4, 664, 2], [636, 4, 665, 2], [636, 10, 665, 8, "pollForCompletion"], [636, 27, 665, 25], [636, 30, 665, 28], [636, 36, 665, 28, "pollForCompletion"], [636, 37, 665, 35, "jobId"], [636, 42, 665, 48], [636, 44, 665, 50, "timestamp"], [636, 53, 665, 67], [636, 55, 665, 69, "attempts"], [636, 63, 665, 77], [636, 66, 665, 80], [636, 67, 665, 81], [636, 72, 665, 86], [637, 6, 666, 4], [637, 12, 666, 10, "MAX_ATTEMPTS"], [637, 24, 666, 22], [637, 27, 666, 25], [637, 29, 666, 27], [637, 30, 666, 28], [637, 31, 666, 29], [638, 6, 667, 4], [638, 12, 667, 10, "POLL_INTERVAL"], [638, 25, 667, 23], [638, 28, 667, 26], [638, 32, 667, 30], [638, 33, 667, 31], [638, 34, 667, 32], [640, 6, 669, 4, "console"], [640, 13, 669, 11], [640, 14, 669, 12, "log"], [640, 17, 669, 15], [640, 18, 669, 16], [640, 53, 669, 51, "attempts"], [640, 61, 669, 59], [640, 64, 669, 62], [640, 65, 669, 63], [640, 69, 669, 67, "MAX_ATTEMPTS"], [640, 81, 669, 79], [640, 93, 669, 91, "jobId"], [640, 98, 669, 96], [640, 100, 669, 98], [640, 101, 669, 99], [641, 6, 671, 4], [641, 10, 671, 8, "attempts"], [641, 18, 671, 16], [641, 22, 671, 20, "MAX_ATTEMPTS"], [641, 34, 671, 32], [641, 36, 671, 34], [642, 8, 672, 6, "console"], [642, 15, 672, 13], [642, 16, 672, 14, "error"], [642, 21, 672, 19], [642, 22, 672, 20], [642, 75, 672, 73], [642, 76, 672, 74], [643, 8, 673, 6, "setErrorMessage"], [643, 23, 673, 21], [643, 24, 673, 22], [643, 63, 673, 61], [643, 64, 673, 62], [644, 8, 674, 6, "setProcessingState"], [644, 26, 674, 24], [644, 27, 674, 25], [644, 34, 674, 32], [644, 35, 674, 33], [645, 8, 675, 6], [646, 6, 676, 4], [647, 6, 678, 4], [647, 10, 678, 8], [648, 8, 679, 6], [648, 14, 679, 12, "response"], [648, 22, 679, 20], [648, 25, 679, 23], [648, 31, 679, 29, "fetch"], [648, 36, 679, 34], [648, 37, 679, 35], [648, 40, 679, 38, "API_BASE_URL"], [648, 52, 679, 50], [648, 75, 679, 73, "jobId"], [648, 80, 679, 78], [648, 82, 679, 80], [648, 84, 679, 82], [649, 10, 680, 8, "headers"], [649, 17, 680, 15], [649, 19, 680, 17], [650, 12, 681, 10], [650, 27, 681, 25], [650, 29, 681, 27], [650, 39, 681, 37], [650, 45, 681, 43, "getAuthToken"], [650, 57, 681, 55], [650, 58, 681, 56], [650, 59, 681, 57], [651, 10, 682, 8], [652, 8, 683, 6], [652, 9, 683, 7], [652, 10, 683, 8], [653, 8, 685, 6], [653, 12, 685, 10], [653, 13, 685, 11, "response"], [653, 21, 685, 19], [653, 22, 685, 20, "ok"], [653, 24, 685, 22], [653, 26, 685, 24], [654, 10, 686, 8], [654, 16, 686, 14], [654, 20, 686, 18, "Error"], [654, 25, 686, 23], [654, 26, 686, 24], [654, 34, 686, 32, "response"], [654, 42, 686, 40], [654, 43, 686, 41, "status"], [654, 49, 686, 47], [654, 54, 686, 52, "response"], [654, 62, 686, 60], [654, 63, 686, 61, "statusText"], [654, 73, 686, 71], [654, 75, 686, 73], [654, 76, 686, 74], [655, 8, 687, 6], [656, 8, 689, 6], [656, 14, 689, 12, "status"], [656, 20, 689, 18], [656, 23, 689, 21], [656, 29, 689, 27, "response"], [656, 37, 689, 35], [656, 38, 689, 36, "json"], [656, 42, 689, 40], [656, 43, 689, 41], [656, 44, 689, 42], [657, 8, 690, 6, "console"], [657, 15, 690, 13], [657, 16, 690, 14, "log"], [657, 19, 690, 17], [657, 20, 690, 18], [657, 54, 690, 52], [657, 56, 690, 54, "status"], [657, 62, 690, 60], [657, 63, 690, 61], [658, 8, 692, 6], [658, 12, 692, 10, "status"], [658, 18, 692, 16], [658, 19, 692, 17, "status"], [658, 25, 692, 23], [658, 30, 692, 28], [658, 41, 692, 39], [658, 43, 692, 41], [659, 10, 693, 8, "console"], [659, 17, 693, 15], [659, 18, 693, 16, "log"], [659, 21, 693, 19], [659, 22, 693, 20], [659, 73, 693, 71], [659, 74, 693, 72], [660, 10, 694, 8, "setProcessingProgress"], [660, 31, 694, 29], [660, 32, 694, 30], [660, 35, 694, 33], [660, 36, 694, 34], [661, 10, 695, 8, "setProcessingState"], [661, 28, 695, 26], [661, 29, 695, 27], [661, 40, 695, 38], [661, 41, 695, 39], [662, 10, 696, 8], [663, 10, 697, 8], [663, 16, 697, 14, "result"], [663, 22, 697, 20], [663, 25, 697, 23], [664, 12, 698, 10, "imageUrl"], [664, 20, 698, 18], [664, 22, 698, 20, "status"], [664, 28, 698, 26], [664, 29, 698, 27, "publicUrl"], [664, 38, 698, 36], [665, 12, 698, 38], [666, 12, 699, 10, "localUri"], [666, 20, 699, 18], [666, 22, 699, 20, "capturedPhoto"], [666, 35, 699, 33], [666, 39, 699, 37, "status"], [666, 45, 699, 43], [666, 46, 699, 44, "publicUrl"], [666, 55, 699, 53], [667, 12, 699, 55], [668, 12, 700, 10, "challengeCode"], [668, 25, 700, 23], [668, 27, 700, 25, "challengeCode"], [668, 40, 700, 38], [668, 44, 700, 42], [668, 46, 700, 44], [669, 12, 701, 10, "timestamp"], [669, 21, 701, 19], [670, 12, 702, 10, "processingStatus"], [670, 28, 702, 26], [670, 30, 702, 28], [671, 10, 703, 8], [671, 11, 703, 9], [672, 10, 704, 8, "console"], [672, 17, 704, 15], [672, 18, 704, 16, "log"], [672, 21, 704, 19], [672, 22, 704, 20], [672, 57, 704, 55], [672, 59, 704, 57, "result"], [672, 65, 704, 63], [672, 66, 704, 64], [673, 10, 705, 8, "onComplete"], [673, 20, 705, 18], [673, 21, 705, 19, "result"], [673, 27, 705, 25], [673, 28, 705, 26], [674, 10, 706, 8], [675, 8, 707, 6], [675, 9, 707, 7], [675, 15, 707, 13], [675, 19, 707, 17, "status"], [675, 25, 707, 23], [675, 26, 707, 24, "status"], [675, 32, 707, 30], [675, 37, 707, 35], [675, 45, 707, 43], [675, 47, 707, 45], [676, 10, 708, 8, "console"], [676, 17, 708, 15], [676, 18, 708, 16, "error"], [676, 23, 708, 21], [676, 24, 708, 22], [676, 60, 708, 58], [676, 62, 708, 60, "status"], [676, 68, 708, 66], [676, 69, 708, 67, "error"], [676, 74, 708, 72], [676, 75, 708, 73], [677, 10, 709, 8], [677, 16, 709, 14], [677, 20, 709, 18, "Error"], [677, 25, 709, 23], [677, 26, 709, 24, "status"], [677, 32, 709, 30], [677, 33, 709, 31, "error"], [677, 38, 709, 36], [677, 42, 709, 40], [677, 61, 709, 59], [677, 62, 709, 60], [678, 8, 710, 6], [678, 9, 710, 7], [678, 15, 710, 13], [679, 10, 711, 8], [680, 10, 712, 8], [680, 16, 712, 14, "progressValue"], [680, 29, 712, 27], [680, 32, 712, 30], [680, 34, 712, 32], [680, 37, 712, 36, "attempts"], [680, 45, 712, 44], [680, 48, 712, 47, "MAX_ATTEMPTS"], [680, 60, 712, 59], [680, 63, 712, 63], [680, 65, 712, 65], [681, 10, 713, 8, "console"], [681, 17, 713, 15], [681, 18, 713, 16, "log"], [681, 21, 713, 19], [681, 22, 713, 20], [681, 71, 713, 69, "progressValue"], [681, 84, 713, 82], [681, 87, 713, 85], [681, 88, 713, 86], [682, 10, 714, 8, "setProcessingProgress"], [682, 31, 714, 29], [682, 32, 714, 30, "progressValue"], [682, 45, 714, 43], [682, 46, 714, 44], [683, 10, 716, 8, "setTimeout"], [683, 20, 716, 18], [683, 21, 716, 19], [683, 27, 716, 25], [684, 12, 717, 10, "pollForCompletion"], [684, 29, 717, 27], [684, 30, 717, 28, "jobId"], [684, 35, 717, 33], [684, 37, 717, 35, "timestamp"], [684, 46, 717, 44], [684, 48, 717, 46, "attempts"], [684, 56, 717, 54], [684, 59, 717, 57], [684, 60, 717, 58], [684, 61, 717, 59], [685, 10, 718, 8], [685, 11, 718, 9], [685, 13, 718, 11, "POLL_INTERVAL"], [685, 26, 718, 24], [685, 27, 718, 25], [686, 8, 719, 6], [687, 6, 720, 4], [687, 7, 720, 5], [687, 8, 720, 6], [687, 15, 720, 13, "error"], [687, 20, 720, 18], [687, 22, 720, 20], [688, 8, 721, 6, "console"], [688, 15, 721, 13], [688, 16, 721, 14, "error"], [688, 21, 721, 19], [688, 22, 721, 20], [688, 54, 721, 52], [688, 56, 721, 54, "error"], [688, 61, 721, 59], [688, 62, 721, 60], [689, 8, 722, 6, "setErrorMessage"], [689, 23, 722, 21], [689, 24, 722, 22], [689, 62, 722, 60, "error"], [689, 67, 722, 65], [689, 68, 722, 66, "message"], [689, 75, 722, 73], [689, 77, 722, 75], [689, 78, 722, 76], [690, 8, 723, 6, "setProcessingState"], [690, 26, 723, 24], [690, 27, 723, 25], [690, 34, 723, 32], [690, 35, 723, 33], [691, 6, 724, 4], [692, 4, 725, 2], [692, 5, 725, 3], [693, 4, 726, 2], [694, 4, 727, 2], [694, 10, 727, 8, "getAuthToken"], [694, 22, 727, 20], [694, 25, 727, 23], [694, 31, 727, 23, "getAuthToken"], [694, 32, 727, 23], [694, 37, 727, 52], [695, 6, 728, 4], [696, 6, 729, 4], [697, 6, 730, 4], [697, 13, 730, 11], [697, 30, 730, 28], [698, 4, 731, 2], [698, 5, 731, 3], [700, 4, 733, 2], [701, 4, 734, 2], [701, 10, 734, 8, "retryCapture"], [701, 22, 734, 20], [701, 25, 734, 23], [701, 29, 734, 23, "useCallback"], [701, 47, 734, 34], [701, 49, 734, 35], [701, 55, 734, 41], [702, 6, 735, 4, "console"], [702, 13, 735, 11], [702, 14, 735, 12, "log"], [702, 17, 735, 15], [702, 18, 735, 16], [702, 55, 735, 53], [702, 56, 735, 54], [703, 6, 736, 4, "setProcessingState"], [703, 24, 736, 22], [703, 25, 736, 23], [703, 31, 736, 29], [703, 32, 736, 30], [704, 6, 737, 4, "setErrorMessage"], [704, 21, 737, 19], [704, 22, 737, 20], [704, 24, 737, 22], [704, 25, 737, 23], [705, 6, 738, 4, "setCapturedPhoto"], [705, 22, 738, 20], [705, 23, 738, 21], [705, 25, 738, 23], [705, 26, 738, 24], [706, 6, 739, 4, "setProcessingProgress"], [706, 27, 739, 25], [706, 28, 739, 26], [706, 29, 739, 27], [706, 30, 739, 28], [707, 4, 740, 2], [707, 5, 740, 3], [707, 7, 740, 5], [707, 9, 740, 7], [707, 10, 740, 8], [708, 4, 741, 2], [709, 4, 742, 2], [709, 8, 742, 2, "useEffect"], [709, 24, 742, 11], [709, 26, 742, 12], [709, 32, 742, 18], [710, 6, 743, 4, "console"], [710, 13, 743, 11], [710, 14, 743, 12, "log"], [710, 17, 743, 15], [710, 18, 743, 16], [710, 53, 743, 51], [710, 55, 743, 53, "permission"], [710, 65, 743, 63], [710, 66, 743, 64], [711, 6, 744, 4], [711, 10, 744, 8, "permission"], [711, 20, 744, 18], [711, 22, 744, 20], [712, 8, 745, 6, "console"], [712, 15, 745, 13], [712, 16, 745, 14, "log"], [712, 19, 745, 17], [712, 20, 745, 18], [712, 57, 745, 55], [712, 59, 745, 57, "permission"], [712, 69, 745, 67], [712, 70, 745, 68, "granted"], [712, 77, 745, 75], [712, 78, 745, 76], [713, 6, 746, 4], [714, 4, 747, 2], [714, 5, 747, 3], [714, 7, 747, 5], [714, 8, 747, 6, "permission"], [714, 18, 747, 16], [714, 19, 747, 17], [714, 20, 747, 18], [715, 4, 748, 2], [716, 4, 749, 2], [716, 8, 749, 6], [716, 9, 749, 7, "permission"], [716, 19, 749, 17], [716, 21, 749, 19], [717, 6, 750, 4, "console"], [717, 13, 750, 11], [717, 14, 750, 12, "log"], [717, 17, 750, 15], [717, 18, 750, 16], [717, 67, 750, 65], [717, 68, 750, 66], [718, 6, 751, 4], [718, 26, 752, 6], [718, 30, 752, 6, "_jsxDevRuntime"], [718, 44, 752, 6], [718, 45, 752, 6, "jsxDEV"], [718, 51, 752, 6], [718, 53, 752, 7, "_View"], [718, 58, 752, 7], [718, 59, 752, 7, "default"], [718, 66, 752, 11], [719, 8, 752, 12, "style"], [719, 13, 752, 17], [719, 15, 752, 19, "styles"], [719, 21, 752, 25], [719, 22, 752, 26, "container"], [719, 31, 752, 36], [720, 8, 752, 36, "children"], [720, 16, 752, 36], [720, 32, 753, 8], [720, 36, 753, 8, "_jsxDevRuntime"], [720, 50, 753, 8], [720, 51, 753, 8, "jsxDEV"], [720, 57, 753, 8], [720, 59, 753, 9, "_ActivityIndicator"], [720, 77, 753, 9], [720, 78, 753, 9, "default"], [720, 85, 753, 26], [721, 10, 753, 27, "size"], [721, 14, 753, 31], [721, 16, 753, 32], [721, 23, 753, 39], [722, 10, 753, 40, "color"], [722, 15, 753, 45], [722, 17, 753, 46], [723, 8, 753, 55], [724, 10, 753, 55, "fileName"], [724, 18, 753, 55], [724, 20, 753, 55, "_jsxFileName"], [724, 32, 753, 55], [725, 10, 753, 55, "lineNumber"], [725, 20, 753, 55], [726, 10, 753, 55, "columnNumber"], [726, 22, 753, 55], [727, 8, 753, 55], [727, 15, 753, 57], [727, 16, 753, 58], [727, 31, 754, 8], [727, 35, 754, 8, "_jsxDevRuntime"], [727, 49, 754, 8], [727, 50, 754, 8, "jsxDEV"], [727, 56, 754, 8], [727, 58, 754, 9, "_Text"], [727, 63, 754, 9], [727, 64, 754, 9, "default"], [727, 71, 754, 13], [728, 10, 754, 14, "style"], [728, 15, 754, 19], [728, 17, 754, 21, "styles"], [728, 23, 754, 27], [728, 24, 754, 28, "loadingText"], [728, 35, 754, 40], [729, 10, 754, 40, "children"], [729, 18, 754, 40], [729, 20, 754, 41], [730, 8, 754, 58], [731, 10, 754, 58, "fileName"], [731, 18, 754, 58], [731, 20, 754, 58, "_jsxFileName"], [731, 32, 754, 58], [732, 10, 754, 58, "lineNumber"], [732, 20, 754, 58], [733, 10, 754, 58, "columnNumber"], [733, 22, 754, 58], [734, 8, 754, 58], [734, 15, 754, 64], [734, 16, 754, 65], [735, 6, 754, 65], [736, 8, 754, 65, "fileName"], [736, 16, 754, 65], [736, 18, 754, 65, "_jsxFileName"], [736, 30, 754, 65], [737, 8, 754, 65, "lineNumber"], [737, 18, 754, 65], [738, 8, 754, 65, "columnNumber"], [738, 20, 754, 65], [739, 6, 754, 65], [739, 13, 755, 12], [739, 14, 755, 13], [740, 4, 757, 2], [741, 4, 758, 2], [741, 8, 758, 6], [741, 9, 758, 7, "permission"], [741, 19, 758, 17], [741, 20, 758, 18, "granted"], [741, 27, 758, 25], [741, 29, 758, 27], [742, 6, 759, 4, "console"], [742, 13, 759, 11], [742, 14, 759, 12, "log"], [742, 17, 759, 15], [742, 18, 759, 16], [742, 93, 759, 91], [742, 94, 759, 92], [743, 6, 760, 4], [743, 26, 761, 6], [743, 30, 761, 6, "_jsxDevRuntime"], [743, 44, 761, 6], [743, 45, 761, 6, "jsxDEV"], [743, 51, 761, 6], [743, 53, 761, 7, "_View"], [743, 58, 761, 7], [743, 59, 761, 7, "default"], [743, 66, 761, 11], [744, 8, 761, 12, "style"], [744, 13, 761, 17], [744, 15, 761, 19, "styles"], [744, 21, 761, 25], [744, 22, 761, 26, "container"], [744, 31, 761, 36], [745, 8, 761, 36, "children"], [745, 16, 761, 36], [745, 31, 762, 8], [745, 35, 762, 8, "_jsxDevRuntime"], [745, 49, 762, 8], [745, 50, 762, 8, "jsxDEV"], [745, 56, 762, 8], [745, 58, 762, 9, "_View"], [745, 63, 762, 9], [745, 64, 762, 9, "default"], [745, 71, 762, 13], [746, 10, 762, 14, "style"], [746, 15, 762, 19], [746, 17, 762, 21, "styles"], [746, 23, 762, 27], [746, 24, 762, 28, "permissionContent"], [746, 41, 762, 46], [747, 10, 762, 46, "children"], [747, 18, 762, 46], [747, 34, 763, 10], [747, 38, 763, 10, "_jsxDevRuntime"], [747, 52, 763, 10], [747, 53, 763, 10, "jsxDEV"], [747, 59, 763, 10], [747, 61, 763, 11, "_lucideReactNative"], [747, 79, 763, 11], [747, 80, 763, 11, "Camera"], [747, 86, 763, 21], [748, 12, 763, 22, "size"], [748, 16, 763, 26], [748, 18, 763, 28], [748, 20, 763, 31], [749, 12, 763, 32, "color"], [749, 17, 763, 37], [749, 19, 763, 38], [750, 10, 763, 47], [751, 12, 763, 47, "fileName"], [751, 20, 763, 47], [751, 22, 763, 47, "_jsxFileName"], [751, 34, 763, 47], [752, 12, 763, 47, "lineNumber"], [752, 22, 763, 47], [753, 12, 763, 47, "columnNumber"], [753, 24, 763, 47], [754, 10, 763, 47], [754, 17, 763, 49], [754, 18, 763, 50], [754, 33, 764, 10], [754, 37, 764, 10, "_jsxDevRuntime"], [754, 51, 764, 10], [754, 52, 764, 10, "jsxDEV"], [754, 58, 764, 10], [754, 60, 764, 11, "_Text"], [754, 65, 764, 11], [754, 66, 764, 11, "default"], [754, 73, 764, 15], [755, 12, 764, 16, "style"], [755, 17, 764, 21], [755, 19, 764, 23, "styles"], [755, 25, 764, 29], [755, 26, 764, 30, "permissionTitle"], [755, 41, 764, 46], [756, 12, 764, 46, "children"], [756, 20, 764, 46], [756, 22, 764, 47], [757, 10, 764, 73], [758, 12, 764, 73, "fileName"], [758, 20, 764, 73], [758, 22, 764, 73, "_jsxFileName"], [758, 34, 764, 73], [759, 12, 764, 73, "lineNumber"], [759, 22, 764, 73], [760, 12, 764, 73, "columnNumber"], [760, 24, 764, 73], [761, 10, 764, 73], [761, 17, 764, 79], [761, 18, 764, 80], [761, 33, 765, 10], [761, 37, 765, 10, "_jsxDevRuntime"], [761, 51, 765, 10], [761, 52, 765, 10, "jsxDEV"], [761, 58, 765, 10], [761, 60, 765, 11, "_Text"], [761, 65, 765, 11], [761, 66, 765, 11, "default"], [761, 73, 765, 15], [762, 12, 765, 16, "style"], [762, 17, 765, 21], [762, 19, 765, 23, "styles"], [762, 25, 765, 29], [762, 26, 765, 30, "permissionDescription"], [762, 47, 765, 52], [763, 12, 765, 52, "children"], [763, 20, 765, 52], [763, 22, 765, 53], [764, 10, 768, 10], [765, 12, 768, 10, "fileName"], [765, 20, 768, 10], [765, 22, 768, 10, "_jsxFileName"], [765, 34, 768, 10], [766, 12, 768, 10, "lineNumber"], [766, 22, 768, 10], [767, 12, 768, 10, "columnNumber"], [767, 24, 768, 10], [768, 10, 768, 10], [768, 17, 768, 16], [768, 18, 768, 17], [768, 33, 769, 10], [768, 37, 769, 10, "_jsxDevRuntime"], [768, 51, 769, 10], [768, 52, 769, 10, "jsxDEV"], [768, 58, 769, 10], [768, 60, 769, 11, "_TouchableOpacity"], [768, 77, 769, 11], [768, 78, 769, 11, "default"], [768, 85, 769, 27], [769, 12, 769, 28, "onPress"], [769, 19, 769, 35], [769, 21, 769, 37, "requestPermission"], [769, 38, 769, 55], [770, 12, 769, 56, "style"], [770, 17, 769, 61], [770, 19, 769, 63, "styles"], [770, 25, 769, 69], [770, 26, 769, 70, "primaryButton"], [770, 39, 769, 84], [771, 12, 769, 84, "children"], [771, 20, 769, 84], [771, 35, 770, 12], [771, 39, 770, 12, "_jsxDevRuntime"], [771, 53, 770, 12], [771, 54, 770, 12, "jsxDEV"], [771, 60, 770, 12], [771, 62, 770, 13, "_Text"], [771, 67, 770, 13], [771, 68, 770, 13, "default"], [771, 75, 770, 17], [772, 14, 770, 18, "style"], [772, 19, 770, 23], [772, 21, 770, 25, "styles"], [772, 27, 770, 31], [772, 28, 770, 32, "primaryButtonText"], [772, 45, 770, 50], [773, 14, 770, 50, "children"], [773, 22, 770, 50], [773, 24, 770, 51], [774, 12, 770, 67], [775, 14, 770, 67, "fileName"], [775, 22, 770, 67], [775, 24, 770, 67, "_jsxFileName"], [775, 36, 770, 67], [776, 14, 770, 67, "lineNumber"], [776, 24, 770, 67], [777, 14, 770, 67, "columnNumber"], [777, 26, 770, 67], [778, 12, 770, 67], [778, 19, 770, 73], [779, 10, 770, 74], [780, 12, 770, 74, "fileName"], [780, 20, 770, 74], [780, 22, 770, 74, "_jsxFileName"], [780, 34, 770, 74], [781, 12, 770, 74, "lineNumber"], [781, 22, 770, 74], [782, 12, 770, 74, "columnNumber"], [782, 24, 770, 74], [783, 10, 770, 74], [783, 17, 771, 28], [783, 18, 771, 29], [783, 33, 772, 10], [783, 37, 772, 10, "_jsxDevRuntime"], [783, 51, 772, 10], [783, 52, 772, 10, "jsxDEV"], [783, 58, 772, 10], [783, 60, 772, 11, "_TouchableOpacity"], [783, 77, 772, 11], [783, 78, 772, 11, "default"], [783, 85, 772, 27], [784, 12, 772, 28, "onPress"], [784, 19, 772, 35], [784, 21, 772, 37, "onCancel"], [784, 29, 772, 46], [785, 12, 772, 47, "style"], [785, 17, 772, 52], [785, 19, 772, 54, "styles"], [785, 25, 772, 60], [785, 26, 772, 61, "secondaryButton"], [785, 41, 772, 77], [786, 12, 772, 77, "children"], [786, 20, 772, 77], [786, 35, 773, 12], [786, 39, 773, 12, "_jsxDevRuntime"], [786, 53, 773, 12], [786, 54, 773, 12, "jsxDEV"], [786, 60, 773, 12], [786, 62, 773, 13, "_Text"], [786, 67, 773, 13], [786, 68, 773, 13, "default"], [786, 75, 773, 17], [787, 14, 773, 18, "style"], [787, 19, 773, 23], [787, 21, 773, 25, "styles"], [787, 27, 773, 31], [787, 28, 773, 32, "secondaryButtonText"], [787, 47, 773, 52], [788, 14, 773, 52, "children"], [788, 22, 773, 52], [788, 24, 773, 53], [789, 12, 773, 59], [790, 14, 773, 59, "fileName"], [790, 22, 773, 59], [790, 24, 773, 59, "_jsxFileName"], [790, 36, 773, 59], [791, 14, 773, 59, "lineNumber"], [791, 24, 773, 59], [792, 14, 773, 59, "columnNumber"], [792, 26, 773, 59], [793, 12, 773, 59], [793, 19, 773, 65], [794, 10, 773, 66], [795, 12, 773, 66, "fileName"], [795, 20, 773, 66], [795, 22, 773, 66, "_jsxFileName"], [795, 34, 773, 66], [796, 12, 773, 66, "lineNumber"], [796, 22, 773, 66], [797, 12, 773, 66, "columnNumber"], [797, 24, 773, 66], [798, 10, 773, 66], [798, 17, 774, 28], [798, 18, 774, 29], [799, 8, 774, 29], [800, 10, 774, 29, "fileName"], [800, 18, 774, 29], [800, 20, 774, 29, "_jsxFileName"], [800, 32, 774, 29], [801, 10, 774, 29, "lineNumber"], [801, 20, 774, 29], [802, 10, 774, 29, "columnNumber"], [802, 22, 774, 29], [803, 8, 774, 29], [803, 15, 775, 14], [804, 6, 775, 15], [805, 8, 775, 15, "fileName"], [805, 16, 775, 15], [805, 18, 775, 15, "_jsxFileName"], [805, 30, 775, 15], [806, 8, 775, 15, "lineNumber"], [806, 18, 775, 15], [807, 8, 775, 15, "columnNumber"], [807, 20, 775, 15], [808, 6, 775, 15], [808, 13, 776, 12], [808, 14, 776, 13], [809, 4, 778, 2], [810, 4, 779, 2], [811, 4, 780, 2, "console"], [811, 11, 780, 9], [811, 12, 780, 10, "log"], [811, 15, 780, 13], [811, 16, 780, 14], [811, 55, 780, 53], [811, 56, 780, 54], [812, 4, 782, 2], [812, 24, 783, 4], [812, 28, 783, 4, "_jsxDevRuntime"], [812, 42, 783, 4], [812, 43, 783, 4, "jsxDEV"], [812, 49, 783, 4], [812, 51, 783, 5, "_View"], [812, 56, 783, 5], [812, 57, 783, 5, "default"], [812, 64, 783, 9], [813, 6, 783, 10, "style"], [813, 11, 783, 15], [813, 13, 783, 17, "styles"], [813, 19, 783, 23], [813, 20, 783, 24, "container"], [813, 29, 783, 34], [814, 6, 783, 34, "children"], [814, 14, 783, 34], [814, 30, 785, 6], [814, 34, 785, 6, "_jsxDevRuntime"], [814, 48, 785, 6], [814, 49, 785, 6, "jsxDEV"], [814, 55, 785, 6], [814, 57, 785, 7, "_View"], [814, 62, 785, 7], [814, 63, 785, 7, "default"], [814, 70, 785, 11], [815, 8, 785, 12, "style"], [815, 13, 785, 17], [815, 15, 785, 19, "styles"], [815, 21, 785, 25], [815, 22, 785, 26, "cameraContainer"], [815, 37, 785, 42], [816, 8, 785, 43, "id"], [816, 10, 785, 45], [816, 12, 785, 46], [816, 29, 785, 63], [817, 8, 785, 63, "children"], [817, 16, 785, 63], [817, 32, 786, 8], [817, 36, 786, 8, "_jsxDevRuntime"], [817, 50, 786, 8], [817, 51, 786, 8, "jsxDEV"], [817, 57, 786, 8], [817, 59, 786, 9, "_expoCamera"], [817, 70, 786, 9], [817, 71, 786, 9, "CameraView"], [817, 81, 786, 19], [818, 10, 787, 10, "ref"], [818, 13, 787, 13], [818, 15, 787, 15, "cameraRef"], [818, 24, 787, 25], [819, 10, 788, 10, "style"], [819, 15, 788, 15], [819, 17, 788, 17], [819, 18, 788, 18, "styles"], [819, 24, 788, 24], [819, 25, 788, 25, "camera"], [819, 31, 788, 31], [819, 33, 788, 33], [820, 12, 788, 35, "backgroundColor"], [820, 27, 788, 50], [820, 29, 788, 52], [821, 10, 788, 62], [821, 11, 788, 63], [821, 12, 788, 65], [822, 10, 789, 10, "facing"], [822, 16, 789, 16], [822, 18, 789, 17], [822, 24, 789, 23], [823, 10, 790, 10, "onLayout"], [823, 18, 790, 18], [823, 20, 790, 21, "e"], [823, 21, 790, 22], [823, 25, 790, 27], [824, 12, 791, 12, "console"], [824, 19, 791, 19], [824, 20, 791, 20, "log"], [824, 23, 791, 23], [824, 24, 791, 24], [824, 56, 791, 56], [824, 58, 791, 58, "e"], [824, 59, 791, 59], [824, 60, 791, 60, "nativeEvent"], [824, 71, 791, 71], [824, 72, 791, 72, "layout"], [824, 78, 791, 78], [824, 79, 791, 79], [825, 12, 792, 12, "setViewSize"], [825, 23, 792, 23], [825, 24, 792, 24], [826, 14, 792, 26, "width"], [826, 19, 792, 31], [826, 21, 792, 33, "e"], [826, 22, 792, 34], [826, 23, 792, 35, "nativeEvent"], [826, 34, 792, 46], [826, 35, 792, 47, "layout"], [826, 41, 792, 53], [826, 42, 792, 54, "width"], [826, 47, 792, 59], [827, 14, 792, 61, "height"], [827, 20, 792, 67], [827, 22, 792, 69, "e"], [827, 23, 792, 70], [827, 24, 792, 71, "nativeEvent"], [827, 35, 792, 82], [827, 36, 792, 83, "layout"], [827, 42, 792, 89], [827, 43, 792, 90, "height"], [828, 12, 792, 97], [828, 13, 792, 98], [828, 14, 792, 99], [829, 10, 793, 10], [829, 11, 793, 12], [830, 10, 794, 10, "onCameraReady"], [830, 23, 794, 23], [830, 25, 794, 25, "onCameraReady"], [830, 26, 794, 25], [830, 31, 794, 31], [831, 12, 795, 12, "console"], [831, 19, 795, 19], [831, 20, 795, 20, "log"], [831, 23, 795, 23], [831, 24, 795, 24], [831, 55, 795, 55], [831, 56, 795, 56], [832, 12, 796, 12, "setIsCameraReady"], [832, 28, 796, 28], [832, 29, 796, 29], [832, 33, 796, 33], [832, 34, 796, 34], [832, 35, 796, 35], [832, 36, 796, 36], [833, 10, 797, 10], [833, 11, 797, 12], [834, 10, 798, 10, "onMountError"], [834, 22, 798, 22], [834, 24, 798, 25, "error"], [834, 29, 798, 30], [834, 33, 798, 35], [835, 12, 799, 12, "console"], [835, 19, 799, 19], [835, 20, 799, 20, "error"], [835, 25, 799, 25], [835, 26, 799, 26], [835, 63, 799, 63], [835, 65, 799, 65, "error"], [835, 70, 799, 70], [835, 71, 799, 71], [836, 12, 800, 12, "setErrorMessage"], [836, 27, 800, 27], [836, 28, 800, 28], [836, 57, 800, 57], [836, 58, 800, 58], [837, 12, 801, 12, "setProcessingState"], [837, 30, 801, 30], [837, 31, 801, 31], [837, 38, 801, 38], [837, 39, 801, 39], [838, 10, 802, 10], [839, 8, 802, 12], [840, 10, 802, 12, "fileName"], [840, 18, 802, 12], [840, 20, 802, 12, "_jsxFileName"], [840, 32, 802, 12], [841, 10, 802, 12, "lineNumber"], [841, 20, 802, 12], [842, 10, 802, 12, "columnNumber"], [842, 22, 802, 12], [843, 8, 802, 12], [843, 15, 803, 9], [843, 16, 803, 10], [843, 18, 805, 9], [843, 19, 805, 10, "isCameraReady"], [843, 32, 805, 23], [843, 49, 806, 10], [843, 53, 806, 10, "_jsxDevRuntime"], [843, 67, 806, 10], [843, 68, 806, 10, "jsxDEV"], [843, 74, 806, 10], [843, 76, 806, 11, "_View"], [843, 81, 806, 11], [843, 82, 806, 11, "default"], [843, 89, 806, 15], [844, 10, 806, 16, "style"], [844, 15, 806, 21], [844, 17, 806, 23], [844, 18, 806, 24, "StyleSheet"], [844, 37, 806, 34], [844, 38, 806, 35, "absoluteFill"], [844, 50, 806, 47], [844, 52, 806, 49], [845, 12, 806, 51, "backgroundColor"], [845, 27, 806, 66], [845, 29, 806, 68], [845, 49, 806, 88], [846, 12, 806, 90, "justifyContent"], [846, 26, 806, 104], [846, 28, 806, 106], [846, 36, 806, 114], [847, 12, 806, 116, "alignItems"], [847, 22, 806, 126], [847, 24, 806, 128], [847, 32, 806, 136], [848, 12, 806, 138, "zIndex"], [848, 18, 806, 144], [848, 20, 806, 146], [849, 10, 806, 151], [849, 11, 806, 152], [849, 12, 806, 154], [850, 10, 806, 154, "children"], [850, 18, 806, 154], [850, 33, 807, 12], [850, 37, 807, 12, "_jsxDevRuntime"], [850, 51, 807, 12], [850, 52, 807, 12, "jsxDEV"], [850, 58, 807, 12], [850, 60, 807, 13, "_View"], [850, 65, 807, 13], [850, 66, 807, 13, "default"], [850, 73, 807, 17], [851, 12, 807, 18, "style"], [851, 17, 807, 23], [851, 19, 807, 25], [852, 14, 807, 27, "backgroundColor"], [852, 29, 807, 42], [852, 31, 807, 44], [852, 51, 807, 64], [853, 14, 807, 66, "padding"], [853, 21, 807, 73], [853, 23, 807, 75], [853, 25, 807, 77], [854, 14, 807, 79, "borderRadius"], [854, 26, 807, 91], [854, 28, 807, 93], [854, 30, 807, 95], [855, 14, 807, 97, "alignItems"], [855, 24, 807, 107], [855, 26, 807, 109], [856, 12, 807, 118], [856, 13, 807, 120], [857, 12, 807, 120, "children"], [857, 20, 807, 120], [857, 36, 808, 14], [857, 40, 808, 14, "_jsxDevRuntime"], [857, 54, 808, 14], [857, 55, 808, 14, "jsxDEV"], [857, 61, 808, 14], [857, 63, 808, 15, "_ActivityIndicator"], [857, 81, 808, 15], [857, 82, 808, 15, "default"], [857, 89, 808, 32], [858, 14, 808, 33, "size"], [858, 18, 808, 37], [858, 20, 808, 38], [858, 27, 808, 45], [859, 14, 808, 46, "color"], [859, 19, 808, 51], [859, 21, 808, 52], [859, 30, 808, 61], [860, 14, 808, 62, "style"], [860, 19, 808, 67], [860, 21, 808, 69], [861, 16, 808, 71, "marginBottom"], [861, 28, 808, 83], [861, 30, 808, 85], [862, 14, 808, 88], [863, 12, 808, 90], [864, 14, 808, 90, "fileName"], [864, 22, 808, 90], [864, 24, 808, 90, "_jsxFileName"], [864, 36, 808, 90], [865, 14, 808, 90, "lineNumber"], [865, 24, 808, 90], [866, 14, 808, 90, "columnNumber"], [866, 26, 808, 90], [867, 12, 808, 90], [867, 19, 808, 92], [867, 20, 808, 93], [867, 35, 809, 14], [867, 39, 809, 14, "_jsxDevRuntime"], [867, 53, 809, 14], [867, 54, 809, 14, "jsxDEV"], [867, 60, 809, 14], [867, 62, 809, 15, "_Text"], [867, 67, 809, 15], [867, 68, 809, 15, "default"], [867, 75, 809, 19], [868, 14, 809, 20, "style"], [868, 19, 809, 25], [868, 21, 809, 27], [869, 16, 809, 29, "color"], [869, 21, 809, 34], [869, 23, 809, 36], [869, 29, 809, 42], [870, 16, 809, 44, "fontSize"], [870, 24, 809, 52], [870, 26, 809, 54], [870, 28, 809, 56], [871, 16, 809, 58, "fontWeight"], [871, 26, 809, 68], [871, 28, 809, 70], [872, 14, 809, 76], [872, 15, 809, 78], [873, 14, 809, 78, "children"], [873, 22, 809, 78], [873, 24, 809, 79], [874, 12, 809, 101], [875, 14, 809, 101, "fileName"], [875, 22, 809, 101], [875, 24, 809, 101, "_jsxFileName"], [875, 36, 809, 101], [876, 14, 809, 101, "lineNumber"], [876, 24, 809, 101], [877, 14, 809, 101, "columnNumber"], [877, 26, 809, 101], [878, 12, 809, 101], [878, 19, 809, 107], [878, 20, 809, 108], [878, 35, 810, 14], [878, 39, 810, 14, "_jsxDevRuntime"], [878, 53, 810, 14], [878, 54, 810, 14, "jsxDEV"], [878, 60, 810, 14], [878, 62, 810, 15, "_Text"], [878, 67, 810, 15], [878, 68, 810, 15, "default"], [878, 75, 810, 19], [879, 14, 810, 20, "style"], [879, 19, 810, 25], [879, 21, 810, 27], [880, 16, 810, 29, "color"], [880, 21, 810, 34], [880, 23, 810, 36], [880, 32, 810, 45], [881, 16, 810, 47, "fontSize"], [881, 24, 810, 55], [881, 26, 810, 57], [881, 28, 810, 59], [882, 16, 810, 61, "marginTop"], [882, 25, 810, 70], [882, 27, 810, 72], [883, 14, 810, 74], [883, 15, 810, 76], [884, 14, 810, 76, "children"], [884, 22, 810, 76], [884, 24, 810, 77], [885, 12, 810, 88], [886, 14, 810, 88, "fileName"], [886, 22, 810, 88], [886, 24, 810, 88, "_jsxFileName"], [886, 36, 810, 88], [887, 14, 810, 88, "lineNumber"], [887, 24, 810, 88], [888, 14, 810, 88, "columnNumber"], [888, 26, 810, 88], [889, 12, 810, 88], [889, 19, 810, 94], [889, 20, 810, 95], [890, 10, 810, 95], [891, 12, 810, 95, "fileName"], [891, 20, 810, 95], [891, 22, 810, 95, "_jsxFileName"], [891, 34, 810, 95], [892, 12, 810, 95, "lineNumber"], [892, 22, 810, 95], [893, 12, 810, 95, "columnNumber"], [893, 24, 810, 95], [894, 10, 810, 95], [894, 17, 811, 18], [895, 8, 811, 19], [896, 10, 811, 19, "fileName"], [896, 18, 811, 19], [896, 20, 811, 19, "_jsxFileName"], [896, 32, 811, 19], [897, 10, 811, 19, "lineNumber"], [897, 20, 811, 19], [898, 10, 811, 19, "columnNumber"], [898, 22, 811, 19], [899, 8, 811, 19], [899, 15, 812, 16], [899, 16, 813, 9], [899, 18, 816, 9, "isCameraReady"], [899, 31, 816, 22], [899, 35, 816, 26, "previewBlurEnabled"], [899, 53, 816, 44], [899, 57, 816, 48, "viewSize"], [899, 65, 816, 56], [899, 66, 816, 57, "width"], [899, 71, 816, 62], [899, 74, 816, 65], [899, 75, 816, 66], [899, 92, 817, 10], [899, 96, 817, 10, "_jsxDevRuntime"], [899, 110, 817, 10], [899, 111, 817, 10, "jsxDEV"], [899, 117, 817, 10], [899, 119, 817, 10, "_jsxDevRuntime"], [899, 133, 817, 10], [899, 134, 817, 10, "Fragment"], [899, 142, 817, 10], [900, 10, 817, 10, "children"], [900, 18, 817, 10], [900, 34, 819, 12], [900, 38, 819, 12, "_jsxDevRuntime"], [900, 52, 819, 12], [900, 53, 819, 12, "jsxDEV"], [900, 59, 819, 12], [900, 61, 819, 13, "_LiveFaceCanvas"], [900, 76, 819, 13], [900, 77, 819, 13, "default"], [900, 84, 819, 27], [901, 12, 819, 28, "containerId"], [901, 23, 819, 39], [901, 25, 819, 40], [901, 42, 819, 57], [902, 12, 819, 58, "width"], [902, 17, 819, 63], [902, 19, 819, 65, "viewSize"], [902, 27, 819, 73], [902, 28, 819, 74, "width"], [902, 33, 819, 80], [903, 12, 819, 81, "height"], [903, 18, 819, 87], [903, 20, 819, 89, "viewSize"], [903, 28, 819, 97], [903, 29, 819, 98, "height"], [904, 10, 819, 105], [905, 12, 819, 105, "fileName"], [905, 20, 819, 105], [905, 22, 819, 105, "_jsxFileName"], [905, 34, 819, 105], [906, 12, 819, 105, "lineNumber"], [906, 22, 819, 105], [907, 12, 819, 105, "columnNumber"], [907, 24, 819, 105], [908, 10, 819, 105], [908, 17, 819, 107], [908, 18, 819, 108], [908, 33, 820, 12], [908, 37, 820, 12, "_jsxDevRuntime"], [908, 51, 820, 12], [908, 52, 820, 12, "jsxDEV"], [908, 58, 820, 12], [908, 60, 820, 13, "_View"], [908, 65, 820, 13], [908, 66, 820, 13, "default"], [908, 73, 820, 17], [909, 12, 820, 18, "style"], [909, 17, 820, 23], [909, 19, 820, 25], [909, 20, 820, 26, "StyleSheet"], [909, 39, 820, 36], [909, 40, 820, 37, "absoluteFill"], [909, 52, 820, 49], [909, 54, 820, 51], [910, 14, 820, 53, "pointerEvents"], [910, 27, 820, 66], [910, 29, 820, 68], [911, 12, 820, 75], [911, 13, 820, 76], [911, 14, 820, 78], [912, 12, 820, 78, "children"], [912, 20, 820, 78], [912, 36, 822, 12], [912, 40, 822, 12, "_jsxDevRuntime"], [912, 54, 822, 12], [912, 55, 822, 12, "jsxDEV"], [912, 61, 822, 12], [912, 63, 822, 13, "_expoBlur"], [912, 72, 822, 13], [912, 73, 822, 13, "BlurView"], [912, 81, 822, 21], [913, 14, 822, 22, "intensity"], [913, 23, 822, 31], [913, 25, 822, 33], [913, 27, 822, 36], [914, 14, 822, 37, "tint"], [914, 18, 822, 41], [914, 20, 822, 42], [914, 26, 822, 48], [915, 14, 822, 49, "style"], [915, 19, 822, 54], [915, 21, 822, 56], [915, 22, 822, 57, "styles"], [915, 28, 822, 63], [915, 29, 822, 64, "blurZone"], [915, 37, 822, 72], [915, 39, 822, 74], [916, 16, 823, 14, "left"], [916, 20, 823, 18], [916, 22, 823, 20], [916, 23, 823, 21], [917, 16, 824, 14, "top"], [917, 19, 824, 17], [917, 21, 824, 19, "viewSize"], [917, 29, 824, 27], [917, 30, 824, 28, "height"], [917, 36, 824, 34], [917, 39, 824, 37], [917, 42, 824, 40], [918, 16, 825, 14, "width"], [918, 21, 825, 19], [918, 23, 825, 21, "viewSize"], [918, 31, 825, 29], [918, 32, 825, 30, "width"], [918, 37, 825, 35], [919, 16, 826, 14, "height"], [919, 22, 826, 20], [919, 24, 826, 22, "viewSize"], [919, 32, 826, 30], [919, 33, 826, 31, "height"], [919, 39, 826, 37], [919, 42, 826, 40], [919, 46, 826, 44], [920, 16, 827, 14, "borderRadius"], [920, 28, 827, 26], [920, 30, 827, 28], [921, 14, 828, 12], [921, 15, 828, 13], [922, 12, 828, 15], [923, 14, 828, 15, "fileName"], [923, 22, 828, 15], [923, 24, 828, 15, "_jsxFileName"], [923, 36, 828, 15], [924, 14, 828, 15, "lineNumber"], [924, 24, 828, 15], [925, 14, 828, 15, "columnNumber"], [925, 26, 828, 15], [926, 12, 828, 15], [926, 19, 828, 17], [926, 20, 828, 18], [926, 35, 830, 12], [926, 39, 830, 12, "_jsxDevRuntime"], [926, 53, 830, 12], [926, 54, 830, 12, "jsxDEV"], [926, 60, 830, 12], [926, 62, 830, 13, "_expoBlur"], [926, 71, 830, 13], [926, 72, 830, 13, "BlurView"], [926, 80, 830, 21], [927, 14, 830, 22, "intensity"], [927, 23, 830, 31], [927, 25, 830, 33], [927, 27, 830, 36], [928, 14, 830, 37, "tint"], [928, 18, 830, 41], [928, 20, 830, 42], [928, 26, 830, 48], [929, 14, 830, 49, "style"], [929, 19, 830, 54], [929, 21, 830, 56], [929, 22, 830, 57, "styles"], [929, 28, 830, 63], [929, 29, 830, 64, "blurZone"], [929, 37, 830, 72], [929, 39, 830, 74], [930, 16, 831, 14, "left"], [930, 20, 831, 18], [930, 22, 831, 20], [930, 23, 831, 21], [931, 16, 832, 14, "top"], [931, 19, 832, 17], [931, 21, 832, 19], [931, 22, 832, 20], [932, 16, 833, 14, "width"], [932, 21, 833, 19], [932, 23, 833, 21, "viewSize"], [932, 31, 833, 29], [932, 32, 833, 30, "width"], [932, 37, 833, 35], [933, 16, 834, 14, "height"], [933, 22, 834, 20], [933, 24, 834, 22, "viewSize"], [933, 32, 834, 30], [933, 33, 834, 31, "height"], [933, 39, 834, 37], [933, 42, 834, 40], [933, 45, 834, 43], [934, 16, 835, 14, "borderRadius"], [934, 28, 835, 26], [934, 30, 835, 28], [935, 14, 836, 12], [935, 15, 836, 13], [936, 12, 836, 15], [937, 14, 836, 15, "fileName"], [937, 22, 836, 15], [937, 24, 836, 15, "_jsxFileName"], [937, 36, 836, 15], [938, 14, 836, 15, "lineNumber"], [938, 24, 836, 15], [939, 14, 836, 15, "columnNumber"], [939, 26, 836, 15], [940, 12, 836, 15], [940, 19, 836, 17], [940, 20, 836, 18], [940, 35, 838, 12], [940, 39, 838, 12, "_jsxDevRuntime"], [940, 53, 838, 12], [940, 54, 838, 12, "jsxDEV"], [940, 60, 838, 12], [940, 62, 838, 13, "_expoBlur"], [940, 71, 838, 13], [940, 72, 838, 13, "BlurView"], [940, 80, 838, 21], [941, 14, 838, 22, "intensity"], [941, 23, 838, 31], [941, 25, 838, 33], [941, 27, 838, 36], [942, 14, 838, 37, "tint"], [942, 18, 838, 41], [942, 20, 838, 42], [942, 26, 838, 48], [943, 14, 838, 49, "style"], [943, 19, 838, 54], [943, 21, 838, 56], [943, 22, 838, 57, "styles"], [943, 28, 838, 63], [943, 29, 838, 64, "blurZone"], [943, 37, 838, 72], [943, 39, 838, 74], [944, 16, 839, 14, "left"], [944, 20, 839, 18], [944, 22, 839, 20, "viewSize"], [944, 30, 839, 28], [944, 31, 839, 29, "width"], [944, 36, 839, 34], [944, 39, 839, 37], [944, 42, 839, 40], [944, 45, 839, 44, "viewSize"], [944, 53, 839, 52], [944, 54, 839, 53, "width"], [944, 59, 839, 58], [944, 62, 839, 61], [944, 66, 839, 66], [945, 16, 840, 14, "top"], [945, 19, 840, 17], [945, 21, 840, 19, "viewSize"], [945, 29, 840, 27], [945, 30, 840, 28, "height"], [945, 36, 840, 34], [945, 39, 840, 37], [945, 43, 840, 41], [945, 46, 840, 45, "viewSize"], [945, 54, 840, 53], [945, 55, 840, 54, "width"], [945, 60, 840, 59], [945, 63, 840, 62], [945, 67, 840, 67], [946, 16, 841, 14, "width"], [946, 21, 841, 19], [946, 23, 841, 21, "viewSize"], [946, 31, 841, 29], [946, 32, 841, 30, "width"], [946, 37, 841, 35], [946, 40, 841, 38], [946, 43, 841, 41], [947, 16, 842, 14, "height"], [947, 22, 842, 20], [947, 24, 842, 22, "viewSize"], [947, 32, 842, 30], [947, 33, 842, 31, "width"], [947, 38, 842, 36], [947, 41, 842, 39], [947, 44, 842, 42], [948, 16, 843, 14, "borderRadius"], [948, 28, 843, 26], [948, 30, 843, 29, "viewSize"], [948, 38, 843, 37], [948, 39, 843, 38, "width"], [948, 44, 843, 43], [948, 47, 843, 46], [948, 50, 843, 49], [948, 53, 843, 53], [949, 14, 844, 12], [949, 15, 844, 13], [950, 12, 844, 15], [951, 14, 844, 15, "fileName"], [951, 22, 844, 15], [951, 24, 844, 15, "_jsxFileName"], [951, 36, 844, 15], [952, 14, 844, 15, "lineNumber"], [952, 24, 844, 15], [953, 14, 844, 15, "columnNumber"], [953, 26, 844, 15], [954, 12, 844, 15], [954, 19, 844, 17], [954, 20, 844, 18], [954, 35, 845, 12], [954, 39, 845, 12, "_jsxDevRuntime"], [954, 53, 845, 12], [954, 54, 845, 12, "jsxDEV"], [954, 60, 845, 12], [954, 62, 845, 13, "_expoBlur"], [954, 71, 845, 13], [954, 72, 845, 13, "BlurView"], [954, 80, 845, 21], [955, 14, 845, 22, "intensity"], [955, 23, 845, 31], [955, 25, 845, 33], [955, 27, 845, 36], [956, 14, 845, 37, "tint"], [956, 18, 845, 41], [956, 20, 845, 42], [956, 26, 845, 48], [957, 14, 845, 49, "style"], [957, 19, 845, 54], [957, 21, 845, 56], [957, 22, 845, 57, "styles"], [957, 28, 845, 63], [957, 29, 845, 64, "blurZone"], [957, 37, 845, 72], [957, 39, 845, 74], [958, 16, 846, 14, "left"], [958, 20, 846, 18], [958, 22, 846, 20, "viewSize"], [958, 30, 846, 28], [958, 31, 846, 29, "width"], [958, 36, 846, 34], [958, 39, 846, 37], [958, 42, 846, 40], [958, 45, 846, 44, "viewSize"], [958, 53, 846, 52], [958, 54, 846, 53, "width"], [958, 59, 846, 58], [958, 62, 846, 61], [958, 66, 846, 66], [959, 16, 847, 14, "top"], [959, 19, 847, 17], [959, 21, 847, 19, "viewSize"], [959, 29, 847, 27], [959, 30, 847, 28, "height"], [959, 36, 847, 34], [959, 39, 847, 37], [959, 42, 847, 40], [959, 45, 847, 44, "viewSize"], [959, 53, 847, 52], [959, 54, 847, 53, "width"], [959, 59, 847, 58], [959, 62, 847, 61], [959, 66, 847, 66], [960, 16, 848, 14, "width"], [960, 21, 848, 19], [960, 23, 848, 21, "viewSize"], [960, 31, 848, 29], [960, 32, 848, 30, "width"], [960, 37, 848, 35], [960, 40, 848, 38], [960, 43, 848, 41], [961, 16, 849, 14, "height"], [961, 22, 849, 20], [961, 24, 849, 22, "viewSize"], [961, 32, 849, 30], [961, 33, 849, 31, "width"], [961, 38, 849, 36], [961, 41, 849, 39], [961, 44, 849, 42], [962, 16, 850, 14, "borderRadius"], [962, 28, 850, 26], [962, 30, 850, 29, "viewSize"], [962, 38, 850, 37], [962, 39, 850, 38, "width"], [962, 44, 850, 43], [962, 47, 850, 46], [962, 50, 850, 49], [962, 53, 850, 53], [963, 14, 851, 12], [963, 15, 851, 13], [964, 12, 851, 15], [965, 14, 851, 15, "fileName"], [965, 22, 851, 15], [965, 24, 851, 15, "_jsxFileName"], [965, 36, 851, 15], [966, 14, 851, 15, "lineNumber"], [966, 24, 851, 15], [967, 14, 851, 15, "columnNumber"], [967, 26, 851, 15], [968, 12, 851, 15], [968, 19, 851, 17], [968, 20, 851, 18], [968, 35, 852, 12], [968, 39, 852, 12, "_jsxDevRuntime"], [968, 53, 852, 12], [968, 54, 852, 12, "jsxDEV"], [968, 60, 852, 12], [968, 62, 852, 13, "_expoBlur"], [968, 71, 852, 13], [968, 72, 852, 13, "BlurView"], [968, 80, 852, 21], [969, 14, 852, 22, "intensity"], [969, 23, 852, 31], [969, 25, 852, 33], [969, 27, 852, 36], [970, 14, 852, 37, "tint"], [970, 18, 852, 41], [970, 20, 852, 42], [970, 26, 852, 48], [971, 14, 852, 49, "style"], [971, 19, 852, 54], [971, 21, 852, 56], [971, 22, 852, 57, "styles"], [971, 28, 852, 63], [971, 29, 852, 64, "blurZone"], [971, 37, 852, 72], [971, 39, 852, 74], [972, 16, 853, 14, "left"], [972, 20, 853, 18], [972, 22, 853, 20, "viewSize"], [972, 30, 853, 28], [972, 31, 853, 29, "width"], [972, 36, 853, 34], [972, 39, 853, 37], [972, 42, 853, 40], [972, 45, 853, 44, "viewSize"], [972, 53, 853, 52], [972, 54, 853, 53, "width"], [972, 59, 853, 58], [972, 62, 853, 61], [972, 66, 853, 66], [973, 16, 854, 14, "top"], [973, 19, 854, 17], [973, 21, 854, 19, "viewSize"], [973, 29, 854, 27], [973, 30, 854, 28, "height"], [973, 36, 854, 34], [973, 39, 854, 37], [973, 42, 854, 40], [973, 45, 854, 44, "viewSize"], [973, 53, 854, 52], [973, 54, 854, 53, "width"], [973, 59, 854, 58], [973, 62, 854, 61], [973, 66, 854, 66], [974, 16, 855, 14, "width"], [974, 21, 855, 19], [974, 23, 855, 21, "viewSize"], [974, 31, 855, 29], [974, 32, 855, 30, "width"], [974, 37, 855, 35], [974, 40, 855, 38], [974, 43, 855, 41], [975, 16, 856, 14, "height"], [975, 22, 856, 20], [975, 24, 856, 22, "viewSize"], [975, 32, 856, 30], [975, 33, 856, 31, "width"], [975, 38, 856, 36], [975, 41, 856, 39], [975, 44, 856, 42], [976, 16, 857, 14, "borderRadius"], [976, 28, 857, 26], [976, 30, 857, 29, "viewSize"], [976, 38, 857, 37], [976, 39, 857, 38, "width"], [976, 44, 857, 43], [976, 47, 857, 46], [976, 50, 857, 49], [976, 53, 857, 53], [977, 14, 858, 12], [977, 15, 858, 13], [978, 12, 858, 15], [979, 14, 858, 15, "fileName"], [979, 22, 858, 15], [979, 24, 858, 15, "_jsxFileName"], [979, 36, 858, 15], [980, 14, 858, 15, "lineNumber"], [980, 24, 858, 15], [981, 14, 858, 15, "columnNumber"], [981, 26, 858, 15], [982, 12, 858, 15], [982, 19, 858, 17], [982, 20, 858, 18], [982, 22, 860, 13, "__DEV__"], [982, 29, 860, 20], [982, 46, 861, 14], [982, 50, 861, 14, "_jsxDevRuntime"], [982, 64, 861, 14], [982, 65, 861, 14, "jsxDEV"], [982, 71, 861, 14], [982, 73, 861, 15, "_View"], [982, 78, 861, 15], [982, 79, 861, 15, "default"], [982, 86, 861, 19], [983, 14, 861, 20, "style"], [983, 19, 861, 25], [983, 21, 861, 27, "styles"], [983, 27, 861, 33], [983, 28, 861, 34, "previewChip"], [983, 39, 861, 46], [984, 14, 861, 46, "children"], [984, 22, 861, 46], [984, 37, 862, 16], [984, 41, 862, 16, "_jsxDevRuntime"], [984, 55, 862, 16], [984, 56, 862, 16, "jsxDEV"], [984, 62, 862, 16], [984, 64, 862, 17, "_Text"], [984, 69, 862, 17], [984, 70, 862, 17, "default"], [984, 77, 862, 21], [985, 16, 862, 22, "style"], [985, 21, 862, 27], [985, 23, 862, 29, "styles"], [985, 29, 862, 35], [985, 30, 862, 36, "previewChipText"], [985, 45, 862, 52], [986, 16, 862, 52, "children"], [986, 24, 862, 52], [986, 26, 862, 53], [987, 14, 862, 73], [988, 16, 862, 73, "fileName"], [988, 24, 862, 73], [988, 26, 862, 73, "_jsxFileName"], [988, 38, 862, 73], [989, 16, 862, 73, "lineNumber"], [989, 26, 862, 73], [990, 16, 862, 73, "columnNumber"], [990, 28, 862, 73], [991, 14, 862, 73], [991, 21, 862, 79], [992, 12, 862, 80], [993, 14, 862, 80, "fileName"], [993, 22, 862, 80], [993, 24, 862, 80, "_jsxFileName"], [993, 36, 862, 80], [994, 14, 862, 80, "lineNumber"], [994, 24, 862, 80], [995, 14, 862, 80, "columnNumber"], [995, 26, 862, 80], [996, 12, 862, 80], [996, 19, 863, 20], [996, 20, 864, 13], [997, 10, 864, 13], [998, 12, 864, 13, "fileName"], [998, 20, 864, 13], [998, 22, 864, 13, "_jsxFileName"], [998, 34, 864, 13], [999, 12, 864, 13, "lineNumber"], [999, 22, 864, 13], [1000, 12, 864, 13, "columnNumber"], [1000, 24, 864, 13], [1001, 10, 864, 13], [1001, 17, 865, 18], [1001, 18, 865, 19], [1002, 8, 865, 19], [1002, 23, 866, 12], [1002, 24, 867, 9], [1002, 26, 869, 9, "isCameraReady"], [1002, 39, 869, 22], [1002, 56, 870, 10], [1002, 60, 870, 10, "_jsxDevRuntime"], [1002, 74, 870, 10], [1002, 75, 870, 10, "jsxDEV"], [1002, 81, 870, 10], [1002, 83, 870, 10, "_jsxDevRuntime"], [1002, 97, 870, 10], [1002, 98, 870, 10, "Fragment"], [1002, 106, 870, 10], [1003, 10, 870, 10, "children"], [1003, 18, 870, 10], [1003, 34, 872, 12], [1003, 38, 872, 12, "_jsxDevRuntime"], [1003, 52, 872, 12], [1003, 53, 872, 12, "jsxDEV"], [1003, 59, 872, 12], [1003, 61, 872, 13, "_View"], [1003, 66, 872, 13], [1003, 67, 872, 13, "default"], [1003, 74, 872, 17], [1004, 12, 872, 18, "style"], [1004, 17, 872, 23], [1004, 19, 872, 25, "styles"], [1004, 25, 872, 31], [1004, 26, 872, 32, "headerOverlay"], [1004, 39, 872, 46], [1005, 12, 872, 46, "children"], [1005, 20, 872, 46], [1005, 35, 873, 14], [1005, 39, 873, 14, "_jsxDevRuntime"], [1005, 53, 873, 14], [1005, 54, 873, 14, "jsxDEV"], [1005, 60, 873, 14], [1005, 62, 873, 15, "_View"], [1005, 67, 873, 15], [1005, 68, 873, 15, "default"], [1005, 75, 873, 19], [1006, 14, 873, 20, "style"], [1006, 19, 873, 25], [1006, 21, 873, 27, "styles"], [1006, 27, 873, 33], [1006, 28, 873, 34, "headerContent"], [1006, 41, 873, 48], [1007, 14, 873, 48, "children"], [1007, 22, 873, 48], [1007, 38, 874, 16], [1007, 42, 874, 16, "_jsxDevRuntime"], [1007, 56, 874, 16], [1007, 57, 874, 16, "jsxDEV"], [1007, 63, 874, 16], [1007, 65, 874, 17, "_View"], [1007, 70, 874, 17], [1007, 71, 874, 17, "default"], [1007, 78, 874, 21], [1008, 16, 874, 22, "style"], [1008, 21, 874, 27], [1008, 23, 874, 29, "styles"], [1008, 29, 874, 35], [1008, 30, 874, 36, "headerLeft"], [1008, 40, 874, 47], [1009, 16, 874, 47, "children"], [1009, 24, 874, 47], [1009, 40, 875, 18], [1009, 44, 875, 18, "_jsxDevRuntime"], [1009, 58, 875, 18], [1009, 59, 875, 18, "jsxDEV"], [1009, 65, 875, 18], [1009, 67, 875, 19, "_Text"], [1009, 72, 875, 19], [1009, 73, 875, 19, "default"], [1009, 80, 875, 23], [1010, 18, 875, 24, "style"], [1010, 23, 875, 29], [1010, 25, 875, 31, "styles"], [1010, 31, 875, 37], [1010, 32, 875, 38, "headerTitle"], [1010, 43, 875, 50], [1011, 18, 875, 50, "children"], [1011, 26, 875, 50], [1011, 28, 875, 51], [1012, 16, 875, 62], [1013, 18, 875, 62, "fileName"], [1013, 26, 875, 62], [1013, 28, 875, 62, "_jsxFileName"], [1013, 40, 875, 62], [1014, 18, 875, 62, "lineNumber"], [1014, 28, 875, 62], [1015, 18, 875, 62, "columnNumber"], [1015, 30, 875, 62], [1016, 16, 875, 62], [1016, 23, 875, 68], [1016, 24, 875, 69], [1016, 39, 876, 18], [1016, 43, 876, 18, "_jsxDevRuntime"], [1016, 57, 876, 18], [1016, 58, 876, 18, "jsxDEV"], [1016, 64, 876, 18], [1016, 66, 876, 19, "_View"], [1016, 71, 876, 19], [1016, 72, 876, 19, "default"], [1016, 79, 876, 23], [1017, 18, 876, 24, "style"], [1017, 23, 876, 29], [1017, 25, 876, 31, "styles"], [1017, 31, 876, 37], [1017, 32, 876, 38, "subtitleRow"], [1017, 43, 876, 50], [1018, 18, 876, 50, "children"], [1018, 26, 876, 50], [1018, 42, 877, 20], [1018, 46, 877, 20, "_jsxDevRuntime"], [1018, 60, 877, 20], [1018, 61, 877, 20, "jsxDEV"], [1018, 67, 877, 20], [1018, 69, 877, 21, "_Text"], [1018, 74, 877, 21], [1018, 75, 877, 21, "default"], [1018, 82, 877, 25], [1019, 20, 877, 26, "style"], [1019, 25, 877, 31], [1019, 27, 877, 33, "styles"], [1019, 33, 877, 39], [1019, 34, 877, 40, "webIcon"], [1019, 41, 877, 48], [1020, 20, 877, 48, "children"], [1020, 28, 877, 48], [1020, 30, 877, 49], [1021, 18, 877, 51], [1022, 20, 877, 51, "fileName"], [1022, 28, 877, 51], [1022, 30, 877, 51, "_jsxFileName"], [1022, 42, 877, 51], [1023, 20, 877, 51, "lineNumber"], [1023, 30, 877, 51], [1024, 20, 877, 51, "columnNumber"], [1024, 32, 877, 51], [1025, 18, 877, 51], [1025, 25, 877, 57], [1025, 26, 877, 58], [1025, 41, 878, 20], [1025, 45, 878, 20, "_jsxDevRuntime"], [1025, 59, 878, 20], [1025, 60, 878, 20, "jsxDEV"], [1025, 66, 878, 20], [1025, 68, 878, 21, "_Text"], [1025, 73, 878, 21], [1025, 74, 878, 21, "default"], [1025, 81, 878, 25], [1026, 20, 878, 26, "style"], [1026, 25, 878, 31], [1026, 27, 878, 33, "styles"], [1026, 33, 878, 39], [1026, 34, 878, 40, "headerSubtitle"], [1026, 48, 878, 55], [1027, 20, 878, 55, "children"], [1027, 28, 878, 55], [1027, 30, 878, 56], [1028, 18, 878, 71], [1029, 20, 878, 71, "fileName"], [1029, 28, 878, 71], [1029, 30, 878, 71, "_jsxFileName"], [1029, 42, 878, 71], [1030, 20, 878, 71, "lineNumber"], [1030, 30, 878, 71], [1031, 20, 878, 71, "columnNumber"], [1031, 32, 878, 71], [1032, 18, 878, 71], [1032, 25, 878, 77], [1032, 26, 878, 78], [1033, 16, 878, 78], [1034, 18, 878, 78, "fileName"], [1034, 26, 878, 78], [1034, 28, 878, 78, "_jsxFileName"], [1034, 40, 878, 78], [1035, 18, 878, 78, "lineNumber"], [1035, 28, 878, 78], [1036, 18, 878, 78, "columnNumber"], [1036, 30, 878, 78], [1037, 16, 878, 78], [1037, 23, 879, 24], [1037, 24, 879, 25], [1037, 26, 880, 19, "challengeCode"], [1037, 39, 880, 32], [1037, 56, 881, 20], [1037, 60, 881, 20, "_jsxDevRuntime"], [1037, 74, 881, 20], [1037, 75, 881, 20, "jsxDEV"], [1037, 81, 881, 20], [1037, 83, 881, 21, "_View"], [1037, 88, 881, 21], [1037, 89, 881, 21, "default"], [1037, 96, 881, 25], [1038, 18, 881, 26, "style"], [1038, 23, 881, 31], [1038, 25, 881, 33, "styles"], [1038, 31, 881, 39], [1038, 32, 881, 40, "challengeRow"], [1038, 44, 881, 53], [1039, 18, 881, 53, "children"], [1039, 26, 881, 53], [1039, 42, 882, 22], [1039, 46, 882, 22, "_jsxDevRuntime"], [1039, 60, 882, 22], [1039, 61, 882, 22, "jsxDEV"], [1039, 67, 882, 22], [1039, 69, 882, 23, "_lucideReactNative"], [1039, 87, 882, 23], [1039, 88, 882, 23, "Shield"], [1039, 94, 882, 29], [1040, 20, 882, 30, "size"], [1040, 24, 882, 34], [1040, 26, 882, 36], [1040, 28, 882, 39], [1041, 20, 882, 40, "color"], [1041, 25, 882, 45], [1041, 27, 882, 46], [1042, 18, 882, 52], [1043, 20, 882, 52, "fileName"], [1043, 28, 882, 52], [1043, 30, 882, 52, "_jsxFileName"], [1043, 42, 882, 52], [1044, 20, 882, 52, "lineNumber"], [1044, 30, 882, 52], [1045, 20, 882, 52, "columnNumber"], [1045, 32, 882, 52], [1046, 18, 882, 52], [1046, 25, 882, 54], [1046, 26, 882, 55], [1046, 41, 883, 22], [1046, 45, 883, 22, "_jsxDevRuntime"], [1046, 59, 883, 22], [1046, 60, 883, 22, "jsxDEV"], [1046, 66, 883, 22], [1046, 68, 883, 23, "_Text"], [1046, 73, 883, 23], [1046, 74, 883, 23, "default"], [1046, 81, 883, 27], [1047, 20, 883, 28, "style"], [1047, 25, 883, 33], [1047, 27, 883, 35, "styles"], [1047, 33, 883, 41], [1047, 34, 883, 42, "challengeCode"], [1047, 47, 883, 56], [1048, 20, 883, 56, "children"], [1048, 28, 883, 56], [1048, 30, 883, 58, "challengeCode"], [1049, 18, 883, 71], [1050, 20, 883, 71, "fileName"], [1050, 28, 883, 71], [1050, 30, 883, 71, "_jsxFileName"], [1050, 42, 883, 71], [1051, 20, 883, 71, "lineNumber"], [1051, 30, 883, 71], [1052, 20, 883, 71, "columnNumber"], [1052, 32, 883, 71], [1053, 18, 883, 71], [1053, 25, 883, 78], [1053, 26, 883, 79], [1054, 16, 883, 79], [1055, 18, 883, 79, "fileName"], [1055, 26, 883, 79], [1055, 28, 883, 79, "_jsxFileName"], [1055, 40, 883, 79], [1056, 18, 883, 79, "lineNumber"], [1056, 28, 883, 79], [1057, 18, 883, 79, "columnNumber"], [1057, 30, 883, 79], [1058, 16, 883, 79], [1058, 23, 884, 26], [1058, 24, 885, 19], [1059, 14, 885, 19], [1060, 16, 885, 19, "fileName"], [1060, 24, 885, 19], [1060, 26, 885, 19, "_jsxFileName"], [1060, 38, 885, 19], [1061, 16, 885, 19, "lineNumber"], [1061, 26, 885, 19], [1062, 16, 885, 19, "columnNumber"], [1062, 28, 885, 19], [1063, 14, 885, 19], [1063, 21, 886, 22], [1063, 22, 886, 23], [1063, 37, 887, 16], [1063, 41, 887, 16, "_jsxDevRuntime"], [1063, 55, 887, 16], [1063, 56, 887, 16, "jsxDEV"], [1063, 62, 887, 16], [1063, 64, 887, 17, "_TouchableOpacity"], [1063, 81, 887, 17], [1063, 82, 887, 17, "default"], [1063, 89, 887, 33], [1064, 16, 887, 34, "onPress"], [1064, 23, 887, 41], [1064, 25, 887, 43, "onCancel"], [1064, 33, 887, 52], [1065, 16, 887, 53, "style"], [1065, 21, 887, 58], [1065, 23, 887, 60, "styles"], [1065, 29, 887, 66], [1065, 30, 887, 67, "closeButton"], [1065, 41, 887, 79], [1066, 16, 887, 79, "children"], [1066, 24, 887, 79], [1066, 39, 888, 18], [1066, 43, 888, 18, "_jsxDevRuntime"], [1066, 57, 888, 18], [1066, 58, 888, 18, "jsxDEV"], [1066, 64, 888, 18], [1066, 66, 888, 19, "_lucideReactNative"], [1066, 84, 888, 19], [1066, 85, 888, 19, "X"], [1066, 86, 888, 20], [1067, 18, 888, 21, "size"], [1067, 22, 888, 25], [1067, 24, 888, 27], [1067, 26, 888, 30], [1068, 18, 888, 31, "color"], [1068, 23, 888, 36], [1068, 25, 888, 37], [1069, 16, 888, 43], [1070, 18, 888, 43, "fileName"], [1070, 26, 888, 43], [1070, 28, 888, 43, "_jsxFileName"], [1070, 40, 888, 43], [1071, 18, 888, 43, "lineNumber"], [1071, 28, 888, 43], [1072, 18, 888, 43, "columnNumber"], [1072, 30, 888, 43], [1073, 16, 888, 43], [1073, 23, 888, 45], [1074, 14, 888, 46], [1075, 16, 888, 46, "fileName"], [1075, 24, 888, 46], [1075, 26, 888, 46, "_jsxFileName"], [1075, 38, 888, 46], [1076, 16, 888, 46, "lineNumber"], [1076, 26, 888, 46], [1077, 16, 888, 46, "columnNumber"], [1077, 28, 888, 46], [1078, 14, 888, 46], [1078, 21, 889, 34], [1078, 22, 889, 35], [1079, 12, 889, 35], [1080, 14, 889, 35, "fileName"], [1080, 22, 889, 35], [1080, 24, 889, 35, "_jsxFileName"], [1080, 36, 889, 35], [1081, 14, 889, 35, "lineNumber"], [1081, 24, 889, 35], [1082, 14, 889, 35, "columnNumber"], [1082, 26, 889, 35], [1083, 12, 889, 35], [1083, 19, 890, 20], [1084, 10, 890, 21], [1085, 12, 890, 21, "fileName"], [1085, 20, 890, 21], [1085, 22, 890, 21, "_jsxFileName"], [1085, 34, 890, 21], [1086, 12, 890, 21, "lineNumber"], [1086, 22, 890, 21], [1087, 12, 890, 21, "columnNumber"], [1087, 24, 890, 21], [1088, 10, 890, 21], [1088, 17, 891, 18], [1088, 18, 891, 19], [1088, 33, 893, 12], [1088, 37, 893, 12, "_jsxDevRuntime"], [1088, 51, 893, 12], [1088, 52, 893, 12, "jsxDEV"], [1088, 58, 893, 12], [1088, 60, 893, 13, "_View"], [1088, 65, 893, 13], [1088, 66, 893, 13, "default"], [1088, 73, 893, 17], [1089, 12, 893, 18, "style"], [1089, 17, 893, 23], [1089, 19, 893, 25, "styles"], [1089, 25, 893, 31], [1089, 26, 893, 32, "privacyNotice"], [1089, 39, 893, 46], [1090, 12, 893, 46, "children"], [1090, 20, 893, 46], [1090, 36, 894, 14], [1090, 40, 894, 14, "_jsxDevRuntime"], [1090, 54, 894, 14], [1090, 55, 894, 14, "jsxDEV"], [1090, 61, 894, 14], [1090, 63, 894, 15, "_lucideReactNative"], [1090, 81, 894, 15], [1090, 82, 894, 15, "Shield"], [1090, 88, 894, 21], [1091, 14, 894, 22, "size"], [1091, 18, 894, 26], [1091, 20, 894, 28], [1091, 22, 894, 31], [1092, 14, 894, 32, "color"], [1092, 19, 894, 37], [1092, 21, 894, 38], [1093, 12, 894, 47], [1094, 14, 894, 47, "fileName"], [1094, 22, 894, 47], [1094, 24, 894, 47, "_jsxFileName"], [1094, 36, 894, 47], [1095, 14, 894, 47, "lineNumber"], [1095, 24, 894, 47], [1096, 14, 894, 47, "columnNumber"], [1096, 26, 894, 47], [1097, 12, 894, 47], [1097, 19, 894, 49], [1097, 20, 894, 50], [1097, 35, 895, 14], [1097, 39, 895, 14, "_jsxDevRuntime"], [1097, 53, 895, 14], [1097, 54, 895, 14, "jsxDEV"], [1097, 60, 895, 14], [1097, 62, 895, 15, "_Text"], [1097, 67, 895, 15], [1097, 68, 895, 15, "default"], [1097, 75, 895, 19], [1098, 14, 895, 20, "style"], [1098, 19, 895, 25], [1098, 21, 895, 27, "styles"], [1098, 27, 895, 33], [1098, 28, 895, 34, "privacyText"], [1098, 39, 895, 46], [1099, 14, 895, 46, "children"], [1099, 22, 895, 46], [1099, 24, 895, 47], [1100, 12, 897, 14], [1101, 14, 897, 14, "fileName"], [1101, 22, 897, 14], [1101, 24, 897, 14, "_jsxFileName"], [1101, 36, 897, 14], [1102, 14, 897, 14, "lineNumber"], [1102, 24, 897, 14], [1103, 14, 897, 14, "columnNumber"], [1103, 26, 897, 14], [1104, 12, 897, 14], [1104, 19, 897, 20], [1104, 20, 897, 21], [1105, 10, 897, 21], [1106, 12, 897, 21, "fileName"], [1106, 20, 897, 21], [1106, 22, 897, 21, "_jsxFileName"], [1106, 34, 897, 21], [1107, 12, 897, 21, "lineNumber"], [1107, 22, 897, 21], [1108, 12, 897, 21, "columnNumber"], [1108, 24, 897, 21], [1109, 10, 897, 21], [1109, 17, 898, 18], [1109, 18, 898, 19], [1109, 33, 900, 12], [1109, 37, 900, 12, "_jsxDevRuntime"], [1109, 51, 900, 12], [1109, 52, 900, 12, "jsxDEV"], [1109, 58, 900, 12], [1109, 60, 900, 13, "_View"], [1109, 65, 900, 13], [1109, 66, 900, 13, "default"], [1109, 73, 900, 17], [1110, 12, 900, 18, "style"], [1110, 17, 900, 23], [1110, 19, 900, 25, "styles"], [1110, 25, 900, 31], [1110, 26, 900, 32, "footer<PERSON><PERSON><PERSON>"], [1110, 39, 900, 46], [1111, 12, 900, 46, "children"], [1111, 20, 900, 46], [1111, 36, 901, 14], [1111, 40, 901, 14, "_jsxDevRuntime"], [1111, 54, 901, 14], [1111, 55, 901, 14, "jsxDEV"], [1111, 61, 901, 14], [1111, 63, 901, 15, "_Text"], [1111, 68, 901, 15], [1111, 69, 901, 15, "default"], [1111, 76, 901, 19], [1112, 14, 901, 20, "style"], [1112, 19, 901, 25], [1112, 21, 901, 27, "styles"], [1112, 27, 901, 33], [1112, 28, 901, 34, "instruction"], [1112, 39, 901, 46], [1113, 14, 901, 46, "children"], [1113, 22, 901, 46], [1113, 24, 901, 47], [1114, 12, 903, 14], [1115, 14, 903, 14, "fileName"], [1115, 22, 903, 14], [1115, 24, 903, 14, "_jsxFileName"], [1115, 36, 903, 14], [1116, 14, 903, 14, "lineNumber"], [1116, 24, 903, 14], [1117, 14, 903, 14, "columnNumber"], [1117, 26, 903, 14], [1118, 12, 903, 14], [1118, 19, 903, 20], [1118, 20, 903, 21], [1118, 35, 905, 14], [1118, 39, 905, 14, "_jsxDevRuntime"], [1118, 53, 905, 14], [1118, 54, 905, 14, "jsxDEV"], [1118, 60, 905, 14], [1118, 62, 905, 15, "_TouchableOpacity"], [1118, 79, 905, 15], [1118, 80, 905, 15, "default"], [1118, 87, 905, 31], [1119, 14, 906, 16, "onPress"], [1119, 21, 906, 23], [1119, 23, 906, 25, "capturePhoto"], [1119, 35, 906, 38], [1120, 14, 907, 16, "disabled"], [1120, 22, 907, 24], [1120, 24, 907, 26, "processingState"], [1120, 39, 907, 41], [1120, 44, 907, 46], [1120, 50, 907, 52], [1120, 54, 907, 56], [1120, 55, 907, 57, "isCameraReady"], [1120, 68, 907, 71], [1121, 14, 908, 16, "style"], [1121, 19, 908, 21], [1121, 21, 908, 23], [1121, 22, 909, 18, "styles"], [1121, 28, 909, 24], [1121, 29, 909, 25, "shutterButton"], [1121, 42, 909, 38], [1121, 44, 910, 18, "processingState"], [1121, 59, 910, 33], [1121, 64, 910, 38], [1121, 70, 910, 44], [1121, 74, 910, 48, "styles"], [1121, 80, 910, 54], [1121, 81, 910, 55, "shutterButtonDisabled"], [1121, 102, 910, 76], [1121, 103, 911, 18], [1122, 14, 911, 18, "children"], [1122, 22, 911, 18], [1122, 24, 913, 17, "processingState"], [1122, 39, 913, 32], [1122, 44, 913, 37], [1122, 50, 913, 43], [1122, 66, 914, 18], [1122, 70, 914, 18, "_jsxDevRuntime"], [1122, 84, 914, 18], [1122, 85, 914, 18, "jsxDEV"], [1122, 91, 914, 18], [1122, 93, 914, 19, "_View"], [1122, 98, 914, 19], [1122, 99, 914, 19, "default"], [1122, 106, 914, 23], [1123, 16, 914, 24, "style"], [1123, 21, 914, 29], [1123, 23, 914, 31, "styles"], [1123, 29, 914, 37], [1123, 30, 914, 38, "shutterInner"], [1124, 14, 914, 51], [1125, 16, 914, 51, "fileName"], [1125, 24, 914, 51], [1125, 26, 914, 51, "_jsxFileName"], [1125, 38, 914, 51], [1126, 16, 914, 51, "lineNumber"], [1126, 26, 914, 51], [1127, 16, 914, 51, "columnNumber"], [1127, 28, 914, 51], [1128, 14, 914, 51], [1128, 21, 914, 53], [1128, 22, 914, 54], [1128, 38, 916, 18], [1128, 42, 916, 18, "_jsxDevRuntime"], [1128, 56, 916, 18], [1128, 57, 916, 18, "jsxDEV"], [1128, 63, 916, 18], [1128, 65, 916, 19, "_ActivityIndicator"], [1128, 83, 916, 19], [1128, 84, 916, 19, "default"], [1128, 91, 916, 36], [1129, 16, 916, 37, "size"], [1129, 20, 916, 41], [1129, 22, 916, 42], [1129, 29, 916, 49], [1130, 16, 916, 50, "color"], [1130, 21, 916, 55], [1130, 23, 916, 56], [1131, 14, 916, 65], [1132, 16, 916, 65, "fileName"], [1132, 24, 916, 65], [1132, 26, 916, 65, "_jsxFileName"], [1132, 38, 916, 65], [1133, 16, 916, 65, "lineNumber"], [1133, 26, 916, 65], [1134, 16, 916, 65, "columnNumber"], [1134, 28, 916, 65], [1135, 14, 916, 65], [1135, 21, 916, 67], [1136, 12, 917, 17], [1137, 14, 917, 17, "fileName"], [1137, 22, 917, 17], [1137, 24, 917, 17, "_jsxFileName"], [1137, 36, 917, 17], [1138, 14, 917, 17, "lineNumber"], [1138, 24, 917, 17], [1139, 14, 917, 17, "columnNumber"], [1139, 26, 917, 17], [1140, 12, 917, 17], [1140, 19, 918, 32], [1140, 20, 918, 33], [1140, 35, 919, 14], [1140, 39, 919, 14, "_jsxDevRuntime"], [1140, 53, 919, 14], [1140, 54, 919, 14, "jsxDEV"], [1140, 60, 919, 14], [1140, 62, 919, 15, "_Text"], [1140, 67, 919, 15], [1140, 68, 919, 15, "default"], [1140, 75, 919, 19], [1141, 14, 919, 20, "style"], [1141, 19, 919, 25], [1141, 21, 919, 27, "styles"], [1141, 27, 919, 33], [1141, 28, 919, 34, "privacyNote"], [1141, 39, 919, 46], [1142, 14, 919, 46, "children"], [1142, 22, 919, 46], [1142, 24, 919, 47], [1143, 12, 921, 14], [1144, 14, 921, 14, "fileName"], [1144, 22, 921, 14], [1144, 24, 921, 14, "_jsxFileName"], [1144, 36, 921, 14], [1145, 14, 921, 14, "lineNumber"], [1145, 24, 921, 14], [1146, 14, 921, 14, "columnNumber"], [1146, 26, 921, 14], [1147, 12, 921, 14], [1147, 19, 921, 20], [1147, 20, 921, 21], [1148, 10, 921, 21], [1149, 12, 921, 21, "fileName"], [1149, 20, 921, 21], [1149, 22, 921, 21, "_jsxFileName"], [1149, 34, 921, 21], [1150, 12, 921, 21, "lineNumber"], [1150, 22, 921, 21], [1151, 12, 921, 21, "columnNumber"], [1151, 24, 921, 21], [1152, 10, 921, 21], [1152, 17, 922, 18], [1152, 18, 922, 19], [1153, 8, 922, 19], [1153, 23, 923, 12], [1153, 24, 924, 9], [1154, 6, 924, 9], [1155, 8, 924, 9, "fileName"], [1155, 16, 924, 9], [1155, 18, 924, 9, "_jsxFileName"], [1155, 30, 924, 9], [1156, 8, 924, 9, "lineNumber"], [1156, 18, 924, 9], [1157, 8, 924, 9, "columnNumber"], [1157, 20, 924, 9], [1158, 6, 924, 9], [1158, 13, 925, 12], [1158, 14, 925, 13], [1158, 29, 927, 6], [1158, 33, 927, 6, "_jsxDevRuntime"], [1158, 47, 927, 6], [1158, 48, 927, 6, "jsxDEV"], [1158, 54, 927, 6], [1158, 56, 927, 7, "_Modal"], [1158, 62, 927, 7], [1158, 63, 927, 7, "default"], [1158, 70, 927, 12], [1159, 8, 928, 8, "visible"], [1159, 15, 928, 15], [1159, 17, 928, 17, "processingState"], [1159, 32, 928, 32], [1159, 37, 928, 37], [1159, 43, 928, 43], [1159, 47, 928, 47, "processingState"], [1159, 62, 928, 62], [1159, 67, 928, 67], [1159, 74, 928, 75], [1160, 8, 929, 8, "transparent"], [1160, 19, 929, 19], [1161, 8, 930, 8, "animationType"], [1161, 21, 930, 21], [1161, 23, 930, 22], [1161, 29, 930, 28], [1162, 8, 930, 28, "children"], [1162, 16, 930, 28], [1162, 31, 932, 8], [1162, 35, 932, 8, "_jsxDevRuntime"], [1162, 49, 932, 8], [1162, 50, 932, 8, "jsxDEV"], [1162, 56, 932, 8], [1162, 58, 932, 9, "_View"], [1162, 63, 932, 9], [1162, 64, 932, 9, "default"], [1162, 71, 932, 13], [1163, 10, 932, 14, "style"], [1163, 15, 932, 19], [1163, 17, 932, 21, "styles"], [1163, 23, 932, 27], [1163, 24, 932, 28, "processingModal"], [1163, 39, 932, 44], [1164, 10, 932, 44, "children"], [1164, 18, 932, 44], [1164, 33, 933, 10], [1164, 37, 933, 10, "_jsxDevRuntime"], [1164, 51, 933, 10], [1164, 52, 933, 10, "jsxDEV"], [1164, 58, 933, 10], [1164, 60, 933, 11, "_View"], [1164, 65, 933, 11], [1164, 66, 933, 11, "default"], [1164, 73, 933, 15], [1165, 12, 933, 16, "style"], [1165, 17, 933, 21], [1165, 19, 933, 23, "styles"], [1165, 25, 933, 29], [1165, 26, 933, 30, "processingContent"], [1165, 43, 933, 48], [1166, 12, 933, 48, "children"], [1166, 20, 933, 48], [1166, 36, 934, 12], [1166, 40, 934, 12, "_jsxDevRuntime"], [1166, 54, 934, 12], [1166, 55, 934, 12, "jsxDEV"], [1166, 61, 934, 12], [1166, 63, 934, 13, "_ActivityIndicator"], [1166, 81, 934, 13], [1166, 82, 934, 13, "default"], [1166, 89, 934, 30], [1167, 14, 934, 31, "size"], [1167, 18, 934, 35], [1167, 20, 934, 36], [1167, 27, 934, 43], [1168, 14, 934, 44, "color"], [1168, 19, 934, 49], [1168, 21, 934, 50], [1169, 12, 934, 59], [1170, 14, 934, 59, "fileName"], [1170, 22, 934, 59], [1170, 24, 934, 59, "_jsxFileName"], [1170, 36, 934, 59], [1171, 14, 934, 59, "lineNumber"], [1171, 24, 934, 59], [1172, 14, 934, 59, "columnNumber"], [1172, 26, 934, 59], [1173, 12, 934, 59], [1173, 19, 934, 61], [1173, 20, 934, 62], [1173, 35, 936, 12], [1173, 39, 936, 12, "_jsxDevRuntime"], [1173, 53, 936, 12], [1173, 54, 936, 12, "jsxDEV"], [1173, 60, 936, 12], [1173, 62, 936, 13, "_Text"], [1173, 67, 936, 13], [1173, 68, 936, 13, "default"], [1173, 75, 936, 17], [1174, 14, 936, 18, "style"], [1174, 19, 936, 23], [1174, 21, 936, 25, "styles"], [1174, 27, 936, 31], [1174, 28, 936, 32, "processingTitle"], [1174, 43, 936, 48], [1175, 14, 936, 48, "children"], [1175, 22, 936, 48], [1175, 25, 937, 15, "processingState"], [1175, 40, 937, 30], [1175, 45, 937, 35], [1175, 56, 937, 46], [1175, 60, 937, 50], [1175, 80, 937, 70], [1175, 82, 938, 15, "processingState"], [1175, 97, 938, 30], [1175, 102, 938, 35], [1175, 113, 938, 46], [1175, 117, 938, 50], [1175, 146, 938, 79], [1175, 148, 939, 15, "processingState"], [1175, 163, 939, 30], [1175, 168, 939, 35], [1175, 180, 939, 47], [1175, 184, 939, 51], [1175, 216, 939, 83], [1175, 218, 940, 15, "processingState"], [1175, 233, 940, 30], [1175, 238, 940, 35], [1175, 249, 940, 46], [1175, 253, 940, 50], [1175, 275, 940, 72], [1176, 12, 940, 72], [1177, 14, 940, 72, "fileName"], [1177, 22, 940, 72], [1177, 24, 940, 72, "_jsxFileName"], [1177, 36, 940, 72], [1178, 14, 940, 72, "lineNumber"], [1178, 24, 940, 72], [1179, 14, 940, 72, "columnNumber"], [1179, 26, 940, 72], [1180, 12, 940, 72], [1180, 19, 941, 18], [1180, 20, 941, 19], [1180, 35, 942, 12], [1180, 39, 942, 12, "_jsxDevRuntime"], [1180, 53, 942, 12], [1180, 54, 942, 12, "jsxDEV"], [1180, 60, 942, 12], [1180, 62, 942, 13, "_View"], [1180, 67, 942, 13], [1180, 68, 942, 13, "default"], [1180, 75, 942, 17], [1181, 14, 942, 18, "style"], [1181, 19, 942, 23], [1181, 21, 942, 25, "styles"], [1181, 27, 942, 31], [1181, 28, 942, 32, "progressBar"], [1181, 39, 942, 44], [1182, 14, 942, 44, "children"], [1182, 22, 942, 44], [1182, 37, 943, 14], [1182, 41, 943, 14, "_jsxDevRuntime"], [1182, 55, 943, 14], [1182, 56, 943, 14, "jsxDEV"], [1182, 62, 943, 14], [1182, 64, 943, 15, "_View"], [1182, 69, 943, 15], [1182, 70, 943, 15, "default"], [1182, 77, 943, 19], [1183, 16, 944, 16, "style"], [1183, 21, 944, 21], [1183, 23, 944, 23], [1183, 24, 945, 18, "styles"], [1183, 30, 945, 24], [1183, 31, 945, 25, "progressFill"], [1183, 43, 945, 37], [1183, 45, 946, 18], [1184, 18, 946, 20, "width"], [1184, 23, 946, 25], [1184, 25, 946, 27], [1184, 28, 946, 30, "processingProgress"], [1184, 46, 946, 48], [1185, 16, 946, 52], [1185, 17, 946, 53], [1186, 14, 947, 18], [1187, 16, 947, 18, "fileName"], [1187, 24, 947, 18], [1187, 26, 947, 18, "_jsxFileName"], [1187, 38, 947, 18], [1188, 16, 947, 18, "lineNumber"], [1188, 26, 947, 18], [1189, 16, 947, 18, "columnNumber"], [1189, 28, 947, 18], [1190, 14, 947, 18], [1190, 21, 948, 15], [1191, 12, 948, 16], [1192, 14, 948, 16, "fileName"], [1192, 22, 948, 16], [1192, 24, 948, 16, "_jsxFileName"], [1192, 36, 948, 16], [1193, 14, 948, 16, "lineNumber"], [1193, 24, 948, 16], [1194, 14, 948, 16, "columnNumber"], [1194, 26, 948, 16], [1195, 12, 948, 16], [1195, 19, 949, 18], [1195, 20, 949, 19], [1195, 35, 950, 12], [1195, 39, 950, 12, "_jsxDevRuntime"], [1195, 53, 950, 12], [1195, 54, 950, 12, "jsxDEV"], [1195, 60, 950, 12], [1195, 62, 950, 13, "_Text"], [1195, 67, 950, 13], [1195, 68, 950, 13, "default"], [1195, 75, 950, 17], [1196, 14, 950, 18, "style"], [1196, 19, 950, 23], [1196, 21, 950, 25, "styles"], [1196, 27, 950, 31], [1196, 28, 950, 32, "processingDescription"], [1196, 49, 950, 54], [1197, 14, 950, 54, "children"], [1197, 22, 950, 54], [1197, 25, 951, 15, "processingState"], [1197, 40, 951, 30], [1197, 45, 951, 35], [1197, 56, 951, 46], [1197, 60, 951, 50], [1197, 89, 951, 79], [1197, 91, 952, 15, "processingState"], [1197, 106, 952, 30], [1197, 111, 952, 35], [1197, 122, 952, 46], [1197, 126, 952, 50], [1197, 164, 952, 88], [1197, 166, 953, 15, "processingState"], [1197, 181, 953, 30], [1197, 186, 953, 35], [1197, 198, 953, 47], [1197, 202, 953, 51], [1197, 247, 953, 96], [1197, 249, 954, 15, "processingState"], [1197, 264, 954, 30], [1197, 269, 954, 35], [1197, 280, 954, 46], [1197, 284, 954, 50], [1197, 325, 954, 91], [1198, 12, 954, 91], [1199, 14, 954, 91, "fileName"], [1199, 22, 954, 91], [1199, 24, 954, 91, "_jsxFileName"], [1199, 36, 954, 91], [1200, 14, 954, 91, "lineNumber"], [1200, 24, 954, 91], [1201, 14, 954, 91, "columnNumber"], [1201, 26, 954, 91], [1202, 12, 954, 91], [1202, 19, 955, 18], [1202, 20, 955, 19], [1202, 22, 956, 13, "processingState"], [1202, 37, 956, 28], [1202, 42, 956, 33], [1202, 53, 956, 44], [1202, 70, 957, 14], [1202, 74, 957, 14, "_jsxDevRuntime"], [1202, 88, 957, 14], [1202, 89, 957, 14, "jsxDEV"], [1202, 95, 957, 14], [1202, 97, 957, 15, "_lucideReactNative"], [1202, 115, 957, 15], [1202, 116, 957, 15, "CheckCircle"], [1202, 127, 957, 26], [1203, 14, 957, 27, "size"], [1203, 18, 957, 31], [1203, 20, 957, 33], [1203, 22, 957, 36], [1204, 14, 957, 37, "color"], [1204, 19, 957, 42], [1204, 21, 957, 43], [1204, 30, 957, 52], [1205, 14, 957, 53, "style"], [1205, 19, 957, 58], [1205, 21, 957, 60, "styles"], [1205, 27, 957, 66], [1205, 28, 957, 67, "successIcon"], [1206, 12, 957, 79], [1207, 14, 957, 79, "fileName"], [1207, 22, 957, 79], [1207, 24, 957, 79, "_jsxFileName"], [1207, 36, 957, 79], [1208, 14, 957, 79, "lineNumber"], [1208, 24, 957, 79], [1209, 14, 957, 79, "columnNumber"], [1209, 26, 957, 79], [1210, 12, 957, 79], [1210, 19, 957, 81], [1210, 20, 958, 13], [1211, 10, 958, 13], [1212, 12, 958, 13, "fileName"], [1212, 20, 958, 13], [1212, 22, 958, 13, "_jsxFileName"], [1212, 34, 958, 13], [1213, 12, 958, 13, "lineNumber"], [1213, 22, 958, 13], [1214, 12, 958, 13, "columnNumber"], [1214, 24, 958, 13], [1215, 10, 958, 13], [1215, 17, 959, 16], [1216, 8, 959, 17], [1217, 10, 959, 17, "fileName"], [1217, 18, 959, 17], [1217, 20, 959, 17, "_jsxFileName"], [1217, 32, 959, 17], [1218, 10, 959, 17, "lineNumber"], [1218, 20, 959, 17], [1219, 10, 959, 17, "columnNumber"], [1219, 22, 959, 17], [1220, 8, 959, 17], [1220, 15, 960, 14], [1221, 6, 960, 15], [1222, 8, 960, 15, "fileName"], [1222, 16, 960, 15], [1222, 18, 960, 15, "_jsxFileName"], [1222, 30, 960, 15], [1223, 8, 960, 15, "lineNumber"], [1223, 18, 960, 15], [1224, 8, 960, 15, "columnNumber"], [1224, 20, 960, 15], [1225, 6, 960, 15], [1225, 13, 961, 13], [1225, 14, 961, 14], [1225, 29, 963, 6], [1225, 33, 963, 6, "_jsxDevRuntime"], [1225, 47, 963, 6], [1225, 48, 963, 6, "jsxDEV"], [1225, 54, 963, 6], [1225, 56, 963, 7, "_Modal"], [1225, 62, 963, 7], [1225, 63, 963, 7, "default"], [1225, 70, 963, 12], [1226, 8, 964, 8, "visible"], [1226, 15, 964, 15], [1226, 17, 964, 17, "processingState"], [1226, 32, 964, 32], [1226, 37, 964, 37], [1226, 44, 964, 45], [1227, 8, 965, 8, "transparent"], [1227, 19, 965, 19], [1228, 8, 966, 8, "animationType"], [1228, 21, 966, 21], [1228, 23, 966, 22], [1228, 29, 966, 28], [1229, 8, 966, 28, "children"], [1229, 16, 966, 28], [1229, 31, 968, 8], [1229, 35, 968, 8, "_jsxDevRuntime"], [1229, 49, 968, 8], [1229, 50, 968, 8, "jsxDEV"], [1229, 56, 968, 8], [1229, 58, 968, 9, "_View"], [1229, 63, 968, 9], [1229, 64, 968, 9, "default"], [1229, 71, 968, 13], [1230, 10, 968, 14, "style"], [1230, 15, 968, 19], [1230, 17, 968, 21, "styles"], [1230, 23, 968, 27], [1230, 24, 968, 28, "processingModal"], [1230, 39, 968, 44], [1231, 10, 968, 44, "children"], [1231, 18, 968, 44], [1231, 33, 969, 10], [1231, 37, 969, 10, "_jsxDevRuntime"], [1231, 51, 969, 10], [1231, 52, 969, 10, "jsxDEV"], [1231, 58, 969, 10], [1231, 60, 969, 11, "_View"], [1231, 65, 969, 11], [1231, 66, 969, 11, "default"], [1231, 73, 969, 15], [1232, 12, 969, 16, "style"], [1232, 17, 969, 21], [1232, 19, 969, 23, "styles"], [1232, 25, 969, 29], [1232, 26, 969, 30, "errorContent"], [1232, 38, 969, 43], [1233, 12, 969, 43, "children"], [1233, 20, 969, 43], [1233, 36, 970, 12], [1233, 40, 970, 12, "_jsxDevRuntime"], [1233, 54, 970, 12], [1233, 55, 970, 12, "jsxDEV"], [1233, 61, 970, 12], [1233, 63, 970, 13, "_lucideReactNative"], [1233, 81, 970, 13], [1233, 82, 970, 13, "X"], [1233, 83, 970, 14], [1234, 14, 970, 15, "size"], [1234, 18, 970, 19], [1234, 20, 970, 21], [1234, 22, 970, 24], [1235, 14, 970, 25, "color"], [1235, 19, 970, 30], [1235, 21, 970, 31], [1236, 12, 970, 40], [1237, 14, 970, 40, "fileName"], [1237, 22, 970, 40], [1237, 24, 970, 40, "_jsxFileName"], [1237, 36, 970, 40], [1238, 14, 970, 40, "lineNumber"], [1238, 24, 970, 40], [1239, 14, 970, 40, "columnNumber"], [1239, 26, 970, 40], [1240, 12, 970, 40], [1240, 19, 970, 42], [1240, 20, 970, 43], [1240, 35, 971, 12], [1240, 39, 971, 12, "_jsxDevRuntime"], [1240, 53, 971, 12], [1240, 54, 971, 12, "jsxDEV"], [1240, 60, 971, 12], [1240, 62, 971, 13, "_Text"], [1240, 67, 971, 13], [1240, 68, 971, 13, "default"], [1240, 75, 971, 17], [1241, 14, 971, 18, "style"], [1241, 19, 971, 23], [1241, 21, 971, 25, "styles"], [1241, 27, 971, 31], [1241, 28, 971, 32, "errorTitle"], [1241, 38, 971, 43], [1242, 14, 971, 43, "children"], [1242, 22, 971, 43], [1242, 24, 971, 44], [1243, 12, 971, 61], [1244, 14, 971, 61, "fileName"], [1244, 22, 971, 61], [1244, 24, 971, 61, "_jsxFileName"], [1244, 36, 971, 61], [1245, 14, 971, 61, "lineNumber"], [1245, 24, 971, 61], [1246, 14, 971, 61, "columnNumber"], [1246, 26, 971, 61], [1247, 12, 971, 61], [1247, 19, 971, 67], [1247, 20, 971, 68], [1247, 35, 972, 12], [1247, 39, 972, 12, "_jsxDevRuntime"], [1247, 53, 972, 12], [1247, 54, 972, 12, "jsxDEV"], [1247, 60, 972, 12], [1247, 62, 972, 13, "_Text"], [1247, 67, 972, 13], [1247, 68, 972, 13, "default"], [1247, 75, 972, 17], [1248, 14, 972, 18, "style"], [1248, 19, 972, 23], [1248, 21, 972, 25, "styles"], [1248, 27, 972, 31], [1248, 28, 972, 32, "errorMessage"], [1248, 40, 972, 45], [1249, 14, 972, 45, "children"], [1249, 22, 972, 45], [1249, 24, 972, 47, "errorMessage"], [1250, 12, 972, 59], [1251, 14, 972, 59, "fileName"], [1251, 22, 972, 59], [1251, 24, 972, 59, "_jsxFileName"], [1251, 36, 972, 59], [1252, 14, 972, 59, "lineNumber"], [1252, 24, 972, 59], [1253, 14, 972, 59, "columnNumber"], [1253, 26, 972, 59], [1254, 12, 972, 59], [1254, 19, 972, 66], [1254, 20, 972, 67], [1254, 35, 973, 12], [1254, 39, 973, 12, "_jsxDevRuntime"], [1254, 53, 973, 12], [1254, 54, 973, 12, "jsxDEV"], [1254, 60, 973, 12], [1254, 62, 973, 13, "_TouchableOpacity"], [1254, 79, 973, 13], [1254, 80, 973, 13, "default"], [1254, 87, 973, 29], [1255, 14, 974, 14, "onPress"], [1255, 21, 974, 21], [1255, 23, 974, 23, "retryCapture"], [1255, 35, 974, 36], [1256, 14, 975, 14, "style"], [1256, 19, 975, 19], [1256, 21, 975, 21, "styles"], [1256, 27, 975, 27], [1256, 28, 975, 28, "primaryButton"], [1256, 41, 975, 42], [1257, 14, 975, 42, "children"], [1257, 22, 975, 42], [1257, 37, 977, 14], [1257, 41, 977, 14, "_jsxDevRuntime"], [1257, 55, 977, 14], [1257, 56, 977, 14, "jsxDEV"], [1257, 62, 977, 14], [1257, 64, 977, 15, "_Text"], [1257, 69, 977, 15], [1257, 70, 977, 15, "default"], [1257, 77, 977, 19], [1258, 16, 977, 20, "style"], [1258, 21, 977, 25], [1258, 23, 977, 27, "styles"], [1258, 29, 977, 33], [1258, 30, 977, 34, "primaryButtonText"], [1258, 47, 977, 52], [1259, 16, 977, 52, "children"], [1259, 24, 977, 52], [1259, 26, 977, 53], [1260, 14, 977, 62], [1261, 16, 977, 62, "fileName"], [1261, 24, 977, 62], [1261, 26, 977, 62, "_jsxFileName"], [1261, 38, 977, 62], [1262, 16, 977, 62, "lineNumber"], [1262, 26, 977, 62], [1263, 16, 977, 62, "columnNumber"], [1263, 28, 977, 62], [1264, 14, 977, 62], [1264, 21, 977, 68], [1265, 12, 977, 69], [1266, 14, 977, 69, "fileName"], [1266, 22, 977, 69], [1266, 24, 977, 69, "_jsxFileName"], [1266, 36, 977, 69], [1267, 14, 977, 69, "lineNumber"], [1267, 24, 977, 69], [1268, 14, 977, 69, "columnNumber"], [1268, 26, 977, 69], [1269, 12, 977, 69], [1269, 19, 978, 30], [1269, 20, 978, 31], [1269, 35, 979, 12], [1269, 39, 979, 12, "_jsxDevRuntime"], [1269, 53, 979, 12], [1269, 54, 979, 12, "jsxDEV"], [1269, 60, 979, 12], [1269, 62, 979, 13, "_TouchableOpacity"], [1269, 79, 979, 13], [1269, 80, 979, 13, "default"], [1269, 87, 979, 29], [1270, 14, 980, 14, "onPress"], [1270, 21, 980, 21], [1270, 23, 980, 23, "onCancel"], [1270, 31, 980, 32], [1271, 14, 981, 14, "style"], [1271, 19, 981, 19], [1271, 21, 981, 21, "styles"], [1271, 27, 981, 27], [1271, 28, 981, 28, "secondaryButton"], [1271, 43, 981, 44], [1272, 14, 981, 44, "children"], [1272, 22, 981, 44], [1272, 37, 983, 14], [1272, 41, 983, 14, "_jsxDevRuntime"], [1272, 55, 983, 14], [1272, 56, 983, 14, "jsxDEV"], [1272, 62, 983, 14], [1272, 64, 983, 15, "_Text"], [1272, 69, 983, 15], [1272, 70, 983, 15, "default"], [1272, 77, 983, 19], [1273, 16, 983, 20, "style"], [1273, 21, 983, 25], [1273, 23, 983, 27, "styles"], [1273, 29, 983, 33], [1273, 30, 983, 34, "secondaryButtonText"], [1273, 49, 983, 54], [1274, 16, 983, 54, "children"], [1274, 24, 983, 54], [1274, 26, 983, 55], [1275, 14, 983, 61], [1276, 16, 983, 61, "fileName"], [1276, 24, 983, 61], [1276, 26, 983, 61, "_jsxFileName"], [1276, 38, 983, 61], [1277, 16, 983, 61, "lineNumber"], [1277, 26, 983, 61], [1278, 16, 983, 61, "columnNumber"], [1278, 28, 983, 61], [1279, 14, 983, 61], [1279, 21, 983, 67], [1280, 12, 983, 68], [1281, 14, 983, 68, "fileName"], [1281, 22, 983, 68], [1281, 24, 983, 68, "_jsxFileName"], [1281, 36, 983, 68], [1282, 14, 983, 68, "lineNumber"], [1282, 24, 983, 68], [1283, 14, 983, 68, "columnNumber"], [1283, 26, 983, 68], [1284, 12, 983, 68], [1284, 19, 984, 30], [1284, 20, 984, 31], [1285, 10, 984, 31], [1286, 12, 984, 31, "fileName"], [1286, 20, 984, 31], [1286, 22, 984, 31, "_jsxFileName"], [1286, 34, 984, 31], [1287, 12, 984, 31, "lineNumber"], [1287, 22, 984, 31], [1288, 12, 984, 31, "columnNumber"], [1288, 24, 984, 31], [1289, 10, 984, 31], [1289, 17, 985, 16], [1290, 8, 985, 17], [1291, 10, 985, 17, "fileName"], [1291, 18, 985, 17], [1291, 20, 985, 17, "_jsxFileName"], [1291, 32, 985, 17], [1292, 10, 985, 17, "lineNumber"], [1292, 20, 985, 17], [1293, 10, 985, 17, "columnNumber"], [1293, 22, 985, 17], [1294, 8, 985, 17], [1294, 15, 986, 14], [1295, 6, 986, 15], [1296, 8, 986, 15, "fileName"], [1296, 16, 986, 15], [1296, 18, 986, 15, "_jsxFileName"], [1296, 30, 986, 15], [1297, 8, 986, 15, "lineNumber"], [1297, 18, 986, 15], [1298, 8, 986, 15, "columnNumber"], [1298, 20, 986, 15], [1299, 6, 986, 15], [1299, 13, 987, 13], [1299, 14, 987, 14], [1300, 4, 987, 14], [1301, 6, 987, 14, "fileName"], [1301, 14, 987, 14], [1301, 16, 987, 14, "_jsxFileName"], [1301, 28, 987, 14], [1302, 6, 987, 14, "lineNumber"], [1302, 16, 987, 14], [1303, 6, 987, 14, "columnNumber"], [1303, 18, 987, 14], [1304, 4, 987, 14], [1304, 11, 988, 10], [1304, 12, 988, 11], [1305, 2, 990, 0], [1306, 2, 990, 1, "_s"], [1306, 4, 990, 1], [1306, 5, 51, 24, "EchoCameraWeb"], [1306, 18, 51, 37], [1307, 4, 51, 37], [1307, 12, 58, 42, "useCameraPermissions"], [1307, 44, 58, 62], [1307, 46, 72, 19, "useUpload"], [1307, 64, 72, 28], [1308, 2, 72, 28], [1309, 2, 72, 28, "_c"], [1309, 4, 72, 28], [1309, 7, 51, 24, "EchoCameraWeb"], [1309, 20, 51, 37], [1310, 2, 991, 0], [1310, 8, 991, 6, "styles"], [1310, 14, 991, 12], [1310, 17, 991, 15, "StyleSheet"], [1310, 36, 991, 25], [1310, 37, 991, 26, "create"], [1310, 43, 991, 32], [1310, 44, 991, 33], [1311, 4, 992, 2, "container"], [1311, 13, 992, 11], [1311, 15, 992, 13], [1312, 6, 993, 4, "flex"], [1312, 10, 993, 8], [1312, 12, 993, 10], [1312, 13, 993, 11], [1313, 6, 994, 4, "backgroundColor"], [1313, 21, 994, 19], [1313, 23, 994, 21], [1314, 4, 995, 2], [1314, 5, 995, 3], [1315, 4, 996, 2, "cameraContainer"], [1315, 19, 996, 17], [1315, 21, 996, 19], [1316, 6, 997, 4, "flex"], [1316, 10, 997, 8], [1316, 12, 997, 10], [1316, 13, 997, 11], [1317, 6, 998, 4, "max<PERSON><PERSON><PERSON>"], [1317, 14, 998, 12], [1317, 16, 998, 14], [1317, 19, 998, 17], [1318, 6, 999, 4, "alignSelf"], [1318, 15, 999, 13], [1318, 17, 999, 15], [1318, 25, 999, 23], [1319, 6, 1000, 4, "width"], [1319, 11, 1000, 9], [1319, 13, 1000, 11], [1320, 4, 1001, 2], [1320, 5, 1001, 3], [1321, 4, 1002, 2, "camera"], [1321, 10, 1002, 8], [1321, 12, 1002, 10], [1322, 6, 1003, 4, "flex"], [1322, 10, 1003, 8], [1322, 12, 1003, 10], [1323, 4, 1004, 2], [1323, 5, 1004, 3], [1324, 4, 1005, 2, "headerOverlay"], [1324, 17, 1005, 15], [1324, 19, 1005, 17], [1325, 6, 1006, 4, "position"], [1325, 14, 1006, 12], [1325, 16, 1006, 14], [1325, 26, 1006, 24], [1326, 6, 1007, 4, "top"], [1326, 9, 1007, 7], [1326, 11, 1007, 9], [1326, 12, 1007, 10], [1327, 6, 1008, 4, "left"], [1327, 10, 1008, 8], [1327, 12, 1008, 10], [1327, 13, 1008, 11], [1328, 6, 1009, 4, "right"], [1328, 11, 1009, 9], [1328, 13, 1009, 11], [1328, 14, 1009, 12], [1329, 6, 1010, 4, "backgroundColor"], [1329, 21, 1010, 19], [1329, 23, 1010, 21], [1329, 36, 1010, 34], [1330, 6, 1011, 4, "paddingTop"], [1330, 16, 1011, 14], [1330, 18, 1011, 16], [1330, 20, 1011, 18], [1331, 6, 1012, 4, "paddingHorizontal"], [1331, 23, 1012, 21], [1331, 25, 1012, 23], [1331, 27, 1012, 25], [1332, 6, 1013, 4, "paddingBottom"], [1332, 19, 1013, 17], [1332, 21, 1013, 19], [1333, 4, 1014, 2], [1333, 5, 1014, 3], [1334, 4, 1015, 2, "headerContent"], [1334, 17, 1015, 15], [1334, 19, 1015, 17], [1335, 6, 1016, 4, "flexDirection"], [1335, 19, 1016, 17], [1335, 21, 1016, 19], [1335, 26, 1016, 24], [1336, 6, 1017, 4, "justifyContent"], [1336, 20, 1017, 18], [1336, 22, 1017, 20], [1336, 37, 1017, 35], [1337, 6, 1018, 4, "alignItems"], [1337, 16, 1018, 14], [1337, 18, 1018, 16], [1338, 4, 1019, 2], [1338, 5, 1019, 3], [1339, 4, 1020, 2, "headerLeft"], [1339, 14, 1020, 12], [1339, 16, 1020, 14], [1340, 6, 1021, 4, "flex"], [1340, 10, 1021, 8], [1340, 12, 1021, 10], [1341, 4, 1022, 2], [1341, 5, 1022, 3], [1342, 4, 1023, 2, "headerTitle"], [1342, 15, 1023, 13], [1342, 17, 1023, 15], [1343, 6, 1024, 4, "fontSize"], [1343, 14, 1024, 12], [1343, 16, 1024, 14], [1343, 18, 1024, 16], [1344, 6, 1025, 4, "fontWeight"], [1344, 16, 1025, 14], [1344, 18, 1025, 16], [1344, 23, 1025, 21], [1345, 6, 1026, 4, "color"], [1345, 11, 1026, 9], [1345, 13, 1026, 11], [1345, 19, 1026, 17], [1346, 6, 1027, 4, "marginBottom"], [1346, 18, 1027, 16], [1346, 20, 1027, 18], [1347, 4, 1028, 2], [1347, 5, 1028, 3], [1348, 4, 1029, 2, "subtitleRow"], [1348, 15, 1029, 13], [1348, 17, 1029, 15], [1349, 6, 1030, 4, "flexDirection"], [1349, 19, 1030, 17], [1349, 21, 1030, 19], [1349, 26, 1030, 24], [1350, 6, 1031, 4, "alignItems"], [1350, 16, 1031, 14], [1350, 18, 1031, 16], [1350, 26, 1031, 24], [1351, 6, 1032, 4, "marginBottom"], [1351, 18, 1032, 16], [1351, 20, 1032, 18], [1352, 4, 1033, 2], [1352, 5, 1033, 3], [1353, 4, 1034, 2, "webIcon"], [1353, 11, 1034, 9], [1353, 13, 1034, 11], [1354, 6, 1035, 4, "fontSize"], [1354, 14, 1035, 12], [1354, 16, 1035, 14], [1354, 18, 1035, 16], [1355, 6, 1036, 4, "marginRight"], [1355, 17, 1036, 15], [1355, 19, 1036, 17], [1356, 4, 1037, 2], [1356, 5, 1037, 3], [1357, 4, 1038, 2, "headerSubtitle"], [1357, 18, 1038, 16], [1357, 20, 1038, 18], [1358, 6, 1039, 4, "fontSize"], [1358, 14, 1039, 12], [1358, 16, 1039, 14], [1358, 18, 1039, 16], [1359, 6, 1040, 4, "color"], [1359, 11, 1040, 9], [1359, 13, 1040, 11], [1359, 19, 1040, 17], [1360, 6, 1041, 4, "opacity"], [1360, 13, 1041, 11], [1360, 15, 1041, 13], [1361, 4, 1042, 2], [1361, 5, 1042, 3], [1362, 4, 1043, 2, "challengeRow"], [1362, 16, 1043, 14], [1362, 18, 1043, 16], [1363, 6, 1044, 4, "flexDirection"], [1363, 19, 1044, 17], [1363, 21, 1044, 19], [1363, 26, 1044, 24], [1364, 6, 1045, 4, "alignItems"], [1364, 16, 1045, 14], [1364, 18, 1045, 16], [1365, 4, 1046, 2], [1365, 5, 1046, 3], [1366, 4, 1047, 2, "challengeCode"], [1366, 17, 1047, 15], [1366, 19, 1047, 17], [1367, 6, 1048, 4, "fontSize"], [1367, 14, 1048, 12], [1367, 16, 1048, 14], [1367, 18, 1048, 16], [1368, 6, 1049, 4, "color"], [1368, 11, 1049, 9], [1368, 13, 1049, 11], [1368, 19, 1049, 17], [1369, 6, 1050, 4, "marginLeft"], [1369, 16, 1050, 14], [1369, 18, 1050, 16], [1369, 19, 1050, 17], [1370, 6, 1051, 4, "fontFamily"], [1370, 16, 1051, 14], [1370, 18, 1051, 16], [1371, 4, 1052, 2], [1371, 5, 1052, 3], [1372, 4, 1053, 2, "closeButton"], [1372, 15, 1053, 13], [1372, 17, 1053, 15], [1373, 6, 1054, 4, "padding"], [1373, 13, 1054, 11], [1373, 15, 1054, 13], [1374, 4, 1055, 2], [1374, 5, 1055, 3], [1375, 4, 1056, 2, "privacyNotice"], [1375, 17, 1056, 15], [1375, 19, 1056, 17], [1376, 6, 1057, 4, "position"], [1376, 14, 1057, 12], [1376, 16, 1057, 14], [1376, 26, 1057, 24], [1377, 6, 1058, 4, "top"], [1377, 9, 1058, 7], [1377, 11, 1058, 9], [1377, 14, 1058, 12], [1378, 6, 1059, 4, "left"], [1378, 10, 1059, 8], [1378, 12, 1059, 10], [1378, 14, 1059, 12], [1379, 6, 1060, 4, "right"], [1379, 11, 1060, 9], [1379, 13, 1060, 11], [1379, 15, 1060, 13], [1380, 6, 1061, 4, "backgroundColor"], [1380, 21, 1061, 19], [1380, 23, 1061, 21], [1380, 48, 1061, 46], [1381, 6, 1062, 4, "borderRadius"], [1381, 18, 1062, 16], [1381, 20, 1062, 18], [1381, 21, 1062, 19], [1382, 6, 1063, 4, "padding"], [1382, 13, 1063, 11], [1382, 15, 1063, 13], [1382, 17, 1063, 15], [1383, 6, 1064, 4, "flexDirection"], [1383, 19, 1064, 17], [1383, 21, 1064, 19], [1383, 26, 1064, 24], [1384, 6, 1065, 4, "alignItems"], [1384, 16, 1065, 14], [1384, 18, 1065, 16], [1385, 4, 1066, 2], [1385, 5, 1066, 3], [1386, 4, 1067, 2, "privacyText"], [1386, 15, 1067, 13], [1386, 17, 1067, 15], [1387, 6, 1068, 4, "color"], [1387, 11, 1068, 9], [1387, 13, 1068, 11], [1387, 19, 1068, 17], [1388, 6, 1069, 4, "fontSize"], [1388, 14, 1069, 12], [1388, 16, 1069, 14], [1388, 18, 1069, 16], [1389, 6, 1070, 4, "marginLeft"], [1389, 16, 1070, 14], [1389, 18, 1070, 16], [1389, 19, 1070, 17], [1390, 6, 1071, 4, "flex"], [1390, 10, 1071, 8], [1390, 12, 1071, 10], [1391, 4, 1072, 2], [1391, 5, 1072, 3], [1392, 4, 1073, 2, "footer<PERSON><PERSON><PERSON>"], [1392, 17, 1073, 15], [1392, 19, 1073, 17], [1393, 6, 1074, 4, "position"], [1393, 14, 1074, 12], [1393, 16, 1074, 14], [1393, 26, 1074, 24], [1394, 6, 1075, 4, "bottom"], [1394, 12, 1075, 10], [1394, 14, 1075, 12], [1394, 15, 1075, 13], [1395, 6, 1076, 4, "left"], [1395, 10, 1076, 8], [1395, 12, 1076, 10], [1395, 13, 1076, 11], [1396, 6, 1077, 4, "right"], [1396, 11, 1077, 9], [1396, 13, 1077, 11], [1396, 14, 1077, 12], [1397, 6, 1078, 4, "backgroundColor"], [1397, 21, 1078, 19], [1397, 23, 1078, 21], [1397, 36, 1078, 34], [1398, 6, 1079, 4, "paddingBottom"], [1398, 19, 1079, 17], [1398, 21, 1079, 19], [1398, 23, 1079, 21], [1399, 6, 1080, 4, "paddingTop"], [1399, 16, 1080, 14], [1399, 18, 1080, 16], [1399, 20, 1080, 18], [1400, 6, 1081, 4, "alignItems"], [1400, 16, 1081, 14], [1400, 18, 1081, 16], [1401, 4, 1082, 2], [1401, 5, 1082, 3], [1402, 4, 1083, 2, "instruction"], [1402, 15, 1083, 13], [1402, 17, 1083, 15], [1403, 6, 1084, 4, "fontSize"], [1403, 14, 1084, 12], [1403, 16, 1084, 14], [1403, 18, 1084, 16], [1404, 6, 1085, 4, "color"], [1404, 11, 1085, 9], [1404, 13, 1085, 11], [1404, 19, 1085, 17], [1405, 6, 1086, 4, "marginBottom"], [1405, 18, 1086, 16], [1405, 20, 1086, 18], [1406, 4, 1087, 2], [1406, 5, 1087, 3], [1407, 4, 1088, 2, "shutterButton"], [1407, 17, 1088, 15], [1407, 19, 1088, 17], [1408, 6, 1089, 4, "width"], [1408, 11, 1089, 9], [1408, 13, 1089, 11], [1408, 15, 1089, 13], [1409, 6, 1090, 4, "height"], [1409, 12, 1090, 10], [1409, 14, 1090, 12], [1409, 16, 1090, 14], [1410, 6, 1091, 4, "borderRadius"], [1410, 18, 1091, 16], [1410, 20, 1091, 18], [1410, 22, 1091, 20], [1411, 6, 1092, 4, "backgroundColor"], [1411, 21, 1092, 19], [1411, 23, 1092, 21], [1411, 29, 1092, 27], [1412, 6, 1093, 4, "justifyContent"], [1412, 20, 1093, 18], [1412, 22, 1093, 20], [1412, 30, 1093, 28], [1413, 6, 1094, 4, "alignItems"], [1413, 16, 1094, 14], [1413, 18, 1094, 16], [1413, 26, 1094, 24], [1414, 6, 1095, 4, "marginBottom"], [1414, 18, 1095, 16], [1414, 20, 1095, 18], [1414, 22, 1095, 20], [1415, 6, 1096, 4], [1415, 9, 1096, 7, "Platform"], [1415, 26, 1096, 15], [1415, 27, 1096, 16, "select"], [1415, 33, 1096, 22], [1415, 34, 1096, 23], [1416, 8, 1097, 6, "ios"], [1416, 11, 1097, 9], [1416, 13, 1097, 11], [1417, 10, 1098, 8, "shadowColor"], [1417, 21, 1098, 19], [1417, 23, 1098, 21], [1417, 32, 1098, 30], [1418, 10, 1099, 8, "shadowOffset"], [1418, 22, 1099, 20], [1418, 24, 1099, 22], [1419, 12, 1099, 24, "width"], [1419, 17, 1099, 29], [1419, 19, 1099, 31], [1419, 20, 1099, 32], [1420, 12, 1099, 34, "height"], [1420, 18, 1099, 40], [1420, 20, 1099, 42], [1421, 10, 1099, 44], [1421, 11, 1099, 45], [1422, 10, 1100, 8, "shadowOpacity"], [1422, 23, 1100, 21], [1422, 25, 1100, 23], [1422, 28, 1100, 26], [1423, 10, 1101, 8, "shadowRadius"], [1423, 22, 1101, 20], [1423, 24, 1101, 22], [1424, 8, 1102, 6], [1424, 9, 1102, 7], [1425, 8, 1103, 6, "android"], [1425, 15, 1103, 13], [1425, 17, 1103, 15], [1426, 10, 1104, 8, "elevation"], [1426, 19, 1104, 17], [1426, 21, 1104, 19], [1427, 8, 1105, 6], [1427, 9, 1105, 7], [1428, 8, 1106, 6, "web"], [1428, 11, 1106, 9], [1428, 13, 1106, 11], [1429, 10, 1107, 8, "boxShadow"], [1429, 19, 1107, 17], [1429, 21, 1107, 19], [1430, 8, 1108, 6], [1431, 6, 1109, 4], [1431, 7, 1109, 5], [1432, 4, 1110, 2], [1432, 5, 1110, 3], [1433, 4, 1111, 2, "shutterButtonDisabled"], [1433, 25, 1111, 23], [1433, 27, 1111, 25], [1434, 6, 1112, 4, "opacity"], [1434, 13, 1112, 11], [1434, 15, 1112, 13], [1435, 4, 1113, 2], [1435, 5, 1113, 3], [1436, 4, 1114, 2, "shutterInner"], [1436, 16, 1114, 14], [1436, 18, 1114, 16], [1437, 6, 1115, 4, "width"], [1437, 11, 1115, 9], [1437, 13, 1115, 11], [1437, 15, 1115, 13], [1438, 6, 1116, 4, "height"], [1438, 12, 1116, 10], [1438, 14, 1116, 12], [1438, 16, 1116, 14], [1439, 6, 1117, 4, "borderRadius"], [1439, 18, 1117, 16], [1439, 20, 1117, 18], [1439, 22, 1117, 20], [1440, 6, 1118, 4, "backgroundColor"], [1440, 21, 1118, 19], [1440, 23, 1118, 21], [1440, 29, 1118, 27], [1441, 6, 1119, 4, "borderWidth"], [1441, 17, 1119, 15], [1441, 19, 1119, 17], [1441, 20, 1119, 18], [1442, 6, 1120, 4, "borderColor"], [1442, 17, 1120, 15], [1442, 19, 1120, 17], [1443, 4, 1121, 2], [1443, 5, 1121, 3], [1444, 4, 1122, 2, "privacyNote"], [1444, 15, 1122, 13], [1444, 17, 1122, 15], [1445, 6, 1123, 4, "fontSize"], [1445, 14, 1123, 12], [1445, 16, 1123, 14], [1445, 18, 1123, 16], [1446, 6, 1124, 4, "color"], [1446, 11, 1124, 9], [1446, 13, 1124, 11], [1447, 4, 1125, 2], [1447, 5, 1125, 3], [1448, 4, 1126, 2, "processingModal"], [1448, 19, 1126, 17], [1448, 21, 1126, 19], [1449, 6, 1127, 4, "flex"], [1449, 10, 1127, 8], [1449, 12, 1127, 10], [1449, 13, 1127, 11], [1450, 6, 1128, 4, "backgroundColor"], [1450, 21, 1128, 19], [1450, 23, 1128, 21], [1450, 43, 1128, 41], [1451, 6, 1129, 4, "justifyContent"], [1451, 20, 1129, 18], [1451, 22, 1129, 20], [1451, 30, 1129, 28], [1452, 6, 1130, 4, "alignItems"], [1452, 16, 1130, 14], [1452, 18, 1130, 16], [1453, 4, 1131, 2], [1453, 5, 1131, 3], [1454, 4, 1132, 2, "processingContent"], [1454, 21, 1132, 19], [1454, 23, 1132, 21], [1455, 6, 1133, 4, "backgroundColor"], [1455, 21, 1133, 19], [1455, 23, 1133, 21], [1455, 29, 1133, 27], [1456, 6, 1134, 4, "borderRadius"], [1456, 18, 1134, 16], [1456, 20, 1134, 18], [1456, 22, 1134, 20], [1457, 6, 1135, 4, "padding"], [1457, 13, 1135, 11], [1457, 15, 1135, 13], [1457, 17, 1135, 15], [1458, 6, 1136, 4, "width"], [1458, 11, 1136, 9], [1458, 13, 1136, 11], [1458, 18, 1136, 16], [1459, 6, 1137, 4, "max<PERSON><PERSON><PERSON>"], [1459, 14, 1137, 12], [1459, 16, 1137, 14], [1459, 19, 1137, 17], [1460, 6, 1138, 4, "alignItems"], [1460, 16, 1138, 14], [1460, 18, 1138, 16], [1461, 4, 1139, 2], [1461, 5, 1139, 3], [1462, 4, 1140, 2, "processingTitle"], [1462, 19, 1140, 17], [1462, 21, 1140, 19], [1463, 6, 1141, 4, "fontSize"], [1463, 14, 1141, 12], [1463, 16, 1141, 14], [1463, 18, 1141, 16], [1464, 6, 1142, 4, "fontWeight"], [1464, 16, 1142, 14], [1464, 18, 1142, 16], [1464, 23, 1142, 21], [1465, 6, 1143, 4, "color"], [1465, 11, 1143, 9], [1465, 13, 1143, 11], [1465, 22, 1143, 20], [1466, 6, 1144, 4, "marginTop"], [1466, 15, 1144, 13], [1466, 17, 1144, 15], [1466, 19, 1144, 17], [1467, 6, 1145, 4, "marginBottom"], [1467, 18, 1145, 16], [1467, 20, 1145, 18], [1468, 4, 1146, 2], [1468, 5, 1146, 3], [1469, 4, 1147, 2, "progressBar"], [1469, 15, 1147, 13], [1469, 17, 1147, 15], [1470, 6, 1148, 4, "width"], [1470, 11, 1148, 9], [1470, 13, 1148, 11], [1470, 19, 1148, 17], [1471, 6, 1149, 4, "height"], [1471, 12, 1149, 10], [1471, 14, 1149, 12], [1471, 15, 1149, 13], [1472, 6, 1150, 4, "backgroundColor"], [1472, 21, 1150, 19], [1472, 23, 1150, 21], [1472, 32, 1150, 30], [1473, 6, 1151, 4, "borderRadius"], [1473, 18, 1151, 16], [1473, 20, 1151, 18], [1473, 21, 1151, 19], [1474, 6, 1152, 4, "overflow"], [1474, 14, 1152, 12], [1474, 16, 1152, 14], [1474, 24, 1152, 22], [1475, 6, 1153, 4, "marginBottom"], [1475, 18, 1153, 16], [1475, 20, 1153, 18], [1476, 4, 1154, 2], [1476, 5, 1154, 3], [1477, 4, 1155, 2, "progressFill"], [1477, 16, 1155, 14], [1477, 18, 1155, 16], [1478, 6, 1156, 4, "height"], [1478, 12, 1156, 10], [1478, 14, 1156, 12], [1478, 20, 1156, 18], [1479, 6, 1157, 4, "backgroundColor"], [1479, 21, 1157, 19], [1479, 23, 1157, 21], [1479, 32, 1157, 30], [1480, 6, 1158, 4, "borderRadius"], [1480, 18, 1158, 16], [1480, 20, 1158, 18], [1481, 4, 1159, 2], [1481, 5, 1159, 3], [1482, 4, 1160, 2, "processingDescription"], [1482, 25, 1160, 23], [1482, 27, 1160, 25], [1483, 6, 1161, 4, "fontSize"], [1483, 14, 1161, 12], [1483, 16, 1161, 14], [1483, 18, 1161, 16], [1484, 6, 1162, 4, "color"], [1484, 11, 1162, 9], [1484, 13, 1162, 11], [1484, 22, 1162, 20], [1485, 6, 1163, 4, "textAlign"], [1485, 15, 1163, 13], [1485, 17, 1163, 15], [1486, 4, 1164, 2], [1486, 5, 1164, 3], [1487, 4, 1165, 2, "successIcon"], [1487, 15, 1165, 13], [1487, 17, 1165, 15], [1488, 6, 1166, 4, "marginTop"], [1488, 15, 1166, 13], [1488, 17, 1166, 15], [1489, 4, 1167, 2], [1489, 5, 1167, 3], [1490, 4, 1168, 2, "errorContent"], [1490, 16, 1168, 14], [1490, 18, 1168, 16], [1491, 6, 1169, 4, "backgroundColor"], [1491, 21, 1169, 19], [1491, 23, 1169, 21], [1491, 29, 1169, 27], [1492, 6, 1170, 4, "borderRadius"], [1492, 18, 1170, 16], [1492, 20, 1170, 18], [1492, 22, 1170, 20], [1493, 6, 1171, 4, "padding"], [1493, 13, 1171, 11], [1493, 15, 1171, 13], [1493, 17, 1171, 15], [1494, 6, 1172, 4, "width"], [1494, 11, 1172, 9], [1494, 13, 1172, 11], [1494, 18, 1172, 16], [1495, 6, 1173, 4, "max<PERSON><PERSON><PERSON>"], [1495, 14, 1173, 12], [1495, 16, 1173, 14], [1495, 19, 1173, 17], [1496, 6, 1174, 4, "alignItems"], [1496, 16, 1174, 14], [1496, 18, 1174, 16], [1497, 4, 1175, 2], [1497, 5, 1175, 3], [1498, 4, 1176, 2, "errorTitle"], [1498, 14, 1176, 12], [1498, 16, 1176, 14], [1499, 6, 1177, 4, "fontSize"], [1499, 14, 1177, 12], [1499, 16, 1177, 14], [1499, 18, 1177, 16], [1500, 6, 1178, 4, "fontWeight"], [1500, 16, 1178, 14], [1500, 18, 1178, 16], [1500, 23, 1178, 21], [1501, 6, 1179, 4, "color"], [1501, 11, 1179, 9], [1501, 13, 1179, 11], [1501, 22, 1179, 20], [1502, 6, 1180, 4, "marginTop"], [1502, 15, 1180, 13], [1502, 17, 1180, 15], [1502, 19, 1180, 17], [1503, 6, 1181, 4, "marginBottom"], [1503, 18, 1181, 16], [1503, 20, 1181, 18], [1504, 4, 1182, 2], [1504, 5, 1182, 3], [1505, 4, 1183, 2, "errorMessage"], [1505, 16, 1183, 14], [1505, 18, 1183, 16], [1506, 6, 1184, 4, "fontSize"], [1506, 14, 1184, 12], [1506, 16, 1184, 14], [1506, 18, 1184, 16], [1507, 6, 1185, 4, "color"], [1507, 11, 1185, 9], [1507, 13, 1185, 11], [1507, 22, 1185, 20], [1508, 6, 1186, 4, "textAlign"], [1508, 15, 1186, 13], [1508, 17, 1186, 15], [1508, 25, 1186, 23], [1509, 6, 1187, 4, "marginBottom"], [1509, 18, 1187, 16], [1509, 20, 1187, 18], [1510, 4, 1188, 2], [1510, 5, 1188, 3], [1511, 4, 1189, 2, "primaryButton"], [1511, 17, 1189, 15], [1511, 19, 1189, 17], [1512, 6, 1190, 4, "backgroundColor"], [1512, 21, 1190, 19], [1512, 23, 1190, 21], [1512, 32, 1190, 30], [1513, 6, 1191, 4, "paddingHorizontal"], [1513, 23, 1191, 21], [1513, 25, 1191, 23], [1513, 27, 1191, 25], [1514, 6, 1192, 4, "paddingVertical"], [1514, 21, 1192, 19], [1514, 23, 1192, 21], [1514, 25, 1192, 23], [1515, 6, 1193, 4, "borderRadius"], [1515, 18, 1193, 16], [1515, 20, 1193, 18], [1515, 21, 1193, 19], [1516, 6, 1194, 4, "marginTop"], [1516, 15, 1194, 13], [1516, 17, 1194, 15], [1517, 4, 1195, 2], [1517, 5, 1195, 3], [1518, 4, 1196, 2, "primaryButtonText"], [1518, 21, 1196, 19], [1518, 23, 1196, 21], [1519, 6, 1197, 4, "color"], [1519, 11, 1197, 9], [1519, 13, 1197, 11], [1519, 19, 1197, 17], [1520, 6, 1198, 4, "fontSize"], [1520, 14, 1198, 12], [1520, 16, 1198, 14], [1520, 18, 1198, 16], [1521, 6, 1199, 4, "fontWeight"], [1521, 16, 1199, 14], [1521, 18, 1199, 16], [1522, 4, 1200, 2], [1522, 5, 1200, 3], [1523, 4, 1201, 2, "secondaryButton"], [1523, 19, 1201, 17], [1523, 21, 1201, 19], [1524, 6, 1202, 4, "paddingHorizontal"], [1524, 23, 1202, 21], [1524, 25, 1202, 23], [1524, 27, 1202, 25], [1525, 6, 1203, 4, "paddingVertical"], [1525, 21, 1203, 19], [1525, 23, 1203, 21], [1525, 25, 1203, 23], [1526, 6, 1204, 4, "marginTop"], [1526, 15, 1204, 13], [1526, 17, 1204, 15], [1527, 4, 1205, 2], [1527, 5, 1205, 3], [1528, 4, 1206, 2, "secondaryButtonText"], [1528, 23, 1206, 21], [1528, 25, 1206, 23], [1529, 6, 1207, 4, "color"], [1529, 11, 1207, 9], [1529, 13, 1207, 11], [1529, 22, 1207, 20], [1530, 6, 1208, 4, "fontSize"], [1530, 14, 1208, 12], [1530, 16, 1208, 14], [1531, 4, 1209, 2], [1531, 5, 1209, 3], [1532, 4, 1210, 2, "permissionContent"], [1532, 21, 1210, 19], [1532, 23, 1210, 21], [1533, 6, 1211, 4, "flex"], [1533, 10, 1211, 8], [1533, 12, 1211, 10], [1533, 13, 1211, 11], [1534, 6, 1212, 4, "justifyContent"], [1534, 20, 1212, 18], [1534, 22, 1212, 20], [1534, 30, 1212, 28], [1535, 6, 1213, 4, "alignItems"], [1535, 16, 1213, 14], [1535, 18, 1213, 16], [1535, 26, 1213, 24], [1536, 6, 1214, 4, "padding"], [1536, 13, 1214, 11], [1536, 15, 1214, 13], [1537, 4, 1215, 2], [1537, 5, 1215, 3], [1538, 4, 1216, 2, "permissionTitle"], [1538, 19, 1216, 17], [1538, 21, 1216, 19], [1539, 6, 1217, 4, "fontSize"], [1539, 14, 1217, 12], [1539, 16, 1217, 14], [1539, 18, 1217, 16], [1540, 6, 1218, 4, "fontWeight"], [1540, 16, 1218, 14], [1540, 18, 1218, 16], [1540, 23, 1218, 21], [1541, 6, 1219, 4, "color"], [1541, 11, 1219, 9], [1541, 13, 1219, 11], [1541, 22, 1219, 20], [1542, 6, 1220, 4, "marginTop"], [1542, 15, 1220, 13], [1542, 17, 1220, 15], [1542, 19, 1220, 17], [1543, 6, 1221, 4, "marginBottom"], [1543, 18, 1221, 16], [1543, 20, 1221, 18], [1544, 4, 1222, 2], [1544, 5, 1222, 3], [1545, 4, 1223, 2, "permissionDescription"], [1545, 25, 1223, 23], [1545, 27, 1223, 25], [1546, 6, 1224, 4, "fontSize"], [1546, 14, 1224, 12], [1546, 16, 1224, 14], [1546, 18, 1224, 16], [1547, 6, 1225, 4, "color"], [1547, 11, 1225, 9], [1547, 13, 1225, 11], [1547, 22, 1225, 20], [1548, 6, 1226, 4, "textAlign"], [1548, 15, 1226, 13], [1548, 17, 1226, 15], [1548, 25, 1226, 23], [1549, 6, 1227, 4, "marginBottom"], [1549, 18, 1227, 16], [1549, 20, 1227, 18], [1550, 4, 1228, 2], [1550, 5, 1228, 3], [1551, 4, 1229, 2, "loadingText"], [1551, 15, 1229, 13], [1551, 17, 1229, 15], [1552, 6, 1230, 4, "color"], [1552, 11, 1230, 9], [1552, 13, 1230, 11], [1552, 22, 1230, 20], [1553, 6, 1231, 4, "marginTop"], [1553, 15, 1231, 13], [1553, 17, 1231, 15], [1554, 4, 1232, 2], [1554, 5, 1232, 3], [1555, 4, 1233, 2], [1556, 4, 1234, 2, "blurZone"], [1556, 12, 1234, 10], [1556, 14, 1234, 12], [1557, 6, 1235, 4, "position"], [1557, 14, 1235, 12], [1557, 16, 1235, 14], [1557, 26, 1235, 24], [1558, 6, 1236, 4, "overflow"], [1558, 14, 1236, 12], [1558, 16, 1236, 14], [1559, 4, 1237, 2], [1559, 5, 1237, 3], [1560, 4, 1238, 2, "previewChip"], [1560, 15, 1238, 13], [1560, 17, 1238, 15], [1561, 6, 1239, 4, "position"], [1561, 14, 1239, 12], [1561, 16, 1239, 14], [1561, 26, 1239, 24], [1562, 6, 1240, 4, "top"], [1562, 9, 1240, 7], [1562, 11, 1240, 9], [1562, 12, 1240, 10], [1563, 6, 1241, 4, "right"], [1563, 11, 1241, 9], [1563, 13, 1241, 11], [1563, 14, 1241, 12], [1564, 6, 1242, 4, "backgroundColor"], [1564, 21, 1242, 19], [1564, 23, 1242, 21], [1564, 40, 1242, 38], [1565, 6, 1243, 4, "paddingHorizontal"], [1565, 23, 1243, 21], [1565, 25, 1243, 23], [1565, 27, 1243, 25], [1566, 6, 1244, 4, "paddingVertical"], [1566, 21, 1244, 19], [1566, 23, 1244, 21], [1566, 24, 1244, 22], [1567, 6, 1245, 4, "borderRadius"], [1567, 18, 1245, 16], [1567, 20, 1245, 18], [1568, 4, 1246, 2], [1568, 5, 1246, 3], [1569, 4, 1247, 2, "previewChipText"], [1569, 19, 1247, 17], [1569, 21, 1247, 19], [1570, 6, 1248, 4, "color"], [1570, 11, 1248, 9], [1570, 13, 1248, 11], [1570, 19, 1248, 17], [1571, 6, 1249, 4, "fontSize"], [1571, 14, 1249, 12], [1571, 16, 1249, 14], [1571, 18, 1249, 16], [1572, 6, 1250, 4, "fontWeight"], [1572, 16, 1250, 14], [1572, 18, 1250, 16], [1573, 4, 1251, 2], [1574, 2, 1252, 0], [1574, 3, 1252, 1], [1574, 4, 1252, 2], [1575, 2, 1252, 3], [1575, 6, 1252, 3, "_c"], [1575, 8, 1252, 3], [1576, 2, 1252, 3, "$RefreshReg$"], [1576, 14, 1252, 3], [1576, 15, 1252, 3, "_c"], [1576, 17, 1252, 3], [1577, 0, 1252, 3], [1577, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "countSkinPixelsInRegion", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;kCSE;GTgB;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdqD;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SFwB;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}