{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 47, "index": 47}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 48}, "end": {"line": 2, "column": 62, "index": 110}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/createElement", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 160}, "end": {"line": 4, "column": 72, "index": 232}}], "key": "TmdlMkCj7Q38hztt3cxXAUOFN8M=", "exportNames": ["*"]}}, {"name": "./ExpoCameraManager.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 233}, "end": {"line": 5, "column": 52, "index": 285}}], "key": "bCn/ZATDxhHXIw+6diQTj6+FSr8=", "exportNames": ["*"]}}, {"name": "./web/WebCameraUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 286}, "end": {"line": 6, "column": 47, "index": 333}}], "key": "XRO8rYq5MKJNgV7LdTgYQG/9Zd0=", "exportNames": ["*"]}}, {"name": "./web/WebConstants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 334}, "end": {"line": 7, "column": 50, "index": 384}}], "key": "JZCmfw6Yvb1iFyzCmCeEPNNOKXw=", "exportNames": ["*"]}}, {"name": "./web/useWebCameraStream", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 385}, "end": {"line": 8, "column": 62, "index": 447}}], "key": "I6tCGOn1ROzWujdFThN13Of/Y00=", "exportNames": ["*"]}}, {"name": "./web/useWebQRScanner", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 448}, "end": {"line": 9, "column": 56, "index": 504}}], "key": "bZtaM5yB7B478U0sqbglpWw6RBE=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _react = require(_dependencyMap[2], \"react\");\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/View\"));\n  var _createElement = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/createElement\"));\n  var _ExpoCameraManager = _interopRequireDefault(require(_dependencyMap[6], \"./ExpoCameraManager.web\"));\n  var _WebCameraUtils = require(_dependencyMap[7], \"./web/WebCameraUtils\");\n  var _WebConstants = require(_dependencyMap[8], \"./web/WebConstants\");\n  var _useWebCameraStream = require(_dependencyMap[9], \"./web/useWebCameraStream\");\n  var _useWebQRScanner = require(_dependencyMap[10], \"./web/useWebQRScanner\");\n  var _jsxDevRuntime = require(_dependencyMap[11], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\expo-camera\\\\build\\\\ExpoCamera.web.js\";\n  const ExponentCamera = ({\n    facing,\n    poster,\n    ref,\n    ...props\n  }) => {\n    const video = (0, _react.useRef)(null);\n    const native = (0, _useWebCameraStream.useWebCameraStream)(video, facing, props, {\n      onCameraReady() {\n        if (props.onCameraReady) {\n          props.onCameraReady();\n        }\n      },\n      onMountError: props.onMountError\n    });\n    const isQRScannerEnabled = (0, _react.useMemo)(() => {\n      return Boolean(props.barcodeScannerSettings?.barcodeTypes?.includes('qr') && !!props.onBarcodeScanned);\n    }, [props.barcodeScannerSettings?.barcodeTypes, props.onBarcodeScanned]);\n    (0, _useWebQRScanner.useWebQRScanner)(video, {\n      interval: 300,\n      isEnabled: isQRScannerEnabled,\n      captureOptions: {\n        scale: 1,\n        isImageMirror: native.type === 'front'\n      },\n      onScanned(event) {\n        if (props.onBarcodeScanned) {\n          props.onBarcodeScanned(event);\n        }\n      }\n    });\n    (0, _react.useImperativeHandle)(ref, () => ({\n      async getAvailablePictureSizes() {\n        return _WebConstants.PictureSizes;\n      },\n      async takePicture(options) {\n        if (!video.current || video.current?.readyState !== video.current?.HAVE_ENOUGH_DATA) {\n          throw new _expoModulesCore.CodedError('ERR_CAMERA_NOT_READY', 'HTMLVideoElement does not have enough camera data to construct an image yet.');\n        }\n        const settings = native.mediaTrackSettings;\n        if (!settings) {\n          throw new _expoModulesCore.CodedError('ERR_CAMERA_NOT_READY', 'MediaStream is not ready yet.');\n        }\n        return (0, _WebCameraUtils.capture)(video.current, settings, {\n          ...options,\n          // This will always be defined, the option gets added to a queue in the upper-level. We should replace the original so it isn't called twice.\n          onPictureSaved(picture) {\n            if (options.onPictureSaved) {\n              options.onPictureSaved(picture);\n            }\n            if (props.onPictureSaved) {\n              props.onPictureSaved({\n                nativeEvent: {\n                  data: picture,\n                  id: -1\n                }\n              });\n            }\n          }\n        });\n      },\n      async resumePreview() {\n        if (video.current) {\n          video.current.play();\n        }\n      },\n      async pausePreview() {\n        if (video.current) {\n          video.current.pause();\n        }\n      },\n      async stopRecording() {\n        console.warn('stopRecording is not supported on web.');\n      },\n      async record() {\n        console.warn('record is not supported on web.');\n        return {\n          uri: ''\n        };\n      },\n      async toggleRecording() {\n        console.warn('toggleRecording is not supported on web.');\n      },\n      async launchModernScanner() {\n        console.warn('launchModernScanner is not supported on web.');\n      },\n      async getAvailableLenses() {\n        console.warn('getAvailableLenses is not supported on web.');\n        return [];\n      }\n    }), [native.mediaTrackSettings, props.onPictureSaved]);\n    // TODO(Bacon): Create a universal prop, on native the microphone is only used when recording videos.\n    // Because we don't support recording video in the browser we don't need the user to give microphone permissions.\n    const isMuted = true;\n    const style = (0, _react.useMemo)(() => {\n      const isFrontFacingCamera = native.type === _ExpoCameraManager.default.Type.front;\n      return [_StyleSheet.default.absoluteFill, styles.video, {\n        // Flip the camera\n        transform: isFrontFacingCamera ? [{\n          scaleX: -1\n        }] : undefined\n      }];\n    }, [native.type]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      pointerEvents: \"box-none\",\n      style: [styles.videoWrapper, props.style],\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Video, {\n        autoPlay: true,\n        playsInline: true,\n        muted: isMuted,\n        poster: poster,\n        pointerEvents: props.pointerEvents,\n        ref: video,\n        style: style\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 7\n      }, this), props.children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this);\n  };\n  var _default = exports.default = ExponentCamera;\n  const Video = props => (0, _createElement.default)('video', {\n    ...props\n  });\n  const styles = _StyleSheet.default.create({\n    videoWrapper: {\n      flex: 1,\n      alignItems: 'stretch'\n    },\n    video: {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover'\n    }\n  });\n});", "lineCount": 159, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_expoModulesCore"], [7, 22, 1, 0], [7, 25, 1, 0, "require"], [7, 32, 1, 0], [7, 33, 1, 0, "_dependencyMap"], [7, 47, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 2, 62], [9, 6, 2, 62, "_StyleSheet"], [9, 17, 2, 62], [9, 20, 2, 62, "_interopRequireDefault"], [9, 42, 2, 62], [9, 43, 2, 62, "require"], [9, 50, 2, 62], [9, 51, 2, 62, "_dependencyMap"], [9, 65, 2, 62], [10, 2, 2, 62], [10, 6, 2, 62, "_View"], [10, 11, 2, 62], [10, 14, 2, 62, "_interopRequireDefault"], [10, 36, 2, 62], [10, 37, 2, 62, "require"], [10, 44, 2, 62], [10, 45, 2, 62, "_dependencyMap"], [10, 59, 2, 62], [11, 2, 4, 0], [11, 6, 4, 0, "_createElement"], [11, 20, 4, 0], [11, 23, 4, 0, "_interopRequireDefault"], [11, 45, 4, 0], [11, 46, 4, 0, "require"], [11, 53, 4, 0], [11, 54, 4, 0, "_dependencyMap"], [11, 68, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_ExpoCameraManager"], [12, 24, 5, 0], [12, 27, 5, 0, "_interopRequireDefault"], [12, 49, 5, 0], [12, 50, 5, 0, "require"], [12, 57, 5, 0], [12, 58, 5, 0, "_dependencyMap"], [12, 72, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_WebCameraUtils"], [13, 21, 6, 0], [13, 24, 6, 0, "require"], [13, 31, 6, 0], [13, 32, 6, 0, "_dependencyMap"], [13, 46, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_WebConstants"], [14, 19, 7, 0], [14, 22, 7, 0, "require"], [14, 29, 7, 0], [14, 30, 7, 0, "_dependencyMap"], [14, 44, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_useWebCameraStream"], [15, 25, 8, 0], [15, 28, 8, 0, "require"], [15, 35, 8, 0], [15, 36, 8, 0, "_dependencyMap"], [15, 50, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_useWebQRScanner"], [16, 22, 9, 0], [16, 25, 9, 0, "require"], [16, 32, 9, 0], [16, 33, 9, 0, "_dependencyMap"], [16, 47, 9, 0], [17, 2, 9, 56], [17, 6, 9, 56, "_jsxDevRuntime"], [17, 20, 9, 56], [17, 23, 9, 56, "require"], [17, 30, 9, 56], [17, 31, 9, 56, "_dependencyMap"], [17, 45, 9, 56], [18, 2, 9, 56], [18, 6, 9, 56, "_jsxFileName"], [18, 18, 9, 56], [19, 2, 10, 0], [19, 8, 10, 6, "ExponentCamera"], [19, 22, 10, 20], [19, 25, 10, 23, "ExponentCamera"], [19, 26, 10, 24], [20, 4, 10, 26, "facing"], [20, 10, 10, 32], [21, 4, 10, 34, "poster"], [21, 10, 10, 40], [22, 4, 10, 42, "ref"], [22, 7, 10, 45], [23, 4, 10, 47], [23, 7, 10, 50, "props"], [24, 2, 10, 56], [24, 3, 10, 57], [24, 8, 10, 62], [25, 4, 11, 4], [25, 10, 11, 10, "video"], [25, 15, 11, 15], [25, 18, 11, 18], [25, 22, 11, 18, "useRef"], [25, 35, 11, 24], [25, 37, 11, 25], [25, 41, 11, 29], [25, 42, 11, 30], [26, 4, 12, 4], [26, 10, 12, 10, "native"], [26, 16, 12, 16], [26, 19, 12, 19], [26, 23, 12, 19, "useWebCameraStream"], [26, 61, 12, 37], [26, 63, 12, 38, "video"], [26, 68, 12, 43], [26, 70, 12, 45, "facing"], [26, 76, 12, 51], [26, 78, 12, 53, "props"], [26, 83, 12, 58], [26, 85, 12, 60], [27, 6, 13, 8, "onCameraReady"], [27, 19, 13, 21, "onCameraReady"], [27, 20, 13, 21], [27, 22, 13, 24], [28, 8, 14, 12], [28, 12, 14, 16, "props"], [28, 17, 14, 21], [28, 18, 14, 22, "onCameraReady"], [28, 31, 14, 35], [28, 33, 14, 37], [29, 10, 15, 16, "props"], [29, 15, 15, 21], [29, 16, 15, 22, "onCameraReady"], [29, 29, 15, 35], [29, 30, 15, 36], [29, 31, 15, 37], [30, 8, 16, 12], [31, 6, 17, 8], [31, 7, 17, 9], [32, 6, 18, 8, "onMountError"], [32, 18, 18, 20], [32, 20, 18, 22, "props"], [32, 25, 18, 27], [32, 26, 18, 28, "onMountError"], [33, 4, 19, 4], [33, 5, 19, 5], [33, 6, 19, 6], [34, 4, 20, 4], [34, 10, 20, 10, "isQRScannerEnabled"], [34, 28, 20, 28], [34, 31, 20, 31], [34, 35, 20, 31, "useMemo"], [34, 49, 20, 38], [34, 51, 20, 39], [34, 57, 20, 45], [35, 6, 21, 8], [35, 13, 21, 15, "Boolean"], [35, 20, 21, 22], [35, 21, 21, 23, "props"], [35, 26, 21, 28], [35, 27, 21, 29, "barcodeScannerSettings"], [35, 49, 21, 51], [35, 51, 21, 53, "barcodeTypes"], [35, 63, 21, 65], [35, 65, 21, 67, "includes"], [35, 73, 21, 75], [35, 74, 21, 76], [35, 78, 21, 80], [35, 79, 21, 81], [35, 83, 21, 85], [35, 84, 21, 86], [35, 85, 21, 87, "props"], [35, 90, 21, 92], [35, 91, 21, 93, "onBarcodeScanned"], [35, 107, 21, 109], [35, 108, 21, 110], [36, 4, 22, 4], [36, 5, 22, 5], [36, 7, 22, 7], [36, 8, 22, 8, "props"], [36, 13, 22, 13], [36, 14, 22, 14, "barcodeScannerSettings"], [36, 36, 22, 36], [36, 38, 22, 38, "barcodeTypes"], [36, 50, 22, 50], [36, 52, 22, 52, "props"], [36, 57, 22, 57], [36, 58, 22, 58, "onBarcodeScanned"], [36, 74, 22, 74], [36, 75, 22, 75], [36, 76, 22, 76], [37, 4, 23, 4], [37, 8, 23, 4, "useWebQRScanner"], [37, 40, 23, 19], [37, 42, 23, 20, "video"], [37, 47, 23, 25], [37, 49, 23, 27], [38, 6, 24, 8, "interval"], [38, 14, 24, 16], [38, 16, 24, 18], [38, 19, 24, 21], [39, 6, 25, 8, "isEnabled"], [39, 15, 25, 17], [39, 17, 25, 19, "isQRScannerEnabled"], [39, 35, 25, 37], [40, 6, 26, 8, "captureOptions"], [40, 20, 26, 22], [40, 22, 26, 24], [41, 8, 26, 26, "scale"], [41, 13, 26, 31], [41, 15, 26, 33], [41, 16, 26, 34], [42, 8, 26, 36, "isImageMirror"], [42, 21, 26, 49], [42, 23, 26, 51, "native"], [42, 29, 26, 57], [42, 30, 26, 58, "type"], [42, 34, 26, 62], [42, 39, 26, 67], [43, 6, 26, 75], [43, 7, 26, 76], [44, 6, 27, 8, "onScanned"], [44, 15, 27, 17, "onScanned"], [44, 16, 27, 18, "event"], [44, 21, 27, 23], [44, 23, 27, 25], [45, 8, 28, 12], [45, 12, 28, 16, "props"], [45, 17, 28, 21], [45, 18, 28, 22, "onBarcodeScanned"], [45, 34, 28, 38], [45, 36, 28, 40], [46, 10, 29, 16, "props"], [46, 15, 29, 21], [46, 16, 29, 22, "onBarcodeScanned"], [46, 32, 29, 38], [46, 33, 29, 39, "event"], [46, 38, 29, 44], [46, 39, 29, 45], [47, 8, 30, 12], [48, 6, 31, 8], [49, 4, 32, 4], [49, 5, 32, 5], [49, 6, 32, 6], [50, 4, 33, 4], [50, 8, 33, 4, "useImperativeHandle"], [50, 34, 33, 23], [50, 36, 33, 24, "ref"], [50, 39, 33, 27], [50, 41, 33, 29], [50, 48, 33, 36], [51, 6, 34, 8], [51, 12, 34, 14, "getAvailablePictureSizes"], [51, 36, 34, 38, "getAvailablePictureSizes"], [51, 37, 34, 38], [51, 39, 34, 41], [52, 8, 35, 12], [52, 15, 35, 19, "PictureSizes"], [52, 41, 35, 31], [53, 6, 36, 8], [53, 7, 36, 9], [54, 6, 37, 8], [54, 12, 37, 14, "takePicture"], [54, 23, 37, 25, "takePicture"], [54, 24, 37, 26, "options"], [54, 31, 37, 33], [54, 33, 37, 35], [55, 8, 38, 12], [55, 12, 38, 16], [55, 13, 38, 17, "video"], [55, 18, 38, 22], [55, 19, 38, 23, "current"], [55, 26, 38, 30], [55, 30, 38, 34, "video"], [55, 35, 38, 39], [55, 36, 38, 40, "current"], [55, 43, 38, 47], [55, 45, 38, 49, "readyState"], [55, 55, 38, 59], [55, 60, 38, 64, "video"], [55, 65, 38, 69], [55, 66, 38, 70, "current"], [55, 73, 38, 77], [55, 75, 38, 79, "HAVE_ENOUGH_DATA"], [55, 91, 38, 95], [55, 93, 38, 97], [56, 10, 39, 16], [56, 16, 39, 22], [56, 20, 39, 26, "CodedError"], [56, 47, 39, 36], [56, 48, 39, 37], [56, 70, 39, 59], [56, 72, 39, 61], [56, 150, 39, 139], [56, 151, 39, 140], [57, 8, 40, 12], [58, 8, 41, 12], [58, 14, 41, 18, "settings"], [58, 22, 41, 26], [58, 25, 41, 29, "native"], [58, 31, 41, 35], [58, 32, 41, 36, "mediaTrackSettings"], [58, 50, 41, 54], [59, 8, 42, 12], [59, 12, 42, 16], [59, 13, 42, 17, "settings"], [59, 21, 42, 25], [59, 23, 42, 27], [60, 10, 43, 16], [60, 16, 43, 22], [60, 20, 43, 26, "CodedError"], [60, 47, 43, 36], [60, 48, 43, 37], [60, 70, 43, 59], [60, 72, 43, 61], [60, 103, 43, 92], [60, 104, 43, 93], [61, 8, 44, 12], [62, 8, 45, 12], [62, 15, 45, 19], [62, 19, 45, 19, "capture"], [62, 42, 45, 26], [62, 44, 45, 27, "video"], [62, 49, 45, 32], [62, 50, 45, 33, "current"], [62, 57, 45, 40], [62, 59, 45, 42, "settings"], [62, 67, 45, 50], [62, 69, 45, 52], [63, 10, 46, 16], [63, 13, 46, 19, "options"], [63, 20, 46, 26], [64, 10, 47, 16], [65, 10, 48, 16, "onPictureSaved"], [65, 24, 48, 30, "onPictureSaved"], [65, 25, 48, 31, "picture"], [65, 32, 48, 38], [65, 34, 48, 40], [66, 12, 49, 20], [66, 16, 49, 24, "options"], [66, 23, 49, 31], [66, 24, 49, 32, "onPictureSaved"], [66, 38, 49, 46], [66, 40, 49, 48], [67, 14, 50, 24, "options"], [67, 21, 50, 31], [67, 22, 50, 32, "onPictureSaved"], [67, 36, 50, 46], [67, 37, 50, 47, "picture"], [67, 44, 50, 54], [67, 45, 50, 55], [68, 12, 51, 20], [69, 12, 52, 20], [69, 16, 52, 24, "props"], [69, 21, 52, 29], [69, 22, 52, 30, "onPictureSaved"], [69, 36, 52, 44], [69, 38, 52, 46], [70, 14, 53, 24, "props"], [70, 19, 53, 29], [70, 20, 53, 30, "onPictureSaved"], [70, 34, 53, 44], [70, 35, 53, 45], [71, 16, 53, 47, "nativeEvent"], [71, 27, 53, 58], [71, 29, 53, 60], [72, 18, 53, 62, "data"], [72, 22, 53, 66], [72, 24, 53, 68, "picture"], [72, 31, 53, 75], [73, 18, 53, 77, "id"], [73, 20, 53, 79], [73, 22, 53, 81], [73, 23, 53, 82], [74, 16, 53, 84], [75, 14, 53, 86], [75, 15, 53, 87], [75, 16, 53, 88], [76, 12, 54, 20], [77, 10, 55, 16], [78, 8, 56, 12], [78, 9, 56, 13], [78, 10, 56, 14], [79, 6, 57, 8], [79, 7, 57, 9], [80, 6, 58, 8], [80, 12, 58, 14, "resumePreview"], [80, 25, 58, 27, "resumePreview"], [80, 26, 58, 27], [80, 28, 58, 30], [81, 8, 59, 12], [81, 12, 59, 16, "video"], [81, 17, 59, 21], [81, 18, 59, 22, "current"], [81, 25, 59, 29], [81, 27, 59, 31], [82, 10, 60, 16, "video"], [82, 15, 60, 21], [82, 16, 60, 22, "current"], [82, 23, 60, 29], [82, 24, 60, 30, "play"], [82, 28, 60, 34], [82, 29, 60, 35], [82, 30, 60, 36], [83, 8, 61, 12], [84, 6, 62, 8], [84, 7, 62, 9], [85, 6, 63, 8], [85, 12, 63, 14, "pausePreview"], [85, 24, 63, 26, "pausePreview"], [85, 25, 63, 26], [85, 27, 63, 29], [86, 8, 64, 12], [86, 12, 64, 16, "video"], [86, 17, 64, 21], [86, 18, 64, 22, "current"], [86, 25, 64, 29], [86, 27, 64, 31], [87, 10, 65, 16, "video"], [87, 15, 65, 21], [87, 16, 65, 22, "current"], [87, 23, 65, 29], [87, 24, 65, 30, "pause"], [87, 29, 65, 35], [87, 30, 65, 36], [87, 31, 65, 37], [88, 8, 66, 12], [89, 6, 67, 8], [89, 7, 67, 9], [90, 6, 68, 8], [90, 12, 68, 14, "stopRecording"], [90, 25, 68, 27, "stopRecording"], [90, 26, 68, 27], [90, 28, 68, 30], [91, 8, 69, 12, "console"], [91, 15, 69, 19], [91, 16, 69, 20, "warn"], [91, 20, 69, 24], [91, 21, 69, 25], [91, 61, 69, 65], [91, 62, 69, 66], [92, 6, 70, 8], [92, 7, 70, 9], [93, 6, 71, 8], [93, 12, 71, 14, "record"], [93, 18, 71, 20, "record"], [93, 19, 71, 20], [93, 21, 71, 23], [94, 8, 72, 12, "console"], [94, 15, 72, 19], [94, 16, 72, 20, "warn"], [94, 20, 72, 24], [94, 21, 72, 25], [94, 54, 72, 58], [94, 55, 72, 59], [95, 8, 73, 12], [95, 15, 73, 19], [96, 10, 73, 21, "uri"], [96, 13, 73, 24], [96, 15, 73, 26], [97, 8, 73, 29], [97, 9, 73, 30], [98, 6, 74, 8], [98, 7, 74, 9], [99, 6, 75, 8], [99, 12, 75, 14, "toggleRecording"], [99, 27, 75, 29, "toggleRecording"], [99, 28, 75, 29], [99, 30, 75, 32], [100, 8, 76, 12, "console"], [100, 15, 76, 19], [100, 16, 76, 20, "warn"], [100, 20, 76, 24], [100, 21, 76, 25], [100, 63, 76, 67], [100, 64, 76, 68], [101, 6, 77, 8], [101, 7, 77, 9], [102, 6, 78, 8], [102, 12, 78, 14, "launchModernScanner"], [102, 31, 78, 33, "launchModernScanner"], [102, 32, 78, 33], [102, 34, 78, 36], [103, 8, 79, 12, "console"], [103, 15, 79, 19], [103, 16, 79, 20, "warn"], [103, 20, 79, 24], [103, 21, 79, 25], [103, 67, 79, 71], [103, 68, 79, 72], [104, 6, 80, 8], [104, 7, 80, 9], [105, 6, 81, 8], [105, 12, 81, 14, "getAvailableLenses"], [105, 30, 81, 32, "getAvailableLenses"], [105, 31, 81, 32], [105, 33, 81, 35], [106, 8, 82, 12, "console"], [106, 15, 82, 19], [106, 16, 82, 20, "warn"], [106, 20, 82, 24], [106, 21, 82, 25], [106, 66, 82, 70], [106, 67, 82, 71], [107, 8, 83, 12], [107, 15, 83, 19], [107, 17, 83, 21], [108, 6, 84, 8], [109, 4, 85, 4], [109, 5, 85, 5], [109, 6, 85, 6], [109, 8, 85, 8], [109, 9, 85, 9, "native"], [109, 15, 85, 15], [109, 16, 85, 16, "mediaTrackSettings"], [109, 34, 85, 34], [109, 36, 85, 36, "props"], [109, 41, 85, 41], [109, 42, 85, 42, "onPictureSaved"], [109, 56, 85, 56], [109, 57, 85, 57], [109, 58, 85, 58], [110, 4, 86, 4], [111, 4, 87, 4], [112, 4, 88, 4], [112, 10, 88, 10, "isMuted"], [112, 17, 88, 17], [112, 20, 88, 20], [112, 24, 88, 24], [113, 4, 89, 4], [113, 10, 89, 10, "style"], [113, 15, 89, 15], [113, 18, 89, 18], [113, 22, 89, 18, "useMemo"], [113, 36, 89, 25], [113, 38, 89, 26], [113, 44, 89, 32], [114, 6, 90, 8], [114, 12, 90, 14, "isFrontFacingCamera"], [114, 31, 90, 33], [114, 34, 90, 36, "native"], [114, 40, 90, 42], [114, 41, 90, 43, "type"], [114, 45, 90, 47], [114, 50, 90, 52, "CameraManager"], [114, 76, 90, 65], [114, 77, 90, 66, "Type"], [114, 81, 90, 70], [114, 82, 90, 71, "front"], [114, 87, 90, 76], [115, 6, 91, 8], [115, 13, 91, 15], [115, 14, 92, 12, "StyleSheet"], [115, 33, 92, 22], [115, 34, 92, 23, "absoluteFill"], [115, 46, 92, 35], [115, 48, 93, 12, "styles"], [115, 54, 93, 18], [115, 55, 93, 19, "video"], [115, 60, 93, 24], [115, 62, 94, 12], [116, 8, 95, 16], [117, 8, 96, 16, "transform"], [117, 17, 96, 25], [117, 19, 96, 27, "isFrontFacingCamera"], [117, 38, 96, 46], [117, 41, 96, 49], [117, 42, 96, 50], [118, 10, 96, 52, "scaleX"], [118, 16, 96, 58], [118, 18, 96, 60], [118, 19, 96, 61], [119, 8, 96, 63], [119, 9, 96, 64], [119, 10, 96, 65], [119, 13, 96, 68, "undefined"], [120, 6, 97, 12], [120, 7, 97, 13], [120, 8, 98, 9], [121, 4, 99, 4], [121, 5, 99, 5], [121, 7, 99, 7], [121, 8, 99, 8, "native"], [121, 14, 99, 14], [121, 15, 99, 15, "type"], [121, 19, 99, 19], [121, 20, 99, 20], [121, 21, 99, 21], [122, 4, 100, 4], [122, 24, 100, 12], [122, 28, 100, 12, "_jsxDevRuntime"], [122, 42, 100, 12], [122, 43, 100, 12, "jsxDEV"], [122, 49, 100, 12], [122, 51, 100, 13, "_View"], [122, 56, 100, 13], [122, 57, 100, 13, "default"], [122, 64, 100, 17], [123, 6, 100, 18, "pointerEvents"], [123, 19, 100, 31], [123, 21, 100, 32], [123, 31, 100, 42], [124, 6, 100, 43, "style"], [124, 11, 100, 48], [124, 13, 100, 50], [124, 14, 100, 51, "styles"], [124, 20, 100, 57], [124, 21, 100, 58, "videoWrapper"], [124, 33, 100, 70], [124, 35, 100, 72, "props"], [124, 40, 100, 77], [124, 41, 100, 78, "style"], [124, 46, 100, 83], [124, 47, 100, 85], [125, 6, 100, 85, "children"], [125, 14, 100, 85], [125, 30, 101, 6], [125, 34, 101, 6, "_jsxDevRuntime"], [125, 48, 101, 6], [125, 49, 101, 6, "jsxDEV"], [125, 55, 101, 6], [125, 57, 101, 7, "Video"], [125, 62, 101, 12], [126, 8, 101, 13, "autoPlay"], [126, 16, 101, 21], [127, 8, 101, 22, "playsInline"], [127, 19, 101, 33], [128, 8, 101, 34, "muted"], [128, 13, 101, 39], [128, 15, 101, 41, "isMuted"], [128, 22, 101, 49], [129, 8, 101, 50, "poster"], [129, 14, 101, 56], [129, 16, 101, 58, "poster"], [129, 22, 101, 65], [130, 8, 101, 66, "pointerEvents"], [130, 21, 101, 79], [130, 23, 101, 81, "props"], [130, 28, 101, 86], [130, 29, 101, 87, "pointerEvents"], [130, 42, 101, 101], [131, 8, 101, 102, "ref"], [131, 11, 101, 105], [131, 13, 101, 107, "video"], [131, 18, 101, 113], [132, 8, 101, 114, "style"], [132, 13, 101, 119], [132, 15, 101, 121, "style"], [133, 6, 101, 127], [134, 8, 101, 127, "fileName"], [134, 16, 101, 127], [134, 18, 101, 127, "_jsxFileName"], [134, 30, 101, 127], [135, 8, 101, 127, "lineNumber"], [135, 18, 101, 127], [136, 8, 101, 127, "columnNumber"], [136, 20, 101, 127], [137, 6, 101, 127], [137, 13, 101, 128], [137, 14, 101, 129], [137, 16, 102, 7, "props"], [137, 21, 102, 12], [137, 22, 102, 13, "children"], [137, 30, 102, 21], [138, 4, 102, 21], [139, 6, 102, 21, "fileName"], [139, 14, 102, 21], [139, 16, 102, 21, "_jsxFileName"], [139, 28, 102, 21], [140, 6, 102, 21, "lineNumber"], [140, 16, 102, 21], [141, 6, 102, 21, "columnNumber"], [141, 18, 102, 21], [142, 4, 102, 21], [142, 11, 103, 10], [142, 12, 103, 11], [143, 2, 104, 0], [143, 3, 104, 1], [144, 2, 104, 2], [144, 6, 104, 2, "_default"], [144, 14, 104, 2], [144, 17, 104, 2, "exports"], [144, 24, 104, 2], [144, 25, 104, 2, "default"], [144, 32, 104, 2], [144, 35, 105, 15, "ExponentCamera"], [144, 49, 105, 29], [145, 2, 106, 0], [145, 8, 106, 6, "Video"], [145, 13, 106, 11], [145, 16, 106, 15, "props"], [145, 21, 106, 20], [145, 25, 106, 25], [145, 29, 106, 25, "createElement"], [145, 51, 106, 38], [145, 53, 106, 39], [145, 60, 106, 46], [145, 62, 106, 48], [146, 4, 106, 50], [146, 7, 106, 53, "props"], [147, 2, 106, 59], [147, 3, 106, 60], [147, 4, 106, 61], [148, 2, 107, 0], [148, 8, 107, 6, "styles"], [148, 14, 107, 12], [148, 17, 107, 15, "StyleSheet"], [148, 36, 107, 25], [148, 37, 107, 26, "create"], [148, 43, 107, 32], [148, 44, 107, 33], [149, 4, 108, 4, "videoWrapper"], [149, 16, 108, 16], [149, 18, 108, 18], [150, 6, 109, 8, "flex"], [150, 10, 109, 12], [150, 12, 109, 14], [150, 13, 109, 15], [151, 6, 110, 8, "alignItems"], [151, 16, 110, 18], [151, 18, 110, 20], [152, 4, 111, 4], [152, 5, 111, 5], [153, 4, 112, 4, "video"], [153, 9, 112, 9], [153, 11, 112, 11], [154, 6, 113, 8, "width"], [154, 11, 113, 13], [154, 13, 113, 15], [154, 19, 113, 21], [155, 6, 114, 8, "height"], [155, 12, 114, 14], [155, 14, 114, 16], [155, 20, 114, 22], [156, 6, 115, 8, "objectFit"], [156, 15, 115, 17], [156, 17, 115, 19], [157, 4, 116, 4], [158, 2, 117, 0], [158, 3, 117, 1], [158, 4, 117, 2], [159, 0, 117, 3], [159, 3]], "functionMap": {"names": ["<global>", "ExponentCamera", "useWebCameraStream$argument_3.onCameraReady", "useMemo$argument_0", "useWebQRScanner$argument_1.onScanned", "useImperativeHandle$argument_1", "getAvailablePictureSizes", "takePicture", "capture$argument_2.onPictureSaved", "resumePreview", "pausePreview", "stopRecording", "record", "toggleRecording", "launchModernScanner", "getAvailableLenses", "Video"], "mappings": "AAA;uBCS;QCG;SDI;uCEG;KFE;QGK;SHI;6BIE;QCC;SDE;QEC;gBCW;iBDO;SFE;QIC;SJI;QKC;SLI;QMC;SNE;QOC;SPG;QQC;SRE;QSC;STE;QUC;SVG;MJC;0BEI;KFU;CDK;cgBE,+ChB"}}, "type": "js/module"}]}