{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 860,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 859,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 989,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 990,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 988,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 892,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1039,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1077,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1078,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1086,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1076,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1070,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 890,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1690, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 72, 383, 70], [357, 73, 383, 71], [358, 8, 384, 6], [359, 6, 385, 4], [361, 6, 387, 4], [362, 6, 388, 4], [362, 12, 388, 10, "imageData"], [362, 21, 388, 19], [362, 24, 388, 22, "ctx"], [362, 27, 388, 25], [362, 28, 388, 26, "getImageData"], [362, 40, 388, 38], [362, 41, 388, 39, "clampedX"], [362, 49, 388, 47], [362, 51, 388, 49, "clampedY"], [362, 59, 388, 57], [362, 61, 388, 59, "<PERSON><PERSON><PERSON><PERSON>"], [362, 73, 388, 71], [362, 75, 388, 73, "clampedHeight"], [362, 88, 388, 86], [362, 89, 388, 87], [363, 6, 389, 4], [363, 12, 389, 10, "data"], [363, 16, 389, 14], [363, 19, 389, 17, "imageData"], [363, 28, 389, 26], [363, 29, 389, 27, "data"], [363, 33, 389, 31], [365, 6, 391, 4], [366, 6, 392, 4], [366, 12, 392, 10, "pixelSize"], [366, 21, 392, 19], [366, 24, 392, 22, "Math"], [366, 28, 392, 26], [366, 29, 392, 27, "max"], [366, 32, 392, 30], [366, 33, 392, 31], [366, 35, 392, 33], [366, 37, 392, 35, "Math"], [366, 41, 392, 39], [366, 42, 392, 40, "min"], [366, 45, 392, 43], [366, 46, 392, 44, "<PERSON><PERSON><PERSON><PERSON>"], [366, 58, 392, 56], [366, 60, 392, 58, "clampedHeight"], [366, 73, 392, 71], [366, 74, 392, 72], [366, 77, 392, 75], [366, 78, 392, 76], [366, 79, 392, 77], [367, 6, 394, 4], [367, 11, 394, 9], [367, 15, 394, 13, "py"], [367, 17, 394, 15], [367, 20, 394, 18], [367, 21, 394, 19], [367, 23, 394, 21, "py"], [367, 25, 394, 23], [367, 28, 394, 26, "clampedHeight"], [367, 41, 394, 39], [367, 43, 394, 41, "py"], [367, 45, 394, 43], [367, 49, 394, 47, "pixelSize"], [367, 58, 394, 56], [367, 60, 394, 58], [368, 8, 395, 6], [368, 13, 395, 11], [368, 17, 395, 15, "px"], [368, 19, 395, 17], [368, 22, 395, 20], [368, 23, 395, 21], [368, 25, 395, 23, "px"], [368, 27, 395, 25], [368, 30, 395, 28, "<PERSON><PERSON><PERSON><PERSON>"], [368, 42, 395, 40], [368, 44, 395, 42, "px"], [368, 46, 395, 44], [368, 50, 395, 48, "pixelSize"], [368, 59, 395, 57], [368, 61, 395, 59], [369, 10, 396, 8], [370, 10, 397, 8], [370, 14, 397, 12, "r"], [370, 15, 397, 13], [370, 18, 397, 16], [370, 19, 397, 17], [371, 12, 397, 19, "g"], [371, 13, 397, 20], [371, 16, 397, 23], [371, 17, 397, 24], [372, 12, 397, 26, "b"], [372, 13, 397, 27], [372, 16, 397, 30], [372, 17, 397, 31], [373, 12, 397, 33, "count"], [373, 17, 397, 38], [373, 20, 397, 41], [373, 21, 397, 42], [374, 10, 399, 8], [374, 15, 399, 13], [374, 19, 399, 17, "dy"], [374, 21, 399, 19], [374, 24, 399, 22], [374, 25, 399, 23], [374, 27, 399, 25, "dy"], [374, 29, 399, 27], [374, 32, 399, 30, "pixelSize"], [374, 41, 399, 39], [374, 45, 399, 43, "py"], [374, 47, 399, 45], [374, 50, 399, 48, "dy"], [374, 52, 399, 50], [374, 55, 399, 53, "clampedHeight"], [374, 68, 399, 66], [374, 70, 399, 68, "dy"], [374, 72, 399, 70], [374, 74, 399, 72], [374, 76, 399, 74], [375, 12, 400, 10], [375, 17, 400, 15], [375, 21, 400, 19, "dx"], [375, 23, 400, 21], [375, 26, 400, 24], [375, 27, 400, 25], [375, 29, 400, 27, "dx"], [375, 31, 400, 29], [375, 34, 400, 32, "pixelSize"], [375, 43, 400, 41], [375, 47, 400, 45, "px"], [375, 49, 400, 47], [375, 52, 400, 50, "dx"], [375, 54, 400, 52], [375, 57, 400, 55, "<PERSON><PERSON><PERSON><PERSON>"], [375, 69, 400, 67], [375, 71, 400, 69, "dx"], [375, 73, 400, 71], [375, 75, 400, 73], [375, 77, 400, 75], [376, 14, 401, 12], [376, 20, 401, 18, "index"], [376, 25, 401, 23], [376, 28, 401, 26], [376, 29, 401, 27], [376, 30, 401, 28, "py"], [376, 32, 401, 30], [376, 35, 401, 33, "dy"], [376, 37, 401, 35], [376, 41, 401, 39, "<PERSON><PERSON><PERSON><PERSON>"], [376, 53, 401, 51], [376, 57, 401, 55, "px"], [376, 59, 401, 57], [376, 62, 401, 60, "dx"], [376, 64, 401, 62], [376, 65, 401, 63], [376, 69, 401, 67], [376, 70, 401, 68], [377, 14, 402, 12, "r"], [377, 15, 402, 13], [377, 19, 402, 17, "data"], [377, 23, 402, 21], [377, 24, 402, 22, "index"], [377, 29, 402, 27], [377, 30, 402, 28], [378, 14, 403, 12, "g"], [378, 15, 403, 13], [378, 19, 403, 17, "data"], [378, 23, 403, 21], [378, 24, 403, 22, "index"], [378, 29, 403, 27], [378, 32, 403, 30], [378, 33, 403, 31], [378, 34, 403, 32], [379, 14, 404, 12, "b"], [379, 15, 404, 13], [379, 19, 404, 17, "data"], [379, 23, 404, 21], [379, 24, 404, 22, "index"], [379, 29, 404, 27], [379, 32, 404, 30], [379, 33, 404, 31], [379, 34, 404, 32], [380, 14, 405, 12, "count"], [380, 19, 405, 17], [380, 21, 405, 19], [381, 12, 406, 10], [382, 10, 407, 8], [383, 10, 409, 8], [383, 14, 409, 12, "count"], [383, 19, 409, 17], [383, 22, 409, 20], [383, 23, 409, 21], [383, 25, 409, 23], [384, 12, 410, 10, "r"], [384, 13, 410, 11], [384, 16, 410, 14, "Math"], [384, 20, 410, 18], [384, 21, 410, 19, "floor"], [384, 26, 410, 24], [384, 27, 410, 25, "r"], [384, 28, 410, 26], [384, 31, 410, 29, "count"], [384, 36, 410, 34], [384, 37, 410, 35], [385, 12, 411, 10, "g"], [385, 13, 411, 11], [385, 16, 411, 14, "Math"], [385, 20, 411, 18], [385, 21, 411, 19, "floor"], [385, 26, 411, 24], [385, 27, 411, 25, "g"], [385, 28, 411, 26], [385, 31, 411, 29, "count"], [385, 36, 411, 34], [385, 37, 411, 35], [386, 12, 412, 10, "b"], [386, 13, 412, 11], [386, 16, 412, 14, "Math"], [386, 20, 412, 18], [386, 21, 412, 19, "floor"], [386, 26, 412, 24], [386, 27, 412, 25, "b"], [386, 28, 412, 26], [386, 31, 412, 29, "count"], [386, 36, 412, 34], [386, 37, 412, 35], [388, 12, 414, 10], [389, 12, 415, 10], [389, 17, 415, 15], [389, 21, 415, 19, "dy"], [389, 23, 415, 21], [389, 26, 415, 24], [389, 27, 415, 25], [389, 29, 415, 27, "dy"], [389, 31, 415, 29], [389, 34, 415, 32, "pixelSize"], [389, 43, 415, 41], [389, 47, 415, 45, "py"], [389, 49, 415, 47], [389, 52, 415, 50, "dy"], [389, 54, 415, 52], [389, 57, 415, 55, "clampedHeight"], [389, 70, 415, 68], [389, 72, 415, 70, "dy"], [389, 74, 415, 72], [389, 76, 415, 74], [389, 78, 415, 76], [390, 14, 416, 12], [390, 19, 416, 17], [390, 23, 416, 21, "dx"], [390, 25, 416, 23], [390, 28, 416, 26], [390, 29, 416, 27], [390, 31, 416, 29, "dx"], [390, 33, 416, 31], [390, 36, 416, 34, "pixelSize"], [390, 45, 416, 43], [390, 49, 416, 47, "px"], [390, 51, 416, 49], [390, 54, 416, 52, "dx"], [390, 56, 416, 54], [390, 59, 416, 57, "<PERSON><PERSON><PERSON><PERSON>"], [390, 71, 416, 69], [390, 73, 416, 71, "dx"], [390, 75, 416, 73], [390, 77, 416, 75], [390, 79, 416, 77], [391, 16, 417, 14], [391, 22, 417, 20, "index"], [391, 27, 417, 25], [391, 30, 417, 28], [391, 31, 417, 29], [391, 32, 417, 30, "py"], [391, 34, 417, 32], [391, 37, 417, 35, "dy"], [391, 39, 417, 37], [391, 43, 417, 41, "<PERSON><PERSON><PERSON><PERSON>"], [391, 55, 417, 53], [391, 59, 417, 57, "px"], [391, 61, 417, 59], [391, 64, 417, 62, "dx"], [391, 66, 417, 64], [391, 67, 417, 65], [391, 71, 417, 69], [391, 72, 417, 70], [392, 16, 418, 14, "data"], [392, 20, 418, 18], [392, 21, 418, 19, "index"], [392, 26, 418, 24], [392, 27, 418, 25], [392, 30, 418, 28, "r"], [392, 31, 418, 29], [393, 16, 419, 14, "data"], [393, 20, 419, 18], [393, 21, 419, 19, "index"], [393, 26, 419, 24], [393, 29, 419, 27], [393, 30, 419, 28], [393, 31, 419, 29], [393, 34, 419, 32, "g"], [393, 35, 419, 33], [394, 16, 420, 14, "data"], [394, 20, 420, 18], [394, 21, 420, 19, "index"], [394, 26, 420, 24], [394, 29, 420, 27], [394, 30, 420, 28], [394, 31, 420, 29], [394, 34, 420, 32, "b"], [394, 35, 420, 33], [395, 16, 421, 14], [396, 14, 422, 12], [397, 12, 423, 10], [398, 10, 424, 8], [399, 8, 425, 6], [400, 6, 426, 4], [402, 6, 428, 4], [403, 6, 429, 4], [403, 11, 429, 9], [403, 15, 429, 13, "i"], [403, 16, 429, 14], [403, 19, 429, 17], [403, 20, 429, 18], [403, 22, 429, 20, "i"], [403, 23, 429, 21], [403, 26, 429, 24], [403, 27, 429, 25], [403, 29, 429, 27, "i"], [403, 30, 429, 28], [403, 32, 429, 30], [403, 34, 429, 32], [404, 8, 430, 6, "applySimpleBlur"], [404, 23, 430, 21], [404, 24, 430, 22, "data"], [404, 28, 430, 26], [404, 30, 430, 28, "<PERSON><PERSON><PERSON><PERSON>"], [404, 42, 430, 40], [404, 44, 430, 42, "clampedHeight"], [404, 57, 430, 55], [404, 58, 430, 56], [405, 6, 431, 4], [407, 6, 433, 4], [408, 6, 434, 4, "ctx"], [408, 9, 434, 7], [408, 10, 434, 8, "putImageData"], [408, 22, 434, 20], [408, 23, 434, 21, "imageData"], [408, 32, 434, 30], [408, 34, 434, 32, "clampedX"], [408, 42, 434, 40], [408, 44, 434, 42, "clampedY"], [408, 52, 434, 50], [408, 53, 434, 51], [410, 6, 436, 4], [411, 6, 437, 4, "ctx"], [411, 9, 437, 7], [411, 10, 437, 8, "fillStyle"], [411, 19, 437, 17], [411, 22, 437, 20], [411, 48, 437, 46], [412, 6, 438, 4, "ctx"], [412, 9, 438, 7], [412, 10, 438, 8, "fillRect"], [412, 18, 438, 16], [412, 19, 438, 17, "clampedX"], [412, 27, 438, 25], [412, 29, 438, 27, "clampedY"], [412, 37, 438, 35], [412, 39, 438, 37, "<PERSON><PERSON><PERSON><PERSON>"], [412, 51, 438, 49], [412, 53, 438, 51, "clampedHeight"], [412, 66, 438, 64], [412, 67, 438, 65], [413, 4, 439, 2], [413, 5, 439, 3], [414, 4, 441, 2], [414, 10, 441, 8, "applySimpleBlur"], [414, 25, 441, 23], [414, 28, 441, 26, "applySimpleBlur"], [414, 29, 441, 27, "data"], [414, 33, 441, 50], [414, 35, 441, 52, "width"], [414, 40, 441, 65], [414, 42, 441, 67, "height"], [414, 48, 441, 81], [414, 53, 441, 86], [415, 6, 442, 4], [415, 12, 442, 10, "original"], [415, 20, 442, 18], [415, 23, 442, 21], [415, 27, 442, 25, "Uint8ClampedArray"], [415, 44, 442, 42], [415, 45, 442, 43, "data"], [415, 49, 442, 47], [415, 50, 442, 48], [416, 6, 444, 4], [416, 11, 444, 9], [416, 15, 444, 13, "y"], [416, 16, 444, 14], [416, 19, 444, 17], [416, 20, 444, 18], [416, 22, 444, 20, "y"], [416, 23, 444, 21], [416, 26, 444, 24, "height"], [416, 32, 444, 30], [416, 35, 444, 33], [416, 36, 444, 34], [416, 38, 444, 36, "y"], [416, 39, 444, 37], [416, 41, 444, 39], [416, 43, 444, 41], [417, 8, 445, 6], [417, 13, 445, 11], [417, 17, 445, 15, "x"], [417, 18, 445, 16], [417, 21, 445, 19], [417, 22, 445, 20], [417, 24, 445, 22, "x"], [417, 25, 445, 23], [417, 28, 445, 26, "width"], [417, 33, 445, 31], [417, 36, 445, 34], [417, 37, 445, 35], [417, 39, 445, 37, "x"], [417, 40, 445, 38], [417, 42, 445, 40], [417, 44, 445, 42], [418, 10, 446, 8], [418, 16, 446, 14, "index"], [418, 21, 446, 19], [418, 24, 446, 22], [418, 25, 446, 23, "y"], [418, 26, 446, 24], [418, 29, 446, 27, "width"], [418, 34, 446, 32], [418, 37, 446, 35, "x"], [418, 38, 446, 36], [418, 42, 446, 40], [418, 43, 446, 41], [420, 10, 448, 8], [421, 10, 449, 8], [421, 14, 449, 12, "r"], [421, 15, 449, 13], [421, 18, 449, 16], [421, 19, 449, 17], [422, 12, 449, 19, "g"], [422, 13, 449, 20], [422, 16, 449, 23], [422, 17, 449, 24], [423, 12, 449, 26, "b"], [423, 13, 449, 27], [423, 16, 449, 30], [423, 17, 449, 31], [424, 10, 450, 8], [424, 15, 450, 13], [424, 19, 450, 17, "dy"], [424, 21, 450, 19], [424, 24, 450, 22], [424, 25, 450, 23], [424, 26, 450, 24], [424, 28, 450, 26, "dy"], [424, 30, 450, 28], [424, 34, 450, 32], [424, 35, 450, 33], [424, 37, 450, 35, "dy"], [424, 39, 450, 37], [424, 41, 450, 39], [424, 43, 450, 41], [425, 12, 451, 10], [425, 17, 451, 15], [425, 21, 451, 19, "dx"], [425, 23, 451, 21], [425, 26, 451, 24], [425, 27, 451, 25], [425, 28, 451, 26], [425, 30, 451, 28, "dx"], [425, 32, 451, 30], [425, 36, 451, 34], [425, 37, 451, 35], [425, 39, 451, 37, "dx"], [425, 41, 451, 39], [425, 43, 451, 41], [425, 45, 451, 43], [426, 14, 452, 12], [426, 20, 452, 18, "neighborIndex"], [426, 33, 452, 31], [426, 36, 452, 34], [426, 37, 452, 35], [426, 38, 452, 36, "y"], [426, 39, 452, 37], [426, 42, 452, 40, "dy"], [426, 44, 452, 42], [426, 48, 452, 46, "width"], [426, 53, 452, 51], [426, 57, 452, 55, "x"], [426, 58, 452, 56], [426, 61, 452, 59, "dx"], [426, 63, 452, 61], [426, 64, 452, 62], [426, 68, 452, 66], [426, 69, 452, 67], [427, 14, 453, 12, "r"], [427, 15, 453, 13], [427, 19, 453, 17, "original"], [427, 27, 453, 25], [427, 28, 453, 26, "neighborIndex"], [427, 41, 453, 39], [427, 42, 453, 40], [428, 14, 454, 12, "g"], [428, 15, 454, 13], [428, 19, 454, 17, "original"], [428, 27, 454, 25], [428, 28, 454, 26, "neighborIndex"], [428, 41, 454, 39], [428, 44, 454, 42], [428, 45, 454, 43], [428, 46, 454, 44], [429, 14, 455, 12, "b"], [429, 15, 455, 13], [429, 19, 455, 17, "original"], [429, 27, 455, 25], [429, 28, 455, 26, "neighborIndex"], [429, 41, 455, 39], [429, 44, 455, 42], [429, 45, 455, 43], [429, 46, 455, 44], [430, 12, 456, 10], [431, 10, 457, 8], [432, 10, 459, 8, "data"], [432, 14, 459, 12], [432, 15, 459, 13, "index"], [432, 20, 459, 18], [432, 21, 459, 19], [432, 24, 459, 22, "r"], [432, 25, 459, 23], [432, 28, 459, 26], [432, 29, 459, 27], [433, 10, 460, 8, "data"], [433, 14, 460, 12], [433, 15, 460, 13, "index"], [433, 20, 460, 18], [433, 23, 460, 21], [433, 24, 460, 22], [433, 25, 460, 23], [433, 28, 460, 26, "g"], [433, 29, 460, 27], [433, 32, 460, 30], [433, 33, 460, 31], [434, 10, 461, 8, "data"], [434, 14, 461, 12], [434, 15, 461, 13, "index"], [434, 20, 461, 18], [434, 23, 461, 21], [434, 24, 461, 22], [434, 25, 461, 23], [434, 28, 461, 26, "b"], [434, 29, 461, 27], [434, 32, 461, 30], [434, 33, 461, 31], [435, 8, 462, 6], [436, 6, 463, 4], [437, 4, 464, 2], [437, 5, 464, 3], [438, 4, 466, 2], [438, 10, 466, 8, "applyFallbackFaceBlur"], [438, 31, 466, 29], [438, 34, 466, 32, "applyFallbackFaceBlur"], [438, 35, 466, 33, "ctx"], [438, 38, 466, 62], [438, 40, 466, 64, "imgWidth"], [438, 48, 466, 80], [438, 50, 466, 82, "imgHeight"], [438, 59, 466, 99], [438, 64, 466, 104], [439, 6, 467, 4, "console"], [439, 13, 467, 11], [439, 14, 467, 12, "log"], [439, 17, 467, 15], [439, 18, 467, 16], [439, 90, 467, 88], [439, 91, 467, 89], [441, 6, 469, 4], [442, 6, 470, 4], [442, 12, 470, 10, "areas"], [442, 17, 470, 15], [442, 20, 470, 18], [443, 6, 471, 6], [444, 6, 472, 6], [445, 8, 472, 8, "x"], [445, 9, 472, 9], [445, 11, 472, 11, "imgWidth"], [445, 19, 472, 19], [445, 22, 472, 22], [445, 26, 472, 26], [446, 8, 472, 28, "y"], [446, 9, 472, 29], [446, 11, 472, 31, "imgHeight"], [446, 20, 472, 40], [446, 23, 472, 43], [446, 27, 472, 47], [447, 8, 472, 49, "w"], [447, 9, 472, 50], [447, 11, 472, 52, "imgWidth"], [447, 19, 472, 60], [447, 22, 472, 63], [447, 25, 472, 66], [448, 8, 472, 68, "h"], [448, 9, 472, 69], [448, 11, 472, 71, "imgHeight"], [448, 20, 472, 80], [448, 23, 472, 83], [449, 6, 472, 87], [449, 7, 472, 88], [450, 6, 473, 6], [451, 6, 474, 6], [452, 8, 474, 8, "x"], [452, 9, 474, 9], [452, 11, 474, 11, "imgWidth"], [452, 19, 474, 19], [452, 22, 474, 22], [452, 25, 474, 25], [453, 8, 474, 27, "y"], [453, 9, 474, 28], [453, 11, 474, 30, "imgHeight"], [453, 20, 474, 39], [453, 23, 474, 42], [453, 26, 474, 45], [454, 8, 474, 47, "w"], [454, 9, 474, 48], [454, 11, 474, 50, "imgWidth"], [454, 19, 474, 58], [454, 22, 474, 61], [454, 26, 474, 65], [455, 8, 474, 67, "h"], [455, 9, 474, 68], [455, 11, 474, 70, "imgHeight"], [455, 20, 474, 79], [455, 23, 474, 82], [456, 6, 474, 86], [456, 7, 474, 87], [457, 6, 475, 6], [458, 6, 476, 6], [459, 8, 476, 8, "x"], [459, 9, 476, 9], [459, 11, 476, 11, "imgWidth"], [459, 19, 476, 19], [459, 22, 476, 22], [459, 26, 476, 26], [460, 8, 476, 28, "y"], [460, 9, 476, 29], [460, 11, 476, 31, "imgHeight"], [460, 20, 476, 40], [460, 23, 476, 43], [460, 26, 476, 46], [461, 8, 476, 48, "w"], [461, 9, 476, 49], [461, 11, 476, 51, "imgWidth"], [461, 19, 476, 59], [461, 22, 476, 62], [461, 26, 476, 66], [462, 8, 476, 68, "h"], [462, 9, 476, 69], [462, 11, 476, 71, "imgHeight"], [462, 20, 476, 80], [462, 23, 476, 83], [463, 6, 476, 87], [463, 7, 476, 88], [463, 8, 477, 5], [464, 6, 479, 4, "areas"], [464, 11, 479, 9], [464, 12, 479, 10, "for<PERSON>ach"], [464, 19, 479, 17], [464, 20, 479, 18], [464, 21, 479, 19, "area"], [464, 25, 479, 23], [464, 27, 479, 25, "index"], [464, 32, 479, 30], [464, 37, 479, 35], [465, 8, 480, 6, "console"], [465, 15, 480, 13], [465, 16, 480, 14, "log"], [465, 19, 480, 17], [465, 20, 480, 18], [465, 65, 480, 63, "index"], [465, 70, 480, 68], [465, 73, 480, 71], [465, 74, 480, 72], [465, 77, 480, 75], [465, 79, 480, 77, "area"], [465, 83, 480, 81], [465, 84, 480, 82], [466, 8, 481, 6, "applyStrongBlur"], [466, 23, 481, 21], [466, 24, 481, 22, "ctx"], [466, 27, 481, 25], [466, 29, 481, 27, "area"], [466, 33, 481, 31], [466, 34, 481, 32, "x"], [466, 35, 481, 33], [466, 37, 481, 35, "area"], [466, 41, 481, 39], [466, 42, 481, 40, "y"], [466, 43, 481, 41], [466, 45, 481, 43, "area"], [466, 49, 481, 47], [466, 50, 481, 48, "w"], [466, 51, 481, 49], [466, 53, 481, 51, "area"], [466, 57, 481, 55], [466, 58, 481, 56, "h"], [466, 59, 481, 57], [466, 60, 481, 58], [467, 6, 482, 4], [467, 7, 482, 5], [467, 8, 482, 6], [468, 4, 483, 2], [468, 5, 483, 3], [470, 4, 485, 2], [471, 4, 486, 2], [471, 10, 486, 8, "capturePhoto"], [471, 22, 486, 20], [471, 25, 486, 23], [471, 29, 486, 23, "useCallback"], [471, 47, 486, 34], [471, 49, 486, 35], [471, 61, 486, 47], [472, 6, 487, 4], [473, 6, 488, 4], [473, 12, 488, 10, "isDev"], [473, 17, 488, 15], [473, 20, 488, 18, "process"], [473, 27, 488, 25], [473, 28, 488, 26, "env"], [473, 31, 488, 29], [473, 32, 488, 30, "NODE_ENV"], [473, 40, 488, 38], [473, 45, 488, 43], [473, 58, 488, 56], [473, 62, 488, 60, "__DEV__"], [473, 69, 488, 67], [474, 6, 490, 4], [474, 10, 490, 8], [474, 11, 490, 9, "cameraRef"], [474, 20, 490, 18], [474, 21, 490, 19, "current"], [474, 28, 490, 26], [474, 32, 490, 30], [474, 33, 490, 31, "isDev"], [474, 38, 490, 36], [474, 40, 490, 38], [475, 8, 491, 6, "<PERSON><PERSON>"], [475, 22, 491, 11], [475, 23, 491, 12, "alert"], [475, 28, 491, 17], [475, 29, 491, 18], [475, 36, 491, 25], [475, 38, 491, 27], [475, 56, 491, 45], [475, 57, 491, 46], [476, 8, 492, 6], [477, 6, 493, 4], [478, 6, 494, 4], [478, 10, 494, 8], [479, 8, 495, 6, "setProcessingState"], [479, 26, 495, 24], [479, 27, 495, 25], [479, 38, 495, 36], [479, 39, 495, 37], [480, 8, 496, 6, "setProcessingProgress"], [480, 29, 496, 27], [480, 30, 496, 28], [480, 32, 496, 30], [480, 33, 496, 31], [481, 8, 497, 6], [482, 8, 498, 6], [483, 8, 499, 6], [484, 8, 500, 6], [484, 14, 500, 12], [484, 18, 500, 16, "Promise"], [484, 25, 500, 23], [484, 26, 500, 24, "resolve"], [484, 33, 500, 31], [484, 37, 500, 35, "setTimeout"], [484, 47, 500, 45], [484, 48, 500, 46, "resolve"], [484, 55, 500, 53], [484, 57, 500, 55], [484, 59, 500, 57], [484, 60, 500, 58], [484, 61, 500, 59], [485, 8, 501, 6], [486, 8, 502, 6], [486, 12, 502, 10, "photo"], [486, 17, 502, 15], [487, 8, 504, 6], [487, 12, 504, 10], [488, 10, 505, 8, "photo"], [488, 15, 505, 13], [488, 18, 505, 16], [488, 24, 505, 22, "cameraRef"], [488, 33, 505, 31], [488, 34, 505, 32, "current"], [488, 41, 505, 39], [488, 42, 505, 40, "takePictureAsync"], [488, 58, 505, 56], [488, 59, 505, 57], [489, 12, 506, 10, "quality"], [489, 19, 506, 17], [489, 21, 506, 19], [489, 24, 506, 22], [490, 12, 507, 10, "base64"], [490, 18, 507, 16], [490, 20, 507, 18], [490, 25, 507, 23], [491, 12, 508, 10, "skipProcessing"], [491, 26, 508, 24], [491, 28, 508, 26], [491, 32, 508, 30], [491, 33, 508, 32], [492, 10, 509, 8], [492, 11, 509, 9], [492, 12, 509, 10], [493, 8, 510, 6], [493, 9, 510, 7], [493, 10, 510, 8], [493, 17, 510, 15, "cameraError"], [493, 28, 510, 26], [493, 30, 510, 28], [494, 10, 511, 8, "console"], [494, 17, 511, 15], [494, 18, 511, 16, "log"], [494, 21, 511, 19], [494, 22, 511, 20], [494, 82, 511, 80], [494, 84, 511, 82, "cameraError"], [494, 95, 511, 93], [494, 96, 511, 94], [495, 10, 512, 8], [496, 10, 513, 8], [496, 14, 513, 12, "isDev"], [496, 19, 513, 17], [496, 21, 513, 19], [497, 12, 514, 10, "photo"], [497, 17, 514, 15], [497, 20, 514, 18], [498, 14, 515, 12, "uri"], [498, 17, 515, 15], [498, 19, 515, 17], [499, 12, 516, 10], [499, 13, 516, 11], [500, 10, 517, 8], [500, 11, 517, 9], [500, 17, 517, 15], [501, 12, 518, 10], [501, 18, 518, 16, "cameraError"], [501, 29, 518, 27], [502, 10, 519, 8], [503, 8, 520, 6], [504, 8, 521, 6], [504, 12, 521, 10], [504, 13, 521, 11, "photo"], [504, 18, 521, 16], [504, 20, 521, 18], [505, 10, 522, 8], [505, 16, 522, 14], [505, 20, 522, 18, "Error"], [505, 25, 522, 23], [505, 26, 522, 24], [505, 51, 522, 49], [505, 52, 522, 50], [506, 8, 523, 6], [507, 8, 524, 6, "console"], [507, 15, 524, 13], [507, 16, 524, 14, "log"], [507, 19, 524, 17], [507, 20, 524, 18], [507, 56, 524, 54], [507, 58, 524, 56, "photo"], [507, 63, 524, 61], [507, 64, 524, 62, "uri"], [507, 67, 524, 65], [507, 68, 524, 66], [508, 8, 525, 6, "setCapturedPhoto"], [508, 24, 525, 22], [508, 25, 525, 23, "photo"], [508, 30, 525, 28], [508, 31, 525, 29, "uri"], [508, 34, 525, 32], [508, 35, 525, 33], [509, 8, 526, 6, "setProcessingProgress"], [509, 29, 526, 27], [509, 30, 526, 28], [509, 32, 526, 30], [509, 33, 526, 31], [510, 8, 527, 6], [511, 8, 528, 6, "console"], [511, 15, 528, 13], [511, 16, 528, 14, "log"], [511, 19, 528, 17], [511, 20, 528, 18], [511, 73, 528, 71], [511, 74, 528, 72], [512, 8, 529, 6], [512, 14, 529, 12, "processImageWithFaceBlur"], [512, 38, 529, 36], [512, 39, 529, 37, "photo"], [512, 44, 529, 42], [512, 45, 529, 43, "uri"], [512, 48, 529, 46], [512, 49, 529, 47], [513, 8, 530, 6, "console"], [513, 15, 530, 13], [513, 16, 530, 14, "log"], [513, 19, 530, 17], [513, 20, 530, 18], [513, 71, 530, 69], [513, 72, 530, 70], [514, 6, 531, 4], [514, 7, 531, 5], [514, 8, 531, 6], [514, 15, 531, 13, "error"], [514, 20, 531, 18], [514, 22, 531, 20], [515, 8, 532, 6, "console"], [515, 15, 532, 13], [515, 16, 532, 14, "error"], [515, 21, 532, 19], [515, 22, 532, 20], [515, 54, 532, 52], [515, 56, 532, 54, "error"], [515, 61, 532, 59], [515, 62, 532, 60], [516, 8, 533, 6, "setErrorMessage"], [516, 23, 533, 21], [516, 24, 533, 22], [516, 68, 533, 66], [516, 69, 533, 67], [517, 8, 534, 6, "setProcessingState"], [517, 26, 534, 24], [517, 27, 534, 25], [517, 34, 534, 32], [517, 35, 534, 33], [518, 6, 535, 4], [519, 4, 536, 2], [519, 5, 536, 3], [519, 7, 536, 5], [519, 9, 536, 7], [519, 10, 536, 8], [520, 4, 537, 2], [521, 4, 538, 2], [521, 10, 538, 8, "processImageWithFaceBlur"], [521, 34, 538, 32], [521, 37, 538, 35], [521, 43, 538, 42, "photoUri"], [521, 51, 538, 58], [521, 55, 538, 63], [522, 6, 539, 4], [522, 10, 539, 8], [523, 8, 540, 6, "console"], [523, 15, 540, 13], [523, 16, 540, 14, "log"], [523, 19, 540, 17], [523, 20, 540, 18], [523, 84, 540, 82], [523, 85, 540, 83], [524, 8, 541, 6, "setProcessingState"], [524, 26, 541, 24], [524, 27, 541, 25], [524, 39, 541, 37], [524, 40, 541, 38], [525, 8, 542, 6, "setProcessingProgress"], [525, 29, 542, 27], [525, 30, 542, 28], [525, 32, 542, 30], [525, 33, 542, 31], [527, 8, 544, 6], [528, 8, 545, 6], [528, 14, 545, 12, "canvas"], [528, 20, 545, 18], [528, 23, 545, 21, "document"], [528, 31, 545, 29], [528, 32, 545, 30, "createElement"], [528, 45, 545, 43], [528, 46, 545, 44], [528, 54, 545, 52], [528, 55, 545, 53], [529, 8, 546, 6], [529, 14, 546, 12, "ctx"], [529, 17, 546, 15], [529, 20, 546, 18, "canvas"], [529, 26, 546, 24], [529, 27, 546, 25, "getContext"], [529, 37, 546, 35], [529, 38, 546, 36], [529, 42, 546, 40], [529, 43, 546, 41], [530, 8, 547, 6], [530, 12, 547, 10], [530, 13, 547, 11, "ctx"], [530, 16, 547, 14], [530, 18, 547, 16], [530, 24, 547, 22], [530, 28, 547, 26, "Error"], [530, 33, 547, 31], [530, 34, 547, 32], [530, 64, 547, 62], [530, 65, 547, 63], [532, 8, 549, 6], [533, 8, 550, 6], [533, 14, 550, 12, "img"], [533, 17, 550, 15], [533, 20, 550, 18], [533, 24, 550, 22, "Image"], [533, 29, 550, 27], [533, 30, 550, 28], [533, 31, 550, 29], [534, 8, 551, 6], [534, 14, 551, 12], [534, 18, 551, 16, "Promise"], [534, 25, 551, 23], [534, 26, 551, 24], [534, 27, 551, 25, "resolve"], [534, 34, 551, 32], [534, 36, 551, 34, "reject"], [534, 42, 551, 40], [534, 47, 551, 45], [535, 10, 552, 8, "img"], [535, 13, 552, 11], [535, 14, 552, 12, "onload"], [535, 20, 552, 18], [535, 23, 552, 21, "resolve"], [535, 30, 552, 28], [536, 10, 553, 8, "img"], [536, 13, 553, 11], [536, 14, 553, 12, "onerror"], [536, 21, 553, 19], [536, 24, 553, 22, "reject"], [536, 30, 553, 28], [537, 10, 554, 8, "img"], [537, 13, 554, 11], [537, 14, 554, 12, "src"], [537, 17, 554, 15], [537, 20, 554, 18, "photoUri"], [537, 28, 554, 26], [538, 8, 555, 6], [538, 9, 555, 7], [538, 10, 555, 8], [540, 8, 557, 6], [541, 8, 558, 6, "canvas"], [541, 14, 558, 12], [541, 15, 558, 13, "width"], [541, 20, 558, 18], [541, 23, 558, 21, "img"], [541, 26, 558, 24], [541, 27, 558, 25, "width"], [541, 32, 558, 30], [542, 8, 559, 6, "canvas"], [542, 14, 559, 12], [542, 15, 559, 13, "height"], [542, 21, 559, 19], [542, 24, 559, 22, "img"], [542, 27, 559, 25], [542, 28, 559, 26, "height"], [542, 34, 559, 32], [543, 8, 560, 6, "console"], [543, 15, 560, 13], [543, 16, 560, 14, "log"], [543, 19, 560, 17], [543, 20, 560, 18], [543, 54, 560, 52], [543, 56, 560, 54], [544, 10, 560, 56, "width"], [544, 15, 560, 61], [544, 17, 560, 63, "img"], [544, 20, 560, 66], [544, 21, 560, 67, "width"], [544, 26, 560, 72], [545, 10, 560, 74, "height"], [545, 16, 560, 80], [545, 18, 560, 82, "img"], [545, 21, 560, 85], [545, 22, 560, 86, "height"], [546, 8, 560, 93], [546, 9, 560, 94], [546, 10, 560, 95], [548, 8, 562, 6], [549, 8, 563, 6, "ctx"], [549, 11, 563, 9], [549, 12, 563, 10, "drawImage"], [549, 21, 563, 19], [549, 22, 563, 20, "img"], [549, 25, 563, 23], [549, 27, 563, 25], [549, 28, 563, 26], [549, 30, 563, 28], [549, 31, 563, 29], [549, 32, 563, 30], [550, 8, 564, 6, "console"], [550, 15, 564, 13], [550, 16, 564, 14, "log"], [550, 19, 564, 17], [550, 20, 564, 18], [550, 72, 564, 70], [550, 73, 564, 71], [551, 8, 566, 6, "setProcessingProgress"], [551, 29, 566, 27], [551, 30, 566, 28], [551, 32, 566, 30], [551, 33, 566, 31], [553, 8, 568, 6], [554, 8, 569, 6], [554, 12, 569, 10, "detectedFaces"], [554, 25, 569, 23], [554, 28, 569, 26], [554, 30, 569, 28], [555, 8, 571, 6, "console"], [555, 15, 571, 13], [555, 16, 571, 14, "log"], [555, 19, 571, 17], [555, 20, 571, 18], [555, 81, 571, 79], [555, 82, 571, 80], [557, 8, 573, 6], [558, 8, 574, 6], [558, 12, 574, 10], [559, 10, 575, 8], [559, 16, 575, 14, "loadTensorFlowFaceDetection"], [559, 43, 575, 41], [559, 44, 575, 42], [559, 45, 575, 43], [560, 10, 576, 8, "detectedFaces"], [560, 23, 576, 21], [560, 26, 576, 24], [560, 32, 576, 30, "detectFacesWithTensorFlow"], [560, 57, 576, 55], [560, 58, 576, 56, "img"], [560, 61, 576, 59], [560, 62, 576, 60], [561, 10, 577, 8, "console"], [561, 17, 577, 15], [561, 18, 577, 16, "log"], [561, 21, 577, 19], [561, 22, 577, 20], [561, 70, 577, 68, "detectedFaces"], [561, 83, 577, 81], [561, 84, 577, 82, "length"], [561, 90, 577, 88], [561, 98, 577, 96], [561, 99, 577, 97], [562, 8, 578, 6], [562, 9, 578, 7], [562, 10, 578, 8], [562, 17, 578, 15, "tensorFlowError"], [562, 32, 578, 30], [562, 34, 578, 32], [563, 10, 579, 8, "console"], [563, 17, 579, 15], [563, 18, 579, 16, "warn"], [563, 22, 579, 20], [563, 23, 579, 21], [563, 61, 579, 59], [563, 63, 579, 61, "tensorFlowError"], [563, 78, 579, 76], [563, 79, 579, 77], [565, 10, 581, 8], [566, 10, 582, 8, "console"], [566, 17, 582, 15], [566, 18, 582, 16, "log"], [566, 21, 582, 19], [566, 22, 582, 20], [566, 86, 582, 84], [566, 87, 582, 85], [567, 10, 583, 8, "detectedFaces"], [567, 23, 583, 21], [567, 26, 583, 24, "detectFacesHeuristic"], [567, 46, 583, 44], [567, 47, 583, 45, "img"], [567, 50, 583, 48], [567, 52, 583, 50, "ctx"], [567, 55, 583, 53], [567, 56, 583, 54], [568, 10, 584, 8, "console"], [568, 17, 584, 15], [568, 18, 584, 16, "log"], [568, 21, 584, 19], [568, 22, 584, 20], [568, 70, 584, 68, "detectedFaces"], [568, 83, 584, 81], [568, 84, 584, 82, "length"], [568, 90, 584, 88], [568, 98, 584, 96], [568, 99, 584, 97], [569, 8, 585, 6], [571, 8, 587, 6], [572, 8, 588, 6], [572, 12, 588, 10, "detectedFaces"], [572, 25, 588, 23], [572, 26, 588, 24, "length"], [572, 32, 588, 30], [572, 37, 588, 35], [572, 38, 588, 36], [572, 40, 588, 38], [573, 10, 589, 8, "console"], [573, 17, 589, 15], [573, 18, 589, 16, "log"], [573, 21, 589, 19], [573, 22, 589, 20], [573, 89, 589, 87], [573, 90, 589, 88], [574, 10, 590, 8, "detectedFaces"], [574, 23, 590, 21], [574, 26, 590, 24, "detectFacesAggressive"], [574, 47, 590, 45], [574, 48, 590, 46, "img"], [574, 51, 590, 49], [574, 53, 590, 51, "ctx"], [574, 56, 590, 54], [574, 57, 590, 55], [575, 10, 591, 8, "console"], [575, 17, 591, 15], [575, 18, 591, 16, "log"], [575, 21, 591, 19], [575, 22, 591, 20], [575, 71, 591, 69, "detectedFaces"], [575, 84, 591, 82], [575, 85, 591, 83, "length"], [575, 91, 591, 89], [575, 99, 591, 97], [575, 100, 591, 98], [576, 8, 592, 6], [577, 8, 594, 6, "console"], [577, 15, 594, 13], [577, 16, 594, 14, "log"], [577, 19, 594, 17], [577, 20, 594, 18], [577, 72, 594, 70, "detectedFaces"], [577, 85, 594, 83], [577, 86, 594, 84, "length"], [577, 92, 594, 90], [577, 100, 594, 98], [577, 101, 594, 99], [578, 8, 595, 6], [578, 12, 595, 10, "detectedFaces"], [578, 25, 595, 23], [578, 26, 595, 24, "length"], [578, 32, 595, 30], [578, 35, 595, 33], [578, 36, 595, 34], [578, 38, 595, 36], [579, 10, 596, 8, "console"], [579, 17, 596, 15], [579, 18, 596, 16, "log"], [579, 21, 596, 19], [579, 22, 596, 20], [579, 66, 596, 64], [579, 68, 596, 66, "detectedFaces"], [579, 81, 596, 79], [579, 82, 596, 80, "map"], [579, 85, 596, 83], [579, 86, 596, 84], [579, 87, 596, 85, "face"], [579, 91, 596, 89], [579, 93, 596, 91, "i"], [579, 94, 596, 92], [579, 100, 596, 98], [580, 12, 597, 10, "faceNumber"], [580, 22, 597, 20], [580, 24, 597, 22, "i"], [580, 25, 597, 23], [580, 28, 597, 26], [580, 29, 597, 27], [581, 12, 598, 10, "centerX"], [581, 19, 598, 17], [581, 21, 598, 19, "face"], [581, 25, 598, 23], [581, 26, 598, 24, "boundingBox"], [581, 37, 598, 35], [581, 38, 598, 36, "xCenter"], [581, 45, 598, 43], [582, 12, 599, 10, "centerY"], [582, 19, 599, 17], [582, 21, 599, 19, "face"], [582, 25, 599, 23], [582, 26, 599, 24, "boundingBox"], [582, 37, 599, 35], [582, 38, 599, 36, "yCenter"], [582, 45, 599, 43], [583, 12, 600, 10, "width"], [583, 17, 600, 15], [583, 19, 600, 17, "face"], [583, 23, 600, 21], [583, 24, 600, 22, "boundingBox"], [583, 35, 600, 33], [583, 36, 600, 34, "width"], [583, 41, 600, 39], [584, 12, 601, 10, "height"], [584, 18, 601, 16], [584, 20, 601, 18, "face"], [584, 24, 601, 22], [584, 25, 601, 23, "boundingBox"], [584, 36, 601, 34], [584, 37, 601, 35, "height"], [585, 10, 602, 8], [585, 11, 602, 9], [585, 12, 602, 10], [585, 13, 602, 11], [585, 14, 602, 12], [586, 8, 603, 6], [586, 9, 603, 7], [586, 15, 603, 13], [587, 10, 604, 8, "console"], [587, 17, 604, 15], [587, 18, 604, 16, "log"], [587, 21, 604, 19], [587, 22, 604, 20], [587, 91, 604, 89], [587, 92, 604, 90], [588, 8, 605, 6], [589, 8, 607, 6, "setProcessingProgress"], [589, 29, 607, 27], [589, 30, 607, 28], [589, 32, 607, 30], [589, 33, 607, 31], [591, 8, 609, 6], [592, 8, 610, 6], [592, 12, 610, 10, "detectedFaces"], [592, 25, 610, 23], [592, 26, 610, 24, "length"], [592, 32, 610, 30], [592, 35, 610, 33], [592, 36, 610, 34], [592, 38, 610, 36], [593, 10, 611, 8, "console"], [593, 17, 611, 15], [593, 18, 611, 16, "log"], [593, 21, 611, 19], [593, 22, 611, 20], [593, 61, 611, 59, "detectedFaces"], [593, 74, 611, 72], [593, 75, 611, 73, "length"], [593, 81, 611, 79], [593, 101, 611, 99], [593, 102, 611, 100], [594, 10, 613, 8, "detectedFaces"], [594, 23, 613, 21], [594, 24, 613, 22, "for<PERSON>ach"], [594, 31, 613, 29], [594, 32, 613, 30], [594, 33, 613, 31, "detection"], [594, 42, 613, 40], [594, 44, 613, 42, "index"], [594, 49, 613, 47], [594, 54, 613, 52], [595, 12, 614, 10], [595, 18, 614, 16, "bbox"], [595, 22, 614, 20], [595, 25, 614, 23, "detection"], [595, 34, 614, 32], [595, 35, 614, 33, "boundingBox"], [595, 46, 614, 44], [597, 12, 616, 10], [598, 12, 617, 10], [598, 18, 617, 16, "faceX"], [598, 23, 617, 21], [598, 26, 617, 24, "bbox"], [598, 30, 617, 28], [598, 31, 617, 29, "xCenter"], [598, 38, 617, 36], [598, 41, 617, 39, "img"], [598, 44, 617, 42], [598, 45, 617, 43, "width"], [598, 50, 617, 48], [598, 53, 617, 52, "bbox"], [598, 57, 617, 56], [598, 58, 617, 57, "width"], [598, 63, 617, 62], [598, 66, 617, 65, "img"], [598, 69, 617, 68], [598, 70, 617, 69, "width"], [598, 75, 617, 74], [598, 78, 617, 78], [598, 79, 617, 79], [599, 12, 618, 10], [599, 18, 618, 16, "faceY"], [599, 23, 618, 21], [599, 26, 618, 24, "bbox"], [599, 30, 618, 28], [599, 31, 618, 29, "yCenter"], [599, 38, 618, 36], [599, 41, 618, 39, "img"], [599, 44, 618, 42], [599, 45, 618, 43, "height"], [599, 51, 618, 49], [599, 54, 618, 53, "bbox"], [599, 58, 618, 57], [599, 59, 618, 58, "height"], [599, 65, 618, 64], [599, 68, 618, 67, "img"], [599, 71, 618, 70], [599, 72, 618, 71, "height"], [599, 78, 618, 77], [599, 81, 618, 81], [599, 82, 618, 82], [600, 12, 619, 10], [600, 18, 619, 16, "faceWidth"], [600, 27, 619, 25], [600, 30, 619, 28, "bbox"], [600, 34, 619, 32], [600, 35, 619, 33, "width"], [600, 40, 619, 38], [600, 43, 619, 41, "img"], [600, 46, 619, 44], [600, 47, 619, 45, "width"], [600, 52, 619, 50], [601, 12, 620, 10], [601, 18, 620, 16, "faceHeight"], [601, 28, 620, 26], [601, 31, 620, 29, "bbox"], [601, 35, 620, 33], [601, 36, 620, 34, "height"], [601, 42, 620, 40], [601, 45, 620, 43, "img"], [601, 48, 620, 46], [601, 49, 620, 47, "height"], [601, 55, 620, 53], [603, 12, 622, 10], [604, 12, 623, 10], [604, 18, 623, 16, "padding"], [604, 25, 623, 23], [604, 28, 623, 26], [604, 31, 623, 29], [605, 12, 624, 10], [605, 18, 624, 16, "paddedX"], [605, 25, 624, 23], [605, 28, 624, 26, "Math"], [605, 32, 624, 30], [605, 33, 624, 31, "max"], [605, 36, 624, 34], [605, 37, 624, 35], [605, 38, 624, 36], [605, 40, 624, 38, "faceX"], [605, 45, 624, 43], [605, 48, 624, 46, "faceWidth"], [605, 57, 624, 55], [605, 60, 624, 58, "padding"], [605, 67, 624, 65], [605, 68, 624, 66], [606, 12, 625, 10], [606, 18, 625, 16, "paddedY"], [606, 25, 625, 23], [606, 28, 625, 26, "Math"], [606, 32, 625, 30], [606, 33, 625, 31, "max"], [606, 36, 625, 34], [606, 37, 625, 35], [606, 38, 625, 36], [606, 40, 625, 38, "faceY"], [606, 45, 625, 43], [606, 48, 625, 46, "faceHeight"], [606, 58, 625, 56], [606, 61, 625, 59, "padding"], [606, 68, 625, 66], [606, 69, 625, 67], [607, 12, 626, 10], [607, 18, 626, 16, "<PERSON><PERSON><PERSON><PERSON>"], [607, 29, 626, 27], [607, 32, 626, 30, "Math"], [607, 36, 626, 34], [607, 37, 626, 35, "min"], [607, 40, 626, 38], [607, 41, 626, 39, "img"], [607, 44, 626, 42], [607, 45, 626, 43, "width"], [607, 50, 626, 48], [607, 53, 626, 51, "paddedX"], [607, 60, 626, 58], [607, 62, 626, 60, "faceWidth"], [607, 71, 626, 69], [607, 75, 626, 73], [607, 76, 626, 74], [607, 79, 626, 77], [607, 80, 626, 78], [607, 83, 626, 81, "padding"], [607, 90, 626, 88], [607, 91, 626, 89], [607, 92, 626, 90], [608, 12, 627, 10], [608, 18, 627, 16, "paddedHeight"], [608, 30, 627, 28], [608, 33, 627, 31, "Math"], [608, 37, 627, 35], [608, 38, 627, 36, "min"], [608, 41, 627, 39], [608, 42, 627, 40, "img"], [608, 45, 627, 43], [608, 46, 627, 44, "height"], [608, 52, 627, 50], [608, 55, 627, 53, "paddedY"], [608, 62, 627, 60], [608, 64, 627, 62, "faceHeight"], [608, 74, 627, 72], [608, 78, 627, 76], [608, 79, 627, 77], [608, 82, 627, 80], [608, 83, 627, 81], [608, 86, 627, 84, "padding"], [608, 93, 627, 91], [608, 94, 627, 92], [608, 95, 627, 93], [609, 12, 629, 10, "console"], [609, 19, 629, 17], [609, 20, 629, 18, "log"], [609, 23, 629, 21], [609, 24, 629, 22], [609, 60, 629, 58, "index"], [609, 65, 629, 63], [609, 68, 629, 66], [609, 69, 629, 67], [609, 72, 629, 70], [609, 74, 629, 72], [610, 14, 630, 12, "original"], [610, 22, 630, 20], [610, 24, 630, 22], [611, 16, 630, 24, "x"], [611, 17, 630, 25], [611, 19, 630, 27, "Math"], [611, 23, 630, 31], [611, 24, 630, 32, "round"], [611, 29, 630, 37], [611, 30, 630, 38, "faceX"], [611, 35, 630, 43], [611, 36, 630, 44], [612, 16, 630, 46, "y"], [612, 17, 630, 47], [612, 19, 630, 49, "Math"], [612, 23, 630, 53], [612, 24, 630, 54, "round"], [612, 29, 630, 59], [612, 30, 630, 60, "faceY"], [612, 35, 630, 65], [612, 36, 630, 66], [613, 16, 630, 68, "w"], [613, 17, 630, 69], [613, 19, 630, 71, "Math"], [613, 23, 630, 75], [613, 24, 630, 76, "round"], [613, 29, 630, 81], [613, 30, 630, 82, "faceWidth"], [613, 39, 630, 91], [613, 40, 630, 92], [614, 16, 630, 94, "h"], [614, 17, 630, 95], [614, 19, 630, 97, "Math"], [614, 23, 630, 101], [614, 24, 630, 102, "round"], [614, 29, 630, 107], [614, 30, 630, 108, "faceHeight"], [614, 40, 630, 118], [615, 14, 630, 120], [615, 15, 630, 121], [616, 14, 631, 12, "padded"], [616, 20, 631, 18], [616, 22, 631, 20], [617, 16, 631, 22, "x"], [617, 17, 631, 23], [617, 19, 631, 25, "Math"], [617, 23, 631, 29], [617, 24, 631, 30, "round"], [617, 29, 631, 35], [617, 30, 631, 36, "paddedX"], [617, 37, 631, 43], [617, 38, 631, 44], [618, 16, 631, 46, "y"], [618, 17, 631, 47], [618, 19, 631, 49, "Math"], [618, 23, 631, 53], [618, 24, 631, 54, "round"], [618, 29, 631, 59], [618, 30, 631, 60, "paddedY"], [618, 37, 631, 67], [618, 38, 631, 68], [619, 16, 631, 70, "w"], [619, 17, 631, 71], [619, 19, 631, 73, "Math"], [619, 23, 631, 77], [619, 24, 631, 78, "round"], [619, 29, 631, 83], [619, 30, 631, 84, "<PERSON><PERSON><PERSON><PERSON>"], [619, 41, 631, 95], [619, 42, 631, 96], [620, 16, 631, 98, "h"], [620, 17, 631, 99], [620, 19, 631, 101, "Math"], [620, 23, 631, 105], [620, 24, 631, 106, "round"], [620, 29, 631, 111], [620, 30, 631, 112, "paddedHeight"], [620, 42, 631, 124], [621, 14, 631, 126], [622, 12, 632, 10], [622, 13, 632, 11], [622, 14, 632, 12], [624, 12, 634, 10], [625, 12, 635, 10, "console"], [625, 19, 635, 17], [625, 20, 635, 18, "log"], [625, 23, 635, 21], [625, 24, 635, 22], [625, 70, 635, 68], [625, 72, 635, 70], [626, 14, 636, 12, "width"], [626, 19, 636, 17], [626, 21, 636, 19, "canvas"], [626, 27, 636, 25], [626, 28, 636, 26, "width"], [626, 33, 636, 31], [627, 14, 637, 12, "height"], [627, 20, 637, 18], [627, 22, 637, 20, "canvas"], [627, 28, 637, 26], [627, 29, 637, 27, "height"], [627, 35, 637, 33], [628, 14, 638, 12, "contextValid"], [628, 26, 638, 24], [628, 28, 638, 26], [628, 29, 638, 27], [628, 30, 638, 28, "ctx"], [629, 12, 639, 10], [629, 13, 639, 11], [629, 14, 639, 12], [631, 12, 641, 10], [632, 12, 642, 10, "applyStrongBlur"], [632, 27, 642, 25], [632, 28, 642, 26, "ctx"], [632, 31, 642, 29], [632, 33, 642, 31, "paddedX"], [632, 40, 642, 38], [632, 42, 642, 40, "paddedY"], [632, 49, 642, 47], [632, 51, 642, 49, "<PERSON><PERSON><PERSON><PERSON>"], [632, 62, 642, 60], [632, 64, 642, 62, "paddedHeight"], [632, 76, 642, 74], [632, 77, 642, 75], [634, 12, 644, 10], [635, 12, 645, 10, "console"], [635, 19, 645, 17], [635, 20, 645, 18, "log"], [635, 23, 645, 21], [635, 24, 645, 22], [635, 102, 645, 100], [635, 103, 645, 101], [637, 12, 647, 10], [638, 12, 648, 10], [638, 18, 648, 16, "testImageData"], [638, 31, 648, 29], [638, 34, 648, 32, "ctx"], [638, 37, 648, 35], [638, 38, 648, 36, "getImageData"], [638, 50, 648, 48], [638, 51, 648, 49, "paddedX"], [638, 58, 648, 56], [638, 61, 648, 59], [638, 63, 648, 61], [638, 65, 648, 63, "paddedY"], [638, 72, 648, 70], [638, 75, 648, 73], [638, 77, 648, 75], [638, 79, 648, 77], [638, 81, 648, 79], [638, 83, 648, 81], [638, 85, 648, 83], [638, 86, 648, 84], [639, 12, 649, 10, "console"], [639, 19, 649, 17], [639, 20, 649, 18, "log"], [639, 23, 649, 21], [639, 24, 649, 22], [639, 70, 649, 68], [639, 72, 649, 70], [640, 14, 650, 12, "firstPixel"], [640, 24, 650, 22], [640, 26, 650, 24], [640, 27, 650, 25, "testImageData"], [640, 40, 650, 38], [640, 41, 650, 39, "data"], [640, 45, 650, 43], [640, 46, 650, 44], [640, 47, 650, 45], [640, 48, 650, 46], [640, 50, 650, 48, "testImageData"], [640, 63, 650, 61], [640, 64, 650, 62, "data"], [640, 68, 650, 66], [640, 69, 650, 67], [640, 70, 650, 68], [640, 71, 650, 69], [640, 73, 650, 71, "testImageData"], [640, 86, 650, 84], [640, 87, 650, 85, "data"], [640, 91, 650, 89], [640, 92, 650, 90], [640, 93, 650, 91], [640, 94, 650, 92], [640, 95, 650, 93], [641, 14, 651, 12, "secondPixel"], [641, 25, 651, 23], [641, 27, 651, 25], [641, 28, 651, 26, "testImageData"], [641, 41, 651, 39], [641, 42, 651, 40, "data"], [641, 46, 651, 44], [641, 47, 651, 45], [641, 48, 651, 46], [641, 49, 651, 47], [641, 51, 651, 49, "testImageData"], [641, 64, 651, 62], [641, 65, 651, 63, "data"], [641, 69, 651, 67], [641, 70, 651, 68], [641, 71, 651, 69], [641, 72, 651, 70], [641, 74, 651, 72, "testImageData"], [641, 87, 651, 85], [641, 88, 651, 86, "data"], [641, 92, 651, 90], [641, 93, 651, 91], [641, 94, 651, 92], [641, 95, 651, 93], [642, 12, 652, 10], [642, 13, 652, 11], [642, 14, 652, 12], [643, 12, 654, 10, "console"], [643, 19, 654, 17], [643, 20, 654, 18, "log"], [643, 23, 654, 21], [643, 24, 654, 22], [643, 50, 654, 48, "index"], [643, 55, 654, 53], [643, 58, 654, 56], [643, 59, 654, 57], [643, 79, 654, 77], [643, 80, 654, 78], [644, 10, 655, 8], [644, 11, 655, 9], [644, 12, 655, 10], [645, 10, 657, 8, "console"], [645, 17, 657, 15], [645, 18, 657, 16, "log"], [645, 21, 657, 19], [645, 22, 657, 20], [645, 48, 657, 46, "detectedFaces"], [645, 61, 657, 59], [645, 62, 657, 60, "length"], [645, 68, 657, 66], [645, 104, 657, 102], [645, 105, 657, 103], [646, 8, 658, 6], [646, 9, 658, 7], [646, 15, 658, 13], [647, 10, 659, 8, "console"], [647, 17, 659, 15], [647, 18, 659, 16, "log"], [647, 21, 659, 19], [647, 22, 659, 20], [647, 109, 659, 107], [647, 110, 659, 108], [648, 10, 660, 8], [649, 10, 661, 8, "applyFallbackFaceBlur"], [649, 31, 661, 29], [649, 32, 661, 30, "ctx"], [649, 35, 661, 33], [649, 37, 661, 35, "img"], [649, 40, 661, 38], [649, 41, 661, 39, "width"], [649, 46, 661, 44], [649, 48, 661, 46, "img"], [649, 51, 661, 49], [649, 52, 661, 50, "height"], [649, 58, 661, 56], [649, 59, 661, 57], [650, 8, 662, 6], [651, 8, 664, 6, "setProcessingProgress"], [651, 29, 664, 27], [651, 30, 664, 28], [651, 32, 664, 30], [651, 33, 664, 31], [653, 8, 666, 6], [654, 8, 667, 6, "console"], [654, 15, 667, 13], [654, 16, 667, 14, "log"], [654, 19, 667, 17], [654, 20, 667, 18], [654, 85, 667, 83], [654, 86, 667, 84], [655, 8, 668, 6], [655, 14, 668, 12, "blurredImageBlob"], [655, 30, 668, 28], [655, 33, 668, 31], [655, 39, 668, 37], [655, 43, 668, 41, "Promise"], [655, 50, 668, 48], [655, 51, 668, 56, "resolve"], [655, 58, 668, 63], [655, 62, 668, 68], [656, 10, 669, 8, "canvas"], [656, 16, 669, 14], [656, 17, 669, 15, "toBlob"], [656, 23, 669, 21], [656, 24, 669, 23, "blob"], [656, 28, 669, 27], [656, 32, 669, 32, "resolve"], [656, 39, 669, 39], [656, 40, 669, 40, "blob"], [656, 44, 669, 45], [656, 45, 669, 46], [656, 47, 669, 48], [656, 59, 669, 60], [656, 61, 669, 62], [656, 64, 669, 65], [656, 65, 669, 66], [657, 8, 670, 6], [657, 9, 670, 7], [657, 10, 670, 8], [658, 8, 672, 6], [658, 14, 672, 12, "blurredImageUrl"], [658, 29, 672, 27], [658, 32, 672, 30, "URL"], [658, 35, 672, 33], [658, 36, 672, 34, "createObjectURL"], [658, 51, 672, 49], [658, 52, 672, 50, "blurredImageBlob"], [658, 68, 672, 66], [658, 69, 672, 67], [659, 8, 673, 6, "console"], [659, 15, 673, 13], [659, 16, 673, 14, "log"], [659, 19, 673, 17], [659, 20, 673, 18], [659, 66, 673, 64], [659, 68, 673, 66, "blurredImageUrl"], [659, 83, 673, 81], [659, 84, 673, 82, "substring"], [659, 93, 673, 91], [659, 94, 673, 92], [659, 95, 673, 93], [659, 97, 673, 95], [659, 99, 673, 97], [659, 100, 673, 98], [659, 103, 673, 101], [659, 108, 673, 106], [659, 109, 673, 107], [661, 8, 675, 6], [662, 8, 676, 6, "setCapturedPhoto"], [662, 24, 676, 22], [662, 25, 676, 23, "blurredImageUrl"], [662, 40, 676, 38], [662, 41, 676, 39], [663, 8, 677, 6, "console"], [663, 15, 677, 13], [663, 16, 677, 14, "log"], [663, 19, 677, 17], [663, 20, 677, 18], [663, 87, 677, 85], [663, 88, 677, 86], [664, 8, 679, 6, "setProcessingProgress"], [664, 29, 679, 27], [664, 30, 679, 28], [664, 33, 679, 31], [664, 34, 679, 32], [666, 8, 681, 6], [667, 8, 682, 6], [667, 14, 682, 12, "completeProcessing"], [667, 32, 682, 30], [667, 33, 682, 31, "blurredImageUrl"], [667, 48, 682, 46], [667, 49, 682, 47], [668, 6, 684, 4], [668, 7, 684, 5], [668, 8, 684, 6], [668, 15, 684, 13, "error"], [668, 20, 684, 18], [668, 22, 684, 20], [669, 8, 685, 6, "console"], [669, 15, 685, 13], [669, 16, 685, 14, "error"], [669, 21, 685, 19], [669, 22, 685, 20], [669, 57, 685, 55], [669, 59, 685, 57, "error"], [669, 64, 685, 62], [669, 65, 685, 63], [670, 8, 686, 6, "setErrorMessage"], [670, 23, 686, 21], [670, 24, 686, 22], [670, 50, 686, 48], [670, 51, 686, 49], [671, 8, 687, 6, "setProcessingState"], [671, 26, 687, 24], [671, 27, 687, 25], [671, 34, 687, 32], [671, 35, 687, 33], [672, 6, 688, 4], [673, 4, 689, 2], [673, 5, 689, 3], [675, 4, 691, 2], [676, 4, 692, 2], [676, 10, 692, 8, "completeProcessing"], [676, 28, 692, 26], [676, 31, 692, 29], [676, 37, 692, 36, "blurredImageUrl"], [676, 52, 692, 59], [676, 56, 692, 64], [677, 6, 693, 4], [677, 10, 693, 8], [678, 8, 694, 6, "setProcessingState"], [678, 26, 694, 24], [678, 27, 694, 25], [678, 37, 694, 35], [678, 38, 694, 36], [680, 8, 696, 6], [681, 8, 697, 6], [681, 14, 697, 12, "timestamp"], [681, 23, 697, 21], [681, 26, 697, 24, "Date"], [681, 30, 697, 28], [681, 31, 697, 29, "now"], [681, 34, 697, 32], [681, 35, 697, 33], [681, 36, 697, 34], [682, 8, 698, 6], [682, 14, 698, 12, "result"], [682, 20, 698, 18], [682, 23, 698, 21], [683, 10, 699, 8, "imageUrl"], [683, 18, 699, 16], [683, 20, 699, 18, "blurredImageUrl"], [683, 35, 699, 33], [684, 10, 700, 8, "localUri"], [684, 18, 700, 16], [684, 20, 700, 18, "blurredImageUrl"], [684, 35, 700, 33], [685, 10, 701, 8, "challengeCode"], [685, 23, 701, 21], [685, 25, 701, 23, "challengeCode"], [685, 38, 701, 36], [685, 42, 701, 40], [685, 44, 701, 42], [686, 10, 702, 8, "timestamp"], [686, 19, 702, 17], [687, 10, 703, 8, "jobId"], [687, 15, 703, 13], [687, 17, 703, 15], [687, 27, 703, 25, "timestamp"], [687, 36, 703, 34], [687, 38, 703, 36], [688, 10, 704, 8, "status"], [688, 16, 704, 14], [688, 18, 704, 16], [689, 8, 705, 6], [689, 9, 705, 7], [690, 8, 707, 6, "console"], [690, 15, 707, 13], [690, 16, 707, 14, "log"], [690, 19, 707, 17], [690, 20, 707, 18], [690, 100, 707, 98], [690, 102, 707, 100], [691, 10, 708, 8, "imageUrl"], [691, 18, 708, 16], [691, 20, 708, 18, "blurredImageUrl"], [691, 35, 708, 33], [691, 36, 708, 34, "substring"], [691, 45, 708, 43], [691, 46, 708, 44], [691, 47, 708, 45], [691, 49, 708, 47], [691, 51, 708, 49], [691, 52, 708, 50], [691, 55, 708, 53], [691, 60, 708, 58], [692, 10, 709, 8, "timestamp"], [692, 19, 709, 17], [693, 10, 710, 8, "jobId"], [693, 15, 710, 13], [693, 17, 710, 15, "result"], [693, 23, 710, 21], [693, 24, 710, 22, "jobId"], [694, 8, 711, 6], [694, 9, 711, 7], [694, 10, 711, 8], [696, 8, 713, 6], [697, 8, 714, 6, "onComplete"], [697, 18, 714, 16], [697, 19, 714, 17, "result"], [697, 25, 714, 23], [697, 26, 714, 24], [698, 6, 716, 4], [698, 7, 716, 5], [698, 8, 716, 6], [698, 15, 716, 13, "error"], [698, 20, 716, 18], [698, 22, 716, 20], [699, 8, 717, 6, "console"], [699, 15, 717, 13], [699, 16, 717, 14, "error"], [699, 21, 717, 19], [699, 22, 717, 20], [699, 57, 717, 55], [699, 59, 717, 57, "error"], [699, 64, 717, 62], [699, 65, 717, 63], [700, 8, 718, 6, "setErrorMessage"], [700, 23, 718, 21], [700, 24, 718, 22], [700, 56, 718, 54], [700, 57, 718, 55], [701, 8, 719, 6, "setProcessingState"], [701, 26, 719, 24], [701, 27, 719, 25], [701, 34, 719, 32], [701, 35, 719, 33], [702, 6, 720, 4], [703, 4, 721, 2], [703, 5, 721, 3], [705, 4, 723, 2], [706, 4, 724, 2], [706, 10, 724, 8, "triggerServerProcessing"], [706, 33, 724, 31], [706, 36, 724, 34], [706, 42, 724, 34, "triggerServerProcessing"], [706, 43, 724, 41, "privateImageUrl"], [706, 58, 724, 64], [706, 60, 724, 66, "timestamp"], [706, 69, 724, 83], [706, 74, 724, 88], [707, 6, 725, 4], [707, 10, 725, 8], [708, 8, 726, 6, "console"], [708, 15, 726, 13], [708, 16, 726, 14, "log"], [708, 19, 726, 17], [708, 20, 726, 18], [708, 74, 726, 72], [708, 76, 726, 74, "privateImageUrl"], [708, 91, 726, 89], [708, 92, 726, 90], [709, 8, 727, 6, "setProcessingState"], [709, 26, 727, 24], [709, 27, 727, 25], [709, 39, 727, 37], [709, 40, 727, 38], [710, 8, 728, 6, "setProcessingProgress"], [710, 29, 728, 27], [710, 30, 728, 28], [710, 32, 728, 30], [710, 33, 728, 31], [711, 8, 730, 6], [711, 14, 730, 12, "requestBody"], [711, 25, 730, 23], [711, 28, 730, 26], [712, 10, 731, 8, "imageUrl"], [712, 18, 731, 16], [712, 20, 731, 18, "privateImageUrl"], [712, 35, 731, 33], [713, 10, 732, 8, "userId"], [713, 16, 732, 14], [714, 10, 733, 8, "requestId"], [714, 19, 733, 17], [715, 10, 734, 8, "timestamp"], [715, 19, 734, 17], [716, 10, 735, 8, "platform"], [716, 18, 735, 16], [716, 20, 735, 18], [717, 8, 736, 6], [717, 9, 736, 7], [718, 8, 738, 6, "console"], [718, 15, 738, 13], [718, 16, 738, 14, "log"], [718, 19, 738, 17], [718, 20, 738, 18], [718, 65, 738, 63], [718, 67, 738, 65, "requestBody"], [718, 78, 738, 76], [718, 79, 738, 77], [720, 8, 740, 6], [721, 8, 741, 6], [721, 14, 741, 12, "response"], [721, 22, 741, 20], [721, 25, 741, 23], [721, 31, 741, 29, "fetch"], [721, 36, 741, 34], [721, 37, 741, 35], [721, 40, 741, 38, "API_BASE_URL"], [721, 52, 741, 50], [721, 72, 741, 70], [721, 74, 741, 72], [722, 10, 742, 8, "method"], [722, 16, 742, 14], [722, 18, 742, 16], [722, 24, 742, 22], [723, 10, 743, 8, "headers"], [723, 17, 743, 15], [723, 19, 743, 17], [724, 12, 744, 10], [724, 26, 744, 24], [724, 28, 744, 26], [724, 46, 744, 44], [725, 12, 745, 10], [725, 27, 745, 25], [725, 29, 745, 27], [725, 39, 745, 37], [725, 45, 745, 43, "getAuthToken"], [725, 57, 745, 55], [725, 58, 745, 56], [725, 59, 745, 57], [726, 10, 746, 8], [726, 11, 746, 9], [727, 10, 747, 8, "body"], [727, 14, 747, 12], [727, 16, 747, 14, "JSON"], [727, 20, 747, 18], [727, 21, 747, 19, "stringify"], [727, 30, 747, 28], [727, 31, 747, 29, "requestBody"], [727, 42, 747, 40], [728, 8, 748, 6], [728, 9, 748, 7], [728, 10, 748, 8], [729, 8, 750, 6], [729, 12, 750, 10], [729, 13, 750, 11, "response"], [729, 21, 750, 19], [729, 22, 750, 20, "ok"], [729, 24, 750, 22], [729, 26, 750, 24], [730, 10, 751, 8], [730, 16, 751, 14, "errorText"], [730, 25, 751, 23], [730, 28, 751, 26], [730, 34, 751, 32, "response"], [730, 42, 751, 40], [730, 43, 751, 41, "text"], [730, 47, 751, 45], [730, 48, 751, 46], [730, 49, 751, 47], [731, 10, 752, 8, "console"], [731, 17, 752, 15], [731, 18, 752, 16, "error"], [731, 23, 752, 21], [731, 24, 752, 22], [731, 68, 752, 66], [731, 70, 752, 68, "response"], [731, 78, 752, 76], [731, 79, 752, 77, "status"], [731, 85, 752, 83], [731, 87, 752, 85, "errorText"], [731, 96, 752, 94], [731, 97, 752, 95], [732, 10, 753, 8], [732, 16, 753, 14], [732, 20, 753, 18, "Error"], [732, 25, 753, 23], [732, 26, 753, 24], [732, 48, 753, 46, "response"], [732, 56, 753, 54], [732, 57, 753, 55, "status"], [732, 63, 753, 61], [732, 67, 753, 65, "response"], [732, 75, 753, 73], [732, 76, 753, 74, "statusText"], [732, 86, 753, 84], [732, 88, 753, 86], [732, 89, 753, 87], [733, 8, 754, 6], [734, 8, 756, 6], [734, 14, 756, 12, "result"], [734, 20, 756, 18], [734, 23, 756, 21], [734, 29, 756, 27, "response"], [734, 37, 756, 35], [734, 38, 756, 36, "json"], [734, 42, 756, 40], [734, 43, 756, 41], [734, 44, 756, 42], [735, 8, 757, 6, "console"], [735, 15, 757, 13], [735, 16, 757, 14, "log"], [735, 19, 757, 17], [735, 20, 757, 18], [735, 68, 757, 66], [735, 70, 757, 68, "result"], [735, 76, 757, 74], [735, 77, 757, 75], [736, 8, 759, 6], [736, 12, 759, 10], [736, 13, 759, 11, "result"], [736, 19, 759, 17], [736, 20, 759, 18, "jobId"], [736, 25, 759, 23], [736, 27, 759, 25], [737, 10, 760, 8], [737, 16, 760, 14], [737, 20, 760, 18, "Error"], [737, 25, 760, 23], [737, 26, 760, 24], [737, 70, 760, 68], [737, 71, 760, 69], [738, 8, 761, 6], [740, 8, 763, 6], [741, 8, 764, 6], [741, 14, 764, 12, "pollForCompletion"], [741, 31, 764, 29], [741, 32, 764, 30, "result"], [741, 38, 764, 36], [741, 39, 764, 37, "jobId"], [741, 44, 764, 42], [741, 46, 764, 44, "timestamp"], [741, 55, 764, 53], [741, 56, 764, 54], [742, 6, 765, 4], [742, 7, 765, 5], [742, 8, 765, 6], [742, 15, 765, 13, "error"], [742, 20, 765, 18], [742, 22, 765, 20], [743, 8, 766, 6, "console"], [743, 15, 766, 13], [743, 16, 766, 14, "error"], [743, 21, 766, 19], [743, 22, 766, 20], [743, 57, 766, 55], [743, 59, 766, 57, "error"], [743, 64, 766, 62], [743, 65, 766, 63], [744, 8, 767, 6, "setErrorMessage"], [744, 23, 767, 21], [744, 24, 767, 22], [744, 52, 767, 50, "error"], [744, 57, 767, 55], [744, 58, 767, 56, "message"], [744, 65, 767, 63], [744, 67, 767, 65], [744, 68, 767, 66], [745, 8, 768, 6, "setProcessingState"], [745, 26, 768, 24], [745, 27, 768, 25], [745, 34, 768, 32], [745, 35, 768, 33], [746, 6, 769, 4], [747, 4, 770, 2], [747, 5, 770, 3], [748, 4, 771, 2], [749, 4, 772, 2], [749, 10, 772, 8, "pollForCompletion"], [749, 27, 772, 25], [749, 30, 772, 28], [749, 36, 772, 28, "pollForCompletion"], [749, 37, 772, 35, "jobId"], [749, 42, 772, 48], [749, 44, 772, 50, "timestamp"], [749, 53, 772, 67], [749, 55, 772, 69, "attempts"], [749, 63, 772, 77], [749, 66, 772, 80], [749, 67, 772, 81], [749, 72, 772, 86], [750, 6, 773, 4], [750, 12, 773, 10, "MAX_ATTEMPTS"], [750, 24, 773, 22], [750, 27, 773, 25], [750, 29, 773, 27], [750, 30, 773, 28], [750, 31, 773, 29], [751, 6, 774, 4], [751, 12, 774, 10, "POLL_INTERVAL"], [751, 25, 774, 23], [751, 28, 774, 26], [751, 32, 774, 30], [751, 33, 774, 31], [751, 34, 774, 32], [753, 6, 776, 4, "console"], [753, 13, 776, 11], [753, 14, 776, 12, "log"], [753, 17, 776, 15], [753, 18, 776, 16], [753, 53, 776, 51, "attempts"], [753, 61, 776, 59], [753, 64, 776, 62], [753, 65, 776, 63], [753, 69, 776, 67, "MAX_ATTEMPTS"], [753, 81, 776, 79], [753, 93, 776, 91, "jobId"], [753, 98, 776, 96], [753, 100, 776, 98], [753, 101, 776, 99], [754, 6, 778, 4], [754, 10, 778, 8, "attempts"], [754, 18, 778, 16], [754, 22, 778, 20, "MAX_ATTEMPTS"], [754, 34, 778, 32], [754, 36, 778, 34], [755, 8, 779, 6, "console"], [755, 15, 779, 13], [755, 16, 779, 14, "error"], [755, 21, 779, 19], [755, 22, 779, 20], [755, 75, 779, 73], [755, 76, 779, 74], [756, 8, 780, 6, "setErrorMessage"], [756, 23, 780, 21], [756, 24, 780, 22], [756, 63, 780, 61], [756, 64, 780, 62], [757, 8, 781, 6, "setProcessingState"], [757, 26, 781, 24], [757, 27, 781, 25], [757, 34, 781, 32], [757, 35, 781, 33], [758, 8, 782, 6], [759, 6, 783, 4], [760, 6, 785, 4], [760, 10, 785, 8], [761, 8, 786, 6], [761, 14, 786, 12, "response"], [761, 22, 786, 20], [761, 25, 786, 23], [761, 31, 786, 29, "fetch"], [761, 36, 786, 34], [761, 37, 786, 35], [761, 40, 786, 38, "API_BASE_URL"], [761, 52, 786, 50], [761, 75, 786, 73, "jobId"], [761, 80, 786, 78], [761, 82, 786, 80], [761, 84, 786, 82], [762, 10, 787, 8, "headers"], [762, 17, 787, 15], [762, 19, 787, 17], [763, 12, 788, 10], [763, 27, 788, 25], [763, 29, 788, 27], [763, 39, 788, 37], [763, 45, 788, 43, "getAuthToken"], [763, 57, 788, 55], [763, 58, 788, 56], [763, 59, 788, 57], [764, 10, 789, 8], [765, 8, 790, 6], [765, 9, 790, 7], [765, 10, 790, 8], [766, 8, 792, 6], [766, 12, 792, 10], [766, 13, 792, 11, "response"], [766, 21, 792, 19], [766, 22, 792, 20, "ok"], [766, 24, 792, 22], [766, 26, 792, 24], [767, 10, 793, 8], [767, 16, 793, 14], [767, 20, 793, 18, "Error"], [767, 25, 793, 23], [767, 26, 793, 24], [767, 34, 793, 32, "response"], [767, 42, 793, 40], [767, 43, 793, 41, "status"], [767, 49, 793, 47], [767, 54, 793, 52, "response"], [767, 62, 793, 60], [767, 63, 793, 61, "statusText"], [767, 73, 793, 71], [767, 75, 793, 73], [767, 76, 793, 74], [768, 8, 794, 6], [769, 8, 796, 6], [769, 14, 796, 12, "status"], [769, 20, 796, 18], [769, 23, 796, 21], [769, 29, 796, 27, "response"], [769, 37, 796, 35], [769, 38, 796, 36, "json"], [769, 42, 796, 40], [769, 43, 796, 41], [769, 44, 796, 42], [770, 8, 797, 6, "console"], [770, 15, 797, 13], [770, 16, 797, 14, "log"], [770, 19, 797, 17], [770, 20, 797, 18], [770, 54, 797, 52], [770, 56, 797, 54, "status"], [770, 62, 797, 60], [770, 63, 797, 61], [771, 8, 799, 6], [771, 12, 799, 10, "status"], [771, 18, 799, 16], [771, 19, 799, 17, "status"], [771, 25, 799, 23], [771, 30, 799, 28], [771, 41, 799, 39], [771, 43, 799, 41], [772, 10, 800, 8, "console"], [772, 17, 800, 15], [772, 18, 800, 16, "log"], [772, 21, 800, 19], [772, 22, 800, 20], [772, 73, 800, 71], [772, 74, 800, 72], [773, 10, 801, 8, "setProcessingProgress"], [773, 31, 801, 29], [773, 32, 801, 30], [773, 35, 801, 33], [773, 36, 801, 34], [774, 10, 802, 8, "setProcessingState"], [774, 28, 802, 26], [774, 29, 802, 27], [774, 40, 802, 38], [774, 41, 802, 39], [775, 10, 803, 8], [776, 10, 804, 8], [776, 16, 804, 14, "result"], [776, 22, 804, 20], [776, 25, 804, 23], [777, 12, 805, 10, "imageUrl"], [777, 20, 805, 18], [777, 22, 805, 20, "status"], [777, 28, 805, 26], [777, 29, 805, 27, "publicUrl"], [777, 38, 805, 36], [778, 12, 805, 38], [779, 12, 806, 10, "localUri"], [779, 20, 806, 18], [779, 22, 806, 20, "capturedPhoto"], [779, 35, 806, 33], [779, 39, 806, 37, "status"], [779, 45, 806, 43], [779, 46, 806, 44, "publicUrl"], [779, 55, 806, 53], [780, 12, 806, 55], [781, 12, 807, 10, "challengeCode"], [781, 25, 807, 23], [781, 27, 807, 25, "challengeCode"], [781, 40, 807, 38], [781, 44, 807, 42], [781, 46, 807, 44], [782, 12, 808, 10, "timestamp"], [782, 21, 808, 19], [783, 12, 809, 10, "processingStatus"], [783, 28, 809, 26], [783, 30, 809, 28], [784, 10, 810, 8], [784, 11, 810, 9], [785, 10, 811, 8, "console"], [785, 17, 811, 15], [785, 18, 811, 16, "log"], [785, 21, 811, 19], [785, 22, 811, 20], [785, 57, 811, 55], [785, 59, 811, 57, "result"], [785, 65, 811, 63], [785, 66, 811, 64], [786, 10, 812, 8, "onComplete"], [786, 20, 812, 18], [786, 21, 812, 19, "result"], [786, 27, 812, 25], [786, 28, 812, 26], [787, 10, 813, 8], [788, 8, 814, 6], [788, 9, 814, 7], [788, 15, 814, 13], [788, 19, 814, 17, "status"], [788, 25, 814, 23], [788, 26, 814, 24, "status"], [788, 32, 814, 30], [788, 37, 814, 35], [788, 45, 814, 43], [788, 47, 814, 45], [789, 10, 815, 8, "console"], [789, 17, 815, 15], [789, 18, 815, 16, "error"], [789, 23, 815, 21], [789, 24, 815, 22], [789, 60, 815, 58], [789, 62, 815, 60, "status"], [789, 68, 815, 66], [789, 69, 815, 67, "error"], [789, 74, 815, 72], [789, 75, 815, 73], [790, 10, 816, 8], [790, 16, 816, 14], [790, 20, 816, 18, "Error"], [790, 25, 816, 23], [790, 26, 816, 24, "status"], [790, 32, 816, 30], [790, 33, 816, 31, "error"], [790, 38, 816, 36], [790, 42, 816, 40], [790, 61, 816, 59], [790, 62, 816, 60], [791, 8, 817, 6], [791, 9, 817, 7], [791, 15, 817, 13], [792, 10, 818, 8], [793, 10, 819, 8], [793, 16, 819, 14, "progressValue"], [793, 29, 819, 27], [793, 32, 819, 30], [793, 34, 819, 32], [793, 37, 819, 36, "attempts"], [793, 45, 819, 44], [793, 48, 819, 47, "MAX_ATTEMPTS"], [793, 60, 819, 59], [793, 63, 819, 63], [793, 65, 819, 65], [794, 10, 820, 8, "console"], [794, 17, 820, 15], [794, 18, 820, 16, "log"], [794, 21, 820, 19], [794, 22, 820, 20], [794, 71, 820, 69, "progressValue"], [794, 84, 820, 82], [794, 87, 820, 85], [794, 88, 820, 86], [795, 10, 821, 8, "setProcessingProgress"], [795, 31, 821, 29], [795, 32, 821, 30, "progressValue"], [795, 45, 821, 43], [795, 46, 821, 44], [796, 10, 823, 8, "setTimeout"], [796, 20, 823, 18], [796, 21, 823, 19], [796, 27, 823, 25], [797, 12, 824, 10, "pollForCompletion"], [797, 29, 824, 27], [797, 30, 824, 28, "jobId"], [797, 35, 824, 33], [797, 37, 824, 35, "timestamp"], [797, 46, 824, 44], [797, 48, 824, 46, "attempts"], [797, 56, 824, 54], [797, 59, 824, 57], [797, 60, 824, 58], [797, 61, 824, 59], [798, 10, 825, 8], [798, 11, 825, 9], [798, 13, 825, 11, "POLL_INTERVAL"], [798, 26, 825, 24], [798, 27, 825, 25], [799, 8, 826, 6], [800, 6, 827, 4], [800, 7, 827, 5], [800, 8, 827, 6], [800, 15, 827, 13, "error"], [800, 20, 827, 18], [800, 22, 827, 20], [801, 8, 828, 6, "console"], [801, 15, 828, 13], [801, 16, 828, 14, "error"], [801, 21, 828, 19], [801, 22, 828, 20], [801, 54, 828, 52], [801, 56, 828, 54, "error"], [801, 61, 828, 59], [801, 62, 828, 60], [802, 8, 829, 6, "setErrorMessage"], [802, 23, 829, 21], [802, 24, 829, 22], [802, 62, 829, 60, "error"], [802, 67, 829, 65], [802, 68, 829, 66, "message"], [802, 75, 829, 73], [802, 77, 829, 75], [802, 78, 829, 76], [803, 8, 830, 6, "setProcessingState"], [803, 26, 830, 24], [803, 27, 830, 25], [803, 34, 830, 32], [803, 35, 830, 33], [804, 6, 831, 4], [805, 4, 832, 2], [805, 5, 832, 3], [806, 4, 833, 2], [807, 4, 834, 2], [807, 10, 834, 8, "getAuthToken"], [807, 22, 834, 20], [807, 25, 834, 23], [807, 31, 834, 23, "getAuthToken"], [807, 32, 834, 23], [807, 37, 834, 52], [808, 6, 835, 4], [809, 6, 836, 4], [810, 6, 837, 4], [810, 13, 837, 11], [810, 30, 837, 28], [811, 4, 838, 2], [811, 5, 838, 3], [813, 4, 840, 2], [814, 4, 841, 2], [814, 10, 841, 8, "retryCapture"], [814, 22, 841, 20], [814, 25, 841, 23], [814, 29, 841, 23, "useCallback"], [814, 47, 841, 34], [814, 49, 841, 35], [814, 55, 841, 41], [815, 6, 842, 4, "console"], [815, 13, 842, 11], [815, 14, 842, 12, "log"], [815, 17, 842, 15], [815, 18, 842, 16], [815, 55, 842, 53], [815, 56, 842, 54], [816, 6, 843, 4, "setProcessingState"], [816, 24, 843, 22], [816, 25, 843, 23], [816, 31, 843, 29], [816, 32, 843, 30], [817, 6, 844, 4, "setErrorMessage"], [817, 21, 844, 19], [817, 22, 844, 20], [817, 24, 844, 22], [817, 25, 844, 23], [818, 6, 845, 4, "setCapturedPhoto"], [818, 22, 845, 20], [818, 23, 845, 21], [818, 25, 845, 23], [818, 26, 845, 24], [819, 6, 846, 4, "setProcessingProgress"], [819, 27, 846, 25], [819, 28, 846, 26], [819, 29, 846, 27], [819, 30, 846, 28], [820, 4, 847, 2], [820, 5, 847, 3], [820, 7, 847, 5], [820, 9, 847, 7], [820, 10, 847, 8], [821, 4, 848, 2], [822, 4, 849, 2], [822, 8, 849, 2, "useEffect"], [822, 24, 849, 11], [822, 26, 849, 12], [822, 32, 849, 18], [823, 6, 850, 4, "console"], [823, 13, 850, 11], [823, 14, 850, 12, "log"], [823, 17, 850, 15], [823, 18, 850, 16], [823, 53, 850, 51], [823, 55, 850, 53, "permission"], [823, 65, 850, 63], [823, 66, 850, 64], [824, 6, 851, 4], [824, 10, 851, 8, "permission"], [824, 20, 851, 18], [824, 22, 851, 20], [825, 8, 852, 6, "console"], [825, 15, 852, 13], [825, 16, 852, 14, "log"], [825, 19, 852, 17], [825, 20, 852, 18], [825, 57, 852, 55], [825, 59, 852, 57, "permission"], [825, 69, 852, 67], [825, 70, 852, 68, "granted"], [825, 77, 852, 75], [825, 78, 852, 76], [826, 6, 853, 4], [827, 4, 854, 2], [827, 5, 854, 3], [827, 7, 854, 5], [827, 8, 854, 6, "permission"], [827, 18, 854, 16], [827, 19, 854, 17], [827, 20, 854, 18], [828, 4, 855, 2], [829, 4, 856, 2], [829, 8, 856, 6], [829, 9, 856, 7, "permission"], [829, 19, 856, 17], [829, 21, 856, 19], [830, 6, 857, 4, "console"], [830, 13, 857, 11], [830, 14, 857, 12, "log"], [830, 17, 857, 15], [830, 18, 857, 16], [830, 67, 857, 65], [830, 68, 857, 66], [831, 6, 858, 4], [831, 26, 859, 6], [831, 30, 859, 6, "_jsxDevRuntime"], [831, 44, 859, 6], [831, 45, 859, 6, "jsxDEV"], [831, 51, 859, 6], [831, 53, 859, 7, "_View"], [831, 58, 859, 7], [831, 59, 859, 7, "default"], [831, 66, 859, 11], [832, 8, 859, 12, "style"], [832, 13, 859, 17], [832, 15, 859, 19, "styles"], [832, 21, 859, 25], [832, 22, 859, 26, "container"], [832, 31, 859, 36], [833, 8, 859, 36, "children"], [833, 16, 859, 36], [833, 32, 860, 8], [833, 36, 860, 8, "_jsxDevRuntime"], [833, 50, 860, 8], [833, 51, 860, 8, "jsxDEV"], [833, 57, 860, 8], [833, 59, 860, 9, "_ActivityIndicator"], [833, 77, 860, 9], [833, 78, 860, 9, "default"], [833, 85, 860, 26], [834, 10, 860, 27, "size"], [834, 14, 860, 31], [834, 16, 860, 32], [834, 23, 860, 39], [835, 10, 860, 40, "color"], [835, 15, 860, 45], [835, 17, 860, 46], [836, 8, 860, 55], [837, 10, 860, 55, "fileName"], [837, 18, 860, 55], [837, 20, 860, 55, "_jsxFileName"], [837, 32, 860, 55], [838, 10, 860, 55, "lineNumber"], [838, 20, 860, 55], [839, 10, 860, 55, "columnNumber"], [839, 22, 860, 55], [840, 8, 860, 55], [840, 15, 860, 57], [840, 16, 860, 58], [840, 31, 861, 8], [840, 35, 861, 8, "_jsxDevRuntime"], [840, 49, 861, 8], [840, 50, 861, 8, "jsxDEV"], [840, 56, 861, 8], [840, 58, 861, 9, "_Text"], [840, 63, 861, 9], [840, 64, 861, 9, "default"], [840, 71, 861, 13], [841, 10, 861, 14, "style"], [841, 15, 861, 19], [841, 17, 861, 21, "styles"], [841, 23, 861, 27], [841, 24, 861, 28, "loadingText"], [841, 35, 861, 40], [842, 10, 861, 40, "children"], [842, 18, 861, 40], [842, 20, 861, 41], [843, 8, 861, 58], [844, 10, 861, 58, "fileName"], [844, 18, 861, 58], [844, 20, 861, 58, "_jsxFileName"], [844, 32, 861, 58], [845, 10, 861, 58, "lineNumber"], [845, 20, 861, 58], [846, 10, 861, 58, "columnNumber"], [846, 22, 861, 58], [847, 8, 861, 58], [847, 15, 861, 64], [847, 16, 861, 65], [848, 6, 861, 65], [849, 8, 861, 65, "fileName"], [849, 16, 861, 65], [849, 18, 861, 65, "_jsxFileName"], [849, 30, 861, 65], [850, 8, 861, 65, "lineNumber"], [850, 18, 861, 65], [851, 8, 861, 65, "columnNumber"], [851, 20, 861, 65], [852, 6, 861, 65], [852, 13, 862, 12], [852, 14, 862, 13], [853, 4, 864, 2], [854, 4, 865, 2], [854, 8, 865, 6], [854, 9, 865, 7, "permission"], [854, 19, 865, 17], [854, 20, 865, 18, "granted"], [854, 27, 865, 25], [854, 29, 865, 27], [855, 6, 866, 4, "console"], [855, 13, 866, 11], [855, 14, 866, 12, "log"], [855, 17, 866, 15], [855, 18, 866, 16], [855, 93, 866, 91], [855, 94, 866, 92], [856, 6, 867, 4], [856, 26, 868, 6], [856, 30, 868, 6, "_jsxDevRuntime"], [856, 44, 868, 6], [856, 45, 868, 6, "jsxDEV"], [856, 51, 868, 6], [856, 53, 868, 7, "_View"], [856, 58, 868, 7], [856, 59, 868, 7, "default"], [856, 66, 868, 11], [857, 8, 868, 12, "style"], [857, 13, 868, 17], [857, 15, 868, 19, "styles"], [857, 21, 868, 25], [857, 22, 868, 26, "container"], [857, 31, 868, 36], [858, 8, 868, 36, "children"], [858, 16, 868, 36], [858, 31, 869, 8], [858, 35, 869, 8, "_jsxDevRuntime"], [858, 49, 869, 8], [858, 50, 869, 8, "jsxDEV"], [858, 56, 869, 8], [858, 58, 869, 9, "_View"], [858, 63, 869, 9], [858, 64, 869, 9, "default"], [858, 71, 869, 13], [859, 10, 869, 14, "style"], [859, 15, 869, 19], [859, 17, 869, 21, "styles"], [859, 23, 869, 27], [859, 24, 869, 28, "permissionContent"], [859, 41, 869, 46], [860, 10, 869, 46, "children"], [860, 18, 869, 46], [860, 34, 870, 10], [860, 38, 870, 10, "_jsxDevRuntime"], [860, 52, 870, 10], [860, 53, 870, 10, "jsxDEV"], [860, 59, 870, 10], [860, 61, 870, 11, "_lucideReactNative"], [860, 79, 870, 11], [860, 80, 870, 11, "Camera"], [860, 86, 870, 21], [861, 12, 870, 22, "size"], [861, 16, 870, 26], [861, 18, 870, 28], [861, 20, 870, 31], [862, 12, 870, 32, "color"], [862, 17, 870, 37], [862, 19, 870, 38], [863, 10, 870, 47], [864, 12, 870, 47, "fileName"], [864, 20, 870, 47], [864, 22, 870, 47, "_jsxFileName"], [864, 34, 870, 47], [865, 12, 870, 47, "lineNumber"], [865, 22, 870, 47], [866, 12, 870, 47, "columnNumber"], [866, 24, 870, 47], [867, 10, 870, 47], [867, 17, 870, 49], [867, 18, 870, 50], [867, 33, 871, 10], [867, 37, 871, 10, "_jsxDevRuntime"], [867, 51, 871, 10], [867, 52, 871, 10, "jsxDEV"], [867, 58, 871, 10], [867, 60, 871, 11, "_Text"], [867, 65, 871, 11], [867, 66, 871, 11, "default"], [867, 73, 871, 15], [868, 12, 871, 16, "style"], [868, 17, 871, 21], [868, 19, 871, 23, "styles"], [868, 25, 871, 29], [868, 26, 871, 30, "permissionTitle"], [868, 41, 871, 46], [869, 12, 871, 46, "children"], [869, 20, 871, 46], [869, 22, 871, 47], [870, 10, 871, 73], [871, 12, 871, 73, "fileName"], [871, 20, 871, 73], [871, 22, 871, 73, "_jsxFileName"], [871, 34, 871, 73], [872, 12, 871, 73, "lineNumber"], [872, 22, 871, 73], [873, 12, 871, 73, "columnNumber"], [873, 24, 871, 73], [874, 10, 871, 73], [874, 17, 871, 79], [874, 18, 871, 80], [874, 33, 872, 10], [874, 37, 872, 10, "_jsxDevRuntime"], [874, 51, 872, 10], [874, 52, 872, 10, "jsxDEV"], [874, 58, 872, 10], [874, 60, 872, 11, "_Text"], [874, 65, 872, 11], [874, 66, 872, 11, "default"], [874, 73, 872, 15], [875, 12, 872, 16, "style"], [875, 17, 872, 21], [875, 19, 872, 23, "styles"], [875, 25, 872, 29], [875, 26, 872, 30, "permissionDescription"], [875, 47, 872, 52], [876, 12, 872, 52, "children"], [876, 20, 872, 52], [876, 22, 872, 53], [877, 10, 875, 10], [878, 12, 875, 10, "fileName"], [878, 20, 875, 10], [878, 22, 875, 10, "_jsxFileName"], [878, 34, 875, 10], [879, 12, 875, 10, "lineNumber"], [879, 22, 875, 10], [880, 12, 875, 10, "columnNumber"], [880, 24, 875, 10], [881, 10, 875, 10], [881, 17, 875, 16], [881, 18, 875, 17], [881, 33, 876, 10], [881, 37, 876, 10, "_jsxDevRuntime"], [881, 51, 876, 10], [881, 52, 876, 10, "jsxDEV"], [881, 58, 876, 10], [881, 60, 876, 11, "_TouchableOpacity"], [881, 77, 876, 11], [881, 78, 876, 11, "default"], [881, 85, 876, 27], [882, 12, 876, 28, "onPress"], [882, 19, 876, 35], [882, 21, 876, 37, "requestPermission"], [882, 38, 876, 55], [883, 12, 876, 56, "style"], [883, 17, 876, 61], [883, 19, 876, 63, "styles"], [883, 25, 876, 69], [883, 26, 876, 70, "primaryButton"], [883, 39, 876, 84], [884, 12, 876, 84, "children"], [884, 20, 876, 84], [884, 35, 877, 12], [884, 39, 877, 12, "_jsxDevRuntime"], [884, 53, 877, 12], [884, 54, 877, 12, "jsxDEV"], [884, 60, 877, 12], [884, 62, 877, 13, "_Text"], [884, 67, 877, 13], [884, 68, 877, 13, "default"], [884, 75, 877, 17], [885, 14, 877, 18, "style"], [885, 19, 877, 23], [885, 21, 877, 25, "styles"], [885, 27, 877, 31], [885, 28, 877, 32, "primaryButtonText"], [885, 45, 877, 50], [886, 14, 877, 50, "children"], [886, 22, 877, 50], [886, 24, 877, 51], [887, 12, 877, 67], [888, 14, 877, 67, "fileName"], [888, 22, 877, 67], [888, 24, 877, 67, "_jsxFileName"], [888, 36, 877, 67], [889, 14, 877, 67, "lineNumber"], [889, 24, 877, 67], [890, 14, 877, 67, "columnNumber"], [890, 26, 877, 67], [891, 12, 877, 67], [891, 19, 877, 73], [892, 10, 877, 74], [893, 12, 877, 74, "fileName"], [893, 20, 877, 74], [893, 22, 877, 74, "_jsxFileName"], [893, 34, 877, 74], [894, 12, 877, 74, "lineNumber"], [894, 22, 877, 74], [895, 12, 877, 74, "columnNumber"], [895, 24, 877, 74], [896, 10, 877, 74], [896, 17, 878, 28], [896, 18, 878, 29], [896, 33, 879, 10], [896, 37, 879, 10, "_jsxDevRuntime"], [896, 51, 879, 10], [896, 52, 879, 10, "jsxDEV"], [896, 58, 879, 10], [896, 60, 879, 11, "_TouchableOpacity"], [896, 77, 879, 11], [896, 78, 879, 11, "default"], [896, 85, 879, 27], [897, 12, 879, 28, "onPress"], [897, 19, 879, 35], [897, 21, 879, 37, "onCancel"], [897, 29, 879, 46], [898, 12, 879, 47, "style"], [898, 17, 879, 52], [898, 19, 879, 54, "styles"], [898, 25, 879, 60], [898, 26, 879, 61, "secondaryButton"], [898, 41, 879, 77], [899, 12, 879, 77, "children"], [899, 20, 879, 77], [899, 35, 880, 12], [899, 39, 880, 12, "_jsxDevRuntime"], [899, 53, 880, 12], [899, 54, 880, 12, "jsxDEV"], [899, 60, 880, 12], [899, 62, 880, 13, "_Text"], [899, 67, 880, 13], [899, 68, 880, 13, "default"], [899, 75, 880, 17], [900, 14, 880, 18, "style"], [900, 19, 880, 23], [900, 21, 880, 25, "styles"], [900, 27, 880, 31], [900, 28, 880, 32, "secondaryButtonText"], [900, 47, 880, 52], [901, 14, 880, 52, "children"], [901, 22, 880, 52], [901, 24, 880, 53], [902, 12, 880, 59], [903, 14, 880, 59, "fileName"], [903, 22, 880, 59], [903, 24, 880, 59, "_jsxFileName"], [903, 36, 880, 59], [904, 14, 880, 59, "lineNumber"], [904, 24, 880, 59], [905, 14, 880, 59, "columnNumber"], [905, 26, 880, 59], [906, 12, 880, 59], [906, 19, 880, 65], [907, 10, 880, 66], [908, 12, 880, 66, "fileName"], [908, 20, 880, 66], [908, 22, 880, 66, "_jsxFileName"], [908, 34, 880, 66], [909, 12, 880, 66, "lineNumber"], [909, 22, 880, 66], [910, 12, 880, 66, "columnNumber"], [910, 24, 880, 66], [911, 10, 880, 66], [911, 17, 881, 28], [911, 18, 881, 29], [912, 8, 881, 29], [913, 10, 881, 29, "fileName"], [913, 18, 881, 29], [913, 20, 881, 29, "_jsxFileName"], [913, 32, 881, 29], [914, 10, 881, 29, "lineNumber"], [914, 20, 881, 29], [915, 10, 881, 29, "columnNumber"], [915, 22, 881, 29], [916, 8, 881, 29], [916, 15, 882, 14], [917, 6, 882, 15], [918, 8, 882, 15, "fileName"], [918, 16, 882, 15], [918, 18, 882, 15, "_jsxFileName"], [918, 30, 882, 15], [919, 8, 882, 15, "lineNumber"], [919, 18, 882, 15], [920, 8, 882, 15, "columnNumber"], [920, 20, 882, 15], [921, 6, 882, 15], [921, 13, 883, 12], [921, 14, 883, 13], [922, 4, 885, 2], [923, 4, 886, 2], [924, 4, 887, 2, "console"], [924, 11, 887, 9], [924, 12, 887, 10, "log"], [924, 15, 887, 13], [924, 16, 887, 14], [924, 55, 887, 53], [924, 56, 887, 54], [925, 4, 889, 2], [925, 24, 890, 4], [925, 28, 890, 4, "_jsxDevRuntime"], [925, 42, 890, 4], [925, 43, 890, 4, "jsxDEV"], [925, 49, 890, 4], [925, 51, 890, 5, "_View"], [925, 56, 890, 5], [925, 57, 890, 5, "default"], [925, 64, 890, 9], [926, 6, 890, 10, "style"], [926, 11, 890, 15], [926, 13, 890, 17, "styles"], [926, 19, 890, 23], [926, 20, 890, 24, "container"], [926, 29, 890, 34], [927, 6, 890, 34, "children"], [927, 14, 890, 34], [927, 30, 892, 6], [927, 34, 892, 6, "_jsxDevRuntime"], [927, 48, 892, 6], [927, 49, 892, 6, "jsxDEV"], [927, 55, 892, 6], [927, 57, 892, 7, "_View"], [927, 62, 892, 7], [927, 63, 892, 7, "default"], [927, 70, 892, 11], [928, 8, 892, 12, "style"], [928, 13, 892, 17], [928, 15, 892, 19, "styles"], [928, 21, 892, 25], [928, 22, 892, 26, "cameraContainer"], [928, 37, 892, 42], [929, 8, 892, 43, "id"], [929, 10, 892, 45], [929, 12, 892, 46], [929, 29, 892, 63], [930, 8, 892, 63, "children"], [930, 16, 892, 63], [930, 32, 893, 8], [930, 36, 893, 8, "_jsxDevRuntime"], [930, 50, 893, 8], [930, 51, 893, 8, "jsxDEV"], [930, 57, 893, 8], [930, 59, 893, 9, "_expoCamera"], [930, 70, 893, 9], [930, 71, 893, 9, "CameraView"], [930, 81, 893, 19], [931, 10, 894, 10, "ref"], [931, 13, 894, 13], [931, 15, 894, 15, "cameraRef"], [931, 24, 894, 25], [932, 10, 895, 10, "style"], [932, 15, 895, 15], [932, 17, 895, 17], [932, 18, 895, 18, "styles"], [932, 24, 895, 24], [932, 25, 895, 25, "camera"], [932, 31, 895, 31], [932, 33, 895, 33], [933, 12, 895, 35, "backgroundColor"], [933, 27, 895, 50], [933, 29, 895, 52], [934, 10, 895, 62], [934, 11, 895, 63], [934, 12, 895, 65], [935, 10, 896, 10, "facing"], [935, 16, 896, 16], [935, 18, 896, 17], [935, 24, 896, 23], [936, 10, 897, 10, "onLayout"], [936, 18, 897, 18], [936, 20, 897, 21, "e"], [936, 21, 897, 22], [936, 25, 897, 27], [937, 12, 898, 12, "console"], [937, 19, 898, 19], [937, 20, 898, 20, "log"], [937, 23, 898, 23], [937, 24, 898, 24], [937, 56, 898, 56], [937, 58, 898, 58, "e"], [937, 59, 898, 59], [937, 60, 898, 60, "nativeEvent"], [937, 71, 898, 71], [937, 72, 898, 72, "layout"], [937, 78, 898, 78], [937, 79, 898, 79], [938, 12, 899, 12, "setViewSize"], [938, 23, 899, 23], [938, 24, 899, 24], [939, 14, 899, 26, "width"], [939, 19, 899, 31], [939, 21, 899, 33, "e"], [939, 22, 899, 34], [939, 23, 899, 35, "nativeEvent"], [939, 34, 899, 46], [939, 35, 899, 47, "layout"], [939, 41, 899, 53], [939, 42, 899, 54, "width"], [939, 47, 899, 59], [940, 14, 899, 61, "height"], [940, 20, 899, 67], [940, 22, 899, 69, "e"], [940, 23, 899, 70], [940, 24, 899, 71, "nativeEvent"], [940, 35, 899, 82], [940, 36, 899, 83, "layout"], [940, 42, 899, 89], [940, 43, 899, 90, "height"], [941, 12, 899, 97], [941, 13, 899, 98], [941, 14, 899, 99], [942, 10, 900, 10], [942, 11, 900, 12], [943, 10, 901, 10, "onCameraReady"], [943, 23, 901, 23], [943, 25, 901, 25, "onCameraReady"], [943, 26, 901, 25], [943, 31, 901, 31], [944, 12, 902, 12, "console"], [944, 19, 902, 19], [944, 20, 902, 20, "log"], [944, 23, 902, 23], [944, 24, 902, 24], [944, 55, 902, 55], [944, 56, 902, 56], [945, 12, 903, 12, "setIsCameraReady"], [945, 28, 903, 28], [945, 29, 903, 29], [945, 33, 903, 33], [945, 34, 903, 34], [945, 35, 903, 35], [945, 36, 903, 36], [946, 10, 904, 10], [946, 11, 904, 12], [947, 10, 905, 10, "onMountError"], [947, 22, 905, 22], [947, 24, 905, 25, "error"], [947, 29, 905, 30], [947, 33, 905, 35], [948, 12, 906, 12, "console"], [948, 19, 906, 19], [948, 20, 906, 20, "error"], [948, 25, 906, 25], [948, 26, 906, 26], [948, 63, 906, 63], [948, 65, 906, 65, "error"], [948, 70, 906, 70], [948, 71, 906, 71], [949, 12, 907, 12, "setErrorMessage"], [949, 27, 907, 27], [949, 28, 907, 28], [949, 57, 907, 57], [949, 58, 907, 58], [950, 12, 908, 12, "setProcessingState"], [950, 30, 908, 30], [950, 31, 908, 31], [950, 38, 908, 38], [950, 39, 908, 39], [951, 10, 909, 10], [952, 8, 909, 12], [953, 10, 909, 12, "fileName"], [953, 18, 909, 12], [953, 20, 909, 12, "_jsxFileName"], [953, 32, 909, 12], [954, 10, 909, 12, "lineNumber"], [954, 20, 909, 12], [955, 10, 909, 12, "columnNumber"], [955, 22, 909, 12], [956, 8, 909, 12], [956, 15, 910, 9], [956, 16, 910, 10], [956, 18, 912, 9], [956, 19, 912, 10, "isCameraReady"], [956, 32, 912, 23], [956, 49, 913, 10], [956, 53, 913, 10, "_jsxDevRuntime"], [956, 67, 913, 10], [956, 68, 913, 10, "jsxDEV"], [956, 74, 913, 10], [956, 76, 913, 11, "_View"], [956, 81, 913, 11], [956, 82, 913, 11, "default"], [956, 89, 913, 15], [957, 10, 913, 16, "style"], [957, 15, 913, 21], [957, 17, 913, 23], [957, 18, 913, 24, "StyleSheet"], [957, 37, 913, 34], [957, 38, 913, 35, "absoluteFill"], [957, 50, 913, 47], [957, 52, 913, 49], [958, 12, 913, 51, "backgroundColor"], [958, 27, 913, 66], [958, 29, 913, 68], [958, 49, 913, 88], [959, 12, 913, 90, "justifyContent"], [959, 26, 913, 104], [959, 28, 913, 106], [959, 36, 913, 114], [960, 12, 913, 116, "alignItems"], [960, 22, 913, 126], [960, 24, 913, 128], [960, 32, 913, 136], [961, 12, 913, 138, "zIndex"], [961, 18, 913, 144], [961, 20, 913, 146], [962, 10, 913, 151], [962, 11, 913, 152], [962, 12, 913, 154], [963, 10, 913, 154, "children"], [963, 18, 913, 154], [963, 33, 914, 12], [963, 37, 914, 12, "_jsxDevRuntime"], [963, 51, 914, 12], [963, 52, 914, 12, "jsxDEV"], [963, 58, 914, 12], [963, 60, 914, 13, "_View"], [963, 65, 914, 13], [963, 66, 914, 13, "default"], [963, 73, 914, 17], [964, 12, 914, 18, "style"], [964, 17, 914, 23], [964, 19, 914, 25], [965, 14, 914, 27, "backgroundColor"], [965, 29, 914, 42], [965, 31, 914, 44], [965, 51, 914, 64], [966, 14, 914, 66, "padding"], [966, 21, 914, 73], [966, 23, 914, 75], [966, 25, 914, 77], [967, 14, 914, 79, "borderRadius"], [967, 26, 914, 91], [967, 28, 914, 93], [967, 30, 914, 95], [968, 14, 914, 97, "alignItems"], [968, 24, 914, 107], [968, 26, 914, 109], [969, 12, 914, 118], [969, 13, 914, 120], [970, 12, 914, 120, "children"], [970, 20, 914, 120], [970, 36, 915, 14], [970, 40, 915, 14, "_jsxDevRuntime"], [970, 54, 915, 14], [970, 55, 915, 14, "jsxDEV"], [970, 61, 915, 14], [970, 63, 915, 15, "_ActivityIndicator"], [970, 81, 915, 15], [970, 82, 915, 15, "default"], [970, 89, 915, 32], [971, 14, 915, 33, "size"], [971, 18, 915, 37], [971, 20, 915, 38], [971, 27, 915, 45], [972, 14, 915, 46, "color"], [972, 19, 915, 51], [972, 21, 915, 52], [972, 30, 915, 61], [973, 14, 915, 62, "style"], [973, 19, 915, 67], [973, 21, 915, 69], [974, 16, 915, 71, "marginBottom"], [974, 28, 915, 83], [974, 30, 915, 85], [975, 14, 915, 88], [976, 12, 915, 90], [977, 14, 915, 90, "fileName"], [977, 22, 915, 90], [977, 24, 915, 90, "_jsxFileName"], [977, 36, 915, 90], [978, 14, 915, 90, "lineNumber"], [978, 24, 915, 90], [979, 14, 915, 90, "columnNumber"], [979, 26, 915, 90], [980, 12, 915, 90], [980, 19, 915, 92], [980, 20, 915, 93], [980, 35, 916, 14], [980, 39, 916, 14, "_jsxDevRuntime"], [980, 53, 916, 14], [980, 54, 916, 14, "jsxDEV"], [980, 60, 916, 14], [980, 62, 916, 15, "_Text"], [980, 67, 916, 15], [980, 68, 916, 15, "default"], [980, 75, 916, 19], [981, 14, 916, 20, "style"], [981, 19, 916, 25], [981, 21, 916, 27], [982, 16, 916, 29, "color"], [982, 21, 916, 34], [982, 23, 916, 36], [982, 29, 916, 42], [983, 16, 916, 44, "fontSize"], [983, 24, 916, 52], [983, 26, 916, 54], [983, 28, 916, 56], [984, 16, 916, 58, "fontWeight"], [984, 26, 916, 68], [984, 28, 916, 70], [985, 14, 916, 76], [985, 15, 916, 78], [986, 14, 916, 78, "children"], [986, 22, 916, 78], [986, 24, 916, 79], [987, 12, 916, 101], [988, 14, 916, 101, "fileName"], [988, 22, 916, 101], [988, 24, 916, 101, "_jsxFileName"], [988, 36, 916, 101], [989, 14, 916, 101, "lineNumber"], [989, 24, 916, 101], [990, 14, 916, 101, "columnNumber"], [990, 26, 916, 101], [991, 12, 916, 101], [991, 19, 916, 107], [991, 20, 916, 108], [991, 35, 917, 14], [991, 39, 917, 14, "_jsxDevRuntime"], [991, 53, 917, 14], [991, 54, 917, 14, "jsxDEV"], [991, 60, 917, 14], [991, 62, 917, 15, "_Text"], [991, 67, 917, 15], [991, 68, 917, 15, "default"], [991, 75, 917, 19], [992, 14, 917, 20, "style"], [992, 19, 917, 25], [992, 21, 917, 27], [993, 16, 917, 29, "color"], [993, 21, 917, 34], [993, 23, 917, 36], [993, 32, 917, 45], [994, 16, 917, 47, "fontSize"], [994, 24, 917, 55], [994, 26, 917, 57], [994, 28, 917, 59], [995, 16, 917, 61, "marginTop"], [995, 25, 917, 70], [995, 27, 917, 72], [996, 14, 917, 74], [996, 15, 917, 76], [997, 14, 917, 76, "children"], [997, 22, 917, 76], [997, 24, 917, 77], [998, 12, 917, 88], [999, 14, 917, 88, "fileName"], [999, 22, 917, 88], [999, 24, 917, 88, "_jsxFileName"], [999, 36, 917, 88], [1000, 14, 917, 88, "lineNumber"], [1000, 24, 917, 88], [1001, 14, 917, 88, "columnNumber"], [1001, 26, 917, 88], [1002, 12, 917, 88], [1002, 19, 917, 94], [1002, 20, 917, 95], [1003, 10, 917, 95], [1004, 12, 917, 95, "fileName"], [1004, 20, 917, 95], [1004, 22, 917, 95, "_jsxFileName"], [1004, 34, 917, 95], [1005, 12, 917, 95, "lineNumber"], [1005, 22, 917, 95], [1006, 12, 917, 95, "columnNumber"], [1006, 24, 917, 95], [1007, 10, 917, 95], [1007, 17, 918, 18], [1008, 8, 918, 19], [1009, 10, 918, 19, "fileName"], [1009, 18, 918, 19], [1009, 20, 918, 19, "_jsxFileName"], [1009, 32, 918, 19], [1010, 10, 918, 19, "lineNumber"], [1010, 20, 918, 19], [1011, 10, 918, 19, "columnNumber"], [1011, 22, 918, 19], [1012, 8, 918, 19], [1012, 15, 919, 16], [1012, 16, 920, 9], [1012, 18, 923, 9, "isCameraReady"], [1012, 31, 923, 22], [1012, 35, 923, 26, "previewBlurEnabled"], [1012, 53, 923, 44], [1012, 57, 923, 48, "viewSize"], [1012, 65, 923, 56], [1012, 66, 923, 57, "width"], [1012, 71, 923, 62], [1012, 74, 923, 65], [1012, 75, 923, 66], [1012, 92, 924, 10], [1012, 96, 924, 10, "_jsxDevRuntime"], [1012, 110, 924, 10], [1012, 111, 924, 10, "jsxDEV"], [1012, 117, 924, 10], [1012, 119, 924, 10, "_jsxDevRuntime"], [1012, 133, 924, 10], [1012, 134, 924, 10, "Fragment"], [1012, 142, 924, 10], [1013, 10, 924, 10, "children"], [1013, 18, 924, 10], [1013, 34, 926, 12], [1013, 38, 926, 12, "_jsxDevRuntime"], [1013, 52, 926, 12], [1013, 53, 926, 12, "jsxDEV"], [1013, 59, 926, 12], [1013, 61, 926, 13, "_LiveFaceCanvas"], [1013, 76, 926, 13], [1013, 77, 926, 13, "default"], [1013, 84, 926, 27], [1014, 12, 926, 28, "containerId"], [1014, 23, 926, 39], [1014, 25, 926, 40], [1014, 42, 926, 57], [1015, 12, 926, 58, "width"], [1015, 17, 926, 63], [1015, 19, 926, 65, "viewSize"], [1015, 27, 926, 73], [1015, 28, 926, 74, "width"], [1015, 33, 926, 80], [1016, 12, 926, 81, "height"], [1016, 18, 926, 87], [1016, 20, 926, 89, "viewSize"], [1016, 28, 926, 97], [1016, 29, 926, 98, "height"], [1017, 10, 926, 105], [1018, 12, 926, 105, "fileName"], [1018, 20, 926, 105], [1018, 22, 926, 105, "_jsxFileName"], [1018, 34, 926, 105], [1019, 12, 926, 105, "lineNumber"], [1019, 22, 926, 105], [1020, 12, 926, 105, "columnNumber"], [1020, 24, 926, 105], [1021, 10, 926, 105], [1021, 17, 926, 107], [1021, 18, 926, 108], [1021, 33, 927, 12], [1021, 37, 927, 12, "_jsxDevRuntime"], [1021, 51, 927, 12], [1021, 52, 927, 12, "jsxDEV"], [1021, 58, 927, 12], [1021, 60, 927, 13, "_View"], [1021, 65, 927, 13], [1021, 66, 927, 13, "default"], [1021, 73, 927, 17], [1022, 12, 927, 18, "style"], [1022, 17, 927, 23], [1022, 19, 927, 25], [1022, 20, 927, 26, "StyleSheet"], [1022, 39, 927, 36], [1022, 40, 927, 37, "absoluteFill"], [1022, 52, 927, 49], [1022, 54, 927, 51], [1023, 14, 927, 53, "pointerEvents"], [1023, 27, 927, 66], [1023, 29, 927, 68], [1024, 12, 927, 75], [1024, 13, 927, 76], [1024, 14, 927, 78], [1025, 12, 927, 78, "children"], [1025, 20, 927, 78], [1025, 36, 929, 12], [1025, 40, 929, 12, "_jsxDevRuntime"], [1025, 54, 929, 12], [1025, 55, 929, 12, "jsxDEV"], [1025, 61, 929, 12], [1025, 63, 929, 13, "_expoBlur"], [1025, 72, 929, 13], [1025, 73, 929, 13, "BlurView"], [1025, 81, 929, 21], [1026, 14, 929, 22, "intensity"], [1026, 23, 929, 31], [1026, 25, 929, 33], [1026, 27, 929, 36], [1027, 14, 929, 37, "tint"], [1027, 18, 929, 41], [1027, 20, 929, 42], [1027, 26, 929, 48], [1028, 14, 929, 49, "style"], [1028, 19, 929, 54], [1028, 21, 929, 56], [1028, 22, 929, 57, "styles"], [1028, 28, 929, 63], [1028, 29, 929, 64, "blurZone"], [1028, 37, 929, 72], [1028, 39, 929, 74], [1029, 16, 930, 14, "left"], [1029, 20, 930, 18], [1029, 22, 930, 20], [1029, 23, 930, 21], [1030, 16, 931, 14, "top"], [1030, 19, 931, 17], [1030, 21, 931, 19, "viewSize"], [1030, 29, 931, 27], [1030, 30, 931, 28, "height"], [1030, 36, 931, 34], [1030, 39, 931, 37], [1030, 42, 931, 40], [1031, 16, 932, 14, "width"], [1031, 21, 932, 19], [1031, 23, 932, 21, "viewSize"], [1031, 31, 932, 29], [1031, 32, 932, 30, "width"], [1031, 37, 932, 35], [1032, 16, 933, 14, "height"], [1032, 22, 933, 20], [1032, 24, 933, 22, "viewSize"], [1032, 32, 933, 30], [1032, 33, 933, 31, "height"], [1032, 39, 933, 37], [1032, 42, 933, 40], [1032, 46, 933, 44], [1033, 16, 934, 14, "borderRadius"], [1033, 28, 934, 26], [1033, 30, 934, 28], [1034, 14, 935, 12], [1034, 15, 935, 13], [1035, 12, 935, 15], [1036, 14, 935, 15, "fileName"], [1036, 22, 935, 15], [1036, 24, 935, 15, "_jsxFileName"], [1036, 36, 935, 15], [1037, 14, 935, 15, "lineNumber"], [1037, 24, 935, 15], [1038, 14, 935, 15, "columnNumber"], [1038, 26, 935, 15], [1039, 12, 935, 15], [1039, 19, 935, 17], [1039, 20, 935, 18], [1039, 35, 937, 12], [1039, 39, 937, 12, "_jsxDevRuntime"], [1039, 53, 937, 12], [1039, 54, 937, 12, "jsxDEV"], [1039, 60, 937, 12], [1039, 62, 937, 13, "_expoBlur"], [1039, 71, 937, 13], [1039, 72, 937, 13, "BlurView"], [1039, 80, 937, 21], [1040, 14, 937, 22, "intensity"], [1040, 23, 937, 31], [1040, 25, 937, 33], [1040, 27, 937, 36], [1041, 14, 937, 37, "tint"], [1041, 18, 937, 41], [1041, 20, 937, 42], [1041, 26, 937, 48], [1042, 14, 937, 49, "style"], [1042, 19, 937, 54], [1042, 21, 937, 56], [1042, 22, 937, 57, "styles"], [1042, 28, 937, 63], [1042, 29, 937, 64, "blurZone"], [1042, 37, 937, 72], [1042, 39, 937, 74], [1043, 16, 938, 14, "left"], [1043, 20, 938, 18], [1043, 22, 938, 20], [1043, 23, 938, 21], [1044, 16, 939, 14, "top"], [1044, 19, 939, 17], [1044, 21, 939, 19], [1044, 22, 939, 20], [1045, 16, 940, 14, "width"], [1045, 21, 940, 19], [1045, 23, 940, 21, "viewSize"], [1045, 31, 940, 29], [1045, 32, 940, 30, "width"], [1045, 37, 940, 35], [1046, 16, 941, 14, "height"], [1046, 22, 941, 20], [1046, 24, 941, 22, "viewSize"], [1046, 32, 941, 30], [1046, 33, 941, 31, "height"], [1046, 39, 941, 37], [1046, 42, 941, 40], [1046, 45, 941, 43], [1047, 16, 942, 14, "borderRadius"], [1047, 28, 942, 26], [1047, 30, 942, 28], [1048, 14, 943, 12], [1048, 15, 943, 13], [1049, 12, 943, 15], [1050, 14, 943, 15, "fileName"], [1050, 22, 943, 15], [1050, 24, 943, 15, "_jsxFileName"], [1050, 36, 943, 15], [1051, 14, 943, 15, "lineNumber"], [1051, 24, 943, 15], [1052, 14, 943, 15, "columnNumber"], [1052, 26, 943, 15], [1053, 12, 943, 15], [1053, 19, 943, 17], [1053, 20, 943, 18], [1053, 35, 945, 12], [1053, 39, 945, 12, "_jsxDevRuntime"], [1053, 53, 945, 12], [1053, 54, 945, 12, "jsxDEV"], [1053, 60, 945, 12], [1053, 62, 945, 13, "_expoBlur"], [1053, 71, 945, 13], [1053, 72, 945, 13, "BlurView"], [1053, 80, 945, 21], [1054, 14, 945, 22, "intensity"], [1054, 23, 945, 31], [1054, 25, 945, 33], [1054, 27, 945, 36], [1055, 14, 945, 37, "tint"], [1055, 18, 945, 41], [1055, 20, 945, 42], [1055, 26, 945, 48], [1056, 14, 945, 49, "style"], [1056, 19, 945, 54], [1056, 21, 945, 56], [1056, 22, 945, 57, "styles"], [1056, 28, 945, 63], [1056, 29, 945, 64, "blurZone"], [1056, 37, 945, 72], [1056, 39, 945, 74], [1057, 16, 946, 14, "left"], [1057, 20, 946, 18], [1057, 22, 946, 20, "viewSize"], [1057, 30, 946, 28], [1057, 31, 946, 29, "width"], [1057, 36, 946, 34], [1057, 39, 946, 37], [1057, 42, 946, 40], [1057, 45, 946, 44, "viewSize"], [1057, 53, 946, 52], [1057, 54, 946, 53, "width"], [1057, 59, 946, 58], [1057, 62, 946, 61], [1057, 66, 946, 66], [1058, 16, 947, 14, "top"], [1058, 19, 947, 17], [1058, 21, 947, 19, "viewSize"], [1058, 29, 947, 27], [1058, 30, 947, 28, "height"], [1058, 36, 947, 34], [1058, 39, 947, 37], [1058, 43, 947, 41], [1058, 46, 947, 45, "viewSize"], [1058, 54, 947, 53], [1058, 55, 947, 54, "width"], [1058, 60, 947, 59], [1058, 63, 947, 62], [1058, 67, 947, 67], [1059, 16, 948, 14, "width"], [1059, 21, 948, 19], [1059, 23, 948, 21, "viewSize"], [1059, 31, 948, 29], [1059, 32, 948, 30, "width"], [1059, 37, 948, 35], [1059, 40, 948, 38], [1059, 43, 948, 41], [1060, 16, 949, 14, "height"], [1060, 22, 949, 20], [1060, 24, 949, 22, "viewSize"], [1060, 32, 949, 30], [1060, 33, 949, 31, "width"], [1060, 38, 949, 36], [1060, 41, 949, 39], [1060, 44, 949, 42], [1061, 16, 950, 14, "borderRadius"], [1061, 28, 950, 26], [1061, 30, 950, 29, "viewSize"], [1061, 38, 950, 37], [1061, 39, 950, 38, "width"], [1061, 44, 950, 43], [1061, 47, 950, 46], [1061, 50, 950, 49], [1061, 53, 950, 53], [1062, 14, 951, 12], [1062, 15, 951, 13], [1063, 12, 951, 15], [1064, 14, 951, 15, "fileName"], [1064, 22, 951, 15], [1064, 24, 951, 15, "_jsxFileName"], [1064, 36, 951, 15], [1065, 14, 951, 15, "lineNumber"], [1065, 24, 951, 15], [1066, 14, 951, 15, "columnNumber"], [1066, 26, 951, 15], [1067, 12, 951, 15], [1067, 19, 951, 17], [1067, 20, 951, 18], [1067, 35, 952, 12], [1067, 39, 952, 12, "_jsxDevRuntime"], [1067, 53, 952, 12], [1067, 54, 952, 12, "jsxDEV"], [1067, 60, 952, 12], [1067, 62, 952, 13, "_expoBlur"], [1067, 71, 952, 13], [1067, 72, 952, 13, "BlurView"], [1067, 80, 952, 21], [1068, 14, 952, 22, "intensity"], [1068, 23, 952, 31], [1068, 25, 952, 33], [1068, 27, 952, 36], [1069, 14, 952, 37, "tint"], [1069, 18, 952, 41], [1069, 20, 952, 42], [1069, 26, 952, 48], [1070, 14, 952, 49, "style"], [1070, 19, 952, 54], [1070, 21, 952, 56], [1070, 22, 952, 57, "styles"], [1070, 28, 952, 63], [1070, 29, 952, 64, "blurZone"], [1070, 37, 952, 72], [1070, 39, 952, 74], [1071, 16, 953, 14, "left"], [1071, 20, 953, 18], [1071, 22, 953, 20, "viewSize"], [1071, 30, 953, 28], [1071, 31, 953, 29, "width"], [1071, 36, 953, 34], [1071, 39, 953, 37], [1071, 42, 953, 40], [1071, 45, 953, 44, "viewSize"], [1071, 53, 953, 52], [1071, 54, 953, 53, "width"], [1071, 59, 953, 58], [1071, 62, 953, 61], [1071, 66, 953, 66], [1072, 16, 954, 14, "top"], [1072, 19, 954, 17], [1072, 21, 954, 19, "viewSize"], [1072, 29, 954, 27], [1072, 30, 954, 28, "height"], [1072, 36, 954, 34], [1072, 39, 954, 37], [1072, 42, 954, 40], [1072, 45, 954, 44, "viewSize"], [1072, 53, 954, 52], [1072, 54, 954, 53, "width"], [1072, 59, 954, 58], [1072, 62, 954, 61], [1072, 66, 954, 66], [1073, 16, 955, 14, "width"], [1073, 21, 955, 19], [1073, 23, 955, 21, "viewSize"], [1073, 31, 955, 29], [1073, 32, 955, 30, "width"], [1073, 37, 955, 35], [1073, 40, 955, 38], [1073, 43, 955, 41], [1074, 16, 956, 14, "height"], [1074, 22, 956, 20], [1074, 24, 956, 22, "viewSize"], [1074, 32, 956, 30], [1074, 33, 956, 31, "width"], [1074, 38, 956, 36], [1074, 41, 956, 39], [1074, 44, 956, 42], [1075, 16, 957, 14, "borderRadius"], [1075, 28, 957, 26], [1075, 30, 957, 29, "viewSize"], [1075, 38, 957, 37], [1075, 39, 957, 38, "width"], [1075, 44, 957, 43], [1075, 47, 957, 46], [1075, 50, 957, 49], [1075, 53, 957, 53], [1076, 14, 958, 12], [1076, 15, 958, 13], [1077, 12, 958, 15], [1078, 14, 958, 15, "fileName"], [1078, 22, 958, 15], [1078, 24, 958, 15, "_jsxFileName"], [1078, 36, 958, 15], [1079, 14, 958, 15, "lineNumber"], [1079, 24, 958, 15], [1080, 14, 958, 15, "columnNumber"], [1080, 26, 958, 15], [1081, 12, 958, 15], [1081, 19, 958, 17], [1081, 20, 958, 18], [1081, 35, 959, 12], [1081, 39, 959, 12, "_jsxDevRuntime"], [1081, 53, 959, 12], [1081, 54, 959, 12, "jsxDEV"], [1081, 60, 959, 12], [1081, 62, 959, 13, "_expoBlur"], [1081, 71, 959, 13], [1081, 72, 959, 13, "BlurView"], [1081, 80, 959, 21], [1082, 14, 959, 22, "intensity"], [1082, 23, 959, 31], [1082, 25, 959, 33], [1082, 27, 959, 36], [1083, 14, 959, 37, "tint"], [1083, 18, 959, 41], [1083, 20, 959, 42], [1083, 26, 959, 48], [1084, 14, 959, 49, "style"], [1084, 19, 959, 54], [1084, 21, 959, 56], [1084, 22, 959, 57, "styles"], [1084, 28, 959, 63], [1084, 29, 959, 64, "blurZone"], [1084, 37, 959, 72], [1084, 39, 959, 74], [1085, 16, 960, 14, "left"], [1085, 20, 960, 18], [1085, 22, 960, 20, "viewSize"], [1085, 30, 960, 28], [1085, 31, 960, 29, "width"], [1085, 36, 960, 34], [1085, 39, 960, 37], [1085, 42, 960, 40], [1085, 45, 960, 44, "viewSize"], [1085, 53, 960, 52], [1085, 54, 960, 53, "width"], [1085, 59, 960, 58], [1085, 62, 960, 61], [1085, 66, 960, 66], [1086, 16, 961, 14, "top"], [1086, 19, 961, 17], [1086, 21, 961, 19, "viewSize"], [1086, 29, 961, 27], [1086, 30, 961, 28, "height"], [1086, 36, 961, 34], [1086, 39, 961, 37], [1086, 42, 961, 40], [1086, 45, 961, 44, "viewSize"], [1086, 53, 961, 52], [1086, 54, 961, 53, "width"], [1086, 59, 961, 58], [1086, 62, 961, 61], [1086, 66, 961, 66], [1087, 16, 962, 14, "width"], [1087, 21, 962, 19], [1087, 23, 962, 21, "viewSize"], [1087, 31, 962, 29], [1087, 32, 962, 30, "width"], [1087, 37, 962, 35], [1087, 40, 962, 38], [1087, 43, 962, 41], [1088, 16, 963, 14, "height"], [1088, 22, 963, 20], [1088, 24, 963, 22, "viewSize"], [1088, 32, 963, 30], [1088, 33, 963, 31, "width"], [1088, 38, 963, 36], [1088, 41, 963, 39], [1088, 44, 963, 42], [1089, 16, 964, 14, "borderRadius"], [1089, 28, 964, 26], [1089, 30, 964, 29, "viewSize"], [1089, 38, 964, 37], [1089, 39, 964, 38, "width"], [1089, 44, 964, 43], [1089, 47, 964, 46], [1089, 50, 964, 49], [1089, 53, 964, 53], [1090, 14, 965, 12], [1090, 15, 965, 13], [1091, 12, 965, 15], [1092, 14, 965, 15, "fileName"], [1092, 22, 965, 15], [1092, 24, 965, 15, "_jsxFileName"], [1092, 36, 965, 15], [1093, 14, 965, 15, "lineNumber"], [1093, 24, 965, 15], [1094, 14, 965, 15, "columnNumber"], [1094, 26, 965, 15], [1095, 12, 965, 15], [1095, 19, 965, 17], [1095, 20, 965, 18], [1095, 22, 967, 13, "__DEV__"], [1095, 29, 967, 20], [1095, 46, 968, 14], [1095, 50, 968, 14, "_jsxDevRuntime"], [1095, 64, 968, 14], [1095, 65, 968, 14, "jsxDEV"], [1095, 71, 968, 14], [1095, 73, 968, 15, "_View"], [1095, 78, 968, 15], [1095, 79, 968, 15, "default"], [1095, 86, 968, 19], [1096, 14, 968, 20, "style"], [1096, 19, 968, 25], [1096, 21, 968, 27, "styles"], [1096, 27, 968, 33], [1096, 28, 968, 34, "previewChip"], [1096, 39, 968, 46], [1097, 14, 968, 46, "children"], [1097, 22, 968, 46], [1097, 37, 969, 16], [1097, 41, 969, 16, "_jsxDevRuntime"], [1097, 55, 969, 16], [1097, 56, 969, 16, "jsxDEV"], [1097, 62, 969, 16], [1097, 64, 969, 17, "_Text"], [1097, 69, 969, 17], [1097, 70, 969, 17, "default"], [1097, 77, 969, 21], [1098, 16, 969, 22, "style"], [1098, 21, 969, 27], [1098, 23, 969, 29, "styles"], [1098, 29, 969, 35], [1098, 30, 969, 36, "previewChipText"], [1098, 45, 969, 52], [1099, 16, 969, 52, "children"], [1099, 24, 969, 52], [1099, 26, 969, 53], [1100, 14, 969, 73], [1101, 16, 969, 73, "fileName"], [1101, 24, 969, 73], [1101, 26, 969, 73, "_jsxFileName"], [1101, 38, 969, 73], [1102, 16, 969, 73, "lineNumber"], [1102, 26, 969, 73], [1103, 16, 969, 73, "columnNumber"], [1103, 28, 969, 73], [1104, 14, 969, 73], [1104, 21, 969, 79], [1105, 12, 969, 80], [1106, 14, 969, 80, "fileName"], [1106, 22, 969, 80], [1106, 24, 969, 80, "_jsxFileName"], [1106, 36, 969, 80], [1107, 14, 969, 80, "lineNumber"], [1107, 24, 969, 80], [1108, 14, 969, 80, "columnNumber"], [1108, 26, 969, 80], [1109, 12, 969, 80], [1109, 19, 970, 20], [1109, 20, 971, 13], [1110, 10, 971, 13], [1111, 12, 971, 13, "fileName"], [1111, 20, 971, 13], [1111, 22, 971, 13, "_jsxFileName"], [1111, 34, 971, 13], [1112, 12, 971, 13, "lineNumber"], [1112, 22, 971, 13], [1113, 12, 971, 13, "columnNumber"], [1113, 24, 971, 13], [1114, 10, 971, 13], [1114, 17, 972, 18], [1114, 18, 972, 19], [1115, 8, 972, 19], [1115, 23, 973, 12], [1115, 24, 974, 9], [1115, 26, 976, 9, "isCameraReady"], [1115, 39, 976, 22], [1115, 56, 977, 10], [1115, 60, 977, 10, "_jsxDevRuntime"], [1115, 74, 977, 10], [1115, 75, 977, 10, "jsxDEV"], [1115, 81, 977, 10], [1115, 83, 977, 10, "_jsxDevRuntime"], [1115, 97, 977, 10], [1115, 98, 977, 10, "Fragment"], [1115, 106, 977, 10], [1116, 10, 977, 10, "children"], [1116, 18, 977, 10], [1116, 34, 979, 12], [1116, 38, 979, 12, "_jsxDevRuntime"], [1116, 52, 979, 12], [1116, 53, 979, 12, "jsxDEV"], [1116, 59, 979, 12], [1116, 61, 979, 13, "_View"], [1116, 66, 979, 13], [1116, 67, 979, 13, "default"], [1116, 74, 979, 17], [1117, 12, 979, 18, "style"], [1117, 17, 979, 23], [1117, 19, 979, 25, "styles"], [1117, 25, 979, 31], [1117, 26, 979, 32, "headerOverlay"], [1117, 39, 979, 46], [1118, 12, 979, 46, "children"], [1118, 20, 979, 46], [1118, 35, 980, 14], [1118, 39, 980, 14, "_jsxDevRuntime"], [1118, 53, 980, 14], [1118, 54, 980, 14, "jsxDEV"], [1118, 60, 980, 14], [1118, 62, 980, 15, "_View"], [1118, 67, 980, 15], [1118, 68, 980, 15, "default"], [1118, 75, 980, 19], [1119, 14, 980, 20, "style"], [1119, 19, 980, 25], [1119, 21, 980, 27, "styles"], [1119, 27, 980, 33], [1119, 28, 980, 34, "headerContent"], [1119, 41, 980, 48], [1120, 14, 980, 48, "children"], [1120, 22, 980, 48], [1120, 38, 981, 16], [1120, 42, 981, 16, "_jsxDevRuntime"], [1120, 56, 981, 16], [1120, 57, 981, 16, "jsxDEV"], [1120, 63, 981, 16], [1120, 65, 981, 17, "_View"], [1120, 70, 981, 17], [1120, 71, 981, 17, "default"], [1120, 78, 981, 21], [1121, 16, 981, 22, "style"], [1121, 21, 981, 27], [1121, 23, 981, 29, "styles"], [1121, 29, 981, 35], [1121, 30, 981, 36, "headerLeft"], [1121, 40, 981, 47], [1122, 16, 981, 47, "children"], [1122, 24, 981, 47], [1122, 40, 982, 18], [1122, 44, 982, 18, "_jsxDevRuntime"], [1122, 58, 982, 18], [1122, 59, 982, 18, "jsxDEV"], [1122, 65, 982, 18], [1122, 67, 982, 19, "_Text"], [1122, 72, 982, 19], [1122, 73, 982, 19, "default"], [1122, 80, 982, 23], [1123, 18, 982, 24, "style"], [1123, 23, 982, 29], [1123, 25, 982, 31, "styles"], [1123, 31, 982, 37], [1123, 32, 982, 38, "headerTitle"], [1123, 43, 982, 50], [1124, 18, 982, 50, "children"], [1124, 26, 982, 50], [1124, 28, 982, 51], [1125, 16, 982, 62], [1126, 18, 982, 62, "fileName"], [1126, 26, 982, 62], [1126, 28, 982, 62, "_jsxFileName"], [1126, 40, 982, 62], [1127, 18, 982, 62, "lineNumber"], [1127, 28, 982, 62], [1128, 18, 982, 62, "columnNumber"], [1128, 30, 982, 62], [1129, 16, 982, 62], [1129, 23, 982, 68], [1129, 24, 982, 69], [1129, 39, 983, 18], [1129, 43, 983, 18, "_jsxDevRuntime"], [1129, 57, 983, 18], [1129, 58, 983, 18, "jsxDEV"], [1129, 64, 983, 18], [1129, 66, 983, 19, "_View"], [1129, 71, 983, 19], [1129, 72, 983, 19, "default"], [1129, 79, 983, 23], [1130, 18, 983, 24, "style"], [1130, 23, 983, 29], [1130, 25, 983, 31, "styles"], [1130, 31, 983, 37], [1130, 32, 983, 38, "subtitleRow"], [1130, 43, 983, 50], [1131, 18, 983, 50, "children"], [1131, 26, 983, 50], [1131, 42, 984, 20], [1131, 46, 984, 20, "_jsxDevRuntime"], [1131, 60, 984, 20], [1131, 61, 984, 20, "jsxDEV"], [1131, 67, 984, 20], [1131, 69, 984, 21, "_Text"], [1131, 74, 984, 21], [1131, 75, 984, 21, "default"], [1131, 82, 984, 25], [1132, 20, 984, 26, "style"], [1132, 25, 984, 31], [1132, 27, 984, 33, "styles"], [1132, 33, 984, 39], [1132, 34, 984, 40, "webIcon"], [1132, 41, 984, 48], [1133, 20, 984, 48, "children"], [1133, 28, 984, 48], [1133, 30, 984, 49], [1134, 18, 984, 51], [1135, 20, 984, 51, "fileName"], [1135, 28, 984, 51], [1135, 30, 984, 51, "_jsxFileName"], [1135, 42, 984, 51], [1136, 20, 984, 51, "lineNumber"], [1136, 30, 984, 51], [1137, 20, 984, 51, "columnNumber"], [1137, 32, 984, 51], [1138, 18, 984, 51], [1138, 25, 984, 57], [1138, 26, 984, 58], [1138, 41, 985, 20], [1138, 45, 985, 20, "_jsxDevRuntime"], [1138, 59, 985, 20], [1138, 60, 985, 20, "jsxDEV"], [1138, 66, 985, 20], [1138, 68, 985, 21, "_Text"], [1138, 73, 985, 21], [1138, 74, 985, 21, "default"], [1138, 81, 985, 25], [1139, 20, 985, 26, "style"], [1139, 25, 985, 31], [1139, 27, 985, 33, "styles"], [1139, 33, 985, 39], [1139, 34, 985, 40, "headerSubtitle"], [1139, 48, 985, 55], [1140, 20, 985, 55, "children"], [1140, 28, 985, 55], [1140, 30, 985, 56], [1141, 18, 985, 71], [1142, 20, 985, 71, "fileName"], [1142, 28, 985, 71], [1142, 30, 985, 71, "_jsxFileName"], [1142, 42, 985, 71], [1143, 20, 985, 71, "lineNumber"], [1143, 30, 985, 71], [1144, 20, 985, 71, "columnNumber"], [1144, 32, 985, 71], [1145, 18, 985, 71], [1145, 25, 985, 77], [1145, 26, 985, 78], [1146, 16, 985, 78], [1147, 18, 985, 78, "fileName"], [1147, 26, 985, 78], [1147, 28, 985, 78, "_jsxFileName"], [1147, 40, 985, 78], [1148, 18, 985, 78, "lineNumber"], [1148, 28, 985, 78], [1149, 18, 985, 78, "columnNumber"], [1149, 30, 985, 78], [1150, 16, 985, 78], [1150, 23, 986, 24], [1150, 24, 986, 25], [1150, 26, 987, 19, "challengeCode"], [1150, 39, 987, 32], [1150, 56, 988, 20], [1150, 60, 988, 20, "_jsxDevRuntime"], [1150, 74, 988, 20], [1150, 75, 988, 20, "jsxDEV"], [1150, 81, 988, 20], [1150, 83, 988, 21, "_View"], [1150, 88, 988, 21], [1150, 89, 988, 21, "default"], [1150, 96, 988, 25], [1151, 18, 988, 26, "style"], [1151, 23, 988, 31], [1151, 25, 988, 33, "styles"], [1151, 31, 988, 39], [1151, 32, 988, 40, "challengeRow"], [1151, 44, 988, 53], [1152, 18, 988, 53, "children"], [1152, 26, 988, 53], [1152, 42, 989, 22], [1152, 46, 989, 22, "_jsxDevRuntime"], [1152, 60, 989, 22], [1152, 61, 989, 22, "jsxDEV"], [1152, 67, 989, 22], [1152, 69, 989, 23, "_lucideReactNative"], [1152, 87, 989, 23], [1152, 88, 989, 23, "Shield"], [1152, 94, 989, 29], [1153, 20, 989, 30, "size"], [1153, 24, 989, 34], [1153, 26, 989, 36], [1153, 28, 989, 39], [1154, 20, 989, 40, "color"], [1154, 25, 989, 45], [1154, 27, 989, 46], [1155, 18, 989, 52], [1156, 20, 989, 52, "fileName"], [1156, 28, 989, 52], [1156, 30, 989, 52, "_jsxFileName"], [1156, 42, 989, 52], [1157, 20, 989, 52, "lineNumber"], [1157, 30, 989, 52], [1158, 20, 989, 52, "columnNumber"], [1158, 32, 989, 52], [1159, 18, 989, 52], [1159, 25, 989, 54], [1159, 26, 989, 55], [1159, 41, 990, 22], [1159, 45, 990, 22, "_jsxDevRuntime"], [1159, 59, 990, 22], [1159, 60, 990, 22, "jsxDEV"], [1159, 66, 990, 22], [1159, 68, 990, 23, "_Text"], [1159, 73, 990, 23], [1159, 74, 990, 23, "default"], [1159, 81, 990, 27], [1160, 20, 990, 28, "style"], [1160, 25, 990, 33], [1160, 27, 990, 35, "styles"], [1160, 33, 990, 41], [1160, 34, 990, 42, "challengeCode"], [1160, 47, 990, 56], [1161, 20, 990, 56, "children"], [1161, 28, 990, 56], [1161, 30, 990, 58, "challengeCode"], [1162, 18, 990, 71], [1163, 20, 990, 71, "fileName"], [1163, 28, 990, 71], [1163, 30, 990, 71, "_jsxFileName"], [1163, 42, 990, 71], [1164, 20, 990, 71, "lineNumber"], [1164, 30, 990, 71], [1165, 20, 990, 71, "columnNumber"], [1165, 32, 990, 71], [1166, 18, 990, 71], [1166, 25, 990, 78], [1166, 26, 990, 79], [1167, 16, 990, 79], [1168, 18, 990, 79, "fileName"], [1168, 26, 990, 79], [1168, 28, 990, 79, "_jsxFileName"], [1168, 40, 990, 79], [1169, 18, 990, 79, "lineNumber"], [1169, 28, 990, 79], [1170, 18, 990, 79, "columnNumber"], [1170, 30, 990, 79], [1171, 16, 990, 79], [1171, 23, 991, 26], [1171, 24, 992, 19], [1172, 14, 992, 19], [1173, 16, 992, 19, "fileName"], [1173, 24, 992, 19], [1173, 26, 992, 19, "_jsxFileName"], [1173, 38, 992, 19], [1174, 16, 992, 19, "lineNumber"], [1174, 26, 992, 19], [1175, 16, 992, 19, "columnNumber"], [1175, 28, 992, 19], [1176, 14, 992, 19], [1176, 21, 993, 22], [1176, 22, 993, 23], [1176, 37, 994, 16], [1176, 41, 994, 16, "_jsxDevRuntime"], [1176, 55, 994, 16], [1176, 56, 994, 16, "jsxDEV"], [1176, 62, 994, 16], [1176, 64, 994, 17, "_TouchableOpacity"], [1176, 81, 994, 17], [1176, 82, 994, 17, "default"], [1176, 89, 994, 33], [1177, 16, 994, 34, "onPress"], [1177, 23, 994, 41], [1177, 25, 994, 43, "onCancel"], [1177, 33, 994, 52], [1178, 16, 994, 53, "style"], [1178, 21, 994, 58], [1178, 23, 994, 60, "styles"], [1178, 29, 994, 66], [1178, 30, 994, 67, "closeButton"], [1178, 41, 994, 79], [1179, 16, 994, 79, "children"], [1179, 24, 994, 79], [1179, 39, 995, 18], [1179, 43, 995, 18, "_jsxDevRuntime"], [1179, 57, 995, 18], [1179, 58, 995, 18, "jsxDEV"], [1179, 64, 995, 18], [1179, 66, 995, 19, "_lucideReactNative"], [1179, 84, 995, 19], [1179, 85, 995, 19, "X"], [1179, 86, 995, 20], [1180, 18, 995, 21, "size"], [1180, 22, 995, 25], [1180, 24, 995, 27], [1180, 26, 995, 30], [1181, 18, 995, 31, "color"], [1181, 23, 995, 36], [1181, 25, 995, 37], [1182, 16, 995, 43], [1183, 18, 995, 43, "fileName"], [1183, 26, 995, 43], [1183, 28, 995, 43, "_jsxFileName"], [1183, 40, 995, 43], [1184, 18, 995, 43, "lineNumber"], [1184, 28, 995, 43], [1185, 18, 995, 43, "columnNumber"], [1185, 30, 995, 43], [1186, 16, 995, 43], [1186, 23, 995, 45], [1187, 14, 995, 46], [1188, 16, 995, 46, "fileName"], [1188, 24, 995, 46], [1188, 26, 995, 46, "_jsxFileName"], [1188, 38, 995, 46], [1189, 16, 995, 46, "lineNumber"], [1189, 26, 995, 46], [1190, 16, 995, 46, "columnNumber"], [1190, 28, 995, 46], [1191, 14, 995, 46], [1191, 21, 996, 34], [1191, 22, 996, 35], [1192, 12, 996, 35], [1193, 14, 996, 35, "fileName"], [1193, 22, 996, 35], [1193, 24, 996, 35, "_jsxFileName"], [1193, 36, 996, 35], [1194, 14, 996, 35, "lineNumber"], [1194, 24, 996, 35], [1195, 14, 996, 35, "columnNumber"], [1195, 26, 996, 35], [1196, 12, 996, 35], [1196, 19, 997, 20], [1197, 10, 997, 21], [1198, 12, 997, 21, "fileName"], [1198, 20, 997, 21], [1198, 22, 997, 21, "_jsxFileName"], [1198, 34, 997, 21], [1199, 12, 997, 21, "lineNumber"], [1199, 22, 997, 21], [1200, 12, 997, 21, "columnNumber"], [1200, 24, 997, 21], [1201, 10, 997, 21], [1201, 17, 998, 18], [1201, 18, 998, 19], [1201, 33, 1000, 12], [1201, 37, 1000, 12, "_jsxDevRuntime"], [1201, 51, 1000, 12], [1201, 52, 1000, 12, "jsxDEV"], [1201, 58, 1000, 12], [1201, 60, 1000, 13, "_View"], [1201, 65, 1000, 13], [1201, 66, 1000, 13, "default"], [1201, 73, 1000, 17], [1202, 12, 1000, 18, "style"], [1202, 17, 1000, 23], [1202, 19, 1000, 25, "styles"], [1202, 25, 1000, 31], [1202, 26, 1000, 32, "privacyNotice"], [1202, 39, 1000, 46], [1203, 12, 1000, 46, "children"], [1203, 20, 1000, 46], [1203, 36, 1001, 14], [1203, 40, 1001, 14, "_jsxDevRuntime"], [1203, 54, 1001, 14], [1203, 55, 1001, 14, "jsxDEV"], [1203, 61, 1001, 14], [1203, 63, 1001, 15, "_lucideReactNative"], [1203, 81, 1001, 15], [1203, 82, 1001, 15, "Shield"], [1203, 88, 1001, 21], [1204, 14, 1001, 22, "size"], [1204, 18, 1001, 26], [1204, 20, 1001, 28], [1204, 22, 1001, 31], [1205, 14, 1001, 32, "color"], [1205, 19, 1001, 37], [1205, 21, 1001, 38], [1206, 12, 1001, 47], [1207, 14, 1001, 47, "fileName"], [1207, 22, 1001, 47], [1207, 24, 1001, 47, "_jsxFileName"], [1207, 36, 1001, 47], [1208, 14, 1001, 47, "lineNumber"], [1208, 24, 1001, 47], [1209, 14, 1001, 47, "columnNumber"], [1209, 26, 1001, 47], [1210, 12, 1001, 47], [1210, 19, 1001, 49], [1210, 20, 1001, 50], [1210, 35, 1002, 14], [1210, 39, 1002, 14, "_jsxDevRuntime"], [1210, 53, 1002, 14], [1210, 54, 1002, 14, "jsxDEV"], [1210, 60, 1002, 14], [1210, 62, 1002, 15, "_Text"], [1210, 67, 1002, 15], [1210, 68, 1002, 15, "default"], [1210, 75, 1002, 19], [1211, 14, 1002, 20, "style"], [1211, 19, 1002, 25], [1211, 21, 1002, 27, "styles"], [1211, 27, 1002, 33], [1211, 28, 1002, 34, "privacyText"], [1211, 39, 1002, 46], [1212, 14, 1002, 46, "children"], [1212, 22, 1002, 46], [1212, 24, 1002, 47], [1213, 12, 1004, 14], [1214, 14, 1004, 14, "fileName"], [1214, 22, 1004, 14], [1214, 24, 1004, 14, "_jsxFileName"], [1214, 36, 1004, 14], [1215, 14, 1004, 14, "lineNumber"], [1215, 24, 1004, 14], [1216, 14, 1004, 14, "columnNumber"], [1216, 26, 1004, 14], [1217, 12, 1004, 14], [1217, 19, 1004, 20], [1217, 20, 1004, 21], [1218, 10, 1004, 21], [1219, 12, 1004, 21, "fileName"], [1219, 20, 1004, 21], [1219, 22, 1004, 21, "_jsxFileName"], [1219, 34, 1004, 21], [1220, 12, 1004, 21, "lineNumber"], [1220, 22, 1004, 21], [1221, 12, 1004, 21, "columnNumber"], [1221, 24, 1004, 21], [1222, 10, 1004, 21], [1222, 17, 1005, 18], [1222, 18, 1005, 19], [1222, 33, 1007, 12], [1222, 37, 1007, 12, "_jsxDevRuntime"], [1222, 51, 1007, 12], [1222, 52, 1007, 12, "jsxDEV"], [1222, 58, 1007, 12], [1222, 60, 1007, 13, "_View"], [1222, 65, 1007, 13], [1222, 66, 1007, 13, "default"], [1222, 73, 1007, 17], [1223, 12, 1007, 18, "style"], [1223, 17, 1007, 23], [1223, 19, 1007, 25, "styles"], [1223, 25, 1007, 31], [1223, 26, 1007, 32, "footer<PERSON><PERSON><PERSON>"], [1223, 39, 1007, 46], [1224, 12, 1007, 46, "children"], [1224, 20, 1007, 46], [1224, 36, 1008, 14], [1224, 40, 1008, 14, "_jsxDevRuntime"], [1224, 54, 1008, 14], [1224, 55, 1008, 14, "jsxDEV"], [1224, 61, 1008, 14], [1224, 63, 1008, 15, "_Text"], [1224, 68, 1008, 15], [1224, 69, 1008, 15, "default"], [1224, 76, 1008, 19], [1225, 14, 1008, 20, "style"], [1225, 19, 1008, 25], [1225, 21, 1008, 27, "styles"], [1225, 27, 1008, 33], [1225, 28, 1008, 34, "instruction"], [1225, 39, 1008, 46], [1226, 14, 1008, 46, "children"], [1226, 22, 1008, 46], [1226, 24, 1008, 47], [1227, 12, 1010, 14], [1228, 14, 1010, 14, "fileName"], [1228, 22, 1010, 14], [1228, 24, 1010, 14, "_jsxFileName"], [1228, 36, 1010, 14], [1229, 14, 1010, 14, "lineNumber"], [1229, 24, 1010, 14], [1230, 14, 1010, 14, "columnNumber"], [1230, 26, 1010, 14], [1231, 12, 1010, 14], [1231, 19, 1010, 20], [1231, 20, 1010, 21], [1231, 35, 1012, 14], [1231, 39, 1012, 14, "_jsxDevRuntime"], [1231, 53, 1012, 14], [1231, 54, 1012, 14, "jsxDEV"], [1231, 60, 1012, 14], [1231, 62, 1012, 15, "_TouchableOpacity"], [1231, 79, 1012, 15], [1231, 80, 1012, 15, "default"], [1231, 87, 1012, 31], [1232, 14, 1013, 16, "onPress"], [1232, 21, 1013, 23], [1232, 23, 1013, 25, "capturePhoto"], [1232, 35, 1013, 38], [1233, 14, 1014, 16, "disabled"], [1233, 22, 1014, 24], [1233, 24, 1014, 26, "processingState"], [1233, 39, 1014, 41], [1233, 44, 1014, 46], [1233, 50, 1014, 52], [1233, 54, 1014, 56], [1233, 55, 1014, 57, "isCameraReady"], [1233, 68, 1014, 71], [1234, 14, 1015, 16, "style"], [1234, 19, 1015, 21], [1234, 21, 1015, 23], [1234, 22, 1016, 18, "styles"], [1234, 28, 1016, 24], [1234, 29, 1016, 25, "shutterButton"], [1234, 42, 1016, 38], [1234, 44, 1017, 18, "processingState"], [1234, 59, 1017, 33], [1234, 64, 1017, 38], [1234, 70, 1017, 44], [1234, 74, 1017, 48, "styles"], [1234, 80, 1017, 54], [1234, 81, 1017, 55, "shutterButtonDisabled"], [1234, 102, 1017, 76], [1234, 103, 1018, 18], [1235, 14, 1018, 18, "children"], [1235, 22, 1018, 18], [1235, 24, 1020, 17, "processingState"], [1235, 39, 1020, 32], [1235, 44, 1020, 37], [1235, 50, 1020, 43], [1235, 66, 1021, 18], [1235, 70, 1021, 18, "_jsxDevRuntime"], [1235, 84, 1021, 18], [1235, 85, 1021, 18, "jsxDEV"], [1235, 91, 1021, 18], [1235, 93, 1021, 19, "_View"], [1235, 98, 1021, 19], [1235, 99, 1021, 19, "default"], [1235, 106, 1021, 23], [1236, 16, 1021, 24, "style"], [1236, 21, 1021, 29], [1236, 23, 1021, 31, "styles"], [1236, 29, 1021, 37], [1236, 30, 1021, 38, "shutterInner"], [1237, 14, 1021, 51], [1238, 16, 1021, 51, "fileName"], [1238, 24, 1021, 51], [1238, 26, 1021, 51, "_jsxFileName"], [1238, 38, 1021, 51], [1239, 16, 1021, 51, "lineNumber"], [1239, 26, 1021, 51], [1240, 16, 1021, 51, "columnNumber"], [1240, 28, 1021, 51], [1241, 14, 1021, 51], [1241, 21, 1021, 53], [1241, 22, 1021, 54], [1241, 38, 1023, 18], [1241, 42, 1023, 18, "_jsxDevRuntime"], [1241, 56, 1023, 18], [1241, 57, 1023, 18, "jsxDEV"], [1241, 63, 1023, 18], [1241, 65, 1023, 19, "_ActivityIndicator"], [1241, 83, 1023, 19], [1241, 84, 1023, 19, "default"], [1241, 91, 1023, 36], [1242, 16, 1023, 37, "size"], [1242, 20, 1023, 41], [1242, 22, 1023, 42], [1242, 29, 1023, 49], [1243, 16, 1023, 50, "color"], [1243, 21, 1023, 55], [1243, 23, 1023, 56], [1244, 14, 1023, 65], [1245, 16, 1023, 65, "fileName"], [1245, 24, 1023, 65], [1245, 26, 1023, 65, "_jsxFileName"], [1245, 38, 1023, 65], [1246, 16, 1023, 65, "lineNumber"], [1246, 26, 1023, 65], [1247, 16, 1023, 65, "columnNumber"], [1247, 28, 1023, 65], [1248, 14, 1023, 65], [1248, 21, 1023, 67], [1249, 12, 1024, 17], [1250, 14, 1024, 17, "fileName"], [1250, 22, 1024, 17], [1250, 24, 1024, 17, "_jsxFileName"], [1250, 36, 1024, 17], [1251, 14, 1024, 17, "lineNumber"], [1251, 24, 1024, 17], [1252, 14, 1024, 17, "columnNumber"], [1252, 26, 1024, 17], [1253, 12, 1024, 17], [1253, 19, 1025, 32], [1253, 20, 1025, 33], [1253, 35, 1026, 14], [1253, 39, 1026, 14, "_jsxDevRuntime"], [1253, 53, 1026, 14], [1253, 54, 1026, 14, "jsxDEV"], [1253, 60, 1026, 14], [1253, 62, 1026, 15, "_Text"], [1253, 67, 1026, 15], [1253, 68, 1026, 15, "default"], [1253, 75, 1026, 19], [1254, 14, 1026, 20, "style"], [1254, 19, 1026, 25], [1254, 21, 1026, 27, "styles"], [1254, 27, 1026, 33], [1254, 28, 1026, 34, "privacyNote"], [1254, 39, 1026, 46], [1255, 14, 1026, 46, "children"], [1255, 22, 1026, 46], [1255, 24, 1026, 47], [1256, 12, 1028, 14], [1257, 14, 1028, 14, "fileName"], [1257, 22, 1028, 14], [1257, 24, 1028, 14, "_jsxFileName"], [1257, 36, 1028, 14], [1258, 14, 1028, 14, "lineNumber"], [1258, 24, 1028, 14], [1259, 14, 1028, 14, "columnNumber"], [1259, 26, 1028, 14], [1260, 12, 1028, 14], [1260, 19, 1028, 20], [1260, 20, 1028, 21], [1261, 10, 1028, 21], [1262, 12, 1028, 21, "fileName"], [1262, 20, 1028, 21], [1262, 22, 1028, 21, "_jsxFileName"], [1262, 34, 1028, 21], [1263, 12, 1028, 21, "lineNumber"], [1263, 22, 1028, 21], [1264, 12, 1028, 21, "columnNumber"], [1264, 24, 1028, 21], [1265, 10, 1028, 21], [1265, 17, 1029, 18], [1265, 18, 1029, 19], [1266, 8, 1029, 19], [1266, 23, 1030, 12], [1266, 24, 1031, 9], [1267, 6, 1031, 9], [1268, 8, 1031, 9, "fileName"], [1268, 16, 1031, 9], [1268, 18, 1031, 9, "_jsxFileName"], [1268, 30, 1031, 9], [1269, 8, 1031, 9, "lineNumber"], [1269, 18, 1031, 9], [1270, 8, 1031, 9, "columnNumber"], [1270, 20, 1031, 9], [1271, 6, 1031, 9], [1271, 13, 1032, 12], [1271, 14, 1032, 13], [1271, 29, 1034, 6], [1271, 33, 1034, 6, "_jsxDevRuntime"], [1271, 47, 1034, 6], [1271, 48, 1034, 6, "jsxDEV"], [1271, 54, 1034, 6], [1271, 56, 1034, 7, "_Modal"], [1271, 62, 1034, 7], [1271, 63, 1034, 7, "default"], [1271, 70, 1034, 12], [1272, 8, 1035, 8, "visible"], [1272, 15, 1035, 15], [1272, 17, 1035, 17, "processingState"], [1272, 32, 1035, 32], [1272, 37, 1035, 37], [1272, 43, 1035, 43], [1272, 47, 1035, 47, "processingState"], [1272, 62, 1035, 62], [1272, 67, 1035, 67], [1272, 74, 1035, 75], [1273, 8, 1036, 8, "transparent"], [1273, 19, 1036, 19], [1274, 8, 1037, 8, "animationType"], [1274, 21, 1037, 21], [1274, 23, 1037, 22], [1274, 29, 1037, 28], [1275, 8, 1037, 28, "children"], [1275, 16, 1037, 28], [1275, 31, 1039, 8], [1275, 35, 1039, 8, "_jsxDevRuntime"], [1275, 49, 1039, 8], [1275, 50, 1039, 8, "jsxDEV"], [1275, 56, 1039, 8], [1275, 58, 1039, 9, "_View"], [1275, 63, 1039, 9], [1275, 64, 1039, 9, "default"], [1275, 71, 1039, 13], [1276, 10, 1039, 14, "style"], [1276, 15, 1039, 19], [1276, 17, 1039, 21, "styles"], [1276, 23, 1039, 27], [1276, 24, 1039, 28, "processingModal"], [1276, 39, 1039, 44], [1277, 10, 1039, 44, "children"], [1277, 18, 1039, 44], [1277, 33, 1040, 10], [1277, 37, 1040, 10, "_jsxDevRuntime"], [1277, 51, 1040, 10], [1277, 52, 1040, 10, "jsxDEV"], [1277, 58, 1040, 10], [1277, 60, 1040, 11, "_View"], [1277, 65, 1040, 11], [1277, 66, 1040, 11, "default"], [1277, 73, 1040, 15], [1278, 12, 1040, 16, "style"], [1278, 17, 1040, 21], [1278, 19, 1040, 23, "styles"], [1278, 25, 1040, 29], [1278, 26, 1040, 30, "processingContent"], [1278, 43, 1040, 48], [1279, 12, 1040, 48, "children"], [1279, 20, 1040, 48], [1279, 36, 1041, 12], [1279, 40, 1041, 12, "_jsxDevRuntime"], [1279, 54, 1041, 12], [1279, 55, 1041, 12, "jsxDEV"], [1279, 61, 1041, 12], [1279, 63, 1041, 13, "_ActivityIndicator"], [1279, 81, 1041, 13], [1279, 82, 1041, 13, "default"], [1279, 89, 1041, 30], [1280, 14, 1041, 31, "size"], [1280, 18, 1041, 35], [1280, 20, 1041, 36], [1280, 27, 1041, 43], [1281, 14, 1041, 44, "color"], [1281, 19, 1041, 49], [1281, 21, 1041, 50], [1282, 12, 1041, 59], [1283, 14, 1041, 59, "fileName"], [1283, 22, 1041, 59], [1283, 24, 1041, 59, "_jsxFileName"], [1283, 36, 1041, 59], [1284, 14, 1041, 59, "lineNumber"], [1284, 24, 1041, 59], [1285, 14, 1041, 59, "columnNumber"], [1285, 26, 1041, 59], [1286, 12, 1041, 59], [1286, 19, 1041, 61], [1286, 20, 1041, 62], [1286, 35, 1043, 12], [1286, 39, 1043, 12, "_jsxDevRuntime"], [1286, 53, 1043, 12], [1286, 54, 1043, 12, "jsxDEV"], [1286, 60, 1043, 12], [1286, 62, 1043, 13, "_Text"], [1286, 67, 1043, 13], [1286, 68, 1043, 13, "default"], [1286, 75, 1043, 17], [1287, 14, 1043, 18, "style"], [1287, 19, 1043, 23], [1287, 21, 1043, 25, "styles"], [1287, 27, 1043, 31], [1287, 28, 1043, 32, "processingTitle"], [1287, 43, 1043, 48], [1288, 14, 1043, 48, "children"], [1288, 22, 1043, 48], [1288, 25, 1044, 15, "processingState"], [1288, 40, 1044, 30], [1288, 45, 1044, 35], [1288, 56, 1044, 46], [1288, 60, 1044, 50], [1288, 80, 1044, 70], [1288, 82, 1045, 15, "processingState"], [1288, 97, 1045, 30], [1288, 102, 1045, 35], [1288, 113, 1045, 46], [1288, 117, 1045, 50], [1288, 146, 1045, 79], [1288, 148, 1046, 15, "processingState"], [1288, 163, 1046, 30], [1288, 168, 1046, 35], [1288, 180, 1046, 47], [1288, 184, 1046, 51], [1288, 216, 1046, 83], [1288, 218, 1047, 15, "processingState"], [1288, 233, 1047, 30], [1288, 238, 1047, 35], [1288, 249, 1047, 46], [1288, 253, 1047, 50], [1288, 275, 1047, 72], [1289, 12, 1047, 72], [1290, 14, 1047, 72, "fileName"], [1290, 22, 1047, 72], [1290, 24, 1047, 72, "_jsxFileName"], [1290, 36, 1047, 72], [1291, 14, 1047, 72, "lineNumber"], [1291, 24, 1047, 72], [1292, 14, 1047, 72, "columnNumber"], [1292, 26, 1047, 72], [1293, 12, 1047, 72], [1293, 19, 1048, 18], [1293, 20, 1048, 19], [1293, 35, 1049, 12], [1293, 39, 1049, 12, "_jsxDevRuntime"], [1293, 53, 1049, 12], [1293, 54, 1049, 12, "jsxDEV"], [1293, 60, 1049, 12], [1293, 62, 1049, 13, "_View"], [1293, 67, 1049, 13], [1293, 68, 1049, 13, "default"], [1293, 75, 1049, 17], [1294, 14, 1049, 18, "style"], [1294, 19, 1049, 23], [1294, 21, 1049, 25, "styles"], [1294, 27, 1049, 31], [1294, 28, 1049, 32, "progressBar"], [1294, 39, 1049, 44], [1295, 14, 1049, 44, "children"], [1295, 22, 1049, 44], [1295, 37, 1050, 14], [1295, 41, 1050, 14, "_jsxDevRuntime"], [1295, 55, 1050, 14], [1295, 56, 1050, 14, "jsxDEV"], [1295, 62, 1050, 14], [1295, 64, 1050, 15, "_View"], [1295, 69, 1050, 15], [1295, 70, 1050, 15, "default"], [1295, 77, 1050, 19], [1296, 16, 1051, 16, "style"], [1296, 21, 1051, 21], [1296, 23, 1051, 23], [1296, 24, 1052, 18, "styles"], [1296, 30, 1052, 24], [1296, 31, 1052, 25, "progressFill"], [1296, 43, 1052, 37], [1296, 45, 1053, 18], [1297, 18, 1053, 20, "width"], [1297, 23, 1053, 25], [1297, 25, 1053, 27], [1297, 28, 1053, 30, "processingProgress"], [1297, 46, 1053, 48], [1298, 16, 1053, 52], [1298, 17, 1053, 53], [1299, 14, 1054, 18], [1300, 16, 1054, 18, "fileName"], [1300, 24, 1054, 18], [1300, 26, 1054, 18, "_jsxFileName"], [1300, 38, 1054, 18], [1301, 16, 1054, 18, "lineNumber"], [1301, 26, 1054, 18], [1302, 16, 1054, 18, "columnNumber"], [1302, 28, 1054, 18], [1303, 14, 1054, 18], [1303, 21, 1055, 15], [1304, 12, 1055, 16], [1305, 14, 1055, 16, "fileName"], [1305, 22, 1055, 16], [1305, 24, 1055, 16, "_jsxFileName"], [1305, 36, 1055, 16], [1306, 14, 1055, 16, "lineNumber"], [1306, 24, 1055, 16], [1307, 14, 1055, 16, "columnNumber"], [1307, 26, 1055, 16], [1308, 12, 1055, 16], [1308, 19, 1056, 18], [1308, 20, 1056, 19], [1308, 35, 1057, 12], [1308, 39, 1057, 12, "_jsxDevRuntime"], [1308, 53, 1057, 12], [1308, 54, 1057, 12, "jsxDEV"], [1308, 60, 1057, 12], [1308, 62, 1057, 13, "_Text"], [1308, 67, 1057, 13], [1308, 68, 1057, 13, "default"], [1308, 75, 1057, 17], [1309, 14, 1057, 18, "style"], [1309, 19, 1057, 23], [1309, 21, 1057, 25, "styles"], [1309, 27, 1057, 31], [1309, 28, 1057, 32, "processingDescription"], [1309, 49, 1057, 54], [1310, 14, 1057, 54, "children"], [1310, 22, 1057, 54], [1310, 25, 1058, 15, "processingState"], [1310, 40, 1058, 30], [1310, 45, 1058, 35], [1310, 56, 1058, 46], [1310, 60, 1058, 50], [1310, 89, 1058, 79], [1310, 91, 1059, 15, "processingState"], [1310, 106, 1059, 30], [1310, 111, 1059, 35], [1310, 122, 1059, 46], [1310, 126, 1059, 50], [1310, 164, 1059, 88], [1310, 166, 1060, 15, "processingState"], [1310, 181, 1060, 30], [1310, 186, 1060, 35], [1310, 198, 1060, 47], [1310, 202, 1060, 51], [1310, 247, 1060, 96], [1310, 249, 1061, 15, "processingState"], [1310, 264, 1061, 30], [1310, 269, 1061, 35], [1310, 280, 1061, 46], [1310, 284, 1061, 50], [1310, 325, 1061, 91], [1311, 12, 1061, 91], [1312, 14, 1061, 91, "fileName"], [1312, 22, 1061, 91], [1312, 24, 1061, 91, "_jsxFileName"], [1312, 36, 1061, 91], [1313, 14, 1061, 91, "lineNumber"], [1313, 24, 1061, 91], [1314, 14, 1061, 91, "columnNumber"], [1314, 26, 1061, 91], [1315, 12, 1061, 91], [1315, 19, 1062, 18], [1315, 20, 1062, 19], [1315, 22, 1063, 13, "processingState"], [1315, 37, 1063, 28], [1315, 42, 1063, 33], [1315, 53, 1063, 44], [1315, 70, 1064, 14], [1315, 74, 1064, 14, "_jsxDevRuntime"], [1315, 88, 1064, 14], [1315, 89, 1064, 14, "jsxDEV"], [1315, 95, 1064, 14], [1315, 97, 1064, 15, "_lucideReactNative"], [1315, 115, 1064, 15], [1315, 116, 1064, 15, "CheckCircle"], [1315, 127, 1064, 26], [1316, 14, 1064, 27, "size"], [1316, 18, 1064, 31], [1316, 20, 1064, 33], [1316, 22, 1064, 36], [1317, 14, 1064, 37, "color"], [1317, 19, 1064, 42], [1317, 21, 1064, 43], [1317, 30, 1064, 52], [1318, 14, 1064, 53, "style"], [1318, 19, 1064, 58], [1318, 21, 1064, 60, "styles"], [1318, 27, 1064, 66], [1318, 28, 1064, 67, "successIcon"], [1319, 12, 1064, 79], [1320, 14, 1064, 79, "fileName"], [1320, 22, 1064, 79], [1320, 24, 1064, 79, "_jsxFileName"], [1320, 36, 1064, 79], [1321, 14, 1064, 79, "lineNumber"], [1321, 24, 1064, 79], [1322, 14, 1064, 79, "columnNumber"], [1322, 26, 1064, 79], [1323, 12, 1064, 79], [1323, 19, 1064, 81], [1323, 20, 1065, 13], [1324, 10, 1065, 13], [1325, 12, 1065, 13, "fileName"], [1325, 20, 1065, 13], [1325, 22, 1065, 13, "_jsxFileName"], [1325, 34, 1065, 13], [1326, 12, 1065, 13, "lineNumber"], [1326, 22, 1065, 13], [1327, 12, 1065, 13, "columnNumber"], [1327, 24, 1065, 13], [1328, 10, 1065, 13], [1328, 17, 1066, 16], [1329, 8, 1066, 17], [1330, 10, 1066, 17, "fileName"], [1330, 18, 1066, 17], [1330, 20, 1066, 17, "_jsxFileName"], [1330, 32, 1066, 17], [1331, 10, 1066, 17, "lineNumber"], [1331, 20, 1066, 17], [1332, 10, 1066, 17, "columnNumber"], [1332, 22, 1066, 17], [1333, 8, 1066, 17], [1333, 15, 1067, 14], [1334, 6, 1067, 15], [1335, 8, 1067, 15, "fileName"], [1335, 16, 1067, 15], [1335, 18, 1067, 15, "_jsxFileName"], [1335, 30, 1067, 15], [1336, 8, 1067, 15, "lineNumber"], [1336, 18, 1067, 15], [1337, 8, 1067, 15, "columnNumber"], [1337, 20, 1067, 15], [1338, 6, 1067, 15], [1338, 13, 1068, 13], [1338, 14, 1068, 14], [1338, 29, 1070, 6], [1338, 33, 1070, 6, "_jsxDevRuntime"], [1338, 47, 1070, 6], [1338, 48, 1070, 6, "jsxDEV"], [1338, 54, 1070, 6], [1338, 56, 1070, 7, "_Modal"], [1338, 62, 1070, 7], [1338, 63, 1070, 7, "default"], [1338, 70, 1070, 12], [1339, 8, 1071, 8, "visible"], [1339, 15, 1071, 15], [1339, 17, 1071, 17, "processingState"], [1339, 32, 1071, 32], [1339, 37, 1071, 37], [1339, 44, 1071, 45], [1340, 8, 1072, 8, "transparent"], [1340, 19, 1072, 19], [1341, 8, 1073, 8, "animationType"], [1341, 21, 1073, 21], [1341, 23, 1073, 22], [1341, 29, 1073, 28], [1342, 8, 1073, 28, "children"], [1342, 16, 1073, 28], [1342, 31, 1075, 8], [1342, 35, 1075, 8, "_jsxDevRuntime"], [1342, 49, 1075, 8], [1342, 50, 1075, 8, "jsxDEV"], [1342, 56, 1075, 8], [1342, 58, 1075, 9, "_View"], [1342, 63, 1075, 9], [1342, 64, 1075, 9, "default"], [1342, 71, 1075, 13], [1343, 10, 1075, 14, "style"], [1343, 15, 1075, 19], [1343, 17, 1075, 21, "styles"], [1343, 23, 1075, 27], [1343, 24, 1075, 28, "processingModal"], [1343, 39, 1075, 44], [1344, 10, 1075, 44, "children"], [1344, 18, 1075, 44], [1344, 33, 1076, 10], [1344, 37, 1076, 10, "_jsxDevRuntime"], [1344, 51, 1076, 10], [1344, 52, 1076, 10, "jsxDEV"], [1344, 58, 1076, 10], [1344, 60, 1076, 11, "_View"], [1344, 65, 1076, 11], [1344, 66, 1076, 11, "default"], [1344, 73, 1076, 15], [1345, 12, 1076, 16, "style"], [1345, 17, 1076, 21], [1345, 19, 1076, 23, "styles"], [1345, 25, 1076, 29], [1345, 26, 1076, 30, "errorContent"], [1345, 38, 1076, 43], [1346, 12, 1076, 43, "children"], [1346, 20, 1076, 43], [1346, 36, 1077, 12], [1346, 40, 1077, 12, "_jsxDevRuntime"], [1346, 54, 1077, 12], [1346, 55, 1077, 12, "jsxDEV"], [1346, 61, 1077, 12], [1346, 63, 1077, 13, "_lucideReactNative"], [1346, 81, 1077, 13], [1346, 82, 1077, 13, "X"], [1346, 83, 1077, 14], [1347, 14, 1077, 15, "size"], [1347, 18, 1077, 19], [1347, 20, 1077, 21], [1347, 22, 1077, 24], [1348, 14, 1077, 25, "color"], [1348, 19, 1077, 30], [1348, 21, 1077, 31], [1349, 12, 1077, 40], [1350, 14, 1077, 40, "fileName"], [1350, 22, 1077, 40], [1350, 24, 1077, 40, "_jsxFileName"], [1350, 36, 1077, 40], [1351, 14, 1077, 40, "lineNumber"], [1351, 24, 1077, 40], [1352, 14, 1077, 40, "columnNumber"], [1352, 26, 1077, 40], [1353, 12, 1077, 40], [1353, 19, 1077, 42], [1353, 20, 1077, 43], [1353, 35, 1078, 12], [1353, 39, 1078, 12, "_jsxDevRuntime"], [1353, 53, 1078, 12], [1353, 54, 1078, 12, "jsxDEV"], [1353, 60, 1078, 12], [1353, 62, 1078, 13, "_Text"], [1353, 67, 1078, 13], [1353, 68, 1078, 13, "default"], [1353, 75, 1078, 17], [1354, 14, 1078, 18, "style"], [1354, 19, 1078, 23], [1354, 21, 1078, 25, "styles"], [1354, 27, 1078, 31], [1354, 28, 1078, 32, "errorTitle"], [1354, 38, 1078, 43], [1355, 14, 1078, 43, "children"], [1355, 22, 1078, 43], [1355, 24, 1078, 44], [1356, 12, 1078, 61], [1357, 14, 1078, 61, "fileName"], [1357, 22, 1078, 61], [1357, 24, 1078, 61, "_jsxFileName"], [1357, 36, 1078, 61], [1358, 14, 1078, 61, "lineNumber"], [1358, 24, 1078, 61], [1359, 14, 1078, 61, "columnNumber"], [1359, 26, 1078, 61], [1360, 12, 1078, 61], [1360, 19, 1078, 67], [1360, 20, 1078, 68], [1360, 35, 1079, 12], [1360, 39, 1079, 12, "_jsxDevRuntime"], [1360, 53, 1079, 12], [1360, 54, 1079, 12, "jsxDEV"], [1360, 60, 1079, 12], [1360, 62, 1079, 13, "_Text"], [1360, 67, 1079, 13], [1360, 68, 1079, 13, "default"], [1360, 75, 1079, 17], [1361, 14, 1079, 18, "style"], [1361, 19, 1079, 23], [1361, 21, 1079, 25, "styles"], [1361, 27, 1079, 31], [1361, 28, 1079, 32, "errorMessage"], [1361, 40, 1079, 45], [1362, 14, 1079, 45, "children"], [1362, 22, 1079, 45], [1362, 24, 1079, 47, "errorMessage"], [1363, 12, 1079, 59], [1364, 14, 1079, 59, "fileName"], [1364, 22, 1079, 59], [1364, 24, 1079, 59, "_jsxFileName"], [1364, 36, 1079, 59], [1365, 14, 1079, 59, "lineNumber"], [1365, 24, 1079, 59], [1366, 14, 1079, 59, "columnNumber"], [1366, 26, 1079, 59], [1367, 12, 1079, 59], [1367, 19, 1079, 66], [1367, 20, 1079, 67], [1367, 35, 1080, 12], [1367, 39, 1080, 12, "_jsxDevRuntime"], [1367, 53, 1080, 12], [1367, 54, 1080, 12, "jsxDEV"], [1367, 60, 1080, 12], [1367, 62, 1080, 13, "_TouchableOpacity"], [1367, 79, 1080, 13], [1367, 80, 1080, 13, "default"], [1367, 87, 1080, 29], [1368, 14, 1081, 14, "onPress"], [1368, 21, 1081, 21], [1368, 23, 1081, 23, "retryCapture"], [1368, 35, 1081, 36], [1369, 14, 1082, 14, "style"], [1369, 19, 1082, 19], [1369, 21, 1082, 21, "styles"], [1369, 27, 1082, 27], [1369, 28, 1082, 28, "primaryButton"], [1369, 41, 1082, 42], [1370, 14, 1082, 42, "children"], [1370, 22, 1082, 42], [1370, 37, 1084, 14], [1370, 41, 1084, 14, "_jsxDevRuntime"], [1370, 55, 1084, 14], [1370, 56, 1084, 14, "jsxDEV"], [1370, 62, 1084, 14], [1370, 64, 1084, 15, "_Text"], [1370, 69, 1084, 15], [1370, 70, 1084, 15, "default"], [1370, 77, 1084, 19], [1371, 16, 1084, 20, "style"], [1371, 21, 1084, 25], [1371, 23, 1084, 27, "styles"], [1371, 29, 1084, 33], [1371, 30, 1084, 34, "primaryButtonText"], [1371, 47, 1084, 52], [1372, 16, 1084, 52, "children"], [1372, 24, 1084, 52], [1372, 26, 1084, 53], [1373, 14, 1084, 62], [1374, 16, 1084, 62, "fileName"], [1374, 24, 1084, 62], [1374, 26, 1084, 62, "_jsxFileName"], [1374, 38, 1084, 62], [1375, 16, 1084, 62, "lineNumber"], [1375, 26, 1084, 62], [1376, 16, 1084, 62, "columnNumber"], [1376, 28, 1084, 62], [1377, 14, 1084, 62], [1377, 21, 1084, 68], [1378, 12, 1084, 69], [1379, 14, 1084, 69, "fileName"], [1379, 22, 1084, 69], [1379, 24, 1084, 69, "_jsxFileName"], [1379, 36, 1084, 69], [1380, 14, 1084, 69, "lineNumber"], [1380, 24, 1084, 69], [1381, 14, 1084, 69, "columnNumber"], [1381, 26, 1084, 69], [1382, 12, 1084, 69], [1382, 19, 1085, 30], [1382, 20, 1085, 31], [1382, 35, 1086, 12], [1382, 39, 1086, 12, "_jsxDevRuntime"], [1382, 53, 1086, 12], [1382, 54, 1086, 12, "jsxDEV"], [1382, 60, 1086, 12], [1382, 62, 1086, 13, "_TouchableOpacity"], [1382, 79, 1086, 13], [1382, 80, 1086, 13, "default"], [1382, 87, 1086, 29], [1383, 14, 1087, 14, "onPress"], [1383, 21, 1087, 21], [1383, 23, 1087, 23, "onCancel"], [1383, 31, 1087, 32], [1384, 14, 1088, 14, "style"], [1384, 19, 1088, 19], [1384, 21, 1088, 21, "styles"], [1384, 27, 1088, 27], [1384, 28, 1088, 28, "secondaryButton"], [1384, 43, 1088, 44], [1385, 14, 1088, 44, "children"], [1385, 22, 1088, 44], [1385, 37, 1090, 14], [1385, 41, 1090, 14, "_jsxDevRuntime"], [1385, 55, 1090, 14], [1385, 56, 1090, 14, "jsxDEV"], [1385, 62, 1090, 14], [1385, 64, 1090, 15, "_Text"], [1385, 69, 1090, 15], [1385, 70, 1090, 15, "default"], [1385, 77, 1090, 19], [1386, 16, 1090, 20, "style"], [1386, 21, 1090, 25], [1386, 23, 1090, 27, "styles"], [1386, 29, 1090, 33], [1386, 30, 1090, 34, "secondaryButtonText"], [1386, 49, 1090, 54], [1387, 16, 1090, 54, "children"], [1387, 24, 1090, 54], [1387, 26, 1090, 55], [1388, 14, 1090, 61], [1389, 16, 1090, 61, "fileName"], [1389, 24, 1090, 61], [1389, 26, 1090, 61, "_jsxFileName"], [1389, 38, 1090, 61], [1390, 16, 1090, 61, "lineNumber"], [1390, 26, 1090, 61], [1391, 16, 1090, 61, "columnNumber"], [1391, 28, 1090, 61], [1392, 14, 1090, 61], [1392, 21, 1090, 67], [1393, 12, 1090, 68], [1394, 14, 1090, 68, "fileName"], [1394, 22, 1090, 68], [1394, 24, 1090, 68, "_jsxFileName"], [1394, 36, 1090, 68], [1395, 14, 1090, 68, "lineNumber"], [1395, 24, 1090, 68], [1396, 14, 1090, 68, "columnNumber"], [1396, 26, 1090, 68], [1397, 12, 1090, 68], [1397, 19, 1091, 30], [1397, 20, 1091, 31], [1398, 10, 1091, 31], [1399, 12, 1091, 31, "fileName"], [1399, 20, 1091, 31], [1399, 22, 1091, 31, "_jsxFileName"], [1399, 34, 1091, 31], [1400, 12, 1091, 31, "lineNumber"], [1400, 22, 1091, 31], [1401, 12, 1091, 31, "columnNumber"], [1401, 24, 1091, 31], [1402, 10, 1091, 31], [1402, 17, 1092, 16], [1403, 8, 1092, 17], [1404, 10, 1092, 17, "fileName"], [1404, 18, 1092, 17], [1404, 20, 1092, 17, "_jsxFileName"], [1404, 32, 1092, 17], [1405, 10, 1092, 17, "lineNumber"], [1405, 20, 1092, 17], [1406, 10, 1092, 17, "columnNumber"], [1406, 22, 1092, 17], [1407, 8, 1092, 17], [1407, 15, 1093, 14], [1408, 6, 1093, 15], [1409, 8, 1093, 15, "fileName"], [1409, 16, 1093, 15], [1409, 18, 1093, 15, "_jsxFileName"], [1409, 30, 1093, 15], [1410, 8, 1093, 15, "lineNumber"], [1410, 18, 1093, 15], [1411, 8, 1093, 15, "columnNumber"], [1411, 20, 1093, 15], [1412, 6, 1093, 15], [1412, 13, 1094, 13], [1412, 14, 1094, 14], [1413, 4, 1094, 14], [1414, 6, 1094, 14, "fileName"], [1414, 14, 1094, 14], [1414, 16, 1094, 14, "_jsxFileName"], [1414, 28, 1094, 14], [1415, 6, 1094, 14, "lineNumber"], [1415, 16, 1094, 14], [1416, 6, 1094, 14, "columnNumber"], [1416, 18, 1094, 14], [1417, 4, 1094, 14], [1417, 11, 1095, 10], [1417, 12, 1095, 11], [1418, 2, 1097, 0], [1419, 2, 1097, 1, "_s"], [1419, 4, 1097, 1], [1419, 5, 51, 24, "EchoCameraWeb"], [1419, 18, 51, 37], [1420, 4, 51, 37], [1420, 12, 58, 42, "useCameraPermissions"], [1420, 44, 58, 62], [1420, 46, 72, 19, "useUpload"], [1420, 64, 72, 28], [1421, 2, 72, 28], [1422, 2, 72, 28, "_c"], [1422, 4, 72, 28], [1422, 7, 51, 24, "EchoCameraWeb"], [1422, 20, 51, 37], [1423, 2, 1098, 0], [1423, 8, 1098, 6, "styles"], [1423, 14, 1098, 12], [1423, 17, 1098, 15, "StyleSheet"], [1423, 36, 1098, 25], [1423, 37, 1098, 26, "create"], [1423, 43, 1098, 32], [1423, 44, 1098, 33], [1424, 4, 1099, 2, "container"], [1424, 13, 1099, 11], [1424, 15, 1099, 13], [1425, 6, 1100, 4, "flex"], [1425, 10, 1100, 8], [1425, 12, 1100, 10], [1425, 13, 1100, 11], [1426, 6, 1101, 4, "backgroundColor"], [1426, 21, 1101, 19], [1426, 23, 1101, 21], [1427, 4, 1102, 2], [1427, 5, 1102, 3], [1428, 4, 1103, 2, "cameraContainer"], [1428, 19, 1103, 17], [1428, 21, 1103, 19], [1429, 6, 1104, 4, "flex"], [1429, 10, 1104, 8], [1429, 12, 1104, 10], [1429, 13, 1104, 11], [1430, 6, 1105, 4, "max<PERSON><PERSON><PERSON>"], [1430, 14, 1105, 12], [1430, 16, 1105, 14], [1430, 19, 1105, 17], [1431, 6, 1106, 4, "alignSelf"], [1431, 15, 1106, 13], [1431, 17, 1106, 15], [1431, 25, 1106, 23], [1432, 6, 1107, 4, "width"], [1432, 11, 1107, 9], [1432, 13, 1107, 11], [1433, 4, 1108, 2], [1433, 5, 1108, 3], [1434, 4, 1109, 2, "camera"], [1434, 10, 1109, 8], [1434, 12, 1109, 10], [1435, 6, 1110, 4, "flex"], [1435, 10, 1110, 8], [1435, 12, 1110, 10], [1436, 4, 1111, 2], [1436, 5, 1111, 3], [1437, 4, 1112, 2, "headerOverlay"], [1437, 17, 1112, 15], [1437, 19, 1112, 17], [1438, 6, 1113, 4, "position"], [1438, 14, 1113, 12], [1438, 16, 1113, 14], [1438, 26, 1113, 24], [1439, 6, 1114, 4, "top"], [1439, 9, 1114, 7], [1439, 11, 1114, 9], [1439, 12, 1114, 10], [1440, 6, 1115, 4, "left"], [1440, 10, 1115, 8], [1440, 12, 1115, 10], [1440, 13, 1115, 11], [1441, 6, 1116, 4, "right"], [1441, 11, 1116, 9], [1441, 13, 1116, 11], [1441, 14, 1116, 12], [1442, 6, 1117, 4, "backgroundColor"], [1442, 21, 1117, 19], [1442, 23, 1117, 21], [1442, 36, 1117, 34], [1443, 6, 1118, 4, "paddingTop"], [1443, 16, 1118, 14], [1443, 18, 1118, 16], [1443, 20, 1118, 18], [1444, 6, 1119, 4, "paddingHorizontal"], [1444, 23, 1119, 21], [1444, 25, 1119, 23], [1444, 27, 1119, 25], [1445, 6, 1120, 4, "paddingBottom"], [1445, 19, 1120, 17], [1445, 21, 1120, 19], [1446, 4, 1121, 2], [1446, 5, 1121, 3], [1447, 4, 1122, 2, "headerContent"], [1447, 17, 1122, 15], [1447, 19, 1122, 17], [1448, 6, 1123, 4, "flexDirection"], [1448, 19, 1123, 17], [1448, 21, 1123, 19], [1448, 26, 1123, 24], [1449, 6, 1124, 4, "justifyContent"], [1449, 20, 1124, 18], [1449, 22, 1124, 20], [1449, 37, 1124, 35], [1450, 6, 1125, 4, "alignItems"], [1450, 16, 1125, 14], [1450, 18, 1125, 16], [1451, 4, 1126, 2], [1451, 5, 1126, 3], [1452, 4, 1127, 2, "headerLeft"], [1452, 14, 1127, 12], [1452, 16, 1127, 14], [1453, 6, 1128, 4, "flex"], [1453, 10, 1128, 8], [1453, 12, 1128, 10], [1454, 4, 1129, 2], [1454, 5, 1129, 3], [1455, 4, 1130, 2, "headerTitle"], [1455, 15, 1130, 13], [1455, 17, 1130, 15], [1456, 6, 1131, 4, "fontSize"], [1456, 14, 1131, 12], [1456, 16, 1131, 14], [1456, 18, 1131, 16], [1457, 6, 1132, 4, "fontWeight"], [1457, 16, 1132, 14], [1457, 18, 1132, 16], [1457, 23, 1132, 21], [1458, 6, 1133, 4, "color"], [1458, 11, 1133, 9], [1458, 13, 1133, 11], [1458, 19, 1133, 17], [1459, 6, 1134, 4, "marginBottom"], [1459, 18, 1134, 16], [1459, 20, 1134, 18], [1460, 4, 1135, 2], [1460, 5, 1135, 3], [1461, 4, 1136, 2, "subtitleRow"], [1461, 15, 1136, 13], [1461, 17, 1136, 15], [1462, 6, 1137, 4, "flexDirection"], [1462, 19, 1137, 17], [1462, 21, 1137, 19], [1462, 26, 1137, 24], [1463, 6, 1138, 4, "alignItems"], [1463, 16, 1138, 14], [1463, 18, 1138, 16], [1463, 26, 1138, 24], [1464, 6, 1139, 4, "marginBottom"], [1464, 18, 1139, 16], [1464, 20, 1139, 18], [1465, 4, 1140, 2], [1465, 5, 1140, 3], [1466, 4, 1141, 2, "webIcon"], [1466, 11, 1141, 9], [1466, 13, 1141, 11], [1467, 6, 1142, 4, "fontSize"], [1467, 14, 1142, 12], [1467, 16, 1142, 14], [1467, 18, 1142, 16], [1468, 6, 1143, 4, "marginRight"], [1468, 17, 1143, 15], [1468, 19, 1143, 17], [1469, 4, 1144, 2], [1469, 5, 1144, 3], [1470, 4, 1145, 2, "headerSubtitle"], [1470, 18, 1145, 16], [1470, 20, 1145, 18], [1471, 6, 1146, 4, "fontSize"], [1471, 14, 1146, 12], [1471, 16, 1146, 14], [1471, 18, 1146, 16], [1472, 6, 1147, 4, "color"], [1472, 11, 1147, 9], [1472, 13, 1147, 11], [1472, 19, 1147, 17], [1473, 6, 1148, 4, "opacity"], [1473, 13, 1148, 11], [1473, 15, 1148, 13], [1474, 4, 1149, 2], [1474, 5, 1149, 3], [1475, 4, 1150, 2, "challengeRow"], [1475, 16, 1150, 14], [1475, 18, 1150, 16], [1476, 6, 1151, 4, "flexDirection"], [1476, 19, 1151, 17], [1476, 21, 1151, 19], [1476, 26, 1151, 24], [1477, 6, 1152, 4, "alignItems"], [1477, 16, 1152, 14], [1477, 18, 1152, 16], [1478, 4, 1153, 2], [1478, 5, 1153, 3], [1479, 4, 1154, 2, "challengeCode"], [1479, 17, 1154, 15], [1479, 19, 1154, 17], [1480, 6, 1155, 4, "fontSize"], [1480, 14, 1155, 12], [1480, 16, 1155, 14], [1480, 18, 1155, 16], [1481, 6, 1156, 4, "color"], [1481, 11, 1156, 9], [1481, 13, 1156, 11], [1481, 19, 1156, 17], [1482, 6, 1157, 4, "marginLeft"], [1482, 16, 1157, 14], [1482, 18, 1157, 16], [1482, 19, 1157, 17], [1483, 6, 1158, 4, "fontFamily"], [1483, 16, 1158, 14], [1483, 18, 1158, 16], [1484, 4, 1159, 2], [1484, 5, 1159, 3], [1485, 4, 1160, 2, "closeButton"], [1485, 15, 1160, 13], [1485, 17, 1160, 15], [1486, 6, 1161, 4, "padding"], [1486, 13, 1161, 11], [1486, 15, 1161, 13], [1487, 4, 1162, 2], [1487, 5, 1162, 3], [1488, 4, 1163, 2, "privacyNotice"], [1488, 17, 1163, 15], [1488, 19, 1163, 17], [1489, 6, 1164, 4, "position"], [1489, 14, 1164, 12], [1489, 16, 1164, 14], [1489, 26, 1164, 24], [1490, 6, 1165, 4, "top"], [1490, 9, 1165, 7], [1490, 11, 1165, 9], [1490, 14, 1165, 12], [1491, 6, 1166, 4, "left"], [1491, 10, 1166, 8], [1491, 12, 1166, 10], [1491, 14, 1166, 12], [1492, 6, 1167, 4, "right"], [1492, 11, 1167, 9], [1492, 13, 1167, 11], [1492, 15, 1167, 13], [1493, 6, 1168, 4, "backgroundColor"], [1493, 21, 1168, 19], [1493, 23, 1168, 21], [1493, 48, 1168, 46], [1494, 6, 1169, 4, "borderRadius"], [1494, 18, 1169, 16], [1494, 20, 1169, 18], [1494, 21, 1169, 19], [1495, 6, 1170, 4, "padding"], [1495, 13, 1170, 11], [1495, 15, 1170, 13], [1495, 17, 1170, 15], [1496, 6, 1171, 4, "flexDirection"], [1496, 19, 1171, 17], [1496, 21, 1171, 19], [1496, 26, 1171, 24], [1497, 6, 1172, 4, "alignItems"], [1497, 16, 1172, 14], [1497, 18, 1172, 16], [1498, 4, 1173, 2], [1498, 5, 1173, 3], [1499, 4, 1174, 2, "privacyText"], [1499, 15, 1174, 13], [1499, 17, 1174, 15], [1500, 6, 1175, 4, "color"], [1500, 11, 1175, 9], [1500, 13, 1175, 11], [1500, 19, 1175, 17], [1501, 6, 1176, 4, "fontSize"], [1501, 14, 1176, 12], [1501, 16, 1176, 14], [1501, 18, 1176, 16], [1502, 6, 1177, 4, "marginLeft"], [1502, 16, 1177, 14], [1502, 18, 1177, 16], [1502, 19, 1177, 17], [1503, 6, 1178, 4, "flex"], [1503, 10, 1178, 8], [1503, 12, 1178, 10], [1504, 4, 1179, 2], [1504, 5, 1179, 3], [1505, 4, 1180, 2, "footer<PERSON><PERSON><PERSON>"], [1505, 17, 1180, 15], [1505, 19, 1180, 17], [1506, 6, 1181, 4, "position"], [1506, 14, 1181, 12], [1506, 16, 1181, 14], [1506, 26, 1181, 24], [1507, 6, 1182, 4, "bottom"], [1507, 12, 1182, 10], [1507, 14, 1182, 12], [1507, 15, 1182, 13], [1508, 6, 1183, 4, "left"], [1508, 10, 1183, 8], [1508, 12, 1183, 10], [1508, 13, 1183, 11], [1509, 6, 1184, 4, "right"], [1509, 11, 1184, 9], [1509, 13, 1184, 11], [1509, 14, 1184, 12], [1510, 6, 1185, 4, "backgroundColor"], [1510, 21, 1185, 19], [1510, 23, 1185, 21], [1510, 36, 1185, 34], [1511, 6, 1186, 4, "paddingBottom"], [1511, 19, 1186, 17], [1511, 21, 1186, 19], [1511, 23, 1186, 21], [1512, 6, 1187, 4, "paddingTop"], [1512, 16, 1187, 14], [1512, 18, 1187, 16], [1512, 20, 1187, 18], [1513, 6, 1188, 4, "alignItems"], [1513, 16, 1188, 14], [1513, 18, 1188, 16], [1514, 4, 1189, 2], [1514, 5, 1189, 3], [1515, 4, 1190, 2, "instruction"], [1515, 15, 1190, 13], [1515, 17, 1190, 15], [1516, 6, 1191, 4, "fontSize"], [1516, 14, 1191, 12], [1516, 16, 1191, 14], [1516, 18, 1191, 16], [1517, 6, 1192, 4, "color"], [1517, 11, 1192, 9], [1517, 13, 1192, 11], [1517, 19, 1192, 17], [1518, 6, 1193, 4, "marginBottom"], [1518, 18, 1193, 16], [1518, 20, 1193, 18], [1519, 4, 1194, 2], [1519, 5, 1194, 3], [1520, 4, 1195, 2, "shutterButton"], [1520, 17, 1195, 15], [1520, 19, 1195, 17], [1521, 6, 1196, 4, "width"], [1521, 11, 1196, 9], [1521, 13, 1196, 11], [1521, 15, 1196, 13], [1522, 6, 1197, 4, "height"], [1522, 12, 1197, 10], [1522, 14, 1197, 12], [1522, 16, 1197, 14], [1523, 6, 1198, 4, "borderRadius"], [1523, 18, 1198, 16], [1523, 20, 1198, 18], [1523, 22, 1198, 20], [1524, 6, 1199, 4, "backgroundColor"], [1524, 21, 1199, 19], [1524, 23, 1199, 21], [1524, 29, 1199, 27], [1525, 6, 1200, 4, "justifyContent"], [1525, 20, 1200, 18], [1525, 22, 1200, 20], [1525, 30, 1200, 28], [1526, 6, 1201, 4, "alignItems"], [1526, 16, 1201, 14], [1526, 18, 1201, 16], [1526, 26, 1201, 24], [1527, 6, 1202, 4, "marginBottom"], [1527, 18, 1202, 16], [1527, 20, 1202, 18], [1527, 22, 1202, 20], [1528, 6, 1203, 4], [1528, 9, 1203, 7, "Platform"], [1528, 26, 1203, 15], [1528, 27, 1203, 16, "select"], [1528, 33, 1203, 22], [1528, 34, 1203, 23], [1529, 8, 1204, 6, "ios"], [1529, 11, 1204, 9], [1529, 13, 1204, 11], [1530, 10, 1205, 8, "shadowColor"], [1530, 21, 1205, 19], [1530, 23, 1205, 21], [1530, 32, 1205, 30], [1531, 10, 1206, 8, "shadowOffset"], [1531, 22, 1206, 20], [1531, 24, 1206, 22], [1532, 12, 1206, 24, "width"], [1532, 17, 1206, 29], [1532, 19, 1206, 31], [1532, 20, 1206, 32], [1533, 12, 1206, 34, "height"], [1533, 18, 1206, 40], [1533, 20, 1206, 42], [1534, 10, 1206, 44], [1534, 11, 1206, 45], [1535, 10, 1207, 8, "shadowOpacity"], [1535, 23, 1207, 21], [1535, 25, 1207, 23], [1535, 28, 1207, 26], [1536, 10, 1208, 8, "shadowRadius"], [1536, 22, 1208, 20], [1536, 24, 1208, 22], [1537, 8, 1209, 6], [1537, 9, 1209, 7], [1538, 8, 1210, 6, "android"], [1538, 15, 1210, 13], [1538, 17, 1210, 15], [1539, 10, 1211, 8, "elevation"], [1539, 19, 1211, 17], [1539, 21, 1211, 19], [1540, 8, 1212, 6], [1540, 9, 1212, 7], [1541, 8, 1213, 6, "web"], [1541, 11, 1213, 9], [1541, 13, 1213, 11], [1542, 10, 1214, 8, "boxShadow"], [1542, 19, 1214, 17], [1542, 21, 1214, 19], [1543, 8, 1215, 6], [1544, 6, 1216, 4], [1544, 7, 1216, 5], [1545, 4, 1217, 2], [1545, 5, 1217, 3], [1546, 4, 1218, 2, "shutterButtonDisabled"], [1546, 25, 1218, 23], [1546, 27, 1218, 25], [1547, 6, 1219, 4, "opacity"], [1547, 13, 1219, 11], [1547, 15, 1219, 13], [1548, 4, 1220, 2], [1548, 5, 1220, 3], [1549, 4, 1221, 2, "shutterInner"], [1549, 16, 1221, 14], [1549, 18, 1221, 16], [1550, 6, 1222, 4, "width"], [1550, 11, 1222, 9], [1550, 13, 1222, 11], [1550, 15, 1222, 13], [1551, 6, 1223, 4, "height"], [1551, 12, 1223, 10], [1551, 14, 1223, 12], [1551, 16, 1223, 14], [1552, 6, 1224, 4, "borderRadius"], [1552, 18, 1224, 16], [1552, 20, 1224, 18], [1552, 22, 1224, 20], [1553, 6, 1225, 4, "backgroundColor"], [1553, 21, 1225, 19], [1553, 23, 1225, 21], [1553, 29, 1225, 27], [1554, 6, 1226, 4, "borderWidth"], [1554, 17, 1226, 15], [1554, 19, 1226, 17], [1554, 20, 1226, 18], [1555, 6, 1227, 4, "borderColor"], [1555, 17, 1227, 15], [1555, 19, 1227, 17], [1556, 4, 1228, 2], [1556, 5, 1228, 3], [1557, 4, 1229, 2, "privacyNote"], [1557, 15, 1229, 13], [1557, 17, 1229, 15], [1558, 6, 1230, 4, "fontSize"], [1558, 14, 1230, 12], [1558, 16, 1230, 14], [1558, 18, 1230, 16], [1559, 6, 1231, 4, "color"], [1559, 11, 1231, 9], [1559, 13, 1231, 11], [1560, 4, 1232, 2], [1560, 5, 1232, 3], [1561, 4, 1233, 2, "processingModal"], [1561, 19, 1233, 17], [1561, 21, 1233, 19], [1562, 6, 1234, 4, "flex"], [1562, 10, 1234, 8], [1562, 12, 1234, 10], [1562, 13, 1234, 11], [1563, 6, 1235, 4, "backgroundColor"], [1563, 21, 1235, 19], [1563, 23, 1235, 21], [1563, 43, 1235, 41], [1564, 6, 1236, 4, "justifyContent"], [1564, 20, 1236, 18], [1564, 22, 1236, 20], [1564, 30, 1236, 28], [1565, 6, 1237, 4, "alignItems"], [1565, 16, 1237, 14], [1565, 18, 1237, 16], [1566, 4, 1238, 2], [1566, 5, 1238, 3], [1567, 4, 1239, 2, "processingContent"], [1567, 21, 1239, 19], [1567, 23, 1239, 21], [1568, 6, 1240, 4, "backgroundColor"], [1568, 21, 1240, 19], [1568, 23, 1240, 21], [1568, 29, 1240, 27], [1569, 6, 1241, 4, "borderRadius"], [1569, 18, 1241, 16], [1569, 20, 1241, 18], [1569, 22, 1241, 20], [1570, 6, 1242, 4, "padding"], [1570, 13, 1242, 11], [1570, 15, 1242, 13], [1570, 17, 1242, 15], [1571, 6, 1243, 4, "width"], [1571, 11, 1243, 9], [1571, 13, 1243, 11], [1571, 18, 1243, 16], [1572, 6, 1244, 4, "max<PERSON><PERSON><PERSON>"], [1572, 14, 1244, 12], [1572, 16, 1244, 14], [1572, 19, 1244, 17], [1573, 6, 1245, 4, "alignItems"], [1573, 16, 1245, 14], [1573, 18, 1245, 16], [1574, 4, 1246, 2], [1574, 5, 1246, 3], [1575, 4, 1247, 2, "processingTitle"], [1575, 19, 1247, 17], [1575, 21, 1247, 19], [1576, 6, 1248, 4, "fontSize"], [1576, 14, 1248, 12], [1576, 16, 1248, 14], [1576, 18, 1248, 16], [1577, 6, 1249, 4, "fontWeight"], [1577, 16, 1249, 14], [1577, 18, 1249, 16], [1577, 23, 1249, 21], [1578, 6, 1250, 4, "color"], [1578, 11, 1250, 9], [1578, 13, 1250, 11], [1578, 22, 1250, 20], [1579, 6, 1251, 4, "marginTop"], [1579, 15, 1251, 13], [1579, 17, 1251, 15], [1579, 19, 1251, 17], [1580, 6, 1252, 4, "marginBottom"], [1580, 18, 1252, 16], [1580, 20, 1252, 18], [1581, 4, 1253, 2], [1581, 5, 1253, 3], [1582, 4, 1254, 2, "progressBar"], [1582, 15, 1254, 13], [1582, 17, 1254, 15], [1583, 6, 1255, 4, "width"], [1583, 11, 1255, 9], [1583, 13, 1255, 11], [1583, 19, 1255, 17], [1584, 6, 1256, 4, "height"], [1584, 12, 1256, 10], [1584, 14, 1256, 12], [1584, 15, 1256, 13], [1585, 6, 1257, 4, "backgroundColor"], [1585, 21, 1257, 19], [1585, 23, 1257, 21], [1585, 32, 1257, 30], [1586, 6, 1258, 4, "borderRadius"], [1586, 18, 1258, 16], [1586, 20, 1258, 18], [1586, 21, 1258, 19], [1587, 6, 1259, 4, "overflow"], [1587, 14, 1259, 12], [1587, 16, 1259, 14], [1587, 24, 1259, 22], [1588, 6, 1260, 4, "marginBottom"], [1588, 18, 1260, 16], [1588, 20, 1260, 18], [1589, 4, 1261, 2], [1589, 5, 1261, 3], [1590, 4, 1262, 2, "progressFill"], [1590, 16, 1262, 14], [1590, 18, 1262, 16], [1591, 6, 1263, 4, "height"], [1591, 12, 1263, 10], [1591, 14, 1263, 12], [1591, 20, 1263, 18], [1592, 6, 1264, 4, "backgroundColor"], [1592, 21, 1264, 19], [1592, 23, 1264, 21], [1592, 32, 1264, 30], [1593, 6, 1265, 4, "borderRadius"], [1593, 18, 1265, 16], [1593, 20, 1265, 18], [1594, 4, 1266, 2], [1594, 5, 1266, 3], [1595, 4, 1267, 2, "processingDescription"], [1595, 25, 1267, 23], [1595, 27, 1267, 25], [1596, 6, 1268, 4, "fontSize"], [1596, 14, 1268, 12], [1596, 16, 1268, 14], [1596, 18, 1268, 16], [1597, 6, 1269, 4, "color"], [1597, 11, 1269, 9], [1597, 13, 1269, 11], [1597, 22, 1269, 20], [1598, 6, 1270, 4, "textAlign"], [1598, 15, 1270, 13], [1598, 17, 1270, 15], [1599, 4, 1271, 2], [1599, 5, 1271, 3], [1600, 4, 1272, 2, "successIcon"], [1600, 15, 1272, 13], [1600, 17, 1272, 15], [1601, 6, 1273, 4, "marginTop"], [1601, 15, 1273, 13], [1601, 17, 1273, 15], [1602, 4, 1274, 2], [1602, 5, 1274, 3], [1603, 4, 1275, 2, "errorContent"], [1603, 16, 1275, 14], [1603, 18, 1275, 16], [1604, 6, 1276, 4, "backgroundColor"], [1604, 21, 1276, 19], [1604, 23, 1276, 21], [1604, 29, 1276, 27], [1605, 6, 1277, 4, "borderRadius"], [1605, 18, 1277, 16], [1605, 20, 1277, 18], [1605, 22, 1277, 20], [1606, 6, 1278, 4, "padding"], [1606, 13, 1278, 11], [1606, 15, 1278, 13], [1606, 17, 1278, 15], [1607, 6, 1279, 4, "width"], [1607, 11, 1279, 9], [1607, 13, 1279, 11], [1607, 18, 1279, 16], [1608, 6, 1280, 4, "max<PERSON><PERSON><PERSON>"], [1608, 14, 1280, 12], [1608, 16, 1280, 14], [1608, 19, 1280, 17], [1609, 6, 1281, 4, "alignItems"], [1609, 16, 1281, 14], [1609, 18, 1281, 16], [1610, 4, 1282, 2], [1610, 5, 1282, 3], [1611, 4, 1283, 2, "errorTitle"], [1611, 14, 1283, 12], [1611, 16, 1283, 14], [1612, 6, 1284, 4, "fontSize"], [1612, 14, 1284, 12], [1612, 16, 1284, 14], [1612, 18, 1284, 16], [1613, 6, 1285, 4, "fontWeight"], [1613, 16, 1285, 14], [1613, 18, 1285, 16], [1613, 23, 1285, 21], [1614, 6, 1286, 4, "color"], [1614, 11, 1286, 9], [1614, 13, 1286, 11], [1614, 22, 1286, 20], [1615, 6, 1287, 4, "marginTop"], [1615, 15, 1287, 13], [1615, 17, 1287, 15], [1615, 19, 1287, 17], [1616, 6, 1288, 4, "marginBottom"], [1616, 18, 1288, 16], [1616, 20, 1288, 18], [1617, 4, 1289, 2], [1617, 5, 1289, 3], [1618, 4, 1290, 2, "errorMessage"], [1618, 16, 1290, 14], [1618, 18, 1290, 16], [1619, 6, 1291, 4, "fontSize"], [1619, 14, 1291, 12], [1619, 16, 1291, 14], [1619, 18, 1291, 16], [1620, 6, 1292, 4, "color"], [1620, 11, 1292, 9], [1620, 13, 1292, 11], [1620, 22, 1292, 20], [1621, 6, 1293, 4, "textAlign"], [1621, 15, 1293, 13], [1621, 17, 1293, 15], [1621, 25, 1293, 23], [1622, 6, 1294, 4, "marginBottom"], [1622, 18, 1294, 16], [1622, 20, 1294, 18], [1623, 4, 1295, 2], [1623, 5, 1295, 3], [1624, 4, 1296, 2, "primaryButton"], [1624, 17, 1296, 15], [1624, 19, 1296, 17], [1625, 6, 1297, 4, "backgroundColor"], [1625, 21, 1297, 19], [1625, 23, 1297, 21], [1625, 32, 1297, 30], [1626, 6, 1298, 4, "paddingHorizontal"], [1626, 23, 1298, 21], [1626, 25, 1298, 23], [1626, 27, 1298, 25], [1627, 6, 1299, 4, "paddingVertical"], [1627, 21, 1299, 19], [1627, 23, 1299, 21], [1627, 25, 1299, 23], [1628, 6, 1300, 4, "borderRadius"], [1628, 18, 1300, 16], [1628, 20, 1300, 18], [1628, 21, 1300, 19], [1629, 6, 1301, 4, "marginTop"], [1629, 15, 1301, 13], [1629, 17, 1301, 15], [1630, 4, 1302, 2], [1630, 5, 1302, 3], [1631, 4, 1303, 2, "primaryButtonText"], [1631, 21, 1303, 19], [1631, 23, 1303, 21], [1632, 6, 1304, 4, "color"], [1632, 11, 1304, 9], [1632, 13, 1304, 11], [1632, 19, 1304, 17], [1633, 6, 1305, 4, "fontSize"], [1633, 14, 1305, 12], [1633, 16, 1305, 14], [1633, 18, 1305, 16], [1634, 6, 1306, 4, "fontWeight"], [1634, 16, 1306, 14], [1634, 18, 1306, 16], [1635, 4, 1307, 2], [1635, 5, 1307, 3], [1636, 4, 1308, 2, "secondaryButton"], [1636, 19, 1308, 17], [1636, 21, 1308, 19], [1637, 6, 1309, 4, "paddingHorizontal"], [1637, 23, 1309, 21], [1637, 25, 1309, 23], [1637, 27, 1309, 25], [1638, 6, 1310, 4, "paddingVertical"], [1638, 21, 1310, 19], [1638, 23, 1310, 21], [1638, 25, 1310, 23], [1639, 6, 1311, 4, "marginTop"], [1639, 15, 1311, 13], [1639, 17, 1311, 15], [1640, 4, 1312, 2], [1640, 5, 1312, 3], [1641, 4, 1313, 2, "secondaryButtonText"], [1641, 23, 1313, 21], [1641, 25, 1313, 23], [1642, 6, 1314, 4, "color"], [1642, 11, 1314, 9], [1642, 13, 1314, 11], [1642, 22, 1314, 20], [1643, 6, 1315, 4, "fontSize"], [1643, 14, 1315, 12], [1643, 16, 1315, 14], [1644, 4, 1316, 2], [1644, 5, 1316, 3], [1645, 4, 1317, 2, "permissionContent"], [1645, 21, 1317, 19], [1645, 23, 1317, 21], [1646, 6, 1318, 4, "flex"], [1646, 10, 1318, 8], [1646, 12, 1318, 10], [1646, 13, 1318, 11], [1647, 6, 1319, 4, "justifyContent"], [1647, 20, 1319, 18], [1647, 22, 1319, 20], [1647, 30, 1319, 28], [1648, 6, 1320, 4, "alignItems"], [1648, 16, 1320, 14], [1648, 18, 1320, 16], [1648, 26, 1320, 24], [1649, 6, 1321, 4, "padding"], [1649, 13, 1321, 11], [1649, 15, 1321, 13], [1650, 4, 1322, 2], [1650, 5, 1322, 3], [1651, 4, 1323, 2, "permissionTitle"], [1651, 19, 1323, 17], [1651, 21, 1323, 19], [1652, 6, 1324, 4, "fontSize"], [1652, 14, 1324, 12], [1652, 16, 1324, 14], [1652, 18, 1324, 16], [1653, 6, 1325, 4, "fontWeight"], [1653, 16, 1325, 14], [1653, 18, 1325, 16], [1653, 23, 1325, 21], [1654, 6, 1326, 4, "color"], [1654, 11, 1326, 9], [1654, 13, 1326, 11], [1654, 22, 1326, 20], [1655, 6, 1327, 4, "marginTop"], [1655, 15, 1327, 13], [1655, 17, 1327, 15], [1655, 19, 1327, 17], [1656, 6, 1328, 4, "marginBottom"], [1656, 18, 1328, 16], [1656, 20, 1328, 18], [1657, 4, 1329, 2], [1657, 5, 1329, 3], [1658, 4, 1330, 2, "permissionDescription"], [1658, 25, 1330, 23], [1658, 27, 1330, 25], [1659, 6, 1331, 4, "fontSize"], [1659, 14, 1331, 12], [1659, 16, 1331, 14], [1659, 18, 1331, 16], [1660, 6, 1332, 4, "color"], [1660, 11, 1332, 9], [1660, 13, 1332, 11], [1660, 22, 1332, 20], [1661, 6, 1333, 4, "textAlign"], [1661, 15, 1333, 13], [1661, 17, 1333, 15], [1661, 25, 1333, 23], [1662, 6, 1334, 4, "marginBottom"], [1662, 18, 1334, 16], [1662, 20, 1334, 18], [1663, 4, 1335, 2], [1663, 5, 1335, 3], [1664, 4, 1336, 2, "loadingText"], [1664, 15, 1336, 13], [1664, 17, 1336, 15], [1665, 6, 1337, 4, "color"], [1665, 11, 1337, 9], [1665, 13, 1337, 11], [1665, 22, 1337, 20], [1666, 6, 1338, 4, "marginTop"], [1666, 15, 1338, 13], [1666, 17, 1338, 15], [1667, 4, 1339, 2], [1667, 5, 1339, 3], [1668, 4, 1340, 2], [1669, 4, 1341, 2, "blurZone"], [1669, 12, 1341, 10], [1669, 14, 1341, 12], [1670, 6, 1342, 4, "position"], [1670, 14, 1342, 12], [1670, 16, 1342, 14], [1670, 26, 1342, 24], [1671, 6, 1343, 4, "overflow"], [1671, 14, 1343, 12], [1671, 16, 1343, 14], [1672, 4, 1344, 2], [1672, 5, 1344, 3], [1673, 4, 1345, 2, "previewChip"], [1673, 15, 1345, 13], [1673, 17, 1345, 15], [1674, 6, 1346, 4, "position"], [1674, 14, 1346, 12], [1674, 16, 1346, 14], [1674, 26, 1346, 24], [1675, 6, 1347, 4, "top"], [1675, 9, 1347, 7], [1675, 11, 1347, 9], [1675, 12, 1347, 10], [1676, 6, 1348, 4, "right"], [1676, 11, 1348, 9], [1676, 13, 1348, 11], [1676, 14, 1348, 12], [1677, 6, 1349, 4, "backgroundColor"], [1677, 21, 1349, 19], [1677, 23, 1349, 21], [1677, 40, 1349, 38], [1678, 6, 1350, 4, "paddingHorizontal"], [1678, 23, 1350, 21], [1678, 25, 1350, 23], [1678, 27, 1350, 25], [1679, 6, 1351, 4, "paddingVertical"], [1679, 21, 1351, 19], [1679, 23, 1351, 21], [1679, 24, 1351, 22], [1680, 6, 1352, 4, "borderRadius"], [1680, 18, 1352, 16], [1680, 20, 1352, 18], [1681, 4, 1353, 2], [1681, 5, 1353, 3], [1682, 4, 1354, 2, "previewChipText"], [1682, 19, 1354, 17], [1682, 21, 1354, 19], [1683, 6, 1355, 4, "color"], [1683, 11, 1355, 9], [1683, 13, 1355, 11], [1683, 19, 1355, 17], [1684, 6, 1356, 4, "fontSize"], [1684, 14, 1356, 12], [1684, 16, 1356, 14], [1684, 18, 1356, 16], [1685, 6, 1357, 4, "fontWeight"], [1685, 16, 1357, 14], [1685, 18, 1357, 16], [1686, 4, 1358, 2], [1687, 2, 1359, 0], [1687, 3, 1359, 1], [1687, 4, 1359, 2], [1688, 2, 1359, 3], [1688, 6, 1359, 3, "_c"], [1688, 8, 1359, 3], [1689, 2, 1359, 3, "$RefreshReg$"], [1689, 14, 1359, 3], [1689, 15, 1359, 3, "_c"], [1689, 17, 1359, 3], [1690, 0, 1359, 3], [1690, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfmE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBa;OgBI;oFCyC;UDM;8BEW;SF0C;uDhBa;sBmBC,wBnB;OgBC;GpBmB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}