{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: Helper functions for robust face detection\n    const loadMediaPipeFaceDetection = async () => {\n      // Try to load MediaPipe Face Detection from CDN\n      if (!window.FaceDetection) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/face_detection@0.4.1646425229/face_detection.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n    };\n    const detectFacesWithMediaPipe = async img => {\n      // This is a placeholder - MediaPipe integration would be complex\n      // For now, return empty array to fall back to heuristic\n      return [];\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Simple skin tone detection and face-like region finding\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 20; // Adaptive block size\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize) {\n        for (let x = 0; x < img.width - blockSize; x += blockSize) {\n          const skinPixels = countSkinPixelsInRegion(data, x, y, blockSize, img.width);\n          const skinRatio = skinPixels / (blockSize * blockSize);\n\n          // If this region has a high concentration of skin-like pixels\n          if (skinRatio > 0.3) {\n            // Check if it's in a face-like position (upper 2/3 of image)\n            if (y < img.height * 0.67) {\n              faces.push({\n                boundingBox: {\n                  xCenter: (x + blockSize / 2) / img.width,\n                  yCenter: (y + blockSize / 2) / img.height,\n                  width: blockSize * 2 / img.width,\n                  // Make bounding box larger\n                  height: blockSize * 2.5 / img.height\n                }\n              });\n              console.log(`[EchoCameraWeb] 🎯 Found face-like region at (${x}, ${y}) with ${(skinRatio * 100).toFixed(1)}% skin pixels`);\n            }\n          }\n        }\n      }\n\n      // Merge overlapping detections and limit to most likely faces\n      const mergedFaces = mergeFaceDetections(faces);\n      return mergedFaces.slice(0, 5); // Max 5 faces\n    };\n    const countSkinPixelsInRegion = (data, startX, startY, size, imageWidth) => {\n      let skinPixels = 0;\n      for (let y = startY; y < startY + size; y++) {\n        for (let x = startX; x < startX + size; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Simple skin tone detection\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n        }\n      }\n      return skinPixels;\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Simple but effective face detection using multiple strategies\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting NEW face detection system...');\n\n        // Strategy 1: Try MediaPipe Face Detection (more reliable)\n        try {\n          await loadMediaPipeFaceDetection();\n          detectedFaces = await detectFacesWithMediaPipe(img);\n          console.log(`[EchoCameraWeb] ✅ MediaPipe found ${detectedFaces.length} faces`);\n        } catch (mediaPipeError) {\n          console.warn('[EchoCameraWeb] ❌ MediaPipe failed:', mediaPipeError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Using intelligent heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1412, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadMediaPipeFaceDetection"], [91, 36, 91, 34], [91, 39, 91, 37], [91, 45, 91, 37, "loadMediaPipeFaceDetection"], [91, 46, 91, 37], [91, 51, 91, 49], [92, 6, 92, 4], [93, 6, 93, 4], [93, 10, 93, 8], [93, 11, 93, 10, "window"], [93, 17, 93, 16], [93, 18, 93, 25, "FaceDetection"], [93, 31, 93, 38], [93, 33, 93, 40], [94, 8, 94, 6], [94, 14, 94, 12], [94, 18, 94, 16, "Promise"], [94, 25, 94, 23], [94, 26, 94, 24], [94, 27, 94, 25, "resolve"], [94, 34, 94, 32], [94, 36, 94, 34, "reject"], [94, 42, 94, 40], [94, 47, 94, 45], [95, 10, 95, 8], [95, 16, 95, 14, "script"], [95, 22, 95, 20], [95, 25, 95, 23, "document"], [95, 33, 95, 31], [95, 34, 95, 32, "createElement"], [95, 47, 95, 45], [95, 48, 95, 46], [95, 56, 95, 54], [95, 57, 95, 55], [96, 10, 96, 8, "script"], [96, 16, 96, 14], [96, 17, 96, 15, "src"], [96, 20, 96, 18], [96, 23, 96, 21], [96, 112, 96, 110], [97, 10, 97, 8, "script"], [97, 16, 97, 14], [97, 17, 97, 15, "onload"], [97, 23, 97, 21], [97, 26, 97, 24, "resolve"], [97, 33, 97, 31], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "onerror"], [98, 24, 98, 22], [98, 27, 98, 25, "reject"], [98, 33, 98, 31], [99, 10, 99, 8, "document"], [99, 18, 99, 16], [99, 19, 99, 17, "head"], [99, 23, 99, 21], [99, 24, 99, 22, "append<PERSON><PERSON><PERSON>"], [99, 35, 99, 33], [99, 36, 99, 34, "script"], [99, 42, 99, 40], [99, 43, 99, 41], [100, 8, 100, 6], [100, 9, 100, 7], [100, 10, 100, 8], [101, 6, 101, 4], [102, 4, 102, 2], [102, 5, 102, 3], [103, 4, 104, 2], [103, 10, 104, 8, "detectFacesWithMediaPipe"], [103, 34, 104, 32], [103, 37, 104, 35], [103, 43, 104, 42, "img"], [103, 46, 104, 63], [103, 50, 104, 68], [104, 6, 105, 4], [105, 6, 106, 4], [106, 6, 107, 4], [106, 13, 107, 11], [106, 15, 107, 13], [107, 4, 108, 2], [107, 5, 108, 3], [108, 4, 110, 2], [108, 10, 110, 8, "detectFacesHeuristic"], [108, 30, 110, 28], [108, 33, 110, 31, "detectFacesHeuristic"], [108, 34, 110, 32, "img"], [108, 37, 110, 53], [108, 39, 110, 55, "ctx"], [108, 42, 110, 84], [108, 47, 110, 89], [109, 6, 111, 4, "console"], [109, 13, 111, 11], [109, 14, 111, 12, "log"], [109, 17, 111, 15], [109, 18, 111, 16], [109, 74, 111, 72], [109, 75, 111, 73], [111, 6, 113, 4], [112, 6, 114, 4], [112, 12, 114, 10, "imageData"], [112, 21, 114, 19], [112, 24, 114, 22, "ctx"], [112, 27, 114, 25], [112, 28, 114, 26, "getImageData"], [112, 40, 114, 38], [112, 41, 114, 39], [112, 42, 114, 40], [112, 44, 114, 42], [112, 45, 114, 43], [112, 47, 114, 45, "img"], [112, 50, 114, 48], [112, 51, 114, 49, "width"], [112, 56, 114, 54], [112, 58, 114, 56, "img"], [112, 61, 114, 59], [112, 62, 114, 60, "height"], [112, 68, 114, 66], [112, 69, 114, 67], [113, 6, 115, 4], [113, 12, 115, 10, "data"], [113, 16, 115, 14], [113, 19, 115, 17, "imageData"], [113, 28, 115, 26], [113, 29, 115, 27, "data"], [113, 33, 115, 31], [115, 6, 117, 4], [116, 6, 118, 4], [116, 12, 118, 10, "faces"], [116, 17, 118, 15], [116, 20, 118, 18], [116, 22, 118, 20], [117, 6, 119, 4], [117, 12, 119, 10, "blockSize"], [117, 21, 119, 19], [117, 24, 119, 22, "Math"], [117, 28, 119, 26], [117, 29, 119, 27, "min"], [117, 32, 119, 30], [117, 33, 119, 31, "img"], [117, 36, 119, 34], [117, 37, 119, 35, "width"], [117, 42, 119, 40], [117, 44, 119, 42, "img"], [117, 47, 119, 45], [117, 48, 119, 46, "height"], [117, 54, 119, 52], [117, 55, 119, 53], [117, 58, 119, 56], [117, 60, 119, 58], [117, 61, 119, 59], [117, 62, 119, 60], [119, 6, 121, 4], [119, 11, 121, 9], [119, 15, 121, 13, "y"], [119, 16, 121, 14], [119, 19, 121, 17], [119, 20, 121, 18], [119, 22, 121, 20, "y"], [119, 23, 121, 21], [119, 26, 121, 24, "img"], [119, 29, 121, 27], [119, 30, 121, 28, "height"], [119, 36, 121, 34], [119, 39, 121, 37, "blockSize"], [119, 48, 121, 46], [119, 50, 121, 48, "y"], [119, 51, 121, 49], [119, 55, 121, 53, "blockSize"], [119, 64, 121, 62], [119, 66, 121, 64], [120, 8, 122, 6], [120, 13, 122, 11], [120, 17, 122, 15, "x"], [120, 18, 122, 16], [120, 21, 122, 19], [120, 22, 122, 20], [120, 24, 122, 22, "x"], [120, 25, 122, 23], [120, 28, 122, 26, "img"], [120, 31, 122, 29], [120, 32, 122, 30, "width"], [120, 37, 122, 35], [120, 40, 122, 38, "blockSize"], [120, 49, 122, 47], [120, 51, 122, 49, "x"], [120, 52, 122, 50], [120, 56, 122, 54, "blockSize"], [120, 65, 122, 63], [120, 67, 122, 65], [121, 10, 123, 8], [121, 16, 123, 14, "skinPixels"], [121, 26, 123, 24], [121, 29, 123, 27, "countSkinPixelsInRegion"], [121, 52, 123, 50], [121, 53, 123, 51, "data"], [121, 57, 123, 55], [121, 59, 123, 57, "x"], [121, 60, 123, 58], [121, 62, 123, 60, "y"], [121, 63, 123, 61], [121, 65, 123, 63, "blockSize"], [121, 74, 123, 72], [121, 76, 123, 74, "img"], [121, 79, 123, 77], [121, 80, 123, 78, "width"], [121, 85, 123, 83], [121, 86, 123, 84], [122, 10, 124, 8], [122, 16, 124, 14, "skinRatio"], [122, 25, 124, 23], [122, 28, 124, 26, "skinPixels"], [122, 38, 124, 36], [122, 42, 124, 40, "blockSize"], [122, 51, 124, 49], [122, 54, 124, 52, "blockSize"], [122, 63, 124, 61], [122, 64, 124, 62], [124, 10, 126, 8], [125, 10, 127, 8], [125, 14, 127, 12, "skinRatio"], [125, 23, 127, 21], [125, 26, 127, 24], [125, 29, 127, 27], [125, 31, 127, 29], [126, 12, 128, 10], [127, 12, 129, 10], [127, 16, 129, 14, "y"], [127, 17, 129, 15], [127, 20, 129, 18, "img"], [127, 23, 129, 21], [127, 24, 129, 22, "height"], [127, 30, 129, 28], [127, 33, 129, 31], [127, 37, 129, 35], [127, 39, 129, 37], [128, 14, 130, 12, "faces"], [128, 19, 130, 17], [128, 20, 130, 18, "push"], [128, 24, 130, 22], [128, 25, 130, 23], [129, 16, 131, 14, "boundingBox"], [129, 27, 131, 25], [129, 29, 131, 27], [130, 18, 132, 16, "xCenter"], [130, 25, 132, 23], [130, 27, 132, 25], [130, 28, 132, 26, "x"], [130, 29, 132, 27], [130, 32, 132, 30, "blockSize"], [130, 41, 132, 39], [130, 44, 132, 42], [130, 45, 132, 43], [130, 49, 132, 47, "img"], [130, 52, 132, 50], [130, 53, 132, 51, "width"], [130, 58, 132, 56], [131, 18, 133, 16, "yCenter"], [131, 25, 133, 23], [131, 27, 133, 25], [131, 28, 133, 26, "y"], [131, 29, 133, 27], [131, 32, 133, 30, "blockSize"], [131, 41, 133, 39], [131, 44, 133, 42], [131, 45, 133, 43], [131, 49, 133, 47, "img"], [131, 52, 133, 50], [131, 53, 133, 51, "height"], [131, 59, 133, 57], [132, 18, 134, 16, "width"], [132, 23, 134, 21], [132, 25, 134, 24, "blockSize"], [132, 34, 134, 33], [132, 37, 134, 36], [132, 38, 134, 37], [132, 41, 134, 41, "img"], [132, 44, 134, 44], [132, 45, 134, 45, "width"], [132, 50, 134, 50], [133, 18, 134, 53], [134, 18, 135, 16, "height"], [134, 24, 135, 22], [134, 26, 135, 25, "blockSize"], [134, 35, 135, 34], [134, 38, 135, 37], [134, 41, 135, 40], [134, 44, 135, 44, "img"], [134, 47, 135, 47], [134, 48, 135, 48, "height"], [135, 16, 136, 14], [136, 14, 137, 12], [136, 15, 137, 13], [136, 16, 137, 14], [137, 14, 138, 12, "console"], [137, 21, 138, 19], [137, 22, 138, 20, "log"], [137, 25, 138, 23], [137, 26, 138, 24], [137, 75, 138, 73, "x"], [137, 76, 138, 74], [137, 81, 138, 79, "y"], [137, 82, 138, 80], [137, 92, 138, 90], [137, 93, 138, 91, "skinRatio"], [137, 102, 138, 100], [137, 105, 138, 103], [137, 108, 138, 106], [137, 110, 138, 108, "toFixed"], [137, 117, 138, 115], [137, 118, 138, 116], [137, 119, 138, 117], [137, 120, 138, 118], [137, 135, 138, 133], [137, 136, 138, 134], [138, 12, 139, 10], [139, 10, 140, 8], [140, 8, 141, 6], [141, 6, 142, 4], [143, 6, 144, 4], [144, 6, 145, 4], [144, 12, 145, 10, "mergedFaces"], [144, 23, 145, 21], [144, 26, 145, 24, "mergeFaceDetections"], [144, 45, 145, 43], [144, 46, 145, 44, "faces"], [144, 51, 145, 49], [144, 52, 145, 50], [145, 6, 146, 4], [145, 13, 146, 11, "mergedFaces"], [145, 24, 146, 22], [145, 25, 146, 23, "slice"], [145, 30, 146, 28], [145, 31, 146, 29], [145, 32, 146, 30], [145, 34, 146, 32], [145, 35, 146, 33], [145, 36, 146, 34], [145, 37, 146, 35], [145, 38, 146, 36], [146, 4, 147, 2], [146, 5, 147, 3], [147, 4, 149, 2], [147, 10, 149, 8, "countSkinPixelsInRegion"], [147, 33, 149, 31], [147, 36, 149, 34, "countSkinPixelsInRegion"], [147, 37, 149, 35, "data"], [147, 41, 149, 58], [147, 43, 149, 60, "startX"], [147, 49, 149, 74], [147, 51, 149, 76, "startY"], [147, 57, 149, 90], [147, 59, 149, 92, "size"], [147, 63, 149, 104], [147, 65, 149, 106, "imageWidth"], [147, 75, 149, 124], [147, 80, 149, 129], [148, 6, 150, 4], [148, 10, 150, 8, "skinPixels"], [148, 20, 150, 18], [148, 23, 150, 21], [148, 24, 150, 22], [149, 6, 151, 4], [149, 11, 151, 9], [149, 15, 151, 13, "y"], [149, 16, 151, 14], [149, 19, 151, 17, "startY"], [149, 25, 151, 23], [149, 27, 151, 25, "y"], [149, 28, 151, 26], [149, 31, 151, 29, "startY"], [149, 37, 151, 35], [149, 40, 151, 38, "size"], [149, 44, 151, 42], [149, 46, 151, 44, "y"], [149, 47, 151, 45], [149, 49, 151, 47], [149, 51, 151, 49], [150, 8, 152, 6], [150, 13, 152, 11], [150, 17, 152, 15, "x"], [150, 18, 152, 16], [150, 21, 152, 19, "startX"], [150, 27, 152, 25], [150, 29, 152, 27, "x"], [150, 30, 152, 28], [150, 33, 152, 31, "startX"], [150, 39, 152, 37], [150, 42, 152, 40, "size"], [150, 46, 152, 44], [150, 48, 152, 46, "x"], [150, 49, 152, 47], [150, 51, 152, 49], [150, 53, 152, 51], [151, 10, 153, 8], [151, 16, 153, 14, "index"], [151, 21, 153, 19], [151, 24, 153, 22], [151, 25, 153, 23, "y"], [151, 26, 153, 24], [151, 29, 153, 27, "imageWidth"], [151, 39, 153, 37], [151, 42, 153, 40, "x"], [151, 43, 153, 41], [151, 47, 153, 45], [151, 48, 153, 46], [152, 10, 154, 8], [152, 16, 154, 14, "r"], [152, 17, 154, 15], [152, 20, 154, 18, "data"], [152, 24, 154, 22], [152, 25, 154, 23, "index"], [152, 30, 154, 28], [152, 31, 154, 29], [153, 10, 155, 8], [153, 16, 155, 14, "g"], [153, 17, 155, 15], [153, 20, 155, 18, "data"], [153, 24, 155, 22], [153, 25, 155, 23, "index"], [153, 30, 155, 28], [153, 33, 155, 31], [153, 34, 155, 32], [153, 35, 155, 33], [154, 10, 156, 8], [154, 16, 156, 14, "b"], [154, 17, 156, 15], [154, 20, 156, 18, "data"], [154, 24, 156, 22], [154, 25, 156, 23, "index"], [154, 30, 156, 28], [154, 33, 156, 31], [154, 34, 156, 32], [154, 35, 156, 33], [156, 10, 158, 8], [157, 10, 159, 8], [157, 14, 159, 12, "isSkinTone"], [157, 24, 159, 22], [157, 25, 159, 23, "r"], [157, 26, 159, 24], [157, 28, 159, 26, "g"], [157, 29, 159, 27], [157, 31, 159, 29, "b"], [157, 32, 159, 30], [157, 33, 159, 31], [157, 35, 159, 33], [158, 12, 160, 10, "skinPixels"], [158, 22, 160, 20], [158, 24, 160, 22], [159, 10, 161, 8], [160, 8, 162, 6], [161, 6, 163, 4], [162, 6, 164, 4], [162, 13, 164, 11, "skinPixels"], [162, 23, 164, 21], [163, 4, 165, 2], [163, 5, 165, 3], [164, 4, 167, 2], [164, 10, 167, 8, "isSkinTone"], [164, 20, 167, 18], [164, 23, 167, 21, "isSkinTone"], [164, 24, 167, 22, "r"], [164, 25, 167, 31], [164, 27, 167, 33, "g"], [164, 28, 167, 42], [164, 30, 167, 44, "b"], [164, 31, 167, 53], [164, 36, 167, 58], [165, 6, 168, 4], [166, 6, 169, 4], [166, 13, 170, 6, "r"], [166, 14, 170, 7], [166, 17, 170, 10], [166, 19, 170, 12], [166, 23, 170, 16, "g"], [166, 24, 170, 17], [166, 27, 170, 20], [166, 29, 170, 22], [166, 33, 170, 26, "b"], [166, 34, 170, 27], [166, 37, 170, 30], [166, 39, 170, 32], [166, 43, 171, 6, "r"], [166, 44, 171, 7], [166, 47, 171, 10, "g"], [166, 48, 171, 11], [166, 52, 171, 15, "r"], [166, 53, 171, 16], [166, 56, 171, 19, "b"], [166, 57, 171, 20], [166, 61, 172, 6, "Math"], [166, 65, 172, 10], [166, 66, 172, 11, "abs"], [166, 69, 172, 14], [166, 70, 172, 15, "r"], [166, 71, 172, 16], [166, 74, 172, 19, "g"], [166, 75, 172, 20], [166, 76, 172, 21], [166, 79, 172, 24], [166, 81, 172, 26], [166, 85, 173, 6, "Math"], [166, 89, 173, 10], [166, 90, 173, 11, "max"], [166, 93, 173, 14], [166, 94, 173, 15, "r"], [166, 95, 173, 16], [166, 97, 173, 18, "g"], [166, 98, 173, 19], [166, 100, 173, 21, "b"], [166, 101, 173, 22], [166, 102, 173, 23], [166, 105, 173, 26, "Math"], [166, 109, 173, 30], [166, 110, 173, 31, "min"], [166, 113, 173, 34], [166, 114, 173, 35, "r"], [166, 115, 173, 36], [166, 117, 173, 38, "g"], [166, 118, 173, 39], [166, 120, 173, 41, "b"], [166, 121, 173, 42], [166, 122, 173, 43], [166, 125, 173, 46], [166, 127, 173, 48], [167, 4, 175, 2], [167, 5, 175, 3], [168, 4, 177, 2], [168, 10, 177, 8, "mergeFaceDetections"], [168, 29, 177, 27], [168, 32, 177, 31, "faces"], [168, 37, 177, 43], [168, 41, 177, 48], [169, 6, 178, 4], [169, 10, 178, 8, "faces"], [169, 15, 178, 13], [169, 16, 178, 14, "length"], [169, 22, 178, 20], [169, 26, 178, 24], [169, 27, 178, 25], [169, 29, 178, 27], [169, 36, 178, 34, "faces"], [169, 41, 178, 39], [170, 6, 180, 4], [170, 12, 180, 10, "merged"], [170, 18, 180, 16], [170, 21, 180, 19], [170, 23, 180, 21], [171, 6, 181, 4], [171, 12, 181, 10, "used"], [171, 16, 181, 14], [171, 19, 181, 17], [171, 23, 181, 21, "Set"], [171, 26, 181, 24], [171, 27, 181, 25], [171, 28, 181, 26], [172, 6, 183, 4], [172, 11, 183, 9], [172, 15, 183, 13, "i"], [172, 16, 183, 14], [172, 19, 183, 17], [172, 20, 183, 18], [172, 22, 183, 20, "i"], [172, 23, 183, 21], [172, 26, 183, 24, "faces"], [172, 31, 183, 29], [172, 32, 183, 30, "length"], [172, 38, 183, 36], [172, 40, 183, 38, "i"], [172, 41, 183, 39], [172, 43, 183, 41], [172, 45, 183, 43], [173, 8, 184, 6], [173, 12, 184, 10, "used"], [173, 16, 184, 14], [173, 17, 184, 15, "has"], [173, 20, 184, 18], [173, 21, 184, 19, "i"], [173, 22, 184, 20], [173, 23, 184, 21], [173, 25, 184, 23], [174, 8, 186, 6], [174, 12, 186, 10, "currentFace"], [174, 23, 186, 21], [174, 26, 186, 24, "faces"], [174, 31, 186, 29], [174, 32, 186, 30, "i"], [174, 33, 186, 31], [174, 34, 186, 32], [175, 8, 187, 6, "used"], [175, 12, 187, 10], [175, 13, 187, 11, "add"], [175, 16, 187, 14], [175, 17, 187, 15, "i"], [175, 18, 187, 16], [175, 19, 187, 17], [177, 8, 189, 6], [178, 8, 190, 6], [178, 13, 190, 11], [178, 17, 190, 15, "j"], [178, 18, 190, 16], [178, 21, 190, 19, "i"], [178, 22, 190, 20], [178, 25, 190, 23], [178, 26, 190, 24], [178, 28, 190, 26, "j"], [178, 29, 190, 27], [178, 32, 190, 30, "faces"], [178, 37, 190, 35], [178, 38, 190, 36, "length"], [178, 44, 190, 42], [178, 46, 190, 44, "j"], [178, 47, 190, 45], [178, 49, 190, 47], [178, 51, 190, 49], [179, 10, 191, 8], [179, 14, 191, 12, "used"], [179, 18, 191, 16], [179, 19, 191, 17, "has"], [179, 22, 191, 20], [179, 23, 191, 21, "j"], [179, 24, 191, 22], [179, 25, 191, 23], [179, 27, 191, 25], [180, 10, 193, 8], [180, 16, 193, 14, "overlap"], [180, 23, 193, 21], [180, 26, 193, 24, "calculateOverlap"], [180, 42, 193, 40], [180, 43, 193, 41, "currentFace"], [180, 54, 193, 52], [180, 55, 193, 53, "boundingBox"], [180, 66, 193, 64], [180, 68, 193, 66, "faces"], [180, 73, 193, 71], [180, 74, 193, 72, "j"], [180, 75, 193, 73], [180, 76, 193, 74], [180, 77, 193, 75, "boundingBox"], [180, 88, 193, 86], [180, 89, 193, 87], [181, 10, 194, 8], [181, 14, 194, 12, "overlap"], [181, 21, 194, 19], [181, 24, 194, 22], [181, 27, 194, 25], [181, 29, 194, 27], [182, 12, 194, 29], [183, 12, 195, 10], [184, 12, 196, 10, "currentFace"], [184, 23, 196, 21], [184, 26, 196, 24, "mergeTwoFaces"], [184, 39, 196, 37], [184, 40, 196, 38, "currentFace"], [184, 51, 196, 49], [184, 53, 196, 51, "faces"], [184, 58, 196, 56], [184, 59, 196, 57, "j"], [184, 60, 196, 58], [184, 61, 196, 59], [184, 62, 196, 60], [185, 12, 197, 10, "used"], [185, 16, 197, 14], [185, 17, 197, 15, "add"], [185, 20, 197, 18], [185, 21, 197, 19, "j"], [185, 22, 197, 20], [185, 23, 197, 21], [186, 10, 198, 8], [187, 8, 199, 6], [188, 8, 201, 6, "merged"], [188, 14, 201, 12], [188, 15, 201, 13, "push"], [188, 19, 201, 17], [188, 20, 201, 18, "currentFace"], [188, 31, 201, 29], [188, 32, 201, 30], [189, 6, 202, 4], [190, 6, 204, 4], [190, 13, 204, 11, "merged"], [190, 19, 204, 17], [191, 4, 205, 2], [191, 5, 205, 3], [192, 4, 207, 2], [192, 10, 207, 8, "calculateOverlap"], [192, 26, 207, 24], [192, 29, 207, 27, "calculateOverlap"], [192, 30, 207, 28, "box1"], [192, 34, 207, 37], [192, 36, 207, 39, "box2"], [192, 40, 207, 48], [192, 45, 207, 53], [193, 6, 208, 4], [193, 12, 208, 10, "x1"], [193, 14, 208, 12], [193, 17, 208, 15, "Math"], [193, 21, 208, 19], [193, 22, 208, 20, "max"], [193, 25, 208, 23], [193, 26, 208, 24, "box1"], [193, 30, 208, 28], [193, 31, 208, 29, "xCenter"], [193, 38, 208, 36], [193, 41, 208, 39, "box1"], [193, 45, 208, 43], [193, 46, 208, 44, "width"], [193, 51, 208, 49], [193, 54, 208, 50], [193, 55, 208, 51], [193, 57, 208, 53, "box2"], [193, 61, 208, 57], [193, 62, 208, 58, "xCenter"], [193, 69, 208, 65], [193, 72, 208, 68, "box2"], [193, 76, 208, 72], [193, 77, 208, 73, "width"], [193, 82, 208, 78], [193, 85, 208, 79], [193, 86, 208, 80], [193, 87, 208, 81], [194, 6, 209, 4], [194, 12, 209, 10, "y1"], [194, 14, 209, 12], [194, 17, 209, 15, "Math"], [194, 21, 209, 19], [194, 22, 209, 20, "max"], [194, 25, 209, 23], [194, 26, 209, 24, "box1"], [194, 30, 209, 28], [194, 31, 209, 29, "yCenter"], [194, 38, 209, 36], [194, 41, 209, 39, "box1"], [194, 45, 209, 43], [194, 46, 209, 44, "height"], [194, 52, 209, 50], [194, 55, 209, 51], [194, 56, 209, 52], [194, 58, 209, 54, "box2"], [194, 62, 209, 58], [194, 63, 209, 59, "yCenter"], [194, 70, 209, 66], [194, 73, 209, 69, "box2"], [194, 77, 209, 73], [194, 78, 209, 74, "height"], [194, 84, 209, 80], [194, 87, 209, 81], [194, 88, 209, 82], [194, 89, 209, 83], [195, 6, 210, 4], [195, 12, 210, 10, "x2"], [195, 14, 210, 12], [195, 17, 210, 15, "Math"], [195, 21, 210, 19], [195, 22, 210, 20, "min"], [195, 25, 210, 23], [195, 26, 210, 24, "box1"], [195, 30, 210, 28], [195, 31, 210, 29, "xCenter"], [195, 38, 210, 36], [195, 41, 210, 39, "box1"], [195, 45, 210, 43], [195, 46, 210, 44, "width"], [195, 51, 210, 49], [195, 54, 210, 50], [195, 55, 210, 51], [195, 57, 210, 53, "box2"], [195, 61, 210, 57], [195, 62, 210, 58, "xCenter"], [195, 69, 210, 65], [195, 72, 210, 68, "box2"], [195, 76, 210, 72], [195, 77, 210, 73, "width"], [195, 82, 210, 78], [195, 85, 210, 79], [195, 86, 210, 80], [195, 87, 210, 81], [196, 6, 211, 4], [196, 12, 211, 10, "y2"], [196, 14, 211, 12], [196, 17, 211, 15, "Math"], [196, 21, 211, 19], [196, 22, 211, 20, "min"], [196, 25, 211, 23], [196, 26, 211, 24, "box1"], [196, 30, 211, 28], [196, 31, 211, 29, "yCenter"], [196, 38, 211, 36], [196, 41, 211, 39, "box1"], [196, 45, 211, 43], [196, 46, 211, 44, "height"], [196, 52, 211, 50], [196, 55, 211, 51], [196, 56, 211, 52], [196, 58, 211, 54, "box2"], [196, 62, 211, 58], [196, 63, 211, 59, "yCenter"], [196, 70, 211, 66], [196, 73, 211, 69, "box2"], [196, 77, 211, 73], [196, 78, 211, 74, "height"], [196, 84, 211, 80], [196, 87, 211, 81], [196, 88, 211, 82], [196, 89, 211, 83], [197, 6, 213, 4], [197, 10, 213, 8, "x2"], [197, 12, 213, 10], [197, 16, 213, 14, "x1"], [197, 18, 213, 16], [197, 22, 213, 20, "y2"], [197, 24, 213, 22], [197, 28, 213, 26, "y1"], [197, 30, 213, 28], [197, 32, 213, 30], [197, 39, 213, 37], [197, 40, 213, 38], [198, 6, 215, 4], [198, 12, 215, 10, "overlapArea"], [198, 23, 215, 21], [198, 26, 215, 24], [198, 27, 215, 25, "x2"], [198, 29, 215, 27], [198, 32, 215, 30, "x1"], [198, 34, 215, 32], [198, 39, 215, 37, "y2"], [198, 41, 215, 39], [198, 44, 215, 42, "y1"], [198, 46, 215, 44], [198, 47, 215, 45], [199, 6, 216, 4], [199, 12, 216, 10, "box1Area"], [199, 20, 216, 18], [199, 23, 216, 21, "box1"], [199, 27, 216, 25], [199, 28, 216, 26, "width"], [199, 33, 216, 31], [199, 36, 216, 34, "box1"], [199, 40, 216, 38], [199, 41, 216, 39, "height"], [199, 47, 216, 45], [200, 6, 217, 4], [200, 12, 217, 10, "box2Area"], [200, 20, 217, 18], [200, 23, 217, 21, "box2"], [200, 27, 217, 25], [200, 28, 217, 26, "width"], [200, 33, 217, 31], [200, 36, 217, 34, "box2"], [200, 40, 217, 38], [200, 41, 217, 39, "height"], [200, 47, 217, 45], [201, 6, 219, 4], [201, 13, 219, 11, "overlapArea"], [201, 24, 219, 22], [201, 27, 219, 25, "Math"], [201, 31, 219, 29], [201, 32, 219, 30, "min"], [201, 35, 219, 33], [201, 36, 219, 34, "box1Area"], [201, 44, 219, 42], [201, 46, 219, 44, "box2Area"], [201, 54, 219, 52], [201, 55, 219, 53], [202, 4, 220, 2], [202, 5, 220, 3], [203, 4, 222, 2], [203, 10, 222, 8, "mergeTwoFaces"], [203, 23, 222, 21], [203, 26, 222, 24, "mergeTwoFaces"], [203, 27, 222, 25, "face1"], [203, 32, 222, 35], [203, 34, 222, 37, "face2"], [203, 39, 222, 47], [203, 44, 222, 52], [204, 6, 223, 4], [204, 12, 223, 10, "box1"], [204, 16, 223, 14], [204, 19, 223, 17, "face1"], [204, 24, 223, 22], [204, 25, 223, 23, "boundingBox"], [204, 36, 223, 34], [205, 6, 224, 4], [205, 12, 224, 10, "box2"], [205, 16, 224, 14], [205, 19, 224, 17, "face2"], [205, 24, 224, 22], [205, 25, 224, 23, "boundingBox"], [205, 36, 224, 34], [206, 6, 226, 4], [206, 12, 226, 10, "left"], [206, 16, 226, 14], [206, 19, 226, 17, "Math"], [206, 23, 226, 21], [206, 24, 226, 22, "min"], [206, 27, 226, 25], [206, 28, 226, 26, "box1"], [206, 32, 226, 30], [206, 33, 226, 31, "xCenter"], [206, 40, 226, 38], [206, 43, 226, 41, "box1"], [206, 47, 226, 45], [206, 48, 226, 46, "width"], [206, 53, 226, 51], [206, 56, 226, 52], [206, 57, 226, 53], [206, 59, 226, 55, "box2"], [206, 63, 226, 59], [206, 64, 226, 60, "xCenter"], [206, 71, 226, 67], [206, 74, 226, 70, "box2"], [206, 78, 226, 74], [206, 79, 226, 75, "width"], [206, 84, 226, 80], [206, 87, 226, 81], [206, 88, 226, 82], [206, 89, 226, 83], [207, 6, 227, 4], [207, 12, 227, 10, "right"], [207, 17, 227, 15], [207, 20, 227, 18, "Math"], [207, 24, 227, 22], [207, 25, 227, 23, "max"], [207, 28, 227, 26], [207, 29, 227, 27, "box1"], [207, 33, 227, 31], [207, 34, 227, 32, "xCenter"], [207, 41, 227, 39], [207, 44, 227, 42, "box1"], [207, 48, 227, 46], [207, 49, 227, 47, "width"], [207, 54, 227, 52], [207, 57, 227, 53], [207, 58, 227, 54], [207, 60, 227, 56, "box2"], [207, 64, 227, 60], [207, 65, 227, 61, "xCenter"], [207, 72, 227, 68], [207, 75, 227, 71, "box2"], [207, 79, 227, 75], [207, 80, 227, 76, "width"], [207, 85, 227, 81], [207, 88, 227, 82], [207, 89, 227, 83], [207, 90, 227, 84], [208, 6, 228, 4], [208, 12, 228, 10, "top"], [208, 15, 228, 13], [208, 18, 228, 16, "Math"], [208, 22, 228, 20], [208, 23, 228, 21, "min"], [208, 26, 228, 24], [208, 27, 228, 25, "box1"], [208, 31, 228, 29], [208, 32, 228, 30, "yCenter"], [208, 39, 228, 37], [208, 42, 228, 40, "box1"], [208, 46, 228, 44], [208, 47, 228, 45, "height"], [208, 53, 228, 51], [208, 56, 228, 52], [208, 57, 228, 53], [208, 59, 228, 55, "box2"], [208, 63, 228, 59], [208, 64, 228, 60, "yCenter"], [208, 71, 228, 67], [208, 74, 228, 70, "box2"], [208, 78, 228, 74], [208, 79, 228, 75, "height"], [208, 85, 228, 81], [208, 88, 228, 82], [208, 89, 228, 83], [208, 90, 228, 84], [209, 6, 229, 4], [209, 12, 229, 10, "bottom"], [209, 18, 229, 16], [209, 21, 229, 19, "Math"], [209, 25, 229, 23], [209, 26, 229, 24, "max"], [209, 29, 229, 27], [209, 30, 229, 28, "box1"], [209, 34, 229, 32], [209, 35, 229, 33, "yCenter"], [209, 42, 229, 40], [209, 45, 229, 43, "box1"], [209, 49, 229, 47], [209, 50, 229, 48, "height"], [209, 56, 229, 54], [209, 59, 229, 55], [209, 60, 229, 56], [209, 62, 229, 58, "box2"], [209, 66, 229, 62], [209, 67, 229, 63, "yCenter"], [209, 74, 229, 70], [209, 77, 229, 73, "box2"], [209, 81, 229, 77], [209, 82, 229, 78, "height"], [209, 88, 229, 84], [209, 91, 229, 85], [209, 92, 229, 86], [209, 93, 229, 87], [210, 6, 231, 4], [210, 13, 231, 11], [211, 8, 232, 6, "boundingBox"], [211, 19, 232, 17], [211, 21, 232, 19], [212, 10, 233, 8, "xCenter"], [212, 17, 233, 15], [212, 19, 233, 17], [212, 20, 233, 18, "left"], [212, 24, 233, 22], [212, 27, 233, 25, "right"], [212, 32, 233, 30], [212, 36, 233, 34], [212, 37, 233, 35], [213, 10, 234, 8, "yCenter"], [213, 17, 234, 15], [213, 19, 234, 17], [213, 20, 234, 18, "top"], [213, 23, 234, 21], [213, 26, 234, 24, "bottom"], [213, 32, 234, 30], [213, 36, 234, 34], [213, 37, 234, 35], [214, 10, 235, 8, "width"], [214, 15, 235, 13], [214, 17, 235, 15, "right"], [214, 22, 235, 20], [214, 25, 235, 23, "left"], [214, 29, 235, 27], [215, 10, 236, 8, "height"], [215, 16, 236, 14], [215, 18, 236, 16, "bottom"], [215, 24, 236, 22], [215, 27, 236, 25, "top"], [216, 8, 237, 6], [217, 6, 238, 4], [217, 7, 238, 5], [218, 4, 239, 2], [218, 5, 239, 3], [220, 4, 241, 2], [221, 4, 242, 2], [221, 10, 242, 8, "capturePhoto"], [221, 22, 242, 20], [221, 25, 242, 23], [221, 29, 242, 23, "useCallback"], [221, 47, 242, 34], [221, 49, 242, 35], [221, 61, 242, 47], [222, 6, 243, 4], [223, 6, 244, 4], [223, 12, 244, 10, "isDev"], [223, 17, 244, 15], [223, 20, 244, 18, "process"], [223, 27, 244, 25], [223, 28, 244, 26, "env"], [223, 31, 244, 29], [223, 32, 244, 30, "NODE_ENV"], [223, 40, 244, 38], [223, 45, 244, 43], [223, 58, 244, 56], [223, 62, 244, 60, "__DEV__"], [223, 69, 244, 67], [224, 6, 246, 4], [224, 10, 246, 8], [224, 11, 246, 9, "cameraRef"], [224, 20, 246, 18], [224, 21, 246, 19, "current"], [224, 28, 246, 26], [224, 32, 246, 30], [224, 33, 246, 31, "isDev"], [224, 38, 246, 36], [224, 40, 246, 38], [225, 8, 247, 6, "<PERSON><PERSON>"], [225, 22, 247, 11], [225, 23, 247, 12, "alert"], [225, 28, 247, 17], [225, 29, 247, 18], [225, 36, 247, 25], [225, 38, 247, 27], [225, 56, 247, 45], [225, 57, 247, 46], [226, 8, 248, 6], [227, 6, 249, 4], [228, 6, 250, 4], [228, 10, 250, 8], [229, 8, 251, 6, "setProcessingState"], [229, 26, 251, 24], [229, 27, 251, 25], [229, 38, 251, 36], [229, 39, 251, 37], [230, 8, 252, 6, "setProcessingProgress"], [230, 29, 252, 27], [230, 30, 252, 28], [230, 32, 252, 30], [230, 33, 252, 31], [231, 8, 253, 6], [232, 8, 254, 6], [233, 8, 255, 6], [234, 8, 256, 6], [234, 14, 256, 12], [234, 18, 256, 16, "Promise"], [234, 25, 256, 23], [234, 26, 256, 24, "resolve"], [234, 33, 256, 31], [234, 37, 256, 35, "setTimeout"], [234, 47, 256, 45], [234, 48, 256, 46, "resolve"], [234, 55, 256, 53], [234, 57, 256, 55], [234, 59, 256, 57], [234, 60, 256, 58], [234, 61, 256, 59], [235, 8, 257, 6], [236, 8, 258, 6], [236, 12, 258, 10, "photo"], [236, 17, 258, 15], [237, 8, 260, 6], [237, 12, 260, 10], [238, 10, 261, 8, "photo"], [238, 15, 261, 13], [238, 18, 261, 16], [238, 24, 261, 22, "cameraRef"], [238, 33, 261, 31], [238, 34, 261, 32, "current"], [238, 41, 261, 39], [238, 42, 261, 40, "takePictureAsync"], [238, 58, 261, 56], [238, 59, 261, 57], [239, 12, 262, 10, "quality"], [239, 19, 262, 17], [239, 21, 262, 19], [239, 24, 262, 22], [240, 12, 263, 10, "base64"], [240, 18, 263, 16], [240, 20, 263, 18], [240, 25, 263, 23], [241, 12, 264, 10, "skipProcessing"], [241, 26, 264, 24], [241, 28, 264, 26], [241, 32, 264, 30], [241, 33, 264, 32], [242, 10, 265, 8], [242, 11, 265, 9], [242, 12, 265, 10], [243, 8, 266, 6], [243, 9, 266, 7], [243, 10, 266, 8], [243, 17, 266, 15, "cameraError"], [243, 28, 266, 26], [243, 30, 266, 28], [244, 10, 267, 8, "console"], [244, 17, 267, 15], [244, 18, 267, 16, "log"], [244, 21, 267, 19], [244, 22, 267, 20], [244, 82, 267, 80], [244, 84, 267, 82, "cameraError"], [244, 95, 267, 93], [244, 96, 267, 94], [245, 10, 268, 8], [246, 10, 269, 8], [246, 14, 269, 12, "isDev"], [246, 19, 269, 17], [246, 21, 269, 19], [247, 12, 270, 10, "photo"], [247, 17, 270, 15], [247, 20, 270, 18], [248, 14, 271, 12, "uri"], [248, 17, 271, 15], [248, 19, 271, 17], [249, 12, 272, 10], [249, 13, 272, 11], [250, 10, 273, 8], [250, 11, 273, 9], [250, 17, 273, 15], [251, 12, 274, 10], [251, 18, 274, 16, "cameraError"], [251, 29, 274, 27], [252, 10, 275, 8], [253, 8, 276, 6], [254, 8, 277, 6], [254, 12, 277, 10], [254, 13, 277, 11, "photo"], [254, 18, 277, 16], [254, 20, 277, 18], [255, 10, 278, 8], [255, 16, 278, 14], [255, 20, 278, 18, "Error"], [255, 25, 278, 23], [255, 26, 278, 24], [255, 51, 278, 49], [255, 52, 278, 50], [256, 8, 279, 6], [257, 8, 280, 6, "console"], [257, 15, 280, 13], [257, 16, 280, 14, "log"], [257, 19, 280, 17], [257, 20, 280, 18], [257, 56, 280, 54], [257, 58, 280, 56, "photo"], [257, 63, 280, 61], [257, 64, 280, 62, "uri"], [257, 67, 280, 65], [257, 68, 280, 66], [258, 8, 281, 6, "setCapturedPhoto"], [258, 24, 281, 22], [258, 25, 281, 23, "photo"], [258, 30, 281, 28], [258, 31, 281, 29, "uri"], [258, 34, 281, 32], [258, 35, 281, 33], [259, 8, 282, 6, "setProcessingProgress"], [259, 29, 282, 27], [259, 30, 282, 28], [259, 32, 282, 30], [259, 33, 282, 31], [260, 8, 283, 6], [261, 8, 284, 6, "console"], [261, 15, 284, 13], [261, 16, 284, 14, "log"], [261, 19, 284, 17], [261, 20, 284, 18], [261, 73, 284, 71], [261, 74, 284, 72], [262, 8, 285, 6], [262, 14, 285, 12, "processImageWithFaceBlur"], [262, 38, 285, 36], [262, 39, 285, 37, "photo"], [262, 44, 285, 42], [262, 45, 285, 43, "uri"], [262, 48, 285, 46], [262, 49, 285, 47], [263, 8, 286, 6, "console"], [263, 15, 286, 13], [263, 16, 286, 14, "log"], [263, 19, 286, 17], [263, 20, 286, 18], [263, 71, 286, 69], [263, 72, 286, 70], [264, 6, 287, 4], [264, 7, 287, 5], [264, 8, 287, 6], [264, 15, 287, 13, "error"], [264, 20, 287, 18], [264, 22, 287, 20], [265, 8, 288, 6, "console"], [265, 15, 288, 13], [265, 16, 288, 14, "error"], [265, 21, 288, 19], [265, 22, 288, 20], [265, 54, 288, 52], [265, 56, 288, 54, "error"], [265, 61, 288, 59], [265, 62, 288, 60], [266, 8, 289, 6, "setErrorMessage"], [266, 23, 289, 21], [266, 24, 289, 22], [266, 68, 289, 66], [266, 69, 289, 67], [267, 8, 290, 6, "setProcessingState"], [267, 26, 290, 24], [267, 27, 290, 25], [267, 34, 290, 32], [267, 35, 290, 33], [268, 6, 291, 4], [269, 4, 292, 2], [269, 5, 292, 3], [269, 7, 292, 5], [269, 9, 292, 7], [269, 10, 292, 8], [270, 4, 293, 2], [271, 4, 294, 2], [271, 10, 294, 8, "processImageWithFaceBlur"], [271, 34, 294, 32], [271, 37, 294, 35], [271, 43, 294, 42, "photoUri"], [271, 51, 294, 58], [271, 55, 294, 63], [272, 6, 295, 4], [272, 10, 295, 8], [273, 8, 296, 6, "console"], [273, 15, 296, 13], [273, 16, 296, 14, "log"], [273, 19, 296, 17], [273, 20, 296, 18], [273, 84, 296, 82], [273, 85, 296, 83], [274, 8, 297, 6, "setProcessingState"], [274, 26, 297, 24], [274, 27, 297, 25], [274, 39, 297, 37], [274, 40, 297, 38], [275, 8, 298, 6, "setProcessingProgress"], [275, 29, 298, 27], [275, 30, 298, 28], [275, 32, 298, 30], [275, 33, 298, 31], [277, 8, 300, 6], [278, 8, 301, 6], [278, 14, 301, 12, "canvas"], [278, 20, 301, 18], [278, 23, 301, 21, "document"], [278, 31, 301, 29], [278, 32, 301, 30, "createElement"], [278, 45, 301, 43], [278, 46, 301, 44], [278, 54, 301, 52], [278, 55, 301, 53], [279, 8, 302, 6], [279, 14, 302, 12, "ctx"], [279, 17, 302, 15], [279, 20, 302, 18, "canvas"], [279, 26, 302, 24], [279, 27, 302, 25, "getContext"], [279, 37, 302, 35], [279, 38, 302, 36], [279, 42, 302, 40], [279, 43, 302, 41], [280, 8, 303, 6], [280, 12, 303, 10], [280, 13, 303, 11, "ctx"], [280, 16, 303, 14], [280, 18, 303, 16], [280, 24, 303, 22], [280, 28, 303, 26, "Error"], [280, 33, 303, 31], [280, 34, 303, 32], [280, 64, 303, 62], [280, 65, 303, 63], [282, 8, 305, 6], [283, 8, 306, 6], [283, 14, 306, 12, "img"], [283, 17, 306, 15], [283, 20, 306, 18], [283, 24, 306, 22, "Image"], [283, 29, 306, 27], [283, 30, 306, 28], [283, 31, 306, 29], [284, 8, 307, 6], [284, 14, 307, 12], [284, 18, 307, 16, "Promise"], [284, 25, 307, 23], [284, 26, 307, 24], [284, 27, 307, 25, "resolve"], [284, 34, 307, 32], [284, 36, 307, 34, "reject"], [284, 42, 307, 40], [284, 47, 307, 45], [285, 10, 308, 8, "img"], [285, 13, 308, 11], [285, 14, 308, 12, "onload"], [285, 20, 308, 18], [285, 23, 308, 21, "resolve"], [285, 30, 308, 28], [286, 10, 309, 8, "img"], [286, 13, 309, 11], [286, 14, 309, 12, "onerror"], [286, 21, 309, 19], [286, 24, 309, 22, "reject"], [286, 30, 309, 28], [287, 10, 310, 8, "img"], [287, 13, 310, 11], [287, 14, 310, 12, "src"], [287, 17, 310, 15], [287, 20, 310, 18, "photoUri"], [287, 28, 310, 26], [288, 8, 311, 6], [288, 9, 311, 7], [288, 10, 311, 8], [290, 8, 313, 6], [291, 8, 314, 6, "canvas"], [291, 14, 314, 12], [291, 15, 314, 13, "width"], [291, 20, 314, 18], [291, 23, 314, 21, "img"], [291, 26, 314, 24], [291, 27, 314, 25, "width"], [291, 32, 314, 30], [292, 8, 315, 6, "canvas"], [292, 14, 315, 12], [292, 15, 315, 13, "height"], [292, 21, 315, 19], [292, 24, 315, 22, "img"], [292, 27, 315, 25], [292, 28, 315, 26, "height"], [292, 34, 315, 32], [293, 8, 316, 6, "console"], [293, 15, 316, 13], [293, 16, 316, 14, "log"], [293, 19, 316, 17], [293, 20, 316, 18], [293, 54, 316, 52], [293, 56, 316, 54], [294, 10, 316, 56, "width"], [294, 15, 316, 61], [294, 17, 316, 63, "img"], [294, 20, 316, 66], [294, 21, 316, 67, "width"], [294, 26, 316, 72], [295, 10, 316, 74, "height"], [295, 16, 316, 80], [295, 18, 316, 82, "img"], [295, 21, 316, 85], [295, 22, 316, 86, "height"], [296, 8, 316, 93], [296, 9, 316, 94], [296, 10, 316, 95], [298, 8, 318, 6], [299, 8, 319, 6, "ctx"], [299, 11, 319, 9], [299, 12, 319, 10, "drawImage"], [299, 21, 319, 19], [299, 22, 319, 20, "img"], [299, 25, 319, 23], [299, 27, 319, 25], [299, 28, 319, 26], [299, 30, 319, 28], [299, 31, 319, 29], [299, 32, 319, 30], [300, 8, 320, 6, "console"], [300, 15, 320, 13], [300, 16, 320, 14, "log"], [300, 19, 320, 17], [300, 20, 320, 18], [300, 72, 320, 70], [300, 73, 320, 71], [301, 8, 322, 6, "setProcessingProgress"], [301, 29, 322, 27], [301, 30, 322, 28], [301, 32, 322, 30], [301, 33, 322, 31], [303, 8, 324, 6], [304, 8, 325, 6], [304, 12, 325, 10, "detectedFaces"], [304, 25, 325, 23], [304, 28, 325, 26], [304, 30, 325, 28], [305, 8, 327, 6, "console"], [305, 15, 327, 13], [305, 16, 327, 14, "log"], [305, 19, 327, 17], [305, 20, 327, 18], [305, 78, 327, 76], [305, 79, 327, 77], [307, 8, 329, 6], [308, 8, 330, 6], [308, 12, 330, 10], [309, 10, 331, 8], [309, 16, 331, 14, "loadMediaPipeFaceDetection"], [309, 42, 331, 40], [309, 43, 331, 41], [309, 44, 331, 42], [310, 10, 332, 8, "detectedFaces"], [310, 23, 332, 21], [310, 26, 332, 24], [310, 32, 332, 30, "detectFacesWithMediaPipe"], [310, 56, 332, 54], [310, 57, 332, 55, "img"], [310, 60, 332, 58], [310, 61, 332, 59], [311, 10, 333, 8, "console"], [311, 17, 333, 15], [311, 18, 333, 16, "log"], [311, 21, 333, 19], [311, 22, 333, 20], [311, 59, 333, 57, "detectedFaces"], [311, 72, 333, 70], [311, 73, 333, 71, "length"], [311, 79, 333, 77], [311, 87, 333, 85], [311, 88, 333, 86], [312, 8, 334, 6], [312, 9, 334, 7], [312, 10, 334, 8], [312, 17, 334, 15, "mediaPipeError"], [312, 31, 334, 29], [312, 33, 334, 31], [313, 10, 335, 8, "console"], [313, 17, 335, 15], [313, 18, 335, 16, "warn"], [313, 22, 335, 20], [313, 23, 335, 21], [313, 60, 335, 58], [313, 62, 335, 60, "mediaPipeError"], [313, 76, 335, 74], [313, 77, 335, 75], [315, 10, 337, 8], [316, 10, 338, 8, "console"], [316, 17, 338, 15], [316, 18, 338, 16, "log"], [316, 21, 338, 19], [316, 22, 338, 20], [316, 88, 338, 86], [316, 89, 338, 87], [317, 10, 339, 8, "detectedFaces"], [317, 23, 339, 21], [317, 26, 339, 24, "detectFacesHeuristic"], [317, 46, 339, 44], [317, 47, 339, 45, "img"], [317, 50, 339, 48], [317, 52, 339, 50, "ctx"], [317, 55, 339, 53], [317, 56, 339, 54], [318, 10, 340, 8, "console"], [318, 17, 340, 15], [318, 18, 340, 16, "log"], [318, 21, 340, 19], [318, 22, 340, 20], [318, 70, 340, 68, "detectedFaces"], [318, 83, 340, 81], [318, 84, 340, 82, "length"], [318, 90, 340, 88], [318, 98, 340, 96], [318, 99, 340, 97], [319, 8, 341, 6], [320, 8, 343, 6, "console"], [320, 15, 343, 13], [320, 16, 343, 14, "log"], [320, 19, 343, 17], [320, 20, 343, 18], [320, 72, 343, 70, "detectedFaces"], [320, 85, 343, 83], [320, 86, 343, 84, "length"], [320, 92, 343, 90], [320, 100, 343, 98], [320, 101, 343, 99], [321, 8, 344, 6], [321, 12, 344, 10, "detectedFaces"], [321, 25, 344, 23], [321, 26, 344, 24, "length"], [321, 32, 344, 30], [321, 35, 344, 33], [321, 36, 344, 34], [321, 38, 344, 36], [322, 10, 345, 8, "console"], [322, 17, 345, 15], [322, 18, 345, 16, "log"], [322, 21, 345, 19], [322, 22, 345, 20], [322, 66, 345, 64], [322, 68, 345, 66, "detectedFaces"], [322, 81, 345, 79], [322, 82, 345, 80, "map"], [322, 85, 345, 83], [322, 86, 345, 84], [322, 87, 345, 85, "face"], [322, 91, 345, 89], [322, 93, 345, 91, "i"], [322, 94, 345, 92], [322, 100, 345, 98], [323, 12, 346, 10, "faceNumber"], [323, 22, 346, 20], [323, 24, 346, 22, "i"], [323, 25, 346, 23], [323, 28, 346, 26], [323, 29, 346, 27], [324, 12, 347, 10, "centerX"], [324, 19, 347, 17], [324, 21, 347, 19, "face"], [324, 25, 347, 23], [324, 26, 347, 24, "boundingBox"], [324, 37, 347, 35], [324, 38, 347, 36, "xCenter"], [324, 45, 347, 43], [325, 12, 348, 10, "centerY"], [325, 19, 348, 17], [325, 21, 348, 19, "face"], [325, 25, 348, 23], [325, 26, 348, 24, "boundingBox"], [325, 37, 348, 35], [325, 38, 348, 36, "yCenter"], [325, 45, 348, 43], [326, 12, 349, 10, "width"], [326, 17, 349, 15], [326, 19, 349, 17, "face"], [326, 23, 349, 21], [326, 24, 349, 22, "boundingBox"], [326, 35, 349, 33], [326, 36, 349, 34, "width"], [326, 41, 349, 39], [327, 12, 350, 10, "height"], [327, 18, 350, 16], [327, 20, 350, 18, "face"], [327, 24, 350, 22], [327, 25, 350, 23, "boundingBox"], [327, 36, 350, 34], [327, 37, 350, 35, "height"], [328, 10, 351, 8], [328, 11, 351, 9], [328, 12, 351, 10], [328, 13, 351, 11], [328, 14, 351, 12], [329, 8, 352, 6], [329, 9, 352, 7], [329, 15, 352, 13], [330, 10, 353, 8, "console"], [330, 17, 353, 15], [330, 18, 353, 16, "log"], [330, 21, 353, 19], [330, 22, 353, 20], [330, 91, 353, 89], [330, 92, 353, 90], [331, 8, 354, 6], [332, 8, 356, 6, "setProcessingProgress"], [332, 29, 356, 27], [332, 30, 356, 28], [332, 32, 356, 30], [332, 33, 356, 31], [334, 8, 358, 6], [335, 8, 359, 6], [335, 12, 359, 10, "detectedFaces"], [335, 25, 359, 23], [335, 26, 359, 24, "length"], [335, 32, 359, 30], [335, 35, 359, 33], [335, 36, 359, 34], [335, 38, 359, 36], [336, 10, 360, 8, "console"], [336, 17, 360, 15], [336, 18, 360, 16, "log"], [336, 21, 360, 19], [336, 22, 360, 20], [336, 61, 360, 59, "detectedFaces"], [336, 74, 360, 72], [336, 75, 360, 73, "length"], [336, 81, 360, 79], [336, 101, 360, 99], [336, 102, 360, 100], [337, 10, 362, 8, "detectedFaces"], [337, 23, 362, 21], [337, 24, 362, 22, "for<PERSON>ach"], [337, 31, 362, 29], [337, 32, 362, 30], [337, 33, 362, 31, "detection"], [337, 42, 362, 40], [337, 44, 362, 42, "index"], [337, 49, 362, 47], [337, 54, 362, 52], [338, 12, 363, 10], [338, 18, 363, 16, "bbox"], [338, 22, 363, 20], [338, 25, 363, 23, "detection"], [338, 34, 363, 32], [338, 35, 363, 33, "boundingBox"], [338, 46, 363, 44], [340, 12, 365, 10], [341, 12, 366, 10], [341, 18, 366, 16, "faceX"], [341, 23, 366, 21], [341, 26, 366, 24, "bbox"], [341, 30, 366, 28], [341, 31, 366, 29, "xCenter"], [341, 38, 366, 36], [341, 41, 366, 39, "img"], [341, 44, 366, 42], [341, 45, 366, 43, "width"], [341, 50, 366, 48], [341, 53, 366, 52, "bbox"], [341, 57, 366, 56], [341, 58, 366, 57, "width"], [341, 63, 366, 62], [341, 66, 366, 65, "img"], [341, 69, 366, 68], [341, 70, 366, 69, "width"], [341, 75, 366, 74], [341, 78, 366, 78], [341, 79, 366, 79], [342, 12, 367, 10], [342, 18, 367, 16, "faceY"], [342, 23, 367, 21], [342, 26, 367, 24, "bbox"], [342, 30, 367, 28], [342, 31, 367, 29, "yCenter"], [342, 38, 367, 36], [342, 41, 367, 39, "img"], [342, 44, 367, 42], [342, 45, 367, 43, "height"], [342, 51, 367, 49], [342, 54, 367, 53, "bbox"], [342, 58, 367, 57], [342, 59, 367, 58, "height"], [342, 65, 367, 64], [342, 68, 367, 67, "img"], [342, 71, 367, 70], [342, 72, 367, 71, "height"], [342, 78, 367, 77], [342, 81, 367, 81], [342, 82, 367, 82], [343, 12, 368, 10], [343, 18, 368, 16, "faceWidth"], [343, 27, 368, 25], [343, 30, 368, 28, "bbox"], [343, 34, 368, 32], [343, 35, 368, 33, "width"], [343, 40, 368, 38], [343, 43, 368, 41, "img"], [343, 46, 368, 44], [343, 47, 368, 45, "width"], [343, 52, 368, 50], [344, 12, 369, 10], [344, 18, 369, 16, "faceHeight"], [344, 28, 369, 26], [344, 31, 369, 29, "bbox"], [344, 35, 369, 33], [344, 36, 369, 34, "height"], [344, 42, 369, 40], [344, 45, 369, 43, "img"], [344, 48, 369, 46], [344, 49, 369, 47, "height"], [344, 55, 369, 53], [346, 12, 371, 10], [347, 12, 372, 10], [347, 18, 372, 16, "padding"], [347, 25, 372, 23], [347, 28, 372, 26], [347, 31, 372, 29], [348, 12, 373, 10], [348, 18, 373, 16, "paddedX"], [348, 25, 373, 23], [348, 28, 373, 26, "Math"], [348, 32, 373, 30], [348, 33, 373, 31, "max"], [348, 36, 373, 34], [348, 37, 373, 35], [348, 38, 373, 36], [348, 40, 373, 38, "faceX"], [348, 45, 373, 43], [348, 48, 373, 46, "faceWidth"], [348, 57, 373, 55], [348, 60, 373, 58, "padding"], [348, 67, 373, 65], [348, 68, 373, 66], [349, 12, 374, 10], [349, 18, 374, 16, "paddedY"], [349, 25, 374, 23], [349, 28, 374, 26, "Math"], [349, 32, 374, 30], [349, 33, 374, 31, "max"], [349, 36, 374, 34], [349, 37, 374, 35], [349, 38, 374, 36], [349, 40, 374, 38, "faceY"], [349, 45, 374, 43], [349, 48, 374, 46, "faceHeight"], [349, 58, 374, 56], [349, 61, 374, 59, "padding"], [349, 68, 374, 66], [349, 69, 374, 67], [350, 12, 375, 10], [350, 18, 375, 16, "<PERSON><PERSON><PERSON><PERSON>"], [350, 29, 375, 27], [350, 32, 375, 30, "Math"], [350, 36, 375, 34], [350, 37, 375, 35, "min"], [350, 40, 375, 38], [350, 41, 375, 39, "img"], [350, 44, 375, 42], [350, 45, 375, 43, "width"], [350, 50, 375, 48], [350, 53, 375, 51, "paddedX"], [350, 60, 375, 58], [350, 62, 375, 60, "faceWidth"], [350, 71, 375, 69], [350, 75, 375, 73], [350, 76, 375, 74], [350, 79, 375, 77], [350, 80, 375, 78], [350, 83, 375, 81, "padding"], [350, 90, 375, 88], [350, 91, 375, 89], [350, 92, 375, 90], [351, 12, 376, 10], [351, 18, 376, 16, "paddedHeight"], [351, 30, 376, 28], [351, 33, 376, 31, "Math"], [351, 37, 376, 35], [351, 38, 376, 36, "min"], [351, 41, 376, 39], [351, 42, 376, 40, "img"], [351, 45, 376, 43], [351, 46, 376, 44, "height"], [351, 52, 376, 50], [351, 55, 376, 53, "paddedY"], [351, 62, 376, 60], [351, 64, 376, 62, "faceHeight"], [351, 74, 376, 72], [351, 78, 376, 76], [351, 79, 376, 77], [351, 82, 376, 80], [351, 83, 376, 81], [351, 86, 376, 84, "padding"], [351, 93, 376, 91], [351, 94, 376, 92], [351, 95, 376, 93], [352, 12, 378, 10, "console"], [352, 19, 378, 17], [352, 20, 378, 18, "log"], [352, 23, 378, 21], [352, 24, 378, 22], [352, 60, 378, 58, "index"], [352, 65, 378, 63], [352, 68, 378, 66], [352, 69, 378, 67], [352, 72, 378, 70], [352, 74, 378, 72], [353, 14, 379, 12, "original"], [353, 22, 379, 20], [353, 24, 379, 22], [354, 16, 379, 24, "x"], [354, 17, 379, 25], [354, 19, 379, 27, "Math"], [354, 23, 379, 31], [354, 24, 379, 32, "round"], [354, 29, 379, 37], [354, 30, 379, 38, "faceX"], [354, 35, 379, 43], [354, 36, 379, 44], [355, 16, 379, 46, "y"], [355, 17, 379, 47], [355, 19, 379, 49, "Math"], [355, 23, 379, 53], [355, 24, 379, 54, "round"], [355, 29, 379, 59], [355, 30, 379, 60, "faceY"], [355, 35, 379, 65], [355, 36, 379, 66], [356, 16, 379, 68, "w"], [356, 17, 379, 69], [356, 19, 379, 71, "Math"], [356, 23, 379, 75], [356, 24, 379, 76, "round"], [356, 29, 379, 81], [356, 30, 379, 82, "faceWidth"], [356, 39, 379, 91], [356, 40, 379, 92], [357, 16, 379, 94, "h"], [357, 17, 379, 95], [357, 19, 379, 97, "Math"], [357, 23, 379, 101], [357, 24, 379, 102, "round"], [357, 29, 379, 107], [357, 30, 379, 108, "faceHeight"], [357, 40, 379, 118], [358, 14, 379, 120], [358, 15, 379, 121], [359, 14, 380, 12, "padded"], [359, 20, 380, 18], [359, 22, 380, 20], [360, 16, 380, 22, "x"], [360, 17, 380, 23], [360, 19, 380, 25, "Math"], [360, 23, 380, 29], [360, 24, 380, 30, "round"], [360, 29, 380, 35], [360, 30, 380, 36, "paddedX"], [360, 37, 380, 43], [360, 38, 380, 44], [361, 16, 380, 46, "y"], [361, 17, 380, 47], [361, 19, 380, 49, "Math"], [361, 23, 380, 53], [361, 24, 380, 54, "round"], [361, 29, 380, 59], [361, 30, 380, 60, "paddedY"], [361, 37, 380, 67], [361, 38, 380, 68], [362, 16, 380, 70, "w"], [362, 17, 380, 71], [362, 19, 380, 73, "Math"], [362, 23, 380, 77], [362, 24, 380, 78, "round"], [362, 29, 380, 83], [362, 30, 380, 84, "<PERSON><PERSON><PERSON><PERSON>"], [362, 41, 380, 95], [362, 42, 380, 96], [363, 16, 380, 98, "h"], [363, 17, 380, 99], [363, 19, 380, 101, "Math"], [363, 23, 380, 105], [363, 24, 380, 106, "round"], [363, 29, 380, 111], [363, 30, 380, 112, "paddedHeight"], [363, 42, 380, 124], [364, 14, 380, 126], [365, 12, 381, 10], [365, 13, 381, 11], [365, 14, 381, 12], [367, 12, 383, 10], [368, 12, 384, 10, "applyStrongBlur"], [368, 27, 384, 25], [368, 28, 384, 26, "ctx"], [368, 31, 384, 29], [368, 33, 384, 31, "paddedX"], [368, 40, 384, 38], [368, 42, 384, 40, "paddedY"], [368, 49, 384, 47], [368, 51, 384, 49, "<PERSON><PERSON><PERSON><PERSON>"], [368, 62, 384, 60], [368, 64, 384, 62, "paddedHeight"], [368, 76, 384, 74], [368, 77, 384, 75], [369, 12, 385, 10, "console"], [369, 19, 385, 17], [369, 20, 385, 18, "log"], [369, 23, 385, 21], [369, 24, 385, 22], [369, 50, 385, 48, "index"], [369, 55, 385, 53], [369, 58, 385, 56], [369, 59, 385, 57], [369, 79, 385, 77], [369, 80, 385, 78], [370, 10, 386, 8], [370, 11, 386, 9], [370, 12, 386, 10], [371, 10, 388, 8, "console"], [371, 17, 388, 15], [371, 18, 388, 16, "log"], [371, 21, 388, 19], [371, 22, 388, 20], [371, 48, 388, 46, "detectedFaces"], [371, 61, 388, 59], [371, 62, 388, 60, "length"], [371, 68, 388, 66], [371, 104, 388, 102], [371, 105, 388, 103], [372, 8, 389, 6], [372, 9, 389, 7], [372, 15, 389, 13], [373, 10, 390, 8, "console"], [373, 17, 390, 15], [373, 18, 390, 16, "log"], [373, 21, 390, 19], [373, 22, 390, 20], [373, 109, 390, 107], [373, 110, 390, 108], [374, 10, 391, 8], [375, 10, 392, 8, "applyFallbackFaceBlur"], [375, 31, 392, 29], [375, 32, 392, 30, "ctx"], [375, 35, 392, 33], [375, 37, 392, 35, "img"], [375, 40, 392, 38], [375, 41, 392, 39, "width"], [375, 46, 392, 44], [375, 48, 392, 46, "img"], [375, 51, 392, 49], [375, 52, 392, 50, "height"], [375, 58, 392, 56], [375, 59, 392, 57], [376, 8, 393, 6], [377, 8, 395, 6, "setProcessingProgress"], [377, 29, 395, 27], [377, 30, 395, 28], [377, 32, 395, 30], [377, 33, 395, 31], [379, 8, 397, 6], [380, 8, 398, 6, "console"], [380, 15, 398, 13], [380, 16, 398, 14, "log"], [380, 19, 398, 17], [380, 20, 398, 18], [380, 85, 398, 83], [380, 86, 398, 84], [381, 8, 399, 6], [381, 14, 399, 12, "blurredImageBlob"], [381, 30, 399, 28], [381, 33, 399, 31], [381, 39, 399, 37], [381, 43, 399, 41, "Promise"], [381, 50, 399, 48], [381, 51, 399, 56, "resolve"], [381, 58, 399, 63], [381, 62, 399, 68], [382, 10, 400, 8, "canvas"], [382, 16, 400, 14], [382, 17, 400, 15, "toBlob"], [382, 23, 400, 21], [382, 24, 400, 23, "blob"], [382, 28, 400, 27], [382, 32, 400, 32, "resolve"], [382, 39, 400, 39], [382, 40, 400, 40, "blob"], [382, 44, 400, 45], [382, 45, 400, 46], [382, 47, 400, 48], [382, 59, 400, 60], [382, 61, 400, 62], [382, 64, 400, 65], [382, 65, 400, 66], [383, 8, 401, 6], [383, 9, 401, 7], [383, 10, 401, 8], [384, 8, 403, 6], [384, 14, 403, 12, "blurredImageUrl"], [384, 29, 403, 27], [384, 32, 403, 30, "URL"], [384, 35, 403, 33], [384, 36, 403, 34, "createObjectURL"], [384, 51, 403, 49], [384, 52, 403, 50, "blurredImageBlob"], [384, 68, 403, 66], [384, 69, 403, 67], [385, 8, 404, 6, "console"], [385, 15, 404, 13], [385, 16, 404, 14, "log"], [385, 19, 404, 17], [385, 20, 404, 18], [385, 66, 404, 64], [385, 68, 404, 66, "blurredImageUrl"], [385, 83, 404, 81], [385, 84, 404, 82, "substring"], [385, 93, 404, 91], [385, 94, 404, 92], [385, 95, 404, 93], [385, 97, 404, 95], [385, 99, 404, 97], [385, 100, 404, 98], [385, 103, 404, 101], [385, 108, 404, 106], [385, 109, 404, 107], [386, 8, 406, 6, "setProcessingProgress"], [386, 29, 406, 27], [386, 30, 406, 28], [386, 33, 406, 31], [386, 34, 406, 32], [388, 8, 408, 6], [389, 8, 409, 6], [389, 14, 409, 12, "completeProcessing"], [389, 32, 409, 30], [389, 33, 409, 31, "blurredImageUrl"], [389, 48, 409, 46], [389, 49, 409, 47], [390, 6, 411, 4], [390, 7, 411, 5], [390, 8, 411, 6], [390, 15, 411, 13, "error"], [390, 20, 411, 18], [390, 22, 411, 20], [391, 8, 412, 6, "console"], [391, 15, 412, 13], [391, 16, 412, 14, "error"], [391, 21, 412, 19], [391, 22, 412, 20], [391, 57, 412, 55], [391, 59, 412, 57, "error"], [391, 64, 412, 62], [391, 65, 412, 63], [392, 8, 413, 6, "setErrorMessage"], [392, 23, 413, 21], [392, 24, 413, 22], [392, 50, 413, 48], [392, 51, 413, 49], [393, 8, 414, 6, "setProcessingState"], [393, 26, 414, 24], [393, 27, 414, 25], [393, 34, 414, 32], [393, 35, 414, 33], [394, 6, 415, 4], [395, 4, 416, 2], [395, 5, 416, 3], [397, 4, 418, 2], [398, 4, 419, 2], [398, 10, 419, 8, "completeProcessing"], [398, 28, 419, 26], [398, 31, 419, 29], [398, 37, 419, 36, "blurredImageUrl"], [398, 52, 419, 59], [398, 56, 419, 64], [399, 6, 420, 4], [399, 10, 420, 8], [400, 8, 421, 6, "setProcessingState"], [400, 26, 421, 24], [400, 27, 421, 25], [400, 37, 421, 35], [400, 38, 421, 36], [402, 8, 423, 6], [403, 8, 424, 6], [403, 14, 424, 12, "timestamp"], [403, 23, 424, 21], [403, 26, 424, 24, "Date"], [403, 30, 424, 28], [403, 31, 424, 29, "now"], [403, 34, 424, 32], [403, 35, 424, 33], [403, 36, 424, 34], [404, 8, 425, 6], [404, 14, 425, 12, "result"], [404, 20, 425, 18], [404, 23, 425, 21], [405, 10, 426, 8, "imageUrl"], [405, 18, 426, 16], [405, 20, 426, 18, "blurredImageUrl"], [405, 35, 426, 33], [406, 10, 427, 8, "localUri"], [406, 18, 427, 16], [406, 20, 427, 18, "blurredImageUrl"], [406, 35, 427, 33], [407, 10, 428, 8, "challengeCode"], [407, 23, 428, 21], [407, 25, 428, 23, "challengeCode"], [407, 38, 428, 36], [407, 42, 428, 40], [407, 44, 428, 42], [408, 10, 429, 8, "timestamp"], [408, 19, 429, 17], [409, 10, 430, 8, "jobId"], [409, 15, 430, 13], [409, 17, 430, 15], [409, 27, 430, 25, "timestamp"], [409, 36, 430, 34], [409, 38, 430, 36], [410, 10, 431, 8, "status"], [410, 16, 431, 14], [410, 18, 431, 16], [411, 8, 432, 6], [411, 9, 432, 7], [412, 8, 434, 6, "console"], [412, 15, 434, 13], [412, 16, 434, 14, "log"], [412, 19, 434, 17], [412, 20, 434, 18], [412, 100, 434, 98], [412, 102, 434, 100], [413, 10, 435, 8, "imageUrl"], [413, 18, 435, 16], [413, 20, 435, 18, "blurredImageUrl"], [413, 35, 435, 33], [413, 36, 435, 34, "substring"], [413, 45, 435, 43], [413, 46, 435, 44], [413, 47, 435, 45], [413, 49, 435, 47], [413, 51, 435, 49], [413, 52, 435, 50], [413, 55, 435, 53], [413, 60, 435, 58], [414, 10, 436, 8, "timestamp"], [414, 19, 436, 17], [415, 10, 437, 8, "jobId"], [415, 15, 437, 13], [415, 17, 437, 15, "result"], [415, 23, 437, 21], [415, 24, 437, 22, "jobId"], [416, 8, 438, 6], [416, 9, 438, 7], [416, 10, 438, 8], [418, 8, 440, 6], [419, 8, 441, 6, "onComplete"], [419, 18, 441, 16], [419, 19, 441, 17, "result"], [419, 25, 441, 23], [419, 26, 441, 24], [420, 6, 443, 4], [420, 7, 443, 5], [420, 8, 443, 6], [420, 15, 443, 13, "error"], [420, 20, 443, 18], [420, 22, 443, 20], [421, 8, 444, 6, "console"], [421, 15, 444, 13], [421, 16, 444, 14, "error"], [421, 21, 444, 19], [421, 22, 444, 20], [421, 57, 444, 55], [421, 59, 444, 57, "error"], [421, 64, 444, 62], [421, 65, 444, 63], [422, 8, 445, 6, "setErrorMessage"], [422, 23, 445, 21], [422, 24, 445, 22], [422, 56, 445, 54], [422, 57, 445, 55], [423, 8, 446, 6, "setProcessingState"], [423, 26, 446, 24], [423, 27, 446, 25], [423, 34, 446, 32], [423, 35, 446, 33], [424, 6, 447, 4], [425, 4, 448, 2], [425, 5, 448, 3], [427, 4, 450, 2], [428, 4, 451, 2], [428, 10, 451, 8, "triggerServerProcessing"], [428, 33, 451, 31], [428, 36, 451, 34], [428, 42, 451, 34, "triggerServerProcessing"], [428, 43, 451, 41, "privateImageUrl"], [428, 58, 451, 64], [428, 60, 451, 66, "timestamp"], [428, 69, 451, 83], [428, 74, 451, 88], [429, 6, 452, 4], [429, 10, 452, 8], [430, 8, 453, 6, "console"], [430, 15, 453, 13], [430, 16, 453, 14, "log"], [430, 19, 453, 17], [430, 20, 453, 18], [430, 74, 453, 72], [430, 76, 453, 74, "privateImageUrl"], [430, 91, 453, 89], [430, 92, 453, 90], [431, 8, 454, 6, "setProcessingState"], [431, 26, 454, 24], [431, 27, 454, 25], [431, 39, 454, 37], [431, 40, 454, 38], [432, 8, 455, 6, "setProcessingProgress"], [432, 29, 455, 27], [432, 30, 455, 28], [432, 32, 455, 30], [432, 33, 455, 31], [433, 8, 457, 6], [433, 14, 457, 12, "requestBody"], [433, 25, 457, 23], [433, 28, 457, 26], [434, 10, 458, 8, "imageUrl"], [434, 18, 458, 16], [434, 20, 458, 18, "privateImageUrl"], [434, 35, 458, 33], [435, 10, 459, 8, "userId"], [435, 16, 459, 14], [436, 10, 460, 8, "requestId"], [436, 19, 460, 17], [437, 10, 461, 8, "timestamp"], [437, 19, 461, 17], [438, 10, 462, 8, "platform"], [438, 18, 462, 16], [438, 20, 462, 18], [439, 8, 463, 6], [439, 9, 463, 7], [440, 8, 465, 6, "console"], [440, 15, 465, 13], [440, 16, 465, 14, "log"], [440, 19, 465, 17], [440, 20, 465, 18], [440, 65, 465, 63], [440, 67, 465, 65, "requestBody"], [440, 78, 465, 76], [440, 79, 465, 77], [442, 8, 467, 6], [443, 8, 468, 6], [443, 14, 468, 12, "response"], [443, 22, 468, 20], [443, 25, 468, 23], [443, 31, 468, 29, "fetch"], [443, 36, 468, 34], [443, 37, 468, 35], [443, 40, 468, 38, "API_BASE_URL"], [443, 52, 468, 50], [443, 72, 468, 70], [443, 74, 468, 72], [444, 10, 469, 8, "method"], [444, 16, 469, 14], [444, 18, 469, 16], [444, 24, 469, 22], [445, 10, 470, 8, "headers"], [445, 17, 470, 15], [445, 19, 470, 17], [446, 12, 471, 10], [446, 26, 471, 24], [446, 28, 471, 26], [446, 46, 471, 44], [447, 12, 472, 10], [447, 27, 472, 25], [447, 29, 472, 27], [447, 39, 472, 37], [447, 45, 472, 43, "getAuthToken"], [447, 57, 472, 55], [447, 58, 472, 56], [447, 59, 472, 57], [448, 10, 473, 8], [448, 11, 473, 9], [449, 10, 474, 8, "body"], [449, 14, 474, 12], [449, 16, 474, 14, "JSON"], [449, 20, 474, 18], [449, 21, 474, 19, "stringify"], [449, 30, 474, 28], [449, 31, 474, 29, "requestBody"], [449, 42, 474, 40], [450, 8, 475, 6], [450, 9, 475, 7], [450, 10, 475, 8], [451, 8, 477, 6], [451, 12, 477, 10], [451, 13, 477, 11, "response"], [451, 21, 477, 19], [451, 22, 477, 20, "ok"], [451, 24, 477, 22], [451, 26, 477, 24], [452, 10, 478, 8], [452, 16, 478, 14, "errorText"], [452, 25, 478, 23], [452, 28, 478, 26], [452, 34, 478, 32, "response"], [452, 42, 478, 40], [452, 43, 478, 41, "text"], [452, 47, 478, 45], [452, 48, 478, 46], [452, 49, 478, 47], [453, 10, 479, 8, "console"], [453, 17, 479, 15], [453, 18, 479, 16, "error"], [453, 23, 479, 21], [453, 24, 479, 22], [453, 68, 479, 66], [453, 70, 479, 68, "response"], [453, 78, 479, 76], [453, 79, 479, 77, "status"], [453, 85, 479, 83], [453, 87, 479, 85, "errorText"], [453, 96, 479, 94], [453, 97, 479, 95], [454, 10, 480, 8], [454, 16, 480, 14], [454, 20, 480, 18, "Error"], [454, 25, 480, 23], [454, 26, 480, 24], [454, 48, 480, 46, "response"], [454, 56, 480, 54], [454, 57, 480, 55, "status"], [454, 63, 480, 61], [454, 67, 480, 65, "response"], [454, 75, 480, 73], [454, 76, 480, 74, "statusText"], [454, 86, 480, 84], [454, 88, 480, 86], [454, 89, 480, 87], [455, 8, 481, 6], [456, 8, 483, 6], [456, 14, 483, 12, "result"], [456, 20, 483, 18], [456, 23, 483, 21], [456, 29, 483, 27, "response"], [456, 37, 483, 35], [456, 38, 483, 36, "json"], [456, 42, 483, 40], [456, 43, 483, 41], [456, 44, 483, 42], [457, 8, 484, 6, "console"], [457, 15, 484, 13], [457, 16, 484, 14, "log"], [457, 19, 484, 17], [457, 20, 484, 18], [457, 68, 484, 66], [457, 70, 484, 68, "result"], [457, 76, 484, 74], [457, 77, 484, 75], [458, 8, 486, 6], [458, 12, 486, 10], [458, 13, 486, 11, "result"], [458, 19, 486, 17], [458, 20, 486, 18, "jobId"], [458, 25, 486, 23], [458, 27, 486, 25], [459, 10, 487, 8], [459, 16, 487, 14], [459, 20, 487, 18, "Error"], [459, 25, 487, 23], [459, 26, 487, 24], [459, 70, 487, 68], [459, 71, 487, 69], [460, 8, 488, 6], [462, 8, 490, 6], [463, 8, 491, 6], [463, 14, 491, 12, "pollForCompletion"], [463, 31, 491, 29], [463, 32, 491, 30, "result"], [463, 38, 491, 36], [463, 39, 491, 37, "jobId"], [463, 44, 491, 42], [463, 46, 491, 44, "timestamp"], [463, 55, 491, 53], [463, 56, 491, 54], [464, 6, 492, 4], [464, 7, 492, 5], [464, 8, 492, 6], [464, 15, 492, 13, "error"], [464, 20, 492, 18], [464, 22, 492, 20], [465, 8, 493, 6, "console"], [465, 15, 493, 13], [465, 16, 493, 14, "error"], [465, 21, 493, 19], [465, 22, 493, 20], [465, 57, 493, 55], [465, 59, 493, 57, "error"], [465, 64, 493, 62], [465, 65, 493, 63], [466, 8, 494, 6, "setErrorMessage"], [466, 23, 494, 21], [466, 24, 494, 22], [466, 52, 494, 50, "error"], [466, 57, 494, 55], [466, 58, 494, 56, "message"], [466, 65, 494, 63], [466, 67, 494, 65], [466, 68, 494, 66], [467, 8, 495, 6, "setProcessingState"], [467, 26, 495, 24], [467, 27, 495, 25], [467, 34, 495, 32], [467, 35, 495, 33], [468, 6, 496, 4], [469, 4, 497, 2], [469, 5, 497, 3], [470, 4, 498, 2], [471, 4, 499, 2], [471, 10, 499, 8, "pollForCompletion"], [471, 27, 499, 25], [471, 30, 499, 28], [471, 36, 499, 28, "pollForCompletion"], [471, 37, 499, 35, "jobId"], [471, 42, 499, 48], [471, 44, 499, 50, "timestamp"], [471, 53, 499, 67], [471, 55, 499, 69, "attempts"], [471, 63, 499, 77], [471, 66, 499, 80], [471, 67, 499, 81], [471, 72, 499, 86], [472, 6, 500, 4], [472, 12, 500, 10, "MAX_ATTEMPTS"], [472, 24, 500, 22], [472, 27, 500, 25], [472, 29, 500, 27], [472, 30, 500, 28], [472, 31, 500, 29], [473, 6, 501, 4], [473, 12, 501, 10, "POLL_INTERVAL"], [473, 25, 501, 23], [473, 28, 501, 26], [473, 32, 501, 30], [473, 33, 501, 31], [473, 34, 501, 32], [475, 6, 503, 4, "console"], [475, 13, 503, 11], [475, 14, 503, 12, "log"], [475, 17, 503, 15], [475, 18, 503, 16], [475, 53, 503, 51, "attempts"], [475, 61, 503, 59], [475, 64, 503, 62], [475, 65, 503, 63], [475, 69, 503, 67, "MAX_ATTEMPTS"], [475, 81, 503, 79], [475, 93, 503, 91, "jobId"], [475, 98, 503, 96], [475, 100, 503, 98], [475, 101, 503, 99], [476, 6, 505, 4], [476, 10, 505, 8, "attempts"], [476, 18, 505, 16], [476, 22, 505, 20, "MAX_ATTEMPTS"], [476, 34, 505, 32], [476, 36, 505, 34], [477, 8, 506, 6, "console"], [477, 15, 506, 13], [477, 16, 506, 14, "error"], [477, 21, 506, 19], [477, 22, 506, 20], [477, 75, 506, 73], [477, 76, 506, 74], [478, 8, 507, 6, "setErrorMessage"], [478, 23, 507, 21], [478, 24, 507, 22], [478, 63, 507, 61], [478, 64, 507, 62], [479, 8, 508, 6, "setProcessingState"], [479, 26, 508, 24], [479, 27, 508, 25], [479, 34, 508, 32], [479, 35, 508, 33], [480, 8, 509, 6], [481, 6, 510, 4], [482, 6, 512, 4], [482, 10, 512, 8], [483, 8, 513, 6], [483, 14, 513, 12, "response"], [483, 22, 513, 20], [483, 25, 513, 23], [483, 31, 513, 29, "fetch"], [483, 36, 513, 34], [483, 37, 513, 35], [483, 40, 513, 38, "API_BASE_URL"], [483, 52, 513, 50], [483, 75, 513, 73, "jobId"], [483, 80, 513, 78], [483, 82, 513, 80], [483, 84, 513, 82], [484, 10, 514, 8, "headers"], [484, 17, 514, 15], [484, 19, 514, 17], [485, 12, 515, 10], [485, 27, 515, 25], [485, 29, 515, 27], [485, 39, 515, 37], [485, 45, 515, 43, "getAuthToken"], [485, 57, 515, 55], [485, 58, 515, 56], [485, 59, 515, 57], [486, 10, 516, 8], [487, 8, 517, 6], [487, 9, 517, 7], [487, 10, 517, 8], [488, 8, 519, 6], [488, 12, 519, 10], [488, 13, 519, 11, "response"], [488, 21, 519, 19], [488, 22, 519, 20, "ok"], [488, 24, 519, 22], [488, 26, 519, 24], [489, 10, 520, 8], [489, 16, 520, 14], [489, 20, 520, 18, "Error"], [489, 25, 520, 23], [489, 26, 520, 24], [489, 34, 520, 32, "response"], [489, 42, 520, 40], [489, 43, 520, 41, "status"], [489, 49, 520, 47], [489, 54, 520, 52, "response"], [489, 62, 520, 60], [489, 63, 520, 61, "statusText"], [489, 73, 520, 71], [489, 75, 520, 73], [489, 76, 520, 74], [490, 8, 521, 6], [491, 8, 523, 6], [491, 14, 523, 12, "status"], [491, 20, 523, 18], [491, 23, 523, 21], [491, 29, 523, 27, "response"], [491, 37, 523, 35], [491, 38, 523, 36, "json"], [491, 42, 523, 40], [491, 43, 523, 41], [491, 44, 523, 42], [492, 8, 524, 6, "console"], [492, 15, 524, 13], [492, 16, 524, 14, "log"], [492, 19, 524, 17], [492, 20, 524, 18], [492, 54, 524, 52], [492, 56, 524, 54, "status"], [492, 62, 524, 60], [492, 63, 524, 61], [493, 8, 526, 6], [493, 12, 526, 10, "status"], [493, 18, 526, 16], [493, 19, 526, 17, "status"], [493, 25, 526, 23], [493, 30, 526, 28], [493, 41, 526, 39], [493, 43, 526, 41], [494, 10, 527, 8, "console"], [494, 17, 527, 15], [494, 18, 527, 16, "log"], [494, 21, 527, 19], [494, 22, 527, 20], [494, 73, 527, 71], [494, 74, 527, 72], [495, 10, 528, 8, "setProcessingProgress"], [495, 31, 528, 29], [495, 32, 528, 30], [495, 35, 528, 33], [495, 36, 528, 34], [496, 10, 529, 8, "setProcessingState"], [496, 28, 529, 26], [496, 29, 529, 27], [496, 40, 529, 38], [496, 41, 529, 39], [497, 10, 530, 8], [498, 10, 531, 8], [498, 16, 531, 14, "result"], [498, 22, 531, 20], [498, 25, 531, 23], [499, 12, 532, 10, "imageUrl"], [499, 20, 532, 18], [499, 22, 532, 20, "status"], [499, 28, 532, 26], [499, 29, 532, 27, "publicUrl"], [499, 38, 532, 36], [500, 12, 532, 38], [501, 12, 533, 10, "localUri"], [501, 20, 533, 18], [501, 22, 533, 20, "capturedPhoto"], [501, 35, 533, 33], [501, 39, 533, 37, "status"], [501, 45, 533, 43], [501, 46, 533, 44, "publicUrl"], [501, 55, 533, 53], [502, 12, 533, 55], [503, 12, 534, 10, "challengeCode"], [503, 25, 534, 23], [503, 27, 534, 25, "challengeCode"], [503, 40, 534, 38], [503, 44, 534, 42], [503, 46, 534, 44], [504, 12, 535, 10, "timestamp"], [504, 21, 535, 19], [505, 12, 536, 10, "processingStatus"], [505, 28, 536, 26], [505, 30, 536, 28], [506, 10, 537, 8], [506, 11, 537, 9], [507, 10, 538, 8, "console"], [507, 17, 538, 15], [507, 18, 538, 16, "log"], [507, 21, 538, 19], [507, 22, 538, 20], [507, 57, 538, 55], [507, 59, 538, 57, "result"], [507, 65, 538, 63], [507, 66, 538, 64], [508, 10, 539, 8, "onComplete"], [508, 20, 539, 18], [508, 21, 539, 19, "result"], [508, 27, 539, 25], [508, 28, 539, 26], [509, 10, 540, 8], [510, 8, 541, 6], [510, 9, 541, 7], [510, 15, 541, 13], [510, 19, 541, 17, "status"], [510, 25, 541, 23], [510, 26, 541, 24, "status"], [510, 32, 541, 30], [510, 37, 541, 35], [510, 45, 541, 43], [510, 47, 541, 45], [511, 10, 542, 8, "console"], [511, 17, 542, 15], [511, 18, 542, 16, "error"], [511, 23, 542, 21], [511, 24, 542, 22], [511, 60, 542, 58], [511, 62, 542, 60, "status"], [511, 68, 542, 66], [511, 69, 542, 67, "error"], [511, 74, 542, 72], [511, 75, 542, 73], [512, 10, 543, 8], [512, 16, 543, 14], [512, 20, 543, 18, "Error"], [512, 25, 543, 23], [512, 26, 543, 24, "status"], [512, 32, 543, 30], [512, 33, 543, 31, "error"], [512, 38, 543, 36], [512, 42, 543, 40], [512, 61, 543, 59], [512, 62, 543, 60], [513, 8, 544, 6], [513, 9, 544, 7], [513, 15, 544, 13], [514, 10, 545, 8], [515, 10, 546, 8], [515, 16, 546, 14, "progressValue"], [515, 29, 546, 27], [515, 32, 546, 30], [515, 34, 546, 32], [515, 37, 546, 36, "attempts"], [515, 45, 546, 44], [515, 48, 546, 47, "MAX_ATTEMPTS"], [515, 60, 546, 59], [515, 63, 546, 63], [515, 65, 546, 65], [516, 10, 547, 8, "console"], [516, 17, 547, 15], [516, 18, 547, 16, "log"], [516, 21, 547, 19], [516, 22, 547, 20], [516, 71, 547, 69, "progressValue"], [516, 84, 547, 82], [516, 87, 547, 85], [516, 88, 547, 86], [517, 10, 548, 8, "setProcessingProgress"], [517, 31, 548, 29], [517, 32, 548, 30, "progressValue"], [517, 45, 548, 43], [517, 46, 548, 44], [518, 10, 550, 8, "setTimeout"], [518, 20, 550, 18], [518, 21, 550, 19], [518, 27, 550, 25], [519, 12, 551, 10, "pollForCompletion"], [519, 29, 551, 27], [519, 30, 551, 28, "jobId"], [519, 35, 551, 33], [519, 37, 551, 35, "timestamp"], [519, 46, 551, 44], [519, 48, 551, 46, "attempts"], [519, 56, 551, 54], [519, 59, 551, 57], [519, 60, 551, 58], [519, 61, 551, 59], [520, 10, 552, 8], [520, 11, 552, 9], [520, 13, 552, 11, "POLL_INTERVAL"], [520, 26, 552, 24], [520, 27, 552, 25], [521, 8, 553, 6], [522, 6, 554, 4], [522, 7, 554, 5], [522, 8, 554, 6], [522, 15, 554, 13, "error"], [522, 20, 554, 18], [522, 22, 554, 20], [523, 8, 555, 6, "console"], [523, 15, 555, 13], [523, 16, 555, 14, "error"], [523, 21, 555, 19], [523, 22, 555, 20], [523, 54, 555, 52], [523, 56, 555, 54, "error"], [523, 61, 555, 59], [523, 62, 555, 60], [524, 8, 556, 6, "setErrorMessage"], [524, 23, 556, 21], [524, 24, 556, 22], [524, 62, 556, 60, "error"], [524, 67, 556, 65], [524, 68, 556, 66, "message"], [524, 75, 556, 73], [524, 77, 556, 75], [524, 78, 556, 76], [525, 8, 557, 6, "setProcessingState"], [525, 26, 557, 24], [525, 27, 557, 25], [525, 34, 557, 32], [525, 35, 557, 33], [526, 6, 558, 4], [527, 4, 559, 2], [527, 5, 559, 3], [528, 4, 560, 2], [529, 4, 561, 2], [529, 10, 561, 8, "getAuthToken"], [529, 22, 561, 20], [529, 25, 561, 23], [529, 31, 561, 23, "getAuthToken"], [529, 32, 561, 23], [529, 37, 561, 52], [530, 6, 562, 4], [531, 6, 563, 4], [532, 6, 564, 4], [532, 13, 564, 11], [532, 30, 564, 28], [533, 4, 565, 2], [533, 5, 565, 3], [535, 4, 567, 2], [536, 4, 568, 2], [536, 10, 568, 8, "retryCapture"], [536, 22, 568, 20], [536, 25, 568, 23], [536, 29, 568, 23, "useCallback"], [536, 47, 568, 34], [536, 49, 568, 35], [536, 55, 568, 41], [537, 6, 569, 4, "console"], [537, 13, 569, 11], [537, 14, 569, 12, "log"], [537, 17, 569, 15], [537, 18, 569, 16], [537, 55, 569, 53], [537, 56, 569, 54], [538, 6, 570, 4, "setProcessingState"], [538, 24, 570, 22], [538, 25, 570, 23], [538, 31, 570, 29], [538, 32, 570, 30], [539, 6, 571, 4, "setErrorMessage"], [539, 21, 571, 19], [539, 22, 571, 20], [539, 24, 571, 22], [539, 25, 571, 23], [540, 6, 572, 4, "setCapturedPhoto"], [540, 22, 572, 20], [540, 23, 572, 21], [540, 25, 572, 23], [540, 26, 572, 24], [541, 6, 573, 4, "setProcessingProgress"], [541, 27, 573, 25], [541, 28, 573, 26], [541, 29, 573, 27], [541, 30, 573, 28], [542, 4, 574, 2], [542, 5, 574, 3], [542, 7, 574, 5], [542, 9, 574, 7], [542, 10, 574, 8], [543, 4, 575, 2], [544, 4, 576, 2], [544, 8, 576, 2, "useEffect"], [544, 24, 576, 11], [544, 26, 576, 12], [544, 32, 576, 18], [545, 6, 577, 4, "console"], [545, 13, 577, 11], [545, 14, 577, 12, "log"], [545, 17, 577, 15], [545, 18, 577, 16], [545, 53, 577, 51], [545, 55, 577, 53, "permission"], [545, 65, 577, 63], [545, 66, 577, 64], [546, 6, 578, 4], [546, 10, 578, 8, "permission"], [546, 20, 578, 18], [546, 22, 578, 20], [547, 8, 579, 6, "console"], [547, 15, 579, 13], [547, 16, 579, 14, "log"], [547, 19, 579, 17], [547, 20, 579, 18], [547, 57, 579, 55], [547, 59, 579, 57, "permission"], [547, 69, 579, 67], [547, 70, 579, 68, "granted"], [547, 77, 579, 75], [547, 78, 579, 76], [548, 6, 580, 4], [549, 4, 581, 2], [549, 5, 581, 3], [549, 7, 581, 5], [549, 8, 581, 6, "permission"], [549, 18, 581, 16], [549, 19, 581, 17], [549, 20, 581, 18], [550, 4, 582, 2], [551, 4, 583, 2], [551, 8, 583, 6], [551, 9, 583, 7, "permission"], [551, 19, 583, 17], [551, 21, 583, 19], [552, 6, 584, 4, "console"], [552, 13, 584, 11], [552, 14, 584, 12, "log"], [552, 17, 584, 15], [552, 18, 584, 16], [552, 67, 584, 65], [552, 68, 584, 66], [553, 6, 585, 4], [553, 26, 586, 6], [553, 30, 586, 6, "_jsxDevRuntime"], [553, 44, 586, 6], [553, 45, 586, 6, "jsxDEV"], [553, 51, 586, 6], [553, 53, 586, 7, "_View"], [553, 58, 586, 7], [553, 59, 586, 7, "default"], [553, 66, 586, 11], [554, 8, 586, 12, "style"], [554, 13, 586, 17], [554, 15, 586, 19, "styles"], [554, 21, 586, 25], [554, 22, 586, 26, "container"], [554, 31, 586, 36], [555, 8, 586, 36, "children"], [555, 16, 586, 36], [555, 32, 587, 8], [555, 36, 587, 8, "_jsxDevRuntime"], [555, 50, 587, 8], [555, 51, 587, 8, "jsxDEV"], [555, 57, 587, 8], [555, 59, 587, 9, "_ActivityIndicator"], [555, 77, 587, 9], [555, 78, 587, 9, "default"], [555, 85, 587, 26], [556, 10, 587, 27, "size"], [556, 14, 587, 31], [556, 16, 587, 32], [556, 23, 587, 39], [557, 10, 587, 40, "color"], [557, 15, 587, 45], [557, 17, 587, 46], [558, 8, 587, 55], [559, 10, 587, 55, "fileName"], [559, 18, 587, 55], [559, 20, 587, 55, "_jsxFileName"], [559, 32, 587, 55], [560, 10, 587, 55, "lineNumber"], [560, 20, 587, 55], [561, 10, 587, 55, "columnNumber"], [561, 22, 587, 55], [562, 8, 587, 55], [562, 15, 587, 57], [562, 16, 587, 58], [562, 31, 588, 8], [562, 35, 588, 8, "_jsxDevRuntime"], [562, 49, 588, 8], [562, 50, 588, 8, "jsxDEV"], [562, 56, 588, 8], [562, 58, 588, 9, "_Text"], [562, 63, 588, 9], [562, 64, 588, 9, "default"], [562, 71, 588, 13], [563, 10, 588, 14, "style"], [563, 15, 588, 19], [563, 17, 588, 21, "styles"], [563, 23, 588, 27], [563, 24, 588, 28, "loadingText"], [563, 35, 588, 40], [564, 10, 588, 40, "children"], [564, 18, 588, 40], [564, 20, 588, 41], [565, 8, 588, 58], [566, 10, 588, 58, "fileName"], [566, 18, 588, 58], [566, 20, 588, 58, "_jsxFileName"], [566, 32, 588, 58], [567, 10, 588, 58, "lineNumber"], [567, 20, 588, 58], [568, 10, 588, 58, "columnNumber"], [568, 22, 588, 58], [569, 8, 588, 58], [569, 15, 588, 64], [569, 16, 588, 65], [570, 6, 588, 65], [571, 8, 588, 65, "fileName"], [571, 16, 588, 65], [571, 18, 588, 65, "_jsxFileName"], [571, 30, 588, 65], [572, 8, 588, 65, "lineNumber"], [572, 18, 588, 65], [573, 8, 588, 65, "columnNumber"], [573, 20, 588, 65], [574, 6, 588, 65], [574, 13, 589, 12], [574, 14, 589, 13], [575, 4, 591, 2], [576, 4, 592, 2], [576, 8, 592, 6], [576, 9, 592, 7, "permission"], [576, 19, 592, 17], [576, 20, 592, 18, "granted"], [576, 27, 592, 25], [576, 29, 592, 27], [577, 6, 593, 4, "console"], [577, 13, 593, 11], [577, 14, 593, 12, "log"], [577, 17, 593, 15], [577, 18, 593, 16], [577, 93, 593, 91], [577, 94, 593, 92], [578, 6, 594, 4], [578, 26, 595, 6], [578, 30, 595, 6, "_jsxDevRuntime"], [578, 44, 595, 6], [578, 45, 595, 6, "jsxDEV"], [578, 51, 595, 6], [578, 53, 595, 7, "_View"], [578, 58, 595, 7], [578, 59, 595, 7, "default"], [578, 66, 595, 11], [579, 8, 595, 12, "style"], [579, 13, 595, 17], [579, 15, 595, 19, "styles"], [579, 21, 595, 25], [579, 22, 595, 26, "container"], [579, 31, 595, 36], [580, 8, 595, 36, "children"], [580, 16, 595, 36], [580, 31, 596, 8], [580, 35, 596, 8, "_jsxDevRuntime"], [580, 49, 596, 8], [580, 50, 596, 8, "jsxDEV"], [580, 56, 596, 8], [580, 58, 596, 9, "_View"], [580, 63, 596, 9], [580, 64, 596, 9, "default"], [580, 71, 596, 13], [581, 10, 596, 14, "style"], [581, 15, 596, 19], [581, 17, 596, 21, "styles"], [581, 23, 596, 27], [581, 24, 596, 28, "permissionContent"], [581, 41, 596, 46], [582, 10, 596, 46, "children"], [582, 18, 596, 46], [582, 34, 597, 10], [582, 38, 597, 10, "_jsxDevRuntime"], [582, 52, 597, 10], [582, 53, 597, 10, "jsxDEV"], [582, 59, 597, 10], [582, 61, 597, 11, "_lucideReactNative"], [582, 79, 597, 11], [582, 80, 597, 11, "Camera"], [582, 86, 597, 21], [583, 12, 597, 22, "size"], [583, 16, 597, 26], [583, 18, 597, 28], [583, 20, 597, 31], [584, 12, 597, 32, "color"], [584, 17, 597, 37], [584, 19, 597, 38], [585, 10, 597, 47], [586, 12, 597, 47, "fileName"], [586, 20, 597, 47], [586, 22, 597, 47, "_jsxFileName"], [586, 34, 597, 47], [587, 12, 597, 47, "lineNumber"], [587, 22, 597, 47], [588, 12, 597, 47, "columnNumber"], [588, 24, 597, 47], [589, 10, 597, 47], [589, 17, 597, 49], [589, 18, 597, 50], [589, 33, 598, 10], [589, 37, 598, 10, "_jsxDevRuntime"], [589, 51, 598, 10], [589, 52, 598, 10, "jsxDEV"], [589, 58, 598, 10], [589, 60, 598, 11, "_Text"], [589, 65, 598, 11], [589, 66, 598, 11, "default"], [589, 73, 598, 15], [590, 12, 598, 16, "style"], [590, 17, 598, 21], [590, 19, 598, 23, "styles"], [590, 25, 598, 29], [590, 26, 598, 30, "permissionTitle"], [590, 41, 598, 46], [591, 12, 598, 46, "children"], [591, 20, 598, 46], [591, 22, 598, 47], [592, 10, 598, 73], [593, 12, 598, 73, "fileName"], [593, 20, 598, 73], [593, 22, 598, 73, "_jsxFileName"], [593, 34, 598, 73], [594, 12, 598, 73, "lineNumber"], [594, 22, 598, 73], [595, 12, 598, 73, "columnNumber"], [595, 24, 598, 73], [596, 10, 598, 73], [596, 17, 598, 79], [596, 18, 598, 80], [596, 33, 599, 10], [596, 37, 599, 10, "_jsxDevRuntime"], [596, 51, 599, 10], [596, 52, 599, 10, "jsxDEV"], [596, 58, 599, 10], [596, 60, 599, 11, "_Text"], [596, 65, 599, 11], [596, 66, 599, 11, "default"], [596, 73, 599, 15], [597, 12, 599, 16, "style"], [597, 17, 599, 21], [597, 19, 599, 23, "styles"], [597, 25, 599, 29], [597, 26, 599, 30, "permissionDescription"], [597, 47, 599, 52], [598, 12, 599, 52, "children"], [598, 20, 599, 52], [598, 22, 599, 53], [599, 10, 602, 10], [600, 12, 602, 10, "fileName"], [600, 20, 602, 10], [600, 22, 602, 10, "_jsxFileName"], [600, 34, 602, 10], [601, 12, 602, 10, "lineNumber"], [601, 22, 602, 10], [602, 12, 602, 10, "columnNumber"], [602, 24, 602, 10], [603, 10, 602, 10], [603, 17, 602, 16], [603, 18, 602, 17], [603, 33, 603, 10], [603, 37, 603, 10, "_jsxDevRuntime"], [603, 51, 603, 10], [603, 52, 603, 10, "jsxDEV"], [603, 58, 603, 10], [603, 60, 603, 11, "_TouchableOpacity"], [603, 77, 603, 11], [603, 78, 603, 11, "default"], [603, 85, 603, 27], [604, 12, 603, 28, "onPress"], [604, 19, 603, 35], [604, 21, 603, 37, "requestPermission"], [604, 38, 603, 55], [605, 12, 603, 56, "style"], [605, 17, 603, 61], [605, 19, 603, 63, "styles"], [605, 25, 603, 69], [605, 26, 603, 70, "primaryButton"], [605, 39, 603, 84], [606, 12, 603, 84, "children"], [606, 20, 603, 84], [606, 35, 604, 12], [606, 39, 604, 12, "_jsxDevRuntime"], [606, 53, 604, 12], [606, 54, 604, 12, "jsxDEV"], [606, 60, 604, 12], [606, 62, 604, 13, "_Text"], [606, 67, 604, 13], [606, 68, 604, 13, "default"], [606, 75, 604, 17], [607, 14, 604, 18, "style"], [607, 19, 604, 23], [607, 21, 604, 25, "styles"], [607, 27, 604, 31], [607, 28, 604, 32, "primaryButtonText"], [607, 45, 604, 50], [608, 14, 604, 50, "children"], [608, 22, 604, 50], [608, 24, 604, 51], [609, 12, 604, 67], [610, 14, 604, 67, "fileName"], [610, 22, 604, 67], [610, 24, 604, 67, "_jsxFileName"], [610, 36, 604, 67], [611, 14, 604, 67, "lineNumber"], [611, 24, 604, 67], [612, 14, 604, 67, "columnNumber"], [612, 26, 604, 67], [613, 12, 604, 67], [613, 19, 604, 73], [614, 10, 604, 74], [615, 12, 604, 74, "fileName"], [615, 20, 604, 74], [615, 22, 604, 74, "_jsxFileName"], [615, 34, 604, 74], [616, 12, 604, 74, "lineNumber"], [616, 22, 604, 74], [617, 12, 604, 74, "columnNumber"], [617, 24, 604, 74], [618, 10, 604, 74], [618, 17, 605, 28], [618, 18, 605, 29], [618, 33, 606, 10], [618, 37, 606, 10, "_jsxDevRuntime"], [618, 51, 606, 10], [618, 52, 606, 10, "jsxDEV"], [618, 58, 606, 10], [618, 60, 606, 11, "_TouchableOpacity"], [618, 77, 606, 11], [618, 78, 606, 11, "default"], [618, 85, 606, 27], [619, 12, 606, 28, "onPress"], [619, 19, 606, 35], [619, 21, 606, 37, "onCancel"], [619, 29, 606, 46], [620, 12, 606, 47, "style"], [620, 17, 606, 52], [620, 19, 606, 54, "styles"], [620, 25, 606, 60], [620, 26, 606, 61, "secondaryButton"], [620, 41, 606, 77], [621, 12, 606, 77, "children"], [621, 20, 606, 77], [621, 35, 607, 12], [621, 39, 607, 12, "_jsxDevRuntime"], [621, 53, 607, 12], [621, 54, 607, 12, "jsxDEV"], [621, 60, 607, 12], [621, 62, 607, 13, "_Text"], [621, 67, 607, 13], [621, 68, 607, 13, "default"], [621, 75, 607, 17], [622, 14, 607, 18, "style"], [622, 19, 607, 23], [622, 21, 607, 25, "styles"], [622, 27, 607, 31], [622, 28, 607, 32, "secondaryButtonText"], [622, 47, 607, 52], [623, 14, 607, 52, "children"], [623, 22, 607, 52], [623, 24, 607, 53], [624, 12, 607, 59], [625, 14, 607, 59, "fileName"], [625, 22, 607, 59], [625, 24, 607, 59, "_jsxFileName"], [625, 36, 607, 59], [626, 14, 607, 59, "lineNumber"], [626, 24, 607, 59], [627, 14, 607, 59, "columnNumber"], [627, 26, 607, 59], [628, 12, 607, 59], [628, 19, 607, 65], [629, 10, 607, 66], [630, 12, 607, 66, "fileName"], [630, 20, 607, 66], [630, 22, 607, 66, "_jsxFileName"], [630, 34, 607, 66], [631, 12, 607, 66, "lineNumber"], [631, 22, 607, 66], [632, 12, 607, 66, "columnNumber"], [632, 24, 607, 66], [633, 10, 607, 66], [633, 17, 608, 28], [633, 18, 608, 29], [634, 8, 608, 29], [635, 10, 608, 29, "fileName"], [635, 18, 608, 29], [635, 20, 608, 29, "_jsxFileName"], [635, 32, 608, 29], [636, 10, 608, 29, "lineNumber"], [636, 20, 608, 29], [637, 10, 608, 29, "columnNumber"], [637, 22, 608, 29], [638, 8, 608, 29], [638, 15, 609, 14], [639, 6, 609, 15], [640, 8, 609, 15, "fileName"], [640, 16, 609, 15], [640, 18, 609, 15, "_jsxFileName"], [640, 30, 609, 15], [641, 8, 609, 15, "lineNumber"], [641, 18, 609, 15], [642, 8, 609, 15, "columnNumber"], [642, 20, 609, 15], [643, 6, 609, 15], [643, 13, 610, 12], [643, 14, 610, 13], [644, 4, 612, 2], [645, 4, 613, 2], [646, 4, 614, 2, "console"], [646, 11, 614, 9], [646, 12, 614, 10, "log"], [646, 15, 614, 13], [646, 16, 614, 14], [646, 55, 614, 53], [646, 56, 614, 54], [647, 4, 616, 2], [647, 24, 617, 4], [647, 28, 617, 4, "_jsxDevRuntime"], [647, 42, 617, 4], [647, 43, 617, 4, "jsxDEV"], [647, 49, 617, 4], [647, 51, 617, 5, "_View"], [647, 56, 617, 5], [647, 57, 617, 5, "default"], [647, 64, 617, 9], [648, 6, 617, 10, "style"], [648, 11, 617, 15], [648, 13, 617, 17, "styles"], [648, 19, 617, 23], [648, 20, 617, 24, "container"], [648, 29, 617, 34], [649, 6, 617, 34, "children"], [649, 14, 617, 34], [649, 30, 619, 6], [649, 34, 619, 6, "_jsxDevRuntime"], [649, 48, 619, 6], [649, 49, 619, 6, "jsxDEV"], [649, 55, 619, 6], [649, 57, 619, 7, "_View"], [649, 62, 619, 7], [649, 63, 619, 7, "default"], [649, 70, 619, 11], [650, 8, 619, 12, "style"], [650, 13, 619, 17], [650, 15, 619, 19, "styles"], [650, 21, 619, 25], [650, 22, 619, 26, "cameraContainer"], [650, 37, 619, 42], [651, 8, 619, 43, "id"], [651, 10, 619, 45], [651, 12, 619, 46], [651, 29, 619, 63], [652, 8, 619, 63, "children"], [652, 16, 619, 63], [652, 32, 620, 8], [652, 36, 620, 8, "_jsxDevRuntime"], [652, 50, 620, 8], [652, 51, 620, 8, "jsxDEV"], [652, 57, 620, 8], [652, 59, 620, 9, "_expoCamera"], [652, 70, 620, 9], [652, 71, 620, 9, "CameraView"], [652, 81, 620, 19], [653, 10, 621, 10, "ref"], [653, 13, 621, 13], [653, 15, 621, 15, "cameraRef"], [653, 24, 621, 25], [654, 10, 622, 10, "style"], [654, 15, 622, 15], [654, 17, 622, 17], [654, 18, 622, 18, "styles"], [654, 24, 622, 24], [654, 25, 622, 25, "camera"], [654, 31, 622, 31], [654, 33, 622, 33], [655, 12, 622, 35, "backgroundColor"], [655, 27, 622, 50], [655, 29, 622, 52], [656, 10, 622, 62], [656, 11, 622, 63], [656, 12, 622, 65], [657, 10, 623, 10, "facing"], [657, 16, 623, 16], [657, 18, 623, 17], [657, 24, 623, 23], [658, 10, 624, 10, "onLayout"], [658, 18, 624, 18], [658, 20, 624, 21, "e"], [658, 21, 624, 22], [658, 25, 624, 27], [659, 12, 625, 12, "console"], [659, 19, 625, 19], [659, 20, 625, 20, "log"], [659, 23, 625, 23], [659, 24, 625, 24], [659, 56, 625, 56], [659, 58, 625, 58, "e"], [659, 59, 625, 59], [659, 60, 625, 60, "nativeEvent"], [659, 71, 625, 71], [659, 72, 625, 72, "layout"], [659, 78, 625, 78], [659, 79, 625, 79], [660, 12, 626, 12, "setViewSize"], [660, 23, 626, 23], [660, 24, 626, 24], [661, 14, 626, 26, "width"], [661, 19, 626, 31], [661, 21, 626, 33, "e"], [661, 22, 626, 34], [661, 23, 626, 35, "nativeEvent"], [661, 34, 626, 46], [661, 35, 626, 47, "layout"], [661, 41, 626, 53], [661, 42, 626, 54, "width"], [661, 47, 626, 59], [662, 14, 626, 61, "height"], [662, 20, 626, 67], [662, 22, 626, 69, "e"], [662, 23, 626, 70], [662, 24, 626, 71, "nativeEvent"], [662, 35, 626, 82], [662, 36, 626, 83, "layout"], [662, 42, 626, 89], [662, 43, 626, 90, "height"], [663, 12, 626, 97], [663, 13, 626, 98], [663, 14, 626, 99], [664, 10, 627, 10], [664, 11, 627, 12], [665, 10, 628, 10, "onCameraReady"], [665, 23, 628, 23], [665, 25, 628, 25, "onCameraReady"], [665, 26, 628, 25], [665, 31, 628, 31], [666, 12, 629, 12, "console"], [666, 19, 629, 19], [666, 20, 629, 20, "log"], [666, 23, 629, 23], [666, 24, 629, 24], [666, 55, 629, 55], [666, 56, 629, 56], [667, 12, 630, 12, "setIsCameraReady"], [667, 28, 630, 28], [667, 29, 630, 29], [667, 33, 630, 33], [667, 34, 630, 34], [667, 35, 630, 35], [667, 36, 630, 36], [668, 10, 631, 10], [668, 11, 631, 12], [669, 10, 632, 10, "onMountError"], [669, 22, 632, 22], [669, 24, 632, 25, "error"], [669, 29, 632, 30], [669, 33, 632, 35], [670, 12, 633, 12, "console"], [670, 19, 633, 19], [670, 20, 633, 20, "error"], [670, 25, 633, 25], [670, 26, 633, 26], [670, 63, 633, 63], [670, 65, 633, 65, "error"], [670, 70, 633, 70], [670, 71, 633, 71], [671, 12, 634, 12, "setErrorMessage"], [671, 27, 634, 27], [671, 28, 634, 28], [671, 57, 634, 57], [671, 58, 634, 58], [672, 12, 635, 12, "setProcessingState"], [672, 30, 635, 30], [672, 31, 635, 31], [672, 38, 635, 38], [672, 39, 635, 39], [673, 10, 636, 10], [674, 8, 636, 12], [675, 10, 636, 12, "fileName"], [675, 18, 636, 12], [675, 20, 636, 12, "_jsxFileName"], [675, 32, 636, 12], [676, 10, 636, 12, "lineNumber"], [676, 20, 636, 12], [677, 10, 636, 12, "columnNumber"], [677, 22, 636, 12], [678, 8, 636, 12], [678, 15, 637, 9], [678, 16, 637, 10], [678, 18, 639, 9], [678, 19, 639, 10, "isCameraReady"], [678, 32, 639, 23], [678, 49, 640, 10], [678, 53, 640, 10, "_jsxDevRuntime"], [678, 67, 640, 10], [678, 68, 640, 10, "jsxDEV"], [678, 74, 640, 10], [678, 76, 640, 11, "_View"], [678, 81, 640, 11], [678, 82, 640, 11, "default"], [678, 89, 640, 15], [679, 10, 640, 16, "style"], [679, 15, 640, 21], [679, 17, 640, 23], [679, 18, 640, 24, "StyleSheet"], [679, 37, 640, 34], [679, 38, 640, 35, "absoluteFill"], [679, 50, 640, 47], [679, 52, 640, 49], [680, 12, 640, 51, "backgroundColor"], [680, 27, 640, 66], [680, 29, 640, 68], [680, 49, 640, 88], [681, 12, 640, 90, "justifyContent"], [681, 26, 640, 104], [681, 28, 640, 106], [681, 36, 640, 114], [682, 12, 640, 116, "alignItems"], [682, 22, 640, 126], [682, 24, 640, 128], [682, 32, 640, 136], [683, 12, 640, 138, "zIndex"], [683, 18, 640, 144], [683, 20, 640, 146], [684, 10, 640, 151], [684, 11, 640, 152], [684, 12, 640, 154], [685, 10, 640, 154, "children"], [685, 18, 640, 154], [685, 33, 641, 12], [685, 37, 641, 12, "_jsxDevRuntime"], [685, 51, 641, 12], [685, 52, 641, 12, "jsxDEV"], [685, 58, 641, 12], [685, 60, 641, 13, "_View"], [685, 65, 641, 13], [685, 66, 641, 13, "default"], [685, 73, 641, 17], [686, 12, 641, 18, "style"], [686, 17, 641, 23], [686, 19, 641, 25], [687, 14, 641, 27, "backgroundColor"], [687, 29, 641, 42], [687, 31, 641, 44], [687, 51, 641, 64], [688, 14, 641, 66, "padding"], [688, 21, 641, 73], [688, 23, 641, 75], [688, 25, 641, 77], [689, 14, 641, 79, "borderRadius"], [689, 26, 641, 91], [689, 28, 641, 93], [689, 30, 641, 95], [690, 14, 641, 97, "alignItems"], [690, 24, 641, 107], [690, 26, 641, 109], [691, 12, 641, 118], [691, 13, 641, 120], [692, 12, 641, 120, "children"], [692, 20, 641, 120], [692, 36, 642, 14], [692, 40, 642, 14, "_jsxDevRuntime"], [692, 54, 642, 14], [692, 55, 642, 14, "jsxDEV"], [692, 61, 642, 14], [692, 63, 642, 15, "_ActivityIndicator"], [692, 81, 642, 15], [692, 82, 642, 15, "default"], [692, 89, 642, 32], [693, 14, 642, 33, "size"], [693, 18, 642, 37], [693, 20, 642, 38], [693, 27, 642, 45], [694, 14, 642, 46, "color"], [694, 19, 642, 51], [694, 21, 642, 52], [694, 30, 642, 61], [695, 14, 642, 62, "style"], [695, 19, 642, 67], [695, 21, 642, 69], [696, 16, 642, 71, "marginBottom"], [696, 28, 642, 83], [696, 30, 642, 85], [697, 14, 642, 88], [698, 12, 642, 90], [699, 14, 642, 90, "fileName"], [699, 22, 642, 90], [699, 24, 642, 90, "_jsxFileName"], [699, 36, 642, 90], [700, 14, 642, 90, "lineNumber"], [700, 24, 642, 90], [701, 14, 642, 90, "columnNumber"], [701, 26, 642, 90], [702, 12, 642, 90], [702, 19, 642, 92], [702, 20, 642, 93], [702, 35, 643, 14], [702, 39, 643, 14, "_jsxDevRuntime"], [702, 53, 643, 14], [702, 54, 643, 14, "jsxDEV"], [702, 60, 643, 14], [702, 62, 643, 15, "_Text"], [702, 67, 643, 15], [702, 68, 643, 15, "default"], [702, 75, 643, 19], [703, 14, 643, 20, "style"], [703, 19, 643, 25], [703, 21, 643, 27], [704, 16, 643, 29, "color"], [704, 21, 643, 34], [704, 23, 643, 36], [704, 29, 643, 42], [705, 16, 643, 44, "fontSize"], [705, 24, 643, 52], [705, 26, 643, 54], [705, 28, 643, 56], [706, 16, 643, 58, "fontWeight"], [706, 26, 643, 68], [706, 28, 643, 70], [707, 14, 643, 76], [707, 15, 643, 78], [708, 14, 643, 78, "children"], [708, 22, 643, 78], [708, 24, 643, 79], [709, 12, 643, 101], [710, 14, 643, 101, "fileName"], [710, 22, 643, 101], [710, 24, 643, 101, "_jsxFileName"], [710, 36, 643, 101], [711, 14, 643, 101, "lineNumber"], [711, 24, 643, 101], [712, 14, 643, 101, "columnNumber"], [712, 26, 643, 101], [713, 12, 643, 101], [713, 19, 643, 107], [713, 20, 643, 108], [713, 35, 644, 14], [713, 39, 644, 14, "_jsxDevRuntime"], [713, 53, 644, 14], [713, 54, 644, 14, "jsxDEV"], [713, 60, 644, 14], [713, 62, 644, 15, "_Text"], [713, 67, 644, 15], [713, 68, 644, 15, "default"], [713, 75, 644, 19], [714, 14, 644, 20, "style"], [714, 19, 644, 25], [714, 21, 644, 27], [715, 16, 644, 29, "color"], [715, 21, 644, 34], [715, 23, 644, 36], [715, 32, 644, 45], [716, 16, 644, 47, "fontSize"], [716, 24, 644, 55], [716, 26, 644, 57], [716, 28, 644, 59], [717, 16, 644, 61, "marginTop"], [717, 25, 644, 70], [717, 27, 644, 72], [718, 14, 644, 74], [718, 15, 644, 76], [719, 14, 644, 76, "children"], [719, 22, 644, 76], [719, 24, 644, 77], [720, 12, 644, 88], [721, 14, 644, 88, "fileName"], [721, 22, 644, 88], [721, 24, 644, 88, "_jsxFileName"], [721, 36, 644, 88], [722, 14, 644, 88, "lineNumber"], [722, 24, 644, 88], [723, 14, 644, 88, "columnNumber"], [723, 26, 644, 88], [724, 12, 644, 88], [724, 19, 644, 94], [724, 20, 644, 95], [725, 10, 644, 95], [726, 12, 644, 95, "fileName"], [726, 20, 644, 95], [726, 22, 644, 95, "_jsxFileName"], [726, 34, 644, 95], [727, 12, 644, 95, "lineNumber"], [727, 22, 644, 95], [728, 12, 644, 95, "columnNumber"], [728, 24, 644, 95], [729, 10, 644, 95], [729, 17, 645, 18], [730, 8, 645, 19], [731, 10, 645, 19, "fileName"], [731, 18, 645, 19], [731, 20, 645, 19, "_jsxFileName"], [731, 32, 645, 19], [732, 10, 645, 19, "lineNumber"], [732, 20, 645, 19], [733, 10, 645, 19, "columnNumber"], [733, 22, 645, 19], [734, 8, 645, 19], [734, 15, 646, 16], [734, 16, 647, 9], [734, 18, 650, 9, "isCameraReady"], [734, 31, 650, 22], [734, 35, 650, 26, "previewBlurEnabled"], [734, 53, 650, 44], [734, 57, 650, 48, "viewSize"], [734, 65, 650, 56], [734, 66, 650, 57, "width"], [734, 71, 650, 62], [734, 74, 650, 65], [734, 75, 650, 66], [734, 92, 651, 10], [734, 96, 651, 10, "_jsxDevRuntime"], [734, 110, 651, 10], [734, 111, 651, 10, "jsxDEV"], [734, 117, 651, 10], [734, 119, 651, 10, "_jsxDevRuntime"], [734, 133, 651, 10], [734, 134, 651, 10, "Fragment"], [734, 142, 651, 10], [735, 10, 651, 10, "children"], [735, 18, 651, 10], [735, 34, 653, 12], [735, 38, 653, 12, "_jsxDevRuntime"], [735, 52, 653, 12], [735, 53, 653, 12, "jsxDEV"], [735, 59, 653, 12], [735, 61, 653, 13, "_LiveFaceCanvas"], [735, 76, 653, 13], [735, 77, 653, 13, "default"], [735, 84, 653, 27], [736, 12, 653, 28, "containerId"], [736, 23, 653, 39], [736, 25, 653, 40], [736, 42, 653, 57], [737, 12, 653, 58, "width"], [737, 17, 653, 63], [737, 19, 653, 65, "viewSize"], [737, 27, 653, 73], [737, 28, 653, 74, "width"], [737, 33, 653, 80], [738, 12, 653, 81, "height"], [738, 18, 653, 87], [738, 20, 653, 89, "viewSize"], [738, 28, 653, 97], [738, 29, 653, 98, "height"], [739, 10, 653, 105], [740, 12, 653, 105, "fileName"], [740, 20, 653, 105], [740, 22, 653, 105, "_jsxFileName"], [740, 34, 653, 105], [741, 12, 653, 105, "lineNumber"], [741, 22, 653, 105], [742, 12, 653, 105, "columnNumber"], [742, 24, 653, 105], [743, 10, 653, 105], [743, 17, 653, 107], [743, 18, 653, 108], [743, 33, 654, 12], [743, 37, 654, 12, "_jsxDevRuntime"], [743, 51, 654, 12], [743, 52, 654, 12, "jsxDEV"], [743, 58, 654, 12], [743, 60, 654, 13, "_View"], [743, 65, 654, 13], [743, 66, 654, 13, "default"], [743, 73, 654, 17], [744, 12, 654, 18, "style"], [744, 17, 654, 23], [744, 19, 654, 25], [744, 20, 654, 26, "StyleSheet"], [744, 39, 654, 36], [744, 40, 654, 37, "absoluteFill"], [744, 52, 654, 49], [744, 54, 654, 51], [745, 14, 654, 53, "pointerEvents"], [745, 27, 654, 66], [745, 29, 654, 68], [746, 12, 654, 75], [746, 13, 654, 76], [746, 14, 654, 78], [747, 12, 654, 78, "children"], [747, 20, 654, 78], [747, 36, 656, 12], [747, 40, 656, 12, "_jsxDevRuntime"], [747, 54, 656, 12], [747, 55, 656, 12, "jsxDEV"], [747, 61, 656, 12], [747, 63, 656, 13, "_expoBlur"], [747, 72, 656, 13], [747, 73, 656, 13, "BlurView"], [747, 81, 656, 21], [748, 14, 656, 22, "intensity"], [748, 23, 656, 31], [748, 25, 656, 33], [748, 27, 656, 36], [749, 14, 656, 37, "tint"], [749, 18, 656, 41], [749, 20, 656, 42], [749, 26, 656, 48], [750, 14, 656, 49, "style"], [750, 19, 656, 54], [750, 21, 656, 56], [750, 22, 656, 57, "styles"], [750, 28, 656, 63], [750, 29, 656, 64, "blurZone"], [750, 37, 656, 72], [750, 39, 656, 74], [751, 16, 657, 14, "left"], [751, 20, 657, 18], [751, 22, 657, 20], [751, 23, 657, 21], [752, 16, 658, 14, "top"], [752, 19, 658, 17], [752, 21, 658, 19, "viewSize"], [752, 29, 658, 27], [752, 30, 658, 28, "height"], [752, 36, 658, 34], [752, 39, 658, 37], [752, 42, 658, 40], [753, 16, 659, 14, "width"], [753, 21, 659, 19], [753, 23, 659, 21, "viewSize"], [753, 31, 659, 29], [753, 32, 659, 30, "width"], [753, 37, 659, 35], [754, 16, 660, 14, "height"], [754, 22, 660, 20], [754, 24, 660, 22, "viewSize"], [754, 32, 660, 30], [754, 33, 660, 31, "height"], [754, 39, 660, 37], [754, 42, 660, 40], [754, 46, 660, 44], [755, 16, 661, 14, "borderRadius"], [755, 28, 661, 26], [755, 30, 661, 28], [756, 14, 662, 12], [756, 15, 662, 13], [757, 12, 662, 15], [758, 14, 662, 15, "fileName"], [758, 22, 662, 15], [758, 24, 662, 15, "_jsxFileName"], [758, 36, 662, 15], [759, 14, 662, 15, "lineNumber"], [759, 24, 662, 15], [760, 14, 662, 15, "columnNumber"], [760, 26, 662, 15], [761, 12, 662, 15], [761, 19, 662, 17], [761, 20, 662, 18], [761, 35, 664, 12], [761, 39, 664, 12, "_jsxDevRuntime"], [761, 53, 664, 12], [761, 54, 664, 12, "jsxDEV"], [761, 60, 664, 12], [761, 62, 664, 13, "_expoBlur"], [761, 71, 664, 13], [761, 72, 664, 13, "BlurView"], [761, 80, 664, 21], [762, 14, 664, 22, "intensity"], [762, 23, 664, 31], [762, 25, 664, 33], [762, 27, 664, 36], [763, 14, 664, 37, "tint"], [763, 18, 664, 41], [763, 20, 664, 42], [763, 26, 664, 48], [764, 14, 664, 49, "style"], [764, 19, 664, 54], [764, 21, 664, 56], [764, 22, 664, 57, "styles"], [764, 28, 664, 63], [764, 29, 664, 64, "blurZone"], [764, 37, 664, 72], [764, 39, 664, 74], [765, 16, 665, 14, "left"], [765, 20, 665, 18], [765, 22, 665, 20], [765, 23, 665, 21], [766, 16, 666, 14, "top"], [766, 19, 666, 17], [766, 21, 666, 19], [766, 22, 666, 20], [767, 16, 667, 14, "width"], [767, 21, 667, 19], [767, 23, 667, 21, "viewSize"], [767, 31, 667, 29], [767, 32, 667, 30, "width"], [767, 37, 667, 35], [768, 16, 668, 14, "height"], [768, 22, 668, 20], [768, 24, 668, 22, "viewSize"], [768, 32, 668, 30], [768, 33, 668, 31, "height"], [768, 39, 668, 37], [768, 42, 668, 40], [768, 45, 668, 43], [769, 16, 669, 14, "borderRadius"], [769, 28, 669, 26], [769, 30, 669, 28], [770, 14, 670, 12], [770, 15, 670, 13], [771, 12, 670, 15], [772, 14, 670, 15, "fileName"], [772, 22, 670, 15], [772, 24, 670, 15, "_jsxFileName"], [772, 36, 670, 15], [773, 14, 670, 15, "lineNumber"], [773, 24, 670, 15], [774, 14, 670, 15, "columnNumber"], [774, 26, 670, 15], [775, 12, 670, 15], [775, 19, 670, 17], [775, 20, 670, 18], [775, 35, 672, 12], [775, 39, 672, 12, "_jsxDevRuntime"], [775, 53, 672, 12], [775, 54, 672, 12, "jsxDEV"], [775, 60, 672, 12], [775, 62, 672, 13, "_expoBlur"], [775, 71, 672, 13], [775, 72, 672, 13, "BlurView"], [775, 80, 672, 21], [776, 14, 672, 22, "intensity"], [776, 23, 672, 31], [776, 25, 672, 33], [776, 27, 672, 36], [777, 14, 672, 37, "tint"], [777, 18, 672, 41], [777, 20, 672, 42], [777, 26, 672, 48], [778, 14, 672, 49, "style"], [778, 19, 672, 54], [778, 21, 672, 56], [778, 22, 672, 57, "styles"], [778, 28, 672, 63], [778, 29, 672, 64, "blurZone"], [778, 37, 672, 72], [778, 39, 672, 74], [779, 16, 673, 14, "left"], [779, 20, 673, 18], [779, 22, 673, 20, "viewSize"], [779, 30, 673, 28], [779, 31, 673, 29, "width"], [779, 36, 673, 34], [779, 39, 673, 37], [779, 42, 673, 40], [779, 45, 673, 44, "viewSize"], [779, 53, 673, 52], [779, 54, 673, 53, "width"], [779, 59, 673, 58], [779, 62, 673, 61], [779, 66, 673, 66], [780, 16, 674, 14, "top"], [780, 19, 674, 17], [780, 21, 674, 19, "viewSize"], [780, 29, 674, 27], [780, 30, 674, 28, "height"], [780, 36, 674, 34], [780, 39, 674, 37], [780, 43, 674, 41], [780, 46, 674, 45, "viewSize"], [780, 54, 674, 53], [780, 55, 674, 54, "width"], [780, 60, 674, 59], [780, 63, 674, 62], [780, 67, 674, 67], [781, 16, 675, 14, "width"], [781, 21, 675, 19], [781, 23, 675, 21, "viewSize"], [781, 31, 675, 29], [781, 32, 675, 30, "width"], [781, 37, 675, 35], [781, 40, 675, 38], [781, 43, 675, 41], [782, 16, 676, 14, "height"], [782, 22, 676, 20], [782, 24, 676, 22, "viewSize"], [782, 32, 676, 30], [782, 33, 676, 31, "width"], [782, 38, 676, 36], [782, 41, 676, 39], [782, 44, 676, 42], [783, 16, 677, 14, "borderRadius"], [783, 28, 677, 26], [783, 30, 677, 29, "viewSize"], [783, 38, 677, 37], [783, 39, 677, 38, "width"], [783, 44, 677, 43], [783, 47, 677, 46], [783, 50, 677, 49], [783, 53, 677, 53], [784, 14, 678, 12], [784, 15, 678, 13], [785, 12, 678, 15], [786, 14, 678, 15, "fileName"], [786, 22, 678, 15], [786, 24, 678, 15, "_jsxFileName"], [786, 36, 678, 15], [787, 14, 678, 15, "lineNumber"], [787, 24, 678, 15], [788, 14, 678, 15, "columnNumber"], [788, 26, 678, 15], [789, 12, 678, 15], [789, 19, 678, 17], [789, 20, 678, 18], [789, 35, 679, 12], [789, 39, 679, 12, "_jsxDevRuntime"], [789, 53, 679, 12], [789, 54, 679, 12, "jsxDEV"], [789, 60, 679, 12], [789, 62, 679, 13, "_expoBlur"], [789, 71, 679, 13], [789, 72, 679, 13, "BlurView"], [789, 80, 679, 21], [790, 14, 679, 22, "intensity"], [790, 23, 679, 31], [790, 25, 679, 33], [790, 27, 679, 36], [791, 14, 679, 37, "tint"], [791, 18, 679, 41], [791, 20, 679, 42], [791, 26, 679, 48], [792, 14, 679, 49, "style"], [792, 19, 679, 54], [792, 21, 679, 56], [792, 22, 679, 57, "styles"], [792, 28, 679, 63], [792, 29, 679, 64, "blurZone"], [792, 37, 679, 72], [792, 39, 679, 74], [793, 16, 680, 14, "left"], [793, 20, 680, 18], [793, 22, 680, 20, "viewSize"], [793, 30, 680, 28], [793, 31, 680, 29, "width"], [793, 36, 680, 34], [793, 39, 680, 37], [793, 42, 680, 40], [793, 45, 680, 44, "viewSize"], [793, 53, 680, 52], [793, 54, 680, 53, "width"], [793, 59, 680, 58], [793, 62, 680, 61], [793, 66, 680, 66], [794, 16, 681, 14, "top"], [794, 19, 681, 17], [794, 21, 681, 19, "viewSize"], [794, 29, 681, 27], [794, 30, 681, 28, "height"], [794, 36, 681, 34], [794, 39, 681, 37], [794, 42, 681, 40], [794, 45, 681, 44, "viewSize"], [794, 53, 681, 52], [794, 54, 681, 53, "width"], [794, 59, 681, 58], [794, 62, 681, 61], [794, 66, 681, 66], [795, 16, 682, 14, "width"], [795, 21, 682, 19], [795, 23, 682, 21, "viewSize"], [795, 31, 682, 29], [795, 32, 682, 30, "width"], [795, 37, 682, 35], [795, 40, 682, 38], [795, 43, 682, 41], [796, 16, 683, 14, "height"], [796, 22, 683, 20], [796, 24, 683, 22, "viewSize"], [796, 32, 683, 30], [796, 33, 683, 31, "width"], [796, 38, 683, 36], [796, 41, 683, 39], [796, 44, 683, 42], [797, 16, 684, 14, "borderRadius"], [797, 28, 684, 26], [797, 30, 684, 29, "viewSize"], [797, 38, 684, 37], [797, 39, 684, 38, "width"], [797, 44, 684, 43], [797, 47, 684, 46], [797, 50, 684, 49], [797, 53, 684, 53], [798, 14, 685, 12], [798, 15, 685, 13], [799, 12, 685, 15], [800, 14, 685, 15, "fileName"], [800, 22, 685, 15], [800, 24, 685, 15, "_jsxFileName"], [800, 36, 685, 15], [801, 14, 685, 15, "lineNumber"], [801, 24, 685, 15], [802, 14, 685, 15, "columnNumber"], [802, 26, 685, 15], [803, 12, 685, 15], [803, 19, 685, 17], [803, 20, 685, 18], [803, 35, 686, 12], [803, 39, 686, 12, "_jsxDevRuntime"], [803, 53, 686, 12], [803, 54, 686, 12, "jsxDEV"], [803, 60, 686, 12], [803, 62, 686, 13, "_expoBlur"], [803, 71, 686, 13], [803, 72, 686, 13, "BlurView"], [803, 80, 686, 21], [804, 14, 686, 22, "intensity"], [804, 23, 686, 31], [804, 25, 686, 33], [804, 27, 686, 36], [805, 14, 686, 37, "tint"], [805, 18, 686, 41], [805, 20, 686, 42], [805, 26, 686, 48], [806, 14, 686, 49, "style"], [806, 19, 686, 54], [806, 21, 686, 56], [806, 22, 686, 57, "styles"], [806, 28, 686, 63], [806, 29, 686, 64, "blurZone"], [806, 37, 686, 72], [806, 39, 686, 74], [807, 16, 687, 14, "left"], [807, 20, 687, 18], [807, 22, 687, 20, "viewSize"], [807, 30, 687, 28], [807, 31, 687, 29, "width"], [807, 36, 687, 34], [807, 39, 687, 37], [807, 42, 687, 40], [807, 45, 687, 44, "viewSize"], [807, 53, 687, 52], [807, 54, 687, 53, "width"], [807, 59, 687, 58], [807, 62, 687, 61], [807, 66, 687, 66], [808, 16, 688, 14, "top"], [808, 19, 688, 17], [808, 21, 688, 19, "viewSize"], [808, 29, 688, 27], [808, 30, 688, 28, "height"], [808, 36, 688, 34], [808, 39, 688, 37], [808, 42, 688, 40], [808, 45, 688, 44, "viewSize"], [808, 53, 688, 52], [808, 54, 688, 53, "width"], [808, 59, 688, 58], [808, 62, 688, 61], [808, 66, 688, 66], [809, 16, 689, 14, "width"], [809, 21, 689, 19], [809, 23, 689, 21, "viewSize"], [809, 31, 689, 29], [809, 32, 689, 30, "width"], [809, 37, 689, 35], [809, 40, 689, 38], [809, 43, 689, 41], [810, 16, 690, 14, "height"], [810, 22, 690, 20], [810, 24, 690, 22, "viewSize"], [810, 32, 690, 30], [810, 33, 690, 31, "width"], [810, 38, 690, 36], [810, 41, 690, 39], [810, 44, 690, 42], [811, 16, 691, 14, "borderRadius"], [811, 28, 691, 26], [811, 30, 691, 29, "viewSize"], [811, 38, 691, 37], [811, 39, 691, 38, "width"], [811, 44, 691, 43], [811, 47, 691, 46], [811, 50, 691, 49], [811, 53, 691, 53], [812, 14, 692, 12], [812, 15, 692, 13], [813, 12, 692, 15], [814, 14, 692, 15, "fileName"], [814, 22, 692, 15], [814, 24, 692, 15, "_jsxFileName"], [814, 36, 692, 15], [815, 14, 692, 15, "lineNumber"], [815, 24, 692, 15], [816, 14, 692, 15, "columnNumber"], [816, 26, 692, 15], [817, 12, 692, 15], [817, 19, 692, 17], [817, 20, 692, 18], [817, 22, 694, 13, "__DEV__"], [817, 29, 694, 20], [817, 46, 695, 14], [817, 50, 695, 14, "_jsxDevRuntime"], [817, 64, 695, 14], [817, 65, 695, 14, "jsxDEV"], [817, 71, 695, 14], [817, 73, 695, 15, "_View"], [817, 78, 695, 15], [817, 79, 695, 15, "default"], [817, 86, 695, 19], [818, 14, 695, 20, "style"], [818, 19, 695, 25], [818, 21, 695, 27, "styles"], [818, 27, 695, 33], [818, 28, 695, 34, "previewChip"], [818, 39, 695, 46], [819, 14, 695, 46, "children"], [819, 22, 695, 46], [819, 37, 696, 16], [819, 41, 696, 16, "_jsxDevRuntime"], [819, 55, 696, 16], [819, 56, 696, 16, "jsxDEV"], [819, 62, 696, 16], [819, 64, 696, 17, "_Text"], [819, 69, 696, 17], [819, 70, 696, 17, "default"], [819, 77, 696, 21], [820, 16, 696, 22, "style"], [820, 21, 696, 27], [820, 23, 696, 29, "styles"], [820, 29, 696, 35], [820, 30, 696, 36, "previewChipText"], [820, 45, 696, 52], [821, 16, 696, 52, "children"], [821, 24, 696, 52], [821, 26, 696, 53], [822, 14, 696, 73], [823, 16, 696, 73, "fileName"], [823, 24, 696, 73], [823, 26, 696, 73, "_jsxFileName"], [823, 38, 696, 73], [824, 16, 696, 73, "lineNumber"], [824, 26, 696, 73], [825, 16, 696, 73, "columnNumber"], [825, 28, 696, 73], [826, 14, 696, 73], [826, 21, 696, 79], [827, 12, 696, 80], [828, 14, 696, 80, "fileName"], [828, 22, 696, 80], [828, 24, 696, 80, "_jsxFileName"], [828, 36, 696, 80], [829, 14, 696, 80, "lineNumber"], [829, 24, 696, 80], [830, 14, 696, 80, "columnNumber"], [830, 26, 696, 80], [831, 12, 696, 80], [831, 19, 697, 20], [831, 20, 698, 13], [832, 10, 698, 13], [833, 12, 698, 13, "fileName"], [833, 20, 698, 13], [833, 22, 698, 13, "_jsxFileName"], [833, 34, 698, 13], [834, 12, 698, 13, "lineNumber"], [834, 22, 698, 13], [835, 12, 698, 13, "columnNumber"], [835, 24, 698, 13], [836, 10, 698, 13], [836, 17, 699, 18], [836, 18, 699, 19], [837, 8, 699, 19], [837, 23, 700, 12], [837, 24, 701, 9], [837, 26, 703, 9, "isCameraReady"], [837, 39, 703, 22], [837, 56, 704, 10], [837, 60, 704, 10, "_jsxDevRuntime"], [837, 74, 704, 10], [837, 75, 704, 10, "jsxDEV"], [837, 81, 704, 10], [837, 83, 704, 10, "_jsxDevRuntime"], [837, 97, 704, 10], [837, 98, 704, 10, "Fragment"], [837, 106, 704, 10], [838, 10, 704, 10, "children"], [838, 18, 704, 10], [838, 34, 706, 12], [838, 38, 706, 12, "_jsxDevRuntime"], [838, 52, 706, 12], [838, 53, 706, 12, "jsxDEV"], [838, 59, 706, 12], [838, 61, 706, 13, "_View"], [838, 66, 706, 13], [838, 67, 706, 13, "default"], [838, 74, 706, 17], [839, 12, 706, 18, "style"], [839, 17, 706, 23], [839, 19, 706, 25, "styles"], [839, 25, 706, 31], [839, 26, 706, 32, "headerOverlay"], [839, 39, 706, 46], [840, 12, 706, 46, "children"], [840, 20, 706, 46], [840, 35, 707, 14], [840, 39, 707, 14, "_jsxDevRuntime"], [840, 53, 707, 14], [840, 54, 707, 14, "jsxDEV"], [840, 60, 707, 14], [840, 62, 707, 15, "_View"], [840, 67, 707, 15], [840, 68, 707, 15, "default"], [840, 75, 707, 19], [841, 14, 707, 20, "style"], [841, 19, 707, 25], [841, 21, 707, 27, "styles"], [841, 27, 707, 33], [841, 28, 707, 34, "headerContent"], [841, 41, 707, 48], [842, 14, 707, 48, "children"], [842, 22, 707, 48], [842, 38, 708, 16], [842, 42, 708, 16, "_jsxDevRuntime"], [842, 56, 708, 16], [842, 57, 708, 16, "jsxDEV"], [842, 63, 708, 16], [842, 65, 708, 17, "_View"], [842, 70, 708, 17], [842, 71, 708, 17, "default"], [842, 78, 708, 21], [843, 16, 708, 22, "style"], [843, 21, 708, 27], [843, 23, 708, 29, "styles"], [843, 29, 708, 35], [843, 30, 708, 36, "headerLeft"], [843, 40, 708, 47], [844, 16, 708, 47, "children"], [844, 24, 708, 47], [844, 40, 709, 18], [844, 44, 709, 18, "_jsxDevRuntime"], [844, 58, 709, 18], [844, 59, 709, 18, "jsxDEV"], [844, 65, 709, 18], [844, 67, 709, 19, "_Text"], [844, 72, 709, 19], [844, 73, 709, 19, "default"], [844, 80, 709, 23], [845, 18, 709, 24, "style"], [845, 23, 709, 29], [845, 25, 709, 31, "styles"], [845, 31, 709, 37], [845, 32, 709, 38, "headerTitle"], [845, 43, 709, 50], [846, 18, 709, 50, "children"], [846, 26, 709, 50], [846, 28, 709, 51], [847, 16, 709, 62], [848, 18, 709, 62, "fileName"], [848, 26, 709, 62], [848, 28, 709, 62, "_jsxFileName"], [848, 40, 709, 62], [849, 18, 709, 62, "lineNumber"], [849, 28, 709, 62], [850, 18, 709, 62, "columnNumber"], [850, 30, 709, 62], [851, 16, 709, 62], [851, 23, 709, 68], [851, 24, 709, 69], [851, 39, 710, 18], [851, 43, 710, 18, "_jsxDevRuntime"], [851, 57, 710, 18], [851, 58, 710, 18, "jsxDEV"], [851, 64, 710, 18], [851, 66, 710, 19, "_View"], [851, 71, 710, 19], [851, 72, 710, 19, "default"], [851, 79, 710, 23], [852, 18, 710, 24, "style"], [852, 23, 710, 29], [852, 25, 710, 31, "styles"], [852, 31, 710, 37], [852, 32, 710, 38, "subtitleRow"], [852, 43, 710, 50], [853, 18, 710, 50, "children"], [853, 26, 710, 50], [853, 42, 711, 20], [853, 46, 711, 20, "_jsxDevRuntime"], [853, 60, 711, 20], [853, 61, 711, 20, "jsxDEV"], [853, 67, 711, 20], [853, 69, 711, 21, "_Text"], [853, 74, 711, 21], [853, 75, 711, 21, "default"], [853, 82, 711, 25], [854, 20, 711, 26, "style"], [854, 25, 711, 31], [854, 27, 711, 33, "styles"], [854, 33, 711, 39], [854, 34, 711, 40, "webIcon"], [854, 41, 711, 48], [855, 20, 711, 48, "children"], [855, 28, 711, 48], [855, 30, 711, 49], [856, 18, 711, 51], [857, 20, 711, 51, "fileName"], [857, 28, 711, 51], [857, 30, 711, 51, "_jsxFileName"], [857, 42, 711, 51], [858, 20, 711, 51, "lineNumber"], [858, 30, 711, 51], [859, 20, 711, 51, "columnNumber"], [859, 32, 711, 51], [860, 18, 711, 51], [860, 25, 711, 57], [860, 26, 711, 58], [860, 41, 712, 20], [860, 45, 712, 20, "_jsxDevRuntime"], [860, 59, 712, 20], [860, 60, 712, 20, "jsxDEV"], [860, 66, 712, 20], [860, 68, 712, 21, "_Text"], [860, 73, 712, 21], [860, 74, 712, 21, "default"], [860, 81, 712, 25], [861, 20, 712, 26, "style"], [861, 25, 712, 31], [861, 27, 712, 33, "styles"], [861, 33, 712, 39], [861, 34, 712, 40, "headerSubtitle"], [861, 48, 712, 55], [862, 20, 712, 55, "children"], [862, 28, 712, 55], [862, 30, 712, 56], [863, 18, 712, 71], [864, 20, 712, 71, "fileName"], [864, 28, 712, 71], [864, 30, 712, 71, "_jsxFileName"], [864, 42, 712, 71], [865, 20, 712, 71, "lineNumber"], [865, 30, 712, 71], [866, 20, 712, 71, "columnNumber"], [866, 32, 712, 71], [867, 18, 712, 71], [867, 25, 712, 77], [867, 26, 712, 78], [868, 16, 712, 78], [869, 18, 712, 78, "fileName"], [869, 26, 712, 78], [869, 28, 712, 78, "_jsxFileName"], [869, 40, 712, 78], [870, 18, 712, 78, "lineNumber"], [870, 28, 712, 78], [871, 18, 712, 78, "columnNumber"], [871, 30, 712, 78], [872, 16, 712, 78], [872, 23, 713, 24], [872, 24, 713, 25], [872, 26, 714, 19, "challengeCode"], [872, 39, 714, 32], [872, 56, 715, 20], [872, 60, 715, 20, "_jsxDevRuntime"], [872, 74, 715, 20], [872, 75, 715, 20, "jsxDEV"], [872, 81, 715, 20], [872, 83, 715, 21, "_View"], [872, 88, 715, 21], [872, 89, 715, 21, "default"], [872, 96, 715, 25], [873, 18, 715, 26, "style"], [873, 23, 715, 31], [873, 25, 715, 33, "styles"], [873, 31, 715, 39], [873, 32, 715, 40, "challengeRow"], [873, 44, 715, 53], [874, 18, 715, 53, "children"], [874, 26, 715, 53], [874, 42, 716, 22], [874, 46, 716, 22, "_jsxDevRuntime"], [874, 60, 716, 22], [874, 61, 716, 22, "jsxDEV"], [874, 67, 716, 22], [874, 69, 716, 23, "_lucideReactNative"], [874, 87, 716, 23], [874, 88, 716, 23, "Shield"], [874, 94, 716, 29], [875, 20, 716, 30, "size"], [875, 24, 716, 34], [875, 26, 716, 36], [875, 28, 716, 39], [876, 20, 716, 40, "color"], [876, 25, 716, 45], [876, 27, 716, 46], [877, 18, 716, 52], [878, 20, 716, 52, "fileName"], [878, 28, 716, 52], [878, 30, 716, 52, "_jsxFileName"], [878, 42, 716, 52], [879, 20, 716, 52, "lineNumber"], [879, 30, 716, 52], [880, 20, 716, 52, "columnNumber"], [880, 32, 716, 52], [881, 18, 716, 52], [881, 25, 716, 54], [881, 26, 716, 55], [881, 41, 717, 22], [881, 45, 717, 22, "_jsxDevRuntime"], [881, 59, 717, 22], [881, 60, 717, 22, "jsxDEV"], [881, 66, 717, 22], [881, 68, 717, 23, "_Text"], [881, 73, 717, 23], [881, 74, 717, 23, "default"], [881, 81, 717, 27], [882, 20, 717, 28, "style"], [882, 25, 717, 33], [882, 27, 717, 35, "styles"], [882, 33, 717, 41], [882, 34, 717, 42, "challengeCode"], [882, 47, 717, 56], [883, 20, 717, 56, "children"], [883, 28, 717, 56], [883, 30, 717, 58, "challengeCode"], [884, 18, 717, 71], [885, 20, 717, 71, "fileName"], [885, 28, 717, 71], [885, 30, 717, 71, "_jsxFileName"], [885, 42, 717, 71], [886, 20, 717, 71, "lineNumber"], [886, 30, 717, 71], [887, 20, 717, 71, "columnNumber"], [887, 32, 717, 71], [888, 18, 717, 71], [888, 25, 717, 78], [888, 26, 717, 79], [889, 16, 717, 79], [890, 18, 717, 79, "fileName"], [890, 26, 717, 79], [890, 28, 717, 79, "_jsxFileName"], [890, 40, 717, 79], [891, 18, 717, 79, "lineNumber"], [891, 28, 717, 79], [892, 18, 717, 79, "columnNumber"], [892, 30, 717, 79], [893, 16, 717, 79], [893, 23, 718, 26], [893, 24, 719, 19], [894, 14, 719, 19], [895, 16, 719, 19, "fileName"], [895, 24, 719, 19], [895, 26, 719, 19, "_jsxFileName"], [895, 38, 719, 19], [896, 16, 719, 19, "lineNumber"], [896, 26, 719, 19], [897, 16, 719, 19, "columnNumber"], [897, 28, 719, 19], [898, 14, 719, 19], [898, 21, 720, 22], [898, 22, 720, 23], [898, 37, 721, 16], [898, 41, 721, 16, "_jsxDevRuntime"], [898, 55, 721, 16], [898, 56, 721, 16, "jsxDEV"], [898, 62, 721, 16], [898, 64, 721, 17, "_TouchableOpacity"], [898, 81, 721, 17], [898, 82, 721, 17, "default"], [898, 89, 721, 33], [899, 16, 721, 34, "onPress"], [899, 23, 721, 41], [899, 25, 721, 43, "onCancel"], [899, 33, 721, 52], [900, 16, 721, 53, "style"], [900, 21, 721, 58], [900, 23, 721, 60, "styles"], [900, 29, 721, 66], [900, 30, 721, 67, "closeButton"], [900, 41, 721, 79], [901, 16, 721, 79, "children"], [901, 24, 721, 79], [901, 39, 722, 18], [901, 43, 722, 18, "_jsxDevRuntime"], [901, 57, 722, 18], [901, 58, 722, 18, "jsxDEV"], [901, 64, 722, 18], [901, 66, 722, 19, "_lucideReactNative"], [901, 84, 722, 19], [901, 85, 722, 19, "X"], [901, 86, 722, 20], [902, 18, 722, 21, "size"], [902, 22, 722, 25], [902, 24, 722, 27], [902, 26, 722, 30], [903, 18, 722, 31, "color"], [903, 23, 722, 36], [903, 25, 722, 37], [904, 16, 722, 43], [905, 18, 722, 43, "fileName"], [905, 26, 722, 43], [905, 28, 722, 43, "_jsxFileName"], [905, 40, 722, 43], [906, 18, 722, 43, "lineNumber"], [906, 28, 722, 43], [907, 18, 722, 43, "columnNumber"], [907, 30, 722, 43], [908, 16, 722, 43], [908, 23, 722, 45], [909, 14, 722, 46], [910, 16, 722, 46, "fileName"], [910, 24, 722, 46], [910, 26, 722, 46, "_jsxFileName"], [910, 38, 722, 46], [911, 16, 722, 46, "lineNumber"], [911, 26, 722, 46], [912, 16, 722, 46, "columnNumber"], [912, 28, 722, 46], [913, 14, 722, 46], [913, 21, 723, 34], [913, 22, 723, 35], [914, 12, 723, 35], [915, 14, 723, 35, "fileName"], [915, 22, 723, 35], [915, 24, 723, 35, "_jsxFileName"], [915, 36, 723, 35], [916, 14, 723, 35, "lineNumber"], [916, 24, 723, 35], [917, 14, 723, 35, "columnNumber"], [917, 26, 723, 35], [918, 12, 723, 35], [918, 19, 724, 20], [919, 10, 724, 21], [920, 12, 724, 21, "fileName"], [920, 20, 724, 21], [920, 22, 724, 21, "_jsxFileName"], [920, 34, 724, 21], [921, 12, 724, 21, "lineNumber"], [921, 22, 724, 21], [922, 12, 724, 21, "columnNumber"], [922, 24, 724, 21], [923, 10, 724, 21], [923, 17, 725, 18], [923, 18, 725, 19], [923, 33, 727, 12], [923, 37, 727, 12, "_jsxDevRuntime"], [923, 51, 727, 12], [923, 52, 727, 12, "jsxDEV"], [923, 58, 727, 12], [923, 60, 727, 13, "_View"], [923, 65, 727, 13], [923, 66, 727, 13, "default"], [923, 73, 727, 17], [924, 12, 727, 18, "style"], [924, 17, 727, 23], [924, 19, 727, 25, "styles"], [924, 25, 727, 31], [924, 26, 727, 32, "privacyNotice"], [924, 39, 727, 46], [925, 12, 727, 46, "children"], [925, 20, 727, 46], [925, 36, 728, 14], [925, 40, 728, 14, "_jsxDevRuntime"], [925, 54, 728, 14], [925, 55, 728, 14, "jsxDEV"], [925, 61, 728, 14], [925, 63, 728, 15, "_lucideReactNative"], [925, 81, 728, 15], [925, 82, 728, 15, "Shield"], [925, 88, 728, 21], [926, 14, 728, 22, "size"], [926, 18, 728, 26], [926, 20, 728, 28], [926, 22, 728, 31], [927, 14, 728, 32, "color"], [927, 19, 728, 37], [927, 21, 728, 38], [928, 12, 728, 47], [929, 14, 728, 47, "fileName"], [929, 22, 728, 47], [929, 24, 728, 47, "_jsxFileName"], [929, 36, 728, 47], [930, 14, 728, 47, "lineNumber"], [930, 24, 728, 47], [931, 14, 728, 47, "columnNumber"], [931, 26, 728, 47], [932, 12, 728, 47], [932, 19, 728, 49], [932, 20, 728, 50], [932, 35, 729, 14], [932, 39, 729, 14, "_jsxDevRuntime"], [932, 53, 729, 14], [932, 54, 729, 14, "jsxDEV"], [932, 60, 729, 14], [932, 62, 729, 15, "_Text"], [932, 67, 729, 15], [932, 68, 729, 15, "default"], [932, 75, 729, 19], [933, 14, 729, 20, "style"], [933, 19, 729, 25], [933, 21, 729, 27, "styles"], [933, 27, 729, 33], [933, 28, 729, 34, "privacyText"], [933, 39, 729, 46], [934, 14, 729, 46, "children"], [934, 22, 729, 46], [934, 24, 729, 47], [935, 12, 731, 14], [936, 14, 731, 14, "fileName"], [936, 22, 731, 14], [936, 24, 731, 14, "_jsxFileName"], [936, 36, 731, 14], [937, 14, 731, 14, "lineNumber"], [937, 24, 731, 14], [938, 14, 731, 14, "columnNumber"], [938, 26, 731, 14], [939, 12, 731, 14], [939, 19, 731, 20], [939, 20, 731, 21], [940, 10, 731, 21], [941, 12, 731, 21, "fileName"], [941, 20, 731, 21], [941, 22, 731, 21, "_jsxFileName"], [941, 34, 731, 21], [942, 12, 731, 21, "lineNumber"], [942, 22, 731, 21], [943, 12, 731, 21, "columnNumber"], [943, 24, 731, 21], [944, 10, 731, 21], [944, 17, 732, 18], [944, 18, 732, 19], [944, 33, 734, 12], [944, 37, 734, 12, "_jsxDevRuntime"], [944, 51, 734, 12], [944, 52, 734, 12, "jsxDEV"], [944, 58, 734, 12], [944, 60, 734, 13, "_View"], [944, 65, 734, 13], [944, 66, 734, 13, "default"], [944, 73, 734, 17], [945, 12, 734, 18, "style"], [945, 17, 734, 23], [945, 19, 734, 25, "styles"], [945, 25, 734, 31], [945, 26, 734, 32, "footer<PERSON><PERSON><PERSON>"], [945, 39, 734, 46], [946, 12, 734, 46, "children"], [946, 20, 734, 46], [946, 36, 735, 14], [946, 40, 735, 14, "_jsxDevRuntime"], [946, 54, 735, 14], [946, 55, 735, 14, "jsxDEV"], [946, 61, 735, 14], [946, 63, 735, 15, "_Text"], [946, 68, 735, 15], [946, 69, 735, 15, "default"], [946, 76, 735, 19], [947, 14, 735, 20, "style"], [947, 19, 735, 25], [947, 21, 735, 27, "styles"], [947, 27, 735, 33], [947, 28, 735, 34, "instruction"], [947, 39, 735, 46], [948, 14, 735, 46, "children"], [948, 22, 735, 46], [948, 24, 735, 47], [949, 12, 737, 14], [950, 14, 737, 14, "fileName"], [950, 22, 737, 14], [950, 24, 737, 14, "_jsxFileName"], [950, 36, 737, 14], [951, 14, 737, 14, "lineNumber"], [951, 24, 737, 14], [952, 14, 737, 14, "columnNumber"], [952, 26, 737, 14], [953, 12, 737, 14], [953, 19, 737, 20], [953, 20, 737, 21], [953, 35, 739, 14], [953, 39, 739, 14, "_jsxDevRuntime"], [953, 53, 739, 14], [953, 54, 739, 14, "jsxDEV"], [953, 60, 739, 14], [953, 62, 739, 15, "_TouchableOpacity"], [953, 79, 739, 15], [953, 80, 739, 15, "default"], [953, 87, 739, 31], [954, 14, 740, 16, "onPress"], [954, 21, 740, 23], [954, 23, 740, 25, "capturePhoto"], [954, 35, 740, 38], [955, 14, 741, 16, "disabled"], [955, 22, 741, 24], [955, 24, 741, 26, "processingState"], [955, 39, 741, 41], [955, 44, 741, 46], [955, 50, 741, 52], [955, 54, 741, 56], [955, 55, 741, 57, "isCameraReady"], [955, 68, 741, 71], [956, 14, 742, 16, "style"], [956, 19, 742, 21], [956, 21, 742, 23], [956, 22, 743, 18, "styles"], [956, 28, 743, 24], [956, 29, 743, 25, "shutterButton"], [956, 42, 743, 38], [956, 44, 744, 18, "processingState"], [956, 59, 744, 33], [956, 64, 744, 38], [956, 70, 744, 44], [956, 74, 744, 48, "styles"], [956, 80, 744, 54], [956, 81, 744, 55, "shutterButtonDisabled"], [956, 102, 744, 76], [956, 103, 745, 18], [957, 14, 745, 18, "children"], [957, 22, 745, 18], [957, 24, 747, 17, "processingState"], [957, 39, 747, 32], [957, 44, 747, 37], [957, 50, 747, 43], [957, 66, 748, 18], [957, 70, 748, 18, "_jsxDevRuntime"], [957, 84, 748, 18], [957, 85, 748, 18, "jsxDEV"], [957, 91, 748, 18], [957, 93, 748, 19, "_View"], [957, 98, 748, 19], [957, 99, 748, 19, "default"], [957, 106, 748, 23], [958, 16, 748, 24, "style"], [958, 21, 748, 29], [958, 23, 748, 31, "styles"], [958, 29, 748, 37], [958, 30, 748, 38, "shutterInner"], [959, 14, 748, 51], [960, 16, 748, 51, "fileName"], [960, 24, 748, 51], [960, 26, 748, 51, "_jsxFileName"], [960, 38, 748, 51], [961, 16, 748, 51, "lineNumber"], [961, 26, 748, 51], [962, 16, 748, 51, "columnNumber"], [962, 28, 748, 51], [963, 14, 748, 51], [963, 21, 748, 53], [963, 22, 748, 54], [963, 38, 750, 18], [963, 42, 750, 18, "_jsxDevRuntime"], [963, 56, 750, 18], [963, 57, 750, 18, "jsxDEV"], [963, 63, 750, 18], [963, 65, 750, 19, "_ActivityIndicator"], [963, 83, 750, 19], [963, 84, 750, 19, "default"], [963, 91, 750, 36], [964, 16, 750, 37, "size"], [964, 20, 750, 41], [964, 22, 750, 42], [964, 29, 750, 49], [965, 16, 750, 50, "color"], [965, 21, 750, 55], [965, 23, 750, 56], [966, 14, 750, 65], [967, 16, 750, 65, "fileName"], [967, 24, 750, 65], [967, 26, 750, 65, "_jsxFileName"], [967, 38, 750, 65], [968, 16, 750, 65, "lineNumber"], [968, 26, 750, 65], [969, 16, 750, 65, "columnNumber"], [969, 28, 750, 65], [970, 14, 750, 65], [970, 21, 750, 67], [971, 12, 751, 17], [972, 14, 751, 17, "fileName"], [972, 22, 751, 17], [972, 24, 751, 17, "_jsxFileName"], [972, 36, 751, 17], [973, 14, 751, 17, "lineNumber"], [973, 24, 751, 17], [974, 14, 751, 17, "columnNumber"], [974, 26, 751, 17], [975, 12, 751, 17], [975, 19, 752, 32], [975, 20, 752, 33], [975, 35, 753, 14], [975, 39, 753, 14, "_jsxDevRuntime"], [975, 53, 753, 14], [975, 54, 753, 14, "jsxDEV"], [975, 60, 753, 14], [975, 62, 753, 15, "_Text"], [975, 67, 753, 15], [975, 68, 753, 15, "default"], [975, 75, 753, 19], [976, 14, 753, 20, "style"], [976, 19, 753, 25], [976, 21, 753, 27, "styles"], [976, 27, 753, 33], [976, 28, 753, 34, "privacyNote"], [976, 39, 753, 46], [977, 14, 753, 46, "children"], [977, 22, 753, 46], [977, 24, 753, 47], [978, 12, 755, 14], [979, 14, 755, 14, "fileName"], [979, 22, 755, 14], [979, 24, 755, 14, "_jsxFileName"], [979, 36, 755, 14], [980, 14, 755, 14, "lineNumber"], [980, 24, 755, 14], [981, 14, 755, 14, "columnNumber"], [981, 26, 755, 14], [982, 12, 755, 14], [982, 19, 755, 20], [982, 20, 755, 21], [983, 10, 755, 21], [984, 12, 755, 21, "fileName"], [984, 20, 755, 21], [984, 22, 755, 21, "_jsxFileName"], [984, 34, 755, 21], [985, 12, 755, 21, "lineNumber"], [985, 22, 755, 21], [986, 12, 755, 21, "columnNumber"], [986, 24, 755, 21], [987, 10, 755, 21], [987, 17, 756, 18], [987, 18, 756, 19], [988, 8, 756, 19], [988, 23, 757, 12], [988, 24, 758, 9], [989, 6, 758, 9], [990, 8, 758, 9, "fileName"], [990, 16, 758, 9], [990, 18, 758, 9, "_jsxFileName"], [990, 30, 758, 9], [991, 8, 758, 9, "lineNumber"], [991, 18, 758, 9], [992, 8, 758, 9, "columnNumber"], [992, 20, 758, 9], [993, 6, 758, 9], [993, 13, 759, 12], [993, 14, 759, 13], [993, 29, 761, 6], [993, 33, 761, 6, "_jsxDevRuntime"], [993, 47, 761, 6], [993, 48, 761, 6, "jsxDEV"], [993, 54, 761, 6], [993, 56, 761, 7, "_Modal"], [993, 62, 761, 7], [993, 63, 761, 7, "default"], [993, 70, 761, 12], [994, 8, 762, 8, "visible"], [994, 15, 762, 15], [994, 17, 762, 17, "processingState"], [994, 32, 762, 32], [994, 37, 762, 37], [994, 43, 762, 43], [994, 47, 762, 47, "processingState"], [994, 62, 762, 62], [994, 67, 762, 67], [994, 74, 762, 75], [995, 8, 763, 8, "transparent"], [995, 19, 763, 19], [996, 8, 764, 8, "animationType"], [996, 21, 764, 21], [996, 23, 764, 22], [996, 29, 764, 28], [997, 8, 764, 28, "children"], [997, 16, 764, 28], [997, 31, 766, 8], [997, 35, 766, 8, "_jsxDevRuntime"], [997, 49, 766, 8], [997, 50, 766, 8, "jsxDEV"], [997, 56, 766, 8], [997, 58, 766, 9, "_View"], [997, 63, 766, 9], [997, 64, 766, 9, "default"], [997, 71, 766, 13], [998, 10, 766, 14, "style"], [998, 15, 766, 19], [998, 17, 766, 21, "styles"], [998, 23, 766, 27], [998, 24, 766, 28, "processingModal"], [998, 39, 766, 44], [999, 10, 766, 44, "children"], [999, 18, 766, 44], [999, 33, 767, 10], [999, 37, 767, 10, "_jsxDevRuntime"], [999, 51, 767, 10], [999, 52, 767, 10, "jsxDEV"], [999, 58, 767, 10], [999, 60, 767, 11, "_View"], [999, 65, 767, 11], [999, 66, 767, 11, "default"], [999, 73, 767, 15], [1000, 12, 767, 16, "style"], [1000, 17, 767, 21], [1000, 19, 767, 23, "styles"], [1000, 25, 767, 29], [1000, 26, 767, 30, "processingContent"], [1000, 43, 767, 48], [1001, 12, 767, 48, "children"], [1001, 20, 767, 48], [1001, 36, 768, 12], [1001, 40, 768, 12, "_jsxDevRuntime"], [1001, 54, 768, 12], [1001, 55, 768, 12, "jsxDEV"], [1001, 61, 768, 12], [1001, 63, 768, 13, "_ActivityIndicator"], [1001, 81, 768, 13], [1001, 82, 768, 13, "default"], [1001, 89, 768, 30], [1002, 14, 768, 31, "size"], [1002, 18, 768, 35], [1002, 20, 768, 36], [1002, 27, 768, 43], [1003, 14, 768, 44, "color"], [1003, 19, 768, 49], [1003, 21, 768, 50], [1004, 12, 768, 59], [1005, 14, 768, 59, "fileName"], [1005, 22, 768, 59], [1005, 24, 768, 59, "_jsxFileName"], [1005, 36, 768, 59], [1006, 14, 768, 59, "lineNumber"], [1006, 24, 768, 59], [1007, 14, 768, 59, "columnNumber"], [1007, 26, 768, 59], [1008, 12, 768, 59], [1008, 19, 768, 61], [1008, 20, 768, 62], [1008, 35, 770, 12], [1008, 39, 770, 12, "_jsxDevRuntime"], [1008, 53, 770, 12], [1008, 54, 770, 12, "jsxDEV"], [1008, 60, 770, 12], [1008, 62, 770, 13, "_Text"], [1008, 67, 770, 13], [1008, 68, 770, 13, "default"], [1008, 75, 770, 17], [1009, 14, 770, 18, "style"], [1009, 19, 770, 23], [1009, 21, 770, 25, "styles"], [1009, 27, 770, 31], [1009, 28, 770, 32, "processingTitle"], [1009, 43, 770, 48], [1010, 14, 770, 48, "children"], [1010, 22, 770, 48], [1010, 25, 771, 15, "processingState"], [1010, 40, 771, 30], [1010, 45, 771, 35], [1010, 56, 771, 46], [1010, 60, 771, 50], [1010, 80, 771, 70], [1010, 82, 772, 15, "processingState"], [1010, 97, 772, 30], [1010, 102, 772, 35], [1010, 113, 772, 46], [1010, 117, 772, 50], [1010, 146, 772, 79], [1010, 148, 773, 15, "processingState"], [1010, 163, 773, 30], [1010, 168, 773, 35], [1010, 180, 773, 47], [1010, 184, 773, 51], [1010, 216, 773, 83], [1010, 218, 774, 15, "processingState"], [1010, 233, 774, 30], [1010, 238, 774, 35], [1010, 249, 774, 46], [1010, 253, 774, 50], [1010, 275, 774, 72], [1011, 12, 774, 72], [1012, 14, 774, 72, "fileName"], [1012, 22, 774, 72], [1012, 24, 774, 72, "_jsxFileName"], [1012, 36, 774, 72], [1013, 14, 774, 72, "lineNumber"], [1013, 24, 774, 72], [1014, 14, 774, 72, "columnNumber"], [1014, 26, 774, 72], [1015, 12, 774, 72], [1015, 19, 775, 18], [1015, 20, 775, 19], [1015, 35, 776, 12], [1015, 39, 776, 12, "_jsxDevRuntime"], [1015, 53, 776, 12], [1015, 54, 776, 12, "jsxDEV"], [1015, 60, 776, 12], [1015, 62, 776, 13, "_View"], [1015, 67, 776, 13], [1015, 68, 776, 13, "default"], [1015, 75, 776, 17], [1016, 14, 776, 18, "style"], [1016, 19, 776, 23], [1016, 21, 776, 25, "styles"], [1016, 27, 776, 31], [1016, 28, 776, 32, "progressBar"], [1016, 39, 776, 44], [1017, 14, 776, 44, "children"], [1017, 22, 776, 44], [1017, 37, 777, 14], [1017, 41, 777, 14, "_jsxDevRuntime"], [1017, 55, 777, 14], [1017, 56, 777, 14, "jsxDEV"], [1017, 62, 777, 14], [1017, 64, 777, 15, "_View"], [1017, 69, 777, 15], [1017, 70, 777, 15, "default"], [1017, 77, 777, 19], [1018, 16, 778, 16, "style"], [1018, 21, 778, 21], [1018, 23, 778, 23], [1018, 24, 779, 18, "styles"], [1018, 30, 779, 24], [1018, 31, 779, 25, "progressFill"], [1018, 43, 779, 37], [1018, 45, 780, 18], [1019, 18, 780, 20, "width"], [1019, 23, 780, 25], [1019, 25, 780, 27], [1019, 28, 780, 30, "processingProgress"], [1019, 46, 780, 48], [1020, 16, 780, 52], [1020, 17, 780, 53], [1021, 14, 781, 18], [1022, 16, 781, 18, "fileName"], [1022, 24, 781, 18], [1022, 26, 781, 18, "_jsxFileName"], [1022, 38, 781, 18], [1023, 16, 781, 18, "lineNumber"], [1023, 26, 781, 18], [1024, 16, 781, 18, "columnNumber"], [1024, 28, 781, 18], [1025, 14, 781, 18], [1025, 21, 782, 15], [1026, 12, 782, 16], [1027, 14, 782, 16, "fileName"], [1027, 22, 782, 16], [1027, 24, 782, 16, "_jsxFileName"], [1027, 36, 782, 16], [1028, 14, 782, 16, "lineNumber"], [1028, 24, 782, 16], [1029, 14, 782, 16, "columnNumber"], [1029, 26, 782, 16], [1030, 12, 782, 16], [1030, 19, 783, 18], [1030, 20, 783, 19], [1030, 35, 784, 12], [1030, 39, 784, 12, "_jsxDevRuntime"], [1030, 53, 784, 12], [1030, 54, 784, 12, "jsxDEV"], [1030, 60, 784, 12], [1030, 62, 784, 13, "_Text"], [1030, 67, 784, 13], [1030, 68, 784, 13, "default"], [1030, 75, 784, 17], [1031, 14, 784, 18, "style"], [1031, 19, 784, 23], [1031, 21, 784, 25, "styles"], [1031, 27, 784, 31], [1031, 28, 784, 32, "processingDescription"], [1031, 49, 784, 54], [1032, 14, 784, 54, "children"], [1032, 22, 784, 54], [1032, 25, 785, 15, "processingState"], [1032, 40, 785, 30], [1032, 45, 785, 35], [1032, 56, 785, 46], [1032, 60, 785, 50], [1032, 89, 785, 79], [1032, 91, 786, 15, "processingState"], [1032, 106, 786, 30], [1032, 111, 786, 35], [1032, 122, 786, 46], [1032, 126, 786, 50], [1032, 164, 786, 88], [1032, 166, 787, 15, "processingState"], [1032, 181, 787, 30], [1032, 186, 787, 35], [1032, 198, 787, 47], [1032, 202, 787, 51], [1032, 247, 787, 96], [1032, 249, 788, 15, "processingState"], [1032, 264, 788, 30], [1032, 269, 788, 35], [1032, 280, 788, 46], [1032, 284, 788, 50], [1032, 325, 788, 91], [1033, 12, 788, 91], [1034, 14, 788, 91, "fileName"], [1034, 22, 788, 91], [1034, 24, 788, 91, "_jsxFileName"], [1034, 36, 788, 91], [1035, 14, 788, 91, "lineNumber"], [1035, 24, 788, 91], [1036, 14, 788, 91, "columnNumber"], [1036, 26, 788, 91], [1037, 12, 788, 91], [1037, 19, 789, 18], [1037, 20, 789, 19], [1037, 22, 790, 13, "processingState"], [1037, 37, 790, 28], [1037, 42, 790, 33], [1037, 53, 790, 44], [1037, 70, 791, 14], [1037, 74, 791, 14, "_jsxDevRuntime"], [1037, 88, 791, 14], [1037, 89, 791, 14, "jsxDEV"], [1037, 95, 791, 14], [1037, 97, 791, 15, "_lucideReactNative"], [1037, 115, 791, 15], [1037, 116, 791, 15, "CheckCircle"], [1037, 127, 791, 26], [1038, 14, 791, 27, "size"], [1038, 18, 791, 31], [1038, 20, 791, 33], [1038, 22, 791, 36], [1039, 14, 791, 37, "color"], [1039, 19, 791, 42], [1039, 21, 791, 43], [1039, 30, 791, 52], [1040, 14, 791, 53, "style"], [1040, 19, 791, 58], [1040, 21, 791, 60, "styles"], [1040, 27, 791, 66], [1040, 28, 791, 67, "successIcon"], [1041, 12, 791, 79], [1042, 14, 791, 79, "fileName"], [1042, 22, 791, 79], [1042, 24, 791, 79, "_jsxFileName"], [1042, 36, 791, 79], [1043, 14, 791, 79, "lineNumber"], [1043, 24, 791, 79], [1044, 14, 791, 79, "columnNumber"], [1044, 26, 791, 79], [1045, 12, 791, 79], [1045, 19, 791, 81], [1045, 20, 792, 13], [1046, 10, 792, 13], [1047, 12, 792, 13, "fileName"], [1047, 20, 792, 13], [1047, 22, 792, 13, "_jsxFileName"], [1047, 34, 792, 13], [1048, 12, 792, 13, "lineNumber"], [1048, 22, 792, 13], [1049, 12, 792, 13, "columnNumber"], [1049, 24, 792, 13], [1050, 10, 792, 13], [1050, 17, 793, 16], [1051, 8, 793, 17], [1052, 10, 793, 17, "fileName"], [1052, 18, 793, 17], [1052, 20, 793, 17, "_jsxFileName"], [1052, 32, 793, 17], [1053, 10, 793, 17, "lineNumber"], [1053, 20, 793, 17], [1054, 10, 793, 17, "columnNumber"], [1054, 22, 793, 17], [1055, 8, 793, 17], [1055, 15, 794, 14], [1056, 6, 794, 15], [1057, 8, 794, 15, "fileName"], [1057, 16, 794, 15], [1057, 18, 794, 15, "_jsxFileName"], [1057, 30, 794, 15], [1058, 8, 794, 15, "lineNumber"], [1058, 18, 794, 15], [1059, 8, 794, 15, "columnNumber"], [1059, 20, 794, 15], [1060, 6, 794, 15], [1060, 13, 795, 13], [1060, 14, 795, 14], [1060, 29, 797, 6], [1060, 33, 797, 6, "_jsxDevRuntime"], [1060, 47, 797, 6], [1060, 48, 797, 6, "jsxDEV"], [1060, 54, 797, 6], [1060, 56, 797, 7, "_Modal"], [1060, 62, 797, 7], [1060, 63, 797, 7, "default"], [1060, 70, 797, 12], [1061, 8, 798, 8, "visible"], [1061, 15, 798, 15], [1061, 17, 798, 17, "processingState"], [1061, 32, 798, 32], [1061, 37, 798, 37], [1061, 44, 798, 45], [1062, 8, 799, 8, "transparent"], [1062, 19, 799, 19], [1063, 8, 800, 8, "animationType"], [1063, 21, 800, 21], [1063, 23, 800, 22], [1063, 29, 800, 28], [1064, 8, 800, 28, "children"], [1064, 16, 800, 28], [1064, 31, 802, 8], [1064, 35, 802, 8, "_jsxDevRuntime"], [1064, 49, 802, 8], [1064, 50, 802, 8, "jsxDEV"], [1064, 56, 802, 8], [1064, 58, 802, 9, "_View"], [1064, 63, 802, 9], [1064, 64, 802, 9, "default"], [1064, 71, 802, 13], [1065, 10, 802, 14, "style"], [1065, 15, 802, 19], [1065, 17, 802, 21, "styles"], [1065, 23, 802, 27], [1065, 24, 802, 28, "processingModal"], [1065, 39, 802, 44], [1066, 10, 802, 44, "children"], [1066, 18, 802, 44], [1066, 33, 803, 10], [1066, 37, 803, 10, "_jsxDevRuntime"], [1066, 51, 803, 10], [1066, 52, 803, 10, "jsxDEV"], [1066, 58, 803, 10], [1066, 60, 803, 11, "_View"], [1066, 65, 803, 11], [1066, 66, 803, 11, "default"], [1066, 73, 803, 15], [1067, 12, 803, 16, "style"], [1067, 17, 803, 21], [1067, 19, 803, 23, "styles"], [1067, 25, 803, 29], [1067, 26, 803, 30, "errorContent"], [1067, 38, 803, 43], [1068, 12, 803, 43, "children"], [1068, 20, 803, 43], [1068, 36, 804, 12], [1068, 40, 804, 12, "_jsxDevRuntime"], [1068, 54, 804, 12], [1068, 55, 804, 12, "jsxDEV"], [1068, 61, 804, 12], [1068, 63, 804, 13, "_lucideReactNative"], [1068, 81, 804, 13], [1068, 82, 804, 13, "X"], [1068, 83, 804, 14], [1069, 14, 804, 15, "size"], [1069, 18, 804, 19], [1069, 20, 804, 21], [1069, 22, 804, 24], [1070, 14, 804, 25, "color"], [1070, 19, 804, 30], [1070, 21, 804, 31], [1071, 12, 804, 40], [1072, 14, 804, 40, "fileName"], [1072, 22, 804, 40], [1072, 24, 804, 40, "_jsxFileName"], [1072, 36, 804, 40], [1073, 14, 804, 40, "lineNumber"], [1073, 24, 804, 40], [1074, 14, 804, 40, "columnNumber"], [1074, 26, 804, 40], [1075, 12, 804, 40], [1075, 19, 804, 42], [1075, 20, 804, 43], [1075, 35, 805, 12], [1075, 39, 805, 12, "_jsxDevRuntime"], [1075, 53, 805, 12], [1075, 54, 805, 12, "jsxDEV"], [1075, 60, 805, 12], [1075, 62, 805, 13, "_Text"], [1075, 67, 805, 13], [1075, 68, 805, 13, "default"], [1075, 75, 805, 17], [1076, 14, 805, 18, "style"], [1076, 19, 805, 23], [1076, 21, 805, 25, "styles"], [1076, 27, 805, 31], [1076, 28, 805, 32, "errorTitle"], [1076, 38, 805, 43], [1077, 14, 805, 43, "children"], [1077, 22, 805, 43], [1077, 24, 805, 44], [1078, 12, 805, 61], [1079, 14, 805, 61, "fileName"], [1079, 22, 805, 61], [1079, 24, 805, 61, "_jsxFileName"], [1079, 36, 805, 61], [1080, 14, 805, 61, "lineNumber"], [1080, 24, 805, 61], [1081, 14, 805, 61, "columnNumber"], [1081, 26, 805, 61], [1082, 12, 805, 61], [1082, 19, 805, 67], [1082, 20, 805, 68], [1082, 35, 806, 12], [1082, 39, 806, 12, "_jsxDevRuntime"], [1082, 53, 806, 12], [1082, 54, 806, 12, "jsxDEV"], [1082, 60, 806, 12], [1082, 62, 806, 13, "_Text"], [1082, 67, 806, 13], [1082, 68, 806, 13, "default"], [1082, 75, 806, 17], [1083, 14, 806, 18, "style"], [1083, 19, 806, 23], [1083, 21, 806, 25, "styles"], [1083, 27, 806, 31], [1083, 28, 806, 32, "errorMessage"], [1083, 40, 806, 45], [1084, 14, 806, 45, "children"], [1084, 22, 806, 45], [1084, 24, 806, 47, "errorMessage"], [1085, 12, 806, 59], [1086, 14, 806, 59, "fileName"], [1086, 22, 806, 59], [1086, 24, 806, 59, "_jsxFileName"], [1086, 36, 806, 59], [1087, 14, 806, 59, "lineNumber"], [1087, 24, 806, 59], [1088, 14, 806, 59, "columnNumber"], [1088, 26, 806, 59], [1089, 12, 806, 59], [1089, 19, 806, 66], [1089, 20, 806, 67], [1089, 35, 807, 12], [1089, 39, 807, 12, "_jsxDevRuntime"], [1089, 53, 807, 12], [1089, 54, 807, 12, "jsxDEV"], [1089, 60, 807, 12], [1089, 62, 807, 13, "_TouchableOpacity"], [1089, 79, 807, 13], [1089, 80, 807, 13, "default"], [1089, 87, 807, 29], [1090, 14, 808, 14, "onPress"], [1090, 21, 808, 21], [1090, 23, 808, 23, "retryCapture"], [1090, 35, 808, 36], [1091, 14, 809, 14, "style"], [1091, 19, 809, 19], [1091, 21, 809, 21, "styles"], [1091, 27, 809, 27], [1091, 28, 809, 28, "primaryButton"], [1091, 41, 809, 42], [1092, 14, 809, 42, "children"], [1092, 22, 809, 42], [1092, 37, 811, 14], [1092, 41, 811, 14, "_jsxDevRuntime"], [1092, 55, 811, 14], [1092, 56, 811, 14, "jsxDEV"], [1092, 62, 811, 14], [1092, 64, 811, 15, "_Text"], [1092, 69, 811, 15], [1092, 70, 811, 15, "default"], [1092, 77, 811, 19], [1093, 16, 811, 20, "style"], [1093, 21, 811, 25], [1093, 23, 811, 27, "styles"], [1093, 29, 811, 33], [1093, 30, 811, 34, "primaryButtonText"], [1093, 47, 811, 52], [1094, 16, 811, 52, "children"], [1094, 24, 811, 52], [1094, 26, 811, 53], [1095, 14, 811, 62], [1096, 16, 811, 62, "fileName"], [1096, 24, 811, 62], [1096, 26, 811, 62, "_jsxFileName"], [1096, 38, 811, 62], [1097, 16, 811, 62, "lineNumber"], [1097, 26, 811, 62], [1098, 16, 811, 62, "columnNumber"], [1098, 28, 811, 62], [1099, 14, 811, 62], [1099, 21, 811, 68], [1100, 12, 811, 69], [1101, 14, 811, 69, "fileName"], [1101, 22, 811, 69], [1101, 24, 811, 69, "_jsxFileName"], [1101, 36, 811, 69], [1102, 14, 811, 69, "lineNumber"], [1102, 24, 811, 69], [1103, 14, 811, 69, "columnNumber"], [1103, 26, 811, 69], [1104, 12, 811, 69], [1104, 19, 812, 30], [1104, 20, 812, 31], [1104, 35, 813, 12], [1104, 39, 813, 12, "_jsxDevRuntime"], [1104, 53, 813, 12], [1104, 54, 813, 12, "jsxDEV"], [1104, 60, 813, 12], [1104, 62, 813, 13, "_TouchableOpacity"], [1104, 79, 813, 13], [1104, 80, 813, 13, "default"], [1104, 87, 813, 29], [1105, 14, 814, 14, "onPress"], [1105, 21, 814, 21], [1105, 23, 814, 23, "onCancel"], [1105, 31, 814, 32], [1106, 14, 815, 14, "style"], [1106, 19, 815, 19], [1106, 21, 815, 21, "styles"], [1106, 27, 815, 27], [1106, 28, 815, 28, "secondaryButton"], [1106, 43, 815, 44], [1107, 14, 815, 44, "children"], [1107, 22, 815, 44], [1107, 37, 817, 14], [1107, 41, 817, 14, "_jsxDevRuntime"], [1107, 55, 817, 14], [1107, 56, 817, 14, "jsxDEV"], [1107, 62, 817, 14], [1107, 64, 817, 15, "_Text"], [1107, 69, 817, 15], [1107, 70, 817, 15, "default"], [1107, 77, 817, 19], [1108, 16, 817, 20, "style"], [1108, 21, 817, 25], [1108, 23, 817, 27, "styles"], [1108, 29, 817, 33], [1108, 30, 817, 34, "secondaryButtonText"], [1108, 49, 817, 54], [1109, 16, 817, 54, "children"], [1109, 24, 817, 54], [1109, 26, 817, 55], [1110, 14, 817, 61], [1111, 16, 817, 61, "fileName"], [1111, 24, 817, 61], [1111, 26, 817, 61, "_jsxFileName"], [1111, 38, 817, 61], [1112, 16, 817, 61, "lineNumber"], [1112, 26, 817, 61], [1113, 16, 817, 61, "columnNumber"], [1113, 28, 817, 61], [1114, 14, 817, 61], [1114, 21, 817, 67], [1115, 12, 817, 68], [1116, 14, 817, 68, "fileName"], [1116, 22, 817, 68], [1116, 24, 817, 68, "_jsxFileName"], [1116, 36, 817, 68], [1117, 14, 817, 68, "lineNumber"], [1117, 24, 817, 68], [1118, 14, 817, 68, "columnNumber"], [1118, 26, 817, 68], [1119, 12, 817, 68], [1119, 19, 818, 30], [1119, 20, 818, 31], [1120, 10, 818, 31], [1121, 12, 818, 31, "fileName"], [1121, 20, 818, 31], [1121, 22, 818, 31, "_jsxFileName"], [1121, 34, 818, 31], [1122, 12, 818, 31, "lineNumber"], [1122, 22, 818, 31], [1123, 12, 818, 31, "columnNumber"], [1123, 24, 818, 31], [1124, 10, 818, 31], [1124, 17, 819, 16], [1125, 8, 819, 17], [1126, 10, 819, 17, "fileName"], [1126, 18, 819, 17], [1126, 20, 819, 17, "_jsxFileName"], [1126, 32, 819, 17], [1127, 10, 819, 17, "lineNumber"], [1127, 20, 819, 17], [1128, 10, 819, 17, "columnNumber"], [1128, 22, 819, 17], [1129, 8, 819, 17], [1129, 15, 820, 14], [1130, 6, 820, 15], [1131, 8, 820, 15, "fileName"], [1131, 16, 820, 15], [1131, 18, 820, 15, "_jsxFileName"], [1131, 30, 820, 15], [1132, 8, 820, 15, "lineNumber"], [1132, 18, 820, 15], [1133, 8, 820, 15, "columnNumber"], [1133, 20, 820, 15], [1134, 6, 820, 15], [1134, 13, 821, 13], [1134, 14, 821, 14], [1135, 4, 821, 14], [1136, 6, 821, 14, "fileName"], [1136, 14, 821, 14], [1136, 16, 821, 14, "_jsxFileName"], [1136, 28, 821, 14], [1137, 6, 821, 14, "lineNumber"], [1137, 16, 821, 14], [1138, 6, 821, 14, "columnNumber"], [1138, 18, 821, 14], [1139, 4, 821, 14], [1139, 11, 822, 10], [1139, 12, 822, 11], [1140, 2, 824, 0], [1141, 2, 824, 1, "_s"], [1141, 4, 824, 1], [1141, 5, 51, 24, "EchoCameraWeb"], [1141, 18, 51, 37], [1142, 4, 51, 37], [1142, 12, 58, 42, "useCameraPermissions"], [1142, 44, 58, 62], [1142, 46, 72, 19, "useUpload"], [1142, 64, 72, 28], [1143, 2, 72, 28], [1144, 2, 72, 28, "_c"], [1144, 4, 72, 28], [1144, 7, 51, 24, "EchoCameraWeb"], [1144, 20, 51, 37], [1145, 2, 825, 0], [1145, 8, 825, 6, "styles"], [1145, 14, 825, 12], [1145, 17, 825, 15, "StyleSheet"], [1145, 36, 825, 25], [1145, 37, 825, 26, "create"], [1145, 43, 825, 32], [1145, 44, 825, 33], [1146, 4, 826, 2, "container"], [1146, 13, 826, 11], [1146, 15, 826, 13], [1147, 6, 827, 4, "flex"], [1147, 10, 827, 8], [1147, 12, 827, 10], [1147, 13, 827, 11], [1148, 6, 828, 4, "backgroundColor"], [1148, 21, 828, 19], [1148, 23, 828, 21], [1149, 4, 829, 2], [1149, 5, 829, 3], [1150, 4, 830, 2, "cameraContainer"], [1150, 19, 830, 17], [1150, 21, 830, 19], [1151, 6, 831, 4, "flex"], [1151, 10, 831, 8], [1151, 12, 831, 10], [1151, 13, 831, 11], [1152, 6, 832, 4, "max<PERSON><PERSON><PERSON>"], [1152, 14, 832, 12], [1152, 16, 832, 14], [1152, 19, 832, 17], [1153, 6, 833, 4, "alignSelf"], [1153, 15, 833, 13], [1153, 17, 833, 15], [1153, 25, 833, 23], [1154, 6, 834, 4, "width"], [1154, 11, 834, 9], [1154, 13, 834, 11], [1155, 4, 835, 2], [1155, 5, 835, 3], [1156, 4, 836, 2, "camera"], [1156, 10, 836, 8], [1156, 12, 836, 10], [1157, 6, 837, 4, "flex"], [1157, 10, 837, 8], [1157, 12, 837, 10], [1158, 4, 838, 2], [1158, 5, 838, 3], [1159, 4, 839, 2, "headerOverlay"], [1159, 17, 839, 15], [1159, 19, 839, 17], [1160, 6, 840, 4, "position"], [1160, 14, 840, 12], [1160, 16, 840, 14], [1160, 26, 840, 24], [1161, 6, 841, 4, "top"], [1161, 9, 841, 7], [1161, 11, 841, 9], [1161, 12, 841, 10], [1162, 6, 842, 4, "left"], [1162, 10, 842, 8], [1162, 12, 842, 10], [1162, 13, 842, 11], [1163, 6, 843, 4, "right"], [1163, 11, 843, 9], [1163, 13, 843, 11], [1163, 14, 843, 12], [1164, 6, 844, 4, "backgroundColor"], [1164, 21, 844, 19], [1164, 23, 844, 21], [1164, 36, 844, 34], [1165, 6, 845, 4, "paddingTop"], [1165, 16, 845, 14], [1165, 18, 845, 16], [1165, 20, 845, 18], [1166, 6, 846, 4, "paddingHorizontal"], [1166, 23, 846, 21], [1166, 25, 846, 23], [1166, 27, 846, 25], [1167, 6, 847, 4, "paddingBottom"], [1167, 19, 847, 17], [1167, 21, 847, 19], [1168, 4, 848, 2], [1168, 5, 848, 3], [1169, 4, 849, 2, "headerContent"], [1169, 17, 849, 15], [1169, 19, 849, 17], [1170, 6, 850, 4, "flexDirection"], [1170, 19, 850, 17], [1170, 21, 850, 19], [1170, 26, 850, 24], [1171, 6, 851, 4, "justifyContent"], [1171, 20, 851, 18], [1171, 22, 851, 20], [1171, 37, 851, 35], [1172, 6, 852, 4, "alignItems"], [1172, 16, 852, 14], [1172, 18, 852, 16], [1173, 4, 853, 2], [1173, 5, 853, 3], [1174, 4, 854, 2, "headerLeft"], [1174, 14, 854, 12], [1174, 16, 854, 14], [1175, 6, 855, 4, "flex"], [1175, 10, 855, 8], [1175, 12, 855, 10], [1176, 4, 856, 2], [1176, 5, 856, 3], [1177, 4, 857, 2, "headerTitle"], [1177, 15, 857, 13], [1177, 17, 857, 15], [1178, 6, 858, 4, "fontSize"], [1178, 14, 858, 12], [1178, 16, 858, 14], [1178, 18, 858, 16], [1179, 6, 859, 4, "fontWeight"], [1179, 16, 859, 14], [1179, 18, 859, 16], [1179, 23, 859, 21], [1180, 6, 860, 4, "color"], [1180, 11, 860, 9], [1180, 13, 860, 11], [1180, 19, 860, 17], [1181, 6, 861, 4, "marginBottom"], [1181, 18, 861, 16], [1181, 20, 861, 18], [1182, 4, 862, 2], [1182, 5, 862, 3], [1183, 4, 863, 2, "subtitleRow"], [1183, 15, 863, 13], [1183, 17, 863, 15], [1184, 6, 864, 4, "flexDirection"], [1184, 19, 864, 17], [1184, 21, 864, 19], [1184, 26, 864, 24], [1185, 6, 865, 4, "alignItems"], [1185, 16, 865, 14], [1185, 18, 865, 16], [1185, 26, 865, 24], [1186, 6, 866, 4, "marginBottom"], [1186, 18, 866, 16], [1186, 20, 866, 18], [1187, 4, 867, 2], [1187, 5, 867, 3], [1188, 4, 868, 2, "webIcon"], [1188, 11, 868, 9], [1188, 13, 868, 11], [1189, 6, 869, 4, "fontSize"], [1189, 14, 869, 12], [1189, 16, 869, 14], [1189, 18, 869, 16], [1190, 6, 870, 4, "marginRight"], [1190, 17, 870, 15], [1190, 19, 870, 17], [1191, 4, 871, 2], [1191, 5, 871, 3], [1192, 4, 872, 2, "headerSubtitle"], [1192, 18, 872, 16], [1192, 20, 872, 18], [1193, 6, 873, 4, "fontSize"], [1193, 14, 873, 12], [1193, 16, 873, 14], [1193, 18, 873, 16], [1194, 6, 874, 4, "color"], [1194, 11, 874, 9], [1194, 13, 874, 11], [1194, 19, 874, 17], [1195, 6, 875, 4, "opacity"], [1195, 13, 875, 11], [1195, 15, 875, 13], [1196, 4, 876, 2], [1196, 5, 876, 3], [1197, 4, 877, 2, "challengeRow"], [1197, 16, 877, 14], [1197, 18, 877, 16], [1198, 6, 878, 4, "flexDirection"], [1198, 19, 878, 17], [1198, 21, 878, 19], [1198, 26, 878, 24], [1199, 6, 879, 4, "alignItems"], [1199, 16, 879, 14], [1199, 18, 879, 16], [1200, 4, 880, 2], [1200, 5, 880, 3], [1201, 4, 881, 2, "challengeCode"], [1201, 17, 881, 15], [1201, 19, 881, 17], [1202, 6, 882, 4, "fontSize"], [1202, 14, 882, 12], [1202, 16, 882, 14], [1202, 18, 882, 16], [1203, 6, 883, 4, "color"], [1203, 11, 883, 9], [1203, 13, 883, 11], [1203, 19, 883, 17], [1204, 6, 884, 4, "marginLeft"], [1204, 16, 884, 14], [1204, 18, 884, 16], [1204, 19, 884, 17], [1205, 6, 885, 4, "fontFamily"], [1205, 16, 885, 14], [1205, 18, 885, 16], [1206, 4, 886, 2], [1206, 5, 886, 3], [1207, 4, 887, 2, "closeButton"], [1207, 15, 887, 13], [1207, 17, 887, 15], [1208, 6, 888, 4, "padding"], [1208, 13, 888, 11], [1208, 15, 888, 13], [1209, 4, 889, 2], [1209, 5, 889, 3], [1210, 4, 890, 2, "privacyNotice"], [1210, 17, 890, 15], [1210, 19, 890, 17], [1211, 6, 891, 4, "position"], [1211, 14, 891, 12], [1211, 16, 891, 14], [1211, 26, 891, 24], [1212, 6, 892, 4, "top"], [1212, 9, 892, 7], [1212, 11, 892, 9], [1212, 14, 892, 12], [1213, 6, 893, 4, "left"], [1213, 10, 893, 8], [1213, 12, 893, 10], [1213, 14, 893, 12], [1214, 6, 894, 4, "right"], [1214, 11, 894, 9], [1214, 13, 894, 11], [1214, 15, 894, 13], [1215, 6, 895, 4, "backgroundColor"], [1215, 21, 895, 19], [1215, 23, 895, 21], [1215, 48, 895, 46], [1216, 6, 896, 4, "borderRadius"], [1216, 18, 896, 16], [1216, 20, 896, 18], [1216, 21, 896, 19], [1217, 6, 897, 4, "padding"], [1217, 13, 897, 11], [1217, 15, 897, 13], [1217, 17, 897, 15], [1218, 6, 898, 4, "flexDirection"], [1218, 19, 898, 17], [1218, 21, 898, 19], [1218, 26, 898, 24], [1219, 6, 899, 4, "alignItems"], [1219, 16, 899, 14], [1219, 18, 899, 16], [1220, 4, 900, 2], [1220, 5, 900, 3], [1221, 4, 901, 2, "privacyText"], [1221, 15, 901, 13], [1221, 17, 901, 15], [1222, 6, 902, 4, "color"], [1222, 11, 902, 9], [1222, 13, 902, 11], [1222, 19, 902, 17], [1223, 6, 903, 4, "fontSize"], [1223, 14, 903, 12], [1223, 16, 903, 14], [1223, 18, 903, 16], [1224, 6, 904, 4, "marginLeft"], [1224, 16, 904, 14], [1224, 18, 904, 16], [1224, 19, 904, 17], [1225, 6, 905, 4, "flex"], [1225, 10, 905, 8], [1225, 12, 905, 10], [1226, 4, 906, 2], [1226, 5, 906, 3], [1227, 4, 907, 2, "footer<PERSON><PERSON><PERSON>"], [1227, 17, 907, 15], [1227, 19, 907, 17], [1228, 6, 908, 4, "position"], [1228, 14, 908, 12], [1228, 16, 908, 14], [1228, 26, 908, 24], [1229, 6, 909, 4, "bottom"], [1229, 12, 909, 10], [1229, 14, 909, 12], [1229, 15, 909, 13], [1230, 6, 910, 4, "left"], [1230, 10, 910, 8], [1230, 12, 910, 10], [1230, 13, 910, 11], [1231, 6, 911, 4, "right"], [1231, 11, 911, 9], [1231, 13, 911, 11], [1231, 14, 911, 12], [1232, 6, 912, 4, "backgroundColor"], [1232, 21, 912, 19], [1232, 23, 912, 21], [1232, 36, 912, 34], [1233, 6, 913, 4, "paddingBottom"], [1233, 19, 913, 17], [1233, 21, 913, 19], [1233, 23, 913, 21], [1234, 6, 914, 4, "paddingTop"], [1234, 16, 914, 14], [1234, 18, 914, 16], [1234, 20, 914, 18], [1235, 6, 915, 4, "alignItems"], [1235, 16, 915, 14], [1235, 18, 915, 16], [1236, 4, 916, 2], [1236, 5, 916, 3], [1237, 4, 917, 2, "instruction"], [1237, 15, 917, 13], [1237, 17, 917, 15], [1238, 6, 918, 4, "fontSize"], [1238, 14, 918, 12], [1238, 16, 918, 14], [1238, 18, 918, 16], [1239, 6, 919, 4, "color"], [1239, 11, 919, 9], [1239, 13, 919, 11], [1239, 19, 919, 17], [1240, 6, 920, 4, "marginBottom"], [1240, 18, 920, 16], [1240, 20, 920, 18], [1241, 4, 921, 2], [1241, 5, 921, 3], [1242, 4, 922, 2, "shutterButton"], [1242, 17, 922, 15], [1242, 19, 922, 17], [1243, 6, 923, 4, "width"], [1243, 11, 923, 9], [1243, 13, 923, 11], [1243, 15, 923, 13], [1244, 6, 924, 4, "height"], [1244, 12, 924, 10], [1244, 14, 924, 12], [1244, 16, 924, 14], [1245, 6, 925, 4, "borderRadius"], [1245, 18, 925, 16], [1245, 20, 925, 18], [1245, 22, 925, 20], [1246, 6, 926, 4, "backgroundColor"], [1246, 21, 926, 19], [1246, 23, 926, 21], [1246, 29, 926, 27], [1247, 6, 927, 4, "justifyContent"], [1247, 20, 927, 18], [1247, 22, 927, 20], [1247, 30, 927, 28], [1248, 6, 928, 4, "alignItems"], [1248, 16, 928, 14], [1248, 18, 928, 16], [1248, 26, 928, 24], [1249, 6, 929, 4, "marginBottom"], [1249, 18, 929, 16], [1249, 20, 929, 18], [1249, 22, 929, 20], [1250, 6, 930, 4], [1250, 9, 930, 7, "Platform"], [1250, 26, 930, 15], [1250, 27, 930, 16, "select"], [1250, 33, 930, 22], [1250, 34, 930, 23], [1251, 8, 931, 6, "ios"], [1251, 11, 931, 9], [1251, 13, 931, 11], [1252, 10, 932, 8, "shadowColor"], [1252, 21, 932, 19], [1252, 23, 932, 21], [1252, 32, 932, 30], [1253, 10, 933, 8, "shadowOffset"], [1253, 22, 933, 20], [1253, 24, 933, 22], [1254, 12, 933, 24, "width"], [1254, 17, 933, 29], [1254, 19, 933, 31], [1254, 20, 933, 32], [1255, 12, 933, 34, "height"], [1255, 18, 933, 40], [1255, 20, 933, 42], [1256, 10, 933, 44], [1256, 11, 933, 45], [1257, 10, 934, 8, "shadowOpacity"], [1257, 23, 934, 21], [1257, 25, 934, 23], [1257, 28, 934, 26], [1258, 10, 935, 8, "shadowRadius"], [1258, 22, 935, 20], [1258, 24, 935, 22], [1259, 8, 936, 6], [1259, 9, 936, 7], [1260, 8, 937, 6, "android"], [1260, 15, 937, 13], [1260, 17, 937, 15], [1261, 10, 938, 8, "elevation"], [1261, 19, 938, 17], [1261, 21, 938, 19], [1262, 8, 939, 6], [1262, 9, 939, 7], [1263, 8, 940, 6, "web"], [1263, 11, 940, 9], [1263, 13, 940, 11], [1264, 10, 941, 8, "boxShadow"], [1264, 19, 941, 17], [1264, 21, 941, 19], [1265, 8, 942, 6], [1266, 6, 943, 4], [1266, 7, 943, 5], [1267, 4, 944, 2], [1267, 5, 944, 3], [1268, 4, 945, 2, "shutterButtonDisabled"], [1268, 25, 945, 23], [1268, 27, 945, 25], [1269, 6, 946, 4, "opacity"], [1269, 13, 946, 11], [1269, 15, 946, 13], [1270, 4, 947, 2], [1270, 5, 947, 3], [1271, 4, 948, 2, "shutterInner"], [1271, 16, 948, 14], [1271, 18, 948, 16], [1272, 6, 949, 4, "width"], [1272, 11, 949, 9], [1272, 13, 949, 11], [1272, 15, 949, 13], [1273, 6, 950, 4, "height"], [1273, 12, 950, 10], [1273, 14, 950, 12], [1273, 16, 950, 14], [1274, 6, 951, 4, "borderRadius"], [1274, 18, 951, 16], [1274, 20, 951, 18], [1274, 22, 951, 20], [1275, 6, 952, 4, "backgroundColor"], [1275, 21, 952, 19], [1275, 23, 952, 21], [1275, 29, 952, 27], [1276, 6, 953, 4, "borderWidth"], [1276, 17, 953, 15], [1276, 19, 953, 17], [1276, 20, 953, 18], [1277, 6, 954, 4, "borderColor"], [1277, 17, 954, 15], [1277, 19, 954, 17], [1278, 4, 955, 2], [1278, 5, 955, 3], [1279, 4, 956, 2, "privacyNote"], [1279, 15, 956, 13], [1279, 17, 956, 15], [1280, 6, 957, 4, "fontSize"], [1280, 14, 957, 12], [1280, 16, 957, 14], [1280, 18, 957, 16], [1281, 6, 958, 4, "color"], [1281, 11, 958, 9], [1281, 13, 958, 11], [1282, 4, 959, 2], [1282, 5, 959, 3], [1283, 4, 960, 2, "processingModal"], [1283, 19, 960, 17], [1283, 21, 960, 19], [1284, 6, 961, 4, "flex"], [1284, 10, 961, 8], [1284, 12, 961, 10], [1284, 13, 961, 11], [1285, 6, 962, 4, "backgroundColor"], [1285, 21, 962, 19], [1285, 23, 962, 21], [1285, 43, 962, 41], [1286, 6, 963, 4, "justifyContent"], [1286, 20, 963, 18], [1286, 22, 963, 20], [1286, 30, 963, 28], [1287, 6, 964, 4, "alignItems"], [1287, 16, 964, 14], [1287, 18, 964, 16], [1288, 4, 965, 2], [1288, 5, 965, 3], [1289, 4, 966, 2, "processingContent"], [1289, 21, 966, 19], [1289, 23, 966, 21], [1290, 6, 967, 4, "backgroundColor"], [1290, 21, 967, 19], [1290, 23, 967, 21], [1290, 29, 967, 27], [1291, 6, 968, 4, "borderRadius"], [1291, 18, 968, 16], [1291, 20, 968, 18], [1291, 22, 968, 20], [1292, 6, 969, 4, "padding"], [1292, 13, 969, 11], [1292, 15, 969, 13], [1292, 17, 969, 15], [1293, 6, 970, 4, "width"], [1293, 11, 970, 9], [1293, 13, 970, 11], [1293, 18, 970, 16], [1294, 6, 971, 4, "max<PERSON><PERSON><PERSON>"], [1294, 14, 971, 12], [1294, 16, 971, 14], [1294, 19, 971, 17], [1295, 6, 972, 4, "alignItems"], [1295, 16, 972, 14], [1295, 18, 972, 16], [1296, 4, 973, 2], [1296, 5, 973, 3], [1297, 4, 974, 2, "processingTitle"], [1297, 19, 974, 17], [1297, 21, 974, 19], [1298, 6, 975, 4, "fontSize"], [1298, 14, 975, 12], [1298, 16, 975, 14], [1298, 18, 975, 16], [1299, 6, 976, 4, "fontWeight"], [1299, 16, 976, 14], [1299, 18, 976, 16], [1299, 23, 976, 21], [1300, 6, 977, 4, "color"], [1300, 11, 977, 9], [1300, 13, 977, 11], [1300, 22, 977, 20], [1301, 6, 978, 4, "marginTop"], [1301, 15, 978, 13], [1301, 17, 978, 15], [1301, 19, 978, 17], [1302, 6, 979, 4, "marginBottom"], [1302, 18, 979, 16], [1302, 20, 979, 18], [1303, 4, 980, 2], [1303, 5, 980, 3], [1304, 4, 981, 2, "progressBar"], [1304, 15, 981, 13], [1304, 17, 981, 15], [1305, 6, 982, 4, "width"], [1305, 11, 982, 9], [1305, 13, 982, 11], [1305, 19, 982, 17], [1306, 6, 983, 4, "height"], [1306, 12, 983, 10], [1306, 14, 983, 12], [1306, 15, 983, 13], [1307, 6, 984, 4, "backgroundColor"], [1307, 21, 984, 19], [1307, 23, 984, 21], [1307, 32, 984, 30], [1308, 6, 985, 4, "borderRadius"], [1308, 18, 985, 16], [1308, 20, 985, 18], [1308, 21, 985, 19], [1309, 6, 986, 4, "overflow"], [1309, 14, 986, 12], [1309, 16, 986, 14], [1309, 24, 986, 22], [1310, 6, 987, 4, "marginBottom"], [1310, 18, 987, 16], [1310, 20, 987, 18], [1311, 4, 988, 2], [1311, 5, 988, 3], [1312, 4, 989, 2, "progressFill"], [1312, 16, 989, 14], [1312, 18, 989, 16], [1313, 6, 990, 4, "height"], [1313, 12, 990, 10], [1313, 14, 990, 12], [1313, 20, 990, 18], [1314, 6, 991, 4, "backgroundColor"], [1314, 21, 991, 19], [1314, 23, 991, 21], [1314, 32, 991, 30], [1315, 6, 992, 4, "borderRadius"], [1315, 18, 992, 16], [1315, 20, 992, 18], [1316, 4, 993, 2], [1316, 5, 993, 3], [1317, 4, 994, 2, "processingDescription"], [1317, 25, 994, 23], [1317, 27, 994, 25], [1318, 6, 995, 4, "fontSize"], [1318, 14, 995, 12], [1318, 16, 995, 14], [1318, 18, 995, 16], [1319, 6, 996, 4, "color"], [1319, 11, 996, 9], [1319, 13, 996, 11], [1319, 22, 996, 20], [1320, 6, 997, 4, "textAlign"], [1320, 15, 997, 13], [1320, 17, 997, 15], [1321, 4, 998, 2], [1321, 5, 998, 3], [1322, 4, 999, 2, "successIcon"], [1322, 15, 999, 13], [1322, 17, 999, 15], [1323, 6, 1000, 4, "marginTop"], [1323, 15, 1000, 13], [1323, 17, 1000, 15], [1324, 4, 1001, 2], [1324, 5, 1001, 3], [1325, 4, 1002, 2, "errorContent"], [1325, 16, 1002, 14], [1325, 18, 1002, 16], [1326, 6, 1003, 4, "backgroundColor"], [1326, 21, 1003, 19], [1326, 23, 1003, 21], [1326, 29, 1003, 27], [1327, 6, 1004, 4, "borderRadius"], [1327, 18, 1004, 16], [1327, 20, 1004, 18], [1327, 22, 1004, 20], [1328, 6, 1005, 4, "padding"], [1328, 13, 1005, 11], [1328, 15, 1005, 13], [1328, 17, 1005, 15], [1329, 6, 1006, 4, "width"], [1329, 11, 1006, 9], [1329, 13, 1006, 11], [1329, 18, 1006, 16], [1330, 6, 1007, 4, "max<PERSON><PERSON><PERSON>"], [1330, 14, 1007, 12], [1330, 16, 1007, 14], [1330, 19, 1007, 17], [1331, 6, 1008, 4, "alignItems"], [1331, 16, 1008, 14], [1331, 18, 1008, 16], [1332, 4, 1009, 2], [1332, 5, 1009, 3], [1333, 4, 1010, 2, "errorTitle"], [1333, 14, 1010, 12], [1333, 16, 1010, 14], [1334, 6, 1011, 4, "fontSize"], [1334, 14, 1011, 12], [1334, 16, 1011, 14], [1334, 18, 1011, 16], [1335, 6, 1012, 4, "fontWeight"], [1335, 16, 1012, 14], [1335, 18, 1012, 16], [1335, 23, 1012, 21], [1336, 6, 1013, 4, "color"], [1336, 11, 1013, 9], [1336, 13, 1013, 11], [1336, 22, 1013, 20], [1337, 6, 1014, 4, "marginTop"], [1337, 15, 1014, 13], [1337, 17, 1014, 15], [1337, 19, 1014, 17], [1338, 6, 1015, 4, "marginBottom"], [1338, 18, 1015, 16], [1338, 20, 1015, 18], [1339, 4, 1016, 2], [1339, 5, 1016, 3], [1340, 4, 1017, 2, "errorMessage"], [1340, 16, 1017, 14], [1340, 18, 1017, 16], [1341, 6, 1018, 4, "fontSize"], [1341, 14, 1018, 12], [1341, 16, 1018, 14], [1341, 18, 1018, 16], [1342, 6, 1019, 4, "color"], [1342, 11, 1019, 9], [1342, 13, 1019, 11], [1342, 22, 1019, 20], [1343, 6, 1020, 4, "textAlign"], [1343, 15, 1020, 13], [1343, 17, 1020, 15], [1343, 25, 1020, 23], [1344, 6, 1021, 4, "marginBottom"], [1344, 18, 1021, 16], [1344, 20, 1021, 18], [1345, 4, 1022, 2], [1345, 5, 1022, 3], [1346, 4, 1023, 2, "primaryButton"], [1346, 17, 1023, 15], [1346, 19, 1023, 17], [1347, 6, 1024, 4, "backgroundColor"], [1347, 21, 1024, 19], [1347, 23, 1024, 21], [1347, 32, 1024, 30], [1348, 6, 1025, 4, "paddingHorizontal"], [1348, 23, 1025, 21], [1348, 25, 1025, 23], [1348, 27, 1025, 25], [1349, 6, 1026, 4, "paddingVertical"], [1349, 21, 1026, 19], [1349, 23, 1026, 21], [1349, 25, 1026, 23], [1350, 6, 1027, 4, "borderRadius"], [1350, 18, 1027, 16], [1350, 20, 1027, 18], [1350, 21, 1027, 19], [1351, 6, 1028, 4, "marginTop"], [1351, 15, 1028, 13], [1351, 17, 1028, 15], [1352, 4, 1029, 2], [1352, 5, 1029, 3], [1353, 4, 1030, 2, "primaryButtonText"], [1353, 21, 1030, 19], [1353, 23, 1030, 21], [1354, 6, 1031, 4, "color"], [1354, 11, 1031, 9], [1354, 13, 1031, 11], [1354, 19, 1031, 17], [1355, 6, 1032, 4, "fontSize"], [1355, 14, 1032, 12], [1355, 16, 1032, 14], [1355, 18, 1032, 16], [1356, 6, 1033, 4, "fontWeight"], [1356, 16, 1033, 14], [1356, 18, 1033, 16], [1357, 4, 1034, 2], [1357, 5, 1034, 3], [1358, 4, 1035, 2, "secondaryButton"], [1358, 19, 1035, 17], [1358, 21, 1035, 19], [1359, 6, 1036, 4, "paddingHorizontal"], [1359, 23, 1036, 21], [1359, 25, 1036, 23], [1359, 27, 1036, 25], [1360, 6, 1037, 4, "paddingVertical"], [1360, 21, 1037, 19], [1360, 23, 1037, 21], [1360, 25, 1037, 23], [1361, 6, 1038, 4, "marginTop"], [1361, 15, 1038, 13], [1361, 17, 1038, 15], [1362, 4, 1039, 2], [1362, 5, 1039, 3], [1363, 4, 1040, 2, "secondaryButtonText"], [1363, 23, 1040, 21], [1363, 25, 1040, 23], [1364, 6, 1041, 4, "color"], [1364, 11, 1041, 9], [1364, 13, 1041, 11], [1364, 22, 1041, 20], [1365, 6, 1042, 4, "fontSize"], [1365, 14, 1042, 12], [1365, 16, 1042, 14], [1366, 4, 1043, 2], [1366, 5, 1043, 3], [1367, 4, 1044, 2, "permissionContent"], [1367, 21, 1044, 19], [1367, 23, 1044, 21], [1368, 6, 1045, 4, "flex"], [1368, 10, 1045, 8], [1368, 12, 1045, 10], [1368, 13, 1045, 11], [1369, 6, 1046, 4, "justifyContent"], [1369, 20, 1046, 18], [1369, 22, 1046, 20], [1369, 30, 1046, 28], [1370, 6, 1047, 4, "alignItems"], [1370, 16, 1047, 14], [1370, 18, 1047, 16], [1370, 26, 1047, 24], [1371, 6, 1048, 4, "padding"], [1371, 13, 1048, 11], [1371, 15, 1048, 13], [1372, 4, 1049, 2], [1372, 5, 1049, 3], [1373, 4, 1050, 2, "permissionTitle"], [1373, 19, 1050, 17], [1373, 21, 1050, 19], [1374, 6, 1051, 4, "fontSize"], [1374, 14, 1051, 12], [1374, 16, 1051, 14], [1374, 18, 1051, 16], [1375, 6, 1052, 4, "fontWeight"], [1375, 16, 1052, 14], [1375, 18, 1052, 16], [1375, 23, 1052, 21], [1376, 6, 1053, 4, "color"], [1376, 11, 1053, 9], [1376, 13, 1053, 11], [1376, 22, 1053, 20], [1377, 6, 1054, 4, "marginTop"], [1377, 15, 1054, 13], [1377, 17, 1054, 15], [1377, 19, 1054, 17], [1378, 6, 1055, 4, "marginBottom"], [1378, 18, 1055, 16], [1378, 20, 1055, 18], [1379, 4, 1056, 2], [1379, 5, 1056, 3], [1380, 4, 1057, 2, "permissionDescription"], [1380, 25, 1057, 23], [1380, 27, 1057, 25], [1381, 6, 1058, 4, "fontSize"], [1381, 14, 1058, 12], [1381, 16, 1058, 14], [1381, 18, 1058, 16], [1382, 6, 1059, 4, "color"], [1382, 11, 1059, 9], [1382, 13, 1059, 11], [1382, 22, 1059, 20], [1383, 6, 1060, 4, "textAlign"], [1383, 15, 1060, 13], [1383, 17, 1060, 15], [1383, 25, 1060, 23], [1384, 6, 1061, 4, "marginBottom"], [1384, 18, 1061, 16], [1384, 20, 1061, 18], [1385, 4, 1062, 2], [1385, 5, 1062, 3], [1386, 4, 1063, 2, "loadingText"], [1386, 15, 1063, 13], [1386, 17, 1063, 15], [1387, 6, 1064, 4, "color"], [1387, 11, 1064, 9], [1387, 13, 1064, 11], [1387, 22, 1064, 20], [1388, 6, 1065, 4, "marginTop"], [1388, 15, 1065, 13], [1388, 17, 1065, 15], [1389, 4, 1066, 2], [1389, 5, 1066, 3], [1390, 4, 1067, 2], [1391, 4, 1068, 2, "blurZone"], [1391, 12, 1068, 10], [1391, 14, 1068, 12], [1392, 6, 1069, 4, "position"], [1392, 14, 1069, 12], [1392, 16, 1069, 14], [1392, 26, 1069, 24], [1393, 6, 1070, 4, "overflow"], [1393, 14, 1070, 12], [1393, 16, 1070, 14], [1394, 4, 1071, 2], [1394, 5, 1071, 3], [1395, 4, 1072, 2, "previewChip"], [1395, 15, 1072, 13], [1395, 17, 1072, 15], [1396, 6, 1073, 4, "position"], [1396, 14, 1073, 12], [1396, 16, 1073, 14], [1396, 26, 1073, 24], [1397, 6, 1074, 4, "top"], [1397, 9, 1074, 7], [1397, 11, 1074, 9], [1397, 12, 1074, 10], [1398, 6, 1075, 4, "right"], [1398, 11, 1075, 9], [1398, 13, 1075, 11], [1398, 14, 1075, 12], [1399, 6, 1076, 4, "backgroundColor"], [1399, 21, 1076, 19], [1399, 23, 1076, 21], [1399, 40, 1076, 38], [1400, 6, 1077, 4, "paddingHorizontal"], [1400, 23, 1077, 21], [1400, 25, 1077, 23], [1400, 27, 1077, 25], [1401, 6, 1078, 4, "paddingVertical"], [1401, 21, 1078, 19], [1401, 23, 1078, 21], [1401, 24, 1078, 22], [1402, 6, 1079, 4, "borderRadius"], [1402, 18, 1079, 16], [1402, 20, 1079, 18], [1403, 4, 1080, 2], [1403, 5, 1080, 3], [1404, 4, 1081, 2, "previewChipText"], [1404, 19, 1081, 17], [1404, 21, 1081, 19], [1405, 6, 1082, 4, "color"], [1405, 11, 1082, 9], [1405, 13, 1082, 11], [1405, 19, 1082, 17], [1406, 6, 1083, 4, "fontSize"], [1406, 14, 1083, 12], [1406, 16, 1083, 14], [1406, 18, 1083, 16], [1407, 6, 1084, 4, "fontWeight"], [1407, 16, 1084, 14], [1407, 18, 1084, 16], [1408, 4, 1085, 2], [1409, 2, 1086, 0], [1409, 3, 1086, 1], [1409, 4, 1086, 2], [1410, 2, 1086, 3], [1410, 6, 1086, 3, "_c"], [1410, 8, 1086, 3], [1411, 2, 1086, 3, "$RefreshReg$"], [1411, 14, 1086, 3], [1411, 15, 1086, 3, "_c"], [1411, 17, 1086, 3], [1412, 0, 1086, 3], [1412, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadMediaPipeFaceDetection", "Promise$argument_0", "detectFacesWithMediaPipe", "detectFacesHeuristic", "countSkinPixelsInRegion", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;qCGG;wBCG;ODM;GHE;mCKE;GLI;+BME;GNqC;kCOE;GPgB;qBQE;GRQ;8BSE;GT4B;2BUE;GVa;wBWE;GXiB;mCYG;wBRc,kCQ;GZoC;mCaE;wBTa;OSI;oFCkC;UDM;8BEW;SFwB;uDTa;sBYC,wBZ;OSC;Gbe;6BiBG;GjB6B;kCkBG;GlB8C;4BmBE;mBCmD;SDE;GnBO;uBqBE;GrBI;mCsBG;GtBM;YCE;GDK;oBuB2C;WvBG;yBwBC;WxBG;wByBC;WzBI;CD4L"}}, "type": "js/module"}]}