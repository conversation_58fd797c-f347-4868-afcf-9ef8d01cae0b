{"dependencies": [{"name": "../../../skia/types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 47, "index": 47}}], "key": "hnxlDT1tba4gQfvf2h/i6nte9KM=", "exportNames": ["*"]}}, {"name": "./Enum", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 48}, "end": {"line": 2, "column": 33, "index": 81}}], "key": "tOqSPbCBcVq8NtyuTPCNYQznSa0=", "exportNames": ["*"]}}, {"name": "./Transform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 82}, "end": {"line": 3, "column": 52, "index": 134}}], "key": "ZSN3Yq08DKgZLETHgj8HOJdf0n0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.transformOrigin = exports.processGradientProps = exports.processColor = exports.getRect = void 0;\n  var _types = require(_dependencyMap[0], \"../../../skia/types\");\n  var _Enum = require(_dependencyMap[1], \"./Enum\");\n  var _Transform = require(_dependencyMap[2], \"./Transform\");\n  const _worklet_5577550751885_init_data = {\n    code: \"function GradientJs1(origin,transform){return[{translateX:origin.x},{translateY:origin.y},...transform,{translateX:-origin.x},{translateY:-origin.y}];}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Gradient.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"GradientJs1\\\",\\\"origin\\\",\\\"transform\\\",\\\"translateX\\\",\\\"x\\\",\\\"translateY\\\",\\\"y\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Gradient.js\\\"],\\\"mappings\\\":\\\"AAG+B,QAAC,CAAAA,WAAQA,CAAAC,MAAA,CAASC,SAAK,EAGpD,MAAO,CAAC,CACNC,UAAU,CAAEF,MAAM,CAACG,CACrB,CAAC,CAAE,CACDC,UAAU,CAAEJ,MAAM,CAACK,CACrB,CAAC,CAAE,GAAGJ,SAAS,CAAE,CACfC,UAAU,CAAE,CAACF,MAAM,CAACG,CACtB,CAAC,CAAE,CACDC,UAAU,CAAE,CAACJ,MAAM,CAACK,CACtB,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const transformOrigin = exports.transformOrigin = function () {\n    const _e = [new global.Error(), 1, -27];\n    const GradientJs1 = function (origin, transform) {\n      return [{\n        translateX: origin.x\n      }, {\n        translateY: origin.y\n      }, ...transform, {\n        translateX: -origin.x\n      }, {\n        translateY: -origin.y\n      }];\n    };\n    GradientJs1.__closure = {};\n    GradientJs1.__workletHash = 5577550751885;\n    GradientJs1.__initData = _worklet_5577550751885_init_data;\n    GradientJs1.__stackDetails = _e;\n    return GradientJs1;\n  }();\n  const _worklet_7411685453151_init_data = {\n    code: \"function GradientJs2(Skia,color){if(typeof color===\\\"string\\\"||typeof color===\\\"number\\\"){return Skia.Color(color);}else if(Array.isArray(color)||color instanceof Float32Array){return color instanceof Float32Array?color:new Float32Array(color);}else{throw new Error(\\\"Invalid color type: \\\"+typeof color+\\\". Expected number, string, or array.\\\");}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Gradient.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"GradientJs2\\\",\\\"Skia\\\",\\\"color\\\",\\\"Color\\\",\\\"Array\\\",\\\"isArray\\\",\\\"Float32Array\\\",\\\"Error\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Gradient.js\\\"],\\\"mappings\\\":\\\"AAgB4B,QAAC,CAAAA,WAAMA,CAAAC,IAAU,CAAAC,KAAA,EAG3C,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC1D,MAAO,CAAAD,IAAI,CAACE,KAAK,CAACD,KAAK,CAAC,CAC1B,CAAC,IAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAIA,KAAK,WAAY,CAAAI,YAAY,CAAE,CAChE,MAAO,CAAAJ,KAAK,WAAY,CAAAI,YAAY,CAAGJ,KAAK,CAAG,GAAI,CAAAI,YAAY,CAACJ,KAAK,CAAC,CACxE,CAAC,IAAM,CACL,KAAM,IAAI,CAAAK,KAAK,wBAAwB,MAAO,CAAAL,KAAK,uCAAsC,CAAC,CAC5F,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processColor = exports.processColor = function () {\n    const _e = [new global.Error(), 1, -27];\n    const GradientJs2 = function (Skia, color) {\n      if (typeof color === \"string\" || typeof color === \"number\") {\n        return Skia.Color(color);\n      } else if (Array.isArray(color) || color instanceof Float32Array) {\n        return color instanceof Float32Array ? color : new Float32Array(color);\n      } else {\n        throw new Error(`Invalid color type: ${typeof color}. Expected number, string, or array.`);\n      }\n    };\n    GradientJs2.__closure = {};\n    GradientJs2.__workletHash = 7411685453151;\n    GradientJs2.__initData = _worklet_7411685453151_init_data;\n    GradientJs2.__stackDetails = _e;\n    return GradientJs2;\n  }();\n  const _worklet_354387835171_init_data = {\n    code: \"function GradientJs3(Skia,{colors:colors,positions:positions,mode:mode,flags:flags,...transform}){const{processTransformProps,processColor,TileMode,enumKey}=this.__closure;const localMatrix=Skia.Matrix();processTransformProps(localMatrix,transform);return{colors:colors.map(function(color){return processColor(Skia,color);}),positions:positions!==null&&positions!==void 0?positions:null,mode:TileMode[enumKey(mode!==null&&mode!==void 0?mode:\\\"clamp\\\")],flags:flags,localMatrix:localMatrix};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Gradient.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"GradientJs3\\\",\\\"Skia\\\",\\\"colors\\\",\\\"positions\\\",\\\"mode\\\",\\\"flags\\\",\\\"transform\\\",\\\"processTransformProps\\\",\\\"processColor\\\",\\\"TileMode\\\",\\\"enumKey\\\",\\\"__closure\\\",\\\"localMatrix\\\",\\\"Matrix\\\",\\\"map\\\",\\\"color\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Gradient.js\\\"],\\\"mappings\\\":\\\"AA2BoC,QAAC,CAAAA,WAAMA,CAAAC,IAAA,EACzCC,MAAM,CAANA,MAAM,CACNC,SAAS,CAATA,SAAS,CACTC,IAAI,CAAJA,IAAI,CACJC,KAAK,CAALA,KAAK,CACL,GAAGC,SACL,CAAC,CAAK,OAAAC,qBAAA,CAAAC,YAAA,CAAAC,QAAA,CAAAC,OAAA,OAAAC,SAAA,CAGJ,KAAM,CAAAC,WAAW,CAAGX,IAAI,CAACY,MAAM,CAAC,CAAC,CACjCN,qBAAqB,CAACK,WAAW,CAAEN,SAAS,CAAC,CAC7C,MAAO,CACLJ,MAAM,CAAEA,MAAM,CAACY,GAAG,CAAC,SAAAC,KAAK,QAAI,CAAAP,YAAY,CAACP,IAAI,CAAEc,KAAK,CAAC,GAAC,CACtDZ,SAAS,CAAEA,SAAS,GAAK,IAAI,EAAIA,SAAS,GAAK,IAAK,EAAC,CAAGA,SAAS,CAAG,IAAI,CACxEC,IAAI,CAAEK,QAAQ,CAACC,OAAO,CAACN,IAAI,GAAK,IAAI,EAAIA,IAAI,GAAK,IAAK,EAAC,CAAGA,IAAI,CAAG,OAAO,CAAC,CAAC,CAC1EC,KAAK,CAALA,KAAK,CACLO,WAAA,CAAAA,WACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processGradientProps = exports.processGradientProps = function () {\n    const _e = [new global.Error(), -5, -27];\n    const GradientJs3 = function (Skia, {\n      colors,\n      positions,\n      mode,\n      flags,\n      ...transform\n    }) {\n      const localMatrix = Skia.Matrix();\n      (0, _Transform.processTransformProps)(localMatrix, transform);\n      return {\n        colors: colors.map(color => processColor(Skia, color)),\n        positions: positions !== null && positions !== void 0 ? positions : null,\n        mode: _types.TileMode[(0, _Enum.enumKey)(mode !== null && mode !== void 0 ? mode : \"clamp\")],\n        flags,\n        localMatrix\n      };\n    };\n    GradientJs3.__closure = {\n      processTransformProps: _Transform.processTransformProps,\n      processColor,\n      TileMode: _types.TileMode,\n      enumKey: _Enum.enumKey\n    };\n    GradientJs3.__workletHash = 354387835171;\n    GradientJs3.__initData = _worklet_354387835171_init_data;\n    GradientJs3.__stackDetails = _e;\n    return GradientJs3;\n  }();\n  const _worklet_10921439172029_init_data = {\n    code: \"function GradientJs4(Skia,props){const{x:x,y:y,width:width,height:height}=props;if(props.rect){return props.rect;}else if(width!==undefined&&height!==undefined){return Skia.XYWHRect(x!==null&&x!==void 0?x:0,y!==null&&y!==void 0?y:0,width,height);}else{return undefined;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Gradient.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"GradientJs4\\\",\\\"Skia\\\",\\\"props\\\",\\\"x\\\",\\\"y\\\",\\\"width\\\",\\\"height\\\",\\\"rect\\\",\\\"undefined\\\",\\\"XYWHRect\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Gradient.js\\\"],\\\"mappings\\\":\\\"AA8CuB,QAAC,CAAAA,WAAMA,CAAAC,IAAU,CAAAC,KAAA,EAGtC,KAAM,CACJC,CAAC,CAADA,CAAC,CACDC,CAAC,CAADA,CAAC,CACDC,KAAK,CAALA,KAAK,CACLC,MAAA,CAAAA,MACF,CAAC,CAAGJ,KAAK,CACT,GAAIA,KAAK,CAACK,IAAI,CAAE,CACd,MAAO,CAAAL,KAAK,CAACK,IAAI,CACnB,CAAC,IAAM,IAAIF,KAAK,GAAKG,SAAS,EAAIF,MAAM,GAAKE,SAAS,CAAE,CACtD,MAAO,CAAAP,IAAI,CAACQ,QAAQ,CAACN,CAAC,GAAK,IAAI,EAAIA,CAAC,GAAK,IAAK,EAAC,CAAGA,CAAC,CAAG,CAAC,CAAEC,CAAC,GAAK,IAAI,EAAIA,CAAC,GAAK,IAAK,EAAC,CAAGA,CAAC,CAAG,CAAC,CAAEC,KAAK,CAAEC,MAAM,CAAC,CAC7G,CAAC,IAAM,CACL,MAAO,CAAAE,SAAS,CAClB,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const getRect = exports.getRect = function () {\n    const _e = [new global.Error(), 1, -27];\n    const GradientJs4 = function (Skia, props) {\n      const {\n        x,\n        y,\n        width,\n        height\n      } = props;\n      if (props.rect) {\n        return props.rect;\n      } else if (width !== undefined && height !== undefined) {\n        return Skia.XYWHRect(x !== null && x !== void 0 ? x : 0, y !== null && y !== void 0 ? y : 0, width, height);\n      } else {\n        return undefined;\n      }\n    };\n    GradientJs4.__closure = {};\n    GradientJs4.__workletHash = 10921439172029;\n    GradientJs4.__initData = _worklet_10921439172029_init_data;\n    GradientJs4.__stackDetails = _e;\n    return GradientJs4;\n  }();\n});", "lineCount": 122, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_types"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Enum"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_Transform"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 3, 52], [9, 8, 3, 52, "_worklet_5577550751885_init_data"], [9, 40, 3, 52], [10, 4, 3, 52, "code"], [10, 8, 3, 52], [11, 4, 3, 52, "location"], [11, 12, 3, 52], [12, 4, 3, 52, "sourceMap"], [12, 13, 3, 52], [13, 4, 3, 52, "version"], [13, 11, 3, 52], [14, 2, 3, 52], [15, 2, 4, 7], [15, 8, 4, 13, "transform<PERSON><PERSON>in"], [15, 23, 4, 28], [15, 26, 4, 28, "exports"], [15, 33, 4, 28], [15, 34, 4, 28, "transform<PERSON><PERSON>in"], [15, 49, 4, 28], [15, 52, 4, 31], [16, 4, 4, 31], [16, 10, 4, 31, "_e"], [16, 12, 4, 31], [16, 20, 4, 31, "global"], [16, 26, 4, 31], [16, 27, 4, 31, "Error"], [16, 32, 4, 31], [17, 4, 4, 31], [17, 10, 4, 31, "GradientJs1"], [17, 21, 4, 31], [17, 33, 4, 31, "GradientJs1"], [17, 34, 4, 32, "origin"], [17, 40, 4, 38], [17, 42, 4, 40, "transform"], [17, 51, 4, 49], [17, 53, 4, 54], [18, 6, 7, 2], [18, 13, 7, 9], [18, 14, 7, 10], [19, 8, 8, 4, "translateX"], [19, 18, 8, 14], [19, 20, 8, 16, "origin"], [19, 26, 8, 22], [19, 27, 8, 23, "x"], [20, 6, 9, 2], [20, 7, 9, 3], [20, 9, 9, 5], [21, 8, 10, 4, "translateY"], [21, 18, 10, 14], [21, 20, 10, 16, "origin"], [21, 26, 10, 22], [21, 27, 10, 23, "y"], [22, 6, 11, 2], [22, 7, 11, 3], [22, 9, 11, 5], [22, 12, 11, 8, "transform"], [22, 21, 11, 17], [22, 23, 11, 19], [23, 8, 12, 4, "translateX"], [23, 18, 12, 14], [23, 20, 12, 16], [23, 21, 12, 17, "origin"], [23, 27, 12, 23], [23, 28, 12, 24, "x"], [24, 6, 13, 2], [24, 7, 13, 3], [24, 9, 13, 5], [25, 8, 14, 4, "translateY"], [25, 18, 14, 14], [25, 20, 14, 16], [25, 21, 14, 17, "origin"], [25, 27, 14, 23], [25, 28, 14, 24, "y"], [26, 6, 15, 2], [26, 7, 15, 3], [26, 8, 15, 4], [27, 4, 16, 0], [27, 5, 16, 1], [28, 4, 16, 1, "GradientJs1"], [28, 15, 16, 1], [28, 16, 16, 1, "__closure"], [28, 25, 16, 1], [29, 4, 16, 1, "GradientJs1"], [29, 15, 16, 1], [29, 16, 16, 1, "__workletHash"], [29, 29, 16, 1], [30, 4, 16, 1, "GradientJs1"], [30, 15, 16, 1], [30, 16, 16, 1, "__initData"], [30, 26, 16, 1], [30, 29, 16, 1, "_worklet_5577550751885_init_data"], [30, 61, 16, 1], [31, 4, 16, 1, "GradientJs1"], [31, 15, 16, 1], [31, 16, 16, 1, "__stackDetails"], [31, 30, 16, 1], [31, 33, 16, 1, "_e"], [31, 35, 16, 1], [32, 4, 16, 1], [32, 11, 16, 1, "GradientJs1"], [32, 22, 16, 1], [33, 2, 16, 1], [33, 3, 4, 31], [33, 5, 16, 1], [34, 2, 16, 2], [34, 8, 16, 2, "_worklet_7411685453151_init_data"], [34, 40, 16, 2], [35, 4, 16, 2, "code"], [35, 8, 16, 2], [36, 4, 16, 2, "location"], [36, 12, 16, 2], [37, 4, 16, 2, "sourceMap"], [37, 13, 16, 2], [38, 4, 16, 2, "version"], [38, 11, 16, 2], [39, 2, 16, 2], [40, 2, 17, 7], [40, 8, 17, 13, "processColor"], [40, 20, 17, 25], [40, 23, 17, 25, "exports"], [40, 30, 17, 25], [40, 31, 17, 25, "processColor"], [40, 43, 17, 25], [40, 46, 17, 28], [41, 4, 17, 28], [41, 10, 17, 28, "_e"], [41, 12, 17, 28], [41, 20, 17, 28, "global"], [41, 26, 17, 28], [41, 27, 17, 28, "Error"], [41, 32, 17, 28], [42, 4, 17, 28], [42, 10, 17, 28, "GradientJs2"], [42, 21, 17, 28], [42, 33, 17, 28, "GradientJs2"], [42, 34, 17, 29, "Skia"], [42, 38, 17, 33], [42, 40, 17, 35, "color"], [42, 45, 17, 40], [42, 47, 17, 45], [43, 6, 20, 2], [43, 10, 20, 6], [43, 17, 20, 13, "color"], [43, 22, 20, 18], [43, 27, 20, 23], [43, 35, 20, 31], [43, 39, 20, 35], [43, 46, 20, 42, "color"], [43, 51, 20, 47], [43, 56, 20, 52], [43, 64, 20, 60], [43, 66, 20, 62], [44, 8, 21, 4], [44, 15, 21, 11, "Skia"], [44, 19, 21, 15], [44, 20, 21, 16, "Color"], [44, 25, 21, 21], [44, 26, 21, 22, "color"], [44, 31, 21, 27], [44, 32, 21, 28], [45, 6, 22, 2], [45, 7, 22, 3], [45, 13, 22, 9], [45, 17, 22, 13, "Array"], [45, 22, 22, 18], [45, 23, 22, 19, "isArray"], [45, 30, 22, 26], [45, 31, 22, 27, "color"], [45, 36, 22, 32], [45, 37, 22, 33], [45, 41, 22, 37, "color"], [45, 46, 22, 42], [45, 58, 22, 54, "Float32Array"], [45, 70, 22, 66], [45, 72, 22, 68], [46, 8, 23, 4], [46, 15, 23, 11, "color"], [46, 20, 23, 16], [46, 32, 23, 28, "Float32Array"], [46, 44, 23, 40], [46, 47, 23, 43, "color"], [46, 52, 23, 48], [46, 55, 23, 51], [46, 59, 23, 55, "Float32Array"], [46, 71, 23, 67], [46, 72, 23, 68, "color"], [46, 77, 23, 73], [46, 78, 23, 74], [47, 6, 24, 2], [47, 7, 24, 3], [47, 13, 24, 9], [48, 8, 25, 4], [48, 14, 25, 10], [48, 18, 25, 14, "Error"], [48, 23, 25, 19], [48, 24, 25, 20], [48, 47, 25, 43], [48, 54, 25, 50, "color"], [48, 59, 25, 55], [48, 97, 25, 93], [48, 98, 25, 94], [49, 6, 26, 2], [50, 4, 27, 0], [50, 5, 27, 1], [51, 4, 27, 1, "GradientJs2"], [51, 15, 27, 1], [51, 16, 27, 1, "__closure"], [51, 25, 27, 1], [52, 4, 27, 1, "GradientJs2"], [52, 15, 27, 1], [52, 16, 27, 1, "__workletHash"], [52, 29, 27, 1], [53, 4, 27, 1, "GradientJs2"], [53, 15, 27, 1], [53, 16, 27, 1, "__initData"], [53, 26, 27, 1], [53, 29, 27, 1, "_worklet_7411685453151_init_data"], [53, 61, 27, 1], [54, 4, 27, 1, "GradientJs2"], [54, 15, 27, 1], [54, 16, 27, 1, "__stackDetails"], [54, 30, 27, 1], [54, 33, 27, 1, "_e"], [54, 35, 27, 1], [55, 4, 27, 1], [55, 11, 27, 1, "GradientJs2"], [55, 22, 27, 1], [56, 2, 27, 1], [56, 3, 17, 28], [56, 5, 27, 1], [57, 2, 27, 2], [57, 8, 27, 2, "_worklet_354387835171_init_data"], [57, 39, 27, 2], [58, 4, 27, 2, "code"], [58, 8, 27, 2], [59, 4, 27, 2, "location"], [59, 12, 27, 2], [60, 4, 27, 2, "sourceMap"], [60, 13, 27, 2], [61, 4, 27, 2, "version"], [61, 11, 27, 2], [62, 2, 27, 2], [63, 2, 28, 7], [63, 8, 28, 13, "processGradientProps"], [63, 28, 28, 33], [63, 31, 28, 33, "exports"], [63, 38, 28, 33], [63, 39, 28, 33, "processGradientProps"], [63, 59, 28, 33], [63, 62, 28, 36], [64, 4, 28, 36], [64, 10, 28, 36, "_e"], [64, 12, 28, 36], [64, 20, 28, 36, "global"], [64, 26, 28, 36], [64, 27, 28, 36, "Error"], [64, 32, 28, 36], [65, 4, 28, 36], [65, 10, 28, 36, "GradientJs3"], [65, 21, 28, 36], [65, 33, 28, 36, "GradientJs3"], [65, 34, 28, 37, "Skia"], [65, 38, 28, 41], [65, 40, 28, 43], [66, 6, 29, 2, "colors"], [66, 12, 29, 8], [67, 6, 30, 2, "positions"], [67, 15, 30, 11], [68, 6, 31, 2, "mode"], [68, 10, 31, 6], [69, 6, 32, 2, "flags"], [69, 11, 32, 7], [70, 6, 33, 2], [70, 9, 33, 5, "transform"], [71, 4, 34, 0], [71, 5, 34, 1], [71, 7, 34, 6], [72, 6, 37, 2], [72, 12, 37, 8, "localMatrix"], [72, 23, 37, 19], [72, 26, 37, 22, "Skia"], [72, 30, 37, 26], [72, 31, 37, 27, "Matrix"], [72, 37, 37, 33], [72, 38, 37, 34], [72, 39, 37, 35], [73, 6, 38, 2], [73, 10, 38, 2, "processTransformProps"], [73, 42, 38, 23], [73, 44, 38, 24, "localMatrix"], [73, 55, 38, 35], [73, 57, 38, 37, "transform"], [73, 66, 38, 46], [73, 67, 38, 47], [74, 6, 39, 2], [74, 13, 39, 9], [75, 8, 40, 4, "colors"], [75, 14, 40, 10], [75, 16, 40, 12, "colors"], [75, 22, 40, 18], [75, 23, 40, 19, "map"], [75, 26, 40, 22], [75, 27, 40, 23, "color"], [75, 32, 40, 28], [75, 36, 40, 32, "processColor"], [75, 48, 40, 44], [75, 49, 40, 45, "Skia"], [75, 53, 40, 49], [75, 55, 40, 51, "color"], [75, 60, 40, 56], [75, 61, 40, 57], [75, 62, 40, 58], [76, 8, 41, 4, "positions"], [76, 17, 41, 13], [76, 19, 41, 15, "positions"], [76, 28, 41, 24], [76, 33, 41, 29], [76, 37, 41, 33], [76, 41, 41, 37, "positions"], [76, 50, 41, 46], [76, 55, 41, 51], [76, 60, 41, 56], [76, 61, 41, 57], [76, 64, 41, 60, "positions"], [76, 73, 41, 69], [76, 76, 41, 72], [76, 80, 41, 76], [77, 8, 42, 4, "mode"], [77, 12, 42, 8], [77, 14, 42, 10, "TileMode"], [77, 29, 42, 18], [77, 30, 42, 19], [77, 34, 42, 19, "<PERSON><PERSON><PERSON><PERSON>"], [77, 47, 42, 26], [77, 49, 42, 27, "mode"], [77, 53, 42, 31], [77, 58, 42, 36], [77, 62, 42, 40], [77, 66, 42, 44, "mode"], [77, 70, 42, 48], [77, 75, 42, 53], [77, 80, 42, 58], [77, 81, 42, 59], [77, 84, 42, 62, "mode"], [77, 88, 42, 66], [77, 91, 42, 69], [77, 98, 42, 76], [77, 99, 42, 77], [77, 100, 42, 78], [78, 8, 43, 4, "flags"], [78, 13, 43, 9], [79, 8, 44, 4, "localMatrix"], [80, 6, 45, 2], [80, 7, 45, 3], [81, 4, 46, 0], [81, 5, 46, 1], [82, 4, 46, 1, "GradientJs3"], [82, 15, 46, 1], [82, 16, 46, 1, "__closure"], [82, 25, 46, 1], [83, 6, 46, 1, "processTransformProps"], [83, 27, 46, 1], [83, 29, 38, 2, "processTransformProps"], [83, 61, 38, 23], [84, 6, 38, 23, "processColor"], [84, 18, 38, 23], [85, 6, 38, 23, "TileMode"], [85, 14, 38, 23], [85, 16, 42, 10, "TileMode"], [85, 31, 42, 18], [86, 6, 42, 18, "<PERSON><PERSON><PERSON><PERSON>"], [86, 13, 42, 18], [86, 15, 42, 19, "<PERSON><PERSON><PERSON><PERSON>"], [87, 4, 42, 26], [88, 4, 42, 26, "GradientJs3"], [88, 15, 42, 26], [88, 16, 42, 26, "__workletHash"], [88, 29, 42, 26], [89, 4, 42, 26, "GradientJs3"], [89, 15, 42, 26], [89, 16, 42, 26, "__initData"], [89, 26, 42, 26], [89, 29, 42, 26, "_worklet_354387835171_init_data"], [89, 60, 42, 26], [90, 4, 42, 26, "GradientJs3"], [90, 15, 42, 26], [90, 16, 42, 26, "__stackDetails"], [90, 30, 42, 26], [90, 33, 42, 26, "_e"], [90, 35, 42, 26], [91, 4, 42, 26], [91, 11, 42, 26, "GradientJs3"], [91, 22, 42, 26], [92, 2, 42, 26], [92, 3, 28, 36], [92, 5, 46, 1], [93, 2, 46, 2], [93, 8, 46, 2, "_worklet_10921439172029_init_data"], [93, 41, 46, 2], [94, 4, 46, 2, "code"], [94, 8, 46, 2], [95, 4, 46, 2, "location"], [95, 12, 46, 2], [96, 4, 46, 2, "sourceMap"], [96, 13, 46, 2], [97, 4, 46, 2, "version"], [97, 11, 46, 2], [98, 2, 46, 2], [99, 2, 47, 7], [99, 8, 47, 13, "getRect"], [99, 15, 47, 20], [99, 18, 47, 20, "exports"], [99, 25, 47, 20], [99, 26, 47, 20, "getRect"], [99, 33, 47, 20], [99, 36, 47, 23], [100, 4, 47, 23], [100, 10, 47, 23, "_e"], [100, 12, 47, 23], [100, 20, 47, 23, "global"], [100, 26, 47, 23], [100, 27, 47, 23, "Error"], [100, 32, 47, 23], [101, 4, 47, 23], [101, 10, 47, 23, "GradientJs4"], [101, 21, 47, 23], [101, 33, 47, 23, "GradientJs4"], [101, 34, 47, 24, "Skia"], [101, 38, 47, 28], [101, 40, 47, 30, "props"], [101, 45, 47, 35], [101, 47, 47, 40], [102, 6, 50, 2], [102, 12, 50, 8], [103, 8, 51, 4, "x"], [103, 9, 51, 5], [104, 8, 52, 4, "y"], [104, 9, 52, 5], [105, 8, 53, 4, "width"], [105, 13, 53, 9], [106, 8, 54, 4, "height"], [107, 6, 55, 2], [107, 7, 55, 3], [107, 10, 55, 6, "props"], [107, 15, 55, 11], [108, 6, 56, 2], [108, 10, 56, 6, "props"], [108, 15, 56, 11], [108, 16, 56, 12, "rect"], [108, 20, 56, 16], [108, 22, 56, 18], [109, 8, 57, 4], [109, 15, 57, 11, "props"], [109, 20, 57, 16], [109, 21, 57, 17, "rect"], [109, 25, 57, 21], [110, 6, 58, 2], [110, 7, 58, 3], [110, 13, 58, 9], [110, 17, 58, 13, "width"], [110, 22, 58, 18], [110, 27, 58, 23, "undefined"], [110, 36, 58, 32], [110, 40, 58, 36, "height"], [110, 46, 58, 42], [110, 51, 58, 47, "undefined"], [110, 60, 58, 56], [110, 62, 58, 58], [111, 8, 59, 4], [111, 15, 59, 11, "Skia"], [111, 19, 59, 15], [111, 20, 59, 16, "XYWHRect"], [111, 28, 59, 24], [111, 29, 59, 25, "x"], [111, 30, 59, 26], [111, 35, 59, 31], [111, 39, 59, 35], [111, 43, 59, 39, "x"], [111, 44, 59, 40], [111, 49, 59, 45], [111, 54, 59, 50], [111, 55, 59, 51], [111, 58, 59, 54, "x"], [111, 59, 59, 55], [111, 62, 59, 58], [111, 63, 59, 59], [111, 65, 59, 61, "y"], [111, 66, 59, 62], [111, 71, 59, 67], [111, 75, 59, 71], [111, 79, 59, 75, "y"], [111, 80, 59, 76], [111, 85, 59, 81], [111, 90, 59, 86], [111, 91, 59, 87], [111, 94, 59, 90, "y"], [111, 95, 59, 91], [111, 98, 59, 94], [111, 99, 59, 95], [111, 101, 59, 97, "width"], [111, 106, 59, 102], [111, 108, 59, 104, "height"], [111, 114, 59, 110], [111, 115, 59, 111], [112, 6, 60, 2], [112, 7, 60, 3], [112, 13, 60, 9], [113, 8, 61, 4], [113, 15, 61, 11, "undefined"], [113, 24, 61, 20], [114, 6, 62, 2], [115, 4, 63, 0], [115, 5, 63, 1], [116, 4, 63, 1, "GradientJs4"], [116, 15, 63, 1], [116, 16, 63, 1, "__closure"], [116, 25, 63, 1], [117, 4, 63, 1, "GradientJs4"], [117, 15, 63, 1], [117, 16, 63, 1, "__workletHash"], [117, 29, 63, 1], [118, 4, 63, 1, "GradientJs4"], [118, 15, 63, 1], [118, 16, 63, 1, "__initData"], [118, 26, 63, 1], [118, 29, 63, 1, "_worklet_10921439172029_init_data"], [118, 62, 63, 1], [119, 4, 63, 1, "GradientJs4"], [119, 15, 63, 1], [119, 16, 63, 1, "__stackDetails"], [119, 30, 63, 1], [119, 33, 63, 1, "_e"], [119, 35, 63, 1], [120, 4, 63, 1], [120, 11, 63, 1, "GradientJs4"], [120, 22, 63, 1], [121, 2, 63, 1], [121, 3, 47, 23], [121, 5, 63, 1], [122, 0, 63, 2], [122, 3]], "functionMap": {"names": ["<global>", "transform<PERSON><PERSON>in", "processColor", "processGradientProps", "colors.map$argument_0", "getRect"], "mappings": "AAA;+BCG;CDY;4BEC;CFU;oCGC;uBCY,kCD;CHM;uBKC;CLgB"}}, "type": "js/module"}]}