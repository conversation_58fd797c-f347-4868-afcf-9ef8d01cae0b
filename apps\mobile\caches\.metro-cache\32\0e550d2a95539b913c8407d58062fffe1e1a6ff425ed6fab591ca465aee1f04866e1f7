{"dependencies": [{"name": "./JsiSkVertices", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 48, "index": 48}}], "key": "v7t1sO52wRZjHCJe3y6WxuPRi1Q=", "exportNames": ["*"]}}, {"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 49}, "end": {"line": 2, "column": 33, "index": 82}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MakeVertices = void 0;\n  var _JsiSkVertices = require(_dependencyMap[0], \"./JsiSkVertices\");\n  var _Host = require(_dependencyMap[1], \"./Host\");\n  const concat = (...arrays) => {\n    let totalLength = 0;\n    for (const arr of arrays) {\n      totalLength += arr.length;\n    }\n    const result = new Float32Array(totalLength);\n    let offset = 0;\n    for (const arr of arrays) {\n      result.set(arr, offset);\n      offset += arr.length;\n    }\n    return result;\n  };\n  const MakeVertices = (CanvasKit, mode, positions, textureCoordinates, colors, indices, isVolatile) => new _JsiSkVertices.JsiSkVertices(CanvasKit, CanvasKit.MakeVertices((0, _Host.getEnum)(CanvasKit, \"VertexMode\", mode), positions.map(({\n    x,\n    y\n  }) => [x, y]).flat(), (textureCoordinates || []).map(({\n    x,\n    y\n  }) => [x, y]).flat(), !colors ? null : colors.reduce((a, c) => concat(a, c)), indices, isVolatile));\n  exports.MakeVertices = MakeVertices;\n});", "lineCount": 29, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_JsiSkVertices"], [6, 20, 1, 0], [6, 23, 1, 0, "require"], [6, 30, 1, 0], [6, 31, 1, 0, "_dependencyMap"], [6, 45, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Host"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 8, 3, 6, "concat"], [8, 14, 3, 12], [8, 17, 3, 15, "concat"], [8, 18, 3, 16], [8, 21, 3, 19, "arrays"], [8, 27, 3, 25], [8, 32, 3, 30], [9, 4, 4, 2], [9, 8, 4, 6, "totalLength"], [9, 19, 4, 17], [9, 22, 4, 20], [9, 23, 4, 21], [10, 4, 5, 2], [10, 9, 5, 7], [10, 15, 5, 13, "arr"], [10, 18, 5, 16], [10, 22, 5, 20, "arrays"], [10, 28, 5, 26], [10, 30, 5, 28], [11, 6, 6, 4, "totalLength"], [11, 17, 6, 15], [11, 21, 6, 19, "arr"], [11, 24, 6, 22], [11, 25, 6, 23, "length"], [11, 31, 6, 29], [12, 4, 7, 2], [13, 4, 8, 2], [13, 10, 8, 8, "result"], [13, 16, 8, 14], [13, 19, 8, 17], [13, 23, 8, 21, "Float32Array"], [13, 35, 8, 33], [13, 36, 8, 34, "totalLength"], [13, 47, 8, 45], [13, 48, 8, 46], [14, 4, 9, 2], [14, 8, 9, 6, "offset"], [14, 14, 9, 12], [14, 17, 9, 15], [14, 18, 9, 16], [15, 4, 10, 2], [15, 9, 10, 7], [15, 15, 10, 13, "arr"], [15, 18, 10, 16], [15, 22, 10, 20, "arrays"], [15, 28, 10, 26], [15, 30, 10, 28], [16, 6, 11, 4, "result"], [16, 12, 11, 10], [16, 13, 11, 11, "set"], [16, 16, 11, 14], [16, 17, 11, 15, "arr"], [16, 20, 11, 18], [16, 22, 11, 20, "offset"], [16, 28, 11, 26], [16, 29, 11, 27], [17, 6, 12, 4, "offset"], [17, 12, 12, 10], [17, 16, 12, 14, "arr"], [17, 19, 12, 17], [17, 20, 12, 18, "length"], [17, 26, 12, 24], [18, 4, 13, 2], [19, 4, 14, 2], [19, 11, 14, 9, "result"], [19, 17, 14, 15], [20, 2, 15, 0], [20, 3, 15, 1], [21, 2, 16, 7], [21, 8, 16, 13, "MakeVertices"], [21, 20, 16, 25], [21, 23, 16, 28, "MakeVertices"], [21, 24, 16, 29, "CanvasKit"], [21, 33, 16, 38], [21, 35, 16, 40, "mode"], [21, 39, 16, 44], [21, 41, 16, 46, "positions"], [21, 50, 16, 55], [21, 52, 16, 57, "textureCoordinates"], [21, 70, 16, 75], [21, 72, 16, 77, "colors"], [21, 78, 16, 83], [21, 80, 16, 85, "indices"], [21, 87, 16, 92], [21, 89, 16, 94, "isVolatile"], [21, 99, 16, 104], [21, 104, 16, 109], [21, 108, 16, 113, "JsiSkVertices"], [21, 136, 16, 126], [21, 137, 16, 127, "CanvasKit"], [21, 146, 16, 136], [21, 148, 16, 138, "CanvasKit"], [21, 157, 16, 147], [21, 158, 16, 148, "MakeVertices"], [21, 170, 16, 160], [21, 171, 16, 161], [21, 175, 16, 161, "getEnum"], [21, 188, 16, 168], [21, 190, 16, 169, "CanvasKit"], [21, 199, 16, 178], [21, 201, 16, 180], [21, 213, 16, 192], [21, 215, 16, 194, "mode"], [21, 219, 16, 198], [21, 220, 16, 199], [21, 222, 16, 201, "positions"], [21, 231, 16, 210], [21, 232, 16, 211, "map"], [21, 235, 16, 214], [21, 236, 16, 215], [21, 237, 16, 216], [22, 4, 17, 2, "x"], [22, 5, 17, 3], [23, 4, 18, 2, "y"], [24, 2, 19, 0], [24, 3, 19, 1], [24, 8, 19, 6], [24, 9, 19, 7, "x"], [24, 10, 19, 8], [24, 12, 19, 10, "y"], [24, 13, 19, 11], [24, 14, 19, 12], [24, 15, 19, 13], [24, 16, 19, 14, "flat"], [24, 20, 19, 18], [24, 21, 19, 19], [24, 22, 19, 20], [24, 24, 19, 22], [24, 25, 19, 23, "textureCoordinates"], [24, 43, 19, 41], [24, 47, 19, 45], [24, 49, 19, 47], [24, 51, 19, 49, "map"], [24, 54, 19, 52], [24, 55, 19, 53], [24, 56, 19, 54], [25, 4, 20, 2, "x"], [25, 5, 20, 3], [26, 4, 21, 2, "y"], [27, 2, 22, 0], [27, 3, 22, 1], [27, 8, 22, 6], [27, 9, 22, 7, "x"], [27, 10, 22, 8], [27, 12, 22, 10, "y"], [27, 13, 22, 11], [27, 14, 22, 12], [27, 15, 22, 13], [27, 16, 22, 14, "flat"], [27, 20, 22, 18], [27, 21, 22, 19], [27, 22, 22, 20], [27, 24, 22, 22], [27, 25, 22, 23, "colors"], [27, 31, 22, 29], [27, 34, 22, 32], [27, 38, 22, 36], [27, 41, 22, 39, "colors"], [27, 47, 22, 45], [27, 48, 22, 46, "reduce"], [27, 54, 22, 52], [27, 55, 22, 53], [27, 56, 22, 54, "a"], [27, 57, 22, 55], [27, 59, 22, 57, "c"], [27, 60, 22, 58], [27, 65, 22, 63, "concat"], [27, 71, 22, 69], [27, 72, 22, 70, "a"], [27, 73, 22, 71], [27, 75, 22, 73, "c"], [27, 76, 22, 74], [27, 77, 22, 75], [27, 78, 22, 76], [27, 80, 22, 78, "indices"], [27, 87, 22, 85], [27, 89, 22, 87, "isVolatile"], [27, 99, 22, 97], [27, 100, 22, 98], [27, 101, 22, 99], [28, 2, 22, 100, "exports"], [28, 9, 22, 100], [28, 10, 22, 100, "MakeVertices"], [28, 22, 22, 100], [28, 25, 22, 100, "MakeVertices"], [28, 37, 22, 100], [29, 0, 22, 100], [29, 3]], "functionMap": {"names": ["<global>", "concat", "MakeVertices", "positions.map$argument_0", "map$argument_0", "colors.reduce$argument_0"], "mappings": "AAA;eCE;CDY;4BEC,2LC;YDG,yCE;YFG,yCG,sBH,wBF"}}, "type": "js/module"}]}