{"dependencies": [{"name": "../../../skia/types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 55, "index": 55}}], "key": "hnxlDT1tba4gQfvf2h/i6nte9KM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.processTransformProps2 = exports.processTransformProps = void 0;\n  var _types = require(_dependencyMap[0], \"../../../skia/types\");\n  const _worklet_6997418011774_init_data = {\n    code: \"function TransformJs1(m3,props){const{processTransform}=this.__closure;const{transform:transform,origin:origin,matrix:matrix}=props;if(matrix){if(origin){m3.translate(origin.x,origin.y);m3.concat(matrix);m3.translate(-origin.x,-origin.y);}else{m3.concat(matrix);}}else if(transform){if(origin){m3.translate(origin.x,origin.y);}processTransform(m3,transform);if(origin){m3.translate(-origin.x,-origin.y);}}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Transform.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"TransformJs1\\\",\\\"m3\\\",\\\"props\\\",\\\"processTransform\\\",\\\"__closure\\\",\\\"transform\\\",\\\"origin\\\",\\\"matrix\\\",\\\"translate\\\",\\\"x\\\",\\\"y\\\",\\\"concat\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Transform.js\\\"],\\\"mappings\\\":\\\"AACqC,QAAC,CAAAA,YAASA,CAAKC,EAAA,CAAAC,KAAA,QAAAC,gBAAA,OAAAC,SAAA,CAGlD,KAAM,CACJC,SAAS,CAATA,SAAS,CACTC,MAAM,CAANA,MAAM,CACNC,MAAA,CAAAA,MACF,CAAC,CAAGL,KAAK,CACT,GAAIK,MAAM,CAAE,CACV,GAAID,MAAM,CAAE,CACVL,EAAE,CAACO,SAAS,CAACF,MAAM,CAACG,CAAC,CAAEH,MAAM,CAACI,CAAC,CAAC,CAChCT,EAAE,CAACU,MAAM,CAACJ,MAAM,CAAC,CACjBN,EAAE,CAACO,SAAS,CAAC,CAACF,MAAM,CAACG,CAAC,CAAE,CAACH,MAAM,CAACI,CAAC,CAAC,CACpC,CAAC,IAAM,CACLT,EAAE,CAACU,MAAM,CAACJ,MAAM,CAAC,CACnB,CACF,CAAC,IAAM,IAAIF,SAAS,CAAE,CACpB,GAAIC,MAAM,CAAE,CACVL,EAAE,CAACO,SAAS,CAACF,MAAM,CAACG,CAAC,CAAEH,MAAM,CAACI,CAAC,CAAC,CAClC,CACAP,gBAAgB,CAACF,EAAE,CAAEI,SAAS,CAAC,CAC/B,GAAIC,MAAM,CAAE,CACVL,EAAE,CAACO,SAAS,CAAC,CAACF,MAAM,CAACG,CAAC,CAAE,CAACH,MAAM,CAACI,CAAC,CAAC,CACpC,CACF,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processTransformProps = exports.processTransformProps = function () {\n    const _e = [new global.Error(), -2, -27];\n    const TransformJs1 = function (m3, props) {\n      const {\n        transform,\n        origin,\n        matrix\n      } = props;\n      if (matrix) {\n        if (origin) {\n          m3.translate(origin.x, origin.y);\n          m3.concat(matrix);\n          m3.translate(-origin.x, -origin.y);\n        } else {\n          m3.concat(matrix);\n        }\n      } else if (transform) {\n        if (origin) {\n          m3.translate(origin.x, origin.y);\n        }\n        (0, _types.processTransform)(m3, transform);\n        if (origin) {\n          m3.translate(-origin.x, -origin.y);\n        }\n      }\n    };\n    TransformJs1.__closure = {\n      processTransform: _types.processTransform\n    };\n    TransformJs1.__workletHash = 6997418011774;\n    TransformJs1.__initData = _worklet_6997418011774_init_data;\n    TransformJs1.__stackDetails = _e;\n    return TransformJs1;\n  }();\n  const _worklet_5650771643257_init_data = {\n    code: \"function TransformJs2(Skia,props){const{processTransform}=this.__closure;const{transform:transform,origin:origin,matrix:matrix}=props;if(matrix){const m3=Skia.Matrix();if(origin){m3.translate(origin.x,origin.y);m3.concat(matrix);m3.translate(-origin.x,-origin.y);}else{m3.concat(matrix);}return m3;}else if(transform){const m3=Skia.Matrix();if(origin){m3.translate(origin.x,origin.y);}processTransform(m3,transform);if(origin){m3.translate(-origin.x,-origin.y);}return m3;}return null;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Transform.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"TransformJs2\\\",\\\"Skia\\\",\\\"props\\\",\\\"processTransform\\\",\\\"__closure\\\",\\\"transform\\\",\\\"origin\\\",\\\"matrix\\\",\\\"m3\\\",\\\"Matrix\\\",\\\"translate\\\",\\\"x\\\",\\\"y\\\",\\\"concat\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Transform.js\\\"],\\\"mappings\\\":\\\"AA2BsC,QAAC,CAAAA,YAAMA,CAAKC,IAAK,CAAAC,KAAA,QAAAC,gBAAA,OAAAC,SAAA,CAGrD,KAAM,CACJC,SAAS,CAATA,SAAS,CACTC,MAAM,CAANA,MAAM,CACNC,MAAA,CAAAA,MACF,CAAC,CAAGL,KAAK,CACT,GAAIK,MAAM,CAAE,CACV,KAAM,CAAAC,EAAE,CAAGP,IAAI,CAACQ,MAAM,CAAC,CAAC,CACxB,GAAIH,MAAM,CAAE,CACVE,EAAE,CAACE,SAAS,CAACJ,MAAM,CAACK,CAAC,CAAEL,MAAM,CAACM,CAAC,CAAC,CAChCJ,EAAE,CAACK,MAAM,CAACN,MAAM,CAAC,CACjBC,EAAE,CAACE,SAAS,CAAC,CAACJ,MAAM,CAACK,CAAC,CAAE,CAACL,MAAM,CAACM,CAAC,CAAC,CACpC,CAAC,IAAM,CACLJ,EAAE,CAACK,MAAM,CAACN,MAAM,CAAC,CACnB,CACA,MAAO,CAAAC,EAAE,CACX,CAAC,IAAM,IAAIH,SAAS,CAAE,CACpB,KAAM,CAAAG,EAAE,CAAGP,IAAI,CAACQ,MAAM,CAAC,CAAC,CACxB,GAAIH,MAAM,CAAE,CACVE,EAAE,CAACE,SAAS,CAACJ,MAAM,CAACK,CAAC,CAAEL,MAAM,CAACM,CAAC,CAAC,CAClC,CACAT,gBAAgB,CAACK,EAAE,CAAEH,SAAS,CAAC,CAC/B,GAAIC,MAAM,CAAE,CACVE,EAAE,CAACE,SAAS,CAAC,CAACJ,MAAM,CAACK,CAAC,CAAE,CAACL,MAAM,CAACM,CAAC,CAAC,CACpC,CACA,MAAO,CAAAJ,EAAE,CACX,CACA,MAAO,KAAI,CACb\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processTransformProps2 = exports.processTransformProps2 = function () {\n    const _e = [new global.Error(), -2, -27];\n    const TransformJs2 = function (Skia, props) {\n      const {\n        transform,\n        origin,\n        matrix\n      } = props;\n      if (matrix) {\n        const m3 = Skia.Matrix();\n        if (origin) {\n          m3.translate(origin.x, origin.y);\n          m3.concat(matrix);\n          m3.translate(-origin.x, -origin.y);\n        } else {\n          m3.concat(matrix);\n        }\n        return m3;\n      } else if (transform) {\n        const m3 = Skia.Matrix();\n        if (origin) {\n          m3.translate(origin.x, origin.y);\n        }\n        (0, _types.processTransform)(m3, transform);\n        if (origin) {\n          m3.translate(-origin.x, -origin.y);\n        }\n        return m3;\n      }\n      return null;\n    };\n    TransformJs2.__closure = {\n      processTransform: _types.processTransform\n    };\n    TransformJs2.__workletHash = 5650771643257;\n    TransformJs2.__initData = _worklet_5650771643257_init_data;\n    TransformJs2.__stackDetails = _e;\n    return TransformJs2;\n  }();\n});", "lineCount": 92, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_types"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 1, 55], [7, 8, 1, 55, "_worklet_6997418011774_init_data"], [7, 40, 1, 55], [8, 4, 1, 55, "code"], [8, 8, 1, 55], [9, 4, 1, 55, "location"], [9, 12, 1, 55], [10, 4, 1, 55, "sourceMap"], [10, 13, 1, 55], [11, 4, 1, 55, "version"], [11, 11, 1, 55], [12, 2, 1, 55], [13, 2, 2, 7], [13, 8, 2, 13, "processTransformProps"], [13, 29, 2, 34], [13, 32, 2, 34, "exports"], [13, 39, 2, 34], [13, 40, 2, 34, "processTransformProps"], [13, 61, 2, 34], [13, 64, 2, 37], [14, 4, 2, 37], [14, 10, 2, 37, "_e"], [14, 12, 2, 37], [14, 20, 2, 37, "global"], [14, 26, 2, 37], [14, 27, 2, 37, "Error"], [14, 32, 2, 37], [15, 4, 2, 37], [15, 10, 2, 37, "TransformJs1"], [15, 22, 2, 37], [15, 34, 2, 37, "TransformJs1"], [15, 35, 2, 38, "m3"], [15, 37, 2, 40], [15, 39, 2, 42, "props"], [15, 44, 2, 47], [15, 46, 2, 52], [16, 6, 5, 2], [16, 12, 5, 8], [17, 8, 6, 4, "transform"], [17, 17, 6, 13], [18, 8, 7, 4, "origin"], [18, 14, 7, 10], [19, 8, 8, 4, "matrix"], [20, 6, 9, 2], [20, 7, 9, 3], [20, 10, 9, 6, "props"], [20, 15, 9, 11], [21, 6, 10, 2], [21, 10, 10, 6, "matrix"], [21, 16, 10, 12], [21, 18, 10, 14], [22, 8, 11, 4], [22, 12, 11, 8, "origin"], [22, 18, 11, 14], [22, 20, 11, 16], [23, 10, 12, 6, "m3"], [23, 12, 12, 8], [23, 13, 12, 9, "translate"], [23, 22, 12, 18], [23, 23, 12, 19, "origin"], [23, 29, 12, 25], [23, 30, 12, 26, "x"], [23, 31, 12, 27], [23, 33, 12, 29, "origin"], [23, 39, 12, 35], [23, 40, 12, 36, "y"], [23, 41, 12, 37], [23, 42, 12, 38], [24, 10, 13, 6, "m3"], [24, 12, 13, 8], [24, 13, 13, 9, "concat"], [24, 19, 13, 15], [24, 20, 13, 16, "matrix"], [24, 26, 13, 22], [24, 27, 13, 23], [25, 10, 14, 6, "m3"], [25, 12, 14, 8], [25, 13, 14, 9, "translate"], [25, 22, 14, 18], [25, 23, 14, 19], [25, 24, 14, 20, "origin"], [25, 30, 14, 26], [25, 31, 14, 27, "x"], [25, 32, 14, 28], [25, 34, 14, 30], [25, 35, 14, 31, "origin"], [25, 41, 14, 37], [25, 42, 14, 38, "y"], [25, 43, 14, 39], [25, 44, 14, 40], [26, 8, 15, 4], [26, 9, 15, 5], [26, 15, 15, 11], [27, 10, 16, 6, "m3"], [27, 12, 16, 8], [27, 13, 16, 9, "concat"], [27, 19, 16, 15], [27, 20, 16, 16, "matrix"], [27, 26, 16, 22], [27, 27, 16, 23], [28, 8, 17, 4], [29, 6, 18, 2], [29, 7, 18, 3], [29, 13, 18, 9], [29, 17, 18, 13, "transform"], [29, 26, 18, 22], [29, 28, 18, 24], [30, 8, 19, 4], [30, 12, 19, 8, "origin"], [30, 18, 19, 14], [30, 20, 19, 16], [31, 10, 20, 6, "m3"], [31, 12, 20, 8], [31, 13, 20, 9, "translate"], [31, 22, 20, 18], [31, 23, 20, 19, "origin"], [31, 29, 20, 25], [31, 30, 20, 26, "x"], [31, 31, 20, 27], [31, 33, 20, 29, "origin"], [31, 39, 20, 35], [31, 40, 20, 36, "y"], [31, 41, 20, 37], [31, 42, 20, 38], [32, 8, 21, 4], [33, 8, 22, 4], [33, 12, 22, 4, "processTransform"], [33, 35, 22, 20], [33, 37, 22, 21, "m3"], [33, 39, 22, 23], [33, 41, 22, 25, "transform"], [33, 50, 22, 34], [33, 51, 22, 35], [34, 8, 23, 4], [34, 12, 23, 8, "origin"], [34, 18, 23, 14], [34, 20, 23, 16], [35, 10, 24, 6, "m3"], [35, 12, 24, 8], [35, 13, 24, 9, "translate"], [35, 22, 24, 18], [35, 23, 24, 19], [35, 24, 24, 20, "origin"], [35, 30, 24, 26], [35, 31, 24, 27, "x"], [35, 32, 24, 28], [35, 34, 24, 30], [35, 35, 24, 31, "origin"], [35, 41, 24, 37], [35, 42, 24, 38, "y"], [35, 43, 24, 39], [35, 44, 24, 40], [36, 8, 25, 4], [37, 6, 26, 2], [38, 4, 27, 0], [38, 5, 27, 1], [39, 4, 27, 1, "TransformJs1"], [39, 16, 27, 1], [39, 17, 27, 1, "__closure"], [39, 26, 27, 1], [40, 6, 27, 1, "processTransform"], [40, 22, 27, 1], [40, 24, 22, 4, "processTransform"], [41, 4, 22, 20], [42, 4, 22, 20, "TransformJs1"], [42, 16, 22, 20], [42, 17, 22, 20, "__workletHash"], [42, 30, 22, 20], [43, 4, 22, 20, "TransformJs1"], [43, 16, 22, 20], [43, 17, 22, 20, "__initData"], [43, 27, 22, 20], [43, 30, 22, 20, "_worklet_6997418011774_init_data"], [43, 62, 22, 20], [44, 4, 22, 20, "TransformJs1"], [44, 16, 22, 20], [44, 17, 22, 20, "__stackDetails"], [44, 31, 22, 20], [44, 34, 22, 20, "_e"], [44, 36, 22, 20], [45, 4, 22, 20], [45, 11, 22, 20, "TransformJs1"], [45, 23, 22, 20], [46, 2, 22, 20], [46, 3, 2, 37], [46, 5, 27, 1], [47, 2, 27, 2], [47, 8, 27, 2, "_worklet_5650771643257_init_data"], [47, 40, 27, 2], [48, 4, 27, 2, "code"], [48, 8, 27, 2], [49, 4, 27, 2, "location"], [49, 12, 27, 2], [50, 4, 27, 2, "sourceMap"], [50, 13, 27, 2], [51, 4, 27, 2, "version"], [51, 11, 27, 2], [52, 2, 27, 2], [53, 2, 28, 7], [53, 8, 28, 13, "processTransformProps2"], [53, 30, 28, 35], [53, 33, 28, 35, "exports"], [53, 40, 28, 35], [53, 41, 28, 35, "processTransformProps2"], [53, 63, 28, 35], [53, 66, 28, 38], [54, 4, 28, 38], [54, 10, 28, 38, "_e"], [54, 12, 28, 38], [54, 20, 28, 38, "global"], [54, 26, 28, 38], [54, 27, 28, 38, "Error"], [54, 32, 28, 38], [55, 4, 28, 38], [55, 10, 28, 38, "TransformJs2"], [55, 22, 28, 38], [55, 34, 28, 38, "TransformJs2"], [55, 35, 28, 39, "Skia"], [55, 39, 28, 43], [55, 41, 28, 45, "props"], [55, 46, 28, 50], [55, 48, 28, 55], [56, 6, 31, 2], [56, 12, 31, 8], [57, 8, 32, 4, "transform"], [57, 17, 32, 13], [58, 8, 33, 4, "origin"], [58, 14, 33, 10], [59, 8, 34, 4, "matrix"], [60, 6, 35, 2], [60, 7, 35, 3], [60, 10, 35, 6, "props"], [60, 15, 35, 11], [61, 6, 36, 2], [61, 10, 36, 6, "matrix"], [61, 16, 36, 12], [61, 18, 36, 14], [62, 8, 37, 4], [62, 14, 37, 10, "m3"], [62, 16, 37, 12], [62, 19, 37, 15, "Skia"], [62, 23, 37, 19], [62, 24, 37, 20, "Matrix"], [62, 30, 37, 26], [62, 31, 37, 27], [62, 32, 37, 28], [63, 8, 38, 4], [63, 12, 38, 8, "origin"], [63, 18, 38, 14], [63, 20, 38, 16], [64, 10, 39, 6, "m3"], [64, 12, 39, 8], [64, 13, 39, 9, "translate"], [64, 22, 39, 18], [64, 23, 39, 19, "origin"], [64, 29, 39, 25], [64, 30, 39, 26, "x"], [64, 31, 39, 27], [64, 33, 39, 29, "origin"], [64, 39, 39, 35], [64, 40, 39, 36, "y"], [64, 41, 39, 37], [64, 42, 39, 38], [65, 10, 40, 6, "m3"], [65, 12, 40, 8], [65, 13, 40, 9, "concat"], [65, 19, 40, 15], [65, 20, 40, 16, "matrix"], [65, 26, 40, 22], [65, 27, 40, 23], [66, 10, 41, 6, "m3"], [66, 12, 41, 8], [66, 13, 41, 9, "translate"], [66, 22, 41, 18], [66, 23, 41, 19], [66, 24, 41, 20, "origin"], [66, 30, 41, 26], [66, 31, 41, 27, "x"], [66, 32, 41, 28], [66, 34, 41, 30], [66, 35, 41, 31, "origin"], [66, 41, 41, 37], [66, 42, 41, 38, "y"], [66, 43, 41, 39], [66, 44, 41, 40], [67, 8, 42, 4], [67, 9, 42, 5], [67, 15, 42, 11], [68, 10, 43, 6, "m3"], [68, 12, 43, 8], [68, 13, 43, 9, "concat"], [68, 19, 43, 15], [68, 20, 43, 16, "matrix"], [68, 26, 43, 22], [68, 27, 43, 23], [69, 8, 44, 4], [70, 8, 45, 4], [70, 15, 45, 11, "m3"], [70, 17, 45, 13], [71, 6, 46, 2], [71, 7, 46, 3], [71, 13, 46, 9], [71, 17, 46, 13, "transform"], [71, 26, 46, 22], [71, 28, 46, 24], [72, 8, 47, 4], [72, 14, 47, 10, "m3"], [72, 16, 47, 12], [72, 19, 47, 15, "Skia"], [72, 23, 47, 19], [72, 24, 47, 20, "Matrix"], [72, 30, 47, 26], [72, 31, 47, 27], [72, 32, 47, 28], [73, 8, 48, 4], [73, 12, 48, 8, "origin"], [73, 18, 48, 14], [73, 20, 48, 16], [74, 10, 49, 6, "m3"], [74, 12, 49, 8], [74, 13, 49, 9, "translate"], [74, 22, 49, 18], [74, 23, 49, 19, "origin"], [74, 29, 49, 25], [74, 30, 49, 26, "x"], [74, 31, 49, 27], [74, 33, 49, 29, "origin"], [74, 39, 49, 35], [74, 40, 49, 36, "y"], [74, 41, 49, 37], [74, 42, 49, 38], [75, 8, 50, 4], [76, 8, 51, 4], [76, 12, 51, 4, "processTransform"], [76, 35, 51, 20], [76, 37, 51, 21, "m3"], [76, 39, 51, 23], [76, 41, 51, 25, "transform"], [76, 50, 51, 34], [76, 51, 51, 35], [77, 8, 52, 4], [77, 12, 52, 8, "origin"], [77, 18, 52, 14], [77, 20, 52, 16], [78, 10, 53, 6, "m3"], [78, 12, 53, 8], [78, 13, 53, 9, "translate"], [78, 22, 53, 18], [78, 23, 53, 19], [78, 24, 53, 20, "origin"], [78, 30, 53, 26], [78, 31, 53, 27, "x"], [78, 32, 53, 28], [78, 34, 53, 30], [78, 35, 53, 31, "origin"], [78, 41, 53, 37], [78, 42, 53, 38, "y"], [78, 43, 53, 39], [78, 44, 53, 40], [79, 8, 54, 4], [80, 8, 55, 4], [80, 15, 55, 11, "m3"], [80, 17, 55, 13], [81, 6, 56, 2], [82, 6, 57, 2], [82, 13, 57, 9], [82, 17, 57, 13], [83, 4, 58, 0], [83, 5, 58, 1], [84, 4, 58, 1, "TransformJs2"], [84, 16, 58, 1], [84, 17, 58, 1, "__closure"], [84, 26, 58, 1], [85, 6, 58, 1, "processTransform"], [85, 22, 58, 1], [85, 24, 51, 4, "processTransform"], [86, 4, 51, 20], [87, 4, 51, 20, "TransformJs2"], [87, 16, 51, 20], [87, 17, 51, 20, "__workletHash"], [87, 30, 51, 20], [88, 4, 51, 20, "TransformJs2"], [88, 16, 51, 20], [88, 17, 51, 20, "__initData"], [88, 27, 51, 20], [88, 30, 51, 20, "_worklet_5650771643257_init_data"], [88, 62, 51, 20], [89, 4, 51, 20, "TransformJs2"], [89, 16, 51, 20], [89, 17, 51, 20, "__stackDetails"], [89, 31, 51, 20], [89, 34, 51, 20, "_e"], [89, 36, 51, 20], [90, 4, 51, 20], [90, 11, 51, 20, "TransformJs2"], [90, 23, 51, 20], [91, 2, 51, 20], [91, 3, 28, 38], [91, 5, 58, 1], [92, 0, 58, 2], [92, 3]], "functionMap": {"names": ["<global>", "processTransformProps", "processTransformProps2"], "mappings": "AAA;qCCC;CDyB;sCEC;CF8B"}}, "type": "js/module"}]}