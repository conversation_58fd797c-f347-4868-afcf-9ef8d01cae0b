{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = within;\n  function within(ids, coords, qx, qy, r, nodeSize) {\n    const stack = [0, ids.length - 1, 0];\n    const result = [];\n    const r2 = r * r;\n    while (stack.length) {\n      const axis = stack.pop();\n      const right = stack.pop();\n      const left = stack.pop();\n      if (right - left <= nodeSize) {\n        for (let i = left; i <= right; i++) {\n          if (sqDist(coords[2 * i], coords[2 * i + 1], qx, qy) <= r2) result.push(ids[i]);\n        }\n        continue;\n      }\n      const m = Math.floor((left + right) / 2);\n      const x = coords[2 * m];\n      const y = coords[2 * m + 1];\n      if (sqDist(x, y, qx, qy) <= r2) result.push(ids[m]);\n      const nextAxis = (axis + 1) % 2;\n      if (axis === 0 ? qx - r <= x : qy - r <= y) {\n        stack.push(left);\n        stack.push(m - 1);\n        stack.push(nextAxis);\n      }\n      if (axis === 0 ? qx + r >= x : qy + r >= y) {\n        stack.push(m + 1);\n        stack.push(right);\n        stack.push(nextAxis);\n      }\n    }\n    return result;\n  }\n  function sqDist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n  }\n});", "lineCount": 43, "map": [[6, 2, 2, 15], [6, 11, 2, 24, "within"], [6, 17, 2, 30, "within"], [6, 18, 2, 31, "ids"], [6, 21, 2, 34], [6, 23, 2, 36, "coords"], [6, 29, 2, 42], [6, 31, 2, 44, "qx"], [6, 33, 2, 46], [6, 35, 2, 48, "qy"], [6, 37, 2, 50], [6, 39, 2, 52, "r"], [6, 40, 2, 53], [6, 42, 2, 55, "nodeSize"], [6, 50, 2, 63], [6, 52, 2, 65], [7, 4, 3, 4], [7, 10, 3, 10, "stack"], [7, 15, 3, 15], [7, 18, 3, 18], [7, 19, 3, 19], [7, 20, 3, 20], [7, 22, 3, 22, "ids"], [7, 25, 3, 25], [7, 26, 3, 26, "length"], [7, 32, 3, 32], [7, 35, 3, 35], [7, 36, 3, 36], [7, 38, 3, 38], [7, 39, 3, 39], [7, 40, 3, 40], [8, 4, 4, 4], [8, 10, 4, 10, "result"], [8, 16, 4, 16], [8, 19, 4, 19], [8, 21, 4, 21], [9, 4, 5, 4], [9, 10, 5, 10, "r2"], [9, 12, 5, 12], [9, 15, 5, 15, "r"], [9, 16, 5, 16], [9, 19, 5, 19, "r"], [9, 20, 5, 20], [10, 4, 7, 4], [10, 11, 7, 11, "stack"], [10, 16, 7, 16], [10, 17, 7, 17, "length"], [10, 23, 7, 23], [10, 25, 7, 25], [11, 6, 8, 8], [11, 12, 8, 14, "axis"], [11, 16, 8, 18], [11, 19, 8, 21, "stack"], [11, 24, 8, 26], [11, 25, 8, 27, "pop"], [11, 28, 8, 30], [11, 29, 8, 31], [11, 30, 8, 32], [12, 6, 9, 8], [12, 12, 9, 14, "right"], [12, 17, 9, 19], [12, 20, 9, 22, "stack"], [12, 25, 9, 27], [12, 26, 9, 28, "pop"], [12, 29, 9, 31], [12, 30, 9, 32], [12, 31, 9, 33], [13, 6, 10, 8], [13, 12, 10, 14, "left"], [13, 16, 10, 18], [13, 19, 10, 21, "stack"], [13, 24, 10, 26], [13, 25, 10, 27, "pop"], [13, 28, 10, 30], [13, 29, 10, 31], [13, 30, 10, 32], [14, 6, 12, 8], [14, 10, 12, 12, "right"], [14, 15, 12, 17], [14, 18, 12, 20, "left"], [14, 22, 12, 24], [14, 26, 12, 28, "nodeSize"], [14, 34, 12, 36], [14, 36, 12, 38], [15, 8, 13, 12], [15, 13, 13, 17], [15, 17, 13, 21, "i"], [15, 18, 13, 22], [15, 21, 13, 25, "left"], [15, 25, 13, 29], [15, 27, 13, 31, "i"], [15, 28, 13, 32], [15, 32, 13, 36, "right"], [15, 37, 13, 41], [15, 39, 13, 43, "i"], [15, 40, 13, 44], [15, 42, 13, 46], [15, 44, 13, 48], [16, 10, 14, 16], [16, 14, 14, 20, "sqDist"], [16, 20, 14, 26], [16, 21, 14, 27, "coords"], [16, 27, 14, 33], [16, 28, 14, 34], [16, 29, 14, 35], [16, 32, 14, 38, "i"], [16, 33, 14, 39], [16, 34, 14, 40], [16, 36, 14, 42, "coords"], [16, 42, 14, 48], [16, 43, 14, 49], [16, 44, 14, 50], [16, 47, 14, 53, "i"], [16, 48, 14, 54], [16, 51, 14, 57], [16, 52, 14, 58], [16, 53, 14, 59], [16, 55, 14, 61, "qx"], [16, 57, 14, 63], [16, 59, 14, 65, "qy"], [16, 61, 14, 67], [16, 62, 14, 68], [16, 66, 14, 72, "r2"], [16, 68, 14, 74], [16, 70, 14, 76, "result"], [16, 76, 14, 82], [16, 77, 14, 83, "push"], [16, 81, 14, 87], [16, 82, 14, 88, "ids"], [16, 85, 14, 91], [16, 86, 14, 92, "i"], [16, 87, 14, 93], [16, 88, 14, 94], [16, 89, 14, 95], [17, 8, 15, 12], [18, 8, 16, 12], [19, 6, 17, 8], [20, 6, 19, 8], [20, 12, 19, 14, "m"], [20, 13, 19, 15], [20, 16, 19, 18, "Math"], [20, 20, 19, 22], [20, 21, 19, 23, "floor"], [20, 26, 19, 28], [20, 27, 19, 29], [20, 28, 19, 30, "left"], [20, 32, 19, 34], [20, 35, 19, 37, "right"], [20, 40, 19, 42], [20, 44, 19, 46], [20, 45, 19, 47], [20, 46, 19, 48], [21, 6, 21, 8], [21, 12, 21, 14, "x"], [21, 13, 21, 15], [21, 16, 21, 18, "coords"], [21, 22, 21, 24], [21, 23, 21, 25], [21, 24, 21, 26], [21, 27, 21, 29, "m"], [21, 28, 21, 30], [21, 29, 21, 31], [22, 6, 22, 8], [22, 12, 22, 14, "y"], [22, 13, 22, 15], [22, 16, 22, 18, "coords"], [22, 22, 22, 24], [22, 23, 22, 25], [22, 24, 22, 26], [22, 27, 22, 29, "m"], [22, 28, 22, 30], [22, 31, 22, 33], [22, 32, 22, 34], [22, 33, 22, 35], [23, 6, 24, 8], [23, 10, 24, 12, "sqDist"], [23, 16, 24, 18], [23, 17, 24, 19, "x"], [23, 18, 24, 20], [23, 20, 24, 22, "y"], [23, 21, 24, 23], [23, 23, 24, 25, "qx"], [23, 25, 24, 27], [23, 27, 24, 29, "qy"], [23, 29, 24, 31], [23, 30, 24, 32], [23, 34, 24, 36, "r2"], [23, 36, 24, 38], [23, 38, 24, 40, "result"], [23, 44, 24, 46], [23, 45, 24, 47, "push"], [23, 49, 24, 51], [23, 50, 24, 52, "ids"], [23, 53, 24, 55], [23, 54, 24, 56, "m"], [23, 55, 24, 57], [23, 56, 24, 58], [23, 57, 24, 59], [24, 6, 26, 8], [24, 12, 26, 14, "nextAxis"], [24, 20, 26, 22], [24, 23, 26, 25], [24, 24, 26, 26, "axis"], [24, 28, 26, 30], [24, 31, 26, 33], [24, 32, 26, 34], [24, 36, 26, 38], [24, 37, 26, 39], [25, 6, 28, 8], [25, 10, 28, 12, "axis"], [25, 14, 28, 16], [25, 19, 28, 21], [25, 20, 28, 22], [25, 23, 28, 25, "qx"], [25, 25, 28, 27], [25, 28, 28, 30, "r"], [25, 29, 28, 31], [25, 33, 28, 35, "x"], [25, 34, 28, 36], [25, 37, 28, 39, "qy"], [25, 39, 28, 41], [25, 42, 28, 44, "r"], [25, 43, 28, 45], [25, 47, 28, 49, "y"], [25, 48, 28, 50], [25, 50, 28, 52], [26, 8, 29, 12, "stack"], [26, 13, 29, 17], [26, 14, 29, 18, "push"], [26, 18, 29, 22], [26, 19, 29, 23, "left"], [26, 23, 29, 27], [26, 24, 29, 28], [27, 8, 30, 12, "stack"], [27, 13, 30, 17], [27, 14, 30, 18, "push"], [27, 18, 30, 22], [27, 19, 30, 23, "m"], [27, 20, 30, 24], [27, 23, 30, 27], [27, 24, 30, 28], [27, 25, 30, 29], [28, 8, 31, 12, "stack"], [28, 13, 31, 17], [28, 14, 31, 18, "push"], [28, 18, 31, 22], [28, 19, 31, 23, "nextAxis"], [28, 27, 31, 31], [28, 28, 31, 32], [29, 6, 32, 8], [30, 6, 33, 8], [30, 10, 33, 12, "axis"], [30, 14, 33, 16], [30, 19, 33, 21], [30, 20, 33, 22], [30, 23, 33, 25, "qx"], [30, 25, 33, 27], [30, 28, 33, 30, "r"], [30, 29, 33, 31], [30, 33, 33, 35, "x"], [30, 34, 33, 36], [30, 37, 33, 39, "qy"], [30, 39, 33, 41], [30, 42, 33, 44, "r"], [30, 43, 33, 45], [30, 47, 33, 49, "y"], [30, 48, 33, 50], [30, 50, 33, 52], [31, 8, 34, 12, "stack"], [31, 13, 34, 17], [31, 14, 34, 18, "push"], [31, 18, 34, 22], [31, 19, 34, 23, "m"], [31, 20, 34, 24], [31, 23, 34, 27], [31, 24, 34, 28], [31, 25, 34, 29], [32, 8, 35, 12, "stack"], [32, 13, 35, 17], [32, 14, 35, 18, "push"], [32, 18, 35, 22], [32, 19, 35, 23, "right"], [32, 24, 35, 28], [32, 25, 35, 29], [33, 8, 36, 12, "stack"], [33, 13, 36, 17], [33, 14, 36, 18, "push"], [33, 18, 36, 22], [33, 19, 36, 23, "nextAxis"], [33, 27, 36, 31], [33, 28, 36, 32], [34, 6, 37, 8], [35, 4, 38, 4], [36, 4, 40, 4], [36, 11, 40, 11, "result"], [36, 17, 40, 17], [37, 2, 41, 0], [38, 2, 43, 0], [38, 11, 43, 9, "sqDist"], [38, 17, 43, 15, "sqDist"], [38, 18, 43, 16, "ax"], [38, 20, 43, 18], [38, 22, 43, 20, "ay"], [38, 24, 43, 22], [38, 26, 43, 24, "bx"], [38, 28, 43, 26], [38, 30, 43, 28, "by"], [38, 32, 43, 30], [38, 34, 43, 32], [39, 4, 44, 4], [39, 10, 44, 10, "dx"], [39, 12, 44, 12], [39, 15, 44, 15, "ax"], [39, 17, 44, 17], [39, 20, 44, 20, "bx"], [39, 22, 44, 22], [40, 4, 45, 4], [40, 10, 45, 10, "dy"], [40, 12, 45, 12], [40, 15, 45, 15, "ay"], [40, 17, 45, 17], [40, 20, 45, 20, "by"], [40, 22, 45, 22], [41, 4, 46, 4], [41, 11, 46, 11, "dx"], [41, 13, 46, 13], [41, 16, 46, 16, "dx"], [41, 18, 46, 18], [41, 21, 46, 21, "dy"], [41, 23, 46, 23], [41, 26, 46, 26, "dy"], [41, 28, 46, 28], [42, 2, 47, 0], [43, 0, 47, 1], [43, 3]], "functionMap": {"names": ["<global>", "within", "sqDist"], "mappings": "AAA;eCC;CDuC;AEE;CFI"}}, "type": "js/module"}]}