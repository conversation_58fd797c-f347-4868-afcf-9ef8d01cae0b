{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 25}, "end": {"line": 2, "column": 31, "index": 56}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./WebCameraUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 57}, "end": {"line": 3, "column": 42, "index": 99}}], "key": "7QFlRc+R5Zhr/RLcJUPMtLUHEsc=", "exportNames": ["*"]}}, {"name": "./WebConstants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 100}, "end": {"line": 4, "column": 56, "index": 156}}], "key": "VYBjMDeNhUYVRZIl4m+mFrGQcXE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useWebCameraStream = useWebCameraStream;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var Utils = _interopRequireWildcard(require(_dependencyMap[1], \"./WebCameraUtils\"));\n  var _WebConstants = require(_dependencyMap[2], \"./WebConstants\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /* eslint-env browser */\n\n  const VALID_SETTINGS_KEYS = ['autoFocus', 'flashMode', 'exposureCompensation', 'colorTemperature', 'iso', 'brightness', 'contrast', 'saturation', 'sharpness', 'focusDistance', 'whiteBalance', 'zoom'];\n  function useLoadedVideo(video, onLoaded) {\n    React.useEffect(() => {\n      if (video) {\n        video.addEventListener('loadedmetadata', () => {\n          // without this async block the constraints aren't properly applied to the camera,\n          // this means that if you were to turn on the torch and swap to the front camera,\n          // then swap back to the rear camera the torch setting wouldn't be applied.\n          requestAnimationFrame(() => {\n            onLoaded();\n          });\n        });\n      }\n    }, [video]);\n  }\n  function useWebCameraStream(video, preferredType, settings, {\n    onCameraReady,\n    onMountError\n  }) {\n    const isStartingCamera = React.useRef(false);\n    const activeStreams = React.useRef([]);\n    const capabilities = React.useRef({\n      autoFocus: 'continuous',\n      flashMode: 'off',\n      whiteBalance: 'continuous',\n      zoom: 1\n    });\n    const [stream, setStream] = React.useState(null);\n    const mediaTrackSettings = React.useMemo(() => {\n      return stream ? stream.getTracks()[0].getSettings() : null;\n    }, [stream]);\n    // The actual camera type - this can be different from the incoming camera type.\n    const type = React.useMemo(() => {\n      if (!mediaTrackSettings) {\n        return null;\n      }\n      // On desktop no value will be returned, in this case we should assume the cameraType is 'front'\n      const {\n        facingMode = 'user'\n      } = mediaTrackSettings;\n      return _WebConstants.FacingModeToCameraType[facingMode];\n    }, [mediaTrackSettings]);\n    const getStreamDeviceAsync = React.useCallback(async () => {\n      try {\n        return await Utils.getPreferredStreamDevice(preferredType);\n      } catch (nativeEvent) {\n        if (__DEV__) {\n          console.warn(`Error requesting UserMedia for type \"${preferredType}\":`, nativeEvent);\n        }\n        if (onMountError) {\n          onMountError({\n            nativeEvent\n          });\n        }\n        return null;\n      }\n    }, [preferredType, onMountError]);\n    const resumeAsync = React.useCallback(async () => {\n      const nextStream = await getStreamDeviceAsync();\n      if (Utils.compareStreams(nextStream, stream)) {\n        // Do nothing if the streams are the same.\n        // This happens when the device only supports one camera (i.e. desktop) and the mode was toggled between front/back while already active.\n        // Without this check there is a screen flash while the video switches.\n        return false;\n      }\n      // Save a history of all active streams (usually 2+) so we can close them later.\n      // Keeping them open makes swapping camera types much faster.\n      if (!activeStreams.current.some(value => value.id === nextStream?.id)) {\n        activeStreams.current.push(nextStream);\n      }\n      // Set the new stream -> update the video, settings, and actual camera type.\n      setStream(nextStream);\n      if (onCameraReady) {\n        onCameraReady();\n      }\n      return false;\n    }, [getStreamDeviceAsync, setStream, onCameraReady, stream, activeStreams.current]);\n    React.useEffect(() => {\n      // Restart the camera and guard concurrent actions.\n      if (isStartingCamera.current) {\n        return;\n      }\n      isStartingCamera.current = true;\n      resumeAsync().then(isStarting => {\n        isStartingCamera.current = isStarting;\n      }).catch(() => {\n        // ensure the camera can be started again.\n        isStartingCamera.current = false;\n      });\n    }, [preferredType]);\n    // Update the native camera with any custom capabilities.\n    React.useEffect(() => {\n      const changes = {};\n      for (const key of VALID_SETTINGS_KEYS) {\n        if (key in settings) {\n          const nextValue = settings[key];\n          if (nextValue !== capabilities.current[key]) {\n            changes[key] = nextValue;\n          }\n        }\n      }\n      // Only update the native camera if changes were found\n      const hasChanges = !!Object.keys(changes).length;\n      const nextWebCameraSettings = {\n        ...capabilities.current,\n        ...changes\n      };\n      if (hasChanges) {\n        Utils.syncTrackCapabilities(preferredType, stream, changes);\n      }\n      capabilities.current = nextWebCameraSettings;\n    }, [settings.autoFocus, settings.flashMode, settings.exposureCompensation, settings.colorTemperature, settings.iso, settings.brightness, settings.contrast, settings.saturation, settings.sharpness, settings.focusDistance, settings.whiteBalance, settings.zoom]);\n    React.useEffect(() => {\n      // set or unset the video source.\n      if (!video.current) {\n        return;\n      }\n      Utils.setVideoSource(video.current, stream);\n    }, [video.current, stream]);\n    React.useEffect(() => {\n      return () => {\n        // Clean up on dismount, this is important for making sure the camera light goes off when the component is removed.\n        for (const stream of activeStreams.current) {\n          // Close all open streams.\n          Utils.stopMediaStream(stream);\n        }\n        if (video.current) {\n          // Invalidate the video source.\n          Utils.setVideoSource(video.current, stream);\n        }\n      };\n    }, []);\n    // Update props when the video loads.\n    useLoadedVideo(video.current, () => {\n      Utils.syncTrackCapabilities(preferredType, stream, capabilities.current);\n    });\n    return {\n      type,\n      mediaTrackSettings\n    };\n  }\n});", "lineCount": 153, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "React"], [6, 11, 2, 0], [6, 14, 2, 0, "_interopRequireWildcard"], [6, 37, 2, 0], [6, 38, 2, 0, "require"], [6, 45, 2, 0], [6, 46, 2, 0, "_dependencyMap"], [6, 60, 2, 0], [7, 2, 3, 0], [7, 6, 3, 0, "Utils"], [7, 11, 3, 0], [7, 14, 3, 0, "_interopRequireWildcard"], [7, 37, 3, 0], [7, 38, 3, 0, "require"], [7, 45, 3, 0], [7, 46, 3, 0, "_dependencyMap"], [7, 60, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_WebConstants"], [8, 19, 4, 0], [8, 22, 4, 0, "require"], [8, 29, 4, 0], [8, 30, 4, 0, "_dependencyMap"], [8, 44, 4, 0], [9, 2, 4, 56], [9, 11, 4, 56, "_interopRequireWildcard"], [9, 35, 4, 56, "e"], [9, 36, 4, 56], [9, 38, 4, 56, "t"], [9, 39, 4, 56], [9, 68, 4, 56, "WeakMap"], [9, 75, 4, 56], [9, 81, 4, 56, "r"], [9, 82, 4, 56], [9, 89, 4, 56, "WeakMap"], [9, 96, 4, 56], [9, 100, 4, 56, "n"], [9, 101, 4, 56], [9, 108, 4, 56, "WeakMap"], [9, 115, 4, 56], [9, 127, 4, 56, "_interopRequireWildcard"], [9, 150, 4, 56], [9, 162, 4, 56, "_interopRequireWildcard"], [9, 163, 4, 56, "e"], [9, 164, 4, 56], [9, 166, 4, 56, "t"], [9, 167, 4, 56], [9, 176, 4, 56, "t"], [9, 177, 4, 56], [9, 181, 4, 56, "e"], [9, 182, 4, 56], [9, 186, 4, 56, "e"], [9, 187, 4, 56], [9, 188, 4, 56, "__esModule"], [9, 198, 4, 56], [9, 207, 4, 56, "e"], [9, 208, 4, 56], [9, 214, 4, 56, "o"], [9, 215, 4, 56], [9, 217, 4, 56, "i"], [9, 218, 4, 56], [9, 220, 4, 56, "f"], [9, 221, 4, 56], [9, 226, 4, 56, "__proto__"], [9, 235, 4, 56], [9, 243, 4, 56, "default"], [9, 250, 4, 56], [9, 252, 4, 56, "e"], [9, 253, 4, 56], [9, 270, 4, 56, "e"], [9, 271, 4, 56], [9, 294, 4, 56, "e"], [9, 295, 4, 56], [9, 320, 4, 56, "e"], [9, 321, 4, 56], [9, 330, 4, 56, "f"], [9, 331, 4, 56], [9, 337, 4, 56, "o"], [9, 338, 4, 56], [9, 341, 4, 56, "t"], [9, 342, 4, 56], [9, 345, 4, 56, "n"], [9, 346, 4, 56], [9, 349, 4, 56, "r"], [9, 350, 4, 56], [9, 358, 4, 56, "o"], [9, 359, 4, 56], [9, 360, 4, 56, "has"], [9, 363, 4, 56], [9, 364, 4, 56, "e"], [9, 365, 4, 56], [9, 375, 4, 56, "o"], [9, 376, 4, 56], [9, 377, 4, 56, "get"], [9, 380, 4, 56], [9, 381, 4, 56, "e"], [9, 382, 4, 56], [9, 385, 4, 56, "o"], [9, 386, 4, 56], [9, 387, 4, 56, "set"], [9, 390, 4, 56], [9, 391, 4, 56, "e"], [9, 392, 4, 56], [9, 394, 4, 56, "f"], [9, 395, 4, 56], [9, 411, 4, 56, "t"], [9, 412, 4, 56], [9, 416, 4, 56, "e"], [9, 417, 4, 56], [9, 433, 4, 56, "t"], [9, 434, 4, 56], [9, 441, 4, 56, "hasOwnProperty"], [9, 455, 4, 56], [9, 456, 4, 56, "call"], [9, 460, 4, 56], [9, 461, 4, 56, "e"], [9, 462, 4, 56], [9, 464, 4, 56, "t"], [9, 465, 4, 56], [9, 472, 4, 56, "i"], [9, 473, 4, 56], [9, 477, 4, 56, "o"], [9, 478, 4, 56], [9, 481, 4, 56, "Object"], [9, 487, 4, 56], [9, 488, 4, 56, "defineProperty"], [9, 502, 4, 56], [9, 507, 4, 56, "Object"], [9, 513, 4, 56], [9, 514, 4, 56, "getOwnPropertyDescriptor"], [9, 538, 4, 56], [9, 539, 4, 56, "e"], [9, 540, 4, 56], [9, 542, 4, 56, "t"], [9, 543, 4, 56], [9, 550, 4, 56, "i"], [9, 551, 4, 56], [9, 552, 4, 56, "get"], [9, 555, 4, 56], [9, 559, 4, 56, "i"], [9, 560, 4, 56], [9, 561, 4, 56, "set"], [9, 564, 4, 56], [9, 568, 4, 56, "o"], [9, 569, 4, 56], [9, 570, 4, 56, "f"], [9, 571, 4, 56], [9, 573, 4, 56, "t"], [9, 574, 4, 56], [9, 576, 4, 56, "i"], [9, 577, 4, 56], [9, 581, 4, 56, "f"], [9, 582, 4, 56], [9, 583, 4, 56, "t"], [9, 584, 4, 56], [9, 588, 4, 56, "e"], [9, 589, 4, 56], [9, 590, 4, 56, "t"], [9, 591, 4, 56], [9, 602, 4, 56, "f"], [9, 603, 4, 56], [9, 608, 4, 56, "e"], [9, 609, 4, 56], [9, 611, 4, 56, "t"], [9, 612, 4, 56], [10, 2, 1, 0], [12, 2, 5, 0], [12, 8, 5, 6, "VALID_SETTINGS_KEYS"], [12, 27, 5, 25], [12, 30, 5, 28], [12, 31, 6, 4], [12, 42, 6, 15], [12, 44, 7, 4], [12, 55, 7, 15], [12, 57, 8, 4], [12, 79, 8, 26], [12, 81, 9, 4], [12, 99, 9, 22], [12, 101, 10, 4], [12, 106, 10, 9], [12, 108, 11, 4], [12, 120, 11, 16], [12, 122, 12, 4], [12, 132, 12, 14], [12, 134, 13, 4], [12, 146, 13, 16], [12, 148, 14, 4], [12, 159, 14, 15], [12, 161, 15, 4], [12, 176, 15, 19], [12, 178, 16, 4], [12, 192, 16, 18], [12, 194, 17, 4], [12, 200, 17, 10], [12, 201, 18, 1], [13, 2, 19, 0], [13, 11, 19, 9, "useLoadedVideo"], [13, 25, 19, 23, "useLoadedVideo"], [13, 26, 19, 24, "video"], [13, 31, 19, 29], [13, 33, 19, 31, "onLoaded"], [13, 41, 19, 39], [13, 43, 19, 41], [14, 4, 20, 4, "React"], [14, 9, 20, 9], [14, 10, 20, 10, "useEffect"], [14, 19, 20, 19], [14, 20, 20, 20], [14, 26, 20, 26], [15, 6, 21, 8], [15, 10, 21, 12, "video"], [15, 15, 21, 17], [15, 17, 21, 19], [16, 8, 22, 12, "video"], [16, 13, 22, 17], [16, 14, 22, 18, "addEventListener"], [16, 30, 22, 34], [16, 31, 22, 35], [16, 47, 22, 51], [16, 49, 22, 53], [16, 55, 22, 59], [17, 10, 23, 16], [18, 10, 24, 16], [19, 10, 25, 16], [20, 10, 26, 16, "requestAnimationFrame"], [20, 31, 26, 37], [20, 32, 26, 38], [20, 38, 26, 44], [21, 12, 27, 20, "onLoaded"], [21, 20, 27, 28], [21, 21, 27, 29], [21, 22, 27, 30], [22, 10, 28, 16], [22, 11, 28, 17], [22, 12, 28, 18], [23, 8, 29, 12], [23, 9, 29, 13], [23, 10, 29, 14], [24, 6, 30, 8], [25, 4, 31, 4], [25, 5, 31, 5], [25, 7, 31, 7], [25, 8, 31, 8, "video"], [25, 13, 31, 13], [25, 14, 31, 14], [25, 15, 31, 15], [26, 2, 32, 0], [27, 2, 33, 7], [27, 11, 33, 16, "useWebCameraStream"], [27, 29, 33, 34, "useWebCameraStream"], [27, 30, 33, 35, "video"], [27, 35, 33, 40], [27, 37, 33, 42, "preferredType"], [27, 50, 33, 55], [27, 52, 33, 57, "settings"], [27, 60, 33, 65], [27, 62, 33, 67], [28, 4, 33, 69, "onCameraReady"], [28, 17, 33, 82], [29, 4, 33, 84, "onMountError"], [30, 2, 33, 98], [30, 3, 33, 99], [30, 5, 33, 101], [31, 4, 34, 4], [31, 10, 34, 10, "isStartingCamera"], [31, 26, 34, 26], [31, 29, 34, 29, "React"], [31, 34, 34, 34], [31, 35, 34, 35, "useRef"], [31, 41, 34, 41], [31, 42, 34, 42], [31, 47, 34, 47], [31, 48, 34, 48], [32, 4, 35, 4], [32, 10, 35, 10, "activeStreams"], [32, 23, 35, 23], [32, 26, 35, 26, "React"], [32, 31, 35, 31], [32, 32, 35, 32, "useRef"], [32, 38, 35, 38], [32, 39, 35, 39], [32, 41, 35, 41], [32, 42, 35, 42], [33, 4, 36, 4], [33, 10, 36, 10, "capabilities"], [33, 22, 36, 22], [33, 25, 36, 25, "React"], [33, 30, 36, 30], [33, 31, 36, 31, "useRef"], [33, 37, 36, 37], [33, 38, 36, 38], [34, 6, 37, 8, "autoFocus"], [34, 15, 37, 17], [34, 17, 37, 19], [34, 29, 37, 31], [35, 6, 38, 8, "flashMode"], [35, 15, 38, 17], [35, 17, 38, 19], [35, 22, 38, 24], [36, 6, 39, 8, "whiteBalance"], [36, 18, 39, 20], [36, 20, 39, 22], [36, 32, 39, 34], [37, 6, 40, 8, "zoom"], [37, 10, 40, 12], [37, 12, 40, 14], [38, 4, 41, 4], [38, 5, 41, 5], [38, 6, 41, 6], [39, 4, 42, 4], [39, 10, 42, 10], [39, 11, 42, 11, "stream"], [39, 17, 42, 17], [39, 19, 42, 19, "setStream"], [39, 28, 42, 28], [39, 29, 42, 29], [39, 32, 42, 32, "React"], [39, 37, 42, 37], [39, 38, 42, 38, "useState"], [39, 46, 42, 46], [39, 47, 42, 47], [39, 51, 42, 51], [39, 52, 42, 52], [40, 4, 43, 4], [40, 10, 43, 10, "mediaTrackSettings"], [40, 28, 43, 28], [40, 31, 43, 31, "React"], [40, 36, 43, 36], [40, 37, 43, 37, "useMemo"], [40, 44, 43, 44], [40, 45, 43, 45], [40, 51, 43, 51], [41, 6, 44, 8], [41, 13, 44, 15, "stream"], [41, 19, 44, 21], [41, 22, 44, 24, "stream"], [41, 28, 44, 30], [41, 29, 44, 31, "getTracks"], [41, 38, 44, 40], [41, 39, 44, 41], [41, 40, 44, 42], [41, 41, 44, 43], [41, 42, 44, 44], [41, 43, 44, 45], [41, 44, 44, 46, "getSettings"], [41, 55, 44, 57], [41, 56, 44, 58], [41, 57, 44, 59], [41, 60, 44, 62], [41, 64, 44, 66], [42, 4, 45, 4], [42, 5, 45, 5], [42, 7, 45, 7], [42, 8, 45, 8, "stream"], [42, 14, 45, 14], [42, 15, 45, 15], [42, 16, 45, 16], [43, 4, 46, 4], [44, 4, 47, 4], [44, 10, 47, 10, "type"], [44, 14, 47, 14], [44, 17, 47, 17, "React"], [44, 22, 47, 22], [44, 23, 47, 23, "useMemo"], [44, 30, 47, 30], [44, 31, 47, 31], [44, 37, 47, 37], [45, 6, 48, 8], [45, 10, 48, 12], [45, 11, 48, 13, "mediaTrackSettings"], [45, 29, 48, 31], [45, 31, 48, 33], [46, 8, 49, 12], [46, 15, 49, 19], [46, 19, 49, 23], [47, 6, 50, 8], [48, 6, 51, 8], [49, 6, 52, 8], [49, 12, 52, 14], [50, 8, 52, 16, "facingMode"], [50, 18, 52, 26], [50, 21, 52, 29], [51, 6, 52, 36], [51, 7, 52, 37], [51, 10, 52, 40, "mediaTrackSettings"], [51, 28, 52, 58], [52, 6, 53, 8], [52, 13, 53, 15, "FacingModeToCameraType"], [52, 49, 53, 37], [52, 50, 53, 38, "facingMode"], [52, 60, 53, 48], [52, 61, 53, 49], [53, 4, 54, 4], [53, 5, 54, 5], [53, 7, 54, 7], [53, 8, 54, 8, "mediaTrackSettings"], [53, 26, 54, 26], [53, 27, 54, 27], [53, 28, 54, 28], [54, 4, 55, 4], [54, 10, 55, 10, "getStreamDeviceAsync"], [54, 30, 55, 30], [54, 33, 55, 33, "React"], [54, 38, 55, 38], [54, 39, 55, 39, "useCallback"], [54, 50, 55, 50], [54, 51, 55, 51], [54, 63, 55, 63], [55, 6, 56, 8], [55, 10, 56, 12], [56, 8, 57, 12], [56, 15, 57, 19], [56, 21, 57, 25, "Utils"], [56, 26, 57, 30], [56, 27, 57, 31, "getPreferredStreamDevice"], [56, 51, 57, 55], [56, 52, 57, 56, "preferredType"], [56, 65, 57, 69], [56, 66, 57, 70], [57, 6, 58, 8], [57, 7, 58, 9], [57, 8, 59, 8], [57, 15, 59, 15, "nativeEvent"], [57, 26, 59, 26], [57, 28, 59, 28], [58, 8, 60, 12], [58, 12, 60, 16, "__DEV__"], [58, 19, 60, 23], [58, 21, 60, 25], [59, 10, 61, 16, "console"], [59, 17, 61, 23], [59, 18, 61, 24, "warn"], [59, 22, 61, 28], [59, 23, 61, 29], [59, 63, 61, 69, "preferredType"], [59, 76, 61, 82], [59, 80, 61, 86], [59, 82, 61, 88, "nativeEvent"], [59, 93, 61, 99], [59, 94, 61, 100], [60, 8, 62, 12], [61, 8, 63, 12], [61, 12, 63, 16, "onMountError"], [61, 24, 63, 28], [61, 26, 63, 30], [62, 10, 64, 16, "onMountError"], [62, 22, 64, 28], [62, 23, 64, 29], [63, 12, 64, 31, "nativeEvent"], [64, 10, 64, 43], [64, 11, 64, 44], [64, 12, 64, 45], [65, 8, 65, 12], [66, 8, 66, 12], [66, 15, 66, 19], [66, 19, 66, 23], [67, 6, 67, 8], [68, 4, 68, 4], [68, 5, 68, 5], [68, 7, 68, 7], [68, 8, 68, 8, "preferredType"], [68, 21, 68, 21], [68, 23, 68, 23, "onMountError"], [68, 35, 68, 35], [68, 36, 68, 36], [68, 37, 68, 37], [69, 4, 69, 4], [69, 10, 69, 10, "resumeAsync"], [69, 21, 69, 21], [69, 24, 69, 24, "React"], [69, 29, 69, 29], [69, 30, 69, 30, "useCallback"], [69, 41, 69, 41], [69, 42, 69, 42], [69, 54, 69, 54], [70, 6, 70, 8], [70, 12, 70, 14, "nextStream"], [70, 22, 70, 24], [70, 25, 70, 27], [70, 31, 70, 33, "getStreamDeviceAsync"], [70, 51, 70, 53], [70, 52, 70, 54], [70, 53, 70, 55], [71, 6, 71, 8], [71, 10, 71, 12, "Utils"], [71, 15, 71, 17], [71, 16, 71, 18, "compareStreams"], [71, 30, 71, 32], [71, 31, 71, 33, "nextStream"], [71, 41, 71, 43], [71, 43, 71, 45, "stream"], [71, 49, 71, 51], [71, 50, 71, 52], [71, 52, 71, 54], [72, 8, 72, 12], [73, 8, 73, 12], [74, 8, 74, 12], [75, 8, 75, 12], [75, 15, 75, 19], [75, 20, 75, 24], [76, 6, 76, 8], [77, 6, 77, 8], [78, 6, 78, 8], [79, 6, 79, 8], [79, 10, 79, 12], [79, 11, 79, 13, "activeStreams"], [79, 24, 79, 26], [79, 25, 79, 27, "current"], [79, 32, 79, 34], [79, 33, 79, 35, "some"], [79, 37, 79, 39], [79, 38, 79, 41, "value"], [79, 43, 79, 46], [79, 47, 79, 51, "value"], [79, 52, 79, 56], [79, 53, 79, 57, "id"], [79, 55, 79, 59], [79, 60, 79, 64, "nextStream"], [79, 70, 79, 74], [79, 72, 79, 76, "id"], [79, 74, 79, 78], [79, 75, 79, 79], [79, 77, 79, 81], [80, 8, 80, 12, "activeStreams"], [80, 21, 80, 25], [80, 22, 80, 26, "current"], [80, 29, 80, 33], [80, 30, 80, 34, "push"], [80, 34, 80, 38], [80, 35, 80, 39, "nextStream"], [80, 45, 80, 49], [80, 46, 80, 50], [81, 6, 81, 8], [82, 6, 82, 8], [83, 6, 83, 8, "setStream"], [83, 15, 83, 17], [83, 16, 83, 18, "nextStream"], [83, 26, 83, 28], [83, 27, 83, 29], [84, 6, 84, 8], [84, 10, 84, 12, "onCameraReady"], [84, 23, 84, 25], [84, 25, 84, 27], [85, 8, 85, 12, "onCameraReady"], [85, 21, 85, 25], [85, 22, 85, 26], [85, 23, 85, 27], [86, 6, 86, 8], [87, 6, 87, 8], [87, 13, 87, 15], [87, 18, 87, 20], [88, 4, 88, 4], [88, 5, 88, 5], [88, 7, 88, 7], [88, 8, 88, 8, "getStreamDeviceAsync"], [88, 28, 88, 28], [88, 30, 88, 30, "setStream"], [88, 39, 88, 39], [88, 41, 88, 41, "onCameraReady"], [88, 54, 88, 54], [88, 56, 88, 56, "stream"], [88, 62, 88, 62], [88, 64, 88, 64, "activeStreams"], [88, 77, 88, 77], [88, 78, 88, 78, "current"], [88, 85, 88, 85], [88, 86, 88, 86], [88, 87, 88, 87], [89, 4, 89, 4, "React"], [89, 9, 89, 9], [89, 10, 89, 10, "useEffect"], [89, 19, 89, 19], [89, 20, 89, 20], [89, 26, 89, 26], [90, 6, 90, 8], [91, 6, 91, 8], [91, 10, 91, 12, "isStartingCamera"], [91, 26, 91, 28], [91, 27, 91, 29, "current"], [91, 34, 91, 36], [91, 36, 91, 38], [92, 8, 92, 12], [93, 6, 93, 8], [94, 6, 94, 8, "isStartingCamera"], [94, 22, 94, 24], [94, 23, 94, 25, "current"], [94, 30, 94, 32], [94, 33, 94, 35], [94, 37, 94, 39], [95, 6, 95, 8, "resumeAsync"], [95, 17, 95, 19], [95, 18, 95, 20], [95, 19, 95, 21], [95, 20, 96, 13, "then"], [95, 24, 96, 17], [95, 25, 96, 19, "isStarting"], [95, 35, 96, 29], [95, 39, 96, 34], [96, 8, 97, 12, "isStartingCamera"], [96, 24, 97, 28], [96, 25, 97, 29, "current"], [96, 32, 97, 36], [96, 35, 97, 39, "isStarting"], [96, 45, 97, 49], [97, 6, 98, 8], [97, 7, 98, 9], [97, 8, 98, 10], [97, 9, 99, 13, "catch"], [97, 14, 99, 18], [97, 15, 99, 19], [97, 21, 99, 25], [98, 8, 100, 12], [99, 8, 101, 12, "isStartingCamera"], [99, 24, 101, 28], [99, 25, 101, 29, "current"], [99, 32, 101, 36], [99, 35, 101, 39], [99, 40, 101, 44], [100, 6, 102, 8], [100, 7, 102, 9], [100, 8, 102, 10], [101, 4, 103, 4], [101, 5, 103, 5], [101, 7, 103, 7], [101, 8, 103, 8, "preferredType"], [101, 21, 103, 21], [101, 22, 103, 22], [101, 23, 103, 23], [102, 4, 104, 4], [103, 4, 105, 4, "React"], [103, 9, 105, 9], [103, 10, 105, 10, "useEffect"], [103, 19, 105, 19], [103, 20, 105, 20], [103, 26, 105, 26], [104, 6, 106, 8], [104, 12, 106, 14, "changes"], [104, 19, 106, 21], [104, 22, 106, 24], [104, 23, 106, 25], [104, 24, 106, 26], [105, 6, 107, 8], [105, 11, 107, 13], [105, 17, 107, 19, "key"], [105, 20, 107, 22], [105, 24, 107, 26, "VALID_SETTINGS_KEYS"], [105, 43, 107, 45], [105, 45, 107, 47], [106, 8, 108, 12], [106, 12, 108, 16, "key"], [106, 15, 108, 19], [106, 19, 108, 23, "settings"], [106, 27, 108, 31], [106, 29, 108, 33], [107, 10, 109, 16], [107, 16, 109, 22, "nextValue"], [107, 25, 109, 31], [107, 28, 109, 34, "settings"], [107, 36, 109, 42], [107, 37, 109, 43, "key"], [107, 40, 109, 46], [107, 41, 109, 47], [108, 10, 110, 16], [108, 14, 110, 20, "nextValue"], [108, 23, 110, 29], [108, 28, 110, 34, "capabilities"], [108, 40, 110, 46], [108, 41, 110, 47, "current"], [108, 48, 110, 54], [108, 49, 110, 55, "key"], [108, 52, 110, 58], [108, 53, 110, 59], [108, 55, 110, 61], [109, 12, 111, 20, "changes"], [109, 19, 111, 27], [109, 20, 111, 28, "key"], [109, 23, 111, 31], [109, 24, 111, 32], [109, 27, 111, 35, "nextValue"], [109, 36, 111, 44], [110, 10, 112, 16], [111, 8, 113, 12], [112, 6, 114, 8], [113, 6, 115, 8], [114, 6, 116, 8], [114, 12, 116, 14, "has<PERSON><PERSON><PERSON>"], [114, 22, 116, 24], [114, 25, 116, 27], [114, 26, 116, 28], [114, 27, 116, 29, "Object"], [114, 33, 116, 35], [114, 34, 116, 36, "keys"], [114, 38, 116, 40], [114, 39, 116, 41, "changes"], [114, 46, 116, 48], [114, 47, 116, 49], [114, 48, 116, 50, "length"], [114, 54, 116, 56], [115, 6, 117, 8], [115, 12, 117, 14, "nextWebCameraSettings"], [115, 33, 117, 35], [115, 36, 117, 38], [116, 8, 117, 40], [116, 11, 117, 43, "capabilities"], [116, 23, 117, 55], [116, 24, 117, 56, "current"], [116, 31, 117, 63], [117, 8, 117, 65], [117, 11, 117, 68, "changes"], [118, 6, 117, 76], [118, 7, 117, 77], [119, 6, 118, 8], [119, 10, 118, 12, "has<PERSON><PERSON><PERSON>"], [119, 20, 118, 22], [119, 22, 118, 24], [120, 8, 119, 12, "Utils"], [120, 13, 119, 17], [120, 14, 119, 18, "syncTrackCapabilities"], [120, 35, 119, 39], [120, 36, 119, 40, "preferredType"], [120, 49, 119, 53], [120, 51, 119, 55, "stream"], [120, 57, 119, 61], [120, 59, 119, 63, "changes"], [120, 66, 119, 70], [120, 67, 119, 71], [121, 6, 120, 8], [122, 6, 121, 8, "capabilities"], [122, 18, 121, 20], [122, 19, 121, 21, "current"], [122, 26, 121, 28], [122, 29, 121, 31, "nextWebCameraSettings"], [122, 50, 121, 52], [123, 4, 122, 4], [123, 5, 122, 5], [123, 7, 122, 7], [123, 8, 123, 8, "settings"], [123, 16, 123, 16], [123, 17, 123, 17, "autoFocus"], [123, 26, 123, 26], [123, 28, 124, 8, "settings"], [123, 36, 124, 16], [123, 37, 124, 17, "flashMode"], [123, 46, 124, 26], [123, 48, 125, 8, "settings"], [123, 56, 125, 16], [123, 57, 125, 17, "exposureCompensation"], [123, 77, 125, 37], [123, 79, 126, 8, "settings"], [123, 87, 126, 16], [123, 88, 126, 17, "colorTemperature"], [123, 104, 126, 33], [123, 106, 127, 8, "settings"], [123, 114, 127, 16], [123, 115, 127, 17, "iso"], [123, 118, 127, 20], [123, 120, 128, 8, "settings"], [123, 128, 128, 16], [123, 129, 128, 17, "brightness"], [123, 139, 128, 27], [123, 141, 129, 8, "settings"], [123, 149, 129, 16], [123, 150, 129, 17, "contrast"], [123, 158, 129, 25], [123, 160, 130, 8, "settings"], [123, 168, 130, 16], [123, 169, 130, 17, "saturation"], [123, 179, 130, 27], [123, 181, 131, 8, "settings"], [123, 189, 131, 16], [123, 190, 131, 17, "sharpness"], [123, 199, 131, 26], [123, 201, 132, 8, "settings"], [123, 209, 132, 16], [123, 210, 132, 17, "focusDistance"], [123, 223, 132, 30], [123, 225, 133, 8, "settings"], [123, 233, 133, 16], [123, 234, 133, 17, "whiteBalance"], [123, 246, 133, 29], [123, 248, 134, 8, "settings"], [123, 256, 134, 16], [123, 257, 134, 17, "zoom"], [123, 261, 134, 21], [123, 262, 135, 5], [123, 263, 135, 6], [124, 4, 136, 4, "React"], [124, 9, 136, 9], [124, 10, 136, 10, "useEffect"], [124, 19, 136, 19], [124, 20, 136, 20], [124, 26, 136, 26], [125, 6, 137, 8], [126, 6, 138, 8], [126, 10, 138, 12], [126, 11, 138, 13, "video"], [126, 16, 138, 18], [126, 17, 138, 19, "current"], [126, 24, 138, 26], [126, 26, 138, 28], [127, 8, 139, 12], [128, 6, 140, 8], [129, 6, 141, 8, "Utils"], [129, 11, 141, 13], [129, 12, 141, 14, "setVideoSource"], [129, 26, 141, 28], [129, 27, 141, 29, "video"], [129, 32, 141, 34], [129, 33, 141, 35, "current"], [129, 40, 141, 42], [129, 42, 141, 44, "stream"], [129, 48, 141, 50], [129, 49, 141, 51], [130, 4, 142, 4], [130, 5, 142, 5], [130, 7, 142, 7], [130, 8, 142, 8, "video"], [130, 13, 142, 13], [130, 14, 142, 14, "current"], [130, 21, 142, 21], [130, 23, 142, 23, "stream"], [130, 29, 142, 29], [130, 30, 142, 30], [130, 31, 142, 31], [131, 4, 143, 4, "React"], [131, 9, 143, 9], [131, 10, 143, 10, "useEffect"], [131, 19, 143, 19], [131, 20, 143, 20], [131, 26, 143, 26], [132, 6, 144, 8], [132, 13, 144, 15], [132, 19, 144, 21], [133, 8, 145, 12], [134, 8, 146, 12], [134, 13, 146, 17], [134, 19, 146, 23, "stream"], [134, 25, 146, 29], [134, 29, 146, 33, "activeStreams"], [134, 42, 146, 46], [134, 43, 146, 47, "current"], [134, 50, 146, 54], [134, 52, 146, 56], [135, 10, 147, 16], [136, 10, 148, 16, "Utils"], [136, 15, 148, 21], [136, 16, 148, 22, "stopMediaStream"], [136, 31, 148, 37], [136, 32, 148, 38, "stream"], [136, 38, 148, 44], [136, 39, 148, 45], [137, 8, 149, 12], [138, 8, 150, 12], [138, 12, 150, 16, "video"], [138, 17, 150, 21], [138, 18, 150, 22, "current"], [138, 25, 150, 29], [138, 27, 150, 31], [139, 10, 151, 16], [140, 10, 152, 16, "Utils"], [140, 15, 152, 21], [140, 16, 152, 22, "setVideoSource"], [140, 30, 152, 36], [140, 31, 152, 37, "video"], [140, 36, 152, 42], [140, 37, 152, 43, "current"], [140, 44, 152, 50], [140, 46, 152, 52, "stream"], [140, 52, 152, 58], [140, 53, 152, 59], [141, 8, 153, 12], [142, 6, 154, 8], [142, 7, 154, 9], [143, 4, 155, 4], [143, 5, 155, 5], [143, 7, 155, 7], [143, 9, 155, 9], [143, 10, 155, 10], [144, 4, 156, 4], [145, 4, 157, 4, "useLoadedVideo"], [145, 18, 157, 18], [145, 19, 157, 19, "video"], [145, 24, 157, 24], [145, 25, 157, 25, "current"], [145, 32, 157, 32], [145, 34, 157, 34], [145, 40, 157, 40], [146, 6, 158, 8, "Utils"], [146, 11, 158, 13], [146, 12, 158, 14, "syncTrackCapabilities"], [146, 33, 158, 35], [146, 34, 158, 36, "preferredType"], [146, 47, 158, 49], [146, 49, 158, 51, "stream"], [146, 55, 158, 57], [146, 57, 158, 59, "capabilities"], [146, 69, 158, 71], [146, 70, 158, 72, "current"], [146, 77, 158, 79], [146, 78, 158, 80], [147, 4, 159, 4], [147, 5, 159, 5], [147, 6, 159, 6], [148, 4, 160, 4], [148, 11, 160, 11], [149, 6, 161, 8, "type"], [149, 10, 161, 12], [150, 6, 162, 8, "mediaTrackSettings"], [151, 4, 163, 4], [151, 5, 163, 5], [152, 2, 164, 0], [153, 0, 164, 1], [153, 3]], "functionMap": {"names": ["<global>", "useLoadedVideo", "React.useEffect$argument_0", "video.addEventListener$argument_1", "requestAnimationFrame$argument_0", "useWebCameraStream", "React.useMemo$argument_0", "getStreamDeviceAsync", "resumeAsync", "activeStreams.current.some$argument_0", "resumeAsync.then$argument_0", "resumeAsync.then._catch$argument_0", "<anonymous>", "useLoadedVideo$argument_1"], "mappings": "AAA;ACkB;oBCC;qDCE;sCCI;iBDE;aDC;KDE;CDC;OKC;6CCU;KDE;+BCE;KDO;mDEC;KFa;0CGC;wCCU,sCD;KHS;oBHC;kBQO;SRE;mBSC;STG;KGC;oBHE;KGiB;oBHc;KGM;oBHC;eUC;SVU;KGC;kCQE;KRE;CLK"}}, "type": "js/module"}]}