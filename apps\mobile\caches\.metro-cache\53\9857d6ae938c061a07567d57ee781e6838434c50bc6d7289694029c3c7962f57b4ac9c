{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "PWvtvXU7MaET6Yd1Gn8oQOXJQ8A=", "exportNames": ["*"]}}, {"name": "./ImageManipulatorImageRef.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 118}, "end": {"line": 4, "column": 70, "index": 188}}], "key": "QQZbnSwpXLIftoWHZupzxzaCK6U=", "exportNames": ["*"]}}, {"name": "./actions/index.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 189}, "end": {"line": 5, "column": 73, "index": 262}}], "key": "U6OZJbqKaL+1PCYEnCSYGuR8Q6A=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expo = require(_dependencyMap[1], \"expo\");\n  var _ImageManipulatorImageRef = _interopRequireDefault(require(_dependencyMap[2], \"./ImageManipulatorImageRef.web\"));\n  var _index = require(_dependencyMap[3], \"./actions/index.web\");\n  class ImageManipulatorContext extends _expo.SharedObject {\n    get currentTask() {\n      if (this._currentTask) {\n        return this._currentTask;\n      }\n      this._currentTask = new Promise(resolve => resolve(this.loader()));\n      return this._currentTask;\n    }\n    set currentTask(task) {\n      this._currentTask = task;\n    }\n    constructor(loader) {\n      super();\n      this.loader = loader ?? (() => document.createElement('canvas'));\n    }\n    resize(size) {\n      return this.addTask(canvas => (0, _index.resize)(canvas, size));\n    }\n    rotate(degrees) {\n      return this.addTask(canvas => (0, _index.rotate)(canvas, degrees));\n    }\n    flip(flipType) {\n      return this.addTask(canvas => (0, _index.flip)(canvas, flipType));\n    }\n    crop(rect) {\n      return this.addTask(canvas => (0, _index.crop)(canvas, rect));\n    }\n    extent(options) {\n      return this.addTask(canvas => (0, _index.extent)(canvas, options));\n    }\n    reset() {\n      this.currentTask = new Promise(resolve => resolve(this.loader()));\n      return this;\n    }\n    async renderAsync() {\n      const canvas = await this.currentTask;\n\n      // We're copying the canvas so ref's `saveAsync` can safely use `toBlob` again with the desired format and quality.\n      // The original canvas cannot be reused as the manipulator context may still draw on it.\n      const clonedCanvas = document.createElement('canvas');\n      const clonedCanvasCtx = clonedCanvas.getContext('2d');\n      clonedCanvas.width = canvas.width;\n      clonedCanvas.height = canvas.height;\n      clonedCanvasCtx?.drawImage(canvas, 0, 0);\n      return new Promise(resolve => {\n        // Create a full-sized, full-quality blob from the original canvas.\n        canvas.toBlob(blob => {\n          const url = blob ? URL.createObjectURL(blob) : canvas.toDataURL();\n          const image = new _ImageManipulatorImageRef.default(url, clonedCanvas);\n          resolve(image);\n        },\n        // Use PNG format so the result is of the best quality.\n        // If you need another format, see `saveAsync` function on the image ref.\n        'image/png');\n      });\n    }\n    addTask(task) {\n      this.currentTask = this.currentTask.then(canvas => {\n        return task(canvas);\n      });\n      return this;\n    }\n  }\n  exports.default = ImageManipulatorContext;\n});", "lineCount": 74, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_expo"], [7, 11, 1, 0], [7, 14, 1, 0, "require"], [7, 21, 1, 0], [7, 22, 1, 0, "_dependencyMap"], [7, 36, 1, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_ImageManipulatorImageRef"], [8, 31, 4, 0], [8, 34, 4, 0, "_interopRequireDefault"], [8, 56, 4, 0], [8, 57, 4, 0, "require"], [8, 64, 4, 0], [8, 65, 4, 0, "_dependencyMap"], [8, 79, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_index"], [9, 12, 5, 0], [9, 15, 5, 0, "require"], [9, 22, 5, 0], [9, 23, 5, 0, "_dependencyMap"], [9, 37, 5, 0], [10, 2, 9, 15], [10, 8, 9, 21, "ImageManipulatorContext"], [10, 31, 9, 44], [10, 40, 9, 53, "SharedObject"], [10, 58, 9, 65], [10, 59, 9, 66], [11, 4, 13, 2], [11, 8, 13, 6, "currentTask"], [11, 19, 13, 17, "currentTask"], [11, 20, 13, 17], [11, 22, 13, 20], [12, 6, 14, 4], [12, 10, 14, 8], [12, 14, 14, 12], [12, 15, 14, 13, "_currentTask"], [12, 27, 14, 25], [12, 29, 14, 27], [13, 8, 15, 6], [13, 15, 15, 13], [13, 19, 15, 17], [13, 20, 15, 18, "_currentTask"], [13, 32, 15, 30], [14, 6, 16, 4], [15, 6, 17, 4], [15, 10, 17, 8], [15, 11, 17, 9, "_currentTask"], [15, 23, 17, 21], [15, 26, 17, 24], [15, 30, 17, 28, "Promise"], [15, 37, 17, 35], [15, 38, 17, 37, "resolve"], [15, 45, 17, 44], [15, 49, 17, 49, "resolve"], [15, 56, 17, 56], [15, 57, 17, 57], [15, 61, 17, 61], [15, 62, 17, 62, "loader"], [15, 68, 17, 68], [15, 69, 17, 69], [15, 70, 17, 70], [15, 71, 17, 71], [15, 72, 17, 72], [16, 6, 18, 4], [16, 13, 18, 11], [16, 17, 18, 15], [16, 18, 18, 16, "_currentTask"], [16, 30, 18, 28], [17, 4, 19, 2], [18, 4, 20, 2], [18, 8, 20, 6, "currentTask"], [18, 19, 20, 17, "currentTask"], [18, 20, 20, 18, "task"], [18, 24, 20, 22], [18, 26, 20, 24], [19, 6, 21, 4], [19, 10, 21, 8], [19, 11, 21, 9, "_currentTask"], [19, 23, 21, 21], [19, 26, 21, 24, "task"], [19, 30, 21, 28], [20, 4, 22, 2], [21, 4, 24, 2, "constructor"], [21, 15, 24, 13, "constructor"], [21, 16, 24, 14, "loader"], [21, 22, 24, 36], [21, 24, 24, 38], [22, 6, 25, 4], [22, 11, 25, 9], [22, 12, 25, 10], [22, 13, 25, 11], [23, 6, 26, 4], [23, 10, 26, 8], [23, 11, 26, 9, "loader"], [23, 17, 26, 15], [23, 20, 26, 18, "loader"], [23, 26, 26, 24], [23, 31, 26, 29], [23, 37, 26, 35, "document"], [23, 45, 26, 43], [23, 46, 26, 44, "createElement"], [23, 59, 26, 57], [23, 60, 26, 58], [23, 68, 26, 66], [23, 69, 26, 67], [23, 70, 26, 68], [24, 4, 27, 2], [25, 4, 29, 2, "resize"], [25, 10, 29, 8, "resize"], [25, 11, 29, 9, "size"], [25, 15, 29, 48], [25, 17, 29, 75], [26, 6, 30, 4], [26, 13, 30, 11], [26, 17, 30, 15], [26, 18, 30, 16, "addTask"], [26, 25, 30, 23], [26, 26, 30, 25, "canvas"], [26, 32, 30, 31], [26, 36, 30, 36], [26, 40, 30, 36, "resize"], [26, 53, 30, 42], [26, 55, 30, 43, "canvas"], [26, 61, 30, 49], [26, 63, 30, 51, "size"], [26, 67, 30, 55], [26, 68, 30, 56], [26, 69, 30, 57], [27, 4, 31, 2], [28, 4, 33, 2, "rotate"], [28, 10, 33, 8, "rotate"], [28, 11, 33, 9, "degrees"], [28, 18, 33, 24], [28, 20, 33, 51], [29, 6, 34, 4], [29, 13, 34, 11], [29, 17, 34, 15], [29, 18, 34, 16, "addTask"], [29, 25, 34, 23], [29, 26, 34, 25, "canvas"], [29, 32, 34, 31], [29, 36, 34, 36], [29, 40, 34, 36, "rotate"], [29, 53, 34, 42], [29, 55, 34, 43, "canvas"], [29, 61, 34, 49], [29, 63, 34, 51, "degrees"], [29, 70, 34, 58], [29, 71, 34, 59], [29, 72, 34, 60], [30, 4, 35, 2], [31, 4, 37, 2, "flip"], [31, 8, 37, 6, "flip"], [31, 9, 37, 7, "flipType"], [31, 17, 37, 25], [31, 19, 37, 52], [32, 6, 38, 4], [32, 13, 38, 11], [32, 17, 38, 15], [32, 18, 38, 16, "addTask"], [32, 25, 38, 23], [32, 26, 38, 25, "canvas"], [32, 32, 38, 31], [32, 36, 38, 36], [32, 40, 38, 36, "flip"], [32, 51, 38, 40], [32, 53, 38, 41, "canvas"], [32, 59, 38, 47], [32, 61, 38, 49, "flipType"], [32, 69, 38, 57], [32, 70, 38, 58], [32, 71, 38, 59], [33, 4, 39, 2], [34, 4, 41, 2, "crop"], [34, 8, 41, 6, "crop"], [34, 9, 41, 7, "rect"], [34, 13, 41, 31], [34, 15, 41, 58], [35, 6, 42, 4], [35, 13, 42, 11], [35, 17, 42, 15], [35, 18, 42, 16, "addTask"], [35, 25, 42, 23], [35, 26, 42, 25, "canvas"], [35, 32, 42, 31], [35, 36, 42, 36], [35, 40, 42, 36, "crop"], [35, 51, 42, 40], [35, 53, 42, 41, "canvas"], [35, 59, 42, 47], [35, 61, 42, 49, "rect"], [35, 65, 42, 53], [35, 66, 42, 54], [35, 67, 42, 55], [36, 4, 43, 2], [37, 4, 45, 2, "extent"], [37, 10, 45, 8, "extent"], [37, 11, 45, 9, "options"], [37, 18, 45, 40], [37, 20, 45, 67], [38, 6, 46, 4], [38, 13, 46, 11], [38, 17, 46, 15], [38, 18, 46, 16, "addTask"], [38, 25, 46, 23], [38, 26, 46, 25, "canvas"], [38, 32, 46, 31], [38, 36, 46, 36], [38, 40, 46, 36, "extent"], [38, 53, 46, 42], [38, 55, 46, 43, "canvas"], [38, 61, 46, 49], [38, 63, 46, 51, "options"], [38, 70, 46, 58], [38, 71, 46, 59], [38, 72, 46, 60], [39, 4, 47, 2], [40, 4, 49, 2, "reset"], [40, 9, 49, 7, "reset"], [40, 10, 49, 7], [40, 12, 49, 35], [41, 6, 50, 4], [41, 10, 50, 8], [41, 11, 50, 9, "currentTask"], [41, 22, 50, 20], [41, 25, 50, 23], [41, 29, 50, 27, "Promise"], [41, 36, 50, 34], [41, 37, 50, 36, "resolve"], [41, 44, 50, 43], [41, 48, 50, 48, "resolve"], [41, 55, 50, 55], [41, 56, 50, 56], [41, 60, 50, 60], [41, 61, 50, 61, "loader"], [41, 67, 50, 67], [41, 68, 50, 68], [41, 69, 50, 69], [41, 70, 50, 70], [41, 71, 50, 71], [42, 6, 51, 4], [42, 13, 51, 11], [42, 17, 51, 15], [43, 4, 52, 2], [44, 4, 54, 2], [44, 10, 54, 8, "renderAsync"], [44, 21, 54, 19, "renderAsync"], [44, 22, 54, 19], [44, 24, 54, 57], [45, 6, 55, 4], [45, 12, 55, 10, "canvas"], [45, 18, 55, 16], [45, 21, 55, 19], [45, 27, 55, 25], [45, 31, 55, 29], [45, 32, 55, 30, "currentTask"], [45, 43, 55, 41], [47, 6, 57, 4], [48, 6, 58, 4], [49, 6, 59, 4], [49, 12, 59, 10, "clonedCan<PERSON>"], [49, 24, 59, 22], [49, 27, 59, 25, "document"], [49, 35, 59, 33], [49, 36, 59, 34, "createElement"], [49, 49, 59, 47], [49, 50, 59, 48], [49, 58, 59, 56], [49, 59, 59, 57], [50, 6, 60, 4], [50, 12, 60, 10, "clonedCanvasCtx"], [50, 27, 60, 25], [50, 30, 60, 28, "clonedCan<PERSON>"], [50, 42, 60, 40], [50, 43, 60, 41, "getContext"], [50, 53, 60, 51], [50, 54, 60, 52], [50, 58, 60, 56], [50, 59, 60, 57], [51, 6, 62, 4, "clonedCan<PERSON>"], [51, 18, 62, 16], [51, 19, 62, 17, "width"], [51, 24, 62, 22], [51, 27, 62, 25, "canvas"], [51, 33, 62, 31], [51, 34, 62, 32, "width"], [51, 39, 62, 37], [52, 6, 63, 4, "clonedCan<PERSON>"], [52, 18, 63, 16], [52, 19, 63, 17, "height"], [52, 25, 63, 23], [52, 28, 63, 26, "canvas"], [52, 34, 63, 32], [52, 35, 63, 33, "height"], [52, 41, 63, 39], [53, 6, 64, 4, "clonedCanvasCtx"], [53, 21, 64, 19], [53, 23, 64, 21, "drawImage"], [53, 32, 64, 30], [53, 33, 64, 31, "canvas"], [53, 39, 64, 37], [53, 41, 64, 39], [53, 42, 64, 40], [53, 44, 64, 42], [53, 45, 64, 43], [53, 46, 64, 44], [54, 6, 66, 4], [54, 13, 66, 11], [54, 17, 66, 15, "Promise"], [54, 24, 66, 22], [54, 25, 66, 24, "resolve"], [54, 32, 66, 31], [54, 36, 66, 36], [55, 8, 67, 6], [56, 8, 68, 6, "canvas"], [56, 14, 68, 12], [56, 15, 68, 13, "toBlob"], [56, 21, 68, 19], [56, 22, 69, 9, "blob"], [56, 26, 69, 13], [56, 30, 69, 18], [57, 10, 70, 10], [57, 16, 70, 16, "url"], [57, 19, 70, 19], [57, 22, 70, 22, "blob"], [57, 26, 70, 26], [57, 29, 70, 29, "URL"], [57, 32, 70, 32], [57, 33, 70, 33, "createObjectURL"], [57, 48, 70, 48], [57, 49, 70, 49, "blob"], [57, 53, 70, 53], [57, 54, 70, 54], [57, 57, 70, 57, "canvas"], [57, 63, 70, 63], [57, 64, 70, 64, "toDataURL"], [57, 73, 70, 73], [57, 74, 70, 74], [57, 75, 70, 75], [58, 10, 71, 10], [58, 16, 71, 16, "image"], [58, 21, 71, 21], [58, 24, 71, 24], [58, 28, 71, 28, "ImageManipulatorImageRef"], [58, 61, 71, 52], [58, 62, 71, 53, "url"], [58, 65, 71, 56], [58, 67, 71, 58, "clonedCan<PERSON>"], [58, 79, 71, 70], [58, 80, 71, 71], [59, 10, 73, 10, "resolve"], [59, 17, 73, 17], [59, 18, 73, 18, "image"], [59, 23, 73, 23], [59, 24, 73, 24], [60, 8, 74, 8], [60, 9, 74, 9], [61, 8, 75, 8], [62, 8, 76, 8], [63, 8, 77, 8], [63, 19, 78, 6], [63, 20, 78, 7], [64, 6, 79, 4], [64, 7, 79, 5], [64, 8, 79, 6], [65, 4, 80, 2], [66, 4, 82, 10, "addTask"], [66, 11, 82, 17, "addTask"], [66, 12, 83, 4, "task"], [66, 16, 83, 87], [66, 18, 84, 29], [67, 6, 85, 4], [67, 10, 85, 8], [67, 11, 85, 9, "currentTask"], [67, 22, 85, 20], [67, 25, 85, 23], [67, 29, 85, 27], [67, 30, 85, 28, "currentTask"], [67, 41, 85, 39], [67, 42, 85, 40, "then"], [67, 46, 85, 44], [67, 47, 85, 46, "canvas"], [67, 53, 85, 52], [67, 57, 85, 57], [68, 8, 86, 6], [68, 15, 86, 13, "task"], [68, 19, 86, 17], [68, 20, 86, 18, "canvas"], [68, 26, 86, 24], [68, 27, 86, 25], [69, 6, 87, 4], [69, 7, 87, 5], [69, 8, 87, 6], [70, 6, 88, 4], [70, 13, 88, 11], [70, 17, 88, 15], [71, 4, 89, 2], [72, 2, 90, 0], [73, 2, 90, 1, "exports"], [73, 9, 90, 1], [73, 10, 90, 1, "default"], [73, 17, 90, 1], [73, 20, 90, 1, "ImageManipulatorContext"], [73, 43, 90, 1], [74, 0, 90, 1], [74, 3]], "functionMap": {"names": ["<global>", "ImageManipulatorContext", "get__currentTask", "Promise$argument_0", "set__currentTask", "constructor", "<anonymous>", "resize", "addTask$argument_0", "rotate", "flip", "crop", "extent", "reset", "renderAsync", "canvas.toBlob$argument_0", "addTask", "currentTask.then$argument_0"], "mappings": "AAA;eCQ;ECI;oCCI,mCD;GDE;EGC;GHE;EIE;6BCE,sCD;GJC;EME;wBCC,gCD;GNC;EQE;wBDC,mCC;GRC;ESE;wBFC,kCE;GTC;EUE;wBHC,8BG;GVC;EWE;wBJC,mCI;GXC;EYE;mCVC,mCU;GZE;EaE;uBXY;QYG;SZK;KWK;GbC;EeE;6CCG;KDE;GfE;CDC"}}, "type": "js/module"}]}