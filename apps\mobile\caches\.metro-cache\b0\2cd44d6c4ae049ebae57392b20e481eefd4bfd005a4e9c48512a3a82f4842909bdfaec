{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./Group", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 27}, "end": {"line": 2, "column": 32, "index": 59}}], "key": "mQWecd8ThPskNiKurG16ksisZsY=", "exportNames": ["*"]}}, {"name": "./colorFilters/LumaColorFilter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 60}, "end": {"line": 3, "column": 65, "index": 125}}], "key": "qKXWfNGMrpjX2NgmSyk8sKc8XvM=", "exportNames": ["*"]}}, {"name": "./Paint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 126}, "end": {"line": 4, "column": 32, "index": 158}}], "key": "tuSCRKiti4ksRJgfEb9C0tTwAWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Mask = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _Group = require(_dependencyMap[2], \"./Group\");\n  var _LumaColorFilter = require(_dependencyMap[3], \"./colorFilters/LumaColorFilter\");\n  var _Paint = require(_dependencyMap[4], \"./Paint\");\n  const Mask = ({\n    children,\n    mask,\n    mode = \"alpha\",\n    clip = true\n  }) => {\n    return /*#__PURE__*/_react.default.createElement(_Group.Group, {\n      layer: true\n    }, /*#__PURE__*/_react.default.createElement(_Group.Group, {\n      layer: /*#__PURE__*/_react.default.createElement(_Paint.Paint, {\n        blendMode: \"src\"\n      }, mode === \"luminance\" && /*#__PURE__*/_react.default.createElement(_LumaColorFilter.LumaColorFilter, null))\n    }, mask, clip && /*#__PURE__*/_react.default.createElement(_Group.Group, {\n      layer: /*#__PURE__*/_react.default.createElement(_Paint.Paint, {\n        blendMode: \"dstIn\"\n      })\n    }, children)), /*#__PURE__*/_react.default.createElement(_Group.Group, {\n      blendMode: \"srcIn\"\n    }, children));\n  };\n  exports.Mask = Mask;\n});", "lineCount": 32, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_Group"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_LumaColorFilter"], [9, 22, 3, 0], [9, 25, 3, 0, "require"], [9, 32, 3, 0], [9, 33, 3, 0, "_dependencyMap"], [9, 47, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_Paint"], [10, 12, 4, 0], [10, 15, 4, 0, "require"], [10, 22, 4, 0], [10, 23, 4, 0, "_dependencyMap"], [10, 37, 4, 0], [11, 2, 5, 7], [11, 8, 5, 13, "Mask"], [11, 12, 5, 17], [11, 15, 5, 20, "Mask"], [11, 16, 5, 21], [12, 4, 6, 2, "children"], [12, 12, 6, 10], [13, 4, 7, 2, "mask"], [13, 8, 7, 6], [14, 4, 8, 2, "mode"], [14, 8, 8, 6], [14, 11, 8, 9], [14, 18, 8, 16], [15, 4, 9, 2, "clip"], [15, 8, 9, 6], [15, 11, 9, 9], [16, 2, 10, 0], [16, 3, 10, 1], [16, 8, 10, 6], [17, 4, 11, 2], [17, 11, 11, 9], [17, 24, 11, 22, "React"], [17, 38, 11, 27], [17, 39, 11, 28, "createElement"], [17, 52, 11, 41], [17, 53, 11, 42, "Group"], [17, 65, 11, 47], [17, 67, 11, 49], [18, 6, 12, 4, "layer"], [18, 11, 12, 9], [18, 13, 12, 11], [19, 4, 13, 2], [19, 5, 13, 3], [19, 7, 13, 5], [19, 20, 13, 18, "React"], [19, 34, 13, 23], [19, 35, 13, 24, "createElement"], [19, 48, 13, 37], [19, 49, 13, 38, "Group"], [19, 61, 13, 43], [19, 63, 13, 45], [20, 6, 14, 4, "layer"], [20, 11, 14, 9], [20, 13, 14, 11], [20, 26, 14, 24, "React"], [20, 40, 14, 29], [20, 41, 14, 30, "createElement"], [20, 54, 14, 43], [20, 55, 14, 44, "Paint"], [20, 67, 14, 49], [20, 69, 14, 51], [21, 8, 15, 6, "blendMode"], [21, 17, 15, 15], [21, 19, 15, 17], [22, 6, 16, 4], [22, 7, 16, 5], [22, 9, 16, 7, "mode"], [22, 13, 16, 11], [22, 18, 16, 16], [22, 29, 16, 27], [22, 33, 16, 31], [22, 46, 16, 44, "React"], [22, 60, 16, 49], [22, 61, 16, 50, "createElement"], [22, 74, 16, 63], [22, 75, 16, 64, "LumaColorFilter"], [22, 107, 16, 79], [22, 109, 16, 81], [22, 113, 16, 85], [22, 114, 16, 86], [23, 4, 17, 2], [23, 5, 17, 3], [23, 7, 17, 5, "mask"], [23, 11, 17, 9], [23, 13, 17, 11, "clip"], [23, 17, 17, 15], [23, 21, 17, 19], [23, 34, 17, 32, "React"], [23, 48, 17, 37], [23, 49, 17, 38, "createElement"], [23, 62, 17, 51], [23, 63, 17, 52, "Group"], [23, 75, 17, 57], [23, 77, 17, 59], [24, 6, 18, 4, "layer"], [24, 11, 18, 9], [24, 13, 18, 11], [24, 26, 18, 24, "React"], [24, 40, 18, 29], [24, 41, 18, 30, "createElement"], [24, 54, 18, 43], [24, 55, 18, 44, "Paint"], [24, 67, 18, 49], [24, 69, 18, 51], [25, 8, 19, 6, "blendMode"], [25, 17, 19, 15], [25, 19, 19, 17], [26, 6, 20, 4], [26, 7, 20, 5], [27, 4, 21, 2], [27, 5, 21, 3], [27, 7, 21, 5, "children"], [27, 15, 21, 13], [27, 16, 21, 14], [27, 17, 21, 15], [27, 19, 21, 17], [27, 32, 21, 30, "React"], [27, 46, 21, 35], [27, 47, 21, 36, "createElement"], [27, 60, 21, 49], [27, 61, 21, 50, "Group"], [27, 73, 21, 55], [27, 75, 21, 57], [28, 6, 22, 4, "blendMode"], [28, 15, 22, 13], [28, 17, 22, 15], [29, 4, 23, 2], [29, 5, 23, 3], [29, 7, 23, 5, "children"], [29, 15, 23, 13], [29, 16, 23, 14], [29, 17, 23, 15], [30, 2, 24, 0], [30, 3, 24, 1], [31, 2, 24, 2, "exports"], [31, 9, 24, 2], [31, 10, 24, 2, "Mask"], [31, 14, 24, 2], [31, 17, 24, 2, "Mask"], [31, 21, 24, 2], [32, 0, 24, 2], [32, 3]], "functionMap": {"names": ["<global>", "Mask"], "mappings": "AAA;oBCI;CDmB"}}, "type": "js/module"}]}