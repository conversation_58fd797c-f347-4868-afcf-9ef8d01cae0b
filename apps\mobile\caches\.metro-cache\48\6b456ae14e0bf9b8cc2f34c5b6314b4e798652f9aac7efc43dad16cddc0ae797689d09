{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 45, "index": 647}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkColorFilter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 648}, "end": {"line": 5, "column": 54, "index": 702}}], "key": "zQChm2irMCKhUBxvb1hmNVoer2A=", "exportNames": ["*"]}}, {"name": "./JsiSkImageFilter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 703}, "end": {"line": 6, "column": 54, "index": 757}}], "key": "wZakwk1fGNWxYRxu1UpAHXjAB8M=", "exportNames": ["*"]}}, {"name": "./JsiSkMaskFilter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 758}, "end": {"line": 7, "column": 52, "index": 810}}], "key": "0iOo+R4nBiiKEN7Otoa3llJz2BA=", "exportNames": ["*"]}}, {"name": "./JsiSkPathEffect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 811}, "end": {"line": 8, "column": 52, "index": 863}}], "key": "vX5UcJL7b59BiVotyhwArfDZk24=", "exportNames": ["*"]}}, {"name": "./JsiSkShader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 864}, "end": {"line": 9, "column": 44, "index": 908}}], "key": "qmH0e2X2qhdhK7DOZEHi4mFPbz8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPaint = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkColorFilter = require(_dependencyMap[1], \"./JsiSkColorFilter\");\n  var _JsiSkImageFilter = require(_dependencyMap[2], \"./JsiSkImageFilter\");\n  var _JsiSkMaskFilter = require(_dependencyMap[3], \"./JsiSkMaskFilter\");\n  var _JsiSkPathEffect = require(_dependencyMap[4], \"./JsiSkPathEffect\");\n  var _JsiSkShader = require(_dependencyMap[5], \"./JsiSkShader\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkPaint extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Paint\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    copy() {\n      return new JsiSkPaint(this.CanvasKit, this.ref.copy());\n    }\n    assign(paint) {\n      this.ref = paint.ref.copy();\n    }\n    reset() {\n      this.ref = new this.CanvasKit.Paint();\n    }\n    getAlphaf() {\n      return this.getColor()[3];\n    }\n    getColor() {\n      return this.ref.getColor();\n    }\n    getStrokeCap() {\n      return this.ref.getStrokeCap().value;\n    }\n    getStrokeJoin() {\n      return this.ref.getStrokeJoin().value;\n    }\n    getStrokeMiter() {\n      return this.ref.getStrokeMiter();\n    }\n    getStrokeWidth() {\n      return this.ref.getStrokeWidth();\n    }\n    setAlphaf(alpha) {\n      this.ref.setAlphaf(alpha);\n    }\n    setAntiAlias(aa) {\n      this.ref.setAntiAlias(aa);\n    }\n    setDither(dither) {\n      this.ref.setDither(dither);\n    }\n    setBlendMode(blendMode) {\n      this.ref.setBlendMode((0, _Host.getEnum)(this.CanvasKit, \"BlendMode\", blendMode));\n    }\n    setColor(color) {\n      this.ref.setColor(color);\n    }\n    setColorFilter(filter) {\n      this.ref.setColorFilter(filter ? _JsiSkColorFilter.JsiSkColorFilter.fromValue(filter) : null);\n    }\n    setImageFilter(filter) {\n      this.ref.setImageFilter(filter ? _JsiSkImageFilter.JsiSkImageFilter.fromValue(filter) : null);\n    }\n    setMaskFilter(filter) {\n      this.ref.setMaskFilter(filter ? _JsiSkMaskFilter.JsiSkMaskFilter.fromValue(filter) : null);\n    }\n    setPathEffect(effect) {\n      this.ref.setPathEffect(effect ? _JsiSkPathEffect.JsiSkPathEffect.fromValue(effect) : null);\n    }\n    setShader(shader) {\n      this.ref.setShader(shader ? _JsiSkShader.JsiSkShader.fromValue(shader) : null);\n    }\n    setStrokeCap(cap) {\n      this.ref.setStrokeCap((0, _Host.getEnum)(this.CanvasKit, \"StrokeCap\", cap));\n    }\n    setStrokeJoin(join) {\n      this.ref.setStrokeJoin((0, _Host.getEnum)(this.CanvasKit, \"StrokeJoin\", join));\n    }\n    setStrokeMiter(limit) {\n      this.ref.setStrokeMiter(limit);\n    }\n    setStrokeWidth(width) {\n      this.ref.setStrokeWidth(width);\n    }\n    setStyle(style) {\n      this.ref.setStyle({\n        value: style\n      });\n    }\n  }\n  exports.JsiSkPaint = JsiSkPaint;\n});", "lineCount": 117, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkColorFilter"], [7, 23, 5, 0], [7, 26, 5, 0, "require"], [7, 33, 5, 0], [7, 34, 5, 0, "_dependencyMap"], [7, 48, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkImageFilter"], [8, 23, 6, 0], [8, 26, 6, 0, "require"], [8, 33, 6, 0], [8, 34, 6, 0, "_dependencyMap"], [8, 48, 6, 0], [9, 2, 7, 0], [9, 6, 7, 0, "_JsiSkMaskFilter"], [9, 22, 7, 0], [9, 25, 7, 0, "require"], [9, 32, 7, 0], [9, 33, 7, 0, "_dependencyMap"], [9, 47, 7, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_JsiSkPathEffect"], [10, 22, 8, 0], [10, 25, 8, 0, "require"], [10, 32, 8, 0], [10, 33, 8, 0, "_dependencyMap"], [10, 47, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_JsiSkShader"], [11, 18, 9, 0], [11, 21, 9, 0, "require"], [11, 28, 9, 0], [11, 29, 9, 0, "_dependencyMap"], [11, 43, 9, 0], [12, 2, 1, 0], [12, 11, 1, 9, "_defineProperty"], [12, 26, 1, 24, "_defineProperty"], [12, 27, 1, 25, "e"], [12, 28, 1, 26], [12, 30, 1, 28, "r"], [12, 31, 1, 29], [12, 33, 1, 31, "t"], [12, 34, 1, 32], [12, 36, 1, 34], [13, 4, 1, 36], [13, 11, 1, 43], [13, 12, 1, 44, "r"], [13, 13, 1, 45], [13, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [13, 30, 1, 62], [13, 31, 1, 63, "r"], [13, 32, 1, 64], [13, 33, 1, 65], [13, 38, 1, 70, "e"], [13, 39, 1, 71], [13, 42, 1, 74, "Object"], [13, 48, 1, 80], [13, 49, 1, 81, "defineProperty"], [13, 63, 1, 95], [13, 64, 1, 96, "e"], [13, 65, 1, 97], [13, 67, 1, 99, "r"], [13, 68, 1, 100], [13, 70, 1, 102], [14, 6, 1, 104, "value"], [14, 11, 1, 109], [14, 13, 1, 111, "t"], [14, 14, 1, 112], [15, 6, 1, 114, "enumerable"], [15, 16, 1, 124], [15, 18, 1, 126], [15, 19, 1, 127], [15, 20, 1, 128], [16, 6, 1, 130, "configurable"], [16, 18, 1, 142], [16, 20, 1, 144], [16, 21, 1, 145], [16, 22, 1, 146], [17, 6, 1, 148, "writable"], [17, 14, 1, 156], [17, 16, 1, 158], [17, 17, 1, 159], [18, 4, 1, 161], [18, 5, 1, 162], [18, 6, 1, 163], [18, 9, 1, 166, "e"], [18, 10, 1, 167], [18, 11, 1, 168, "r"], [18, 12, 1, 169], [18, 13, 1, 170], [18, 16, 1, 173, "t"], [18, 17, 1, 174], [18, 19, 1, 176, "e"], [18, 20, 1, 177], [19, 2, 1, 179], [20, 2, 2, 0], [20, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 26, 2, 24, "t"], [20, 27, 2, 25], [20, 29, 2, 27], [21, 4, 2, 29], [21, 8, 2, 33, "i"], [21, 9, 2, 34], [21, 12, 2, 37, "_toPrimitive"], [21, 24, 2, 49], [21, 25, 2, 50, "t"], [21, 26, 2, 51], [21, 28, 2, 53], [21, 36, 2, 61], [21, 37, 2, 62], [22, 4, 2, 64], [22, 11, 2, 71], [22, 19, 2, 79], [22, 23, 2, 83], [22, 30, 2, 90, "i"], [22, 31, 2, 91], [22, 34, 2, 94, "i"], [22, 35, 2, 95], [22, 38, 2, 98, "i"], [22, 39, 2, 99], [22, 42, 2, 102], [22, 44, 2, 104], [23, 2, 2, 106], [24, 2, 3, 0], [24, 11, 3, 9, "_toPrimitive"], [24, 23, 3, 21, "_toPrimitive"], [24, 24, 3, 22, "t"], [24, 25, 3, 23], [24, 27, 3, 25, "r"], [24, 28, 3, 26], [24, 30, 3, 28], [25, 4, 3, 30], [25, 8, 3, 34], [25, 16, 3, 42], [25, 20, 3, 46], [25, 27, 3, 53, "t"], [25, 28, 3, 54], [25, 32, 3, 58], [25, 33, 3, 59, "t"], [25, 34, 3, 60], [25, 36, 3, 62], [25, 43, 3, 69, "t"], [25, 44, 3, 70], [26, 4, 3, 72], [26, 8, 3, 76, "e"], [26, 9, 3, 77], [26, 12, 3, 80, "t"], [26, 13, 3, 81], [26, 14, 3, 82, "Symbol"], [26, 20, 3, 88], [26, 21, 3, 89, "toPrimitive"], [26, 32, 3, 100], [26, 33, 3, 101], [27, 4, 3, 103], [27, 8, 3, 107], [27, 13, 3, 112], [27, 14, 3, 113], [27, 19, 3, 118, "e"], [27, 20, 3, 119], [27, 22, 3, 121], [28, 6, 3, 123], [28, 10, 3, 127, "i"], [28, 11, 3, 128], [28, 14, 3, 131, "e"], [28, 15, 3, 132], [28, 16, 3, 133, "call"], [28, 20, 3, 137], [28, 21, 3, 138, "t"], [28, 22, 3, 139], [28, 24, 3, 141, "r"], [28, 25, 3, 142], [28, 29, 3, 146], [28, 38, 3, 155], [28, 39, 3, 156], [29, 6, 3, 158], [29, 10, 3, 162], [29, 18, 3, 170], [29, 22, 3, 174], [29, 29, 3, 181, "i"], [29, 30, 3, 182], [29, 32, 3, 184], [29, 39, 3, 191, "i"], [29, 40, 3, 192], [30, 6, 3, 194], [30, 12, 3, 200], [30, 16, 3, 204, "TypeError"], [30, 25, 3, 213], [30, 26, 3, 214], [30, 72, 3, 260], [30, 73, 3, 261], [31, 4, 3, 263], [32, 4, 3, 265], [32, 11, 3, 272], [32, 12, 3, 273], [32, 20, 3, 281], [32, 25, 3, 286, "r"], [32, 26, 3, 287], [32, 29, 3, 290, "String"], [32, 35, 3, 296], [32, 38, 3, 299, "Number"], [32, 44, 3, 305], [32, 46, 3, 307, "t"], [32, 47, 3, 308], [32, 48, 3, 309], [33, 2, 3, 311], [34, 2, 10, 7], [34, 8, 10, 13, "JsiSkPaint"], [34, 18, 10, 23], [34, 27, 10, 32, "HostObject"], [34, 43, 10, 42], [34, 44, 10, 43], [35, 4, 11, 2, "constructor"], [35, 15, 11, 13, "constructor"], [35, 16, 11, 14, "CanvasKit"], [35, 25, 11, 23], [35, 27, 11, 25, "ref"], [35, 30, 11, 28], [35, 32, 11, 30], [36, 6, 12, 4], [36, 11, 12, 9], [36, 12, 12, 10, "CanvasKit"], [36, 21, 12, 19], [36, 23, 12, 21, "ref"], [36, 26, 12, 24], [36, 28, 12, 26], [36, 35, 12, 33], [36, 36, 12, 34], [37, 6, 13, 4, "_defineProperty"], [37, 21, 13, 19], [37, 22, 13, 20], [37, 26, 13, 24], [37, 28, 13, 26], [37, 37, 13, 35], [37, 39, 13, 37], [37, 45, 13, 43], [38, 8, 14, 6], [38, 12, 14, 10], [38, 13, 14, 11, "ref"], [38, 16, 14, 14], [38, 17, 14, 15, "delete"], [38, 23, 14, 21], [38, 24, 14, 22], [38, 25, 14, 23], [39, 6, 15, 4], [39, 7, 15, 5], [39, 8, 15, 6], [40, 4, 16, 2], [41, 4, 17, 2, "copy"], [41, 8, 17, 6, "copy"], [41, 9, 17, 6], [41, 11, 17, 9], [42, 6, 18, 4], [42, 13, 18, 11], [42, 17, 18, 15, "JsiSkPaint"], [42, 27, 18, 25], [42, 28, 18, 26], [42, 32, 18, 30], [42, 33, 18, 31, "CanvasKit"], [42, 42, 18, 40], [42, 44, 18, 42], [42, 48, 18, 46], [42, 49, 18, 47, "ref"], [42, 52, 18, 50], [42, 53, 18, 51, "copy"], [42, 57, 18, 55], [42, 58, 18, 56], [42, 59, 18, 57], [42, 60, 18, 58], [43, 4, 19, 2], [44, 4, 20, 2, "assign"], [44, 10, 20, 8, "assign"], [44, 11, 20, 9, "paint"], [44, 16, 20, 14], [44, 18, 20, 16], [45, 6, 21, 4], [45, 10, 21, 8], [45, 11, 21, 9, "ref"], [45, 14, 21, 12], [45, 17, 21, 15, "paint"], [45, 22, 21, 20], [45, 23, 21, 21, "ref"], [45, 26, 21, 24], [45, 27, 21, 25, "copy"], [45, 31, 21, 29], [45, 32, 21, 30], [45, 33, 21, 31], [46, 4, 22, 2], [47, 4, 23, 2, "reset"], [47, 9, 23, 7, "reset"], [47, 10, 23, 7], [47, 12, 23, 10], [48, 6, 24, 4], [48, 10, 24, 8], [48, 11, 24, 9, "ref"], [48, 14, 24, 12], [48, 17, 24, 15], [48, 21, 24, 19], [48, 25, 24, 23], [48, 26, 24, 24, "CanvasKit"], [48, 35, 24, 33], [48, 36, 24, 34, "Paint"], [48, 41, 24, 39], [48, 42, 24, 40], [48, 43, 24, 41], [49, 4, 25, 2], [50, 4, 26, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [50, 13, 26, 11, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [50, 14, 26, 11], [50, 16, 26, 14], [51, 6, 27, 4], [51, 13, 27, 11], [51, 17, 27, 15], [51, 18, 27, 16, "getColor"], [51, 26, 27, 24], [51, 27, 27, 25], [51, 28, 27, 26], [51, 29, 27, 27], [51, 30, 27, 28], [51, 31, 27, 29], [52, 4, 28, 2], [53, 4, 29, 2, "getColor"], [53, 12, 29, 10, "getColor"], [53, 13, 29, 10], [53, 15, 29, 13], [54, 6, 30, 4], [54, 13, 30, 11], [54, 17, 30, 15], [54, 18, 30, 16, "ref"], [54, 21, 30, 19], [54, 22, 30, 20, "getColor"], [54, 30, 30, 28], [54, 31, 30, 29], [54, 32, 30, 30], [55, 4, 31, 2], [56, 4, 32, 2, "getStrokeCap"], [56, 16, 32, 14, "getStrokeCap"], [56, 17, 32, 14], [56, 19, 32, 17], [57, 6, 33, 4], [57, 13, 33, 11], [57, 17, 33, 15], [57, 18, 33, 16, "ref"], [57, 21, 33, 19], [57, 22, 33, 20, "getStrokeCap"], [57, 34, 33, 32], [57, 35, 33, 33], [57, 36, 33, 34], [57, 37, 33, 35, "value"], [57, 42, 33, 40], [58, 4, 34, 2], [59, 4, 35, 2, "getStroke<PERSON>oin"], [59, 17, 35, 15, "getStroke<PERSON>oin"], [59, 18, 35, 15], [59, 20, 35, 18], [60, 6, 36, 4], [60, 13, 36, 11], [60, 17, 36, 15], [60, 18, 36, 16, "ref"], [60, 21, 36, 19], [60, 22, 36, 20, "getStroke<PERSON>oin"], [60, 35, 36, 33], [60, 36, 36, 34], [60, 37, 36, 35], [60, 38, 36, 36, "value"], [60, 43, 36, 41], [61, 4, 37, 2], [62, 4, 38, 2, "getStrokeMiter"], [62, 18, 38, 16, "getStrokeMiter"], [62, 19, 38, 16], [62, 21, 38, 19], [63, 6, 39, 4], [63, 13, 39, 11], [63, 17, 39, 15], [63, 18, 39, 16, "ref"], [63, 21, 39, 19], [63, 22, 39, 20, "getStrokeMiter"], [63, 36, 39, 34], [63, 37, 39, 35], [63, 38, 39, 36], [64, 4, 40, 2], [65, 4, 41, 2, "getStrokeWidth"], [65, 18, 41, 16, "getStrokeWidth"], [65, 19, 41, 16], [65, 21, 41, 19], [66, 6, 42, 4], [66, 13, 42, 11], [66, 17, 42, 15], [66, 18, 42, 16, "ref"], [66, 21, 42, 19], [66, 22, 42, 20, "getStrokeWidth"], [66, 36, 42, 34], [66, 37, 42, 35], [66, 38, 42, 36], [67, 4, 43, 2], [68, 4, 44, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [68, 13, 44, 11, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [68, 14, 44, 12, "alpha"], [68, 19, 44, 17], [68, 21, 44, 19], [69, 6, 45, 4], [69, 10, 45, 8], [69, 11, 45, 9, "ref"], [69, 14, 45, 12], [69, 15, 45, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [69, 24, 45, 22], [69, 25, 45, 23, "alpha"], [69, 30, 45, 28], [69, 31, 45, 29], [70, 4, 46, 2], [71, 4, 47, 2, "set<PERSON>nti<PERSON><PERSON><PERSON>"], [71, 16, 47, 14, "set<PERSON>nti<PERSON><PERSON><PERSON>"], [71, 17, 47, 15, "aa"], [71, 19, 47, 17], [71, 21, 47, 19], [72, 6, 48, 4], [72, 10, 48, 8], [72, 11, 48, 9, "ref"], [72, 14, 48, 12], [72, 15, 48, 13, "set<PERSON>nti<PERSON><PERSON><PERSON>"], [72, 27, 48, 25], [72, 28, 48, 26, "aa"], [72, 30, 48, 28], [72, 31, 48, 29], [73, 4, 49, 2], [74, 4, 50, 2, "<PERSON><PERSON><PERSON><PERSON>"], [74, 13, 50, 11, "<PERSON><PERSON><PERSON><PERSON>"], [74, 14, 50, 12, "dither"], [74, 20, 50, 18], [74, 22, 50, 20], [75, 6, 51, 4], [75, 10, 51, 8], [75, 11, 51, 9, "ref"], [75, 14, 51, 12], [75, 15, 51, 13, "<PERSON><PERSON><PERSON><PERSON>"], [75, 24, 51, 22], [75, 25, 51, 23, "dither"], [75, 31, 51, 29], [75, 32, 51, 30], [76, 4, 52, 2], [77, 4, 53, 2, "setBlendMode"], [77, 16, 53, 14, "setBlendMode"], [77, 17, 53, 15, "blendMode"], [77, 26, 53, 24], [77, 28, 53, 26], [78, 6, 54, 4], [78, 10, 54, 8], [78, 11, 54, 9, "ref"], [78, 14, 54, 12], [78, 15, 54, 13, "setBlendMode"], [78, 27, 54, 25], [78, 28, 54, 26], [78, 32, 54, 26, "getEnum"], [78, 45, 54, 33], [78, 47, 54, 34], [78, 51, 54, 38], [78, 52, 54, 39, "CanvasKit"], [78, 61, 54, 48], [78, 63, 54, 50], [78, 74, 54, 61], [78, 76, 54, 63, "blendMode"], [78, 85, 54, 72], [78, 86, 54, 73], [78, 87, 54, 74], [79, 4, 55, 2], [80, 4, 56, 2, "setColor"], [80, 12, 56, 10, "setColor"], [80, 13, 56, 11, "color"], [80, 18, 56, 16], [80, 20, 56, 18], [81, 6, 57, 4], [81, 10, 57, 8], [81, 11, 57, 9, "ref"], [81, 14, 57, 12], [81, 15, 57, 13, "setColor"], [81, 23, 57, 21], [81, 24, 57, 22, "color"], [81, 29, 57, 27], [81, 30, 57, 28], [82, 4, 58, 2], [83, 4, 59, 2, "setColorFilter"], [83, 18, 59, 16, "setColorFilter"], [83, 19, 59, 17, "filter"], [83, 25, 59, 23], [83, 27, 59, 25], [84, 6, 60, 4], [84, 10, 60, 8], [84, 11, 60, 9, "ref"], [84, 14, 60, 12], [84, 15, 60, 13, "setColorFilter"], [84, 29, 60, 27], [84, 30, 60, 28, "filter"], [84, 36, 60, 34], [84, 39, 60, 37, "JsiSkColorFilter"], [84, 73, 60, 53], [84, 74, 60, 54, "fromValue"], [84, 83, 60, 63], [84, 84, 60, 64, "filter"], [84, 90, 60, 70], [84, 91, 60, 71], [84, 94, 60, 74], [84, 98, 60, 78], [84, 99, 60, 79], [85, 4, 61, 2], [86, 4, 62, 2, "setImageFilter"], [86, 18, 62, 16, "setImageFilter"], [86, 19, 62, 17, "filter"], [86, 25, 62, 23], [86, 27, 62, 25], [87, 6, 63, 4], [87, 10, 63, 8], [87, 11, 63, 9, "ref"], [87, 14, 63, 12], [87, 15, 63, 13, "setImageFilter"], [87, 29, 63, 27], [87, 30, 63, 28, "filter"], [87, 36, 63, 34], [87, 39, 63, 37, "JsiSkImageFilter"], [87, 73, 63, 53], [87, 74, 63, 54, "fromValue"], [87, 83, 63, 63], [87, 84, 63, 64, "filter"], [87, 90, 63, 70], [87, 91, 63, 71], [87, 94, 63, 74], [87, 98, 63, 78], [87, 99, 63, 79], [88, 4, 64, 2], [89, 4, 65, 2, "setMaskFilter"], [89, 17, 65, 15, "setMaskFilter"], [89, 18, 65, 16, "filter"], [89, 24, 65, 22], [89, 26, 65, 24], [90, 6, 66, 4], [90, 10, 66, 8], [90, 11, 66, 9, "ref"], [90, 14, 66, 12], [90, 15, 66, 13, "setMaskFilter"], [90, 28, 66, 26], [90, 29, 66, 27, "filter"], [90, 35, 66, 33], [90, 38, 66, 36, "JsiSkMaskFilter"], [90, 70, 66, 51], [90, 71, 66, 52, "fromValue"], [90, 80, 66, 61], [90, 81, 66, 62, "filter"], [90, 87, 66, 68], [90, 88, 66, 69], [90, 91, 66, 72], [90, 95, 66, 76], [90, 96, 66, 77], [91, 4, 67, 2], [92, 4, 68, 2, "setPathEffect"], [92, 17, 68, 15, "setPathEffect"], [92, 18, 68, 16, "effect"], [92, 24, 68, 22], [92, 26, 68, 24], [93, 6, 69, 4], [93, 10, 69, 8], [93, 11, 69, 9, "ref"], [93, 14, 69, 12], [93, 15, 69, 13, "setPathEffect"], [93, 28, 69, 26], [93, 29, 69, 27, "effect"], [93, 35, 69, 33], [93, 38, 69, 36, "JsiSkPathEffect"], [93, 70, 69, 51], [93, 71, 69, 52, "fromValue"], [93, 80, 69, 61], [93, 81, 69, 62, "effect"], [93, 87, 69, 68], [93, 88, 69, 69], [93, 91, 69, 72], [93, 95, 69, 76], [93, 96, 69, 77], [94, 4, 70, 2], [95, 4, 71, 2, "<PERSON><PERSON><PERSON><PERSON>"], [95, 13, 71, 11, "<PERSON><PERSON><PERSON><PERSON>"], [95, 14, 71, 12, "shader"], [95, 20, 71, 18], [95, 22, 71, 20], [96, 6, 72, 4], [96, 10, 72, 8], [96, 11, 72, 9, "ref"], [96, 14, 72, 12], [96, 15, 72, 13, "<PERSON><PERSON><PERSON><PERSON>"], [96, 24, 72, 22], [96, 25, 72, 23, "shader"], [96, 31, 72, 29], [96, 34, 72, 32, "JsiSkShader"], [96, 58, 72, 43], [96, 59, 72, 44, "fromValue"], [96, 68, 72, 53], [96, 69, 72, 54, "shader"], [96, 75, 72, 60], [96, 76, 72, 61], [96, 79, 72, 64], [96, 83, 72, 68], [96, 84, 72, 69], [97, 4, 73, 2], [98, 4, 74, 2, "setStrokeCap"], [98, 16, 74, 14, "setStrokeCap"], [98, 17, 74, 15, "cap"], [98, 20, 74, 18], [98, 22, 74, 20], [99, 6, 75, 4], [99, 10, 75, 8], [99, 11, 75, 9, "ref"], [99, 14, 75, 12], [99, 15, 75, 13, "setStrokeCap"], [99, 27, 75, 25], [99, 28, 75, 26], [99, 32, 75, 26, "getEnum"], [99, 45, 75, 33], [99, 47, 75, 34], [99, 51, 75, 38], [99, 52, 75, 39, "CanvasKit"], [99, 61, 75, 48], [99, 63, 75, 50], [99, 74, 75, 61], [99, 76, 75, 63, "cap"], [99, 79, 75, 66], [99, 80, 75, 67], [99, 81, 75, 68], [100, 4, 76, 2], [101, 4, 77, 2, "set<PERSON><PERSON><PERSON><PERSON><PERSON>"], [101, 17, 77, 15, "set<PERSON><PERSON><PERSON><PERSON><PERSON>"], [101, 18, 77, 16, "join"], [101, 22, 77, 20], [101, 24, 77, 22], [102, 6, 78, 4], [102, 10, 78, 8], [102, 11, 78, 9, "ref"], [102, 14, 78, 12], [102, 15, 78, 13, "set<PERSON><PERSON><PERSON><PERSON><PERSON>"], [102, 28, 78, 26], [102, 29, 78, 27], [102, 33, 78, 27, "getEnum"], [102, 46, 78, 34], [102, 48, 78, 35], [102, 52, 78, 39], [102, 53, 78, 40, "CanvasKit"], [102, 62, 78, 49], [102, 64, 78, 51], [102, 76, 78, 63], [102, 78, 78, 65, "join"], [102, 82, 78, 69], [102, 83, 78, 70], [102, 84, 78, 71], [103, 4, 79, 2], [104, 4, 80, 2, "setStrokeMiter"], [104, 18, 80, 16, "setStrokeMiter"], [104, 19, 80, 17, "limit"], [104, 24, 80, 22], [104, 26, 80, 24], [105, 6, 81, 4], [105, 10, 81, 8], [105, 11, 81, 9, "ref"], [105, 14, 81, 12], [105, 15, 81, 13, "setStrokeMiter"], [105, 29, 81, 27], [105, 30, 81, 28, "limit"], [105, 35, 81, 33], [105, 36, 81, 34], [106, 4, 82, 2], [107, 4, 83, 2, "setStrokeWidth"], [107, 18, 83, 16, "setStrokeWidth"], [107, 19, 83, 17, "width"], [107, 24, 83, 22], [107, 26, 83, 24], [108, 6, 84, 4], [108, 10, 84, 8], [108, 11, 84, 9, "ref"], [108, 14, 84, 12], [108, 15, 84, 13, "setStrokeWidth"], [108, 29, 84, 27], [108, 30, 84, 28, "width"], [108, 35, 84, 33], [108, 36, 84, 34], [109, 4, 85, 2], [110, 4, 86, 2, "setStyle"], [110, 12, 86, 10, "setStyle"], [110, 13, 86, 11, "style"], [110, 18, 86, 16], [110, 20, 86, 18], [111, 6, 87, 4], [111, 10, 87, 8], [111, 11, 87, 9, "ref"], [111, 14, 87, 12], [111, 15, 87, 13, "setStyle"], [111, 23, 87, 21], [111, 24, 87, 22], [112, 8, 88, 6, "value"], [112, 13, 88, 11], [112, 15, 88, 13, "style"], [113, 6, 89, 4], [113, 7, 89, 5], [113, 8, 89, 6], [114, 4, 90, 2], [115, 2, 91, 0], [116, 2, 91, 1, "exports"], [116, 9, 91, 1], [116, 10, 91, 1, "JsiSkPaint"], [116, 20, 91, 1], [116, 23, 91, 1, "JsiSkPaint"], [116, 33, 91, 1], [117, 0, 91, 1], [117, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkPaint", "constructor", "_defineProperty$argument_2", "copy", "assign", "reset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getColor", "getStrokeCap", "getStroke<PERSON>oin", "getStrokeMiter", "getStrokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON>nti<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setBlendMode", "setColor", "setColorFilter", "setImageFilter", "setMaskFilter", "setPathEffect", "<PERSON><PERSON><PERSON><PERSON>", "setStrokeCap", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "setStrokeMiter", "setStrokeWidth", "setStyle"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGO;ECC;qCCE;KDE;GDC;EGC;GHE;EIC;GJE;EKC;GLE;EMC;GNE;EOC;GPE;EQC;GRE;ESC;GTE;EUC;GVE;EWC;GXE;EYC;GZE;EaC;GbE;EcC;GdE;EeC;GfE;EgBC;GhBE;EiBC;GjBE;EkBC;GlBE;EmBC;GnBE;EoBC;GpBE;EqBC;GrBE;EsBC;GtBE;EuBC;GvBE;EwBC;GxBE;EyBC;GzBE;E0BC;G1BI;CHC"}}, "type": "js/module"}]}