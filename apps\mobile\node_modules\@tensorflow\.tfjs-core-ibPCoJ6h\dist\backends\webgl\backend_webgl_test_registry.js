"use strict";
/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
var jasmine_util_1 = require("../../jasmine_util");
exports.WEBGL_ENVS = {
    predicate: function (testEnv) { return testEnv.backendName === 'webgl'; }
};
exports.PACKED_ENVS = {
    flags: { 'WEBGL_PACK': true }
};
jasmine_util_1.registerTestEnv({
    name: 'webgl1',
    backendName: 'webgl',
    flags: {
        'WEBGL_VERSION': 1,
        'WEBGL_CPU_FORWARD': false,
        'WEBGL_SIZE_UPLOAD_UNIFORM': 0
    },
    isDataSync: true
});
jasmine_util_1.registerTestEnv({
    name: 'webgl2',
    backendName: 'webgl',
    flags: {
        'WEBGL_VERSION': 2,
        'WEBGL_CPU_FORWARD': false,
        'WEBGL_SIZE_UPLOAD_UNIFORM': 0
    },
    isDataSync: true
});
//# sourceMappingURL=backend_webgl_test_registry.js.map