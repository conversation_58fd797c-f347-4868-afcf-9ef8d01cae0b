{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      console.log(`[EchoCameraWeb] 🎨 STARTING BLUR: region (${Math.round(x)}, ${Math.round(y)}) ${Math.round(width)}x${Math.round(height)}`);\n\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      console.log(`[EchoCameraWeb] 📐 CLAMPED REGION: (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.error(`[EchoCameraWeb] ❌ INVALID BLUR REGION: width=${clampedWidth}, height=${clampedHeight}`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n      console.log(`[EchoCameraWeb] 🎨 Applying strong blur to face region...`);\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur passes...`);\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n      console.log(`[EchoCameraWeb] ✅ BLUR COMPLETE: Applied to (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 824,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 848,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1013,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 990,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1040,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1032,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1031,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 846,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1636, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4, "console"], [303, 13, 335, 11], [303, 14, 335, 12, "log"], [303, 17, 335, 15], [303, 18, 335, 16], [303, 63, 335, 61, "Math"], [303, 67, 335, 65], [303, 68, 335, 66, "round"], [303, 73, 335, 71], [303, 74, 335, 72, "x"], [303, 75, 335, 73], [303, 76, 335, 74], [303, 81, 335, 79, "Math"], [303, 85, 335, 83], [303, 86, 335, 84, "round"], [303, 91, 335, 89], [303, 92, 335, 90, "y"], [303, 93, 335, 91], [303, 94, 335, 92], [303, 99, 335, 97, "Math"], [303, 103, 335, 101], [303, 104, 335, 102, "round"], [303, 109, 335, 107], [303, 110, 335, 108, "width"], [303, 115, 335, 113], [303, 116, 335, 114], [303, 120, 335, 118, "Math"], [303, 124, 335, 122], [303, 125, 335, 123, "round"], [303, 130, 335, 128], [303, 131, 335, 129, "height"], [303, 137, 335, 135], [303, 138, 335, 136], [303, 140, 335, 138], [303, 141, 335, 139], [305, 6, 337, 4], [306, 6, 338, 4], [306, 12, 338, 10, "canvasWidth"], [306, 23, 338, 21], [306, 26, 338, 24, "ctx"], [306, 29, 338, 27], [306, 30, 338, 28, "canvas"], [306, 36, 338, 34], [306, 37, 338, 35, "width"], [306, 42, 338, 40], [307, 6, 339, 4], [307, 12, 339, 10, "canvasHeight"], [307, 24, 339, 22], [307, 27, 339, 25, "ctx"], [307, 30, 339, 28], [307, 31, 339, 29, "canvas"], [307, 37, 339, 35], [307, 38, 339, 36, "height"], [307, 44, 339, 42], [308, 6, 341, 4], [308, 12, 341, 10, "clampedX"], [308, 20, 341, 18], [308, 23, 341, 21, "Math"], [308, 27, 341, 25], [308, 28, 341, 26, "max"], [308, 31, 341, 29], [308, 32, 341, 30], [308, 33, 341, 31], [308, 35, 341, 33, "Math"], [308, 39, 341, 37], [308, 40, 341, 38, "min"], [308, 43, 341, 41], [308, 44, 341, 42, "Math"], [308, 48, 341, 46], [308, 49, 341, 47, "floor"], [308, 54, 341, 52], [308, 55, 341, 53, "x"], [308, 56, 341, 54], [308, 57, 341, 55], [308, 59, 341, 57, "canvasWidth"], [308, 70, 341, 68], [308, 73, 341, 71], [308, 74, 341, 72], [308, 75, 341, 73], [308, 76, 341, 74], [309, 6, 342, 4], [309, 12, 342, 10, "clampedY"], [309, 20, 342, 18], [309, 23, 342, 21, "Math"], [309, 27, 342, 25], [309, 28, 342, 26, "max"], [309, 31, 342, 29], [309, 32, 342, 30], [309, 33, 342, 31], [309, 35, 342, 33, "Math"], [309, 39, 342, 37], [309, 40, 342, 38, "min"], [309, 43, 342, 41], [309, 44, 342, 42, "Math"], [309, 48, 342, 46], [309, 49, 342, 47, "floor"], [309, 54, 342, 52], [309, 55, 342, 53, "y"], [309, 56, 342, 54], [309, 57, 342, 55], [309, 59, 342, 57, "canvasHeight"], [309, 71, 342, 69], [309, 74, 342, 72], [309, 75, 342, 73], [309, 76, 342, 74], [309, 77, 342, 75], [310, 6, 343, 4], [310, 12, 343, 10, "<PERSON><PERSON><PERSON><PERSON>"], [310, 24, 343, 22], [310, 27, 343, 25, "Math"], [310, 31, 343, 29], [310, 32, 343, 30, "min"], [310, 35, 343, 33], [310, 36, 343, 34, "Math"], [310, 40, 343, 38], [310, 41, 343, 39, "floor"], [310, 46, 343, 44], [310, 47, 343, 45, "width"], [310, 52, 343, 50], [310, 53, 343, 51], [310, 55, 343, 53, "canvasWidth"], [310, 66, 343, 64], [310, 69, 343, 67, "clampedX"], [310, 77, 343, 75], [310, 78, 343, 76], [311, 6, 344, 4], [311, 12, 344, 10, "clampedHeight"], [311, 25, 344, 23], [311, 28, 344, 26, "Math"], [311, 32, 344, 30], [311, 33, 344, 31, "min"], [311, 36, 344, 34], [311, 37, 344, 35, "Math"], [311, 41, 344, 39], [311, 42, 344, 40, "floor"], [311, 47, 344, 45], [311, 48, 344, 46, "height"], [311, 54, 344, 52], [311, 55, 344, 53], [311, 57, 344, 55, "canvasHeight"], [311, 69, 344, 67], [311, 72, 344, 70, "clampedY"], [311, 80, 344, 78], [311, 81, 344, 79], [312, 6, 346, 4, "console"], [312, 13, 346, 11], [312, 14, 346, 12, "log"], [312, 17, 346, 15], [312, 18, 346, 16], [312, 57, 346, 55, "clampedX"], [312, 65, 346, 63], [312, 70, 346, 68, "clampedY"], [312, 78, 346, 76], [312, 83, 346, 81, "<PERSON><PERSON><PERSON><PERSON>"], [312, 95, 346, 93], [312, 99, 346, 97, "clampedHeight"], [312, 112, 346, 110], [312, 114, 346, 112], [312, 115, 346, 113], [313, 6, 348, 4], [313, 10, 348, 8, "<PERSON><PERSON><PERSON><PERSON>"], [313, 22, 348, 20], [313, 26, 348, 24], [313, 27, 348, 25], [313, 31, 348, 29, "clampedHeight"], [313, 44, 348, 42], [313, 48, 348, 46], [313, 49, 348, 47], [313, 51, 348, 49], [314, 8, 349, 6, "console"], [314, 15, 349, 13], [314, 16, 349, 14, "error"], [314, 21, 349, 19], [314, 22, 349, 20], [314, 70, 349, 68, "<PERSON><PERSON><PERSON><PERSON>"], [314, 82, 349, 80], [314, 94, 349, 92, "clampedHeight"], [314, 107, 349, 105], [314, 109, 349, 107], [314, 110, 349, 108], [315, 8, 350, 6], [316, 6, 351, 4], [318, 6, 353, 4], [319, 6, 354, 4], [319, 12, 354, 10, "imageData"], [319, 21, 354, 19], [319, 24, 354, 22, "ctx"], [319, 27, 354, 25], [319, 28, 354, 26, "getImageData"], [319, 40, 354, 38], [319, 41, 354, 39, "clampedX"], [319, 49, 354, 47], [319, 51, 354, 49, "clampedY"], [319, 59, 354, 57], [319, 61, 354, 59, "<PERSON><PERSON><PERSON><PERSON>"], [319, 73, 354, 71], [319, 75, 354, 73, "clampedHeight"], [319, 88, 354, 86], [319, 89, 354, 87], [320, 6, 355, 4], [320, 12, 355, 10, "data"], [320, 16, 355, 14], [320, 19, 355, 17, "imageData"], [320, 28, 355, 26], [320, 29, 355, 27, "data"], [320, 33, 355, 31], [321, 6, 357, 4, "console"], [321, 13, 357, 11], [321, 14, 357, 12, "log"], [321, 17, 357, 15], [321, 18, 357, 16], [321, 77, 357, 75], [321, 78, 357, 76], [323, 6, 359, 4], [324, 6, 360, 4], [324, 12, 360, 10, "pixelSize"], [324, 21, 360, 19], [324, 24, 360, 22, "Math"], [324, 28, 360, 26], [324, 29, 360, 27, "max"], [324, 32, 360, 30], [324, 33, 360, 31], [324, 35, 360, 33], [324, 37, 360, 35, "Math"], [324, 41, 360, 39], [324, 42, 360, 40, "min"], [324, 45, 360, 43], [324, 46, 360, 44, "<PERSON><PERSON><PERSON><PERSON>"], [324, 58, 360, 56], [324, 60, 360, 58, "clampedHeight"], [324, 73, 360, 71], [324, 74, 360, 72], [324, 77, 360, 75], [324, 78, 360, 76], [324, 79, 360, 77], [325, 6, 361, 4, "console"], [325, 13, 361, 11], [325, 14, 361, 12, "log"], [325, 17, 361, 15], [325, 18, 361, 16], [325, 77, 361, 75, "pixelSize"], [325, 86, 361, 84], [325, 90, 361, 88], [325, 91, 361, 89], [326, 6, 363, 4], [326, 11, 363, 9], [326, 15, 363, 13, "py"], [326, 17, 363, 15], [326, 20, 363, 18], [326, 21, 363, 19], [326, 23, 363, 21, "py"], [326, 25, 363, 23], [326, 28, 363, 26, "clampedHeight"], [326, 41, 363, 39], [326, 43, 363, 41, "py"], [326, 45, 363, 43], [326, 49, 363, 47, "pixelSize"], [326, 58, 363, 56], [326, 60, 363, 58], [327, 8, 364, 6], [327, 13, 364, 11], [327, 17, 364, 15, "px"], [327, 19, 364, 17], [327, 22, 364, 20], [327, 23, 364, 21], [327, 25, 364, 23, "px"], [327, 27, 364, 25], [327, 30, 364, 28, "<PERSON><PERSON><PERSON><PERSON>"], [327, 42, 364, 40], [327, 44, 364, 42, "px"], [327, 46, 364, 44], [327, 50, 364, 48, "pixelSize"], [327, 59, 364, 57], [327, 61, 364, 59], [328, 10, 365, 8], [329, 10, 366, 8], [329, 14, 366, 12, "r"], [329, 15, 366, 13], [329, 18, 366, 16], [329, 19, 366, 17], [330, 12, 366, 19, "g"], [330, 13, 366, 20], [330, 16, 366, 23], [330, 17, 366, 24], [331, 12, 366, 26, "b"], [331, 13, 366, 27], [331, 16, 366, 30], [331, 17, 366, 31], [332, 12, 366, 33, "count"], [332, 17, 366, 38], [332, 20, 366, 41], [332, 21, 366, 42], [333, 10, 368, 8], [333, 15, 368, 13], [333, 19, 368, 17, "dy"], [333, 21, 368, 19], [333, 24, 368, 22], [333, 25, 368, 23], [333, 27, 368, 25, "dy"], [333, 29, 368, 27], [333, 32, 368, 30, "pixelSize"], [333, 41, 368, 39], [333, 45, 368, 43, "py"], [333, 47, 368, 45], [333, 50, 368, 48, "dy"], [333, 52, 368, 50], [333, 55, 368, 53, "clampedHeight"], [333, 68, 368, 66], [333, 70, 368, 68, "dy"], [333, 72, 368, 70], [333, 74, 368, 72], [333, 76, 368, 74], [334, 12, 369, 10], [334, 17, 369, 15], [334, 21, 369, 19, "dx"], [334, 23, 369, 21], [334, 26, 369, 24], [334, 27, 369, 25], [334, 29, 369, 27, "dx"], [334, 31, 369, 29], [334, 34, 369, 32, "pixelSize"], [334, 43, 369, 41], [334, 47, 369, 45, "px"], [334, 49, 369, 47], [334, 52, 369, 50, "dx"], [334, 54, 369, 52], [334, 57, 369, 55, "<PERSON><PERSON><PERSON><PERSON>"], [334, 69, 369, 67], [334, 71, 369, 69, "dx"], [334, 73, 369, 71], [334, 75, 369, 73], [334, 77, 369, 75], [335, 14, 370, 12], [335, 20, 370, 18, "index"], [335, 25, 370, 23], [335, 28, 370, 26], [335, 29, 370, 27], [335, 30, 370, 28, "py"], [335, 32, 370, 30], [335, 35, 370, 33, "dy"], [335, 37, 370, 35], [335, 41, 370, 39, "<PERSON><PERSON><PERSON><PERSON>"], [335, 53, 370, 51], [335, 57, 370, 55, "px"], [335, 59, 370, 57], [335, 62, 370, 60, "dx"], [335, 64, 370, 62], [335, 65, 370, 63], [335, 69, 370, 67], [335, 70, 370, 68], [336, 14, 371, 12, "r"], [336, 15, 371, 13], [336, 19, 371, 17, "data"], [336, 23, 371, 21], [336, 24, 371, 22, "index"], [336, 29, 371, 27], [336, 30, 371, 28], [337, 14, 372, 12, "g"], [337, 15, 372, 13], [337, 19, 372, 17, "data"], [337, 23, 372, 21], [337, 24, 372, 22, "index"], [337, 29, 372, 27], [337, 32, 372, 30], [337, 33, 372, 31], [337, 34, 372, 32], [338, 14, 373, 12, "b"], [338, 15, 373, 13], [338, 19, 373, 17, "data"], [338, 23, 373, 21], [338, 24, 373, 22, "index"], [338, 29, 373, 27], [338, 32, 373, 30], [338, 33, 373, 31], [338, 34, 373, 32], [339, 14, 374, 12, "count"], [339, 19, 374, 17], [339, 21, 374, 19], [340, 12, 375, 10], [341, 10, 376, 8], [342, 10, 378, 8], [342, 14, 378, 12, "count"], [342, 19, 378, 17], [342, 22, 378, 20], [342, 23, 378, 21], [342, 25, 378, 23], [343, 12, 379, 10, "r"], [343, 13, 379, 11], [343, 16, 379, 14, "Math"], [343, 20, 379, 18], [343, 21, 379, 19, "floor"], [343, 26, 379, 24], [343, 27, 379, 25, "r"], [343, 28, 379, 26], [343, 31, 379, 29, "count"], [343, 36, 379, 34], [343, 37, 379, 35], [344, 12, 380, 10, "g"], [344, 13, 380, 11], [344, 16, 380, 14, "Math"], [344, 20, 380, 18], [344, 21, 380, 19, "floor"], [344, 26, 380, 24], [344, 27, 380, 25, "g"], [344, 28, 380, 26], [344, 31, 380, 29, "count"], [344, 36, 380, 34], [344, 37, 380, 35], [345, 12, 381, 10, "b"], [345, 13, 381, 11], [345, 16, 381, 14, "Math"], [345, 20, 381, 18], [345, 21, 381, 19, "floor"], [345, 26, 381, 24], [345, 27, 381, 25, "b"], [345, 28, 381, 26], [345, 31, 381, 29, "count"], [345, 36, 381, 34], [345, 37, 381, 35], [347, 12, 383, 10], [348, 12, 384, 10], [348, 17, 384, 15], [348, 21, 384, 19, "dy"], [348, 23, 384, 21], [348, 26, 384, 24], [348, 27, 384, 25], [348, 29, 384, 27, "dy"], [348, 31, 384, 29], [348, 34, 384, 32, "pixelSize"], [348, 43, 384, 41], [348, 47, 384, 45, "py"], [348, 49, 384, 47], [348, 52, 384, 50, "dy"], [348, 54, 384, 52], [348, 57, 384, 55, "clampedHeight"], [348, 70, 384, 68], [348, 72, 384, 70, "dy"], [348, 74, 384, 72], [348, 76, 384, 74], [348, 78, 384, 76], [349, 14, 385, 12], [349, 19, 385, 17], [349, 23, 385, 21, "dx"], [349, 25, 385, 23], [349, 28, 385, 26], [349, 29, 385, 27], [349, 31, 385, 29, "dx"], [349, 33, 385, 31], [349, 36, 385, 34, "pixelSize"], [349, 45, 385, 43], [349, 49, 385, 47, "px"], [349, 51, 385, 49], [349, 54, 385, 52, "dx"], [349, 56, 385, 54], [349, 59, 385, 57, "<PERSON><PERSON><PERSON><PERSON>"], [349, 71, 385, 69], [349, 73, 385, 71, "dx"], [349, 75, 385, 73], [349, 77, 385, 75], [349, 79, 385, 77], [350, 16, 386, 14], [350, 22, 386, 20, "index"], [350, 27, 386, 25], [350, 30, 386, 28], [350, 31, 386, 29], [350, 32, 386, 30, "py"], [350, 34, 386, 32], [350, 37, 386, 35, "dy"], [350, 39, 386, 37], [350, 43, 386, 41, "<PERSON><PERSON><PERSON><PERSON>"], [350, 55, 386, 53], [350, 59, 386, 57, "px"], [350, 61, 386, 59], [350, 64, 386, 62, "dx"], [350, 66, 386, 64], [350, 67, 386, 65], [350, 71, 386, 69], [350, 72, 386, 70], [351, 16, 387, 14, "data"], [351, 20, 387, 18], [351, 21, 387, 19, "index"], [351, 26, 387, 24], [351, 27, 387, 25], [351, 30, 387, 28, "r"], [351, 31, 387, 29], [352, 16, 388, 14, "data"], [352, 20, 388, 18], [352, 21, 388, 19, "index"], [352, 26, 388, 24], [352, 29, 388, 27], [352, 30, 388, 28], [352, 31, 388, 29], [352, 34, 388, 32, "g"], [352, 35, 388, 33], [353, 16, 389, 14, "data"], [353, 20, 389, 18], [353, 21, 389, 19, "index"], [353, 26, 389, 24], [353, 29, 389, 27], [353, 30, 389, 28], [353, 31, 389, 29], [353, 34, 389, 32, "b"], [353, 35, 389, 33], [354, 16, 390, 14], [355, 14, 391, 12], [356, 12, 392, 10], [357, 10, 393, 8], [358, 8, 394, 6], [359, 6, 395, 4], [361, 6, 397, 4], [362, 6, 398, 4, "console"], [362, 13, 398, 11], [362, 14, 398, 12, "log"], [362, 17, 398, 15], [362, 18, 398, 16], [362, 74, 398, 72], [362, 75, 398, 73], [363, 6, 399, 4], [363, 11, 399, 9], [363, 15, 399, 13, "i"], [363, 16, 399, 14], [363, 19, 399, 17], [363, 20, 399, 18], [363, 22, 399, 20, "i"], [363, 23, 399, 21], [363, 26, 399, 24], [363, 27, 399, 25], [363, 29, 399, 27, "i"], [363, 30, 399, 28], [363, 32, 399, 30], [363, 34, 399, 32], [364, 8, 400, 6, "applySimpleBlur"], [364, 23, 400, 21], [364, 24, 400, 22, "data"], [364, 28, 400, 26], [364, 30, 400, 28, "<PERSON><PERSON><PERSON><PERSON>"], [364, 42, 400, 40], [364, 44, 400, 42, "clampedHeight"], [364, 57, 400, 55], [364, 58, 400, 56], [365, 6, 401, 4], [367, 6, 403, 4], [368, 6, 404, 4, "ctx"], [368, 9, 404, 7], [368, 10, 404, 8, "putImageData"], [368, 22, 404, 20], [368, 23, 404, 21, "imageData"], [368, 32, 404, 30], [368, 34, 404, 32, "clampedX"], [368, 42, 404, 40], [368, 44, 404, 42, "clampedY"], [368, 52, 404, 50], [368, 53, 404, 51], [369, 6, 405, 4, "console"], [369, 13, 405, 11], [369, 14, 405, 12, "log"], [369, 17, 405, 15], [369, 18, 405, 16], [369, 66, 405, 64, "clampedX"], [369, 74, 405, 72], [369, 79, 405, 77, "clampedY"], [369, 87, 405, 85], [369, 92, 405, 90, "<PERSON><PERSON><PERSON><PERSON>"], [369, 104, 405, 102], [369, 108, 405, 106, "clampedHeight"], [369, 121, 405, 119], [369, 123, 405, 121], [369, 124, 405, 122], [370, 4, 406, 2], [370, 5, 406, 3], [371, 4, 408, 2], [371, 10, 408, 8, "applySimpleBlur"], [371, 25, 408, 23], [371, 28, 408, 26, "applySimpleBlur"], [371, 29, 408, 27, "data"], [371, 33, 408, 50], [371, 35, 408, 52, "width"], [371, 40, 408, 65], [371, 42, 408, 67, "height"], [371, 48, 408, 81], [371, 53, 408, 86], [372, 6, 409, 4], [372, 12, 409, 10, "original"], [372, 20, 409, 18], [372, 23, 409, 21], [372, 27, 409, 25, "Uint8ClampedArray"], [372, 44, 409, 42], [372, 45, 409, 43, "data"], [372, 49, 409, 47], [372, 50, 409, 48], [373, 6, 411, 4], [373, 11, 411, 9], [373, 15, 411, 13, "y"], [373, 16, 411, 14], [373, 19, 411, 17], [373, 20, 411, 18], [373, 22, 411, 20, "y"], [373, 23, 411, 21], [373, 26, 411, 24, "height"], [373, 32, 411, 30], [373, 35, 411, 33], [373, 36, 411, 34], [373, 38, 411, 36, "y"], [373, 39, 411, 37], [373, 41, 411, 39], [373, 43, 411, 41], [374, 8, 412, 6], [374, 13, 412, 11], [374, 17, 412, 15, "x"], [374, 18, 412, 16], [374, 21, 412, 19], [374, 22, 412, 20], [374, 24, 412, 22, "x"], [374, 25, 412, 23], [374, 28, 412, 26, "width"], [374, 33, 412, 31], [374, 36, 412, 34], [374, 37, 412, 35], [374, 39, 412, 37, "x"], [374, 40, 412, 38], [374, 42, 412, 40], [374, 44, 412, 42], [375, 10, 413, 8], [375, 16, 413, 14, "index"], [375, 21, 413, 19], [375, 24, 413, 22], [375, 25, 413, 23, "y"], [375, 26, 413, 24], [375, 29, 413, 27, "width"], [375, 34, 413, 32], [375, 37, 413, 35, "x"], [375, 38, 413, 36], [375, 42, 413, 40], [375, 43, 413, 41], [377, 10, 415, 8], [378, 10, 416, 8], [378, 14, 416, 12, "r"], [378, 15, 416, 13], [378, 18, 416, 16], [378, 19, 416, 17], [379, 12, 416, 19, "g"], [379, 13, 416, 20], [379, 16, 416, 23], [379, 17, 416, 24], [380, 12, 416, 26, "b"], [380, 13, 416, 27], [380, 16, 416, 30], [380, 17, 416, 31], [381, 10, 417, 8], [381, 15, 417, 13], [381, 19, 417, 17, "dy"], [381, 21, 417, 19], [381, 24, 417, 22], [381, 25, 417, 23], [381, 26, 417, 24], [381, 28, 417, 26, "dy"], [381, 30, 417, 28], [381, 34, 417, 32], [381, 35, 417, 33], [381, 37, 417, 35, "dy"], [381, 39, 417, 37], [381, 41, 417, 39], [381, 43, 417, 41], [382, 12, 418, 10], [382, 17, 418, 15], [382, 21, 418, 19, "dx"], [382, 23, 418, 21], [382, 26, 418, 24], [382, 27, 418, 25], [382, 28, 418, 26], [382, 30, 418, 28, "dx"], [382, 32, 418, 30], [382, 36, 418, 34], [382, 37, 418, 35], [382, 39, 418, 37, "dx"], [382, 41, 418, 39], [382, 43, 418, 41], [382, 45, 418, 43], [383, 14, 419, 12], [383, 20, 419, 18, "neighborIndex"], [383, 33, 419, 31], [383, 36, 419, 34], [383, 37, 419, 35], [383, 38, 419, 36, "y"], [383, 39, 419, 37], [383, 42, 419, 40, "dy"], [383, 44, 419, 42], [383, 48, 419, 46, "width"], [383, 53, 419, 51], [383, 57, 419, 55, "x"], [383, 58, 419, 56], [383, 61, 419, 59, "dx"], [383, 63, 419, 61], [383, 64, 419, 62], [383, 68, 419, 66], [383, 69, 419, 67], [384, 14, 420, 12, "r"], [384, 15, 420, 13], [384, 19, 420, 17, "original"], [384, 27, 420, 25], [384, 28, 420, 26, "neighborIndex"], [384, 41, 420, 39], [384, 42, 420, 40], [385, 14, 421, 12, "g"], [385, 15, 421, 13], [385, 19, 421, 17, "original"], [385, 27, 421, 25], [385, 28, 421, 26, "neighborIndex"], [385, 41, 421, 39], [385, 44, 421, 42], [385, 45, 421, 43], [385, 46, 421, 44], [386, 14, 422, 12, "b"], [386, 15, 422, 13], [386, 19, 422, 17, "original"], [386, 27, 422, 25], [386, 28, 422, 26, "neighborIndex"], [386, 41, 422, 39], [386, 44, 422, 42], [386, 45, 422, 43], [386, 46, 422, 44], [387, 12, 423, 10], [388, 10, 424, 8], [389, 10, 426, 8, "data"], [389, 14, 426, 12], [389, 15, 426, 13, "index"], [389, 20, 426, 18], [389, 21, 426, 19], [389, 24, 426, 22, "r"], [389, 25, 426, 23], [389, 28, 426, 26], [389, 29, 426, 27], [390, 10, 427, 8, "data"], [390, 14, 427, 12], [390, 15, 427, 13, "index"], [390, 20, 427, 18], [390, 23, 427, 21], [390, 24, 427, 22], [390, 25, 427, 23], [390, 28, 427, 26, "g"], [390, 29, 427, 27], [390, 32, 427, 30], [390, 33, 427, 31], [391, 10, 428, 8, "data"], [391, 14, 428, 12], [391, 15, 428, 13, "index"], [391, 20, 428, 18], [391, 23, 428, 21], [391, 24, 428, 22], [391, 25, 428, 23], [391, 28, 428, 26, "b"], [391, 29, 428, 27], [391, 32, 428, 30], [391, 33, 428, 31], [392, 8, 429, 6], [393, 6, 430, 4], [394, 4, 431, 2], [394, 5, 431, 3], [395, 4, 433, 2], [395, 10, 433, 8, "applyFallbackFaceBlur"], [395, 31, 433, 29], [395, 34, 433, 32, "applyFallbackFaceBlur"], [395, 35, 433, 33, "ctx"], [395, 38, 433, 62], [395, 40, 433, 64, "imgWidth"], [395, 48, 433, 80], [395, 50, 433, 82, "imgHeight"], [395, 59, 433, 99], [395, 64, 433, 104], [396, 6, 434, 4, "console"], [396, 13, 434, 11], [396, 14, 434, 12, "log"], [396, 17, 434, 15], [396, 18, 434, 16], [396, 90, 434, 88], [396, 91, 434, 89], [398, 6, 436, 4], [399, 6, 437, 4], [399, 12, 437, 10, "areas"], [399, 17, 437, 15], [399, 20, 437, 18], [400, 6, 438, 6], [401, 6, 439, 6], [402, 8, 439, 8, "x"], [402, 9, 439, 9], [402, 11, 439, 11, "imgWidth"], [402, 19, 439, 19], [402, 22, 439, 22], [402, 26, 439, 26], [403, 8, 439, 28, "y"], [403, 9, 439, 29], [403, 11, 439, 31, "imgHeight"], [403, 20, 439, 40], [403, 23, 439, 43], [403, 27, 439, 47], [404, 8, 439, 49, "w"], [404, 9, 439, 50], [404, 11, 439, 52, "imgWidth"], [404, 19, 439, 60], [404, 22, 439, 63], [404, 25, 439, 66], [405, 8, 439, 68, "h"], [405, 9, 439, 69], [405, 11, 439, 71, "imgHeight"], [405, 20, 439, 80], [405, 23, 439, 83], [406, 6, 439, 87], [406, 7, 439, 88], [407, 6, 440, 6], [408, 6, 441, 6], [409, 8, 441, 8, "x"], [409, 9, 441, 9], [409, 11, 441, 11, "imgWidth"], [409, 19, 441, 19], [409, 22, 441, 22], [409, 25, 441, 25], [410, 8, 441, 27, "y"], [410, 9, 441, 28], [410, 11, 441, 30, "imgHeight"], [410, 20, 441, 39], [410, 23, 441, 42], [410, 26, 441, 45], [411, 8, 441, 47, "w"], [411, 9, 441, 48], [411, 11, 441, 50, "imgWidth"], [411, 19, 441, 58], [411, 22, 441, 61], [411, 26, 441, 65], [412, 8, 441, 67, "h"], [412, 9, 441, 68], [412, 11, 441, 70, "imgHeight"], [412, 20, 441, 79], [412, 23, 441, 82], [413, 6, 441, 86], [413, 7, 441, 87], [414, 6, 442, 6], [415, 6, 443, 6], [416, 8, 443, 8, "x"], [416, 9, 443, 9], [416, 11, 443, 11, "imgWidth"], [416, 19, 443, 19], [416, 22, 443, 22], [416, 26, 443, 26], [417, 8, 443, 28, "y"], [417, 9, 443, 29], [417, 11, 443, 31, "imgHeight"], [417, 20, 443, 40], [417, 23, 443, 43], [417, 26, 443, 46], [418, 8, 443, 48, "w"], [418, 9, 443, 49], [418, 11, 443, 51, "imgWidth"], [418, 19, 443, 59], [418, 22, 443, 62], [418, 26, 443, 66], [419, 8, 443, 68, "h"], [419, 9, 443, 69], [419, 11, 443, 71, "imgHeight"], [419, 20, 443, 80], [419, 23, 443, 83], [420, 6, 443, 87], [420, 7, 443, 88], [420, 8, 444, 5], [421, 6, 446, 4, "areas"], [421, 11, 446, 9], [421, 12, 446, 10, "for<PERSON>ach"], [421, 19, 446, 17], [421, 20, 446, 18], [421, 21, 446, 19, "area"], [421, 25, 446, 23], [421, 27, 446, 25, "index"], [421, 32, 446, 30], [421, 37, 446, 35], [422, 8, 447, 6, "console"], [422, 15, 447, 13], [422, 16, 447, 14, "log"], [422, 19, 447, 17], [422, 20, 447, 18], [422, 65, 447, 63, "index"], [422, 70, 447, 68], [422, 73, 447, 71], [422, 74, 447, 72], [422, 77, 447, 75], [422, 79, 447, 77, "area"], [422, 83, 447, 81], [422, 84, 447, 82], [423, 8, 448, 6, "applyStrongBlur"], [423, 23, 448, 21], [423, 24, 448, 22, "ctx"], [423, 27, 448, 25], [423, 29, 448, 27, "area"], [423, 33, 448, 31], [423, 34, 448, 32, "x"], [423, 35, 448, 33], [423, 37, 448, 35, "area"], [423, 41, 448, 39], [423, 42, 448, 40, "y"], [423, 43, 448, 41], [423, 45, 448, 43, "area"], [423, 49, 448, 47], [423, 50, 448, 48, "w"], [423, 51, 448, 49], [423, 53, 448, 51, "area"], [423, 57, 448, 55], [423, 58, 448, 56, "h"], [423, 59, 448, 57], [423, 60, 448, 58], [424, 6, 449, 4], [424, 7, 449, 5], [424, 8, 449, 6], [425, 4, 450, 2], [425, 5, 450, 3], [427, 4, 452, 2], [428, 4, 453, 2], [428, 10, 453, 8, "capturePhoto"], [428, 22, 453, 20], [428, 25, 453, 23], [428, 29, 453, 23, "useCallback"], [428, 47, 453, 34], [428, 49, 453, 35], [428, 61, 453, 47], [429, 6, 454, 4], [430, 6, 455, 4], [430, 12, 455, 10, "isDev"], [430, 17, 455, 15], [430, 20, 455, 18, "process"], [430, 27, 455, 25], [430, 28, 455, 26, "env"], [430, 31, 455, 29], [430, 32, 455, 30, "NODE_ENV"], [430, 40, 455, 38], [430, 45, 455, 43], [430, 58, 455, 56], [430, 62, 455, 60, "__DEV__"], [430, 69, 455, 67], [431, 6, 457, 4], [431, 10, 457, 8], [431, 11, 457, 9, "cameraRef"], [431, 20, 457, 18], [431, 21, 457, 19, "current"], [431, 28, 457, 26], [431, 32, 457, 30], [431, 33, 457, 31, "isDev"], [431, 38, 457, 36], [431, 40, 457, 38], [432, 8, 458, 6, "<PERSON><PERSON>"], [432, 22, 458, 11], [432, 23, 458, 12, "alert"], [432, 28, 458, 17], [432, 29, 458, 18], [432, 36, 458, 25], [432, 38, 458, 27], [432, 56, 458, 45], [432, 57, 458, 46], [433, 8, 459, 6], [434, 6, 460, 4], [435, 6, 461, 4], [435, 10, 461, 8], [436, 8, 462, 6, "setProcessingState"], [436, 26, 462, 24], [436, 27, 462, 25], [436, 38, 462, 36], [436, 39, 462, 37], [437, 8, 463, 6, "setProcessingProgress"], [437, 29, 463, 27], [437, 30, 463, 28], [437, 32, 463, 30], [437, 33, 463, 31], [438, 8, 464, 6], [439, 8, 465, 6], [440, 8, 466, 6], [441, 8, 467, 6], [441, 14, 467, 12], [441, 18, 467, 16, "Promise"], [441, 25, 467, 23], [441, 26, 467, 24, "resolve"], [441, 33, 467, 31], [441, 37, 467, 35, "setTimeout"], [441, 47, 467, 45], [441, 48, 467, 46, "resolve"], [441, 55, 467, 53], [441, 57, 467, 55], [441, 59, 467, 57], [441, 60, 467, 58], [441, 61, 467, 59], [442, 8, 468, 6], [443, 8, 469, 6], [443, 12, 469, 10, "photo"], [443, 17, 469, 15], [444, 8, 471, 6], [444, 12, 471, 10], [445, 10, 472, 8, "photo"], [445, 15, 472, 13], [445, 18, 472, 16], [445, 24, 472, 22, "cameraRef"], [445, 33, 472, 31], [445, 34, 472, 32, "current"], [445, 41, 472, 39], [445, 42, 472, 40, "takePictureAsync"], [445, 58, 472, 56], [445, 59, 472, 57], [446, 12, 473, 10, "quality"], [446, 19, 473, 17], [446, 21, 473, 19], [446, 24, 473, 22], [447, 12, 474, 10, "base64"], [447, 18, 474, 16], [447, 20, 474, 18], [447, 25, 474, 23], [448, 12, 475, 10, "skipProcessing"], [448, 26, 475, 24], [448, 28, 475, 26], [448, 32, 475, 30], [448, 33, 475, 32], [449, 10, 476, 8], [449, 11, 476, 9], [449, 12, 476, 10], [450, 8, 477, 6], [450, 9, 477, 7], [450, 10, 477, 8], [450, 17, 477, 15, "cameraError"], [450, 28, 477, 26], [450, 30, 477, 28], [451, 10, 478, 8, "console"], [451, 17, 478, 15], [451, 18, 478, 16, "log"], [451, 21, 478, 19], [451, 22, 478, 20], [451, 82, 478, 80], [451, 84, 478, 82, "cameraError"], [451, 95, 478, 93], [451, 96, 478, 94], [452, 10, 479, 8], [453, 10, 480, 8], [453, 14, 480, 12, "isDev"], [453, 19, 480, 17], [453, 21, 480, 19], [454, 12, 481, 10, "photo"], [454, 17, 481, 15], [454, 20, 481, 18], [455, 14, 482, 12, "uri"], [455, 17, 482, 15], [455, 19, 482, 17], [456, 12, 483, 10], [456, 13, 483, 11], [457, 10, 484, 8], [457, 11, 484, 9], [457, 17, 484, 15], [458, 12, 485, 10], [458, 18, 485, 16, "cameraError"], [458, 29, 485, 27], [459, 10, 486, 8], [460, 8, 487, 6], [461, 8, 488, 6], [461, 12, 488, 10], [461, 13, 488, 11, "photo"], [461, 18, 488, 16], [461, 20, 488, 18], [462, 10, 489, 8], [462, 16, 489, 14], [462, 20, 489, 18, "Error"], [462, 25, 489, 23], [462, 26, 489, 24], [462, 51, 489, 49], [462, 52, 489, 50], [463, 8, 490, 6], [464, 8, 491, 6, "console"], [464, 15, 491, 13], [464, 16, 491, 14, "log"], [464, 19, 491, 17], [464, 20, 491, 18], [464, 56, 491, 54], [464, 58, 491, 56, "photo"], [464, 63, 491, 61], [464, 64, 491, 62, "uri"], [464, 67, 491, 65], [464, 68, 491, 66], [465, 8, 492, 6, "setCapturedPhoto"], [465, 24, 492, 22], [465, 25, 492, 23, "photo"], [465, 30, 492, 28], [465, 31, 492, 29, "uri"], [465, 34, 492, 32], [465, 35, 492, 33], [466, 8, 493, 6, "setProcessingProgress"], [466, 29, 493, 27], [466, 30, 493, 28], [466, 32, 493, 30], [466, 33, 493, 31], [467, 8, 494, 6], [468, 8, 495, 6, "console"], [468, 15, 495, 13], [468, 16, 495, 14, "log"], [468, 19, 495, 17], [468, 20, 495, 18], [468, 73, 495, 71], [468, 74, 495, 72], [469, 8, 496, 6], [469, 14, 496, 12, "processImageWithFaceBlur"], [469, 38, 496, 36], [469, 39, 496, 37, "photo"], [469, 44, 496, 42], [469, 45, 496, 43, "uri"], [469, 48, 496, 46], [469, 49, 496, 47], [470, 8, 497, 6, "console"], [470, 15, 497, 13], [470, 16, 497, 14, "log"], [470, 19, 497, 17], [470, 20, 497, 18], [470, 71, 497, 69], [470, 72, 497, 70], [471, 6, 498, 4], [471, 7, 498, 5], [471, 8, 498, 6], [471, 15, 498, 13, "error"], [471, 20, 498, 18], [471, 22, 498, 20], [472, 8, 499, 6, "console"], [472, 15, 499, 13], [472, 16, 499, 14, "error"], [472, 21, 499, 19], [472, 22, 499, 20], [472, 54, 499, 52], [472, 56, 499, 54, "error"], [472, 61, 499, 59], [472, 62, 499, 60], [473, 8, 500, 6, "setErrorMessage"], [473, 23, 500, 21], [473, 24, 500, 22], [473, 68, 500, 66], [473, 69, 500, 67], [474, 8, 501, 6, "setProcessingState"], [474, 26, 501, 24], [474, 27, 501, 25], [474, 34, 501, 32], [474, 35, 501, 33], [475, 6, 502, 4], [476, 4, 503, 2], [476, 5, 503, 3], [476, 7, 503, 5], [476, 9, 503, 7], [476, 10, 503, 8], [477, 4, 504, 2], [478, 4, 505, 2], [478, 10, 505, 8, "processImageWithFaceBlur"], [478, 34, 505, 32], [478, 37, 505, 35], [478, 43, 505, 42, "photoUri"], [478, 51, 505, 58], [478, 55, 505, 63], [479, 6, 506, 4], [479, 10, 506, 8], [480, 8, 507, 6, "console"], [480, 15, 507, 13], [480, 16, 507, 14, "log"], [480, 19, 507, 17], [480, 20, 507, 18], [480, 84, 507, 82], [480, 85, 507, 83], [481, 8, 508, 6, "setProcessingState"], [481, 26, 508, 24], [481, 27, 508, 25], [481, 39, 508, 37], [481, 40, 508, 38], [482, 8, 509, 6, "setProcessingProgress"], [482, 29, 509, 27], [482, 30, 509, 28], [482, 32, 509, 30], [482, 33, 509, 31], [484, 8, 511, 6], [485, 8, 512, 6], [485, 14, 512, 12, "canvas"], [485, 20, 512, 18], [485, 23, 512, 21, "document"], [485, 31, 512, 29], [485, 32, 512, 30, "createElement"], [485, 45, 512, 43], [485, 46, 512, 44], [485, 54, 512, 52], [485, 55, 512, 53], [486, 8, 513, 6], [486, 14, 513, 12, "ctx"], [486, 17, 513, 15], [486, 20, 513, 18, "canvas"], [486, 26, 513, 24], [486, 27, 513, 25, "getContext"], [486, 37, 513, 35], [486, 38, 513, 36], [486, 42, 513, 40], [486, 43, 513, 41], [487, 8, 514, 6], [487, 12, 514, 10], [487, 13, 514, 11, "ctx"], [487, 16, 514, 14], [487, 18, 514, 16], [487, 24, 514, 22], [487, 28, 514, 26, "Error"], [487, 33, 514, 31], [487, 34, 514, 32], [487, 64, 514, 62], [487, 65, 514, 63], [489, 8, 516, 6], [490, 8, 517, 6], [490, 14, 517, 12, "img"], [490, 17, 517, 15], [490, 20, 517, 18], [490, 24, 517, 22, "Image"], [490, 29, 517, 27], [490, 30, 517, 28], [490, 31, 517, 29], [491, 8, 518, 6], [491, 14, 518, 12], [491, 18, 518, 16, "Promise"], [491, 25, 518, 23], [491, 26, 518, 24], [491, 27, 518, 25, "resolve"], [491, 34, 518, 32], [491, 36, 518, 34, "reject"], [491, 42, 518, 40], [491, 47, 518, 45], [492, 10, 519, 8, "img"], [492, 13, 519, 11], [492, 14, 519, 12, "onload"], [492, 20, 519, 18], [492, 23, 519, 21, "resolve"], [492, 30, 519, 28], [493, 10, 520, 8, "img"], [493, 13, 520, 11], [493, 14, 520, 12, "onerror"], [493, 21, 520, 19], [493, 24, 520, 22, "reject"], [493, 30, 520, 28], [494, 10, 521, 8, "img"], [494, 13, 521, 11], [494, 14, 521, 12, "src"], [494, 17, 521, 15], [494, 20, 521, 18, "photoUri"], [494, 28, 521, 26], [495, 8, 522, 6], [495, 9, 522, 7], [495, 10, 522, 8], [497, 8, 524, 6], [498, 8, 525, 6, "canvas"], [498, 14, 525, 12], [498, 15, 525, 13, "width"], [498, 20, 525, 18], [498, 23, 525, 21, "img"], [498, 26, 525, 24], [498, 27, 525, 25, "width"], [498, 32, 525, 30], [499, 8, 526, 6, "canvas"], [499, 14, 526, 12], [499, 15, 526, 13, "height"], [499, 21, 526, 19], [499, 24, 526, 22, "img"], [499, 27, 526, 25], [499, 28, 526, 26, "height"], [499, 34, 526, 32], [500, 8, 527, 6, "console"], [500, 15, 527, 13], [500, 16, 527, 14, "log"], [500, 19, 527, 17], [500, 20, 527, 18], [500, 54, 527, 52], [500, 56, 527, 54], [501, 10, 527, 56, "width"], [501, 15, 527, 61], [501, 17, 527, 63, "img"], [501, 20, 527, 66], [501, 21, 527, 67, "width"], [501, 26, 527, 72], [502, 10, 527, 74, "height"], [502, 16, 527, 80], [502, 18, 527, 82, "img"], [502, 21, 527, 85], [502, 22, 527, 86, "height"], [503, 8, 527, 93], [503, 9, 527, 94], [503, 10, 527, 95], [505, 8, 529, 6], [506, 8, 530, 6, "ctx"], [506, 11, 530, 9], [506, 12, 530, 10, "drawImage"], [506, 21, 530, 19], [506, 22, 530, 20, "img"], [506, 25, 530, 23], [506, 27, 530, 25], [506, 28, 530, 26], [506, 30, 530, 28], [506, 31, 530, 29], [506, 32, 530, 30], [507, 8, 531, 6, "console"], [507, 15, 531, 13], [507, 16, 531, 14, "log"], [507, 19, 531, 17], [507, 20, 531, 18], [507, 72, 531, 70], [507, 73, 531, 71], [508, 8, 533, 6, "setProcessingProgress"], [508, 29, 533, 27], [508, 30, 533, 28], [508, 32, 533, 30], [508, 33, 533, 31], [510, 8, 535, 6], [511, 8, 536, 6], [511, 12, 536, 10, "detectedFaces"], [511, 25, 536, 23], [511, 28, 536, 26], [511, 30, 536, 28], [512, 8, 538, 6, "console"], [512, 15, 538, 13], [512, 16, 538, 14, "log"], [512, 19, 538, 17], [512, 20, 538, 18], [512, 81, 538, 79], [512, 82, 538, 80], [514, 8, 540, 6], [515, 8, 541, 6], [515, 12, 541, 10], [516, 10, 542, 8], [516, 16, 542, 14, "loadTensorFlowFaceDetection"], [516, 43, 542, 41], [516, 44, 542, 42], [516, 45, 542, 43], [517, 10, 543, 8, "detectedFaces"], [517, 23, 543, 21], [517, 26, 543, 24], [517, 32, 543, 30, "detectFacesWithTensorFlow"], [517, 57, 543, 55], [517, 58, 543, 56, "img"], [517, 61, 543, 59], [517, 62, 543, 60], [518, 10, 544, 8, "console"], [518, 17, 544, 15], [518, 18, 544, 16, "log"], [518, 21, 544, 19], [518, 22, 544, 20], [518, 70, 544, 68, "detectedFaces"], [518, 83, 544, 81], [518, 84, 544, 82, "length"], [518, 90, 544, 88], [518, 98, 544, 96], [518, 99, 544, 97], [519, 8, 545, 6], [519, 9, 545, 7], [519, 10, 545, 8], [519, 17, 545, 15, "tensorFlowError"], [519, 32, 545, 30], [519, 34, 545, 32], [520, 10, 546, 8, "console"], [520, 17, 546, 15], [520, 18, 546, 16, "warn"], [520, 22, 546, 20], [520, 23, 546, 21], [520, 61, 546, 59], [520, 63, 546, 61, "tensorFlowError"], [520, 78, 546, 76], [520, 79, 546, 77], [522, 10, 548, 8], [523, 10, 549, 8, "console"], [523, 17, 549, 15], [523, 18, 549, 16, "log"], [523, 21, 549, 19], [523, 22, 549, 20], [523, 86, 549, 84], [523, 87, 549, 85], [524, 10, 550, 8, "detectedFaces"], [524, 23, 550, 21], [524, 26, 550, 24, "detectFacesHeuristic"], [524, 46, 550, 44], [524, 47, 550, 45, "img"], [524, 50, 550, 48], [524, 52, 550, 50, "ctx"], [524, 55, 550, 53], [524, 56, 550, 54], [525, 10, 551, 8, "console"], [525, 17, 551, 15], [525, 18, 551, 16, "log"], [525, 21, 551, 19], [525, 22, 551, 20], [525, 70, 551, 68, "detectedFaces"], [525, 83, 551, 81], [525, 84, 551, 82, "length"], [525, 90, 551, 88], [525, 98, 551, 96], [525, 99, 551, 97], [526, 8, 552, 6], [527, 8, 554, 6, "console"], [527, 15, 554, 13], [527, 16, 554, 14, "log"], [527, 19, 554, 17], [527, 20, 554, 18], [527, 72, 554, 70, "detectedFaces"], [527, 85, 554, 83], [527, 86, 554, 84, "length"], [527, 92, 554, 90], [527, 100, 554, 98], [527, 101, 554, 99], [528, 8, 555, 6], [528, 12, 555, 10, "detectedFaces"], [528, 25, 555, 23], [528, 26, 555, 24, "length"], [528, 32, 555, 30], [528, 35, 555, 33], [528, 36, 555, 34], [528, 38, 555, 36], [529, 10, 556, 8, "console"], [529, 17, 556, 15], [529, 18, 556, 16, "log"], [529, 21, 556, 19], [529, 22, 556, 20], [529, 66, 556, 64], [529, 68, 556, 66, "detectedFaces"], [529, 81, 556, 79], [529, 82, 556, 80, "map"], [529, 85, 556, 83], [529, 86, 556, 84], [529, 87, 556, 85, "face"], [529, 91, 556, 89], [529, 93, 556, 91, "i"], [529, 94, 556, 92], [529, 100, 556, 98], [530, 12, 557, 10, "faceNumber"], [530, 22, 557, 20], [530, 24, 557, 22, "i"], [530, 25, 557, 23], [530, 28, 557, 26], [530, 29, 557, 27], [531, 12, 558, 10, "centerX"], [531, 19, 558, 17], [531, 21, 558, 19, "face"], [531, 25, 558, 23], [531, 26, 558, 24, "boundingBox"], [531, 37, 558, 35], [531, 38, 558, 36, "xCenter"], [531, 45, 558, 43], [532, 12, 559, 10, "centerY"], [532, 19, 559, 17], [532, 21, 559, 19, "face"], [532, 25, 559, 23], [532, 26, 559, 24, "boundingBox"], [532, 37, 559, 35], [532, 38, 559, 36, "yCenter"], [532, 45, 559, 43], [533, 12, 560, 10, "width"], [533, 17, 560, 15], [533, 19, 560, 17, "face"], [533, 23, 560, 21], [533, 24, 560, 22, "boundingBox"], [533, 35, 560, 33], [533, 36, 560, 34, "width"], [533, 41, 560, 39], [534, 12, 561, 10, "height"], [534, 18, 561, 16], [534, 20, 561, 18, "face"], [534, 24, 561, 22], [534, 25, 561, 23, "boundingBox"], [534, 36, 561, 34], [534, 37, 561, 35, "height"], [535, 10, 562, 8], [535, 11, 562, 9], [535, 12, 562, 10], [535, 13, 562, 11], [535, 14, 562, 12], [536, 8, 563, 6], [536, 9, 563, 7], [536, 15, 563, 13], [537, 10, 564, 8, "console"], [537, 17, 564, 15], [537, 18, 564, 16, "log"], [537, 21, 564, 19], [537, 22, 564, 20], [537, 91, 564, 89], [537, 92, 564, 90], [538, 8, 565, 6], [539, 8, 567, 6, "setProcessingProgress"], [539, 29, 567, 27], [539, 30, 567, 28], [539, 32, 567, 30], [539, 33, 567, 31], [541, 8, 569, 6], [542, 8, 570, 6], [542, 12, 570, 10, "detectedFaces"], [542, 25, 570, 23], [542, 26, 570, 24, "length"], [542, 32, 570, 30], [542, 35, 570, 33], [542, 36, 570, 34], [542, 38, 570, 36], [543, 10, 571, 8, "console"], [543, 17, 571, 15], [543, 18, 571, 16, "log"], [543, 21, 571, 19], [543, 22, 571, 20], [543, 61, 571, 59, "detectedFaces"], [543, 74, 571, 72], [543, 75, 571, 73, "length"], [543, 81, 571, 79], [543, 101, 571, 99], [543, 102, 571, 100], [544, 10, 573, 8, "detectedFaces"], [544, 23, 573, 21], [544, 24, 573, 22, "for<PERSON>ach"], [544, 31, 573, 29], [544, 32, 573, 30], [544, 33, 573, 31, "detection"], [544, 42, 573, 40], [544, 44, 573, 42, "index"], [544, 49, 573, 47], [544, 54, 573, 52], [545, 12, 574, 10], [545, 18, 574, 16, "bbox"], [545, 22, 574, 20], [545, 25, 574, 23, "detection"], [545, 34, 574, 32], [545, 35, 574, 33, "boundingBox"], [545, 46, 574, 44], [547, 12, 576, 10], [548, 12, 577, 10], [548, 18, 577, 16, "faceX"], [548, 23, 577, 21], [548, 26, 577, 24, "bbox"], [548, 30, 577, 28], [548, 31, 577, 29, "xCenter"], [548, 38, 577, 36], [548, 41, 577, 39, "img"], [548, 44, 577, 42], [548, 45, 577, 43, "width"], [548, 50, 577, 48], [548, 53, 577, 52, "bbox"], [548, 57, 577, 56], [548, 58, 577, 57, "width"], [548, 63, 577, 62], [548, 66, 577, 65, "img"], [548, 69, 577, 68], [548, 70, 577, 69, "width"], [548, 75, 577, 74], [548, 78, 577, 78], [548, 79, 577, 79], [549, 12, 578, 10], [549, 18, 578, 16, "faceY"], [549, 23, 578, 21], [549, 26, 578, 24, "bbox"], [549, 30, 578, 28], [549, 31, 578, 29, "yCenter"], [549, 38, 578, 36], [549, 41, 578, 39, "img"], [549, 44, 578, 42], [549, 45, 578, 43, "height"], [549, 51, 578, 49], [549, 54, 578, 53, "bbox"], [549, 58, 578, 57], [549, 59, 578, 58, "height"], [549, 65, 578, 64], [549, 68, 578, 67, "img"], [549, 71, 578, 70], [549, 72, 578, 71, "height"], [549, 78, 578, 77], [549, 81, 578, 81], [549, 82, 578, 82], [550, 12, 579, 10], [550, 18, 579, 16, "faceWidth"], [550, 27, 579, 25], [550, 30, 579, 28, "bbox"], [550, 34, 579, 32], [550, 35, 579, 33, "width"], [550, 40, 579, 38], [550, 43, 579, 41, "img"], [550, 46, 579, 44], [550, 47, 579, 45, "width"], [550, 52, 579, 50], [551, 12, 580, 10], [551, 18, 580, 16, "faceHeight"], [551, 28, 580, 26], [551, 31, 580, 29, "bbox"], [551, 35, 580, 33], [551, 36, 580, 34, "height"], [551, 42, 580, 40], [551, 45, 580, 43, "img"], [551, 48, 580, 46], [551, 49, 580, 47, "height"], [551, 55, 580, 53], [553, 12, 582, 10], [554, 12, 583, 10], [554, 18, 583, 16, "padding"], [554, 25, 583, 23], [554, 28, 583, 26], [554, 31, 583, 29], [555, 12, 584, 10], [555, 18, 584, 16, "paddedX"], [555, 25, 584, 23], [555, 28, 584, 26, "Math"], [555, 32, 584, 30], [555, 33, 584, 31, "max"], [555, 36, 584, 34], [555, 37, 584, 35], [555, 38, 584, 36], [555, 40, 584, 38, "faceX"], [555, 45, 584, 43], [555, 48, 584, 46, "faceWidth"], [555, 57, 584, 55], [555, 60, 584, 58, "padding"], [555, 67, 584, 65], [555, 68, 584, 66], [556, 12, 585, 10], [556, 18, 585, 16, "paddedY"], [556, 25, 585, 23], [556, 28, 585, 26, "Math"], [556, 32, 585, 30], [556, 33, 585, 31, "max"], [556, 36, 585, 34], [556, 37, 585, 35], [556, 38, 585, 36], [556, 40, 585, 38, "faceY"], [556, 45, 585, 43], [556, 48, 585, 46, "faceHeight"], [556, 58, 585, 56], [556, 61, 585, 59, "padding"], [556, 68, 585, 66], [556, 69, 585, 67], [557, 12, 586, 10], [557, 18, 586, 16, "<PERSON><PERSON><PERSON><PERSON>"], [557, 29, 586, 27], [557, 32, 586, 30, "Math"], [557, 36, 586, 34], [557, 37, 586, 35, "min"], [557, 40, 586, 38], [557, 41, 586, 39, "img"], [557, 44, 586, 42], [557, 45, 586, 43, "width"], [557, 50, 586, 48], [557, 53, 586, 51, "paddedX"], [557, 60, 586, 58], [557, 62, 586, 60, "faceWidth"], [557, 71, 586, 69], [557, 75, 586, 73], [557, 76, 586, 74], [557, 79, 586, 77], [557, 80, 586, 78], [557, 83, 586, 81, "padding"], [557, 90, 586, 88], [557, 91, 586, 89], [557, 92, 586, 90], [558, 12, 587, 10], [558, 18, 587, 16, "paddedHeight"], [558, 30, 587, 28], [558, 33, 587, 31, "Math"], [558, 37, 587, 35], [558, 38, 587, 36, "min"], [558, 41, 587, 39], [558, 42, 587, 40, "img"], [558, 45, 587, 43], [558, 46, 587, 44, "height"], [558, 52, 587, 50], [558, 55, 587, 53, "paddedY"], [558, 62, 587, 60], [558, 64, 587, 62, "faceHeight"], [558, 74, 587, 72], [558, 78, 587, 76], [558, 79, 587, 77], [558, 82, 587, 80], [558, 83, 587, 81], [558, 86, 587, 84, "padding"], [558, 93, 587, 91], [558, 94, 587, 92], [558, 95, 587, 93], [559, 12, 589, 10, "console"], [559, 19, 589, 17], [559, 20, 589, 18, "log"], [559, 23, 589, 21], [559, 24, 589, 22], [559, 60, 589, 58, "index"], [559, 65, 589, 63], [559, 68, 589, 66], [559, 69, 589, 67], [559, 72, 589, 70], [559, 74, 589, 72], [560, 14, 590, 12, "original"], [560, 22, 590, 20], [560, 24, 590, 22], [561, 16, 590, 24, "x"], [561, 17, 590, 25], [561, 19, 590, 27, "Math"], [561, 23, 590, 31], [561, 24, 590, 32, "round"], [561, 29, 590, 37], [561, 30, 590, 38, "faceX"], [561, 35, 590, 43], [561, 36, 590, 44], [562, 16, 590, 46, "y"], [562, 17, 590, 47], [562, 19, 590, 49, "Math"], [562, 23, 590, 53], [562, 24, 590, 54, "round"], [562, 29, 590, 59], [562, 30, 590, 60, "faceY"], [562, 35, 590, 65], [562, 36, 590, 66], [563, 16, 590, 68, "w"], [563, 17, 590, 69], [563, 19, 590, 71, "Math"], [563, 23, 590, 75], [563, 24, 590, 76, "round"], [563, 29, 590, 81], [563, 30, 590, 82, "faceWidth"], [563, 39, 590, 91], [563, 40, 590, 92], [564, 16, 590, 94, "h"], [564, 17, 590, 95], [564, 19, 590, 97, "Math"], [564, 23, 590, 101], [564, 24, 590, 102, "round"], [564, 29, 590, 107], [564, 30, 590, 108, "faceHeight"], [564, 40, 590, 118], [565, 14, 590, 120], [565, 15, 590, 121], [566, 14, 591, 12, "padded"], [566, 20, 591, 18], [566, 22, 591, 20], [567, 16, 591, 22, "x"], [567, 17, 591, 23], [567, 19, 591, 25, "Math"], [567, 23, 591, 29], [567, 24, 591, 30, "round"], [567, 29, 591, 35], [567, 30, 591, 36, "paddedX"], [567, 37, 591, 43], [567, 38, 591, 44], [568, 16, 591, 46, "y"], [568, 17, 591, 47], [568, 19, 591, 49, "Math"], [568, 23, 591, 53], [568, 24, 591, 54, "round"], [568, 29, 591, 59], [568, 30, 591, 60, "paddedY"], [568, 37, 591, 67], [568, 38, 591, 68], [569, 16, 591, 70, "w"], [569, 17, 591, 71], [569, 19, 591, 73, "Math"], [569, 23, 591, 77], [569, 24, 591, 78, "round"], [569, 29, 591, 83], [569, 30, 591, 84, "<PERSON><PERSON><PERSON><PERSON>"], [569, 41, 591, 95], [569, 42, 591, 96], [570, 16, 591, 98, "h"], [570, 17, 591, 99], [570, 19, 591, 101, "Math"], [570, 23, 591, 105], [570, 24, 591, 106, "round"], [570, 29, 591, 111], [570, 30, 591, 112, "paddedHeight"], [570, 42, 591, 124], [571, 14, 591, 126], [572, 12, 592, 10], [572, 13, 592, 11], [572, 14, 592, 12], [574, 12, 594, 10], [575, 12, 595, 10, "console"], [575, 19, 595, 17], [575, 20, 595, 18, "log"], [575, 23, 595, 21], [575, 24, 595, 22], [575, 70, 595, 68], [575, 72, 595, 70], [576, 14, 596, 12, "width"], [576, 19, 596, 17], [576, 21, 596, 19, "canvas"], [576, 27, 596, 25], [576, 28, 596, 26, "width"], [576, 33, 596, 31], [577, 14, 597, 12, "height"], [577, 20, 597, 18], [577, 22, 597, 20, "canvas"], [577, 28, 597, 26], [577, 29, 597, 27, "height"], [577, 35, 597, 33], [578, 14, 598, 12, "contextValid"], [578, 26, 598, 24], [578, 28, 598, 26], [578, 29, 598, 27], [578, 30, 598, 28, "ctx"], [579, 12, 599, 10], [579, 13, 599, 11], [579, 14, 599, 12], [581, 12, 601, 10], [582, 12, 602, 10, "applyStrongBlur"], [582, 27, 602, 25], [582, 28, 602, 26, "ctx"], [582, 31, 602, 29], [582, 33, 602, 31, "paddedX"], [582, 40, 602, 38], [582, 42, 602, 40, "paddedY"], [582, 49, 602, 47], [582, 51, 602, 49, "<PERSON><PERSON><PERSON><PERSON>"], [582, 62, 602, 60], [582, 64, 602, 62, "paddedHeight"], [582, 76, 602, 74], [582, 77, 602, 75], [584, 12, 604, 10], [585, 12, 605, 10, "console"], [585, 19, 605, 17], [585, 20, 605, 18, "log"], [585, 23, 605, 21], [585, 24, 605, 22], [585, 102, 605, 100], [585, 103, 605, 101], [587, 12, 607, 10], [588, 12, 608, 10], [588, 18, 608, 16, "testImageData"], [588, 31, 608, 29], [588, 34, 608, 32, "ctx"], [588, 37, 608, 35], [588, 38, 608, 36, "getImageData"], [588, 50, 608, 48], [588, 51, 608, 49, "paddedX"], [588, 58, 608, 56], [588, 61, 608, 59], [588, 63, 608, 61], [588, 65, 608, 63, "paddedY"], [588, 72, 608, 70], [588, 75, 608, 73], [588, 77, 608, 75], [588, 79, 608, 77], [588, 81, 608, 79], [588, 83, 608, 81], [588, 85, 608, 83], [588, 86, 608, 84], [589, 12, 609, 10, "console"], [589, 19, 609, 17], [589, 20, 609, 18, "log"], [589, 23, 609, 21], [589, 24, 609, 22], [589, 70, 609, 68], [589, 72, 609, 70], [590, 14, 610, 12, "firstPixel"], [590, 24, 610, 22], [590, 26, 610, 24], [590, 27, 610, 25, "testImageData"], [590, 40, 610, 38], [590, 41, 610, 39, "data"], [590, 45, 610, 43], [590, 46, 610, 44], [590, 47, 610, 45], [590, 48, 610, 46], [590, 50, 610, 48, "testImageData"], [590, 63, 610, 61], [590, 64, 610, 62, "data"], [590, 68, 610, 66], [590, 69, 610, 67], [590, 70, 610, 68], [590, 71, 610, 69], [590, 73, 610, 71, "testImageData"], [590, 86, 610, 84], [590, 87, 610, 85, "data"], [590, 91, 610, 89], [590, 92, 610, 90], [590, 93, 610, 91], [590, 94, 610, 92], [590, 95, 610, 93], [591, 14, 611, 12, "secondPixel"], [591, 25, 611, 23], [591, 27, 611, 25], [591, 28, 611, 26, "testImageData"], [591, 41, 611, 39], [591, 42, 611, 40, "data"], [591, 46, 611, 44], [591, 47, 611, 45], [591, 48, 611, 46], [591, 49, 611, 47], [591, 51, 611, 49, "testImageData"], [591, 64, 611, 62], [591, 65, 611, 63, "data"], [591, 69, 611, 67], [591, 70, 611, 68], [591, 71, 611, 69], [591, 72, 611, 70], [591, 74, 611, 72, "testImageData"], [591, 87, 611, 85], [591, 88, 611, 86, "data"], [591, 92, 611, 90], [591, 93, 611, 91], [591, 94, 611, 92], [591, 95, 611, 93], [592, 12, 612, 10], [592, 13, 612, 11], [592, 14, 612, 12], [593, 12, 614, 10, "console"], [593, 19, 614, 17], [593, 20, 614, 18, "log"], [593, 23, 614, 21], [593, 24, 614, 22], [593, 50, 614, 48, "index"], [593, 55, 614, 53], [593, 58, 614, 56], [593, 59, 614, 57], [593, 79, 614, 77], [593, 80, 614, 78], [594, 10, 615, 8], [594, 11, 615, 9], [594, 12, 615, 10], [595, 10, 617, 8, "console"], [595, 17, 617, 15], [595, 18, 617, 16, "log"], [595, 21, 617, 19], [595, 22, 617, 20], [595, 48, 617, 46, "detectedFaces"], [595, 61, 617, 59], [595, 62, 617, 60, "length"], [595, 68, 617, 66], [595, 104, 617, 102], [595, 105, 617, 103], [596, 8, 618, 6], [596, 9, 618, 7], [596, 15, 618, 13], [597, 10, 619, 8, "console"], [597, 17, 619, 15], [597, 18, 619, 16, "log"], [597, 21, 619, 19], [597, 22, 619, 20], [597, 109, 619, 107], [597, 110, 619, 108], [598, 10, 620, 8], [599, 10, 621, 8, "applyFallbackFaceBlur"], [599, 31, 621, 29], [599, 32, 621, 30, "ctx"], [599, 35, 621, 33], [599, 37, 621, 35, "img"], [599, 40, 621, 38], [599, 41, 621, 39, "width"], [599, 46, 621, 44], [599, 48, 621, 46, "img"], [599, 51, 621, 49], [599, 52, 621, 50, "height"], [599, 58, 621, 56], [599, 59, 621, 57], [600, 8, 622, 6], [601, 8, 624, 6, "setProcessingProgress"], [601, 29, 624, 27], [601, 30, 624, 28], [601, 32, 624, 30], [601, 33, 624, 31], [603, 8, 626, 6], [604, 8, 627, 6, "console"], [604, 15, 627, 13], [604, 16, 627, 14, "log"], [604, 19, 627, 17], [604, 20, 627, 18], [604, 85, 627, 83], [604, 86, 627, 84], [605, 8, 628, 6], [605, 14, 628, 12, "blurredImageBlob"], [605, 30, 628, 28], [605, 33, 628, 31], [605, 39, 628, 37], [605, 43, 628, 41, "Promise"], [605, 50, 628, 48], [605, 51, 628, 56, "resolve"], [605, 58, 628, 63], [605, 62, 628, 68], [606, 10, 629, 8, "canvas"], [606, 16, 629, 14], [606, 17, 629, 15, "toBlob"], [606, 23, 629, 21], [606, 24, 629, 23, "blob"], [606, 28, 629, 27], [606, 32, 629, 32, "resolve"], [606, 39, 629, 39], [606, 40, 629, 40, "blob"], [606, 44, 629, 45], [606, 45, 629, 46], [606, 47, 629, 48], [606, 59, 629, 60], [606, 61, 629, 62], [606, 64, 629, 65], [606, 65, 629, 66], [607, 8, 630, 6], [607, 9, 630, 7], [607, 10, 630, 8], [608, 8, 632, 6], [608, 14, 632, 12, "blurredImageUrl"], [608, 29, 632, 27], [608, 32, 632, 30, "URL"], [608, 35, 632, 33], [608, 36, 632, 34, "createObjectURL"], [608, 51, 632, 49], [608, 52, 632, 50, "blurredImageBlob"], [608, 68, 632, 66], [608, 69, 632, 67], [609, 8, 633, 6, "console"], [609, 15, 633, 13], [609, 16, 633, 14, "log"], [609, 19, 633, 17], [609, 20, 633, 18], [609, 66, 633, 64], [609, 68, 633, 66, "blurredImageUrl"], [609, 83, 633, 81], [609, 84, 633, 82, "substring"], [609, 93, 633, 91], [609, 94, 633, 92], [609, 95, 633, 93], [609, 97, 633, 95], [609, 99, 633, 97], [609, 100, 633, 98], [609, 103, 633, 101], [609, 108, 633, 106], [609, 109, 633, 107], [610, 8, 635, 6, "setProcessingProgress"], [610, 29, 635, 27], [610, 30, 635, 28], [610, 33, 635, 31], [610, 34, 635, 32], [612, 8, 637, 6], [613, 8, 638, 6], [613, 14, 638, 12, "completeProcessing"], [613, 32, 638, 30], [613, 33, 638, 31, "blurredImageUrl"], [613, 48, 638, 46], [613, 49, 638, 47], [614, 6, 640, 4], [614, 7, 640, 5], [614, 8, 640, 6], [614, 15, 640, 13, "error"], [614, 20, 640, 18], [614, 22, 640, 20], [615, 8, 641, 6, "console"], [615, 15, 641, 13], [615, 16, 641, 14, "error"], [615, 21, 641, 19], [615, 22, 641, 20], [615, 57, 641, 55], [615, 59, 641, 57, "error"], [615, 64, 641, 62], [615, 65, 641, 63], [616, 8, 642, 6, "setErrorMessage"], [616, 23, 642, 21], [616, 24, 642, 22], [616, 50, 642, 48], [616, 51, 642, 49], [617, 8, 643, 6, "setProcessingState"], [617, 26, 643, 24], [617, 27, 643, 25], [617, 34, 643, 32], [617, 35, 643, 33], [618, 6, 644, 4], [619, 4, 645, 2], [619, 5, 645, 3], [621, 4, 647, 2], [622, 4, 648, 2], [622, 10, 648, 8, "completeProcessing"], [622, 28, 648, 26], [622, 31, 648, 29], [622, 37, 648, 36, "blurredImageUrl"], [622, 52, 648, 59], [622, 56, 648, 64], [623, 6, 649, 4], [623, 10, 649, 8], [624, 8, 650, 6, "setProcessingState"], [624, 26, 650, 24], [624, 27, 650, 25], [624, 37, 650, 35], [624, 38, 650, 36], [626, 8, 652, 6], [627, 8, 653, 6], [627, 14, 653, 12, "timestamp"], [627, 23, 653, 21], [627, 26, 653, 24, "Date"], [627, 30, 653, 28], [627, 31, 653, 29, "now"], [627, 34, 653, 32], [627, 35, 653, 33], [627, 36, 653, 34], [628, 8, 654, 6], [628, 14, 654, 12, "result"], [628, 20, 654, 18], [628, 23, 654, 21], [629, 10, 655, 8, "imageUrl"], [629, 18, 655, 16], [629, 20, 655, 18, "blurredImageUrl"], [629, 35, 655, 33], [630, 10, 656, 8, "localUri"], [630, 18, 656, 16], [630, 20, 656, 18, "blurredImageUrl"], [630, 35, 656, 33], [631, 10, 657, 8, "challengeCode"], [631, 23, 657, 21], [631, 25, 657, 23, "challengeCode"], [631, 38, 657, 36], [631, 42, 657, 40], [631, 44, 657, 42], [632, 10, 658, 8, "timestamp"], [632, 19, 658, 17], [633, 10, 659, 8, "jobId"], [633, 15, 659, 13], [633, 17, 659, 15], [633, 27, 659, 25, "timestamp"], [633, 36, 659, 34], [633, 38, 659, 36], [634, 10, 660, 8, "status"], [634, 16, 660, 14], [634, 18, 660, 16], [635, 8, 661, 6], [635, 9, 661, 7], [636, 8, 663, 6, "console"], [636, 15, 663, 13], [636, 16, 663, 14, "log"], [636, 19, 663, 17], [636, 20, 663, 18], [636, 100, 663, 98], [636, 102, 663, 100], [637, 10, 664, 8, "imageUrl"], [637, 18, 664, 16], [637, 20, 664, 18, "blurredImageUrl"], [637, 35, 664, 33], [637, 36, 664, 34, "substring"], [637, 45, 664, 43], [637, 46, 664, 44], [637, 47, 664, 45], [637, 49, 664, 47], [637, 51, 664, 49], [637, 52, 664, 50], [637, 55, 664, 53], [637, 60, 664, 58], [638, 10, 665, 8, "timestamp"], [638, 19, 665, 17], [639, 10, 666, 8, "jobId"], [639, 15, 666, 13], [639, 17, 666, 15, "result"], [639, 23, 666, 21], [639, 24, 666, 22, "jobId"], [640, 8, 667, 6], [640, 9, 667, 7], [640, 10, 667, 8], [642, 8, 669, 6], [643, 8, 670, 6, "onComplete"], [643, 18, 670, 16], [643, 19, 670, 17, "result"], [643, 25, 670, 23], [643, 26, 670, 24], [644, 6, 672, 4], [644, 7, 672, 5], [644, 8, 672, 6], [644, 15, 672, 13, "error"], [644, 20, 672, 18], [644, 22, 672, 20], [645, 8, 673, 6, "console"], [645, 15, 673, 13], [645, 16, 673, 14, "error"], [645, 21, 673, 19], [645, 22, 673, 20], [645, 57, 673, 55], [645, 59, 673, 57, "error"], [645, 64, 673, 62], [645, 65, 673, 63], [646, 8, 674, 6, "setErrorMessage"], [646, 23, 674, 21], [646, 24, 674, 22], [646, 56, 674, 54], [646, 57, 674, 55], [647, 8, 675, 6, "setProcessingState"], [647, 26, 675, 24], [647, 27, 675, 25], [647, 34, 675, 32], [647, 35, 675, 33], [648, 6, 676, 4], [649, 4, 677, 2], [649, 5, 677, 3], [651, 4, 679, 2], [652, 4, 680, 2], [652, 10, 680, 8, "triggerServerProcessing"], [652, 33, 680, 31], [652, 36, 680, 34], [652, 42, 680, 34, "triggerServerProcessing"], [652, 43, 680, 41, "privateImageUrl"], [652, 58, 680, 64], [652, 60, 680, 66, "timestamp"], [652, 69, 680, 83], [652, 74, 680, 88], [653, 6, 681, 4], [653, 10, 681, 8], [654, 8, 682, 6, "console"], [654, 15, 682, 13], [654, 16, 682, 14, "log"], [654, 19, 682, 17], [654, 20, 682, 18], [654, 74, 682, 72], [654, 76, 682, 74, "privateImageUrl"], [654, 91, 682, 89], [654, 92, 682, 90], [655, 8, 683, 6, "setProcessingState"], [655, 26, 683, 24], [655, 27, 683, 25], [655, 39, 683, 37], [655, 40, 683, 38], [656, 8, 684, 6, "setProcessingProgress"], [656, 29, 684, 27], [656, 30, 684, 28], [656, 32, 684, 30], [656, 33, 684, 31], [657, 8, 686, 6], [657, 14, 686, 12, "requestBody"], [657, 25, 686, 23], [657, 28, 686, 26], [658, 10, 687, 8, "imageUrl"], [658, 18, 687, 16], [658, 20, 687, 18, "privateImageUrl"], [658, 35, 687, 33], [659, 10, 688, 8, "userId"], [659, 16, 688, 14], [660, 10, 689, 8, "requestId"], [660, 19, 689, 17], [661, 10, 690, 8, "timestamp"], [661, 19, 690, 17], [662, 10, 691, 8, "platform"], [662, 18, 691, 16], [662, 20, 691, 18], [663, 8, 692, 6], [663, 9, 692, 7], [664, 8, 694, 6, "console"], [664, 15, 694, 13], [664, 16, 694, 14, "log"], [664, 19, 694, 17], [664, 20, 694, 18], [664, 65, 694, 63], [664, 67, 694, 65, "requestBody"], [664, 78, 694, 76], [664, 79, 694, 77], [666, 8, 696, 6], [667, 8, 697, 6], [667, 14, 697, 12, "response"], [667, 22, 697, 20], [667, 25, 697, 23], [667, 31, 697, 29, "fetch"], [667, 36, 697, 34], [667, 37, 697, 35], [667, 40, 697, 38, "API_BASE_URL"], [667, 52, 697, 50], [667, 72, 697, 70], [667, 74, 697, 72], [668, 10, 698, 8, "method"], [668, 16, 698, 14], [668, 18, 698, 16], [668, 24, 698, 22], [669, 10, 699, 8, "headers"], [669, 17, 699, 15], [669, 19, 699, 17], [670, 12, 700, 10], [670, 26, 700, 24], [670, 28, 700, 26], [670, 46, 700, 44], [671, 12, 701, 10], [671, 27, 701, 25], [671, 29, 701, 27], [671, 39, 701, 37], [671, 45, 701, 43, "getAuthToken"], [671, 57, 701, 55], [671, 58, 701, 56], [671, 59, 701, 57], [672, 10, 702, 8], [672, 11, 702, 9], [673, 10, 703, 8, "body"], [673, 14, 703, 12], [673, 16, 703, 14, "JSON"], [673, 20, 703, 18], [673, 21, 703, 19, "stringify"], [673, 30, 703, 28], [673, 31, 703, 29, "requestBody"], [673, 42, 703, 40], [674, 8, 704, 6], [674, 9, 704, 7], [674, 10, 704, 8], [675, 8, 706, 6], [675, 12, 706, 10], [675, 13, 706, 11, "response"], [675, 21, 706, 19], [675, 22, 706, 20, "ok"], [675, 24, 706, 22], [675, 26, 706, 24], [676, 10, 707, 8], [676, 16, 707, 14, "errorText"], [676, 25, 707, 23], [676, 28, 707, 26], [676, 34, 707, 32, "response"], [676, 42, 707, 40], [676, 43, 707, 41, "text"], [676, 47, 707, 45], [676, 48, 707, 46], [676, 49, 707, 47], [677, 10, 708, 8, "console"], [677, 17, 708, 15], [677, 18, 708, 16, "error"], [677, 23, 708, 21], [677, 24, 708, 22], [677, 68, 708, 66], [677, 70, 708, 68, "response"], [677, 78, 708, 76], [677, 79, 708, 77, "status"], [677, 85, 708, 83], [677, 87, 708, 85, "errorText"], [677, 96, 708, 94], [677, 97, 708, 95], [678, 10, 709, 8], [678, 16, 709, 14], [678, 20, 709, 18, "Error"], [678, 25, 709, 23], [678, 26, 709, 24], [678, 48, 709, 46, "response"], [678, 56, 709, 54], [678, 57, 709, 55, "status"], [678, 63, 709, 61], [678, 67, 709, 65, "response"], [678, 75, 709, 73], [678, 76, 709, 74, "statusText"], [678, 86, 709, 84], [678, 88, 709, 86], [678, 89, 709, 87], [679, 8, 710, 6], [680, 8, 712, 6], [680, 14, 712, 12, "result"], [680, 20, 712, 18], [680, 23, 712, 21], [680, 29, 712, 27, "response"], [680, 37, 712, 35], [680, 38, 712, 36, "json"], [680, 42, 712, 40], [680, 43, 712, 41], [680, 44, 712, 42], [681, 8, 713, 6, "console"], [681, 15, 713, 13], [681, 16, 713, 14, "log"], [681, 19, 713, 17], [681, 20, 713, 18], [681, 68, 713, 66], [681, 70, 713, 68, "result"], [681, 76, 713, 74], [681, 77, 713, 75], [682, 8, 715, 6], [682, 12, 715, 10], [682, 13, 715, 11, "result"], [682, 19, 715, 17], [682, 20, 715, 18, "jobId"], [682, 25, 715, 23], [682, 27, 715, 25], [683, 10, 716, 8], [683, 16, 716, 14], [683, 20, 716, 18, "Error"], [683, 25, 716, 23], [683, 26, 716, 24], [683, 70, 716, 68], [683, 71, 716, 69], [684, 8, 717, 6], [686, 8, 719, 6], [687, 8, 720, 6], [687, 14, 720, 12, "pollForCompletion"], [687, 31, 720, 29], [687, 32, 720, 30, "result"], [687, 38, 720, 36], [687, 39, 720, 37, "jobId"], [687, 44, 720, 42], [687, 46, 720, 44, "timestamp"], [687, 55, 720, 53], [687, 56, 720, 54], [688, 6, 721, 4], [688, 7, 721, 5], [688, 8, 721, 6], [688, 15, 721, 13, "error"], [688, 20, 721, 18], [688, 22, 721, 20], [689, 8, 722, 6, "console"], [689, 15, 722, 13], [689, 16, 722, 14, "error"], [689, 21, 722, 19], [689, 22, 722, 20], [689, 57, 722, 55], [689, 59, 722, 57, "error"], [689, 64, 722, 62], [689, 65, 722, 63], [690, 8, 723, 6, "setErrorMessage"], [690, 23, 723, 21], [690, 24, 723, 22], [690, 52, 723, 50, "error"], [690, 57, 723, 55], [690, 58, 723, 56, "message"], [690, 65, 723, 63], [690, 67, 723, 65], [690, 68, 723, 66], [691, 8, 724, 6, "setProcessingState"], [691, 26, 724, 24], [691, 27, 724, 25], [691, 34, 724, 32], [691, 35, 724, 33], [692, 6, 725, 4], [693, 4, 726, 2], [693, 5, 726, 3], [694, 4, 727, 2], [695, 4, 728, 2], [695, 10, 728, 8, "pollForCompletion"], [695, 27, 728, 25], [695, 30, 728, 28], [695, 36, 728, 28, "pollForCompletion"], [695, 37, 728, 35, "jobId"], [695, 42, 728, 48], [695, 44, 728, 50, "timestamp"], [695, 53, 728, 67], [695, 55, 728, 69, "attempts"], [695, 63, 728, 77], [695, 66, 728, 80], [695, 67, 728, 81], [695, 72, 728, 86], [696, 6, 729, 4], [696, 12, 729, 10, "MAX_ATTEMPTS"], [696, 24, 729, 22], [696, 27, 729, 25], [696, 29, 729, 27], [696, 30, 729, 28], [696, 31, 729, 29], [697, 6, 730, 4], [697, 12, 730, 10, "POLL_INTERVAL"], [697, 25, 730, 23], [697, 28, 730, 26], [697, 32, 730, 30], [697, 33, 730, 31], [697, 34, 730, 32], [699, 6, 732, 4, "console"], [699, 13, 732, 11], [699, 14, 732, 12, "log"], [699, 17, 732, 15], [699, 18, 732, 16], [699, 53, 732, 51, "attempts"], [699, 61, 732, 59], [699, 64, 732, 62], [699, 65, 732, 63], [699, 69, 732, 67, "MAX_ATTEMPTS"], [699, 81, 732, 79], [699, 93, 732, 91, "jobId"], [699, 98, 732, 96], [699, 100, 732, 98], [699, 101, 732, 99], [700, 6, 734, 4], [700, 10, 734, 8, "attempts"], [700, 18, 734, 16], [700, 22, 734, 20, "MAX_ATTEMPTS"], [700, 34, 734, 32], [700, 36, 734, 34], [701, 8, 735, 6, "console"], [701, 15, 735, 13], [701, 16, 735, 14, "error"], [701, 21, 735, 19], [701, 22, 735, 20], [701, 75, 735, 73], [701, 76, 735, 74], [702, 8, 736, 6, "setErrorMessage"], [702, 23, 736, 21], [702, 24, 736, 22], [702, 63, 736, 61], [702, 64, 736, 62], [703, 8, 737, 6, "setProcessingState"], [703, 26, 737, 24], [703, 27, 737, 25], [703, 34, 737, 32], [703, 35, 737, 33], [704, 8, 738, 6], [705, 6, 739, 4], [706, 6, 741, 4], [706, 10, 741, 8], [707, 8, 742, 6], [707, 14, 742, 12, "response"], [707, 22, 742, 20], [707, 25, 742, 23], [707, 31, 742, 29, "fetch"], [707, 36, 742, 34], [707, 37, 742, 35], [707, 40, 742, 38, "API_BASE_URL"], [707, 52, 742, 50], [707, 75, 742, 73, "jobId"], [707, 80, 742, 78], [707, 82, 742, 80], [707, 84, 742, 82], [708, 10, 743, 8, "headers"], [708, 17, 743, 15], [708, 19, 743, 17], [709, 12, 744, 10], [709, 27, 744, 25], [709, 29, 744, 27], [709, 39, 744, 37], [709, 45, 744, 43, "getAuthToken"], [709, 57, 744, 55], [709, 58, 744, 56], [709, 59, 744, 57], [710, 10, 745, 8], [711, 8, 746, 6], [711, 9, 746, 7], [711, 10, 746, 8], [712, 8, 748, 6], [712, 12, 748, 10], [712, 13, 748, 11, "response"], [712, 21, 748, 19], [712, 22, 748, 20, "ok"], [712, 24, 748, 22], [712, 26, 748, 24], [713, 10, 749, 8], [713, 16, 749, 14], [713, 20, 749, 18, "Error"], [713, 25, 749, 23], [713, 26, 749, 24], [713, 34, 749, 32, "response"], [713, 42, 749, 40], [713, 43, 749, 41, "status"], [713, 49, 749, 47], [713, 54, 749, 52, "response"], [713, 62, 749, 60], [713, 63, 749, 61, "statusText"], [713, 73, 749, 71], [713, 75, 749, 73], [713, 76, 749, 74], [714, 8, 750, 6], [715, 8, 752, 6], [715, 14, 752, 12, "status"], [715, 20, 752, 18], [715, 23, 752, 21], [715, 29, 752, 27, "response"], [715, 37, 752, 35], [715, 38, 752, 36, "json"], [715, 42, 752, 40], [715, 43, 752, 41], [715, 44, 752, 42], [716, 8, 753, 6, "console"], [716, 15, 753, 13], [716, 16, 753, 14, "log"], [716, 19, 753, 17], [716, 20, 753, 18], [716, 54, 753, 52], [716, 56, 753, 54, "status"], [716, 62, 753, 60], [716, 63, 753, 61], [717, 8, 755, 6], [717, 12, 755, 10, "status"], [717, 18, 755, 16], [717, 19, 755, 17, "status"], [717, 25, 755, 23], [717, 30, 755, 28], [717, 41, 755, 39], [717, 43, 755, 41], [718, 10, 756, 8, "console"], [718, 17, 756, 15], [718, 18, 756, 16, "log"], [718, 21, 756, 19], [718, 22, 756, 20], [718, 73, 756, 71], [718, 74, 756, 72], [719, 10, 757, 8, "setProcessingProgress"], [719, 31, 757, 29], [719, 32, 757, 30], [719, 35, 757, 33], [719, 36, 757, 34], [720, 10, 758, 8, "setProcessingState"], [720, 28, 758, 26], [720, 29, 758, 27], [720, 40, 758, 38], [720, 41, 758, 39], [721, 10, 759, 8], [722, 10, 760, 8], [722, 16, 760, 14, "result"], [722, 22, 760, 20], [722, 25, 760, 23], [723, 12, 761, 10, "imageUrl"], [723, 20, 761, 18], [723, 22, 761, 20, "status"], [723, 28, 761, 26], [723, 29, 761, 27, "publicUrl"], [723, 38, 761, 36], [724, 12, 761, 38], [725, 12, 762, 10, "localUri"], [725, 20, 762, 18], [725, 22, 762, 20, "capturedPhoto"], [725, 35, 762, 33], [725, 39, 762, 37, "status"], [725, 45, 762, 43], [725, 46, 762, 44, "publicUrl"], [725, 55, 762, 53], [726, 12, 762, 55], [727, 12, 763, 10, "challengeCode"], [727, 25, 763, 23], [727, 27, 763, 25, "challengeCode"], [727, 40, 763, 38], [727, 44, 763, 42], [727, 46, 763, 44], [728, 12, 764, 10, "timestamp"], [728, 21, 764, 19], [729, 12, 765, 10, "processingStatus"], [729, 28, 765, 26], [729, 30, 765, 28], [730, 10, 766, 8], [730, 11, 766, 9], [731, 10, 767, 8, "console"], [731, 17, 767, 15], [731, 18, 767, 16, "log"], [731, 21, 767, 19], [731, 22, 767, 20], [731, 57, 767, 55], [731, 59, 767, 57, "result"], [731, 65, 767, 63], [731, 66, 767, 64], [732, 10, 768, 8, "onComplete"], [732, 20, 768, 18], [732, 21, 768, 19, "result"], [732, 27, 768, 25], [732, 28, 768, 26], [733, 10, 769, 8], [734, 8, 770, 6], [734, 9, 770, 7], [734, 15, 770, 13], [734, 19, 770, 17, "status"], [734, 25, 770, 23], [734, 26, 770, 24, "status"], [734, 32, 770, 30], [734, 37, 770, 35], [734, 45, 770, 43], [734, 47, 770, 45], [735, 10, 771, 8, "console"], [735, 17, 771, 15], [735, 18, 771, 16, "error"], [735, 23, 771, 21], [735, 24, 771, 22], [735, 60, 771, 58], [735, 62, 771, 60, "status"], [735, 68, 771, 66], [735, 69, 771, 67, "error"], [735, 74, 771, 72], [735, 75, 771, 73], [736, 10, 772, 8], [736, 16, 772, 14], [736, 20, 772, 18, "Error"], [736, 25, 772, 23], [736, 26, 772, 24, "status"], [736, 32, 772, 30], [736, 33, 772, 31, "error"], [736, 38, 772, 36], [736, 42, 772, 40], [736, 61, 772, 59], [736, 62, 772, 60], [737, 8, 773, 6], [737, 9, 773, 7], [737, 15, 773, 13], [738, 10, 774, 8], [739, 10, 775, 8], [739, 16, 775, 14, "progressValue"], [739, 29, 775, 27], [739, 32, 775, 30], [739, 34, 775, 32], [739, 37, 775, 36, "attempts"], [739, 45, 775, 44], [739, 48, 775, 47, "MAX_ATTEMPTS"], [739, 60, 775, 59], [739, 63, 775, 63], [739, 65, 775, 65], [740, 10, 776, 8, "console"], [740, 17, 776, 15], [740, 18, 776, 16, "log"], [740, 21, 776, 19], [740, 22, 776, 20], [740, 71, 776, 69, "progressValue"], [740, 84, 776, 82], [740, 87, 776, 85], [740, 88, 776, 86], [741, 10, 777, 8, "setProcessingProgress"], [741, 31, 777, 29], [741, 32, 777, 30, "progressValue"], [741, 45, 777, 43], [741, 46, 777, 44], [742, 10, 779, 8, "setTimeout"], [742, 20, 779, 18], [742, 21, 779, 19], [742, 27, 779, 25], [743, 12, 780, 10, "pollForCompletion"], [743, 29, 780, 27], [743, 30, 780, 28, "jobId"], [743, 35, 780, 33], [743, 37, 780, 35, "timestamp"], [743, 46, 780, 44], [743, 48, 780, 46, "attempts"], [743, 56, 780, 54], [743, 59, 780, 57], [743, 60, 780, 58], [743, 61, 780, 59], [744, 10, 781, 8], [744, 11, 781, 9], [744, 13, 781, 11, "POLL_INTERVAL"], [744, 26, 781, 24], [744, 27, 781, 25], [745, 8, 782, 6], [746, 6, 783, 4], [746, 7, 783, 5], [746, 8, 783, 6], [746, 15, 783, 13, "error"], [746, 20, 783, 18], [746, 22, 783, 20], [747, 8, 784, 6, "console"], [747, 15, 784, 13], [747, 16, 784, 14, "error"], [747, 21, 784, 19], [747, 22, 784, 20], [747, 54, 784, 52], [747, 56, 784, 54, "error"], [747, 61, 784, 59], [747, 62, 784, 60], [748, 8, 785, 6, "setErrorMessage"], [748, 23, 785, 21], [748, 24, 785, 22], [748, 62, 785, 60, "error"], [748, 67, 785, 65], [748, 68, 785, 66, "message"], [748, 75, 785, 73], [748, 77, 785, 75], [748, 78, 785, 76], [749, 8, 786, 6, "setProcessingState"], [749, 26, 786, 24], [749, 27, 786, 25], [749, 34, 786, 32], [749, 35, 786, 33], [750, 6, 787, 4], [751, 4, 788, 2], [751, 5, 788, 3], [752, 4, 789, 2], [753, 4, 790, 2], [753, 10, 790, 8, "getAuthToken"], [753, 22, 790, 20], [753, 25, 790, 23], [753, 31, 790, 23, "getAuthToken"], [753, 32, 790, 23], [753, 37, 790, 52], [754, 6, 791, 4], [755, 6, 792, 4], [756, 6, 793, 4], [756, 13, 793, 11], [756, 30, 793, 28], [757, 4, 794, 2], [757, 5, 794, 3], [759, 4, 796, 2], [760, 4, 797, 2], [760, 10, 797, 8, "retryCapture"], [760, 22, 797, 20], [760, 25, 797, 23], [760, 29, 797, 23, "useCallback"], [760, 47, 797, 34], [760, 49, 797, 35], [760, 55, 797, 41], [761, 6, 798, 4, "console"], [761, 13, 798, 11], [761, 14, 798, 12, "log"], [761, 17, 798, 15], [761, 18, 798, 16], [761, 55, 798, 53], [761, 56, 798, 54], [762, 6, 799, 4, "setProcessingState"], [762, 24, 799, 22], [762, 25, 799, 23], [762, 31, 799, 29], [762, 32, 799, 30], [763, 6, 800, 4, "setErrorMessage"], [763, 21, 800, 19], [763, 22, 800, 20], [763, 24, 800, 22], [763, 25, 800, 23], [764, 6, 801, 4, "setCapturedPhoto"], [764, 22, 801, 20], [764, 23, 801, 21], [764, 25, 801, 23], [764, 26, 801, 24], [765, 6, 802, 4, "setProcessingProgress"], [765, 27, 802, 25], [765, 28, 802, 26], [765, 29, 802, 27], [765, 30, 802, 28], [766, 4, 803, 2], [766, 5, 803, 3], [766, 7, 803, 5], [766, 9, 803, 7], [766, 10, 803, 8], [767, 4, 804, 2], [768, 4, 805, 2], [768, 8, 805, 2, "useEffect"], [768, 24, 805, 11], [768, 26, 805, 12], [768, 32, 805, 18], [769, 6, 806, 4, "console"], [769, 13, 806, 11], [769, 14, 806, 12, "log"], [769, 17, 806, 15], [769, 18, 806, 16], [769, 53, 806, 51], [769, 55, 806, 53, "permission"], [769, 65, 806, 63], [769, 66, 806, 64], [770, 6, 807, 4], [770, 10, 807, 8, "permission"], [770, 20, 807, 18], [770, 22, 807, 20], [771, 8, 808, 6, "console"], [771, 15, 808, 13], [771, 16, 808, 14, "log"], [771, 19, 808, 17], [771, 20, 808, 18], [771, 57, 808, 55], [771, 59, 808, 57, "permission"], [771, 69, 808, 67], [771, 70, 808, 68, "granted"], [771, 77, 808, 75], [771, 78, 808, 76], [772, 6, 809, 4], [773, 4, 810, 2], [773, 5, 810, 3], [773, 7, 810, 5], [773, 8, 810, 6, "permission"], [773, 18, 810, 16], [773, 19, 810, 17], [773, 20, 810, 18], [774, 4, 811, 2], [775, 4, 812, 2], [775, 8, 812, 6], [775, 9, 812, 7, "permission"], [775, 19, 812, 17], [775, 21, 812, 19], [776, 6, 813, 4, "console"], [776, 13, 813, 11], [776, 14, 813, 12, "log"], [776, 17, 813, 15], [776, 18, 813, 16], [776, 67, 813, 65], [776, 68, 813, 66], [777, 6, 814, 4], [777, 26, 815, 6], [777, 30, 815, 6, "_jsxDevRuntime"], [777, 44, 815, 6], [777, 45, 815, 6, "jsxDEV"], [777, 51, 815, 6], [777, 53, 815, 7, "_View"], [777, 58, 815, 7], [777, 59, 815, 7, "default"], [777, 66, 815, 11], [778, 8, 815, 12, "style"], [778, 13, 815, 17], [778, 15, 815, 19, "styles"], [778, 21, 815, 25], [778, 22, 815, 26, "container"], [778, 31, 815, 36], [779, 8, 815, 36, "children"], [779, 16, 815, 36], [779, 32, 816, 8], [779, 36, 816, 8, "_jsxDevRuntime"], [779, 50, 816, 8], [779, 51, 816, 8, "jsxDEV"], [779, 57, 816, 8], [779, 59, 816, 9, "_ActivityIndicator"], [779, 77, 816, 9], [779, 78, 816, 9, "default"], [779, 85, 816, 26], [780, 10, 816, 27, "size"], [780, 14, 816, 31], [780, 16, 816, 32], [780, 23, 816, 39], [781, 10, 816, 40, "color"], [781, 15, 816, 45], [781, 17, 816, 46], [782, 8, 816, 55], [783, 10, 816, 55, "fileName"], [783, 18, 816, 55], [783, 20, 816, 55, "_jsxFileName"], [783, 32, 816, 55], [784, 10, 816, 55, "lineNumber"], [784, 20, 816, 55], [785, 10, 816, 55, "columnNumber"], [785, 22, 816, 55], [786, 8, 816, 55], [786, 15, 816, 57], [786, 16, 816, 58], [786, 31, 817, 8], [786, 35, 817, 8, "_jsxDevRuntime"], [786, 49, 817, 8], [786, 50, 817, 8, "jsxDEV"], [786, 56, 817, 8], [786, 58, 817, 9, "_Text"], [786, 63, 817, 9], [786, 64, 817, 9, "default"], [786, 71, 817, 13], [787, 10, 817, 14, "style"], [787, 15, 817, 19], [787, 17, 817, 21, "styles"], [787, 23, 817, 27], [787, 24, 817, 28, "loadingText"], [787, 35, 817, 40], [788, 10, 817, 40, "children"], [788, 18, 817, 40], [788, 20, 817, 41], [789, 8, 817, 58], [790, 10, 817, 58, "fileName"], [790, 18, 817, 58], [790, 20, 817, 58, "_jsxFileName"], [790, 32, 817, 58], [791, 10, 817, 58, "lineNumber"], [791, 20, 817, 58], [792, 10, 817, 58, "columnNumber"], [792, 22, 817, 58], [793, 8, 817, 58], [793, 15, 817, 64], [793, 16, 817, 65], [794, 6, 817, 65], [795, 8, 817, 65, "fileName"], [795, 16, 817, 65], [795, 18, 817, 65, "_jsxFileName"], [795, 30, 817, 65], [796, 8, 817, 65, "lineNumber"], [796, 18, 817, 65], [797, 8, 817, 65, "columnNumber"], [797, 20, 817, 65], [798, 6, 817, 65], [798, 13, 818, 12], [798, 14, 818, 13], [799, 4, 820, 2], [800, 4, 821, 2], [800, 8, 821, 6], [800, 9, 821, 7, "permission"], [800, 19, 821, 17], [800, 20, 821, 18, "granted"], [800, 27, 821, 25], [800, 29, 821, 27], [801, 6, 822, 4, "console"], [801, 13, 822, 11], [801, 14, 822, 12, "log"], [801, 17, 822, 15], [801, 18, 822, 16], [801, 93, 822, 91], [801, 94, 822, 92], [802, 6, 823, 4], [802, 26, 824, 6], [802, 30, 824, 6, "_jsxDevRuntime"], [802, 44, 824, 6], [802, 45, 824, 6, "jsxDEV"], [802, 51, 824, 6], [802, 53, 824, 7, "_View"], [802, 58, 824, 7], [802, 59, 824, 7, "default"], [802, 66, 824, 11], [803, 8, 824, 12, "style"], [803, 13, 824, 17], [803, 15, 824, 19, "styles"], [803, 21, 824, 25], [803, 22, 824, 26, "container"], [803, 31, 824, 36], [804, 8, 824, 36, "children"], [804, 16, 824, 36], [804, 31, 825, 8], [804, 35, 825, 8, "_jsxDevRuntime"], [804, 49, 825, 8], [804, 50, 825, 8, "jsxDEV"], [804, 56, 825, 8], [804, 58, 825, 9, "_View"], [804, 63, 825, 9], [804, 64, 825, 9, "default"], [804, 71, 825, 13], [805, 10, 825, 14, "style"], [805, 15, 825, 19], [805, 17, 825, 21, "styles"], [805, 23, 825, 27], [805, 24, 825, 28, "permissionContent"], [805, 41, 825, 46], [806, 10, 825, 46, "children"], [806, 18, 825, 46], [806, 34, 826, 10], [806, 38, 826, 10, "_jsxDevRuntime"], [806, 52, 826, 10], [806, 53, 826, 10, "jsxDEV"], [806, 59, 826, 10], [806, 61, 826, 11, "_lucideReactNative"], [806, 79, 826, 11], [806, 80, 826, 11, "Camera"], [806, 86, 826, 21], [807, 12, 826, 22, "size"], [807, 16, 826, 26], [807, 18, 826, 28], [807, 20, 826, 31], [808, 12, 826, 32, "color"], [808, 17, 826, 37], [808, 19, 826, 38], [809, 10, 826, 47], [810, 12, 826, 47, "fileName"], [810, 20, 826, 47], [810, 22, 826, 47, "_jsxFileName"], [810, 34, 826, 47], [811, 12, 826, 47, "lineNumber"], [811, 22, 826, 47], [812, 12, 826, 47, "columnNumber"], [812, 24, 826, 47], [813, 10, 826, 47], [813, 17, 826, 49], [813, 18, 826, 50], [813, 33, 827, 10], [813, 37, 827, 10, "_jsxDevRuntime"], [813, 51, 827, 10], [813, 52, 827, 10, "jsxDEV"], [813, 58, 827, 10], [813, 60, 827, 11, "_Text"], [813, 65, 827, 11], [813, 66, 827, 11, "default"], [813, 73, 827, 15], [814, 12, 827, 16, "style"], [814, 17, 827, 21], [814, 19, 827, 23, "styles"], [814, 25, 827, 29], [814, 26, 827, 30, "permissionTitle"], [814, 41, 827, 46], [815, 12, 827, 46, "children"], [815, 20, 827, 46], [815, 22, 827, 47], [816, 10, 827, 73], [817, 12, 827, 73, "fileName"], [817, 20, 827, 73], [817, 22, 827, 73, "_jsxFileName"], [817, 34, 827, 73], [818, 12, 827, 73, "lineNumber"], [818, 22, 827, 73], [819, 12, 827, 73, "columnNumber"], [819, 24, 827, 73], [820, 10, 827, 73], [820, 17, 827, 79], [820, 18, 827, 80], [820, 33, 828, 10], [820, 37, 828, 10, "_jsxDevRuntime"], [820, 51, 828, 10], [820, 52, 828, 10, "jsxDEV"], [820, 58, 828, 10], [820, 60, 828, 11, "_Text"], [820, 65, 828, 11], [820, 66, 828, 11, "default"], [820, 73, 828, 15], [821, 12, 828, 16, "style"], [821, 17, 828, 21], [821, 19, 828, 23, "styles"], [821, 25, 828, 29], [821, 26, 828, 30, "permissionDescription"], [821, 47, 828, 52], [822, 12, 828, 52, "children"], [822, 20, 828, 52], [822, 22, 828, 53], [823, 10, 831, 10], [824, 12, 831, 10, "fileName"], [824, 20, 831, 10], [824, 22, 831, 10, "_jsxFileName"], [824, 34, 831, 10], [825, 12, 831, 10, "lineNumber"], [825, 22, 831, 10], [826, 12, 831, 10, "columnNumber"], [826, 24, 831, 10], [827, 10, 831, 10], [827, 17, 831, 16], [827, 18, 831, 17], [827, 33, 832, 10], [827, 37, 832, 10, "_jsxDevRuntime"], [827, 51, 832, 10], [827, 52, 832, 10, "jsxDEV"], [827, 58, 832, 10], [827, 60, 832, 11, "_TouchableOpacity"], [827, 77, 832, 11], [827, 78, 832, 11, "default"], [827, 85, 832, 27], [828, 12, 832, 28, "onPress"], [828, 19, 832, 35], [828, 21, 832, 37, "requestPermission"], [828, 38, 832, 55], [829, 12, 832, 56, "style"], [829, 17, 832, 61], [829, 19, 832, 63, "styles"], [829, 25, 832, 69], [829, 26, 832, 70, "primaryButton"], [829, 39, 832, 84], [830, 12, 832, 84, "children"], [830, 20, 832, 84], [830, 35, 833, 12], [830, 39, 833, 12, "_jsxDevRuntime"], [830, 53, 833, 12], [830, 54, 833, 12, "jsxDEV"], [830, 60, 833, 12], [830, 62, 833, 13, "_Text"], [830, 67, 833, 13], [830, 68, 833, 13, "default"], [830, 75, 833, 17], [831, 14, 833, 18, "style"], [831, 19, 833, 23], [831, 21, 833, 25, "styles"], [831, 27, 833, 31], [831, 28, 833, 32, "primaryButtonText"], [831, 45, 833, 50], [832, 14, 833, 50, "children"], [832, 22, 833, 50], [832, 24, 833, 51], [833, 12, 833, 67], [834, 14, 833, 67, "fileName"], [834, 22, 833, 67], [834, 24, 833, 67, "_jsxFileName"], [834, 36, 833, 67], [835, 14, 833, 67, "lineNumber"], [835, 24, 833, 67], [836, 14, 833, 67, "columnNumber"], [836, 26, 833, 67], [837, 12, 833, 67], [837, 19, 833, 73], [838, 10, 833, 74], [839, 12, 833, 74, "fileName"], [839, 20, 833, 74], [839, 22, 833, 74, "_jsxFileName"], [839, 34, 833, 74], [840, 12, 833, 74, "lineNumber"], [840, 22, 833, 74], [841, 12, 833, 74, "columnNumber"], [841, 24, 833, 74], [842, 10, 833, 74], [842, 17, 834, 28], [842, 18, 834, 29], [842, 33, 835, 10], [842, 37, 835, 10, "_jsxDevRuntime"], [842, 51, 835, 10], [842, 52, 835, 10, "jsxDEV"], [842, 58, 835, 10], [842, 60, 835, 11, "_TouchableOpacity"], [842, 77, 835, 11], [842, 78, 835, 11, "default"], [842, 85, 835, 27], [843, 12, 835, 28, "onPress"], [843, 19, 835, 35], [843, 21, 835, 37, "onCancel"], [843, 29, 835, 46], [844, 12, 835, 47, "style"], [844, 17, 835, 52], [844, 19, 835, 54, "styles"], [844, 25, 835, 60], [844, 26, 835, 61, "secondaryButton"], [844, 41, 835, 77], [845, 12, 835, 77, "children"], [845, 20, 835, 77], [845, 35, 836, 12], [845, 39, 836, 12, "_jsxDevRuntime"], [845, 53, 836, 12], [845, 54, 836, 12, "jsxDEV"], [845, 60, 836, 12], [845, 62, 836, 13, "_Text"], [845, 67, 836, 13], [845, 68, 836, 13, "default"], [845, 75, 836, 17], [846, 14, 836, 18, "style"], [846, 19, 836, 23], [846, 21, 836, 25, "styles"], [846, 27, 836, 31], [846, 28, 836, 32, "secondaryButtonText"], [846, 47, 836, 52], [847, 14, 836, 52, "children"], [847, 22, 836, 52], [847, 24, 836, 53], [848, 12, 836, 59], [849, 14, 836, 59, "fileName"], [849, 22, 836, 59], [849, 24, 836, 59, "_jsxFileName"], [849, 36, 836, 59], [850, 14, 836, 59, "lineNumber"], [850, 24, 836, 59], [851, 14, 836, 59, "columnNumber"], [851, 26, 836, 59], [852, 12, 836, 59], [852, 19, 836, 65], [853, 10, 836, 66], [854, 12, 836, 66, "fileName"], [854, 20, 836, 66], [854, 22, 836, 66, "_jsxFileName"], [854, 34, 836, 66], [855, 12, 836, 66, "lineNumber"], [855, 22, 836, 66], [856, 12, 836, 66, "columnNumber"], [856, 24, 836, 66], [857, 10, 836, 66], [857, 17, 837, 28], [857, 18, 837, 29], [858, 8, 837, 29], [859, 10, 837, 29, "fileName"], [859, 18, 837, 29], [859, 20, 837, 29, "_jsxFileName"], [859, 32, 837, 29], [860, 10, 837, 29, "lineNumber"], [860, 20, 837, 29], [861, 10, 837, 29, "columnNumber"], [861, 22, 837, 29], [862, 8, 837, 29], [862, 15, 838, 14], [863, 6, 838, 15], [864, 8, 838, 15, "fileName"], [864, 16, 838, 15], [864, 18, 838, 15, "_jsxFileName"], [864, 30, 838, 15], [865, 8, 838, 15, "lineNumber"], [865, 18, 838, 15], [866, 8, 838, 15, "columnNumber"], [866, 20, 838, 15], [867, 6, 838, 15], [867, 13, 839, 12], [867, 14, 839, 13], [868, 4, 841, 2], [869, 4, 842, 2], [870, 4, 843, 2, "console"], [870, 11, 843, 9], [870, 12, 843, 10, "log"], [870, 15, 843, 13], [870, 16, 843, 14], [870, 55, 843, 53], [870, 56, 843, 54], [871, 4, 845, 2], [871, 24, 846, 4], [871, 28, 846, 4, "_jsxDevRuntime"], [871, 42, 846, 4], [871, 43, 846, 4, "jsxDEV"], [871, 49, 846, 4], [871, 51, 846, 5, "_View"], [871, 56, 846, 5], [871, 57, 846, 5, "default"], [871, 64, 846, 9], [872, 6, 846, 10, "style"], [872, 11, 846, 15], [872, 13, 846, 17, "styles"], [872, 19, 846, 23], [872, 20, 846, 24, "container"], [872, 29, 846, 34], [873, 6, 846, 34, "children"], [873, 14, 846, 34], [873, 30, 848, 6], [873, 34, 848, 6, "_jsxDevRuntime"], [873, 48, 848, 6], [873, 49, 848, 6, "jsxDEV"], [873, 55, 848, 6], [873, 57, 848, 7, "_View"], [873, 62, 848, 7], [873, 63, 848, 7, "default"], [873, 70, 848, 11], [874, 8, 848, 12, "style"], [874, 13, 848, 17], [874, 15, 848, 19, "styles"], [874, 21, 848, 25], [874, 22, 848, 26, "cameraContainer"], [874, 37, 848, 42], [875, 8, 848, 43, "id"], [875, 10, 848, 45], [875, 12, 848, 46], [875, 29, 848, 63], [876, 8, 848, 63, "children"], [876, 16, 848, 63], [876, 32, 849, 8], [876, 36, 849, 8, "_jsxDevRuntime"], [876, 50, 849, 8], [876, 51, 849, 8, "jsxDEV"], [876, 57, 849, 8], [876, 59, 849, 9, "_expoCamera"], [876, 70, 849, 9], [876, 71, 849, 9, "CameraView"], [876, 81, 849, 19], [877, 10, 850, 10, "ref"], [877, 13, 850, 13], [877, 15, 850, 15, "cameraRef"], [877, 24, 850, 25], [878, 10, 851, 10, "style"], [878, 15, 851, 15], [878, 17, 851, 17], [878, 18, 851, 18, "styles"], [878, 24, 851, 24], [878, 25, 851, 25, "camera"], [878, 31, 851, 31], [878, 33, 851, 33], [879, 12, 851, 35, "backgroundColor"], [879, 27, 851, 50], [879, 29, 851, 52], [880, 10, 851, 62], [880, 11, 851, 63], [880, 12, 851, 65], [881, 10, 852, 10, "facing"], [881, 16, 852, 16], [881, 18, 852, 17], [881, 24, 852, 23], [882, 10, 853, 10, "onLayout"], [882, 18, 853, 18], [882, 20, 853, 21, "e"], [882, 21, 853, 22], [882, 25, 853, 27], [883, 12, 854, 12, "console"], [883, 19, 854, 19], [883, 20, 854, 20, "log"], [883, 23, 854, 23], [883, 24, 854, 24], [883, 56, 854, 56], [883, 58, 854, 58, "e"], [883, 59, 854, 59], [883, 60, 854, 60, "nativeEvent"], [883, 71, 854, 71], [883, 72, 854, 72, "layout"], [883, 78, 854, 78], [883, 79, 854, 79], [884, 12, 855, 12, "setViewSize"], [884, 23, 855, 23], [884, 24, 855, 24], [885, 14, 855, 26, "width"], [885, 19, 855, 31], [885, 21, 855, 33, "e"], [885, 22, 855, 34], [885, 23, 855, 35, "nativeEvent"], [885, 34, 855, 46], [885, 35, 855, 47, "layout"], [885, 41, 855, 53], [885, 42, 855, 54, "width"], [885, 47, 855, 59], [886, 14, 855, 61, "height"], [886, 20, 855, 67], [886, 22, 855, 69, "e"], [886, 23, 855, 70], [886, 24, 855, 71, "nativeEvent"], [886, 35, 855, 82], [886, 36, 855, 83, "layout"], [886, 42, 855, 89], [886, 43, 855, 90, "height"], [887, 12, 855, 97], [887, 13, 855, 98], [887, 14, 855, 99], [888, 10, 856, 10], [888, 11, 856, 12], [889, 10, 857, 10, "onCameraReady"], [889, 23, 857, 23], [889, 25, 857, 25, "onCameraReady"], [889, 26, 857, 25], [889, 31, 857, 31], [890, 12, 858, 12, "console"], [890, 19, 858, 19], [890, 20, 858, 20, "log"], [890, 23, 858, 23], [890, 24, 858, 24], [890, 55, 858, 55], [890, 56, 858, 56], [891, 12, 859, 12, "setIsCameraReady"], [891, 28, 859, 28], [891, 29, 859, 29], [891, 33, 859, 33], [891, 34, 859, 34], [891, 35, 859, 35], [891, 36, 859, 36], [892, 10, 860, 10], [892, 11, 860, 12], [893, 10, 861, 10, "onMountError"], [893, 22, 861, 22], [893, 24, 861, 25, "error"], [893, 29, 861, 30], [893, 33, 861, 35], [894, 12, 862, 12, "console"], [894, 19, 862, 19], [894, 20, 862, 20, "error"], [894, 25, 862, 25], [894, 26, 862, 26], [894, 63, 862, 63], [894, 65, 862, 65, "error"], [894, 70, 862, 70], [894, 71, 862, 71], [895, 12, 863, 12, "setErrorMessage"], [895, 27, 863, 27], [895, 28, 863, 28], [895, 57, 863, 57], [895, 58, 863, 58], [896, 12, 864, 12, "setProcessingState"], [896, 30, 864, 30], [896, 31, 864, 31], [896, 38, 864, 38], [896, 39, 864, 39], [897, 10, 865, 10], [898, 8, 865, 12], [899, 10, 865, 12, "fileName"], [899, 18, 865, 12], [899, 20, 865, 12, "_jsxFileName"], [899, 32, 865, 12], [900, 10, 865, 12, "lineNumber"], [900, 20, 865, 12], [901, 10, 865, 12, "columnNumber"], [901, 22, 865, 12], [902, 8, 865, 12], [902, 15, 866, 9], [902, 16, 866, 10], [902, 18, 868, 9], [902, 19, 868, 10, "isCameraReady"], [902, 32, 868, 23], [902, 49, 869, 10], [902, 53, 869, 10, "_jsxDevRuntime"], [902, 67, 869, 10], [902, 68, 869, 10, "jsxDEV"], [902, 74, 869, 10], [902, 76, 869, 11, "_View"], [902, 81, 869, 11], [902, 82, 869, 11, "default"], [902, 89, 869, 15], [903, 10, 869, 16, "style"], [903, 15, 869, 21], [903, 17, 869, 23], [903, 18, 869, 24, "StyleSheet"], [903, 37, 869, 34], [903, 38, 869, 35, "absoluteFill"], [903, 50, 869, 47], [903, 52, 869, 49], [904, 12, 869, 51, "backgroundColor"], [904, 27, 869, 66], [904, 29, 869, 68], [904, 49, 869, 88], [905, 12, 869, 90, "justifyContent"], [905, 26, 869, 104], [905, 28, 869, 106], [905, 36, 869, 114], [906, 12, 869, 116, "alignItems"], [906, 22, 869, 126], [906, 24, 869, 128], [906, 32, 869, 136], [907, 12, 869, 138, "zIndex"], [907, 18, 869, 144], [907, 20, 869, 146], [908, 10, 869, 151], [908, 11, 869, 152], [908, 12, 869, 154], [909, 10, 869, 154, "children"], [909, 18, 869, 154], [909, 33, 870, 12], [909, 37, 870, 12, "_jsxDevRuntime"], [909, 51, 870, 12], [909, 52, 870, 12, "jsxDEV"], [909, 58, 870, 12], [909, 60, 870, 13, "_View"], [909, 65, 870, 13], [909, 66, 870, 13, "default"], [909, 73, 870, 17], [910, 12, 870, 18, "style"], [910, 17, 870, 23], [910, 19, 870, 25], [911, 14, 870, 27, "backgroundColor"], [911, 29, 870, 42], [911, 31, 870, 44], [911, 51, 870, 64], [912, 14, 870, 66, "padding"], [912, 21, 870, 73], [912, 23, 870, 75], [912, 25, 870, 77], [913, 14, 870, 79, "borderRadius"], [913, 26, 870, 91], [913, 28, 870, 93], [913, 30, 870, 95], [914, 14, 870, 97, "alignItems"], [914, 24, 870, 107], [914, 26, 870, 109], [915, 12, 870, 118], [915, 13, 870, 120], [916, 12, 870, 120, "children"], [916, 20, 870, 120], [916, 36, 871, 14], [916, 40, 871, 14, "_jsxDevRuntime"], [916, 54, 871, 14], [916, 55, 871, 14, "jsxDEV"], [916, 61, 871, 14], [916, 63, 871, 15, "_ActivityIndicator"], [916, 81, 871, 15], [916, 82, 871, 15, "default"], [916, 89, 871, 32], [917, 14, 871, 33, "size"], [917, 18, 871, 37], [917, 20, 871, 38], [917, 27, 871, 45], [918, 14, 871, 46, "color"], [918, 19, 871, 51], [918, 21, 871, 52], [918, 30, 871, 61], [919, 14, 871, 62, "style"], [919, 19, 871, 67], [919, 21, 871, 69], [920, 16, 871, 71, "marginBottom"], [920, 28, 871, 83], [920, 30, 871, 85], [921, 14, 871, 88], [922, 12, 871, 90], [923, 14, 871, 90, "fileName"], [923, 22, 871, 90], [923, 24, 871, 90, "_jsxFileName"], [923, 36, 871, 90], [924, 14, 871, 90, "lineNumber"], [924, 24, 871, 90], [925, 14, 871, 90, "columnNumber"], [925, 26, 871, 90], [926, 12, 871, 90], [926, 19, 871, 92], [926, 20, 871, 93], [926, 35, 872, 14], [926, 39, 872, 14, "_jsxDevRuntime"], [926, 53, 872, 14], [926, 54, 872, 14, "jsxDEV"], [926, 60, 872, 14], [926, 62, 872, 15, "_Text"], [926, 67, 872, 15], [926, 68, 872, 15, "default"], [926, 75, 872, 19], [927, 14, 872, 20, "style"], [927, 19, 872, 25], [927, 21, 872, 27], [928, 16, 872, 29, "color"], [928, 21, 872, 34], [928, 23, 872, 36], [928, 29, 872, 42], [929, 16, 872, 44, "fontSize"], [929, 24, 872, 52], [929, 26, 872, 54], [929, 28, 872, 56], [930, 16, 872, 58, "fontWeight"], [930, 26, 872, 68], [930, 28, 872, 70], [931, 14, 872, 76], [931, 15, 872, 78], [932, 14, 872, 78, "children"], [932, 22, 872, 78], [932, 24, 872, 79], [933, 12, 872, 101], [934, 14, 872, 101, "fileName"], [934, 22, 872, 101], [934, 24, 872, 101, "_jsxFileName"], [934, 36, 872, 101], [935, 14, 872, 101, "lineNumber"], [935, 24, 872, 101], [936, 14, 872, 101, "columnNumber"], [936, 26, 872, 101], [937, 12, 872, 101], [937, 19, 872, 107], [937, 20, 872, 108], [937, 35, 873, 14], [937, 39, 873, 14, "_jsxDevRuntime"], [937, 53, 873, 14], [937, 54, 873, 14, "jsxDEV"], [937, 60, 873, 14], [937, 62, 873, 15, "_Text"], [937, 67, 873, 15], [937, 68, 873, 15, "default"], [937, 75, 873, 19], [938, 14, 873, 20, "style"], [938, 19, 873, 25], [938, 21, 873, 27], [939, 16, 873, 29, "color"], [939, 21, 873, 34], [939, 23, 873, 36], [939, 32, 873, 45], [940, 16, 873, 47, "fontSize"], [940, 24, 873, 55], [940, 26, 873, 57], [940, 28, 873, 59], [941, 16, 873, 61, "marginTop"], [941, 25, 873, 70], [941, 27, 873, 72], [942, 14, 873, 74], [942, 15, 873, 76], [943, 14, 873, 76, "children"], [943, 22, 873, 76], [943, 24, 873, 77], [944, 12, 873, 88], [945, 14, 873, 88, "fileName"], [945, 22, 873, 88], [945, 24, 873, 88, "_jsxFileName"], [945, 36, 873, 88], [946, 14, 873, 88, "lineNumber"], [946, 24, 873, 88], [947, 14, 873, 88, "columnNumber"], [947, 26, 873, 88], [948, 12, 873, 88], [948, 19, 873, 94], [948, 20, 873, 95], [949, 10, 873, 95], [950, 12, 873, 95, "fileName"], [950, 20, 873, 95], [950, 22, 873, 95, "_jsxFileName"], [950, 34, 873, 95], [951, 12, 873, 95, "lineNumber"], [951, 22, 873, 95], [952, 12, 873, 95, "columnNumber"], [952, 24, 873, 95], [953, 10, 873, 95], [953, 17, 874, 18], [954, 8, 874, 19], [955, 10, 874, 19, "fileName"], [955, 18, 874, 19], [955, 20, 874, 19, "_jsxFileName"], [955, 32, 874, 19], [956, 10, 874, 19, "lineNumber"], [956, 20, 874, 19], [957, 10, 874, 19, "columnNumber"], [957, 22, 874, 19], [958, 8, 874, 19], [958, 15, 875, 16], [958, 16, 876, 9], [958, 18, 879, 9, "isCameraReady"], [958, 31, 879, 22], [958, 35, 879, 26, "previewBlurEnabled"], [958, 53, 879, 44], [958, 57, 879, 48, "viewSize"], [958, 65, 879, 56], [958, 66, 879, 57, "width"], [958, 71, 879, 62], [958, 74, 879, 65], [958, 75, 879, 66], [958, 92, 880, 10], [958, 96, 880, 10, "_jsxDevRuntime"], [958, 110, 880, 10], [958, 111, 880, 10, "jsxDEV"], [958, 117, 880, 10], [958, 119, 880, 10, "_jsxDevRuntime"], [958, 133, 880, 10], [958, 134, 880, 10, "Fragment"], [958, 142, 880, 10], [959, 10, 880, 10, "children"], [959, 18, 880, 10], [959, 34, 882, 12], [959, 38, 882, 12, "_jsxDevRuntime"], [959, 52, 882, 12], [959, 53, 882, 12, "jsxDEV"], [959, 59, 882, 12], [959, 61, 882, 13, "_LiveFaceCanvas"], [959, 76, 882, 13], [959, 77, 882, 13, "default"], [959, 84, 882, 27], [960, 12, 882, 28, "containerId"], [960, 23, 882, 39], [960, 25, 882, 40], [960, 42, 882, 57], [961, 12, 882, 58, "width"], [961, 17, 882, 63], [961, 19, 882, 65, "viewSize"], [961, 27, 882, 73], [961, 28, 882, 74, "width"], [961, 33, 882, 80], [962, 12, 882, 81, "height"], [962, 18, 882, 87], [962, 20, 882, 89, "viewSize"], [962, 28, 882, 97], [962, 29, 882, 98, "height"], [963, 10, 882, 105], [964, 12, 882, 105, "fileName"], [964, 20, 882, 105], [964, 22, 882, 105, "_jsxFileName"], [964, 34, 882, 105], [965, 12, 882, 105, "lineNumber"], [965, 22, 882, 105], [966, 12, 882, 105, "columnNumber"], [966, 24, 882, 105], [967, 10, 882, 105], [967, 17, 882, 107], [967, 18, 882, 108], [967, 33, 883, 12], [967, 37, 883, 12, "_jsxDevRuntime"], [967, 51, 883, 12], [967, 52, 883, 12, "jsxDEV"], [967, 58, 883, 12], [967, 60, 883, 13, "_View"], [967, 65, 883, 13], [967, 66, 883, 13, "default"], [967, 73, 883, 17], [968, 12, 883, 18, "style"], [968, 17, 883, 23], [968, 19, 883, 25], [968, 20, 883, 26, "StyleSheet"], [968, 39, 883, 36], [968, 40, 883, 37, "absoluteFill"], [968, 52, 883, 49], [968, 54, 883, 51], [969, 14, 883, 53, "pointerEvents"], [969, 27, 883, 66], [969, 29, 883, 68], [970, 12, 883, 75], [970, 13, 883, 76], [970, 14, 883, 78], [971, 12, 883, 78, "children"], [971, 20, 883, 78], [971, 36, 885, 12], [971, 40, 885, 12, "_jsxDevRuntime"], [971, 54, 885, 12], [971, 55, 885, 12, "jsxDEV"], [971, 61, 885, 12], [971, 63, 885, 13, "_expoBlur"], [971, 72, 885, 13], [971, 73, 885, 13, "BlurView"], [971, 81, 885, 21], [972, 14, 885, 22, "intensity"], [972, 23, 885, 31], [972, 25, 885, 33], [972, 27, 885, 36], [973, 14, 885, 37, "tint"], [973, 18, 885, 41], [973, 20, 885, 42], [973, 26, 885, 48], [974, 14, 885, 49, "style"], [974, 19, 885, 54], [974, 21, 885, 56], [974, 22, 885, 57, "styles"], [974, 28, 885, 63], [974, 29, 885, 64, "blurZone"], [974, 37, 885, 72], [974, 39, 885, 74], [975, 16, 886, 14, "left"], [975, 20, 886, 18], [975, 22, 886, 20], [975, 23, 886, 21], [976, 16, 887, 14, "top"], [976, 19, 887, 17], [976, 21, 887, 19, "viewSize"], [976, 29, 887, 27], [976, 30, 887, 28, "height"], [976, 36, 887, 34], [976, 39, 887, 37], [976, 42, 887, 40], [977, 16, 888, 14, "width"], [977, 21, 888, 19], [977, 23, 888, 21, "viewSize"], [977, 31, 888, 29], [977, 32, 888, 30, "width"], [977, 37, 888, 35], [978, 16, 889, 14, "height"], [978, 22, 889, 20], [978, 24, 889, 22, "viewSize"], [978, 32, 889, 30], [978, 33, 889, 31, "height"], [978, 39, 889, 37], [978, 42, 889, 40], [978, 46, 889, 44], [979, 16, 890, 14, "borderRadius"], [979, 28, 890, 26], [979, 30, 890, 28], [980, 14, 891, 12], [980, 15, 891, 13], [981, 12, 891, 15], [982, 14, 891, 15, "fileName"], [982, 22, 891, 15], [982, 24, 891, 15, "_jsxFileName"], [982, 36, 891, 15], [983, 14, 891, 15, "lineNumber"], [983, 24, 891, 15], [984, 14, 891, 15, "columnNumber"], [984, 26, 891, 15], [985, 12, 891, 15], [985, 19, 891, 17], [985, 20, 891, 18], [985, 35, 893, 12], [985, 39, 893, 12, "_jsxDevRuntime"], [985, 53, 893, 12], [985, 54, 893, 12, "jsxDEV"], [985, 60, 893, 12], [985, 62, 893, 13, "_expoBlur"], [985, 71, 893, 13], [985, 72, 893, 13, "BlurView"], [985, 80, 893, 21], [986, 14, 893, 22, "intensity"], [986, 23, 893, 31], [986, 25, 893, 33], [986, 27, 893, 36], [987, 14, 893, 37, "tint"], [987, 18, 893, 41], [987, 20, 893, 42], [987, 26, 893, 48], [988, 14, 893, 49, "style"], [988, 19, 893, 54], [988, 21, 893, 56], [988, 22, 893, 57, "styles"], [988, 28, 893, 63], [988, 29, 893, 64, "blurZone"], [988, 37, 893, 72], [988, 39, 893, 74], [989, 16, 894, 14, "left"], [989, 20, 894, 18], [989, 22, 894, 20], [989, 23, 894, 21], [990, 16, 895, 14, "top"], [990, 19, 895, 17], [990, 21, 895, 19], [990, 22, 895, 20], [991, 16, 896, 14, "width"], [991, 21, 896, 19], [991, 23, 896, 21, "viewSize"], [991, 31, 896, 29], [991, 32, 896, 30, "width"], [991, 37, 896, 35], [992, 16, 897, 14, "height"], [992, 22, 897, 20], [992, 24, 897, 22, "viewSize"], [992, 32, 897, 30], [992, 33, 897, 31, "height"], [992, 39, 897, 37], [992, 42, 897, 40], [992, 45, 897, 43], [993, 16, 898, 14, "borderRadius"], [993, 28, 898, 26], [993, 30, 898, 28], [994, 14, 899, 12], [994, 15, 899, 13], [995, 12, 899, 15], [996, 14, 899, 15, "fileName"], [996, 22, 899, 15], [996, 24, 899, 15, "_jsxFileName"], [996, 36, 899, 15], [997, 14, 899, 15, "lineNumber"], [997, 24, 899, 15], [998, 14, 899, 15, "columnNumber"], [998, 26, 899, 15], [999, 12, 899, 15], [999, 19, 899, 17], [999, 20, 899, 18], [999, 35, 901, 12], [999, 39, 901, 12, "_jsxDevRuntime"], [999, 53, 901, 12], [999, 54, 901, 12, "jsxDEV"], [999, 60, 901, 12], [999, 62, 901, 13, "_expoBlur"], [999, 71, 901, 13], [999, 72, 901, 13, "BlurView"], [999, 80, 901, 21], [1000, 14, 901, 22, "intensity"], [1000, 23, 901, 31], [1000, 25, 901, 33], [1000, 27, 901, 36], [1001, 14, 901, 37, "tint"], [1001, 18, 901, 41], [1001, 20, 901, 42], [1001, 26, 901, 48], [1002, 14, 901, 49, "style"], [1002, 19, 901, 54], [1002, 21, 901, 56], [1002, 22, 901, 57, "styles"], [1002, 28, 901, 63], [1002, 29, 901, 64, "blurZone"], [1002, 37, 901, 72], [1002, 39, 901, 74], [1003, 16, 902, 14, "left"], [1003, 20, 902, 18], [1003, 22, 902, 20, "viewSize"], [1003, 30, 902, 28], [1003, 31, 902, 29, "width"], [1003, 36, 902, 34], [1003, 39, 902, 37], [1003, 42, 902, 40], [1003, 45, 902, 44, "viewSize"], [1003, 53, 902, 52], [1003, 54, 902, 53, "width"], [1003, 59, 902, 58], [1003, 62, 902, 61], [1003, 66, 902, 66], [1004, 16, 903, 14, "top"], [1004, 19, 903, 17], [1004, 21, 903, 19, "viewSize"], [1004, 29, 903, 27], [1004, 30, 903, 28, "height"], [1004, 36, 903, 34], [1004, 39, 903, 37], [1004, 43, 903, 41], [1004, 46, 903, 45, "viewSize"], [1004, 54, 903, 53], [1004, 55, 903, 54, "width"], [1004, 60, 903, 59], [1004, 63, 903, 62], [1004, 67, 903, 67], [1005, 16, 904, 14, "width"], [1005, 21, 904, 19], [1005, 23, 904, 21, "viewSize"], [1005, 31, 904, 29], [1005, 32, 904, 30, "width"], [1005, 37, 904, 35], [1005, 40, 904, 38], [1005, 43, 904, 41], [1006, 16, 905, 14, "height"], [1006, 22, 905, 20], [1006, 24, 905, 22, "viewSize"], [1006, 32, 905, 30], [1006, 33, 905, 31, "width"], [1006, 38, 905, 36], [1006, 41, 905, 39], [1006, 44, 905, 42], [1007, 16, 906, 14, "borderRadius"], [1007, 28, 906, 26], [1007, 30, 906, 29, "viewSize"], [1007, 38, 906, 37], [1007, 39, 906, 38, "width"], [1007, 44, 906, 43], [1007, 47, 906, 46], [1007, 50, 906, 49], [1007, 53, 906, 53], [1008, 14, 907, 12], [1008, 15, 907, 13], [1009, 12, 907, 15], [1010, 14, 907, 15, "fileName"], [1010, 22, 907, 15], [1010, 24, 907, 15, "_jsxFileName"], [1010, 36, 907, 15], [1011, 14, 907, 15, "lineNumber"], [1011, 24, 907, 15], [1012, 14, 907, 15, "columnNumber"], [1012, 26, 907, 15], [1013, 12, 907, 15], [1013, 19, 907, 17], [1013, 20, 907, 18], [1013, 35, 908, 12], [1013, 39, 908, 12, "_jsxDevRuntime"], [1013, 53, 908, 12], [1013, 54, 908, 12, "jsxDEV"], [1013, 60, 908, 12], [1013, 62, 908, 13, "_expoBlur"], [1013, 71, 908, 13], [1013, 72, 908, 13, "BlurView"], [1013, 80, 908, 21], [1014, 14, 908, 22, "intensity"], [1014, 23, 908, 31], [1014, 25, 908, 33], [1014, 27, 908, 36], [1015, 14, 908, 37, "tint"], [1015, 18, 908, 41], [1015, 20, 908, 42], [1015, 26, 908, 48], [1016, 14, 908, 49, "style"], [1016, 19, 908, 54], [1016, 21, 908, 56], [1016, 22, 908, 57, "styles"], [1016, 28, 908, 63], [1016, 29, 908, 64, "blurZone"], [1016, 37, 908, 72], [1016, 39, 908, 74], [1017, 16, 909, 14, "left"], [1017, 20, 909, 18], [1017, 22, 909, 20, "viewSize"], [1017, 30, 909, 28], [1017, 31, 909, 29, "width"], [1017, 36, 909, 34], [1017, 39, 909, 37], [1017, 42, 909, 40], [1017, 45, 909, 44, "viewSize"], [1017, 53, 909, 52], [1017, 54, 909, 53, "width"], [1017, 59, 909, 58], [1017, 62, 909, 61], [1017, 66, 909, 66], [1018, 16, 910, 14, "top"], [1018, 19, 910, 17], [1018, 21, 910, 19, "viewSize"], [1018, 29, 910, 27], [1018, 30, 910, 28, "height"], [1018, 36, 910, 34], [1018, 39, 910, 37], [1018, 42, 910, 40], [1018, 45, 910, 44, "viewSize"], [1018, 53, 910, 52], [1018, 54, 910, 53, "width"], [1018, 59, 910, 58], [1018, 62, 910, 61], [1018, 66, 910, 66], [1019, 16, 911, 14, "width"], [1019, 21, 911, 19], [1019, 23, 911, 21, "viewSize"], [1019, 31, 911, 29], [1019, 32, 911, 30, "width"], [1019, 37, 911, 35], [1019, 40, 911, 38], [1019, 43, 911, 41], [1020, 16, 912, 14, "height"], [1020, 22, 912, 20], [1020, 24, 912, 22, "viewSize"], [1020, 32, 912, 30], [1020, 33, 912, 31, "width"], [1020, 38, 912, 36], [1020, 41, 912, 39], [1020, 44, 912, 42], [1021, 16, 913, 14, "borderRadius"], [1021, 28, 913, 26], [1021, 30, 913, 29, "viewSize"], [1021, 38, 913, 37], [1021, 39, 913, 38, "width"], [1021, 44, 913, 43], [1021, 47, 913, 46], [1021, 50, 913, 49], [1021, 53, 913, 53], [1022, 14, 914, 12], [1022, 15, 914, 13], [1023, 12, 914, 15], [1024, 14, 914, 15, "fileName"], [1024, 22, 914, 15], [1024, 24, 914, 15, "_jsxFileName"], [1024, 36, 914, 15], [1025, 14, 914, 15, "lineNumber"], [1025, 24, 914, 15], [1026, 14, 914, 15, "columnNumber"], [1026, 26, 914, 15], [1027, 12, 914, 15], [1027, 19, 914, 17], [1027, 20, 914, 18], [1027, 35, 915, 12], [1027, 39, 915, 12, "_jsxDevRuntime"], [1027, 53, 915, 12], [1027, 54, 915, 12, "jsxDEV"], [1027, 60, 915, 12], [1027, 62, 915, 13, "_expoBlur"], [1027, 71, 915, 13], [1027, 72, 915, 13, "BlurView"], [1027, 80, 915, 21], [1028, 14, 915, 22, "intensity"], [1028, 23, 915, 31], [1028, 25, 915, 33], [1028, 27, 915, 36], [1029, 14, 915, 37, "tint"], [1029, 18, 915, 41], [1029, 20, 915, 42], [1029, 26, 915, 48], [1030, 14, 915, 49, "style"], [1030, 19, 915, 54], [1030, 21, 915, 56], [1030, 22, 915, 57, "styles"], [1030, 28, 915, 63], [1030, 29, 915, 64, "blurZone"], [1030, 37, 915, 72], [1030, 39, 915, 74], [1031, 16, 916, 14, "left"], [1031, 20, 916, 18], [1031, 22, 916, 20, "viewSize"], [1031, 30, 916, 28], [1031, 31, 916, 29, "width"], [1031, 36, 916, 34], [1031, 39, 916, 37], [1031, 42, 916, 40], [1031, 45, 916, 44, "viewSize"], [1031, 53, 916, 52], [1031, 54, 916, 53, "width"], [1031, 59, 916, 58], [1031, 62, 916, 61], [1031, 66, 916, 66], [1032, 16, 917, 14, "top"], [1032, 19, 917, 17], [1032, 21, 917, 19, "viewSize"], [1032, 29, 917, 27], [1032, 30, 917, 28, "height"], [1032, 36, 917, 34], [1032, 39, 917, 37], [1032, 42, 917, 40], [1032, 45, 917, 44, "viewSize"], [1032, 53, 917, 52], [1032, 54, 917, 53, "width"], [1032, 59, 917, 58], [1032, 62, 917, 61], [1032, 66, 917, 66], [1033, 16, 918, 14, "width"], [1033, 21, 918, 19], [1033, 23, 918, 21, "viewSize"], [1033, 31, 918, 29], [1033, 32, 918, 30, "width"], [1033, 37, 918, 35], [1033, 40, 918, 38], [1033, 43, 918, 41], [1034, 16, 919, 14, "height"], [1034, 22, 919, 20], [1034, 24, 919, 22, "viewSize"], [1034, 32, 919, 30], [1034, 33, 919, 31, "width"], [1034, 38, 919, 36], [1034, 41, 919, 39], [1034, 44, 919, 42], [1035, 16, 920, 14, "borderRadius"], [1035, 28, 920, 26], [1035, 30, 920, 29, "viewSize"], [1035, 38, 920, 37], [1035, 39, 920, 38, "width"], [1035, 44, 920, 43], [1035, 47, 920, 46], [1035, 50, 920, 49], [1035, 53, 920, 53], [1036, 14, 921, 12], [1036, 15, 921, 13], [1037, 12, 921, 15], [1038, 14, 921, 15, "fileName"], [1038, 22, 921, 15], [1038, 24, 921, 15, "_jsxFileName"], [1038, 36, 921, 15], [1039, 14, 921, 15, "lineNumber"], [1039, 24, 921, 15], [1040, 14, 921, 15, "columnNumber"], [1040, 26, 921, 15], [1041, 12, 921, 15], [1041, 19, 921, 17], [1041, 20, 921, 18], [1041, 22, 923, 13, "__DEV__"], [1041, 29, 923, 20], [1041, 46, 924, 14], [1041, 50, 924, 14, "_jsxDevRuntime"], [1041, 64, 924, 14], [1041, 65, 924, 14, "jsxDEV"], [1041, 71, 924, 14], [1041, 73, 924, 15, "_View"], [1041, 78, 924, 15], [1041, 79, 924, 15, "default"], [1041, 86, 924, 19], [1042, 14, 924, 20, "style"], [1042, 19, 924, 25], [1042, 21, 924, 27, "styles"], [1042, 27, 924, 33], [1042, 28, 924, 34, "previewChip"], [1042, 39, 924, 46], [1043, 14, 924, 46, "children"], [1043, 22, 924, 46], [1043, 37, 925, 16], [1043, 41, 925, 16, "_jsxDevRuntime"], [1043, 55, 925, 16], [1043, 56, 925, 16, "jsxDEV"], [1043, 62, 925, 16], [1043, 64, 925, 17, "_Text"], [1043, 69, 925, 17], [1043, 70, 925, 17, "default"], [1043, 77, 925, 21], [1044, 16, 925, 22, "style"], [1044, 21, 925, 27], [1044, 23, 925, 29, "styles"], [1044, 29, 925, 35], [1044, 30, 925, 36, "previewChipText"], [1044, 45, 925, 52], [1045, 16, 925, 52, "children"], [1045, 24, 925, 52], [1045, 26, 925, 53], [1046, 14, 925, 73], [1047, 16, 925, 73, "fileName"], [1047, 24, 925, 73], [1047, 26, 925, 73, "_jsxFileName"], [1047, 38, 925, 73], [1048, 16, 925, 73, "lineNumber"], [1048, 26, 925, 73], [1049, 16, 925, 73, "columnNumber"], [1049, 28, 925, 73], [1050, 14, 925, 73], [1050, 21, 925, 79], [1051, 12, 925, 80], [1052, 14, 925, 80, "fileName"], [1052, 22, 925, 80], [1052, 24, 925, 80, "_jsxFileName"], [1052, 36, 925, 80], [1053, 14, 925, 80, "lineNumber"], [1053, 24, 925, 80], [1054, 14, 925, 80, "columnNumber"], [1054, 26, 925, 80], [1055, 12, 925, 80], [1055, 19, 926, 20], [1055, 20, 927, 13], [1056, 10, 927, 13], [1057, 12, 927, 13, "fileName"], [1057, 20, 927, 13], [1057, 22, 927, 13, "_jsxFileName"], [1057, 34, 927, 13], [1058, 12, 927, 13, "lineNumber"], [1058, 22, 927, 13], [1059, 12, 927, 13, "columnNumber"], [1059, 24, 927, 13], [1060, 10, 927, 13], [1060, 17, 928, 18], [1060, 18, 928, 19], [1061, 8, 928, 19], [1061, 23, 929, 12], [1061, 24, 930, 9], [1061, 26, 932, 9, "isCameraReady"], [1061, 39, 932, 22], [1061, 56, 933, 10], [1061, 60, 933, 10, "_jsxDevRuntime"], [1061, 74, 933, 10], [1061, 75, 933, 10, "jsxDEV"], [1061, 81, 933, 10], [1061, 83, 933, 10, "_jsxDevRuntime"], [1061, 97, 933, 10], [1061, 98, 933, 10, "Fragment"], [1061, 106, 933, 10], [1062, 10, 933, 10, "children"], [1062, 18, 933, 10], [1062, 34, 935, 12], [1062, 38, 935, 12, "_jsxDevRuntime"], [1062, 52, 935, 12], [1062, 53, 935, 12, "jsxDEV"], [1062, 59, 935, 12], [1062, 61, 935, 13, "_View"], [1062, 66, 935, 13], [1062, 67, 935, 13, "default"], [1062, 74, 935, 17], [1063, 12, 935, 18, "style"], [1063, 17, 935, 23], [1063, 19, 935, 25, "styles"], [1063, 25, 935, 31], [1063, 26, 935, 32, "headerOverlay"], [1063, 39, 935, 46], [1064, 12, 935, 46, "children"], [1064, 20, 935, 46], [1064, 35, 936, 14], [1064, 39, 936, 14, "_jsxDevRuntime"], [1064, 53, 936, 14], [1064, 54, 936, 14, "jsxDEV"], [1064, 60, 936, 14], [1064, 62, 936, 15, "_View"], [1064, 67, 936, 15], [1064, 68, 936, 15, "default"], [1064, 75, 936, 19], [1065, 14, 936, 20, "style"], [1065, 19, 936, 25], [1065, 21, 936, 27, "styles"], [1065, 27, 936, 33], [1065, 28, 936, 34, "headerContent"], [1065, 41, 936, 48], [1066, 14, 936, 48, "children"], [1066, 22, 936, 48], [1066, 38, 937, 16], [1066, 42, 937, 16, "_jsxDevRuntime"], [1066, 56, 937, 16], [1066, 57, 937, 16, "jsxDEV"], [1066, 63, 937, 16], [1066, 65, 937, 17, "_View"], [1066, 70, 937, 17], [1066, 71, 937, 17, "default"], [1066, 78, 937, 21], [1067, 16, 937, 22, "style"], [1067, 21, 937, 27], [1067, 23, 937, 29, "styles"], [1067, 29, 937, 35], [1067, 30, 937, 36, "headerLeft"], [1067, 40, 937, 47], [1068, 16, 937, 47, "children"], [1068, 24, 937, 47], [1068, 40, 938, 18], [1068, 44, 938, 18, "_jsxDevRuntime"], [1068, 58, 938, 18], [1068, 59, 938, 18, "jsxDEV"], [1068, 65, 938, 18], [1068, 67, 938, 19, "_Text"], [1068, 72, 938, 19], [1068, 73, 938, 19, "default"], [1068, 80, 938, 23], [1069, 18, 938, 24, "style"], [1069, 23, 938, 29], [1069, 25, 938, 31, "styles"], [1069, 31, 938, 37], [1069, 32, 938, 38, "headerTitle"], [1069, 43, 938, 50], [1070, 18, 938, 50, "children"], [1070, 26, 938, 50], [1070, 28, 938, 51], [1071, 16, 938, 62], [1072, 18, 938, 62, "fileName"], [1072, 26, 938, 62], [1072, 28, 938, 62, "_jsxFileName"], [1072, 40, 938, 62], [1073, 18, 938, 62, "lineNumber"], [1073, 28, 938, 62], [1074, 18, 938, 62, "columnNumber"], [1074, 30, 938, 62], [1075, 16, 938, 62], [1075, 23, 938, 68], [1075, 24, 938, 69], [1075, 39, 939, 18], [1075, 43, 939, 18, "_jsxDevRuntime"], [1075, 57, 939, 18], [1075, 58, 939, 18, "jsxDEV"], [1075, 64, 939, 18], [1075, 66, 939, 19, "_View"], [1075, 71, 939, 19], [1075, 72, 939, 19, "default"], [1075, 79, 939, 23], [1076, 18, 939, 24, "style"], [1076, 23, 939, 29], [1076, 25, 939, 31, "styles"], [1076, 31, 939, 37], [1076, 32, 939, 38, "subtitleRow"], [1076, 43, 939, 50], [1077, 18, 939, 50, "children"], [1077, 26, 939, 50], [1077, 42, 940, 20], [1077, 46, 940, 20, "_jsxDevRuntime"], [1077, 60, 940, 20], [1077, 61, 940, 20, "jsxDEV"], [1077, 67, 940, 20], [1077, 69, 940, 21, "_Text"], [1077, 74, 940, 21], [1077, 75, 940, 21, "default"], [1077, 82, 940, 25], [1078, 20, 940, 26, "style"], [1078, 25, 940, 31], [1078, 27, 940, 33, "styles"], [1078, 33, 940, 39], [1078, 34, 940, 40, "webIcon"], [1078, 41, 940, 48], [1079, 20, 940, 48, "children"], [1079, 28, 940, 48], [1079, 30, 940, 49], [1080, 18, 940, 51], [1081, 20, 940, 51, "fileName"], [1081, 28, 940, 51], [1081, 30, 940, 51, "_jsxFileName"], [1081, 42, 940, 51], [1082, 20, 940, 51, "lineNumber"], [1082, 30, 940, 51], [1083, 20, 940, 51, "columnNumber"], [1083, 32, 940, 51], [1084, 18, 940, 51], [1084, 25, 940, 57], [1084, 26, 940, 58], [1084, 41, 941, 20], [1084, 45, 941, 20, "_jsxDevRuntime"], [1084, 59, 941, 20], [1084, 60, 941, 20, "jsxDEV"], [1084, 66, 941, 20], [1084, 68, 941, 21, "_Text"], [1084, 73, 941, 21], [1084, 74, 941, 21, "default"], [1084, 81, 941, 25], [1085, 20, 941, 26, "style"], [1085, 25, 941, 31], [1085, 27, 941, 33, "styles"], [1085, 33, 941, 39], [1085, 34, 941, 40, "headerSubtitle"], [1085, 48, 941, 55], [1086, 20, 941, 55, "children"], [1086, 28, 941, 55], [1086, 30, 941, 56], [1087, 18, 941, 71], [1088, 20, 941, 71, "fileName"], [1088, 28, 941, 71], [1088, 30, 941, 71, "_jsxFileName"], [1088, 42, 941, 71], [1089, 20, 941, 71, "lineNumber"], [1089, 30, 941, 71], [1090, 20, 941, 71, "columnNumber"], [1090, 32, 941, 71], [1091, 18, 941, 71], [1091, 25, 941, 77], [1091, 26, 941, 78], [1092, 16, 941, 78], [1093, 18, 941, 78, "fileName"], [1093, 26, 941, 78], [1093, 28, 941, 78, "_jsxFileName"], [1093, 40, 941, 78], [1094, 18, 941, 78, "lineNumber"], [1094, 28, 941, 78], [1095, 18, 941, 78, "columnNumber"], [1095, 30, 941, 78], [1096, 16, 941, 78], [1096, 23, 942, 24], [1096, 24, 942, 25], [1096, 26, 943, 19, "challengeCode"], [1096, 39, 943, 32], [1096, 56, 944, 20], [1096, 60, 944, 20, "_jsxDevRuntime"], [1096, 74, 944, 20], [1096, 75, 944, 20, "jsxDEV"], [1096, 81, 944, 20], [1096, 83, 944, 21, "_View"], [1096, 88, 944, 21], [1096, 89, 944, 21, "default"], [1096, 96, 944, 25], [1097, 18, 944, 26, "style"], [1097, 23, 944, 31], [1097, 25, 944, 33, "styles"], [1097, 31, 944, 39], [1097, 32, 944, 40, "challengeRow"], [1097, 44, 944, 53], [1098, 18, 944, 53, "children"], [1098, 26, 944, 53], [1098, 42, 945, 22], [1098, 46, 945, 22, "_jsxDevRuntime"], [1098, 60, 945, 22], [1098, 61, 945, 22, "jsxDEV"], [1098, 67, 945, 22], [1098, 69, 945, 23, "_lucideReactNative"], [1098, 87, 945, 23], [1098, 88, 945, 23, "Shield"], [1098, 94, 945, 29], [1099, 20, 945, 30, "size"], [1099, 24, 945, 34], [1099, 26, 945, 36], [1099, 28, 945, 39], [1100, 20, 945, 40, "color"], [1100, 25, 945, 45], [1100, 27, 945, 46], [1101, 18, 945, 52], [1102, 20, 945, 52, "fileName"], [1102, 28, 945, 52], [1102, 30, 945, 52, "_jsxFileName"], [1102, 42, 945, 52], [1103, 20, 945, 52, "lineNumber"], [1103, 30, 945, 52], [1104, 20, 945, 52, "columnNumber"], [1104, 32, 945, 52], [1105, 18, 945, 52], [1105, 25, 945, 54], [1105, 26, 945, 55], [1105, 41, 946, 22], [1105, 45, 946, 22, "_jsxDevRuntime"], [1105, 59, 946, 22], [1105, 60, 946, 22, "jsxDEV"], [1105, 66, 946, 22], [1105, 68, 946, 23, "_Text"], [1105, 73, 946, 23], [1105, 74, 946, 23, "default"], [1105, 81, 946, 27], [1106, 20, 946, 28, "style"], [1106, 25, 946, 33], [1106, 27, 946, 35, "styles"], [1106, 33, 946, 41], [1106, 34, 946, 42, "challengeCode"], [1106, 47, 946, 56], [1107, 20, 946, 56, "children"], [1107, 28, 946, 56], [1107, 30, 946, 58, "challengeCode"], [1108, 18, 946, 71], [1109, 20, 946, 71, "fileName"], [1109, 28, 946, 71], [1109, 30, 946, 71, "_jsxFileName"], [1109, 42, 946, 71], [1110, 20, 946, 71, "lineNumber"], [1110, 30, 946, 71], [1111, 20, 946, 71, "columnNumber"], [1111, 32, 946, 71], [1112, 18, 946, 71], [1112, 25, 946, 78], [1112, 26, 946, 79], [1113, 16, 946, 79], [1114, 18, 946, 79, "fileName"], [1114, 26, 946, 79], [1114, 28, 946, 79, "_jsxFileName"], [1114, 40, 946, 79], [1115, 18, 946, 79, "lineNumber"], [1115, 28, 946, 79], [1116, 18, 946, 79, "columnNumber"], [1116, 30, 946, 79], [1117, 16, 946, 79], [1117, 23, 947, 26], [1117, 24, 948, 19], [1118, 14, 948, 19], [1119, 16, 948, 19, "fileName"], [1119, 24, 948, 19], [1119, 26, 948, 19, "_jsxFileName"], [1119, 38, 948, 19], [1120, 16, 948, 19, "lineNumber"], [1120, 26, 948, 19], [1121, 16, 948, 19, "columnNumber"], [1121, 28, 948, 19], [1122, 14, 948, 19], [1122, 21, 949, 22], [1122, 22, 949, 23], [1122, 37, 950, 16], [1122, 41, 950, 16, "_jsxDevRuntime"], [1122, 55, 950, 16], [1122, 56, 950, 16, "jsxDEV"], [1122, 62, 950, 16], [1122, 64, 950, 17, "_TouchableOpacity"], [1122, 81, 950, 17], [1122, 82, 950, 17, "default"], [1122, 89, 950, 33], [1123, 16, 950, 34, "onPress"], [1123, 23, 950, 41], [1123, 25, 950, 43, "onCancel"], [1123, 33, 950, 52], [1124, 16, 950, 53, "style"], [1124, 21, 950, 58], [1124, 23, 950, 60, "styles"], [1124, 29, 950, 66], [1124, 30, 950, 67, "closeButton"], [1124, 41, 950, 79], [1125, 16, 950, 79, "children"], [1125, 24, 950, 79], [1125, 39, 951, 18], [1125, 43, 951, 18, "_jsxDevRuntime"], [1125, 57, 951, 18], [1125, 58, 951, 18, "jsxDEV"], [1125, 64, 951, 18], [1125, 66, 951, 19, "_lucideReactNative"], [1125, 84, 951, 19], [1125, 85, 951, 19, "X"], [1125, 86, 951, 20], [1126, 18, 951, 21, "size"], [1126, 22, 951, 25], [1126, 24, 951, 27], [1126, 26, 951, 30], [1127, 18, 951, 31, "color"], [1127, 23, 951, 36], [1127, 25, 951, 37], [1128, 16, 951, 43], [1129, 18, 951, 43, "fileName"], [1129, 26, 951, 43], [1129, 28, 951, 43, "_jsxFileName"], [1129, 40, 951, 43], [1130, 18, 951, 43, "lineNumber"], [1130, 28, 951, 43], [1131, 18, 951, 43, "columnNumber"], [1131, 30, 951, 43], [1132, 16, 951, 43], [1132, 23, 951, 45], [1133, 14, 951, 46], [1134, 16, 951, 46, "fileName"], [1134, 24, 951, 46], [1134, 26, 951, 46, "_jsxFileName"], [1134, 38, 951, 46], [1135, 16, 951, 46, "lineNumber"], [1135, 26, 951, 46], [1136, 16, 951, 46, "columnNumber"], [1136, 28, 951, 46], [1137, 14, 951, 46], [1137, 21, 952, 34], [1137, 22, 952, 35], [1138, 12, 952, 35], [1139, 14, 952, 35, "fileName"], [1139, 22, 952, 35], [1139, 24, 952, 35, "_jsxFileName"], [1139, 36, 952, 35], [1140, 14, 952, 35, "lineNumber"], [1140, 24, 952, 35], [1141, 14, 952, 35, "columnNumber"], [1141, 26, 952, 35], [1142, 12, 952, 35], [1142, 19, 953, 20], [1143, 10, 953, 21], [1144, 12, 953, 21, "fileName"], [1144, 20, 953, 21], [1144, 22, 953, 21, "_jsxFileName"], [1144, 34, 953, 21], [1145, 12, 953, 21, "lineNumber"], [1145, 22, 953, 21], [1146, 12, 953, 21, "columnNumber"], [1146, 24, 953, 21], [1147, 10, 953, 21], [1147, 17, 954, 18], [1147, 18, 954, 19], [1147, 33, 956, 12], [1147, 37, 956, 12, "_jsxDevRuntime"], [1147, 51, 956, 12], [1147, 52, 956, 12, "jsxDEV"], [1147, 58, 956, 12], [1147, 60, 956, 13, "_View"], [1147, 65, 956, 13], [1147, 66, 956, 13, "default"], [1147, 73, 956, 17], [1148, 12, 956, 18, "style"], [1148, 17, 956, 23], [1148, 19, 956, 25, "styles"], [1148, 25, 956, 31], [1148, 26, 956, 32, "privacyNotice"], [1148, 39, 956, 46], [1149, 12, 956, 46, "children"], [1149, 20, 956, 46], [1149, 36, 957, 14], [1149, 40, 957, 14, "_jsxDevRuntime"], [1149, 54, 957, 14], [1149, 55, 957, 14, "jsxDEV"], [1149, 61, 957, 14], [1149, 63, 957, 15, "_lucideReactNative"], [1149, 81, 957, 15], [1149, 82, 957, 15, "Shield"], [1149, 88, 957, 21], [1150, 14, 957, 22, "size"], [1150, 18, 957, 26], [1150, 20, 957, 28], [1150, 22, 957, 31], [1151, 14, 957, 32, "color"], [1151, 19, 957, 37], [1151, 21, 957, 38], [1152, 12, 957, 47], [1153, 14, 957, 47, "fileName"], [1153, 22, 957, 47], [1153, 24, 957, 47, "_jsxFileName"], [1153, 36, 957, 47], [1154, 14, 957, 47, "lineNumber"], [1154, 24, 957, 47], [1155, 14, 957, 47, "columnNumber"], [1155, 26, 957, 47], [1156, 12, 957, 47], [1156, 19, 957, 49], [1156, 20, 957, 50], [1156, 35, 958, 14], [1156, 39, 958, 14, "_jsxDevRuntime"], [1156, 53, 958, 14], [1156, 54, 958, 14, "jsxDEV"], [1156, 60, 958, 14], [1156, 62, 958, 15, "_Text"], [1156, 67, 958, 15], [1156, 68, 958, 15, "default"], [1156, 75, 958, 19], [1157, 14, 958, 20, "style"], [1157, 19, 958, 25], [1157, 21, 958, 27, "styles"], [1157, 27, 958, 33], [1157, 28, 958, 34, "privacyText"], [1157, 39, 958, 46], [1158, 14, 958, 46, "children"], [1158, 22, 958, 46], [1158, 24, 958, 47], [1159, 12, 960, 14], [1160, 14, 960, 14, "fileName"], [1160, 22, 960, 14], [1160, 24, 960, 14, "_jsxFileName"], [1160, 36, 960, 14], [1161, 14, 960, 14, "lineNumber"], [1161, 24, 960, 14], [1162, 14, 960, 14, "columnNumber"], [1162, 26, 960, 14], [1163, 12, 960, 14], [1163, 19, 960, 20], [1163, 20, 960, 21], [1164, 10, 960, 21], [1165, 12, 960, 21, "fileName"], [1165, 20, 960, 21], [1165, 22, 960, 21, "_jsxFileName"], [1165, 34, 960, 21], [1166, 12, 960, 21, "lineNumber"], [1166, 22, 960, 21], [1167, 12, 960, 21, "columnNumber"], [1167, 24, 960, 21], [1168, 10, 960, 21], [1168, 17, 961, 18], [1168, 18, 961, 19], [1168, 33, 963, 12], [1168, 37, 963, 12, "_jsxDevRuntime"], [1168, 51, 963, 12], [1168, 52, 963, 12, "jsxDEV"], [1168, 58, 963, 12], [1168, 60, 963, 13, "_View"], [1168, 65, 963, 13], [1168, 66, 963, 13, "default"], [1168, 73, 963, 17], [1169, 12, 963, 18, "style"], [1169, 17, 963, 23], [1169, 19, 963, 25, "styles"], [1169, 25, 963, 31], [1169, 26, 963, 32, "footer<PERSON><PERSON><PERSON>"], [1169, 39, 963, 46], [1170, 12, 963, 46, "children"], [1170, 20, 963, 46], [1170, 36, 964, 14], [1170, 40, 964, 14, "_jsxDevRuntime"], [1170, 54, 964, 14], [1170, 55, 964, 14, "jsxDEV"], [1170, 61, 964, 14], [1170, 63, 964, 15, "_Text"], [1170, 68, 964, 15], [1170, 69, 964, 15, "default"], [1170, 76, 964, 19], [1171, 14, 964, 20, "style"], [1171, 19, 964, 25], [1171, 21, 964, 27, "styles"], [1171, 27, 964, 33], [1171, 28, 964, 34, "instruction"], [1171, 39, 964, 46], [1172, 14, 964, 46, "children"], [1172, 22, 964, 46], [1172, 24, 964, 47], [1173, 12, 966, 14], [1174, 14, 966, 14, "fileName"], [1174, 22, 966, 14], [1174, 24, 966, 14, "_jsxFileName"], [1174, 36, 966, 14], [1175, 14, 966, 14, "lineNumber"], [1175, 24, 966, 14], [1176, 14, 966, 14, "columnNumber"], [1176, 26, 966, 14], [1177, 12, 966, 14], [1177, 19, 966, 20], [1177, 20, 966, 21], [1177, 35, 968, 14], [1177, 39, 968, 14, "_jsxDevRuntime"], [1177, 53, 968, 14], [1177, 54, 968, 14, "jsxDEV"], [1177, 60, 968, 14], [1177, 62, 968, 15, "_TouchableOpacity"], [1177, 79, 968, 15], [1177, 80, 968, 15, "default"], [1177, 87, 968, 31], [1178, 14, 969, 16, "onPress"], [1178, 21, 969, 23], [1178, 23, 969, 25, "capturePhoto"], [1178, 35, 969, 38], [1179, 14, 970, 16, "disabled"], [1179, 22, 970, 24], [1179, 24, 970, 26, "processingState"], [1179, 39, 970, 41], [1179, 44, 970, 46], [1179, 50, 970, 52], [1179, 54, 970, 56], [1179, 55, 970, 57, "isCameraReady"], [1179, 68, 970, 71], [1180, 14, 971, 16, "style"], [1180, 19, 971, 21], [1180, 21, 971, 23], [1180, 22, 972, 18, "styles"], [1180, 28, 972, 24], [1180, 29, 972, 25, "shutterButton"], [1180, 42, 972, 38], [1180, 44, 973, 18, "processingState"], [1180, 59, 973, 33], [1180, 64, 973, 38], [1180, 70, 973, 44], [1180, 74, 973, 48, "styles"], [1180, 80, 973, 54], [1180, 81, 973, 55, "shutterButtonDisabled"], [1180, 102, 973, 76], [1180, 103, 974, 18], [1181, 14, 974, 18, "children"], [1181, 22, 974, 18], [1181, 24, 976, 17, "processingState"], [1181, 39, 976, 32], [1181, 44, 976, 37], [1181, 50, 976, 43], [1181, 66, 977, 18], [1181, 70, 977, 18, "_jsxDevRuntime"], [1181, 84, 977, 18], [1181, 85, 977, 18, "jsxDEV"], [1181, 91, 977, 18], [1181, 93, 977, 19, "_View"], [1181, 98, 977, 19], [1181, 99, 977, 19, "default"], [1181, 106, 977, 23], [1182, 16, 977, 24, "style"], [1182, 21, 977, 29], [1182, 23, 977, 31, "styles"], [1182, 29, 977, 37], [1182, 30, 977, 38, "shutterInner"], [1183, 14, 977, 51], [1184, 16, 977, 51, "fileName"], [1184, 24, 977, 51], [1184, 26, 977, 51, "_jsxFileName"], [1184, 38, 977, 51], [1185, 16, 977, 51, "lineNumber"], [1185, 26, 977, 51], [1186, 16, 977, 51, "columnNumber"], [1186, 28, 977, 51], [1187, 14, 977, 51], [1187, 21, 977, 53], [1187, 22, 977, 54], [1187, 38, 979, 18], [1187, 42, 979, 18, "_jsxDevRuntime"], [1187, 56, 979, 18], [1187, 57, 979, 18, "jsxDEV"], [1187, 63, 979, 18], [1187, 65, 979, 19, "_ActivityIndicator"], [1187, 83, 979, 19], [1187, 84, 979, 19, "default"], [1187, 91, 979, 36], [1188, 16, 979, 37, "size"], [1188, 20, 979, 41], [1188, 22, 979, 42], [1188, 29, 979, 49], [1189, 16, 979, 50, "color"], [1189, 21, 979, 55], [1189, 23, 979, 56], [1190, 14, 979, 65], [1191, 16, 979, 65, "fileName"], [1191, 24, 979, 65], [1191, 26, 979, 65, "_jsxFileName"], [1191, 38, 979, 65], [1192, 16, 979, 65, "lineNumber"], [1192, 26, 979, 65], [1193, 16, 979, 65, "columnNumber"], [1193, 28, 979, 65], [1194, 14, 979, 65], [1194, 21, 979, 67], [1195, 12, 980, 17], [1196, 14, 980, 17, "fileName"], [1196, 22, 980, 17], [1196, 24, 980, 17, "_jsxFileName"], [1196, 36, 980, 17], [1197, 14, 980, 17, "lineNumber"], [1197, 24, 980, 17], [1198, 14, 980, 17, "columnNumber"], [1198, 26, 980, 17], [1199, 12, 980, 17], [1199, 19, 981, 32], [1199, 20, 981, 33], [1199, 35, 982, 14], [1199, 39, 982, 14, "_jsxDevRuntime"], [1199, 53, 982, 14], [1199, 54, 982, 14, "jsxDEV"], [1199, 60, 982, 14], [1199, 62, 982, 15, "_Text"], [1199, 67, 982, 15], [1199, 68, 982, 15, "default"], [1199, 75, 982, 19], [1200, 14, 982, 20, "style"], [1200, 19, 982, 25], [1200, 21, 982, 27, "styles"], [1200, 27, 982, 33], [1200, 28, 982, 34, "privacyNote"], [1200, 39, 982, 46], [1201, 14, 982, 46, "children"], [1201, 22, 982, 46], [1201, 24, 982, 47], [1202, 12, 984, 14], [1203, 14, 984, 14, "fileName"], [1203, 22, 984, 14], [1203, 24, 984, 14, "_jsxFileName"], [1203, 36, 984, 14], [1204, 14, 984, 14, "lineNumber"], [1204, 24, 984, 14], [1205, 14, 984, 14, "columnNumber"], [1205, 26, 984, 14], [1206, 12, 984, 14], [1206, 19, 984, 20], [1206, 20, 984, 21], [1207, 10, 984, 21], [1208, 12, 984, 21, "fileName"], [1208, 20, 984, 21], [1208, 22, 984, 21, "_jsxFileName"], [1208, 34, 984, 21], [1209, 12, 984, 21, "lineNumber"], [1209, 22, 984, 21], [1210, 12, 984, 21, "columnNumber"], [1210, 24, 984, 21], [1211, 10, 984, 21], [1211, 17, 985, 18], [1211, 18, 985, 19], [1212, 8, 985, 19], [1212, 23, 986, 12], [1212, 24, 987, 9], [1213, 6, 987, 9], [1214, 8, 987, 9, "fileName"], [1214, 16, 987, 9], [1214, 18, 987, 9, "_jsxFileName"], [1214, 30, 987, 9], [1215, 8, 987, 9, "lineNumber"], [1215, 18, 987, 9], [1216, 8, 987, 9, "columnNumber"], [1216, 20, 987, 9], [1217, 6, 987, 9], [1217, 13, 988, 12], [1217, 14, 988, 13], [1217, 29, 990, 6], [1217, 33, 990, 6, "_jsxDevRuntime"], [1217, 47, 990, 6], [1217, 48, 990, 6, "jsxDEV"], [1217, 54, 990, 6], [1217, 56, 990, 7, "_Modal"], [1217, 62, 990, 7], [1217, 63, 990, 7, "default"], [1217, 70, 990, 12], [1218, 8, 991, 8, "visible"], [1218, 15, 991, 15], [1218, 17, 991, 17, "processingState"], [1218, 32, 991, 32], [1218, 37, 991, 37], [1218, 43, 991, 43], [1218, 47, 991, 47, "processingState"], [1218, 62, 991, 62], [1218, 67, 991, 67], [1218, 74, 991, 75], [1219, 8, 992, 8, "transparent"], [1219, 19, 992, 19], [1220, 8, 993, 8, "animationType"], [1220, 21, 993, 21], [1220, 23, 993, 22], [1220, 29, 993, 28], [1221, 8, 993, 28, "children"], [1221, 16, 993, 28], [1221, 31, 995, 8], [1221, 35, 995, 8, "_jsxDevRuntime"], [1221, 49, 995, 8], [1221, 50, 995, 8, "jsxDEV"], [1221, 56, 995, 8], [1221, 58, 995, 9, "_View"], [1221, 63, 995, 9], [1221, 64, 995, 9, "default"], [1221, 71, 995, 13], [1222, 10, 995, 14, "style"], [1222, 15, 995, 19], [1222, 17, 995, 21, "styles"], [1222, 23, 995, 27], [1222, 24, 995, 28, "processingModal"], [1222, 39, 995, 44], [1223, 10, 995, 44, "children"], [1223, 18, 995, 44], [1223, 33, 996, 10], [1223, 37, 996, 10, "_jsxDevRuntime"], [1223, 51, 996, 10], [1223, 52, 996, 10, "jsxDEV"], [1223, 58, 996, 10], [1223, 60, 996, 11, "_View"], [1223, 65, 996, 11], [1223, 66, 996, 11, "default"], [1223, 73, 996, 15], [1224, 12, 996, 16, "style"], [1224, 17, 996, 21], [1224, 19, 996, 23, "styles"], [1224, 25, 996, 29], [1224, 26, 996, 30, "processingContent"], [1224, 43, 996, 48], [1225, 12, 996, 48, "children"], [1225, 20, 996, 48], [1225, 36, 997, 12], [1225, 40, 997, 12, "_jsxDevRuntime"], [1225, 54, 997, 12], [1225, 55, 997, 12, "jsxDEV"], [1225, 61, 997, 12], [1225, 63, 997, 13, "_ActivityIndicator"], [1225, 81, 997, 13], [1225, 82, 997, 13, "default"], [1225, 89, 997, 30], [1226, 14, 997, 31, "size"], [1226, 18, 997, 35], [1226, 20, 997, 36], [1226, 27, 997, 43], [1227, 14, 997, 44, "color"], [1227, 19, 997, 49], [1227, 21, 997, 50], [1228, 12, 997, 59], [1229, 14, 997, 59, "fileName"], [1229, 22, 997, 59], [1229, 24, 997, 59, "_jsxFileName"], [1229, 36, 997, 59], [1230, 14, 997, 59, "lineNumber"], [1230, 24, 997, 59], [1231, 14, 997, 59, "columnNumber"], [1231, 26, 997, 59], [1232, 12, 997, 59], [1232, 19, 997, 61], [1232, 20, 997, 62], [1232, 35, 999, 12], [1232, 39, 999, 12, "_jsxDevRuntime"], [1232, 53, 999, 12], [1232, 54, 999, 12, "jsxDEV"], [1232, 60, 999, 12], [1232, 62, 999, 13, "_Text"], [1232, 67, 999, 13], [1232, 68, 999, 13, "default"], [1232, 75, 999, 17], [1233, 14, 999, 18, "style"], [1233, 19, 999, 23], [1233, 21, 999, 25, "styles"], [1233, 27, 999, 31], [1233, 28, 999, 32, "processingTitle"], [1233, 43, 999, 48], [1234, 14, 999, 48, "children"], [1234, 22, 999, 48], [1234, 25, 1000, 15, "processingState"], [1234, 40, 1000, 30], [1234, 45, 1000, 35], [1234, 56, 1000, 46], [1234, 60, 1000, 50], [1234, 80, 1000, 70], [1234, 82, 1001, 15, "processingState"], [1234, 97, 1001, 30], [1234, 102, 1001, 35], [1234, 113, 1001, 46], [1234, 117, 1001, 50], [1234, 146, 1001, 79], [1234, 148, 1002, 15, "processingState"], [1234, 163, 1002, 30], [1234, 168, 1002, 35], [1234, 180, 1002, 47], [1234, 184, 1002, 51], [1234, 216, 1002, 83], [1234, 218, 1003, 15, "processingState"], [1234, 233, 1003, 30], [1234, 238, 1003, 35], [1234, 249, 1003, 46], [1234, 253, 1003, 50], [1234, 275, 1003, 72], [1235, 12, 1003, 72], [1236, 14, 1003, 72, "fileName"], [1236, 22, 1003, 72], [1236, 24, 1003, 72, "_jsxFileName"], [1236, 36, 1003, 72], [1237, 14, 1003, 72, "lineNumber"], [1237, 24, 1003, 72], [1238, 14, 1003, 72, "columnNumber"], [1238, 26, 1003, 72], [1239, 12, 1003, 72], [1239, 19, 1004, 18], [1239, 20, 1004, 19], [1239, 35, 1005, 12], [1239, 39, 1005, 12, "_jsxDevRuntime"], [1239, 53, 1005, 12], [1239, 54, 1005, 12, "jsxDEV"], [1239, 60, 1005, 12], [1239, 62, 1005, 13, "_View"], [1239, 67, 1005, 13], [1239, 68, 1005, 13, "default"], [1239, 75, 1005, 17], [1240, 14, 1005, 18, "style"], [1240, 19, 1005, 23], [1240, 21, 1005, 25, "styles"], [1240, 27, 1005, 31], [1240, 28, 1005, 32, "progressBar"], [1240, 39, 1005, 44], [1241, 14, 1005, 44, "children"], [1241, 22, 1005, 44], [1241, 37, 1006, 14], [1241, 41, 1006, 14, "_jsxDevRuntime"], [1241, 55, 1006, 14], [1241, 56, 1006, 14, "jsxDEV"], [1241, 62, 1006, 14], [1241, 64, 1006, 15, "_View"], [1241, 69, 1006, 15], [1241, 70, 1006, 15, "default"], [1241, 77, 1006, 19], [1242, 16, 1007, 16, "style"], [1242, 21, 1007, 21], [1242, 23, 1007, 23], [1242, 24, 1008, 18, "styles"], [1242, 30, 1008, 24], [1242, 31, 1008, 25, "progressFill"], [1242, 43, 1008, 37], [1242, 45, 1009, 18], [1243, 18, 1009, 20, "width"], [1243, 23, 1009, 25], [1243, 25, 1009, 27], [1243, 28, 1009, 30, "processingProgress"], [1243, 46, 1009, 48], [1244, 16, 1009, 52], [1244, 17, 1009, 53], [1245, 14, 1010, 18], [1246, 16, 1010, 18, "fileName"], [1246, 24, 1010, 18], [1246, 26, 1010, 18, "_jsxFileName"], [1246, 38, 1010, 18], [1247, 16, 1010, 18, "lineNumber"], [1247, 26, 1010, 18], [1248, 16, 1010, 18, "columnNumber"], [1248, 28, 1010, 18], [1249, 14, 1010, 18], [1249, 21, 1011, 15], [1250, 12, 1011, 16], [1251, 14, 1011, 16, "fileName"], [1251, 22, 1011, 16], [1251, 24, 1011, 16, "_jsxFileName"], [1251, 36, 1011, 16], [1252, 14, 1011, 16, "lineNumber"], [1252, 24, 1011, 16], [1253, 14, 1011, 16, "columnNumber"], [1253, 26, 1011, 16], [1254, 12, 1011, 16], [1254, 19, 1012, 18], [1254, 20, 1012, 19], [1254, 35, 1013, 12], [1254, 39, 1013, 12, "_jsxDevRuntime"], [1254, 53, 1013, 12], [1254, 54, 1013, 12, "jsxDEV"], [1254, 60, 1013, 12], [1254, 62, 1013, 13, "_Text"], [1254, 67, 1013, 13], [1254, 68, 1013, 13, "default"], [1254, 75, 1013, 17], [1255, 14, 1013, 18, "style"], [1255, 19, 1013, 23], [1255, 21, 1013, 25, "styles"], [1255, 27, 1013, 31], [1255, 28, 1013, 32, "processingDescription"], [1255, 49, 1013, 54], [1256, 14, 1013, 54, "children"], [1256, 22, 1013, 54], [1256, 25, 1014, 15, "processingState"], [1256, 40, 1014, 30], [1256, 45, 1014, 35], [1256, 56, 1014, 46], [1256, 60, 1014, 50], [1256, 89, 1014, 79], [1256, 91, 1015, 15, "processingState"], [1256, 106, 1015, 30], [1256, 111, 1015, 35], [1256, 122, 1015, 46], [1256, 126, 1015, 50], [1256, 164, 1015, 88], [1256, 166, 1016, 15, "processingState"], [1256, 181, 1016, 30], [1256, 186, 1016, 35], [1256, 198, 1016, 47], [1256, 202, 1016, 51], [1256, 247, 1016, 96], [1256, 249, 1017, 15, "processingState"], [1256, 264, 1017, 30], [1256, 269, 1017, 35], [1256, 280, 1017, 46], [1256, 284, 1017, 50], [1256, 325, 1017, 91], [1257, 12, 1017, 91], [1258, 14, 1017, 91, "fileName"], [1258, 22, 1017, 91], [1258, 24, 1017, 91, "_jsxFileName"], [1258, 36, 1017, 91], [1259, 14, 1017, 91, "lineNumber"], [1259, 24, 1017, 91], [1260, 14, 1017, 91, "columnNumber"], [1260, 26, 1017, 91], [1261, 12, 1017, 91], [1261, 19, 1018, 18], [1261, 20, 1018, 19], [1261, 22, 1019, 13, "processingState"], [1261, 37, 1019, 28], [1261, 42, 1019, 33], [1261, 53, 1019, 44], [1261, 70, 1020, 14], [1261, 74, 1020, 14, "_jsxDevRuntime"], [1261, 88, 1020, 14], [1261, 89, 1020, 14, "jsxDEV"], [1261, 95, 1020, 14], [1261, 97, 1020, 15, "_lucideReactNative"], [1261, 115, 1020, 15], [1261, 116, 1020, 15, "CheckCircle"], [1261, 127, 1020, 26], [1262, 14, 1020, 27, "size"], [1262, 18, 1020, 31], [1262, 20, 1020, 33], [1262, 22, 1020, 36], [1263, 14, 1020, 37, "color"], [1263, 19, 1020, 42], [1263, 21, 1020, 43], [1263, 30, 1020, 52], [1264, 14, 1020, 53, "style"], [1264, 19, 1020, 58], [1264, 21, 1020, 60, "styles"], [1264, 27, 1020, 66], [1264, 28, 1020, 67, "successIcon"], [1265, 12, 1020, 79], [1266, 14, 1020, 79, "fileName"], [1266, 22, 1020, 79], [1266, 24, 1020, 79, "_jsxFileName"], [1266, 36, 1020, 79], [1267, 14, 1020, 79, "lineNumber"], [1267, 24, 1020, 79], [1268, 14, 1020, 79, "columnNumber"], [1268, 26, 1020, 79], [1269, 12, 1020, 79], [1269, 19, 1020, 81], [1269, 20, 1021, 13], [1270, 10, 1021, 13], [1271, 12, 1021, 13, "fileName"], [1271, 20, 1021, 13], [1271, 22, 1021, 13, "_jsxFileName"], [1271, 34, 1021, 13], [1272, 12, 1021, 13, "lineNumber"], [1272, 22, 1021, 13], [1273, 12, 1021, 13, "columnNumber"], [1273, 24, 1021, 13], [1274, 10, 1021, 13], [1274, 17, 1022, 16], [1275, 8, 1022, 17], [1276, 10, 1022, 17, "fileName"], [1276, 18, 1022, 17], [1276, 20, 1022, 17, "_jsxFileName"], [1276, 32, 1022, 17], [1277, 10, 1022, 17, "lineNumber"], [1277, 20, 1022, 17], [1278, 10, 1022, 17, "columnNumber"], [1278, 22, 1022, 17], [1279, 8, 1022, 17], [1279, 15, 1023, 14], [1280, 6, 1023, 15], [1281, 8, 1023, 15, "fileName"], [1281, 16, 1023, 15], [1281, 18, 1023, 15, "_jsxFileName"], [1281, 30, 1023, 15], [1282, 8, 1023, 15, "lineNumber"], [1282, 18, 1023, 15], [1283, 8, 1023, 15, "columnNumber"], [1283, 20, 1023, 15], [1284, 6, 1023, 15], [1284, 13, 1024, 13], [1284, 14, 1024, 14], [1284, 29, 1026, 6], [1284, 33, 1026, 6, "_jsxDevRuntime"], [1284, 47, 1026, 6], [1284, 48, 1026, 6, "jsxDEV"], [1284, 54, 1026, 6], [1284, 56, 1026, 7, "_Modal"], [1284, 62, 1026, 7], [1284, 63, 1026, 7, "default"], [1284, 70, 1026, 12], [1285, 8, 1027, 8, "visible"], [1285, 15, 1027, 15], [1285, 17, 1027, 17, "processingState"], [1285, 32, 1027, 32], [1285, 37, 1027, 37], [1285, 44, 1027, 45], [1286, 8, 1028, 8, "transparent"], [1286, 19, 1028, 19], [1287, 8, 1029, 8, "animationType"], [1287, 21, 1029, 21], [1287, 23, 1029, 22], [1287, 29, 1029, 28], [1288, 8, 1029, 28, "children"], [1288, 16, 1029, 28], [1288, 31, 1031, 8], [1288, 35, 1031, 8, "_jsxDevRuntime"], [1288, 49, 1031, 8], [1288, 50, 1031, 8, "jsxDEV"], [1288, 56, 1031, 8], [1288, 58, 1031, 9, "_View"], [1288, 63, 1031, 9], [1288, 64, 1031, 9, "default"], [1288, 71, 1031, 13], [1289, 10, 1031, 14, "style"], [1289, 15, 1031, 19], [1289, 17, 1031, 21, "styles"], [1289, 23, 1031, 27], [1289, 24, 1031, 28, "processingModal"], [1289, 39, 1031, 44], [1290, 10, 1031, 44, "children"], [1290, 18, 1031, 44], [1290, 33, 1032, 10], [1290, 37, 1032, 10, "_jsxDevRuntime"], [1290, 51, 1032, 10], [1290, 52, 1032, 10, "jsxDEV"], [1290, 58, 1032, 10], [1290, 60, 1032, 11, "_View"], [1290, 65, 1032, 11], [1290, 66, 1032, 11, "default"], [1290, 73, 1032, 15], [1291, 12, 1032, 16, "style"], [1291, 17, 1032, 21], [1291, 19, 1032, 23, "styles"], [1291, 25, 1032, 29], [1291, 26, 1032, 30, "errorContent"], [1291, 38, 1032, 43], [1292, 12, 1032, 43, "children"], [1292, 20, 1032, 43], [1292, 36, 1033, 12], [1292, 40, 1033, 12, "_jsxDevRuntime"], [1292, 54, 1033, 12], [1292, 55, 1033, 12, "jsxDEV"], [1292, 61, 1033, 12], [1292, 63, 1033, 13, "_lucideReactNative"], [1292, 81, 1033, 13], [1292, 82, 1033, 13, "X"], [1292, 83, 1033, 14], [1293, 14, 1033, 15, "size"], [1293, 18, 1033, 19], [1293, 20, 1033, 21], [1293, 22, 1033, 24], [1294, 14, 1033, 25, "color"], [1294, 19, 1033, 30], [1294, 21, 1033, 31], [1295, 12, 1033, 40], [1296, 14, 1033, 40, "fileName"], [1296, 22, 1033, 40], [1296, 24, 1033, 40, "_jsxFileName"], [1296, 36, 1033, 40], [1297, 14, 1033, 40, "lineNumber"], [1297, 24, 1033, 40], [1298, 14, 1033, 40, "columnNumber"], [1298, 26, 1033, 40], [1299, 12, 1033, 40], [1299, 19, 1033, 42], [1299, 20, 1033, 43], [1299, 35, 1034, 12], [1299, 39, 1034, 12, "_jsxDevRuntime"], [1299, 53, 1034, 12], [1299, 54, 1034, 12, "jsxDEV"], [1299, 60, 1034, 12], [1299, 62, 1034, 13, "_Text"], [1299, 67, 1034, 13], [1299, 68, 1034, 13, "default"], [1299, 75, 1034, 17], [1300, 14, 1034, 18, "style"], [1300, 19, 1034, 23], [1300, 21, 1034, 25, "styles"], [1300, 27, 1034, 31], [1300, 28, 1034, 32, "errorTitle"], [1300, 38, 1034, 43], [1301, 14, 1034, 43, "children"], [1301, 22, 1034, 43], [1301, 24, 1034, 44], [1302, 12, 1034, 61], [1303, 14, 1034, 61, "fileName"], [1303, 22, 1034, 61], [1303, 24, 1034, 61, "_jsxFileName"], [1303, 36, 1034, 61], [1304, 14, 1034, 61, "lineNumber"], [1304, 24, 1034, 61], [1305, 14, 1034, 61, "columnNumber"], [1305, 26, 1034, 61], [1306, 12, 1034, 61], [1306, 19, 1034, 67], [1306, 20, 1034, 68], [1306, 35, 1035, 12], [1306, 39, 1035, 12, "_jsxDevRuntime"], [1306, 53, 1035, 12], [1306, 54, 1035, 12, "jsxDEV"], [1306, 60, 1035, 12], [1306, 62, 1035, 13, "_Text"], [1306, 67, 1035, 13], [1306, 68, 1035, 13, "default"], [1306, 75, 1035, 17], [1307, 14, 1035, 18, "style"], [1307, 19, 1035, 23], [1307, 21, 1035, 25, "styles"], [1307, 27, 1035, 31], [1307, 28, 1035, 32, "errorMessage"], [1307, 40, 1035, 45], [1308, 14, 1035, 45, "children"], [1308, 22, 1035, 45], [1308, 24, 1035, 47, "errorMessage"], [1309, 12, 1035, 59], [1310, 14, 1035, 59, "fileName"], [1310, 22, 1035, 59], [1310, 24, 1035, 59, "_jsxFileName"], [1310, 36, 1035, 59], [1311, 14, 1035, 59, "lineNumber"], [1311, 24, 1035, 59], [1312, 14, 1035, 59, "columnNumber"], [1312, 26, 1035, 59], [1313, 12, 1035, 59], [1313, 19, 1035, 66], [1313, 20, 1035, 67], [1313, 35, 1036, 12], [1313, 39, 1036, 12, "_jsxDevRuntime"], [1313, 53, 1036, 12], [1313, 54, 1036, 12, "jsxDEV"], [1313, 60, 1036, 12], [1313, 62, 1036, 13, "_TouchableOpacity"], [1313, 79, 1036, 13], [1313, 80, 1036, 13, "default"], [1313, 87, 1036, 29], [1314, 14, 1037, 14, "onPress"], [1314, 21, 1037, 21], [1314, 23, 1037, 23, "retryCapture"], [1314, 35, 1037, 36], [1315, 14, 1038, 14, "style"], [1315, 19, 1038, 19], [1315, 21, 1038, 21, "styles"], [1315, 27, 1038, 27], [1315, 28, 1038, 28, "primaryButton"], [1315, 41, 1038, 42], [1316, 14, 1038, 42, "children"], [1316, 22, 1038, 42], [1316, 37, 1040, 14], [1316, 41, 1040, 14, "_jsxDevRuntime"], [1316, 55, 1040, 14], [1316, 56, 1040, 14, "jsxDEV"], [1316, 62, 1040, 14], [1316, 64, 1040, 15, "_Text"], [1316, 69, 1040, 15], [1316, 70, 1040, 15, "default"], [1316, 77, 1040, 19], [1317, 16, 1040, 20, "style"], [1317, 21, 1040, 25], [1317, 23, 1040, 27, "styles"], [1317, 29, 1040, 33], [1317, 30, 1040, 34, "primaryButtonText"], [1317, 47, 1040, 52], [1318, 16, 1040, 52, "children"], [1318, 24, 1040, 52], [1318, 26, 1040, 53], [1319, 14, 1040, 62], [1320, 16, 1040, 62, "fileName"], [1320, 24, 1040, 62], [1320, 26, 1040, 62, "_jsxFileName"], [1320, 38, 1040, 62], [1321, 16, 1040, 62, "lineNumber"], [1321, 26, 1040, 62], [1322, 16, 1040, 62, "columnNumber"], [1322, 28, 1040, 62], [1323, 14, 1040, 62], [1323, 21, 1040, 68], [1324, 12, 1040, 69], [1325, 14, 1040, 69, "fileName"], [1325, 22, 1040, 69], [1325, 24, 1040, 69, "_jsxFileName"], [1325, 36, 1040, 69], [1326, 14, 1040, 69, "lineNumber"], [1326, 24, 1040, 69], [1327, 14, 1040, 69, "columnNumber"], [1327, 26, 1040, 69], [1328, 12, 1040, 69], [1328, 19, 1041, 30], [1328, 20, 1041, 31], [1328, 35, 1042, 12], [1328, 39, 1042, 12, "_jsxDevRuntime"], [1328, 53, 1042, 12], [1328, 54, 1042, 12, "jsxDEV"], [1328, 60, 1042, 12], [1328, 62, 1042, 13, "_TouchableOpacity"], [1328, 79, 1042, 13], [1328, 80, 1042, 13, "default"], [1328, 87, 1042, 29], [1329, 14, 1043, 14, "onPress"], [1329, 21, 1043, 21], [1329, 23, 1043, 23, "onCancel"], [1329, 31, 1043, 32], [1330, 14, 1044, 14, "style"], [1330, 19, 1044, 19], [1330, 21, 1044, 21, "styles"], [1330, 27, 1044, 27], [1330, 28, 1044, 28, "secondaryButton"], [1330, 43, 1044, 44], [1331, 14, 1044, 44, "children"], [1331, 22, 1044, 44], [1331, 37, 1046, 14], [1331, 41, 1046, 14, "_jsxDevRuntime"], [1331, 55, 1046, 14], [1331, 56, 1046, 14, "jsxDEV"], [1331, 62, 1046, 14], [1331, 64, 1046, 15, "_Text"], [1331, 69, 1046, 15], [1331, 70, 1046, 15, "default"], [1331, 77, 1046, 19], [1332, 16, 1046, 20, "style"], [1332, 21, 1046, 25], [1332, 23, 1046, 27, "styles"], [1332, 29, 1046, 33], [1332, 30, 1046, 34, "secondaryButtonText"], [1332, 49, 1046, 54], [1333, 16, 1046, 54, "children"], [1333, 24, 1046, 54], [1333, 26, 1046, 55], [1334, 14, 1046, 61], [1335, 16, 1046, 61, "fileName"], [1335, 24, 1046, 61], [1335, 26, 1046, 61, "_jsxFileName"], [1335, 38, 1046, 61], [1336, 16, 1046, 61, "lineNumber"], [1336, 26, 1046, 61], [1337, 16, 1046, 61, "columnNumber"], [1337, 28, 1046, 61], [1338, 14, 1046, 61], [1338, 21, 1046, 67], [1339, 12, 1046, 68], [1340, 14, 1046, 68, "fileName"], [1340, 22, 1046, 68], [1340, 24, 1046, 68, "_jsxFileName"], [1340, 36, 1046, 68], [1341, 14, 1046, 68, "lineNumber"], [1341, 24, 1046, 68], [1342, 14, 1046, 68, "columnNumber"], [1342, 26, 1046, 68], [1343, 12, 1046, 68], [1343, 19, 1047, 30], [1343, 20, 1047, 31], [1344, 10, 1047, 31], [1345, 12, 1047, 31, "fileName"], [1345, 20, 1047, 31], [1345, 22, 1047, 31, "_jsxFileName"], [1345, 34, 1047, 31], [1346, 12, 1047, 31, "lineNumber"], [1346, 22, 1047, 31], [1347, 12, 1047, 31, "columnNumber"], [1347, 24, 1047, 31], [1348, 10, 1047, 31], [1348, 17, 1048, 16], [1349, 8, 1048, 17], [1350, 10, 1048, 17, "fileName"], [1350, 18, 1048, 17], [1350, 20, 1048, 17, "_jsxFileName"], [1350, 32, 1048, 17], [1351, 10, 1048, 17, "lineNumber"], [1351, 20, 1048, 17], [1352, 10, 1048, 17, "columnNumber"], [1352, 22, 1048, 17], [1353, 8, 1048, 17], [1353, 15, 1049, 14], [1354, 6, 1049, 15], [1355, 8, 1049, 15, "fileName"], [1355, 16, 1049, 15], [1355, 18, 1049, 15, "_jsxFileName"], [1355, 30, 1049, 15], [1356, 8, 1049, 15, "lineNumber"], [1356, 18, 1049, 15], [1357, 8, 1049, 15, "columnNumber"], [1357, 20, 1049, 15], [1358, 6, 1049, 15], [1358, 13, 1050, 13], [1358, 14, 1050, 14], [1359, 4, 1050, 14], [1360, 6, 1050, 14, "fileName"], [1360, 14, 1050, 14], [1360, 16, 1050, 14, "_jsxFileName"], [1360, 28, 1050, 14], [1361, 6, 1050, 14, "lineNumber"], [1361, 16, 1050, 14], [1362, 6, 1050, 14, "columnNumber"], [1362, 18, 1050, 14], [1363, 4, 1050, 14], [1363, 11, 1051, 10], [1363, 12, 1051, 11], [1364, 2, 1053, 0], [1365, 2, 1053, 1, "_s"], [1365, 4, 1053, 1], [1365, 5, 51, 24, "EchoCameraWeb"], [1365, 18, 51, 37], [1366, 4, 51, 37], [1366, 12, 58, 42, "useCameraPermissions"], [1366, 44, 58, 62], [1366, 46, 72, 19, "useUpload"], [1366, 64, 72, 28], [1367, 2, 72, 28], [1368, 2, 72, 28, "_c"], [1368, 4, 72, 28], [1368, 7, 51, 24, "EchoCameraWeb"], [1368, 20, 51, 37], [1369, 2, 1054, 0], [1369, 8, 1054, 6, "styles"], [1369, 14, 1054, 12], [1369, 17, 1054, 15, "StyleSheet"], [1369, 36, 1054, 25], [1369, 37, 1054, 26, "create"], [1369, 43, 1054, 32], [1369, 44, 1054, 33], [1370, 4, 1055, 2, "container"], [1370, 13, 1055, 11], [1370, 15, 1055, 13], [1371, 6, 1056, 4, "flex"], [1371, 10, 1056, 8], [1371, 12, 1056, 10], [1371, 13, 1056, 11], [1372, 6, 1057, 4, "backgroundColor"], [1372, 21, 1057, 19], [1372, 23, 1057, 21], [1373, 4, 1058, 2], [1373, 5, 1058, 3], [1374, 4, 1059, 2, "cameraContainer"], [1374, 19, 1059, 17], [1374, 21, 1059, 19], [1375, 6, 1060, 4, "flex"], [1375, 10, 1060, 8], [1375, 12, 1060, 10], [1375, 13, 1060, 11], [1376, 6, 1061, 4, "max<PERSON><PERSON><PERSON>"], [1376, 14, 1061, 12], [1376, 16, 1061, 14], [1376, 19, 1061, 17], [1377, 6, 1062, 4, "alignSelf"], [1377, 15, 1062, 13], [1377, 17, 1062, 15], [1377, 25, 1062, 23], [1378, 6, 1063, 4, "width"], [1378, 11, 1063, 9], [1378, 13, 1063, 11], [1379, 4, 1064, 2], [1379, 5, 1064, 3], [1380, 4, 1065, 2, "camera"], [1380, 10, 1065, 8], [1380, 12, 1065, 10], [1381, 6, 1066, 4, "flex"], [1381, 10, 1066, 8], [1381, 12, 1066, 10], [1382, 4, 1067, 2], [1382, 5, 1067, 3], [1383, 4, 1068, 2, "headerOverlay"], [1383, 17, 1068, 15], [1383, 19, 1068, 17], [1384, 6, 1069, 4, "position"], [1384, 14, 1069, 12], [1384, 16, 1069, 14], [1384, 26, 1069, 24], [1385, 6, 1070, 4, "top"], [1385, 9, 1070, 7], [1385, 11, 1070, 9], [1385, 12, 1070, 10], [1386, 6, 1071, 4, "left"], [1386, 10, 1071, 8], [1386, 12, 1071, 10], [1386, 13, 1071, 11], [1387, 6, 1072, 4, "right"], [1387, 11, 1072, 9], [1387, 13, 1072, 11], [1387, 14, 1072, 12], [1388, 6, 1073, 4, "backgroundColor"], [1388, 21, 1073, 19], [1388, 23, 1073, 21], [1388, 36, 1073, 34], [1389, 6, 1074, 4, "paddingTop"], [1389, 16, 1074, 14], [1389, 18, 1074, 16], [1389, 20, 1074, 18], [1390, 6, 1075, 4, "paddingHorizontal"], [1390, 23, 1075, 21], [1390, 25, 1075, 23], [1390, 27, 1075, 25], [1391, 6, 1076, 4, "paddingBottom"], [1391, 19, 1076, 17], [1391, 21, 1076, 19], [1392, 4, 1077, 2], [1392, 5, 1077, 3], [1393, 4, 1078, 2, "headerContent"], [1393, 17, 1078, 15], [1393, 19, 1078, 17], [1394, 6, 1079, 4, "flexDirection"], [1394, 19, 1079, 17], [1394, 21, 1079, 19], [1394, 26, 1079, 24], [1395, 6, 1080, 4, "justifyContent"], [1395, 20, 1080, 18], [1395, 22, 1080, 20], [1395, 37, 1080, 35], [1396, 6, 1081, 4, "alignItems"], [1396, 16, 1081, 14], [1396, 18, 1081, 16], [1397, 4, 1082, 2], [1397, 5, 1082, 3], [1398, 4, 1083, 2, "headerLeft"], [1398, 14, 1083, 12], [1398, 16, 1083, 14], [1399, 6, 1084, 4, "flex"], [1399, 10, 1084, 8], [1399, 12, 1084, 10], [1400, 4, 1085, 2], [1400, 5, 1085, 3], [1401, 4, 1086, 2, "headerTitle"], [1401, 15, 1086, 13], [1401, 17, 1086, 15], [1402, 6, 1087, 4, "fontSize"], [1402, 14, 1087, 12], [1402, 16, 1087, 14], [1402, 18, 1087, 16], [1403, 6, 1088, 4, "fontWeight"], [1403, 16, 1088, 14], [1403, 18, 1088, 16], [1403, 23, 1088, 21], [1404, 6, 1089, 4, "color"], [1404, 11, 1089, 9], [1404, 13, 1089, 11], [1404, 19, 1089, 17], [1405, 6, 1090, 4, "marginBottom"], [1405, 18, 1090, 16], [1405, 20, 1090, 18], [1406, 4, 1091, 2], [1406, 5, 1091, 3], [1407, 4, 1092, 2, "subtitleRow"], [1407, 15, 1092, 13], [1407, 17, 1092, 15], [1408, 6, 1093, 4, "flexDirection"], [1408, 19, 1093, 17], [1408, 21, 1093, 19], [1408, 26, 1093, 24], [1409, 6, 1094, 4, "alignItems"], [1409, 16, 1094, 14], [1409, 18, 1094, 16], [1409, 26, 1094, 24], [1410, 6, 1095, 4, "marginBottom"], [1410, 18, 1095, 16], [1410, 20, 1095, 18], [1411, 4, 1096, 2], [1411, 5, 1096, 3], [1412, 4, 1097, 2, "webIcon"], [1412, 11, 1097, 9], [1412, 13, 1097, 11], [1413, 6, 1098, 4, "fontSize"], [1413, 14, 1098, 12], [1413, 16, 1098, 14], [1413, 18, 1098, 16], [1414, 6, 1099, 4, "marginRight"], [1414, 17, 1099, 15], [1414, 19, 1099, 17], [1415, 4, 1100, 2], [1415, 5, 1100, 3], [1416, 4, 1101, 2, "headerSubtitle"], [1416, 18, 1101, 16], [1416, 20, 1101, 18], [1417, 6, 1102, 4, "fontSize"], [1417, 14, 1102, 12], [1417, 16, 1102, 14], [1417, 18, 1102, 16], [1418, 6, 1103, 4, "color"], [1418, 11, 1103, 9], [1418, 13, 1103, 11], [1418, 19, 1103, 17], [1419, 6, 1104, 4, "opacity"], [1419, 13, 1104, 11], [1419, 15, 1104, 13], [1420, 4, 1105, 2], [1420, 5, 1105, 3], [1421, 4, 1106, 2, "challengeRow"], [1421, 16, 1106, 14], [1421, 18, 1106, 16], [1422, 6, 1107, 4, "flexDirection"], [1422, 19, 1107, 17], [1422, 21, 1107, 19], [1422, 26, 1107, 24], [1423, 6, 1108, 4, "alignItems"], [1423, 16, 1108, 14], [1423, 18, 1108, 16], [1424, 4, 1109, 2], [1424, 5, 1109, 3], [1425, 4, 1110, 2, "challengeCode"], [1425, 17, 1110, 15], [1425, 19, 1110, 17], [1426, 6, 1111, 4, "fontSize"], [1426, 14, 1111, 12], [1426, 16, 1111, 14], [1426, 18, 1111, 16], [1427, 6, 1112, 4, "color"], [1427, 11, 1112, 9], [1427, 13, 1112, 11], [1427, 19, 1112, 17], [1428, 6, 1113, 4, "marginLeft"], [1428, 16, 1113, 14], [1428, 18, 1113, 16], [1428, 19, 1113, 17], [1429, 6, 1114, 4, "fontFamily"], [1429, 16, 1114, 14], [1429, 18, 1114, 16], [1430, 4, 1115, 2], [1430, 5, 1115, 3], [1431, 4, 1116, 2, "closeButton"], [1431, 15, 1116, 13], [1431, 17, 1116, 15], [1432, 6, 1117, 4, "padding"], [1432, 13, 1117, 11], [1432, 15, 1117, 13], [1433, 4, 1118, 2], [1433, 5, 1118, 3], [1434, 4, 1119, 2, "privacyNotice"], [1434, 17, 1119, 15], [1434, 19, 1119, 17], [1435, 6, 1120, 4, "position"], [1435, 14, 1120, 12], [1435, 16, 1120, 14], [1435, 26, 1120, 24], [1436, 6, 1121, 4, "top"], [1436, 9, 1121, 7], [1436, 11, 1121, 9], [1436, 14, 1121, 12], [1437, 6, 1122, 4, "left"], [1437, 10, 1122, 8], [1437, 12, 1122, 10], [1437, 14, 1122, 12], [1438, 6, 1123, 4, "right"], [1438, 11, 1123, 9], [1438, 13, 1123, 11], [1438, 15, 1123, 13], [1439, 6, 1124, 4, "backgroundColor"], [1439, 21, 1124, 19], [1439, 23, 1124, 21], [1439, 48, 1124, 46], [1440, 6, 1125, 4, "borderRadius"], [1440, 18, 1125, 16], [1440, 20, 1125, 18], [1440, 21, 1125, 19], [1441, 6, 1126, 4, "padding"], [1441, 13, 1126, 11], [1441, 15, 1126, 13], [1441, 17, 1126, 15], [1442, 6, 1127, 4, "flexDirection"], [1442, 19, 1127, 17], [1442, 21, 1127, 19], [1442, 26, 1127, 24], [1443, 6, 1128, 4, "alignItems"], [1443, 16, 1128, 14], [1443, 18, 1128, 16], [1444, 4, 1129, 2], [1444, 5, 1129, 3], [1445, 4, 1130, 2, "privacyText"], [1445, 15, 1130, 13], [1445, 17, 1130, 15], [1446, 6, 1131, 4, "color"], [1446, 11, 1131, 9], [1446, 13, 1131, 11], [1446, 19, 1131, 17], [1447, 6, 1132, 4, "fontSize"], [1447, 14, 1132, 12], [1447, 16, 1132, 14], [1447, 18, 1132, 16], [1448, 6, 1133, 4, "marginLeft"], [1448, 16, 1133, 14], [1448, 18, 1133, 16], [1448, 19, 1133, 17], [1449, 6, 1134, 4, "flex"], [1449, 10, 1134, 8], [1449, 12, 1134, 10], [1450, 4, 1135, 2], [1450, 5, 1135, 3], [1451, 4, 1136, 2, "footer<PERSON><PERSON><PERSON>"], [1451, 17, 1136, 15], [1451, 19, 1136, 17], [1452, 6, 1137, 4, "position"], [1452, 14, 1137, 12], [1452, 16, 1137, 14], [1452, 26, 1137, 24], [1453, 6, 1138, 4, "bottom"], [1453, 12, 1138, 10], [1453, 14, 1138, 12], [1453, 15, 1138, 13], [1454, 6, 1139, 4, "left"], [1454, 10, 1139, 8], [1454, 12, 1139, 10], [1454, 13, 1139, 11], [1455, 6, 1140, 4, "right"], [1455, 11, 1140, 9], [1455, 13, 1140, 11], [1455, 14, 1140, 12], [1456, 6, 1141, 4, "backgroundColor"], [1456, 21, 1141, 19], [1456, 23, 1141, 21], [1456, 36, 1141, 34], [1457, 6, 1142, 4, "paddingBottom"], [1457, 19, 1142, 17], [1457, 21, 1142, 19], [1457, 23, 1142, 21], [1458, 6, 1143, 4, "paddingTop"], [1458, 16, 1143, 14], [1458, 18, 1143, 16], [1458, 20, 1143, 18], [1459, 6, 1144, 4, "alignItems"], [1459, 16, 1144, 14], [1459, 18, 1144, 16], [1460, 4, 1145, 2], [1460, 5, 1145, 3], [1461, 4, 1146, 2, "instruction"], [1461, 15, 1146, 13], [1461, 17, 1146, 15], [1462, 6, 1147, 4, "fontSize"], [1462, 14, 1147, 12], [1462, 16, 1147, 14], [1462, 18, 1147, 16], [1463, 6, 1148, 4, "color"], [1463, 11, 1148, 9], [1463, 13, 1148, 11], [1463, 19, 1148, 17], [1464, 6, 1149, 4, "marginBottom"], [1464, 18, 1149, 16], [1464, 20, 1149, 18], [1465, 4, 1150, 2], [1465, 5, 1150, 3], [1466, 4, 1151, 2, "shutterButton"], [1466, 17, 1151, 15], [1466, 19, 1151, 17], [1467, 6, 1152, 4, "width"], [1467, 11, 1152, 9], [1467, 13, 1152, 11], [1467, 15, 1152, 13], [1468, 6, 1153, 4, "height"], [1468, 12, 1153, 10], [1468, 14, 1153, 12], [1468, 16, 1153, 14], [1469, 6, 1154, 4, "borderRadius"], [1469, 18, 1154, 16], [1469, 20, 1154, 18], [1469, 22, 1154, 20], [1470, 6, 1155, 4, "backgroundColor"], [1470, 21, 1155, 19], [1470, 23, 1155, 21], [1470, 29, 1155, 27], [1471, 6, 1156, 4, "justifyContent"], [1471, 20, 1156, 18], [1471, 22, 1156, 20], [1471, 30, 1156, 28], [1472, 6, 1157, 4, "alignItems"], [1472, 16, 1157, 14], [1472, 18, 1157, 16], [1472, 26, 1157, 24], [1473, 6, 1158, 4, "marginBottom"], [1473, 18, 1158, 16], [1473, 20, 1158, 18], [1473, 22, 1158, 20], [1474, 6, 1159, 4], [1474, 9, 1159, 7, "Platform"], [1474, 26, 1159, 15], [1474, 27, 1159, 16, "select"], [1474, 33, 1159, 22], [1474, 34, 1159, 23], [1475, 8, 1160, 6, "ios"], [1475, 11, 1160, 9], [1475, 13, 1160, 11], [1476, 10, 1161, 8, "shadowColor"], [1476, 21, 1161, 19], [1476, 23, 1161, 21], [1476, 32, 1161, 30], [1477, 10, 1162, 8, "shadowOffset"], [1477, 22, 1162, 20], [1477, 24, 1162, 22], [1478, 12, 1162, 24, "width"], [1478, 17, 1162, 29], [1478, 19, 1162, 31], [1478, 20, 1162, 32], [1479, 12, 1162, 34, "height"], [1479, 18, 1162, 40], [1479, 20, 1162, 42], [1480, 10, 1162, 44], [1480, 11, 1162, 45], [1481, 10, 1163, 8, "shadowOpacity"], [1481, 23, 1163, 21], [1481, 25, 1163, 23], [1481, 28, 1163, 26], [1482, 10, 1164, 8, "shadowRadius"], [1482, 22, 1164, 20], [1482, 24, 1164, 22], [1483, 8, 1165, 6], [1483, 9, 1165, 7], [1484, 8, 1166, 6, "android"], [1484, 15, 1166, 13], [1484, 17, 1166, 15], [1485, 10, 1167, 8, "elevation"], [1485, 19, 1167, 17], [1485, 21, 1167, 19], [1486, 8, 1168, 6], [1486, 9, 1168, 7], [1487, 8, 1169, 6, "web"], [1487, 11, 1169, 9], [1487, 13, 1169, 11], [1488, 10, 1170, 8, "boxShadow"], [1488, 19, 1170, 17], [1488, 21, 1170, 19], [1489, 8, 1171, 6], [1490, 6, 1172, 4], [1490, 7, 1172, 5], [1491, 4, 1173, 2], [1491, 5, 1173, 3], [1492, 4, 1174, 2, "shutterButtonDisabled"], [1492, 25, 1174, 23], [1492, 27, 1174, 25], [1493, 6, 1175, 4, "opacity"], [1493, 13, 1175, 11], [1493, 15, 1175, 13], [1494, 4, 1176, 2], [1494, 5, 1176, 3], [1495, 4, 1177, 2, "shutterInner"], [1495, 16, 1177, 14], [1495, 18, 1177, 16], [1496, 6, 1178, 4, "width"], [1496, 11, 1178, 9], [1496, 13, 1178, 11], [1496, 15, 1178, 13], [1497, 6, 1179, 4, "height"], [1497, 12, 1179, 10], [1497, 14, 1179, 12], [1497, 16, 1179, 14], [1498, 6, 1180, 4, "borderRadius"], [1498, 18, 1180, 16], [1498, 20, 1180, 18], [1498, 22, 1180, 20], [1499, 6, 1181, 4, "backgroundColor"], [1499, 21, 1181, 19], [1499, 23, 1181, 21], [1499, 29, 1181, 27], [1500, 6, 1182, 4, "borderWidth"], [1500, 17, 1182, 15], [1500, 19, 1182, 17], [1500, 20, 1182, 18], [1501, 6, 1183, 4, "borderColor"], [1501, 17, 1183, 15], [1501, 19, 1183, 17], [1502, 4, 1184, 2], [1502, 5, 1184, 3], [1503, 4, 1185, 2, "privacyNote"], [1503, 15, 1185, 13], [1503, 17, 1185, 15], [1504, 6, 1186, 4, "fontSize"], [1504, 14, 1186, 12], [1504, 16, 1186, 14], [1504, 18, 1186, 16], [1505, 6, 1187, 4, "color"], [1505, 11, 1187, 9], [1505, 13, 1187, 11], [1506, 4, 1188, 2], [1506, 5, 1188, 3], [1507, 4, 1189, 2, "processingModal"], [1507, 19, 1189, 17], [1507, 21, 1189, 19], [1508, 6, 1190, 4, "flex"], [1508, 10, 1190, 8], [1508, 12, 1190, 10], [1508, 13, 1190, 11], [1509, 6, 1191, 4, "backgroundColor"], [1509, 21, 1191, 19], [1509, 23, 1191, 21], [1509, 43, 1191, 41], [1510, 6, 1192, 4, "justifyContent"], [1510, 20, 1192, 18], [1510, 22, 1192, 20], [1510, 30, 1192, 28], [1511, 6, 1193, 4, "alignItems"], [1511, 16, 1193, 14], [1511, 18, 1193, 16], [1512, 4, 1194, 2], [1512, 5, 1194, 3], [1513, 4, 1195, 2, "processingContent"], [1513, 21, 1195, 19], [1513, 23, 1195, 21], [1514, 6, 1196, 4, "backgroundColor"], [1514, 21, 1196, 19], [1514, 23, 1196, 21], [1514, 29, 1196, 27], [1515, 6, 1197, 4, "borderRadius"], [1515, 18, 1197, 16], [1515, 20, 1197, 18], [1515, 22, 1197, 20], [1516, 6, 1198, 4, "padding"], [1516, 13, 1198, 11], [1516, 15, 1198, 13], [1516, 17, 1198, 15], [1517, 6, 1199, 4, "width"], [1517, 11, 1199, 9], [1517, 13, 1199, 11], [1517, 18, 1199, 16], [1518, 6, 1200, 4, "max<PERSON><PERSON><PERSON>"], [1518, 14, 1200, 12], [1518, 16, 1200, 14], [1518, 19, 1200, 17], [1519, 6, 1201, 4, "alignItems"], [1519, 16, 1201, 14], [1519, 18, 1201, 16], [1520, 4, 1202, 2], [1520, 5, 1202, 3], [1521, 4, 1203, 2, "processingTitle"], [1521, 19, 1203, 17], [1521, 21, 1203, 19], [1522, 6, 1204, 4, "fontSize"], [1522, 14, 1204, 12], [1522, 16, 1204, 14], [1522, 18, 1204, 16], [1523, 6, 1205, 4, "fontWeight"], [1523, 16, 1205, 14], [1523, 18, 1205, 16], [1523, 23, 1205, 21], [1524, 6, 1206, 4, "color"], [1524, 11, 1206, 9], [1524, 13, 1206, 11], [1524, 22, 1206, 20], [1525, 6, 1207, 4, "marginTop"], [1525, 15, 1207, 13], [1525, 17, 1207, 15], [1525, 19, 1207, 17], [1526, 6, 1208, 4, "marginBottom"], [1526, 18, 1208, 16], [1526, 20, 1208, 18], [1527, 4, 1209, 2], [1527, 5, 1209, 3], [1528, 4, 1210, 2, "progressBar"], [1528, 15, 1210, 13], [1528, 17, 1210, 15], [1529, 6, 1211, 4, "width"], [1529, 11, 1211, 9], [1529, 13, 1211, 11], [1529, 19, 1211, 17], [1530, 6, 1212, 4, "height"], [1530, 12, 1212, 10], [1530, 14, 1212, 12], [1530, 15, 1212, 13], [1531, 6, 1213, 4, "backgroundColor"], [1531, 21, 1213, 19], [1531, 23, 1213, 21], [1531, 32, 1213, 30], [1532, 6, 1214, 4, "borderRadius"], [1532, 18, 1214, 16], [1532, 20, 1214, 18], [1532, 21, 1214, 19], [1533, 6, 1215, 4, "overflow"], [1533, 14, 1215, 12], [1533, 16, 1215, 14], [1533, 24, 1215, 22], [1534, 6, 1216, 4, "marginBottom"], [1534, 18, 1216, 16], [1534, 20, 1216, 18], [1535, 4, 1217, 2], [1535, 5, 1217, 3], [1536, 4, 1218, 2, "progressFill"], [1536, 16, 1218, 14], [1536, 18, 1218, 16], [1537, 6, 1219, 4, "height"], [1537, 12, 1219, 10], [1537, 14, 1219, 12], [1537, 20, 1219, 18], [1538, 6, 1220, 4, "backgroundColor"], [1538, 21, 1220, 19], [1538, 23, 1220, 21], [1538, 32, 1220, 30], [1539, 6, 1221, 4, "borderRadius"], [1539, 18, 1221, 16], [1539, 20, 1221, 18], [1540, 4, 1222, 2], [1540, 5, 1222, 3], [1541, 4, 1223, 2, "processingDescription"], [1541, 25, 1223, 23], [1541, 27, 1223, 25], [1542, 6, 1224, 4, "fontSize"], [1542, 14, 1224, 12], [1542, 16, 1224, 14], [1542, 18, 1224, 16], [1543, 6, 1225, 4, "color"], [1543, 11, 1225, 9], [1543, 13, 1225, 11], [1543, 22, 1225, 20], [1544, 6, 1226, 4, "textAlign"], [1544, 15, 1226, 13], [1544, 17, 1226, 15], [1545, 4, 1227, 2], [1545, 5, 1227, 3], [1546, 4, 1228, 2, "successIcon"], [1546, 15, 1228, 13], [1546, 17, 1228, 15], [1547, 6, 1229, 4, "marginTop"], [1547, 15, 1229, 13], [1547, 17, 1229, 15], [1548, 4, 1230, 2], [1548, 5, 1230, 3], [1549, 4, 1231, 2, "errorContent"], [1549, 16, 1231, 14], [1549, 18, 1231, 16], [1550, 6, 1232, 4, "backgroundColor"], [1550, 21, 1232, 19], [1550, 23, 1232, 21], [1550, 29, 1232, 27], [1551, 6, 1233, 4, "borderRadius"], [1551, 18, 1233, 16], [1551, 20, 1233, 18], [1551, 22, 1233, 20], [1552, 6, 1234, 4, "padding"], [1552, 13, 1234, 11], [1552, 15, 1234, 13], [1552, 17, 1234, 15], [1553, 6, 1235, 4, "width"], [1553, 11, 1235, 9], [1553, 13, 1235, 11], [1553, 18, 1235, 16], [1554, 6, 1236, 4, "max<PERSON><PERSON><PERSON>"], [1554, 14, 1236, 12], [1554, 16, 1236, 14], [1554, 19, 1236, 17], [1555, 6, 1237, 4, "alignItems"], [1555, 16, 1237, 14], [1555, 18, 1237, 16], [1556, 4, 1238, 2], [1556, 5, 1238, 3], [1557, 4, 1239, 2, "errorTitle"], [1557, 14, 1239, 12], [1557, 16, 1239, 14], [1558, 6, 1240, 4, "fontSize"], [1558, 14, 1240, 12], [1558, 16, 1240, 14], [1558, 18, 1240, 16], [1559, 6, 1241, 4, "fontWeight"], [1559, 16, 1241, 14], [1559, 18, 1241, 16], [1559, 23, 1241, 21], [1560, 6, 1242, 4, "color"], [1560, 11, 1242, 9], [1560, 13, 1242, 11], [1560, 22, 1242, 20], [1561, 6, 1243, 4, "marginTop"], [1561, 15, 1243, 13], [1561, 17, 1243, 15], [1561, 19, 1243, 17], [1562, 6, 1244, 4, "marginBottom"], [1562, 18, 1244, 16], [1562, 20, 1244, 18], [1563, 4, 1245, 2], [1563, 5, 1245, 3], [1564, 4, 1246, 2, "errorMessage"], [1564, 16, 1246, 14], [1564, 18, 1246, 16], [1565, 6, 1247, 4, "fontSize"], [1565, 14, 1247, 12], [1565, 16, 1247, 14], [1565, 18, 1247, 16], [1566, 6, 1248, 4, "color"], [1566, 11, 1248, 9], [1566, 13, 1248, 11], [1566, 22, 1248, 20], [1567, 6, 1249, 4, "textAlign"], [1567, 15, 1249, 13], [1567, 17, 1249, 15], [1567, 25, 1249, 23], [1568, 6, 1250, 4, "marginBottom"], [1568, 18, 1250, 16], [1568, 20, 1250, 18], [1569, 4, 1251, 2], [1569, 5, 1251, 3], [1570, 4, 1252, 2, "primaryButton"], [1570, 17, 1252, 15], [1570, 19, 1252, 17], [1571, 6, 1253, 4, "backgroundColor"], [1571, 21, 1253, 19], [1571, 23, 1253, 21], [1571, 32, 1253, 30], [1572, 6, 1254, 4, "paddingHorizontal"], [1572, 23, 1254, 21], [1572, 25, 1254, 23], [1572, 27, 1254, 25], [1573, 6, 1255, 4, "paddingVertical"], [1573, 21, 1255, 19], [1573, 23, 1255, 21], [1573, 25, 1255, 23], [1574, 6, 1256, 4, "borderRadius"], [1574, 18, 1256, 16], [1574, 20, 1256, 18], [1574, 21, 1256, 19], [1575, 6, 1257, 4, "marginTop"], [1575, 15, 1257, 13], [1575, 17, 1257, 15], [1576, 4, 1258, 2], [1576, 5, 1258, 3], [1577, 4, 1259, 2, "primaryButtonText"], [1577, 21, 1259, 19], [1577, 23, 1259, 21], [1578, 6, 1260, 4, "color"], [1578, 11, 1260, 9], [1578, 13, 1260, 11], [1578, 19, 1260, 17], [1579, 6, 1261, 4, "fontSize"], [1579, 14, 1261, 12], [1579, 16, 1261, 14], [1579, 18, 1261, 16], [1580, 6, 1262, 4, "fontWeight"], [1580, 16, 1262, 14], [1580, 18, 1262, 16], [1581, 4, 1263, 2], [1581, 5, 1263, 3], [1582, 4, 1264, 2, "secondaryButton"], [1582, 19, 1264, 17], [1582, 21, 1264, 19], [1583, 6, 1265, 4, "paddingHorizontal"], [1583, 23, 1265, 21], [1583, 25, 1265, 23], [1583, 27, 1265, 25], [1584, 6, 1266, 4, "paddingVertical"], [1584, 21, 1266, 19], [1584, 23, 1266, 21], [1584, 25, 1266, 23], [1585, 6, 1267, 4, "marginTop"], [1585, 15, 1267, 13], [1585, 17, 1267, 15], [1586, 4, 1268, 2], [1586, 5, 1268, 3], [1587, 4, 1269, 2, "secondaryButtonText"], [1587, 23, 1269, 21], [1587, 25, 1269, 23], [1588, 6, 1270, 4, "color"], [1588, 11, 1270, 9], [1588, 13, 1270, 11], [1588, 22, 1270, 20], [1589, 6, 1271, 4, "fontSize"], [1589, 14, 1271, 12], [1589, 16, 1271, 14], [1590, 4, 1272, 2], [1590, 5, 1272, 3], [1591, 4, 1273, 2, "permissionContent"], [1591, 21, 1273, 19], [1591, 23, 1273, 21], [1592, 6, 1274, 4, "flex"], [1592, 10, 1274, 8], [1592, 12, 1274, 10], [1592, 13, 1274, 11], [1593, 6, 1275, 4, "justifyContent"], [1593, 20, 1275, 18], [1593, 22, 1275, 20], [1593, 30, 1275, 28], [1594, 6, 1276, 4, "alignItems"], [1594, 16, 1276, 14], [1594, 18, 1276, 16], [1594, 26, 1276, 24], [1595, 6, 1277, 4, "padding"], [1595, 13, 1277, 11], [1595, 15, 1277, 13], [1596, 4, 1278, 2], [1596, 5, 1278, 3], [1597, 4, 1279, 2, "permissionTitle"], [1597, 19, 1279, 17], [1597, 21, 1279, 19], [1598, 6, 1280, 4, "fontSize"], [1598, 14, 1280, 12], [1598, 16, 1280, 14], [1598, 18, 1280, 16], [1599, 6, 1281, 4, "fontWeight"], [1599, 16, 1281, 14], [1599, 18, 1281, 16], [1599, 23, 1281, 21], [1600, 6, 1282, 4, "color"], [1600, 11, 1282, 9], [1600, 13, 1282, 11], [1600, 22, 1282, 20], [1601, 6, 1283, 4, "marginTop"], [1601, 15, 1283, 13], [1601, 17, 1283, 15], [1601, 19, 1283, 17], [1602, 6, 1284, 4, "marginBottom"], [1602, 18, 1284, 16], [1602, 20, 1284, 18], [1603, 4, 1285, 2], [1603, 5, 1285, 3], [1604, 4, 1286, 2, "permissionDescription"], [1604, 25, 1286, 23], [1604, 27, 1286, 25], [1605, 6, 1287, 4, "fontSize"], [1605, 14, 1287, 12], [1605, 16, 1287, 14], [1605, 18, 1287, 16], [1606, 6, 1288, 4, "color"], [1606, 11, 1288, 9], [1606, 13, 1288, 11], [1606, 22, 1288, 20], [1607, 6, 1289, 4, "textAlign"], [1607, 15, 1289, 13], [1607, 17, 1289, 15], [1607, 25, 1289, 23], [1608, 6, 1290, 4, "marginBottom"], [1608, 18, 1290, 16], [1608, 20, 1290, 18], [1609, 4, 1291, 2], [1609, 5, 1291, 3], [1610, 4, 1292, 2, "loadingText"], [1610, 15, 1292, 13], [1610, 17, 1292, 15], [1611, 6, 1293, 4, "color"], [1611, 11, 1293, 9], [1611, 13, 1293, 11], [1611, 22, 1293, 20], [1612, 6, 1294, 4, "marginTop"], [1612, 15, 1294, 13], [1612, 17, 1294, 15], [1613, 4, 1295, 2], [1613, 5, 1295, 3], [1614, 4, 1296, 2], [1615, 4, 1297, 2, "blurZone"], [1615, 12, 1297, 10], [1615, 14, 1297, 12], [1616, 6, 1298, 4, "position"], [1616, 14, 1298, 12], [1616, 16, 1298, 14], [1616, 26, 1298, 24], [1617, 6, 1299, 4, "overflow"], [1617, 14, 1299, 12], [1617, 16, 1299, 14], [1618, 4, 1300, 2], [1618, 5, 1300, 3], [1619, 4, 1301, 2, "previewChip"], [1619, 15, 1301, 13], [1619, 17, 1301, 15], [1620, 6, 1302, 4, "position"], [1620, 14, 1302, 12], [1620, 16, 1302, 14], [1620, 26, 1302, 24], [1621, 6, 1303, 4, "top"], [1621, 9, 1303, 7], [1621, 11, 1303, 9], [1621, 12, 1303, 10], [1622, 6, 1304, 4, "right"], [1622, 11, 1304, 9], [1622, 13, 1304, 11], [1622, 14, 1304, 12], [1623, 6, 1305, 4, "backgroundColor"], [1623, 21, 1305, 19], [1623, 23, 1305, 21], [1623, 40, 1305, 38], [1624, 6, 1306, 4, "paddingHorizontal"], [1624, 23, 1306, 21], [1624, 25, 1306, 23], [1624, 27, 1306, 25], [1625, 6, 1307, 4, "paddingVertical"], [1625, 21, 1307, 19], [1625, 23, 1307, 21], [1625, 24, 1307, 22], [1626, 6, 1308, 4, "borderRadius"], [1626, 18, 1308, 16], [1626, 20, 1308, 18], [1627, 4, 1309, 2], [1627, 5, 1309, 3], [1628, 4, 1310, 2, "previewChipText"], [1628, 19, 1310, 17], [1628, 21, 1310, 19], [1629, 6, 1311, 4, "color"], [1629, 11, 1311, 9], [1629, 13, 1311, 11], [1629, 19, 1311, 17], [1630, 6, 1312, 4, "fontSize"], [1630, 14, 1312, 12], [1630, 16, 1312, 14], [1630, 18, 1312, 16], [1631, 6, 1313, 4, "fontWeight"], [1631, 16, 1313, 14], [1631, 18, 1313, 16], [1632, 4, 1314, 2], [1633, 2, 1315, 0], [1633, 3, 1315, 1], [1633, 4, 1315, 2], [1634, 2, 1315, 3], [1634, 6, 1315, 3, "_c"], [1634, 8, 1315, 3], [1635, 2, 1315, 3, "$RefreshReg$"], [1635, 14, 1315, 3], [1635, 15, 1315, 3, "_c"], [1635, 17, 1315, 3], [1636, 0, 1315, 3], [1636, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdwE;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}