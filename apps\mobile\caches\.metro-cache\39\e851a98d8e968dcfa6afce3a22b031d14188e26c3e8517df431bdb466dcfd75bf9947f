{"dependencies": [{"name": "./JsiSkPoint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 42, "index": 42}}], "key": "t00LaVO/wJ2FJvJQ0krGRNiisCQ=", "exportNames": ["*"]}}, {"name": "./JsiSkPaint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 42, "index": 85}}], "key": "tZL5XO67L1lBKN/ngQFuxeWOGxA=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 86}, "end": {"line": 3, "column": 40, "index": 126}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}, {"name": "./JsiSkColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 127}, "end": {"line": 4, "column": 37, "index": 164}}], "key": "qiyS+7+qWEZCxGEYLrnH/T0sLLo=", "exportNames": ["*"]}}, {"name": "./JsiSkSurfaceFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 165}, "end": {"line": 5, "column": 60, "index": 225}}], "key": "QsD86QlRnGcfjBXTSZ76uEe+5hA=", "exportNames": ["*"]}}, {"name": "./JsiSkRRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 226}, "end": {"line": 6, "column": 42, "index": 268}}], "key": "n4Z2DW77BVppQ2PhsKnE4j8f2qY=", "exportNames": ["*"]}}, {"name": "./JsiSkRSXform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 269}, "end": {"line": 7, "column": 46, "index": 315}}], "key": "HY7rKwrek3L50sQmwciH/W0T7ew=", "exportNames": ["*"]}}, {"name": "./JsiSkContourMeasureIter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 316}, "end": {"line": 8, "column": 68, "index": 384}}], "key": "baL96DIW+TvGbW6hkAoKZZbukh8=", "exportNames": ["*"]}}, {"name": "./JsiSkPictureRecorder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 385}, "end": {"line": 9, "column": 62, "index": 447}}], "key": "VOZfkpPXEfCJL/UYmfsj0iFDfCE=", "exportNames": ["*"]}}, {"name": "./JsiSkPictureFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 448}, "end": {"line": 10, "column": 60, "index": 508}}], "key": "zIwoOVZaY+5eg96kuX7jNx+koC0=", "exportNames": ["*"]}}, {"name": "./JsiSkPathFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 509}, "end": {"line": 11, "column": 54, "index": 563}}], "key": "xOnnLKeyjUUQXkTGkDqcfpMReAY=", "exportNames": ["*"]}}, {"name": "./JsiSkMatrix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 564}, "end": {"line": 12, "column": 44, "index": 608}}], "key": "aOVfjZgmz4R2ci39pV6HZujK8og=", "exportNames": ["*"]}}, {"name": "./JsiSkColorFilterFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 609}, "end": {"line": 13, "column": 68, "index": 677}}], "key": "K6gNw6D7r0hAvpqhUdcNTamU/QY=", "exportNames": ["*"]}}, {"name": "./JsiSkTypefaceFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 678}, "end": {"line": 14, "column": 62, "index": 740}}], "key": "ryE3yMcmiQGLx9RTV9FuXoVtPrU=", "exportNames": ["*"]}}, {"name": "./JsiSkMaskFilterFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 741}, "end": {"line": 15, "column": 66, "index": 807}}], "key": "UXRe4p1re4CVSLnPwfEqsrvj968=", "exportNames": ["*"]}}, {"name": "./JsiSkRuntimeEffectFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 808}, "end": {"line": 16, "column": 72, "index": 880}}], "key": "8Juf9QRyOfCsFXk+Rg33wDl5FCQ=", "exportNames": ["*"]}}, {"name": "./JsiSkImageFilterFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 881}, "end": {"line": 17, "column": 68, "index": 949}}], "key": "MSX240Emu6Voj3f8NfOeww9XQoY=", "exportNames": ["*"]}}, {"name": "./JsiSkShaderFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 950}, "end": {"line": 18, "column": 58, "index": 1008}}], "key": "mwD1USShrLW0/SuLk63b6FFXMKM=", "exportNames": ["*"]}}, {"name": "./JsiSkPathEffectFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 1009}, "end": {"line": 19, "column": 66, "index": 1075}}], "key": "ELh8dwXIMhS2QjSubOolhjGUqg8=", "exportNames": ["*"]}}, {"name": "./JsiSkDataFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 1076}, "end": {"line": 20, "column": 54, "index": 1130}}], "key": "L1dP2thoICT6VA3bWCN0srPrqBc=", "exportNames": ["*"]}}, {"name": "./JsiSkImageFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 1131}, "end": {"line": 21, "column": 56, "index": 1187}}], "key": "orJVjKA7eKpKgOM8dE8JQ/Gzh98=", "exportNames": ["*"]}}, {"name": "./JsiSkSVGFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 1188}, "end": {"line": 22, "column": 52, "index": 1240}}], "key": "53UTIVywcfNvKBUc+gR00K4nZHc=", "exportNames": ["*"]}}, {"name": "./JsiSkTextBlobFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 1241}, "end": {"line": 23, "column": 62, "index": 1303}}], "key": "cQJ70p3Mwex9m6v1/pRTjL1RO5Y=", "exportNames": ["*"]}}, {"name": "./JsiSkFont", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 1304}, "end": {"line": 24, "column": 40, "index": 1344}}], "key": "s9s7ketQ537BYUKlzBosFu+c4vc=", "exportNames": ["*"]}}, {"name": "./JsiSkVerticesFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 1345}, "end": {"line": 25, "column": 54, "index": 1399}}], "key": "AKVfbkciLgzlREID/dSdLHwrfe4=", "exportNames": ["*"]}}, {"name": "./JsiSkPath", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 1400}, "end": {"line": 26, "column": 40, "index": 1440}}], "key": "h12LvMRBvFyvLJVrX3awiGHZPFU=", "exportNames": ["*"]}}, {"name": "./JsiSkTypeface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 1441}, "end": {"line": 27, "column": 48, "index": 1489}}], "key": "oqqQaxz4M2TUiGCEbbh7Ll+rY0E=", "exportNames": ["*"]}}, {"name": "./JsiSkTypefaceFontProviderFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 1490}, "end": {"line": 28, "column": 86, "index": 1576}}], "key": "2kL6sQh+omIUOwOqqT7quB3ogGE=", "exportNames": ["*"]}}, {"name": "./JsiSkFontMgrFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 1577}, "end": {"line": 29, "column": 60, "index": 1637}}], "key": "BfNpxWbD+NYqwZbls+qtwdc8pKU=", "exportNames": ["*"]}}, {"name": "./JsiSkAnimatedImageFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 1638}, "end": {"line": 30, "column": 72, "index": 1710}}], "key": "CREexXoq1t269UH2SJUd9Zkv6UA=", "exportNames": ["*"]}}, {"name": "./JsiSkParagraphBuilderFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 1711}, "end": {"line": 31, "column": 78, "index": 1789}}], "key": "YO7F3PEc8DtRM9jbTuccyBZWNRc=", "exportNames": ["*"]}}, {"name": "./JsiSkNativeBufferFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 1790}, "end": {"line": 32, "column": 70, "index": 1860}}], "key": "VFGsUz5/VFJy8JiLBE9Yvkt51lQ=", "exportNames": ["*"]}}, {"name": "./JsiVideo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 1861}, "end": {"line": 33, "column": 41, "index": 1902}}], "key": "fuQogJyUU4aeE84GON74peZ4SCo=", "exportNames": ["*"]}}, {"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1903}, "end": {"line": 34, "column": 52, "index": 1955}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkApi = void 0;\n  var _JsiSkPoint = require(_dependencyMap[0], \"./JsiSkPoint\");\n  var _JsiSkPaint = require(_dependencyMap[1], \"./JsiSkPaint\");\n  var _JsiSkRect = require(_dependencyMap[2], \"./JsiSkRect\");\n  var _JsiSkColor = require(_dependencyMap[3], \"./JsiSkColor\");\n  var _JsiSkSurfaceFactory = require(_dependencyMap[4], \"./JsiSkSurfaceFactory\");\n  var _JsiSkRRect = require(_dependencyMap[5], \"./JsiSkRRect\");\n  var _JsiSkRSXform = require(_dependencyMap[6], \"./JsiSkRSXform\");\n  var _JsiSkContourMeasureIter = require(_dependencyMap[7], \"./JsiSkContourMeasureIter\");\n  var _JsiSkPictureRecorder = require(_dependencyMap[8], \"./JsiSkPictureRecorder\");\n  var _JsiSkPictureFactory = require(_dependencyMap[9], \"./JsiSkPictureFactory\");\n  var _JsiSkPathFactory = require(_dependencyMap[10], \"./JsiSkPathFactory\");\n  var _JsiSkMatrix = require(_dependencyMap[11], \"./JsiSkMatrix\");\n  var _JsiSkColorFilterFactory = require(_dependencyMap[12], \"./JsiSkColorFilterFactory\");\n  var _JsiSkTypefaceFactory = require(_dependencyMap[13], \"./JsiSkTypefaceFactory\");\n  var _JsiSkMaskFilterFactory = require(_dependencyMap[14], \"./JsiSkMaskFilterFactory\");\n  var _JsiSkRuntimeEffectFactory = require(_dependencyMap[15], \"./JsiSkRuntimeEffectFactory\");\n  var _JsiSkImageFilterFactory = require(_dependencyMap[16], \"./JsiSkImageFilterFactory\");\n  var _JsiSkShaderFactory = require(_dependencyMap[17], \"./JsiSkShaderFactory\");\n  var _JsiSkPathEffectFactory = require(_dependencyMap[18], \"./JsiSkPathEffectFactory\");\n  var _JsiSkDataFactory = require(_dependencyMap[19], \"./JsiSkDataFactory\");\n  var _JsiSkImageFactory = require(_dependencyMap[20], \"./JsiSkImageFactory\");\n  var _JsiSkSVGFactory = require(_dependencyMap[21], \"./JsiSkSVGFactory\");\n  var _JsiSkTextBlobFactory = require(_dependencyMap[22], \"./JsiSkTextBlobFactory\");\n  var _JsiSkFont = require(_dependencyMap[23], \"./JsiSkFont\");\n  var _JsiSkVerticesFactory = require(_dependencyMap[24], \"./JsiSkVerticesFactory\");\n  var _JsiSkPath = require(_dependencyMap[25], \"./JsiSkPath\");\n  var _JsiSkTypeface = require(_dependencyMap[26], \"./JsiSkTypeface\");\n  var _JsiSkTypefaceFontProviderFactory = require(_dependencyMap[27], \"./JsiSkTypefaceFontProviderFactory\");\n  var _JsiSkFontMgrFactory = require(_dependencyMap[28], \"./JsiSkFontMgrFactory\");\n  var _JsiSkAnimatedImageFactory = require(_dependencyMap[29], \"./JsiSkAnimatedImageFactory\");\n  var _JsiSkParagraphBuilderFactory = require(_dependencyMap[30], \"./JsiSkParagraphBuilderFactory\");\n  var _JsiSkNativeBufferFactory = require(_dependencyMap[31], \"./JsiSkNativeBufferFactory\");\n  var _JsiVideo = require(_dependencyMap[32], \"./JsiVideo\");\n  var _Host = require(_dependencyMap[33], \"./Host\");\n  const JsiSkApi = CanvasKit => ({\n    Point: (x, y) => new _JsiSkPoint.JsiSkPoint(CanvasKit, Float32Array.of(x, y)),\n    RuntimeShaderBuilder: _ => {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    },\n    RRectXY: (rect, rx, ry) => new _JsiSkRRect.JsiSkRRect(CanvasKit, rect, rx, ry),\n    RSXform: (scos, ssin, tx, ty) => new _JsiSkRSXform.JsiSkRSXform(CanvasKit, Float32Array.of(scos, ssin, tx, ty)),\n    RSXformFromRadians: (scale, r, tx, ty, px, py) => {\n      const s = Math.sin(r) * scale;\n      const c = Math.cos(r) * scale;\n      return new _JsiSkRSXform.JsiSkRSXform(CanvasKit, Float32Array.of(c, s, tx - c * px + s * py, ty - s * px - c * py));\n    },\n    Color: _JsiSkColor.Color,\n    ContourMeasureIter: (path, forceClosed, resScale) => new _JsiSkContourMeasureIter.JsiSkContourMeasureIter(CanvasKit, new CanvasKit.ContourMeasureIter(_JsiSkPath.JsiSkPath.fromValue(path), forceClosed, resScale)),\n    Paint: () => {\n      const paint = new _JsiSkPaint.JsiSkPaint(CanvasKit, new CanvasKit.Paint());\n      paint.setAntiAlias(true);\n      return paint;\n    },\n    PictureRecorder: () => new _JsiSkPictureRecorder.JsiSkPictureRecorder(CanvasKit, new CanvasKit.PictureRecorder()),\n    Picture: new _JsiSkPictureFactory.JsiSkPictureFactory(CanvasKit),\n    Path: new _JsiSkPathFactory.JsiSkPathFactory(CanvasKit),\n    Matrix: matrix => new _JsiSkMatrix.JsiSkMatrix(CanvasKit, matrix ? Float32Array.of(...matrix) : Float32Array.of(...CanvasKit.Matrix.identity())),\n    ColorFilter: new _JsiSkColorFilterFactory.JsiSkColorFilterFactory(CanvasKit),\n    Font: (typeface, size) => new _JsiSkFont.JsiSkFont(CanvasKit, new CanvasKit.Font(typeface === undefined ? null : _JsiSkTypeface.JsiSkTypeface.fromValue(typeface), size)),\n    Typeface: new _JsiSkTypefaceFactory.JsiSkTypefaceFactory(CanvasKit),\n    MaskFilter: new _JsiSkMaskFilterFactory.JsiSkMaskFilterFactory(CanvasKit),\n    RuntimeEffect: new _JsiSkRuntimeEffectFactory.JsiSkRuntimeEffectFactory(CanvasKit),\n    ImageFilter: new _JsiSkImageFilterFactory.JsiSkImageFilterFactory(CanvasKit),\n    Shader: new _JsiSkShaderFactory.JsiSkShaderFactory(CanvasKit),\n    PathEffect: new _JsiSkPathEffectFactory.JsiSkPathEffectFactory(CanvasKit),\n    MakeVertices: _JsiSkVerticesFactory.MakeVertices.bind(null, CanvasKit),\n    Data: new _JsiSkDataFactory.JsiSkDataFactory(CanvasKit),\n    Image: new _JsiSkImageFactory.JsiSkImageFactory(CanvasKit),\n    AnimatedImage: new _JsiSkAnimatedImageFactory.JsiSkAnimatedImageFactory(CanvasKit),\n    SVG: new _JsiSkSVGFactory.JsiSkSVGFactory(CanvasKit),\n    TextBlob: new _JsiSkTextBlobFactory.JsiSkTextBlobFactory(CanvasKit),\n    XYWHRect: (x, y, width, height) => {\n      return new _JsiSkRect.JsiSkRect(CanvasKit, CanvasKit.XYWHRect(x, y, width, height));\n    },\n    Surface: new _JsiSkSurfaceFactory.JsiSkSurfaceFactory(CanvasKit),\n    TypefaceFontProvider: new _JsiSkTypefaceFontProviderFactory.JsiSkTypefaceFontProviderFactory(CanvasKit),\n    FontMgr: new _JsiSkFontMgrFactory.JsiSkFontMgrFactory(CanvasKit),\n    ParagraphBuilder: new _JsiSkParagraphBuilderFactory.JsiSkParagraphBuilderFactory(CanvasKit),\n    NativeBuffer: new _JsiSkNativeBufferFactory.JsiSkNativeBufferFactory(CanvasKit),\n    Video: _JsiVideo.createVideo.bind(null, CanvasKit),\n    Context: (_surface, _width, _height) => {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    },\n    Recorder: () => {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n  });\n  exports.JsiSkApi = JsiSkApi;\n});", "lineCount": 94, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_JsiSkPoint"], [6, 17, 1, 0], [6, 20, 1, 0, "require"], [6, 27, 1, 0], [6, 28, 1, 0, "_dependencyMap"], [6, 42, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkPaint"], [7, 17, 2, 0], [7, 20, 2, 0, "require"], [7, 27, 2, 0], [7, 28, 2, 0, "_dependencyMap"], [7, 42, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkRect"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JsiSkColor"], [9, 17, 4, 0], [9, 20, 4, 0, "require"], [9, 27, 4, 0], [9, 28, 4, 0, "_dependencyMap"], [9, 42, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_JsiSkSurfaceFactory"], [10, 26, 5, 0], [10, 29, 5, 0, "require"], [10, 36, 5, 0], [10, 37, 5, 0, "_dependencyMap"], [10, 51, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_JsiSkRRect"], [11, 17, 6, 0], [11, 20, 6, 0, "require"], [11, 27, 6, 0], [11, 28, 6, 0, "_dependencyMap"], [11, 42, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_JsiSkRSXform"], [12, 19, 7, 0], [12, 22, 7, 0, "require"], [12, 29, 7, 0], [12, 30, 7, 0, "_dependencyMap"], [12, 44, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_JsiSkContourMeasureIter"], [13, 30, 8, 0], [13, 33, 8, 0, "require"], [13, 40, 8, 0], [13, 41, 8, 0, "_dependencyMap"], [13, 55, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_JsiSkPictureRecorder"], [14, 27, 9, 0], [14, 30, 9, 0, "require"], [14, 37, 9, 0], [14, 38, 9, 0, "_dependencyMap"], [14, 52, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_JsiSkPictureFactory"], [15, 26, 10, 0], [15, 29, 10, 0, "require"], [15, 36, 10, 0], [15, 37, 10, 0, "_dependencyMap"], [15, 51, 10, 0], [16, 2, 11, 0], [16, 6, 11, 0, "_JsiSkPathFactory"], [16, 23, 11, 0], [16, 26, 11, 0, "require"], [16, 33, 11, 0], [16, 34, 11, 0, "_dependencyMap"], [16, 48, 11, 0], [17, 2, 12, 0], [17, 6, 12, 0, "_JsiSkMatrix"], [17, 18, 12, 0], [17, 21, 12, 0, "require"], [17, 28, 12, 0], [17, 29, 12, 0, "_dependencyMap"], [17, 43, 12, 0], [18, 2, 13, 0], [18, 6, 13, 0, "_JsiSkColorFilterFactory"], [18, 30, 13, 0], [18, 33, 13, 0, "require"], [18, 40, 13, 0], [18, 41, 13, 0, "_dependencyMap"], [18, 55, 13, 0], [19, 2, 14, 0], [19, 6, 14, 0, "_JsiSkTypefaceFactory"], [19, 27, 14, 0], [19, 30, 14, 0, "require"], [19, 37, 14, 0], [19, 38, 14, 0, "_dependencyMap"], [19, 52, 14, 0], [20, 2, 15, 0], [20, 6, 15, 0, "_JsiSkMaskFilterFactory"], [20, 29, 15, 0], [20, 32, 15, 0, "require"], [20, 39, 15, 0], [20, 40, 15, 0, "_dependencyMap"], [20, 54, 15, 0], [21, 2, 16, 0], [21, 6, 16, 0, "_JsiSkRuntimeEffectFactory"], [21, 32, 16, 0], [21, 35, 16, 0, "require"], [21, 42, 16, 0], [21, 43, 16, 0, "_dependencyMap"], [21, 57, 16, 0], [22, 2, 17, 0], [22, 6, 17, 0, "_JsiSkImageFilterFactory"], [22, 30, 17, 0], [22, 33, 17, 0, "require"], [22, 40, 17, 0], [22, 41, 17, 0, "_dependencyMap"], [22, 55, 17, 0], [23, 2, 18, 0], [23, 6, 18, 0, "_JsiSkShaderFactory"], [23, 25, 18, 0], [23, 28, 18, 0, "require"], [23, 35, 18, 0], [23, 36, 18, 0, "_dependencyMap"], [23, 50, 18, 0], [24, 2, 19, 0], [24, 6, 19, 0, "_JsiSkPathEffectFactory"], [24, 29, 19, 0], [24, 32, 19, 0, "require"], [24, 39, 19, 0], [24, 40, 19, 0, "_dependencyMap"], [24, 54, 19, 0], [25, 2, 20, 0], [25, 6, 20, 0, "_JsiSkDataFactory"], [25, 23, 20, 0], [25, 26, 20, 0, "require"], [25, 33, 20, 0], [25, 34, 20, 0, "_dependencyMap"], [25, 48, 20, 0], [26, 2, 21, 0], [26, 6, 21, 0, "_JsiSkImageFactory"], [26, 24, 21, 0], [26, 27, 21, 0, "require"], [26, 34, 21, 0], [26, 35, 21, 0, "_dependencyMap"], [26, 49, 21, 0], [27, 2, 22, 0], [27, 6, 22, 0, "_JsiSkSVGFactory"], [27, 22, 22, 0], [27, 25, 22, 0, "require"], [27, 32, 22, 0], [27, 33, 22, 0, "_dependencyMap"], [27, 47, 22, 0], [28, 2, 23, 0], [28, 6, 23, 0, "_JsiSkTextBlobFactory"], [28, 27, 23, 0], [28, 30, 23, 0, "require"], [28, 37, 23, 0], [28, 38, 23, 0, "_dependencyMap"], [28, 52, 23, 0], [29, 2, 24, 0], [29, 6, 24, 0, "_JsiSkFont"], [29, 16, 24, 0], [29, 19, 24, 0, "require"], [29, 26, 24, 0], [29, 27, 24, 0, "_dependencyMap"], [29, 41, 24, 0], [30, 2, 25, 0], [30, 6, 25, 0, "_JsiSkVerticesFactory"], [30, 27, 25, 0], [30, 30, 25, 0, "require"], [30, 37, 25, 0], [30, 38, 25, 0, "_dependencyMap"], [30, 52, 25, 0], [31, 2, 26, 0], [31, 6, 26, 0, "_JsiSkPath"], [31, 16, 26, 0], [31, 19, 26, 0, "require"], [31, 26, 26, 0], [31, 27, 26, 0, "_dependencyMap"], [31, 41, 26, 0], [32, 2, 27, 0], [32, 6, 27, 0, "_JsiSkTypeface"], [32, 20, 27, 0], [32, 23, 27, 0, "require"], [32, 30, 27, 0], [32, 31, 27, 0, "_dependencyMap"], [32, 45, 27, 0], [33, 2, 28, 0], [33, 6, 28, 0, "_JsiSkTypefaceFontProviderFactory"], [33, 39, 28, 0], [33, 42, 28, 0, "require"], [33, 49, 28, 0], [33, 50, 28, 0, "_dependencyMap"], [33, 64, 28, 0], [34, 2, 29, 0], [34, 6, 29, 0, "_JsiSkFontMgrFactory"], [34, 26, 29, 0], [34, 29, 29, 0, "require"], [34, 36, 29, 0], [34, 37, 29, 0, "_dependencyMap"], [34, 51, 29, 0], [35, 2, 30, 0], [35, 6, 30, 0, "_JsiSkAnimatedImageFactory"], [35, 32, 30, 0], [35, 35, 30, 0, "require"], [35, 42, 30, 0], [35, 43, 30, 0, "_dependencyMap"], [35, 57, 30, 0], [36, 2, 31, 0], [36, 6, 31, 0, "_JsiSkParagraphBuilderFactory"], [36, 35, 31, 0], [36, 38, 31, 0, "require"], [36, 45, 31, 0], [36, 46, 31, 0, "_dependencyMap"], [36, 60, 31, 0], [37, 2, 32, 0], [37, 6, 32, 0, "_JsiSkNativeBufferFactory"], [37, 31, 32, 0], [37, 34, 32, 0, "require"], [37, 41, 32, 0], [37, 42, 32, 0, "_dependencyMap"], [37, 56, 32, 0], [38, 2, 33, 0], [38, 6, 33, 0, "_JsiVideo"], [38, 15, 33, 0], [38, 18, 33, 0, "require"], [38, 25, 33, 0], [38, 26, 33, 0, "_dependencyMap"], [38, 40, 33, 0], [39, 2, 34, 0], [39, 6, 34, 0, "_Host"], [39, 11, 34, 0], [39, 14, 34, 0, "require"], [39, 21, 34, 0], [39, 22, 34, 0, "_dependencyMap"], [39, 36, 34, 0], [40, 2, 35, 7], [40, 8, 35, 13, "JsiSkApi"], [40, 16, 35, 21], [40, 19, 35, 24, "CanvasKit"], [40, 28, 35, 33], [40, 33, 35, 38], [41, 4, 36, 2, "Point"], [41, 9, 36, 7], [41, 11, 36, 9, "Point"], [41, 12, 36, 10, "x"], [41, 13, 36, 11], [41, 15, 36, 13, "y"], [41, 16, 36, 14], [41, 21, 36, 19], [41, 25, 36, 23, "JsiSkPoint"], [41, 47, 36, 33], [41, 48, 36, 34, "CanvasKit"], [41, 57, 36, 43], [41, 59, 36, 45, "Float32Array"], [41, 71, 36, 57], [41, 72, 36, 58, "of"], [41, 74, 36, 60], [41, 75, 36, 61, "x"], [41, 76, 36, 62], [41, 78, 36, 64, "y"], [41, 79, 36, 65], [41, 80, 36, 66], [41, 81, 36, 67], [42, 4, 37, 2, "RuntimeShaderBuilder"], [42, 24, 37, 22], [42, 26, 37, 24, "_"], [42, 27, 37, 25], [42, 31, 37, 29], [43, 6, 38, 4], [43, 13, 38, 11], [43, 17, 38, 11, "throwNotImplementedOnRNWeb"], [43, 49, 38, 37], [43, 51, 38, 38], [43, 52, 38, 39], [44, 4, 39, 2], [44, 5, 39, 3], [45, 4, 40, 2, "RRectXY"], [45, 11, 40, 9], [45, 13, 40, 11, "RRectXY"], [45, 14, 40, 12, "rect"], [45, 18, 40, 16], [45, 20, 40, 18, "rx"], [45, 22, 40, 20], [45, 24, 40, 22, "ry"], [45, 26, 40, 24], [45, 31, 40, 29], [45, 35, 40, 33, "JsiSkRRect"], [45, 57, 40, 43], [45, 58, 40, 44, "CanvasKit"], [45, 67, 40, 53], [45, 69, 40, 55, "rect"], [45, 73, 40, 59], [45, 75, 40, 61, "rx"], [45, 77, 40, 63], [45, 79, 40, 65, "ry"], [45, 81, 40, 67], [45, 82, 40, 68], [46, 4, 41, 2, "RSXform"], [46, 11, 41, 9], [46, 13, 41, 11, "RSXform"], [46, 14, 41, 12, "scos"], [46, 18, 41, 16], [46, 20, 41, 18, "ssin"], [46, 24, 41, 22], [46, 26, 41, 24, "tx"], [46, 28, 41, 26], [46, 30, 41, 28, "ty"], [46, 32, 41, 30], [46, 37, 41, 35], [46, 41, 41, 39, "JsiSkRSXform"], [46, 67, 41, 51], [46, 68, 41, 52, "CanvasKit"], [46, 77, 41, 61], [46, 79, 41, 63, "Float32Array"], [46, 91, 41, 75], [46, 92, 41, 76, "of"], [46, 94, 41, 78], [46, 95, 41, 79, "scos"], [46, 99, 41, 83], [46, 101, 41, 85, "ssin"], [46, 105, 41, 89], [46, 107, 41, 91, "tx"], [46, 109, 41, 93], [46, 111, 41, 95, "ty"], [46, 113, 41, 97], [46, 114, 41, 98], [46, 115, 41, 99], [47, 4, 42, 2, "RSXformFromRadians"], [47, 22, 42, 20], [47, 24, 42, 22, "RSXformFromRadians"], [47, 25, 42, 23, "scale"], [47, 30, 42, 28], [47, 32, 42, 30, "r"], [47, 33, 42, 31], [47, 35, 42, 33, "tx"], [47, 37, 42, 35], [47, 39, 42, 37, "ty"], [47, 41, 42, 39], [47, 43, 42, 41, "px"], [47, 45, 42, 43], [47, 47, 42, 45, "py"], [47, 49, 42, 47], [47, 54, 42, 52], [48, 6, 43, 4], [48, 12, 43, 10, "s"], [48, 13, 43, 11], [48, 16, 43, 14, "Math"], [48, 20, 43, 18], [48, 21, 43, 19, "sin"], [48, 24, 43, 22], [48, 25, 43, 23, "r"], [48, 26, 43, 24], [48, 27, 43, 25], [48, 30, 43, 28, "scale"], [48, 35, 43, 33], [49, 6, 44, 4], [49, 12, 44, 10, "c"], [49, 13, 44, 11], [49, 16, 44, 14, "Math"], [49, 20, 44, 18], [49, 21, 44, 19, "cos"], [49, 24, 44, 22], [49, 25, 44, 23, "r"], [49, 26, 44, 24], [49, 27, 44, 25], [49, 30, 44, 28, "scale"], [49, 35, 44, 33], [50, 6, 45, 4], [50, 13, 45, 11], [50, 17, 45, 15, "JsiSkRSXform"], [50, 43, 45, 27], [50, 44, 45, 28, "CanvasKit"], [50, 53, 45, 37], [50, 55, 45, 39, "Float32Array"], [50, 67, 45, 51], [50, 68, 45, 52, "of"], [50, 70, 45, 54], [50, 71, 45, 55, "c"], [50, 72, 45, 56], [50, 74, 45, 58, "s"], [50, 75, 45, 59], [50, 77, 45, 61, "tx"], [50, 79, 45, 63], [50, 82, 45, 66, "c"], [50, 83, 45, 67], [50, 86, 45, 70, "px"], [50, 88, 45, 72], [50, 91, 45, 75, "s"], [50, 92, 45, 76], [50, 95, 45, 79, "py"], [50, 97, 45, 81], [50, 99, 45, 83, "ty"], [50, 101, 45, 85], [50, 104, 45, 88, "s"], [50, 105, 45, 89], [50, 108, 45, 92, "px"], [50, 110, 45, 94], [50, 113, 45, 97, "c"], [50, 114, 45, 98], [50, 117, 45, 101, "py"], [50, 119, 45, 103], [50, 120, 45, 104], [50, 121, 45, 105], [51, 4, 46, 2], [51, 5, 46, 3], [52, 4, 47, 2, "Color"], [52, 9, 47, 7], [52, 11, 47, 2, "Color"], [52, 28, 47, 7], [53, 4, 48, 2, "ContourMeasureIter"], [53, 22, 48, 20], [53, 24, 48, 22, "ContourMeasureIter"], [53, 25, 48, 23, "path"], [53, 29, 48, 27], [53, 31, 48, 29, "forceClosed"], [53, 42, 48, 40], [53, 44, 48, 42, "resScale"], [53, 52, 48, 50], [53, 57, 48, 55], [53, 61, 48, 59, "JsiSkContourMeasureIter"], [53, 109, 48, 82], [53, 110, 48, 83, "CanvasKit"], [53, 119, 48, 92], [53, 121, 48, 94], [53, 125, 48, 98, "CanvasKit"], [53, 134, 48, 107], [53, 135, 48, 108, "ContourMeasureIter"], [53, 153, 48, 126], [53, 154, 48, 127, "JsiSkPath"], [53, 174, 48, 136], [53, 175, 48, 137, "fromValue"], [53, 184, 48, 146], [53, 185, 48, 147, "path"], [53, 189, 48, 151], [53, 190, 48, 152], [53, 192, 48, 154, "forceClosed"], [53, 203, 48, 165], [53, 205, 48, 167, "resScale"], [53, 213, 48, 175], [53, 214, 48, 176], [53, 215, 48, 177], [54, 4, 49, 2, "Paint"], [54, 9, 49, 7], [54, 11, 49, 9, "Paint"], [54, 12, 49, 9], [54, 17, 49, 15], [55, 6, 50, 4], [55, 12, 50, 10, "paint"], [55, 17, 50, 15], [55, 20, 50, 18], [55, 24, 50, 22, "JsiSkPaint"], [55, 46, 50, 32], [55, 47, 50, 33, "CanvasKit"], [55, 56, 50, 42], [55, 58, 50, 44], [55, 62, 50, 48, "CanvasKit"], [55, 71, 50, 57], [55, 72, 50, 58, "Paint"], [55, 77, 50, 63], [55, 78, 50, 64], [55, 79, 50, 65], [55, 80, 50, 66], [56, 6, 51, 4, "paint"], [56, 11, 51, 9], [56, 12, 51, 10, "set<PERSON>nti<PERSON><PERSON><PERSON>"], [56, 24, 51, 22], [56, 25, 51, 23], [56, 29, 51, 27], [56, 30, 51, 28], [57, 6, 52, 4], [57, 13, 52, 11, "paint"], [57, 18, 52, 16], [58, 4, 53, 2], [58, 5, 53, 3], [59, 4, 54, 2, "PictureRecorder"], [59, 19, 54, 17], [59, 21, 54, 19, "PictureRecorder"], [59, 22, 54, 19], [59, 27, 54, 25], [59, 31, 54, 29, "JsiSkPictureRecorder"], [59, 73, 54, 49], [59, 74, 54, 50, "CanvasKit"], [59, 83, 54, 59], [59, 85, 54, 61], [59, 89, 54, 65, "CanvasKit"], [59, 98, 54, 74], [59, 99, 54, 75, "PictureRecorder"], [59, 114, 54, 90], [59, 115, 54, 91], [59, 116, 54, 92], [59, 117, 54, 93], [60, 4, 55, 2, "Picture"], [60, 11, 55, 9], [60, 13, 55, 11], [60, 17, 55, 15, "JsiSkPictureFactory"], [60, 57, 55, 34], [60, 58, 55, 35, "CanvasKit"], [60, 67, 55, 44], [60, 68, 55, 45], [61, 4, 56, 2, "Path"], [61, 8, 56, 6], [61, 10, 56, 8], [61, 14, 56, 12, "JsiSkPathFactory"], [61, 48, 56, 28], [61, 49, 56, 29, "CanvasKit"], [61, 58, 56, 38], [61, 59, 56, 39], [62, 4, 57, 2, "Matrix"], [62, 10, 57, 8], [62, 12, 57, 10, "matrix"], [62, 18, 57, 16], [62, 22, 57, 20], [62, 26, 57, 24, "JsiSkMatrix"], [62, 50, 57, 35], [62, 51, 57, 36, "CanvasKit"], [62, 60, 57, 45], [62, 62, 57, 47, "matrix"], [62, 68, 57, 53], [62, 71, 57, 56, "Float32Array"], [62, 83, 57, 68], [62, 84, 57, 69, "of"], [62, 86, 57, 71], [62, 87, 57, 72], [62, 90, 57, 75, "matrix"], [62, 96, 57, 81], [62, 97, 57, 82], [62, 100, 57, 85, "Float32Array"], [62, 112, 57, 97], [62, 113, 57, 98, "of"], [62, 115, 57, 100], [62, 116, 57, 101], [62, 119, 57, 104, "CanvasKit"], [62, 128, 57, 113], [62, 129, 57, 114, "Matrix"], [62, 135, 57, 120], [62, 136, 57, 121, "identity"], [62, 144, 57, 129], [62, 145, 57, 130], [62, 146, 57, 131], [62, 147, 57, 132], [62, 148, 57, 133], [63, 4, 58, 2, "ColorFilter"], [63, 15, 58, 13], [63, 17, 58, 15], [63, 21, 58, 19, "JsiSkColorFilterFactory"], [63, 69, 58, 42], [63, 70, 58, 43, "CanvasKit"], [63, 79, 58, 52], [63, 80, 58, 53], [64, 4, 59, 2, "Font"], [64, 8, 59, 6], [64, 10, 59, 8, "Font"], [64, 11, 59, 9, "typeface"], [64, 19, 59, 17], [64, 21, 59, 19, "size"], [64, 25, 59, 23], [64, 30, 59, 28], [64, 34, 59, 32, "JsiSkFont"], [64, 54, 59, 41], [64, 55, 59, 42, "CanvasKit"], [64, 64, 59, 51], [64, 66, 59, 53], [64, 70, 59, 57, "CanvasKit"], [64, 79, 59, 66], [64, 80, 59, 67, "Font"], [64, 84, 59, 71], [64, 85, 59, 72, "typeface"], [64, 93, 59, 80], [64, 98, 59, 85, "undefined"], [64, 107, 59, 94], [64, 110, 59, 97], [64, 114, 59, 101], [64, 117, 59, 104, "JsiSkTypeface"], [64, 145, 59, 117], [64, 146, 59, 118, "fromValue"], [64, 155, 59, 127], [64, 156, 59, 128, "typeface"], [64, 164, 59, 136], [64, 165, 59, 137], [64, 167, 59, 139, "size"], [64, 171, 59, 143], [64, 172, 59, 144], [64, 173, 59, 145], [65, 4, 60, 2, "Typeface"], [65, 12, 60, 10], [65, 14, 60, 12], [65, 18, 60, 16, "JsiSkTypefaceFactory"], [65, 60, 60, 36], [65, 61, 60, 37, "CanvasKit"], [65, 70, 60, 46], [65, 71, 60, 47], [66, 4, 61, 2, "<PERSON><PERSON><PERSON><PERSON>"], [66, 14, 61, 12], [66, 16, 61, 14], [66, 20, 61, 18, "JsiSkMaskFilterFactory"], [66, 66, 61, 40], [66, 67, 61, 41, "CanvasKit"], [66, 76, 61, 50], [66, 77, 61, 51], [67, 4, 62, 2, "RuntimeEffect"], [67, 17, 62, 15], [67, 19, 62, 17], [67, 23, 62, 21, "JsiSkRuntimeEffectFactory"], [67, 75, 62, 46], [67, 76, 62, 47, "CanvasKit"], [67, 85, 62, 56], [67, 86, 62, 57], [68, 4, 63, 2, "ImageFilter"], [68, 15, 63, 13], [68, 17, 63, 15], [68, 21, 63, 19, "JsiSkImageFilterFactory"], [68, 69, 63, 42], [68, 70, 63, 43, "CanvasKit"], [68, 79, 63, 52], [68, 80, 63, 53], [69, 4, 64, 2, "Shader"], [69, 10, 64, 8], [69, 12, 64, 10], [69, 16, 64, 14, "JsiSkShaderFactory"], [69, 54, 64, 32], [69, 55, 64, 33, "CanvasKit"], [69, 64, 64, 42], [69, 65, 64, 43], [70, 4, 65, 2, "PathEffect"], [70, 14, 65, 12], [70, 16, 65, 14], [70, 20, 65, 18, "JsiSkPathEffectFactory"], [70, 66, 65, 40], [70, 67, 65, 41, "CanvasKit"], [70, 76, 65, 50], [70, 77, 65, 51], [71, 4, 66, 2, "MakeVertices"], [71, 16, 66, 14], [71, 18, 66, 16, "MakeVertices"], [71, 52, 66, 28], [71, 53, 66, 29, "bind"], [71, 57, 66, 33], [71, 58, 66, 34], [71, 62, 66, 38], [71, 64, 66, 40, "CanvasKit"], [71, 73, 66, 49], [71, 74, 66, 50], [72, 4, 67, 2, "Data"], [72, 8, 67, 6], [72, 10, 67, 8], [72, 14, 67, 12, "JsiSkDataFactory"], [72, 48, 67, 28], [72, 49, 67, 29, "CanvasKit"], [72, 58, 67, 38], [72, 59, 67, 39], [73, 4, 68, 2, "Image"], [73, 9, 68, 7], [73, 11, 68, 9], [73, 15, 68, 13, "JsiSkImageFactory"], [73, 51, 68, 30], [73, 52, 68, 31, "CanvasKit"], [73, 61, 68, 40], [73, 62, 68, 41], [74, 4, 69, 2, "AnimatedImage"], [74, 17, 69, 15], [74, 19, 69, 17], [74, 23, 69, 21, "JsiSkAnimatedImageFactory"], [74, 75, 69, 46], [74, 76, 69, 47, "CanvasKit"], [74, 85, 69, 56], [74, 86, 69, 57], [75, 4, 70, 2, "SVG"], [75, 7, 70, 5], [75, 9, 70, 7], [75, 13, 70, 11, "JsiSkSVGFactory"], [75, 45, 70, 26], [75, 46, 70, 27, "CanvasKit"], [75, 55, 70, 36], [75, 56, 70, 37], [76, 4, 71, 2, "TextBlob"], [76, 12, 71, 10], [76, 14, 71, 12], [76, 18, 71, 16, "JsiSkTextBlobFactory"], [76, 60, 71, 36], [76, 61, 71, 37, "CanvasKit"], [76, 70, 71, 46], [76, 71, 71, 47], [77, 4, 72, 2, "XYWHRect"], [77, 12, 72, 10], [77, 14, 72, 12, "XYWHRect"], [77, 15, 72, 13, "x"], [77, 16, 72, 14], [77, 18, 72, 16, "y"], [77, 19, 72, 17], [77, 21, 72, 19, "width"], [77, 26, 72, 24], [77, 28, 72, 26, "height"], [77, 34, 72, 32], [77, 39, 72, 37], [78, 6, 73, 4], [78, 13, 73, 11], [78, 17, 73, 15, "JsiSkRect"], [78, 37, 73, 24], [78, 38, 73, 25, "CanvasKit"], [78, 47, 73, 34], [78, 49, 73, 36, "CanvasKit"], [78, 58, 73, 45], [78, 59, 73, 46, "XYWHRect"], [78, 67, 73, 54], [78, 68, 73, 55, "x"], [78, 69, 73, 56], [78, 71, 73, 58, "y"], [78, 72, 73, 59], [78, 74, 73, 61, "width"], [78, 79, 73, 66], [78, 81, 73, 68, "height"], [78, 87, 73, 74], [78, 88, 73, 75], [78, 89, 73, 76], [79, 4, 74, 2], [79, 5, 74, 3], [80, 4, 75, 2, "Surface"], [80, 11, 75, 9], [80, 13, 75, 11], [80, 17, 75, 15, "JsiSkSurfaceFactory"], [80, 57, 75, 34], [80, 58, 75, 35, "CanvasKit"], [80, 67, 75, 44], [80, 68, 75, 45], [81, 4, 76, 2, "TypefaceFontProvider"], [81, 24, 76, 22], [81, 26, 76, 24], [81, 30, 76, 28, "JsiSkTypefaceFontProviderFactory"], [81, 96, 76, 60], [81, 97, 76, 61, "CanvasKit"], [81, 106, 76, 70], [81, 107, 76, 71], [82, 4, 77, 2, "FontMgr"], [82, 11, 77, 9], [82, 13, 77, 11], [82, 17, 77, 15, "JsiSkFontMgrFactory"], [82, 57, 77, 34], [82, 58, 77, 35, "CanvasKit"], [82, 67, 77, 44], [82, 68, 77, 45], [83, 4, 78, 2, "ParagraphBuilder"], [83, 20, 78, 18], [83, 22, 78, 20], [83, 26, 78, 24, "JsiSkParagraphBuilderFactory"], [83, 84, 78, 52], [83, 85, 78, 53, "CanvasKit"], [83, 94, 78, 62], [83, 95, 78, 63], [84, 4, 79, 2, "<PERSON><PERSON><PERSON><PERSON>"], [84, 16, 79, 14], [84, 18, 79, 16], [84, 22, 79, 20, "JsiSkNativeBufferFactory"], [84, 72, 79, 44], [84, 73, 79, 45, "CanvasKit"], [84, 82, 79, 54], [84, 83, 79, 55], [85, 4, 80, 2, "Video"], [85, 9, 80, 7], [85, 11, 80, 9, "createVideo"], [85, 32, 80, 20], [85, 33, 80, 21, "bind"], [85, 37, 80, 25], [85, 38, 80, 26], [85, 42, 80, 30], [85, 44, 80, 32, "CanvasKit"], [85, 53, 80, 41], [85, 54, 80, 42], [86, 4, 81, 2, "Context"], [86, 11, 81, 9], [86, 13, 81, 11, "Context"], [86, 14, 81, 12, "_surface"], [86, 22, 81, 20], [86, 24, 81, 22, "_width"], [86, 30, 81, 28], [86, 32, 81, 30, "_height"], [86, 39, 81, 37], [86, 44, 81, 42], [87, 6, 82, 4], [87, 13, 82, 11], [87, 17, 82, 11, "throwNotImplementedOnRNWeb"], [87, 49, 82, 37], [87, 51, 82, 38], [87, 52, 82, 39], [88, 4, 83, 2], [88, 5, 83, 3], [89, 4, 84, 2, "Recorder"], [89, 12, 84, 10], [89, 14, 84, 12, "Recorder"], [89, 15, 84, 12], [89, 20, 84, 18], [90, 6, 85, 4], [90, 13, 85, 11], [90, 17, 85, 11, "throwNotImplementedOnRNWeb"], [90, 49, 85, 37], [90, 51, 85, 38], [90, 52, 85, 39], [91, 4, 86, 2], [92, 2, 87, 0], [92, 3, 87, 1], [92, 4, 87, 2], [93, 2, 87, 3, "exports"], [93, 9, 87, 3], [93, 10, 87, 3, "JsiSkApi"], [93, 18, 87, 3], [93, 21, 87, 3, "JsiSkApi"], [93, 29, 87, 3], [94, 0, 87, 3], [94, 3]], "functionMap": {"names": ["<global>", "JsiSkApi", "Point", "RuntimeShaderBuilder", "RRectXY", "RSXform", "RSXformFromRadians", "ContourMeasureIter", "Paint", "PictureRecorder", "Matrix", "Font", "XYWHRect", "Context", "Recorder"], "mappings": "AAA;wBCkC;SCC,0DD;wBEC;GFE;WGC,yDH;WIC,wFJ;sBKC;GLI;sBME,2JN;SOC;GPI;mBQC,0ER;USG,2HT;QUE,yIV;YWa;GXE;WYO;GZE;YaC;GbE;EDC"}}, "type": "js/module"}]}