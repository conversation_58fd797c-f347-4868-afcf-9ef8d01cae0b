{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 39, "index": 39}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkColorFilter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 54, "index": 94}}], "key": "zQChm2irMCKhUBxvb1hmNVoer2A=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkColorFilterFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkColorFilter = require(_dependencyMap[1], \"./JsiSkColorFilter\");\n  class JsiSkColorFilterFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeMatrix(cMatrix) {\n      return new _JsiSkColorFilter.JsiSkColorFilter(this.CanvasKit, this.CanvasKit.ColorFilter.MakeMatrix(cMatrix));\n    }\n    MakeBlend(color, mode) {\n      return new _JsiSkColorFilter.JsiSkColorFilter(this.CanvasKit, this.CanvasKit.ColorFilter.MakeBlend(color, (0, _Host.getEnum)(this.CanvasKit, \"BlendMode\", mode)));\n    }\n    MakeCompose(outer, inner) {\n      return new _JsiSkColorFilter.JsiSkColorFilter(this.CanvasKit, this.CanvasKit.ColorFilter.MakeCompose(_JsiSkColorFilter.JsiSkColorFilter.fromValue(outer), _JsiSkColorFilter.JsiSkColorFilter.fromValue(inner)));\n    }\n    MakeLerp(t, dst, src) {\n      return new _JsiSkColorFilter.JsiSkColorFilter(this.CanvasKit, this.CanvasKit.ColorFilter.MakeLerp(t, _JsiSkColorFilter.JsiSkColorFilter.fromValue(dst), _JsiSkColorFilter.JsiSkColorFilter.fromValue(src)));\n    }\n    MakeLinearToSRGBGamma() {\n      return new _JsiSkColorFilter.JsiSkColorFilter(this.CanvasKit, this.CanvasKit.ColorFilter.MakeLinearToSRGBGamma());\n    }\n    MakeSRGBToLinearGamma() {\n      return new _JsiSkColorFilter.JsiSkColorFilter(this.CanvasKit, this.CanvasKit.ColorFilter.MakeSRGBToLinearGamma());\n    }\n    MakeLumaColorFilter() {\n      return new _JsiSkColorFilter.JsiSkColorFilter(this.CanvasKit, this.CanvasKit.ColorFilter.MakeLuma());\n    }\n  }\n  exports.JsiSkColorFilterFactory = JsiSkColorFilterFactory;\n});", "lineCount": 35, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkColorFilter"], [7, 23, 2, 0], [7, 26, 2, 0, "require"], [7, 33, 2, 0], [7, 34, 2, 0, "_dependencyMap"], [7, 48, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkColorFilterFactory"], [8, 31, 3, 36], [8, 40, 3, 45, "Host"], [8, 50, 3, 49], [8, 51, 3, 50], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "MakeMatrix"], [12, 14, 7, 12, "MakeMatrix"], [12, 15, 7, 13, "cMatrix"], [12, 22, 7, 20], [12, 24, 7, 22], [13, 6, 8, 4], [13, 13, 8, 11], [13, 17, 8, 15, "JsiSkColorFilter"], [13, 51, 8, 31], [13, 52, 8, 32], [13, 56, 8, 36], [13, 57, 8, 37, "CanvasKit"], [13, 66, 8, 46], [13, 68, 8, 48], [13, 72, 8, 52], [13, 73, 8, 53, "CanvasKit"], [13, 82, 8, 62], [13, 83, 8, 63, "ColorFilter"], [13, 94, 8, 74], [13, 95, 8, 75, "MakeMatrix"], [13, 105, 8, 85], [13, 106, 8, 86, "cMatrix"], [13, 113, 8, 93], [13, 114, 8, 94], [13, 115, 8, 95], [14, 4, 9, 2], [15, 4, 10, 2, "MakeBlend"], [15, 13, 10, 11, "MakeBlend"], [15, 14, 10, 12, "color"], [15, 19, 10, 17], [15, 21, 10, 19, "mode"], [15, 25, 10, 23], [15, 27, 10, 25], [16, 6, 11, 4], [16, 13, 11, 11], [16, 17, 11, 15, "JsiSkColorFilter"], [16, 51, 11, 31], [16, 52, 11, 32], [16, 56, 11, 36], [16, 57, 11, 37, "CanvasKit"], [16, 66, 11, 46], [16, 68, 11, 48], [16, 72, 11, 52], [16, 73, 11, 53, "CanvasKit"], [16, 82, 11, 62], [16, 83, 11, 63, "ColorFilter"], [16, 94, 11, 74], [16, 95, 11, 75, "MakeBlend"], [16, 104, 11, 84], [16, 105, 11, 85, "color"], [16, 110, 11, 90], [16, 112, 11, 92], [16, 116, 11, 92, "getEnum"], [16, 129, 11, 99], [16, 131, 11, 100], [16, 135, 11, 104], [16, 136, 11, 105, "CanvasKit"], [16, 145, 11, 114], [16, 147, 11, 116], [16, 158, 11, 127], [16, 160, 11, 129, "mode"], [16, 164, 11, 133], [16, 165, 11, 134], [16, 166, 11, 135], [16, 167, 11, 136], [17, 4, 12, 2], [18, 4, 13, 2, "MakeCompose"], [18, 15, 13, 13, "MakeCompose"], [18, 16, 13, 14, "outer"], [18, 21, 13, 19], [18, 23, 13, 21, "inner"], [18, 28, 13, 26], [18, 30, 13, 28], [19, 6, 14, 4], [19, 13, 14, 11], [19, 17, 14, 15, "JsiSkColorFilter"], [19, 51, 14, 31], [19, 52, 14, 32], [19, 56, 14, 36], [19, 57, 14, 37, "CanvasKit"], [19, 66, 14, 46], [19, 68, 14, 48], [19, 72, 14, 52], [19, 73, 14, 53, "CanvasKit"], [19, 82, 14, 62], [19, 83, 14, 63, "ColorFilter"], [19, 94, 14, 74], [19, 95, 14, 75, "MakeCompose"], [19, 106, 14, 86], [19, 107, 14, 87, "JsiSkColorFilter"], [19, 141, 14, 103], [19, 142, 14, 104, "fromValue"], [19, 151, 14, 113], [19, 152, 14, 114, "outer"], [19, 157, 14, 119], [19, 158, 14, 120], [19, 160, 14, 122, "JsiSkColorFilter"], [19, 194, 14, 138], [19, 195, 14, 139, "fromValue"], [19, 204, 14, 148], [19, 205, 14, 149, "inner"], [19, 210, 14, 154], [19, 211, 14, 155], [19, 212, 14, 156], [19, 213, 14, 157], [20, 4, 15, 2], [21, 4, 16, 2, "MakeLerp"], [21, 12, 16, 10, "MakeLerp"], [21, 13, 16, 11, "t"], [21, 14, 16, 12], [21, 16, 16, 14, "dst"], [21, 19, 16, 17], [21, 21, 16, 19, "src"], [21, 24, 16, 22], [21, 26, 16, 24], [22, 6, 17, 4], [22, 13, 17, 11], [22, 17, 17, 15, "JsiSkColorFilter"], [22, 51, 17, 31], [22, 52, 17, 32], [22, 56, 17, 36], [22, 57, 17, 37, "CanvasKit"], [22, 66, 17, 46], [22, 68, 17, 48], [22, 72, 17, 52], [22, 73, 17, 53, "CanvasKit"], [22, 82, 17, 62], [22, 83, 17, 63, "ColorFilter"], [22, 94, 17, 74], [22, 95, 17, 75, "MakeLerp"], [22, 103, 17, 83], [22, 104, 17, 84, "t"], [22, 105, 17, 85], [22, 107, 17, 87, "JsiSkColorFilter"], [22, 141, 17, 103], [22, 142, 17, 104, "fromValue"], [22, 151, 17, 113], [22, 152, 17, 114, "dst"], [22, 155, 17, 117], [22, 156, 17, 118], [22, 158, 17, 120, "JsiSkColorFilter"], [22, 192, 17, 136], [22, 193, 17, 137, "fromValue"], [22, 202, 17, 146], [22, 203, 17, 147, "src"], [22, 206, 17, 150], [22, 207, 17, 151], [22, 208, 17, 152], [22, 209, 17, 153], [23, 4, 18, 2], [24, 4, 19, 2, "MakeLinearToSRGBGamma"], [24, 25, 19, 23, "MakeLinearToSRGBGamma"], [24, 26, 19, 23], [24, 28, 19, 26], [25, 6, 20, 4], [25, 13, 20, 11], [25, 17, 20, 15, "JsiSkColorFilter"], [25, 51, 20, 31], [25, 52, 20, 32], [25, 56, 20, 36], [25, 57, 20, 37, "CanvasKit"], [25, 66, 20, 46], [25, 68, 20, 48], [25, 72, 20, 52], [25, 73, 20, 53, "CanvasKit"], [25, 82, 20, 62], [25, 83, 20, 63, "ColorFilter"], [25, 94, 20, 74], [25, 95, 20, 75, "MakeLinearToSRGBGamma"], [25, 116, 20, 96], [25, 117, 20, 97], [25, 118, 20, 98], [25, 119, 20, 99], [26, 4, 21, 2], [27, 4, 22, 2, "MakeSRGBToLinearGamma"], [27, 25, 22, 23, "MakeSRGBToLinearGamma"], [27, 26, 22, 23], [27, 28, 22, 26], [28, 6, 23, 4], [28, 13, 23, 11], [28, 17, 23, 15, "JsiSkColorFilter"], [28, 51, 23, 31], [28, 52, 23, 32], [28, 56, 23, 36], [28, 57, 23, 37, "CanvasKit"], [28, 66, 23, 46], [28, 68, 23, 48], [28, 72, 23, 52], [28, 73, 23, 53, "CanvasKit"], [28, 82, 23, 62], [28, 83, 23, 63, "ColorFilter"], [28, 94, 23, 74], [28, 95, 23, 75, "MakeSRGBToLinearGamma"], [28, 116, 23, 96], [28, 117, 23, 97], [28, 118, 23, 98], [28, 119, 23, 99], [29, 4, 24, 2], [30, 4, 25, 2, "MakeLumaColorFilter"], [30, 23, 25, 21, "MakeLumaColorFilter"], [30, 24, 25, 21], [30, 26, 25, 24], [31, 6, 26, 4], [31, 13, 26, 11], [31, 17, 26, 15, "JsiSkColorFilter"], [31, 51, 26, 31], [31, 52, 26, 32], [31, 56, 26, 36], [31, 57, 26, 37, "CanvasKit"], [31, 66, 26, 46], [31, 68, 26, 48], [31, 72, 26, 52], [31, 73, 26, 53, "CanvasKit"], [31, 82, 26, 62], [31, 83, 26, 63, "ColorFilter"], [31, 94, 26, 74], [31, 95, 26, 75, "MakeLuma"], [31, 103, 26, 83], [31, 104, 26, 84], [31, 105, 26, 85], [31, 106, 26, 86], [32, 4, 27, 2], [33, 2, 28, 0], [34, 2, 28, 1, "exports"], [34, 9, 28, 1], [34, 10, 28, 1, "JsiSkColorFilterFactory"], [34, 33, 28, 1], [34, 36, 28, 1, "JsiSkColorFilterFactory"], [34, 59, 28, 1], [35, 0, 28, 1], [35, 3]], "functionMap": {"names": ["<global>", "JsiSkColorFilterFactory", "constructor", "MakeMatrix", "MakeBlend", "MakeCompose", "MakeLerp", "MakeLinearToSRGBGamma", "MakeSRGBToLinearGamma", "MakeLumaColorFilter"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFE;EGC;GHE;EIC;GJE;EKC;GLE;EMC;GNE;EOC;GPE;EQC;GRE;CDC"}}, "type": "js/module"}]}