{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 73, "index": 73}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TextInput", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "DmXc1F5dPYWntVgqRwh73w0VngA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 53, "column": 0, "index": 320}, "end": {"line": 53, "column": 67, "index": 387}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "expo-status-bar", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 57, "column": 0, "index": 395}, "end": {"line": 57, "column": 44, "index": 439}}], "key": "tlkgvZrxUMG8C7vDDJbsBGIlvhs=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 61, "column": 0, "index": 447}, "end": {"line": 61, "column": 59, "index": 506}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 65, "column": 0, "index": 514}, "end": {"line": 113, "column": 29, "index": 774}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-location", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 117, "column": 0, "index": 782}, "end": {"line": 117, "column": 42, "index": 824}}], "key": "GNP7AGCKsBRUhlnTZ4lIPpbkT9E=", "exportNames": ["*"]}}, {"name": "@/components/EchoCameraUnified", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 121, "column": 0, "index": 832}, "end": {"line": 121, "column": 63, "index": 895}}], "key": "wA7DUOcNir1rKeEVRofkWVobA04=", "exportNames": ["*"]}}, {"name": "@/components/camera/EchoCameraWeb", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 125, "column": 0, "index": 903}, "end": {"line": 125, "column": 62, "index": 965}}], "key": "a5L7e3cPb+NheECyYdST63CXrdc=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 129, "column": 0, "index": 973}, "end": {"line": 129, "column": 85, "index": 1058}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = RespondScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/ScrollView\"));\n  var _TextInput = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/TextInput\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Modal\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/Platform\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Image\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[11], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[12], \"react-native-safe-area-context\");\n  var _expoStatusBar = require(_dependencyMap[13], \"expo-status-bar\");\n  var _expoRouter = require(_dependencyMap[14], \"expo-router\");\n  var _lucideReactNative = require(_dependencyMap[15], \"lucide-react-native\");\n  var Location = _interopRequireWildcard(require(_dependencyMap[16], \"expo-location\"));\n  var _EchoCameraUnified = _interopRequireDefault(require(_dependencyMap[17], \"@/components/EchoCameraUnified\"));\n  var _EchoCameraWeb = _interopRequireDefault(require(_dependencyMap[18], \"@/components/camera/EchoCameraWeb\"));\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[19], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _jsxDevRuntime = require(_dependencyMap[20], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\app\\\\respond\\\\[id].jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function RespondScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const {\n      id\n    } = (0, _expoRouter.useLocalSearchParams)();\n    const [response, setResponse] = (0, _react.useState)(\"\");\n    const [showCamera, setShowCamera] = (0, _react.useState)(false);\n    const [cameraResult, setCameraResult] = (0, _react.useState)(null);\n    const [submitting, setSubmitting] = (0, _react.useState)(false);\n    const [capturedPhotoUri, setCapturedPhotoUri] = (0, _react.useState)(null);\n    const defaultTestingMode = (0, _react.useMemo)(() => {\n      if (_Platform.default.OS !== \"web\" || typeof window === \"undefined\") {\n        return false;\n      }\n      const {\n        protocol,\n        hostname\n      } = window.location;\n      const localHosts = [\"localhost\", \"127.0.0.1\", \"::1\"];\n      return protocol !== \"https:\" || localHosts.includes(hostname);\n    }, []);\n    const [testingMode, setTestingMode] = (0, _react.useState)(defaultTestingMode);\n\n    // Location verification state\n\n    const [locationStatus, setLocationStatus] = (0, _react.useState)(\"checking\"); // 'checking', 'verified', 'too_far', 'error'\n\n    const [currentLocation, setCurrentLocation] = (0, _react.useState)(null);\n    const [distance, setDistance] = (0, _react.useState)(null);\n    const [gettingLocation, setGettingLocation] = (0, _react.useState)(false);\n    const [locationError, setLocationError] = (0, _react.useState)(null);\n\n    // Mock question data - in real app, fetch based on id\n\n    const question = {\n      id: id,\n      question: \"Is the coffee shop on Main Street currently open? I need to know if they have seating available.\",\n      location: \"123 Main Street, Downtown\",\n      coordinates: {\n        // FOR TESTING: Updated to Amadora, Portugal coordinates for testing\n\n        latitude: 38.7555,\n        // Amadora, Portugal\n\n        longitude: -9.2337\n      },\n      reward: 2.5,\n      postedAt: \"2 hours ago\",\n      userId: \"user123\"\n    };\n    const questionLatitude = question.coordinates.latitude;\n    const questionLongitude = question.coordinates.longitude;\n\n    // Calculate distance between two coordinates in meters\n\n    const calculateDistance = (lat1, lon1, lat2, lon2) => {\n      const R = 6371e3; // Earth's radius in meters\n\n      const lat1Rad = lat1 * Math.PI / 180;\n      const lat2Rad = lat2 * Math.PI / 180;\n      const deltaLat = (lat2 - lat1) * Math.PI / 180;\n      const deltaLon = (lon2 - lon1) * Math.PI / 180;\n      const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);\n      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n      return R * c; // Distance in meters\n    };\n\n    // Verify user location\n\n    const verifyLocation = (0, _react.useCallback)(async () => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      try {\n        setGettingLocation(true);\n        setLocationError(null);\n        setLocationStatus(\"checking\");\n\n        // Request location permission\n\n        const {\n          status\n        } = await Location.requestForegroundPermissionsAsync();\n        if (status !== \"granted\") {\n          const message = _Platform.default.OS === \"web\" ? \"Allow location access in your browser settings or enable Testing Mode to continue without verification.\" : \"We need your location to verify you're at the question location.\";\n          setLocationError(message);\n          setLocationStatus(\"error\");\n          _Alert.default.alert(\"Location Required\", message);\n          return;\n        }\n\n        // Get current location\n\n        const locationData = await Location.getCurrentPositionAsync({\n          accuracy: Location.Accuracy.High,\n          timeout: 15000,\n          maximumAge: 60000\n        });\n        const userLat = locationData.coords.latitude;\n        const userLon = locationData.coords.longitude;\n        const questionLat = questionLatitude;\n        const questionLon = questionLongitude;\n\n        // Calculate distance\n\n        const distanceInMeters = calculateDistance(userLat, userLon, questionLat, questionLon);\n        setDistance(Math.round(distanceInMeters));\n        setCurrentLocation({\n          latitude: userLat,\n          longitude: userLon\n        });\n\n        // Check if user is within acceptable range (200 meters)\n\n        const maxDistance = 200;\n        if (distanceInMeters <= maxDistance) {\n          setLocationStatus(\"verified\");\n        } else {\n          setLocationStatus(\"too_far\");\n        }\n      } catch (error) {\n        console.error(\"Error verifying location:\", error);\n        let message = \"Could not verify your location. Please check your GPS and try again.\";\n        if (error?.code === 1) {\n          message = \"Location permission was denied. Enable access in your device or browser settings, or turn on Testing Mode.\";\n        } else if (error?.code === 2) {\n          message = \"We couldn't determine your position. Try moving to an open area or toggling airplane mode.\";\n        } else if (error?.code === 3) {\n          message = \"Location request timed out. Please try again.\";\n        } else if (_Platform.default.OS === \"web\" && typeof error?.message === \"string\" && error.message.toLowerCase().includes(\"secure\")) {\n          message = \"The browser blocked location services on this connection. Use https:// or enable Testing Mode for manual capture.\";\n        }\n        setLocationError(message);\n        setLocationStatus(\"error\");\n        _Alert.default.alert(\"Location Error\", message);\n      } finally {\n        setGettingLocation(false);\n      }\n    }, [questionLatitude, questionLongitude, testingMode]);\n\n    // Verify location on mount or when testing mode changes\n\n    (0, _react.useEffect)(() => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      verifyLocation();\n    }, [testingMode, verifyLocation]);\n    const handleStartCamera = () => {\n      console.log(\"Camera button pressed:\", {\n        locationStatus,\n        testingMode,\n        disabled: locationStatus !== \"verified\" && !testingMode,\n        shouldEnable: locationStatus === \"verified\" || testingMode,\n        existingCameraResult: cameraResult // Log existing result\n      });\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", locationStatus === \"too_far\" ? `You are ${distance || 0}m away from the question location. You need to be within 200m to respond.` : \"Please verify your location first.\");\n        return;\n      }\n      setShowCamera(true);\n    };\n    const handleCameraComplete = (0, _react.useCallback)(result => {\n      console.log('Camera result received:', result); // Debug log\n      console.log('🔍 DEBUGGING: Camera result properties:', {\n        imageUrl: result.imageUrl,\n        localUri: result.localUri,\n        uri: result.uri,\n        publicUrl: result.publicUrl,\n        timestamp: result.timestamp\n      });\n\n      // Extract the URI from various possible sources\n\n      let imageUri = result.imageUrl || result.localUri || result.uri || result.publicUrl;\n      console.log('🔍 DEBUGGING: Selected imageUri:', imageUri);\n\n      // Handle data URIs that might be malformed\n\n      if (imageUri && imageUri.startsWith('data:image')) {\n        // Ensure data URI is properly formatted\n\n        if (!imageUri.includes('base64,')) {\n          console.error('Invalid data URI format:', imageUri.substring(0, 50));\n          imageUri = null;\n        }\n      }\n\n      // For development, use a placeholder if no valid URI\n\n      if (!imageUri && __DEV__) {\n        console.warn('No valid image URI, using placeholder');\n        imageUri = 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Photo+Captured';\n      }\n      setCapturedPhotoUri(imageUri);\n\n      // 🔍 DEBUGGING: Test if blob URL is accessible\n      if (imageUri && imageUri.startsWith('blob:')) {\n        console.log('🔍 DEBUGGING: Testing blob URL accessibility...');\n        fetch(imageUri).then(response => {\n          console.log('🔍 DEBUGGING: Blob URL fetch successful:', response.ok, response.status);\n          return response.blob();\n        }).then(blob => {\n          console.log('🔍 DEBUGGING: Blob size:', blob.size, 'bytes, type:', blob.type);\n        }).catch(error => {\n          console.error('🔍 DEBUGGING: Blob URL fetch failed:', error);\n        });\n      }\n\n      // Normalize the result to ensure we have the correct URI property\n\n      const normalizedResult = {\n        ...result,\n        imageUrl: imageUri,\n        localUri: imageUri,\n        // Store original URI for debugging\n\n        originalUri: result.imageUrl || result.localUri || result.uri || result.publicUrl\n      };\n      console.log('Normalized result with URI:', imageUri);\n      console.log('Full normalized result:', normalizedResult);\n      setCameraResult(normalizedResult);\n      setShowCamera(false);\n\n      // Removed redundant Alert - the UI will show the success state with image\n    }, []);\n    const handleCameraCancel = (0, _react.useCallback)(() => {\n      setShowCamera(false);\n      setCameraResult(null);\n      setCapturedPhotoUri(null);\n    }, []);\n    const submitResponse = async () => {\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", \"Please verify your location first.\");\n        return;\n      }\n      if (!cameraResult) {\n        _Alert.default.alert(\"Missing Photo\", \"Please take the required photo first.\");\n        return;\n      }\n      if (!response.trim()) {\n        _Alert.default.alert(\"Missing Text\", \"Please provide a text explanation with your photo.\");\n        return;\n      }\n      setSubmitting(true);\n      try {\n        // TODO: Submit to API\n\n        const responseData = {\n          questionId: id,\n          textResponse: response.trim(),\n          imageUrl: cameraResult.imageUrl,\n          challengeCode: cameraResult.challengeCode,\n          timestamp: cameraResult.timestamp,\n          userLocation: currentLocation,\n          distanceFromQuestion: distance,\n          testingMode: testingMode // Include testing mode flag\n        };\n        console.log(\"Submitting response:\", responseData);\n\n        // Simulate API call\n\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        _Alert.default.alert(\"Response Submitted!\", testingMode ? \"Test response submitted successfully! This was in testing mode.\" : `You'll receive $${question.reward.toFixed(2)} once the questioner confirms your response.`, [{\n          text: \"OK\",\n          onPress: () => _expoRouter.router.back()\n        }]);\n      } catch (error) {\n        console.error(\"Error submitting response:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to submit response. Please try again.\");\n      } finally {\n        setSubmitting(false);\n      }\n    };\n\n    // Location status component\n\n    const LocationStatus = () => {\n      const getStatusConfig = () => {\n        switch (locationStatus) {\n          case \"checking\":\n            return {\n              color: \"#F59E0B\",\n              bgColor: \"#FEF3C7\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#D97706\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1177,\n                columnNumber: 19\n              }, this),\n              title: \"Checking Location\",\n              message: gettingLocation ? \"Getting your current location\" : \"Verifying position\"\n            };\n          case \"verified\":\n            return {\n              color: \"#10B981\",\n              bgColor: \"#D1FAE5\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                size: 16,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 19\n              }, this),\n              title: \"Location Verified\",\n              message: `You're ${distance || 0}m from the question location`\n            };\n          case \"too_far\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1249,\n                columnNumber: 19\n              }, this),\n              title: \"Too Far Away\",\n              message: `You're ${distance || 0}m away (max 200m allowed)`\n            };\n          case \"error\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1281,\n                columnNumber: 19\n              }, this),\n              title: \"Location Error\",\n              message: \"Could not verify your location\"\n            };\n          default:\n            return {\n              color: \"#6B7280\",\n              bgColor: \"#F3F4F6\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 19\n              }, this),\n              title: \"Unknown Status\",\n              message: \"Please try again\"\n            };\n        }\n      };\n      const config = getStatusConfig();\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: config.bgColor,\n          borderRadius: 12,\n          padding: 16,\n          marginBottom: 24,\n          borderWidth: 1,\n          borderColor: config.color + \"40\"\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 8\n          },\n          children: [config.icon, /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontWeight: \"600\",\n              color: config.color,\n              marginLeft: 8\n            },\n            children: config.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1417,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1385,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: {\n            fontSize: 14,\n            color: config.color,\n            lineHeight: 20\n          },\n          children: config.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1461,\n          columnNumber: 9\n        }, this), (locationStatus === \"too_far\" || locationStatus === \"error\") && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            marginTop: 12\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: verifyLocation,\n            disabled: gettingLocation,\n            style: {\n              backgroundColor: config.color,\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              opacity: gettingLocation ? 0.6 : 1\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: gettingLocation ? \"Checking\" : \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1525,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1481,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => {\n              console.log(\"Toggle testing mode:\", {\n                before: testingMode,\n                after: !testingMode\n              });\n              setTestingMode(!testingMode);\n            },\n            style: {\n              backgroundColor: testingMode ? \"#10B981\" : \"#6B7280\",\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              marginLeft: 12\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: testingMode ? \"Testing ON\" : \"Enable Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1605,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1477,\n          columnNumber: 11\n        }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#FEF3C7\",\n            borderRadius: 8,\n            padding: 12,\n            marginTop: 12,\n            borderWidth: 1,\n            borderColor: \"#F59E0B\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 14,\n              fontWeight: \"600\",\n              color: \"#D97706\",\n              marginBottom: 4\n            },\n            children: \"Testing Mode Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1673,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 13,\n              color: \"#D97706\",\n              lineHeight: 18\n            },\n            children: \"Location verification bypassed for testing. You can now use the camera regardless of your location.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1713,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1633,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1345,\n        columnNumber: 7\n      }, this);\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: \"#F9FAFB\"\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoStatusBar.StatusBar, {\n        style: \"dark\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1757,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: \"#fff\",\n          paddingTop: insets.top + 8,\n          paddingHorizontal: 20,\n          paddingBottom: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: \"#E5E7EB\",\n          zIndex: 1000\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => _expoRouter.router.back(),\n            style: {\n              marginRight: 16\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.ArrowLeft, {\n              size: 24,\n              color: \"#111827\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1853,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1837,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 20,\n              fontWeight: \"bold\",\n              color: \"#111827\",\n              flex: 1\n            },\n            children: \"Respond to Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1861,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 10,\n              color: \"#EF4444\"\n            },\n            children: `DEBUG: testing=${testingMode ? \"ON\" : \"OFF\"}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1905,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1809,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#F0F9FF\",\n            borderRadius: 12,\n            padding: 16,\n            borderLeftWidth: 4,\n            borderLeftColor: \"#3B82F6\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              color: \"#1E40AF\",\n              fontWeight: \"500\",\n              marginBottom: 12\n            },\n            children: question.question\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1961,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MapPin, {\n              size: 14,\n              color: \"#6B7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2029,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#6B7280\",\n                marginLeft: 6,\n                flex: 1\n              },\n              children: question.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2033,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2001,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.DollarSign, {\n                size: 14,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2089,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#059669\",\n                  fontWeight: \"600\",\n                  marginLeft: 2\n                },\n                children: `$${question.reward.toFixed(2)} reward`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2093,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2085,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Clock, {\n                size: 14,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2141,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 12,\n                  color: \"#6B7280\",\n                  marginLeft: 4\n                },\n                children: question.postedAt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2145,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2137,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2057,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1925,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1765,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        behavior: \"padding\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingBottom: insets.bottom + 100\n          },\n          showsVerticalScrollIndicator: false,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              padding: 20\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(LocationStatus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2205,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: capturedPhotoUri ? '#10B981' : locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    marginRight: 12\n                  },\n                  children: capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2259,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 12,\n                      fontWeight: '600'\n                    },\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2263,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2227,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Capture Privacy-Safe Photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2269,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2213,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: '#6B7280',\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Take a privacy-safe photo using our real-time face blurring camera. Faces are automatically blurred before the photo is captured.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2289,\n                columnNumber: 15\n              }, this), capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: '#fff',\n                  borderRadius: 16,\n                  overflow: 'hidden',\n                  borderWidth: 1,\n                  borderColor: '#E5E7EB',\n                  ..._Platform.default.select({\n                    ios: {\n                      shadowColor: '#000',\n                      shadowOffset: {\n                        width: 0,\n                        height: 2\n                      },\n                      shadowOpacity: 0.05,\n                      shadowRadius: 8\n                    },\n                    android: {\n                      elevation: 3\n                    },\n                    web: {\n                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\n                    }\n                  })\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      position: 'absolute',\n                      top: 5,\n                      left: 5,\n                      backgroundColor: 'rgba(0,0,0,0.7)',\n                      padding: 4,\n                      borderRadius: 4,\n                      zIndex: 10\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        color: 'white',\n                        fontSize: 10\n                      },\n                      children: capturedPhotoUri?.startsWith('blob:') ? '🛡️ BLURRED' : capturedPhotoUri?.startsWith('data:') ? '📷 DATA' : capturedPhotoUri?.startsWith('http') ? '🌐 URL' : '❓ UNKNOWN'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2368,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        color: 'white',\n                        fontSize: 8\n                      },\n                      children: [capturedPhotoUri?.substring(0, 30), \"...\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2373,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Image.default, {\n                    source: {\n                      uri: capturedPhotoUri\n                    },\n                    // Force re-render when URI changes\n\n                    style: {\n                      width: '100%',\n                      height: 400,\n                      // Increased from 240 to 400 for better visibility\n                      backgroundColor: '#F3F4F6',\n                      borderRadius: 12 // Add slight rounding for better appearance\n                    },\n                    resizeMode: \"contain\" // Changed from \"cover\" to \"contain\" to show full image\n                    ,\n\n                    onError: e => {\n                      console.error('[IMAGE ERROR] Failed to load image:', {\n                        error: e.nativeEvent?.error,\n                        uri: capturedPhotoUri,\n                        cameraResult: JSON.stringify(cameraResult, null, 2)\n                      });\n                    },\n                    onLoad: () => {\n                      console.log('[IMAGE SUCCESS] Image loaded successfully:', capturedPhotoUri);\n                    },\n                    onLoadStart: () => {\n                      console.log('[IMAGE START] Loading image from:', capturedPhotoUri);\n                      console.log('🔍 DEBUGGING: Image source details:', {\n                        capturedPhotoUri,\n                        isBlob: capturedPhotoUri?.startsWith('blob:'),\n                        isDataUri: capturedPhotoUri?.startsWith('data:'),\n                        length: capturedPhotoUri?.length\n                      });\n                    },\n                    onLoadEnd: () => {\n                      console.log('[IMAGE END] Image loading finished');\n                    }\n                  }, capturedPhotoUri, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2378,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      position: 'absolute',\n                      top: 12,\n                      right: 12,\n                      backgroundColor: 'rgba(16, 185, 129, 0.95)',\n                      paddingHorizontal: 12,\n                      paddingVertical: 6,\n                      borderRadius: 20,\n                      flexDirection: 'row',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                      size: 14,\n                      color: \"#fff\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2458,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 12,\n                        fontWeight: '600',\n                        marginLeft: 4\n                      },\n                      children: \"Privacy Protected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2460,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2432,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2357,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    padding: 16\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'center',\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        width: 32,\n                        height: 32,\n                        borderRadius: 16,\n                        backgroundColor: '#D1FAE5',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 20,\n                        color: \"#10B981\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2506,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2486,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        marginLeft: 12,\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 18,\n                          fontWeight: '700',\n                          color: '#111827'\n                        },\n                        children: \"Photo Captured Successfully\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2512,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 13,\n                          color: '#6B7280',\n                          marginTop: 2\n                        },\n                        children: \"Ready to submit with your response\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2518,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2510,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2472,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      backgroundColor: '#F0FDF4',\n                      borderRadius: 12,\n                      padding: 12,\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        fontSize: 12,\n                        fontWeight: '600',\n                        color: '#15803D',\n                        marginBottom: 8,\n                        textTransform: 'uppercase',\n                        letterSpacing: 0.5\n                      },\n                      children: \"Protection Applied\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2544,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2570,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: \"Faces automatically blurred in real-time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2572,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2568,\n                      columnNumber: 23\n                    }, this), cameraResult?.challengeCode && cameraResult.challengeCode.trim() && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2584,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Challenge verified: ${cameraResult.challengeCode || 'N/A'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2586,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2582,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2598,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Location confirmed: ${distance || 0}m away`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2600,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2596,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2528,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row'\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                      onPress: () => {\n                        setCameraResult(null);\n                        setCapturedPhotoUri(null);\n                        handleStartCamera();\n                      },\n                      style: {\n                        flex: 1,\n                        backgroundColor: '#fff',\n                        borderWidth: 1,\n                        borderColor: '#D1D5DB',\n                        borderRadius: 12,\n                        paddingVertical: 12,\n                        paddingHorizontal: 16,\n                        flexDirection: 'row',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                        size: 18,\n                        color: \"#6B7280\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2650,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 15,\n                          color: '#6B7280',\n                          fontWeight: '600',\n                          marginLeft: 8\n                        },\n                        children: \"Retake Photo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2652,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2612,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2610,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2470,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2311,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: handleStartCamera,\n                disabled: locationStatus !== 'verified' && !testingMode,\n                style: {\n                  backgroundColor: locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                  borderRadius: 12,\n                  padding: 16,\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                  size: 20,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2694,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 16,\n                    fontWeight: '600',\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: locationStatus === 'verified' || testingMode ? 'Start Camera' : 'Verify Location First'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2696,\n                  columnNumber: 19\n                }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 10,\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: \"TEST\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2722,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2668,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2211,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: \"row\",\n                  alignItems: \"center\",\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: response.trim() ? \"#10B981\" : cameraResult ? \"#3B82F6\" : \"#9CA3AF\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    marginRight: 12\n                  },\n                  children: response.trim() ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2757,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: \"#fff\",\n                      fontSize: 12,\n                      fontWeight: \"600\"\n                    },\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2759,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2741,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: \"600\",\n                    color: \"#111827\"\n                  },\n                  children: \"Add Text Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2767,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2734,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#6B7280\",\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Describe what your photo shows. Be specific and helpful to answer the question completely.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2778,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: \"#fff\",\n                  borderRadius: 12,\n                  borderWidth: 1,\n                  borderColor: \"#E5E7EB\",\n                  padding: 4\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    alignItems: \"flex-start\",\n                    padding: 12\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageCircle, {\n                    size: 20,\n                    color: \"#6B7280\",\n                    style: {\n                      marginTop: 2,\n                      marginRight: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2805,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextInput.default, {\n                    style: {\n                      flex: 1,\n                      fontSize: 16,\n                      color: \"#111827\",\n                      minHeight: 100,\n                      textAlignVertical: \"top\"\n                    },\n                    placeholder: \"Describe what you can see that answers their question\",\n                    placeholderTextColor: \"#9CA3AF\",\n                    value: response,\n                    onChangeText: setResponse,\n                    multiline: true,\n                    maxLength: 500\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2810,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2798,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    paddingHorizontal: 16,\n                    paddingBottom: 8\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#6B7280\"\n                    },\n                    children: \"Be specific and helpful\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2836,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#9CA3AF\"\n                    },\n                    children: `${response.length}/500`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2839,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2827,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2789,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2733,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: \"#EBF5FF\",\n                borderRadius: 12,\n                padding: 16,\n                marginBottom: 24\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontWeight: \"600\",\n                  color: \"#1E40AF\",\n                  marginBottom: 8\n                },\n                children: \"Privacy Protection Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2881,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 13,\n                  color: \"#1E40AF\",\n                  lineHeight: 18\n                },\n                children: `Your photo is processed on-device with real-time face blurring. All faces are automatically blurred before the photo is captured, ensuring complete privacy protection.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2921,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2849,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2197,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2177,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0,\n            backgroundColor: \"#fff\",\n            borderTopWidth: 1,\n            borderTopColor: \"#E5E7EB\",\n            padding: 20,\n            paddingBottom: insets.bottom + 20\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: submitResponse,\n            disabled: submitting || !cameraResult || !response.trim(),\n            style: {\n              backgroundColor: submitting || !cameraResult || !response.trim() ? \"#9CA3AF\" : \"#10B981\",\n              borderRadius: 12,\n              padding: 16,\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: submitting ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  width: 16,\n                  height: 16,\n                  borderRadius: 8,\n                  borderWidth: 2,\n                  borderColor: \"#fff\",\n                  borderTopColor: \"transparent\",\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3093,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\"\n                },\n                children: \"Submitting Response\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Send, {\n                size: 20,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\",\n                  marginLeft: 8\n                },\n                children: `Submit Response ($${question.reward.toFixed(2)})`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3025,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2973,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2173,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: showCamera,\n        animationType: \"slide\",\n        presentationStyle: \"fullScreen\",\n        children: _Platform.default.OS === 'web' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraWeb.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3261,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraUnified.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 3237,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1753,\n      columnNumber: 5\n    }, this);\n  }\n  _s(RespondScreen, \"gw2Imk4A9BoQq1d4k9rSymf1PEo=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _expoRouter.useLocalSearchParams];\n  });\n  _c = RespondScreen;\n  var _c;\n  $RefreshReg$(_c, \"RespondScreen\");\n});", "lineCount": 1628, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 73], [8, 6, 1, 73, "_View"], [8, 11, 1, 73], [8, 14, 1, 73, "_interopRequireDefault"], [8, 36, 1, 73], [8, 37, 1, 73, "require"], [8, 44, 1, 73], [8, 45, 1, 73, "_dependencyMap"], [8, 59, 1, 73], [9, 2, 1, 73], [9, 6, 1, 73, "_Text"], [9, 11, 1, 73], [9, 14, 1, 73, "_interopRequireDefault"], [9, 36, 1, 73], [9, 37, 1, 73, "require"], [9, 44, 1, 73], [9, 45, 1, 73, "_dependencyMap"], [9, 59, 1, 73], [10, 2, 1, 73], [10, 6, 1, 73, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 17, 1, 73], [10, 20, 1, 73, "_interopRequireDefault"], [10, 42, 1, 73], [10, 43, 1, 73, "require"], [10, 50, 1, 73], [10, 51, 1, 73, "_dependencyMap"], [10, 65, 1, 73], [11, 2, 1, 73], [11, 6, 1, 73, "_TextInput"], [11, 16, 1, 73], [11, 19, 1, 73, "_interopRequireDefault"], [11, 41, 1, 73], [11, 42, 1, 73, "require"], [11, 49, 1, 73], [11, 50, 1, 73, "_dependencyMap"], [11, 64, 1, 73], [12, 2, 1, 73], [12, 6, 1, 73, "_TouchableOpacity"], [12, 23, 1, 73], [12, 26, 1, 73, "_interopRequireDefault"], [12, 48, 1, 73], [12, 49, 1, 73, "require"], [12, 56, 1, 73], [12, 57, 1, 73, "_dependencyMap"], [12, 71, 1, 73], [13, 2, 1, 73], [13, 6, 1, 73, "_<PERSON><PERSON>"], [13, 12, 1, 73], [13, 15, 1, 73, "_interopRequireDefault"], [13, 37, 1, 73], [13, 38, 1, 73, "require"], [13, 45, 1, 73], [13, 46, 1, 73, "_dependencyMap"], [13, 60, 1, 73], [14, 2, 1, 73], [14, 6, 1, 73, "_Modal"], [14, 12, 1, 73], [14, 15, 1, 73, "_interopRequireDefault"], [14, 37, 1, 73], [14, 38, 1, 73, "require"], [14, 45, 1, 73], [14, 46, 1, 73, "_dependencyMap"], [14, 60, 1, 73], [15, 2, 1, 73], [15, 6, 1, 73, "_Platform"], [15, 15, 1, 73], [15, 18, 1, 73, "_interopRequireDefault"], [15, 40, 1, 73], [15, 41, 1, 73, "require"], [15, 48, 1, 73], [15, 49, 1, 73, "_dependencyMap"], [15, 63, 1, 73], [16, 2, 1, 73], [16, 6, 1, 73, "_Image"], [16, 12, 1, 73], [16, 15, 1, 73, "_interopRequireDefault"], [16, 37, 1, 73], [16, 38, 1, 73, "require"], [16, 45, 1, 73], [16, 46, 1, 73, "_dependencyMap"], [16, 60, 1, 73], [17, 2, 1, 73], [17, 6, 1, 73, "_ActivityIndicator"], [17, 24, 1, 73], [17, 27, 1, 73, "_interopRequireDefault"], [17, 49, 1, 73], [17, 50, 1, 73, "require"], [17, 57, 1, 73], [17, 58, 1, 73, "_dependencyMap"], [17, 72, 1, 73], [18, 2, 53, 0], [18, 6, 53, 0, "_reactNativeSafeAreaContext"], [18, 33, 53, 0], [18, 36, 53, 0, "require"], [18, 43, 53, 0], [18, 44, 53, 0, "_dependencyMap"], [18, 58, 53, 0], [19, 2, 57, 0], [19, 6, 57, 0, "_expoStatusBar"], [19, 20, 57, 0], [19, 23, 57, 0, "require"], [19, 30, 57, 0], [19, 31, 57, 0, "_dependencyMap"], [19, 45, 57, 0], [20, 2, 61, 0], [20, 6, 61, 0, "_expoRouter"], [20, 17, 61, 0], [20, 20, 61, 0, "require"], [20, 27, 61, 0], [20, 28, 61, 0, "_dependencyMap"], [20, 42, 61, 0], [21, 2, 65, 0], [21, 6, 65, 0, "_lucideReactNative"], [21, 24, 65, 0], [21, 27, 65, 0, "require"], [21, 34, 65, 0], [21, 35, 65, 0, "_dependencyMap"], [21, 49, 65, 0], [22, 2, 117, 0], [22, 6, 117, 0, "Location"], [22, 14, 117, 0], [22, 17, 117, 0, "_interopRequireWildcard"], [22, 40, 117, 0], [22, 41, 117, 0, "require"], [22, 48, 117, 0], [22, 49, 117, 0, "_dependencyMap"], [22, 63, 117, 0], [23, 2, 121, 0], [23, 6, 121, 0, "_EchoCameraUnified"], [23, 24, 121, 0], [23, 27, 121, 0, "_interopRequireDefault"], [23, 49, 121, 0], [23, 50, 121, 0, "require"], [23, 57, 121, 0], [23, 58, 121, 0, "_dependencyMap"], [23, 72, 121, 0], [24, 2, 125, 0], [24, 6, 125, 0, "_EchoCameraWeb"], [24, 20, 125, 0], [24, 23, 125, 0, "_interopRequireDefault"], [24, 45, 125, 0], [24, 46, 125, 0, "require"], [24, 53, 125, 0], [24, 54, 125, 0, "_dependencyMap"], [24, 68, 125, 0], [25, 2, 129, 0], [25, 6, 129, 0, "_KeyboardAvoidingAnimatedView"], [25, 35, 129, 0], [25, 38, 129, 0, "_interopRequireDefault"], [25, 60, 129, 0], [25, 61, 129, 0, "require"], [25, 68, 129, 0], [25, 69, 129, 0, "_dependencyMap"], [25, 83, 129, 0], [26, 2, 129, 85], [26, 6, 129, 85, "_jsxDevRuntime"], [26, 20, 129, 85], [26, 23, 129, 85, "require"], [26, 30, 129, 85], [26, 31, 129, 85, "_dependencyMap"], [26, 45, 129, 85], [27, 2, 129, 85], [27, 6, 129, 85, "_jsxFileName"], [27, 18, 129, 85], [28, 4, 129, 85, "_s"], [28, 6, 129, 85], [28, 9, 129, 85, "$RefreshSig$"], [28, 21, 129, 85], [29, 2, 129, 85], [29, 11, 129, 85, "_interopRequireWildcard"], [29, 35, 129, 85, "e"], [29, 36, 129, 85], [29, 38, 129, 85, "t"], [29, 39, 129, 85], [29, 68, 129, 85, "WeakMap"], [29, 75, 129, 85], [29, 81, 129, 85, "r"], [29, 82, 129, 85], [29, 89, 129, 85, "WeakMap"], [29, 96, 129, 85], [29, 100, 129, 85, "n"], [29, 101, 129, 85], [29, 108, 129, 85, "WeakMap"], [29, 115, 129, 85], [29, 127, 129, 85, "_interopRequireWildcard"], [29, 150, 129, 85], [29, 162, 129, 85, "_interopRequireWildcard"], [29, 163, 129, 85, "e"], [29, 164, 129, 85], [29, 166, 129, 85, "t"], [29, 167, 129, 85], [29, 176, 129, 85, "t"], [29, 177, 129, 85], [29, 181, 129, 85, "e"], [29, 182, 129, 85], [29, 186, 129, 85, "e"], [29, 187, 129, 85], [29, 188, 129, 85, "__esModule"], [29, 198, 129, 85], [29, 207, 129, 85, "e"], [29, 208, 129, 85], [29, 214, 129, 85, "o"], [29, 215, 129, 85], [29, 217, 129, 85, "i"], [29, 218, 129, 85], [29, 220, 129, 85, "f"], [29, 221, 129, 85], [29, 226, 129, 85, "__proto__"], [29, 235, 129, 85], [29, 243, 129, 85, "default"], [29, 250, 129, 85], [29, 252, 129, 85, "e"], [29, 253, 129, 85], [29, 270, 129, 85, "e"], [29, 271, 129, 85], [29, 294, 129, 85, "e"], [29, 295, 129, 85], [29, 320, 129, 85, "e"], [29, 321, 129, 85], [29, 330, 129, 85, "f"], [29, 331, 129, 85], [29, 337, 129, 85, "o"], [29, 338, 129, 85], [29, 341, 129, 85, "t"], [29, 342, 129, 85], [29, 345, 129, 85, "n"], [29, 346, 129, 85], [29, 349, 129, 85, "r"], [29, 350, 129, 85], [29, 358, 129, 85, "o"], [29, 359, 129, 85], [29, 360, 129, 85, "has"], [29, 363, 129, 85], [29, 364, 129, 85, "e"], [29, 365, 129, 85], [29, 375, 129, 85, "o"], [29, 376, 129, 85], [29, 377, 129, 85, "get"], [29, 380, 129, 85], [29, 381, 129, 85, "e"], [29, 382, 129, 85], [29, 385, 129, 85, "o"], [29, 386, 129, 85], [29, 387, 129, 85, "set"], [29, 390, 129, 85], [29, 391, 129, 85, "e"], [29, 392, 129, 85], [29, 394, 129, 85, "f"], [29, 395, 129, 85], [29, 411, 129, 85, "t"], [29, 412, 129, 85], [29, 416, 129, 85, "e"], [29, 417, 129, 85], [29, 433, 129, 85, "t"], [29, 434, 129, 85], [29, 441, 129, 85, "hasOwnProperty"], [29, 455, 129, 85], [29, 456, 129, 85, "call"], [29, 460, 129, 85], [29, 461, 129, 85, "e"], [29, 462, 129, 85], [29, 464, 129, 85, "t"], [29, 465, 129, 85], [29, 472, 129, 85, "i"], [29, 473, 129, 85], [29, 477, 129, 85, "o"], [29, 478, 129, 85], [29, 481, 129, 85, "Object"], [29, 487, 129, 85], [29, 488, 129, 85, "defineProperty"], [29, 502, 129, 85], [29, 507, 129, 85, "Object"], [29, 513, 129, 85], [29, 514, 129, 85, "getOwnPropertyDescriptor"], [29, 538, 129, 85], [29, 539, 129, 85, "e"], [29, 540, 129, 85], [29, 542, 129, 85, "t"], [29, 543, 129, 85], [29, 550, 129, 85, "i"], [29, 551, 129, 85], [29, 552, 129, 85, "get"], [29, 555, 129, 85], [29, 559, 129, 85, "i"], [29, 560, 129, 85], [29, 561, 129, 85, "set"], [29, 564, 129, 85], [29, 568, 129, 85, "o"], [29, 569, 129, 85], [29, 570, 129, 85, "f"], [29, 571, 129, 85], [29, 573, 129, 85, "t"], [29, 574, 129, 85], [29, 576, 129, 85, "i"], [29, 577, 129, 85], [29, 581, 129, 85, "f"], [29, 582, 129, 85], [29, 583, 129, 85, "t"], [29, 584, 129, 85], [29, 588, 129, 85, "e"], [29, 589, 129, 85], [29, 590, 129, 85, "t"], [29, 591, 129, 85], [29, 602, 129, 85, "f"], [29, 603, 129, 85], [29, 608, 129, 85, "e"], [29, 609, 129, 85], [29, 611, 129, 85, "t"], [29, 612, 129, 85], [30, 2, 133, 15], [30, 11, 133, 24, "RespondScreen"], [30, 24, 133, 37, "RespondScreen"], [30, 25, 133, 37], [30, 27, 133, 40], [31, 4, 133, 40, "_s"], [31, 6, 133, 40], [32, 4, 137, 2], [32, 10, 137, 8, "insets"], [32, 16, 137, 14], [32, 19, 137, 17], [32, 23, 137, 17, "useSafeAreaInsets"], [32, 68, 137, 34], [32, 70, 137, 35], [32, 71, 137, 36], [33, 4, 141, 2], [33, 10, 141, 8], [34, 6, 141, 10, "id"], [35, 4, 141, 13], [35, 5, 141, 14], [35, 8, 141, 17], [35, 12, 141, 17, "useLocalSearchParams"], [35, 44, 141, 37], [35, 46, 141, 38], [35, 47, 141, 39], [36, 4, 145, 2], [36, 10, 145, 8], [36, 11, 145, 9, "response"], [36, 19, 145, 17], [36, 21, 145, 19, "setResponse"], [36, 32, 145, 30], [36, 33, 145, 31], [36, 36, 145, 34], [36, 40, 145, 34, "useState"], [36, 55, 145, 42], [36, 57, 145, 43], [36, 59, 145, 45], [36, 60, 145, 46], [37, 4, 149, 2], [37, 10, 149, 8], [37, 11, 149, 9, "showCamera"], [37, 21, 149, 19], [37, 23, 149, 21, "setShowCamera"], [37, 36, 149, 34], [37, 37, 149, 35], [37, 40, 149, 38], [37, 44, 149, 38, "useState"], [37, 59, 149, 46], [37, 61, 149, 47], [37, 66, 149, 52], [37, 67, 149, 53], [38, 4, 153, 2], [38, 10, 153, 8], [38, 11, 153, 9, "cameraResult"], [38, 23, 153, 21], [38, 25, 153, 23, "setCameraResult"], [38, 40, 153, 38], [38, 41, 153, 39], [38, 44, 153, 42], [38, 48, 153, 42, "useState"], [38, 63, 153, 50], [38, 65, 153, 51], [38, 69, 153, 55], [38, 70, 153, 56], [39, 4, 157, 2], [39, 10, 157, 8], [39, 11, 157, 9, "submitting"], [39, 21, 157, 19], [39, 23, 157, 21, "setSubmitting"], [39, 36, 157, 34], [39, 37, 157, 35], [39, 40, 157, 38], [39, 44, 157, 38, "useState"], [39, 59, 157, 46], [39, 61, 157, 47], [39, 66, 157, 52], [39, 67, 157, 53], [40, 4, 161, 2], [40, 10, 161, 8], [40, 11, 161, 9, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [40, 27, 161, 25], [40, 29, 161, 27, "setCapturedPhotoUri"], [40, 48, 161, 46], [40, 49, 161, 47], [40, 52, 161, 50], [40, 56, 161, 50, "useState"], [40, 71, 161, 58], [40, 73, 161, 59], [40, 77, 161, 63], [40, 78, 161, 64], [41, 4, 165, 2], [41, 10, 165, 8, "defaultTestingMode"], [41, 28, 165, 26], [41, 31, 165, 29], [41, 35, 165, 29, "useMemo"], [41, 49, 165, 36], [41, 51, 165, 37], [41, 57, 165, 43], [42, 6, 169, 4], [42, 10, 169, 8, "Platform"], [42, 27, 169, 16], [42, 28, 169, 17, "OS"], [42, 30, 169, 19], [42, 35, 169, 24], [42, 40, 169, 29], [42, 44, 169, 33], [42, 51, 169, 40, "window"], [42, 57, 169, 46], [42, 62, 169, 51], [42, 73, 169, 62], [42, 75, 169, 64], [43, 8, 173, 6], [43, 15, 173, 13], [43, 20, 173, 18], [44, 6, 177, 4], [45, 6, 181, 4], [45, 12, 181, 10], [46, 8, 181, 12, "protocol"], [46, 16, 181, 20], [47, 8, 181, 22, "hostname"], [48, 6, 181, 31], [48, 7, 181, 32], [48, 10, 181, 35, "window"], [48, 16, 181, 41], [48, 17, 181, 42, "location"], [48, 25, 181, 50], [49, 6, 185, 4], [49, 12, 185, 10, "localHosts"], [49, 22, 185, 20], [49, 25, 185, 23], [49, 26, 185, 24], [49, 37, 185, 35], [49, 39, 185, 37], [49, 50, 185, 48], [49, 52, 185, 50], [49, 57, 185, 55], [49, 58, 185, 56], [50, 6, 189, 4], [50, 13, 189, 11, "protocol"], [50, 21, 189, 19], [50, 26, 189, 24], [50, 34, 189, 32], [50, 38, 189, 36, "localHosts"], [50, 48, 189, 46], [50, 49, 189, 47, "includes"], [50, 57, 189, 55], [50, 58, 189, 56, "hostname"], [50, 66, 189, 64], [50, 67, 189, 65], [51, 4, 193, 2], [51, 5, 193, 3], [51, 7, 193, 5], [51, 9, 193, 7], [51, 10, 193, 8], [52, 4, 197, 2], [52, 10, 197, 8], [52, 11, 197, 9, "testingMode"], [52, 22, 197, 20], [52, 24, 197, 22, "setTestingMode"], [52, 38, 197, 36], [52, 39, 197, 37], [52, 42, 197, 40], [52, 46, 197, 40, "useState"], [52, 61, 197, 48], [52, 63, 197, 49, "defaultTestingMode"], [52, 81, 197, 67], [52, 82, 197, 68], [54, 4, 201, 2], [56, 4, 205, 2], [56, 10, 205, 8], [56, 11, 205, 9, "locationStatus"], [56, 25, 205, 23], [56, 27, 205, 25, "setLocationStatus"], [56, 44, 205, 42], [56, 45, 205, 43], [56, 48, 205, 46], [56, 52, 205, 46, "useState"], [56, 67, 205, 54], [56, 69, 205, 55], [56, 79, 205, 65], [56, 80, 205, 66], [56, 81, 205, 67], [56, 82, 205, 68], [58, 4, 209, 2], [58, 10, 209, 8], [58, 11, 209, 9, "currentLocation"], [58, 26, 209, 24], [58, 28, 209, 26, "setCurrentLocation"], [58, 46, 209, 44], [58, 47, 209, 45], [58, 50, 209, 48], [58, 54, 209, 48, "useState"], [58, 69, 209, 56], [58, 71, 209, 57], [58, 75, 209, 61], [58, 76, 209, 62], [59, 4, 213, 2], [59, 10, 213, 8], [59, 11, 213, 9, "distance"], [59, 19, 213, 17], [59, 21, 213, 19, "setDistance"], [59, 32, 213, 30], [59, 33, 213, 31], [59, 36, 213, 34], [59, 40, 213, 34, "useState"], [59, 55, 213, 42], [59, 57, 213, 43], [59, 61, 213, 47], [59, 62, 213, 48], [60, 4, 217, 2], [60, 10, 217, 8], [60, 11, 217, 9, "gettingLocation"], [60, 26, 217, 24], [60, 28, 217, 26, "setGettingLocation"], [60, 46, 217, 44], [60, 47, 217, 45], [60, 50, 217, 48], [60, 54, 217, 48, "useState"], [60, 69, 217, 56], [60, 71, 217, 57], [60, 76, 217, 62], [60, 77, 217, 63], [61, 4, 221, 2], [61, 10, 221, 8], [61, 11, 221, 9, "locationError"], [61, 24, 221, 22], [61, 26, 221, 24, "setLocationError"], [61, 42, 221, 40], [61, 43, 221, 41], [61, 46, 221, 44], [61, 50, 221, 44, "useState"], [61, 65, 221, 52], [61, 67, 221, 53], [61, 71, 221, 57], [61, 72, 221, 58], [63, 4, 225, 2], [65, 4, 229, 2], [65, 10, 229, 8, "question"], [65, 18, 229, 16], [65, 21, 229, 19], [66, 6, 233, 4, "id"], [66, 8, 233, 6], [66, 10, 233, 8, "id"], [66, 12, 233, 10], [67, 6, 237, 4, "question"], [67, 14, 237, 12], [67, 16, 241, 6], [67, 114, 241, 104], [68, 6, 245, 4, "location"], [68, 14, 245, 12], [68, 16, 245, 14], [68, 43, 245, 41], [69, 6, 249, 4, "coordinates"], [69, 17, 249, 15], [69, 19, 249, 17], [70, 8, 253, 6], [72, 8, 257, 6, "latitude"], [72, 16, 257, 14], [72, 18, 257, 16], [72, 25, 257, 23], [73, 8, 257, 25], [75, 8, 261, 6, "longitude"], [75, 17, 261, 15], [75, 19, 261, 17], [75, 20, 261, 18], [76, 6, 265, 4], [76, 7, 265, 5], [77, 6, 269, 4, "reward"], [77, 12, 269, 10], [77, 14, 269, 12], [77, 17, 269, 15], [78, 6, 273, 4, "postedAt"], [78, 14, 273, 12], [78, 16, 273, 14], [78, 29, 273, 27], [79, 6, 277, 4, "userId"], [79, 12, 277, 10], [79, 14, 277, 12], [80, 4, 281, 0], [80, 5, 281, 1], [81, 4, 285, 2], [81, 10, 285, 8, "questionLatitude"], [81, 26, 285, 24], [81, 29, 285, 27, "question"], [81, 37, 285, 35], [81, 38, 285, 36, "coordinates"], [81, 49, 285, 47], [81, 50, 285, 48, "latitude"], [81, 58, 285, 56], [82, 4, 289, 2], [82, 10, 289, 8, "questionLongitude"], [82, 27, 289, 25], [82, 30, 289, 28, "question"], [82, 38, 289, 36], [82, 39, 289, 37, "coordinates"], [82, 50, 289, 48], [82, 51, 289, 49, "longitude"], [82, 60, 289, 58], [84, 4, 293, 2], [86, 4, 297, 2], [86, 10, 297, 8, "calculateDistance"], [86, 27, 297, 25], [86, 30, 297, 28, "calculateDistance"], [86, 31, 297, 29, "lat1"], [86, 35, 297, 33], [86, 37, 297, 35, "lon1"], [86, 41, 297, 39], [86, 43, 297, 41, "lat2"], [86, 47, 297, 45], [86, 49, 297, 47, "lon2"], [86, 53, 297, 51], [86, 58, 297, 56], [87, 6, 301, 4], [87, 12, 301, 10, "R"], [87, 13, 301, 11], [87, 16, 301, 14], [87, 22, 301, 20], [87, 23, 301, 21], [87, 24, 301, 22], [89, 6, 305, 4], [89, 12, 305, 10, "lat1Rad"], [89, 19, 305, 17], [89, 22, 305, 21, "lat1"], [89, 26, 305, 25], [89, 29, 305, 28, "Math"], [89, 33, 305, 32], [89, 34, 305, 33, "PI"], [89, 36, 305, 35], [89, 39, 305, 39], [89, 42, 305, 42], [90, 6, 309, 4], [90, 12, 309, 10, "lat2Rad"], [90, 19, 309, 17], [90, 22, 309, 21, "lat2"], [90, 26, 309, 25], [90, 29, 309, 28, "Math"], [90, 33, 309, 32], [90, 34, 309, 33, "PI"], [90, 36, 309, 35], [90, 39, 309, 39], [90, 42, 309, 42], [91, 6, 313, 4], [91, 12, 313, 10, "deltaLat"], [91, 20, 313, 18], [91, 23, 313, 22], [91, 24, 313, 23, "lat2"], [91, 28, 313, 27], [91, 31, 313, 30, "lat1"], [91, 35, 313, 34], [91, 39, 313, 38, "Math"], [91, 43, 313, 42], [91, 44, 313, 43, "PI"], [91, 46, 313, 45], [91, 49, 313, 49], [91, 52, 313, 52], [92, 6, 317, 4], [92, 12, 317, 10, "deltaLon"], [92, 20, 317, 18], [92, 23, 317, 22], [92, 24, 317, 23, "lon2"], [92, 28, 317, 27], [92, 31, 317, 30, "lon1"], [92, 35, 317, 34], [92, 39, 317, 38, "Math"], [92, 43, 317, 42], [92, 44, 317, 43, "PI"], [92, 46, 317, 45], [92, 49, 317, 49], [92, 52, 317, 52], [93, 6, 321, 4], [93, 12, 321, 10, "a"], [93, 13, 321, 11], [93, 16, 325, 6, "Math"], [93, 20, 325, 10], [93, 21, 325, 11, "sin"], [93, 24, 325, 14], [93, 25, 325, 15, "deltaLat"], [93, 33, 325, 23], [93, 36, 325, 26], [93, 37, 325, 27], [93, 38, 325, 28], [93, 41, 325, 31, "Math"], [93, 45, 325, 35], [93, 46, 325, 36, "sin"], [93, 49, 325, 39], [93, 50, 325, 40, "deltaLat"], [93, 58, 325, 48], [93, 61, 325, 51], [93, 62, 325, 52], [93, 63, 325, 53], [93, 66, 329, 6, "Math"], [93, 70, 329, 10], [93, 71, 329, 11, "cos"], [93, 74, 329, 14], [93, 75, 329, 15, "lat1Rad"], [93, 82, 329, 22], [93, 83, 329, 23], [93, 86, 329, 26, "Math"], [93, 90, 329, 30], [93, 91, 329, 31, "cos"], [93, 94, 329, 34], [93, 95, 329, 35, "lat2Rad"], [93, 102, 329, 42], [93, 103, 329, 43], [93, 106, 333, 8, "Math"], [93, 110, 333, 12], [93, 111, 333, 13, "sin"], [93, 114, 333, 16], [93, 115, 333, 17, "deltaLon"], [93, 123, 333, 25], [93, 126, 333, 28], [93, 127, 333, 29], [93, 128, 333, 30], [93, 131, 333, 33, "Math"], [93, 135, 333, 37], [93, 136, 333, 38, "sin"], [93, 139, 333, 41], [93, 140, 333, 42, "deltaLon"], [93, 148, 333, 50], [93, 151, 333, 53], [93, 152, 333, 54], [93, 153, 333, 55], [94, 6, 337, 4], [94, 12, 337, 10, "c"], [94, 13, 337, 11], [94, 16, 337, 14], [94, 17, 337, 15], [94, 20, 337, 18, "Math"], [94, 24, 337, 22], [94, 25, 337, 23, "atan2"], [94, 30, 337, 28], [94, 31, 337, 29, "Math"], [94, 35, 337, 33], [94, 36, 337, 34, "sqrt"], [94, 40, 337, 38], [94, 41, 337, 39, "a"], [94, 42, 337, 40], [94, 43, 337, 41], [94, 45, 337, 43, "Math"], [94, 49, 337, 47], [94, 50, 337, 48, "sqrt"], [94, 54, 337, 52], [94, 55, 337, 53], [94, 56, 337, 54], [94, 59, 337, 57, "a"], [94, 60, 337, 58], [94, 61, 337, 59], [94, 62, 337, 60], [95, 6, 341, 4], [95, 13, 341, 11, "R"], [95, 14, 341, 12], [95, 17, 341, 15, "c"], [95, 18, 341, 16], [95, 19, 341, 17], [95, 20, 341, 18], [96, 4, 345, 2], [96, 5, 345, 3], [98, 4, 349, 2], [100, 4, 353, 2], [100, 10, 353, 8, "verifyLocation"], [100, 24, 353, 22], [100, 27, 353, 25], [100, 31, 353, 25, "useCallback"], [100, 49, 353, 36], [100, 51, 353, 37], [100, 63, 353, 49], [101, 6, 357, 4], [101, 10, 357, 8, "testingMode"], [101, 21, 357, 19], [101, 23, 357, 21], [102, 8, 361, 6, "setLocationStatus"], [102, 25, 361, 23], [102, 26, 361, 24], [102, 36, 361, 34], [102, 37, 361, 35], [103, 8, 365, 6, "setLocationError"], [103, 24, 365, 22], [103, 25, 365, 23], [103, 29, 365, 27], [103, 30, 365, 28], [104, 8, 369, 6, "setDistance"], [104, 19, 369, 17], [104, 20, 369, 18], [104, 21, 369, 19], [104, 22, 369, 20], [105, 8, 373, 6, "setCurrentLocation"], [105, 26, 373, 24], [105, 27, 373, 25], [105, 31, 373, 29], [105, 32, 373, 30], [106, 8, 377, 6, "setGettingLocation"], [106, 26, 377, 24], [106, 27, 377, 25], [106, 32, 377, 30], [106, 33, 377, 31], [107, 8, 381, 6], [108, 6, 385, 4], [109, 6, 389, 4], [109, 10, 389, 8], [110, 8, 393, 6, "setGettingLocation"], [110, 26, 393, 24], [110, 27, 393, 25], [110, 31, 393, 29], [110, 32, 393, 30], [111, 8, 397, 6, "setLocationError"], [111, 24, 397, 22], [111, 25, 397, 23], [111, 29, 397, 27], [111, 30, 397, 28], [112, 8, 401, 6, "setLocationStatus"], [112, 25, 401, 23], [112, 26, 401, 24], [112, 36, 401, 34], [112, 37, 401, 35], [114, 8, 405, 6], [116, 8, 409, 6], [116, 14, 409, 12], [117, 10, 409, 14, "status"], [118, 8, 409, 21], [118, 9, 409, 22], [118, 12, 409, 25], [118, 18, 409, 31, "Location"], [118, 26, 409, 39], [118, 27, 409, 40, "requestForegroundPermissionsAsync"], [118, 60, 409, 73], [118, 61, 409, 74], [118, 62, 409, 75], [119, 8, 413, 6], [119, 12, 413, 10, "status"], [119, 18, 413, 16], [119, 23, 413, 21], [119, 32, 413, 30], [119, 34, 413, 32], [120, 10, 417, 8], [120, 16, 417, 14, "message"], [120, 23, 417, 21], [120, 26, 421, 10, "Platform"], [120, 43, 421, 18], [120, 44, 421, 19, "OS"], [120, 46, 421, 21], [120, 51, 421, 26], [120, 56, 421, 31], [120, 59, 425, 14], [120, 164, 425, 119], [120, 167, 429, 14], [120, 233, 429, 80], [121, 10, 433, 8, "setLocationError"], [121, 26, 433, 24], [121, 27, 433, 25, "message"], [121, 34, 433, 32], [121, 35, 433, 33], [122, 10, 437, 8, "setLocationStatus"], [122, 27, 437, 25], [122, 28, 437, 26], [122, 35, 437, 33], [122, 36, 437, 34], [123, 10, 441, 8, "<PERSON><PERSON>"], [123, 24, 441, 13], [123, 25, 441, 14, "alert"], [123, 30, 441, 19], [123, 31, 441, 20], [123, 50, 441, 39], [123, 52, 441, 41, "message"], [123, 59, 441, 48], [123, 60, 441, 49], [124, 10, 445, 8], [125, 8, 449, 6], [127, 8, 453, 6], [129, 8, 457, 6], [129, 14, 457, 12, "locationData"], [129, 26, 457, 24], [129, 29, 457, 27], [129, 35, 457, 33, "Location"], [129, 43, 457, 41], [129, 44, 457, 42, "getCurrentPositionAsync"], [129, 67, 457, 65], [129, 68, 457, 66], [130, 10, 461, 8, "accuracy"], [130, 18, 461, 16], [130, 20, 461, 18, "Location"], [130, 28, 461, 26], [130, 29, 461, 27, "Accuracy"], [130, 37, 461, 35], [130, 38, 461, 36, "High"], [130, 42, 461, 40], [131, 10, 465, 8, "timeout"], [131, 17, 465, 15], [131, 19, 465, 17], [131, 24, 465, 22], [132, 10, 469, 8, "maximumAge"], [132, 20, 469, 18], [132, 22, 469, 20], [133, 8, 473, 6], [133, 9, 473, 7], [133, 10, 473, 8], [134, 8, 477, 6], [134, 14, 477, 12, "userLat"], [134, 21, 477, 19], [134, 24, 477, 22, "locationData"], [134, 36, 477, 34], [134, 37, 477, 35, "coords"], [134, 43, 477, 41], [134, 44, 477, 42, "latitude"], [134, 52, 477, 50], [135, 8, 481, 6], [135, 14, 481, 12, "userLon"], [135, 21, 481, 19], [135, 24, 481, 22, "locationData"], [135, 36, 481, 34], [135, 37, 481, 35, "coords"], [135, 43, 481, 41], [135, 44, 481, 42, "longitude"], [135, 53, 481, 51], [136, 8, 485, 6], [136, 14, 485, 12, "questionLat"], [136, 25, 485, 23], [136, 28, 485, 26, "questionLatitude"], [136, 44, 485, 42], [137, 8, 489, 6], [137, 14, 489, 12, "questionLon"], [137, 25, 489, 23], [137, 28, 489, 26, "questionLongitude"], [137, 45, 489, 43], [139, 8, 493, 6], [141, 8, 497, 6], [141, 14, 497, 12, "distanceInMeters"], [141, 30, 497, 28], [141, 33, 497, 31, "calculateDistance"], [141, 50, 497, 48], [141, 51, 501, 8, "userLat"], [141, 58, 501, 15], [141, 60, 505, 8, "userLon"], [141, 67, 505, 15], [141, 69, 509, 8, "questionLat"], [141, 80, 509, 19], [141, 82, 513, 8, "questionLon"], [141, 93, 517, 6], [141, 94, 517, 7], [142, 8, 521, 6, "setDistance"], [142, 19, 521, 17], [142, 20, 521, 18, "Math"], [142, 24, 521, 22], [142, 25, 521, 23, "round"], [142, 30, 521, 28], [142, 31, 521, 29, "distanceInMeters"], [142, 47, 521, 45], [142, 48, 521, 46], [142, 49, 521, 47], [143, 8, 525, 6, "setCurrentLocation"], [143, 26, 525, 24], [143, 27, 525, 25], [144, 10, 529, 8, "latitude"], [144, 18, 529, 16], [144, 20, 529, 18, "userLat"], [144, 27, 529, 25], [145, 10, 533, 8, "longitude"], [145, 19, 533, 17], [145, 21, 533, 19, "userLon"], [146, 8, 537, 6], [146, 9, 537, 7], [146, 10, 537, 8], [148, 8, 541, 6], [150, 8, 545, 6], [150, 14, 545, 12, "maxDistance"], [150, 25, 545, 23], [150, 28, 545, 26], [150, 31, 545, 29], [151, 8, 549, 6], [151, 12, 549, 10, "distanceInMeters"], [151, 28, 549, 26], [151, 32, 549, 30, "maxDistance"], [151, 43, 549, 41], [151, 45, 549, 43], [152, 10, 553, 8, "setLocationStatus"], [152, 27, 553, 25], [152, 28, 553, 26], [152, 38, 553, 36], [152, 39, 553, 37], [153, 8, 557, 6], [153, 9, 557, 7], [153, 15, 557, 13], [154, 10, 561, 8, "setLocationStatus"], [154, 27, 561, 25], [154, 28, 561, 26], [154, 37, 561, 35], [154, 38, 561, 36], [155, 8, 565, 6], [156, 6, 569, 4], [156, 7, 569, 5], [156, 8, 569, 6], [156, 15, 569, 13, "error"], [156, 20, 569, 18], [156, 22, 569, 20], [157, 8, 573, 6, "console"], [157, 15, 573, 13], [157, 16, 573, 14, "error"], [157, 21, 573, 19], [157, 22, 573, 20], [157, 49, 573, 47], [157, 51, 573, 49, "error"], [157, 56, 573, 54], [157, 57, 573, 55], [158, 8, 577, 6], [158, 12, 577, 10, "message"], [158, 19, 577, 17], [158, 22, 577, 20], [158, 92, 577, 90], [159, 8, 581, 6], [159, 12, 581, 10, "error"], [159, 17, 581, 15], [159, 19, 581, 17, "code"], [159, 23, 581, 21], [159, 28, 581, 26], [159, 29, 581, 27], [159, 31, 581, 29], [160, 10, 585, 8, "message"], [160, 17, 585, 15], [160, 20, 585, 18], [160, 128, 585, 126], [161, 8, 589, 6], [161, 9, 589, 7], [161, 15, 589, 13], [161, 19, 589, 17, "error"], [161, 24, 589, 22], [161, 26, 589, 24, "code"], [161, 30, 589, 28], [161, 35, 589, 33], [161, 36, 589, 34], [161, 38, 589, 36], [162, 10, 593, 8, "message"], [162, 17, 593, 15], [162, 20, 593, 18], [162, 112, 593, 110], [163, 8, 597, 6], [163, 9, 597, 7], [163, 15, 597, 13], [163, 19, 597, 17, "error"], [163, 24, 597, 22], [163, 26, 597, 24, "code"], [163, 30, 597, 28], [163, 35, 597, 33], [163, 36, 597, 34], [163, 38, 597, 36], [164, 10, 601, 8, "message"], [164, 17, 601, 15], [164, 20, 601, 18], [164, 67, 601, 65], [165, 8, 605, 6], [165, 9, 605, 7], [165, 15, 605, 13], [165, 19, 605, 17, "Platform"], [165, 36, 605, 25], [165, 37, 605, 26, "OS"], [165, 39, 605, 28], [165, 44, 605, 33], [165, 49, 605, 38], [165, 53, 605, 42], [165, 60, 605, 49, "error"], [165, 65, 605, 54], [165, 67, 605, 56, "message"], [165, 74, 605, 63], [165, 79, 605, 68], [165, 87, 605, 76], [165, 91, 605, 80, "error"], [165, 96, 605, 85], [165, 97, 605, 86, "message"], [165, 104, 605, 93], [165, 105, 605, 94, "toLowerCase"], [165, 116, 605, 105], [165, 117, 605, 106], [165, 118, 605, 107], [165, 119, 605, 108, "includes"], [165, 127, 605, 116], [165, 128, 605, 117], [165, 136, 605, 125], [165, 137, 605, 126], [165, 139, 605, 128], [166, 10, 609, 8, "message"], [166, 17, 609, 15], [166, 20, 609, 18], [166, 135, 609, 133], [167, 8, 613, 6], [168, 8, 617, 6, "setLocationError"], [168, 24, 617, 22], [168, 25, 617, 23, "message"], [168, 32, 617, 30], [168, 33, 617, 31], [169, 8, 621, 6, "setLocationStatus"], [169, 25, 621, 23], [169, 26, 621, 24], [169, 33, 621, 31], [169, 34, 621, 32], [170, 8, 625, 6, "<PERSON><PERSON>"], [170, 22, 625, 11], [170, 23, 625, 12, "alert"], [170, 28, 625, 17], [170, 29, 625, 18], [170, 45, 625, 34], [170, 47, 625, 36, "message"], [170, 54, 625, 43], [170, 55, 625, 44], [171, 6, 629, 4], [171, 7, 629, 5], [171, 16, 629, 14], [172, 8, 633, 6, "setGettingLocation"], [172, 26, 633, 24], [172, 27, 633, 25], [172, 32, 633, 30], [172, 33, 633, 31], [173, 6, 637, 4], [174, 4, 641, 2], [174, 5, 641, 3], [174, 7, 641, 5], [174, 8, 641, 6, "questionLatitude"], [174, 24, 641, 22], [174, 26, 641, 24, "questionLongitude"], [174, 43, 641, 41], [174, 45, 641, 43, "testingMode"], [174, 56, 641, 54], [174, 57, 641, 55], [174, 58, 641, 56], [176, 4, 645, 2], [178, 4, 649, 2], [178, 8, 649, 2, "useEffect"], [178, 24, 649, 11], [178, 26, 649, 12], [178, 32, 649, 18], [179, 6, 653, 4], [179, 10, 653, 8, "testingMode"], [179, 21, 653, 19], [179, 23, 653, 21], [180, 8, 657, 6, "setLocationStatus"], [180, 25, 657, 23], [180, 26, 657, 24], [180, 36, 657, 34], [180, 37, 657, 35], [181, 8, 661, 6, "setLocationError"], [181, 24, 661, 22], [181, 25, 661, 23], [181, 29, 661, 27], [181, 30, 661, 28], [182, 8, 665, 6, "setDistance"], [182, 19, 665, 17], [182, 20, 665, 18], [182, 21, 665, 19], [182, 22, 665, 20], [183, 8, 669, 6, "setCurrentLocation"], [183, 26, 669, 24], [183, 27, 669, 25], [183, 31, 669, 29], [183, 32, 669, 30], [184, 8, 673, 6, "setGettingLocation"], [184, 26, 673, 24], [184, 27, 673, 25], [184, 32, 673, 30], [184, 33, 673, 31], [185, 8, 677, 6], [186, 6, 681, 4], [187, 6, 685, 4, "verifyLocation"], [187, 20, 685, 18], [187, 21, 685, 19], [187, 22, 685, 20], [188, 4, 689, 2], [188, 5, 689, 3], [188, 7, 689, 5], [188, 8, 689, 6, "testingMode"], [188, 19, 689, 17], [188, 21, 689, 19, "verifyLocation"], [188, 35, 689, 33], [188, 36, 689, 34], [188, 37, 689, 35], [189, 4, 693, 2], [189, 10, 693, 8, "handleStartCamera"], [189, 27, 693, 25], [189, 30, 693, 28, "handleStartCamera"], [189, 31, 693, 28], [189, 36, 693, 34], [190, 6, 697, 4, "console"], [190, 13, 697, 11], [190, 14, 697, 12, "log"], [190, 17, 697, 15], [190, 18, 697, 16], [190, 42, 697, 40], [190, 44, 697, 42], [191, 8, 701, 6, "locationStatus"], [191, 22, 701, 20], [192, 8, 705, 6, "testingMode"], [192, 19, 705, 17], [193, 8, 709, 6, "disabled"], [193, 16, 709, 14], [193, 18, 709, 16, "locationStatus"], [193, 32, 709, 30], [193, 37, 709, 35], [193, 47, 709, 45], [193, 51, 709, 49], [193, 52, 709, 50, "testingMode"], [193, 63, 709, 61], [194, 8, 713, 6, "shouldEnable"], [194, 20, 713, 18], [194, 22, 713, 20, "locationStatus"], [194, 36, 713, 34], [194, 41, 713, 39], [194, 51, 713, 49], [194, 55, 713, 53, "testingMode"], [194, 66, 713, 64], [195, 8, 717, 6, "existingCameraResult"], [195, 28, 717, 26], [195, 30, 717, 28, "cameraResult"], [195, 42, 717, 40], [195, 43, 717, 42], [196, 6, 721, 4], [196, 7, 721, 5], [196, 8, 721, 6], [197, 6, 725, 4], [197, 10, 725, 8, "locationStatus"], [197, 24, 725, 22], [197, 29, 725, 27], [197, 39, 725, 37], [197, 43, 725, 41], [197, 44, 725, 42, "testingMode"], [197, 55, 725, 53], [197, 57, 725, 55], [198, 8, 729, 6, "<PERSON><PERSON>"], [198, 22, 729, 11], [198, 23, 729, 12, "alert"], [198, 28, 729, 17], [198, 29, 733, 8], [198, 48, 733, 27], [198, 50, 737, 8, "locationStatus"], [198, 64, 737, 22], [198, 69, 737, 27], [198, 78, 737, 36], [198, 81, 741, 12], [198, 92, 741, 23, "distance"], [198, 100, 741, 31], [198, 104, 741, 35], [198, 105, 741, 36], [198, 180, 741, 111], [198, 183, 745, 12], [198, 219, 749, 6], [198, 220, 749, 7], [199, 8, 753, 6], [200, 6, 757, 4], [201, 6, 761, 4, "setShowCamera"], [201, 19, 761, 17], [201, 20, 761, 18], [201, 24, 761, 22], [201, 25, 761, 23], [202, 4, 765, 2], [202, 5, 765, 3], [203, 4, 769, 2], [203, 10, 769, 8, "handleCameraComplete"], [203, 30, 769, 28], [203, 33, 769, 31], [203, 37, 769, 31, "useCallback"], [203, 55, 769, 42], [203, 57, 769, 44, "result"], [203, 63, 769, 50], [203, 67, 769, 55], [204, 6, 773, 4, "console"], [204, 13, 773, 11], [204, 14, 773, 12, "log"], [204, 17, 773, 15], [204, 18, 773, 16], [204, 43, 773, 41], [204, 45, 773, 43, "result"], [204, 51, 773, 49], [204, 52, 773, 50], [204, 53, 773, 51], [204, 54, 773, 52], [205, 6, 774, 4, "console"], [205, 13, 774, 11], [205, 14, 774, 12, "log"], [205, 17, 774, 15], [205, 18, 774, 16], [205, 59, 774, 57], [205, 61, 774, 59], [206, 8, 775, 6, "imageUrl"], [206, 16, 775, 14], [206, 18, 775, 16, "result"], [206, 24, 775, 22], [206, 25, 775, 23, "imageUrl"], [206, 33, 775, 31], [207, 8, 776, 6, "localUri"], [207, 16, 776, 14], [207, 18, 776, 16, "result"], [207, 24, 776, 22], [207, 25, 776, 23, "localUri"], [207, 33, 776, 31], [208, 8, 777, 6, "uri"], [208, 11, 777, 9], [208, 13, 777, 11, "result"], [208, 19, 777, 17], [208, 20, 777, 18, "uri"], [208, 23, 777, 21], [209, 8, 778, 6, "publicUrl"], [209, 17, 778, 15], [209, 19, 778, 17, "result"], [209, 25, 778, 23], [209, 26, 778, 24, "publicUrl"], [209, 35, 778, 33], [210, 8, 779, 6, "timestamp"], [210, 17, 779, 15], [210, 19, 779, 17, "result"], [210, 25, 779, 23], [210, 26, 779, 24, "timestamp"], [211, 6, 780, 4], [211, 7, 780, 5], [211, 8, 780, 6], [213, 6, 784, 4], [215, 6, 788, 4], [215, 10, 788, 8, "imageUri"], [215, 18, 788, 16], [215, 21, 788, 19, "result"], [215, 27, 788, 25], [215, 28, 788, 26, "imageUrl"], [215, 36, 788, 34], [215, 40, 788, 38, "result"], [215, 46, 788, 44], [215, 47, 788, 45, "localUri"], [215, 55, 788, 53], [215, 59, 788, 57, "result"], [215, 65, 788, 63], [215, 66, 788, 64, "uri"], [215, 69, 788, 67], [215, 73, 788, 71, "result"], [215, 79, 788, 77], [215, 80, 788, 78, "publicUrl"], [215, 89, 788, 87], [216, 6, 789, 4, "console"], [216, 13, 789, 11], [216, 14, 789, 12, "log"], [216, 17, 789, 15], [216, 18, 789, 16], [216, 52, 789, 50], [216, 54, 789, 52, "imageUri"], [216, 62, 789, 60], [216, 63, 789, 61], [218, 6, 793, 4], [220, 6, 797, 4], [220, 10, 797, 8, "imageUri"], [220, 18, 797, 16], [220, 22, 797, 20, "imageUri"], [220, 30, 797, 28], [220, 31, 797, 29, "startsWith"], [220, 41, 797, 39], [220, 42, 797, 40], [220, 54, 797, 52], [220, 55, 797, 53], [220, 57, 797, 55], [221, 8, 801, 6], [223, 8, 805, 6], [223, 12, 805, 10], [223, 13, 805, 11, "imageUri"], [223, 21, 805, 19], [223, 22, 805, 20, "includes"], [223, 30, 805, 28], [223, 31, 805, 29], [223, 40, 805, 38], [223, 41, 805, 39], [223, 43, 805, 41], [224, 10, 809, 8, "console"], [224, 17, 809, 15], [224, 18, 809, 16, "error"], [224, 23, 809, 21], [224, 24, 809, 22], [224, 50, 809, 48], [224, 52, 809, 50, "imageUri"], [224, 60, 809, 58], [224, 61, 809, 59, "substring"], [224, 70, 809, 68], [224, 71, 809, 69], [224, 72, 809, 70], [224, 74, 809, 72], [224, 76, 809, 74], [224, 77, 809, 75], [224, 78, 809, 76], [225, 10, 813, 8, "imageUri"], [225, 18, 813, 16], [225, 21, 813, 19], [225, 25, 813, 23], [226, 8, 817, 6], [227, 6, 821, 4], [229, 6, 825, 4], [231, 6, 829, 4], [231, 10, 829, 8], [231, 11, 829, 9, "imageUri"], [231, 19, 829, 17], [231, 23, 829, 21, "__DEV__"], [231, 30, 829, 28], [231, 32, 829, 30], [232, 8, 833, 6, "console"], [232, 15, 833, 13], [232, 16, 833, 14, "warn"], [232, 20, 833, 18], [232, 21, 833, 19], [232, 60, 833, 58], [232, 61, 833, 59], [233, 8, 837, 6, "imageUri"], [233, 16, 837, 14], [233, 19, 837, 17], [233, 90, 837, 88], [234, 6, 841, 4], [235, 6, 845, 4, "setCapturedPhotoUri"], [235, 25, 845, 23], [235, 26, 845, 24, "imageUri"], [235, 34, 845, 32], [235, 35, 845, 33], [237, 6, 847, 4], [238, 6, 848, 4], [238, 10, 848, 8, "imageUri"], [238, 18, 848, 16], [238, 22, 848, 20, "imageUri"], [238, 30, 848, 28], [238, 31, 848, 29, "startsWith"], [238, 41, 848, 39], [238, 42, 848, 40], [238, 49, 848, 47], [238, 50, 848, 48], [238, 52, 848, 50], [239, 8, 849, 6, "console"], [239, 15, 849, 13], [239, 16, 849, 14, "log"], [239, 19, 849, 17], [239, 20, 849, 18], [239, 69, 849, 67], [239, 70, 849, 68], [240, 8, 850, 6, "fetch"], [240, 13, 850, 11], [240, 14, 850, 12, "imageUri"], [240, 22, 850, 20], [240, 23, 850, 21], [240, 24, 851, 9, "then"], [240, 28, 851, 13], [240, 29, 851, 14, "response"], [240, 37, 851, 22], [240, 41, 851, 26], [241, 10, 852, 10, "console"], [241, 17, 852, 17], [241, 18, 852, 18, "log"], [241, 21, 852, 21], [241, 22, 852, 22], [241, 64, 852, 64], [241, 66, 852, 66, "response"], [241, 74, 852, 74], [241, 75, 852, 75, "ok"], [241, 77, 852, 77], [241, 79, 852, 79, "response"], [241, 87, 852, 87], [241, 88, 852, 88, "status"], [241, 94, 852, 94], [241, 95, 852, 95], [242, 10, 853, 10], [242, 17, 853, 17, "response"], [242, 25, 853, 25], [242, 26, 853, 26, "blob"], [242, 30, 853, 30], [242, 31, 853, 31], [242, 32, 853, 32], [243, 8, 854, 8], [243, 9, 854, 9], [243, 10, 854, 10], [243, 11, 855, 9, "then"], [243, 15, 855, 13], [243, 16, 855, 14, "blob"], [243, 20, 855, 18], [243, 24, 855, 22], [244, 10, 856, 10, "console"], [244, 17, 856, 17], [244, 18, 856, 18, "log"], [244, 21, 856, 21], [244, 22, 856, 22], [244, 48, 856, 48], [244, 50, 856, 50, "blob"], [244, 54, 856, 54], [244, 55, 856, 55, "size"], [244, 59, 856, 59], [244, 61, 856, 61], [244, 75, 856, 75], [244, 77, 856, 77, "blob"], [244, 81, 856, 81], [244, 82, 856, 82, "type"], [244, 86, 856, 86], [244, 87, 856, 87], [245, 8, 857, 8], [245, 9, 857, 9], [245, 10, 857, 10], [245, 11, 858, 9, "catch"], [245, 16, 858, 14], [245, 17, 858, 15, "error"], [245, 22, 858, 20], [245, 26, 858, 24], [246, 10, 859, 10, "console"], [246, 17, 859, 17], [246, 18, 859, 18, "error"], [246, 23, 859, 23], [246, 24, 859, 24], [246, 62, 859, 62], [246, 64, 859, 64, "error"], [246, 69, 859, 69], [246, 70, 859, 70], [247, 8, 860, 8], [247, 9, 860, 9], [247, 10, 860, 10], [248, 6, 861, 4], [250, 6, 865, 4], [252, 6, 869, 4], [252, 12, 869, 10, "normalizedResult"], [252, 28, 869, 26], [252, 31, 869, 29], [253, 8, 873, 6], [253, 11, 873, 9, "result"], [253, 17, 873, 15], [254, 8, 877, 6, "imageUrl"], [254, 16, 877, 14], [254, 18, 877, 16, "imageUri"], [254, 26, 877, 24], [255, 8, 881, 6, "localUri"], [255, 16, 881, 14], [255, 18, 881, 16, "imageUri"], [255, 26, 881, 24], [256, 8, 885, 6], [258, 8, 889, 6, "originalUri"], [258, 19, 889, 17], [258, 21, 889, 19, "result"], [258, 27, 889, 25], [258, 28, 889, 26, "imageUrl"], [258, 36, 889, 34], [258, 40, 889, 38, "result"], [258, 46, 889, 44], [258, 47, 889, 45, "localUri"], [258, 55, 889, 53], [258, 59, 889, 57, "result"], [258, 65, 889, 63], [258, 66, 889, 64, "uri"], [258, 69, 889, 67], [258, 73, 889, 71, "result"], [258, 79, 889, 77], [258, 80, 889, 78, "publicUrl"], [259, 6, 893, 4], [259, 7, 893, 5], [260, 6, 897, 4, "console"], [260, 13, 897, 11], [260, 14, 897, 12, "log"], [260, 17, 897, 15], [260, 18, 897, 16], [260, 47, 897, 45], [260, 49, 897, 47, "imageUri"], [260, 57, 897, 55], [260, 58, 897, 56], [261, 6, 901, 4, "console"], [261, 13, 901, 11], [261, 14, 901, 12, "log"], [261, 17, 901, 15], [261, 18, 901, 16], [261, 43, 901, 41], [261, 45, 901, 43, "normalizedResult"], [261, 61, 901, 59], [261, 62, 901, 60], [262, 6, 905, 4, "setCameraResult"], [262, 21, 905, 19], [262, 22, 905, 20, "normalizedResult"], [262, 38, 905, 36], [262, 39, 905, 37], [263, 6, 909, 4, "setShowCamera"], [263, 19, 909, 17], [263, 20, 909, 18], [263, 25, 909, 23], [263, 26, 909, 24], [265, 6, 913, 4], [266, 4, 917, 2], [266, 5, 917, 3], [266, 7, 917, 5], [266, 9, 917, 7], [266, 10, 917, 8], [267, 4, 921, 2], [267, 10, 921, 8, "handleCameraCancel"], [267, 28, 921, 26], [267, 31, 921, 29], [267, 35, 921, 29, "useCallback"], [267, 53, 921, 40], [267, 55, 921, 41], [267, 61, 921, 47], [268, 6, 925, 4, "setShowCamera"], [268, 19, 925, 17], [268, 20, 925, 18], [268, 25, 925, 23], [268, 26, 925, 24], [269, 6, 929, 4, "setCameraResult"], [269, 21, 929, 19], [269, 22, 929, 20], [269, 26, 929, 24], [269, 27, 929, 25], [270, 6, 933, 4, "setCapturedPhotoUri"], [270, 25, 933, 23], [270, 26, 933, 24], [270, 30, 933, 28], [270, 31, 933, 29], [271, 4, 937, 2], [271, 5, 937, 3], [271, 7, 937, 5], [271, 9, 937, 7], [271, 10, 937, 8], [272, 4, 941, 2], [272, 10, 941, 8, "submitResponse"], [272, 24, 941, 22], [272, 27, 941, 25], [272, 33, 941, 25, "submitResponse"], [272, 34, 941, 25], [272, 39, 941, 37], [273, 6, 945, 4], [273, 10, 945, 8, "locationStatus"], [273, 24, 945, 22], [273, 29, 945, 27], [273, 39, 945, 37], [273, 43, 945, 41], [273, 44, 945, 42, "testingMode"], [273, 55, 945, 53], [273, 57, 945, 55], [274, 8, 949, 6, "<PERSON><PERSON>"], [274, 22, 949, 11], [274, 23, 949, 12, "alert"], [274, 28, 949, 17], [274, 29, 949, 18], [274, 48, 949, 37], [274, 50, 949, 39], [274, 86, 949, 75], [274, 87, 949, 76], [275, 8, 953, 6], [276, 6, 957, 4], [277, 6, 961, 4], [277, 10, 961, 8], [277, 11, 961, 9, "cameraResult"], [277, 23, 961, 21], [277, 25, 961, 23], [278, 8, 965, 6, "<PERSON><PERSON>"], [278, 22, 965, 11], [278, 23, 965, 12, "alert"], [278, 28, 965, 17], [278, 29, 965, 18], [278, 44, 965, 33], [278, 46, 965, 35], [278, 85, 965, 74], [278, 86, 965, 75], [279, 8, 969, 6], [280, 6, 973, 4], [281, 6, 977, 4], [281, 10, 977, 8], [281, 11, 977, 9, "response"], [281, 19, 977, 17], [281, 20, 977, 18, "trim"], [281, 24, 977, 22], [281, 25, 977, 23], [281, 26, 977, 24], [281, 28, 977, 26], [282, 8, 981, 6, "<PERSON><PERSON>"], [282, 22, 981, 11], [282, 23, 981, 12, "alert"], [282, 28, 981, 17], [282, 29, 985, 8], [282, 43, 985, 22], [282, 45, 989, 8], [282, 97, 993, 6], [282, 98, 993, 7], [283, 8, 997, 6], [284, 6, 1001, 4], [285, 6, 1005, 4, "setSubmitting"], [285, 19, 1005, 17], [285, 20, 1005, 18], [285, 24, 1005, 22], [285, 25, 1005, 23], [286, 6, 1009, 4], [286, 10, 1009, 8], [287, 8, 1013, 6], [289, 8, 1017, 6], [289, 14, 1017, 12, "responseData"], [289, 26, 1017, 24], [289, 29, 1017, 27], [290, 10, 1021, 8, "questionId"], [290, 20, 1021, 18], [290, 22, 1021, 20, "id"], [290, 24, 1021, 22], [291, 10, 1025, 8, "textResponse"], [291, 22, 1025, 20], [291, 24, 1025, 22, "response"], [291, 32, 1025, 30], [291, 33, 1025, 31, "trim"], [291, 37, 1025, 35], [291, 38, 1025, 36], [291, 39, 1025, 37], [292, 10, 1029, 8, "imageUrl"], [292, 18, 1029, 16], [292, 20, 1029, 18, "cameraResult"], [292, 32, 1029, 30], [292, 33, 1029, 31, "imageUrl"], [292, 41, 1029, 39], [293, 10, 1033, 8, "challengeCode"], [293, 23, 1033, 21], [293, 25, 1033, 23, "cameraResult"], [293, 37, 1033, 35], [293, 38, 1033, 36, "challengeCode"], [293, 51, 1033, 49], [294, 10, 1037, 8, "timestamp"], [294, 19, 1037, 17], [294, 21, 1037, 19, "cameraResult"], [294, 33, 1037, 31], [294, 34, 1037, 32, "timestamp"], [294, 43, 1037, 41], [295, 10, 1041, 8, "userLocation"], [295, 22, 1041, 20], [295, 24, 1041, 22, "currentLocation"], [295, 39, 1041, 37], [296, 10, 1045, 8, "distanceFromQuestion"], [296, 30, 1045, 28], [296, 32, 1045, 30, "distance"], [296, 40, 1045, 38], [297, 10, 1049, 8, "testingMode"], [297, 21, 1049, 19], [297, 23, 1049, 21, "testingMode"], [297, 34, 1049, 32], [297, 35, 1049, 34], [298, 8, 1053, 6], [298, 9, 1053, 7], [299, 8, 1057, 6, "console"], [299, 15, 1057, 13], [299, 16, 1057, 14, "log"], [299, 19, 1057, 17], [299, 20, 1057, 18], [299, 42, 1057, 40], [299, 44, 1057, 42, "responseData"], [299, 56, 1057, 54], [299, 57, 1057, 55], [301, 8, 1061, 6], [303, 8, 1065, 6], [303, 14, 1065, 12], [303, 18, 1065, 16, "Promise"], [303, 25, 1065, 23], [303, 26, 1065, 25, "resolve"], [303, 33, 1065, 32], [303, 37, 1065, 37, "setTimeout"], [303, 47, 1065, 47], [303, 48, 1065, 48, "resolve"], [303, 55, 1065, 55], [303, 57, 1065, 57], [303, 61, 1065, 61], [303, 62, 1065, 62], [303, 63, 1065, 63], [304, 8, 1069, 6, "<PERSON><PERSON>"], [304, 22, 1069, 11], [304, 23, 1069, 12, "alert"], [304, 28, 1069, 17], [304, 29, 1073, 8], [304, 50, 1073, 29], [304, 52, 1077, 8, "testingMode"], [304, 63, 1077, 19], [304, 66, 1081, 12], [304, 131, 1081, 77], [304, 134, 1085, 12], [304, 153, 1085, 31, "question"], [304, 161, 1085, 39], [304, 162, 1085, 40, "reward"], [304, 168, 1085, 46], [304, 169, 1085, 47, "toFixed"], [304, 176, 1085, 54], [304, 177, 1085, 55], [304, 178, 1085, 56], [304, 179, 1085, 57], [304, 225, 1085, 103], [304, 227, 1089, 8], [304, 228, 1093, 10], [305, 10, 1097, 12, "text"], [305, 14, 1097, 16], [305, 16, 1097, 18], [305, 20, 1097, 22], [306, 10, 1101, 12, "onPress"], [306, 17, 1101, 19], [306, 19, 1101, 21, "onPress"], [306, 20, 1101, 21], [306, 25, 1101, 27, "router"], [306, 43, 1101, 33], [306, 44, 1101, 34, "back"], [306, 48, 1101, 38], [306, 49, 1101, 39], [307, 8, 1105, 10], [307, 9, 1105, 11], [307, 10, 1113, 6], [307, 11, 1113, 7], [308, 6, 1117, 4], [308, 7, 1117, 5], [308, 8, 1117, 6], [308, 15, 1117, 13, "error"], [308, 20, 1117, 18], [308, 22, 1117, 20], [309, 8, 1121, 6, "console"], [309, 15, 1121, 13], [309, 16, 1121, 14, "error"], [309, 21, 1121, 19], [309, 22, 1121, 20], [309, 50, 1121, 48], [309, 52, 1121, 50, "error"], [309, 57, 1121, 55], [309, 58, 1121, 56], [310, 8, 1125, 6, "<PERSON><PERSON>"], [310, 22, 1125, 11], [310, 23, 1125, 12, "alert"], [310, 28, 1125, 17], [310, 29, 1125, 18], [310, 36, 1125, 25], [310, 38, 1125, 27], [310, 84, 1125, 73], [310, 85, 1125, 74], [311, 6, 1129, 4], [311, 7, 1129, 5], [311, 16, 1129, 14], [312, 8, 1133, 6, "setSubmitting"], [312, 21, 1133, 19], [312, 22, 1133, 20], [312, 27, 1133, 25], [312, 28, 1133, 26], [313, 6, 1137, 4], [314, 4, 1141, 2], [314, 5, 1141, 3], [316, 4, 1145, 2], [318, 4, 1149, 2], [318, 10, 1149, 8, "LocationStatus"], [318, 24, 1149, 22], [318, 27, 1149, 25, "LocationStatus"], [318, 28, 1149, 25], [318, 33, 1149, 31], [319, 6, 1153, 4], [319, 12, 1153, 10, "getStatusConfig"], [319, 27, 1153, 25], [319, 30, 1153, 28, "getStatusConfig"], [319, 31, 1153, 28], [319, 36, 1153, 34], [320, 8, 1157, 6], [320, 16, 1157, 14, "locationStatus"], [320, 30, 1157, 28], [321, 10, 1161, 8], [321, 15, 1161, 13], [321, 25, 1161, 23], [322, 12, 1165, 10], [322, 19, 1165, 17], [323, 14, 1169, 12, "color"], [323, 19, 1169, 17], [323, 21, 1169, 19], [323, 30, 1169, 28], [324, 14, 1173, 12, "bgColor"], [324, 21, 1173, 19], [324, 23, 1173, 21], [324, 32, 1173, 30], [325, 14, 1177, 12, "icon"], [325, 18, 1177, 16], [325, 33, 1177, 18], [325, 37, 1177, 18, "_jsxDevRuntime"], [325, 51, 1177, 18], [325, 52, 1177, 18, "jsxDEV"], [325, 58, 1177, 18], [325, 60, 1177, 19, "_lucideReactNative"], [325, 78, 1177, 19], [325, 79, 1177, 19, "Navigation"], [325, 89, 1177, 29], [326, 16, 1177, 30, "size"], [326, 20, 1177, 34], [326, 22, 1177, 36], [326, 24, 1177, 39], [327, 16, 1177, 40, "color"], [327, 21, 1177, 45], [327, 23, 1177, 46], [328, 14, 1177, 55], [329, 16, 1177, 55, "fileName"], [329, 24, 1177, 55], [329, 26, 1177, 55, "_jsxFileName"], [329, 38, 1177, 55], [330, 16, 1177, 55, "lineNumber"], [330, 26, 1177, 55], [331, 16, 1177, 55, "columnNumber"], [331, 28, 1177, 55], [332, 14, 1177, 55], [332, 21, 1177, 57], [332, 22, 1177, 58], [333, 14, 1181, 12, "title"], [333, 19, 1181, 17], [333, 21, 1181, 19], [333, 40, 1181, 38], [334, 14, 1185, 12, "message"], [334, 21, 1185, 19], [334, 23, 1185, 21, "gettingLocation"], [334, 38, 1185, 36], [334, 41, 1189, 16], [334, 72, 1189, 47], [334, 75, 1193, 16], [335, 12, 1197, 10], [335, 13, 1197, 11], [336, 10, 1201, 8], [336, 15, 1201, 13], [336, 25, 1201, 23], [337, 12, 1205, 10], [337, 19, 1205, 17], [338, 14, 1209, 12, "color"], [338, 19, 1209, 17], [338, 21, 1209, 19], [338, 30, 1209, 28], [339, 14, 1213, 12, "bgColor"], [339, 21, 1213, 19], [339, 23, 1213, 21], [339, 32, 1213, 30], [340, 14, 1217, 12, "icon"], [340, 18, 1217, 16], [340, 33, 1217, 18], [340, 37, 1217, 18, "_jsxDevRuntime"], [340, 51, 1217, 18], [340, 52, 1217, 18, "jsxDEV"], [340, 58, 1217, 18], [340, 60, 1217, 19, "_lucideReactNative"], [340, 78, 1217, 19], [340, 79, 1217, 19, "CheckCircle2"], [340, 91, 1217, 31], [341, 16, 1217, 32, "size"], [341, 20, 1217, 36], [341, 22, 1217, 38], [341, 24, 1217, 41], [342, 16, 1217, 42, "color"], [342, 21, 1217, 47], [342, 23, 1217, 48], [343, 14, 1217, 57], [344, 16, 1217, 57, "fileName"], [344, 24, 1217, 57], [344, 26, 1217, 57, "_jsxFileName"], [344, 38, 1217, 57], [345, 16, 1217, 57, "lineNumber"], [345, 26, 1217, 57], [346, 16, 1217, 57, "columnNumber"], [346, 28, 1217, 57], [347, 14, 1217, 57], [347, 21, 1217, 59], [347, 22, 1217, 60], [348, 14, 1221, 12, "title"], [348, 19, 1221, 17], [348, 21, 1221, 19], [348, 40, 1221, 38], [349, 14, 1225, 12, "message"], [349, 21, 1225, 19], [349, 23, 1225, 21], [349, 33, 1225, 31, "distance"], [349, 41, 1225, 39], [349, 45, 1225, 43], [349, 46, 1225, 44], [350, 12, 1229, 10], [350, 13, 1229, 11], [351, 10, 1233, 8], [351, 15, 1233, 13], [351, 24, 1233, 22], [352, 12, 1237, 10], [352, 19, 1237, 17], [353, 14, 1241, 12, "color"], [353, 19, 1241, 17], [353, 21, 1241, 19], [353, 30, 1241, 28], [354, 14, 1245, 12, "bgColor"], [354, 21, 1245, 19], [354, 23, 1245, 21], [354, 32, 1245, 30], [355, 14, 1249, 12, "icon"], [355, 18, 1249, 16], [355, 33, 1249, 18], [355, 37, 1249, 18, "_jsxDevRuntime"], [355, 51, 1249, 18], [355, 52, 1249, 18, "jsxDEV"], [355, 58, 1249, 18], [355, 60, 1249, 19, "_lucideReactNative"], [355, 78, 1249, 19], [355, 79, 1249, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [355, 92, 1249, 32], [356, 16, 1249, 33, "size"], [356, 20, 1249, 37], [356, 22, 1249, 39], [356, 24, 1249, 42], [357, 16, 1249, 43, "color"], [357, 21, 1249, 48], [357, 23, 1249, 49], [358, 14, 1249, 58], [359, 16, 1249, 58, "fileName"], [359, 24, 1249, 58], [359, 26, 1249, 58, "_jsxFileName"], [359, 38, 1249, 58], [360, 16, 1249, 58, "lineNumber"], [360, 26, 1249, 58], [361, 16, 1249, 58, "columnNumber"], [361, 28, 1249, 58], [362, 14, 1249, 58], [362, 21, 1249, 60], [362, 22, 1249, 61], [363, 14, 1253, 12, "title"], [363, 19, 1253, 17], [363, 21, 1253, 19], [363, 35, 1253, 33], [364, 14, 1257, 12, "message"], [364, 21, 1257, 19], [364, 23, 1257, 21], [364, 33, 1257, 31, "distance"], [364, 41, 1257, 39], [364, 45, 1257, 43], [364, 46, 1257, 44], [365, 12, 1261, 10], [365, 13, 1261, 11], [366, 10, 1265, 8], [366, 15, 1265, 13], [366, 22, 1265, 20], [367, 12, 1269, 10], [367, 19, 1269, 17], [368, 14, 1273, 12, "color"], [368, 19, 1273, 17], [368, 21, 1273, 19], [368, 30, 1273, 28], [369, 14, 1277, 12, "bgColor"], [369, 21, 1277, 19], [369, 23, 1277, 21], [369, 32, 1277, 30], [370, 14, 1281, 12, "icon"], [370, 18, 1281, 16], [370, 33, 1281, 18], [370, 37, 1281, 18, "_jsxDevRuntime"], [370, 51, 1281, 18], [370, 52, 1281, 18, "jsxDEV"], [370, 58, 1281, 18], [370, 60, 1281, 19, "_lucideReactNative"], [370, 78, 1281, 19], [370, 79, 1281, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [370, 92, 1281, 32], [371, 16, 1281, 33, "size"], [371, 20, 1281, 37], [371, 22, 1281, 39], [371, 24, 1281, 42], [372, 16, 1281, 43, "color"], [372, 21, 1281, 48], [372, 23, 1281, 49], [373, 14, 1281, 58], [374, 16, 1281, 58, "fileName"], [374, 24, 1281, 58], [374, 26, 1281, 58, "_jsxFileName"], [374, 38, 1281, 58], [375, 16, 1281, 58, "lineNumber"], [375, 26, 1281, 58], [376, 16, 1281, 58, "columnNumber"], [376, 28, 1281, 58], [377, 14, 1281, 58], [377, 21, 1281, 60], [377, 22, 1281, 61], [378, 14, 1285, 12, "title"], [378, 19, 1285, 17], [378, 21, 1285, 19], [378, 37, 1285, 35], [379, 14, 1289, 12, "message"], [379, 21, 1289, 19], [379, 23, 1289, 21], [380, 12, 1293, 10], [380, 13, 1293, 11], [381, 10, 1297, 8], [382, 12, 1301, 10], [382, 19, 1301, 17], [383, 14, 1305, 12, "color"], [383, 19, 1305, 17], [383, 21, 1305, 19], [383, 30, 1305, 28], [384, 14, 1309, 12, "bgColor"], [384, 21, 1309, 19], [384, 23, 1309, 21], [384, 32, 1309, 30], [385, 14, 1313, 12, "icon"], [385, 18, 1313, 16], [385, 33, 1313, 18], [385, 37, 1313, 18, "_jsxDevRuntime"], [385, 51, 1313, 18], [385, 52, 1313, 18, "jsxDEV"], [385, 58, 1313, 18], [385, 60, 1313, 19, "_lucideReactNative"], [385, 78, 1313, 19], [385, 79, 1313, 19, "Navigation"], [385, 89, 1313, 29], [386, 16, 1313, 30, "size"], [386, 20, 1313, 34], [386, 22, 1313, 36], [386, 24, 1313, 39], [387, 16, 1313, 40, "color"], [387, 21, 1313, 45], [387, 23, 1313, 46], [388, 14, 1313, 55], [389, 16, 1313, 55, "fileName"], [389, 24, 1313, 55], [389, 26, 1313, 55, "_jsxFileName"], [389, 38, 1313, 55], [390, 16, 1313, 55, "lineNumber"], [390, 26, 1313, 55], [391, 16, 1313, 55, "columnNumber"], [391, 28, 1313, 55], [392, 14, 1313, 55], [392, 21, 1313, 57], [392, 22, 1313, 58], [393, 14, 1317, 12, "title"], [393, 19, 1317, 17], [393, 21, 1317, 19], [393, 37, 1317, 35], [394, 14, 1321, 12, "message"], [394, 21, 1321, 19], [394, 23, 1321, 21], [395, 12, 1325, 10], [395, 13, 1325, 11], [396, 8, 1329, 6], [397, 6, 1333, 4], [397, 7, 1333, 5], [398, 6, 1337, 4], [398, 12, 1337, 10, "config"], [398, 18, 1337, 16], [398, 21, 1337, 19, "getStatusConfig"], [398, 36, 1337, 34], [398, 37, 1337, 35], [398, 38, 1337, 36], [399, 6, 1341, 4], [399, 26, 1345, 6], [399, 30, 1345, 6, "_jsxDevRuntime"], [399, 44, 1345, 6], [399, 45, 1345, 6, "jsxDEV"], [399, 51, 1345, 6], [399, 53, 1345, 7, "_View"], [399, 58, 1345, 7], [399, 59, 1345, 7, "default"], [399, 66, 1345, 11], [400, 8, 1349, 8, "style"], [400, 13, 1349, 13], [400, 15, 1349, 15], [401, 10, 1353, 10, "backgroundColor"], [401, 25, 1353, 25], [401, 27, 1353, 27, "config"], [401, 33, 1353, 33], [401, 34, 1353, 34, "bgColor"], [401, 41, 1353, 41], [402, 10, 1357, 10, "borderRadius"], [402, 22, 1357, 22], [402, 24, 1357, 24], [402, 26, 1357, 26], [403, 10, 1361, 10, "padding"], [403, 17, 1361, 17], [403, 19, 1361, 19], [403, 21, 1361, 21], [404, 10, 1365, 10, "marginBottom"], [404, 22, 1365, 22], [404, 24, 1365, 24], [404, 26, 1365, 26], [405, 10, 1369, 10, "borderWidth"], [405, 21, 1369, 21], [405, 23, 1369, 23], [405, 24, 1369, 24], [406, 10, 1373, 10, "borderColor"], [406, 21, 1373, 21], [406, 23, 1373, 23, "config"], [406, 29, 1373, 29], [406, 30, 1373, 30, "color"], [406, 35, 1373, 35], [406, 38, 1373, 38], [407, 8, 1377, 8], [407, 9, 1377, 10], [408, 8, 1377, 10, "children"], [408, 16, 1377, 10], [408, 32, 1385, 8], [408, 36, 1385, 8, "_jsxDevRuntime"], [408, 50, 1385, 8], [408, 51, 1385, 8, "jsxDEV"], [408, 57, 1385, 8], [408, 59, 1385, 9, "_View"], [408, 64, 1385, 9], [408, 65, 1385, 9, "default"], [408, 72, 1385, 13], [409, 10, 1389, 10, "style"], [409, 15, 1389, 15], [409, 17, 1389, 17], [410, 12, 1393, 12, "flexDirection"], [410, 25, 1393, 25], [410, 27, 1393, 27], [410, 32, 1393, 32], [411, 12, 1397, 12, "alignItems"], [411, 22, 1397, 22], [411, 24, 1397, 24], [411, 32, 1397, 32], [412, 12, 1401, 12, "marginBottom"], [412, 24, 1401, 24], [412, 26, 1401, 26], [413, 10, 1405, 10], [413, 11, 1405, 12], [414, 10, 1405, 12, "children"], [414, 18, 1405, 12], [414, 21, 1413, 11, "config"], [414, 27, 1413, 17], [414, 28, 1413, 18, "icon"], [414, 32, 1413, 22], [414, 47, 1417, 10], [414, 51, 1417, 10, "_jsxDevRuntime"], [414, 65, 1417, 10], [414, 66, 1417, 10, "jsxDEV"], [414, 72, 1417, 10], [414, 74, 1417, 11, "_Text"], [414, 79, 1417, 11], [414, 80, 1417, 11, "default"], [414, 87, 1417, 15], [415, 12, 1421, 12, "style"], [415, 17, 1421, 17], [415, 19, 1421, 19], [416, 14, 1425, 14, "fontSize"], [416, 22, 1425, 22], [416, 24, 1425, 24], [416, 26, 1425, 26], [417, 14, 1429, 14, "fontWeight"], [417, 24, 1429, 24], [417, 26, 1429, 26], [417, 31, 1429, 31], [418, 14, 1433, 14, "color"], [418, 19, 1433, 19], [418, 21, 1433, 21, "config"], [418, 27, 1433, 27], [418, 28, 1433, 28, "color"], [418, 33, 1433, 33], [419, 14, 1437, 14, "marginLeft"], [419, 24, 1437, 24], [419, 26, 1437, 26], [420, 12, 1441, 12], [420, 13, 1441, 14], [421, 12, 1441, 14, "children"], [421, 20, 1441, 14], [421, 22, 1449, 13, "config"], [421, 28, 1449, 19], [421, 29, 1449, 20, "title"], [422, 10, 1449, 25], [423, 12, 1449, 25, "fileName"], [423, 20, 1449, 25], [423, 22, 1449, 25, "_jsxFileName"], [423, 34, 1449, 25], [424, 12, 1449, 25, "lineNumber"], [424, 22, 1449, 25], [425, 12, 1449, 25, "columnNumber"], [425, 24, 1449, 25], [426, 10, 1449, 25], [426, 17, 1453, 16], [426, 18, 1453, 17], [427, 8, 1453, 17], [428, 10, 1453, 17, "fileName"], [428, 18, 1453, 17], [428, 20, 1453, 17, "_jsxFileName"], [428, 32, 1453, 17], [429, 10, 1453, 17, "lineNumber"], [429, 20, 1453, 17], [430, 10, 1453, 17, "columnNumber"], [430, 22, 1453, 17], [431, 8, 1453, 17], [431, 15, 1457, 14], [431, 16, 1457, 15], [431, 31, 1461, 8], [431, 35, 1461, 8, "_jsxDevRuntime"], [431, 49, 1461, 8], [431, 50, 1461, 8, "jsxDEV"], [431, 56, 1461, 8], [431, 58, 1461, 9, "_Text"], [431, 63, 1461, 9], [431, 64, 1461, 9, "default"], [431, 71, 1461, 13], [432, 10, 1461, 14, "style"], [432, 15, 1461, 19], [432, 17, 1461, 21], [433, 12, 1461, 23, "fontSize"], [433, 20, 1461, 31], [433, 22, 1461, 33], [433, 24, 1461, 35], [434, 12, 1461, 37, "color"], [434, 17, 1461, 42], [434, 19, 1461, 44, "config"], [434, 25, 1461, 50], [434, 26, 1461, 51, "color"], [434, 31, 1461, 56], [435, 12, 1461, 58, "lineHeight"], [435, 22, 1461, 68], [435, 24, 1461, 70], [436, 10, 1461, 73], [436, 11, 1461, 75], [437, 10, 1461, 75, "children"], [437, 18, 1461, 75], [437, 20, 1465, 11, "config"], [437, 26, 1465, 17], [437, 27, 1465, 18, "message"], [438, 8, 1465, 25], [439, 10, 1465, 25, "fileName"], [439, 18, 1465, 25], [439, 20, 1465, 25, "_jsxFileName"], [439, 32, 1465, 25], [440, 10, 1465, 25, "lineNumber"], [440, 20, 1465, 25], [441, 10, 1465, 25, "columnNumber"], [441, 22, 1465, 25], [442, 8, 1465, 25], [442, 15, 1469, 14], [442, 16, 1469, 15], [442, 18, 1473, 9], [442, 19, 1473, 10, "locationStatus"], [442, 33, 1473, 24], [442, 38, 1473, 29], [442, 47, 1473, 38], [442, 51, 1473, 42, "locationStatus"], [442, 65, 1473, 56], [442, 70, 1473, 61], [442, 77, 1473, 68], [442, 95, 1477, 10], [442, 99, 1477, 10, "_jsxDevRuntime"], [442, 113, 1477, 10], [442, 114, 1477, 10, "jsxDEV"], [442, 120, 1477, 10], [442, 122, 1477, 11, "_View"], [442, 127, 1477, 11], [442, 128, 1477, 11, "default"], [442, 135, 1477, 15], [443, 10, 1477, 16, "style"], [443, 15, 1477, 21], [443, 17, 1477, 23], [444, 12, 1477, 25, "flexDirection"], [444, 25, 1477, 38], [444, 27, 1477, 40], [444, 32, 1477, 45], [445, 12, 1477, 47, "marginTop"], [445, 21, 1477, 56], [445, 23, 1477, 58], [446, 10, 1477, 61], [446, 11, 1477, 63], [447, 10, 1477, 63, "children"], [447, 18, 1477, 63], [447, 34, 1481, 12], [447, 38, 1481, 12, "_jsxDevRuntime"], [447, 52, 1481, 12], [447, 53, 1481, 12, "jsxDEV"], [447, 59, 1481, 12], [447, 61, 1481, 13, "_TouchableOpacity"], [447, 78, 1481, 13], [447, 79, 1481, 13, "default"], [447, 86, 1481, 29], [448, 12, 1485, 14, "onPress"], [448, 19, 1485, 21], [448, 21, 1485, 23, "verifyLocation"], [448, 35, 1485, 38], [449, 12, 1489, 14, "disabled"], [449, 20, 1489, 22], [449, 22, 1489, 24, "gettingLocation"], [449, 37, 1489, 40], [450, 12, 1493, 14, "style"], [450, 17, 1493, 19], [450, 19, 1493, 21], [451, 14, 1497, 16, "backgroundColor"], [451, 29, 1497, 31], [451, 31, 1497, 33, "config"], [451, 37, 1497, 39], [451, 38, 1497, 40, "color"], [451, 43, 1497, 45], [452, 14, 1501, 16, "borderRadius"], [452, 26, 1501, 28], [452, 28, 1501, 30], [452, 29, 1501, 31], [453, 14, 1505, 16, "paddingVertical"], [453, 29, 1505, 31], [453, 31, 1505, 33], [453, 32, 1505, 34], [454, 14, 1509, 16, "paddingHorizontal"], [454, 31, 1509, 33], [454, 33, 1509, 35], [454, 35, 1509, 37], [455, 14, 1513, 16, "opacity"], [455, 21, 1513, 23], [455, 23, 1513, 25, "gettingLocation"], [455, 38, 1513, 40], [455, 41, 1513, 43], [455, 44, 1513, 46], [455, 47, 1513, 49], [456, 12, 1517, 14], [456, 13, 1517, 16], [457, 12, 1517, 16, "children"], [457, 20, 1517, 16], [457, 35, 1525, 14], [457, 39, 1525, 14, "_jsxDevRuntime"], [457, 53, 1525, 14], [457, 54, 1525, 14, "jsxDEV"], [457, 60, 1525, 14], [457, 62, 1525, 15, "_Text"], [457, 67, 1525, 15], [457, 68, 1525, 15, "default"], [457, 75, 1525, 19], [458, 14, 1525, 20, "style"], [458, 19, 1525, 25], [458, 21, 1525, 27], [459, 16, 1525, 29, "fontSize"], [459, 24, 1525, 37], [459, 26, 1525, 39], [459, 28, 1525, 41], [460, 16, 1525, 43, "color"], [460, 21, 1525, 48], [460, 23, 1525, 50], [460, 29, 1525, 56], [461, 16, 1525, 58, "fontWeight"], [461, 26, 1525, 68], [461, 28, 1525, 70], [462, 14, 1525, 76], [462, 15, 1525, 78], [463, 14, 1525, 78, "children"], [463, 22, 1525, 78], [463, 24, 1529, 17, "gettingLocation"], [463, 39, 1529, 32], [463, 42, 1529, 35], [463, 52, 1529, 45], [463, 55, 1529, 48], [464, 12, 1529, 59], [465, 14, 1529, 59, "fileName"], [465, 22, 1529, 59], [465, 24, 1529, 59, "_jsxFileName"], [465, 36, 1529, 59], [466, 14, 1529, 59, "lineNumber"], [466, 24, 1529, 59], [467, 14, 1529, 59, "columnNumber"], [467, 26, 1529, 59], [468, 12, 1529, 59], [468, 19, 1533, 20], [469, 10, 1533, 21], [470, 12, 1533, 21, "fileName"], [470, 20, 1533, 21], [470, 22, 1533, 21, "_jsxFileName"], [470, 34, 1533, 21], [471, 12, 1533, 21, "lineNumber"], [471, 22, 1533, 21], [472, 12, 1533, 21, "columnNumber"], [472, 24, 1533, 21], [473, 10, 1533, 21], [473, 17, 1537, 30], [473, 18, 1537, 31], [473, 33, 1541, 12], [473, 37, 1541, 12, "_jsxDevRuntime"], [473, 51, 1541, 12], [473, 52, 1541, 12, "jsxDEV"], [473, 58, 1541, 12], [473, 60, 1541, 13, "_TouchableOpacity"], [473, 77, 1541, 13], [473, 78, 1541, 13, "default"], [473, 85, 1541, 29], [474, 12, 1545, 14, "onPress"], [474, 19, 1545, 21], [474, 21, 1545, 23, "onPress"], [474, 22, 1545, 23], [474, 27, 1545, 29], [475, 14, 1549, 16, "console"], [475, 21, 1549, 23], [475, 22, 1549, 24, "log"], [475, 25, 1549, 27], [475, 26, 1549, 28], [475, 48, 1549, 50], [475, 50, 1549, 52], [476, 16, 1553, 18, "before"], [476, 22, 1553, 24], [476, 24, 1553, 26, "testingMode"], [476, 35, 1553, 37], [477, 16, 1557, 18, "after"], [477, 21, 1557, 23], [477, 23, 1557, 25], [477, 24, 1557, 26, "testingMode"], [478, 14, 1561, 16], [478, 15, 1561, 17], [478, 16, 1561, 18], [479, 14, 1565, 16, "setTestingMode"], [479, 28, 1565, 30], [479, 29, 1565, 31], [479, 30, 1565, 32, "testingMode"], [479, 41, 1565, 43], [479, 42, 1565, 44], [480, 12, 1569, 14], [480, 13, 1569, 16], [481, 12, 1573, 14, "style"], [481, 17, 1573, 19], [481, 19, 1573, 21], [482, 14, 1577, 16, "backgroundColor"], [482, 29, 1577, 31], [482, 31, 1577, 33, "testingMode"], [482, 42, 1577, 44], [482, 45, 1577, 47], [482, 54, 1577, 56], [482, 57, 1577, 59], [482, 66, 1577, 68], [483, 14, 1581, 16, "borderRadius"], [483, 26, 1581, 28], [483, 28, 1581, 30], [483, 29, 1581, 31], [484, 14, 1585, 16, "paddingVertical"], [484, 29, 1585, 31], [484, 31, 1585, 33], [484, 32, 1585, 34], [485, 14, 1589, 16, "paddingHorizontal"], [485, 31, 1589, 33], [485, 33, 1589, 35], [485, 35, 1589, 37], [486, 14, 1593, 16, "marginLeft"], [486, 24, 1593, 26], [486, 26, 1593, 28], [487, 12, 1597, 14], [487, 13, 1597, 16], [488, 12, 1597, 16, "children"], [488, 20, 1597, 16], [488, 35, 1605, 14], [488, 39, 1605, 14, "_jsxDevRuntime"], [488, 53, 1605, 14], [488, 54, 1605, 14, "jsxDEV"], [488, 60, 1605, 14], [488, 62, 1605, 15, "_Text"], [488, 67, 1605, 15], [488, 68, 1605, 15, "default"], [488, 75, 1605, 19], [489, 14, 1605, 20, "style"], [489, 19, 1605, 25], [489, 21, 1605, 27], [490, 16, 1605, 29, "fontSize"], [490, 24, 1605, 37], [490, 26, 1605, 39], [490, 28, 1605, 41], [491, 16, 1605, 43, "color"], [491, 21, 1605, 48], [491, 23, 1605, 50], [491, 29, 1605, 56], [492, 16, 1605, 58, "fontWeight"], [492, 26, 1605, 68], [492, 28, 1605, 70], [493, 14, 1605, 76], [493, 15, 1605, 78], [494, 14, 1605, 78, "children"], [494, 22, 1605, 78], [494, 24, 1609, 17, "testingMode"], [494, 35, 1609, 28], [494, 38, 1609, 31], [494, 50, 1609, 43], [494, 53, 1609, 46], [495, 12, 1609, 62], [496, 14, 1609, 62, "fileName"], [496, 22, 1609, 62], [496, 24, 1609, 62, "_jsxFileName"], [496, 36, 1609, 62], [497, 14, 1609, 62, "lineNumber"], [497, 24, 1609, 62], [498, 14, 1609, 62, "columnNumber"], [498, 26, 1609, 62], [499, 12, 1609, 62], [499, 19, 1613, 20], [500, 10, 1613, 21], [501, 12, 1613, 21, "fileName"], [501, 20, 1613, 21], [501, 22, 1613, 21, "_jsxFileName"], [501, 34, 1613, 21], [502, 12, 1613, 21, "lineNumber"], [502, 22, 1613, 21], [503, 12, 1613, 21, "columnNumber"], [503, 24, 1613, 21], [504, 10, 1613, 21], [504, 17, 1617, 30], [504, 18, 1617, 31], [505, 8, 1617, 31], [506, 10, 1617, 31, "fileName"], [506, 18, 1617, 31], [506, 20, 1617, 31, "_jsxFileName"], [506, 32, 1617, 31], [507, 10, 1617, 31, "lineNumber"], [507, 20, 1617, 31], [508, 10, 1617, 31, "columnNumber"], [508, 22, 1617, 31], [509, 8, 1617, 31], [509, 15, 1621, 16], [509, 16, 1625, 9], [509, 18, 1629, 9, "testingMode"], [509, 29, 1629, 20], [509, 46, 1633, 10], [509, 50, 1633, 10, "_jsxDevRuntime"], [509, 64, 1633, 10], [509, 65, 1633, 10, "jsxDEV"], [509, 71, 1633, 10], [509, 73, 1633, 11, "_View"], [509, 78, 1633, 11], [509, 79, 1633, 11, "default"], [509, 86, 1633, 15], [510, 10, 1637, 12, "style"], [510, 15, 1637, 17], [510, 17, 1637, 19], [511, 12, 1641, 14, "backgroundColor"], [511, 27, 1641, 29], [511, 29, 1641, 31], [511, 38, 1641, 40], [512, 12, 1645, 14, "borderRadius"], [512, 24, 1645, 26], [512, 26, 1645, 28], [512, 27, 1645, 29], [513, 12, 1649, 14, "padding"], [513, 19, 1649, 21], [513, 21, 1649, 23], [513, 23, 1649, 25], [514, 12, 1653, 14, "marginTop"], [514, 21, 1653, 23], [514, 23, 1653, 25], [514, 25, 1653, 27], [515, 12, 1657, 14, "borderWidth"], [515, 23, 1657, 25], [515, 25, 1657, 27], [515, 26, 1657, 28], [516, 12, 1661, 14, "borderColor"], [516, 23, 1661, 25], [516, 25, 1661, 27], [517, 10, 1665, 12], [517, 11, 1665, 14], [518, 10, 1665, 14, "children"], [518, 18, 1665, 14], [518, 34, 1673, 12], [518, 38, 1673, 12, "_jsxDevRuntime"], [518, 52, 1673, 12], [518, 53, 1673, 12, "jsxDEV"], [518, 59, 1673, 12], [518, 61, 1673, 13, "_Text"], [518, 66, 1673, 13], [518, 67, 1673, 13, "default"], [518, 74, 1673, 17], [519, 12, 1677, 14, "style"], [519, 17, 1677, 19], [519, 19, 1677, 21], [520, 14, 1681, 16, "fontSize"], [520, 22, 1681, 24], [520, 24, 1681, 26], [520, 26, 1681, 28], [521, 14, 1685, 16, "fontWeight"], [521, 24, 1685, 26], [521, 26, 1685, 28], [521, 31, 1685, 33], [522, 14, 1689, 16, "color"], [522, 19, 1689, 21], [522, 21, 1689, 23], [522, 30, 1689, 32], [523, 14, 1693, 16, "marginBottom"], [523, 26, 1693, 28], [523, 28, 1693, 30], [524, 12, 1697, 14], [524, 13, 1697, 16], [525, 12, 1697, 16, "children"], [525, 20, 1697, 16], [525, 22, 1701, 13], [526, 10, 1709, 12], [527, 12, 1709, 12, "fileName"], [527, 20, 1709, 12], [527, 22, 1709, 12, "_jsxFileName"], [527, 34, 1709, 12], [528, 12, 1709, 12, "lineNumber"], [528, 22, 1709, 12], [529, 12, 1709, 12, "columnNumber"], [529, 24, 1709, 12], [530, 10, 1709, 12], [530, 17, 1709, 18], [530, 18, 1709, 19], [530, 33, 1713, 12], [530, 37, 1713, 12, "_jsxDevRuntime"], [530, 51, 1713, 12], [530, 52, 1713, 12, "jsxDEV"], [530, 58, 1713, 12], [530, 60, 1713, 13, "_Text"], [530, 65, 1713, 13], [530, 66, 1713, 13, "default"], [530, 73, 1713, 17], [531, 12, 1713, 18, "style"], [531, 17, 1713, 23], [531, 19, 1713, 25], [532, 14, 1713, 27, "fontSize"], [532, 22, 1713, 35], [532, 24, 1713, 37], [532, 26, 1713, 39], [533, 14, 1713, 41, "color"], [533, 19, 1713, 46], [533, 21, 1713, 48], [533, 30, 1713, 57], [534, 14, 1713, 59, "lineHeight"], [534, 24, 1713, 69], [534, 26, 1713, 71], [535, 12, 1713, 74], [535, 13, 1713, 76], [536, 12, 1713, 76, "children"], [536, 20, 1713, 76], [536, 22, 1713, 77], [537, 10, 1725, 12], [538, 12, 1725, 12, "fileName"], [538, 20, 1725, 12], [538, 22, 1725, 12, "_jsxFileName"], [538, 34, 1725, 12], [539, 12, 1725, 12, "lineNumber"], [539, 22, 1725, 12], [540, 12, 1725, 12, "columnNumber"], [540, 24, 1725, 12], [541, 10, 1725, 12], [541, 17, 1725, 18], [541, 18, 1725, 19], [542, 8, 1725, 19], [543, 10, 1725, 19, "fileName"], [543, 18, 1725, 19], [543, 20, 1725, 19, "_jsxFileName"], [543, 32, 1725, 19], [544, 10, 1725, 19, "lineNumber"], [544, 20, 1725, 19], [545, 10, 1725, 19, "columnNumber"], [545, 22, 1725, 19], [546, 8, 1725, 19], [546, 15, 1729, 16], [546, 16, 1733, 9], [547, 6, 1733, 9], [548, 8, 1733, 9, "fileName"], [548, 16, 1733, 9], [548, 18, 1733, 9, "_jsxFileName"], [548, 30, 1733, 9], [549, 8, 1733, 9, "lineNumber"], [549, 18, 1733, 9], [550, 8, 1733, 9, "columnNumber"], [550, 20, 1733, 9], [551, 6, 1733, 9], [551, 13, 1737, 12], [551, 14, 1737, 13], [552, 4, 1745, 2], [552, 5, 1745, 3], [553, 4, 1749, 2], [553, 24, 1753, 4], [553, 28, 1753, 4, "_jsxDevRuntime"], [553, 42, 1753, 4], [553, 43, 1753, 4, "jsxDEV"], [553, 49, 1753, 4], [553, 51, 1753, 5, "_View"], [553, 56, 1753, 5], [553, 57, 1753, 5, "default"], [553, 64, 1753, 9], [554, 6, 1753, 10, "style"], [554, 11, 1753, 15], [554, 13, 1753, 17], [555, 8, 1753, 19, "flex"], [555, 12, 1753, 23], [555, 14, 1753, 25], [555, 15, 1753, 26], [556, 8, 1753, 28, "backgroundColor"], [556, 23, 1753, 43], [556, 25, 1753, 45], [557, 6, 1753, 55], [557, 7, 1753, 57], [558, 6, 1753, 57, "children"], [558, 14, 1753, 57], [558, 30, 1757, 6], [558, 34, 1757, 6, "_jsxDevRuntime"], [558, 48, 1757, 6], [558, 49, 1757, 6, "jsxDEV"], [558, 55, 1757, 6], [558, 57, 1757, 7, "_expoStatusBar"], [558, 71, 1757, 7], [558, 72, 1757, 7, "StatusBar"], [558, 81, 1757, 16], [559, 8, 1757, 17, "style"], [559, 13, 1757, 22], [559, 15, 1757, 23], [560, 6, 1757, 29], [561, 8, 1757, 29, "fileName"], [561, 16, 1757, 29], [561, 18, 1757, 29, "_jsxFileName"], [561, 30, 1757, 29], [562, 8, 1757, 29, "lineNumber"], [562, 18, 1757, 29], [563, 8, 1757, 29, "columnNumber"], [563, 20, 1757, 29], [564, 6, 1757, 29], [564, 13, 1757, 31], [564, 14, 1757, 32], [564, 29, 1765, 6], [564, 33, 1765, 6, "_jsxDevRuntime"], [564, 47, 1765, 6], [564, 48, 1765, 6, "jsxDEV"], [564, 54, 1765, 6], [564, 56, 1765, 7, "_View"], [564, 61, 1765, 7], [564, 62, 1765, 7, "default"], [564, 69, 1765, 11], [565, 8, 1769, 8, "style"], [565, 13, 1769, 13], [565, 15, 1769, 15], [566, 10, 1773, 10, "backgroundColor"], [566, 25, 1773, 25], [566, 27, 1773, 27], [566, 33, 1773, 33], [567, 10, 1777, 10, "paddingTop"], [567, 20, 1777, 20], [567, 22, 1777, 22, "insets"], [567, 28, 1777, 28], [567, 29, 1777, 29, "top"], [567, 32, 1777, 32], [567, 35, 1777, 35], [567, 36, 1777, 36], [568, 10, 1781, 10, "paddingHorizontal"], [568, 27, 1781, 27], [568, 29, 1781, 29], [568, 31, 1781, 31], [569, 10, 1785, 10, "paddingBottom"], [569, 23, 1785, 23], [569, 25, 1785, 25], [569, 27, 1785, 27], [570, 10, 1789, 10, "borderBottomWidth"], [570, 27, 1789, 27], [570, 29, 1789, 29], [570, 30, 1789, 30], [571, 10, 1793, 10, "borderBottomColor"], [571, 27, 1793, 27], [571, 29, 1793, 29], [571, 38, 1793, 38], [572, 10, 1797, 10, "zIndex"], [572, 16, 1797, 16], [572, 18, 1797, 18], [573, 8, 1801, 8], [573, 9, 1801, 10], [574, 8, 1801, 10, "children"], [574, 16, 1801, 10], [574, 32, 1809, 8], [574, 36, 1809, 8, "_jsxDevRuntime"], [574, 50, 1809, 8], [574, 51, 1809, 8, "jsxDEV"], [574, 57, 1809, 8], [574, 59, 1809, 9, "_View"], [574, 64, 1809, 9], [574, 65, 1809, 9, "default"], [574, 72, 1809, 13], [575, 10, 1813, 10, "style"], [575, 15, 1813, 15], [575, 17, 1813, 17], [576, 12, 1817, 12, "flexDirection"], [576, 25, 1817, 25], [576, 27, 1817, 27], [576, 32, 1817, 32], [577, 12, 1821, 12, "alignItems"], [577, 22, 1821, 22], [577, 24, 1821, 24], [577, 32, 1821, 32], [578, 12, 1825, 12, "marginBottom"], [578, 24, 1825, 24], [578, 26, 1825, 26], [579, 10, 1829, 10], [579, 11, 1829, 12], [580, 10, 1829, 12, "children"], [580, 18, 1829, 12], [580, 34, 1837, 10], [580, 38, 1837, 10, "_jsxDevRuntime"], [580, 52, 1837, 10], [580, 53, 1837, 10, "jsxDEV"], [580, 59, 1837, 10], [580, 61, 1837, 11, "_TouchableOpacity"], [580, 78, 1837, 11], [580, 79, 1837, 11, "default"], [580, 86, 1837, 27], [581, 12, 1841, 12, "onPress"], [581, 19, 1841, 19], [581, 21, 1841, 21, "onPress"], [581, 22, 1841, 21], [581, 27, 1841, 27, "router"], [581, 45, 1841, 33], [581, 46, 1841, 34, "back"], [581, 50, 1841, 38], [581, 51, 1841, 39], [581, 52, 1841, 41], [582, 12, 1845, 12, "style"], [582, 17, 1845, 17], [582, 19, 1845, 19], [583, 14, 1845, 21, "marginRight"], [583, 25, 1845, 32], [583, 27, 1845, 34], [584, 12, 1845, 37], [584, 13, 1845, 39], [585, 12, 1845, 39, "children"], [585, 20, 1845, 39], [585, 35, 1853, 12], [585, 39, 1853, 12, "_jsxDevRuntime"], [585, 53, 1853, 12], [585, 54, 1853, 12, "jsxDEV"], [585, 60, 1853, 12], [585, 62, 1853, 13, "_lucideReactNative"], [585, 80, 1853, 13], [585, 81, 1853, 13, "ArrowLeft"], [585, 90, 1853, 22], [586, 14, 1853, 23, "size"], [586, 18, 1853, 27], [586, 20, 1853, 29], [586, 22, 1853, 32], [587, 14, 1853, 33, "color"], [587, 19, 1853, 38], [587, 21, 1853, 39], [588, 12, 1853, 48], [589, 14, 1853, 48, "fileName"], [589, 22, 1853, 48], [589, 24, 1853, 48, "_jsxFileName"], [589, 36, 1853, 48], [590, 14, 1853, 48, "lineNumber"], [590, 24, 1853, 48], [591, 14, 1853, 48, "columnNumber"], [591, 26, 1853, 48], [592, 12, 1853, 48], [592, 19, 1853, 50], [593, 10, 1853, 51], [594, 12, 1853, 51, "fileName"], [594, 20, 1853, 51], [594, 22, 1853, 51, "_jsxFileName"], [594, 34, 1853, 51], [595, 12, 1853, 51, "lineNumber"], [595, 22, 1853, 51], [596, 12, 1853, 51, "columnNumber"], [596, 24, 1853, 51], [597, 10, 1853, 51], [597, 17, 1857, 28], [597, 18, 1857, 29], [597, 33, 1861, 10], [597, 37, 1861, 10, "_jsxDevRuntime"], [597, 51, 1861, 10], [597, 52, 1861, 10, "jsxDEV"], [597, 58, 1861, 10], [597, 60, 1861, 11, "_Text"], [597, 65, 1861, 11], [597, 66, 1861, 11, "default"], [597, 73, 1861, 15], [598, 12, 1865, 12, "style"], [598, 17, 1865, 17], [598, 19, 1865, 19], [599, 14, 1869, 14, "fontSize"], [599, 22, 1869, 22], [599, 24, 1869, 24], [599, 26, 1869, 26], [600, 14, 1873, 14, "fontWeight"], [600, 24, 1873, 24], [600, 26, 1873, 26], [600, 32, 1873, 32], [601, 14, 1877, 14, "color"], [601, 19, 1877, 19], [601, 21, 1877, 21], [601, 30, 1877, 30], [602, 14, 1881, 14, "flex"], [602, 18, 1881, 18], [602, 20, 1881, 20], [603, 12, 1885, 12], [603, 13, 1885, 14], [604, 12, 1885, 14, "children"], [604, 20, 1885, 14], [604, 22, 1889, 11], [605, 10, 1897, 10], [606, 12, 1897, 10, "fileName"], [606, 20, 1897, 10], [606, 22, 1897, 10, "_jsxFileName"], [606, 34, 1897, 10], [607, 12, 1897, 10, "lineNumber"], [607, 22, 1897, 10], [608, 12, 1897, 10, "columnNumber"], [608, 24, 1897, 10], [609, 10, 1897, 10], [609, 17, 1897, 16], [609, 18, 1897, 17], [609, 33, 1905, 10], [609, 37, 1905, 10, "_jsxDevRuntime"], [609, 51, 1905, 10], [609, 52, 1905, 10, "jsxDEV"], [609, 58, 1905, 10], [609, 60, 1905, 11, "_Text"], [609, 65, 1905, 11], [609, 66, 1905, 11, "default"], [609, 73, 1905, 15], [610, 12, 1905, 16, "style"], [610, 17, 1905, 21], [610, 19, 1905, 23], [611, 14, 1905, 25, "fontSize"], [611, 22, 1905, 33], [611, 24, 1905, 35], [611, 26, 1905, 37], [612, 14, 1905, 39, "color"], [612, 19, 1905, 44], [612, 21, 1905, 46], [613, 12, 1905, 56], [613, 13, 1905, 58], [614, 12, 1905, 58, "children"], [614, 20, 1905, 58], [614, 22, 1909, 13], [614, 40, 1909, 31, "testingMode"], [614, 51, 1909, 42], [614, 54, 1909, 45], [614, 58, 1909, 49], [614, 61, 1909, 52], [614, 66, 1909, 57], [615, 10, 1909, 59], [616, 12, 1909, 59, "fileName"], [616, 20, 1909, 59], [616, 22, 1909, 59, "_jsxFileName"], [616, 34, 1909, 59], [617, 12, 1909, 59, "lineNumber"], [617, 22, 1909, 59], [618, 12, 1909, 59, "columnNumber"], [618, 24, 1909, 59], [619, 10, 1909, 59], [619, 17, 1913, 16], [619, 18, 1913, 17], [620, 8, 1913, 17], [621, 10, 1913, 17, "fileName"], [621, 18, 1913, 17], [621, 20, 1913, 17, "_jsxFileName"], [621, 32, 1913, 17], [622, 10, 1913, 17, "lineNumber"], [622, 20, 1913, 17], [623, 10, 1913, 17, "columnNumber"], [623, 22, 1913, 17], [624, 8, 1913, 17], [624, 15, 1917, 14], [624, 16, 1917, 15], [624, 31, 1925, 8], [624, 35, 1925, 8, "_jsxDevRuntime"], [624, 49, 1925, 8], [624, 50, 1925, 8, "jsxDEV"], [624, 56, 1925, 8], [624, 58, 1925, 9, "_View"], [624, 63, 1925, 9], [624, 64, 1925, 9, "default"], [624, 71, 1925, 13], [625, 10, 1929, 10, "style"], [625, 15, 1929, 15], [625, 17, 1929, 17], [626, 12, 1933, 12, "backgroundColor"], [626, 27, 1933, 27], [626, 29, 1933, 29], [626, 38, 1933, 38], [627, 12, 1937, 12, "borderRadius"], [627, 24, 1937, 24], [627, 26, 1937, 26], [627, 28, 1937, 28], [628, 12, 1941, 12, "padding"], [628, 19, 1941, 19], [628, 21, 1941, 21], [628, 23, 1941, 23], [629, 12, 1945, 12, "borderLeftWidth"], [629, 27, 1945, 27], [629, 29, 1945, 29], [629, 30, 1945, 30], [630, 12, 1949, 12, "borderLeftColor"], [630, 27, 1949, 27], [630, 29, 1949, 29], [631, 10, 1953, 10], [631, 11, 1953, 12], [632, 10, 1953, 12, "children"], [632, 18, 1953, 12], [632, 34, 1961, 10], [632, 38, 1961, 10, "_jsxDevRuntime"], [632, 52, 1961, 10], [632, 53, 1961, 10, "jsxDEV"], [632, 59, 1961, 10], [632, 61, 1961, 11, "_Text"], [632, 66, 1961, 11], [632, 67, 1961, 11, "default"], [632, 74, 1961, 15], [633, 12, 1965, 12, "style"], [633, 17, 1965, 17], [633, 19, 1965, 19], [634, 14, 1969, 14, "fontSize"], [634, 22, 1969, 22], [634, 24, 1969, 24], [634, 26, 1969, 26], [635, 14, 1973, 14, "color"], [635, 19, 1973, 19], [635, 21, 1973, 21], [635, 30, 1973, 30], [636, 14, 1977, 14, "fontWeight"], [636, 24, 1977, 24], [636, 26, 1977, 26], [636, 31, 1977, 31], [637, 14, 1981, 14, "marginBottom"], [637, 26, 1981, 26], [637, 28, 1981, 28], [638, 12, 1985, 12], [638, 13, 1985, 14], [639, 12, 1985, 14, "children"], [639, 20, 1985, 14], [639, 22, 1993, 13, "question"], [639, 30, 1993, 21], [639, 31, 1993, 22, "question"], [640, 10, 1993, 30], [641, 12, 1993, 30, "fileName"], [641, 20, 1993, 30], [641, 22, 1993, 30, "_jsxFileName"], [641, 34, 1993, 30], [642, 12, 1993, 30, "lineNumber"], [642, 22, 1993, 30], [643, 12, 1993, 30, "columnNumber"], [643, 24, 1993, 30], [644, 10, 1993, 30], [644, 17, 1997, 16], [644, 18, 1997, 17], [644, 33, 2001, 10], [644, 37, 2001, 10, "_jsxDevRuntime"], [644, 51, 2001, 10], [644, 52, 2001, 10, "jsxDEV"], [644, 58, 2001, 10], [644, 60, 2001, 11, "_View"], [644, 65, 2001, 11], [644, 66, 2001, 11, "default"], [644, 73, 2001, 15], [645, 12, 2005, 12, "style"], [645, 17, 2005, 17], [645, 19, 2005, 19], [646, 14, 2009, 14, "flexDirection"], [646, 27, 2009, 27], [646, 29, 2009, 29], [646, 34, 2009, 34], [647, 14, 2013, 14, "alignItems"], [647, 24, 2013, 24], [647, 26, 2013, 26], [647, 34, 2013, 34], [648, 14, 2017, 14, "marginBottom"], [648, 26, 2017, 26], [648, 28, 2017, 28], [649, 12, 2021, 12], [649, 13, 2021, 14], [650, 12, 2021, 14, "children"], [650, 20, 2021, 14], [650, 36, 2029, 12], [650, 40, 2029, 12, "_jsxDevRuntime"], [650, 54, 2029, 12], [650, 55, 2029, 12, "jsxDEV"], [650, 61, 2029, 12], [650, 63, 2029, 13, "_lucideReactNative"], [650, 81, 2029, 13], [650, 82, 2029, 13, "MapPin"], [650, 88, 2029, 19], [651, 14, 2029, 20, "size"], [651, 18, 2029, 24], [651, 20, 2029, 26], [651, 22, 2029, 29], [652, 14, 2029, 30, "color"], [652, 19, 2029, 35], [652, 21, 2029, 36], [653, 12, 2029, 45], [654, 14, 2029, 45, "fileName"], [654, 22, 2029, 45], [654, 24, 2029, 45, "_jsxFileName"], [654, 36, 2029, 45], [655, 14, 2029, 45, "lineNumber"], [655, 24, 2029, 45], [656, 14, 2029, 45, "columnNumber"], [656, 26, 2029, 45], [657, 12, 2029, 45], [657, 19, 2029, 47], [657, 20, 2029, 48], [657, 35, 2033, 12], [657, 39, 2033, 12, "_jsxDevRuntime"], [657, 53, 2033, 12], [657, 54, 2033, 12, "jsxDEV"], [657, 60, 2033, 12], [657, 62, 2033, 13, "_Text"], [657, 67, 2033, 13], [657, 68, 2033, 13, "default"], [657, 75, 2033, 17], [658, 14, 2037, 14, "style"], [658, 19, 2037, 19], [658, 21, 2037, 21], [659, 16, 2037, 23, "fontSize"], [659, 24, 2037, 31], [659, 26, 2037, 33], [659, 28, 2037, 35], [660, 16, 2037, 37, "color"], [660, 21, 2037, 42], [660, 23, 2037, 44], [660, 32, 2037, 53], [661, 16, 2037, 55, "marginLeft"], [661, 26, 2037, 65], [661, 28, 2037, 67], [661, 29, 2037, 68], [662, 16, 2037, 70, "flex"], [662, 20, 2037, 74], [662, 22, 2037, 76], [663, 14, 2037, 78], [663, 15, 2037, 80], [664, 14, 2037, 80, "children"], [664, 22, 2037, 80], [664, 24, 2045, 15, "question"], [664, 32, 2045, 23], [664, 33, 2045, 24, "location"], [665, 12, 2045, 32], [666, 14, 2045, 32, "fileName"], [666, 22, 2045, 32], [666, 24, 2045, 32, "_jsxFileName"], [666, 36, 2045, 32], [667, 14, 2045, 32, "lineNumber"], [667, 24, 2045, 32], [668, 14, 2045, 32, "columnNumber"], [668, 26, 2045, 32], [669, 12, 2045, 32], [669, 19, 2049, 18], [669, 20, 2049, 19], [670, 10, 2049, 19], [671, 12, 2049, 19, "fileName"], [671, 20, 2049, 19], [671, 22, 2049, 19, "_jsxFileName"], [671, 34, 2049, 19], [672, 12, 2049, 19, "lineNumber"], [672, 22, 2049, 19], [673, 12, 2049, 19, "columnNumber"], [673, 24, 2049, 19], [674, 10, 2049, 19], [674, 17, 2053, 16], [674, 18, 2053, 17], [674, 33, 2057, 10], [674, 37, 2057, 10, "_jsxDevRuntime"], [674, 51, 2057, 10], [674, 52, 2057, 10, "jsxDEV"], [674, 58, 2057, 10], [674, 60, 2057, 11, "_View"], [674, 65, 2057, 11], [674, 66, 2057, 11, "default"], [674, 73, 2057, 15], [675, 12, 2061, 12, "style"], [675, 17, 2061, 17], [675, 19, 2061, 19], [676, 14, 2065, 14, "flexDirection"], [676, 27, 2065, 27], [676, 29, 2065, 29], [676, 34, 2065, 34], [677, 14, 2069, 14, "justifyContent"], [677, 28, 2069, 28], [677, 30, 2069, 30], [677, 45, 2069, 45], [678, 14, 2073, 14, "alignItems"], [678, 24, 2073, 24], [678, 26, 2073, 26], [679, 12, 2077, 12], [679, 13, 2077, 14], [680, 12, 2077, 14, "children"], [680, 20, 2077, 14], [680, 36, 2085, 12], [680, 40, 2085, 12, "_jsxDevRuntime"], [680, 54, 2085, 12], [680, 55, 2085, 12, "jsxDEV"], [680, 61, 2085, 12], [680, 63, 2085, 13, "_View"], [680, 68, 2085, 13], [680, 69, 2085, 13, "default"], [680, 76, 2085, 17], [681, 14, 2085, 18, "style"], [681, 19, 2085, 23], [681, 21, 2085, 25], [682, 16, 2085, 27, "flexDirection"], [682, 29, 2085, 40], [682, 31, 2085, 42], [682, 36, 2085, 47], [683, 16, 2085, 49, "alignItems"], [683, 26, 2085, 59], [683, 28, 2085, 61], [684, 14, 2085, 70], [684, 15, 2085, 72], [685, 14, 2085, 72, "children"], [685, 22, 2085, 72], [685, 38, 2089, 14], [685, 42, 2089, 14, "_jsxDevRuntime"], [685, 56, 2089, 14], [685, 57, 2089, 14, "jsxDEV"], [685, 63, 2089, 14], [685, 65, 2089, 15, "_lucideReactNative"], [685, 83, 2089, 15], [685, 84, 2089, 15, "DollarSign"], [685, 94, 2089, 25], [686, 16, 2089, 26, "size"], [686, 20, 2089, 30], [686, 22, 2089, 32], [686, 24, 2089, 35], [687, 16, 2089, 36, "color"], [687, 21, 2089, 41], [687, 23, 2089, 42], [688, 14, 2089, 51], [689, 16, 2089, 51, "fileName"], [689, 24, 2089, 51], [689, 26, 2089, 51, "_jsxFileName"], [689, 38, 2089, 51], [690, 16, 2089, 51, "lineNumber"], [690, 26, 2089, 51], [691, 16, 2089, 51, "columnNumber"], [691, 28, 2089, 51], [692, 14, 2089, 51], [692, 21, 2089, 53], [692, 22, 2089, 54], [692, 37, 2093, 14], [692, 41, 2093, 14, "_jsxDevRuntime"], [692, 55, 2093, 14], [692, 56, 2093, 14, "jsxDEV"], [692, 62, 2093, 14], [692, 64, 2093, 15, "_Text"], [692, 69, 2093, 15], [692, 70, 2093, 15, "default"], [692, 77, 2093, 19], [693, 16, 2097, 16, "style"], [693, 21, 2097, 21], [693, 23, 2097, 23], [694, 18, 2101, 18, "fontSize"], [694, 26, 2101, 26], [694, 28, 2101, 28], [694, 30, 2101, 30], [695, 18, 2105, 18, "color"], [695, 23, 2105, 23], [695, 25, 2105, 25], [695, 34, 2105, 34], [696, 18, 2109, 18, "fontWeight"], [696, 28, 2109, 28], [696, 30, 2109, 30], [696, 35, 2109, 35], [697, 18, 2113, 18, "marginLeft"], [697, 28, 2113, 28], [697, 30, 2113, 30], [698, 16, 2117, 16], [698, 17, 2117, 18], [699, 16, 2117, 18, "children"], [699, 24, 2117, 18], [699, 26, 2125, 17], [699, 30, 2125, 21, "question"], [699, 38, 2125, 29], [699, 39, 2125, 30, "reward"], [699, 45, 2125, 36], [699, 46, 2125, 37, "toFixed"], [699, 53, 2125, 44], [699, 54, 2125, 45], [699, 55, 2125, 46], [699, 56, 2125, 47], [700, 14, 2125, 56], [701, 16, 2125, 56, "fileName"], [701, 24, 2125, 56], [701, 26, 2125, 56, "_jsxFileName"], [701, 38, 2125, 56], [702, 16, 2125, 56, "lineNumber"], [702, 26, 2125, 56], [703, 16, 2125, 56, "columnNumber"], [703, 28, 2125, 56], [704, 14, 2125, 56], [704, 21, 2129, 20], [704, 22, 2129, 21], [705, 12, 2129, 21], [706, 14, 2129, 21, "fileName"], [706, 22, 2129, 21], [706, 24, 2129, 21, "_jsxFileName"], [706, 36, 2129, 21], [707, 14, 2129, 21, "lineNumber"], [707, 24, 2129, 21], [708, 14, 2129, 21, "columnNumber"], [708, 26, 2129, 21], [709, 12, 2129, 21], [709, 19, 2133, 18], [709, 20, 2133, 19], [709, 35, 2137, 12], [709, 39, 2137, 12, "_jsxDevRuntime"], [709, 53, 2137, 12], [709, 54, 2137, 12, "jsxDEV"], [709, 60, 2137, 12], [709, 62, 2137, 13, "_View"], [709, 67, 2137, 13], [709, 68, 2137, 13, "default"], [709, 75, 2137, 17], [710, 14, 2137, 18, "style"], [710, 19, 2137, 23], [710, 21, 2137, 25], [711, 16, 2137, 27, "flexDirection"], [711, 29, 2137, 40], [711, 31, 2137, 42], [711, 36, 2137, 47], [712, 16, 2137, 49, "alignItems"], [712, 26, 2137, 59], [712, 28, 2137, 61], [713, 14, 2137, 70], [713, 15, 2137, 72], [714, 14, 2137, 72, "children"], [714, 22, 2137, 72], [714, 38, 2141, 14], [714, 42, 2141, 14, "_jsxDevRuntime"], [714, 56, 2141, 14], [714, 57, 2141, 14, "jsxDEV"], [714, 63, 2141, 14], [714, 65, 2141, 15, "_lucideReactNative"], [714, 83, 2141, 15], [714, 84, 2141, 15, "Clock"], [714, 89, 2141, 20], [715, 16, 2141, 21, "size"], [715, 20, 2141, 25], [715, 22, 2141, 27], [715, 24, 2141, 30], [716, 16, 2141, 31, "color"], [716, 21, 2141, 36], [716, 23, 2141, 37], [717, 14, 2141, 46], [718, 16, 2141, 46, "fileName"], [718, 24, 2141, 46], [718, 26, 2141, 46, "_jsxFileName"], [718, 38, 2141, 46], [719, 16, 2141, 46, "lineNumber"], [719, 26, 2141, 46], [720, 16, 2141, 46, "columnNumber"], [720, 28, 2141, 46], [721, 14, 2141, 46], [721, 21, 2141, 48], [721, 22, 2141, 49], [721, 37, 2145, 14], [721, 41, 2145, 14, "_jsxDevRuntime"], [721, 55, 2145, 14], [721, 56, 2145, 14, "jsxDEV"], [721, 62, 2145, 14], [721, 64, 2145, 15, "_Text"], [721, 69, 2145, 15], [721, 70, 2145, 15, "default"], [721, 77, 2145, 19], [722, 16, 2145, 20, "style"], [722, 21, 2145, 25], [722, 23, 2145, 27], [723, 18, 2145, 29, "fontSize"], [723, 26, 2145, 37], [723, 28, 2145, 39], [723, 30, 2145, 41], [724, 18, 2145, 43, "color"], [724, 23, 2145, 48], [724, 25, 2145, 50], [724, 34, 2145, 59], [725, 18, 2145, 61, "marginLeft"], [725, 28, 2145, 71], [725, 30, 2145, 73], [726, 16, 2145, 75], [726, 17, 2145, 77], [727, 16, 2145, 77, "children"], [727, 24, 2145, 77], [727, 26, 2149, 17, "question"], [727, 34, 2149, 25], [727, 35, 2149, 26, "postedAt"], [728, 14, 2149, 34], [729, 16, 2149, 34, "fileName"], [729, 24, 2149, 34], [729, 26, 2149, 34, "_jsxFileName"], [729, 38, 2149, 34], [730, 16, 2149, 34, "lineNumber"], [730, 26, 2149, 34], [731, 16, 2149, 34, "columnNumber"], [731, 28, 2149, 34], [732, 14, 2149, 34], [732, 21, 2153, 20], [732, 22, 2153, 21], [733, 12, 2153, 21], [734, 14, 2153, 21, "fileName"], [734, 22, 2153, 21], [734, 24, 2153, 21, "_jsxFileName"], [734, 36, 2153, 21], [735, 14, 2153, 21, "lineNumber"], [735, 24, 2153, 21], [736, 14, 2153, 21, "columnNumber"], [736, 26, 2153, 21], [737, 12, 2153, 21], [737, 19, 2157, 18], [737, 20, 2157, 19], [738, 10, 2157, 19], [739, 12, 2157, 19, "fileName"], [739, 20, 2157, 19], [739, 22, 2157, 19, "_jsxFileName"], [739, 34, 2157, 19], [740, 12, 2157, 19, "lineNumber"], [740, 22, 2157, 19], [741, 12, 2157, 19, "columnNumber"], [741, 24, 2157, 19], [742, 10, 2157, 19], [742, 17, 2161, 16], [742, 18, 2161, 17], [743, 8, 2161, 17], [744, 10, 2161, 17, "fileName"], [744, 18, 2161, 17], [744, 20, 2161, 17, "_jsxFileName"], [744, 32, 2161, 17], [745, 10, 2161, 17, "lineNumber"], [745, 20, 2161, 17], [746, 10, 2161, 17, "columnNumber"], [746, 22, 2161, 17], [747, 8, 2161, 17], [747, 15, 2165, 14], [747, 16, 2165, 15], [748, 6, 2165, 15], [749, 8, 2165, 15, "fileName"], [749, 16, 2165, 15], [749, 18, 2165, 15, "_jsxFileName"], [749, 30, 2165, 15], [750, 8, 2165, 15, "lineNumber"], [750, 18, 2165, 15], [751, 8, 2165, 15, "columnNumber"], [751, 20, 2165, 15], [752, 6, 2165, 15], [752, 13, 2169, 12], [752, 14, 2169, 13], [752, 29, 2173, 6], [752, 33, 2173, 6, "_jsxDevRuntime"], [752, 47, 2173, 6], [752, 48, 2173, 6, "jsxDEV"], [752, 54, 2173, 6], [752, 56, 2173, 7, "_KeyboardAvoidingAnimatedView"], [752, 85, 2173, 7], [752, 86, 2173, 7, "default"], [752, 93, 2173, 35], [753, 8, 2173, 36, "style"], [753, 13, 2173, 41], [753, 15, 2173, 43], [754, 10, 2173, 45, "flex"], [754, 14, 2173, 49], [754, 16, 2173, 51], [755, 8, 2173, 53], [755, 9, 2173, 55], [756, 8, 2173, 56, "behavior"], [756, 16, 2173, 64], [756, 18, 2173, 65], [756, 27, 2173, 74], [757, 8, 2173, 74, "children"], [757, 16, 2173, 74], [757, 32, 2177, 8], [757, 36, 2177, 8, "_jsxDevRuntime"], [757, 50, 2177, 8], [757, 51, 2177, 8, "jsxDEV"], [757, 57, 2177, 8], [757, 59, 2177, 9, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [757, 70, 2177, 9], [757, 71, 2177, 9, "default"], [757, 78, 2177, 19], [758, 10, 2181, 10, "style"], [758, 15, 2181, 15], [758, 17, 2181, 17], [759, 12, 2181, 19, "flex"], [759, 16, 2181, 23], [759, 18, 2181, 25], [760, 10, 2181, 27], [760, 11, 2181, 29], [761, 10, 2185, 10, "contentContainerStyle"], [761, 31, 2185, 31], [761, 33, 2185, 33], [762, 12, 2185, 35, "paddingBottom"], [762, 25, 2185, 48], [762, 27, 2185, 50, "insets"], [762, 33, 2185, 56], [762, 34, 2185, 57, "bottom"], [762, 40, 2185, 63], [762, 43, 2185, 66], [763, 10, 2185, 70], [763, 11, 2185, 72], [764, 10, 2189, 10, "showsVerticalScrollIndicator"], [764, 38, 2189, 38], [764, 40, 2189, 40], [764, 45, 2189, 46], [765, 10, 2189, 46, "children"], [765, 18, 2189, 46], [765, 33, 2197, 10], [765, 37, 2197, 10, "_jsxDevRuntime"], [765, 51, 2197, 10], [765, 52, 2197, 10, "jsxDEV"], [765, 58, 2197, 10], [765, 60, 2197, 11, "_View"], [765, 65, 2197, 11], [765, 66, 2197, 11, "default"], [765, 73, 2197, 15], [766, 12, 2197, 16, "style"], [766, 17, 2197, 21], [766, 19, 2197, 23], [767, 14, 2197, 25, "padding"], [767, 21, 2197, 32], [767, 23, 2197, 34], [768, 12, 2197, 37], [768, 13, 2197, 39], [769, 12, 2197, 39, "children"], [769, 20, 2197, 39], [769, 36, 2205, 12], [769, 40, 2205, 12, "_jsxDevRuntime"], [769, 54, 2205, 12], [769, 55, 2205, 12, "jsxDEV"], [769, 61, 2205, 12], [769, 63, 2205, 13, "LocationStatus"], [769, 77, 2205, 27], [770, 14, 2205, 27, "fileName"], [770, 22, 2205, 27], [770, 24, 2205, 27, "_jsxFileName"], [770, 36, 2205, 27], [771, 14, 2205, 27, "lineNumber"], [771, 24, 2205, 27], [772, 14, 2205, 27, "columnNumber"], [772, 26, 2205, 27], [773, 12, 2205, 27], [773, 19, 2205, 29], [773, 20, 2205, 30], [773, 35, 2211, 12], [773, 39, 2211, 12, "_jsxDevRuntime"], [773, 53, 2211, 12], [773, 54, 2211, 12, "jsxDEV"], [773, 60, 2211, 12], [773, 62, 2211, 13, "_View"], [773, 67, 2211, 13], [773, 68, 2211, 13, "default"], [773, 75, 2211, 17], [774, 14, 2211, 18, "style"], [774, 19, 2211, 23], [774, 21, 2211, 25], [775, 16, 2211, 27, "marginBottom"], [775, 28, 2211, 39], [775, 30, 2211, 41], [776, 14, 2211, 44], [776, 15, 2211, 46], [777, 14, 2211, 46, "children"], [777, 22, 2211, 46], [777, 38, 2213, 14], [777, 42, 2213, 14, "_jsxDevRuntime"], [777, 56, 2213, 14], [777, 57, 2213, 14, "jsxDEV"], [777, 63, 2213, 14], [777, 65, 2213, 15, "_View"], [777, 70, 2213, 15], [777, 71, 2213, 15, "default"], [777, 78, 2213, 19], [778, 16, 2215, 16, "style"], [778, 21, 2215, 21], [778, 23, 2215, 23], [779, 18, 2217, 18, "flexDirection"], [779, 31, 2217, 31], [779, 33, 2217, 33], [779, 38, 2217, 38], [780, 18, 2219, 18, "alignItems"], [780, 28, 2219, 28], [780, 30, 2219, 30], [780, 38, 2219, 38], [781, 18, 2221, 18, "marginBottom"], [781, 30, 2221, 30], [781, 32, 2221, 32], [782, 16, 2223, 16], [782, 17, 2223, 18], [783, 16, 2223, 18, "children"], [783, 24, 2223, 18], [783, 40, 2227, 16], [783, 44, 2227, 16, "_jsxDevRuntime"], [783, 58, 2227, 16], [783, 59, 2227, 16, "jsxDEV"], [783, 65, 2227, 16], [783, 67, 2227, 17, "_View"], [783, 72, 2227, 17], [783, 73, 2227, 17, "default"], [783, 80, 2227, 21], [784, 18, 2229, 18, "style"], [784, 23, 2229, 23], [784, 25, 2229, 25], [785, 20, 2231, 20, "width"], [785, 25, 2231, 25], [785, 27, 2231, 27], [785, 29, 2231, 29], [786, 20, 2233, 20, "height"], [786, 26, 2233, 26], [786, 28, 2233, 28], [786, 30, 2233, 30], [787, 20, 2235, 20, "borderRadius"], [787, 32, 2235, 32], [787, 34, 2235, 34], [787, 36, 2235, 36], [788, 20, 2237, 20, "backgroundColor"], [788, 35, 2237, 35], [788, 37, 2237, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [788, 53, 2237, 53], [788, 56, 2239, 24], [788, 65, 2239, 33], [788, 68, 2241, 24, "locationStatus"], [788, 82, 2241, 38], [788, 87, 2241, 43], [788, 97, 2241, 53], [788, 101, 2241, 57, "testingMode"], [788, 112, 2241, 68], [788, 115, 2243, 26], [788, 124, 2243, 35], [788, 127, 2245, 26], [788, 136, 2245, 35], [789, 20, 2247, 20, "alignItems"], [789, 30, 2247, 30], [789, 32, 2247, 32], [789, 40, 2247, 40], [790, 20, 2249, 20, "justifyContent"], [790, 34, 2249, 34], [790, 36, 2249, 36], [790, 44, 2249, 44], [791, 20, 2251, 20, "marginRight"], [791, 31, 2251, 31], [791, 33, 2251, 33], [792, 18, 2253, 18], [792, 19, 2253, 20], [793, 18, 2253, 20, "children"], [793, 26, 2253, 20], [793, 28, 2257, 19, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [793, 44, 2257, 35], [793, 60, 2259, 20], [793, 64, 2259, 20, "_jsxDevRuntime"], [793, 78, 2259, 20], [793, 79, 2259, 20, "jsxDEV"], [793, 85, 2259, 20], [793, 87, 2259, 21, "_lucideReactNative"], [793, 105, 2259, 21], [793, 106, 2259, 21, "CheckCircle2"], [793, 118, 2259, 33], [794, 20, 2259, 34, "size"], [794, 24, 2259, 38], [794, 26, 2259, 40], [794, 28, 2259, 43], [795, 20, 2259, 44, "color"], [795, 25, 2259, 49], [795, 27, 2259, 50], [796, 18, 2259, 56], [797, 20, 2259, 56, "fileName"], [797, 28, 2259, 56], [797, 30, 2259, 56, "_jsxFileName"], [797, 42, 2259, 56], [798, 20, 2259, 56, "lineNumber"], [798, 30, 2259, 56], [799, 20, 2259, 56, "columnNumber"], [799, 32, 2259, 56], [800, 18, 2259, 56], [800, 25, 2259, 58], [800, 26, 2259, 59], [800, 42, 2263, 20], [800, 46, 2263, 20, "_jsxDevRuntime"], [800, 60, 2263, 20], [800, 61, 2263, 20, "jsxDEV"], [800, 67, 2263, 20], [800, 69, 2263, 21, "_Text"], [800, 74, 2263, 21], [800, 75, 2263, 21, "default"], [800, 82, 2263, 25], [801, 20, 2263, 26, "style"], [801, 25, 2263, 31], [801, 27, 2263, 33], [802, 22, 2263, 35, "color"], [802, 27, 2263, 40], [802, 29, 2263, 42], [802, 35, 2263, 48], [803, 22, 2263, 50, "fontSize"], [803, 30, 2263, 58], [803, 32, 2263, 60], [803, 34, 2263, 62], [804, 22, 2263, 64, "fontWeight"], [804, 32, 2263, 74], [804, 34, 2263, 76], [805, 20, 2263, 82], [805, 21, 2263, 84], [806, 20, 2263, 84, "children"], [806, 28, 2263, 84], [806, 30, 2263, 85], [807, 18, 2263, 86], [808, 20, 2263, 86, "fileName"], [808, 28, 2263, 86], [808, 30, 2263, 86, "_jsxFileName"], [808, 42, 2263, 86], [809, 20, 2263, 86, "lineNumber"], [809, 30, 2263, 86], [810, 20, 2263, 86, "columnNumber"], [810, 32, 2263, 86], [811, 18, 2263, 86], [811, 25, 2263, 92], [812, 16, 2265, 19], [813, 18, 2265, 19, "fileName"], [813, 26, 2265, 19], [813, 28, 2265, 19, "_jsxFileName"], [813, 40, 2265, 19], [814, 18, 2265, 19, "lineNumber"], [814, 28, 2265, 19], [815, 18, 2265, 19, "columnNumber"], [815, 30, 2265, 19], [816, 16, 2265, 19], [816, 23, 2267, 22], [816, 24, 2267, 23], [816, 39, 2269, 16], [816, 43, 2269, 16, "_jsxDevRuntime"], [816, 57, 2269, 16], [816, 58, 2269, 16, "jsxDEV"], [816, 64, 2269, 16], [816, 66, 2269, 17, "_Text"], [816, 71, 2269, 17], [816, 72, 2269, 17, "default"], [816, 79, 2269, 21], [817, 18, 2271, 18, "style"], [817, 23, 2271, 23], [817, 25, 2271, 25], [818, 20, 2273, 20, "fontSize"], [818, 28, 2273, 28], [818, 30, 2273, 30], [818, 32, 2273, 32], [819, 20, 2275, 20, "fontWeight"], [819, 30, 2275, 30], [819, 32, 2275, 32], [819, 37, 2275, 37], [820, 20, 2277, 20, "color"], [820, 25, 2277, 25], [820, 27, 2277, 27], [821, 18, 2279, 18], [821, 19, 2279, 20], [822, 18, 2279, 20, "children"], [822, 26, 2279, 20], [822, 28, 2281, 17], [823, 16, 2285, 16], [824, 18, 2285, 16, "fileName"], [824, 26, 2285, 16], [824, 28, 2285, 16, "_jsxFileName"], [824, 40, 2285, 16], [825, 18, 2285, 16, "lineNumber"], [825, 28, 2285, 16], [826, 18, 2285, 16, "columnNumber"], [826, 30, 2285, 16], [827, 16, 2285, 16], [827, 23, 2285, 22], [827, 24, 2285, 23], [828, 14, 2285, 23], [829, 16, 2285, 23, "fileName"], [829, 24, 2285, 23], [829, 26, 2285, 23, "_jsxFileName"], [829, 38, 2285, 23], [830, 16, 2285, 23, "lineNumber"], [830, 26, 2285, 23], [831, 16, 2285, 23, "columnNumber"], [831, 28, 2285, 23], [832, 14, 2285, 23], [832, 21, 2287, 20], [832, 22, 2287, 21], [832, 37, 2289, 14], [832, 41, 2289, 14, "_jsxDevRuntime"], [832, 55, 2289, 14], [832, 56, 2289, 14, "jsxDEV"], [832, 62, 2289, 14], [832, 64, 2289, 15, "_Text"], [832, 69, 2289, 15], [832, 70, 2289, 15, "default"], [832, 77, 2289, 19], [833, 16, 2291, 16, "style"], [833, 21, 2291, 21], [833, 23, 2291, 23], [834, 18, 2293, 18, "fontSize"], [834, 26, 2293, 26], [834, 28, 2293, 28], [834, 30, 2293, 30], [835, 18, 2295, 18, "color"], [835, 23, 2295, 23], [835, 25, 2295, 25], [835, 34, 2295, 34], [836, 18, 2297, 18, "marginBottom"], [836, 30, 2297, 30], [836, 32, 2297, 32], [836, 34, 2297, 34], [837, 18, 2299, 18, "lineHeight"], [837, 28, 2299, 28], [837, 30, 2299, 30], [838, 16, 2301, 16], [838, 17, 2301, 18], [839, 16, 2301, 18, "children"], [839, 24, 2301, 18], [839, 26, 2305, 17], [840, 14, 2305, 148], [841, 16, 2305, 148, "fileName"], [841, 24, 2305, 148], [841, 26, 2305, 148, "_jsxFileName"], [841, 38, 2305, 148], [842, 16, 2305, 148, "lineNumber"], [842, 26, 2305, 148], [843, 16, 2305, 148, "columnNumber"], [843, 28, 2305, 148], [844, 14, 2305, 148], [844, 21, 2307, 20], [844, 22, 2307, 21], [844, 24, 2309, 15, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [844, 40, 2309, 31], [844, 56, 2311, 16], [844, 60, 2311, 16, "_jsxDevRuntime"], [844, 74, 2311, 16], [844, 75, 2311, 16, "jsxDEV"], [844, 81, 2311, 16], [844, 83, 2311, 17, "_View"], [844, 88, 2311, 17], [844, 89, 2311, 17, "default"], [844, 96, 2311, 21], [845, 16, 2313, 18, "style"], [845, 21, 2313, 23], [845, 23, 2313, 25], [846, 18, 2315, 20, "backgroundColor"], [846, 33, 2315, 35], [846, 35, 2315, 37], [846, 41, 2315, 43], [847, 18, 2317, 20, "borderRadius"], [847, 30, 2317, 32], [847, 32, 2317, 34], [847, 34, 2317, 36], [848, 18, 2319, 20, "overflow"], [848, 26, 2319, 28], [848, 28, 2319, 30], [848, 36, 2319, 38], [849, 18, 2321, 20, "borderWidth"], [849, 29, 2321, 31], [849, 31, 2321, 33], [849, 32, 2321, 34], [850, 18, 2323, 20, "borderColor"], [850, 29, 2323, 31], [850, 31, 2323, 33], [850, 40, 2323, 42], [851, 18, 2325, 20], [851, 21, 2325, 23, "Platform"], [851, 38, 2325, 31], [851, 39, 2325, 32, "select"], [851, 45, 2325, 38], [851, 46, 2325, 39], [852, 20, 2327, 22, "ios"], [852, 23, 2327, 25], [852, 25, 2327, 27], [853, 22, 2329, 24, "shadowColor"], [853, 33, 2329, 35], [853, 35, 2329, 37], [853, 41, 2329, 43], [854, 22, 2331, 24, "shadowOffset"], [854, 34, 2331, 36], [854, 36, 2331, 38], [855, 24, 2331, 40, "width"], [855, 29, 2331, 45], [855, 31, 2331, 47], [855, 32, 2331, 48], [856, 24, 2331, 50, "height"], [856, 30, 2331, 56], [856, 32, 2331, 58], [857, 22, 2331, 60], [857, 23, 2331, 61], [858, 22, 2333, 24, "shadowOpacity"], [858, 35, 2333, 37], [858, 37, 2333, 39], [858, 41, 2333, 43], [859, 22, 2335, 24, "shadowRadius"], [859, 34, 2335, 36], [859, 36, 2335, 38], [860, 20, 2337, 22], [860, 21, 2337, 23], [861, 20, 2339, 22, "android"], [861, 27, 2339, 29], [861, 29, 2339, 31], [862, 22, 2341, 24, "elevation"], [862, 31, 2341, 33], [862, 33, 2341, 35], [863, 20, 2343, 22], [863, 21, 2343, 23], [864, 20, 2345, 22, "web"], [864, 23, 2345, 25], [864, 25, 2345, 27], [865, 22, 2347, 24, "boxShadow"], [865, 31, 2347, 33], [865, 33, 2347, 35], [866, 20, 2349, 22], [867, 18, 2351, 20], [867, 19, 2351, 21], [868, 16, 2353, 18], [868, 17, 2353, 20], [869, 16, 2353, 20, "children"], [869, 24, 2353, 20], [869, 40, 2357, 18], [869, 44, 2357, 18, "_jsxDevRuntime"], [869, 58, 2357, 18], [869, 59, 2357, 18, "jsxDEV"], [869, 65, 2357, 18], [869, 67, 2357, 19, "_View"], [869, 72, 2357, 19], [869, 73, 2357, 19, "default"], [869, 80, 2357, 23], [870, 18, 2357, 24, "style"], [870, 23, 2357, 29], [870, 25, 2357, 31], [871, 20, 2357, 33, "position"], [871, 28, 2357, 41], [871, 30, 2357, 43], [872, 18, 2357, 54], [872, 19, 2357, 56], [873, 18, 2357, 56, "children"], [873, 26, 2357, 56], [873, 42, 2359, 20], [873, 46, 2359, 20, "_jsxDevRuntime"], [873, 60, 2359, 20], [873, 61, 2359, 20, "jsxDEV"], [873, 67, 2359, 20], [873, 69, 2359, 21, "_View"], [873, 74, 2359, 21], [873, 75, 2359, 21, "default"], [873, 82, 2359, 25], [874, 20, 2359, 26, "style"], [874, 25, 2359, 31], [874, 27, 2359, 33], [875, 22, 2360, 22, "position"], [875, 30, 2360, 30], [875, 32, 2360, 32], [875, 42, 2360, 42], [876, 22, 2361, 22, "top"], [876, 25, 2361, 25], [876, 27, 2361, 27], [876, 28, 2361, 28], [877, 22, 2362, 22, "left"], [877, 26, 2362, 26], [877, 28, 2362, 28], [877, 29, 2362, 29], [878, 22, 2363, 22, "backgroundColor"], [878, 37, 2363, 37], [878, 39, 2363, 39], [878, 56, 2363, 56], [879, 22, 2364, 22, "padding"], [879, 29, 2364, 29], [879, 31, 2364, 31], [879, 32, 2364, 32], [880, 22, 2365, 22, "borderRadius"], [880, 34, 2365, 34], [880, 36, 2365, 36], [880, 37, 2365, 37], [881, 22, 2366, 22, "zIndex"], [881, 28, 2366, 28], [881, 30, 2366, 30], [882, 20, 2367, 20], [882, 21, 2367, 22], [883, 20, 2367, 22, "children"], [883, 28, 2367, 22], [883, 44, 2368, 22], [883, 48, 2368, 22, "_jsxDevRuntime"], [883, 62, 2368, 22], [883, 63, 2368, 22, "jsxDEV"], [883, 69, 2368, 22], [883, 71, 2368, 23, "_Text"], [883, 76, 2368, 23], [883, 77, 2368, 23, "default"], [883, 84, 2368, 27], [884, 22, 2368, 28, "style"], [884, 27, 2368, 33], [884, 29, 2368, 35], [885, 24, 2368, 37, "color"], [885, 29, 2368, 42], [885, 31, 2368, 44], [885, 38, 2368, 51], [886, 24, 2368, 53, "fontSize"], [886, 32, 2368, 61], [886, 34, 2368, 63], [887, 22, 2368, 66], [887, 23, 2368, 68], [888, 22, 2368, 68, "children"], [888, 30, 2368, 68], [888, 32, 2369, 25, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [888, 48, 2369, 41], [888, 50, 2369, 43, "startsWith"], [888, 60, 2369, 53], [888, 61, 2369, 54], [888, 68, 2369, 61], [888, 69, 2369, 62], [888, 72, 2369, 65], [888, 85, 2369, 78], [888, 88, 2370, 25, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [888, 104, 2370, 41], [888, 106, 2370, 43, "startsWith"], [888, 116, 2370, 53], [888, 117, 2370, 54], [888, 124, 2370, 61], [888, 125, 2370, 62], [888, 128, 2370, 65], [888, 137, 2370, 74], [888, 140, 2371, 25, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [888, 156, 2371, 41], [888, 158, 2371, 43, "startsWith"], [888, 168, 2371, 53], [888, 169, 2371, 54], [888, 175, 2371, 60], [888, 176, 2371, 61], [888, 179, 2371, 64], [888, 187, 2371, 72], [888, 190, 2371, 75], [889, 20, 2371, 86], [890, 22, 2371, 86, "fileName"], [890, 30, 2371, 86], [890, 32, 2371, 86, "_jsxFileName"], [890, 44, 2371, 86], [891, 22, 2371, 86, "lineNumber"], [891, 32, 2371, 86], [892, 22, 2371, 86, "columnNumber"], [892, 34, 2371, 86], [893, 20, 2371, 86], [893, 27, 2372, 28], [893, 28, 2372, 29], [893, 43, 2373, 22], [893, 47, 2373, 22, "_jsxDevRuntime"], [893, 61, 2373, 22], [893, 62, 2373, 22, "jsxDEV"], [893, 68, 2373, 22], [893, 70, 2373, 23, "_Text"], [893, 75, 2373, 23], [893, 76, 2373, 23, "default"], [893, 83, 2373, 27], [894, 22, 2373, 28, "style"], [894, 27, 2373, 33], [894, 29, 2373, 35], [895, 24, 2373, 37, "color"], [895, 29, 2373, 42], [895, 31, 2373, 44], [895, 38, 2373, 51], [896, 24, 2373, 53, "fontSize"], [896, 32, 2373, 61], [896, 34, 2373, 63], [897, 22, 2373, 65], [897, 23, 2373, 67], [898, 22, 2373, 67, "children"], [898, 30, 2373, 67], [898, 33, 2374, 25, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [898, 49, 2374, 41], [898, 51, 2374, 43, "substring"], [898, 60, 2374, 52], [898, 61, 2374, 53], [898, 62, 2374, 54], [898, 64, 2374, 56], [898, 66, 2374, 58], [898, 67, 2374, 59], [898, 69, 2374, 60], [898, 74, 2375, 22], [899, 20, 2375, 22], [900, 22, 2375, 22, "fileName"], [900, 30, 2375, 22], [900, 32, 2375, 22, "_jsxFileName"], [900, 44, 2375, 22], [901, 22, 2375, 22, "lineNumber"], [901, 32, 2375, 22], [902, 22, 2375, 22, "columnNumber"], [902, 34, 2375, 22], [903, 20, 2375, 22], [903, 27, 2375, 28], [903, 28, 2375, 29], [904, 18, 2375, 29], [905, 20, 2375, 29, "fileName"], [905, 28, 2375, 29], [905, 30, 2375, 29, "_jsxFileName"], [905, 42, 2375, 29], [906, 20, 2375, 29, "lineNumber"], [906, 30, 2375, 29], [907, 20, 2375, 29, "columnNumber"], [907, 32, 2375, 29], [908, 18, 2375, 29], [908, 25, 2376, 26], [908, 26, 2376, 27], [908, 41, 2378, 20], [908, 45, 2378, 20, "_jsxDevRuntime"], [908, 59, 2378, 20], [908, 60, 2378, 20, "jsxDEV"], [908, 66, 2378, 20], [908, 68, 2378, 21, "_Image"], [908, 74, 2378, 21], [908, 75, 2378, 21, "default"], [908, 82, 2378, 26], [909, 20, 2380, 22, "source"], [909, 26, 2380, 28], [909, 28, 2380, 30], [910, 22, 2380, 32, "uri"], [910, 25, 2380, 35], [910, 27, 2380, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [911, 20, 2380, 54], [911, 21, 2380, 56], [912, 20, 2381, 45], [914, 20, 2383, 22, "style"], [914, 25, 2383, 27], [914, 27, 2383, 29], [915, 22, 2384, 24, "width"], [915, 27, 2384, 29], [915, 29, 2384, 31], [915, 35, 2384, 37], [916, 22, 2385, 24, "height"], [916, 28, 2385, 30], [916, 30, 2385, 32], [916, 33, 2385, 35], [917, 22, 2385, 37], [918, 22, 2386, 24, "backgroundColor"], [918, 37, 2386, 39], [918, 39, 2386, 41], [918, 48, 2386, 50], [919, 22, 2387, 24, "borderRadius"], [919, 34, 2387, 36], [919, 36, 2387, 38], [919, 38, 2387, 40], [919, 39, 2387, 42], [920, 20, 2388, 22], [920, 21, 2388, 24], [921, 20, 2390, 22, "resizeMode"], [921, 30, 2390, 32], [921, 32, 2390, 33], [921, 41, 2390, 42], [921, 42, 2390, 43], [922, 20, 2390, 43], [924, 20, 2392, 22, "onError"], [924, 27, 2392, 29], [924, 29, 2392, 32, "e"], [924, 30, 2392, 33], [924, 34, 2392, 38], [925, 22, 2394, 24, "console"], [925, 29, 2394, 31], [925, 30, 2394, 32, "error"], [925, 35, 2394, 37], [925, 36, 2394, 38], [925, 73, 2394, 75], [925, 75, 2394, 77], [926, 24, 2396, 26, "error"], [926, 29, 2396, 31], [926, 31, 2396, 33, "e"], [926, 32, 2396, 34], [926, 33, 2396, 35, "nativeEvent"], [926, 44, 2396, 46], [926, 46, 2396, 48, "error"], [926, 51, 2396, 53], [927, 24, 2398, 26, "uri"], [927, 27, 2398, 29], [927, 29, 2398, 31, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [927, 45, 2398, 47], [928, 24, 2400, 26, "cameraResult"], [928, 36, 2400, 38], [928, 38, 2400, 40, "JSON"], [928, 42, 2400, 44], [928, 43, 2400, 45, "stringify"], [928, 52, 2400, 54], [928, 53, 2400, 55, "cameraResult"], [928, 65, 2400, 67], [928, 67, 2400, 69], [928, 71, 2400, 73], [928, 73, 2400, 75], [928, 74, 2400, 76], [929, 22, 2402, 24], [929, 23, 2402, 25], [929, 24, 2402, 26], [930, 20, 2404, 22], [930, 21, 2404, 24], [931, 20, 2406, 22, "onLoad"], [931, 26, 2406, 28], [931, 28, 2406, 30, "onLoad"], [931, 29, 2406, 30], [931, 34, 2406, 36], [932, 22, 2408, 24, "console"], [932, 29, 2408, 31], [932, 30, 2408, 32, "log"], [932, 33, 2408, 35], [932, 34, 2408, 36], [932, 78, 2408, 80], [932, 80, 2408, 82, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [932, 96, 2408, 98], [932, 97, 2408, 99], [933, 20, 2410, 22], [933, 21, 2410, 24], [934, 20, 2412, 22, "onLoadStart"], [934, 31, 2412, 33], [934, 33, 2412, 35, "onLoadStart"], [934, 34, 2412, 35], [934, 39, 2412, 41], [935, 22, 2414, 24, "console"], [935, 29, 2414, 31], [935, 30, 2414, 32, "log"], [935, 33, 2414, 35], [935, 34, 2414, 36], [935, 69, 2414, 71], [935, 71, 2414, 73, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [935, 87, 2414, 89], [935, 88, 2414, 90], [936, 22, 2415, 24, "console"], [936, 29, 2415, 31], [936, 30, 2415, 32, "log"], [936, 33, 2415, 35], [936, 34, 2415, 36], [936, 71, 2415, 73], [936, 73, 2415, 75], [937, 24, 2416, 26, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [937, 40, 2416, 42], [938, 24, 2417, 26, "isBlob"], [938, 30, 2417, 32], [938, 32, 2417, 34, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [938, 48, 2417, 50], [938, 50, 2417, 52, "startsWith"], [938, 60, 2417, 62], [938, 61, 2417, 63], [938, 68, 2417, 70], [938, 69, 2417, 71], [939, 24, 2418, 26, "isDataUri"], [939, 33, 2418, 35], [939, 35, 2418, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [939, 51, 2418, 53], [939, 53, 2418, 55, "startsWith"], [939, 63, 2418, 65], [939, 64, 2418, 66], [939, 71, 2418, 73], [939, 72, 2418, 74], [940, 24, 2419, 26, "length"], [940, 30, 2419, 32], [940, 32, 2419, 34, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [940, 48, 2419, 50], [940, 50, 2419, 52, "length"], [941, 22, 2420, 24], [941, 23, 2420, 25], [941, 24, 2420, 26], [942, 20, 2422, 22], [942, 21, 2422, 24], [943, 20, 2424, 22, "onLoadEnd"], [943, 29, 2424, 31], [943, 31, 2424, 33, "onLoadEnd"], [943, 32, 2424, 33], [943, 37, 2424, 39], [944, 22, 2426, 24, "console"], [944, 29, 2426, 31], [944, 30, 2426, 32, "log"], [944, 33, 2426, 35], [944, 34, 2426, 36], [944, 70, 2426, 72], [944, 71, 2426, 73], [945, 20, 2428, 22], [946, 18, 2428, 24], [946, 21, 2381, 27, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [946, 37, 2381, 43], [947, 20, 2381, 43, "fileName"], [947, 28, 2381, 43], [947, 30, 2381, 43, "_jsxFileName"], [947, 42, 2381, 43], [948, 20, 2381, 43, "lineNumber"], [948, 30, 2381, 43], [949, 20, 2381, 43, "columnNumber"], [949, 32, 2381, 43], [950, 18, 2381, 43], [950, 25, 2430, 21], [950, 26, 2430, 22], [950, 41, 2432, 20], [950, 45, 2432, 20, "_jsxDevRuntime"], [950, 59, 2432, 20], [950, 60, 2432, 20, "jsxDEV"], [950, 66, 2432, 20], [950, 68, 2432, 21, "_View"], [950, 73, 2432, 21], [950, 74, 2432, 21, "default"], [950, 81, 2432, 25], [951, 20, 2434, 22, "style"], [951, 25, 2434, 27], [951, 27, 2434, 29], [952, 22, 2436, 24, "position"], [952, 30, 2436, 32], [952, 32, 2436, 34], [952, 42, 2436, 44], [953, 22, 2438, 24, "top"], [953, 25, 2438, 27], [953, 27, 2438, 29], [953, 29, 2438, 31], [954, 22, 2440, 24, "right"], [954, 27, 2440, 29], [954, 29, 2440, 31], [954, 31, 2440, 33], [955, 22, 2442, 24, "backgroundColor"], [955, 37, 2442, 39], [955, 39, 2442, 41], [955, 65, 2442, 67], [956, 22, 2444, 24, "paddingHorizontal"], [956, 39, 2444, 41], [956, 41, 2444, 43], [956, 43, 2444, 45], [957, 22, 2446, 24, "paddingVertical"], [957, 37, 2446, 39], [957, 39, 2446, 41], [957, 40, 2446, 42], [958, 22, 2448, 24, "borderRadius"], [958, 34, 2448, 36], [958, 36, 2448, 38], [958, 38, 2448, 40], [959, 22, 2450, 24, "flexDirection"], [959, 35, 2450, 37], [959, 37, 2450, 39], [959, 42, 2450, 44], [960, 22, 2452, 24, "alignItems"], [960, 32, 2452, 34], [960, 34, 2452, 36], [961, 20, 2454, 22], [961, 21, 2454, 24], [962, 20, 2454, 24, "children"], [962, 28, 2454, 24], [962, 44, 2458, 22], [962, 48, 2458, 22, "_jsxDevRuntime"], [962, 62, 2458, 22], [962, 63, 2458, 22, "jsxDEV"], [962, 69, 2458, 22], [962, 71, 2458, 23, "_lucideReactNative"], [962, 89, 2458, 23], [962, 90, 2458, 23, "Shield"], [962, 96, 2458, 29], [963, 22, 2458, 30, "size"], [963, 26, 2458, 34], [963, 28, 2458, 36], [963, 30, 2458, 39], [964, 22, 2458, 40, "color"], [964, 27, 2458, 45], [964, 29, 2458, 46], [965, 20, 2458, 52], [966, 22, 2458, 52, "fileName"], [966, 30, 2458, 52], [966, 32, 2458, 52, "_jsxFileName"], [966, 44, 2458, 52], [967, 22, 2458, 52, "lineNumber"], [967, 32, 2458, 52], [968, 22, 2458, 52, "columnNumber"], [968, 34, 2458, 52], [969, 20, 2458, 52], [969, 27, 2458, 54], [969, 28, 2458, 55], [969, 43, 2460, 22], [969, 47, 2460, 22, "_jsxDevRuntime"], [969, 61, 2460, 22], [969, 62, 2460, 22, "jsxDEV"], [969, 68, 2460, 22], [969, 70, 2460, 23, "_Text"], [969, 75, 2460, 23], [969, 76, 2460, 23, "default"], [969, 83, 2460, 27], [970, 22, 2460, 28, "style"], [970, 27, 2460, 33], [970, 29, 2460, 35], [971, 24, 2460, 37, "color"], [971, 29, 2460, 42], [971, 31, 2460, 44], [971, 37, 2460, 50], [972, 24, 2460, 52, "fontSize"], [972, 32, 2460, 60], [972, 34, 2460, 62], [972, 36, 2460, 64], [973, 24, 2460, 66, "fontWeight"], [973, 34, 2460, 76], [973, 36, 2460, 78], [973, 41, 2460, 83], [974, 24, 2460, 85, "marginLeft"], [974, 34, 2460, 95], [974, 36, 2460, 97], [975, 22, 2460, 99], [975, 23, 2460, 101], [976, 22, 2460, 101, "children"], [976, 30, 2460, 101], [976, 32, 2460, 102], [977, 20, 2464, 22], [978, 22, 2464, 22, "fileName"], [978, 30, 2464, 22], [978, 32, 2464, 22, "_jsxFileName"], [978, 44, 2464, 22], [979, 22, 2464, 22, "lineNumber"], [979, 32, 2464, 22], [980, 22, 2464, 22, "columnNumber"], [980, 34, 2464, 22], [981, 20, 2464, 22], [981, 27, 2464, 28], [981, 28, 2464, 29], [982, 18, 2464, 29], [983, 20, 2464, 29, "fileName"], [983, 28, 2464, 29], [983, 30, 2464, 29, "_jsxFileName"], [983, 42, 2464, 29], [984, 20, 2464, 29, "lineNumber"], [984, 30, 2464, 29], [985, 20, 2464, 29, "columnNumber"], [985, 32, 2464, 29], [986, 18, 2464, 29], [986, 25, 2466, 26], [986, 26, 2466, 27], [987, 16, 2466, 27], [988, 18, 2466, 27, "fileName"], [988, 26, 2466, 27], [988, 28, 2466, 27, "_jsxFileName"], [988, 40, 2466, 27], [989, 18, 2466, 27, "lineNumber"], [989, 28, 2466, 27], [990, 18, 2466, 27, "columnNumber"], [990, 30, 2466, 27], [991, 16, 2466, 27], [991, 23, 2468, 24], [991, 24, 2468, 25], [991, 39, 2470, 18], [991, 43, 2470, 18, "_jsxDevRuntime"], [991, 57, 2470, 18], [991, 58, 2470, 18, "jsxDEV"], [991, 64, 2470, 18], [991, 66, 2470, 19, "_View"], [991, 71, 2470, 19], [991, 72, 2470, 19, "default"], [991, 79, 2470, 23], [992, 18, 2470, 24, "style"], [992, 23, 2470, 29], [992, 25, 2470, 31], [993, 20, 2470, 33, "padding"], [993, 27, 2470, 40], [993, 29, 2470, 42], [994, 18, 2470, 45], [994, 19, 2470, 47], [995, 18, 2470, 47, "children"], [995, 26, 2470, 47], [995, 42, 2472, 20], [995, 46, 2472, 20, "_jsxDevRuntime"], [995, 60, 2472, 20], [995, 61, 2472, 20, "jsxDEV"], [995, 67, 2472, 20], [995, 69, 2472, 21, "_View"], [995, 74, 2472, 21], [995, 75, 2472, 21, "default"], [995, 82, 2472, 25], [996, 20, 2474, 22, "style"], [996, 25, 2474, 27], [996, 27, 2474, 29], [997, 22, 2476, 24, "flexDirection"], [997, 35, 2476, 37], [997, 37, 2476, 39], [997, 42, 2476, 44], [998, 22, 2478, 24, "alignItems"], [998, 32, 2478, 34], [998, 34, 2478, 36], [998, 42, 2478, 44], [999, 22, 2480, 24, "marginBottom"], [999, 34, 2480, 36], [999, 36, 2480, 38], [1000, 20, 2482, 22], [1000, 21, 2482, 24], [1001, 20, 2482, 24, "children"], [1001, 28, 2482, 24], [1001, 44, 2486, 22], [1001, 48, 2486, 22, "_jsxDevRuntime"], [1001, 62, 2486, 22], [1001, 63, 2486, 22, "jsxDEV"], [1001, 69, 2486, 22], [1001, 71, 2486, 23, "_View"], [1001, 76, 2486, 23], [1001, 77, 2486, 23, "default"], [1001, 84, 2486, 27], [1002, 22, 2488, 24, "style"], [1002, 27, 2488, 29], [1002, 29, 2488, 31], [1003, 24, 2490, 26, "width"], [1003, 29, 2490, 31], [1003, 31, 2490, 33], [1003, 33, 2490, 35], [1004, 24, 2492, 26, "height"], [1004, 30, 2492, 32], [1004, 32, 2492, 34], [1004, 34, 2492, 36], [1005, 24, 2494, 26, "borderRadius"], [1005, 36, 2494, 38], [1005, 38, 2494, 40], [1005, 40, 2494, 42], [1006, 24, 2496, 26, "backgroundColor"], [1006, 39, 2496, 41], [1006, 41, 2496, 43], [1006, 50, 2496, 52], [1007, 24, 2498, 26, "alignItems"], [1007, 34, 2498, 36], [1007, 36, 2498, 38], [1007, 44, 2498, 46], [1008, 24, 2500, 26, "justifyContent"], [1008, 38, 2500, 40], [1008, 40, 2500, 42], [1009, 22, 2502, 24], [1009, 23, 2502, 26], [1010, 22, 2502, 26, "children"], [1010, 30, 2502, 26], [1010, 45, 2506, 24], [1010, 49, 2506, 24, "_jsxDevRuntime"], [1010, 63, 2506, 24], [1010, 64, 2506, 24, "jsxDEV"], [1010, 70, 2506, 24], [1010, 72, 2506, 25, "_lucideReactNative"], [1010, 90, 2506, 25], [1010, 91, 2506, 25, "CheckCircle2"], [1010, 103, 2506, 37], [1011, 24, 2506, 38, "size"], [1011, 28, 2506, 42], [1011, 30, 2506, 44], [1011, 32, 2506, 47], [1012, 24, 2506, 48, "color"], [1012, 29, 2506, 53], [1012, 31, 2506, 54], [1013, 22, 2506, 63], [1014, 24, 2506, 63, "fileName"], [1014, 32, 2506, 63], [1014, 34, 2506, 63, "_jsxFileName"], [1014, 46, 2506, 63], [1015, 24, 2506, 63, "lineNumber"], [1015, 34, 2506, 63], [1016, 24, 2506, 63, "columnNumber"], [1016, 36, 2506, 63], [1017, 22, 2506, 63], [1017, 29, 2506, 65], [1018, 20, 2506, 66], [1019, 22, 2506, 66, "fileName"], [1019, 30, 2506, 66], [1019, 32, 2506, 66, "_jsxFileName"], [1019, 44, 2506, 66], [1020, 22, 2506, 66, "lineNumber"], [1020, 32, 2506, 66], [1021, 22, 2506, 66, "columnNumber"], [1021, 34, 2506, 66], [1022, 20, 2506, 66], [1022, 27, 2508, 28], [1022, 28, 2508, 29], [1022, 43, 2510, 22], [1022, 47, 2510, 22, "_jsxDevRuntime"], [1022, 61, 2510, 22], [1022, 62, 2510, 22, "jsxDEV"], [1022, 68, 2510, 22], [1022, 70, 2510, 23, "_View"], [1022, 75, 2510, 23], [1022, 76, 2510, 23, "default"], [1022, 83, 2510, 27], [1023, 22, 2510, 28, "style"], [1023, 27, 2510, 33], [1023, 29, 2510, 35], [1024, 24, 2510, 37, "marginLeft"], [1024, 34, 2510, 47], [1024, 36, 2510, 49], [1024, 38, 2510, 51], [1025, 24, 2510, 53, "flex"], [1025, 28, 2510, 57], [1025, 30, 2510, 59], [1026, 22, 2510, 61], [1026, 23, 2510, 63], [1027, 22, 2510, 63, "children"], [1027, 30, 2510, 63], [1027, 46, 2512, 24], [1027, 50, 2512, 24, "_jsxDevRuntime"], [1027, 64, 2512, 24], [1027, 65, 2512, 24, "jsxDEV"], [1027, 71, 2512, 24], [1027, 73, 2512, 25, "_Text"], [1027, 78, 2512, 25], [1027, 79, 2512, 25, "default"], [1027, 86, 2512, 29], [1028, 24, 2512, 30, "style"], [1028, 29, 2512, 35], [1028, 31, 2512, 37], [1029, 26, 2512, 39, "fontSize"], [1029, 34, 2512, 47], [1029, 36, 2512, 49], [1029, 38, 2512, 51], [1030, 26, 2512, 53, "fontWeight"], [1030, 36, 2512, 63], [1030, 38, 2512, 65], [1030, 43, 2512, 70], [1031, 26, 2512, 72, "color"], [1031, 31, 2512, 77], [1031, 33, 2512, 79], [1032, 24, 2512, 89], [1032, 25, 2512, 91], [1033, 24, 2512, 91, "children"], [1033, 32, 2512, 91], [1033, 34, 2512, 92], [1034, 22, 2516, 24], [1035, 24, 2516, 24, "fileName"], [1035, 32, 2516, 24], [1035, 34, 2516, 24, "_jsxFileName"], [1035, 46, 2516, 24], [1036, 24, 2516, 24, "lineNumber"], [1036, 34, 2516, 24], [1037, 24, 2516, 24, "columnNumber"], [1037, 36, 2516, 24], [1038, 22, 2516, 24], [1038, 29, 2516, 30], [1038, 30, 2516, 31], [1038, 45, 2518, 24], [1038, 49, 2518, 24, "_jsxDevRuntime"], [1038, 63, 2518, 24], [1038, 64, 2518, 24, "jsxDEV"], [1038, 70, 2518, 24], [1038, 72, 2518, 25, "_Text"], [1038, 77, 2518, 25], [1038, 78, 2518, 25, "default"], [1038, 85, 2518, 29], [1039, 24, 2518, 30, "style"], [1039, 29, 2518, 35], [1039, 31, 2518, 37], [1040, 26, 2518, 39, "fontSize"], [1040, 34, 2518, 47], [1040, 36, 2518, 49], [1040, 38, 2518, 51], [1041, 26, 2518, 53, "color"], [1041, 31, 2518, 58], [1041, 33, 2518, 60], [1041, 42, 2518, 69], [1042, 26, 2518, 71, "marginTop"], [1042, 35, 2518, 80], [1042, 37, 2518, 82], [1043, 24, 2518, 84], [1043, 25, 2518, 86], [1044, 24, 2518, 86, "children"], [1044, 32, 2518, 86], [1044, 34, 2518, 87], [1045, 22, 2522, 24], [1046, 24, 2522, 24, "fileName"], [1046, 32, 2522, 24], [1046, 34, 2522, 24, "_jsxFileName"], [1046, 46, 2522, 24], [1047, 24, 2522, 24, "lineNumber"], [1047, 34, 2522, 24], [1048, 24, 2522, 24, "columnNumber"], [1048, 36, 2522, 24], [1049, 22, 2522, 24], [1049, 29, 2522, 30], [1049, 30, 2522, 31], [1050, 20, 2522, 31], [1051, 22, 2522, 31, "fileName"], [1051, 30, 2522, 31], [1051, 32, 2522, 31, "_jsxFileName"], [1051, 44, 2522, 31], [1052, 22, 2522, 31, "lineNumber"], [1052, 32, 2522, 31], [1053, 22, 2522, 31, "columnNumber"], [1053, 34, 2522, 31], [1054, 20, 2522, 31], [1054, 27, 2524, 28], [1054, 28, 2524, 29], [1055, 18, 2524, 29], [1056, 20, 2524, 29, "fileName"], [1056, 28, 2524, 29], [1056, 30, 2524, 29, "_jsxFileName"], [1056, 42, 2524, 29], [1057, 20, 2524, 29, "lineNumber"], [1057, 30, 2524, 29], [1058, 20, 2524, 29, "columnNumber"], [1058, 32, 2524, 29], [1059, 18, 2524, 29], [1059, 25, 2526, 26], [1059, 26, 2526, 27], [1059, 41, 2528, 20], [1059, 45, 2528, 20, "_jsxDevRuntime"], [1059, 59, 2528, 20], [1059, 60, 2528, 20, "jsxDEV"], [1059, 66, 2528, 20], [1059, 68, 2528, 21, "_View"], [1059, 73, 2528, 21], [1059, 74, 2528, 21, "default"], [1059, 81, 2528, 25], [1060, 20, 2530, 22, "style"], [1060, 25, 2530, 27], [1060, 27, 2530, 29], [1061, 22, 2532, 24, "backgroundColor"], [1061, 37, 2532, 39], [1061, 39, 2532, 41], [1061, 48, 2532, 50], [1062, 22, 2534, 24, "borderRadius"], [1062, 34, 2534, 36], [1062, 36, 2534, 38], [1062, 38, 2534, 40], [1063, 22, 2536, 24, "padding"], [1063, 29, 2536, 31], [1063, 31, 2536, 33], [1063, 33, 2536, 35], [1064, 22, 2538, 24, "marginBottom"], [1064, 34, 2538, 36], [1064, 36, 2538, 38], [1065, 20, 2540, 22], [1065, 21, 2540, 24], [1066, 20, 2540, 24, "children"], [1066, 28, 2540, 24], [1066, 44, 2544, 22], [1066, 48, 2544, 22, "_jsxDevRuntime"], [1066, 62, 2544, 22], [1066, 63, 2544, 22, "jsxDEV"], [1066, 69, 2544, 22], [1066, 71, 2544, 23, "_Text"], [1066, 76, 2544, 23], [1066, 77, 2544, 23, "default"], [1066, 84, 2544, 27], [1067, 22, 2546, 24, "style"], [1067, 27, 2546, 29], [1067, 29, 2546, 31], [1068, 24, 2548, 26, "fontSize"], [1068, 32, 2548, 34], [1068, 34, 2548, 36], [1068, 36, 2548, 38], [1069, 24, 2550, 26, "fontWeight"], [1069, 34, 2550, 36], [1069, 36, 2550, 38], [1069, 41, 2550, 43], [1070, 24, 2552, 26, "color"], [1070, 29, 2552, 31], [1070, 31, 2552, 33], [1070, 40, 2552, 42], [1071, 24, 2554, 26, "marginBottom"], [1071, 36, 2554, 38], [1071, 38, 2554, 40], [1071, 39, 2554, 41], [1072, 24, 2556, 26, "textTransform"], [1072, 37, 2556, 39], [1072, 39, 2556, 41], [1072, 50, 2556, 52], [1073, 24, 2558, 26, "letterSpacing"], [1073, 37, 2558, 39], [1073, 39, 2558, 41], [1074, 22, 2560, 24], [1074, 23, 2560, 26], [1075, 22, 2560, 26, "children"], [1075, 30, 2560, 26], [1075, 32, 2562, 23], [1076, 20, 2566, 22], [1077, 22, 2566, 22, "fileName"], [1077, 30, 2566, 22], [1077, 32, 2566, 22, "_jsxFileName"], [1077, 44, 2566, 22], [1078, 22, 2566, 22, "lineNumber"], [1078, 32, 2566, 22], [1079, 22, 2566, 22, "columnNumber"], [1079, 34, 2566, 22], [1080, 20, 2566, 22], [1080, 27, 2566, 28], [1080, 28, 2566, 29], [1080, 43, 2568, 22], [1080, 47, 2568, 22, "_jsxDevRuntime"], [1080, 61, 2568, 22], [1080, 62, 2568, 22, "jsxDEV"], [1080, 68, 2568, 22], [1080, 70, 2568, 23, "_View"], [1080, 75, 2568, 23], [1080, 76, 2568, 23, "default"], [1080, 83, 2568, 27], [1081, 22, 2568, 28, "style"], [1081, 27, 2568, 33], [1081, 29, 2568, 35], [1082, 24, 2568, 37, "flexDirection"], [1082, 37, 2568, 50], [1082, 39, 2568, 52], [1082, 44, 2568, 57], [1083, 24, 2568, 59, "marginBottom"], [1083, 36, 2568, 71], [1083, 38, 2568, 73], [1083, 39, 2568, 74], [1084, 24, 2568, 76, "alignItems"], [1084, 34, 2568, 86], [1084, 36, 2568, 88], [1085, 22, 2568, 101], [1085, 23, 2568, 103], [1086, 22, 2568, 103, "children"], [1086, 30, 2568, 103], [1086, 46, 2570, 24], [1086, 50, 2570, 24, "_jsxDevRuntime"], [1086, 64, 2570, 24], [1086, 65, 2570, 24, "jsxDEV"], [1086, 71, 2570, 24], [1086, 73, 2570, 25, "_lucideReactNative"], [1086, 91, 2570, 25], [1086, 92, 2570, 25, "CheckCircle2"], [1086, 104, 2570, 37], [1087, 24, 2570, 38, "size"], [1087, 28, 2570, 42], [1087, 30, 2570, 44], [1087, 32, 2570, 47], [1088, 24, 2570, 48, "color"], [1088, 29, 2570, 53], [1088, 31, 2570, 54], [1088, 40, 2570, 63], [1089, 24, 2570, 64, "style"], [1089, 29, 2570, 69], [1089, 31, 2570, 71], [1090, 26, 2570, 73, "marginTop"], [1090, 35, 2570, 82], [1090, 37, 2570, 84], [1091, 24, 2570, 86], [1092, 22, 2570, 88], [1093, 24, 2570, 88, "fileName"], [1093, 32, 2570, 88], [1093, 34, 2570, 88, "_jsxFileName"], [1093, 46, 2570, 88], [1094, 24, 2570, 88, "lineNumber"], [1094, 34, 2570, 88], [1095, 24, 2570, 88, "columnNumber"], [1095, 36, 2570, 88], [1096, 22, 2570, 88], [1096, 29, 2570, 90], [1096, 30, 2570, 91], [1096, 45, 2572, 24], [1096, 49, 2572, 24, "_jsxDevRuntime"], [1096, 63, 2572, 24], [1096, 64, 2572, 24, "jsxDEV"], [1096, 70, 2572, 24], [1096, 72, 2572, 25, "_Text"], [1096, 77, 2572, 25], [1096, 78, 2572, 25, "default"], [1096, 85, 2572, 29], [1097, 24, 2572, 30, "style"], [1097, 29, 2572, 35], [1097, 31, 2572, 37], [1098, 26, 2572, 39, "fontSize"], [1098, 34, 2572, 47], [1098, 36, 2572, 49], [1098, 38, 2572, 51], [1099, 26, 2572, 53, "color"], [1099, 31, 2572, 58], [1099, 33, 2572, 60], [1099, 42, 2572, 69], [1100, 26, 2572, 71, "marginLeft"], [1100, 36, 2572, 81], [1100, 38, 2572, 83], [1100, 39, 2572, 84], [1101, 26, 2572, 86, "flex"], [1101, 30, 2572, 90], [1101, 32, 2572, 92], [1102, 24, 2572, 94], [1102, 25, 2572, 96], [1103, 24, 2572, 96, "children"], [1103, 32, 2572, 96], [1103, 34, 2572, 97], [1104, 22, 2576, 24], [1105, 24, 2576, 24, "fileName"], [1105, 32, 2576, 24], [1105, 34, 2576, 24, "_jsxFileName"], [1105, 46, 2576, 24], [1106, 24, 2576, 24, "lineNumber"], [1106, 34, 2576, 24], [1107, 24, 2576, 24, "columnNumber"], [1107, 36, 2576, 24], [1108, 22, 2576, 24], [1108, 29, 2576, 30], [1108, 30, 2576, 31], [1109, 20, 2576, 31], [1110, 22, 2576, 31, "fileName"], [1110, 30, 2576, 31], [1110, 32, 2576, 31, "_jsxFileName"], [1110, 44, 2576, 31], [1111, 22, 2576, 31, "lineNumber"], [1111, 32, 2576, 31], [1112, 22, 2576, 31, "columnNumber"], [1112, 34, 2576, 31], [1113, 20, 2576, 31], [1113, 27, 2578, 28], [1113, 28, 2578, 29], [1113, 30, 2580, 23, "cameraResult"], [1113, 42, 2580, 35], [1113, 44, 2580, 37, "challengeCode"], [1113, 57, 2580, 50], [1113, 61, 2580, 54, "cameraResult"], [1113, 73, 2580, 66], [1113, 74, 2580, 67, "challengeCode"], [1113, 87, 2580, 80], [1113, 88, 2580, 81, "trim"], [1113, 92, 2580, 85], [1113, 93, 2580, 86], [1113, 94, 2580, 87], [1113, 111, 2582, 24], [1113, 115, 2582, 24, "_jsxDevRuntime"], [1113, 129, 2582, 24], [1113, 130, 2582, 24, "jsxDEV"], [1113, 136, 2582, 24], [1113, 138, 2582, 25, "_View"], [1113, 143, 2582, 25], [1113, 144, 2582, 25, "default"], [1113, 151, 2582, 29], [1114, 22, 2582, 30, "style"], [1114, 27, 2582, 35], [1114, 29, 2582, 37], [1115, 24, 2582, 39, "flexDirection"], [1115, 37, 2582, 52], [1115, 39, 2582, 54], [1115, 44, 2582, 59], [1116, 24, 2582, 61, "marginBottom"], [1116, 36, 2582, 73], [1116, 38, 2582, 75], [1116, 39, 2582, 76], [1117, 24, 2582, 78, "alignItems"], [1117, 34, 2582, 88], [1117, 36, 2582, 90], [1118, 22, 2582, 103], [1118, 23, 2582, 105], [1119, 22, 2582, 105, "children"], [1119, 30, 2582, 105], [1119, 46, 2584, 26], [1119, 50, 2584, 26, "_jsxDevRuntime"], [1119, 64, 2584, 26], [1119, 65, 2584, 26, "jsxDEV"], [1119, 71, 2584, 26], [1119, 73, 2584, 27, "_lucideReactNative"], [1119, 91, 2584, 27], [1119, 92, 2584, 27, "CheckCircle2"], [1119, 104, 2584, 39], [1120, 24, 2584, 40, "size"], [1120, 28, 2584, 44], [1120, 30, 2584, 46], [1120, 32, 2584, 49], [1121, 24, 2584, 50, "color"], [1121, 29, 2584, 55], [1121, 31, 2584, 56], [1121, 40, 2584, 65], [1122, 24, 2584, 66, "style"], [1122, 29, 2584, 71], [1122, 31, 2584, 73], [1123, 26, 2584, 75, "marginTop"], [1123, 35, 2584, 84], [1123, 37, 2584, 86], [1124, 24, 2584, 88], [1125, 22, 2584, 90], [1126, 24, 2584, 90, "fileName"], [1126, 32, 2584, 90], [1126, 34, 2584, 90, "_jsxFileName"], [1126, 46, 2584, 90], [1127, 24, 2584, 90, "lineNumber"], [1127, 34, 2584, 90], [1128, 24, 2584, 90, "columnNumber"], [1128, 36, 2584, 90], [1129, 22, 2584, 90], [1129, 29, 2584, 92], [1129, 30, 2584, 93], [1129, 45, 2586, 26], [1129, 49, 2586, 26, "_jsxDevRuntime"], [1129, 63, 2586, 26], [1129, 64, 2586, 26, "jsxDEV"], [1129, 70, 2586, 26], [1129, 72, 2586, 27, "_Text"], [1129, 77, 2586, 27], [1129, 78, 2586, 27, "default"], [1129, 85, 2586, 31], [1130, 24, 2586, 32, "style"], [1130, 29, 2586, 37], [1130, 31, 2586, 39], [1131, 26, 2586, 41, "fontSize"], [1131, 34, 2586, 49], [1131, 36, 2586, 51], [1131, 38, 2586, 53], [1132, 26, 2586, 55, "color"], [1132, 31, 2586, 60], [1132, 33, 2586, 62], [1132, 42, 2586, 71], [1133, 26, 2586, 73, "marginLeft"], [1133, 36, 2586, 83], [1133, 38, 2586, 85], [1133, 39, 2586, 86], [1134, 26, 2586, 88, "flex"], [1134, 30, 2586, 92], [1134, 32, 2586, 94], [1135, 24, 2586, 96], [1135, 25, 2586, 98], [1136, 24, 2586, 98, "children"], [1136, 32, 2586, 98], [1136, 34, 2588, 29], [1136, 57, 2588, 52, "cameraResult"], [1136, 69, 2588, 64], [1136, 70, 2588, 65, "challengeCode"], [1136, 83, 2588, 78], [1136, 87, 2588, 82], [1136, 92, 2588, 87], [1137, 22, 2588, 89], [1138, 24, 2588, 89, "fileName"], [1138, 32, 2588, 89], [1138, 34, 2588, 89, "_jsxFileName"], [1138, 46, 2588, 89], [1139, 24, 2588, 89, "lineNumber"], [1139, 34, 2588, 89], [1140, 24, 2588, 89, "columnNumber"], [1140, 36, 2588, 89], [1141, 22, 2588, 89], [1141, 29, 2590, 32], [1141, 30, 2590, 33], [1142, 20, 2590, 33], [1143, 22, 2590, 33, "fileName"], [1143, 30, 2590, 33], [1143, 32, 2590, 33, "_jsxFileName"], [1143, 44, 2590, 33], [1144, 22, 2590, 33, "lineNumber"], [1144, 32, 2590, 33], [1145, 22, 2590, 33, "columnNumber"], [1145, 34, 2590, 33], [1146, 20, 2590, 33], [1146, 27, 2592, 30], [1146, 28, 2594, 23], [1146, 43, 2596, 22], [1146, 47, 2596, 22, "_jsxDevRuntime"], [1146, 61, 2596, 22], [1146, 62, 2596, 22, "jsxDEV"], [1146, 68, 2596, 22], [1146, 70, 2596, 23, "_View"], [1146, 75, 2596, 23], [1146, 76, 2596, 23, "default"], [1146, 83, 2596, 27], [1147, 22, 2596, 28, "style"], [1147, 27, 2596, 33], [1147, 29, 2596, 35], [1148, 24, 2596, 37, "flexDirection"], [1148, 37, 2596, 50], [1148, 39, 2596, 52], [1148, 44, 2596, 57], [1149, 24, 2596, 59, "alignItems"], [1149, 34, 2596, 69], [1149, 36, 2596, 71], [1150, 22, 2596, 84], [1150, 23, 2596, 86], [1151, 22, 2596, 86, "children"], [1151, 30, 2596, 86], [1151, 46, 2598, 24], [1151, 50, 2598, 24, "_jsxDevRuntime"], [1151, 64, 2598, 24], [1151, 65, 2598, 24, "jsxDEV"], [1151, 71, 2598, 24], [1151, 73, 2598, 25, "_lucideReactNative"], [1151, 91, 2598, 25], [1151, 92, 2598, 25, "CheckCircle2"], [1151, 104, 2598, 37], [1152, 24, 2598, 38, "size"], [1152, 28, 2598, 42], [1152, 30, 2598, 44], [1152, 32, 2598, 47], [1153, 24, 2598, 48, "color"], [1153, 29, 2598, 53], [1153, 31, 2598, 54], [1153, 40, 2598, 63], [1154, 24, 2598, 64, "style"], [1154, 29, 2598, 69], [1154, 31, 2598, 71], [1155, 26, 2598, 73, "marginTop"], [1155, 35, 2598, 82], [1155, 37, 2598, 84], [1156, 24, 2598, 86], [1157, 22, 2598, 88], [1158, 24, 2598, 88, "fileName"], [1158, 32, 2598, 88], [1158, 34, 2598, 88, "_jsxFileName"], [1158, 46, 2598, 88], [1159, 24, 2598, 88, "lineNumber"], [1159, 34, 2598, 88], [1160, 24, 2598, 88, "columnNumber"], [1160, 36, 2598, 88], [1161, 22, 2598, 88], [1161, 29, 2598, 90], [1161, 30, 2598, 91], [1161, 45, 2600, 24], [1161, 49, 2600, 24, "_jsxDevRuntime"], [1161, 63, 2600, 24], [1161, 64, 2600, 24, "jsxDEV"], [1161, 70, 2600, 24], [1161, 72, 2600, 25, "_Text"], [1161, 77, 2600, 25], [1161, 78, 2600, 25, "default"], [1161, 85, 2600, 29], [1162, 24, 2600, 30, "style"], [1162, 29, 2600, 35], [1162, 31, 2600, 37], [1163, 26, 2600, 39, "fontSize"], [1163, 34, 2600, 47], [1163, 36, 2600, 49], [1163, 38, 2600, 51], [1164, 26, 2600, 53, "color"], [1164, 31, 2600, 58], [1164, 33, 2600, 60], [1164, 42, 2600, 69], [1165, 26, 2600, 71, "marginLeft"], [1165, 36, 2600, 81], [1165, 38, 2600, 83], [1165, 39, 2600, 84], [1166, 26, 2600, 86, "flex"], [1166, 30, 2600, 90], [1166, 32, 2600, 92], [1167, 24, 2600, 94], [1167, 25, 2600, 96], [1168, 24, 2600, 96, "children"], [1168, 32, 2600, 96], [1168, 34, 2602, 27], [1168, 57, 2602, 50, "distance"], [1168, 65, 2602, 58], [1168, 69, 2602, 62], [1168, 70, 2602, 63], [1169, 22, 2602, 71], [1170, 24, 2602, 71, "fileName"], [1170, 32, 2602, 71], [1170, 34, 2602, 71, "_jsxFileName"], [1170, 46, 2602, 71], [1171, 24, 2602, 71, "lineNumber"], [1171, 34, 2602, 71], [1172, 24, 2602, 71, "columnNumber"], [1172, 36, 2602, 71], [1173, 22, 2602, 71], [1173, 29, 2604, 30], [1173, 30, 2604, 31], [1174, 20, 2604, 31], [1175, 22, 2604, 31, "fileName"], [1175, 30, 2604, 31], [1175, 32, 2604, 31, "_jsxFileName"], [1175, 44, 2604, 31], [1176, 22, 2604, 31, "lineNumber"], [1176, 32, 2604, 31], [1177, 22, 2604, 31, "columnNumber"], [1177, 34, 2604, 31], [1178, 20, 2604, 31], [1178, 27, 2606, 28], [1178, 28, 2606, 29], [1179, 18, 2606, 29], [1180, 20, 2606, 29, "fileName"], [1180, 28, 2606, 29], [1180, 30, 2606, 29, "_jsxFileName"], [1180, 42, 2606, 29], [1181, 20, 2606, 29, "lineNumber"], [1181, 30, 2606, 29], [1182, 20, 2606, 29, "columnNumber"], [1182, 32, 2606, 29], [1183, 18, 2606, 29], [1183, 25, 2608, 26], [1183, 26, 2608, 27], [1183, 41, 2610, 20], [1183, 45, 2610, 20, "_jsxDevRuntime"], [1183, 59, 2610, 20], [1183, 60, 2610, 20, "jsxDEV"], [1183, 66, 2610, 20], [1183, 68, 2610, 21, "_View"], [1183, 73, 2610, 21], [1183, 74, 2610, 21, "default"], [1183, 81, 2610, 25], [1184, 20, 2610, 26, "style"], [1184, 25, 2610, 31], [1184, 27, 2610, 33], [1185, 22, 2610, 35, "flexDirection"], [1185, 35, 2610, 48], [1185, 37, 2610, 50], [1186, 20, 2610, 56], [1186, 21, 2610, 58], [1187, 20, 2610, 58, "children"], [1187, 28, 2610, 58], [1187, 43, 2612, 22], [1187, 47, 2612, 22, "_jsxDevRuntime"], [1187, 61, 2612, 22], [1187, 62, 2612, 22, "jsxDEV"], [1187, 68, 2612, 22], [1187, 70, 2612, 23, "_TouchableOpacity"], [1187, 87, 2612, 23], [1187, 88, 2612, 23, "default"], [1187, 95, 2612, 39], [1188, 22, 2614, 24, "onPress"], [1188, 29, 2614, 31], [1188, 31, 2614, 33, "onPress"], [1188, 32, 2614, 33], [1188, 37, 2614, 39], [1189, 24, 2616, 26, "setCameraResult"], [1189, 39, 2616, 41], [1189, 40, 2616, 42], [1189, 44, 2616, 46], [1189, 45, 2616, 47], [1190, 24, 2618, 26, "setCapturedPhotoUri"], [1190, 43, 2618, 45], [1190, 44, 2618, 46], [1190, 48, 2618, 50], [1190, 49, 2618, 51], [1191, 24, 2620, 26, "handleStartCamera"], [1191, 41, 2620, 43], [1191, 42, 2620, 44], [1191, 43, 2620, 45], [1192, 22, 2622, 24], [1192, 23, 2622, 26], [1193, 22, 2624, 24, "style"], [1193, 27, 2624, 29], [1193, 29, 2624, 31], [1194, 24, 2626, 26, "flex"], [1194, 28, 2626, 30], [1194, 30, 2626, 32], [1194, 31, 2626, 33], [1195, 24, 2628, 26, "backgroundColor"], [1195, 39, 2628, 41], [1195, 41, 2628, 43], [1195, 47, 2628, 49], [1196, 24, 2630, 26, "borderWidth"], [1196, 35, 2630, 37], [1196, 37, 2630, 39], [1196, 38, 2630, 40], [1197, 24, 2632, 26, "borderColor"], [1197, 35, 2632, 37], [1197, 37, 2632, 39], [1197, 46, 2632, 48], [1198, 24, 2634, 26, "borderRadius"], [1198, 36, 2634, 38], [1198, 38, 2634, 40], [1198, 40, 2634, 42], [1199, 24, 2636, 26, "paddingVertical"], [1199, 39, 2636, 41], [1199, 41, 2636, 43], [1199, 43, 2636, 45], [1200, 24, 2638, 26, "paddingHorizontal"], [1200, 41, 2638, 43], [1200, 43, 2638, 45], [1200, 45, 2638, 47], [1201, 24, 2640, 26, "flexDirection"], [1201, 37, 2640, 39], [1201, 39, 2640, 41], [1201, 44, 2640, 46], [1202, 24, 2642, 26, "alignItems"], [1202, 34, 2642, 36], [1202, 36, 2642, 38], [1202, 44, 2642, 46], [1203, 24, 2644, 26, "justifyContent"], [1203, 38, 2644, 40], [1203, 40, 2644, 42], [1204, 22, 2646, 24], [1204, 23, 2646, 26], [1205, 22, 2646, 26, "children"], [1205, 30, 2646, 26], [1205, 46, 2650, 24], [1205, 50, 2650, 24, "_jsxDevRuntime"], [1205, 64, 2650, 24], [1205, 65, 2650, 24, "jsxDEV"], [1205, 71, 2650, 24], [1205, 73, 2650, 25, "_lucideReactNative"], [1205, 91, 2650, 25], [1205, 92, 2650, 25, "Camera"], [1205, 98, 2650, 31], [1206, 24, 2650, 32, "size"], [1206, 28, 2650, 36], [1206, 30, 2650, 38], [1206, 32, 2650, 41], [1207, 24, 2650, 42, "color"], [1207, 29, 2650, 47], [1207, 31, 2650, 48], [1208, 22, 2650, 57], [1209, 24, 2650, 57, "fileName"], [1209, 32, 2650, 57], [1209, 34, 2650, 57, "_jsxFileName"], [1209, 46, 2650, 57], [1210, 24, 2650, 57, "lineNumber"], [1210, 34, 2650, 57], [1211, 24, 2650, 57, "columnNumber"], [1211, 36, 2650, 57], [1212, 22, 2650, 57], [1212, 29, 2650, 59], [1212, 30, 2650, 60], [1212, 45, 2652, 24], [1212, 49, 2652, 24, "_jsxDevRuntime"], [1212, 63, 2652, 24], [1212, 64, 2652, 24, "jsxDEV"], [1212, 70, 2652, 24], [1212, 72, 2652, 25, "_Text"], [1212, 77, 2652, 25], [1212, 78, 2652, 25, "default"], [1212, 85, 2652, 29], [1213, 24, 2652, 30, "style"], [1213, 29, 2652, 35], [1213, 31, 2652, 37], [1214, 26, 2652, 39, "fontSize"], [1214, 34, 2652, 47], [1214, 36, 2652, 49], [1214, 38, 2652, 51], [1215, 26, 2652, 53, "color"], [1215, 31, 2652, 58], [1215, 33, 2652, 60], [1215, 42, 2652, 69], [1216, 26, 2652, 71, "fontWeight"], [1216, 36, 2652, 81], [1216, 38, 2652, 83], [1216, 43, 2652, 88], [1217, 26, 2652, 90, "marginLeft"], [1217, 36, 2652, 100], [1217, 38, 2652, 102], [1218, 24, 2652, 104], [1218, 25, 2652, 106], [1219, 24, 2652, 106, "children"], [1219, 32, 2652, 106], [1219, 34, 2652, 107], [1220, 22, 2656, 24], [1221, 24, 2656, 24, "fileName"], [1221, 32, 2656, 24], [1221, 34, 2656, 24, "_jsxFileName"], [1221, 46, 2656, 24], [1222, 24, 2656, 24, "lineNumber"], [1222, 34, 2656, 24], [1223, 24, 2656, 24, "columnNumber"], [1223, 36, 2656, 24], [1224, 22, 2656, 24], [1224, 29, 2656, 30], [1224, 30, 2656, 31], [1225, 20, 2656, 31], [1226, 22, 2656, 31, "fileName"], [1226, 30, 2656, 31], [1226, 32, 2656, 31, "_jsxFileName"], [1226, 44, 2656, 31], [1227, 22, 2656, 31, "lineNumber"], [1227, 32, 2656, 31], [1228, 22, 2656, 31, "columnNumber"], [1228, 34, 2656, 31], [1229, 20, 2656, 31], [1229, 27, 2658, 40], [1230, 18, 2658, 41], [1231, 20, 2658, 41, "fileName"], [1231, 28, 2658, 41], [1231, 30, 2658, 41, "_jsxFileName"], [1231, 42, 2658, 41], [1232, 20, 2658, 41, "lineNumber"], [1232, 30, 2658, 41], [1233, 20, 2658, 41, "columnNumber"], [1233, 32, 2658, 41], [1234, 18, 2658, 41], [1234, 25, 2660, 26], [1234, 26, 2660, 27], [1235, 16, 2660, 27], [1236, 18, 2660, 27, "fileName"], [1236, 26, 2660, 27], [1236, 28, 2660, 27, "_jsxFileName"], [1236, 40, 2660, 27], [1237, 18, 2660, 27, "lineNumber"], [1237, 28, 2660, 27], [1238, 18, 2660, 27, "columnNumber"], [1238, 30, 2660, 27], [1239, 16, 2660, 27], [1239, 23, 2662, 24], [1239, 24, 2662, 25], [1240, 14, 2662, 25], [1241, 16, 2662, 25, "fileName"], [1241, 24, 2662, 25], [1241, 26, 2662, 25, "_jsxFileName"], [1241, 38, 2662, 25], [1242, 16, 2662, 25, "lineNumber"], [1242, 26, 2662, 25], [1243, 16, 2662, 25, "columnNumber"], [1243, 28, 2662, 25], [1244, 14, 2662, 25], [1244, 21, 2664, 22], [1244, 22, 2664, 23], [1244, 38, 2668, 16], [1244, 42, 2668, 16, "_jsxDevRuntime"], [1244, 56, 2668, 16], [1244, 57, 2668, 16, "jsxDEV"], [1244, 63, 2668, 16], [1244, 65, 2668, 17, "_TouchableOpacity"], [1244, 82, 2668, 17], [1244, 83, 2668, 17, "default"], [1244, 90, 2668, 33], [1245, 16, 2670, 18, "onPress"], [1245, 23, 2670, 25], [1245, 25, 2670, 27, "handleStartCamera"], [1245, 42, 2670, 45], [1246, 16, 2672, 18, "disabled"], [1246, 24, 2672, 26], [1246, 26, 2672, 28, "locationStatus"], [1246, 40, 2672, 42], [1246, 45, 2672, 47], [1246, 55, 2672, 57], [1246, 59, 2672, 61], [1246, 60, 2672, 62, "testingMode"], [1246, 71, 2672, 74], [1247, 16, 2674, 18, "style"], [1247, 21, 2674, 23], [1247, 23, 2674, 25], [1248, 18, 2676, 20, "backgroundColor"], [1248, 33, 2676, 35], [1248, 35, 2678, 22, "locationStatus"], [1248, 49, 2678, 36], [1248, 54, 2678, 41], [1248, 64, 2678, 51], [1248, 68, 2678, 55, "testingMode"], [1248, 79, 2678, 66], [1248, 82, 2678, 69], [1248, 91, 2678, 78], [1248, 94, 2678, 81], [1248, 103, 2678, 90], [1249, 18, 2680, 20, "borderRadius"], [1249, 30, 2680, 32], [1249, 32, 2680, 34], [1249, 34, 2680, 36], [1250, 18, 2682, 20, "padding"], [1250, 25, 2682, 27], [1250, 27, 2682, 29], [1250, 29, 2682, 31], [1251, 18, 2684, 20, "flexDirection"], [1251, 31, 2684, 33], [1251, 33, 2684, 35], [1251, 38, 2684, 40], [1252, 18, 2686, 20, "alignItems"], [1252, 28, 2686, 30], [1252, 30, 2686, 32], [1252, 38, 2686, 40], [1253, 18, 2688, 20, "justifyContent"], [1253, 32, 2688, 34], [1253, 34, 2688, 36], [1254, 16, 2690, 18], [1254, 17, 2690, 20], [1255, 16, 2690, 20, "children"], [1255, 24, 2690, 20], [1255, 40, 2694, 18], [1255, 44, 2694, 18, "_jsxDevRuntime"], [1255, 58, 2694, 18], [1255, 59, 2694, 18, "jsxDEV"], [1255, 65, 2694, 18], [1255, 67, 2694, 19, "_lucideReactNative"], [1255, 85, 2694, 19], [1255, 86, 2694, 19, "Camera"], [1255, 92, 2694, 25], [1256, 18, 2694, 26, "size"], [1256, 22, 2694, 30], [1256, 24, 2694, 32], [1256, 26, 2694, 35], [1257, 18, 2694, 36, "color"], [1257, 23, 2694, 41], [1257, 25, 2694, 42], [1258, 16, 2694, 48], [1259, 18, 2694, 48, "fileName"], [1259, 26, 2694, 48], [1259, 28, 2694, 48, "_jsxFileName"], [1259, 40, 2694, 48], [1260, 18, 2694, 48, "lineNumber"], [1260, 28, 2694, 48], [1261, 18, 2694, 48, "columnNumber"], [1261, 30, 2694, 48], [1262, 16, 2694, 48], [1262, 23, 2694, 50], [1262, 24, 2694, 51], [1262, 39, 2696, 18], [1262, 43, 2696, 18, "_jsxDevRuntime"], [1262, 57, 2696, 18], [1262, 58, 2696, 18, "jsxDEV"], [1262, 64, 2696, 18], [1262, 66, 2696, 19, "_Text"], [1262, 71, 2696, 19], [1262, 72, 2696, 19, "default"], [1262, 79, 2696, 23], [1263, 18, 2698, 20, "style"], [1263, 23, 2698, 25], [1263, 25, 2698, 27], [1264, 20, 2700, 22, "fontSize"], [1264, 28, 2700, 30], [1264, 30, 2700, 32], [1264, 32, 2700, 34], [1265, 20, 2702, 22, "fontWeight"], [1265, 30, 2702, 32], [1265, 32, 2702, 34], [1265, 37, 2702, 39], [1266, 20, 2704, 22, "color"], [1266, 25, 2704, 27], [1266, 27, 2704, 29], [1266, 33, 2704, 35], [1267, 20, 2706, 22, "marginLeft"], [1267, 30, 2706, 32], [1267, 32, 2706, 34], [1268, 18, 2708, 20], [1268, 19, 2708, 22], [1269, 18, 2708, 22, "children"], [1269, 26, 2708, 22], [1269, 28, 2712, 21, "locationStatus"], [1269, 42, 2712, 35], [1269, 47, 2712, 40], [1269, 57, 2712, 50], [1269, 61, 2712, 54, "testingMode"], [1269, 72, 2712, 65], [1269, 75, 2714, 24], [1269, 89, 2714, 38], [1269, 92, 2716, 24], [1270, 16, 2716, 47], [1271, 18, 2716, 47, "fileName"], [1271, 26, 2716, 47], [1271, 28, 2716, 47, "_jsxFileName"], [1271, 40, 2716, 47], [1272, 18, 2716, 47, "lineNumber"], [1272, 28, 2716, 47], [1273, 18, 2716, 47, "columnNumber"], [1273, 30, 2716, 47], [1274, 16, 2716, 47], [1274, 23, 2718, 24], [1274, 24, 2718, 25], [1274, 26, 2720, 19, "testingMode"], [1274, 37, 2720, 30], [1274, 54, 2722, 20], [1274, 58, 2722, 20, "_jsxDevRuntime"], [1274, 72, 2722, 20], [1274, 73, 2722, 20, "jsxDEV"], [1274, 79, 2722, 20], [1274, 81, 2722, 21, "_Text"], [1274, 86, 2722, 21], [1274, 87, 2722, 21, "default"], [1274, 94, 2722, 25], [1275, 18, 2722, 26, "style"], [1275, 23, 2722, 31], [1275, 25, 2722, 33], [1276, 20, 2722, 35, "fontSize"], [1276, 28, 2722, 43], [1276, 30, 2722, 45], [1276, 32, 2722, 47], [1277, 20, 2722, 49, "color"], [1277, 25, 2722, 54], [1277, 27, 2722, 56], [1277, 33, 2722, 62], [1278, 20, 2722, 64, "marginLeft"], [1278, 30, 2722, 74], [1278, 32, 2722, 76], [1279, 18, 2722, 78], [1279, 19, 2722, 80], [1280, 18, 2722, 80, "children"], [1280, 26, 2722, 80], [1280, 28, 2722, 81], [1281, 16, 2722, 85], [1282, 18, 2722, 85, "fileName"], [1282, 26, 2722, 85], [1282, 28, 2722, 85, "_jsxFileName"], [1282, 40, 2722, 85], [1283, 18, 2722, 85, "lineNumber"], [1283, 28, 2722, 85], [1284, 18, 2722, 85, "columnNumber"], [1284, 30, 2722, 85], [1285, 16, 2722, 85], [1285, 23, 2722, 91], [1285, 24, 2724, 19], [1286, 14, 2724, 19], [1287, 16, 2724, 19, "fileName"], [1287, 24, 2724, 19], [1287, 26, 2724, 19, "_jsxFileName"], [1287, 38, 2724, 19], [1288, 16, 2724, 19, "lineNumber"], [1288, 26, 2724, 19], [1289, 16, 2724, 19, "columnNumber"], [1289, 28, 2724, 19], [1290, 14, 2724, 19], [1290, 21, 2726, 34], [1290, 22, 2728, 15], [1291, 12, 2728, 15], [1292, 14, 2728, 15, "fileName"], [1292, 22, 2728, 15], [1292, 24, 2728, 15, "_jsxFileName"], [1292, 36, 2728, 15], [1293, 14, 2728, 15, "lineNumber"], [1293, 24, 2728, 15], [1294, 14, 2728, 15, "columnNumber"], [1294, 26, 2728, 15], [1295, 12, 2728, 15], [1295, 19, 2730, 18], [1295, 20, 2730, 19], [1295, 35, 2733, 12], [1295, 39, 2733, 12, "_jsxDevRuntime"], [1295, 53, 2733, 12], [1295, 54, 2733, 12, "jsxDEV"], [1295, 60, 2733, 12], [1295, 62, 2733, 13, "_View"], [1295, 67, 2733, 13], [1295, 68, 2733, 13, "default"], [1295, 75, 2733, 17], [1296, 14, 2733, 18, "style"], [1296, 19, 2733, 23], [1296, 21, 2733, 25], [1297, 16, 2733, 27, "marginBottom"], [1297, 28, 2733, 39], [1297, 30, 2733, 41], [1298, 14, 2733, 44], [1298, 15, 2733, 46], [1299, 14, 2733, 46, "children"], [1299, 22, 2733, 46], [1299, 38, 2734, 14], [1299, 42, 2734, 14, "_jsxDevRuntime"], [1299, 56, 2734, 14], [1299, 57, 2734, 14, "jsxDEV"], [1299, 63, 2734, 14], [1299, 65, 2734, 15, "_View"], [1299, 70, 2734, 15], [1299, 71, 2734, 15, "default"], [1299, 78, 2734, 19], [1300, 16, 2735, 16, "style"], [1300, 21, 2735, 21], [1300, 23, 2735, 23], [1301, 18, 2736, 18, "flexDirection"], [1301, 31, 2736, 31], [1301, 33, 2736, 33], [1301, 38, 2736, 38], [1302, 18, 2737, 18, "alignItems"], [1302, 28, 2737, 28], [1302, 30, 2737, 30], [1302, 38, 2737, 38], [1303, 18, 2738, 18, "marginBottom"], [1303, 30, 2738, 30], [1303, 32, 2738, 32], [1304, 16, 2739, 16], [1304, 17, 2739, 18], [1305, 16, 2739, 18, "children"], [1305, 24, 2739, 18], [1305, 40, 2741, 16], [1305, 44, 2741, 16, "_jsxDevRuntime"], [1305, 58, 2741, 16], [1305, 59, 2741, 16, "jsxDEV"], [1305, 65, 2741, 16], [1305, 67, 2741, 17, "_View"], [1305, 72, 2741, 17], [1305, 73, 2741, 17, "default"], [1305, 80, 2741, 21], [1306, 18, 2742, 18, "style"], [1306, 23, 2742, 23], [1306, 25, 2742, 25], [1307, 20, 2743, 20, "width"], [1307, 25, 2743, 25], [1307, 27, 2743, 27], [1307, 29, 2743, 29], [1308, 20, 2744, 20, "height"], [1308, 26, 2744, 26], [1308, 28, 2744, 28], [1308, 30, 2744, 30], [1309, 20, 2745, 20, "borderRadius"], [1309, 32, 2745, 32], [1309, 34, 2745, 34], [1309, 36, 2745, 36], [1310, 20, 2746, 20, "backgroundColor"], [1310, 35, 2746, 35], [1310, 37, 2746, 37, "response"], [1310, 45, 2746, 45], [1310, 46, 2746, 46, "trim"], [1310, 50, 2746, 50], [1310, 51, 2746, 51], [1310, 52, 2746, 52], [1310, 55, 2747, 24], [1310, 64, 2747, 33], [1310, 67, 2748, 24, "cameraResult"], [1310, 79, 2748, 36], [1310, 82, 2749, 26], [1310, 91, 2749, 35], [1310, 94, 2750, 26], [1310, 103, 2750, 35], [1311, 20, 2751, 20, "alignItems"], [1311, 30, 2751, 30], [1311, 32, 2751, 32], [1311, 40, 2751, 40], [1312, 20, 2752, 20, "justifyContent"], [1312, 34, 2752, 34], [1312, 36, 2752, 36], [1312, 44, 2752, 44], [1313, 20, 2753, 20, "marginRight"], [1313, 31, 2753, 31], [1313, 33, 2753, 33], [1314, 18, 2754, 18], [1314, 19, 2754, 20], [1315, 18, 2754, 20, "children"], [1315, 26, 2754, 20], [1315, 28, 2756, 19, "response"], [1315, 36, 2756, 27], [1315, 37, 2756, 28, "trim"], [1315, 41, 2756, 32], [1315, 42, 2756, 33], [1315, 43, 2756, 34], [1315, 59, 2757, 20], [1315, 63, 2757, 20, "_jsxDevRuntime"], [1315, 77, 2757, 20], [1315, 78, 2757, 20, "jsxDEV"], [1315, 84, 2757, 20], [1315, 86, 2757, 21, "_lucideReactNative"], [1315, 104, 2757, 21], [1315, 105, 2757, 21, "CheckCircle2"], [1315, 117, 2757, 33], [1316, 20, 2757, 34, "size"], [1316, 24, 2757, 38], [1316, 26, 2757, 40], [1316, 28, 2757, 43], [1317, 20, 2757, 44, "color"], [1317, 25, 2757, 49], [1317, 27, 2757, 50], [1318, 18, 2757, 56], [1319, 20, 2757, 56, "fileName"], [1319, 28, 2757, 56], [1319, 30, 2757, 56, "_jsxFileName"], [1319, 42, 2757, 56], [1320, 20, 2757, 56, "lineNumber"], [1320, 30, 2757, 56], [1321, 20, 2757, 56, "columnNumber"], [1321, 32, 2757, 56], [1322, 18, 2757, 56], [1322, 25, 2757, 58], [1322, 26, 2757, 59], [1322, 42, 2759, 20], [1322, 46, 2759, 20, "_jsxDevRuntime"], [1322, 60, 2759, 20], [1322, 61, 2759, 20, "jsxDEV"], [1322, 67, 2759, 20], [1322, 69, 2759, 21, "_Text"], [1322, 74, 2759, 21], [1322, 75, 2759, 21, "default"], [1322, 82, 2759, 25], [1323, 20, 2760, 22, "style"], [1323, 25, 2760, 27], [1323, 27, 2760, 29], [1324, 22, 2760, 31, "color"], [1324, 27, 2760, 36], [1324, 29, 2760, 38], [1324, 35, 2760, 44], [1325, 22, 2760, 46, "fontSize"], [1325, 30, 2760, 54], [1325, 32, 2760, 56], [1325, 34, 2760, 58], [1326, 22, 2760, 60, "fontWeight"], [1326, 32, 2760, 70], [1326, 34, 2760, 72], [1327, 20, 2760, 78], [1327, 21, 2760, 80], [1328, 20, 2760, 80, "children"], [1328, 28, 2760, 80], [1328, 30, 2761, 21], [1329, 18, 2763, 20], [1330, 20, 2763, 20, "fileName"], [1330, 28, 2763, 20], [1330, 30, 2763, 20, "_jsxFileName"], [1330, 42, 2763, 20], [1331, 20, 2763, 20, "lineNumber"], [1331, 30, 2763, 20], [1332, 20, 2763, 20, "columnNumber"], [1332, 32, 2763, 20], [1333, 18, 2763, 20], [1333, 25, 2763, 26], [1334, 16, 2764, 19], [1335, 18, 2764, 19, "fileName"], [1335, 26, 2764, 19], [1335, 28, 2764, 19, "_jsxFileName"], [1335, 40, 2764, 19], [1336, 18, 2764, 19, "lineNumber"], [1336, 28, 2764, 19], [1337, 18, 2764, 19, "columnNumber"], [1337, 30, 2764, 19], [1338, 16, 2764, 19], [1338, 23, 2765, 22], [1338, 24, 2765, 23], [1338, 39, 2767, 16], [1338, 43, 2767, 16, "_jsxDevRuntime"], [1338, 57, 2767, 16], [1338, 58, 2767, 16, "jsxDEV"], [1338, 64, 2767, 16], [1338, 66, 2767, 17, "_Text"], [1338, 71, 2767, 17], [1338, 72, 2767, 17, "default"], [1338, 79, 2767, 21], [1339, 18, 2768, 18, "style"], [1339, 23, 2768, 23], [1339, 25, 2768, 25], [1340, 20, 2769, 20, "fontSize"], [1340, 28, 2769, 28], [1340, 30, 2769, 30], [1340, 32, 2769, 32], [1341, 20, 2770, 20, "fontWeight"], [1341, 30, 2770, 30], [1341, 32, 2770, 32], [1341, 37, 2770, 37], [1342, 20, 2771, 20, "color"], [1342, 25, 2771, 25], [1342, 27, 2771, 27], [1343, 18, 2772, 18], [1343, 19, 2772, 20], [1344, 18, 2772, 20, "children"], [1344, 26, 2772, 20], [1344, 28, 2773, 17], [1345, 16, 2775, 16], [1346, 18, 2775, 16, "fileName"], [1346, 26, 2775, 16], [1346, 28, 2775, 16, "_jsxFileName"], [1346, 40, 2775, 16], [1347, 18, 2775, 16, "lineNumber"], [1347, 28, 2775, 16], [1348, 18, 2775, 16, "columnNumber"], [1348, 30, 2775, 16], [1349, 16, 2775, 16], [1349, 23, 2775, 22], [1349, 24, 2775, 23], [1350, 14, 2775, 23], [1351, 16, 2775, 23, "fileName"], [1351, 24, 2775, 23], [1351, 26, 2775, 23, "_jsxFileName"], [1351, 38, 2775, 23], [1352, 16, 2775, 23, "lineNumber"], [1352, 26, 2775, 23], [1353, 16, 2775, 23, "columnNumber"], [1353, 28, 2775, 23], [1354, 14, 2775, 23], [1354, 21, 2776, 20], [1354, 22, 2776, 21], [1354, 37, 2778, 14], [1354, 41, 2778, 14, "_jsxDevRuntime"], [1354, 55, 2778, 14], [1354, 56, 2778, 14, "jsxDEV"], [1354, 62, 2778, 14], [1354, 64, 2778, 15, "_Text"], [1354, 69, 2778, 15], [1354, 70, 2778, 15, "default"], [1354, 77, 2778, 19], [1355, 16, 2779, 16, "style"], [1355, 21, 2779, 21], [1355, 23, 2779, 23], [1356, 18, 2780, 18, "fontSize"], [1356, 26, 2780, 26], [1356, 28, 2780, 28], [1356, 30, 2780, 30], [1357, 18, 2781, 18, "color"], [1357, 23, 2781, 23], [1357, 25, 2781, 25], [1357, 34, 2781, 34], [1358, 18, 2782, 18, "marginBottom"], [1358, 30, 2782, 30], [1358, 32, 2782, 32], [1358, 34, 2782, 34], [1359, 18, 2783, 18, "lineHeight"], [1359, 28, 2783, 28], [1359, 30, 2783, 30], [1360, 16, 2784, 16], [1360, 17, 2784, 18], [1361, 16, 2784, 18, "children"], [1361, 24, 2784, 18], [1361, 26, 2786, 17], [1362, 14, 2786, 109], [1363, 16, 2786, 109, "fileName"], [1363, 24, 2786, 109], [1363, 26, 2786, 109, "_jsxFileName"], [1363, 38, 2786, 109], [1364, 16, 2786, 109, "lineNumber"], [1364, 26, 2786, 109], [1365, 16, 2786, 109, "columnNumber"], [1365, 28, 2786, 109], [1366, 14, 2786, 109], [1366, 21, 2787, 20], [1366, 22, 2787, 21], [1366, 37, 2789, 14], [1366, 41, 2789, 14, "_jsxDevRuntime"], [1366, 55, 2789, 14], [1366, 56, 2789, 14, "jsxDEV"], [1366, 62, 2789, 14], [1366, 64, 2789, 15, "_View"], [1366, 69, 2789, 15], [1366, 70, 2789, 15, "default"], [1366, 77, 2789, 19], [1367, 16, 2790, 16, "style"], [1367, 21, 2790, 21], [1367, 23, 2790, 23], [1368, 18, 2791, 18, "backgroundColor"], [1368, 33, 2791, 33], [1368, 35, 2791, 35], [1368, 41, 2791, 41], [1369, 18, 2792, 18, "borderRadius"], [1369, 30, 2792, 30], [1369, 32, 2792, 32], [1369, 34, 2792, 34], [1370, 18, 2793, 18, "borderWidth"], [1370, 29, 2793, 29], [1370, 31, 2793, 31], [1370, 32, 2793, 32], [1371, 18, 2794, 18, "borderColor"], [1371, 29, 2794, 29], [1371, 31, 2794, 31], [1371, 40, 2794, 40], [1372, 18, 2795, 18, "padding"], [1372, 25, 2795, 25], [1372, 27, 2795, 27], [1373, 16, 2796, 16], [1373, 17, 2796, 18], [1374, 16, 2796, 18, "children"], [1374, 24, 2796, 18], [1374, 40, 2798, 16], [1374, 44, 2798, 16, "_jsxDevRuntime"], [1374, 58, 2798, 16], [1374, 59, 2798, 16, "jsxDEV"], [1374, 65, 2798, 16], [1374, 67, 2798, 17, "_View"], [1374, 72, 2798, 17], [1374, 73, 2798, 17, "default"], [1374, 80, 2798, 21], [1375, 18, 2799, 18, "style"], [1375, 23, 2799, 23], [1375, 25, 2799, 25], [1376, 20, 2800, 20, "flexDirection"], [1376, 33, 2800, 33], [1376, 35, 2800, 35], [1376, 40, 2800, 40], [1377, 20, 2801, 20, "alignItems"], [1377, 30, 2801, 30], [1377, 32, 2801, 32], [1377, 44, 2801, 44], [1378, 20, 2802, 20, "padding"], [1378, 27, 2802, 27], [1378, 29, 2802, 29], [1379, 18, 2803, 18], [1379, 19, 2803, 20], [1380, 18, 2803, 20, "children"], [1380, 26, 2803, 20], [1380, 42, 2805, 18], [1380, 46, 2805, 18, "_jsxDevRuntime"], [1380, 60, 2805, 18], [1380, 61, 2805, 18, "jsxDEV"], [1380, 67, 2805, 18], [1380, 69, 2805, 19, "_lucideReactNative"], [1380, 87, 2805, 19], [1380, 88, 2805, 19, "MessageCircle"], [1380, 101, 2805, 32], [1381, 20, 2806, 20, "size"], [1381, 24, 2806, 24], [1381, 26, 2806, 26], [1381, 28, 2806, 29], [1382, 20, 2807, 20, "color"], [1382, 25, 2807, 25], [1382, 27, 2807, 26], [1382, 36, 2807, 35], [1383, 20, 2808, 20, "style"], [1383, 25, 2808, 25], [1383, 27, 2808, 27], [1384, 22, 2808, 29, "marginTop"], [1384, 31, 2808, 38], [1384, 33, 2808, 40], [1384, 34, 2808, 41], [1385, 22, 2808, 43, "marginRight"], [1385, 33, 2808, 54], [1385, 35, 2808, 56], [1386, 20, 2808, 59], [1387, 18, 2808, 61], [1388, 20, 2808, 61, "fileName"], [1388, 28, 2808, 61], [1388, 30, 2808, 61, "_jsxFileName"], [1388, 42, 2808, 61], [1389, 20, 2808, 61, "lineNumber"], [1389, 30, 2808, 61], [1390, 20, 2808, 61, "columnNumber"], [1390, 32, 2808, 61], [1391, 18, 2808, 61], [1391, 25, 2809, 19], [1391, 26, 2809, 20], [1391, 41, 2810, 18], [1391, 45, 2810, 18, "_jsxDevRuntime"], [1391, 59, 2810, 18], [1391, 60, 2810, 18, "jsxDEV"], [1391, 66, 2810, 18], [1391, 68, 2810, 19, "_TextInput"], [1391, 78, 2810, 19], [1391, 79, 2810, 19, "default"], [1391, 86, 2810, 28], [1392, 20, 2811, 20, "style"], [1392, 25, 2811, 25], [1392, 27, 2811, 27], [1393, 22, 2812, 22, "flex"], [1393, 26, 2812, 26], [1393, 28, 2812, 28], [1393, 29, 2812, 29], [1394, 22, 2813, 22, "fontSize"], [1394, 30, 2813, 30], [1394, 32, 2813, 32], [1394, 34, 2813, 34], [1395, 22, 2814, 22, "color"], [1395, 27, 2814, 27], [1395, 29, 2814, 29], [1395, 38, 2814, 38], [1396, 22, 2815, 22, "minHeight"], [1396, 31, 2815, 31], [1396, 33, 2815, 33], [1396, 36, 2815, 36], [1397, 22, 2816, 22, "textAlignVertical"], [1397, 39, 2816, 39], [1397, 41, 2816, 41], [1398, 20, 2817, 20], [1398, 21, 2817, 22], [1399, 20, 2818, 20, "placeholder"], [1399, 31, 2818, 31], [1399, 33, 2818, 32], [1399, 88, 2818, 87], [1400, 20, 2819, 20, "placeholderTextColor"], [1400, 40, 2819, 40], [1400, 42, 2819, 41], [1400, 51, 2819, 50], [1401, 20, 2820, 20, "value"], [1401, 25, 2820, 25], [1401, 27, 2820, 27, "response"], [1401, 35, 2820, 36], [1402, 20, 2821, 20, "onChangeText"], [1402, 32, 2821, 32], [1402, 34, 2821, 34, "setResponse"], [1402, 45, 2821, 46], [1403, 20, 2822, 20, "multiline"], [1403, 29, 2822, 29], [1404, 20, 2823, 20, "max<PERSON><PERSON><PERSON>"], [1404, 29, 2823, 29], [1404, 31, 2823, 31], [1405, 18, 2823, 35], [1406, 20, 2823, 35, "fileName"], [1406, 28, 2823, 35], [1406, 30, 2823, 35, "_jsxFileName"], [1406, 42, 2823, 35], [1407, 20, 2823, 35, "lineNumber"], [1407, 30, 2823, 35], [1408, 20, 2823, 35, "columnNumber"], [1408, 32, 2823, 35], [1409, 18, 2823, 35], [1409, 25, 2824, 19], [1409, 26, 2824, 20], [1410, 16, 2824, 20], [1411, 18, 2824, 20, "fileName"], [1411, 26, 2824, 20], [1411, 28, 2824, 20, "_jsxFileName"], [1411, 40, 2824, 20], [1412, 18, 2824, 20, "lineNumber"], [1412, 28, 2824, 20], [1413, 18, 2824, 20, "columnNumber"], [1413, 30, 2824, 20], [1414, 16, 2824, 20], [1414, 23, 2825, 22], [1414, 24, 2825, 23], [1414, 39, 2827, 16], [1414, 43, 2827, 16, "_jsxDevRuntime"], [1414, 57, 2827, 16], [1414, 58, 2827, 16, "jsxDEV"], [1414, 64, 2827, 16], [1414, 66, 2827, 17, "_View"], [1414, 71, 2827, 17], [1414, 72, 2827, 17, "default"], [1414, 79, 2827, 21], [1415, 18, 2828, 18, "style"], [1415, 23, 2828, 23], [1415, 25, 2828, 25], [1416, 20, 2829, 20, "flexDirection"], [1416, 33, 2829, 33], [1416, 35, 2829, 35], [1416, 40, 2829, 40], [1417, 20, 2830, 20, "justifyContent"], [1417, 34, 2830, 34], [1417, 36, 2830, 36], [1417, 51, 2830, 51], [1418, 20, 2831, 20, "alignItems"], [1418, 30, 2831, 30], [1418, 32, 2831, 32], [1418, 40, 2831, 40], [1419, 20, 2832, 20, "paddingHorizontal"], [1419, 37, 2832, 37], [1419, 39, 2832, 39], [1419, 41, 2832, 41], [1420, 20, 2833, 20, "paddingBottom"], [1420, 33, 2833, 33], [1420, 35, 2833, 35], [1421, 18, 2834, 18], [1421, 19, 2834, 20], [1422, 18, 2834, 20, "children"], [1422, 26, 2834, 20], [1422, 42, 2836, 18], [1422, 46, 2836, 18, "_jsxDevRuntime"], [1422, 60, 2836, 18], [1422, 61, 2836, 18, "jsxDEV"], [1422, 67, 2836, 18], [1422, 69, 2836, 19, "_Text"], [1422, 74, 2836, 19], [1422, 75, 2836, 19, "default"], [1422, 82, 2836, 23], [1423, 20, 2836, 24, "style"], [1423, 25, 2836, 29], [1423, 27, 2836, 31], [1424, 22, 2836, 33, "fontSize"], [1424, 30, 2836, 41], [1424, 32, 2836, 43], [1424, 34, 2836, 45], [1425, 22, 2836, 47, "color"], [1425, 27, 2836, 52], [1425, 29, 2836, 54], [1426, 20, 2836, 64], [1426, 21, 2836, 66], [1427, 20, 2836, 66, "children"], [1427, 28, 2836, 66], [1427, 30, 2836, 67], [1428, 18, 2838, 18], [1429, 20, 2838, 18, "fileName"], [1429, 28, 2838, 18], [1429, 30, 2838, 18, "_jsxFileName"], [1429, 42, 2838, 18], [1430, 20, 2838, 18, "lineNumber"], [1430, 30, 2838, 18], [1431, 20, 2838, 18, "columnNumber"], [1431, 32, 2838, 18], [1432, 18, 2838, 18], [1432, 25, 2838, 24], [1432, 26, 2838, 25], [1432, 41, 2839, 18], [1432, 45, 2839, 18, "_jsxDevRuntime"], [1432, 59, 2839, 18], [1432, 60, 2839, 18, "jsxDEV"], [1432, 66, 2839, 18], [1432, 68, 2839, 19, "_Text"], [1432, 73, 2839, 19], [1432, 74, 2839, 19, "default"], [1432, 81, 2839, 23], [1433, 20, 2839, 24, "style"], [1433, 25, 2839, 29], [1433, 27, 2839, 31], [1434, 22, 2839, 33, "fontSize"], [1434, 30, 2839, 41], [1434, 32, 2839, 43], [1434, 34, 2839, 45], [1435, 22, 2839, 47, "color"], [1435, 27, 2839, 52], [1435, 29, 2839, 54], [1436, 20, 2839, 64], [1436, 21, 2839, 66], [1437, 20, 2839, 66, "children"], [1437, 28, 2839, 66], [1437, 30, 2840, 21], [1437, 33, 2840, 24, "response"], [1437, 41, 2840, 32], [1437, 42, 2840, 33, "length"], [1437, 48, 2840, 39], [1438, 18, 2840, 45], [1439, 20, 2840, 45, "fileName"], [1439, 28, 2840, 45], [1439, 30, 2840, 45, "_jsxFileName"], [1439, 42, 2840, 45], [1440, 20, 2840, 45, "lineNumber"], [1440, 30, 2840, 45], [1441, 20, 2840, 45, "columnNumber"], [1441, 32, 2840, 45], [1442, 18, 2840, 45], [1442, 25, 2841, 24], [1442, 26, 2841, 25], [1443, 16, 2841, 25], [1444, 18, 2841, 25, "fileName"], [1444, 26, 2841, 25], [1444, 28, 2841, 25, "_jsxFileName"], [1444, 40, 2841, 25], [1445, 18, 2841, 25, "lineNumber"], [1445, 28, 2841, 25], [1446, 18, 2841, 25, "columnNumber"], [1446, 30, 2841, 25], [1447, 16, 2841, 25], [1447, 23, 2842, 22], [1447, 24, 2842, 23], [1448, 14, 2842, 23], [1449, 16, 2842, 23, "fileName"], [1449, 24, 2842, 23], [1449, 26, 2842, 23, "_jsxFileName"], [1449, 38, 2842, 23], [1450, 16, 2842, 23, "lineNumber"], [1450, 26, 2842, 23], [1451, 16, 2842, 23, "columnNumber"], [1451, 28, 2842, 23], [1452, 14, 2842, 23], [1452, 21, 2843, 20], [1452, 22, 2843, 21], [1453, 12, 2843, 21], [1454, 14, 2843, 21, "fileName"], [1454, 22, 2843, 21], [1454, 24, 2843, 21, "_jsxFileName"], [1454, 36, 2843, 21], [1455, 14, 2843, 21, "lineNumber"], [1455, 24, 2843, 21], [1456, 14, 2843, 21, "columnNumber"], [1456, 26, 2843, 21], [1457, 12, 2843, 21], [1457, 19, 2844, 18], [1457, 20, 2844, 19], [1457, 35, 2849, 12], [1457, 39, 2849, 12, "_jsxDevRuntime"], [1457, 53, 2849, 12], [1457, 54, 2849, 12, "jsxDEV"], [1457, 60, 2849, 12], [1457, 62, 2849, 13, "_View"], [1457, 67, 2849, 13], [1457, 68, 2849, 13, "default"], [1457, 75, 2849, 17], [1458, 14, 2853, 14, "style"], [1458, 19, 2853, 19], [1458, 21, 2853, 21], [1459, 16, 2857, 16, "backgroundColor"], [1459, 31, 2857, 31], [1459, 33, 2857, 33], [1459, 42, 2857, 42], [1460, 16, 2861, 16, "borderRadius"], [1460, 28, 2861, 28], [1460, 30, 2861, 30], [1460, 32, 2861, 32], [1461, 16, 2865, 16, "padding"], [1461, 23, 2865, 23], [1461, 25, 2865, 25], [1461, 27, 2865, 27], [1462, 16, 2869, 16, "marginBottom"], [1462, 28, 2869, 28], [1462, 30, 2869, 30], [1463, 14, 2873, 14], [1463, 15, 2873, 16], [1464, 14, 2873, 16, "children"], [1464, 22, 2873, 16], [1464, 38, 2881, 14], [1464, 42, 2881, 14, "_jsxDevRuntime"], [1464, 56, 2881, 14], [1464, 57, 2881, 14, "jsxDEV"], [1464, 63, 2881, 14], [1464, 65, 2881, 15, "_Text"], [1464, 70, 2881, 15], [1464, 71, 2881, 15, "default"], [1464, 78, 2881, 19], [1465, 16, 2885, 16, "style"], [1465, 21, 2885, 21], [1465, 23, 2885, 23], [1466, 18, 2889, 18, "fontSize"], [1466, 26, 2889, 26], [1466, 28, 2889, 28], [1466, 30, 2889, 30], [1467, 18, 2893, 18, "fontWeight"], [1467, 28, 2893, 28], [1467, 30, 2893, 30], [1467, 35, 2893, 35], [1468, 18, 2897, 18, "color"], [1468, 23, 2897, 23], [1468, 25, 2897, 25], [1468, 34, 2897, 34], [1469, 18, 2901, 18, "marginBottom"], [1469, 30, 2901, 30], [1469, 32, 2901, 32], [1470, 16, 2905, 16], [1470, 17, 2905, 18], [1471, 16, 2905, 18, "children"], [1471, 24, 2905, 18], [1471, 26, 2909, 15], [1472, 14, 2917, 14], [1473, 16, 2917, 14, "fileName"], [1473, 24, 2917, 14], [1473, 26, 2917, 14, "_jsxFileName"], [1473, 38, 2917, 14], [1474, 16, 2917, 14, "lineNumber"], [1474, 26, 2917, 14], [1475, 16, 2917, 14, "columnNumber"], [1475, 28, 2917, 14], [1476, 14, 2917, 14], [1476, 21, 2917, 20], [1476, 22, 2917, 21], [1476, 37, 2921, 14], [1476, 41, 2921, 14, "_jsxDevRuntime"], [1476, 55, 2921, 14], [1476, 56, 2921, 14, "jsxDEV"], [1476, 62, 2921, 14], [1476, 64, 2921, 15, "_Text"], [1476, 69, 2921, 15], [1476, 70, 2921, 15, "default"], [1476, 77, 2921, 19], [1477, 16, 2925, 16, "style"], [1477, 21, 2925, 21], [1477, 23, 2925, 23], [1478, 18, 2929, 18, "fontSize"], [1478, 26, 2929, 26], [1478, 28, 2929, 28], [1478, 30, 2929, 30], [1479, 18, 2933, 18, "color"], [1479, 23, 2933, 23], [1479, 25, 2933, 25], [1479, 34, 2933, 34], [1480, 18, 2937, 18, "lineHeight"], [1480, 28, 2937, 28], [1480, 30, 2937, 30], [1481, 16, 2941, 16], [1481, 17, 2941, 18], [1482, 16, 2941, 18, "children"], [1482, 24, 2941, 18], [1482, 26, 2949, 17], [1483, 14, 2949, 186], [1484, 16, 2949, 186, "fileName"], [1484, 24, 2949, 186], [1484, 26, 2949, 186, "_jsxFileName"], [1484, 38, 2949, 186], [1485, 16, 2949, 186, "lineNumber"], [1485, 26, 2949, 186], [1486, 16, 2949, 186, "columnNumber"], [1486, 28, 2949, 186], [1487, 14, 2949, 186], [1487, 21, 2953, 20], [1487, 22, 2953, 21], [1488, 12, 2953, 21], [1489, 14, 2953, 21, "fileName"], [1489, 22, 2953, 21], [1489, 24, 2953, 21, "_jsxFileName"], [1489, 36, 2953, 21], [1490, 14, 2953, 21, "lineNumber"], [1490, 24, 2953, 21], [1491, 14, 2953, 21, "columnNumber"], [1491, 26, 2953, 21], [1492, 12, 2953, 21], [1492, 19, 2957, 18], [1492, 20, 2957, 19], [1493, 10, 2957, 19], [1494, 12, 2957, 19, "fileName"], [1494, 20, 2957, 19], [1494, 22, 2957, 19, "_jsxFileName"], [1494, 34, 2957, 19], [1495, 12, 2957, 19, "lineNumber"], [1495, 22, 2957, 19], [1496, 12, 2957, 19, "columnNumber"], [1496, 24, 2957, 19], [1497, 10, 2957, 19], [1497, 17, 2961, 16], [1498, 8, 2961, 17], [1499, 10, 2961, 17, "fileName"], [1499, 18, 2961, 17], [1499, 20, 2961, 17, "_jsxFileName"], [1499, 32, 2961, 17], [1500, 10, 2961, 17, "lineNumber"], [1500, 20, 2961, 17], [1501, 10, 2961, 17, "columnNumber"], [1501, 22, 2961, 17], [1502, 8, 2961, 17], [1502, 15, 2965, 20], [1502, 16, 2965, 21], [1502, 31, 2973, 8], [1502, 35, 2973, 8, "_jsxDevRuntime"], [1502, 49, 2973, 8], [1502, 50, 2973, 8, "jsxDEV"], [1502, 56, 2973, 8], [1502, 58, 2973, 9, "_View"], [1502, 63, 2973, 9], [1502, 64, 2973, 9, "default"], [1502, 71, 2973, 13], [1503, 10, 2977, 10, "style"], [1503, 15, 2977, 15], [1503, 17, 2977, 17], [1504, 12, 2981, 12, "position"], [1504, 20, 2981, 20], [1504, 22, 2981, 22], [1504, 32, 2981, 32], [1505, 12, 2985, 12, "bottom"], [1505, 18, 2985, 18], [1505, 20, 2985, 20], [1505, 21, 2985, 21], [1506, 12, 2989, 12, "left"], [1506, 16, 2989, 16], [1506, 18, 2989, 18], [1506, 19, 2989, 19], [1507, 12, 2993, 12, "right"], [1507, 17, 2993, 17], [1507, 19, 2993, 19], [1507, 20, 2993, 20], [1508, 12, 2997, 12, "backgroundColor"], [1508, 27, 2997, 27], [1508, 29, 2997, 29], [1508, 35, 2997, 35], [1509, 12, 3001, 12, "borderTopWidth"], [1509, 26, 3001, 26], [1509, 28, 3001, 28], [1509, 29, 3001, 29], [1510, 12, 3005, 12, "borderTopColor"], [1510, 26, 3005, 26], [1510, 28, 3005, 28], [1510, 37, 3005, 37], [1511, 12, 3009, 12, "padding"], [1511, 19, 3009, 19], [1511, 21, 3009, 21], [1511, 23, 3009, 23], [1512, 12, 3013, 12, "paddingBottom"], [1512, 25, 3013, 25], [1512, 27, 3013, 27, "insets"], [1512, 33, 3013, 33], [1512, 34, 3013, 34, "bottom"], [1512, 40, 3013, 40], [1512, 43, 3013, 43], [1513, 10, 3017, 10], [1513, 11, 3017, 12], [1514, 10, 3017, 12, "children"], [1514, 18, 3017, 12], [1514, 33, 3025, 10], [1514, 37, 3025, 10, "_jsxDevRuntime"], [1514, 51, 3025, 10], [1514, 52, 3025, 10, "jsxDEV"], [1514, 58, 3025, 10], [1514, 60, 3025, 11, "_TouchableOpacity"], [1514, 77, 3025, 11], [1514, 78, 3025, 11, "default"], [1514, 85, 3025, 27], [1515, 12, 3029, 12, "onPress"], [1515, 19, 3029, 19], [1515, 21, 3029, 21, "submitResponse"], [1515, 35, 3029, 36], [1516, 12, 3033, 12, "disabled"], [1516, 20, 3033, 20], [1516, 22, 3033, 22, "submitting"], [1516, 32, 3033, 32], [1516, 36, 3033, 36], [1516, 37, 3033, 37, "cameraResult"], [1516, 49, 3033, 49], [1516, 53, 3033, 53], [1516, 54, 3033, 54, "response"], [1516, 62, 3033, 62], [1516, 63, 3033, 63, "trim"], [1516, 67, 3033, 67], [1516, 68, 3033, 68], [1516, 69, 3033, 70], [1517, 12, 3037, 12, "style"], [1517, 17, 3037, 17], [1517, 19, 3037, 19], [1518, 14, 3041, 14, "backgroundColor"], [1518, 29, 3041, 29], [1518, 31, 3045, 16, "submitting"], [1518, 41, 3045, 26], [1518, 45, 3045, 30], [1518, 46, 3045, 31, "cameraResult"], [1518, 58, 3045, 43], [1518, 62, 3045, 47], [1518, 63, 3045, 48, "response"], [1518, 71, 3045, 56], [1518, 72, 3045, 57, "trim"], [1518, 76, 3045, 61], [1518, 77, 3045, 62], [1518, 78, 3045, 63], [1518, 81, 3049, 20], [1518, 90, 3049, 29], [1518, 93, 3053, 20], [1518, 102, 3053, 29], [1519, 14, 3057, 14, "borderRadius"], [1519, 26, 3057, 26], [1519, 28, 3057, 28], [1519, 30, 3057, 30], [1520, 14, 3061, 14, "padding"], [1520, 21, 3061, 21], [1520, 23, 3061, 23], [1520, 25, 3061, 25], [1521, 14, 3065, 14, "flexDirection"], [1521, 27, 3065, 27], [1521, 29, 3065, 29], [1521, 34, 3065, 34], [1522, 14, 3069, 14, "alignItems"], [1522, 24, 3069, 24], [1522, 26, 3069, 26], [1522, 34, 3069, 34], [1523, 14, 3073, 14, "justifyContent"], [1523, 28, 3073, 28], [1523, 30, 3073, 30], [1524, 12, 3077, 12], [1524, 13, 3077, 14], [1525, 12, 3077, 14, "children"], [1525, 20, 3077, 14], [1525, 22, 3085, 13, "submitting"], [1525, 32, 3085, 23], [1525, 48, 3089, 14], [1525, 52, 3089, 14, "_jsxDevRuntime"], [1525, 66, 3089, 14], [1525, 67, 3089, 14, "jsxDEV"], [1525, 73, 3089, 14], [1525, 75, 3089, 14, "_jsxDevRuntime"], [1525, 89, 3089, 14], [1525, 90, 3089, 14, "Fragment"], [1525, 98, 3089, 14], [1526, 14, 3089, 14, "children"], [1526, 22, 3089, 14], [1526, 38, 3093, 16], [1526, 42, 3093, 16, "_jsxDevRuntime"], [1526, 56, 3093, 16], [1526, 57, 3093, 16, "jsxDEV"], [1526, 63, 3093, 16], [1526, 65, 3093, 17, "_View"], [1526, 70, 3093, 17], [1526, 71, 3093, 17, "default"], [1526, 78, 3093, 21], [1527, 16, 3097, 18, "style"], [1527, 21, 3097, 23], [1527, 23, 3097, 25], [1528, 18, 3101, 20, "width"], [1528, 23, 3101, 25], [1528, 25, 3101, 27], [1528, 27, 3101, 29], [1529, 18, 3105, 20, "height"], [1529, 24, 3105, 26], [1529, 26, 3105, 28], [1529, 28, 3105, 30], [1530, 18, 3109, 20, "borderRadius"], [1530, 30, 3109, 32], [1530, 32, 3109, 34], [1530, 33, 3109, 35], [1531, 18, 3113, 20, "borderWidth"], [1531, 29, 3113, 31], [1531, 31, 3113, 33], [1531, 32, 3113, 34], [1532, 18, 3117, 20, "borderColor"], [1532, 29, 3117, 31], [1532, 31, 3117, 33], [1532, 37, 3117, 39], [1533, 18, 3121, 20, "borderTopColor"], [1533, 32, 3121, 34], [1533, 34, 3121, 36], [1533, 47, 3121, 49], [1534, 18, 3125, 20, "marginRight"], [1534, 29, 3125, 31], [1534, 31, 3125, 33], [1535, 16, 3129, 18], [1536, 14, 3129, 20], [1537, 16, 3129, 20, "fileName"], [1537, 24, 3129, 20], [1537, 26, 3129, 20, "_jsxFileName"], [1537, 38, 3129, 20], [1538, 16, 3129, 20, "lineNumber"], [1538, 26, 3129, 20], [1539, 16, 3129, 20, "columnNumber"], [1539, 28, 3129, 20], [1540, 14, 3129, 20], [1540, 21, 3133, 17], [1540, 22, 3133, 18], [1540, 37, 3137, 16], [1540, 41, 3137, 16, "_jsxDevRuntime"], [1540, 55, 3137, 16], [1540, 56, 3137, 16, "jsxDEV"], [1540, 62, 3137, 16], [1540, 64, 3137, 17, "_Text"], [1540, 69, 3137, 17], [1540, 70, 3137, 17, "default"], [1540, 77, 3137, 21], [1541, 16, 3141, 18, "style"], [1541, 21, 3141, 23], [1541, 23, 3141, 25], [1542, 18, 3141, 27, "fontSize"], [1542, 26, 3141, 35], [1542, 28, 3141, 37], [1542, 30, 3141, 39], [1543, 18, 3141, 41, "fontWeight"], [1543, 28, 3141, 51], [1543, 30, 3141, 53], [1543, 35, 3141, 58], [1544, 18, 3141, 60, "color"], [1544, 23, 3141, 65], [1544, 25, 3141, 67], [1545, 16, 3141, 74], [1545, 17, 3141, 76], [1546, 16, 3141, 76, "children"], [1546, 24, 3141, 76], [1546, 26, 3145, 17], [1547, 14, 3153, 16], [1548, 16, 3153, 16, "fileName"], [1548, 24, 3153, 16], [1548, 26, 3153, 16, "_jsxFileName"], [1548, 38, 3153, 16], [1549, 16, 3153, 16, "lineNumber"], [1549, 26, 3153, 16], [1550, 16, 3153, 16, "columnNumber"], [1550, 28, 3153, 16], [1551, 14, 3153, 16], [1551, 21, 3153, 22], [1551, 22, 3153, 23], [1552, 12, 3153, 23], [1552, 27, 3157, 16], [1552, 28, 3157, 17], [1552, 44, 3165, 14], [1552, 48, 3165, 14, "_jsxDevRuntime"], [1552, 62, 3165, 14], [1552, 63, 3165, 14, "jsxDEV"], [1552, 69, 3165, 14], [1552, 71, 3165, 14, "_jsxDevRuntime"], [1552, 85, 3165, 14], [1552, 86, 3165, 14, "Fragment"], [1552, 94, 3165, 14], [1553, 14, 3165, 14, "children"], [1553, 22, 3165, 14], [1553, 38, 3169, 16], [1553, 42, 3169, 16, "_jsxDevRuntime"], [1553, 56, 3169, 16], [1553, 57, 3169, 16, "jsxDEV"], [1553, 63, 3169, 16], [1553, 65, 3169, 17, "_lucideReactNative"], [1553, 83, 3169, 17], [1553, 84, 3169, 17, "Send"], [1553, 88, 3169, 21], [1554, 16, 3169, 22, "size"], [1554, 20, 3169, 26], [1554, 22, 3169, 28], [1554, 24, 3169, 31], [1555, 16, 3169, 32, "color"], [1555, 21, 3169, 37], [1555, 23, 3169, 38], [1556, 14, 3169, 44], [1557, 16, 3169, 44, "fileName"], [1557, 24, 3169, 44], [1557, 26, 3169, 44, "_jsxFileName"], [1557, 38, 3169, 44], [1558, 16, 3169, 44, "lineNumber"], [1558, 26, 3169, 44], [1559, 16, 3169, 44, "columnNumber"], [1559, 28, 3169, 44], [1560, 14, 3169, 44], [1560, 21, 3169, 46], [1560, 22, 3169, 47], [1560, 37, 3173, 16], [1560, 41, 3173, 16, "_jsxDevRuntime"], [1560, 55, 3173, 16], [1560, 56, 3173, 16, "jsxDEV"], [1560, 62, 3173, 16], [1560, 64, 3173, 17, "_Text"], [1560, 69, 3173, 17], [1560, 70, 3173, 17, "default"], [1560, 77, 3173, 21], [1561, 16, 3177, 18, "style"], [1561, 21, 3177, 23], [1561, 23, 3177, 25], [1562, 18, 3181, 20, "fontSize"], [1562, 26, 3181, 28], [1562, 28, 3181, 30], [1562, 30, 3181, 32], [1563, 18, 3185, 20, "fontWeight"], [1563, 28, 3185, 30], [1563, 30, 3185, 32], [1563, 35, 3185, 37], [1564, 18, 3189, 20, "color"], [1564, 23, 3189, 25], [1564, 25, 3189, 27], [1564, 31, 3189, 33], [1565, 18, 3193, 20, "marginLeft"], [1565, 28, 3193, 30], [1565, 30, 3193, 32], [1566, 16, 3197, 18], [1566, 17, 3197, 20], [1567, 16, 3197, 20, "children"], [1567, 24, 3197, 20], [1567, 26, 3205, 19], [1567, 47, 3205, 40, "question"], [1567, 55, 3205, 48], [1567, 56, 3205, 49, "reward"], [1567, 62, 3205, 55], [1567, 63, 3205, 56, "toFixed"], [1567, 70, 3205, 63], [1567, 71, 3205, 64], [1567, 72, 3205, 65], [1567, 73, 3205, 66], [1568, 14, 3205, 69], [1569, 16, 3205, 69, "fileName"], [1569, 24, 3205, 69], [1569, 26, 3205, 69, "_jsxFileName"], [1569, 38, 3205, 69], [1570, 16, 3205, 69, "lineNumber"], [1570, 26, 3205, 69], [1571, 16, 3205, 69, "columnNumber"], [1571, 28, 3205, 69], [1572, 14, 3205, 69], [1572, 21, 3209, 22], [1572, 22, 3209, 23], [1573, 12, 3209, 23], [1573, 27, 3213, 16], [1574, 10, 3217, 13], [1575, 12, 3217, 13, "fileName"], [1575, 20, 3217, 13], [1575, 22, 3217, 13, "_jsxFileName"], [1575, 34, 3217, 13], [1576, 12, 3217, 13, "lineNumber"], [1576, 22, 3217, 13], [1577, 12, 3217, 13, "columnNumber"], [1577, 24, 3217, 13], [1578, 10, 3217, 13], [1578, 17, 3221, 28], [1579, 8, 3221, 29], [1580, 10, 3221, 29, "fileName"], [1580, 18, 3221, 29], [1580, 20, 3221, 29, "_jsxFileName"], [1580, 32, 3221, 29], [1581, 10, 3221, 29, "lineNumber"], [1581, 20, 3221, 29], [1582, 10, 3221, 29, "columnNumber"], [1582, 22, 3221, 29], [1583, 8, 3221, 29], [1583, 15, 3225, 14], [1583, 16, 3225, 15], [1584, 6, 3225, 15], [1585, 8, 3225, 15, "fileName"], [1585, 16, 3225, 15], [1585, 18, 3225, 15, "_jsxFileName"], [1585, 30, 3225, 15], [1586, 8, 3225, 15, "lineNumber"], [1586, 18, 3225, 15], [1587, 8, 3225, 15, "columnNumber"], [1587, 20, 3225, 15], [1588, 6, 3225, 15], [1588, 13, 3229, 36], [1588, 14, 3229, 37], [1588, 29, 3237, 6], [1588, 33, 3237, 6, "_jsxDevRuntime"], [1588, 47, 3237, 6], [1588, 48, 3237, 6, "jsxDEV"], [1588, 54, 3237, 6], [1588, 56, 3237, 7, "_Modal"], [1588, 62, 3237, 7], [1588, 63, 3237, 7, "default"], [1588, 70, 3237, 12], [1589, 8, 3241, 8, "visible"], [1589, 15, 3241, 15], [1589, 17, 3241, 17, "showCamera"], [1589, 27, 3241, 28], [1590, 8, 3245, 8, "animationType"], [1590, 21, 3245, 21], [1590, 23, 3245, 22], [1590, 30, 3245, 29], [1591, 8, 3249, 8, "presentationStyle"], [1591, 25, 3249, 25], [1591, 27, 3249, 26], [1591, 39, 3249, 38], [1592, 8, 3249, 38, "children"], [1592, 16, 3249, 38], [1592, 18, 3257, 9, "Platform"], [1592, 35, 3257, 17], [1592, 36, 3257, 18, "OS"], [1592, 38, 3257, 20], [1592, 43, 3257, 25], [1592, 48, 3257, 30], [1592, 64, 3261, 10], [1592, 68, 3261, 10, "_jsxDevRuntime"], [1592, 82, 3261, 10], [1592, 83, 3261, 10, "jsxDEV"], [1592, 89, 3261, 10], [1592, 91, 3261, 11, "_EchoCameraWeb"], [1592, 105, 3261, 11], [1592, 106, 3261, 11, "default"], [1592, 113, 3261, 24], [1593, 10, 3265, 12, "userId"], [1593, 16, 3265, 18], [1593, 18, 3265, 19], [1593, 32, 3265, 33], [1594, 10, 3269, 12, "requestId"], [1594, 19, 3269, 21], [1594, 21, 3269, 23, "id"], [1594, 23, 3269, 26], [1595, 10, 3273, 12, "onComplete"], [1595, 20, 3273, 22], [1595, 22, 3273, 24, "handleCameraComplete"], [1595, 42, 3273, 45], [1596, 10, 3277, 12, "onCancel"], [1596, 18, 3277, 20], [1596, 20, 3277, 22, "handleCameraCancel"], [1597, 8, 3277, 41], [1598, 10, 3277, 41, "fileName"], [1598, 18, 3277, 41], [1598, 20, 3277, 41, "_jsxFileName"], [1598, 32, 3277, 41], [1599, 10, 3277, 41, "lineNumber"], [1599, 20, 3277, 41], [1600, 10, 3277, 41, "columnNumber"], [1600, 22, 3277, 41], [1601, 8, 3277, 41], [1601, 15, 3281, 11], [1601, 16, 3281, 12], [1601, 32, 3289, 10], [1601, 36, 3289, 10, "_jsxDevRuntime"], [1601, 50, 3289, 10], [1601, 51, 3289, 10, "jsxDEV"], [1601, 57, 3289, 10], [1601, 59, 3289, 11, "_EchoCameraUnified"], [1601, 77, 3289, 11], [1601, 78, 3289, 11, "default"], [1601, 85, 3289, 28], [1602, 10, 3293, 12, "userId"], [1602, 16, 3293, 18], [1602, 18, 3293, 19], [1602, 32, 3293, 33], [1603, 10, 3297, 12, "requestId"], [1603, 19, 3297, 21], [1603, 21, 3297, 23, "id"], [1603, 23, 3297, 26], [1604, 10, 3301, 12, "onComplete"], [1604, 20, 3301, 22], [1604, 22, 3301, 24, "handleCameraComplete"], [1604, 42, 3301, 45], [1605, 10, 3305, 12, "onCancel"], [1605, 18, 3305, 20], [1605, 20, 3305, 22, "handleCameraCancel"], [1606, 8, 3305, 41], [1607, 10, 3305, 41, "fileName"], [1607, 18, 3305, 41], [1607, 20, 3305, 41, "_jsxFileName"], [1607, 32, 3305, 41], [1608, 10, 3305, 41, "lineNumber"], [1608, 20, 3305, 41], [1609, 10, 3305, 41, "columnNumber"], [1609, 22, 3305, 41], [1610, 8, 3305, 41], [1610, 15, 3309, 11], [1611, 6, 3313, 9], [1612, 8, 3313, 9, "fileName"], [1612, 16, 3313, 9], [1612, 18, 3313, 9, "_jsxFileName"], [1612, 30, 3313, 9], [1613, 8, 3313, 9, "lineNumber"], [1613, 18, 3313, 9], [1614, 8, 3313, 9, "columnNumber"], [1614, 20, 3313, 9], [1615, 6, 3313, 9], [1615, 13, 3317, 13], [1615, 14, 3317, 14], [1616, 4, 3317, 14], [1617, 6, 3317, 14, "fileName"], [1617, 14, 3317, 14], [1617, 16, 3317, 14, "_jsxFileName"], [1617, 28, 3317, 14], [1618, 6, 3317, 14, "lineNumber"], [1618, 16, 3317, 14], [1619, 6, 3317, 14, "columnNumber"], [1619, 18, 3317, 14], [1620, 4, 3317, 14], [1620, 11, 3321, 10], [1620, 12, 3321, 11], [1621, 2, 3329, 0], [1622, 2, 3329, 1, "_s"], [1622, 4, 3329, 1], [1622, 5, 133, 24, "RespondScreen"], [1622, 18, 133, 37], [1623, 4, 133, 37], [1623, 12, 137, 17, "useSafeAreaInsets"], [1623, 57, 137, 34], [1623, 59, 141, 17, "useLocalSearchParams"], [1623, 91, 141, 37], [1624, 2, 141, 37], [1625, 2, 141, 37, "_c"], [1625, 4, 141, 37], [1625, 7, 133, 24, "RespondScreen"], [1625, 20, 133, 37], [1626, 2, 133, 37], [1626, 6, 133, 37, "_c"], [1626, 8, 133, 37], [1627, 2, 133, 37, "$RefreshReg$"], [1627, 14, 133, 37], [1627, 15, 133, 37, "_c"], [1627, 17, 133, 37], [1628, 0, 133, 37], [1628, 3]], "functionMap": {"names": ["<global>", "RespondScreen", "useMemo$argument_0", "calculateDistance", "verifyLocation", "useEffect$argument_0", "handleStartCamera", "handleCameraComplete", "fetch.then$argument_0", "fetch.then.then$argument_0", "fetch.then.then._catch$argument_0", "handleCameraCancel", "submitResponse", "Promise$argument_0", "onPress", "LocationStatus", "getStatusConfig", "TouchableOpacity.props.onPress", "Image.props.onError", "Image.props.onLoad", "Image.props.onLoadStart", "Image.props.onLoadEnd"], "mappings": "AAA;eCoI;qCCgC;GD4B;4BEwG;GFgD;qCGQ;GHgS;YIQ;GJwC;4BKI;GLwE;2CMI;cCkF;SDG;cEC;SFE;eGC;SHE;GNyD;yCUI;GVgB;yBWI;wBC4H,sCD;qBEoC,mBF;GXwC;yBcQ;4BCI;KDoL;uBEoN;eFwB;GdgL;qBgBgG,mBhB;+BiBuiB;uBjBY;8BkBE;uBlBI;mCmBE;uBnBU;iCoBE;uBpBI;iCgB0L;yBhBQ;CDmsB"}}, "type": "js/module"}]}