{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 52, "index": 52}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 53}, "end": {"line": 2, "column": 31, "index": 84}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}, {"name": "../../Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 85}, "end": {"line": 3, "column": 42, "index": 127}}], "key": "AXHAxFjlDdeq1JxYZnWn+aHYhYU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useRawData = exports.useData = exports.useCollectionLoading = exports.loadData = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _Skia = require(_dependencyMap[1], \"../Skia\");\n  var _Platform = require(_dependencyMap[2], \"../../Platform\");\n  const factoryWrapper = (data2, factory, onError) => {\n    const factoryResult = factory(data2);\n    if (factoryResult === null) {\n      onError && onError(new Error(\"Could not load data\"));\n      return null;\n    } else {\n      return factoryResult;\n    }\n  };\n  const loadData = (source, factory, onError) => {\n    if (source === null || source === undefined) {\n      return new Promise(resolve => resolve(null));\n    } else if (source instanceof Uint8Array) {\n      return new Promise(resolve => resolve(factoryWrapper(_Skia.Skia.Data.fromBytes(source), factory, onError)));\n    } else {\n      const uri = typeof source === \"string\" ? source : _Platform.Platform.resolveAsset(source);\n      return _Skia.Skia.Data.fromURI(uri).then(d => factoryWrapper(d, factory, onError));\n    }\n  };\n  exports.loadData = loadData;\n  const useLoading = (source, loader) => {\n    const mounted = (0, _react.useRef)(false);\n    const [data, setData] = (0, _react.useState)(null);\n    const dataRef = (0, _react.useRef)(null);\n    (0, _react.useEffect)(() => {\n      mounted.current = true;\n      loader().then(value => {\n        if (mounted.current) {\n          setData(value);\n          dataRef.current = value;\n        }\n      });\n      return () => {\n        mounted.current = false;\n      };\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [source]);\n    return data;\n  };\n  const useCollectionLoading = (source, loader) => {\n    const mounted = (0, _react.useRef)(false);\n    const [data, setData] = (0, _react.useState)(null);\n    const dataRef = (0, _react.useRef)(null);\n    (0, _react.useEffect)(() => {\n      mounted.current = true;\n      loader().then(result => {\n        const value = result.filter(r => r !== null);\n        if (mounted.current) {\n          setData(value);\n          dataRef.current = value;\n        }\n      });\n      return () => {\n        mounted.current = false;\n      };\n\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [source]);\n    return data;\n  };\n  exports.useCollectionLoading = useCollectionLoading;\n  const useRawData = (source, factory, onError) => useLoading(source, () => loadData(source, factory, onError));\n  exports.useRawData = useRawData;\n  const identity = data => data;\n  const useData = (source, onError) => useRawData(source, identity, onError);\n  exports.useData = useData;\n});", "lineCount": 75, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Skia"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_Platform"], [8, 15, 3, 0], [8, 18, 3, 0, "require"], [8, 25, 3, 0], [8, 26, 3, 0, "_dependencyMap"], [8, 40, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "factoryWrapper"], [9, 22, 4, 20], [9, 25, 4, 23, "factoryWrapper"], [9, 26, 4, 24, "data2"], [9, 31, 4, 29], [9, 33, 4, 31, "factory"], [9, 40, 4, 38], [9, 42, 4, 40, "onError"], [9, 49, 4, 47], [9, 54, 4, 52], [10, 4, 5, 2], [10, 10, 5, 8, "factoryResult"], [10, 23, 5, 21], [10, 26, 5, 24, "factory"], [10, 33, 5, 31], [10, 34, 5, 32, "data2"], [10, 39, 5, 37], [10, 40, 5, 38], [11, 4, 6, 2], [11, 8, 6, 6, "factoryResult"], [11, 21, 6, 19], [11, 26, 6, 24], [11, 30, 6, 28], [11, 32, 6, 30], [12, 6, 7, 4, "onError"], [12, 13, 7, 11], [12, 17, 7, 15, "onError"], [12, 24, 7, 22], [12, 25, 7, 23], [12, 29, 7, 27, "Error"], [12, 34, 7, 32], [12, 35, 7, 33], [12, 56, 7, 54], [12, 57, 7, 55], [12, 58, 7, 56], [13, 6, 8, 4], [13, 13, 8, 11], [13, 17, 8, 15], [14, 4, 9, 2], [14, 5, 9, 3], [14, 11, 9, 9], [15, 6, 10, 4], [15, 13, 10, 11, "factoryResult"], [15, 26, 10, 24], [16, 4, 11, 2], [17, 2, 12, 0], [17, 3, 12, 1], [18, 2, 13, 7], [18, 8, 13, 13, "loadData"], [18, 16, 13, 21], [18, 19, 13, 24, "loadData"], [18, 20, 13, 25, "source"], [18, 26, 13, 31], [18, 28, 13, 33, "factory"], [18, 35, 13, 40], [18, 37, 13, 42, "onError"], [18, 44, 13, 49], [18, 49, 13, 54], [19, 4, 14, 2], [19, 8, 14, 6, "source"], [19, 14, 14, 12], [19, 19, 14, 17], [19, 23, 14, 21], [19, 27, 14, 25, "source"], [19, 33, 14, 31], [19, 38, 14, 36, "undefined"], [19, 47, 14, 45], [19, 49, 14, 47], [20, 6, 15, 4], [20, 13, 15, 11], [20, 17, 15, 15, "Promise"], [20, 24, 15, 22], [20, 25, 15, 23, "resolve"], [20, 32, 15, 30], [20, 36, 15, 34, "resolve"], [20, 43, 15, 41], [20, 44, 15, 42], [20, 48, 15, 46], [20, 49, 15, 47], [20, 50, 15, 48], [21, 4, 16, 2], [21, 5, 16, 3], [21, 11, 16, 9], [21, 15, 16, 13, "source"], [21, 21, 16, 19], [21, 33, 16, 31, "Uint8Array"], [21, 43, 16, 41], [21, 45, 16, 43], [22, 6, 17, 4], [22, 13, 17, 11], [22, 17, 17, 15, "Promise"], [22, 24, 17, 22], [22, 25, 17, 23, "resolve"], [22, 32, 17, 30], [22, 36, 17, 34, "resolve"], [22, 43, 17, 41], [22, 44, 17, 42, "factoryWrapper"], [22, 58, 17, 56], [22, 59, 17, 57, "Skia"], [22, 69, 17, 61], [22, 70, 17, 62, "Data"], [22, 74, 17, 66], [22, 75, 17, 67, "fromBytes"], [22, 84, 17, 76], [22, 85, 17, 77, "source"], [22, 91, 17, 83], [22, 92, 17, 84], [22, 94, 17, 86, "factory"], [22, 101, 17, 93], [22, 103, 17, 95, "onError"], [22, 110, 17, 102], [22, 111, 17, 103], [22, 112, 17, 104], [22, 113, 17, 105], [23, 4, 18, 2], [23, 5, 18, 3], [23, 11, 18, 9], [24, 6, 19, 4], [24, 12, 19, 10, "uri"], [24, 15, 19, 13], [24, 18, 19, 16], [24, 25, 19, 23, "source"], [24, 31, 19, 29], [24, 36, 19, 34], [24, 44, 19, 42], [24, 47, 19, 45, "source"], [24, 53, 19, 51], [24, 56, 19, 54, "Platform"], [24, 74, 19, 62], [24, 75, 19, 63, "resolveAsset"], [24, 87, 19, 75], [24, 88, 19, 76, "source"], [24, 94, 19, 82], [24, 95, 19, 83], [25, 6, 20, 4], [25, 13, 20, 11, "Skia"], [25, 23, 20, 15], [25, 24, 20, 16, "Data"], [25, 28, 20, 20], [25, 29, 20, 21, "fromURI"], [25, 36, 20, 28], [25, 37, 20, 29, "uri"], [25, 40, 20, 32], [25, 41, 20, 33], [25, 42, 20, 34, "then"], [25, 46, 20, 38], [25, 47, 20, 39, "d"], [25, 48, 20, 40], [25, 52, 20, 44, "factoryWrapper"], [25, 66, 20, 58], [25, 67, 20, 59, "d"], [25, 68, 20, 60], [25, 70, 20, 62, "factory"], [25, 77, 20, 69], [25, 79, 20, 71, "onError"], [25, 86, 20, 78], [25, 87, 20, 79], [25, 88, 20, 80], [26, 4, 21, 2], [27, 2, 22, 0], [27, 3, 22, 1], [28, 2, 22, 2, "exports"], [28, 9, 22, 2], [28, 10, 22, 2, "loadData"], [28, 18, 22, 2], [28, 21, 22, 2, "loadData"], [28, 29, 22, 2], [29, 2, 23, 0], [29, 8, 23, 6, "useLoading"], [29, 18, 23, 16], [29, 21, 23, 19, "useLoading"], [29, 22, 23, 20, "source"], [29, 28, 23, 26], [29, 30, 23, 28, "loader"], [29, 36, 23, 34], [29, 41, 23, 39], [30, 4, 24, 2], [30, 10, 24, 8, "mounted"], [30, 17, 24, 15], [30, 20, 24, 18], [30, 24, 24, 18, "useRef"], [30, 37, 24, 24], [30, 39, 24, 25], [30, 44, 24, 30], [30, 45, 24, 31], [31, 4, 25, 2], [31, 10, 25, 8], [31, 11, 25, 9, "data"], [31, 15, 25, 13], [31, 17, 25, 15, "setData"], [31, 24, 25, 22], [31, 25, 25, 23], [31, 28, 25, 26], [31, 32, 25, 26, "useState"], [31, 47, 25, 34], [31, 49, 25, 35], [31, 53, 25, 39], [31, 54, 25, 40], [32, 4, 26, 2], [32, 10, 26, 8, "dataRef"], [32, 17, 26, 15], [32, 20, 26, 18], [32, 24, 26, 18, "useRef"], [32, 37, 26, 24], [32, 39, 26, 25], [32, 43, 26, 29], [32, 44, 26, 30], [33, 4, 27, 2], [33, 8, 27, 2, "useEffect"], [33, 24, 27, 11], [33, 26, 27, 12], [33, 32, 27, 18], [34, 6, 28, 4, "mounted"], [34, 13, 28, 11], [34, 14, 28, 12, "current"], [34, 21, 28, 19], [34, 24, 28, 22], [34, 28, 28, 26], [35, 6, 29, 4, "loader"], [35, 12, 29, 10], [35, 13, 29, 11], [35, 14, 29, 12], [35, 15, 29, 13, "then"], [35, 19, 29, 17], [35, 20, 29, 18, "value"], [35, 25, 29, 23], [35, 29, 29, 27], [36, 8, 30, 6], [36, 12, 30, 10, "mounted"], [36, 19, 30, 17], [36, 20, 30, 18, "current"], [36, 27, 30, 25], [36, 29, 30, 27], [37, 10, 31, 8, "setData"], [37, 17, 31, 15], [37, 18, 31, 16, "value"], [37, 23, 31, 21], [37, 24, 31, 22], [38, 10, 32, 8, "dataRef"], [38, 17, 32, 15], [38, 18, 32, 16, "current"], [38, 25, 32, 23], [38, 28, 32, 26, "value"], [38, 33, 32, 31], [39, 8, 33, 6], [40, 6, 34, 4], [40, 7, 34, 5], [40, 8, 34, 6], [41, 6, 35, 4], [41, 13, 35, 11], [41, 19, 35, 17], [42, 8, 36, 6, "mounted"], [42, 15, 36, 13], [42, 16, 36, 14, "current"], [42, 23, 36, 21], [42, 26, 36, 24], [42, 31, 36, 29], [43, 6, 37, 4], [43, 7, 37, 5], [44, 6, 38, 4], [45, 4, 39, 2], [45, 5, 39, 3], [45, 7, 39, 5], [45, 8, 39, 6, "source"], [45, 14, 39, 12], [45, 15, 39, 13], [45, 16, 39, 14], [46, 4, 40, 2], [46, 11, 40, 9, "data"], [46, 15, 40, 13], [47, 2, 41, 0], [47, 3, 41, 1], [48, 2, 42, 7], [48, 8, 42, 13, "useCollectionLoading"], [48, 28, 42, 33], [48, 31, 42, 36, "useCollectionLoading"], [48, 32, 42, 37, "source"], [48, 38, 42, 43], [48, 40, 42, 45, "loader"], [48, 46, 42, 51], [48, 51, 42, 56], [49, 4, 43, 2], [49, 10, 43, 8, "mounted"], [49, 17, 43, 15], [49, 20, 43, 18], [49, 24, 43, 18, "useRef"], [49, 37, 43, 24], [49, 39, 43, 25], [49, 44, 43, 30], [49, 45, 43, 31], [50, 4, 44, 2], [50, 10, 44, 8], [50, 11, 44, 9, "data"], [50, 15, 44, 13], [50, 17, 44, 15, "setData"], [50, 24, 44, 22], [50, 25, 44, 23], [50, 28, 44, 26], [50, 32, 44, 26, "useState"], [50, 47, 44, 34], [50, 49, 44, 35], [50, 53, 44, 39], [50, 54, 44, 40], [51, 4, 45, 2], [51, 10, 45, 8, "dataRef"], [51, 17, 45, 15], [51, 20, 45, 18], [51, 24, 45, 18, "useRef"], [51, 37, 45, 24], [51, 39, 45, 25], [51, 43, 45, 29], [51, 44, 45, 30], [52, 4, 46, 2], [52, 8, 46, 2, "useEffect"], [52, 24, 46, 11], [52, 26, 46, 12], [52, 32, 46, 18], [53, 6, 47, 4, "mounted"], [53, 13, 47, 11], [53, 14, 47, 12, "current"], [53, 21, 47, 19], [53, 24, 47, 22], [53, 28, 47, 26], [54, 6, 48, 4, "loader"], [54, 12, 48, 10], [54, 13, 48, 11], [54, 14, 48, 12], [54, 15, 48, 13, "then"], [54, 19, 48, 17], [54, 20, 48, 18, "result"], [54, 26, 48, 24], [54, 30, 48, 28], [55, 8, 49, 6], [55, 14, 49, 12, "value"], [55, 19, 49, 17], [55, 22, 49, 20, "result"], [55, 28, 49, 26], [55, 29, 49, 27, "filter"], [55, 35, 49, 33], [55, 36, 49, 34, "r"], [55, 37, 49, 35], [55, 41, 49, 39, "r"], [55, 42, 49, 40], [55, 47, 49, 45], [55, 51, 49, 49], [55, 52, 49, 50], [56, 8, 50, 6], [56, 12, 50, 10, "mounted"], [56, 19, 50, 17], [56, 20, 50, 18, "current"], [56, 27, 50, 25], [56, 29, 50, 27], [57, 10, 51, 8, "setData"], [57, 17, 51, 15], [57, 18, 51, 16, "value"], [57, 23, 51, 21], [57, 24, 51, 22], [58, 10, 52, 8, "dataRef"], [58, 17, 52, 15], [58, 18, 52, 16, "current"], [58, 25, 52, 23], [58, 28, 52, 26, "value"], [58, 33, 52, 31], [59, 8, 53, 6], [60, 6, 54, 4], [60, 7, 54, 5], [60, 8, 54, 6], [61, 6, 55, 4], [61, 13, 55, 11], [61, 19, 55, 17], [62, 8, 56, 6, "mounted"], [62, 15, 56, 13], [62, 16, 56, 14, "current"], [62, 23, 56, 21], [62, 26, 56, 24], [62, 31, 56, 29], [63, 6, 57, 4], [63, 7, 57, 5], [65, 6, 59, 4], [66, 4, 60, 2], [66, 5, 60, 3], [66, 7, 60, 5], [66, 8, 60, 6, "source"], [66, 14, 60, 12], [66, 15, 60, 13], [66, 16, 60, 14], [67, 4, 61, 2], [67, 11, 61, 9, "data"], [67, 15, 61, 13], [68, 2, 62, 0], [68, 3, 62, 1], [69, 2, 62, 2, "exports"], [69, 9, 62, 2], [69, 10, 62, 2, "useCollectionLoading"], [69, 30, 62, 2], [69, 33, 62, 2, "useCollectionLoading"], [69, 53, 62, 2], [70, 2, 63, 7], [70, 8, 63, 13, "useRawData"], [70, 18, 63, 23], [70, 21, 63, 26, "useRawData"], [70, 22, 63, 27, "source"], [70, 28, 63, 33], [70, 30, 63, 35, "factory"], [70, 37, 63, 42], [70, 39, 63, 44, "onError"], [70, 46, 63, 51], [70, 51, 63, 56, "useLoading"], [70, 61, 63, 66], [70, 62, 63, 67, "source"], [70, 68, 63, 73], [70, 70, 63, 75], [70, 76, 63, 81, "loadData"], [70, 84, 63, 89], [70, 85, 63, 90, "source"], [70, 91, 63, 96], [70, 93, 63, 98, "factory"], [70, 100, 63, 105], [70, 102, 63, 107, "onError"], [70, 109, 63, 114], [70, 110, 63, 115], [70, 111, 63, 116], [71, 2, 63, 117, "exports"], [71, 9, 63, 117], [71, 10, 63, 117, "useRawData"], [71, 20, 63, 117], [71, 23, 63, 117, "useRawData"], [71, 33, 63, 117], [72, 2, 64, 0], [72, 8, 64, 6, "identity"], [72, 16, 64, 14], [72, 19, 64, 17, "data"], [72, 23, 64, 21], [72, 27, 64, 25, "data"], [72, 31, 64, 29], [73, 2, 65, 7], [73, 8, 65, 13, "useData"], [73, 15, 65, 20], [73, 18, 65, 23, "useData"], [73, 19, 65, 24, "source"], [73, 25, 65, 30], [73, 27, 65, 32, "onError"], [73, 34, 65, 39], [73, 39, 65, 44, "useRawData"], [73, 49, 65, 54], [73, 50, 65, 55, "source"], [73, 56, 65, 61], [73, 58, 65, 63, "identity"], [73, 66, 65, 71], [73, 68, 65, 73, "onError"], [73, 75, 65, 80], [73, 76, 65, 81], [74, 2, 65, 82, "exports"], [74, 9, 65, 82], [74, 10, 65, 82, "useData"], [74, 17, 65, 82], [74, 20, 65, 82, "useData"], [74, 27, 65, 82], [75, 0, 65, 82], [75, 3]], "functionMap": {"names": ["<global>", "factoryWrapper", "loadData", "Promise$argument_0", "Skia.Data.fromURI.then$argument_0", "useLoading", "useEffect$argument_0", "loader.then$argument_0", "<anonymous>", "useCollectionLoading", "result.filter$argument_0", "useRawData", "useLoading$argument_1", "identity", "useData"], "mappings": "AAA;uBCG;CDQ;wBEC;uBCE,wBD;uBCE,iFD;uCEG,wCF;CFE;mBKC;YCI;kBCE;KDK;WEC;KFE;GDE;CLE;oCSC;YHI;kBCE;kCGC,eH;KDK;WEC;KFE;GGG;CTE;0BWC,iDC,wCD,CX;iBaC,Yb;uBcC,0Dd"}}, "type": "js/module"}]}