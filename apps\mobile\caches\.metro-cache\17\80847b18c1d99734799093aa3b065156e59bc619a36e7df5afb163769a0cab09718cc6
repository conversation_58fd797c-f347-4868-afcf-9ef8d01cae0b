{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkSVG", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 38, "index": 69}}], "key": "s6y4GHrJ/O2IEJbSfPHtPhQNF5g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkSVGFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkSVG = require(_dependencyMap[1], \"./JsiSkSVG\");\n  class JsiSkSVGFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeFromData(data) {\n      const decoder = new TextDecoder(\"utf-8\");\n      const str = decoder.decode(data.ref);\n      return this.MakeFromString(str);\n    }\n    MakeFromString(str) {\n      const parser = new DOMParser();\n      const svgDoc = parser.parseFromString(str, \"image/svg+xml\");\n      const svgElement = svgDoc.documentElement;\n      const attrWidth = svgElement.getAttribute(\"width\");\n      const attrHeight = svgElement.getAttribute(\"height\");\n      let width = attrWidth ? parseFloat(attrWidth) : null;\n      let height = attrHeight ? parseFloat(attrHeight) : null;\n      const svgDataUrl = \"data:image/svg+xml;charset=utf-8,\" + encodeURIComponent(str);\n      // Create a new HTMLImageElement\n      const img = new Image();\n      img.src = svgDataUrl;\n\n      // Optionally set styles or attributes on the image\n      img.style.display = \"none\";\n      img.alt = \"SVG Image\";\n      if (!width || !height) {\n        const viewBox = svgElement.getAttribute(\"viewBox\");\n        if (viewBox) {\n          const viewBoxValues = viewBox.split(\" \");\n          if (viewBoxValues.length === 4) {\n            width = width || parseFloat(viewBoxValues[2]);\n            height = height || parseFloat(viewBoxValues[3]);\n          }\n        }\n      }\n      if (width && height) {\n        img.width = width;\n        img.height = height;\n      }\n      img.onerror = e => {\n        console.error(\"SVG failed to load\", e);\n      };\n      document.body.appendChild(img);\n      return new _JsiSkSVG.JsiSkSVG(this.CanvasKit, img);\n    }\n  }\n  exports.JsiSkSVGFactory = JsiSkSVGFactory;\n});", "lineCount": 55, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkSVG"], [7, 15, 2, 0], [7, 18, 2, 0, "require"], [7, 25, 2, 0], [7, 26, 2, 0, "_dependencyMap"], [7, 40, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkSVGFactory"], [8, 23, 3, 28], [8, 32, 3, 37, "Host"], [8, 42, 3, 41], [8, 43, 3, 42], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "MakeFromData"], [12, 16, 7, 14, "MakeFromData"], [12, 17, 7, 15, "data"], [12, 21, 7, 19], [12, 23, 7, 21], [13, 6, 8, 4], [13, 12, 8, 10, "decoder"], [13, 19, 8, 17], [13, 22, 8, 20], [13, 26, 8, 24, "TextDecoder"], [13, 37, 8, 35], [13, 38, 8, 36], [13, 45, 8, 43], [13, 46, 8, 44], [14, 6, 9, 4], [14, 12, 9, 10, "str"], [14, 15, 9, 13], [14, 18, 9, 16, "decoder"], [14, 25, 9, 23], [14, 26, 9, 24, "decode"], [14, 32, 9, 30], [14, 33, 9, 31, "data"], [14, 37, 9, 35], [14, 38, 9, 36, "ref"], [14, 41, 9, 39], [14, 42, 9, 40], [15, 6, 10, 4], [15, 13, 10, 11], [15, 17, 10, 15], [15, 18, 10, 16, "MakeFromString"], [15, 32, 10, 30], [15, 33, 10, 31, "str"], [15, 36, 10, 34], [15, 37, 10, 35], [16, 4, 11, 2], [17, 4, 12, 2, "MakeFromString"], [17, 18, 12, 16, "MakeFromString"], [17, 19, 12, 17, "str"], [17, 22, 12, 20], [17, 24, 12, 22], [18, 6, 13, 4], [18, 12, 13, 10, "parser"], [18, 18, 13, 16], [18, 21, 13, 19], [18, 25, 13, 23, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 34, 13, 32], [18, 35, 13, 33], [18, 36, 13, 34], [19, 6, 14, 4], [19, 12, 14, 10, "svgDoc"], [19, 18, 14, 16], [19, 21, 14, 19, "parser"], [19, 27, 14, 25], [19, 28, 14, 26, "parseFromString"], [19, 43, 14, 41], [19, 44, 14, 42, "str"], [19, 47, 14, 45], [19, 49, 14, 47], [19, 64, 14, 62], [19, 65, 14, 63], [20, 6, 15, 4], [20, 12, 15, 10, "svgElement"], [20, 22, 15, 20], [20, 25, 15, 23, "svgDoc"], [20, 31, 15, 29], [20, 32, 15, 30, "documentElement"], [20, 47, 15, 45], [21, 6, 16, 4], [21, 12, 16, 10, "attrWidth"], [21, 21, 16, 19], [21, 24, 16, 22, "svgElement"], [21, 34, 16, 32], [21, 35, 16, 33, "getAttribute"], [21, 47, 16, 45], [21, 48, 16, 46], [21, 55, 16, 53], [21, 56, 16, 54], [22, 6, 17, 4], [22, 12, 17, 10, "attrHeight"], [22, 22, 17, 20], [22, 25, 17, 23, "svgElement"], [22, 35, 17, 33], [22, 36, 17, 34, "getAttribute"], [22, 48, 17, 46], [22, 49, 17, 47], [22, 57, 17, 55], [22, 58, 17, 56], [23, 6, 18, 4], [23, 10, 18, 8, "width"], [23, 15, 18, 13], [23, 18, 18, 16, "attrWidth"], [23, 27, 18, 25], [23, 30, 18, 28, "parseFloat"], [23, 40, 18, 38], [23, 41, 18, 39, "attrWidth"], [23, 50, 18, 48], [23, 51, 18, 49], [23, 54, 18, 52], [23, 58, 18, 56], [24, 6, 19, 4], [24, 10, 19, 8, "height"], [24, 16, 19, 14], [24, 19, 19, 17, "attrHeight"], [24, 29, 19, 27], [24, 32, 19, 30, "parseFloat"], [24, 42, 19, 40], [24, 43, 19, 41, "attrHeight"], [24, 53, 19, 51], [24, 54, 19, 52], [24, 57, 19, 55], [24, 61, 19, 59], [25, 6, 20, 4], [25, 12, 20, 10, "svgDataUrl"], [25, 22, 20, 20], [25, 25, 20, 23], [25, 60, 20, 58], [25, 63, 20, 61, "encodeURIComponent"], [25, 81, 20, 79], [25, 82, 20, 80, "str"], [25, 85, 20, 83], [25, 86, 20, 84], [26, 6, 21, 4], [27, 6, 22, 4], [27, 12, 22, 10, "img"], [27, 15, 22, 13], [27, 18, 22, 16], [27, 22, 22, 20, "Image"], [27, 27, 22, 25], [27, 28, 22, 26], [27, 29, 22, 27], [28, 6, 23, 4, "img"], [28, 9, 23, 7], [28, 10, 23, 8, "src"], [28, 13, 23, 11], [28, 16, 23, 14, "svgDataUrl"], [28, 26, 23, 24], [30, 6, 25, 4], [31, 6, 26, 4, "img"], [31, 9, 26, 7], [31, 10, 26, 8, "style"], [31, 15, 26, 13], [31, 16, 26, 14, "display"], [31, 23, 26, 21], [31, 26, 26, 24], [31, 32, 26, 30], [32, 6, 27, 4, "img"], [32, 9, 27, 7], [32, 10, 27, 8, "alt"], [32, 13, 27, 11], [32, 16, 27, 14], [32, 27, 27, 25], [33, 6, 28, 4], [33, 10, 28, 8], [33, 11, 28, 9, "width"], [33, 16, 28, 14], [33, 20, 28, 18], [33, 21, 28, 19, "height"], [33, 27, 28, 25], [33, 29, 28, 27], [34, 8, 29, 6], [34, 14, 29, 12, "viewBox"], [34, 21, 29, 19], [34, 24, 29, 22, "svgElement"], [34, 34, 29, 32], [34, 35, 29, 33, "getAttribute"], [34, 47, 29, 45], [34, 48, 29, 46], [34, 57, 29, 55], [34, 58, 29, 56], [35, 8, 30, 6], [35, 12, 30, 10, "viewBox"], [35, 19, 30, 17], [35, 21, 30, 19], [36, 10, 31, 8], [36, 16, 31, 14, "viewBoxValues"], [36, 29, 31, 27], [36, 32, 31, 30, "viewBox"], [36, 39, 31, 37], [36, 40, 31, 38, "split"], [36, 45, 31, 43], [36, 46, 31, 44], [36, 49, 31, 47], [36, 50, 31, 48], [37, 10, 32, 8], [37, 14, 32, 12, "viewBoxValues"], [37, 27, 32, 25], [37, 28, 32, 26, "length"], [37, 34, 32, 32], [37, 39, 32, 37], [37, 40, 32, 38], [37, 42, 32, 40], [38, 12, 33, 10, "width"], [38, 17, 33, 15], [38, 20, 33, 18, "width"], [38, 25, 33, 23], [38, 29, 33, 27, "parseFloat"], [38, 39, 33, 37], [38, 40, 33, 38, "viewBoxValues"], [38, 53, 33, 51], [38, 54, 33, 52], [38, 55, 33, 53], [38, 56, 33, 54], [38, 57, 33, 55], [39, 12, 34, 10, "height"], [39, 18, 34, 16], [39, 21, 34, 19, "height"], [39, 27, 34, 25], [39, 31, 34, 29, "parseFloat"], [39, 41, 34, 39], [39, 42, 34, 40, "viewBoxValues"], [39, 55, 34, 53], [39, 56, 34, 54], [39, 57, 34, 55], [39, 58, 34, 56], [39, 59, 34, 57], [40, 10, 35, 8], [41, 8, 36, 6], [42, 6, 37, 4], [43, 6, 38, 4], [43, 10, 38, 8, "width"], [43, 15, 38, 13], [43, 19, 38, 17, "height"], [43, 25, 38, 23], [43, 27, 38, 25], [44, 8, 39, 6, "img"], [44, 11, 39, 9], [44, 12, 39, 10, "width"], [44, 17, 39, 15], [44, 20, 39, 18, "width"], [44, 25, 39, 23], [45, 8, 40, 6, "img"], [45, 11, 40, 9], [45, 12, 40, 10, "height"], [45, 18, 40, 16], [45, 21, 40, 19, "height"], [45, 27, 40, 25], [46, 6, 41, 4], [47, 6, 42, 4, "img"], [47, 9, 42, 7], [47, 10, 42, 8, "onerror"], [47, 17, 42, 15], [47, 20, 42, 18, "e"], [47, 21, 42, 19], [47, 25, 42, 23], [48, 8, 43, 6, "console"], [48, 15, 43, 13], [48, 16, 43, 14, "error"], [48, 21, 43, 19], [48, 22, 43, 20], [48, 42, 43, 40], [48, 44, 43, 42, "e"], [48, 45, 43, 43], [48, 46, 43, 44], [49, 6, 44, 4], [49, 7, 44, 5], [50, 6, 45, 4, "document"], [50, 14, 45, 12], [50, 15, 45, 13, "body"], [50, 19, 45, 17], [50, 20, 45, 18, "append<PERSON><PERSON><PERSON>"], [50, 31, 45, 29], [50, 32, 45, 30, "img"], [50, 35, 45, 33], [50, 36, 45, 34], [51, 6, 46, 4], [51, 13, 46, 11], [51, 17, 46, 15, "JsiSkSVG"], [51, 35, 46, 23], [51, 36, 46, 24], [51, 40, 46, 28], [51, 41, 46, 29, "CanvasKit"], [51, 50, 46, 38], [51, 52, 46, 40, "img"], [51, 55, 46, 43], [51, 56, 46, 44], [52, 4, 47, 2], [53, 2, 48, 0], [54, 2, 48, 1, "exports"], [54, 9, 48, 1], [54, 10, 48, 1, "JsiSkSVGFactory"], [54, 25, 48, 1], [54, 28, 48, 1, "JsiSkSVGFactory"], [54, 43, 48, 1], [55, 0, 48, 1], [55, 3]], "functionMap": {"names": ["<global>", "JsiSkSVGFactory", "constructor", "MakeFromData", "MakeFromString", "img.onerror"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFI;EGC;kBC8B;KDE;GHG;CDC"}}, "type": "js/module"}]}