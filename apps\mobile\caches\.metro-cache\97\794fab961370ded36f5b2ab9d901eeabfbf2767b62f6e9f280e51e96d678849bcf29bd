{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/BlazeFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 52, "index": 774}}], "key": "evUr5FsRzgXV+xqFHKyZkgVjslo=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 841}, "end": {"line": 27, "column": 42, "index": 883}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 946}, "end": {"line": 29, "column": 61, "index": 1007}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _BlazeFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/BlazeFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  // Removed TensorFlowFaceCanvas - using BlazeFaceCanvas instead\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection - matching working test implementation\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js - using same version as working test\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {\n          original: {\n            x,\n            y,\n            width,\n            height\n          },\n          canvas: {\n            width: canvasWidth,\n            height: canvasHeight\n          },\n          clamped: {\n            x: clampedX,\n            y: clampedY,\n            width: clampedWidth,\n            height: clampedHeight\n          }\n        });\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: About to call processImageWithFaceBlur with:', photo.uri);\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: Function exists?', typeof processImageWithFaceBlur);\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      console.log('[EchoCameraWeb] 🚀 ENTRY: Starting face blur processing system...');\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {\n              bbox,\n              imageSize: {\n                width: img.width,\n                height: img.height\n              }\n            });\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {\n              faceX,\n              faceY,\n              faceWidth,\n              faceHeight,\n              isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0\n            });\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] 🚨 CRITICAL ERROR in processImageWithFaceBlur:', error);\n        console.error('[EchoCameraWeb] 🚨 Error stack:', error.stack);\n        console.error('[EchoCameraWeb] 🚨 Error details:', {\n          name: error.name,\n          message: error.message,\n          photoUri\n        });\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 908,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a',\n            // Hide video when BlazeFace is active to show only the blurred canvas\n            opacity: isCameraReady && previewBlurEnabled ? 0 : 1\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 942,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_BlazeFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 13\n          }, this)\n        }, void 0, false), isCameraReady && !previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            pointerEvents: 'none'\n          }],\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n            intensity: 15,\n            tint: \"dark\",\n            style: [styles.blurZone, {\n              left: 0,\n              top: viewSize.height * 0.25,\n              width: viewSize.width,\n              height: viewSize.height * 0.5,\n              borderRadius: 20,\n              opacity: 0.8\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n            intensity: 12,\n            tint: \"dark\",\n            style: [styles.blurZone, {\n              left: viewSize.width * 0.5 - viewSize.width * 0.3,\n              top: viewSize.height * 0.4 - viewSize.width * 0.3,\n              width: viewSize.width * 0.6,\n              height: viewSize.width * 0.6,\n              borderRadius: viewSize.width * 0.6 / 2,\n              opacity: 0.1\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 13\n          }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.previewChip,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.previewChipText,\n              children: \"BlazeFace Disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1010,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 11\n        }, this), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1029,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1022,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1064,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1091,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1098,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1075,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1121,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1116,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1111,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 939,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1716, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_BlazeFaceCanvas"], [20, 22, 25, 0], [20, 25, 25, 0, "_interopRequireDefault"], [20, 47, 25, 0], [20, 48, 25, 0, "require"], [20, 55, 25, 0], [20, 56, 25, 0, "_dependencyMap"], [20, 70, 25, 0], [21, 2, 27, 0], [21, 6, 27, 0, "_useUpload"], [21, 16, 27, 0], [21, 19, 27, 0, "_interopRequireDefault"], [21, 41, 27, 0], [21, 42, 27, 0, "require"], [21, 49, 27, 0], [21, 50, 27, 0, "_dependencyMap"], [21, 64, 27, 0], [22, 2, 29, 0], [22, 6, 29, 0, "_cameraChallenge"], [22, 22, 29, 0], [22, 25, 29, 0, "require"], [22, 32, 29, 0], [22, 33, 29, 0, "_dependencyMap"], [22, 47, 29, 0], [23, 2, 29, 61], [23, 6, 29, 61, "_Platform"], [23, 15, 29, 61], [23, 18, 29, 61, "_interopRequireDefault"], [23, 40, 29, 61], [23, 41, 29, 61, "require"], [23, 48, 29, 61], [23, 49, 29, 61, "_dependencyMap"], [23, 63, 29, 61], [24, 2, 29, 61], [24, 6, 29, 61, "_jsxDevRuntime"], [24, 20, 29, 61], [24, 23, 29, 61, "require"], [24, 30, 29, 61], [24, 31, 29, 61, "_dependencyMap"], [24, 45, 29, 61], [25, 2, 29, 61], [25, 6, 29, 61, "_jsxFileName"], [25, 18, 29, 61], [26, 4, 29, 61, "_s"], [26, 6, 29, 61], [26, 9, 29, 61, "$RefreshSig$"], [26, 21, 29, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 26, 0], [38, 2, 26, 0], [38, 11, 26, 0, "_interopRequireWildcard"], [38, 35, 26, 0, "e"], [38, 36, 26, 0], [38, 38, 26, 0, "t"], [38, 39, 26, 0], [38, 68, 26, 0, "WeakMap"], [38, 75, 26, 0], [38, 81, 26, 0, "r"], [38, 82, 26, 0], [38, 89, 26, 0, "WeakMap"], [38, 96, 26, 0], [38, 100, 26, 0, "n"], [38, 101, 26, 0], [38, 108, 26, 0, "WeakMap"], [38, 115, 26, 0], [38, 127, 26, 0, "_interopRequireWildcard"], [38, 150, 26, 0], [38, 162, 26, 0, "_interopRequireWildcard"], [38, 163, 26, 0, "e"], [38, 164, 26, 0], [38, 166, 26, 0, "t"], [38, 167, 26, 0], [38, 176, 26, 0, "t"], [38, 177, 26, 0], [38, 181, 26, 0, "e"], [38, 182, 26, 0], [38, 186, 26, 0, "e"], [38, 187, 26, 0], [38, 188, 26, 0, "__esModule"], [38, 198, 26, 0], [38, 207, 26, 0, "e"], [38, 208, 26, 0], [38, 214, 26, 0, "o"], [38, 215, 26, 0], [38, 217, 26, 0, "i"], [38, 218, 26, 0], [38, 220, 26, 0, "f"], [38, 221, 26, 0], [38, 226, 26, 0, "__proto__"], [38, 235, 26, 0], [38, 243, 26, 0, "default"], [38, 250, 26, 0], [38, 252, 26, 0, "e"], [38, 253, 26, 0], [38, 270, 26, 0, "e"], [38, 271, 26, 0], [38, 294, 26, 0, "e"], [38, 295, 26, 0], [38, 320, 26, 0, "e"], [38, 321, 26, 0], [38, 330, 26, 0, "f"], [38, 331, 26, 0], [38, 337, 26, 0, "o"], [38, 338, 26, 0], [38, 341, 26, 0, "t"], [38, 342, 26, 0], [38, 345, 26, 0, "n"], [38, 346, 26, 0], [38, 349, 26, 0, "r"], [38, 350, 26, 0], [38, 358, 26, 0, "o"], [38, 359, 26, 0], [38, 360, 26, 0, "has"], [38, 363, 26, 0], [38, 364, 26, 0, "e"], [38, 365, 26, 0], [38, 375, 26, 0, "o"], [38, 376, 26, 0], [38, 377, 26, 0, "get"], [38, 380, 26, 0], [38, 381, 26, 0, "e"], [38, 382, 26, 0], [38, 385, 26, 0, "o"], [38, 386, 26, 0], [38, 387, 26, 0, "set"], [38, 390, 26, 0], [38, 391, 26, 0, "e"], [38, 392, 26, 0], [38, 394, 26, 0, "f"], [38, 395, 26, 0], [38, 411, 26, 0, "t"], [38, 412, 26, 0], [38, 416, 26, 0, "e"], [38, 417, 26, 0], [38, 433, 26, 0, "t"], [38, 434, 26, 0], [38, 441, 26, 0, "hasOwnProperty"], [38, 455, 26, 0], [38, 456, 26, 0, "call"], [38, 460, 26, 0], [38, 461, 26, 0, "e"], [38, 462, 26, 0], [38, 464, 26, 0, "t"], [38, 465, 26, 0], [38, 472, 26, 0, "i"], [38, 473, 26, 0], [38, 477, 26, 0, "o"], [38, 478, 26, 0], [38, 481, 26, 0, "Object"], [38, 487, 26, 0], [38, 488, 26, 0, "defineProperty"], [38, 502, 26, 0], [38, 507, 26, 0, "Object"], [38, 513, 26, 0], [38, 514, 26, 0, "getOwnPropertyDescriptor"], [38, 538, 26, 0], [38, 539, 26, 0, "e"], [38, 540, 26, 0], [38, 542, 26, 0, "t"], [38, 543, 26, 0], [38, 550, 26, 0, "i"], [38, 551, 26, 0], [38, 552, 26, 0, "get"], [38, 555, 26, 0], [38, 559, 26, 0, "i"], [38, 560, 26, 0], [38, 561, 26, 0, "set"], [38, 564, 26, 0], [38, 568, 26, 0, "o"], [38, 569, 26, 0], [38, 570, 26, 0, "f"], [38, 571, 26, 0], [38, 573, 26, 0, "t"], [38, 574, 26, 0], [38, 576, 26, 0, "i"], [38, 577, 26, 0], [38, 581, 26, 0, "f"], [38, 582, 26, 0], [38, 583, 26, 0, "t"], [38, 584, 26, 0], [38, 588, 26, 0, "e"], [38, 589, 26, 0], [38, 590, 26, 0, "t"], [38, 591, 26, 0], [38, 602, 26, 0, "f"], [38, 603, 26, 0], [38, 608, 26, 0, "e"], [38, 609, 26, 0], [38, 611, 26, 0, "t"], [38, 612, 26, 0], [39, 2, 31, 0], [39, 8, 31, 6], [40, 4, 31, 8, "width"], [40, 9, 31, 13], [40, 11, 31, 15, "screenWidth"], [40, 22, 31, 26], [41, 4, 31, 28, "height"], [41, 10, 31, 34], [41, 12, 31, 36, "screenHeight"], [42, 2, 31, 49], [42, 3, 31, 50], [42, 6, 31, 53, "Dimensions"], [42, 25, 31, 63], [42, 26, 31, 64, "get"], [42, 29, 31, 67], [42, 30, 31, 68], [42, 38, 31, 76], [42, 39, 31, 77], [43, 2, 32, 0], [43, 8, 32, 6, "API_BASE_URL"], [43, 20, 32, 18], [43, 23, 32, 21], [43, 24, 33, 2, "_env2"], [43, 29, 33, 2], [43, 30, 33, 2, "env"], [43, 33, 33, 2], [43, 34, 33, 2, "EXPO_PUBLIC_BASE_URL"], [43, 54, 33, 2], [43, 58, 33, 2, "_env2"], [43, 63, 33, 2], [43, 64, 33, 2, "env"], [43, 67, 33, 2], [43, 68, 33, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [43, 94, 34, 40], [43, 98, 34, 40, "_env2"], [43, 103, 34, 40], [43, 104, 34, 40, "env"], [43, 107, 34, 40], [43, 108, 34, 40, "EXPO_PUBLIC_HOST"], [43, 124, 35, 30], [43, 128, 36, 2], [43, 130, 36, 4], [43, 132, 37, 2, "replace"], [43, 139, 37, 9], [43, 140, 37, 10], [43, 145, 37, 15], [43, 147, 37, 17], [43, 149, 37, 19], [43, 150, 37, 20], [45, 2, 50, 0], [47, 2, 52, 15], [47, 11, 52, 24, "EchoCameraWeb"], [47, 24, 52, 37, "EchoCameraWeb"], [47, 25, 52, 38], [48, 4, 53, 2, "userId"], [48, 10, 53, 8], [49, 4, 54, 2, "requestId"], [49, 13, 54, 11], [50, 4, 55, 2, "onComplete"], [50, 14, 55, 12], [51, 4, 56, 2, "onCancel"], [52, 2, 57, 20], [52, 3, 57, 21], [52, 5, 57, 23], [53, 4, 57, 23, "_s"], [53, 6, 57, 23], [54, 4, 58, 2], [54, 10, 58, 8, "cameraRef"], [54, 19, 58, 17], [54, 22, 58, 20], [54, 26, 58, 20, "useRef"], [54, 39, 58, 26], [54, 41, 58, 39], [54, 45, 58, 43], [54, 46, 58, 44], [55, 4, 59, 2], [55, 10, 59, 8], [55, 11, 59, 9, "permission"], [55, 21, 59, 19], [55, 23, 59, 21, "requestPermission"], [55, 40, 59, 38], [55, 41, 59, 39], [55, 44, 59, 42], [55, 48, 59, 42, "useCameraPermissions"], [55, 80, 59, 62], [55, 82, 59, 63], [55, 83, 59, 64], [57, 4, 61, 2], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "processingState"], [58, 26, 62, 24], [58, 28, 62, 26, "setProcessingState"], [58, 46, 62, 44], [58, 47, 62, 45], [58, 50, 62, 48], [58, 54, 62, 48, "useState"], [58, 69, 62, 56], [58, 71, 62, 74], [58, 77, 62, 80], [58, 78, 62, 81], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "challengeCode"], [59, 24, 63, 22], [59, 26, 63, 24, "setChallengeCode"], [59, 42, 63, 40], [59, 43, 63, 41], [59, 46, 63, 44], [59, 50, 63, 44, "useState"], [59, 65, 63, 52], [59, 67, 63, 68], [59, 71, 63, 72], [59, 72, 63, 73], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "processingProgress"], [60, 29, 64, 27], [60, 31, 64, 29, "setProcessingProgress"], [60, 52, 64, 50], [60, 53, 64, 51], [60, 56, 64, 54], [60, 60, 64, 54, "useState"], [60, 75, 64, 62], [60, 77, 64, 63], [60, 78, 64, 64], [60, 79, 64, 65], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "errorMessage"], [61, 23, 65, 21], [61, 25, 65, 23, "setErrorMessage"], [61, 40, 65, 38], [61, 41, 65, 39], [61, 44, 65, 42], [61, 48, 65, 42, "useState"], [61, 63, 65, 50], [61, 65, 65, 66], [61, 69, 65, 70], [61, 70, 65, 71], [62, 4, 66, 2], [62, 10, 66, 8], [62, 11, 66, 9, "capturedPhoto"], [62, 24, 66, 22], [62, 26, 66, 24, "setCapturedPhoto"], [62, 42, 66, 40], [62, 43, 66, 41], [62, 46, 66, 44], [62, 50, 66, 44, "useState"], [62, 65, 66, 52], [62, 67, 66, 68], [62, 71, 66, 72], [62, 72, 66, 73], [63, 4, 67, 2], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "previewBlurEnabled"], [64, 29, 68, 27], [64, 31, 68, 29, "setPreviewBlurEnabled"], [64, 52, 68, 50], [64, 53, 68, 51], [64, 56, 68, 54], [64, 60, 68, 54, "useState"], [64, 75, 68, 62], [64, 77, 68, 63], [64, 81, 68, 67], [64, 82, 68, 68], [65, 4, 69, 2], [65, 10, 69, 8], [65, 11, 69, 9, "viewSize"], [65, 19, 69, 17], [65, 21, 69, 19, "setViewSize"], [65, 32, 69, 30], [65, 33, 69, 31], [65, 36, 69, 34], [65, 40, 69, 34, "useState"], [65, 55, 69, 42], [65, 57, 69, 43], [66, 6, 69, 45, "width"], [66, 11, 69, 50], [66, 13, 69, 52], [66, 14, 69, 53], [67, 6, 69, 55, "height"], [67, 12, 69, 61], [67, 14, 69, 63], [68, 4, 69, 65], [68, 5, 69, 66], [68, 6, 69, 67], [69, 4, 70, 2], [70, 4, 71, 2], [70, 10, 71, 8], [70, 11, 71, 9, "isCameraReady"], [70, 24, 71, 22], [70, 26, 71, 24, "setIsCameraReady"], [70, 42, 71, 40], [70, 43, 71, 41], [70, 46, 71, 44], [70, 50, 71, 44, "useState"], [70, 65, 71, 52], [70, 67, 71, 53], [70, 72, 71, 58], [70, 73, 71, 59], [71, 4, 73, 2], [71, 10, 73, 8], [71, 11, 73, 9, "upload"], [71, 17, 73, 15], [71, 18, 73, 16], [71, 21, 73, 19], [71, 25, 73, 19, "useUpload"], [71, 43, 73, 28], [71, 45, 73, 29], [71, 46, 73, 30], [72, 4, 74, 2], [73, 4, 75, 2], [73, 8, 75, 2, "useEffect"], [73, 24, 75, 11], [73, 26, 75, 12], [73, 32, 75, 18], [74, 6, 76, 4], [74, 12, 76, 10, "controller"], [74, 22, 76, 20], [74, 25, 76, 23], [74, 29, 76, 27, "AbortController"], [74, 44, 76, 42], [74, 45, 76, 43], [74, 46, 76, 44], [75, 6, 78, 4], [75, 7, 78, 5], [75, 19, 78, 17], [76, 8, 79, 6], [76, 12, 79, 10], [77, 10, 80, 8], [77, 16, 80, 14, "code"], [77, 20, 80, 18], [77, 23, 80, 21], [77, 29, 80, 27], [77, 33, 80, 27, "fetchChallengeCode"], [77, 68, 80, 45], [77, 70, 80, 46], [78, 12, 80, 48, "userId"], [78, 18, 80, 54], [79, 12, 80, 56, "requestId"], [79, 21, 80, 65], [80, 12, 80, 67, "signal"], [80, 18, 80, 73], [80, 20, 80, 75, "controller"], [80, 30, 80, 85], [80, 31, 80, 86, "signal"], [81, 10, 80, 93], [81, 11, 80, 94], [81, 12, 80, 95], [82, 10, 81, 8, "setChallengeCode"], [82, 26, 81, 24], [82, 27, 81, 25, "code"], [82, 31, 81, 29], [82, 32, 81, 30], [83, 8, 82, 6], [83, 9, 82, 7], [83, 10, 82, 8], [83, 17, 82, 15, "e"], [83, 18, 82, 16], [83, 20, 82, 18], [84, 10, 83, 8, "console"], [84, 17, 83, 15], [84, 18, 83, 16, "warn"], [84, 22, 83, 20], [84, 23, 83, 21], [84, 69, 83, 67], [84, 71, 83, 69, "e"], [84, 72, 83, 70], [84, 73, 83, 71], [85, 10, 84, 8, "setChallengeCode"], [85, 26, 84, 24], [85, 27, 84, 25], [85, 35, 84, 33, "Date"], [85, 39, 84, 37], [85, 40, 84, 38, "now"], [85, 43, 84, 41], [85, 44, 84, 42], [85, 45, 84, 43], [85, 46, 84, 44, "toString"], [85, 54, 84, 52], [85, 55, 84, 53], [85, 57, 84, 55], [85, 58, 84, 56], [85, 59, 84, 57, "toUpperCase"], [85, 70, 84, 68], [85, 71, 84, 69], [85, 72, 84, 70], [85, 74, 84, 72], [85, 75, 84, 73], [86, 8, 85, 6], [87, 6, 86, 4], [87, 7, 86, 5], [87, 9, 86, 7], [87, 10, 86, 8], [88, 6, 88, 4], [88, 13, 88, 11], [88, 19, 88, 17, "controller"], [88, 29, 88, 27], [88, 30, 88, 28, "abort"], [88, 35, 88, 33], [88, 36, 88, 34], [88, 37, 88, 35], [89, 4, 89, 2], [89, 5, 89, 3], [89, 7, 89, 5], [89, 8, 89, 6, "userId"], [89, 14, 89, 12], [89, 16, 89, 14, "requestId"], [89, 25, 89, 23], [89, 26, 89, 24], [89, 27, 89, 25], [91, 4, 91, 2], [92, 4, 92, 2], [92, 10, 92, 8, "loadTensorFlowFaceDetection"], [92, 37, 92, 35], [92, 40, 92, 38], [92, 46, 92, 38, "loadTensorFlowFaceDetection"], [92, 47, 92, 38], [92, 52, 92, 50], [93, 6, 93, 4, "console"], [93, 13, 93, 11], [93, 14, 93, 12, "log"], [93, 17, 93, 15], [93, 18, 93, 16], [93, 78, 93, 76], [93, 79, 93, 77], [95, 6, 95, 4], [96, 6, 96, 4], [96, 10, 96, 8], [96, 11, 96, 10, "window"], [96, 17, 96, 16], [96, 18, 96, 25, "tf"], [96, 20, 96, 27], [96, 22, 96, 29], [97, 8, 97, 6], [97, 14, 97, 12], [97, 18, 97, 16, "Promise"], [97, 25, 97, 23], [97, 26, 97, 24], [97, 27, 97, 25, "resolve"], [97, 34, 97, 32], [97, 36, 97, 34, "reject"], [97, 42, 97, 40], [97, 47, 97, 45], [98, 10, 98, 8], [98, 16, 98, 14, "script"], [98, 22, 98, 20], [98, 25, 98, 23, "document"], [98, 33, 98, 31], [98, 34, 98, 32, "createElement"], [98, 47, 98, 45], [98, 48, 98, 46], [98, 56, 98, 54], [98, 57, 98, 55], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "src"], [99, 20, 99, 18], [99, 23, 99, 21], [99, 92, 99, 90], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onload"], [100, 23, 100, 21], [100, 26, 100, 24, "resolve"], [100, 33, 100, 31], [101, 10, 101, 8, "script"], [101, 16, 101, 14], [101, 17, 101, 15, "onerror"], [101, 24, 101, 22], [101, 27, 101, 25, "reject"], [101, 33, 101, 31], [102, 10, 102, 8, "document"], [102, 18, 102, 16], [102, 19, 102, 17, "head"], [102, 23, 102, 21], [102, 24, 102, 22, "append<PERSON><PERSON><PERSON>"], [102, 35, 102, 33], [102, 36, 102, 34, "script"], [102, 42, 102, 40], [102, 43, 102, 41], [103, 8, 103, 6], [103, 9, 103, 7], [103, 10, 103, 8], [104, 6, 104, 4], [106, 6, 106, 4], [107, 6, 107, 4], [107, 10, 107, 8], [107, 11, 107, 10, "window"], [107, 17, 107, 16], [107, 18, 107, 25, "blazeface"], [107, 27, 107, 34], [107, 29, 107, 36], [108, 8, 108, 6], [108, 14, 108, 12], [108, 18, 108, 16, "Promise"], [108, 25, 108, 23], [108, 26, 108, 24], [108, 27, 108, 25, "resolve"], [108, 34, 108, 32], [108, 36, 108, 34, "reject"], [108, 42, 108, 40], [108, 47, 108, 45], [109, 10, 109, 8], [109, 16, 109, 14, "script"], [109, 22, 109, 20], [109, 25, 109, 23, "document"], [109, 33, 109, 31], [109, 34, 109, 32, "createElement"], [109, 47, 109, 45], [109, 48, 109, 46], [109, 56, 109, 54], [109, 57, 109, 55], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "src"], [110, 20, 110, 18], [110, 23, 110, 21], [110, 106, 110, 104], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onload"], [111, 23, 111, 21], [111, 26, 111, 24, "resolve"], [111, 33, 111, 31], [112, 10, 112, 8, "script"], [112, 16, 112, 14], [112, 17, 112, 15, "onerror"], [112, 24, 112, 22], [112, 27, 112, 25, "reject"], [112, 33, 112, 31], [113, 10, 113, 8, "document"], [113, 18, 113, 16], [113, 19, 113, 17, "head"], [113, 23, 113, 21], [113, 24, 113, 22, "append<PERSON><PERSON><PERSON>"], [113, 35, 113, 33], [113, 36, 113, 34, "script"], [113, 42, 113, 40], [113, 43, 113, 41], [114, 8, 114, 6], [114, 9, 114, 7], [114, 10, 114, 8], [115, 6, 115, 4], [116, 6, 117, 4, "console"], [116, 13, 117, 11], [116, 14, 117, 12, "log"], [116, 17, 117, 15], [116, 18, 117, 16], [116, 72, 117, 70], [116, 73, 117, 71], [117, 4, 118, 2], [117, 5, 118, 3], [118, 4, 120, 2], [118, 10, 120, 8, "detectFacesWithTensorFlow"], [118, 35, 120, 33], [118, 38, 120, 36], [118, 44, 120, 43, "img"], [118, 47, 120, 64], [118, 51, 120, 69], [119, 6, 121, 4], [119, 10, 121, 8], [120, 8, 122, 6], [120, 14, 122, 12, "blazeface"], [120, 23, 122, 21], [120, 26, 122, 25, "window"], [120, 32, 122, 31], [120, 33, 122, 40, "blazeface"], [120, 42, 122, 49], [121, 8, 123, 6], [121, 14, 123, 12, "tf"], [121, 16, 123, 14], [121, 19, 123, 18, "window"], [121, 25, 123, 24], [121, 26, 123, 33, "tf"], [121, 28, 123, 35], [122, 8, 125, 6, "console"], [122, 15, 125, 13], [122, 16, 125, 14, "log"], [122, 19, 125, 17], [122, 20, 125, 18], [122, 67, 125, 65], [122, 68, 125, 66], [123, 8, 126, 6], [123, 14, 126, 12, "model"], [123, 19, 126, 17], [123, 22, 126, 20], [123, 28, 126, 26, "blazeface"], [123, 37, 126, 35], [123, 38, 126, 36, "load"], [123, 42, 126, 40], [123, 43, 126, 41], [123, 44, 126, 42], [124, 8, 127, 6, "console"], [124, 15, 127, 13], [124, 16, 127, 14, "log"], [124, 19, 127, 17], [124, 20, 127, 18], [124, 82, 127, 80], [124, 83, 127, 81], [126, 8, 129, 6], [127, 8, 130, 6], [127, 14, 130, 12, "tensor"], [127, 20, 130, 18], [127, 23, 130, 21, "tf"], [127, 25, 130, 23], [127, 26, 130, 24, "browser"], [127, 33, 130, 31], [127, 34, 130, 32, "fromPixels"], [127, 44, 130, 42], [127, 45, 130, 43, "img"], [127, 48, 130, 46], [127, 49, 130, 47], [129, 8, 132, 6], [130, 8, 133, 6], [130, 14, 133, 12, "predictions"], [130, 25, 133, 23], [130, 28, 133, 26], [130, 34, 133, 32, "model"], [130, 39, 133, 37], [130, 40, 133, 38, "estimateFaces"], [130, 53, 133, 51], [130, 54, 133, 52, "tensor"], [130, 60, 133, 58], [130, 62, 133, 60], [130, 67, 133, 65], [130, 69, 133, 67], [130, 72, 133, 70], [130, 73, 133, 71], [130, 74, 133, 72], [130, 75, 133, 73], [132, 8, 135, 6], [133, 8, 136, 6, "tensor"], [133, 14, 136, 12], [133, 15, 136, 13, "dispose"], [133, 22, 136, 20], [133, 23, 136, 21], [133, 24, 136, 22], [134, 8, 138, 6, "console"], [134, 15, 138, 13], [134, 16, 138, 14, "log"], [134, 19, 138, 17], [134, 20, 138, 18], [134, 61, 138, 59, "predictions"], [134, 72, 138, 70], [134, 73, 138, 71, "length"], [134, 79, 138, 77], [134, 87, 138, 85], [134, 88, 138, 86], [136, 8, 140, 6], [137, 8, 141, 6], [137, 14, 141, 12, "faces"], [137, 19, 141, 17], [137, 22, 141, 20, "predictions"], [137, 33, 141, 31], [137, 34, 141, 32, "map"], [137, 37, 141, 35], [137, 38, 141, 36], [137, 39, 141, 37, "prediction"], [137, 49, 141, 52], [137, 51, 141, 54, "index"], [137, 56, 141, 67], [137, 61, 141, 72], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x"], [138, 18, 142, 16], [138, 20, 142, 18, "y"], [138, 21, 142, 19], [138, 22, 142, 20], [138, 25, 142, 23, "prediction"], [138, 35, 142, 33], [138, 36, 142, 34, "topLeft"], [138, 43, 142, 41], [139, 10, 143, 8], [139, 16, 143, 14], [139, 17, 143, 15, "x2"], [139, 19, 143, 17], [139, 21, 143, 19, "y2"], [139, 23, 143, 21], [139, 24, 143, 22], [139, 27, 143, 25, "prediction"], [139, 37, 143, 35], [139, 38, 143, 36, "bottomRight"], [139, 49, 143, 47], [140, 10, 144, 8], [140, 16, 144, 14, "width"], [140, 21, 144, 19], [140, 24, 144, 22, "x2"], [140, 26, 144, 24], [140, 29, 144, 27, "x"], [140, 30, 144, 28], [141, 10, 145, 8], [141, 16, 145, 14, "height"], [141, 22, 145, 20], [141, 25, 145, 23, "y2"], [141, 27, 145, 25], [141, 30, 145, 28, "y"], [141, 31, 145, 29], [142, 10, 147, 8, "console"], [142, 17, 147, 15], [142, 18, 147, 16, "log"], [142, 21, 147, 19], [142, 22, 147, 20], [142, 49, 147, 47, "index"], [142, 54, 147, 52], [142, 57, 147, 55], [142, 58, 147, 56], [142, 61, 147, 59], [142, 63, 147, 61], [143, 12, 148, 10, "topLeft"], [143, 19, 148, 17], [143, 21, 148, 19], [143, 22, 148, 20, "x"], [143, 23, 148, 21], [143, 25, 148, 23, "y"], [143, 26, 148, 24], [143, 27, 148, 25], [144, 12, 149, 10, "bottomRight"], [144, 23, 149, 21], [144, 25, 149, 23], [144, 26, 149, 24, "x2"], [144, 28, 149, 26], [144, 30, 149, 28, "y2"], [144, 32, 149, 30], [144, 33, 149, 31], [145, 12, 150, 10, "size"], [145, 16, 150, 14], [145, 18, 150, 16], [145, 19, 150, 17, "width"], [145, 24, 150, 22], [145, 26, 150, 24, "height"], [145, 32, 150, 30], [146, 10, 151, 8], [146, 11, 151, 9], [146, 12, 151, 10], [147, 10, 153, 8], [147, 17, 153, 15], [148, 12, 154, 10, "boundingBox"], [148, 23, 154, 21], [148, 25, 154, 23], [149, 14, 155, 12, "xCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "x"], [149, 25, 155, 23], [149, 28, 155, 26, "width"], [149, 33, 155, 31], [149, 36, 155, 34], [149, 37, 155, 35], [149, 41, 155, 39, "img"], [149, 44, 155, 42], [149, 45, 155, 43, "width"], [149, 50, 155, 48], [150, 14, 156, 12, "yCenter"], [150, 21, 156, 19], [150, 23, 156, 21], [150, 24, 156, 22, "y"], [150, 25, 156, 23], [150, 28, 156, 26, "height"], [150, 34, 156, 32], [150, 37, 156, 35], [150, 38, 156, 36], [150, 42, 156, 40, "img"], [150, 45, 156, 43], [150, 46, 156, 44, "height"], [150, 52, 156, 50], [151, 14, 157, 12, "width"], [151, 19, 157, 17], [151, 21, 157, 19, "width"], [151, 26, 157, 24], [151, 29, 157, 27, "img"], [151, 32, 157, 30], [151, 33, 157, 31, "width"], [151, 38, 157, 36], [152, 14, 158, 12, "height"], [152, 20, 158, 18], [152, 22, 158, 20, "height"], [152, 28, 158, 26], [152, 31, 158, 29, "img"], [152, 34, 158, 32], [152, 35, 158, 33, "height"], [153, 12, 159, 10], [154, 10, 160, 8], [154, 11, 160, 9], [155, 8, 161, 6], [155, 9, 161, 7], [155, 10, 161, 8], [156, 8, 163, 6], [156, 15, 163, 13, "faces"], [156, 20, 163, 18], [157, 6, 164, 4], [157, 7, 164, 5], [157, 8, 164, 6], [157, 15, 164, 13, "error"], [157, 20, 164, 18], [157, 22, 164, 20], [158, 8, 165, 6, "console"], [158, 15, 165, 13], [158, 16, 165, 14, "error"], [158, 21, 165, 19], [158, 22, 165, 20], [158, 75, 165, 73], [158, 77, 165, 75, "error"], [158, 82, 165, 80], [158, 83, 165, 81], [159, 8, 166, 6], [159, 15, 166, 13], [159, 17, 166, 15], [160, 6, 167, 4], [161, 4, 168, 2], [161, 5, 168, 3], [162, 4, 170, 2], [162, 10, 170, 8, "detectFacesHeuristic"], [162, 30, 170, 28], [162, 33, 170, 31, "detectFacesHeuristic"], [162, 34, 170, 32, "img"], [162, 37, 170, 53], [162, 39, 170, 55, "ctx"], [162, 42, 170, 84], [162, 47, 170, 89], [163, 6, 171, 4, "console"], [163, 13, 171, 11], [163, 14, 171, 12, "log"], [163, 17, 171, 15], [163, 18, 171, 16], [163, 83, 171, 81], [163, 84, 171, 82], [165, 6, 173, 4], [166, 6, 174, 4], [166, 12, 174, 10, "imageData"], [166, 21, 174, 19], [166, 24, 174, 22, "ctx"], [166, 27, 174, 25], [166, 28, 174, 26, "getImageData"], [166, 40, 174, 38], [166, 41, 174, 39], [166, 42, 174, 40], [166, 44, 174, 42], [166, 45, 174, 43], [166, 47, 174, 45, "img"], [166, 50, 174, 48], [166, 51, 174, 49, "width"], [166, 56, 174, 54], [166, 58, 174, 56, "img"], [166, 61, 174, 59], [166, 62, 174, 60, "height"], [166, 68, 174, 66], [166, 69, 174, 67], [167, 6, 175, 4], [167, 12, 175, 10, "data"], [167, 16, 175, 14], [167, 19, 175, 17, "imageData"], [167, 28, 175, 26], [167, 29, 175, 27, "data"], [167, 33, 175, 31], [169, 6, 177, 4], [170, 6, 178, 4], [170, 12, 178, 10, "faces"], [170, 17, 178, 15], [170, 20, 178, 18], [170, 22, 178, 20], [171, 6, 179, 4], [171, 12, 179, 10, "blockSize"], [171, 21, 179, 19], [171, 24, 179, 22, "Math"], [171, 28, 179, 26], [171, 29, 179, 27, "min"], [171, 32, 179, 30], [171, 33, 179, 31, "img"], [171, 36, 179, 34], [171, 37, 179, 35, "width"], [171, 42, 179, 40], [171, 44, 179, 42, "img"], [171, 47, 179, 45], [171, 48, 179, 46, "height"], [171, 54, 179, 52], [171, 55, 179, 53], [171, 58, 179, 56], [171, 60, 179, 58], [171, 61, 179, 59], [171, 62, 179, 60], [173, 6, 181, 4, "console"], [173, 13, 181, 11], [173, 14, 181, 12, "log"], [173, 17, 181, 15], [173, 18, 181, 16], [173, 59, 181, 57, "blockSize"], [173, 68, 181, 66], [173, 82, 181, 80], [173, 83, 181, 81], [174, 6, 183, 4], [174, 11, 183, 9], [174, 15, 183, 13, "y"], [174, 16, 183, 14], [174, 19, 183, 17], [174, 20, 183, 18], [174, 22, 183, 20, "y"], [174, 23, 183, 21], [174, 26, 183, 24, "img"], [174, 29, 183, 27], [174, 30, 183, 28, "height"], [174, 36, 183, 34], [174, 39, 183, 37, "blockSize"], [174, 48, 183, 46], [174, 50, 183, 48, "y"], [174, 51, 183, 49], [174, 55, 183, 53, "blockSize"], [174, 64, 183, 62], [174, 67, 183, 65], [174, 68, 183, 66], [174, 70, 183, 68], [175, 8, 183, 70], [176, 8, 184, 6], [176, 13, 184, 11], [176, 17, 184, 15, "x"], [176, 18, 184, 16], [176, 21, 184, 19], [176, 22, 184, 20], [176, 24, 184, 22, "x"], [176, 25, 184, 23], [176, 28, 184, 26, "img"], [176, 31, 184, 29], [176, 32, 184, 30, "width"], [176, 37, 184, 35], [176, 40, 184, 38, "blockSize"], [176, 49, 184, 47], [176, 51, 184, 49, "x"], [176, 52, 184, 50], [176, 56, 184, 54, "blockSize"], [176, 65, 184, 63], [176, 68, 184, 66], [176, 69, 184, 67], [176, 71, 184, 69], [177, 10, 185, 8], [177, 16, 185, 14, "analysis"], [177, 24, 185, 22], [177, 27, 185, 25, "analyzeRegionForFace"], [177, 47, 185, 45], [177, 48, 185, 46, "data"], [177, 52, 185, 50], [177, 54, 185, 52, "x"], [177, 55, 185, 53], [177, 57, 185, 55, "y"], [177, 58, 185, 56], [177, 60, 185, 58, "blockSize"], [177, 69, 185, 67], [177, 71, 185, 69, "img"], [177, 74, 185, 72], [177, 75, 185, 73, "width"], [177, 80, 185, 78], [177, 82, 185, 80, "img"], [177, 85, 185, 83], [177, 86, 185, 84, "height"], [177, 92, 185, 90], [177, 93, 185, 91], [179, 10, 187, 8], [180, 10, 188, 8], [180, 14, 188, 12, "analysis"], [180, 22, 188, 20], [180, 23, 188, 21, "skinRatio"], [180, 32, 188, 30], [180, 35, 188, 33], [180, 39, 188, 37], [181, 10, 188, 42], [182, 10, 189, 12, "analysis"], [182, 18, 189, 20], [182, 19, 189, 21, "hasVariation"], [182, 31, 189, 33], [182, 35, 190, 12, "analysis"], [182, 43, 190, 20], [182, 44, 190, 21, "brightness"], [182, 54, 190, 31], [182, 57, 190, 34], [182, 61, 190, 38], [183, 10, 190, 43], [184, 10, 191, 12, "analysis"], [184, 18, 191, 20], [184, 19, 191, 21, "brightness"], [184, 29, 191, 31], [184, 32, 191, 34], [184, 35, 191, 37], [184, 37, 191, 39], [185, 12, 191, 43], [187, 12, 193, 10, "faces"], [187, 17, 193, 15], [187, 18, 193, 16, "push"], [187, 22, 193, 20], [187, 23, 193, 21], [188, 14, 194, 12, "boundingBox"], [188, 25, 194, 23], [188, 27, 194, 25], [189, 16, 195, 14, "xCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "x"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "width"], [189, 56, 195, 54], [190, 16, 196, 14, "yCenter"], [190, 23, 196, 21], [190, 25, 196, 23], [190, 26, 196, 24, "y"], [190, 27, 196, 25], [190, 30, 196, 28, "blockSize"], [190, 39, 196, 37], [190, 42, 196, 40], [190, 43, 196, 41], [190, 47, 196, 45, "img"], [190, 50, 196, 48], [190, 51, 196, 49, "height"], [190, 57, 196, 55], [191, 16, 197, 14, "width"], [191, 21, 197, 19], [191, 23, 197, 22, "blockSize"], [191, 32, 197, 31], [191, 35, 197, 34], [191, 38, 197, 37], [191, 41, 197, 41, "img"], [191, 44, 197, 44], [191, 45, 197, 45, "width"], [191, 50, 197, 50], [192, 16, 198, 14, "height"], [192, 22, 198, 20], [192, 24, 198, 23, "blockSize"], [192, 33, 198, 32], [192, 36, 198, 35], [192, 39, 198, 38], [192, 42, 198, 42, "img"], [192, 45, 198, 45], [192, 46, 198, 46, "height"], [193, 14, 199, 12], [193, 15, 199, 13], [194, 14, 200, 12, "confidence"], [194, 24, 200, 22], [194, 26, 200, 24, "analysis"], [194, 34, 200, 32], [194, 35, 200, 33, "skinRatio"], [194, 44, 200, 42], [194, 47, 200, 45, "analysis"], [194, 55, 200, 53], [194, 56, 200, 54, "variation"], [195, 12, 201, 10], [195, 13, 201, 11], [195, 14, 201, 12], [196, 12, 203, 10, "console"], [196, 19, 203, 17], [196, 20, 203, 18, "log"], [196, 23, 203, 21], [196, 24, 203, 22], [196, 71, 203, 69, "Math"], [196, 75, 203, 73], [196, 76, 203, 74, "round"], [196, 81, 203, 79], [196, 82, 203, 80, "x"], [196, 83, 203, 81], [196, 84, 203, 82], [196, 89, 203, 87, "Math"], [196, 93, 203, 91], [196, 94, 203, 92, "round"], [196, 99, 203, 97], [196, 100, 203, 98, "y"], [196, 101, 203, 99], [196, 102, 203, 100], [196, 115, 203, 113], [196, 116, 203, 114, "analysis"], [196, 124, 203, 122], [196, 125, 203, 123, "skinRatio"], [196, 134, 203, 132], [196, 137, 203, 135], [196, 140, 203, 138], [196, 142, 203, 140, "toFixed"], [196, 149, 203, 147], [196, 150, 203, 148], [196, 151, 203, 149], [196, 152, 203, 150], [196, 169, 203, 167, "analysis"], [196, 177, 203, 175], [196, 178, 203, 176, "variation"], [196, 187, 203, 185], [196, 188, 203, 186, "toFixed"], [196, 195, 203, 193], [196, 196, 203, 194], [196, 197, 203, 195], [196, 198, 203, 196], [196, 215, 203, 213, "analysis"], [196, 223, 203, 221], [196, 224, 203, 222, "brightness"], [196, 234, 203, 232], [196, 235, 203, 233, "toFixed"], [196, 242, 203, 240], [196, 243, 203, 241], [196, 244, 203, 242], [196, 245, 203, 243], [196, 247, 203, 245], [196, 248, 203, 246], [197, 10, 204, 8], [198, 8, 205, 6], [199, 6, 206, 4], [201, 6, 208, 4], [202, 6, 209, 4, "faces"], [202, 11, 209, 9], [202, 12, 209, 10, "sort"], [202, 16, 209, 14], [202, 17, 209, 15], [202, 18, 209, 16, "a"], [202, 19, 209, 17], [202, 21, 209, 19, "b"], [202, 22, 209, 20], [202, 27, 209, 25], [202, 28, 209, 26, "b"], [202, 29, 209, 27], [202, 30, 209, 28, "confidence"], [202, 40, 209, 38], [202, 44, 209, 42], [202, 45, 209, 43], [202, 50, 209, 48, "a"], [202, 51, 209, 49], [202, 52, 209, 50, "confidence"], [202, 62, 209, 60], [202, 66, 209, 64], [202, 67, 209, 65], [202, 68, 209, 66], [202, 69, 209, 67], [203, 6, 210, 4], [203, 12, 210, 10, "mergedFaces"], [203, 23, 210, 21], [203, 26, 210, 24, "mergeFaceDetections"], [203, 45, 210, 43], [203, 46, 210, 44, "faces"], [203, 51, 210, 49], [203, 52, 210, 50], [204, 6, 212, 4, "console"], [204, 13, 212, 11], [204, 14, 212, 12, "log"], [204, 17, 212, 15], [204, 18, 212, 16], [204, 61, 212, 59, "faces"], [204, 66, 212, 64], [204, 67, 212, 65, "length"], [204, 73, 212, 71], [204, 90, 212, 88, "mergedFaces"], [204, 101, 212, 99], [204, 102, 212, 100, "length"], [204, 108, 212, 106], [204, 123, 212, 121], [204, 124, 212, 122], [205, 6, 213, 4], [205, 13, 213, 11, "mergedFaces"], [205, 24, 213, 22], [205, 25, 213, 23, "slice"], [205, 30, 213, 28], [205, 31, 213, 29], [205, 32, 213, 30], [205, 34, 213, 32], [205, 35, 213, 33], [205, 36, 213, 34], [205, 37, 213, 35], [205, 38, 213, 36], [206, 4, 214, 2], [206, 5, 214, 3], [207, 4, 216, 2], [207, 10, 216, 8, "detectFacesAggressive"], [207, 31, 216, 29], [207, 34, 216, 32, "detectFacesAggressive"], [207, 35, 216, 33, "img"], [207, 38, 216, 54], [207, 40, 216, 56, "ctx"], [207, 43, 216, 85], [207, 48, 216, 90], [208, 6, 217, 4, "console"], [208, 13, 217, 11], [208, 14, 217, 12, "log"], [208, 17, 217, 15], [208, 18, 217, 16], [208, 75, 217, 73], [208, 76, 217, 74], [210, 6, 219, 4], [211, 6, 220, 4], [211, 12, 220, 10, "imageData"], [211, 21, 220, 19], [211, 24, 220, 22, "ctx"], [211, 27, 220, 25], [211, 28, 220, 26, "getImageData"], [211, 40, 220, 38], [211, 41, 220, 39], [211, 42, 220, 40], [211, 44, 220, 42], [211, 45, 220, 43], [211, 47, 220, 45, "img"], [211, 50, 220, 48], [211, 51, 220, 49, "width"], [211, 56, 220, 54], [211, 58, 220, 56, "img"], [211, 61, 220, 59], [211, 62, 220, 60, "height"], [211, 68, 220, 66], [211, 69, 220, 67], [212, 6, 221, 4], [212, 12, 221, 10, "data"], [212, 16, 221, 14], [212, 19, 221, 17, "imageData"], [212, 28, 221, 26], [212, 29, 221, 27, "data"], [212, 33, 221, 31], [213, 6, 223, 4], [213, 12, 223, 10, "faces"], [213, 17, 223, 15], [213, 20, 223, 18], [213, 22, 223, 20], [214, 6, 224, 4], [214, 12, 224, 10, "blockSize"], [214, 21, 224, 19], [214, 24, 224, 22, "Math"], [214, 28, 224, 26], [214, 29, 224, 27, "min"], [214, 32, 224, 30], [214, 33, 224, 31, "img"], [214, 36, 224, 34], [214, 37, 224, 35, "width"], [214, 42, 224, 40], [214, 44, 224, 42, "img"], [214, 47, 224, 45], [214, 48, 224, 46, "height"], [214, 54, 224, 52], [214, 55, 224, 53], [214, 58, 224, 56], [214, 59, 224, 57], [214, 60, 224, 58], [214, 61, 224, 59], [216, 6, 226, 4], [216, 11, 226, 9], [216, 15, 226, 13, "y"], [216, 16, 226, 14], [216, 19, 226, 17], [216, 20, 226, 18], [216, 22, 226, 20, "y"], [216, 23, 226, 21], [216, 26, 226, 24, "img"], [216, 29, 226, 27], [216, 30, 226, 28, "height"], [216, 36, 226, 34], [216, 39, 226, 37, "blockSize"], [216, 48, 226, 46], [216, 50, 226, 48, "y"], [216, 51, 226, 49], [216, 55, 226, 53, "blockSize"], [216, 64, 226, 62], [216, 67, 226, 65], [216, 68, 226, 66], [216, 70, 226, 68], [217, 8, 226, 70], [218, 8, 227, 6], [218, 13, 227, 11], [218, 17, 227, 15, "x"], [218, 18, 227, 16], [218, 21, 227, 19], [218, 22, 227, 20], [218, 24, 227, 22, "x"], [218, 25, 227, 23], [218, 28, 227, 26, "img"], [218, 31, 227, 29], [218, 32, 227, 30, "width"], [218, 37, 227, 35], [218, 40, 227, 38, "blockSize"], [218, 49, 227, 47], [218, 51, 227, 49, "x"], [218, 52, 227, 50], [218, 56, 227, 54, "blockSize"], [218, 65, 227, 63], [218, 68, 227, 66], [218, 69, 227, 67], [218, 71, 227, 69], [219, 10, 228, 8], [219, 16, 228, 14, "analysis"], [219, 24, 228, 22], [219, 27, 228, 25, "analyzeRegionForFace"], [219, 47, 228, 45], [219, 48, 228, 46, "data"], [219, 52, 228, 50], [219, 54, 228, 52, "x"], [219, 55, 228, 53], [219, 57, 228, 55, "y"], [219, 58, 228, 56], [219, 60, 228, 58, "blockSize"], [219, 69, 228, 67], [219, 71, 228, 69, "img"], [219, 74, 228, 72], [219, 75, 228, 73, "width"], [219, 80, 228, 78], [219, 82, 228, 80, "img"], [219, 85, 228, 83], [219, 86, 228, 84, "height"], [219, 92, 228, 90], [219, 93, 228, 91], [221, 10, 230, 8], [222, 10, 231, 8], [222, 14, 231, 12, "analysis"], [222, 22, 231, 20], [222, 23, 231, 21, "skinRatio"], [222, 32, 231, 30], [222, 35, 231, 33], [222, 39, 231, 37], [223, 10, 231, 42], [224, 10, 232, 12, "analysis"], [224, 18, 232, 20], [224, 19, 232, 21, "brightness"], [224, 29, 232, 31], [224, 32, 232, 34], [224, 35, 232, 37], [225, 10, 232, 42], [226, 10, 233, 12, "analysis"], [226, 18, 233, 20], [226, 19, 233, 21, "brightness"], [226, 29, 233, 31], [226, 32, 233, 34], [226, 36, 233, 38], [226, 38, 233, 40], [227, 12, 233, 43], [229, 12, 235, 10, "faces"], [229, 17, 235, 15], [229, 18, 235, 16, "push"], [229, 22, 235, 20], [229, 23, 235, 21], [230, 14, 236, 12, "boundingBox"], [230, 25, 236, 23], [230, 27, 236, 25], [231, 16, 237, 14, "xCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "x"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "width"], [231, 56, 237, 54], [232, 16, 238, 14, "yCenter"], [232, 23, 238, 21], [232, 25, 238, 23], [232, 26, 238, 24, "y"], [232, 27, 238, 25], [232, 30, 238, 28, "blockSize"], [232, 39, 238, 37], [232, 42, 238, 40], [232, 43, 238, 41], [232, 47, 238, 45, "img"], [232, 50, 238, 48], [232, 51, 238, 49, "height"], [232, 57, 238, 55], [233, 16, 239, 14, "width"], [233, 21, 239, 19], [233, 23, 239, 21, "blockSize"], [233, 32, 239, 30], [233, 35, 239, 33, "img"], [233, 38, 239, 36], [233, 39, 239, 37, "width"], [233, 44, 239, 42], [234, 16, 240, 14, "height"], [234, 22, 240, 20], [234, 24, 240, 22, "blockSize"], [234, 33, 240, 31], [234, 36, 240, 34, "img"], [234, 39, 240, 37], [234, 40, 240, 38, "height"], [235, 14, 241, 12], [235, 15, 241, 13], [236, 14, 242, 12, "confidence"], [236, 24, 242, 22], [236, 26, 242, 24], [236, 29, 242, 27], [236, 30, 242, 28], [237, 12, 243, 10], [237, 13, 243, 11], [237, 14, 243, 12], [238, 10, 244, 8], [239, 8, 245, 6], [240, 6, 246, 4], [242, 6, 248, 4], [243, 6, 249, 4], [243, 12, 249, 10, "mergedFaces"], [243, 23, 249, 21], [243, 26, 249, 24, "mergeFaceDetections"], [243, 45, 249, 43], [243, 46, 249, 44, "faces"], [243, 51, 249, 49], [243, 52, 249, 50], [244, 6, 250, 4, "console"], [244, 13, 250, 11], [244, 14, 250, 12, "log"], [244, 17, 250, 15], [244, 18, 250, 16], [244, 62, 250, 60, "faces"], [244, 67, 250, 65], [244, 68, 250, 66, "length"], [244, 74, 250, 72], [244, 91, 250, 89, "mergedFaces"], [244, 102, 250, 100], [244, 103, 250, 101, "length"], [244, 109, 250, 107], [244, 124, 250, 122], [244, 125, 250, 123], [245, 6, 251, 4], [245, 13, 251, 11, "mergedFaces"], [245, 24, 251, 22], [245, 25, 251, 23, "slice"], [245, 30, 251, 28], [245, 31, 251, 29], [245, 32, 251, 30], [245, 34, 251, 32], [245, 35, 251, 33], [245, 36, 251, 34], [245, 37, 251, 35], [245, 38, 251, 36], [246, 4, 252, 2], [246, 5, 252, 3], [247, 4, 254, 2], [247, 10, 254, 8, "analyzeRegionForFace"], [247, 30, 254, 28], [247, 33, 254, 31, "analyzeRegionForFace"], [247, 34, 254, 32, "data"], [247, 38, 254, 55], [247, 40, 254, 57, "startX"], [247, 46, 254, 71], [247, 48, 254, 73, "startY"], [247, 54, 254, 87], [247, 56, 254, 89, "size"], [247, 60, 254, 101], [247, 62, 254, 103, "imageWidth"], [247, 72, 254, 121], [247, 74, 254, 123, "imageHeight"], [247, 85, 254, 142], [247, 90, 254, 147], [248, 6, 255, 4], [248, 10, 255, 8, "skinPixels"], [248, 20, 255, 18], [248, 23, 255, 21], [248, 24, 255, 22], [249, 6, 256, 4], [249, 10, 256, 8, "totalPixels"], [249, 21, 256, 19], [249, 24, 256, 22], [249, 25, 256, 23], [250, 6, 257, 4], [250, 10, 257, 8, "totalBrightness"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "colorVariations"], [251, 25, 258, 23], [251, 28, 258, 26], [251, 29, 258, 27], [252, 6, 259, 4], [252, 10, 259, 8, "prevR"], [252, 15, 259, 13], [252, 18, 259, 16], [252, 19, 259, 17], [253, 8, 259, 19, "prevG"], [253, 13, 259, 24], [253, 16, 259, 27], [253, 17, 259, 28], [254, 8, 259, 30, "prevB"], [254, 13, 259, 35], [254, 16, 259, 38], [254, 17, 259, 39], [255, 6, 261, 4], [255, 11, 261, 9], [255, 15, 261, 13, "y"], [255, 16, 261, 14], [255, 19, 261, 17, "startY"], [255, 25, 261, 23], [255, 27, 261, 25, "y"], [255, 28, 261, 26], [255, 31, 261, 29, "startY"], [255, 37, 261, 35], [255, 40, 261, 38, "size"], [255, 44, 261, 42], [255, 48, 261, 46, "y"], [255, 49, 261, 47], [255, 52, 261, 50, "imageHeight"], [255, 63, 261, 61], [255, 65, 261, 63, "y"], [255, 66, 261, 64], [255, 68, 261, 66], [255, 70, 261, 68], [256, 8, 262, 6], [256, 13, 262, 11], [256, 17, 262, 15, "x"], [256, 18, 262, 16], [256, 21, 262, 19, "startX"], [256, 27, 262, 25], [256, 29, 262, 27, "x"], [256, 30, 262, 28], [256, 33, 262, 31, "startX"], [256, 39, 262, 37], [256, 42, 262, 40, "size"], [256, 46, 262, 44], [256, 50, 262, 48, "x"], [256, 51, 262, 49], [256, 54, 262, 52, "imageWidth"], [256, 64, 262, 62], [256, 66, 262, 64, "x"], [256, 67, 262, 65], [256, 69, 262, 67], [256, 71, 262, 69], [257, 10, 263, 8], [257, 16, 263, 14, "index"], [257, 21, 263, 19], [257, 24, 263, 22], [257, 25, 263, 23, "y"], [257, 26, 263, 24], [257, 29, 263, 27, "imageWidth"], [257, 39, 263, 37], [257, 42, 263, 40, "x"], [257, 43, 263, 41], [257, 47, 263, 45], [257, 48, 263, 46], [258, 10, 264, 8], [258, 16, 264, 14, "r"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 31, 264, 29], [259, 10, 265, 8], [259, 16, 265, 14, "g"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [260, 10, 266, 8], [260, 16, 266, 14, "b"], [260, 17, 266, 15], [260, 20, 266, 18, "data"], [260, 24, 266, 22], [260, 25, 266, 23, "index"], [260, 30, 266, 28], [260, 33, 266, 31], [260, 34, 266, 32], [260, 35, 266, 33], [262, 10, 268, 8], [263, 10, 269, 8], [263, 14, 269, 12, "isSkinTone"], [263, 24, 269, 22], [263, 25, 269, 23, "r"], [263, 26, 269, 24], [263, 28, 269, 26, "g"], [263, 29, 269, 27], [263, 31, 269, 29, "b"], [263, 32, 269, 30], [263, 33, 269, 31], [263, 35, 269, 33], [264, 12, 270, 10, "skinPixels"], [264, 22, 270, 20], [264, 24, 270, 22], [265, 10, 271, 8], [267, 10, 273, 8], [268, 10, 274, 8], [268, 16, 274, 14, "brightness"], [268, 26, 274, 24], [268, 29, 274, 27], [268, 30, 274, 28, "r"], [268, 31, 274, 29], [268, 34, 274, 32, "g"], [268, 35, 274, 33], [268, 38, 274, 36, "b"], [268, 39, 274, 37], [268, 44, 274, 42], [268, 45, 274, 43], [268, 48, 274, 46], [268, 51, 274, 49], [268, 52, 274, 50], [269, 10, 275, 8, "totalBrightness"], [269, 25, 275, 23], [269, 29, 275, 27, "brightness"], [269, 39, 275, 37], [271, 10, 277, 8], [272, 10, 278, 8], [272, 14, 278, 12, "totalPixels"], [272, 25, 278, 23], [272, 28, 278, 26], [272, 29, 278, 27], [272, 31, 278, 29], [273, 12, 279, 10], [273, 18, 279, 16, "colorDiff"], [273, 27, 279, 25], [273, 30, 279, 28, "Math"], [273, 34, 279, 32], [273, 35, 279, 33, "abs"], [273, 38, 279, 36], [273, 39, 279, 37, "r"], [273, 40, 279, 38], [273, 43, 279, 41, "prevR"], [273, 48, 279, 46], [273, 49, 279, 47], [273, 52, 279, 50, "Math"], [273, 56, 279, 54], [273, 57, 279, 55, "abs"], [273, 60, 279, 58], [273, 61, 279, 59, "g"], [273, 62, 279, 60], [273, 65, 279, 63, "prevG"], [273, 70, 279, 68], [273, 71, 279, 69], [273, 74, 279, 72, "Math"], [273, 78, 279, 76], [273, 79, 279, 77, "abs"], [273, 82, 279, 80], [273, 83, 279, 81, "b"], [273, 84, 279, 82], [273, 87, 279, 85, "prevB"], [273, 92, 279, 90], [273, 93, 279, 91], [274, 12, 280, 10], [274, 16, 280, 14, "colorDiff"], [274, 25, 280, 23], [274, 28, 280, 26], [274, 30, 280, 28], [274, 32, 280, 30], [275, 14, 280, 32], [276, 14, 281, 12, "colorVariations"], [276, 29, 281, 27], [276, 31, 281, 29], [277, 12, 282, 10], [278, 10, 283, 8], [279, 10, 285, 8, "prevR"], [279, 15, 285, 13], [279, 18, 285, 16, "r"], [279, 19, 285, 17], [280, 10, 285, 19, "prevG"], [280, 15, 285, 24], [280, 18, 285, 27, "g"], [280, 19, 285, 28], [281, 10, 285, 30, "prevB"], [281, 15, 285, 35], [281, 18, 285, 38, "b"], [281, 19, 285, 39], [282, 10, 286, 8, "totalPixels"], [282, 21, 286, 19], [282, 23, 286, 21], [283, 8, 287, 6], [284, 6, 288, 4], [285, 6, 290, 4], [285, 13, 290, 11], [286, 8, 291, 6, "skinRatio"], [286, 17, 291, 15], [286, 19, 291, 17, "skinPixels"], [286, 29, 291, 27], [286, 32, 291, 30, "totalPixels"], [286, 43, 291, 41], [287, 8, 292, 6, "brightness"], [287, 18, 292, 16], [287, 20, 292, 18, "totalBrightness"], [287, 35, 292, 33], [287, 38, 292, 36, "totalPixels"], [287, 49, 292, 47], [288, 8, 293, 6, "variation"], [288, 17, 293, 15], [288, 19, 293, 17, "colorVariations"], [288, 34, 293, 32], [288, 37, 293, 35, "totalPixels"], [288, 48, 293, 46], [289, 8, 294, 6, "hasVariation"], [289, 20, 294, 18], [289, 22, 294, 20, "colorVariations"], [289, 37, 294, 35], [289, 40, 294, 38, "totalPixels"], [289, 51, 294, 49], [289, 54, 294, 52], [289, 57, 294, 55], [289, 58, 294, 56], [290, 6, 295, 4], [290, 7, 295, 5], [291, 4, 296, 2], [291, 5, 296, 3], [292, 4, 298, 2], [292, 10, 298, 8, "isSkinTone"], [292, 20, 298, 18], [292, 23, 298, 21, "isSkinTone"], [292, 24, 298, 22, "r"], [292, 25, 298, 31], [292, 27, 298, 33, "g"], [292, 28, 298, 42], [292, 30, 298, 44, "b"], [292, 31, 298, 53], [292, 36, 298, 58], [293, 6, 299, 4], [294, 6, 300, 4], [294, 13, 301, 6, "r"], [294, 14, 301, 7], [294, 17, 301, 10], [294, 19, 301, 12], [294, 23, 301, 16, "g"], [294, 24, 301, 17], [294, 27, 301, 20], [294, 29, 301, 22], [294, 33, 301, 26, "b"], [294, 34, 301, 27], [294, 37, 301, 30], [294, 39, 301, 32], [294, 43, 302, 6, "r"], [294, 44, 302, 7], [294, 47, 302, 10, "g"], [294, 48, 302, 11], [294, 52, 302, 15, "r"], [294, 53, 302, 16], [294, 56, 302, 19, "b"], [294, 57, 302, 20], [294, 61, 303, 6, "Math"], [294, 65, 303, 10], [294, 66, 303, 11, "abs"], [294, 69, 303, 14], [294, 70, 303, 15, "r"], [294, 71, 303, 16], [294, 74, 303, 19, "g"], [294, 75, 303, 20], [294, 76, 303, 21], [294, 79, 303, 24], [294, 81, 303, 26], [294, 85, 304, 6, "Math"], [294, 89, 304, 10], [294, 90, 304, 11, "max"], [294, 93, 304, 14], [294, 94, 304, 15, "r"], [294, 95, 304, 16], [294, 97, 304, 18, "g"], [294, 98, 304, 19], [294, 100, 304, 21, "b"], [294, 101, 304, 22], [294, 102, 304, 23], [294, 105, 304, 26, "Math"], [294, 109, 304, 30], [294, 110, 304, 31, "min"], [294, 113, 304, 34], [294, 114, 304, 35, "r"], [294, 115, 304, 36], [294, 117, 304, 38, "g"], [294, 118, 304, 39], [294, 120, 304, 41, "b"], [294, 121, 304, 42], [294, 122, 304, 43], [294, 125, 304, 46], [294, 127, 304, 48], [295, 4, 306, 2], [295, 5, 306, 3], [296, 4, 308, 2], [296, 10, 308, 8, "mergeFaceDetections"], [296, 29, 308, 27], [296, 32, 308, 31, "faces"], [296, 37, 308, 43], [296, 41, 308, 48], [297, 6, 309, 4], [297, 10, 309, 8, "faces"], [297, 15, 309, 13], [297, 16, 309, 14, "length"], [297, 22, 309, 20], [297, 26, 309, 24], [297, 27, 309, 25], [297, 29, 309, 27], [297, 36, 309, 34, "faces"], [297, 41, 309, 39], [298, 6, 311, 4], [298, 12, 311, 10, "merged"], [298, 18, 311, 16], [298, 21, 311, 19], [298, 23, 311, 21], [299, 6, 312, 4], [299, 12, 312, 10, "used"], [299, 16, 312, 14], [299, 19, 312, 17], [299, 23, 312, 21, "Set"], [299, 26, 312, 24], [299, 27, 312, 25], [299, 28, 312, 26], [300, 6, 314, 4], [300, 11, 314, 9], [300, 15, 314, 13, "i"], [300, 16, 314, 14], [300, 19, 314, 17], [300, 20, 314, 18], [300, 22, 314, 20, "i"], [300, 23, 314, 21], [300, 26, 314, 24, "faces"], [300, 31, 314, 29], [300, 32, 314, 30, "length"], [300, 38, 314, 36], [300, 40, 314, 38, "i"], [300, 41, 314, 39], [300, 43, 314, 41], [300, 45, 314, 43], [301, 8, 315, 6], [301, 12, 315, 10, "used"], [301, 16, 315, 14], [301, 17, 315, 15, "has"], [301, 20, 315, 18], [301, 21, 315, 19, "i"], [301, 22, 315, 20], [301, 23, 315, 21], [301, 25, 315, 23], [302, 8, 317, 6], [302, 12, 317, 10, "currentFace"], [302, 23, 317, 21], [302, 26, 317, 24, "faces"], [302, 31, 317, 29], [302, 32, 317, 30, "i"], [302, 33, 317, 31], [302, 34, 317, 32], [303, 8, 318, 6, "used"], [303, 12, 318, 10], [303, 13, 318, 11, "add"], [303, 16, 318, 14], [303, 17, 318, 15, "i"], [303, 18, 318, 16], [303, 19, 318, 17], [305, 8, 320, 6], [306, 8, 321, 6], [306, 13, 321, 11], [306, 17, 321, 15, "j"], [306, 18, 321, 16], [306, 21, 321, 19, "i"], [306, 22, 321, 20], [306, 25, 321, 23], [306, 26, 321, 24], [306, 28, 321, 26, "j"], [306, 29, 321, 27], [306, 32, 321, 30, "faces"], [306, 37, 321, 35], [306, 38, 321, 36, "length"], [306, 44, 321, 42], [306, 46, 321, 44, "j"], [306, 47, 321, 45], [306, 49, 321, 47], [306, 51, 321, 49], [307, 10, 322, 8], [307, 14, 322, 12, "used"], [307, 18, 322, 16], [307, 19, 322, 17, "has"], [307, 22, 322, 20], [307, 23, 322, 21, "j"], [307, 24, 322, 22], [307, 25, 322, 23], [307, 27, 322, 25], [308, 10, 324, 8], [308, 16, 324, 14, "overlap"], [308, 23, 324, 21], [308, 26, 324, 24, "calculateOverlap"], [308, 42, 324, 40], [308, 43, 324, 41, "currentFace"], [308, 54, 324, 52], [308, 55, 324, 53, "boundingBox"], [308, 66, 324, 64], [308, 68, 324, 66, "faces"], [308, 73, 324, 71], [308, 74, 324, 72, "j"], [308, 75, 324, 73], [308, 76, 324, 74], [308, 77, 324, 75, "boundingBox"], [308, 88, 324, 86], [308, 89, 324, 87], [309, 10, 325, 8], [309, 14, 325, 12, "overlap"], [309, 21, 325, 19], [309, 24, 325, 22], [309, 27, 325, 25], [309, 29, 325, 27], [310, 12, 325, 29], [311, 12, 326, 10], [312, 12, 327, 10, "currentFace"], [312, 23, 327, 21], [312, 26, 327, 24, "mergeTwoFaces"], [312, 39, 327, 37], [312, 40, 327, 38, "currentFace"], [312, 51, 327, 49], [312, 53, 327, 51, "faces"], [312, 58, 327, 56], [312, 59, 327, 57, "j"], [312, 60, 327, 58], [312, 61, 327, 59], [312, 62, 327, 60], [313, 12, 328, 10, "used"], [313, 16, 328, 14], [313, 17, 328, 15, "add"], [313, 20, 328, 18], [313, 21, 328, 19, "j"], [313, 22, 328, 20], [313, 23, 328, 21], [314, 10, 329, 8], [315, 8, 330, 6], [316, 8, 332, 6, "merged"], [316, 14, 332, 12], [316, 15, 332, 13, "push"], [316, 19, 332, 17], [316, 20, 332, 18, "currentFace"], [316, 31, 332, 29], [316, 32, 332, 30], [317, 6, 333, 4], [318, 6, 335, 4], [318, 13, 335, 11, "merged"], [318, 19, 335, 17], [319, 4, 336, 2], [319, 5, 336, 3], [320, 4, 338, 2], [320, 10, 338, 8, "calculateOverlap"], [320, 26, 338, 24], [320, 29, 338, 27, "calculateOverlap"], [320, 30, 338, 28, "box1"], [320, 34, 338, 37], [320, 36, 338, 39, "box2"], [320, 40, 338, 48], [320, 45, 338, 53], [321, 6, 339, 4], [321, 12, 339, 10, "x1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "xCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "width"], [321, 51, 339, 49], [321, 54, 339, 50], [321, 55, 339, 51], [321, 57, 339, 53, "box2"], [321, 61, 339, 57], [321, 62, 339, 58, "xCenter"], [321, 69, 339, 65], [321, 72, 339, 68, "box2"], [321, 76, 339, 72], [321, 77, 339, 73, "width"], [321, 82, 339, 78], [321, 85, 339, 79], [321, 86, 339, 80], [321, 87, 339, 81], [322, 6, 340, 4], [322, 12, 340, 10, "y1"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "max"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "yCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "height"], [322, 52, 340, 50], [322, 55, 340, 51], [322, 56, 340, 52], [322, 58, 340, 54, "box2"], [322, 62, 340, 58], [322, 63, 340, 59, "yCenter"], [322, 70, 340, 66], [322, 73, 340, 69, "box2"], [322, 77, 340, 73], [322, 78, 340, 74, "height"], [322, 84, 340, 80], [322, 87, 340, 81], [322, 88, 340, 82], [322, 89, 340, 83], [323, 6, 341, 4], [323, 12, 341, 10, "x2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "xCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "width"], [323, 51, 341, 49], [323, 54, 341, 50], [323, 55, 341, 51], [323, 57, 341, 53, "box2"], [323, 61, 341, 57], [323, 62, 341, 58, "xCenter"], [323, 69, 341, 65], [323, 72, 341, 68, "box2"], [323, 76, 341, 72], [323, 77, 341, 73, "width"], [323, 82, 341, 78], [323, 85, 341, 79], [323, 86, 341, 80], [323, 87, 341, 81], [324, 6, 342, 4], [324, 12, 342, 10, "y2"], [324, 14, 342, 12], [324, 17, 342, 15, "Math"], [324, 21, 342, 19], [324, 22, 342, 20, "min"], [324, 25, 342, 23], [324, 26, 342, 24, "box1"], [324, 30, 342, 28], [324, 31, 342, 29, "yCenter"], [324, 38, 342, 36], [324, 41, 342, 39, "box1"], [324, 45, 342, 43], [324, 46, 342, 44, "height"], [324, 52, 342, 50], [324, 55, 342, 51], [324, 56, 342, 52], [324, 58, 342, 54, "box2"], [324, 62, 342, 58], [324, 63, 342, 59, "yCenter"], [324, 70, 342, 66], [324, 73, 342, 69, "box2"], [324, 77, 342, 73], [324, 78, 342, 74, "height"], [324, 84, 342, 80], [324, 87, 342, 81], [324, 88, 342, 82], [324, 89, 342, 83], [325, 6, 344, 4], [325, 10, 344, 8, "x2"], [325, 12, 344, 10], [325, 16, 344, 14, "x1"], [325, 18, 344, 16], [325, 22, 344, 20, "y2"], [325, 24, 344, 22], [325, 28, 344, 26, "y1"], [325, 30, 344, 28], [325, 32, 344, 30], [325, 39, 344, 37], [325, 40, 344, 38], [326, 6, 346, 4], [326, 12, 346, 10, "overlapArea"], [326, 23, 346, 21], [326, 26, 346, 24], [326, 27, 346, 25, "x2"], [326, 29, 346, 27], [326, 32, 346, 30, "x1"], [326, 34, 346, 32], [326, 39, 346, 37, "y2"], [326, 41, 346, 39], [326, 44, 346, 42, "y1"], [326, 46, 346, 44], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box1Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box1"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box1"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 348, 4], [328, 12, 348, 10, "box2Area"], [328, 20, 348, 18], [328, 23, 348, 21, "box2"], [328, 27, 348, 25], [328, 28, 348, 26, "width"], [328, 33, 348, 31], [328, 36, 348, 34, "box2"], [328, 40, 348, 38], [328, 41, 348, 39, "height"], [328, 47, 348, 45], [329, 6, 350, 4], [329, 13, 350, 11, "overlapArea"], [329, 24, 350, 22], [329, 27, 350, 25, "Math"], [329, 31, 350, 29], [329, 32, 350, 30, "min"], [329, 35, 350, 33], [329, 36, 350, 34, "box1Area"], [329, 44, 350, 42], [329, 46, 350, 44, "box2Area"], [329, 54, 350, 52], [329, 55, 350, 53], [330, 4, 351, 2], [330, 5, 351, 3], [331, 4, 353, 2], [331, 10, 353, 8, "mergeTwoFaces"], [331, 23, 353, 21], [331, 26, 353, 24, "mergeTwoFaces"], [331, 27, 353, 25, "face1"], [331, 32, 353, 35], [331, 34, 353, 37, "face2"], [331, 39, 353, 47], [331, 44, 353, 52], [332, 6, 354, 4], [332, 12, 354, 10, "box1"], [332, 16, 354, 14], [332, 19, 354, 17, "face1"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 355, 4], [333, 12, 355, 10, "box2"], [333, 16, 355, 14], [333, 19, 355, 17, "face2"], [333, 24, 355, 22], [333, 25, 355, 23, "boundingBox"], [333, 36, 355, 34], [334, 6, 357, 4], [334, 12, 357, 10, "left"], [334, 16, 357, 14], [334, 19, 357, 17, "Math"], [334, 23, 357, 21], [334, 24, 357, 22, "min"], [334, 27, 357, 25], [334, 28, 357, 26, "box1"], [334, 32, 357, 30], [334, 33, 357, 31, "xCenter"], [334, 40, 357, 38], [334, 43, 357, 41, "box1"], [334, 47, 357, 45], [334, 48, 357, 46, "width"], [334, 53, 357, 51], [334, 56, 357, 52], [334, 57, 357, 53], [334, 59, 357, 55, "box2"], [334, 63, 357, 59], [334, 64, 357, 60, "xCenter"], [334, 71, 357, 67], [334, 74, 357, 70, "box2"], [334, 78, 357, 74], [334, 79, 357, 75, "width"], [334, 84, 357, 80], [334, 87, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [335, 6, 358, 4], [335, 12, 358, 10, "right"], [335, 17, 358, 15], [335, 20, 358, 18, "Math"], [335, 24, 358, 22], [335, 25, 358, 23, "max"], [335, 28, 358, 26], [335, 29, 358, 27, "box1"], [335, 33, 358, 31], [335, 34, 358, 32, "xCenter"], [335, 41, 358, 39], [335, 44, 358, 42, "box1"], [335, 48, 358, 46], [335, 49, 358, 47, "width"], [335, 54, 358, 52], [335, 57, 358, 53], [335, 58, 358, 54], [335, 60, 358, 56, "box2"], [335, 64, 358, 60], [335, 65, 358, 61, "xCenter"], [335, 72, 358, 68], [335, 75, 358, 71, "box2"], [335, 79, 358, 75], [335, 80, 358, 76, "width"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "top"], [336, 15, 359, 13], [336, 18, 359, 16, "Math"], [336, 22, 359, 20], [336, 23, 359, 21, "min"], [336, 26, 359, 24], [336, 27, 359, 25, "box1"], [336, 31, 359, 29], [336, 32, 359, 30, "yCenter"], [336, 39, 359, 37], [336, 42, 359, 40, "box1"], [336, 46, 359, 44], [336, 47, 359, 45, "height"], [336, 53, 359, 51], [336, 56, 359, 52], [336, 57, 359, 53], [336, 59, 359, 55, "box2"], [336, 63, 359, 59], [336, 64, 359, 60, "yCenter"], [336, 71, 359, 67], [336, 74, 359, 70, "box2"], [336, 78, 359, 74], [336, 79, 359, 75, "height"], [336, 85, 359, 81], [336, 88, 359, 82], [336, 89, 359, 83], [336, 90, 359, 84], [337, 6, 360, 4], [337, 12, 360, 10, "bottom"], [337, 18, 360, 16], [337, 21, 360, 19, "Math"], [337, 25, 360, 23], [337, 26, 360, 24, "max"], [337, 29, 360, 27], [337, 30, 360, 28, "box1"], [337, 34, 360, 32], [337, 35, 360, 33, "yCenter"], [337, 42, 360, 40], [337, 45, 360, 43, "box1"], [337, 49, 360, 47], [337, 50, 360, 48, "height"], [337, 56, 360, 54], [337, 59, 360, 55], [337, 60, 360, 56], [337, 62, 360, 58, "box2"], [337, 66, 360, 62], [337, 67, 360, 63, "yCenter"], [337, 74, 360, 70], [337, 77, 360, 73, "box2"], [337, 81, 360, 77], [337, 82, 360, 78, "height"], [337, 88, 360, 84], [337, 91, 360, 85], [337, 92, 360, 86], [337, 93, 360, 87], [338, 6, 362, 4], [338, 13, 362, 11], [339, 8, 363, 6, "boundingBox"], [339, 19, 363, 17], [339, 21, 363, 19], [340, 10, 364, 8, "xCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "left"], [340, 24, 364, 22], [340, 27, 364, 25, "right"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "yCenter"], [341, 17, 365, 15], [341, 19, 365, 17], [341, 20, 365, 18, "top"], [341, 23, 365, 21], [341, 26, 365, 24, "bottom"], [341, 32, 365, 30], [341, 36, 365, 34], [341, 37, 365, 35], [342, 10, 366, 8, "width"], [342, 15, 366, 13], [342, 17, 366, 15, "right"], [342, 22, 366, 20], [342, 25, 366, 23, "left"], [342, 29, 366, 27], [343, 10, 367, 8, "height"], [343, 16, 367, 14], [343, 18, 367, 16, "bottom"], [343, 24, 367, 22], [343, 27, 367, 25, "top"], [344, 8, 368, 6], [345, 6, 369, 4], [345, 7, 369, 5], [346, 4, 370, 2], [346, 5, 370, 3], [348, 4, 372, 2], [349, 4, 373, 2], [349, 10, 373, 8, "applyStrongBlur"], [349, 25, 373, 23], [349, 28, 373, 26, "applyStrongBlur"], [349, 29, 373, 27, "ctx"], [349, 32, 373, 56], [349, 34, 373, 58, "x"], [349, 35, 373, 67], [349, 37, 373, 69, "y"], [349, 38, 373, 78], [349, 40, 373, 80, "width"], [349, 45, 373, 93], [349, 47, 373, 95, "height"], [349, 53, 373, 109], [349, 58, 373, 114], [350, 6, 374, 4], [351, 6, 375, 4], [351, 12, 375, 10, "canvasWidth"], [351, 23, 375, 21], [351, 26, 375, 24, "ctx"], [351, 29, 375, 27], [351, 30, 375, 28, "canvas"], [351, 36, 375, 34], [351, 37, 375, 35, "width"], [351, 42, 375, 40], [352, 6, 376, 4], [352, 12, 376, 10, "canvasHeight"], [352, 24, 376, 22], [352, 27, 376, 25, "ctx"], [352, 30, 376, 28], [352, 31, 376, 29, "canvas"], [352, 37, 376, 35], [352, 38, 376, 36, "height"], [352, 44, 376, 42], [353, 6, 378, 4], [353, 12, 378, 10, "clampedX"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "x"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasWidth"], [353, 70, 378, 68], [353, 73, 378, 71], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [354, 6, 379, 4], [354, 12, 379, 10, "clampedY"], [354, 20, 379, 18], [354, 23, 379, 21, "Math"], [354, 27, 379, 25], [354, 28, 379, 26, "max"], [354, 31, 379, 29], [354, 32, 379, 30], [354, 33, 379, 31], [354, 35, 379, 33, "Math"], [354, 39, 379, 37], [354, 40, 379, 38, "min"], [354, 43, 379, 41], [354, 44, 379, 42, "Math"], [354, 48, 379, 46], [354, 49, 379, 47, "floor"], [354, 54, 379, 52], [354, 55, 379, 53, "y"], [354, 56, 379, 54], [354, 57, 379, 55], [354, 59, 379, 57, "canvasHeight"], [354, 71, 379, 69], [354, 74, 379, 72], [354, 75, 379, 73], [354, 76, 379, 74], [354, 77, 379, 75], [355, 6, 380, 4], [355, 12, 380, 10, "<PERSON><PERSON><PERSON><PERSON>"], [355, 24, 380, 22], [355, 27, 380, 25, "Math"], [355, 31, 380, 29], [355, 32, 380, 30, "min"], [355, 35, 380, 33], [355, 36, 380, 34, "Math"], [355, 40, 380, 38], [355, 41, 380, 39, "floor"], [355, 46, 380, 44], [355, 47, 380, 45, "width"], [355, 52, 380, 50], [355, 53, 380, 51], [355, 55, 380, 53, "canvasWidth"], [355, 66, 380, 64], [355, 69, 380, 67, "clampedX"], [355, 77, 380, 75], [355, 78, 380, 76], [356, 6, 381, 4], [356, 12, 381, 10, "clampedHeight"], [356, 25, 381, 23], [356, 28, 381, 26, "Math"], [356, 32, 381, 30], [356, 33, 381, 31, "min"], [356, 36, 381, 34], [356, 37, 381, 35, "Math"], [356, 41, 381, 39], [356, 42, 381, 40, "floor"], [356, 47, 381, 45], [356, 48, 381, 46, "height"], [356, 54, 381, 52], [356, 55, 381, 53], [356, 57, 381, 55, "canvasHeight"], [356, 69, 381, 67], [356, 72, 381, 70, "clampedY"], [356, 80, 381, 78], [356, 81, 381, 79], [357, 6, 383, 4], [357, 10, 383, 8, "<PERSON><PERSON><PERSON><PERSON>"], [357, 22, 383, 20], [357, 26, 383, 24], [357, 27, 383, 25], [357, 31, 383, 29, "clampedHeight"], [357, 44, 383, 42], [357, 48, 383, 46], [357, 49, 383, 47], [357, 51, 383, 49], [358, 8, 384, 6, "console"], [358, 15, 384, 13], [358, 16, 384, 14, "warn"], [358, 20, 384, 18], [358, 21, 384, 19], [358, 73, 384, 71], [358, 75, 384, 73], [359, 10, 385, 8, "original"], [359, 18, 385, 16], [359, 20, 385, 18], [360, 12, 385, 20, "x"], [360, 13, 385, 21], [361, 12, 385, 23, "y"], [361, 13, 385, 24], [362, 12, 385, 26, "width"], [362, 17, 385, 31], [363, 12, 385, 33, "height"], [364, 10, 385, 40], [364, 11, 385, 41], [365, 10, 386, 8, "canvas"], [365, 16, 386, 14], [365, 18, 386, 16], [366, 12, 386, 18, "width"], [366, 17, 386, 23], [366, 19, 386, 25, "canvasWidth"], [366, 30, 386, 36], [367, 12, 386, 38, "height"], [367, 18, 386, 44], [367, 20, 386, 46, "canvasHeight"], [368, 10, 386, 59], [368, 11, 386, 60], [369, 10, 387, 8, "clamped"], [369, 17, 387, 15], [369, 19, 387, 17], [370, 12, 387, 19, "x"], [370, 13, 387, 20], [370, 15, 387, 22, "clampedX"], [370, 23, 387, 30], [371, 12, 387, 32, "y"], [371, 13, 387, 33], [371, 15, 387, 35, "clampedY"], [371, 23, 387, 43], [372, 12, 387, 45, "width"], [372, 17, 387, 50], [372, 19, 387, 52, "<PERSON><PERSON><PERSON><PERSON>"], [372, 31, 387, 64], [373, 12, 387, 66, "height"], [373, 18, 387, 72], [373, 20, 387, 74, "clampedHeight"], [374, 10, 387, 88], [375, 8, 388, 6], [375, 9, 388, 7], [375, 10, 388, 8], [376, 8, 389, 6], [377, 6, 390, 4], [379, 6, 392, 4], [380, 6, 393, 4], [380, 12, 393, 10, "imageData"], [380, 21, 393, 19], [380, 24, 393, 22, "ctx"], [380, 27, 393, 25], [380, 28, 393, 26, "getImageData"], [380, 40, 393, 38], [380, 41, 393, 39, "clampedX"], [380, 49, 393, 47], [380, 51, 393, 49, "clampedY"], [380, 59, 393, 57], [380, 61, 393, 59, "<PERSON><PERSON><PERSON><PERSON>"], [380, 73, 393, 71], [380, 75, 393, 73, "clampedHeight"], [380, 88, 393, 86], [380, 89, 393, 87], [381, 6, 394, 4], [381, 12, 394, 10, "data"], [381, 16, 394, 14], [381, 19, 394, 17, "imageData"], [381, 28, 394, 26], [381, 29, 394, 27, "data"], [381, 33, 394, 31], [383, 6, 396, 4], [384, 6, 397, 4], [384, 12, 397, 10, "pixelSize"], [384, 21, 397, 19], [384, 24, 397, 22, "Math"], [384, 28, 397, 26], [384, 29, 397, 27, "max"], [384, 32, 397, 30], [384, 33, 397, 31], [384, 35, 397, 33], [384, 37, 397, 35, "Math"], [384, 41, 397, 39], [384, 42, 397, 40, "min"], [384, 45, 397, 43], [384, 46, 397, 44, "<PERSON><PERSON><PERSON><PERSON>"], [384, 58, 397, 56], [384, 60, 397, 58, "clampedHeight"], [384, 73, 397, 71], [384, 74, 397, 72], [384, 77, 397, 75], [384, 78, 397, 76], [384, 79, 397, 77], [385, 6, 399, 4], [385, 11, 399, 9], [385, 15, 399, 13, "py"], [385, 17, 399, 15], [385, 20, 399, 18], [385, 21, 399, 19], [385, 23, 399, 21, "py"], [385, 25, 399, 23], [385, 28, 399, 26, "clampedHeight"], [385, 41, 399, 39], [385, 43, 399, 41, "py"], [385, 45, 399, 43], [385, 49, 399, 47, "pixelSize"], [385, 58, 399, 56], [385, 60, 399, 58], [386, 8, 400, 6], [386, 13, 400, 11], [386, 17, 400, 15, "px"], [386, 19, 400, 17], [386, 22, 400, 20], [386, 23, 400, 21], [386, 25, 400, 23, "px"], [386, 27, 400, 25], [386, 30, 400, 28, "<PERSON><PERSON><PERSON><PERSON>"], [386, 42, 400, 40], [386, 44, 400, 42, "px"], [386, 46, 400, 44], [386, 50, 400, 48, "pixelSize"], [386, 59, 400, 57], [386, 61, 400, 59], [387, 10, 401, 8], [388, 10, 402, 8], [388, 14, 402, 12, "r"], [388, 15, 402, 13], [388, 18, 402, 16], [388, 19, 402, 17], [389, 12, 402, 19, "g"], [389, 13, 402, 20], [389, 16, 402, 23], [389, 17, 402, 24], [390, 12, 402, 26, "b"], [390, 13, 402, 27], [390, 16, 402, 30], [390, 17, 402, 31], [391, 12, 402, 33, "count"], [391, 17, 402, 38], [391, 20, 402, 41], [391, 21, 402, 42], [392, 10, 404, 8], [392, 15, 404, 13], [392, 19, 404, 17, "dy"], [392, 21, 404, 19], [392, 24, 404, 22], [392, 25, 404, 23], [392, 27, 404, 25, "dy"], [392, 29, 404, 27], [392, 32, 404, 30, "pixelSize"], [392, 41, 404, 39], [392, 45, 404, 43, "py"], [392, 47, 404, 45], [392, 50, 404, 48, "dy"], [392, 52, 404, 50], [392, 55, 404, 53, "clampedHeight"], [392, 68, 404, 66], [392, 70, 404, 68, "dy"], [392, 72, 404, 70], [392, 74, 404, 72], [392, 76, 404, 74], [393, 12, 405, 10], [393, 17, 405, 15], [393, 21, 405, 19, "dx"], [393, 23, 405, 21], [393, 26, 405, 24], [393, 27, 405, 25], [393, 29, 405, 27, "dx"], [393, 31, 405, 29], [393, 34, 405, 32, "pixelSize"], [393, 43, 405, 41], [393, 47, 405, 45, "px"], [393, 49, 405, 47], [393, 52, 405, 50, "dx"], [393, 54, 405, 52], [393, 57, 405, 55, "<PERSON><PERSON><PERSON><PERSON>"], [393, 69, 405, 67], [393, 71, 405, 69, "dx"], [393, 73, 405, 71], [393, 75, 405, 73], [393, 77, 405, 75], [394, 14, 406, 12], [394, 20, 406, 18, "index"], [394, 25, 406, 23], [394, 28, 406, 26], [394, 29, 406, 27], [394, 30, 406, 28, "py"], [394, 32, 406, 30], [394, 35, 406, 33, "dy"], [394, 37, 406, 35], [394, 41, 406, 39, "<PERSON><PERSON><PERSON><PERSON>"], [394, 53, 406, 51], [394, 57, 406, 55, "px"], [394, 59, 406, 57], [394, 62, 406, 60, "dx"], [394, 64, 406, 62], [394, 65, 406, 63], [394, 69, 406, 67], [394, 70, 406, 68], [395, 14, 407, 12, "r"], [395, 15, 407, 13], [395, 19, 407, 17, "data"], [395, 23, 407, 21], [395, 24, 407, 22, "index"], [395, 29, 407, 27], [395, 30, 407, 28], [396, 14, 408, 12, "g"], [396, 15, 408, 13], [396, 19, 408, 17, "data"], [396, 23, 408, 21], [396, 24, 408, 22, "index"], [396, 29, 408, 27], [396, 32, 408, 30], [396, 33, 408, 31], [396, 34, 408, 32], [397, 14, 409, 12, "b"], [397, 15, 409, 13], [397, 19, 409, 17, "data"], [397, 23, 409, 21], [397, 24, 409, 22, "index"], [397, 29, 409, 27], [397, 32, 409, 30], [397, 33, 409, 31], [397, 34, 409, 32], [398, 14, 410, 12, "count"], [398, 19, 410, 17], [398, 21, 410, 19], [399, 12, 411, 10], [400, 10, 412, 8], [401, 10, 414, 8], [401, 14, 414, 12, "count"], [401, 19, 414, 17], [401, 22, 414, 20], [401, 23, 414, 21], [401, 25, 414, 23], [402, 12, 415, 10, "r"], [402, 13, 415, 11], [402, 16, 415, 14, "Math"], [402, 20, 415, 18], [402, 21, 415, 19, "floor"], [402, 26, 415, 24], [402, 27, 415, 25, "r"], [402, 28, 415, 26], [402, 31, 415, 29, "count"], [402, 36, 415, 34], [402, 37, 415, 35], [403, 12, 416, 10, "g"], [403, 13, 416, 11], [403, 16, 416, 14, "Math"], [403, 20, 416, 18], [403, 21, 416, 19, "floor"], [403, 26, 416, 24], [403, 27, 416, 25, "g"], [403, 28, 416, 26], [403, 31, 416, 29, "count"], [403, 36, 416, 34], [403, 37, 416, 35], [404, 12, 417, 10, "b"], [404, 13, 417, 11], [404, 16, 417, 14, "Math"], [404, 20, 417, 18], [404, 21, 417, 19, "floor"], [404, 26, 417, 24], [404, 27, 417, 25, "b"], [404, 28, 417, 26], [404, 31, 417, 29, "count"], [404, 36, 417, 34], [404, 37, 417, 35], [406, 12, 419, 10], [407, 12, 420, 10], [407, 17, 420, 15], [407, 21, 420, 19, "dy"], [407, 23, 420, 21], [407, 26, 420, 24], [407, 27, 420, 25], [407, 29, 420, 27, "dy"], [407, 31, 420, 29], [407, 34, 420, 32, "pixelSize"], [407, 43, 420, 41], [407, 47, 420, 45, "py"], [407, 49, 420, 47], [407, 52, 420, 50, "dy"], [407, 54, 420, 52], [407, 57, 420, 55, "clampedHeight"], [407, 70, 420, 68], [407, 72, 420, 70, "dy"], [407, 74, 420, 72], [407, 76, 420, 74], [407, 78, 420, 76], [408, 14, 421, 12], [408, 19, 421, 17], [408, 23, 421, 21, "dx"], [408, 25, 421, 23], [408, 28, 421, 26], [408, 29, 421, 27], [408, 31, 421, 29, "dx"], [408, 33, 421, 31], [408, 36, 421, 34, "pixelSize"], [408, 45, 421, 43], [408, 49, 421, 47, "px"], [408, 51, 421, 49], [408, 54, 421, 52, "dx"], [408, 56, 421, 54], [408, 59, 421, 57, "<PERSON><PERSON><PERSON><PERSON>"], [408, 71, 421, 69], [408, 73, 421, 71, "dx"], [408, 75, 421, 73], [408, 77, 421, 75], [408, 79, 421, 77], [409, 16, 422, 14], [409, 22, 422, 20, "index"], [409, 27, 422, 25], [409, 30, 422, 28], [409, 31, 422, 29], [409, 32, 422, 30, "py"], [409, 34, 422, 32], [409, 37, 422, 35, "dy"], [409, 39, 422, 37], [409, 43, 422, 41, "<PERSON><PERSON><PERSON><PERSON>"], [409, 55, 422, 53], [409, 59, 422, 57, "px"], [409, 61, 422, 59], [409, 64, 422, 62, "dx"], [409, 66, 422, 64], [409, 67, 422, 65], [409, 71, 422, 69], [409, 72, 422, 70], [410, 16, 423, 14, "data"], [410, 20, 423, 18], [410, 21, 423, 19, "index"], [410, 26, 423, 24], [410, 27, 423, 25], [410, 30, 423, 28, "r"], [410, 31, 423, 29], [411, 16, 424, 14, "data"], [411, 20, 424, 18], [411, 21, 424, 19, "index"], [411, 26, 424, 24], [411, 29, 424, 27], [411, 30, 424, 28], [411, 31, 424, 29], [411, 34, 424, 32, "g"], [411, 35, 424, 33], [412, 16, 425, 14, "data"], [412, 20, 425, 18], [412, 21, 425, 19, "index"], [412, 26, 425, 24], [412, 29, 425, 27], [412, 30, 425, 28], [412, 31, 425, 29], [412, 34, 425, 32, "b"], [412, 35, 425, 33], [413, 16, 426, 14], [414, 14, 427, 12], [415, 12, 428, 10], [416, 10, 429, 8], [417, 8, 430, 6], [418, 6, 431, 4], [420, 6, 433, 4], [421, 6, 434, 4], [421, 11, 434, 9], [421, 15, 434, 13, "i"], [421, 16, 434, 14], [421, 19, 434, 17], [421, 20, 434, 18], [421, 22, 434, 20, "i"], [421, 23, 434, 21], [421, 26, 434, 24], [421, 27, 434, 25], [421, 29, 434, 27, "i"], [421, 30, 434, 28], [421, 32, 434, 30], [421, 34, 434, 32], [422, 8, 435, 6, "applySimpleBlur"], [422, 23, 435, 21], [422, 24, 435, 22, "data"], [422, 28, 435, 26], [422, 30, 435, 28, "<PERSON><PERSON><PERSON><PERSON>"], [422, 42, 435, 40], [422, 44, 435, 42, "clampedHeight"], [422, 57, 435, 55], [422, 58, 435, 56], [423, 6, 436, 4], [425, 6, 438, 4], [426, 6, 439, 4, "ctx"], [426, 9, 439, 7], [426, 10, 439, 8, "putImageData"], [426, 22, 439, 20], [426, 23, 439, 21, "imageData"], [426, 32, 439, 30], [426, 34, 439, 32, "clampedX"], [426, 42, 439, 40], [426, 44, 439, 42, "clampedY"], [426, 52, 439, 50], [426, 53, 439, 51], [428, 6, 441, 4], [429, 6, 442, 4, "ctx"], [429, 9, 442, 7], [429, 10, 442, 8, "fillStyle"], [429, 19, 442, 17], [429, 22, 442, 20], [429, 48, 442, 46], [430, 6, 443, 4, "ctx"], [430, 9, 443, 7], [430, 10, 443, 8, "fillRect"], [430, 18, 443, 16], [430, 19, 443, 17, "clampedX"], [430, 27, 443, 25], [430, 29, 443, 27, "clampedY"], [430, 37, 443, 35], [430, 39, 443, 37, "<PERSON><PERSON><PERSON><PERSON>"], [430, 51, 443, 49], [430, 53, 443, 51, "clampedHeight"], [430, 66, 443, 64], [430, 67, 443, 65], [431, 4, 444, 2], [431, 5, 444, 3], [432, 4, 446, 2], [432, 10, 446, 8, "applySimpleBlur"], [432, 25, 446, 23], [432, 28, 446, 26, "applySimpleBlur"], [432, 29, 446, 27, "data"], [432, 33, 446, 50], [432, 35, 446, 52, "width"], [432, 40, 446, 65], [432, 42, 446, 67, "height"], [432, 48, 446, 81], [432, 53, 446, 86], [433, 6, 447, 4], [433, 12, 447, 10, "original"], [433, 20, 447, 18], [433, 23, 447, 21], [433, 27, 447, 25, "Uint8ClampedArray"], [433, 44, 447, 42], [433, 45, 447, 43, "data"], [433, 49, 447, 47], [433, 50, 447, 48], [434, 6, 449, 4], [434, 11, 449, 9], [434, 15, 449, 13, "y"], [434, 16, 449, 14], [434, 19, 449, 17], [434, 20, 449, 18], [434, 22, 449, 20, "y"], [434, 23, 449, 21], [434, 26, 449, 24, "height"], [434, 32, 449, 30], [434, 35, 449, 33], [434, 36, 449, 34], [434, 38, 449, 36, "y"], [434, 39, 449, 37], [434, 41, 449, 39], [434, 43, 449, 41], [435, 8, 450, 6], [435, 13, 450, 11], [435, 17, 450, 15, "x"], [435, 18, 450, 16], [435, 21, 450, 19], [435, 22, 450, 20], [435, 24, 450, 22, "x"], [435, 25, 450, 23], [435, 28, 450, 26, "width"], [435, 33, 450, 31], [435, 36, 450, 34], [435, 37, 450, 35], [435, 39, 450, 37, "x"], [435, 40, 450, 38], [435, 42, 450, 40], [435, 44, 450, 42], [436, 10, 451, 8], [436, 16, 451, 14, "index"], [436, 21, 451, 19], [436, 24, 451, 22], [436, 25, 451, 23, "y"], [436, 26, 451, 24], [436, 29, 451, 27, "width"], [436, 34, 451, 32], [436, 37, 451, 35, "x"], [436, 38, 451, 36], [436, 42, 451, 40], [436, 43, 451, 41], [438, 10, 453, 8], [439, 10, 454, 8], [439, 14, 454, 12, "r"], [439, 15, 454, 13], [439, 18, 454, 16], [439, 19, 454, 17], [440, 12, 454, 19, "g"], [440, 13, 454, 20], [440, 16, 454, 23], [440, 17, 454, 24], [441, 12, 454, 26, "b"], [441, 13, 454, 27], [441, 16, 454, 30], [441, 17, 454, 31], [442, 10, 455, 8], [442, 15, 455, 13], [442, 19, 455, 17, "dy"], [442, 21, 455, 19], [442, 24, 455, 22], [442, 25, 455, 23], [442, 26, 455, 24], [442, 28, 455, 26, "dy"], [442, 30, 455, 28], [442, 34, 455, 32], [442, 35, 455, 33], [442, 37, 455, 35, "dy"], [442, 39, 455, 37], [442, 41, 455, 39], [442, 43, 455, 41], [443, 12, 456, 10], [443, 17, 456, 15], [443, 21, 456, 19, "dx"], [443, 23, 456, 21], [443, 26, 456, 24], [443, 27, 456, 25], [443, 28, 456, 26], [443, 30, 456, 28, "dx"], [443, 32, 456, 30], [443, 36, 456, 34], [443, 37, 456, 35], [443, 39, 456, 37, "dx"], [443, 41, 456, 39], [443, 43, 456, 41], [443, 45, 456, 43], [444, 14, 457, 12], [444, 20, 457, 18, "neighborIndex"], [444, 33, 457, 31], [444, 36, 457, 34], [444, 37, 457, 35], [444, 38, 457, 36, "y"], [444, 39, 457, 37], [444, 42, 457, 40, "dy"], [444, 44, 457, 42], [444, 48, 457, 46, "width"], [444, 53, 457, 51], [444, 57, 457, 55, "x"], [444, 58, 457, 56], [444, 61, 457, 59, "dx"], [444, 63, 457, 61], [444, 64, 457, 62], [444, 68, 457, 66], [444, 69, 457, 67], [445, 14, 458, 12, "r"], [445, 15, 458, 13], [445, 19, 458, 17, "original"], [445, 27, 458, 25], [445, 28, 458, 26, "neighborIndex"], [445, 41, 458, 39], [445, 42, 458, 40], [446, 14, 459, 12, "g"], [446, 15, 459, 13], [446, 19, 459, 17, "original"], [446, 27, 459, 25], [446, 28, 459, 26, "neighborIndex"], [446, 41, 459, 39], [446, 44, 459, 42], [446, 45, 459, 43], [446, 46, 459, 44], [447, 14, 460, 12, "b"], [447, 15, 460, 13], [447, 19, 460, 17, "original"], [447, 27, 460, 25], [447, 28, 460, 26, "neighborIndex"], [447, 41, 460, 39], [447, 44, 460, 42], [447, 45, 460, 43], [447, 46, 460, 44], [448, 12, 461, 10], [449, 10, 462, 8], [450, 10, 464, 8, "data"], [450, 14, 464, 12], [450, 15, 464, 13, "index"], [450, 20, 464, 18], [450, 21, 464, 19], [450, 24, 464, 22, "r"], [450, 25, 464, 23], [450, 28, 464, 26], [450, 29, 464, 27], [451, 10, 465, 8, "data"], [451, 14, 465, 12], [451, 15, 465, 13, "index"], [451, 20, 465, 18], [451, 23, 465, 21], [451, 24, 465, 22], [451, 25, 465, 23], [451, 28, 465, 26, "g"], [451, 29, 465, 27], [451, 32, 465, 30], [451, 33, 465, 31], [452, 10, 466, 8, "data"], [452, 14, 466, 12], [452, 15, 466, 13, "index"], [452, 20, 466, 18], [452, 23, 466, 21], [452, 24, 466, 22], [452, 25, 466, 23], [452, 28, 466, 26, "b"], [452, 29, 466, 27], [452, 32, 466, 30], [452, 33, 466, 31], [453, 8, 467, 6], [454, 6, 468, 4], [455, 4, 469, 2], [455, 5, 469, 3], [456, 4, 471, 2], [456, 10, 471, 8, "applyFallbackFaceBlur"], [456, 31, 471, 29], [456, 34, 471, 32, "applyFallbackFaceBlur"], [456, 35, 471, 33, "ctx"], [456, 38, 471, 62], [456, 40, 471, 64, "imgWidth"], [456, 48, 471, 80], [456, 50, 471, 82, "imgHeight"], [456, 59, 471, 99], [456, 64, 471, 104], [457, 6, 472, 4, "console"], [457, 13, 472, 11], [457, 14, 472, 12, "log"], [457, 17, 472, 15], [457, 18, 472, 16], [457, 90, 472, 88], [457, 91, 472, 89], [459, 6, 474, 4], [460, 6, 475, 4], [460, 12, 475, 10, "areas"], [460, 17, 475, 15], [460, 20, 475, 18], [461, 6, 476, 6], [462, 6, 477, 6], [463, 8, 477, 8, "x"], [463, 9, 477, 9], [463, 11, 477, 11, "imgWidth"], [463, 19, 477, 19], [463, 22, 477, 22], [463, 26, 477, 26], [464, 8, 477, 28, "y"], [464, 9, 477, 29], [464, 11, 477, 31, "imgHeight"], [464, 20, 477, 40], [464, 23, 477, 43], [464, 27, 477, 47], [465, 8, 477, 49, "w"], [465, 9, 477, 50], [465, 11, 477, 52, "imgWidth"], [465, 19, 477, 60], [465, 22, 477, 63], [465, 25, 477, 66], [466, 8, 477, 68, "h"], [466, 9, 477, 69], [466, 11, 477, 71, "imgHeight"], [466, 20, 477, 80], [466, 23, 477, 83], [467, 6, 477, 87], [467, 7, 477, 88], [468, 6, 478, 6], [469, 6, 479, 6], [470, 8, 479, 8, "x"], [470, 9, 479, 9], [470, 11, 479, 11, "imgWidth"], [470, 19, 479, 19], [470, 22, 479, 22], [470, 25, 479, 25], [471, 8, 479, 27, "y"], [471, 9, 479, 28], [471, 11, 479, 30, "imgHeight"], [471, 20, 479, 39], [471, 23, 479, 42], [471, 26, 479, 45], [472, 8, 479, 47, "w"], [472, 9, 479, 48], [472, 11, 479, 50, "imgWidth"], [472, 19, 479, 58], [472, 22, 479, 61], [472, 26, 479, 65], [473, 8, 479, 67, "h"], [473, 9, 479, 68], [473, 11, 479, 70, "imgHeight"], [473, 20, 479, 79], [473, 23, 479, 82], [474, 6, 479, 86], [474, 7, 479, 87], [475, 6, 480, 6], [476, 6, 481, 6], [477, 8, 481, 8, "x"], [477, 9, 481, 9], [477, 11, 481, 11, "imgWidth"], [477, 19, 481, 19], [477, 22, 481, 22], [477, 26, 481, 26], [478, 8, 481, 28, "y"], [478, 9, 481, 29], [478, 11, 481, 31, "imgHeight"], [478, 20, 481, 40], [478, 23, 481, 43], [478, 26, 481, 46], [479, 8, 481, 48, "w"], [479, 9, 481, 49], [479, 11, 481, 51, "imgWidth"], [479, 19, 481, 59], [479, 22, 481, 62], [479, 26, 481, 66], [480, 8, 481, 68, "h"], [480, 9, 481, 69], [480, 11, 481, 71, "imgHeight"], [480, 20, 481, 80], [480, 23, 481, 83], [481, 6, 481, 87], [481, 7, 481, 88], [481, 8, 482, 5], [482, 6, 484, 4, "areas"], [482, 11, 484, 9], [482, 12, 484, 10, "for<PERSON>ach"], [482, 19, 484, 17], [482, 20, 484, 18], [482, 21, 484, 19, "area"], [482, 25, 484, 23], [482, 27, 484, 25, "index"], [482, 32, 484, 30], [482, 37, 484, 35], [483, 8, 485, 6, "console"], [483, 15, 485, 13], [483, 16, 485, 14, "log"], [483, 19, 485, 17], [483, 20, 485, 18], [483, 65, 485, 63, "index"], [483, 70, 485, 68], [483, 73, 485, 71], [483, 74, 485, 72], [483, 77, 485, 75], [483, 79, 485, 77, "area"], [483, 83, 485, 81], [483, 84, 485, 82], [484, 8, 486, 6, "applyStrongBlur"], [484, 23, 486, 21], [484, 24, 486, 22, "ctx"], [484, 27, 486, 25], [484, 29, 486, 27, "area"], [484, 33, 486, 31], [484, 34, 486, 32, "x"], [484, 35, 486, 33], [484, 37, 486, 35, "area"], [484, 41, 486, 39], [484, 42, 486, 40, "y"], [484, 43, 486, 41], [484, 45, 486, 43, "area"], [484, 49, 486, 47], [484, 50, 486, 48, "w"], [484, 51, 486, 49], [484, 53, 486, 51, "area"], [484, 57, 486, 55], [484, 58, 486, 56, "h"], [484, 59, 486, 57], [484, 60, 486, 58], [485, 6, 487, 4], [485, 7, 487, 5], [485, 8, 487, 6], [486, 4, 488, 2], [486, 5, 488, 3], [488, 4, 490, 2], [489, 4, 491, 2], [489, 10, 491, 8, "capturePhoto"], [489, 22, 491, 20], [489, 25, 491, 23], [489, 29, 491, 23, "useCallback"], [489, 47, 491, 34], [489, 49, 491, 35], [489, 61, 491, 47], [490, 6, 492, 4], [491, 6, 493, 4], [491, 12, 493, 10, "isDev"], [491, 17, 493, 15], [491, 20, 493, 18, "process"], [491, 27, 493, 25], [491, 28, 493, 26, "env"], [491, 31, 493, 29], [491, 32, 493, 30, "NODE_ENV"], [491, 40, 493, 38], [491, 45, 493, 43], [491, 58, 493, 56], [491, 62, 493, 60, "__DEV__"], [491, 69, 493, 67], [492, 6, 495, 4], [492, 10, 495, 8], [492, 11, 495, 9, "cameraRef"], [492, 20, 495, 18], [492, 21, 495, 19, "current"], [492, 28, 495, 26], [492, 32, 495, 30], [492, 33, 495, 31, "isDev"], [492, 38, 495, 36], [492, 40, 495, 38], [493, 8, 496, 6, "<PERSON><PERSON>"], [493, 22, 496, 11], [493, 23, 496, 12, "alert"], [493, 28, 496, 17], [493, 29, 496, 18], [493, 36, 496, 25], [493, 38, 496, 27], [493, 56, 496, 45], [493, 57, 496, 46], [494, 8, 497, 6], [495, 6, 498, 4], [496, 6, 499, 4], [496, 10, 499, 8], [497, 8, 500, 6, "setProcessingState"], [497, 26, 500, 24], [497, 27, 500, 25], [497, 38, 500, 36], [497, 39, 500, 37], [498, 8, 501, 6, "setProcessingProgress"], [498, 29, 501, 27], [498, 30, 501, 28], [498, 32, 501, 30], [498, 33, 501, 31], [499, 8, 502, 6], [500, 8, 503, 6], [501, 8, 504, 6], [502, 8, 505, 6], [502, 14, 505, 12], [502, 18, 505, 16, "Promise"], [502, 25, 505, 23], [502, 26, 505, 24, "resolve"], [502, 33, 505, 31], [502, 37, 505, 35, "setTimeout"], [502, 47, 505, 45], [502, 48, 505, 46, "resolve"], [502, 55, 505, 53], [502, 57, 505, 55], [502, 59, 505, 57], [502, 60, 505, 58], [502, 61, 505, 59], [503, 8, 506, 6], [504, 8, 507, 6], [504, 12, 507, 10, "photo"], [504, 17, 507, 15], [505, 8, 509, 6], [505, 12, 509, 10], [506, 10, 510, 8, "photo"], [506, 15, 510, 13], [506, 18, 510, 16], [506, 24, 510, 22, "cameraRef"], [506, 33, 510, 31], [506, 34, 510, 32, "current"], [506, 41, 510, 39], [506, 42, 510, 40, "takePictureAsync"], [506, 58, 510, 56], [506, 59, 510, 57], [507, 12, 511, 10, "quality"], [507, 19, 511, 17], [507, 21, 511, 19], [507, 24, 511, 22], [508, 12, 512, 10, "base64"], [508, 18, 512, 16], [508, 20, 512, 18], [508, 25, 512, 23], [509, 12, 513, 10, "skipProcessing"], [509, 26, 513, 24], [509, 28, 513, 26], [509, 32, 513, 30], [509, 33, 513, 32], [510, 10, 514, 8], [510, 11, 514, 9], [510, 12, 514, 10], [511, 8, 515, 6], [511, 9, 515, 7], [511, 10, 515, 8], [511, 17, 515, 15, "cameraError"], [511, 28, 515, 26], [511, 30, 515, 28], [512, 10, 516, 8, "console"], [512, 17, 516, 15], [512, 18, 516, 16, "log"], [512, 21, 516, 19], [512, 22, 516, 20], [512, 82, 516, 80], [512, 84, 516, 82, "cameraError"], [512, 95, 516, 93], [512, 96, 516, 94], [513, 10, 517, 8], [514, 10, 518, 8], [514, 14, 518, 12, "isDev"], [514, 19, 518, 17], [514, 21, 518, 19], [515, 12, 519, 10, "photo"], [515, 17, 519, 15], [515, 20, 519, 18], [516, 14, 520, 12, "uri"], [516, 17, 520, 15], [516, 19, 520, 17], [517, 12, 521, 10], [517, 13, 521, 11], [518, 10, 522, 8], [518, 11, 522, 9], [518, 17, 522, 15], [519, 12, 523, 10], [519, 18, 523, 16, "cameraError"], [519, 29, 523, 27], [520, 10, 524, 8], [521, 8, 525, 6], [522, 8, 526, 6], [522, 12, 526, 10], [522, 13, 526, 11, "photo"], [522, 18, 526, 16], [522, 20, 526, 18], [523, 10, 527, 8], [523, 16, 527, 14], [523, 20, 527, 18, "Error"], [523, 25, 527, 23], [523, 26, 527, 24], [523, 51, 527, 49], [523, 52, 527, 50], [524, 8, 528, 6], [525, 8, 529, 6, "console"], [525, 15, 529, 13], [525, 16, 529, 14, "log"], [525, 19, 529, 17], [525, 20, 529, 18], [525, 56, 529, 54], [525, 58, 529, 56, "photo"], [525, 63, 529, 61], [525, 64, 529, 62, "uri"], [525, 67, 529, 65], [525, 68, 529, 66], [526, 8, 530, 6, "setCapturedPhoto"], [526, 24, 530, 22], [526, 25, 530, 23, "photo"], [526, 30, 530, 28], [526, 31, 530, 29, "uri"], [526, 34, 530, 32], [526, 35, 530, 33], [527, 8, 531, 6, "setProcessingProgress"], [527, 29, 531, 27], [527, 30, 531, 28], [527, 32, 531, 30], [527, 33, 531, 31], [528, 8, 532, 6], [529, 8, 533, 6, "console"], [529, 15, 533, 13], [529, 16, 533, 14, "log"], [529, 19, 533, 17], [529, 20, 533, 18], [529, 73, 533, 71], [529, 74, 533, 72], [530, 8, 534, 6, "console"], [530, 15, 534, 13], [530, 16, 534, 14, "log"], [530, 19, 534, 17], [530, 20, 534, 18], [530, 96, 534, 94], [530, 98, 534, 96, "photo"], [530, 103, 534, 101], [530, 104, 534, 102, "uri"], [530, 107, 534, 105], [530, 108, 534, 106], [531, 8, 535, 6, "console"], [531, 15, 535, 13], [531, 16, 535, 14, "log"], [531, 19, 535, 17], [531, 20, 535, 18], [531, 68, 535, 66], [531, 70, 535, 68], [531, 77, 535, 75, "processImageWithFaceBlur"], [531, 101, 535, 99], [531, 102, 535, 100], [532, 8, 536, 6], [532, 14, 536, 12, "processImageWithFaceBlur"], [532, 38, 536, 36], [532, 39, 536, 37, "photo"], [532, 44, 536, 42], [532, 45, 536, 43, "uri"], [532, 48, 536, 46], [532, 49, 536, 47], [533, 8, 537, 6, "console"], [533, 15, 537, 13], [533, 16, 537, 14, "log"], [533, 19, 537, 17], [533, 20, 537, 18], [533, 71, 537, 69], [533, 72, 537, 70], [534, 6, 538, 4], [534, 7, 538, 5], [534, 8, 538, 6], [534, 15, 538, 13, "error"], [534, 20, 538, 18], [534, 22, 538, 20], [535, 8, 539, 6, "console"], [535, 15, 539, 13], [535, 16, 539, 14, "error"], [535, 21, 539, 19], [535, 22, 539, 20], [535, 54, 539, 52], [535, 56, 539, 54, "error"], [535, 61, 539, 59], [535, 62, 539, 60], [536, 8, 540, 6, "setErrorMessage"], [536, 23, 540, 21], [536, 24, 540, 22], [536, 68, 540, 66], [536, 69, 540, 67], [537, 8, 541, 6, "setProcessingState"], [537, 26, 541, 24], [537, 27, 541, 25], [537, 34, 541, 32], [537, 35, 541, 33], [538, 6, 542, 4], [539, 4, 543, 2], [539, 5, 543, 3], [539, 7, 543, 5], [539, 9, 543, 7], [539, 10, 543, 8], [540, 4, 544, 2], [541, 4, 545, 2], [541, 10, 545, 8, "processImageWithFaceBlur"], [541, 34, 545, 32], [541, 37, 545, 35], [541, 43, 545, 42, "photoUri"], [541, 51, 545, 58], [541, 55, 545, 63], [542, 6, 546, 4, "console"], [542, 13, 546, 11], [542, 14, 546, 12, "log"], [542, 17, 546, 15], [542, 18, 546, 16], [542, 85, 546, 83], [542, 86, 546, 84], [543, 6, 547, 4], [543, 10, 547, 8], [544, 8, 548, 6, "console"], [544, 15, 548, 13], [544, 16, 548, 14, "log"], [544, 19, 548, 17], [544, 20, 548, 18], [544, 84, 548, 82], [544, 85, 548, 83], [545, 8, 549, 6, "setProcessingState"], [545, 26, 549, 24], [545, 27, 549, 25], [545, 39, 549, 37], [545, 40, 549, 38], [546, 8, 550, 6, "setProcessingProgress"], [546, 29, 550, 27], [546, 30, 550, 28], [546, 32, 550, 30], [546, 33, 550, 31], [548, 8, 552, 6], [549, 8, 553, 6], [549, 14, 553, 12, "canvas"], [549, 20, 553, 18], [549, 23, 553, 21, "document"], [549, 31, 553, 29], [549, 32, 553, 30, "createElement"], [549, 45, 553, 43], [549, 46, 553, 44], [549, 54, 553, 52], [549, 55, 553, 53], [550, 8, 554, 6], [550, 14, 554, 12, "ctx"], [550, 17, 554, 15], [550, 20, 554, 18, "canvas"], [550, 26, 554, 24], [550, 27, 554, 25, "getContext"], [550, 37, 554, 35], [550, 38, 554, 36], [550, 42, 554, 40], [550, 43, 554, 41], [551, 8, 555, 6], [551, 12, 555, 10], [551, 13, 555, 11, "ctx"], [551, 16, 555, 14], [551, 18, 555, 16], [551, 24, 555, 22], [551, 28, 555, 26, "Error"], [551, 33, 555, 31], [551, 34, 555, 32], [551, 64, 555, 62], [551, 65, 555, 63], [553, 8, 557, 6], [554, 8, 558, 6], [554, 14, 558, 12, "img"], [554, 17, 558, 15], [554, 20, 558, 18], [554, 24, 558, 22, "Image"], [554, 29, 558, 27], [554, 30, 558, 28], [554, 31, 558, 29], [555, 8, 559, 6], [555, 14, 559, 12], [555, 18, 559, 16, "Promise"], [555, 25, 559, 23], [555, 26, 559, 24], [555, 27, 559, 25, "resolve"], [555, 34, 559, 32], [555, 36, 559, 34, "reject"], [555, 42, 559, 40], [555, 47, 559, 45], [556, 10, 560, 8, "img"], [556, 13, 560, 11], [556, 14, 560, 12, "onload"], [556, 20, 560, 18], [556, 23, 560, 21, "resolve"], [556, 30, 560, 28], [557, 10, 561, 8, "img"], [557, 13, 561, 11], [557, 14, 561, 12, "onerror"], [557, 21, 561, 19], [557, 24, 561, 22, "reject"], [557, 30, 561, 28], [558, 10, 562, 8, "img"], [558, 13, 562, 11], [558, 14, 562, 12, "src"], [558, 17, 562, 15], [558, 20, 562, 18, "photoUri"], [558, 28, 562, 26], [559, 8, 563, 6], [559, 9, 563, 7], [559, 10, 563, 8], [561, 8, 565, 6], [562, 8, 566, 6, "canvas"], [562, 14, 566, 12], [562, 15, 566, 13, "width"], [562, 20, 566, 18], [562, 23, 566, 21, "img"], [562, 26, 566, 24], [562, 27, 566, 25, "width"], [562, 32, 566, 30], [563, 8, 567, 6, "canvas"], [563, 14, 567, 12], [563, 15, 567, 13, "height"], [563, 21, 567, 19], [563, 24, 567, 22, "img"], [563, 27, 567, 25], [563, 28, 567, 26, "height"], [563, 34, 567, 32], [564, 8, 568, 6, "console"], [564, 15, 568, 13], [564, 16, 568, 14, "log"], [564, 19, 568, 17], [564, 20, 568, 18], [564, 54, 568, 52], [564, 56, 568, 54], [565, 10, 568, 56, "width"], [565, 15, 568, 61], [565, 17, 568, 63, "img"], [565, 20, 568, 66], [565, 21, 568, 67, "width"], [565, 26, 568, 72], [566, 10, 568, 74, "height"], [566, 16, 568, 80], [566, 18, 568, 82, "img"], [566, 21, 568, 85], [566, 22, 568, 86, "height"], [567, 8, 568, 93], [567, 9, 568, 94], [567, 10, 568, 95], [569, 8, 570, 6], [570, 8, 571, 6, "ctx"], [570, 11, 571, 9], [570, 12, 571, 10, "drawImage"], [570, 21, 571, 19], [570, 22, 571, 20, "img"], [570, 25, 571, 23], [570, 27, 571, 25], [570, 28, 571, 26], [570, 30, 571, 28], [570, 31, 571, 29], [570, 32, 571, 30], [571, 8, 572, 6, "console"], [571, 15, 572, 13], [571, 16, 572, 14, "log"], [571, 19, 572, 17], [571, 20, 572, 18], [571, 72, 572, 70], [571, 73, 572, 71], [572, 8, 574, 6, "setProcessingProgress"], [572, 29, 574, 27], [572, 30, 574, 28], [572, 32, 574, 30], [572, 33, 574, 31], [574, 8, 576, 6], [575, 8, 577, 6], [575, 12, 577, 10, "detectedFaces"], [575, 25, 577, 23], [575, 28, 577, 26], [575, 30, 577, 28], [576, 8, 579, 6, "console"], [576, 15, 579, 13], [576, 16, 579, 14, "log"], [576, 19, 579, 17], [576, 20, 579, 18], [576, 81, 579, 79], [576, 82, 579, 80], [578, 8, 581, 6], [579, 8, 582, 6], [579, 12, 582, 10], [580, 10, 583, 8, "console"], [580, 17, 583, 15], [580, 18, 583, 16, "log"], [580, 21, 583, 19], [580, 22, 583, 20], [580, 81, 583, 79], [580, 82, 583, 80], [581, 10, 584, 8], [581, 16, 584, 14, "loadTensorFlowFaceDetection"], [581, 43, 584, 41], [581, 44, 584, 42], [581, 45, 584, 43], [582, 10, 585, 8, "console"], [582, 17, 585, 15], [582, 18, 585, 16, "log"], [582, 21, 585, 19], [582, 22, 585, 20], [582, 90, 585, 88], [582, 91, 585, 89], [583, 10, 586, 8, "detectedFaces"], [583, 23, 586, 21], [583, 26, 586, 24], [583, 32, 586, 30, "detectFacesWithTensorFlow"], [583, 57, 586, 55], [583, 58, 586, 56, "img"], [583, 61, 586, 59], [583, 62, 586, 60], [584, 10, 587, 8, "console"], [584, 17, 587, 15], [584, 18, 587, 16, "log"], [584, 21, 587, 19], [584, 22, 587, 20], [584, 70, 587, 68, "detectedFaces"], [584, 83, 587, 81], [584, 84, 587, 82, "length"], [584, 90, 587, 88], [584, 98, 587, 96], [584, 99, 587, 97], [585, 8, 588, 6], [585, 9, 588, 7], [585, 10, 588, 8], [585, 17, 588, 15, "tensorFlowError"], [585, 32, 588, 30], [585, 34, 588, 32], [586, 10, 589, 8, "console"], [586, 17, 589, 15], [586, 18, 589, 16, "warn"], [586, 22, 589, 20], [586, 23, 589, 21], [586, 61, 589, 59], [586, 63, 589, 61, "tensorFlowError"], [586, 78, 589, 76], [586, 79, 589, 77], [587, 10, 590, 8, "console"], [587, 17, 590, 15], [587, 18, 590, 16, "warn"], [587, 22, 590, 20], [587, 23, 590, 21], [587, 68, 590, 66], [587, 70, 590, 68], [588, 12, 591, 10, "message"], [588, 19, 591, 17], [588, 21, 591, 19, "tensorFlowError"], [588, 36, 591, 34], [588, 37, 591, 35, "message"], [588, 44, 591, 42], [589, 12, 592, 10, "stack"], [589, 17, 592, 15], [589, 19, 592, 17, "tensorFlowError"], [589, 34, 592, 32], [589, 35, 592, 33, "stack"], [590, 10, 593, 8], [590, 11, 593, 9], [590, 12, 593, 10], [592, 10, 595, 8], [593, 10, 596, 8, "console"], [593, 17, 596, 15], [593, 18, 596, 16, "log"], [593, 21, 596, 19], [593, 22, 596, 20], [593, 86, 596, 84], [593, 87, 596, 85], [594, 10, 597, 8, "detectedFaces"], [594, 23, 597, 21], [594, 26, 597, 24, "detectFacesHeuristic"], [594, 46, 597, 44], [594, 47, 597, 45, "img"], [594, 50, 597, 48], [594, 52, 597, 50, "ctx"], [594, 55, 597, 53], [594, 56, 597, 54], [595, 10, 598, 8, "console"], [595, 17, 598, 15], [595, 18, 598, 16, "log"], [595, 21, 598, 19], [595, 22, 598, 20], [595, 70, 598, 68, "detectedFaces"], [595, 83, 598, 81], [595, 84, 598, 82, "length"], [595, 90, 598, 88], [595, 98, 598, 96], [595, 99, 598, 97], [596, 8, 599, 6], [598, 8, 601, 6], [599, 8, 602, 6], [599, 12, 602, 10, "detectedFaces"], [599, 25, 602, 23], [599, 26, 602, 24, "length"], [599, 32, 602, 30], [599, 37, 602, 35], [599, 38, 602, 36], [599, 40, 602, 38], [600, 10, 603, 8, "console"], [600, 17, 603, 15], [600, 18, 603, 16, "log"], [600, 21, 603, 19], [600, 22, 603, 20], [600, 89, 603, 87], [600, 90, 603, 88], [601, 10, 604, 8, "detectedFaces"], [601, 23, 604, 21], [601, 26, 604, 24, "detectFacesAggressive"], [601, 47, 604, 45], [601, 48, 604, 46, "img"], [601, 51, 604, 49], [601, 53, 604, 51, "ctx"], [601, 56, 604, 54], [601, 57, 604, 55], [602, 10, 605, 8, "console"], [602, 17, 605, 15], [602, 18, 605, 16, "log"], [602, 21, 605, 19], [602, 22, 605, 20], [602, 71, 605, 69, "detectedFaces"], [602, 84, 605, 82], [602, 85, 605, 83, "length"], [602, 91, 605, 89], [602, 99, 605, 97], [602, 100, 605, 98], [603, 8, 606, 6], [604, 8, 608, 6, "console"], [604, 15, 608, 13], [604, 16, 608, 14, "log"], [604, 19, 608, 17], [604, 20, 608, 18], [604, 72, 608, 70, "detectedFaces"], [604, 85, 608, 83], [604, 86, 608, 84, "length"], [604, 92, 608, 90], [604, 100, 608, 98], [604, 101, 608, 99], [605, 8, 609, 6], [605, 12, 609, 10, "detectedFaces"], [605, 25, 609, 23], [605, 26, 609, 24, "length"], [605, 32, 609, 30], [605, 35, 609, 33], [605, 36, 609, 34], [605, 38, 609, 36], [606, 10, 610, 8, "console"], [606, 17, 610, 15], [606, 18, 610, 16, "log"], [606, 21, 610, 19], [606, 22, 610, 20], [606, 66, 610, 64], [606, 68, 610, 66, "detectedFaces"], [606, 81, 610, 79], [606, 82, 610, 80, "map"], [606, 85, 610, 83], [606, 86, 610, 84], [606, 87, 610, 85, "face"], [606, 91, 610, 89], [606, 93, 610, 91, "i"], [606, 94, 610, 92], [606, 100, 610, 98], [607, 12, 611, 10, "faceNumber"], [607, 22, 611, 20], [607, 24, 611, 22, "i"], [607, 25, 611, 23], [607, 28, 611, 26], [607, 29, 611, 27], [608, 12, 612, 10, "centerX"], [608, 19, 612, 17], [608, 21, 612, 19, "face"], [608, 25, 612, 23], [608, 26, 612, 24, "boundingBox"], [608, 37, 612, 35], [608, 38, 612, 36, "xCenter"], [608, 45, 612, 43], [609, 12, 613, 10, "centerY"], [609, 19, 613, 17], [609, 21, 613, 19, "face"], [609, 25, 613, 23], [609, 26, 613, 24, "boundingBox"], [609, 37, 613, 35], [609, 38, 613, 36, "yCenter"], [609, 45, 613, 43], [610, 12, 614, 10, "width"], [610, 17, 614, 15], [610, 19, 614, 17, "face"], [610, 23, 614, 21], [610, 24, 614, 22, "boundingBox"], [610, 35, 614, 33], [610, 36, 614, 34, "width"], [610, 41, 614, 39], [611, 12, 615, 10, "height"], [611, 18, 615, 16], [611, 20, 615, 18, "face"], [611, 24, 615, 22], [611, 25, 615, 23, "boundingBox"], [611, 36, 615, 34], [611, 37, 615, 35, "height"], [612, 10, 616, 8], [612, 11, 616, 9], [612, 12, 616, 10], [612, 13, 616, 11], [612, 14, 616, 12], [613, 8, 617, 6], [613, 9, 617, 7], [613, 15, 617, 13], [614, 10, 618, 8, "console"], [614, 17, 618, 15], [614, 18, 618, 16, "log"], [614, 21, 618, 19], [614, 22, 618, 20], [614, 74, 618, 72], [614, 75, 618, 73], [615, 10, 619, 8, "console"], [615, 17, 619, 15], [615, 18, 619, 16, "log"], [615, 21, 619, 19], [615, 22, 619, 20], [615, 95, 619, 93], [615, 96, 619, 94], [617, 10, 621, 8], [618, 10, 622, 8], [618, 16, 622, 14, "centerX"], [618, 23, 622, 21], [618, 26, 622, 24, "img"], [618, 29, 622, 27], [618, 30, 622, 28, "width"], [618, 35, 622, 33], [618, 38, 622, 36], [618, 41, 622, 39], [619, 10, 623, 8], [619, 16, 623, 14, "centerY"], [619, 23, 623, 21], [619, 26, 623, 24, "img"], [619, 29, 623, 27], [619, 30, 623, 28, "height"], [619, 36, 623, 34], [619, 39, 623, 37], [619, 42, 623, 40], [620, 10, 624, 8], [620, 16, 624, 14, "centerWidth"], [620, 27, 624, 25], [620, 30, 624, 28, "img"], [620, 33, 624, 31], [620, 34, 624, 32, "width"], [620, 39, 624, 37], [620, 42, 624, 40], [620, 45, 624, 43], [621, 10, 625, 8], [621, 16, 625, 14, "centerHeight"], [621, 28, 625, 26], [621, 31, 625, 29, "img"], [621, 34, 625, 32], [621, 35, 625, 33, "height"], [621, 41, 625, 39], [621, 44, 625, 42], [621, 47, 625, 45], [622, 10, 627, 8, "detectedFaces"], [622, 23, 627, 21], [622, 26, 627, 24], [622, 27, 627, 25], [623, 12, 628, 10, "boundingBox"], [623, 23, 628, 21], [623, 25, 628, 23], [624, 14, 629, 12, "xCenter"], [624, 21, 629, 19], [624, 23, 629, 21], [624, 26, 629, 24], [625, 14, 630, 12, "yCenter"], [625, 21, 630, 19], [625, 23, 630, 21], [625, 26, 630, 24], [626, 14, 631, 12, "width"], [626, 19, 631, 17], [626, 21, 631, 19], [626, 24, 631, 22], [627, 14, 632, 12, "height"], [627, 20, 632, 18], [627, 22, 632, 20], [628, 12, 633, 10], [628, 13, 633, 11], [629, 12, 634, 10, "confidence"], [629, 22, 634, 20], [629, 24, 634, 22], [630, 10, 635, 8], [630, 11, 635, 9], [630, 12, 635, 10], [631, 10, 637, 8, "console"], [631, 17, 637, 15], [631, 18, 637, 16, "log"], [631, 21, 637, 19], [631, 22, 637, 20], [631, 100, 637, 98], [631, 101, 637, 99], [632, 8, 638, 6], [633, 8, 640, 6, "setProcessingProgress"], [633, 29, 640, 27], [633, 30, 640, 28], [633, 32, 640, 30], [633, 33, 640, 31], [635, 8, 642, 6], [636, 8, 643, 6], [636, 12, 643, 10, "detectedFaces"], [636, 25, 643, 23], [636, 26, 643, 24, "length"], [636, 32, 643, 30], [636, 35, 643, 33], [636, 36, 643, 34], [636, 38, 643, 36], [637, 10, 644, 8, "console"], [637, 17, 644, 15], [637, 18, 644, 16, "log"], [637, 21, 644, 19], [637, 22, 644, 20], [637, 61, 644, 59, "detectedFaces"], [637, 74, 644, 72], [637, 75, 644, 73, "length"], [637, 81, 644, 79], [637, 101, 644, 99], [637, 102, 644, 100], [638, 10, 646, 8, "detectedFaces"], [638, 23, 646, 21], [638, 24, 646, 22, "for<PERSON>ach"], [638, 31, 646, 29], [638, 32, 646, 30], [638, 33, 646, 31, "detection"], [638, 42, 646, 40], [638, 44, 646, 42, "index"], [638, 49, 646, 47], [638, 54, 646, 52], [639, 12, 647, 10], [639, 18, 647, 16, "bbox"], [639, 22, 647, 20], [639, 25, 647, 23, "detection"], [639, 34, 647, 32], [639, 35, 647, 33, "boundingBox"], [639, 46, 647, 44], [640, 12, 649, 10, "console"], [640, 19, 649, 17], [640, 20, 649, 18, "log"], [640, 23, 649, 21], [640, 24, 649, 22], [640, 85, 649, 83, "index"], [640, 90, 649, 88], [640, 93, 649, 91], [640, 94, 649, 92], [640, 97, 649, 95], [640, 99, 649, 97], [641, 14, 650, 12, "bbox"], [641, 18, 650, 16], [642, 14, 651, 12, "imageSize"], [642, 23, 651, 21], [642, 25, 651, 23], [643, 16, 651, 25, "width"], [643, 21, 651, 30], [643, 23, 651, 32, "img"], [643, 26, 651, 35], [643, 27, 651, 36, "width"], [643, 32, 651, 41], [644, 16, 651, 43, "height"], [644, 22, 651, 49], [644, 24, 651, 51, "img"], [644, 27, 651, 54], [644, 28, 651, 55, "height"], [645, 14, 651, 62], [646, 12, 652, 10], [646, 13, 652, 11], [646, 14, 652, 12], [648, 12, 654, 10], [649, 12, 655, 10], [649, 18, 655, 16, "faceX"], [649, 23, 655, 21], [649, 26, 655, 24, "bbox"], [649, 30, 655, 28], [649, 31, 655, 29, "xCenter"], [649, 38, 655, 36], [649, 41, 655, 39, "img"], [649, 44, 655, 42], [649, 45, 655, 43, "width"], [649, 50, 655, 48], [649, 53, 655, 52, "bbox"], [649, 57, 655, 56], [649, 58, 655, 57, "width"], [649, 63, 655, 62], [649, 66, 655, 65, "img"], [649, 69, 655, 68], [649, 70, 655, 69, "width"], [649, 75, 655, 74], [649, 78, 655, 78], [649, 79, 655, 79], [650, 12, 656, 10], [650, 18, 656, 16, "faceY"], [650, 23, 656, 21], [650, 26, 656, 24, "bbox"], [650, 30, 656, 28], [650, 31, 656, 29, "yCenter"], [650, 38, 656, 36], [650, 41, 656, 39, "img"], [650, 44, 656, 42], [650, 45, 656, 43, "height"], [650, 51, 656, 49], [650, 54, 656, 53, "bbox"], [650, 58, 656, 57], [650, 59, 656, 58, "height"], [650, 65, 656, 64], [650, 68, 656, 67, "img"], [650, 71, 656, 70], [650, 72, 656, 71, "height"], [650, 78, 656, 77], [650, 81, 656, 81], [650, 82, 656, 82], [651, 12, 657, 10], [651, 18, 657, 16, "faceWidth"], [651, 27, 657, 25], [651, 30, 657, 28, "bbox"], [651, 34, 657, 32], [651, 35, 657, 33, "width"], [651, 40, 657, 38], [651, 43, 657, 41, "img"], [651, 46, 657, 44], [651, 47, 657, 45, "width"], [651, 52, 657, 50], [652, 12, 658, 10], [652, 18, 658, 16, "faceHeight"], [652, 28, 658, 26], [652, 31, 658, 29, "bbox"], [652, 35, 658, 33], [652, 36, 658, 34, "height"], [652, 42, 658, 40], [652, 45, 658, 43, "img"], [652, 48, 658, 46], [652, 49, 658, 47, "height"], [652, 55, 658, 53], [653, 12, 660, 10, "console"], [653, 19, 660, 17], [653, 20, 660, 18, "log"], [653, 23, 660, 21], [653, 24, 660, 22], [653, 84, 660, 82], [653, 86, 660, 84], [654, 14, 661, 12, "faceX"], [654, 19, 661, 17], [655, 14, 661, 19, "faceY"], [655, 19, 661, 24], [656, 14, 661, 26, "faceWidth"], [656, 23, 661, 35], [657, 14, 661, 37, "faceHeight"], [657, 24, 661, 47], [658, 14, 662, 12, "<PERSON><PERSON><PERSON><PERSON>"], [658, 21, 662, 19], [658, 23, 662, 21, "faceX"], [658, 28, 662, 26], [658, 32, 662, 30], [658, 33, 662, 31], [658, 37, 662, 35, "faceY"], [658, 42, 662, 40], [658, 46, 662, 44], [658, 47, 662, 45], [658, 51, 662, 49, "faceWidth"], [658, 60, 662, 58], [658, 63, 662, 61], [658, 64, 662, 62], [658, 68, 662, 66, "faceHeight"], [658, 78, 662, 76], [658, 81, 662, 79], [659, 12, 663, 10], [659, 13, 663, 11], [659, 14, 663, 12], [661, 12, 665, 10], [662, 12, 666, 10], [662, 18, 666, 16, "padding"], [662, 25, 666, 23], [662, 28, 666, 26], [662, 31, 666, 29], [663, 12, 667, 10], [663, 18, 667, 16, "paddedX"], [663, 25, 667, 23], [663, 28, 667, 26, "Math"], [663, 32, 667, 30], [663, 33, 667, 31, "max"], [663, 36, 667, 34], [663, 37, 667, 35], [663, 38, 667, 36], [663, 40, 667, 38, "faceX"], [663, 45, 667, 43], [663, 48, 667, 46, "faceWidth"], [663, 57, 667, 55], [663, 60, 667, 58, "padding"], [663, 67, 667, 65], [663, 68, 667, 66], [664, 12, 668, 10], [664, 18, 668, 16, "paddedY"], [664, 25, 668, 23], [664, 28, 668, 26, "Math"], [664, 32, 668, 30], [664, 33, 668, 31, "max"], [664, 36, 668, 34], [664, 37, 668, 35], [664, 38, 668, 36], [664, 40, 668, 38, "faceY"], [664, 45, 668, 43], [664, 48, 668, 46, "faceHeight"], [664, 58, 668, 56], [664, 61, 668, 59, "padding"], [664, 68, 668, 66], [664, 69, 668, 67], [665, 12, 669, 10], [665, 18, 669, 16, "<PERSON><PERSON><PERSON><PERSON>"], [665, 29, 669, 27], [665, 32, 669, 30, "Math"], [665, 36, 669, 34], [665, 37, 669, 35, "min"], [665, 40, 669, 38], [665, 41, 669, 39, "img"], [665, 44, 669, 42], [665, 45, 669, 43, "width"], [665, 50, 669, 48], [665, 53, 669, 51, "paddedX"], [665, 60, 669, 58], [665, 62, 669, 60, "faceWidth"], [665, 71, 669, 69], [665, 75, 669, 73], [665, 76, 669, 74], [665, 79, 669, 77], [665, 80, 669, 78], [665, 83, 669, 81, "padding"], [665, 90, 669, 88], [665, 91, 669, 89], [665, 92, 669, 90], [666, 12, 670, 10], [666, 18, 670, 16, "paddedHeight"], [666, 30, 670, 28], [666, 33, 670, 31, "Math"], [666, 37, 670, 35], [666, 38, 670, 36, "min"], [666, 41, 670, 39], [666, 42, 670, 40, "img"], [666, 45, 670, 43], [666, 46, 670, 44, "height"], [666, 52, 670, 50], [666, 55, 670, 53, "paddedY"], [666, 62, 670, 60], [666, 64, 670, 62, "faceHeight"], [666, 74, 670, 72], [666, 78, 670, 76], [666, 79, 670, 77], [666, 82, 670, 80], [666, 83, 670, 81], [666, 86, 670, 84, "padding"], [666, 93, 670, 91], [666, 94, 670, 92], [666, 95, 670, 93], [667, 12, 672, 10, "console"], [667, 19, 672, 17], [667, 20, 672, 18, "log"], [667, 23, 672, 21], [667, 24, 672, 22], [667, 60, 672, 58, "index"], [667, 65, 672, 63], [667, 68, 672, 66], [667, 69, 672, 67], [667, 72, 672, 70], [667, 74, 672, 72], [668, 14, 673, 12, "original"], [668, 22, 673, 20], [668, 24, 673, 22], [669, 16, 673, 24, "x"], [669, 17, 673, 25], [669, 19, 673, 27, "Math"], [669, 23, 673, 31], [669, 24, 673, 32, "round"], [669, 29, 673, 37], [669, 30, 673, 38, "faceX"], [669, 35, 673, 43], [669, 36, 673, 44], [670, 16, 673, 46, "y"], [670, 17, 673, 47], [670, 19, 673, 49, "Math"], [670, 23, 673, 53], [670, 24, 673, 54, "round"], [670, 29, 673, 59], [670, 30, 673, 60, "faceY"], [670, 35, 673, 65], [670, 36, 673, 66], [671, 16, 673, 68, "w"], [671, 17, 673, 69], [671, 19, 673, 71, "Math"], [671, 23, 673, 75], [671, 24, 673, 76, "round"], [671, 29, 673, 81], [671, 30, 673, 82, "faceWidth"], [671, 39, 673, 91], [671, 40, 673, 92], [672, 16, 673, 94, "h"], [672, 17, 673, 95], [672, 19, 673, 97, "Math"], [672, 23, 673, 101], [672, 24, 673, 102, "round"], [672, 29, 673, 107], [672, 30, 673, 108, "faceHeight"], [672, 40, 673, 118], [673, 14, 673, 120], [673, 15, 673, 121], [674, 14, 674, 12, "padded"], [674, 20, 674, 18], [674, 22, 674, 20], [675, 16, 674, 22, "x"], [675, 17, 674, 23], [675, 19, 674, 25, "Math"], [675, 23, 674, 29], [675, 24, 674, 30, "round"], [675, 29, 674, 35], [675, 30, 674, 36, "paddedX"], [675, 37, 674, 43], [675, 38, 674, 44], [676, 16, 674, 46, "y"], [676, 17, 674, 47], [676, 19, 674, 49, "Math"], [676, 23, 674, 53], [676, 24, 674, 54, "round"], [676, 29, 674, 59], [676, 30, 674, 60, "paddedY"], [676, 37, 674, 67], [676, 38, 674, 68], [677, 16, 674, 70, "w"], [677, 17, 674, 71], [677, 19, 674, 73, "Math"], [677, 23, 674, 77], [677, 24, 674, 78, "round"], [677, 29, 674, 83], [677, 30, 674, 84, "<PERSON><PERSON><PERSON><PERSON>"], [677, 41, 674, 95], [677, 42, 674, 96], [678, 16, 674, 98, "h"], [678, 17, 674, 99], [678, 19, 674, 101, "Math"], [678, 23, 674, 105], [678, 24, 674, 106, "round"], [678, 29, 674, 111], [678, 30, 674, 112, "paddedHeight"], [678, 42, 674, 124], [679, 14, 674, 126], [680, 12, 675, 10], [680, 13, 675, 11], [680, 14, 675, 12], [682, 12, 677, 10], [683, 12, 678, 10, "console"], [683, 19, 678, 17], [683, 20, 678, 18, "log"], [683, 23, 678, 21], [683, 24, 678, 22], [683, 70, 678, 68], [683, 72, 678, 70], [684, 14, 679, 12, "width"], [684, 19, 679, 17], [684, 21, 679, 19, "canvas"], [684, 27, 679, 25], [684, 28, 679, 26, "width"], [684, 33, 679, 31], [685, 14, 680, 12, "height"], [685, 20, 680, 18], [685, 22, 680, 20, "canvas"], [685, 28, 680, 26], [685, 29, 680, 27, "height"], [685, 35, 680, 33], [686, 14, 681, 12, "contextValid"], [686, 26, 681, 24], [686, 28, 681, 26], [686, 29, 681, 27], [686, 30, 681, 28, "ctx"], [687, 12, 682, 10], [687, 13, 682, 11], [687, 14, 682, 12], [689, 12, 684, 10], [690, 12, 685, 10, "applyStrongBlur"], [690, 27, 685, 25], [690, 28, 685, 26, "ctx"], [690, 31, 685, 29], [690, 33, 685, 31, "paddedX"], [690, 40, 685, 38], [690, 42, 685, 40, "paddedY"], [690, 49, 685, 47], [690, 51, 685, 49, "<PERSON><PERSON><PERSON><PERSON>"], [690, 62, 685, 60], [690, 64, 685, 62, "paddedHeight"], [690, 76, 685, 74], [690, 77, 685, 75], [692, 12, 687, 10], [693, 12, 688, 10, "console"], [693, 19, 688, 17], [693, 20, 688, 18, "log"], [693, 23, 688, 21], [693, 24, 688, 22], [693, 102, 688, 100], [693, 103, 688, 101], [695, 12, 690, 10], [696, 12, 691, 10], [696, 18, 691, 16, "testImageData"], [696, 31, 691, 29], [696, 34, 691, 32, "ctx"], [696, 37, 691, 35], [696, 38, 691, 36, "getImageData"], [696, 50, 691, 48], [696, 51, 691, 49, "paddedX"], [696, 58, 691, 56], [696, 61, 691, 59], [696, 63, 691, 61], [696, 65, 691, 63, "paddedY"], [696, 72, 691, 70], [696, 75, 691, 73], [696, 77, 691, 75], [696, 79, 691, 77], [696, 81, 691, 79], [696, 83, 691, 81], [696, 85, 691, 83], [696, 86, 691, 84], [697, 12, 692, 10, "console"], [697, 19, 692, 17], [697, 20, 692, 18, "log"], [697, 23, 692, 21], [697, 24, 692, 22], [697, 70, 692, 68], [697, 72, 692, 70], [698, 14, 693, 12, "firstPixel"], [698, 24, 693, 22], [698, 26, 693, 24], [698, 27, 693, 25, "testImageData"], [698, 40, 693, 38], [698, 41, 693, 39, "data"], [698, 45, 693, 43], [698, 46, 693, 44], [698, 47, 693, 45], [698, 48, 693, 46], [698, 50, 693, 48, "testImageData"], [698, 63, 693, 61], [698, 64, 693, 62, "data"], [698, 68, 693, 66], [698, 69, 693, 67], [698, 70, 693, 68], [698, 71, 693, 69], [698, 73, 693, 71, "testImageData"], [698, 86, 693, 84], [698, 87, 693, 85, "data"], [698, 91, 693, 89], [698, 92, 693, 90], [698, 93, 693, 91], [698, 94, 693, 92], [698, 95, 693, 93], [699, 14, 694, 12, "secondPixel"], [699, 25, 694, 23], [699, 27, 694, 25], [699, 28, 694, 26, "testImageData"], [699, 41, 694, 39], [699, 42, 694, 40, "data"], [699, 46, 694, 44], [699, 47, 694, 45], [699, 48, 694, 46], [699, 49, 694, 47], [699, 51, 694, 49, "testImageData"], [699, 64, 694, 62], [699, 65, 694, 63, "data"], [699, 69, 694, 67], [699, 70, 694, 68], [699, 71, 694, 69], [699, 72, 694, 70], [699, 74, 694, 72, "testImageData"], [699, 87, 694, 85], [699, 88, 694, 86, "data"], [699, 92, 694, 90], [699, 93, 694, 91], [699, 94, 694, 92], [699, 95, 694, 93], [700, 12, 695, 10], [700, 13, 695, 11], [700, 14, 695, 12], [701, 12, 697, 10, "console"], [701, 19, 697, 17], [701, 20, 697, 18, "log"], [701, 23, 697, 21], [701, 24, 697, 22], [701, 50, 697, 48, "index"], [701, 55, 697, 53], [701, 58, 697, 56], [701, 59, 697, 57], [701, 79, 697, 77], [701, 80, 697, 78], [702, 10, 698, 8], [702, 11, 698, 9], [702, 12, 698, 10], [703, 10, 700, 8, "console"], [703, 17, 700, 15], [703, 18, 700, 16, "log"], [703, 21, 700, 19], [703, 22, 700, 20], [703, 48, 700, 46, "detectedFaces"], [703, 61, 700, 59], [703, 62, 700, 60, "length"], [703, 68, 700, 66], [703, 104, 700, 102], [703, 105, 700, 103], [704, 8, 701, 6], [704, 9, 701, 7], [704, 15, 701, 13], [705, 10, 702, 8, "console"], [705, 17, 702, 15], [705, 18, 702, 16, "log"], [705, 21, 702, 19], [705, 22, 702, 20], [705, 109, 702, 107], [705, 110, 702, 108], [706, 10, 703, 8], [707, 10, 704, 8, "applyFallbackFaceBlur"], [707, 31, 704, 29], [707, 32, 704, 30, "ctx"], [707, 35, 704, 33], [707, 37, 704, 35, "img"], [707, 40, 704, 38], [707, 41, 704, 39, "width"], [707, 46, 704, 44], [707, 48, 704, 46, "img"], [707, 51, 704, 49], [707, 52, 704, 50, "height"], [707, 58, 704, 56], [707, 59, 704, 57], [708, 8, 705, 6], [709, 8, 707, 6, "setProcessingProgress"], [709, 29, 707, 27], [709, 30, 707, 28], [709, 32, 707, 30], [709, 33, 707, 31], [711, 8, 709, 6], [712, 8, 710, 6, "console"], [712, 15, 710, 13], [712, 16, 710, 14, "log"], [712, 19, 710, 17], [712, 20, 710, 18], [712, 85, 710, 83], [712, 86, 710, 84], [713, 8, 711, 6], [713, 14, 711, 12, "blurredImageBlob"], [713, 30, 711, 28], [713, 33, 711, 31], [713, 39, 711, 37], [713, 43, 711, 41, "Promise"], [713, 50, 711, 48], [713, 51, 711, 56, "resolve"], [713, 58, 711, 63], [713, 62, 711, 68], [714, 10, 712, 8, "canvas"], [714, 16, 712, 14], [714, 17, 712, 15, "toBlob"], [714, 23, 712, 21], [714, 24, 712, 23, "blob"], [714, 28, 712, 27], [714, 32, 712, 32, "resolve"], [714, 39, 712, 39], [714, 40, 712, 40, "blob"], [714, 44, 712, 45], [714, 45, 712, 46], [714, 47, 712, 48], [714, 59, 712, 60], [714, 61, 712, 62], [714, 64, 712, 65], [714, 65, 712, 66], [715, 8, 713, 6], [715, 9, 713, 7], [715, 10, 713, 8], [716, 8, 715, 6], [716, 14, 715, 12, "blurredImageUrl"], [716, 29, 715, 27], [716, 32, 715, 30, "URL"], [716, 35, 715, 33], [716, 36, 715, 34, "createObjectURL"], [716, 51, 715, 49], [716, 52, 715, 50, "blurredImageBlob"], [716, 68, 715, 66], [716, 69, 715, 67], [717, 8, 716, 6, "console"], [717, 15, 716, 13], [717, 16, 716, 14, "log"], [717, 19, 716, 17], [717, 20, 716, 18], [717, 66, 716, 64], [717, 68, 716, 66, "blurredImageUrl"], [717, 83, 716, 81], [717, 84, 716, 82, "substring"], [717, 93, 716, 91], [717, 94, 716, 92], [717, 95, 716, 93], [717, 97, 716, 95], [717, 99, 716, 97], [717, 100, 716, 98], [717, 103, 716, 101], [717, 108, 716, 106], [717, 109, 716, 107], [719, 8, 718, 6], [720, 8, 719, 6, "setCapturedPhoto"], [720, 24, 719, 22], [720, 25, 719, 23, "blurredImageUrl"], [720, 40, 719, 38], [720, 41, 719, 39], [721, 8, 720, 6, "console"], [721, 15, 720, 13], [721, 16, 720, 14, "log"], [721, 19, 720, 17], [721, 20, 720, 18], [721, 87, 720, 85], [721, 88, 720, 86], [722, 8, 722, 6, "setProcessingProgress"], [722, 29, 722, 27], [722, 30, 722, 28], [722, 33, 722, 31], [722, 34, 722, 32], [724, 8, 724, 6], [725, 8, 725, 6], [725, 14, 725, 12, "completeProcessing"], [725, 32, 725, 30], [725, 33, 725, 31, "blurredImageUrl"], [725, 48, 725, 46], [725, 49, 725, 47], [726, 6, 727, 4], [726, 7, 727, 5], [726, 8, 727, 6], [726, 15, 727, 13, "error"], [726, 20, 727, 18], [726, 22, 727, 20], [727, 8, 728, 6, "console"], [727, 15, 728, 13], [727, 16, 728, 14, "error"], [727, 21, 728, 19], [727, 22, 728, 20], [727, 86, 728, 84], [727, 88, 728, 86, "error"], [727, 93, 728, 91], [727, 94, 728, 92], [728, 8, 729, 6, "console"], [728, 15, 729, 13], [728, 16, 729, 14, "error"], [728, 21, 729, 19], [728, 22, 729, 20], [728, 55, 729, 53], [728, 57, 729, 55, "error"], [728, 62, 729, 60], [728, 63, 729, 61, "stack"], [728, 68, 729, 66], [728, 69, 729, 67], [729, 8, 730, 6, "console"], [729, 15, 730, 13], [729, 16, 730, 14, "error"], [729, 21, 730, 19], [729, 22, 730, 20], [729, 57, 730, 55], [729, 59, 730, 57], [730, 10, 731, 8, "name"], [730, 14, 731, 12], [730, 16, 731, 14, "error"], [730, 21, 731, 19], [730, 22, 731, 20, "name"], [730, 26, 731, 24], [731, 10, 732, 8, "message"], [731, 17, 732, 15], [731, 19, 732, 17, "error"], [731, 24, 732, 22], [731, 25, 732, 23, "message"], [731, 32, 732, 30], [732, 10, 733, 8, "photoUri"], [733, 8, 734, 6], [733, 9, 734, 7], [733, 10, 734, 8], [734, 8, 735, 6, "setErrorMessage"], [734, 23, 735, 21], [734, 24, 735, 22], [734, 50, 735, 48], [734, 51, 735, 49], [735, 8, 736, 6, "setProcessingState"], [735, 26, 736, 24], [735, 27, 736, 25], [735, 34, 736, 32], [735, 35, 736, 33], [736, 6, 737, 4], [737, 4, 738, 2], [737, 5, 738, 3], [739, 4, 740, 2], [740, 4, 741, 2], [740, 10, 741, 8, "completeProcessing"], [740, 28, 741, 26], [740, 31, 741, 29], [740, 37, 741, 36, "blurredImageUrl"], [740, 52, 741, 59], [740, 56, 741, 64], [741, 6, 742, 4], [741, 10, 742, 8], [742, 8, 743, 6, "setProcessingState"], [742, 26, 743, 24], [742, 27, 743, 25], [742, 37, 743, 35], [742, 38, 743, 36], [744, 8, 745, 6], [745, 8, 746, 6], [745, 14, 746, 12, "timestamp"], [745, 23, 746, 21], [745, 26, 746, 24, "Date"], [745, 30, 746, 28], [745, 31, 746, 29, "now"], [745, 34, 746, 32], [745, 35, 746, 33], [745, 36, 746, 34], [746, 8, 747, 6], [746, 14, 747, 12, "result"], [746, 20, 747, 18], [746, 23, 747, 21], [747, 10, 748, 8, "imageUrl"], [747, 18, 748, 16], [747, 20, 748, 18, "blurredImageUrl"], [747, 35, 748, 33], [748, 10, 749, 8, "localUri"], [748, 18, 749, 16], [748, 20, 749, 18, "blurredImageUrl"], [748, 35, 749, 33], [749, 10, 750, 8, "challengeCode"], [749, 23, 750, 21], [749, 25, 750, 23, "challengeCode"], [749, 38, 750, 36], [749, 42, 750, 40], [749, 44, 750, 42], [750, 10, 751, 8, "timestamp"], [750, 19, 751, 17], [751, 10, 752, 8, "jobId"], [751, 15, 752, 13], [751, 17, 752, 15], [751, 27, 752, 25, "timestamp"], [751, 36, 752, 34], [751, 38, 752, 36], [752, 10, 753, 8, "status"], [752, 16, 753, 14], [752, 18, 753, 16], [753, 8, 754, 6], [753, 9, 754, 7], [754, 8, 756, 6, "console"], [754, 15, 756, 13], [754, 16, 756, 14, "log"], [754, 19, 756, 17], [754, 20, 756, 18], [754, 100, 756, 98], [754, 102, 756, 100], [755, 10, 757, 8, "imageUrl"], [755, 18, 757, 16], [755, 20, 757, 18, "blurredImageUrl"], [755, 35, 757, 33], [755, 36, 757, 34, "substring"], [755, 45, 757, 43], [755, 46, 757, 44], [755, 47, 757, 45], [755, 49, 757, 47], [755, 51, 757, 49], [755, 52, 757, 50], [755, 55, 757, 53], [755, 60, 757, 58], [756, 10, 758, 8, "timestamp"], [756, 19, 758, 17], [757, 10, 759, 8, "jobId"], [757, 15, 759, 13], [757, 17, 759, 15, "result"], [757, 23, 759, 21], [757, 24, 759, 22, "jobId"], [758, 8, 760, 6], [758, 9, 760, 7], [758, 10, 760, 8], [760, 8, 762, 6], [761, 8, 763, 6, "onComplete"], [761, 18, 763, 16], [761, 19, 763, 17, "result"], [761, 25, 763, 23], [761, 26, 763, 24], [762, 6, 765, 4], [762, 7, 765, 5], [762, 8, 765, 6], [762, 15, 765, 13, "error"], [762, 20, 765, 18], [762, 22, 765, 20], [763, 8, 766, 6, "console"], [763, 15, 766, 13], [763, 16, 766, 14, "error"], [763, 21, 766, 19], [763, 22, 766, 20], [763, 57, 766, 55], [763, 59, 766, 57, "error"], [763, 64, 766, 62], [763, 65, 766, 63], [764, 8, 767, 6, "setErrorMessage"], [764, 23, 767, 21], [764, 24, 767, 22], [764, 56, 767, 54], [764, 57, 767, 55], [765, 8, 768, 6, "setProcessingState"], [765, 26, 768, 24], [765, 27, 768, 25], [765, 34, 768, 32], [765, 35, 768, 33], [766, 6, 769, 4], [767, 4, 770, 2], [767, 5, 770, 3], [769, 4, 772, 2], [770, 4, 773, 2], [770, 10, 773, 8, "triggerServerProcessing"], [770, 33, 773, 31], [770, 36, 773, 34], [770, 42, 773, 34, "triggerServerProcessing"], [770, 43, 773, 41, "privateImageUrl"], [770, 58, 773, 64], [770, 60, 773, 66, "timestamp"], [770, 69, 773, 83], [770, 74, 773, 88], [771, 6, 774, 4], [771, 10, 774, 8], [772, 8, 775, 6, "console"], [772, 15, 775, 13], [772, 16, 775, 14, "log"], [772, 19, 775, 17], [772, 20, 775, 18], [772, 74, 775, 72], [772, 76, 775, 74, "privateImageUrl"], [772, 91, 775, 89], [772, 92, 775, 90], [773, 8, 776, 6, "setProcessingState"], [773, 26, 776, 24], [773, 27, 776, 25], [773, 39, 776, 37], [773, 40, 776, 38], [774, 8, 777, 6, "setProcessingProgress"], [774, 29, 777, 27], [774, 30, 777, 28], [774, 32, 777, 30], [774, 33, 777, 31], [775, 8, 779, 6], [775, 14, 779, 12, "requestBody"], [775, 25, 779, 23], [775, 28, 779, 26], [776, 10, 780, 8, "imageUrl"], [776, 18, 780, 16], [776, 20, 780, 18, "privateImageUrl"], [776, 35, 780, 33], [777, 10, 781, 8, "userId"], [777, 16, 781, 14], [778, 10, 782, 8, "requestId"], [778, 19, 782, 17], [779, 10, 783, 8, "timestamp"], [779, 19, 783, 17], [780, 10, 784, 8, "platform"], [780, 18, 784, 16], [780, 20, 784, 18], [781, 8, 785, 6], [781, 9, 785, 7], [782, 8, 787, 6, "console"], [782, 15, 787, 13], [782, 16, 787, 14, "log"], [782, 19, 787, 17], [782, 20, 787, 18], [782, 65, 787, 63], [782, 67, 787, 65, "requestBody"], [782, 78, 787, 76], [782, 79, 787, 77], [784, 8, 789, 6], [785, 8, 790, 6], [785, 14, 790, 12, "response"], [785, 22, 790, 20], [785, 25, 790, 23], [785, 31, 790, 29, "fetch"], [785, 36, 790, 34], [785, 37, 790, 35], [785, 40, 790, 38, "API_BASE_URL"], [785, 52, 790, 50], [785, 72, 790, 70], [785, 74, 790, 72], [786, 10, 791, 8, "method"], [786, 16, 791, 14], [786, 18, 791, 16], [786, 24, 791, 22], [787, 10, 792, 8, "headers"], [787, 17, 792, 15], [787, 19, 792, 17], [788, 12, 793, 10], [788, 26, 793, 24], [788, 28, 793, 26], [788, 46, 793, 44], [789, 12, 794, 10], [789, 27, 794, 25], [789, 29, 794, 27], [789, 39, 794, 37], [789, 45, 794, 43, "getAuthToken"], [789, 57, 794, 55], [789, 58, 794, 56], [789, 59, 794, 57], [790, 10, 795, 8], [790, 11, 795, 9], [791, 10, 796, 8, "body"], [791, 14, 796, 12], [791, 16, 796, 14, "JSON"], [791, 20, 796, 18], [791, 21, 796, 19, "stringify"], [791, 30, 796, 28], [791, 31, 796, 29, "requestBody"], [791, 42, 796, 40], [792, 8, 797, 6], [792, 9, 797, 7], [792, 10, 797, 8], [793, 8, 799, 6], [793, 12, 799, 10], [793, 13, 799, 11, "response"], [793, 21, 799, 19], [793, 22, 799, 20, "ok"], [793, 24, 799, 22], [793, 26, 799, 24], [794, 10, 800, 8], [794, 16, 800, 14, "errorText"], [794, 25, 800, 23], [794, 28, 800, 26], [794, 34, 800, 32, "response"], [794, 42, 800, 40], [794, 43, 800, 41, "text"], [794, 47, 800, 45], [794, 48, 800, 46], [794, 49, 800, 47], [795, 10, 801, 8, "console"], [795, 17, 801, 15], [795, 18, 801, 16, "error"], [795, 23, 801, 21], [795, 24, 801, 22], [795, 68, 801, 66], [795, 70, 801, 68, "response"], [795, 78, 801, 76], [795, 79, 801, 77, "status"], [795, 85, 801, 83], [795, 87, 801, 85, "errorText"], [795, 96, 801, 94], [795, 97, 801, 95], [796, 10, 802, 8], [796, 16, 802, 14], [796, 20, 802, 18, "Error"], [796, 25, 802, 23], [796, 26, 802, 24], [796, 48, 802, 46, "response"], [796, 56, 802, 54], [796, 57, 802, 55, "status"], [796, 63, 802, 61], [796, 67, 802, 65, "response"], [796, 75, 802, 73], [796, 76, 802, 74, "statusText"], [796, 86, 802, 84], [796, 88, 802, 86], [796, 89, 802, 87], [797, 8, 803, 6], [798, 8, 805, 6], [798, 14, 805, 12, "result"], [798, 20, 805, 18], [798, 23, 805, 21], [798, 29, 805, 27, "response"], [798, 37, 805, 35], [798, 38, 805, 36, "json"], [798, 42, 805, 40], [798, 43, 805, 41], [798, 44, 805, 42], [799, 8, 806, 6, "console"], [799, 15, 806, 13], [799, 16, 806, 14, "log"], [799, 19, 806, 17], [799, 20, 806, 18], [799, 68, 806, 66], [799, 70, 806, 68, "result"], [799, 76, 806, 74], [799, 77, 806, 75], [800, 8, 808, 6], [800, 12, 808, 10], [800, 13, 808, 11, "result"], [800, 19, 808, 17], [800, 20, 808, 18, "jobId"], [800, 25, 808, 23], [800, 27, 808, 25], [801, 10, 809, 8], [801, 16, 809, 14], [801, 20, 809, 18, "Error"], [801, 25, 809, 23], [801, 26, 809, 24], [801, 70, 809, 68], [801, 71, 809, 69], [802, 8, 810, 6], [804, 8, 812, 6], [805, 8, 813, 6], [805, 14, 813, 12, "pollForCompletion"], [805, 31, 813, 29], [805, 32, 813, 30, "result"], [805, 38, 813, 36], [805, 39, 813, 37, "jobId"], [805, 44, 813, 42], [805, 46, 813, 44, "timestamp"], [805, 55, 813, 53], [805, 56, 813, 54], [806, 6, 814, 4], [806, 7, 814, 5], [806, 8, 814, 6], [806, 15, 814, 13, "error"], [806, 20, 814, 18], [806, 22, 814, 20], [807, 8, 815, 6, "console"], [807, 15, 815, 13], [807, 16, 815, 14, "error"], [807, 21, 815, 19], [807, 22, 815, 20], [807, 57, 815, 55], [807, 59, 815, 57, "error"], [807, 64, 815, 62], [807, 65, 815, 63], [808, 8, 816, 6, "setErrorMessage"], [808, 23, 816, 21], [808, 24, 816, 22], [808, 52, 816, 50, "error"], [808, 57, 816, 55], [808, 58, 816, 56, "message"], [808, 65, 816, 63], [808, 67, 816, 65], [808, 68, 816, 66], [809, 8, 817, 6, "setProcessingState"], [809, 26, 817, 24], [809, 27, 817, 25], [809, 34, 817, 32], [809, 35, 817, 33], [810, 6, 818, 4], [811, 4, 819, 2], [811, 5, 819, 3], [812, 4, 820, 2], [813, 4, 821, 2], [813, 10, 821, 8, "pollForCompletion"], [813, 27, 821, 25], [813, 30, 821, 28], [813, 36, 821, 28, "pollForCompletion"], [813, 37, 821, 35, "jobId"], [813, 42, 821, 48], [813, 44, 821, 50, "timestamp"], [813, 53, 821, 67], [813, 55, 821, 69, "attempts"], [813, 63, 821, 77], [813, 66, 821, 80], [813, 67, 821, 81], [813, 72, 821, 86], [814, 6, 822, 4], [814, 12, 822, 10, "MAX_ATTEMPTS"], [814, 24, 822, 22], [814, 27, 822, 25], [814, 29, 822, 27], [814, 30, 822, 28], [814, 31, 822, 29], [815, 6, 823, 4], [815, 12, 823, 10, "POLL_INTERVAL"], [815, 25, 823, 23], [815, 28, 823, 26], [815, 32, 823, 30], [815, 33, 823, 31], [815, 34, 823, 32], [817, 6, 825, 4, "console"], [817, 13, 825, 11], [817, 14, 825, 12, "log"], [817, 17, 825, 15], [817, 18, 825, 16], [817, 53, 825, 51, "attempts"], [817, 61, 825, 59], [817, 64, 825, 62], [817, 65, 825, 63], [817, 69, 825, 67, "MAX_ATTEMPTS"], [817, 81, 825, 79], [817, 93, 825, 91, "jobId"], [817, 98, 825, 96], [817, 100, 825, 98], [817, 101, 825, 99], [818, 6, 827, 4], [818, 10, 827, 8, "attempts"], [818, 18, 827, 16], [818, 22, 827, 20, "MAX_ATTEMPTS"], [818, 34, 827, 32], [818, 36, 827, 34], [819, 8, 828, 6, "console"], [819, 15, 828, 13], [819, 16, 828, 14, "error"], [819, 21, 828, 19], [819, 22, 828, 20], [819, 75, 828, 73], [819, 76, 828, 74], [820, 8, 829, 6, "setErrorMessage"], [820, 23, 829, 21], [820, 24, 829, 22], [820, 63, 829, 61], [820, 64, 829, 62], [821, 8, 830, 6, "setProcessingState"], [821, 26, 830, 24], [821, 27, 830, 25], [821, 34, 830, 32], [821, 35, 830, 33], [822, 8, 831, 6], [823, 6, 832, 4], [824, 6, 834, 4], [824, 10, 834, 8], [825, 8, 835, 6], [825, 14, 835, 12, "response"], [825, 22, 835, 20], [825, 25, 835, 23], [825, 31, 835, 29, "fetch"], [825, 36, 835, 34], [825, 37, 835, 35], [825, 40, 835, 38, "API_BASE_URL"], [825, 52, 835, 50], [825, 75, 835, 73, "jobId"], [825, 80, 835, 78], [825, 82, 835, 80], [825, 84, 835, 82], [826, 10, 836, 8, "headers"], [826, 17, 836, 15], [826, 19, 836, 17], [827, 12, 837, 10], [827, 27, 837, 25], [827, 29, 837, 27], [827, 39, 837, 37], [827, 45, 837, 43, "getAuthToken"], [827, 57, 837, 55], [827, 58, 837, 56], [827, 59, 837, 57], [828, 10, 838, 8], [829, 8, 839, 6], [829, 9, 839, 7], [829, 10, 839, 8], [830, 8, 841, 6], [830, 12, 841, 10], [830, 13, 841, 11, "response"], [830, 21, 841, 19], [830, 22, 841, 20, "ok"], [830, 24, 841, 22], [830, 26, 841, 24], [831, 10, 842, 8], [831, 16, 842, 14], [831, 20, 842, 18, "Error"], [831, 25, 842, 23], [831, 26, 842, 24], [831, 34, 842, 32, "response"], [831, 42, 842, 40], [831, 43, 842, 41, "status"], [831, 49, 842, 47], [831, 54, 842, 52, "response"], [831, 62, 842, 60], [831, 63, 842, 61, "statusText"], [831, 73, 842, 71], [831, 75, 842, 73], [831, 76, 842, 74], [832, 8, 843, 6], [833, 8, 845, 6], [833, 14, 845, 12, "status"], [833, 20, 845, 18], [833, 23, 845, 21], [833, 29, 845, 27, "response"], [833, 37, 845, 35], [833, 38, 845, 36, "json"], [833, 42, 845, 40], [833, 43, 845, 41], [833, 44, 845, 42], [834, 8, 846, 6, "console"], [834, 15, 846, 13], [834, 16, 846, 14, "log"], [834, 19, 846, 17], [834, 20, 846, 18], [834, 54, 846, 52], [834, 56, 846, 54, "status"], [834, 62, 846, 60], [834, 63, 846, 61], [835, 8, 848, 6], [835, 12, 848, 10, "status"], [835, 18, 848, 16], [835, 19, 848, 17, "status"], [835, 25, 848, 23], [835, 30, 848, 28], [835, 41, 848, 39], [835, 43, 848, 41], [836, 10, 849, 8, "console"], [836, 17, 849, 15], [836, 18, 849, 16, "log"], [836, 21, 849, 19], [836, 22, 849, 20], [836, 73, 849, 71], [836, 74, 849, 72], [837, 10, 850, 8, "setProcessingProgress"], [837, 31, 850, 29], [837, 32, 850, 30], [837, 35, 850, 33], [837, 36, 850, 34], [838, 10, 851, 8, "setProcessingState"], [838, 28, 851, 26], [838, 29, 851, 27], [838, 40, 851, 38], [838, 41, 851, 39], [839, 10, 852, 8], [840, 10, 853, 8], [840, 16, 853, 14, "result"], [840, 22, 853, 20], [840, 25, 853, 23], [841, 12, 854, 10, "imageUrl"], [841, 20, 854, 18], [841, 22, 854, 20, "status"], [841, 28, 854, 26], [841, 29, 854, 27, "publicUrl"], [841, 38, 854, 36], [842, 12, 854, 38], [843, 12, 855, 10, "localUri"], [843, 20, 855, 18], [843, 22, 855, 20, "capturedPhoto"], [843, 35, 855, 33], [843, 39, 855, 37, "status"], [843, 45, 855, 43], [843, 46, 855, 44, "publicUrl"], [843, 55, 855, 53], [844, 12, 855, 55], [845, 12, 856, 10, "challengeCode"], [845, 25, 856, 23], [845, 27, 856, 25, "challengeCode"], [845, 40, 856, 38], [845, 44, 856, 42], [845, 46, 856, 44], [846, 12, 857, 10, "timestamp"], [846, 21, 857, 19], [847, 12, 858, 10, "processingStatus"], [847, 28, 858, 26], [847, 30, 858, 28], [848, 10, 859, 8], [848, 11, 859, 9], [849, 10, 860, 8, "console"], [849, 17, 860, 15], [849, 18, 860, 16, "log"], [849, 21, 860, 19], [849, 22, 860, 20], [849, 57, 860, 55], [849, 59, 860, 57, "result"], [849, 65, 860, 63], [849, 66, 860, 64], [850, 10, 861, 8, "onComplete"], [850, 20, 861, 18], [850, 21, 861, 19, "result"], [850, 27, 861, 25], [850, 28, 861, 26], [851, 10, 862, 8], [852, 8, 863, 6], [852, 9, 863, 7], [852, 15, 863, 13], [852, 19, 863, 17, "status"], [852, 25, 863, 23], [852, 26, 863, 24, "status"], [852, 32, 863, 30], [852, 37, 863, 35], [852, 45, 863, 43], [852, 47, 863, 45], [853, 10, 864, 8, "console"], [853, 17, 864, 15], [853, 18, 864, 16, "error"], [853, 23, 864, 21], [853, 24, 864, 22], [853, 60, 864, 58], [853, 62, 864, 60, "status"], [853, 68, 864, 66], [853, 69, 864, 67, "error"], [853, 74, 864, 72], [853, 75, 864, 73], [854, 10, 865, 8], [854, 16, 865, 14], [854, 20, 865, 18, "Error"], [854, 25, 865, 23], [854, 26, 865, 24, "status"], [854, 32, 865, 30], [854, 33, 865, 31, "error"], [854, 38, 865, 36], [854, 42, 865, 40], [854, 61, 865, 59], [854, 62, 865, 60], [855, 8, 866, 6], [855, 9, 866, 7], [855, 15, 866, 13], [856, 10, 867, 8], [857, 10, 868, 8], [857, 16, 868, 14, "progressValue"], [857, 29, 868, 27], [857, 32, 868, 30], [857, 34, 868, 32], [857, 37, 868, 36, "attempts"], [857, 45, 868, 44], [857, 48, 868, 47, "MAX_ATTEMPTS"], [857, 60, 868, 59], [857, 63, 868, 63], [857, 65, 868, 65], [858, 10, 869, 8, "console"], [858, 17, 869, 15], [858, 18, 869, 16, "log"], [858, 21, 869, 19], [858, 22, 869, 20], [858, 71, 869, 69, "progressValue"], [858, 84, 869, 82], [858, 87, 869, 85], [858, 88, 869, 86], [859, 10, 870, 8, "setProcessingProgress"], [859, 31, 870, 29], [859, 32, 870, 30, "progressValue"], [859, 45, 870, 43], [859, 46, 870, 44], [860, 10, 872, 8, "setTimeout"], [860, 20, 872, 18], [860, 21, 872, 19], [860, 27, 872, 25], [861, 12, 873, 10, "pollForCompletion"], [861, 29, 873, 27], [861, 30, 873, 28, "jobId"], [861, 35, 873, 33], [861, 37, 873, 35, "timestamp"], [861, 46, 873, 44], [861, 48, 873, 46, "attempts"], [861, 56, 873, 54], [861, 59, 873, 57], [861, 60, 873, 58], [861, 61, 873, 59], [862, 10, 874, 8], [862, 11, 874, 9], [862, 13, 874, 11, "POLL_INTERVAL"], [862, 26, 874, 24], [862, 27, 874, 25], [863, 8, 875, 6], [864, 6, 876, 4], [864, 7, 876, 5], [864, 8, 876, 6], [864, 15, 876, 13, "error"], [864, 20, 876, 18], [864, 22, 876, 20], [865, 8, 877, 6, "console"], [865, 15, 877, 13], [865, 16, 877, 14, "error"], [865, 21, 877, 19], [865, 22, 877, 20], [865, 54, 877, 52], [865, 56, 877, 54, "error"], [865, 61, 877, 59], [865, 62, 877, 60], [866, 8, 878, 6, "setErrorMessage"], [866, 23, 878, 21], [866, 24, 878, 22], [866, 62, 878, 60, "error"], [866, 67, 878, 65], [866, 68, 878, 66, "message"], [866, 75, 878, 73], [866, 77, 878, 75], [866, 78, 878, 76], [867, 8, 879, 6, "setProcessingState"], [867, 26, 879, 24], [867, 27, 879, 25], [867, 34, 879, 32], [867, 35, 879, 33], [868, 6, 880, 4], [869, 4, 881, 2], [869, 5, 881, 3], [870, 4, 882, 2], [871, 4, 883, 2], [871, 10, 883, 8, "getAuthToken"], [871, 22, 883, 20], [871, 25, 883, 23], [871, 31, 883, 23, "getAuthToken"], [871, 32, 883, 23], [871, 37, 883, 52], [872, 6, 884, 4], [873, 6, 885, 4], [874, 6, 886, 4], [874, 13, 886, 11], [874, 30, 886, 28], [875, 4, 887, 2], [875, 5, 887, 3], [877, 4, 889, 2], [878, 4, 890, 2], [878, 10, 890, 8, "retryCapture"], [878, 22, 890, 20], [878, 25, 890, 23], [878, 29, 890, 23, "useCallback"], [878, 47, 890, 34], [878, 49, 890, 35], [878, 55, 890, 41], [879, 6, 891, 4, "console"], [879, 13, 891, 11], [879, 14, 891, 12, "log"], [879, 17, 891, 15], [879, 18, 891, 16], [879, 55, 891, 53], [879, 56, 891, 54], [880, 6, 892, 4, "setProcessingState"], [880, 24, 892, 22], [880, 25, 892, 23], [880, 31, 892, 29], [880, 32, 892, 30], [881, 6, 893, 4, "setErrorMessage"], [881, 21, 893, 19], [881, 22, 893, 20], [881, 24, 893, 22], [881, 25, 893, 23], [882, 6, 894, 4, "setCapturedPhoto"], [882, 22, 894, 20], [882, 23, 894, 21], [882, 25, 894, 23], [882, 26, 894, 24], [883, 6, 895, 4, "setProcessingProgress"], [883, 27, 895, 25], [883, 28, 895, 26], [883, 29, 895, 27], [883, 30, 895, 28], [884, 4, 896, 2], [884, 5, 896, 3], [884, 7, 896, 5], [884, 9, 896, 7], [884, 10, 896, 8], [885, 4, 897, 2], [886, 4, 898, 2], [886, 8, 898, 2, "useEffect"], [886, 24, 898, 11], [886, 26, 898, 12], [886, 32, 898, 18], [887, 6, 899, 4, "console"], [887, 13, 899, 11], [887, 14, 899, 12, "log"], [887, 17, 899, 15], [887, 18, 899, 16], [887, 53, 899, 51], [887, 55, 899, 53, "permission"], [887, 65, 899, 63], [887, 66, 899, 64], [888, 6, 900, 4], [888, 10, 900, 8, "permission"], [888, 20, 900, 18], [888, 22, 900, 20], [889, 8, 901, 6, "console"], [889, 15, 901, 13], [889, 16, 901, 14, "log"], [889, 19, 901, 17], [889, 20, 901, 18], [889, 57, 901, 55], [889, 59, 901, 57, "permission"], [889, 69, 901, 67], [889, 70, 901, 68, "granted"], [889, 77, 901, 75], [889, 78, 901, 76], [890, 6, 902, 4], [891, 4, 903, 2], [891, 5, 903, 3], [891, 7, 903, 5], [891, 8, 903, 6, "permission"], [891, 18, 903, 16], [891, 19, 903, 17], [891, 20, 903, 18], [892, 4, 904, 2], [893, 4, 905, 2], [893, 8, 905, 6], [893, 9, 905, 7, "permission"], [893, 19, 905, 17], [893, 21, 905, 19], [894, 6, 906, 4, "console"], [894, 13, 906, 11], [894, 14, 906, 12, "log"], [894, 17, 906, 15], [894, 18, 906, 16], [894, 67, 906, 65], [894, 68, 906, 66], [895, 6, 907, 4], [895, 26, 908, 6], [895, 30, 908, 6, "_jsxDevRuntime"], [895, 44, 908, 6], [895, 45, 908, 6, "jsxDEV"], [895, 51, 908, 6], [895, 53, 908, 7, "_View"], [895, 58, 908, 7], [895, 59, 908, 7, "default"], [895, 66, 908, 11], [896, 8, 908, 12, "style"], [896, 13, 908, 17], [896, 15, 908, 19, "styles"], [896, 21, 908, 25], [896, 22, 908, 26, "container"], [896, 31, 908, 36], [897, 8, 908, 36, "children"], [897, 16, 908, 36], [897, 32, 909, 8], [897, 36, 909, 8, "_jsxDevRuntime"], [897, 50, 909, 8], [897, 51, 909, 8, "jsxDEV"], [897, 57, 909, 8], [897, 59, 909, 9, "_ActivityIndicator"], [897, 77, 909, 9], [897, 78, 909, 9, "default"], [897, 85, 909, 26], [898, 10, 909, 27, "size"], [898, 14, 909, 31], [898, 16, 909, 32], [898, 23, 909, 39], [899, 10, 909, 40, "color"], [899, 15, 909, 45], [899, 17, 909, 46], [900, 8, 909, 55], [901, 10, 909, 55, "fileName"], [901, 18, 909, 55], [901, 20, 909, 55, "_jsxFileName"], [901, 32, 909, 55], [902, 10, 909, 55, "lineNumber"], [902, 20, 909, 55], [903, 10, 909, 55, "columnNumber"], [903, 22, 909, 55], [904, 8, 909, 55], [904, 15, 909, 57], [904, 16, 909, 58], [904, 31, 910, 8], [904, 35, 910, 8, "_jsxDevRuntime"], [904, 49, 910, 8], [904, 50, 910, 8, "jsxDEV"], [904, 56, 910, 8], [904, 58, 910, 9, "_Text"], [904, 63, 910, 9], [904, 64, 910, 9, "default"], [904, 71, 910, 13], [905, 10, 910, 14, "style"], [905, 15, 910, 19], [905, 17, 910, 21, "styles"], [905, 23, 910, 27], [905, 24, 910, 28, "loadingText"], [905, 35, 910, 40], [906, 10, 910, 40, "children"], [906, 18, 910, 40], [906, 20, 910, 41], [907, 8, 910, 58], [908, 10, 910, 58, "fileName"], [908, 18, 910, 58], [908, 20, 910, 58, "_jsxFileName"], [908, 32, 910, 58], [909, 10, 910, 58, "lineNumber"], [909, 20, 910, 58], [910, 10, 910, 58, "columnNumber"], [910, 22, 910, 58], [911, 8, 910, 58], [911, 15, 910, 64], [911, 16, 910, 65], [912, 6, 910, 65], [913, 8, 910, 65, "fileName"], [913, 16, 910, 65], [913, 18, 910, 65, "_jsxFileName"], [913, 30, 910, 65], [914, 8, 910, 65, "lineNumber"], [914, 18, 910, 65], [915, 8, 910, 65, "columnNumber"], [915, 20, 910, 65], [916, 6, 910, 65], [916, 13, 911, 12], [916, 14, 911, 13], [917, 4, 913, 2], [918, 4, 914, 2], [918, 8, 914, 6], [918, 9, 914, 7, "permission"], [918, 19, 914, 17], [918, 20, 914, 18, "granted"], [918, 27, 914, 25], [918, 29, 914, 27], [919, 6, 915, 4, "console"], [919, 13, 915, 11], [919, 14, 915, 12, "log"], [919, 17, 915, 15], [919, 18, 915, 16], [919, 93, 915, 91], [919, 94, 915, 92], [920, 6, 916, 4], [920, 26, 917, 6], [920, 30, 917, 6, "_jsxDevRuntime"], [920, 44, 917, 6], [920, 45, 917, 6, "jsxDEV"], [920, 51, 917, 6], [920, 53, 917, 7, "_View"], [920, 58, 917, 7], [920, 59, 917, 7, "default"], [920, 66, 917, 11], [921, 8, 917, 12, "style"], [921, 13, 917, 17], [921, 15, 917, 19, "styles"], [921, 21, 917, 25], [921, 22, 917, 26, "container"], [921, 31, 917, 36], [922, 8, 917, 36, "children"], [922, 16, 917, 36], [922, 31, 918, 8], [922, 35, 918, 8, "_jsxDevRuntime"], [922, 49, 918, 8], [922, 50, 918, 8, "jsxDEV"], [922, 56, 918, 8], [922, 58, 918, 9, "_View"], [922, 63, 918, 9], [922, 64, 918, 9, "default"], [922, 71, 918, 13], [923, 10, 918, 14, "style"], [923, 15, 918, 19], [923, 17, 918, 21, "styles"], [923, 23, 918, 27], [923, 24, 918, 28, "permissionContent"], [923, 41, 918, 46], [924, 10, 918, 46, "children"], [924, 18, 918, 46], [924, 34, 919, 10], [924, 38, 919, 10, "_jsxDevRuntime"], [924, 52, 919, 10], [924, 53, 919, 10, "jsxDEV"], [924, 59, 919, 10], [924, 61, 919, 11, "_lucideReactNative"], [924, 79, 919, 11], [924, 80, 919, 11, "Camera"], [924, 86, 919, 21], [925, 12, 919, 22, "size"], [925, 16, 919, 26], [925, 18, 919, 28], [925, 20, 919, 31], [926, 12, 919, 32, "color"], [926, 17, 919, 37], [926, 19, 919, 38], [927, 10, 919, 47], [928, 12, 919, 47, "fileName"], [928, 20, 919, 47], [928, 22, 919, 47, "_jsxFileName"], [928, 34, 919, 47], [929, 12, 919, 47, "lineNumber"], [929, 22, 919, 47], [930, 12, 919, 47, "columnNumber"], [930, 24, 919, 47], [931, 10, 919, 47], [931, 17, 919, 49], [931, 18, 919, 50], [931, 33, 920, 10], [931, 37, 920, 10, "_jsxDevRuntime"], [931, 51, 920, 10], [931, 52, 920, 10, "jsxDEV"], [931, 58, 920, 10], [931, 60, 920, 11, "_Text"], [931, 65, 920, 11], [931, 66, 920, 11, "default"], [931, 73, 920, 15], [932, 12, 920, 16, "style"], [932, 17, 920, 21], [932, 19, 920, 23, "styles"], [932, 25, 920, 29], [932, 26, 920, 30, "permissionTitle"], [932, 41, 920, 46], [933, 12, 920, 46, "children"], [933, 20, 920, 46], [933, 22, 920, 47], [934, 10, 920, 73], [935, 12, 920, 73, "fileName"], [935, 20, 920, 73], [935, 22, 920, 73, "_jsxFileName"], [935, 34, 920, 73], [936, 12, 920, 73, "lineNumber"], [936, 22, 920, 73], [937, 12, 920, 73, "columnNumber"], [937, 24, 920, 73], [938, 10, 920, 73], [938, 17, 920, 79], [938, 18, 920, 80], [938, 33, 921, 10], [938, 37, 921, 10, "_jsxDevRuntime"], [938, 51, 921, 10], [938, 52, 921, 10, "jsxDEV"], [938, 58, 921, 10], [938, 60, 921, 11, "_Text"], [938, 65, 921, 11], [938, 66, 921, 11, "default"], [938, 73, 921, 15], [939, 12, 921, 16, "style"], [939, 17, 921, 21], [939, 19, 921, 23, "styles"], [939, 25, 921, 29], [939, 26, 921, 30, "permissionDescription"], [939, 47, 921, 52], [940, 12, 921, 52, "children"], [940, 20, 921, 52], [940, 22, 921, 53], [941, 10, 924, 10], [942, 12, 924, 10, "fileName"], [942, 20, 924, 10], [942, 22, 924, 10, "_jsxFileName"], [942, 34, 924, 10], [943, 12, 924, 10, "lineNumber"], [943, 22, 924, 10], [944, 12, 924, 10, "columnNumber"], [944, 24, 924, 10], [945, 10, 924, 10], [945, 17, 924, 16], [945, 18, 924, 17], [945, 33, 925, 10], [945, 37, 925, 10, "_jsxDevRuntime"], [945, 51, 925, 10], [945, 52, 925, 10, "jsxDEV"], [945, 58, 925, 10], [945, 60, 925, 11, "_TouchableOpacity"], [945, 77, 925, 11], [945, 78, 925, 11, "default"], [945, 85, 925, 27], [946, 12, 925, 28, "onPress"], [946, 19, 925, 35], [946, 21, 925, 37, "requestPermission"], [946, 38, 925, 55], [947, 12, 925, 56, "style"], [947, 17, 925, 61], [947, 19, 925, 63, "styles"], [947, 25, 925, 69], [947, 26, 925, 70, "primaryButton"], [947, 39, 925, 84], [948, 12, 925, 84, "children"], [948, 20, 925, 84], [948, 35, 926, 12], [948, 39, 926, 12, "_jsxDevRuntime"], [948, 53, 926, 12], [948, 54, 926, 12, "jsxDEV"], [948, 60, 926, 12], [948, 62, 926, 13, "_Text"], [948, 67, 926, 13], [948, 68, 926, 13, "default"], [948, 75, 926, 17], [949, 14, 926, 18, "style"], [949, 19, 926, 23], [949, 21, 926, 25, "styles"], [949, 27, 926, 31], [949, 28, 926, 32, "primaryButtonText"], [949, 45, 926, 50], [950, 14, 926, 50, "children"], [950, 22, 926, 50], [950, 24, 926, 51], [951, 12, 926, 67], [952, 14, 926, 67, "fileName"], [952, 22, 926, 67], [952, 24, 926, 67, "_jsxFileName"], [952, 36, 926, 67], [953, 14, 926, 67, "lineNumber"], [953, 24, 926, 67], [954, 14, 926, 67, "columnNumber"], [954, 26, 926, 67], [955, 12, 926, 67], [955, 19, 926, 73], [956, 10, 926, 74], [957, 12, 926, 74, "fileName"], [957, 20, 926, 74], [957, 22, 926, 74, "_jsxFileName"], [957, 34, 926, 74], [958, 12, 926, 74, "lineNumber"], [958, 22, 926, 74], [959, 12, 926, 74, "columnNumber"], [959, 24, 926, 74], [960, 10, 926, 74], [960, 17, 927, 28], [960, 18, 927, 29], [960, 33, 928, 10], [960, 37, 928, 10, "_jsxDevRuntime"], [960, 51, 928, 10], [960, 52, 928, 10, "jsxDEV"], [960, 58, 928, 10], [960, 60, 928, 11, "_TouchableOpacity"], [960, 77, 928, 11], [960, 78, 928, 11, "default"], [960, 85, 928, 27], [961, 12, 928, 28, "onPress"], [961, 19, 928, 35], [961, 21, 928, 37, "onCancel"], [961, 29, 928, 46], [962, 12, 928, 47, "style"], [962, 17, 928, 52], [962, 19, 928, 54, "styles"], [962, 25, 928, 60], [962, 26, 928, 61, "secondaryButton"], [962, 41, 928, 77], [963, 12, 928, 77, "children"], [963, 20, 928, 77], [963, 35, 929, 12], [963, 39, 929, 12, "_jsxDevRuntime"], [963, 53, 929, 12], [963, 54, 929, 12, "jsxDEV"], [963, 60, 929, 12], [963, 62, 929, 13, "_Text"], [963, 67, 929, 13], [963, 68, 929, 13, "default"], [963, 75, 929, 17], [964, 14, 929, 18, "style"], [964, 19, 929, 23], [964, 21, 929, 25, "styles"], [964, 27, 929, 31], [964, 28, 929, 32, "secondaryButtonText"], [964, 47, 929, 52], [965, 14, 929, 52, "children"], [965, 22, 929, 52], [965, 24, 929, 53], [966, 12, 929, 59], [967, 14, 929, 59, "fileName"], [967, 22, 929, 59], [967, 24, 929, 59, "_jsxFileName"], [967, 36, 929, 59], [968, 14, 929, 59, "lineNumber"], [968, 24, 929, 59], [969, 14, 929, 59, "columnNumber"], [969, 26, 929, 59], [970, 12, 929, 59], [970, 19, 929, 65], [971, 10, 929, 66], [972, 12, 929, 66, "fileName"], [972, 20, 929, 66], [972, 22, 929, 66, "_jsxFileName"], [972, 34, 929, 66], [973, 12, 929, 66, "lineNumber"], [973, 22, 929, 66], [974, 12, 929, 66, "columnNumber"], [974, 24, 929, 66], [975, 10, 929, 66], [975, 17, 930, 28], [975, 18, 930, 29], [976, 8, 930, 29], [977, 10, 930, 29, "fileName"], [977, 18, 930, 29], [977, 20, 930, 29, "_jsxFileName"], [977, 32, 930, 29], [978, 10, 930, 29, "lineNumber"], [978, 20, 930, 29], [979, 10, 930, 29, "columnNumber"], [979, 22, 930, 29], [980, 8, 930, 29], [980, 15, 931, 14], [981, 6, 931, 15], [982, 8, 931, 15, "fileName"], [982, 16, 931, 15], [982, 18, 931, 15, "_jsxFileName"], [982, 30, 931, 15], [983, 8, 931, 15, "lineNumber"], [983, 18, 931, 15], [984, 8, 931, 15, "columnNumber"], [984, 20, 931, 15], [985, 6, 931, 15], [985, 13, 932, 12], [985, 14, 932, 13], [986, 4, 934, 2], [987, 4, 935, 2], [988, 4, 936, 2, "console"], [988, 11, 936, 9], [988, 12, 936, 10, "log"], [988, 15, 936, 13], [988, 16, 936, 14], [988, 55, 936, 53], [988, 56, 936, 54], [989, 4, 938, 2], [989, 24, 939, 4], [989, 28, 939, 4, "_jsxDevRuntime"], [989, 42, 939, 4], [989, 43, 939, 4, "jsxDEV"], [989, 49, 939, 4], [989, 51, 939, 5, "_View"], [989, 56, 939, 5], [989, 57, 939, 5, "default"], [989, 64, 939, 9], [990, 6, 939, 10, "style"], [990, 11, 939, 15], [990, 13, 939, 17, "styles"], [990, 19, 939, 23], [990, 20, 939, 24, "container"], [990, 29, 939, 34], [991, 6, 939, 34, "children"], [991, 14, 939, 34], [991, 30, 941, 6], [991, 34, 941, 6, "_jsxDevRuntime"], [991, 48, 941, 6], [991, 49, 941, 6, "jsxDEV"], [991, 55, 941, 6], [991, 57, 941, 7, "_View"], [991, 62, 941, 7], [991, 63, 941, 7, "default"], [991, 70, 941, 11], [992, 8, 941, 12, "style"], [992, 13, 941, 17], [992, 15, 941, 19, "styles"], [992, 21, 941, 25], [992, 22, 941, 26, "cameraContainer"], [992, 37, 941, 42], [993, 8, 941, 43, "id"], [993, 10, 941, 45], [993, 12, 941, 46], [993, 29, 941, 63], [994, 8, 941, 63, "children"], [994, 16, 941, 63], [994, 32, 942, 8], [994, 36, 942, 8, "_jsxDevRuntime"], [994, 50, 942, 8], [994, 51, 942, 8, "jsxDEV"], [994, 57, 942, 8], [994, 59, 942, 9, "_expoCamera"], [994, 70, 942, 9], [994, 71, 942, 9, "CameraView"], [994, 81, 942, 19], [995, 10, 943, 10, "ref"], [995, 13, 943, 13], [995, 15, 943, 15, "cameraRef"], [995, 24, 943, 25], [996, 10, 944, 10, "style"], [996, 15, 944, 15], [996, 17, 944, 17], [996, 18, 945, 12, "styles"], [996, 24, 945, 18], [996, 25, 945, 19, "camera"], [996, 31, 945, 25], [996, 33, 946, 12], [997, 12, 947, 14, "backgroundColor"], [997, 27, 947, 29], [997, 29, 947, 31], [997, 38, 947, 40], [998, 12, 948, 14], [999, 12, 949, 14, "opacity"], [999, 19, 949, 21], [999, 21, 949, 24, "isCameraReady"], [999, 34, 949, 37], [999, 38, 949, 41, "previewBlurEnabled"], [999, 56, 949, 59], [999, 59, 949, 63], [999, 60, 949, 64], [999, 63, 949, 67], [1000, 10, 950, 12], [1000, 11, 950, 13], [1000, 12, 951, 12], [1001, 10, 952, 10, "facing"], [1001, 16, 952, 16], [1001, 18, 952, 17], [1001, 24, 952, 23], [1002, 10, 953, 10, "onLayout"], [1002, 18, 953, 18], [1002, 20, 953, 21, "e"], [1002, 21, 953, 22], [1002, 25, 953, 27], [1003, 12, 954, 12, "console"], [1003, 19, 954, 19], [1003, 20, 954, 20, "log"], [1003, 23, 954, 23], [1003, 24, 954, 24], [1003, 56, 954, 56], [1003, 58, 954, 58, "e"], [1003, 59, 954, 59], [1003, 60, 954, 60, "nativeEvent"], [1003, 71, 954, 71], [1003, 72, 954, 72, "layout"], [1003, 78, 954, 78], [1003, 79, 954, 79], [1004, 12, 955, 12, "setViewSize"], [1004, 23, 955, 23], [1004, 24, 955, 24], [1005, 14, 955, 26, "width"], [1005, 19, 955, 31], [1005, 21, 955, 33, "e"], [1005, 22, 955, 34], [1005, 23, 955, 35, "nativeEvent"], [1005, 34, 955, 46], [1005, 35, 955, 47, "layout"], [1005, 41, 955, 53], [1005, 42, 955, 54, "width"], [1005, 47, 955, 59], [1006, 14, 955, 61, "height"], [1006, 20, 955, 67], [1006, 22, 955, 69, "e"], [1006, 23, 955, 70], [1006, 24, 955, 71, "nativeEvent"], [1006, 35, 955, 82], [1006, 36, 955, 83, "layout"], [1006, 42, 955, 89], [1006, 43, 955, 90, "height"], [1007, 12, 955, 97], [1007, 13, 955, 98], [1007, 14, 955, 99], [1008, 10, 956, 10], [1008, 11, 956, 12], [1009, 10, 957, 10, "onCameraReady"], [1009, 23, 957, 23], [1009, 25, 957, 25, "onCameraReady"], [1009, 26, 957, 25], [1009, 31, 957, 31], [1010, 12, 958, 12, "console"], [1010, 19, 958, 19], [1010, 20, 958, 20, "log"], [1010, 23, 958, 23], [1010, 24, 958, 24], [1010, 55, 958, 55], [1010, 56, 958, 56], [1011, 12, 959, 12, "setIsCameraReady"], [1011, 28, 959, 28], [1011, 29, 959, 29], [1011, 33, 959, 33], [1011, 34, 959, 34], [1011, 35, 959, 35], [1011, 36, 959, 36], [1012, 10, 960, 10], [1012, 11, 960, 12], [1013, 10, 961, 10, "onMountError"], [1013, 22, 961, 22], [1013, 24, 961, 25, "error"], [1013, 29, 961, 30], [1013, 33, 961, 35], [1014, 12, 962, 12, "console"], [1014, 19, 962, 19], [1014, 20, 962, 20, "error"], [1014, 25, 962, 25], [1014, 26, 962, 26], [1014, 63, 962, 63], [1014, 65, 962, 65, "error"], [1014, 70, 962, 70], [1014, 71, 962, 71], [1015, 12, 963, 12, "setErrorMessage"], [1015, 27, 963, 27], [1015, 28, 963, 28], [1015, 57, 963, 57], [1015, 58, 963, 58], [1016, 12, 964, 12, "setProcessingState"], [1016, 30, 964, 30], [1016, 31, 964, 31], [1016, 38, 964, 38], [1016, 39, 964, 39], [1017, 10, 965, 10], [1018, 8, 965, 12], [1019, 10, 965, 12, "fileName"], [1019, 18, 965, 12], [1019, 20, 965, 12, "_jsxFileName"], [1019, 32, 965, 12], [1020, 10, 965, 12, "lineNumber"], [1020, 20, 965, 12], [1021, 10, 965, 12, "columnNumber"], [1021, 22, 965, 12], [1022, 8, 965, 12], [1022, 15, 966, 9], [1022, 16, 966, 10], [1022, 18, 968, 9], [1022, 19, 968, 10, "isCameraReady"], [1022, 32, 968, 23], [1022, 49, 969, 10], [1022, 53, 969, 10, "_jsxDevRuntime"], [1022, 67, 969, 10], [1022, 68, 969, 10, "jsxDEV"], [1022, 74, 969, 10], [1022, 76, 969, 11, "_View"], [1022, 81, 969, 11], [1022, 82, 969, 11, "default"], [1022, 89, 969, 15], [1023, 10, 969, 16, "style"], [1023, 15, 969, 21], [1023, 17, 969, 23], [1023, 18, 969, 24, "StyleSheet"], [1023, 37, 969, 34], [1023, 38, 969, 35, "absoluteFill"], [1023, 50, 969, 47], [1023, 52, 969, 49], [1024, 12, 969, 51, "backgroundColor"], [1024, 27, 969, 66], [1024, 29, 969, 68], [1024, 49, 969, 88], [1025, 12, 969, 90, "justifyContent"], [1025, 26, 969, 104], [1025, 28, 969, 106], [1025, 36, 969, 114], [1026, 12, 969, 116, "alignItems"], [1026, 22, 969, 126], [1026, 24, 969, 128], [1026, 32, 969, 136], [1027, 12, 969, 138, "zIndex"], [1027, 18, 969, 144], [1027, 20, 969, 146], [1028, 10, 969, 151], [1028, 11, 969, 152], [1028, 12, 969, 154], [1029, 10, 969, 154, "children"], [1029, 18, 969, 154], [1029, 33, 970, 12], [1029, 37, 970, 12, "_jsxDevRuntime"], [1029, 51, 970, 12], [1029, 52, 970, 12, "jsxDEV"], [1029, 58, 970, 12], [1029, 60, 970, 13, "_View"], [1029, 65, 970, 13], [1029, 66, 970, 13, "default"], [1029, 73, 970, 17], [1030, 12, 970, 18, "style"], [1030, 17, 970, 23], [1030, 19, 970, 25], [1031, 14, 970, 27, "backgroundColor"], [1031, 29, 970, 42], [1031, 31, 970, 44], [1031, 51, 970, 64], [1032, 14, 970, 66, "padding"], [1032, 21, 970, 73], [1032, 23, 970, 75], [1032, 25, 970, 77], [1033, 14, 970, 79, "borderRadius"], [1033, 26, 970, 91], [1033, 28, 970, 93], [1033, 30, 970, 95], [1034, 14, 970, 97, "alignItems"], [1034, 24, 970, 107], [1034, 26, 970, 109], [1035, 12, 970, 118], [1035, 13, 970, 120], [1036, 12, 970, 120, "children"], [1036, 20, 970, 120], [1036, 36, 971, 14], [1036, 40, 971, 14, "_jsxDevRuntime"], [1036, 54, 971, 14], [1036, 55, 971, 14, "jsxDEV"], [1036, 61, 971, 14], [1036, 63, 971, 15, "_ActivityIndicator"], [1036, 81, 971, 15], [1036, 82, 971, 15, "default"], [1036, 89, 971, 32], [1037, 14, 971, 33, "size"], [1037, 18, 971, 37], [1037, 20, 971, 38], [1037, 27, 971, 45], [1038, 14, 971, 46, "color"], [1038, 19, 971, 51], [1038, 21, 971, 52], [1038, 30, 971, 61], [1039, 14, 971, 62, "style"], [1039, 19, 971, 67], [1039, 21, 971, 69], [1040, 16, 971, 71, "marginBottom"], [1040, 28, 971, 83], [1040, 30, 971, 85], [1041, 14, 971, 88], [1042, 12, 971, 90], [1043, 14, 971, 90, "fileName"], [1043, 22, 971, 90], [1043, 24, 971, 90, "_jsxFileName"], [1043, 36, 971, 90], [1044, 14, 971, 90, "lineNumber"], [1044, 24, 971, 90], [1045, 14, 971, 90, "columnNumber"], [1045, 26, 971, 90], [1046, 12, 971, 90], [1046, 19, 971, 92], [1046, 20, 971, 93], [1046, 35, 972, 14], [1046, 39, 972, 14, "_jsxDevRuntime"], [1046, 53, 972, 14], [1046, 54, 972, 14, "jsxDEV"], [1046, 60, 972, 14], [1046, 62, 972, 15, "_Text"], [1046, 67, 972, 15], [1046, 68, 972, 15, "default"], [1046, 75, 972, 19], [1047, 14, 972, 20, "style"], [1047, 19, 972, 25], [1047, 21, 972, 27], [1048, 16, 972, 29, "color"], [1048, 21, 972, 34], [1048, 23, 972, 36], [1048, 29, 972, 42], [1049, 16, 972, 44, "fontSize"], [1049, 24, 972, 52], [1049, 26, 972, 54], [1049, 28, 972, 56], [1050, 16, 972, 58, "fontWeight"], [1050, 26, 972, 68], [1050, 28, 972, 70], [1051, 14, 972, 76], [1051, 15, 972, 78], [1052, 14, 972, 78, "children"], [1052, 22, 972, 78], [1052, 24, 972, 79], [1053, 12, 972, 101], [1054, 14, 972, 101, "fileName"], [1054, 22, 972, 101], [1054, 24, 972, 101, "_jsxFileName"], [1054, 36, 972, 101], [1055, 14, 972, 101, "lineNumber"], [1055, 24, 972, 101], [1056, 14, 972, 101, "columnNumber"], [1056, 26, 972, 101], [1057, 12, 972, 101], [1057, 19, 972, 107], [1057, 20, 972, 108], [1057, 35, 973, 14], [1057, 39, 973, 14, "_jsxDevRuntime"], [1057, 53, 973, 14], [1057, 54, 973, 14, "jsxDEV"], [1057, 60, 973, 14], [1057, 62, 973, 15, "_Text"], [1057, 67, 973, 15], [1057, 68, 973, 15, "default"], [1057, 75, 973, 19], [1058, 14, 973, 20, "style"], [1058, 19, 973, 25], [1058, 21, 973, 27], [1059, 16, 973, 29, "color"], [1059, 21, 973, 34], [1059, 23, 973, 36], [1059, 32, 973, 45], [1060, 16, 973, 47, "fontSize"], [1060, 24, 973, 55], [1060, 26, 973, 57], [1060, 28, 973, 59], [1061, 16, 973, 61, "marginTop"], [1061, 25, 973, 70], [1061, 27, 973, 72], [1062, 14, 973, 74], [1062, 15, 973, 76], [1063, 14, 973, 76, "children"], [1063, 22, 973, 76], [1063, 24, 973, 77], [1064, 12, 973, 88], [1065, 14, 973, 88, "fileName"], [1065, 22, 973, 88], [1065, 24, 973, 88, "_jsxFileName"], [1065, 36, 973, 88], [1066, 14, 973, 88, "lineNumber"], [1066, 24, 973, 88], [1067, 14, 973, 88, "columnNumber"], [1067, 26, 973, 88], [1068, 12, 973, 88], [1068, 19, 973, 94], [1068, 20, 973, 95], [1069, 10, 973, 95], [1070, 12, 973, 95, "fileName"], [1070, 20, 973, 95], [1070, 22, 973, 95, "_jsxFileName"], [1070, 34, 973, 95], [1071, 12, 973, 95, "lineNumber"], [1071, 22, 973, 95], [1072, 12, 973, 95, "columnNumber"], [1072, 24, 973, 95], [1073, 10, 973, 95], [1073, 17, 974, 18], [1074, 8, 974, 19], [1075, 10, 974, 19, "fileName"], [1075, 18, 974, 19], [1075, 20, 974, 19, "_jsxFileName"], [1075, 32, 974, 19], [1076, 10, 974, 19, "lineNumber"], [1076, 20, 974, 19], [1077, 10, 974, 19, "columnNumber"], [1077, 22, 974, 19], [1078, 8, 974, 19], [1078, 15, 975, 16], [1078, 16, 976, 9], [1078, 18, 979, 9, "isCameraReady"], [1078, 31, 979, 22], [1078, 35, 979, 26, "previewBlurEnabled"], [1078, 53, 979, 44], [1078, 57, 979, 48, "viewSize"], [1078, 65, 979, 56], [1078, 66, 979, 57, "width"], [1078, 71, 979, 62], [1078, 74, 979, 65], [1078, 75, 979, 66], [1078, 92, 980, 10], [1078, 96, 980, 10, "_jsxDevRuntime"], [1078, 110, 980, 10], [1078, 111, 980, 10, "jsxDEV"], [1078, 117, 980, 10], [1078, 119, 980, 10, "_jsxDevRuntime"], [1078, 133, 980, 10], [1078, 134, 980, 10, "Fragment"], [1078, 142, 980, 10], [1079, 10, 980, 10, "children"], [1079, 18, 980, 10], [1079, 33, 982, 12], [1079, 37, 982, 12, "_jsxDevRuntime"], [1079, 51, 982, 12], [1079, 52, 982, 12, "jsxDEV"], [1079, 58, 982, 12], [1079, 60, 982, 13, "_BlazeFaceCanvas"], [1079, 76, 982, 13], [1079, 77, 982, 13, "default"], [1079, 84, 982, 28], [1080, 12, 982, 29, "containerId"], [1080, 23, 982, 40], [1080, 25, 982, 41], [1080, 42, 982, 58], [1081, 12, 982, 59, "width"], [1081, 17, 982, 64], [1081, 19, 982, 66, "viewSize"], [1081, 27, 982, 74], [1081, 28, 982, 75, "width"], [1081, 33, 982, 81], [1082, 12, 982, 82, "height"], [1082, 18, 982, 88], [1082, 20, 982, 90, "viewSize"], [1082, 28, 982, 98], [1082, 29, 982, 99, "height"], [1083, 10, 982, 106], [1084, 12, 982, 106, "fileName"], [1084, 20, 982, 106], [1084, 22, 982, 106, "_jsxFileName"], [1084, 34, 982, 106], [1085, 12, 982, 106, "lineNumber"], [1085, 22, 982, 106], [1086, 12, 982, 106, "columnNumber"], [1086, 24, 982, 106], [1087, 10, 982, 106], [1087, 17, 982, 108], [1088, 8, 982, 109], [1088, 24, 984, 12], [1088, 25, 985, 9], [1088, 27, 988, 9, "isCameraReady"], [1088, 40, 988, 22], [1088, 44, 988, 26], [1088, 45, 988, 27, "previewBlurEnabled"], [1088, 63, 988, 45], [1088, 67, 988, 49, "viewSize"], [1088, 75, 988, 57], [1088, 76, 988, 58, "width"], [1088, 81, 988, 63], [1088, 84, 988, 66], [1088, 85, 988, 67], [1088, 102, 989, 10], [1088, 106, 989, 10, "_jsxDevRuntime"], [1088, 120, 989, 10], [1088, 121, 989, 10, "jsxDEV"], [1088, 127, 989, 10], [1088, 129, 989, 11, "_View"], [1088, 134, 989, 11], [1088, 135, 989, 11, "default"], [1088, 142, 989, 15], [1089, 10, 989, 16, "style"], [1089, 15, 989, 21], [1089, 17, 989, 23], [1089, 18, 989, 24, "StyleSheet"], [1089, 37, 989, 34], [1089, 38, 989, 35, "absoluteFill"], [1089, 50, 989, 47], [1089, 52, 989, 49], [1090, 12, 989, 51, "pointerEvents"], [1090, 25, 989, 64], [1090, 27, 989, 66], [1091, 10, 989, 73], [1091, 11, 989, 74], [1091, 12, 989, 76], [1092, 10, 989, 76, "children"], [1092, 18, 989, 76], [1092, 34, 991, 12], [1092, 38, 991, 12, "_jsxDevRuntime"], [1092, 52, 991, 12], [1092, 53, 991, 12, "jsxDEV"], [1092, 59, 991, 12], [1092, 61, 991, 13, "_expoBlur"], [1092, 70, 991, 13], [1092, 71, 991, 13, "BlurView"], [1092, 79, 991, 21], [1093, 12, 991, 22, "intensity"], [1093, 21, 991, 31], [1093, 23, 991, 33], [1093, 25, 991, 36], [1094, 12, 991, 37, "tint"], [1094, 16, 991, 41], [1094, 18, 991, 42], [1094, 24, 991, 48], [1095, 12, 991, 49, "style"], [1095, 17, 991, 54], [1095, 19, 991, 56], [1095, 20, 991, 57, "styles"], [1095, 26, 991, 63], [1095, 27, 991, 64, "blurZone"], [1095, 35, 991, 72], [1095, 37, 991, 74], [1096, 14, 992, 14, "left"], [1096, 18, 992, 18], [1096, 20, 992, 20], [1096, 21, 992, 21], [1097, 14, 993, 14, "top"], [1097, 17, 993, 17], [1097, 19, 993, 19, "viewSize"], [1097, 27, 993, 27], [1097, 28, 993, 28, "height"], [1097, 34, 993, 34], [1097, 37, 993, 37], [1097, 41, 993, 41], [1098, 14, 994, 14, "width"], [1098, 19, 994, 19], [1098, 21, 994, 21, "viewSize"], [1098, 29, 994, 29], [1098, 30, 994, 30, "width"], [1098, 35, 994, 35], [1099, 14, 995, 14, "height"], [1099, 20, 995, 20], [1099, 22, 995, 22, "viewSize"], [1099, 30, 995, 30], [1099, 31, 995, 31, "height"], [1099, 37, 995, 37], [1099, 40, 995, 40], [1099, 43, 995, 43], [1100, 14, 996, 14, "borderRadius"], [1100, 26, 996, 26], [1100, 28, 996, 28], [1100, 30, 996, 30], [1101, 14, 997, 14, "opacity"], [1101, 21, 997, 21], [1101, 23, 997, 23], [1102, 12, 998, 12], [1102, 13, 998, 13], [1103, 10, 998, 15], [1104, 12, 998, 15, "fileName"], [1104, 20, 998, 15], [1104, 22, 998, 15, "_jsxFileName"], [1104, 34, 998, 15], [1105, 12, 998, 15, "lineNumber"], [1105, 22, 998, 15], [1106, 12, 998, 15, "columnNumber"], [1106, 24, 998, 15], [1107, 10, 998, 15], [1107, 17, 998, 17], [1107, 18, 998, 18], [1107, 33, 1000, 12], [1107, 37, 1000, 12, "_jsxDevRuntime"], [1107, 51, 1000, 12], [1107, 52, 1000, 12, "jsxDEV"], [1107, 58, 1000, 12], [1107, 60, 1000, 13, "_expoBlur"], [1107, 69, 1000, 13], [1107, 70, 1000, 13, "BlurView"], [1107, 78, 1000, 21], [1108, 12, 1000, 22, "intensity"], [1108, 21, 1000, 31], [1108, 23, 1000, 33], [1108, 25, 1000, 36], [1109, 12, 1000, 37, "tint"], [1109, 16, 1000, 41], [1109, 18, 1000, 42], [1109, 24, 1000, 48], [1110, 12, 1000, 49, "style"], [1110, 17, 1000, 54], [1110, 19, 1000, 56], [1110, 20, 1000, 57, "styles"], [1110, 26, 1000, 63], [1110, 27, 1000, 64, "blurZone"], [1110, 35, 1000, 72], [1110, 37, 1000, 74], [1111, 14, 1001, 14, "left"], [1111, 18, 1001, 18], [1111, 20, 1001, 20, "viewSize"], [1111, 28, 1001, 28], [1111, 29, 1001, 29, "width"], [1111, 34, 1001, 34], [1111, 37, 1001, 37], [1111, 40, 1001, 40], [1111, 43, 1001, 44, "viewSize"], [1111, 51, 1001, 52], [1111, 52, 1001, 53, "width"], [1111, 57, 1001, 58], [1111, 60, 1001, 61], [1111, 63, 1001, 65], [1112, 14, 1002, 14, "top"], [1112, 17, 1002, 17], [1112, 19, 1002, 19, "viewSize"], [1112, 27, 1002, 27], [1112, 28, 1002, 28, "height"], [1112, 34, 1002, 34], [1112, 37, 1002, 37], [1112, 40, 1002, 40], [1112, 43, 1002, 44, "viewSize"], [1112, 51, 1002, 52], [1112, 52, 1002, 53, "width"], [1112, 57, 1002, 58], [1112, 60, 1002, 61], [1112, 63, 1002, 65], [1113, 14, 1003, 14, "width"], [1113, 19, 1003, 19], [1113, 21, 1003, 21, "viewSize"], [1113, 29, 1003, 29], [1113, 30, 1003, 30, "width"], [1113, 35, 1003, 35], [1113, 38, 1003, 38], [1113, 41, 1003, 41], [1114, 14, 1004, 14, "height"], [1114, 20, 1004, 20], [1114, 22, 1004, 22, "viewSize"], [1114, 30, 1004, 30], [1114, 31, 1004, 31, "width"], [1114, 36, 1004, 36], [1114, 39, 1004, 39], [1114, 42, 1004, 42], [1115, 14, 1005, 14, "borderRadius"], [1115, 26, 1005, 26], [1115, 28, 1005, 29, "viewSize"], [1115, 36, 1005, 37], [1115, 37, 1005, 38, "width"], [1115, 42, 1005, 43], [1115, 45, 1005, 46], [1115, 48, 1005, 49], [1115, 51, 1005, 53], [1115, 52, 1005, 54], [1116, 14, 1006, 14, "opacity"], [1116, 21, 1006, 21], [1116, 23, 1006, 23], [1117, 12, 1007, 12], [1117, 13, 1007, 13], [1118, 10, 1007, 15], [1119, 12, 1007, 15, "fileName"], [1119, 20, 1007, 15], [1119, 22, 1007, 15, "_jsxFileName"], [1119, 34, 1007, 15], [1120, 12, 1007, 15, "lineNumber"], [1120, 22, 1007, 15], [1121, 12, 1007, 15, "columnNumber"], [1121, 24, 1007, 15], [1122, 10, 1007, 15], [1122, 17, 1007, 17], [1122, 18, 1007, 18], [1122, 20, 1009, 13, "__DEV__"], [1122, 27, 1009, 20], [1122, 44, 1010, 14], [1122, 48, 1010, 14, "_jsxDevRuntime"], [1122, 62, 1010, 14], [1122, 63, 1010, 14, "jsxDEV"], [1122, 69, 1010, 14], [1122, 71, 1010, 15, "_View"], [1122, 76, 1010, 15], [1122, 77, 1010, 15, "default"], [1122, 84, 1010, 19], [1123, 12, 1010, 20, "style"], [1123, 17, 1010, 25], [1123, 19, 1010, 27, "styles"], [1123, 25, 1010, 33], [1123, 26, 1010, 34, "previewChip"], [1123, 37, 1010, 46], [1124, 12, 1010, 46, "children"], [1124, 20, 1010, 46], [1124, 35, 1011, 16], [1124, 39, 1011, 16, "_jsxDevRuntime"], [1124, 53, 1011, 16], [1124, 54, 1011, 16, "jsxDEV"], [1124, 60, 1011, 16], [1124, 62, 1011, 17, "_Text"], [1124, 67, 1011, 17], [1124, 68, 1011, 17, "default"], [1124, 75, 1011, 21], [1125, 14, 1011, 22, "style"], [1125, 19, 1011, 27], [1125, 21, 1011, 29, "styles"], [1125, 27, 1011, 35], [1125, 28, 1011, 36, "previewChipText"], [1125, 43, 1011, 52], [1126, 14, 1011, 52, "children"], [1126, 22, 1011, 52], [1126, 24, 1011, 53], [1127, 12, 1011, 71], [1128, 14, 1011, 71, "fileName"], [1128, 22, 1011, 71], [1128, 24, 1011, 71, "_jsxFileName"], [1128, 36, 1011, 71], [1129, 14, 1011, 71, "lineNumber"], [1129, 24, 1011, 71], [1130, 14, 1011, 71, "columnNumber"], [1130, 26, 1011, 71], [1131, 12, 1011, 71], [1131, 19, 1011, 77], [1132, 10, 1011, 78], [1133, 12, 1011, 78, "fileName"], [1133, 20, 1011, 78], [1133, 22, 1011, 78, "_jsxFileName"], [1133, 34, 1011, 78], [1134, 12, 1011, 78, "lineNumber"], [1134, 22, 1011, 78], [1135, 12, 1011, 78, "columnNumber"], [1135, 24, 1011, 78], [1136, 10, 1011, 78], [1136, 17, 1012, 20], [1136, 18, 1013, 13], [1137, 8, 1013, 13], [1138, 10, 1013, 13, "fileName"], [1138, 18, 1013, 13], [1138, 20, 1013, 13, "_jsxFileName"], [1138, 32, 1013, 13], [1139, 10, 1013, 13, "lineNumber"], [1139, 20, 1013, 13], [1140, 10, 1013, 13, "columnNumber"], [1140, 22, 1013, 13], [1141, 8, 1013, 13], [1141, 15, 1014, 16], [1141, 16, 1015, 9], [1141, 18, 1017, 9, "isCameraReady"], [1141, 31, 1017, 22], [1141, 48, 1018, 10], [1141, 52, 1018, 10, "_jsxDevRuntime"], [1141, 66, 1018, 10], [1141, 67, 1018, 10, "jsxDEV"], [1141, 73, 1018, 10], [1141, 75, 1018, 10, "_jsxDevRuntime"], [1141, 89, 1018, 10], [1141, 90, 1018, 10, "Fragment"], [1141, 98, 1018, 10], [1142, 10, 1018, 10, "children"], [1142, 18, 1018, 10], [1142, 34, 1020, 12], [1142, 38, 1020, 12, "_jsxDevRuntime"], [1142, 52, 1020, 12], [1142, 53, 1020, 12, "jsxDEV"], [1142, 59, 1020, 12], [1142, 61, 1020, 13, "_View"], [1142, 66, 1020, 13], [1142, 67, 1020, 13, "default"], [1142, 74, 1020, 17], [1143, 12, 1020, 18, "style"], [1143, 17, 1020, 23], [1143, 19, 1020, 25, "styles"], [1143, 25, 1020, 31], [1143, 26, 1020, 32, "headerOverlay"], [1143, 39, 1020, 46], [1144, 12, 1020, 46, "children"], [1144, 20, 1020, 46], [1144, 35, 1021, 14], [1144, 39, 1021, 14, "_jsxDevRuntime"], [1144, 53, 1021, 14], [1144, 54, 1021, 14, "jsxDEV"], [1144, 60, 1021, 14], [1144, 62, 1021, 15, "_View"], [1144, 67, 1021, 15], [1144, 68, 1021, 15, "default"], [1144, 75, 1021, 19], [1145, 14, 1021, 20, "style"], [1145, 19, 1021, 25], [1145, 21, 1021, 27, "styles"], [1145, 27, 1021, 33], [1145, 28, 1021, 34, "headerContent"], [1145, 41, 1021, 48], [1146, 14, 1021, 48, "children"], [1146, 22, 1021, 48], [1146, 38, 1022, 16], [1146, 42, 1022, 16, "_jsxDevRuntime"], [1146, 56, 1022, 16], [1146, 57, 1022, 16, "jsxDEV"], [1146, 63, 1022, 16], [1146, 65, 1022, 17, "_View"], [1146, 70, 1022, 17], [1146, 71, 1022, 17, "default"], [1146, 78, 1022, 21], [1147, 16, 1022, 22, "style"], [1147, 21, 1022, 27], [1147, 23, 1022, 29, "styles"], [1147, 29, 1022, 35], [1147, 30, 1022, 36, "headerLeft"], [1147, 40, 1022, 47], [1148, 16, 1022, 47, "children"], [1148, 24, 1022, 47], [1148, 40, 1023, 18], [1148, 44, 1023, 18, "_jsxDevRuntime"], [1148, 58, 1023, 18], [1148, 59, 1023, 18, "jsxDEV"], [1148, 65, 1023, 18], [1148, 67, 1023, 19, "_Text"], [1148, 72, 1023, 19], [1148, 73, 1023, 19, "default"], [1148, 80, 1023, 23], [1149, 18, 1023, 24, "style"], [1149, 23, 1023, 29], [1149, 25, 1023, 31, "styles"], [1149, 31, 1023, 37], [1149, 32, 1023, 38, "headerTitle"], [1149, 43, 1023, 50], [1150, 18, 1023, 50, "children"], [1150, 26, 1023, 50], [1150, 28, 1023, 51], [1151, 16, 1023, 62], [1152, 18, 1023, 62, "fileName"], [1152, 26, 1023, 62], [1152, 28, 1023, 62, "_jsxFileName"], [1152, 40, 1023, 62], [1153, 18, 1023, 62, "lineNumber"], [1153, 28, 1023, 62], [1154, 18, 1023, 62, "columnNumber"], [1154, 30, 1023, 62], [1155, 16, 1023, 62], [1155, 23, 1023, 68], [1155, 24, 1023, 69], [1155, 39, 1024, 18], [1155, 43, 1024, 18, "_jsxDevRuntime"], [1155, 57, 1024, 18], [1155, 58, 1024, 18, "jsxDEV"], [1155, 64, 1024, 18], [1155, 66, 1024, 19, "_View"], [1155, 71, 1024, 19], [1155, 72, 1024, 19, "default"], [1155, 79, 1024, 23], [1156, 18, 1024, 24, "style"], [1156, 23, 1024, 29], [1156, 25, 1024, 31, "styles"], [1156, 31, 1024, 37], [1156, 32, 1024, 38, "subtitleRow"], [1156, 43, 1024, 50], [1157, 18, 1024, 50, "children"], [1157, 26, 1024, 50], [1157, 42, 1025, 20], [1157, 46, 1025, 20, "_jsxDevRuntime"], [1157, 60, 1025, 20], [1157, 61, 1025, 20, "jsxDEV"], [1157, 67, 1025, 20], [1157, 69, 1025, 21, "_Text"], [1157, 74, 1025, 21], [1157, 75, 1025, 21, "default"], [1157, 82, 1025, 25], [1158, 20, 1025, 26, "style"], [1158, 25, 1025, 31], [1158, 27, 1025, 33, "styles"], [1158, 33, 1025, 39], [1158, 34, 1025, 40, "webIcon"], [1158, 41, 1025, 48], [1159, 20, 1025, 48, "children"], [1159, 28, 1025, 48], [1159, 30, 1025, 49], [1160, 18, 1025, 51], [1161, 20, 1025, 51, "fileName"], [1161, 28, 1025, 51], [1161, 30, 1025, 51, "_jsxFileName"], [1161, 42, 1025, 51], [1162, 20, 1025, 51, "lineNumber"], [1162, 30, 1025, 51], [1163, 20, 1025, 51, "columnNumber"], [1163, 32, 1025, 51], [1164, 18, 1025, 51], [1164, 25, 1025, 57], [1164, 26, 1025, 58], [1164, 41, 1026, 20], [1164, 45, 1026, 20, "_jsxDevRuntime"], [1164, 59, 1026, 20], [1164, 60, 1026, 20, "jsxDEV"], [1164, 66, 1026, 20], [1164, 68, 1026, 21, "_Text"], [1164, 73, 1026, 21], [1164, 74, 1026, 21, "default"], [1164, 81, 1026, 25], [1165, 20, 1026, 26, "style"], [1165, 25, 1026, 31], [1165, 27, 1026, 33, "styles"], [1165, 33, 1026, 39], [1165, 34, 1026, 40, "headerSubtitle"], [1165, 48, 1026, 55], [1166, 20, 1026, 55, "children"], [1166, 28, 1026, 55], [1166, 30, 1026, 56], [1167, 18, 1026, 71], [1168, 20, 1026, 71, "fileName"], [1168, 28, 1026, 71], [1168, 30, 1026, 71, "_jsxFileName"], [1168, 42, 1026, 71], [1169, 20, 1026, 71, "lineNumber"], [1169, 30, 1026, 71], [1170, 20, 1026, 71, "columnNumber"], [1170, 32, 1026, 71], [1171, 18, 1026, 71], [1171, 25, 1026, 77], [1171, 26, 1026, 78], [1172, 16, 1026, 78], [1173, 18, 1026, 78, "fileName"], [1173, 26, 1026, 78], [1173, 28, 1026, 78, "_jsxFileName"], [1173, 40, 1026, 78], [1174, 18, 1026, 78, "lineNumber"], [1174, 28, 1026, 78], [1175, 18, 1026, 78, "columnNumber"], [1175, 30, 1026, 78], [1176, 16, 1026, 78], [1176, 23, 1027, 24], [1176, 24, 1027, 25], [1176, 26, 1028, 19, "challengeCode"], [1176, 39, 1028, 32], [1176, 56, 1029, 20], [1176, 60, 1029, 20, "_jsxDevRuntime"], [1176, 74, 1029, 20], [1176, 75, 1029, 20, "jsxDEV"], [1176, 81, 1029, 20], [1176, 83, 1029, 21, "_View"], [1176, 88, 1029, 21], [1176, 89, 1029, 21, "default"], [1176, 96, 1029, 25], [1177, 18, 1029, 26, "style"], [1177, 23, 1029, 31], [1177, 25, 1029, 33, "styles"], [1177, 31, 1029, 39], [1177, 32, 1029, 40, "challengeRow"], [1177, 44, 1029, 53], [1178, 18, 1029, 53, "children"], [1178, 26, 1029, 53], [1178, 42, 1030, 22], [1178, 46, 1030, 22, "_jsxDevRuntime"], [1178, 60, 1030, 22], [1178, 61, 1030, 22, "jsxDEV"], [1178, 67, 1030, 22], [1178, 69, 1030, 23, "_lucideReactNative"], [1178, 87, 1030, 23], [1178, 88, 1030, 23, "Shield"], [1178, 94, 1030, 29], [1179, 20, 1030, 30, "size"], [1179, 24, 1030, 34], [1179, 26, 1030, 36], [1179, 28, 1030, 39], [1180, 20, 1030, 40, "color"], [1180, 25, 1030, 45], [1180, 27, 1030, 46], [1181, 18, 1030, 52], [1182, 20, 1030, 52, "fileName"], [1182, 28, 1030, 52], [1182, 30, 1030, 52, "_jsxFileName"], [1182, 42, 1030, 52], [1183, 20, 1030, 52, "lineNumber"], [1183, 30, 1030, 52], [1184, 20, 1030, 52, "columnNumber"], [1184, 32, 1030, 52], [1185, 18, 1030, 52], [1185, 25, 1030, 54], [1185, 26, 1030, 55], [1185, 41, 1031, 22], [1185, 45, 1031, 22, "_jsxDevRuntime"], [1185, 59, 1031, 22], [1185, 60, 1031, 22, "jsxDEV"], [1185, 66, 1031, 22], [1185, 68, 1031, 23, "_Text"], [1185, 73, 1031, 23], [1185, 74, 1031, 23, "default"], [1185, 81, 1031, 27], [1186, 20, 1031, 28, "style"], [1186, 25, 1031, 33], [1186, 27, 1031, 35, "styles"], [1186, 33, 1031, 41], [1186, 34, 1031, 42, "challengeCode"], [1186, 47, 1031, 56], [1187, 20, 1031, 56, "children"], [1187, 28, 1031, 56], [1187, 30, 1031, 58, "challengeCode"], [1188, 18, 1031, 71], [1189, 20, 1031, 71, "fileName"], [1189, 28, 1031, 71], [1189, 30, 1031, 71, "_jsxFileName"], [1189, 42, 1031, 71], [1190, 20, 1031, 71, "lineNumber"], [1190, 30, 1031, 71], [1191, 20, 1031, 71, "columnNumber"], [1191, 32, 1031, 71], [1192, 18, 1031, 71], [1192, 25, 1031, 78], [1192, 26, 1031, 79], [1193, 16, 1031, 79], [1194, 18, 1031, 79, "fileName"], [1194, 26, 1031, 79], [1194, 28, 1031, 79, "_jsxFileName"], [1194, 40, 1031, 79], [1195, 18, 1031, 79, "lineNumber"], [1195, 28, 1031, 79], [1196, 18, 1031, 79, "columnNumber"], [1196, 30, 1031, 79], [1197, 16, 1031, 79], [1197, 23, 1032, 26], [1197, 24, 1033, 19], [1198, 14, 1033, 19], [1199, 16, 1033, 19, "fileName"], [1199, 24, 1033, 19], [1199, 26, 1033, 19, "_jsxFileName"], [1199, 38, 1033, 19], [1200, 16, 1033, 19, "lineNumber"], [1200, 26, 1033, 19], [1201, 16, 1033, 19, "columnNumber"], [1201, 28, 1033, 19], [1202, 14, 1033, 19], [1202, 21, 1034, 22], [1202, 22, 1034, 23], [1202, 37, 1035, 16], [1202, 41, 1035, 16, "_jsxDevRuntime"], [1202, 55, 1035, 16], [1202, 56, 1035, 16, "jsxDEV"], [1202, 62, 1035, 16], [1202, 64, 1035, 17, "_TouchableOpacity"], [1202, 81, 1035, 17], [1202, 82, 1035, 17, "default"], [1202, 89, 1035, 33], [1203, 16, 1035, 34, "onPress"], [1203, 23, 1035, 41], [1203, 25, 1035, 43, "onCancel"], [1203, 33, 1035, 52], [1204, 16, 1035, 53, "style"], [1204, 21, 1035, 58], [1204, 23, 1035, 60, "styles"], [1204, 29, 1035, 66], [1204, 30, 1035, 67, "closeButton"], [1204, 41, 1035, 79], [1205, 16, 1035, 79, "children"], [1205, 24, 1035, 79], [1205, 39, 1036, 18], [1205, 43, 1036, 18, "_jsxDevRuntime"], [1205, 57, 1036, 18], [1205, 58, 1036, 18, "jsxDEV"], [1205, 64, 1036, 18], [1205, 66, 1036, 19, "_lucideReactNative"], [1205, 84, 1036, 19], [1205, 85, 1036, 19, "X"], [1205, 86, 1036, 20], [1206, 18, 1036, 21, "size"], [1206, 22, 1036, 25], [1206, 24, 1036, 27], [1206, 26, 1036, 30], [1207, 18, 1036, 31, "color"], [1207, 23, 1036, 36], [1207, 25, 1036, 37], [1208, 16, 1036, 43], [1209, 18, 1036, 43, "fileName"], [1209, 26, 1036, 43], [1209, 28, 1036, 43, "_jsxFileName"], [1209, 40, 1036, 43], [1210, 18, 1036, 43, "lineNumber"], [1210, 28, 1036, 43], [1211, 18, 1036, 43, "columnNumber"], [1211, 30, 1036, 43], [1212, 16, 1036, 43], [1212, 23, 1036, 45], [1213, 14, 1036, 46], [1214, 16, 1036, 46, "fileName"], [1214, 24, 1036, 46], [1214, 26, 1036, 46, "_jsxFileName"], [1214, 38, 1036, 46], [1215, 16, 1036, 46, "lineNumber"], [1215, 26, 1036, 46], [1216, 16, 1036, 46, "columnNumber"], [1216, 28, 1036, 46], [1217, 14, 1036, 46], [1217, 21, 1037, 34], [1217, 22, 1037, 35], [1218, 12, 1037, 35], [1219, 14, 1037, 35, "fileName"], [1219, 22, 1037, 35], [1219, 24, 1037, 35, "_jsxFileName"], [1219, 36, 1037, 35], [1220, 14, 1037, 35, "lineNumber"], [1220, 24, 1037, 35], [1221, 14, 1037, 35, "columnNumber"], [1221, 26, 1037, 35], [1222, 12, 1037, 35], [1222, 19, 1038, 20], [1223, 10, 1038, 21], [1224, 12, 1038, 21, "fileName"], [1224, 20, 1038, 21], [1224, 22, 1038, 21, "_jsxFileName"], [1224, 34, 1038, 21], [1225, 12, 1038, 21, "lineNumber"], [1225, 22, 1038, 21], [1226, 12, 1038, 21, "columnNumber"], [1226, 24, 1038, 21], [1227, 10, 1038, 21], [1227, 17, 1039, 18], [1227, 18, 1039, 19], [1227, 33, 1041, 12], [1227, 37, 1041, 12, "_jsxDevRuntime"], [1227, 51, 1041, 12], [1227, 52, 1041, 12, "jsxDEV"], [1227, 58, 1041, 12], [1227, 60, 1041, 13, "_View"], [1227, 65, 1041, 13], [1227, 66, 1041, 13, "default"], [1227, 73, 1041, 17], [1228, 12, 1041, 18, "style"], [1228, 17, 1041, 23], [1228, 19, 1041, 25, "styles"], [1228, 25, 1041, 31], [1228, 26, 1041, 32, "privacyNotice"], [1228, 39, 1041, 46], [1229, 12, 1041, 46, "children"], [1229, 20, 1041, 46], [1229, 36, 1042, 14], [1229, 40, 1042, 14, "_jsxDevRuntime"], [1229, 54, 1042, 14], [1229, 55, 1042, 14, "jsxDEV"], [1229, 61, 1042, 14], [1229, 63, 1042, 15, "_lucideReactNative"], [1229, 81, 1042, 15], [1229, 82, 1042, 15, "Shield"], [1229, 88, 1042, 21], [1230, 14, 1042, 22, "size"], [1230, 18, 1042, 26], [1230, 20, 1042, 28], [1230, 22, 1042, 31], [1231, 14, 1042, 32, "color"], [1231, 19, 1042, 37], [1231, 21, 1042, 38], [1232, 12, 1042, 47], [1233, 14, 1042, 47, "fileName"], [1233, 22, 1042, 47], [1233, 24, 1042, 47, "_jsxFileName"], [1233, 36, 1042, 47], [1234, 14, 1042, 47, "lineNumber"], [1234, 24, 1042, 47], [1235, 14, 1042, 47, "columnNumber"], [1235, 26, 1042, 47], [1236, 12, 1042, 47], [1236, 19, 1042, 49], [1236, 20, 1042, 50], [1236, 35, 1043, 14], [1236, 39, 1043, 14, "_jsxDevRuntime"], [1236, 53, 1043, 14], [1236, 54, 1043, 14, "jsxDEV"], [1236, 60, 1043, 14], [1236, 62, 1043, 15, "_Text"], [1236, 67, 1043, 15], [1236, 68, 1043, 15, "default"], [1236, 75, 1043, 19], [1237, 14, 1043, 20, "style"], [1237, 19, 1043, 25], [1237, 21, 1043, 27, "styles"], [1237, 27, 1043, 33], [1237, 28, 1043, 34, "privacyText"], [1237, 39, 1043, 46], [1238, 14, 1043, 46, "children"], [1238, 22, 1043, 46], [1238, 24, 1043, 47], [1239, 12, 1045, 14], [1240, 14, 1045, 14, "fileName"], [1240, 22, 1045, 14], [1240, 24, 1045, 14, "_jsxFileName"], [1240, 36, 1045, 14], [1241, 14, 1045, 14, "lineNumber"], [1241, 24, 1045, 14], [1242, 14, 1045, 14, "columnNumber"], [1242, 26, 1045, 14], [1243, 12, 1045, 14], [1243, 19, 1045, 20], [1243, 20, 1045, 21], [1244, 10, 1045, 21], [1245, 12, 1045, 21, "fileName"], [1245, 20, 1045, 21], [1245, 22, 1045, 21, "_jsxFileName"], [1245, 34, 1045, 21], [1246, 12, 1045, 21, "lineNumber"], [1246, 22, 1045, 21], [1247, 12, 1045, 21, "columnNumber"], [1247, 24, 1045, 21], [1248, 10, 1045, 21], [1248, 17, 1046, 18], [1248, 18, 1046, 19], [1248, 33, 1048, 12], [1248, 37, 1048, 12, "_jsxDevRuntime"], [1248, 51, 1048, 12], [1248, 52, 1048, 12, "jsxDEV"], [1248, 58, 1048, 12], [1248, 60, 1048, 13, "_View"], [1248, 65, 1048, 13], [1248, 66, 1048, 13, "default"], [1248, 73, 1048, 17], [1249, 12, 1048, 18, "style"], [1249, 17, 1048, 23], [1249, 19, 1048, 25, "styles"], [1249, 25, 1048, 31], [1249, 26, 1048, 32, "footer<PERSON><PERSON><PERSON>"], [1249, 39, 1048, 46], [1250, 12, 1048, 46, "children"], [1250, 20, 1048, 46], [1250, 36, 1049, 14], [1250, 40, 1049, 14, "_jsxDevRuntime"], [1250, 54, 1049, 14], [1250, 55, 1049, 14, "jsxDEV"], [1250, 61, 1049, 14], [1250, 63, 1049, 15, "_Text"], [1250, 68, 1049, 15], [1250, 69, 1049, 15, "default"], [1250, 76, 1049, 19], [1251, 14, 1049, 20, "style"], [1251, 19, 1049, 25], [1251, 21, 1049, 27, "styles"], [1251, 27, 1049, 33], [1251, 28, 1049, 34, "instruction"], [1251, 39, 1049, 46], [1252, 14, 1049, 46, "children"], [1252, 22, 1049, 46], [1252, 24, 1049, 47], [1253, 12, 1051, 14], [1254, 14, 1051, 14, "fileName"], [1254, 22, 1051, 14], [1254, 24, 1051, 14, "_jsxFileName"], [1254, 36, 1051, 14], [1255, 14, 1051, 14, "lineNumber"], [1255, 24, 1051, 14], [1256, 14, 1051, 14, "columnNumber"], [1256, 26, 1051, 14], [1257, 12, 1051, 14], [1257, 19, 1051, 20], [1257, 20, 1051, 21], [1257, 35, 1053, 14], [1257, 39, 1053, 14, "_jsxDevRuntime"], [1257, 53, 1053, 14], [1257, 54, 1053, 14, "jsxDEV"], [1257, 60, 1053, 14], [1257, 62, 1053, 15, "_TouchableOpacity"], [1257, 79, 1053, 15], [1257, 80, 1053, 15, "default"], [1257, 87, 1053, 31], [1258, 14, 1054, 16, "onPress"], [1258, 21, 1054, 23], [1258, 23, 1054, 25, "capturePhoto"], [1258, 35, 1054, 38], [1259, 14, 1055, 16, "disabled"], [1259, 22, 1055, 24], [1259, 24, 1055, 26, "processingState"], [1259, 39, 1055, 41], [1259, 44, 1055, 46], [1259, 50, 1055, 52], [1259, 54, 1055, 56], [1259, 55, 1055, 57, "isCameraReady"], [1259, 68, 1055, 71], [1260, 14, 1056, 16, "style"], [1260, 19, 1056, 21], [1260, 21, 1056, 23], [1260, 22, 1057, 18, "styles"], [1260, 28, 1057, 24], [1260, 29, 1057, 25, "shutterButton"], [1260, 42, 1057, 38], [1260, 44, 1058, 18, "processingState"], [1260, 59, 1058, 33], [1260, 64, 1058, 38], [1260, 70, 1058, 44], [1260, 74, 1058, 48, "styles"], [1260, 80, 1058, 54], [1260, 81, 1058, 55, "shutterButtonDisabled"], [1260, 102, 1058, 76], [1260, 103, 1059, 18], [1261, 14, 1059, 18, "children"], [1261, 22, 1059, 18], [1261, 24, 1061, 17, "processingState"], [1261, 39, 1061, 32], [1261, 44, 1061, 37], [1261, 50, 1061, 43], [1261, 66, 1062, 18], [1261, 70, 1062, 18, "_jsxDevRuntime"], [1261, 84, 1062, 18], [1261, 85, 1062, 18, "jsxDEV"], [1261, 91, 1062, 18], [1261, 93, 1062, 19, "_View"], [1261, 98, 1062, 19], [1261, 99, 1062, 19, "default"], [1261, 106, 1062, 23], [1262, 16, 1062, 24, "style"], [1262, 21, 1062, 29], [1262, 23, 1062, 31, "styles"], [1262, 29, 1062, 37], [1262, 30, 1062, 38, "shutterInner"], [1263, 14, 1062, 51], [1264, 16, 1062, 51, "fileName"], [1264, 24, 1062, 51], [1264, 26, 1062, 51, "_jsxFileName"], [1264, 38, 1062, 51], [1265, 16, 1062, 51, "lineNumber"], [1265, 26, 1062, 51], [1266, 16, 1062, 51, "columnNumber"], [1266, 28, 1062, 51], [1267, 14, 1062, 51], [1267, 21, 1062, 53], [1267, 22, 1062, 54], [1267, 38, 1064, 18], [1267, 42, 1064, 18, "_jsxDevRuntime"], [1267, 56, 1064, 18], [1267, 57, 1064, 18, "jsxDEV"], [1267, 63, 1064, 18], [1267, 65, 1064, 19, "_ActivityIndicator"], [1267, 83, 1064, 19], [1267, 84, 1064, 19, "default"], [1267, 91, 1064, 36], [1268, 16, 1064, 37, "size"], [1268, 20, 1064, 41], [1268, 22, 1064, 42], [1268, 29, 1064, 49], [1269, 16, 1064, 50, "color"], [1269, 21, 1064, 55], [1269, 23, 1064, 56], [1270, 14, 1064, 65], [1271, 16, 1064, 65, "fileName"], [1271, 24, 1064, 65], [1271, 26, 1064, 65, "_jsxFileName"], [1271, 38, 1064, 65], [1272, 16, 1064, 65, "lineNumber"], [1272, 26, 1064, 65], [1273, 16, 1064, 65, "columnNumber"], [1273, 28, 1064, 65], [1274, 14, 1064, 65], [1274, 21, 1064, 67], [1275, 12, 1065, 17], [1276, 14, 1065, 17, "fileName"], [1276, 22, 1065, 17], [1276, 24, 1065, 17, "_jsxFileName"], [1276, 36, 1065, 17], [1277, 14, 1065, 17, "lineNumber"], [1277, 24, 1065, 17], [1278, 14, 1065, 17, "columnNumber"], [1278, 26, 1065, 17], [1279, 12, 1065, 17], [1279, 19, 1066, 32], [1279, 20, 1066, 33], [1279, 35, 1067, 14], [1279, 39, 1067, 14, "_jsxDevRuntime"], [1279, 53, 1067, 14], [1279, 54, 1067, 14, "jsxDEV"], [1279, 60, 1067, 14], [1279, 62, 1067, 15, "_Text"], [1279, 67, 1067, 15], [1279, 68, 1067, 15, "default"], [1279, 75, 1067, 19], [1280, 14, 1067, 20, "style"], [1280, 19, 1067, 25], [1280, 21, 1067, 27, "styles"], [1280, 27, 1067, 33], [1280, 28, 1067, 34, "privacyNote"], [1280, 39, 1067, 46], [1281, 14, 1067, 46, "children"], [1281, 22, 1067, 46], [1281, 24, 1067, 47], [1282, 12, 1069, 14], [1283, 14, 1069, 14, "fileName"], [1283, 22, 1069, 14], [1283, 24, 1069, 14, "_jsxFileName"], [1283, 36, 1069, 14], [1284, 14, 1069, 14, "lineNumber"], [1284, 24, 1069, 14], [1285, 14, 1069, 14, "columnNumber"], [1285, 26, 1069, 14], [1286, 12, 1069, 14], [1286, 19, 1069, 20], [1286, 20, 1069, 21], [1287, 10, 1069, 21], [1288, 12, 1069, 21, "fileName"], [1288, 20, 1069, 21], [1288, 22, 1069, 21, "_jsxFileName"], [1288, 34, 1069, 21], [1289, 12, 1069, 21, "lineNumber"], [1289, 22, 1069, 21], [1290, 12, 1069, 21, "columnNumber"], [1290, 24, 1069, 21], [1291, 10, 1069, 21], [1291, 17, 1070, 18], [1291, 18, 1070, 19], [1292, 8, 1070, 19], [1292, 23, 1071, 12], [1292, 24, 1072, 9], [1293, 6, 1072, 9], [1294, 8, 1072, 9, "fileName"], [1294, 16, 1072, 9], [1294, 18, 1072, 9, "_jsxFileName"], [1294, 30, 1072, 9], [1295, 8, 1072, 9, "lineNumber"], [1295, 18, 1072, 9], [1296, 8, 1072, 9, "columnNumber"], [1296, 20, 1072, 9], [1297, 6, 1072, 9], [1297, 13, 1073, 12], [1297, 14, 1073, 13], [1297, 29, 1075, 6], [1297, 33, 1075, 6, "_jsxDevRuntime"], [1297, 47, 1075, 6], [1297, 48, 1075, 6, "jsxDEV"], [1297, 54, 1075, 6], [1297, 56, 1075, 7, "_Modal"], [1297, 62, 1075, 7], [1297, 63, 1075, 7, "default"], [1297, 70, 1075, 12], [1298, 8, 1076, 8, "visible"], [1298, 15, 1076, 15], [1298, 17, 1076, 17, "processingState"], [1298, 32, 1076, 32], [1298, 37, 1076, 37], [1298, 43, 1076, 43], [1298, 47, 1076, 47, "processingState"], [1298, 62, 1076, 62], [1298, 67, 1076, 67], [1298, 74, 1076, 75], [1299, 8, 1077, 8, "transparent"], [1299, 19, 1077, 19], [1300, 8, 1078, 8, "animationType"], [1300, 21, 1078, 21], [1300, 23, 1078, 22], [1300, 29, 1078, 28], [1301, 8, 1078, 28, "children"], [1301, 16, 1078, 28], [1301, 31, 1080, 8], [1301, 35, 1080, 8, "_jsxDevRuntime"], [1301, 49, 1080, 8], [1301, 50, 1080, 8, "jsxDEV"], [1301, 56, 1080, 8], [1301, 58, 1080, 9, "_View"], [1301, 63, 1080, 9], [1301, 64, 1080, 9, "default"], [1301, 71, 1080, 13], [1302, 10, 1080, 14, "style"], [1302, 15, 1080, 19], [1302, 17, 1080, 21, "styles"], [1302, 23, 1080, 27], [1302, 24, 1080, 28, "processingModal"], [1302, 39, 1080, 44], [1303, 10, 1080, 44, "children"], [1303, 18, 1080, 44], [1303, 33, 1081, 10], [1303, 37, 1081, 10, "_jsxDevRuntime"], [1303, 51, 1081, 10], [1303, 52, 1081, 10, "jsxDEV"], [1303, 58, 1081, 10], [1303, 60, 1081, 11, "_View"], [1303, 65, 1081, 11], [1303, 66, 1081, 11, "default"], [1303, 73, 1081, 15], [1304, 12, 1081, 16, "style"], [1304, 17, 1081, 21], [1304, 19, 1081, 23, "styles"], [1304, 25, 1081, 29], [1304, 26, 1081, 30, "processingContent"], [1304, 43, 1081, 48], [1305, 12, 1081, 48, "children"], [1305, 20, 1081, 48], [1305, 36, 1082, 12], [1305, 40, 1082, 12, "_jsxDevRuntime"], [1305, 54, 1082, 12], [1305, 55, 1082, 12, "jsxDEV"], [1305, 61, 1082, 12], [1305, 63, 1082, 13, "_ActivityIndicator"], [1305, 81, 1082, 13], [1305, 82, 1082, 13, "default"], [1305, 89, 1082, 30], [1306, 14, 1082, 31, "size"], [1306, 18, 1082, 35], [1306, 20, 1082, 36], [1306, 27, 1082, 43], [1307, 14, 1082, 44, "color"], [1307, 19, 1082, 49], [1307, 21, 1082, 50], [1308, 12, 1082, 59], [1309, 14, 1082, 59, "fileName"], [1309, 22, 1082, 59], [1309, 24, 1082, 59, "_jsxFileName"], [1309, 36, 1082, 59], [1310, 14, 1082, 59, "lineNumber"], [1310, 24, 1082, 59], [1311, 14, 1082, 59, "columnNumber"], [1311, 26, 1082, 59], [1312, 12, 1082, 59], [1312, 19, 1082, 61], [1312, 20, 1082, 62], [1312, 35, 1084, 12], [1312, 39, 1084, 12, "_jsxDevRuntime"], [1312, 53, 1084, 12], [1312, 54, 1084, 12, "jsxDEV"], [1312, 60, 1084, 12], [1312, 62, 1084, 13, "_Text"], [1312, 67, 1084, 13], [1312, 68, 1084, 13, "default"], [1312, 75, 1084, 17], [1313, 14, 1084, 18, "style"], [1313, 19, 1084, 23], [1313, 21, 1084, 25, "styles"], [1313, 27, 1084, 31], [1313, 28, 1084, 32, "processingTitle"], [1313, 43, 1084, 48], [1314, 14, 1084, 48, "children"], [1314, 22, 1084, 48], [1314, 25, 1085, 15, "processingState"], [1314, 40, 1085, 30], [1314, 45, 1085, 35], [1314, 56, 1085, 46], [1314, 60, 1085, 50], [1314, 80, 1085, 70], [1314, 82, 1086, 15, "processingState"], [1314, 97, 1086, 30], [1314, 102, 1086, 35], [1314, 113, 1086, 46], [1314, 117, 1086, 50], [1314, 146, 1086, 79], [1314, 148, 1087, 15, "processingState"], [1314, 163, 1087, 30], [1314, 168, 1087, 35], [1314, 180, 1087, 47], [1314, 184, 1087, 51], [1314, 216, 1087, 83], [1314, 218, 1088, 15, "processingState"], [1314, 233, 1088, 30], [1314, 238, 1088, 35], [1314, 249, 1088, 46], [1314, 253, 1088, 50], [1314, 275, 1088, 72], [1315, 12, 1088, 72], [1316, 14, 1088, 72, "fileName"], [1316, 22, 1088, 72], [1316, 24, 1088, 72, "_jsxFileName"], [1316, 36, 1088, 72], [1317, 14, 1088, 72, "lineNumber"], [1317, 24, 1088, 72], [1318, 14, 1088, 72, "columnNumber"], [1318, 26, 1088, 72], [1319, 12, 1088, 72], [1319, 19, 1089, 18], [1319, 20, 1089, 19], [1319, 35, 1090, 12], [1319, 39, 1090, 12, "_jsxDevRuntime"], [1319, 53, 1090, 12], [1319, 54, 1090, 12, "jsxDEV"], [1319, 60, 1090, 12], [1319, 62, 1090, 13, "_View"], [1319, 67, 1090, 13], [1319, 68, 1090, 13, "default"], [1319, 75, 1090, 17], [1320, 14, 1090, 18, "style"], [1320, 19, 1090, 23], [1320, 21, 1090, 25, "styles"], [1320, 27, 1090, 31], [1320, 28, 1090, 32, "progressBar"], [1320, 39, 1090, 44], [1321, 14, 1090, 44, "children"], [1321, 22, 1090, 44], [1321, 37, 1091, 14], [1321, 41, 1091, 14, "_jsxDevRuntime"], [1321, 55, 1091, 14], [1321, 56, 1091, 14, "jsxDEV"], [1321, 62, 1091, 14], [1321, 64, 1091, 15, "_View"], [1321, 69, 1091, 15], [1321, 70, 1091, 15, "default"], [1321, 77, 1091, 19], [1322, 16, 1092, 16, "style"], [1322, 21, 1092, 21], [1322, 23, 1092, 23], [1322, 24, 1093, 18, "styles"], [1322, 30, 1093, 24], [1322, 31, 1093, 25, "progressFill"], [1322, 43, 1093, 37], [1322, 45, 1094, 18], [1323, 18, 1094, 20, "width"], [1323, 23, 1094, 25], [1323, 25, 1094, 27], [1323, 28, 1094, 30, "processingProgress"], [1323, 46, 1094, 48], [1324, 16, 1094, 52], [1324, 17, 1094, 53], [1325, 14, 1095, 18], [1326, 16, 1095, 18, "fileName"], [1326, 24, 1095, 18], [1326, 26, 1095, 18, "_jsxFileName"], [1326, 38, 1095, 18], [1327, 16, 1095, 18, "lineNumber"], [1327, 26, 1095, 18], [1328, 16, 1095, 18, "columnNumber"], [1328, 28, 1095, 18], [1329, 14, 1095, 18], [1329, 21, 1096, 15], [1330, 12, 1096, 16], [1331, 14, 1096, 16, "fileName"], [1331, 22, 1096, 16], [1331, 24, 1096, 16, "_jsxFileName"], [1331, 36, 1096, 16], [1332, 14, 1096, 16, "lineNumber"], [1332, 24, 1096, 16], [1333, 14, 1096, 16, "columnNumber"], [1333, 26, 1096, 16], [1334, 12, 1096, 16], [1334, 19, 1097, 18], [1334, 20, 1097, 19], [1334, 35, 1098, 12], [1334, 39, 1098, 12, "_jsxDevRuntime"], [1334, 53, 1098, 12], [1334, 54, 1098, 12, "jsxDEV"], [1334, 60, 1098, 12], [1334, 62, 1098, 13, "_Text"], [1334, 67, 1098, 13], [1334, 68, 1098, 13, "default"], [1334, 75, 1098, 17], [1335, 14, 1098, 18, "style"], [1335, 19, 1098, 23], [1335, 21, 1098, 25, "styles"], [1335, 27, 1098, 31], [1335, 28, 1098, 32, "processingDescription"], [1335, 49, 1098, 54], [1336, 14, 1098, 54, "children"], [1336, 22, 1098, 54], [1336, 25, 1099, 15, "processingState"], [1336, 40, 1099, 30], [1336, 45, 1099, 35], [1336, 56, 1099, 46], [1336, 60, 1099, 50], [1336, 89, 1099, 79], [1336, 91, 1100, 15, "processingState"], [1336, 106, 1100, 30], [1336, 111, 1100, 35], [1336, 122, 1100, 46], [1336, 126, 1100, 50], [1336, 164, 1100, 88], [1336, 166, 1101, 15, "processingState"], [1336, 181, 1101, 30], [1336, 186, 1101, 35], [1336, 198, 1101, 47], [1336, 202, 1101, 51], [1336, 247, 1101, 96], [1336, 249, 1102, 15, "processingState"], [1336, 264, 1102, 30], [1336, 269, 1102, 35], [1336, 280, 1102, 46], [1336, 284, 1102, 50], [1336, 325, 1102, 91], [1337, 12, 1102, 91], [1338, 14, 1102, 91, "fileName"], [1338, 22, 1102, 91], [1338, 24, 1102, 91, "_jsxFileName"], [1338, 36, 1102, 91], [1339, 14, 1102, 91, "lineNumber"], [1339, 24, 1102, 91], [1340, 14, 1102, 91, "columnNumber"], [1340, 26, 1102, 91], [1341, 12, 1102, 91], [1341, 19, 1103, 18], [1341, 20, 1103, 19], [1341, 22, 1104, 13, "processingState"], [1341, 37, 1104, 28], [1341, 42, 1104, 33], [1341, 53, 1104, 44], [1341, 70, 1105, 14], [1341, 74, 1105, 14, "_jsxDevRuntime"], [1341, 88, 1105, 14], [1341, 89, 1105, 14, "jsxDEV"], [1341, 95, 1105, 14], [1341, 97, 1105, 15, "_lucideReactNative"], [1341, 115, 1105, 15], [1341, 116, 1105, 15, "CheckCircle"], [1341, 127, 1105, 26], [1342, 14, 1105, 27, "size"], [1342, 18, 1105, 31], [1342, 20, 1105, 33], [1342, 22, 1105, 36], [1343, 14, 1105, 37, "color"], [1343, 19, 1105, 42], [1343, 21, 1105, 43], [1343, 30, 1105, 52], [1344, 14, 1105, 53, "style"], [1344, 19, 1105, 58], [1344, 21, 1105, 60, "styles"], [1344, 27, 1105, 66], [1344, 28, 1105, 67, "successIcon"], [1345, 12, 1105, 79], [1346, 14, 1105, 79, "fileName"], [1346, 22, 1105, 79], [1346, 24, 1105, 79, "_jsxFileName"], [1346, 36, 1105, 79], [1347, 14, 1105, 79, "lineNumber"], [1347, 24, 1105, 79], [1348, 14, 1105, 79, "columnNumber"], [1348, 26, 1105, 79], [1349, 12, 1105, 79], [1349, 19, 1105, 81], [1349, 20, 1106, 13], [1350, 10, 1106, 13], [1351, 12, 1106, 13, "fileName"], [1351, 20, 1106, 13], [1351, 22, 1106, 13, "_jsxFileName"], [1351, 34, 1106, 13], [1352, 12, 1106, 13, "lineNumber"], [1352, 22, 1106, 13], [1353, 12, 1106, 13, "columnNumber"], [1353, 24, 1106, 13], [1354, 10, 1106, 13], [1354, 17, 1107, 16], [1355, 8, 1107, 17], [1356, 10, 1107, 17, "fileName"], [1356, 18, 1107, 17], [1356, 20, 1107, 17, "_jsxFileName"], [1356, 32, 1107, 17], [1357, 10, 1107, 17, "lineNumber"], [1357, 20, 1107, 17], [1358, 10, 1107, 17, "columnNumber"], [1358, 22, 1107, 17], [1359, 8, 1107, 17], [1359, 15, 1108, 14], [1360, 6, 1108, 15], [1361, 8, 1108, 15, "fileName"], [1361, 16, 1108, 15], [1361, 18, 1108, 15, "_jsxFileName"], [1361, 30, 1108, 15], [1362, 8, 1108, 15, "lineNumber"], [1362, 18, 1108, 15], [1363, 8, 1108, 15, "columnNumber"], [1363, 20, 1108, 15], [1364, 6, 1108, 15], [1364, 13, 1109, 13], [1364, 14, 1109, 14], [1364, 29, 1111, 6], [1364, 33, 1111, 6, "_jsxDevRuntime"], [1364, 47, 1111, 6], [1364, 48, 1111, 6, "jsxDEV"], [1364, 54, 1111, 6], [1364, 56, 1111, 7, "_Modal"], [1364, 62, 1111, 7], [1364, 63, 1111, 7, "default"], [1364, 70, 1111, 12], [1365, 8, 1112, 8, "visible"], [1365, 15, 1112, 15], [1365, 17, 1112, 17, "processingState"], [1365, 32, 1112, 32], [1365, 37, 1112, 37], [1365, 44, 1112, 45], [1366, 8, 1113, 8, "transparent"], [1366, 19, 1113, 19], [1367, 8, 1114, 8, "animationType"], [1367, 21, 1114, 21], [1367, 23, 1114, 22], [1367, 29, 1114, 28], [1368, 8, 1114, 28, "children"], [1368, 16, 1114, 28], [1368, 31, 1116, 8], [1368, 35, 1116, 8, "_jsxDevRuntime"], [1368, 49, 1116, 8], [1368, 50, 1116, 8, "jsxDEV"], [1368, 56, 1116, 8], [1368, 58, 1116, 9, "_View"], [1368, 63, 1116, 9], [1368, 64, 1116, 9, "default"], [1368, 71, 1116, 13], [1369, 10, 1116, 14, "style"], [1369, 15, 1116, 19], [1369, 17, 1116, 21, "styles"], [1369, 23, 1116, 27], [1369, 24, 1116, 28, "processingModal"], [1369, 39, 1116, 44], [1370, 10, 1116, 44, "children"], [1370, 18, 1116, 44], [1370, 33, 1117, 10], [1370, 37, 1117, 10, "_jsxDevRuntime"], [1370, 51, 1117, 10], [1370, 52, 1117, 10, "jsxDEV"], [1370, 58, 1117, 10], [1370, 60, 1117, 11, "_View"], [1370, 65, 1117, 11], [1370, 66, 1117, 11, "default"], [1370, 73, 1117, 15], [1371, 12, 1117, 16, "style"], [1371, 17, 1117, 21], [1371, 19, 1117, 23, "styles"], [1371, 25, 1117, 29], [1371, 26, 1117, 30, "errorContent"], [1371, 38, 1117, 43], [1372, 12, 1117, 43, "children"], [1372, 20, 1117, 43], [1372, 36, 1118, 12], [1372, 40, 1118, 12, "_jsxDevRuntime"], [1372, 54, 1118, 12], [1372, 55, 1118, 12, "jsxDEV"], [1372, 61, 1118, 12], [1372, 63, 1118, 13, "_lucideReactNative"], [1372, 81, 1118, 13], [1372, 82, 1118, 13, "X"], [1372, 83, 1118, 14], [1373, 14, 1118, 15, "size"], [1373, 18, 1118, 19], [1373, 20, 1118, 21], [1373, 22, 1118, 24], [1374, 14, 1118, 25, "color"], [1374, 19, 1118, 30], [1374, 21, 1118, 31], [1375, 12, 1118, 40], [1376, 14, 1118, 40, "fileName"], [1376, 22, 1118, 40], [1376, 24, 1118, 40, "_jsxFileName"], [1376, 36, 1118, 40], [1377, 14, 1118, 40, "lineNumber"], [1377, 24, 1118, 40], [1378, 14, 1118, 40, "columnNumber"], [1378, 26, 1118, 40], [1379, 12, 1118, 40], [1379, 19, 1118, 42], [1379, 20, 1118, 43], [1379, 35, 1119, 12], [1379, 39, 1119, 12, "_jsxDevRuntime"], [1379, 53, 1119, 12], [1379, 54, 1119, 12, "jsxDEV"], [1379, 60, 1119, 12], [1379, 62, 1119, 13, "_Text"], [1379, 67, 1119, 13], [1379, 68, 1119, 13, "default"], [1379, 75, 1119, 17], [1380, 14, 1119, 18, "style"], [1380, 19, 1119, 23], [1380, 21, 1119, 25, "styles"], [1380, 27, 1119, 31], [1380, 28, 1119, 32, "errorTitle"], [1380, 38, 1119, 43], [1381, 14, 1119, 43, "children"], [1381, 22, 1119, 43], [1381, 24, 1119, 44], [1382, 12, 1119, 61], [1383, 14, 1119, 61, "fileName"], [1383, 22, 1119, 61], [1383, 24, 1119, 61, "_jsxFileName"], [1383, 36, 1119, 61], [1384, 14, 1119, 61, "lineNumber"], [1384, 24, 1119, 61], [1385, 14, 1119, 61, "columnNumber"], [1385, 26, 1119, 61], [1386, 12, 1119, 61], [1386, 19, 1119, 67], [1386, 20, 1119, 68], [1386, 35, 1120, 12], [1386, 39, 1120, 12, "_jsxDevRuntime"], [1386, 53, 1120, 12], [1386, 54, 1120, 12, "jsxDEV"], [1386, 60, 1120, 12], [1386, 62, 1120, 13, "_Text"], [1386, 67, 1120, 13], [1386, 68, 1120, 13, "default"], [1386, 75, 1120, 17], [1387, 14, 1120, 18, "style"], [1387, 19, 1120, 23], [1387, 21, 1120, 25, "styles"], [1387, 27, 1120, 31], [1387, 28, 1120, 32, "errorMessage"], [1387, 40, 1120, 45], [1388, 14, 1120, 45, "children"], [1388, 22, 1120, 45], [1388, 24, 1120, 47, "errorMessage"], [1389, 12, 1120, 59], [1390, 14, 1120, 59, "fileName"], [1390, 22, 1120, 59], [1390, 24, 1120, 59, "_jsxFileName"], [1390, 36, 1120, 59], [1391, 14, 1120, 59, "lineNumber"], [1391, 24, 1120, 59], [1392, 14, 1120, 59, "columnNumber"], [1392, 26, 1120, 59], [1393, 12, 1120, 59], [1393, 19, 1120, 66], [1393, 20, 1120, 67], [1393, 35, 1121, 12], [1393, 39, 1121, 12, "_jsxDevRuntime"], [1393, 53, 1121, 12], [1393, 54, 1121, 12, "jsxDEV"], [1393, 60, 1121, 12], [1393, 62, 1121, 13, "_TouchableOpacity"], [1393, 79, 1121, 13], [1393, 80, 1121, 13, "default"], [1393, 87, 1121, 29], [1394, 14, 1122, 14, "onPress"], [1394, 21, 1122, 21], [1394, 23, 1122, 23, "retryCapture"], [1394, 35, 1122, 36], [1395, 14, 1123, 14, "style"], [1395, 19, 1123, 19], [1395, 21, 1123, 21, "styles"], [1395, 27, 1123, 27], [1395, 28, 1123, 28, "primaryButton"], [1395, 41, 1123, 42], [1396, 14, 1123, 42, "children"], [1396, 22, 1123, 42], [1396, 37, 1125, 14], [1396, 41, 1125, 14, "_jsxDevRuntime"], [1396, 55, 1125, 14], [1396, 56, 1125, 14, "jsxDEV"], [1396, 62, 1125, 14], [1396, 64, 1125, 15, "_Text"], [1396, 69, 1125, 15], [1396, 70, 1125, 15, "default"], [1396, 77, 1125, 19], [1397, 16, 1125, 20, "style"], [1397, 21, 1125, 25], [1397, 23, 1125, 27, "styles"], [1397, 29, 1125, 33], [1397, 30, 1125, 34, "primaryButtonText"], [1397, 47, 1125, 52], [1398, 16, 1125, 52, "children"], [1398, 24, 1125, 52], [1398, 26, 1125, 53], [1399, 14, 1125, 62], [1400, 16, 1125, 62, "fileName"], [1400, 24, 1125, 62], [1400, 26, 1125, 62, "_jsxFileName"], [1400, 38, 1125, 62], [1401, 16, 1125, 62, "lineNumber"], [1401, 26, 1125, 62], [1402, 16, 1125, 62, "columnNumber"], [1402, 28, 1125, 62], [1403, 14, 1125, 62], [1403, 21, 1125, 68], [1404, 12, 1125, 69], [1405, 14, 1125, 69, "fileName"], [1405, 22, 1125, 69], [1405, 24, 1125, 69, "_jsxFileName"], [1405, 36, 1125, 69], [1406, 14, 1125, 69, "lineNumber"], [1406, 24, 1125, 69], [1407, 14, 1125, 69, "columnNumber"], [1407, 26, 1125, 69], [1408, 12, 1125, 69], [1408, 19, 1126, 30], [1408, 20, 1126, 31], [1408, 35, 1127, 12], [1408, 39, 1127, 12, "_jsxDevRuntime"], [1408, 53, 1127, 12], [1408, 54, 1127, 12, "jsxDEV"], [1408, 60, 1127, 12], [1408, 62, 1127, 13, "_TouchableOpacity"], [1408, 79, 1127, 13], [1408, 80, 1127, 13, "default"], [1408, 87, 1127, 29], [1409, 14, 1128, 14, "onPress"], [1409, 21, 1128, 21], [1409, 23, 1128, 23, "onCancel"], [1409, 31, 1128, 32], [1410, 14, 1129, 14, "style"], [1410, 19, 1129, 19], [1410, 21, 1129, 21, "styles"], [1410, 27, 1129, 27], [1410, 28, 1129, 28, "secondaryButton"], [1410, 43, 1129, 44], [1411, 14, 1129, 44, "children"], [1411, 22, 1129, 44], [1411, 37, 1131, 14], [1411, 41, 1131, 14, "_jsxDevRuntime"], [1411, 55, 1131, 14], [1411, 56, 1131, 14, "jsxDEV"], [1411, 62, 1131, 14], [1411, 64, 1131, 15, "_Text"], [1411, 69, 1131, 15], [1411, 70, 1131, 15, "default"], [1411, 77, 1131, 19], [1412, 16, 1131, 20, "style"], [1412, 21, 1131, 25], [1412, 23, 1131, 27, "styles"], [1412, 29, 1131, 33], [1412, 30, 1131, 34, "secondaryButtonText"], [1412, 49, 1131, 54], [1413, 16, 1131, 54, "children"], [1413, 24, 1131, 54], [1413, 26, 1131, 55], [1414, 14, 1131, 61], [1415, 16, 1131, 61, "fileName"], [1415, 24, 1131, 61], [1415, 26, 1131, 61, "_jsxFileName"], [1415, 38, 1131, 61], [1416, 16, 1131, 61, "lineNumber"], [1416, 26, 1131, 61], [1417, 16, 1131, 61, "columnNumber"], [1417, 28, 1131, 61], [1418, 14, 1131, 61], [1418, 21, 1131, 67], [1419, 12, 1131, 68], [1420, 14, 1131, 68, "fileName"], [1420, 22, 1131, 68], [1420, 24, 1131, 68, "_jsxFileName"], [1420, 36, 1131, 68], [1421, 14, 1131, 68, "lineNumber"], [1421, 24, 1131, 68], [1422, 14, 1131, 68, "columnNumber"], [1422, 26, 1131, 68], [1423, 12, 1131, 68], [1423, 19, 1132, 30], [1423, 20, 1132, 31], [1424, 10, 1132, 31], [1425, 12, 1132, 31, "fileName"], [1425, 20, 1132, 31], [1425, 22, 1132, 31, "_jsxFileName"], [1425, 34, 1132, 31], [1426, 12, 1132, 31, "lineNumber"], [1426, 22, 1132, 31], [1427, 12, 1132, 31, "columnNumber"], [1427, 24, 1132, 31], [1428, 10, 1132, 31], [1428, 17, 1133, 16], [1429, 8, 1133, 17], [1430, 10, 1133, 17, "fileName"], [1430, 18, 1133, 17], [1430, 20, 1133, 17, "_jsxFileName"], [1430, 32, 1133, 17], [1431, 10, 1133, 17, "lineNumber"], [1431, 20, 1133, 17], [1432, 10, 1133, 17, "columnNumber"], [1432, 22, 1133, 17], [1433, 8, 1133, 17], [1433, 15, 1134, 14], [1434, 6, 1134, 15], [1435, 8, 1134, 15, "fileName"], [1435, 16, 1134, 15], [1435, 18, 1134, 15, "_jsxFileName"], [1435, 30, 1134, 15], [1436, 8, 1134, 15, "lineNumber"], [1436, 18, 1134, 15], [1437, 8, 1134, 15, "columnNumber"], [1437, 20, 1134, 15], [1438, 6, 1134, 15], [1438, 13, 1135, 13], [1438, 14, 1135, 14], [1439, 4, 1135, 14], [1440, 6, 1135, 14, "fileName"], [1440, 14, 1135, 14], [1440, 16, 1135, 14, "_jsxFileName"], [1440, 28, 1135, 14], [1441, 6, 1135, 14, "lineNumber"], [1441, 16, 1135, 14], [1442, 6, 1135, 14, "columnNumber"], [1442, 18, 1135, 14], [1443, 4, 1135, 14], [1443, 11, 1136, 10], [1443, 12, 1136, 11], [1444, 2, 1138, 0], [1445, 2, 1138, 1, "_s"], [1445, 4, 1138, 1], [1445, 5, 52, 24, "EchoCameraWeb"], [1445, 18, 52, 37], [1446, 4, 52, 37], [1446, 12, 59, 42, "useCameraPermissions"], [1446, 44, 59, 62], [1446, 46, 73, 19, "useUpload"], [1446, 64, 73, 28], [1447, 2, 73, 28], [1448, 2, 73, 28, "_c"], [1448, 4, 73, 28], [1448, 7, 52, 24, "EchoCameraWeb"], [1448, 20, 52, 37], [1449, 2, 1139, 0], [1449, 8, 1139, 6, "styles"], [1449, 14, 1139, 12], [1449, 17, 1139, 15, "StyleSheet"], [1449, 36, 1139, 25], [1449, 37, 1139, 26, "create"], [1449, 43, 1139, 32], [1449, 44, 1139, 33], [1450, 4, 1140, 2, "container"], [1450, 13, 1140, 11], [1450, 15, 1140, 13], [1451, 6, 1141, 4, "flex"], [1451, 10, 1141, 8], [1451, 12, 1141, 10], [1451, 13, 1141, 11], [1452, 6, 1142, 4, "backgroundColor"], [1452, 21, 1142, 19], [1452, 23, 1142, 21], [1453, 4, 1143, 2], [1453, 5, 1143, 3], [1454, 4, 1144, 2, "cameraContainer"], [1454, 19, 1144, 17], [1454, 21, 1144, 19], [1455, 6, 1145, 4, "flex"], [1455, 10, 1145, 8], [1455, 12, 1145, 10], [1455, 13, 1145, 11], [1456, 6, 1146, 4, "max<PERSON><PERSON><PERSON>"], [1456, 14, 1146, 12], [1456, 16, 1146, 14], [1456, 19, 1146, 17], [1457, 6, 1147, 4, "alignSelf"], [1457, 15, 1147, 13], [1457, 17, 1147, 15], [1457, 25, 1147, 23], [1458, 6, 1148, 4, "width"], [1458, 11, 1148, 9], [1458, 13, 1148, 11], [1459, 4, 1149, 2], [1459, 5, 1149, 3], [1460, 4, 1150, 2, "camera"], [1460, 10, 1150, 8], [1460, 12, 1150, 10], [1461, 6, 1151, 4, "flex"], [1461, 10, 1151, 8], [1461, 12, 1151, 10], [1462, 4, 1152, 2], [1462, 5, 1152, 3], [1463, 4, 1153, 2, "headerOverlay"], [1463, 17, 1153, 15], [1463, 19, 1153, 17], [1464, 6, 1154, 4, "position"], [1464, 14, 1154, 12], [1464, 16, 1154, 14], [1464, 26, 1154, 24], [1465, 6, 1155, 4, "top"], [1465, 9, 1155, 7], [1465, 11, 1155, 9], [1465, 12, 1155, 10], [1466, 6, 1156, 4, "left"], [1466, 10, 1156, 8], [1466, 12, 1156, 10], [1466, 13, 1156, 11], [1467, 6, 1157, 4, "right"], [1467, 11, 1157, 9], [1467, 13, 1157, 11], [1467, 14, 1157, 12], [1468, 6, 1158, 4, "backgroundColor"], [1468, 21, 1158, 19], [1468, 23, 1158, 21], [1468, 36, 1158, 34], [1469, 6, 1159, 4, "paddingTop"], [1469, 16, 1159, 14], [1469, 18, 1159, 16], [1469, 20, 1159, 18], [1470, 6, 1160, 4, "paddingHorizontal"], [1470, 23, 1160, 21], [1470, 25, 1160, 23], [1470, 27, 1160, 25], [1471, 6, 1161, 4, "paddingBottom"], [1471, 19, 1161, 17], [1471, 21, 1161, 19], [1472, 4, 1162, 2], [1472, 5, 1162, 3], [1473, 4, 1163, 2, "headerContent"], [1473, 17, 1163, 15], [1473, 19, 1163, 17], [1474, 6, 1164, 4, "flexDirection"], [1474, 19, 1164, 17], [1474, 21, 1164, 19], [1474, 26, 1164, 24], [1475, 6, 1165, 4, "justifyContent"], [1475, 20, 1165, 18], [1475, 22, 1165, 20], [1475, 37, 1165, 35], [1476, 6, 1166, 4, "alignItems"], [1476, 16, 1166, 14], [1476, 18, 1166, 16], [1477, 4, 1167, 2], [1477, 5, 1167, 3], [1478, 4, 1168, 2, "headerLeft"], [1478, 14, 1168, 12], [1478, 16, 1168, 14], [1479, 6, 1169, 4, "flex"], [1479, 10, 1169, 8], [1479, 12, 1169, 10], [1480, 4, 1170, 2], [1480, 5, 1170, 3], [1481, 4, 1171, 2, "headerTitle"], [1481, 15, 1171, 13], [1481, 17, 1171, 15], [1482, 6, 1172, 4, "fontSize"], [1482, 14, 1172, 12], [1482, 16, 1172, 14], [1482, 18, 1172, 16], [1483, 6, 1173, 4, "fontWeight"], [1483, 16, 1173, 14], [1483, 18, 1173, 16], [1483, 23, 1173, 21], [1484, 6, 1174, 4, "color"], [1484, 11, 1174, 9], [1484, 13, 1174, 11], [1484, 19, 1174, 17], [1485, 6, 1175, 4, "marginBottom"], [1485, 18, 1175, 16], [1485, 20, 1175, 18], [1486, 4, 1176, 2], [1486, 5, 1176, 3], [1487, 4, 1177, 2, "subtitleRow"], [1487, 15, 1177, 13], [1487, 17, 1177, 15], [1488, 6, 1178, 4, "flexDirection"], [1488, 19, 1178, 17], [1488, 21, 1178, 19], [1488, 26, 1178, 24], [1489, 6, 1179, 4, "alignItems"], [1489, 16, 1179, 14], [1489, 18, 1179, 16], [1489, 26, 1179, 24], [1490, 6, 1180, 4, "marginBottom"], [1490, 18, 1180, 16], [1490, 20, 1180, 18], [1491, 4, 1181, 2], [1491, 5, 1181, 3], [1492, 4, 1182, 2, "webIcon"], [1492, 11, 1182, 9], [1492, 13, 1182, 11], [1493, 6, 1183, 4, "fontSize"], [1493, 14, 1183, 12], [1493, 16, 1183, 14], [1493, 18, 1183, 16], [1494, 6, 1184, 4, "marginRight"], [1494, 17, 1184, 15], [1494, 19, 1184, 17], [1495, 4, 1185, 2], [1495, 5, 1185, 3], [1496, 4, 1186, 2, "headerSubtitle"], [1496, 18, 1186, 16], [1496, 20, 1186, 18], [1497, 6, 1187, 4, "fontSize"], [1497, 14, 1187, 12], [1497, 16, 1187, 14], [1497, 18, 1187, 16], [1498, 6, 1188, 4, "color"], [1498, 11, 1188, 9], [1498, 13, 1188, 11], [1498, 19, 1188, 17], [1499, 6, 1189, 4, "opacity"], [1499, 13, 1189, 11], [1499, 15, 1189, 13], [1500, 4, 1190, 2], [1500, 5, 1190, 3], [1501, 4, 1191, 2, "challengeRow"], [1501, 16, 1191, 14], [1501, 18, 1191, 16], [1502, 6, 1192, 4, "flexDirection"], [1502, 19, 1192, 17], [1502, 21, 1192, 19], [1502, 26, 1192, 24], [1503, 6, 1193, 4, "alignItems"], [1503, 16, 1193, 14], [1503, 18, 1193, 16], [1504, 4, 1194, 2], [1504, 5, 1194, 3], [1505, 4, 1195, 2, "challengeCode"], [1505, 17, 1195, 15], [1505, 19, 1195, 17], [1506, 6, 1196, 4, "fontSize"], [1506, 14, 1196, 12], [1506, 16, 1196, 14], [1506, 18, 1196, 16], [1507, 6, 1197, 4, "color"], [1507, 11, 1197, 9], [1507, 13, 1197, 11], [1507, 19, 1197, 17], [1508, 6, 1198, 4, "marginLeft"], [1508, 16, 1198, 14], [1508, 18, 1198, 16], [1508, 19, 1198, 17], [1509, 6, 1199, 4, "fontFamily"], [1509, 16, 1199, 14], [1509, 18, 1199, 16], [1510, 4, 1200, 2], [1510, 5, 1200, 3], [1511, 4, 1201, 2, "closeButton"], [1511, 15, 1201, 13], [1511, 17, 1201, 15], [1512, 6, 1202, 4, "padding"], [1512, 13, 1202, 11], [1512, 15, 1202, 13], [1513, 4, 1203, 2], [1513, 5, 1203, 3], [1514, 4, 1204, 2, "privacyNotice"], [1514, 17, 1204, 15], [1514, 19, 1204, 17], [1515, 6, 1205, 4, "position"], [1515, 14, 1205, 12], [1515, 16, 1205, 14], [1515, 26, 1205, 24], [1516, 6, 1206, 4, "top"], [1516, 9, 1206, 7], [1516, 11, 1206, 9], [1516, 14, 1206, 12], [1517, 6, 1207, 4, "left"], [1517, 10, 1207, 8], [1517, 12, 1207, 10], [1517, 14, 1207, 12], [1518, 6, 1208, 4, "right"], [1518, 11, 1208, 9], [1518, 13, 1208, 11], [1518, 15, 1208, 13], [1519, 6, 1209, 4, "backgroundColor"], [1519, 21, 1209, 19], [1519, 23, 1209, 21], [1519, 48, 1209, 46], [1520, 6, 1210, 4, "borderRadius"], [1520, 18, 1210, 16], [1520, 20, 1210, 18], [1520, 21, 1210, 19], [1521, 6, 1211, 4, "padding"], [1521, 13, 1211, 11], [1521, 15, 1211, 13], [1521, 17, 1211, 15], [1522, 6, 1212, 4, "flexDirection"], [1522, 19, 1212, 17], [1522, 21, 1212, 19], [1522, 26, 1212, 24], [1523, 6, 1213, 4, "alignItems"], [1523, 16, 1213, 14], [1523, 18, 1213, 16], [1524, 4, 1214, 2], [1524, 5, 1214, 3], [1525, 4, 1215, 2, "privacyText"], [1525, 15, 1215, 13], [1525, 17, 1215, 15], [1526, 6, 1216, 4, "color"], [1526, 11, 1216, 9], [1526, 13, 1216, 11], [1526, 19, 1216, 17], [1527, 6, 1217, 4, "fontSize"], [1527, 14, 1217, 12], [1527, 16, 1217, 14], [1527, 18, 1217, 16], [1528, 6, 1218, 4, "marginLeft"], [1528, 16, 1218, 14], [1528, 18, 1218, 16], [1528, 19, 1218, 17], [1529, 6, 1219, 4, "flex"], [1529, 10, 1219, 8], [1529, 12, 1219, 10], [1530, 4, 1220, 2], [1530, 5, 1220, 3], [1531, 4, 1221, 2, "footer<PERSON><PERSON><PERSON>"], [1531, 17, 1221, 15], [1531, 19, 1221, 17], [1532, 6, 1222, 4, "position"], [1532, 14, 1222, 12], [1532, 16, 1222, 14], [1532, 26, 1222, 24], [1533, 6, 1223, 4, "bottom"], [1533, 12, 1223, 10], [1533, 14, 1223, 12], [1533, 15, 1223, 13], [1534, 6, 1224, 4, "left"], [1534, 10, 1224, 8], [1534, 12, 1224, 10], [1534, 13, 1224, 11], [1535, 6, 1225, 4, "right"], [1535, 11, 1225, 9], [1535, 13, 1225, 11], [1535, 14, 1225, 12], [1536, 6, 1226, 4, "backgroundColor"], [1536, 21, 1226, 19], [1536, 23, 1226, 21], [1536, 36, 1226, 34], [1537, 6, 1227, 4, "paddingBottom"], [1537, 19, 1227, 17], [1537, 21, 1227, 19], [1537, 23, 1227, 21], [1538, 6, 1228, 4, "paddingTop"], [1538, 16, 1228, 14], [1538, 18, 1228, 16], [1538, 20, 1228, 18], [1539, 6, 1229, 4, "alignItems"], [1539, 16, 1229, 14], [1539, 18, 1229, 16], [1540, 4, 1230, 2], [1540, 5, 1230, 3], [1541, 4, 1231, 2, "instruction"], [1541, 15, 1231, 13], [1541, 17, 1231, 15], [1542, 6, 1232, 4, "fontSize"], [1542, 14, 1232, 12], [1542, 16, 1232, 14], [1542, 18, 1232, 16], [1543, 6, 1233, 4, "color"], [1543, 11, 1233, 9], [1543, 13, 1233, 11], [1543, 19, 1233, 17], [1544, 6, 1234, 4, "marginBottom"], [1544, 18, 1234, 16], [1544, 20, 1234, 18], [1545, 4, 1235, 2], [1545, 5, 1235, 3], [1546, 4, 1236, 2, "shutterButton"], [1546, 17, 1236, 15], [1546, 19, 1236, 17], [1547, 6, 1237, 4, "width"], [1547, 11, 1237, 9], [1547, 13, 1237, 11], [1547, 15, 1237, 13], [1548, 6, 1238, 4, "height"], [1548, 12, 1238, 10], [1548, 14, 1238, 12], [1548, 16, 1238, 14], [1549, 6, 1239, 4, "borderRadius"], [1549, 18, 1239, 16], [1549, 20, 1239, 18], [1549, 22, 1239, 20], [1550, 6, 1240, 4, "backgroundColor"], [1550, 21, 1240, 19], [1550, 23, 1240, 21], [1550, 29, 1240, 27], [1551, 6, 1241, 4, "justifyContent"], [1551, 20, 1241, 18], [1551, 22, 1241, 20], [1551, 30, 1241, 28], [1552, 6, 1242, 4, "alignItems"], [1552, 16, 1242, 14], [1552, 18, 1242, 16], [1552, 26, 1242, 24], [1553, 6, 1243, 4, "marginBottom"], [1553, 18, 1243, 16], [1553, 20, 1243, 18], [1553, 22, 1243, 20], [1554, 6, 1244, 4], [1554, 9, 1244, 7, "Platform"], [1554, 26, 1244, 15], [1554, 27, 1244, 16, "select"], [1554, 33, 1244, 22], [1554, 34, 1244, 23], [1555, 8, 1245, 6, "ios"], [1555, 11, 1245, 9], [1555, 13, 1245, 11], [1556, 10, 1246, 8, "shadowColor"], [1556, 21, 1246, 19], [1556, 23, 1246, 21], [1556, 32, 1246, 30], [1557, 10, 1247, 8, "shadowOffset"], [1557, 22, 1247, 20], [1557, 24, 1247, 22], [1558, 12, 1247, 24, "width"], [1558, 17, 1247, 29], [1558, 19, 1247, 31], [1558, 20, 1247, 32], [1559, 12, 1247, 34, "height"], [1559, 18, 1247, 40], [1559, 20, 1247, 42], [1560, 10, 1247, 44], [1560, 11, 1247, 45], [1561, 10, 1248, 8, "shadowOpacity"], [1561, 23, 1248, 21], [1561, 25, 1248, 23], [1561, 28, 1248, 26], [1562, 10, 1249, 8, "shadowRadius"], [1562, 22, 1249, 20], [1562, 24, 1249, 22], [1563, 8, 1250, 6], [1563, 9, 1250, 7], [1564, 8, 1251, 6, "android"], [1564, 15, 1251, 13], [1564, 17, 1251, 15], [1565, 10, 1252, 8, "elevation"], [1565, 19, 1252, 17], [1565, 21, 1252, 19], [1566, 8, 1253, 6], [1566, 9, 1253, 7], [1567, 8, 1254, 6, "web"], [1567, 11, 1254, 9], [1567, 13, 1254, 11], [1568, 10, 1255, 8, "boxShadow"], [1568, 19, 1255, 17], [1568, 21, 1255, 19], [1569, 8, 1256, 6], [1570, 6, 1257, 4], [1570, 7, 1257, 5], [1571, 4, 1258, 2], [1571, 5, 1258, 3], [1572, 4, 1259, 2, "shutterButtonDisabled"], [1572, 25, 1259, 23], [1572, 27, 1259, 25], [1573, 6, 1260, 4, "opacity"], [1573, 13, 1260, 11], [1573, 15, 1260, 13], [1574, 4, 1261, 2], [1574, 5, 1261, 3], [1575, 4, 1262, 2, "shutterInner"], [1575, 16, 1262, 14], [1575, 18, 1262, 16], [1576, 6, 1263, 4, "width"], [1576, 11, 1263, 9], [1576, 13, 1263, 11], [1576, 15, 1263, 13], [1577, 6, 1264, 4, "height"], [1577, 12, 1264, 10], [1577, 14, 1264, 12], [1577, 16, 1264, 14], [1578, 6, 1265, 4, "borderRadius"], [1578, 18, 1265, 16], [1578, 20, 1265, 18], [1578, 22, 1265, 20], [1579, 6, 1266, 4, "backgroundColor"], [1579, 21, 1266, 19], [1579, 23, 1266, 21], [1579, 29, 1266, 27], [1580, 6, 1267, 4, "borderWidth"], [1580, 17, 1267, 15], [1580, 19, 1267, 17], [1580, 20, 1267, 18], [1581, 6, 1268, 4, "borderColor"], [1581, 17, 1268, 15], [1581, 19, 1268, 17], [1582, 4, 1269, 2], [1582, 5, 1269, 3], [1583, 4, 1270, 2, "privacyNote"], [1583, 15, 1270, 13], [1583, 17, 1270, 15], [1584, 6, 1271, 4, "fontSize"], [1584, 14, 1271, 12], [1584, 16, 1271, 14], [1584, 18, 1271, 16], [1585, 6, 1272, 4, "color"], [1585, 11, 1272, 9], [1585, 13, 1272, 11], [1586, 4, 1273, 2], [1586, 5, 1273, 3], [1587, 4, 1274, 2, "processingModal"], [1587, 19, 1274, 17], [1587, 21, 1274, 19], [1588, 6, 1275, 4, "flex"], [1588, 10, 1275, 8], [1588, 12, 1275, 10], [1588, 13, 1275, 11], [1589, 6, 1276, 4, "backgroundColor"], [1589, 21, 1276, 19], [1589, 23, 1276, 21], [1589, 43, 1276, 41], [1590, 6, 1277, 4, "justifyContent"], [1590, 20, 1277, 18], [1590, 22, 1277, 20], [1590, 30, 1277, 28], [1591, 6, 1278, 4, "alignItems"], [1591, 16, 1278, 14], [1591, 18, 1278, 16], [1592, 4, 1279, 2], [1592, 5, 1279, 3], [1593, 4, 1280, 2, "processingContent"], [1593, 21, 1280, 19], [1593, 23, 1280, 21], [1594, 6, 1281, 4, "backgroundColor"], [1594, 21, 1281, 19], [1594, 23, 1281, 21], [1594, 29, 1281, 27], [1595, 6, 1282, 4, "borderRadius"], [1595, 18, 1282, 16], [1595, 20, 1282, 18], [1595, 22, 1282, 20], [1596, 6, 1283, 4, "padding"], [1596, 13, 1283, 11], [1596, 15, 1283, 13], [1596, 17, 1283, 15], [1597, 6, 1284, 4, "width"], [1597, 11, 1284, 9], [1597, 13, 1284, 11], [1597, 18, 1284, 16], [1598, 6, 1285, 4, "max<PERSON><PERSON><PERSON>"], [1598, 14, 1285, 12], [1598, 16, 1285, 14], [1598, 19, 1285, 17], [1599, 6, 1286, 4, "alignItems"], [1599, 16, 1286, 14], [1599, 18, 1286, 16], [1600, 4, 1287, 2], [1600, 5, 1287, 3], [1601, 4, 1288, 2, "processingTitle"], [1601, 19, 1288, 17], [1601, 21, 1288, 19], [1602, 6, 1289, 4, "fontSize"], [1602, 14, 1289, 12], [1602, 16, 1289, 14], [1602, 18, 1289, 16], [1603, 6, 1290, 4, "fontWeight"], [1603, 16, 1290, 14], [1603, 18, 1290, 16], [1603, 23, 1290, 21], [1604, 6, 1291, 4, "color"], [1604, 11, 1291, 9], [1604, 13, 1291, 11], [1604, 22, 1291, 20], [1605, 6, 1292, 4, "marginTop"], [1605, 15, 1292, 13], [1605, 17, 1292, 15], [1605, 19, 1292, 17], [1606, 6, 1293, 4, "marginBottom"], [1606, 18, 1293, 16], [1606, 20, 1293, 18], [1607, 4, 1294, 2], [1607, 5, 1294, 3], [1608, 4, 1295, 2, "progressBar"], [1608, 15, 1295, 13], [1608, 17, 1295, 15], [1609, 6, 1296, 4, "width"], [1609, 11, 1296, 9], [1609, 13, 1296, 11], [1609, 19, 1296, 17], [1610, 6, 1297, 4, "height"], [1610, 12, 1297, 10], [1610, 14, 1297, 12], [1610, 15, 1297, 13], [1611, 6, 1298, 4, "backgroundColor"], [1611, 21, 1298, 19], [1611, 23, 1298, 21], [1611, 32, 1298, 30], [1612, 6, 1299, 4, "borderRadius"], [1612, 18, 1299, 16], [1612, 20, 1299, 18], [1612, 21, 1299, 19], [1613, 6, 1300, 4, "overflow"], [1613, 14, 1300, 12], [1613, 16, 1300, 14], [1613, 24, 1300, 22], [1614, 6, 1301, 4, "marginBottom"], [1614, 18, 1301, 16], [1614, 20, 1301, 18], [1615, 4, 1302, 2], [1615, 5, 1302, 3], [1616, 4, 1303, 2, "progressFill"], [1616, 16, 1303, 14], [1616, 18, 1303, 16], [1617, 6, 1304, 4, "height"], [1617, 12, 1304, 10], [1617, 14, 1304, 12], [1617, 20, 1304, 18], [1618, 6, 1305, 4, "backgroundColor"], [1618, 21, 1305, 19], [1618, 23, 1305, 21], [1618, 32, 1305, 30], [1619, 6, 1306, 4, "borderRadius"], [1619, 18, 1306, 16], [1619, 20, 1306, 18], [1620, 4, 1307, 2], [1620, 5, 1307, 3], [1621, 4, 1308, 2, "processingDescription"], [1621, 25, 1308, 23], [1621, 27, 1308, 25], [1622, 6, 1309, 4, "fontSize"], [1622, 14, 1309, 12], [1622, 16, 1309, 14], [1622, 18, 1309, 16], [1623, 6, 1310, 4, "color"], [1623, 11, 1310, 9], [1623, 13, 1310, 11], [1623, 22, 1310, 20], [1624, 6, 1311, 4, "textAlign"], [1624, 15, 1311, 13], [1624, 17, 1311, 15], [1625, 4, 1312, 2], [1625, 5, 1312, 3], [1626, 4, 1313, 2, "successIcon"], [1626, 15, 1313, 13], [1626, 17, 1313, 15], [1627, 6, 1314, 4, "marginTop"], [1627, 15, 1314, 13], [1627, 17, 1314, 15], [1628, 4, 1315, 2], [1628, 5, 1315, 3], [1629, 4, 1316, 2, "errorContent"], [1629, 16, 1316, 14], [1629, 18, 1316, 16], [1630, 6, 1317, 4, "backgroundColor"], [1630, 21, 1317, 19], [1630, 23, 1317, 21], [1630, 29, 1317, 27], [1631, 6, 1318, 4, "borderRadius"], [1631, 18, 1318, 16], [1631, 20, 1318, 18], [1631, 22, 1318, 20], [1632, 6, 1319, 4, "padding"], [1632, 13, 1319, 11], [1632, 15, 1319, 13], [1632, 17, 1319, 15], [1633, 6, 1320, 4, "width"], [1633, 11, 1320, 9], [1633, 13, 1320, 11], [1633, 18, 1320, 16], [1634, 6, 1321, 4, "max<PERSON><PERSON><PERSON>"], [1634, 14, 1321, 12], [1634, 16, 1321, 14], [1634, 19, 1321, 17], [1635, 6, 1322, 4, "alignItems"], [1635, 16, 1322, 14], [1635, 18, 1322, 16], [1636, 4, 1323, 2], [1636, 5, 1323, 3], [1637, 4, 1324, 2, "errorTitle"], [1637, 14, 1324, 12], [1637, 16, 1324, 14], [1638, 6, 1325, 4, "fontSize"], [1638, 14, 1325, 12], [1638, 16, 1325, 14], [1638, 18, 1325, 16], [1639, 6, 1326, 4, "fontWeight"], [1639, 16, 1326, 14], [1639, 18, 1326, 16], [1639, 23, 1326, 21], [1640, 6, 1327, 4, "color"], [1640, 11, 1327, 9], [1640, 13, 1327, 11], [1640, 22, 1327, 20], [1641, 6, 1328, 4, "marginTop"], [1641, 15, 1328, 13], [1641, 17, 1328, 15], [1641, 19, 1328, 17], [1642, 6, 1329, 4, "marginBottom"], [1642, 18, 1329, 16], [1642, 20, 1329, 18], [1643, 4, 1330, 2], [1643, 5, 1330, 3], [1644, 4, 1331, 2, "errorMessage"], [1644, 16, 1331, 14], [1644, 18, 1331, 16], [1645, 6, 1332, 4, "fontSize"], [1645, 14, 1332, 12], [1645, 16, 1332, 14], [1645, 18, 1332, 16], [1646, 6, 1333, 4, "color"], [1646, 11, 1333, 9], [1646, 13, 1333, 11], [1646, 22, 1333, 20], [1647, 6, 1334, 4, "textAlign"], [1647, 15, 1334, 13], [1647, 17, 1334, 15], [1647, 25, 1334, 23], [1648, 6, 1335, 4, "marginBottom"], [1648, 18, 1335, 16], [1648, 20, 1335, 18], [1649, 4, 1336, 2], [1649, 5, 1336, 3], [1650, 4, 1337, 2, "primaryButton"], [1650, 17, 1337, 15], [1650, 19, 1337, 17], [1651, 6, 1338, 4, "backgroundColor"], [1651, 21, 1338, 19], [1651, 23, 1338, 21], [1651, 32, 1338, 30], [1652, 6, 1339, 4, "paddingHorizontal"], [1652, 23, 1339, 21], [1652, 25, 1339, 23], [1652, 27, 1339, 25], [1653, 6, 1340, 4, "paddingVertical"], [1653, 21, 1340, 19], [1653, 23, 1340, 21], [1653, 25, 1340, 23], [1654, 6, 1341, 4, "borderRadius"], [1654, 18, 1341, 16], [1654, 20, 1341, 18], [1654, 21, 1341, 19], [1655, 6, 1342, 4, "marginTop"], [1655, 15, 1342, 13], [1655, 17, 1342, 15], [1656, 4, 1343, 2], [1656, 5, 1343, 3], [1657, 4, 1344, 2, "primaryButtonText"], [1657, 21, 1344, 19], [1657, 23, 1344, 21], [1658, 6, 1345, 4, "color"], [1658, 11, 1345, 9], [1658, 13, 1345, 11], [1658, 19, 1345, 17], [1659, 6, 1346, 4, "fontSize"], [1659, 14, 1346, 12], [1659, 16, 1346, 14], [1659, 18, 1346, 16], [1660, 6, 1347, 4, "fontWeight"], [1660, 16, 1347, 14], [1660, 18, 1347, 16], [1661, 4, 1348, 2], [1661, 5, 1348, 3], [1662, 4, 1349, 2, "secondaryButton"], [1662, 19, 1349, 17], [1662, 21, 1349, 19], [1663, 6, 1350, 4, "paddingHorizontal"], [1663, 23, 1350, 21], [1663, 25, 1350, 23], [1663, 27, 1350, 25], [1664, 6, 1351, 4, "paddingVertical"], [1664, 21, 1351, 19], [1664, 23, 1351, 21], [1664, 25, 1351, 23], [1665, 6, 1352, 4, "marginTop"], [1665, 15, 1352, 13], [1665, 17, 1352, 15], [1666, 4, 1353, 2], [1666, 5, 1353, 3], [1667, 4, 1354, 2, "secondaryButtonText"], [1667, 23, 1354, 21], [1667, 25, 1354, 23], [1668, 6, 1355, 4, "color"], [1668, 11, 1355, 9], [1668, 13, 1355, 11], [1668, 22, 1355, 20], [1669, 6, 1356, 4, "fontSize"], [1669, 14, 1356, 12], [1669, 16, 1356, 14], [1670, 4, 1357, 2], [1670, 5, 1357, 3], [1671, 4, 1358, 2, "permissionContent"], [1671, 21, 1358, 19], [1671, 23, 1358, 21], [1672, 6, 1359, 4, "flex"], [1672, 10, 1359, 8], [1672, 12, 1359, 10], [1672, 13, 1359, 11], [1673, 6, 1360, 4, "justifyContent"], [1673, 20, 1360, 18], [1673, 22, 1360, 20], [1673, 30, 1360, 28], [1674, 6, 1361, 4, "alignItems"], [1674, 16, 1361, 14], [1674, 18, 1361, 16], [1674, 26, 1361, 24], [1675, 6, 1362, 4, "padding"], [1675, 13, 1362, 11], [1675, 15, 1362, 13], [1676, 4, 1363, 2], [1676, 5, 1363, 3], [1677, 4, 1364, 2, "permissionTitle"], [1677, 19, 1364, 17], [1677, 21, 1364, 19], [1678, 6, 1365, 4, "fontSize"], [1678, 14, 1365, 12], [1678, 16, 1365, 14], [1678, 18, 1365, 16], [1679, 6, 1366, 4, "fontWeight"], [1679, 16, 1366, 14], [1679, 18, 1366, 16], [1679, 23, 1366, 21], [1680, 6, 1367, 4, "color"], [1680, 11, 1367, 9], [1680, 13, 1367, 11], [1680, 22, 1367, 20], [1681, 6, 1368, 4, "marginTop"], [1681, 15, 1368, 13], [1681, 17, 1368, 15], [1681, 19, 1368, 17], [1682, 6, 1369, 4, "marginBottom"], [1682, 18, 1369, 16], [1682, 20, 1369, 18], [1683, 4, 1370, 2], [1683, 5, 1370, 3], [1684, 4, 1371, 2, "permissionDescription"], [1684, 25, 1371, 23], [1684, 27, 1371, 25], [1685, 6, 1372, 4, "fontSize"], [1685, 14, 1372, 12], [1685, 16, 1372, 14], [1685, 18, 1372, 16], [1686, 6, 1373, 4, "color"], [1686, 11, 1373, 9], [1686, 13, 1373, 11], [1686, 22, 1373, 20], [1687, 6, 1374, 4, "textAlign"], [1687, 15, 1374, 13], [1687, 17, 1374, 15], [1687, 25, 1374, 23], [1688, 6, 1375, 4, "marginBottom"], [1688, 18, 1375, 16], [1688, 20, 1375, 18], [1689, 4, 1376, 2], [1689, 5, 1376, 3], [1690, 4, 1377, 2, "loadingText"], [1690, 15, 1377, 13], [1690, 17, 1377, 15], [1691, 6, 1378, 4, "color"], [1691, 11, 1378, 9], [1691, 13, 1378, 11], [1691, 22, 1378, 20], [1692, 6, 1379, 4, "marginTop"], [1692, 15, 1379, 13], [1692, 17, 1379, 15], [1693, 4, 1380, 2], [1693, 5, 1380, 3], [1694, 4, 1381, 2], [1695, 4, 1382, 2, "blurZone"], [1695, 12, 1382, 10], [1695, 14, 1382, 12], [1696, 6, 1383, 4, "position"], [1696, 14, 1383, 12], [1696, 16, 1383, 14], [1696, 26, 1383, 24], [1697, 6, 1384, 4, "overflow"], [1697, 14, 1384, 12], [1697, 16, 1384, 14], [1698, 4, 1385, 2], [1698, 5, 1385, 3], [1699, 4, 1386, 2, "previewChip"], [1699, 15, 1386, 13], [1699, 17, 1386, 15], [1700, 6, 1387, 4, "position"], [1700, 14, 1387, 12], [1700, 16, 1387, 14], [1700, 26, 1387, 24], [1701, 6, 1388, 4, "top"], [1701, 9, 1388, 7], [1701, 11, 1388, 9], [1701, 12, 1388, 10], [1702, 6, 1389, 4, "right"], [1702, 11, 1389, 9], [1702, 13, 1389, 11], [1702, 14, 1389, 12], [1703, 6, 1390, 4, "backgroundColor"], [1703, 21, 1390, 19], [1703, 23, 1390, 21], [1703, 40, 1390, 38], [1704, 6, 1391, 4, "paddingHorizontal"], [1704, 23, 1391, 21], [1704, 25, 1391, 23], [1704, 27, 1391, 25], [1705, 6, 1392, 4, "paddingVertical"], [1705, 21, 1392, 19], [1705, 23, 1392, 21], [1705, 24, 1392, 22], [1706, 6, 1393, 4, "borderRadius"], [1706, 18, 1393, 16], [1706, 20, 1393, 18], [1707, 4, 1394, 2], [1707, 5, 1394, 3], [1708, 4, 1395, 2, "previewChipText"], [1708, 19, 1395, 17], [1708, 21, 1395, 19], [1709, 6, 1396, 4, "color"], [1709, 11, 1396, 9], [1709, 13, 1396, 11], [1709, 19, 1396, 17], [1710, 6, 1397, 4, "fontSize"], [1710, 14, 1397, 12], [1710, 16, 1397, 14], [1710, 18, 1397, 16], [1711, 6, 1398, 4, "fontWeight"], [1711, 16, 1398, 14], [1711, 18, 1398, 16], [1712, 4, 1399, 2], [1713, 2, 1400, 0], [1713, 3, 1400, 1], [1713, 4, 1400, 2], [1714, 2, 1400, 3], [1714, 6, 1400, 3, "_c"], [1714, 8, 1400, 3], [1715, 2, 1400, 3, "$RefreshReg$"], [1715, 14, 1400, 3], [1715, 15, 1400, 3, "_c"], [1715, 17, 1400, 3], [1716, 0, 1400, 3], [1716, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCmD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfuE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBsC;mCoBE;wBhBc;OgBI;oFC+C;UDM;8BE8B;SFoD;uDhBa;sBmBC,wBnB;OgBC;GpByB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8BkD;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD6K"}}, "type": "js/module"}]}