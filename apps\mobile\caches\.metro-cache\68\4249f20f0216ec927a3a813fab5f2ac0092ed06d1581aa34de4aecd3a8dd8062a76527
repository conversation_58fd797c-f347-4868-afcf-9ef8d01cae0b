{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 47, "index": 47}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "../utils.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 110}, "end": {"line": 4, "column": 42, "index": 152}}], "key": "V6Cd8v/K/LtdxP2wf1WD5HCNhqM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  var _utils = require(_dependencyMap[1], \"../utils.web\");\n  var _default = (canvas, options) => {\n    // ensure values are defined.\n    const {\n      backgroundColor = null,\n      originX = 0,\n      originY = 0,\n      width = 0,\n      height = 0\n    } = options;\n    if (width === 0 || height === 0) {\n      throw new _expoModulesCore.CodedError('ERR_IMAGE_MANIPULATOR_EXTENT', 'Extent size must be greater than 0: ' + JSON.stringify(options, null, 2));\n    }\n    const result = document.createElement('canvas');\n    result.width = width;\n    result.height = height;\n    const sx = originX < 0 ? 0 : originX;\n    const sy = originY < 0 ? 0 : originY;\n    const sw = originX < 0 ? Math.min(canvas.width, width + originX) : Math.min(canvas.width - originX, width);\n    const sh = originY < 0 ? Math.min(canvas.height, height + originY) : Math.min(canvas.height - originY, height);\n    const dx = originX < 0 ? -originX : 0;\n    const dy = originY < 0 ? -originY : 0;\n    const context = (0, _utils.getContext)(result);\n    if (backgroundColor != null) {\n      context.fillStyle = backgroundColor;\n      context.fillRect(0, 0, width, height);\n    }\n    context.drawImage(canvas, sx, sy, sw, sh, dx, dy, sw, sh);\n    return result;\n  };\n  exports.default = _default;\n});", "lineCount": 38, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_utils"], [7, 12, 4, 0], [7, 15, 4, 0, "require"], [7, 22, 4, 0], [7, 23, 4, 0, "_dependencyMap"], [7, 37, 4, 0], [8, 2, 4, 42], [8, 6, 4, 42, "_default"], [8, 14, 4, 42], [8, 17, 6, 15, "_default"], [8, 18, 6, 16, "canvas"], [8, 24, 6, 41], [8, 26, 6, 43, "options"], [8, 33, 6, 74], [8, 38, 6, 79], [9, 4, 7, 2], [10, 4, 8, 2], [10, 10, 8, 8], [11, 6, 8, 10, "backgroundColor"], [11, 21, 8, 25], [11, 24, 8, 28], [11, 28, 8, 32], [12, 6, 8, 34, "originX"], [12, 13, 8, 41], [12, 16, 8, 44], [12, 17, 8, 45], [13, 6, 8, 47, "originY"], [13, 13, 8, 54], [13, 16, 8, 57], [13, 17, 8, 58], [14, 6, 8, 60, "width"], [14, 11, 8, 65], [14, 14, 8, 68], [14, 15, 8, 69], [15, 6, 8, 71, "height"], [15, 12, 8, 77], [15, 15, 8, 80], [16, 4, 8, 82], [16, 5, 8, 83], [16, 8, 8, 86, "options"], [16, 15, 8, 93], [17, 4, 10, 2], [17, 8, 10, 6, "width"], [17, 13, 10, 11], [17, 18, 10, 16], [17, 19, 10, 17], [17, 23, 10, 21, "height"], [17, 29, 10, 27], [17, 34, 10, 32], [17, 35, 10, 33], [17, 37, 10, 35], [18, 6, 11, 4], [18, 12, 11, 10], [18, 16, 11, 14, "CodedError"], [18, 43, 11, 24], [18, 44, 12, 6], [18, 74, 12, 36], [18, 76, 13, 6], [18, 114, 13, 44], [18, 117, 13, 47, "JSON"], [18, 121, 13, 51], [18, 122, 13, 52, "stringify"], [18, 131, 13, 61], [18, 132, 13, 62, "options"], [18, 139, 13, 69], [18, 141, 13, 71], [18, 145, 13, 75], [18, 147, 13, 77], [18, 148, 13, 78], [18, 149, 14, 4], [18, 150, 14, 5], [19, 4, 15, 2], [20, 4, 17, 2], [20, 10, 17, 8, "result"], [20, 16, 17, 14], [20, 19, 17, 17, "document"], [20, 27, 17, 25], [20, 28, 17, 26, "createElement"], [20, 41, 17, 39], [20, 42, 17, 40], [20, 50, 17, 48], [20, 51, 17, 49], [21, 4, 18, 2, "result"], [21, 10, 18, 8], [21, 11, 18, 9, "width"], [21, 16, 18, 14], [21, 19, 18, 17, "width"], [21, 24, 18, 22], [22, 4, 19, 2, "result"], [22, 10, 19, 8], [22, 11, 19, 9, "height"], [22, 17, 19, 15], [22, 20, 19, 18, "height"], [22, 26, 19, 24], [23, 4, 21, 2], [23, 10, 21, 8, "sx"], [23, 12, 21, 10], [23, 15, 21, 13, "originX"], [23, 22, 21, 20], [23, 25, 21, 23], [23, 26, 21, 24], [23, 29, 21, 27], [23, 30, 21, 28], [23, 33, 21, 31, "originX"], [23, 40, 21, 38], [24, 4, 22, 2], [24, 10, 22, 8, "sy"], [24, 12, 22, 10], [24, 15, 22, 13, "originY"], [24, 22, 22, 20], [24, 25, 22, 23], [24, 26, 22, 24], [24, 29, 22, 27], [24, 30, 22, 28], [24, 33, 22, 31, "originY"], [24, 40, 22, 38], [25, 4, 23, 2], [25, 10, 23, 8, "sw"], [25, 12, 23, 10], [25, 15, 24, 4, "originX"], [25, 22, 24, 11], [25, 25, 24, 14], [25, 26, 24, 15], [25, 29, 24, 18, "Math"], [25, 33, 24, 22], [25, 34, 24, 23, "min"], [25, 37, 24, 26], [25, 38, 24, 27, "canvas"], [25, 44, 24, 33], [25, 45, 24, 34, "width"], [25, 50, 24, 39], [25, 52, 24, 41, "width"], [25, 57, 24, 46], [25, 60, 24, 49, "originX"], [25, 67, 24, 56], [25, 68, 24, 57], [25, 71, 24, 60, "Math"], [25, 75, 24, 64], [25, 76, 24, 65, "min"], [25, 79, 24, 68], [25, 80, 24, 69, "canvas"], [25, 86, 24, 75], [25, 87, 24, 76, "width"], [25, 92, 24, 81], [25, 95, 24, 84, "originX"], [25, 102, 24, 91], [25, 104, 24, 93, "width"], [25, 109, 24, 98], [25, 110, 24, 99], [26, 4, 25, 2], [26, 10, 25, 8, "sh"], [26, 12, 25, 10], [26, 15, 26, 4, "originY"], [26, 22, 26, 11], [26, 25, 26, 14], [26, 26, 26, 15], [26, 29, 27, 8, "Math"], [26, 33, 27, 12], [26, 34, 27, 13, "min"], [26, 37, 27, 16], [26, 38, 27, 17, "canvas"], [26, 44, 27, 23], [26, 45, 27, 24, "height"], [26, 51, 27, 30], [26, 53, 27, 32, "height"], [26, 59, 27, 38], [26, 62, 27, 41, "originY"], [26, 69, 27, 48], [26, 70, 27, 49], [26, 73, 28, 8, "Math"], [26, 77, 28, 12], [26, 78, 28, 13, "min"], [26, 81, 28, 16], [26, 82, 28, 17, "canvas"], [26, 88, 28, 23], [26, 89, 28, 24, "height"], [26, 95, 28, 30], [26, 98, 28, 33, "originY"], [26, 105, 28, 40], [26, 107, 28, 42, "height"], [26, 113, 28, 48], [26, 114, 28, 49], [27, 4, 30, 2], [27, 10, 30, 8, "dx"], [27, 12, 30, 10], [27, 15, 30, 13, "originX"], [27, 22, 30, 20], [27, 25, 30, 23], [27, 26, 30, 24], [27, 29, 30, 27], [27, 30, 30, 28, "originX"], [27, 37, 30, 35], [27, 40, 30, 38], [27, 41, 30, 39], [28, 4, 31, 2], [28, 10, 31, 8, "dy"], [28, 12, 31, 10], [28, 15, 31, 13, "originY"], [28, 22, 31, 20], [28, 25, 31, 23], [28, 26, 31, 24], [28, 29, 31, 27], [28, 30, 31, 28, "originY"], [28, 37, 31, 35], [28, 40, 31, 38], [28, 41, 31, 39], [29, 4, 33, 2], [29, 10, 33, 8, "context"], [29, 17, 33, 15], [29, 20, 33, 18], [29, 24, 33, 18, "getContext"], [29, 41, 33, 28], [29, 43, 33, 29, "result"], [29, 49, 33, 35], [29, 50, 33, 36], [30, 4, 35, 2], [30, 8, 35, 6, "backgroundColor"], [30, 23, 35, 21], [30, 27, 35, 25], [30, 31, 35, 29], [30, 33, 35, 31], [31, 6, 36, 4, "context"], [31, 13, 36, 11], [31, 14, 36, 12, "fillStyle"], [31, 23, 36, 21], [31, 26, 36, 24, "backgroundColor"], [31, 41, 36, 39], [32, 6, 37, 4, "context"], [32, 13, 37, 11], [32, 14, 37, 12, "fillRect"], [32, 22, 37, 20], [32, 23, 37, 21], [32, 24, 37, 22], [32, 26, 37, 24], [32, 27, 37, 25], [32, 29, 37, 27, "width"], [32, 34, 37, 32], [32, 36, 37, 34, "height"], [32, 42, 37, 40], [32, 43, 37, 41], [33, 4, 38, 2], [34, 4, 40, 2, "context"], [34, 11, 40, 9], [34, 12, 40, 10, "drawImage"], [34, 21, 40, 19], [34, 22, 40, 20, "canvas"], [34, 28, 40, 26], [34, 30, 40, 28, "sx"], [34, 32, 40, 30], [34, 34, 40, 32, "sy"], [34, 36, 40, 34], [34, 38, 40, 36, "sw"], [34, 40, 40, 38], [34, 42, 40, 40, "sh"], [34, 44, 40, 42], [34, 46, 40, 44, "dx"], [34, 48, 40, 46], [34, 50, 40, 48, "dy"], [34, 52, 40, 50], [34, 54, 40, 52, "sw"], [34, 56, 40, 54], [34, 58, 40, 56, "sh"], [34, 60, 40, 58], [34, 61, 40, 59], [35, 4, 42, 2], [35, 11, 42, 9, "result"], [35, 17, 42, 15], [36, 2, 43, 0], [36, 3, 43, 1], [37, 2, 43, 1, "exports"], [37, 9, 43, 1], [37, 10, 43, 1, "default"], [37, 17, 43, 1], [37, 20, 43, 1, "_default"], [37, 28, 43, 1], [38, 0, 43, 1], [38, 3]], "functionMap": {"names": ["<global>", "default"], "mappings": "AAA;eCK;CDqC"}}, "type": "js/module"}]}