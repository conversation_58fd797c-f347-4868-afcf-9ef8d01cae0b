{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 67, "index": 67}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkPath", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 68}, "end": {"line": 2, "column": 40, "index": 108}}], "key": "h12LvMRBvFyvLJVrX3awiGHZPFU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPathFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkPath = require(_dependencyMap[1], \"./JsiSkPath\");\n  class JsiSkPathFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    Make() {\n      return new _JsiSkPath.JsiSkPath(this.CanvasKit, new this.CanvasKit.Path());\n    }\n    MakeFromSVGString(str) {\n      const path = this.CanvasKit.Path.MakeFromSVGString(str);\n      if (path === null) {\n        return null;\n      }\n      return new _JsiSkPath.JsiSkPath(this.CanvasKit, path);\n    }\n    MakeFromOp(one, two, op) {\n      const path = this.CanvasKit.Path.MakeFromOp(_JsiSkPath.JsiSkPath.fromValue(one), _JsiSkPath.JsiSkPath.fromValue(two), (0, _Host.getEnum)(this.CanvasKit, \"PathOp\", op));\n      if (path === null) {\n        return null;\n      }\n      return new _JsiSkPath.JsiSkPath(this.CanvasKit, path);\n    }\n    MakeFromCmds(cmds) {\n      const path = this.CanvasKit.Path.MakeFromCmds(cmds.flat());\n      if (path === null) {\n        return null;\n      }\n      return new _JsiSkPath.JsiSkPath(this.CanvasKit, path);\n    }\n    MakeFromText(_text, _x, _y, _font) {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n  }\n  exports.JsiSkPathFactory = JsiSkPathFactory;\n});", "lineCount": 41, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkPath"], [7, 16, 2, 0], [7, 19, 2, 0, "require"], [7, 26, 2, 0], [7, 27, 2, 0, "_dependencyMap"], [7, 41, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkPathFactory"], [8, 24, 3, 29], [8, 33, 3, 38, "Host"], [8, 43, 3, 42], [8, 44, 3, 43], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "Make"], [12, 8, 7, 6, "Make"], [12, 9, 7, 6], [12, 11, 7, 9], [13, 6, 8, 4], [13, 13, 8, 11], [13, 17, 8, 15, "JsiSkPath"], [13, 37, 8, 24], [13, 38, 8, 25], [13, 42, 8, 29], [13, 43, 8, 30, "CanvasKit"], [13, 52, 8, 39], [13, 54, 8, 41], [13, 58, 8, 45], [13, 62, 8, 49], [13, 63, 8, 50, "CanvasKit"], [13, 72, 8, 59], [13, 73, 8, 60, "Path"], [13, 77, 8, 64], [13, 78, 8, 65], [13, 79, 8, 66], [13, 80, 8, 67], [14, 4, 9, 2], [15, 4, 10, 2, "MakeFromSVGString"], [15, 21, 10, 19, "MakeFromSVGString"], [15, 22, 10, 20, "str"], [15, 25, 10, 23], [15, 27, 10, 25], [16, 6, 11, 4], [16, 12, 11, 10, "path"], [16, 16, 11, 14], [16, 19, 11, 17], [16, 23, 11, 21], [16, 24, 11, 22, "CanvasKit"], [16, 33, 11, 31], [16, 34, 11, 32, "Path"], [16, 38, 11, 36], [16, 39, 11, 37, "MakeFromSVGString"], [16, 56, 11, 54], [16, 57, 11, 55, "str"], [16, 60, 11, 58], [16, 61, 11, 59], [17, 6, 12, 4], [17, 10, 12, 8, "path"], [17, 14, 12, 12], [17, 19, 12, 17], [17, 23, 12, 21], [17, 25, 12, 23], [18, 8, 13, 6], [18, 15, 13, 13], [18, 19, 13, 17], [19, 6, 14, 4], [20, 6, 15, 4], [20, 13, 15, 11], [20, 17, 15, 15, "JsiSkPath"], [20, 37, 15, 24], [20, 38, 15, 25], [20, 42, 15, 29], [20, 43, 15, 30, "CanvasKit"], [20, 52, 15, 39], [20, 54, 15, 41, "path"], [20, 58, 15, 45], [20, 59, 15, 46], [21, 4, 16, 2], [22, 4, 17, 2, "MakeFromOp"], [22, 14, 17, 12, "MakeFromOp"], [22, 15, 17, 13, "one"], [22, 18, 17, 16], [22, 20, 17, 18, "two"], [22, 23, 17, 21], [22, 25, 17, 23, "op"], [22, 27, 17, 25], [22, 29, 17, 27], [23, 6, 18, 4], [23, 12, 18, 10, "path"], [23, 16, 18, 14], [23, 19, 18, 17], [23, 23, 18, 21], [23, 24, 18, 22, "CanvasKit"], [23, 33, 18, 31], [23, 34, 18, 32, "Path"], [23, 38, 18, 36], [23, 39, 18, 37, "MakeFromOp"], [23, 49, 18, 47], [23, 50, 18, 48, "JsiSkPath"], [23, 70, 18, 57], [23, 71, 18, 58, "fromValue"], [23, 80, 18, 67], [23, 81, 18, 68, "one"], [23, 84, 18, 71], [23, 85, 18, 72], [23, 87, 18, 74, "JsiSkPath"], [23, 107, 18, 83], [23, 108, 18, 84, "fromValue"], [23, 117, 18, 93], [23, 118, 18, 94, "two"], [23, 121, 18, 97], [23, 122, 18, 98], [23, 124, 18, 100], [23, 128, 18, 100, "getEnum"], [23, 141, 18, 107], [23, 143, 18, 108], [23, 147, 18, 112], [23, 148, 18, 113, "CanvasKit"], [23, 157, 18, 122], [23, 159, 18, 124], [23, 167, 18, 132], [23, 169, 18, 134, "op"], [23, 171, 18, 136], [23, 172, 18, 137], [23, 173, 18, 138], [24, 6, 19, 4], [24, 10, 19, 8, "path"], [24, 14, 19, 12], [24, 19, 19, 17], [24, 23, 19, 21], [24, 25, 19, 23], [25, 8, 20, 6], [25, 15, 20, 13], [25, 19, 20, 17], [26, 6, 21, 4], [27, 6, 22, 4], [27, 13, 22, 11], [27, 17, 22, 15, "JsiSkPath"], [27, 37, 22, 24], [27, 38, 22, 25], [27, 42, 22, 29], [27, 43, 22, 30, "CanvasKit"], [27, 52, 22, 39], [27, 54, 22, 41, "path"], [27, 58, 22, 45], [27, 59, 22, 46], [28, 4, 23, 2], [29, 4, 24, 2, "MakeFromCmds"], [29, 16, 24, 14, "MakeFromCmds"], [29, 17, 24, 15, "cmds"], [29, 21, 24, 19], [29, 23, 24, 21], [30, 6, 25, 4], [30, 12, 25, 10, "path"], [30, 16, 25, 14], [30, 19, 25, 17], [30, 23, 25, 21], [30, 24, 25, 22, "CanvasKit"], [30, 33, 25, 31], [30, 34, 25, 32, "Path"], [30, 38, 25, 36], [30, 39, 25, 37, "MakeFromCmds"], [30, 51, 25, 49], [30, 52, 25, 50, "cmds"], [30, 56, 25, 54], [30, 57, 25, 55, "flat"], [30, 61, 25, 59], [30, 62, 25, 60], [30, 63, 25, 61], [30, 64, 25, 62], [31, 6, 26, 4], [31, 10, 26, 8, "path"], [31, 14, 26, 12], [31, 19, 26, 17], [31, 23, 26, 21], [31, 25, 26, 23], [32, 8, 27, 6], [32, 15, 27, 13], [32, 19, 27, 17], [33, 6, 28, 4], [34, 6, 29, 4], [34, 13, 29, 11], [34, 17, 29, 15, "JsiSkPath"], [34, 37, 29, 24], [34, 38, 29, 25], [34, 42, 29, 29], [34, 43, 29, 30, "CanvasKit"], [34, 52, 29, 39], [34, 54, 29, 41, "path"], [34, 58, 29, 45], [34, 59, 29, 46], [35, 4, 30, 2], [36, 4, 31, 2, "MakeFromText"], [36, 16, 31, 14, "MakeFromText"], [36, 17, 31, 15, "_text"], [36, 22, 31, 20], [36, 24, 31, 22, "_x"], [36, 26, 31, 24], [36, 28, 31, 26, "_y"], [36, 30, 31, 28], [36, 32, 31, 30, "_font"], [36, 37, 31, 35], [36, 39, 31, 37], [37, 6, 32, 4], [37, 13, 32, 11], [37, 17, 32, 11, "throwNotImplementedOnRNWeb"], [37, 49, 32, 37], [37, 51, 32, 38], [37, 52, 32, 39], [38, 4, 33, 2], [39, 2, 34, 0], [40, 2, 34, 1, "exports"], [40, 9, 34, 1], [40, 10, 34, 1, "JsiSkPathFactory"], [40, 26, 34, 1], [40, 29, 34, 1, "JsiSkPathFactory"], [40, 45, 34, 1], [41, 0, 34, 1], [41, 3]], "functionMap": {"names": ["<global>", "JsiSkPathFactory", "constructor", "Make", "MakeFromSVGString", "MakeFromOp", "MakeFromCmds", "MakeFromText"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFE;EGC;GHM;EIC;GJM;EKC;GLM;EMC;GNE;CDC"}}, "type": "js/module"}]}