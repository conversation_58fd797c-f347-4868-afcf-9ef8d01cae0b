{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/TensorFlowFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 62, "index": 836}}], "key": "mMY6APx5x9yizp8W6Mq/zEdxj+I=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 838}, "end": {"line": 27, "column": 42, "index": 880}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 943}, "end": {"line": 29, "column": 61, "index": 1004}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _TensorFlowFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/TensorFlowFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {\n          original: {\n            x,\n            y,\n            width,\n            height\n          },\n          canvas: {\n            width: canvasWidth,\n            height: canvasHeight\n          },\n          clamped: {\n            x: clampedX,\n            y: clampedY,\n            width: clampedWidth,\n            height: clampedHeight\n          }\n        });\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: About to call processImageWithFaceBlur with:', photo.uri);\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: Function exists?', typeof processImageWithFaceBlur);\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      console.log('[EchoCameraWeb] 🚀 ENTRY: Starting face blur processing system...');\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {\n              bbox,\n              imageSize: {\n                width: img.width,\n                height: img.height\n              }\n            });\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {\n              faceX,\n              faceY,\n              faceWidth,\n              faceHeight,\n              isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0\n            });\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] 🚨 CRITICAL ERROR in processImageWithFaceBlur:', error);\n        console.error('[EchoCameraWeb] 🚨 Error stack:', error.stack);\n        console.error('[EchoCameraWeb] 🚨 Error details:', {\n          name: error.name,\n          message: error.message,\n          photoUri\n        });\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 908,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 942,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TensorFlowFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 15,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.2,\n                width: viewSize.width,\n                height: viewSize.height * 0.6,\n                borderRadius: 20,\n                opacity: 0.4 // Reduced opacity since we have dynamic detection\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 10,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2,\n                opacity: 0.3\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 987,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 10,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2,\n                opacity: 0.3\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 10,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2,\n                opacity: 0.3\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1013,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 976,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1034,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1071,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1052,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1086,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1088,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1095,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1094,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1085,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1084,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1079,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1122,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1124,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1129,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1135,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1120,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1115,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 939,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1743, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 26, 0], [20, 6, 26, 0, "_TensorFlowFaceCanvas"], [20, 27, 26, 0], [20, 30, 26, 0, "_interopRequireDefault"], [20, 52, 26, 0], [20, 53, 26, 0, "require"], [20, 60, 26, 0], [20, 61, 26, 0, "_dependencyMap"], [20, 75, 26, 0], [21, 2, 27, 0], [21, 6, 27, 0, "_useUpload"], [21, 16, 27, 0], [21, 19, 27, 0, "_interopRequireDefault"], [21, 41, 27, 0], [21, 42, 27, 0, "require"], [21, 49, 27, 0], [21, 50, 27, 0, "_dependencyMap"], [21, 64, 27, 0], [22, 2, 29, 0], [22, 6, 29, 0, "_cameraChallenge"], [22, 22, 29, 0], [22, 25, 29, 0, "require"], [22, 32, 29, 0], [22, 33, 29, 0, "_dependencyMap"], [22, 47, 29, 0], [23, 2, 29, 61], [23, 6, 29, 61, "_Platform"], [23, 15, 29, 61], [23, 18, 29, 61, "_interopRequireDefault"], [23, 40, 29, 61], [23, 41, 29, 61, "require"], [23, 48, 29, 61], [23, 49, 29, 61, "_dependencyMap"], [23, 63, 29, 61], [24, 2, 29, 61], [24, 6, 29, 61, "_jsxDevRuntime"], [24, 20, 29, 61], [24, 23, 29, 61, "require"], [24, 30, 29, 61], [24, 31, 29, 61, "_dependencyMap"], [24, 45, 29, 61], [25, 2, 29, 61], [25, 6, 29, 61, "_jsxFileName"], [25, 18, 29, 61], [26, 4, 29, 61, "_s"], [26, 6, 29, 61], [26, 9, 29, 61, "$RefreshSig$"], [26, 21, 29, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 31, 0], [38, 8, 31, 6], [39, 4, 31, 8, "width"], [39, 9, 31, 13], [39, 11, 31, 15, "screenWidth"], [39, 22, 31, 26], [40, 4, 31, 28, "height"], [40, 10, 31, 34], [40, 12, 31, 36, "screenHeight"], [41, 2, 31, 49], [41, 3, 31, 50], [41, 6, 31, 53, "Dimensions"], [41, 25, 31, 63], [41, 26, 31, 64, "get"], [41, 29, 31, 67], [41, 30, 31, 68], [41, 38, 31, 76], [41, 39, 31, 77], [42, 2, 32, 0], [42, 8, 32, 6, "API_BASE_URL"], [42, 20, 32, 18], [42, 23, 32, 21], [42, 24, 33, 2, "_env2"], [42, 29, 33, 2], [42, 30, 33, 2, "env"], [42, 33, 33, 2], [42, 34, 33, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 33, 2], [42, 58, 33, 2, "_env2"], [42, 63, 33, 2], [42, 64, 33, 2, "env"], [42, 67, 33, 2], [42, 68, 33, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 34, 40], [42, 98, 34, 40, "_env2"], [42, 103, 34, 40], [42, 104, 34, 40, "env"], [42, 107, 34, 40], [42, 108, 34, 40, "EXPO_PUBLIC_HOST"], [42, 124, 35, 30], [42, 128, 36, 2], [42, 130, 36, 4], [42, 132, 37, 2, "replace"], [42, 139, 37, 9], [42, 140, 37, 10], [42, 145, 37, 15], [42, 147, 37, 17], [42, 149, 37, 19], [42, 150, 37, 20], [44, 2, 50, 0], [46, 2, 52, 15], [46, 11, 52, 24, "EchoCameraWeb"], [46, 24, 52, 37, "EchoCameraWeb"], [46, 25, 52, 38], [47, 4, 53, 2, "userId"], [47, 10, 53, 8], [48, 4, 54, 2, "requestId"], [48, 13, 54, 11], [49, 4, 55, 2, "onComplete"], [49, 14, 55, 12], [50, 4, 56, 2, "onCancel"], [51, 2, 57, 20], [51, 3, 57, 21], [51, 5, 57, 23], [52, 4, 57, 23, "_s"], [52, 6, 57, 23], [53, 4, 58, 2], [53, 10, 58, 8, "cameraRef"], [53, 19, 58, 17], [53, 22, 58, 20], [53, 26, 58, 20, "useRef"], [53, 39, 58, 26], [53, 41, 58, 39], [53, 45, 58, 43], [53, 46, 58, 44], [54, 4, 59, 2], [54, 10, 59, 8], [54, 11, 59, 9, "permission"], [54, 21, 59, 19], [54, 23, 59, 21, "requestPermission"], [54, 40, 59, 38], [54, 41, 59, 39], [54, 44, 59, 42], [54, 48, 59, 42, "useCameraPermissions"], [54, 80, 59, 62], [54, 82, 59, 63], [54, 83, 59, 64], [56, 4, 61, 2], [57, 4, 62, 2], [57, 10, 62, 8], [57, 11, 62, 9, "processingState"], [57, 26, 62, 24], [57, 28, 62, 26, "setProcessingState"], [57, 46, 62, 44], [57, 47, 62, 45], [57, 50, 62, 48], [57, 54, 62, 48, "useState"], [57, 69, 62, 56], [57, 71, 62, 74], [57, 77, 62, 80], [57, 78, 62, 81], [58, 4, 63, 2], [58, 10, 63, 8], [58, 11, 63, 9, "challengeCode"], [58, 24, 63, 22], [58, 26, 63, 24, "setChallengeCode"], [58, 42, 63, 40], [58, 43, 63, 41], [58, 46, 63, 44], [58, 50, 63, 44, "useState"], [58, 65, 63, 52], [58, 67, 63, 68], [58, 71, 63, 72], [58, 72, 63, 73], [59, 4, 64, 2], [59, 10, 64, 8], [59, 11, 64, 9, "processingProgress"], [59, 29, 64, 27], [59, 31, 64, 29, "setProcessingProgress"], [59, 52, 64, 50], [59, 53, 64, 51], [59, 56, 64, 54], [59, 60, 64, 54, "useState"], [59, 75, 64, 62], [59, 77, 64, 63], [59, 78, 64, 64], [59, 79, 64, 65], [60, 4, 65, 2], [60, 10, 65, 8], [60, 11, 65, 9, "errorMessage"], [60, 23, 65, 21], [60, 25, 65, 23, "setErrorMessage"], [60, 40, 65, 38], [60, 41, 65, 39], [60, 44, 65, 42], [60, 48, 65, 42, "useState"], [60, 63, 65, 50], [60, 65, 65, 66], [60, 69, 65, 70], [60, 70, 65, 71], [61, 4, 66, 2], [61, 10, 66, 8], [61, 11, 66, 9, "capturedPhoto"], [61, 24, 66, 22], [61, 26, 66, 24, "setCapturedPhoto"], [61, 42, 66, 40], [61, 43, 66, 41], [61, 46, 66, 44], [61, 50, 66, 44, "useState"], [61, 65, 66, 52], [61, 67, 66, 68], [61, 71, 66, 72], [61, 72, 66, 73], [62, 4, 67, 2], [63, 4, 68, 2], [63, 10, 68, 8], [63, 11, 68, 9, "previewBlurEnabled"], [63, 29, 68, 27], [63, 31, 68, 29, "setPreviewBlurEnabled"], [63, 52, 68, 50], [63, 53, 68, 51], [63, 56, 68, 54], [63, 60, 68, 54, "useState"], [63, 75, 68, 62], [63, 77, 68, 63], [63, 81, 68, 67], [63, 82, 68, 68], [64, 4, 69, 2], [64, 10, 69, 8], [64, 11, 69, 9, "viewSize"], [64, 19, 69, 17], [64, 21, 69, 19, "setViewSize"], [64, 32, 69, 30], [64, 33, 69, 31], [64, 36, 69, 34], [64, 40, 69, 34, "useState"], [64, 55, 69, 42], [64, 57, 69, 43], [65, 6, 69, 45, "width"], [65, 11, 69, 50], [65, 13, 69, 52], [65, 14, 69, 53], [66, 6, 69, 55, "height"], [66, 12, 69, 61], [66, 14, 69, 63], [67, 4, 69, 65], [67, 5, 69, 66], [67, 6, 69, 67], [68, 4, 70, 2], [69, 4, 71, 2], [69, 10, 71, 8], [69, 11, 71, 9, "isCameraReady"], [69, 24, 71, 22], [69, 26, 71, 24, "setIsCameraReady"], [69, 42, 71, 40], [69, 43, 71, 41], [69, 46, 71, 44], [69, 50, 71, 44, "useState"], [69, 65, 71, 52], [69, 67, 71, 53], [69, 72, 71, 58], [69, 73, 71, 59], [70, 4, 73, 2], [70, 10, 73, 8], [70, 11, 73, 9, "upload"], [70, 17, 73, 15], [70, 18, 73, 16], [70, 21, 73, 19], [70, 25, 73, 19, "useUpload"], [70, 43, 73, 28], [70, 45, 73, 29], [70, 46, 73, 30], [71, 4, 74, 2], [72, 4, 75, 2], [72, 8, 75, 2, "useEffect"], [72, 24, 75, 11], [72, 26, 75, 12], [72, 32, 75, 18], [73, 6, 76, 4], [73, 12, 76, 10, "controller"], [73, 22, 76, 20], [73, 25, 76, 23], [73, 29, 76, 27, "AbortController"], [73, 44, 76, 42], [73, 45, 76, 43], [73, 46, 76, 44], [74, 6, 78, 4], [74, 7, 78, 5], [74, 19, 78, 17], [75, 8, 79, 6], [75, 12, 79, 10], [76, 10, 80, 8], [76, 16, 80, 14, "code"], [76, 20, 80, 18], [76, 23, 80, 21], [76, 29, 80, 27], [76, 33, 80, 27, "fetchChallengeCode"], [76, 68, 80, 45], [76, 70, 80, 46], [77, 12, 80, 48, "userId"], [77, 18, 80, 54], [78, 12, 80, 56, "requestId"], [78, 21, 80, 65], [79, 12, 80, 67, "signal"], [79, 18, 80, 73], [79, 20, 80, 75, "controller"], [79, 30, 80, 85], [79, 31, 80, 86, "signal"], [80, 10, 80, 93], [80, 11, 80, 94], [80, 12, 80, 95], [81, 10, 81, 8, "setChallengeCode"], [81, 26, 81, 24], [81, 27, 81, 25, "code"], [81, 31, 81, 29], [81, 32, 81, 30], [82, 8, 82, 6], [82, 9, 82, 7], [82, 10, 82, 8], [82, 17, 82, 15, "e"], [82, 18, 82, 16], [82, 20, 82, 18], [83, 10, 83, 8, "console"], [83, 17, 83, 15], [83, 18, 83, 16, "warn"], [83, 22, 83, 20], [83, 23, 83, 21], [83, 69, 83, 67], [83, 71, 83, 69, "e"], [83, 72, 83, 70], [83, 73, 83, 71], [84, 10, 84, 8, "setChallengeCode"], [84, 26, 84, 24], [84, 27, 84, 25], [84, 35, 84, 33, "Date"], [84, 39, 84, 37], [84, 40, 84, 38, "now"], [84, 43, 84, 41], [84, 44, 84, 42], [84, 45, 84, 43], [84, 46, 84, 44, "toString"], [84, 54, 84, 52], [84, 55, 84, 53], [84, 57, 84, 55], [84, 58, 84, 56], [84, 59, 84, 57, "toUpperCase"], [84, 70, 84, 68], [84, 71, 84, 69], [84, 72, 84, 70], [84, 74, 84, 72], [84, 75, 84, 73], [85, 8, 85, 6], [86, 6, 86, 4], [86, 7, 86, 5], [86, 9, 86, 7], [86, 10, 86, 8], [87, 6, 88, 4], [87, 13, 88, 11], [87, 19, 88, 17, "controller"], [87, 29, 88, 27], [87, 30, 88, 28, "abort"], [87, 35, 88, 33], [87, 36, 88, 34], [87, 37, 88, 35], [88, 4, 89, 2], [88, 5, 89, 3], [88, 7, 89, 5], [88, 8, 89, 6, "userId"], [88, 14, 89, 12], [88, 16, 89, 14, "requestId"], [88, 25, 89, 23], [88, 26, 89, 24], [88, 27, 89, 25], [90, 4, 91, 2], [91, 4, 92, 2], [91, 10, 92, 8, "loadTensorFlowFaceDetection"], [91, 37, 92, 35], [91, 40, 92, 38], [91, 46, 92, 38, "loadTensorFlowFaceDetection"], [91, 47, 92, 38], [91, 52, 92, 50], [92, 6, 93, 4, "console"], [92, 13, 93, 11], [92, 14, 93, 12, "log"], [92, 17, 93, 15], [92, 18, 93, 16], [92, 78, 93, 76], [92, 79, 93, 77], [94, 6, 95, 4], [95, 6, 96, 4], [95, 10, 96, 8], [95, 11, 96, 10, "window"], [95, 17, 96, 16], [95, 18, 96, 25, "tf"], [95, 20, 96, 27], [95, 22, 96, 29], [96, 8, 97, 6], [96, 14, 97, 12], [96, 18, 97, 16, "Promise"], [96, 25, 97, 23], [96, 26, 97, 24], [96, 27, 97, 25, "resolve"], [96, 34, 97, 32], [96, 36, 97, 34, "reject"], [96, 42, 97, 40], [96, 47, 97, 45], [97, 10, 98, 8], [97, 16, 98, 14, "script"], [97, 22, 98, 20], [97, 25, 98, 23, "document"], [97, 33, 98, 31], [97, 34, 98, 32, "createElement"], [97, 47, 98, 45], [97, 48, 98, 46], [97, 56, 98, 54], [97, 57, 98, 55], [98, 10, 99, 8, "script"], [98, 16, 99, 14], [98, 17, 99, 15, "src"], [98, 20, 99, 18], [98, 23, 99, 21], [98, 92, 99, 90], [99, 10, 100, 8, "script"], [99, 16, 100, 14], [99, 17, 100, 15, "onload"], [99, 23, 100, 21], [99, 26, 100, 24, "resolve"], [99, 33, 100, 31], [100, 10, 101, 8, "script"], [100, 16, 101, 14], [100, 17, 101, 15, "onerror"], [100, 24, 101, 22], [100, 27, 101, 25, "reject"], [100, 33, 101, 31], [101, 10, 102, 8, "document"], [101, 18, 102, 16], [101, 19, 102, 17, "head"], [101, 23, 102, 21], [101, 24, 102, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 102, 33], [101, 36, 102, 34, "script"], [101, 42, 102, 40], [101, 43, 102, 41], [102, 8, 103, 6], [102, 9, 103, 7], [102, 10, 103, 8], [103, 6, 104, 4], [105, 6, 106, 4], [106, 6, 107, 4], [106, 10, 107, 8], [106, 11, 107, 10, "window"], [106, 17, 107, 16], [106, 18, 107, 25, "blazeface"], [106, 27, 107, 34], [106, 29, 107, 36], [107, 8, 108, 6], [107, 14, 108, 12], [107, 18, 108, 16, "Promise"], [107, 25, 108, 23], [107, 26, 108, 24], [107, 27, 108, 25, "resolve"], [107, 34, 108, 32], [107, 36, 108, 34, "reject"], [107, 42, 108, 40], [107, 47, 108, 45], [108, 10, 109, 8], [108, 16, 109, 14, "script"], [108, 22, 109, 20], [108, 25, 109, 23, "document"], [108, 33, 109, 31], [108, 34, 109, 32, "createElement"], [108, 47, 109, 45], [108, 48, 109, 46], [108, 56, 109, 54], [108, 57, 109, 55], [109, 10, 110, 8, "script"], [109, 16, 110, 14], [109, 17, 110, 15, "src"], [109, 20, 110, 18], [109, 23, 110, 21], [109, 106, 110, 104], [110, 10, 111, 8, "script"], [110, 16, 111, 14], [110, 17, 111, 15, "onload"], [110, 23, 111, 21], [110, 26, 111, 24, "resolve"], [110, 33, 111, 31], [111, 10, 112, 8, "script"], [111, 16, 112, 14], [111, 17, 112, 15, "onerror"], [111, 24, 112, 22], [111, 27, 112, 25, "reject"], [111, 33, 112, 31], [112, 10, 113, 8, "document"], [112, 18, 113, 16], [112, 19, 113, 17, "head"], [112, 23, 113, 21], [112, 24, 113, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 113, 33], [112, 36, 113, 34, "script"], [112, 42, 113, 40], [112, 43, 113, 41], [113, 8, 114, 6], [113, 9, 114, 7], [113, 10, 114, 8], [114, 6, 115, 4], [115, 6, 117, 4, "console"], [115, 13, 117, 11], [115, 14, 117, 12, "log"], [115, 17, 117, 15], [115, 18, 117, 16], [115, 72, 117, 70], [115, 73, 117, 71], [116, 4, 118, 2], [116, 5, 118, 3], [117, 4, 120, 2], [117, 10, 120, 8, "detectFacesWithTensorFlow"], [117, 35, 120, 33], [117, 38, 120, 36], [117, 44, 120, 43, "img"], [117, 47, 120, 64], [117, 51, 120, 69], [118, 6, 121, 4], [118, 10, 121, 8], [119, 8, 122, 6], [119, 14, 122, 12, "blazeface"], [119, 23, 122, 21], [119, 26, 122, 25, "window"], [119, 32, 122, 31], [119, 33, 122, 40, "blazeface"], [119, 42, 122, 49], [120, 8, 123, 6], [120, 14, 123, 12, "tf"], [120, 16, 123, 14], [120, 19, 123, 18, "window"], [120, 25, 123, 24], [120, 26, 123, 33, "tf"], [120, 28, 123, 35], [121, 8, 125, 6, "console"], [121, 15, 125, 13], [121, 16, 125, 14, "log"], [121, 19, 125, 17], [121, 20, 125, 18], [121, 67, 125, 65], [121, 68, 125, 66], [122, 8, 126, 6], [122, 14, 126, 12, "model"], [122, 19, 126, 17], [122, 22, 126, 20], [122, 28, 126, 26, "blazeface"], [122, 37, 126, 35], [122, 38, 126, 36, "load"], [122, 42, 126, 40], [122, 43, 126, 41], [122, 44, 126, 42], [123, 8, 127, 6, "console"], [123, 15, 127, 13], [123, 16, 127, 14, "log"], [123, 19, 127, 17], [123, 20, 127, 18], [123, 82, 127, 80], [123, 83, 127, 81], [125, 8, 129, 6], [126, 8, 130, 6], [126, 14, 130, 12, "tensor"], [126, 20, 130, 18], [126, 23, 130, 21, "tf"], [126, 25, 130, 23], [126, 26, 130, 24, "browser"], [126, 33, 130, 31], [126, 34, 130, 32, "fromPixels"], [126, 44, 130, 42], [126, 45, 130, 43, "img"], [126, 48, 130, 46], [126, 49, 130, 47], [128, 8, 132, 6], [129, 8, 133, 6], [129, 14, 133, 12, "predictions"], [129, 25, 133, 23], [129, 28, 133, 26], [129, 34, 133, 32, "model"], [129, 39, 133, 37], [129, 40, 133, 38, "estimateFaces"], [129, 53, 133, 51], [129, 54, 133, 52, "tensor"], [129, 60, 133, 58], [129, 62, 133, 60], [129, 67, 133, 65], [129, 69, 133, 67], [129, 72, 133, 70], [129, 73, 133, 71], [129, 74, 133, 72], [129, 75, 133, 73], [131, 8, 135, 6], [132, 8, 136, 6, "tensor"], [132, 14, 136, 12], [132, 15, 136, 13, "dispose"], [132, 22, 136, 20], [132, 23, 136, 21], [132, 24, 136, 22], [133, 8, 138, 6, "console"], [133, 15, 138, 13], [133, 16, 138, 14, "log"], [133, 19, 138, 17], [133, 20, 138, 18], [133, 61, 138, 59, "predictions"], [133, 72, 138, 70], [133, 73, 138, 71, "length"], [133, 79, 138, 77], [133, 87, 138, 85], [133, 88, 138, 86], [135, 8, 140, 6], [136, 8, 141, 6], [136, 14, 141, 12, "faces"], [136, 19, 141, 17], [136, 22, 141, 20, "predictions"], [136, 33, 141, 31], [136, 34, 141, 32, "map"], [136, 37, 141, 35], [136, 38, 141, 36], [136, 39, 141, 37, "prediction"], [136, 49, 141, 52], [136, 51, 141, 54, "index"], [136, 56, 141, 67], [136, 61, 141, 72], [137, 10, 142, 8], [137, 16, 142, 14], [137, 17, 142, 15, "x"], [137, 18, 142, 16], [137, 20, 142, 18, "y"], [137, 21, 142, 19], [137, 22, 142, 20], [137, 25, 142, 23, "prediction"], [137, 35, 142, 33], [137, 36, 142, 34, "topLeft"], [137, 43, 142, 41], [138, 10, 143, 8], [138, 16, 143, 14], [138, 17, 143, 15, "x2"], [138, 19, 143, 17], [138, 21, 143, 19, "y2"], [138, 23, 143, 21], [138, 24, 143, 22], [138, 27, 143, 25, "prediction"], [138, 37, 143, 35], [138, 38, 143, 36, "bottomRight"], [138, 49, 143, 47], [139, 10, 144, 8], [139, 16, 144, 14, "width"], [139, 21, 144, 19], [139, 24, 144, 22, "x2"], [139, 26, 144, 24], [139, 29, 144, 27, "x"], [139, 30, 144, 28], [140, 10, 145, 8], [140, 16, 145, 14, "height"], [140, 22, 145, 20], [140, 25, 145, 23, "y2"], [140, 27, 145, 25], [140, 30, 145, 28, "y"], [140, 31, 145, 29], [141, 10, 147, 8, "console"], [141, 17, 147, 15], [141, 18, 147, 16, "log"], [141, 21, 147, 19], [141, 22, 147, 20], [141, 49, 147, 47, "index"], [141, 54, 147, 52], [141, 57, 147, 55], [141, 58, 147, 56], [141, 61, 147, 59], [141, 63, 147, 61], [142, 12, 148, 10, "topLeft"], [142, 19, 148, 17], [142, 21, 148, 19], [142, 22, 148, 20, "x"], [142, 23, 148, 21], [142, 25, 148, 23, "y"], [142, 26, 148, 24], [142, 27, 148, 25], [143, 12, 149, 10, "bottomRight"], [143, 23, 149, 21], [143, 25, 149, 23], [143, 26, 149, 24, "x2"], [143, 28, 149, 26], [143, 30, 149, 28, "y2"], [143, 32, 149, 30], [143, 33, 149, 31], [144, 12, 150, 10, "size"], [144, 16, 150, 14], [144, 18, 150, 16], [144, 19, 150, 17, "width"], [144, 24, 150, 22], [144, 26, 150, 24, "height"], [144, 32, 150, 30], [145, 10, 151, 8], [145, 11, 151, 9], [145, 12, 151, 10], [146, 10, 153, 8], [146, 17, 153, 15], [147, 12, 154, 10, "boundingBox"], [147, 23, 154, 21], [147, 25, 154, 23], [148, 14, 155, 12, "xCenter"], [148, 21, 155, 19], [148, 23, 155, 21], [148, 24, 155, 22, "x"], [148, 25, 155, 23], [148, 28, 155, 26, "width"], [148, 33, 155, 31], [148, 36, 155, 34], [148, 37, 155, 35], [148, 41, 155, 39, "img"], [148, 44, 155, 42], [148, 45, 155, 43, "width"], [148, 50, 155, 48], [149, 14, 156, 12, "yCenter"], [149, 21, 156, 19], [149, 23, 156, 21], [149, 24, 156, 22, "y"], [149, 25, 156, 23], [149, 28, 156, 26, "height"], [149, 34, 156, 32], [149, 37, 156, 35], [149, 38, 156, 36], [149, 42, 156, 40, "img"], [149, 45, 156, 43], [149, 46, 156, 44, "height"], [149, 52, 156, 50], [150, 14, 157, 12, "width"], [150, 19, 157, 17], [150, 21, 157, 19, "width"], [150, 26, 157, 24], [150, 29, 157, 27, "img"], [150, 32, 157, 30], [150, 33, 157, 31, "width"], [150, 38, 157, 36], [151, 14, 158, 12, "height"], [151, 20, 158, 18], [151, 22, 158, 20, "height"], [151, 28, 158, 26], [151, 31, 158, 29, "img"], [151, 34, 158, 32], [151, 35, 158, 33, "height"], [152, 12, 159, 10], [153, 10, 160, 8], [153, 11, 160, 9], [154, 8, 161, 6], [154, 9, 161, 7], [154, 10, 161, 8], [155, 8, 163, 6], [155, 15, 163, 13, "faces"], [155, 20, 163, 18], [156, 6, 164, 4], [156, 7, 164, 5], [156, 8, 164, 6], [156, 15, 164, 13, "error"], [156, 20, 164, 18], [156, 22, 164, 20], [157, 8, 165, 6, "console"], [157, 15, 165, 13], [157, 16, 165, 14, "error"], [157, 21, 165, 19], [157, 22, 165, 20], [157, 75, 165, 73], [157, 77, 165, 75, "error"], [157, 82, 165, 80], [157, 83, 165, 81], [158, 8, 166, 6], [158, 15, 166, 13], [158, 17, 166, 15], [159, 6, 167, 4], [160, 4, 168, 2], [160, 5, 168, 3], [161, 4, 170, 2], [161, 10, 170, 8, "detectFacesHeuristic"], [161, 30, 170, 28], [161, 33, 170, 31, "detectFacesHeuristic"], [161, 34, 170, 32, "img"], [161, 37, 170, 53], [161, 39, 170, 55, "ctx"], [161, 42, 170, 84], [161, 47, 170, 89], [162, 6, 171, 4, "console"], [162, 13, 171, 11], [162, 14, 171, 12, "log"], [162, 17, 171, 15], [162, 18, 171, 16], [162, 83, 171, 81], [162, 84, 171, 82], [164, 6, 173, 4], [165, 6, 174, 4], [165, 12, 174, 10, "imageData"], [165, 21, 174, 19], [165, 24, 174, 22, "ctx"], [165, 27, 174, 25], [165, 28, 174, 26, "getImageData"], [165, 40, 174, 38], [165, 41, 174, 39], [165, 42, 174, 40], [165, 44, 174, 42], [165, 45, 174, 43], [165, 47, 174, 45, "img"], [165, 50, 174, 48], [165, 51, 174, 49, "width"], [165, 56, 174, 54], [165, 58, 174, 56, "img"], [165, 61, 174, 59], [165, 62, 174, 60, "height"], [165, 68, 174, 66], [165, 69, 174, 67], [166, 6, 175, 4], [166, 12, 175, 10, "data"], [166, 16, 175, 14], [166, 19, 175, 17, "imageData"], [166, 28, 175, 26], [166, 29, 175, 27, "data"], [166, 33, 175, 31], [168, 6, 177, 4], [169, 6, 178, 4], [169, 12, 178, 10, "faces"], [169, 17, 178, 15], [169, 20, 178, 18], [169, 22, 178, 20], [170, 6, 179, 4], [170, 12, 179, 10, "blockSize"], [170, 21, 179, 19], [170, 24, 179, 22, "Math"], [170, 28, 179, 26], [170, 29, 179, 27, "min"], [170, 32, 179, 30], [170, 33, 179, 31, "img"], [170, 36, 179, 34], [170, 37, 179, 35, "width"], [170, 42, 179, 40], [170, 44, 179, 42, "img"], [170, 47, 179, 45], [170, 48, 179, 46, "height"], [170, 54, 179, 52], [170, 55, 179, 53], [170, 58, 179, 56], [170, 60, 179, 58], [170, 61, 179, 59], [170, 62, 179, 60], [172, 6, 181, 4, "console"], [172, 13, 181, 11], [172, 14, 181, 12, "log"], [172, 17, 181, 15], [172, 18, 181, 16], [172, 59, 181, 57, "blockSize"], [172, 68, 181, 66], [172, 82, 181, 80], [172, 83, 181, 81], [173, 6, 183, 4], [173, 11, 183, 9], [173, 15, 183, 13, "y"], [173, 16, 183, 14], [173, 19, 183, 17], [173, 20, 183, 18], [173, 22, 183, 20, "y"], [173, 23, 183, 21], [173, 26, 183, 24, "img"], [173, 29, 183, 27], [173, 30, 183, 28, "height"], [173, 36, 183, 34], [173, 39, 183, 37, "blockSize"], [173, 48, 183, 46], [173, 50, 183, 48, "y"], [173, 51, 183, 49], [173, 55, 183, 53, "blockSize"], [173, 64, 183, 62], [173, 67, 183, 65], [173, 68, 183, 66], [173, 70, 183, 68], [174, 8, 183, 70], [175, 8, 184, 6], [175, 13, 184, 11], [175, 17, 184, 15, "x"], [175, 18, 184, 16], [175, 21, 184, 19], [175, 22, 184, 20], [175, 24, 184, 22, "x"], [175, 25, 184, 23], [175, 28, 184, 26, "img"], [175, 31, 184, 29], [175, 32, 184, 30, "width"], [175, 37, 184, 35], [175, 40, 184, 38, "blockSize"], [175, 49, 184, 47], [175, 51, 184, 49, "x"], [175, 52, 184, 50], [175, 56, 184, 54, "blockSize"], [175, 65, 184, 63], [175, 68, 184, 66], [175, 69, 184, 67], [175, 71, 184, 69], [176, 10, 185, 8], [176, 16, 185, 14, "analysis"], [176, 24, 185, 22], [176, 27, 185, 25, "analyzeRegionForFace"], [176, 47, 185, 45], [176, 48, 185, 46, "data"], [176, 52, 185, 50], [176, 54, 185, 52, "x"], [176, 55, 185, 53], [176, 57, 185, 55, "y"], [176, 58, 185, 56], [176, 60, 185, 58, "blockSize"], [176, 69, 185, 67], [176, 71, 185, 69, "img"], [176, 74, 185, 72], [176, 75, 185, 73, "width"], [176, 80, 185, 78], [176, 82, 185, 80, "img"], [176, 85, 185, 83], [176, 86, 185, 84, "height"], [176, 92, 185, 90], [176, 93, 185, 91], [178, 10, 187, 8], [179, 10, 188, 8], [179, 14, 188, 12, "analysis"], [179, 22, 188, 20], [179, 23, 188, 21, "skinRatio"], [179, 32, 188, 30], [179, 35, 188, 33], [179, 39, 188, 37], [180, 10, 188, 42], [181, 10, 189, 12, "analysis"], [181, 18, 189, 20], [181, 19, 189, 21, "hasVariation"], [181, 31, 189, 33], [181, 35, 190, 12, "analysis"], [181, 43, 190, 20], [181, 44, 190, 21, "brightness"], [181, 54, 190, 31], [181, 57, 190, 34], [181, 61, 190, 38], [182, 10, 190, 43], [183, 10, 191, 12, "analysis"], [183, 18, 191, 20], [183, 19, 191, 21, "brightness"], [183, 29, 191, 31], [183, 32, 191, 34], [183, 35, 191, 37], [183, 37, 191, 39], [184, 12, 191, 43], [186, 12, 193, 10, "faces"], [186, 17, 193, 15], [186, 18, 193, 16, "push"], [186, 22, 193, 20], [186, 23, 193, 21], [187, 14, 194, 12, "boundingBox"], [187, 25, 194, 23], [187, 27, 194, 25], [188, 16, 195, 14, "xCenter"], [188, 23, 195, 21], [188, 25, 195, 23], [188, 26, 195, 24, "x"], [188, 27, 195, 25], [188, 30, 195, 28, "blockSize"], [188, 39, 195, 37], [188, 42, 195, 40], [188, 43, 195, 41], [188, 47, 195, 45, "img"], [188, 50, 195, 48], [188, 51, 195, 49, "width"], [188, 56, 195, 54], [189, 16, 196, 14, "yCenter"], [189, 23, 196, 21], [189, 25, 196, 23], [189, 26, 196, 24, "y"], [189, 27, 196, 25], [189, 30, 196, 28, "blockSize"], [189, 39, 196, 37], [189, 42, 196, 40], [189, 43, 196, 41], [189, 47, 196, 45, "img"], [189, 50, 196, 48], [189, 51, 196, 49, "height"], [189, 57, 196, 55], [190, 16, 197, 14, "width"], [190, 21, 197, 19], [190, 23, 197, 22, "blockSize"], [190, 32, 197, 31], [190, 35, 197, 34], [190, 38, 197, 37], [190, 41, 197, 41, "img"], [190, 44, 197, 44], [190, 45, 197, 45, "width"], [190, 50, 197, 50], [191, 16, 198, 14, "height"], [191, 22, 198, 20], [191, 24, 198, 23, "blockSize"], [191, 33, 198, 32], [191, 36, 198, 35], [191, 39, 198, 38], [191, 42, 198, 42, "img"], [191, 45, 198, 45], [191, 46, 198, 46, "height"], [192, 14, 199, 12], [192, 15, 199, 13], [193, 14, 200, 12, "confidence"], [193, 24, 200, 22], [193, 26, 200, 24, "analysis"], [193, 34, 200, 32], [193, 35, 200, 33, "skinRatio"], [193, 44, 200, 42], [193, 47, 200, 45, "analysis"], [193, 55, 200, 53], [193, 56, 200, 54, "variation"], [194, 12, 201, 10], [194, 13, 201, 11], [194, 14, 201, 12], [195, 12, 203, 10, "console"], [195, 19, 203, 17], [195, 20, 203, 18, "log"], [195, 23, 203, 21], [195, 24, 203, 22], [195, 71, 203, 69, "Math"], [195, 75, 203, 73], [195, 76, 203, 74, "round"], [195, 81, 203, 79], [195, 82, 203, 80, "x"], [195, 83, 203, 81], [195, 84, 203, 82], [195, 89, 203, 87, "Math"], [195, 93, 203, 91], [195, 94, 203, 92, "round"], [195, 99, 203, 97], [195, 100, 203, 98, "y"], [195, 101, 203, 99], [195, 102, 203, 100], [195, 115, 203, 113], [195, 116, 203, 114, "analysis"], [195, 124, 203, 122], [195, 125, 203, 123, "skinRatio"], [195, 134, 203, 132], [195, 137, 203, 135], [195, 140, 203, 138], [195, 142, 203, 140, "toFixed"], [195, 149, 203, 147], [195, 150, 203, 148], [195, 151, 203, 149], [195, 152, 203, 150], [195, 169, 203, 167, "analysis"], [195, 177, 203, 175], [195, 178, 203, 176, "variation"], [195, 187, 203, 185], [195, 188, 203, 186, "toFixed"], [195, 195, 203, 193], [195, 196, 203, 194], [195, 197, 203, 195], [195, 198, 203, 196], [195, 215, 203, 213, "analysis"], [195, 223, 203, 221], [195, 224, 203, 222, "brightness"], [195, 234, 203, 232], [195, 235, 203, 233, "toFixed"], [195, 242, 203, 240], [195, 243, 203, 241], [195, 244, 203, 242], [195, 245, 203, 243], [195, 247, 203, 245], [195, 248, 203, 246], [196, 10, 204, 8], [197, 8, 205, 6], [198, 6, 206, 4], [200, 6, 208, 4], [201, 6, 209, 4, "faces"], [201, 11, 209, 9], [201, 12, 209, 10, "sort"], [201, 16, 209, 14], [201, 17, 209, 15], [201, 18, 209, 16, "a"], [201, 19, 209, 17], [201, 21, 209, 19, "b"], [201, 22, 209, 20], [201, 27, 209, 25], [201, 28, 209, 26, "b"], [201, 29, 209, 27], [201, 30, 209, 28, "confidence"], [201, 40, 209, 38], [201, 44, 209, 42], [201, 45, 209, 43], [201, 50, 209, 48, "a"], [201, 51, 209, 49], [201, 52, 209, 50, "confidence"], [201, 62, 209, 60], [201, 66, 209, 64], [201, 67, 209, 65], [201, 68, 209, 66], [201, 69, 209, 67], [202, 6, 210, 4], [202, 12, 210, 10, "mergedFaces"], [202, 23, 210, 21], [202, 26, 210, 24, "mergeFaceDetections"], [202, 45, 210, 43], [202, 46, 210, 44, "faces"], [202, 51, 210, 49], [202, 52, 210, 50], [203, 6, 212, 4, "console"], [203, 13, 212, 11], [203, 14, 212, 12, "log"], [203, 17, 212, 15], [203, 18, 212, 16], [203, 61, 212, 59, "faces"], [203, 66, 212, 64], [203, 67, 212, 65, "length"], [203, 73, 212, 71], [203, 90, 212, 88, "mergedFaces"], [203, 101, 212, 99], [203, 102, 212, 100, "length"], [203, 108, 212, 106], [203, 123, 212, 121], [203, 124, 212, 122], [204, 6, 213, 4], [204, 13, 213, 11, "mergedFaces"], [204, 24, 213, 22], [204, 25, 213, 23, "slice"], [204, 30, 213, 28], [204, 31, 213, 29], [204, 32, 213, 30], [204, 34, 213, 32], [204, 35, 213, 33], [204, 36, 213, 34], [204, 37, 213, 35], [204, 38, 213, 36], [205, 4, 214, 2], [205, 5, 214, 3], [206, 4, 216, 2], [206, 10, 216, 8, "detectFacesAggressive"], [206, 31, 216, 29], [206, 34, 216, 32, "detectFacesAggressive"], [206, 35, 216, 33, "img"], [206, 38, 216, 54], [206, 40, 216, 56, "ctx"], [206, 43, 216, 85], [206, 48, 216, 90], [207, 6, 217, 4, "console"], [207, 13, 217, 11], [207, 14, 217, 12, "log"], [207, 17, 217, 15], [207, 18, 217, 16], [207, 75, 217, 73], [207, 76, 217, 74], [209, 6, 219, 4], [210, 6, 220, 4], [210, 12, 220, 10, "imageData"], [210, 21, 220, 19], [210, 24, 220, 22, "ctx"], [210, 27, 220, 25], [210, 28, 220, 26, "getImageData"], [210, 40, 220, 38], [210, 41, 220, 39], [210, 42, 220, 40], [210, 44, 220, 42], [210, 45, 220, 43], [210, 47, 220, 45, "img"], [210, 50, 220, 48], [210, 51, 220, 49, "width"], [210, 56, 220, 54], [210, 58, 220, 56, "img"], [210, 61, 220, 59], [210, 62, 220, 60, "height"], [210, 68, 220, 66], [210, 69, 220, 67], [211, 6, 221, 4], [211, 12, 221, 10, "data"], [211, 16, 221, 14], [211, 19, 221, 17, "imageData"], [211, 28, 221, 26], [211, 29, 221, 27, "data"], [211, 33, 221, 31], [212, 6, 223, 4], [212, 12, 223, 10, "faces"], [212, 17, 223, 15], [212, 20, 223, 18], [212, 22, 223, 20], [213, 6, 224, 4], [213, 12, 224, 10, "blockSize"], [213, 21, 224, 19], [213, 24, 224, 22, "Math"], [213, 28, 224, 26], [213, 29, 224, 27, "min"], [213, 32, 224, 30], [213, 33, 224, 31, "img"], [213, 36, 224, 34], [213, 37, 224, 35, "width"], [213, 42, 224, 40], [213, 44, 224, 42, "img"], [213, 47, 224, 45], [213, 48, 224, 46, "height"], [213, 54, 224, 52], [213, 55, 224, 53], [213, 58, 224, 56], [213, 59, 224, 57], [213, 60, 224, 58], [213, 61, 224, 59], [215, 6, 226, 4], [215, 11, 226, 9], [215, 15, 226, 13, "y"], [215, 16, 226, 14], [215, 19, 226, 17], [215, 20, 226, 18], [215, 22, 226, 20, "y"], [215, 23, 226, 21], [215, 26, 226, 24, "img"], [215, 29, 226, 27], [215, 30, 226, 28, "height"], [215, 36, 226, 34], [215, 39, 226, 37, "blockSize"], [215, 48, 226, 46], [215, 50, 226, 48, "y"], [215, 51, 226, 49], [215, 55, 226, 53, "blockSize"], [215, 64, 226, 62], [215, 67, 226, 65], [215, 68, 226, 66], [215, 70, 226, 68], [216, 8, 226, 70], [217, 8, 227, 6], [217, 13, 227, 11], [217, 17, 227, 15, "x"], [217, 18, 227, 16], [217, 21, 227, 19], [217, 22, 227, 20], [217, 24, 227, 22, "x"], [217, 25, 227, 23], [217, 28, 227, 26, "img"], [217, 31, 227, 29], [217, 32, 227, 30, "width"], [217, 37, 227, 35], [217, 40, 227, 38, "blockSize"], [217, 49, 227, 47], [217, 51, 227, 49, "x"], [217, 52, 227, 50], [217, 56, 227, 54, "blockSize"], [217, 65, 227, 63], [217, 68, 227, 66], [217, 69, 227, 67], [217, 71, 227, 69], [218, 10, 228, 8], [218, 16, 228, 14, "analysis"], [218, 24, 228, 22], [218, 27, 228, 25, "analyzeRegionForFace"], [218, 47, 228, 45], [218, 48, 228, 46, "data"], [218, 52, 228, 50], [218, 54, 228, 52, "x"], [218, 55, 228, 53], [218, 57, 228, 55, "y"], [218, 58, 228, 56], [218, 60, 228, 58, "blockSize"], [218, 69, 228, 67], [218, 71, 228, 69, "img"], [218, 74, 228, 72], [218, 75, 228, 73, "width"], [218, 80, 228, 78], [218, 82, 228, 80, "img"], [218, 85, 228, 83], [218, 86, 228, 84, "height"], [218, 92, 228, 90], [218, 93, 228, 91], [220, 10, 230, 8], [221, 10, 231, 8], [221, 14, 231, 12, "analysis"], [221, 22, 231, 20], [221, 23, 231, 21, "skinRatio"], [221, 32, 231, 30], [221, 35, 231, 33], [221, 39, 231, 37], [222, 10, 231, 42], [223, 10, 232, 12, "analysis"], [223, 18, 232, 20], [223, 19, 232, 21, "brightness"], [223, 29, 232, 31], [223, 32, 232, 34], [223, 35, 232, 37], [224, 10, 232, 42], [225, 10, 233, 12, "analysis"], [225, 18, 233, 20], [225, 19, 233, 21, "brightness"], [225, 29, 233, 31], [225, 32, 233, 34], [225, 36, 233, 38], [225, 38, 233, 40], [226, 12, 233, 43], [228, 12, 235, 10, "faces"], [228, 17, 235, 15], [228, 18, 235, 16, "push"], [228, 22, 235, 20], [228, 23, 235, 21], [229, 14, 236, 12, "boundingBox"], [229, 25, 236, 23], [229, 27, 236, 25], [230, 16, 237, 14, "xCenter"], [230, 23, 237, 21], [230, 25, 237, 23], [230, 26, 237, 24, "x"], [230, 27, 237, 25], [230, 30, 237, 28, "blockSize"], [230, 39, 237, 37], [230, 42, 237, 40], [230, 43, 237, 41], [230, 47, 237, 45, "img"], [230, 50, 237, 48], [230, 51, 237, 49, "width"], [230, 56, 237, 54], [231, 16, 238, 14, "yCenter"], [231, 23, 238, 21], [231, 25, 238, 23], [231, 26, 238, 24, "y"], [231, 27, 238, 25], [231, 30, 238, 28, "blockSize"], [231, 39, 238, 37], [231, 42, 238, 40], [231, 43, 238, 41], [231, 47, 238, 45, "img"], [231, 50, 238, 48], [231, 51, 238, 49, "height"], [231, 57, 238, 55], [232, 16, 239, 14, "width"], [232, 21, 239, 19], [232, 23, 239, 21, "blockSize"], [232, 32, 239, 30], [232, 35, 239, 33, "img"], [232, 38, 239, 36], [232, 39, 239, 37, "width"], [232, 44, 239, 42], [233, 16, 240, 14, "height"], [233, 22, 240, 20], [233, 24, 240, 22, "blockSize"], [233, 33, 240, 31], [233, 36, 240, 34, "img"], [233, 39, 240, 37], [233, 40, 240, 38, "height"], [234, 14, 241, 12], [234, 15, 241, 13], [235, 14, 242, 12, "confidence"], [235, 24, 242, 22], [235, 26, 242, 24], [235, 29, 242, 27], [235, 30, 242, 28], [236, 12, 243, 10], [236, 13, 243, 11], [236, 14, 243, 12], [237, 10, 244, 8], [238, 8, 245, 6], [239, 6, 246, 4], [241, 6, 248, 4], [242, 6, 249, 4], [242, 12, 249, 10, "mergedFaces"], [242, 23, 249, 21], [242, 26, 249, 24, "mergeFaceDetections"], [242, 45, 249, 43], [242, 46, 249, 44, "faces"], [242, 51, 249, 49], [242, 52, 249, 50], [243, 6, 250, 4, "console"], [243, 13, 250, 11], [243, 14, 250, 12, "log"], [243, 17, 250, 15], [243, 18, 250, 16], [243, 62, 250, 60, "faces"], [243, 67, 250, 65], [243, 68, 250, 66, "length"], [243, 74, 250, 72], [243, 91, 250, 89, "mergedFaces"], [243, 102, 250, 100], [243, 103, 250, 101, "length"], [243, 109, 250, 107], [243, 124, 250, 122], [243, 125, 250, 123], [244, 6, 251, 4], [244, 13, 251, 11, "mergedFaces"], [244, 24, 251, 22], [244, 25, 251, 23, "slice"], [244, 30, 251, 28], [244, 31, 251, 29], [244, 32, 251, 30], [244, 34, 251, 32], [244, 35, 251, 33], [244, 36, 251, 34], [244, 37, 251, 35], [244, 38, 251, 36], [245, 4, 252, 2], [245, 5, 252, 3], [246, 4, 254, 2], [246, 10, 254, 8, "analyzeRegionForFace"], [246, 30, 254, 28], [246, 33, 254, 31, "analyzeRegionForFace"], [246, 34, 254, 32, "data"], [246, 38, 254, 55], [246, 40, 254, 57, "startX"], [246, 46, 254, 71], [246, 48, 254, 73, "startY"], [246, 54, 254, 87], [246, 56, 254, 89, "size"], [246, 60, 254, 101], [246, 62, 254, 103, "imageWidth"], [246, 72, 254, 121], [246, 74, 254, 123, "imageHeight"], [246, 85, 254, 142], [246, 90, 254, 147], [247, 6, 255, 4], [247, 10, 255, 8, "skinPixels"], [247, 20, 255, 18], [247, 23, 255, 21], [247, 24, 255, 22], [248, 6, 256, 4], [248, 10, 256, 8, "totalPixels"], [248, 21, 256, 19], [248, 24, 256, 22], [248, 25, 256, 23], [249, 6, 257, 4], [249, 10, 257, 8, "totalBrightness"], [249, 25, 257, 23], [249, 28, 257, 26], [249, 29, 257, 27], [250, 6, 258, 4], [250, 10, 258, 8, "colorVariations"], [250, 25, 258, 23], [250, 28, 258, 26], [250, 29, 258, 27], [251, 6, 259, 4], [251, 10, 259, 8, "prevR"], [251, 15, 259, 13], [251, 18, 259, 16], [251, 19, 259, 17], [252, 8, 259, 19, "prevG"], [252, 13, 259, 24], [252, 16, 259, 27], [252, 17, 259, 28], [253, 8, 259, 30, "prevB"], [253, 13, 259, 35], [253, 16, 259, 38], [253, 17, 259, 39], [254, 6, 261, 4], [254, 11, 261, 9], [254, 15, 261, 13, "y"], [254, 16, 261, 14], [254, 19, 261, 17, "startY"], [254, 25, 261, 23], [254, 27, 261, 25, "y"], [254, 28, 261, 26], [254, 31, 261, 29, "startY"], [254, 37, 261, 35], [254, 40, 261, 38, "size"], [254, 44, 261, 42], [254, 48, 261, 46, "y"], [254, 49, 261, 47], [254, 52, 261, 50, "imageHeight"], [254, 63, 261, 61], [254, 65, 261, 63, "y"], [254, 66, 261, 64], [254, 68, 261, 66], [254, 70, 261, 68], [255, 8, 262, 6], [255, 13, 262, 11], [255, 17, 262, 15, "x"], [255, 18, 262, 16], [255, 21, 262, 19, "startX"], [255, 27, 262, 25], [255, 29, 262, 27, "x"], [255, 30, 262, 28], [255, 33, 262, 31, "startX"], [255, 39, 262, 37], [255, 42, 262, 40, "size"], [255, 46, 262, 44], [255, 50, 262, 48, "x"], [255, 51, 262, 49], [255, 54, 262, 52, "imageWidth"], [255, 64, 262, 62], [255, 66, 262, 64, "x"], [255, 67, 262, 65], [255, 69, 262, 67], [255, 71, 262, 69], [256, 10, 263, 8], [256, 16, 263, 14, "index"], [256, 21, 263, 19], [256, 24, 263, 22], [256, 25, 263, 23, "y"], [256, 26, 263, 24], [256, 29, 263, 27, "imageWidth"], [256, 39, 263, 37], [256, 42, 263, 40, "x"], [256, 43, 263, 41], [256, 47, 263, 45], [256, 48, 263, 46], [257, 10, 264, 8], [257, 16, 264, 14, "r"], [257, 17, 264, 15], [257, 20, 264, 18, "data"], [257, 24, 264, 22], [257, 25, 264, 23, "index"], [257, 30, 264, 28], [257, 31, 264, 29], [258, 10, 265, 8], [258, 16, 265, 14, "g"], [258, 17, 265, 15], [258, 20, 265, 18, "data"], [258, 24, 265, 22], [258, 25, 265, 23, "index"], [258, 30, 265, 28], [258, 33, 265, 31], [258, 34, 265, 32], [258, 35, 265, 33], [259, 10, 266, 8], [259, 16, 266, 14, "b"], [259, 17, 266, 15], [259, 20, 266, 18, "data"], [259, 24, 266, 22], [259, 25, 266, 23, "index"], [259, 30, 266, 28], [259, 33, 266, 31], [259, 34, 266, 32], [259, 35, 266, 33], [261, 10, 268, 8], [262, 10, 269, 8], [262, 14, 269, 12, "isSkinTone"], [262, 24, 269, 22], [262, 25, 269, 23, "r"], [262, 26, 269, 24], [262, 28, 269, 26, "g"], [262, 29, 269, 27], [262, 31, 269, 29, "b"], [262, 32, 269, 30], [262, 33, 269, 31], [262, 35, 269, 33], [263, 12, 270, 10, "skinPixels"], [263, 22, 270, 20], [263, 24, 270, 22], [264, 10, 271, 8], [266, 10, 273, 8], [267, 10, 274, 8], [267, 16, 274, 14, "brightness"], [267, 26, 274, 24], [267, 29, 274, 27], [267, 30, 274, 28, "r"], [267, 31, 274, 29], [267, 34, 274, 32, "g"], [267, 35, 274, 33], [267, 38, 274, 36, "b"], [267, 39, 274, 37], [267, 44, 274, 42], [267, 45, 274, 43], [267, 48, 274, 46], [267, 51, 274, 49], [267, 52, 274, 50], [268, 10, 275, 8, "totalBrightness"], [268, 25, 275, 23], [268, 29, 275, 27, "brightness"], [268, 39, 275, 37], [270, 10, 277, 8], [271, 10, 278, 8], [271, 14, 278, 12, "totalPixels"], [271, 25, 278, 23], [271, 28, 278, 26], [271, 29, 278, 27], [271, 31, 278, 29], [272, 12, 279, 10], [272, 18, 279, 16, "colorDiff"], [272, 27, 279, 25], [272, 30, 279, 28, "Math"], [272, 34, 279, 32], [272, 35, 279, 33, "abs"], [272, 38, 279, 36], [272, 39, 279, 37, "r"], [272, 40, 279, 38], [272, 43, 279, 41, "prevR"], [272, 48, 279, 46], [272, 49, 279, 47], [272, 52, 279, 50, "Math"], [272, 56, 279, 54], [272, 57, 279, 55, "abs"], [272, 60, 279, 58], [272, 61, 279, 59, "g"], [272, 62, 279, 60], [272, 65, 279, 63, "prevG"], [272, 70, 279, 68], [272, 71, 279, 69], [272, 74, 279, 72, "Math"], [272, 78, 279, 76], [272, 79, 279, 77, "abs"], [272, 82, 279, 80], [272, 83, 279, 81, "b"], [272, 84, 279, 82], [272, 87, 279, 85, "prevB"], [272, 92, 279, 90], [272, 93, 279, 91], [273, 12, 280, 10], [273, 16, 280, 14, "colorDiff"], [273, 25, 280, 23], [273, 28, 280, 26], [273, 30, 280, 28], [273, 32, 280, 30], [274, 14, 280, 32], [275, 14, 281, 12, "colorVariations"], [275, 29, 281, 27], [275, 31, 281, 29], [276, 12, 282, 10], [277, 10, 283, 8], [278, 10, 285, 8, "prevR"], [278, 15, 285, 13], [278, 18, 285, 16, "r"], [278, 19, 285, 17], [279, 10, 285, 19, "prevG"], [279, 15, 285, 24], [279, 18, 285, 27, "g"], [279, 19, 285, 28], [280, 10, 285, 30, "prevB"], [280, 15, 285, 35], [280, 18, 285, 38, "b"], [280, 19, 285, 39], [281, 10, 286, 8, "totalPixels"], [281, 21, 286, 19], [281, 23, 286, 21], [282, 8, 287, 6], [283, 6, 288, 4], [284, 6, 290, 4], [284, 13, 290, 11], [285, 8, 291, 6, "skinRatio"], [285, 17, 291, 15], [285, 19, 291, 17, "skinPixels"], [285, 29, 291, 27], [285, 32, 291, 30, "totalPixels"], [285, 43, 291, 41], [286, 8, 292, 6, "brightness"], [286, 18, 292, 16], [286, 20, 292, 18, "totalBrightness"], [286, 35, 292, 33], [286, 38, 292, 36, "totalPixels"], [286, 49, 292, 47], [287, 8, 293, 6, "variation"], [287, 17, 293, 15], [287, 19, 293, 17, "colorVariations"], [287, 34, 293, 32], [287, 37, 293, 35, "totalPixels"], [287, 48, 293, 46], [288, 8, 294, 6, "hasVariation"], [288, 20, 294, 18], [288, 22, 294, 20, "colorVariations"], [288, 37, 294, 35], [288, 40, 294, 38, "totalPixels"], [288, 51, 294, 49], [288, 54, 294, 52], [288, 57, 294, 55], [288, 58, 294, 56], [289, 6, 295, 4], [289, 7, 295, 5], [290, 4, 296, 2], [290, 5, 296, 3], [291, 4, 298, 2], [291, 10, 298, 8, "isSkinTone"], [291, 20, 298, 18], [291, 23, 298, 21, "isSkinTone"], [291, 24, 298, 22, "r"], [291, 25, 298, 31], [291, 27, 298, 33, "g"], [291, 28, 298, 42], [291, 30, 298, 44, "b"], [291, 31, 298, 53], [291, 36, 298, 58], [292, 6, 299, 4], [293, 6, 300, 4], [293, 13, 301, 6, "r"], [293, 14, 301, 7], [293, 17, 301, 10], [293, 19, 301, 12], [293, 23, 301, 16, "g"], [293, 24, 301, 17], [293, 27, 301, 20], [293, 29, 301, 22], [293, 33, 301, 26, "b"], [293, 34, 301, 27], [293, 37, 301, 30], [293, 39, 301, 32], [293, 43, 302, 6, "r"], [293, 44, 302, 7], [293, 47, 302, 10, "g"], [293, 48, 302, 11], [293, 52, 302, 15, "r"], [293, 53, 302, 16], [293, 56, 302, 19, "b"], [293, 57, 302, 20], [293, 61, 303, 6, "Math"], [293, 65, 303, 10], [293, 66, 303, 11, "abs"], [293, 69, 303, 14], [293, 70, 303, 15, "r"], [293, 71, 303, 16], [293, 74, 303, 19, "g"], [293, 75, 303, 20], [293, 76, 303, 21], [293, 79, 303, 24], [293, 81, 303, 26], [293, 85, 304, 6, "Math"], [293, 89, 304, 10], [293, 90, 304, 11, "max"], [293, 93, 304, 14], [293, 94, 304, 15, "r"], [293, 95, 304, 16], [293, 97, 304, 18, "g"], [293, 98, 304, 19], [293, 100, 304, 21, "b"], [293, 101, 304, 22], [293, 102, 304, 23], [293, 105, 304, 26, "Math"], [293, 109, 304, 30], [293, 110, 304, 31, "min"], [293, 113, 304, 34], [293, 114, 304, 35, "r"], [293, 115, 304, 36], [293, 117, 304, 38, "g"], [293, 118, 304, 39], [293, 120, 304, 41, "b"], [293, 121, 304, 42], [293, 122, 304, 43], [293, 125, 304, 46], [293, 127, 304, 48], [294, 4, 306, 2], [294, 5, 306, 3], [295, 4, 308, 2], [295, 10, 308, 8, "mergeFaceDetections"], [295, 29, 308, 27], [295, 32, 308, 31, "faces"], [295, 37, 308, 43], [295, 41, 308, 48], [296, 6, 309, 4], [296, 10, 309, 8, "faces"], [296, 15, 309, 13], [296, 16, 309, 14, "length"], [296, 22, 309, 20], [296, 26, 309, 24], [296, 27, 309, 25], [296, 29, 309, 27], [296, 36, 309, 34, "faces"], [296, 41, 309, 39], [297, 6, 311, 4], [297, 12, 311, 10, "merged"], [297, 18, 311, 16], [297, 21, 311, 19], [297, 23, 311, 21], [298, 6, 312, 4], [298, 12, 312, 10, "used"], [298, 16, 312, 14], [298, 19, 312, 17], [298, 23, 312, 21, "Set"], [298, 26, 312, 24], [298, 27, 312, 25], [298, 28, 312, 26], [299, 6, 314, 4], [299, 11, 314, 9], [299, 15, 314, 13, "i"], [299, 16, 314, 14], [299, 19, 314, 17], [299, 20, 314, 18], [299, 22, 314, 20, "i"], [299, 23, 314, 21], [299, 26, 314, 24, "faces"], [299, 31, 314, 29], [299, 32, 314, 30, "length"], [299, 38, 314, 36], [299, 40, 314, 38, "i"], [299, 41, 314, 39], [299, 43, 314, 41], [299, 45, 314, 43], [300, 8, 315, 6], [300, 12, 315, 10, "used"], [300, 16, 315, 14], [300, 17, 315, 15, "has"], [300, 20, 315, 18], [300, 21, 315, 19, "i"], [300, 22, 315, 20], [300, 23, 315, 21], [300, 25, 315, 23], [301, 8, 317, 6], [301, 12, 317, 10, "currentFace"], [301, 23, 317, 21], [301, 26, 317, 24, "faces"], [301, 31, 317, 29], [301, 32, 317, 30, "i"], [301, 33, 317, 31], [301, 34, 317, 32], [302, 8, 318, 6, "used"], [302, 12, 318, 10], [302, 13, 318, 11, "add"], [302, 16, 318, 14], [302, 17, 318, 15, "i"], [302, 18, 318, 16], [302, 19, 318, 17], [304, 8, 320, 6], [305, 8, 321, 6], [305, 13, 321, 11], [305, 17, 321, 15, "j"], [305, 18, 321, 16], [305, 21, 321, 19, "i"], [305, 22, 321, 20], [305, 25, 321, 23], [305, 26, 321, 24], [305, 28, 321, 26, "j"], [305, 29, 321, 27], [305, 32, 321, 30, "faces"], [305, 37, 321, 35], [305, 38, 321, 36, "length"], [305, 44, 321, 42], [305, 46, 321, 44, "j"], [305, 47, 321, 45], [305, 49, 321, 47], [305, 51, 321, 49], [306, 10, 322, 8], [306, 14, 322, 12, "used"], [306, 18, 322, 16], [306, 19, 322, 17, "has"], [306, 22, 322, 20], [306, 23, 322, 21, "j"], [306, 24, 322, 22], [306, 25, 322, 23], [306, 27, 322, 25], [307, 10, 324, 8], [307, 16, 324, 14, "overlap"], [307, 23, 324, 21], [307, 26, 324, 24, "calculateOverlap"], [307, 42, 324, 40], [307, 43, 324, 41, "currentFace"], [307, 54, 324, 52], [307, 55, 324, 53, "boundingBox"], [307, 66, 324, 64], [307, 68, 324, 66, "faces"], [307, 73, 324, 71], [307, 74, 324, 72, "j"], [307, 75, 324, 73], [307, 76, 324, 74], [307, 77, 324, 75, "boundingBox"], [307, 88, 324, 86], [307, 89, 324, 87], [308, 10, 325, 8], [308, 14, 325, 12, "overlap"], [308, 21, 325, 19], [308, 24, 325, 22], [308, 27, 325, 25], [308, 29, 325, 27], [309, 12, 325, 29], [310, 12, 326, 10], [311, 12, 327, 10, "currentFace"], [311, 23, 327, 21], [311, 26, 327, 24, "mergeTwoFaces"], [311, 39, 327, 37], [311, 40, 327, 38, "currentFace"], [311, 51, 327, 49], [311, 53, 327, 51, "faces"], [311, 58, 327, 56], [311, 59, 327, 57, "j"], [311, 60, 327, 58], [311, 61, 327, 59], [311, 62, 327, 60], [312, 12, 328, 10, "used"], [312, 16, 328, 14], [312, 17, 328, 15, "add"], [312, 20, 328, 18], [312, 21, 328, 19, "j"], [312, 22, 328, 20], [312, 23, 328, 21], [313, 10, 329, 8], [314, 8, 330, 6], [315, 8, 332, 6, "merged"], [315, 14, 332, 12], [315, 15, 332, 13, "push"], [315, 19, 332, 17], [315, 20, 332, 18, "currentFace"], [315, 31, 332, 29], [315, 32, 332, 30], [316, 6, 333, 4], [317, 6, 335, 4], [317, 13, 335, 11, "merged"], [317, 19, 335, 17], [318, 4, 336, 2], [318, 5, 336, 3], [319, 4, 338, 2], [319, 10, 338, 8, "calculateOverlap"], [319, 26, 338, 24], [319, 29, 338, 27, "calculateOverlap"], [319, 30, 338, 28, "box1"], [319, 34, 338, 37], [319, 36, 338, 39, "box2"], [319, 40, 338, 48], [319, 45, 338, 53], [320, 6, 339, 4], [320, 12, 339, 10, "x1"], [320, 14, 339, 12], [320, 17, 339, 15, "Math"], [320, 21, 339, 19], [320, 22, 339, 20, "max"], [320, 25, 339, 23], [320, 26, 339, 24, "box1"], [320, 30, 339, 28], [320, 31, 339, 29, "xCenter"], [320, 38, 339, 36], [320, 41, 339, 39, "box1"], [320, 45, 339, 43], [320, 46, 339, 44, "width"], [320, 51, 339, 49], [320, 54, 339, 50], [320, 55, 339, 51], [320, 57, 339, 53, "box2"], [320, 61, 339, 57], [320, 62, 339, 58, "xCenter"], [320, 69, 339, 65], [320, 72, 339, 68, "box2"], [320, 76, 339, 72], [320, 77, 339, 73, "width"], [320, 82, 339, 78], [320, 85, 339, 79], [320, 86, 339, 80], [320, 87, 339, 81], [321, 6, 340, 4], [321, 12, 340, 10, "y1"], [321, 14, 340, 12], [321, 17, 340, 15, "Math"], [321, 21, 340, 19], [321, 22, 340, 20, "max"], [321, 25, 340, 23], [321, 26, 340, 24, "box1"], [321, 30, 340, 28], [321, 31, 340, 29, "yCenter"], [321, 38, 340, 36], [321, 41, 340, 39, "box1"], [321, 45, 340, 43], [321, 46, 340, 44, "height"], [321, 52, 340, 50], [321, 55, 340, 51], [321, 56, 340, 52], [321, 58, 340, 54, "box2"], [321, 62, 340, 58], [321, 63, 340, 59, "yCenter"], [321, 70, 340, 66], [321, 73, 340, 69, "box2"], [321, 77, 340, 73], [321, 78, 340, 74, "height"], [321, 84, 340, 80], [321, 87, 340, 81], [321, 88, 340, 82], [321, 89, 340, 83], [322, 6, 341, 4], [322, 12, 341, 10, "x2"], [322, 14, 341, 12], [322, 17, 341, 15, "Math"], [322, 21, 341, 19], [322, 22, 341, 20, "min"], [322, 25, 341, 23], [322, 26, 341, 24, "box1"], [322, 30, 341, 28], [322, 31, 341, 29, "xCenter"], [322, 38, 341, 36], [322, 41, 341, 39, "box1"], [322, 45, 341, 43], [322, 46, 341, 44, "width"], [322, 51, 341, 49], [322, 54, 341, 50], [322, 55, 341, 51], [322, 57, 341, 53, "box2"], [322, 61, 341, 57], [322, 62, 341, 58, "xCenter"], [322, 69, 341, 65], [322, 72, 341, 68, "box2"], [322, 76, 341, 72], [322, 77, 341, 73, "width"], [322, 82, 341, 78], [322, 85, 341, 79], [322, 86, 341, 80], [322, 87, 341, 81], [323, 6, 342, 4], [323, 12, 342, 10, "y2"], [323, 14, 342, 12], [323, 17, 342, 15, "Math"], [323, 21, 342, 19], [323, 22, 342, 20, "min"], [323, 25, 342, 23], [323, 26, 342, 24, "box1"], [323, 30, 342, 28], [323, 31, 342, 29, "yCenter"], [323, 38, 342, 36], [323, 41, 342, 39, "box1"], [323, 45, 342, 43], [323, 46, 342, 44, "height"], [323, 52, 342, 50], [323, 55, 342, 51], [323, 56, 342, 52], [323, 58, 342, 54, "box2"], [323, 62, 342, 58], [323, 63, 342, 59, "yCenter"], [323, 70, 342, 66], [323, 73, 342, 69, "box2"], [323, 77, 342, 73], [323, 78, 342, 74, "height"], [323, 84, 342, 80], [323, 87, 342, 81], [323, 88, 342, 82], [323, 89, 342, 83], [324, 6, 344, 4], [324, 10, 344, 8, "x2"], [324, 12, 344, 10], [324, 16, 344, 14, "x1"], [324, 18, 344, 16], [324, 22, 344, 20, "y2"], [324, 24, 344, 22], [324, 28, 344, 26, "y1"], [324, 30, 344, 28], [324, 32, 344, 30], [324, 39, 344, 37], [324, 40, 344, 38], [325, 6, 346, 4], [325, 12, 346, 10, "overlapArea"], [325, 23, 346, 21], [325, 26, 346, 24], [325, 27, 346, 25, "x2"], [325, 29, 346, 27], [325, 32, 346, 30, "x1"], [325, 34, 346, 32], [325, 39, 346, 37, "y2"], [325, 41, 346, 39], [325, 44, 346, 42, "y1"], [325, 46, 346, 44], [325, 47, 346, 45], [326, 6, 347, 4], [326, 12, 347, 10, "box1Area"], [326, 20, 347, 18], [326, 23, 347, 21, "box1"], [326, 27, 347, 25], [326, 28, 347, 26, "width"], [326, 33, 347, 31], [326, 36, 347, 34, "box1"], [326, 40, 347, 38], [326, 41, 347, 39, "height"], [326, 47, 347, 45], [327, 6, 348, 4], [327, 12, 348, 10, "box2Area"], [327, 20, 348, 18], [327, 23, 348, 21, "box2"], [327, 27, 348, 25], [327, 28, 348, 26, "width"], [327, 33, 348, 31], [327, 36, 348, 34, "box2"], [327, 40, 348, 38], [327, 41, 348, 39, "height"], [327, 47, 348, 45], [328, 6, 350, 4], [328, 13, 350, 11, "overlapArea"], [328, 24, 350, 22], [328, 27, 350, 25, "Math"], [328, 31, 350, 29], [328, 32, 350, 30, "min"], [328, 35, 350, 33], [328, 36, 350, 34, "box1Area"], [328, 44, 350, 42], [328, 46, 350, 44, "box2Area"], [328, 54, 350, 52], [328, 55, 350, 53], [329, 4, 351, 2], [329, 5, 351, 3], [330, 4, 353, 2], [330, 10, 353, 8, "mergeTwoFaces"], [330, 23, 353, 21], [330, 26, 353, 24, "mergeTwoFaces"], [330, 27, 353, 25, "face1"], [330, 32, 353, 35], [330, 34, 353, 37, "face2"], [330, 39, 353, 47], [330, 44, 353, 52], [331, 6, 354, 4], [331, 12, 354, 10, "box1"], [331, 16, 354, 14], [331, 19, 354, 17, "face1"], [331, 24, 354, 22], [331, 25, 354, 23, "boundingBox"], [331, 36, 354, 34], [332, 6, 355, 4], [332, 12, 355, 10, "box2"], [332, 16, 355, 14], [332, 19, 355, 17, "face2"], [332, 24, 355, 22], [332, 25, 355, 23, "boundingBox"], [332, 36, 355, 34], [333, 6, 357, 4], [333, 12, 357, 10, "left"], [333, 16, 357, 14], [333, 19, 357, 17, "Math"], [333, 23, 357, 21], [333, 24, 357, 22, "min"], [333, 27, 357, 25], [333, 28, 357, 26, "box1"], [333, 32, 357, 30], [333, 33, 357, 31, "xCenter"], [333, 40, 357, 38], [333, 43, 357, 41, "box1"], [333, 47, 357, 45], [333, 48, 357, 46, "width"], [333, 53, 357, 51], [333, 56, 357, 52], [333, 57, 357, 53], [333, 59, 357, 55, "box2"], [333, 63, 357, 59], [333, 64, 357, 60, "xCenter"], [333, 71, 357, 67], [333, 74, 357, 70, "box2"], [333, 78, 357, 74], [333, 79, 357, 75, "width"], [333, 84, 357, 80], [333, 87, 357, 81], [333, 88, 357, 82], [333, 89, 357, 83], [334, 6, 358, 4], [334, 12, 358, 10, "right"], [334, 17, 358, 15], [334, 20, 358, 18, "Math"], [334, 24, 358, 22], [334, 25, 358, 23, "max"], [334, 28, 358, 26], [334, 29, 358, 27, "box1"], [334, 33, 358, 31], [334, 34, 358, 32, "xCenter"], [334, 41, 358, 39], [334, 44, 358, 42, "box1"], [334, 48, 358, 46], [334, 49, 358, 47, "width"], [334, 54, 358, 52], [334, 57, 358, 53], [334, 58, 358, 54], [334, 60, 358, 56, "box2"], [334, 64, 358, 60], [334, 65, 358, 61, "xCenter"], [334, 72, 358, 68], [334, 75, 358, 71, "box2"], [334, 79, 358, 75], [334, 80, 358, 76, "width"], [334, 85, 358, 81], [334, 88, 358, 82], [334, 89, 358, 83], [334, 90, 358, 84], [335, 6, 359, 4], [335, 12, 359, 10, "top"], [335, 15, 359, 13], [335, 18, 359, 16, "Math"], [335, 22, 359, 20], [335, 23, 359, 21, "min"], [335, 26, 359, 24], [335, 27, 359, 25, "box1"], [335, 31, 359, 29], [335, 32, 359, 30, "yCenter"], [335, 39, 359, 37], [335, 42, 359, 40, "box1"], [335, 46, 359, 44], [335, 47, 359, 45, "height"], [335, 53, 359, 51], [335, 56, 359, 52], [335, 57, 359, 53], [335, 59, 359, 55, "box2"], [335, 63, 359, 59], [335, 64, 359, 60, "yCenter"], [335, 71, 359, 67], [335, 74, 359, 70, "box2"], [335, 78, 359, 74], [335, 79, 359, 75, "height"], [335, 85, 359, 81], [335, 88, 359, 82], [335, 89, 359, 83], [335, 90, 359, 84], [336, 6, 360, 4], [336, 12, 360, 10, "bottom"], [336, 18, 360, 16], [336, 21, 360, 19, "Math"], [336, 25, 360, 23], [336, 26, 360, 24, "max"], [336, 29, 360, 27], [336, 30, 360, 28, "box1"], [336, 34, 360, 32], [336, 35, 360, 33, "yCenter"], [336, 42, 360, 40], [336, 45, 360, 43, "box1"], [336, 49, 360, 47], [336, 50, 360, 48, "height"], [336, 56, 360, 54], [336, 59, 360, 55], [336, 60, 360, 56], [336, 62, 360, 58, "box2"], [336, 66, 360, 62], [336, 67, 360, 63, "yCenter"], [336, 74, 360, 70], [336, 77, 360, 73, "box2"], [336, 81, 360, 77], [336, 82, 360, 78, "height"], [336, 88, 360, 84], [336, 91, 360, 85], [336, 92, 360, 86], [336, 93, 360, 87], [337, 6, 362, 4], [337, 13, 362, 11], [338, 8, 363, 6, "boundingBox"], [338, 19, 363, 17], [338, 21, 363, 19], [339, 10, 364, 8, "xCenter"], [339, 17, 364, 15], [339, 19, 364, 17], [339, 20, 364, 18, "left"], [339, 24, 364, 22], [339, 27, 364, 25, "right"], [339, 32, 364, 30], [339, 36, 364, 34], [339, 37, 364, 35], [340, 10, 365, 8, "yCenter"], [340, 17, 365, 15], [340, 19, 365, 17], [340, 20, 365, 18, "top"], [340, 23, 365, 21], [340, 26, 365, 24, "bottom"], [340, 32, 365, 30], [340, 36, 365, 34], [340, 37, 365, 35], [341, 10, 366, 8, "width"], [341, 15, 366, 13], [341, 17, 366, 15, "right"], [341, 22, 366, 20], [341, 25, 366, 23, "left"], [341, 29, 366, 27], [342, 10, 367, 8, "height"], [342, 16, 367, 14], [342, 18, 367, 16, "bottom"], [342, 24, 367, 22], [342, 27, 367, 25, "top"], [343, 8, 368, 6], [344, 6, 369, 4], [344, 7, 369, 5], [345, 4, 370, 2], [345, 5, 370, 3], [347, 4, 372, 2], [348, 4, 373, 2], [348, 10, 373, 8, "applyStrongBlur"], [348, 25, 373, 23], [348, 28, 373, 26, "applyStrongBlur"], [348, 29, 373, 27, "ctx"], [348, 32, 373, 56], [348, 34, 373, 58, "x"], [348, 35, 373, 67], [348, 37, 373, 69, "y"], [348, 38, 373, 78], [348, 40, 373, 80, "width"], [348, 45, 373, 93], [348, 47, 373, 95, "height"], [348, 53, 373, 109], [348, 58, 373, 114], [349, 6, 374, 4], [350, 6, 375, 4], [350, 12, 375, 10, "canvasWidth"], [350, 23, 375, 21], [350, 26, 375, 24, "ctx"], [350, 29, 375, 27], [350, 30, 375, 28, "canvas"], [350, 36, 375, 34], [350, 37, 375, 35, "width"], [350, 42, 375, 40], [351, 6, 376, 4], [351, 12, 376, 10, "canvasHeight"], [351, 24, 376, 22], [351, 27, 376, 25, "ctx"], [351, 30, 376, 28], [351, 31, 376, 29, "canvas"], [351, 37, 376, 35], [351, 38, 376, 36, "height"], [351, 44, 376, 42], [352, 6, 378, 4], [352, 12, 378, 10, "clampedX"], [352, 20, 378, 18], [352, 23, 378, 21, "Math"], [352, 27, 378, 25], [352, 28, 378, 26, "max"], [352, 31, 378, 29], [352, 32, 378, 30], [352, 33, 378, 31], [352, 35, 378, 33, "Math"], [352, 39, 378, 37], [352, 40, 378, 38, "min"], [352, 43, 378, 41], [352, 44, 378, 42, "Math"], [352, 48, 378, 46], [352, 49, 378, 47, "floor"], [352, 54, 378, 52], [352, 55, 378, 53, "x"], [352, 56, 378, 54], [352, 57, 378, 55], [352, 59, 378, 57, "canvasWidth"], [352, 70, 378, 68], [352, 73, 378, 71], [352, 74, 378, 72], [352, 75, 378, 73], [352, 76, 378, 74], [353, 6, 379, 4], [353, 12, 379, 10, "clampedY"], [353, 20, 379, 18], [353, 23, 379, 21, "Math"], [353, 27, 379, 25], [353, 28, 379, 26, "max"], [353, 31, 379, 29], [353, 32, 379, 30], [353, 33, 379, 31], [353, 35, 379, 33, "Math"], [353, 39, 379, 37], [353, 40, 379, 38, "min"], [353, 43, 379, 41], [353, 44, 379, 42, "Math"], [353, 48, 379, 46], [353, 49, 379, 47, "floor"], [353, 54, 379, 52], [353, 55, 379, 53, "y"], [353, 56, 379, 54], [353, 57, 379, 55], [353, 59, 379, 57, "canvasHeight"], [353, 71, 379, 69], [353, 74, 379, 72], [353, 75, 379, 73], [353, 76, 379, 74], [353, 77, 379, 75], [354, 6, 380, 4], [354, 12, 380, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 380, 22], [354, 27, 380, 25, "Math"], [354, 31, 380, 29], [354, 32, 380, 30, "min"], [354, 35, 380, 33], [354, 36, 380, 34, "Math"], [354, 40, 380, 38], [354, 41, 380, 39, "floor"], [354, 46, 380, 44], [354, 47, 380, 45, "width"], [354, 52, 380, 50], [354, 53, 380, 51], [354, 55, 380, 53, "canvasWidth"], [354, 66, 380, 64], [354, 69, 380, 67, "clampedX"], [354, 77, 380, 75], [354, 78, 380, 76], [355, 6, 381, 4], [355, 12, 381, 10, "clampedHeight"], [355, 25, 381, 23], [355, 28, 381, 26, "Math"], [355, 32, 381, 30], [355, 33, 381, 31, "min"], [355, 36, 381, 34], [355, 37, 381, 35, "Math"], [355, 41, 381, 39], [355, 42, 381, 40, "floor"], [355, 47, 381, 45], [355, 48, 381, 46, "height"], [355, 54, 381, 52], [355, 55, 381, 53], [355, 57, 381, 55, "canvasHeight"], [355, 69, 381, 67], [355, 72, 381, 70, "clampedY"], [355, 80, 381, 78], [355, 81, 381, 79], [356, 6, 383, 4], [356, 10, 383, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 383, 20], [356, 26, 383, 24], [356, 27, 383, 25], [356, 31, 383, 29, "clampedHeight"], [356, 44, 383, 42], [356, 48, 383, 46], [356, 49, 383, 47], [356, 51, 383, 49], [357, 8, 384, 6, "console"], [357, 15, 384, 13], [357, 16, 384, 14, "warn"], [357, 20, 384, 18], [357, 21, 384, 19], [357, 73, 384, 71], [357, 75, 384, 73], [358, 10, 385, 8, "original"], [358, 18, 385, 16], [358, 20, 385, 18], [359, 12, 385, 20, "x"], [359, 13, 385, 21], [360, 12, 385, 23, "y"], [360, 13, 385, 24], [361, 12, 385, 26, "width"], [361, 17, 385, 31], [362, 12, 385, 33, "height"], [363, 10, 385, 40], [363, 11, 385, 41], [364, 10, 386, 8, "canvas"], [364, 16, 386, 14], [364, 18, 386, 16], [365, 12, 386, 18, "width"], [365, 17, 386, 23], [365, 19, 386, 25, "canvasWidth"], [365, 30, 386, 36], [366, 12, 386, 38, "height"], [366, 18, 386, 44], [366, 20, 386, 46, "canvasHeight"], [367, 10, 386, 59], [367, 11, 386, 60], [368, 10, 387, 8, "clamped"], [368, 17, 387, 15], [368, 19, 387, 17], [369, 12, 387, 19, "x"], [369, 13, 387, 20], [369, 15, 387, 22, "clampedX"], [369, 23, 387, 30], [370, 12, 387, 32, "y"], [370, 13, 387, 33], [370, 15, 387, 35, "clampedY"], [370, 23, 387, 43], [371, 12, 387, 45, "width"], [371, 17, 387, 50], [371, 19, 387, 52, "<PERSON><PERSON><PERSON><PERSON>"], [371, 31, 387, 64], [372, 12, 387, 66, "height"], [372, 18, 387, 72], [372, 20, 387, 74, "clampedHeight"], [373, 10, 387, 88], [374, 8, 388, 6], [374, 9, 388, 7], [374, 10, 388, 8], [375, 8, 389, 6], [376, 6, 390, 4], [378, 6, 392, 4], [379, 6, 393, 4], [379, 12, 393, 10, "imageData"], [379, 21, 393, 19], [379, 24, 393, 22, "ctx"], [379, 27, 393, 25], [379, 28, 393, 26, "getImageData"], [379, 40, 393, 38], [379, 41, 393, 39, "clampedX"], [379, 49, 393, 47], [379, 51, 393, 49, "clampedY"], [379, 59, 393, 57], [379, 61, 393, 59, "<PERSON><PERSON><PERSON><PERSON>"], [379, 73, 393, 71], [379, 75, 393, 73, "clampedHeight"], [379, 88, 393, 86], [379, 89, 393, 87], [380, 6, 394, 4], [380, 12, 394, 10, "data"], [380, 16, 394, 14], [380, 19, 394, 17, "imageData"], [380, 28, 394, 26], [380, 29, 394, 27, "data"], [380, 33, 394, 31], [382, 6, 396, 4], [383, 6, 397, 4], [383, 12, 397, 10, "pixelSize"], [383, 21, 397, 19], [383, 24, 397, 22, "Math"], [383, 28, 397, 26], [383, 29, 397, 27, "max"], [383, 32, 397, 30], [383, 33, 397, 31], [383, 35, 397, 33], [383, 37, 397, 35, "Math"], [383, 41, 397, 39], [383, 42, 397, 40, "min"], [383, 45, 397, 43], [383, 46, 397, 44, "<PERSON><PERSON><PERSON><PERSON>"], [383, 58, 397, 56], [383, 60, 397, 58, "clampedHeight"], [383, 73, 397, 71], [383, 74, 397, 72], [383, 77, 397, 75], [383, 78, 397, 76], [383, 79, 397, 77], [384, 6, 399, 4], [384, 11, 399, 9], [384, 15, 399, 13, "py"], [384, 17, 399, 15], [384, 20, 399, 18], [384, 21, 399, 19], [384, 23, 399, 21, "py"], [384, 25, 399, 23], [384, 28, 399, 26, "clampedHeight"], [384, 41, 399, 39], [384, 43, 399, 41, "py"], [384, 45, 399, 43], [384, 49, 399, 47, "pixelSize"], [384, 58, 399, 56], [384, 60, 399, 58], [385, 8, 400, 6], [385, 13, 400, 11], [385, 17, 400, 15, "px"], [385, 19, 400, 17], [385, 22, 400, 20], [385, 23, 400, 21], [385, 25, 400, 23, "px"], [385, 27, 400, 25], [385, 30, 400, 28, "<PERSON><PERSON><PERSON><PERSON>"], [385, 42, 400, 40], [385, 44, 400, 42, "px"], [385, 46, 400, 44], [385, 50, 400, 48, "pixelSize"], [385, 59, 400, 57], [385, 61, 400, 59], [386, 10, 401, 8], [387, 10, 402, 8], [387, 14, 402, 12, "r"], [387, 15, 402, 13], [387, 18, 402, 16], [387, 19, 402, 17], [388, 12, 402, 19, "g"], [388, 13, 402, 20], [388, 16, 402, 23], [388, 17, 402, 24], [389, 12, 402, 26, "b"], [389, 13, 402, 27], [389, 16, 402, 30], [389, 17, 402, 31], [390, 12, 402, 33, "count"], [390, 17, 402, 38], [390, 20, 402, 41], [390, 21, 402, 42], [391, 10, 404, 8], [391, 15, 404, 13], [391, 19, 404, 17, "dy"], [391, 21, 404, 19], [391, 24, 404, 22], [391, 25, 404, 23], [391, 27, 404, 25, "dy"], [391, 29, 404, 27], [391, 32, 404, 30, "pixelSize"], [391, 41, 404, 39], [391, 45, 404, 43, "py"], [391, 47, 404, 45], [391, 50, 404, 48, "dy"], [391, 52, 404, 50], [391, 55, 404, 53, "clampedHeight"], [391, 68, 404, 66], [391, 70, 404, 68, "dy"], [391, 72, 404, 70], [391, 74, 404, 72], [391, 76, 404, 74], [392, 12, 405, 10], [392, 17, 405, 15], [392, 21, 405, 19, "dx"], [392, 23, 405, 21], [392, 26, 405, 24], [392, 27, 405, 25], [392, 29, 405, 27, "dx"], [392, 31, 405, 29], [392, 34, 405, 32, "pixelSize"], [392, 43, 405, 41], [392, 47, 405, 45, "px"], [392, 49, 405, 47], [392, 52, 405, 50, "dx"], [392, 54, 405, 52], [392, 57, 405, 55, "<PERSON><PERSON><PERSON><PERSON>"], [392, 69, 405, 67], [392, 71, 405, 69, "dx"], [392, 73, 405, 71], [392, 75, 405, 73], [392, 77, 405, 75], [393, 14, 406, 12], [393, 20, 406, 18, "index"], [393, 25, 406, 23], [393, 28, 406, 26], [393, 29, 406, 27], [393, 30, 406, 28, "py"], [393, 32, 406, 30], [393, 35, 406, 33, "dy"], [393, 37, 406, 35], [393, 41, 406, 39, "<PERSON><PERSON><PERSON><PERSON>"], [393, 53, 406, 51], [393, 57, 406, 55, "px"], [393, 59, 406, 57], [393, 62, 406, 60, "dx"], [393, 64, 406, 62], [393, 65, 406, 63], [393, 69, 406, 67], [393, 70, 406, 68], [394, 14, 407, 12, "r"], [394, 15, 407, 13], [394, 19, 407, 17, "data"], [394, 23, 407, 21], [394, 24, 407, 22, "index"], [394, 29, 407, 27], [394, 30, 407, 28], [395, 14, 408, 12, "g"], [395, 15, 408, 13], [395, 19, 408, 17, "data"], [395, 23, 408, 21], [395, 24, 408, 22, "index"], [395, 29, 408, 27], [395, 32, 408, 30], [395, 33, 408, 31], [395, 34, 408, 32], [396, 14, 409, 12, "b"], [396, 15, 409, 13], [396, 19, 409, 17, "data"], [396, 23, 409, 21], [396, 24, 409, 22, "index"], [396, 29, 409, 27], [396, 32, 409, 30], [396, 33, 409, 31], [396, 34, 409, 32], [397, 14, 410, 12, "count"], [397, 19, 410, 17], [397, 21, 410, 19], [398, 12, 411, 10], [399, 10, 412, 8], [400, 10, 414, 8], [400, 14, 414, 12, "count"], [400, 19, 414, 17], [400, 22, 414, 20], [400, 23, 414, 21], [400, 25, 414, 23], [401, 12, 415, 10, "r"], [401, 13, 415, 11], [401, 16, 415, 14, "Math"], [401, 20, 415, 18], [401, 21, 415, 19, "floor"], [401, 26, 415, 24], [401, 27, 415, 25, "r"], [401, 28, 415, 26], [401, 31, 415, 29, "count"], [401, 36, 415, 34], [401, 37, 415, 35], [402, 12, 416, 10, "g"], [402, 13, 416, 11], [402, 16, 416, 14, "Math"], [402, 20, 416, 18], [402, 21, 416, 19, "floor"], [402, 26, 416, 24], [402, 27, 416, 25, "g"], [402, 28, 416, 26], [402, 31, 416, 29, "count"], [402, 36, 416, 34], [402, 37, 416, 35], [403, 12, 417, 10, "b"], [403, 13, 417, 11], [403, 16, 417, 14, "Math"], [403, 20, 417, 18], [403, 21, 417, 19, "floor"], [403, 26, 417, 24], [403, 27, 417, 25, "b"], [403, 28, 417, 26], [403, 31, 417, 29, "count"], [403, 36, 417, 34], [403, 37, 417, 35], [405, 12, 419, 10], [406, 12, 420, 10], [406, 17, 420, 15], [406, 21, 420, 19, "dy"], [406, 23, 420, 21], [406, 26, 420, 24], [406, 27, 420, 25], [406, 29, 420, 27, "dy"], [406, 31, 420, 29], [406, 34, 420, 32, "pixelSize"], [406, 43, 420, 41], [406, 47, 420, 45, "py"], [406, 49, 420, 47], [406, 52, 420, 50, "dy"], [406, 54, 420, 52], [406, 57, 420, 55, "clampedHeight"], [406, 70, 420, 68], [406, 72, 420, 70, "dy"], [406, 74, 420, 72], [406, 76, 420, 74], [406, 78, 420, 76], [407, 14, 421, 12], [407, 19, 421, 17], [407, 23, 421, 21, "dx"], [407, 25, 421, 23], [407, 28, 421, 26], [407, 29, 421, 27], [407, 31, 421, 29, "dx"], [407, 33, 421, 31], [407, 36, 421, 34, "pixelSize"], [407, 45, 421, 43], [407, 49, 421, 47, "px"], [407, 51, 421, 49], [407, 54, 421, 52, "dx"], [407, 56, 421, 54], [407, 59, 421, 57, "<PERSON><PERSON><PERSON><PERSON>"], [407, 71, 421, 69], [407, 73, 421, 71, "dx"], [407, 75, 421, 73], [407, 77, 421, 75], [407, 79, 421, 77], [408, 16, 422, 14], [408, 22, 422, 20, "index"], [408, 27, 422, 25], [408, 30, 422, 28], [408, 31, 422, 29], [408, 32, 422, 30, "py"], [408, 34, 422, 32], [408, 37, 422, 35, "dy"], [408, 39, 422, 37], [408, 43, 422, 41, "<PERSON><PERSON><PERSON><PERSON>"], [408, 55, 422, 53], [408, 59, 422, 57, "px"], [408, 61, 422, 59], [408, 64, 422, 62, "dx"], [408, 66, 422, 64], [408, 67, 422, 65], [408, 71, 422, 69], [408, 72, 422, 70], [409, 16, 423, 14, "data"], [409, 20, 423, 18], [409, 21, 423, 19, "index"], [409, 26, 423, 24], [409, 27, 423, 25], [409, 30, 423, 28, "r"], [409, 31, 423, 29], [410, 16, 424, 14, "data"], [410, 20, 424, 18], [410, 21, 424, 19, "index"], [410, 26, 424, 24], [410, 29, 424, 27], [410, 30, 424, 28], [410, 31, 424, 29], [410, 34, 424, 32, "g"], [410, 35, 424, 33], [411, 16, 425, 14, "data"], [411, 20, 425, 18], [411, 21, 425, 19, "index"], [411, 26, 425, 24], [411, 29, 425, 27], [411, 30, 425, 28], [411, 31, 425, 29], [411, 34, 425, 32, "b"], [411, 35, 425, 33], [412, 16, 426, 14], [413, 14, 427, 12], [414, 12, 428, 10], [415, 10, 429, 8], [416, 8, 430, 6], [417, 6, 431, 4], [419, 6, 433, 4], [420, 6, 434, 4], [420, 11, 434, 9], [420, 15, 434, 13, "i"], [420, 16, 434, 14], [420, 19, 434, 17], [420, 20, 434, 18], [420, 22, 434, 20, "i"], [420, 23, 434, 21], [420, 26, 434, 24], [420, 27, 434, 25], [420, 29, 434, 27, "i"], [420, 30, 434, 28], [420, 32, 434, 30], [420, 34, 434, 32], [421, 8, 435, 6, "applySimpleBlur"], [421, 23, 435, 21], [421, 24, 435, 22, "data"], [421, 28, 435, 26], [421, 30, 435, 28, "<PERSON><PERSON><PERSON><PERSON>"], [421, 42, 435, 40], [421, 44, 435, 42, "clampedHeight"], [421, 57, 435, 55], [421, 58, 435, 56], [422, 6, 436, 4], [424, 6, 438, 4], [425, 6, 439, 4, "ctx"], [425, 9, 439, 7], [425, 10, 439, 8, "putImageData"], [425, 22, 439, 20], [425, 23, 439, 21, "imageData"], [425, 32, 439, 30], [425, 34, 439, 32, "clampedX"], [425, 42, 439, 40], [425, 44, 439, 42, "clampedY"], [425, 52, 439, 50], [425, 53, 439, 51], [427, 6, 441, 4], [428, 6, 442, 4, "ctx"], [428, 9, 442, 7], [428, 10, 442, 8, "fillStyle"], [428, 19, 442, 17], [428, 22, 442, 20], [428, 48, 442, 46], [429, 6, 443, 4, "ctx"], [429, 9, 443, 7], [429, 10, 443, 8, "fillRect"], [429, 18, 443, 16], [429, 19, 443, 17, "clampedX"], [429, 27, 443, 25], [429, 29, 443, 27, "clampedY"], [429, 37, 443, 35], [429, 39, 443, 37, "<PERSON><PERSON><PERSON><PERSON>"], [429, 51, 443, 49], [429, 53, 443, 51, "clampedHeight"], [429, 66, 443, 64], [429, 67, 443, 65], [430, 4, 444, 2], [430, 5, 444, 3], [431, 4, 446, 2], [431, 10, 446, 8, "applySimpleBlur"], [431, 25, 446, 23], [431, 28, 446, 26, "applySimpleBlur"], [431, 29, 446, 27, "data"], [431, 33, 446, 50], [431, 35, 446, 52, "width"], [431, 40, 446, 65], [431, 42, 446, 67, "height"], [431, 48, 446, 81], [431, 53, 446, 86], [432, 6, 447, 4], [432, 12, 447, 10, "original"], [432, 20, 447, 18], [432, 23, 447, 21], [432, 27, 447, 25, "Uint8ClampedArray"], [432, 44, 447, 42], [432, 45, 447, 43, "data"], [432, 49, 447, 47], [432, 50, 447, 48], [433, 6, 449, 4], [433, 11, 449, 9], [433, 15, 449, 13, "y"], [433, 16, 449, 14], [433, 19, 449, 17], [433, 20, 449, 18], [433, 22, 449, 20, "y"], [433, 23, 449, 21], [433, 26, 449, 24, "height"], [433, 32, 449, 30], [433, 35, 449, 33], [433, 36, 449, 34], [433, 38, 449, 36, "y"], [433, 39, 449, 37], [433, 41, 449, 39], [433, 43, 449, 41], [434, 8, 450, 6], [434, 13, 450, 11], [434, 17, 450, 15, "x"], [434, 18, 450, 16], [434, 21, 450, 19], [434, 22, 450, 20], [434, 24, 450, 22, "x"], [434, 25, 450, 23], [434, 28, 450, 26, "width"], [434, 33, 450, 31], [434, 36, 450, 34], [434, 37, 450, 35], [434, 39, 450, 37, "x"], [434, 40, 450, 38], [434, 42, 450, 40], [434, 44, 450, 42], [435, 10, 451, 8], [435, 16, 451, 14, "index"], [435, 21, 451, 19], [435, 24, 451, 22], [435, 25, 451, 23, "y"], [435, 26, 451, 24], [435, 29, 451, 27, "width"], [435, 34, 451, 32], [435, 37, 451, 35, "x"], [435, 38, 451, 36], [435, 42, 451, 40], [435, 43, 451, 41], [437, 10, 453, 8], [438, 10, 454, 8], [438, 14, 454, 12, "r"], [438, 15, 454, 13], [438, 18, 454, 16], [438, 19, 454, 17], [439, 12, 454, 19, "g"], [439, 13, 454, 20], [439, 16, 454, 23], [439, 17, 454, 24], [440, 12, 454, 26, "b"], [440, 13, 454, 27], [440, 16, 454, 30], [440, 17, 454, 31], [441, 10, 455, 8], [441, 15, 455, 13], [441, 19, 455, 17, "dy"], [441, 21, 455, 19], [441, 24, 455, 22], [441, 25, 455, 23], [441, 26, 455, 24], [441, 28, 455, 26, "dy"], [441, 30, 455, 28], [441, 34, 455, 32], [441, 35, 455, 33], [441, 37, 455, 35, "dy"], [441, 39, 455, 37], [441, 41, 455, 39], [441, 43, 455, 41], [442, 12, 456, 10], [442, 17, 456, 15], [442, 21, 456, 19, "dx"], [442, 23, 456, 21], [442, 26, 456, 24], [442, 27, 456, 25], [442, 28, 456, 26], [442, 30, 456, 28, "dx"], [442, 32, 456, 30], [442, 36, 456, 34], [442, 37, 456, 35], [442, 39, 456, 37, "dx"], [442, 41, 456, 39], [442, 43, 456, 41], [442, 45, 456, 43], [443, 14, 457, 12], [443, 20, 457, 18, "neighborIndex"], [443, 33, 457, 31], [443, 36, 457, 34], [443, 37, 457, 35], [443, 38, 457, 36, "y"], [443, 39, 457, 37], [443, 42, 457, 40, "dy"], [443, 44, 457, 42], [443, 48, 457, 46, "width"], [443, 53, 457, 51], [443, 57, 457, 55, "x"], [443, 58, 457, 56], [443, 61, 457, 59, "dx"], [443, 63, 457, 61], [443, 64, 457, 62], [443, 68, 457, 66], [443, 69, 457, 67], [444, 14, 458, 12, "r"], [444, 15, 458, 13], [444, 19, 458, 17, "original"], [444, 27, 458, 25], [444, 28, 458, 26, "neighborIndex"], [444, 41, 458, 39], [444, 42, 458, 40], [445, 14, 459, 12, "g"], [445, 15, 459, 13], [445, 19, 459, 17, "original"], [445, 27, 459, 25], [445, 28, 459, 26, "neighborIndex"], [445, 41, 459, 39], [445, 44, 459, 42], [445, 45, 459, 43], [445, 46, 459, 44], [446, 14, 460, 12, "b"], [446, 15, 460, 13], [446, 19, 460, 17, "original"], [446, 27, 460, 25], [446, 28, 460, 26, "neighborIndex"], [446, 41, 460, 39], [446, 44, 460, 42], [446, 45, 460, 43], [446, 46, 460, 44], [447, 12, 461, 10], [448, 10, 462, 8], [449, 10, 464, 8, "data"], [449, 14, 464, 12], [449, 15, 464, 13, "index"], [449, 20, 464, 18], [449, 21, 464, 19], [449, 24, 464, 22, "r"], [449, 25, 464, 23], [449, 28, 464, 26], [449, 29, 464, 27], [450, 10, 465, 8, "data"], [450, 14, 465, 12], [450, 15, 465, 13, "index"], [450, 20, 465, 18], [450, 23, 465, 21], [450, 24, 465, 22], [450, 25, 465, 23], [450, 28, 465, 26, "g"], [450, 29, 465, 27], [450, 32, 465, 30], [450, 33, 465, 31], [451, 10, 466, 8, "data"], [451, 14, 466, 12], [451, 15, 466, 13, "index"], [451, 20, 466, 18], [451, 23, 466, 21], [451, 24, 466, 22], [451, 25, 466, 23], [451, 28, 466, 26, "b"], [451, 29, 466, 27], [451, 32, 466, 30], [451, 33, 466, 31], [452, 8, 467, 6], [453, 6, 468, 4], [454, 4, 469, 2], [454, 5, 469, 3], [455, 4, 471, 2], [455, 10, 471, 8, "applyFallbackFaceBlur"], [455, 31, 471, 29], [455, 34, 471, 32, "applyFallbackFaceBlur"], [455, 35, 471, 33, "ctx"], [455, 38, 471, 62], [455, 40, 471, 64, "imgWidth"], [455, 48, 471, 80], [455, 50, 471, 82, "imgHeight"], [455, 59, 471, 99], [455, 64, 471, 104], [456, 6, 472, 4, "console"], [456, 13, 472, 11], [456, 14, 472, 12, "log"], [456, 17, 472, 15], [456, 18, 472, 16], [456, 90, 472, 88], [456, 91, 472, 89], [458, 6, 474, 4], [459, 6, 475, 4], [459, 12, 475, 10, "areas"], [459, 17, 475, 15], [459, 20, 475, 18], [460, 6, 476, 6], [461, 6, 477, 6], [462, 8, 477, 8, "x"], [462, 9, 477, 9], [462, 11, 477, 11, "imgWidth"], [462, 19, 477, 19], [462, 22, 477, 22], [462, 26, 477, 26], [463, 8, 477, 28, "y"], [463, 9, 477, 29], [463, 11, 477, 31, "imgHeight"], [463, 20, 477, 40], [463, 23, 477, 43], [463, 27, 477, 47], [464, 8, 477, 49, "w"], [464, 9, 477, 50], [464, 11, 477, 52, "imgWidth"], [464, 19, 477, 60], [464, 22, 477, 63], [464, 25, 477, 66], [465, 8, 477, 68, "h"], [465, 9, 477, 69], [465, 11, 477, 71, "imgHeight"], [465, 20, 477, 80], [465, 23, 477, 83], [466, 6, 477, 87], [466, 7, 477, 88], [467, 6, 478, 6], [468, 6, 479, 6], [469, 8, 479, 8, "x"], [469, 9, 479, 9], [469, 11, 479, 11, "imgWidth"], [469, 19, 479, 19], [469, 22, 479, 22], [469, 25, 479, 25], [470, 8, 479, 27, "y"], [470, 9, 479, 28], [470, 11, 479, 30, "imgHeight"], [470, 20, 479, 39], [470, 23, 479, 42], [470, 26, 479, 45], [471, 8, 479, 47, "w"], [471, 9, 479, 48], [471, 11, 479, 50, "imgWidth"], [471, 19, 479, 58], [471, 22, 479, 61], [471, 26, 479, 65], [472, 8, 479, 67, "h"], [472, 9, 479, 68], [472, 11, 479, 70, "imgHeight"], [472, 20, 479, 79], [472, 23, 479, 82], [473, 6, 479, 86], [473, 7, 479, 87], [474, 6, 480, 6], [475, 6, 481, 6], [476, 8, 481, 8, "x"], [476, 9, 481, 9], [476, 11, 481, 11, "imgWidth"], [476, 19, 481, 19], [476, 22, 481, 22], [476, 26, 481, 26], [477, 8, 481, 28, "y"], [477, 9, 481, 29], [477, 11, 481, 31, "imgHeight"], [477, 20, 481, 40], [477, 23, 481, 43], [477, 26, 481, 46], [478, 8, 481, 48, "w"], [478, 9, 481, 49], [478, 11, 481, 51, "imgWidth"], [478, 19, 481, 59], [478, 22, 481, 62], [478, 26, 481, 66], [479, 8, 481, 68, "h"], [479, 9, 481, 69], [479, 11, 481, 71, "imgHeight"], [479, 20, 481, 80], [479, 23, 481, 83], [480, 6, 481, 87], [480, 7, 481, 88], [480, 8, 482, 5], [481, 6, 484, 4, "areas"], [481, 11, 484, 9], [481, 12, 484, 10, "for<PERSON>ach"], [481, 19, 484, 17], [481, 20, 484, 18], [481, 21, 484, 19, "area"], [481, 25, 484, 23], [481, 27, 484, 25, "index"], [481, 32, 484, 30], [481, 37, 484, 35], [482, 8, 485, 6, "console"], [482, 15, 485, 13], [482, 16, 485, 14, "log"], [482, 19, 485, 17], [482, 20, 485, 18], [482, 65, 485, 63, "index"], [482, 70, 485, 68], [482, 73, 485, 71], [482, 74, 485, 72], [482, 77, 485, 75], [482, 79, 485, 77, "area"], [482, 83, 485, 81], [482, 84, 485, 82], [483, 8, 486, 6, "applyStrongBlur"], [483, 23, 486, 21], [483, 24, 486, 22, "ctx"], [483, 27, 486, 25], [483, 29, 486, 27, "area"], [483, 33, 486, 31], [483, 34, 486, 32, "x"], [483, 35, 486, 33], [483, 37, 486, 35, "area"], [483, 41, 486, 39], [483, 42, 486, 40, "y"], [483, 43, 486, 41], [483, 45, 486, 43, "area"], [483, 49, 486, 47], [483, 50, 486, 48, "w"], [483, 51, 486, 49], [483, 53, 486, 51, "area"], [483, 57, 486, 55], [483, 58, 486, 56, "h"], [483, 59, 486, 57], [483, 60, 486, 58], [484, 6, 487, 4], [484, 7, 487, 5], [484, 8, 487, 6], [485, 4, 488, 2], [485, 5, 488, 3], [487, 4, 490, 2], [488, 4, 491, 2], [488, 10, 491, 8, "capturePhoto"], [488, 22, 491, 20], [488, 25, 491, 23], [488, 29, 491, 23, "useCallback"], [488, 47, 491, 34], [488, 49, 491, 35], [488, 61, 491, 47], [489, 6, 492, 4], [490, 6, 493, 4], [490, 12, 493, 10, "isDev"], [490, 17, 493, 15], [490, 20, 493, 18, "process"], [490, 27, 493, 25], [490, 28, 493, 26, "env"], [490, 31, 493, 29], [490, 32, 493, 30, "NODE_ENV"], [490, 40, 493, 38], [490, 45, 493, 43], [490, 58, 493, 56], [490, 62, 493, 60, "__DEV__"], [490, 69, 493, 67], [491, 6, 495, 4], [491, 10, 495, 8], [491, 11, 495, 9, "cameraRef"], [491, 20, 495, 18], [491, 21, 495, 19, "current"], [491, 28, 495, 26], [491, 32, 495, 30], [491, 33, 495, 31, "isDev"], [491, 38, 495, 36], [491, 40, 495, 38], [492, 8, 496, 6, "<PERSON><PERSON>"], [492, 22, 496, 11], [492, 23, 496, 12, "alert"], [492, 28, 496, 17], [492, 29, 496, 18], [492, 36, 496, 25], [492, 38, 496, 27], [492, 56, 496, 45], [492, 57, 496, 46], [493, 8, 497, 6], [494, 6, 498, 4], [495, 6, 499, 4], [495, 10, 499, 8], [496, 8, 500, 6, "setProcessingState"], [496, 26, 500, 24], [496, 27, 500, 25], [496, 38, 500, 36], [496, 39, 500, 37], [497, 8, 501, 6, "setProcessingProgress"], [497, 29, 501, 27], [497, 30, 501, 28], [497, 32, 501, 30], [497, 33, 501, 31], [498, 8, 502, 6], [499, 8, 503, 6], [500, 8, 504, 6], [501, 8, 505, 6], [501, 14, 505, 12], [501, 18, 505, 16, "Promise"], [501, 25, 505, 23], [501, 26, 505, 24, "resolve"], [501, 33, 505, 31], [501, 37, 505, 35, "setTimeout"], [501, 47, 505, 45], [501, 48, 505, 46, "resolve"], [501, 55, 505, 53], [501, 57, 505, 55], [501, 59, 505, 57], [501, 60, 505, 58], [501, 61, 505, 59], [502, 8, 506, 6], [503, 8, 507, 6], [503, 12, 507, 10, "photo"], [503, 17, 507, 15], [504, 8, 509, 6], [504, 12, 509, 10], [505, 10, 510, 8, "photo"], [505, 15, 510, 13], [505, 18, 510, 16], [505, 24, 510, 22, "cameraRef"], [505, 33, 510, 31], [505, 34, 510, 32, "current"], [505, 41, 510, 39], [505, 42, 510, 40, "takePictureAsync"], [505, 58, 510, 56], [505, 59, 510, 57], [506, 12, 511, 10, "quality"], [506, 19, 511, 17], [506, 21, 511, 19], [506, 24, 511, 22], [507, 12, 512, 10, "base64"], [507, 18, 512, 16], [507, 20, 512, 18], [507, 25, 512, 23], [508, 12, 513, 10, "skipProcessing"], [508, 26, 513, 24], [508, 28, 513, 26], [508, 32, 513, 30], [508, 33, 513, 32], [509, 10, 514, 8], [509, 11, 514, 9], [509, 12, 514, 10], [510, 8, 515, 6], [510, 9, 515, 7], [510, 10, 515, 8], [510, 17, 515, 15, "cameraError"], [510, 28, 515, 26], [510, 30, 515, 28], [511, 10, 516, 8, "console"], [511, 17, 516, 15], [511, 18, 516, 16, "log"], [511, 21, 516, 19], [511, 22, 516, 20], [511, 82, 516, 80], [511, 84, 516, 82, "cameraError"], [511, 95, 516, 93], [511, 96, 516, 94], [512, 10, 517, 8], [513, 10, 518, 8], [513, 14, 518, 12, "isDev"], [513, 19, 518, 17], [513, 21, 518, 19], [514, 12, 519, 10, "photo"], [514, 17, 519, 15], [514, 20, 519, 18], [515, 14, 520, 12, "uri"], [515, 17, 520, 15], [515, 19, 520, 17], [516, 12, 521, 10], [516, 13, 521, 11], [517, 10, 522, 8], [517, 11, 522, 9], [517, 17, 522, 15], [518, 12, 523, 10], [518, 18, 523, 16, "cameraError"], [518, 29, 523, 27], [519, 10, 524, 8], [520, 8, 525, 6], [521, 8, 526, 6], [521, 12, 526, 10], [521, 13, 526, 11, "photo"], [521, 18, 526, 16], [521, 20, 526, 18], [522, 10, 527, 8], [522, 16, 527, 14], [522, 20, 527, 18, "Error"], [522, 25, 527, 23], [522, 26, 527, 24], [522, 51, 527, 49], [522, 52, 527, 50], [523, 8, 528, 6], [524, 8, 529, 6, "console"], [524, 15, 529, 13], [524, 16, 529, 14, "log"], [524, 19, 529, 17], [524, 20, 529, 18], [524, 56, 529, 54], [524, 58, 529, 56, "photo"], [524, 63, 529, 61], [524, 64, 529, 62, "uri"], [524, 67, 529, 65], [524, 68, 529, 66], [525, 8, 530, 6, "setCapturedPhoto"], [525, 24, 530, 22], [525, 25, 530, 23, "photo"], [525, 30, 530, 28], [525, 31, 530, 29, "uri"], [525, 34, 530, 32], [525, 35, 530, 33], [526, 8, 531, 6, "setProcessingProgress"], [526, 29, 531, 27], [526, 30, 531, 28], [526, 32, 531, 30], [526, 33, 531, 31], [527, 8, 532, 6], [528, 8, 533, 6, "console"], [528, 15, 533, 13], [528, 16, 533, 14, "log"], [528, 19, 533, 17], [528, 20, 533, 18], [528, 73, 533, 71], [528, 74, 533, 72], [529, 8, 534, 6, "console"], [529, 15, 534, 13], [529, 16, 534, 14, "log"], [529, 19, 534, 17], [529, 20, 534, 18], [529, 96, 534, 94], [529, 98, 534, 96, "photo"], [529, 103, 534, 101], [529, 104, 534, 102, "uri"], [529, 107, 534, 105], [529, 108, 534, 106], [530, 8, 535, 6, "console"], [530, 15, 535, 13], [530, 16, 535, 14, "log"], [530, 19, 535, 17], [530, 20, 535, 18], [530, 68, 535, 66], [530, 70, 535, 68], [530, 77, 535, 75, "processImageWithFaceBlur"], [530, 101, 535, 99], [530, 102, 535, 100], [531, 8, 536, 6], [531, 14, 536, 12, "processImageWithFaceBlur"], [531, 38, 536, 36], [531, 39, 536, 37, "photo"], [531, 44, 536, 42], [531, 45, 536, 43, "uri"], [531, 48, 536, 46], [531, 49, 536, 47], [532, 8, 537, 6, "console"], [532, 15, 537, 13], [532, 16, 537, 14, "log"], [532, 19, 537, 17], [532, 20, 537, 18], [532, 71, 537, 69], [532, 72, 537, 70], [533, 6, 538, 4], [533, 7, 538, 5], [533, 8, 538, 6], [533, 15, 538, 13, "error"], [533, 20, 538, 18], [533, 22, 538, 20], [534, 8, 539, 6, "console"], [534, 15, 539, 13], [534, 16, 539, 14, "error"], [534, 21, 539, 19], [534, 22, 539, 20], [534, 54, 539, 52], [534, 56, 539, 54, "error"], [534, 61, 539, 59], [534, 62, 539, 60], [535, 8, 540, 6, "setErrorMessage"], [535, 23, 540, 21], [535, 24, 540, 22], [535, 68, 540, 66], [535, 69, 540, 67], [536, 8, 541, 6, "setProcessingState"], [536, 26, 541, 24], [536, 27, 541, 25], [536, 34, 541, 32], [536, 35, 541, 33], [537, 6, 542, 4], [538, 4, 543, 2], [538, 5, 543, 3], [538, 7, 543, 5], [538, 9, 543, 7], [538, 10, 543, 8], [539, 4, 544, 2], [540, 4, 545, 2], [540, 10, 545, 8, "processImageWithFaceBlur"], [540, 34, 545, 32], [540, 37, 545, 35], [540, 43, 545, 42, "photoUri"], [540, 51, 545, 58], [540, 55, 545, 63], [541, 6, 546, 4, "console"], [541, 13, 546, 11], [541, 14, 546, 12, "log"], [541, 17, 546, 15], [541, 18, 546, 16], [541, 85, 546, 83], [541, 86, 546, 84], [542, 6, 547, 4], [542, 10, 547, 8], [543, 8, 548, 6, "console"], [543, 15, 548, 13], [543, 16, 548, 14, "log"], [543, 19, 548, 17], [543, 20, 548, 18], [543, 84, 548, 82], [543, 85, 548, 83], [544, 8, 549, 6, "setProcessingState"], [544, 26, 549, 24], [544, 27, 549, 25], [544, 39, 549, 37], [544, 40, 549, 38], [545, 8, 550, 6, "setProcessingProgress"], [545, 29, 550, 27], [545, 30, 550, 28], [545, 32, 550, 30], [545, 33, 550, 31], [547, 8, 552, 6], [548, 8, 553, 6], [548, 14, 553, 12, "canvas"], [548, 20, 553, 18], [548, 23, 553, 21, "document"], [548, 31, 553, 29], [548, 32, 553, 30, "createElement"], [548, 45, 553, 43], [548, 46, 553, 44], [548, 54, 553, 52], [548, 55, 553, 53], [549, 8, 554, 6], [549, 14, 554, 12, "ctx"], [549, 17, 554, 15], [549, 20, 554, 18, "canvas"], [549, 26, 554, 24], [549, 27, 554, 25, "getContext"], [549, 37, 554, 35], [549, 38, 554, 36], [549, 42, 554, 40], [549, 43, 554, 41], [550, 8, 555, 6], [550, 12, 555, 10], [550, 13, 555, 11, "ctx"], [550, 16, 555, 14], [550, 18, 555, 16], [550, 24, 555, 22], [550, 28, 555, 26, "Error"], [550, 33, 555, 31], [550, 34, 555, 32], [550, 64, 555, 62], [550, 65, 555, 63], [552, 8, 557, 6], [553, 8, 558, 6], [553, 14, 558, 12, "img"], [553, 17, 558, 15], [553, 20, 558, 18], [553, 24, 558, 22, "Image"], [553, 29, 558, 27], [553, 30, 558, 28], [553, 31, 558, 29], [554, 8, 559, 6], [554, 14, 559, 12], [554, 18, 559, 16, "Promise"], [554, 25, 559, 23], [554, 26, 559, 24], [554, 27, 559, 25, "resolve"], [554, 34, 559, 32], [554, 36, 559, 34, "reject"], [554, 42, 559, 40], [554, 47, 559, 45], [555, 10, 560, 8, "img"], [555, 13, 560, 11], [555, 14, 560, 12, "onload"], [555, 20, 560, 18], [555, 23, 560, 21, "resolve"], [555, 30, 560, 28], [556, 10, 561, 8, "img"], [556, 13, 561, 11], [556, 14, 561, 12, "onerror"], [556, 21, 561, 19], [556, 24, 561, 22, "reject"], [556, 30, 561, 28], [557, 10, 562, 8, "img"], [557, 13, 562, 11], [557, 14, 562, 12, "src"], [557, 17, 562, 15], [557, 20, 562, 18, "photoUri"], [557, 28, 562, 26], [558, 8, 563, 6], [558, 9, 563, 7], [558, 10, 563, 8], [560, 8, 565, 6], [561, 8, 566, 6, "canvas"], [561, 14, 566, 12], [561, 15, 566, 13, "width"], [561, 20, 566, 18], [561, 23, 566, 21, "img"], [561, 26, 566, 24], [561, 27, 566, 25, "width"], [561, 32, 566, 30], [562, 8, 567, 6, "canvas"], [562, 14, 567, 12], [562, 15, 567, 13, "height"], [562, 21, 567, 19], [562, 24, 567, 22, "img"], [562, 27, 567, 25], [562, 28, 567, 26, "height"], [562, 34, 567, 32], [563, 8, 568, 6, "console"], [563, 15, 568, 13], [563, 16, 568, 14, "log"], [563, 19, 568, 17], [563, 20, 568, 18], [563, 54, 568, 52], [563, 56, 568, 54], [564, 10, 568, 56, "width"], [564, 15, 568, 61], [564, 17, 568, 63, "img"], [564, 20, 568, 66], [564, 21, 568, 67, "width"], [564, 26, 568, 72], [565, 10, 568, 74, "height"], [565, 16, 568, 80], [565, 18, 568, 82, "img"], [565, 21, 568, 85], [565, 22, 568, 86, "height"], [566, 8, 568, 93], [566, 9, 568, 94], [566, 10, 568, 95], [568, 8, 570, 6], [569, 8, 571, 6, "ctx"], [569, 11, 571, 9], [569, 12, 571, 10, "drawImage"], [569, 21, 571, 19], [569, 22, 571, 20, "img"], [569, 25, 571, 23], [569, 27, 571, 25], [569, 28, 571, 26], [569, 30, 571, 28], [569, 31, 571, 29], [569, 32, 571, 30], [570, 8, 572, 6, "console"], [570, 15, 572, 13], [570, 16, 572, 14, "log"], [570, 19, 572, 17], [570, 20, 572, 18], [570, 72, 572, 70], [570, 73, 572, 71], [571, 8, 574, 6, "setProcessingProgress"], [571, 29, 574, 27], [571, 30, 574, 28], [571, 32, 574, 30], [571, 33, 574, 31], [573, 8, 576, 6], [574, 8, 577, 6], [574, 12, 577, 10, "detectedFaces"], [574, 25, 577, 23], [574, 28, 577, 26], [574, 30, 577, 28], [575, 8, 579, 6, "console"], [575, 15, 579, 13], [575, 16, 579, 14, "log"], [575, 19, 579, 17], [575, 20, 579, 18], [575, 81, 579, 79], [575, 82, 579, 80], [577, 8, 581, 6], [578, 8, 582, 6], [578, 12, 582, 10], [579, 10, 583, 8, "console"], [579, 17, 583, 15], [579, 18, 583, 16, "log"], [579, 21, 583, 19], [579, 22, 583, 20], [579, 81, 583, 79], [579, 82, 583, 80], [580, 10, 584, 8], [580, 16, 584, 14, "loadTensorFlowFaceDetection"], [580, 43, 584, 41], [580, 44, 584, 42], [580, 45, 584, 43], [581, 10, 585, 8, "console"], [581, 17, 585, 15], [581, 18, 585, 16, "log"], [581, 21, 585, 19], [581, 22, 585, 20], [581, 90, 585, 88], [581, 91, 585, 89], [582, 10, 586, 8, "detectedFaces"], [582, 23, 586, 21], [582, 26, 586, 24], [582, 32, 586, 30, "detectFacesWithTensorFlow"], [582, 57, 586, 55], [582, 58, 586, 56, "img"], [582, 61, 586, 59], [582, 62, 586, 60], [583, 10, 587, 8, "console"], [583, 17, 587, 15], [583, 18, 587, 16, "log"], [583, 21, 587, 19], [583, 22, 587, 20], [583, 70, 587, 68, "detectedFaces"], [583, 83, 587, 81], [583, 84, 587, 82, "length"], [583, 90, 587, 88], [583, 98, 587, 96], [583, 99, 587, 97], [584, 8, 588, 6], [584, 9, 588, 7], [584, 10, 588, 8], [584, 17, 588, 15, "tensorFlowError"], [584, 32, 588, 30], [584, 34, 588, 32], [585, 10, 589, 8, "console"], [585, 17, 589, 15], [585, 18, 589, 16, "warn"], [585, 22, 589, 20], [585, 23, 589, 21], [585, 61, 589, 59], [585, 63, 589, 61, "tensorFlowError"], [585, 78, 589, 76], [585, 79, 589, 77], [586, 10, 590, 8, "console"], [586, 17, 590, 15], [586, 18, 590, 16, "warn"], [586, 22, 590, 20], [586, 23, 590, 21], [586, 68, 590, 66], [586, 70, 590, 68], [587, 12, 591, 10, "message"], [587, 19, 591, 17], [587, 21, 591, 19, "tensorFlowError"], [587, 36, 591, 34], [587, 37, 591, 35, "message"], [587, 44, 591, 42], [588, 12, 592, 10, "stack"], [588, 17, 592, 15], [588, 19, 592, 17, "tensorFlowError"], [588, 34, 592, 32], [588, 35, 592, 33, "stack"], [589, 10, 593, 8], [589, 11, 593, 9], [589, 12, 593, 10], [591, 10, 595, 8], [592, 10, 596, 8, "console"], [592, 17, 596, 15], [592, 18, 596, 16, "log"], [592, 21, 596, 19], [592, 22, 596, 20], [592, 86, 596, 84], [592, 87, 596, 85], [593, 10, 597, 8, "detectedFaces"], [593, 23, 597, 21], [593, 26, 597, 24, "detectFacesHeuristic"], [593, 46, 597, 44], [593, 47, 597, 45, "img"], [593, 50, 597, 48], [593, 52, 597, 50, "ctx"], [593, 55, 597, 53], [593, 56, 597, 54], [594, 10, 598, 8, "console"], [594, 17, 598, 15], [594, 18, 598, 16, "log"], [594, 21, 598, 19], [594, 22, 598, 20], [594, 70, 598, 68, "detectedFaces"], [594, 83, 598, 81], [594, 84, 598, 82, "length"], [594, 90, 598, 88], [594, 98, 598, 96], [594, 99, 598, 97], [595, 8, 599, 6], [597, 8, 601, 6], [598, 8, 602, 6], [598, 12, 602, 10, "detectedFaces"], [598, 25, 602, 23], [598, 26, 602, 24, "length"], [598, 32, 602, 30], [598, 37, 602, 35], [598, 38, 602, 36], [598, 40, 602, 38], [599, 10, 603, 8, "console"], [599, 17, 603, 15], [599, 18, 603, 16, "log"], [599, 21, 603, 19], [599, 22, 603, 20], [599, 89, 603, 87], [599, 90, 603, 88], [600, 10, 604, 8, "detectedFaces"], [600, 23, 604, 21], [600, 26, 604, 24, "detectFacesAggressive"], [600, 47, 604, 45], [600, 48, 604, 46, "img"], [600, 51, 604, 49], [600, 53, 604, 51, "ctx"], [600, 56, 604, 54], [600, 57, 604, 55], [601, 10, 605, 8, "console"], [601, 17, 605, 15], [601, 18, 605, 16, "log"], [601, 21, 605, 19], [601, 22, 605, 20], [601, 71, 605, 69, "detectedFaces"], [601, 84, 605, 82], [601, 85, 605, 83, "length"], [601, 91, 605, 89], [601, 99, 605, 97], [601, 100, 605, 98], [602, 8, 606, 6], [603, 8, 608, 6, "console"], [603, 15, 608, 13], [603, 16, 608, 14, "log"], [603, 19, 608, 17], [603, 20, 608, 18], [603, 72, 608, 70, "detectedFaces"], [603, 85, 608, 83], [603, 86, 608, 84, "length"], [603, 92, 608, 90], [603, 100, 608, 98], [603, 101, 608, 99], [604, 8, 609, 6], [604, 12, 609, 10, "detectedFaces"], [604, 25, 609, 23], [604, 26, 609, 24, "length"], [604, 32, 609, 30], [604, 35, 609, 33], [604, 36, 609, 34], [604, 38, 609, 36], [605, 10, 610, 8, "console"], [605, 17, 610, 15], [605, 18, 610, 16, "log"], [605, 21, 610, 19], [605, 22, 610, 20], [605, 66, 610, 64], [605, 68, 610, 66, "detectedFaces"], [605, 81, 610, 79], [605, 82, 610, 80, "map"], [605, 85, 610, 83], [605, 86, 610, 84], [605, 87, 610, 85, "face"], [605, 91, 610, 89], [605, 93, 610, 91, "i"], [605, 94, 610, 92], [605, 100, 610, 98], [606, 12, 611, 10, "faceNumber"], [606, 22, 611, 20], [606, 24, 611, 22, "i"], [606, 25, 611, 23], [606, 28, 611, 26], [606, 29, 611, 27], [607, 12, 612, 10, "centerX"], [607, 19, 612, 17], [607, 21, 612, 19, "face"], [607, 25, 612, 23], [607, 26, 612, 24, "boundingBox"], [607, 37, 612, 35], [607, 38, 612, 36, "xCenter"], [607, 45, 612, 43], [608, 12, 613, 10, "centerY"], [608, 19, 613, 17], [608, 21, 613, 19, "face"], [608, 25, 613, 23], [608, 26, 613, 24, "boundingBox"], [608, 37, 613, 35], [608, 38, 613, 36, "yCenter"], [608, 45, 613, 43], [609, 12, 614, 10, "width"], [609, 17, 614, 15], [609, 19, 614, 17, "face"], [609, 23, 614, 21], [609, 24, 614, 22, "boundingBox"], [609, 35, 614, 33], [609, 36, 614, 34, "width"], [609, 41, 614, 39], [610, 12, 615, 10, "height"], [610, 18, 615, 16], [610, 20, 615, 18, "face"], [610, 24, 615, 22], [610, 25, 615, 23, "boundingBox"], [610, 36, 615, 34], [610, 37, 615, 35, "height"], [611, 10, 616, 8], [611, 11, 616, 9], [611, 12, 616, 10], [611, 13, 616, 11], [611, 14, 616, 12], [612, 8, 617, 6], [612, 9, 617, 7], [612, 15, 617, 13], [613, 10, 618, 8, "console"], [613, 17, 618, 15], [613, 18, 618, 16, "log"], [613, 21, 618, 19], [613, 22, 618, 20], [613, 74, 618, 72], [613, 75, 618, 73], [614, 10, 619, 8, "console"], [614, 17, 619, 15], [614, 18, 619, 16, "log"], [614, 21, 619, 19], [614, 22, 619, 20], [614, 95, 619, 93], [614, 96, 619, 94], [616, 10, 621, 8], [617, 10, 622, 8], [617, 16, 622, 14, "centerX"], [617, 23, 622, 21], [617, 26, 622, 24, "img"], [617, 29, 622, 27], [617, 30, 622, 28, "width"], [617, 35, 622, 33], [617, 38, 622, 36], [617, 41, 622, 39], [618, 10, 623, 8], [618, 16, 623, 14, "centerY"], [618, 23, 623, 21], [618, 26, 623, 24, "img"], [618, 29, 623, 27], [618, 30, 623, 28, "height"], [618, 36, 623, 34], [618, 39, 623, 37], [618, 42, 623, 40], [619, 10, 624, 8], [619, 16, 624, 14, "centerWidth"], [619, 27, 624, 25], [619, 30, 624, 28, "img"], [619, 33, 624, 31], [619, 34, 624, 32, "width"], [619, 39, 624, 37], [619, 42, 624, 40], [619, 45, 624, 43], [620, 10, 625, 8], [620, 16, 625, 14, "centerHeight"], [620, 28, 625, 26], [620, 31, 625, 29, "img"], [620, 34, 625, 32], [620, 35, 625, 33, "height"], [620, 41, 625, 39], [620, 44, 625, 42], [620, 47, 625, 45], [621, 10, 627, 8, "detectedFaces"], [621, 23, 627, 21], [621, 26, 627, 24], [621, 27, 627, 25], [622, 12, 628, 10, "boundingBox"], [622, 23, 628, 21], [622, 25, 628, 23], [623, 14, 629, 12, "xCenter"], [623, 21, 629, 19], [623, 23, 629, 21], [623, 26, 629, 24], [624, 14, 630, 12, "yCenter"], [624, 21, 630, 19], [624, 23, 630, 21], [624, 26, 630, 24], [625, 14, 631, 12, "width"], [625, 19, 631, 17], [625, 21, 631, 19], [625, 24, 631, 22], [626, 14, 632, 12, "height"], [626, 20, 632, 18], [626, 22, 632, 20], [627, 12, 633, 10], [627, 13, 633, 11], [628, 12, 634, 10, "confidence"], [628, 22, 634, 20], [628, 24, 634, 22], [629, 10, 635, 8], [629, 11, 635, 9], [629, 12, 635, 10], [630, 10, 637, 8, "console"], [630, 17, 637, 15], [630, 18, 637, 16, "log"], [630, 21, 637, 19], [630, 22, 637, 20], [630, 100, 637, 98], [630, 101, 637, 99], [631, 8, 638, 6], [632, 8, 640, 6, "setProcessingProgress"], [632, 29, 640, 27], [632, 30, 640, 28], [632, 32, 640, 30], [632, 33, 640, 31], [634, 8, 642, 6], [635, 8, 643, 6], [635, 12, 643, 10, "detectedFaces"], [635, 25, 643, 23], [635, 26, 643, 24, "length"], [635, 32, 643, 30], [635, 35, 643, 33], [635, 36, 643, 34], [635, 38, 643, 36], [636, 10, 644, 8, "console"], [636, 17, 644, 15], [636, 18, 644, 16, "log"], [636, 21, 644, 19], [636, 22, 644, 20], [636, 61, 644, 59, "detectedFaces"], [636, 74, 644, 72], [636, 75, 644, 73, "length"], [636, 81, 644, 79], [636, 101, 644, 99], [636, 102, 644, 100], [637, 10, 646, 8, "detectedFaces"], [637, 23, 646, 21], [637, 24, 646, 22, "for<PERSON>ach"], [637, 31, 646, 29], [637, 32, 646, 30], [637, 33, 646, 31, "detection"], [637, 42, 646, 40], [637, 44, 646, 42, "index"], [637, 49, 646, 47], [637, 54, 646, 52], [638, 12, 647, 10], [638, 18, 647, 16, "bbox"], [638, 22, 647, 20], [638, 25, 647, 23, "detection"], [638, 34, 647, 32], [638, 35, 647, 33, "boundingBox"], [638, 46, 647, 44], [639, 12, 649, 10, "console"], [639, 19, 649, 17], [639, 20, 649, 18, "log"], [639, 23, 649, 21], [639, 24, 649, 22], [639, 85, 649, 83, "index"], [639, 90, 649, 88], [639, 93, 649, 91], [639, 94, 649, 92], [639, 97, 649, 95], [639, 99, 649, 97], [640, 14, 650, 12, "bbox"], [640, 18, 650, 16], [641, 14, 651, 12, "imageSize"], [641, 23, 651, 21], [641, 25, 651, 23], [642, 16, 651, 25, "width"], [642, 21, 651, 30], [642, 23, 651, 32, "img"], [642, 26, 651, 35], [642, 27, 651, 36, "width"], [642, 32, 651, 41], [643, 16, 651, 43, "height"], [643, 22, 651, 49], [643, 24, 651, 51, "img"], [643, 27, 651, 54], [643, 28, 651, 55, "height"], [644, 14, 651, 62], [645, 12, 652, 10], [645, 13, 652, 11], [645, 14, 652, 12], [647, 12, 654, 10], [648, 12, 655, 10], [648, 18, 655, 16, "faceX"], [648, 23, 655, 21], [648, 26, 655, 24, "bbox"], [648, 30, 655, 28], [648, 31, 655, 29, "xCenter"], [648, 38, 655, 36], [648, 41, 655, 39, "img"], [648, 44, 655, 42], [648, 45, 655, 43, "width"], [648, 50, 655, 48], [648, 53, 655, 52, "bbox"], [648, 57, 655, 56], [648, 58, 655, 57, "width"], [648, 63, 655, 62], [648, 66, 655, 65, "img"], [648, 69, 655, 68], [648, 70, 655, 69, "width"], [648, 75, 655, 74], [648, 78, 655, 78], [648, 79, 655, 79], [649, 12, 656, 10], [649, 18, 656, 16, "faceY"], [649, 23, 656, 21], [649, 26, 656, 24, "bbox"], [649, 30, 656, 28], [649, 31, 656, 29, "yCenter"], [649, 38, 656, 36], [649, 41, 656, 39, "img"], [649, 44, 656, 42], [649, 45, 656, 43, "height"], [649, 51, 656, 49], [649, 54, 656, 53, "bbox"], [649, 58, 656, 57], [649, 59, 656, 58, "height"], [649, 65, 656, 64], [649, 68, 656, 67, "img"], [649, 71, 656, 70], [649, 72, 656, 71, "height"], [649, 78, 656, 77], [649, 81, 656, 81], [649, 82, 656, 82], [650, 12, 657, 10], [650, 18, 657, 16, "faceWidth"], [650, 27, 657, 25], [650, 30, 657, 28, "bbox"], [650, 34, 657, 32], [650, 35, 657, 33, "width"], [650, 40, 657, 38], [650, 43, 657, 41, "img"], [650, 46, 657, 44], [650, 47, 657, 45, "width"], [650, 52, 657, 50], [651, 12, 658, 10], [651, 18, 658, 16, "faceHeight"], [651, 28, 658, 26], [651, 31, 658, 29, "bbox"], [651, 35, 658, 33], [651, 36, 658, 34, "height"], [651, 42, 658, 40], [651, 45, 658, 43, "img"], [651, 48, 658, 46], [651, 49, 658, 47, "height"], [651, 55, 658, 53], [652, 12, 660, 10, "console"], [652, 19, 660, 17], [652, 20, 660, 18, "log"], [652, 23, 660, 21], [652, 24, 660, 22], [652, 84, 660, 82], [652, 86, 660, 84], [653, 14, 661, 12, "faceX"], [653, 19, 661, 17], [654, 14, 661, 19, "faceY"], [654, 19, 661, 24], [655, 14, 661, 26, "faceWidth"], [655, 23, 661, 35], [656, 14, 661, 37, "faceHeight"], [656, 24, 661, 47], [657, 14, 662, 12, "<PERSON><PERSON><PERSON><PERSON>"], [657, 21, 662, 19], [657, 23, 662, 21, "faceX"], [657, 28, 662, 26], [657, 32, 662, 30], [657, 33, 662, 31], [657, 37, 662, 35, "faceY"], [657, 42, 662, 40], [657, 46, 662, 44], [657, 47, 662, 45], [657, 51, 662, 49, "faceWidth"], [657, 60, 662, 58], [657, 63, 662, 61], [657, 64, 662, 62], [657, 68, 662, 66, "faceHeight"], [657, 78, 662, 76], [657, 81, 662, 79], [658, 12, 663, 10], [658, 13, 663, 11], [658, 14, 663, 12], [660, 12, 665, 10], [661, 12, 666, 10], [661, 18, 666, 16, "padding"], [661, 25, 666, 23], [661, 28, 666, 26], [661, 31, 666, 29], [662, 12, 667, 10], [662, 18, 667, 16, "paddedX"], [662, 25, 667, 23], [662, 28, 667, 26, "Math"], [662, 32, 667, 30], [662, 33, 667, 31, "max"], [662, 36, 667, 34], [662, 37, 667, 35], [662, 38, 667, 36], [662, 40, 667, 38, "faceX"], [662, 45, 667, 43], [662, 48, 667, 46, "faceWidth"], [662, 57, 667, 55], [662, 60, 667, 58, "padding"], [662, 67, 667, 65], [662, 68, 667, 66], [663, 12, 668, 10], [663, 18, 668, 16, "paddedY"], [663, 25, 668, 23], [663, 28, 668, 26, "Math"], [663, 32, 668, 30], [663, 33, 668, 31, "max"], [663, 36, 668, 34], [663, 37, 668, 35], [663, 38, 668, 36], [663, 40, 668, 38, "faceY"], [663, 45, 668, 43], [663, 48, 668, 46, "faceHeight"], [663, 58, 668, 56], [663, 61, 668, 59, "padding"], [663, 68, 668, 66], [663, 69, 668, 67], [664, 12, 669, 10], [664, 18, 669, 16, "<PERSON><PERSON><PERSON><PERSON>"], [664, 29, 669, 27], [664, 32, 669, 30, "Math"], [664, 36, 669, 34], [664, 37, 669, 35, "min"], [664, 40, 669, 38], [664, 41, 669, 39, "img"], [664, 44, 669, 42], [664, 45, 669, 43, "width"], [664, 50, 669, 48], [664, 53, 669, 51, "paddedX"], [664, 60, 669, 58], [664, 62, 669, 60, "faceWidth"], [664, 71, 669, 69], [664, 75, 669, 73], [664, 76, 669, 74], [664, 79, 669, 77], [664, 80, 669, 78], [664, 83, 669, 81, "padding"], [664, 90, 669, 88], [664, 91, 669, 89], [664, 92, 669, 90], [665, 12, 670, 10], [665, 18, 670, 16, "paddedHeight"], [665, 30, 670, 28], [665, 33, 670, 31, "Math"], [665, 37, 670, 35], [665, 38, 670, 36, "min"], [665, 41, 670, 39], [665, 42, 670, 40, "img"], [665, 45, 670, 43], [665, 46, 670, 44, "height"], [665, 52, 670, 50], [665, 55, 670, 53, "paddedY"], [665, 62, 670, 60], [665, 64, 670, 62, "faceHeight"], [665, 74, 670, 72], [665, 78, 670, 76], [665, 79, 670, 77], [665, 82, 670, 80], [665, 83, 670, 81], [665, 86, 670, 84, "padding"], [665, 93, 670, 91], [665, 94, 670, 92], [665, 95, 670, 93], [666, 12, 672, 10, "console"], [666, 19, 672, 17], [666, 20, 672, 18, "log"], [666, 23, 672, 21], [666, 24, 672, 22], [666, 60, 672, 58, "index"], [666, 65, 672, 63], [666, 68, 672, 66], [666, 69, 672, 67], [666, 72, 672, 70], [666, 74, 672, 72], [667, 14, 673, 12, "original"], [667, 22, 673, 20], [667, 24, 673, 22], [668, 16, 673, 24, "x"], [668, 17, 673, 25], [668, 19, 673, 27, "Math"], [668, 23, 673, 31], [668, 24, 673, 32, "round"], [668, 29, 673, 37], [668, 30, 673, 38, "faceX"], [668, 35, 673, 43], [668, 36, 673, 44], [669, 16, 673, 46, "y"], [669, 17, 673, 47], [669, 19, 673, 49, "Math"], [669, 23, 673, 53], [669, 24, 673, 54, "round"], [669, 29, 673, 59], [669, 30, 673, 60, "faceY"], [669, 35, 673, 65], [669, 36, 673, 66], [670, 16, 673, 68, "w"], [670, 17, 673, 69], [670, 19, 673, 71, "Math"], [670, 23, 673, 75], [670, 24, 673, 76, "round"], [670, 29, 673, 81], [670, 30, 673, 82, "faceWidth"], [670, 39, 673, 91], [670, 40, 673, 92], [671, 16, 673, 94, "h"], [671, 17, 673, 95], [671, 19, 673, 97, "Math"], [671, 23, 673, 101], [671, 24, 673, 102, "round"], [671, 29, 673, 107], [671, 30, 673, 108, "faceHeight"], [671, 40, 673, 118], [672, 14, 673, 120], [672, 15, 673, 121], [673, 14, 674, 12, "padded"], [673, 20, 674, 18], [673, 22, 674, 20], [674, 16, 674, 22, "x"], [674, 17, 674, 23], [674, 19, 674, 25, "Math"], [674, 23, 674, 29], [674, 24, 674, 30, "round"], [674, 29, 674, 35], [674, 30, 674, 36, "paddedX"], [674, 37, 674, 43], [674, 38, 674, 44], [675, 16, 674, 46, "y"], [675, 17, 674, 47], [675, 19, 674, 49, "Math"], [675, 23, 674, 53], [675, 24, 674, 54, "round"], [675, 29, 674, 59], [675, 30, 674, 60, "paddedY"], [675, 37, 674, 67], [675, 38, 674, 68], [676, 16, 674, 70, "w"], [676, 17, 674, 71], [676, 19, 674, 73, "Math"], [676, 23, 674, 77], [676, 24, 674, 78, "round"], [676, 29, 674, 83], [676, 30, 674, 84, "<PERSON><PERSON><PERSON><PERSON>"], [676, 41, 674, 95], [676, 42, 674, 96], [677, 16, 674, 98, "h"], [677, 17, 674, 99], [677, 19, 674, 101, "Math"], [677, 23, 674, 105], [677, 24, 674, 106, "round"], [677, 29, 674, 111], [677, 30, 674, 112, "paddedHeight"], [677, 42, 674, 124], [678, 14, 674, 126], [679, 12, 675, 10], [679, 13, 675, 11], [679, 14, 675, 12], [681, 12, 677, 10], [682, 12, 678, 10, "console"], [682, 19, 678, 17], [682, 20, 678, 18, "log"], [682, 23, 678, 21], [682, 24, 678, 22], [682, 70, 678, 68], [682, 72, 678, 70], [683, 14, 679, 12, "width"], [683, 19, 679, 17], [683, 21, 679, 19, "canvas"], [683, 27, 679, 25], [683, 28, 679, 26, "width"], [683, 33, 679, 31], [684, 14, 680, 12, "height"], [684, 20, 680, 18], [684, 22, 680, 20, "canvas"], [684, 28, 680, 26], [684, 29, 680, 27, "height"], [684, 35, 680, 33], [685, 14, 681, 12, "contextValid"], [685, 26, 681, 24], [685, 28, 681, 26], [685, 29, 681, 27], [685, 30, 681, 28, "ctx"], [686, 12, 682, 10], [686, 13, 682, 11], [686, 14, 682, 12], [688, 12, 684, 10], [689, 12, 685, 10, "applyStrongBlur"], [689, 27, 685, 25], [689, 28, 685, 26, "ctx"], [689, 31, 685, 29], [689, 33, 685, 31, "paddedX"], [689, 40, 685, 38], [689, 42, 685, 40, "paddedY"], [689, 49, 685, 47], [689, 51, 685, 49, "<PERSON><PERSON><PERSON><PERSON>"], [689, 62, 685, 60], [689, 64, 685, 62, "paddedHeight"], [689, 76, 685, 74], [689, 77, 685, 75], [691, 12, 687, 10], [692, 12, 688, 10, "console"], [692, 19, 688, 17], [692, 20, 688, 18, "log"], [692, 23, 688, 21], [692, 24, 688, 22], [692, 102, 688, 100], [692, 103, 688, 101], [694, 12, 690, 10], [695, 12, 691, 10], [695, 18, 691, 16, "testImageData"], [695, 31, 691, 29], [695, 34, 691, 32, "ctx"], [695, 37, 691, 35], [695, 38, 691, 36, "getImageData"], [695, 50, 691, 48], [695, 51, 691, 49, "paddedX"], [695, 58, 691, 56], [695, 61, 691, 59], [695, 63, 691, 61], [695, 65, 691, 63, "paddedY"], [695, 72, 691, 70], [695, 75, 691, 73], [695, 77, 691, 75], [695, 79, 691, 77], [695, 81, 691, 79], [695, 83, 691, 81], [695, 85, 691, 83], [695, 86, 691, 84], [696, 12, 692, 10, "console"], [696, 19, 692, 17], [696, 20, 692, 18, "log"], [696, 23, 692, 21], [696, 24, 692, 22], [696, 70, 692, 68], [696, 72, 692, 70], [697, 14, 693, 12, "firstPixel"], [697, 24, 693, 22], [697, 26, 693, 24], [697, 27, 693, 25, "testImageData"], [697, 40, 693, 38], [697, 41, 693, 39, "data"], [697, 45, 693, 43], [697, 46, 693, 44], [697, 47, 693, 45], [697, 48, 693, 46], [697, 50, 693, 48, "testImageData"], [697, 63, 693, 61], [697, 64, 693, 62, "data"], [697, 68, 693, 66], [697, 69, 693, 67], [697, 70, 693, 68], [697, 71, 693, 69], [697, 73, 693, 71, "testImageData"], [697, 86, 693, 84], [697, 87, 693, 85, "data"], [697, 91, 693, 89], [697, 92, 693, 90], [697, 93, 693, 91], [697, 94, 693, 92], [697, 95, 693, 93], [698, 14, 694, 12, "secondPixel"], [698, 25, 694, 23], [698, 27, 694, 25], [698, 28, 694, 26, "testImageData"], [698, 41, 694, 39], [698, 42, 694, 40, "data"], [698, 46, 694, 44], [698, 47, 694, 45], [698, 48, 694, 46], [698, 49, 694, 47], [698, 51, 694, 49, "testImageData"], [698, 64, 694, 62], [698, 65, 694, 63, "data"], [698, 69, 694, 67], [698, 70, 694, 68], [698, 71, 694, 69], [698, 72, 694, 70], [698, 74, 694, 72, "testImageData"], [698, 87, 694, 85], [698, 88, 694, 86, "data"], [698, 92, 694, 90], [698, 93, 694, 91], [698, 94, 694, 92], [698, 95, 694, 93], [699, 12, 695, 10], [699, 13, 695, 11], [699, 14, 695, 12], [700, 12, 697, 10, "console"], [700, 19, 697, 17], [700, 20, 697, 18, "log"], [700, 23, 697, 21], [700, 24, 697, 22], [700, 50, 697, 48, "index"], [700, 55, 697, 53], [700, 58, 697, 56], [700, 59, 697, 57], [700, 79, 697, 77], [700, 80, 697, 78], [701, 10, 698, 8], [701, 11, 698, 9], [701, 12, 698, 10], [702, 10, 700, 8, "console"], [702, 17, 700, 15], [702, 18, 700, 16, "log"], [702, 21, 700, 19], [702, 22, 700, 20], [702, 48, 700, 46, "detectedFaces"], [702, 61, 700, 59], [702, 62, 700, 60, "length"], [702, 68, 700, 66], [702, 104, 700, 102], [702, 105, 700, 103], [703, 8, 701, 6], [703, 9, 701, 7], [703, 15, 701, 13], [704, 10, 702, 8, "console"], [704, 17, 702, 15], [704, 18, 702, 16, "log"], [704, 21, 702, 19], [704, 22, 702, 20], [704, 109, 702, 107], [704, 110, 702, 108], [705, 10, 703, 8], [706, 10, 704, 8, "applyFallbackFaceBlur"], [706, 31, 704, 29], [706, 32, 704, 30, "ctx"], [706, 35, 704, 33], [706, 37, 704, 35, "img"], [706, 40, 704, 38], [706, 41, 704, 39, "width"], [706, 46, 704, 44], [706, 48, 704, 46, "img"], [706, 51, 704, 49], [706, 52, 704, 50, "height"], [706, 58, 704, 56], [706, 59, 704, 57], [707, 8, 705, 6], [708, 8, 707, 6, "setProcessingProgress"], [708, 29, 707, 27], [708, 30, 707, 28], [708, 32, 707, 30], [708, 33, 707, 31], [710, 8, 709, 6], [711, 8, 710, 6, "console"], [711, 15, 710, 13], [711, 16, 710, 14, "log"], [711, 19, 710, 17], [711, 20, 710, 18], [711, 85, 710, 83], [711, 86, 710, 84], [712, 8, 711, 6], [712, 14, 711, 12, "blurredImageBlob"], [712, 30, 711, 28], [712, 33, 711, 31], [712, 39, 711, 37], [712, 43, 711, 41, "Promise"], [712, 50, 711, 48], [712, 51, 711, 56, "resolve"], [712, 58, 711, 63], [712, 62, 711, 68], [713, 10, 712, 8, "canvas"], [713, 16, 712, 14], [713, 17, 712, 15, "toBlob"], [713, 23, 712, 21], [713, 24, 712, 23, "blob"], [713, 28, 712, 27], [713, 32, 712, 32, "resolve"], [713, 39, 712, 39], [713, 40, 712, 40, "blob"], [713, 44, 712, 45], [713, 45, 712, 46], [713, 47, 712, 48], [713, 59, 712, 60], [713, 61, 712, 62], [713, 64, 712, 65], [713, 65, 712, 66], [714, 8, 713, 6], [714, 9, 713, 7], [714, 10, 713, 8], [715, 8, 715, 6], [715, 14, 715, 12, "blurredImageUrl"], [715, 29, 715, 27], [715, 32, 715, 30, "URL"], [715, 35, 715, 33], [715, 36, 715, 34, "createObjectURL"], [715, 51, 715, 49], [715, 52, 715, 50, "blurredImageBlob"], [715, 68, 715, 66], [715, 69, 715, 67], [716, 8, 716, 6, "console"], [716, 15, 716, 13], [716, 16, 716, 14, "log"], [716, 19, 716, 17], [716, 20, 716, 18], [716, 66, 716, 64], [716, 68, 716, 66, "blurredImageUrl"], [716, 83, 716, 81], [716, 84, 716, 82, "substring"], [716, 93, 716, 91], [716, 94, 716, 92], [716, 95, 716, 93], [716, 97, 716, 95], [716, 99, 716, 97], [716, 100, 716, 98], [716, 103, 716, 101], [716, 108, 716, 106], [716, 109, 716, 107], [718, 8, 718, 6], [719, 8, 719, 6, "setCapturedPhoto"], [719, 24, 719, 22], [719, 25, 719, 23, "blurredImageUrl"], [719, 40, 719, 38], [719, 41, 719, 39], [720, 8, 720, 6, "console"], [720, 15, 720, 13], [720, 16, 720, 14, "log"], [720, 19, 720, 17], [720, 20, 720, 18], [720, 87, 720, 85], [720, 88, 720, 86], [721, 8, 722, 6, "setProcessingProgress"], [721, 29, 722, 27], [721, 30, 722, 28], [721, 33, 722, 31], [721, 34, 722, 32], [723, 8, 724, 6], [724, 8, 725, 6], [724, 14, 725, 12, "completeProcessing"], [724, 32, 725, 30], [724, 33, 725, 31, "blurredImageUrl"], [724, 48, 725, 46], [724, 49, 725, 47], [725, 6, 727, 4], [725, 7, 727, 5], [725, 8, 727, 6], [725, 15, 727, 13, "error"], [725, 20, 727, 18], [725, 22, 727, 20], [726, 8, 728, 6, "console"], [726, 15, 728, 13], [726, 16, 728, 14, "error"], [726, 21, 728, 19], [726, 22, 728, 20], [726, 86, 728, 84], [726, 88, 728, 86, "error"], [726, 93, 728, 91], [726, 94, 728, 92], [727, 8, 729, 6, "console"], [727, 15, 729, 13], [727, 16, 729, 14, "error"], [727, 21, 729, 19], [727, 22, 729, 20], [727, 55, 729, 53], [727, 57, 729, 55, "error"], [727, 62, 729, 60], [727, 63, 729, 61, "stack"], [727, 68, 729, 66], [727, 69, 729, 67], [728, 8, 730, 6, "console"], [728, 15, 730, 13], [728, 16, 730, 14, "error"], [728, 21, 730, 19], [728, 22, 730, 20], [728, 57, 730, 55], [728, 59, 730, 57], [729, 10, 731, 8, "name"], [729, 14, 731, 12], [729, 16, 731, 14, "error"], [729, 21, 731, 19], [729, 22, 731, 20, "name"], [729, 26, 731, 24], [730, 10, 732, 8, "message"], [730, 17, 732, 15], [730, 19, 732, 17, "error"], [730, 24, 732, 22], [730, 25, 732, 23, "message"], [730, 32, 732, 30], [731, 10, 733, 8, "photoUri"], [732, 8, 734, 6], [732, 9, 734, 7], [732, 10, 734, 8], [733, 8, 735, 6, "setErrorMessage"], [733, 23, 735, 21], [733, 24, 735, 22], [733, 50, 735, 48], [733, 51, 735, 49], [734, 8, 736, 6, "setProcessingState"], [734, 26, 736, 24], [734, 27, 736, 25], [734, 34, 736, 32], [734, 35, 736, 33], [735, 6, 737, 4], [736, 4, 738, 2], [736, 5, 738, 3], [738, 4, 740, 2], [739, 4, 741, 2], [739, 10, 741, 8, "completeProcessing"], [739, 28, 741, 26], [739, 31, 741, 29], [739, 37, 741, 36, "blurredImageUrl"], [739, 52, 741, 59], [739, 56, 741, 64], [740, 6, 742, 4], [740, 10, 742, 8], [741, 8, 743, 6, "setProcessingState"], [741, 26, 743, 24], [741, 27, 743, 25], [741, 37, 743, 35], [741, 38, 743, 36], [743, 8, 745, 6], [744, 8, 746, 6], [744, 14, 746, 12, "timestamp"], [744, 23, 746, 21], [744, 26, 746, 24, "Date"], [744, 30, 746, 28], [744, 31, 746, 29, "now"], [744, 34, 746, 32], [744, 35, 746, 33], [744, 36, 746, 34], [745, 8, 747, 6], [745, 14, 747, 12, "result"], [745, 20, 747, 18], [745, 23, 747, 21], [746, 10, 748, 8, "imageUrl"], [746, 18, 748, 16], [746, 20, 748, 18, "blurredImageUrl"], [746, 35, 748, 33], [747, 10, 749, 8, "localUri"], [747, 18, 749, 16], [747, 20, 749, 18, "blurredImageUrl"], [747, 35, 749, 33], [748, 10, 750, 8, "challengeCode"], [748, 23, 750, 21], [748, 25, 750, 23, "challengeCode"], [748, 38, 750, 36], [748, 42, 750, 40], [748, 44, 750, 42], [749, 10, 751, 8, "timestamp"], [749, 19, 751, 17], [750, 10, 752, 8, "jobId"], [750, 15, 752, 13], [750, 17, 752, 15], [750, 27, 752, 25, "timestamp"], [750, 36, 752, 34], [750, 38, 752, 36], [751, 10, 753, 8, "status"], [751, 16, 753, 14], [751, 18, 753, 16], [752, 8, 754, 6], [752, 9, 754, 7], [753, 8, 756, 6, "console"], [753, 15, 756, 13], [753, 16, 756, 14, "log"], [753, 19, 756, 17], [753, 20, 756, 18], [753, 100, 756, 98], [753, 102, 756, 100], [754, 10, 757, 8, "imageUrl"], [754, 18, 757, 16], [754, 20, 757, 18, "blurredImageUrl"], [754, 35, 757, 33], [754, 36, 757, 34, "substring"], [754, 45, 757, 43], [754, 46, 757, 44], [754, 47, 757, 45], [754, 49, 757, 47], [754, 51, 757, 49], [754, 52, 757, 50], [754, 55, 757, 53], [754, 60, 757, 58], [755, 10, 758, 8, "timestamp"], [755, 19, 758, 17], [756, 10, 759, 8, "jobId"], [756, 15, 759, 13], [756, 17, 759, 15, "result"], [756, 23, 759, 21], [756, 24, 759, 22, "jobId"], [757, 8, 760, 6], [757, 9, 760, 7], [757, 10, 760, 8], [759, 8, 762, 6], [760, 8, 763, 6, "onComplete"], [760, 18, 763, 16], [760, 19, 763, 17, "result"], [760, 25, 763, 23], [760, 26, 763, 24], [761, 6, 765, 4], [761, 7, 765, 5], [761, 8, 765, 6], [761, 15, 765, 13, "error"], [761, 20, 765, 18], [761, 22, 765, 20], [762, 8, 766, 6, "console"], [762, 15, 766, 13], [762, 16, 766, 14, "error"], [762, 21, 766, 19], [762, 22, 766, 20], [762, 57, 766, 55], [762, 59, 766, 57, "error"], [762, 64, 766, 62], [762, 65, 766, 63], [763, 8, 767, 6, "setErrorMessage"], [763, 23, 767, 21], [763, 24, 767, 22], [763, 56, 767, 54], [763, 57, 767, 55], [764, 8, 768, 6, "setProcessingState"], [764, 26, 768, 24], [764, 27, 768, 25], [764, 34, 768, 32], [764, 35, 768, 33], [765, 6, 769, 4], [766, 4, 770, 2], [766, 5, 770, 3], [768, 4, 772, 2], [769, 4, 773, 2], [769, 10, 773, 8, "triggerServerProcessing"], [769, 33, 773, 31], [769, 36, 773, 34], [769, 42, 773, 34, "triggerServerProcessing"], [769, 43, 773, 41, "privateImageUrl"], [769, 58, 773, 64], [769, 60, 773, 66, "timestamp"], [769, 69, 773, 83], [769, 74, 773, 88], [770, 6, 774, 4], [770, 10, 774, 8], [771, 8, 775, 6, "console"], [771, 15, 775, 13], [771, 16, 775, 14, "log"], [771, 19, 775, 17], [771, 20, 775, 18], [771, 74, 775, 72], [771, 76, 775, 74, "privateImageUrl"], [771, 91, 775, 89], [771, 92, 775, 90], [772, 8, 776, 6, "setProcessingState"], [772, 26, 776, 24], [772, 27, 776, 25], [772, 39, 776, 37], [772, 40, 776, 38], [773, 8, 777, 6, "setProcessingProgress"], [773, 29, 777, 27], [773, 30, 777, 28], [773, 32, 777, 30], [773, 33, 777, 31], [774, 8, 779, 6], [774, 14, 779, 12, "requestBody"], [774, 25, 779, 23], [774, 28, 779, 26], [775, 10, 780, 8, "imageUrl"], [775, 18, 780, 16], [775, 20, 780, 18, "privateImageUrl"], [775, 35, 780, 33], [776, 10, 781, 8, "userId"], [776, 16, 781, 14], [777, 10, 782, 8, "requestId"], [777, 19, 782, 17], [778, 10, 783, 8, "timestamp"], [778, 19, 783, 17], [779, 10, 784, 8, "platform"], [779, 18, 784, 16], [779, 20, 784, 18], [780, 8, 785, 6], [780, 9, 785, 7], [781, 8, 787, 6, "console"], [781, 15, 787, 13], [781, 16, 787, 14, "log"], [781, 19, 787, 17], [781, 20, 787, 18], [781, 65, 787, 63], [781, 67, 787, 65, "requestBody"], [781, 78, 787, 76], [781, 79, 787, 77], [783, 8, 789, 6], [784, 8, 790, 6], [784, 14, 790, 12, "response"], [784, 22, 790, 20], [784, 25, 790, 23], [784, 31, 790, 29, "fetch"], [784, 36, 790, 34], [784, 37, 790, 35], [784, 40, 790, 38, "API_BASE_URL"], [784, 52, 790, 50], [784, 72, 790, 70], [784, 74, 790, 72], [785, 10, 791, 8, "method"], [785, 16, 791, 14], [785, 18, 791, 16], [785, 24, 791, 22], [786, 10, 792, 8, "headers"], [786, 17, 792, 15], [786, 19, 792, 17], [787, 12, 793, 10], [787, 26, 793, 24], [787, 28, 793, 26], [787, 46, 793, 44], [788, 12, 794, 10], [788, 27, 794, 25], [788, 29, 794, 27], [788, 39, 794, 37], [788, 45, 794, 43, "getAuthToken"], [788, 57, 794, 55], [788, 58, 794, 56], [788, 59, 794, 57], [789, 10, 795, 8], [789, 11, 795, 9], [790, 10, 796, 8, "body"], [790, 14, 796, 12], [790, 16, 796, 14, "JSON"], [790, 20, 796, 18], [790, 21, 796, 19, "stringify"], [790, 30, 796, 28], [790, 31, 796, 29, "requestBody"], [790, 42, 796, 40], [791, 8, 797, 6], [791, 9, 797, 7], [791, 10, 797, 8], [792, 8, 799, 6], [792, 12, 799, 10], [792, 13, 799, 11, "response"], [792, 21, 799, 19], [792, 22, 799, 20, "ok"], [792, 24, 799, 22], [792, 26, 799, 24], [793, 10, 800, 8], [793, 16, 800, 14, "errorText"], [793, 25, 800, 23], [793, 28, 800, 26], [793, 34, 800, 32, "response"], [793, 42, 800, 40], [793, 43, 800, 41, "text"], [793, 47, 800, 45], [793, 48, 800, 46], [793, 49, 800, 47], [794, 10, 801, 8, "console"], [794, 17, 801, 15], [794, 18, 801, 16, "error"], [794, 23, 801, 21], [794, 24, 801, 22], [794, 68, 801, 66], [794, 70, 801, 68, "response"], [794, 78, 801, 76], [794, 79, 801, 77, "status"], [794, 85, 801, 83], [794, 87, 801, 85, "errorText"], [794, 96, 801, 94], [794, 97, 801, 95], [795, 10, 802, 8], [795, 16, 802, 14], [795, 20, 802, 18, "Error"], [795, 25, 802, 23], [795, 26, 802, 24], [795, 48, 802, 46, "response"], [795, 56, 802, 54], [795, 57, 802, 55, "status"], [795, 63, 802, 61], [795, 67, 802, 65, "response"], [795, 75, 802, 73], [795, 76, 802, 74, "statusText"], [795, 86, 802, 84], [795, 88, 802, 86], [795, 89, 802, 87], [796, 8, 803, 6], [797, 8, 805, 6], [797, 14, 805, 12, "result"], [797, 20, 805, 18], [797, 23, 805, 21], [797, 29, 805, 27, "response"], [797, 37, 805, 35], [797, 38, 805, 36, "json"], [797, 42, 805, 40], [797, 43, 805, 41], [797, 44, 805, 42], [798, 8, 806, 6, "console"], [798, 15, 806, 13], [798, 16, 806, 14, "log"], [798, 19, 806, 17], [798, 20, 806, 18], [798, 68, 806, 66], [798, 70, 806, 68, "result"], [798, 76, 806, 74], [798, 77, 806, 75], [799, 8, 808, 6], [799, 12, 808, 10], [799, 13, 808, 11, "result"], [799, 19, 808, 17], [799, 20, 808, 18, "jobId"], [799, 25, 808, 23], [799, 27, 808, 25], [800, 10, 809, 8], [800, 16, 809, 14], [800, 20, 809, 18, "Error"], [800, 25, 809, 23], [800, 26, 809, 24], [800, 70, 809, 68], [800, 71, 809, 69], [801, 8, 810, 6], [803, 8, 812, 6], [804, 8, 813, 6], [804, 14, 813, 12, "pollForCompletion"], [804, 31, 813, 29], [804, 32, 813, 30, "result"], [804, 38, 813, 36], [804, 39, 813, 37, "jobId"], [804, 44, 813, 42], [804, 46, 813, 44, "timestamp"], [804, 55, 813, 53], [804, 56, 813, 54], [805, 6, 814, 4], [805, 7, 814, 5], [805, 8, 814, 6], [805, 15, 814, 13, "error"], [805, 20, 814, 18], [805, 22, 814, 20], [806, 8, 815, 6, "console"], [806, 15, 815, 13], [806, 16, 815, 14, "error"], [806, 21, 815, 19], [806, 22, 815, 20], [806, 57, 815, 55], [806, 59, 815, 57, "error"], [806, 64, 815, 62], [806, 65, 815, 63], [807, 8, 816, 6, "setErrorMessage"], [807, 23, 816, 21], [807, 24, 816, 22], [807, 52, 816, 50, "error"], [807, 57, 816, 55], [807, 58, 816, 56, "message"], [807, 65, 816, 63], [807, 67, 816, 65], [807, 68, 816, 66], [808, 8, 817, 6, "setProcessingState"], [808, 26, 817, 24], [808, 27, 817, 25], [808, 34, 817, 32], [808, 35, 817, 33], [809, 6, 818, 4], [810, 4, 819, 2], [810, 5, 819, 3], [811, 4, 820, 2], [812, 4, 821, 2], [812, 10, 821, 8, "pollForCompletion"], [812, 27, 821, 25], [812, 30, 821, 28], [812, 36, 821, 28, "pollForCompletion"], [812, 37, 821, 35, "jobId"], [812, 42, 821, 48], [812, 44, 821, 50, "timestamp"], [812, 53, 821, 67], [812, 55, 821, 69, "attempts"], [812, 63, 821, 77], [812, 66, 821, 80], [812, 67, 821, 81], [812, 72, 821, 86], [813, 6, 822, 4], [813, 12, 822, 10, "MAX_ATTEMPTS"], [813, 24, 822, 22], [813, 27, 822, 25], [813, 29, 822, 27], [813, 30, 822, 28], [813, 31, 822, 29], [814, 6, 823, 4], [814, 12, 823, 10, "POLL_INTERVAL"], [814, 25, 823, 23], [814, 28, 823, 26], [814, 32, 823, 30], [814, 33, 823, 31], [814, 34, 823, 32], [816, 6, 825, 4, "console"], [816, 13, 825, 11], [816, 14, 825, 12, "log"], [816, 17, 825, 15], [816, 18, 825, 16], [816, 53, 825, 51, "attempts"], [816, 61, 825, 59], [816, 64, 825, 62], [816, 65, 825, 63], [816, 69, 825, 67, "MAX_ATTEMPTS"], [816, 81, 825, 79], [816, 93, 825, 91, "jobId"], [816, 98, 825, 96], [816, 100, 825, 98], [816, 101, 825, 99], [817, 6, 827, 4], [817, 10, 827, 8, "attempts"], [817, 18, 827, 16], [817, 22, 827, 20, "MAX_ATTEMPTS"], [817, 34, 827, 32], [817, 36, 827, 34], [818, 8, 828, 6, "console"], [818, 15, 828, 13], [818, 16, 828, 14, "error"], [818, 21, 828, 19], [818, 22, 828, 20], [818, 75, 828, 73], [818, 76, 828, 74], [819, 8, 829, 6, "setErrorMessage"], [819, 23, 829, 21], [819, 24, 829, 22], [819, 63, 829, 61], [819, 64, 829, 62], [820, 8, 830, 6, "setProcessingState"], [820, 26, 830, 24], [820, 27, 830, 25], [820, 34, 830, 32], [820, 35, 830, 33], [821, 8, 831, 6], [822, 6, 832, 4], [823, 6, 834, 4], [823, 10, 834, 8], [824, 8, 835, 6], [824, 14, 835, 12, "response"], [824, 22, 835, 20], [824, 25, 835, 23], [824, 31, 835, 29, "fetch"], [824, 36, 835, 34], [824, 37, 835, 35], [824, 40, 835, 38, "API_BASE_URL"], [824, 52, 835, 50], [824, 75, 835, 73, "jobId"], [824, 80, 835, 78], [824, 82, 835, 80], [824, 84, 835, 82], [825, 10, 836, 8, "headers"], [825, 17, 836, 15], [825, 19, 836, 17], [826, 12, 837, 10], [826, 27, 837, 25], [826, 29, 837, 27], [826, 39, 837, 37], [826, 45, 837, 43, "getAuthToken"], [826, 57, 837, 55], [826, 58, 837, 56], [826, 59, 837, 57], [827, 10, 838, 8], [828, 8, 839, 6], [828, 9, 839, 7], [828, 10, 839, 8], [829, 8, 841, 6], [829, 12, 841, 10], [829, 13, 841, 11, "response"], [829, 21, 841, 19], [829, 22, 841, 20, "ok"], [829, 24, 841, 22], [829, 26, 841, 24], [830, 10, 842, 8], [830, 16, 842, 14], [830, 20, 842, 18, "Error"], [830, 25, 842, 23], [830, 26, 842, 24], [830, 34, 842, 32, "response"], [830, 42, 842, 40], [830, 43, 842, 41, "status"], [830, 49, 842, 47], [830, 54, 842, 52, "response"], [830, 62, 842, 60], [830, 63, 842, 61, "statusText"], [830, 73, 842, 71], [830, 75, 842, 73], [830, 76, 842, 74], [831, 8, 843, 6], [832, 8, 845, 6], [832, 14, 845, 12, "status"], [832, 20, 845, 18], [832, 23, 845, 21], [832, 29, 845, 27, "response"], [832, 37, 845, 35], [832, 38, 845, 36, "json"], [832, 42, 845, 40], [832, 43, 845, 41], [832, 44, 845, 42], [833, 8, 846, 6, "console"], [833, 15, 846, 13], [833, 16, 846, 14, "log"], [833, 19, 846, 17], [833, 20, 846, 18], [833, 54, 846, 52], [833, 56, 846, 54, "status"], [833, 62, 846, 60], [833, 63, 846, 61], [834, 8, 848, 6], [834, 12, 848, 10, "status"], [834, 18, 848, 16], [834, 19, 848, 17, "status"], [834, 25, 848, 23], [834, 30, 848, 28], [834, 41, 848, 39], [834, 43, 848, 41], [835, 10, 849, 8, "console"], [835, 17, 849, 15], [835, 18, 849, 16, "log"], [835, 21, 849, 19], [835, 22, 849, 20], [835, 73, 849, 71], [835, 74, 849, 72], [836, 10, 850, 8, "setProcessingProgress"], [836, 31, 850, 29], [836, 32, 850, 30], [836, 35, 850, 33], [836, 36, 850, 34], [837, 10, 851, 8, "setProcessingState"], [837, 28, 851, 26], [837, 29, 851, 27], [837, 40, 851, 38], [837, 41, 851, 39], [838, 10, 852, 8], [839, 10, 853, 8], [839, 16, 853, 14, "result"], [839, 22, 853, 20], [839, 25, 853, 23], [840, 12, 854, 10, "imageUrl"], [840, 20, 854, 18], [840, 22, 854, 20, "status"], [840, 28, 854, 26], [840, 29, 854, 27, "publicUrl"], [840, 38, 854, 36], [841, 12, 854, 38], [842, 12, 855, 10, "localUri"], [842, 20, 855, 18], [842, 22, 855, 20, "capturedPhoto"], [842, 35, 855, 33], [842, 39, 855, 37, "status"], [842, 45, 855, 43], [842, 46, 855, 44, "publicUrl"], [842, 55, 855, 53], [843, 12, 855, 55], [844, 12, 856, 10, "challengeCode"], [844, 25, 856, 23], [844, 27, 856, 25, "challengeCode"], [844, 40, 856, 38], [844, 44, 856, 42], [844, 46, 856, 44], [845, 12, 857, 10, "timestamp"], [845, 21, 857, 19], [846, 12, 858, 10, "processingStatus"], [846, 28, 858, 26], [846, 30, 858, 28], [847, 10, 859, 8], [847, 11, 859, 9], [848, 10, 860, 8, "console"], [848, 17, 860, 15], [848, 18, 860, 16, "log"], [848, 21, 860, 19], [848, 22, 860, 20], [848, 57, 860, 55], [848, 59, 860, 57, "result"], [848, 65, 860, 63], [848, 66, 860, 64], [849, 10, 861, 8, "onComplete"], [849, 20, 861, 18], [849, 21, 861, 19, "result"], [849, 27, 861, 25], [849, 28, 861, 26], [850, 10, 862, 8], [851, 8, 863, 6], [851, 9, 863, 7], [851, 15, 863, 13], [851, 19, 863, 17, "status"], [851, 25, 863, 23], [851, 26, 863, 24, "status"], [851, 32, 863, 30], [851, 37, 863, 35], [851, 45, 863, 43], [851, 47, 863, 45], [852, 10, 864, 8, "console"], [852, 17, 864, 15], [852, 18, 864, 16, "error"], [852, 23, 864, 21], [852, 24, 864, 22], [852, 60, 864, 58], [852, 62, 864, 60, "status"], [852, 68, 864, 66], [852, 69, 864, 67, "error"], [852, 74, 864, 72], [852, 75, 864, 73], [853, 10, 865, 8], [853, 16, 865, 14], [853, 20, 865, 18, "Error"], [853, 25, 865, 23], [853, 26, 865, 24, "status"], [853, 32, 865, 30], [853, 33, 865, 31, "error"], [853, 38, 865, 36], [853, 42, 865, 40], [853, 61, 865, 59], [853, 62, 865, 60], [854, 8, 866, 6], [854, 9, 866, 7], [854, 15, 866, 13], [855, 10, 867, 8], [856, 10, 868, 8], [856, 16, 868, 14, "progressValue"], [856, 29, 868, 27], [856, 32, 868, 30], [856, 34, 868, 32], [856, 37, 868, 36, "attempts"], [856, 45, 868, 44], [856, 48, 868, 47, "MAX_ATTEMPTS"], [856, 60, 868, 59], [856, 63, 868, 63], [856, 65, 868, 65], [857, 10, 869, 8, "console"], [857, 17, 869, 15], [857, 18, 869, 16, "log"], [857, 21, 869, 19], [857, 22, 869, 20], [857, 71, 869, 69, "progressValue"], [857, 84, 869, 82], [857, 87, 869, 85], [857, 88, 869, 86], [858, 10, 870, 8, "setProcessingProgress"], [858, 31, 870, 29], [858, 32, 870, 30, "progressValue"], [858, 45, 870, 43], [858, 46, 870, 44], [859, 10, 872, 8, "setTimeout"], [859, 20, 872, 18], [859, 21, 872, 19], [859, 27, 872, 25], [860, 12, 873, 10, "pollForCompletion"], [860, 29, 873, 27], [860, 30, 873, 28, "jobId"], [860, 35, 873, 33], [860, 37, 873, 35, "timestamp"], [860, 46, 873, 44], [860, 48, 873, 46, "attempts"], [860, 56, 873, 54], [860, 59, 873, 57], [860, 60, 873, 58], [860, 61, 873, 59], [861, 10, 874, 8], [861, 11, 874, 9], [861, 13, 874, 11, "POLL_INTERVAL"], [861, 26, 874, 24], [861, 27, 874, 25], [862, 8, 875, 6], [863, 6, 876, 4], [863, 7, 876, 5], [863, 8, 876, 6], [863, 15, 876, 13, "error"], [863, 20, 876, 18], [863, 22, 876, 20], [864, 8, 877, 6, "console"], [864, 15, 877, 13], [864, 16, 877, 14, "error"], [864, 21, 877, 19], [864, 22, 877, 20], [864, 54, 877, 52], [864, 56, 877, 54, "error"], [864, 61, 877, 59], [864, 62, 877, 60], [865, 8, 878, 6, "setErrorMessage"], [865, 23, 878, 21], [865, 24, 878, 22], [865, 62, 878, 60, "error"], [865, 67, 878, 65], [865, 68, 878, 66, "message"], [865, 75, 878, 73], [865, 77, 878, 75], [865, 78, 878, 76], [866, 8, 879, 6, "setProcessingState"], [866, 26, 879, 24], [866, 27, 879, 25], [866, 34, 879, 32], [866, 35, 879, 33], [867, 6, 880, 4], [868, 4, 881, 2], [868, 5, 881, 3], [869, 4, 882, 2], [870, 4, 883, 2], [870, 10, 883, 8, "getAuthToken"], [870, 22, 883, 20], [870, 25, 883, 23], [870, 31, 883, 23, "getAuthToken"], [870, 32, 883, 23], [870, 37, 883, 52], [871, 6, 884, 4], [872, 6, 885, 4], [873, 6, 886, 4], [873, 13, 886, 11], [873, 30, 886, 28], [874, 4, 887, 2], [874, 5, 887, 3], [876, 4, 889, 2], [877, 4, 890, 2], [877, 10, 890, 8, "retryCapture"], [877, 22, 890, 20], [877, 25, 890, 23], [877, 29, 890, 23, "useCallback"], [877, 47, 890, 34], [877, 49, 890, 35], [877, 55, 890, 41], [878, 6, 891, 4, "console"], [878, 13, 891, 11], [878, 14, 891, 12, "log"], [878, 17, 891, 15], [878, 18, 891, 16], [878, 55, 891, 53], [878, 56, 891, 54], [879, 6, 892, 4, "setProcessingState"], [879, 24, 892, 22], [879, 25, 892, 23], [879, 31, 892, 29], [879, 32, 892, 30], [880, 6, 893, 4, "setErrorMessage"], [880, 21, 893, 19], [880, 22, 893, 20], [880, 24, 893, 22], [880, 25, 893, 23], [881, 6, 894, 4, "setCapturedPhoto"], [881, 22, 894, 20], [881, 23, 894, 21], [881, 25, 894, 23], [881, 26, 894, 24], [882, 6, 895, 4, "setProcessingProgress"], [882, 27, 895, 25], [882, 28, 895, 26], [882, 29, 895, 27], [882, 30, 895, 28], [883, 4, 896, 2], [883, 5, 896, 3], [883, 7, 896, 5], [883, 9, 896, 7], [883, 10, 896, 8], [884, 4, 897, 2], [885, 4, 898, 2], [885, 8, 898, 2, "useEffect"], [885, 24, 898, 11], [885, 26, 898, 12], [885, 32, 898, 18], [886, 6, 899, 4, "console"], [886, 13, 899, 11], [886, 14, 899, 12, "log"], [886, 17, 899, 15], [886, 18, 899, 16], [886, 53, 899, 51], [886, 55, 899, 53, "permission"], [886, 65, 899, 63], [886, 66, 899, 64], [887, 6, 900, 4], [887, 10, 900, 8, "permission"], [887, 20, 900, 18], [887, 22, 900, 20], [888, 8, 901, 6, "console"], [888, 15, 901, 13], [888, 16, 901, 14, "log"], [888, 19, 901, 17], [888, 20, 901, 18], [888, 57, 901, 55], [888, 59, 901, 57, "permission"], [888, 69, 901, 67], [888, 70, 901, 68, "granted"], [888, 77, 901, 75], [888, 78, 901, 76], [889, 6, 902, 4], [890, 4, 903, 2], [890, 5, 903, 3], [890, 7, 903, 5], [890, 8, 903, 6, "permission"], [890, 18, 903, 16], [890, 19, 903, 17], [890, 20, 903, 18], [891, 4, 904, 2], [892, 4, 905, 2], [892, 8, 905, 6], [892, 9, 905, 7, "permission"], [892, 19, 905, 17], [892, 21, 905, 19], [893, 6, 906, 4, "console"], [893, 13, 906, 11], [893, 14, 906, 12, "log"], [893, 17, 906, 15], [893, 18, 906, 16], [893, 67, 906, 65], [893, 68, 906, 66], [894, 6, 907, 4], [894, 26, 908, 6], [894, 30, 908, 6, "_jsxDevRuntime"], [894, 44, 908, 6], [894, 45, 908, 6, "jsxDEV"], [894, 51, 908, 6], [894, 53, 908, 7, "_View"], [894, 58, 908, 7], [894, 59, 908, 7, "default"], [894, 66, 908, 11], [895, 8, 908, 12, "style"], [895, 13, 908, 17], [895, 15, 908, 19, "styles"], [895, 21, 908, 25], [895, 22, 908, 26, "container"], [895, 31, 908, 36], [896, 8, 908, 36, "children"], [896, 16, 908, 36], [896, 32, 909, 8], [896, 36, 909, 8, "_jsxDevRuntime"], [896, 50, 909, 8], [896, 51, 909, 8, "jsxDEV"], [896, 57, 909, 8], [896, 59, 909, 9, "_ActivityIndicator"], [896, 77, 909, 9], [896, 78, 909, 9, "default"], [896, 85, 909, 26], [897, 10, 909, 27, "size"], [897, 14, 909, 31], [897, 16, 909, 32], [897, 23, 909, 39], [898, 10, 909, 40, "color"], [898, 15, 909, 45], [898, 17, 909, 46], [899, 8, 909, 55], [900, 10, 909, 55, "fileName"], [900, 18, 909, 55], [900, 20, 909, 55, "_jsxFileName"], [900, 32, 909, 55], [901, 10, 909, 55, "lineNumber"], [901, 20, 909, 55], [902, 10, 909, 55, "columnNumber"], [902, 22, 909, 55], [903, 8, 909, 55], [903, 15, 909, 57], [903, 16, 909, 58], [903, 31, 910, 8], [903, 35, 910, 8, "_jsxDevRuntime"], [903, 49, 910, 8], [903, 50, 910, 8, "jsxDEV"], [903, 56, 910, 8], [903, 58, 910, 9, "_Text"], [903, 63, 910, 9], [903, 64, 910, 9, "default"], [903, 71, 910, 13], [904, 10, 910, 14, "style"], [904, 15, 910, 19], [904, 17, 910, 21, "styles"], [904, 23, 910, 27], [904, 24, 910, 28, "loadingText"], [904, 35, 910, 40], [905, 10, 910, 40, "children"], [905, 18, 910, 40], [905, 20, 910, 41], [906, 8, 910, 58], [907, 10, 910, 58, "fileName"], [907, 18, 910, 58], [907, 20, 910, 58, "_jsxFileName"], [907, 32, 910, 58], [908, 10, 910, 58, "lineNumber"], [908, 20, 910, 58], [909, 10, 910, 58, "columnNumber"], [909, 22, 910, 58], [910, 8, 910, 58], [910, 15, 910, 64], [910, 16, 910, 65], [911, 6, 910, 65], [912, 8, 910, 65, "fileName"], [912, 16, 910, 65], [912, 18, 910, 65, "_jsxFileName"], [912, 30, 910, 65], [913, 8, 910, 65, "lineNumber"], [913, 18, 910, 65], [914, 8, 910, 65, "columnNumber"], [914, 20, 910, 65], [915, 6, 910, 65], [915, 13, 911, 12], [915, 14, 911, 13], [916, 4, 913, 2], [917, 4, 914, 2], [917, 8, 914, 6], [917, 9, 914, 7, "permission"], [917, 19, 914, 17], [917, 20, 914, 18, "granted"], [917, 27, 914, 25], [917, 29, 914, 27], [918, 6, 915, 4, "console"], [918, 13, 915, 11], [918, 14, 915, 12, "log"], [918, 17, 915, 15], [918, 18, 915, 16], [918, 93, 915, 91], [918, 94, 915, 92], [919, 6, 916, 4], [919, 26, 917, 6], [919, 30, 917, 6, "_jsxDevRuntime"], [919, 44, 917, 6], [919, 45, 917, 6, "jsxDEV"], [919, 51, 917, 6], [919, 53, 917, 7, "_View"], [919, 58, 917, 7], [919, 59, 917, 7, "default"], [919, 66, 917, 11], [920, 8, 917, 12, "style"], [920, 13, 917, 17], [920, 15, 917, 19, "styles"], [920, 21, 917, 25], [920, 22, 917, 26, "container"], [920, 31, 917, 36], [921, 8, 917, 36, "children"], [921, 16, 917, 36], [921, 31, 918, 8], [921, 35, 918, 8, "_jsxDevRuntime"], [921, 49, 918, 8], [921, 50, 918, 8, "jsxDEV"], [921, 56, 918, 8], [921, 58, 918, 9, "_View"], [921, 63, 918, 9], [921, 64, 918, 9, "default"], [921, 71, 918, 13], [922, 10, 918, 14, "style"], [922, 15, 918, 19], [922, 17, 918, 21, "styles"], [922, 23, 918, 27], [922, 24, 918, 28, "permissionContent"], [922, 41, 918, 46], [923, 10, 918, 46, "children"], [923, 18, 918, 46], [923, 34, 919, 10], [923, 38, 919, 10, "_jsxDevRuntime"], [923, 52, 919, 10], [923, 53, 919, 10, "jsxDEV"], [923, 59, 919, 10], [923, 61, 919, 11, "_lucideReactNative"], [923, 79, 919, 11], [923, 80, 919, 11, "Camera"], [923, 86, 919, 21], [924, 12, 919, 22, "size"], [924, 16, 919, 26], [924, 18, 919, 28], [924, 20, 919, 31], [925, 12, 919, 32, "color"], [925, 17, 919, 37], [925, 19, 919, 38], [926, 10, 919, 47], [927, 12, 919, 47, "fileName"], [927, 20, 919, 47], [927, 22, 919, 47, "_jsxFileName"], [927, 34, 919, 47], [928, 12, 919, 47, "lineNumber"], [928, 22, 919, 47], [929, 12, 919, 47, "columnNumber"], [929, 24, 919, 47], [930, 10, 919, 47], [930, 17, 919, 49], [930, 18, 919, 50], [930, 33, 920, 10], [930, 37, 920, 10, "_jsxDevRuntime"], [930, 51, 920, 10], [930, 52, 920, 10, "jsxDEV"], [930, 58, 920, 10], [930, 60, 920, 11, "_Text"], [930, 65, 920, 11], [930, 66, 920, 11, "default"], [930, 73, 920, 15], [931, 12, 920, 16, "style"], [931, 17, 920, 21], [931, 19, 920, 23, "styles"], [931, 25, 920, 29], [931, 26, 920, 30, "permissionTitle"], [931, 41, 920, 46], [932, 12, 920, 46, "children"], [932, 20, 920, 46], [932, 22, 920, 47], [933, 10, 920, 73], [934, 12, 920, 73, "fileName"], [934, 20, 920, 73], [934, 22, 920, 73, "_jsxFileName"], [934, 34, 920, 73], [935, 12, 920, 73, "lineNumber"], [935, 22, 920, 73], [936, 12, 920, 73, "columnNumber"], [936, 24, 920, 73], [937, 10, 920, 73], [937, 17, 920, 79], [937, 18, 920, 80], [937, 33, 921, 10], [937, 37, 921, 10, "_jsxDevRuntime"], [937, 51, 921, 10], [937, 52, 921, 10, "jsxDEV"], [937, 58, 921, 10], [937, 60, 921, 11, "_Text"], [937, 65, 921, 11], [937, 66, 921, 11, "default"], [937, 73, 921, 15], [938, 12, 921, 16, "style"], [938, 17, 921, 21], [938, 19, 921, 23, "styles"], [938, 25, 921, 29], [938, 26, 921, 30, "permissionDescription"], [938, 47, 921, 52], [939, 12, 921, 52, "children"], [939, 20, 921, 52], [939, 22, 921, 53], [940, 10, 924, 10], [941, 12, 924, 10, "fileName"], [941, 20, 924, 10], [941, 22, 924, 10, "_jsxFileName"], [941, 34, 924, 10], [942, 12, 924, 10, "lineNumber"], [942, 22, 924, 10], [943, 12, 924, 10, "columnNumber"], [943, 24, 924, 10], [944, 10, 924, 10], [944, 17, 924, 16], [944, 18, 924, 17], [944, 33, 925, 10], [944, 37, 925, 10, "_jsxDevRuntime"], [944, 51, 925, 10], [944, 52, 925, 10, "jsxDEV"], [944, 58, 925, 10], [944, 60, 925, 11, "_TouchableOpacity"], [944, 77, 925, 11], [944, 78, 925, 11, "default"], [944, 85, 925, 27], [945, 12, 925, 28, "onPress"], [945, 19, 925, 35], [945, 21, 925, 37, "requestPermission"], [945, 38, 925, 55], [946, 12, 925, 56, "style"], [946, 17, 925, 61], [946, 19, 925, 63, "styles"], [946, 25, 925, 69], [946, 26, 925, 70, "primaryButton"], [946, 39, 925, 84], [947, 12, 925, 84, "children"], [947, 20, 925, 84], [947, 35, 926, 12], [947, 39, 926, 12, "_jsxDevRuntime"], [947, 53, 926, 12], [947, 54, 926, 12, "jsxDEV"], [947, 60, 926, 12], [947, 62, 926, 13, "_Text"], [947, 67, 926, 13], [947, 68, 926, 13, "default"], [947, 75, 926, 17], [948, 14, 926, 18, "style"], [948, 19, 926, 23], [948, 21, 926, 25, "styles"], [948, 27, 926, 31], [948, 28, 926, 32, "primaryButtonText"], [948, 45, 926, 50], [949, 14, 926, 50, "children"], [949, 22, 926, 50], [949, 24, 926, 51], [950, 12, 926, 67], [951, 14, 926, 67, "fileName"], [951, 22, 926, 67], [951, 24, 926, 67, "_jsxFileName"], [951, 36, 926, 67], [952, 14, 926, 67, "lineNumber"], [952, 24, 926, 67], [953, 14, 926, 67, "columnNumber"], [953, 26, 926, 67], [954, 12, 926, 67], [954, 19, 926, 73], [955, 10, 926, 74], [956, 12, 926, 74, "fileName"], [956, 20, 926, 74], [956, 22, 926, 74, "_jsxFileName"], [956, 34, 926, 74], [957, 12, 926, 74, "lineNumber"], [957, 22, 926, 74], [958, 12, 926, 74, "columnNumber"], [958, 24, 926, 74], [959, 10, 926, 74], [959, 17, 927, 28], [959, 18, 927, 29], [959, 33, 928, 10], [959, 37, 928, 10, "_jsxDevRuntime"], [959, 51, 928, 10], [959, 52, 928, 10, "jsxDEV"], [959, 58, 928, 10], [959, 60, 928, 11, "_TouchableOpacity"], [959, 77, 928, 11], [959, 78, 928, 11, "default"], [959, 85, 928, 27], [960, 12, 928, 28, "onPress"], [960, 19, 928, 35], [960, 21, 928, 37, "onCancel"], [960, 29, 928, 46], [961, 12, 928, 47, "style"], [961, 17, 928, 52], [961, 19, 928, 54, "styles"], [961, 25, 928, 60], [961, 26, 928, 61, "secondaryButton"], [961, 41, 928, 77], [962, 12, 928, 77, "children"], [962, 20, 928, 77], [962, 35, 929, 12], [962, 39, 929, 12, "_jsxDevRuntime"], [962, 53, 929, 12], [962, 54, 929, 12, "jsxDEV"], [962, 60, 929, 12], [962, 62, 929, 13, "_Text"], [962, 67, 929, 13], [962, 68, 929, 13, "default"], [962, 75, 929, 17], [963, 14, 929, 18, "style"], [963, 19, 929, 23], [963, 21, 929, 25, "styles"], [963, 27, 929, 31], [963, 28, 929, 32, "secondaryButtonText"], [963, 47, 929, 52], [964, 14, 929, 52, "children"], [964, 22, 929, 52], [964, 24, 929, 53], [965, 12, 929, 59], [966, 14, 929, 59, "fileName"], [966, 22, 929, 59], [966, 24, 929, 59, "_jsxFileName"], [966, 36, 929, 59], [967, 14, 929, 59, "lineNumber"], [967, 24, 929, 59], [968, 14, 929, 59, "columnNumber"], [968, 26, 929, 59], [969, 12, 929, 59], [969, 19, 929, 65], [970, 10, 929, 66], [971, 12, 929, 66, "fileName"], [971, 20, 929, 66], [971, 22, 929, 66, "_jsxFileName"], [971, 34, 929, 66], [972, 12, 929, 66, "lineNumber"], [972, 22, 929, 66], [973, 12, 929, 66, "columnNumber"], [973, 24, 929, 66], [974, 10, 929, 66], [974, 17, 930, 28], [974, 18, 930, 29], [975, 8, 930, 29], [976, 10, 930, 29, "fileName"], [976, 18, 930, 29], [976, 20, 930, 29, "_jsxFileName"], [976, 32, 930, 29], [977, 10, 930, 29, "lineNumber"], [977, 20, 930, 29], [978, 10, 930, 29, "columnNumber"], [978, 22, 930, 29], [979, 8, 930, 29], [979, 15, 931, 14], [980, 6, 931, 15], [981, 8, 931, 15, "fileName"], [981, 16, 931, 15], [981, 18, 931, 15, "_jsxFileName"], [981, 30, 931, 15], [982, 8, 931, 15, "lineNumber"], [982, 18, 931, 15], [983, 8, 931, 15, "columnNumber"], [983, 20, 931, 15], [984, 6, 931, 15], [984, 13, 932, 12], [984, 14, 932, 13], [985, 4, 934, 2], [986, 4, 935, 2], [987, 4, 936, 2, "console"], [987, 11, 936, 9], [987, 12, 936, 10, "log"], [987, 15, 936, 13], [987, 16, 936, 14], [987, 55, 936, 53], [987, 56, 936, 54], [988, 4, 938, 2], [988, 24, 939, 4], [988, 28, 939, 4, "_jsxDevRuntime"], [988, 42, 939, 4], [988, 43, 939, 4, "jsxDEV"], [988, 49, 939, 4], [988, 51, 939, 5, "_View"], [988, 56, 939, 5], [988, 57, 939, 5, "default"], [988, 64, 939, 9], [989, 6, 939, 10, "style"], [989, 11, 939, 15], [989, 13, 939, 17, "styles"], [989, 19, 939, 23], [989, 20, 939, 24, "container"], [989, 29, 939, 34], [990, 6, 939, 34, "children"], [990, 14, 939, 34], [990, 30, 941, 6], [990, 34, 941, 6, "_jsxDevRuntime"], [990, 48, 941, 6], [990, 49, 941, 6, "jsxDEV"], [990, 55, 941, 6], [990, 57, 941, 7, "_View"], [990, 62, 941, 7], [990, 63, 941, 7, "default"], [990, 70, 941, 11], [991, 8, 941, 12, "style"], [991, 13, 941, 17], [991, 15, 941, 19, "styles"], [991, 21, 941, 25], [991, 22, 941, 26, "cameraContainer"], [991, 37, 941, 42], [992, 8, 941, 43, "id"], [992, 10, 941, 45], [992, 12, 941, 46], [992, 29, 941, 63], [993, 8, 941, 63, "children"], [993, 16, 941, 63], [993, 32, 942, 8], [993, 36, 942, 8, "_jsxDevRuntime"], [993, 50, 942, 8], [993, 51, 942, 8, "jsxDEV"], [993, 57, 942, 8], [993, 59, 942, 9, "_expoCamera"], [993, 70, 942, 9], [993, 71, 942, 9, "CameraView"], [993, 81, 942, 19], [994, 10, 943, 10, "ref"], [994, 13, 943, 13], [994, 15, 943, 15, "cameraRef"], [994, 24, 943, 25], [995, 10, 944, 10, "style"], [995, 15, 944, 15], [995, 17, 944, 17], [995, 18, 944, 18, "styles"], [995, 24, 944, 24], [995, 25, 944, 25, "camera"], [995, 31, 944, 31], [995, 33, 944, 33], [996, 12, 944, 35, "backgroundColor"], [996, 27, 944, 50], [996, 29, 944, 52], [997, 10, 944, 62], [997, 11, 944, 63], [997, 12, 944, 65], [998, 10, 945, 10, "facing"], [998, 16, 945, 16], [998, 18, 945, 17], [998, 24, 945, 23], [999, 10, 946, 10, "onLayout"], [999, 18, 946, 18], [999, 20, 946, 21, "e"], [999, 21, 946, 22], [999, 25, 946, 27], [1000, 12, 947, 12, "console"], [1000, 19, 947, 19], [1000, 20, 947, 20, "log"], [1000, 23, 947, 23], [1000, 24, 947, 24], [1000, 56, 947, 56], [1000, 58, 947, 58, "e"], [1000, 59, 947, 59], [1000, 60, 947, 60, "nativeEvent"], [1000, 71, 947, 71], [1000, 72, 947, 72, "layout"], [1000, 78, 947, 78], [1000, 79, 947, 79], [1001, 12, 948, 12, "setViewSize"], [1001, 23, 948, 23], [1001, 24, 948, 24], [1002, 14, 948, 26, "width"], [1002, 19, 948, 31], [1002, 21, 948, 33, "e"], [1002, 22, 948, 34], [1002, 23, 948, 35, "nativeEvent"], [1002, 34, 948, 46], [1002, 35, 948, 47, "layout"], [1002, 41, 948, 53], [1002, 42, 948, 54, "width"], [1002, 47, 948, 59], [1003, 14, 948, 61, "height"], [1003, 20, 948, 67], [1003, 22, 948, 69, "e"], [1003, 23, 948, 70], [1003, 24, 948, 71, "nativeEvent"], [1003, 35, 948, 82], [1003, 36, 948, 83, "layout"], [1003, 42, 948, 89], [1003, 43, 948, 90, "height"], [1004, 12, 948, 97], [1004, 13, 948, 98], [1004, 14, 948, 99], [1005, 10, 949, 10], [1005, 11, 949, 12], [1006, 10, 950, 10, "onCameraReady"], [1006, 23, 950, 23], [1006, 25, 950, 25, "onCameraReady"], [1006, 26, 950, 25], [1006, 31, 950, 31], [1007, 12, 951, 12, "console"], [1007, 19, 951, 19], [1007, 20, 951, 20, "log"], [1007, 23, 951, 23], [1007, 24, 951, 24], [1007, 55, 951, 55], [1007, 56, 951, 56], [1008, 12, 952, 12, "setIsCameraReady"], [1008, 28, 952, 28], [1008, 29, 952, 29], [1008, 33, 952, 33], [1008, 34, 952, 34], [1008, 35, 952, 35], [1008, 36, 952, 36], [1009, 10, 953, 10], [1009, 11, 953, 12], [1010, 10, 954, 10, "onMountError"], [1010, 22, 954, 22], [1010, 24, 954, 25, "error"], [1010, 29, 954, 30], [1010, 33, 954, 35], [1011, 12, 955, 12, "console"], [1011, 19, 955, 19], [1011, 20, 955, 20, "error"], [1011, 25, 955, 25], [1011, 26, 955, 26], [1011, 63, 955, 63], [1011, 65, 955, 65, "error"], [1011, 70, 955, 70], [1011, 71, 955, 71], [1012, 12, 956, 12, "setErrorMessage"], [1012, 27, 956, 27], [1012, 28, 956, 28], [1012, 57, 956, 57], [1012, 58, 956, 58], [1013, 12, 957, 12, "setProcessingState"], [1013, 30, 957, 30], [1013, 31, 957, 31], [1013, 38, 957, 38], [1013, 39, 957, 39], [1014, 10, 958, 10], [1015, 8, 958, 12], [1016, 10, 958, 12, "fileName"], [1016, 18, 958, 12], [1016, 20, 958, 12, "_jsxFileName"], [1016, 32, 958, 12], [1017, 10, 958, 12, "lineNumber"], [1017, 20, 958, 12], [1018, 10, 958, 12, "columnNumber"], [1018, 22, 958, 12], [1019, 8, 958, 12], [1019, 15, 959, 9], [1019, 16, 959, 10], [1019, 18, 961, 9], [1019, 19, 961, 10, "isCameraReady"], [1019, 32, 961, 23], [1019, 49, 962, 10], [1019, 53, 962, 10, "_jsxDevRuntime"], [1019, 67, 962, 10], [1019, 68, 962, 10, "jsxDEV"], [1019, 74, 962, 10], [1019, 76, 962, 11, "_View"], [1019, 81, 962, 11], [1019, 82, 962, 11, "default"], [1019, 89, 962, 15], [1020, 10, 962, 16, "style"], [1020, 15, 962, 21], [1020, 17, 962, 23], [1020, 18, 962, 24, "StyleSheet"], [1020, 37, 962, 34], [1020, 38, 962, 35, "absoluteFill"], [1020, 50, 962, 47], [1020, 52, 962, 49], [1021, 12, 962, 51, "backgroundColor"], [1021, 27, 962, 66], [1021, 29, 962, 68], [1021, 49, 962, 88], [1022, 12, 962, 90, "justifyContent"], [1022, 26, 962, 104], [1022, 28, 962, 106], [1022, 36, 962, 114], [1023, 12, 962, 116, "alignItems"], [1023, 22, 962, 126], [1023, 24, 962, 128], [1023, 32, 962, 136], [1024, 12, 962, 138, "zIndex"], [1024, 18, 962, 144], [1024, 20, 962, 146], [1025, 10, 962, 151], [1025, 11, 962, 152], [1025, 12, 962, 154], [1026, 10, 962, 154, "children"], [1026, 18, 962, 154], [1026, 33, 963, 12], [1026, 37, 963, 12, "_jsxDevRuntime"], [1026, 51, 963, 12], [1026, 52, 963, 12, "jsxDEV"], [1026, 58, 963, 12], [1026, 60, 963, 13, "_View"], [1026, 65, 963, 13], [1026, 66, 963, 13, "default"], [1026, 73, 963, 17], [1027, 12, 963, 18, "style"], [1027, 17, 963, 23], [1027, 19, 963, 25], [1028, 14, 963, 27, "backgroundColor"], [1028, 29, 963, 42], [1028, 31, 963, 44], [1028, 51, 963, 64], [1029, 14, 963, 66, "padding"], [1029, 21, 963, 73], [1029, 23, 963, 75], [1029, 25, 963, 77], [1030, 14, 963, 79, "borderRadius"], [1030, 26, 963, 91], [1030, 28, 963, 93], [1030, 30, 963, 95], [1031, 14, 963, 97, "alignItems"], [1031, 24, 963, 107], [1031, 26, 963, 109], [1032, 12, 963, 118], [1032, 13, 963, 120], [1033, 12, 963, 120, "children"], [1033, 20, 963, 120], [1033, 36, 964, 14], [1033, 40, 964, 14, "_jsxDevRuntime"], [1033, 54, 964, 14], [1033, 55, 964, 14, "jsxDEV"], [1033, 61, 964, 14], [1033, 63, 964, 15, "_ActivityIndicator"], [1033, 81, 964, 15], [1033, 82, 964, 15, "default"], [1033, 89, 964, 32], [1034, 14, 964, 33, "size"], [1034, 18, 964, 37], [1034, 20, 964, 38], [1034, 27, 964, 45], [1035, 14, 964, 46, "color"], [1035, 19, 964, 51], [1035, 21, 964, 52], [1035, 30, 964, 61], [1036, 14, 964, 62, "style"], [1036, 19, 964, 67], [1036, 21, 964, 69], [1037, 16, 964, 71, "marginBottom"], [1037, 28, 964, 83], [1037, 30, 964, 85], [1038, 14, 964, 88], [1039, 12, 964, 90], [1040, 14, 964, 90, "fileName"], [1040, 22, 964, 90], [1040, 24, 964, 90, "_jsxFileName"], [1040, 36, 964, 90], [1041, 14, 964, 90, "lineNumber"], [1041, 24, 964, 90], [1042, 14, 964, 90, "columnNumber"], [1042, 26, 964, 90], [1043, 12, 964, 90], [1043, 19, 964, 92], [1043, 20, 964, 93], [1043, 35, 965, 14], [1043, 39, 965, 14, "_jsxDevRuntime"], [1043, 53, 965, 14], [1043, 54, 965, 14, "jsxDEV"], [1043, 60, 965, 14], [1043, 62, 965, 15, "_Text"], [1043, 67, 965, 15], [1043, 68, 965, 15, "default"], [1043, 75, 965, 19], [1044, 14, 965, 20, "style"], [1044, 19, 965, 25], [1044, 21, 965, 27], [1045, 16, 965, 29, "color"], [1045, 21, 965, 34], [1045, 23, 965, 36], [1045, 29, 965, 42], [1046, 16, 965, 44, "fontSize"], [1046, 24, 965, 52], [1046, 26, 965, 54], [1046, 28, 965, 56], [1047, 16, 965, 58, "fontWeight"], [1047, 26, 965, 68], [1047, 28, 965, 70], [1048, 14, 965, 76], [1048, 15, 965, 78], [1049, 14, 965, 78, "children"], [1049, 22, 965, 78], [1049, 24, 965, 79], [1050, 12, 965, 101], [1051, 14, 965, 101, "fileName"], [1051, 22, 965, 101], [1051, 24, 965, 101, "_jsxFileName"], [1051, 36, 965, 101], [1052, 14, 965, 101, "lineNumber"], [1052, 24, 965, 101], [1053, 14, 965, 101, "columnNumber"], [1053, 26, 965, 101], [1054, 12, 965, 101], [1054, 19, 965, 107], [1054, 20, 965, 108], [1054, 35, 966, 14], [1054, 39, 966, 14, "_jsxDevRuntime"], [1054, 53, 966, 14], [1054, 54, 966, 14, "jsxDEV"], [1054, 60, 966, 14], [1054, 62, 966, 15, "_Text"], [1054, 67, 966, 15], [1054, 68, 966, 15, "default"], [1054, 75, 966, 19], [1055, 14, 966, 20, "style"], [1055, 19, 966, 25], [1055, 21, 966, 27], [1056, 16, 966, 29, "color"], [1056, 21, 966, 34], [1056, 23, 966, 36], [1056, 32, 966, 45], [1057, 16, 966, 47, "fontSize"], [1057, 24, 966, 55], [1057, 26, 966, 57], [1057, 28, 966, 59], [1058, 16, 966, 61, "marginTop"], [1058, 25, 966, 70], [1058, 27, 966, 72], [1059, 14, 966, 74], [1059, 15, 966, 76], [1060, 14, 966, 76, "children"], [1060, 22, 966, 76], [1060, 24, 966, 77], [1061, 12, 966, 88], [1062, 14, 966, 88, "fileName"], [1062, 22, 966, 88], [1062, 24, 966, 88, "_jsxFileName"], [1062, 36, 966, 88], [1063, 14, 966, 88, "lineNumber"], [1063, 24, 966, 88], [1064, 14, 966, 88, "columnNumber"], [1064, 26, 966, 88], [1065, 12, 966, 88], [1065, 19, 966, 94], [1065, 20, 966, 95], [1066, 10, 966, 95], [1067, 12, 966, 95, "fileName"], [1067, 20, 966, 95], [1067, 22, 966, 95, "_jsxFileName"], [1067, 34, 966, 95], [1068, 12, 966, 95, "lineNumber"], [1068, 22, 966, 95], [1069, 12, 966, 95, "columnNumber"], [1069, 24, 966, 95], [1070, 10, 966, 95], [1070, 17, 967, 18], [1071, 8, 967, 19], [1072, 10, 967, 19, "fileName"], [1072, 18, 967, 19], [1072, 20, 967, 19, "_jsxFileName"], [1072, 32, 967, 19], [1073, 10, 967, 19, "lineNumber"], [1073, 20, 967, 19], [1074, 10, 967, 19, "columnNumber"], [1074, 22, 967, 19], [1075, 8, 967, 19], [1075, 15, 968, 16], [1075, 16, 969, 9], [1075, 18, 972, 9, "isCameraReady"], [1075, 31, 972, 22], [1075, 35, 972, 26, "previewBlurEnabled"], [1075, 53, 972, 44], [1075, 57, 972, 48, "viewSize"], [1075, 65, 972, 56], [1075, 66, 972, 57, "width"], [1075, 71, 972, 62], [1075, 74, 972, 65], [1075, 75, 972, 66], [1075, 92, 973, 10], [1075, 96, 973, 10, "_jsxDevRuntime"], [1075, 110, 973, 10], [1075, 111, 973, 10, "jsxDEV"], [1075, 117, 973, 10], [1075, 119, 973, 10, "_jsxDevRuntime"], [1075, 133, 973, 10], [1075, 134, 973, 10, "Fragment"], [1075, 142, 973, 10], [1076, 10, 973, 10, "children"], [1076, 18, 973, 10], [1076, 34, 975, 12], [1076, 38, 975, 12, "_jsxDevRuntime"], [1076, 52, 975, 12], [1076, 53, 975, 12, "jsxDEV"], [1076, 59, 975, 12], [1076, 61, 975, 13, "_TensorFlowFaceCanvas"], [1076, 82, 975, 13], [1076, 83, 975, 13, "default"], [1076, 90, 975, 33], [1077, 12, 975, 34, "containerId"], [1077, 23, 975, 45], [1077, 25, 975, 46], [1077, 42, 975, 63], [1078, 12, 975, 64, "width"], [1078, 17, 975, 69], [1078, 19, 975, 71, "viewSize"], [1078, 27, 975, 79], [1078, 28, 975, 80, "width"], [1078, 33, 975, 86], [1079, 12, 975, 87, "height"], [1079, 18, 975, 93], [1079, 20, 975, 95, "viewSize"], [1079, 28, 975, 103], [1079, 29, 975, 104, "height"], [1080, 10, 975, 111], [1081, 12, 975, 111, "fileName"], [1081, 20, 975, 111], [1081, 22, 975, 111, "_jsxFileName"], [1081, 34, 975, 111], [1082, 12, 975, 111, "lineNumber"], [1082, 22, 975, 111], [1083, 12, 975, 111, "columnNumber"], [1083, 24, 975, 111], [1084, 10, 975, 111], [1084, 17, 975, 113], [1084, 18, 975, 114], [1084, 33, 976, 12], [1084, 37, 976, 12, "_jsxDevRuntime"], [1084, 51, 976, 12], [1084, 52, 976, 12, "jsxDEV"], [1084, 58, 976, 12], [1084, 60, 976, 13, "_View"], [1084, 65, 976, 13], [1084, 66, 976, 13, "default"], [1084, 73, 976, 17], [1085, 12, 976, 18, "style"], [1085, 17, 976, 23], [1085, 19, 976, 25], [1085, 20, 976, 26, "StyleSheet"], [1085, 39, 976, 36], [1085, 40, 976, 37, "absoluteFill"], [1085, 52, 976, 49], [1085, 54, 976, 51], [1086, 14, 976, 53, "pointerEvents"], [1086, 27, 976, 66], [1086, 29, 976, 68], [1087, 12, 976, 75], [1087, 13, 976, 76], [1087, 14, 976, 78], [1088, 12, 976, 78, "children"], [1088, 20, 976, 78], [1088, 36, 978, 12], [1088, 40, 978, 12, "_jsxDevRuntime"], [1088, 54, 978, 12], [1088, 55, 978, 12, "jsxDEV"], [1088, 61, 978, 12], [1088, 63, 978, 13, "_expoBlur"], [1088, 72, 978, 13], [1088, 73, 978, 13, "BlurView"], [1088, 81, 978, 21], [1089, 14, 978, 22, "intensity"], [1089, 23, 978, 31], [1089, 25, 978, 33], [1089, 27, 978, 36], [1090, 14, 978, 37, "tint"], [1090, 18, 978, 41], [1090, 20, 978, 42], [1090, 26, 978, 48], [1091, 14, 978, 49, "style"], [1091, 19, 978, 54], [1091, 21, 978, 56], [1091, 22, 978, 57, "styles"], [1091, 28, 978, 63], [1091, 29, 978, 64, "blurZone"], [1091, 37, 978, 72], [1091, 39, 978, 74], [1092, 16, 979, 14, "left"], [1092, 20, 979, 18], [1092, 22, 979, 20], [1092, 23, 979, 21], [1093, 16, 980, 14, "top"], [1093, 19, 980, 17], [1093, 21, 980, 19, "viewSize"], [1093, 29, 980, 27], [1093, 30, 980, 28, "height"], [1093, 36, 980, 34], [1093, 39, 980, 37], [1093, 42, 980, 40], [1094, 16, 981, 14, "width"], [1094, 21, 981, 19], [1094, 23, 981, 21, "viewSize"], [1094, 31, 981, 29], [1094, 32, 981, 30, "width"], [1094, 37, 981, 35], [1095, 16, 982, 14, "height"], [1095, 22, 982, 20], [1095, 24, 982, 22, "viewSize"], [1095, 32, 982, 30], [1095, 33, 982, 31, "height"], [1095, 39, 982, 37], [1095, 42, 982, 40], [1095, 45, 982, 43], [1096, 16, 983, 14, "borderRadius"], [1096, 28, 983, 26], [1096, 30, 983, 28], [1096, 32, 983, 30], [1097, 16, 984, 14, "opacity"], [1097, 23, 984, 21], [1097, 25, 984, 23], [1097, 28, 984, 26], [1097, 29, 984, 28], [1098, 14, 985, 12], [1098, 15, 985, 13], [1099, 12, 985, 15], [1100, 14, 985, 15, "fileName"], [1100, 22, 985, 15], [1100, 24, 985, 15, "_jsxFileName"], [1100, 36, 985, 15], [1101, 14, 985, 15, "lineNumber"], [1101, 24, 985, 15], [1102, 14, 985, 15, "columnNumber"], [1102, 26, 985, 15], [1103, 12, 985, 15], [1103, 19, 985, 17], [1103, 20, 985, 18], [1103, 35, 987, 12], [1103, 39, 987, 12, "_jsxDevRuntime"], [1103, 53, 987, 12], [1103, 54, 987, 12, "jsxDEV"], [1103, 60, 987, 12], [1103, 62, 987, 13, "_expoBlur"], [1103, 71, 987, 13], [1103, 72, 987, 13, "BlurView"], [1103, 80, 987, 21], [1104, 14, 987, 22, "intensity"], [1104, 23, 987, 31], [1104, 25, 987, 33], [1104, 27, 987, 36], [1105, 14, 987, 37, "tint"], [1105, 18, 987, 41], [1105, 20, 987, 42], [1105, 26, 987, 48], [1106, 14, 987, 49, "style"], [1106, 19, 987, 54], [1106, 21, 987, 56], [1106, 22, 987, 57, "styles"], [1106, 28, 987, 63], [1106, 29, 987, 64, "blurZone"], [1106, 37, 987, 72], [1106, 39, 987, 74], [1107, 16, 988, 14, "left"], [1107, 20, 988, 18], [1107, 22, 988, 20, "viewSize"], [1107, 30, 988, 28], [1107, 31, 988, 29, "width"], [1107, 36, 988, 34], [1107, 39, 988, 37], [1107, 42, 988, 40], [1107, 45, 988, 44, "viewSize"], [1107, 53, 988, 52], [1107, 54, 988, 53, "width"], [1107, 59, 988, 58], [1107, 62, 988, 61], [1107, 66, 988, 66], [1108, 16, 989, 14, "top"], [1108, 19, 989, 17], [1108, 21, 989, 19, "viewSize"], [1108, 29, 989, 27], [1108, 30, 989, 28, "height"], [1108, 36, 989, 34], [1108, 39, 989, 37], [1108, 43, 989, 41], [1108, 46, 989, 45, "viewSize"], [1108, 54, 989, 53], [1108, 55, 989, 54, "width"], [1108, 60, 989, 59], [1108, 63, 989, 62], [1108, 67, 989, 67], [1109, 16, 990, 14, "width"], [1109, 21, 990, 19], [1109, 23, 990, 21, "viewSize"], [1109, 31, 990, 29], [1109, 32, 990, 30, "width"], [1109, 37, 990, 35], [1109, 40, 990, 38], [1109, 43, 990, 41], [1110, 16, 991, 14, "height"], [1110, 22, 991, 20], [1110, 24, 991, 22, "viewSize"], [1110, 32, 991, 30], [1110, 33, 991, 31, "width"], [1110, 38, 991, 36], [1110, 41, 991, 39], [1110, 44, 991, 42], [1111, 16, 992, 14, "borderRadius"], [1111, 28, 992, 26], [1111, 30, 992, 29, "viewSize"], [1111, 38, 992, 37], [1111, 39, 992, 38, "width"], [1111, 44, 992, 43], [1111, 47, 992, 46], [1111, 50, 992, 49], [1111, 53, 992, 53], [1111, 54, 992, 54], [1112, 16, 993, 14, "opacity"], [1112, 23, 993, 21], [1112, 25, 993, 23], [1113, 14, 994, 12], [1113, 15, 994, 13], [1114, 12, 994, 15], [1115, 14, 994, 15, "fileName"], [1115, 22, 994, 15], [1115, 24, 994, 15, "_jsxFileName"], [1115, 36, 994, 15], [1116, 14, 994, 15, "lineNumber"], [1116, 24, 994, 15], [1117, 14, 994, 15, "columnNumber"], [1117, 26, 994, 15], [1118, 12, 994, 15], [1118, 19, 994, 17], [1118, 20, 994, 18], [1118, 35, 995, 12], [1118, 39, 995, 12, "_jsxDevRuntime"], [1118, 53, 995, 12], [1118, 54, 995, 12, "jsxDEV"], [1118, 60, 995, 12], [1118, 62, 995, 13, "_expoBlur"], [1118, 71, 995, 13], [1118, 72, 995, 13, "BlurView"], [1118, 80, 995, 21], [1119, 14, 995, 22, "intensity"], [1119, 23, 995, 31], [1119, 25, 995, 33], [1119, 27, 995, 36], [1120, 14, 995, 37, "tint"], [1120, 18, 995, 41], [1120, 20, 995, 42], [1120, 26, 995, 48], [1121, 14, 995, 49, "style"], [1121, 19, 995, 54], [1121, 21, 995, 56], [1121, 22, 995, 57, "styles"], [1121, 28, 995, 63], [1121, 29, 995, 64, "blurZone"], [1121, 37, 995, 72], [1121, 39, 995, 74], [1122, 16, 996, 14, "left"], [1122, 20, 996, 18], [1122, 22, 996, 20, "viewSize"], [1122, 30, 996, 28], [1122, 31, 996, 29, "width"], [1122, 36, 996, 34], [1122, 39, 996, 37], [1122, 42, 996, 40], [1122, 45, 996, 44, "viewSize"], [1122, 53, 996, 52], [1122, 54, 996, 53, "width"], [1122, 59, 996, 58], [1122, 62, 996, 61], [1122, 66, 996, 66], [1123, 16, 997, 14, "top"], [1123, 19, 997, 17], [1123, 21, 997, 19, "viewSize"], [1123, 29, 997, 27], [1123, 30, 997, 28, "height"], [1123, 36, 997, 34], [1123, 39, 997, 37], [1123, 42, 997, 40], [1123, 45, 997, 44, "viewSize"], [1123, 53, 997, 52], [1123, 54, 997, 53, "width"], [1123, 59, 997, 58], [1123, 62, 997, 61], [1123, 66, 997, 66], [1124, 16, 998, 14, "width"], [1124, 21, 998, 19], [1124, 23, 998, 21, "viewSize"], [1124, 31, 998, 29], [1124, 32, 998, 30, "width"], [1124, 37, 998, 35], [1124, 40, 998, 38], [1124, 43, 998, 41], [1125, 16, 999, 14, "height"], [1125, 22, 999, 20], [1125, 24, 999, 22, "viewSize"], [1125, 32, 999, 30], [1125, 33, 999, 31, "width"], [1125, 38, 999, 36], [1125, 41, 999, 39], [1125, 44, 999, 42], [1126, 16, 1000, 14, "borderRadius"], [1126, 28, 1000, 26], [1126, 30, 1000, 29, "viewSize"], [1126, 38, 1000, 37], [1126, 39, 1000, 38, "width"], [1126, 44, 1000, 43], [1126, 47, 1000, 46], [1126, 50, 1000, 49], [1126, 53, 1000, 53], [1126, 54, 1000, 54], [1127, 16, 1001, 14, "opacity"], [1127, 23, 1001, 21], [1127, 25, 1001, 23], [1128, 14, 1002, 12], [1128, 15, 1002, 13], [1129, 12, 1002, 15], [1130, 14, 1002, 15, "fileName"], [1130, 22, 1002, 15], [1130, 24, 1002, 15, "_jsxFileName"], [1130, 36, 1002, 15], [1131, 14, 1002, 15, "lineNumber"], [1131, 24, 1002, 15], [1132, 14, 1002, 15, "columnNumber"], [1132, 26, 1002, 15], [1133, 12, 1002, 15], [1133, 19, 1002, 17], [1133, 20, 1002, 18], [1133, 35, 1003, 12], [1133, 39, 1003, 12, "_jsxDevRuntime"], [1133, 53, 1003, 12], [1133, 54, 1003, 12, "jsxDEV"], [1133, 60, 1003, 12], [1133, 62, 1003, 13, "_expoBlur"], [1133, 71, 1003, 13], [1133, 72, 1003, 13, "BlurView"], [1133, 80, 1003, 21], [1134, 14, 1003, 22, "intensity"], [1134, 23, 1003, 31], [1134, 25, 1003, 33], [1134, 27, 1003, 36], [1135, 14, 1003, 37, "tint"], [1135, 18, 1003, 41], [1135, 20, 1003, 42], [1135, 26, 1003, 48], [1136, 14, 1003, 49, "style"], [1136, 19, 1003, 54], [1136, 21, 1003, 56], [1136, 22, 1003, 57, "styles"], [1136, 28, 1003, 63], [1136, 29, 1003, 64, "blurZone"], [1136, 37, 1003, 72], [1136, 39, 1003, 74], [1137, 16, 1004, 14, "left"], [1137, 20, 1004, 18], [1137, 22, 1004, 20, "viewSize"], [1137, 30, 1004, 28], [1137, 31, 1004, 29, "width"], [1137, 36, 1004, 34], [1137, 39, 1004, 37], [1137, 42, 1004, 40], [1137, 45, 1004, 44, "viewSize"], [1137, 53, 1004, 52], [1137, 54, 1004, 53, "width"], [1137, 59, 1004, 58], [1137, 62, 1004, 61], [1137, 66, 1004, 66], [1138, 16, 1005, 14, "top"], [1138, 19, 1005, 17], [1138, 21, 1005, 19, "viewSize"], [1138, 29, 1005, 27], [1138, 30, 1005, 28, "height"], [1138, 36, 1005, 34], [1138, 39, 1005, 37], [1138, 42, 1005, 40], [1138, 45, 1005, 44, "viewSize"], [1138, 53, 1005, 52], [1138, 54, 1005, 53, "width"], [1138, 59, 1005, 58], [1138, 62, 1005, 61], [1138, 66, 1005, 66], [1139, 16, 1006, 14, "width"], [1139, 21, 1006, 19], [1139, 23, 1006, 21, "viewSize"], [1139, 31, 1006, 29], [1139, 32, 1006, 30, "width"], [1139, 37, 1006, 35], [1139, 40, 1006, 38], [1139, 43, 1006, 41], [1140, 16, 1007, 14, "height"], [1140, 22, 1007, 20], [1140, 24, 1007, 22, "viewSize"], [1140, 32, 1007, 30], [1140, 33, 1007, 31, "width"], [1140, 38, 1007, 36], [1140, 41, 1007, 39], [1140, 44, 1007, 42], [1141, 16, 1008, 14, "borderRadius"], [1141, 28, 1008, 26], [1141, 30, 1008, 29, "viewSize"], [1141, 38, 1008, 37], [1141, 39, 1008, 38, "width"], [1141, 44, 1008, 43], [1141, 47, 1008, 46], [1141, 50, 1008, 49], [1141, 53, 1008, 53], [1141, 54, 1008, 54], [1142, 16, 1009, 14, "opacity"], [1142, 23, 1009, 21], [1142, 25, 1009, 23], [1143, 14, 1010, 12], [1143, 15, 1010, 13], [1144, 12, 1010, 15], [1145, 14, 1010, 15, "fileName"], [1145, 22, 1010, 15], [1145, 24, 1010, 15, "_jsxFileName"], [1145, 36, 1010, 15], [1146, 14, 1010, 15, "lineNumber"], [1146, 24, 1010, 15], [1147, 14, 1010, 15, "columnNumber"], [1147, 26, 1010, 15], [1148, 12, 1010, 15], [1148, 19, 1010, 17], [1148, 20, 1010, 18], [1148, 22, 1012, 13, "__DEV__"], [1148, 29, 1012, 20], [1148, 46, 1013, 14], [1148, 50, 1013, 14, "_jsxDevRuntime"], [1148, 64, 1013, 14], [1148, 65, 1013, 14, "jsxDEV"], [1148, 71, 1013, 14], [1148, 73, 1013, 15, "_View"], [1148, 78, 1013, 15], [1148, 79, 1013, 15, "default"], [1148, 86, 1013, 19], [1149, 14, 1013, 20, "style"], [1149, 19, 1013, 25], [1149, 21, 1013, 27, "styles"], [1149, 27, 1013, 33], [1149, 28, 1013, 34, "previewChip"], [1149, 39, 1013, 46], [1150, 14, 1013, 46, "children"], [1150, 22, 1013, 46], [1150, 37, 1014, 16], [1150, 41, 1014, 16, "_jsxDevRuntime"], [1150, 55, 1014, 16], [1150, 56, 1014, 16, "jsxDEV"], [1150, 62, 1014, 16], [1150, 64, 1014, 17, "_Text"], [1150, 69, 1014, 17], [1150, 70, 1014, 17, "default"], [1150, 77, 1014, 21], [1151, 16, 1014, 22, "style"], [1151, 21, 1014, 27], [1151, 23, 1014, 29, "styles"], [1151, 29, 1014, 35], [1151, 30, 1014, 36, "previewChipText"], [1151, 45, 1014, 52], [1152, 16, 1014, 52, "children"], [1152, 24, 1014, 52], [1152, 26, 1014, 53], [1153, 14, 1014, 73], [1154, 16, 1014, 73, "fileName"], [1154, 24, 1014, 73], [1154, 26, 1014, 73, "_jsxFileName"], [1154, 38, 1014, 73], [1155, 16, 1014, 73, "lineNumber"], [1155, 26, 1014, 73], [1156, 16, 1014, 73, "columnNumber"], [1156, 28, 1014, 73], [1157, 14, 1014, 73], [1157, 21, 1014, 79], [1158, 12, 1014, 80], [1159, 14, 1014, 80, "fileName"], [1159, 22, 1014, 80], [1159, 24, 1014, 80, "_jsxFileName"], [1159, 36, 1014, 80], [1160, 14, 1014, 80, "lineNumber"], [1160, 24, 1014, 80], [1161, 14, 1014, 80, "columnNumber"], [1161, 26, 1014, 80], [1162, 12, 1014, 80], [1162, 19, 1015, 20], [1162, 20, 1016, 13], [1163, 10, 1016, 13], [1164, 12, 1016, 13, "fileName"], [1164, 20, 1016, 13], [1164, 22, 1016, 13, "_jsxFileName"], [1164, 34, 1016, 13], [1165, 12, 1016, 13, "lineNumber"], [1165, 22, 1016, 13], [1166, 12, 1016, 13, "columnNumber"], [1166, 24, 1016, 13], [1167, 10, 1016, 13], [1167, 17, 1017, 18], [1167, 18, 1017, 19], [1168, 8, 1017, 19], [1168, 23, 1018, 12], [1168, 24, 1019, 9], [1168, 26, 1021, 9, "isCameraReady"], [1168, 39, 1021, 22], [1168, 56, 1022, 10], [1168, 60, 1022, 10, "_jsxDevRuntime"], [1168, 74, 1022, 10], [1168, 75, 1022, 10, "jsxDEV"], [1168, 81, 1022, 10], [1168, 83, 1022, 10, "_jsxDevRuntime"], [1168, 97, 1022, 10], [1168, 98, 1022, 10, "Fragment"], [1168, 106, 1022, 10], [1169, 10, 1022, 10, "children"], [1169, 18, 1022, 10], [1169, 34, 1024, 12], [1169, 38, 1024, 12, "_jsxDevRuntime"], [1169, 52, 1024, 12], [1169, 53, 1024, 12, "jsxDEV"], [1169, 59, 1024, 12], [1169, 61, 1024, 13, "_View"], [1169, 66, 1024, 13], [1169, 67, 1024, 13, "default"], [1169, 74, 1024, 17], [1170, 12, 1024, 18, "style"], [1170, 17, 1024, 23], [1170, 19, 1024, 25, "styles"], [1170, 25, 1024, 31], [1170, 26, 1024, 32, "headerOverlay"], [1170, 39, 1024, 46], [1171, 12, 1024, 46, "children"], [1171, 20, 1024, 46], [1171, 35, 1025, 14], [1171, 39, 1025, 14, "_jsxDevRuntime"], [1171, 53, 1025, 14], [1171, 54, 1025, 14, "jsxDEV"], [1171, 60, 1025, 14], [1171, 62, 1025, 15, "_View"], [1171, 67, 1025, 15], [1171, 68, 1025, 15, "default"], [1171, 75, 1025, 19], [1172, 14, 1025, 20, "style"], [1172, 19, 1025, 25], [1172, 21, 1025, 27, "styles"], [1172, 27, 1025, 33], [1172, 28, 1025, 34, "headerContent"], [1172, 41, 1025, 48], [1173, 14, 1025, 48, "children"], [1173, 22, 1025, 48], [1173, 38, 1026, 16], [1173, 42, 1026, 16, "_jsxDevRuntime"], [1173, 56, 1026, 16], [1173, 57, 1026, 16, "jsxDEV"], [1173, 63, 1026, 16], [1173, 65, 1026, 17, "_View"], [1173, 70, 1026, 17], [1173, 71, 1026, 17, "default"], [1173, 78, 1026, 21], [1174, 16, 1026, 22, "style"], [1174, 21, 1026, 27], [1174, 23, 1026, 29, "styles"], [1174, 29, 1026, 35], [1174, 30, 1026, 36, "headerLeft"], [1174, 40, 1026, 47], [1175, 16, 1026, 47, "children"], [1175, 24, 1026, 47], [1175, 40, 1027, 18], [1175, 44, 1027, 18, "_jsxDevRuntime"], [1175, 58, 1027, 18], [1175, 59, 1027, 18, "jsxDEV"], [1175, 65, 1027, 18], [1175, 67, 1027, 19, "_Text"], [1175, 72, 1027, 19], [1175, 73, 1027, 19, "default"], [1175, 80, 1027, 23], [1176, 18, 1027, 24, "style"], [1176, 23, 1027, 29], [1176, 25, 1027, 31, "styles"], [1176, 31, 1027, 37], [1176, 32, 1027, 38, "headerTitle"], [1176, 43, 1027, 50], [1177, 18, 1027, 50, "children"], [1177, 26, 1027, 50], [1177, 28, 1027, 51], [1178, 16, 1027, 62], [1179, 18, 1027, 62, "fileName"], [1179, 26, 1027, 62], [1179, 28, 1027, 62, "_jsxFileName"], [1179, 40, 1027, 62], [1180, 18, 1027, 62, "lineNumber"], [1180, 28, 1027, 62], [1181, 18, 1027, 62, "columnNumber"], [1181, 30, 1027, 62], [1182, 16, 1027, 62], [1182, 23, 1027, 68], [1182, 24, 1027, 69], [1182, 39, 1028, 18], [1182, 43, 1028, 18, "_jsxDevRuntime"], [1182, 57, 1028, 18], [1182, 58, 1028, 18, "jsxDEV"], [1182, 64, 1028, 18], [1182, 66, 1028, 19, "_View"], [1182, 71, 1028, 19], [1182, 72, 1028, 19, "default"], [1182, 79, 1028, 23], [1183, 18, 1028, 24, "style"], [1183, 23, 1028, 29], [1183, 25, 1028, 31, "styles"], [1183, 31, 1028, 37], [1183, 32, 1028, 38, "subtitleRow"], [1183, 43, 1028, 50], [1184, 18, 1028, 50, "children"], [1184, 26, 1028, 50], [1184, 42, 1029, 20], [1184, 46, 1029, 20, "_jsxDevRuntime"], [1184, 60, 1029, 20], [1184, 61, 1029, 20, "jsxDEV"], [1184, 67, 1029, 20], [1184, 69, 1029, 21, "_Text"], [1184, 74, 1029, 21], [1184, 75, 1029, 21, "default"], [1184, 82, 1029, 25], [1185, 20, 1029, 26, "style"], [1185, 25, 1029, 31], [1185, 27, 1029, 33, "styles"], [1185, 33, 1029, 39], [1185, 34, 1029, 40, "webIcon"], [1185, 41, 1029, 48], [1186, 20, 1029, 48, "children"], [1186, 28, 1029, 48], [1186, 30, 1029, 49], [1187, 18, 1029, 51], [1188, 20, 1029, 51, "fileName"], [1188, 28, 1029, 51], [1188, 30, 1029, 51, "_jsxFileName"], [1188, 42, 1029, 51], [1189, 20, 1029, 51, "lineNumber"], [1189, 30, 1029, 51], [1190, 20, 1029, 51, "columnNumber"], [1190, 32, 1029, 51], [1191, 18, 1029, 51], [1191, 25, 1029, 57], [1191, 26, 1029, 58], [1191, 41, 1030, 20], [1191, 45, 1030, 20, "_jsxDevRuntime"], [1191, 59, 1030, 20], [1191, 60, 1030, 20, "jsxDEV"], [1191, 66, 1030, 20], [1191, 68, 1030, 21, "_Text"], [1191, 73, 1030, 21], [1191, 74, 1030, 21, "default"], [1191, 81, 1030, 25], [1192, 20, 1030, 26, "style"], [1192, 25, 1030, 31], [1192, 27, 1030, 33, "styles"], [1192, 33, 1030, 39], [1192, 34, 1030, 40, "headerSubtitle"], [1192, 48, 1030, 55], [1193, 20, 1030, 55, "children"], [1193, 28, 1030, 55], [1193, 30, 1030, 56], [1194, 18, 1030, 71], [1195, 20, 1030, 71, "fileName"], [1195, 28, 1030, 71], [1195, 30, 1030, 71, "_jsxFileName"], [1195, 42, 1030, 71], [1196, 20, 1030, 71, "lineNumber"], [1196, 30, 1030, 71], [1197, 20, 1030, 71, "columnNumber"], [1197, 32, 1030, 71], [1198, 18, 1030, 71], [1198, 25, 1030, 77], [1198, 26, 1030, 78], [1199, 16, 1030, 78], [1200, 18, 1030, 78, "fileName"], [1200, 26, 1030, 78], [1200, 28, 1030, 78, "_jsxFileName"], [1200, 40, 1030, 78], [1201, 18, 1030, 78, "lineNumber"], [1201, 28, 1030, 78], [1202, 18, 1030, 78, "columnNumber"], [1202, 30, 1030, 78], [1203, 16, 1030, 78], [1203, 23, 1031, 24], [1203, 24, 1031, 25], [1203, 26, 1032, 19, "challengeCode"], [1203, 39, 1032, 32], [1203, 56, 1033, 20], [1203, 60, 1033, 20, "_jsxDevRuntime"], [1203, 74, 1033, 20], [1203, 75, 1033, 20, "jsxDEV"], [1203, 81, 1033, 20], [1203, 83, 1033, 21, "_View"], [1203, 88, 1033, 21], [1203, 89, 1033, 21, "default"], [1203, 96, 1033, 25], [1204, 18, 1033, 26, "style"], [1204, 23, 1033, 31], [1204, 25, 1033, 33, "styles"], [1204, 31, 1033, 39], [1204, 32, 1033, 40, "challengeRow"], [1204, 44, 1033, 53], [1205, 18, 1033, 53, "children"], [1205, 26, 1033, 53], [1205, 42, 1034, 22], [1205, 46, 1034, 22, "_jsxDevRuntime"], [1205, 60, 1034, 22], [1205, 61, 1034, 22, "jsxDEV"], [1205, 67, 1034, 22], [1205, 69, 1034, 23, "_lucideReactNative"], [1205, 87, 1034, 23], [1205, 88, 1034, 23, "Shield"], [1205, 94, 1034, 29], [1206, 20, 1034, 30, "size"], [1206, 24, 1034, 34], [1206, 26, 1034, 36], [1206, 28, 1034, 39], [1207, 20, 1034, 40, "color"], [1207, 25, 1034, 45], [1207, 27, 1034, 46], [1208, 18, 1034, 52], [1209, 20, 1034, 52, "fileName"], [1209, 28, 1034, 52], [1209, 30, 1034, 52, "_jsxFileName"], [1209, 42, 1034, 52], [1210, 20, 1034, 52, "lineNumber"], [1210, 30, 1034, 52], [1211, 20, 1034, 52, "columnNumber"], [1211, 32, 1034, 52], [1212, 18, 1034, 52], [1212, 25, 1034, 54], [1212, 26, 1034, 55], [1212, 41, 1035, 22], [1212, 45, 1035, 22, "_jsxDevRuntime"], [1212, 59, 1035, 22], [1212, 60, 1035, 22, "jsxDEV"], [1212, 66, 1035, 22], [1212, 68, 1035, 23, "_Text"], [1212, 73, 1035, 23], [1212, 74, 1035, 23, "default"], [1212, 81, 1035, 27], [1213, 20, 1035, 28, "style"], [1213, 25, 1035, 33], [1213, 27, 1035, 35, "styles"], [1213, 33, 1035, 41], [1213, 34, 1035, 42, "challengeCode"], [1213, 47, 1035, 56], [1214, 20, 1035, 56, "children"], [1214, 28, 1035, 56], [1214, 30, 1035, 58, "challengeCode"], [1215, 18, 1035, 71], [1216, 20, 1035, 71, "fileName"], [1216, 28, 1035, 71], [1216, 30, 1035, 71, "_jsxFileName"], [1216, 42, 1035, 71], [1217, 20, 1035, 71, "lineNumber"], [1217, 30, 1035, 71], [1218, 20, 1035, 71, "columnNumber"], [1218, 32, 1035, 71], [1219, 18, 1035, 71], [1219, 25, 1035, 78], [1219, 26, 1035, 79], [1220, 16, 1035, 79], [1221, 18, 1035, 79, "fileName"], [1221, 26, 1035, 79], [1221, 28, 1035, 79, "_jsxFileName"], [1221, 40, 1035, 79], [1222, 18, 1035, 79, "lineNumber"], [1222, 28, 1035, 79], [1223, 18, 1035, 79, "columnNumber"], [1223, 30, 1035, 79], [1224, 16, 1035, 79], [1224, 23, 1036, 26], [1224, 24, 1037, 19], [1225, 14, 1037, 19], [1226, 16, 1037, 19, "fileName"], [1226, 24, 1037, 19], [1226, 26, 1037, 19, "_jsxFileName"], [1226, 38, 1037, 19], [1227, 16, 1037, 19, "lineNumber"], [1227, 26, 1037, 19], [1228, 16, 1037, 19, "columnNumber"], [1228, 28, 1037, 19], [1229, 14, 1037, 19], [1229, 21, 1038, 22], [1229, 22, 1038, 23], [1229, 37, 1039, 16], [1229, 41, 1039, 16, "_jsxDevRuntime"], [1229, 55, 1039, 16], [1229, 56, 1039, 16, "jsxDEV"], [1229, 62, 1039, 16], [1229, 64, 1039, 17, "_TouchableOpacity"], [1229, 81, 1039, 17], [1229, 82, 1039, 17, "default"], [1229, 89, 1039, 33], [1230, 16, 1039, 34, "onPress"], [1230, 23, 1039, 41], [1230, 25, 1039, 43, "onCancel"], [1230, 33, 1039, 52], [1231, 16, 1039, 53, "style"], [1231, 21, 1039, 58], [1231, 23, 1039, 60, "styles"], [1231, 29, 1039, 66], [1231, 30, 1039, 67, "closeButton"], [1231, 41, 1039, 79], [1232, 16, 1039, 79, "children"], [1232, 24, 1039, 79], [1232, 39, 1040, 18], [1232, 43, 1040, 18, "_jsxDevRuntime"], [1232, 57, 1040, 18], [1232, 58, 1040, 18, "jsxDEV"], [1232, 64, 1040, 18], [1232, 66, 1040, 19, "_lucideReactNative"], [1232, 84, 1040, 19], [1232, 85, 1040, 19, "X"], [1232, 86, 1040, 20], [1233, 18, 1040, 21, "size"], [1233, 22, 1040, 25], [1233, 24, 1040, 27], [1233, 26, 1040, 30], [1234, 18, 1040, 31, "color"], [1234, 23, 1040, 36], [1234, 25, 1040, 37], [1235, 16, 1040, 43], [1236, 18, 1040, 43, "fileName"], [1236, 26, 1040, 43], [1236, 28, 1040, 43, "_jsxFileName"], [1236, 40, 1040, 43], [1237, 18, 1040, 43, "lineNumber"], [1237, 28, 1040, 43], [1238, 18, 1040, 43, "columnNumber"], [1238, 30, 1040, 43], [1239, 16, 1040, 43], [1239, 23, 1040, 45], [1240, 14, 1040, 46], [1241, 16, 1040, 46, "fileName"], [1241, 24, 1040, 46], [1241, 26, 1040, 46, "_jsxFileName"], [1241, 38, 1040, 46], [1242, 16, 1040, 46, "lineNumber"], [1242, 26, 1040, 46], [1243, 16, 1040, 46, "columnNumber"], [1243, 28, 1040, 46], [1244, 14, 1040, 46], [1244, 21, 1041, 34], [1244, 22, 1041, 35], [1245, 12, 1041, 35], [1246, 14, 1041, 35, "fileName"], [1246, 22, 1041, 35], [1246, 24, 1041, 35, "_jsxFileName"], [1246, 36, 1041, 35], [1247, 14, 1041, 35, "lineNumber"], [1247, 24, 1041, 35], [1248, 14, 1041, 35, "columnNumber"], [1248, 26, 1041, 35], [1249, 12, 1041, 35], [1249, 19, 1042, 20], [1250, 10, 1042, 21], [1251, 12, 1042, 21, "fileName"], [1251, 20, 1042, 21], [1251, 22, 1042, 21, "_jsxFileName"], [1251, 34, 1042, 21], [1252, 12, 1042, 21, "lineNumber"], [1252, 22, 1042, 21], [1253, 12, 1042, 21, "columnNumber"], [1253, 24, 1042, 21], [1254, 10, 1042, 21], [1254, 17, 1043, 18], [1254, 18, 1043, 19], [1254, 33, 1045, 12], [1254, 37, 1045, 12, "_jsxDevRuntime"], [1254, 51, 1045, 12], [1254, 52, 1045, 12, "jsxDEV"], [1254, 58, 1045, 12], [1254, 60, 1045, 13, "_View"], [1254, 65, 1045, 13], [1254, 66, 1045, 13, "default"], [1254, 73, 1045, 17], [1255, 12, 1045, 18, "style"], [1255, 17, 1045, 23], [1255, 19, 1045, 25, "styles"], [1255, 25, 1045, 31], [1255, 26, 1045, 32, "privacyNotice"], [1255, 39, 1045, 46], [1256, 12, 1045, 46, "children"], [1256, 20, 1045, 46], [1256, 36, 1046, 14], [1256, 40, 1046, 14, "_jsxDevRuntime"], [1256, 54, 1046, 14], [1256, 55, 1046, 14, "jsxDEV"], [1256, 61, 1046, 14], [1256, 63, 1046, 15, "_lucideReactNative"], [1256, 81, 1046, 15], [1256, 82, 1046, 15, "Shield"], [1256, 88, 1046, 21], [1257, 14, 1046, 22, "size"], [1257, 18, 1046, 26], [1257, 20, 1046, 28], [1257, 22, 1046, 31], [1258, 14, 1046, 32, "color"], [1258, 19, 1046, 37], [1258, 21, 1046, 38], [1259, 12, 1046, 47], [1260, 14, 1046, 47, "fileName"], [1260, 22, 1046, 47], [1260, 24, 1046, 47, "_jsxFileName"], [1260, 36, 1046, 47], [1261, 14, 1046, 47, "lineNumber"], [1261, 24, 1046, 47], [1262, 14, 1046, 47, "columnNumber"], [1262, 26, 1046, 47], [1263, 12, 1046, 47], [1263, 19, 1046, 49], [1263, 20, 1046, 50], [1263, 35, 1047, 14], [1263, 39, 1047, 14, "_jsxDevRuntime"], [1263, 53, 1047, 14], [1263, 54, 1047, 14, "jsxDEV"], [1263, 60, 1047, 14], [1263, 62, 1047, 15, "_Text"], [1263, 67, 1047, 15], [1263, 68, 1047, 15, "default"], [1263, 75, 1047, 19], [1264, 14, 1047, 20, "style"], [1264, 19, 1047, 25], [1264, 21, 1047, 27, "styles"], [1264, 27, 1047, 33], [1264, 28, 1047, 34, "privacyText"], [1264, 39, 1047, 46], [1265, 14, 1047, 46, "children"], [1265, 22, 1047, 46], [1265, 24, 1047, 47], [1266, 12, 1049, 14], [1267, 14, 1049, 14, "fileName"], [1267, 22, 1049, 14], [1267, 24, 1049, 14, "_jsxFileName"], [1267, 36, 1049, 14], [1268, 14, 1049, 14, "lineNumber"], [1268, 24, 1049, 14], [1269, 14, 1049, 14, "columnNumber"], [1269, 26, 1049, 14], [1270, 12, 1049, 14], [1270, 19, 1049, 20], [1270, 20, 1049, 21], [1271, 10, 1049, 21], [1272, 12, 1049, 21, "fileName"], [1272, 20, 1049, 21], [1272, 22, 1049, 21, "_jsxFileName"], [1272, 34, 1049, 21], [1273, 12, 1049, 21, "lineNumber"], [1273, 22, 1049, 21], [1274, 12, 1049, 21, "columnNumber"], [1274, 24, 1049, 21], [1275, 10, 1049, 21], [1275, 17, 1050, 18], [1275, 18, 1050, 19], [1275, 33, 1052, 12], [1275, 37, 1052, 12, "_jsxDevRuntime"], [1275, 51, 1052, 12], [1275, 52, 1052, 12, "jsxDEV"], [1275, 58, 1052, 12], [1275, 60, 1052, 13, "_View"], [1275, 65, 1052, 13], [1275, 66, 1052, 13, "default"], [1275, 73, 1052, 17], [1276, 12, 1052, 18, "style"], [1276, 17, 1052, 23], [1276, 19, 1052, 25, "styles"], [1276, 25, 1052, 31], [1276, 26, 1052, 32, "footer<PERSON><PERSON><PERSON>"], [1276, 39, 1052, 46], [1277, 12, 1052, 46, "children"], [1277, 20, 1052, 46], [1277, 36, 1053, 14], [1277, 40, 1053, 14, "_jsxDevRuntime"], [1277, 54, 1053, 14], [1277, 55, 1053, 14, "jsxDEV"], [1277, 61, 1053, 14], [1277, 63, 1053, 15, "_Text"], [1277, 68, 1053, 15], [1277, 69, 1053, 15, "default"], [1277, 76, 1053, 19], [1278, 14, 1053, 20, "style"], [1278, 19, 1053, 25], [1278, 21, 1053, 27, "styles"], [1278, 27, 1053, 33], [1278, 28, 1053, 34, "instruction"], [1278, 39, 1053, 46], [1279, 14, 1053, 46, "children"], [1279, 22, 1053, 46], [1279, 24, 1053, 47], [1280, 12, 1055, 14], [1281, 14, 1055, 14, "fileName"], [1281, 22, 1055, 14], [1281, 24, 1055, 14, "_jsxFileName"], [1281, 36, 1055, 14], [1282, 14, 1055, 14, "lineNumber"], [1282, 24, 1055, 14], [1283, 14, 1055, 14, "columnNumber"], [1283, 26, 1055, 14], [1284, 12, 1055, 14], [1284, 19, 1055, 20], [1284, 20, 1055, 21], [1284, 35, 1057, 14], [1284, 39, 1057, 14, "_jsxDevRuntime"], [1284, 53, 1057, 14], [1284, 54, 1057, 14, "jsxDEV"], [1284, 60, 1057, 14], [1284, 62, 1057, 15, "_TouchableOpacity"], [1284, 79, 1057, 15], [1284, 80, 1057, 15, "default"], [1284, 87, 1057, 31], [1285, 14, 1058, 16, "onPress"], [1285, 21, 1058, 23], [1285, 23, 1058, 25, "capturePhoto"], [1285, 35, 1058, 38], [1286, 14, 1059, 16, "disabled"], [1286, 22, 1059, 24], [1286, 24, 1059, 26, "processingState"], [1286, 39, 1059, 41], [1286, 44, 1059, 46], [1286, 50, 1059, 52], [1286, 54, 1059, 56], [1286, 55, 1059, 57, "isCameraReady"], [1286, 68, 1059, 71], [1287, 14, 1060, 16, "style"], [1287, 19, 1060, 21], [1287, 21, 1060, 23], [1287, 22, 1061, 18, "styles"], [1287, 28, 1061, 24], [1287, 29, 1061, 25, "shutterButton"], [1287, 42, 1061, 38], [1287, 44, 1062, 18, "processingState"], [1287, 59, 1062, 33], [1287, 64, 1062, 38], [1287, 70, 1062, 44], [1287, 74, 1062, 48, "styles"], [1287, 80, 1062, 54], [1287, 81, 1062, 55, "shutterButtonDisabled"], [1287, 102, 1062, 76], [1287, 103, 1063, 18], [1288, 14, 1063, 18, "children"], [1288, 22, 1063, 18], [1288, 24, 1065, 17, "processingState"], [1288, 39, 1065, 32], [1288, 44, 1065, 37], [1288, 50, 1065, 43], [1288, 66, 1066, 18], [1288, 70, 1066, 18, "_jsxDevRuntime"], [1288, 84, 1066, 18], [1288, 85, 1066, 18, "jsxDEV"], [1288, 91, 1066, 18], [1288, 93, 1066, 19, "_View"], [1288, 98, 1066, 19], [1288, 99, 1066, 19, "default"], [1288, 106, 1066, 23], [1289, 16, 1066, 24, "style"], [1289, 21, 1066, 29], [1289, 23, 1066, 31, "styles"], [1289, 29, 1066, 37], [1289, 30, 1066, 38, "shutterInner"], [1290, 14, 1066, 51], [1291, 16, 1066, 51, "fileName"], [1291, 24, 1066, 51], [1291, 26, 1066, 51, "_jsxFileName"], [1291, 38, 1066, 51], [1292, 16, 1066, 51, "lineNumber"], [1292, 26, 1066, 51], [1293, 16, 1066, 51, "columnNumber"], [1293, 28, 1066, 51], [1294, 14, 1066, 51], [1294, 21, 1066, 53], [1294, 22, 1066, 54], [1294, 38, 1068, 18], [1294, 42, 1068, 18, "_jsxDevRuntime"], [1294, 56, 1068, 18], [1294, 57, 1068, 18, "jsxDEV"], [1294, 63, 1068, 18], [1294, 65, 1068, 19, "_ActivityIndicator"], [1294, 83, 1068, 19], [1294, 84, 1068, 19, "default"], [1294, 91, 1068, 36], [1295, 16, 1068, 37, "size"], [1295, 20, 1068, 41], [1295, 22, 1068, 42], [1295, 29, 1068, 49], [1296, 16, 1068, 50, "color"], [1296, 21, 1068, 55], [1296, 23, 1068, 56], [1297, 14, 1068, 65], [1298, 16, 1068, 65, "fileName"], [1298, 24, 1068, 65], [1298, 26, 1068, 65, "_jsxFileName"], [1298, 38, 1068, 65], [1299, 16, 1068, 65, "lineNumber"], [1299, 26, 1068, 65], [1300, 16, 1068, 65, "columnNumber"], [1300, 28, 1068, 65], [1301, 14, 1068, 65], [1301, 21, 1068, 67], [1302, 12, 1069, 17], [1303, 14, 1069, 17, "fileName"], [1303, 22, 1069, 17], [1303, 24, 1069, 17, "_jsxFileName"], [1303, 36, 1069, 17], [1304, 14, 1069, 17, "lineNumber"], [1304, 24, 1069, 17], [1305, 14, 1069, 17, "columnNumber"], [1305, 26, 1069, 17], [1306, 12, 1069, 17], [1306, 19, 1070, 32], [1306, 20, 1070, 33], [1306, 35, 1071, 14], [1306, 39, 1071, 14, "_jsxDevRuntime"], [1306, 53, 1071, 14], [1306, 54, 1071, 14, "jsxDEV"], [1306, 60, 1071, 14], [1306, 62, 1071, 15, "_Text"], [1306, 67, 1071, 15], [1306, 68, 1071, 15, "default"], [1306, 75, 1071, 19], [1307, 14, 1071, 20, "style"], [1307, 19, 1071, 25], [1307, 21, 1071, 27, "styles"], [1307, 27, 1071, 33], [1307, 28, 1071, 34, "privacyNote"], [1307, 39, 1071, 46], [1308, 14, 1071, 46, "children"], [1308, 22, 1071, 46], [1308, 24, 1071, 47], [1309, 12, 1073, 14], [1310, 14, 1073, 14, "fileName"], [1310, 22, 1073, 14], [1310, 24, 1073, 14, "_jsxFileName"], [1310, 36, 1073, 14], [1311, 14, 1073, 14, "lineNumber"], [1311, 24, 1073, 14], [1312, 14, 1073, 14, "columnNumber"], [1312, 26, 1073, 14], [1313, 12, 1073, 14], [1313, 19, 1073, 20], [1313, 20, 1073, 21], [1314, 10, 1073, 21], [1315, 12, 1073, 21, "fileName"], [1315, 20, 1073, 21], [1315, 22, 1073, 21, "_jsxFileName"], [1315, 34, 1073, 21], [1316, 12, 1073, 21, "lineNumber"], [1316, 22, 1073, 21], [1317, 12, 1073, 21, "columnNumber"], [1317, 24, 1073, 21], [1318, 10, 1073, 21], [1318, 17, 1074, 18], [1318, 18, 1074, 19], [1319, 8, 1074, 19], [1319, 23, 1075, 12], [1319, 24, 1076, 9], [1320, 6, 1076, 9], [1321, 8, 1076, 9, "fileName"], [1321, 16, 1076, 9], [1321, 18, 1076, 9, "_jsxFileName"], [1321, 30, 1076, 9], [1322, 8, 1076, 9, "lineNumber"], [1322, 18, 1076, 9], [1323, 8, 1076, 9, "columnNumber"], [1323, 20, 1076, 9], [1324, 6, 1076, 9], [1324, 13, 1077, 12], [1324, 14, 1077, 13], [1324, 29, 1079, 6], [1324, 33, 1079, 6, "_jsxDevRuntime"], [1324, 47, 1079, 6], [1324, 48, 1079, 6, "jsxDEV"], [1324, 54, 1079, 6], [1324, 56, 1079, 7, "_Modal"], [1324, 62, 1079, 7], [1324, 63, 1079, 7, "default"], [1324, 70, 1079, 12], [1325, 8, 1080, 8, "visible"], [1325, 15, 1080, 15], [1325, 17, 1080, 17, "processingState"], [1325, 32, 1080, 32], [1325, 37, 1080, 37], [1325, 43, 1080, 43], [1325, 47, 1080, 47, "processingState"], [1325, 62, 1080, 62], [1325, 67, 1080, 67], [1325, 74, 1080, 75], [1326, 8, 1081, 8, "transparent"], [1326, 19, 1081, 19], [1327, 8, 1082, 8, "animationType"], [1327, 21, 1082, 21], [1327, 23, 1082, 22], [1327, 29, 1082, 28], [1328, 8, 1082, 28, "children"], [1328, 16, 1082, 28], [1328, 31, 1084, 8], [1328, 35, 1084, 8, "_jsxDevRuntime"], [1328, 49, 1084, 8], [1328, 50, 1084, 8, "jsxDEV"], [1328, 56, 1084, 8], [1328, 58, 1084, 9, "_View"], [1328, 63, 1084, 9], [1328, 64, 1084, 9, "default"], [1328, 71, 1084, 13], [1329, 10, 1084, 14, "style"], [1329, 15, 1084, 19], [1329, 17, 1084, 21, "styles"], [1329, 23, 1084, 27], [1329, 24, 1084, 28, "processingModal"], [1329, 39, 1084, 44], [1330, 10, 1084, 44, "children"], [1330, 18, 1084, 44], [1330, 33, 1085, 10], [1330, 37, 1085, 10, "_jsxDevRuntime"], [1330, 51, 1085, 10], [1330, 52, 1085, 10, "jsxDEV"], [1330, 58, 1085, 10], [1330, 60, 1085, 11, "_View"], [1330, 65, 1085, 11], [1330, 66, 1085, 11, "default"], [1330, 73, 1085, 15], [1331, 12, 1085, 16, "style"], [1331, 17, 1085, 21], [1331, 19, 1085, 23, "styles"], [1331, 25, 1085, 29], [1331, 26, 1085, 30, "processingContent"], [1331, 43, 1085, 48], [1332, 12, 1085, 48, "children"], [1332, 20, 1085, 48], [1332, 36, 1086, 12], [1332, 40, 1086, 12, "_jsxDevRuntime"], [1332, 54, 1086, 12], [1332, 55, 1086, 12, "jsxDEV"], [1332, 61, 1086, 12], [1332, 63, 1086, 13, "_ActivityIndicator"], [1332, 81, 1086, 13], [1332, 82, 1086, 13, "default"], [1332, 89, 1086, 30], [1333, 14, 1086, 31, "size"], [1333, 18, 1086, 35], [1333, 20, 1086, 36], [1333, 27, 1086, 43], [1334, 14, 1086, 44, "color"], [1334, 19, 1086, 49], [1334, 21, 1086, 50], [1335, 12, 1086, 59], [1336, 14, 1086, 59, "fileName"], [1336, 22, 1086, 59], [1336, 24, 1086, 59, "_jsxFileName"], [1336, 36, 1086, 59], [1337, 14, 1086, 59, "lineNumber"], [1337, 24, 1086, 59], [1338, 14, 1086, 59, "columnNumber"], [1338, 26, 1086, 59], [1339, 12, 1086, 59], [1339, 19, 1086, 61], [1339, 20, 1086, 62], [1339, 35, 1088, 12], [1339, 39, 1088, 12, "_jsxDevRuntime"], [1339, 53, 1088, 12], [1339, 54, 1088, 12, "jsxDEV"], [1339, 60, 1088, 12], [1339, 62, 1088, 13, "_Text"], [1339, 67, 1088, 13], [1339, 68, 1088, 13, "default"], [1339, 75, 1088, 17], [1340, 14, 1088, 18, "style"], [1340, 19, 1088, 23], [1340, 21, 1088, 25, "styles"], [1340, 27, 1088, 31], [1340, 28, 1088, 32, "processingTitle"], [1340, 43, 1088, 48], [1341, 14, 1088, 48, "children"], [1341, 22, 1088, 48], [1341, 25, 1089, 15, "processingState"], [1341, 40, 1089, 30], [1341, 45, 1089, 35], [1341, 56, 1089, 46], [1341, 60, 1089, 50], [1341, 80, 1089, 70], [1341, 82, 1090, 15, "processingState"], [1341, 97, 1090, 30], [1341, 102, 1090, 35], [1341, 113, 1090, 46], [1341, 117, 1090, 50], [1341, 146, 1090, 79], [1341, 148, 1091, 15, "processingState"], [1341, 163, 1091, 30], [1341, 168, 1091, 35], [1341, 180, 1091, 47], [1341, 184, 1091, 51], [1341, 216, 1091, 83], [1341, 218, 1092, 15, "processingState"], [1341, 233, 1092, 30], [1341, 238, 1092, 35], [1341, 249, 1092, 46], [1341, 253, 1092, 50], [1341, 275, 1092, 72], [1342, 12, 1092, 72], [1343, 14, 1092, 72, "fileName"], [1343, 22, 1092, 72], [1343, 24, 1092, 72, "_jsxFileName"], [1343, 36, 1092, 72], [1344, 14, 1092, 72, "lineNumber"], [1344, 24, 1092, 72], [1345, 14, 1092, 72, "columnNumber"], [1345, 26, 1092, 72], [1346, 12, 1092, 72], [1346, 19, 1093, 18], [1346, 20, 1093, 19], [1346, 35, 1094, 12], [1346, 39, 1094, 12, "_jsxDevRuntime"], [1346, 53, 1094, 12], [1346, 54, 1094, 12, "jsxDEV"], [1346, 60, 1094, 12], [1346, 62, 1094, 13, "_View"], [1346, 67, 1094, 13], [1346, 68, 1094, 13, "default"], [1346, 75, 1094, 17], [1347, 14, 1094, 18, "style"], [1347, 19, 1094, 23], [1347, 21, 1094, 25, "styles"], [1347, 27, 1094, 31], [1347, 28, 1094, 32, "progressBar"], [1347, 39, 1094, 44], [1348, 14, 1094, 44, "children"], [1348, 22, 1094, 44], [1348, 37, 1095, 14], [1348, 41, 1095, 14, "_jsxDevRuntime"], [1348, 55, 1095, 14], [1348, 56, 1095, 14, "jsxDEV"], [1348, 62, 1095, 14], [1348, 64, 1095, 15, "_View"], [1348, 69, 1095, 15], [1348, 70, 1095, 15, "default"], [1348, 77, 1095, 19], [1349, 16, 1096, 16, "style"], [1349, 21, 1096, 21], [1349, 23, 1096, 23], [1349, 24, 1097, 18, "styles"], [1349, 30, 1097, 24], [1349, 31, 1097, 25, "progressFill"], [1349, 43, 1097, 37], [1349, 45, 1098, 18], [1350, 18, 1098, 20, "width"], [1350, 23, 1098, 25], [1350, 25, 1098, 27], [1350, 28, 1098, 30, "processingProgress"], [1350, 46, 1098, 48], [1351, 16, 1098, 52], [1351, 17, 1098, 53], [1352, 14, 1099, 18], [1353, 16, 1099, 18, "fileName"], [1353, 24, 1099, 18], [1353, 26, 1099, 18, "_jsxFileName"], [1353, 38, 1099, 18], [1354, 16, 1099, 18, "lineNumber"], [1354, 26, 1099, 18], [1355, 16, 1099, 18, "columnNumber"], [1355, 28, 1099, 18], [1356, 14, 1099, 18], [1356, 21, 1100, 15], [1357, 12, 1100, 16], [1358, 14, 1100, 16, "fileName"], [1358, 22, 1100, 16], [1358, 24, 1100, 16, "_jsxFileName"], [1358, 36, 1100, 16], [1359, 14, 1100, 16, "lineNumber"], [1359, 24, 1100, 16], [1360, 14, 1100, 16, "columnNumber"], [1360, 26, 1100, 16], [1361, 12, 1100, 16], [1361, 19, 1101, 18], [1361, 20, 1101, 19], [1361, 35, 1102, 12], [1361, 39, 1102, 12, "_jsxDevRuntime"], [1361, 53, 1102, 12], [1361, 54, 1102, 12, "jsxDEV"], [1361, 60, 1102, 12], [1361, 62, 1102, 13, "_Text"], [1361, 67, 1102, 13], [1361, 68, 1102, 13, "default"], [1361, 75, 1102, 17], [1362, 14, 1102, 18, "style"], [1362, 19, 1102, 23], [1362, 21, 1102, 25, "styles"], [1362, 27, 1102, 31], [1362, 28, 1102, 32, "processingDescription"], [1362, 49, 1102, 54], [1363, 14, 1102, 54, "children"], [1363, 22, 1102, 54], [1363, 25, 1103, 15, "processingState"], [1363, 40, 1103, 30], [1363, 45, 1103, 35], [1363, 56, 1103, 46], [1363, 60, 1103, 50], [1363, 89, 1103, 79], [1363, 91, 1104, 15, "processingState"], [1363, 106, 1104, 30], [1363, 111, 1104, 35], [1363, 122, 1104, 46], [1363, 126, 1104, 50], [1363, 164, 1104, 88], [1363, 166, 1105, 15, "processingState"], [1363, 181, 1105, 30], [1363, 186, 1105, 35], [1363, 198, 1105, 47], [1363, 202, 1105, 51], [1363, 247, 1105, 96], [1363, 249, 1106, 15, "processingState"], [1363, 264, 1106, 30], [1363, 269, 1106, 35], [1363, 280, 1106, 46], [1363, 284, 1106, 50], [1363, 325, 1106, 91], [1364, 12, 1106, 91], [1365, 14, 1106, 91, "fileName"], [1365, 22, 1106, 91], [1365, 24, 1106, 91, "_jsxFileName"], [1365, 36, 1106, 91], [1366, 14, 1106, 91, "lineNumber"], [1366, 24, 1106, 91], [1367, 14, 1106, 91, "columnNumber"], [1367, 26, 1106, 91], [1368, 12, 1106, 91], [1368, 19, 1107, 18], [1368, 20, 1107, 19], [1368, 22, 1108, 13, "processingState"], [1368, 37, 1108, 28], [1368, 42, 1108, 33], [1368, 53, 1108, 44], [1368, 70, 1109, 14], [1368, 74, 1109, 14, "_jsxDevRuntime"], [1368, 88, 1109, 14], [1368, 89, 1109, 14, "jsxDEV"], [1368, 95, 1109, 14], [1368, 97, 1109, 15, "_lucideReactNative"], [1368, 115, 1109, 15], [1368, 116, 1109, 15, "CheckCircle"], [1368, 127, 1109, 26], [1369, 14, 1109, 27, "size"], [1369, 18, 1109, 31], [1369, 20, 1109, 33], [1369, 22, 1109, 36], [1370, 14, 1109, 37, "color"], [1370, 19, 1109, 42], [1370, 21, 1109, 43], [1370, 30, 1109, 52], [1371, 14, 1109, 53, "style"], [1371, 19, 1109, 58], [1371, 21, 1109, 60, "styles"], [1371, 27, 1109, 66], [1371, 28, 1109, 67, "successIcon"], [1372, 12, 1109, 79], [1373, 14, 1109, 79, "fileName"], [1373, 22, 1109, 79], [1373, 24, 1109, 79, "_jsxFileName"], [1373, 36, 1109, 79], [1374, 14, 1109, 79, "lineNumber"], [1374, 24, 1109, 79], [1375, 14, 1109, 79, "columnNumber"], [1375, 26, 1109, 79], [1376, 12, 1109, 79], [1376, 19, 1109, 81], [1376, 20, 1110, 13], [1377, 10, 1110, 13], [1378, 12, 1110, 13, "fileName"], [1378, 20, 1110, 13], [1378, 22, 1110, 13, "_jsxFileName"], [1378, 34, 1110, 13], [1379, 12, 1110, 13, "lineNumber"], [1379, 22, 1110, 13], [1380, 12, 1110, 13, "columnNumber"], [1380, 24, 1110, 13], [1381, 10, 1110, 13], [1381, 17, 1111, 16], [1382, 8, 1111, 17], [1383, 10, 1111, 17, "fileName"], [1383, 18, 1111, 17], [1383, 20, 1111, 17, "_jsxFileName"], [1383, 32, 1111, 17], [1384, 10, 1111, 17, "lineNumber"], [1384, 20, 1111, 17], [1385, 10, 1111, 17, "columnNumber"], [1385, 22, 1111, 17], [1386, 8, 1111, 17], [1386, 15, 1112, 14], [1387, 6, 1112, 15], [1388, 8, 1112, 15, "fileName"], [1388, 16, 1112, 15], [1388, 18, 1112, 15, "_jsxFileName"], [1388, 30, 1112, 15], [1389, 8, 1112, 15, "lineNumber"], [1389, 18, 1112, 15], [1390, 8, 1112, 15, "columnNumber"], [1390, 20, 1112, 15], [1391, 6, 1112, 15], [1391, 13, 1113, 13], [1391, 14, 1113, 14], [1391, 29, 1115, 6], [1391, 33, 1115, 6, "_jsxDevRuntime"], [1391, 47, 1115, 6], [1391, 48, 1115, 6, "jsxDEV"], [1391, 54, 1115, 6], [1391, 56, 1115, 7, "_Modal"], [1391, 62, 1115, 7], [1391, 63, 1115, 7, "default"], [1391, 70, 1115, 12], [1392, 8, 1116, 8, "visible"], [1392, 15, 1116, 15], [1392, 17, 1116, 17, "processingState"], [1392, 32, 1116, 32], [1392, 37, 1116, 37], [1392, 44, 1116, 45], [1393, 8, 1117, 8, "transparent"], [1393, 19, 1117, 19], [1394, 8, 1118, 8, "animationType"], [1394, 21, 1118, 21], [1394, 23, 1118, 22], [1394, 29, 1118, 28], [1395, 8, 1118, 28, "children"], [1395, 16, 1118, 28], [1395, 31, 1120, 8], [1395, 35, 1120, 8, "_jsxDevRuntime"], [1395, 49, 1120, 8], [1395, 50, 1120, 8, "jsxDEV"], [1395, 56, 1120, 8], [1395, 58, 1120, 9, "_View"], [1395, 63, 1120, 9], [1395, 64, 1120, 9, "default"], [1395, 71, 1120, 13], [1396, 10, 1120, 14, "style"], [1396, 15, 1120, 19], [1396, 17, 1120, 21, "styles"], [1396, 23, 1120, 27], [1396, 24, 1120, 28, "processingModal"], [1396, 39, 1120, 44], [1397, 10, 1120, 44, "children"], [1397, 18, 1120, 44], [1397, 33, 1121, 10], [1397, 37, 1121, 10, "_jsxDevRuntime"], [1397, 51, 1121, 10], [1397, 52, 1121, 10, "jsxDEV"], [1397, 58, 1121, 10], [1397, 60, 1121, 11, "_View"], [1397, 65, 1121, 11], [1397, 66, 1121, 11, "default"], [1397, 73, 1121, 15], [1398, 12, 1121, 16, "style"], [1398, 17, 1121, 21], [1398, 19, 1121, 23, "styles"], [1398, 25, 1121, 29], [1398, 26, 1121, 30, "errorContent"], [1398, 38, 1121, 43], [1399, 12, 1121, 43, "children"], [1399, 20, 1121, 43], [1399, 36, 1122, 12], [1399, 40, 1122, 12, "_jsxDevRuntime"], [1399, 54, 1122, 12], [1399, 55, 1122, 12, "jsxDEV"], [1399, 61, 1122, 12], [1399, 63, 1122, 13, "_lucideReactNative"], [1399, 81, 1122, 13], [1399, 82, 1122, 13, "X"], [1399, 83, 1122, 14], [1400, 14, 1122, 15, "size"], [1400, 18, 1122, 19], [1400, 20, 1122, 21], [1400, 22, 1122, 24], [1401, 14, 1122, 25, "color"], [1401, 19, 1122, 30], [1401, 21, 1122, 31], [1402, 12, 1122, 40], [1403, 14, 1122, 40, "fileName"], [1403, 22, 1122, 40], [1403, 24, 1122, 40, "_jsxFileName"], [1403, 36, 1122, 40], [1404, 14, 1122, 40, "lineNumber"], [1404, 24, 1122, 40], [1405, 14, 1122, 40, "columnNumber"], [1405, 26, 1122, 40], [1406, 12, 1122, 40], [1406, 19, 1122, 42], [1406, 20, 1122, 43], [1406, 35, 1123, 12], [1406, 39, 1123, 12, "_jsxDevRuntime"], [1406, 53, 1123, 12], [1406, 54, 1123, 12, "jsxDEV"], [1406, 60, 1123, 12], [1406, 62, 1123, 13, "_Text"], [1406, 67, 1123, 13], [1406, 68, 1123, 13, "default"], [1406, 75, 1123, 17], [1407, 14, 1123, 18, "style"], [1407, 19, 1123, 23], [1407, 21, 1123, 25, "styles"], [1407, 27, 1123, 31], [1407, 28, 1123, 32, "errorTitle"], [1407, 38, 1123, 43], [1408, 14, 1123, 43, "children"], [1408, 22, 1123, 43], [1408, 24, 1123, 44], [1409, 12, 1123, 61], [1410, 14, 1123, 61, "fileName"], [1410, 22, 1123, 61], [1410, 24, 1123, 61, "_jsxFileName"], [1410, 36, 1123, 61], [1411, 14, 1123, 61, "lineNumber"], [1411, 24, 1123, 61], [1412, 14, 1123, 61, "columnNumber"], [1412, 26, 1123, 61], [1413, 12, 1123, 61], [1413, 19, 1123, 67], [1413, 20, 1123, 68], [1413, 35, 1124, 12], [1413, 39, 1124, 12, "_jsxDevRuntime"], [1413, 53, 1124, 12], [1413, 54, 1124, 12, "jsxDEV"], [1413, 60, 1124, 12], [1413, 62, 1124, 13, "_Text"], [1413, 67, 1124, 13], [1413, 68, 1124, 13, "default"], [1413, 75, 1124, 17], [1414, 14, 1124, 18, "style"], [1414, 19, 1124, 23], [1414, 21, 1124, 25, "styles"], [1414, 27, 1124, 31], [1414, 28, 1124, 32, "errorMessage"], [1414, 40, 1124, 45], [1415, 14, 1124, 45, "children"], [1415, 22, 1124, 45], [1415, 24, 1124, 47, "errorMessage"], [1416, 12, 1124, 59], [1417, 14, 1124, 59, "fileName"], [1417, 22, 1124, 59], [1417, 24, 1124, 59, "_jsxFileName"], [1417, 36, 1124, 59], [1418, 14, 1124, 59, "lineNumber"], [1418, 24, 1124, 59], [1419, 14, 1124, 59, "columnNumber"], [1419, 26, 1124, 59], [1420, 12, 1124, 59], [1420, 19, 1124, 66], [1420, 20, 1124, 67], [1420, 35, 1125, 12], [1420, 39, 1125, 12, "_jsxDevRuntime"], [1420, 53, 1125, 12], [1420, 54, 1125, 12, "jsxDEV"], [1420, 60, 1125, 12], [1420, 62, 1125, 13, "_TouchableOpacity"], [1420, 79, 1125, 13], [1420, 80, 1125, 13, "default"], [1420, 87, 1125, 29], [1421, 14, 1126, 14, "onPress"], [1421, 21, 1126, 21], [1421, 23, 1126, 23, "retryCapture"], [1421, 35, 1126, 36], [1422, 14, 1127, 14, "style"], [1422, 19, 1127, 19], [1422, 21, 1127, 21, "styles"], [1422, 27, 1127, 27], [1422, 28, 1127, 28, "primaryButton"], [1422, 41, 1127, 42], [1423, 14, 1127, 42, "children"], [1423, 22, 1127, 42], [1423, 37, 1129, 14], [1423, 41, 1129, 14, "_jsxDevRuntime"], [1423, 55, 1129, 14], [1423, 56, 1129, 14, "jsxDEV"], [1423, 62, 1129, 14], [1423, 64, 1129, 15, "_Text"], [1423, 69, 1129, 15], [1423, 70, 1129, 15, "default"], [1423, 77, 1129, 19], [1424, 16, 1129, 20, "style"], [1424, 21, 1129, 25], [1424, 23, 1129, 27, "styles"], [1424, 29, 1129, 33], [1424, 30, 1129, 34, "primaryButtonText"], [1424, 47, 1129, 52], [1425, 16, 1129, 52, "children"], [1425, 24, 1129, 52], [1425, 26, 1129, 53], [1426, 14, 1129, 62], [1427, 16, 1129, 62, "fileName"], [1427, 24, 1129, 62], [1427, 26, 1129, 62, "_jsxFileName"], [1427, 38, 1129, 62], [1428, 16, 1129, 62, "lineNumber"], [1428, 26, 1129, 62], [1429, 16, 1129, 62, "columnNumber"], [1429, 28, 1129, 62], [1430, 14, 1129, 62], [1430, 21, 1129, 68], [1431, 12, 1129, 69], [1432, 14, 1129, 69, "fileName"], [1432, 22, 1129, 69], [1432, 24, 1129, 69, "_jsxFileName"], [1432, 36, 1129, 69], [1433, 14, 1129, 69, "lineNumber"], [1433, 24, 1129, 69], [1434, 14, 1129, 69, "columnNumber"], [1434, 26, 1129, 69], [1435, 12, 1129, 69], [1435, 19, 1130, 30], [1435, 20, 1130, 31], [1435, 35, 1131, 12], [1435, 39, 1131, 12, "_jsxDevRuntime"], [1435, 53, 1131, 12], [1435, 54, 1131, 12, "jsxDEV"], [1435, 60, 1131, 12], [1435, 62, 1131, 13, "_TouchableOpacity"], [1435, 79, 1131, 13], [1435, 80, 1131, 13, "default"], [1435, 87, 1131, 29], [1436, 14, 1132, 14, "onPress"], [1436, 21, 1132, 21], [1436, 23, 1132, 23, "onCancel"], [1436, 31, 1132, 32], [1437, 14, 1133, 14, "style"], [1437, 19, 1133, 19], [1437, 21, 1133, 21, "styles"], [1437, 27, 1133, 27], [1437, 28, 1133, 28, "secondaryButton"], [1437, 43, 1133, 44], [1438, 14, 1133, 44, "children"], [1438, 22, 1133, 44], [1438, 37, 1135, 14], [1438, 41, 1135, 14, "_jsxDevRuntime"], [1438, 55, 1135, 14], [1438, 56, 1135, 14, "jsxDEV"], [1438, 62, 1135, 14], [1438, 64, 1135, 15, "_Text"], [1438, 69, 1135, 15], [1438, 70, 1135, 15, "default"], [1438, 77, 1135, 19], [1439, 16, 1135, 20, "style"], [1439, 21, 1135, 25], [1439, 23, 1135, 27, "styles"], [1439, 29, 1135, 33], [1439, 30, 1135, 34, "secondaryButtonText"], [1439, 49, 1135, 54], [1440, 16, 1135, 54, "children"], [1440, 24, 1135, 54], [1440, 26, 1135, 55], [1441, 14, 1135, 61], [1442, 16, 1135, 61, "fileName"], [1442, 24, 1135, 61], [1442, 26, 1135, 61, "_jsxFileName"], [1442, 38, 1135, 61], [1443, 16, 1135, 61, "lineNumber"], [1443, 26, 1135, 61], [1444, 16, 1135, 61, "columnNumber"], [1444, 28, 1135, 61], [1445, 14, 1135, 61], [1445, 21, 1135, 67], [1446, 12, 1135, 68], [1447, 14, 1135, 68, "fileName"], [1447, 22, 1135, 68], [1447, 24, 1135, 68, "_jsxFileName"], [1447, 36, 1135, 68], [1448, 14, 1135, 68, "lineNumber"], [1448, 24, 1135, 68], [1449, 14, 1135, 68, "columnNumber"], [1449, 26, 1135, 68], [1450, 12, 1135, 68], [1450, 19, 1136, 30], [1450, 20, 1136, 31], [1451, 10, 1136, 31], [1452, 12, 1136, 31, "fileName"], [1452, 20, 1136, 31], [1452, 22, 1136, 31, "_jsxFileName"], [1452, 34, 1136, 31], [1453, 12, 1136, 31, "lineNumber"], [1453, 22, 1136, 31], [1454, 12, 1136, 31, "columnNumber"], [1454, 24, 1136, 31], [1455, 10, 1136, 31], [1455, 17, 1137, 16], [1456, 8, 1137, 17], [1457, 10, 1137, 17, "fileName"], [1457, 18, 1137, 17], [1457, 20, 1137, 17, "_jsxFileName"], [1457, 32, 1137, 17], [1458, 10, 1137, 17, "lineNumber"], [1458, 20, 1137, 17], [1459, 10, 1137, 17, "columnNumber"], [1459, 22, 1137, 17], [1460, 8, 1137, 17], [1460, 15, 1138, 14], [1461, 6, 1138, 15], [1462, 8, 1138, 15, "fileName"], [1462, 16, 1138, 15], [1462, 18, 1138, 15, "_jsxFileName"], [1462, 30, 1138, 15], [1463, 8, 1138, 15, "lineNumber"], [1463, 18, 1138, 15], [1464, 8, 1138, 15, "columnNumber"], [1464, 20, 1138, 15], [1465, 6, 1138, 15], [1465, 13, 1139, 13], [1465, 14, 1139, 14], [1466, 4, 1139, 14], [1467, 6, 1139, 14, "fileName"], [1467, 14, 1139, 14], [1467, 16, 1139, 14, "_jsxFileName"], [1467, 28, 1139, 14], [1468, 6, 1139, 14, "lineNumber"], [1468, 16, 1139, 14], [1469, 6, 1139, 14, "columnNumber"], [1469, 18, 1139, 14], [1470, 4, 1139, 14], [1470, 11, 1140, 10], [1470, 12, 1140, 11], [1471, 2, 1142, 0], [1472, 2, 1142, 1, "_s"], [1472, 4, 1142, 1], [1472, 5, 52, 24, "EchoCameraWeb"], [1472, 18, 52, 37], [1473, 4, 52, 37], [1473, 12, 59, 42, "useCameraPermissions"], [1473, 44, 59, 62], [1473, 46, 73, 19, "useUpload"], [1473, 64, 73, 28], [1474, 2, 73, 28], [1475, 2, 73, 28, "_c"], [1475, 4, 73, 28], [1475, 7, 52, 24, "EchoCameraWeb"], [1475, 20, 52, 37], [1476, 2, 1143, 0], [1476, 8, 1143, 6, "styles"], [1476, 14, 1143, 12], [1476, 17, 1143, 15, "StyleSheet"], [1476, 36, 1143, 25], [1476, 37, 1143, 26, "create"], [1476, 43, 1143, 32], [1476, 44, 1143, 33], [1477, 4, 1144, 2, "container"], [1477, 13, 1144, 11], [1477, 15, 1144, 13], [1478, 6, 1145, 4, "flex"], [1478, 10, 1145, 8], [1478, 12, 1145, 10], [1478, 13, 1145, 11], [1479, 6, 1146, 4, "backgroundColor"], [1479, 21, 1146, 19], [1479, 23, 1146, 21], [1480, 4, 1147, 2], [1480, 5, 1147, 3], [1481, 4, 1148, 2, "cameraContainer"], [1481, 19, 1148, 17], [1481, 21, 1148, 19], [1482, 6, 1149, 4, "flex"], [1482, 10, 1149, 8], [1482, 12, 1149, 10], [1482, 13, 1149, 11], [1483, 6, 1150, 4, "max<PERSON><PERSON><PERSON>"], [1483, 14, 1150, 12], [1483, 16, 1150, 14], [1483, 19, 1150, 17], [1484, 6, 1151, 4, "alignSelf"], [1484, 15, 1151, 13], [1484, 17, 1151, 15], [1484, 25, 1151, 23], [1485, 6, 1152, 4, "width"], [1485, 11, 1152, 9], [1485, 13, 1152, 11], [1486, 4, 1153, 2], [1486, 5, 1153, 3], [1487, 4, 1154, 2, "camera"], [1487, 10, 1154, 8], [1487, 12, 1154, 10], [1488, 6, 1155, 4, "flex"], [1488, 10, 1155, 8], [1488, 12, 1155, 10], [1489, 4, 1156, 2], [1489, 5, 1156, 3], [1490, 4, 1157, 2, "headerOverlay"], [1490, 17, 1157, 15], [1490, 19, 1157, 17], [1491, 6, 1158, 4, "position"], [1491, 14, 1158, 12], [1491, 16, 1158, 14], [1491, 26, 1158, 24], [1492, 6, 1159, 4, "top"], [1492, 9, 1159, 7], [1492, 11, 1159, 9], [1492, 12, 1159, 10], [1493, 6, 1160, 4, "left"], [1493, 10, 1160, 8], [1493, 12, 1160, 10], [1493, 13, 1160, 11], [1494, 6, 1161, 4, "right"], [1494, 11, 1161, 9], [1494, 13, 1161, 11], [1494, 14, 1161, 12], [1495, 6, 1162, 4, "backgroundColor"], [1495, 21, 1162, 19], [1495, 23, 1162, 21], [1495, 36, 1162, 34], [1496, 6, 1163, 4, "paddingTop"], [1496, 16, 1163, 14], [1496, 18, 1163, 16], [1496, 20, 1163, 18], [1497, 6, 1164, 4, "paddingHorizontal"], [1497, 23, 1164, 21], [1497, 25, 1164, 23], [1497, 27, 1164, 25], [1498, 6, 1165, 4, "paddingBottom"], [1498, 19, 1165, 17], [1498, 21, 1165, 19], [1499, 4, 1166, 2], [1499, 5, 1166, 3], [1500, 4, 1167, 2, "headerContent"], [1500, 17, 1167, 15], [1500, 19, 1167, 17], [1501, 6, 1168, 4, "flexDirection"], [1501, 19, 1168, 17], [1501, 21, 1168, 19], [1501, 26, 1168, 24], [1502, 6, 1169, 4, "justifyContent"], [1502, 20, 1169, 18], [1502, 22, 1169, 20], [1502, 37, 1169, 35], [1503, 6, 1170, 4, "alignItems"], [1503, 16, 1170, 14], [1503, 18, 1170, 16], [1504, 4, 1171, 2], [1504, 5, 1171, 3], [1505, 4, 1172, 2, "headerLeft"], [1505, 14, 1172, 12], [1505, 16, 1172, 14], [1506, 6, 1173, 4, "flex"], [1506, 10, 1173, 8], [1506, 12, 1173, 10], [1507, 4, 1174, 2], [1507, 5, 1174, 3], [1508, 4, 1175, 2, "headerTitle"], [1508, 15, 1175, 13], [1508, 17, 1175, 15], [1509, 6, 1176, 4, "fontSize"], [1509, 14, 1176, 12], [1509, 16, 1176, 14], [1509, 18, 1176, 16], [1510, 6, 1177, 4, "fontWeight"], [1510, 16, 1177, 14], [1510, 18, 1177, 16], [1510, 23, 1177, 21], [1511, 6, 1178, 4, "color"], [1511, 11, 1178, 9], [1511, 13, 1178, 11], [1511, 19, 1178, 17], [1512, 6, 1179, 4, "marginBottom"], [1512, 18, 1179, 16], [1512, 20, 1179, 18], [1513, 4, 1180, 2], [1513, 5, 1180, 3], [1514, 4, 1181, 2, "subtitleRow"], [1514, 15, 1181, 13], [1514, 17, 1181, 15], [1515, 6, 1182, 4, "flexDirection"], [1515, 19, 1182, 17], [1515, 21, 1182, 19], [1515, 26, 1182, 24], [1516, 6, 1183, 4, "alignItems"], [1516, 16, 1183, 14], [1516, 18, 1183, 16], [1516, 26, 1183, 24], [1517, 6, 1184, 4, "marginBottom"], [1517, 18, 1184, 16], [1517, 20, 1184, 18], [1518, 4, 1185, 2], [1518, 5, 1185, 3], [1519, 4, 1186, 2, "webIcon"], [1519, 11, 1186, 9], [1519, 13, 1186, 11], [1520, 6, 1187, 4, "fontSize"], [1520, 14, 1187, 12], [1520, 16, 1187, 14], [1520, 18, 1187, 16], [1521, 6, 1188, 4, "marginRight"], [1521, 17, 1188, 15], [1521, 19, 1188, 17], [1522, 4, 1189, 2], [1522, 5, 1189, 3], [1523, 4, 1190, 2, "headerSubtitle"], [1523, 18, 1190, 16], [1523, 20, 1190, 18], [1524, 6, 1191, 4, "fontSize"], [1524, 14, 1191, 12], [1524, 16, 1191, 14], [1524, 18, 1191, 16], [1525, 6, 1192, 4, "color"], [1525, 11, 1192, 9], [1525, 13, 1192, 11], [1525, 19, 1192, 17], [1526, 6, 1193, 4, "opacity"], [1526, 13, 1193, 11], [1526, 15, 1193, 13], [1527, 4, 1194, 2], [1527, 5, 1194, 3], [1528, 4, 1195, 2, "challengeRow"], [1528, 16, 1195, 14], [1528, 18, 1195, 16], [1529, 6, 1196, 4, "flexDirection"], [1529, 19, 1196, 17], [1529, 21, 1196, 19], [1529, 26, 1196, 24], [1530, 6, 1197, 4, "alignItems"], [1530, 16, 1197, 14], [1530, 18, 1197, 16], [1531, 4, 1198, 2], [1531, 5, 1198, 3], [1532, 4, 1199, 2, "challengeCode"], [1532, 17, 1199, 15], [1532, 19, 1199, 17], [1533, 6, 1200, 4, "fontSize"], [1533, 14, 1200, 12], [1533, 16, 1200, 14], [1533, 18, 1200, 16], [1534, 6, 1201, 4, "color"], [1534, 11, 1201, 9], [1534, 13, 1201, 11], [1534, 19, 1201, 17], [1535, 6, 1202, 4, "marginLeft"], [1535, 16, 1202, 14], [1535, 18, 1202, 16], [1535, 19, 1202, 17], [1536, 6, 1203, 4, "fontFamily"], [1536, 16, 1203, 14], [1536, 18, 1203, 16], [1537, 4, 1204, 2], [1537, 5, 1204, 3], [1538, 4, 1205, 2, "closeButton"], [1538, 15, 1205, 13], [1538, 17, 1205, 15], [1539, 6, 1206, 4, "padding"], [1539, 13, 1206, 11], [1539, 15, 1206, 13], [1540, 4, 1207, 2], [1540, 5, 1207, 3], [1541, 4, 1208, 2, "privacyNotice"], [1541, 17, 1208, 15], [1541, 19, 1208, 17], [1542, 6, 1209, 4, "position"], [1542, 14, 1209, 12], [1542, 16, 1209, 14], [1542, 26, 1209, 24], [1543, 6, 1210, 4, "top"], [1543, 9, 1210, 7], [1543, 11, 1210, 9], [1543, 14, 1210, 12], [1544, 6, 1211, 4, "left"], [1544, 10, 1211, 8], [1544, 12, 1211, 10], [1544, 14, 1211, 12], [1545, 6, 1212, 4, "right"], [1545, 11, 1212, 9], [1545, 13, 1212, 11], [1545, 15, 1212, 13], [1546, 6, 1213, 4, "backgroundColor"], [1546, 21, 1213, 19], [1546, 23, 1213, 21], [1546, 48, 1213, 46], [1547, 6, 1214, 4, "borderRadius"], [1547, 18, 1214, 16], [1547, 20, 1214, 18], [1547, 21, 1214, 19], [1548, 6, 1215, 4, "padding"], [1548, 13, 1215, 11], [1548, 15, 1215, 13], [1548, 17, 1215, 15], [1549, 6, 1216, 4, "flexDirection"], [1549, 19, 1216, 17], [1549, 21, 1216, 19], [1549, 26, 1216, 24], [1550, 6, 1217, 4, "alignItems"], [1550, 16, 1217, 14], [1550, 18, 1217, 16], [1551, 4, 1218, 2], [1551, 5, 1218, 3], [1552, 4, 1219, 2, "privacyText"], [1552, 15, 1219, 13], [1552, 17, 1219, 15], [1553, 6, 1220, 4, "color"], [1553, 11, 1220, 9], [1553, 13, 1220, 11], [1553, 19, 1220, 17], [1554, 6, 1221, 4, "fontSize"], [1554, 14, 1221, 12], [1554, 16, 1221, 14], [1554, 18, 1221, 16], [1555, 6, 1222, 4, "marginLeft"], [1555, 16, 1222, 14], [1555, 18, 1222, 16], [1555, 19, 1222, 17], [1556, 6, 1223, 4, "flex"], [1556, 10, 1223, 8], [1556, 12, 1223, 10], [1557, 4, 1224, 2], [1557, 5, 1224, 3], [1558, 4, 1225, 2, "footer<PERSON><PERSON><PERSON>"], [1558, 17, 1225, 15], [1558, 19, 1225, 17], [1559, 6, 1226, 4, "position"], [1559, 14, 1226, 12], [1559, 16, 1226, 14], [1559, 26, 1226, 24], [1560, 6, 1227, 4, "bottom"], [1560, 12, 1227, 10], [1560, 14, 1227, 12], [1560, 15, 1227, 13], [1561, 6, 1228, 4, "left"], [1561, 10, 1228, 8], [1561, 12, 1228, 10], [1561, 13, 1228, 11], [1562, 6, 1229, 4, "right"], [1562, 11, 1229, 9], [1562, 13, 1229, 11], [1562, 14, 1229, 12], [1563, 6, 1230, 4, "backgroundColor"], [1563, 21, 1230, 19], [1563, 23, 1230, 21], [1563, 36, 1230, 34], [1564, 6, 1231, 4, "paddingBottom"], [1564, 19, 1231, 17], [1564, 21, 1231, 19], [1564, 23, 1231, 21], [1565, 6, 1232, 4, "paddingTop"], [1565, 16, 1232, 14], [1565, 18, 1232, 16], [1565, 20, 1232, 18], [1566, 6, 1233, 4, "alignItems"], [1566, 16, 1233, 14], [1566, 18, 1233, 16], [1567, 4, 1234, 2], [1567, 5, 1234, 3], [1568, 4, 1235, 2, "instruction"], [1568, 15, 1235, 13], [1568, 17, 1235, 15], [1569, 6, 1236, 4, "fontSize"], [1569, 14, 1236, 12], [1569, 16, 1236, 14], [1569, 18, 1236, 16], [1570, 6, 1237, 4, "color"], [1570, 11, 1237, 9], [1570, 13, 1237, 11], [1570, 19, 1237, 17], [1571, 6, 1238, 4, "marginBottom"], [1571, 18, 1238, 16], [1571, 20, 1238, 18], [1572, 4, 1239, 2], [1572, 5, 1239, 3], [1573, 4, 1240, 2, "shutterButton"], [1573, 17, 1240, 15], [1573, 19, 1240, 17], [1574, 6, 1241, 4, "width"], [1574, 11, 1241, 9], [1574, 13, 1241, 11], [1574, 15, 1241, 13], [1575, 6, 1242, 4, "height"], [1575, 12, 1242, 10], [1575, 14, 1242, 12], [1575, 16, 1242, 14], [1576, 6, 1243, 4, "borderRadius"], [1576, 18, 1243, 16], [1576, 20, 1243, 18], [1576, 22, 1243, 20], [1577, 6, 1244, 4, "backgroundColor"], [1577, 21, 1244, 19], [1577, 23, 1244, 21], [1577, 29, 1244, 27], [1578, 6, 1245, 4, "justifyContent"], [1578, 20, 1245, 18], [1578, 22, 1245, 20], [1578, 30, 1245, 28], [1579, 6, 1246, 4, "alignItems"], [1579, 16, 1246, 14], [1579, 18, 1246, 16], [1579, 26, 1246, 24], [1580, 6, 1247, 4, "marginBottom"], [1580, 18, 1247, 16], [1580, 20, 1247, 18], [1580, 22, 1247, 20], [1581, 6, 1248, 4], [1581, 9, 1248, 7, "Platform"], [1581, 26, 1248, 15], [1581, 27, 1248, 16, "select"], [1581, 33, 1248, 22], [1581, 34, 1248, 23], [1582, 8, 1249, 6, "ios"], [1582, 11, 1249, 9], [1582, 13, 1249, 11], [1583, 10, 1250, 8, "shadowColor"], [1583, 21, 1250, 19], [1583, 23, 1250, 21], [1583, 32, 1250, 30], [1584, 10, 1251, 8, "shadowOffset"], [1584, 22, 1251, 20], [1584, 24, 1251, 22], [1585, 12, 1251, 24, "width"], [1585, 17, 1251, 29], [1585, 19, 1251, 31], [1585, 20, 1251, 32], [1586, 12, 1251, 34, "height"], [1586, 18, 1251, 40], [1586, 20, 1251, 42], [1587, 10, 1251, 44], [1587, 11, 1251, 45], [1588, 10, 1252, 8, "shadowOpacity"], [1588, 23, 1252, 21], [1588, 25, 1252, 23], [1588, 28, 1252, 26], [1589, 10, 1253, 8, "shadowRadius"], [1589, 22, 1253, 20], [1589, 24, 1253, 22], [1590, 8, 1254, 6], [1590, 9, 1254, 7], [1591, 8, 1255, 6, "android"], [1591, 15, 1255, 13], [1591, 17, 1255, 15], [1592, 10, 1256, 8, "elevation"], [1592, 19, 1256, 17], [1592, 21, 1256, 19], [1593, 8, 1257, 6], [1593, 9, 1257, 7], [1594, 8, 1258, 6, "web"], [1594, 11, 1258, 9], [1594, 13, 1258, 11], [1595, 10, 1259, 8, "boxShadow"], [1595, 19, 1259, 17], [1595, 21, 1259, 19], [1596, 8, 1260, 6], [1597, 6, 1261, 4], [1597, 7, 1261, 5], [1598, 4, 1262, 2], [1598, 5, 1262, 3], [1599, 4, 1263, 2, "shutterButtonDisabled"], [1599, 25, 1263, 23], [1599, 27, 1263, 25], [1600, 6, 1264, 4, "opacity"], [1600, 13, 1264, 11], [1600, 15, 1264, 13], [1601, 4, 1265, 2], [1601, 5, 1265, 3], [1602, 4, 1266, 2, "shutterInner"], [1602, 16, 1266, 14], [1602, 18, 1266, 16], [1603, 6, 1267, 4, "width"], [1603, 11, 1267, 9], [1603, 13, 1267, 11], [1603, 15, 1267, 13], [1604, 6, 1268, 4, "height"], [1604, 12, 1268, 10], [1604, 14, 1268, 12], [1604, 16, 1268, 14], [1605, 6, 1269, 4, "borderRadius"], [1605, 18, 1269, 16], [1605, 20, 1269, 18], [1605, 22, 1269, 20], [1606, 6, 1270, 4, "backgroundColor"], [1606, 21, 1270, 19], [1606, 23, 1270, 21], [1606, 29, 1270, 27], [1607, 6, 1271, 4, "borderWidth"], [1607, 17, 1271, 15], [1607, 19, 1271, 17], [1607, 20, 1271, 18], [1608, 6, 1272, 4, "borderColor"], [1608, 17, 1272, 15], [1608, 19, 1272, 17], [1609, 4, 1273, 2], [1609, 5, 1273, 3], [1610, 4, 1274, 2, "privacyNote"], [1610, 15, 1274, 13], [1610, 17, 1274, 15], [1611, 6, 1275, 4, "fontSize"], [1611, 14, 1275, 12], [1611, 16, 1275, 14], [1611, 18, 1275, 16], [1612, 6, 1276, 4, "color"], [1612, 11, 1276, 9], [1612, 13, 1276, 11], [1613, 4, 1277, 2], [1613, 5, 1277, 3], [1614, 4, 1278, 2, "processingModal"], [1614, 19, 1278, 17], [1614, 21, 1278, 19], [1615, 6, 1279, 4, "flex"], [1615, 10, 1279, 8], [1615, 12, 1279, 10], [1615, 13, 1279, 11], [1616, 6, 1280, 4, "backgroundColor"], [1616, 21, 1280, 19], [1616, 23, 1280, 21], [1616, 43, 1280, 41], [1617, 6, 1281, 4, "justifyContent"], [1617, 20, 1281, 18], [1617, 22, 1281, 20], [1617, 30, 1281, 28], [1618, 6, 1282, 4, "alignItems"], [1618, 16, 1282, 14], [1618, 18, 1282, 16], [1619, 4, 1283, 2], [1619, 5, 1283, 3], [1620, 4, 1284, 2, "processingContent"], [1620, 21, 1284, 19], [1620, 23, 1284, 21], [1621, 6, 1285, 4, "backgroundColor"], [1621, 21, 1285, 19], [1621, 23, 1285, 21], [1621, 29, 1285, 27], [1622, 6, 1286, 4, "borderRadius"], [1622, 18, 1286, 16], [1622, 20, 1286, 18], [1622, 22, 1286, 20], [1623, 6, 1287, 4, "padding"], [1623, 13, 1287, 11], [1623, 15, 1287, 13], [1623, 17, 1287, 15], [1624, 6, 1288, 4, "width"], [1624, 11, 1288, 9], [1624, 13, 1288, 11], [1624, 18, 1288, 16], [1625, 6, 1289, 4, "max<PERSON><PERSON><PERSON>"], [1625, 14, 1289, 12], [1625, 16, 1289, 14], [1625, 19, 1289, 17], [1626, 6, 1290, 4, "alignItems"], [1626, 16, 1290, 14], [1626, 18, 1290, 16], [1627, 4, 1291, 2], [1627, 5, 1291, 3], [1628, 4, 1292, 2, "processingTitle"], [1628, 19, 1292, 17], [1628, 21, 1292, 19], [1629, 6, 1293, 4, "fontSize"], [1629, 14, 1293, 12], [1629, 16, 1293, 14], [1629, 18, 1293, 16], [1630, 6, 1294, 4, "fontWeight"], [1630, 16, 1294, 14], [1630, 18, 1294, 16], [1630, 23, 1294, 21], [1631, 6, 1295, 4, "color"], [1631, 11, 1295, 9], [1631, 13, 1295, 11], [1631, 22, 1295, 20], [1632, 6, 1296, 4, "marginTop"], [1632, 15, 1296, 13], [1632, 17, 1296, 15], [1632, 19, 1296, 17], [1633, 6, 1297, 4, "marginBottom"], [1633, 18, 1297, 16], [1633, 20, 1297, 18], [1634, 4, 1298, 2], [1634, 5, 1298, 3], [1635, 4, 1299, 2, "progressBar"], [1635, 15, 1299, 13], [1635, 17, 1299, 15], [1636, 6, 1300, 4, "width"], [1636, 11, 1300, 9], [1636, 13, 1300, 11], [1636, 19, 1300, 17], [1637, 6, 1301, 4, "height"], [1637, 12, 1301, 10], [1637, 14, 1301, 12], [1637, 15, 1301, 13], [1638, 6, 1302, 4, "backgroundColor"], [1638, 21, 1302, 19], [1638, 23, 1302, 21], [1638, 32, 1302, 30], [1639, 6, 1303, 4, "borderRadius"], [1639, 18, 1303, 16], [1639, 20, 1303, 18], [1639, 21, 1303, 19], [1640, 6, 1304, 4, "overflow"], [1640, 14, 1304, 12], [1640, 16, 1304, 14], [1640, 24, 1304, 22], [1641, 6, 1305, 4, "marginBottom"], [1641, 18, 1305, 16], [1641, 20, 1305, 18], [1642, 4, 1306, 2], [1642, 5, 1306, 3], [1643, 4, 1307, 2, "progressFill"], [1643, 16, 1307, 14], [1643, 18, 1307, 16], [1644, 6, 1308, 4, "height"], [1644, 12, 1308, 10], [1644, 14, 1308, 12], [1644, 20, 1308, 18], [1645, 6, 1309, 4, "backgroundColor"], [1645, 21, 1309, 19], [1645, 23, 1309, 21], [1645, 32, 1309, 30], [1646, 6, 1310, 4, "borderRadius"], [1646, 18, 1310, 16], [1646, 20, 1310, 18], [1647, 4, 1311, 2], [1647, 5, 1311, 3], [1648, 4, 1312, 2, "processingDescription"], [1648, 25, 1312, 23], [1648, 27, 1312, 25], [1649, 6, 1313, 4, "fontSize"], [1649, 14, 1313, 12], [1649, 16, 1313, 14], [1649, 18, 1313, 16], [1650, 6, 1314, 4, "color"], [1650, 11, 1314, 9], [1650, 13, 1314, 11], [1650, 22, 1314, 20], [1651, 6, 1315, 4, "textAlign"], [1651, 15, 1315, 13], [1651, 17, 1315, 15], [1652, 4, 1316, 2], [1652, 5, 1316, 3], [1653, 4, 1317, 2, "successIcon"], [1653, 15, 1317, 13], [1653, 17, 1317, 15], [1654, 6, 1318, 4, "marginTop"], [1654, 15, 1318, 13], [1654, 17, 1318, 15], [1655, 4, 1319, 2], [1655, 5, 1319, 3], [1656, 4, 1320, 2, "errorContent"], [1656, 16, 1320, 14], [1656, 18, 1320, 16], [1657, 6, 1321, 4, "backgroundColor"], [1657, 21, 1321, 19], [1657, 23, 1321, 21], [1657, 29, 1321, 27], [1658, 6, 1322, 4, "borderRadius"], [1658, 18, 1322, 16], [1658, 20, 1322, 18], [1658, 22, 1322, 20], [1659, 6, 1323, 4, "padding"], [1659, 13, 1323, 11], [1659, 15, 1323, 13], [1659, 17, 1323, 15], [1660, 6, 1324, 4, "width"], [1660, 11, 1324, 9], [1660, 13, 1324, 11], [1660, 18, 1324, 16], [1661, 6, 1325, 4, "max<PERSON><PERSON><PERSON>"], [1661, 14, 1325, 12], [1661, 16, 1325, 14], [1661, 19, 1325, 17], [1662, 6, 1326, 4, "alignItems"], [1662, 16, 1326, 14], [1662, 18, 1326, 16], [1663, 4, 1327, 2], [1663, 5, 1327, 3], [1664, 4, 1328, 2, "errorTitle"], [1664, 14, 1328, 12], [1664, 16, 1328, 14], [1665, 6, 1329, 4, "fontSize"], [1665, 14, 1329, 12], [1665, 16, 1329, 14], [1665, 18, 1329, 16], [1666, 6, 1330, 4, "fontWeight"], [1666, 16, 1330, 14], [1666, 18, 1330, 16], [1666, 23, 1330, 21], [1667, 6, 1331, 4, "color"], [1667, 11, 1331, 9], [1667, 13, 1331, 11], [1667, 22, 1331, 20], [1668, 6, 1332, 4, "marginTop"], [1668, 15, 1332, 13], [1668, 17, 1332, 15], [1668, 19, 1332, 17], [1669, 6, 1333, 4, "marginBottom"], [1669, 18, 1333, 16], [1669, 20, 1333, 18], [1670, 4, 1334, 2], [1670, 5, 1334, 3], [1671, 4, 1335, 2, "errorMessage"], [1671, 16, 1335, 14], [1671, 18, 1335, 16], [1672, 6, 1336, 4, "fontSize"], [1672, 14, 1336, 12], [1672, 16, 1336, 14], [1672, 18, 1336, 16], [1673, 6, 1337, 4, "color"], [1673, 11, 1337, 9], [1673, 13, 1337, 11], [1673, 22, 1337, 20], [1674, 6, 1338, 4, "textAlign"], [1674, 15, 1338, 13], [1674, 17, 1338, 15], [1674, 25, 1338, 23], [1675, 6, 1339, 4, "marginBottom"], [1675, 18, 1339, 16], [1675, 20, 1339, 18], [1676, 4, 1340, 2], [1676, 5, 1340, 3], [1677, 4, 1341, 2, "primaryButton"], [1677, 17, 1341, 15], [1677, 19, 1341, 17], [1678, 6, 1342, 4, "backgroundColor"], [1678, 21, 1342, 19], [1678, 23, 1342, 21], [1678, 32, 1342, 30], [1679, 6, 1343, 4, "paddingHorizontal"], [1679, 23, 1343, 21], [1679, 25, 1343, 23], [1679, 27, 1343, 25], [1680, 6, 1344, 4, "paddingVertical"], [1680, 21, 1344, 19], [1680, 23, 1344, 21], [1680, 25, 1344, 23], [1681, 6, 1345, 4, "borderRadius"], [1681, 18, 1345, 16], [1681, 20, 1345, 18], [1681, 21, 1345, 19], [1682, 6, 1346, 4, "marginTop"], [1682, 15, 1346, 13], [1682, 17, 1346, 15], [1683, 4, 1347, 2], [1683, 5, 1347, 3], [1684, 4, 1348, 2, "primaryButtonText"], [1684, 21, 1348, 19], [1684, 23, 1348, 21], [1685, 6, 1349, 4, "color"], [1685, 11, 1349, 9], [1685, 13, 1349, 11], [1685, 19, 1349, 17], [1686, 6, 1350, 4, "fontSize"], [1686, 14, 1350, 12], [1686, 16, 1350, 14], [1686, 18, 1350, 16], [1687, 6, 1351, 4, "fontWeight"], [1687, 16, 1351, 14], [1687, 18, 1351, 16], [1688, 4, 1352, 2], [1688, 5, 1352, 3], [1689, 4, 1353, 2, "secondaryButton"], [1689, 19, 1353, 17], [1689, 21, 1353, 19], [1690, 6, 1354, 4, "paddingHorizontal"], [1690, 23, 1354, 21], [1690, 25, 1354, 23], [1690, 27, 1354, 25], [1691, 6, 1355, 4, "paddingVertical"], [1691, 21, 1355, 19], [1691, 23, 1355, 21], [1691, 25, 1355, 23], [1692, 6, 1356, 4, "marginTop"], [1692, 15, 1356, 13], [1692, 17, 1356, 15], [1693, 4, 1357, 2], [1693, 5, 1357, 3], [1694, 4, 1358, 2, "secondaryButtonText"], [1694, 23, 1358, 21], [1694, 25, 1358, 23], [1695, 6, 1359, 4, "color"], [1695, 11, 1359, 9], [1695, 13, 1359, 11], [1695, 22, 1359, 20], [1696, 6, 1360, 4, "fontSize"], [1696, 14, 1360, 12], [1696, 16, 1360, 14], [1697, 4, 1361, 2], [1697, 5, 1361, 3], [1698, 4, 1362, 2, "permissionContent"], [1698, 21, 1362, 19], [1698, 23, 1362, 21], [1699, 6, 1363, 4, "flex"], [1699, 10, 1363, 8], [1699, 12, 1363, 10], [1699, 13, 1363, 11], [1700, 6, 1364, 4, "justifyContent"], [1700, 20, 1364, 18], [1700, 22, 1364, 20], [1700, 30, 1364, 28], [1701, 6, 1365, 4, "alignItems"], [1701, 16, 1365, 14], [1701, 18, 1365, 16], [1701, 26, 1365, 24], [1702, 6, 1366, 4, "padding"], [1702, 13, 1366, 11], [1702, 15, 1366, 13], [1703, 4, 1367, 2], [1703, 5, 1367, 3], [1704, 4, 1368, 2, "permissionTitle"], [1704, 19, 1368, 17], [1704, 21, 1368, 19], [1705, 6, 1369, 4, "fontSize"], [1705, 14, 1369, 12], [1705, 16, 1369, 14], [1705, 18, 1369, 16], [1706, 6, 1370, 4, "fontWeight"], [1706, 16, 1370, 14], [1706, 18, 1370, 16], [1706, 23, 1370, 21], [1707, 6, 1371, 4, "color"], [1707, 11, 1371, 9], [1707, 13, 1371, 11], [1707, 22, 1371, 20], [1708, 6, 1372, 4, "marginTop"], [1708, 15, 1372, 13], [1708, 17, 1372, 15], [1708, 19, 1372, 17], [1709, 6, 1373, 4, "marginBottom"], [1709, 18, 1373, 16], [1709, 20, 1373, 18], [1710, 4, 1374, 2], [1710, 5, 1374, 3], [1711, 4, 1375, 2, "permissionDescription"], [1711, 25, 1375, 23], [1711, 27, 1375, 25], [1712, 6, 1376, 4, "fontSize"], [1712, 14, 1376, 12], [1712, 16, 1376, 14], [1712, 18, 1376, 16], [1713, 6, 1377, 4, "color"], [1713, 11, 1377, 9], [1713, 13, 1377, 11], [1713, 22, 1377, 20], [1714, 6, 1378, 4, "textAlign"], [1714, 15, 1378, 13], [1714, 17, 1378, 15], [1714, 25, 1378, 23], [1715, 6, 1379, 4, "marginBottom"], [1715, 18, 1379, 16], [1715, 20, 1379, 18], [1716, 4, 1380, 2], [1716, 5, 1380, 3], [1717, 4, 1381, 2, "loadingText"], [1717, 15, 1381, 13], [1717, 17, 1381, 15], [1718, 6, 1382, 4, "color"], [1718, 11, 1382, 9], [1718, 13, 1382, 11], [1718, 22, 1382, 20], [1719, 6, 1383, 4, "marginTop"], [1719, 15, 1383, 13], [1719, 17, 1383, 15], [1720, 4, 1384, 2], [1720, 5, 1384, 3], [1721, 4, 1385, 2], [1722, 4, 1386, 2, "blurZone"], [1722, 12, 1386, 10], [1722, 14, 1386, 12], [1723, 6, 1387, 4, "position"], [1723, 14, 1387, 12], [1723, 16, 1387, 14], [1723, 26, 1387, 24], [1724, 6, 1388, 4, "overflow"], [1724, 14, 1388, 12], [1724, 16, 1388, 14], [1725, 4, 1389, 2], [1725, 5, 1389, 3], [1726, 4, 1390, 2, "previewChip"], [1726, 15, 1390, 13], [1726, 17, 1390, 15], [1727, 6, 1391, 4, "position"], [1727, 14, 1391, 12], [1727, 16, 1391, 14], [1727, 26, 1391, 24], [1728, 6, 1392, 4, "top"], [1728, 9, 1392, 7], [1728, 11, 1392, 9], [1728, 12, 1392, 10], [1729, 6, 1393, 4, "right"], [1729, 11, 1393, 9], [1729, 13, 1393, 11], [1729, 14, 1393, 12], [1730, 6, 1394, 4, "backgroundColor"], [1730, 21, 1394, 19], [1730, 23, 1394, 21], [1730, 40, 1394, 38], [1731, 6, 1395, 4, "paddingHorizontal"], [1731, 23, 1395, 21], [1731, 25, 1395, 23], [1731, 27, 1395, 25], [1732, 6, 1396, 4, "paddingVertical"], [1732, 21, 1396, 19], [1732, 23, 1396, 21], [1732, 24, 1396, 22], [1733, 6, 1397, 4, "borderRadius"], [1733, 18, 1397, 16], [1733, 20, 1397, 18], [1734, 4, 1398, 2], [1734, 5, 1398, 3], [1735, 4, 1399, 2, "previewChipText"], [1735, 19, 1399, 17], [1735, 21, 1399, 19], [1736, 6, 1400, 4, "color"], [1736, 11, 1400, 9], [1736, 13, 1400, 11], [1736, 19, 1400, 17], [1737, 6, 1401, 4, "fontSize"], [1737, 14, 1401, 12], [1737, 16, 1401, 14], [1737, 18, 1401, 16], [1738, 6, 1402, 4, "fontWeight"], [1738, 16, 1402, 14], [1738, 18, 1402, 16], [1739, 4, 1403, 2], [1740, 2, 1404, 0], [1740, 3, 1404, 1], [1740, 4, 1404, 2], [1741, 2, 1404, 3], [1741, 6, 1404, 3, "_c"], [1741, 8, 1404, 3], [1742, 2, 1404, 3, "$RefreshReg$"], [1742, 14, 1404, 3], [1742, 15, 1404, 3, "_c"], [1742, 17, 1404, 3], [1743, 0, 1404, 3], [1743, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCmD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfuE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBsC;mCoBE;wBhBc;OgBI;oFC+C;UDM;8BE8B;SFoD;uDhBa;sBmBC,wBnB;OgBC;GpByB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CDwL"}}, "type": "js/module"}]}