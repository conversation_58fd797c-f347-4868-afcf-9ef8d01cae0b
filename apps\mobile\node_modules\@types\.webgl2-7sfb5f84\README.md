# Installation
> `npm install --save @types/webgl2`

# Summary
This package contains type definitions for WebGL 2, Editor's Draft Fri Feb 24 16:10:18 2017 -0800 (https://www.khronos.org/registry/webgl/specs/latest/2.0/).

# Details
Files were exported from https://www.github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webgl2

Additional Details
 * Last updated: Thu, 26 Apr 2018 22:15:55 GMT
 * Dependencies: none
 * Global values: WebGL2RenderingContext, WebGLQuery, WebGLSampler, WebGLSync, WebGLTransformFeedback, WebGLVertexArrayObject

# Credits
These definitions were written by <PERSON> <https://github.com/nkemnitz>, <PERSON> <https://github.com/karhu>.
