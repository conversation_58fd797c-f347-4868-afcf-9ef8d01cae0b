{"dependencies": [{"name": "../../dom/types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 43, "index": 645}}], "key": "sExY87SNUbbJtOx4ghJnWxzXqE0=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 646}, "end": {"line": 5, "column": 41, "index": 687}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../Node", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 688}, "end": {"line": 6, "column": 79, "index": 767}}], "key": "629oO5tQuU3DWlSfXJ8iqlt7cNE=", "exportNames": ["*"]}}, {"name": "./Core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 768}, "end": {"line": 7, "column": 37, "index": 805}}], "key": "dny1ljGekIf+viLKNae+kH0CgzE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Recorder = void 0;\n  var _types = require(_dependencyMap[0], \"../../dom/types\");\n  var _utils = require(_dependencyMap[1], \"../utils\");\n  var _Node = require(_dependencyMap[2], \"../Node\");\n  var _Core = require(_dependencyMap[3], \"./Core\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class Recorder {\n    constructor() {\n      _defineProperty(this, \"commands\", []);\n      _defineProperty(this, \"cursors\", []);\n      _defineProperty(this, \"animationValues\", new Set());\n      this.cursors.push(this.commands);\n    }\n    getRecording() {\n      return {\n        commands: this.commands,\n        paintPool: [],\n        animationValues: this.animationValues\n      };\n    }\n    processProps(props) {\n      const animatedProps = {};\n      let hasAnimatedProps = false;\n      for (const key in props) {\n        const prop = props[key];\n        if ((0, _utils.isSharedValue)(prop)) {\n          this.animationValues.add(prop);\n          animatedProps[key] = prop;\n          hasAnimatedProps = true;\n        }\n      }\n      return {\n        props,\n        animatedProps: hasAnimatedProps ? animatedProps : undefined\n      };\n    }\n    add(command) {\n      if (command.props) {\n        const {\n          animatedProps\n        } = this.processProps(command.props);\n        if (animatedProps) {\n          command.animatedProps = animatedProps;\n        }\n      }\n      this.cursors[this.cursors.length - 1].push(command);\n    }\n    saveGroup() {\n      const children = [];\n      this.add({\n        type: _Core.CommandType.Group,\n        children\n      });\n      this.cursors.push(children);\n    }\n    restoreGroup() {\n      this.cursors.pop();\n    }\n    savePaint(props) {\n      this.add({\n        type: _Core.CommandType.SavePaint,\n        props\n      });\n    }\n    restorePaint() {\n      this.add({\n        type: _Core.CommandType.RestorePaint\n      });\n    }\n    restorePaintDeclaration() {\n      this.add({\n        type: _Core.CommandType.RestorePaintDeclaration\n      });\n    }\n    materializePaint() {\n      this.add({\n        type: _Core.CommandType.MaterializePaint\n      });\n    }\n    pushPathEffect(pathEffectType, props) {\n      if (!(0, _Node.isPathEffect)(pathEffectType)) {\n        throw new Error(\"Invalid color filter type: \" + pathEffectType);\n      }\n      this.add({\n        type: _Core.CommandType.PushPathEffect,\n        pathEffectType,\n        props\n      });\n    }\n    pushImageFilter(imageFilterType, props) {\n      if (!(0, _Node.isImageFilter)(imageFilterType)) {\n        throw new Error(\"Invalid color filter type: \" + imageFilterType);\n      }\n      this.add({\n        type: _Core.CommandType.PushImageFilter,\n        imageFilterType,\n        props\n      });\n    }\n    pushColorFilter(colorFilterType, props) {\n      if (!(0, _Node.isColorFilter)(colorFilterType)) {\n        throw new Error(\"Invalid color filter type: \" + colorFilterType);\n      }\n      this.add({\n        type: _Core.CommandType.PushColorFilter,\n        colorFilterType,\n        props\n      });\n    }\n    pushShader(shaderType, props) {\n      if (!(0, _Node.isShader)(shaderType) && !(shaderType === _types.NodeType.Blend)) {\n        throw new Error(\"Invalid color filter type: \" + shaderType);\n      }\n      this.add({\n        type: _Core.CommandType.PushShader,\n        shaderType,\n        props\n      });\n    }\n    pushBlurMaskFilter(props) {\n      this.add({\n        type: _Core.CommandType.PushBlurMaskFilter,\n        props\n      });\n    }\n    composePathEffect() {\n      this.add({\n        type: _Core.CommandType.ComposePathEffect\n      });\n    }\n    composeColorFilter() {\n      this.add({\n        type: _Core.CommandType.ComposeColorFilter\n      });\n    }\n    composeImageFilter() {\n      this.add({\n        type: _Core.CommandType.ComposeImageFilter\n      });\n    }\n    saveCTM(props) {\n      this.add({\n        type: _Core.CommandType.SaveCTM,\n        props\n      });\n    }\n    restoreCTM() {\n      this.add({\n        type: _Core.CommandType.RestoreCTM\n      });\n    }\n    drawPaint() {\n      this.add({\n        type: _Core.CommandType.DrawPaint\n      });\n    }\n    saveLayer() {\n      this.add({\n        type: _Core.CommandType.SaveLayer\n      });\n    }\n    saveBackdropFilter() {\n      this.add({\n        type: _Core.CommandType.SaveBackdropFilter\n      });\n    }\n    drawBox(boxProps, shadows) {\n      shadows.forEach(shadow => {\n        if (shadow.props) {\n          if (shadow.props) {\n            const {\n              animatedProps\n            } = this.processProps(shadow.props);\n            if (animatedProps) {\n              shadow.animatedProps = animatedProps;\n            }\n          }\n        }\n      });\n      this.add({\n        type: _Core.CommandType.DrawBox,\n        props: boxProps,\n        shadows\n      });\n    }\n    drawImage(props) {\n      this.add({\n        type: _Core.CommandType.DrawImage,\n        props\n      });\n    }\n    drawCircle(props) {\n      this.add({\n        type: _Core.CommandType.DrawCircle,\n        props\n      });\n    }\n    drawPoints(props) {\n      this.add({\n        type: _Core.CommandType.DrawPoints,\n        props\n      });\n    }\n    drawPath(props) {\n      this.add({\n        type: _Core.CommandType.DrawPath,\n        props\n      });\n    }\n    drawRect(props) {\n      this.add({\n        type: _Core.CommandType.DrawRect,\n        props\n      });\n    }\n    drawRRect(props) {\n      this.add({\n        type: _Core.CommandType.DrawRRect,\n        props\n      });\n    }\n    drawOval(props) {\n      this.add({\n        type: _Core.CommandType.DrawOval,\n        props\n      });\n    }\n    drawLine(props) {\n      this.add({\n        type: _Core.CommandType.DrawLine,\n        props\n      });\n    }\n    drawPatch(props) {\n      this.add({\n        type: _Core.CommandType.DrawPatch,\n        props\n      });\n    }\n    drawVertices(props) {\n      this.add({\n        type: _Core.CommandType.DrawVertices,\n        props\n      });\n    }\n    drawDiffRect(props) {\n      this.add({\n        type: _Core.CommandType.DrawDiffRect,\n        props\n      });\n    }\n    drawText(props) {\n      this.add({\n        type: _Core.CommandType.DrawText,\n        props\n      });\n    }\n    drawTextPath(props) {\n      this.add({\n        type: _Core.CommandType.DrawTextPath,\n        props\n      });\n    }\n    drawTextBlob(props) {\n      this.add({\n        type: _Core.CommandType.DrawTextBlob,\n        props\n      });\n    }\n    drawGlyphs(props) {\n      this.add({\n        type: _Core.CommandType.DrawGlyphs,\n        props\n      });\n    }\n    drawPicture(props) {\n      this.add({\n        type: _Core.CommandType.DrawPicture,\n        props\n      });\n    }\n    drawImageSVG(props) {\n      this.add({\n        type: _Core.CommandType.DrawImageSVG,\n        props\n      });\n    }\n    drawParagraph(props) {\n      this.add({\n        type: _Core.CommandType.DrawParagraph,\n        props\n      });\n    }\n    drawAtlas(props) {\n      this.add({\n        type: _Core.CommandType.DrawAtlas,\n        props\n      });\n    }\n  }\n  exports.Recorder = Recorder;\n});", "lineCount": 327, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_types"], [6, 12, 4, 0], [6, 15, 4, 0, "require"], [6, 22, 4, 0], [6, 23, 4, 0, "_dependencyMap"], [6, 37, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_utils"], [7, 12, 5, 0], [7, 15, 5, 0, "require"], [7, 22, 5, 0], [7, 23, 5, 0, "_dependencyMap"], [7, 37, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_Node"], [8, 11, 6, 0], [8, 14, 6, 0, "require"], [8, 21, 6, 0], [8, 22, 6, 0, "_dependencyMap"], [8, 36, 6, 0], [9, 2, 7, 0], [9, 6, 7, 0, "_Core"], [9, 11, 7, 0], [9, 14, 7, 0, "require"], [9, 21, 7, 0], [9, 22, 7, 0, "_dependencyMap"], [9, 36, 7, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "e"], [10, 28, 1, 26], [10, 30, 1, 28, "r"], [10, 31, 1, 29], [10, 33, 1, 31, "t"], [10, 34, 1, 32], [10, 36, 1, 34], [11, 4, 1, 36], [11, 11, 1, 43], [11, 12, 1, 44, "r"], [11, 13, 1, 45], [11, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 30, 1, 62], [11, 31, 1, 63, "r"], [11, 32, 1, 64], [11, 33, 1, 65], [11, 38, 1, 70, "e"], [11, 39, 1, 71], [11, 42, 1, 74, "Object"], [11, 48, 1, 80], [11, 49, 1, 81, "defineProperty"], [11, 63, 1, 95], [11, 64, 1, 96, "e"], [11, 65, 1, 97], [11, 67, 1, 99, "r"], [11, 68, 1, 100], [11, 70, 1, 102], [12, 6, 1, 104, "value"], [12, 11, 1, 109], [12, 13, 1, 111, "t"], [12, 14, 1, 112], [13, 6, 1, 114, "enumerable"], [13, 16, 1, 124], [13, 18, 1, 126], [13, 19, 1, 127], [13, 20, 1, 128], [14, 6, 1, 130, "configurable"], [14, 18, 1, 142], [14, 20, 1, 144], [14, 21, 1, 145], [14, 22, 1, 146], [15, 6, 1, 148, "writable"], [15, 14, 1, 156], [15, 16, 1, 158], [15, 17, 1, 159], [16, 4, 1, 161], [16, 5, 1, 162], [16, 6, 1, 163], [16, 9, 1, 166, "e"], [16, 10, 1, 167], [16, 11, 1, 168, "r"], [16, 12, 1, 169], [16, 13, 1, 170], [16, 16, 1, 173, "t"], [16, 17, 1, 174], [16, 19, 1, 176, "e"], [16, 20, 1, 177], [17, 2, 1, 179], [18, 2, 2, 0], [18, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 26, 2, 24, "t"], [18, 27, 2, 25], [18, 29, 2, 27], [19, 4, 2, 29], [19, 8, 2, 33, "i"], [19, 9, 2, 34], [19, 12, 2, 37, "_toPrimitive"], [19, 24, 2, 49], [19, 25, 2, 50, "t"], [19, 26, 2, 51], [19, 28, 2, 53], [19, 36, 2, 61], [19, 37, 2, 62], [20, 4, 2, 64], [20, 11, 2, 71], [20, 19, 2, 79], [20, 23, 2, 83], [20, 30, 2, 90, "i"], [20, 31, 2, 91], [20, 34, 2, 94, "i"], [20, 35, 2, 95], [20, 38, 2, 98, "i"], [20, 39, 2, 99], [20, 42, 2, 102], [20, 44, 2, 104], [21, 2, 2, 106], [22, 2, 3, 0], [22, 11, 3, 9, "_toPrimitive"], [22, 23, 3, 21, "_toPrimitive"], [22, 24, 3, 22, "t"], [22, 25, 3, 23], [22, 27, 3, 25, "r"], [22, 28, 3, 26], [22, 30, 3, 28], [23, 4, 3, 30], [23, 8, 3, 34], [23, 16, 3, 42], [23, 20, 3, 46], [23, 27, 3, 53, "t"], [23, 28, 3, 54], [23, 32, 3, 58], [23, 33, 3, 59, "t"], [23, 34, 3, 60], [23, 36, 3, 62], [23, 43, 3, 69, "t"], [23, 44, 3, 70], [24, 4, 3, 72], [24, 8, 3, 76, "e"], [24, 9, 3, 77], [24, 12, 3, 80, "t"], [24, 13, 3, 81], [24, 14, 3, 82, "Symbol"], [24, 20, 3, 88], [24, 21, 3, 89, "toPrimitive"], [24, 32, 3, 100], [24, 33, 3, 101], [25, 4, 3, 103], [25, 8, 3, 107], [25, 13, 3, 112], [25, 14, 3, 113], [25, 19, 3, 118, "e"], [25, 20, 3, 119], [25, 22, 3, 121], [26, 6, 3, 123], [26, 10, 3, 127, "i"], [26, 11, 3, 128], [26, 14, 3, 131, "e"], [26, 15, 3, 132], [26, 16, 3, 133, "call"], [26, 20, 3, 137], [26, 21, 3, 138, "t"], [26, 22, 3, 139], [26, 24, 3, 141, "r"], [26, 25, 3, 142], [26, 29, 3, 146], [26, 38, 3, 155], [26, 39, 3, 156], [27, 6, 3, 158], [27, 10, 3, 162], [27, 18, 3, 170], [27, 22, 3, 174], [27, 29, 3, 181, "i"], [27, 30, 3, 182], [27, 32, 3, 184], [27, 39, 3, 191, "i"], [27, 40, 3, 192], [28, 6, 3, 194], [28, 12, 3, 200], [28, 16, 3, 204, "TypeError"], [28, 25, 3, 213], [28, 26, 3, 214], [28, 72, 3, 260], [28, 73, 3, 261], [29, 4, 3, 263], [30, 4, 3, 265], [30, 11, 3, 272], [30, 12, 3, 273], [30, 20, 3, 281], [30, 25, 3, 286, "r"], [30, 26, 3, 287], [30, 29, 3, 290, "String"], [30, 35, 3, 296], [30, 38, 3, 299, "Number"], [30, 44, 3, 305], [30, 46, 3, 307, "t"], [30, 47, 3, 308], [30, 48, 3, 309], [31, 2, 3, 311], [32, 2, 8, 7], [32, 8, 8, 13, "Recorder"], [32, 16, 8, 21], [32, 17, 8, 22], [33, 4, 9, 2, "constructor"], [33, 15, 9, 13, "constructor"], [33, 16, 9, 13], [33, 18, 9, 16], [34, 6, 10, 4, "_defineProperty"], [34, 21, 10, 19], [34, 22, 10, 20], [34, 26, 10, 24], [34, 28, 10, 26], [34, 38, 10, 36], [34, 40, 10, 38], [34, 42, 10, 40], [34, 43, 10, 41], [35, 6, 11, 4, "_defineProperty"], [35, 21, 11, 19], [35, 22, 11, 20], [35, 26, 11, 24], [35, 28, 11, 26], [35, 37, 11, 35], [35, 39, 11, 37], [35, 41, 11, 39], [35, 42, 11, 40], [36, 6, 12, 4, "_defineProperty"], [36, 21, 12, 19], [36, 22, 12, 20], [36, 26, 12, 24], [36, 28, 12, 26], [36, 45, 12, 43], [36, 47, 12, 45], [36, 51, 12, 49, "Set"], [36, 54, 12, 52], [36, 55, 12, 53], [36, 56, 12, 54], [36, 57, 12, 55], [37, 6, 13, 4], [37, 10, 13, 8], [37, 11, 13, 9, "cursors"], [37, 18, 13, 16], [37, 19, 13, 17, "push"], [37, 23, 13, 21], [37, 24, 13, 22], [37, 28, 13, 26], [37, 29, 13, 27, "commands"], [37, 37, 13, 35], [37, 38, 13, 36], [38, 4, 14, 2], [39, 4, 15, 2, "getRecording"], [39, 16, 15, 14, "getRecording"], [39, 17, 15, 14], [39, 19, 15, 17], [40, 6, 16, 4], [40, 13, 16, 11], [41, 8, 17, 6, "commands"], [41, 16, 17, 14], [41, 18, 17, 16], [41, 22, 17, 20], [41, 23, 17, 21, "commands"], [41, 31, 17, 29], [42, 8, 18, 6, "paintPool"], [42, 17, 18, 15], [42, 19, 18, 17], [42, 21, 18, 19], [43, 8, 19, 6, "animationValues"], [43, 23, 19, 21], [43, 25, 19, 23], [43, 29, 19, 27], [43, 30, 19, 28, "animationValues"], [44, 6, 20, 4], [44, 7, 20, 5], [45, 4, 21, 2], [46, 4, 22, 2, "processProps"], [46, 16, 22, 14, "processProps"], [46, 17, 22, 15, "props"], [46, 22, 22, 20], [46, 24, 22, 22], [47, 6, 23, 4], [47, 12, 23, 10, "animatedProps"], [47, 25, 23, 23], [47, 28, 23, 26], [47, 29, 23, 27], [47, 30, 23, 28], [48, 6, 24, 4], [48, 10, 24, 8, "hasAnimatedProps"], [48, 26, 24, 24], [48, 29, 24, 27], [48, 34, 24, 32], [49, 6, 25, 4], [49, 11, 25, 9], [49, 17, 25, 15, "key"], [49, 20, 25, 18], [49, 24, 25, 22, "props"], [49, 29, 25, 27], [49, 31, 25, 29], [50, 8, 26, 6], [50, 14, 26, 12, "prop"], [50, 18, 26, 16], [50, 21, 26, 19, "props"], [50, 26, 26, 24], [50, 27, 26, 25, "key"], [50, 30, 26, 28], [50, 31, 26, 29], [51, 8, 27, 6], [51, 12, 27, 10], [51, 16, 27, 10, "isSharedValue"], [51, 36, 27, 23], [51, 38, 27, 24, "prop"], [51, 42, 27, 28], [51, 43, 27, 29], [51, 45, 27, 31], [52, 10, 28, 8], [52, 14, 28, 12], [52, 15, 28, 13, "animationValues"], [52, 30, 28, 28], [52, 31, 28, 29, "add"], [52, 34, 28, 32], [52, 35, 28, 33, "prop"], [52, 39, 28, 37], [52, 40, 28, 38], [53, 10, 29, 8, "animatedProps"], [53, 23, 29, 21], [53, 24, 29, 22, "key"], [53, 27, 29, 25], [53, 28, 29, 26], [53, 31, 29, 29, "prop"], [53, 35, 29, 33], [54, 10, 30, 8, "hasAnimatedProps"], [54, 26, 30, 24], [54, 29, 30, 27], [54, 33, 30, 31], [55, 8, 31, 6], [56, 6, 32, 4], [57, 6, 33, 4], [57, 13, 33, 11], [58, 8, 34, 6, "props"], [58, 13, 34, 11], [59, 8, 35, 6, "animatedProps"], [59, 21, 35, 19], [59, 23, 35, 21, "hasAnimatedProps"], [59, 39, 35, 37], [59, 42, 35, 40, "animatedProps"], [59, 55, 35, 53], [59, 58, 35, 56, "undefined"], [60, 6, 36, 4], [60, 7, 36, 5], [61, 4, 37, 2], [62, 4, 38, 2, "add"], [62, 7, 38, 5, "add"], [62, 8, 38, 6, "command"], [62, 15, 38, 13], [62, 17, 38, 15], [63, 6, 39, 4], [63, 10, 39, 8, "command"], [63, 17, 39, 15], [63, 18, 39, 16, "props"], [63, 23, 39, 21], [63, 25, 39, 23], [64, 8, 40, 6], [64, 14, 40, 12], [65, 10, 41, 8, "animatedProps"], [66, 8, 42, 6], [66, 9, 42, 7], [66, 12, 42, 10], [66, 16, 42, 14], [66, 17, 42, 15, "processProps"], [66, 29, 42, 27], [66, 30, 42, 28, "command"], [66, 37, 42, 35], [66, 38, 42, 36, "props"], [66, 43, 42, 41], [66, 44, 42, 42], [67, 8, 43, 6], [67, 12, 43, 10, "animatedProps"], [67, 25, 43, 23], [67, 27, 43, 25], [68, 10, 44, 8, "command"], [68, 17, 44, 15], [68, 18, 44, 16, "animatedProps"], [68, 31, 44, 29], [68, 34, 44, 32, "animatedProps"], [68, 47, 44, 45], [69, 8, 45, 6], [70, 6, 46, 4], [71, 6, 47, 4], [71, 10, 47, 8], [71, 11, 47, 9, "cursors"], [71, 18, 47, 16], [71, 19, 47, 17], [71, 23, 47, 21], [71, 24, 47, 22, "cursors"], [71, 31, 47, 29], [71, 32, 47, 30, "length"], [71, 38, 47, 36], [71, 41, 47, 39], [71, 42, 47, 40], [71, 43, 47, 41], [71, 44, 47, 42, "push"], [71, 48, 47, 46], [71, 49, 47, 47, "command"], [71, 56, 47, 54], [71, 57, 47, 55], [72, 4, 48, 2], [73, 4, 49, 2, "saveGroup"], [73, 13, 49, 11, "saveGroup"], [73, 14, 49, 11], [73, 16, 49, 14], [74, 6, 50, 4], [74, 12, 50, 10, "children"], [74, 20, 50, 18], [74, 23, 50, 21], [74, 25, 50, 23], [75, 6, 51, 4], [75, 10, 51, 8], [75, 11, 51, 9, "add"], [75, 14, 51, 12], [75, 15, 51, 13], [76, 8, 52, 6, "type"], [76, 12, 52, 10], [76, 14, 52, 12, "CommandType"], [76, 31, 52, 23], [76, 32, 52, 24, "Group"], [76, 37, 52, 29], [77, 8, 53, 6, "children"], [78, 6, 54, 4], [78, 7, 54, 5], [78, 8, 54, 6], [79, 6, 55, 4], [79, 10, 55, 8], [79, 11, 55, 9, "cursors"], [79, 18, 55, 16], [79, 19, 55, 17, "push"], [79, 23, 55, 21], [79, 24, 55, 22, "children"], [79, 32, 55, 30], [79, 33, 55, 31], [80, 4, 56, 2], [81, 4, 57, 2, "restoreGroup"], [81, 16, 57, 14, "restoreGroup"], [81, 17, 57, 14], [81, 19, 57, 17], [82, 6, 58, 4], [82, 10, 58, 8], [82, 11, 58, 9, "cursors"], [82, 18, 58, 16], [82, 19, 58, 17, "pop"], [82, 22, 58, 20], [82, 23, 58, 21], [82, 24, 58, 22], [83, 4, 59, 2], [84, 4, 60, 2, "save<PERSON><PERSON>t"], [84, 13, 60, 11, "save<PERSON><PERSON>t"], [84, 14, 60, 12, "props"], [84, 19, 60, 17], [84, 21, 60, 19], [85, 6, 61, 4], [85, 10, 61, 8], [85, 11, 61, 9, "add"], [85, 14, 61, 12], [85, 15, 61, 13], [86, 8, 62, 6, "type"], [86, 12, 62, 10], [86, 14, 62, 12, "CommandType"], [86, 31, 62, 23], [86, 32, 62, 24, "<PERSON><PERSON><PERSON><PERSON>"], [86, 41, 62, 33], [87, 8, 63, 6, "props"], [88, 6, 64, 4], [88, 7, 64, 5], [88, 8, 64, 6], [89, 4, 65, 2], [90, 4, 66, 2, "<PERSON><PERSON><PERSON><PERSON>"], [90, 16, 66, 14, "<PERSON><PERSON><PERSON><PERSON>"], [90, 17, 66, 14], [90, 19, 66, 17], [91, 6, 67, 4], [91, 10, 67, 8], [91, 11, 67, 9, "add"], [91, 14, 67, 12], [91, 15, 67, 13], [92, 8, 68, 6, "type"], [92, 12, 68, 10], [92, 14, 68, 12, "CommandType"], [92, 31, 68, 23], [92, 32, 68, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [93, 6, 69, 4], [93, 7, 69, 5], [93, 8, 69, 6], [94, 4, 70, 2], [95, 4, 71, 2, "restorePaintDeclaration"], [95, 27, 71, 25, "restorePaintDeclaration"], [95, 28, 71, 25], [95, 30, 71, 28], [96, 6, 72, 4], [96, 10, 72, 8], [96, 11, 72, 9, "add"], [96, 14, 72, 12], [96, 15, 72, 13], [97, 8, 73, 6, "type"], [97, 12, 73, 10], [97, 14, 73, 12, "CommandType"], [97, 31, 73, 23], [97, 32, 73, 24, "RestorePaintDeclaration"], [98, 6, 74, 4], [98, 7, 74, 5], [98, 8, 74, 6], [99, 4, 75, 2], [100, 4, 76, 2, "materialize<PERSON><PERSON><PERSON>"], [100, 20, 76, 18, "materialize<PERSON><PERSON><PERSON>"], [100, 21, 76, 18], [100, 23, 76, 21], [101, 6, 77, 4], [101, 10, 77, 8], [101, 11, 77, 9, "add"], [101, 14, 77, 12], [101, 15, 77, 13], [102, 8, 78, 6, "type"], [102, 12, 78, 10], [102, 14, 78, 12, "CommandType"], [102, 31, 78, 23], [102, 32, 78, 24, "MaterializePaint"], [103, 6, 79, 4], [103, 7, 79, 5], [103, 8, 79, 6], [104, 4, 80, 2], [105, 4, 81, 2, "pushPathEffect"], [105, 18, 81, 16, "pushPathEffect"], [105, 19, 81, 17, "pathEffectType"], [105, 33, 81, 31], [105, 35, 81, 33, "props"], [105, 40, 81, 38], [105, 42, 81, 40], [106, 6, 82, 4], [106, 10, 82, 8], [106, 11, 82, 9], [106, 15, 82, 9, "isPathEffect"], [106, 33, 82, 21], [106, 35, 82, 22, "pathEffectType"], [106, 49, 82, 36], [106, 50, 82, 37], [106, 52, 82, 39], [107, 8, 83, 6], [107, 14, 83, 12], [107, 18, 83, 16, "Error"], [107, 23, 83, 21], [107, 24, 83, 22], [107, 53, 83, 51], [107, 56, 83, 54, "pathEffectType"], [107, 70, 83, 68], [107, 71, 83, 69], [108, 6, 84, 4], [109, 6, 85, 4], [109, 10, 85, 8], [109, 11, 85, 9, "add"], [109, 14, 85, 12], [109, 15, 85, 13], [110, 8, 86, 6, "type"], [110, 12, 86, 10], [110, 14, 86, 12, "CommandType"], [110, 31, 86, 23], [110, 32, 86, 24, "PushPathEffect"], [110, 46, 86, 38], [111, 8, 87, 6, "pathEffectType"], [111, 22, 87, 20], [112, 8, 88, 6, "props"], [113, 6, 89, 4], [113, 7, 89, 5], [113, 8, 89, 6], [114, 4, 90, 2], [115, 4, 91, 2, "pushImageFilter"], [115, 19, 91, 17, "pushImageFilter"], [115, 20, 91, 18, "imageFilterType"], [115, 35, 91, 33], [115, 37, 91, 35, "props"], [115, 42, 91, 40], [115, 44, 91, 42], [116, 6, 92, 4], [116, 10, 92, 8], [116, 11, 92, 9], [116, 15, 92, 9, "isImageFilter"], [116, 34, 92, 22], [116, 36, 92, 23, "imageFilterType"], [116, 51, 92, 38], [116, 52, 92, 39], [116, 54, 92, 41], [117, 8, 93, 6], [117, 14, 93, 12], [117, 18, 93, 16, "Error"], [117, 23, 93, 21], [117, 24, 93, 22], [117, 53, 93, 51], [117, 56, 93, 54, "imageFilterType"], [117, 71, 93, 69], [117, 72, 93, 70], [118, 6, 94, 4], [119, 6, 95, 4], [119, 10, 95, 8], [119, 11, 95, 9, "add"], [119, 14, 95, 12], [119, 15, 95, 13], [120, 8, 96, 6, "type"], [120, 12, 96, 10], [120, 14, 96, 12, "CommandType"], [120, 31, 96, 23], [120, 32, 96, 24, "PushImageFilter"], [120, 47, 96, 39], [121, 8, 97, 6, "imageFilterType"], [121, 23, 97, 21], [122, 8, 98, 6, "props"], [123, 6, 99, 4], [123, 7, 99, 5], [123, 8, 99, 6], [124, 4, 100, 2], [125, 4, 101, 2, "pushColorFilter"], [125, 19, 101, 17, "pushColorFilter"], [125, 20, 101, 18, "colorFilterType"], [125, 35, 101, 33], [125, 37, 101, 35, "props"], [125, 42, 101, 40], [125, 44, 101, 42], [126, 6, 102, 4], [126, 10, 102, 8], [126, 11, 102, 9], [126, 15, 102, 9, "isColorFilter"], [126, 34, 102, 22], [126, 36, 102, 23, "colorFilterType"], [126, 51, 102, 38], [126, 52, 102, 39], [126, 54, 102, 41], [127, 8, 103, 6], [127, 14, 103, 12], [127, 18, 103, 16, "Error"], [127, 23, 103, 21], [127, 24, 103, 22], [127, 53, 103, 51], [127, 56, 103, 54, "colorFilterType"], [127, 71, 103, 69], [127, 72, 103, 70], [128, 6, 104, 4], [129, 6, 105, 4], [129, 10, 105, 8], [129, 11, 105, 9, "add"], [129, 14, 105, 12], [129, 15, 105, 13], [130, 8, 106, 6, "type"], [130, 12, 106, 10], [130, 14, 106, 12, "CommandType"], [130, 31, 106, 23], [130, 32, 106, 24, "PushColorFilter"], [130, 47, 106, 39], [131, 8, 107, 6, "colorFilterType"], [131, 23, 107, 21], [132, 8, 108, 6, "props"], [133, 6, 109, 4], [133, 7, 109, 5], [133, 8, 109, 6], [134, 4, 110, 2], [135, 4, 111, 2, "push<PERSON><PERSON>er"], [135, 14, 111, 12, "push<PERSON><PERSON>er"], [135, 15, 111, 13, "shaderType"], [135, 25, 111, 23], [135, 27, 111, 25, "props"], [135, 32, 111, 30], [135, 34, 111, 32], [136, 6, 112, 4], [136, 10, 112, 8], [136, 11, 112, 9], [136, 15, 112, 9, "<PERSON><PERSON><PERSON><PERSON>"], [136, 29, 112, 17], [136, 31, 112, 18, "shaderType"], [136, 41, 112, 28], [136, 42, 112, 29], [136, 46, 112, 33], [136, 48, 112, 35, "shaderType"], [136, 58, 112, 45], [136, 63, 112, 50, "NodeType"], [136, 78, 112, 58], [136, 79, 112, 59, "Blend"], [136, 84, 112, 64], [136, 85, 112, 65], [136, 87, 112, 67], [137, 8, 113, 6], [137, 14, 113, 12], [137, 18, 113, 16, "Error"], [137, 23, 113, 21], [137, 24, 113, 22], [137, 53, 113, 51], [137, 56, 113, 54, "shaderType"], [137, 66, 113, 64], [137, 67, 113, 65], [138, 6, 114, 4], [139, 6, 115, 4], [139, 10, 115, 8], [139, 11, 115, 9, "add"], [139, 14, 115, 12], [139, 15, 115, 13], [140, 8, 116, 6, "type"], [140, 12, 116, 10], [140, 14, 116, 12, "CommandType"], [140, 31, 116, 23], [140, 32, 116, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [140, 42, 116, 34], [141, 8, 117, 6, "shaderType"], [141, 18, 117, 16], [142, 8, 118, 6, "props"], [143, 6, 119, 4], [143, 7, 119, 5], [143, 8, 119, 6], [144, 4, 120, 2], [145, 4, 121, 2, "pushBlurMaskFilter"], [145, 22, 121, 20, "pushBlurMaskFilter"], [145, 23, 121, 21, "props"], [145, 28, 121, 26], [145, 30, 121, 28], [146, 6, 122, 4], [146, 10, 122, 8], [146, 11, 122, 9, "add"], [146, 14, 122, 12], [146, 15, 122, 13], [147, 8, 123, 6, "type"], [147, 12, 123, 10], [147, 14, 123, 12, "CommandType"], [147, 31, 123, 23], [147, 32, 123, 24, "<PERSON>ush<PERSON><PERSON>rMask<PERSON><PERSON><PERSON>"], [147, 50, 123, 42], [148, 8, 124, 6, "props"], [149, 6, 125, 4], [149, 7, 125, 5], [149, 8, 125, 6], [150, 4, 126, 2], [151, 4, 127, 2, "composePathEffect"], [151, 21, 127, 19, "composePathEffect"], [151, 22, 127, 19], [151, 24, 127, 22], [152, 6, 128, 4], [152, 10, 128, 8], [152, 11, 128, 9, "add"], [152, 14, 128, 12], [152, 15, 128, 13], [153, 8, 129, 6, "type"], [153, 12, 129, 10], [153, 14, 129, 12, "CommandType"], [153, 31, 129, 23], [153, 32, 129, 24, "ComposePathEffect"], [154, 6, 130, 4], [154, 7, 130, 5], [154, 8, 130, 6], [155, 4, 131, 2], [156, 4, 132, 2, "composeColorFilter"], [156, 22, 132, 20, "composeColorFilter"], [156, 23, 132, 20], [156, 25, 132, 23], [157, 6, 133, 4], [157, 10, 133, 8], [157, 11, 133, 9, "add"], [157, 14, 133, 12], [157, 15, 133, 13], [158, 8, 134, 6, "type"], [158, 12, 134, 10], [158, 14, 134, 12, "CommandType"], [158, 31, 134, 23], [158, 32, 134, 24, "ComposeColorFilter"], [159, 6, 135, 4], [159, 7, 135, 5], [159, 8, 135, 6], [160, 4, 136, 2], [161, 4, 137, 2, "composeImageFilter"], [161, 22, 137, 20, "composeImageFilter"], [161, 23, 137, 20], [161, 25, 137, 23], [162, 6, 138, 4], [162, 10, 138, 8], [162, 11, 138, 9, "add"], [162, 14, 138, 12], [162, 15, 138, 13], [163, 8, 139, 6, "type"], [163, 12, 139, 10], [163, 14, 139, 12, "CommandType"], [163, 31, 139, 23], [163, 32, 139, 24, "ComposeImageFilter"], [164, 6, 140, 4], [164, 7, 140, 5], [164, 8, 140, 6], [165, 4, 141, 2], [166, 4, 142, 2, "saveCTM"], [166, 11, 142, 9, "saveCTM"], [166, 12, 142, 10, "props"], [166, 17, 142, 15], [166, 19, 142, 17], [167, 6, 143, 4], [167, 10, 143, 8], [167, 11, 143, 9, "add"], [167, 14, 143, 12], [167, 15, 143, 13], [168, 8, 144, 6, "type"], [168, 12, 144, 10], [168, 14, 144, 12, "CommandType"], [168, 31, 144, 23], [168, 32, 144, 24, "SaveCTM"], [168, 39, 144, 31], [169, 8, 145, 6, "props"], [170, 6, 146, 4], [170, 7, 146, 5], [170, 8, 146, 6], [171, 4, 147, 2], [172, 4, 148, 2, "restoreCTM"], [172, 14, 148, 12, "restoreCTM"], [172, 15, 148, 12], [172, 17, 148, 15], [173, 6, 149, 4], [173, 10, 149, 8], [173, 11, 149, 9, "add"], [173, 14, 149, 12], [173, 15, 149, 13], [174, 8, 150, 6, "type"], [174, 12, 150, 10], [174, 14, 150, 12, "CommandType"], [174, 31, 150, 23], [174, 32, 150, 24, "RestoreCTM"], [175, 6, 151, 4], [175, 7, 151, 5], [175, 8, 151, 6], [176, 4, 152, 2], [177, 4, 153, 2, "<PERSON><PERSON><PERSON><PERSON>"], [177, 13, 153, 11, "<PERSON><PERSON><PERSON><PERSON>"], [177, 14, 153, 11], [177, 16, 153, 14], [178, 6, 154, 4], [178, 10, 154, 8], [178, 11, 154, 9, "add"], [178, 14, 154, 12], [178, 15, 154, 13], [179, 8, 155, 6, "type"], [179, 12, 155, 10], [179, 14, 155, 12, "CommandType"], [179, 31, 155, 23], [179, 32, 155, 24, "DrawPaint"], [180, 6, 156, 4], [180, 7, 156, 5], [180, 8, 156, 6], [181, 4, 157, 2], [182, 4, 158, 2, "save<PERSON><PERSON><PERSON>"], [182, 13, 158, 11, "save<PERSON><PERSON><PERSON>"], [182, 14, 158, 11], [182, 16, 158, 14], [183, 6, 159, 4], [183, 10, 159, 8], [183, 11, 159, 9, "add"], [183, 14, 159, 12], [183, 15, 159, 13], [184, 8, 160, 6, "type"], [184, 12, 160, 10], [184, 14, 160, 12, "CommandType"], [184, 31, 160, 23], [184, 32, 160, 24, "<PERSON><PERSON><PERSON><PERSON>"], [185, 6, 161, 4], [185, 7, 161, 5], [185, 8, 161, 6], [186, 4, 162, 2], [187, 4, 163, 2, "saveBackdropFilter"], [187, 22, 163, 20, "saveBackdropFilter"], [187, 23, 163, 20], [187, 25, 163, 23], [188, 6, 164, 4], [188, 10, 164, 8], [188, 11, 164, 9, "add"], [188, 14, 164, 12], [188, 15, 164, 13], [189, 8, 165, 6, "type"], [189, 12, 165, 10], [189, 14, 165, 12, "CommandType"], [189, 31, 165, 23], [189, 32, 165, 24, "SaveBackdropFilter"], [190, 6, 166, 4], [190, 7, 166, 5], [190, 8, 166, 6], [191, 4, 167, 2], [192, 4, 168, 2, "drawBox"], [192, 11, 168, 9, "drawBox"], [192, 12, 168, 10, "boxProps"], [192, 20, 168, 18], [192, 22, 168, 20, "shadows"], [192, 29, 168, 27], [192, 31, 168, 29], [193, 6, 169, 4, "shadows"], [193, 13, 169, 11], [193, 14, 169, 12, "for<PERSON>ach"], [193, 21, 169, 19], [193, 22, 169, 20, "shadow"], [193, 28, 169, 26], [193, 32, 169, 30], [194, 8, 170, 6], [194, 12, 170, 10, "shadow"], [194, 18, 170, 16], [194, 19, 170, 17, "props"], [194, 24, 170, 22], [194, 26, 170, 24], [195, 10, 171, 8], [195, 14, 171, 12, "shadow"], [195, 20, 171, 18], [195, 21, 171, 19, "props"], [195, 26, 171, 24], [195, 28, 171, 26], [196, 12, 172, 10], [196, 18, 172, 16], [197, 14, 173, 12, "animatedProps"], [198, 12, 174, 10], [198, 13, 174, 11], [198, 16, 174, 14], [198, 20, 174, 18], [198, 21, 174, 19, "processProps"], [198, 33, 174, 31], [198, 34, 174, 32, "shadow"], [198, 40, 174, 38], [198, 41, 174, 39, "props"], [198, 46, 174, 44], [198, 47, 174, 45], [199, 12, 175, 10], [199, 16, 175, 14, "animatedProps"], [199, 29, 175, 27], [199, 31, 175, 29], [200, 14, 176, 12, "shadow"], [200, 20, 176, 18], [200, 21, 176, 19, "animatedProps"], [200, 34, 176, 32], [200, 37, 176, 35, "animatedProps"], [200, 50, 176, 48], [201, 12, 177, 10], [202, 10, 178, 8], [203, 8, 179, 6], [204, 6, 180, 4], [204, 7, 180, 5], [204, 8, 180, 6], [205, 6, 181, 4], [205, 10, 181, 8], [205, 11, 181, 9, "add"], [205, 14, 181, 12], [205, 15, 181, 13], [206, 8, 182, 6, "type"], [206, 12, 182, 10], [206, 14, 182, 12, "CommandType"], [206, 31, 182, 23], [206, 32, 182, 24, "DrawBox"], [206, 39, 182, 31], [207, 8, 183, 6, "props"], [207, 13, 183, 11], [207, 15, 183, 13, "boxProps"], [207, 23, 183, 21], [208, 8, 184, 6, "shadows"], [209, 6, 185, 4], [209, 7, 185, 5], [209, 8, 185, 6], [210, 4, 186, 2], [211, 4, 187, 2, "drawImage"], [211, 13, 187, 11, "drawImage"], [211, 14, 187, 12, "props"], [211, 19, 187, 17], [211, 21, 187, 19], [212, 6, 188, 4], [212, 10, 188, 8], [212, 11, 188, 9, "add"], [212, 14, 188, 12], [212, 15, 188, 13], [213, 8, 189, 6, "type"], [213, 12, 189, 10], [213, 14, 189, 12, "CommandType"], [213, 31, 189, 23], [213, 32, 189, 24, "DrawImage"], [213, 41, 189, 33], [214, 8, 190, 6, "props"], [215, 6, 191, 4], [215, 7, 191, 5], [215, 8, 191, 6], [216, 4, 192, 2], [217, 4, 193, 2, "drawCircle"], [217, 14, 193, 12, "drawCircle"], [217, 15, 193, 13, "props"], [217, 20, 193, 18], [217, 22, 193, 20], [218, 6, 194, 4], [218, 10, 194, 8], [218, 11, 194, 9, "add"], [218, 14, 194, 12], [218, 15, 194, 13], [219, 8, 195, 6, "type"], [219, 12, 195, 10], [219, 14, 195, 12, "CommandType"], [219, 31, 195, 23], [219, 32, 195, 24, "DrawCircle"], [219, 42, 195, 34], [220, 8, 196, 6, "props"], [221, 6, 197, 4], [221, 7, 197, 5], [221, 8, 197, 6], [222, 4, 198, 2], [223, 4, 199, 2, "drawPoints"], [223, 14, 199, 12, "drawPoints"], [223, 15, 199, 13, "props"], [223, 20, 199, 18], [223, 22, 199, 20], [224, 6, 200, 4], [224, 10, 200, 8], [224, 11, 200, 9, "add"], [224, 14, 200, 12], [224, 15, 200, 13], [225, 8, 201, 6, "type"], [225, 12, 201, 10], [225, 14, 201, 12, "CommandType"], [225, 31, 201, 23], [225, 32, 201, 24, "DrawPoints"], [225, 42, 201, 34], [226, 8, 202, 6, "props"], [227, 6, 203, 4], [227, 7, 203, 5], [227, 8, 203, 6], [228, 4, 204, 2], [229, 4, 205, 2, "drawPath"], [229, 12, 205, 10, "drawPath"], [229, 13, 205, 11, "props"], [229, 18, 205, 16], [229, 20, 205, 18], [230, 6, 206, 4], [230, 10, 206, 8], [230, 11, 206, 9, "add"], [230, 14, 206, 12], [230, 15, 206, 13], [231, 8, 207, 6, "type"], [231, 12, 207, 10], [231, 14, 207, 12, "CommandType"], [231, 31, 207, 23], [231, 32, 207, 24, "DrawPath"], [231, 40, 207, 32], [232, 8, 208, 6, "props"], [233, 6, 209, 4], [233, 7, 209, 5], [233, 8, 209, 6], [234, 4, 210, 2], [235, 4, 211, 2, "drawRect"], [235, 12, 211, 10, "drawRect"], [235, 13, 211, 11, "props"], [235, 18, 211, 16], [235, 20, 211, 18], [236, 6, 212, 4], [236, 10, 212, 8], [236, 11, 212, 9, "add"], [236, 14, 212, 12], [236, 15, 212, 13], [237, 8, 213, 6, "type"], [237, 12, 213, 10], [237, 14, 213, 12, "CommandType"], [237, 31, 213, 23], [237, 32, 213, 24, "DrawRect"], [237, 40, 213, 32], [238, 8, 214, 6, "props"], [239, 6, 215, 4], [239, 7, 215, 5], [239, 8, 215, 6], [240, 4, 216, 2], [241, 4, 217, 2, "drawRRect"], [241, 13, 217, 11, "drawRRect"], [241, 14, 217, 12, "props"], [241, 19, 217, 17], [241, 21, 217, 19], [242, 6, 218, 4], [242, 10, 218, 8], [242, 11, 218, 9, "add"], [242, 14, 218, 12], [242, 15, 218, 13], [243, 8, 219, 6, "type"], [243, 12, 219, 10], [243, 14, 219, 12, "CommandType"], [243, 31, 219, 23], [243, 32, 219, 24, "DrawRRect"], [243, 41, 219, 33], [244, 8, 220, 6, "props"], [245, 6, 221, 4], [245, 7, 221, 5], [245, 8, 221, 6], [246, 4, 222, 2], [247, 4, 223, 2, "drawOval"], [247, 12, 223, 10, "drawOval"], [247, 13, 223, 11, "props"], [247, 18, 223, 16], [247, 20, 223, 18], [248, 6, 224, 4], [248, 10, 224, 8], [248, 11, 224, 9, "add"], [248, 14, 224, 12], [248, 15, 224, 13], [249, 8, 225, 6, "type"], [249, 12, 225, 10], [249, 14, 225, 12, "CommandType"], [249, 31, 225, 23], [249, 32, 225, 24, "DrawOval"], [249, 40, 225, 32], [250, 8, 226, 6, "props"], [251, 6, 227, 4], [251, 7, 227, 5], [251, 8, 227, 6], [252, 4, 228, 2], [253, 4, 229, 2, "drawLine"], [253, 12, 229, 10, "drawLine"], [253, 13, 229, 11, "props"], [253, 18, 229, 16], [253, 20, 229, 18], [254, 6, 230, 4], [254, 10, 230, 8], [254, 11, 230, 9, "add"], [254, 14, 230, 12], [254, 15, 230, 13], [255, 8, 231, 6, "type"], [255, 12, 231, 10], [255, 14, 231, 12, "CommandType"], [255, 31, 231, 23], [255, 32, 231, 24, "DrawLine"], [255, 40, 231, 32], [256, 8, 232, 6, "props"], [257, 6, 233, 4], [257, 7, 233, 5], [257, 8, 233, 6], [258, 4, 234, 2], [259, 4, 235, 2, "drawPatch"], [259, 13, 235, 11, "drawPatch"], [259, 14, 235, 12, "props"], [259, 19, 235, 17], [259, 21, 235, 19], [260, 6, 236, 4], [260, 10, 236, 8], [260, 11, 236, 9, "add"], [260, 14, 236, 12], [260, 15, 236, 13], [261, 8, 237, 6, "type"], [261, 12, 237, 10], [261, 14, 237, 12, "CommandType"], [261, 31, 237, 23], [261, 32, 237, 24, "DrawPatch"], [261, 41, 237, 33], [262, 8, 238, 6, "props"], [263, 6, 239, 4], [263, 7, 239, 5], [263, 8, 239, 6], [264, 4, 240, 2], [265, 4, 241, 2, "drawVertices"], [265, 16, 241, 14, "drawVertices"], [265, 17, 241, 15, "props"], [265, 22, 241, 20], [265, 24, 241, 22], [266, 6, 242, 4], [266, 10, 242, 8], [266, 11, 242, 9, "add"], [266, 14, 242, 12], [266, 15, 242, 13], [267, 8, 243, 6, "type"], [267, 12, 243, 10], [267, 14, 243, 12, "CommandType"], [267, 31, 243, 23], [267, 32, 243, 24, "DrawVertices"], [267, 44, 243, 36], [268, 8, 244, 6, "props"], [269, 6, 245, 4], [269, 7, 245, 5], [269, 8, 245, 6], [270, 4, 246, 2], [271, 4, 247, 2, "drawDiffRect"], [271, 16, 247, 14, "drawDiffRect"], [271, 17, 247, 15, "props"], [271, 22, 247, 20], [271, 24, 247, 22], [272, 6, 248, 4], [272, 10, 248, 8], [272, 11, 248, 9, "add"], [272, 14, 248, 12], [272, 15, 248, 13], [273, 8, 249, 6, "type"], [273, 12, 249, 10], [273, 14, 249, 12, "CommandType"], [273, 31, 249, 23], [273, 32, 249, 24, "DrawDiffRect"], [273, 44, 249, 36], [274, 8, 250, 6, "props"], [275, 6, 251, 4], [275, 7, 251, 5], [275, 8, 251, 6], [276, 4, 252, 2], [277, 4, 253, 2, "drawText"], [277, 12, 253, 10, "drawText"], [277, 13, 253, 11, "props"], [277, 18, 253, 16], [277, 20, 253, 18], [278, 6, 254, 4], [278, 10, 254, 8], [278, 11, 254, 9, "add"], [278, 14, 254, 12], [278, 15, 254, 13], [279, 8, 255, 6, "type"], [279, 12, 255, 10], [279, 14, 255, 12, "CommandType"], [279, 31, 255, 23], [279, 32, 255, 24, "DrawText"], [279, 40, 255, 32], [280, 8, 256, 6, "props"], [281, 6, 257, 4], [281, 7, 257, 5], [281, 8, 257, 6], [282, 4, 258, 2], [283, 4, 259, 2, "drawTextPath"], [283, 16, 259, 14, "drawTextPath"], [283, 17, 259, 15, "props"], [283, 22, 259, 20], [283, 24, 259, 22], [284, 6, 260, 4], [284, 10, 260, 8], [284, 11, 260, 9, "add"], [284, 14, 260, 12], [284, 15, 260, 13], [285, 8, 261, 6, "type"], [285, 12, 261, 10], [285, 14, 261, 12, "CommandType"], [285, 31, 261, 23], [285, 32, 261, 24, "DrawTextPath"], [285, 44, 261, 36], [286, 8, 262, 6, "props"], [287, 6, 263, 4], [287, 7, 263, 5], [287, 8, 263, 6], [288, 4, 264, 2], [289, 4, 265, 2, "drawTextBlob"], [289, 16, 265, 14, "drawTextBlob"], [289, 17, 265, 15, "props"], [289, 22, 265, 20], [289, 24, 265, 22], [290, 6, 266, 4], [290, 10, 266, 8], [290, 11, 266, 9, "add"], [290, 14, 266, 12], [290, 15, 266, 13], [291, 8, 267, 6, "type"], [291, 12, 267, 10], [291, 14, 267, 12, "CommandType"], [291, 31, 267, 23], [291, 32, 267, 24, "DrawTextBlob"], [291, 44, 267, 36], [292, 8, 268, 6, "props"], [293, 6, 269, 4], [293, 7, 269, 5], [293, 8, 269, 6], [294, 4, 270, 2], [295, 4, 271, 2, "drawGlyphs"], [295, 14, 271, 12, "drawGlyphs"], [295, 15, 271, 13, "props"], [295, 20, 271, 18], [295, 22, 271, 20], [296, 6, 272, 4], [296, 10, 272, 8], [296, 11, 272, 9, "add"], [296, 14, 272, 12], [296, 15, 272, 13], [297, 8, 273, 6, "type"], [297, 12, 273, 10], [297, 14, 273, 12, "CommandType"], [297, 31, 273, 23], [297, 32, 273, 24, "DrawGlyphs"], [297, 42, 273, 34], [298, 8, 274, 6, "props"], [299, 6, 275, 4], [299, 7, 275, 5], [299, 8, 275, 6], [300, 4, 276, 2], [301, 4, 277, 2, "drawPicture"], [301, 15, 277, 13, "drawPicture"], [301, 16, 277, 14, "props"], [301, 21, 277, 19], [301, 23, 277, 21], [302, 6, 278, 4], [302, 10, 278, 8], [302, 11, 278, 9, "add"], [302, 14, 278, 12], [302, 15, 278, 13], [303, 8, 279, 6, "type"], [303, 12, 279, 10], [303, 14, 279, 12, "CommandType"], [303, 31, 279, 23], [303, 32, 279, 24, "DrawPicture"], [303, 43, 279, 35], [304, 8, 280, 6, "props"], [305, 6, 281, 4], [305, 7, 281, 5], [305, 8, 281, 6], [306, 4, 282, 2], [307, 4, 283, 2, "drawImageSVG"], [307, 16, 283, 14, "drawImageSVG"], [307, 17, 283, 15, "props"], [307, 22, 283, 20], [307, 24, 283, 22], [308, 6, 284, 4], [308, 10, 284, 8], [308, 11, 284, 9, "add"], [308, 14, 284, 12], [308, 15, 284, 13], [309, 8, 285, 6, "type"], [309, 12, 285, 10], [309, 14, 285, 12, "CommandType"], [309, 31, 285, 23], [309, 32, 285, 24, "DrawImageSVG"], [309, 44, 285, 36], [310, 8, 286, 6, "props"], [311, 6, 287, 4], [311, 7, 287, 5], [311, 8, 287, 6], [312, 4, 288, 2], [313, 4, 289, 2, "drawParagraph"], [313, 17, 289, 15, "drawParagraph"], [313, 18, 289, 16, "props"], [313, 23, 289, 21], [313, 25, 289, 23], [314, 6, 290, 4], [314, 10, 290, 8], [314, 11, 290, 9, "add"], [314, 14, 290, 12], [314, 15, 290, 13], [315, 8, 291, 6, "type"], [315, 12, 291, 10], [315, 14, 291, 12, "CommandType"], [315, 31, 291, 23], [315, 32, 291, 24, "DrawParagraph"], [315, 45, 291, 37], [316, 8, 292, 6, "props"], [317, 6, 293, 4], [317, 7, 293, 5], [317, 8, 293, 6], [318, 4, 294, 2], [319, 4, 295, 2, "drawAtlas"], [319, 13, 295, 11, "drawAtlas"], [319, 14, 295, 12, "props"], [319, 19, 295, 17], [319, 21, 295, 19], [320, 6, 296, 4], [320, 10, 296, 8], [320, 11, 296, 9, "add"], [320, 14, 296, 12], [320, 15, 296, 13], [321, 8, 297, 6, "type"], [321, 12, 297, 10], [321, 14, 297, 12, "CommandType"], [321, 31, 297, 23], [321, 32, 297, 24, "DrawAtlas"], [321, 41, 297, 33], [322, 8, 298, 6, "props"], [323, 6, 299, 4], [323, 7, 299, 5], [323, 8, 299, 6], [324, 4, 300, 2], [325, 2, 301, 0], [326, 2, 301, 1, "exports"], [326, 9, 301, 1], [326, 10, 301, 1, "Recorder"], [326, 18, 301, 1], [326, 21, 301, 1, "Recorder"], [326, 29, 301, 1], [327, 0, 301, 1], [327, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "Recorder", "constructor", "getRecording", "processProps", "add", "saveGroup", "restoreGroup", "save<PERSON><PERSON>t", "<PERSON><PERSON><PERSON><PERSON>", "restorePaintDeclaration", "materialize<PERSON><PERSON><PERSON>", "pushPathEffect", "pushImageFilter", "pushColorFilter", "push<PERSON><PERSON>er", "pushBlurMaskFilter", "composePathEffect", "composeColorFilter", "composeImageFilter", "saveCTM", "restoreCTM", "<PERSON><PERSON><PERSON><PERSON>", "save<PERSON><PERSON><PERSON>", "saveBackdropFilter", "drawBox", "shadows.forEach$argument_0", "drawImage", "drawCircle", "drawPoints", "drawPath", "drawRect", "drawRRect", "drawOval", "drawLine", "drawPatch", "drawVertices", "drawDiffRect", "drawText", "drawTextPath", "drawTextBlob", "drawGlyphs", "drawPicture", "drawImageSVG", "drawParagraph", "drawAtlas"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGK;ECC;GDK;EEC;GFM;EGC;GHe;EIC;GJU;EKC;GLO;EMC;GNE;EOC;GPK;EQC;GRI;ESC;GTI;EUC;GVI;EWC;GXS;EYC;GZS;EaC;GbS;EcC;GdS;EeC;GfK;EgBC;GhBI;EiBC;GjBI;EkBC;GlBI;EmBC;GnBK;EoBC;GpBI;EqBC;GrBI;EsBC;GtBI;EuBC;GvBI;EwBC;oBCC;KDW;GxBM;E0BC;G1BK;E2BC;G3BK;E4BC;G5BK;E6BC;G7BK;E8BC;G9BK;E+BC;G/BK;EgCC;GhCK;EiCC;GjCK;EkCC;GlCK;EmCC;GnCK;EoCC;GpCK;EqCC;GrCK;EsCC;GtCK;EuCC;GvCK;EwCC;GxCK;EyCC;GzCK;E0CC;G1CK;E2CC;G3CK;E4CC;G5CK;CHC"}}, "type": "js/module"}]}