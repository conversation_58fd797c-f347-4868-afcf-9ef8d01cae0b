{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./WebCameraUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 52, "index": 84}}], "key": "7QFlRc+R5Zhr/RLcJUPMtLUHEsc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useWebQRScanner = useWebQRScanner;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _WebCameraUtils = require(_dependencyMap[1], \"./WebCameraUtils\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const qrWorkerMethod = ({\n    data,\n    width,\n    height\n  }) => {\n    // eslint-disable-next-line no-undef\n    const decoded = self.jsQR(data, width, height, {\n      inversionAttempts: 'attemptBoth'\n    });\n    let parsed;\n    try {\n      parsed = JSON.parse(decoded);\n    } catch {\n      parsed = decoded;\n    }\n    if (parsed?.data) {\n      const nativeEvent = {\n        type: 'qr',\n        data: parsed.data,\n        cornerPoints: [],\n        bounds: {\n          origin: {\n            x: 0,\n            y: 0\n          },\n          size: {\n            width: 0,\n            height: 0\n          }\n        }\n      };\n      if (parsed.location) {\n        nativeEvent.cornerPoints = [parsed.location.topLeftCorner, parsed.location.bottomLeftCorner, parsed.location.topRightCorner, parsed.location.bottomRightCorner];\n      }\n      return nativeEvent;\n    }\n    return parsed;\n  };\n  const createWorkerAsyncFunction = (fn, deps) => {\n    if (typeof window === 'undefined') {\n      return async () => {\n        throw new Error('Cannot use createWorkerAsyncFunction in a non-browser environment');\n      };\n    }\n    const stringifiedFn = [`self.func = ${fn.toString()};`, 'self.onmessage = (e) => {', '  const result = self.func(e.data);', '  self.postMessage(result);', '};'];\n    if (deps.length > 0) {\n      stringifiedFn.unshift(`importScripts(${deps.map(dep => `'${dep}'`).join(', ')});`);\n    }\n    const blob = new Blob(stringifiedFn, {\n      type: 'text/javascript'\n    });\n    const worker = new Worker(URL.createObjectURL(blob));\n    // First-In First-Out queue of promises\n    const promises = [];\n    worker.onmessage = e => promises.shift()?.resolve(e.data);\n    return data => {\n      return new Promise((resolve, reject) => {\n        promises.push({\n          resolve,\n          reject\n        });\n        worker.postMessage(data);\n      });\n    };\n  };\n  const decode = createWorkerAsyncFunction(qrWorkerMethod, ['https://cdn.jsdelivr.net/npm/jsqr@1.2.0/dist/jsQR.min.js']);\n  function useWebQRScanner(video, {\n    isEnabled,\n    captureOptions,\n    interval,\n    onScanned,\n    onError\n  }) {\n    const isRunning = React.useRef(false);\n    const timeout = React.useRef(undefined);\n    async function scanAsync() {\n      // If interval is 0 then only scan once.\n      if (!isRunning.current || !onScanned) {\n        stop();\n        return;\n      }\n      try {\n        const data = (0, _WebCameraUtils.captureImageData)(video.current, captureOptions);\n        if (data) {\n          const nativeEvent = await decode(data);\n          if (nativeEvent?.data) {\n            onScanned({\n              nativeEvent\n            });\n          }\n        }\n      } catch (error) {\n        if (onError) {\n          onError({\n            nativeEvent: error\n          });\n        }\n      } finally {\n        // If interval is 0 then only scan once.\n        if (interval === 0) {\n          stop();\n          return;\n        }\n        const intervalToUse = !interval || interval < 0 ? 16 : interval;\n        // @ts-ignore: Type 'Timeout' is not assignable to type 'number'\n        timeout.current = setTimeout(() => {\n          scanAsync();\n        }, intervalToUse);\n      }\n    }\n    function stop() {\n      isRunning.current = false;\n      clearTimeout(timeout.current);\n    }\n    React.useEffect(() => {\n      if (isEnabled) {\n        isRunning.current = true;\n        scanAsync();\n      }\n      return () => {\n        if (isEnabled) {\n          stop();\n        }\n      };\n    }, [isEnabled]);\n  }\n});", "lineCount": 135, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "React"], [6, 11, 1, 0], [6, 14, 1, 0, "_interopRequireWildcard"], [6, 37, 1, 0], [6, 38, 1, 0, "require"], [6, 45, 1, 0], [6, 46, 1, 0, "_dependencyMap"], [6, 60, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_WebCameraUtils"], [7, 21, 2, 0], [7, 24, 2, 0, "require"], [7, 31, 2, 0], [7, 32, 2, 0, "_dependencyMap"], [7, 46, 2, 0], [8, 2, 2, 52], [8, 11, 2, 52, "_interopRequireWildcard"], [8, 35, 2, 52, "e"], [8, 36, 2, 52], [8, 38, 2, 52, "t"], [8, 39, 2, 52], [8, 68, 2, 52, "WeakMap"], [8, 75, 2, 52], [8, 81, 2, 52, "r"], [8, 82, 2, 52], [8, 89, 2, 52, "WeakMap"], [8, 96, 2, 52], [8, 100, 2, 52, "n"], [8, 101, 2, 52], [8, 108, 2, 52, "WeakMap"], [8, 115, 2, 52], [8, 127, 2, 52, "_interopRequireWildcard"], [8, 150, 2, 52], [8, 162, 2, 52, "_interopRequireWildcard"], [8, 163, 2, 52, "e"], [8, 164, 2, 52], [8, 166, 2, 52, "t"], [8, 167, 2, 52], [8, 176, 2, 52, "t"], [8, 177, 2, 52], [8, 181, 2, 52, "e"], [8, 182, 2, 52], [8, 186, 2, 52, "e"], [8, 187, 2, 52], [8, 188, 2, 52, "__esModule"], [8, 198, 2, 52], [8, 207, 2, 52, "e"], [8, 208, 2, 52], [8, 214, 2, 52, "o"], [8, 215, 2, 52], [8, 217, 2, 52, "i"], [8, 218, 2, 52], [8, 220, 2, 52, "f"], [8, 221, 2, 52], [8, 226, 2, 52, "__proto__"], [8, 235, 2, 52], [8, 243, 2, 52, "default"], [8, 250, 2, 52], [8, 252, 2, 52, "e"], [8, 253, 2, 52], [8, 270, 2, 52, "e"], [8, 271, 2, 52], [8, 294, 2, 52, "e"], [8, 295, 2, 52], [8, 320, 2, 52, "e"], [8, 321, 2, 52], [8, 330, 2, 52, "f"], [8, 331, 2, 52], [8, 337, 2, 52, "o"], [8, 338, 2, 52], [8, 341, 2, 52, "t"], [8, 342, 2, 52], [8, 345, 2, 52, "n"], [8, 346, 2, 52], [8, 349, 2, 52, "r"], [8, 350, 2, 52], [8, 358, 2, 52, "o"], [8, 359, 2, 52], [8, 360, 2, 52, "has"], [8, 363, 2, 52], [8, 364, 2, 52, "e"], [8, 365, 2, 52], [8, 375, 2, 52, "o"], [8, 376, 2, 52], [8, 377, 2, 52, "get"], [8, 380, 2, 52], [8, 381, 2, 52, "e"], [8, 382, 2, 52], [8, 385, 2, 52, "o"], [8, 386, 2, 52], [8, 387, 2, 52, "set"], [8, 390, 2, 52], [8, 391, 2, 52, "e"], [8, 392, 2, 52], [8, 394, 2, 52, "f"], [8, 395, 2, 52], [8, 411, 2, 52, "t"], [8, 412, 2, 52], [8, 416, 2, 52, "e"], [8, 417, 2, 52], [8, 433, 2, 52, "t"], [8, 434, 2, 52], [8, 441, 2, 52, "hasOwnProperty"], [8, 455, 2, 52], [8, 456, 2, 52, "call"], [8, 460, 2, 52], [8, 461, 2, 52, "e"], [8, 462, 2, 52], [8, 464, 2, 52, "t"], [8, 465, 2, 52], [8, 472, 2, 52, "i"], [8, 473, 2, 52], [8, 477, 2, 52, "o"], [8, 478, 2, 52], [8, 481, 2, 52, "Object"], [8, 487, 2, 52], [8, 488, 2, 52, "defineProperty"], [8, 502, 2, 52], [8, 507, 2, 52, "Object"], [8, 513, 2, 52], [8, 514, 2, 52, "getOwnPropertyDescriptor"], [8, 538, 2, 52], [8, 539, 2, 52, "e"], [8, 540, 2, 52], [8, 542, 2, 52, "t"], [8, 543, 2, 52], [8, 550, 2, 52, "i"], [8, 551, 2, 52], [8, 552, 2, 52, "get"], [8, 555, 2, 52], [8, 559, 2, 52, "i"], [8, 560, 2, 52], [8, 561, 2, 52, "set"], [8, 564, 2, 52], [8, 568, 2, 52, "o"], [8, 569, 2, 52], [8, 570, 2, 52, "f"], [8, 571, 2, 52], [8, 573, 2, 52, "t"], [8, 574, 2, 52], [8, 576, 2, 52, "i"], [8, 577, 2, 52], [8, 581, 2, 52, "f"], [8, 582, 2, 52], [8, 583, 2, 52, "t"], [8, 584, 2, 52], [8, 588, 2, 52, "e"], [8, 589, 2, 52], [8, 590, 2, 52, "t"], [8, 591, 2, 52], [8, 602, 2, 52, "f"], [8, 603, 2, 52], [8, 608, 2, 52, "e"], [8, 609, 2, 52], [8, 611, 2, 52, "t"], [8, 612, 2, 52], [9, 2, 3, 0], [9, 8, 3, 6, "qrWorkerMethod"], [9, 22, 3, 20], [9, 25, 3, 23, "qrWorkerMethod"], [9, 26, 3, 24], [10, 4, 3, 26, "data"], [10, 8, 3, 30], [11, 4, 3, 32, "width"], [11, 9, 3, 37], [12, 4, 3, 39, "height"], [13, 2, 3, 46], [13, 3, 3, 47], [13, 8, 3, 52], [14, 4, 4, 4], [15, 4, 5, 4], [15, 10, 5, 10, "decoded"], [15, 17, 5, 17], [15, 20, 5, 20, "self"], [15, 24, 5, 24], [15, 25, 5, 25, "jsQR"], [15, 29, 5, 29], [15, 30, 5, 30, "data"], [15, 34, 5, 34], [15, 36, 5, 36, "width"], [15, 41, 5, 41], [15, 43, 5, 43, "height"], [15, 49, 5, 49], [15, 51, 5, 51], [16, 6, 6, 8, "inversionAttempts"], [16, 23, 6, 25], [16, 25, 6, 27], [17, 4, 7, 4], [17, 5, 7, 5], [17, 6, 7, 6], [18, 4, 8, 4], [18, 8, 8, 8, "parsed"], [18, 14, 8, 14], [19, 4, 9, 4], [19, 8, 9, 8], [20, 6, 10, 8, "parsed"], [20, 12, 10, 14], [20, 15, 10, 17, "JSON"], [20, 19, 10, 21], [20, 20, 10, 22, "parse"], [20, 25, 10, 27], [20, 26, 10, 28, "decoded"], [20, 33, 10, 35], [20, 34, 10, 36], [21, 4, 11, 4], [21, 5, 11, 5], [21, 6, 12, 4], [21, 12, 12, 10], [22, 6, 13, 8, "parsed"], [22, 12, 13, 14], [22, 15, 13, 17, "decoded"], [22, 22, 13, 24], [23, 4, 14, 4], [24, 4, 15, 4], [24, 8, 15, 8, "parsed"], [24, 14, 15, 14], [24, 16, 15, 16, "data"], [24, 20, 15, 20], [24, 22, 15, 22], [25, 6, 16, 8], [25, 12, 16, 14, "nativeEvent"], [25, 23, 16, 25], [25, 26, 16, 28], [26, 8, 17, 12, "type"], [26, 12, 17, 16], [26, 14, 17, 18], [26, 18, 17, 22], [27, 8, 18, 12, "data"], [27, 12, 18, 16], [27, 14, 18, 18, "parsed"], [27, 20, 18, 24], [27, 21, 18, 25, "data"], [27, 25, 18, 29], [28, 8, 19, 12, "cornerPoints"], [28, 20, 19, 24], [28, 22, 19, 26], [28, 24, 19, 28], [29, 8, 20, 12, "bounds"], [29, 14, 20, 18], [29, 16, 20, 20], [30, 10, 20, 22, "origin"], [30, 16, 20, 28], [30, 18, 20, 30], [31, 12, 20, 32, "x"], [31, 13, 20, 33], [31, 15, 20, 35], [31, 16, 20, 36], [32, 12, 20, 38, "y"], [32, 13, 20, 39], [32, 15, 20, 41], [33, 10, 20, 43], [33, 11, 20, 44], [34, 10, 20, 46, "size"], [34, 14, 20, 50], [34, 16, 20, 52], [35, 12, 20, 54, "width"], [35, 17, 20, 59], [35, 19, 20, 61], [35, 20, 20, 62], [36, 12, 20, 64, "height"], [36, 18, 20, 70], [36, 20, 20, 72], [37, 10, 20, 74], [38, 8, 20, 76], [39, 6, 21, 8], [39, 7, 21, 9], [40, 6, 22, 8], [40, 10, 22, 12, "parsed"], [40, 16, 22, 18], [40, 17, 22, 19, "location"], [40, 25, 22, 27], [40, 27, 22, 29], [41, 8, 23, 12, "nativeEvent"], [41, 19, 23, 23], [41, 20, 23, 24, "cornerPoints"], [41, 32, 23, 36], [41, 35, 23, 39], [41, 36, 24, 16, "parsed"], [41, 42, 24, 22], [41, 43, 24, 23, "location"], [41, 51, 24, 31], [41, 52, 24, 32, "topLeftCorner"], [41, 65, 24, 45], [41, 67, 25, 16, "parsed"], [41, 73, 25, 22], [41, 74, 25, 23, "location"], [41, 82, 25, 31], [41, 83, 25, 32, "bottomLeftCorner"], [41, 99, 25, 48], [41, 101, 26, 16, "parsed"], [41, 107, 26, 22], [41, 108, 26, 23, "location"], [41, 116, 26, 31], [41, 117, 26, 32, "topRightCorner"], [41, 131, 26, 46], [41, 133, 27, 16, "parsed"], [41, 139, 27, 22], [41, 140, 27, 23, "location"], [41, 148, 27, 31], [41, 149, 27, 32, "bottomRightCorner"], [41, 166, 27, 49], [41, 167, 28, 13], [42, 6, 29, 8], [43, 6, 30, 8], [43, 13, 30, 15, "nativeEvent"], [43, 24, 30, 26], [44, 4, 31, 4], [45, 4, 32, 4], [45, 11, 32, 11, "parsed"], [45, 17, 32, 17], [46, 2, 33, 0], [46, 3, 33, 1], [47, 2, 34, 0], [47, 8, 34, 6, "createWorkerAsyncFunction"], [47, 33, 34, 31], [47, 36, 34, 34, "createWorkerAsyncFunction"], [47, 37, 34, 35, "fn"], [47, 39, 34, 37], [47, 41, 34, 39, "deps"], [47, 45, 34, 43], [47, 50, 34, 48], [48, 4, 35, 4], [48, 8, 35, 8], [48, 15, 35, 15, "window"], [48, 21, 35, 21], [48, 26, 35, 26], [48, 37, 35, 37], [48, 39, 35, 39], [49, 6, 36, 8], [49, 13, 36, 15], [49, 25, 36, 27], [50, 8, 37, 12], [50, 14, 37, 18], [50, 18, 37, 22, "Error"], [50, 23, 37, 27], [50, 24, 37, 28], [50, 91, 37, 95], [50, 92, 37, 96], [51, 6, 38, 8], [51, 7, 38, 9], [52, 4, 39, 4], [53, 4, 40, 4], [53, 10, 40, 10, "stringifiedFn"], [53, 23, 40, 23], [53, 26, 40, 26], [53, 27, 41, 8], [53, 42, 41, 23, "fn"], [53, 44, 41, 25], [53, 45, 41, 26, "toString"], [53, 53, 41, 34], [53, 54, 41, 35], [53, 55, 41, 36], [53, 58, 41, 39], [53, 60, 42, 8], [53, 87, 42, 35], [53, 89, 43, 8], [53, 126, 43, 45], [53, 128, 44, 8], [53, 157, 44, 37], [53, 159, 45, 8], [53, 163, 45, 12], [53, 164, 46, 5], [54, 4, 47, 4], [54, 8, 47, 8, "deps"], [54, 12, 47, 12], [54, 13, 47, 13, "length"], [54, 19, 47, 19], [54, 22, 47, 22], [54, 23, 47, 23], [54, 25, 47, 25], [55, 6, 48, 8, "stringifiedFn"], [55, 19, 48, 21], [55, 20, 48, 22, "unshift"], [55, 27, 48, 29], [55, 28, 48, 30], [55, 45, 48, 47, "deps"], [55, 49, 48, 51], [55, 50, 48, 52, "map"], [55, 53, 48, 55], [55, 54, 48, 57, "dep"], [55, 57, 48, 60], [55, 61, 48, 65], [55, 65, 48, 69, "dep"], [55, 68, 48, 72], [55, 71, 48, 75], [55, 72, 48, 76], [55, 73, 48, 77, "join"], [55, 77, 48, 81], [55, 78, 48, 82], [55, 82, 48, 86], [55, 83, 48, 87], [55, 87, 48, 91], [55, 88, 48, 92], [56, 4, 49, 4], [57, 4, 50, 4], [57, 10, 50, 10, "blob"], [57, 14, 50, 14], [57, 17, 50, 17], [57, 21, 50, 21, "Blob"], [57, 25, 50, 25], [57, 26, 50, 26, "stringifiedFn"], [57, 39, 50, 39], [57, 41, 50, 41], [58, 6, 50, 43, "type"], [58, 10, 50, 47], [58, 12, 50, 49], [59, 4, 50, 67], [59, 5, 50, 68], [59, 6, 50, 69], [60, 4, 51, 4], [60, 10, 51, 10, "worker"], [60, 16, 51, 16], [60, 19, 51, 19], [60, 23, 51, 23, "Worker"], [60, 29, 51, 29], [60, 30, 51, 30, "URL"], [60, 33, 51, 33], [60, 34, 51, 34, "createObjectURL"], [60, 49, 51, 49], [60, 50, 51, 50, "blob"], [60, 54, 51, 54], [60, 55, 51, 55], [60, 56, 51, 56], [61, 4, 52, 4], [62, 4, 53, 4], [62, 10, 53, 10, "promises"], [62, 18, 53, 18], [62, 21, 53, 21], [62, 23, 53, 23], [63, 4, 54, 4, "worker"], [63, 10, 54, 10], [63, 11, 54, 11, "onmessage"], [63, 20, 54, 20], [63, 23, 54, 24, "e"], [63, 24, 54, 25], [63, 28, 54, 30, "promises"], [63, 36, 54, 38], [63, 37, 54, 39, "shift"], [63, 42, 54, 44], [63, 43, 54, 45], [63, 44, 54, 46], [63, 46, 54, 48, "resolve"], [63, 53, 54, 55], [63, 54, 54, 56, "e"], [63, 55, 54, 57], [63, 56, 54, 58, "data"], [63, 60, 54, 62], [63, 61, 54, 63], [64, 4, 55, 4], [64, 11, 55, 12, "data"], [64, 15, 55, 16], [64, 19, 55, 21], [65, 6, 56, 8], [65, 13, 56, 15], [65, 17, 56, 19, "Promise"], [65, 24, 56, 26], [65, 25, 56, 27], [65, 26, 56, 28, "resolve"], [65, 33, 56, 35], [65, 35, 56, 37, "reject"], [65, 41, 56, 43], [65, 46, 56, 48], [66, 8, 57, 12, "promises"], [66, 16, 57, 20], [66, 17, 57, 21, "push"], [66, 21, 57, 25], [66, 22, 57, 26], [67, 10, 57, 28, "resolve"], [67, 17, 57, 35], [68, 10, 57, 37, "reject"], [69, 8, 57, 44], [69, 9, 57, 45], [69, 10, 57, 46], [70, 8, 58, 12, "worker"], [70, 14, 58, 18], [70, 15, 58, 19, "postMessage"], [70, 26, 58, 30], [70, 27, 58, 31, "data"], [70, 31, 58, 35], [70, 32, 58, 36], [71, 6, 59, 8], [71, 7, 59, 9], [71, 8, 59, 10], [72, 4, 60, 4], [72, 5, 60, 5], [73, 2, 61, 0], [73, 3, 61, 1], [74, 2, 62, 0], [74, 8, 62, 6, "decode"], [74, 14, 62, 12], [74, 17, 62, 15, "createWorkerAsyncFunction"], [74, 42, 62, 40], [74, 43, 62, 41, "qrWorkerMethod"], [74, 57, 62, 55], [74, 59, 62, 57], [74, 60, 63, 4], [74, 118, 63, 62], [74, 119, 64, 1], [74, 120, 64, 2], [75, 2, 65, 7], [75, 11, 65, 16, "useWebQRScanner"], [75, 26, 65, 31, "useWebQRScanner"], [75, 27, 65, 32, "video"], [75, 32, 65, 37], [75, 34, 65, 39], [76, 4, 65, 41, "isEnabled"], [76, 13, 65, 50], [77, 4, 65, 52, "captureOptions"], [77, 18, 65, 66], [78, 4, 65, 68, "interval"], [78, 12, 65, 76], [79, 4, 65, 78, "onScanned"], [79, 13, 65, 87], [80, 4, 65, 89, "onError"], [81, 2, 65, 98], [81, 3, 65, 99], [81, 5, 65, 101], [82, 4, 66, 4], [82, 10, 66, 10, "isRunning"], [82, 19, 66, 19], [82, 22, 66, 22, "React"], [82, 27, 66, 27], [82, 28, 66, 28, "useRef"], [82, 34, 66, 34], [82, 35, 66, 35], [82, 40, 66, 40], [82, 41, 66, 41], [83, 4, 67, 4], [83, 10, 67, 10, "timeout"], [83, 17, 67, 17], [83, 20, 67, 20, "React"], [83, 25, 67, 25], [83, 26, 67, 26, "useRef"], [83, 32, 67, 32], [83, 33, 67, 33, "undefined"], [83, 42, 67, 42], [83, 43, 67, 43], [84, 4, 68, 4], [84, 19, 68, 19, "scanAsync"], [84, 28, 68, 28, "scanAsync"], [84, 29, 68, 28], [84, 31, 68, 31], [85, 6, 69, 8], [86, 6, 70, 8], [86, 10, 70, 12], [86, 11, 70, 13, "isRunning"], [86, 20, 70, 22], [86, 21, 70, 23, "current"], [86, 28, 70, 30], [86, 32, 70, 34], [86, 33, 70, 35, "onScanned"], [86, 42, 70, 44], [86, 44, 70, 46], [87, 8, 71, 12, "stop"], [87, 12, 71, 16], [87, 13, 71, 17], [87, 14, 71, 18], [88, 8, 72, 12], [89, 6, 73, 8], [90, 6, 74, 8], [90, 10, 74, 12], [91, 8, 75, 12], [91, 14, 75, 18, "data"], [91, 18, 75, 22], [91, 21, 75, 25], [91, 25, 75, 25, "captureImageData"], [91, 57, 75, 41], [91, 59, 75, 42, "video"], [91, 64, 75, 47], [91, 65, 75, 48, "current"], [91, 72, 75, 55], [91, 74, 75, 57, "captureOptions"], [91, 88, 75, 71], [91, 89, 75, 72], [92, 8, 76, 12], [92, 12, 76, 16, "data"], [92, 16, 76, 20], [92, 18, 76, 22], [93, 10, 77, 16], [93, 16, 77, 22, "nativeEvent"], [93, 27, 77, 33], [93, 30, 77, 36], [93, 36, 77, 42, "decode"], [93, 42, 77, 48], [93, 43, 77, 49, "data"], [93, 47, 77, 53], [93, 48, 77, 54], [94, 10, 78, 16], [94, 14, 78, 20, "nativeEvent"], [94, 25, 78, 31], [94, 27, 78, 33, "data"], [94, 31, 78, 37], [94, 33, 78, 39], [95, 12, 79, 20, "onScanned"], [95, 21, 79, 29], [95, 22, 79, 30], [96, 14, 80, 24, "nativeEvent"], [97, 12, 81, 20], [97, 13, 81, 21], [97, 14, 81, 22], [98, 10, 82, 16], [99, 8, 83, 12], [100, 6, 84, 8], [100, 7, 84, 9], [100, 8, 85, 8], [100, 15, 85, 15, "error"], [100, 20, 85, 20], [100, 22, 85, 22], [101, 8, 86, 12], [101, 12, 86, 16, "onError"], [101, 19, 86, 23], [101, 21, 86, 25], [102, 10, 87, 16, "onError"], [102, 17, 87, 23], [102, 18, 87, 24], [103, 12, 87, 26, "nativeEvent"], [103, 23, 87, 37], [103, 25, 87, 39, "error"], [104, 10, 87, 45], [104, 11, 87, 46], [104, 12, 87, 47], [105, 8, 88, 12], [106, 6, 89, 8], [106, 7, 89, 9], [106, 16, 90, 16], [107, 8, 91, 12], [108, 8, 92, 12], [108, 12, 92, 16, "interval"], [108, 20, 92, 24], [108, 25, 92, 29], [108, 26, 92, 30], [108, 28, 92, 32], [109, 10, 93, 16, "stop"], [109, 14, 93, 20], [109, 15, 93, 21], [109, 16, 93, 22], [110, 10, 94, 16], [111, 8, 95, 12], [112, 8, 96, 12], [112, 14, 96, 18, "intervalToUse"], [112, 27, 96, 31], [112, 30, 96, 34], [112, 31, 96, 35, "interval"], [112, 39, 96, 43], [112, 43, 96, 47, "interval"], [112, 51, 96, 55], [112, 54, 96, 58], [112, 55, 96, 59], [112, 58, 96, 62], [112, 60, 96, 64], [112, 63, 96, 67, "interval"], [112, 71, 96, 75], [113, 8, 97, 12], [114, 8, 98, 12, "timeout"], [114, 15, 98, 19], [114, 16, 98, 20, "current"], [114, 23, 98, 27], [114, 26, 98, 30, "setTimeout"], [114, 36, 98, 40], [114, 37, 98, 41], [114, 43, 98, 47], [115, 10, 99, 16, "scanAsync"], [115, 19, 99, 25], [115, 20, 99, 26], [115, 21, 99, 27], [116, 8, 100, 12], [116, 9, 100, 13], [116, 11, 100, 15, "intervalToUse"], [116, 24, 100, 28], [116, 25, 100, 29], [117, 6, 101, 8], [118, 4, 102, 4], [119, 4, 103, 4], [119, 13, 103, 13, "stop"], [119, 17, 103, 17, "stop"], [119, 18, 103, 17], [119, 20, 103, 20], [120, 6, 104, 8, "isRunning"], [120, 15, 104, 17], [120, 16, 104, 18, "current"], [120, 23, 104, 25], [120, 26, 104, 28], [120, 31, 104, 33], [121, 6, 105, 8, "clearTimeout"], [121, 18, 105, 20], [121, 19, 105, 21, "timeout"], [121, 26, 105, 28], [121, 27, 105, 29, "current"], [121, 34, 105, 36], [121, 35, 105, 37], [122, 4, 106, 4], [123, 4, 107, 4, "React"], [123, 9, 107, 9], [123, 10, 107, 10, "useEffect"], [123, 19, 107, 19], [123, 20, 107, 20], [123, 26, 107, 26], [124, 6, 108, 8], [124, 10, 108, 12, "isEnabled"], [124, 19, 108, 21], [124, 21, 108, 23], [125, 8, 109, 12, "isRunning"], [125, 17, 109, 21], [125, 18, 109, 22, "current"], [125, 25, 109, 29], [125, 28, 109, 32], [125, 32, 109, 36], [126, 8, 110, 12, "scanAsync"], [126, 17, 110, 21], [126, 18, 110, 22], [126, 19, 110, 23], [127, 6, 111, 8], [128, 6, 112, 8], [128, 13, 112, 15], [128, 19, 112, 21], [129, 8, 113, 12], [129, 12, 113, 16, "isEnabled"], [129, 21, 113, 25], [129, 23, 113, 27], [130, 10, 114, 16, "stop"], [130, 14, 114, 20], [130, 15, 114, 21], [130, 16, 114, 22], [131, 8, 115, 12], [132, 6, 116, 8], [132, 7, 116, 9], [133, 4, 117, 4], [133, 5, 117, 5], [133, 7, 117, 7], [133, 8, 117, 8, "isEnabled"], [133, 17, 117, 17], [133, 18, 117, 18], [133, 19, 117, 19], [134, 2, 118, 0], [135, 0, 118, 1], [135, 3]], "functionMap": {"names": ["<global>", "qrWorkerMethod", "createWorkerAsyncFunction", "<anonymous>", "deps.map$argument_0", "worker.onmessage", "Promise$argument_0", "useWebQRScanner", "scanAsync", "setTimeout$argument_0", "stop", "React.useEffect$argument_0"], "mappings": "AAA;uBCE;CD8B;kCEC;eCE;SDE;wDEU,mBF;uBGM,wCH;WCC;2BGC;SHG;KDC;CFC;OOI;ICG;yCC8B;aDE;KDE;IGC;KHG;oBIC;eRK;SQI;KJC;CPC"}}, "type": "js/module"}]}