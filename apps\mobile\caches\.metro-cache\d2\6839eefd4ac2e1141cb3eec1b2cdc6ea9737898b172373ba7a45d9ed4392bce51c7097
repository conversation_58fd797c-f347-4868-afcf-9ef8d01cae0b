{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 73, "index": 73}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TextInput", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "DmXc1F5dPYWntVgqRwh73w0VngA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 53, "column": 0, "index": 320}, "end": {"line": 53, "column": 67, "index": 387}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "expo-status-bar", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 57, "column": 0, "index": 395}, "end": {"line": 57, "column": 44, "index": 439}}], "key": "tlkgvZrxUMG8C7vDDJbsBGIlvhs=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 61, "column": 0, "index": 447}, "end": {"line": 61, "column": 59, "index": 506}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 65, "column": 0, "index": 514}, "end": {"line": 113, "column": 29, "index": 774}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-location", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 117, "column": 0, "index": 782}, "end": {"line": 117, "column": 42, "index": 824}}], "key": "GNP7AGCKsBRUhlnTZ4lIPpbkT9E=", "exportNames": ["*"]}}, {"name": "@/components/EchoCameraUnified", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 121, "column": 0, "index": 832}, "end": {"line": 121, "column": 63, "index": 895}}], "key": "wA7DUOcNir1rKeEVRofkWVobA04=", "exportNames": ["*"]}}, {"name": "@/components/camera/EchoCameraWeb", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 125, "column": 0, "index": 903}, "end": {"line": 125, "column": 62, "index": 965}}], "key": "a5L7e3cPb+NheECyYdST63CXrdc=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 129, "column": 0, "index": 973}, "end": {"line": 129, "column": 85, "index": 1058}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = RespondScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/ScrollView\"));\n  var _TextInput = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/TextInput\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Modal\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/Platform\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Image\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[11], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[12], \"react-native-safe-area-context\");\n  var _expoStatusBar = require(_dependencyMap[13], \"expo-status-bar\");\n  var _expoRouter = require(_dependencyMap[14], \"expo-router\");\n  var _lucideReactNative = require(_dependencyMap[15], \"lucide-react-native\");\n  var Location = _interopRequireWildcard(require(_dependencyMap[16], \"expo-location\"));\n  var _EchoCameraUnified = _interopRequireDefault(require(_dependencyMap[17], \"@/components/EchoCameraUnified\"));\n  var _EchoCameraWeb = _interopRequireDefault(require(_dependencyMap[18], \"@/components/camera/EchoCameraWeb\"));\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[19], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _jsxDevRuntime = require(_dependencyMap[20], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\app\\\\respond\\\\[id].jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function RespondScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const {\n      id\n    } = (0, _expoRouter.useLocalSearchParams)();\n    const [response, setResponse] = (0, _react.useState)(\"\");\n    const [showCamera, setShowCamera] = (0, _react.useState)(false);\n    const [cameraResult, setCameraResult] = (0, _react.useState)(null);\n    const [submitting, setSubmitting] = (0, _react.useState)(false);\n    const [capturedPhotoUri, setCapturedPhotoUri] = (0, _react.useState)(null);\n    const defaultTestingMode = (0, _react.useMemo)(() => {\n      if (_Platform.default.OS !== \"web\" || typeof window === \"undefined\") {\n        return false;\n      }\n      const {\n        protocol,\n        hostname\n      } = window.location;\n      const localHosts = [\"localhost\", \"127.0.0.1\", \"::1\"];\n      return protocol !== \"https:\" || localHosts.includes(hostname);\n    }, []);\n    const [testingMode, setTestingMode] = (0, _react.useState)(defaultTestingMode);\n\n    // Location verification state\n\n    const [locationStatus, setLocationStatus] = (0, _react.useState)(\"checking\"); // 'checking', 'verified', 'too_far', 'error'\n\n    const [currentLocation, setCurrentLocation] = (0, _react.useState)(null);\n    const [distance, setDistance] = (0, _react.useState)(null);\n    const [gettingLocation, setGettingLocation] = (0, _react.useState)(false);\n    const [locationError, setLocationError] = (0, _react.useState)(null);\n\n    // Mock question data - in real app, fetch based on id\n\n    const question = {\n      id: id,\n      question: \"Is the coffee shop on Main Street currently open? I need to know if they have seating available.\",\n      location: \"123 Main Street, Downtown\",\n      coordinates: {\n        // FOR TESTING: Updated to Amadora, Portugal coordinates for testing\n\n        latitude: 38.7555,\n        // Amadora, Portugal\n\n        longitude: -9.2337\n      },\n      reward: 2.5,\n      postedAt: \"2 hours ago\",\n      userId: \"user123\"\n    };\n    const questionLatitude = question.coordinates.latitude;\n    const questionLongitude = question.coordinates.longitude;\n\n    // Calculate distance between two coordinates in meters\n\n    const calculateDistance = (lat1, lon1, lat2, lon2) => {\n      const R = 6371e3; // Earth's radius in meters\n\n      const lat1Rad = lat1 * Math.PI / 180;\n      const lat2Rad = lat2 * Math.PI / 180;\n      const deltaLat = (lat2 - lat1) * Math.PI / 180;\n      const deltaLon = (lon2 - lon1) * Math.PI / 180;\n      const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);\n      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n      return R * c; // Distance in meters\n    };\n\n    // Verify user location\n\n    const verifyLocation = (0, _react.useCallback)(async () => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      try {\n        setGettingLocation(true);\n        setLocationError(null);\n        setLocationStatus(\"checking\");\n\n        // Request location permission\n\n        const {\n          status\n        } = await Location.requestForegroundPermissionsAsync();\n        if (status !== \"granted\") {\n          const message = _Platform.default.OS === \"web\" ? \"Allow location access in your browser settings or enable Testing Mode to continue without verification.\" : \"We need your location to verify you're at the question location.\";\n          setLocationError(message);\n          setLocationStatus(\"error\");\n          _Alert.default.alert(\"Location Required\", message);\n          return;\n        }\n\n        // Get current location\n\n        const locationData = await Location.getCurrentPositionAsync({\n          accuracy: Location.Accuracy.High,\n          timeout: 15000,\n          maximumAge: 60000\n        });\n        const userLat = locationData.coords.latitude;\n        const userLon = locationData.coords.longitude;\n        const questionLat = questionLatitude;\n        const questionLon = questionLongitude;\n\n        // Calculate distance\n\n        const distanceInMeters = calculateDistance(userLat, userLon, questionLat, questionLon);\n        setDistance(Math.round(distanceInMeters));\n        setCurrentLocation({\n          latitude: userLat,\n          longitude: userLon\n        });\n\n        // Check if user is within acceptable range (200 meters)\n\n        const maxDistance = 200;\n        if (distanceInMeters <= maxDistance) {\n          setLocationStatus(\"verified\");\n        } else {\n          setLocationStatus(\"too_far\");\n        }\n      } catch (error) {\n        console.error(\"Error verifying location:\", error);\n        let message = \"Could not verify your location. Please check your GPS and try again.\";\n        if (error?.code === 1) {\n          message = \"Location permission was denied. Enable access in your device or browser settings, or turn on Testing Mode.\";\n        } else if (error?.code === 2) {\n          message = \"We couldn't determine your position. Try moving to an open area or toggling airplane mode.\";\n        } else if (error?.code === 3) {\n          message = \"Location request timed out. Please try again.\";\n        } else if (_Platform.default.OS === \"web\" && typeof error?.message === \"string\" && error.message.toLowerCase().includes(\"secure\")) {\n          message = \"The browser blocked location services on this connection. Use https:// or enable Testing Mode for manual capture.\";\n        }\n        setLocationError(message);\n        setLocationStatus(\"error\");\n        _Alert.default.alert(\"Location Error\", message);\n      } finally {\n        setGettingLocation(false);\n      }\n    }, [questionLatitude, questionLongitude, testingMode]);\n\n    // Verify location on mount or when testing mode changes\n\n    (0, _react.useEffect)(() => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      verifyLocation();\n    }, [testingMode, verifyLocation]);\n    const handleStartCamera = () => {\n      console.log(\"Camera button pressed:\", {\n        locationStatus,\n        testingMode,\n        disabled: locationStatus !== \"verified\" && !testingMode,\n        shouldEnable: locationStatus === \"verified\" || testingMode,\n        existingCameraResult: cameraResult // Log existing result\n      });\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", locationStatus === \"too_far\" ? `You are ${distance || 0}m away from the question location. You need to be within 200m to respond.` : \"Please verify your location first.\");\n        return;\n      }\n      setShowCamera(true);\n    };\n    const handleCameraComplete = (0, _react.useCallback)(result => {\n      console.log('Camera result received:', result); // Debug log\n      console.log('🔍 DEBUGGING: Camera result properties:', {\n        imageUrl: result.imageUrl,\n        localUri: result.localUri,\n        uri: result.uri,\n        publicUrl: result.publicUrl,\n        timestamp: result.timestamp\n      });\n\n      // Extract the URI from various possible sources\n\n      let imageUri = result.imageUrl || result.localUri || result.uri || result.publicUrl;\n      console.log('🔍 DEBUGGING: Selected imageUri:', imageUri);\n\n      // Handle data URIs that might be malformed\n\n      if (imageUri && imageUri.startsWith('data:image')) {\n        // Ensure data URI is properly formatted\n\n        if (!imageUri.includes('base64,')) {\n          console.error('Invalid data URI format:', imageUri.substring(0, 50));\n          imageUri = null;\n        }\n      }\n\n      // For development, use a placeholder if no valid URI\n\n      if (!imageUri && __DEV__) {\n        console.warn('No valid image URI, using placeholder');\n        imageUri = 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Photo+Captured';\n      }\n      setCapturedPhotoUri(imageUri);\n\n      // Normalize the result to ensure we have the correct URI property\n\n      const normalizedResult = {\n        ...result,\n        imageUrl: imageUri,\n        localUri: imageUri,\n        // Store original URI for debugging\n\n        originalUri: result.imageUrl || result.localUri || result.uri || result.publicUrl\n      };\n      console.log('Normalized result with URI:', imageUri);\n      console.log('Full normalized result:', normalizedResult);\n      setCameraResult(normalizedResult);\n      setShowCamera(false);\n\n      // Removed redundant Alert - the UI will show the success state with image\n    }, []);\n    const handleCameraCancel = (0, _react.useCallback)(() => {\n      setShowCamera(false);\n      setCameraResult(null);\n      setCapturedPhotoUri(null);\n    }, []);\n    const submitResponse = async () => {\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", \"Please verify your location first.\");\n        return;\n      }\n      if (!cameraResult) {\n        _Alert.default.alert(\"Missing Photo\", \"Please take the required photo first.\");\n        return;\n      }\n      if (!response.trim()) {\n        _Alert.default.alert(\"Missing Text\", \"Please provide a text explanation with your photo.\");\n        return;\n      }\n      setSubmitting(true);\n      try {\n        // TODO: Submit to API\n\n        const responseData = {\n          questionId: id,\n          textResponse: response.trim(),\n          imageUrl: cameraResult.imageUrl,\n          challengeCode: cameraResult.challengeCode,\n          timestamp: cameraResult.timestamp,\n          userLocation: currentLocation,\n          distanceFromQuestion: distance,\n          testingMode: testingMode // Include testing mode flag\n        };\n        console.log(\"Submitting response:\", responseData);\n\n        // Simulate API call\n\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        _Alert.default.alert(\"Response Submitted!\", testingMode ? \"Test response submitted successfully! This was in testing mode.\" : `You'll receive $${question.reward.toFixed(2)} once the questioner confirms your response.`, [{\n          text: \"OK\",\n          onPress: () => _expoRouter.router.back()\n        }]);\n      } catch (error) {\n        console.error(\"Error submitting response:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to submit response. Please try again.\");\n      } finally {\n        setSubmitting(false);\n      }\n    };\n\n    // Location status component\n\n    const LocationStatus = () => {\n      const getStatusConfig = () => {\n        switch (locationStatus) {\n          case \"checking\":\n            return {\n              color: \"#F59E0B\",\n              bgColor: \"#FEF3C7\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#D97706\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 19\n              }, this),\n              title: \"Checking Location\",\n              message: gettingLocation ? \"Getting your current location\" : \"Verifying position\"\n            };\n          case \"verified\":\n            return {\n              color: \"#10B981\",\n              bgColor: \"#D1FAE5\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                size: 16,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1201,\n                columnNumber: 19\n              }, this),\n              title: \"Location Verified\",\n              message: `You're ${distance || 0}m from the question location`\n            };\n          case \"too_far\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 19\n              }, this),\n              title: \"Too Far Away\",\n              message: `You're ${distance || 0}m away (max 200m allowed)`\n            };\n          case \"error\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1265,\n                columnNumber: 19\n              }, this),\n              title: \"Location Error\",\n              message: \"Could not verify your location\"\n            };\n          default:\n            return {\n              color: \"#6B7280\",\n              bgColor: \"#F3F4F6\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1297,\n                columnNumber: 19\n              }, this),\n              title: \"Unknown Status\",\n              message: \"Please try again\"\n            };\n        }\n      };\n      const config = getStatusConfig();\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: config.bgColor,\n          borderRadius: 12,\n          padding: 16,\n          marginBottom: 24,\n          borderWidth: 1,\n          borderColor: config.color + \"40\"\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 8\n          },\n          children: [config.icon, /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontWeight: \"600\",\n              color: config.color,\n              marginLeft: 8\n            },\n            children: config.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1401,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1369,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: {\n            fontSize: 14,\n            color: config.color,\n            lineHeight: 20\n          },\n          children: config.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1445,\n          columnNumber: 9\n        }, this), (locationStatus === \"too_far\" || locationStatus === \"error\") && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            marginTop: 12\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: verifyLocation,\n            disabled: gettingLocation,\n            style: {\n              backgroundColor: config.color,\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              opacity: gettingLocation ? 0.6 : 1\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: gettingLocation ? \"Checking\" : \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1509,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => {\n              console.log(\"Toggle testing mode:\", {\n                before: testingMode,\n                after: !testingMode\n              });\n              setTestingMode(!testingMode);\n            },\n            style: {\n              backgroundColor: testingMode ? \"#10B981\" : \"#6B7280\",\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              marginLeft: 12\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: testingMode ? \"Testing ON\" : \"Enable Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1589,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1461,\n          columnNumber: 11\n        }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#FEF3C7\",\n            borderRadius: 8,\n            padding: 12,\n            marginTop: 12,\n            borderWidth: 1,\n            borderColor: \"#F59E0B\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 14,\n              fontWeight: \"600\",\n              color: \"#D97706\",\n              marginBottom: 4\n            },\n            children: \"Testing Mode Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 13,\n              color: \"#D97706\",\n              lineHeight: 18\n            },\n            children: \"Location verification bypassed for testing. You can now use the camera regardless of your location.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1697,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1617,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1329,\n        columnNumber: 7\n      }, this);\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: \"#F9FAFB\"\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoStatusBar.StatusBar, {\n        style: \"dark\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1741,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: \"#fff\",\n          paddingTop: insets.top + 8,\n          paddingHorizontal: 20,\n          paddingBottom: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: \"#E5E7EB\",\n          zIndex: 1000\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => _expoRouter.router.back(),\n            style: {\n              marginRight: 16\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.ArrowLeft, {\n              size: 24,\n              color: \"#111827\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1837,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1821,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 20,\n              fontWeight: \"bold\",\n              color: \"#111827\",\n              flex: 1\n            },\n            children: \"Respond to Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1845,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 10,\n              color: \"#EF4444\"\n            },\n            children: `DEBUG: testing=${testingMode ? \"ON\" : \"OFF\"}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1889,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1793,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#F0F9FF\",\n            borderRadius: 12,\n            padding: 16,\n            borderLeftWidth: 4,\n            borderLeftColor: \"#3B82F6\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              color: \"#1E40AF\",\n              fontWeight: \"500\",\n              marginBottom: 12\n            },\n            children: question.question\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1945,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MapPin, {\n              size: 14,\n              color: \"#6B7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2013,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#6B7280\",\n                marginLeft: 6,\n                flex: 1\n              },\n              children: question.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2017,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1985,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.DollarSign, {\n                size: 14,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2073,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#059669\",\n                  fontWeight: \"600\",\n                  marginLeft: 2\n                },\n                children: `$${question.reward.toFixed(2)} reward`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2077,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2069,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Clock, {\n                size: 14,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2125,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 12,\n                  color: \"#6B7280\",\n                  marginLeft: 4\n                },\n                children: question.postedAt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2129,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2121,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2041,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1909,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1749,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        behavior: \"padding\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingBottom: insets.bottom + 100\n          },\n          showsVerticalScrollIndicator: false,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              padding: 20\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(LocationStatus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2189,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: capturedPhotoUri ? '#10B981' : locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    marginRight: 12\n                  },\n                  children: capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2243,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 12,\n                      fontWeight: '600'\n                    },\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2247,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2211,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Capture Privacy-Safe Photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2253,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2197,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: '#6B7280',\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Take a privacy-safe photo using our real-time face blurring camera. Faces are automatically blurred before the photo is captured.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2273,\n                columnNumber: 15\n              }, this), capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: '#fff',\n                  borderRadius: 16,\n                  overflow: 'hidden',\n                  borderWidth: 1,\n                  borderColor: '#E5E7EB',\n                  ..._Platform.default.select({\n                    ios: {\n                      shadowColor: '#000',\n                      shadowOffset: {\n                        width: 0,\n                        height: 2\n                      },\n                      shadowOpacity: 0.05,\n                      shadowRadius: 8\n                    },\n                    android: {\n                      elevation: 3\n                    },\n                    web: {\n                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\n                    }\n                  })\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Image.default, {\n                    source: {\n                      uri: capturedPhotoUri\n                    },\n                    style: {\n                      width: '100%',\n                      height: 400,\n                      // Increased from 240 to 400 for better visibility\n                      backgroundColor: '#F3F4F6',\n                      borderRadius: 12 // Add slight rounding for better appearance\n                    },\n                    resizeMode: \"contain\" // Changed from \"cover\" to \"contain\" to show full image\n                    ,\n\n                    onError: e => {\n                      console.error('[IMAGE ERROR] Failed to load image:', {\n                        error: e.nativeEvent?.error,\n                        uri: capturedPhotoUri,\n                        cameraResult: JSON.stringify(cameraResult, null, 2)\n                      });\n                    },\n                    onLoad: () => {\n                      console.log('[IMAGE SUCCESS] Image loaded successfully:', capturedPhotoUri);\n                    },\n                    onLoadStart: () => {\n                      console.log('[IMAGE START] Loading image from:', capturedPhotoUri);\n                      console.log('🔍 DEBUGGING: Image source details:', {\n                        capturedPhotoUri,\n                        isBlob: capturedPhotoUri?.startsWith('blob:'),\n                        isDataUri: capturedPhotoUri?.startsWith('data:'),\n                        length: capturedPhotoUri?.length\n                      });\n                    },\n                    onLoadEnd: () => {\n                      console.log('[IMAGE END] Image loading finished');\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      position: 'absolute',\n                      top: 12,\n                      right: 12,\n                      backgroundColor: 'rgba(16, 185, 129, 0.95)',\n                      paddingHorizontal: 12,\n                      paddingVertical: 6,\n                      borderRadius: 20,\n                      flexDirection: 'row',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                      size: 14,\n                      color: \"#fff\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2422,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 12,\n                        fontWeight: '600',\n                        marginLeft: 4\n                      },\n                      children: \"Privacy Protected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2424,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2396,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2341,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    padding: 16\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'center',\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        width: 32,\n                        height: 32,\n                        borderRadius: 16,\n                        backgroundColor: '#D1FAE5',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 20,\n                        color: \"#10B981\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2470,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2450,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        marginLeft: 12,\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 18,\n                          fontWeight: '700',\n                          color: '#111827'\n                        },\n                        children: \"Photo Captured Successfully\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2476,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 13,\n                          color: '#6B7280',\n                          marginTop: 2\n                        },\n                        children: \"Ready to submit with your response\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2482,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2474,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2436,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      backgroundColor: '#F0FDF4',\n                      borderRadius: 12,\n                      padding: 12,\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        fontSize: 12,\n                        fontWeight: '600',\n                        color: '#15803D',\n                        marginBottom: 8,\n                        textTransform: 'uppercase',\n                        letterSpacing: 0.5\n                      },\n                      children: \"Protection Applied\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2508,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2534,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: \"Faces automatically blurred in real-time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2536,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2532,\n                      columnNumber: 23\n                    }, this), cameraResult?.challengeCode && cameraResult.challengeCode.trim() && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2548,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Challenge verified: ${cameraResult.challengeCode || 'N/A'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2550,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2546,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2562,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Location confirmed: ${distance || 0}m away`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2564,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2560,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2492,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row'\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                      onPress: () => {\n                        setCameraResult(null);\n                        setCapturedPhotoUri(null);\n                        handleStartCamera();\n                      },\n                      style: {\n                        flex: 1,\n                        backgroundColor: '#fff',\n                        borderWidth: 1,\n                        borderColor: '#D1D5DB',\n                        borderRadius: 12,\n                        paddingVertical: 12,\n                        paddingHorizontal: 16,\n                        flexDirection: 'row',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                        size: 18,\n                        color: \"#6B7280\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2614,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 15,\n                          color: '#6B7280',\n                          fontWeight: '600',\n                          marginLeft: 8\n                        },\n                        children: \"Retake Photo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2616,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2576,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2574,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2434,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2295,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: handleStartCamera,\n                disabled: locationStatus !== 'verified' && !testingMode,\n                style: {\n                  backgroundColor: locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                  borderRadius: 12,\n                  padding: 16,\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                  size: 20,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2658,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 16,\n                    fontWeight: '600',\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: locationStatus === 'verified' || testingMode ? 'Start Camera' : 'Verify Location First'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2660,\n                  columnNumber: 19\n                }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 10,\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: \"TEST\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2686,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2632,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2195,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: \"row\",\n                  alignItems: \"center\",\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: response.trim() ? \"#10B981\" : cameraResult ? \"#3B82F6\" : \"#9CA3AF\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    marginRight: 12\n                  },\n                  children: response.trim() ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2721,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: \"#fff\",\n                      fontSize: 12,\n                      fontWeight: \"600\"\n                    },\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2723,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2705,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: \"600\",\n                    color: \"#111827\"\n                  },\n                  children: \"Add Text Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2731,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2698,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#6B7280\",\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Describe what your photo shows. Be specific and helpful to answer the question completely.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2742,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: \"#fff\",\n                  borderRadius: 12,\n                  borderWidth: 1,\n                  borderColor: \"#E5E7EB\",\n                  padding: 4\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    alignItems: \"flex-start\",\n                    padding: 12\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageCircle, {\n                    size: 20,\n                    color: \"#6B7280\",\n                    style: {\n                      marginTop: 2,\n                      marginRight: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2769,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextInput.default, {\n                    style: {\n                      flex: 1,\n                      fontSize: 16,\n                      color: \"#111827\",\n                      minHeight: 100,\n                      textAlignVertical: \"top\"\n                    },\n                    placeholder: \"Describe what you can see that answers their question\",\n                    placeholderTextColor: \"#9CA3AF\",\n                    value: response,\n                    onChangeText: setResponse,\n                    multiline: true,\n                    maxLength: 500\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2774,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2762,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    paddingHorizontal: 16,\n                    paddingBottom: 8\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#6B7280\"\n                    },\n                    children: \"Be specific and helpful\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2800,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#9CA3AF\"\n                    },\n                    children: `${response.length}/500`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2803,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2791,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2753,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2697,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: \"#EBF5FF\",\n                borderRadius: 12,\n                padding: 16,\n                marginBottom: 24\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontWeight: \"600\",\n                  color: \"#1E40AF\",\n                  marginBottom: 8\n                },\n                children: \"Privacy Protection Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2845,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 13,\n                  color: \"#1E40AF\",\n                  lineHeight: 18\n                },\n                children: `Your photo is processed on-device with real-time face blurring. All faces are automatically blurred before the photo is captured, ensuring complete privacy protection.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2885,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2813,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2181,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2161,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0,\n            backgroundColor: \"#fff\",\n            borderTopWidth: 1,\n            borderTopColor: \"#E5E7EB\",\n            padding: 20,\n            paddingBottom: insets.bottom + 20\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: submitResponse,\n            disabled: submitting || !cameraResult || !response.trim(),\n            style: {\n              backgroundColor: submitting || !cameraResult || !response.trim() ? \"#9CA3AF\" : \"#10B981\",\n              borderRadius: 12,\n              padding: 16,\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: submitting ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  width: 16,\n                  height: 16,\n                  borderRadius: 8,\n                  borderWidth: 2,\n                  borderColor: \"#fff\",\n                  borderTopColor: \"transparent\",\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3057,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\"\n                },\n                children: \"Submitting Response\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3101,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Send, {\n                size: 20,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\",\n                  marginLeft: 8\n                },\n                children: `Submit Response ($${question.reward.toFixed(2)})`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2989,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2937,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2157,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: showCamera,\n        animationType: \"slide\",\n        presentationStyle: \"fullScreen\",\n        children: _Platform.default.OS === 'web' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraWeb.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3225,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraUnified.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3253,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 3201,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1737,\n      columnNumber: 5\n    }, this);\n  }\n  _s(RespondScreen, \"gw2Imk4A9BoQq1d4k9rSymf1PEo=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _expoRouter.useLocalSearchParams];\n  });\n  _c = RespondScreen;\n  var _c;\n  $RefreshReg$(_c, \"RespondScreen\");\n});", "lineCount": 1578, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 73], [8, 6, 1, 73, "_View"], [8, 11, 1, 73], [8, 14, 1, 73, "_interopRequireDefault"], [8, 36, 1, 73], [8, 37, 1, 73, "require"], [8, 44, 1, 73], [8, 45, 1, 73, "_dependencyMap"], [8, 59, 1, 73], [9, 2, 1, 73], [9, 6, 1, 73, "_Text"], [9, 11, 1, 73], [9, 14, 1, 73, "_interopRequireDefault"], [9, 36, 1, 73], [9, 37, 1, 73, "require"], [9, 44, 1, 73], [9, 45, 1, 73, "_dependencyMap"], [9, 59, 1, 73], [10, 2, 1, 73], [10, 6, 1, 73, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 17, 1, 73], [10, 20, 1, 73, "_interopRequireDefault"], [10, 42, 1, 73], [10, 43, 1, 73, "require"], [10, 50, 1, 73], [10, 51, 1, 73, "_dependencyMap"], [10, 65, 1, 73], [11, 2, 1, 73], [11, 6, 1, 73, "_TextInput"], [11, 16, 1, 73], [11, 19, 1, 73, "_interopRequireDefault"], [11, 41, 1, 73], [11, 42, 1, 73, "require"], [11, 49, 1, 73], [11, 50, 1, 73, "_dependencyMap"], [11, 64, 1, 73], [12, 2, 1, 73], [12, 6, 1, 73, "_TouchableOpacity"], [12, 23, 1, 73], [12, 26, 1, 73, "_interopRequireDefault"], [12, 48, 1, 73], [12, 49, 1, 73, "require"], [12, 56, 1, 73], [12, 57, 1, 73, "_dependencyMap"], [12, 71, 1, 73], [13, 2, 1, 73], [13, 6, 1, 73, "_<PERSON><PERSON>"], [13, 12, 1, 73], [13, 15, 1, 73, "_interopRequireDefault"], [13, 37, 1, 73], [13, 38, 1, 73, "require"], [13, 45, 1, 73], [13, 46, 1, 73, "_dependencyMap"], [13, 60, 1, 73], [14, 2, 1, 73], [14, 6, 1, 73, "_Modal"], [14, 12, 1, 73], [14, 15, 1, 73, "_interopRequireDefault"], [14, 37, 1, 73], [14, 38, 1, 73, "require"], [14, 45, 1, 73], [14, 46, 1, 73, "_dependencyMap"], [14, 60, 1, 73], [15, 2, 1, 73], [15, 6, 1, 73, "_Platform"], [15, 15, 1, 73], [15, 18, 1, 73, "_interopRequireDefault"], [15, 40, 1, 73], [15, 41, 1, 73, "require"], [15, 48, 1, 73], [15, 49, 1, 73, "_dependencyMap"], [15, 63, 1, 73], [16, 2, 1, 73], [16, 6, 1, 73, "_Image"], [16, 12, 1, 73], [16, 15, 1, 73, "_interopRequireDefault"], [16, 37, 1, 73], [16, 38, 1, 73, "require"], [16, 45, 1, 73], [16, 46, 1, 73, "_dependencyMap"], [16, 60, 1, 73], [17, 2, 1, 73], [17, 6, 1, 73, "_ActivityIndicator"], [17, 24, 1, 73], [17, 27, 1, 73, "_interopRequireDefault"], [17, 49, 1, 73], [17, 50, 1, 73, "require"], [17, 57, 1, 73], [17, 58, 1, 73, "_dependencyMap"], [17, 72, 1, 73], [18, 2, 53, 0], [18, 6, 53, 0, "_reactNativeSafeAreaContext"], [18, 33, 53, 0], [18, 36, 53, 0, "require"], [18, 43, 53, 0], [18, 44, 53, 0, "_dependencyMap"], [18, 58, 53, 0], [19, 2, 57, 0], [19, 6, 57, 0, "_expoStatusBar"], [19, 20, 57, 0], [19, 23, 57, 0, "require"], [19, 30, 57, 0], [19, 31, 57, 0, "_dependencyMap"], [19, 45, 57, 0], [20, 2, 61, 0], [20, 6, 61, 0, "_expoRouter"], [20, 17, 61, 0], [20, 20, 61, 0, "require"], [20, 27, 61, 0], [20, 28, 61, 0, "_dependencyMap"], [20, 42, 61, 0], [21, 2, 65, 0], [21, 6, 65, 0, "_lucideReactNative"], [21, 24, 65, 0], [21, 27, 65, 0, "require"], [21, 34, 65, 0], [21, 35, 65, 0, "_dependencyMap"], [21, 49, 65, 0], [22, 2, 117, 0], [22, 6, 117, 0, "Location"], [22, 14, 117, 0], [22, 17, 117, 0, "_interopRequireWildcard"], [22, 40, 117, 0], [22, 41, 117, 0, "require"], [22, 48, 117, 0], [22, 49, 117, 0, "_dependencyMap"], [22, 63, 117, 0], [23, 2, 121, 0], [23, 6, 121, 0, "_EchoCameraUnified"], [23, 24, 121, 0], [23, 27, 121, 0, "_interopRequireDefault"], [23, 49, 121, 0], [23, 50, 121, 0, "require"], [23, 57, 121, 0], [23, 58, 121, 0, "_dependencyMap"], [23, 72, 121, 0], [24, 2, 125, 0], [24, 6, 125, 0, "_EchoCameraWeb"], [24, 20, 125, 0], [24, 23, 125, 0, "_interopRequireDefault"], [24, 45, 125, 0], [24, 46, 125, 0, "require"], [24, 53, 125, 0], [24, 54, 125, 0, "_dependencyMap"], [24, 68, 125, 0], [25, 2, 129, 0], [25, 6, 129, 0, "_KeyboardAvoidingAnimatedView"], [25, 35, 129, 0], [25, 38, 129, 0, "_interopRequireDefault"], [25, 60, 129, 0], [25, 61, 129, 0, "require"], [25, 68, 129, 0], [25, 69, 129, 0, "_dependencyMap"], [25, 83, 129, 0], [26, 2, 129, 85], [26, 6, 129, 85, "_jsxDevRuntime"], [26, 20, 129, 85], [26, 23, 129, 85, "require"], [26, 30, 129, 85], [26, 31, 129, 85, "_dependencyMap"], [26, 45, 129, 85], [27, 2, 129, 85], [27, 6, 129, 85, "_jsxFileName"], [27, 18, 129, 85], [28, 4, 129, 85, "_s"], [28, 6, 129, 85], [28, 9, 129, 85, "$RefreshSig$"], [28, 21, 129, 85], [29, 2, 129, 85], [29, 11, 129, 85, "_interopRequireWildcard"], [29, 35, 129, 85, "e"], [29, 36, 129, 85], [29, 38, 129, 85, "t"], [29, 39, 129, 85], [29, 68, 129, 85, "WeakMap"], [29, 75, 129, 85], [29, 81, 129, 85, "r"], [29, 82, 129, 85], [29, 89, 129, 85, "WeakMap"], [29, 96, 129, 85], [29, 100, 129, 85, "n"], [29, 101, 129, 85], [29, 108, 129, 85, "WeakMap"], [29, 115, 129, 85], [29, 127, 129, 85, "_interopRequireWildcard"], [29, 150, 129, 85], [29, 162, 129, 85, "_interopRequireWildcard"], [29, 163, 129, 85, "e"], [29, 164, 129, 85], [29, 166, 129, 85, "t"], [29, 167, 129, 85], [29, 176, 129, 85, "t"], [29, 177, 129, 85], [29, 181, 129, 85, "e"], [29, 182, 129, 85], [29, 186, 129, 85, "e"], [29, 187, 129, 85], [29, 188, 129, 85, "__esModule"], [29, 198, 129, 85], [29, 207, 129, 85, "e"], [29, 208, 129, 85], [29, 214, 129, 85, "o"], [29, 215, 129, 85], [29, 217, 129, 85, "i"], [29, 218, 129, 85], [29, 220, 129, 85, "f"], [29, 221, 129, 85], [29, 226, 129, 85, "__proto__"], [29, 235, 129, 85], [29, 243, 129, 85, "default"], [29, 250, 129, 85], [29, 252, 129, 85, "e"], [29, 253, 129, 85], [29, 270, 129, 85, "e"], [29, 271, 129, 85], [29, 294, 129, 85, "e"], [29, 295, 129, 85], [29, 320, 129, 85, "e"], [29, 321, 129, 85], [29, 330, 129, 85, "f"], [29, 331, 129, 85], [29, 337, 129, 85, "o"], [29, 338, 129, 85], [29, 341, 129, 85, "t"], [29, 342, 129, 85], [29, 345, 129, 85, "n"], [29, 346, 129, 85], [29, 349, 129, 85, "r"], [29, 350, 129, 85], [29, 358, 129, 85, "o"], [29, 359, 129, 85], [29, 360, 129, 85, "has"], [29, 363, 129, 85], [29, 364, 129, 85, "e"], [29, 365, 129, 85], [29, 375, 129, 85, "o"], [29, 376, 129, 85], [29, 377, 129, 85, "get"], [29, 380, 129, 85], [29, 381, 129, 85, "e"], [29, 382, 129, 85], [29, 385, 129, 85, "o"], [29, 386, 129, 85], [29, 387, 129, 85, "set"], [29, 390, 129, 85], [29, 391, 129, 85, "e"], [29, 392, 129, 85], [29, 394, 129, 85, "f"], [29, 395, 129, 85], [29, 411, 129, 85, "t"], [29, 412, 129, 85], [29, 416, 129, 85, "e"], [29, 417, 129, 85], [29, 433, 129, 85, "t"], [29, 434, 129, 85], [29, 441, 129, 85, "hasOwnProperty"], [29, 455, 129, 85], [29, 456, 129, 85, "call"], [29, 460, 129, 85], [29, 461, 129, 85, "e"], [29, 462, 129, 85], [29, 464, 129, 85, "t"], [29, 465, 129, 85], [29, 472, 129, 85, "i"], [29, 473, 129, 85], [29, 477, 129, 85, "o"], [29, 478, 129, 85], [29, 481, 129, 85, "Object"], [29, 487, 129, 85], [29, 488, 129, 85, "defineProperty"], [29, 502, 129, 85], [29, 507, 129, 85, "Object"], [29, 513, 129, 85], [29, 514, 129, 85, "getOwnPropertyDescriptor"], [29, 538, 129, 85], [29, 539, 129, 85, "e"], [29, 540, 129, 85], [29, 542, 129, 85, "t"], [29, 543, 129, 85], [29, 550, 129, 85, "i"], [29, 551, 129, 85], [29, 552, 129, 85, "get"], [29, 555, 129, 85], [29, 559, 129, 85, "i"], [29, 560, 129, 85], [29, 561, 129, 85, "set"], [29, 564, 129, 85], [29, 568, 129, 85, "o"], [29, 569, 129, 85], [29, 570, 129, 85, "f"], [29, 571, 129, 85], [29, 573, 129, 85, "t"], [29, 574, 129, 85], [29, 576, 129, 85, "i"], [29, 577, 129, 85], [29, 581, 129, 85, "f"], [29, 582, 129, 85], [29, 583, 129, 85, "t"], [29, 584, 129, 85], [29, 588, 129, 85, "e"], [29, 589, 129, 85], [29, 590, 129, 85, "t"], [29, 591, 129, 85], [29, 602, 129, 85, "f"], [29, 603, 129, 85], [29, 608, 129, 85, "e"], [29, 609, 129, 85], [29, 611, 129, 85, "t"], [29, 612, 129, 85], [30, 2, 133, 15], [30, 11, 133, 24, "RespondScreen"], [30, 24, 133, 37, "RespondScreen"], [30, 25, 133, 37], [30, 27, 133, 40], [31, 4, 133, 40, "_s"], [31, 6, 133, 40], [32, 4, 137, 2], [32, 10, 137, 8, "insets"], [32, 16, 137, 14], [32, 19, 137, 17], [32, 23, 137, 17, "useSafeAreaInsets"], [32, 68, 137, 34], [32, 70, 137, 35], [32, 71, 137, 36], [33, 4, 141, 2], [33, 10, 141, 8], [34, 6, 141, 10, "id"], [35, 4, 141, 13], [35, 5, 141, 14], [35, 8, 141, 17], [35, 12, 141, 17, "useLocalSearchParams"], [35, 44, 141, 37], [35, 46, 141, 38], [35, 47, 141, 39], [36, 4, 145, 2], [36, 10, 145, 8], [36, 11, 145, 9, "response"], [36, 19, 145, 17], [36, 21, 145, 19, "setResponse"], [36, 32, 145, 30], [36, 33, 145, 31], [36, 36, 145, 34], [36, 40, 145, 34, "useState"], [36, 55, 145, 42], [36, 57, 145, 43], [36, 59, 145, 45], [36, 60, 145, 46], [37, 4, 149, 2], [37, 10, 149, 8], [37, 11, 149, 9, "showCamera"], [37, 21, 149, 19], [37, 23, 149, 21, "setShowCamera"], [37, 36, 149, 34], [37, 37, 149, 35], [37, 40, 149, 38], [37, 44, 149, 38, "useState"], [37, 59, 149, 46], [37, 61, 149, 47], [37, 66, 149, 52], [37, 67, 149, 53], [38, 4, 153, 2], [38, 10, 153, 8], [38, 11, 153, 9, "cameraResult"], [38, 23, 153, 21], [38, 25, 153, 23, "setCameraResult"], [38, 40, 153, 38], [38, 41, 153, 39], [38, 44, 153, 42], [38, 48, 153, 42, "useState"], [38, 63, 153, 50], [38, 65, 153, 51], [38, 69, 153, 55], [38, 70, 153, 56], [39, 4, 157, 2], [39, 10, 157, 8], [39, 11, 157, 9, "submitting"], [39, 21, 157, 19], [39, 23, 157, 21, "setSubmitting"], [39, 36, 157, 34], [39, 37, 157, 35], [39, 40, 157, 38], [39, 44, 157, 38, "useState"], [39, 59, 157, 46], [39, 61, 157, 47], [39, 66, 157, 52], [39, 67, 157, 53], [40, 4, 161, 2], [40, 10, 161, 8], [40, 11, 161, 9, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [40, 27, 161, 25], [40, 29, 161, 27, "setCapturedPhotoUri"], [40, 48, 161, 46], [40, 49, 161, 47], [40, 52, 161, 50], [40, 56, 161, 50, "useState"], [40, 71, 161, 58], [40, 73, 161, 59], [40, 77, 161, 63], [40, 78, 161, 64], [41, 4, 165, 2], [41, 10, 165, 8, "defaultTestingMode"], [41, 28, 165, 26], [41, 31, 165, 29], [41, 35, 165, 29, "useMemo"], [41, 49, 165, 36], [41, 51, 165, 37], [41, 57, 165, 43], [42, 6, 169, 4], [42, 10, 169, 8, "Platform"], [42, 27, 169, 16], [42, 28, 169, 17, "OS"], [42, 30, 169, 19], [42, 35, 169, 24], [42, 40, 169, 29], [42, 44, 169, 33], [42, 51, 169, 40, "window"], [42, 57, 169, 46], [42, 62, 169, 51], [42, 73, 169, 62], [42, 75, 169, 64], [43, 8, 173, 6], [43, 15, 173, 13], [43, 20, 173, 18], [44, 6, 177, 4], [45, 6, 181, 4], [45, 12, 181, 10], [46, 8, 181, 12, "protocol"], [46, 16, 181, 20], [47, 8, 181, 22, "hostname"], [48, 6, 181, 31], [48, 7, 181, 32], [48, 10, 181, 35, "window"], [48, 16, 181, 41], [48, 17, 181, 42, "location"], [48, 25, 181, 50], [49, 6, 185, 4], [49, 12, 185, 10, "localHosts"], [49, 22, 185, 20], [49, 25, 185, 23], [49, 26, 185, 24], [49, 37, 185, 35], [49, 39, 185, 37], [49, 50, 185, 48], [49, 52, 185, 50], [49, 57, 185, 55], [49, 58, 185, 56], [50, 6, 189, 4], [50, 13, 189, 11, "protocol"], [50, 21, 189, 19], [50, 26, 189, 24], [50, 34, 189, 32], [50, 38, 189, 36, "localHosts"], [50, 48, 189, 46], [50, 49, 189, 47, "includes"], [50, 57, 189, 55], [50, 58, 189, 56, "hostname"], [50, 66, 189, 64], [50, 67, 189, 65], [51, 4, 193, 2], [51, 5, 193, 3], [51, 7, 193, 5], [51, 9, 193, 7], [51, 10, 193, 8], [52, 4, 197, 2], [52, 10, 197, 8], [52, 11, 197, 9, "testingMode"], [52, 22, 197, 20], [52, 24, 197, 22, "setTestingMode"], [52, 38, 197, 36], [52, 39, 197, 37], [52, 42, 197, 40], [52, 46, 197, 40, "useState"], [52, 61, 197, 48], [52, 63, 197, 49, "defaultTestingMode"], [52, 81, 197, 67], [52, 82, 197, 68], [54, 4, 201, 2], [56, 4, 205, 2], [56, 10, 205, 8], [56, 11, 205, 9, "locationStatus"], [56, 25, 205, 23], [56, 27, 205, 25, "setLocationStatus"], [56, 44, 205, 42], [56, 45, 205, 43], [56, 48, 205, 46], [56, 52, 205, 46, "useState"], [56, 67, 205, 54], [56, 69, 205, 55], [56, 79, 205, 65], [56, 80, 205, 66], [56, 81, 205, 67], [56, 82, 205, 68], [58, 4, 209, 2], [58, 10, 209, 8], [58, 11, 209, 9, "currentLocation"], [58, 26, 209, 24], [58, 28, 209, 26, "setCurrentLocation"], [58, 46, 209, 44], [58, 47, 209, 45], [58, 50, 209, 48], [58, 54, 209, 48, "useState"], [58, 69, 209, 56], [58, 71, 209, 57], [58, 75, 209, 61], [58, 76, 209, 62], [59, 4, 213, 2], [59, 10, 213, 8], [59, 11, 213, 9, "distance"], [59, 19, 213, 17], [59, 21, 213, 19, "setDistance"], [59, 32, 213, 30], [59, 33, 213, 31], [59, 36, 213, 34], [59, 40, 213, 34, "useState"], [59, 55, 213, 42], [59, 57, 213, 43], [59, 61, 213, 47], [59, 62, 213, 48], [60, 4, 217, 2], [60, 10, 217, 8], [60, 11, 217, 9, "gettingLocation"], [60, 26, 217, 24], [60, 28, 217, 26, "setGettingLocation"], [60, 46, 217, 44], [60, 47, 217, 45], [60, 50, 217, 48], [60, 54, 217, 48, "useState"], [60, 69, 217, 56], [60, 71, 217, 57], [60, 76, 217, 62], [60, 77, 217, 63], [61, 4, 221, 2], [61, 10, 221, 8], [61, 11, 221, 9, "locationError"], [61, 24, 221, 22], [61, 26, 221, 24, "setLocationError"], [61, 42, 221, 40], [61, 43, 221, 41], [61, 46, 221, 44], [61, 50, 221, 44, "useState"], [61, 65, 221, 52], [61, 67, 221, 53], [61, 71, 221, 57], [61, 72, 221, 58], [63, 4, 225, 2], [65, 4, 229, 2], [65, 10, 229, 8, "question"], [65, 18, 229, 16], [65, 21, 229, 19], [66, 6, 233, 4, "id"], [66, 8, 233, 6], [66, 10, 233, 8, "id"], [66, 12, 233, 10], [67, 6, 237, 4, "question"], [67, 14, 237, 12], [67, 16, 241, 6], [67, 114, 241, 104], [68, 6, 245, 4, "location"], [68, 14, 245, 12], [68, 16, 245, 14], [68, 43, 245, 41], [69, 6, 249, 4, "coordinates"], [69, 17, 249, 15], [69, 19, 249, 17], [70, 8, 253, 6], [72, 8, 257, 6, "latitude"], [72, 16, 257, 14], [72, 18, 257, 16], [72, 25, 257, 23], [73, 8, 257, 25], [75, 8, 261, 6, "longitude"], [75, 17, 261, 15], [75, 19, 261, 17], [75, 20, 261, 18], [76, 6, 265, 4], [76, 7, 265, 5], [77, 6, 269, 4, "reward"], [77, 12, 269, 10], [77, 14, 269, 12], [77, 17, 269, 15], [78, 6, 273, 4, "postedAt"], [78, 14, 273, 12], [78, 16, 273, 14], [78, 29, 273, 27], [79, 6, 277, 4, "userId"], [79, 12, 277, 10], [79, 14, 277, 12], [80, 4, 281, 0], [80, 5, 281, 1], [81, 4, 285, 2], [81, 10, 285, 8, "questionLatitude"], [81, 26, 285, 24], [81, 29, 285, 27, "question"], [81, 37, 285, 35], [81, 38, 285, 36, "coordinates"], [81, 49, 285, 47], [81, 50, 285, 48, "latitude"], [81, 58, 285, 56], [82, 4, 289, 2], [82, 10, 289, 8, "questionLongitude"], [82, 27, 289, 25], [82, 30, 289, 28, "question"], [82, 38, 289, 36], [82, 39, 289, 37, "coordinates"], [82, 50, 289, 48], [82, 51, 289, 49, "longitude"], [82, 60, 289, 58], [84, 4, 293, 2], [86, 4, 297, 2], [86, 10, 297, 8, "calculateDistance"], [86, 27, 297, 25], [86, 30, 297, 28, "calculateDistance"], [86, 31, 297, 29, "lat1"], [86, 35, 297, 33], [86, 37, 297, 35, "lon1"], [86, 41, 297, 39], [86, 43, 297, 41, "lat2"], [86, 47, 297, 45], [86, 49, 297, 47, "lon2"], [86, 53, 297, 51], [86, 58, 297, 56], [87, 6, 301, 4], [87, 12, 301, 10, "R"], [87, 13, 301, 11], [87, 16, 301, 14], [87, 22, 301, 20], [87, 23, 301, 21], [87, 24, 301, 22], [89, 6, 305, 4], [89, 12, 305, 10, "lat1Rad"], [89, 19, 305, 17], [89, 22, 305, 21, "lat1"], [89, 26, 305, 25], [89, 29, 305, 28, "Math"], [89, 33, 305, 32], [89, 34, 305, 33, "PI"], [89, 36, 305, 35], [89, 39, 305, 39], [89, 42, 305, 42], [90, 6, 309, 4], [90, 12, 309, 10, "lat2Rad"], [90, 19, 309, 17], [90, 22, 309, 21, "lat2"], [90, 26, 309, 25], [90, 29, 309, 28, "Math"], [90, 33, 309, 32], [90, 34, 309, 33, "PI"], [90, 36, 309, 35], [90, 39, 309, 39], [90, 42, 309, 42], [91, 6, 313, 4], [91, 12, 313, 10, "deltaLat"], [91, 20, 313, 18], [91, 23, 313, 22], [91, 24, 313, 23, "lat2"], [91, 28, 313, 27], [91, 31, 313, 30, "lat1"], [91, 35, 313, 34], [91, 39, 313, 38, "Math"], [91, 43, 313, 42], [91, 44, 313, 43, "PI"], [91, 46, 313, 45], [91, 49, 313, 49], [91, 52, 313, 52], [92, 6, 317, 4], [92, 12, 317, 10, "deltaLon"], [92, 20, 317, 18], [92, 23, 317, 22], [92, 24, 317, 23, "lon2"], [92, 28, 317, 27], [92, 31, 317, 30, "lon1"], [92, 35, 317, 34], [92, 39, 317, 38, "Math"], [92, 43, 317, 42], [92, 44, 317, 43, "PI"], [92, 46, 317, 45], [92, 49, 317, 49], [92, 52, 317, 52], [93, 6, 321, 4], [93, 12, 321, 10, "a"], [93, 13, 321, 11], [93, 16, 325, 6, "Math"], [93, 20, 325, 10], [93, 21, 325, 11, "sin"], [93, 24, 325, 14], [93, 25, 325, 15, "deltaLat"], [93, 33, 325, 23], [93, 36, 325, 26], [93, 37, 325, 27], [93, 38, 325, 28], [93, 41, 325, 31, "Math"], [93, 45, 325, 35], [93, 46, 325, 36, "sin"], [93, 49, 325, 39], [93, 50, 325, 40, "deltaLat"], [93, 58, 325, 48], [93, 61, 325, 51], [93, 62, 325, 52], [93, 63, 325, 53], [93, 66, 329, 6, "Math"], [93, 70, 329, 10], [93, 71, 329, 11, "cos"], [93, 74, 329, 14], [93, 75, 329, 15, "lat1Rad"], [93, 82, 329, 22], [93, 83, 329, 23], [93, 86, 329, 26, "Math"], [93, 90, 329, 30], [93, 91, 329, 31, "cos"], [93, 94, 329, 34], [93, 95, 329, 35, "lat2Rad"], [93, 102, 329, 42], [93, 103, 329, 43], [93, 106, 333, 8, "Math"], [93, 110, 333, 12], [93, 111, 333, 13, "sin"], [93, 114, 333, 16], [93, 115, 333, 17, "deltaLon"], [93, 123, 333, 25], [93, 126, 333, 28], [93, 127, 333, 29], [93, 128, 333, 30], [93, 131, 333, 33, "Math"], [93, 135, 333, 37], [93, 136, 333, 38, "sin"], [93, 139, 333, 41], [93, 140, 333, 42, "deltaLon"], [93, 148, 333, 50], [93, 151, 333, 53], [93, 152, 333, 54], [93, 153, 333, 55], [94, 6, 337, 4], [94, 12, 337, 10, "c"], [94, 13, 337, 11], [94, 16, 337, 14], [94, 17, 337, 15], [94, 20, 337, 18, "Math"], [94, 24, 337, 22], [94, 25, 337, 23, "atan2"], [94, 30, 337, 28], [94, 31, 337, 29, "Math"], [94, 35, 337, 33], [94, 36, 337, 34, "sqrt"], [94, 40, 337, 38], [94, 41, 337, 39, "a"], [94, 42, 337, 40], [94, 43, 337, 41], [94, 45, 337, 43, "Math"], [94, 49, 337, 47], [94, 50, 337, 48, "sqrt"], [94, 54, 337, 52], [94, 55, 337, 53], [94, 56, 337, 54], [94, 59, 337, 57, "a"], [94, 60, 337, 58], [94, 61, 337, 59], [94, 62, 337, 60], [95, 6, 341, 4], [95, 13, 341, 11, "R"], [95, 14, 341, 12], [95, 17, 341, 15, "c"], [95, 18, 341, 16], [95, 19, 341, 17], [95, 20, 341, 18], [96, 4, 345, 2], [96, 5, 345, 3], [98, 4, 349, 2], [100, 4, 353, 2], [100, 10, 353, 8, "verifyLocation"], [100, 24, 353, 22], [100, 27, 353, 25], [100, 31, 353, 25, "useCallback"], [100, 49, 353, 36], [100, 51, 353, 37], [100, 63, 353, 49], [101, 6, 357, 4], [101, 10, 357, 8, "testingMode"], [101, 21, 357, 19], [101, 23, 357, 21], [102, 8, 361, 6, "setLocationStatus"], [102, 25, 361, 23], [102, 26, 361, 24], [102, 36, 361, 34], [102, 37, 361, 35], [103, 8, 365, 6, "setLocationError"], [103, 24, 365, 22], [103, 25, 365, 23], [103, 29, 365, 27], [103, 30, 365, 28], [104, 8, 369, 6, "setDistance"], [104, 19, 369, 17], [104, 20, 369, 18], [104, 21, 369, 19], [104, 22, 369, 20], [105, 8, 373, 6, "setCurrentLocation"], [105, 26, 373, 24], [105, 27, 373, 25], [105, 31, 373, 29], [105, 32, 373, 30], [106, 8, 377, 6, "setGettingLocation"], [106, 26, 377, 24], [106, 27, 377, 25], [106, 32, 377, 30], [106, 33, 377, 31], [107, 8, 381, 6], [108, 6, 385, 4], [109, 6, 389, 4], [109, 10, 389, 8], [110, 8, 393, 6, "setGettingLocation"], [110, 26, 393, 24], [110, 27, 393, 25], [110, 31, 393, 29], [110, 32, 393, 30], [111, 8, 397, 6, "setLocationError"], [111, 24, 397, 22], [111, 25, 397, 23], [111, 29, 397, 27], [111, 30, 397, 28], [112, 8, 401, 6, "setLocationStatus"], [112, 25, 401, 23], [112, 26, 401, 24], [112, 36, 401, 34], [112, 37, 401, 35], [114, 8, 405, 6], [116, 8, 409, 6], [116, 14, 409, 12], [117, 10, 409, 14, "status"], [118, 8, 409, 21], [118, 9, 409, 22], [118, 12, 409, 25], [118, 18, 409, 31, "Location"], [118, 26, 409, 39], [118, 27, 409, 40, "requestForegroundPermissionsAsync"], [118, 60, 409, 73], [118, 61, 409, 74], [118, 62, 409, 75], [119, 8, 413, 6], [119, 12, 413, 10, "status"], [119, 18, 413, 16], [119, 23, 413, 21], [119, 32, 413, 30], [119, 34, 413, 32], [120, 10, 417, 8], [120, 16, 417, 14, "message"], [120, 23, 417, 21], [120, 26, 421, 10, "Platform"], [120, 43, 421, 18], [120, 44, 421, 19, "OS"], [120, 46, 421, 21], [120, 51, 421, 26], [120, 56, 421, 31], [120, 59, 425, 14], [120, 164, 425, 119], [120, 167, 429, 14], [120, 233, 429, 80], [121, 10, 433, 8, "setLocationError"], [121, 26, 433, 24], [121, 27, 433, 25, "message"], [121, 34, 433, 32], [121, 35, 433, 33], [122, 10, 437, 8, "setLocationStatus"], [122, 27, 437, 25], [122, 28, 437, 26], [122, 35, 437, 33], [122, 36, 437, 34], [123, 10, 441, 8, "<PERSON><PERSON>"], [123, 24, 441, 13], [123, 25, 441, 14, "alert"], [123, 30, 441, 19], [123, 31, 441, 20], [123, 50, 441, 39], [123, 52, 441, 41, "message"], [123, 59, 441, 48], [123, 60, 441, 49], [124, 10, 445, 8], [125, 8, 449, 6], [127, 8, 453, 6], [129, 8, 457, 6], [129, 14, 457, 12, "locationData"], [129, 26, 457, 24], [129, 29, 457, 27], [129, 35, 457, 33, "Location"], [129, 43, 457, 41], [129, 44, 457, 42, "getCurrentPositionAsync"], [129, 67, 457, 65], [129, 68, 457, 66], [130, 10, 461, 8, "accuracy"], [130, 18, 461, 16], [130, 20, 461, 18, "Location"], [130, 28, 461, 26], [130, 29, 461, 27, "Accuracy"], [130, 37, 461, 35], [130, 38, 461, 36, "High"], [130, 42, 461, 40], [131, 10, 465, 8, "timeout"], [131, 17, 465, 15], [131, 19, 465, 17], [131, 24, 465, 22], [132, 10, 469, 8, "maximumAge"], [132, 20, 469, 18], [132, 22, 469, 20], [133, 8, 473, 6], [133, 9, 473, 7], [133, 10, 473, 8], [134, 8, 477, 6], [134, 14, 477, 12, "userLat"], [134, 21, 477, 19], [134, 24, 477, 22, "locationData"], [134, 36, 477, 34], [134, 37, 477, 35, "coords"], [134, 43, 477, 41], [134, 44, 477, 42, "latitude"], [134, 52, 477, 50], [135, 8, 481, 6], [135, 14, 481, 12, "userLon"], [135, 21, 481, 19], [135, 24, 481, 22, "locationData"], [135, 36, 481, 34], [135, 37, 481, 35, "coords"], [135, 43, 481, 41], [135, 44, 481, 42, "longitude"], [135, 53, 481, 51], [136, 8, 485, 6], [136, 14, 485, 12, "questionLat"], [136, 25, 485, 23], [136, 28, 485, 26, "questionLatitude"], [136, 44, 485, 42], [137, 8, 489, 6], [137, 14, 489, 12, "questionLon"], [137, 25, 489, 23], [137, 28, 489, 26, "questionLongitude"], [137, 45, 489, 43], [139, 8, 493, 6], [141, 8, 497, 6], [141, 14, 497, 12, "distanceInMeters"], [141, 30, 497, 28], [141, 33, 497, 31, "calculateDistance"], [141, 50, 497, 48], [141, 51, 501, 8, "userLat"], [141, 58, 501, 15], [141, 60, 505, 8, "userLon"], [141, 67, 505, 15], [141, 69, 509, 8, "questionLat"], [141, 80, 509, 19], [141, 82, 513, 8, "questionLon"], [141, 93, 517, 6], [141, 94, 517, 7], [142, 8, 521, 6, "setDistance"], [142, 19, 521, 17], [142, 20, 521, 18, "Math"], [142, 24, 521, 22], [142, 25, 521, 23, "round"], [142, 30, 521, 28], [142, 31, 521, 29, "distanceInMeters"], [142, 47, 521, 45], [142, 48, 521, 46], [142, 49, 521, 47], [143, 8, 525, 6, "setCurrentLocation"], [143, 26, 525, 24], [143, 27, 525, 25], [144, 10, 529, 8, "latitude"], [144, 18, 529, 16], [144, 20, 529, 18, "userLat"], [144, 27, 529, 25], [145, 10, 533, 8, "longitude"], [145, 19, 533, 17], [145, 21, 533, 19, "userLon"], [146, 8, 537, 6], [146, 9, 537, 7], [146, 10, 537, 8], [148, 8, 541, 6], [150, 8, 545, 6], [150, 14, 545, 12, "maxDistance"], [150, 25, 545, 23], [150, 28, 545, 26], [150, 31, 545, 29], [151, 8, 549, 6], [151, 12, 549, 10, "distanceInMeters"], [151, 28, 549, 26], [151, 32, 549, 30, "maxDistance"], [151, 43, 549, 41], [151, 45, 549, 43], [152, 10, 553, 8, "setLocationStatus"], [152, 27, 553, 25], [152, 28, 553, 26], [152, 38, 553, 36], [152, 39, 553, 37], [153, 8, 557, 6], [153, 9, 557, 7], [153, 15, 557, 13], [154, 10, 561, 8, "setLocationStatus"], [154, 27, 561, 25], [154, 28, 561, 26], [154, 37, 561, 35], [154, 38, 561, 36], [155, 8, 565, 6], [156, 6, 569, 4], [156, 7, 569, 5], [156, 8, 569, 6], [156, 15, 569, 13, "error"], [156, 20, 569, 18], [156, 22, 569, 20], [157, 8, 573, 6, "console"], [157, 15, 573, 13], [157, 16, 573, 14, "error"], [157, 21, 573, 19], [157, 22, 573, 20], [157, 49, 573, 47], [157, 51, 573, 49, "error"], [157, 56, 573, 54], [157, 57, 573, 55], [158, 8, 577, 6], [158, 12, 577, 10, "message"], [158, 19, 577, 17], [158, 22, 577, 20], [158, 92, 577, 90], [159, 8, 581, 6], [159, 12, 581, 10, "error"], [159, 17, 581, 15], [159, 19, 581, 17, "code"], [159, 23, 581, 21], [159, 28, 581, 26], [159, 29, 581, 27], [159, 31, 581, 29], [160, 10, 585, 8, "message"], [160, 17, 585, 15], [160, 20, 585, 18], [160, 128, 585, 126], [161, 8, 589, 6], [161, 9, 589, 7], [161, 15, 589, 13], [161, 19, 589, 17, "error"], [161, 24, 589, 22], [161, 26, 589, 24, "code"], [161, 30, 589, 28], [161, 35, 589, 33], [161, 36, 589, 34], [161, 38, 589, 36], [162, 10, 593, 8, "message"], [162, 17, 593, 15], [162, 20, 593, 18], [162, 112, 593, 110], [163, 8, 597, 6], [163, 9, 597, 7], [163, 15, 597, 13], [163, 19, 597, 17, "error"], [163, 24, 597, 22], [163, 26, 597, 24, "code"], [163, 30, 597, 28], [163, 35, 597, 33], [163, 36, 597, 34], [163, 38, 597, 36], [164, 10, 601, 8, "message"], [164, 17, 601, 15], [164, 20, 601, 18], [164, 67, 601, 65], [165, 8, 605, 6], [165, 9, 605, 7], [165, 15, 605, 13], [165, 19, 605, 17, "Platform"], [165, 36, 605, 25], [165, 37, 605, 26, "OS"], [165, 39, 605, 28], [165, 44, 605, 33], [165, 49, 605, 38], [165, 53, 605, 42], [165, 60, 605, 49, "error"], [165, 65, 605, 54], [165, 67, 605, 56, "message"], [165, 74, 605, 63], [165, 79, 605, 68], [165, 87, 605, 76], [165, 91, 605, 80, "error"], [165, 96, 605, 85], [165, 97, 605, 86, "message"], [165, 104, 605, 93], [165, 105, 605, 94, "toLowerCase"], [165, 116, 605, 105], [165, 117, 605, 106], [165, 118, 605, 107], [165, 119, 605, 108, "includes"], [165, 127, 605, 116], [165, 128, 605, 117], [165, 136, 605, 125], [165, 137, 605, 126], [165, 139, 605, 128], [166, 10, 609, 8, "message"], [166, 17, 609, 15], [166, 20, 609, 18], [166, 135, 609, 133], [167, 8, 613, 6], [168, 8, 617, 6, "setLocationError"], [168, 24, 617, 22], [168, 25, 617, 23, "message"], [168, 32, 617, 30], [168, 33, 617, 31], [169, 8, 621, 6, "setLocationStatus"], [169, 25, 621, 23], [169, 26, 621, 24], [169, 33, 621, 31], [169, 34, 621, 32], [170, 8, 625, 6, "<PERSON><PERSON>"], [170, 22, 625, 11], [170, 23, 625, 12, "alert"], [170, 28, 625, 17], [170, 29, 625, 18], [170, 45, 625, 34], [170, 47, 625, 36, "message"], [170, 54, 625, 43], [170, 55, 625, 44], [171, 6, 629, 4], [171, 7, 629, 5], [171, 16, 629, 14], [172, 8, 633, 6, "setGettingLocation"], [172, 26, 633, 24], [172, 27, 633, 25], [172, 32, 633, 30], [172, 33, 633, 31], [173, 6, 637, 4], [174, 4, 641, 2], [174, 5, 641, 3], [174, 7, 641, 5], [174, 8, 641, 6, "questionLatitude"], [174, 24, 641, 22], [174, 26, 641, 24, "questionLongitude"], [174, 43, 641, 41], [174, 45, 641, 43, "testingMode"], [174, 56, 641, 54], [174, 57, 641, 55], [174, 58, 641, 56], [176, 4, 645, 2], [178, 4, 649, 2], [178, 8, 649, 2, "useEffect"], [178, 24, 649, 11], [178, 26, 649, 12], [178, 32, 649, 18], [179, 6, 653, 4], [179, 10, 653, 8, "testingMode"], [179, 21, 653, 19], [179, 23, 653, 21], [180, 8, 657, 6, "setLocationStatus"], [180, 25, 657, 23], [180, 26, 657, 24], [180, 36, 657, 34], [180, 37, 657, 35], [181, 8, 661, 6, "setLocationError"], [181, 24, 661, 22], [181, 25, 661, 23], [181, 29, 661, 27], [181, 30, 661, 28], [182, 8, 665, 6, "setDistance"], [182, 19, 665, 17], [182, 20, 665, 18], [182, 21, 665, 19], [182, 22, 665, 20], [183, 8, 669, 6, "setCurrentLocation"], [183, 26, 669, 24], [183, 27, 669, 25], [183, 31, 669, 29], [183, 32, 669, 30], [184, 8, 673, 6, "setGettingLocation"], [184, 26, 673, 24], [184, 27, 673, 25], [184, 32, 673, 30], [184, 33, 673, 31], [185, 8, 677, 6], [186, 6, 681, 4], [187, 6, 685, 4, "verifyLocation"], [187, 20, 685, 18], [187, 21, 685, 19], [187, 22, 685, 20], [188, 4, 689, 2], [188, 5, 689, 3], [188, 7, 689, 5], [188, 8, 689, 6, "testingMode"], [188, 19, 689, 17], [188, 21, 689, 19, "verifyLocation"], [188, 35, 689, 33], [188, 36, 689, 34], [188, 37, 689, 35], [189, 4, 693, 2], [189, 10, 693, 8, "handleStartCamera"], [189, 27, 693, 25], [189, 30, 693, 28, "handleStartCamera"], [189, 31, 693, 28], [189, 36, 693, 34], [190, 6, 697, 4, "console"], [190, 13, 697, 11], [190, 14, 697, 12, "log"], [190, 17, 697, 15], [190, 18, 697, 16], [190, 42, 697, 40], [190, 44, 697, 42], [191, 8, 701, 6, "locationStatus"], [191, 22, 701, 20], [192, 8, 705, 6, "testingMode"], [192, 19, 705, 17], [193, 8, 709, 6, "disabled"], [193, 16, 709, 14], [193, 18, 709, 16, "locationStatus"], [193, 32, 709, 30], [193, 37, 709, 35], [193, 47, 709, 45], [193, 51, 709, 49], [193, 52, 709, 50, "testingMode"], [193, 63, 709, 61], [194, 8, 713, 6, "shouldEnable"], [194, 20, 713, 18], [194, 22, 713, 20, "locationStatus"], [194, 36, 713, 34], [194, 41, 713, 39], [194, 51, 713, 49], [194, 55, 713, 53, "testingMode"], [194, 66, 713, 64], [195, 8, 717, 6, "existingCameraResult"], [195, 28, 717, 26], [195, 30, 717, 28, "cameraResult"], [195, 42, 717, 40], [195, 43, 717, 42], [196, 6, 721, 4], [196, 7, 721, 5], [196, 8, 721, 6], [197, 6, 725, 4], [197, 10, 725, 8, "locationStatus"], [197, 24, 725, 22], [197, 29, 725, 27], [197, 39, 725, 37], [197, 43, 725, 41], [197, 44, 725, 42, "testingMode"], [197, 55, 725, 53], [197, 57, 725, 55], [198, 8, 729, 6, "<PERSON><PERSON>"], [198, 22, 729, 11], [198, 23, 729, 12, "alert"], [198, 28, 729, 17], [198, 29, 733, 8], [198, 48, 733, 27], [198, 50, 737, 8, "locationStatus"], [198, 64, 737, 22], [198, 69, 737, 27], [198, 78, 737, 36], [198, 81, 741, 12], [198, 92, 741, 23, "distance"], [198, 100, 741, 31], [198, 104, 741, 35], [198, 105, 741, 36], [198, 180, 741, 111], [198, 183, 745, 12], [198, 219, 749, 6], [198, 220, 749, 7], [199, 8, 753, 6], [200, 6, 757, 4], [201, 6, 761, 4, "setShowCamera"], [201, 19, 761, 17], [201, 20, 761, 18], [201, 24, 761, 22], [201, 25, 761, 23], [202, 4, 765, 2], [202, 5, 765, 3], [203, 4, 769, 2], [203, 10, 769, 8, "handleCameraComplete"], [203, 30, 769, 28], [203, 33, 769, 31], [203, 37, 769, 31, "useCallback"], [203, 55, 769, 42], [203, 57, 769, 44, "result"], [203, 63, 769, 50], [203, 67, 769, 55], [204, 6, 773, 4, "console"], [204, 13, 773, 11], [204, 14, 773, 12, "log"], [204, 17, 773, 15], [204, 18, 773, 16], [204, 43, 773, 41], [204, 45, 773, 43, "result"], [204, 51, 773, 49], [204, 52, 773, 50], [204, 53, 773, 51], [204, 54, 773, 52], [205, 6, 774, 4, "console"], [205, 13, 774, 11], [205, 14, 774, 12, "log"], [205, 17, 774, 15], [205, 18, 774, 16], [205, 59, 774, 57], [205, 61, 774, 59], [206, 8, 775, 6, "imageUrl"], [206, 16, 775, 14], [206, 18, 775, 16, "result"], [206, 24, 775, 22], [206, 25, 775, 23, "imageUrl"], [206, 33, 775, 31], [207, 8, 776, 6, "localUri"], [207, 16, 776, 14], [207, 18, 776, 16, "result"], [207, 24, 776, 22], [207, 25, 776, 23, "localUri"], [207, 33, 776, 31], [208, 8, 777, 6, "uri"], [208, 11, 777, 9], [208, 13, 777, 11, "result"], [208, 19, 777, 17], [208, 20, 777, 18, "uri"], [208, 23, 777, 21], [209, 8, 778, 6, "publicUrl"], [209, 17, 778, 15], [209, 19, 778, 17, "result"], [209, 25, 778, 23], [209, 26, 778, 24, "publicUrl"], [209, 35, 778, 33], [210, 8, 779, 6, "timestamp"], [210, 17, 779, 15], [210, 19, 779, 17, "result"], [210, 25, 779, 23], [210, 26, 779, 24, "timestamp"], [211, 6, 780, 4], [211, 7, 780, 5], [211, 8, 780, 6], [213, 6, 784, 4], [215, 6, 788, 4], [215, 10, 788, 8, "imageUri"], [215, 18, 788, 16], [215, 21, 788, 19, "result"], [215, 27, 788, 25], [215, 28, 788, 26, "imageUrl"], [215, 36, 788, 34], [215, 40, 788, 38, "result"], [215, 46, 788, 44], [215, 47, 788, 45, "localUri"], [215, 55, 788, 53], [215, 59, 788, 57, "result"], [215, 65, 788, 63], [215, 66, 788, 64, "uri"], [215, 69, 788, 67], [215, 73, 788, 71, "result"], [215, 79, 788, 77], [215, 80, 788, 78, "publicUrl"], [215, 89, 788, 87], [216, 6, 789, 4, "console"], [216, 13, 789, 11], [216, 14, 789, 12, "log"], [216, 17, 789, 15], [216, 18, 789, 16], [216, 52, 789, 50], [216, 54, 789, 52, "imageUri"], [216, 62, 789, 60], [216, 63, 789, 61], [218, 6, 793, 4], [220, 6, 797, 4], [220, 10, 797, 8, "imageUri"], [220, 18, 797, 16], [220, 22, 797, 20, "imageUri"], [220, 30, 797, 28], [220, 31, 797, 29, "startsWith"], [220, 41, 797, 39], [220, 42, 797, 40], [220, 54, 797, 52], [220, 55, 797, 53], [220, 57, 797, 55], [221, 8, 801, 6], [223, 8, 805, 6], [223, 12, 805, 10], [223, 13, 805, 11, "imageUri"], [223, 21, 805, 19], [223, 22, 805, 20, "includes"], [223, 30, 805, 28], [223, 31, 805, 29], [223, 40, 805, 38], [223, 41, 805, 39], [223, 43, 805, 41], [224, 10, 809, 8, "console"], [224, 17, 809, 15], [224, 18, 809, 16, "error"], [224, 23, 809, 21], [224, 24, 809, 22], [224, 50, 809, 48], [224, 52, 809, 50, "imageUri"], [224, 60, 809, 58], [224, 61, 809, 59, "substring"], [224, 70, 809, 68], [224, 71, 809, 69], [224, 72, 809, 70], [224, 74, 809, 72], [224, 76, 809, 74], [224, 77, 809, 75], [224, 78, 809, 76], [225, 10, 813, 8, "imageUri"], [225, 18, 813, 16], [225, 21, 813, 19], [225, 25, 813, 23], [226, 8, 817, 6], [227, 6, 821, 4], [229, 6, 825, 4], [231, 6, 829, 4], [231, 10, 829, 8], [231, 11, 829, 9, "imageUri"], [231, 19, 829, 17], [231, 23, 829, 21, "__DEV__"], [231, 30, 829, 28], [231, 32, 829, 30], [232, 8, 833, 6, "console"], [232, 15, 833, 13], [232, 16, 833, 14, "warn"], [232, 20, 833, 18], [232, 21, 833, 19], [232, 60, 833, 58], [232, 61, 833, 59], [233, 8, 837, 6, "imageUri"], [233, 16, 837, 14], [233, 19, 837, 17], [233, 90, 837, 88], [234, 6, 841, 4], [235, 6, 845, 4, "setCapturedPhotoUri"], [235, 25, 845, 23], [235, 26, 845, 24, "imageUri"], [235, 34, 845, 32], [235, 35, 845, 33], [237, 6, 849, 4], [239, 6, 853, 4], [239, 12, 853, 10, "normalizedResult"], [239, 28, 853, 26], [239, 31, 853, 29], [240, 8, 857, 6], [240, 11, 857, 9, "result"], [240, 17, 857, 15], [241, 8, 861, 6, "imageUrl"], [241, 16, 861, 14], [241, 18, 861, 16, "imageUri"], [241, 26, 861, 24], [242, 8, 865, 6, "localUri"], [242, 16, 865, 14], [242, 18, 865, 16, "imageUri"], [242, 26, 865, 24], [243, 8, 869, 6], [245, 8, 873, 6, "originalUri"], [245, 19, 873, 17], [245, 21, 873, 19, "result"], [245, 27, 873, 25], [245, 28, 873, 26, "imageUrl"], [245, 36, 873, 34], [245, 40, 873, 38, "result"], [245, 46, 873, 44], [245, 47, 873, 45, "localUri"], [245, 55, 873, 53], [245, 59, 873, 57, "result"], [245, 65, 873, 63], [245, 66, 873, 64, "uri"], [245, 69, 873, 67], [245, 73, 873, 71, "result"], [245, 79, 873, 77], [245, 80, 873, 78, "publicUrl"], [246, 6, 877, 4], [246, 7, 877, 5], [247, 6, 881, 4, "console"], [247, 13, 881, 11], [247, 14, 881, 12, "log"], [247, 17, 881, 15], [247, 18, 881, 16], [247, 47, 881, 45], [247, 49, 881, 47, "imageUri"], [247, 57, 881, 55], [247, 58, 881, 56], [248, 6, 885, 4, "console"], [248, 13, 885, 11], [248, 14, 885, 12, "log"], [248, 17, 885, 15], [248, 18, 885, 16], [248, 43, 885, 41], [248, 45, 885, 43, "normalizedResult"], [248, 61, 885, 59], [248, 62, 885, 60], [249, 6, 889, 4, "setCameraResult"], [249, 21, 889, 19], [249, 22, 889, 20, "normalizedResult"], [249, 38, 889, 36], [249, 39, 889, 37], [250, 6, 893, 4, "setShowCamera"], [250, 19, 893, 17], [250, 20, 893, 18], [250, 25, 893, 23], [250, 26, 893, 24], [252, 6, 897, 4], [253, 4, 901, 2], [253, 5, 901, 3], [253, 7, 901, 5], [253, 9, 901, 7], [253, 10, 901, 8], [254, 4, 905, 2], [254, 10, 905, 8, "handleCameraCancel"], [254, 28, 905, 26], [254, 31, 905, 29], [254, 35, 905, 29, "useCallback"], [254, 53, 905, 40], [254, 55, 905, 41], [254, 61, 905, 47], [255, 6, 909, 4, "setShowCamera"], [255, 19, 909, 17], [255, 20, 909, 18], [255, 25, 909, 23], [255, 26, 909, 24], [256, 6, 913, 4, "setCameraResult"], [256, 21, 913, 19], [256, 22, 913, 20], [256, 26, 913, 24], [256, 27, 913, 25], [257, 6, 917, 4, "setCapturedPhotoUri"], [257, 25, 917, 23], [257, 26, 917, 24], [257, 30, 917, 28], [257, 31, 917, 29], [258, 4, 921, 2], [258, 5, 921, 3], [258, 7, 921, 5], [258, 9, 921, 7], [258, 10, 921, 8], [259, 4, 925, 2], [259, 10, 925, 8, "submitResponse"], [259, 24, 925, 22], [259, 27, 925, 25], [259, 33, 925, 25, "submitResponse"], [259, 34, 925, 25], [259, 39, 925, 37], [260, 6, 929, 4], [260, 10, 929, 8, "locationStatus"], [260, 24, 929, 22], [260, 29, 929, 27], [260, 39, 929, 37], [260, 43, 929, 41], [260, 44, 929, 42, "testingMode"], [260, 55, 929, 53], [260, 57, 929, 55], [261, 8, 933, 6, "<PERSON><PERSON>"], [261, 22, 933, 11], [261, 23, 933, 12, "alert"], [261, 28, 933, 17], [261, 29, 933, 18], [261, 48, 933, 37], [261, 50, 933, 39], [261, 86, 933, 75], [261, 87, 933, 76], [262, 8, 937, 6], [263, 6, 941, 4], [264, 6, 945, 4], [264, 10, 945, 8], [264, 11, 945, 9, "cameraResult"], [264, 23, 945, 21], [264, 25, 945, 23], [265, 8, 949, 6, "<PERSON><PERSON>"], [265, 22, 949, 11], [265, 23, 949, 12, "alert"], [265, 28, 949, 17], [265, 29, 949, 18], [265, 44, 949, 33], [265, 46, 949, 35], [265, 85, 949, 74], [265, 86, 949, 75], [266, 8, 953, 6], [267, 6, 957, 4], [268, 6, 961, 4], [268, 10, 961, 8], [268, 11, 961, 9, "response"], [268, 19, 961, 17], [268, 20, 961, 18, "trim"], [268, 24, 961, 22], [268, 25, 961, 23], [268, 26, 961, 24], [268, 28, 961, 26], [269, 8, 965, 6, "<PERSON><PERSON>"], [269, 22, 965, 11], [269, 23, 965, 12, "alert"], [269, 28, 965, 17], [269, 29, 969, 8], [269, 43, 969, 22], [269, 45, 973, 8], [269, 97, 977, 6], [269, 98, 977, 7], [270, 8, 981, 6], [271, 6, 985, 4], [272, 6, 989, 4, "setSubmitting"], [272, 19, 989, 17], [272, 20, 989, 18], [272, 24, 989, 22], [272, 25, 989, 23], [273, 6, 993, 4], [273, 10, 993, 8], [274, 8, 997, 6], [276, 8, 1001, 6], [276, 14, 1001, 12, "responseData"], [276, 26, 1001, 24], [276, 29, 1001, 27], [277, 10, 1005, 8, "questionId"], [277, 20, 1005, 18], [277, 22, 1005, 20, "id"], [277, 24, 1005, 22], [278, 10, 1009, 8, "textResponse"], [278, 22, 1009, 20], [278, 24, 1009, 22, "response"], [278, 32, 1009, 30], [278, 33, 1009, 31, "trim"], [278, 37, 1009, 35], [278, 38, 1009, 36], [278, 39, 1009, 37], [279, 10, 1013, 8, "imageUrl"], [279, 18, 1013, 16], [279, 20, 1013, 18, "cameraResult"], [279, 32, 1013, 30], [279, 33, 1013, 31, "imageUrl"], [279, 41, 1013, 39], [280, 10, 1017, 8, "challengeCode"], [280, 23, 1017, 21], [280, 25, 1017, 23, "cameraResult"], [280, 37, 1017, 35], [280, 38, 1017, 36, "challengeCode"], [280, 51, 1017, 49], [281, 10, 1021, 8, "timestamp"], [281, 19, 1021, 17], [281, 21, 1021, 19, "cameraResult"], [281, 33, 1021, 31], [281, 34, 1021, 32, "timestamp"], [281, 43, 1021, 41], [282, 10, 1025, 8, "userLocation"], [282, 22, 1025, 20], [282, 24, 1025, 22, "currentLocation"], [282, 39, 1025, 37], [283, 10, 1029, 8, "distanceFromQuestion"], [283, 30, 1029, 28], [283, 32, 1029, 30, "distance"], [283, 40, 1029, 38], [284, 10, 1033, 8, "testingMode"], [284, 21, 1033, 19], [284, 23, 1033, 21, "testingMode"], [284, 34, 1033, 32], [284, 35, 1033, 34], [285, 8, 1037, 6], [285, 9, 1037, 7], [286, 8, 1041, 6, "console"], [286, 15, 1041, 13], [286, 16, 1041, 14, "log"], [286, 19, 1041, 17], [286, 20, 1041, 18], [286, 42, 1041, 40], [286, 44, 1041, 42, "responseData"], [286, 56, 1041, 54], [286, 57, 1041, 55], [288, 8, 1045, 6], [290, 8, 1049, 6], [290, 14, 1049, 12], [290, 18, 1049, 16, "Promise"], [290, 25, 1049, 23], [290, 26, 1049, 25, "resolve"], [290, 33, 1049, 32], [290, 37, 1049, 37, "setTimeout"], [290, 47, 1049, 47], [290, 48, 1049, 48, "resolve"], [290, 55, 1049, 55], [290, 57, 1049, 57], [290, 61, 1049, 61], [290, 62, 1049, 62], [290, 63, 1049, 63], [291, 8, 1053, 6, "<PERSON><PERSON>"], [291, 22, 1053, 11], [291, 23, 1053, 12, "alert"], [291, 28, 1053, 17], [291, 29, 1057, 8], [291, 50, 1057, 29], [291, 52, 1061, 8, "testingMode"], [291, 63, 1061, 19], [291, 66, 1065, 12], [291, 131, 1065, 77], [291, 134, 1069, 12], [291, 153, 1069, 31, "question"], [291, 161, 1069, 39], [291, 162, 1069, 40, "reward"], [291, 168, 1069, 46], [291, 169, 1069, 47, "toFixed"], [291, 176, 1069, 54], [291, 177, 1069, 55], [291, 178, 1069, 56], [291, 179, 1069, 57], [291, 225, 1069, 103], [291, 227, 1073, 8], [291, 228, 1077, 10], [292, 10, 1081, 12, "text"], [292, 14, 1081, 16], [292, 16, 1081, 18], [292, 20, 1081, 22], [293, 10, 1085, 12, "onPress"], [293, 17, 1085, 19], [293, 19, 1085, 21, "onPress"], [293, 20, 1085, 21], [293, 25, 1085, 27, "router"], [293, 43, 1085, 33], [293, 44, 1085, 34, "back"], [293, 48, 1085, 38], [293, 49, 1085, 39], [294, 8, 1089, 10], [294, 9, 1089, 11], [294, 10, 1097, 6], [294, 11, 1097, 7], [295, 6, 1101, 4], [295, 7, 1101, 5], [295, 8, 1101, 6], [295, 15, 1101, 13, "error"], [295, 20, 1101, 18], [295, 22, 1101, 20], [296, 8, 1105, 6, "console"], [296, 15, 1105, 13], [296, 16, 1105, 14, "error"], [296, 21, 1105, 19], [296, 22, 1105, 20], [296, 50, 1105, 48], [296, 52, 1105, 50, "error"], [296, 57, 1105, 55], [296, 58, 1105, 56], [297, 8, 1109, 6, "<PERSON><PERSON>"], [297, 22, 1109, 11], [297, 23, 1109, 12, "alert"], [297, 28, 1109, 17], [297, 29, 1109, 18], [297, 36, 1109, 25], [297, 38, 1109, 27], [297, 84, 1109, 73], [297, 85, 1109, 74], [298, 6, 1113, 4], [298, 7, 1113, 5], [298, 16, 1113, 14], [299, 8, 1117, 6, "setSubmitting"], [299, 21, 1117, 19], [299, 22, 1117, 20], [299, 27, 1117, 25], [299, 28, 1117, 26], [300, 6, 1121, 4], [301, 4, 1125, 2], [301, 5, 1125, 3], [303, 4, 1129, 2], [305, 4, 1133, 2], [305, 10, 1133, 8, "LocationStatus"], [305, 24, 1133, 22], [305, 27, 1133, 25, "LocationStatus"], [305, 28, 1133, 25], [305, 33, 1133, 31], [306, 6, 1137, 4], [306, 12, 1137, 10, "getStatusConfig"], [306, 27, 1137, 25], [306, 30, 1137, 28, "getStatusConfig"], [306, 31, 1137, 28], [306, 36, 1137, 34], [307, 8, 1141, 6], [307, 16, 1141, 14, "locationStatus"], [307, 30, 1141, 28], [308, 10, 1145, 8], [308, 15, 1145, 13], [308, 25, 1145, 23], [309, 12, 1149, 10], [309, 19, 1149, 17], [310, 14, 1153, 12, "color"], [310, 19, 1153, 17], [310, 21, 1153, 19], [310, 30, 1153, 28], [311, 14, 1157, 12, "bgColor"], [311, 21, 1157, 19], [311, 23, 1157, 21], [311, 32, 1157, 30], [312, 14, 1161, 12, "icon"], [312, 18, 1161, 16], [312, 33, 1161, 18], [312, 37, 1161, 18, "_jsxDevRuntime"], [312, 51, 1161, 18], [312, 52, 1161, 18, "jsxDEV"], [312, 58, 1161, 18], [312, 60, 1161, 19, "_lucideReactNative"], [312, 78, 1161, 19], [312, 79, 1161, 19, "Navigation"], [312, 89, 1161, 29], [313, 16, 1161, 30, "size"], [313, 20, 1161, 34], [313, 22, 1161, 36], [313, 24, 1161, 39], [314, 16, 1161, 40, "color"], [314, 21, 1161, 45], [314, 23, 1161, 46], [315, 14, 1161, 55], [316, 16, 1161, 55, "fileName"], [316, 24, 1161, 55], [316, 26, 1161, 55, "_jsxFileName"], [316, 38, 1161, 55], [317, 16, 1161, 55, "lineNumber"], [317, 26, 1161, 55], [318, 16, 1161, 55, "columnNumber"], [318, 28, 1161, 55], [319, 14, 1161, 55], [319, 21, 1161, 57], [319, 22, 1161, 58], [320, 14, 1165, 12, "title"], [320, 19, 1165, 17], [320, 21, 1165, 19], [320, 40, 1165, 38], [321, 14, 1169, 12, "message"], [321, 21, 1169, 19], [321, 23, 1169, 21, "gettingLocation"], [321, 38, 1169, 36], [321, 41, 1173, 16], [321, 72, 1173, 47], [321, 75, 1177, 16], [322, 12, 1181, 10], [322, 13, 1181, 11], [323, 10, 1185, 8], [323, 15, 1185, 13], [323, 25, 1185, 23], [324, 12, 1189, 10], [324, 19, 1189, 17], [325, 14, 1193, 12, "color"], [325, 19, 1193, 17], [325, 21, 1193, 19], [325, 30, 1193, 28], [326, 14, 1197, 12, "bgColor"], [326, 21, 1197, 19], [326, 23, 1197, 21], [326, 32, 1197, 30], [327, 14, 1201, 12, "icon"], [327, 18, 1201, 16], [327, 33, 1201, 18], [327, 37, 1201, 18, "_jsxDevRuntime"], [327, 51, 1201, 18], [327, 52, 1201, 18, "jsxDEV"], [327, 58, 1201, 18], [327, 60, 1201, 19, "_lucideReactNative"], [327, 78, 1201, 19], [327, 79, 1201, 19, "CheckCircle2"], [327, 91, 1201, 31], [328, 16, 1201, 32, "size"], [328, 20, 1201, 36], [328, 22, 1201, 38], [328, 24, 1201, 41], [329, 16, 1201, 42, "color"], [329, 21, 1201, 47], [329, 23, 1201, 48], [330, 14, 1201, 57], [331, 16, 1201, 57, "fileName"], [331, 24, 1201, 57], [331, 26, 1201, 57, "_jsxFileName"], [331, 38, 1201, 57], [332, 16, 1201, 57, "lineNumber"], [332, 26, 1201, 57], [333, 16, 1201, 57, "columnNumber"], [333, 28, 1201, 57], [334, 14, 1201, 57], [334, 21, 1201, 59], [334, 22, 1201, 60], [335, 14, 1205, 12, "title"], [335, 19, 1205, 17], [335, 21, 1205, 19], [335, 40, 1205, 38], [336, 14, 1209, 12, "message"], [336, 21, 1209, 19], [336, 23, 1209, 21], [336, 33, 1209, 31, "distance"], [336, 41, 1209, 39], [336, 45, 1209, 43], [336, 46, 1209, 44], [337, 12, 1213, 10], [337, 13, 1213, 11], [338, 10, 1217, 8], [338, 15, 1217, 13], [338, 24, 1217, 22], [339, 12, 1221, 10], [339, 19, 1221, 17], [340, 14, 1225, 12, "color"], [340, 19, 1225, 17], [340, 21, 1225, 19], [340, 30, 1225, 28], [341, 14, 1229, 12, "bgColor"], [341, 21, 1229, 19], [341, 23, 1229, 21], [341, 32, 1229, 30], [342, 14, 1233, 12, "icon"], [342, 18, 1233, 16], [342, 33, 1233, 18], [342, 37, 1233, 18, "_jsxDevRuntime"], [342, 51, 1233, 18], [342, 52, 1233, 18, "jsxDEV"], [342, 58, 1233, 18], [342, 60, 1233, 19, "_lucideReactNative"], [342, 78, 1233, 19], [342, 79, 1233, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [342, 92, 1233, 32], [343, 16, 1233, 33, "size"], [343, 20, 1233, 37], [343, 22, 1233, 39], [343, 24, 1233, 42], [344, 16, 1233, 43, "color"], [344, 21, 1233, 48], [344, 23, 1233, 49], [345, 14, 1233, 58], [346, 16, 1233, 58, "fileName"], [346, 24, 1233, 58], [346, 26, 1233, 58, "_jsxFileName"], [346, 38, 1233, 58], [347, 16, 1233, 58, "lineNumber"], [347, 26, 1233, 58], [348, 16, 1233, 58, "columnNumber"], [348, 28, 1233, 58], [349, 14, 1233, 58], [349, 21, 1233, 60], [349, 22, 1233, 61], [350, 14, 1237, 12, "title"], [350, 19, 1237, 17], [350, 21, 1237, 19], [350, 35, 1237, 33], [351, 14, 1241, 12, "message"], [351, 21, 1241, 19], [351, 23, 1241, 21], [351, 33, 1241, 31, "distance"], [351, 41, 1241, 39], [351, 45, 1241, 43], [351, 46, 1241, 44], [352, 12, 1245, 10], [352, 13, 1245, 11], [353, 10, 1249, 8], [353, 15, 1249, 13], [353, 22, 1249, 20], [354, 12, 1253, 10], [354, 19, 1253, 17], [355, 14, 1257, 12, "color"], [355, 19, 1257, 17], [355, 21, 1257, 19], [355, 30, 1257, 28], [356, 14, 1261, 12, "bgColor"], [356, 21, 1261, 19], [356, 23, 1261, 21], [356, 32, 1261, 30], [357, 14, 1265, 12, "icon"], [357, 18, 1265, 16], [357, 33, 1265, 18], [357, 37, 1265, 18, "_jsxDevRuntime"], [357, 51, 1265, 18], [357, 52, 1265, 18, "jsxDEV"], [357, 58, 1265, 18], [357, 60, 1265, 19, "_lucideReactNative"], [357, 78, 1265, 19], [357, 79, 1265, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [357, 92, 1265, 32], [358, 16, 1265, 33, "size"], [358, 20, 1265, 37], [358, 22, 1265, 39], [358, 24, 1265, 42], [359, 16, 1265, 43, "color"], [359, 21, 1265, 48], [359, 23, 1265, 49], [360, 14, 1265, 58], [361, 16, 1265, 58, "fileName"], [361, 24, 1265, 58], [361, 26, 1265, 58, "_jsxFileName"], [361, 38, 1265, 58], [362, 16, 1265, 58, "lineNumber"], [362, 26, 1265, 58], [363, 16, 1265, 58, "columnNumber"], [363, 28, 1265, 58], [364, 14, 1265, 58], [364, 21, 1265, 60], [364, 22, 1265, 61], [365, 14, 1269, 12, "title"], [365, 19, 1269, 17], [365, 21, 1269, 19], [365, 37, 1269, 35], [366, 14, 1273, 12, "message"], [366, 21, 1273, 19], [366, 23, 1273, 21], [367, 12, 1277, 10], [367, 13, 1277, 11], [368, 10, 1281, 8], [369, 12, 1285, 10], [369, 19, 1285, 17], [370, 14, 1289, 12, "color"], [370, 19, 1289, 17], [370, 21, 1289, 19], [370, 30, 1289, 28], [371, 14, 1293, 12, "bgColor"], [371, 21, 1293, 19], [371, 23, 1293, 21], [371, 32, 1293, 30], [372, 14, 1297, 12, "icon"], [372, 18, 1297, 16], [372, 33, 1297, 18], [372, 37, 1297, 18, "_jsxDevRuntime"], [372, 51, 1297, 18], [372, 52, 1297, 18, "jsxDEV"], [372, 58, 1297, 18], [372, 60, 1297, 19, "_lucideReactNative"], [372, 78, 1297, 19], [372, 79, 1297, 19, "Navigation"], [372, 89, 1297, 29], [373, 16, 1297, 30, "size"], [373, 20, 1297, 34], [373, 22, 1297, 36], [373, 24, 1297, 39], [374, 16, 1297, 40, "color"], [374, 21, 1297, 45], [374, 23, 1297, 46], [375, 14, 1297, 55], [376, 16, 1297, 55, "fileName"], [376, 24, 1297, 55], [376, 26, 1297, 55, "_jsxFileName"], [376, 38, 1297, 55], [377, 16, 1297, 55, "lineNumber"], [377, 26, 1297, 55], [378, 16, 1297, 55, "columnNumber"], [378, 28, 1297, 55], [379, 14, 1297, 55], [379, 21, 1297, 57], [379, 22, 1297, 58], [380, 14, 1301, 12, "title"], [380, 19, 1301, 17], [380, 21, 1301, 19], [380, 37, 1301, 35], [381, 14, 1305, 12, "message"], [381, 21, 1305, 19], [381, 23, 1305, 21], [382, 12, 1309, 10], [382, 13, 1309, 11], [383, 8, 1313, 6], [384, 6, 1317, 4], [384, 7, 1317, 5], [385, 6, 1321, 4], [385, 12, 1321, 10, "config"], [385, 18, 1321, 16], [385, 21, 1321, 19, "getStatusConfig"], [385, 36, 1321, 34], [385, 37, 1321, 35], [385, 38, 1321, 36], [386, 6, 1325, 4], [386, 26, 1329, 6], [386, 30, 1329, 6, "_jsxDevRuntime"], [386, 44, 1329, 6], [386, 45, 1329, 6, "jsxDEV"], [386, 51, 1329, 6], [386, 53, 1329, 7, "_View"], [386, 58, 1329, 7], [386, 59, 1329, 7, "default"], [386, 66, 1329, 11], [387, 8, 1333, 8, "style"], [387, 13, 1333, 13], [387, 15, 1333, 15], [388, 10, 1337, 10, "backgroundColor"], [388, 25, 1337, 25], [388, 27, 1337, 27, "config"], [388, 33, 1337, 33], [388, 34, 1337, 34, "bgColor"], [388, 41, 1337, 41], [389, 10, 1341, 10, "borderRadius"], [389, 22, 1341, 22], [389, 24, 1341, 24], [389, 26, 1341, 26], [390, 10, 1345, 10, "padding"], [390, 17, 1345, 17], [390, 19, 1345, 19], [390, 21, 1345, 21], [391, 10, 1349, 10, "marginBottom"], [391, 22, 1349, 22], [391, 24, 1349, 24], [391, 26, 1349, 26], [392, 10, 1353, 10, "borderWidth"], [392, 21, 1353, 21], [392, 23, 1353, 23], [392, 24, 1353, 24], [393, 10, 1357, 10, "borderColor"], [393, 21, 1357, 21], [393, 23, 1357, 23, "config"], [393, 29, 1357, 29], [393, 30, 1357, 30, "color"], [393, 35, 1357, 35], [393, 38, 1357, 38], [394, 8, 1361, 8], [394, 9, 1361, 10], [395, 8, 1361, 10, "children"], [395, 16, 1361, 10], [395, 32, 1369, 8], [395, 36, 1369, 8, "_jsxDevRuntime"], [395, 50, 1369, 8], [395, 51, 1369, 8, "jsxDEV"], [395, 57, 1369, 8], [395, 59, 1369, 9, "_View"], [395, 64, 1369, 9], [395, 65, 1369, 9, "default"], [395, 72, 1369, 13], [396, 10, 1373, 10, "style"], [396, 15, 1373, 15], [396, 17, 1373, 17], [397, 12, 1377, 12, "flexDirection"], [397, 25, 1377, 25], [397, 27, 1377, 27], [397, 32, 1377, 32], [398, 12, 1381, 12, "alignItems"], [398, 22, 1381, 22], [398, 24, 1381, 24], [398, 32, 1381, 32], [399, 12, 1385, 12, "marginBottom"], [399, 24, 1385, 24], [399, 26, 1385, 26], [400, 10, 1389, 10], [400, 11, 1389, 12], [401, 10, 1389, 12, "children"], [401, 18, 1389, 12], [401, 21, 1397, 11, "config"], [401, 27, 1397, 17], [401, 28, 1397, 18, "icon"], [401, 32, 1397, 22], [401, 47, 1401, 10], [401, 51, 1401, 10, "_jsxDevRuntime"], [401, 65, 1401, 10], [401, 66, 1401, 10, "jsxDEV"], [401, 72, 1401, 10], [401, 74, 1401, 11, "_Text"], [401, 79, 1401, 11], [401, 80, 1401, 11, "default"], [401, 87, 1401, 15], [402, 12, 1405, 12, "style"], [402, 17, 1405, 17], [402, 19, 1405, 19], [403, 14, 1409, 14, "fontSize"], [403, 22, 1409, 22], [403, 24, 1409, 24], [403, 26, 1409, 26], [404, 14, 1413, 14, "fontWeight"], [404, 24, 1413, 24], [404, 26, 1413, 26], [404, 31, 1413, 31], [405, 14, 1417, 14, "color"], [405, 19, 1417, 19], [405, 21, 1417, 21, "config"], [405, 27, 1417, 27], [405, 28, 1417, 28, "color"], [405, 33, 1417, 33], [406, 14, 1421, 14, "marginLeft"], [406, 24, 1421, 24], [406, 26, 1421, 26], [407, 12, 1425, 12], [407, 13, 1425, 14], [408, 12, 1425, 14, "children"], [408, 20, 1425, 14], [408, 22, 1433, 13, "config"], [408, 28, 1433, 19], [408, 29, 1433, 20, "title"], [409, 10, 1433, 25], [410, 12, 1433, 25, "fileName"], [410, 20, 1433, 25], [410, 22, 1433, 25, "_jsxFileName"], [410, 34, 1433, 25], [411, 12, 1433, 25, "lineNumber"], [411, 22, 1433, 25], [412, 12, 1433, 25, "columnNumber"], [412, 24, 1433, 25], [413, 10, 1433, 25], [413, 17, 1437, 16], [413, 18, 1437, 17], [414, 8, 1437, 17], [415, 10, 1437, 17, "fileName"], [415, 18, 1437, 17], [415, 20, 1437, 17, "_jsxFileName"], [415, 32, 1437, 17], [416, 10, 1437, 17, "lineNumber"], [416, 20, 1437, 17], [417, 10, 1437, 17, "columnNumber"], [417, 22, 1437, 17], [418, 8, 1437, 17], [418, 15, 1441, 14], [418, 16, 1441, 15], [418, 31, 1445, 8], [418, 35, 1445, 8, "_jsxDevRuntime"], [418, 49, 1445, 8], [418, 50, 1445, 8, "jsxDEV"], [418, 56, 1445, 8], [418, 58, 1445, 9, "_Text"], [418, 63, 1445, 9], [418, 64, 1445, 9, "default"], [418, 71, 1445, 13], [419, 10, 1445, 14, "style"], [419, 15, 1445, 19], [419, 17, 1445, 21], [420, 12, 1445, 23, "fontSize"], [420, 20, 1445, 31], [420, 22, 1445, 33], [420, 24, 1445, 35], [421, 12, 1445, 37, "color"], [421, 17, 1445, 42], [421, 19, 1445, 44, "config"], [421, 25, 1445, 50], [421, 26, 1445, 51, "color"], [421, 31, 1445, 56], [422, 12, 1445, 58, "lineHeight"], [422, 22, 1445, 68], [422, 24, 1445, 70], [423, 10, 1445, 73], [423, 11, 1445, 75], [424, 10, 1445, 75, "children"], [424, 18, 1445, 75], [424, 20, 1449, 11, "config"], [424, 26, 1449, 17], [424, 27, 1449, 18, "message"], [425, 8, 1449, 25], [426, 10, 1449, 25, "fileName"], [426, 18, 1449, 25], [426, 20, 1449, 25, "_jsxFileName"], [426, 32, 1449, 25], [427, 10, 1449, 25, "lineNumber"], [427, 20, 1449, 25], [428, 10, 1449, 25, "columnNumber"], [428, 22, 1449, 25], [429, 8, 1449, 25], [429, 15, 1453, 14], [429, 16, 1453, 15], [429, 18, 1457, 9], [429, 19, 1457, 10, "locationStatus"], [429, 33, 1457, 24], [429, 38, 1457, 29], [429, 47, 1457, 38], [429, 51, 1457, 42, "locationStatus"], [429, 65, 1457, 56], [429, 70, 1457, 61], [429, 77, 1457, 68], [429, 95, 1461, 10], [429, 99, 1461, 10, "_jsxDevRuntime"], [429, 113, 1461, 10], [429, 114, 1461, 10, "jsxDEV"], [429, 120, 1461, 10], [429, 122, 1461, 11, "_View"], [429, 127, 1461, 11], [429, 128, 1461, 11, "default"], [429, 135, 1461, 15], [430, 10, 1461, 16, "style"], [430, 15, 1461, 21], [430, 17, 1461, 23], [431, 12, 1461, 25, "flexDirection"], [431, 25, 1461, 38], [431, 27, 1461, 40], [431, 32, 1461, 45], [432, 12, 1461, 47, "marginTop"], [432, 21, 1461, 56], [432, 23, 1461, 58], [433, 10, 1461, 61], [433, 11, 1461, 63], [434, 10, 1461, 63, "children"], [434, 18, 1461, 63], [434, 34, 1465, 12], [434, 38, 1465, 12, "_jsxDevRuntime"], [434, 52, 1465, 12], [434, 53, 1465, 12, "jsxDEV"], [434, 59, 1465, 12], [434, 61, 1465, 13, "_TouchableOpacity"], [434, 78, 1465, 13], [434, 79, 1465, 13, "default"], [434, 86, 1465, 29], [435, 12, 1469, 14, "onPress"], [435, 19, 1469, 21], [435, 21, 1469, 23, "verifyLocation"], [435, 35, 1469, 38], [436, 12, 1473, 14, "disabled"], [436, 20, 1473, 22], [436, 22, 1473, 24, "gettingLocation"], [436, 37, 1473, 40], [437, 12, 1477, 14, "style"], [437, 17, 1477, 19], [437, 19, 1477, 21], [438, 14, 1481, 16, "backgroundColor"], [438, 29, 1481, 31], [438, 31, 1481, 33, "config"], [438, 37, 1481, 39], [438, 38, 1481, 40, "color"], [438, 43, 1481, 45], [439, 14, 1485, 16, "borderRadius"], [439, 26, 1485, 28], [439, 28, 1485, 30], [439, 29, 1485, 31], [440, 14, 1489, 16, "paddingVertical"], [440, 29, 1489, 31], [440, 31, 1489, 33], [440, 32, 1489, 34], [441, 14, 1493, 16, "paddingHorizontal"], [441, 31, 1493, 33], [441, 33, 1493, 35], [441, 35, 1493, 37], [442, 14, 1497, 16, "opacity"], [442, 21, 1497, 23], [442, 23, 1497, 25, "gettingLocation"], [442, 38, 1497, 40], [442, 41, 1497, 43], [442, 44, 1497, 46], [442, 47, 1497, 49], [443, 12, 1501, 14], [443, 13, 1501, 16], [444, 12, 1501, 16, "children"], [444, 20, 1501, 16], [444, 35, 1509, 14], [444, 39, 1509, 14, "_jsxDevRuntime"], [444, 53, 1509, 14], [444, 54, 1509, 14, "jsxDEV"], [444, 60, 1509, 14], [444, 62, 1509, 15, "_Text"], [444, 67, 1509, 15], [444, 68, 1509, 15, "default"], [444, 75, 1509, 19], [445, 14, 1509, 20, "style"], [445, 19, 1509, 25], [445, 21, 1509, 27], [446, 16, 1509, 29, "fontSize"], [446, 24, 1509, 37], [446, 26, 1509, 39], [446, 28, 1509, 41], [447, 16, 1509, 43, "color"], [447, 21, 1509, 48], [447, 23, 1509, 50], [447, 29, 1509, 56], [448, 16, 1509, 58, "fontWeight"], [448, 26, 1509, 68], [448, 28, 1509, 70], [449, 14, 1509, 76], [449, 15, 1509, 78], [450, 14, 1509, 78, "children"], [450, 22, 1509, 78], [450, 24, 1513, 17, "gettingLocation"], [450, 39, 1513, 32], [450, 42, 1513, 35], [450, 52, 1513, 45], [450, 55, 1513, 48], [451, 12, 1513, 59], [452, 14, 1513, 59, "fileName"], [452, 22, 1513, 59], [452, 24, 1513, 59, "_jsxFileName"], [452, 36, 1513, 59], [453, 14, 1513, 59, "lineNumber"], [453, 24, 1513, 59], [454, 14, 1513, 59, "columnNumber"], [454, 26, 1513, 59], [455, 12, 1513, 59], [455, 19, 1517, 20], [456, 10, 1517, 21], [457, 12, 1517, 21, "fileName"], [457, 20, 1517, 21], [457, 22, 1517, 21, "_jsxFileName"], [457, 34, 1517, 21], [458, 12, 1517, 21, "lineNumber"], [458, 22, 1517, 21], [459, 12, 1517, 21, "columnNumber"], [459, 24, 1517, 21], [460, 10, 1517, 21], [460, 17, 1521, 30], [460, 18, 1521, 31], [460, 33, 1525, 12], [460, 37, 1525, 12, "_jsxDevRuntime"], [460, 51, 1525, 12], [460, 52, 1525, 12, "jsxDEV"], [460, 58, 1525, 12], [460, 60, 1525, 13, "_TouchableOpacity"], [460, 77, 1525, 13], [460, 78, 1525, 13, "default"], [460, 85, 1525, 29], [461, 12, 1529, 14, "onPress"], [461, 19, 1529, 21], [461, 21, 1529, 23, "onPress"], [461, 22, 1529, 23], [461, 27, 1529, 29], [462, 14, 1533, 16, "console"], [462, 21, 1533, 23], [462, 22, 1533, 24, "log"], [462, 25, 1533, 27], [462, 26, 1533, 28], [462, 48, 1533, 50], [462, 50, 1533, 52], [463, 16, 1537, 18, "before"], [463, 22, 1537, 24], [463, 24, 1537, 26, "testingMode"], [463, 35, 1537, 37], [464, 16, 1541, 18, "after"], [464, 21, 1541, 23], [464, 23, 1541, 25], [464, 24, 1541, 26, "testingMode"], [465, 14, 1545, 16], [465, 15, 1545, 17], [465, 16, 1545, 18], [466, 14, 1549, 16, "setTestingMode"], [466, 28, 1549, 30], [466, 29, 1549, 31], [466, 30, 1549, 32, "testingMode"], [466, 41, 1549, 43], [466, 42, 1549, 44], [467, 12, 1553, 14], [467, 13, 1553, 16], [468, 12, 1557, 14, "style"], [468, 17, 1557, 19], [468, 19, 1557, 21], [469, 14, 1561, 16, "backgroundColor"], [469, 29, 1561, 31], [469, 31, 1561, 33, "testingMode"], [469, 42, 1561, 44], [469, 45, 1561, 47], [469, 54, 1561, 56], [469, 57, 1561, 59], [469, 66, 1561, 68], [470, 14, 1565, 16, "borderRadius"], [470, 26, 1565, 28], [470, 28, 1565, 30], [470, 29, 1565, 31], [471, 14, 1569, 16, "paddingVertical"], [471, 29, 1569, 31], [471, 31, 1569, 33], [471, 32, 1569, 34], [472, 14, 1573, 16, "paddingHorizontal"], [472, 31, 1573, 33], [472, 33, 1573, 35], [472, 35, 1573, 37], [473, 14, 1577, 16, "marginLeft"], [473, 24, 1577, 26], [473, 26, 1577, 28], [474, 12, 1581, 14], [474, 13, 1581, 16], [475, 12, 1581, 16, "children"], [475, 20, 1581, 16], [475, 35, 1589, 14], [475, 39, 1589, 14, "_jsxDevRuntime"], [475, 53, 1589, 14], [475, 54, 1589, 14, "jsxDEV"], [475, 60, 1589, 14], [475, 62, 1589, 15, "_Text"], [475, 67, 1589, 15], [475, 68, 1589, 15, "default"], [475, 75, 1589, 19], [476, 14, 1589, 20, "style"], [476, 19, 1589, 25], [476, 21, 1589, 27], [477, 16, 1589, 29, "fontSize"], [477, 24, 1589, 37], [477, 26, 1589, 39], [477, 28, 1589, 41], [478, 16, 1589, 43, "color"], [478, 21, 1589, 48], [478, 23, 1589, 50], [478, 29, 1589, 56], [479, 16, 1589, 58, "fontWeight"], [479, 26, 1589, 68], [479, 28, 1589, 70], [480, 14, 1589, 76], [480, 15, 1589, 78], [481, 14, 1589, 78, "children"], [481, 22, 1589, 78], [481, 24, 1593, 17, "testingMode"], [481, 35, 1593, 28], [481, 38, 1593, 31], [481, 50, 1593, 43], [481, 53, 1593, 46], [482, 12, 1593, 62], [483, 14, 1593, 62, "fileName"], [483, 22, 1593, 62], [483, 24, 1593, 62, "_jsxFileName"], [483, 36, 1593, 62], [484, 14, 1593, 62, "lineNumber"], [484, 24, 1593, 62], [485, 14, 1593, 62, "columnNumber"], [485, 26, 1593, 62], [486, 12, 1593, 62], [486, 19, 1597, 20], [487, 10, 1597, 21], [488, 12, 1597, 21, "fileName"], [488, 20, 1597, 21], [488, 22, 1597, 21, "_jsxFileName"], [488, 34, 1597, 21], [489, 12, 1597, 21, "lineNumber"], [489, 22, 1597, 21], [490, 12, 1597, 21, "columnNumber"], [490, 24, 1597, 21], [491, 10, 1597, 21], [491, 17, 1601, 30], [491, 18, 1601, 31], [492, 8, 1601, 31], [493, 10, 1601, 31, "fileName"], [493, 18, 1601, 31], [493, 20, 1601, 31, "_jsxFileName"], [493, 32, 1601, 31], [494, 10, 1601, 31, "lineNumber"], [494, 20, 1601, 31], [495, 10, 1601, 31, "columnNumber"], [495, 22, 1601, 31], [496, 8, 1601, 31], [496, 15, 1605, 16], [496, 16, 1609, 9], [496, 18, 1613, 9, "testingMode"], [496, 29, 1613, 20], [496, 46, 1617, 10], [496, 50, 1617, 10, "_jsxDevRuntime"], [496, 64, 1617, 10], [496, 65, 1617, 10, "jsxDEV"], [496, 71, 1617, 10], [496, 73, 1617, 11, "_View"], [496, 78, 1617, 11], [496, 79, 1617, 11, "default"], [496, 86, 1617, 15], [497, 10, 1621, 12, "style"], [497, 15, 1621, 17], [497, 17, 1621, 19], [498, 12, 1625, 14, "backgroundColor"], [498, 27, 1625, 29], [498, 29, 1625, 31], [498, 38, 1625, 40], [499, 12, 1629, 14, "borderRadius"], [499, 24, 1629, 26], [499, 26, 1629, 28], [499, 27, 1629, 29], [500, 12, 1633, 14, "padding"], [500, 19, 1633, 21], [500, 21, 1633, 23], [500, 23, 1633, 25], [501, 12, 1637, 14, "marginTop"], [501, 21, 1637, 23], [501, 23, 1637, 25], [501, 25, 1637, 27], [502, 12, 1641, 14, "borderWidth"], [502, 23, 1641, 25], [502, 25, 1641, 27], [502, 26, 1641, 28], [503, 12, 1645, 14, "borderColor"], [503, 23, 1645, 25], [503, 25, 1645, 27], [504, 10, 1649, 12], [504, 11, 1649, 14], [505, 10, 1649, 14, "children"], [505, 18, 1649, 14], [505, 34, 1657, 12], [505, 38, 1657, 12, "_jsxDevRuntime"], [505, 52, 1657, 12], [505, 53, 1657, 12, "jsxDEV"], [505, 59, 1657, 12], [505, 61, 1657, 13, "_Text"], [505, 66, 1657, 13], [505, 67, 1657, 13, "default"], [505, 74, 1657, 17], [506, 12, 1661, 14, "style"], [506, 17, 1661, 19], [506, 19, 1661, 21], [507, 14, 1665, 16, "fontSize"], [507, 22, 1665, 24], [507, 24, 1665, 26], [507, 26, 1665, 28], [508, 14, 1669, 16, "fontWeight"], [508, 24, 1669, 26], [508, 26, 1669, 28], [508, 31, 1669, 33], [509, 14, 1673, 16, "color"], [509, 19, 1673, 21], [509, 21, 1673, 23], [509, 30, 1673, 32], [510, 14, 1677, 16, "marginBottom"], [510, 26, 1677, 28], [510, 28, 1677, 30], [511, 12, 1681, 14], [511, 13, 1681, 16], [512, 12, 1681, 16, "children"], [512, 20, 1681, 16], [512, 22, 1685, 13], [513, 10, 1693, 12], [514, 12, 1693, 12, "fileName"], [514, 20, 1693, 12], [514, 22, 1693, 12, "_jsxFileName"], [514, 34, 1693, 12], [515, 12, 1693, 12, "lineNumber"], [515, 22, 1693, 12], [516, 12, 1693, 12, "columnNumber"], [516, 24, 1693, 12], [517, 10, 1693, 12], [517, 17, 1693, 18], [517, 18, 1693, 19], [517, 33, 1697, 12], [517, 37, 1697, 12, "_jsxDevRuntime"], [517, 51, 1697, 12], [517, 52, 1697, 12, "jsxDEV"], [517, 58, 1697, 12], [517, 60, 1697, 13, "_Text"], [517, 65, 1697, 13], [517, 66, 1697, 13, "default"], [517, 73, 1697, 17], [518, 12, 1697, 18, "style"], [518, 17, 1697, 23], [518, 19, 1697, 25], [519, 14, 1697, 27, "fontSize"], [519, 22, 1697, 35], [519, 24, 1697, 37], [519, 26, 1697, 39], [520, 14, 1697, 41, "color"], [520, 19, 1697, 46], [520, 21, 1697, 48], [520, 30, 1697, 57], [521, 14, 1697, 59, "lineHeight"], [521, 24, 1697, 69], [521, 26, 1697, 71], [522, 12, 1697, 74], [522, 13, 1697, 76], [523, 12, 1697, 76, "children"], [523, 20, 1697, 76], [523, 22, 1697, 77], [524, 10, 1709, 12], [525, 12, 1709, 12, "fileName"], [525, 20, 1709, 12], [525, 22, 1709, 12, "_jsxFileName"], [525, 34, 1709, 12], [526, 12, 1709, 12, "lineNumber"], [526, 22, 1709, 12], [527, 12, 1709, 12, "columnNumber"], [527, 24, 1709, 12], [528, 10, 1709, 12], [528, 17, 1709, 18], [528, 18, 1709, 19], [529, 8, 1709, 19], [530, 10, 1709, 19, "fileName"], [530, 18, 1709, 19], [530, 20, 1709, 19, "_jsxFileName"], [530, 32, 1709, 19], [531, 10, 1709, 19, "lineNumber"], [531, 20, 1709, 19], [532, 10, 1709, 19, "columnNumber"], [532, 22, 1709, 19], [533, 8, 1709, 19], [533, 15, 1713, 16], [533, 16, 1717, 9], [534, 6, 1717, 9], [535, 8, 1717, 9, "fileName"], [535, 16, 1717, 9], [535, 18, 1717, 9, "_jsxFileName"], [535, 30, 1717, 9], [536, 8, 1717, 9, "lineNumber"], [536, 18, 1717, 9], [537, 8, 1717, 9, "columnNumber"], [537, 20, 1717, 9], [538, 6, 1717, 9], [538, 13, 1721, 12], [538, 14, 1721, 13], [539, 4, 1729, 2], [539, 5, 1729, 3], [540, 4, 1733, 2], [540, 24, 1737, 4], [540, 28, 1737, 4, "_jsxDevRuntime"], [540, 42, 1737, 4], [540, 43, 1737, 4, "jsxDEV"], [540, 49, 1737, 4], [540, 51, 1737, 5, "_View"], [540, 56, 1737, 5], [540, 57, 1737, 5, "default"], [540, 64, 1737, 9], [541, 6, 1737, 10, "style"], [541, 11, 1737, 15], [541, 13, 1737, 17], [542, 8, 1737, 19, "flex"], [542, 12, 1737, 23], [542, 14, 1737, 25], [542, 15, 1737, 26], [543, 8, 1737, 28, "backgroundColor"], [543, 23, 1737, 43], [543, 25, 1737, 45], [544, 6, 1737, 55], [544, 7, 1737, 57], [545, 6, 1737, 57, "children"], [545, 14, 1737, 57], [545, 30, 1741, 6], [545, 34, 1741, 6, "_jsxDevRuntime"], [545, 48, 1741, 6], [545, 49, 1741, 6, "jsxDEV"], [545, 55, 1741, 6], [545, 57, 1741, 7, "_expoStatusBar"], [545, 71, 1741, 7], [545, 72, 1741, 7, "StatusBar"], [545, 81, 1741, 16], [546, 8, 1741, 17, "style"], [546, 13, 1741, 22], [546, 15, 1741, 23], [547, 6, 1741, 29], [548, 8, 1741, 29, "fileName"], [548, 16, 1741, 29], [548, 18, 1741, 29, "_jsxFileName"], [548, 30, 1741, 29], [549, 8, 1741, 29, "lineNumber"], [549, 18, 1741, 29], [550, 8, 1741, 29, "columnNumber"], [550, 20, 1741, 29], [551, 6, 1741, 29], [551, 13, 1741, 31], [551, 14, 1741, 32], [551, 29, 1749, 6], [551, 33, 1749, 6, "_jsxDevRuntime"], [551, 47, 1749, 6], [551, 48, 1749, 6, "jsxDEV"], [551, 54, 1749, 6], [551, 56, 1749, 7, "_View"], [551, 61, 1749, 7], [551, 62, 1749, 7, "default"], [551, 69, 1749, 11], [552, 8, 1753, 8, "style"], [552, 13, 1753, 13], [552, 15, 1753, 15], [553, 10, 1757, 10, "backgroundColor"], [553, 25, 1757, 25], [553, 27, 1757, 27], [553, 33, 1757, 33], [554, 10, 1761, 10, "paddingTop"], [554, 20, 1761, 20], [554, 22, 1761, 22, "insets"], [554, 28, 1761, 28], [554, 29, 1761, 29, "top"], [554, 32, 1761, 32], [554, 35, 1761, 35], [554, 36, 1761, 36], [555, 10, 1765, 10, "paddingHorizontal"], [555, 27, 1765, 27], [555, 29, 1765, 29], [555, 31, 1765, 31], [556, 10, 1769, 10, "paddingBottom"], [556, 23, 1769, 23], [556, 25, 1769, 25], [556, 27, 1769, 27], [557, 10, 1773, 10, "borderBottomWidth"], [557, 27, 1773, 27], [557, 29, 1773, 29], [557, 30, 1773, 30], [558, 10, 1777, 10, "borderBottomColor"], [558, 27, 1777, 27], [558, 29, 1777, 29], [558, 38, 1777, 38], [559, 10, 1781, 10, "zIndex"], [559, 16, 1781, 16], [559, 18, 1781, 18], [560, 8, 1785, 8], [560, 9, 1785, 10], [561, 8, 1785, 10, "children"], [561, 16, 1785, 10], [561, 32, 1793, 8], [561, 36, 1793, 8, "_jsxDevRuntime"], [561, 50, 1793, 8], [561, 51, 1793, 8, "jsxDEV"], [561, 57, 1793, 8], [561, 59, 1793, 9, "_View"], [561, 64, 1793, 9], [561, 65, 1793, 9, "default"], [561, 72, 1793, 13], [562, 10, 1797, 10, "style"], [562, 15, 1797, 15], [562, 17, 1797, 17], [563, 12, 1801, 12, "flexDirection"], [563, 25, 1801, 25], [563, 27, 1801, 27], [563, 32, 1801, 32], [564, 12, 1805, 12, "alignItems"], [564, 22, 1805, 22], [564, 24, 1805, 24], [564, 32, 1805, 32], [565, 12, 1809, 12, "marginBottom"], [565, 24, 1809, 24], [565, 26, 1809, 26], [566, 10, 1813, 10], [566, 11, 1813, 12], [567, 10, 1813, 12, "children"], [567, 18, 1813, 12], [567, 34, 1821, 10], [567, 38, 1821, 10, "_jsxDevRuntime"], [567, 52, 1821, 10], [567, 53, 1821, 10, "jsxDEV"], [567, 59, 1821, 10], [567, 61, 1821, 11, "_TouchableOpacity"], [567, 78, 1821, 11], [567, 79, 1821, 11, "default"], [567, 86, 1821, 27], [568, 12, 1825, 12, "onPress"], [568, 19, 1825, 19], [568, 21, 1825, 21, "onPress"], [568, 22, 1825, 21], [568, 27, 1825, 27, "router"], [568, 45, 1825, 33], [568, 46, 1825, 34, "back"], [568, 50, 1825, 38], [568, 51, 1825, 39], [568, 52, 1825, 41], [569, 12, 1829, 12, "style"], [569, 17, 1829, 17], [569, 19, 1829, 19], [570, 14, 1829, 21, "marginRight"], [570, 25, 1829, 32], [570, 27, 1829, 34], [571, 12, 1829, 37], [571, 13, 1829, 39], [572, 12, 1829, 39, "children"], [572, 20, 1829, 39], [572, 35, 1837, 12], [572, 39, 1837, 12, "_jsxDevRuntime"], [572, 53, 1837, 12], [572, 54, 1837, 12, "jsxDEV"], [572, 60, 1837, 12], [572, 62, 1837, 13, "_lucideReactNative"], [572, 80, 1837, 13], [572, 81, 1837, 13, "ArrowLeft"], [572, 90, 1837, 22], [573, 14, 1837, 23, "size"], [573, 18, 1837, 27], [573, 20, 1837, 29], [573, 22, 1837, 32], [574, 14, 1837, 33, "color"], [574, 19, 1837, 38], [574, 21, 1837, 39], [575, 12, 1837, 48], [576, 14, 1837, 48, "fileName"], [576, 22, 1837, 48], [576, 24, 1837, 48, "_jsxFileName"], [576, 36, 1837, 48], [577, 14, 1837, 48, "lineNumber"], [577, 24, 1837, 48], [578, 14, 1837, 48, "columnNumber"], [578, 26, 1837, 48], [579, 12, 1837, 48], [579, 19, 1837, 50], [580, 10, 1837, 51], [581, 12, 1837, 51, "fileName"], [581, 20, 1837, 51], [581, 22, 1837, 51, "_jsxFileName"], [581, 34, 1837, 51], [582, 12, 1837, 51, "lineNumber"], [582, 22, 1837, 51], [583, 12, 1837, 51, "columnNumber"], [583, 24, 1837, 51], [584, 10, 1837, 51], [584, 17, 1841, 28], [584, 18, 1841, 29], [584, 33, 1845, 10], [584, 37, 1845, 10, "_jsxDevRuntime"], [584, 51, 1845, 10], [584, 52, 1845, 10, "jsxDEV"], [584, 58, 1845, 10], [584, 60, 1845, 11, "_Text"], [584, 65, 1845, 11], [584, 66, 1845, 11, "default"], [584, 73, 1845, 15], [585, 12, 1849, 12, "style"], [585, 17, 1849, 17], [585, 19, 1849, 19], [586, 14, 1853, 14, "fontSize"], [586, 22, 1853, 22], [586, 24, 1853, 24], [586, 26, 1853, 26], [587, 14, 1857, 14, "fontWeight"], [587, 24, 1857, 24], [587, 26, 1857, 26], [587, 32, 1857, 32], [588, 14, 1861, 14, "color"], [588, 19, 1861, 19], [588, 21, 1861, 21], [588, 30, 1861, 30], [589, 14, 1865, 14, "flex"], [589, 18, 1865, 18], [589, 20, 1865, 20], [590, 12, 1869, 12], [590, 13, 1869, 14], [591, 12, 1869, 14, "children"], [591, 20, 1869, 14], [591, 22, 1873, 11], [592, 10, 1881, 10], [593, 12, 1881, 10, "fileName"], [593, 20, 1881, 10], [593, 22, 1881, 10, "_jsxFileName"], [593, 34, 1881, 10], [594, 12, 1881, 10, "lineNumber"], [594, 22, 1881, 10], [595, 12, 1881, 10, "columnNumber"], [595, 24, 1881, 10], [596, 10, 1881, 10], [596, 17, 1881, 16], [596, 18, 1881, 17], [596, 33, 1889, 10], [596, 37, 1889, 10, "_jsxDevRuntime"], [596, 51, 1889, 10], [596, 52, 1889, 10, "jsxDEV"], [596, 58, 1889, 10], [596, 60, 1889, 11, "_Text"], [596, 65, 1889, 11], [596, 66, 1889, 11, "default"], [596, 73, 1889, 15], [597, 12, 1889, 16, "style"], [597, 17, 1889, 21], [597, 19, 1889, 23], [598, 14, 1889, 25, "fontSize"], [598, 22, 1889, 33], [598, 24, 1889, 35], [598, 26, 1889, 37], [599, 14, 1889, 39, "color"], [599, 19, 1889, 44], [599, 21, 1889, 46], [600, 12, 1889, 56], [600, 13, 1889, 58], [601, 12, 1889, 58, "children"], [601, 20, 1889, 58], [601, 22, 1893, 13], [601, 40, 1893, 31, "testingMode"], [601, 51, 1893, 42], [601, 54, 1893, 45], [601, 58, 1893, 49], [601, 61, 1893, 52], [601, 66, 1893, 57], [602, 10, 1893, 59], [603, 12, 1893, 59, "fileName"], [603, 20, 1893, 59], [603, 22, 1893, 59, "_jsxFileName"], [603, 34, 1893, 59], [604, 12, 1893, 59, "lineNumber"], [604, 22, 1893, 59], [605, 12, 1893, 59, "columnNumber"], [605, 24, 1893, 59], [606, 10, 1893, 59], [606, 17, 1897, 16], [606, 18, 1897, 17], [607, 8, 1897, 17], [608, 10, 1897, 17, "fileName"], [608, 18, 1897, 17], [608, 20, 1897, 17, "_jsxFileName"], [608, 32, 1897, 17], [609, 10, 1897, 17, "lineNumber"], [609, 20, 1897, 17], [610, 10, 1897, 17, "columnNumber"], [610, 22, 1897, 17], [611, 8, 1897, 17], [611, 15, 1901, 14], [611, 16, 1901, 15], [611, 31, 1909, 8], [611, 35, 1909, 8, "_jsxDevRuntime"], [611, 49, 1909, 8], [611, 50, 1909, 8, "jsxDEV"], [611, 56, 1909, 8], [611, 58, 1909, 9, "_View"], [611, 63, 1909, 9], [611, 64, 1909, 9, "default"], [611, 71, 1909, 13], [612, 10, 1913, 10, "style"], [612, 15, 1913, 15], [612, 17, 1913, 17], [613, 12, 1917, 12, "backgroundColor"], [613, 27, 1917, 27], [613, 29, 1917, 29], [613, 38, 1917, 38], [614, 12, 1921, 12, "borderRadius"], [614, 24, 1921, 24], [614, 26, 1921, 26], [614, 28, 1921, 28], [615, 12, 1925, 12, "padding"], [615, 19, 1925, 19], [615, 21, 1925, 21], [615, 23, 1925, 23], [616, 12, 1929, 12, "borderLeftWidth"], [616, 27, 1929, 27], [616, 29, 1929, 29], [616, 30, 1929, 30], [617, 12, 1933, 12, "borderLeftColor"], [617, 27, 1933, 27], [617, 29, 1933, 29], [618, 10, 1937, 10], [618, 11, 1937, 12], [619, 10, 1937, 12, "children"], [619, 18, 1937, 12], [619, 34, 1945, 10], [619, 38, 1945, 10, "_jsxDevRuntime"], [619, 52, 1945, 10], [619, 53, 1945, 10, "jsxDEV"], [619, 59, 1945, 10], [619, 61, 1945, 11, "_Text"], [619, 66, 1945, 11], [619, 67, 1945, 11, "default"], [619, 74, 1945, 15], [620, 12, 1949, 12, "style"], [620, 17, 1949, 17], [620, 19, 1949, 19], [621, 14, 1953, 14, "fontSize"], [621, 22, 1953, 22], [621, 24, 1953, 24], [621, 26, 1953, 26], [622, 14, 1957, 14, "color"], [622, 19, 1957, 19], [622, 21, 1957, 21], [622, 30, 1957, 30], [623, 14, 1961, 14, "fontWeight"], [623, 24, 1961, 24], [623, 26, 1961, 26], [623, 31, 1961, 31], [624, 14, 1965, 14, "marginBottom"], [624, 26, 1965, 26], [624, 28, 1965, 28], [625, 12, 1969, 12], [625, 13, 1969, 14], [626, 12, 1969, 14, "children"], [626, 20, 1969, 14], [626, 22, 1977, 13, "question"], [626, 30, 1977, 21], [626, 31, 1977, 22, "question"], [627, 10, 1977, 30], [628, 12, 1977, 30, "fileName"], [628, 20, 1977, 30], [628, 22, 1977, 30, "_jsxFileName"], [628, 34, 1977, 30], [629, 12, 1977, 30, "lineNumber"], [629, 22, 1977, 30], [630, 12, 1977, 30, "columnNumber"], [630, 24, 1977, 30], [631, 10, 1977, 30], [631, 17, 1981, 16], [631, 18, 1981, 17], [631, 33, 1985, 10], [631, 37, 1985, 10, "_jsxDevRuntime"], [631, 51, 1985, 10], [631, 52, 1985, 10, "jsxDEV"], [631, 58, 1985, 10], [631, 60, 1985, 11, "_View"], [631, 65, 1985, 11], [631, 66, 1985, 11, "default"], [631, 73, 1985, 15], [632, 12, 1989, 12, "style"], [632, 17, 1989, 17], [632, 19, 1989, 19], [633, 14, 1993, 14, "flexDirection"], [633, 27, 1993, 27], [633, 29, 1993, 29], [633, 34, 1993, 34], [634, 14, 1997, 14, "alignItems"], [634, 24, 1997, 24], [634, 26, 1997, 26], [634, 34, 1997, 34], [635, 14, 2001, 14, "marginBottom"], [635, 26, 2001, 26], [635, 28, 2001, 28], [636, 12, 2005, 12], [636, 13, 2005, 14], [637, 12, 2005, 14, "children"], [637, 20, 2005, 14], [637, 36, 2013, 12], [637, 40, 2013, 12, "_jsxDevRuntime"], [637, 54, 2013, 12], [637, 55, 2013, 12, "jsxDEV"], [637, 61, 2013, 12], [637, 63, 2013, 13, "_lucideReactNative"], [637, 81, 2013, 13], [637, 82, 2013, 13, "MapPin"], [637, 88, 2013, 19], [638, 14, 2013, 20, "size"], [638, 18, 2013, 24], [638, 20, 2013, 26], [638, 22, 2013, 29], [639, 14, 2013, 30, "color"], [639, 19, 2013, 35], [639, 21, 2013, 36], [640, 12, 2013, 45], [641, 14, 2013, 45, "fileName"], [641, 22, 2013, 45], [641, 24, 2013, 45, "_jsxFileName"], [641, 36, 2013, 45], [642, 14, 2013, 45, "lineNumber"], [642, 24, 2013, 45], [643, 14, 2013, 45, "columnNumber"], [643, 26, 2013, 45], [644, 12, 2013, 45], [644, 19, 2013, 47], [644, 20, 2013, 48], [644, 35, 2017, 12], [644, 39, 2017, 12, "_jsxDevRuntime"], [644, 53, 2017, 12], [644, 54, 2017, 12, "jsxDEV"], [644, 60, 2017, 12], [644, 62, 2017, 13, "_Text"], [644, 67, 2017, 13], [644, 68, 2017, 13, "default"], [644, 75, 2017, 17], [645, 14, 2021, 14, "style"], [645, 19, 2021, 19], [645, 21, 2021, 21], [646, 16, 2021, 23, "fontSize"], [646, 24, 2021, 31], [646, 26, 2021, 33], [646, 28, 2021, 35], [647, 16, 2021, 37, "color"], [647, 21, 2021, 42], [647, 23, 2021, 44], [647, 32, 2021, 53], [648, 16, 2021, 55, "marginLeft"], [648, 26, 2021, 65], [648, 28, 2021, 67], [648, 29, 2021, 68], [649, 16, 2021, 70, "flex"], [649, 20, 2021, 74], [649, 22, 2021, 76], [650, 14, 2021, 78], [650, 15, 2021, 80], [651, 14, 2021, 80, "children"], [651, 22, 2021, 80], [651, 24, 2029, 15, "question"], [651, 32, 2029, 23], [651, 33, 2029, 24, "location"], [652, 12, 2029, 32], [653, 14, 2029, 32, "fileName"], [653, 22, 2029, 32], [653, 24, 2029, 32, "_jsxFileName"], [653, 36, 2029, 32], [654, 14, 2029, 32, "lineNumber"], [654, 24, 2029, 32], [655, 14, 2029, 32, "columnNumber"], [655, 26, 2029, 32], [656, 12, 2029, 32], [656, 19, 2033, 18], [656, 20, 2033, 19], [657, 10, 2033, 19], [658, 12, 2033, 19, "fileName"], [658, 20, 2033, 19], [658, 22, 2033, 19, "_jsxFileName"], [658, 34, 2033, 19], [659, 12, 2033, 19, "lineNumber"], [659, 22, 2033, 19], [660, 12, 2033, 19, "columnNumber"], [660, 24, 2033, 19], [661, 10, 2033, 19], [661, 17, 2037, 16], [661, 18, 2037, 17], [661, 33, 2041, 10], [661, 37, 2041, 10, "_jsxDevRuntime"], [661, 51, 2041, 10], [661, 52, 2041, 10, "jsxDEV"], [661, 58, 2041, 10], [661, 60, 2041, 11, "_View"], [661, 65, 2041, 11], [661, 66, 2041, 11, "default"], [661, 73, 2041, 15], [662, 12, 2045, 12, "style"], [662, 17, 2045, 17], [662, 19, 2045, 19], [663, 14, 2049, 14, "flexDirection"], [663, 27, 2049, 27], [663, 29, 2049, 29], [663, 34, 2049, 34], [664, 14, 2053, 14, "justifyContent"], [664, 28, 2053, 28], [664, 30, 2053, 30], [664, 45, 2053, 45], [665, 14, 2057, 14, "alignItems"], [665, 24, 2057, 24], [665, 26, 2057, 26], [666, 12, 2061, 12], [666, 13, 2061, 14], [667, 12, 2061, 14, "children"], [667, 20, 2061, 14], [667, 36, 2069, 12], [667, 40, 2069, 12, "_jsxDevRuntime"], [667, 54, 2069, 12], [667, 55, 2069, 12, "jsxDEV"], [667, 61, 2069, 12], [667, 63, 2069, 13, "_View"], [667, 68, 2069, 13], [667, 69, 2069, 13, "default"], [667, 76, 2069, 17], [668, 14, 2069, 18, "style"], [668, 19, 2069, 23], [668, 21, 2069, 25], [669, 16, 2069, 27, "flexDirection"], [669, 29, 2069, 40], [669, 31, 2069, 42], [669, 36, 2069, 47], [670, 16, 2069, 49, "alignItems"], [670, 26, 2069, 59], [670, 28, 2069, 61], [671, 14, 2069, 70], [671, 15, 2069, 72], [672, 14, 2069, 72, "children"], [672, 22, 2069, 72], [672, 38, 2073, 14], [672, 42, 2073, 14, "_jsxDevRuntime"], [672, 56, 2073, 14], [672, 57, 2073, 14, "jsxDEV"], [672, 63, 2073, 14], [672, 65, 2073, 15, "_lucideReactNative"], [672, 83, 2073, 15], [672, 84, 2073, 15, "DollarSign"], [672, 94, 2073, 25], [673, 16, 2073, 26, "size"], [673, 20, 2073, 30], [673, 22, 2073, 32], [673, 24, 2073, 35], [674, 16, 2073, 36, "color"], [674, 21, 2073, 41], [674, 23, 2073, 42], [675, 14, 2073, 51], [676, 16, 2073, 51, "fileName"], [676, 24, 2073, 51], [676, 26, 2073, 51, "_jsxFileName"], [676, 38, 2073, 51], [677, 16, 2073, 51, "lineNumber"], [677, 26, 2073, 51], [678, 16, 2073, 51, "columnNumber"], [678, 28, 2073, 51], [679, 14, 2073, 51], [679, 21, 2073, 53], [679, 22, 2073, 54], [679, 37, 2077, 14], [679, 41, 2077, 14, "_jsxDevRuntime"], [679, 55, 2077, 14], [679, 56, 2077, 14, "jsxDEV"], [679, 62, 2077, 14], [679, 64, 2077, 15, "_Text"], [679, 69, 2077, 15], [679, 70, 2077, 15, "default"], [679, 77, 2077, 19], [680, 16, 2081, 16, "style"], [680, 21, 2081, 21], [680, 23, 2081, 23], [681, 18, 2085, 18, "fontSize"], [681, 26, 2085, 26], [681, 28, 2085, 28], [681, 30, 2085, 30], [682, 18, 2089, 18, "color"], [682, 23, 2089, 23], [682, 25, 2089, 25], [682, 34, 2089, 34], [683, 18, 2093, 18, "fontWeight"], [683, 28, 2093, 28], [683, 30, 2093, 30], [683, 35, 2093, 35], [684, 18, 2097, 18, "marginLeft"], [684, 28, 2097, 28], [684, 30, 2097, 30], [685, 16, 2101, 16], [685, 17, 2101, 18], [686, 16, 2101, 18, "children"], [686, 24, 2101, 18], [686, 26, 2109, 17], [686, 30, 2109, 21, "question"], [686, 38, 2109, 29], [686, 39, 2109, 30, "reward"], [686, 45, 2109, 36], [686, 46, 2109, 37, "toFixed"], [686, 53, 2109, 44], [686, 54, 2109, 45], [686, 55, 2109, 46], [686, 56, 2109, 47], [687, 14, 2109, 56], [688, 16, 2109, 56, "fileName"], [688, 24, 2109, 56], [688, 26, 2109, 56, "_jsxFileName"], [688, 38, 2109, 56], [689, 16, 2109, 56, "lineNumber"], [689, 26, 2109, 56], [690, 16, 2109, 56, "columnNumber"], [690, 28, 2109, 56], [691, 14, 2109, 56], [691, 21, 2113, 20], [691, 22, 2113, 21], [692, 12, 2113, 21], [693, 14, 2113, 21, "fileName"], [693, 22, 2113, 21], [693, 24, 2113, 21, "_jsxFileName"], [693, 36, 2113, 21], [694, 14, 2113, 21, "lineNumber"], [694, 24, 2113, 21], [695, 14, 2113, 21, "columnNumber"], [695, 26, 2113, 21], [696, 12, 2113, 21], [696, 19, 2117, 18], [696, 20, 2117, 19], [696, 35, 2121, 12], [696, 39, 2121, 12, "_jsxDevRuntime"], [696, 53, 2121, 12], [696, 54, 2121, 12, "jsxDEV"], [696, 60, 2121, 12], [696, 62, 2121, 13, "_View"], [696, 67, 2121, 13], [696, 68, 2121, 13, "default"], [696, 75, 2121, 17], [697, 14, 2121, 18, "style"], [697, 19, 2121, 23], [697, 21, 2121, 25], [698, 16, 2121, 27, "flexDirection"], [698, 29, 2121, 40], [698, 31, 2121, 42], [698, 36, 2121, 47], [699, 16, 2121, 49, "alignItems"], [699, 26, 2121, 59], [699, 28, 2121, 61], [700, 14, 2121, 70], [700, 15, 2121, 72], [701, 14, 2121, 72, "children"], [701, 22, 2121, 72], [701, 38, 2125, 14], [701, 42, 2125, 14, "_jsxDevRuntime"], [701, 56, 2125, 14], [701, 57, 2125, 14, "jsxDEV"], [701, 63, 2125, 14], [701, 65, 2125, 15, "_lucideReactNative"], [701, 83, 2125, 15], [701, 84, 2125, 15, "Clock"], [701, 89, 2125, 20], [702, 16, 2125, 21, "size"], [702, 20, 2125, 25], [702, 22, 2125, 27], [702, 24, 2125, 30], [703, 16, 2125, 31, "color"], [703, 21, 2125, 36], [703, 23, 2125, 37], [704, 14, 2125, 46], [705, 16, 2125, 46, "fileName"], [705, 24, 2125, 46], [705, 26, 2125, 46, "_jsxFileName"], [705, 38, 2125, 46], [706, 16, 2125, 46, "lineNumber"], [706, 26, 2125, 46], [707, 16, 2125, 46, "columnNumber"], [707, 28, 2125, 46], [708, 14, 2125, 46], [708, 21, 2125, 48], [708, 22, 2125, 49], [708, 37, 2129, 14], [708, 41, 2129, 14, "_jsxDevRuntime"], [708, 55, 2129, 14], [708, 56, 2129, 14, "jsxDEV"], [708, 62, 2129, 14], [708, 64, 2129, 15, "_Text"], [708, 69, 2129, 15], [708, 70, 2129, 15, "default"], [708, 77, 2129, 19], [709, 16, 2129, 20, "style"], [709, 21, 2129, 25], [709, 23, 2129, 27], [710, 18, 2129, 29, "fontSize"], [710, 26, 2129, 37], [710, 28, 2129, 39], [710, 30, 2129, 41], [711, 18, 2129, 43, "color"], [711, 23, 2129, 48], [711, 25, 2129, 50], [711, 34, 2129, 59], [712, 18, 2129, 61, "marginLeft"], [712, 28, 2129, 71], [712, 30, 2129, 73], [713, 16, 2129, 75], [713, 17, 2129, 77], [714, 16, 2129, 77, "children"], [714, 24, 2129, 77], [714, 26, 2133, 17, "question"], [714, 34, 2133, 25], [714, 35, 2133, 26, "postedAt"], [715, 14, 2133, 34], [716, 16, 2133, 34, "fileName"], [716, 24, 2133, 34], [716, 26, 2133, 34, "_jsxFileName"], [716, 38, 2133, 34], [717, 16, 2133, 34, "lineNumber"], [717, 26, 2133, 34], [718, 16, 2133, 34, "columnNumber"], [718, 28, 2133, 34], [719, 14, 2133, 34], [719, 21, 2137, 20], [719, 22, 2137, 21], [720, 12, 2137, 21], [721, 14, 2137, 21, "fileName"], [721, 22, 2137, 21], [721, 24, 2137, 21, "_jsxFileName"], [721, 36, 2137, 21], [722, 14, 2137, 21, "lineNumber"], [722, 24, 2137, 21], [723, 14, 2137, 21, "columnNumber"], [723, 26, 2137, 21], [724, 12, 2137, 21], [724, 19, 2141, 18], [724, 20, 2141, 19], [725, 10, 2141, 19], [726, 12, 2141, 19, "fileName"], [726, 20, 2141, 19], [726, 22, 2141, 19, "_jsxFileName"], [726, 34, 2141, 19], [727, 12, 2141, 19, "lineNumber"], [727, 22, 2141, 19], [728, 12, 2141, 19, "columnNumber"], [728, 24, 2141, 19], [729, 10, 2141, 19], [729, 17, 2145, 16], [729, 18, 2145, 17], [730, 8, 2145, 17], [731, 10, 2145, 17, "fileName"], [731, 18, 2145, 17], [731, 20, 2145, 17, "_jsxFileName"], [731, 32, 2145, 17], [732, 10, 2145, 17, "lineNumber"], [732, 20, 2145, 17], [733, 10, 2145, 17, "columnNumber"], [733, 22, 2145, 17], [734, 8, 2145, 17], [734, 15, 2149, 14], [734, 16, 2149, 15], [735, 6, 2149, 15], [736, 8, 2149, 15, "fileName"], [736, 16, 2149, 15], [736, 18, 2149, 15, "_jsxFileName"], [736, 30, 2149, 15], [737, 8, 2149, 15, "lineNumber"], [737, 18, 2149, 15], [738, 8, 2149, 15, "columnNumber"], [738, 20, 2149, 15], [739, 6, 2149, 15], [739, 13, 2153, 12], [739, 14, 2153, 13], [739, 29, 2157, 6], [739, 33, 2157, 6, "_jsxDevRuntime"], [739, 47, 2157, 6], [739, 48, 2157, 6, "jsxDEV"], [739, 54, 2157, 6], [739, 56, 2157, 7, "_KeyboardAvoidingAnimatedView"], [739, 85, 2157, 7], [739, 86, 2157, 7, "default"], [739, 93, 2157, 35], [740, 8, 2157, 36, "style"], [740, 13, 2157, 41], [740, 15, 2157, 43], [741, 10, 2157, 45, "flex"], [741, 14, 2157, 49], [741, 16, 2157, 51], [742, 8, 2157, 53], [742, 9, 2157, 55], [743, 8, 2157, 56, "behavior"], [743, 16, 2157, 64], [743, 18, 2157, 65], [743, 27, 2157, 74], [744, 8, 2157, 74, "children"], [744, 16, 2157, 74], [744, 32, 2161, 8], [744, 36, 2161, 8, "_jsxDevRuntime"], [744, 50, 2161, 8], [744, 51, 2161, 8, "jsxDEV"], [744, 57, 2161, 8], [744, 59, 2161, 9, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [744, 70, 2161, 9], [744, 71, 2161, 9, "default"], [744, 78, 2161, 19], [745, 10, 2165, 10, "style"], [745, 15, 2165, 15], [745, 17, 2165, 17], [746, 12, 2165, 19, "flex"], [746, 16, 2165, 23], [746, 18, 2165, 25], [747, 10, 2165, 27], [747, 11, 2165, 29], [748, 10, 2169, 10, "contentContainerStyle"], [748, 31, 2169, 31], [748, 33, 2169, 33], [749, 12, 2169, 35, "paddingBottom"], [749, 25, 2169, 48], [749, 27, 2169, 50, "insets"], [749, 33, 2169, 56], [749, 34, 2169, 57, "bottom"], [749, 40, 2169, 63], [749, 43, 2169, 66], [750, 10, 2169, 70], [750, 11, 2169, 72], [751, 10, 2173, 10, "showsVerticalScrollIndicator"], [751, 38, 2173, 38], [751, 40, 2173, 40], [751, 45, 2173, 46], [752, 10, 2173, 46, "children"], [752, 18, 2173, 46], [752, 33, 2181, 10], [752, 37, 2181, 10, "_jsxDevRuntime"], [752, 51, 2181, 10], [752, 52, 2181, 10, "jsxDEV"], [752, 58, 2181, 10], [752, 60, 2181, 11, "_View"], [752, 65, 2181, 11], [752, 66, 2181, 11, "default"], [752, 73, 2181, 15], [753, 12, 2181, 16, "style"], [753, 17, 2181, 21], [753, 19, 2181, 23], [754, 14, 2181, 25, "padding"], [754, 21, 2181, 32], [754, 23, 2181, 34], [755, 12, 2181, 37], [755, 13, 2181, 39], [756, 12, 2181, 39, "children"], [756, 20, 2181, 39], [756, 36, 2189, 12], [756, 40, 2189, 12, "_jsxDevRuntime"], [756, 54, 2189, 12], [756, 55, 2189, 12, "jsxDEV"], [756, 61, 2189, 12], [756, 63, 2189, 13, "LocationStatus"], [756, 77, 2189, 27], [757, 14, 2189, 27, "fileName"], [757, 22, 2189, 27], [757, 24, 2189, 27, "_jsxFileName"], [757, 36, 2189, 27], [758, 14, 2189, 27, "lineNumber"], [758, 24, 2189, 27], [759, 14, 2189, 27, "columnNumber"], [759, 26, 2189, 27], [760, 12, 2189, 27], [760, 19, 2189, 29], [760, 20, 2189, 30], [760, 35, 2195, 12], [760, 39, 2195, 12, "_jsxDevRuntime"], [760, 53, 2195, 12], [760, 54, 2195, 12, "jsxDEV"], [760, 60, 2195, 12], [760, 62, 2195, 13, "_View"], [760, 67, 2195, 13], [760, 68, 2195, 13, "default"], [760, 75, 2195, 17], [761, 14, 2195, 18, "style"], [761, 19, 2195, 23], [761, 21, 2195, 25], [762, 16, 2195, 27, "marginBottom"], [762, 28, 2195, 39], [762, 30, 2195, 41], [763, 14, 2195, 44], [763, 15, 2195, 46], [764, 14, 2195, 46, "children"], [764, 22, 2195, 46], [764, 38, 2197, 14], [764, 42, 2197, 14, "_jsxDevRuntime"], [764, 56, 2197, 14], [764, 57, 2197, 14, "jsxDEV"], [764, 63, 2197, 14], [764, 65, 2197, 15, "_View"], [764, 70, 2197, 15], [764, 71, 2197, 15, "default"], [764, 78, 2197, 19], [765, 16, 2199, 16, "style"], [765, 21, 2199, 21], [765, 23, 2199, 23], [766, 18, 2201, 18, "flexDirection"], [766, 31, 2201, 31], [766, 33, 2201, 33], [766, 38, 2201, 38], [767, 18, 2203, 18, "alignItems"], [767, 28, 2203, 28], [767, 30, 2203, 30], [767, 38, 2203, 38], [768, 18, 2205, 18, "marginBottom"], [768, 30, 2205, 30], [768, 32, 2205, 32], [769, 16, 2207, 16], [769, 17, 2207, 18], [770, 16, 2207, 18, "children"], [770, 24, 2207, 18], [770, 40, 2211, 16], [770, 44, 2211, 16, "_jsxDevRuntime"], [770, 58, 2211, 16], [770, 59, 2211, 16, "jsxDEV"], [770, 65, 2211, 16], [770, 67, 2211, 17, "_View"], [770, 72, 2211, 17], [770, 73, 2211, 17, "default"], [770, 80, 2211, 21], [771, 18, 2213, 18, "style"], [771, 23, 2213, 23], [771, 25, 2213, 25], [772, 20, 2215, 20, "width"], [772, 25, 2215, 25], [772, 27, 2215, 27], [772, 29, 2215, 29], [773, 20, 2217, 20, "height"], [773, 26, 2217, 26], [773, 28, 2217, 28], [773, 30, 2217, 30], [774, 20, 2219, 20, "borderRadius"], [774, 32, 2219, 32], [774, 34, 2219, 34], [774, 36, 2219, 36], [775, 20, 2221, 20, "backgroundColor"], [775, 35, 2221, 35], [775, 37, 2221, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [775, 53, 2221, 53], [775, 56, 2223, 24], [775, 65, 2223, 33], [775, 68, 2225, 24, "locationStatus"], [775, 82, 2225, 38], [775, 87, 2225, 43], [775, 97, 2225, 53], [775, 101, 2225, 57, "testingMode"], [775, 112, 2225, 68], [775, 115, 2227, 26], [775, 124, 2227, 35], [775, 127, 2229, 26], [775, 136, 2229, 35], [776, 20, 2231, 20, "alignItems"], [776, 30, 2231, 30], [776, 32, 2231, 32], [776, 40, 2231, 40], [777, 20, 2233, 20, "justifyContent"], [777, 34, 2233, 34], [777, 36, 2233, 36], [777, 44, 2233, 44], [778, 20, 2235, 20, "marginRight"], [778, 31, 2235, 31], [778, 33, 2235, 33], [779, 18, 2237, 18], [779, 19, 2237, 20], [780, 18, 2237, 20, "children"], [780, 26, 2237, 20], [780, 28, 2241, 19, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [780, 44, 2241, 35], [780, 60, 2243, 20], [780, 64, 2243, 20, "_jsxDevRuntime"], [780, 78, 2243, 20], [780, 79, 2243, 20, "jsxDEV"], [780, 85, 2243, 20], [780, 87, 2243, 21, "_lucideReactNative"], [780, 105, 2243, 21], [780, 106, 2243, 21, "CheckCircle2"], [780, 118, 2243, 33], [781, 20, 2243, 34, "size"], [781, 24, 2243, 38], [781, 26, 2243, 40], [781, 28, 2243, 43], [782, 20, 2243, 44, "color"], [782, 25, 2243, 49], [782, 27, 2243, 50], [783, 18, 2243, 56], [784, 20, 2243, 56, "fileName"], [784, 28, 2243, 56], [784, 30, 2243, 56, "_jsxFileName"], [784, 42, 2243, 56], [785, 20, 2243, 56, "lineNumber"], [785, 30, 2243, 56], [786, 20, 2243, 56, "columnNumber"], [786, 32, 2243, 56], [787, 18, 2243, 56], [787, 25, 2243, 58], [787, 26, 2243, 59], [787, 42, 2247, 20], [787, 46, 2247, 20, "_jsxDevRuntime"], [787, 60, 2247, 20], [787, 61, 2247, 20, "jsxDEV"], [787, 67, 2247, 20], [787, 69, 2247, 21, "_Text"], [787, 74, 2247, 21], [787, 75, 2247, 21, "default"], [787, 82, 2247, 25], [788, 20, 2247, 26, "style"], [788, 25, 2247, 31], [788, 27, 2247, 33], [789, 22, 2247, 35, "color"], [789, 27, 2247, 40], [789, 29, 2247, 42], [789, 35, 2247, 48], [790, 22, 2247, 50, "fontSize"], [790, 30, 2247, 58], [790, 32, 2247, 60], [790, 34, 2247, 62], [791, 22, 2247, 64, "fontWeight"], [791, 32, 2247, 74], [791, 34, 2247, 76], [792, 20, 2247, 82], [792, 21, 2247, 84], [793, 20, 2247, 84, "children"], [793, 28, 2247, 84], [793, 30, 2247, 85], [794, 18, 2247, 86], [795, 20, 2247, 86, "fileName"], [795, 28, 2247, 86], [795, 30, 2247, 86, "_jsxFileName"], [795, 42, 2247, 86], [796, 20, 2247, 86, "lineNumber"], [796, 30, 2247, 86], [797, 20, 2247, 86, "columnNumber"], [797, 32, 2247, 86], [798, 18, 2247, 86], [798, 25, 2247, 92], [799, 16, 2249, 19], [800, 18, 2249, 19, "fileName"], [800, 26, 2249, 19], [800, 28, 2249, 19, "_jsxFileName"], [800, 40, 2249, 19], [801, 18, 2249, 19, "lineNumber"], [801, 28, 2249, 19], [802, 18, 2249, 19, "columnNumber"], [802, 30, 2249, 19], [803, 16, 2249, 19], [803, 23, 2251, 22], [803, 24, 2251, 23], [803, 39, 2253, 16], [803, 43, 2253, 16, "_jsxDevRuntime"], [803, 57, 2253, 16], [803, 58, 2253, 16, "jsxDEV"], [803, 64, 2253, 16], [803, 66, 2253, 17, "_Text"], [803, 71, 2253, 17], [803, 72, 2253, 17, "default"], [803, 79, 2253, 21], [804, 18, 2255, 18, "style"], [804, 23, 2255, 23], [804, 25, 2255, 25], [805, 20, 2257, 20, "fontSize"], [805, 28, 2257, 28], [805, 30, 2257, 30], [805, 32, 2257, 32], [806, 20, 2259, 20, "fontWeight"], [806, 30, 2259, 30], [806, 32, 2259, 32], [806, 37, 2259, 37], [807, 20, 2261, 20, "color"], [807, 25, 2261, 25], [807, 27, 2261, 27], [808, 18, 2263, 18], [808, 19, 2263, 20], [809, 18, 2263, 20, "children"], [809, 26, 2263, 20], [809, 28, 2265, 17], [810, 16, 2269, 16], [811, 18, 2269, 16, "fileName"], [811, 26, 2269, 16], [811, 28, 2269, 16, "_jsxFileName"], [811, 40, 2269, 16], [812, 18, 2269, 16, "lineNumber"], [812, 28, 2269, 16], [813, 18, 2269, 16, "columnNumber"], [813, 30, 2269, 16], [814, 16, 2269, 16], [814, 23, 2269, 22], [814, 24, 2269, 23], [815, 14, 2269, 23], [816, 16, 2269, 23, "fileName"], [816, 24, 2269, 23], [816, 26, 2269, 23, "_jsxFileName"], [816, 38, 2269, 23], [817, 16, 2269, 23, "lineNumber"], [817, 26, 2269, 23], [818, 16, 2269, 23, "columnNumber"], [818, 28, 2269, 23], [819, 14, 2269, 23], [819, 21, 2271, 20], [819, 22, 2271, 21], [819, 37, 2273, 14], [819, 41, 2273, 14, "_jsxDevRuntime"], [819, 55, 2273, 14], [819, 56, 2273, 14, "jsxDEV"], [819, 62, 2273, 14], [819, 64, 2273, 15, "_Text"], [819, 69, 2273, 15], [819, 70, 2273, 15, "default"], [819, 77, 2273, 19], [820, 16, 2275, 16, "style"], [820, 21, 2275, 21], [820, 23, 2275, 23], [821, 18, 2277, 18, "fontSize"], [821, 26, 2277, 26], [821, 28, 2277, 28], [821, 30, 2277, 30], [822, 18, 2279, 18, "color"], [822, 23, 2279, 23], [822, 25, 2279, 25], [822, 34, 2279, 34], [823, 18, 2281, 18, "marginBottom"], [823, 30, 2281, 30], [823, 32, 2281, 32], [823, 34, 2281, 34], [824, 18, 2283, 18, "lineHeight"], [824, 28, 2283, 28], [824, 30, 2283, 30], [825, 16, 2285, 16], [825, 17, 2285, 18], [826, 16, 2285, 18, "children"], [826, 24, 2285, 18], [826, 26, 2289, 17], [827, 14, 2289, 148], [828, 16, 2289, 148, "fileName"], [828, 24, 2289, 148], [828, 26, 2289, 148, "_jsxFileName"], [828, 38, 2289, 148], [829, 16, 2289, 148, "lineNumber"], [829, 26, 2289, 148], [830, 16, 2289, 148, "columnNumber"], [830, 28, 2289, 148], [831, 14, 2289, 148], [831, 21, 2291, 20], [831, 22, 2291, 21], [831, 24, 2293, 15, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [831, 40, 2293, 31], [831, 56, 2295, 16], [831, 60, 2295, 16, "_jsxDevRuntime"], [831, 74, 2295, 16], [831, 75, 2295, 16, "jsxDEV"], [831, 81, 2295, 16], [831, 83, 2295, 17, "_View"], [831, 88, 2295, 17], [831, 89, 2295, 17, "default"], [831, 96, 2295, 21], [832, 16, 2297, 18, "style"], [832, 21, 2297, 23], [832, 23, 2297, 25], [833, 18, 2299, 20, "backgroundColor"], [833, 33, 2299, 35], [833, 35, 2299, 37], [833, 41, 2299, 43], [834, 18, 2301, 20, "borderRadius"], [834, 30, 2301, 32], [834, 32, 2301, 34], [834, 34, 2301, 36], [835, 18, 2303, 20, "overflow"], [835, 26, 2303, 28], [835, 28, 2303, 30], [835, 36, 2303, 38], [836, 18, 2305, 20, "borderWidth"], [836, 29, 2305, 31], [836, 31, 2305, 33], [836, 32, 2305, 34], [837, 18, 2307, 20, "borderColor"], [837, 29, 2307, 31], [837, 31, 2307, 33], [837, 40, 2307, 42], [838, 18, 2309, 20], [838, 21, 2309, 23, "Platform"], [838, 38, 2309, 31], [838, 39, 2309, 32, "select"], [838, 45, 2309, 38], [838, 46, 2309, 39], [839, 20, 2311, 22, "ios"], [839, 23, 2311, 25], [839, 25, 2311, 27], [840, 22, 2313, 24, "shadowColor"], [840, 33, 2313, 35], [840, 35, 2313, 37], [840, 41, 2313, 43], [841, 22, 2315, 24, "shadowOffset"], [841, 34, 2315, 36], [841, 36, 2315, 38], [842, 24, 2315, 40, "width"], [842, 29, 2315, 45], [842, 31, 2315, 47], [842, 32, 2315, 48], [843, 24, 2315, 50, "height"], [843, 30, 2315, 56], [843, 32, 2315, 58], [844, 22, 2315, 60], [844, 23, 2315, 61], [845, 22, 2317, 24, "shadowOpacity"], [845, 35, 2317, 37], [845, 37, 2317, 39], [845, 41, 2317, 43], [846, 22, 2319, 24, "shadowRadius"], [846, 34, 2319, 36], [846, 36, 2319, 38], [847, 20, 2321, 22], [847, 21, 2321, 23], [848, 20, 2323, 22, "android"], [848, 27, 2323, 29], [848, 29, 2323, 31], [849, 22, 2325, 24, "elevation"], [849, 31, 2325, 33], [849, 33, 2325, 35], [850, 20, 2327, 22], [850, 21, 2327, 23], [851, 20, 2329, 22, "web"], [851, 23, 2329, 25], [851, 25, 2329, 27], [852, 22, 2331, 24, "boxShadow"], [852, 31, 2331, 33], [852, 33, 2331, 35], [853, 20, 2333, 22], [854, 18, 2335, 20], [854, 19, 2335, 21], [855, 16, 2337, 18], [855, 17, 2337, 20], [856, 16, 2337, 20, "children"], [856, 24, 2337, 20], [856, 40, 2341, 18], [856, 44, 2341, 18, "_jsxDevRuntime"], [856, 58, 2341, 18], [856, 59, 2341, 18, "jsxDEV"], [856, 65, 2341, 18], [856, 67, 2341, 19, "_View"], [856, 72, 2341, 19], [856, 73, 2341, 19, "default"], [856, 80, 2341, 23], [857, 18, 2341, 24, "style"], [857, 23, 2341, 29], [857, 25, 2341, 31], [858, 20, 2341, 33, "position"], [858, 28, 2341, 41], [858, 30, 2341, 43], [859, 18, 2341, 54], [859, 19, 2341, 56], [860, 18, 2341, 56, "children"], [860, 26, 2341, 56], [860, 42, 2343, 20], [860, 46, 2343, 20, "_jsxDevRuntime"], [860, 60, 2343, 20], [860, 61, 2343, 20, "jsxDEV"], [860, 67, 2343, 20], [860, 69, 2343, 21, "_Image"], [860, 75, 2343, 21], [860, 76, 2343, 21, "default"], [860, 83, 2343, 26], [861, 20, 2345, 22, "source"], [861, 26, 2345, 28], [861, 28, 2345, 30], [862, 22, 2345, 32, "uri"], [862, 25, 2345, 35], [862, 27, 2345, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [863, 20, 2345, 54], [863, 21, 2345, 56], [864, 20, 2347, 22, "style"], [864, 25, 2347, 27], [864, 27, 2347, 29], [865, 22, 2348, 24, "width"], [865, 27, 2348, 29], [865, 29, 2348, 31], [865, 35, 2348, 37], [866, 22, 2349, 24, "height"], [866, 28, 2349, 30], [866, 30, 2349, 32], [866, 33, 2349, 35], [867, 22, 2349, 37], [868, 22, 2350, 24, "backgroundColor"], [868, 37, 2350, 39], [868, 39, 2350, 41], [868, 48, 2350, 50], [869, 22, 2351, 24, "borderRadius"], [869, 34, 2351, 36], [869, 36, 2351, 38], [869, 38, 2351, 40], [869, 39, 2351, 42], [870, 20, 2352, 22], [870, 21, 2352, 24], [871, 20, 2354, 22, "resizeMode"], [871, 30, 2354, 32], [871, 32, 2354, 33], [871, 41, 2354, 42], [871, 42, 2354, 43], [872, 20, 2354, 43], [874, 20, 2356, 22, "onError"], [874, 27, 2356, 29], [874, 29, 2356, 32, "e"], [874, 30, 2356, 33], [874, 34, 2356, 38], [875, 22, 2358, 24, "console"], [875, 29, 2358, 31], [875, 30, 2358, 32, "error"], [875, 35, 2358, 37], [875, 36, 2358, 38], [875, 73, 2358, 75], [875, 75, 2358, 77], [876, 24, 2360, 26, "error"], [876, 29, 2360, 31], [876, 31, 2360, 33, "e"], [876, 32, 2360, 34], [876, 33, 2360, 35, "nativeEvent"], [876, 44, 2360, 46], [876, 46, 2360, 48, "error"], [876, 51, 2360, 53], [877, 24, 2362, 26, "uri"], [877, 27, 2362, 29], [877, 29, 2362, 31, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [877, 45, 2362, 47], [878, 24, 2364, 26, "cameraResult"], [878, 36, 2364, 38], [878, 38, 2364, 40, "JSON"], [878, 42, 2364, 44], [878, 43, 2364, 45, "stringify"], [878, 52, 2364, 54], [878, 53, 2364, 55, "cameraResult"], [878, 65, 2364, 67], [878, 67, 2364, 69], [878, 71, 2364, 73], [878, 73, 2364, 75], [878, 74, 2364, 76], [879, 22, 2366, 24], [879, 23, 2366, 25], [879, 24, 2366, 26], [880, 20, 2368, 22], [880, 21, 2368, 24], [881, 20, 2370, 22, "onLoad"], [881, 26, 2370, 28], [881, 28, 2370, 30, "onLoad"], [881, 29, 2370, 30], [881, 34, 2370, 36], [882, 22, 2372, 24, "console"], [882, 29, 2372, 31], [882, 30, 2372, 32, "log"], [882, 33, 2372, 35], [882, 34, 2372, 36], [882, 78, 2372, 80], [882, 80, 2372, 82, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [882, 96, 2372, 98], [882, 97, 2372, 99], [883, 20, 2374, 22], [883, 21, 2374, 24], [884, 20, 2376, 22, "onLoadStart"], [884, 31, 2376, 33], [884, 33, 2376, 35, "onLoadStart"], [884, 34, 2376, 35], [884, 39, 2376, 41], [885, 22, 2378, 24, "console"], [885, 29, 2378, 31], [885, 30, 2378, 32, "log"], [885, 33, 2378, 35], [885, 34, 2378, 36], [885, 69, 2378, 71], [885, 71, 2378, 73, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [885, 87, 2378, 89], [885, 88, 2378, 90], [886, 22, 2379, 24, "console"], [886, 29, 2379, 31], [886, 30, 2379, 32, "log"], [886, 33, 2379, 35], [886, 34, 2379, 36], [886, 71, 2379, 73], [886, 73, 2379, 75], [887, 24, 2380, 26, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [887, 40, 2380, 42], [888, 24, 2381, 26, "isBlob"], [888, 30, 2381, 32], [888, 32, 2381, 34, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [888, 48, 2381, 50], [888, 50, 2381, 52, "startsWith"], [888, 60, 2381, 62], [888, 61, 2381, 63], [888, 68, 2381, 70], [888, 69, 2381, 71], [889, 24, 2382, 26, "isDataUri"], [889, 33, 2382, 35], [889, 35, 2382, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [889, 51, 2382, 53], [889, 53, 2382, 55, "startsWith"], [889, 63, 2382, 65], [889, 64, 2382, 66], [889, 71, 2382, 73], [889, 72, 2382, 74], [890, 24, 2383, 26, "length"], [890, 30, 2383, 32], [890, 32, 2383, 34, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [890, 48, 2383, 50], [890, 50, 2383, 52, "length"], [891, 22, 2384, 24], [891, 23, 2384, 25], [891, 24, 2384, 26], [892, 20, 2386, 22], [892, 21, 2386, 24], [893, 20, 2388, 22, "onLoadEnd"], [893, 29, 2388, 31], [893, 31, 2388, 33, "onLoadEnd"], [893, 32, 2388, 33], [893, 37, 2388, 39], [894, 22, 2390, 24, "console"], [894, 29, 2390, 31], [894, 30, 2390, 32, "log"], [894, 33, 2390, 35], [894, 34, 2390, 36], [894, 70, 2390, 72], [894, 71, 2390, 73], [895, 20, 2392, 22], [896, 18, 2392, 24], [897, 20, 2392, 24, "fileName"], [897, 28, 2392, 24], [897, 30, 2392, 24, "_jsxFileName"], [897, 42, 2392, 24], [898, 20, 2392, 24, "lineNumber"], [898, 30, 2392, 24], [899, 20, 2392, 24, "columnNumber"], [899, 32, 2392, 24], [900, 18, 2392, 24], [900, 25, 2394, 21], [900, 26, 2394, 22], [900, 41, 2396, 20], [900, 45, 2396, 20, "_jsxDevRuntime"], [900, 59, 2396, 20], [900, 60, 2396, 20, "jsxDEV"], [900, 66, 2396, 20], [900, 68, 2396, 21, "_View"], [900, 73, 2396, 21], [900, 74, 2396, 21, "default"], [900, 81, 2396, 25], [901, 20, 2398, 22, "style"], [901, 25, 2398, 27], [901, 27, 2398, 29], [902, 22, 2400, 24, "position"], [902, 30, 2400, 32], [902, 32, 2400, 34], [902, 42, 2400, 44], [903, 22, 2402, 24, "top"], [903, 25, 2402, 27], [903, 27, 2402, 29], [903, 29, 2402, 31], [904, 22, 2404, 24, "right"], [904, 27, 2404, 29], [904, 29, 2404, 31], [904, 31, 2404, 33], [905, 22, 2406, 24, "backgroundColor"], [905, 37, 2406, 39], [905, 39, 2406, 41], [905, 65, 2406, 67], [906, 22, 2408, 24, "paddingHorizontal"], [906, 39, 2408, 41], [906, 41, 2408, 43], [906, 43, 2408, 45], [907, 22, 2410, 24, "paddingVertical"], [907, 37, 2410, 39], [907, 39, 2410, 41], [907, 40, 2410, 42], [908, 22, 2412, 24, "borderRadius"], [908, 34, 2412, 36], [908, 36, 2412, 38], [908, 38, 2412, 40], [909, 22, 2414, 24, "flexDirection"], [909, 35, 2414, 37], [909, 37, 2414, 39], [909, 42, 2414, 44], [910, 22, 2416, 24, "alignItems"], [910, 32, 2416, 34], [910, 34, 2416, 36], [911, 20, 2418, 22], [911, 21, 2418, 24], [912, 20, 2418, 24, "children"], [912, 28, 2418, 24], [912, 44, 2422, 22], [912, 48, 2422, 22, "_jsxDevRuntime"], [912, 62, 2422, 22], [912, 63, 2422, 22, "jsxDEV"], [912, 69, 2422, 22], [912, 71, 2422, 23, "_lucideReactNative"], [912, 89, 2422, 23], [912, 90, 2422, 23, "Shield"], [912, 96, 2422, 29], [913, 22, 2422, 30, "size"], [913, 26, 2422, 34], [913, 28, 2422, 36], [913, 30, 2422, 39], [914, 22, 2422, 40, "color"], [914, 27, 2422, 45], [914, 29, 2422, 46], [915, 20, 2422, 52], [916, 22, 2422, 52, "fileName"], [916, 30, 2422, 52], [916, 32, 2422, 52, "_jsxFileName"], [916, 44, 2422, 52], [917, 22, 2422, 52, "lineNumber"], [917, 32, 2422, 52], [918, 22, 2422, 52, "columnNumber"], [918, 34, 2422, 52], [919, 20, 2422, 52], [919, 27, 2422, 54], [919, 28, 2422, 55], [919, 43, 2424, 22], [919, 47, 2424, 22, "_jsxDevRuntime"], [919, 61, 2424, 22], [919, 62, 2424, 22, "jsxDEV"], [919, 68, 2424, 22], [919, 70, 2424, 23, "_Text"], [919, 75, 2424, 23], [919, 76, 2424, 23, "default"], [919, 83, 2424, 27], [920, 22, 2424, 28, "style"], [920, 27, 2424, 33], [920, 29, 2424, 35], [921, 24, 2424, 37, "color"], [921, 29, 2424, 42], [921, 31, 2424, 44], [921, 37, 2424, 50], [922, 24, 2424, 52, "fontSize"], [922, 32, 2424, 60], [922, 34, 2424, 62], [922, 36, 2424, 64], [923, 24, 2424, 66, "fontWeight"], [923, 34, 2424, 76], [923, 36, 2424, 78], [923, 41, 2424, 83], [924, 24, 2424, 85, "marginLeft"], [924, 34, 2424, 95], [924, 36, 2424, 97], [925, 22, 2424, 99], [925, 23, 2424, 101], [926, 22, 2424, 101, "children"], [926, 30, 2424, 101], [926, 32, 2424, 102], [927, 20, 2428, 22], [928, 22, 2428, 22, "fileName"], [928, 30, 2428, 22], [928, 32, 2428, 22, "_jsxFileName"], [928, 44, 2428, 22], [929, 22, 2428, 22, "lineNumber"], [929, 32, 2428, 22], [930, 22, 2428, 22, "columnNumber"], [930, 34, 2428, 22], [931, 20, 2428, 22], [931, 27, 2428, 28], [931, 28, 2428, 29], [932, 18, 2428, 29], [933, 20, 2428, 29, "fileName"], [933, 28, 2428, 29], [933, 30, 2428, 29, "_jsxFileName"], [933, 42, 2428, 29], [934, 20, 2428, 29, "lineNumber"], [934, 30, 2428, 29], [935, 20, 2428, 29, "columnNumber"], [935, 32, 2428, 29], [936, 18, 2428, 29], [936, 25, 2430, 26], [936, 26, 2430, 27], [937, 16, 2430, 27], [938, 18, 2430, 27, "fileName"], [938, 26, 2430, 27], [938, 28, 2430, 27, "_jsxFileName"], [938, 40, 2430, 27], [939, 18, 2430, 27, "lineNumber"], [939, 28, 2430, 27], [940, 18, 2430, 27, "columnNumber"], [940, 30, 2430, 27], [941, 16, 2430, 27], [941, 23, 2432, 24], [941, 24, 2432, 25], [941, 39, 2434, 18], [941, 43, 2434, 18, "_jsxDevRuntime"], [941, 57, 2434, 18], [941, 58, 2434, 18, "jsxDEV"], [941, 64, 2434, 18], [941, 66, 2434, 19, "_View"], [941, 71, 2434, 19], [941, 72, 2434, 19, "default"], [941, 79, 2434, 23], [942, 18, 2434, 24, "style"], [942, 23, 2434, 29], [942, 25, 2434, 31], [943, 20, 2434, 33, "padding"], [943, 27, 2434, 40], [943, 29, 2434, 42], [944, 18, 2434, 45], [944, 19, 2434, 47], [945, 18, 2434, 47, "children"], [945, 26, 2434, 47], [945, 42, 2436, 20], [945, 46, 2436, 20, "_jsxDevRuntime"], [945, 60, 2436, 20], [945, 61, 2436, 20, "jsxDEV"], [945, 67, 2436, 20], [945, 69, 2436, 21, "_View"], [945, 74, 2436, 21], [945, 75, 2436, 21, "default"], [945, 82, 2436, 25], [946, 20, 2438, 22, "style"], [946, 25, 2438, 27], [946, 27, 2438, 29], [947, 22, 2440, 24, "flexDirection"], [947, 35, 2440, 37], [947, 37, 2440, 39], [947, 42, 2440, 44], [948, 22, 2442, 24, "alignItems"], [948, 32, 2442, 34], [948, 34, 2442, 36], [948, 42, 2442, 44], [949, 22, 2444, 24, "marginBottom"], [949, 34, 2444, 36], [949, 36, 2444, 38], [950, 20, 2446, 22], [950, 21, 2446, 24], [951, 20, 2446, 24, "children"], [951, 28, 2446, 24], [951, 44, 2450, 22], [951, 48, 2450, 22, "_jsxDevRuntime"], [951, 62, 2450, 22], [951, 63, 2450, 22, "jsxDEV"], [951, 69, 2450, 22], [951, 71, 2450, 23, "_View"], [951, 76, 2450, 23], [951, 77, 2450, 23, "default"], [951, 84, 2450, 27], [952, 22, 2452, 24, "style"], [952, 27, 2452, 29], [952, 29, 2452, 31], [953, 24, 2454, 26, "width"], [953, 29, 2454, 31], [953, 31, 2454, 33], [953, 33, 2454, 35], [954, 24, 2456, 26, "height"], [954, 30, 2456, 32], [954, 32, 2456, 34], [954, 34, 2456, 36], [955, 24, 2458, 26, "borderRadius"], [955, 36, 2458, 38], [955, 38, 2458, 40], [955, 40, 2458, 42], [956, 24, 2460, 26, "backgroundColor"], [956, 39, 2460, 41], [956, 41, 2460, 43], [956, 50, 2460, 52], [957, 24, 2462, 26, "alignItems"], [957, 34, 2462, 36], [957, 36, 2462, 38], [957, 44, 2462, 46], [958, 24, 2464, 26, "justifyContent"], [958, 38, 2464, 40], [958, 40, 2464, 42], [959, 22, 2466, 24], [959, 23, 2466, 26], [960, 22, 2466, 26, "children"], [960, 30, 2466, 26], [960, 45, 2470, 24], [960, 49, 2470, 24, "_jsxDevRuntime"], [960, 63, 2470, 24], [960, 64, 2470, 24, "jsxDEV"], [960, 70, 2470, 24], [960, 72, 2470, 25, "_lucideReactNative"], [960, 90, 2470, 25], [960, 91, 2470, 25, "CheckCircle2"], [960, 103, 2470, 37], [961, 24, 2470, 38, "size"], [961, 28, 2470, 42], [961, 30, 2470, 44], [961, 32, 2470, 47], [962, 24, 2470, 48, "color"], [962, 29, 2470, 53], [962, 31, 2470, 54], [963, 22, 2470, 63], [964, 24, 2470, 63, "fileName"], [964, 32, 2470, 63], [964, 34, 2470, 63, "_jsxFileName"], [964, 46, 2470, 63], [965, 24, 2470, 63, "lineNumber"], [965, 34, 2470, 63], [966, 24, 2470, 63, "columnNumber"], [966, 36, 2470, 63], [967, 22, 2470, 63], [967, 29, 2470, 65], [968, 20, 2470, 66], [969, 22, 2470, 66, "fileName"], [969, 30, 2470, 66], [969, 32, 2470, 66, "_jsxFileName"], [969, 44, 2470, 66], [970, 22, 2470, 66, "lineNumber"], [970, 32, 2470, 66], [971, 22, 2470, 66, "columnNumber"], [971, 34, 2470, 66], [972, 20, 2470, 66], [972, 27, 2472, 28], [972, 28, 2472, 29], [972, 43, 2474, 22], [972, 47, 2474, 22, "_jsxDevRuntime"], [972, 61, 2474, 22], [972, 62, 2474, 22, "jsxDEV"], [972, 68, 2474, 22], [972, 70, 2474, 23, "_View"], [972, 75, 2474, 23], [972, 76, 2474, 23, "default"], [972, 83, 2474, 27], [973, 22, 2474, 28, "style"], [973, 27, 2474, 33], [973, 29, 2474, 35], [974, 24, 2474, 37, "marginLeft"], [974, 34, 2474, 47], [974, 36, 2474, 49], [974, 38, 2474, 51], [975, 24, 2474, 53, "flex"], [975, 28, 2474, 57], [975, 30, 2474, 59], [976, 22, 2474, 61], [976, 23, 2474, 63], [977, 22, 2474, 63, "children"], [977, 30, 2474, 63], [977, 46, 2476, 24], [977, 50, 2476, 24, "_jsxDevRuntime"], [977, 64, 2476, 24], [977, 65, 2476, 24, "jsxDEV"], [977, 71, 2476, 24], [977, 73, 2476, 25, "_Text"], [977, 78, 2476, 25], [977, 79, 2476, 25, "default"], [977, 86, 2476, 29], [978, 24, 2476, 30, "style"], [978, 29, 2476, 35], [978, 31, 2476, 37], [979, 26, 2476, 39, "fontSize"], [979, 34, 2476, 47], [979, 36, 2476, 49], [979, 38, 2476, 51], [980, 26, 2476, 53, "fontWeight"], [980, 36, 2476, 63], [980, 38, 2476, 65], [980, 43, 2476, 70], [981, 26, 2476, 72, "color"], [981, 31, 2476, 77], [981, 33, 2476, 79], [982, 24, 2476, 89], [982, 25, 2476, 91], [983, 24, 2476, 91, "children"], [983, 32, 2476, 91], [983, 34, 2476, 92], [984, 22, 2480, 24], [985, 24, 2480, 24, "fileName"], [985, 32, 2480, 24], [985, 34, 2480, 24, "_jsxFileName"], [985, 46, 2480, 24], [986, 24, 2480, 24, "lineNumber"], [986, 34, 2480, 24], [987, 24, 2480, 24, "columnNumber"], [987, 36, 2480, 24], [988, 22, 2480, 24], [988, 29, 2480, 30], [988, 30, 2480, 31], [988, 45, 2482, 24], [988, 49, 2482, 24, "_jsxDevRuntime"], [988, 63, 2482, 24], [988, 64, 2482, 24, "jsxDEV"], [988, 70, 2482, 24], [988, 72, 2482, 25, "_Text"], [988, 77, 2482, 25], [988, 78, 2482, 25, "default"], [988, 85, 2482, 29], [989, 24, 2482, 30, "style"], [989, 29, 2482, 35], [989, 31, 2482, 37], [990, 26, 2482, 39, "fontSize"], [990, 34, 2482, 47], [990, 36, 2482, 49], [990, 38, 2482, 51], [991, 26, 2482, 53, "color"], [991, 31, 2482, 58], [991, 33, 2482, 60], [991, 42, 2482, 69], [992, 26, 2482, 71, "marginTop"], [992, 35, 2482, 80], [992, 37, 2482, 82], [993, 24, 2482, 84], [993, 25, 2482, 86], [994, 24, 2482, 86, "children"], [994, 32, 2482, 86], [994, 34, 2482, 87], [995, 22, 2486, 24], [996, 24, 2486, 24, "fileName"], [996, 32, 2486, 24], [996, 34, 2486, 24, "_jsxFileName"], [996, 46, 2486, 24], [997, 24, 2486, 24, "lineNumber"], [997, 34, 2486, 24], [998, 24, 2486, 24, "columnNumber"], [998, 36, 2486, 24], [999, 22, 2486, 24], [999, 29, 2486, 30], [999, 30, 2486, 31], [1000, 20, 2486, 31], [1001, 22, 2486, 31, "fileName"], [1001, 30, 2486, 31], [1001, 32, 2486, 31, "_jsxFileName"], [1001, 44, 2486, 31], [1002, 22, 2486, 31, "lineNumber"], [1002, 32, 2486, 31], [1003, 22, 2486, 31, "columnNumber"], [1003, 34, 2486, 31], [1004, 20, 2486, 31], [1004, 27, 2488, 28], [1004, 28, 2488, 29], [1005, 18, 2488, 29], [1006, 20, 2488, 29, "fileName"], [1006, 28, 2488, 29], [1006, 30, 2488, 29, "_jsxFileName"], [1006, 42, 2488, 29], [1007, 20, 2488, 29, "lineNumber"], [1007, 30, 2488, 29], [1008, 20, 2488, 29, "columnNumber"], [1008, 32, 2488, 29], [1009, 18, 2488, 29], [1009, 25, 2490, 26], [1009, 26, 2490, 27], [1009, 41, 2492, 20], [1009, 45, 2492, 20, "_jsxDevRuntime"], [1009, 59, 2492, 20], [1009, 60, 2492, 20, "jsxDEV"], [1009, 66, 2492, 20], [1009, 68, 2492, 21, "_View"], [1009, 73, 2492, 21], [1009, 74, 2492, 21, "default"], [1009, 81, 2492, 25], [1010, 20, 2494, 22, "style"], [1010, 25, 2494, 27], [1010, 27, 2494, 29], [1011, 22, 2496, 24, "backgroundColor"], [1011, 37, 2496, 39], [1011, 39, 2496, 41], [1011, 48, 2496, 50], [1012, 22, 2498, 24, "borderRadius"], [1012, 34, 2498, 36], [1012, 36, 2498, 38], [1012, 38, 2498, 40], [1013, 22, 2500, 24, "padding"], [1013, 29, 2500, 31], [1013, 31, 2500, 33], [1013, 33, 2500, 35], [1014, 22, 2502, 24, "marginBottom"], [1014, 34, 2502, 36], [1014, 36, 2502, 38], [1015, 20, 2504, 22], [1015, 21, 2504, 24], [1016, 20, 2504, 24, "children"], [1016, 28, 2504, 24], [1016, 44, 2508, 22], [1016, 48, 2508, 22, "_jsxDevRuntime"], [1016, 62, 2508, 22], [1016, 63, 2508, 22, "jsxDEV"], [1016, 69, 2508, 22], [1016, 71, 2508, 23, "_Text"], [1016, 76, 2508, 23], [1016, 77, 2508, 23, "default"], [1016, 84, 2508, 27], [1017, 22, 2510, 24, "style"], [1017, 27, 2510, 29], [1017, 29, 2510, 31], [1018, 24, 2512, 26, "fontSize"], [1018, 32, 2512, 34], [1018, 34, 2512, 36], [1018, 36, 2512, 38], [1019, 24, 2514, 26, "fontWeight"], [1019, 34, 2514, 36], [1019, 36, 2514, 38], [1019, 41, 2514, 43], [1020, 24, 2516, 26, "color"], [1020, 29, 2516, 31], [1020, 31, 2516, 33], [1020, 40, 2516, 42], [1021, 24, 2518, 26, "marginBottom"], [1021, 36, 2518, 38], [1021, 38, 2518, 40], [1021, 39, 2518, 41], [1022, 24, 2520, 26, "textTransform"], [1022, 37, 2520, 39], [1022, 39, 2520, 41], [1022, 50, 2520, 52], [1023, 24, 2522, 26, "letterSpacing"], [1023, 37, 2522, 39], [1023, 39, 2522, 41], [1024, 22, 2524, 24], [1024, 23, 2524, 26], [1025, 22, 2524, 26, "children"], [1025, 30, 2524, 26], [1025, 32, 2526, 23], [1026, 20, 2530, 22], [1027, 22, 2530, 22, "fileName"], [1027, 30, 2530, 22], [1027, 32, 2530, 22, "_jsxFileName"], [1027, 44, 2530, 22], [1028, 22, 2530, 22, "lineNumber"], [1028, 32, 2530, 22], [1029, 22, 2530, 22, "columnNumber"], [1029, 34, 2530, 22], [1030, 20, 2530, 22], [1030, 27, 2530, 28], [1030, 28, 2530, 29], [1030, 43, 2532, 22], [1030, 47, 2532, 22, "_jsxDevRuntime"], [1030, 61, 2532, 22], [1030, 62, 2532, 22, "jsxDEV"], [1030, 68, 2532, 22], [1030, 70, 2532, 23, "_View"], [1030, 75, 2532, 23], [1030, 76, 2532, 23, "default"], [1030, 83, 2532, 27], [1031, 22, 2532, 28, "style"], [1031, 27, 2532, 33], [1031, 29, 2532, 35], [1032, 24, 2532, 37, "flexDirection"], [1032, 37, 2532, 50], [1032, 39, 2532, 52], [1032, 44, 2532, 57], [1033, 24, 2532, 59, "marginBottom"], [1033, 36, 2532, 71], [1033, 38, 2532, 73], [1033, 39, 2532, 74], [1034, 24, 2532, 76, "alignItems"], [1034, 34, 2532, 86], [1034, 36, 2532, 88], [1035, 22, 2532, 101], [1035, 23, 2532, 103], [1036, 22, 2532, 103, "children"], [1036, 30, 2532, 103], [1036, 46, 2534, 24], [1036, 50, 2534, 24, "_jsxDevRuntime"], [1036, 64, 2534, 24], [1036, 65, 2534, 24, "jsxDEV"], [1036, 71, 2534, 24], [1036, 73, 2534, 25, "_lucideReactNative"], [1036, 91, 2534, 25], [1036, 92, 2534, 25, "CheckCircle2"], [1036, 104, 2534, 37], [1037, 24, 2534, 38, "size"], [1037, 28, 2534, 42], [1037, 30, 2534, 44], [1037, 32, 2534, 47], [1038, 24, 2534, 48, "color"], [1038, 29, 2534, 53], [1038, 31, 2534, 54], [1038, 40, 2534, 63], [1039, 24, 2534, 64, "style"], [1039, 29, 2534, 69], [1039, 31, 2534, 71], [1040, 26, 2534, 73, "marginTop"], [1040, 35, 2534, 82], [1040, 37, 2534, 84], [1041, 24, 2534, 86], [1042, 22, 2534, 88], [1043, 24, 2534, 88, "fileName"], [1043, 32, 2534, 88], [1043, 34, 2534, 88, "_jsxFileName"], [1043, 46, 2534, 88], [1044, 24, 2534, 88, "lineNumber"], [1044, 34, 2534, 88], [1045, 24, 2534, 88, "columnNumber"], [1045, 36, 2534, 88], [1046, 22, 2534, 88], [1046, 29, 2534, 90], [1046, 30, 2534, 91], [1046, 45, 2536, 24], [1046, 49, 2536, 24, "_jsxDevRuntime"], [1046, 63, 2536, 24], [1046, 64, 2536, 24, "jsxDEV"], [1046, 70, 2536, 24], [1046, 72, 2536, 25, "_Text"], [1046, 77, 2536, 25], [1046, 78, 2536, 25, "default"], [1046, 85, 2536, 29], [1047, 24, 2536, 30, "style"], [1047, 29, 2536, 35], [1047, 31, 2536, 37], [1048, 26, 2536, 39, "fontSize"], [1048, 34, 2536, 47], [1048, 36, 2536, 49], [1048, 38, 2536, 51], [1049, 26, 2536, 53, "color"], [1049, 31, 2536, 58], [1049, 33, 2536, 60], [1049, 42, 2536, 69], [1050, 26, 2536, 71, "marginLeft"], [1050, 36, 2536, 81], [1050, 38, 2536, 83], [1050, 39, 2536, 84], [1051, 26, 2536, 86, "flex"], [1051, 30, 2536, 90], [1051, 32, 2536, 92], [1052, 24, 2536, 94], [1052, 25, 2536, 96], [1053, 24, 2536, 96, "children"], [1053, 32, 2536, 96], [1053, 34, 2536, 97], [1054, 22, 2540, 24], [1055, 24, 2540, 24, "fileName"], [1055, 32, 2540, 24], [1055, 34, 2540, 24, "_jsxFileName"], [1055, 46, 2540, 24], [1056, 24, 2540, 24, "lineNumber"], [1056, 34, 2540, 24], [1057, 24, 2540, 24, "columnNumber"], [1057, 36, 2540, 24], [1058, 22, 2540, 24], [1058, 29, 2540, 30], [1058, 30, 2540, 31], [1059, 20, 2540, 31], [1060, 22, 2540, 31, "fileName"], [1060, 30, 2540, 31], [1060, 32, 2540, 31, "_jsxFileName"], [1060, 44, 2540, 31], [1061, 22, 2540, 31, "lineNumber"], [1061, 32, 2540, 31], [1062, 22, 2540, 31, "columnNumber"], [1062, 34, 2540, 31], [1063, 20, 2540, 31], [1063, 27, 2542, 28], [1063, 28, 2542, 29], [1063, 30, 2544, 23, "cameraResult"], [1063, 42, 2544, 35], [1063, 44, 2544, 37, "challengeCode"], [1063, 57, 2544, 50], [1063, 61, 2544, 54, "cameraResult"], [1063, 73, 2544, 66], [1063, 74, 2544, 67, "challengeCode"], [1063, 87, 2544, 80], [1063, 88, 2544, 81, "trim"], [1063, 92, 2544, 85], [1063, 93, 2544, 86], [1063, 94, 2544, 87], [1063, 111, 2546, 24], [1063, 115, 2546, 24, "_jsxDevRuntime"], [1063, 129, 2546, 24], [1063, 130, 2546, 24, "jsxDEV"], [1063, 136, 2546, 24], [1063, 138, 2546, 25, "_View"], [1063, 143, 2546, 25], [1063, 144, 2546, 25, "default"], [1063, 151, 2546, 29], [1064, 22, 2546, 30, "style"], [1064, 27, 2546, 35], [1064, 29, 2546, 37], [1065, 24, 2546, 39, "flexDirection"], [1065, 37, 2546, 52], [1065, 39, 2546, 54], [1065, 44, 2546, 59], [1066, 24, 2546, 61, "marginBottom"], [1066, 36, 2546, 73], [1066, 38, 2546, 75], [1066, 39, 2546, 76], [1067, 24, 2546, 78, "alignItems"], [1067, 34, 2546, 88], [1067, 36, 2546, 90], [1068, 22, 2546, 103], [1068, 23, 2546, 105], [1069, 22, 2546, 105, "children"], [1069, 30, 2546, 105], [1069, 46, 2548, 26], [1069, 50, 2548, 26, "_jsxDevRuntime"], [1069, 64, 2548, 26], [1069, 65, 2548, 26, "jsxDEV"], [1069, 71, 2548, 26], [1069, 73, 2548, 27, "_lucideReactNative"], [1069, 91, 2548, 27], [1069, 92, 2548, 27, "CheckCircle2"], [1069, 104, 2548, 39], [1070, 24, 2548, 40, "size"], [1070, 28, 2548, 44], [1070, 30, 2548, 46], [1070, 32, 2548, 49], [1071, 24, 2548, 50, "color"], [1071, 29, 2548, 55], [1071, 31, 2548, 56], [1071, 40, 2548, 65], [1072, 24, 2548, 66, "style"], [1072, 29, 2548, 71], [1072, 31, 2548, 73], [1073, 26, 2548, 75, "marginTop"], [1073, 35, 2548, 84], [1073, 37, 2548, 86], [1074, 24, 2548, 88], [1075, 22, 2548, 90], [1076, 24, 2548, 90, "fileName"], [1076, 32, 2548, 90], [1076, 34, 2548, 90, "_jsxFileName"], [1076, 46, 2548, 90], [1077, 24, 2548, 90, "lineNumber"], [1077, 34, 2548, 90], [1078, 24, 2548, 90, "columnNumber"], [1078, 36, 2548, 90], [1079, 22, 2548, 90], [1079, 29, 2548, 92], [1079, 30, 2548, 93], [1079, 45, 2550, 26], [1079, 49, 2550, 26, "_jsxDevRuntime"], [1079, 63, 2550, 26], [1079, 64, 2550, 26, "jsxDEV"], [1079, 70, 2550, 26], [1079, 72, 2550, 27, "_Text"], [1079, 77, 2550, 27], [1079, 78, 2550, 27, "default"], [1079, 85, 2550, 31], [1080, 24, 2550, 32, "style"], [1080, 29, 2550, 37], [1080, 31, 2550, 39], [1081, 26, 2550, 41, "fontSize"], [1081, 34, 2550, 49], [1081, 36, 2550, 51], [1081, 38, 2550, 53], [1082, 26, 2550, 55, "color"], [1082, 31, 2550, 60], [1082, 33, 2550, 62], [1082, 42, 2550, 71], [1083, 26, 2550, 73, "marginLeft"], [1083, 36, 2550, 83], [1083, 38, 2550, 85], [1083, 39, 2550, 86], [1084, 26, 2550, 88, "flex"], [1084, 30, 2550, 92], [1084, 32, 2550, 94], [1085, 24, 2550, 96], [1085, 25, 2550, 98], [1086, 24, 2550, 98, "children"], [1086, 32, 2550, 98], [1086, 34, 2552, 29], [1086, 57, 2552, 52, "cameraResult"], [1086, 69, 2552, 64], [1086, 70, 2552, 65, "challengeCode"], [1086, 83, 2552, 78], [1086, 87, 2552, 82], [1086, 92, 2552, 87], [1087, 22, 2552, 89], [1088, 24, 2552, 89, "fileName"], [1088, 32, 2552, 89], [1088, 34, 2552, 89, "_jsxFileName"], [1088, 46, 2552, 89], [1089, 24, 2552, 89, "lineNumber"], [1089, 34, 2552, 89], [1090, 24, 2552, 89, "columnNumber"], [1090, 36, 2552, 89], [1091, 22, 2552, 89], [1091, 29, 2554, 32], [1091, 30, 2554, 33], [1092, 20, 2554, 33], [1093, 22, 2554, 33, "fileName"], [1093, 30, 2554, 33], [1093, 32, 2554, 33, "_jsxFileName"], [1093, 44, 2554, 33], [1094, 22, 2554, 33, "lineNumber"], [1094, 32, 2554, 33], [1095, 22, 2554, 33, "columnNumber"], [1095, 34, 2554, 33], [1096, 20, 2554, 33], [1096, 27, 2556, 30], [1096, 28, 2558, 23], [1096, 43, 2560, 22], [1096, 47, 2560, 22, "_jsxDevRuntime"], [1096, 61, 2560, 22], [1096, 62, 2560, 22, "jsxDEV"], [1096, 68, 2560, 22], [1096, 70, 2560, 23, "_View"], [1096, 75, 2560, 23], [1096, 76, 2560, 23, "default"], [1096, 83, 2560, 27], [1097, 22, 2560, 28, "style"], [1097, 27, 2560, 33], [1097, 29, 2560, 35], [1098, 24, 2560, 37, "flexDirection"], [1098, 37, 2560, 50], [1098, 39, 2560, 52], [1098, 44, 2560, 57], [1099, 24, 2560, 59, "alignItems"], [1099, 34, 2560, 69], [1099, 36, 2560, 71], [1100, 22, 2560, 84], [1100, 23, 2560, 86], [1101, 22, 2560, 86, "children"], [1101, 30, 2560, 86], [1101, 46, 2562, 24], [1101, 50, 2562, 24, "_jsxDevRuntime"], [1101, 64, 2562, 24], [1101, 65, 2562, 24, "jsxDEV"], [1101, 71, 2562, 24], [1101, 73, 2562, 25, "_lucideReactNative"], [1101, 91, 2562, 25], [1101, 92, 2562, 25, "CheckCircle2"], [1101, 104, 2562, 37], [1102, 24, 2562, 38, "size"], [1102, 28, 2562, 42], [1102, 30, 2562, 44], [1102, 32, 2562, 47], [1103, 24, 2562, 48, "color"], [1103, 29, 2562, 53], [1103, 31, 2562, 54], [1103, 40, 2562, 63], [1104, 24, 2562, 64, "style"], [1104, 29, 2562, 69], [1104, 31, 2562, 71], [1105, 26, 2562, 73, "marginTop"], [1105, 35, 2562, 82], [1105, 37, 2562, 84], [1106, 24, 2562, 86], [1107, 22, 2562, 88], [1108, 24, 2562, 88, "fileName"], [1108, 32, 2562, 88], [1108, 34, 2562, 88, "_jsxFileName"], [1108, 46, 2562, 88], [1109, 24, 2562, 88, "lineNumber"], [1109, 34, 2562, 88], [1110, 24, 2562, 88, "columnNumber"], [1110, 36, 2562, 88], [1111, 22, 2562, 88], [1111, 29, 2562, 90], [1111, 30, 2562, 91], [1111, 45, 2564, 24], [1111, 49, 2564, 24, "_jsxDevRuntime"], [1111, 63, 2564, 24], [1111, 64, 2564, 24, "jsxDEV"], [1111, 70, 2564, 24], [1111, 72, 2564, 25, "_Text"], [1111, 77, 2564, 25], [1111, 78, 2564, 25, "default"], [1111, 85, 2564, 29], [1112, 24, 2564, 30, "style"], [1112, 29, 2564, 35], [1112, 31, 2564, 37], [1113, 26, 2564, 39, "fontSize"], [1113, 34, 2564, 47], [1113, 36, 2564, 49], [1113, 38, 2564, 51], [1114, 26, 2564, 53, "color"], [1114, 31, 2564, 58], [1114, 33, 2564, 60], [1114, 42, 2564, 69], [1115, 26, 2564, 71, "marginLeft"], [1115, 36, 2564, 81], [1115, 38, 2564, 83], [1115, 39, 2564, 84], [1116, 26, 2564, 86, "flex"], [1116, 30, 2564, 90], [1116, 32, 2564, 92], [1117, 24, 2564, 94], [1117, 25, 2564, 96], [1118, 24, 2564, 96, "children"], [1118, 32, 2564, 96], [1118, 34, 2566, 27], [1118, 57, 2566, 50, "distance"], [1118, 65, 2566, 58], [1118, 69, 2566, 62], [1118, 70, 2566, 63], [1119, 22, 2566, 71], [1120, 24, 2566, 71, "fileName"], [1120, 32, 2566, 71], [1120, 34, 2566, 71, "_jsxFileName"], [1120, 46, 2566, 71], [1121, 24, 2566, 71, "lineNumber"], [1121, 34, 2566, 71], [1122, 24, 2566, 71, "columnNumber"], [1122, 36, 2566, 71], [1123, 22, 2566, 71], [1123, 29, 2568, 30], [1123, 30, 2568, 31], [1124, 20, 2568, 31], [1125, 22, 2568, 31, "fileName"], [1125, 30, 2568, 31], [1125, 32, 2568, 31, "_jsxFileName"], [1125, 44, 2568, 31], [1126, 22, 2568, 31, "lineNumber"], [1126, 32, 2568, 31], [1127, 22, 2568, 31, "columnNumber"], [1127, 34, 2568, 31], [1128, 20, 2568, 31], [1128, 27, 2570, 28], [1128, 28, 2570, 29], [1129, 18, 2570, 29], [1130, 20, 2570, 29, "fileName"], [1130, 28, 2570, 29], [1130, 30, 2570, 29, "_jsxFileName"], [1130, 42, 2570, 29], [1131, 20, 2570, 29, "lineNumber"], [1131, 30, 2570, 29], [1132, 20, 2570, 29, "columnNumber"], [1132, 32, 2570, 29], [1133, 18, 2570, 29], [1133, 25, 2572, 26], [1133, 26, 2572, 27], [1133, 41, 2574, 20], [1133, 45, 2574, 20, "_jsxDevRuntime"], [1133, 59, 2574, 20], [1133, 60, 2574, 20, "jsxDEV"], [1133, 66, 2574, 20], [1133, 68, 2574, 21, "_View"], [1133, 73, 2574, 21], [1133, 74, 2574, 21, "default"], [1133, 81, 2574, 25], [1134, 20, 2574, 26, "style"], [1134, 25, 2574, 31], [1134, 27, 2574, 33], [1135, 22, 2574, 35, "flexDirection"], [1135, 35, 2574, 48], [1135, 37, 2574, 50], [1136, 20, 2574, 56], [1136, 21, 2574, 58], [1137, 20, 2574, 58, "children"], [1137, 28, 2574, 58], [1137, 43, 2576, 22], [1137, 47, 2576, 22, "_jsxDevRuntime"], [1137, 61, 2576, 22], [1137, 62, 2576, 22, "jsxDEV"], [1137, 68, 2576, 22], [1137, 70, 2576, 23, "_TouchableOpacity"], [1137, 87, 2576, 23], [1137, 88, 2576, 23, "default"], [1137, 95, 2576, 39], [1138, 22, 2578, 24, "onPress"], [1138, 29, 2578, 31], [1138, 31, 2578, 33, "onPress"], [1138, 32, 2578, 33], [1138, 37, 2578, 39], [1139, 24, 2580, 26, "setCameraResult"], [1139, 39, 2580, 41], [1139, 40, 2580, 42], [1139, 44, 2580, 46], [1139, 45, 2580, 47], [1140, 24, 2582, 26, "setCapturedPhotoUri"], [1140, 43, 2582, 45], [1140, 44, 2582, 46], [1140, 48, 2582, 50], [1140, 49, 2582, 51], [1141, 24, 2584, 26, "handleStartCamera"], [1141, 41, 2584, 43], [1141, 42, 2584, 44], [1141, 43, 2584, 45], [1142, 22, 2586, 24], [1142, 23, 2586, 26], [1143, 22, 2588, 24, "style"], [1143, 27, 2588, 29], [1143, 29, 2588, 31], [1144, 24, 2590, 26, "flex"], [1144, 28, 2590, 30], [1144, 30, 2590, 32], [1144, 31, 2590, 33], [1145, 24, 2592, 26, "backgroundColor"], [1145, 39, 2592, 41], [1145, 41, 2592, 43], [1145, 47, 2592, 49], [1146, 24, 2594, 26, "borderWidth"], [1146, 35, 2594, 37], [1146, 37, 2594, 39], [1146, 38, 2594, 40], [1147, 24, 2596, 26, "borderColor"], [1147, 35, 2596, 37], [1147, 37, 2596, 39], [1147, 46, 2596, 48], [1148, 24, 2598, 26, "borderRadius"], [1148, 36, 2598, 38], [1148, 38, 2598, 40], [1148, 40, 2598, 42], [1149, 24, 2600, 26, "paddingVertical"], [1149, 39, 2600, 41], [1149, 41, 2600, 43], [1149, 43, 2600, 45], [1150, 24, 2602, 26, "paddingHorizontal"], [1150, 41, 2602, 43], [1150, 43, 2602, 45], [1150, 45, 2602, 47], [1151, 24, 2604, 26, "flexDirection"], [1151, 37, 2604, 39], [1151, 39, 2604, 41], [1151, 44, 2604, 46], [1152, 24, 2606, 26, "alignItems"], [1152, 34, 2606, 36], [1152, 36, 2606, 38], [1152, 44, 2606, 46], [1153, 24, 2608, 26, "justifyContent"], [1153, 38, 2608, 40], [1153, 40, 2608, 42], [1154, 22, 2610, 24], [1154, 23, 2610, 26], [1155, 22, 2610, 26, "children"], [1155, 30, 2610, 26], [1155, 46, 2614, 24], [1155, 50, 2614, 24, "_jsxDevRuntime"], [1155, 64, 2614, 24], [1155, 65, 2614, 24, "jsxDEV"], [1155, 71, 2614, 24], [1155, 73, 2614, 25, "_lucideReactNative"], [1155, 91, 2614, 25], [1155, 92, 2614, 25, "Camera"], [1155, 98, 2614, 31], [1156, 24, 2614, 32, "size"], [1156, 28, 2614, 36], [1156, 30, 2614, 38], [1156, 32, 2614, 41], [1157, 24, 2614, 42, "color"], [1157, 29, 2614, 47], [1157, 31, 2614, 48], [1158, 22, 2614, 57], [1159, 24, 2614, 57, "fileName"], [1159, 32, 2614, 57], [1159, 34, 2614, 57, "_jsxFileName"], [1159, 46, 2614, 57], [1160, 24, 2614, 57, "lineNumber"], [1160, 34, 2614, 57], [1161, 24, 2614, 57, "columnNumber"], [1161, 36, 2614, 57], [1162, 22, 2614, 57], [1162, 29, 2614, 59], [1162, 30, 2614, 60], [1162, 45, 2616, 24], [1162, 49, 2616, 24, "_jsxDevRuntime"], [1162, 63, 2616, 24], [1162, 64, 2616, 24, "jsxDEV"], [1162, 70, 2616, 24], [1162, 72, 2616, 25, "_Text"], [1162, 77, 2616, 25], [1162, 78, 2616, 25, "default"], [1162, 85, 2616, 29], [1163, 24, 2616, 30, "style"], [1163, 29, 2616, 35], [1163, 31, 2616, 37], [1164, 26, 2616, 39, "fontSize"], [1164, 34, 2616, 47], [1164, 36, 2616, 49], [1164, 38, 2616, 51], [1165, 26, 2616, 53, "color"], [1165, 31, 2616, 58], [1165, 33, 2616, 60], [1165, 42, 2616, 69], [1166, 26, 2616, 71, "fontWeight"], [1166, 36, 2616, 81], [1166, 38, 2616, 83], [1166, 43, 2616, 88], [1167, 26, 2616, 90, "marginLeft"], [1167, 36, 2616, 100], [1167, 38, 2616, 102], [1168, 24, 2616, 104], [1168, 25, 2616, 106], [1169, 24, 2616, 106, "children"], [1169, 32, 2616, 106], [1169, 34, 2616, 107], [1170, 22, 2620, 24], [1171, 24, 2620, 24, "fileName"], [1171, 32, 2620, 24], [1171, 34, 2620, 24, "_jsxFileName"], [1171, 46, 2620, 24], [1172, 24, 2620, 24, "lineNumber"], [1172, 34, 2620, 24], [1173, 24, 2620, 24, "columnNumber"], [1173, 36, 2620, 24], [1174, 22, 2620, 24], [1174, 29, 2620, 30], [1174, 30, 2620, 31], [1175, 20, 2620, 31], [1176, 22, 2620, 31, "fileName"], [1176, 30, 2620, 31], [1176, 32, 2620, 31, "_jsxFileName"], [1176, 44, 2620, 31], [1177, 22, 2620, 31, "lineNumber"], [1177, 32, 2620, 31], [1178, 22, 2620, 31, "columnNumber"], [1178, 34, 2620, 31], [1179, 20, 2620, 31], [1179, 27, 2622, 40], [1180, 18, 2622, 41], [1181, 20, 2622, 41, "fileName"], [1181, 28, 2622, 41], [1181, 30, 2622, 41, "_jsxFileName"], [1181, 42, 2622, 41], [1182, 20, 2622, 41, "lineNumber"], [1182, 30, 2622, 41], [1183, 20, 2622, 41, "columnNumber"], [1183, 32, 2622, 41], [1184, 18, 2622, 41], [1184, 25, 2624, 26], [1184, 26, 2624, 27], [1185, 16, 2624, 27], [1186, 18, 2624, 27, "fileName"], [1186, 26, 2624, 27], [1186, 28, 2624, 27, "_jsxFileName"], [1186, 40, 2624, 27], [1187, 18, 2624, 27, "lineNumber"], [1187, 28, 2624, 27], [1188, 18, 2624, 27, "columnNumber"], [1188, 30, 2624, 27], [1189, 16, 2624, 27], [1189, 23, 2626, 24], [1189, 24, 2626, 25], [1190, 14, 2626, 25], [1191, 16, 2626, 25, "fileName"], [1191, 24, 2626, 25], [1191, 26, 2626, 25, "_jsxFileName"], [1191, 38, 2626, 25], [1192, 16, 2626, 25, "lineNumber"], [1192, 26, 2626, 25], [1193, 16, 2626, 25, "columnNumber"], [1193, 28, 2626, 25], [1194, 14, 2626, 25], [1194, 21, 2628, 22], [1194, 22, 2628, 23], [1194, 38, 2632, 16], [1194, 42, 2632, 16, "_jsxDevRuntime"], [1194, 56, 2632, 16], [1194, 57, 2632, 16, "jsxDEV"], [1194, 63, 2632, 16], [1194, 65, 2632, 17, "_TouchableOpacity"], [1194, 82, 2632, 17], [1194, 83, 2632, 17, "default"], [1194, 90, 2632, 33], [1195, 16, 2634, 18, "onPress"], [1195, 23, 2634, 25], [1195, 25, 2634, 27, "handleStartCamera"], [1195, 42, 2634, 45], [1196, 16, 2636, 18, "disabled"], [1196, 24, 2636, 26], [1196, 26, 2636, 28, "locationStatus"], [1196, 40, 2636, 42], [1196, 45, 2636, 47], [1196, 55, 2636, 57], [1196, 59, 2636, 61], [1196, 60, 2636, 62, "testingMode"], [1196, 71, 2636, 74], [1197, 16, 2638, 18, "style"], [1197, 21, 2638, 23], [1197, 23, 2638, 25], [1198, 18, 2640, 20, "backgroundColor"], [1198, 33, 2640, 35], [1198, 35, 2642, 22, "locationStatus"], [1198, 49, 2642, 36], [1198, 54, 2642, 41], [1198, 64, 2642, 51], [1198, 68, 2642, 55, "testingMode"], [1198, 79, 2642, 66], [1198, 82, 2642, 69], [1198, 91, 2642, 78], [1198, 94, 2642, 81], [1198, 103, 2642, 90], [1199, 18, 2644, 20, "borderRadius"], [1199, 30, 2644, 32], [1199, 32, 2644, 34], [1199, 34, 2644, 36], [1200, 18, 2646, 20, "padding"], [1200, 25, 2646, 27], [1200, 27, 2646, 29], [1200, 29, 2646, 31], [1201, 18, 2648, 20, "flexDirection"], [1201, 31, 2648, 33], [1201, 33, 2648, 35], [1201, 38, 2648, 40], [1202, 18, 2650, 20, "alignItems"], [1202, 28, 2650, 30], [1202, 30, 2650, 32], [1202, 38, 2650, 40], [1203, 18, 2652, 20, "justifyContent"], [1203, 32, 2652, 34], [1203, 34, 2652, 36], [1204, 16, 2654, 18], [1204, 17, 2654, 20], [1205, 16, 2654, 20, "children"], [1205, 24, 2654, 20], [1205, 40, 2658, 18], [1205, 44, 2658, 18, "_jsxDevRuntime"], [1205, 58, 2658, 18], [1205, 59, 2658, 18, "jsxDEV"], [1205, 65, 2658, 18], [1205, 67, 2658, 19, "_lucideReactNative"], [1205, 85, 2658, 19], [1205, 86, 2658, 19, "Camera"], [1205, 92, 2658, 25], [1206, 18, 2658, 26, "size"], [1206, 22, 2658, 30], [1206, 24, 2658, 32], [1206, 26, 2658, 35], [1207, 18, 2658, 36, "color"], [1207, 23, 2658, 41], [1207, 25, 2658, 42], [1208, 16, 2658, 48], [1209, 18, 2658, 48, "fileName"], [1209, 26, 2658, 48], [1209, 28, 2658, 48, "_jsxFileName"], [1209, 40, 2658, 48], [1210, 18, 2658, 48, "lineNumber"], [1210, 28, 2658, 48], [1211, 18, 2658, 48, "columnNumber"], [1211, 30, 2658, 48], [1212, 16, 2658, 48], [1212, 23, 2658, 50], [1212, 24, 2658, 51], [1212, 39, 2660, 18], [1212, 43, 2660, 18, "_jsxDevRuntime"], [1212, 57, 2660, 18], [1212, 58, 2660, 18, "jsxDEV"], [1212, 64, 2660, 18], [1212, 66, 2660, 19, "_Text"], [1212, 71, 2660, 19], [1212, 72, 2660, 19, "default"], [1212, 79, 2660, 23], [1213, 18, 2662, 20, "style"], [1213, 23, 2662, 25], [1213, 25, 2662, 27], [1214, 20, 2664, 22, "fontSize"], [1214, 28, 2664, 30], [1214, 30, 2664, 32], [1214, 32, 2664, 34], [1215, 20, 2666, 22, "fontWeight"], [1215, 30, 2666, 32], [1215, 32, 2666, 34], [1215, 37, 2666, 39], [1216, 20, 2668, 22, "color"], [1216, 25, 2668, 27], [1216, 27, 2668, 29], [1216, 33, 2668, 35], [1217, 20, 2670, 22, "marginLeft"], [1217, 30, 2670, 32], [1217, 32, 2670, 34], [1218, 18, 2672, 20], [1218, 19, 2672, 22], [1219, 18, 2672, 22, "children"], [1219, 26, 2672, 22], [1219, 28, 2676, 21, "locationStatus"], [1219, 42, 2676, 35], [1219, 47, 2676, 40], [1219, 57, 2676, 50], [1219, 61, 2676, 54, "testingMode"], [1219, 72, 2676, 65], [1219, 75, 2678, 24], [1219, 89, 2678, 38], [1219, 92, 2680, 24], [1220, 16, 2680, 47], [1221, 18, 2680, 47, "fileName"], [1221, 26, 2680, 47], [1221, 28, 2680, 47, "_jsxFileName"], [1221, 40, 2680, 47], [1222, 18, 2680, 47, "lineNumber"], [1222, 28, 2680, 47], [1223, 18, 2680, 47, "columnNumber"], [1223, 30, 2680, 47], [1224, 16, 2680, 47], [1224, 23, 2682, 24], [1224, 24, 2682, 25], [1224, 26, 2684, 19, "testingMode"], [1224, 37, 2684, 30], [1224, 54, 2686, 20], [1224, 58, 2686, 20, "_jsxDevRuntime"], [1224, 72, 2686, 20], [1224, 73, 2686, 20, "jsxDEV"], [1224, 79, 2686, 20], [1224, 81, 2686, 21, "_Text"], [1224, 86, 2686, 21], [1224, 87, 2686, 21, "default"], [1224, 94, 2686, 25], [1225, 18, 2686, 26, "style"], [1225, 23, 2686, 31], [1225, 25, 2686, 33], [1226, 20, 2686, 35, "fontSize"], [1226, 28, 2686, 43], [1226, 30, 2686, 45], [1226, 32, 2686, 47], [1227, 20, 2686, 49, "color"], [1227, 25, 2686, 54], [1227, 27, 2686, 56], [1227, 33, 2686, 62], [1228, 20, 2686, 64, "marginLeft"], [1228, 30, 2686, 74], [1228, 32, 2686, 76], [1229, 18, 2686, 78], [1229, 19, 2686, 80], [1230, 18, 2686, 80, "children"], [1230, 26, 2686, 80], [1230, 28, 2686, 81], [1231, 16, 2686, 85], [1232, 18, 2686, 85, "fileName"], [1232, 26, 2686, 85], [1232, 28, 2686, 85, "_jsxFileName"], [1232, 40, 2686, 85], [1233, 18, 2686, 85, "lineNumber"], [1233, 28, 2686, 85], [1234, 18, 2686, 85, "columnNumber"], [1234, 30, 2686, 85], [1235, 16, 2686, 85], [1235, 23, 2686, 91], [1235, 24, 2688, 19], [1236, 14, 2688, 19], [1237, 16, 2688, 19, "fileName"], [1237, 24, 2688, 19], [1237, 26, 2688, 19, "_jsxFileName"], [1237, 38, 2688, 19], [1238, 16, 2688, 19, "lineNumber"], [1238, 26, 2688, 19], [1239, 16, 2688, 19, "columnNumber"], [1239, 28, 2688, 19], [1240, 14, 2688, 19], [1240, 21, 2690, 34], [1240, 22, 2692, 15], [1241, 12, 2692, 15], [1242, 14, 2692, 15, "fileName"], [1242, 22, 2692, 15], [1242, 24, 2692, 15, "_jsxFileName"], [1242, 36, 2692, 15], [1243, 14, 2692, 15, "lineNumber"], [1243, 24, 2692, 15], [1244, 14, 2692, 15, "columnNumber"], [1244, 26, 2692, 15], [1245, 12, 2692, 15], [1245, 19, 2694, 18], [1245, 20, 2694, 19], [1245, 35, 2697, 12], [1245, 39, 2697, 12, "_jsxDevRuntime"], [1245, 53, 2697, 12], [1245, 54, 2697, 12, "jsxDEV"], [1245, 60, 2697, 12], [1245, 62, 2697, 13, "_View"], [1245, 67, 2697, 13], [1245, 68, 2697, 13, "default"], [1245, 75, 2697, 17], [1246, 14, 2697, 18, "style"], [1246, 19, 2697, 23], [1246, 21, 2697, 25], [1247, 16, 2697, 27, "marginBottom"], [1247, 28, 2697, 39], [1247, 30, 2697, 41], [1248, 14, 2697, 44], [1248, 15, 2697, 46], [1249, 14, 2697, 46, "children"], [1249, 22, 2697, 46], [1249, 38, 2698, 14], [1249, 42, 2698, 14, "_jsxDevRuntime"], [1249, 56, 2698, 14], [1249, 57, 2698, 14, "jsxDEV"], [1249, 63, 2698, 14], [1249, 65, 2698, 15, "_View"], [1249, 70, 2698, 15], [1249, 71, 2698, 15, "default"], [1249, 78, 2698, 19], [1250, 16, 2699, 16, "style"], [1250, 21, 2699, 21], [1250, 23, 2699, 23], [1251, 18, 2700, 18, "flexDirection"], [1251, 31, 2700, 31], [1251, 33, 2700, 33], [1251, 38, 2700, 38], [1252, 18, 2701, 18, "alignItems"], [1252, 28, 2701, 28], [1252, 30, 2701, 30], [1252, 38, 2701, 38], [1253, 18, 2702, 18, "marginBottom"], [1253, 30, 2702, 30], [1253, 32, 2702, 32], [1254, 16, 2703, 16], [1254, 17, 2703, 18], [1255, 16, 2703, 18, "children"], [1255, 24, 2703, 18], [1255, 40, 2705, 16], [1255, 44, 2705, 16, "_jsxDevRuntime"], [1255, 58, 2705, 16], [1255, 59, 2705, 16, "jsxDEV"], [1255, 65, 2705, 16], [1255, 67, 2705, 17, "_View"], [1255, 72, 2705, 17], [1255, 73, 2705, 17, "default"], [1255, 80, 2705, 21], [1256, 18, 2706, 18, "style"], [1256, 23, 2706, 23], [1256, 25, 2706, 25], [1257, 20, 2707, 20, "width"], [1257, 25, 2707, 25], [1257, 27, 2707, 27], [1257, 29, 2707, 29], [1258, 20, 2708, 20, "height"], [1258, 26, 2708, 26], [1258, 28, 2708, 28], [1258, 30, 2708, 30], [1259, 20, 2709, 20, "borderRadius"], [1259, 32, 2709, 32], [1259, 34, 2709, 34], [1259, 36, 2709, 36], [1260, 20, 2710, 20, "backgroundColor"], [1260, 35, 2710, 35], [1260, 37, 2710, 37, "response"], [1260, 45, 2710, 45], [1260, 46, 2710, 46, "trim"], [1260, 50, 2710, 50], [1260, 51, 2710, 51], [1260, 52, 2710, 52], [1260, 55, 2711, 24], [1260, 64, 2711, 33], [1260, 67, 2712, 24, "cameraResult"], [1260, 79, 2712, 36], [1260, 82, 2713, 26], [1260, 91, 2713, 35], [1260, 94, 2714, 26], [1260, 103, 2714, 35], [1261, 20, 2715, 20, "alignItems"], [1261, 30, 2715, 30], [1261, 32, 2715, 32], [1261, 40, 2715, 40], [1262, 20, 2716, 20, "justifyContent"], [1262, 34, 2716, 34], [1262, 36, 2716, 36], [1262, 44, 2716, 44], [1263, 20, 2717, 20, "marginRight"], [1263, 31, 2717, 31], [1263, 33, 2717, 33], [1264, 18, 2718, 18], [1264, 19, 2718, 20], [1265, 18, 2718, 20, "children"], [1265, 26, 2718, 20], [1265, 28, 2720, 19, "response"], [1265, 36, 2720, 27], [1265, 37, 2720, 28, "trim"], [1265, 41, 2720, 32], [1265, 42, 2720, 33], [1265, 43, 2720, 34], [1265, 59, 2721, 20], [1265, 63, 2721, 20, "_jsxDevRuntime"], [1265, 77, 2721, 20], [1265, 78, 2721, 20, "jsxDEV"], [1265, 84, 2721, 20], [1265, 86, 2721, 21, "_lucideReactNative"], [1265, 104, 2721, 21], [1265, 105, 2721, 21, "CheckCircle2"], [1265, 117, 2721, 33], [1266, 20, 2721, 34, "size"], [1266, 24, 2721, 38], [1266, 26, 2721, 40], [1266, 28, 2721, 43], [1267, 20, 2721, 44, "color"], [1267, 25, 2721, 49], [1267, 27, 2721, 50], [1268, 18, 2721, 56], [1269, 20, 2721, 56, "fileName"], [1269, 28, 2721, 56], [1269, 30, 2721, 56, "_jsxFileName"], [1269, 42, 2721, 56], [1270, 20, 2721, 56, "lineNumber"], [1270, 30, 2721, 56], [1271, 20, 2721, 56, "columnNumber"], [1271, 32, 2721, 56], [1272, 18, 2721, 56], [1272, 25, 2721, 58], [1272, 26, 2721, 59], [1272, 42, 2723, 20], [1272, 46, 2723, 20, "_jsxDevRuntime"], [1272, 60, 2723, 20], [1272, 61, 2723, 20, "jsxDEV"], [1272, 67, 2723, 20], [1272, 69, 2723, 21, "_Text"], [1272, 74, 2723, 21], [1272, 75, 2723, 21, "default"], [1272, 82, 2723, 25], [1273, 20, 2724, 22, "style"], [1273, 25, 2724, 27], [1273, 27, 2724, 29], [1274, 22, 2724, 31, "color"], [1274, 27, 2724, 36], [1274, 29, 2724, 38], [1274, 35, 2724, 44], [1275, 22, 2724, 46, "fontSize"], [1275, 30, 2724, 54], [1275, 32, 2724, 56], [1275, 34, 2724, 58], [1276, 22, 2724, 60, "fontWeight"], [1276, 32, 2724, 70], [1276, 34, 2724, 72], [1277, 20, 2724, 78], [1277, 21, 2724, 80], [1278, 20, 2724, 80, "children"], [1278, 28, 2724, 80], [1278, 30, 2725, 21], [1279, 18, 2727, 20], [1280, 20, 2727, 20, "fileName"], [1280, 28, 2727, 20], [1280, 30, 2727, 20, "_jsxFileName"], [1280, 42, 2727, 20], [1281, 20, 2727, 20, "lineNumber"], [1281, 30, 2727, 20], [1282, 20, 2727, 20, "columnNumber"], [1282, 32, 2727, 20], [1283, 18, 2727, 20], [1283, 25, 2727, 26], [1284, 16, 2728, 19], [1285, 18, 2728, 19, "fileName"], [1285, 26, 2728, 19], [1285, 28, 2728, 19, "_jsxFileName"], [1285, 40, 2728, 19], [1286, 18, 2728, 19, "lineNumber"], [1286, 28, 2728, 19], [1287, 18, 2728, 19, "columnNumber"], [1287, 30, 2728, 19], [1288, 16, 2728, 19], [1288, 23, 2729, 22], [1288, 24, 2729, 23], [1288, 39, 2731, 16], [1288, 43, 2731, 16, "_jsxDevRuntime"], [1288, 57, 2731, 16], [1288, 58, 2731, 16, "jsxDEV"], [1288, 64, 2731, 16], [1288, 66, 2731, 17, "_Text"], [1288, 71, 2731, 17], [1288, 72, 2731, 17, "default"], [1288, 79, 2731, 21], [1289, 18, 2732, 18, "style"], [1289, 23, 2732, 23], [1289, 25, 2732, 25], [1290, 20, 2733, 20, "fontSize"], [1290, 28, 2733, 28], [1290, 30, 2733, 30], [1290, 32, 2733, 32], [1291, 20, 2734, 20, "fontWeight"], [1291, 30, 2734, 30], [1291, 32, 2734, 32], [1291, 37, 2734, 37], [1292, 20, 2735, 20, "color"], [1292, 25, 2735, 25], [1292, 27, 2735, 27], [1293, 18, 2736, 18], [1293, 19, 2736, 20], [1294, 18, 2736, 20, "children"], [1294, 26, 2736, 20], [1294, 28, 2737, 17], [1295, 16, 2739, 16], [1296, 18, 2739, 16, "fileName"], [1296, 26, 2739, 16], [1296, 28, 2739, 16, "_jsxFileName"], [1296, 40, 2739, 16], [1297, 18, 2739, 16, "lineNumber"], [1297, 28, 2739, 16], [1298, 18, 2739, 16, "columnNumber"], [1298, 30, 2739, 16], [1299, 16, 2739, 16], [1299, 23, 2739, 22], [1299, 24, 2739, 23], [1300, 14, 2739, 23], [1301, 16, 2739, 23, "fileName"], [1301, 24, 2739, 23], [1301, 26, 2739, 23, "_jsxFileName"], [1301, 38, 2739, 23], [1302, 16, 2739, 23, "lineNumber"], [1302, 26, 2739, 23], [1303, 16, 2739, 23, "columnNumber"], [1303, 28, 2739, 23], [1304, 14, 2739, 23], [1304, 21, 2740, 20], [1304, 22, 2740, 21], [1304, 37, 2742, 14], [1304, 41, 2742, 14, "_jsxDevRuntime"], [1304, 55, 2742, 14], [1304, 56, 2742, 14, "jsxDEV"], [1304, 62, 2742, 14], [1304, 64, 2742, 15, "_Text"], [1304, 69, 2742, 15], [1304, 70, 2742, 15, "default"], [1304, 77, 2742, 19], [1305, 16, 2743, 16, "style"], [1305, 21, 2743, 21], [1305, 23, 2743, 23], [1306, 18, 2744, 18, "fontSize"], [1306, 26, 2744, 26], [1306, 28, 2744, 28], [1306, 30, 2744, 30], [1307, 18, 2745, 18, "color"], [1307, 23, 2745, 23], [1307, 25, 2745, 25], [1307, 34, 2745, 34], [1308, 18, 2746, 18, "marginBottom"], [1308, 30, 2746, 30], [1308, 32, 2746, 32], [1308, 34, 2746, 34], [1309, 18, 2747, 18, "lineHeight"], [1309, 28, 2747, 28], [1309, 30, 2747, 30], [1310, 16, 2748, 16], [1310, 17, 2748, 18], [1311, 16, 2748, 18, "children"], [1311, 24, 2748, 18], [1311, 26, 2750, 17], [1312, 14, 2750, 109], [1313, 16, 2750, 109, "fileName"], [1313, 24, 2750, 109], [1313, 26, 2750, 109, "_jsxFileName"], [1313, 38, 2750, 109], [1314, 16, 2750, 109, "lineNumber"], [1314, 26, 2750, 109], [1315, 16, 2750, 109, "columnNumber"], [1315, 28, 2750, 109], [1316, 14, 2750, 109], [1316, 21, 2751, 20], [1316, 22, 2751, 21], [1316, 37, 2753, 14], [1316, 41, 2753, 14, "_jsxDevRuntime"], [1316, 55, 2753, 14], [1316, 56, 2753, 14, "jsxDEV"], [1316, 62, 2753, 14], [1316, 64, 2753, 15, "_View"], [1316, 69, 2753, 15], [1316, 70, 2753, 15, "default"], [1316, 77, 2753, 19], [1317, 16, 2754, 16, "style"], [1317, 21, 2754, 21], [1317, 23, 2754, 23], [1318, 18, 2755, 18, "backgroundColor"], [1318, 33, 2755, 33], [1318, 35, 2755, 35], [1318, 41, 2755, 41], [1319, 18, 2756, 18, "borderRadius"], [1319, 30, 2756, 30], [1319, 32, 2756, 32], [1319, 34, 2756, 34], [1320, 18, 2757, 18, "borderWidth"], [1320, 29, 2757, 29], [1320, 31, 2757, 31], [1320, 32, 2757, 32], [1321, 18, 2758, 18, "borderColor"], [1321, 29, 2758, 29], [1321, 31, 2758, 31], [1321, 40, 2758, 40], [1322, 18, 2759, 18, "padding"], [1322, 25, 2759, 25], [1322, 27, 2759, 27], [1323, 16, 2760, 16], [1323, 17, 2760, 18], [1324, 16, 2760, 18, "children"], [1324, 24, 2760, 18], [1324, 40, 2762, 16], [1324, 44, 2762, 16, "_jsxDevRuntime"], [1324, 58, 2762, 16], [1324, 59, 2762, 16, "jsxDEV"], [1324, 65, 2762, 16], [1324, 67, 2762, 17, "_View"], [1324, 72, 2762, 17], [1324, 73, 2762, 17, "default"], [1324, 80, 2762, 21], [1325, 18, 2763, 18, "style"], [1325, 23, 2763, 23], [1325, 25, 2763, 25], [1326, 20, 2764, 20, "flexDirection"], [1326, 33, 2764, 33], [1326, 35, 2764, 35], [1326, 40, 2764, 40], [1327, 20, 2765, 20, "alignItems"], [1327, 30, 2765, 30], [1327, 32, 2765, 32], [1327, 44, 2765, 44], [1328, 20, 2766, 20, "padding"], [1328, 27, 2766, 27], [1328, 29, 2766, 29], [1329, 18, 2767, 18], [1329, 19, 2767, 20], [1330, 18, 2767, 20, "children"], [1330, 26, 2767, 20], [1330, 42, 2769, 18], [1330, 46, 2769, 18, "_jsxDevRuntime"], [1330, 60, 2769, 18], [1330, 61, 2769, 18, "jsxDEV"], [1330, 67, 2769, 18], [1330, 69, 2769, 19, "_lucideReactNative"], [1330, 87, 2769, 19], [1330, 88, 2769, 19, "MessageCircle"], [1330, 101, 2769, 32], [1331, 20, 2770, 20, "size"], [1331, 24, 2770, 24], [1331, 26, 2770, 26], [1331, 28, 2770, 29], [1332, 20, 2771, 20, "color"], [1332, 25, 2771, 25], [1332, 27, 2771, 26], [1332, 36, 2771, 35], [1333, 20, 2772, 20, "style"], [1333, 25, 2772, 25], [1333, 27, 2772, 27], [1334, 22, 2772, 29, "marginTop"], [1334, 31, 2772, 38], [1334, 33, 2772, 40], [1334, 34, 2772, 41], [1335, 22, 2772, 43, "marginRight"], [1335, 33, 2772, 54], [1335, 35, 2772, 56], [1336, 20, 2772, 59], [1337, 18, 2772, 61], [1338, 20, 2772, 61, "fileName"], [1338, 28, 2772, 61], [1338, 30, 2772, 61, "_jsxFileName"], [1338, 42, 2772, 61], [1339, 20, 2772, 61, "lineNumber"], [1339, 30, 2772, 61], [1340, 20, 2772, 61, "columnNumber"], [1340, 32, 2772, 61], [1341, 18, 2772, 61], [1341, 25, 2773, 19], [1341, 26, 2773, 20], [1341, 41, 2774, 18], [1341, 45, 2774, 18, "_jsxDevRuntime"], [1341, 59, 2774, 18], [1341, 60, 2774, 18, "jsxDEV"], [1341, 66, 2774, 18], [1341, 68, 2774, 19, "_TextInput"], [1341, 78, 2774, 19], [1341, 79, 2774, 19, "default"], [1341, 86, 2774, 28], [1342, 20, 2775, 20, "style"], [1342, 25, 2775, 25], [1342, 27, 2775, 27], [1343, 22, 2776, 22, "flex"], [1343, 26, 2776, 26], [1343, 28, 2776, 28], [1343, 29, 2776, 29], [1344, 22, 2777, 22, "fontSize"], [1344, 30, 2777, 30], [1344, 32, 2777, 32], [1344, 34, 2777, 34], [1345, 22, 2778, 22, "color"], [1345, 27, 2778, 27], [1345, 29, 2778, 29], [1345, 38, 2778, 38], [1346, 22, 2779, 22, "minHeight"], [1346, 31, 2779, 31], [1346, 33, 2779, 33], [1346, 36, 2779, 36], [1347, 22, 2780, 22, "textAlignVertical"], [1347, 39, 2780, 39], [1347, 41, 2780, 41], [1348, 20, 2781, 20], [1348, 21, 2781, 22], [1349, 20, 2782, 20, "placeholder"], [1349, 31, 2782, 31], [1349, 33, 2782, 32], [1349, 88, 2782, 87], [1350, 20, 2783, 20, "placeholderTextColor"], [1350, 40, 2783, 40], [1350, 42, 2783, 41], [1350, 51, 2783, 50], [1351, 20, 2784, 20, "value"], [1351, 25, 2784, 25], [1351, 27, 2784, 27, "response"], [1351, 35, 2784, 36], [1352, 20, 2785, 20, "onChangeText"], [1352, 32, 2785, 32], [1352, 34, 2785, 34, "setResponse"], [1352, 45, 2785, 46], [1353, 20, 2786, 20, "multiline"], [1353, 29, 2786, 29], [1354, 20, 2787, 20, "max<PERSON><PERSON><PERSON>"], [1354, 29, 2787, 29], [1354, 31, 2787, 31], [1355, 18, 2787, 35], [1356, 20, 2787, 35, "fileName"], [1356, 28, 2787, 35], [1356, 30, 2787, 35, "_jsxFileName"], [1356, 42, 2787, 35], [1357, 20, 2787, 35, "lineNumber"], [1357, 30, 2787, 35], [1358, 20, 2787, 35, "columnNumber"], [1358, 32, 2787, 35], [1359, 18, 2787, 35], [1359, 25, 2788, 19], [1359, 26, 2788, 20], [1360, 16, 2788, 20], [1361, 18, 2788, 20, "fileName"], [1361, 26, 2788, 20], [1361, 28, 2788, 20, "_jsxFileName"], [1361, 40, 2788, 20], [1362, 18, 2788, 20, "lineNumber"], [1362, 28, 2788, 20], [1363, 18, 2788, 20, "columnNumber"], [1363, 30, 2788, 20], [1364, 16, 2788, 20], [1364, 23, 2789, 22], [1364, 24, 2789, 23], [1364, 39, 2791, 16], [1364, 43, 2791, 16, "_jsxDevRuntime"], [1364, 57, 2791, 16], [1364, 58, 2791, 16, "jsxDEV"], [1364, 64, 2791, 16], [1364, 66, 2791, 17, "_View"], [1364, 71, 2791, 17], [1364, 72, 2791, 17, "default"], [1364, 79, 2791, 21], [1365, 18, 2792, 18, "style"], [1365, 23, 2792, 23], [1365, 25, 2792, 25], [1366, 20, 2793, 20, "flexDirection"], [1366, 33, 2793, 33], [1366, 35, 2793, 35], [1366, 40, 2793, 40], [1367, 20, 2794, 20, "justifyContent"], [1367, 34, 2794, 34], [1367, 36, 2794, 36], [1367, 51, 2794, 51], [1368, 20, 2795, 20, "alignItems"], [1368, 30, 2795, 30], [1368, 32, 2795, 32], [1368, 40, 2795, 40], [1369, 20, 2796, 20, "paddingHorizontal"], [1369, 37, 2796, 37], [1369, 39, 2796, 39], [1369, 41, 2796, 41], [1370, 20, 2797, 20, "paddingBottom"], [1370, 33, 2797, 33], [1370, 35, 2797, 35], [1371, 18, 2798, 18], [1371, 19, 2798, 20], [1372, 18, 2798, 20, "children"], [1372, 26, 2798, 20], [1372, 42, 2800, 18], [1372, 46, 2800, 18, "_jsxDevRuntime"], [1372, 60, 2800, 18], [1372, 61, 2800, 18, "jsxDEV"], [1372, 67, 2800, 18], [1372, 69, 2800, 19, "_Text"], [1372, 74, 2800, 19], [1372, 75, 2800, 19, "default"], [1372, 82, 2800, 23], [1373, 20, 2800, 24, "style"], [1373, 25, 2800, 29], [1373, 27, 2800, 31], [1374, 22, 2800, 33, "fontSize"], [1374, 30, 2800, 41], [1374, 32, 2800, 43], [1374, 34, 2800, 45], [1375, 22, 2800, 47, "color"], [1375, 27, 2800, 52], [1375, 29, 2800, 54], [1376, 20, 2800, 64], [1376, 21, 2800, 66], [1377, 20, 2800, 66, "children"], [1377, 28, 2800, 66], [1377, 30, 2800, 67], [1378, 18, 2802, 18], [1379, 20, 2802, 18, "fileName"], [1379, 28, 2802, 18], [1379, 30, 2802, 18, "_jsxFileName"], [1379, 42, 2802, 18], [1380, 20, 2802, 18, "lineNumber"], [1380, 30, 2802, 18], [1381, 20, 2802, 18, "columnNumber"], [1381, 32, 2802, 18], [1382, 18, 2802, 18], [1382, 25, 2802, 24], [1382, 26, 2802, 25], [1382, 41, 2803, 18], [1382, 45, 2803, 18, "_jsxDevRuntime"], [1382, 59, 2803, 18], [1382, 60, 2803, 18, "jsxDEV"], [1382, 66, 2803, 18], [1382, 68, 2803, 19, "_Text"], [1382, 73, 2803, 19], [1382, 74, 2803, 19, "default"], [1382, 81, 2803, 23], [1383, 20, 2803, 24, "style"], [1383, 25, 2803, 29], [1383, 27, 2803, 31], [1384, 22, 2803, 33, "fontSize"], [1384, 30, 2803, 41], [1384, 32, 2803, 43], [1384, 34, 2803, 45], [1385, 22, 2803, 47, "color"], [1385, 27, 2803, 52], [1385, 29, 2803, 54], [1386, 20, 2803, 64], [1386, 21, 2803, 66], [1387, 20, 2803, 66, "children"], [1387, 28, 2803, 66], [1387, 30, 2804, 21], [1387, 33, 2804, 24, "response"], [1387, 41, 2804, 32], [1387, 42, 2804, 33, "length"], [1387, 48, 2804, 39], [1388, 18, 2804, 45], [1389, 20, 2804, 45, "fileName"], [1389, 28, 2804, 45], [1389, 30, 2804, 45, "_jsxFileName"], [1389, 42, 2804, 45], [1390, 20, 2804, 45, "lineNumber"], [1390, 30, 2804, 45], [1391, 20, 2804, 45, "columnNumber"], [1391, 32, 2804, 45], [1392, 18, 2804, 45], [1392, 25, 2805, 24], [1392, 26, 2805, 25], [1393, 16, 2805, 25], [1394, 18, 2805, 25, "fileName"], [1394, 26, 2805, 25], [1394, 28, 2805, 25, "_jsxFileName"], [1394, 40, 2805, 25], [1395, 18, 2805, 25, "lineNumber"], [1395, 28, 2805, 25], [1396, 18, 2805, 25, "columnNumber"], [1396, 30, 2805, 25], [1397, 16, 2805, 25], [1397, 23, 2806, 22], [1397, 24, 2806, 23], [1398, 14, 2806, 23], [1399, 16, 2806, 23, "fileName"], [1399, 24, 2806, 23], [1399, 26, 2806, 23, "_jsxFileName"], [1399, 38, 2806, 23], [1400, 16, 2806, 23, "lineNumber"], [1400, 26, 2806, 23], [1401, 16, 2806, 23, "columnNumber"], [1401, 28, 2806, 23], [1402, 14, 2806, 23], [1402, 21, 2807, 20], [1402, 22, 2807, 21], [1403, 12, 2807, 21], [1404, 14, 2807, 21, "fileName"], [1404, 22, 2807, 21], [1404, 24, 2807, 21, "_jsxFileName"], [1404, 36, 2807, 21], [1405, 14, 2807, 21, "lineNumber"], [1405, 24, 2807, 21], [1406, 14, 2807, 21, "columnNumber"], [1406, 26, 2807, 21], [1407, 12, 2807, 21], [1407, 19, 2808, 18], [1407, 20, 2808, 19], [1407, 35, 2813, 12], [1407, 39, 2813, 12, "_jsxDevRuntime"], [1407, 53, 2813, 12], [1407, 54, 2813, 12, "jsxDEV"], [1407, 60, 2813, 12], [1407, 62, 2813, 13, "_View"], [1407, 67, 2813, 13], [1407, 68, 2813, 13, "default"], [1407, 75, 2813, 17], [1408, 14, 2817, 14, "style"], [1408, 19, 2817, 19], [1408, 21, 2817, 21], [1409, 16, 2821, 16, "backgroundColor"], [1409, 31, 2821, 31], [1409, 33, 2821, 33], [1409, 42, 2821, 42], [1410, 16, 2825, 16, "borderRadius"], [1410, 28, 2825, 28], [1410, 30, 2825, 30], [1410, 32, 2825, 32], [1411, 16, 2829, 16, "padding"], [1411, 23, 2829, 23], [1411, 25, 2829, 25], [1411, 27, 2829, 27], [1412, 16, 2833, 16, "marginBottom"], [1412, 28, 2833, 28], [1412, 30, 2833, 30], [1413, 14, 2837, 14], [1413, 15, 2837, 16], [1414, 14, 2837, 16, "children"], [1414, 22, 2837, 16], [1414, 38, 2845, 14], [1414, 42, 2845, 14, "_jsxDevRuntime"], [1414, 56, 2845, 14], [1414, 57, 2845, 14, "jsxDEV"], [1414, 63, 2845, 14], [1414, 65, 2845, 15, "_Text"], [1414, 70, 2845, 15], [1414, 71, 2845, 15, "default"], [1414, 78, 2845, 19], [1415, 16, 2849, 16, "style"], [1415, 21, 2849, 21], [1415, 23, 2849, 23], [1416, 18, 2853, 18, "fontSize"], [1416, 26, 2853, 26], [1416, 28, 2853, 28], [1416, 30, 2853, 30], [1417, 18, 2857, 18, "fontWeight"], [1417, 28, 2857, 28], [1417, 30, 2857, 30], [1417, 35, 2857, 35], [1418, 18, 2861, 18, "color"], [1418, 23, 2861, 23], [1418, 25, 2861, 25], [1418, 34, 2861, 34], [1419, 18, 2865, 18, "marginBottom"], [1419, 30, 2865, 30], [1419, 32, 2865, 32], [1420, 16, 2869, 16], [1420, 17, 2869, 18], [1421, 16, 2869, 18, "children"], [1421, 24, 2869, 18], [1421, 26, 2873, 15], [1422, 14, 2881, 14], [1423, 16, 2881, 14, "fileName"], [1423, 24, 2881, 14], [1423, 26, 2881, 14, "_jsxFileName"], [1423, 38, 2881, 14], [1424, 16, 2881, 14, "lineNumber"], [1424, 26, 2881, 14], [1425, 16, 2881, 14, "columnNumber"], [1425, 28, 2881, 14], [1426, 14, 2881, 14], [1426, 21, 2881, 20], [1426, 22, 2881, 21], [1426, 37, 2885, 14], [1426, 41, 2885, 14, "_jsxDevRuntime"], [1426, 55, 2885, 14], [1426, 56, 2885, 14, "jsxDEV"], [1426, 62, 2885, 14], [1426, 64, 2885, 15, "_Text"], [1426, 69, 2885, 15], [1426, 70, 2885, 15, "default"], [1426, 77, 2885, 19], [1427, 16, 2889, 16, "style"], [1427, 21, 2889, 21], [1427, 23, 2889, 23], [1428, 18, 2893, 18, "fontSize"], [1428, 26, 2893, 26], [1428, 28, 2893, 28], [1428, 30, 2893, 30], [1429, 18, 2897, 18, "color"], [1429, 23, 2897, 23], [1429, 25, 2897, 25], [1429, 34, 2897, 34], [1430, 18, 2901, 18, "lineHeight"], [1430, 28, 2901, 28], [1430, 30, 2901, 30], [1431, 16, 2905, 16], [1431, 17, 2905, 18], [1432, 16, 2905, 18, "children"], [1432, 24, 2905, 18], [1432, 26, 2913, 17], [1433, 14, 2913, 186], [1434, 16, 2913, 186, "fileName"], [1434, 24, 2913, 186], [1434, 26, 2913, 186, "_jsxFileName"], [1434, 38, 2913, 186], [1435, 16, 2913, 186, "lineNumber"], [1435, 26, 2913, 186], [1436, 16, 2913, 186, "columnNumber"], [1436, 28, 2913, 186], [1437, 14, 2913, 186], [1437, 21, 2917, 20], [1437, 22, 2917, 21], [1438, 12, 2917, 21], [1439, 14, 2917, 21, "fileName"], [1439, 22, 2917, 21], [1439, 24, 2917, 21, "_jsxFileName"], [1439, 36, 2917, 21], [1440, 14, 2917, 21, "lineNumber"], [1440, 24, 2917, 21], [1441, 14, 2917, 21, "columnNumber"], [1441, 26, 2917, 21], [1442, 12, 2917, 21], [1442, 19, 2921, 18], [1442, 20, 2921, 19], [1443, 10, 2921, 19], [1444, 12, 2921, 19, "fileName"], [1444, 20, 2921, 19], [1444, 22, 2921, 19, "_jsxFileName"], [1444, 34, 2921, 19], [1445, 12, 2921, 19, "lineNumber"], [1445, 22, 2921, 19], [1446, 12, 2921, 19, "columnNumber"], [1446, 24, 2921, 19], [1447, 10, 2921, 19], [1447, 17, 2925, 16], [1448, 8, 2925, 17], [1449, 10, 2925, 17, "fileName"], [1449, 18, 2925, 17], [1449, 20, 2925, 17, "_jsxFileName"], [1449, 32, 2925, 17], [1450, 10, 2925, 17, "lineNumber"], [1450, 20, 2925, 17], [1451, 10, 2925, 17, "columnNumber"], [1451, 22, 2925, 17], [1452, 8, 2925, 17], [1452, 15, 2929, 20], [1452, 16, 2929, 21], [1452, 31, 2937, 8], [1452, 35, 2937, 8, "_jsxDevRuntime"], [1452, 49, 2937, 8], [1452, 50, 2937, 8, "jsxDEV"], [1452, 56, 2937, 8], [1452, 58, 2937, 9, "_View"], [1452, 63, 2937, 9], [1452, 64, 2937, 9, "default"], [1452, 71, 2937, 13], [1453, 10, 2941, 10, "style"], [1453, 15, 2941, 15], [1453, 17, 2941, 17], [1454, 12, 2945, 12, "position"], [1454, 20, 2945, 20], [1454, 22, 2945, 22], [1454, 32, 2945, 32], [1455, 12, 2949, 12, "bottom"], [1455, 18, 2949, 18], [1455, 20, 2949, 20], [1455, 21, 2949, 21], [1456, 12, 2953, 12, "left"], [1456, 16, 2953, 16], [1456, 18, 2953, 18], [1456, 19, 2953, 19], [1457, 12, 2957, 12, "right"], [1457, 17, 2957, 17], [1457, 19, 2957, 19], [1457, 20, 2957, 20], [1458, 12, 2961, 12, "backgroundColor"], [1458, 27, 2961, 27], [1458, 29, 2961, 29], [1458, 35, 2961, 35], [1459, 12, 2965, 12, "borderTopWidth"], [1459, 26, 2965, 26], [1459, 28, 2965, 28], [1459, 29, 2965, 29], [1460, 12, 2969, 12, "borderTopColor"], [1460, 26, 2969, 26], [1460, 28, 2969, 28], [1460, 37, 2969, 37], [1461, 12, 2973, 12, "padding"], [1461, 19, 2973, 19], [1461, 21, 2973, 21], [1461, 23, 2973, 23], [1462, 12, 2977, 12, "paddingBottom"], [1462, 25, 2977, 25], [1462, 27, 2977, 27, "insets"], [1462, 33, 2977, 33], [1462, 34, 2977, 34, "bottom"], [1462, 40, 2977, 40], [1462, 43, 2977, 43], [1463, 10, 2981, 10], [1463, 11, 2981, 12], [1464, 10, 2981, 12, "children"], [1464, 18, 2981, 12], [1464, 33, 2989, 10], [1464, 37, 2989, 10, "_jsxDevRuntime"], [1464, 51, 2989, 10], [1464, 52, 2989, 10, "jsxDEV"], [1464, 58, 2989, 10], [1464, 60, 2989, 11, "_TouchableOpacity"], [1464, 77, 2989, 11], [1464, 78, 2989, 11, "default"], [1464, 85, 2989, 27], [1465, 12, 2993, 12, "onPress"], [1465, 19, 2993, 19], [1465, 21, 2993, 21, "submitResponse"], [1465, 35, 2993, 36], [1466, 12, 2997, 12, "disabled"], [1466, 20, 2997, 20], [1466, 22, 2997, 22, "submitting"], [1466, 32, 2997, 32], [1466, 36, 2997, 36], [1466, 37, 2997, 37, "cameraResult"], [1466, 49, 2997, 49], [1466, 53, 2997, 53], [1466, 54, 2997, 54, "response"], [1466, 62, 2997, 62], [1466, 63, 2997, 63, "trim"], [1466, 67, 2997, 67], [1466, 68, 2997, 68], [1466, 69, 2997, 70], [1467, 12, 3001, 12, "style"], [1467, 17, 3001, 17], [1467, 19, 3001, 19], [1468, 14, 3005, 14, "backgroundColor"], [1468, 29, 3005, 29], [1468, 31, 3009, 16, "submitting"], [1468, 41, 3009, 26], [1468, 45, 3009, 30], [1468, 46, 3009, 31, "cameraResult"], [1468, 58, 3009, 43], [1468, 62, 3009, 47], [1468, 63, 3009, 48, "response"], [1468, 71, 3009, 56], [1468, 72, 3009, 57, "trim"], [1468, 76, 3009, 61], [1468, 77, 3009, 62], [1468, 78, 3009, 63], [1468, 81, 3013, 20], [1468, 90, 3013, 29], [1468, 93, 3017, 20], [1468, 102, 3017, 29], [1469, 14, 3021, 14, "borderRadius"], [1469, 26, 3021, 26], [1469, 28, 3021, 28], [1469, 30, 3021, 30], [1470, 14, 3025, 14, "padding"], [1470, 21, 3025, 21], [1470, 23, 3025, 23], [1470, 25, 3025, 25], [1471, 14, 3029, 14, "flexDirection"], [1471, 27, 3029, 27], [1471, 29, 3029, 29], [1471, 34, 3029, 34], [1472, 14, 3033, 14, "alignItems"], [1472, 24, 3033, 24], [1472, 26, 3033, 26], [1472, 34, 3033, 34], [1473, 14, 3037, 14, "justifyContent"], [1473, 28, 3037, 28], [1473, 30, 3037, 30], [1474, 12, 3041, 12], [1474, 13, 3041, 14], [1475, 12, 3041, 14, "children"], [1475, 20, 3041, 14], [1475, 22, 3049, 13, "submitting"], [1475, 32, 3049, 23], [1475, 48, 3053, 14], [1475, 52, 3053, 14, "_jsxDevRuntime"], [1475, 66, 3053, 14], [1475, 67, 3053, 14, "jsxDEV"], [1475, 73, 3053, 14], [1475, 75, 3053, 14, "_jsxDevRuntime"], [1475, 89, 3053, 14], [1475, 90, 3053, 14, "Fragment"], [1475, 98, 3053, 14], [1476, 14, 3053, 14, "children"], [1476, 22, 3053, 14], [1476, 38, 3057, 16], [1476, 42, 3057, 16, "_jsxDevRuntime"], [1476, 56, 3057, 16], [1476, 57, 3057, 16, "jsxDEV"], [1476, 63, 3057, 16], [1476, 65, 3057, 17, "_View"], [1476, 70, 3057, 17], [1476, 71, 3057, 17, "default"], [1476, 78, 3057, 21], [1477, 16, 3061, 18, "style"], [1477, 21, 3061, 23], [1477, 23, 3061, 25], [1478, 18, 3065, 20, "width"], [1478, 23, 3065, 25], [1478, 25, 3065, 27], [1478, 27, 3065, 29], [1479, 18, 3069, 20, "height"], [1479, 24, 3069, 26], [1479, 26, 3069, 28], [1479, 28, 3069, 30], [1480, 18, 3073, 20, "borderRadius"], [1480, 30, 3073, 32], [1480, 32, 3073, 34], [1480, 33, 3073, 35], [1481, 18, 3077, 20, "borderWidth"], [1481, 29, 3077, 31], [1481, 31, 3077, 33], [1481, 32, 3077, 34], [1482, 18, 3081, 20, "borderColor"], [1482, 29, 3081, 31], [1482, 31, 3081, 33], [1482, 37, 3081, 39], [1483, 18, 3085, 20, "borderTopColor"], [1483, 32, 3085, 34], [1483, 34, 3085, 36], [1483, 47, 3085, 49], [1484, 18, 3089, 20, "marginRight"], [1484, 29, 3089, 31], [1484, 31, 3089, 33], [1485, 16, 3093, 18], [1486, 14, 3093, 20], [1487, 16, 3093, 20, "fileName"], [1487, 24, 3093, 20], [1487, 26, 3093, 20, "_jsxFileName"], [1487, 38, 3093, 20], [1488, 16, 3093, 20, "lineNumber"], [1488, 26, 3093, 20], [1489, 16, 3093, 20, "columnNumber"], [1489, 28, 3093, 20], [1490, 14, 3093, 20], [1490, 21, 3097, 17], [1490, 22, 3097, 18], [1490, 37, 3101, 16], [1490, 41, 3101, 16, "_jsxDevRuntime"], [1490, 55, 3101, 16], [1490, 56, 3101, 16, "jsxDEV"], [1490, 62, 3101, 16], [1490, 64, 3101, 17, "_Text"], [1490, 69, 3101, 17], [1490, 70, 3101, 17, "default"], [1490, 77, 3101, 21], [1491, 16, 3105, 18, "style"], [1491, 21, 3105, 23], [1491, 23, 3105, 25], [1492, 18, 3105, 27, "fontSize"], [1492, 26, 3105, 35], [1492, 28, 3105, 37], [1492, 30, 3105, 39], [1493, 18, 3105, 41, "fontWeight"], [1493, 28, 3105, 51], [1493, 30, 3105, 53], [1493, 35, 3105, 58], [1494, 18, 3105, 60, "color"], [1494, 23, 3105, 65], [1494, 25, 3105, 67], [1495, 16, 3105, 74], [1495, 17, 3105, 76], [1496, 16, 3105, 76, "children"], [1496, 24, 3105, 76], [1496, 26, 3109, 17], [1497, 14, 3117, 16], [1498, 16, 3117, 16, "fileName"], [1498, 24, 3117, 16], [1498, 26, 3117, 16, "_jsxFileName"], [1498, 38, 3117, 16], [1499, 16, 3117, 16, "lineNumber"], [1499, 26, 3117, 16], [1500, 16, 3117, 16, "columnNumber"], [1500, 28, 3117, 16], [1501, 14, 3117, 16], [1501, 21, 3117, 22], [1501, 22, 3117, 23], [1502, 12, 3117, 23], [1502, 27, 3121, 16], [1502, 28, 3121, 17], [1502, 44, 3129, 14], [1502, 48, 3129, 14, "_jsxDevRuntime"], [1502, 62, 3129, 14], [1502, 63, 3129, 14, "jsxDEV"], [1502, 69, 3129, 14], [1502, 71, 3129, 14, "_jsxDevRuntime"], [1502, 85, 3129, 14], [1502, 86, 3129, 14, "Fragment"], [1502, 94, 3129, 14], [1503, 14, 3129, 14, "children"], [1503, 22, 3129, 14], [1503, 38, 3133, 16], [1503, 42, 3133, 16, "_jsxDevRuntime"], [1503, 56, 3133, 16], [1503, 57, 3133, 16, "jsxDEV"], [1503, 63, 3133, 16], [1503, 65, 3133, 17, "_lucideReactNative"], [1503, 83, 3133, 17], [1503, 84, 3133, 17, "Send"], [1503, 88, 3133, 21], [1504, 16, 3133, 22, "size"], [1504, 20, 3133, 26], [1504, 22, 3133, 28], [1504, 24, 3133, 31], [1505, 16, 3133, 32, "color"], [1505, 21, 3133, 37], [1505, 23, 3133, 38], [1506, 14, 3133, 44], [1507, 16, 3133, 44, "fileName"], [1507, 24, 3133, 44], [1507, 26, 3133, 44, "_jsxFileName"], [1507, 38, 3133, 44], [1508, 16, 3133, 44, "lineNumber"], [1508, 26, 3133, 44], [1509, 16, 3133, 44, "columnNumber"], [1509, 28, 3133, 44], [1510, 14, 3133, 44], [1510, 21, 3133, 46], [1510, 22, 3133, 47], [1510, 37, 3137, 16], [1510, 41, 3137, 16, "_jsxDevRuntime"], [1510, 55, 3137, 16], [1510, 56, 3137, 16, "jsxDEV"], [1510, 62, 3137, 16], [1510, 64, 3137, 17, "_Text"], [1510, 69, 3137, 17], [1510, 70, 3137, 17, "default"], [1510, 77, 3137, 21], [1511, 16, 3141, 18, "style"], [1511, 21, 3141, 23], [1511, 23, 3141, 25], [1512, 18, 3145, 20, "fontSize"], [1512, 26, 3145, 28], [1512, 28, 3145, 30], [1512, 30, 3145, 32], [1513, 18, 3149, 20, "fontWeight"], [1513, 28, 3149, 30], [1513, 30, 3149, 32], [1513, 35, 3149, 37], [1514, 18, 3153, 20, "color"], [1514, 23, 3153, 25], [1514, 25, 3153, 27], [1514, 31, 3153, 33], [1515, 18, 3157, 20, "marginLeft"], [1515, 28, 3157, 30], [1515, 30, 3157, 32], [1516, 16, 3161, 18], [1516, 17, 3161, 20], [1517, 16, 3161, 20, "children"], [1517, 24, 3161, 20], [1517, 26, 3169, 19], [1517, 47, 3169, 40, "question"], [1517, 55, 3169, 48], [1517, 56, 3169, 49, "reward"], [1517, 62, 3169, 55], [1517, 63, 3169, 56, "toFixed"], [1517, 70, 3169, 63], [1517, 71, 3169, 64], [1517, 72, 3169, 65], [1517, 73, 3169, 66], [1518, 14, 3169, 69], [1519, 16, 3169, 69, "fileName"], [1519, 24, 3169, 69], [1519, 26, 3169, 69, "_jsxFileName"], [1519, 38, 3169, 69], [1520, 16, 3169, 69, "lineNumber"], [1520, 26, 3169, 69], [1521, 16, 3169, 69, "columnNumber"], [1521, 28, 3169, 69], [1522, 14, 3169, 69], [1522, 21, 3173, 22], [1522, 22, 3173, 23], [1523, 12, 3173, 23], [1523, 27, 3177, 16], [1524, 10, 3181, 13], [1525, 12, 3181, 13, "fileName"], [1525, 20, 3181, 13], [1525, 22, 3181, 13, "_jsxFileName"], [1525, 34, 3181, 13], [1526, 12, 3181, 13, "lineNumber"], [1526, 22, 3181, 13], [1527, 12, 3181, 13, "columnNumber"], [1527, 24, 3181, 13], [1528, 10, 3181, 13], [1528, 17, 3185, 28], [1529, 8, 3185, 29], [1530, 10, 3185, 29, "fileName"], [1530, 18, 3185, 29], [1530, 20, 3185, 29, "_jsxFileName"], [1530, 32, 3185, 29], [1531, 10, 3185, 29, "lineNumber"], [1531, 20, 3185, 29], [1532, 10, 3185, 29, "columnNumber"], [1532, 22, 3185, 29], [1533, 8, 3185, 29], [1533, 15, 3189, 14], [1533, 16, 3189, 15], [1534, 6, 3189, 15], [1535, 8, 3189, 15, "fileName"], [1535, 16, 3189, 15], [1535, 18, 3189, 15, "_jsxFileName"], [1535, 30, 3189, 15], [1536, 8, 3189, 15, "lineNumber"], [1536, 18, 3189, 15], [1537, 8, 3189, 15, "columnNumber"], [1537, 20, 3189, 15], [1538, 6, 3189, 15], [1538, 13, 3193, 36], [1538, 14, 3193, 37], [1538, 29, 3201, 6], [1538, 33, 3201, 6, "_jsxDevRuntime"], [1538, 47, 3201, 6], [1538, 48, 3201, 6, "jsxDEV"], [1538, 54, 3201, 6], [1538, 56, 3201, 7, "_Modal"], [1538, 62, 3201, 7], [1538, 63, 3201, 7, "default"], [1538, 70, 3201, 12], [1539, 8, 3205, 8, "visible"], [1539, 15, 3205, 15], [1539, 17, 3205, 17, "showCamera"], [1539, 27, 3205, 28], [1540, 8, 3209, 8, "animationType"], [1540, 21, 3209, 21], [1540, 23, 3209, 22], [1540, 30, 3209, 29], [1541, 8, 3213, 8, "presentationStyle"], [1541, 25, 3213, 25], [1541, 27, 3213, 26], [1541, 39, 3213, 38], [1542, 8, 3213, 38, "children"], [1542, 16, 3213, 38], [1542, 18, 3221, 9, "Platform"], [1542, 35, 3221, 17], [1542, 36, 3221, 18, "OS"], [1542, 38, 3221, 20], [1542, 43, 3221, 25], [1542, 48, 3221, 30], [1542, 64, 3225, 10], [1542, 68, 3225, 10, "_jsxDevRuntime"], [1542, 82, 3225, 10], [1542, 83, 3225, 10, "jsxDEV"], [1542, 89, 3225, 10], [1542, 91, 3225, 11, "_EchoCameraWeb"], [1542, 105, 3225, 11], [1542, 106, 3225, 11, "default"], [1542, 113, 3225, 24], [1543, 10, 3229, 12, "userId"], [1543, 16, 3229, 18], [1543, 18, 3229, 19], [1543, 32, 3229, 33], [1544, 10, 3233, 12, "requestId"], [1544, 19, 3233, 21], [1544, 21, 3233, 23, "id"], [1544, 23, 3233, 26], [1545, 10, 3237, 12, "onComplete"], [1545, 20, 3237, 22], [1545, 22, 3237, 24, "handleCameraComplete"], [1545, 42, 3237, 45], [1546, 10, 3241, 12, "onCancel"], [1546, 18, 3241, 20], [1546, 20, 3241, 22, "handleCameraCancel"], [1547, 8, 3241, 41], [1548, 10, 3241, 41, "fileName"], [1548, 18, 3241, 41], [1548, 20, 3241, 41, "_jsxFileName"], [1548, 32, 3241, 41], [1549, 10, 3241, 41, "lineNumber"], [1549, 20, 3241, 41], [1550, 10, 3241, 41, "columnNumber"], [1550, 22, 3241, 41], [1551, 8, 3241, 41], [1551, 15, 3245, 11], [1551, 16, 3245, 12], [1551, 32, 3253, 10], [1551, 36, 3253, 10, "_jsxDevRuntime"], [1551, 50, 3253, 10], [1551, 51, 3253, 10, "jsxDEV"], [1551, 57, 3253, 10], [1551, 59, 3253, 11, "_EchoCameraUnified"], [1551, 77, 3253, 11], [1551, 78, 3253, 11, "default"], [1551, 85, 3253, 28], [1552, 10, 3257, 12, "userId"], [1552, 16, 3257, 18], [1552, 18, 3257, 19], [1552, 32, 3257, 33], [1553, 10, 3261, 12, "requestId"], [1553, 19, 3261, 21], [1553, 21, 3261, 23, "id"], [1553, 23, 3261, 26], [1554, 10, 3265, 12, "onComplete"], [1554, 20, 3265, 22], [1554, 22, 3265, 24, "handleCameraComplete"], [1554, 42, 3265, 45], [1555, 10, 3269, 12, "onCancel"], [1555, 18, 3269, 20], [1555, 20, 3269, 22, "handleCameraCancel"], [1556, 8, 3269, 41], [1557, 10, 3269, 41, "fileName"], [1557, 18, 3269, 41], [1557, 20, 3269, 41, "_jsxFileName"], [1557, 32, 3269, 41], [1558, 10, 3269, 41, "lineNumber"], [1558, 20, 3269, 41], [1559, 10, 3269, 41, "columnNumber"], [1559, 22, 3269, 41], [1560, 8, 3269, 41], [1560, 15, 3273, 11], [1561, 6, 3277, 9], [1562, 8, 3277, 9, "fileName"], [1562, 16, 3277, 9], [1562, 18, 3277, 9, "_jsxFileName"], [1562, 30, 3277, 9], [1563, 8, 3277, 9, "lineNumber"], [1563, 18, 3277, 9], [1564, 8, 3277, 9, "columnNumber"], [1564, 20, 3277, 9], [1565, 6, 3277, 9], [1565, 13, 3281, 13], [1565, 14, 3281, 14], [1566, 4, 3281, 14], [1567, 6, 3281, 14, "fileName"], [1567, 14, 3281, 14], [1567, 16, 3281, 14, "_jsxFileName"], [1567, 28, 3281, 14], [1568, 6, 3281, 14, "lineNumber"], [1568, 16, 3281, 14], [1569, 6, 3281, 14, "columnNumber"], [1569, 18, 3281, 14], [1570, 4, 3281, 14], [1570, 11, 3285, 10], [1570, 12, 3285, 11], [1571, 2, 3293, 0], [1572, 2, 3293, 1, "_s"], [1572, 4, 3293, 1], [1572, 5, 133, 24, "RespondScreen"], [1572, 18, 133, 37], [1573, 4, 133, 37], [1573, 12, 137, 17, "useSafeAreaInsets"], [1573, 57, 137, 34], [1573, 59, 141, 17, "useLocalSearchParams"], [1573, 91, 141, 37], [1574, 2, 141, 37], [1575, 2, 141, 37, "_c"], [1575, 4, 141, 37], [1575, 7, 133, 24, "RespondScreen"], [1575, 20, 133, 37], [1576, 2, 133, 37], [1576, 6, 133, 37, "_c"], [1576, 8, 133, 37], [1577, 2, 133, 37, "$RefreshReg$"], [1577, 14, 133, 37], [1577, 15, 133, 37, "_c"], [1577, 17, 133, 37], [1578, 0, 133, 37], [1578, 3]], "functionMap": {"names": ["<global>", "RespondScreen", "useMemo$argument_0", "calculateDistance", "verifyLocation", "useEffect$argument_0", "handleStartCamera", "handleCameraComplete", "handleCameraCancel", "submitResponse", "Promise$argument_0", "onPress", "LocationStatus", "getStatusConfig", "TouchableOpacity.props.onPress", "Image.props.onError", "Image.props.onLoad", "Image.props.onLoadStart", "Image.props.onLoadEnd"], "mappings": "AAA;eCoI;qCCgC;GD4B;4BEwG;GFgD;qCGQ;GHgS;YIQ;GJwC;4BKI;GLwE;2CMI;GNoI;yCOI;GPgB;yBQI;wBC4H,sCD;qBEoC,mBF;GRwC;yBWQ;4BCI;KDoL;uBEoN;eFwB;GXgL;qBagG,mBb;+BcmhB;uBdY;8BeE;uBfI;mCgBE;uBhBU;iCiBE;uBjBI;iCa0L;yBbQ;CDmsB"}}, "type": "js/module"}]}