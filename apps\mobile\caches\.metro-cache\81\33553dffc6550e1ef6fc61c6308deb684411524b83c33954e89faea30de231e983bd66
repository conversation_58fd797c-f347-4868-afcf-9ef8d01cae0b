{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkParagraphBuilder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 64, "index": 95}}], "key": "bSBLbQ+OySr+vkrROd64l/u6aUg=", "exportNames": ["*"]}}, {"name": "./JsiSkParagraphStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 96}, "end": {"line": 3, "column": 60, "index": 156}}], "key": "g4d7rQuLZDnunJQWb4wyUyWpYgQ=", "exportNames": ["*"]}}, {"name": "./JsiSkTypefaceFontProvider", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 157}, "end": {"line": 4, "column": 72, "index": 229}}], "key": "r+Ns9adQjK48AIzdjTmnDHoPemU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkParagraphBuilderFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkParagraphBuilder = require(_dependencyMap[1], \"./JsiSkParagraphBuilder\");\n  var _JsiSkParagraphStyle = require(_dependencyMap[2], \"./JsiSkParagraphStyle\");\n  var _JsiSkTypefaceFontProvider = require(_dependencyMap[3], \"./JsiSkTypefaceFontProvider\");\n  class JsiSkParagraphBuilderFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    Make(paragraphStyle, typefaceProvider) {\n      const style = new this.CanvasKit.ParagraphStyle(_JsiSkParagraphStyle.JsiSkParagraphStyle.toParagraphStyle(this.CanvasKit, paragraphStyle !== null && paragraphStyle !== void 0 ? paragraphStyle : {}));\n      if (typefaceProvider === undefined) {\n        throw new Error(\"SkTypefaceFontProvider is required on React Native Web.\");\n      }\n      const fontCollection = this.CanvasKit.FontCollection.Make();\n      fontCollection.setDefaultFontManager(_JsiSkTypefaceFontProvider.JsiSkTypefaceFontProvider.fromValue(typefaceProvider));\n      fontCollection.enableFontFallback();\n      return new _JsiSkParagraphBuilder.JsiSkParagraphBuilder(this.CanvasKit, this.CanvasKit.ParagraphBuilder.MakeFromFontCollection(style, fontCollection));\n    }\n  }\n  exports.JsiSkParagraphBuilderFactory = JsiSkParagraphBuilderFactory;\n});", "lineCount": 26, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkParagraphBuilder"], [7, 28, 2, 0], [7, 31, 2, 0, "require"], [7, 38, 2, 0], [7, 39, 2, 0, "_dependencyMap"], [7, 53, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkParagraphStyle"], [8, 26, 3, 0], [8, 29, 3, 0, "require"], [8, 36, 3, 0], [8, 37, 3, 0, "_dependencyMap"], [8, 51, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JsiSkTypefaceFontProvider"], [9, 32, 4, 0], [9, 35, 4, 0, "require"], [9, 42, 4, 0], [9, 43, 4, 0, "_dependencyMap"], [9, 57, 4, 0], [10, 2, 5, 7], [10, 8, 5, 13, "JsiSkParagraphBuilderFactory"], [10, 36, 5, 41], [10, 45, 5, 50, "Host"], [10, 55, 5, 54], [10, 56, 5, 55], [11, 4, 6, 2, "constructor"], [11, 15, 6, 13, "constructor"], [11, 16, 6, 14, "CanvasKit"], [11, 25, 6, 23], [11, 27, 6, 25], [12, 6, 7, 4], [12, 11, 7, 9], [12, 12, 7, 10, "CanvasKit"], [12, 21, 7, 19], [12, 22, 7, 20], [13, 4, 8, 2], [14, 4, 9, 2, "Make"], [14, 8, 9, 6, "Make"], [14, 9, 9, 7, "paragraphStyle"], [14, 23, 9, 21], [14, 25, 9, 23, "typefaceProvider"], [14, 41, 9, 39], [14, 43, 9, 41], [15, 6, 10, 4], [15, 12, 10, 10, "style"], [15, 17, 10, 15], [15, 20, 10, 18], [15, 24, 10, 22], [15, 28, 10, 26], [15, 29, 10, 27, "CanvasKit"], [15, 38, 10, 36], [15, 39, 10, 37, "ParagraphStyle"], [15, 53, 10, 51], [15, 54, 10, 52, "JsiSkParagraphStyle"], [15, 94, 10, 71], [15, 95, 10, 72, "toParagraphStyle"], [15, 111, 10, 88], [15, 112, 10, 89], [15, 116, 10, 93], [15, 117, 10, 94, "CanvasKit"], [15, 126, 10, 103], [15, 128, 10, 105, "paragraphStyle"], [15, 142, 10, 119], [15, 147, 10, 124], [15, 151, 10, 128], [15, 155, 10, 132, "paragraphStyle"], [15, 169, 10, 146], [15, 174, 10, 151], [15, 179, 10, 156], [15, 180, 10, 157], [15, 183, 10, 160, "paragraphStyle"], [15, 197, 10, 174], [15, 200, 10, 177], [15, 201, 10, 178], [15, 202, 10, 179], [15, 203, 10, 180], [15, 204, 10, 181], [16, 6, 11, 4], [16, 10, 11, 8, "typefaceProvider"], [16, 26, 11, 24], [16, 31, 11, 29, "undefined"], [16, 40, 11, 38], [16, 42, 11, 40], [17, 8, 12, 6], [17, 14, 12, 12], [17, 18, 12, 16, "Error"], [17, 23, 12, 21], [17, 24, 12, 22], [17, 81, 12, 79], [17, 82, 12, 80], [18, 6, 13, 4], [19, 6, 14, 4], [19, 12, 14, 10, "fontCollection"], [19, 26, 14, 24], [19, 29, 14, 27], [19, 33, 14, 31], [19, 34, 14, 32, "CanvasKit"], [19, 43, 14, 41], [19, 44, 14, 42, "FontCollection"], [19, 58, 14, 56], [19, 59, 14, 57, "Make"], [19, 63, 14, 61], [19, 64, 14, 62], [19, 65, 14, 63], [20, 6, 15, 4, "fontCollection"], [20, 20, 15, 18], [20, 21, 15, 19, "setDefaultFontManager"], [20, 42, 15, 40], [20, 43, 15, 41, "JsiSkTypefaceFontProvider"], [20, 95, 15, 66], [20, 96, 15, 67, "fromValue"], [20, 105, 15, 76], [20, 106, 15, 77, "typefaceProvider"], [20, 122, 15, 93], [20, 123, 15, 94], [20, 124, 15, 95], [21, 6, 16, 4, "fontCollection"], [21, 20, 16, 18], [21, 21, 16, 19, "enableFontFallback"], [21, 39, 16, 37], [21, 40, 16, 38], [21, 41, 16, 39], [22, 6, 17, 4], [22, 13, 17, 11], [22, 17, 17, 15, "JsiSkParagraphBuilder"], [22, 61, 17, 36], [22, 62, 17, 37], [22, 66, 17, 41], [22, 67, 17, 42, "CanvasKit"], [22, 76, 17, 51], [22, 78, 17, 53], [22, 82, 17, 57], [22, 83, 17, 58, "CanvasKit"], [22, 92, 17, 67], [22, 93, 17, 68, "ParagraphBuilder"], [22, 109, 17, 84], [22, 110, 17, 85, "MakeFromFontCollection"], [22, 132, 17, 107], [22, 133, 17, 108, "style"], [22, 138, 17, 113], [22, 140, 17, 115, "fontCollection"], [22, 154, 17, 129], [22, 155, 17, 130], [22, 156, 17, 131], [23, 4, 18, 2], [24, 2, 19, 0], [25, 2, 19, 1, "exports"], [25, 9, 19, 1], [25, 10, 19, 1, "JsiSkParagraphBuilderFactory"], [25, 38, 19, 1], [25, 41, 19, 1, "JsiSkParagraphBuilderFactory"], [25, 69, 19, 1], [26, 0, 19, 1], [26, 3]], "functionMap": {"names": ["<global>", "JsiSkParagraphBuilderFactory", "constructor", "Make"], "mappings": "AAA;OCI;ECC;GDE;EEC;GFS;CDC"}}, "type": "js/module"}]}