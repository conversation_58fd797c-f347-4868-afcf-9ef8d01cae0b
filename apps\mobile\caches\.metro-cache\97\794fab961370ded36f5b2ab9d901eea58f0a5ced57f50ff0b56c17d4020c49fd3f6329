{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Get the region to blur\n      const imageData = ctx.getImageData(x, y, width, height);\n      const data = imageData.data;\n\n      // Apply multiple blur effects\n\n      // 1. Heavy pixelation\n      const pixelSize = Math.max(25, Math.min(width, height) / 6);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < height; py += pixelSize) {\n        for (let px = 0; px < width; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n              const index = ((py + dy) * width + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n                const index = ((py + dy) * width + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // 2. Additional gaussian-like blur\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur effect`);\n      for (let i = 0; i < 3; i++) {\n        // Multiple passes\n        applySimpleBlur(data, width, height);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, x, y);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 971,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1017,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1012,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1007,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 827,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1622, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4], [304, 6, 336, 4], [304, 12, 336, 10, "imageData"], [304, 21, 336, 19], [304, 24, 336, 22, "ctx"], [304, 27, 336, 25], [304, 28, 336, 26, "getImageData"], [304, 40, 336, 38], [304, 41, 336, 39, "x"], [304, 42, 336, 40], [304, 44, 336, 42, "y"], [304, 45, 336, 43], [304, 47, 336, 45, "width"], [304, 52, 336, 50], [304, 54, 336, 52, "height"], [304, 60, 336, 58], [304, 61, 336, 59], [305, 6, 337, 4], [305, 12, 337, 10, "data"], [305, 16, 337, 14], [305, 19, 337, 17, "imageData"], [305, 28, 337, 26], [305, 29, 337, 27, "data"], [305, 33, 337, 31], [307, 6, 339, 4], [309, 6, 341, 4], [310, 6, 342, 4], [310, 12, 342, 10, "pixelSize"], [310, 21, 342, 19], [310, 24, 342, 22, "Math"], [310, 28, 342, 26], [310, 29, 342, 27, "max"], [310, 32, 342, 30], [310, 33, 342, 31], [310, 35, 342, 33], [310, 37, 342, 35, "Math"], [310, 41, 342, 39], [310, 42, 342, 40, "min"], [310, 45, 342, 43], [310, 46, 342, 44, "width"], [310, 51, 342, 49], [310, 53, 342, 51, "height"], [310, 59, 342, 57], [310, 60, 342, 58], [310, 63, 342, 61], [310, 64, 342, 62], [310, 65, 342, 63], [311, 6, 343, 4, "console"], [311, 13, 343, 11], [311, 14, 343, 12, "log"], [311, 17, 343, 15], [311, 18, 343, 16], [311, 77, 343, 75, "pixelSize"], [311, 86, 343, 84], [311, 90, 343, 88], [311, 91, 343, 89], [312, 6, 345, 4], [312, 11, 345, 9], [312, 15, 345, 13, "py"], [312, 17, 345, 15], [312, 20, 345, 18], [312, 21, 345, 19], [312, 23, 345, 21, "py"], [312, 25, 345, 23], [312, 28, 345, 26, "height"], [312, 34, 345, 32], [312, 36, 345, 34, "py"], [312, 38, 345, 36], [312, 42, 345, 40, "pixelSize"], [312, 51, 345, 49], [312, 53, 345, 51], [313, 8, 346, 6], [313, 13, 346, 11], [313, 17, 346, 15, "px"], [313, 19, 346, 17], [313, 22, 346, 20], [313, 23, 346, 21], [313, 25, 346, 23, "px"], [313, 27, 346, 25], [313, 30, 346, 28, "width"], [313, 35, 346, 33], [313, 37, 346, 35, "px"], [313, 39, 346, 37], [313, 43, 346, 41, "pixelSize"], [313, 52, 346, 50], [313, 54, 346, 52], [314, 10, 347, 8], [315, 10, 348, 8], [315, 14, 348, 12, "r"], [315, 15, 348, 13], [315, 18, 348, 16], [315, 19, 348, 17], [316, 12, 348, 19, "g"], [316, 13, 348, 20], [316, 16, 348, 23], [316, 17, 348, 24], [317, 12, 348, 26, "b"], [317, 13, 348, 27], [317, 16, 348, 30], [317, 17, 348, 31], [318, 12, 348, 33, "count"], [318, 17, 348, 38], [318, 20, 348, 41], [318, 21, 348, 42], [319, 10, 350, 8], [319, 15, 350, 13], [319, 19, 350, 17, "dy"], [319, 21, 350, 19], [319, 24, 350, 22], [319, 25, 350, 23], [319, 27, 350, 25, "dy"], [319, 29, 350, 27], [319, 32, 350, 30, "pixelSize"], [319, 41, 350, 39], [319, 45, 350, 43, "py"], [319, 47, 350, 45], [319, 50, 350, 48, "dy"], [319, 52, 350, 50], [319, 55, 350, 53, "height"], [319, 61, 350, 59], [319, 63, 350, 61, "dy"], [319, 65, 350, 63], [319, 67, 350, 65], [319, 69, 350, 67], [320, 12, 351, 10], [320, 17, 351, 15], [320, 21, 351, 19, "dx"], [320, 23, 351, 21], [320, 26, 351, 24], [320, 27, 351, 25], [320, 29, 351, 27, "dx"], [320, 31, 351, 29], [320, 34, 351, 32, "pixelSize"], [320, 43, 351, 41], [320, 47, 351, 45, "px"], [320, 49, 351, 47], [320, 52, 351, 50, "dx"], [320, 54, 351, 52], [320, 57, 351, 55, "width"], [320, 62, 351, 60], [320, 64, 351, 62, "dx"], [320, 66, 351, 64], [320, 68, 351, 66], [320, 70, 351, 68], [321, 14, 352, 12], [321, 20, 352, 18, "index"], [321, 25, 352, 23], [321, 28, 352, 26], [321, 29, 352, 27], [321, 30, 352, 28, "py"], [321, 32, 352, 30], [321, 35, 352, 33, "dy"], [321, 37, 352, 35], [321, 41, 352, 39, "width"], [321, 46, 352, 44], [321, 50, 352, 48, "px"], [321, 52, 352, 50], [321, 55, 352, 53, "dx"], [321, 57, 352, 55], [321, 58, 352, 56], [321, 62, 352, 60], [321, 63, 352, 61], [322, 14, 353, 12, "r"], [322, 15, 353, 13], [322, 19, 353, 17, "data"], [322, 23, 353, 21], [322, 24, 353, 22, "index"], [322, 29, 353, 27], [322, 30, 353, 28], [323, 14, 354, 12, "g"], [323, 15, 354, 13], [323, 19, 354, 17, "data"], [323, 23, 354, 21], [323, 24, 354, 22, "index"], [323, 29, 354, 27], [323, 32, 354, 30], [323, 33, 354, 31], [323, 34, 354, 32], [324, 14, 355, 12, "b"], [324, 15, 355, 13], [324, 19, 355, 17, "data"], [324, 23, 355, 21], [324, 24, 355, 22, "index"], [324, 29, 355, 27], [324, 32, 355, 30], [324, 33, 355, 31], [324, 34, 355, 32], [325, 14, 356, 12, "count"], [325, 19, 356, 17], [325, 21, 356, 19], [326, 12, 357, 10], [327, 10, 358, 8], [328, 10, 360, 8], [328, 14, 360, 12, "count"], [328, 19, 360, 17], [328, 22, 360, 20], [328, 23, 360, 21], [328, 25, 360, 23], [329, 12, 361, 10, "r"], [329, 13, 361, 11], [329, 16, 361, 14, "Math"], [329, 20, 361, 18], [329, 21, 361, 19, "floor"], [329, 26, 361, 24], [329, 27, 361, 25, "r"], [329, 28, 361, 26], [329, 31, 361, 29, "count"], [329, 36, 361, 34], [329, 37, 361, 35], [330, 12, 362, 10, "g"], [330, 13, 362, 11], [330, 16, 362, 14, "Math"], [330, 20, 362, 18], [330, 21, 362, 19, "floor"], [330, 26, 362, 24], [330, 27, 362, 25, "g"], [330, 28, 362, 26], [330, 31, 362, 29, "count"], [330, 36, 362, 34], [330, 37, 362, 35], [331, 12, 363, 10, "b"], [331, 13, 363, 11], [331, 16, 363, 14, "Math"], [331, 20, 363, 18], [331, 21, 363, 19, "floor"], [331, 26, 363, 24], [331, 27, 363, 25, "b"], [331, 28, 363, 26], [331, 31, 363, 29, "count"], [331, 36, 363, 34], [331, 37, 363, 35], [333, 12, 365, 10], [334, 12, 366, 10], [334, 17, 366, 15], [334, 21, 366, 19, "dy"], [334, 23, 366, 21], [334, 26, 366, 24], [334, 27, 366, 25], [334, 29, 366, 27, "dy"], [334, 31, 366, 29], [334, 34, 366, 32, "pixelSize"], [334, 43, 366, 41], [334, 47, 366, 45, "py"], [334, 49, 366, 47], [334, 52, 366, 50, "dy"], [334, 54, 366, 52], [334, 57, 366, 55, "height"], [334, 63, 366, 61], [334, 65, 366, 63, "dy"], [334, 67, 366, 65], [334, 69, 366, 67], [334, 71, 366, 69], [335, 14, 367, 12], [335, 19, 367, 17], [335, 23, 367, 21, "dx"], [335, 25, 367, 23], [335, 28, 367, 26], [335, 29, 367, 27], [335, 31, 367, 29, "dx"], [335, 33, 367, 31], [335, 36, 367, 34, "pixelSize"], [335, 45, 367, 43], [335, 49, 367, 47, "px"], [335, 51, 367, 49], [335, 54, 367, 52, "dx"], [335, 56, 367, 54], [335, 59, 367, 57, "width"], [335, 64, 367, 62], [335, 66, 367, 64, "dx"], [335, 68, 367, 66], [335, 70, 367, 68], [335, 72, 367, 70], [336, 16, 368, 14], [336, 22, 368, 20, "index"], [336, 27, 368, 25], [336, 30, 368, 28], [336, 31, 368, 29], [336, 32, 368, 30, "py"], [336, 34, 368, 32], [336, 37, 368, 35, "dy"], [336, 39, 368, 37], [336, 43, 368, 41, "width"], [336, 48, 368, 46], [336, 52, 368, 50, "px"], [336, 54, 368, 52], [336, 57, 368, 55, "dx"], [336, 59, 368, 57], [336, 60, 368, 58], [336, 64, 368, 62], [336, 65, 368, 63], [337, 16, 369, 14, "data"], [337, 20, 369, 18], [337, 21, 369, 19, "index"], [337, 26, 369, 24], [337, 27, 369, 25], [337, 30, 369, 28, "r"], [337, 31, 369, 29], [338, 16, 370, 14, "data"], [338, 20, 370, 18], [338, 21, 370, 19, "index"], [338, 26, 370, 24], [338, 29, 370, 27], [338, 30, 370, 28], [338, 31, 370, 29], [338, 34, 370, 32, "g"], [338, 35, 370, 33], [339, 16, 371, 14, "data"], [339, 20, 371, 18], [339, 21, 371, 19, "index"], [339, 26, 371, 24], [339, 29, 371, 27], [339, 30, 371, 28], [339, 31, 371, 29], [339, 34, 371, 32, "b"], [339, 35, 371, 33], [340, 16, 372, 14], [341, 14, 373, 12], [342, 12, 374, 10], [343, 10, 375, 8], [344, 8, 376, 6], [345, 6, 377, 4], [347, 6, 379, 4], [348, 6, 380, 4, "console"], [348, 13, 380, 11], [348, 14, 380, 12, "log"], [348, 17, 380, 15], [348, 18, 380, 16], [348, 71, 380, 69], [348, 72, 380, 70], [349, 6, 381, 4], [349, 11, 381, 9], [349, 15, 381, 13, "i"], [349, 16, 381, 14], [349, 19, 381, 17], [349, 20, 381, 18], [349, 22, 381, 20, "i"], [349, 23, 381, 21], [349, 26, 381, 24], [349, 27, 381, 25], [349, 29, 381, 27, "i"], [349, 30, 381, 28], [349, 32, 381, 30], [349, 34, 381, 32], [350, 8, 381, 34], [351, 8, 382, 6, "applySimpleBlur"], [351, 23, 382, 21], [351, 24, 382, 22, "data"], [351, 28, 382, 26], [351, 30, 382, 28, "width"], [351, 35, 382, 33], [351, 37, 382, 35, "height"], [351, 43, 382, 41], [351, 44, 382, 42], [352, 6, 383, 4], [354, 6, 385, 4], [355, 6, 386, 4, "ctx"], [355, 9, 386, 7], [355, 10, 386, 8, "putImageData"], [355, 22, 386, 20], [355, 23, 386, 21, "imageData"], [355, 32, 386, 30], [355, 34, 386, 32, "x"], [355, 35, 386, 33], [355, 37, 386, 35, "y"], [355, 38, 386, 36], [355, 39, 386, 37], [356, 4, 387, 2], [356, 5, 387, 3], [357, 4, 389, 2], [357, 10, 389, 8, "applySimpleBlur"], [357, 25, 389, 23], [357, 28, 389, 26, "applySimpleBlur"], [357, 29, 389, 27, "data"], [357, 33, 389, 50], [357, 35, 389, 52, "width"], [357, 40, 389, 65], [357, 42, 389, 67, "height"], [357, 48, 389, 81], [357, 53, 389, 86], [358, 6, 390, 4], [358, 12, 390, 10, "original"], [358, 20, 390, 18], [358, 23, 390, 21], [358, 27, 390, 25, "Uint8ClampedArray"], [358, 44, 390, 42], [358, 45, 390, 43, "data"], [358, 49, 390, 47], [358, 50, 390, 48], [359, 6, 392, 4], [359, 11, 392, 9], [359, 15, 392, 13, "y"], [359, 16, 392, 14], [359, 19, 392, 17], [359, 20, 392, 18], [359, 22, 392, 20, "y"], [359, 23, 392, 21], [359, 26, 392, 24, "height"], [359, 32, 392, 30], [359, 35, 392, 33], [359, 36, 392, 34], [359, 38, 392, 36, "y"], [359, 39, 392, 37], [359, 41, 392, 39], [359, 43, 392, 41], [360, 8, 393, 6], [360, 13, 393, 11], [360, 17, 393, 15, "x"], [360, 18, 393, 16], [360, 21, 393, 19], [360, 22, 393, 20], [360, 24, 393, 22, "x"], [360, 25, 393, 23], [360, 28, 393, 26, "width"], [360, 33, 393, 31], [360, 36, 393, 34], [360, 37, 393, 35], [360, 39, 393, 37, "x"], [360, 40, 393, 38], [360, 42, 393, 40], [360, 44, 393, 42], [361, 10, 394, 8], [361, 16, 394, 14, "index"], [361, 21, 394, 19], [361, 24, 394, 22], [361, 25, 394, 23, "y"], [361, 26, 394, 24], [361, 29, 394, 27, "width"], [361, 34, 394, 32], [361, 37, 394, 35, "x"], [361, 38, 394, 36], [361, 42, 394, 40], [361, 43, 394, 41], [363, 10, 396, 8], [364, 10, 397, 8], [364, 14, 397, 12, "r"], [364, 15, 397, 13], [364, 18, 397, 16], [364, 19, 397, 17], [365, 12, 397, 19, "g"], [365, 13, 397, 20], [365, 16, 397, 23], [365, 17, 397, 24], [366, 12, 397, 26, "b"], [366, 13, 397, 27], [366, 16, 397, 30], [366, 17, 397, 31], [367, 10, 398, 8], [367, 15, 398, 13], [367, 19, 398, 17, "dy"], [367, 21, 398, 19], [367, 24, 398, 22], [367, 25, 398, 23], [367, 26, 398, 24], [367, 28, 398, 26, "dy"], [367, 30, 398, 28], [367, 34, 398, 32], [367, 35, 398, 33], [367, 37, 398, 35, "dy"], [367, 39, 398, 37], [367, 41, 398, 39], [367, 43, 398, 41], [368, 12, 399, 10], [368, 17, 399, 15], [368, 21, 399, 19, "dx"], [368, 23, 399, 21], [368, 26, 399, 24], [368, 27, 399, 25], [368, 28, 399, 26], [368, 30, 399, 28, "dx"], [368, 32, 399, 30], [368, 36, 399, 34], [368, 37, 399, 35], [368, 39, 399, 37, "dx"], [368, 41, 399, 39], [368, 43, 399, 41], [368, 45, 399, 43], [369, 14, 400, 12], [369, 20, 400, 18, "neighborIndex"], [369, 33, 400, 31], [369, 36, 400, 34], [369, 37, 400, 35], [369, 38, 400, 36, "y"], [369, 39, 400, 37], [369, 42, 400, 40, "dy"], [369, 44, 400, 42], [369, 48, 400, 46, "width"], [369, 53, 400, 51], [369, 57, 400, 55, "x"], [369, 58, 400, 56], [369, 61, 400, 59, "dx"], [369, 63, 400, 61], [369, 64, 400, 62], [369, 68, 400, 66], [369, 69, 400, 67], [370, 14, 401, 12, "r"], [370, 15, 401, 13], [370, 19, 401, 17, "original"], [370, 27, 401, 25], [370, 28, 401, 26, "neighborIndex"], [370, 41, 401, 39], [370, 42, 401, 40], [371, 14, 402, 12, "g"], [371, 15, 402, 13], [371, 19, 402, 17, "original"], [371, 27, 402, 25], [371, 28, 402, 26, "neighborIndex"], [371, 41, 402, 39], [371, 44, 402, 42], [371, 45, 402, 43], [371, 46, 402, 44], [372, 14, 403, 12, "b"], [372, 15, 403, 13], [372, 19, 403, 17, "original"], [372, 27, 403, 25], [372, 28, 403, 26, "neighborIndex"], [372, 41, 403, 39], [372, 44, 403, 42], [372, 45, 403, 43], [372, 46, 403, 44], [373, 12, 404, 10], [374, 10, 405, 8], [375, 10, 407, 8, "data"], [375, 14, 407, 12], [375, 15, 407, 13, "index"], [375, 20, 407, 18], [375, 21, 407, 19], [375, 24, 407, 22, "r"], [375, 25, 407, 23], [375, 28, 407, 26], [375, 29, 407, 27], [376, 10, 408, 8, "data"], [376, 14, 408, 12], [376, 15, 408, 13, "index"], [376, 20, 408, 18], [376, 23, 408, 21], [376, 24, 408, 22], [376, 25, 408, 23], [376, 28, 408, 26, "g"], [376, 29, 408, 27], [376, 32, 408, 30], [376, 33, 408, 31], [377, 10, 409, 8, "data"], [377, 14, 409, 12], [377, 15, 409, 13, "index"], [377, 20, 409, 18], [377, 23, 409, 21], [377, 24, 409, 22], [377, 25, 409, 23], [377, 28, 409, 26, "b"], [377, 29, 409, 27], [377, 32, 409, 30], [377, 33, 409, 31], [378, 8, 410, 6], [379, 6, 411, 4], [380, 4, 412, 2], [380, 5, 412, 3], [381, 4, 414, 2], [381, 10, 414, 8, "applyFallbackFaceBlur"], [381, 31, 414, 29], [381, 34, 414, 32, "applyFallbackFaceBlur"], [381, 35, 414, 33, "ctx"], [381, 38, 414, 62], [381, 40, 414, 64, "imgWidth"], [381, 48, 414, 80], [381, 50, 414, 82, "imgHeight"], [381, 59, 414, 99], [381, 64, 414, 104], [382, 6, 415, 4, "console"], [382, 13, 415, 11], [382, 14, 415, 12, "log"], [382, 17, 415, 15], [382, 18, 415, 16], [382, 90, 415, 88], [382, 91, 415, 89], [384, 6, 417, 4], [385, 6, 418, 4], [385, 12, 418, 10, "areas"], [385, 17, 418, 15], [385, 20, 418, 18], [386, 6, 419, 6], [387, 6, 420, 6], [388, 8, 420, 8, "x"], [388, 9, 420, 9], [388, 11, 420, 11, "imgWidth"], [388, 19, 420, 19], [388, 22, 420, 22], [388, 26, 420, 26], [389, 8, 420, 28, "y"], [389, 9, 420, 29], [389, 11, 420, 31, "imgHeight"], [389, 20, 420, 40], [389, 23, 420, 43], [389, 27, 420, 47], [390, 8, 420, 49, "w"], [390, 9, 420, 50], [390, 11, 420, 52, "imgWidth"], [390, 19, 420, 60], [390, 22, 420, 63], [390, 25, 420, 66], [391, 8, 420, 68, "h"], [391, 9, 420, 69], [391, 11, 420, 71, "imgHeight"], [391, 20, 420, 80], [391, 23, 420, 83], [392, 6, 420, 87], [392, 7, 420, 88], [393, 6, 421, 6], [394, 6, 422, 6], [395, 8, 422, 8, "x"], [395, 9, 422, 9], [395, 11, 422, 11, "imgWidth"], [395, 19, 422, 19], [395, 22, 422, 22], [395, 25, 422, 25], [396, 8, 422, 27, "y"], [396, 9, 422, 28], [396, 11, 422, 30, "imgHeight"], [396, 20, 422, 39], [396, 23, 422, 42], [396, 26, 422, 45], [397, 8, 422, 47, "w"], [397, 9, 422, 48], [397, 11, 422, 50, "imgWidth"], [397, 19, 422, 58], [397, 22, 422, 61], [397, 26, 422, 65], [398, 8, 422, 67, "h"], [398, 9, 422, 68], [398, 11, 422, 70, "imgHeight"], [398, 20, 422, 79], [398, 23, 422, 82], [399, 6, 422, 86], [399, 7, 422, 87], [400, 6, 423, 6], [401, 6, 424, 6], [402, 8, 424, 8, "x"], [402, 9, 424, 9], [402, 11, 424, 11, "imgWidth"], [402, 19, 424, 19], [402, 22, 424, 22], [402, 26, 424, 26], [403, 8, 424, 28, "y"], [403, 9, 424, 29], [403, 11, 424, 31, "imgHeight"], [403, 20, 424, 40], [403, 23, 424, 43], [403, 26, 424, 46], [404, 8, 424, 48, "w"], [404, 9, 424, 49], [404, 11, 424, 51, "imgWidth"], [404, 19, 424, 59], [404, 22, 424, 62], [404, 26, 424, 66], [405, 8, 424, 68, "h"], [405, 9, 424, 69], [405, 11, 424, 71, "imgHeight"], [405, 20, 424, 80], [405, 23, 424, 83], [406, 6, 424, 87], [406, 7, 424, 88], [406, 8, 425, 5], [407, 6, 427, 4, "areas"], [407, 11, 427, 9], [407, 12, 427, 10, "for<PERSON>ach"], [407, 19, 427, 17], [407, 20, 427, 18], [407, 21, 427, 19, "area"], [407, 25, 427, 23], [407, 27, 427, 25, "index"], [407, 32, 427, 30], [407, 37, 427, 35], [408, 8, 428, 6, "console"], [408, 15, 428, 13], [408, 16, 428, 14, "log"], [408, 19, 428, 17], [408, 20, 428, 18], [408, 65, 428, 63, "index"], [408, 70, 428, 68], [408, 73, 428, 71], [408, 74, 428, 72], [408, 77, 428, 75], [408, 79, 428, 77, "area"], [408, 83, 428, 81], [408, 84, 428, 82], [409, 8, 429, 6, "applyStrongBlur"], [409, 23, 429, 21], [409, 24, 429, 22, "ctx"], [409, 27, 429, 25], [409, 29, 429, 27, "area"], [409, 33, 429, 31], [409, 34, 429, 32, "x"], [409, 35, 429, 33], [409, 37, 429, 35, "area"], [409, 41, 429, 39], [409, 42, 429, 40, "y"], [409, 43, 429, 41], [409, 45, 429, 43, "area"], [409, 49, 429, 47], [409, 50, 429, 48, "w"], [409, 51, 429, 49], [409, 53, 429, 51, "area"], [409, 57, 429, 55], [409, 58, 429, 56, "h"], [409, 59, 429, 57], [409, 60, 429, 58], [410, 6, 430, 4], [410, 7, 430, 5], [410, 8, 430, 6], [411, 4, 431, 2], [411, 5, 431, 3], [413, 4, 433, 2], [414, 4, 434, 2], [414, 10, 434, 8, "capturePhoto"], [414, 22, 434, 20], [414, 25, 434, 23], [414, 29, 434, 23, "useCallback"], [414, 47, 434, 34], [414, 49, 434, 35], [414, 61, 434, 47], [415, 6, 435, 4], [416, 6, 436, 4], [416, 12, 436, 10, "isDev"], [416, 17, 436, 15], [416, 20, 436, 18, "process"], [416, 27, 436, 25], [416, 28, 436, 26, "env"], [416, 31, 436, 29], [416, 32, 436, 30, "NODE_ENV"], [416, 40, 436, 38], [416, 45, 436, 43], [416, 58, 436, 56], [416, 62, 436, 60, "__DEV__"], [416, 69, 436, 67], [417, 6, 438, 4], [417, 10, 438, 8], [417, 11, 438, 9, "cameraRef"], [417, 20, 438, 18], [417, 21, 438, 19, "current"], [417, 28, 438, 26], [417, 32, 438, 30], [417, 33, 438, 31, "isDev"], [417, 38, 438, 36], [417, 40, 438, 38], [418, 8, 439, 6, "<PERSON><PERSON>"], [418, 22, 439, 11], [418, 23, 439, 12, "alert"], [418, 28, 439, 17], [418, 29, 439, 18], [418, 36, 439, 25], [418, 38, 439, 27], [418, 56, 439, 45], [418, 57, 439, 46], [419, 8, 440, 6], [420, 6, 441, 4], [421, 6, 442, 4], [421, 10, 442, 8], [422, 8, 443, 6, "setProcessingState"], [422, 26, 443, 24], [422, 27, 443, 25], [422, 38, 443, 36], [422, 39, 443, 37], [423, 8, 444, 6, "setProcessingProgress"], [423, 29, 444, 27], [423, 30, 444, 28], [423, 32, 444, 30], [423, 33, 444, 31], [424, 8, 445, 6], [425, 8, 446, 6], [426, 8, 447, 6], [427, 8, 448, 6], [427, 14, 448, 12], [427, 18, 448, 16, "Promise"], [427, 25, 448, 23], [427, 26, 448, 24, "resolve"], [427, 33, 448, 31], [427, 37, 448, 35, "setTimeout"], [427, 47, 448, 45], [427, 48, 448, 46, "resolve"], [427, 55, 448, 53], [427, 57, 448, 55], [427, 59, 448, 57], [427, 60, 448, 58], [427, 61, 448, 59], [428, 8, 449, 6], [429, 8, 450, 6], [429, 12, 450, 10, "photo"], [429, 17, 450, 15], [430, 8, 452, 6], [430, 12, 452, 10], [431, 10, 453, 8, "photo"], [431, 15, 453, 13], [431, 18, 453, 16], [431, 24, 453, 22, "cameraRef"], [431, 33, 453, 31], [431, 34, 453, 32, "current"], [431, 41, 453, 39], [431, 42, 453, 40, "takePictureAsync"], [431, 58, 453, 56], [431, 59, 453, 57], [432, 12, 454, 10, "quality"], [432, 19, 454, 17], [432, 21, 454, 19], [432, 24, 454, 22], [433, 12, 455, 10, "base64"], [433, 18, 455, 16], [433, 20, 455, 18], [433, 25, 455, 23], [434, 12, 456, 10, "skipProcessing"], [434, 26, 456, 24], [434, 28, 456, 26], [434, 32, 456, 30], [434, 33, 456, 32], [435, 10, 457, 8], [435, 11, 457, 9], [435, 12, 457, 10], [436, 8, 458, 6], [436, 9, 458, 7], [436, 10, 458, 8], [436, 17, 458, 15, "cameraError"], [436, 28, 458, 26], [436, 30, 458, 28], [437, 10, 459, 8, "console"], [437, 17, 459, 15], [437, 18, 459, 16, "log"], [437, 21, 459, 19], [437, 22, 459, 20], [437, 82, 459, 80], [437, 84, 459, 82, "cameraError"], [437, 95, 459, 93], [437, 96, 459, 94], [438, 10, 460, 8], [439, 10, 461, 8], [439, 14, 461, 12, "isDev"], [439, 19, 461, 17], [439, 21, 461, 19], [440, 12, 462, 10, "photo"], [440, 17, 462, 15], [440, 20, 462, 18], [441, 14, 463, 12, "uri"], [441, 17, 463, 15], [441, 19, 463, 17], [442, 12, 464, 10], [442, 13, 464, 11], [443, 10, 465, 8], [443, 11, 465, 9], [443, 17, 465, 15], [444, 12, 466, 10], [444, 18, 466, 16, "cameraError"], [444, 29, 466, 27], [445, 10, 467, 8], [446, 8, 468, 6], [447, 8, 469, 6], [447, 12, 469, 10], [447, 13, 469, 11, "photo"], [447, 18, 469, 16], [447, 20, 469, 18], [448, 10, 470, 8], [448, 16, 470, 14], [448, 20, 470, 18, "Error"], [448, 25, 470, 23], [448, 26, 470, 24], [448, 51, 470, 49], [448, 52, 470, 50], [449, 8, 471, 6], [450, 8, 472, 6, "console"], [450, 15, 472, 13], [450, 16, 472, 14, "log"], [450, 19, 472, 17], [450, 20, 472, 18], [450, 56, 472, 54], [450, 58, 472, 56, "photo"], [450, 63, 472, 61], [450, 64, 472, 62, "uri"], [450, 67, 472, 65], [450, 68, 472, 66], [451, 8, 473, 6, "setCapturedPhoto"], [451, 24, 473, 22], [451, 25, 473, 23, "photo"], [451, 30, 473, 28], [451, 31, 473, 29, "uri"], [451, 34, 473, 32], [451, 35, 473, 33], [452, 8, 474, 6, "setProcessingProgress"], [452, 29, 474, 27], [452, 30, 474, 28], [452, 32, 474, 30], [452, 33, 474, 31], [453, 8, 475, 6], [454, 8, 476, 6, "console"], [454, 15, 476, 13], [454, 16, 476, 14, "log"], [454, 19, 476, 17], [454, 20, 476, 18], [454, 73, 476, 71], [454, 74, 476, 72], [455, 8, 477, 6], [455, 14, 477, 12, "processImageWithFaceBlur"], [455, 38, 477, 36], [455, 39, 477, 37, "photo"], [455, 44, 477, 42], [455, 45, 477, 43, "uri"], [455, 48, 477, 46], [455, 49, 477, 47], [456, 8, 478, 6, "console"], [456, 15, 478, 13], [456, 16, 478, 14, "log"], [456, 19, 478, 17], [456, 20, 478, 18], [456, 71, 478, 69], [456, 72, 478, 70], [457, 6, 479, 4], [457, 7, 479, 5], [457, 8, 479, 6], [457, 15, 479, 13, "error"], [457, 20, 479, 18], [457, 22, 479, 20], [458, 8, 480, 6, "console"], [458, 15, 480, 13], [458, 16, 480, 14, "error"], [458, 21, 480, 19], [458, 22, 480, 20], [458, 54, 480, 52], [458, 56, 480, 54, "error"], [458, 61, 480, 59], [458, 62, 480, 60], [459, 8, 481, 6, "setErrorMessage"], [459, 23, 481, 21], [459, 24, 481, 22], [459, 68, 481, 66], [459, 69, 481, 67], [460, 8, 482, 6, "setProcessingState"], [460, 26, 482, 24], [460, 27, 482, 25], [460, 34, 482, 32], [460, 35, 482, 33], [461, 6, 483, 4], [462, 4, 484, 2], [462, 5, 484, 3], [462, 7, 484, 5], [462, 9, 484, 7], [462, 10, 484, 8], [463, 4, 485, 2], [464, 4, 486, 2], [464, 10, 486, 8, "processImageWithFaceBlur"], [464, 34, 486, 32], [464, 37, 486, 35], [464, 43, 486, 42, "photoUri"], [464, 51, 486, 58], [464, 55, 486, 63], [465, 6, 487, 4], [465, 10, 487, 8], [466, 8, 488, 6, "console"], [466, 15, 488, 13], [466, 16, 488, 14, "log"], [466, 19, 488, 17], [466, 20, 488, 18], [466, 84, 488, 82], [466, 85, 488, 83], [467, 8, 489, 6, "setProcessingState"], [467, 26, 489, 24], [467, 27, 489, 25], [467, 39, 489, 37], [467, 40, 489, 38], [468, 8, 490, 6, "setProcessingProgress"], [468, 29, 490, 27], [468, 30, 490, 28], [468, 32, 490, 30], [468, 33, 490, 31], [470, 8, 492, 6], [471, 8, 493, 6], [471, 14, 493, 12, "canvas"], [471, 20, 493, 18], [471, 23, 493, 21, "document"], [471, 31, 493, 29], [471, 32, 493, 30, "createElement"], [471, 45, 493, 43], [471, 46, 493, 44], [471, 54, 493, 52], [471, 55, 493, 53], [472, 8, 494, 6], [472, 14, 494, 12, "ctx"], [472, 17, 494, 15], [472, 20, 494, 18, "canvas"], [472, 26, 494, 24], [472, 27, 494, 25, "getContext"], [472, 37, 494, 35], [472, 38, 494, 36], [472, 42, 494, 40], [472, 43, 494, 41], [473, 8, 495, 6], [473, 12, 495, 10], [473, 13, 495, 11, "ctx"], [473, 16, 495, 14], [473, 18, 495, 16], [473, 24, 495, 22], [473, 28, 495, 26, "Error"], [473, 33, 495, 31], [473, 34, 495, 32], [473, 64, 495, 62], [473, 65, 495, 63], [475, 8, 497, 6], [476, 8, 498, 6], [476, 14, 498, 12, "img"], [476, 17, 498, 15], [476, 20, 498, 18], [476, 24, 498, 22, "Image"], [476, 29, 498, 27], [476, 30, 498, 28], [476, 31, 498, 29], [477, 8, 499, 6], [477, 14, 499, 12], [477, 18, 499, 16, "Promise"], [477, 25, 499, 23], [477, 26, 499, 24], [477, 27, 499, 25, "resolve"], [477, 34, 499, 32], [477, 36, 499, 34, "reject"], [477, 42, 499, 40], [477, 47, 499, 45], [478, 10, 500, 8, "img"], [478, 13, 500, 11], [478, 14, 500, 12, "onload"], [478, 20, 500, 18], [478, 23, 500, 21, "resolve"], [478, 30, 500, 28], [479, 10, 501, 8, "img"], [479, 13, 501, 11], [479, 14, 501, 12, "onerror"], [479, 21, 501, 19], [479, 24, 501, 22, "reject"], [479, 30, 501, 28], [480, 10, 502, 8, "img"], [480, 13, 502, 11], [480, 14, 502, 12, "src"], [480, 17, 502, 15], [480, 20, 502, 18, "photoUri"], [480, 28, 502, 26], [481, 8, 503, 6], [481, 9, 503, 7], [481, 10, 503, 8], [483, 8, 505, 6], [484, 8, 506, 6, "canvas"], [484, 14, 506, 12], [484, 15, 506, 13, "width"], [484, 20, 506, 18], [484, 23, 506, 21, "img"], [484, 26, 506, 24], [484, 27, 506, 25, "width"], [484, 32, 506, 30], [485, 8, 507, 6, "canvas"], [485, 14, 507, 12], [485, 15, 507, 13, "height"], [485, 21, 507, 19], [485, 24, 507, 22, "img"], [485, 27, 507, 25], [485, 28, 507, 26, "height"], [485, 34, 507, 32], [486, 8, 508, 6, "console"], [486, 15, 508, 13], [486, 16, 508, 14, "log"], [486, 19, 508, 17], [486, 20, 508, 18], [486, 54, 508, 52], [486, 56, 508, 54], [487, 10, 508, 56, "width"], [487, 15, 508, 61], [487, 17, 508, 63, "img"], [487, 20, 508, 66], [487, 21, 508, 67, "width"], [487, 26, 508, 72], [488, 10, 508, 74, "height"], [488, 16, 508, 80], [488, 18, 508, 82, "img"], [488, 21, 508, 85], [488, 22, 508, 86, "height"], [489, 8, 508, 93], [489, 9, 508, 94], [489, 10, 508, 95], [491, 8, 510, 6], [492, 8, 511, 6, "ctx"], [492, 11, 511, 9], [492, 12, 511, 10, "drawImage"], [492, 21, 511, 19], [492, 22, 511, 20, "img"], [492, 25, 511, 23], [492, 27, 511, 25], [492, 28, 511, 26], [492, 30, 511, 28], [492, 31, 511, 29], [492, 32, 511, 30], [493, 8, 512, 6, "console"], [493, 15, 512, 13], [493, 16, 512, 14, "log"], [493, 19, 512, 17], [493, 20, 512, 18], [493, 72, 512, 70], [493, 73, 512, 71], [494, 8, 514, 6, "setProcessingProgress"], [494, 29, 514, 27], [494, 30, 514, 28], [494, 32, 514, 30], [494, 33, 514, 31], [496, 8, 516, 6], [497, 8, 517, 6], [497, 12, 517, 10, "detectedFaces"], [497, 25, 517, 23], [497, 28, 517, 26], [497, 30, 517, 28], [498, 8, 519, 6, "console"], [498, 15, 519, 13], [498, 16, 519, 14, "log"], [498, 19, 519, 17], [498, 20, 519, 18], [498, 81, 519, 79], [498, 82, 519, 80], [500, 8, 521, 6], [501, 8, 522, 6], [501, 12, 522, 10], [502, 10, 523, 8], [502, 16, 523, 14, "loadTensorFlowFaceDetection"], [502, 43, 523, 41], [502, 44, 523, 42], [502, 45, 523, 43], [503, 10, 524, 8, "detectedFaces"], [503, 23, 524, 21], [503, 26, 524, 24], [503, 32, 524, 30, "detectFacesWithTensorFlow"], [503, 57, 524, 55], [503, 58, 524, 56, "img"], [503, 61, 524, 59], [503, 62, 524, 60], [504, 10, 525, 8, "console"], [504, 17, 525, 15], [504, 18, 525, 16, "log"], [504, 21, 525, 19], [504, 22, 525, 20], [504, 70, 525, 68, "detectedFaces"], [504, 83, 525, 81], [504, 84, 525, 82, "length"], [504, 90, 525, 88], [504, 98, 525, 96], [504, 99, 525, 97], [505, 8, 526, 6], [505, 9, 526, 7], [505, 10, 526, 8], [505, 17, 526, 15, "tensorFlowError"], [505, 32, 526, 30], [505, 34, 526, 32], [506, 10, 527, 8, "console"], [506, 17, 527, 15], [506, 18, 527, 16, "warn"], [506, 22, 527, 20], [506, 23, 527, 21], [506, 61, 527, 59], [506, 63, 527, 61, "tensorFlowError"], [506, 78, 527, 76], [506, 79, 527, 77], [508, 10, 529, 8], [509, 10, 530, 8, "console"], [509, 17, 530, 15], [509, 18, 530, 16, "log"], [509, 21, 530, 19], [509, 22, 530, 20], [509, 86, 530, 84], [509, 87, 530, 85], [510, 10, 531, 8, "detectedFaces"], [510, 23, 531, 21], [510, 26, 531, 24, "detectFacesHeuristic"], [510, 46, 531, 44], [510, 47, 531, 45, "img"], [510, 50, 531, 48], [510, 52, 531, 50, "ctx"], [510, 55, 531, 53], [510, 56, 531, 54], [511, 10, 532, 8, "console"], [511, 17, 532, 15], [511, 18, 532, 16, "log"], [511, 21, 532, 19], [511, 22, 532, 20], [511, 70, 532, 68, "detectedFaces"], [511, 83, 532, 81], [511, 84, 532, 82, "length"], [511, 90, 532, 88], [511, 98, 532, 96], [511, 99, 532, 97], [512, 8, 533, 6], [513, 8, 535, 6, "console"], [513, 15, 535, 13], [513, 16, 535, 14, "log"], [513, 19, 535, 17], [513, 20, 535, 18], [513, 72, 535, 70, "detectedFaces"], [513, 85, 535, 83], [513, 86, 535, 84, "length"], [513, 92, 535, 90], [513, 100, 535, 98], [513, 101, 535, 99], [514, 8, 536, 6], [514, 12, 536, 10, "detectedFaces"], [514, 25, 536, 23], [514, 26, 536, 24, "length"], [514, 32, 536, 30], [514, 35, 536, 33], [514, 36, 536, 34], [514, 38, 536, 36], [515, 10, 537, 8, "console"], [515, 17, 537, 15], [515, 18, 537, 16, "log"], [515, 21, 537, 19], [515, 22, 537, 20], [515, 66, 537, 64], [515, 68, 537, 66, "detectedFaces"], [515, 81, 537, 79], [515, 82, 537, 80, "map"], [515, 85, 537, 83], [515, 86, 537, 84], [515, 87, 537, 85, "face"], [515, 91, 537, 89], [515, 93, 537, 91, "i"], [515, 94, 537, 92], [515, 100, 537, 98], [516, 12, 538, 10, "faceNumber"], [516, 22, 538, 20], [516, 24, 538, 22, "i"], [516, 25, 538, 23], [516, 28, 538, 26], [516, 29, 538, 27], [517, 12, 539, 10, "centerX"], [517, 19, 539, 17], [517, 21, 539, 19, "face"], [517, 25, 539, 23], [517, 26, 539, 24, "boundingBox"], [517, 37, 539, 35], [517, 38, 539, 36, "xCenter"], [517, 45, 539, 43], [518, 12, 540, 10, "centerY"], [518, 19, 540, 17], [518, 21, 540, 19, "face"], [518, 25, 540, 23], [518, 26, 540, 24, "boundingBox"], [518, 37, 540, 35], [518, 38, 540, 36, "yCenter"], [518, 45, 540, 43], [519, 12, 541, 10, "width"], [519, 17, 541, 15], [519, 19, 541, 17, "face"], [519, 23, 541, 21], [519, 24, 541, 22, "boundingBox"], [519, 35, 541, 33], [519, 36, 541, 34, "width"], [519, 41, 541, 39], [520, 12, 542, 10, "height"], [520, 18, 542, 16], [520, 20, 542, 18, "face"], [520, 24, 542, 22], [520, 25, 542, 23, "boundingBox"], [520, 36, 542, 34], [520, 37, 542, 35, "height"], [521, 10, 543, 8], [521, 11, 543, 9], [521, 12, 543, 10], [521, 13, 543, 11], [521, 14, 543, 12], [522, 8, 544, 6], [522, 9, 544, 7], [522, 15, 544, 13], [523, 10, 545, 8, "console"], [523, 17, 545, 15], [523, 18, 545, 16, "log"], [523, 21, 545, 19], [523, 22, 545, 20], [523, 91, 545, 89], [523, 92, 545, 90], [524, 8, 546, 6], [525, 8, 548, 6, "setProcessingProgress"], [525, 29, 548, 27], [525, 30, 548, 28], [525, 32, 548, 30], [525, 33, 548, 31], [527, 8, 550, 6], [528, 8, 551, 6], [528, 12, 551, 10, "detectedFaces"], [528, 25, 551, 23], [528, 26, 551, 24, "length"], [528, 32, 551, 30], [528, 35, 551, 33], [528, 36, 551, 34], [528, 38, 551, 36], [529, 10, 552, 8, "console"], [529, 17, 552, 15], [529, 18, 552, 16, "log"], [529, 21, 552, 19], [529, 22, 552, 20], [529, 61, 552, 59, "detectedFaces"], [529, 74, 552, 72], [529, 75, 552, 73, "length"], [529, 81, 552, 79], [529, 101, 552, 99], [529, 102, 552, 100], [530, 10, 554, 8, "detectedFaces"], [530, 23, 554, 21], [530, 24, 554, 22, "for<PERSON>ach"], [530, 31, 554, 29], [530, 32, 554, 30], [530, 33, 554, 31, "detection"], [530, 42, 554, 40], [530, 44, 554, 42, "index"], [530, 49, 554, 47], [530, 54, 554, 52], [531, 12, 555, 10], [531, 18, 555, 16, "bbox"], [531, 22, 555, 20], [531, 25, 555, 23, "detection"], [531, 34, 555, 32], [531, 35, 555, 33, "boundingBox"], [531, 46, 555, 44], [533, 12, 557, 10], [534, 12, 558, 10], [534, 18, 558, 16, "faceX"], [534, 23, 558, 21], [534, 26, 558, 24, "bbox"], [534, 30, 558, 28], [534, 31, 558, 29, "xCenter"], [534, 38, 558, 36], [534, 41, 558, 39, "img"], [534, 44, 558, 42], [534, 45, 558, 43, "width"], [534, 50, 558, 48], [534, 53, 558, 52, "bbox"], [534, 57, 558, 56], [534, 58, 558, 57, "width"], [534, 63, 558, 62], [534, 66, 558, 65, "img"], [534, 69, 558, 68], [534, 70, 558, 69, "width"], [534, 75, 558, 74], [534, 78, 558, 78], [534, 79, 558, 79], [535, 12, 559, 10], [535, 18, 559, 16, "faceY"], [535, 23, 559, 21], [535, 26, 559, 24, "bbox"], [535, 30, 559, 28], [535, 31, 559, 29, "yCenter"], [535, 38, 559, 36], [535, 41, 559, 39, "img"], [535, 44, 559, 42], [535, 45, 559, 43, "height"], [535, 51, 559, 49], [535, 54, 559, 53, "bbox"], [535, 58, 559, 57], [535, 59, 559, 58, "height"], [535, 65, 559, 64], [535, 68, 559, 67, "img"], [535, 71, 559, 70], [535, 72, 559, 71, "height"], [535, 78, 559, 77], [535, 81, 559, 81], [535, 82, 559, 82], [536, 12, 560, 10], [536, 18, 560, 16, "faceWidth"], [536, 27, 560, 25], [536, 30, 560, 28, "bbox"], [536, 34, 560, 32], [536, 35, 560, 33, "width"], [536, 40, 560, 38], [536, 43, 560, 41, "img"], [536, 46, 560, 44], [536, 47, 560, 45, "width"], [536, 52, 560, 50], [537, 12, 561, 10], [537, 18, 561, 16, "faceHeight"], [537, 28, 561, 26], [537, 31, 561, 29, "bbox"], [537, 35, 561, 33], [537, 36, 561, 34, "height"], [537, 42, 561, 40], [537, 45, 561, 43, "img"], [537, 48, 561, 46], [537, 49, 561, 47, "height"], [537, 55, 561, 53], [539, 12, 563, 10], [540, 12, 564, 10], [540, 18, 564, 16, "padding"], [540, 25, 564, 23], [540, 28, 564, 26], [540, 31, 564, 29], [541, 12, 565, 10], [541, 18, 565, 16, "paddedX"], [541, 25, 565, 23], [541, 28, 565, 26, "Math"], [541, 32, 565, 30], [541, 33, 565, 31, "max"], [541, 36, 565, 34], [541, 37, 565, 35], [541, 38, 565, 36], [541, 40, 565, 38, "faceX"], [541, 45, 565, 43], [541, 48, 565, 46, "faceWidth"], [541, 57, 565, 55], [541, 60, 565, 58, "padding"], [541, 67, 565, 65], [541, 68, 565, 66], [542, 12, 566, 10], [542, 18, 566, 16, "paddedY"], [542, 25, 566, 23], [542, 28, 566, 26, "Math"], [542, 32, 566, 30], [542, 33, 566, 31, "max"], [542, 36, 566, 34], [542, 37, 566, 35], [542, 38, 566, 36], [542, 40, 566, 38, "faceY"], [542, 45, 566, 43], [542, 48, 566, 46, "faceHeight"], [542, 58, 566, 56], [542, 61, 566, 59, "padding"], [542, 68, 566, 66], [542, 69, 566, 67], [543, 12, 567, 10], [543, 18, 567, 16, "<PERSON><PERSON><PERSON><PERSON>"], [543, 29, 567, 27], [543, 32, 567, 30, "Math"], [543, 36, 567, 34], [543, 37, 567, 35, "min"], [543, 40, 567, 38], [543, 41, 567, 39, "img"], [543, 44, 567, 42], [543, 45, 567, 43, "width"], [543, 50, 567, 48], [543, 53, 567, 51, "paddedX"], [543, 60, 567, 58], [543, 62, 567, 60, "faceWidth"], [543, 71, 567, 69], [543, 75, 567, 73], [543, 76, 567, 74], [543, 79, 567, 77], [543, 80, 567, 78], [543, 83, 567, 81, "padding"], [543, 90, 567, 88], [543, 91, 567, 89], [543, 92, 567, 90], [544, 12, 568, 10], [544, 18, 568, 16, "paddedHeight"], [544, 30, 568, 28], [544, 33, 568, 31, "Math"], [544, 37, 568, 35], [544, 38, 568, 36, "min"], [544, 41, 568, 39], [544, 42, 568, 40, "img"], [544, 45, 568, 43], [544, 46, 568, 44, "height"], [544, 52, 568, 50], [544, 55, 568, 53, "paddedY"], [544, 62, 568, 60], [544, 64, 568, 62, "faceHeight"], [544, 74, 568, 72], [544, 78, 568, 76], [544, 79, 568, 77], [544, 82, 568, 80], [544, 83, 568, 81], [544, 86, 568, 84, "padding"], [544, 93, 568, 91], [544, 94, 568, 92], [544, 95, 568, 93], [545, 12, 570, 10, "console"], [545, 19, 570, 17], [545, 20, 570, 18, "log"], [545, 23, 570, 21], [545, 24, 570, 22], [545, 60, 570, 58, "index"], [545, 65, 570, 63], [545, 68, 570, 66], [545, 69, 570, 67], [545, 72, 570, 70], [545, 74, 570, 72], [546, 14, 571, 12, "original"], [546, 22, 571, 20], [546, 24, 571, 22], [547, 16, 571, 24, "x"], [547, 17, 571, 25], [547, 19, 571, 27, "Math"], [547, 23, 571, 31], [547, 24, 571, 32, "round"], [547, 29, 571, 37], [547, 30, 571, 38, "faceX"], [547, 35, 571, 43], [547, 36, 571, 44], [548, 16, 571, 46, "y"], [548, 17, 571, 47], [548, 19, 571, 49, "Math"], [548, 23, 571, 53], [548, 24, 571, 54, "round"], [548, 29, 571, 59], [548, 30, 571, 60, "faceY"], [548, 35, 571, 65], [548, 36, 571, 66], [549, 16, 571, 68, "w"], [549, 17, 571, 69], [549, 19, 571, 71, "Math"], [549, 23, 571, 75], [549, 24, 571, 76, "round"], [549, 29, 571, 81], [549, 30, 571, 82, "faceWidth"], [549, 39, 571, 91], [549, 40, 571, 92], [550, 16, 571, 94, "h"], [550, 17, 571, 95], [550, 19, 571, 97, "Math"], [550, 23, 571, 101], [550, 24, 571, 102, "round"], [550, 29, 571, 107], [550, 30, 571, 108, "faceHeight"], [550, 40, 571, 118], [551, 14, 571, 120], [551, 15, 571, 121], [552, 14, 572, 12, "padded"], [552, 20, 572, 18], [552, 22, 572, 20], [553, 16, 572, 22, "x"], [553, 17, 572, 23], [553, 19, 572, 25, "Math"], [553, 23, 572, 29], [553, 24, 572, 30, "round"], [553, 29, 572, 35], [553, 30, 572, 36, "paddedX"], [553, 37, 572, 43], [553, 38, 572, 44], [554, 16, 572, 46, "y"], [554, 17, 572, 47], [554, 19, 572, 49, "Math"], [554, 23, 572, 53], [554, 24, 572, 54, "round"], [554, 29, 572, 59], [554, 30, 572, 60, "paddedY"], [554, 37, 572, 67], [554, 38, 572, 68], [555, 16, 572, 70, "w"], [555, 17, 572, 71], [555, 19, 572, 73, "Math"], [555, 23, 572, 77], [555, 24, 572, 78, "round"], [555, 29, 572, 83], [555, 30, 572, 84, "<PERSON><PERSON><PERSON><PERSON>"], [555, 41, 572, 95], [555, 42, 572, 96], [556, 16, 572, 98, "h"], [556, 17, 572, 99], [556, 19, 572, 101, "Math"], [556, 23, 572, 105], [556, 24, 572, 106, "round"], [556, 29, 572, 111], [556, 30, 572, 112, "paddedHeight"], [556, 42, 572, 124], [557, 14, 572, 126], [558, 12, 573, 10], [558, 13, 573, 11], [558, 14, 573, 12], [560, 12, 575, 10], [561, 12, 576, 10, "console"], [561, 19, 576, 17], [561, 20, 576, 18, "log"], [561, 23, 576, 21], [561, 24, 576, 22], [561, 70, 576, 68], [561, 72, 576, 70], [562, 14, 577, 12, "width"], [562, 19, 577, 17], [562, 21, 577, 19, "canvas"], [562, 27, 577, 25], [562, 28, 577, 26, "width"], [562, 33, 577, 31], [563, 14, 578, 12, "height"], [563, 20, 578, 18], [563, 22, 578, 20, "canvas"], [563, 28, 578, 26], [563, 29, 578, 27, "height"], [563, 35, 578, 33], [564, 14, 579, 12, "contextValid"], [564, 26, 579, 24], [564, 28, 579, 26], [564, 29, 579, 27], [564, 30, 579, 28, "ctx"], [565, 12, 580, 10], [565, 13, 580, 11], [565, 14, 580, 12], [567, 12, 582, 10], [568, 12, 583, 10, "applyStrongBlur"], [568, 27, 583, 25], [568, 28, 583, 26, "ctx"], [568, 31, 583, 29], [568, 33, 583, 31, "paddedX"], [568, 40, 583, 38], [568, 42, 583, 40, "paddedY"], [568, 49, 583, 47], [568, 51, 583, 49, "<PERSON><PERSON><PERSON><PERSON>"], [568, 62, 583, 60], [568, 64, 583, 62, "paddedHeight"], [568, 76, 583, 74], [568, 77, 583, 75], [570, 12, 585, 10], [571, 12, 586, 10, "console"], [571, 19, 586, 17], [571, 20, 586, 18, "log"], [571, 23, 586, 21], [571, 24, 586, 22], [571, 102, 586, 100], [571, 103, 586, 101], [573, 12, 588, 10], [574, 12, 589, 10], [574, 18, 589, 16, "testImageData"], [574, 31, 589, 29], [574, 34, 589, 32, "ctx"], [574, 37, 589, 35], [574, 38, 589, 36, "getImageData"], [574, 50, 589, 48], [574, 51, 589, 49, "paddedX"], [574, 58, 589, 56], [574, 61, 589, 59], [574, 63, 589, 61], [574, 65, 589, 63, "paddedY"], [574, 72, 589, 70], [574, 75, 589, 73], [574, 77, 589, 75], [574, 79, 589, 77], [574, 81, 589, 79], [574, 83, 589, 81], [574, 85, 589, 83], [574, 86, 589, 84], [575, 12, 590, 10, "console"], [575, 19, 590, 17], [575, 20, 590, 18, "log"], [575, 23, 590, 21], [575, 24, 590, 22], [575, 70, 590, 68], [575, 72, 590, 70], [576, 14, 591, 12, "firstPixel"], [576, 24, 591, 22], [576, 26, 591, 24], [576, 27, 591, 25, "testImageData"], [576, 40, 591, 38], [576, 41, 591, 39, "data"], [576, 45, 591, 43], [576, 46, 591, 44], [576, 47, 591, 45], [576, 48, 591, 46], [576, 50, 591, 48, "testImageData"], [576, 63, 591, 61], [576, 64, 591, 62, "data"], [576, 68, 591, 66], [576, 69, 591, 67], [576, 70, 591, 68], [576, 71, 591, 69], [576, 73, 591, 71, "testImageData"], [576, 86, 591, 84], [576, 87, 591, 85, "data"], [576, 91, 591, 89], [576, 92, 591, 90], [576, 93, 591, 91], [576, 94, 591, 92], [576, 95, 591, 93], [577, 14, 592, 12, "secondPixel"], [577, 25, 592, 23], [577, 27, 592, 25], [577, 28, 592, 26, "testImageData"], [577, 41, 592, 39], [577, 42, 592, 40, "data"], [577, 46, 592, 44], [577, 47, 592, 45], [577, 48, 592, 46], [577, 49, 592, 47], [577, 51, 592, 49, "testImageData"], [577, 64, 592, 62], [577, 65, 592, 63, "data"], [577, 69, 592, 67], [577, 70, 592, 68], [577, 71, 592, 69], [577, 72, 592, 70], [577, 74, 592, 72, "testImageData"], [577, 87, 592, 85], [577, 88, 592, 86, "data"], [577, 92, 592, 90], [577, 93, 592, 91], [577, 94, 592, 92], [577, 95, 592, 93], [578, 12, 593, 10], [578, 13, 593, 11], [578, 14, 593, 12], [579, 12, 595, 10, "console"], [579, 19, 595, 17], [579, 20, 595, 18, "log"], [579, 23, 595, 21], [579, 24, 595, 22], [579, 50, 595, 48, "index"], [579, 55, 595, 53], [579, 58, 595, 56], [579, 59, 595, 57], [579, 79, 595, 77], [579, 80, 595, 78], [580, 10, 596, 8], [580, 11, 596, 9], [580, 12, 596, 10], [581, 10, 598, 8, "console"], [581, 17, 598, 15], [581, 18, 598, 16, "log"], [581, 21, 598, 19], [581, 22, 598, 20], [581, 48, 598, 46, "detectedFaces"], [581, 61, 598, 59], [581, 62, 598, 60, "length"], [581, 68, 598, 66], [581, 104, 598, 102], [581, 105, 598, 103], [582, 8, 599, 6], [582, 9, 599, 7], [582, 15, 599, 13], [583, 10, 600, 8, "console"], [583, 17, 600, 15], [583, 18, 600, 16, "log"], [583, 21, 600, 19], [583, 22, 600, 20], [583, 109, 600, 107], [583, 110, 600, 108], [584, 10, 601, 8], [585, 10, 602, 8, "applyFallbackFaceBlur"], [585, 31, 602, 29], [585, 32, 602, 30, "ctx"], [585, 35, 602, 33], [585, 37, 602, 35, "img"], [585, 40, 602, 38], [585, 41, 602, 39, "width"], [585, 46, 602, 44], [585, 48, 602, 46, "img"], [585, 51, 602, 49], [585, 52, 602, 50, "height"], [585, 58, 602, 56], [585, 59, 602, 57], [586, 8, 603, 6], [587, 8, 605, 6, "setProcessingProgress"], [587, 29, 605, 27], [587, 30, 605, 28], [587, 32, 605, 30], [587, 33, 605, 31], [589, 8, 607, 6], [590, 8, 608, 6, "console"], [590, 15, 608, 13], [590, 16, 608, 14, "log"], [590, 19, 608, 17], [590, 20, 608, 18], [590, 85, 608, 83], [590, 86, 608, 84], [591, 8, 609, 6], [591, 14, 609, 12, "blurredImageBlob"], [591, 30, 609, 28], [591, 33, 609, 31], [591, 39, 609, 37], [591, 43, 609, 41, "Promise"], [591, 50, 609, 48], [591, 51, 609, 56, "resolve"], [591, 58, 609, 63], [591, 62, 609, 68], [592, 10, 610, 8, "canvas"], [592, 16, 610, 14], [592, 17, 610, 15, "toBlob"], [592, 23, 610, 21], [592, 24, 610, 23, "blob"], [592, 28, 610, 27], [592, 32, 610, 32, "resolve"], [592, 39, 610, 39], [592, 40, 610, 40, "blob"], [592, 44, 610, 45], [592, 45, 610, 46], [592, 47, 610, 48], [592, 59, 610, 60], [592, 61, 610, 62], [592, 64, 610, 65], [592, 65, 610, 66], [593, 8, 611, 6], [593, 9, 611, 7], [593, 10, 611, 8], [594, 8, 613, 6], [594, 14, 613, 12, "blurredImageUrl"], [594, 29, 613, 27], [594, 32, 613, 30, "URL"], [594, 35, 613, 33], [594, 36, 613, 34, "createObjectURL"], [594, 51, 613, 49], [594, 52, 613, 50, "blurredImageBlob"], [594, 68, 613, 66], [594, 69, 613, 67], [595, 8, 614, 6, "console"], [595, 15, 614, 13], [595, 16, 614, 14, "log"], [595, 19, 614, 17], [595, 20, 614, 18], [595, 66, 614, 64], [595, 68, 614, 66, "blurredImageUrl"], [595, 83, 614, 81], [595, 84, 614, 82, "substring"], [595, 93, 614, 91], [595, 94, 614, 92], [595, 95, 614, 93], [595, 97, 614, 95], [595, 99, 614, 97], [595, 100, 614, 98], [595, 103, 614, 101], [595, 108, 614, 106], [595, 109, 614, 107], [596, 8, 616, 6, "setProcessingProgress"], [596, 29, 616, 27], [596, 30, 616, 28], [596, 33, 616, 31], [596, 34, 616, 32], [598, 8, 618, 6], [599, 8, 619, 6], [599, 14, 619, 12, "completeProcessing"], [599, 32, 619, 30], [599, 33, 619, 31, "blurredImageUrl"], [599, 48, 619, 46], [599, 49, 619, 47], [600, 6, 621, 4], [600, 7, 621, 5], [600, 8, 621, 6], [600, 15, 621, 13, "error"], [600, 20, 621, 18], [600, 22, 621, 20], [601, 8, 622, 6, "console"], [601, 15, 622, 13], [601, 16, 622, 14, "error"], [601, 21, 622, 19], [601, 22, 622, 20], [601, 57, 622, 55], [601, 59, 622, 57, "error"], [601, 64, 622, 62], [601, 65, 622, 63], [602, 8, 623, 6, "setErrorMessage"], [602, 23, 623, 21], [602, 24, 623, 22], [602, 50, 623, 48], [602, 51, 623, 49], [603, 8, 624, 6, "setProcessingState"], [603, 26, 624, 24], [603, 27, 624, 25], [603, 34, 624, 32], [603, 35, 624, 33], [604, 6, 625, 4], [605, 4, 626, 2], [605, 5, 626, 3], [607, 4, 628, 2], [608, 4, 629, 2], [608, 10, 629, 8, "completeProcessing"], [608, 28, 629, 26], [608, 31, 629, 29], [608, 37, 629, 36, "blurredImageUrl"], [608, 52, 629, 59], [608, 56, 629, 64], [609, 6, 630, 4], [609, 10, 630, 8], [610, 8, 631, 6, "setProcessingState"], [610, 26, 631, 24], [610, 27, 631, 25], [610, 37, 631, 35], [610, 38, 631, 36], [612, 8, 633, 6], [613, 8, 634, 6], [613, 14, 634, 12, "timestamp"], [613, 23, 634, 21], [613, 26, 634, 24, "Date"], [613, 30, 634, 28], [613, 31, 634, 29, "now"], [613, 34, 634, 32], [613, 35, 634, 33], [613, 36, 634, 34], [614, 8, 635, 6], [614, 14, 635, 12, "result"], [614, 20, 635, 18], [614, 23, 635, 21], [615, 10, 636, 8, "imageUrl"], [615, 18, 636, 16], [615, 20, 636, 18, "blurredImageUrl"], [615, 35, 636, 33], [616, 10, 637, 8, "localUri"], [616, 18, 637, 16], [616, 20, 637, 18, "blurredImageUrl"], [616, 35, 637, 33], [617, 10, 638, 8, "challengeCode"], [617, 23, 638, 21], [617, 25, 638, 23, "challengeCode"], [617, 38, 638, 36], [617, 42, 638, 40], [617, 44, 638, 42], [618, 10, 639, 8, "timestamp"], [618, 19, 639, 17], [619, 10, 640, 8, "jobId"], [619, 15, 640, 13], [619, 17, 640, 15], [619, 27, 640, 25, "timestamp"], [619, 36, 640, 34], [619, 38, 640, 36], [620, 10, 641, 8, "status"], [620, 16, 641, 14], [620, 18, 641, 16], [621, 8, 642, 6], [621, 9, 642, 7], [622, 8, 644, 6, "console"], [622, 15, 644, 13], [622, 16, 644, 14, "log"], [622, 19, 644, 17], [622, 20, 644, 18], [622, 100, 644, 98], [622, 102, 644, 100], [623, 10, 645, 8, "imageUrl"], [623, 18, 645, 16], [623, 20, 645, 18, "blurredImageUrl"], [623, 35, 645, 33], [623, 36, 645, 34, "substring"], [623, 45, 645, 43], [623, 46, 645, 44], [623, 47, 645, 45], [623, 49, 645, 47], [623, 51, 645, 49], [623, 52, 645, 50], [623, 55, 645, 53], [623, 60, 645, 58], [624, 10, 646, 8, "timestamp"], [624, 19, 646, 17], [625, 10, 647, 8, "jobId"], [625, 15, 647, 13], [625, 17, 647, 15, "result"], [625, 23, 647, 21], [625, 24, 647, 22, "jobId"], [626, 8, 648, 6], [626, 9, 648, 7], [626, 10, 648, 8], [628, 8, 650, 6], [629, 8, 651, 6, "onComplete"], [629, 18, 651, 16], [629, 19, 651, 17, "result"], [629, 25, 651, 23], [629, 26, 651, 24], [630, 6, 653, 4], [630, 7, 653, 5], [630, 8, 653, 6], [630, 15, 653, 13, "error"], [630, 20, 653, 18], [630, 22, 653, 20], [631, 8, 654, 6, "console"], [631, 15, 654, 13], [631, 16, 654, 14, "error"], [631, 21, 654, 19], [631, 22, 654, 20], [631, 57, 654, 55], [631, 59, 654, 57, "error"], [631, 64, 654, 62], [631, 65, 654, 63], [632, 8, 655, 6, "setErrorMessage"], [632, 23, 655, 21], [632, 24, 655, 22], [632, 56, 655, 54], [632, 57, 655, 55], [633, 8, 656, 6, "setProcessingState"], [633, 26, 656, 24], [633, 27, 656, 25], [633, 34, 656, 32], [633, 35, 656, 33], [634, 6, 657, 4], [635, 4, 658, 2], [635, 5, 658, 3], [637, 4, 660, 2], [638, 4, 661, 2], [638, 10, 661, 8, "triggerServerProcessing"], [638, 33, 661, 31], [638, 36, 661, 34], [638, 42, 661, 34, "triggerServerProcessing"], [638, 43, 661, 41, "privateImageUrl"], [638, 58, 661, 64], [638, 60, 661, 66, "timestamp"], [638, 69, 661, 83], [638, 74, 661, 88], [639, 6, 662, 4], [639, 10, 662, 8], [640, 8, 663, 6, "console"], [640, 15, 663, 13], [640, 16, 663, 14, "log"], [640, 19, 663, 17], [640, 20, 663, 18], [640, 74, 663, 72], [640, 76, 663, 74, "privateImageUrl"], [640, 91, 663, 89], [640, 92, 663, 90], [641, 8, 664, 6, "setProcessingState"], [641, 26, 664, 24], [641, 27, 664, 25], [641, 39, 664, 37], [641, 40, 664, 38], [642, 8, 665, 6, "setProcessingProgress"], [642, 29, 665, 27], [642, 30, 665, 28], [642, 32, 665, 30], [642, 33, 665, 31], [643, 8, 667, 6], [643, 14, 667, 12, "requestBody"], [643, 25, 667, 23], [643, 28, 667, 26], [644, 10, 668, 8, "imageUrl"], [644, 18, 668, 16], [644, 20, 668, 18, "privateImageUrl"], [644, 35, 668, 33], [645, 10, 669, 8, "userId"], [645, 16, 669, 14], [646, 10, 670, 8, "requestId"], [646, 19, 670, 17], [647, 10, 671, 8, "timestamp"], [647, 19, 671, 17], [648, 10, 672, 8, "platform"], [648, 18, 672, 16], [648, 20, 672, 18], [649, 8, 673, 6], [649, 9, 673, 7], [650, 8, 675, 6, "console"], [650, 15, 675, 13], [650, 16, 675, 14, "log"], [650, 19, 675, 17], [650, 20, 675, 18], [650, 65, 675, 63], [650, 67, 675, 65, "requestBody"], [650, 78, 675, 76], [650, 79, 675, 77], [652, 8, 677, 6], [653, 8, 678, 6], [653, 14, 678, 12, "response"], [653, 22, 678, 20], [653, 25, 678, 23], [653, 31, 678, 29, "fetch"], [653, 36, 678, 34], [653, 37, 678, 35], [653, 40, 678, 38, "API_BASE_URL"], [653, 52, 678, 50], [653, 72, 678, 70], [653, 74, 678, 72], [654, 10, 679, 8, "method"], [654, 16, 679, 14], [654, 18, 679, 16], [654, 24, 679, 22], [655, 10, 680, 8, "headers"], [655, 17, 680, 15], [655, 19, 680, 17], [656, 12, 681, 10], [656, 26, 681, 24], [656, 28, 681, 26], [656, 46, 681, 44], [657, 12, 682, 10], [657, 27, 682, 25], [657, 29, 682, 27], [657, 39, 682, 37], [657, 45, 682, 43, "getAuthToken"], [657, 57, 682, 55], [657, 58, 682, 56], [657, 59, 682, 57], [658, 10, 683, 8], [658, 11, 683, 9], [659, 10, 684, 8, "body"], [659, 14, 684, 12], [659, 16, 684, 14, "JSON"], [659, 20, 684, 18], [659, 21, 684, 19, "stringify"], [659, 30, 684, 28], [659, 31, 684, 29, "requestBody"], [659, 42, 684, 40], [660, 8, 685, 6], [660, 9, 685, 7], [660, 10, 685, 8], [661, 8, 687, 6], [661, 12, 687, 10], [661, 13, 687, 11, "response"], [661, 21, 687, 19], [661, 22, 687, 20, "ok"], [661, 24, 687, 22], [661, 26, 687, 24], [662, 10, 688, 8], [662, 16, 688, 14, "errorText"], [662, 25, 688, 23], [662, 28, 688, 26], [662, 34, 688, 32, "response"], [662, 42, 688, 40], [662, 43, 688, 41, "text"], [662, 47, 688, 45], [662, 48, 688, 46], [662, 49, 688, 47], [663, 10, 689, 8, "console"], [663, 17, 689, 15], [663, 18, 689, 16, "error"], [663, 23, 689, 21], [663, 24, 689, 22], [663, 68, 689, 66], [663, 70, 689, 68, "response"], [663, 78, 689, 76], [663, 79, 689, 77, "status"], [663, 85, 689, 83], [663, 87, 689, 85, "errorText"], [663, 96, 689, 94], [663, 97, 689, 95], [664, 10, 690, 8], [664, 16, 690, 14], [664, 20, 690, 18, "Error"], [664, 25, 690, 23], [664, 26, 690, 24], [664, 48, 690, 46, "response"], [664, 56, 690, 54], [664, 57, 690, 55, "status"], [664, 63, 690, 61], [664, 67, 690, 65, "response"], [664, 75, 690, 73], [664, 76, 690, 74, "statusText"], [664, 86, 690, 84], [664, 88, 690, 86], [664, 89, 690, 87], [665, 8, 691, 6], [666, 8, 693, 6], [666, 14, 693, 12, "result"], [666, 20, 693, 18], [666, 23, 693, 21], [666, 29, 693, 27, "response"], [666, 37, 693, 35], [666, 38, 693, 36, "json"], [666, 42, 693, 40], [666, 43, 693, 41], [666, 44, 693, 42], [667, 8, 694, 6, "console"], [667, 15, 694, 13], [667, 16, 694, 14, "log"], [667, 19, 694, 17], [667, 20, 694, 18], [667, 68, 694, 66], [667, 70, 694, 68, "result"], [667, 76, 694, 74], [667, 77, 694, 75], [668, 8, 696, 6], [668, 12, 696, 10], [668, 13, 696, 11, "result"], [668, 19, 696, 17], [668, 20, 696, 18, "jobId"], [668, 25, 696, 23], [668, 27, 696, 25], [669, 10, 697, 8], [669, 16, 697, 14], [669, 20, 697, 18, "Error"], [669, 25, 697, 23], [669, 26, 697, 24], [669, 70, 697, 68], [669, 71, 697, 69], [670, 8, 698, 6], [672, 8, 700, 6], [673, 8, 701, 6], [673, 14, 701, 12, "pollForCompletion"], [673, 31, 701, 29], [673, 32, 701, 30, "result"], [673, 38, 701, 36], [673, 39, 701, 37, "jobId"], [673, 44, 701, 42], [673, 46, 701, 44, "timestamp"], [673, 55, 701, 53], [673, 56, 701, 54], [674, 6, 702, 4], [674, 7, 702, 5], [674, 8, 702, 6], [674, 15, 702, 13, "error"], [674, 20, 702, 18], [674, 22, 702, 20], [675, 8, 703, 6, "console"], [675, 15, 703, 13], [675, 16, 703, 14, "error"], [675, 21, 703, 19], [675, 22, 703, 20], [675, 57, 703, 55], [675, 59, 703, 57, "error"], [675, 64, 703, 62], [675, 65, 703, 63], [676, 8, 704, 6, "setErrorMessage"], [676, 23, 704, 21], [676, 24, 704, 22], [676, 52, 704, 50, "error"], [676, 57, 704, 55], [676, 58, 704, 56, "message"], [676, 65, 704, 63], [676, 67, 704, 65], [676, 68, 704, 66], [677, 8, 705, 6, "setProcessingState"], [677, 26, 705, 24], [677, 27, 705, 25], [677, 34, 705, 32], [677, 35, 705, 33], [678, 6, 706, 4], [679, 4, 707, 2], [679, 5, 707, 3], [680, 4, 708, 2], [681, 4, 709, 2], [681, 10, 709, 8, "pollForCompletion"], [681, 27, 709, 25], [681, 30, 709, 28], [681, 36, 709, 28, "pollForCompletion"], [681, 37, 709, 35, "jobId"], [681, 42, 709, 48], [681, 44, 709, 50, "timestamp"], [681, 53, 709, 67], [681, 55, 709, 69, "attempts"], [681, 63, 709, 77], [681, 66, 709, 80], [681, 67, 709, 81], [681, 72, 709, 86], [682, 6, 710, 4], [682, 12, 710, 10, "MAX_ATTEMPTS"], [682, 24, 710, 22], [682, 27, 710, 25], [682, 29, 710, 27], [682, 30, 710, 28], [682, 31, 710, 29], [683, 6, 711, 4], [683, 12, 711, 10, "POLL_INTERVAL"], [683, 25, 711, 23], [683, 28, 711, 26], [683, 32, 711, 30], [683, 33, 711, 31], [683, 34, 711, 32], [685, 6, 713, 4, "console"], [685, 13, 713, 11], [685, 14, 713, 12, "log"], [685, 17, 713, 15], [685, 18, 713, 16], [685, 53, 713, 51, "attempts"], [685, 61, 713, 59], [685, 64, 713, 62], [685, 65, 713, 63], [685, 69, 713, 67, "MAX_ATTEMPTS"], [685, 81, 713, 79], [685, 93, 713, 91, "jobId"], [685, 98, 713, 96], [685, 100, 713, 98], [685, 101, 713, 99], [686, 6, 715, 4], [686, 10, 715, 8, "attempts"], [686, 18, 715, 16], [686, 22, 715, 20, "MAX_ATTEMPTS"], [686, 34, 715, 32], [686, 36, 715, 34], [687, 8, 716, 6, "console"], [687, 15, 716, 13], [687, 16, 716, 14, "error"], [687, 21, 716, 19], [687, 22, 716, 20], [687, 75, 716, 73], [687, 76, 716, 74], [688, 8, 717, 6, "setErrorMessage"], [688, 23, 717, 21], [688, 24, 717, 22], [688, 63, 717, 61], [688, 64, 717, 62], [689, 8, 718, 6, "setProcessingState"], [689, 26, 718, 24], [689, 27, 718, 25], [689, 34, 718, 32], [689, 35, 718, 33], [690, 8, 719, 6], [691, 6, 720, 4], [692, 6, 722, 4], [692, 10, 722, 8], [693, 8, 723, 6], [693, 14, 723, 12, "response"], [693, 22, 723, 20], [693, 25, 723, 23], [693, 31, 723, 29, "fetch"], [693, 36, 723, 34], [693, 37, 723, 35], [693, 40, 723, 38, "API_BASE_URL"], [693, 52, 723, 50], [693, 75, 723, 73, "jobId"], [693, 80, 723, 78], [693, 82, 723, 80], [693, 84, 723, 82], [694, 10, 724, 8, "headers"], [694, 17, 724, 15], [694, 19, 724, 17], [695, 12, 725, 10], [695, 27, 725, 25], [695, 29, 725, 27], [695, 39, 725, 37], [695, 45, 725, 43, "getAuthToken"], [695, 57, 725, 55], [695, 58, 725, 56], [695, 59, 725, 57], [696, 10, 726, 8], [697, 8, 727, 6], [697, 9, 727, 7], [697, 10, 727, 8], [698, 8, 729, 6], [698, 12, 729, 10], [698, 13, 729, 11, "response"], [698, 21, 729, 19], [698, 22, 729, 20, "ok"], [698, 24, 729, 22], [698, 26, 729, 24], [699, 10, 730, 8], [699, 16, 730, 14], [699, 20, 730, 18, "Error"], [699, 25, 730, 23], [699, 26, 730, 24], [699, 34, 730, 32, "response"], [699, 42, 730, 40], [699, 43, 730, 41, "status"], [699, 49, 730, 47], [699, 54, 730, 52, "response"], [699, 62, 730, 60], [699, 63, 730, 61, "statusText"], [699, 73, 730, 71], [699, 75, 730, 73], [699, 76, 730, 74], [700, 8, 731, 6], [701, 8, 733, 6], [701, 14, 733, 12, "status"], [701, 20, 733, 18], [701, 23, 733, 21], [701, 29, 733, 27, "response"], [701, 37, 733, 35], [701, 38, 733, 36, "json"], [701, 42, 733, 40], [701, 43, 733, 41], [701, 44, 733, 42], [702, 8, 734, 6, "console"], [702, 15, 734, 13], [702, 16, 734, 14, "log"], [702, 19, 734, 17], [702, 20, 734, 18], [702, 54, 734, 52], [702, 56, 734, 54, "status"], [702, 62, 734, 60], [702, 63, 734, 61], [703, 8, 736, 6], [703, 12, 736, 10, "status"], [703, 18, 736, 16], [703, 19, 736, 17, "status"], [703, 25, 736, 23], [703, 30, 736, 28], [703, 41, 736, 39], [703, 43, 736, 41], [704, 10, 737, 8, "console"], [704, 17, 737, 15], [704, 18, 737, 16, "log"], [704, 21, 737, 19], [704, 22, 737, 20], [704, 73, 737, 71], [704, 74, 737, 72], [705, 10, 738, 8, "setProcessingProgress"], [705, 31, 738, 29], [705, 32, 738, 30], [705, 35, 738, 33], [705, 36, 738, 34], [706, 10, 739, 8, "setProcessingState"], [706, 28, 739, 26], [706, 29, 739, 27], [706, 40, 739, 38], [706, 41, 739, 39], [707, 10, 740, 8], [708, 10, 741, 8], [708, 16, 741, 14, "result"], [708, 22, 741, 20], [708, 25, 741, 23], [709, 12, 742, 10, "imageUrl"], [709, 20, 742, 18], [709, 22, 742, 20, "status"], [709, 28, 742, 26], [709, 29, 742, 27, "publicUrl"], [709, 38, 742, 36], [710, 12, 742, 38], [711, 12, 743, 10, "localUri"], [711, 20, 743, 18], [711, 22, 743, 20, "capturedPhoto"], [711, 35, 743, 33], [711, 39, 743, 37, "status"], [711, 45, 743, 43], [711, 46, 743, 44, "publicUrl"], [711, 55, 743, 53], [712, 12, 743, 55], [713, 12, 744, 10, "challengeCode"], [713, 25, 744, 23], [713, 27, 744, 25, "challengeCode"], [713, 40, 744, 38], [713, 44, 744, 42], [713, 46, 744, 44], [714, 12, 745, 10, "timestamp"], [714, 21, 745, 19], [715, 12, 746, 10, "processingStatus"], [715, 28, 746, 26], [715, 30, 746, 28], [716, 10, 747, 8], [716, 11, 747, 9], [717, 10, 748, 8, "console"], [717, 17, 748, 15], [717, 18, 748, 16, "log"], [717, 21, 748, 19], [717, 22, 748, 20], [717, 57, 748, 55], [717, 59, 748, 57, "result"], [717, 65, 748, 63], [717, 66, 748, 64], [718, 10, 749, 8, "onComplete"], [718, 20, 749, 18], [718, 21, 749, 19, "result"], [718, 27, 749, 25], [718, 28, 749, 26], [719, 10, 750, 8], [720, 8, 751, 6], [720, 9, 751, 7], [720, 15, 751, 13], [720, 19, 751, 17, "status"], [720, 25, 751, 23], [720, 26, 751, 24, "status"], [720, 32, 751, 30], [720, 37, 751, 35], [720, 45, 751, 43], [720, 47, 751, 45], [721, 10, 752, 8, "console"], [721, 17, 752, 15], [721, 18, 752, 16, "error"], [721, 23, 752, 21], [721, 24, 752, 22], [721, 60, 752, 58], [721, 62, 752, 60, "status"], [721, 68, 752, 66], [721, 69, 752, 67, "error"], [721, 74, 752, 72], [721, 75, 752, 73], [722, 10, 753, 8], [722, 16, 753, 14], [722, 20, 753, 18, "Error"], [722, 25, 753, 23], [722, 26, 753, 24, "status"], [722, 32, 753, 30], [722, 33, 753, 31, "error"], [722, 38, 753, 36], [722, 42, 753, 40], [722, 61, 753, 59], [722, 62, 753, 60], [723, 8, 754, 6], [723, 9, 754, 7], [723, 15, 754, 13], [724, 10, 755, 8], [725, 10, 756, 8], [725, 16, 756, 14, "progressValue"], [725, 29, 756, 27], [725, 32, 756, 30], [725, 34, 756, 32], [725, 37, 756, 36, "attempts"], [725, 45, 756, 44], [725, 48, 756, 47, "MAX_ATTEMPTS"], [725, 60, 756, 59], [725, 63, 756, 63], [725, 65, 756, 65], [726, 10, 757, 8, "console"], [726, 17, 757, 15], [726, 18, 757, 16, "log"], [726, 21, 757, 19], [726, 22, 757, 20], [726, 71, 757, 69, "progressValue"], [726, 84, 757, 82], [726, 87, 757, 85], [726, 88, 757, 86], [727, 10, 758, 8, "setProcessingProgress"], [727, 31, 758, 29], [727, 32, 758, 30, "progressValue"], [727, 45, 758, 43], [727, 46, 758, 44], [728, 10, 760, 8, "setTimeout"], [728, 20, 760, 18], [728, 21, 760, 19], [728, 27, 760, 25], [729, 12, 761, 10, "pollForCompletion"], [729, 29, 761, 27], [729, 30, 761, 28, "jobId"], [729, 35, 761, 33], [729, 37, 761, 35, "timestamp"], [729, 46, 761, 44], [729, 48, 761, 46, "attempts"], [729, 56, 761, 54], [729, 59, 761, 57], [729, 60, 761, 58], [729, 61, 761, 59], [730, 10, 762, 8], [730, 11, 762, 9], [730, 13, 762, 11, "POLL_INTERVAL"], [730, 26, 762, 24], [730, 27, 762, 25], [731, 8, 763, 6], [732, 6, 764, 4], [732, 7, 764, 5], [732, 8, 764, 6], [732, 15, 764, 13, "error"], [732, 20, 764, 18], [732, 22, 764, 20], [733, 8, 765, 6, "console"], [733, 15, 765, 13], [733, 16, 765, 14, "error"], [733, 21, 765, 19], [733, 22, 765, 20], [733, 54, 765, 52], [733, 56, 765, 54, "error"], [733, 61, 765, 59], [733, 62, 765, 60], [734, 8, 766, 6, "setErrorMessage"], [734, 23, 766, 21], [734, 24, 766, 22], [734, 62, 766, 60, "error"], [734, 67, 766, 65], [734, 68, 766, 66, "message"], [734, 75, 766, 73], [734, 77, 766, 75], [734, 78, 766, 76], [735, 8, 767, 6, "setProcessingState"], [735, 26, 767, 24], [735, 27, 767, 25], [735, 34, 767, 32], [735, 35, 767, 33], [736, 6, 768, 4], [737, 4, 769, 2], [737, 5, 769, 3], [738, 4, 770, 2], [739, 4, 771, 2], [739, 10, 771, 8, "getAuthToken"], [739, 22, 771, 20], [739, 25, 771, 23], [739, 31, 771, 23, "getAuthToken"], [739, 32, 771, 23], [739, 37, 771, 52], [740, 6, 772, 4], [741, 6, 773, 4], [742, 6, 774, 4], [742, 13, 774, 11], [742, 30, 774, 28], [743, 4, 775, 2], [743, 5, 775, 3], [745, 4, 777, 2], [746, 4, 778, 2], [746, 10, 778, 8, "retryCapture"], [746, 22, 778, 20], [746, 25, 778, 23], [746, 29, 778, 23, "useCallback"], [746, 47, 778, 34], [746, 49, 778, 35], [746, 55, 778, 41], [747, 6, 779, 4, "console"], [747, 13, 779, 11], [747, 14, 779, 12, "log"], [747, 17, 779, 15], [747, 18, 779, 16], [747, 55, 779, 53], [747, 56, 779, 54], [748, 6, 780, 4, "setProcessingState"], [748, 24, 780, 22], [748, 25, 780, 23], [748, 31, 780, 29], [748, 32, 780, 30], [749, 6, 781, 4, "setErrorMessage"], [749, 21, 781, 19], [749, 22, 781, 20], [749, 24, 781, 22], [749, 25, 781, 23], [750, 6, 782, 4, "setCapturedPhoto"], [750, 22, 782, 20], [750, 23, 782, 21], [750, 25, 782, 23], [750, 26, 782, 24], [751, 6, 783, 4, "setProcessingProgress"], [751, 27, 783, 25], [751, 28, 783, 26], [751, 29, 783, 27], [751, 30, 783, 28], [752, 4, 784, 2], [752, 5, 784, 3], [752, 7, 784, 5], [752, 9, 784, 7], [752, 10, 784, 8], [753, 4, 785, 2], [754, 4, 786, 2], [754, 8, 786, 2, "useEffect"], [754, 24, 786, 11], [754, 26, 786, 12], [754, 32, 786, 18], [755, 6, 787, 4, "console"], [755, 13, 787, 11], [755, 14, 787, 12, "log"], [755, 17, 787, 15], [755, 18, 787, 16], [755, 53, 787, 51], [755, 55, 787, 53, "permission"], [755, 65, 787, 63], [755, 66, 787, 64], [756, 6, 788, 4], [756, 10, 788, 8, "permission"], [756, 20, 788, 18], [756, 22, 788, 20], [757, 8, 789, 6, "console"], [757, 15, 789, 13], [757, 16, 789, 14, "log"], [757, 19, 789, 17], [757, 20, 789, 18], [757, 57, 789, 55], [757, 59, 789, 57, "permission"], [757, 69, 789, 67], [757, 70, 789, 68, "granted"], [757, 77, 789, 75], [757, 78, 789, 76], [758, 6, 790, 4], [759, 4, 791, 2], [759, 5, 791, 3], [759, 7, 791, 5], [759, 8, 791, 6, "permission"], [759, 18, 791, 16], [759, 19, 791, 17], [759, 20, 791, 18], [760, 4, 792, 2], [761, 4, 793, 2], [761, 8, 793, 6], [761, 9, 793, 7, "permission"], [761, 19, 793, 17], [761, 21, 793, 19], [762, 6, 794, 4, "console"], [762, 13, 794, 11], [762, 14, 794, 12, "log"], [762, 17, 794, 15], [762, 18, 794, 16], [762, 67, 794, 65], [762, 68, 794, 66], [763, 6, 795, 4], [763, 26, 796, 6], [763, 30, 796, 6, "_jsxDevRuntime"], [763, 44, 796, 6], [763, 45, 796, 6, "jsxDEV"], [763, 51, 796, 6], [763, 53, 796, 7, "_View"], [763, 58, 796, 7], [763, 59, 796, 7, "default"], [763, 66, 796, 11], [764, 8, 796, 12, "style"], [764, 13, 796, 17], [764, 15, 796, 19, "styles"], [764, 21, 796, 25], [764, 22, 796, 26, "container"], [764, 31, 796, 36], [765, 8, 796, 36, "children"], [765, 16, 796, 36], [765, 32, 797, 8], [765, 36, 797, 8, "_jsxDevRuntime"], [765, 50, 797, 8], [765, 51, 797, 8, "jsxDEV"], [765, 57, 797, 8], [765, 59, 797, 9, "_ActivityIndicator"], [765, 77, 797, 9], [765, 78, 797, 9, "default"], [765, 85, 797, 26], [766, 10, 797, 27, "size"], [766, 14, 797, 31], [766, 16, 797, 32], [766, 23, 797, 39], [767, 10, 797, 40, "color"], [767, 15, 797, 45], [767, 17, 797, 46], [768, 8, 797, 55], [769, 10, 797, 55, "fileName"], [769, 18, 797, 55], [769, 20, 797, 55, "_jsxFileName"], [769, 32, 797, 55], [770, 10, 797, 55, "lineNumber"], [770, 20, 797, 55], [771, 10, 797, 55, "columnNumber"], [771, 22, 797, 55], [772, 8, 797, 55], [772, 15, 797, 57], [772, 16, 797, 58], [772, 31, 798, 8], [772, 35, 798, 8, "_jsxDevRuntime"], [772, 49, 798, 8], [772, 50, 798, 8, "jsxDEV"], [772, 56, 798, 8], [772, 58, 798, 9, "_Text"], [772, 63, 798, 9], [772, 64, 798, 9, "default"], [772, 71, 798, 13], [773, 10, 798, 14, "style"], [773, 15, 798, 19], [773, 17, 798, 21, "styles"], [773, 23, 798, 27], [773, 24, 798, 28, "loadingText"], [773, 35, 798, 40], [774, 10, 798, 40, "children"], [774, 18, 798, 40], [774, 20, 798, 41], [775, 8, 798, 58], [776, 10, 798, 58, "fileName"], [776, 18, 798, 58], [776, 20, 798, 58, "_jsxFileName"], [776, 32, 798, 58], [777, 10, 798, 58, "lineNumber"], [777, 20, 798, 58], [778, 10, 798, 58, "columnNumber"], [778, 22, 798, 58], [779, 8, 798, 58], [779, 15, 798, 64], [779, 16, 798, 65], [780, 6, 798, 65], [781, 8, 798, 65, "fileName"], [781, 16, 798, 65], [781, 18, 798, 65, "_jsxFileName"], [781, 30, 798, 65], [782, 8, 798, 65, "lineNumber"], [782, 18, 798, 65], [783, 8, 798, 65, "columnNumber"], [783, 20, 798, 65], [784, 6, 798, 65], [784, 13, 799, 12], [784, 14, 799, 13], [785, 4, 801, 2], [786, 4, 802, 2], [786, 8, 802, 6], [786, 9, 802, 7, "permission"], [786, 19, 802, 17], [786, 20, 802, 18, "granted"], [786, 27, 802, 25], [786, 29, 802, 27], [787, 6, 803, 4, "console"], [787, 13, 803, 11], [787, 14, 803, 12, "log"], [787, 17, 803, 15], [787, 18, 803, 16], [787, 93, 803, 91], [787, 94, 803, 92], [788, 6, 804, 4], [788, 26, 805, 6], [788, 30, 805, 6, "_jsxDevRuntime"], [788, 44, 805, 6], [788, 45, 805, 6, "jsxDEV"], [788, 51, 805, 6], [788, 53, 805, 7, "_View"], [788, 58, 805, 7], [788, 59, 805, 7, "default"], [788, 66, 805, 11], [789, 8, 805, 12, "style"], [789, 13, 805, 17], [789, 15, 805, 19, "styles"], [789, 21, 805, 25], [789, 22, 805, 26, "container"], [789, 31, 805, 36], [790, 8, 805, 36, "children"], [790, 16, 805, 36], [790, 31, 806, 8], [790, 35, 806, 8, "_jsxDevRuntime"], [790, 49, 806, 8], [790, 50, 806, 8, "jsxDEV"], [790, 56, 806, 8], [790, 58, 806, 9, "_View"], [790, 63, 806, 9], [790, 64, 806, 9, "default"], [790, 71, 806, 13], [791, 10, 806, 14, "style"], [791, 15, 806, 19], [791, 17, 806, 21, "styles"], [791, 23, 806, 27], [791, 24, 806, 28, "permissionContent"], [791, 41, 806, 46], [792, 10, 806, 46, "children"], [792, 18, 806, 46], [792, 34, 807, 10], [792, 38, 807, 10, "_jsxDevRuntime"], [792, 52, 807, 10], [792, 53, 807, 10, "jsxDEV"], [792, 59, 807, 10], [792, 61, 807, 11, "_lucideReactNative"], [792, 79, 807, 11], [792, 80, 807, 11, "Camera"], [792, 86, 807, 21], [793, 12, 807, 22, "size"], [793, 16, 807, 26], [793, 18, 807, 28], [793, 20, 807, 31], [794, 12, 807, 32, "color"], [794, 17, 807, 37], [794, 19, 807, 38], [795, 10, 807, 47], [796, 12, 807, 47, "fileName"], [796, 20, 807, 47], [796, 22, 807, 47, "_jsxFileName"], [796, 34, 807, 47], [797, 12, 807, 47, "lineNumber"], [797, 22, 807, 47], [798, 12, 807, 47, "columnNumber"], [798, 24, 807, 47], [799, 10, 807, 47], [799, 17, 807, 49], [799, 18, 807, 50], [799, 33, 808, 10], [799, 37, 808, 10, "_jsxDevRuntime"], [799, 51, 808, 10], [799, 52, 808, 10, "jsxDEV"], [799, 58, 808, 10], [799, 60, 808, 11, "_Text"], [799, 65, 808, 11], [799, 66, 808, 11, "default"], [799, 73, 808, 15], [800, 12, 808, 16, "style"], [800, 17, 808, 21], [800, 19, 808, 23, "styles"], [800, 25, 808, 29], [800, 26, 808, 30, "permissionTitle"], [800, 41, 808, 46], [801, 12, 808, 46, "children"], [801, 20, 808, 46], [801, 22, 808, 47], [802, 10, 808, 73], [803, 12, 808, 73, "fileName"], [803, 20, 808, 73], [803, 22, 808, 73, "_jsxFileName"], [803, 34, 808, 73], [804, 12, 808, 73, "lineNumber"], [804, 22, 808, 73], [805, 12, 808, 73, "columnNumber"], [805, 24, 808, 73], [806, 10, 808, 73], [806, 17, 808, 79], [806, 18, 808, 80], [806, 33, 809, 10], [806, 37, 809, 10, "_jsxDevRuntime"], [806, 51, 809, 10], [806, 52, 809, 10, "jsxDEV"], [806, 58, 809, 10], [806, 60, 809, 11, "_Text"], [806, 65, 809, 11], [806, 66, 809, 11, "default"], [806, 73, 809, 15], [807, 12, 809, 16, "style"], [807, 17, 809, 21], [807, 19, 809, 23, "styles"], [807, 25, 809, 29], [807, 26, 809, 30, "permissionDescription"], [807, 47, 809, 52], [808, 12, 809, 52, "children"], [808, 20, 809, 52], [808, 22, 809, 53], [809, 10, 812, 10], [810, 12, 812, 10, "fileName"], [810, 20, 812, 10], [810, 22, 812, 10, "_jsxFileName"], [810, 34, 812, 10], [811, 12, 812, 10, "lineNumber"], [811, 22, 812, 10], [812, 12, 812, 10, "columnNumber"], [812, 24, 812, 10], [813, 10, 812, 10], [813, 17, 812, 16], [813, 18, 812, 17], [813, 33, 813, 10], [813, 37, 813, 10, "_jsxDevRuntime"], [813, 51, 813, 10], [813, 52, 813, 10, "jsxDEV"], [813, 58, 813, 10], [813, 60, 813, 11, "_TouchableOpacity"], [813, 77, 813, 11], [813, 78, 813, 11, "default"], [813, 85, 813, 27], [814, 12, 813, 28, "onPress"], [814, 19, 813, 35], [814, 21, 813, 37, "requestPermission"], [814, 38, 813, 55], [815, 12, 813, 56, "style"], [815, 17, 813, 61], [815, 19, 813, 63, "styles"], [815, 25, 813, 69], [815, 26, 813, 70, "primaryButton"], [815, 39, 813, 84], [816, 12, 813, 84, "children"], [816, 20, 813, 84], [816, 35, 814, 12], [816, 39, 814, 12, "_jsxDevRuntime"], [816, 53, 814, 12], [816, 54, 814, 12, "jsxDEV"], [816, 60, 814, 12], [816, 62, 814, 13, "_Text"], [816, 67, 814, 13], [816, 68, 814, 13, "default"], [816, 75, 814, 17], [817, 14, 814, 18, "style"], [817, 19, 814, 23], [817, 21, 814, 25, "styles"], [817, 27, 814, 31], [817, 28, 814, 32, "primaryButtonText"], [817, 45, 814, 50], [818, 14, 814, 50, "children"], [818, 22, 814, 50], [818, 24, 814, 51], [819, 12, 814, 67], [820, 14, 814, 67, "fileName"], [820, 22, 814, 67], [820, 24, 814, 67, "_jsxFileName"], [820, 36, 814, 67], [821, 14, 814, 67, "lineNumber"], [821, 24, 814, 67], [822, 14, 814, 67, "columnNumber"], [822, 26, 814, 67], [823, 12, 814, 67], [823, 19, 814, 73], [824, 10, 814, 74], [825, 12, 814, 74, "fileName"], [825, 20, 814, 74], [825, 22, 814, 74, "_jsxFileName"], [825, 34, 814, 74], [826, 12, 814, 74, "lineNumber"], [826, 22, 814, 74], [827, 12, 814, 74, "columnNumber"], [827, 24, 814, 74], [828, 10, 814, 74], [828, 17, 815, 28], [828, 18, 815, 29], [828, 33, 816, 10], [828, 37, 816, 10, "_jsxDevRuntime"], [828, 51, 816, 10], [828, 52, 816, 10, "jsxDEV"], [828, 58, 816, 10], [828, 60, 816, 11, "_TouchableOpacity"], [828, 77, 816, 11], [828, 78, 816, 11, "default"], [828, 85, 816, 27], [829, 12, 816, 28, "onPress"], [829, 19, 816, 35], [829, 21, 816, 37, "onCancel"], [829, 29, 816, 46], [830, 12, 816, 47, "style"], [830, 17, 816, 52], [830, 19, 816, 54, "styles"], [830, 25, 816, 60], [830, 26, 816, 61, "secondaryButton"], [830, 41, 816, 77], [831, 12, 816, 77, "children"], [831, 20, 816, 77], [831, 35, 817, 12], [831, 39, 817, 12, "_jsxDevRuntime"], [831, 53, 817, 12], [831, 54, 817, 12, "jsxDEV"], [831, 60, 817, 12], [831, 62, 817, 13, "_Text"], [831, 67, 817, 13], [831, 68, 817, 13, "default"], [831, 75, 817, 17], [832, 14, 817, 18, "style"], [832, 19, 817, 23], [832, 21, 817, 25, "styles"], [832, 27, 817, 31], [832, 28, 817, 32, "secondaryButtonText"], [832, 47, 817, 52], [833, 14, 817, 52, "children"], [833, 22, 817, 52], [833, 24, 817, 53], [834, 12, 817, 59], [835, 14, 817, 59, "fileName"], [835, 22, 817, 59], [835, 24, 817, 59, "_jsxFileName"], [835, 36, 817, 59], [836, 14, 817, 59, "lineNumber"], [836, 24, 817, 59], [837, 14, 817, 59, "columnNumber"], [837, 26, 817, 59], [838, 12, 817, 59], [838, 19, 817, 65], [839, 10, 817, 66], [840, 12, 817, 66, "fileName"], [840, 20, 817, 66], [840, 22, 817, 66, "_jsxFileName"], [840, 34, 817, 66], [841, 12, 817, 66, "lineNumber"], [841, 22, 817, 66], [842, 12, 817, 66, "columnNumber"], [842, 24, 817, 66], [843, 10, 817, 66], [843, 17, 818, 28], [843, 18, 818, 29], [844, 8, 818, 29], [845, 10, 818, 29, "fileName"], [845, 18, 818, 29], [845, 20, 818, 29, "_jsxFileName"], [845, 32, 818, 29], [846, 10, 818, 29, "lineNumber"], [846, 20, 818, 29], [847, 10, 818, 29, "columnNumber"], [847, 22, 818, 29], [848, 8, 818, 29], [848, 15, 819, 14], [849, 6, 819, 15], [850, 8, 819, 15, "fileName"], [850, 16, 819, 15], [850, 18, 819, 15, "_jsxFileName"], [850, 30, 819, 15], [851, 8, 819, 15, "lineNumber"], [851, 18, 819, 15], [852, 8, 819, 15, "columnNumber"], [852, 20, 819, 15], [853, 6, 819, 15], [853, 13, 820, 12], [853, 14, 820, 13], [854, 4, 822, 2], [855, 4, 823, 2], [856, 4, 824, 2, "console"], [856, 11, 824, 9], [856, 12, 824, 10, "log"], [856, 15, 824, 13], [856, 16, 824, 14], [856, 55, 824, 53], [856, 56, 824, 54], [857, 4, 826, 2], [857, 24, 827, 4], [857, 28, 827, 4, "_jsxDevRuntime"], [857, 42, 827, 4], [857, 43, 827, 4, "jsxDEV"], [857, 49, 827, 4], [857, 51, 827, 5, "_View"], [857, 56, 827, 5], [857, 57, 827, 5, "default"], [857, 64, 827, 9], [858, 6, 827, 10, "style"], [858, 11, 827, 15], [858, 13, 827, 17, "styles"], [858, 19, 827, 23], [858, 20, 827, 24, "container"], [858, 29, 827, 34], [859, 6, 827, 34, "children"], [859, 14, 827, 34], [859, 30, 829, 6], [859, 34, 829, 6, "_jsxDevRuntime"], [859, 48, 829, 6], [859, 49, 829, 6, "jsxDEV"], [859, 55, 829, 6], [859, 57, 829, 7, "_View"], [859, 62, 829, 7], [859, 63, 829, 7, "default"], [859, 70, 829, 11], [860, 8, 829, 12, "style"], [860, 13, 829, 17], [860, 15, 829, 19, "styles"], [860, 21, 829, 25], [860, 22, 829, 26, "cameraContainer"], [860, 37, 829, 42], [861, 8, 829, 43, "id"], [861, 10, 829, 45], [861, 12, 829, 46], [861, 29, 829, 63], [862, 8, 829, 63, "children"], [862, 16, 829, 63], [862, 32, 830, 8], [862, 36, 830, 8, "_jsxDevRuntime"], [862, 50, 830, 8], [862, 51, 830, 8, "jsxDEV"], [862, 57, 830, 8], [862, 59, 830, 9, "_expoCamera"], [862, 70, 830, 9], [862, 71, 830, 9, "CameraView"], [862, 81, 830, 19], [863, 10, 831, 10, "ref"], [863, 13, 831, 13], [863, 15, 831, 15, "cameraRef"], [863, 24, 831, 25], [864, 10, 832, 10, "style"], [864, 15, 832, 15], [864, 17, 832, 17], [864, 18, 832, 18, "styles"], [864, 24, 832, 24], [864, 25, 832, 25, "camera"], [864, 31, 832, 31], [864, 33, 832, 33], [865, 12, 832, 35, "backgroundColor"], [865, 27, 832, 50], [865, 29, 832, 52], [866, 10, 832, 62], [866, 11, 832, 63], [866, 12, 832, 65], [867, 10, 833, 10, "facing"], [867, 16, 833, 16], [867, 18, 833, 17], [867, 24, 833, 23], [868, 10, 834, 10, "onLayout"], [868, 18, 834, 18], [868, 20, 834, 21, "e"], [868, 21, 834, 22], [868, 25, 834, 27], [869, 12, 835, 12, "console"], [869, 19, 835, 19], [869, 20, 835, 20, "log"], [869, 23, 835, 23], [869, 24, 835, 24], [869, 56, 835, 56], [869, 58, 835, 58, "e"], [869, 59, 835, 59], [869, 60, 835, 60, "nativeEvent"], [869, 71, 835, 71], [869, 72, 835, 72, "layout"], [869, 78, 835, 78], [869, 79, 835, 79], [870, 12, 836, 12, "setViewSize"], [870, 23, 836, 23], [870, 24, 836, 24], [871, 14, 836, 26, "width"], [871, 19, 836, 31], [871, 21, 836, 33, "e"], [871, 22, 836, 34], [871, 23, 836, 35, "nativeEvent"], [871, 34, 836, 46], [871, 35, 836, 47, "layout"], [871, 41, 836, 53], [871, 42, 836, 54, "width"], [871, 47, 836, 59], [872, 14, 836, 61, "height"], [872, 20, 836, 67], [872, 22, 836, 69, "e"], [872, 23, 836, 70], [872, 24, 836, 71, "nativeEvent"], [872, 35, 836, 82], [872, 36, 836, 83, "layout"], [872, 42, 836, 89], [872, 43, 836, 90, "height"], [873, 12, 836, 97], [873, 13, 836, 98], [873, 14, 836, 99], [874, 10, 837, 10], [874, 11, 837, 12], [875, 10, 838, 10, "onCameraReady"], [875, 23, 838, 23], [875, 25, 838, 25, "onCameraReady"], [875, 26, 838, 25], [875, 31, 838, 31], [876, 12, 839, 12, "console"], [876, 19, 839, 19], [876, 20, 839, 20, "log"], [876, 23, 839, 23], [876, 24, 839, 24], [876, 55, 839, 55], [876, 56, 839, 56], [877, 12, 840, 12, "setIsCameraReady"], [877, 28, 840, 28], [877, 29, 840, 29], [877, 33, 840, 33], [877, 34, 840, 34], [877, 35, 840, 35], [877, 36, 840, 36], [878, 10, 841, 10], [878, 11, 841, 12], [879, 10, 842, 10, "onMountError"], [879, 22, 842, 22], [879, 24, 842, 25, "error"], [879, 29, 842, 30], [879, 33, 842, 35], [880, 12, 843, 12, "console"], [880, 19, 843, 19], [880, 20, 843, 20, "error"], [880, 25, 843, 25], [880, 26, 843, 26], [880, 63, 843, 63], [880, 65, 843, 65, "error"], [880, 70, 843, 70], [880, 71, 843, 71], [881, 12, 844, 12, "setErrorMessage"], [881, 27, 844, 27], [881, 28, 844, 28], [881, 57, 844, 57], [881, 58, 844, 58], [882, 12, 845, 12, "setProcessingState"], [882, 30, 845, 30], [882, 31, 845, 31], [882, 38, 845, 38], [882, 39, 845, 39], [883, 10, 846, 10], [884, 8, 846, 12], [885, 10, 846, 12, "fileName"], [885, 18, 846, 12], [885, 20, 846, 12, "_jsxFileName"], [885, 32, 846, 12], [886, 10, 846, 12, "lineNumber"], [886, 20, 846, 12], [887, 10, 846, 12, "columnNumber"], [887, 22, 846, 12], [888, 8, 846, 12], [888, 15, 847, 9], [888, 16, 847, 10], [888, 18, 849, 9], [888, 19, 849, 10, "isCameraReady"], [888, 32, 849, 23], [888, 49, 850, 10], [888, 53, 850, 10, "_jsxDevRuntime"], [888, 67, 850, 10], [888, 68, 850, 10, "jsxDEV"], [888, 74, 850, 10], [888, 76, 850, 11, "_View"], [888, 81, 850, 11], [888, 82, 850, 11, "default"], [888, 89, 850, 15], [889, 10, 850, 16, "style"], [889, 15, 850, 21], [889, 17, 850, 23], [889, 18, 850, 24, "StyleSheet"], [889, 37, 850, 34], [889, 38, 850, 35, "absoluteFill"], [889, 50, 850, 47], [889, 52, 850, 49], [890, 12, 850, 51, "backgroundColor"], [890, 27, 850, 66], [890, 29, 850, 68], [890, 49, 850, 88], [891, 12, 850, 90, "justifyContent"], [891, 26, 850, 104], [891, 28, 850, 106], [891, 36, 850, 114], [892, 12, 850, 116, "alignItems"], [892, 22, 850, 126], [892, 24, 850, 128], [892, 32, 850, 136], [893, 12, 850, 138, "zIndex"], [893, 18, 850, 144], [893, 20, 850, 146], [894, 10, 850, 151], [894, 11, 850, 152], [894, 12, 850, 154], [895, 10, 850, 154, "children"], [895, 18, 850, 154], [895, 33, 851, 12], [895, 37, 851, 12, "_jsxDevRuntime"], [895, 51, 851, 12], [895, 52, 851, 12, "jsxDEV"], [895, 58, 851, 12], [895, 60, 851, 13, "_View"], [895, 65, 851, 13], [895, 66, 851, 13, "default"], [895, 73, 851, 17], [896, 12, 851, 18, "style"], [896, 17, 851, 23], [896, 19, 851, 25], [897, 14, 851, 27, "backgroundColor"], [897, 29, 851, 42], [897, 31, 851, 44], [897, 51, 851, 64], [898, 14, 851, 66, "padding"], [898, 21, 851, 73], [898, 23, 851, 75], [898, 25, 851, 77], [899, 14, 851, 79, "borderRadius"], [899, 26, 851, 91], [899, 28, 851, 93], [899, 30, 851, 95], [900, 14, 851, 97, "alignItems"], [900, 24, 851, 107], [900, 26, 851, 109], [901, 12, 851, 118], [901, 13, 851, 120], [902, 12, 851, 120, "children"], [902, 20, 851, 120], [902, 36, 852, 14], [902, 40, 852, 14, "_jsxDevRuntime"], [902, 54, 852, 14], [902, 55, 852, 14, "jsxDEV"], [902, 61, 852, 14], [902, 63, 852, 15, "_ActivityIndicator"], [902, 81, 852, 15], [902, 82, 852, 15, "default"], [902, 89, 852, 32], [903, 14, 852, 33, "size"], [903, 18, 852, 37], [903, 20, 852, 38], [903, 27, 852, 45], [904, 14, 852, 46, "color"], [904, 19, 852, 51], [904, 21, 852, 52], [904, 30, 852, 61], [905, 14, 852, 62, "style"], [905, 19, 852, 67], [905, 21, 852, 69], [906, 16, 852, 71, "marginBottom"], [906, 28, 852, 83], [906, 30, 852, 85], [907, 14, 852, 88], [908, 12, 852, 90], [909, 14, 852, 90, "fileName"], [909, 22, 852, 90], [909, 24, 852, 90, "_jsxFileName"], [909, 36, 852, 90], [910, 14, 852, 90, "lineNumber"], [910, 24, 852, 90], [911, 14, 852, 90, "columnNumber"], [911, 26, 852, 90], [912, 12, 852, 90], [912, 19, 852, 92], [912, 20, 852, 93], [912, 35, 853, 14], [912, 39, 853, 14, "_jsxDevRuntime"], [912, 53, 853, 14], [912, 54, 853, 14, "jsxDEV"], [912, 60, 853, 14], [912, 62, 853, 15, "_Text"], [912, 67, 853, 15], [912, 68, 853, 15, "default"], [912, 75, 853, 19], [913, 14, 853, 20, "style"], [913, 19, 853, 25], [913, 21, 853, 27], [914, 16, 853, 29, "color"], [914, 21, 853, 34], [914, 23, 853, 36], [914, 29, 853, 42], [915, 16, 853, 44, "fontSize"], [915, 24, 853, 52], [915, 26, 853, 54], [915, 28, 853, 56], [916, 16, 853, 58, "fontWeight"], [916, 26, 853, 68], [916, 28, 853, 70], [917, 14, 853, 76], [917, 15, 853, 78], [918, 14, 853, 78, "children"], [918, 22, 853, 78], [918, 24, 853, 79], [919, 12, 853, 101], [920, 14, 853, 101, "fileName"], [920, 22, 853, 101], [920, 24, 853, 101, "_jsxFileName"], [920, 36, 853, 101], [921, 14, 853, 101, "lineNumber"], [921, 24, 853, 101], [922, 14, 853, 101, "columnNumber"], [922, 26, 853, 101], [923, 12, 853, 101], [923, 19, 853, 107], [923, 20, 853, 108], [923, 35, 854, 14], [923, 39, 854, 14, "_jsxDevRuntime"], [923, 53, 854, 14], [923, 54, 854, 14, "jsxDEV"], [923, 60, 854, 14], [923, 62, 854, 15, "_Text"], [923, 67, 854, 15], [923, 68, 854, 15, "default"], [923, 75, 854, 19], [924, 14, 854, 20, "style"], [924, 19, 854, 25], [924, 21, 854, 27], [925, 16, 854, 29, "color"], [925, 21, 854, 34], [925, 23, 854, 36], [925, 32, 854, 45], [926, 16, 854, 47, "fontSize"], [926, 24, 854, 55], [926, 26, 854, 57], [926, 28, 854, 59], [927, 16, 854, 61, "marginTop"], [927, 25, 854, 70], [927, 27, 854, 72], [928, 14, 854, 74], [928, 15, 854, 76], [929, 14, 854, 76, "children"], [929, 22, 854, 76], [929, 24, 854, 77], [930, 12, 854, 88], [931, 14, 854, 88, "fileName"], [931, 22, 854, 88], [931, 24, 854, 88, "_jsxFileName"], [931, 36, 854, 88], [932, 14, 854, 88, "lineNumber"], [932, 24, 854, 88], [933, 14, 854, 88, "columnNumber"], [933, 26, 854, 88], [934, 12, 854, 88], [934, 19, 854, 94], [934, 20, 854, 95], [935, 10, 854, 95], [936, 12, 854, 95, "fileName"], [936, 20, 854, 95], [936, 22, 854, 95, "_jsxFileName"], [936, 34, 854, 95], [937, 12, 854, 95, "lineNumber"], [937, 22, 854, 95], [938, 12, 854, 95, "columnNumber"], [938, 24, 854, 95], [939, 10, 854, 95], [939, 17, 855, 18], [940, 8, 855, 19], [941, 10, 855, 19, "fileName"], [941, 18, 855, 19], [941, 20, 855, 19, "_jsxFileName"], [941, 32, 855, 19], [942, 10, 855, 19, "lineNumber"], [942, 20, 855, 19], [943, 10, 855, 19, "columnNumber"], [943, 22, 855, 19], [944, 8, 855, 19], [944, 15, 856, 16], [944, 16, 857, 9], [944, 18, 860, 9, "isCameraReady"], [944, 31, 860, 22], [944, 35, 860, 26, "previewBlurEnabled"], [944, 53, 860, 44], [944, 57, 860, 48, "viewSize"], [944, 65, 860, 56], [944, 66, 860, 57, "width"], [944, 71, 860, 62], [944, 74, 860, 65], [944, 75, 860, 66], [944, 92, 861, 10], [944, 96, 861, 10, "_jsxDevRuntime"], [944, 110, 861, 10], [944, 111, 861, 10, "jsxDEV"], [944, 117, 861, 10], [944, 119, 861, 10, "_jsxDevRuntime"], [944, 133, 861, 10], [944, 134, 861, 10, "Fragment"], [944, 142, 861, 10], [945, 10, 861, 10, "children"], [945, 18, 861, 10], [945, 34, 863, 12], [945, 38, 863, 12, "_jsxDevRuntime"], [945, 52, 863, 12], [945, 53, 863, 12, "jsxDEV"], [945, 59, 863, 12], [945, 61, 863, 13, "_LiveFaceCanvas"], [945, 76, 863, 13], [945, 77, 863, 13, "default"], [945, 84, 863, 27], [946, 12, 863, 28, "containerId"], [946, 23, 863, 39], [946, 25, 863, 40], [946, 42, 863, 57], [947, 12, 863, 58, "width"], [947, 17, 863, 63], [947, 19, 863, 65, "viewSize"], [947, 27, 863, 73], [947, 28, 863, 74, "width"], [947, 33, 863, 80], [948, 12, 863, 81, "height"], [948, 18, 863, 87], [948, 20, 863, 89, "viewSize"], [948, 28, 863, 97], [948, 29, 863, 98, "height"], [949, 10, 863, 105], [950, 12, 863, 105, "fileName"], [950, 20, 863, 105], [950, 22, 863, 105, "_jsxFileName"], [950, 34, 863, 105], [951, 12, 863, 105, "lineNumber"], [951, 22, 863, 105], [952, 12, 863, 105, "columnNumber"], [952, 24, 863, 105], [953, 10, 863, 105], [953, 17, 863, 107], [953, 18, 863, 108], [953, 33, 864, 12], [953, 37, 864, 12, "_jsxDevRuntime"], [953, 51, 864, 12], [953, 52, 864, 12, "jsxDEV"], [953, 58, 864, 12], [953, 60, 864, 13, "_View"], [953, 65, 864, 13], [953, 66, 864, 13, "default"], [953, 73, 864, 17], [954, 12, 864, 18, "style"], [954, 17, 864, 23], [954, 19, 864, 25], [954, 20, 864, 26, "StyleSheet"], [954, 39, 864, 36], [954, 40, 864, 37, "absoluteFill"], [954, 52, 864, 49], [954, 54, 864, 51], [955, 14, 864, 53, "pointerEvents"], [955, 27, 864, 66], [955, 29, 864, 68], [956, 12, 864, 75], [956, 13, 864, 76], [956, 14, 864, 78], [957, 12, 864, 78, "children"], [957, 20, 864, 78], [957, 36, 866, 12], [957, 40, 866, 12, "_jsxDevRuntime"], [957, 54, 866, 12], [957, 55, 866, 12, "jsxDEV"], [957, 61, 866, 12], [957, 63, 866, 13, "_expoBlur"], [957, 72, 866, 13], [957, 73, 866, 13, "BlurView"], [957, 81, 866, 21], [958, 14, 866, 22, "intensity"], [958, 23, 866, 31], [958, 25, 866, 33], [958, 27, 866, 36], [959, 14, 866, 37, "tint"], [959, 18, 866, 41], [959, 20, 866, 42], [959, 26, 866, 48], [960, 14, 866, 49, "style"], [960, 19, 866, 54], [960, 21, 866, 56], [960, 22, 866, 57, "styles"], [960, 28, 866, 63], [960, 29, 866, 64, "blurZone"], [960, 37, 866, 72], [960, 39, 866, 74], [961, 16, 867, 14, "left"], [961, 20, 867, 18], [961, 22, 867, 20], [961, 23, 867, 21], [962, 16, 868, 14, "top"], [962, 19, 868, 17], [962, 21, 868, 19, "viewSize"], [962, 29, 868, 27], [962, 30, 868, 28, "height"], [962, 36, 868, 34], [962, 39, 868, 37], [962, 42, 868, 40], [963, 16, 869, 14, "width"], [963, 21, 869, 19], [963, 23, 869, 21, "viewSize"], [963, 31, 869, 29], [963, 32, 869, 30, "width"], [963, 37, 869, 35], [964, 16, 870, 14, "height"], [964, 22, 870, 20], [964, 24, 870, 22, "viewSize"], [964, 32, 870, 30], [964, 33, 870, 31, "height"], [964, 39, 870, 37], [964, 42, 870, 40], [964, 46, 870, 44], [965, 16, 871, 14, "borderRadius"], [965, 28, 871, 26], [965, 30, 871, 28], [966, 14, 872, 12], [966, 15, 872, 13], [967, 12, 872, 15], [968, 14, 872, 15, "fileName"], [968, 22, 872, 15], [968, 24, 872, 15, "_jsxFileName"], [968, 36, 872, 15], [969, 14, 872, 15, "lineNumber"], [969, 24, 872, 15], [970, 14, 872, 15, "columnNumber"], [970, 26, 872, 15], [971, 12, 872, 15], [971, 19, 872, 17], [971, 20, 872, 18], [971, 35, 874, 12], [971, 39, 874, 12, "_jsxDevRuntime"], [971, 53, 874, 12], [971, 54, 874, 12, "jsxDEV"], [971, 60, 874, 12], [971, 62, 874, 13, "_expoBlur"], [971, 71, 874, 13], [971, 72, 874, 13, "BlurView"], [971, 80, 874, 21], [972, 14, 874, 22, "intensity"], [972, 23, 874, 31], [972, 25, 874, 33], [972, 27, 874, 36], [973, 14, 874, 37, "tint"], [973, 18, 874, 41], [973, 20, 874, 42], [973, 26, 874, 48], [974, 14, 874, 49, "style"], [974, 19, 874, 54], [974, 21, 874, 56], [974, 22, 874, 57, "styles"], [974, 28, 874, 63], [974, 29, 874, 64, "blurZone"], [974, 37, 874, 72], [974, 39, 874, 74], [975, 16, 875, 14, "left"], [975, 20, 875, 18], [975, 22, 875, 20], [975, 23, 875, 21], [976, 16, 876, 14, "top"], [976, 19, 876, 17], [976, 21, 876, 19], [976, 22, 876, 20], [977, 16, 877, 14, "width"], [977, 21, 877, 19], [977, 23, 877, 21, "viewSize"], [977, 31, 877, 29], [977, 32, 877, 30, "width"], [977, 37, 877, 35], [978, 16, 878, 14, "height"], [978, 22, 878, 20], [978, 24, 878, 22, "viewSize"], [978, 32, 878, 30], [978, 33, 878, 31, "height"], [978, 39, 878, 37], [978, 42, 878, 40], [978, 45, 878, 43], [979, 16, 879, 14, "borderRadius"], [979, 28, 879, 26], [979, 30, 879, 28], [980, 14, 880, 12], [980, 15, 880, 13], [981, 12, 880, 15], [982, 14, 880, 15, "fileName"], [982, 22, 880, 15], [982, 24, 880, 15, "_jsxFileName"], [982, 36, 880, 15], [983, 14, 880, 15, "lineNumber"], [983, 24, 880, 15], [984, 14, 880, 15, "columnNumber"], [984, 26, 880, 15], [985, 12, 880, 15], [985, 19, 880, 17], [985, 20, 880, 18], [985, 35, 882, 12], [985, 39, 882, 12, "_jsxDevRuntime"], [985, 53, 882, 12], [985, 54, 882, 12, "jsxDEV"], [985, 60, 882, 12], [985, 62, 882, 13, "_expoBlur"], [985, 71, 882, 13], [985, 72, 882, 13, "BlurView"], [985, 80, 882, 21], [986, 14, 882, 22, "intensity"], [986, 23, 882, 31], [986, 25, 882, 33], [986, 27, 882, 36], [987, 14, 882, 37, "tint"], [987, 18, 882, 41], [987, 20, 882, 42], [987, 26, 882, 48], [988, 14, 882, 49, "style"], [988, 19, 882, 54], [988, 21, 882, 56], [988, 22, 882, 57, "styles"], [988, 28, 882, 63], [988, 29, 882, 64, "blurZone"], [988, 37, 882, 72], [988, 39, 882, 74], [989, 16, 883, 14, "left"], [989, 20, 883, 18], [989, 22, 883, 20, "viewSize"], [989, 30, 883, 28], [989, 31, 883, 29, "width"], [989, 36, 883, 34], [989, 39, 883, 37], [989, 42, 883, 40], [989, 45, 883, 44, "viewSize"], [989, 53, 883, 52], [989, 54, 883, 53, "width"], [989, 59, 883, 58], [989, 62, 883, 61], [989, 66, 883, 66], [990, 16, 884, 14, "top"], [990, 19, 884, 17], [990, 21, 884, 19, "viewSize"], [990, 29, 884, 27], [990, 30, 884, 28, "height"], [990, 36, 884, 34], [990, 39, 884, 37], [990, 43, 884, 41], [990, 46, 884, 45, "viewSize"], [990, 54, 884, 53], [990, 55, 884, 54, "width"], [990, 60, 884, 59], [990, 63, 884, 62], [990, 67, 884, 67], [991, 16, 885, 14, "width"], [991, 21, 885, 19], [991, 23, 885, 21, "viewSize"], [991, 31, 885, 29], [991, 32, 885, 30, "width"], [991, 37, 885, 35], [991, 40, 885, 38], [991, 43, 885, 41], [992, 16, 886, 14, "height"], [992, 22, 886, 20], [992, 24, 886, 22, "viewSize"], [992, 32, 886, 30], [992, 33, 886, 31, "width"], [992, 38, 886, 36], [992, 41, 886, 39], [992, 44, 886, 42], [993, 16, 887, 14, "borderRadius"], [993, 28, 887, 26], [993, 30, 887, 29, "viewSize"], [993, 38, 887, 37], [993, 39, 887, 38, "width"], [993, 44, 887, 43], [993, 47, 887, 46], [993, 50, 887, 49], [993, 53, 887, 53], [994, 14, 888, 12], [994, 15, 888, 13], [995, 12, 888, 15], [996, 14, 888, 15, "fileName"], [996, 22, 888, 15], [996, 24, 888, 15, "_jsxFileName"], [996, 36, 888, 15], [997, 14, 888, 15, "lineNumber"], [997, 24, 888, 15], [998, 14, 888, 15, "columnNumber"], [998, 26, 888, 15], [999, 12, 888, 15], [999, 19, 888, 17], [999, 20, 888, 18], [999, 35, 889, 12], [999, 39, 889, 12, "_jsxDevRuntime"], [999, 53, 889, 12], [999, 54, 889, 12, "jsxDEV"], [999, 60, 889, 12], [999, 62, 889, 13, "_expoBlur"], [999, 71, 889, 13], [999, 72, 889, 13, "BlurView"], [999, 80, 889, 21], [1000, 14, 889, 22, "intensity"], [1000, 23, 889, 31], [1000, 25, 889, 33], [1000, 27, 889, 36], [1001, 14, 889, 37, "tint"], [1001, 18, 889, 41], [1001, 20, 889, 42], [1001, 26, 889, 48], [1002, 14, 889, 49, "style"], [1002, 19, 889, 54], [1002, 21, 889, 56], [1002, 22, 889, 57, "styles"], [1002, 28, 889, 63], [1002, 29, 889, 64, "blurZone"], [1002, 37, 889, 72], [1002, 39, 889, 74], [1003, 16, 890, 14, "left"], [1003, 20, 890, 18], [1003, 22, 890, 20, "viewSize"], [1003, 30, 890, 28], [1003, 31, 890, 29, "width"], [1003, 36, 890, 34], [1003, 39, 890, 37], [1003, 42, 890, 40], [1003, 45, 890, 44, "viewSize"], [1003, 53, 890, 52], [1003, 54, 890, 53, "width"], [1003, 59, 890, 58], [1003, 62, 890, 61], [1003, 66, 890, 66], [1004, 16, 891, 14, "top"], [1004, 19, 891, 17], [1004, 21, 891, 19, "viewSize"], [1004, 29, 891, 27], [1004, 30, 891, 28, "height"], [1004, 36, 891, 34], [1004, 39, 891, 37], [1004, 42, 891, 40], [1004, 45, 891, 44, "viewSize"], [1004, 53, 891, 52], [1004, 54, 891, 53, "width"], [1004, 59, 891, 58], [1004, 62, 891, 61], [1004, 66, 891, 66], [1005, 16, 892, 14, "width"], [1005, 21, 892, 19], [1005, 23, 892, 21, "viewSize"], [1005, 31, 892, 29], [1005, 32, 892, 30, "width"], [1005, 37, 892, 35], [1005, 40, 892, 38], [1005, 43, 892, 41], [1006, 16, 893, 14, "height"], [1006, 22, 893, 20], [1006, 24, 893, 22, "viewSize"], [1006, 32, 893, 30], [1006, 33, 893, 31, "width"], [1006, 38, 893, 36], [1006, 41, 893, 39], [1006, 44, 893, 42], [1007, 16, 894, 14, "borderRadius"], [1007, 28, 894, 26], [1007, 30, 894, 29, "viewSize"], [1007, 38, 894, 37], [1007, 39, 894, 38, "width"], [1007, 44, 894, 43], [1007, 47, 894, 46], [1007, 50, 894, 49], [1007, 53, 894, 53], [1008, 14, 895, 12], [1008, 15, 895, 13], [1009, 12, 895, 15], [1010, 14, 895, 15, "fileName"], [1010, 22, 895, 15], [1010, 24, 895, 15, "_jsxFileName"], [1010, 36, 895, 15], [1011, 14, 895, 15, "lineNumber"], [1011, 24, 895, 15], [1012, 14, 895, 15, "columnNumber"], [1012, 26, 895, 15], [1013, 12, 895, 15], [1013, 19, 895, 17], [1013, 20, 895, 18], [1013, 35, 896, 12], [1013, 39, 896, 12, "_jsxDevRuntime"], [1013, 53, 896, 12], [1013, 54, 896, 12, "jsxDEV"], [1013, 60, 896, 12], [1013, 62, 896, 13, "_expoBlur"], [1013, 71, 896, 13], [1013, 72, 896, 13, "BlurView"], [1013, 80, 896, 21], [1014, 14, 896, 22, "intensity"], [1014, 23, 896, 31], [1014, 25, 896, 33], [1014, 27, 896, 36], [1015, 14, 896, 37, "tint"], [1015, 18, 896, 41], [1015, 20, 896, 42], [1015, 26, 896, 48], [1016, 14, 896, 49, "style"], [1016, 19, 896, 54], [1016, 21, 896, 56], [1016, 22, 896, 57, "styles"], [1016, 28, 896, 63], [1016, 29, 896, 64, "blurZone"], [1016, 37, 896, 72], [1016, 39, 896, 74], [1017, 16, 897, 14, "left"], [1017, 20, 897, 18], [1017, 22, 897, 20, "viewSize"], [1017, 30, 897, 28], [1017, 31, 897, 29, "width"], [1017, 36, 897, 34], [1017, 39, 897, 37], [1017, 42, 897, 40], [1017, 45, 897, 44, "viewSize"], [1017, 53, 897, 52], [1017, 54, 897, 53, "width"], [1017, 59, 897, 58], [1017, 62, 897, 61], [1017, 66, 897, 66], [1018, 16, 898, 14, "top"], [1018, 19, 898, 17], [1018, 21, 898, 19, "viewSize"], [1018, 29, 898, 27], [1018, 30, 898, 28, "height"], [1018, 36, 898, 34], [1018, 39, 898, 37], [1018, 42, 898, 40], [1018, 45, 898, 44, "viewSize"], [1018, 53, 898, 52], [1018, 54, 898, 53, "width"], [1018, 59, 898, 58], [1018, 62, 898, 61], [1018, 66, 898, 66], [1019, 16, 899, 14, "width"], [1019, 21, 899, 19], [1019, 23, 899, 21, "viewSize"], [1019, 31, 899, 29], [1019, 32, 899, 30, "width"], [1019, 37, 899, 35], [1019, 40, 899, 38], [1019, 43, 899, 41], [1020, 16, 900, 14, "height"], [1020, 22, 900, 20], [1020, 24, 900, 22, "viewSize"], [1020, 32, 900, 30], [1020, 33, 900, 31, "width"], [1020, 38, 900, 36], [1020, 41, 900, 39], [1020, 44, 900, 42], [1021, 16, 901, 14, "borderRadius"], [1021, 28, 901, 26], [1021, 30, 901, 29, "viewSize"], [1021, 38, 901, 37], [1021, 39, 901, 38, "width"], [1021, 44, 901, 43], [1021, 47, 901, 46], [1021, 50, 901, 49], [1021, 53, 901, 53], [1022, 14, 902, 12], [1022, 15, 902, 13], [1023, 12, 902, 15], [1024, 14, 902, 15, "fileName"], [1024, 22, 902, 15], [1024, 24, 902, 15, "_jsxFileName"], [1024, 36, 902, 15], [1025, 14, 902, 15, "lineNumber"], [1025, 24, 902, 15], [1026, 14, 902, 15, "columnNumber"], [1026, 26, 902, 15], [1027, 12, 902, 15], [1027, 19, 902, 17], [1027, 20, 902, 18], [1027, 22, 904, 13, "__DEV__"], [1027, 29, 904, 20], [1027, 46, 905, 14], [1027, 50, 905, 14, "_jsxDevRuntime"], [1027, 64, 905, 14], [1027, 65, 905, 14, "jsxDEV"], [1027, 71, 905, 14], [1027, 73, 905, 15, "_View"], [1027, 78, 905, 15], [1027, 79, 905, 15, "default"], [1027, 86, 905, 19], [1028, 14, 905, 20, "style"], [1028, 19, 905, 25], [1028, 21, 905, 27, "styles"], [1028, 27, 905, 33], [1028, 28, 905, 34, "previewChip"], [1028, 39, 905, 46], [1029, 14, 905, 46, "children"], [1029, 22, 905, 46], [1029, 37, 906, 16], [1029, 41, 906, 16, "_jsxDevRuntime"], [1029, 55, 906, 16], [1029, 56, 906, 16, "jsxDEV"], [1029, 62, 906, 16], [1029, 64, 906, 17, "_Text"], [1029, 69, 906, 17], [1029, 70, 906, 17, "default"], [1029, 77, 906, 21], [1030, 16, 906, 22, "style"], [1030, 21, 906, 27], [1030, 23, 906, 29, "styles"], [1030, 29, 906, 35], [1030, 30, 906, 36, "previewChipText"], [1030, 45, 906, 52], [1031, 16, 906, 52, "children"], [1031, 24, 906, 52], [1031, 26, 906, 53], [1032, 14, 906, 73], [1033, 16, 906, 73, "fileName"], [1033, 24, 906, 73], [1033, 26, 906, 73, "_jsxFileName"], [1033, 38, 906, 73], [1034, 16, 906, 73, "lineNumber"], [1034, 26, 906, 73], [1035, 16, 906, 73, "columnNumber"], [1035, 28, 906, 73], [1036, 14, 906, 73], [1036, 21, 906, 79], [1037, 12, 906, 80], [1038, 14, 906, 80, "fileName"], [1038, 22, 906, 80], [1038, 24, 906, 80, "_jsxFileName"], [1038, 36, 906, 80], [1039, 14, 906, 80, "lineNumber"], [1039, 24, 906, 80], [1040, 14, 906, 80, "columnNumber"], [1040, 26, 906, 80], [1041, 12, 906, 80], [1041, 19, 907, 20], [1041, 20, 908, 13], [1042, 10, 908, 13], [1043, 12, 908, 13, "fileName"], [1043, 20, 908, 13], [1043, 22, 908, 13, "_jsxFileName"], [1043, 34, 908, 13], [1044, 12, 908, 13, "lineNumber"], [1044, 22, 908, 13], [1045, 12, 908, 13, "columnNumber"], [1045, 24, 908, 13], [1046, 10, 908, 13], [1046, 17, 909, 18], [1046, 18, 909, 19], [1047, 8, 909, 19], [1047, 23, 910, 12], [1047, 24, 911, 9], [1047, 26, 913, 9, "isCameraReady"], [1047, 39, 913, 22], [1047, 56, 914, 10], [1047, 60, 914, 10, "_jsxDevRuntime"], [1047, 74, 914, 10], [1047, 75, 914, 10, "jsxDEV"], [1047, 81, 914, 10], [1047, 83, 914, 10, "_jsxDevRuntime"], [1047, 97, 914, 10], [1047, 98, 914, 10, "Fragment"], [1047, 106, 914, 10], [1048, 10, 914, 10, "children"], [1048, 18, 914, 10], [1048, 34, 916, 12], [1048, 38, 916, 12, "_jsxDevRuntime"], [1048, 52, 916, 12], [1048, 53, 916, 12, "jsxDEV"], [1048, 59, 916, 12], [1048, 61, 916, 13, "_View"], [1048, 66, 916, 13], [1048, 67, 916, 13, "default"], [1048, 74, 916, 17], [1049, 12, 916, 18, "style"], [1049, 17, 916, 23], [1049, 19, 916, 25, "styles"], [1049, 25, 916, 31], [1049, 26, 916, 32, "headerOverlay"], [1049, 39, 916, 46], [1050, 12, 916, 46, "children"], [1050, 20, 916, 46], [1050, 35, 917, 14], [1050, 39, 917, 14, "_jsxDevRuntime"], [1050, 53, 917, 14], [1050, 54, 917, 14, "jsxDEV"], [1050, 60, 917, 14], [1050, 62, 917, 15, "_View"], [1050, 67, 917, 15], [1050, 68, 917, 15, "default"], [1050, 75, 917, 19], [1051, 14, 917, 20, "style"], [1051, 19, 917, 25], [1051, 21, 917, 27, "styles"], [1051, 27, 917, 33], [1051, 28, 917, 34, "headerContent"], [1051, 41, 917, 48], [1052, 14, 917, 48, "children"], [1052, 22, 917, 48], [1052, 38, 918, 16], [1052, 42, 918, 16, "_jsxDevRuntime"], [1052, 56, 918, 16], [1052, 57, 918, 16, "jsxDEV"], [1052, 63, 918, 16], [1052, 65, 918, 17, "_View"], [1052, 70, 918, 17], [1052, 71, 918, 17, "default"], [1052, 78, 918, 21], [1053, 16, 918, 22, "style"], [1053, 21, 918, 27], [1053, 23, 918, 29, "styles"], [1053, 29, 918, 35], [1053, 30, 918, 36, "headerLeft"], [1053, 40, 918, 47], [1054, 16, 918, 47, "children"], [1054, 24, 918, 47], [1054, 40, 919, 18], [1054, 44, 919, 18, "_jsxDevRuntime"], [1054, 58, 919, 18], [1054, 59, 919, 18, "jsxDEV"], [1054, 65, 919, 18], [1054, 67, 919, 19, "_Text"], [1054, 72, 919, 19], [1054, 73, 919, 19, "default"], [1054, 80, 919, 23], [1055, 18, 919, 24, "style"], [1055, 23, 919, 29], [1055, 25, 919, 31, "styles"], [1055, 31, 919, 37], [1055, 32, 919, 38, "headerTitle"], [1055, 43, 919, 50], [1056, 18, 919, 50, "children"], [1056, 26, 919, 50], [1056, 28, 919, 51], [1057, 16, 919, 62], [1058, 18, 919, 62, "fileName"], [1058, 26, 919, 62], [1058, 28, 919, 62, "_jsxFileName"], [1058, 40, 919, 62], [1059, 18, 919, 62, "lineNumber"], [1059, 28, 919, 62], [1060, 18, 919, 62, "columnNumber"], [1060, 30, 919, 62], [1061, 16, 919, 62], [1061, 23, 919, 68], [1061, 24, 919, 69], [1061, 39, 920, 18], [1061, 43, 920, 18, "_jsxDevRuntime"], [1061, 57, 920, 18], [1061, 58, 920, 18, "jsxDEV"], [1061, 64, 920, 18], [1061, 66, 920, 19, "_View"], [1061, 71, 920, 19], [1061, 72, 920, 19, "default"], [1061, 79, 920, 23], [1062, 18, 920, 24, "style"], [1062, 23, 920, 29], [1062, 25, 920, 31, "styles"], [1062, 31, 920, 37], [1062, 32, 920, 38, "subtitleRow"], [1062, 43, 920, 50], [1063, 18, 920, 50, "children"], [1063, 26, 920, 50], [1063, 42, 921, 20], [1063, 46, 921, 20, "_jsxDevRuntime"], [1063, 60, 921, 20], [1063, 61, 921, 20, "jsxDEV"], [1063, 67, 921, 20], [1063, 69, 921, 21, "_Text"], [1063, 74, 921, 21], [1063, 75, 921, 21, "default"], [1063, 82, 921, 25], [1064, 20, 921, 26, "style"], [1064, 25, 921, 31], [1064, 27, 921, 33, "styles"], [1064, 33, 921, 39], [1064, 34, 921, 40, "webIcon"], [1064, 41, 921, 48], [1065, 20, 921, 48, "children"], [1065, 28, 921, 48], [1065, 30, 921, 49], [1066, 18, 921, 51], [1067, 20, 921, 51, "fileName"], [1067, 28, 921, 51], [1067, 30, 921, 51, "_jsxFileName"], [1067, 42, 921, 51], [1068, 20, 921, 51, "lineNumber"], [1068, 30, 921, 51], [1069, 20, 921, 51, "columnNumber"], [1069, 32, 921, 51], [1070, 18, 921, 51], [1070, 25, 921, 57], [1070, 26, 921, 58], [1070, 41, 922, 20], [1070, 45, 922, 20, "_jsxDevRuntime"], [1070, 59, 922, 20], [1070, 60, 922, 20, "jsxDEV"], [1070, 66, 922, 20], [1070, 68, 922, 21, "_Text"], [1070, 73, 922, 21], [1070, 74, 922, 21, "default"], [1070, 81, 922, 25], [1071, 20, 922, 26, "style"], [1071, 25, 922, 31], [1071, 27, 922, 33, "styles"], [1071, 33, 922, 39], [1071, 34, 922, 40, "headerSubtitle"], [1071, 48, 922, 55], [1072, 20, 922, 55, "children"], [1072, 28, 922, 55], [1072, 30, 922, 56], [1073, 18, 922, 71], [1074, 20, 922, 71, "fileName"], [1074, 28, 922, 71], [1074, 30, 922, 71, "_jsxFileName"], [1074, 42, 922, 71], [1075, 20, 922, 71, "lineNumber"], [1075, 30, 922, 71], [1076, 20, 922, 71, "columnNumber"], [1076, 32, 922, 71], [1077, 18, 922, 71], [1077, 25, 922, 77], [1077, 26, 922, 78], [1078, 16, 922, 78], [1079, 18, 922, 78, "fileName"], [1079, 26, 922, 78], [1079, 28, 922, 78, "_jsxFileName"], [1079, 40, 922, 78], [1080, 18, 922, 78, "lineNumber"], [1080, 28, 922, 78], [1081, 18, 922, 78, "columnNumber"], [1081, 30, 922, 78], [1082, 16, 922, 78], [1082, 23, 923, 24], [1082, 24, 923, 25], [1082, 26, 924, 19, "challengeCode"], [1082, 39, 924, 32], [1082, 56, 925, 20], [1082, 60, 925, 20, "_jsxDevRuntime"], [1082, 74, 925, 20], [1082, 75, 925, 20, "jsxDEV"], [1082, 81, 925, 20], [1082, 83, 925, 21, "_View"], [1082, 88, 925, 21], [1082, 89, 925, 21, "default"], [1082, 96, 925, 25], [1083, 18, 925, 26, "style"], [1083, 23, 925, 31], [1083, 25, 925, 33, "styles"], [1083, 31, 925, 39], [1083, 32, 925, 40, "challengeRow"], [1083, 44, 925, 53], [1084, 18, 925, 53, "children"], [1084, 26, 925, 53], [1084, 42, 926, 22], [1084, 46, 926, 22, "_jsxDevRuntime"], [1084, 60, 926, 22], [1084, 61, 926, 22, "jsxDEV"], [1084, 67, 926, 22], [1084, 69, 926, 23, "_lucideReactNative"], [1084, 87, 926, 23], [1084, 88, 926, 23, "Shield"], [1084, 94, 926, 29], [1085, 20, 926, 30, "size"], [1085, 24, 926, 34], [1085, 26, 926, 36], [1085, 28, 926, 39], [1086, 20, 926, 40, "color"], [1086, 25, 926, 45], [1086, 27, 926, 46], [1087, 18, 926, 52], [1088, 20, 926, 52, "fileName"], [1088, 28, 926, 52], [1088, 30, 926, 52, "_jsxFileName"], [1088, 42, 926, 52], [1089, 20, 926, 52, "lineNumber"], [1089, 30, 926, 52], [1090, 20, 926, 52, "columnNumber"], [1090, 32, 926, 52], [1091, 18, 926, 52], [1091, 25, 926, 54], [1091, 26, 926, 55], [1091, 41, 927, 22], [1091, 45, 927, 22, "_jsxDevRuntime"], [1091, 59, 927, 22], [1091, 60, 927, 22, "jsxDEV"], [1091, 66, 927, 22], [1091, 68, 927, 23, "_Text"], [1091, 73, 927, 23], [1091, 74, 927, 23, "default"], [1091, 81, 927, 27], [1092, 20, 927, 28, "style"], [1092, 25, 927, 33], [1092, 27, 927, 35, "styles"], [1092, 33, 927, 41], [1092, 34, 927, 42, "challengeCode"], [1092, 47, 927, 56], [1093, 20, 927, 56, "children"], [1093, 28, 927, 56], [1093, 30, 927, 58, "challengeCode"], [1094, 18, 927, 71], [1095, 20, 927, 71, "fileName"], [1095, 28, 927, 71], [1095, 30, 927, 71, "_jsxFileName"], [1095, 42, 927, 71], [1096, 20, 927, 71, "lineNumber"], [1096, 30, 927, 71], [1097, 20, 927, 71, "columnNumber"], [1097, 32, 927, 71], [1098, 18, 927, 71], [1098, 25, 927, 78], [1098, 26, 927, 79], [1099, 16, 927, 79], [1100, 18, 927, 79, "fileName"], [1100, 26, 927, 79], [1100, 28, 927, 79, "_jsxFileName"], [1100, 40, 927, 79], [1101, 18, 927, 79, "lineNumber"], [1101, 28, 927, 79], [1102, 18, 927, 79, "columnNumber"], [1102, 30, 927, 79], [1103, 16, 927, 79], [1103, 23, 928, 26], [1103, 24, 929, 19], [1104, 14, 929, 19], [1105, 16, 929, 19, "fileName"], [1105, 24, 929, 19], [1105, 26, 929, 19, "_jsxFileName"], [1105, 38, 929, 19], [1106, 16, 929, 19, "lineNumber"], [1106, 26, 929, 19], [1107, 16, 929, 19, "columnNumber"], [1107, 28, 929, 19], [1108, 14, 929, 19], [1108, 21, 930, 22], [1108, 22, 930, 23], [1108, 37, 931, 16], [1108, 41, 931, 16, "_jsxDevRuntime"], [1108, 55, 931, 16], [1108, 56, 931, 16, "jsxDEV"], [1108, 62, 931, 16], [1108, 64, 931, 17, "_TouchableOpacity"], [1108, 81, 931, 17], [1108, 82, 931, 17, "default"], [1108, 89, 931, 33], [1109, 16, 931, 34, "onPress"], [1109, 23, 931, 41], [1109, 25, 931, 43, "onCancel"], [1109, 33, 931, 52], [1110, 16, 931, 53, "style"], [1110, 21, 931, 58], [1110, 23, 931, 60, "styles"], [1110, 29, 931, 66], [1110, 30, 931, 67, "closeButton"], [1110, 41, 931, 79], [1111, 16, 931, 79, "children"], [1111, 24, 931, 79], [1111, 39, 932, 18], [1111, 43, 932, 18, "_jsxDevRuntime"], [1111, 57, 932, 18], [1111, 58, 932, 18, "jsxDEV"], [1111, 64, 932, 18], [1111, 66, 932, 19, "_lucideReactNative"], [1111, 84, 932, 19], [1111, 85, 932, 19, "X"], [1111, 86, 932, 20], [1112, 18, 932, 21, "size"], [1112, 22, 932, 25], [1112, 24, 932, 27], [1112, 26, 932, 30], [1113, 18, 932, 31, "color"], [1113, 23, 932, 36], [1113, 25, 932, 37], [1114, 16, 932, 43], [1115, 18, 932, 43, "fileName"], [1115, 26, 932, 43], [1115, 28, 932, 43, "_jsxFileName"], [1115, 40, 932, 43], [1116, 18, 932, 43, "lineNumber"], [1116, 28, 932, 43], [1117, 18, 932, 43, "columnNumber"], [1117, 30, 932, 43], [1118, 16, 932, 43], [1118, 23, 932, 45], [1119, 14, 932, 46], [1120, 16, 932, 46, "fileName"], [1120, 24, 932, 46], [1120, 26, 932, 46, "_jsxFileName"], [1120, 38, 932, 46], [1121, 16, 932, 46, "lineNumber"], [1121, 26, 932, 46], [1122, 16, 932, 46, "columnNumber"], [1122, 28, 932, 46], [1123, 14, 932, 46], [1123, 21, 933, 34], [1123, 22, 933, 35], [1124, 12, 933, 35], [1125, 14, 933, 35, "fileName"], [1125, 22, 933, 35], [1125, 24, 933, 35, "_jsxFileName"], [1125, 36, 933, 35], [1126, 14, 933, 35, "lineNumber"], [1126, 24, 933, 35], [1127, 14, 933, 35, "columnNumber"], [1127, 26, 933, 35], [1128, 12, 933, 35], [1128, 19, 934, 20], [1129, 10, 934, 21], [1130, 12, 934, 21, "fileName"], [1130, 20, 934, 21], [1130, 22, 934, 21, "_jsxFileName"], [1130, 34, 934, 21], [1131, 12, 934, 21, "lineNumber"], [1131, 22, 934, 21], [1132, 12, 934, 21, "columnNumber"], [1132, 24, 934, 21], [1133, 10, 934, 21], [1133, 17, 935, 18], [1133, 18, 935, 19], [1133, 33, 937, 12], [1133, 37, 937, 12, "_jsxDevRuntime"], [1133, 51, 937, 12], [1133, 52, 937, 12, "jsxDEV"], [1133, 58, 937, 12], [1133, 60, 937, 13, "_View"], [1133, 65, 937, 13], [1133, 66, 937, 13, "default"], [1133, 73, 937, 17], [1134, 12, 937, 18, "style"], [1134, 17, 937, 23], [1134, 19, 937, 25, "styles"], [1134, 25, 937, 31], [1134, 26, 937, 32, "privacyNotice"], [1134, 39, 937, 46], [1135, 12, 937, 46, "children"], [1135, 20, 937, 46], [1135, 36, 938, 14], [1135, 40, 938, 14, "_jsxDevRuntime"], [1135, 54, 938, 14], [1135, 55, 938, 14, "jsxDEV"], [1135, 61, 938, 14], [1135, 63, 938, 15, "_lucideReactNative"], [1135, 81, 938, 15], [1135, 82, 938, 15, "Shield"], [1135, 88, 938, 21], [1136, 14, 938, 22, "size"], [1136, 18, 938, 26], [1136, 20, 938, 28], [1136, 22, 938, 31], [1137, 14, 938, 32, "color"], [1137, 19, 938, 37], [1137, 21, 938, 38], [1138, 12, 938, 47], [1139, 14, 938, 47, "fileName"], [1139, 22, 938, 47], [1139, 24, 938, 47, "_jsxFileName"], [1139, 36, 938, 47], [1140, 14, 938, 47, "lineNumber"], [1140, 24, 938, 47], [1141, 14, 938, 47, "columnNumber"], [1141, 26, 938, 47], [1142, 12, 938, 47], [1142, 19, 938, 49], [1142, 20, 938, 50], [1142, 35, 939, 14], [1142, 39, 939, 14, "_jsxDevRuntime"], [1142, 53, 939, 14], [1142, 54, 939, 14, "jsxDEV"], [1142, 60, 939, 14], [1142, 62, 939, 15, "_Text"], [1142, 67, 939, 15], [1142, 68, 939, 15, "default"], [1142, 75, 939, 19], [1143, 14, 939, 20, "style"], [1143, 19, 939, 25], [1143, 21, 939, 27, "styles"], [1143, 27, 939, 33], [1143, 28, 939, 34, "privacyText"], [1143, 39, 939, 46], [1144, 14, 939, 46, "children"], [1144, 22, 939, 46], [1144, 24, 939, 47], [1145, 12, 941, 14], [1146, 14, 941, 14, "fileName"], [1146, 22, 941, 14], [1146, 24, 941, 14, "_jsxFileName"], [1146, 36, 941, 14], [1147, 14, 941, 14, "lineNumber"], [1147, 24, 941, 14], [1148, 14, 941, 14, "columnNumber"], [1148, 26, 941, 14], [1149, 12, 941, 14], [1149, 19, 941, 20], [1149, 20, 941, 21], [1150, 10, 941, 21], [1151, 12, 941, 21, "fileName"], [1151, 20, 941, 21], [1151, 22, 941, 21, "_jsxFileName"], [1151, 34, 941, 21], [1152, 12, 941, 21, "lineNumber"], [1152, 22, 941, 21], [1153, 12, 941, 21, "columnNumber"], [1153, 24, 941, 21], [1154, 10, 941, 21], [1154, 17, 942, 18], [1154, 18, 942, 19], [1154, 33, 944, 12], [1154, 37, 944, 12, "_jsxDevRuntime"], [1154, 51, 944, 12], [1154, 52, 944, 12, "jsxDEV"], [1154, 58, 944, 12], [1154, 60, 944, 13, "_View"], [1154, 65, 944, 13], [1154, 66, 944, 13, "default"], [1154, 73, 944, 17], [1155, 12, 944, 18, "style"], [1155, 17, 944, 23], [1155, 19, 944, 25, "styles"], [1155, 25, 944, 31], [1155, 26, 944, 32, "footer<PERSON><PERSON><PERSON>"], [1155, 39, 944, 46], [1156, 12, 944, 46, "children"], [1156, 20, 944, 46], [1156, 36, 945, 14], [1156, 40, 945, 14, "_jsxDevRuntime"], [1156, 54, 945, 14], [1156, 55, 945, 14, "jsxDEV"], [1156, 61, 945, 14], [1156, 63, 945, 15, "_Text"], [1156, 68, 945, 15], [1156, 69, 945, 15, "default"], [1156, 76, 945, 19], [1157, 14, 945, 20, "style"], [1157, 19, 945, 25], [1157, 21, 945, 27, "styles"], [1157, 27, 945, 33], [1157, 28, 945, 34, "instruction"], [1157, 39, 945, 46], [1158, 14, 945, 46, "children"], [1158, 22, 945, 46], [1158, 24, 945, 47], [1159, 12, 947, 14], [1160, 14, 947, 14, "fileName"], [1160, 22, 947, 14], [1160, 24, 947, 14, "_jsxFileName"], [1160, 36, 947, 14], [1161, 14, 947, 14, "lineNumber"], [1161, 24, 947, 14], [1162, 14, 947, 14, "columnNumber"], [1162, 26, 947, 14], [1163, 12, 947, 14], [1163, 19, 947, 20], [1163, 20, 947, 21], [1163, 35, 949, 14], [1163, 39, 949, 14, "_jsxDevRuntime"], [1163, 53, 949, 14], [1163, 54, 949, 14, "jsxDEV"], [1163, 60, 949, 14], [1163, 62, 949, 15, "_TouchableOpacity"], [1163, 79, 949, 15], [1163, 80, 949, 15, "default"], [1163, 87, 949, 31], [1164, 14, 950, 16, "onPress"], [1164, 21, 950, 23], [1164, 23, 950, 25, "capturePhoto"], [1164, 35, 950, 38], [1165, 14, 951, 16, "disabled"], [1165, 22, 951, 24], [1165, 24, 951, 26, "processingState"], [1165, 39, 951, 41], [1165, 44, 951, 46], [1165, 50, 951, 52], [1165, 54, 951, 56], [1165, 55, 951, 57, "isCameraReady"], [1165, 68, 951, 71], [1166, 14, 952, 16, "style"], [1166, 19, 952, 21], [1166, 21, 952, 23], [1166, 22, 953, 18, "styles"], [1166, 28, 953, 24], [1166, 29, 953, 25, "shutterButton"], [1166, 42, 953, 38], [1166, 44, 954, 18, "processingState"], [1166, 59, 954, 33], [1166, 64, 954, 38], [1166, 70, 954, 44], [1166, 74, 954, 48, "styles"], [1166, 80, 954, 54], [1166, 81, 954, 55, "shutterButtonDisabled"], [1166, 102, 954, 76], [1166, 103, 955, 18], [1167, 14, 955, 18, "children"], [1167, 22, 955, 18], [1167, 24, 957, 17, "processingState"], [1167, 39, 957, 32], [1167, 44, 957, 37], [1167, 50, 957, 43], [1167, 66, 958, 18], [1167, 70, 958, 18, "_jsxDevRuntime"], [1167, 84, 958, 18], [1167, 85, 958, 18, "jsxDEV"], [1167, 91, 958, 18], [1167, 93, 958, 19, "_View"], [1167, 98, 958, 19], [1167, 99, 958, 19, "default"], [1167, 106, 958, 23], [1168, 16, 958, 24, "style"], [1168, 21, 958, 29], [1168, 23, 958, 31, "styles"], [1168, 29, 958, 37], [1168, 30, 958, 38, "shutterInner"], [1169, 14, 958, 51], [1170, 16, 958, 51, "fileName"], [1170, 24, 958, 51], [1170, 26, 958, 51, "_jsxFileName"], [1170, 38, 958, 51], [1171, 16, 958, 51, "lineNumber"], [1171, 26, 958, 51], [1172, 16, 958, 51, "columnNumber"], [1172, 28, 958, 51], [1173, 14, 958, 51], [1173, 21, 958, 53], [1173, 22, 958, 54], [1173, 38, 960, 18], [1173, 42, 960, 18, "_jsxDevRuntime"], [1173, 56, 960, 18], [1173, 57, 960, 18, "jsxDEV"], [1173, 63, 960, 18], [1173, 65, 960, 19, "_ActivityIndicator"], [1173, 83, 960, 19], [1173, 84, 960, 19, "default"], [1173, 91, 960, 36], [1174, 16, 960, 37, "size"], [1174, 20, 960, 41], [1174, 22, 960, 42], [1174, 29, 960, 49], [1175, 16, 960, 50, "color"], [1175, 21, 960, 55], [1175, 23, 960, 56], [1176, 14, 960, 65], [1177, 16, 960, 65, "fileName"], [1177, 24, 960, 65], [1177, 26, 960, 65, "_jsxFileName"], [1177, 38, 960, 65], [1178, 16, 960, 65, "lineNumber"], [1178, 26, 960, 65], [1179, 16, 960, 65, "columnNumber"], [1179, 28, 960, 65], [1180, 14, 960, 65], [1180, 21, 960, 67], [1181, 12, 961, 17], [1182, 14, 961, 17, "fileName"], [1182, 22, 961, 17], [1182, 24, 961, 17, "_jsxFileName"], [1182, 36, 961, 17], [1183, 14, 961, 17, "lineNumber"], [1183, 24, 961, 17], [1184, 14, 961, 17, "columnNumber"], [1184, 26, 961, 17], [1185, 12, 961, 17], [1185, 19, 962, 32], [1185, 20, 962, 33], [1185, 35, 963, 14], [1185, 39, 963, 14, "_jsxDevRuntime"], [1185, 53, 963, 14], [1185, 54, 963, 14, "jsxDEV"], [1185, 60, 963, 14], [1185, 62, 963, 15, "_Text"], [1185, 67, 963, 15], [1185, 68, 963, 15, "default"], [1185, 75, 963, 19], [1186, 14, 963, 20, "style"], [1186, 19, 963, 25], [1186, 21, 963, 27, "styles"], [1186, 27, 963, 33], [1186, 28, 963, 34, "privacyNote"], [1186, 39, 963, 46], [1187, 14, 963, 46, "children"], [1187, 22, 963, 46], [1187, 24, 963, 47], [1188, 12, 965, 14], [1189, 14, 965, 14, "fileName"], [1189, 22, 965, 14], [1189, 24, 965, 14, "_jsxFileName"], [1189, 36, 965, 14], [1190, 14, 965, 14, "lineNumber"], [1190, 24, 965, 14], [1191, 14, 965, 14, "columnNumber"], [1191, 26, 965, 14], [1192, 12, 965, 14], [1192, 19, 965, 20], [1192, 20, 965, 21], [1193, 10, 965, 21], [1194, 12, 965, 21, "fileName"], [1194, 20, 965, 21], [1194, 22, 965, 21, "_jsxFileName"], [1194, 34, 965, 21], [1195, 12, 965, 21, "lineNumber"], [1195, 22, 965, 21], [1196, 12, 965, 21, "columnNumber"], [1196, 24, 965, 21], [1197, 10, 965, 21], [1197, 17, 966, 18], [1197, 18, 966, 19], [1198, 8, 966, 19], [1198, 23, 967, 12], [1198, 24, 968, 9], [1199, 6, 968, 9], [1200, 8, 968, 9, "fileName"], [1200, 16, 968, 9], [1200, 18, 968, 9, "_jsxFileName"], [1200, 30, 968, 9], [1201, 8, 968, 9, "lineNumber"], [1201, 18, 968, 9], [1202, 8, 968, 9, "columnNumber"], [1202, 20, 968, 9], [1203, 6, 968, 9], [1203, 13, 969, 12], [1203, 14, 969, 13], [1203, 29, 971, 6], [1203, 33, 971, 6, "_jsxDevRuntime"], [1203, 47, 971, 6], [1203, 48, 971, 6, "jsxDEV"], [1203, 54, 971, 6], [1203, 56, 971, 7, "_Modal"], [1203, 62, 971, 7], [1203, 63, 971, 7, "default"], [1203, 70, 971, 12], [1204, 8, 972, 8, "visible"], [1204, 15, 972, 15], [1204, 17, 972, 17, "processingState"], [1204, 32, 972, 32], [1204, 37, 972, 37], [1204, 43, 972, 43], [1204, 47, 972, 47, "processingState"], [1204, 62, 972, 62], [1204, 67, 972, 67], [1204, 74, 972, 75], [1205, 8, 973, 8, "transparent"], [1205, 19, 973, 19], [1206, 8, 974, 8, "animationType"], [1206, 21, 974, 21], [1206, 23, 974, 22], [1206, 29, 974, 28], [1207, 8, 974, 28, "children"], [1207, 16, 974, 28], [1207, 31, 976, 8], [1207, 35, 976, 8, "_jsxDevRuntime"], [1207, 49, 976, 8], [1207, 50, 976, 8, "jsxDEV"], [1207, 56, 976, 8], [1207, 58, 976, 9, "_View"], [1207, 63, 976, 9], [1207, 64, 976, 9, "default"], [1207, 71, 976, 13], [1208, 10, 976, 14, "style"], [1208, 15, 976, 19], [1208, 17, 976, 21, "styles"], [1208, 23, 976, 27], [1208, 24, 976, 28, "processingModal"], [1208, 39, 976, 44], [1209, 10, 976, 44, "children"], [1209, 18, 976, 44], [1209, 33, 977, 10], [1209, 37, 977, 10, "_jsxDevRuntime"], [1209, 51, 977, 10], [1209, 52, 977, 10, "jsxDEV"], [1209, 58, 977, 10], [1209, 60, 977, 11, "_View"], [1209, 65, 977, 11], [1209, 66, 977, 11, "default"], [1209, 73, 977, 15], [1210, 12, 977, 16, "style"], [1210, 17, 977, 21], [1210, 19, 977, 23, "styles"], [1210, 25, 977, 29], [1210, 26, 977, 30, "processingContent"], [1210, 43, 977, 48], [1211, 12, 977, 48, "children"], [1211, 20, 977, 48], [1211, 36, 978, 12], [1211, 40, 978, 12, "_jsxDevRuntime"], [1211, 54, 978, 12], [1211, 55, 978, 12, "jsxDEV"], [1211, 61, 978, 12], [1211, 63, 978, 13, "_ActivityIndicator"], [1211, 81, 978, 13], [1211, 82, 978, 13, "default"], [1211, 89, 978, 30], [1212, 14, 978, 31, "size"], [1212, 18, 978, 35], [1212, 20, 978, 36], [1212, 27, 978, 43], [1213, 14, 978, 44, "color"], [1213, 19, 978, 49], [1213, 21, 978, 50], [1214, 12, 978, 59], [1215, 14, 978, 59, "fileName"], [1215, 22, 978, 59], [1215, 24, 978, 59, "_jsxFileName"], [1215, 36, 978, 59], [1216, 14, 978, 59, "lineNumber"], [1216, 24, 978, 59], [1217, 14, 978, 59, "columnNumber"], [1217, 26, 978, 59], [1218, 12, 978, 59], [1218, 19, 978, 61], [1218, 20, 978, 62], [1218, 35, 980, 12], [1218, 39, 980, 12, "_jsxDevRuntime"], [1218, 53, 980, 12], [1218, 54, 980, 12, "jsxDEV"], [1218, 60, 980, 12], [1218, 62, 980, 13, "_Text"], [1218, 67, 980, 13], [1218, 68, 980, 13, "default"], [1218, 75, 980, 17], [1219, 14, 980, 18, "style"], [1219, 19, 980, 23], [1219, 21, 980, 25, "styles"], [1219, 27, 980, 31], [1219, 28, 980, 32, "processingTitle"], [1219, 43, 980, 48], [1220, 14, 980, 48, "children"], [1220, 22, 980, 48], [1220, 25, 981, 15, "processingState"], [1220, 40, 981, 30], [1220, 45, 981, 35], [1220, 56, 981, 46], [1220, 60, 981, 50], [1220, 80, 981, 70], [1220, 82, 982, 15, "processingState"], [1220, 97, 982, 30], [1220, 102, 982, 35], [1220, 113, 982, 46], [1220, 117, 982, 50], [1220, 146, 982, 79], [1220, 148, 983, 15, "processingState"], [1220, 163, 983, 30], [1220, 168, 983, 35], [1220, 180, 983, 47], [1220, 184, 983, 51], [1220, 216, 983, 83], [1220, 218, 984, 15, "processingState"], [1220, 233, 984, 30], [1220, 238, 984, 35], [1220, 249, 984, 46], [1220, 253, 984, 50], [1220, 275, 984, 72], [1221, 12, 984, 72], [1222, 14, 984, 72, "fileName"], [1222, 22, 984, 72], [1222, 24, 984, 72, "_jsxFileName"], [1222, 36, 984, 72], [1223, 14, 984, 72, "lineNumber"], [1223, 24, 984, 72], [1224, 14, 984, 72, "columnNumber"], [1224, 26, 984, 72], [1225, 12, 984, 72], [1225, 19, 985, 18], [1225, 20, 985, 19], [1225, 35, 986, 12], [1225, 39, 986, 12, "_jsxDevRuntime"], [1225, 53, 986, 12], [1225, 54, 986, 12, "jsxDEV"], [1225, 60, 986, 12], [1225, 62, 986, 13, "_View"], [1225, 67, 986, 13], [1225, 68, 986, 13, "default"], [1225, 75, 986, 17], [1226, 14, 986, 18, "style"], [1226, 19, 986, 23], [1226, 21, 986, 25, "styles"], [1226, 27, 986, 31], [1226, 28, 986, 32, "progressBar"], [1226, 39, 986, 44], [1227, 14, 986, 44, "children"], [1227, 22, 986, 44], [1227, 37, 987, 14], [1227, 41, 987, 14, "_jsxDevRuntime"], [1227, 55, 987, 14], [1227, 56, 987, 14, "jsxDEV"], [1227, 62, 987, 14], [1227, 64, 987, 15, "_View"], [1227, 69, 987, 15], [1227, 70, 987, 15, "default"], [1227, 77, 987, 19], [1228, 16, 988, 16, "style"], [1228, 21, 988, 21], [1228, 23, 988, 23], [1228, 24, 989, 18, "styles"], [1228, 30, 989, 24], [1228, 31, 989, 25, "progressFill"], [1228, 43, 989, 37], [1228, 45, 990, 18], [1229, 18, 990, 20, "width"], [1229, 23, 990, 25], [1229, 25, 990, 27], [1229, 28, 990, 30, "processingProgress"], [1229, 46, 990, 48], [1230, 16, 990, 52], [1230, 17, 990, 53], [1231, 14, 991, 18], [1232, 16, 991, 18, "fileName"], [1232, 24, 991, 18], [1232, 26, 991, 18, "_jsxFileName"], [1232, 38, 991, 18], [1233, 16, 991, 18, "lineNumber"], [1233, 26, 991, 18], [1234, 16, 991, 18, "columnNumber"], [1234, 28, 991, 18], [1235, 14, 991, 18], [1235, 21, 992, 15], [1236, 12, 992, 16], [1237, 14, 992, 16, "fileName"], [1237, 22, 992, 16], [1237, 24, 992, 16, "_jsxFileName"], [1237, 36, 992, 16], [1238, 14, 992, 16, "lineNumber"], [1238, 24, 992, 16], [1239, 14, 992, 16, "columnNumber"], [1239, 26, 992, 16], [1240, 12, 992, 16], [1240, 19, 993, 18], [1240, 20, 993, 19], [1240, 35, 994, 12], [1240, 39, 994, 12, "_jsxDevRuntime"], [1240, 53, 994, 12], [1240, 54, 994, 12, "jsxDEV"], [1240, 60, 994, 12], [1240, 62, 994, 13, "_Text"], [1240, 67, 994, 13], [1240, 68, 994, 13, "default"], [1240, 75, 994, 17], [1241, 14, 994, 18, "style"], [1241, 19, 994, 23], [1241, 21, 994, 25, "styles"], [1241, 27, 994, 31], [1241, 28, 994, 32, "processingDescription"], [1241, 49, 994, 54], [1242, 14, 994, 54, "children"], [1242, 22, 994, 54], [1242, 25, 995, 15, "processingState"], [1242, 40, 995, 30], [1242, 45, 995, 35], [1242, 56, 995, 46], [1242, 60, 995, 50], [1242, 89, 995, 79], [1242, 91, 996, 15, "processingState"], [1242, 106, 996, 30], [1242, 111, 996, 35], [1242, 122, 996, 46], [1242, 126, 996, 50], [1242, 164, 996, 88], [1242, 166, 997, 15, "processingState"], [1242, 181, 997, 30], [1242, 186, 997, 35], [1242, 198, 997, 47], [1242, 202, 997, 51], [1242, 247, 997, 96], [1242, 249, 998, 15, "processingState"], [1242, 264, 998, 30], [1242, 269, 998, 35], [1242, 280, 998, 46], [1242, 284, 998, 50], [1242, 325, 998, 91], [1243, 12, 998, 91], [1244, 14, 998, 91, "fileName"], [1244, 22, 998, 91], [1244, 24, 998, 91, "_jsxFileName"], [1244, 36, 998, 91], [1245, 14, 998, 91, "lineNumber"], [1245, 24, 998, 91], [1246, 14, 998, 91, "columnNumber"], [1246, 26, 998, 91], [1247, 12, 998, 91], [1247, 19, 999, 18], [1247, 20, 999, 19], [1247, 22, 1000, 13, "processingState"], [1247, 37, 1000, 28], [1247, 42, 1000, 33], [1247, 53, 1000, 44], [1247, 70, 1001, 14], [1247, 74, 1001, 14, "_jsxDevRuntime"], [1247, 88, 1001, 14], [1247, 89, 1001, 14, "jsxDEV"], [1247, 95, 1001, 14], [1247, 97, 1001, 15, "_lucideReactNative"], [1247, 115, 1001, 15], [1247, 116, 1001, 15, "CheckCircle"], [1247, 127, 1001, 26], [1248, 14, 1001, 27, "size"], [1248, 18, 1001, 31], [1248, 20, 1001, 33], [1248, 22, 1001, 36], [1249, 14, 1001, 37, "color"], [1249, 19, 1001, 42], [1249, 21, 1001, 43], [1249, 30, 1001, 52], [1250, 14, 1001, 53, "style"], [1250, 19, 1001, 58], [1250, 21, 1001, 60, "styles"], [1250, 27, 1001, 66], [1250, 28, 1001, 67, "successIcon"], [1251, 12, 1001, 79], [1252, 14, 1001, 79, "fileName"], [1252, 22, 1001, 79], [1252, 24, 1001, 79, "_jsxFileName"], [1252, 36, 1001, 79], [1253, 14, 1001, 79, "lineNumber"], [1253, 24, 1001, 79], [1254, 14, 1001, 79, "columnNumber"], [1254, 26, 1001, 79], [1255, 12, 1001, 79], [1255, 19, 1001, 81], [1255, 20, 1002, 13], [1256, 10, 1002, 13], [1257, 12, 1002, 13, "fileName"], [1257, 20, 1002, 13], [1257, 22, 1002, 13, "_jsxFileName"], [1257, 34, 1002, 13], [1258, 12, 1002, 13, "lineNumber"], [1258, 22, 1002, 13], [1259, 12, 1002, 13, "columnNumber"], [1259, 24, 1002, 13], [1260, 10, 1002, 13], [1260, 17, 1003, 16], [1261, 8, 1003, 17], [1262, 10, 1003, 17, "fileName"], [1262, 18, 1003, 17], [1262, 20, 1003, 17, "_jsxFileName"], [1262, 32, 1003, 17], [1263, 10, 1003, 17, "lineNumber"], [1263, 20, 1003, 17], [1264, 10, 1003, 17, "columnNumber"], [1264, 22, 1003, 17], [1265, 8, 1003, 17], [1265, 15, 1004, 14], [1266, 6, 1004, 15], [1267, 8, 1004, 15, "fileName"], [1267, 16, 1004, 15], [1267, 18, 1004, 15, "_jsxFileName"], [1267, 30, 1004, 15], [1268, 8, 1004, 15, "lineNumber"], [1268, 18, 1004, 15], [1269, 8, 1004, 15, "columnNumber"], [1269, 20, 1004, 15], [1270, 6, 1004, 15], [1270, 13, 1005, 13], [1270, 14, 1005, 14], [1270, 29, 1007, 6], [1270, 33, 1007, 6, "_jsxDevRuntime"], [1270, 47, 1007, 6], [1270, 48, 1007, 6, "jsxDEV"], [1270, 54, 1007, 6], [1270, 56, 1007, 7, "_Modal"], [1270, 62, 1007, 7], [1270, 63, 1007, 7, "default"], [1270, 70, 1007, 12], [1271, 8, 1008, 8, "visible"], [1271, 15, 1008, 15], [1271, 17, 1008, 17, "processingState"], [1271, 32, 1008, 32], [1271, 37, 1008, 37], [1271, 44, 1008, 45], [1272, 8, 1009, 8, "transparent"], [1272, 19, 1009, 19], [1273, 8, 1010, 8, "animationType"], [1273, 21, 1010, 21], [1273, 23, 1010, 22], [1273, 29, 1010, 28], [1274, 8, 1010, 28, "children"], [1274, 16, 1010, 28], [1274, 31, 1012, 8], [1274, 35, 1012, 8, "_jsxDevRuntime"], [1274, 49, 1012, 8], [1274, 50, 1012, 8, "jsxDEV"], [1274, 56, 1012, 8], [1274, 58, 1012, 9, "_View"], [1274, 63, 1012, 9], [1274, 64, 1012, 9, "default"], [1274, 71, 1012, 13], [1275, 10, 1012, 14, "style"], [1275, 15, 1012, 19], [1275, 17, 1012, 21, "styles"], [1275, 23, 1012, 27], [1275, 24, 1012, 28, "processingModal"], [1275, 39, 1012, 44], [1276, 10, 1012, 44, "children"], [1276, 18, 1012, 44], [1276, 33, 1013, 10], [1276, 37, 1013, 10, "_jsxDevRuntime"], [1276, 51, 1013, 10], [1276, 52, 1013, 10, "jsxDEV"], [1276, 58, 1013, 10], [1276, 60, 1013, 11, "_View"], [1276, 65, 1013, 11], [1276, 66, 1013, 11, "default"], [1276, 73, 1013, 15], [1277, 12, 1013, 16, "style"], [1277, 17, 1013, 21], [1277, 19, 1013, 23, "styles"], [1277, 25, 1013, 29], [1277, 26, 1013, 30, "errorContent"], [1277, 38, 1013, 43], [1278, 12, 1013, 43, "children"], [1278, 20, 1013, 43], [1278, 36, 1014, 12], [1278, 40, 1014, 12, "_jsxDevRuntime"], [1278, 54, 1014, 12], [1278, 55, 1014, 12, "jsxDEV"], [1278, 61, 1014, 12], [1278, 63, 1014, 13, "_lucideReactNative"], [1278, 81, 1014, 13], [1278, 82, 1014, 13, "X"], [1278, 83, 1014, 14], [1279, 14, 1014, 15, "size"], [1279, 18, 1014, 19], [1279, 20, 1014, 21], [1279, 22, 1014, 24], [1280, 14, 1014, 25, "color"], [1280, 19, 1014, 30], [1280, 21, 1014, 31], [1281, 12, 1014, 40], [1282, 14, 1014, 40, "fileName"], [1282, 22, 1014, 40], [1282, 24, 1014, 40, "_jsxFileName"], [1282, 36, 1014, 40], [1283, 14, 1014, 40, "lineNumber"], [1283, 24, 1014, 40], [1284, 14, 1014, 40, "columnNumber"], [1284, 26, 1014, 40], [1285, 12, 1014, 40], [1285, 19, 1014, 42], [1285, 20, 1014, 43], [1285, 35, 1015, 12], [1285, 39, 1015, 12, "_jsxDevRuntime"], [1285, 53, 1015, 12], [1285, 54, 1015, 12, "jsxDEV"], [1285, 60, 1015, 12], [1285, 62, 1015, 13, "_Text"], [1285, 67, 1015, 13], [1285, 68, 1015, 13, "default"], [1285, 75, 1015, 17], [1286, 14, 1015, 18, "style"], [1286, 19, 1015, 23], [1286, 21, 1015, 25, "styles"], [1286, 27, 1015, 31], [1286, 28, 1015, 32, "errorTitle"], [1286, 38, 1015, 43], [1287, 14, 1015, 43, "children"], [1287, 22, 1015, 43], [1287, 24, 1015, 44], [1288, 12, 1015, 61], [1289, 14, 1015, 61, "fileName"], [1289, 22, 1015, 61], [1289, 24, 1015, 61, "_jsxFileName"], [1289, 36, 1015, 61], [1290, 14, 1015, 61, "lineNumber"], [1290, 24, 1015, 61], [1291, 14, 1015, 61, "columnNumber"], [1291, 26, 1015, 61], [1292, 12, 1015, 61], [1292, 19, 1015, 67], [1292, 20, 1015, 68], [1292, 35, 1016, 12], [1292, 39, 1016, 12, "_jsxDevRuntime"], [1292, 53, 1016, 12], [1292, 54, 1016, 12, "jsxDEV"], [1292, 60, 1016, 12], [1292, 62, 1016, 13, "_Text"], [1292, 67, 1016, 13], [1292, 68, 1016, 13, "default"], [1292, 75, 1016, 17], [1293, 14, 1016, 18, "style"], [1293, 19, 1016, 23], [1293, 21, 1016, 25, "styles"], [1293, 27, 1016, 31], [1293, 28, 1016, 32, "errorMessage"], [1293, 40, 1016, 45], [1294, 14, 1016, 45, "children"], [1294, 22, 1016, 45], [1294, 24, 1016, 47, "errorMessage"], [1295, 12, 1016, 59], [1296, 14, 1016, 59, "fileName"], [1296, 22, 1016, 59], [1296, 24, 1016, 59, "_jsxFileName"], [1296, 36, 1016, 59], [1297, 14, 1016, 59, "lineNumber"], [1297, 24, 1016, 59], [1298, 14, 1016, 59, "columnNumber"], [1298, 26, 1016, 59], [1299, 12, 1016, 59], [1299, 19, 1016, 66], [1299, 20, 1016, 67], [1299, 35, 1017, 12], [1299, 39, 1017, 12, "_jsxDevRuntime"], [1299, 53, 1017, 12], [1299, 54, 1017, 12, "jsxDEV"], [1299, 60, 1017, 12], [1299, 62, 1017, 13, "_TouchableOpacity"], [1299, 79, 1017, 13], [1299, 80, 1017, 13, "default"], [1299, 87, 1017, 29], [1300, 14, 1018, 14, "onPress"], [1300, 21, 1018, 21], [1300, 23, 1018, 23, "retryCapture"], [1300, 35, 1018, 36], [1301, 14, 1019, 14, "style"], [1301, 19, 1019, 19], [1301, 21, 1019, 21, "styles"], [1301, 27, 1019, 27], [1301, 28, 1019, 28, "primaryButton"], [1301, 41, 1019, 42], [1302, 14, 1019, 42, "children"], [1302, 22, 1019, 42], [1302, 37, 1021, 14], [1302, 41, 1021, 14, "_jsxDevRuntime"], [1302, 55, 1021, 14], [1302, 56, 1021, 14, "jsxDEV"], [1302, 62, 1021, 14], [1302, 64, 1021, 15, "_Text"], [1302, 69, 1021, 15], [1302, 70, 1021, 15, "default"], [1302, 77, 1021, 19], [1303, 16, 1021, 20, "style"], [1303, 21, 1021, 25], [1303, 23, 1021, 27, "styles"], [1303, 29, 1021, 33], [1303, 30, 1021, 34, "primaryButtonText"], [1303, 47, 1021, 52], [1304, 16, 1021, 52, "children"], [1304, 24, 1021, 52], [1304, 26, 1021, 53], [1305, 14, 1021, 62], [1306, 16, 1021, 62, "fileName"], [1306, 24, 1021, 62], [1306, 26, 1021, 62, "_jsxFileName"], [1306, 38, 1021, 62], [1307, 16, 1021, 62, "lineNumber"], [1307, 26, 1021, 62], [1308, 16, 1021, 62, "columnNumber"], [1308, 28, 1021, 62], [1309, 14, 1021, 62], [1309, 21, 1021, 68], [1310, 12, 1021, 69], [1311, 14, 1021, 69, "fileName"], [1311, 22, 1021, 69], [1311, 24, 1021, 69, "_jsxFileName"], [1311, 36, 1021, 69], [1312, 14, 1021, 69, "lineNumber"], [1312, 24, 1021, 69], [1313, 14, 1021, 69, "columnNumber"], [1313, 26, 1021, 69], [1314, 12, 1021, 69], [1314, 19, 1022, 30], [1314, 20, 1022, 31], [1314, 35, 1023, 12], [1314, 39, 1023, 12, "_jsxDevRuntime"], [1314, 53, 1023, 12], [1314, 54, 1023, 12, "jsxDEV"], [1314, 60, 1023, 12], [1314, 62, 1023, 13, "_TouchableOpacity"], [1314, 79, 1023, 13], [1314, 80, 1023, 13, "default"], [1314, 87, 1023, 29], [1315, 14, 1024, 14, "onPress"], [1315, 21, 1024, 21], [1315, 23, 1024, 23, "onCancel"], [1315, 31, 1024, 32], [1316, 14, 1025, 14, "style"], [1316, 19, 1025, 19], [1316, 21, 1025, 21, "styles"], [1316, 27, 1025, 27], [1316, 28, 1025, 28, "secondaryButton"], [1316, 43, 1025, 44], [1317, 14, 1025, 44, "children"], [1317, 22, 1025, 44], [1317, 37, 1027, 14], [1317, 41, 1027, 14, "_jsxDevRuntime"], [1317, 55, 1027, 14], [1317, 56, 1027, 14, "jsxDEV"], [1317, 62, 1027, 14], [1317, 64, 1027, 15, "_Text"], [1317, 69, 1027, 15], [1317, 70, 1027, 15, "default"], [1317, 77, 1027, 19], [1318, 16, 1027, 20, "style"], [1318, 21, 1027, 25], [1318, 23, 1027, 27, "styles"], [1318, 29, 1027, 33], [1318, 30, 1027, 34, "secondaryButtonText"], [1318, 49, 1027, 54], [1319, 16, 1027, 54, "children"], [1319, 24, 1027, 54], [1319, 26, 1027, 55], [1320, 14, 1027, 61], [1321, 16, 1027, 61, "fileName"], [1321, 24, 1027, 61], [1321, 26, 1027, 61, "_jsxFileName"], [1321, 38, 1027, 61], [1322, 16, 1027, 61, "lineNumber"], [1322, 26, 1027, 61], [1323, 16, 1027, 61, "columnNumber"], [1323, 28, 1027, 61], [1324, 14, 1027, 61], [1324, 21, 1027, 67], [1325, 12, 1027, 68], [1326, 14, 1027, 68, "fileName"], [1326, 22, 1027, 68], [1326, 24, 1027, 68, "_jsxFileName"], [1326, 36, 1027, 68], [1327, 14, 1027, 68, "lineNumber"], [1327, 24, 1027, 68], [1328, 14, 1027, 68, "columnNumber"], [1328, 26, 1027, 68], [1329, 12, 1027, 68], [1329, 19, 1028, 30], [1329, 20, 1028, 31], [1330, 10, 1028, 31], [1331, 12, 1028, 31, "fileName"], [1331, 20, 1028, 31], [1331, 22, 1028, 31, "_jsxFileName"], [1331, 34, 1028, 31], [1332, 12, 1028, 31, "lineNumber"], [1332, 22, 1028, 31], [1333, 12, 1028, 31, "columnNumber"], [1333, 24, 1028, 31], [1334, 10, 1028, 31], [1334, 17, 1029, 16], [1335, 8, 1029, 17], [1336, 10, 1029, 17, "fileName"], [1336, 18, 1029, 17], [1336, 20, 1029, 17, "_jsxFileName"], [1336, 32, 1029, 17], [1337, 10, 1029, 17, "lineNumber"], [1337, 20, 1029, 17], [1338, 10, 1029, 17, "columnNumber"], [1338, 22, 1029, 17], [1339, 8, 1029, 17], [1339, 15, 1030, 14], [1340, 6, 1030, 15], [1341, 8, 1030, 15, "fileName"], [1341, 16, 1030, 15], [1341, 18, 1030, 15, "_jsxFileName"], [1341, 30, 1030, 15], [1342, 8, 1030, 15, "lineNumber"], [1342, 18, 1030, 15], [1343, 8, 1030, 15, "columnNumber"], [1343, 20, 1030, 15], [1344, 6, 1030, 15], [1344, 13, 1031, 13], [1344, 14, 1031, 14], [1345, 4, 1031, 14], [1346, 6, 1031, 14, "fileName"], [1346, 14, 1031, 14], [1346, 16, 1031, 14, "_jsxFileName"], [1346, 28, 1031, 14], [1347, 6, 1031, 14, "lineNumber"], [1347, 16, 1031, 14], [1348, 6, 1031, 14, "columnNumber"], [1348, 18, 1031, 14], [1349, 4, 1031, 14], [1349, 11, 1032, 10], [1349, 12, 1032, 11], [1350, 2, 1034, 0], [1351, 2, 1034, 1, "_s"], [1351, 4, 1034, 1], [1351, 5, 51, 24, "EchoCameraWeb"], [1351, 18, 51, 37], [1352, 4, 51, 37], [1352, 12, 58, 42, "useCameraPermissions"], [1352, 44, 58, 62], [1352, 46, 72, 19, "useUpload"], [1352, 64, 72, 28], [1353, 2, 72, 28], [1354, 2, 72, 28, "_c"], [1354, 4, 72, 28], [1354, 7, 51, 24, "EchoCameraWeb"], [1354, 20, 51, 37], [1355, 2, 1035, 0], [1355, 8, 1035, 6, "styles"], [1355, 14, 1035, 12], [1355, 17, 1035, 15, "StyleSheet"], [1355, 36, 1035, 25], [1355, 37, 1035, 26, "create"], [1355, 43, 1035, 32], [1355, 44, 1035, 33], [1356, 4, 1036, 2, "container"], [1356, 13, 1036, 11], [1356, 15, 1036, 13], [1357, 6, 1037, 4, "flex"], [1357, 10, 1037, 8], [1357, 12, 1037, 10], [1357, 13, 1037, 11], [1358, 6, 1038, 4, "backgroundColor"], [1358, 21, 1038, 19], [1358, 23, 1038, 21], [1359, 4, 1039, 2], [1359, 5, 1039, 3], [1360, 4, 1040, 2, "cameraContainer"], [1360, 19, 1040, 17], [1360, 21, 1040, 19], [1361, 6, 1041, 4, "flex"], [1361, 10, 1041, 8], [1361, 12, 1041, 10], [1361, 13, 1041, 11], [1362, 6, 1042, 4, "max<PERSON><PERSON><PERSON>"], [1362, 14, 1042, 12], [1362, 16, 1042, 14], [1362, 19, 1042, 17], [1363, 6, 1043, 4, "alignSelf"], [1363, 15, 1043, 13], [1363, 17, 1043, 15], [1363, 25, 1043, 23], [1364, 6, 1044, 4, "width"], [1364, 11, 1044, 9], [1364, 13, 1044, 11], [1365, 4, 1045, 2], [1365, 5, 1045, 3], [1366, 4, 1046, 2, "camera"], [1366, 10, 1046, 8], [1366, 12, 1046, 10], [1367, 6, 1047, 4, "flex"], [1367, 10, 1047, 8], [1367, 12, 1047, 10], [1368, 4, 1048, 2], [1368, 5, 1048, 3], [1369, 4, 1049, 2, "headerOverlay"], [1369, 17, 1049, 15], [1369, 19, 1049, 17], [1370, 6, 1050, 4, "position"], [1370, 14, 1050, 12], [1370, 16, 1050, 14], [1370, 26, 1050, 24], [1371, 6, 1051, 4, "top"], [1371, 9, 1051, 7], [1371, 11, 1051, 9], [1371, 12, 1051, 10], [1372, 6, 1052, 4, "left"], [1372, 10, 1052, 8], [1372, 12, 1052, 10], [1372, 13, 1052, 11], [1373, 6, 1053, 4, "right"], [1373, 11, 1053, 9], [1373, 13, 1053, 11], [1373, 14, 1053, 12], [1374, 6, 1054, 4, "backgroundColor"], [1374, 21, 1054, 19], [1374, 23, 1054, 21], [1374, 36, 1054, 34], [1375, 6, 1055, 4, "paddingTop"], [1375, 16, 1055, 14], [1375, 18, 1055, 16], [1375, 20, 1055, 18], [1376, 6, 1056, 4, "paddingHorizontal"], [1376, 23, 1056, 21], [1376, 25, 1056, 23], [1376, 27, 1056, 25], [1377, 6, 1057, 4, "paddingBottom"], [1377, 19, 1057, 17], [1377, 21, 1057, 19], [1378, 4, 1058, 2], [1378, 5, 1058, 3], [1379, 4, 1059, 2, "headerContent"], [1379, 17, 1059, 15], [1379, 19, 1059, 17], [1380, 6, 1060, 4, "flexDirection"], [1380, 19, 1060, 17], [1380, 21, 1060, 19], [1380, 26, 1060, 24], [1381, 6, 1061, 4, "justifyContent"], [1381, 20, 1061, 18], [1381, 22, 1061, 20], [1381, 37, 1061, 35], [1382, 6, 1062, 4, "alignItems"], [1382, 16, 1062, 14], [1382, 18, 1062, 16], [1383, 4, 1063, 2], [1383, 5, 1063, 3], [1384, 4, 1064, 2, "headerLeft"], [1384, 14, 1064, 12], [1384, 16, 1064, 14], [1385, 6, 1065, 4, "flex"], [1385, 10, 1065, 8], [1385, 12, 1065, 10], [1386, 4, 1066, 2], [1386, 5, 1066, 3], [1387, 4, 1067, 2, "headerTitle"], [1387, 15, 1067, 13], [1387, 17, 1067, 15], [1388, 6, 1068, 4, "fontSize"], [1388, 14, 1068, 12], [1388, 16, 1068, 14], [1388, 18, 1068, 16], [1389, 6, 1069, 4, "fontWeight"], [1389, 16, 1069, 14], [1389, 18, 1069, 16], [1389, 23, 1069, 21], [1390, 6, 1070, 4, "color"], [1390, 11, 1070, 9], [1390, 13, 1070, 11], [1390, 19, 1070, 17], [1391, 6, 1071, 4, "marginBottom"], [1391, 18, 1071, 16], [1391, 20, 1071, 18], [1392, 4, 1072, 2], [1392, 5, 1072, 3], [1393, 4, 1073, 2, "subtitleRow"], [1393, 15, 1073, 13], [1393, 17, 1073, 15], [1394, 6, 1074, 4, "flexDirection"], [1394, 19, 1074, 17], [1394, 21, 1074, 19], [1394, 26, 1074, 24], [1395, 6, 1075, 4, "alignItems"], [1395, 16, 1075, 14], [1395, 18, 1075, 16], [1395, 26, 1075, 24], [1396, 6, 1076, 4, "marginBottom"], [1396, 18, 1076, 16], [1396, 20, 1076, 18], [1397, 4, 1077, 2], [1397, 5, 1077, 3], [1398, 4, 1078, 2, "webIcon"], [1398, 11, 1078, 9], [1398, 13, 1078, 11], [1399, 6, 1079, 4, "fontSize"], [1399, 14, 1079, 12], [1399, 16, 1079, 14], [1399, 18, 1079, 16], [1400, 6, 1080, 4, "marginRight"], [1400, 17, 1080, 15], [1400, 19, 1080, 17], [1401, 4, 1081, 2], [1401, 5, 1081, 3], [1402, 4, 1082, 2, "headerSubtitle"], [1402, 18, 1082, 16], [1402, 20, 1082, 18], [1403, 6, 1083, 4, "fontSize"], [1403, 14, 1083, 12], [1403, 16, 1083, 14], [1403, 18, 1083, 16], [1404, 6, 1084, 4, "color"], [1404, 11, 1084, 9], [1404, 13, 1084, 11], [1404, 19, 1084, 17], [1405, 6, 1085, 4, "opacity"], [1405, 13, 1085, 11], [1405, 15, 1085, 13], [1406, 4, 1086, 2], [1406, 5, 1086, 3], [1407, 4, 1087, 2, "challengeRow"], [1407, 16, 1087, 14], [1407, 18, 1087, 16], [1408, 6, 1088, 4, "flexDirection"], [1408, 19, 1088, 17], [1408, 21, 1088, 19], [1408, 26, 1088, 24], [1409, 6, 1089, 4, "alignItems"], [1409, 16, 1089, 14], [1409, 18, 1089, 16], [1410, 4, 1090, 2], [1410, 5, 1090, 3], [1411, 4, 1091, 2, "challengeCode"], [1411, 17, 1091, 15], [1411, 19, 1091, 17], [1412, 6, 1092, 4, "fontSize"], [1412, 14, 1092, 12], [1412, 16, 1092, 14], [1412, 18, 1092, 16], [1413, 6, 1093, 4, "color"], [1413, 11, 1093, 9], [1413, 13, 1093, 11], [1413, 19, 1093, 17], [1414, 6, 1094, 4, "marginLeft"], [1414, 16, 1094, 14], [1414, 18, 1094, 16], [1414, 19, 1094, 17], [1415, 6, 1095, 4, "fontFamily"], [1415, 16, 1095, 14], [1415, 18, 1095, 16], [1416, 4, 1096, 2], [1416, 5, 1096, 3], [1417, 4, 1097, 2, "closeButton"], [1417, 15, 1097, 13], [1417, 17, 1097, 15], [1418, 6, 1098, 4, "padding"], [1418, 13, 1098, 11], [1418, 15, 1098, 13], [1419, 4, 1099, 2], [1419, 5, 1099, 3], [1420, 4, 1100, 2, "privacyNotice"], [1420, 17, 1100, 15], [1420, 19, 1100, 17], [1421, 6, 1101, 4, "position"], [1421, 14, 1101, 12], [1421, 16, 1101, 14], [1421, 26, 1101, 24], [1422, 6, 1102, 4, "top"], [1422, 9, 1102, 7], [1422, 11, 1102, 9], [1422, 14, 1102, 12], [1423, 6, 1103, 4, "left"], [1423, 10, 1103, 8], [1423, 12, 1103, 10], [1423, 14, 1103, 12], [1424, 6, 1104, 4, "right"], [1424, 11, 1104, 9], [1424, 13, 1104, 11], [1424, 15, 1104, 13], [1425, 6, 1105, 4, "backgroundColor"], [1425, 21, 1105, 19], [1425, 23, 1105, 21], [1425, 48, 1105, 46], [1426, 6, 1106, 4, "borderRadius"], [1426, 18, 1106, 16], [1426, 20, 1106, 18], [1426, 21, 1106, 19], [1427, 6, 1107, 4, "padding"], [1427, 13, 1107, 11], [1427, 15, 1107, 13], [1427, 17, 1107, 15], [1428, 6, 1108, 4, "flexDirection"], [1428, 19, 1108, 17], [1428, 21, 1108, 19], [1428, 26, 1108, 24], [1429, 6, 1109, 4, "alignItems"], [1429, 16, 1109, 14], [1429, 18, 1109, 16], [1430, 4, 1110, 2], [1430, 5, 1110, 3], [1431, 4, 1111, 2, "privacyText"], [1431, 15, 1111, 13], [1431, 17, 1111, 15], [1432, 6, 1112, 4, "color"], [1432, 11, 1112, 9], [1432, 13, 1112, 11], [1432, 19, 1112, 17], [1433, 6, 1113, 4, "fontSize"], [1433, 14, 1113, 12], [1433, 16, 1113, 14], [1433, 18, 1113, 16], [1434, 6, 1114, 4, "marginLeft"], [1434, 16, 1114, 14], [1434, 18, 1114, 16], [1434, 19, 1114, 17], [1435, 6, 1115, 4, "flex"], [1435, 10, 1115, 8], [1435, 12, 1115, 10], [1436, 4, 1116, 2], [1436, 5, 1116, 3], [1437, 4, 1117, 2, "footer<PERSON><PERSON><PERSON>"], [1437, 17, 1117, 15], [1437, 19, 1117, 17], [1438, 6, 1118, 4, "position"], [1438, 14, 1118, 12], [1438, 16, 1118, 14], [1438, 26, 1118, 24], [1439, 6, 1119, 4, "bottom"], [1439, 12, 1119, 10], [1439, 14, 1119, 12], [1439, 15, 1119, 13], [1440, 6, 1120, 4, "left"], [1440, 10, 1120, 8], [1440, 12, 1120, 10], [1440, 13, 1120, 11], [1441, 6, 1121, 4, "right"], [1441, 11, 1121, 9], [1441, 13, 1121, 11], [1441, 14, 1121, 12], [1442, 6, 1122, 4, "backgroundColor"], [1442, 21, 1122, 19], [1442, 23, 1122, 21], [1442, 36, 1122, 34], [1443, 6, 1123, 4, "paddingBottom"], [1443, 19, 1123, 17], [1443, 21, 1123, 19], [1443, 23, 1123, 21], [1444, 6, 1124, 4, "paddingTop"], [1444, 16, 1124, 14], [1444, 18, 1124, 16], [1444, 20, 1124, 18], [1445, 6, 1125, 4, "alignItems"], [1445, 16, 1125, 14], [1445, 18, 1125, 16], [1446, 4, 1126, 2], [1446, 5, 1126, 3], [1447, 4, 1127, 2, "instruction"], [1447, 15, 1127, 13], [1447, 17, 1127, 15], [1448, 6, 1128, 4, "fontSize"], [1448, 14, 1128, 12], [1448, 16, 1128, 14], [1448, 18, 1128, 16], [1449, 6, 1129, 4, "color"], [1449, 11, 1129, 9], [1449, 13, 1129, 11], [1449, 19, 1129, 17], [1450, 6, 1130, 4, "marginBottom"], [1450, 18, 1130, 16], [1450, 20, 1130, 18], [1451, 4, 1131, 2], [1451, 5, 1131, 3], [1452, 4, 1132, 2, "shutterButton"], [1452, 17, 1132, 15], [1452, 19, 1132, 17], [1453, 6, 1133, 4, "width"], [1453, 11, 1133, 9], [1453, 13, 1133, 11], [1453, 15, 1133, 13], [1454, 6, 1134, 4, "height"], [1454, 12, 1134, 10], [1454, 14, 1134, 12], [1454, 16, 1134, 14], [1455, 6, 1135, 4, "borderRadius"], [1455, 18, 1135, 16], [1455, 20, 1135, 18], [1455, 22, 1135, 20], [1456, 6, 1136, 4, "backgroundColor"], [1456, 21, 1136, 19], [1456, 23, 1136, 21], [1456, 29, 1136, 27], [1457, 6, 1137, 4, "justifyContent"], [1457, 20, 1137, 18], [1457, 22, 1137, 20], [1457, 30, 1137, 28], [1458, 6, 1138, 4, "alignItems"], [1458, 16, 1138, 14], [1458, 18, 1138, 16], [1458, 26, 1138, 24], [1459, 6, 1139, 4, "marginBottom"], [1459, 18, 1139, 16], [1459, 20, 1139, 18], [1459, 22, 1139, 20], [1460, 6, 1140, 4], [1460, 9, 1140, 7, "Platform"], [1460, 26, 1140, 15], [1460, 27, 1140, 16, "select"], [1460, 33, 1140, 22], [1460, 34, 1140, 23], [1461, 8, 1141, 6, "ios"], [1461, 11, 1141, 9], [1461, 13, 1141, 11], [1462, 10, 1142, 8, "shadowColor"], [1462, 21, 1142, 19], [1462, 23, 1142, 21], [1462, 32, 1142, 30], [1463, 10, 1143, 8, "shadowOffset"], [1463, 22, 1143, 20], [1463, 24, 1143, 22], [1464, 12, 1143, 24, "width"], [1464, 17, 1143, 29], [1464, 19, 1143, 31], [1464, 20, 1143, 32], [1465, 12, 1143, 34, "height"], [1465, 18, 1143, 40], [1465, 20, 1143, 42], [1466, 10, 1143, 44], [1466, 11, 1143, 45], [1467, 10, 1144, 8, "shadowOpacity"], [1467, 23, 1144, 21], [1467, 25, 1144, 23], [1467, 28, 1144, 26], [1468, 10, 1145, 8, "shadowRadius"], [1468, 22, 1145, 20], [1468, 24, 1145, 22], [1469, 8, 1146, 6], [1469, 9, 1146, 7], [1470, 8, 1147, 6, "android"], [1470, 15, 1147, 13], [1470, 17, 1147, 15], [1471, 10, 1148, 8, "elevation"], [1471, 19, 1148, 17], [1471, 21, 1148, 19], [1472, 8, 1149, 6], [1472, 9, 1149, 7], [1473, 8, 1150, 6, "web"], [1473, 11, 1150, 9], [1473, 13, 1150, 11], [1474, 10, 1151, 8, "boxShadow"], [1474, 19, 1151, 17], [1474, 21, 1151, 19], [1475, 8, 1152, 6], [1476, 6, 1153, 4], [1476, 7, 1153, 5], [1477, 4, 1154, 2], [1477, 5, 1154, 3], [1478, 4, 1155, 2, "shutterButtonDisabled"], [1478, 25, 1155, 23], [1478, 27, 1155, 25], [1479, 6, 1156, 4, "opacity"], [1479, 13, 1156, 11], [1479, 15, 1156, 13], [1480, 4, 1157, 2], [1480, 5, 1157, 3], [1481, 4, 1158, 2, "shutterInner"], [1481, 16, 1158, 14], [1481, 18, 1158, 16], [1482, 6, 1159, 4, "width"], [1482, 11, 1159, 9], [1482, 13, 1159, 11], [1482, 15, 1159, 13], [1483, 6, 1160, 4, "height"], [1483, 12, 1160, 10], [1483, 14, 1160, 12], [1483, 16, 1160, 14], [1484, 6, 1161, 4, "borderRadius"], [1484, 18, 1161, 16], [1484, 20, 1161, 18], [1484, 22, 1161, 20], [1485, 6, 1162, 4, "backgroundColor"], [1485, 21, 1162, 19], [1485, 23, 1162, 21], [1485, 29, 1162, 27], [1486, 6, 1163, 4, "borderWidth"], [1486, 17, 1163, 15], [1486, 19, 1163, 17], [1486, 20, 1163, 18], [1487, 6, 1164, 4, "borderColor"], [1487, 17, 1164, 15], [1487, 19, 1164, 17], [1488, 4, 1165, 2], [1488, 5, 1165, 3], [1489, 4, 1166, 2, "privacyNote"], [1489, 15, 1166, 13], [1489, 17, 1166, 15], [1490, 6, 1167, 4, "fontSize"], [1490, 14, 1167, 12], [1490, 16, 1167, 14], [1490, 18, 1167, 16], [1491, 6, 1168, 4, "color"], [1491, 11, 1168, 9], [1491, 13, 1168, 11], [1492, 4, 1169, 2], [1492, 5, 1169, 3], [1493, 4, 1170, 2, "processingModal"], [1493, 19, 1170, 17], [1493, 21, 1170, 19], [1494, 6, 1171, 4, "flex"], [1494, 10, 1171, 8], [1494, 12, 1171, 10], [1494, 13, 1171, 11], [1495, 6, 1172, 4, "backgroundColor"], [1495, 21, 1172, 19], [1495, 23, 1172, 21], [1495, 43, 1172, 41], [1496, 6, 1173, 4, "justifyContent"], [1496, 20, 1173, 18], [1496, 22, 1173, 20], [1496, 30, 1173, 28], [1497, 6, 1174, 4, "alignItems"], [1497, 16, 1174, 14], [1497, 18, 1174, 16], [1498, 4, 1175, 2], [1498, 5, 1175, 3], [1499, 4, 1176, 2, "processingContent"], [1499, 21, 1176, 19], [1499, 23, 1176, 21], [1500, 6, 1177, 4, "backgroundColor"], [1500, 21, 1177, 19], [1500, 23, 1177, 21], [1500, 29, 1177, 27], [1501, 6, 1178, 4, "borderRadius"], [1501, 18, 1178, 16], [1501, 20, 1178, 18], [1501, 22, 1178, 20], [1502, 6, 1179, 4, "padding"], [1502, 13, 1179, 11], [1502, 15, 1179, 13], [1502, 17, 1179, 15], [1503, 6, 1180, 4, "width"], [1503, 11, 1180, 9], [1503, 13, 1180, 11], [1503, 18, 1180, 16], [1504, 6, 1181, 4, "max<PERSON><PERSON><PERSON>"], [1504, 14, 1181, 12], [1504, 16, 1181, 14], [1504, 19, 1181, 17], [1505, 6, 1182, 4, "alignItems"], [1505, 16, 1182, 14], [1505, 18, 1182, 16], [1506, 4, 1183, 2], [1506, 5, 1183, 3], [1507, 4, 1184, 2, "processingTitle"], [1507, 19, 1184, 17], [1507, 21, 1184, 19], [1508, 6, 1185, 4, "fontSize"], [1508, 14, 1185, 12], [1508, 16, 1185, 14], [1508, 18, 1185, 16], [1509, 6, 1186, 4, "fontWeight"], [1509, 16, 1186, 14], [1509, 18, 1186, 16], [1509, 23, 1186, 21], [1510, 6, 1187, 4, "color"], [1510, 11, 1187, 9], [1510, 13, 1187, 11], [1510, 22, 1187, 20], [1511, 6, 1188, 4, "marginTop"], [1511, 15, 1188, 13], [1511, 17, 1188, 15], [1511, 19, 1188, 17], [1512, 6, 1189, 4, "marginBottom"], [1512, 18, 1189, 16], [1512, 20, 1189, 18], [1513, 4, 1190, 2], [1513, 5, 1190, 3], [1514, 4, 1191, 2, "progressBar"], [1514, 15, 1191, 13], [1514, 17, 1191, 15], [1515, 6, 1192, 4, "width"], [1515, 11, 1192, 9], [1515, 13, 1192, 11], [1515, 19, 1192, 17], [1516, 6, 1193, 4, "height"], [1516, 12, 1193, 10], [1516, 14, 1193, 12], [1516, 15, 1193, 13], [1517, 6, 1194, 4, "backgroundColor"], [1517, 21, 1194, 19], [1517, 23, 1194, 21], [1517, 32, 1194, 30], [1518, 6, 1195, 4, "borderRadius"], [1518, 18, 1195, 16], [1518, 20, 1195, 18], [1518, 21, 1195, 19], [1519, 6, 1196, 4, "overflow"], [1519, 14, 1196, 12], [1519, 16, 1196, 14], [1519, 24, 1196, 22], [1520, 6, 1197, 4, "marginBottom"], [1520, 18, 1197, 16], [1520, 20, 1197, 18], [1521, 4, 1198, 2], [1521, 5, 1198, 3], [1522, 4, 1199, 2, "progressFill"], [1522, 16, 1199, 14], [1522, 18, 1199, 16], [1523, 6, 1200, 4, "height"], [1523, 12, 1200, 10], [1523, 14, 1200, 12], [1523, 20, 1200, 18], [1524, 6, 1201, 4, "backgroundColor"], [1524, 21, 1201, 19], [1524, 23, 1201, 21], [1524, 32, 1201, 30], [1525, 6, 1202, 4, "borderRadius"], [1525, 18, 1202, 16], [1525, 20, 1202, 18], [1526, 4, 1203, 2], [1526, 5, 1203, 3], [1527, 4, 1204, 2, "processingDescription"], [1527, 25, 1204, 23], [1527, 27, 1204, 25], [1528, 6, 1205, 4, "fontSize"], [1528, 14, 1205, 12], [1528, 16, 1205, 14], [1528, 18, 1205, 16], [1529, 6, 1206, 4, "color"], [1529, 11, 1206, 9], [1529, 13, 1206, 11], [1529, 22, 1206, 20], [1530, 6, 1207, 4, "textAlign"], [1530, 15, 1207, 13], [1530, 17, 1207, 15], [1531, 4, 1208, 2], [1531, 5, 1208, 3], [1532, 4, 1209, 2, "successIcon"], [1532, 15, 1209, 13], [1532, 17, 1209, 15], [1533, 6, 1210, 4, "marginTop"], [1533, 15, 1210, 13], [1533, 17, 1210, 15], [1534, 4, 1211, 2], [1534, 5, 1211, 3], [1535, 4, 1212, 2, "errorContent"], [1535, 16, 1212, 14], [1535, 18, 1212, 16], [1536, 6, 1213, 4, "backgroundColor"], [1536, 21, 1213, 19], [1536, 23, 1213, 21], [1536, 29, 1213, 27], [1537, 6, 1214, 4, "borderRadius"], [1537, 18, 1214, 16], [1537, 20, 1214, 18], [1537, 22, 1214, 20], [1538, 6, 1215, 4, "padding"], [1538, 13, 1215, 11], [1538, 15, 1215, 13], [1538, 17, 1215, 15], [1539, 6, 1216, 4, "width"], [1539, 11, 1216, 9], [1539, 13, 1216, 11], [1539, 18, 1216, 16], [1540, 6, 1217, 4, "max<PERSON><PERSON><PERSON>"], [1540, 14, 1217, 12], [1540, 16, 1217, 14], [1540, 19, 1217, 17], [1541, 6, 1218, 4, "alignItems"], [1541, 16, 1218, 14], [1541, 18, 1218, 16], [1542, 4, 1219, 2], [1542, 5, 1219, 3], [1543, 4, 1220, 2, "errorTitle"], [1543, 14, 1220, 12], [1543, 16, 1220, 14], [1544, 6, 1221, 4, "fontSize"], [1544, 14, 1221, 12], [1544, 16, 1221, 14], [1544, 18, 1221, 16], [1545, 6, 1222, 4, "fontWeight"], [1545, 16, 1222, 14], [1545, 18, 1222, 16], [1545, 23, 1222, 21], [1546, 6, 1223, 4, "color"], [1546, 11, 1223, 9], [1546, 13, 1223, 11], [1546, 22, 1223, 20], [1547, 6, 1224, 4, "marginTop"], [1547, 15, 1224, 13], [1547, 17, 1224, 15], [1547, 19, 1224, 17], [1548, 6, 1225, 4, "marginBottom"], [1548, 18, 1225, 16], [1548, 20, 1225, 18], [1549, 4, 1226, 2], [1549, 5, 1226, 3], [1550, 4, 1227, 2, "errorMessage"], [1550, 16, 1227, 14], [1550, 18, 1227, 16], [1551, 6, 1228, 4, "fontSize"], [1551, 14, 1228, 12], [1551, 16, 1228, 14], [1551, 18, 1228, 16], [1552, 6, 1229, 4, "color"], [1552, 11, 1229, 9], [1552, 13, 1229, 11], [1552, 22, 1229, 20], [1553, 6, 1230, 4, "textAlign"], [1553, 15, 1230, 13], [1553, 17, 1230, 15], [1553, 25, 1230, 23], [1554, 6, 1231, 4, "marginBottom"], [1554, 18, 1231, 16], [1554, 20, 1231, 18], [1555, 4, 1232, 2], [1555, 5, 1232, 3], [1556, 4, 1233, 2, "primaryButton"], [1556, 17, 1233, 15], [1556, 19, 1233, 17], [1557, 6, 1234, 4, "backgroundColor"], [1557, 21, 1234, 19], [1557, 23, 1234, 21], [1557, 32, 1234, 30], [1558, 6, 1235, 4, "paddingHorizontal"], [1558, 23, 1235, 21], [1558, 25, 1235, 23], [1558, 27, 1235, 25], [1559, 6, 1236, 4, "paddingVertical"], [1559, 21, 1236, 19], [1559, 23, 1236, 21], [1559, 25, 1236, 23], [1560, 6, 1237, 4, "borderRadius"], [1560, 18, 1237, 16], [1560, 20, 1237, 18], [1560, 21, 1237, 19], [1561, 6, 1238, 4, "marginTop"], [1561, 15, 1238, 13], [1561, 17, 1238, 15], [1562, 4, 1239, 2], [1562, 5, 1239, 3], [1563, 4, 1240, 2, "primaryButtonText"], [1563, 21, 1240, 19], [1563, 23, 1240, 21], [1564, 6, 1241, 4, "color"], [1564, 11, 1241, 9], [1564, 13, 1241, 11], [1564, 19, 1241, 17], [1565, 6, 1242, 4, "fontSize"], [1565, 14, 1242, 12], [1565, 16, 1242, 14], [1565, 18, 1242, 16], [1566, 6, 1243, 4, "fontWeight"], [1566, 16, 1243, 14], [1566, 18, 1243, 16], [1567, 4, 1244, 2], [1567, 5, 1244, 3], [1568, 4, 1245, 2, "secondaryButton"], [1568, 19, 1245, 17], [1568, 21, 1245, 19], [1569, 6, 1246, 4, "paddingHorizontal"], [1569, 23, 1246, 21], [1569, 25, 1246, 23], [1569, 27, 1246, 25], [1570, 6, 1247, 4, "paddingVertical"], [1570, 21, 1247, 19], [1570, 23, 1247, 21], [1570, 25, 1247, 23], [1571, 6, 1248, 4, "marginTop"], [1571, 15, 1248, 13], [1571, 17, 1248, 15], [1572, 4, 1249, 2], [1572, 5, 1249, 3], [1573, 4, 1250, 2, "secondaryButtonText"], [1573, 23, 1250, 21], [1573, 25, 1250, 23], [1574, 6, 1251, 4, "color"], [1574, 11, 1251, 9], [1574, 13, 1251, 11], [1574, 22, 1251, 20], [1575, 6, 1252, 4, "fontSize"], [1575, 14, 1252, 12], [1575, 16, 1252, 14], [1576, 4, 1253, 2], [1576, 5, 1253, 3], [1577, 4, 1254, 2, "permissionContent"], [1577, 21, 1254, 19], [1577, 23, 1254, 21], [1578, 6, 1255, 4, "flex"], [1578, 10, 1255, 8], [1578, 12, 1255, 10], [1578, 13, 1255, 11], [1579, 6, 1256, 4, "justifyContent"], [1579, 20, 1256, 18], [1579, 22, 1256, 20], [1579, 30, 1256, 28], [1580, 6, 1257, 4, "alignItems"], [1580, 16, 1257, 14], [1580, 18, 1257, 16], [1580, 26, 1257, 24], [1581, 6, 1258, 4, "padding"], [1581, 13, 1258, 11], [1581, 15, 1258, 13], [1582, 4, 1259, 2], [1582, 5, 1259, 3], [1583, 4, 1260, 2, "permissionTitle"], [1583, 19, 1260, 17], [1583, 21, 1260, 19], [1584, 6, 1261, 4, "fontSize"], [1584, 14, 1261, 12], [1584, 16, 1261, 14], [1584, 18, 1261, 16], [1585, 6, 1262, 4, "fontWeight"], [1585, 16, 1262, 14], [1585, 18, 1262, 16], [1585, 23, 1262, 21], [1586, 6, 1263, 4, "color"], [1586, 11, 1263, 9], [1586, 13, 1263, 11], [1586, 22, 1263, 20], [1587, 6, 1264, 4, "marginTop"], [1587, 15, 1264, 13], [1587, 17, 1264, 15], [1587, 19, 1264, 17], [1588, 6, 1265, 4, "marginBottom"], [1588, 18, 1265, 16], [1588, 20, 1265, 18], [1589, 4, 1266, 2], [1589, 5, 1266, 3], [1590, 4, 1267, 2, "permissionDescription"], [1590, 25, 1267, 23], [1590, 27, 1267, 25], [1591, 6, 1268, 4, "fontSize"], [1591, 14, 1268, 12], [1591, 16, 1268, 14], [1591, 18, 1268, 16], [1592, 6, 1269, 4, "color"], [1592, 11, 1269, 9], [1592, 13, 1269, 11], [1592, 22, 1269, 20], [1593, 6, 1270, 4, "textAlign"], [1593, 15, 1270, 13], [1593, 17, 1270, 15], [1593, 25, 1270, 23], [1594, 6, 1271, 4, "marginBottom"], [1594, 18, 1271, 16], [1594, 20, 1271, 18], [1595, 4, 1272, 2], [1595, 5, 1272, 3], [1596, 4, 1273, 2, "loadingText"], [1596, 15, 1273, 13], [1596, 17, 1273, 15], [1597, 6, 1274, 4, "color"], [1597, 11, 1274, 9], [1597, 13, 1274, 11], [1597, 22, 1274, 20], [1598, 6, 1275, 4, "marginTop"], [1598, 15, 1275, 13], [1598, 17, 1275, 15], [1599, 4, 1276, 2], [1599, 5, 1276, 3], [1600, 4, 1277, 2], [1601, 4, 1278, 2, "blurZone"], [1601, 12, 1278, 10], [1601, 14, 1278, 12], [1602, 6, 1279, 4, "position"], [1602, 14, 1279, 12], [1602, 16, 1279, 14], [1602, 26, 1279, 24], [1603, 6, 1280, 4, "overflow"], [1603, 14, 1280, 12], [1603, 16, 1280, 14], [1604, 4, 1281, 2], [1604, 5, 1281, 3], [1605, 4, 1282, 2, "previewChip"], [1605, 15, 1282, 13], [1605, 17, 1282, 15], [1606, 6, 1283, 4, "position"], [1606, 14, 1283, 12], [1606, 16, 1283, 14], [1606, 26, 1283, 24], [1607, 6, 1284, 4, "top"], [1607, 9, 1284, 7], [1607, 11, 1284, 9], [1607, 12, 1284, 10], [1608, 6, 1285, 4, "right"], [1608, 11, 1285, 9], [1608, 13, 1285, 11], [1608, 14, 1285, 12], [1609, 6, 1286, 4, "backgroundColor"], [1609, 21, 1286, 19], [1609, 23, 1286, 21], [1609, 40, 1286, 38], [1610, 6, 1287, 4, "paddingHorizontal"], [1610, 23, 1287, 21], [1610, 25, 1287, 23], [1610, 27, 1287, 25], [1611, 6, 1288, 4, "paddingVertical"], [1611, 21, 1288, 19], [1611, 23, 1288, 21], [1611, 24, 1288, 22], [1612, 6, 1289, 4, "borderRadius"], [1612, 18, 1289, 16], [1612, 20, 1289, 18], [1613, 4, 1290, 2], [1613, 5, 1290, 3], [1614, 4, 1291, 2, "previewChipText"], [1614, 19, 1291, 17], [1614, 21, 1291, 19], [1615, 6, 1292, 4, "color"], [1615, 11, 1292, 9], [1615, 13, 1292, 11], [1615, 19, 1292, 17], [1616, 6, 1293, 4, "fontSize"], [1616, 14, 1293, 12], [1616, 16, 1293, 14], [1616, 18, 1293, 16], [1617, 6, 1294, 4, "fontWeight"], [1617, 16, 1294, 14], [1617, 18, 1294, 16], [1618, 4, 1295, 2], [1619, 2, 1296, 0], [1619, 3, 1296, 1], [1619, 4, 1296, 2], [1620, 2, 1296, 3], [1620, 6, 1296, 3, "_c"], [1620, 8, 1296, 3], [1621, 2, 1296, 3, "$RefreshReg$"], [1621, 14, 1296, 3], [1621, 15, 1296, 3, "_c"], [1621, 17, 1296, 3], [1622, 0, 1296, 3], [1622, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdqD;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}