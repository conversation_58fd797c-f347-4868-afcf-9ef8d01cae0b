import { __extends } from "tslib";
import { FaceLandmark68Net } from './FaceLandmark68Net';
export * from './FaceLandmark68Net';
export * from './FaceLandmark68TinyNet';
var FaceLandmarkNet = /** @class */ (function (_super) {
    __extends(FaceLandmarkNet, _super);
    function FaceLandmarkNet() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return FaceLandmarkNet;
}(FaceLandmark68Net));
export { FaceLandmarkNet };
//# sourceMappingURL=index.js.map