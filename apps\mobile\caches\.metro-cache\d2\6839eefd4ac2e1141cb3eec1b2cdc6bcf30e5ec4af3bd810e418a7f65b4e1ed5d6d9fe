{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 73, "index": 73}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TextInput", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "DmXc1F5dPYWntVgqRwh73w0VngA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 53, "column": 0, "index": 320}, "end": {"line": 53, "column": 67, "index": 387}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "expo-status-bar", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 57, "column": 0, "index": 395}, "end": {"line": 57, "column": 44, "index": 439}}], "key": "tlkgvZrxUMG8C7vDDJbsBGIlvhs=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 61, "column": 0, "index": 447}, "end": {"line": 61, "column": 59, "index": 506}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 65, "column": 0, "index": 514}, "end": {"line": 113, "column": 29, "index": 774}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-location", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 117, "column": 0, "index": 782}, "end": {"line": 117, "column": 42, "index": 824}}], "key": "GNP7AGCKsBRUhlnTZ4lIPpbkT9E=", "exportNames": ["*"]}}, {"name": "@/components/EchoCameraUnified", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 121, "column": 0, "index": 832}, "end": {"line": 121, "column": 63, "index": 895}}], "key": "wA7DUOcNir1rKeEVRofkWVobA04=", "exportNames": ["*"]}}, {"name": "@/components/camera/EchoCameraWeb", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 125, "column": 0, "index": 903}, "end": {"line": 125, "column": 62, "index": 965}}], "key": "a5L7e3cPb+NheECyYdST63CXrdc=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 129, "column": 0, "index": 973}, "end": {"line": 129, "column": 85, "index": 1058}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = RespondScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/ScrollView\"));\n  var _TextInput = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/TextInput\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Modal\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/Platform\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Image\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[11], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[12], \"react-native-safe-area-context\");\n  var _expoStatusBar = require(_dependencyMap[13], \"expo-status-bar\");\n  var _expoRouter = require(_dependencyMap[14], \"expo-router\");\n  var _lucideReactNative = require(_dependencyMap[15], \"lucide-react-native\");\n  var Location = _interopRequireWildcard(require(_dependencyMap[16], \"expo-location\"));\n  var _EchoCameraUnified = _interopRequireDefault(require(_dependencyMap[17], \"@/components/EchoCameraUnified\"));\n  var _EchoCameraWeb = _interopRequireDefault(require(_dependencyMap[18], \"@/components/camera/EchoCameraWeb\"));\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[19], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _jsxDevRuntime = require(_dependencyMap[20], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\app\\\\respond\\\\[id].jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function RespondScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const {\n      id\n    } = (0, _expoRouter.useLocalSearchParams)();\n    const [response, setResponse] = (0, _react.useState)(\"\");\n    const [showCamera, setShowCamera] = (0, _react.useState)(false);\n    const [cameraResult, setCameraResult] = (0, _react.useState)(null);\n    const [submitting, setSubmitting] = (0, _react.useState)(false);\n    const [capturedPhotoUri, setCapturedPhotoUri] = (0, _react.useState)(null);\n    const defaultTestingMode = (0, _react.useMemo)(() => {\n      if (_Platform.default.OS !== \"web\" || typeof window === \"undefined\") {\n        return false;\n      }\n      const {\n        protocol,\n        hostname\n      } = window.location;\n      const localHosts = [\"localhost\", \"127.0.0.1\", \"::1\"];\n      return protocol !== \"https:\" || localHosts.includes(hostname);\n    }, []);\n    const [testingMode, setTestingMode] = (0, _react.useState)(defaultTestingMode);\n\n    // Location verification state\n\n    const [locationStatus, setLocationStatus] = (0, _react.useState)(\"checking\"); // 'checking', 'verified', 'too_far', 'error'\n\n    const [currentLocation, setCurrentLocation] = (0, _react.useState)(null);\n    const [distance, setDistance] = (0, _react.useState)(null);\n    const [gettingLocation, setGettingLocation] = (0, _react.useState)(false);\n    const [locationError, setLocationError] = (0, _react.useState)(null);\n\n    // Mock question data - in real app, fetch based on id\n\n    const question = {\n      id: id,\n      question: \"Is the coffee shop on Main Street currently open? I need to know if they have seating available.\",\n      location: \"123 Main Street, Downtown\",\n      coordinates: {\n        // FOR TESTING: Updated to Amadora, Portugal coordinates for testing\n\n        latitude: 38.7555,\n        // Amadora, Portugal\n\n        longitude: -9.2337\n      },\n      reward: 2.5,\n      postedAt: \"2 hours ago\",\n      userId: \"user123\"\n    };\n    const questionLatitude = question.coordinates.latitude;\n    const questionLongitude = question.coordinates.longitude;\n\n    // Calculate distance between two coordinates in meters\n\n    const calculateDistance = (lat1, lon1, lat2, lon2) => {\n      const R = 6371e3; // Earth's radius in meters\n\n      const lat1Rad = lat1 * Math.PI / 180;\n      const lat2Rad = lat2 * Math.PI / 180;\n      const deltaLat = (lat2 - lat1) * Math.PI / 180;\n      const deltaLon = (lon2 - lon1) * Math.PI / 180;\n      const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);\n      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n      return R * c; // Distance in meters\n    };\n\n    // Verify user location\n\n    const verifyLocation = (0, _react.useCallback)(async () => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      try {\n        setGettingLocation(true);\n        setLocationError(null);\n        setLocationStatus(\"checking\");\n\n        // Request location permission\n\n        const {\n          status\n        } = await Location.requestForegroundPermissionsAsync();\n        if (status !== \"granted\") {\n          const message = _Platform.default.OS === \"web\" ? \"Allow location access in your browser settings or enable Testing Mode to continue without verification.\" : \"We need your location to verify you're at the question location.\";\n          setLocationError(message);\n          setLocationStatus(\"error\");\n          _Alert.default.alert(\"Location Required\", message);\n          return;\n        }\n\n        // Get current location\n\n        const locationData = await Location.getCurrentPositionAsync({\n          accuracy: Location.Accuracy.High,\n          timeout: 15000,\n          maximumAge: 60000\n        });\n        const userLat = locationData.coords.latitude;\n        const userLon = locationData.coords.longitude;\n        const questionLat = questionLatitude;\n        const questionLon = questionLongitude;\n\n        // Calculate distance\n\n        const distanceInMeters = calculateDistance(userLat, userLon, questionLat, questionLon);\n        setDistance(Math.round(distanceInMeters));\n        setCurrentLocation({\n          latitude: userLat,\n          longitude: userLon\n        });\n\n        // Check if user is within acceptable range (200 meters)\n\n        const maxDistance = 200;\n        if (distanceInMeters <= maxDistance) {\n          setLocationStatus(\"verified\");\n        } else {\n          setLocationStatus(\"too_far\");\n        }\n      } catch (error) {\n        console.error(\"Error verifying location:\", error);\n        let message = \"Could not verify your location. Please check your GPS and try again.\";\n        if (error?.code === 1) {\n          message = \"Location permission was denied. Enable access in your device or browser settings, or turn on Testing Mode.\";\n        } else if (error?.code === 2) {\n          message = \"We couldn't determine your position. Try moving to an open area or toggling airplane mode.\";\n        } else if (error?.code === 3) {\n          message = \"Location request timed out. Please try again.\";\n        } else if (_Platform.default.OS === \"web\" && typeof error?.message === \"string\" && error.message.toLowerCase().includes(\"secure\")) {\n          message = \"The browser blocked location services on this connection. Use https:// or enable Testing Mode for manual capture.\";\n        }\n        setLocationError(message);\n        setLocationStatus(\"error\");\n        _Alert.default.alert(\"Location Error\", message);\n      } finally {\n        setGettingLocation(false);\n      }\n    }, [questionLatitude, questionLongitude, testingMode]);\n\n    // Verify location on mount or when testing mode changes\n\n    (0, _react.useEffect)(() => {\n      if (testingMode) {\n        setLocationStatus(\"verified\");\n        setLocationError(null);\n        setDistance(0);\n        setCurrentLocation(null);\n        setGettingLocation(false);\n        return;\n      }\n      verifyLocation();\n    }, [testingMode, verifyLocation]);\n    const handleStartCamera = () => {\n      console.log(\"Camera button pressed:\", {\n        locationStatus,\n        testingMode,\n        disabled: locationStatus !== \"verified\" && !testingMode,\n        shouldEnable: locationStatus === \"verified\" || testingMode,\n        existingCameraResult: cameraResult // Log existing result\n      });\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", locationStatus === \"too_far\" ? `You are ${distance || 0}m away from the question location. You need to be within 200m to respond.` : \"Please verify your location first.\");\n        return;\n      }\n      setShowCamera(true);\n    };\n    const handleCameraComplete = (0, _react.useCallback)(result => {\n      console.log('Camera result received:', result); // Debug log\n      console.log('🔍 DEBUGGING: Camera result properties:', {\n        imageUrl: result.imageUrl,\n        localUri: result.localUri,\n        uri: result.uri,\n        publicUrl: result.publicUrl,\n        timestamp: result.timestamp\n      });\n\n      // Extract the URI from various possible sources\n\n      let imageUri = result.imageUrl || result.localUri || result.uri || result.publicUrl;\n      console.log('🔍 DEBUGGING: Selected imageUri:', imageUri);\n\n      // Handle data URIs that might be malformed\n\n      if (imageUri && imageUri.startsWith('data:image')) {\n        // Ensure data URI is properly formatted\n\n        if (!imageUri.includes('base64,')) {\n          console.error('Invalid data URI format:', imageUri.substring(0, 50));\n          imageUri = null;\n        }\n      }\n\n      // For development, use a placeholder if no valid URI\n\n      if (!imageUri && __DEV__) {\n        console.warn('No valid image URI, using placeholder');\n        imageUri = 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Photo+Captured';\n      }\n      setCapturedPhotoUri(imageUri);\n\n      // 🔍 DEBUGGING: Test if blob URL is accessible\n      if (imageUri && imageUri.startsWith('blob:')) {\n        console.log('🔍 DEBUGGING: Testing blob URL accessibility...');\n        fetch(imageUri).then(response => {\n          console.log('🔍 DEBUGGING: Blob URL fetch successful:', response.ok, response.status);\n          return response.blob();\n        }).then(blob => {\n          console.log('🔍 DEBUGGING: Blob size:', blob.size, 'bytes, type:', blob.type);\n        }).catch(error => {\n          console.error('🔍 DEBUGGING: Blob URL fetch failed:', error);\n        });\n      }\n\n      // Normalize the result to ensure we have the correct URI property\n\n      const normalizedResult = {\n        ...result,\n        imageUrl: imageUri,\n        localUri: imageUri,\n        // Store original URI for debugging\n\n        originalUri: result.imageUrl || result.localUri || result.uri || result.publicUrl\n      };\n      console.log('Normalized result with URI:', imageUri);\n      console.log('Full normalized result:', normalizedResult);\n      setCameraResult(normalizedResult);\n      setShowCamera(false);\n\n      // Removed redundant Alert - the UI will show the success state with image\n    }, []);\n    const handleCameraCancel = (0, _react.useCallback)(() => {\n      setShowCamera(false);\n      setCameraResult(null);\n      setCapturedPhotoUri(null);\n    }, []);\n    const submitResponse = async () => {\n      if (locationStatus !== \"verified\" && !testingMode) {\n        _Alert.default.alert(\"Location Required\", \"Please verify your location first.\");\n        return;\n      }\n      if (!cameraResult) {\n        _Alert.default.alert(\"Missing Photo\", \"Please take the required photo first.\");\n        return;\n      }\n      if (!response.trim()) {\n        _Alert.default.alert(\"Missing Text\", \"Please provide a text explanation with your photo.\");\n        return;\n      }\n      setSubmitting(true);\n      try {\n        // TODO: Submit to API\n\n        const responseData = {\n          questionId: id,\n          textResponse: response.trim(),\n          imageUrl: cameraResult.imageUrl,\n          challengeCode: cameraResult.challengeCode,\n          timestamp: cameraResult.timestamp,\n          userLocation: currentLocation,\n          distanceFromQuestion: distance,\n          testingMode: testingMode // Include testing mode flag\n        };\n        console.log(\"Submitting response:\", responseData);\n\n        // Simulate API call\n\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        _Alert.default.alert(\"Response Submitted!\", testingMode ? \"Test response submitted successfully! This was in testing mode.\" : `You'll receive $${question.reward.toFixed(2)} once the questioner confirms your response.`, [{\n          text: \"OK\",\n          onPress: () => _expoRouter.router.back()\n        }]);\n      } catch (error) {\n        console.error(\"Error submitting response:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to submit response. Please try again.\");\n      } finally {\n        setSubmitting(false);\n      }\n    };\n\n    // Location status component\n\n    const LocationStatus = () => {\n      const getStatusConfig = () => {\n        switch (locationStatus) {\n          case \"checking\":\n            return {\n              color: \"#F59E0B\",\n              bgColor: \"#FEF3C7\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#D97706\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1177,\n                columnNumber: 19\n              }, this),\n              title: \"Checking Location\",\n              message: gettingLocation ? \"Getting your current location\" : \"Verifying position\"\n            };\n          case \"verified\":\n            return {\n              color: \"#10B981\",\n              bgColor: \"#D1FAE5\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                size: 16,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 19\n              }, this),\n              title: \"Location Verified\",\n              message: `You're ${distance || 0}m from the question location`\n            };\n          case \"too_far\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1249,\n                columnNumber: 19\n              }, this),\n              title: \"Too Far Away\",\n              message: `You're ${distance || 0}m away (max 200m allowed)`\n            };\n          case \"error\":\n            return {\n              color: \"#EF4444\",\n              bgColor: \"#FEE2E2\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.AlertTriangle, {\n                size: 16,\n                color: \"#DC2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1281,\n                columnNumber: 19\n              }, this),\n              title: \"Location Error\",\n              message: \"Could not verify your location\"\n            };\n          default:\n            return {\n              color: \"#6B7280\",\n              bgColor: \"#F3F4F6\",\n              icon: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Navigation, {\n                size: 16,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 19\n              }, this),\n              title: \"Unknown Status\",\n              message: \"Please try again\"\n            };\n        }\n      };\n      const config = getStatusConfig();\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: config.bgColor,\n          borderRadius: 12,\n          padding: 16,\n          marginBottom: 24,\n          borderWidth: 1,\n          borderColor: config.color + \"40\"\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 8\n          },\n          children: [config.icon, /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontWeight: \"600\",\n              color: config.color,\n              marginLeft: 8\n            },\n            children: config.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1417,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1385,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: {\n            fontSize: 14,\n            color: config.color,\n            lineHeight: 20\n          },\n          children: config.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1461,\n          columnNumber: 9\n        }, this), (locationStatus === \"too_far\" || locationStatus === \"error\") && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            marginTop: 12\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: verifyLocation,\n            disabled: gettingLocation,\n            style: {\n              backgroundColor: config.color,\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              opacity: gettingLocation ? 0.6 : 1\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: gettingLocation ? \"Checking\" : \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1525,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1481,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => {\n              console.log(\"Toggle testing mode:\", {\n                before: testingMode,\n                after: !testingMode\n              });\n              setTestingMode(!testingMode);\n            },\n            style: {\n              backgroundColor: testingMode ? \"#10B981\" : \"#6B7280\",\n              borderRadius: 8,\n              paddingVertical: 8,\n              paddingHorizontal: 12,\n              marginLeft: 12\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#fff\",\n                fontWeight: \"500\"\n              },\n              children: testingMode ? \"Testing ON\" : \"Enable Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1605,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1477,\n          columnNumber: 11\n        }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#FEF3C7\",\n            borderRadius: 8,\n            padding: 12,\n            marginTop: 12,\n            borderWidth: 1,\n            borderColor: \"#F59E0B\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 14,\n              fontWeight: \"600\",\n              color: \"#D97706\",\n              marginBottom: 4\n            },\n            children: \"Testing Mode Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1673,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 13,\n              color: \"#D97706\",\n              lineHeight: 18\n            },\n            children: \"Location verification bypassed for testing. You can now use the camera regardless of your location.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1713,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1633,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1345,\n        columnNumber: 7\n      }, this);\n    };\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: \"#F9FAFB\"\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoStatusBar.StatusBar, {\n        style: \"dark\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1757,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          backgroundColor: \"#fff\",\n          paddingTop: insets.top + 8,\n          paddingHorizontal: 20,\n          paddingBottom: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: \"#E5E7EB\",\n          zIndex: 1000\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: () => _expoRouter.router.back(),\n            style: {\n              marginRight: 16\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.ArrowLeft, {\n              size: 24,\n              color: \"#111827\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1853,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1837,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 20,\n              fontWeight: \"bold\",\n              color: \"#111827\",\n              flex: 1\n            },\n            children: \"Respond to Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1861,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 10,\n              color: \"#EF4444\"\n            },\n            children: `DEBUG: testing=${testingMode ? \"ON\" : \"OFF\"}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1905,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1809,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            backgroundColor: \"#F0F9FF\",\n            borderRadius: 12,\n            padding: 16,\n            borderLeftWidth: 4,\n            borderLeftColor: \"#3B82F6\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              color: \"#1E40AF\",\n              fontWeight: \"500\",\n              marginBottom: 12\n            },\n            children: question.question\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1961,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MapPin, {\n              size: 14,\n              color: \"#6B7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2029,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 14,\n                color: \"#6B7280\",\n                marginLeft: 6,\n                flex: 1\n              },\n              children: question.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2033,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2001,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              flexDirection: \"row\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.DollarSign, {\n                size: 14,\n                color: \"#059669\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2089,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#059669\",\n                  fontWeight: \"600\",\n                  marginLeft: 2\n                },\n                children: `$${question.reward.toFixed(2)} reward`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2093,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2085,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                flexDirection: \"row\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Clock, {\n                size: 14,\n                color: \"#6B7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2141,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 12,\n                  color: \"#6B7280\",\n                  marginLeft: 4\n                },\n                children: question.postedAt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2145,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2137,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2057,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1925,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1765,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        behavior: \"padding\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingBottom: insets.bottom + 100\n          },\n          showsVerticalScrollIndicator: false,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              padding: 20\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(LocationStatus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2205,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: capturedPhotoUri ? '#10B981' : locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    marginRight: 12\n                  },\n                  children: capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2259,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 12,\n                      fontWeight: '600'\n                    },\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2263,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2227,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Capture Privacy-Safe Photo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2269,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2213,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: '#6B7280',\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Take a privacy-safe photo using our real-time face blurring camera. Faces are automatically blurred before the photo is captured.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2289,\n                columnNumber: 15\n              }, this), capturedPhotoUri ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: '#fff',\n                  borderRadius: 16,\n                  overflow: 'hidden',\n                  borderWidth: 1,\n                  borderColor: '#E5E7EB',\n                  ..._Platform.default.select({\n                    ios: {\n                      shadowColor: '#000',\n                      shadowOffset: {\n                        width: 0,\n                        height: 2\n                      },\n                      shadowOpacity: 0.05,\n                      shadowRadius: 8\n                    },\n                    android: {\n                      elevation: 3\n                    },\n                    web: {\n                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\n                    }\n                  })\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Image.default, {\n                    source: {\n                      uri: capturedPhotoUri\n                    },\n                    style: {\n                      width: '100%',\n                      height: 400,\n                      // Increased from 240 to 400 for better visibility\n                      backgroundColor: '#F3F4F6',\n                      borderRadius: 12 // Add slight rounding for better appearance\n                    },\n                    resizeMode: \"contain\" // Changed from \"cover\" to \"contain\" to show full image\n                    ,\n\n                    onError: e => {\n                      console.error('[IMAGE ERROR] Failed to load image:', {\n                        error: e.nativeEvent?.error,\n                        uri: capturedPhotoUri,\n                        cameraResult: JSON.stringify(cameraResult, null, 2)\n                      });\n                    },\n                    onLoad: () => {\n                      console.log('[IMAGE SUCCESS] Image loaded successfully:', capturedPhotoUri);\n                    },\n                    onLoadStart: () => {\n                      console.log('[IMAGE START] Loading image from:', capturedPhotoUri);\n                      console.log('🔍 DEBUGGING: Image source details:', {\n                        capturedPhotoUri,\n                        isBlob: capturedPhotoUri?.startsWith('blob:'),\n                        isDataUri: capturedPhotoUri?.startsWith('data:'),\n                        length: capturedPhotoUri?.length\n                      });\n                    },\n                    onLoadEnd: () => {\n                      console.log('[IMAGE END] Image loading finished');\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      position: 'absolute',\n                      top: 12,\n                      right: 12,\n                      backgroundColor: 'rgba(16, 185, 129, 0.95)',\n                      paddingHorizontal: 12,\n                      paddingVertical: 6,\n                      borderRadius: 20,\n                      flexDirection: 'row',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                      size: 14,\n                      color: \"#fff\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2438,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 12,\n                        fontWeight: '600',\n                        marginLeft: 4\n                      },\n                      children: \"Privacy Protected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2440,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2412,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2357,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    padding: 16\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'center',\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        width: 32,\n                        height: 32,\n                        borderRadius: 16,\n                        backgroundColor: '#D1FAE5',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 20,\n                        color: \"#10B981\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2486,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2466,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        marginLeft: 12,\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 18,\n                          fontWeight: '700',\n                          color: '#111827'\n                        },\n                        children: \"Photo Captured Successfully\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2492,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 13,\n                          color: '#6B7280',\n                          marginTop: 2\n                        },\n                        children: \"Ready to submit with your response\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2498,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2490,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2452,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      backgroundColor: '#F0FDF4',\n                      borderRadius: 12,\n                      padding: 12,\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                      style: {\n                        fontSize: 12,\n                        fontWeight: '600',\n                        color: '#15803D',\n                        marginBottom: 8,\n                        textTransform: 'uppercase',\n                        letterSpacing: 0.5\n                      },\n                      children: \"Protection Applied\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2524,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2550,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: \"Faces automatically blurred in real-time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2552,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2548,\n                      columnNumber: 23\n                    }, this), cameraResult?.challengeCode && cameraResult.challengeCode.trim() && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        marginBottom: 6,\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2564,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Challenge verified: ${cameraResult.challengeCode || 'N/A'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2566,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2562,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                      style: {\n                        flexDirection: 'row',\n                        alignItems: 'flex-start'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                        size: 14,\n                        color: \"#15803D\",\n                        style: {\n                          marginTop: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2578,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 14,\n                          color: '#15803D',\n                          marginLeft: 8,\n                          flex: 1\n                        },\n                        children: `Location confirmed: ${distance || 0}m away`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2580,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2576,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2508,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                    style: {\n                      flexDirection: 'row'\n                    },\n                    children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                      onPress: () => {\n                        setCameraResult(null);\n                        setCapturedPhotoUri(null);\n                        handleStartCamera();\n                      },\n                      style: {\n                        flex: 1,\n                        backgroundColor: '#fff',\n                        borderWidth: 1,\n                        borderColor: '#D1D5DB',\n                        borderRadius: 12,\n                        paddingVertical: 12,\n                        paddingHorizontal: 16,\n                        flexDirection: 'row',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                        size: 18,\n                        color: \"#6B7280\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2630,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                        style: {\n                          fontSize: 15,\n                          color: '#6B7280',\n                          fontWeight: '600',\n                          marginLeft: 8\n                        },\n                        children: \"Retake Photo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2632,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2592,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2590,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2450,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2311,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: handleStartCamera,\n                disabled: locationStatus !== 'verified' && !testingMode,\n                style: {\n                  backgroundColor: locationStatus === 'verified' || testingMode ? '#3B82F6' : '#9CA3AF',\n                  borderRadius: 12,\n                  padding: 16,\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n                  size: 20,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2674,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 16,\n                    fontWeight: '600',\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: locationStatus === 'verified' || testingMode ? 'Start Camera' : 'Verify Location First'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2676,\n                  columnNumber: 19\n                }, this), testingMode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 10,\n                    color: '#fff',\n                    marginLeft: 8\n                  },\n                  children: \"TEST\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2702,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2648,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2211,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  flexDirection: \"row\",\n                  alignItems: \"center\",\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    width: 24,\n                    height: 24,\n                    borderRadius: 12,\n                    backgroundColor: response.trim() ? \"#10B981\" : cameraResult ? \"#3B82F6\" : \"#9CA3AF\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    marginRight: 12\n                  },\n                  children: response.trim() ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle2, {\n                    size: 16,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2737,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      color: \"#fff\",\n                      fontSize: 12,\n                      fontWeight: \"600\"\n                    },\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2739,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2721,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: {\n                    fontSize: 18,\n                    fontWeight: \"600\",\n                    color: \"#111827\"\n                  },\n                  children: \"Add Text Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2747,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2714,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  color: \"#6B7280\",\n                  marginBottom: 16,\n                  lineHeight: 20\n                },\n                children: `Describe what your photo shows. Be specific and helpful to answer the question completely.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2758,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  backgroundColor: \"#fff\",\n                  borderRadius: 12,\n                  borderWidth: 1,\n                  borderColor: \"#E5E7EB\",\n                  padding: 4\n                },\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    alignItems: \"flex-start\",\n                    padding: 12\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageCircle, {\n                    size: 20,\n                    color: \"#6B7280\",\n                    style: {\n                      marginTop: 2,\n                      marginRight: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2785,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextInput.default, {\n                    style: {\n                      flex: 1,\n                      fontSize: 16,\n                      color: \"#111827\",\n                      minHeight: 100,\n                      textAlignVertical: \"top\"\n                    },\n                    placeholder: \"Describe what you can see that answers their question\",\n                    placeholderTextColor: \"#9CA3AF\",\n                    value: response,\n                    onChangeText: setResponse,\n                    multiline: true,\n                    maxLength: 500\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2790,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2778,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: {\n                    flexDirection: \"row\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    paddingHorizontal: 16,\n                    paddingBottom: 8\n                  },\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#6B7280\"\n                    },\n                    children: \"Be specific and helpful\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2816,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: {\n                      fontSize: 12,\n                      color: \"#9CA3AF\"\n                    },\n                    children: `${response.length}/500`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2819,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2807,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2769,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2713,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: \"#EBF5FF\",\n                borderRadius: 12,\n                padding: 16,\n                marginBottom: 24\n              },\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontWeight: \"600\",\n                  color: \"#1E40AF\",\n                  marginBottom: 8\n                },\n                children: \"Privacy Protection Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2861,\n                columnNumber: 15\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 13,\n                  color: \"#1E40AF\",\n                  lineHeight: 18\n                },\n                children: `Your photo is processed on-device with real-time face blurring. All faces are automatically blurred before the photo is captured, ensuring complete privacy protection.`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2901,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2829,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2197,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2177,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0,\n            backgroundColor: \"#fff\",\n            borderTopWidth: 1,\n            borderTopColor: \"#E5E7EB\",\n            padding: 20,\n            paddingBottom: insets.bottom + 20\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: submitResponse,\n            disabled: submitting || !cameraResult || !response.trim(),\n            style: {\n              backgroundColor: submitting || !cameraResult || !response.trim() ? \"#9CA3AF\" : \"#10B981\",\n              borderRadius: 12,\n              padding: 16,\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: submitting ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: {\n                  width: 16,\n                  height: 16,\n                  borderRadius: 8,\n                  borderWidth: 2,\n                  borderColor: \"#fff\",\n                  borderTopColor: \"transparent\",\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3073,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\"\n                },\n                children: \"Submitting Response\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Send, {\n                size: 20,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontWeight: \"600\",\n                  color: \"#fff\",\n                  marginLeft: 8\n                },\n                children: `Submit Response ($${question.reward.toFixed(2)})`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3153,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3005,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2953,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2173,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: showCamera,\n        animationType: \"slide\",\n        presentationStyle: \"fullScreen\",\n        children: _Platform.default.OS === 'web' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraWeb.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3241,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_EchoCameraUnified.default, {\n          userId: \"current-user\",\n          requestId: id,\n          onComplete: handleCameraComplete,\n          onCancel: handleCameraCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3269,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 3217,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1753,\n      columnNumber: 5\n    }, this);\n  }\n  _s(RespondScreen, \"gw2Imk4A9BoQq1d4k9rSymf1PEo=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _expoRouter.useLocalSearchParams];\n  });\n  _c = RespondScreen;\n  var _c;\n  $RefreshReg$(_c, \"RespondScreen\");\n});", "lineCount": 1591, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 73], [8, 6, 1, 73, "_View"], [8, 11, 1, 73], [8, 14, 1, 73, "_interopRequireDefault"], [8, 36, 1, 73], [8, 37, 1, 73, "require"], [8, 44, 1, 73], [8, 45, 1, 73, "_dependencyMap"], [8, 59, 1, 73], [9, 2, 1, 73], [9, 6, 1, 73, "_Text"], [9, 11, 1, 73], [9, 14, 1, 73, "_interopRequireDefault"], [9, 36, 1, 73], [9, 37, 1, 73, "require"], [9, 44, 1, 73], [9, 45, 1, 73, "_dependencyMap"], [9, 59, 1, 73], [10, 2, 1, 73], [10, 6, 1, 73, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 17, 1, 73], [10, 20, 1, 73, "_interopRequireDefault"], [10, 42, 1, 73], [10, 43, 1, 73, "require"], [10, 50, 1, 73], [10, 51, 1, 73, "_dependencyMap"], [10, 65, 1, 73], [11, 2, 1, 73], [11, 6, 1, 73, "_TextInput"], [11, 16, 1, 73], [11, 19, 1, 73, "_interopRequireDefault"], [11, 41, 1, 73], [11, 42, 1, 73, "require"], [11, 49, 1, 73], [11, 50, 1, 73, "_dependencyMap"], [11, 64, 1, 73], [12, 2, 1, 73], [12, 6, 1, 73, "_TouchableOpacity"], [12, 23, 1, 73], [12, 26, 1, 73, "_interopRequireDefault"], [12, 48, 1, 73], [12, 49, 1, 73, "require"], [12, 56, 1, 73], [12, 57, 1, 73, "_dependencyMap"], [12, 71, 1, 73], [13, 2, 1, 73], [13, 6, 1, 73, "_<PERSON><PERSON>"], [13, 12, 1, 73], [13, 15, 1, 73, "_interopRequireDefault"], [13, 37, 1, 73], [13, 38, 1, 73, "require"], [13, 45, 1, 73], [13, 46, 1, 73, "_dependencyMap"], [13, 60, 1, 73], [14, 2, 1, 73], [14, 6, 1, 73, "_Modal"], [14, 12, 1, 73], [14, 15, 1, 73, "_interopRequireDefault"], [14, 37, 1, 73], [14, 38, 1, 73, "require"], [14, 45, 1, 73], [14, 46, 1, 73, "_dependencyMap"], [14, 60, 1, 73], [15, 2, 1, 73], [15, 6, 1, 73, "_Platform"], [15, 15, 1, 73], [15, 18, 1, 73, "_interopRequireDefault"], [15, 40, 1, 73], [15, 41, 1, 73, "require"], [15, 48, 1, 73], [15, 49, 1, 73, "_dependencyMap"], [15, 63, 1, 73], [16, 2, 1, 73], [16, 6, 1, 73, "_Image"], [16, 12, 1, 73], [16, 15, 1, 73, "_interopRequireDefault"], [16, 37, 1, 73], [16, 38, 1, 73, "require"], [16, 45, 1, 73], [16, 46, 1, 73, "_dependencyMap"], [16, 60, 1, 73], [17, 2, 1, 73], [17, 6, 1, 73, "_ActivityIndicator"], [17, 24, 1, 73], [17, 27, 1, 73, "_interopRequireDefault"], [17, 49, 1, 73], [17, 50, 1, 73, "require"], [17, 57, 1, 73], [17, 58, 1, 73, "_dependencyMap"], [17, 72, 1, 73], [18, 2, 53, 0], [18, 6, 53, 0, "_reactNativeSafeAreaContext"], [18, 33, 53, 0], [18, 36, 53, 0, "require"], [18, 43, 53, 0], [18, 44, 53, 0, "_dependencyMap"], [18, 58, 53, 0], [19, 2, 57, 0], [19, 6, 57, 0, "_expoStatusBar"], [19, 20, 57, 0], [19, 23, 57, 0, "require"], [19, 30, 57, 0], [19, 31, 57, 0, "_dependencyMap"], [19, 45, 57, 0], [20, 2, 61, 0], [20, 6, 61, 0, "_expoRouter"], [20, 17, 61, 0], [20, 20, 61, 0, "require"], [20, 27, 61, 0], [20, 28, 61, 0, "_dependencyMap"], [20, 42, 61, 0], [21, 2, 65, 0], [21, 6, 65, 0, "_lucideReactNative"], [21, 24, 65, 0], [21, 27, 65, 0, "require"], [21, 34, 65, 0], [21, 35, 65, 0, "_dependencyMap"], [21, 49, 65, 0], [22, 2, 117, 0], [22, 6, 117, 0, "Location"], [22, 14, 117, 0], [22, 17, 117, 0, "_interopRequireWildcard"], [22, 40, 117, 0], [22, 41, 117, 0, "require"], [22, 48, 117, 0], [22, 49, 117, 0, "_dependencyMap"], [22, 63, 117, 0], [23, 2, 121, 0], [23, 6, 121, 0, "_EchoCameraUnified"], [23, 24, 121, 0], [23, 27, 121, 0, "_interopRequireDefault"], [23, 49, 121, 0], [23, 50, 121, 0, "require"], [23, 57, 121, 0], [23, 58, 121, 0, "_dependencyMap"], [23, 72, 121, 0], [24, 2, 125, 0], [24, 6, 125, 0, "_EchoCameraWeb"], [24, 20, 125, 0], [24, 23, 125, 0, "_interopRequireDefault"], [24, 45, 125, 0], [24, 46, 125, 0, "require"], [24, 53, 125, 0], [24, 54, 125, 0, "_dependencyMap"], [24, 68, 125, 0], [25, 2, 129, 0], [25, 6, 129, 0, "_KeyboardAvoidingAnimatedView"], [25, 35, 129, 0], [25, 38, 129, 0, "_interopRequireDefault"], [25, 60, 129, 0], [25, 61, 129, 0, "require"], [25, 68, 129, 0], [25, 69, 129, 0, "_dependencyMap"], [25, 83, 129, 0], [26, 2, 129, 85], [26, 6, 129, 85, "_jsxDevRuntime"], [26, 20, 129, 85], [26, 23, 129, 85, "require"], [26, 30, 129, 85], [26, 31, 129, 85, "_dependencyMap"], [26, 45, 129, 85], [27, 2, 129, 85], [27, 6, 129, 85, "_jsxFileName"], [27, 18, 129, 85], [28, 4, 129, 85, "_s"], [28, 6, 129, 85], [28, 9, 129, 85, "$RefreshSig$"], [28, 21, 129, 85], [29, 2, 129, 85], [29, 11, 129, 85, "_interopRequireWildcard"], [29, 35, 129, 85, "e"], [29, 36, 129, 85], [29, 38, 129, 85, "t"], [29, 39, 129, 85], [29, 68, 129, 85, "WeakMap"], [29, 75, 129, 85], [29, 81, 129, 85, "r"], [29, 82, 129, 85], [29, 89, 129, 85, "WeakMap"], [29, 96, 129, 85], [29, 100, 129, 85, "n"], [29, 101, 129, 85], [29, 108, 129, 85, "WeakMap"], [29, 115, 129, 85], [29, 127, 129, 85, "_interopRequireWildcard"], [29, 150, 129, 85], [29, 162, 129, 85, "_interopRequireWildcard"], [29, 163, 129, 85, "e"], [29, 164, 129, 85], [29, 166, 129, 85, "t"], [29, 167, 129, 85], [29, 176, 129, 85, "t"], [29, 177, 129, 85], [29, 181, 129, 85, "e"], [29, 182, 129, 85], [29, 186, 129, 85, "e"], [29, 187, 129, 85], [29, 188, 129, 85, "__esModule"], [29, 198, 129, 85], [29, 207, 129, 85, "e"], [29, 208, 129, 85], [29, 214, 129, 85, "o"], [29, 215, 129, 85], [29, 217, 129, 85, "i"], [29, 218, 129, 85], [29, 220, 129, 85, "f"], [29, 221, 129, 85], [29, 226, 129, 85, "__proto__"], [29, 235, 129, 85], [29, 243, 129, 85, "default"], [29, 250, 129, 85], [29, 252, 129, 85, "e"], [29, 253, 129, 85], [29, 270, 129, 85, "e"], [29, 271, 129, 85], [29, 294, 129, 85, "e"], [29, 295, 129, 85], [29, 320, 129, 85, "e"], [29, 321, 129, 85], [29, 330, 129, 85, "f"], [29, 331, 129, 85], [29, 337, 129, 85, "o"], [29, 338, 129, 85], [29, 341, 129, 85, "t"], [29, 342, 129, 85], [29, 345, 129, 85, "n"], [29, 346, 129, 85], [29, 349, 129, 85, "r"], [29, 350, 129, 85], [29, 358, 129, 85, "o"], [29, 359, 129, 85], [29, 360, 129, 85, "has"], [29, 363, 129, 85], [29, 364, 129, 85, "e"], [29, 365, 129, 85], [29, 375, 129, 85, "o"], [29, 376, 129, 85], [29, 377, 129, 85, "get"], [29, 380, 129, 85], [29, 381, 129, 85, "e"], [29, 382, 129, 85], [29, 385, 129, 85, "o"], [29, 386, 129, 85], [29, 387, 129, 85, "set"], [29, 390, 129, 85], [29, 391, 129, 85, "e"], [29, 392, 129, 85], [29, 394, 129, 85, "f"], [29, 395, 129, 85], [29, 411, 129, 85, "t"], [29, 412, 129, 85], [29, 416, 129, 85, "e"], [29, 417, 129, 85], [29, 433, 129, 85, "t"], [29, 434, 129, 85], [29, 441, 129, 85, "hasOwnProperty"], [29, 455, 129, 85], [29, 456, 129, 85, "call"], [29, 460, 129, 85], [29, 461, 129, 85, "e"], [29, 462, 129, 85], [29, 464, 129, 85, "t"], [29, 465, 129, 85], [29, 472, 129, 85, "i"], [29, 473, 129, 85], [29, 477, 129, 85, "o"], [29, 478, 129, 85], [29, 481, 129, 85, "Object"], [29, 487, 129, 85], [29, 488, 129, 85, "defineProperty"], [29, 502, 129, 85], [29, 507, 129, 85, "Object"], [29, 513, 129, 85], [29, 514, 129, 85, "getOwnPropertyDescriptor"], [29, 538, 129, 85], [29, 539, 129, 85, "e"], [29, 540, 129, 85], [29, 542, 129, 85, "t"], [29, 543, 129, 85], [29, 550, 129, 85, "i"], [29, 551, 129, 85], [29, 552, 129, 85, "get"], [29, 555, 129, 85], [29, 559, 129, 85, "i"], [29, 560, 129, 85], [29, 561, 129, 85, "set"], [29, 564, 129, 85], [29, 568, 129, 85, "o"], [29, 569, 129, 85], [29, 570, 129, 85, "f"], [29, 571, 129, 85], [29, 573, 129, 85, "t"], [29, 574, 129, 85], [29, 576, 129, 85, "i"], [29, 577, 129, 85], [29, 581, 129, 85, "f"], [29, 582, 129, 85], [29, 583, 129, 85, "t"], [29, 584, 129, 85], [29, 588, 129, 85, "e"], [29, 589, 129, 85], [29, 590, 129, 85, "t"], [29, 591, 129, 85], [29, 602, 129, 85, "f"], [29, 603, 129, 85], [29, 608, 129, 85, "e"], [29, 609, 129, 85], [29, 611, 129, 85, "t"], [29, 612, 129, 85], [30, 2, 133, 15], [30, 11, 133, 24, "RespondScreen"], [30, 24, 133, 37, "RespondScreen"], [30, 25, 133, 37], [30, 27, 133, 40], [31, 4, 133, 40, "_s"], [31, 6, 133, 40], [32, 4, 137, 2], [32, 10, 137, 8, "insets"], [32, 16, 137, 14], [32, 19, 137, 17], [32, 23, 137, 17, "useSafeAreaInsets"], [32, 68, 137, 34], [32, 70, 137, 35], [32, 71, 137, 36], [33, 4, 141, 2], [33, 10, 141, 8], [34, 6, 141, 10, "id"], [35, 4, 141, 13], [35, 5, 141, 14], [35, 8, 141, 17], [35, 12, 141, 17, "useLocalSearchParams"], [35, 44, 141, 37], [35, 46, 141, 38], [35, 47, 141, 39], [36, 4, 145, 2], [36, 10, 145, 8], [36, 11, 145, 9, "response"], [36, 19, 145, 17], [36, 21, 145, 19, "setResponse"], [36, 32, 145, 30], [36, 33, 145, 31], [36, 36, 145, 34], [36, 40, 145, 34, "useState"], [36, 55, 145, 42], [36, 57, 145, 43], [36, 59, 145, 45], [36, 60, 145, 46], [37, 4, 149, 2], [37, 10, 149, 8], [37, 11, 149, 9, "showCamera"], [37, 21, 149, 19], [37, 23, 149, 21, "setShowCamera"], [37, 36, 149, 34], [37, 37, 149, 35], [37, 40, 149, 38], [37, 44, 149, 38, "useState"], [37, 59, 149, 46], [37, 61, 149, 47], [37, 66, 149, 52], [37, 67, 149, 53], [38, 4, 153, 2], [38, 10, 153, 8], [38, 11, 153, 9, "cameraResult"], [38, 23, 153, 21], [38, 25, 153, 23, "setCameraResult"], [38, 40, 153, 38], [38, 41, 153, 39], [38, 44, 153, 42], [38, 48, 153, 42, "useState"], [38, 63, 153, 50], [38, 65, 153, 51], [38, 69, 153, 55], [38, 70, 153, 56], [39, 4, 157, 2], [39, 10, 157, 8], [39, 11, 157, 9, "submitting"], [39, 21, 157, 19], [39, 23, 157, 21, "setSubmitting"], [39, 36, 157, 34], [39, 37, 157, 35], [39, 40, 157, 38], [39, 44, 157, 38, "useState"], [39, 59, 157, 46], [39, 61, 157, 47], [39, 66, 157, 52], [39, 67, 157, 53], [40, 4, 161, 2], [40, 10, 161, 8], [40, 11, 161, 9, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [40, 27, 161, 25], [40, 29, 161, 27, "setCapturedPhotoUri"], [40, 48, 161, 46], [40, 49, 161, 47], [40, 52, 161, 50], [40, 56, 161, 50, "useState"], [40, 71, 161, 58], [40, 73, 161, 59], [40, 77, 161, 63], [40, 78, 161, 64], [41, 4, 165, 2], [41, 10, 165, 8, "defaultTestingMode"], [41, 28, 165, 26], [41, 31, 165, 29], [41, 35, 165, 29, "useMemo"], [41, 49, 165, 36], [41, 51, 165, 37], [41, 57, 165, 43], [42, 6, 169, 4], [42, 10, 169, 8, "Platform"], [42, 27, 169, 16], [42, 28, 169, 17, "OS"], [42, 30, 169, 19], [42, 35, 169, 24], [42, 40, 169, 29], [42, 44, 169, 33], [42, 51, 169, 40, "window"], [42, 57, 169, 46], [42, 62, 169, 51], [42, 73, 169, 62], [42, 75, 169, 64], [43, 8, 173, 6], [43, 15, 173, 13], [43, 20, 173, 18], [44, 6, 177, 4], [45, 6, 181, 4], [45, 12, 181, 10], [46, 8, 181, 12, "protocol"], [46, 16, 181, 20], [47, 8, 181, 22, "hostname"], [48, 6, 181, 31], [48, 7, 181, 32], [48, 10, 181, 35, "window"], [48, 16, 181, 41], [48, 17, 181, 42, "location"], [48, 25, 181, 50], [49, 6, 185, 4], [49, 12, 185, 10, "localHosts"], [49, 22, 185, 20], [49, 25, 185, 23], [49, 26, 185, 24], [49, 37, 185, 35], [49, 39, 185, 37], [49, 50, 185, 48], [49, 52, 185, 50], [49, 57, 185, 55], [49, 58, 185, 56], [50, 6, 189, 4], [50, 13, 189, 11, "protocol"], [50, 21, 189, 19], [50, 26, 189, 24], [50, 34, 189, 32], [50, 38, 189, 36, "localHosts"], [50, 48, 189, 46], [50, 49, 189, 47, "includes"], [50, 57, 189, 55], [50, 58, 189, 56, "hostname"], [50, 66, 189, 64], [50, 67, 189, 65], [51, 4, 193, 2], [51, 5, 193, 3], [51, 7, 193, 5], [51, 9, 193, 7], [51, 10, 193, 8], [52, 4, 197, 2], [52, 10, 197, 8], [52, 11, 197, 9, "testingMode"], [52, 22, 197, 20], [52, 24, 197, 22, "setTestingMode"], [52, 38, 197, 36], [52, 39, 197, 37], [52, 42, 197, 40], [52, 46, 197, 40, "useState"], [52, 61, 197, 48], [52, 63, 197, 49, "defaultTestingMode"], [52, 81, 197, 67], [52, 82, 197, 68], [54, 4, 201, 2], [56, 4, 205, 2], [56, 10, 205, 8], [56, 11, 205, 9, "locationStatus"], [56, 25, 205, 23], [56, 27, 205, 25, "setLocationStatus"], [56, 44, 205, 42], [56, 45, 205, 43], [56, 48, 205, 46], [56, 52, 205, 46, "useState"], [56, 67, 205, 54], [56, 69, 205, 55], [56, 79, 205, 65], [56, 80, 205, 66], [56, 81, 205, 67], [56, 82, 205, 68], [58, 4, 209, 2], [58, 10, 209, 8], [58, 11, 209, 9, "currentLocation"], [58, 26, 209, 24], [58, 28, 209, 26, "setCurrentLocation"], [58, 46, 209, 44], [58, 47, 209, 45], [58, 50, 209, 48], [58, 54, 209, 48, "useState"], [58, 69, 209, 56], [58, 71, 209, 57], [58, 75, 209, 61], [58, 76, 209, 62], [59, 4, 213, 2], [59, 10, 213, 8], [59, 11, 213, 9, "distance"], [59, 19, 213, 17], [59, 21, 213, 19, "setDistance"], [59, 32, 213, 30], [59, 33, 213, 31], [59, 36, 213, 34], [59, 40, 213, 34, "useState"], [59, 55, 213, 42], [59, 57, 213, 43], [59, 61, 213, 47], [59, 62, 213, 48], [60, 4, 217, 2], [60, 10, 217, 8], [60, 11, 217, 9, "gettingLocation"], [60, 26, 217, 24], [60, 28, 217, 26, "setGettingLocation"], [60, 46, 217, 44], [60, 47, 217, 45], [60, 50, 217, 48], [60, 54, 217, 48, "useState"], [60, 69, 217, 56], [60, 71, 217, 57], [60, 76, 217, 62], [60, 77, 217, 63], [61, 4, 221, 2], [61, 10, 221, 8], [61, 11, 221, 9, "locationError"], [61, 24, 221, 22], [61, 26, 221, 24, "setLocationError"], [61, 42, 221, 40], [61, 43, 221, 41], [61, 46, 221, 44], [61, 50, 221, 44, "useState"], [61, 65, 221, 52], [61, 67, 221, 53], [61, 71, 221, 57], [61, 72, 221, 58], [63, 4, 225, 2], [65, 4, 229, 2], [65, 10, 229, 8, "question"], [65, 18, 229, 16], [65, 21, 229, 19], [66, 6, 233, 4, "id"], [66, 8, 233, 6], [66, 10, 233, 8, "id"], [66, 12, 233, 10], [67, 6, 237, 4, "question"], [67, 14, 237, 12], [67, 16, 241, 6], [67, 114, 241, 104], [68, 6, 245, 4, "location"], [68, 14, 245, 12], [68, 16, 245, 14], [68, 43, 245, 41], [69, 6, 249, 4, "coordinates"], [69, 17, 249, 15], [69, 19, 249, 17], [70, 8, 253, 6], [72, 8, 257, 6, "latitude"], [72, 16, 257, 14], [72, 18, 257, 16], [72, 25, 257, 23], [73, 8, 257, 25], [75, 8, 261, 6, "longitude"], [75, 17, 261, 15], [75, 19, 261, 17], [75, 20, 261, 18], [76, 6, 265, 4], [76, 7, 265, 5], [77, 6, 269, 4, "reward"], [77, 12, 269, 10], [77, 14, 269, 12], [77, 17, 269, 15], [78, 6, 273, 4, "postedAt"], [78, 14, 273, 12], [78, 16, 273, 14], [78, 29, 273, 27], [79, 6, 277, 4, "userId"], [79, 12, 277, 10], [79, 14, 277, 12], [80, 4, 281, 0], [80, 5, 281, 1], [81, 4, 285, 2], [81, 10, 285, 8, "questionLatitude"], [81, 26, 285, 24], [81, 29, 285, 27, "question"], [81, 37, 285, 35], [81, 38, 285, 36, "coordinates"], [81, 49, 285, 47], [81, 50, 285, 48, "latitude"], [81, 58, 285, 56], [82, 4, 289, 2], [82, 10, 289, 8, "questionLongitude"], [82, 27, 289, 25], [82, 30, 289, 28, "question"], [82, 38, 289, 36], [82, 39, 289, 37, "coordinates"], [82, 50, 289, 48], [82, 51, 289, 49, "longitude"], [82, 60, 289, 58], [84, 4, 293, 2], [86, 4, 297, 2], [86, 10, 297, 8, "calculateDistance"], [86, 27, 297, 25], [86, 30, 297, 28, "calculateDistance"], [86, 31, 297, 29, "lat1"], [86, 35, 297, 33], [86, 37, 297, 35, "lon1"], [86, 41, 297, 39], [86, 43, 297, 41, "lat2"], [86, 47, 297, 45], [86, 49, 297, 47, "lon2"], [86, 53, 297, 51], [86, 58, 297, 56], [87, 6, 301, 4], [87, 12, 301, 10, "R"], [87, 13, 301, 11], [87, 16, 301, 14], [87, 22, 301, 20], [87, 23, 301, 21], [87, 24, 301, 22], [89, 6, 305, 4], [89, 12, 305, 10, "lat1Rad"], [89, 19, 305, 17], [89, 22, 305, 21, "lat1"], [89, 26, 305, 25], [89, 29, 305, 28, "Math"], [89, 33, 305, 32], [89, 34, 305, 33, "PI"], [89, 36, 305, 35], [89, 39, 305, 39], [89, 42, 305, 42], [90, 6, 309, 4], [90, 12, 309, 10, "lat2Rad"], [90, 19, 309, 17], [90, 22, 309, 21, "lat2"], [90, 26, 309, 25], [90, 29, 309, 28, "Math"], [90, 33, 309, 32], [90, 34, 309, 33, "PI"], [90, 36, 309, 35], [90, 39, 309, 39], [90, 42, 309, 42], [91, 6, 313, 4], [91, 12, 313, 10, "deltaLat"], [91, 20, 313, 18], [91, 23, 313, 22], [91, 24, 313, 23, "lat2"], [91, 28, 313, 27], [91, 31, 313, 30, "lat1"], [91, 35, 313, 34], [91, 39, 313, 38, "Math"], [91, 43, 313, 42], [91, 44, 313, 43, "PI"], [91, 46, 313, 45], [91, 49, 313, 49], [91, 52, 313, 52], [92, 6, 317, 4], [92, 12, 317, 10, "deltaLon"], [92, 20, 317, 18], [92, 23, 317, 22], [92, 24, 317, 23, "lon2"], [92, 28, 317, 27], [92, 31, 317, 30, "lon1"], [92, 35, 317, 34], [92, 39, 317, 38, "Math"], [92, 43, 317, 42], [92, 44, 317, 43, "PI"], [92, 46, 317, 45], [92, 49, 317, 49], [92, 52, 317, 52], [93, 6, 321, 4], [93, 12, 321, 10, "a"], [93, 13, 321, 11], [93, 16, 325, 6, "Math"], [93, 20, 325, 10], [93, 21, 325, 11, "sin"], [93, 24, 325, 14], [93, 25, 325, 15, "deltaLat"], [93, 33, 325, 23], [93, 36, 325, 26], [93, 37, 325, 27], [93, 38, 325, 28], [93, 41, 325, 31, "Math"], [93, 45, 325, 35], [93, 46, 325, 36, "sin"], [93, 49, 325, 39], [93, 50, 325, 40, "deltaLat"], [93, 58, 325, 48], [93, 61, 325, 51], [93, 62, 325, 52], [93, 63, 325, 53], [93, 66, 329, 6, "Math"], [93, 70, 329, 10], [93, 71, 329, 11, "cos"], [93, 74, 329, 14], [93, 75, 329, 15, "lat1Rad"], [93, 82, 329, 22], [93, 83, 329, 23], [93, 86, 329, 26, "Math"], [93, 90, 329, 30], [93, 91, 329, 31, "cos"], [93, 94, 329, 34], [93, 95, 329, 35, "lat2Rad"], [93, 102, 329, 42], [93, 103, 329, 43], [93, 106, 333, 8, "Math"], [93, 110, 333, 12], [93, 111, 333, 13, "sin"], [93, 114, 333, 16], [93, 115, 333, 17, "deltaLon"], [93, 123, 333, 25], [93, 126, 333, 28], [93, 127, 333, 29], [93, 128, 333, 30], [93, 131, 333, 33, "Math"], [93, 135, 333, 37], [93, 136, 333, 38, "sin"], [93, 139, 333, 41], [93, 140, 333, 42, "deltaLon"], [93, 148, 333, 50], [93, 151, 333, 53], [93, 152, 333, 54], [93, 153, 333, 55], [94, 6, 337, 4], [94, 12, 337, 10, "c"], [94, 13, 337, 11], [94, 16, 337, 14], [94, 17, 337, 15], [94, 20, 337, 18, "Math"], [94, 24, 337, 22], [94, 25, 337, 23, "atan2"], [94, 30, 337, 28], [94, 31, 337, 29, "Math"], [94, 35, 337, 33], [94, 36, 337, 34, "sqrt"], [94, 40, 337, 38], [94, 41, 337, 39, "a"], [94, 42, 337, 40], [94, 43, 337, 41], [94, 45, 337, 43, "Math"], [94, 49, 337, 47], [94, 50, 337, 48, "sqrt"], [94, 54, 337, 52], [94, 55, 337, 53], [94, 56, 337, 54], [94, 59, 337, 57, "a"], [94, 60, 337, 58], [94, 61, 337, 59], [94, 62, 337, 60], [95, 6, 341, 4], [95, 13, 341, 11, "R"], [95, 14, 341, 12], [95, 17, 341, 15, "c"], [95, 18, 341, 16], [95, 19, 341, 17], [95, 20, 341, 18], [96, 4, 345, 2], [96, 5, 345, 3], [98, 4, 349, 2], [100, 4, 353, 2], [100, 10, 353, 8, "verifyLocation"], [100, 24, 353, 22], [100, 27, 353, 25], [100, 31, 353, 25, "useCallback"], [100, 49, 353, 36], [100, 51, 353, 37], [100, 63, 353, 49], [101, 6, 357, 4], [101, 10, 357, 8, "testingMode"], [101, 21, 357, 19], [101, 23, 357, 21], [102, 8, 361, 6, "setLocationStatus"], [102, 25, 361, 23], [102, 26, 361, 24], [102, 36, 361, 34], [102, 37, 361, 35], [103, 8, 365, 6, "setLocationError"], [103, 24, 365, 22], [103, 25, 365, 23], [103, 29, 365, 27], [103, 30, 365, 28], [104, 8, 369, 6, "setDistance"], [104, 19, 369, 17], [104, 20, 369, 18], [104, 21, 369, 19], [104, 22, 369, 20], [105, 8, 373, 6, "setCurrentLocation"], [105, 26, 373, 24], [105, 27, 373, 25], [105, 31, 373, 29], [105, 32, 373, 30], [106, 8, 377, 6, "setGettingLocation"], [106, 26, 377, 24], [106, 27, 377, 25], [106, 32, 377, 30], [106, 33, 377, 31], [107, 8, 381, 6], [108, 6, 385, 4], [109, 6, 389, 4], [109, 10, 389, 8], [110, 8, 393, 6, "setGettingLocation"], [110, 26, 393, 24], [110, 27, 393, 25], [110, 31, 393, 29], [110, 32, 393, 30], [111, 8, 397, 6, "setLocationError"], [111, 24, 397, 22], [111, 25, 397, 23], [111, 29, 397, 27], [111, 30, 397, 28], [112, 8, 401, 6, "setLocationStatus"], [112, 25, 401, 23], [112, 26, 401, 24], [112, 36, 401, 34], [112, 37, 401, 35], [114, 8, 405, 6], [116, 8, 409, 6], [116, 14, 409, 12], [117, 10, 409, 14, "status"], [118, 8, 409, 21], [118, 9, 409, 22], [118, 12, 409, 25], [118, 18, 409, 31, "Location"], [118, 26, 409, 39], [118, 27, 409, 40, "requestForegroundPermissionsAsync"], [118, 60, 409, 73], [118, 61, 409, 74], [118, 62, 409, 75], [119, 8, 413, 6], [119, 12, 413, 10, "status"], [119, 18, 413, 16], [119, 23, 413, 21], [119, 32, 413, 30], [119, 34, 413, 32], [120, 10, 417, 8], [120, 16, 417, 14, "message"], [120, 23, 417, 21], [120, 26, 421, 10, "Platform"], [120, 43, 421, 18], [120, 44, 421, 19, "OS"], [120, 46, 421, 21], [120, 51, 421, 26], [120, 56, 421, 31], [120, 59, 425, 14], [120, 164, 425, 119], [120, 167, 429, 14], [120, 233, 429, 80], [121, 10, 433, 8, "setLocationError"], [121, 26, 433, 24], [121, 27, 433, 25, "message"], [121, 34, 433, 32], [121, 35, 433, 33], [122, 10, 437, 8, "setLocationStatus"], [122, 27, 437, 25], [122, 28, 437, 26], [122, 35, 437, 33], [122, 36, 437, 34], [123, 10, 441, 8, "<PERSON><PERSON>"], [123, 24, 441, 13], [123, 25, 441, 14, "alert"], [123, 30, 441, 19], [123, 31, 441, 20], [123, 50, 441, 39], [123, 52, 441, 41, "message"], [123, 59, 441, 48], [123, 60, 441, 49], [124, 10, 445, 8], [125, 8, 449, 6], [127, 8, 453, 6], [129, 8, 457, 6], [129, 14, 457, 12, "locationData"], [129, 26, 457, 24], [129, 29, 457, 27], [129, 35, 457, 33, "Location"], [129, 43, 457, 41], [129, 44, 457, 42, "getCurrentPositionAsync"], [129, 67, 457, 65], [129, 68, 457, 66], [130, 10, 461, 8, "accuracy"], [130, 18, 461, 16], [130, 20, 461, 18, "Location"], [130, 28, 461, 26], [130, 29, 461, 27, "Accuracy"], [130, 37, 461, 35], [130, 38, 461, 36, "High"], [130, 42, 461, 40], [131, 10, 465, 8, "timeout"], [131, 17, 465, 15], [131, 19, 465, 17], [131, 24, 465, 22], [132, 10, 469, 8, "maximumAge"], [132, 20, 469, 18], [132, 22, 469, 20], [133, 8, 473, 6], [133, 9, 473, 7], [133, 10, 473, 8], [134, 8, 477, 6], [134, 14, 477, 12, "userLat"], [134, 21, 477, 19], [134, 24, 477, 22, "locationData"], [134, 36, 477, 34], [134, 37, 477, 35, "coords"], [134, 43, 477, 41], [134, 44, 477, 42, "latitude"], [134, 52, 477, 50], [135, 8, 481, 6], [135, 14, 481, 12, "userLon"], [135, 21, 481, 19], [135, 24, 481, 22, "locationData"], [135, 36, 481, 34], [135, 37, 481, 35, "coords"], [135, 43, 481, 41], [135, 44, 481, 42, "longitude"], [135, 53, 481, 51], [136, 8, 485, 6], [136, 14, 485, 12, "questionLat"], [136, 25, 485, 23], [136, 28, 485, 26, "questionLatitude"], [136, 44, 485, 42], [137, 8, 489, 6], [137, 14, 489, 12, "questionLon"], [137, 25, 489, 23], [137, 28, 489, 26, "questionLongitude"], [137, 45, 489, 43], [139, 8, 493, 6], [141, 8, 497, 6], [141, 14, 497, 12, "distanceInMeters"], [141, 30, 497, 28], [141, 33, 497, 31, "calculateDistance"], [141, 50, 497, 48], [141, 51, 501, 8, "userLat"], [141, 58, 501, 15], [141, 60, 505, 8, "userLon"], [141, 67, 505, 15], [141, 69, 509, 8, "questionLat"], [141, 80, 509, 19], [141, 82, 513, 8, "questionLon"], [141, 93, 517, 6], [141, 94, 517, 7], [142, 8, 521, 6, "setDistance"], [142, 19, 521, 17], [142, 20, 521, 18, "Math"], [142, 24, 521, 22], [142, 25, 521, 23, "round"], [142, 30, 521, 28], [142, 31, 521, 29, "distanceInMeters"], [142, 47, 521, 45], [142, 48, 521, 46], [142, 49, 521, 47], [143, 8, 525, 6, "setCurrentLocation"], [143, 26, 525, 24], [143, 27, 525, 25], [144, 10, 529, 8, "latitude"], [144, 18, 529, 16], [144, 20, 529, 18, "userLat"], [144, 27, 529, 25], [145, 10, 533, 8, "longitude"], [145, 19, 533, 17], [145, 21, 533, 19, "userLon"], [146, 8, 537, 6], [146, 9, 537, 7], [146, 10, 537, 8], [148, 8, 541, 6], [150, 8, 545, 6], [150, 14, 545, 12, "maxDistance"], [150, 25, 545, 23], [150, 28, 545, 26], [150, 31, 545, 29], [151, 8, 549, 6], [151, 12, 549, 10, "distanceInMeters"], [151, 28, 549, 26], [151, 32, 549, 30, "maxDistance"], [151, 43, 549, 41], [151, 45, 549, 43], [152, 10, 553, 8, "setLocationStatus"], [152, 27, 553, 25], [152, 28, 553, 26], [152, 38, 553, 36], [152, 39, 553, 37], [153, 8, 557, 6], [153, 9, 557, 7], [153, 15, 557, 13], [154, 10, 561, 8, "setLocationStatus"], [154, 27, 561, 25], [154, 28, 561, 26], [154, 37, 561, 35], [154, 38, 561, 36], [155, 8, 565, 6], [156, 6, 569, 4], [156, 7, 569, 5], [156, 8, 569, 6], [156, 15, 569, 13, "error"], [156, 20, 569, 18], [156, 22, 569, 20], [157, 8, 573, 6, "console"], [157, 15, 573, 13], [157, 16, 573, 14, "error"], [157, 21, 573, 19], [157, 22, 573, 20], [157, 49, 573, 47], [157, 51, 573, 49, "error"], [157, 56, 573, 54], [157, 57, 573, 55], [158, 8, 577, 6], [158, 12, 577, 10, "message"], [158, 19, 577, 17], [158, 22, 577, 20], [158, 92, 577, 90], [159, 8, 581, 6], [159, 12, 581, 10, "error"], [159, 17, 581, 15], [159, 19, 581, 17, "code"], [159, 23, 581, 21], [159, 28, 581, 26], [159, 29, 581, 27], [159, 31, 581, 29], [160, 10, 585, 8, "message"], [160, 17, 585, 15], [160, 20, 585, 18], [160, 128, 585, 126], [161, 8, 589, 6], [161, 9, 589, 7], [161, 15, 589, 13], [161, 19, 589, 17, "error"], [161, 24, 589, 22], [161, 26, 589, 24, "code"], [161, 30, 589, 28], [161, 35, 589, 33], [161, 36, 589, 34], [161, 38, 589, 36], [162, 10, 593, 8, "message"], [162, 17, 593, 15], [162, 20, 593, 18], [162, 112, 593, 110], [163, 8, 597, 6], [163, 9, 597, 7], [163, 15, 597, 13], [163, 19, 597, 17, "error"], [163, 24, 597, 22], [163, 26, 597, 24, "code"], [163, 30, 597, 28], [163, 35, 597, 33], [163, 36, 597, 34], [163, 38, 597, 36], [164, 10, 601, 8, "message"], [164, 17, 601, 15], [164, 20, 601, 18], [164, 67, 601, 65], [165, 8, 605, 6], [165, 9, 605, 7], [165, 15, 605, 13], [165, 19, 605, 17, "Platform"], [165, 36, 605, 25], [165, 37, 605, 26, "OS"], [165, 39, 605, 28], [165, 44, 605, 33], [165, 49, 605, 38], [165, 53, 605, 42], [165, 60, 605, 49, "error"], [165, 65, 605, 54], [165, 67, 605, 56, "message"], [165, 74, 605, 63], [165, 79, 605, 68], [165, 87, 605, 76], [165, 91, 605, 80, "error"], [165, 96, 605, 85], [165, 97, 605, 86, "message"], [165, 104, 605, 93], [165, 105, 605, 94, "toLowerCase"], [165, 116, 605, 105], [165, 117, 605, 106], [165, 118, 605, 107], [165, 119, 605, 108, "includes"], [165, 127, 605, 116], [165, 128, 605, 117], [165, 136, 605, 125], [165, 137, 605, 126], [165, 139, 605, 128], [166, 10, 609, 8, "message"], [166, 17, 609, 15], [166, 20, 609, 18], [166, 135, 609, 133], [167, 8, 613, 6], [168, 8, 617, 6, "setLocationError"], [168, 24, 617, 22], [168, 25, 617, 23, "message"], [168, 32, 617, 30], [168, 33, 617, 31], [169, 8, 621, 6, "setLocationStatus"], [169, 25, 621, 23], [169, 26, 621, 24], [169, 33, 621, 31], [169, 34, 621, 32], [170, 8, 625, 6, "<PERSON><PERSON>"], [170, 22, 625, 11], [170, 23, 625, 12, "alert"], [170, 28, 625, 17], [170, 29, 625, 18], [170, 45, 625, 34], [170, 47, 625, 36, "message"], [170, 54, 625, 43], [170, 55, 625, 44], [171, 6, 629, 4], [171, 7, 629, 5], [171, 16, 629, 14], [172, 8, 633, 6, "setGettingLocation"], [172, 26, 633, 24], [172, 27, 633, 25], [172, 32, 633, 30], [172, 33, 633, 31], [173, 6, 637, 4], [174, 4, 641, 2], [174, 5, 641, 3], [174, 7, 641, 5], [174, 8, 641, 6, "questionLatitude"], [174, 24, 641, 22], [174, 26, 641, 24, "questionLongitude"], [174, 43, 641, 41], [174, 45, 641, 43, "testingMode"], [174, 56, 641, 54], [174, 57, 641, 55], [174, 58, 641, 56], [176, 4, 645, 2], [178, 4, 649, 2], [178, 8, 649, 2, "useEffect"], [178, 24, 649, 11], [178, 26, 649, 12], [178, 32, 649, 18], [179, 6, 653, 4], [179, 10, 653, 8, "testingMode"], [179, 21, 653, 19], [179, 23, 653, 21], [180, 8, 657, 6, "setLocationStatus"], [180, 25, 657, 23], [180, 26, 657, 24], [180, 36, 657, 34], [180, 37, 657, 35], [181, 8, 661, 6, "setLocationError"], [181, 24, 661, 22], [181, 25, 661, 23], [181, 29, 661, 27], [181, 30, 661, 28], [182, 8, 665, 6, "setDistance"], [182, 19, 665, 17], [182, 20, 665, 18], [182, 21, 665, 19], [182, 22, 665, 20], [183, 8, 669, 6, "setCurrentLocation"], [183, 26, 669, 24], [183, 27, 669, 25], [183, 31, 669, 29], [183, 32, 669, 30], [184, 8, 673, 6, "setGettingLocation"], [184, 26, 673, 24], [184, 27, 673, 25], [184, 32, 673, 30], [184, 33, 673, 31], [185, 8, 677, 6], [186, 6, 681, 4], [187, 6, 685, 4, "verifyLocation"], [187, 20, 685, 18], [187, 21, 685, 19], [187, 22, 685, 20], [188, 4, 689, 2], [188, 5, 689, 3], [188, 7, 689, 5], [188, 8, 689, 6, "testingMode"], [188, 19, 689, 17], [188, 21, 689, 19, "verifyLocation"], [188, 35, 689, 33], [188, 36, 689, 34], [188, 37, 689, 35], [189, 4, 693, 2], [189, 10, 693, 8, "handleStartCamera"], [189, 27, 693, 25], [189, 30, 693, 28, "handleStartCamera"], [189, 31, 693, 28], [189, 36, 693, 34], [190, 6, 697, 4, "console"], [190, 13, 697, 11], [190, 14, 697, 12, "log"], [190, 17, 697, 15], [190, 18, 697, 16], [190, 42, 697, 40], [190, 44, 697, 42], [191, 8, 701, 6, "locationStatus"], [191, 22, 701, 20], [192, 8, 705, 6, "testingMode"], [192, 19, 705, 17], [193, 8, 709, 6, "disabled"], [193, 16, 709, 14], [193, 18, 709, 16, "locationStatus"], [193, 32, 709, 30], [193, 37, 709, 35], [193, 47, 709, 45], [193, 51, 709, 49], [193, 52, 709, 50, "testingMode"], [193, 63, 709, 61], [194, 8, 713, 6, "shouldEnable"], [194, 20, 713, 18], [194, 22, 713, 20, "locationStatus"], [194, 36, 713, 34], [194, 41, 713, 39], [194, 51, 713, 49], [194, 55, 713, 53, "testingMode"], [194, 66, 713, 64], [195, 8, 717, 6, "existingCameraResult"], [195, 28, 717, 26], [195, 30, 717, 28, "cameraResult"], [195, 42, 717, 40], [195, 43, 717, 42], [196, 6, 721, 4], [196, 7, 721, 5], [196, 8, 721, 6], [197, 6, 725, 4], [197, 10, 725, 8, "locationStatus"], [197, 24, 725, 22], [197, 29, 725, 27], [197, 39, 725, 37], [197, 43, 725, 41], [197, 44, 725, 42, "testingMode"], [197, 55, 725, 53], [197, 57, 725, 55], [198, 8, 729, 6, "<PERSON><PERSON>"], [198, 22, 729, 11], [198, 23, 729, 12, "alert"], [198, 28, 729, 17], [198, 29, 733, 8], [198, 48, 733, 27], [198, 50, 737, 8, "locationStatus"], [198, 64, 737, 22], [198, 69, 737, 27], [198, 78, 737, 36], [198, 81, 741, 12], [198, 92, 741, 23, "distance"], [198, 100, 741, 31], [198, 104, 741, 35], [198, 105, 741, 36], [198, 180, 741, 111], [198, 183, 745, 12], [198, 219, 749, 6], [198, 220, 749, 7], [199, 8, 753, 6], [200, 6, 757, 4], [201, 6, 761, 4, "setShowCamera"], [201, 19, 761, 17], [201, 20, 761, 18], [201, 24, 761, 22], [201, 25, 761, 23], [202, 4, 765, 2], [202, 5, 765, 3], [203, 4, 769, 2], [203, 10, 769, 8, "handleCameraComplete"], [203, 30, 769, 28], [203, 33, 769, 31], [203, 37, 769, 31, "useCallback"], [203, 55, 769, 42], [203, 57, 769, 44, "result"], [203, 63, 769, 50], [203, 67, 769, 55], [204, 6, 773, 4, "console"], [204, 13, 773, 11], [204, 14, 773, 12, "log"], [204, 17, 773, 15], [204, 18, 773, 16], [204, 43, 773, 41], [204, 45, 773, 43, "result"], [204, 51, 773, 49], [204, 52, 773, 50], [204, 53, 773, 51], [204, 54, 773, 52], [205, 6, 774, 4, "console"], [205, 13, 774, 11], [205, 14, 774, 12, "log"], [205, 17, 774, 15], [205, 18, 774, 16], [205, 59, 774, 57], [205, 61, 774, 59], [206, 8, 775, 6, "imageUrl"], [206, 16, 775, 14], [206, 18, 775, 16, "result"], [206, 24, 775, 22], [206, 25, 775, 23, "imageUrl"], [206, 33, 775, 31], [207, 8, 776, 6, "localUri"], [207, 16, 776, 14], [207, 18, 776, 16, "result"], [207, 24, 776, 22], [207, 25, 776, 23, "localUri"], [207, 33, 776, 31], [208, 8, 777, 6, "uri"], [208, 11, 777, 9], [208, 13, 777, 11, "result"], [208, 19, 777, 17], [208, 20, 777, 18, "uri"], [208, 23, 777, 21], [209, 8, 778, 6, "publicUrl"], [209, 17, 778, 15], [209, 19, 778, 17, "result"], [209, 25, 778, 23], [209, 26, 778, 24, "publicUrl"], [209, 35, 778, 33], [210, 8, 779, 6, "timestamp"], [210, 17, 779, 15], [210, 19, 779, 17, "result"], [210, 25, 779, 23], [210, 26, 779, 24, "timestamp"], [211, 6, 780, 4], [211, 7, 780, 5], [211, 8, 780, 6], [213, 6, 784, 4], [215, 6, 788, 4], [215, 10, 788, 8, "imageUri"], [215, 18, 788, 16], [215, 21, 788, 19, "result"], [215, 27, 788, 25], [215, 28, 788, 26, "imageUrl"], [215, 36, 788, 34], [215, 40, 788, 38, "result"], [215, 46, 788, 44], [215, 47, 788, 45, "localUri"], [215, 55, 788, 53], [215, 59, 788, 57, "result"], [215, 65, 788, 63], [215, 66, 788, 64, "uri"], [215, 69, 788, 67], [215, 73, 788, 71, "result"], [215, 79, 788, 77], [215, 80, 788, 78, "publicUrl"], [215, 89, 788, 87], [216, 6, 789, 4, "console"], [216, 13, 789, 11], [216, 14, 789, 12, "log"], [216, 17, 789, 15], [216, 18, 789, 16], [216, 52, 789, 50], [216, 54, 789, 52, "imageUri"], [216, 62, 789, 60], [216, 63, 789, 61], [218, 6, 793, 4], [220, 6, 797, 4], [220, 10, 797, 8, "imageUri"], [220, 18, 797, 16], [220, 22, 797, 20, "imageUri"], [220, 30, 797, 28], [220, 31, 797, 29, "startsWith"], [220, 41, 797, 39], [220, 42, 797, 40], [220, 54, 797, 52], [220, 55, 797, 53], [220, 57, 797, 55], [221, 8, 801, 6], [223, 8, 805, 6], [223, 12, 805, 10], [223, 13, 805, 11, "imageUri"], [223, 21, 805, 19], [223, 22, 805, 20, "includes"], [223, 30, 805, 28], [223, 31, 805, 29], [223, 40, 805, 38], [223, 41, 805, 39], [223, 43, 805, 41], [224, 10, 809, 8, "console"], [224, 17, 809, 15], [224, 18, 809, 16, "error"], [224, 23, 809, 21], [224, 24, 809, 22], [224, 50, 809, 48], [224, 52, 809, 50, "imageUri"], [224, 60, 809, 58], [224, 61, 809, 59, "substring"], [224, 70, 809, 68], [224, 71, 809, 69], [224, 72, 809, 70], [224, 74, 809, 72], [224, 76, 809, 74], [224, 77, 809, 75], [224, 78, 809, 76], [225, 10, 813, 8, "imageUri"], [225, 18, 813, 16], [225, 21, 813, 19], [225, 25, 813, 23], [226, 8, 817, 6], [227, 6, 821, 4], [229, 6, 825, 4], [231, 6, 829, 4], [231, 10, 829, 8], [231, 11, 829, 9, "imageUri"], [231, 19, 829, 17], [231, 23, 829, 21, "__DEV__"], [231, 30, 829, 28], [231, 32, 829, 30], [232, 8, 833, 6, "console"], [232, 15, 833, 13], [232, 16, 833, 14, "warn"], [232, 20, 833, 18], [232, 21, 833, 19], [232, 60, 833, 58], [232, 61, 833, 59], [233, 8, 837, 6, "imageUri"], [233, 16, 837, 14], [233, 19, 837, 17], [233, 90, 837, 88], [234, 6, 841, 4], [235, 6, 845, 4, "setCapturedPhotoUri"], [235, 25, 845, 23], [235, 26, 845, 24, "imageUri"], [235, 34, 845, 32], [235, 35, 845, 33], [237, 6, 847, 4], [238, 6, 848, 4], [238, 10, 848, 8, "imageUri"], [238, 18, 848, 16], [238, 22, 848, 20, "imageUri"], [238, 30, 848, 28], [238, 31, 848, 29, "startsWith"], [238, 41, 848, 39], [238, 42, 848, 40], [238, 49, 848, 47], [238, 50, 848, 48], [238, 52, 848, 50], [239, 8, 849, 6, "console"], [239, 15, 849, 13], [239, 16, 849, 14, "log"], [239, 19, 849, 17], [239, 20, 849, 18], [239, 69, 849, 67], [239, 70, 849, 68], [240, 8, 850, 6, "fetch"], [240, 13, 850, 11], [240, 14, 850, 12, "imageUri"], [240, 22, 850, 20], [240, 23, 850, 21], [240, 24, 851, 9, "then"], [240, 28, 851, 13], [240, 29, 851, 14, "response"], [240, 37, 851, 22], [240, 41, 851, 26], [241, 10, 852, 10, "console"], [241, 17, 852, 17], [241, 18, 852, 18, "log"], [241, 21, 852, 21], [241, 22, 852, 22], [241, 64, 852, 64], [241, 66, 852, 66, "response"], [241, 74, 852, 74], [241, 75, 852, 75, "ok"], [241, 77, 852, 77], [241, 79, 852, 79, "response"], [241, 87, 852, 87], [241, 88, 852, 88, "status"], [241, 94, 852, 94], [241, 95, 852, 95], [242, 10, 853, 10], [242, 17, 853, 17, "response"], [242, 25, 853, 25], [242, 26, 853, 26, "blob"], [242, 30, 853, 30], [242, 31, 853, 31], [242, 32, 853, 32], [243, 8, 854, 8], [243, 9, 854, 9], [243, 10, 854, 10], [243, 11, 855, 9, "then"], [243, 15, 855, 13], [243, 16, 855, 14, "blob"], [243, 20, 855, 18], [243, 24, 855, 22], [244, 10, 856, 10, "console"], [244, 17, 856, 17], [244, 18, 856, 18, "log"], [244, 21, 856, 21], [244, 22, 856, 22], [244, 48, 856, 48], [244, 50, 856, 50, "blob"], [244, 54, 856, 54], [244, 55, 856, 55, "size"], [244, 59, 856, 59], [244, 61, 856, 61], [244, 75, 856, 75], [244, 77, 856, 77, "blob"], [244, 81, 856, 81], [244, 82, 856, 82, "type"], [244, 86, 856, 86], [244, 87, 856, 87], [245, 8, 857, 8], [245, 9, 857, 9], [245, 10, 857, 10], [245, 11, 858, 9, "catch"], [245, 16, 858, 14], [245, 17, 858, 15, "error"], [245, 22, 858, 20], [245, 26, 858, 24], [246, 10, 859, 10, "console"], [246, 17, 859, 17], [246, 18, 859, 18, "error"], [246, 23, 859, 23], [246, 24, 859, 24], [246, 62, 859, 62], [246, 64, 859, 64, "error"], [246, 69, 859, 69], [246, 70, 859, 70], [247, 8, 860, 8], [247, 9, 860, 9], [247, 10, 860, 10], [248, 6, 861, 4], [250, 6, 865, 4], [252, 6, 869, 4], [252, 12, 869, 10, "normalizedResult"], [252, 28, 869, 26], [252, 31, 869, 29], [253, 8, 873, 6], [253, 11, 873, 9, "result"], [253, 17, 873, 15], [254, 8, 877, 6, "imageUrl"], [254, 16, 877, 14], [254, 18, 877, 16, "imageUri"], [254, 26, 877, 24], [255, 8, 881, 6, "localUri"], [255, 16, 881, 14], [255, 18, 881, 16, "imageUri"], [255, 26, 881, 24], [256, 8, 885, 6], [258, 8, 889, 6, "originalUri"], [258, 19, 889, 17], [258, 21, 889, 19, "result"], [258, 27, 889, 25], [258, 28, 889, 26, "imageUrl"], [258, 36, 889, 34], [258, 40, 889, 38, "result"], [258, 46, 889, 44], [258, 47, 889, 45, "localUri"], [258, 55, 889, 53], [258, 59, 889, 57, "result"], [258, 65, 889, 63], [258, 66, 889, 64, "uri"], [258, 69, 889, 67], [258, 73, 889, 71, "result"], [258, 79, 889, 77], [258, 80, 889, 78, "publicUrl"], [259, 6, 893, 4], [259, 7, 893, 5], [260, 6, 897, 4, "console"], [260, 13, 897, 11], [260, 14, 897, 12, "log"], [260, 17, 897, 15], [260, 18, 897, 16], [260, 47, 897, 45], [260, 49, 897, 47, "imageUri"], [260, 57, 897, 55], [260, 58, 897, 56], [261, 6, 901, 4, "console"], [261, 13, 901, 11], [261, 14, 901, 12, "log"], [261, 17, 901, 15], [261, 18, 901, 16], [261, 43, 901, 41], [261, 45, 901, 43, "normalizedResult"], [261, 61, 901, 59], [261, 62, 901, 60], [262, 6, 905, 4, "setCameraResult"], [262, 21, 905, 19], [262, 22, 905, 20, "normalizedResult"], [262, 38, 905, 36], [262, 39, 905, 37], [263, 6, 909, 4, "setShowCamera"], [263, 19, 909, 17], [263, 20, 909, 18], [263, 25, 909, 23], [263, 26, 909, 24], [265, 6, 913, 4], [266, 4, 917, 2], [266, 5, 917, 3], [266, 7, 917, 5], [266, 9, 917, 7], [266, 10, 917, 8], [267, 4, 921, 2], [267, 10, 921, 8, "handleCameraCancel"], [267, 28, 921, 26], [267, 31, 921, 29], [267, 35, 921, 29, "useCallback"], [267, 53, 921, 40], [267, 55, 921, 41], [267, 61, 921, 47], [268, 6, 925, 4, "setShowCamera"], [268, 19, 925, 17], [268, 20, 925, 18], [268, 25, 925, 23], [268, 26, 925, 24], [269, 6, 929, 4, "setCameraResult"], [269, 21, 929, 19], [269, 22, 929, 20], [269, 26, 929, 24], [269, 27, 929, 25], [270, 6, 933, 4, "setCapturedPhotoUri"], [270, 25, 933, 23], [270, 26, 933, 24], [270, 30, 933, 28], [270, 31, 933, 29], [271, 4, 937, 2], [271, 5, 937, 3], [271, 7, 937, 5], [271, 9, 937, 7], [271, 10, 937, 8], [272, 4, 941, 2], [272, 10, 941, 8, "submitResponse"], [272, 24, 941, 22], [272, 27, 941, 25], [272, 33, 941, 25, "submitResponse"], [272, 34, 941, 25], [272, 39, 941, 37], [273, 6, 945, 4], [273, 10, 945, 8, "locationStatus"], [273, 24, 945, 22], [273, 29, 945, 27], [273, 39, 945, 37], [273, 43, 945, 41], [273, 44, 945, 42, "testingMode"], [273, 55, 945, 53], [273, 57, 945, 55], [274, 8, 949, 6, "<PERSON><PERSON>"], [274, 22, 949, 11], [274, 23, 949, 12, "alert"], [274, 28, 949, 17], [274, 29, 949, 18], [274, 48, 949, 37], [274, 50, 949, 39], [274, 86, 949, 75], [274, 87, 949, 76], [275, 8, 953, 6], [276, 6, 957, 4], [277, 6, 961, 4], [277, 10, 961, 8], [277, 11, 961, 9, "cameraResult"], [277, 23, 961, 21], [277, 25, 961, 23], [278, 8, 965, 6, "<PERSON><PERSON>"], [278, 22, 965, 11], [278, 23, 965, 12, "alert"], [278, 28, 965, 17], [278, 29, 965, 18], [278, 44, 965, 33], [278, 46, 965, 35], [278, 85, 965, 74], [278, 86, 965, 75], [279, 8, 969, 6], [280, 6, 973, 4], [281, 6, 977, 4], [281, 10, 977, 8], [281, 11, 977, 9, "response"], [281, 19, 977, 17], [281, 20, 977, 18, "trim"], [281, 24, 977, 22], [281, 25, 977, 23], [281, 26, 977, 24], [281, 28, 977, 26], [282, 8, 981, 6, "<PERSON><PERSON>"], [282, 22, 981, 11], [282, 23, 981, 12, "alert"], [282, 28, 981, 17], [282, 29, 985, 8], [282, 43, 985, 22], [282, 45, 989, 8], [282, 97, 993, 6], [282, 98, 993, 7], [283, 8, 997, 6], [284, 6, 1001, 4], [285, 6, 1005, 4, "setSubmitting"], [285, 19, 1005, 17], [285, 20, 1005, 18], [285, 24, 1005, 22], [285, 25, 1005, 23], [286, 6, 1009, 4], [286, 10, 1009, 8], [287, 8, 1013, 6], [289, 8, 1017, 6], [289, 14, 1017, 12, "responseData"], [289, 26, 1017, 24], [289, 29, 1017, 27], [290, 10, 1021, 8, "questionId"], [290, 20, 1021, 18], [290, 22, 1021, 20, "id"], [290, 24, 1021, 22], [291, 10, 1025, 8, "textResponse"], [291, 22, 1025, 20], [291, 24, 1025, 22, "response"], [291, 32, 1025, 30], [291, 33, 1025, 31, "trim"], [291, 37, 1025, 35], [291, 38, 1025, 36], [291, 39, 1025, 37], [292, 10, 1029, 8, "imageUrl"], [292, 18, 1029, 16], [292, 20, 1029, 18, "cameraResult"], [292, 32, 1029, 30], [292, 33, 1029, 31, "imageUrl"], [292, 41, 1029, 39], [293, 10, 1033, 8, "challengeCode"], [293, 23, 1033, 21], [293, 25, 1033, 23, "cameraResult"], [293, 37, 1033, 35], [293, 38, 1033, 36, "challengeCode"], [293, 51, 1033, 49], [294, 10, 1037, 8, "timestamp"], [294, 19, 1037, 17], [294, 21, 1037, 19, "cameraResult"], [294, 33, 1037, 31], [294, 34, 1037, 32, "timestamp"], [294, 43, 1037, 41], [295, 10, 1041, 8, "userLocation"], [295, 22, 1041, 20], [295, 24, 1041, 22, "currentLocation"], [295, 39, 1041, 37], [296, 10, 1045, 8, "distanceFromQuestion"], [296, 30, 1045, 28], [296, 32, 1045, 30, "distance"], [296, 40, 1045, 38], [297, 10, 1049, 8, "testingMode"], [297, 21, 1049, 19], [297, 23, 1049, 21, "testingMode"], [297, 34, 1049, 32], [297, 35, 1049, 34], [298, 8, 1053, 6], [298, 9, 1053, 7], [299, 8, 1057, 6, "console"], [299, 15, 1057, 13], [299, 16, 1057, 14, "log"], [299, 19, 1057, 17], [299, 20, 1057, 18], [299, 42, 1057, 40], [299, 44, 1057, 42, "responseData"], [299, 56, 1057, 54], [299, 57, 1057, 55], [301, 8, 1061, 6], [303, 8, 1065, 6], [303, 14, 1065, 12], [303, 18, 1065, 16, "Promise"], [303, 25, 1065, 23], [303, 26, 1065, 25, "resolve"], [303, 33, 1065, 32], [303, 37, 1065, 37, "setTimeout"], [303, 47, 1065, 47], [303, 48, 1065, 48, "resolve"], [303, 55, 1065, 55], [303, 57, 1065, 57], [303, 61, 1065, 61], [303, 62, 1065, 62], [303, 63, 1065, 63], [304, 8, 1069, 6, "<PERSON><PERSON>"], [304, 22, 1069, 11], [304, 23, 1069, 12, "alert"], [304, 28, 1069, 17], [304, 29, 1073, 8], [304, 50, 1073, 29], [304, 52, 1077, 8, "testingMode"], [304, 63, 1077, 19], [304, 66, 1081, 12], [304, 131, 1081, 77], [304, 134, 1085, 12], [304, 153, 1085, 31, "question"], [304, 161, 1085, 39], [304, 162, 1085, 40, "reward"], [304, 168, 1085, 46], [304, 169, 1085, 47, "toFixed"], [304, 176, 1085, 54], [304, 177, 1085, 55], [304, 178, 1085, 56], [304, 179, 1085, 57], [304, 225, 1085, 103], [304, 227, 1089, 8], [304, 228, 1093, 10], [305, 10, 1097, 12, "text"], [305, 14, 1097, 16], [305, 16, 1097, 18], [305, 20, 1097, 22], [306, 10, 1101, 12, "onPress"], [306, 17, 1101, 19], [306, 19, 1101, 21, "onPress"], [306, 20, 1101, 21], [306, 25, 1101, 27, "router"], [306, 43, 1101, 33], [306, 44, 1101, 34, "back"], [306, 48, 1101, 38], [306, 49, 1101, 39], [307, 8, 1105, 10], [307, 9, 1105, 11], [307, 10, 1113, 6], [307, 11, 1113, 7], [308, 6, 1117, 4], [308, 7, 1117, 5], [308, 8, 1117, 6], [308, 15, 1117, 13, "error"], [308, 20, 1117, 18], [308, 22, 1117, 20], [309, 8, 1121, 6, "console"], [309, 15, 1121, 13], [309, 16, 1121, 14, "error"], [309, 21, 1121, 19], [309, 22, 1121, 20], [309, 50, 1121, 48], [309, 52, 1121, 50, "error"], [309, 57, 1121, 55], [309, 58, 1121, 56], [310, 8, 1125, 6, "<PERSON><PERSON>"], [310, 22, 1125, 11], [310, 23, 1125, 12, "alert"], [310, 28, 1125, 17], [310, 29, 1125, 18], [310, 36, 1125, 25], [310, 38, 1125, 27], [310, 84, 1125, 73], [310, 85, 1125, 74], [311, 6, 1129, 4], [311, 7, 1129, 5], [311, 16, 1129, 14], [312, 8, 1133, 6, "setSubmitting"], [312, 21, 1133, 19], [312, 22, 1133, 20], [312, 27, 1133, 25], [312, 28, 1133, 26], [313, 6, 1137, 4], [314, 4, 1141, 2], [314, 5, 1141, 3], [316, 4, 1145, 2], [318, 4, 1149, 2], [318, 10, 1149, 8, "LocationStatus"], [318, 24, 1149, 22], [318, 27, 1149, 25, "LocationStatus"], [318, 28, 1149, 25], [318, 33, 1149, 31], [319, 6, 1153, 4], [319, 12, 1153, 10, "getStatusConfig"], [319, 27, 1153, 25], [319, 30, 1153, 28, "getStatusConfig"], [319, 31, 1153, 28], [319, 36, 1153, 34], [320, 8, 1157, 6], [320, 16, 1157, 14, "locationStatus"], [320, 30, 1157, 28], [321, 10, 1161, 8], [321, 15, 1161, 13], [321, 25, 1161, 23], [322, 12, 1165, 10], [322, 19, 1165, 17], [323, 14, 1169, 12, "color"], [323, 19, 1169, 17], [323, 21, 1169, 19], [323, 30, 1169, 28], [324, 14, 1173, 12, "bgColor"], [324, 21, 1173, 19], [324, 23, 1173, 21], [324, 32, 1173, 30], [325, 14, 1177, 12, "icon"], [325, 18, 1177, 16], [325, 33, 1177, 18], [325, 37, 1177, 18, "_jsxDevRuntime"], [325, 51, 1177, 18], [325, 52, 1177, 18, "jsxDEV"], [325, 58, 1177, 18], [325, 60, 1177, 19, "_lucideReactNative"], [325, 78, 1177, 19], [325, 79, 1177, 19, "Navigation"], [325, 89, 1177, 29], [326, 16, 1177, 30, "size"], [326, 20, 1177, 34], [326, 22, 1177, 36], [326, 24, 1177, 39], [327, 16, 1177, 40, "color"], [327, 21, 1177, 45], [327, 23, 1177, 46], [328, 14, 1177, 55], [329, 16, 1177, 55, "fileName"], [329, 24, 1177, 55], [329, 26, 1177, 55, "_jsxFileName"], [329, 38, 1177, 55], [330, 16, 1177, 55, "lineNumber"], [330, 26, 1177, 55], [331, 16, 1177, 55, "columnNumber"], [331, 28, 1177, 55], [332, 14, 1177, 55], [332, 21, 1177, 57], [332, 22, 1177, 58], [333, 14, 1181, 12, "title"], [333, 19, 1181, 17], [333, 21, 1181, 19], [333, 40, 1181, 38], [334, 14, 1185, 12, "message"], [334, 21, 1185, 19], [334, 23, 1185, 21, "gettingLocation"], [334, 38, 1185, 36], [334, 41, 1189, 16], [334, 72, 1189, 47], [334, 75, 1193, 16], [335, 12, 1197, 10], [335, 13, 1197, 11], [336, 10, 1201, 8], [336, 15, 1201, 13], [336, 25, 1201, 23], [337, 12, 1205, 10], [337, 19, 1205, 17], [338, 14, 1209, 12, "color"], [338, 19, 1209, 17], [338, 21, 1209, 19], [338, 30, 1209, 28], [339, 14, 1213, 12, "bgColor"], [339, 21, 1213, 19], [339, 23, 1213, 21], [339, 32, 1213, 30], [340, 14, 1217, 12, "icon"], [340, 18, 1217, 16], [340, 33, 1217, 18], [340, 37, 1217, 18, "_jsxDevRuntime"], [340, 51, 1217, 18], [340, 52, 1217, 18, "jsxDEV"], [340, 58, 1217, 18], [340, 60, 1217, 19, "_lucideReactNative"], [340, 78, 1217, 19], [340, 79, 1217, 19, "CheckCircle2"], [340, 91, 1217, 31], [341, 16, 1217, 32, "size"], [341, 20, 1217, 36], [341, 22, 1217, 38], [341, 24, 1217, 41], [342, 16, 1217, 42, "color"], [342, 21, 1217, 47], [342, 23, 1217, 48], [343, 14, 1217, 57], [344, 16, 1217, 57, "fileName"], [344, 24, 1217, 57], [344, 26, 1217, 57, "_jsxFileName"], [344, 38, 1217, 57], [345, 16, 1217, 57, "lineNumber"], [345, 26, 1217, 57], [346, 16, 1217, 57, "columnNumber"], [346, 28, 1217, 57], [347, 14, 1217, 57], [347, 21, 1217, 59], [347, 22, 1217, 60], [348, 14, 1221, 12, "title"], [348, 19, 1221, 17], [348, 21, 1221, 19], [348, 40, 1221, 38], [349, 14, 1225, 12, "message"], [349, 21, 1225, 19], [349, 23, 1225, 21], [349, 33, 1225, 31, "distance"], [349, 41, 1225, 39], [349, 45, 1225, 43], [349, 46, 1225, 44], [350, 12, 1229, 10], [350, 13, 1229, 11], [351, 10, 1233, 8], [351, 15, 1233, 13], [351, 24, 1233, 22], [352, 12, 1237, 10], [352, 19, 1237, 17], [353, 14, 1241, 12, "color"], [353, 19, 1241, 17], [353, 21, 1241, 19], [353, 30, 1241, 28], [354, 14, 1245, 12, "bgColor"], [354, 21, 1245, 19], [354, 23, 1245, 21], [354, 32, 1245, 30], [355, 14, 1249, 12, "icon"], [355, 18, 1249, 16], [355, 33, 1249, 18], [355, 37, 1249, 18, "_jsxDevRuntime"], [355, 51, 1249, 18], [355, 52, 1249, 18, "jsxDEV"], [355, 58, 1249, 18], [355, 60, 1249, 19, "_lucideReactNative"], [355, 78, 1249, 19], [355, 79, 1249, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [355, 92, 1249, 32], [356, 16, 1249, 33, "size"], [356, 20, 1249, 37], [356, 22, 1249, 39], [356, 24, 1249, 42], [357, 16, 1249, 43, "color"], [357, 21, 1249, 48], [357, 23, 1249, 49], [358, 14, 1249, 58], [359, 16, 1249, 58, "fileName"], [359, 24, 1249, 58], [359, 26, 1249, 58, "_jsxFileName"], [359, 38, 1249, 58], [360, 16, 1249, 58, "lineNumber"], [360, 26, 1249, 58], [361, 16, 1249, 58, "columnNumber"], [361, 28, 1249, 58], [362, 14, 1249, 58], [362, 21, 1249, 60], [362, 22, 1249, 61], [363, 14, 1253, 12, "title"], [363, 19, 1253, 17], [363, 21, 1253, 19], [363, 35, 1253, 33], [364, 14, 1257, 12, "message"], [364, 21, 1257, 19], [364, 23, 1257, 21], [364, 33, 1257, 31, "distance"], [364, 41, 1257, 39], [364, 45, 1257, 43], [364, 46, 1257, 44], [365, 12, 1261, 10], [365, 13, 1261, 11], [366, 10, 1265, 8], [366, 15, 1265, 13], [366, 22, 1265, 20], [367, 12, 1269, 10], [367, 19, 1269, 17], [368, 14, 1273, 12, "color"], [368, 19, 1273, 17], [368, 21, 1273, 19], [368, 30, 1273, 28], [369, 14, 1277, 12, "bgColor"], [369, 21, 1277, 19], [369, 23, 1277, 21], [369, 32, 1277, 30], [370, 14, 1281, 12, "icon"], [370, 18, 1281, 16], [370, 33, 1281, 18], [370, 37, 1281, 18, "_jsxDevRuntime"], [370, 51, 1281, 18], [370, 52, 1281, 18, "jsxDEV"], [370, 58, 1281, 18], [370, 60, 1281, 19, "_lucideReactNative"], [370, 78, 1281, 19], [370, 79, 1281, 19, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [370, 92, 1281, 32], [371, 16, 1281, 33, "size"], [371, 20, 1281, 37], [371, 22, 1281, 39], [371, 24, 1281, 42], [372, 16, 1281, 43, "color"], [372, 21, 1281, 48], [372, 23, 1281, 49], [373, 14, 1281, 58], [374, 16, 1281, 58, "fileName"], [374, 24, 1281, 58], [374, 26, 1281, 58, "_jsxFileName"], [374, 38, 1281, 58], [375, 16, 1281, 58, "lineNumber"], [375, 26, 1281, 58], [376, 16, 1281, 58, "columnNumber"], [376, 28, 1281, 58], [377, 14, 1281, 58], [377, 21, 1281, 60], [377, 22, 1281, 61], [378, 14, 1285, 12, "title"], [378, 19, 1285, 17], [378, 21, 1285, 19], [378, 37, 1285, 35], [379, 14, 1289, 12, "message"], [379, 21, 1289, 19], [379, 23, 1289, 21], [380, 12, 1293, 10], [380, 13, 1293, 11], [381, 10, 1297, 8], [382, 12, 1301, 10], [382, 19, 1301, 17], [383, 14, 1305, 12, "color"], [383, 19, 1305, 17], [383, 21, 1305, 19], [383, 30, 1305, 28], [384, 14, 1309, 12, "bgColor"], [384, 21, 1309, 19], [384, 23, 1309, 21], [384, 32, 1309, 30], [385, 14, 1313, 12, "icon"], [385, 18, 1313, 16], [385, 33, 1313, 18], [385, 37, 1313, 18, "_jsxDevRuntime"], [385, 51, 1313, 18], [385, 52, 1313, 18, "jsxDEV"], [385, 58, 1313, 18], [385, 60, 1313, 19, "_lucideReactNative"], [385, 78, 1313, 19], [385, 79, 1313, 19, "Navigation"], [385, 89, 1313, 29], [386, 16, 1313, 30, "size"], [386, 20, 1313, 34], [386, 22, 1313, 36], [386, 24, 1313, 39], [387, 16, 1313, 40, "color"], [387, 21, 1313, 45], [387, 23, 1313, 46], [388, 14, 1313, 55], [389, 16, 1313, 55, "fileName"], [389, 24, 1313, 55], [389, 26, 1313, 55, "_jsxFileName"], [389, 38, 1313, 55], [390, 16, 1313, 55, "lineNumber"], [390, 26, 1313, 55], [391, 16, 1313, 55, "columnNumber"], [391, 28, 1313, 55], [392, 14, 1313, 55], [392, 21, 1313, 57], [392, 22, 1313, 58], [393, 14, 1317, 12, "title"], [393, 19, 1317, 17], [393, 21, 1317, 19], [393, 37, 1317, 35], [394, 14, 1321, 12, "message"], [394, 21, 1321, 19], [394, 23, 1321, 21], [395, 12, 1325, 10], [395, 13, 1325, 11], [396, 8, 1329, 6], [397, 6, 1333, 4], [397, 7, 1333, 5], [398, 6, 1337, 4], [398, 12, 1337, 10, "config"], [398, 18, 1337, 16], [398, 21, 1337, 19, "getStatusConfig"], [398, 36, 1337, 34], [398, 37, 1337, 35], [398, 38, 1337, 36], [399, 6, 1341, 4], [399, 26, 1345, 6], [399, 30, 1345, 6, "_jsxDevRuntime"], [399, 44, 1345, 6], [399, 45, 1345, 6, "jsxDEV"], [399, 51, 1345, 6], [399, 53, 1345, 7, "_View"], [399, 58, 1345, 7], [399, 59, 1345, 7, "default"], [399, 66, 1345, 11], [400, 8, 1349, 8, "style"], [400, 13, 1349, 13], [400, 15, 1349, 15], [401, 10, 1353, 10, "backgroundColor"], [401, 25, 1353, 25], [401, 27, 1353, 27, "config"], [401, 33, 1353, 33], [401, 34, 1353, 34, "bgColor"], [401, 41, 1353, 41], [402, 10, 1357, 10, "borderRadius"], [402, 22, 1357, 22], [402, 24, 1357, 24], [402, 26, 1357, 26], [403, 10, 1361, 10, "padding"], [403, 17, 1361, 17], [403, 19, 1361, 19], [403, 21, 1361, 21], [404, 10, 1365, 10, "marginBottom"], [404, 22, 1365, 22], [404, 24, 1365, 24], [404, 26, 1365, 26], [405, 10, 1369, 10, "borderWidth"], [405, 21, 1369, 21], [405, 23, 1369, 23], [405, 24, 1369, 24], [406, 10, 1373, 10, "borderColor"], [406, 21, 1373, 21], [406, 23, 1373, 23, "config"], [406, 29, 1373, 29], [406, 30, 1373, 30, "color"], [406, 35, 1373, 35], [406, 38, 1373, 38], [407, 8, 1377, 8], [407, 9, 1377, 10], [408, 8, 1377, 10, "children"], [408, 16, 1377, 10], [408, 32, 1385, 8], [408, 36, 1385, 8, "_jsxDevRuntime"], [408, 50, 1385, 8], [408, 51, 1385, 8, "jsxDEV"], [408, 57, 1385, 8], [408, 59, 1385, 9, "_View"], [408, 64, 1385, 9], [408, 65, 1385, 9, "default"], [408, 72, 1385, 13], [409, 10, 1389, 10, "style"], [409, 15, 1389, 15], [409, 17, 1389, 17], [410, 12, 1393, 12, "flexDirection"], [410, 25, 1393, 25], [410, 27, 1393, 27], [410, 32, 1393, 32], [411, 12, 1397, 12, "alignItems"], [411, 22, 1397, 22], [411, 24, 1397, 24], [411, 32, 1397, 32], [412, 12, 1401, 12, "marginBottom"], [412, 24, 1401, 24], [412, 26, 1401, 26], [413, 10, 1405, 10], [413, 11, 1405, 12], [414, 10, 1405, 12, "children"], [414, 18, 1405, 12], [414, 21, 1413, 11, "config"], [414, 27, 1413, 17], [414, 28, 1413, 18, "icon"], [414, 32, 1413, 22], [414, 47, 1417, 10], [414, 51, 1417, 10, "_jsxDevRuntime"], [414, 65, 1417, 10], [414, 66, 1417, 10, "jsxDEV"], [414, 72, 1417, 10], [414, 74, 1417, 11, "_Text"], [414, 79, 1417, 11], [414, 80, 1417, 11, "default"], [414, 87, 1417, 15], [415, 12, 1421, 12, "style"], [415, 17, 1421, 17], [415, 19, 1421, 19], [416, 14, 1425, 14, "fontSize"], [416, 22, 1425, 22], [416, 24, 1425, 24], [416, 26, 1425, 26], [417, 14, 1429, 14, "fontWeight"], [417, 24, 1429, 24], [417, 26, 1429, 26], [417, 31, 1429, 31], [418, 14, 1433, 14, "color"], [418, 19, 1433, 19], [418, 21, 1433, 21, "config"], [418, 27, 1433, 27], [418, 28, 1433, 28, "color"], [418, 33, 1433, 33], [419, 14, 1437, 14, "marginLeft"], [419, 24, 1437, 24], [419, 26, 1437, 26], [420, 12, 1441, 12], [420, 13, 1441, 14], [421, 12, 1441, 14, "children"], [421, 20, 1441, 14], [421, 22, 1449, 13, "config"], [421, 28, 1449, 19], [421, 29, 1449, 20, "title"], [422, 10, 1449, 25], [423, 12, 1449, 25, "fileName"], [423, 20, 1449, 25], [423, 22, 1449, 25, "_jsxFileName"], [423, 34, 1449, 25], [424, 12, 1449, 25, "lineNumber"], [424, 22, 1449, 25], [425, 12, 1449, 25, "columnNumber"], [425, 24, 1449, 25], [426, 10, 1449, 25], [426, 17, 1453, 16], [426, 18, 1453, 17], [427, 8, 1453, 17], [428, 10, 1453, 17, "fileName"], [428, 18, 1453, 17], [428, 20, 1453, 17, "_jsxFileName"], [428, 32, 1453, 17], [429, 10, 1453, 17, "lineNumber"], [429, 20, 1453, 17], [430, 10, 1453, 17, "columnNumber"], [430, 22, 1453, 17], [431, 8, 1453, 17], [431, 15, 1457, 14], [431, 16, 1457, 15], [431, 31, 1461, 8], [431, 35, 1461, 8, "_jsxDevRuntime"], [431, 49, 1461, 8], [431, 50, 1461, 8, "jsxDEV"], [431, 56, 1461, 8], [431, 58, 1461, 9, "_Text"], [431, 63, 1461, 9], [431, 64, 1461, 9, "default"], [431, 71, 1461, 13], [432, 10, 1461, 14, "style"], [432, 15, 1461, 19], [432, 17, 1461, 21], [433, 12, 1461, 23, "fontSize"], [433, 20, 1461, 31], [433, 22, 1461, 33], [433, 24, 1461, 35], [434, 12, 1461, 37, "color"], [434, 17, 1461, 42], [434, 19, 1461, 44, "config"], [434, 25, 1461, 50], [434, 26, 1461, 51, "color"], [434, 31, 1461, 56], [435, 12, 1461, 58, "lineHeight"], [435, 22, 1461, 68], [435, 24, 1461, 70], [436, 10, 1461, 73], [436, 11, 1461, 75], [437, 10, 1461, 75, "children"], [437, 18, 1461, 75], [437, 20, 1465, 11, "config"], [437, 26, 1465, 17], [437, 27, 1465, 18, "message"], [438, 8, 1465, 25], [439, 10, 1465, 25, "fileName"], [439, 18, 1465, 25], [439, 20, 1465, 25, "_jsxFileName"], [439, 32, 1465, 25], [440, 10, 1465, 25, "lineNumber"], [440, 20, 1465, 25], [441, 10, 1465, 25, "columnNumber"], [441, 22, 1465, 25], [442, 8, 1465, 25], [442, 15, 1469, 14], [442, 16, 1469, 15], [442, 18, 1473, 9], [442, 19, 1473, 10, "locationStatus"], [442, 33, 1473, 24], [442, 38, 1473, 29], [442, 47, 1473, 38], [442, 51, 1473, 42, "locationStatus"], [442, 65, 1473, 56], [442, 70, 1473, 61], [442, 77, 1473, 68], [442, 95, 1477, 10], [442, 99, 1477, 10, "_jsxDevRuntime"], [442, 113, 1477, 10], [442, 114, 1477, 10, "jsxDEV"], [442, 120, 1477, 10], [442, 122, 1477, 11, "_View"], [442, 127, 1477, 11], [442, 128, 1477, 11, "default"], [442, 135, 1477, 15], [443, 10, 1477, 16, "style"], [443, 15, 1477, 21], [443, 17, 1477, 23], [444, 12, 1477, 25, "flexDirection"], [444, 25, 1477, 38], [444, 27, 1477, 40], [444, 32, 1477, 45], [445, 12, 1477, 47, "marginTop"], [445, 21, 1477, 56], [445, 23, 1477, 58], [446, 10, 1477, 61], [446, 11, 1477, 63], [447, 10, 1477, 63, "children"], [447, 18, 1477, 63], [447, 34, 1481, 12], [447, 38, 1481, 12, "_jsxDevRuntime"], [447, 52, 1481, 12], [447, 53, 1481, 12, "jsxDEV"], [447, 59, 1481, 12], [447, 61, 1481, 13, "_TouchableOpacity"], [447, 78, 1481, 13], [447, 79, 1481, 13, "default"], [447, 86, 1481, 29], [448, 12, 1485, 14, "onPress"], [448, 19, 1485, 21], [448, 21, 1485, 23, "verifyLocation"], [448, 35, 1485, 38], [449, 12, 1489, 14, "disabled"], [449, 20, 1489, 22], [449, 22, 1489, 24, "gettingLocation"], [449, 37, 1489, 40], [450, 12, 1493, 14, "style"], [450, 17, 1493, 19], [450, 19, 1493, 21], [451, 14, 1497, 16, "backgroundColor"], [451, 29, 1497, 31], [451, 31, 1497, 33, "config"], [451, 37, 1497, 39], [451, 38, 1497, 40, "color"], [451, 43, 1497, 45], [452, 14, 1501, 16, "borderRadius"], [452, 26, 1501, 28], [452, 28, 1501, 30], [452, 29, 1501, 31], [453, 14, 1505, 16, "paddingVertical"], [453, 29, 1505, 31], [453, 31, 1505, 33], [453, 32, 1505, 34], [454, 14, 1509, 16, "paddingHorizontal"], [454, 31, 1509, 33], [454, 33, 1509, 35], [454, 35, 1509, 37], [455, 14, 1513, 16, "opacity"], [455, 21, 1513, 23], [455, 23, 1513, 25, "gettingLocation"], [455, 38, 1513, 40], [455, 41, 1513, 43], [455, 44, 1513, 46], [455, 47, 1513, 49], [456, 12, 1517, 14], [456, 13, 1517, 16], [457, 12, 1517, 16, "children"], [457, 20, 1517, 16], [457, 35, 1525, 14], [457, 39, 1525, 14, "_jsxDevRuntime"], [457, 53, 1525, 14], [457, 54, 1525, 14, "jsxDEV"], [457, 60, 1525, 14], [457, 62, 1525, 15, "_Text"], [457, 67, 1525, 15], [457, 68, 1525, 15, "default"], [457, 75, 1525, 19], [458, 14, 1525, 20, "style"], [458, 19, 1525, 25], [458, 21, 1525, 27], [459, 16, 1525, 29, "fontSize"], [459, 24, 1525, 37], [459, 26, 1525, 39], [459, 28, 1525, 41], [460, 16, 1525, 43, "color"], [460, 21, 1525, 48], [460, 23, 1525, 50], [460, 29, 1525, 56], [461, 16, 1525, 58, "fontWeight"], [461, 26, 1525, 68], [461, 28, 1525, 70], [462, 14, 1525, 76], [462, 15, 1525, 78], [463, 14, 1525, 78, "children"], [463, 22, 1525, 78], [463, 24, 1529, 17, "gettingLocation"], [463, 39, 1529, 32], [463, 42, 1529, 35], [463, 52, 1529, 45], [463, 55, 1529, 48], [464, 12, 1529, 59], [465, 14, 1529, 59, "fileName"], [465, 22, 1529, 59], [465, 24, 1529, 59, "_jsxFileName"], [465, 36, 1529, 59], [466, 14, 1529, 59, "lineNumber"], [466, 24, 1529, 59], [467, 14, 1529, 59, "columnNumber"], [467, 26, 1529, 59], [468, 12, 1529, 59], [468, 19, 1533, 20], [469, 10, 1533, 21], [470, 12, 1533, 21, "fileName"], [470, 20, 1533, 21], [470, 22, 1533, 21, "_jsxFileName"], [470, 34, 1533, 21], [471, 12, 1533, 21, "lineNumber"], [471, 22, 1533, 21], [472, 12, 1533, 21, "columnNumber"], [472, 24, 1533, 21], [473, 10, 1533, 21], [473, 17, 1537, 30], [473, 18, 1537, 31], [473, 33, 1541, 12], [473, 37, 1541, 12, "_jsxDevRuntime"], [473, 51, 1541, 12], [473, 52, 1541, 12, "jsxDEV"], [473, 58, 1541, 12], [473, 60, 1541, 13, "_TouchableOpacity"], [473, 77, 1541, 13], [473, 78, 1541, 13, "default"], [473, 85, 1541, 29], [474, 12, 1545, 14, "onPress"], [474, 19, 1545, 21], [474, 21, 1545, 23, "onPress"], [474, 22, 1545, 23], [474, 27, 1545, 29], [475, 14, 1549, 16, "console"], [475, 21, 1549, 23], [475, 22, 1549, 24, "log"], [475, 25, 1549, 27], [475, 26, 1549, 28], [475, 48, 1549, 50], [475, 50, 1549, 52], [476, 16, 1553, 18, "before"], [476, 22, 1553, 24], [476, 24, 1553, 26, "testingMode"], [476, 35, 1553, 37], [477, 16, 1557, 18, "after"], [477, 21, 1557, 23], [477, 23, 1557, 25], [477, 24, 1557, 26, "testingMode"], [478, 14, 1561, 16], [478, 15, 1561, 17], [478, 16, 1561, 18], [479, 14, 1565, 16, "setTestingMode"], [479, 28, 1565, 30], [479, 29, 1565, 31], [479, 30, 1565, 32, "testingMode"], [479, 41, 1565, 43], [479, 42, 1565, 44], [480, 12, 1569, 14], [480, 13, 1569, 16], [481, 12, 1573, 14, "style"], [481, 17, 1573, 19], [481, 19, 1573, 21], [482, 14, 1577, 16, "backgroundColor"], [482, 29, 1577, 31], [482, 31, 1577, 33, "testingMode"], [482, 42, 1577, 44], [482, 45, 1577, 47], [482, 54, 1577, 56], [482, 57, 1577, 59], [482, 66, 1577, 68], [483, 14, 1581, 16, "borderRadius"], [483, 26, 1581, 28], [483, 28, 1581, 30], [483, 29, 1581, 31], [484, 14, 1585, 16, "paddingVertical"], [484, 29, 1585, 31], [484, 31, 1585, 33], [484, 32, 1585, 34], [485, 14, 1589, 16, "paddingHorizontal"], [485, 31, 1589, 33], [485, 33, 1589, 35], [485, 35, 1589, 37], [486, 14, 1593, 16, "marginLeft"], [486, 24, 1593, 26], [486, 26, 1593, 28], [487, 12, 1597, 14], [487, 13, 1597, 16], [488, 12, 1597, 16, "children"], [488, 20, 1597, 16], [488, 35, 1605, 14], [488, 39, 1605, 14, "_jsxDevRuntime"], [488, 53, 1605, 14], [488, 54, 1605, 14, "jsxDEV"], [488, 60, 1605, 14], [488, 62, 1605, 15, "_Text"], [488, 67, 1605, 15], [488, 68, 1605, 15, "default"], [488, 75, 1605, 19], [489, 14, 1605, 20, "style"], [489, 19, 1605, 25], [489, 21, 1605, 27], [490, 16, 1605, 29, "fontSize"], [490, 24, 1605, 37], [490, 26, 1605, 39], [490, 28, 1605, 41], [491, 16, 1605, 43, "color"], [491, 21, 1605, 48], [491, 23, 1605, 50], [491, 29, 1605, 56], [492, 16, 1605, 58, "fontWeight"], [492, 26, 1605, 68], [492, 28, 1605, 70], [493, 14, 1605, 76], [493, 15, 1605, 78], [494, 14, 1605, 78, "children"], [494, 22, 1605, 78], [494, 24, 1609, 17, "testingMode"], [494, 35, 1609, 28], [494, 38, 1609, 31], [494, 50, 1609, 43], [494, 53, 1609, 46], [495, 12, 1609, 62], [496, 14, 1609, 62, "fileName"], [496, 22, 1609, 62], [496, 24, 1609, 62, "_jsxFileName"], [496, 36, 1609, 62], [497, 14, 1609, 62, "lineNumber"], [497, 24, 1609, 62], [498, 14, 1609, 62, "columnNumber"], [498, 26, 1609, 62], [499, 12, 1609, 62], [499, 19, 1613, 20], [500, 10, 1613, 21], [501, 12, 1613, 21, "fileName"], [501, 20, 1613, 21], [501, 22, 1613, 21, "_jsxFileName"], [501, 34, 1613, 21], [502, 12, 1613, 21, "lineNumber"], [502, 22, 1613, 21], [503, 12, 1613, 21, "columnNumber"], [503, 24, 1613, 21], [504, 10, 1613, 21], [504, 17, 1617, 30], [504, 18, 1617, 31], [505, 8, 1617, 31], [506, 10, 1617, 31, "fileName"], [506, 18, 1617, 31], [506, 20, 1617, 31, "_jsxFileName"], [506, 32, 1617, 31], [507, 10, 1617, 31, "lineNumber"], [507, 20, 1617, 31], [508, 10, 1617, 31, "columnNumber"], [508, 22, 1617, 31], [509, 8, 1617, 31], [509, 15, 1621, 16], [509, 16, 1625, 9], [509, 18, 1629, 9, "testingMode"], [509, 29, 1629, 20], [509, 46, 1633, 10], [509, 50, 1633, 10, "_jsxDevRuntime"], [509, 64, 1633, 10], [509, 65, 1633, 10, "jsxDEV"], [509, 71, 1633, 10], [509, 73, 1633, 11, "_View"], [509, 78, 1633, 11], [509, 79, 1633, 11, "default"], [509, 86, 1633, 15], [510, 10, 1637, 12, "style"], [510, 15, 1637, 17], [510, 17, 1637, 19], [511, 12, 1641, 14, "backgroundColor"], [511, 27, 1641, 29], [511, 29, 1641, 31], [511, 38, 1641, 40], [512, 12, 1645, 14, "borderRadius"], [512, 24, 1645, 26], [512, 26, 1645, 28], [512, 27, 1645, 29], [513, 12, 1649, 14, "padding"], [513, 19, 1649, 21], [513, 21, 1649, 23], [513, 23, 1649, 25], [514, 12, 1653, 14, "marginTop"], [514, 21, 1653, 23], [514, 23, 1653, 25], [514, 25, 1653, 27], [515, 12, 1657, 14, "borderWidth"], [515, 23, 1657, 25], [515, 25, 1657, 27], [515, 26, 1657, 28], [516, 12, 1661, 14, "borderColor"], [516, 23, 1661, 25], [516, 25, 1661, 27], [517, 10, 1665, 12], [517, 11, 1665, 14], [518, 10, 1665, 14, "children"], [518, 18, 1665, 14], [518, 34, 1673, 12], [518, 38, 1673, 12, "_jsxDevRuntime"], [518, 52, 1673, 12], [518, 53, 1673, 12, "jsxDEV"], [518, 59, 1673, 12], [518, 61, 1673, 13, "_Text"], [518, 66, 1673, 13], [518, 67, 1673, 13, "default"], [518, 74, 1673, 17], [519, 12, 1677, 14, "style"], [519, 17, 1677, 19], [519, 19, 1677, 21], [520, 14, 1681, 16, "fontSize"], [520, 22, 1681, 24], [520, 24, 1681, 26], [520, 26, 1681, 28], [521, 14, 1685, 16, "fontWeight"], [521, 24, 1685, 26], [521, 26, 1685, 28], [521, 31, 1685, 33], [522, 14, 1689, 16, "color"], [522, 19, 1689, 21], [522, 21, 1689, 23], [522, 30, 1689, 32], [523, 14, 1693, 16, "marginBottom"], [523, 26, 1693, 28], [523, 28, 1693, 30], [524, 12, 1697, 14], [524, 13, 1697, 16], [525, 12, 1697, 16, "children"], [525, 20, 1697, 16], [525, 22, 1701, 13], [526, 10, 1709, 12], [527, 12, 1709, 12, "fileName"], [527, 20, 1709, 12], [527, 22, 1709, 12, "_jsxFileName"], [527, 34, 1709, 12], [528, 12, 1709, 12, "lineNumber"], [528, 22, 1709, 12], [529, 12, 1709, 12, "columnNumber"], [529, 24, 1709, 12], [530, 10, 1709, 12], [530, 17, 1709, 18], [530, 18, 1709, 19], [530, 33, 1713, 12], [530, 37, 1713, 12, "_jsxDevRuntime"], [530, 51, 1713, 12], [530, 52, 1713, 12, "jsxDEV"], [530, 58, 1713, 12], [530, 60, 1713, 13, "_Text"], [530, 65, 1713, 13], [530, 66, 1713, 13, "default"], [530, 73, 1713, 17], [531, 12, 1713, 18, "style"], [531, 17, 1713, 23], [531, 19, 1713, 25], [532, 14, 1713, 27, "fontSize"], [532, 22, 1713, 35], [532, 24, 1713, 37], [532, 26, 1713, 39], [533, 14, 1713, 41, "color"], [533, 19, 1713, 46], [533, 21, 1713, 48], [533, 30, 1713, 57], [534, 14, 1713, 59, "lineHeight"], [534, 24, 1713, 69], [534, 26, 1713, 71], [535, 12, 1713, 74], [535, 13, 1713, 76], [536, 12, 1713, 76, "children"], [536, 20, 1713, 76], [536, 22, 1713, 77], [537, 10, 1725, 12], [538, 12, 1725, 12, "fileName"], [538, 20, 1725, 12], [538, 22, 1725, 12, "_jsxFileName"], [538, 34, 1725, 12], [539, 12, 1725, 12, "lineNumber"], [539, 22, 1725, 12], [540, 12, 1725, 12, "columnNumber"], [540, 24, 1725, 12], [541, 10, 1725, 12], [541, 17, 1725, 18], [541, 18, 1725, 19], [542, 8, 1725, 19], [543, 10, 1725, 19, "fileName"], [543, 18, 1725, 19], [543, 20, 1725, 19, "_jsxFileName"], [543, 32, 1725, 19], [544, 10, 1725, 19, "lineNumber"], [544, 20, 1725, 19], [545, 10, 1725, 19, "columnNumber"], [545, 22, 1725, 19], [546, 8, 1725, 19], [546, 15, 1729, 16], [546, 16, 1733, 9], [547, 6, 1733, 9], [548, 8, 1733, 9, "fileName"], [548, 16, 1733, 9], [548, 18, 1733, 9, "_jsxFileName"], [548, 30, 1733, 9], [549, 8, 1733, 9, "lineNumber"], [549, 18, 1733, 9], [550, 8, 1733, 9, "columnNumber"], [550, 20, 1733, 9], [551, 6, 1733, 9], [551, 13, 1737, 12], [551, 14, 1737, 13], [552, 4, 1745, 2], [552, 5, 1745, 3], [553, 4, 1749, 2], [553, 24, 1753, 4], [553, 28, 1753, 4, "_jsxDevRuntime"], [553, 42, 1753, 4], [553, 43, 1753, 4, "jsxDEV"], [553, 49, 1753, 4], [553, 51, 1753, 5, "_View"], [553, 56, 1753, 5], [553, 57, 1753, 5, "default"], [553, 64, 1753, 9], [554, 6, 1753, 10, "style"], [554, 11, 1753, 15], [554, 13, 1753, 17], [555, 8, 1753, 19, "flex"], [555, 12, 1753, 23], [555, 14, 1753, 25], [555, 15, 1753, 26], [556, 8, 1753, 28, "backgroundColor"], [556, 23, 1753, 43], [556, 25, 1753, 45], [557, 6, 1753, 55], [557, 7, 1753, 57], [558, 6, 1753, 57, "children"], [558, 14, 1753, 57], [558, 30, 1757, 6], [558, 34, 1757, 6, "_jsxDevRuntime"], [558, 48, 1757, 6], [558, 49, 1757, 6, "jsxDEV"], [558, 55, 1757, 6], [558, 57, 1757, 7, "_expoStatusBar"], [558, 71, 1757, 7], [558, 72, 1757, 7, "StatusBar"], [558, 81, 1757, 16], [559, 8, 1757, 17, "style"], [559, 13, 1757, 22], [559, 15, 1757, 23], [560, 6, 1757, 29], [561, 8, 1757, 29, "fileName"], [561, 16, 1757, 29], [561, 18, 1757, 29, "_jsxFileName"], [561, 30, 1757, 29], [562, 8, 1757, 29, "lineNumber"], [562, 18, 1757, 29], [563, 8, 1757, 29, "columnNumber"], [563, 20, 1757, 29], [564, 6, 1757, 29], [564, 13, 1757, 31], [564, 14, 1757, 32], [564, 29, 1765, 6], [564, 33, 1765, 6, "_jsxDevRuntime"], [564, 47, 1765, 6], [564, 48, 1765, 6, "jsxDEV"], [564, 54, 1765, 6], [564, 56, 1765, 7, "_View"], [564, 61, 1765, 7], [564, 62, 1765, 7, "default"], [564, 69, 1765, 11], [565, 8, 1769, 8, "style"], [565, 13, 1769, 13], [565, 15, 1769, 15], [566, 10, 1773, 10, "backgroundColor"], [566, 25, 1773, 25], [566, 27, 1773, 27], [566, 33, 1773, 33], [567, 10, 1777, 10, "paddingTop"], [567, 20, 1777, 20], [567, 22, 1777, 22, "insets"], [567, 28, 1777, 28], [567, 29, 1777, 29, "top"], [567, 32, 1777, 32], [567, 35, 1777, 35], [567, 36, 1777, 36], [568, 10, 1781, 10, "paddingHorizontal"], [568, 27, 1781, 27], [568, 29, 1781, 29], [568, 31, 1781, 31], [569, 10, 1785, 10, "paddingBottom"], [569, 23, 1785, 23], [569, 25, 1785, 25], [569, 27, 1785, 27], [570, 10, 1789, 10, "borderBottomWidth"], [570, 27, 1789, 27], [570, 29, 1789, 29], [570, 30, 1789, 30], [571, 10, 1793, 10, "borderBottomColor"], [571, 27, 1793, 27], [571, 29, 1793, 29], [571, 38, 1793, 38], [572, 10, 1797, 10, "zIndex"], [572, 16, 1797, 16], [572, 18, 1797, 18], [573, 8, 1801, 8], [573, 9, 1801, 10], [574, 8, 1801, 10, "children"], [574, 16, 1801, 10], [574, 32, 1809, 8], [574, 36, 1809, 8, "_jsxDevRuntime"], [574, 50, 1809, 8], [574, 51, 1809, 8, "jsxDEV"], [574, 57, 1809, 8], [574, 59, 1809, 9, "_View"], [574, 64, 1809, 9], [574, 65, 1809, 9, "default"], [574, 72, 1809, 13], [575, 10, 1813, 10, "style"], [575, 15, 1813, 15], [575, 17, 1813, 17], [576, 12, 1817, 12, "flexDirection"], [576, 25, 1817, 25], [576, 27, 1817, 27], [576, 32, 1817, 32], [577, 12, 1821, 12, "alignItems"], [577, 22, 1821, 22], [577, 24, 1821, 24], [577, 32, 1821, 32], [578, 12, 1825, 12, "marginBottom"], [578, 24, 1825, 24], [578, 26, 1825, 26], [579, 10, 1829, 10], [579, 11, 1829, 12], [580, 10, 1829, 12, "children"], [580, 18, 1829, 12], [580, 34, 1837, 10], [580, 38, 1837, 10, "_jsxDevRuntime"], [580, 52, 1837, 10], [580, 53, 1837, 10, "jsxDEV"], [580, 59, 1837, 10], [580, 61, 1837, 11, "_TouchableOpacity"], [580, 78, 1837, 11], [580, 79, 1837, 11, "default"], [580, 86, 1837, 27], [581, 12, 1841, 12, "onPress"], [581, 19, 1841, 19], [581, 21, 1841, 21, "onPress"], [581, 22, 1841, 21], [581, 27, 1841, 27, "router"], [581, 45, 1841, 33], [581, 46, 1841, 34, "back"], [581, 50, 1841, 38], [581, 51, 1841, 39], [581, 52, 1841, 41], [582, 12, 1845, 12, "style"], [582, 17, 1845, 17], [582, 19, 1845, 19], [583, 14, 1845, 21, "marginRight"], [583, 25, 1845, 32], [583, 27, 1845, 34], [584, 12, 1845, 37], [584, 13, 1845, 39], [585, 12, 1845, 39, "children"], [585, 20, 1845, 39], [585, 35, 1853, 12], [585, 39, 1853, 12, "_jsxDevRuntime"], [585, 53, 1853, 12], [585, 54, 1853, 12, "jsxDEV"], [585, 60, 1853, 12], [585, 62, 1853, 13, "_lucideReactNative"], [585, 80, 1853, 13], [585, 81, 1853, 13, "ArrowLeft"], [585, 90, 1853, 22], [586, 14, 1853, 23, "size"], [586, 18, 1853, 27], [586, 20, 1853, 29], [586, 22, 1853, 32], [587, 14, 1853, 33, "color"], [587, 19, 1853, 38], [587, 21, 1853, 39], [588, 12, 1853, 48], [589, 14, 1853, 48, "fileName"], [589, 22, 1853, 48], [589, 24, 1853, 48, "_jsxFileName"], [589, 36, 1853, 48], [590, 14, 1853, 48, "lineNumber"], [590, 24, 1853, 48], [591, 14, 1853, 48, "columnNumber"], [591, 26, 1853, 48], [592, 12, 1853, 48], [592, 19, 1853, 50], [593, 10, 1853, 51], [594, 12, 1853, 51, "fileName"], [594, 20, 1853, 51], [594, 22, 1853, 51, "_jsxFileName"], [594, 34, 1853, 51], [595, 12, 1853, 51, "lineNumber"], [595, 22, 1853, 51], [596, 12, 1853, 51, "columnNumber"], [596, 24, 1853, 51], [597, 10, 1853, 51], [597, 17, 1857, 28], [597, 18, 1857, 29], [597, 33, 1861, 10], [597, 37, 1861, 10, "_jsxDevRuntime"], [597, 51, 1861, 10], [597, 52, 1861, 10, "jsxDEV"], [597, 58, 1861, 10], [597, 60, 1861, 11, "_Text"], [597, 65, 1861, 11], [597, 66, 1861, 11, "default"], [597, 73, 1861, 15], [598, 12, 1865, 12, "style"], [598, 17, 1865, 17], [598, 19, 1865, 19], [599, 14, 1869, 14, "fontSize"], [599, 22, 1869, 22], [599, 24, 1869, 24], [599, 26, 1869, 26], [600, 14, 1873, 14, "fontWeight"], [600, 24, 1873, 24], [600, 26, 1873, 26], [600, 32, 1873, 32], [601, 14, 1877, 14, "color"], [601, 19, 1877, 19], [601, 21, 1877, 21], [601, 30, 1877, 30], [602, 14, 1881, 14, "flex"], [602, 18, 1881, 18], [602, 20, 1881, 20], [603, 12, 1885, 12], [603, 13, 1885, 14], [604, 12, 1885, 14, "children"], [604, 20, 1885, 14], [604, 22, 1889, 11], [605, 10, 1897, 10], [606, 12, 1897, 10, "fileName"], [606, 20, 1897, 10], [606, 22, 1897, 10, "_jsxFileName"], [606, 34, 1897, 10], [607, 12, 1897, 10, "lineNumber"], [607, 22, 1897, 10], [608, 12, 1897, 10, "columnNumber"], [608, 24, 1897, 10], [609, 10, 1897, 10], [609, 17, 1897, 16], [609, 18, 1897, 17], [609, 33, 1905, 10], [609, 37, 1905, 10, "_jsxDevRuntime"], [609, 51, 1905, 10], [609, 52, 1905, 10, "jsxDEV"], [609, 58, 1905, 10], [609, 60, 1905, 11, "_Text"], [609, 65, 1905, 11], [609, 66, 1905, 11, "default"], [609, 73, 1905, 15], [610, 12, 1905, 16, "style"], [610, 17, 1905, 21], [610, 19, 1905, 23], [611, 14, 1905, 25, "fontSize"], [611, 22, 1905, 33], [611, 24, 1905, 35], [611, 26, 1905, 37], [612, 14, 1905, 39, "color"], [612, 19, 1905, 44], [612, 21, 1905, 46], [613, 12, 1905, 56], [613, 13, 1905, 58], [614, 12, 1905, 58, "children"], [614, 20, 1905, 58], [614, 22, 1909, 13], [614, 40, 1909, 31, "testingMode"], [614, 51, 1909, 42], [614, 54, 1909, 45], [614, 58, 1909, 49], [614, 61, 1909, 52], [614, 66, 1909, 57], [615, 10, 1909, 59], [616, 12, 1909, 59, "fileName"], [616, 20, 1909, 59], [616, 22, 1909, 59, "_jsxFileName"], [616, 34, 1909, 59], [617, 12, 1909, 59, "lineNumber"], [617, 22, 1909, 59], [618, 12, 1909, 59, "columnNumber"], [618, 24, 1909, 59], [619, 10, 1909, 59], [619, 17, 1913, 16], [619, 18, 1913, 17], [620, 8, 1913, 17], [621, 10, 1913, 17, "fileName"], [621, 18, 1913, 17], [621, 20, 1913, 17, "_jsxFileName"], [621, 32, 1913, 17], [622, 10, 1913, 17, "lineNumber"], [622, 20, 1913, 17], [623, 10, 1913, 17, "columnNumber"], [623, 22, 1913, 17], [624, 8, 1913, 17], [624, 15, 1917, 14], [624, 16, 1917, 15], [624, 31, 1925, 8], [624, 35, 1925, 8, "_jsxDevRuntime"], [624, 49, 1925, 8], [624, 50, 1925, 8, "jsxDEV"], [624, 56, 1925, 8], [624, 58, 1925, 9, "_View"], [624, 63, 1925, 9], [624, 64, 1925, 9, "default"], [624, 71, 1925, 13], [625, 10, 1929, 10, "style"], [625, 15, 1929, 15], [625, 17, 1929, 17], [626, 12, 1933, 12, "backgroundColor"], [626, 27, 1933, 27], [626, 29, 1933, 29], [626, 38, 1933, 38], [627, 12, 1937, 12, "borderRadius"], [627, 24, 1937, 24], [627, 26, 1937, 26], [627, 28, 1937, 28], [628, 12, 1941, 12, "padding"], [628, 19, 1941, 19], [628, 21, 1941, 21], [628, 23, 1941, 23], [629, 12, 1945, 12, "borderLeftWidth"], [629, 27, 1945, 27], [629, 29, 1945, 29], [629, 30, 1945, 30], [630, 12, 1949, 12, "borderLeftColor"], [630, 27, 1949, 27], [630, 29, 1949, 29], [631, 10, 1953, 10], [631, 11, 1953, 12], [632, 10, 1953, 12, "children"], [632, 18, 1953, 12], [632, 34, 1961, 10], [632, 38, 1961, 10, "_jsxDevRuntime"], [632, 52, 1961, 10], [632, 53, 1961, 10, "jsxDEV"], [632, 59, 1961, 10], [632, 61, 1961, 11, "_Text"], [632, 66, 1961, 11], [632, 67, 1961, 11, "default"], [632, 74, 1961, 15], [633, 12, 1965, 12, "style"], [633, 17, 1965, 17], [633, 19, 1965, 19], [634, 14, 1969, 14, "fontSize"], [634, 22, 1969, 22], [634, 24, 1969, 24], [634, 26, 1969, 26], [635, 14, 1973, 14, "color"], [635, 19, 1973, 19], [635, 21, 1973, 21], [635, 30, 1973, 30], [636, 14, 1977, 14, "fontWeight"], [636, 24, 1977, 24], [636, 26, 1977, 26], [636, 31, 1977, 31], [637, 14, 1981, 14, "marginBottom"], [637, 26, 1981, 26], [637, 28, 1981, 28], [638, 12, 1985, 12], [638, 13, 1985, 14], [639, 12, 1985, 14, "children"], [639, 20, 1985, 14], [639, 22, 1993, 13, "question"], [639, 30, 1993, 21], [639, 31, 1993, 22, "question"], [640, 10, 1993, 30], [641, 12, 1993, 30, "fileName"], [641, 20, 1993, 30], [641, 22, 1993, 30, "_jsxFileName"], [641, 34, 1993, 30], [642, 12, 1993, 30, "lineNumber"], [642, 22, 1993, 30], [643, 12, 1993, 30, "columnNumber"], [643, 24, 1993, 30], [644, 10, 1993, 30], [644, 17, 1997, 16], [644, 18, 1997, 17], [644, 33, 2001, 10], [644, 37, 2001, 10, "_jsxDevRuntime"], [644, 51, 2001, 10], [644, 52, 2001, 10, "jsxDEV"], [644, 58, 2001, 10], [644, 60, 2001, 11, "_View"], [644, 65, 2001, 11], [644, 66, 2001, 11, "default"], [644, 73, 2001, 15], [645, 12, 2005, 12, "style"], [645, 17, 2005, 17], [645, 19, 2005, 19], [646, 14, 2009, 14, "flexDirection"], [646, 27, 2009, 27], [646, 29, 2009, 29], [646, 34, 2009, 34], [647, 14, 2013, 14, "alignItems"], [647, 24, 2013, 24], [647, 26, 2013, 26], [647, 34, 2013, 34], [648, 14, 2017, 14, "marginBottom"], [648, 26, 2017, 26], [648, 28, 2017, 28], [649, 12, 2021, 12], [649, 13, 2021, 14], [650, 12, 2021, 14, "children"], [650, 20, 2021, 14], [650, 36, 2029, 12], [650, 40, 2029, 12, "_jsxDevRuntime"], [650, 54, 2029, 12], [650, 55, 2029, 12, "jsxDEV"], [650, 61, 2029, 12], [650, 63, 2029, 13, "_lucideReactNative"], [650, 81, 2029, 13], [650, 82, 2029, 13, "MapPin"], [650, 88, 2029, 19], [651, 14, 2029, 20, "size"], [651, 18, 2029, 24], [651, 20, 2029, 26], [651, 22, 2029, 29], [652, 14, 2029, 30, "color"], [652, 19, 2029, 35], [652, 21, 2029, 36], [653, 12, 2029, 45], [654, 14, 2029, 45, "fileName"], [654, 22, 2029, 45], [654, 24, 2029, 45, "_jsxFileName"], [654, 36, 2029, 45], [655, 14, 2029, 45, "lineNumber"], [655, 24, 2029, 45], [656, 14, 2029, 45, "columnNumber"], [656, 26, 2029, 45], [657, 12, 2029, 45], [657, 19, 2029, 47], [657, 20, 2029, 48], [657, 35, 2033, 12], [657, 39, 2033, 12, "_jsxDevRuntime"], [657, 53, 2033, 12], [657, 54, 2033, 12, "jsxDEV"], [657, 60, 2033, 12], [657, 62, 2033, 13, "_Text"], [657, 67, 2033, 13], [657, 68, 2033, 13, "default"], [657, 75, 2033, 17], [658, 14, 2037, 14, "style"], [658, 19, 2037, 19], [658, 21, 2037, 21], [659, 16, 2037, 23, "fontSize"], [659, 24, 2037, 31], [659, 26, 2037, 33], [659, 28, 2037, 35], [660, 16, 2037, 37, "color"], [660, 21, 2037, 42], [660, 23, 2037, 44], [660, 32, 2037, 53], [661, 16, 2037, 55, "marginLeft"], [661, 26, 2037, 65], [661, 28, 2037, 67], [661, 29, 2037, 68], [662, 16, 2037, 70, "flex"], [662, 20, 2037, 74], [662, 22, 2037, 76], [663, 14, 2037, 78], [663, 15, 2037, 80], [664, 14, 2037, 80, "children"], [664, 22, 2037, 80], [664, 24, 2045, 15, "question"], [664, 32, 2045, 23], [664, 33, 2045, 24, "location"], [665, 12, 2045, 32], [666, 14, 2045, 32, "fileName"], [666, 22, 2045, 32], [666, 24, 2045, 32, "_jsxFileName"], [666, 36, 2045, 32], [667, 14, 2045, 32, "lineNumber"], [667, 24, 2045, 32], [668, 14, 2045, 32, "columnNumber"], [668, 26, 2045, 32], [669, 12, 2045, 32], [669, 19, 2049, 18], [669, 20, 2049, 19], [670, 10, 2049, 19], [671, 12, 2049, 19, "fileName"], [671, 20, 2049, 19], [671, 22, 2049, 19, "_jsxFileName"], [671, 34, 2049, 19], [672, 12, 2049, 19, "lineNumber"], [672, 22, 2049, 19], [673, 12, 2049, 19, "columnNumber"], [673, 24, 2049, 19], [674, 10, 2049, 19], [674, 17, 2053, 16], [674, 18, 2053, 17], [674, 33, 2057, 10], [674, 37, 2057, 10, "_jsxDevRuntime"], [674, 51, 2057, 10], [674, 52, 2057, 10, "jsxDEV"], [674, 58, 2057, 10], [674, 60, 2057, 11, "_View"], [674, 65, 2057, 11], [674, 66, 2057, 11, "default"], [674, 73, 2057, 15], [675, 12, 2061, 12, "style"], [675, 17, 2061, 17], [675, 19, 2061, 19], [676, 14, 2065, 14, "flexDirection"], [676, 27, 2065, 27], [676, 29, 2065, 29], [676, 34, 2065, 34], [677, 14, 2069, 14, "justifyContent"], [677, 28, 2069, 28], [677, 30, 2069, 30], [677, 45, 2069, 45], [678, 14, 2073, 14, "alignItems"], [678, 24, 2073, 24], [678, 26, 2073, 26], [679, 12, 2077, 12], [679, 13, 2077, 14], [680, 12, 2077, 14, "children"], [680, 20, 2077, 14], [680, 36, 2085, 12], [680, 40, 2085, 12, "_jsxDevRuntime"], [680, 54, 2085, 12], [680, 55, 2085, 12, "jsxDEV"], [680, 61, 2085, 12], [680, 63, 2085, 13, "_View"], [680, 68, 2085, 13], [680, 69, 2085, 13, "default"], [680, 76, 2085, 17], [681, 14, 2085, 18, "style"], [681, 19, 2085, 23], [681, 21, 2085, 25], [682, 16, 2085, 27, "flexDirection"], [682, 29, 2085, 40], [682, 31, 2085, 42], [682, 36, 2085, 47], [683, 16, 2085, 49, "alignItems"], [683, 26, 2085, 59], [683, 28, 2085, 61], [684, 14, 2085, 70], [684, 15, 2085, 72], [685, 14, 2085, 72, "children"], [685, 22, 2085, 72], [685, 38, 2089, 14], [685, 42, 2089, 14, "_jsxDevRuntime"], [685, 56, 2089, 14], [685, 57, 2089, 14, "jsxDEV"], [685, 63, 2089, 14], [685, 65, 2089, 15, "_lucideReactNative"], [685, 83, 2089, 15], [685, 84, 2089, 15, "DollarSign"], [685, 94, 2089, 25], [686, 16, 2089, 26, "size"], [686, 20, 2089, 30], [686, 22, 2089, 32], [686, 24, 2089, 35], [687, 16, 2089, 36, "color"], [687, 21, 2089, 41], [687, 23, 2089, 42], [688, 14, 2089, 51], [689, 16, 2089, 51, "fileName"], [689, 24, 2089, 51], [689, 26, 2089, 51, "_jsxFileName"], [689, 38, 2089, 51], [690, 16, 2089, 51, "lineNumber"], [690, 26, 2089, 51], [691, 16, 2089, 51, "columnNumber"], [691, 28, 2089, 51], [692, 14, 2089, 51], [692, 21, 2089, 53], [692, 22, 2089, 54], [692, 37, 2093, 14], [692, 41, 2093, 14, "_jsxDevRuntime"], [692, 55, 2093, 14], [692, 56, 2093, 14, "jsxDEV"], [692, 62, 2093, 14], [692, 64, 2093, 15, "_Text"], [692, 69, 2093, 15], [692, 70, 2093, 15, "default"], [692, 77, 2093, 19], [693, 16, 2097, 16, "style"], [693, 21, 2097, 21], [693, 23, 2097, 23], [694, 18, 2101, 18, "fontSize"], [694, 26, 2101, 26], [694, 28, 2101, 28], [694, 30, 2101, 30], [695, 18, 2105, 18, "color"], [695, 23, 2105, 23], [695, 25, 2105, 25], [695, 34, 2105, 34], [696, 18, 2109, 18, "fontWeight"], [696, 28, 2109, 28], [696, 30, 2109, 30], [696, 35, 2109, 35], [697, 18, 2113, 18, "marginLeft"], [697, 28, 2113, 28], [697, 30, 2113, 30], [698, 16, 2117, 16], [698, 17, 2117, 18], [699, 16, 2117, 18, "children"], [699, 24, 2117, 18], [699, 26, 2125, 17], [699, 30, 2125, 21, "question"], [699, 38, 2125, 29], [699, 39, 2125, 30, "reward"], [699, 45, 2125, 36], [699, 46, 2125, 37, "toFixed"], [699, 53, 2125, 44], [699, 54, 2125, 45], [699, 55, 2125, 46], [699, 56, 2125, 47], [700, 14, 2125, 56], [701, 16, 2125, 56, "fileName"], [701, 24, 2125, 56], [701, 26, 2125, 56, "_jsxFileName"], [701, 38, 2125, 56], [702, 16, 2125, 56, "lineNumber"], [702, 26, 2125, 56], [703, 16, 2125, 56, "columnNumber"], [703, 28, 2125, 56], [704, 14, 2125, 56], [704, 21, 2129, 20], [704, 22, 2129, 21], [705, 12, 2129, 21], [706, 14, 2129, 21, "fileName"], [706, 22, 2129, 21], [706, 24, 2129, 21, "_jsxFileName"], [706, 36, 2129, 21], [707, 14, 2129, 21, "lineNumber"], [707, 24, 2129, 21], [708, 14, 2129, 21, "columnNumber"], [708, 26, 2129, 21], [709, 12, 2129, 21], [709, 19, 2133, 18], [709, 20, 2133, 19], [709, 35, 2137, 12], [709, 39, 2137, 12, "_jsxDevRuntime"], [709, 53, 2137, 12], [709, 54, 2137, 12, "jsxDEV"], [709, 60, 2137, 12], [709, 62, 2137, 13, "_View"], [709, 67, 2137, 13], [709, 68, 2137, 13, "default"], [709, 75, 2137, 17], [710, 14, 2137, 18, "style"], [710, 19, 2137, 23], [710, 21, 2137, 25], [711, 16, 2137, 27, "flexDirection"], [711, 29, 2137, 40], [711, 31, 2137, 42], [711, 36, 2137, 47], [712, 16, 2137, 49, "alignItems"], [712, 26, 2137, 59], [712, 28, 2137, 61], [713, 14, 2137, 70], [713, 15, 2137, 72], [714, 14, 2137, 72, "children"], [714, 22, 2137, 72], [714, 38, 2141, 14], [714, 42, 2141, 14, "_jsxDevRuntime"], [714, 56, 2141, 14], [714, 57, 2141, 14, "jsxDEV"], [714, 63, 2141, 14], [714, 65, 2141, 15, "_lucideReactNative"], [714, 83, 2141, 15], [714, 84, 2141, 15, "Clock"], [714, 89, 2141, 20], [715, 16, 2141, 21, "size"], [715, 20, 2141, 25], [715, 22, 2141, 27], [715, 24, 2141, 30], [716, 16, 2141, 31, "color"], [716, 21, 2141, 36], [716, 23, 2141, 37], [717, 14, 2141, 46], [718, 16, 2141, 46, "fileName"], [718, 24, 2141, 46], [718, 26, 2141, 46, "_jsxFileName"], [718, 38, 2141, 46], [719, 16, 2141, 46, "lineNumber"], [719, 26, 2141, 46], [720, 16, 2141, 46, "columnNumber"], [720, 28, 2141, 46], [721, 14, 2141, 46], [721, 21, 2141, 48], [721, 22, 2141, 49], [721, 37, 2145, 14], [721, 41, 2145, 14, "_jsxDevRuntime"], [721, 55, 2145, 14], [721, 56, 2145, 14, "jsxDEV"], [721, 62, 2145, 14], [721, 64, 2145, 15, "_Text"], [721, 69, 2145, 15], [721, 70, 2145, 15, "default"], [721, 77, 2145, 19], [722, 16, 2145, 20, "style"], [722, 21, 2145, 25], [722, 23, 2145, 27], [723, 18, 2145, 29, "fontSize"], [723, 26, 2145, 37], [723, 28, 2145, 39], [723, 30, 2145, 41], [724, 18, 2145, 43, "color"], [724, 23, 2145, 48], [724, 25, 2145, 50], [724, 34, 2145, 59], [725, 18, 2145, 61, "marginLeft"], [725, 28, 2145, 71], [725, 30, 2145, 73], [726, 16, 2145, 75], [726, 17, 2145, 77], [727, 16, 2145, 77, "children"], [727, 24, 2145, 77], [727, 26, 2149, 17, "question"], [727, 34, 2149, 25], [727, 35, 2149, 26, "postedAt"], [728, 14, 2149, 34], [729, 16, 2149, 34, "fileName"], [729, 24, 2149, 34], [729, 26, 2149, 34, "_jsxFileName"], [729, 38, 2149, 34], [730, 16, 2149, 34, "lineNumber"], [730, 26, 2149, 34], [731, 16, 2149, 34, "columnNumber"], [731, 28, 2149, 34], [732, 14, 2149, 34], [732, 21, 2153, 20], [732, 22, 2153, 21], [733, 12, 2153, 21], [734, 14, 2153, 21, "fileName"], [734, 22, 2153, 21], [734, 24, 2153, 21, "_jsxFileName"], [734, 36, 2153, 21], [735, 14, 2153, 21, "lineNumber"], [735, 24, 2153, 21], [736, 14, 2153, 21, "columnNumber"], [736, 26, 2153, 21], [737, 12, 2153, 21], [737, 19, 2157, 18], [737, 20, 2157, 19], [738, 10, 2157, 19], [739, 12, 2157, 19, "fileName"], [739, 20, 2157, 19], [739, 22, 2157, 19, "_jsxFileName"], [739, 34, 2157, 19], [740, 12, 2157, 19, "lineNumber"], [740, 22, 2157, 19], [741, 12, 2157, 19, "columnNumber"], [741, 24, 2157, 19], [742, 10, 2157, 19], [742, 17, 2161, 16], [742, 18, 2161, 17], [743, 8, 2161, 17], [744, 10, 2161, 17, "fileName"], [744, 18, 2161, 17], [744, 20, 2161, 17, "_jsxFileName"], [744, 32, 2161, 17], [745, 10, 2161, 17, "lineNumber"], [745, 20, 2161, 17], [746, 10, 2161, 17, "columnNumber"], [746, 22, 2161, 17], [747, 8, 2161, 17], [747, 15, 2165, 14], [747, 16, 2165, 15], [748, 6, 2165, 15], [749, 8, 2165, 15, "fileName"], [749, 16, 2165, 15], [749, 18, 2165, 15, "_jsxFileName"], [749, 30, 2165, 15], [750, 8, 2165, 15, "lineNumber"], [750, 18, 2165, 15], [751, 8, 2165, 15, "columnNumber"], [751, 20, 2165, 15], [752, 6, 2165, 15], [752, 13, 2169, 12], [752, 14, 2169, 13], [752, 29, 2173, 6], [752, 33, 2173, 6, "_jsxDevRuntime"], [752, 47, 2173, 6], [752, 48, 2173, 6, "jsxDEV"], [752, 54, 2173, 6], [752, 56, 2173, 7, "_KeyboardAvoidingAnimatedView"], [752, 85, 2173, 7], [752, 86, 2173, 7, "default"], [752, 93, 2173, 35], [753, 8, 2173, 36, "style"], [753, 13, 2173, 41], [753, 15, 2173, 43], [754, 10, 2173, 45, "flex"], [754, 14, 2173, 49], [754, 16, 2173, 51], [755, 8, 2173, 53], [755, 9, 2173, 55], [756, 8, 2173, 56, "behavior"], [756, 16, 2173, 64], [756, 18, 2173, 65], [756, 27, 2173, 74], [757, 8, 2173, 74, "children"], [757, 16, 2173, 74], [757, 32, 2177, 8], [757, 36, 2177, 8, "_jsxDevRuntime"], [757, 50, 2177, 8], [757, 51, 2177, 8, "jsxDEV"], [757, 57, 2177, 8], [757, 59, 2177, 9, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [757, 70, 2177, 9], [757, 71, 2177, 9, "default"], [757, 78, 2177, 19], [758, 10, 2181, 10, "style"], [758, 15, 2181, 15], [758, 17, 2181, 17], [759, 12, 2181, 19, "flex"], [759, 16, 2181, 23], [759, 18, 2181, 25], [760, 10, 2181, 27], [760, 11, 2181, 29], [761, 10, 2185, 10, "contentContainerStyle"], [761, 31, 2185, 31], [761, 33, 2185, 33], [762, 12, 2185, 35, "paddingBottom"], [762, 25, 2185, 48], [762, 27, 2185, 50, "insets"], [762, 33, 2185, 56], [762, 34, 2185, 57, "bottom"], [762, 40, 2185, 63], [762, 43, 2185, 66], [763, 10, 2185, 70], [763, 11, 2185, 72], [764, 10, 2189, 10, "showsVerticalScrollIndicator"], [764, 38, 2189, 38], [764, 40, 2189, 40], [764, 45, 2189, 46], [765, 10, 2189, 46, "children"], [765, 18, 2189, 46], [765, 33, 2197, 10], [765, 37, 2197, 10, "_jsxDevRuntime"], [765, 51, 2197, 10], [765, 52, 2197, 10, "jsxDEV"], [765, 58, 2197, 10], [765, 60, 2197, 11, "_View"], [765, 65, 2197, 11], [765, 66, 2197, 11, "default"], [765, 73, 2197, 15], [766, 12, 2197, 16, "style"], [766, 17, 2197, 21], [766, 19, 2197, 23], [767, 14, 2197, 25, "padding"], [767, 21, 2197, 32], [767, 23, 2197, 34], [768, 12, 2197, 37], [768, 13, 2197, 39], [769, 12, 2197, 39, "children"], [769, 20, 2197, 39], [769, 36, 2205, 12], [769, 40, 2205, 12, "_jsxDevRuntime"], [769, 54, 2205, 12], [769, 55, 2205, 12, "jsxDEV"], [769, 61, 2205, 12], [769, 63, 2205, 13, "LocationStatus"], [769, 77, 2205, 27], [770, 14, 2205, 27, "fileName"], [770, 22, 2205, 27], [770, 24, 2205, 27, "_jsxFileName"], [770, 36, 2205, 27], [771, 14, 2205, 27, "lineNumber"], [771, 24, 2205, 27], [772, 14, 2205, 27, "columnNumber"], [772, 26, 2205, 27], [773, 12, 2205, 27], [773, 19, 2205, 29], [773, 20, 2205, 30], [773, 35, 2211, 12], [773, 39, 2211, 12, "_jsxDevRuntime"], [773, 53, 2211, 12], [773, 54, 2211, 12, "jsxDEV"], [773, 60, 2211, 12], [773, 62, 2211, 13, "_View"], [773, 67, 2211, 13], [773, 68, 2211, 13, "default"], [773, 75, 2211, 17], [774, 14, 2211, 18, "style"], [774, 19, 2211, 23], [774, 21, 2211, 25], [775, 16, 2211, 27, "marginBottom"], [775, 28, 2211, 39], [775, 30, 2211, 41], [776, 14, 2211, 44], [776, 15, 2211, 46], [777, 14, 2211, 46, "children"], [777, 22, 2211, 46], [777, 38, 2213, 14], [777, 42, 2213, 14, "_jsxDevRuntime"], [777, 56, 2213, 14], [777, 57, 2213, 14, "jsxDEV"], [777, 63, 2213, 14], [777, 65, 2213, 15, "_View"], [777, 70, 2213, 15], [777, 71, 2213, 15, "default"], [777, 78, 2213, 19], [778, 16, 2215, 16, "style"], [778, 21, 2215, 21], [778, 23, 2215, 23], [779, 18, 2217, 18, "flexDirection"], [779, 31, 2217, 31], [779, 33, 2217, 33], [779, 38, 2217, 38], [780, 18, 2219, 18, "alignItems"], [780, 28, 2219, 28], [780, 30, 2219, 30], [780, 38, 2219, 38], [781, 18, 2221, 18, "marginBottom"], [781, 30, 2221, 30], [781, 32, 2221, 32], [782, 16, 2223, 16], [782, 17, 2223, 18], [783, 16, 2223, 18, "children"], [783, 24, 2223, 18], [783, 40, 2227, 16], [783, 44, 2227, 16, "_jsxDevRuntime"], [783, 58, 2227, 16], [783, 59, 2227, 16, "jsxDEV"], [783, 65, 2227, 16], [783, 67, 2227, 17, "_View"], [783, 72, 2227, 17], [783, 73, 2227, 17, "default"], [783, 80, 2227, 21], [784, 18, 2229, 18, "style"], [784, 23, 2229, 23], [784, 25, 2229, 25], [785, 20, 2231, 20, "width"], [785, 25, 2231, 25], [785, 27, 2231, 27], [785, 29, 2231, 29], [786, 20, 2233, 20, "height"], [786, 26, 2233, 26], [786, 28, 2233, 28], [786, 30, 2233, 30], [787, 20, 2235, 20, "borderRadius"], [787, 32, 2235, 32], [787, 34, 2235, 34], [787, 36, 2235, 36], [788, 20, 2237, 20, "backgroundColor"], [788, 35, 2237, 35], [788, 37, 2237, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [788, 53, 2237, 53], [788, 56, 2239, 24], [788, 65, 2239, 33], [788, 68, 2241, 24, "locationStatus"], [788, 82, 2241, 38], [788, 87, 2241, 43], [788, 97, 2241, 53], [788, 101, 2241, 57, "testingMode"], [788, 112, 2241, 68], [788, 115, 2243, 26], [788, 124, 2243, 35], [788, 127, 2245, 26], [788, 136, 2245, 35], [789, 20, 2247, 20, "alignItems"], [789, 30, 2247, 30], [789, 32, 2247, 32], [789, 40, 2247, 40], [790, 20, 2249, 20, "justifyContent"], [790, 34, 2249, 34], [790, 36, 2249, 36], [790, 44, 2249, 44], [791, 20, 2251, 20, "marginRight"], [791, 31, 2251, 31], [791, 33, 2251, 33], [792, 18, 2253, 18], [792, 19, 2253, 20], [793, 18, 2253, 20, "children"], [793, 26, 2253, 20], [793, 28, 2257, 19, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [793, 44, 2257, 35], [793, 60, 2259, 20], [793, 64, 2259, 20, "_jsxDevRuntime"], [793, 78, 2259, 20], [793, 79, 2259, 20, "jsxDEV"], [793, 85, 2259, 20], [793, 87, 2259, 21, "_lucideReactNative"], [793, 105, 2259, 21], [793, 106, 2259, 21, "CheckCircle2"], [793, 118, 2259, 33], [794, 20, 2259, 34, "size"], [794, 24, 2259, 38], [794, 26, 2259, 40], [794, 28, 2259, 43], [795, 20, 2259, 44, "color"], [795, 25, 2259, 49], [795, 27, 2259, 50], [796, 18, 2259, 56], [797, 20, 2259, 56, "fileName"], [797, 28, 2259, 56], [797, 30, 2259, 56, "_jsxFileName"], [797, 42, 2259, 56], [798, 20, 2259, 56, "lineNumber"], [798, 30, 2259, 56], [799, 20, 2259, 56, "columnNumber"], [799, 32, 2259, 56], [800, 18, 2259, 56], [800, 25, 2259, 58], [800, 26, 2259, 59], [800, 42, 2263, 20], [800, 46, 2263, 20, "_jsxDevRuntime"], [800, 60, 2263, 20], [800, 61, 2263, 20, "jsxDEV"], [800, 67, 2263, 20], [800, 69, 2263, 21, "_Text"], [800, 74, 2263, 21], [800, 75, 2263, 21, "default"], [800, 82, 2263, 25], [801, 20, 2263, 26, "style"], [801, 25, 2263, 31], [801, 27, 2263, 33], [802, 22, 2263, 35, "color"], [802, 27, 2263, 40], [802, 29, 2263, 42], [802, 35, 2263, 48], [803, 22, 2263, 50, "fontSize"], [803, 30, 2263, 58], [803, 32, 2263, 60], [803, 34, 2263, 62], [804, 22, 2263, 64, "fontWeight"], [804, 32, 2263, 74], [804, 34, 2263, 76], [805, 20, 2263, 82], [805, 21, 2263, 84], [806, 20, 2263, 84, "children"], [806, 28, 2263, 84], [806, 30, 2263, 85], [807, 18, 2263, 86], [808, 20, 2263, 86, "fileName"], [808, 28, 2263, 86], [808, 30, 2263, 86, "_jsxFileName"], [808, 42, 2263, 86], [809, 20, 2263, 86, "lineNumber"], [809, 30, 2263, 86], [810, 20, 2263, 86, "columnNumber"], [810, 32, 2263, 86], [811, 18, 2263, 86], [811, 25, 2263, 92], [812, 16, 2265, 19], [813, 18, 2265, 19, "fileName"], [813, 26, 2265, 19], [813, 28, 2265, 19, "_jsxFileName"], [813, 40, 2265, 19], [814, 18, 2265, 19, "lineNumber"], [814, 28, 2265, 19], [815, 18, 2265, 19, "columnNumber"], [815, 30, 2265, 19], [816, 16, 2265, 19], [816, 23, 2267, 22], [816, 24, 2267, 23], [816, 39, 2269, 16], [816, 43, 2269, 16, "_jsxDevRuntime"], [816, 57, 2269, 16], [816, 58, 2269, 16, "jsxDEV"], [816, 64, 2269, 16], [816, 66, 2269, 17, "_Text"], [816, 71, 2269, 17], [816, 72, 2269, 17, "default"], [816, 79, 2269, 21], [817, 18, 2271, 18, "style"], [817, 23, 2271, 23], [817, 25, 2271, 25], [818, 20, 2273, 20, "fontSize"], [818, 28, 2273, 28], [818, 30, 2273, 30], [818, 32, 2273, 32], [819, 20, 2275, 20, "fontWeight"], [819, 30, 2275, 30], [819, 32, 2275, 32], [819, 37, 2275, 37], [820, 20, 2277, 20, "color"], [820, 25, 2277, 25], [820, 27, 2277, 27], [821, 18, 2279, 18], [821, 19, 2279, 20], [822, 18, 2279, 20, "children"], [822, 26, 2279, 20], [822, 28, 2281, 17], [823, 16, 2285, 16], [824, 18, 2285, 16, "fileName"], [824, 26, 2285, 16], [824, 28, 2285, 16, "_jsxFileName"], [824, 40, 2285, 16], [825, 18, 2285, 16, "lineNumber"], [825, 28, 2285, 16], [826, 18, 2285, 16, "columnNumber"], [826, 30, 2285, 16], [827, 16, 2285, 16], [827, 23, 2285, 22], [827, 24, 2285, 23], [828, 14, 2285, 23], [829, 16, 2285, 23, "fileName"], [829, 24, 2285, 23], [829, 26, 2285, 23, "_jsxFileName"], [829, 38, 2285, 23], [830, 16, 2285, 23, "lineNumber"], [830, 26, 2285, 23], [831, 16, 2285, 23, "columnNumber"], [831, 28, 2285, 23], [832, 14, 2285, 23], [832, 21, 2287, 20], [832, 22, 2287, 21], [832, 37, 2289, 14], [832, 41, 2289, 14, "_jsxDevRuntime"], [832, 55, 2289, 14], [832, 56, 2289, 14, "jsxDEV"], [832, 62, 2289, 14], [832, 64, 2289, 15, "_Text"], [832, 69, 2289, 15], [832, 70, 2289, 15, "default"], [832, 77, 2289, 19], [833, 16, 2291, 16, "style"], [833, 21, 2291, 21], [833, 23, 2291, 23], [834, 18, 2293, 18, "fontSize"], [834, 26, 2293, 26], [834, 28, 2293, 28], [834, 30, 2293, 30], [835, 18, 2295, 18, "color"], [835, 23, 2295, 23], [835, 25, 2295, 25], [835, 34, 2295, 34], [836, 18, 2297, 18, "marginBottom"], [836, 30, 2297, 30], [836, 32, 2297, 32], [836, 34, 2297, 34], [837, 18, 2299, 18, "lineHeight"], [837, 28, 2299, 28], [837, 30, 2299, 30], [838, 16, 2301, 16], [838, 17, 2301, 18], [839, 16, 2301, 18, "children"], [839, 24, 2301, 18], [839, 26, 2305, 17], [840, 14, 2305, 148], [841, 16, 2305, 148, "fileName"], [841, 24, 2305, 148], [841, 26, 2305, 148, "_jsxFileName"], [841, 38, 2305, 148], [842, 16, 2305, 148, "lineNumber"], [842, 26, 2305, 148], [843, 16, 2305, 148, "columnNumber"], [843, 28, 2305, 148], [844, 14, 2305, 148], [844, 21, 2307, 20], [844, 22, 2307, 21], [844, 24, 2309, 15, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [844, 40, 2309, 31], [844, 56, 2311, 16], [844, 60, 2311, 16, "_jsxDevRuntime"], [844, 74, 2311, 16], [844, 75, 2311, 16, "jsxDEV"], [844, 81, 2311, 16], [844, 83, 2311, 17, "_View"], [844, 88, 2311, 17], [844, 89, 2311, 17, "default"], [844, 96, 2311, 21], [845, 16, 2313, 18, "style"], [845, 21, 2313, 23], [845, 23, 2313, 25], [846, 18, 2315, 20, "backgroundColor"], [846, 33, 2315, 35], [846, 35, 2315, 37], [846, 41, 2315, 43], [847, 18, 2317, 20, "borderRadius"], [847, 30, 2317, 32], [847, 32, 2317, 34], [847, 34, 2317, 36], [848, 18, 2319, 20, "overflow"], [848, 26, 2319, 28], [848, 28, 2319, 30], [848, 36, 2319, 38], [849, 18, 2321, 20, "borderWidth"], [849, 29, 2321, 31], [849, 31, 2321, 33], [849, 32, 2321, 34], [850, 18, 2323, 20, "borderColor"], [850, 29, 2323, 31], [850, 31, 2323, 33], [850, 40, 2323, 42], [851, 18, 2325, 20], [851, 21, 2325, 23, "Platform"], [851, 38, 2325, 31], [851, 39, 2325, 32, "select"], [851, 45, 2325, 38], [851, 46, 2325, 39], [852, 20, 2327, 22, "ios"], [852, 23, 2327, 25], [852, 25, 2327, 27], [853, 22, 2329, 24, "shadowColor"], [853, 33, 2329, 35], [853, 35, 2329, 37], [853, 41, 2329, 43], [854, 22, 2331, 24, "shadowOffset"], [854, 34, 2331, 36], [854, 36, 2331, 38], [855, 24, 2331, 40, "width"], [855, 29, 2331, 45], [855, 31, 2331, 47], [855, 32, 2331, 48], [856, 24, 2331, 50, "height"], [856, 30, 2331, 56], [856, 32, 2331, 58], [857, 22, 2331, 60], [857, 23, 2331, 61], [858, 22, 2333, 24, "shadowOpacity"], [858, 35, 2333, 37], [858, 37, 2333, 39], [858, 41, 2333, 43], [859, 22, 2335, 24, "shadowRadius"], [859, 34, 2335, 36], [859, 36, 2335, 38], [860, 20, 2337, 22], [860, 21, 2337, 23], [861, 20, 2339, 22, "android"], [861, 27, 2339, 29], [861, 29, 2339, 31], [862, 22, 2341, 24, "elevation"], [862, 31, 2341, 33], [862, 33, 2341, 35], [863, 20, 2343, 22], [863, 21, 2343, 23], [864, 20, 2345, 22, "web"], [864, 23, 2345, 25], [864, 25, 2345, 27], [865, 22, 2347, 24, "boxShadow"], [865, 31, 2347, 33], [865, 33, 2347, 35], [866, 20, 2349, 22], [867, 18, 2351, 20], [867, 19, 2351, 21], [868, 16, 2353, 18], [868, 17, 2353, 20], [869, 16, 2353, 20, "children"], [869, 24, 2353, 20], [869, 40, 2357, 18], [869, 44, 2357, 18, "_jsxDevRuntime"], [869, 58, 2357, 18], [869, 59, 2357, 18, "jsxDEV"], [869, 65, 2357, 18], [869, 67, 2357, 19, "_View"], [869, 72, 2357, 19], [869, 73, 2357, 19, "default"], [869, 80, 2357, 23], [870, 18, 2357, 24, "style"], [870, 23, 2357, 29], [870, 25, 2357, 31], [871, 20, 2357, 33, "position"], [871, 28, 2357, 41], [871, 30, 2357, 43], [872, 18, 2357, 54], [872, 19, 2357, 56], [873, 18, 2357, 56, "children"], [873, 26, 2357, 56], [873, 42, 2359, 20], [873, 46, 2359, 20, "_jsxDevRuntime"], [873, 60, 2359, 20], [873, 61, 2359, 20, "jsxDEV"], [873, 67, 2359, 20], [873, 69, 2359, 21, "_Image"], [873, 75, 2359, 21], [873, 76, 2359, 21, "default"], [873, 83, 2359, 26], [874, 20, 2361, 22, "source"], [874, 26, 2361, 28], [874, 28, 2361, 30], [875, 22, 2361, 32, "uri"], [875, 25, 2361, 35], [875, 27, 2361, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [876, 20, 2361, 54], [876, 21, 2361, 56], [877, 20, 2363, 22, "style"], [877, 25, 2363, 27], [877, 27, 2363, 29], [878, 22, 2364, 24, "width"], [878, 27, 2364, 29], [878, 29, 2364, 31], [878, 35, 2364, 37], [879, 22, 2365, 24, "height"], [879, 28, 2365, 30], [879, 30, 2365, 32], [879, 33, 2365, 35], [880, 22, 2365, 37], [881, 22, 2366, 24, "backgroundColor"], [881, 37, 2366, 39], [881, 39, 2366, 41], [881, 48, 2366, 50], [882, 22, 2367, 24, "borderRadius"], [882, 34, 2367, 36], [882, 36, 2367, 38], [882, 38, 2367, 40], [882, 39, 2367, 42], [883, 20, 2368, 22], [883, 21, 2368, 24], [884, 20, 2370, 22, "resizeMode"], [884, 30, 2370, 32], [884, 32, 2370, 33], [884, 41, 2370, 42], [884, 42, 2370, 43], [885, 20, 2370, 43], [887, 20, 2372, 22, "onError"], [887, 27, 2372, 29], [887, 29, 2372, 32, "e"], [887, 30, 2372, 33], [887, 34, 2372, 38], [888, 22, 2374, 24, "console"], [888, 29, 2374, 31], [888, 30, 2374, 32, "error"], [888, 35, 2374, 37], [888, 36, 2374, 38], [888, 73, 2374, 75], [888, 75, 2374, 77], [889, 24, 2376, 26, "error"], [889, 29, 2376, 31], [889, 31, 2376, 33, "e"], [889, 32, 2376, 34], [889, 33, 2376, 35, "nativeEvent"], [889, 44, 2376, 46], [889, 46, 2376, 48, "error"], [889, 51, 2376, 53], [890, 24, 2378, 26, "uri"], [890, 27, 2378, 29], [890, 29, 2378, 31, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [890, 45, 2378, 47], [891, 24, 2380, 26, "cameraResult"], [891, 36, 2380, 38], [891, 38, 2380, 40, "JSON"], [891, 42, 2380, 44], [891, 43, 2380, 45, "stringify"], [891, 52, 2380, 54], [891, 53, 2380, 55, "cameraResult"], [891, 65, 2380, 67], [891, 67, 2380, 69], [891, 71, 2380, 73], [891, 73, 2380, 75], [891, 74, 2380, 76], [892, 22, 2382, 24], [892, 23, 2382, 25], [892, 24, 2382, 26], [893, 20, 2384, 22], [893, 21, 2384, 24], [894, 20, 2386, 22, "onLoad"], [894, 26, 2386, 28], [894, 28, 2386, 30, "onLoad"], [894, 29, 2386, 30], [894, 34, 2386, 36], [895, 22, 2388, 24, "console"], [895, 29, 2388, 31], [895, 30, 2388, 32, "log"], [895, 33, 2388, 35], [895, 34, 2388, 36], [895, 78, 2388, 80], [895, 80, 2388, 82, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [895, 96, 2388, 98], [895, 97, 2388, 99], [896, 20, 2390, 22], [896, 21, 2390, 24], [897, 20, 2392, 22, "onLoadStart"], [897, 31, 2392, 33], [897, 33, 2392, 35, "onLoadStart"], [897, 34, 2392, 35], [897, 39, 2392, 41], [898, 22, 2394, 24, "console"], [898, 29, 2394, 31], [898, 30, 2394, 32, "log"], [898, 33, 2394, 35], [898, 34, 2394, 36], [898, 69, 2394, 71], [898, 71, 2394, 73, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [898, 87, 2394, 89], [898, 88, 2394, 90], [899, 22, 2395, 24, "console"], [899, 29, 2395, 31], [899, 30, 2395, 32, "log"], [899, 33, 2395, 35], [899, 34, 2395, 36], [899, 71, 2395, 73], [899, 73, 2395, 75], [900, 24, 2396, 26, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [900, 40, 2396, 42], [901, 24, 2397, 26, "isBlob"], [901, 30, 2397, 32], [901, 32, 2397, 34, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [901, 48, 2397, 50], [901, 50, 2397, 52, "startsWith"], [901, 60, 2397, 62], [901, 61, 2397, 63], [901, 68, 2397, 70], [901, 69, 2397, 71], [902, 24, 2398, 26, "isDataUri"], [902, 33, 2398, 35], [902, 35, 2398, 37, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [902, 51, 2398, 53], [902, 53, 2398, 55, "startsWith"], [902, 63, 2398, 65], [902, 64, 2398, 66], [902, 71, 2398, 73], [902, 72, 2398, 74], [903, 24, 2399, 26, "length"], [903, 30, 2399, 32], [903, 32, 2399, 34, "captured<PERSON><PERSON><PERSON><PERSON><PERSON>"], [903, 48, 2399, 50], [903, 50, 2399, 52, "length"], [904, 22, 2400, 24], [904, 23, 2400, 25], [904, 24, 2400, 26], [905, 20, 2402, 22], [905, 21, 2402, 24], [906, 20, 2404, 22, "onLoadEnd"], [906, 29, 2404, 31], [906, 31, 2404, 33, "onLoadEnd"], [906, 32, 2404, 33], [906, 37, 2404, 39], [907, 22, 2406, 24, "console"], [907, 29, 2406, 31], [907, 30, 2406, 32, "log"], [907, 33, 2406, 35], [907, 34, 2406, 36], [907, 70, 2406, 72], [907, 71, 2406, 73], [908, 20, 2408, 22], [909, 18, 2408, 24], [910, 20, 2408, 24, "fileName"], [910, 28, 2408, 24], [910, 30, 2408, 24, "_jsxFileName"], [910, 42, 2408, 24], [911, 20, 2408, 24, "lineNumber"], [911, 30, 2408, 24], [912, 20, 2408, 24, "columnNumber"], [912, 32, 2408, 24], [913, 18, 2408, 24], [913, 25, 2410, 21], [913, 26, 2410, 22], [913, 41, 2412, 20], [913, 45, 2412, 20, "_jsxDevRuntime"], [913, 59, 2412, 20], [913, 60, 2412, 20, "jsxDEV"], [913, 66, 2412, 20], [913, 68, 2412, 21, "_View"], [913, 73, 2412, 21], [913, 74, 2412, 21, "default"], [913, 81, 2412, 25], [914, 20, 2414, 22, "style"], [914, 25, 2414, 27], [914, 27, 2414, 29], [915, 22, 2416, 24, "position"], [915, 30, 2416, 32], [915, 32, 2416, 34], [915, 42, 2416, 44], [916, 22, 2418, 24, "top"], [916, 25, 2418, 27], [916, 27, 2418, 29], [916, 29, 2418, 31], [917, 22, 2420, 24, "right"], [917, 27, 2420, 29], [917, 29, 2420, 31], [917, 31, 2420, 33], [918, 22, 2422, 24, "backgroundColor"], [918, 37, 2422, 39], [918, 39, 2422, 41], [918, 65, 2422, 67], [919, 22, 2424, 24, "paddingHorizontal"], [919, 39, 2424, 41], [919, 41, 2424, 43], [919, 43, 2424, 45], [920, 22, 2426, 24, "paddingVertical"], [920, 37, 2426, 39], [920, 39, 2426, 41], [920, 40, 2426, 42], [921, 22, 2428, 24, "borderRadius"], [921, 34, 2428, 36], [921, 36, 2428, 38], [921, 38, 2428, 40], [922, 22, 2430, 24, "flexDirection"], [922, 35, 2430, 37], [922, 37, 2430, 39], [922, 42, 2430, 44], [923, 22, 2432, 24, "alignItems"], [923, 32, 2432, 34], [923, 34, 2432, 36], [924, 20, 2434, 22], [924, 21, 2434, 24], [925, 20, 2434, 24, "children"], [925, 28, 2434, 24], [925, 44, 2438, 22], [925, 48, 2438, 22, "_jsxDevRuntime"], [925, 62, 2438, 22], [925, 63, 2438, 22, "jsxDEV"], [925, 69, 2438, 22], [925, 71, 2438, 23, "_lucideReactNative"], [925, 89, 2438, 23], [925, 90, 2438, 23, "Shield"], [925, 96, 2438, 29], [926, 22, 2438, 30, "size"], [926, 26, 2438, 34], [926, 28, 2438, 36], [926, 30, 2438, 39], [927, 22, 2438, 40, "color"], [927, 27, 2438, 45], [927, 29, 2438, 46], [928, 20, 2438, 52], [929, 22, 2438, 52, "fileName"], [929, 30, 2438, 52], [929, 32, 2438, 52, "_jsxFileName"], [929, 44, 2438, 52], [930, 22, 2438, 52, "lineNumber"], [930, 32, 2438, 52], [931, 22, 2438, 52, "columnNumber"], [931, 34, 2438, 52], [932, 20, 2438, 52], [932, 27, 2438, 54], [932, 28, 2438, 55], [932, 43, 2440, 22], [932, 47, 2440, 22, "_jsxDevRuntime"], [932, 61, 2440, 22], [932, 62, 2440, 22, "jsxDEV"], [932, 68, 2440, 22], [932, 70, 2440, 23, "_Text"], [932, 75, 2440, 23], [932, 76, 2440, 23, "default"], [932, 83, 2440, 27], [933, 22, 2440, 28, "style"], [933, 27, 2440, 33], [933, 29, 2440, 35], [934, 24, 2440, 37, "color"], [934, 29, 2440, 42], [934, 31, 2440, 44], [934, 37, 2440, 50], [935, 24, 2440, 52, "fontSize"], [935, 32, 2440, 60], [935, 34, 2440, 62], [935, 36, 2440, 64], [936, 24, 2440, 66, "fontWeight"], [936, 34, 2440, 76], [936, 36, 2440, 78], [936, 41, 2440, 83], [937, 24, 2440, 85, "marginLeft"], [937, 34, 2440, 95], [937, 36, 2440, 97], [938, 22, 2440, 99], [938, 23, 2440, 101], [939, 22, 2440, 101, "children"], [939, 30, 2440, 101], [939, 32, 2440, 102], [940, 20, 2444, 22], [941, 22, 2444, 22, "fileName"], [941, 30, 2444, 22], [941, 32, 2444, 22, "_jsxFileName"], [941, 44, 2444, 22], [942, 22, 2444, 22, "lineNumber"], [942, 32, 2444, 22], [943, 22, 2444, 22, "columnNumber"], [943, 34, 2444, 22], [944, 20, 2444, 22], [944, 27, 2444, 28], [944, 28, 2444, 29], [945, 18, 2444, 29], [946, 20, 2444, 29, "fileName"], [946, 28, 2444, 29], [946, 30, 2444, 29, "_jsxFileName"], [946, 42, 2444, 29], [947, 20, 2444, 29, "lineNumber"], [947, 30, 2444, 29], [948, 20, 2444, 29, "columnNumber"], [948, 32, 2444, 29], [949, 18, 2444, 29], [949, 25, 2446, 26], [949, 26, 2446, 27], [950, 16, 2446, 27], [951, 18, 2446, 27, "fileName"], [951, 26, 2446, 27], [951, 28, 2446, 27, "_jsxFileName"], [951, 40, 2446, 27], [952, 18, 2446, 27, "lineNumber"], [952, 28, 2446, 27], [953, 18, 2446, 27, "columnNumber"], [953, 30, 2446, 27], [954, 16, 2446, 27], [954, 23, 2448, 24], [954, 24, 2448, 25], [954, 39, 2450, 18], [954, 43, 2450, 18, "_jsxDevRuntime"], [954, 57, 2450, 18], [954, 58, 2450, 18, "jsxDEV"], [954, 64, 2450, 18], [954, 66, 2450, 19, "_View"], [954, 71, 2450, 19], [954, 72, 2450, 19, "default"], [954, 79, 2450, 23], [955, 18, 2450, 24, "style"], [955, 23, 2450, 29], [955, 25, 2450, 31], [956, 20, 2450, 33, "padding"], [956, 27, 2450, 40], [956, 29, 2450, 42], [957, 18, 2450, 45], [957, 19, 2450, 47], [958, 18, 2450, 47, "children"], [958, 26, 2450, 47], [958, 42, 2452, 20], [958, 46, 2452, 20, "_jsxDevRuntime"], [958, 60, 2452, 20], [958, 61, 2452, 20, "jsxDEV"], [958, 67, 2452, 20], [958, 69, 2452, 21, "_View"], [958, 74, 2452, 21], [958, 75, 2452, 21, "default"], [958, 82, 2452, 25], [959, 20, 2454, 22, "style"], [959, 25, 2454, 27], [959, 27, 2454, 29], [960, 22, 2456, 24, "flexDirection"], [960, 35, 2456, 37], [960, 37, 2456, 39], [960, 42, 2456, 44], [961, 22, 2458, 24, "alignItems"], [961, 32, 2458, 34], [961, 34, 2458, 36], [961, 42, 2458, 44], [962, 22, 2460, 24, "marginBottom"], [962, 34, 2460, 36], [962, 36, 2460, 38], [963, 20, 2462, 22], [963, 21, 2462, 24], [964, 20, 2462, 24, "children"], [964, 28, 2462, 24], [964, 44, 2466, 22], [964, 48, 2466, 22, "_jsxDevRuntime"], [964, 62, 2466, 22], [964, 63, 2466, 22, "jsxDEV"], [964, 69, 2466, 22], [964, 71, 2466, 23, "_View"], [964, 76, 2466, 23], [964, 77, 2466, 23, "default"], [964, 84, 2466, 27], [965, 22, 2468, 24, "style"], [965, 27, 2468, 29], [965, 29, 2468, 31], [966, 24, 2470, 26, "width"], [966, 29, 2470, 31], [966, 31, 2470, 33], [966, 33, 2470, 35], [967, 24, 2472, 26, "height"], [967, 30, 2472, 32], [967, 32, 2472, 34], [967, 34, 2472, 36], [968, 24, 2474, 26, "borderRadius"], [968, 36, 2474, 38], [968, 38, 2474, 40], [968, 40, 2474, 42], [969, 24, 2476, 26, "backgroundColor"], [969, 39, 2476, 41], [969, 41, 2476, 43], [969, 50, 2476, 52], [970, 24, 2478, 26, "alignItems"], [970, 34, 2478, 36], [970, 36, 2478, 38], [970, 44, 2478, 46], [971, 24, 2480, 26, "justifyContent"], [971, 38, 2480, 40], [971, 40, 2480, 42], [972, 22, 2482, 24], [972, 23, 2482, 26], [973, 22, 2482, 26, "children"], [973, 30, 2482, 26], [973, 45, 2486, 24], [973, 49, 2486, 24, "_jsxDevRuntime"], [973, 63, 2486, 24], [973, 64, 2486, 24, "jsxDEV"], [973, 70, 2486, 24], [973, 72, 2486, 25, "_lucideReactNative"], [973, 90, 2486, 25], [973, 91, 2486, 25, "CheckCircle2"], [973, 103, 2486, 37], [974, 24, 2486, 38, "size"], [974, 28, 2486, 42], [974, 30, 2486, 44], [974, 32, 2486, 47], [975, 24, 2486, 48, "color"], [975, 29, 2486, 53], [975, 31, 2486, 54], [976, 22, 2486, 63], [977, 24, 2486, 63, "fileName"], [977, 32, 2486, 63], [977, 34, 2486, 63, "_jsxFileName"], [977, 46, 2486, 63], [978, 24, 2486, 63, "lineNumber"], [978, 34, 2486, 63], [979, 24, 2486, 63, "columnNumber"], [979, 36, 2486, 63], [980, 22, 2486, 63], [980, 29, 2486, 65], [981, 20, 2486, 66], [982, 22, 2486, 66, "fileName"], [982, 30, 2486, 66], [982, 32, 2486, 66, "_jsxFileName"], [982, 44, 2486, 66], [983, 22, 2486, 66, "lineNumber"], [983, 32, 2486, 66], [984, 22, 2486, 66, "columnNumber"], [984, 34, 2486, 66], [985, 20, 2486, 66], [985, 27, 2488, 28], [985, 28, 2488, 29], [985, 43, 2490, 22], [985, 47, 2490, 22, "_jsxDevRuntime"], [985, 61, 2490, 22], [985, 62, 2490, 22, "jsxDEV"], [985, 68, 2490, 22], [985, 70, 2490, 23, "_View"], [985, 75, 2490, 23], [985, 76, 2490, 23, "default"], [985, 83, 2490, 27], [986, 22, 2490, 28, "style"], [986, 27, 2490, 33], [986, 29, 2490, 35], [987, 24, 2490, 37, "marginLeft"], [987, 34, 2490, 47], [987, 36, 2490, 49], [987, 38, 2490, 51], [988, 24, 2490, 53, "flex"], [988, 28, 2490, 57], [988, 30, 2490, 59], [989, 22, 2490, 61], [989, 23, 2490, 63], [990, 22, 2490, 63, "children"], [990, 30, 2490, 63], [990, 46, 2492, 24], [990, 50, 2492, 24, "_jsxDevRuntime"], [990, 64, 2492, 24], [990, 65, 2492, 24, "jsxDEV"], [990, 71, 2492, 24], [990, 73, 2492, 25, "_Text"], [990, 78, 2492, 25], [990, 79, 2492, 25, "default"], [990, 86, 2492, 29], [991, 24, 2492, 30, "style"], [991, 29, 2492, 35], [991, 31, 2492, 37], [992, 26, 2492, 39, "fontSize"], [992, 34, 2492, 47], [992, 36, 2492, 49], [992, 38, 2492, 51], [993, 26, 2492, 53, "fontWeight"], [993, 36, 2492, 63], [993, 38, 2492, 65], [993, 43, 2492, 70], [994, 26, 2492, 72, "color"], [994, 31, 2492, 77], [994, 33, 2492, 79], [995, 24, 2492, 89], [995, 25, 2492, 91], [996, 24, 2492, 91, "children"], [996, 32, 2492, 91], [996, 34, 2492, 92], [997, 22, 2496, 24], [998, 24, 2496, 24, "fileName"], [998, 32, 2496, 24], [998, 34, 2496, 24, "_jsxFileName"], [998, 46, 2496, 24], [999, 24, 2496, 24, "lineNumber"], [999, 34, 2496, 24], [1000, 24, 2496, 24, "columnNumber"], [1000, 36, 2496, 24], [1001, 22, 2496, 24], [1001, 29, 2496, 30], [1001, 30, 2496, 31], [1001, 45, 2498, 24], [1001, 49, 2498, 24, "_jsxDevRuntime"], [1001, 63, 2498, 24], [1001, 64, 2498, 24, "jsxDEV"], [1001, 70, 2498, 24], [1001, 72, 2498, 25, "_Text"], [1001, 77, 2498, 25], [1001, 78, 2498, 25, "default"], [1001, 85, 2498, 29], [1002, 24, 2498, 30, "style"], [1002, 29, 2498, 35], [1002, 31, 2498, 37], [1003, 26, 2498, 39, "fontSize"], [1003, 34, 2498, 47], [1003, 36, 2498, 49], [1003, 38, 2498, 51], [1004, 26, 2498, 53, "color"], [1004, 31, 2498, 58], [1004, 33, 2498, 60], [1004, 42, 2498, 69], [1005, 26, 2498, 71, "marginTop"], [1005, 35, 2498, 80], [1005, 37, 2498, 82], [1006, 24, 2498, 84], [1006, 25, 2498, 86], [1007, 24, 2498, 86, "children"], [1007, 32, 2498, 86], [1007, 34, 2498, 87], [1008, 22, 2502, 24], [1009, 24, 2502, 24, "fileName"], [1009, 32, 2502, 24], [1009, 34, 2502, 24, "_jsxFileName"], [1009, 46, 2502, 24], [1010, 24, 2502, 24, "lineNumber"], [1010, 34, 2502, 24], [1011, 24, 2502, 24, "columnNumber"], [1011, 36, 2502, 24], [1012, 22, 2502, 24], [1012, 29, 2502, 30], [1012, 30, 2502, 31], [1013, 20, 2502, 31], [1014, 22, 2502, 31, "fileName"], [1014, 30, 2502, 31], [1014, 32, 2502, 31, "_jsxFileName"], [1014, 44, 2502, 31], [1015, 22, 2502, 31, "lineNumber"], [1015, 32, 2502, 31], [1016, 22, 2502, 31, "columnNumber"], [1016, 34, 2502, 31], [1017, 20, 2502, 31], [1017, 27, 2504, 28], [1017, 28, 2504, 29], [1018, 18, 2504, 29], [1019, 20, 2504, 29, "fileName"], [1019, 28, 2504, 29], [1019, 30, 2504, 29, "_jsxFileName"], [1019, 42, 2504, 29], [1020, 20, 2504, 29, "lineNumber"], [1020, 30, 2504, 29], [1021, 20, 2504, 29, "columnNumber"], [1021, 32, 2504, 29], [1022, 18, 2504, 29], [1022, 25, 2506, 26], [1022, 26, 2506, 27], [1022, 41, 2508, 20], [1022, 45, 2508, 20, "_jsxDevRuntime"], [1022, 59, 2508, 20], [1022, 60, 2508, 20, "jsxDEV"], [1022, 66, 2508, 20], [1022, 68, 2508, 21, "_View"], [1022, 73, 2508, 21], [1022, 74, 2508, 21, "default"], [1022, 81, 2508, 25], [1023, 20, 2510, 22, "style"], [1023, 25, 2510, 27], [1023, 27, 2510, 29], [1024, 22, 2512, 24, "backgroundColor"], [1024, 37, 2512, 39], [1024, 39, 2512, 41], [1024, 48, 2512, 50], [1025, 22, 2514, 24, "borderRadius"], [1025, 34, 2514, 36], [1025, 36, 2514, 38], [1025, 38, 2514, 40], [1026, 22, 2516, 24, "padding"], [1026, 29, 2516, 31], [1026, 31, 2516, 33], [1026, 33, 2516, 35], [1027, 22, 2518, 24, "marginBottom"], [1027, 34, 2518, 36], [1027, 36, 2518, 38], [1028, 20, 2520, 22], [1028, 21, 2520, 24], [1029, 20, 2520, 24, "children"], [1029, 28, 2520, 24], [1029, 44, 2524, 22], [1029, 48, 2524, 22, "_jsxDevRuntime"], [1029, 62, 2524, 22], [1029, 63, 2524, 22, "jsxDEV"], [1029, 69, 2524, 22], [1029, 71, 2524, 23, "_Text"], [1029, 76, 2524, 23], [1029, 77, 2524, 23, "default"], [1029, 84, 2524, 27], [1030, 22, 2526, 24, "style"], [1030, 27, 2526, 29], [1030, 29, 2526, 31], [1031, 24, 2528, 26, "fontSize"], [1031, 32, 2528, 34], [1031, 34, 2528, 36], [1031, 36, 2528, 38], [1032, 24, 2530, 26, "fontWeight"], [1032, 34, 2530, 36], [1032, 36, 2530, 38], [1032, 41, 2530, 43], [1033, 24, 2532, 26, "color"], [1033, 29, 2532, 31], [1033, 31, 2532, 33], [1033, 40, 2532, 42], [1034, 24, 2534, 26, "marginBottom"], [1034, 36, 2534, 38], [1034, 38, 2534, 40], [1034, 39, 2534, 41], [1035, 24, 2536, 26, "textTransform"], [1035, 37, 2536, 39], [1035, 39, 2536, 41], [1035, 50, 2536, 52], [1036, 24, 2538, 26, "letterSpacing"], [1036, 37, 2538, 39], [1036, 39, 2538, 41], [1037, 22, 2540, 24], [1037, 23, 2540, 26], [1038, 22, 2540, 26, "children"], [1038, 30, 2540, 26], [1038, 32, 2542, 23], [1039, 20, 2546, 22], [1040, 22, 2546, 22, "fileName"], [1040, 30, 2546, 22], [1040, 32, 2546, 22, "_jsxFileName"], [1040, 44, 2546, 22], [1041, 22, 2546, 22, "lineNumber"], [1041, 32, 2546, 22], [1042, 22, 2546, 22, "columnNumber"], [1042, 34, 2546, 22], [1043, 20, 2546, 22], [1043, 27, 2546, 28], [1043, 28, 2546, 29], [1043, 43, 2548, 22], [1043, 47, 2548, 22, "_jsxDevRuntime"], [1043, 61, 2548, 22], [1043, 62, 2548, 22, "jsxDEV"], [1043, 68, 2548, 22], [1043, 70, 2548, 23, "_View"], [1043, 75, 2548, 23], [1043, 76, 2548, 23, "default"], [1043, 83, 2548, 27], [1044, 22, 2548, 28, "style"], [1044, 27, 2548, 33], [1044, 29, 2548, 35], [1045, 24, 2548, 37, "flexDirection"], [1045, 37, 2548, 50], [1045, 39, 2548, 52], [1045, 44, 2548, 57], [1046, 24, 2548, 59, "marginBottom"], [1046, 36, 2548, 71], [1046, 38, 2548, 73], [1046, 39, 2548, 74], [1047, 24, 2548, 76, "alignItems"], [1047, 34, 2548, 86], [1047, 36, 2548, 88], [1048, 22, 2548, 101], [1048, 23, 2548, 103], [1049, 22, 2548, 103, "children"], [1049, 30, 2548, 103], [1049, 46, 2550, 24], [1049, 50, 2550, 24, "_jsxDevRuntime"], [1049, 64, 2550, 24], [1049, 65, 2550, 24, "jsxDEV"], [1049, 71, 2550, 24], [1049, 73, 2550, 25, "_lucideReactNative"], [1049, 91, 2550, 25], [1049, 92, 2550, 25, "CheckCircle2"], [1049, 104, 2550, 37], [1050, 24, 2550, 38, "size"], [1050, 28, 2550, 42], [1050, 30, 2550, 44], [1050, 32, 2550, 47], [1051, 24, 2550, 48, "color"], [1051, 29, 2550, 53], [1051, 31, 2550, 54], [1051, 40, 2550, 63], [1052, 24, 2550, 64, "style"], [1052, 29, 2550, 69], [1052, 31, 2550, 71], [1053, 26, 2550, 73, "marginTop"], [1053, 35, 2550, 82], [1053, 37, 2550, 84], [1054, 24, 2550, 86], [1055, 22, 2550, 88], [1056, 24, 2550, 88, "fileName"], [1056, 32, 2550, 88], [1056, 34, 2550, 88, "_jsxFileName"], [1056, 46, 2550, 88], [1057, 24, 2550, 88, "lineNumber"], [1057, 34, 2550, 88], [1058, 24, 2550, 88, "columnNumber"], [1058, 36, 2550, 88], [1059, 22, 2550, 88], [1059, 29, 2550, 90], [1059, 30, 2550, 91], [1059, 45, 2552, 24], [1059, 49, 2552, 24, "_jsxDevRuntime"], [1059, 63, 2552, 24], [1059, 64, 2552, 24, "jsxDEV"], [1059, 70, 2552, 24], [1059, 72, 2552, 25, "_Text"], [1059, 77, 2552, 25], [1059, 78, 2552, 25, "default"], [1059, 85, 2552, 29], [1060, 24, 2552, 30, "style"], [1060, 29, 2552, 35], [1060, 31, 2552, 37], [1061, 26, 2552, 39, "fontSize"], [1061, 34, 2552, 47], [1061, 36, 2552, 49], [1061, 38, 2552, 51], [1062, 26, 2552, 53, "color"], [1062, 31, 2552, 58], [1062, 33, 2552, 60], [1062, 42, 2552, 69], [1063, 26, 2552, 71, "marginLeft"], [1063, 36, 2552, 81], [1063, 38, 2552, 83], [1063, 39, 2552, 84], [1064, 26, 2552, 86, "flex"], [1064, 30, 2552, 90], [1064, 32, 2552, 92], [1065, 24, 2552, 94], [1065, 25, 2552, 96], [1066, 24, 2552, 96, "children"], [1066, 32, 2552, 96], [1066, 34, 2552, 97], [1067, 22, 2556, 24], [1068, 24, 2556, 24, "fileName"], [1068, 32, 2556, 24], [1068, 34, 2556, 24, "_jsxFileName"], [1068, 46, 2556, 24], [1069, 24, 2556, 24, "lineNumber"], [1069, 34, 2556, 24], [1070, 24, 2556, 24, "columnNumber"], [1070, 36, 2556, 24], [1071, 22, 2556, 24], [1071, 29, 2556, 30], [1071, 30, 2556, 31], [1072, 20, 2556, 31], [1073, 22, 2556, 31, "fileName"], [1073, 30, 2556, 31], [1073, 32, 2556, 31, "_jsxFileName"], [1073, 44, 2556, 31], [1074, 22, 2556, 31, "lineNumber"], [1074, 32, 2556, 31], [1075, 22, 2556, 31, "columnNumber"], [1075, 34, 2556, 31], [1076, 20, 2556, 31], [1076, 27, 2558, 28], [1076, 28, 2558, 29], [1076, 30, 2560, 23, "cameraResult"], [1076, 42, 2560, 35], [1076, 44, 2560, 37, "challengeCode"], [1076, 57, 2560, 50], [1076, 61, 2560, 54, "cameraResult"], [1076, 73, 2560, 66], [1076, 74, 2560, 67, "challengeCode"], [1076, 87, 2560, 80], [1076, 88, 2560, 81, "trim"], [1076, 92, 2560, 85], [1076, 93, 2560, 86], [1076, 94, 2560, 87], [1076, 111, 2562, 24], [1076, 115, 2562, 24, "_jsxDevRuntime"], [1076, 129, 2562, 24], [1076, 130, 2562, 24, "jsxDEV"], [1076, 136, 2562, 24], [1076, 138, 2562, 25, "_View"], [1076, 143, 2562, 25], [1076, 144, 2562, 25, "default"], [1076, 151, 2562, 29], [1077, 22, 2562, 30, "style"], [1077, 27, 2562, 35], [1077, 29, 2562, 37], [1078, 24, 2562, 39, "flexDirection"], [1078, 37, 2562, 52], [1078, 39, 2562, 54], [1078, 44, 2562, 59], [1079, 24, 2562, 61, "marginBottom"], [1079, 36, 2562, 73], [1079, 38, 2562, 75], [1079, 39, 2562, 76], [1080, 24, 2562, 78, "alignItems"], [1080, 34, 2562, 88], [1080, 36, 2562, 90], [1081, 22, 2562, 103], [1081, 23, 2562, 105], [1082, 22, 2562, 105, "children"], [1082, 30, 2562, 105], [1082, 46, 2564, 26], [1082, 50, 2564, 26, "_jsxDevRuntime"], [1082, 64, 2564, 26], [1082, 65, 2564, 26, "jsxDEV"], [1082, 71, 2564, 26], [1082, 73, 2564, 27, "_lucideReactNative"], [1082, 91, 2564, 27], [1082, 92, 2564, 27, "CheckCircle2"], [1082, 104, 2564, 39], [1083, 24, 2564, 40, "size"], [1083, 28, 2564, 44], [1083, 30, 2564, 46], [1083, 32, 2564, 49], [1084, 24, 2564, 50, "color"], [1084, 29, 2564, 55], [1084, 31, 2564, 56], [1084, 40, 2564, 65], [1085, 24, 2564, 66, "style"], [1085, 29, 2564, 71], [1085, 31, 2564, 73], [1086, 26, 2564, 75, "marginTop"], [1086, 35, 2564, 84], [1086, 37, 2564, 86], [1087, 24, 2564, 88], [1088, 22, 2564, 90], [1089, 24, 2564, 90, "fileName"], [1089, 32, 2564, 90], [1089, 34, 2564, 90, "_jsxFileName"], [1089, 46, 2564, 90], [1090, 24, 2564, 90, "lineNumber"], [1090, 34, 2564, 90], [1091, 24, 2564, 90, "columnNumber"], [1091, 36, 2564, 90], [1092, 22, 2564, 90], [1092, 29, 2564, 92], [1092, 30, 2564, 93], [1092, 45, 2566, 26], [1092, 49, 2566, 26, "_jsxDevRuntime"], [1092, 63, 2566, 26], [1092, 64, 2566, 26, "jsxDEV"], [1092, 70, 2566, 26], [1092, 72, 2566, 27, "_Text"], [1092, 77, 2566, 27], [1092, 78, 2566, 27, "default"], [1092, 85, 2566, 31], [1093, 24, 2566, 32, "style"], [1093, 29, 2566, 37], [1093, 31, 2566, 39], [1094, 26, 2566, 41, "fontSize"], [1094, 34, 2566, 49], [1094, 36, 2566, 51], [1094, 38, 2566, 53], [1095, 26, 2566, 55, "color"], [1095, 31, 2566, 60], [1095, 33, 2566, 62], [1095, 42, 2566, 71], [1096, 26, 2566, 73, "marginLeft"], [1096, 36, 2566, 83], [1096, 38, 2566, 85], [1096, 39, 2566, 86], [1097, 26, 2566, 88, "flex"], [1097, 30, 2566, 92], [1097, 32, 2566, 94], [1098, 24, 2566, 96], [1098, 25, 2566, 98], [1099, 24, 2566, 98, "children"], [1099, 32, 2566, 98], [1099, 34, 2568, 29], [1099, 57, 2568, 52, "cameraResult"], [1099, 69, 2568, 64], [1099, 70, 2568, 65, "challengeCode"], [1099, 83, 2568, 78], [1099, 87, 2568, 82], [1099, 92, 2568, 87], [1100, 22, 2568, 89], [1101, 24, 2568, 89, "fileName"], [1101, 32, 2568, 89], [1101, 34, 2568, 89, "_jsxFileName"], [1101, 46, 2568, 89], [1102, 24, 2568, 89, "lineNumber"], [1102, 34, 2568, 89], [1103, 24, 2568, 89, "columnNumber"], [1103, 36, 2568, 89], [1104, 22, 2568, 89], [1104, 29, 2570, 32], [1104, 30, 2570, 33], [1105, 20, 2570, 33], [1106, 22, 2570, 33, "fileName"], [1106, 30, 2570, 33], [1106, 32, 2570, 33, "_jsxFileName"], [1106, 44, 2570, 33], [1107, 22, 2570, 33, "lineNumber"], [1107, 32, 2570, 33], [1108, 22, 2570, 33, "columnNumber"], [1108, 34, 2570, 33], [1109, 20, 2570, 33], [1109, 27, 2572, 30], [1109, 28, 2574, 23], [1109, 43, 2576, 22], [1109, 47, 2576, 22, "_jsxDevRuntime"], [1109, 61, 2576, 22], [1109, 62, 2576, 22, "jsxDEV"], [1109, 68, 2576, 22], [1109, 70, 2576, 23, "_View"], [1109, 75, 2576, 23], [1109, 76, 2576, 23, "default"], [1109, 83, 2576, 27], [1110, 22, 2576, 28, "style"], [1110, 27, 2576, 33], [1110, 29, 2576, 35], [1111, 24, 2576, 37, "flexDirection"], [1111, 37, 2576, 50], [1111, 39, 2576, 52], [1111, 44, 2576, 57], [1112, 24, 2576, 59, "alignItems"], [1112, 34, 2576, 69], [1112, 36, 2576, 71], [1113, 22, 2576, 84], [1113, 23, 2576, 86], [1114, 22, 2576, 86, "children"], [1114, 30, 2576, 86], [1114, 46, 2578, 24], [1114, 50, 2578, 24, "_jsxDevRuntime"], [1114, 64, 2578, 24], [1114, 65, 2578, 24, "jsxDEV"], [1114, 71, 2578, 24], [1114, 73, 2578, 25, "_lucideReactNative"], [1114, 91, 2578, 25], [1114, 92, 2578, 25, "CheckCircle2"], [1114, 104, 2578, 37], [1115, 24, 2578, 38, "size"], [1115, 28, 2578, 42], [1115, 30, 2578, 44], [1115, 32, 2578, 47], [1116, 24, 2578, 48, "color"], [1116, 29, 2578, 53], [1116, 31, 2578, 54], [1116, 40, 2578, 63], [1117, 24, 2578, 64, "style"], [1117, 29, 2578, 69], [1117, 31, 2578, 71], [1118, 26, 2578, 73, "marginTop"], [1118, 35, 2578, 82], [1118, 37, 2578, 84], [1119, 24, 2578, 86], [1120, 22, 2578, 88], [1121, 24, 2578, 88, "fileName"], [1121, 32, 2578, 88], [1121, 34, 2578, 88, "_jsxFileName"], [1121, 46, 2578, 88], [1122, 24, 2578, 88, "lineNumber"], [1122, 34, 2578, 88], [1123, 24, 2578, 88, "columnNumber"], [1123, 36, 2578, 88], [1124, 22, 2578, 88], [1124, 29, 2578, 90], [1124, 30, 2578, 91], [1124, 45, 2580, 24], [1124, 49, 2580, 24, "_jsxDevRuntime"], [1124, 63, 2580, 24], [1124, 64, 2580, 24, "jsxDEV"], [1124, 70, 2580, 24], [1124, 72, 2580, 25, "_Text"], [1124, 77, 2580, 25], [1124, 78, 2580, 25, "default"], [1124, 85, 2580, 29], [1125, 24, 2580, 30, "style"], [1125, 29, 2580, 35], [1125, 31, 2580, 37], [1126, 26, 2580, 39, "fontSize"], [1126, 34, 2580, 47], [1126, 36, 2580, 49], [1126, 38, 2580, 51], [1127, 26, 2580, 53, "color"], [1127, 31, 2580, 58], [1127, 33, 2580, 60], [1127, 42, 2580, 69], [1128, 26, 2580, 71, "marginLeft"], [1128, 36, 2580, 81], [1128, 38, 2580, 83], [1128, 39, 2580, 84], [1129, 26, 2580, 86, "flex"], [1129, 30, 2580, 90], [1129, 32, 2580, 92], [1130, 24, 2580, 94], [1130, 25, 2580, 96], [1131, 24, 2580, 96, "children"], [1131, 32, 2580, 96], [1131, 34, 2582, 27], [1131, 57, 2582, 50, "distance"], [1131, 65, 2582, 58], [1131, 69, 2582, 62], [1131, 70, 2582, 63], [1132, 22, 2582, 71], [1133, 24, 2582, 71, "fileName"], [1133, 32, 2582, 71], [1133, 34, 2582, 71, "_jsxFileName"], [1133, 46, 2582, 71], [1134, 24, 2582, 71, "lineNumber"], [1134, 34, 2582, 71], [1135, 24, 2582, 71, "columnNumber"], [1135, 36, 2582, 71], [1136, 22, 2582, 71], [1136, 29, 2584, 30], [1136, 30, 2584, 31], [1137, 20, 2584, 31], [1138, 22, 2584, 31, "fileName"], [1138, 30, 2584, 31], [1138, 32, 2584, 31, "_jsxFileName"], [1138, 44, 2584, 31], [1139, 22, 2584, 31, "lineNumber"], [1139, 32, 2584, 31], [1140, 22, 2584, 31, "columnNumber"], [1140, 34, 2584, 31], [1141, 20, 2584, 31], [1141, 27, 2586, 28], [1141, 28, 2586, 29], [1142, 18, 2586, 29], [1143, 20, 2586, 29, "fileName"], [1143, 28, 2586, 29], [1143, 30, 2586, 29, "_jsxFileName"], [1143, 42, 2586, 29], [1144, 20, 2586, 29, "lineNumber"], [1144, 30, 2586, 29], [1145, 20, 2586, 29, "columnNumber"], [1145, 32, 2586, 29], [1146, 18, 2586, 29], [1146, 25, 2588, 26], [1146, 26, 2588, 27], [1146, 41, 2590, 20], [1146, 45, 2590, 20, "_jsxDevRuntime"], [1146, 59, 2590, 20], [1146, 60, 2590, 20, "jsxDEV"], [1146, 66, 2590, 20], [1146, 68, 2590, 21, "_View"], [1146, 73, 2590, 21], [1146, 74, 2590, 21, "default"], [1146, 81, 2590, 25], [1147, 20, 2590, 26, "style"], [1147, 25, 2590, 31], [1147, 27, 2590, 33], [1148, 22, 2590, 35, "flexDirection"], [1148, 35, 2590, 48], [1148, 37, 2590, 50], [1149, 20, 2590, 56], [1149, 21, 2590, 58], [1150, 20, 2590, 58, "children"], [1150, 28, 2590, 58], [1150, 43, 2592, 22], [1150, 47, 2592, 22, "_jsxDevRuntime"], [1150, 61, 2592, 22], [1150, 62, 2592, 22, "jsxDEV"], [1150, 68, 2592, 22], [1150, 70, 2592, 23, "_TouchableOpacity"], [1150, 87, 2592, 23], [1150, 88, 2592, 23, "default"], [1150, 95, 2592, 39], [1151, 22, 2594, 24, "onPress"], [1151, 29, 2594, 31], [1151, 31, 2594, 33, "onPress"], [1151, 32, 2594, 33], [1151, 37, 2594, 39], [1152, 24, 2596, 26, "setCameraResult"], [1152, 39, 2596, 41], [1152, 40, 2596, 42], [1152, 44, 2596, 46], [1152, 45, 2596, 47], [1153, 24, 2598, 26, "setCapturedPhotoUri"], [1153, 43, 2598, 45], [1153, 44, 2598, 46], [1153, 48, 2598, 50], [1153, 49, 2598, 51], [1154, 24, 2600, 26, "handleStartCamera"], [1154, 41, 2600, 43], [1154, 42, 2600, 44], [1154, 43, 2600, 45], [1155, 22, 2602, 24], [1155, 23, 2602, 26], [1156, 22, 2604, 24, "style"], [1156, 27, 2604, 29], [1156, 29, 2604, 31], [1157, 24, 2606, 26, "flex"], [1157, 28, 2606, 30], [1157, 30, 2606, 32], [1157, 31, 2606, 33], [1158, 24, 2608, 26, "backgroundColor"], [1158, 39, 2608, 41], [1158, 41, 2608, 43], [1158, 47, 2608, 49], [1159, 24, 2610, 26, "borderWidth"], [1159, 35, 2610, 37], [1159, 37, 2610, 39], [1159, 38, 2610, 40], [1160, 24, 2612, 26, "borderColor"], [1160, 35, 2612, 37], [1160, 37, 2612, 39], [1160, 46, 2612, 48], [1161, 24, 2614, 26, "borderRadius"], [1161, 36, 2614, 38], [1161, 38, 2614, 40], [1161, 40, 2614, 42], [1162, 24, 2616, 26, "paddingVertical"], [1162, 39, 2616, 41], [1162, 41, 2616, 43], [1162, 43, 2616, 45], [1163, 24, 2618, 26, "paddingHorizontal"], [1163, 41, 2618, 43], [1163, 43, 2618, 45], [1163, 45, 2618, 47], [1164, 24, 2620, 26, "flexDirection"], [1164, 37, 2620, 39], [1164, 39, 2620, 41], [1164, 44, 2620, 46], [1165, 24, 2622, 26, "alignItems"], [1165, 34, 2622, 36], [1165, 36, 2622, 38], [1165, 44, 2622, 46], [1166, 24, 2624, 26, "justifyContent"], [1166, 38, 2624, 40], [1166, 40, 2624, 42], [1167, 22, 2626, 24], [1167, 23, 2626, 26], [1168, 22, 2626, 26, "children"], [1168, 30, 2626, 26], [1168, 46, 2630, 24], [1168, 50, 2630, 24, "_jsxDevRuntime"], [1168, 64, 2630, 24], [1168, 65, 2630, 24, "jsxDEV"], [1168, 71, 2630, 24], [1168, 73, 2630, 25, "_lucideReactNative"], [1168, 91, 2630, 25], [1168, 92, 2630, 25, "Camera"], [1168, 98, 2630, 31], [1169, 24, 2630, 32, "size"], [1169, 28, 2630, 36], [1169, 30, 2630, 38], [1169, 32, 2630, 41], [1170, 24, 2630, 42, "color"], [1170, 29, 2630, 47], [1170, 31, 2630, 48], [1171, 22, 2630, 57], [1172, 24, 2630, 57, "fileName"], [1172, 32, 2630, 57], [1172, 34, 2630, 57, "_jsxFileName"], [1172, 46, 2630, 57], [1173, 24, 2630, 57, "lineNumber"], [1173, 34, 2630, 57], [1174, 24, 2630, 57, "columnNumber"], [1174, 36, 2630, 57], [1175, 22, 2630, 57], [1175, 29, 2630, 59], [1175, 30, 2630, 60], [1175, 45, 2632, 24], [1175, 49, 2632, 24, "_jsxDevRuntime"], [1175, 63, 2632, 24], [1175, 64, 2632, 24, "jsxDEV"], [1175, 70, 2632, 24], [1175, 72, 2632, 25, "_Text"], [1175, 77, 2632, 25], [1175, 78, 2632, 25, "default"], [1175, 85, 2632, 29], [1176, 24, 2632, 30, "style"], [1176, 29, 2632, 35], [1176, 31, 2632, 37], [1177, 26, 2632, 39, "fontSize"], [1177, 34, 2632, 47], [1177, 36, 2632, 49], [1177, 38, 2632, 51], [1178, 26, 2632, 53, "color"], [1178, 31, 2632, 58], [1178, 33, 2632, 60], [1178, 42, 2632, 69], [1179, 26, 2632, 71, "fontWeight"], [1179, 36, 2632, 81], [1179, 38, 2632, 83], [1179, 43, 2632, 88], [1180, 26, 2632, 90, "marginLeft"], [1180, 36, 2632, 100], [1180, 38, 2632, 102], [1181, 24, 2632, 104], [1181, 25, 2632, 106], [1182, 24, 2632, 106, "children"], [1182, 32, 2632, 106], [1182, 34, 2632, 107], [1183, 22, 2636, 24], [1184, 24, 2636, 24, "fileName"], [1184, 32, 2636, 24], [1184, 34, 2636, 24, "_jsxFileName"], [1184, 46, 2636, 24], [1185, 24, 2636, 24, "lineNumber"], [1185, 34, 2636, 24], [1186, 24, 2636, 24, "columnNumber"], [1186, 36, 2636, 24], [1187, 22, 2636, 24], [1187, 29, 2636, 30], [1187, 30, 2636, 31], [1188, 20, 2636, 31], [1189, 22, 2636, 31, "fileName"], [1189, 30, 2636, 31], [1189, 32, 2636, 31, "_jsxFileName"], [1189, 44, 2636, 31], [1190, 22, 2636, 31, "lineNumber"], [1190, 32, 2636, 31], [1191, 22, 2636, 31, "columnNumber"], [1191, 34, 2636, 31], [1192, 20, 2636, 31], [1192, 27, 2638, 40], [1193, 18, 2638, 41], [1194, 20, 2638, 41, "fileName"], [1194, 28, 2638, 41], [1194, 30, 2638, 41, "_jsxFileName"], [1194, 42, 2638, 41], [1195, 20, 2638, 41, "lineNumber"], [1195, 30, 2638, 41], [1196, 20, 2638, 41, "columnNumber"], [1196, 32, 2638, 41], [1197, 18, 2638, 41], [1197, 25, 2640, 26], [1197, 26, 2640, 27], [1198, 16, 2640, 27], [1199, 18, 2640, 27, "fileName"], [1199, 26, 2640, 27], [1199, 28, 2640, 27, "_jsxFileName"], [1199, 40, 2640, 27], [1200, 18, 2640, 27, "lineNumber"], [1200, 28, 2640, 27], [1201, 18, 2640, 27, "columnNumber"], [1201, 30, 2640, 27], [1202, 16, 2640, 27], [1202, 23, 2642, 24], [1202, 24, 2642, 25], [1203, 14, 2642, 25], [1204, 16, 2642, 25, "fileName"], [1204, 24, 2642, 25], [1204, 26, 2642, 25, "_jsxFileName"], [1204, 38, 2642, 25], [1205, 16, 2642, 25, "lineNumber"], [1205, 26, 2642, 25], [1206, 16, 2642, 25, "columnNumber"], [1206, 28, 2642, 25], [1207, 14, 2642, 25], [1207, 21, 2644, 22], [1207, 22, 2644, 23], [1207, 38, 2648, 16], [1207, 42, 2648, 16, "_jsxDevRuntime"], [1207, 56, 2648, 16], [1207, 57, 2648, 16, "jsxDEV"], [1207, 63, 2648, 16], [1207, 65, 2648, 17, "_TouchableOpacity"], [1207, 82, 2648, 17], [1207, 83, 2648, 17, "default"], [1207, 90, 2648, 33], [1208, 16, 2650, 18, "onPress"], [1208, 23, 2650, 25], [1208, 25, 2650, 27, "handleStartCamera"], [1208, 42, 2650, 45], [1209, 16, 2652, 18, "disabled"], [1209, 24, 2652, 26], [1209, 26, 2652, 28, "locationStatus"], [1209, 40, 2652, 42], [1209, 45, 2652, 47], [1209, 55, 2652, 57], [1209, 59, 2652, 61], [1209, 60, 2652, 62, "testingMode"], [1209, 71, 2652, 74], [1210, 16, 2654, 18, "style"], [1210, 21, 2654, 23], [1210, 23, 2654, 25], [1211, 18, 2656, 20, "backgroundColor"], [1211, 33, 2656, 35], [1211, 35, 2658, 22, "locationStatus"], [1211, 49, 2658, 36], [1211, 54, 2658, 41], [1211, 64, 2658, 51], [1211, 68, 2658, 55, "testingMode"], [1211, 79, 2658, 66], [1211, 82, 2658, 69], [1211, 91, 2658, 78], [1211, 94, 2658, 81], [1211, 103, 2658, 90], [1212, 18, 2660, 20, "borderRadius"], [1212, 30, 2660, 32], [1212, 32, 2660, 34], [1212, 34, 2660, 36], [1213, 18, 2662, 20, "padding"], [1213, 25, 2662, 27], [1213, 27, 2662, 29], [1213, 29, 2662, 31], [1214, 18, 2664, 20, "flexDirection"], [1214, 31, 2664, 33], [1214, 33, 2664, 35], [1214, 38, 2664, 40], [1215, 18, 2666, 20, "alignItems"], [1215, 28, 2666, 30], [1215, 30, 2666, 32], [1215, 38, 2666, 40], [1216, 18, 2668, 20, "justifyContent"], [1216, 32, 2668, 34], [1216, 34, 2668, 36], [1217, 16, 2670, 18], [1217, 17, 2670, 20], [1218, 16, 2670, 20, "children"], [1218, 24, 2670, 20], [1218, 40, 2674, 18], [1218, 44, 2674, 18, "_jsxDevRuntime"], [1218, 58, 2674, 18], [1218, 59, 2674, 18, "jsxDEV"], [1218, 65, 2674, 18], [1218, 67, 2674, 19, "_lucideReactNative"], [1218, 85, 2674, 19], [1218, 86, 2674, 19, "Camera"], [1218, 92, 2674, 25], [1219, 18, 2674, 26, "size"], [1219, 22, 2674, 30], [1219, 24, 2674, 32], [1219, 26, 2674, 35], [1220, 18, 2674, 36, "color"], [1220, 23, 2674, 41], [1220, 25, 2674, 42], [1221, 16, 2674, 48], [1222, 18, 2674, 48, "fileName"], [1222, 26, 2674, 48], [1222, 28, 2674, 48, "_jsxFileName"], [1222, 40, 2674, 48], [1223, 18, 2674, 48, "lineNumber"], [1223, 28, 2674, 48], [1224, 18, 2674, 48, "columnNumber"], [1224, 30, 2674, 48], [1225, 16, 2674, 48], [1225, 23, 2674, 50], [1225, 24, 2674, 51], [1225, 39, 2676, 18], [1225, 43, 2676, 18, "_jsxDevRuntime"], [1225, 57, 2676, 18], [1225, 58, 2676, 18, "jsxDEV"], [1225, 64, 2676, 18], [1225, 66, 2676, 19, "_Text"], [1225, 71, 2676, 19], [1225, 72, 2676, 19, "default"], [1225, 79, 2676, 23], [1226, 18, 2678, 20, "style"], [1226, 23, 2678, 25], [1226, 25, 2678, 27], [1227, 20, 2680, 22, "fontSize"], [1227, 28, 2680, 30], [1227, 30, 2680, 32], [1227, 32, 2680, 34], [1228, 20, 2682, 22, "fontWeight"], [1228, 30, 2682, 32], [1228, 32, 2682, 34], [1228, 37, 2682, 39], [1229, 20, 2684, 22, "color"], [1229, 25, 2684, 27], [1229, 27, 2684, 29], [1229, 33, 2684, 35], [1230, 20, 2686, 22, "marginLeft"], [1230, 30, 2686, 32], [1230, 32, 2686, 34], [1231, 18, 2688, 20], [1231, 19, 2688, 22], [1232, 18, 2688, 22, "children"], [1232, 26, 2688, 22], [1232, 28, 2692, 21, "locationStatus"], [1232, 42, 2692, 35], [1232, 47, 2692, 40], [1232, 57, 2692, 50], [1232, 61, 2692, 54, "testingMode"], [1232, 72, 2692, 65], [1232, 75, 2694, 24], [1232, 89, 2694, 38], [1232, 92, 2696, 24], [1233, 16, 2696, 47], [1234, 18, 2696, 47, "fileName"], [1234, 26, 2696, 47], [1234, 28, 2696, 47, "_jsxFileName"], [1234, 40, 2696, 47], [1235, 18, 2696, 47, "lineNumber"], [1235, 28, 2696, 47], [1236, 18, 2696, 47, "columnNumber"], [1236, 30, 2696, 47], [1237, 16, 2696, 47], [1237, 23, 2698, 24], [1237, 24, 2698, 25], [1237, 26, 2700, 19, "testingMode"], [1237, 37, 2700, 30], [1237, 54, 2702, 20], [1237, 58, 2702, 20, "_jsxDevRuntime"], [1237, 72, 2702, 20], [1237, 73, 2702, 20, "jsxDEV"], [1237, 79, 2702, 20], [1237, 81, 2702, 21, "_Text"], [1237, 86, 2702, 21], [1237, 87, 2702, 21, "default"], [1237, 94, 2702, 25], [1238, 18, 2702, 26, "style"], [1238, 23, 2702, 31], [1238, 25, 2702, 33], [1239, 20, 2702, 35, "fontSize"], [1239, 28, 2702, 43], [1239, 30, 2702, 45], [1239, 32, 2702, 47], [1240, 20, 2702, 49, "color"], [1240, 25, 2702, 54], [1240, 27, 2702, 56], [1240, 33, 2702, 62], [1241, 20, 2702, 64, "marginLeft"], [1241, 30, 2702, 74], [1241, 32, 2702, 76], [1242, 18, 2702, 78], [1242, 19, 2702, 80], [1243, 18, 2702, 80, "children"], [1243, 26, 2702, 80], [1243, 28, 2702, 81], [1244, 16, 2702, 85], [1245, 18, 2702, 85, "fileName"], [1245, 26, 2702, 85], [1245, 28, 2702, 85, "_jsxFileName"], [1245, 40, 2702, 85], [1246, 18, 2702, 85, "lineNumber"], [1246, 28, 2702, 85], [1247, 18, 2702, 85, "columnNumber"], [1247, 30, 2702, 85], [1248, 16, 2702, 85], [1248, 23, 2702, 91], [1248, 24, 2704, 19], [1249, 14, 2704, 19], [1250, 16, 2704, 19, "fileName"], [1250, 24, 2704, 19], [1250, 26, 2704, 19, "_jsxFileName"], [1250, 38, 2704, 19], [1251, 16, 2704, 19, "lineNumber"], [1251, 26, 2704, 19], [1252, 16, 2704, 19, "columnNumber"], [1252, 28, 2704, 19], [1253, 14, 2704, 19], [1253, 21, 2706, 34], [1253, 22, 2708, 15], [1254, 12, 2708, 15], [1255, 14, 2708, 15, "fileName"], [1255, 22, 2708, 15], [1255, 24, 2708, 15, "_jsxFileName"], [1255, 36, 2708, 15], [1256, 14, 2708, 15, "lineNumber"], [1256, 24, 2708, 15], [1257, 14, 2708, 15, "columnNumber"], [1257, 26, 2708, 15], [1258, 12, 2708, 15], [1258, 19, 2710, 18], [1258, 20, 2710, 19], [1258, 35, 2713, 12], [1258, 39, 2713, 12, "_jsxDevRuntime"], [1258, 53, 2713, 12], [1258, 54, 2713, 12, "jsxDEV"], [1258, 60, 2713, 12], [1258, 62, 2713, 13, "_View"], [1258, 67, 2713, 13], [1258, 68, 2713, 13, "default"], [1258, 75, 2713, 17], [1259, 14, 2713, 18, "style"], [1259, 19, 2713, 23], [1259, 21, 2713, 25], [1260, 16, 2713, 27, "marginBottom"], [1260, 28, 2713, 39], [1260, 30, 2713, 41], [1261, 14, 2713, 44], [1261, 15, 2713, 46], [1262, 14, 2713, 46, "children"], [1262, 22, 2713, 46], [1262, 38, 2714, 14], [1262, 42, 2714, 14, "_jsxDevRuntime"], [1262, 56, 2714, 14], [1262, 57, 2714, 14, "jsxDEV"], [1262, 63, 2714, 14], [1262, 65, 2714, 15, "_View"], [1262, 70, 2714, 15], [1262, 71, 2714, 15, "default"], [1262, 78, 2714, 19], [1263, 16, 2715, 16, "style"], [1263, 21, 2715, 21], [1263, 23, 2715, 23], [1264, 18, 2716, 18, "flexDirection"], [1264, 31, 2716, 31], [1264, 33, 2716, 33], [1264, 38, 2716, 38], [1265, 18, 2717, 18, "alignItems"], [1265, 28, 2717, 28], [1265, 30, 2717, 30], [1265, 38, 2717, 38], [1266, 18, 2718, 18, "marginBottom"], [1266, 30, 2718, 30], [1266, 32, 2718, 32], [1267, 16, 2719, 16], [1267, 17, 2719, 18], [1268, 16, 2719, 18, "children"], [1268, 24, 2719, 18], [1268, 40, 2721, 16], [1268, 44, 2721, 16, "_jsxDevRuntime"], [1268, 58, 2721, 16], [1268, 59, 2721, 16, "jsxDEV"], [1268, 65, 2721, 16], [1268, 67, 2721, 17, "_View"], [1268, 72, 2721, 17], [1268, 73, 2721, 17, "default"], [1268, 80, 2721, 21], [1269, 18, 2722, 18, "style"], [1269, 23, 2722, 23], [1269, 25, 2722, 25], [1270, 20, 2723, 20, "width"], [1270, 25, 2723, 25], [1270, 27, 2723, 27], [1270, 29, 2723, 29], [1271, 20, 2724, 20, "height"], [1271, 26, 2724, 26], [1271, 28, 2724, 28], [1271, 30, 2724, 30], [1272, 20, 2725, 20, "borderRadius"], [1272, 32, 2725, 32], [1272, 34, 2725, 34], [1272, 36, 2725, 36], [1273, 20, 2726, 20, "backgroundColor"], [1273, 35, 2726, 35], [1273, 37, 2726, 37, "response"], [1273, 45, 2726, 45], [1273, 46, 2726, 46, "trim"], [1273, 50, 2726, 50], [1273, 51, 2726, 51], [1273, 52, 2726, 52], [1273, 55, 2727, 24], [1273, 64, 2727, 33], [1273, 67, 2728, 24, "cameraResult"], [1273, 79, 2728, 36], [1273, 82, 2729, 26], [1273, 91, 2729, 35], [1273, 94, 2730, 26], [1273, 103, 2730, 35], [1274, 20, 2731, 20, "alignItems"], [1274, 30, 2731, 30], [1274, 32, 2731, 32], [1274, 40, 2731, 40], [1275, 20, 2732, 20, "justifyContent"], [1275, 34, 2732, 34], [1275, 36, 2732, 36], [1275, 44, 2732, 44], [1276, 20, 2733, 20, "marginRight"], [1276, 31, 2733, 31], [1276, 33, 2733, 33], [1277, 18, 2734, 18], [1277, 19, 2734, 20], [1278, 18, 2734, 20, "children"], [1278, 26, 2734, 20], [1278, 28, 2736, 19, "response"], [1278, 36, 2736, 27], [1278, 37, 2736, 28, "trim"], [1278, 41, 2736, 32], [1278, 42, 2736, 33], [1278, 43, 2736, 34], [1278, 59, 2737, 20], [1278, 63, 2737, 20, "_jsxDevRuntime"], [1278, 77, 2737, 20], [1278, 78, 2737, 20, "jsxDEV"], [1278, 84, 2737, 20], [1278, 86, 2737, 21, "_lucideReactNative"], [1278, 104, 2737, 21], [1278, 105, 2737, 21, "CheckCircle2"], [1278, 117, 2737, 33], [1279, 20, 2737, 34, "size"], [1279, 24, 2737, 38], [1279, 26, 2737, 40], [1279, 28, 2737, 43], [1280, 20, 2737, 44, "color"], [1280, 25, 2737, 49], [1280, 27, 2737, 50], [1281, 18, 2737, 56], [1282, 20, 2737, 56, "fileName"], [1282, 28, 2737, 56], [1282, 30, 2737, 56, "_jsxFileName"], [1282, 42, 2737, 56], [1283, 20, 2737, 56, "lineNumber"], [1283, 30, 2737, 56], [1284, 20, 2737, 56, "columnNumber"], [1284, 32, 2737, 56], [1285, 18, 2737, 56], [1285, 25, 2737, 58], [1285, 26, 2737, 59], [1285, 42, 2739, 20], [1285, 46, 2739, 20, "_jsxDevRuntime"], [1285, 60, 2739, 20], [1285, 61, 2739, 20, "jsxDEV"], [1285, 67, 2739, 20], [1285, 69, 2739, 21, "_Text"], [1285, 74, 2739, 21], [1285, 75, 2739, 21, "default"], [1285, 82, 2739, 25], [1286, 20, 2740, 22, "style"], [1286, 25, 2740, 27], [1286, 27, 2740, 29], [1287, 22, 2740, 31, "color"], [1287, 27, 2740, 36], [1287, 29, 2740, 38], [1287, 35, 2740, 44], [1288, 22, 2740, 46, "fontSize"], [1288, 30, 2740, 54], [1288, 32, 2740, 56], [1288, 34, 2740, 58], [1289, 22, 2740, 60, "fontWeight"], [1289, 32, 2740, 70], [1289, 34, 2740, 72], [1290, 20, 2740, 78], [1290, 21, 2740, 80], [1291, 20, 2740, 80, "children"], [1291, 28, 2740, 80], [1291, 30, 2741, 21], [1292, 18, 2743, 20], [1293, 20, 2743, 20, "fileName"], [1293, 28, 2743, 20], [1293, 30, 2743, 20, "_jsxFileName"], [1293, 42, 2743, 20], [1294, 20, 2743, 20, "lineNumber"], [1294, 30, 2743, 20], [1295, 20, 2743, 20, "columnNumber"], [1295, 32, 2743, 20], [1296, 18, 2743, 20], [1296, 25, 2743, 26], [1297, 16, 2744, 19], [1298, 18, 2744, 19, "fileName"], [1298, 26, 2744, 19], [1298, 28, 2744, 19, "_jsxFileName"], [1298, 40, 2744, 19], [1299, 18, 2744, 19, "lineNumber"], [1299, 28, 2744, 19], [1300, 18, 2744, 19, "columnNumber"], [1300, 30, 2744, 19], [1301, 16, 2744, 19], [1301, 23, 2745, 22], [1301, 24, 2745, 23], [1301, 39, 2747, 16], [1301, 43, 2747, 16, "_jsxDevRuntime"], [1301, 57, 2747, 16], [1301, 58, 2747, 16, "jsxDEV"], [1301, 64, 2747, 16], [1301, 66, 2747, 17, "_Text"], [1301, 71, 2747, 17], [1301, 72, 2747, 17, "default"], [1301, 79, 2747, 21], [1302, 18, 2748, 18, "style"], [1302, 23, 2748, 23], [1302, 25, 2748, 25], [1303, 20, 2749, 20, "fontSize"], [1303, 28, 2749, 28], [1303, 30, 2749, 30], [1303, 32, 2749, 32], [1304, 20, 2750, 20, "fontWeight"], [1304, 30, 2750, 30], [1304, 32, 2750, 32], [1304, 37, 2750, 37], [1305, 20, 2751, 20, "color"], [1305, 25, 2751, 25], [1305, 27, 2751, 27], [1306, 18, 2752, 18], [1306, 19, 2752, 20], [1307, 18, 2752, 20, "children"], [1307, 26, 2752, 20], [1307, 28, 2753, 17], [1308, 16, 2755, 16], [1309, 18, 2755, 16, "fileName"], [1309, 26, 2755, 16], [1309, 28, 2755, 16, "_jsxFileName"], [1309, 40, 2755, 16], [1310, 18, 2755, 16, "lineNumber"], [1310, 28, 2755, 16], [1311, 18, 2755, 16, "columnNumber"], [1311, 30, 2755, 16], [1312, 16, 2755, 16], [1312, 23, 2755, 22], [1312, 24, 2755, 23], [1313, 14, 2755, 23], [1314, 16, 2755, 23, "fileName"], [1314, 24, 2755, 23], [1314, 26, 2755, 23, "_jsxFileName"], [1314, 38, 2755, 23], [1315, 16, 2755, 23, "lineNumber"], [1315, 26, 2755, 23], [1316, 16, 2755, 23, "columnNumber"], [1316, 28, 2755, 23], [1317, 14, 2755, 23], [1317, 21, 2756, 20], [1317, 22, 2756, 21], [1317, 37, 2758, 14], [1317, 41, 2758, 14, "_jsxDevRuntime"], [1317, 55, 2758, 14], [1317, 56, 2758, 14, "jsxDEV"], [1317, 62, 2758, 14], [1317, 64, 2758, 15, "_Text"], [1317, 69, 2758, 15], [1317, 70, 2758, 15, "default"], [1317, 77, 2758, 19], [1318, 16, 2759, 16, "style"], [1318, 21, 2759, 21], [1318, 23, 2759, 23], [1319, 18, 2760, 18, "fontSize"], [1319, 26, 2760, 26], [1319, 28, 2760, 28], [1319, 30, 2760, 30], [1320, 18, 2761, 18, "color"], [1320, 23, 2761, 23], [1320, 25, 2761, 25], [1320, 34, 2761, 34], [1321, 18, 2762, 18, "marginBottom"], [1321, 30, 2762, 30], [1321, 32, 2762, 32], [1321, 34, 2762, 34], [1322, 18, 2763, 18, "lineHeight"], [1322, 28, 2763, 28], [1322, 30, 2763, 30], [1323, 16, 2764, 16], [1323, 17, 2764, 18], [1324, 16, 2764, 18, "children"], [1324, 24, 2764, 18], [1324, 26, 2766, 17], [1325, 14, 2766, 109], [1326, 16, 2766, 109, "fileName"], [1326, 24, 2766, 109], [1326, 26, 2766, 109, "_jsxFileName"], [1326, 38, 2766, 109], [1327, 16, 2766, 109, "lineNumber"], [1327, 26, 2766, 109], [1328, 16, 2766, 109, "columnNumber"], [1328, 28, 2766, 109], [1329, 14, 2766, 109], [1329, 21, 2767, 20], [1329, 22, 2767, 21], [1329, 37, 2769, 14], [1329, 41, 2769, 14, "_jsxDevRuntime"], [1329, 55, 2769, 14], [1329, 56, 2769, 14, "jsxDEV"], [1329, 62, 2769, 14], [1329, 64, 2769, 15, "_View"], [1329, 69, 2769, 15], [1329, 70, 2769, 15, "default"], [1329, 77, 2769, 19], [1330, 16, 2770, 16, "style"], [1330, 21, 2770, 21], [1330, 23, 2770, 23], [1331, 18, 2771, 18, "backgroundColor"], [1331, 33, 2771, 33], [1331, 35, 2771, 35], [1331, 41, 2771, 41], [1332, 18, 2772, 18, "borderRadius"], [1332, 30, 2772, 30], [1332, 32, 2772, 32], [1332, 34, 2772, 34], [1333, 18, 2773, 18, "borderWidth"], [1333, 29, 2773, 29], [1333, 31, 2773, 31], [1333, 32, 2773, 32], [1334, 18, 2774, 18, "borderColor"], [1334, 29, 2774, 29], [1334, 31, 2774, 31], [1334, 40, 2774, 40], [1335, 18, 2775, 18, "padding"], [1335, 25, 2775, 25], [1335, 27, 2775, 27], [1336, 16, 2776, 16], [1336, 17, 2776, 18], [1337, 16, 2776, 18, "children"], [1337, 24, 2776, 18], [1337, 40, 2778, 16], [1337, 44, 2778, 16, "_jsxDevRuntime"], [1337, 58, 2778, 16], [1337, 59, 2778, 16, "jsxDEV"], [1337, 65, 2778, 16], [1337, 67, 2778, 17, "_View"], [1337, 72, 2778, 17], [1337, 73, 2778, 17, "default"], [1337, 80, 2778, 21], [1338, 18, 2779, 18, "style"], [1338, 23, 2779, 23], [1338, 25, 2779, 25], [1339, 20, 2780, 20, "flexDirection"], [1339, 33, 2780, 33], [1339, 35, 2780, 35], [1339, 40, 2780, 40], [1340, 20, 2781, 20, "alignItems"], [1340, 30, 2781, 30], [1340, 32, 2781, 32], [1340, 44, 2781, 44], [1341, 20, 2782, 20, "padding"], [1341, 27, 2782, 27], [1341, 29, 2782, 29], [1342, 18, 2783, 18], [1342, 19, 2783, 20], [1343, 18, 2783, 20, "children"], [1343, 26, 2783, 20], [1343, 42, 2785, 18], [1343, 46, 2785, 18, "_jsxDevRuntime"], [1343, 60, 2785, 18], [1343, 61, 2785, 18, "jsxDEV"], [1343, 67, 2785, 18], [1343, 69, 2785, 19, "_lucideReactNative"], [1343, 87, 2785, 19], [1343, 88, 2785, 19, "MessageCircle"], [1343, 101, 2785, 32], [1344, 20, 2786, 20, "size"], [1344, 24, 2786, 24], [1344, 26, 2786, 26], [1344, 28, 2786, 29], [1345, 20, 2787, 20, "color"], [1345, 25, 2787, 25], [1345, 27, 2787, 26], [1345, 36, 2787, 35], [1346, 20, 2788, 20, "style"], [1346, 25, 2788, 25], [1346, 27, 2788, 27], [1347, 22, 2788, 29, "marginTop"], [1347, 31, 2788, 38], [1347, 33, 2788, 40], [1347, 34, 2788, 41], [1348, 22, 2788, 43, "marginRight"], [1348, 33, 2788, 54], [1348, 35, 2788, 56], [1349, 20, 2788, 59], [1350, 18, 2788, 61], [1351, 20, 2788, 61, "fileName"], [1351, 28, 2788, 61], [1351, 30, 2788, 61, "_jsxFileName"], [1351, 42, 2788, 61], [1352, 20, 2788, 61, "lineNumber"], [1352, 30, 2788, 61], [1353, 20, 2788, 61, "columnNumber"], [1353, 32, 2788, 61], [1354, 18, 2788, 61], [1354, 25, 2789, 19], [1354, 26, 2789, 20], [1354, 41, 2790, 18], [1354, 45, 2790, 18, "_jsxDevRuntime"], [1354, 59, 2790, 18], [1354, 60, 2790, 18, "jsxDEV"], [1354, 66, 2790, 18], [1354, 68, 2790, 19, "_TextInput"], [1354, 78, 2790, 19], [1354, 79, 2790, 19, "default"], [1354, 86, 2790, 28], [1355, 20, 2791, 20, "style"], [1355, 25, 2791, 25], [1355, 27, 2791, 27], [1356, 22, 2792, 22, "flex"], [1356, 26, 2792, 26], [1356, 28, 2792, 28], [1356, 29, 2792, 29], [1357, 22, 2793, 22, "fontSize"], [1357, 30, 2793, 30], [1357, 32, 2793, 32], [1357, 34, 2793, 34], [1358, 22, 2794, 22, "color"], [1358, 27, 2794, 27], [1358, 29, 2794, 29], [1358, 38, 2794, 38], [1359, 22, 2795, 22, "minHeight"], [1359, 31, 2795, 31], [1359, 33, 2795, 33], [1359, 36, 2795, 36], [1360, 22, 2796, 22, "textAlignVertical"], [1360, 39, 2796, 39], [1360, 41, 2796, 41], [1361, 20, 2797, 20], [1361, 21, 2797, 22], [1362, 20, 2798, 20, "placeholder"], [1362, 31, 2798, 31], [1362, 33, 2798, 32], [1362, 88, 2798, 87], [1363, 20, 2799, 20, "placeholderTextColor"], [1363, 40, 2799, 40], [1363, 42, 2799, 41], [1363, 51, 2799, 50], [1364, 20, 2800, 20, "value"], [1364, 25, 2800, 25], [1364, 27, 2800, 27, "response"], [1364, 35, 2800, 36], [1365, 20, 2801, 20, "onChangeText"], [1365, 32, 2801, 32], [1365, 34, 2801, 34, "setResponse"], [1365, 45, 2801, 46], [1366, 20, 2802, 20, "multiline"], [1366, 29, 2802, 29], [1367, 20, 2803, 20, "max<PERSON><PERSON><PERSON>"], [1367, 29, 2803, 29], [1367, 31, 2803, 31], [1368, 18, 2803, 35], [1369, 20, 2803, 35, "fileName"], [1369, 28, 2803, 35], [1369, 30, 2803, 35, "_jsxFileName"], [1369, 42, 2803, 35], [1370, 20, 2803, 35, "lineNumber"], [1370, 30, 2803, 35], [1371, 20, 2803, 35, "columnNumber"], [1371, 32, 2803, 35], [1372, 18, 2803, 35], [1372, 25, 2804, 19], [1372, 26, 2804, 20], [1373, 16, 2804, 20], [1374, 18, 2804, 20, "fileName"], [1374, 26, 2804, 20], [1374, 28, 2804, 20, "_jsxFileName"], [1374, 40, 2804, 20], [1375, 18, 2804, 20, "lineNumber"], [1375, 28, 2804, 20], [1376, 18, 2804, 20, "columnNumber"], [1376, 30, 2804, 20], [1377, 16, 2804, 20], [1377, 23, 2805, 22], [1377, 24, 2805, 23], [1377, 39, 2807, 16], [1377, 43, 2807, 16, "_jsxDevRuntime"], [1377, 57, 2807, 16], [1377, 58, 2807, 16, "jsxDEV"], [1377, 64, 2807, 16], [1377, 66, 2807, 17, "_View"], [1377, 71, 2807, 17], [1377, 72, 2807, 17, "default"], [1377, 79, 2807, 21], [1378, 18, 2808, 18, "style"], [1378, 23, 2808, 23], [1378, 25, 2808, 25], [1379, 20, 2809, 20, "flexDirection"], [1379, 33, 2809, 33], [1379, 35, 2809, 35], [1379, 40, 2809, 40], [1380, 20, 2810, 20, "justifyContent"], [1380, 34, 2810, 34], [1380, 36, 2810, 36], [1380, 51, 2810, 51], [1381, 20, 2811, 20, "alignItems"], [1381, 30, 2811, 30], [1381, 32, 2811, 32], [1381, 40, 2811, 40], [1382, 20, 2812, 20, "paddingHorizontal"], [1382, 37, 2812, 37], [1382, 39, 2812, 39], [1382, 41, 2812, 41], [1383, 20, 2813, 20, "paddingBottom"], [1383, 33, 2813, 33], [1383, 35, 2813, 35], [1384, 18, 2814, 18], [1384, 19, 2814, 20], [1385, 18, 2814, 20, "children"], [1385, 26, 2814, 20], [1385, 42, 2816, 18], [1385, 46, 2816, 18, "_jsxDevRuntime"], [1385, 60, 2816, 18], [1385, 61, 2816, 18, "jsxDEV"], [1385, 67, 2816, 18], [1385, 69, 2816, 19, "_Text"], [1385, 74, 2816, 19], [1385, 75, 2816, 19, "default"], [1385, 82, 2816, 23], [1386, 20, 2816, 24, "style"], [1386, 25, 2816, 29], [1386, 27, 2816, 31], [1387, 22, 2816, 33, "fontSize"], [1387, 30, 2816, 41], [1387, 32, 2816, 43], [1387, 34, 2816, 45], [1388, 22, 2816, 47, "color"], [1388, 27, 2816, 52], [1388, 29, 2816, 54], [1389, 20, 2816, 64], [1389, 21, 2816, 66], [1390, 20, 2816, 66, "children"], [1390, 28, 2816, 66], [1390, 30, 2816, 67], [1391, 18, 2818, 18], [1392, 20, 2818, 18, "fileName"], [1392, 28, 2818, 18], [1392, 30, 2818, 18, "_jsxFileName"], [1392, 42, 2818, 18], [1393, 20, 2818, 18, "lineNumber"], [1393, 30, 2818, 18], [1394, 20, 2818, 18, "columnNumber"], [1394, 32, 2818, 18], [1395, 18, 2818, 18], [1395, 25, 2818, 24], [1395, 26, 2818, 25], [1395, 41, 2819, 18], [1395, 45, 2819, 18, "_jsxDevRuntime"], [1395, 59, 2819, 18], [1395, 60, 2819, 18, "jsxDEV"], [1395, 66, 2819, 18], [1395, 68, 2819, 19, "_Text"], [1395, 73, 2819, 19], [1395, 74, 2819, 19, "default"], [1395, 81, 2819, 23], [1396, 20, 2819, 24, "style"], [1396, 25, 2819, 29], [1396, 27, 2819, 31], [1397, 22, 2819, 33, "fontSize"], [1397, 30, 2819, 41], [1397, 32, 2819, 43], [1397, 34, 2819, 45], [1398, 22, 2819, 47, "color"], [1398, 27, 2819, 52], [1398, 29, 2819, 54], [1399, 20, 2819, 64], [1399, 21, 2819, 66], [1400, 20, 2819, 66, "children"], [1400, 28, 2819, 66], [1400, 30, 2820, 21], [1400, 33, 2820, 24, "response"], [1400, 41, 2820, 32], [1400, 42, 2820, 33, "length"], [1400, 48, 2820, 39], [1401, 18, 2820, 45], [1402, 20, 2820, 45, "fileName"], [1402, 28, 2820, 45], [1402, 30, 2820, 45, "_jsxFileName"], [1402, 42, 2820, 45], [1403, 20, 2820, 45, "lineNumber"], [1403, 30, 2820, 45], [1404, 20, 2820, 45, "columnNumber"], [1404, 32, 2820, 45], [1405, 18, 2820, 45], [1405, 25, 2821, 24], [1405, 26, 2821, 25], [1406, 16, 2821, 25], [1407, 18, 2821, 25, "fileName"], [1407, 26, 2821, 25], [1407, 28, 2821, 25, "_jsxFileName"], [1407, 40, 2821, 25], [1408, 18, 2821, 25, "lineNumber"], [1408, 28, 2821, 25], [1409, 18, 2821, 25, "columnNumber"], [1409, 30, 2821, 25], [1410, 16, 2821, 25], [1410, 23, 2822, 22], [1410, 24, 2822, 23], [1411, 14, 2822, 23], [1412, 16, 2822, 23, "fileName"], [1412, 24, 2822, 23], [1412, 26, 2822, 23, "_jsxFileName"], [1412, 38, 2822, 23], [1413, 16, 2822, 23, "lineNumber"], [1413, 26, 2822, 23], [1414, 16, 2822, 23, "columnNumber"], [1414, 28, 2822, 23], [1415, 14, 2822, 23], [1415, 21, 2823, 20], [1415, 22, 2823, 21], [1416, 12, 2823, 21], [1417, 14, 2823, 21, "fileName"], [1417, 22, 2823, 21], [1417, 24, 2823, 21, "_jsxFileName"], [1417, 36, 2823, 21], [1418, 14, 2823, 21, "lineNumber"], [1418, 24, 2823, 21], [1419, 14, 2823, 21, "columnNumber"], [1419, 26, 2823, 21], [1420, 12, 2823, 21], [1420, 19, 2824, 18], [1420, 20, 2824, 19], [1420, 35, 2829, 12], [1420, 39, 2829, 12, "_jsxDevRuntime"], [1420, 53, 2829, 12], [1420, 54, 2829, 12, "jsxDEV"], [1420, 60, 2829, 12], [1420, 62, 2829, 13, "_View"], [1420, 67, 2829, 13], [1420, 68, 2829, 13, "default"], [1420, 75, 2829, 17], [1421, 14, 2833, 14, "style"], [1421, 19, 2833, 19], [1421, 21, 2833, 21], [1422, 16, 2837, 16, "backgroundColor"], [1422, 31, 2837, 31], [1422, 33, 2837, 33], [1422, 42, 2837, 42], [1423, 16, 2841, 16, "borderRadius"], [1423, 28, 2841, 28], [1423, 30, 2841, 30], [1423, 32, 2841, 32], [1424, 16, 2845, 16, "padding"], [1424, 23, 2845, 23], [1424, 25, 2845, 25], [1424, 27, 2845, 27], [1425, 16, 2849, 16, "marginBottom"], [1425, 28, 2849, 28], [1425, 30, 2849, 30], [1426, 14, 2853, 14], [1426, 15, 2853, 16], [1427, 14, 2853, 16, "children"], [1427, 22, 2853, 16], [1427, 38, 2861, 14], [1427, 42, 2861, 14, "_jsxDevRuntime"], [1427, 56, 2861, 14], [1427, 57, 2861, 14, "jsxDEV"], [1427, 63, 2861, 14], [1427, 65, 2861, 15, "_Text"], [1427, 70, 2861, 15], [1427, 71, 2861, 15, "default"], [1427, 78, 2861, 19], [1428, 16, 2865, 16, "style"], [1428, 21, 2865, 21], [1428, 23, 2865, 23], [1429, 18, 2869, 18, "fontSize"], [1429, 26, 2869, 26], [1429, 28, 2869, 28], [1429, 30, 2869, 30], [1430, 18, 2873, 18, "fontWeight"], [1430, 28, 2873, 28], [1430, 30, 2873, 30], [1430, 35, 2873, 35], [1431, 18, 2877, 18, "color"], [1431, 23, 2877, 23], [1431, 25, 2877, 25], [1431, 34, 2877, 34], [1432, 18, 2881, 18, "marginBottom"], [1432, 30, 2881, 30], [1432, 32, 2881, 32], [1433, 16, 2885, 16], [1433, 17, 2885, 18], [1434, 16, 2885, 18, "children"], [1434, 24, 2885, 18], [1434, 26, 2889, 15], [1435, 14, 2897, 14], [1436, 16, 2897, 14, "fileName"], [1436, 24, 2897, 14], [1436, 26, 2897, 14, "_jsxFileName"], [1436, 38, 2897, 14], [1437, 16, 2897, 14, "lineNumber"], [1437, 26, 2897, 14], [1438, 16, 2897, 14, "columnNumber"], [1438, 28, 2897, 14], [1439, 14, 2897, 14], [1439, 21, 2897, 20], [1439, 22, 2897, 21], [1439, 37, 2901, 14], [1439, 41, 2901, 14, "_jsxDevRuntime"], [1439, 55, 2901, 14], [1439, 56, 2901, 14, "jsxDEV"], [1439, 62, 2901, 14], [1439, 64, 2901, 15, "_Text"], [1439, 69, 2901, 15], [1439, 70, 2901, 15, "default"], [1439, 77, 2901, 19], [1440, 16, 2905, 16, "style"], [1440, 21, 2905, 21], [1440, 23, 2905, 23], [1441, 18, 2909, 18, "fontSize"], [1441, 26, 2909, 26], [1441, 28, 2909, 28], [1441, 30, 2909, 30], [1442, 18, 2913, 18, "color"], [1442, 23, 2913, 23], [1442, 25, 2913, 25], [1442, 34, 2913, 34], [1443, 18, 2917, 18, "lineHeight"], [1443, 28, 2917, 28], [1443, 30, 2917, 30], [1444, 16, 2921, 16], [1444, 17, 2921, 18], [1445, 16, 2921, 18, "children"], [1445, 24, 2921, 18], [1445, 26, 2929, 17], [1446, 14, 2929, 186], [1447, 16, 2929, 186, "fileName"], [1447, 24, 2929, 186], [1447, 26, 2929, 186, "_jsxFileName"], [1447, 38, 2929, 186], [1448, 16, 2929, 186, "lineNumber"], [1448, 26, 2929, 186], [1449, 16, 2929, 186, "columnNumber"], [1449, 28, 2929, 186], [1450, 14, 2929, 186], [1450, 21, 2933, 20], [1450, 22, 2933, 21], [1451, 12, 2933, 21], [1452, 14, 2933, 21, "fileName"], [1452, 22, 2933, 21], [1452, 24, 2933, 21, "_jsxFileName"], [1452, 36, 2933, 21], [1453, 14, 2933, 21, "lineNumber"], [1453, 24, 2933, 21], [1454, 14, 2933, 21, "columnNumber"], [1454, 26, 2933, 21], [1455, 12, 2933, 21], [1455, 19, 2937, 18], [1455, 20, 2937, 19], [1456, 10, 2937, 19], [1457, 12, 2937, 19, "fileName"], [1457, 20, 2937, 19], [1457, 22, 2937, 19, "_jsxFileName"], [1457, 34, 2937, 19], [1458, 12, 2937, 19, "lineNumber"], [1458, 22, 2937, 19], [1459, 12, 2937, 19, "columnNumber"], [1459, 24, 2937, 19], [1460, 10, 2937, 19], [1460, 17, 2941, 16], [1461, 8, 2941, 17], [1462, 10, 2941, 17, "fileName"], [1462, 18, 2941, 17], [1462, 20, 2941, 17, "_jsxFileName"], [1462, 32, 2941, 17], [1463, 10, 2941, 17, "lineNumber"], [1463, 20, 2941, 17], [1464, 10, 2941, 17, "columnNumber"], [1464, 22, 2941, 17], [1465, 8, 2941, 17], [1465, 15, 2945, 20], [1465, 16, 2945, 21], [1465, 31, 2953, 8], [1465, 35, 2953, 8, "_jsxDevRuntime"], [1465, 49, 2953, 8], [1465, 50, 2953, 8, "jsxDEV"], [1465, 56, 2953, 8], [1465, 58, 2953, 9, "_View"], [1465, 63, 2953, 9], [1465, 64, 2953, 9, "default"], [1465, 71, 2953, 13], [1466, 10, 2957, 10, "style"], [1466, 15, 2957, 15], [1466, 17, 2957, 17], [1467, 12, 2961, 12, "position"], [1467, 20, 2961, 20], [1467, 22, 2961, 22], [1467, 32, 2961, 32], [1468, 12, 2965, 12, "bottom"], [1468, 18, 2965, 18], [1468, 20, 2965, 20], [1468, 21, 2965, 21], [1469, 12, 2969, 12, "left"], [1469, 16, 2969, 16], [1469, 18, 2969, 18], [1469, 19, 2969, 19], [1470, 12, 2973, 12, "right"], [1470, 17, 2973, 17], [1470, 19, 2973, 19], [1470, 20, 2973, 20], [1471, 12, 2977, 12, "backgroundColor"], [1471, 27, 2977, 27], [1471, 29, 2977, 29], [1471, 35, 2977, 35], [1472, 12, 2981, 12, "borderTopWidth"], [1472, 26, 2981, 26], [1472, 28, 2981, 28], [1472, 29, 2981, 29], [1473, 12, 2985, 12, "borderTopColor"], [1473, 26, 2985, 26], [1473, 28, 2985, 28], [1473, 37, 2985, 37], [1474, 12, 2989, 12, "padding"], [1474, 19, 2989, 19], [1474, 21, 2989, 21], [1474, 23, 2989, 23], [1475, 12, 2993, 12, "paddingBottom"], [1475, 25, 2993, 25], [1475, 27, 2993, 27, "insets"], [1475, 33, 2993, 33], [1475, 34, 2993, 34, "bottom"], [1475, 40, 2993, 40], [1475, 43, 2993, 43], [1476, 10, 2997, 10], [1476, 11, 2997, 12], [1477, 10, 2997, 12, "children"], [1477, 18, 2997, 12], [1477, 33, 3005, 10], [1477, 37, 3005, 10, "_jsxDevRuntime"], [1477, 51, 3005, 10], [1477, 52, 3005, 10, "jsxDEV"], [1477, 58, 3005, 10], [1477, 60, 3005, 11, "_TouchableOpacity"], [1477, 77, 3005, 11], [1477, 78, 3005, 11, "default"], [1477, 85, 3005, 27], [1478, 12, 3009, 12, "onPress"], [1478, 19, 3009, 19], [1478, 21, 3009, 21, "submitResponse"], [1478, 35, 3009, 36], [1479, 12, 3013, 12, "disabled"], [1479, 20, 3013, 20], [1479, 22, 3013, 22, "submitting"], [1479, 32, 3013, 32], [1479, 36, 3013, 36], [1479, 37, 3013, 37, "cameraResult"], [1479, 49, 3013, 49], [1479, 53, 3013, 53], [1479, 54, 3013, 54, "response"], [1479, 62, 3013, 62], [1479, 63, 3013, 63, "trim"], [1479, 67, 3013, 67], [1479, 68, 3013, 68], [1479, 69, 3013, 70], [1480, 12, 3017, 12, "style"], [1480, 17, 3017, 17], [1480, 19, 3017, 19], [1481, 14, 3021, 14, "backgroundColor"], [1481, 29, 3021, 29], [1481, 31, 3025, 16, "submitting"], [1481, 41, 3025, 26], [1481, 45, 3025, 30], [1481, 46, 3025, 31, "cameraResult"], [1481, 58, 3025, 43], [1481, 62, 3025, 47], [1481, 63, 3025, 48, "response"], [1481, 71, 3025, 56], [1481, 72, 3025, 57, "trim"], [1481, 76, 3025, 61], [1481, 77, 3025, 62], [1481, 78, 3025, 63], [1481, 81, 3029, 20], [1481, 90, 3029, 29], [1481, 93, 3033, 20], [1481, 102, 3033, 29], [1482, 14, 3037, 14, "borderRadius"], [1482, 26, 3037, 26], [1482, 28, 3037, 28], [1482, 30, 3037, 30], [1483, 14, 3041, 14, "padding"], [1483, 21, 3041, 21], [1483, 23, 3041, 23], [1483, 25, 3041, 25], [1484, 14, 3045, 14, "flexDirection"], [1484, 27, 3045, 27], [1484, 29, 3045, 29], [1484, 34, 3045, 34], [1485, 14, 3049, 14, "alignItems"], [1485, 24, 3049, 24], [1485, 26, 3049, 26], [1485, 34, 3049, 34], [1486, 14, 3053, 14, "justifyContent"], [1486, 28, 3053, 28], [1486, 30, 3053, 30], [1487, 12, 3057, 12], [1487, 13, 3057, 14], [1488, 12, 3057, 14, "children"], [1488, 20, 3057, 14], [1488, 22, 3065, 13, "submitting"], [1488, 32, 3065, 23], [1488, 48, 3069, 14], [1488, 52, 3069, 14, "_jsxDevRuntime"], [1488, 66, 3069, 14], [1488, 67, 3069, 14, "jsxDEV"], [1488, 73, 3069, 14], [1488, 75, 3069, 14, "_jsxDevRuntime"], [1488, 89, 3069, 14], [1488, 90, 3069, 14, "Fragment"], [1488, 98, 3069, 14], [1489, 14, 3069, 14, "children"], [1489, 22, 3069, 14], [1489, 38, 3073, 16], [1489, 42, 3073, 16, "_jsxDevRuntime"], [1489, 56, 3073, 16], [1489, 57, 3073, 16, "jsxDEV"], [1489, 63, 3073, 16], [1489, 65, 3073, 17, "_View"], [1489, 70, 3073, 17], [1489, 71, 3073, 17, "default"], [1489, 78, 3073, 21], [1490, 16, 3077, 18, "style"], [1490, 21, 3077, 23], [1490, 23, 3077, 25], [1491, 18, 3081, 20, "width"], [1491, 23, 3081, 25], [1491, 25, 3081, 27], [1491, 27, 3081, 29], [1492, 18, 3085, 20, "height"], [1492, 24, 3085, 26], [1492, 26, 3085, 28], [1492, 28, 3085, 30], [1493, 18, 3089, 20, "borderRadius"], [1493, 30, 3089, 32], [1493, 32, 3089, 34], [1493, 33, 3089, 35], [1494, 18, 3093, 20, "borderWidth"], [1494, 29, 3093, 31], [1494, 31, 3093, 33], [1494, 32, 3093, 34], [1495, 18, 3097, 20, "borderColor"], [1495, 29, 3097, 31], [1495, 31, 3097, 33], [1495, 37, 3097, 39], [1496, 18, 3101, 20, "borderTopColor"], [1496, 32, 3101, 34], [1496, 34, 3101, 36], [1496, 47, 3101, 49], [1497, 18, 3105, 20, "marginRight"], [1497, 29, 3105, 31], [1497, 31, 3105, 33], [1498, 16, 3109, 18], [1499, 14, 3109, 20], [1500, 16, 3109, 20, "fileName"], [1500, 24, 3109, 20], [1500, 26, 3109, 20, "_jsxFileName"], [1500, 38, 3109, 20], [1501, 16, 3109, 20, "lineNumber"], [1501, 26, 3109, 20], [1502, 16, 3109, 20, "columnNumber"], [1502, 28, 3109, 20], [1503, 14, 3109, 20], [1503, 21, 3113, 17], [1503, 22, 3113, 18], [1503, 37, 3117, 16], [1503, 41, 3117, 16, "_jsxDevRuntime"], [1503, 55, 3117, 16], [1503, 56, 3117, 16, "jsxDEV"], [1503, 62, 3117, 16], [1503, 64, 3117, 17, "_Text"], [1503, 69, 3117, 17], [1503, 70, 3117, 17, "default"], [1503, 77, 3117, 21], [1504, 16, 3121, 18, "style"], [1504, 21, 3121, 23], [1504, 23, 3121, 25], [1505, 18, 3121, 27, "fontSize"], [1505, 26, 3121, 35], [1505, 28, 3121, 37], [1505, 30, 3121, 39], [1506, 18, 3121, 41, "fontWeight"], [1506, 28, 3121, 51], [1506, 30, 3121, 53], [1506, 35, 3121, 58], [1507, 18, 3121, 60, "color"], [1507, 23, 3121, 65], [1507, 25, 3121, 67], [1508, 16, 3121, 74], [1508, 17, 3121, 76], [1509, 16, 3121, 76, "children"], [1509, 24, 3121, 76], [1509, 26, 3125, 17], [1510, 14, 3133, 16], [1511, 16, 3133, 16, "fileName"], [1511, 24, 3133, 16], [1511, 26, 3133, 16, "_jsxFileName"], [1511, 38, 3133, 16], [1512, 16, 3133, 16, "lineNumber"], [1512, 26, 3133, 16], [1513, 16, 3133, 16, "columnNumber"], [1513, 28, 3133, 16], [1514, 14, 3133, 16], [1514, 21, 3133, 22], [1514, 22, 3133, 23], [1515, 12, 3133, 23], [1515, 27, 3137, 16], [1515, 28, 3137, 17], [1515, 44, 3145, 14], [1515, 48, 3145, 14, "_jsxDevRuntime"], [1515, 62, 3145, 14], [1515, 63, 3145, 14, "jsxDEV"], [1515, 69, 3145, 14], [1515, 71, 3145, 14, "_jsxDevRuntime"], [1515, 85, 3145, 14], [1515, 86, 3145, 14, "Fragment"], [1515, 94, 3145, 14], [1516, 14, 3145, 14, "children"], [1516, 22, 3145, 14], [1516, 38, 3149, 16], [1516, 42, 3149, 16, "_jsxDevRuntime"], [1516, 56, 3149, 16], [1516, 57, 3149, 16, "jsxDEV"], [1516, 63, 3149, 16], [1516, 65, 3149, 17, "_lucideReactNative"], [1516, 83, 3149, 17], [1516, 84, 3149, 17, "Send"], [1516, 88, 3149, 21], [1517, 16, 3149, 22, "size"], [1517, 20, 3149, 26], [1517, 22, 3149, 28], [1517, 24, 3149, 31], [1518, 16, 3149, 32, "color"], [1518, 21, 3149, 37], [1518, 23, 3149, 38], [1519, 14, 3149, 44], [1520, 16, 3149, 44, "fileName"], [1520, 24, 3149, 44], [1520, 26, 3149, 44, "_jsxFileName"], [1520, 38, 3149, 44], [1521, 16, 3149, 44, "lineNumber"], [1521, 26, 3149, 44], [1522, 16, 3149, 44, "columnNumber"], [1522, 28, 3149, 44], [1523, 14, 3149, 44], [1523, 21, 3149, 46], [1523, 22, 3149, 47], [1523, 37, 3153, 16], [1523, 41, 3153, 16, "_jsxDevRuntime"], [1523, 55, 3153, 16], [1523, 56, 3153, 16, "jsxDEV"], [1523, 62, 3153, 16], [1523, 64, 3153, 17, "_Text"], [1523, 69, 3153, 17], [1523, 70, 3153, 17, "default"], [1523, 77, 3153, 21], [1524, 16, 3157, 18, "style"], [1524, 21, 3157, 23], [1524, 23, 3157, 25], [1525, 18, 3161, 20, "fontSize"], [1525, 26, 3161, 28], [1525, 28, 3161, 30], [1525, 30, 3161, 32], [1526, 18, 3165, 20, "fontWeight"], [1526, 28, 3165, 30], [1526, 30, 3165, 32], [1526, 35, 3165, 37], [1527, 18, 3169, 20, "color"], [1527, 23, 3169, 25], [1527, 25, 3169, 27], [1527, 31, 3169, 33], [1528, 18, 3173, 20, "marginLeft"], [1528, 28, 3173, 30], [1528, 30, 3173, 32], [1529, 16, 3177, 18], [1529, 17, 3177, 20], [1530, 16, 3177, 20, "children"], [1530, 24, 3177, 20], [1530, 26, 3185, 19], [1530, 47, 3185, 40, "question"], [1530, 55, 3185, 48], [1530, 56, 3185, 49, "reward"], [1530, 62, 3185, 55], [1530, 63, 3185, 56, "toFixed"], [1530, 70, 3185, 63], [1530, 71, 3185, 64], [1530, 72, 3185, 65], [1530, 73, 3185, 66], [1531, 14, 3185, 69], [1532, 16, 3185, 69, "fileName"], [1532, 24, 3185, 69], [1532, 26, 3185, 69, "_jsxFileName"], [1532, 38, 3185, 69], [1533, 16, 3185, 69, "lineNumber"], [1533, 26, 3185, 69], [1534, 16, 3185, 69, "columnNumber"], [1534, 28, 3185, 69], [1535, 14, 3185, 69], [1535, 21, 3189, 22], [1535, 22, 3189, 23], [1536, 12, 3189, 23], [1536, 27, 3193, 16], [1537, 10, 3197, 13], [1538, 12, 3197, 13, "fileName"], [1538, 20, 3197, 13], [1538, 22, 3197, 13, "_jsxFileName"], [1538, 34, 3197, 13], [1539, 12, 3197, 13, "lineNumber"], [1539, 22, 3197, 13], [1540, 12, 3197, 13, "columnNumber"], [1540, 24, 3197, 13], [1541, 10, 3197, 13], [1541, 17, 3201, 28], [1542, 8, 3201, 29], [1543, 10, 3201, 29, "fileName"], [1543, 18, 3201, 29], [1543, 20, 3201, 29, "_jsxFileName"], [1543, 32, 3201, 29], [1544, 10, 3201, 29, "lineNumber"], [1544, 20, 3201, 29], [1545, 10, 3201, 29, "columnNumber"], [1545, 22, 3201, 29], [1546, 8, 3201, 29], [1546, 15, 3205, 14], [1546, 16, 3205, 15], [1547, 6, 3205, 15], [1548, 8, 3205, 15, "fileName"], [1548, 16, 3205, 15], [1548, 18, 3205, 15, "_jsxFileName"], [1548, 30, 3205, 15], [1549, 8, 3205, 15, "lineNumber"], [1549, 18, 3205, 15], [1550, 8, 3205, 15, "columnNumber"], [1550, 20, 3205, 15], [1551, 6, 3205, 15], [1551, 13, 3209, 36], [1551, 14, 3209, 37], [1551, 29, 3217, 6], [1551, 33, 3217, 6, "_jsxDevRuntime"], [1551, 47, 3217, 6], [1551, 48, 3217, 6, "jsxDEV"], [1551, 54, 3217, 6], [1551, 56, 3217, 7, "_Modal"], [1551, 62, 3217, 7], [1551, 63, 3217, 7, "default"], [1551, 70, 3217, 12], [1552, 8, 3221, 8, "visible"], [1552, 15, 3221, 15], [1552, 17, 3221, 17, "showCamera"], [1552, 27, 3221, 28], [1553, 8, 3225, 8, "animationType"], [1553, 21, 3225, 21], [1553, 23, 3225, 22], [1553, 30, 3225, 29], [1554, 8, 3229, 8, "presentationStyle"], [1554, 25, 3229, 25], [1554, 27, 3229, 26], [1554, 39, 3229, 38], [1555, 8, 3229, 38, "children"], [1555, 16, 3229, 38], [1555, 18, 3237, 9, "Platform"], [1555, 35, 3237, 17], [1555, 36, 3237, 18, "OS"], [1555, 38, 3237, 20], [1555, 43, 3237, 25], [1555, 48, 3237, 30], [1555, 64, 3241, 10], [1555, 68, 3241, 10, "_jsxDevRuntime"], [1555, 82, 3241, 10], [1555, 83, 3241, 10, "jsxDEV"], [1555, 89, 3241, 10], [1555, 91, 3241, 11, "_EchoCameraWeb"], [1555, 105, 3241, 11], [1555, 106, 3241, 11, "default"], [1555, 113, 3241, 24], [1556, 10, 3245, 12, "userId"], [1556, 16, 3245, 18], [1556, 18, 3245, 19], [1556, 32, 3245, 33], [1557, 10, 3249, 12, "requestId"], [1557, 19, 3249, 21], [1557, 21, 3249, 23, "id"], [1557, 23, 3249, 26], [1558, 10, 3253, 12, "onComplete"], [1558, 20, 3253, 22], [1558, 22, 3253, 24, "handleCameraComplete"], [1558, 42, 3253, 45], [1559, 10, 3257, 12, "onCancel"], [1559, 18, 3257, 20], [1559, 20, 3257, 22, "handleCameraCancel"], [1560, 8, 3257, 41], [1561, 10, 3257, 41, "fileName"], [1561, 18, 3257, 41], [1561, 20, 3257, 41, "_jsxFileName"], [1561, 32, 3257, 41], [1562, 10, 3257, 41, "lineNumber"], [1562, 20, 3257, 41], [1563, 10, 3257, 41, "columnNumber"], [1563, 22, 3257, 41], [1564, 8, 3257, 41], [1564, 15, 3261, 11], [1564, 16, 3261, 12], [1564, 32, 3269, 10], [1564, 36, 3269, 10, "_jsxDevRuntime"], [1564, 50, 3269, 10], [1564, 51, 3269, 10, "jsxDEV"], [1564, 57, 3269, 10], [1564, 59, 3269, 11, "_EchoCameraUnified"], [1564, 77, 3269, 11], [1564, 78, 3269, 11, "default"], [1564, 85, 3269, 28], [1565, 10, 3273, 12, "userId"], [1565, 16, 3273, 18], [1565, 18, 3273, 19], [1565, 32, 3273, 33], [1566, 10, 3277, 12, "requestId"], [1566, 19, 3277, 21], [1566, 21, 3277, 23, "id"], [1566, 23, 3277, 26], [1567, 10, 3281, 12, "onComplete"], [1567, 20, 3281, 22], [1567, 22, 3281, 24, "handleCameraComplete"], [1567, 42, 3281, 45], [1568, 10, 3285, 12, "onCancel"], [1568, 18, 3285, 20], [1568, 20, 3285, 22, "handleCameraCancel"], [1569, 8, 3285, 41], [1570, 10, 3285, 41, "fileName"], [1570, 18, 3285, 41], [1570, 20, 3285, 41, "_jsxFileName"], [1570, 32, 3285, 41], [1571, 10, 3285, 41, "lineNumber"], [1571, 20, 3285, 41], [1572, 10, 3285, 41, "columnNumber"], [1572, 22, 3285, 41], [1573, 8, 3285, 41], [1573, 15, 3289, 11], [1574, 6, 3293, 9], [1575, 8, 3293, 9, "fileName"], [1575, 16, 3293, 9], [1575, 18, 3293, 9, "_jsxFileName"], [1575, 30, 3293, 9], [1576, 8, 3293, 9, "lineNumber"], [1576, 18, 3293, 9], [1577, 8, 3293, 9, "columnNumber"], [1577, 20, 3293, 9], [1578, 6, 3293, 9], [1578, 13, 3297, 13], [1578, 14, 3297, 14], [1579, 4, 3297, 14], [1580, 6, 3297, 14, "fileName"], [1580, 14, 3297, 14], [1580, 16, 3297, 14, "_jsxFileName"], [1580, 28, 3297, 14], [1581, 6, 3297, 14, "lineNumber"], [1581, 16, 3297, 14], [1582, 6, 3297, 14, "columnNumber"], [1582, 18, 3297, 14], [1583, 4, 3297, 14], [1583, 11, 3301, 10], [1583, 12, 3301, 11], [1584, 2, 3309, 0], [1585, 2, 3309, 1, "_s"], [1585, 4, 3309, 1], [1585, 5, 133, 24, "RespondScreen"], [1585, 18, 133, 37], [1586, 4, 133, 37], [1586, 12, 137, 17, "useSafeAreaInsets"], [1586, 57, 137, 34], [1586, 59, 141, 17, "useLocalSearchParams"], [1586, 91, 141, 37], [1587, 2, 141, 37], [1588, 2, 141, 37, "_c"], [1588, 4, 141, 37], [1588, 7, 133, 24, "RespondScreen"], [1588, 20, 133, 37], [1589, 2, 133, 37], [1589, 6, 133, 37, "_c"], [1589, 8, 133, 37], [1590, 2, 133, 37, "$RefreshReg$"], [1590, 14, 133, 37], [1590, 15, 133, 37, "_c"], [1590, 17, 133, 37], [1591, 0, 133, 37], [1591, 3]], "functionMap": {"names": ["<global>", "RespondScreen", "useMemo$argument_0", "calculateDistance", "verifyLocation", "useEffect$argument_0", "handleStartCamera", "handleCameraComplete", "fetch.then$argument_0", "fetch.then.then$argument_0", "fetch.then.then._catch$argument_0", "handleCameraCancel", "submitResponse", "Promise$argument_0", "onPress", "LocationStatus", "getStatusConfig", "TouchableOpacity.props.onPress", "Image.props.onError", "Image.props.onLoad", "Image.props.onLoadStart", "Image.props.onLoadEnd"], "mappings": "AAA;eCoI;qCCgC;GD4B;4BEwG;GFgD;qCGQ;GHgS;YIQ;GJwC;4BKI;GLwE;2CMI;cCkF;SDG;cEC;SFE;eGC;SHE;GNyD;yCUI;GVgB;yBWI;wBC4H,sCD;qBEoC,mBF;GXwC;yBcQ;4BCI;KDoL;uBEoN;eFwB;GdgL;qBgBgG,mBhB;+BiBmhB;uBjBY;8BkBE;uBlBI;mCmBE;uBnBU;iCoBE;uBpBI;iCgB0L;yBhBQ;CDmsB"}}, "type": "js/module"}]}