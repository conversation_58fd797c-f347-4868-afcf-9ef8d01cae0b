{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.resolve = resolve;\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/StyleSheet\"));\n  // Kept in separate file, to avoid name collision with Symbol element\n  function resolve(styleProp, cleanedProps) {\n    if (styleProp) {\n      return _StyleSheet.default ? [styleProp, cleanedProps] :\n      // Compatibility for arrays of styles in plain react web\n      styleProp[Symbol.iterator] ? Object.assign({}, ...styleProp, cleanedProps) : Object.assign({}, styleProp, cleanedProps);\n    } else {\n      return cleanedProps;\n    }\n  }\n});", "lineCount": 18, "map": [[8, 2, 3, 0], [9, 2, 4, 7], [9, 11, 4, 16, "resolve"], [9, 18, 4, 23, "resolve"], [9, 19, 4, 24, "styleProp"], [9, 28, 4, 33], [9, 30, 4, 35, "cleanedProps"], [9, 42, 4, 47], [9, 44, 4, 49], [10, 4, 5, 2], [10, 8, 5, 6, "styleProp"], [10, 17, 5, 15], [10, 19, 5, 17], [11, 6, 6, 4], [11, 13, 6, 11, "StyleSheet"], [11, 32, 6, 21], [11, 35, 6, 24], [11, 36, 6, 25, "styleProp"], [11, 45, 6, 34], [11, 47, 6, 36, "cleanedProps"], [11, 59, 6, 48], [11, 60, 6, 49], [12, 6, 7, 4], [13, 6, 8, 4, "styleProp"], [13, 15, 8, 13], [13, 16, 8, 14, "Symbol"], [13, 22, 8, 20], [13, 23, 8, 21, "iterator"], [13, 31, 8, 29], [13, 32, 8, 30], [13, 35, 8, 33, "Object"], [13, 41, 8, 39], [13, 42, 8, 40, "assign"], [13, 48, 8, 46], [13, 49, 8, 47], [13, 50, 8, 48], [13, 51, 8, 49], [13, 53, 8, 51], [13, 56, 8, 54, "styleProp"], [13, 65, 8, 63], [13, 67, 8, 65, "cleanedProps"], [13, 79, 8, 77], [13, 80, 8, 78], [13, 83, 8, 81, "Object"], [13, 89, 8, 87], [13, 90, 8, 88, "assign"], [13, 96, 8, 94], [13, 97, 8, 95], [13, 98, 8, 96], [13, 99, 8, 97], [13, 101, 8, 99, "styleProp"], [13, 110, 8, 108], [13, 112, 8, 110, "cleanedProps"], [13, 124, 8, 122], [13, 125, 8, 123], [14, 4, 9, 2], [14, 5, 9, 3], [14, 11, 9, 9], [15, 6, 10, 4], [15, 13, 10, 11, "cleanedProps"], [15, 25, 10, 23], [16, 4, 11, 2], [17, 2, 12, 0], [18, 0, 12, 1], [18, 3]], "functionMap": {"names": ["<global>", "resolve"], "mappings": "AAA;OCG;CDQ"}}, "type": "js/module"}]}