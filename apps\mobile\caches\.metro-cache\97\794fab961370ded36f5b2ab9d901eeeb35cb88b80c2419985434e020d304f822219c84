{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 877,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 935,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1029,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1021,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 841,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1639, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "analyzeRegionForFace"], [206, 30, 215, 28], [206, 33, 215, 31, "analyzeRegionForFace"], [206, 34, 215, 32, "data"], [206, 38, 215, 55], [206, 40, 215, 57, "startX"], [206, 46, 215, 71], [206, 48, 215, 73, "startY"], [206, 54, 215, 87], [206, 56, 215, 89, "size"], [206, 60, 215, 101], [206, 62, 215, 103, "imageWidth"], [206, 72, 215, 121], [206, 74, 215, 123, "imageHeight"], [206, 85, 215, 142], [206, 90, 215, 147], [207, 6, 216, 4], [207, 10, 216, 8, "skinPixels"], [207, 20, 216, 18], [207, 23, 216, 21], [207, 24, 216, 22], [208, 6, 217, 4], [208, 10, 217, 8, "totalPixels"], [208, 21, 217, 19], [208, 24, 217, 22], [208, 25, 217, 23], [209, 6, 218, 4], [209, 10, 218, 8, "totalBrightness"], [209, 25, 218, 23], [209, 28, 218, 26], [209, 29, 218, 27], [210, 6, 219, 4], [210, 10, 219, 8, "colorVariations"], [210, 25, 219, 23], [210, 28, 219, 26], [210, 29, 219, 27], [211, 6, 220, 4], [211, 10, 220, 8, "prevR"], [211, 15, 220, 13], [211, 18, 220, 16], [211, 19, 220, 17], [212, 8, 220, 19, "prevG"], [212, 13, 220, 24], [212, 16, 220, 27], [212, 17, 220, 28], [213, 8, 220, 30, "prevB"], [213, 13, 220, 35], [213, 16, 220, 38], [213, 17, 220, 39], [214, 6, 222, 4], [214, 11, 222, 9], [214, 15, 222, 13, "y"], [214, 16, 222, 14], [214, 19, 222, 17, "startY"], [214, 25, 222, 23], [214, 27, 222, 25, "y"], [214, 28, 222, 26], [214, 31, 222, 29, "startY"], [214, 37, 222, 35], [214, 40, 222, 38, "size"], [214, 44, 222, 42], [214, 48, 222, 46, "y"], [214, 49, 222, 47], [214, 52, 222, 50, "imageHeight"], [214, 63, 222, 61], [214, 65, 222, 63, "y"], [214, 66, 222, 64], [214, 68, 222, 66], [214, 70, 222, 68], [215, 8, 223, 6], [215, 13, 223, 11], [215, 17, 223, 15, "x"], [215, 18, 223, 16], [215, 21, 223, 19, "startX"], [215, 27, 223, 25], [215, 29, 223, 27, "x"], [215, 30, 223, 28], [215, 33, 223, 31, "startX"], [215, 39, 223, 37], [215, 42, 223, 40, "size"], [215, 46, 223, 44], [215, 50, 223, 48, "x"], [215, 51, 223, 49], [215, 54, 223, 52, "imageWidth"], [215, 64, 223, 62], [215, 66, 223, 64, "x"], [215, 67, 223, 65], [215, 69, 223, 67], [215, 71, 223, 69], [216, 10, 224, 8], [216, 16, 224, 14, "index"], [216, 21, 224, 19], [216, 24, 224, 22], [216, 25, 224, 23, "y"], [216, 26, 224, 24], [216, 29, 224, 27, "imageWidth"], [216, 39, 224, 37], [216, 42, 224, 40, "x"], [216, 43, 224, 41], [216, 47, 224, 45], [216, 48, 224, 46], [217, 10, 225, 8], [217, 16, 225, 14, "r"], [217, 17, 225, 15], [217, 20, 225, 18, "data"], [217, 24, 225, 22], [217, 25, 225, 23, "index"], [217, 30, 225, 28], [217, 31, 225, 29], [218, 10, 226, 8], [218, 16, 226, 14, "g"], [218, 17, 226, 15], [218, 20, 226, 18, "data"], [218, 24, 226, 22], [218, 25, 226, 23, "index"], [218, 30, 226, 28], [218, 33, 226, 31], [218, 34, 226, 32], [218, 35, 226, 33], [219, 10, 227, 8], [219, 16, 227, 14, "b"], [219, 17, 227, 15], [219, 20, 227, 18, "data"], [219, 24, 227, 22], [219, 25, 227, 23, "index"], [219, 30, 227, 28], [219, 33, 227, 31], [219, 34, 227, 32], [219, 35, 227, 33], [221, 10, 229, 8], [222, 10, 230, 8], [222, 14, 230, 12, "isSkinTone"], [222, 24, 230, 22], [222, 25, 230, 23, "r"], [222, 26, 230, 24], [222, 28, 230, 26, "g"], [222, 29, 230, 27], [222, 31, 230, 29, "b"], [222, 32, 230, 30], [222, 33, 230, 31], [222, 35, 230, 33], [223, 12, 231, 10, "skinPixels"], [223, 22, 231, 20], [223, 24, 231, 22], [224, 10, 232, 8], [226, 10, 234, 8], [227, 10, 235, 8], [227, 16, 235, 14, "brightness"], [227, 26, 235, 24], [227, 29, 235, 27], [227, 30, 235, 28, "r"], [227, 31, 235, 29], [227, 34, 235, 32, "g"], [227, 35, 235, 33], [227, 38, 235, 36, "b"], [227, 39, 235, 37], [227, 44, 235, 42], [227, 45, 235, 43], [227, 48, 235, 46], [227, 51, 235, 49], [227, 52, 235, 50], [228, 10, 236, 8, "totalBrightness"], [228, 25, 236, 23], [228, 29, 236, 27, "brightness"], [228, 39, 236, 37], [230, 10, 238, 8], [231, 10, 239, 8], [231, 14, 239, 12, "totalPixels"], [231, 25, 239, 23], [231, 28, 239, 26], [231, 29, 239, 27], [231, 31, 239, 29], [232, 12, 240, 10], [232, 18, 240, 16, "colorDiff"], [232, 27, 240, 25], [232, 30, 240, 28, "Math"], [232, 34, 240, 32], [232, 35, 240, 33, "abs"], [232, 38, 240, 36], [232, 39, 240, 37, "r"], [232, 40, 240, 38], [232, 43, 240, 41, "prevR"], [232, 48, 240, 46], [232, 49, 240, 47], [232, 52, 240, 50, "Math"], [232, 56, 240, 54], [232, 57, 240, 55, "abs"], [232, 60, 240, 58], [232, 61, 240, 59, "g"], [232, 62, 240, 60], [232, 65, 240, 63, "prevG"], [232, 70, 240, 68], [232, 71, 240, 69], [232, 74, 240, 72, "Math"], [232, 78, 240, 76], [232, 79, 240, 77, "abs"], [232, 82, 240, 80], [232, 83, 240, 81, "b"], [232, 84, 240, 82], [232, 87, 240, 85, "prevB"], [232, 92, 240, 90], [232, 93, 240, 91], [233, 12, 241, 10], [233, 16, 241, 14, "colorDiff"], [233, 25, 241, 23], [233, 28, 241, 26], [233, 30, 241, 28], [233, 32, 241, 30], [234, 14, 241, 32], [235, 14, 242, 12, "colorVariations"], [235, 29, 242, 27], [235, 31, 242, 29], [236, 12, 243, 10], [237, 10, 244, 8], [238, 10, 246, 8, "prevR"], [238, 15, 246, 13], [238, 18, 246, 16, "r"], [238, 19, 246, 17], [239, 10, 246, 19, "prevG"], [239, 15, 246, 24], [239, 18, 246, 27, "g"], [239, 19, 246, 28], [240, 10, 246, 30, "prevB"], [240, 15, 246, 35], [240, 18, 246, 38, "b"], [240, 19, 246, 39], [241, 10, 247, 8, "totalPixels"], [241, 21, 247, 19], [241, 23, 247, 21], [242, 8, 248, 6], [243, 6, 249, 4], [244, 6, 251, 4], [244, 13, 251, 11], [245, 8, 252, 6, "skinRatio"], [245, 17, 252, 15], [245, 19, 252, 17, "skinPixels"], [245, 29, 252, 27], [245, 32, 252, 30, "totalPixels"], [245, 43, 252, 41], [246, 8, 253, 6, "brightness"], [246, 18, 253, 16], [246, 20, 253, 18, "totalBrightness"], [246, 35, 253, 33], [246, 38, 253, 36, "totalPixels"], [246, 49, 253, 47], [247, 8, 254, 6, "variation"], [247, 17, 254, 15], [247, 19, 254, 17, "colorVariations"], [247, 34, 254, 32], [247, 37, 254, 35, "totalPixels"], [247, 48, 254, 46], [248, 8, 255, 6, "hasVariation"], [248, 20, 255, 18], [248, 22, 255, 20, "colorVariations"], [248, 37, 255, 35], [248, 40, 255, 38, "totalPixels"], [248, 51, 255, 49], [248, 54, 255, 52], [248, 57, 255, 55], [248, 58, 255, 56], [249, 6, 256, 4], [249, 7, 256, 5], [250, 4, 257, 2], [250, 5, 257, 3], [251, 4, 259, 2], [251, 10, 259, 8, "isSkinTone"], [251, 20, 259, 18], [251, 23, 259, 21, "isSkinTone"], [251, 24, 259, 22, "r"], [251, 25, 259, 31], [251, 27, 259, 33, "g"], [251, 28, 259, 42], [251, 30, 259, 44, "b"], [251, 31, 259, 53], [251, 36, 259, 58], [252, 6, 260, 4], [253, 6, 261, 4], [253, 13, 262, 6, "r"], [253, 14, 262, 7], [253, 17, 262, 10], [253, 19, 262, 12], [253, 23, 262, 16, "g"], [253, 24, 262, 17], [253, 27, 262, 20], [253, 29, 262, 22], [253, 33, 262, 26, "b"], [253, 34, 262, 27], [253, 37, 262, 30], [253, 39, 262, 32], [253, 43, 263, 6, "r"], [253, 44, 263, 7], [253, 47, 263, 10, "g"], [253, 48, 263, 11], [253, 52, 263, 15, "r"], [253, 53, 263, 16], [253, 56, 263, 19, "b"], [253, 57, 263, 20], [253, 61, 264, 6, "Math"], [253, 65, 264, 10], [253, 66, 264, 11, "abs"], [253, 69, 264, 14], [253, 70, 264, 15, "r"], [253, 71, 264, 16], [253, 74, 264, 19, "g"], [253, 75, 264, 20], [253, 76, 264, 21], [253, 79, 264, 24], [253, 81, 264, 26], [253, 85, 265, 6, "Math"], [253, 89, 265, 10], [253, 90, 265, 11, "max"], [253, 93, 265, 14], [253, 94, 265, 15, "r"], [253, 95, 265, 16], [253, 97, 265, 18, "g"], [253, 98, 265, 19], [253, 100, 265, 21, "b"], [253, 101, 265, 22], [253, 102, 265, 23], [253, 105, 265, 26, "Math"], [253, 109, 265, 30], [253, 110, 265, 31, "min"], [253, 113, 265, 34], [253, 114, 265, 35, "r"], [253, 115, 265, 36], [253, 117, 265, 38, "g"], [253, 118, 265, 39], [253, 120, 265, 41, "b"], [253, 121, 265, 42], [253, 122, 265, 43], [253, 125, 265, 46], [253, 127, 265, 48], [254, 4, 267, 2], [254, 5, 267, 3], [255, 4, 269, 2], [255, 10, 269, 8, "mergeFaceDetections"], [255, 29, 269, 27], [255, 32, 269, 31, "faces"], [255, 37, 269, 43], [255, 41, 269, 48], [256, 6, 270, 4], [256, 10, 270, 8, "faces"], [256, 15, 270, 13], [256, 16, 270, 14, "length"], [256, 22, 270, 20], [256, 26, 270, 24], [256, 27, 270, 25], [256, 29, 270, 27], [256, 36, 270, 34, "faces"], [256, 41, 270, 39], [257, 6, 272, 4], [257, 12, 272, 10, "merged"], [257, 18, 272, 16], [257, 21, 272, 19], [257, 23, 272, 21], [258, 6, 273, 4], [258, 12, 273, 10, "used"], [258, 16, 273, 14], [258, 19, 273, 17], [258, 23, 273, 21, "Set"], [258, 26, 273, 24], [258, 27, 273, 25], [258, 28, 273, 26], [259, 6, 275, 4], [259, 11, 275, 9], [259, 15, 275, 13, "i"], [259, 16, 275, 14], [259, 19, 275, 17], [259, 20, 275, 18], [259, 22, 275, 20, "i"], [259, 23, 275, 21], [259, 26, 275, 24, "faces"], [259, 31, 275, 29], [259, 32, 275, 30, "length"], [259, 38, 275, 36], [259, 40, 275, 38, "i"], [259, 41, 275, 39], [259, 43, 275, 41], [259, 45, 275, 43], [260, 8, 276, 6], [260, 12, 276, 10, "used"], [260, 16, 276, 14], [260, 17, 276, 15, "has"], [260, 20, 276, 18], [260, 21, 276, 19, "i"], [260, 22, 276, 20], [260, 23, 276, 21], [260, 25, 276, 23], [261, 8, 278, 6], [261, 12, 278, 10, "currentFace"], [261, 23, 278, 21], [261, 26, 278, 24, "faces"], [261, 31, 278, 29], [261, 32, 278, 30, "i"], [261, 33, 278, 31], [261, 34, 278, 32], [262, 8, 279, 6, "used"], [262, 12, 279, 10], [262, 13, 279, 11, "add"], [262, 16, 279, 14], [262, 17, 279, 15, "i"], [262, 18, 279, 16], [262, 19, 279, 17], [264, 8, 281, 6], [265, 8, 282, 6], [265, 13, 282, 11], [265, 17, 282, 15, "j"], [265, 18, 282, 16], [265, 21, 282, 19, "i"], [265, 22, 282, 20], [265, 25, 282, 23], [265, 26, 282, 24], [265, 28, 282, 26, "j"], [265, 29, 282, 27], [265, 32, 282, 30, "faces"], [265, 37, 282, 35], [265, 38, 282, 36, "length"], [265, 44, 282, 42], [265, 46, 282, 44, "j"], [265, 47, 282, 45], [265, 49, 282, 47], [265, 51, 282, 49], [266, 10, 283, 8], [266, 14, 283, 12, "used"], [266, 18, 283, 16], [266, 19, 283, 17, "has"], [266, 22, 283, 20], [266, 23, 283, 21, "j"], [266, 24, 283, 22], [266, 25, 283, 23], [266, 27, 283, 25], [267, 10, 285, 8], [267, 16, 285, 14, "overlap"], [267, 23, 285, 21], [267, 26, 285, 24, "calculateOverlap"], [267, 42, 285, 40], [267, 43, 285, 41, "currentFace"], [267, 54, 285, 52], [267, 55, 285, 53, "boundingBox"], [267, 66, 285, 64], [267, 68, 285, 66, "faces"], [267, 73, 285, 71], [267, 74, 285, 72, "j"], [267, 75, 285, 73], [267, 76, 285, 74], [267, 77, 285, 75, "boundingBox"], [267, 88, 285, 86], [267, 89, 285, 87], [268, 10, 286, 8], [268, 14, 286, 12, "overlap"], [268, 21, 286, 19], [268, 24, 286, 22], [268, 27, 286, 25], [268, 29, 286, 27], [269, 12, 286, 29], [270, 12, 287, 10], [271, 12, 288, 10, "currentFace"], [271, 23, 288, 21], [271, 26, 288, 24, "mergeTwoFaces"], [271, 39, 288, 37], [271, 40, 288, 38, "currentFace"], [271, 51, 288, 49], [271, 53, 288, 51, "faces"], [271, 58, 288, 56], [271, 59, 288, 57, "j"], [271, 60, 288, 58], [271, 61, 288, 59], [271, 62, 288, 60], [272, 12, 289, 10, "used"], [272, 16, 289, 14], [272, 17, 289, 15, "add"], [272, 20, 289, 18], [272, 21, 289, 19, "j"], [272, 22, 289, 20], [272, 23, 289, 21], [273, 10, 290, 8], [274, 8, 291, 6], [275, 8, 293, 6, "merged"], [275, 14, 293, 12], [275, 15, 293, 13, "push"], [275, 19, 293, 17], [275, 20, 293, 18, "currentFace"], [275, 31, 293, 29], [275, 32, 293, 30], [276, 6, 294, 4], [277, 6, 296, 4], [277, 13, 296, 11, "merged"], [277, 19, 296, 17], [278, 4, 297, 2], [278, 5, 297, 3], [279, 4, 299, 2], [279, 10, 299, 8, "calculateOverlap"], [279, 26, 299, 24], [279, 29, 299, 27, "calculateOverlap"], [279, 30, 299, 28, "box1"], [279, 34, 299, 37], [279, 36, 299, 39, "box2"], [279, 40, 299, 48], [279, 45, 299, 53], [280, 6, 300, 4], [280, 12, 300, 10, "x1"], [280, 14, 300, 12], [280, 17, 300, 15, "Math"], [280, 21, 300, 19], [280, 22, 300, 20, "max"], [280, 25, 300, 23], [280, 26, 300, 24, "box1"], [280, 30, 300, 28], [280, 31, 300, 29, "xCenter"], [280, 38, 300, 36], [280, 41, 300, 39, "box1"], [280, 45, 300, 43], [280, 46, 300, 44, "width"], [280, 51, 300, 49], [280, 54, 300, 50], [280, 55, 300, 51], [280, 57, 300, 53, "box2"], [280, 61, 300, 57], [280, 62, 300, 58, "xCenter"], [280, 69, 300, 65], [280, 72, 300, 68, "box2"], [280, 76, 300, 72], [280, 77, 300, 73, "width"], [280, 82, 300, 78], [280, 85, 300, 79], [280, 86, 300, 80], [280, 87, 300, 81], [281, 6, 301, 4], [281, 12, 301, 10, "y1"], [281, 14, 301, 12], [281, 17, 301, 15, "Math"], [281, 21, 301, 19], [281, 22, 301, 20, "max"], [281, 25, 301, 23], [281, 26, 301, 24, "box1"], [281, 30, 301, 28], [281, 31, 301, 29, "yCenter"], [281, 38, 301, 36], [281, 41, 301, 39, "box1"], [281, 45, 301, 43], [281, 46, 301, 44, "height"], [281, 52, 301, 50], [281, 55, 301, 51], [281, 56, 301, 52], [281, 58, 301, 54, "box2"], [281, 62, 301, 58], [281, 63, 301, 59, "yCenter"], [281, 70, 301, 66], [281, 73, 301, 69, "box2"], [281, 77, 301, 73], [281, 78, 301, 74, "height"], [281, 84, 301, 80], [281, 87, 301, 81], [281, 88, 301, 82], [281, 89, 301, 83], [282, 6, 302, 4], [282, 12, 302, 10, "x2"], [282, 14, 302, 12], [282, 17, 302, 15, "Math"], [282, 21, 302, 19], [282, 22, 302, 20, "min"], [282, 25, 302, 23], [282, 26, 302, 24, "box1"], [282, 30, 302, 28], [282, 31, 302, 29, "xCenter"], [282, 38, 302, 36], [282, 41, 302, 39, "box1"], [282, 45, 302, 43], [282, 46, 302, 44, "width"], [282, 51, 302, 49], [282, 54, 302, 50], [282, 55, 302, 51], [282, 57, 302, 53, "box2"], [282, 61, 302, 57], [282, 62, 302, 58, "xCenter"], [282, 69, 302, 65], [282, 72, 302, 68, "box2"], [282, 76, 302, 72], [282, 77, 302, 73, "width"], [282, 82, 302, 78], [282, 85, 302, 79], [282, 86, 302, 80], [282, 87, 302, 81], [283, 6, 303, 4], [283, 12, 303, 10, "y2"], [283, 14, 303, 12], [283, 17, 303, 15, "Math"], [283, 21, 303, 19], [283, 22, 303, 20, "min"], [283, 25, 303, 23], [283, 26, 303, 24, "box1"], [283, 30, 303, 28], [283, 31, 303, 29, "yCenter"], [283, 38, 303, 36], [283, 41, 303, 39, "box1"], [283, 45, 303, 43], [283, 46, 303, 44, "height"], [283, 52, 303, 50], [283, 55, 303, 51], [283, 56, 303, 52], [283, 58, 303, 54, "box2"], [283, 62, 303, 58], [283, 63, 303, 59, "yCenter"], [283, 70, 303, 66], [283, 73, 303, 69, "box2"], [283, 77, 303, 73], [283, 78, 303, 74, "height"], [283, 84, 303, 80], [283, 87, 303, 81], [283, 88, 303, 82], [283, 89, 303, 83], [284, 6, 305, 4], [284, 10, 305, 8, "x2"], [284, 12, 305, 10], [284, 16, 305, 14, "x1"], [284, 18, 305, 16], [284, 22, 305, 20, "y2"], [284, 24, 305, 22], [284, 28, 305, 26, "y1"], [284, 30, 305, 28], [284, 32, 305, 30], [284, 39, 305, 37], [284, 40, 305, 38], [285, 6, 307, 4], [285, 12, 307, 10, "overlapArea"], [285, 23, 307, 21], [285, 26, 307, 24], [285, 27, 307, 25, "x2"], [285, 29, 307, 27], [285, 32, 307, 30, "x1"], [285, 34, 307, 32], [285, 39, 307, 37, "y2"], [285, 41, 307, 39], [285, 44, 307, 42, "y1"], [285, 46, 307, 44], [285, 47, 307, 45], [286, 6, 308, 4], [286, 12, 308, 10, "box1Area"], [286, 20, 308, 18], [286, 23, 308, 21, "box1"], [286, 27, 308, 25], [286, 28, 308, 26, "width"], [286, 33, 308, 31], [286, 36, 308, 34, "box1"], [286, 40, 308, 38], [286, 41, 308, 39, "height"], [286, 47, 308, 45], [287, 6, 309, 4], [287, 12, 309, 10, "box2Area"], [287, 20, 309, 18], [287, 23, 309, 21, "box2"], [287, 27, 309, 25], [287, 28, 309, 26, "width"], [287, 33, 309, 31], [287, 36, 309, 34, "box2"], [287, 40, 309, 38], [287, 41, 309, 39, "height"], [287, 47, 309, 45], [288, 6, 311, 4], [288, 13, 311, 11, "overlapArea"], [288, 24, 311, 22], [288, 27, 311, 25, "Math"], [288, 31, 311, 29], [288, 32, 311, 30, "min"], [288, 35, 311, 33], [288, 36, 311, 34, "box1Area"], [288, 44, 311, 42], [288, 46, 311, 44, "box2Area"], [288, 54, 311, 52], [288, 55, 311, 53], [289, 4, 312, 2], [289, 5, 312, 3], [290, 4, 314, 2], [290, 10, 314, 8, "mergeTwoFaces"], [290, 23, 314, 21], [290, 26, 314, 24, "mergeTwoFaces"], [290, 27, 314, 25, "face1"], [290, 32, 314, 35], [290, 34, 314, 37, "face2"], [290, 39, 314, 47], [290, 44, 314, 52], [291, 6, 315, 4], [291, 12, 315, 10, "box1"], [291, 16, 315, 14], [291, 19, 315, 17, "face1"], [291, 24, 315, 22], [291, 25, 315, 23, "boundingBox"], [291, 36, 315, 34], [292, 6, 316, 4], [292, 12, 316, 10, "box2"], [292, 16, 316, 14], [292, 19, 316, 17, "face2"], [292, 24, 316, 22], [292, 25, 316, 23, "boundingBox"], [292, 36, 316, 34], [293, 6, 318, 4], [293, 12, 318, 10, "left"], [293, 16, 318, 14], [293, 19, 318, 17, "Math"], [293, 23, 318, 21], [293, 24, 318, 22, "min"], [293, 27, 318, 25], [293, 28, 318, 26, "box1"], [293, 32, 318, 30], [293, 33, 318, 31, "xCenter"], [293, 40, 318, 38], [293, 43, 318, 41, "box1"], [293, 47, 318, 45], [293, 48, 318, 46, "width"], [293, 53, 318, 51], [293, 56, 318, 52], [293, 57, 318, 53], [293, 59, 318, 55, "box2"], [293, 63, 318, 59], [293, 64, 318, 60, "xCenter"], [293, 71, 318, 67], [293, 74, 318, 70, "box2"], [293, 78, 318, 74], [293, 79, 318, 75, "width"], [293, 84, 318, 80], [293, 87, 318, 81], [293, 88, 318, 82], [293, 89, 318, 83], [294, 6, 319, 4], [294, 12, 319, 10, "right"], [294, 17, 319, 15], [294, 20, 319, 18, "Math"], [294, 24, 319, 22], [294, 25, 319, 23, "max"], [294, 28, 319, 26], [294, 29, 319, 27, "box1"], [294, 33, 319, 31], [294, 34, 319, 32, "xCenter"], [294, 41, 319, 39], [294, 44, 319, 42, "box1"], [294, 48, 319, 46], [294, 49, 319, 47, "width"], [294, 54, 319, 52], [294, 57, 319, 53], [294, 58, 319, 54], [294, 60, 319, 56, "box2"], [294, 64, 319, 60], [294, 65, 319, 61, "xCenter"], [294, 72, 319, 68], [294, 75, 319, 71, "box2"], [294, 79, 319, 75], [294, 80, 319, 76, "width"], [294, 85, 319, 81], [294, 88, 319, 82], [294, 89, 319, 83], [294, 90, 319, 84], [295, 6, 320, 4], [295, 12, 320, 10, "top"], [295, 15, 320, 13], [295, 18, 320, 16, "Math"], [295, 22, 320, 20], [295, 23, 320, 21, "min"], [295, 26, 320, 24], [295, 27, 320, 25, "box1"], [295, 31, 320, 29], [295, 32, 320, 30, "yCenter"], [295, 39, 320, 37], [295, 42, 320, 40, "box1"], [295, 46, 320, 44], [295, 47, 320, 45, "height"], [295, 53, 320, 51], [295, 56, 320, 52], [295, 57, 320, 53], [295, 59, 320, 55, "box2"], [295, 63, 320, 59], [295, 64, 320, 60, "yCenter"], [295, 71, 320, 67], [295, 74, 320, 70, "box2"], [295, 78, 320, 74], [295, 79, 320, 75, "height"], [295, 85, 320, 81], [295, 88, 320, 82], [295, 89, 320, 83], [295, 90, 320, 84], [296, 6, 321, 4], [296, 12, 321, 10, "bottom"], [296, 18, 321, 16], [296, 21, 321, 19, "Math"], [296, 25, 321, 23], [296, 26, 321, 24, "max"], [296, 29, 321, 27], [296, 30, 321, 28, "box1"], [296, 34, 321, 32], [296, 35, 321, 33, "yCenter"], [296, 42, 321, 40], [296, 45, 321, 43, "box1"], [296, 49, 321, 47], [296, 50, 321, 48, "height"], [296, 56, 321, 54], [296, 59, 321, 55], [296, 60, 321, 56], [296, 62, 321, 58, "box2"], [296, 66, 321, 62], [296, 67, 321, 63, "yCenter"], [296, 74, 321, 70], [296, 77, 321, 73, "box2"], [296, 81, 321, 77], [296, 82, 321, 78, "height"], [296, 88, 321, 84], [296, 91, 321, 85], [296, 92, 321, 86], [296, 93, 321, 87], [297, 6, 323, 4], [297, 13, 323, 11], [298, 8, 324, 6, "boundingBox"], [298, 19, 324, 17], [298, 21, 324, 19], [299, 10, 325, 8, "xCenter"], [299, 17, 325, 15], [299, 19, 325, 17], [299, 20, 325, 18, "left"], [299, 24, 325, 22], [299, 27, 325, 25, "right"], [299, 32, 325, 30], [299, 36, 325, 34], [299, 37, 325, 35], [300, 10, 326, 8, "yCenter"], [300, 17, 326, 15], [300, 19, 326, 17], [300, 20, 326, 18, "top"], [300, 23, 326, 21], [300, 26, 326, 24, "bottom"], [300, 32, 326, 30], [300, 36, 326, 34], [300, 37, 326, 35], [301, 10, 327, 8, "width"], [301, 15, 327, 13], [301, 17, 327, 15, "right"], [301, 22, 327, 20], [301, 25, 327, 23, "left"], [301, 29, 327, 27], [302, 10, 328, 8, "height"], [302, 16, 328, 14], [302, 18, 328, 16, "bottom"], [302, 24, 328, 22], [302, 27, 328, 25, "top"], [303, 8, 329, 6], [304, 6, 330, 4], [304, 7, 330, 5], [305, 4, 331, 2], [305, 5, 331, 3], [307, 4, 333, 2], [308, 4, 334, 2], [308, 10, 334, 8, "applyStrongBlur"], [308, 25, 334, 23], [308, 28, 334, 26, "applyStrongBlur"], [308, 29, 334, 27, "ctx"], [308, 32, 334, 56], [308, 34, 334, 58, "x"], [308, 35, 334, 67], [308, 37, 334, 69, "y"], [308, 38, 334, 78], [308, 40, 334, 80, "width"], [308, 45, 334, 93], [308, 47, 334, 95, "height"], [308, 53, 334, 109], [308, 58, 334, 114], [309, 6, 335, 4], [310, 6, 336, 4], [310, 12, 336, 10, "canvasWidth"], [310, 23, 336, 21], [310, 26, 336, 24, "ctx"], [310, 29, 336, 27], [310, 30, 336, 28, "canvas"], [310, 36, 336, 34], [310, 37, 336, 35, "width"], [310, 42, 336, 40], [311, 6, 337, 4], [311, 12, 337, 10, "canvasHeight"], [311, 24, 337, 22], [311, 27, 337, 25, "ctx"], [311, 30, 337, 28], [311, 31, 337, 29, "canvas"], [311, 37, 337, 35], [311, 38, 337, 36, "height"], [311, 44, 337, 42], [312, 6, 339, 4], [312, 12, 339, 10, "clampedX"], [312, 20, 339, 18], [312, 23, 339, 21, "Math"], [312, 27, 339, 25], [312, 28, 339, 26, "max"], [312, 31, 339, 29], [312, 32, 339, 30], [312, 33, 339, 31], [312, 35, 339, 33, "Math"], [312, 39, 339, 37], [312, 40, 339, 38, "min"], [312, 43, 339, 41], [312, 44, 339, 42, "Math"], [312, 48, 339, 46], [312, 49, 339, 47, "floor"], [312, 54, 339, 52], [312, 55, 339, 53, "x"], [312, 56, 339, 54], [312, 57, 339, 55], [312, 59, 339, 57, "canvasWidth"], [312, 70, 339, 68], [312, 73, 339, 71], [312, 74, 339, 72], [312, 75, 339, 73], [312, 76, 339, 74], [313, 6, 340, 4], [313, 12, 340, 10, "clampedY"], [313, 20, 340, 18], [313, 23, 340, 21, "Math"], [313, 27, 340, 25], [313, 28, 340, 26, "max"], [313, 31, 340, 29], [313, 32, 340, 30], [313, 33, 340, 31], [313, 35, 340, 33, "Math"], [313, 39, 340, 37], [313, 40, 340, 38, "min"], [313, 43, 340, 41], [313, 44, 340, 42, "Math"], [313, 48, 340, 46], [313, 49, 340, 47, "floor"], [313, 54, 340, 52], [313, 55, 340, 53, "y"], [313, 56, 340, 54], [313, 57, 340, 55], [313, 59, 340, 57, "canvasHeight"], [313, 71, 340, 69], [313, 74, 340, 72], [313, 75, 340, 73], [313, 76, 340, 74], [313, 77, 340, 75], [314, 6, 341, 4], [314, 12, 341, 10, "<PERSON><PERSON><PERSON><PERSON>"], [314, 24, 341, 22], [314, 27, 341, 25, "Math"], [314, 31, 341, 29], [314, 32, 341, 30, "min"], [314, 35, 341, 33], [314, 36, 341, 34, "Math"], [314, 40, 341, 38], [314, 41, 341, 39, "floor"], [314, 46, 341, 44], [314, 47, 341, 45, "width"], [314, 52, 341, 50], [314, 53, 341, 51], [314, 55, 341, 53, "canvasWidth"], [314, 66, 341, 64], [314, 69, 341, 67, "clampedX"], [314, 77, 341, 75], [314, 78, 341, 76], [315, 6, 342, 4], [315, 12, 342, 10, "clampedHeight"], [315, 25, 342, 23], [315, 28, 342, 26, "Math"], [315, 32, 342, 30], [315, 33, 342, 31, "min"], [315, 36, 342, 34], [315, 37, 342, 35, "Math"], [315, 41, 342, 39], [315, 42, 342, 40, "floor"], [315, 47, 342, 45], [315, 48, 342, 46, "height"], [315, 54, 342, 52], [315, 55, 342, 53], [315, 57, 342, 55, "canvasHeight"], [315, 69, 342, 67], [315, 72, 342, 70, "clampedY"], [315, 80, 342, 78], [315, 81, 342, 79], [316, 6, 344, 4], [316, 10, 344, 8, "<PERSON><PERSON><PERSON><PERSON>"], [316, 22, 344, 20], [316, 26, 344, 24], [316, 27, 344, 25], [316, 31, 344, 29, "clampedHeight"], [316, 44, 344, 42], [316, 48, 344, 46], [316, 49, 344, 47], [316, 51, 344, 49], [317, 8, 345, 6, "console"], [317, 15, 345, 13], [317, 16, 345, 14, "warn"], [317, 20, 345, 18], [317, 21, 345, 19], [317, 72, 345, 70], [317, 73, 345, 71], [318, 8, 346, 6], [319, 6, 347, 4], [321, 6, 349, 4], [322, 6, 350, 4], [322, 12, 350, 10, "imageData"], [322, 21, 350, 19], [322, 24, 350, 22, "ctx"], [322, 27, 350, 25], [322, 28, 350, 26, "getImageData"], [322, 40, 350, 38], [322, 41, 350, 39, "clampedX"], [322, 49, 350, 47], [322, 51, 350, 49, "clampedY"], [322, 59, 350, 57], [322, 61, 350, 59, "<PERSON><PERSON><PERSON><PERSON>"], [322, 73, 350, 71], [322, 75, 350, 73, "clampedHeight"], [322, 88, 350, 86], [322, 89, 350, 87], [323, 6, 351, 4], [323, 12, 351, 10, "data"], [323, 16, 351, 14], [323, 19, 351, 17, "imageData"], [323, 28, 351, 26], [323, 29, 351, 27, "data"], [323, 33, 351, 31], [325, 6, 353, 4], [326, 6, 354, 4], [326, 12, 354, 10, "pixelSize"], [326, 21, 354, 19], [326, 24, 354, 22, "Math"], [326, 28, 354, 26], [326, 29, 354, 27, "max"], [326, 32, 354, 30], [326, 33, 354, 31], [326, 35, 354, 33], [326, 37, 354, 35, "Math"], [326, 41, 354, 39], [326, 42, 354, 40, "min"], [326, 45, 354, 43], [326, 46, 354, 44, "<PERSON><PERSON><PERSON><PERSON>"], [326, 58, 354, 56], [326, 60, 354, 58, "clampedHeight"], [326, 73, 354, 71], [326, 74, 354, 72], [326, 77, 354, 75], [326, 78, 354, 76], [326, 79, 354, 77], [327, 6, 356, 4], [327, 11, 356, 9], [327, 15, 356, 13, "py"], [327, 17, 356, 15], [327, 20, 356, 18], [327, 21, 356, 19], [327, 23, 356, 21, "py"], [327, 25, 356, 23], [327, 28, 356, 26, "clampedHeight"], [327, 41, 356, 39], [327, 43, 356, 41, "py"], [327, 45, 356, 43], [327, 49, 356, 47, "pixelSize"], [327, 58, 356, 56], [327, 60, 356, 58], [328, 8, 357, 6], [328, 13, 357, 11], [328, 17, 357, 15, "px"], [328, 19, 357, 17], [328, 22, 357, 20], [328, 23, 357, 21], [328, 25, 357, 23, "px"], [328, 27, 357, 25], [328, 30, 357, 28, "<PERSON><PERSON><PERSON><PERSON>"], [328, 42, 357, 40], [328, 44, 357, 42, "px"], [328, 46, 357, 44], [328, 50, 357, 48, "pixelSize"], [328, 59, 357, 57], [328, 61, 357, 59], [329, 10, 358, 8], [330, 10, 359, 8], [330, 14, 359, 12, "r"], [330, 15, 359, 13], [330, 18, 359, 16], [330, 19, 359, 17], [331, 12, 359, 19, "g"], [331, 13, 359, 20], [331, 16, 359, 23], [331, 17, 359, 24], [332, 12, 359, 26, "b"], [332, 13, 359, 27], [332, 16, 359, 30], [332, 17, 359, 31], [333, 12, 359, 33, "count"], [333, 17, 359, 38], [333, 20, 359, 41], [333, 21, 359, 42], [334, 10, 361, 8], [334, 15, 361, 13], [334, 19, 361, 17, "dy"], [334, 21, 361, 19], [334, 24, 361, 22], [334, 25, 361, 23], [334, 27, 361, 25, "dy"], [334, 29, 361, 27], [334, 32, 361, 30, "pixelSize"], [334, 41, 361, 39], [334, 45, 361, 43, "py"], [334, 47, 361, 45], [334, 50, 361, 48, "dy"], [334, 52, 361, 50], [334, 55, 361, 53, "clampedHeight"], [334, 68, 361, 66], [334, 70, 361, 68, "dy"], [334, 72, 361, 70], [334, 74, 361, 72], [334, 76, 361, 74], [335, 12, 362, 10], [335, 17, 362, 15], [335, 21, 362, 19, "dx"], [335, 23, 362, 21], [335, 26, 362, 24], [335, 27, 362, 25], [335, 29, 362, 27, "dx"], [335, 31, 362, 29], [335, 34, 362, 32, "pixelSize"], [335, 43, 362, 41], [335, 47, 362, 45, "px"], [335, 49, 362, 47], [335, 52, 362, 50, "dx"], [335, 54, 362, 52], [335, 57, 362, 55, "<PERSON><PERSON><PERSON><PERSON>"], [335, 69, 362, 67], [335, 71, 362, 69, "dx"], [335, 73, 362, 71], [335, 75, 362, 73], [335, 77, 362, 75], [336, 14, 363, 12], [336, 20, 363, 18, "index"], [336, 25, 363, 23], [336, 28, 363, 26], [336, 29, 363, 27], [336, 30, 363, 28, "py"], [336, 32, 363, 30], [336, 35, 363, 33, "dy"], [336, 37, 363, 35], [336, 41, 363, 39, "<PERSON><PERSON><PERSON><PERSON>"], [336, 53, 363, 51], [336, 57, 363, 55, "px"], [336, 59, 363, 57], [336, 62, 363, 60, "dx"], [336, 64, 363, 62], [336, 65, 363, 63], [336, 69, 363, 67], [336, 70, 363, 68], [337, 14, 364, 12, "r"], [337, 15, 364, 13], [337, 19, 364, 17, "data"], [337, 23, 364, 21], [337, 24, 364, 22, "index"], [337, 29, 364, 27], [337, 30, 364, 28], [338, 14, 365, 12, "g"], [338, 15, 365, 13], [338, 19, 365, 17, "data"], [338, 23, 365, 21], [338, 24, 365, 22, "index"], [338, 29, 365, 27], [338, 32, 365, 30], [338, 33, 365, 31], [338, 34, 365, 32], [339, 14, 366, 12, "b"], [339, 15, 366, 13], [339, 19, 366, 17, "data"], [339, 23, 366, 21], [339, 24, 366, 22, "index"], [339, 29, 366, 27], [339, 32, 366, 30], [339, 33, 366, 31], [339, 34, 366, 32], [340, 14, 367, 12, "count"], [340, 19, 367, 17], [340, 21, 367, 19], [341, 12, 368, 10], [342, 10, 369, 8], [343, 10, 371, 8], [343, 14, 371, 12, "count"], [343, 19, 371, 17], [343, 22, 371, 20], [343, 23, 371, 21], [343, 25, 371, 23], [344, 12, 372, 10, "r"], [344, 13, 372, 11], [344, 16, 372, 14, "Math"], [344, 20, 372, 18], [344, 21, 372, 19, "floor"], [344, 26, 372, 24], [344, 27, 372, 25, "r"], [344, 28, 372, 26], [344, 31, 372, 29, "count"], [344, 36, 372, 34], [344, 37, 372, 35], [345, 12, 373, 10, "g"], [345, 13, 373, 11], [345, 16, 373, 14, "Math"], [345, 20, 373, 18], [345, 21, 373, 19, "floor"], [345, 26, 373, 24], [345, 27, 373, 25, "g"], [345, 28, 373, 26], [345, 31, 373, 29, "count"], [345, 36, 373, 34], [345, 37, 373, 35], [346, 12, 374, 10, "b"], [346, 13, 374, 11], [346, 16, 374, 14, "Math"], [346, 20, 374, 18], [346, 21, 374, 19, "floor"], [346, 26, 374, 24], [346, 27, 374, 25, "b"], [346, 28, 374, 26], [346, 31, 374, 29, "count"], [346, 36, 374, 34], [346, 37, 374, 35], [348, 12, 376, 10], [349, 12, 377, 10], [349, 17, 377, 15], [349, 21, 377, 19, "dy"], [349, 23, 377, 21], [349, 26, 377, 24], [349, 27, 377, 25], [349, 29, 377, 27, "dy"], [349, 31, 377, 29], [349, 34, 377, 32, "pixelSize"], [349, 43, 377, 41], [349, 47, 377, 45, "py"], [349, 49, 377, 47], [349, 52, 377, 50, "dy"], [349, 54, 377, 52], [349, 57, 377, 55, "clampedHeight"], [349, 70, 377, 68], [349, 72, 377, 70, "dy"], [349, 74, 377, 72], [349, 76, 377, 74], [349, 78, 377, 76], [350, 14, 378, 12], [350, 19, 378, 17], [350, 23, 378, 21, "dx"], [350, 25, 378, 23], [350, 28, 378, 26], [350, 29, 378, 27], [350, 31, 378, 29, "dx"], [350, 33, 378, 31], [350, 36, 378, 34, "pixelSize"], [350, 45, 378, 43], [350, 49, 378, 47, "px"], [350, 51, 378, 49], [350, 54, 378, 52, "dx"], [350, 56, 378, 54], [350, 59, 378, 57, "<PERSON><PERSON><PERSON><PERSON>"], [350, 71, 378, 69], [350, 73, 378, 71, "dx"], [350, 75, 378, 73], [350, 77, 378, 75], [350, 79, 378, 77], [351, 16, 379, 14], [351, 22, 379, 20, "index"], [351, 27, 379, 25], [351, 30, 379, 28], [351, 31, 379, 29], [351, 32, 379, 30, "py"], [351, 34, 379, 32], [351, 37, 379, 35, "dy"], [351, 39, 379, 37], [351, 43, 379, 41, "<PERSON><PERSON><PERSON><PERSON>"], [351, 55, 379, 53], [351, 59, 379, 57, "px"], [351, 61, 379, 59], [351, 64, 379, 62, "dx"], [351, 66, 379, 64], [351, 67, 379, 65], [351, 71, 379, 69], [351, 72, 379, 70], [352, 16, 380, 14, "data"], [352, 20, 380, 18], [352, 21, 380, 19, "index"], [352, 26, 380, 24], [352, 27, 380, 25], [352, 30, 380, 28, "r"], [352, 31, 380, 29], [353, 16, 381, 14, "data"], [353, 20, 381, 18], [353, 21, 381, 19, "index"], [353, 26, 381, 24], [353, 29, 381, 27], [353, 30, 381, 28], [353, 31, 381, 29], [353, 34, 381, 32, "g"], [353, 35, 381, 33], [354, 16, 382, 14, "data"], [354, 20, 382, 18], [354, 21, 382, 19, "index"], [354, 26, 382, 24], [354, 29, 382, 27], [354, 30, 382, 28], [354, 31, 382, 29], [354, 34, 382, 32, "b"], [354, 35, 382, 33], [355, 16, 383, 14], [356, 14, 384, 12], [357, 12, 385, 10], [358, 10, 386, 8], [359, 8, 387, 6], [360, 6, 388, 4], [362, 6, 390, 4], [363, 6, 391, 4], [363, 11, 391, 9], [363, 15, 391, 13, "i"], [363, 16, 391, 14], [363, 19, 391, 17], [363, 20, 391, 18], [363, 22, 391, 20, "i"], [363, 23, 391, 21], [363, 26, 391, 24], [363, 27, 391, 25], [363, 29, 391, 27, "i"], [363, 30, 391, 28], [363, 32, 391, 30], [363, 34, 391, 32], [364, 8, 392, 6, "applySimpleBlur"], [364, 23, 392, 21], [364, 24, 392, 22, "data"], [364, 28, 392, 26], [364, 30, 392, 28, "<PERSON><PERSON><PERSON><PERSON>"], [364, 42, 392, 40], [364, 44, 392, 42, "clampedHeight"], [364, 57, 392, 55], [364, 58, 392, 56], [365, 6, 393, 4], [367, 6, 395, 4], [368, 6, 396, 4, "ctx"], [368, 9, 396, 7], [368, 10, 396, 8, "putImageData"], [368, 22, 396, 20], [368, 23, 396, 21, "imageData"], [368, 32, 396, 30], [368, 34, 396, 32, "clampedX"], [368, 42, 396, 40], [368, 44, 396, 42, "clampedY"], [368, 52, 396, 50], [368, 53, 396, 51], [370, 6, 398, 4], [371, 6, 399, 4, "ctx"], [371, 9, 399, 7], [371, 10, 399, 8, "fillStyle"], [371, 19, 399, 17], [371, 22, 399, 20], [371, 48, 399, 46], [372, 6, 400, 4, "ctx"], [372, 9, 400, 7], [372, 10, 400, 8, "fillRect"], [372, 18, 400, 16], [372, 19, 400, 17, "clampedX"], [372, 27, 400, 25], [372, 29, 400, 27, "clampedY"], [372, 37, 400, 35], [372, 39, 400, 37, "<PERSON><PERSON><PERSON><PERSON>"], [372, 51, 400, 49], [372, 53, 400, 51, "clampedHeight"], [372, 66, 400, 64], [372, 67, 400, 65], [373, 4, 401, 2], [373, 5, 401, 3], [374, 4, 403, 2], [374, 10, 403, 8, "applySimpleBlur"], [374, 25, 403, 23], [374, 28, 403, 26, "applySimpleBlur"], [374, 29, 403, 27, "data"], [374, 33, 403, 50], [374, 35, 403, 52, "width"], [374, 40, 403, 65], [374, 42, 403, 67, "height"], [374, 48, 403, 81], [374, 53, 403, 86], [375, 6, 404, 4], [375, 12, 404, 10, "original"], [375, 20, 404, 18], [375, 23, 404, 21], [375, 27, 404, 25, "Uint8ClampedArray"], [375, 44, 404, 42], [375, 45, 404, 43, "data"], [375, 49, 404, 47], [375, 50, 404, 48], [376, 6, 406, 4], [376, 11, 406, 9], [376, 15, 406, 13, "y"], [376, 16, 406, 14], [376, 19, 406, 17], [376, 20, 406, 18], [376, 22, 406, 20, "y"], [376, 23, 406, 21], [376, 26, 406, 24, "height"], [376, 32, 406, 30], [376, 35, 406, 33], [376, 36, 406, 34], [376, 38, 406, 36, "y"], [376, 39, 406, 37], [376, 41, 406, 39], [376, 43, 406, 41], [377, 8, 407, 6], [377, 13, 407, 11], [377, 17, 407, 15, "x"], [377, 18, 407, 16], [377, 21, 407, 19], [377, 22, 407, 20], [377, 24, 407, 22, "x"], [377, 25, 407, 23], [377, 28, 407, 26, "width"], [377, 33, 407, 31], [377, 36, 407, 34], [377, 37, 407, 35], [377, 39, 407, 37, "x"], [377, 40, 407, 38], [377, 42, 407, 40], [377, 44, 407, 42], [378, 10, 408, 8], [378, 16, 408, 14, "index"], [378, 21, 408, 19], [378, 24, 408, 22], [378, 25, 408, 23, "y"], [378, 26, 408, 24], [378, 29, 408, 27, "width"], [378, 34, 408, 32], [378, 37, 408, 35, "x"], [378, 38, 408, 36], [378, 42, 408, 40], [378, 43, 408, 41], [380, 10, 410, 8], [381, 10, 411, 8], [381, 14, 411, 12, "r"], [381, 15, 411, 13], [381, 18, 411, 16], [381, 19, 411, 17], [382, 12, 411, 19, "g"], [382, 13, 411, 20], [382, 16, 411, 23], [382, 17, 411, 24], [383, 12, 411, 26, "b"], [383, 13, 411, 27], [383, 16, 411, 30], [383, 17, 411, 31], [384, 10, 412, 8], [384, 15, 412, 13], [384, 19, 412, 17, "dy"], [384, 21, 412, 19], [384, 24, 412, 22], [384, 25, 412, 23], [384, 26, 412, 24], [384, 28, 412, 26, "dy"], [384, 30, 412, 28], [384, 34, 412, 32], [384, 35, 412, 33], [384, 37, 412, 35, "dy"], [384, 39, 412, 37], [384, 41, 412, 39], [384, 43, 412, 41], [385, 12, 413, 10], [385, 17, 413, 15], [385, 21, 413, 19, "dx"], [385, 23, 413, 21], [385, 26, 413, 24], [385, 27, 413, 25], [385, 28, 413, 26], [385, 30, 413, 28, "dx"], [385, 32, 413, 30], [385, 36, 413, 34], [385, 37, 413, 35], [385, 39, 413, 37, "dx"], [385, 41, 413, 39], [385, 43, 413, 41], [385, 45, 413, 43], [386, 14, 414, 12], [386, 20, 414, 18, "neighborIndex"], [386, 33, 414, 31], [386, 36, 414, 34], [386, 37, 414, 35], [386, 38, 414, 36, "y"], [386, 39, 414, 37], [386, 42, 414, 40, "dy"], [386, 44, 414, 42], [386, 48, 414, 46, "width"], [386, 53, 414, 51], [386, 57, 414, 55, "x"], [386, 58, 414, 56], [386, 61, 414, 59, "dx"], [386, 63, 414, 61], [386, 64, 414, 62], [386, 68, 414, 66], [386, 69, 414, 67], [387, 14, 415, 12, "r"], [387, 15, 415, 13], [387, 19, 415, 17, "original"], [387, 27, 415, 25], [387, 28, 415, 26, "neighborIndex"], [387, 41, 415, 39], [387, 42, 415, 40], [388, 14, 416, 12, "g"], [388, 15, 416, 13], [388, 19, 416, 17, "original"], [388, 27, 416, 25], [388, 28, 416, 26, "neighborIndex"], [388, 41, 416, 39], [388, 44, 416, 42], [388, 45, 416, 43], [388, 46, 416, 44], [389, 14, 417, 12, "b"], [389, 15, 417, 13], [389, 19, 417, 17, "original"], [389, 27, 417, 25], [389, 28, 417, 26, "neighborIndex"], [389, 41, 417, 39], [389, 44, 417, 42], [389, 45, 417, 43], [389, 46, 417, 44], [390, 12, 418, 10], [391, 10, 419, 8], [392, 10, 421, 8, "data"], [392, 14, 421, 12], [392, 15, 421, 13, "index"], [392, 20, 421, 18], [392, 21, 421, 19], [392, 24, 421, 22, "r"], [392, 25, 421, 23], [392, 28, 421, 26], [392, 29, 421, 27], [393, 10, 422, 8, "data"], [393, 14, 422, 12], [393, 15, 422, 13, "index"], [393, 20, 422, 18], [393, 23, 422, 21], [393, 24, 422, 22], [393, 25, 422, 23], [393, 28, 422, 26, "g"], [393, 29, 422, 27], [393, 32, 422, 30], [393, 33, 422, 31], [394, 10, 423, 8, "data"], [394, 14, 423, 12], [394, 15, 423, 13, "index"], [394, 20, 423, 18], [394, 23, 423, 21], [394, 24, 423, 22], [394, 25, 423, 23], [394, 28, 423, 26, "b"], [394, 29, 423, 27], [394, 32, 423, 30], [394, 33, 423, 31], [395, 8, 424, 6], [396, 6, 425, 4], [397, 4, 426, 2], [397, 5, 426, 3], [398, 4, 428, 2], [398, 10, 428, 8, "applyFallbackFaceBlur"], [398, 31, 428, 29], [398, 34, 428, 32, "applyFallbackFaceBlur"], [398, 35, 428, 33, "ctx"], [398, 38, 428, 62], [398, 40, 428, 64, "imgWidth"], [398, 48, 428, 80], [398, 50, 428, 82, "imgHeight"], [398, 59, 428, 99], [398, 64, 428, 104], [399, 6, 429, 4, "console"], [399, 13, 429, 11], [399, 14, 429, 12, "log"], [399, 17, 429, 15], [399, 18, 429, 16], [399, 90, 429, 88], [399, 91, 429, 89], [401, 6, 431, 4], [402, 6, 432, 4], [402, 12, 432, 10, "areas"], [402, 17, 432, 15], [402, 20, 432, 18], [403, 6, 433, 6], [404, 6, 434, 6], [405, 8, 434, 8, "x"], [405, 9, 434, 9], [405, 11, 434, 11, "imgWidth"], [405, 19, 434, 19], [405, 22, 434, 22], [405, 26, 434, 26], [406, 8, 434, 28, "y"], [406, 9, 434, 29], [406, 11, 434, 31, "imgHeight"], [406, 20, 434, 40], [406, 23, 434, 43], [406, 27, 434, 47], [407, 8, 434, 49, "w"], [407, 9, 434, 50], [407, 11, 434, 52, "imgWidth"], [407, 19, 434, 60], [407, 22, 434, 63], [407, 25, 434, 66], [408, 8, 434, 68, "h"], [408, 9, 434, 69], [408, 11, 434, 71, "imgHeight"], [408, 20, 434, 80], [408, 23, 434, 83], [409, 6, 434, 87], [409, 7, 434, 88], [410, 6, 435, 6], [411, 6, 436, 6], [412, 8, 436, 8, "x"], [412, 9, 436, 9], [412, 11, 436, 11, "imgWidth"], [412, 19, 436, 19], [412, 22, 436, 22], [412, 25, 436, 25], [413, 8, 436, 27, "y"], [413, 9, 436, 28], [413, 11, 436, 30, "imgHeight"], [413, 20, 436, 39], [413, 23, 436, 42], [413, 26, 436, 45], [414, 8, 436, 47, "w"], [414, 9, 436, 48], [414, 11, 436, 50, "imgWidth"], [414, 19, 436, 58], [414, 22, 436, 61], [414, 26, 436, 65], [415, 8, 436, 67, "h"], [415, 9, 436, 68], [415, 11, 436, 70, "imgHeight"], [415, 20, 436, 79], [415, 23, 436, 82], [416, 6, 436, 86], [416, 7, 436, 87], [417, 6, 437, 6], [418, 6, 438, 6], [419, 8, 438, 8, "x"], [419, 9, 438, 9], [419, 11, 438, 11, "imgWidth"], [419, 19, 438, 19], [419, 22, 438, 22], [419, 26, 438, 26], [420, 8, 438, 28, "y"], [420, 9, 438, 29], [420, 11, 438, 31, "imgHeight"], [420, 20, 438, 40], [420, 23, 438, 43], [420, 26, 438, 46], [421, 8, 438, 48, "w"], [421, 9, 438, 49], [421, 11, 438, 51, "imgWidth"], [421, 19, 438, 59], [421, 22, 438, 62], [421, 26, 438, 66], [422, 8, 438, 68, "h"], [422, 9, 438, 69], [422, 11, 438, 71, "imgHeight"], [422, 20, 438, 80], [422, 23, 438, 83], [423, 6, 438, 87], [423, 7, 438, 88], [423, 8, 439, 5], [424, 6, 441, 4, "areas"], [424, 11, 441, 9], [424, 12, 441, 10, "for<PERSON>ach"], [424, 19, 441, 17], [424, 20, 441, 18], [424, 21, 441, 19, "area"], [424, 25, 441, 23], [424, 27, 441, 25, "index"], [424, 32, 441, 30], [424, 37, 441, 35], [425, 8, 442, 6, "console"], [425, 15, 442, 13], [425, 16, 442, 14, "log"], [425, 19, 442, 17], [425, 20, 442, 18], [425, 65, 442, 63, "index"], [425, 70, 442, 68], [425, 73, 442, 71], [425, 74, 442, 72], [425, 77, 442, 75], [425, 79, 442, 77, "area"], [425, 83, 442, 81], [425, 84, 442, 82], [426, 8, 443, 6, "applyStrongBlur"], [426, 23, 443, 21], [426, 24, 443, 22, "ctx"], [426, 27, 443, 25], [426, 29, 443, 27, "area"], [426, 33, 443, 31], [426, 34, 443, 32, "x"], [426, 35, 443, 33], [426, 37, 443, 35, "area"], [426, 41, 443, 39], [426, 42, 443, 40, "y"], [426, 43, 443, 41], [426, 45, 443, 43, "area"], [426, 49, 443, 47], [426, 50, 443, 48, "w"], [426, 51, 443, 49], [426, 53, 443, 51, "area"], [426, 57, 443, 55], [426, 58, 443, 56, "h"], [426, 59, 443, 57], [426, 60, 443, 58], [427, 6, 444, 4], [427, 7, 444, 5], [427, 8, 444, 6], [428, 4, 445, 2], [428, 5, 445, 3], [430, 4, 447, 2], [431, 4, 448, 2], [431, 10, 448, 8, "capturePhoto"], [431, 22, 448, 20], [431, 25, 448, 23], [431, 29, 448, 23, "useCallback"], [431, 47, 448, 34], [431, 49, 448, 35], [431, 61, 448, 47], [432, 6, 449, 4], [433, 6, 450, 4], [433, 12, 450, 10, "isDev"], [433, 17, 450, 15], [433, 20, 450, 18, "process"], [433, 27, 450, 25], [433, 28, 450, 26, "env"], [433, 31, 450, 29], [433, 32, 450, 30, "NODE_ENV"], [433, 40, 450, 38], [433, 45, 450, 43], [433, 58, 450, 56], [433, 62, 450, 60, "__DEV__"], [433, 69, 450, 67], [434, 6, 452, 4], [434, 10, 452, 8], [434, 11, 452, 9, "cameraRef"], [434, 20, 452, 18], [434, 21, 452, 19, "current"], [434, 28, 452, 26], [434, 32, 452, 30], [434, 33, 452, 31, "isDev"], [434, 38, 452, 36], [434, 40, 452, 38], [435, 8, 453, 6, "<PERSON><PERSON>"], [435, 22, 453, 11], [435, 23, 453, 12, "alert"], [435, 28, 453, 17], [435, 29, 453, 18], [435, 36, 453, 25], [435, 38, 453, 27], [435, 56, 453, 45], [435, 57, 453, 46], [436, 8, 454, 6], [437, 6, 455, 4], [438, 6, 456, 4], [438, 10, 456, 8], [439, 8, 457, 6, "setProcessingState"], [439, 26, 457, 24], [439, 27, 457, 25], [439, 38, 457, 36], [439, 39, 457, 37], [440, 8, 458, 6, "setProcessingProgress"], [440, 29, 458, 27], [440, 30, 458, 28], [440, 32, 458, 30], [440, 33, 458, 31], [441, 8, 459, 6], [442, 8, 460, 6], [443, 8, 461, 6], [444, 8, 462, 6], [444, 14, 462, 12], [444, 18, 462, 16, "Promise"], [444, 25, 462, 23], [444, 26, 462, 24, "resolve"], [444, 33, 462, 31], [444, 37, 462, 35, "setTimeout"], [444, 47, 462, 45], [444, 48, 462, 46, "resolve"], [444, 55, 462, 53], [444, 57, 462, 55], [444, 59, 462, 57], [444, 60, 462, 58], [444, 61, 462, 59], [445, 8, 463, 6], [446, 8, 464, 6], [446, 12, 464, 10, "photo"], [446, 17, 464, 15], [447, 8, 466, 6], [447, 12, 466, 10], [448, 10, 467, 8, "photo"], [448, 15, 467, 13], [448, 18, 467, 16], [448, 24, 467, 22, "cameraRef"], [448, 33, 467, 31], [448, 34, 467, 32, "current"], [448, 41, 467, 39], [448, 42, 467, 40, "takePictureAsync"], [448, 58, 467, 56], [448, 59, 467, 57], [449, 12, 468, 10, "quality"], [449, 19, 468, 17], [449, 21, 468, 19], [449, 24, 468, 22], [450, 12, 469, 10, "base64"], [450, 18, 469, 16], [450, 20, 469, 18], [450, 25, 469, 23], [451, 12, 470, 10, "skipProcessing"], [451, 26, 470, 24], [451, 28, 470, 26], [451, 32, 470, 30], [451, 33, 470, 32], [452, 10, 471, 8], [452, 11, 471, 9], [452, 12, 471, 10], [453, 8, 472, 6], [453, 9, 472, 7], [453, 10, 472, 8], [453, 17, 472, 15, "cameraError"], [453, 28, 472, 26], [453, 30, 472, 28], [454, 10, 473, 8, "console"], [454, 17, 473, 15], [454, 18, 473, 16, "log"], [454, 21, 473, 19], [454, 22, 473, 20], [454, 82, 473, 80], [454, 84, 473, 82, "cameraError"], [454, 95, 473, 93], [454, 96, 473, 94], [455, 10, 474, 8], [456, 10, 475, 8], [456, 14, 475, 12, "isDev"], [456, 19, 475, 17], [456, 21, 475, 19], [457, 12, 476, 10, "photo"], [457, 17, 476, 15], [457, 20, 476, 18], [458, 14, 477, 12, "uri"], [458, 17, 477, 15], [458, 19, 477, 17], [459, 12, 478, 10], [459, 13, 478, 11], [460, 10, 479, 8], [460, 11, 479, 9], [460, 17, 479, 15], [461, 12, 480, 10], [461, 18, 480, 16, "cameraError"], [461, 29, 480, 27], [462, 10, 481, 8], [463, 8, 482, 6], [464, 8, 483, 6], [464, 12, 483, 10], [464, 13, 483, 11, "photo"], [464, 18, 483, 16], [464, 20, 483, 18], [465, 10, 484, 8], [465, 16, 484, 14], [465, 20, 484, 18, "Error"], [465, 25, 484, 23], [465, 26, 484, 24], [465, 51, 484, 49], [465, 52, 484, 50], [466, 8, 485, 6], [467, 8, 486, 6, "console"], [467, 15, 486, 13], [467, 16, 486, 14, "log"], [467, 19, 486, 17], [467, 20, 486, 18], [467, 56, 486, 54], [467, 58, 486, 56, "photo"], [467, 63, 486, 61], [467, 64, 486, 62, "uri"], [467, 67, 486, 65], [467, 68, 486, 66], [468, 8, 487, 6, "setCapturedPhoto"], [468, 24, 487, 22], [468, 25, 487, 23, "photo"], [468, 30, 487, 28], [468, 31, 487, 29, "uri"], [468, 34, 487, 32], [468, 35, 487, 33], [469, 8, 488, 6, "setProcessingProgress"], [469, 29, 488, 27], [469, 30, 488, 28], [469, 32, 488, 30], [469, 33, 488, 31], [470, 8, 489, 6], [471, 8, 490, 6, "console"], [471, 15, 490, 13], [471, 16, 490, 14, "log"], [471, 19, 490, 17], [471, 20, 490, 18], [471, 73, 490, 71], [471, 74, 490, 72], [472, 8, 491, 6], [472, 14, 491, 12, "processImageWithFaceBlur"], [472, 38, 491, 36], [472, 39, 491, 37, "photo"], [472, 44, 491, 42], [472, 45, 491, 43, "uri"], [472, 48, 491, 46], [472, 49, 491, 47], [473, 8, 492, 6, "console"], [473, 15, 492, 13], [473, 16, 492, 14, "log"], [473, 19, 492, 17], [473, 20, 492, 18], [473, 71, 492, 69], [473, 72, 492, 70], [474, 6, 493, 4], [474, 7, 493, 5], [474, 8, 493, 6], [474, 15, 493, 13, "error"], [474, 20, 493, 18], [474, 22, 493, 20], [475, 8, 494, 6, "console"], [475, 15, 494, 13], [475, 16, 494, 14, "error"], [475, 21, 494, 19], [475, 22, 494, 20], [475, 54, 494, 52], [475, 56, 494, 54, "error"], [475, 61, 494, 59], [475, 62, 494, 60], [476, 8, 495, 6, "setErrorMessage"], [476, 23, 495, 21], [476, 24, 495, 22], [476, 68, 495, 66], [476, 69, 495, 67], [477, 8, 496, 6, "setProcessingState"], [477, 26, 496, 24], [477, 27, 496, 25], [477, 34, 496, 32], [477, 35, 496, 33], [478, 6, 497, 4], [479, 4, 498, 2], [479, 5, 498, 3], [479, 7, 498, 5], [479, 9, 498, 7], [479, 10, 498, 8], [480, 4, 499, 2], [481, 4, 500, 2], [481, 10, 500, 8, "processImageWithFaceBlur"], [481, 34, 500, 32], [481, 37, 500, 35], [481, 43, 500, 42, "photoUri"], [481, 51, 500, 58], [481, 55, 500, 63], [482, 6, 501, 4], [482, 10, 501, 8], [483, 8, 502, 6, "console"], [483, 15, 502, 13], [483, 16, 502, 14, "log"], [483, 19, 502, 17], [483, 20, 502, 18], [483, 84, 502, 82], [483, 85, 502, 83], [484, 8, 503, 6, "setProcessingState"], [484, 26, 503, 24], [484, 27, 503, 25], [484, 39, 503, 37], [484, 40, 503, 38], [485, 8, 504, 6, "setProcessingProgress"], [485, 29, 504, 27], [485, 30, 504, 28], [485, 32, 504, 30], [485, 33, 504, 31], [487, 8, 506, 6], [488, 8, 507, 6], [488, 14, 507, 12, "canvas"], [488, 20, 507, 18], [488, 23, 507, 21, "document"], [488, 31, 507, 29], [488, 32, 507, 30, "createElement"], [488, 45, 507, 43], [488, 46, 507, 44], [488, 54, 507, 52], [488, 55, 507, 53], [489, 8, 508, 6], [489, 14, 508, 12, "ctx"], [489, 17, 508, 15], [489, 20, 508, 18, "canvas"], [489, 26, 508, 24], [489, 27, 508, 25, "getContext"], [489, 37, 508, 35], [489, 38, 508, 36], [489, 42, 508, 40], [489, 43, 508, 41], [490, 8, 509, 6], [490, 12, 509, 10], [490, 13, 509, 11, "ctx"], [490, 16, 509, 14], [490, 18, 509, 16], [490, 24, 509, 22], [490, 28, 509, 26, "Error"], [490, 33, 509, 31], [490, 34, 509, 32], [490, 64, 509, 62], [490, 65, 509, 63], [492, 8, 511, 6], [493, 8, 512, 6], [493, 14, 512, 12, "img"], [493, 17, 512, 15], [493, 20, 512, 18], [493, 24, 512, 22, "Image"], [493, 29, 512, 27], [493, 30, 512, 28], [493, 31, 512, 29], [494, 8, 513, 6], [494, 14, 513, 12], [494, 18, 513, 16, "Promise"], [494, 25, 513, 23], [494, 26, 513, 24], [494, 27, 513, 25, "resolve"], [494, 34, 513, 32], [494, 36, 513, 34, "reject"], [494, 42, 513, 40], [494, 47, 513, 45], [495, 10, 514, 8, "img"], [495, 13, 514, 11], [495, 14, 514, 12, "onload"], [495, 20, 514, 18], [495, 23, 514, 21, "resolve"], [495, 30, 514, 28], [496, 10, 515, 8, "img"], [496, 13, 515, 11], [496, 14, 515, 12, "onerror"], [496, 21, 515, 19], [496, 24, 515, 22, "reject"], [496, 30, 515, 28], [497, 10, 516, 8, "img"], [497, 13, 516, 11], [497, 14, 516, 12, "src"], [497, 17, 516, 15], [497, 20, 516, 18, "photoUri"], [497, 28, 516, 26], [498, 8, 517, 6], [498, 9, 517, 7], [498, 10, 517, 8], [500, 8, 519, 6], [501, 8, 520, 6, "canvas"], [501, 14, 520, 12], [501, 15, 520, 13, "width"], [501, 20, 520, 18], [501, 23, 520, 21, "img"], [501, 26, 520, 24], [501, 27, 520, 25, "width"], [501, 32, 520, 30], [502, 8, 521, 6, "canvas"], [502, 14, 521, 12], [502, 15, 521, 13, "height"], [502, 21, 521, 19], [502, 24, 521, 22, "img"], [502, 27, 521, 25], [502, 28, 521, 26, "height"], [502, 34, 521, 32], [503, 8, 522, 6, "console"], [503, 15, 522, 13], [503, 16, 522, 14, "log"], [503, 19, 522, 17], [503, 20, 522, 18], [503, 54, 522, 52], [503, 56, 522, 54], [504, 10, 522, 56, "width"], [504, 15, 522, 61], [504, 17, 522, 63, "img"], [504, 20, 522, 66], [504, 21, 522, 67, "width"], [504, 26, 522, 72], [505, 10, 522, 74, "height"], [505, 16, 522, 80], [505, 18, 522, 82, "img"], [505, 21, 522, 85], [505, 22, 522, 86, "height"], [506, 8, 522, 93], [506, 9, 522, 94], [506, 10, 522, 95], [508, 8, 524, 6], [509, 8, 525, 6, "ctx"], [509, 11, 525, 9], [509, 12, 525, 10, "drawImage"], [509, 21, 525, 19], [509, 22, 525, 20, "img"], [509, 25, 525, 23], [509, 27, 525, 25], [509, 28, 525, 26], [509, 30, 525, 28], [509, 31, 525, 29], [509, 32, 525, 30], [510, 8, 526, 6, "console"], [510, 15, 526, 13], [510, 16, 526, 14, "log"], [510, 19, 526, 17], [510, 20, 526, 18], [510, 72, 526, 70], [510, 73, 526, 71], [511, 8, 528, 6, "setProcessingProgress"], [511, 29, 528, 27], [511, 30, 528, 28], [511, 32, 528, 30], [511, 33, 528, 31], [513, 8, 530, 6], [514, 8, 531, 6], [514, 12, 531, 10, "detectedFaces"], [514, 25, 531, 23], [514, 28, 531, 26], [514, 30, 531, 28], [515, 8, 533, 6, "console"], [515, 15, 533, 13], [515, 16, 533, 14, "log"], [515, 19, 533, 17], [515, 20, 533, 18], [515, 81, 533, 79], [515, 82, 533, 80], [517, 8, 535, 6], [518, 8, 536, 6], [518, 12, 536, 10], [519, 10, 537, 8], [519, 16, 537, 14, "loadTensorFlowFaceDetection"], [519, 43, 537, 41], [519, 44, 537, 42], [519, 45, 537, 43], [520, 10, 538, 8, "detectedFaces"], [520, 23, 538, 21], [520, 26, 538, 24], [520, 32, 538, 30, "detectFacesWithTensorFlow"], [520, 57, 538, 55], [520, 58, 538, 56, "img"], [520, 61, 538, 59], [520, 62, 538, 60], [521, 10, 539, 8, "console"], [521, 17, 539, 15], [521, 18, 539, 16, "log"], [521, 21, 539, 19], [521, 22, 539, 20], [521, 70, 539, 68, "detectedFaces"], [521, 83, 539, 81], [521, 84, 539, 82, "length"], [521, 90, 539, 88], [521, 98, 539, 96], [521, 99, 539, 97], [522, 8, 540, 6], [522, 9, 540, 7], [522, 10, 540, 8], [522, 17, 540, 15, "tensorFlowError"], [522, 32, 540, 30], [522, 34, 540, 32], [523, 10, 541, 8, "console"], [523, 17, 541, 15], [523, 18, 541, 16, "warn"], [523, 22, 541, 20], [523, 23, 541, 21], [523, 61, 541, 59], [523, 63, 541, 61, "tensorFlowError"], [523, 78, 541, 76], [523, 79, 541, 77], [525, 10, 543, 8], [526, 10, 544, 8, "console"], [526, 17, 544, 15], [526, 18, 544, 16, "log"], [526, 21, 544, 19], [526, 22, 544, 20], [526, 86, 544, 84], [526, 87, 544, 85], [527, 10, 545, 8, "detectedFaces"], [527, 23, 545, 21], [527, 26, 545, 24, "detectFacesHeuristic"], [527, 46, 545, 44], [527, 47, 545, 45, "img"], [527, 50, 545, 48], [527, 52, 545, 50, "ctx"], [527, 55, 545, 53], [527, 56, 545, 54], [528, 10, 546, 8, "console"], [528, 17, 546, 15], [528, 18, 546, 16, "log"], [528, 21, 546, 19], [528, 22, 546, 20], [528, 70, 546, 68, "detectedFaces"], [528, 83, 546, 81], [528, 84, 546, 82, "length"], [528, 90, 546, 88], [528, 98, 546, 96], [528, 99, 546, 97], [529, 8, 547, 6], [530, 8, 549, 6, "console"], [530, 15, 549, 13], [530, 16, 549, 14, "log"], [530, 19, 549, 17], [530, 20, 549, 18], [530, 72, 549, 70, "detectedFaces"], [530, 85, 549, 83], [530, 86, 549, 84, "length"], [530, 92, 549, 90], [530, 100, 549, 98], [530, 101, 549, 99], [531, 8, 550, 6], [531, 12, 550, 10, "detectedFaces"], [531, 25, 550, 23], [531, 26, 550, 24, "length"], [531, 32, 550, 30], [531, 35, 550, 33], [531, 36, 550, 34], [531, 38, 550, 36], [532, 10, 551, 8, "console"], [532, 17, 551, 15], [532, 18, 551, 16, "log"], [532, 21, 551, 19], [532, 22, 551, 20], [532, 66, 551, 64], [532, 68, 551, 66, "detectedFaces"], [532, 81, 551, 79], [532, 82, 551, 80, "map"], [532, 85, 551, 83], [532, 86, 551, 84], [532, 87, 551, 85, "face"], [532, 91, 551, 89], [532, 93, 551, 91, "i"], [532, 94, 551, 92], [532, 100, 551, 98], [533, 12, 552, 10, "faceNumber"], [533, 22, 552, 20], [533, 24, 552, 22, "i"], [533, 25, 552, 23], [533, 28, 552, 26], [533, 29, 552, 27], [534, 12, 553, 10, "centerX"], [534, 19, 553, 17], [534, 21, 553, 19, "face"], [534, 25, 553, 23], [534, 26, 553, 24, "boundingBox"], [534, 37, 553, 35], [534, 38, 553, 36, "xCenter"], [534, 45, 553, 43], [535, 12, 554, 10, "centerY"], [535, 19, 554, 17], [535, 21, 554, 19, "face"], [535, 25, 554, 23], [535, 26, 554, 24, "boundingBox"], [535, 37, 554, 35], [535, 38, 554, 36, "yCenter"], [535, 45, 554, 43], [536, 12, 555, 10, "width"], [536, 17, 555, 15], [536, 19, 555, 17, "face"], [536, 23, 555, 21], [536, 24, 555, 22, "boundingBox"], [536, 35, 555, 33], [536, 36, 555, 34, "width"], [536, 41, 555, 39], [537, 12, 556, 10, "height"], [537, 18, 556, 16], [537, 20, 556, 18, "face"], [537, 24, 556, 22], [537, 25, 556, 23, "boundingBox"], [537, 36, 556, 34], [537, 37, 556, 35, "height"], [538, 10, 557, 8], [538, 11, 557, 9], [538, 12, 557, 10], [538, 13, 557, 11], [538, 14, 557, 12], [539, 8, 558, 6], [539, 9, 558, 7], [539, 15, 558, 13], [540, 10, 559, 8, "console"], [540, 17, 559, 15], [540, 18, 559, 16, "log"], [540, 21, 559, 19], [540, 22, 559, 20], [540, 91, 559, 89], [540, 92, 559, 90], [541, 8, 560, 6], [542, 8, 562, 6, "setProcessingProgress"], [542, 29, 562, 27], [542, 30, 562, 28], [542, 32, 562, 30], [542, 33, 562, 31], [544, 8, 564, 6], [545, 8, 565, 6], [545, 12, 565, 10, "detectedFaces"], [545, 25, 565, 23], [545, 26, 565, 24, "length"], [545, 32, 565, 30], [545, 35, 565, 33], [545, 36, 565, 34], [545, 38, 565, 36], [546, 10, 566, 8, "console"], [546, 17, 566, 15], [546, 18, 566, 16, "log"], [546, 21, 566, 19], [546, 22, 566, 20], [546, 61, 566, 59, "detectedFaces"], [546, 74, 566, 72], [546, 75, 566, 73, "length"], [546, 81, 566, 79], [546, 101, 566, 99], [546, 102, 566, 100], [547, 10, 568, 8, "detectedFaces"], [547, 23, 568, 21], [547, 24, 568, 22, "for<PERSON>ach"], [547, 31, 568, 29], [547, 32, 568, 30], [547, 33, 568, 31, "detection"], [547, 42, 568, 40], [547, 44, 568, 42, "index"], [547, 49, 568, 47], [547, 54, 568, 52], [548, 12, 569, 10], [548, 18, 569, 16, "bbox"], [548, 22, 569, 20], [548, 25, 569, 23, "detection"], [548, 34, 569, 32], [548, 35, 569, 33, "boundingBox"], [548, 46, 569, 44], [550, 12, 571, 10], [551, 12, 572, 10], [551, 18, 572, 16, "faceX"], [551, 23, 572, 21], [551, 26, 572, 24, "bbox"], [551, 30, 572, 28], [551, 31, 572, 29, "xCenter"], [551, 38, 572, 36], [551, 41, 572, 39, "img"], [551, 44, 572, 42], [551, 45, 572, 43, "width"], [551, 50, 572, 48], [551, 53, 572, 52, "bbox"], [551, 57, 572, 56], [551, 58, 572, 57, "width"], [551, 63, 572, 62], [551, 66, 572, 65, "img"], [551, 69, 572, 68], [551, 70, 572, 69, "width"], [551, 75, 572, 74], [551, 78, 572, 78], [551, 79, 572, 79], [552, 12, 573, 10], [552, 18, 573, 16, "faceY"], [552, 23, 573, 21], [552, 26, 573, 24, "bbox"], [552, 30, 573, 28], [552, 31, 573, 29, "yCenter"], [552, 38, 573, 36], [552, 41, 573, 39, "img"], [552, 44, 573, 42], [552, 45, 573, 43, "height"], [552, 51, 573, 49], [552, 54, 573, 53, "bbox"], [552, 58, 573, 57], [552, 59, 573, 58, "height"], [552, 65, 573, 64], [552, 68, 573, 67, "img"], [552, 71, 573, 70], [552, 72, 573, 71, "height"], [552, 78, 573, 77], [552, 81, 573, 81], [552, 82, 573, 82], [553, 12, 574, 10], [553, 18, 574, 16, "faceWidth"], [553, 27, 574, 25], [553, 30, 574, 28, "bbox"], [553, 34, 574, 32], [553, 35, 574, 33, "width"], [553, 40, 574, 38], [553, 43, 574, 41, "img"], [553, 46, 574, 44], [553, 47, 574, 45, "width"], [553, 52, 574, 50], [554, 12, 575, 10], [554, 18, 575, 16, "faceHeight"], [554, 28, 575, 26], [554, 31, 575, 29, "bbox"], [554, 35, 575, 33], [554, 36, 575, 34, "height"], [554, 42, 575, 40], [554, 45, 575, 43, "img"], [554, 48, 575, 46], [554, 49, 575, 47, "height"], [554, 55, 575, 53], [556, 12, 577, 10], [557, 12, 578, 10], [557, 18, 578, 16, "padding"], [557, 25, 578, 23], [557, 28, 578, 26], [557, 31, 578, 29], [558, 12, 579, 10], [558, 18, 579, 16, "paddedX"], [558, 25, 579, 23], [558, 28, 579, 26, "Math"], [558, 32, 579, 30], [558, 33, 579, 31, "max"], [558, 36, 579, 34], [558, 37, 579, 35], [558, 38, 579, 36], [558, 40, 579, 38, "faceX"], [558, 45, 579, 43], [558, 48, 579, 46, "faceWidth"], [558, 57, 579, 55], [558, 60, 579, 58, "padding"], [558, 67, 579, 65], [558, 68, 579, 66], [559, 12, 580, 10], [559, 18, 580, 16, "paddedY"], [559, 25, 580, 23], [559, 28, 580, 26, "Math"], [559, 32, 580, 30], [559, 33, 580, 31, "max"], [559, 36, 580, 34], [559, 37, 580, 35], [559, 38, 580, 36], [559, 40, 580, 38, "faceY"], [559, 45, 580, 43], [559, 48, 580, 46, "faceHeight"], [559, 58, 580, 56], [559, 61, 580, 59, "padding"], [559, 68, 580, 66], [559, 69, 580, 67], [560, 12, 581, 10], [560, 18, 581, 16, "<PERSON><PERSON><PERSON><PERSON>"], [560, 29, 581, 27], [560, 32, 581, 30, "Math"], [560, 36, 581, 34], [560, 37, 581, 35, "min"], [560, 40, 581, 38], [560, 41, 581, 39, "img"], [560, 44, 581, 42], [560, 45, 581, 43, "width"], [560, 50, 581, 48], [560, 53, 581, 51, "paddedX"], [560, 60, 581, 58], [560, 62, 581, 60, "faceWidth"], [560, 71, 581, 69], [560, 75, 581, 73], [560, 76, 581, 74], [560, 79, 581, 77], [560, 80, 581, 78], [560, 83, 581, 81, "padding"], [560, 90, 581, 88], [560, 91, 581, 89], [560, 92, 581, 90], [561, 12, 582, 10], [561, 18, 582, 16, "paddedHeight"], [561, 30, 582, 28], [561, 33, 582, 31, "Math"], [561, 37, 582, 35], [561, 38, 582, 36, "min"], [561, 41, 582, 39], [561, 42, 582, 40, "img"], [561, 45, 582, 43], [561, 46, 582, 44, "height"], [561, 52, 582, 50], [561, 55, 582, 53, "paddedY"], [561, 62, 582, 60], [561, 64, 582, 62, "faceHeight"], [561, 74, 582, 72], [561, 78, 582, 76], [561, 79, 582, 77], [561, 82, 582, 80], [561, 83, 582, 81], [561, 86, 582, 84, "padding"], [561, 93, 582, 91], [561, 94, 582, 92], [561, 95, 582, 93], [562, 12, 584, 10, "console"], [562, 19, 584, 17], [562, 20, 584, 18, "log"], [562, 23, 584, 21], [562, 24, 584, 22], [562, 60, 584, 58, "index"], [562, 65, 584, 63], [562, 68, 584, 66], [562, 69, 584, 67], [562, 72, 584, 70], [562, 74, 584, 72], [563, 14, 585, 12, "original"], [563, 22, 585, 20], [563, 24, 585, 22], [564, 16, 585, 24, "x"], [564, 17, 585, 25], [564, 19, 585, 27, "Math"], [564, 23, 585, 31], [564, 24, 585, 32, "round"], [564, 29, 585, 37], [564, 30, 585, 38, "faceX"], [564, 35, 585, 43], [564, 36, 585, 44], [565, 16, 585, 46, "y"], [565, 17, 585, 47], [565, 19, 585, 49, "Math"], [565, 23, 585, 53], [565, 24, 585, 54, "round"], [565, 29, 585, 59], [565, 30, 585, 60, "faceY"], [565, 35, 585, 65], [565, 36, 585, 66], [566, 16, 585, 68, "w"], [566, 17, 585, 69], [566, 19, 585, 71, "Math"], [566, 23, 585, 75], [566, 24, 585, 76, "round"], [566, 29, 585, 81], [566, 30, 585, 82, "faceWidth"], [566, 39, 585, 91], [566, 40, 585, 92], [567, 16, 585, 94, "h"], [567, 17, 585, 95], [567, 19, 585, 97, "Math"], [567, 23, 585, 101], [567, 24, 585, 102, "round"], [567, 29, 585, 107], [567, 30, 585, 108, "faceHeight"], [567, 40, 585, 118], [568, 14, 585, 120], [568, 15, 585, 121], [569, 14, 586, 12, "padded"], [569, 20, 586, 18], [569, 22, 586, 20], [570, 16, 586, 22, "x"], [570, 17, 586, 23], [570, 19, 586, 25, "Math"], [570, 23, 586, 29], [570, 24, 586, 30, "round"], [570, 29, 586, 35], [570, 30, 586, 36, "paddedX"], [570, 37, 586, 43], [570, 38, 586, 44], [571, 16, 586, 46, "y"], [571, 17, 586, 47], [571, 19, 586, 49, "Math"], [571, 23, 586, 53], [571, 24, 586, 54, "round"], [571, 29, 586, 59], [571, 30, 586, 60, "paddedY"], [571, 37, 586, 67], [571, 38, 586, 68], [572, 16, 586, 70, "w"], [572, 17, 586, 71], [572, 19, 586, 73, "Math"], [572, 23, 586, 77], [572, 24, 586, 78, "round"], [572, 29, 586, 83], [572, 30, 586, 84, "<PERSON><PERSON><PERSON><PERSON>"], [572, 41, 586, 95], [572, 42, 586, 96], [573, 16, 586, 98, "h"], [573, 17, 586, 99], [573, 19, 586, 101, "Math"], [573, 23, 586, 105], [573, 24, 586, 106, "round"], [573, 29, 586, 111], [573, 30, 586, 112, "paddedHeight"], [573, 42, 586, 124], [574, 14, 586, 126], [575, 12, 587, 10], [575, 13, 587, 11], [575, 14, 587, 12], [577, 12, 589, 10], [578, 12, 590, 10, "console"], [578, 19, 590, 17], [578, 20, 590, 18, "log"], [578, 23, 590, 21], [578, 24, 590, 22], [578, 70, 590, 68], [578, 72, 590, 70], [579, 14, 591, 12, "width"], [579, 19, 591, 17], [579, 21, 591, 19, "canvas"], [579, 27, 591, 25], [579, 28, 591, 26, "width"], [579, 33, 591, 31], [580, 14, 592, 12, "height"], [580, 20, 592, 18], [580, 22, 592, 20, "canvas"], [580, 28, 592, 26], [580, 29, 592, 27, "height"], [580, 35, 592, 33], [581, 14, 593, 12, "contextValid"], [581, 26, 593, 24], [581, 28, 593, 26], [581, 29, 593, 27], [581, 30, 593, 28, "ctx"], [582, 12, 594, 10], [582, 13, 594, 11], [582, 14, 594, 12], [584, 12, 596, 10], [585, 12, 597, 10, "applyStrongBlur"], [585, 27, 597, 25], [585, 28, 597, 26, "ctx"], [585, 31, 597, 29], [585, 33, 597, 31, "paddedX"], [585, 40, 597, 38], [585, 42, 597, 40, "paddedY"], [585, 49, 597, 47], [585, 51, 597, 49, "<PERSON><PERSON><PERSON><PERSON>"], [585, 62, 597, 60], [585, 64, 597, 62, "paddedHeight"], [585, 76, 597, 74], [585, 77, 597, 75], [587, 12, 599, 10], [588, 12, 600, 10, "console"], [588, 19, 600, 17], [588, 20, 600, 18, "log"], [588, 23, 600, 21], [588, 24, 600, 22], [588, 102, 600, 100], [588, 103, 600, 101], [590, 12, 602, 10], [591, 12, 603, 10], [591, 18, 603, 16, "testImageData"], [591, 31, 603, 29], [591, 34, 603, 32, "ctx"], [591, 37, 603, 35], [591, 38, 603, 36, "getImageData"], [591, 50, 603, 48], [591, 51, 603, 49, "paddedX"], [591, 58, 603, 56], [591, 61, 603, 59], [591, 63, 603, 61], [591, 65, 603, 63, "paddedY"], [591, 72, 603, 70], [591, 75, 603, 73], [591, 77, 603, 75], [591, 79, 603, 77], [591, 81, 603, 79], [591, 83, 603, 81], [591, 85, 603, 83], [591, 86, 603, 84], [592, 12, 604, 10, "console"], [592, 19, 604, 17], [592, 20, 604, 18, "log"], [592, 23, 604, 21], [592, 24, 604, 22], [592, 70, 604, 68], [592, 72, 604, 70], [593, 14, 605, 12, "firstPixel"], [593, 24, 605, 22], [593, 26, 605, 24], [593, 27, 605, 25, "testImageData"], [593, 40, 605, 38], [593, 41, 605, 39, "data"], [593, 45, 605, 43], [593, 46, 605, 44], [593, 47, 605, 45], [593, 48, 605, 46], [593, 50, 605, 48, "testImageData"], [593, 63, 605, 61], [593, 64, 605, 62, "data"], [593, 68, 605, 66], [593, 69, 605, 67], [593, 70, 605, 68], [593, 71, 605, 69], [593, 73, 605, 71, "testImageData"], [593, 86, 605, 84], [593, 87, 605, 85, "data"], [593, 91, 605, 89], [593, 92, 605, 90], [593, 93, 605, 91], [593, 94, 605, 92], [593, 95, 605, 93], [594, 14, 606, 12, "secondPixel"], [594, 25, 606, 23], [594, 27, 606, 25], [594, 28, 606, 26, "testImageData"], [594, 41, 606, 39], [594, 42, 606, 40, "data"], [594, 46, 606, 44], [594, 47, 606, 45], [594, 48, 606, 46], [594, 49, 606, 47], [594, 51, 606, 49, "testImageData"], [594, 64, 606, 62], [594, 65, 606, 63, "data"], [594, 69, 606, 67], [594, 70, 606, 68], [594, 71, 606, 69], [594, 72, 606, 70], [594, 74, 606, 72, "testImageData"], [594, 87, 606, 85], [594, 88, 606, 86, "data"], [594, 92, 606, 90], [594, 93, 606, 91], [594, 94, 606, 92], [594, 95, 606, 93], [595, 12, 607, 10], [595, 13, 607, 11], [595, 14, 607, 12], [596, 12, 609, 10, "console"], [596, 19, 609, 17], [596, 20, 609, 18, "log"], [596, 23, 609, 21], [596, 24, 609, 22], [596, 50, 609, 48, "index"], [596, 55, 609, 53], [596, 58, 609, 56], [596, 59, 609, 57], [596, 79, 609, 77], [596, 80, 609, 78], [597, 10, 610, 8], [597, 11, 610, 9], [597, 12, 610, 10], [598, 10, 612, 8, "console"], [598, 17, 612, 15], [598, 18, 612, 16, "log"], [598, 21, 612, 19], [598, 22, 612, 20], [598, 48, 612, 46, "detectedFaces"], [598, 61, 612, 59], [598, 62, 612, 60, "length"], [598, 68, 612, 66], [598, 104, 612, 102], [598, 105, 612, 103], [599, 8, 613, 6], [599, 9, 613, 7], [599, 15, 613, 13], [600, 10, 614, 8, "console"], [600, 17, 614, 15], [600, 18, 614, 16, "log"], [600, 21, 614, 19], [600, 22, 614, 20], [600, 109, 614, 107], [600, 110, 614, 108], [601, 10, 615, 8], [602, 10, 616, 8, "applyFallbackFaceBlur"], [602, 31, 616, 29], [602, 32, 616, 30, "ctx"], [602, 35, 616, 33], [602, 37, 616, 35, "img"], [602, 40, 616, 38], [602, 41, 616, 39, "width"], [602, 46, 616, 44], [602, 48, 616, 46, "img"], [602, 51, 616, 49], [602, 52, 616, 50, "height"], [602, 58, 616, 56], [602, 59, 616, 57], [603, 8, 617, 6], [604, 8, 619, 6, "setProcessingProgress"], [604, 29, 619, 27], [604, 30, 619, 28], [604, 32, 619, 30], [604, 33, 619, 31], [606, 8, 621, 6], [607, 8, 622, 6, "console"], [607, 15, 622, 13], [607, 16, 622, 14, "log"], [607, 19, 622, 17], [607, 20, 622, 18], [607, 85, 622, 83], [607, 86, 622, 84], [608, 8, 623, 6], [608, 14, 623, 12, "blurredImageBlob"], [608, 30, 623, 28], [608, 33, 623, 31], [608, 39, 623, 37], [608, 43, 623, 41, "Promise"], [608, 50, 623, 48], [608, 51, 623, 56, "resolve"], [608, 58, 623, 63], [608, 62, 623, 68], [609, 10, 624, 8, "canvas"], [609, 16, 624, 14], [609, 17, 624, 15, "toBlob"], [609, 23, 624, 21], [609, 24, 624, 23, "blob"], [609, 28, 624, 27], [609, 32, 624, 32, "resolve"], [609, 39, 624, 39], [609, 40, 624, 40, "blob"], [609, 44, 624, 45], [609, 45, 624, 46], [609, 47, 624, 48], [609, 59, 624, 60], [609, 61, 624, 62], [609, 64, 624, 65], [609, 65, 624, 66], [610, 8, 625, 6], [610, 9, 625, 7], [610, 10, 625, 8], [611, 8, 627, 6], [611, 14, 627, 12, "blurredImageUrl"], [611, 29, 627, 27], [611, 32, 627, 30, "URL"], [611, 35, 627, 33], [611, 36, 627, 34, "createObjectURL"], [611, 51, 627, 49], [611, 52, 627, 50, "blurredImageBlob"], [611, 68, 627, 66], [611, 69, 627, 67], [612, 8, 628, 6, "console"], [612, 15, 628, 13], [612, 16, 628, 14, "log"], [612, 19, 628, 17], [612, 20, 628, 18], [612, 66, 628, 64], [612, 68, 628, 66, "blurredImageUrl"], [612, 83, 628, 81], [612, 84, 628, 82, "substring"], [612, 93, 628, 91], [612, 94, 628, 92], [612, 95, 628, 93], [612, 97, 628, 95], [612, 99, 628, 97], [612, 100, 628, 98], [612, 103, 628, 101], [612, 108, 628, 106], [612, 109, 628, 107], [613, 8, 630, 6, "setProcessingProgress"], [613, 29, 630, 27], [613, 30, 630, 28], [613, 33, 630, 31], [613, 34, 630, 32], [615, 8, 632, 6], [616, 8, 633, 6], [616, 14, 633, 12, "completeProcessing"], [616, 32, 633, 30], [616, 33, 633, 31, "blurredImageUrl"], [616, 48, 633, 46], [616, 49, 633, 47], [617, 6, 635, 4], [617, 7, 635, 5], [617, 8, 635, 6], [617, 15, 635, 13, "error"], [617, 20, 635, 18], [617, 22, 635, 20], [618, 8, 636, 6, "console"], [618, 15, 636, 13], [618, 16, 636, 14, "error"], [618, 21, 636, 19], [618, 22, 636, 20], [618, 57, 636, 55], [618, 59, 636, 57, "error"], [618, 64, 636, 62], [618, 65, 636, 63], [619, 8, 637, 6, "setErrorMessage"], [619, 23, 637, 21], [619, 24, 637, 22], [619, 50, 637, 48], [619, 51, 637, 49], [620, 8, 638, 6, "setProcessingState"], [620, 26, 638, 24], [620, 27, 638, 25], [620, 34, 638, 32], [620, 35, 638, 33], [621, 6, 639, 4], [622, 4, 640, 2], [622, 5, 640, 3], [624, 4, 642, 2], [625, 4, 643, 2], [625, 10, 643, 8, "completeProcessing"], [625, 28, 643, 26], [625, 31, 643, 29], [625, 37, 643, 36, "blurredImageUrl"], [625, 52, 643, 59], [625, 56, 643, 64], [626, 6, 644, 4], [626, 10, 644, 8], [627, 8, 645, 6, "setProcessingState"], [627, 26, 645, 24], [627, 27, 645, 25], [627, 37, 645, 35], [627, 38, 645, 36], [629, 8, 647, 6], [630, 8, 648, 6], [630, 14, 648, 12, "timestamp"], [630, 23, 648, 21], [630, 26, 648, 24, "Date"], [630, 30, 648, 28], [630, 31, 648, 29, "now"], [630, 34, 648, 32], [630, 35, 648, 33], [630, 36, 648, 34], [631, 8, 649, 6], [631, 14, 649, 12, "result"], [631, 20, 649, 18], [631, 23, 649, 21], [632, 10, 650, 8, "imageUrl"], [632, 18, 650, 16], [632, 20, 650, 18, "blurredImageUrl"], [632, 35, 650, 33], [633, 10, 651, 8, "localUri"], [633, 18, 651, 16], [633, 20, 651, 18, "blurredImageUrl"], [633, 35, 651, 33], [634, 10, 652, 8, "challengeCode"], [634, 23, 652, 21], [634, 25, 652, 23, "challengeCode"], [634, 38, 652, 36], [634, 42, 652, 40], [634, 44, 652, 42], [635, 10, 653, 8, "timestamp"], [635, 19, 653, 17], [636, 10, 654, 8, "jobId"], [636, 15, 654, 13], [636, 17, 654, 15], [636, 27, 654, 25, "timestamp"], [636, 36, 654, 34], [636, 38, 654, 36], [637, 10, 655, 8, "status"], [637, 16, 655, 14], [637, 18, 655, 16], [638, 8, 656, 6], [638, 9, 656, 7], [639, 8, 658, 6, "console"], [639, 15, 658, 13], [639, 16, 658, 14, "log"], [639, 19, 658, 17], [639, 20, 658, 18], [639, 100, 658, 98], [639, 102, 658, 100], [640, 10, 659, 8, "imageUrl"], [640, 18, 659, 16], [640, 20, 659, 18, "blurredImageUrl"], [640, 35, 659, 33], [640, 36, 659, 34, "substring"], [640, 45, 659, 43], [640, 46, 659, 44], [640, 47, 659, 45], [640, 49, 659, 47], [640, 51, 659, 49], [640, 52, 659, 50], [640, 55, 659, 53], [640, 60, 659, 58], [641, 10, 660, 8, "timestamp"], [641, 19, 660, 17], [642, 10, 661, 8, "jobId"], [642, 15, 661, 13], [642, 17, 661, 15, "result"], [642, 23, 661, 21], [642, 24, 661, 22, "jobId"], [643, 8, 662, 6], [643, 9, 662, 7], [643, 10, 662, 8], [645, 8, 664, 6], [646, 8, 665, 6, "onComplete"], [646, 18, 665, 16], [646, 19, 665, 17, "result"], [646, 25, 665, 23], [646, 26, 665, 24], [647, 6, 667, 4], [647, 7, 667, 5], [647, 8, 667, 6], [647, 15, 667, 13, "error"], [647, 20, 667, 18], [647, 22, 667, 20], [648, 8, 668, 6, "console"], [648, 15, 668, 13], [648, 16, 668, 14, "error"], [648, 21, 668, 19], [648, 22, 668, 20], [648, 57, 668, 55], [648, 59, 668, 57, "error"], [648, 64, 668, 62], [648, 65, 668, 63], [649, 8, 669, 6, "setErrorMessage"], [649, 23, 669, 21], [649, 24, 669, 22], [649, 56, 669, 54], [649, 57, 669, 55], [650, 8, 670, 6, "setProcessingState"], [650, 26, 670, 24], [650, 27, 670, 25], [650, 34, 670, 32], [650, 35, 670, 33], [651, 6, 671, 4], [652, 4, 672, 2], [652, 5, 672, 3], [654, 4, 674, 2], [655, 4, 675, 2], [655, 10, 675, 8, "triggerServerProcessing"], [655, 33, 675, 31], [655, 36, 675, 34], [655, 42, 675, 34, "triggerServerProcessing"], [655, 43, 675, 41, "privateImageUrl"], [655, 58, 675, 64], [655, 60, 675, 66, "timestamp"], [655, 69, 675, 83], [655, 74, 675, 88], [656, 6, 676, 4], [656, 10, 676, 8], [657, 8, 677, 6, "console"], [657, 15, 677, 13], [657, 16, 677, 14, "log"], [657, 19, 677, 17], [657, 20, 677, 18], [657, 74, 677, 72], [657, 76, 677, 74, "privateImageUrl"], [657, 91, 677, 89], [657, 92, 677, 90], [658, 8, 678, 6, "setProcessingState"], [658, 26, 678, 24], [658, 27, 678, 25], [658, 39, 678, 37], [658, 40, 678, 38], [659, 8, 679, 6, "setProcessingProgress"], [659, 29, 679, 27], [659, 30, 679, 28], [659, 32, 679, 30], [659, 33, 679, 31], [660, 8, 681, 6], [660, 14, 681, 12, "requestBody"], [660, 25, 681, 23], [660, 28, 681, 26], [661, 10, 682, 8, "imageUrl"], [661, 18, 682, 16], [661, 20, 682, 18, "privateImageUrl"], [661, 35, 682, 33], [662, 10, 683, 8, "userId"], [662, 16, 683, 14], [663, 10, 684, 8, "requestId"], [663, 19, 684, 17], [664, 10, 685, 8, "timestamp"], [664, 19, 685, 17], [665, 10, 686, 8, "platform"], [665, 18, 686, 16], [665, 20, 686, 18], [666, 8, 687, 6], [666, 9, 687, 7], [667, 8, 689, 6, "console"], [667, 15, 689, 13], [667, 16, 689, 14, "log"], [667, 19, 689, 17], [667, 20, 689, 18], [667, 65, 689, 63], [667, 67, 689, 65, "requestBody"], [667, 78, 689, 76], [667, 79, 689, 77], [669, 8, 691, 6], [670, 8, 692, 6], [670, 14, 692, 12, "response"], [670, 22, 692, 20], [670, 25, 692, 23], [670, 31, 692, 29, "fetch"], [670, 36, 692, 34], [670, 37, 692, 35], [670, 40, 692, 38, "API_BASE_URL"], [670, 52, 692, 50], [670, 72, 692, 70], [670, 74, 692, 72], [671, 10, 693, 8, "method"], [671, 16, 693, 14], [671, 18, 693, 16], [671, 24, 693, 22], [672, 10, 694, 8, "headers"], [672, 17, 694, 15], [672, 19, 694, 17], [673, 12, 695, 10], [673, 26, 695, 24], [673, 28, 695, 26], [673, 46, 695, 44], [674, 12, 696, 10], [674, 27, 696, 25], [674, 29, 696, 27], [674, 39, 696, 37], [674, 45, 696, 43, "getAuthToken"], [674, 57, 696, 55], [674, 58, 696, 56], [674, 59, 696, 57], [675, 10, 697, 8], [675, 11, 697, 9], [676, 10, 698, 8, "body"], [676, 14, 698, 12], [676, 16, 698, 14, "JSON"], [676, 20, 698, 18], [676, 21, 698, 19, "stringify"], [676, 30, 698, 28], [676, 31, 698, 29, "requestBody"], [676, 42, 698, 40], [677, 8, 699, 6], [677, 9, 699, 7], [677, 10, 699, 8], [678, 8, 701, 6], [678, 12, 701, 10], [678, 13, 701, 11, "response"], [678, 21, 701, 19], [678, 22, 701, 20, "ok"], [678, 24, 701, 22], [678, 26, 701, 24], [679, 10, 702, 8], [679, 16, 702, 14, "errorText"], [679, 25, 702, 23], [679, 28, 702, 26], [679, 34, 702, 32, "response"], [679, 42, 702, 40], [679, 43, 702, 41, "text"], [679, 47, 702, 45], [679, 48, 702, 46], [679, 49, 702, 47], [680, 10, 703, 8, "console"], [680, 17, 703, 15], [680, 18, 703, 16, "error"], [680, 23, 703, 21], [680, 24, 703, 22], [680, 68, 703, 66], [680, 70, 703, 68, "response"], [680, 78, 703, 76], [680, 79, 703, 77, "status"], [680, 85, 703, 83], [680, 87, 703, 85, "errorText"], [680, 96, 703, 94], [680, 97, 703, 95], [681, 10, 704, 8], [681, 16, 704, 14], [681, 20, 704, 18, "Error"], [681, 25, 704, 23], [681, 26, 704, 24], [681, 48, 704, 46, "response"], [681, 56, 704, 54], [681, 57, 704, 55, "status"], [681, 63, 704, 61], [681, 67, 704, 65, "response"], [681, 75, 704, 73], [681, 76, 704, 74, "statusText"], [681, 86, 704, 84], [681, 88, 704, 86], [681, 89, 704, 87], [682, 8, 705, 6], [683, 8, 707, 6], [683, 14, 707, 12, "result"], [683, 20, 707, 18], [683, 23, 707, 21], [683, 29, 707, 27, "response"], [683, 37, 707, 35], [683, 38, 707, 36, "json"], [683, 42, 707, 40], [683, 43, 707, 41], [683, 44, 707, 42], [684, 8, 708, 6, "console"], [684, 15, 708, 13], [684, 16, 708, 14, "log"], [684, 19, 708, 17], [684, 20, 708, 18], [684, 68, 708, 66], [684, 70, 708, 68, "result"], [684, 76, 708, 74], [684, 77, 708, 75], [685, 8, 710, 6], [685, 12, 710, 10], [685, 13, 710, 11, "result"], [685, 19, 710, 17], [685, 20, 710, 18, "jobId"], [685, 25, 710, 23], [685, 27, 710, 25], [686, 10, 711, 8], [686, 16, 711, 14], [686, 20, 711, 18, "Error"], [686, 25, 711, 23], [686, 26, 711, 24], [686, 70, 711, 68], [686, 71, 711, 69], [687, 8, 712, 6], [689, 8, 714, 6], [690, 8, 715, 6], [690, 14, 715, 12, "pollForCompletion"], [690, 31, 715, 29], [690, 32, 715, 30, "result"], [690, 38, 715, 36], [690, 39, 715, 37, "jobId"], [690, 44, 715, 42], [690, 46, 715, 44, "timestamp"], [690, 55, 715, 53], [690, 56, 715, 54], [691, 6, 716, 4], [691, 7, 716, 5], [691, 8, 716, 6], [691, 15, 716, 13, "error"], [691, 20, 716, 18], [691, 22, 716, 20], [692, 8, 717, 6, "console"], [692, 15, 717, 13], [692, 16, 717, 14, "error"], [692, 21, 717, 19], [692, 22, 717, 20], [692, 57, 717, 55], [692, 59, 717, 57, "error"], [692, 64, 717, 62], [692, 65, 717, 63], [693, 8, 718, 6, "setErrorMessage"], [693, 23, 718, 21], [693, 24, 718, 22], [693, 52, 718, 50, "error"], [693, 57, 718, 55], [693, 58, 718, 56, "message"], [693, 65, 718, 63], [693, 67, 718, 65], [693, 68, 718, 66], [694, 8, 719, 6, "setProcessingState"], [694, 26, 719, 24], [694, 27, 719, 25], [694, 34, 719, 32], [694, 35, 719, 33], [695, 6, 720, 4], [696, 4, 721, 2], [696, 5, 721, 3], [697, 4, 722, 2], [698, 4, 723, 2], [698, 10, 723, 8, "pollForCompletion"], [698, 27, 723, 25], [698, 30, 723, 28], [698, 36, 723, 28, "pollForCompletion"], [698, 37, 723, 35, "jobId"], [698, 42, 723, 48], [698, 44, 723, 50, "timestamp"], [698, 53, 723, 67], [698, 55, 723, 69, "attempts"], [698, 63, 723, 77], [698, 66, 723, 80], [698, 67, 723, 81], [698, 72, 723, 86], [699, 6, 724, 4], [699, 12, 724, 10, "MAX_ATTEMPTS"], [699, 24, 724, 22], [699, 27, 724, 25], [699, 29, 724, 27], [699, 30, 724, 28], [699, 31, 724, 29], [700, 6, 725, 4], [700, 12, 725, 10, "POLL_INTERVAL"], [700, 25, 725, 23], [700, 28, 725, 26], [700, 32, 725, 30], [700, 33, 725, 31], [700, 34, 725, 32], [702, 6, 727, 4, "console"], [702, 13, 727, 11], [702, 14, 727, 12, "log"], [702, 17, 727, 15], [702, 18, 727, 16], [702, 53, 727, 51, "attempts"], [702, 61, 727, 59], [702, 64, 727, 62], [702, 65, 727, 63], [702, 69, 727, 67, "MAX_ATTEMPTS"], [702, 81, 727, 79], [702, 93, 727, 91, "jobId"], [702, 98, 727, 96], [702, 100, 727, 98], [702, 101, 727, 99], [703, 6, 729, 4], [703, 10, 729, 8, "attempts"], [703, 18, 729, 16], [703, 22, 729, 20, "MAX_ATTEMPTS"], [703, 34, 729, 32], [703, 36, 729, 34], [704, 8, 730, 6, "console"], [704, 15, 730, 13], [704, 16, 730, 14, "error"], [704, 21, 730, 19], [704, 22, 730, 20], [704, 75, 730, 73], [704, 76, 730, 74], [705, 8, 731, 6, "setErrorMessage"], [705, 23, 731, 21], [705, 24, 731, 22], [705, 63, 731, 61], [705, 64, 731, 62], [706, 8, 732, 6, "setProcessingState"], [706, 26, 732, 24], [706, 27, 732, 25], [706, 34, 732, 32], [706, 35, 732, 33], [707, 8, 733, 6], [708, 6, 734, 4], [709, 6, 736, 4], [709, 10, 736, 8], [710, 8, 737, 6], [710, 14, 737, 12, "response"], [710, 22, 737, 20], [710, 25, 737, 23], [710, 31, 737, 29, "fetch"], [710, 36, 737, 34], [710, 37, 737, 35], [710, 40, 737, 38, "API_BASE_URL"], [710, 52, 737, 50], [710, 75, 737, 73, "jobId"], [710, 80, 737, 78], [710, 82, 737, 80], [710, 84, 737, 82], [711, 10, 738, 8, "headers"], [711, 17, 738, 15], [711, 19, 738, 17], [712, 12, 739, 10], [712, 27, 739, 25], [712, 29, 739, 27], [712, 39, 739, 37], [712, 45, 739, 43, "getAuthToken"], [712, 57, 739, 55], [712, 58, 739, 56], [712, 59, 739, 57], [713, 10, 740, 8], [714, 8, 741, 6], [714, 9, 741, 7], [714, 10, 741, 8], [715, 8, 743, 6], [715, 12, 743, 10], [715, 13, 743, 11, "response"], [715, 21, 743, 19], [715, 22, 743, 20, "ok"], [715, 24, 743, 22], [715, 26, 743, 24], [716, 10, 744, 8], [716, 16, 744, 14], [716, 20, 744, 18, "Error"], [716, 25, 744, 23], [716, 26, 744, 24], [716, 34, 744, 32, "response"], [716, 42, 744, 40], [716, 43, 744, 41, "status"], [716, 49, 744, 47], [716, 54, 744, 52, "response"], [716, 62, 744, 60], [716, 63, 744, 61, "statusText"], [716, 73, 744, 71], [716, 75, 744, 73], [716, 76, 744, 74], [717, 8, 745, 6], [718, 8, 747, 6], [718, 14, 747, 12, "status"], [718, 20, 747, 18], [718, 23, 747, 21], [718, 29, 747, 27, "response"], [718, 37, 747, 35], [718, 38, 747, 36, "json"], [718, 42, 747, 40], [718, 43, 747, 41], [718, 44, 747, 42], [719, 8, 748, 6, "console"], [719, 15, 748, 13], [719, 16, 748, 14, "log"], [719, 19, 748, 17], [719, 20, 748, 18], [719, 54, 748, 52], [719, 56, 748, 54, "status"], [719, 62, 748, 60], [719, 63, 748, 61], [720, 8, 750, 6], [720, 12, 750, 10, "status"], [720, 18, 750, 16], [720, 19, 750, 17, "status"], [720, 25, 750, 23], [720, 30, 750, 28], [720, 41, 750, 39], [720, 43, 750, 41], [721, 10, 751, 8, "console"], [721, 17, 751, 15], [721, 18, 751, 16, "log"], [721, 21, 751, 19], [721, 22, 751, 20], [721, 73, 751, 71], [721, 74, 751, 72], [722, 10, 752, 8, "setProcessingProgress"], [722, 31, 752, 29], [722, 32, 752, 30], [722, 35, 752, 33], [722, 36, 752, 34], [723, 10, 753, 8, "setProcessingState"], [723, 28, 753, 26], [723, 29, 753, 27], [723, 40, 753, 38], [723, 41, 753, 39], [724, 10, 754, 8], [725, 10, 755, 8], [725, 16, 755, 14, "result"], [725, 22, 755, 20], [725, 25, 755, 23], [726, 12, 756, 10, "imageUrl"], [726, 20, 756, 18], [726, 22, 756, 20, "status"], [726, 28, 756, 26], [726, 29, 756, 27, "publicUrl"], [726, 38, 756, 36], [727, 12, 756, 38], [728, 12, 757, 10, "localUri"], [728, 20, 757, 18], [728, 22, 757, 20, "capturedPhoto"], [728, 35, 757, 33], [728, 39, 757, 37, "status"], [728, 45, 757, 43], [728, 46, 757, 44, "publicUrl"], [728, 55, 757, 53], [729, 12, 757, 55], [730, 12, 758, 10, "challengeCode"], [730, 25, 758, 23], [730, 27, 758, 25, "challengeCode"], [730, 40, 758, 38], [730, 44, 758, 42], [730, 46, 758, 44], [731, 12, 759, 10, "timestamp"], [731, 21, 759, 19], [732, 12, 760, 10, "processingStatus"], [732, 28, 760, 26], [732, 30, 760, 28], [733, 10, 761, 8], [733, 11, 761, 9], [734, 10, 762, 8, "console"], [734, 17, 762, 15], [734, 18, 762, 16, "log"], [734, 21, 762, 19], [734, 22, 762, 20], [734, 57, 762, 55], [734, 59, 762, 57, "result"], [734, 65, 762, 63], [734, 66, 762, 64], [735, 10, 763, 8, "onComplete"], [735, 20, 763, 18], [735, 21, 763, 19, "result"], [735, 27, 763, 25], [735, 28, 763, 26], [736, 10, 764, 8], [737, 8, 765, 6], [737, 9, 765, 7], [737, 15, 765, 13], [737, 19, 765, 17, "status"], [737, 25, 765, 23], [737, 26, 765, 24, "status"], [737, 32, 765, 30], [737, 37, 765, 35], [737, 45, 765, 43], [737, 47, 765, 45], [738, 10, 766, 8, "console"], [738, 17, 766, 15], [738, 18, 766, 16, "error"], [738, 23, 766, 21], [738, 24, 766, 22], [738, 60, 766, 58], [738, 62, 766, 60, "status"], [738, 68, 766, 66], [738, 69, 766, 67, "error"], [738, 74, 766, 72], [738, 75, 766, 73], [739, 10, 767, 8], [739, 16, 767, 14], [739, 20, 767, 18, "Error"], [739, 25, 767, 23], [739, 26, 767, 24, "status"], [739, 32, 767, 30], [739, 33, 767, 31, "error"], [739, 38, 767, 36], [739, 42, 767, 40], [739, 61, 767, 59], [739, 62, 767, 60], [740, 8, 768, 6], [740, 9, 768, 7], [740, 15, 768, 13], [741, 10, 769, 8], [742, 10, 770, 8], [742, 16, 770, 14, "progressValue"], [742, 29, 770, 27], [742, 32, 770, 30], [742, 34, 770, 32], [742, 37, 770, 36, "attempts"], [742, 45, 770, 44], [742, 48, 770, 47, "MAX_ATTEMPTS"], [742, 60, 770, 59], [742, 63, 770, 63], [742, 65, 770, 65], [743, 10, 771, 8, "console"], [743, 17, 771, 15], [743, 18, 771, 16, "log"], [743, 21, 771, 19], [743, 22, 771, 20], [743, 71, 771, 69, "progressValue"], [743, 84, 771, 82], [743, 87, 771, 85], [743, 88, 771, 86], [744, 10, 772, 8, "setProcessingProgress"], [744, 31, 772, 29], [744, 32, 772, 30, "progressValue"], [744, 45, 772, 43], [744, 46, 772, 44], [745, 10, 774, 8, "setTimeout"], [745, 20, 774, 18], [745, 21, 774, 19], [745, 27, 774, 25], [746, 12, 775, 10, "pollForCompletion"], [746, 29, 775, 27], [746, 30, 775, 28, "jobId"], [746, 35, 775, 33], [746, 37, 775, 35, "timestamp"], [746, 46, 775, 44], [746, 48, 775, 46, "attempts"], [746, 56, 775, 54], [746, 59, 775, 57], [746, 60, 775, 58], [746, 61, 775, 59], [747, 10, 776, 8], [747, 11, 776, 9], [747, 13, 776, 11, "POLL_INTERVAL"], [747, 26, 776, 24], [747, 27, 776, 25], [748, 8, 777, 6], [749, 6, 778, 4], [749, 7, 778, 5], [749, 8, 778, 6], [749, 15, 778, 13, "error"], [749, 20, 778, 18], [749, 22, 778, 20], [750, 8, 779, 6, "console"], [750, 15, 779, 13], [750, 16, 779, 14, "error"], [750, 21, 779, 19], [750, 22, 779, 20], [750, 54, 779, 52], [750, 56, 779, 54, "error"], [750, 61, 779, 59], [750, 62, 779, 60], [751, 8, 780, 6, "setErrorMessage"], [751, 23, 780, 21], [751, 24, 780, 22], [751, 62, 780, 60, "error"], [751, 67, 780, 65], [751, 68, 780, 66, "message"], [751, 75, 780, 73], [751, 77, 780, 75], [751, 78, 780, 76], [752, 8, 781, 6, "setProcessingState"], [752, 26, 781, 24], [752, 27, 781, 25], [752, 34, 781, 32], [752, 35, 781, 33], [753, 6, 782, 4], [754, 4, 783, 2], [754, 5, 783, 3], [755, 4, 784, 2], [756, 4, 785, 2], [756, 10, 785, 8, "getAuthToken"], [756, 22, 785, 20], [756, 25, 785, 23], [756, 31, 785, 23, "getAuthToken"], [756, 32, 785, 23], [756, 37, 785, 52], [757, 6, 786, 4], [758, 6, 787, 4], [759, 6, 788, 4], [759, 13, 788, 11], [759, 30, 788, 28], [760, 4, 789, 2], [760, 5, 789, 3], [762, 4, 791, 2], [763, 4, 792, 2], [763, 10, 792, 8, "retryCapture"], [763, 22, 792, 20], [763, 25, 792, 23], [763, 29, 792, 23, "useCallback"], [763, 47, 792, 34], [763, 49, 792, 35], [763, 55, 792, 41], [764, 6, 793, 4, "console"], [764, 13, 793, 11], [764, 14, 793, 12, "log"], [764, 17, 793, 15], [764, 18, 793, 16], [764, 55, 793, 53], [764, 56, 793, 54], [765, 6, 794, 4, "setProcessingState"], [765, 24, 794, 22], [765, 25, 794, 23], [765, 31, 794, 29], [765, 32, 794, 30], [766, 6, 795, 4, "setErrorMessage"], [766, 21, 795, 19], [766, 22, 795, 20], [766, 24, 795, 22], [766, 25, 795, 23], [767, 6, 796, 4, "setCapturedPhoto"], [767, 22, 796, 20], [767, 23, 796, 21], [767, 25, 796, 23], [767, 26, 796, 24], [768, 6, 797, 4, "setProcessingProgress"], [768, 27, 797, 25], [768, 28, 797, 26], [768, 29, 797, 27], [768, 30, 797, 28], [769, 4, 798, 2], [769, 5, 798, 3], [769, 7, 798, 5], [769, 9, 798, 7], [769, 10, 798, 8], [770, 4, 799, 2], [771, 4, 800, 2], [771, 8, 800, 2, "useEffect"], [771, 24, 800, 11], [771, 26, 800, 12], [771, 32, 800, 18], [772, 6, 801, 4, "console"], [772, 13, 801, 11], [772, 14, 801, 12, "log"], [772, 17, 801, 15], [772, 18, 801, 16], [772, 53, 801, 51], [772, 55, 801, 53, "permission"], [772, 65, 801, 63], [772, 66, 801, 64], [773, 6, 802, 4], [773, 10, 802, 8, "permission"], [773, 20, 802, 18], [773, 22, 802, 20], [774, 8, 803, 6, "console"], [774, 15, 803, 13], [774, 16, 803, 14, "log"], [774, 19, 803, 17], [774, 20, 803, 18], [774, 57, 803, 55], [774, 59, 803, 57, "permission"], [774, 69, 803, 67], [774, 70, 803, 68, "granted"], [774, 77, 803, 75], [774, 78, 803, 76], [775, 6, 804, 4], [776, 4, 805, 2], [776, 5, 805, 3], [776, 7, 805, 5], [776, 8, 805, 6, "permission"], [776, 18, 805, 16], [776, 19, 805, 17], [776, 20, 805, 18], [777, 4, 806, 2], [778, 4, 807, 2], [778, 8, 807, 6], [778, 9, 807, 7, "permission"], [778, 19, 807, 17], [778, 21, 807, 19], [779, 6, 808, 4, "console"], [779, 13, 808, 11], [779, 14, 808, 12, "log"], [779, 17, 808, 15], [779, 18, 808, 16], [779, 67, 808, 65], [779, 68, 808, 66], [780, 6, 809, 4], [780, 26, 810, 6], [780, 30, 810, 6, "_jsxDevRuntime"], [780, 44, 810, 6], [780, 45, 810, 6, "jsxDEV"], [780, 51, 810, 6], [780, 53, 810, 7, "_View"], [780, 58, 810, 7], [780, 59, 810, 7, "default"], [780, 66, 810, 11], [781, 8, 810, 12, "style"], [781, 13, 810, 17], [781, 15, 810, 19, "styles"], [781, 21, 810, 25], [781, 22, 810, 26, "container"], [781, 31, 810, 36], [782, 8, 810, 36, "children"], [782, 16, 810, 36], [782, 32, 811, 8], [782, 36, 811, 8, "_jsxDevRuntime"], [782, 50, 811, 8], [782, 51, 811, 8, "jsxDEV"], [782, 57, 811, 8], [782, 59, 811, 9, "_ActivityIndicator"], [782, 77, 811, 9], [782, 78, 811, 9, "default"], [782, 85, 811, 26], [783, 10, 811, 27, "size"], [783, 14, 811, 31], [783, 16, 811, 32], [783, 23, 811, 39], [784, 10, 811, 40, "color"], [784, 15, 811, 45], [784, 17, 811, 46], [785, 8, 811, 55], [786, 10, 811, 55, "fileName"], [786, 18, 811, 55], [786, 20, 811, 55, "_jsxFileName"], [786, 32, 811, 55], [787, 10, 811, 55, "lineNumber"], [787, 20, 811, 55], [788, 10, 811, 55, "columnNumber"], [788, 22, 811, 55], [789, 8, 811, 55], [789, 15, 811, 57], [789, 16, 811, 58], [789, 31, 812, 8], [789, 35, 812, 8, "_jsxDevRuntime"], [789, 49, 812, 8], [789, 50, 812, 8, "jsxDEV"], [789, 56, 812, 8], [789, 58, 812, 9, "_Text"], [789, 63, 812, 9], [789, 64, 812, 9, "default"], [789, 71, 812, 13], [790, 10, 812, 14, "style"], [790, 15, 812, 19], [790, 17, 812, 21, "styles"], [790, 23, 812, 27], [790, 24, 812, 28, "loadingText"], [790, 35, 812, 40], [791, 10, 812, 40, "children"], [791, 18, 812, 40], [791, 20, 812, 41], [792, 8, 812, 58], [793, 10, 812, 58, "fileName"], [793, 18, 812, 58], [793, 20, 812, 58, "_jsxFileName"], [793, 32, 812, 58], [794, 10, 812, 58, "lineNumber"], [794, 20, 812, 58], [795, 10, 812, 58, "columnNumber"], [795, 22, 812, 58], [796, 8, 812, 58], [796, 15, 812, 64], [796, 16, 812, 65], [797, 6, 812, 65], [798, 8, 812, 65, "fileName"], [798, 16, 812, 65], [798, 18, 812, 65, "_jsxFileName"], [798, 30, 812, 65], [799, 8, 812, 65, "lineNumber"], [799, 18, 812, 65], [800, 8, 812, 65, "columnNumber"], [800, 20, 812, 65], [801, 6, 812, 65], [801, 13, 813, 12], [801, 14, 813, 13], [802, 4, 815, 2], [803, 4, 816, 2], [803, 8, 816, 6], [803, 9, 816, 7, "permission"], [803, 19, 816, 17], [803, 20, 816, 18, "granted"], [803, 27, 816, 25], [803, 29, 816, 27], [804, 6, 817, 4, "console"], [804, 13, 817, 11], [804, 14, 817, 12, "log"], [804, 17, 817, 15], [804, 18, 817, 16], [804, 93, 817, 91], [804, 94, 817, 92], [805, 6, 818, 4], [805, 26, 819, 6], [805, 30, 819, 6, "_jsxDevRuntime"], [805, 44, 819, 6], [805, 45, 819, 6, "jsxDEV"], [805, 51, 819, 6], [805, 53, 819, 7, "_View"], [805, 58, 819, 7], [805, 59, 819, 7, "default"], [805, 66, 819, 11], [806, 8, 819, 12, "style"], [806, 13, 819, 17], [806, 15, 819, 19, "styles"], [806, 21, 819, 25], [806, 22, 819, 26, "container"], [806, 31, 819, 36], [807, 8, 819, 36, "children"], [807, 16, 819, 36], [807, 31, 820, 8], [807, 35, 820, 8, "_jsxDevRuntime"], [807, 49, 820, 8], [807, 50, 820, 8, "jsxDEV"], [807, 56, 820, 8], [807, 58, 820, 9, "_View"], [807, 63, 820, 9], [807, 64, 820, 9, "default"], [807, 71, 820, 13], [808, 10, 820, 14, "style"], [808, 15, 820, 19], [808, 17, 820, 21, "styles"], [808, 23, 820, 27], [808, 24, 820, 28, "permissionContent"], [808, 41, 820, 46], [809, 10, 820, 46, "children"], [809, 18, 820, 46], [809, 34, 821, 10], [809, 38, 821, 10, "_jsxDevRuntime"], [809, 52, 821, 10], [809, 53, 821, 10, "jsxDEV"], [809, 59, 821, 10], [809, 61, 821, 11, "_lucideReactNative"], [809, 79, 821, 11], [809, 80, 821, 11, "Camera"], [809, 86, 821, 21], [810, 12, 821, 22, "size"], [810, 16, 821, 26], [810, 18, 821, 28], [810, 20, 821, 31], [811, 12, 821, 32, "color"], [811, 17, 821, 37], [811, 19, 821, 38], [812, 10, 821, 47], [813, 12, 821, 47, "fileName"], [813, 20, 821, 47], [813, 22, 821, 47, "_jsxFileName"], [813, 34, 821, 47], [814, 12, 821, 47, "lineNumber"], [814, 22, 821, 47], [815, 12, 821, 47, "columnNumber"], [815, 24, 821, 47], [816, 10, 821, 47], [816, 17, 821, 49], [816, 18, 821, 50], [816, 33, 822, 10], [816, 37, 822, 10, "_jsxDevRuntime"], [816, 51, 822, 10], [816, 52, 822, 10, "jsxDEV"], [816, 58, 822, 10], [816, 60, 822, 11, "_Text"], [816, 65, 822, 11], [816, 66, 822, 11, "default"], [816, 73, 822, 15], [817, 12, 822, 16, "style"], [817, 17, 822, 21], [817, 19, 822, 23, "styles"], [817, 25, 822, 29], [817, 26, 822, 30, "permissionTitle"], [817, 41, 822, 46], [818, 12, 822, 46, "children"], [818, 20, 822, 46], [818, 22, 822, 47], [819, 10, 822, 73], [820, 12, 822, 73, "fileName"], [820, 20, 822, 73], [820, 22, 822, 73, "_jsxFileName"], [820, 34, 822, 73], [821, 12, 822, 73, "lineNumber"], [821, 22, 822, 73], [822, 12, 822, 73, "columnNumber"], [822, 24, 822, 73], [823, 10, 822, 73], [823, 17, 822, 79], [823, 18, 822, 80], [823, 33, 823, 10], [823, 37, 823, 10, "_jsxDevRuntime"], [823, 51, 823, 10], [823, 52, 823, 10, "jsxDEV"], [823, 58, 823, 10], [823, 60, 823, 11, "_Text"], [823, 65, 823, 11], [823, 66, 823, 11, "default"], [823, 73, 823, 15], [824, 12, 823, 16, "style"], [824, 17, 823, 21], [824, 19, 823, 23, "styles"], [824, 25, 823, 29], [824, 26, 823, 30, "permissionDescription"], [824, 47, 823, 52], [825, 12, 823, 52, "children"], [825, 20, 823, 52], [825, 22, 823, 53], [826, 10, 826, 10], [827, 12, 826, 10, "fileName"], [827, 20, 826, 10], [827, 22, 826, 10, "_jsxFileName"], [827, 34, 826, 10], [828, 12, 826, 10, "lineNumber"], [828, 22, 826, 10], [829, 12, 826, 10, "columnNumber"], [829, 24, 826, 10], [830, 10, 826, 10], [830, 17, 826, 16], [830, 18, 826, 17], [830, 33, 827, 10], [830, 37, 827, 10, "_jsxDevRuntime"], [830, 51, 827, 10], [830, 52, 827, 10, "jsxDEV"], [830, 58, 827, 10], [830, 60, 827, 11, "_TouchableOpacity"], [830, 77, 827, 11], [830, 78, 827, 11, "default"], [830, 85, 827, 27], [831, 12, 827, 28, "onPress"], [831, 19, 827, 35], [831, 21, 827, 37, "requestPermission"], [831, 38, 827, 55], [832, 12, 827, 56, "style"], [832, 17, 827, 61], [832, 19, 827, 63, "styles"], [832, 25, 827, 69], [832, 26, 827, 70, "primaryButton"], [832, 39, 827, 84], [833, 12, 827, 84, "children"], [833, 20, 827, 84], [833, 35, 828, 12], [833, 39, 828, 12, "_jsxDevRuntime"], [833, 53, 828, 12], [833, 54, 828, 12, "jsxDEV"], [833, 60, 828, 12], [833, 62, 828, 13, "_Text"], [833, 67, 828, 13], [833, 68, 828, 13, "default"], [833, 75, 828, 17], [834, 14, 828, 18, "style"], [834, 19, 828, 23], [834, 21, 828, 25, "styles"], [834, 27, 828, 31], [834, 28, 828, 32, "primaryButtonText"], [834, 45, 828, 50], [835, 14, 828, 50, "children"], [835, 22, 828, 50], [835, 24, 828, 51], [836, 12, 828, 67], [837, 14, 828, 67, "fileName"], [837, 22, 828, 67], [837, 24, 828, 67, "_jsxFileName"], [837, 36, 828, 67], [838, 14, 828, 67, "lineNumber"], [838, 24, 828, 67], [839, 14, 828, 67, "columnNumber"], [839, 26, 828, 67], [840, 12, 828, 67], [840, 19, 828, 73], [841, 10, 828, 74], [842, 12, 828, 74, "fileName"], [842, 20, 828, 74], [842, 22, 828, 74, "_jsxFileName"], [842, 34, 828, 74], [843, 12, 828, 74, "lineNumber"], [843, 22, 828, 74], [844, 12, 828, 74, "columnNumber"], [844, 24, 828, 74], [845, 10, 828, 74], [845, 17, 829, 28], [845, 18, 829, 29], [845, 33, 830, 10], [845, 37, 830, 10, "_jsxDevRuntime"], [845, 51, 830, 10], [845, 52, 830, 10, "jsxDEV"], [845, 58, 830, 10], [845, 60, 830, 11, "_TouchableOpacity"], [845, 77, 830, 11], [845, 78, 830, 11, "default"], [845, 85, 830, 27], [846, 12, 830, 28, "onPress"], [846, 19, 830, 35], [846, 21, 830, 37, "onCancel"], [846, 29, 830, 46], [847, 12, 830, 47, "style"], [847, 17, 830, 52], [847, 19, 830, 54, "styles"], [847, 25, 830, 60], [847, 26, 830, 61, "secondaryButton"], [847, 41, 830, 77], [848, 12, 830, 77, "children"], [848, 20, 830, 77], [848, 35, 831, 12], [848, 39, 831, 12, "_jsxDevRuntime"], [848, 53, 831, 12], [848, 54, 831, 12, "jsxDEV"], [848, 60, 831, 12], [848, 62, 831, 13, "_Text"], [848, 67, 831, 13], [848, 68, 831, 13, "default"], [848, 75, 831, 17], [849, 14, 831, 18, "style"], [849, 19, 831, 23], [849, 21, 831, 25, "styles"], [849, 27, 831, 31], [849, 28, 831, 32, "secondaryButtonText"], [849, 47, 831, 52], [850, 14, 831, 52, "children"], [850, 22, 831, 52], [850, 24, 831, 53], [851, 12, 831, 59], [852, 14, 831, 59, "fileName"], [852, 22, 831, 59], [852, 24, 831, 59, "_jsxFileName"], [852, 36, 831, 59], [853, 14, 831, 59, "lineNumber"], [853, 24, 831, 59], [854, 14, 831, 59, "columnNumber"], [854, 26, 831, 59], [855, 12, 831, 59], [855, 19, 831, 65], [856, 10, 831, 66], [857, 12, 831, 66, "fileName"], [857, 20, 831, 66], [857, 22, 831, 66, "_jsxFileName"], [857, 34, 831, 66], [858, 12, 831, 66, "lineNumber"], [858, 22, 831, 66], [859, 12, 831, 66, "columnNumber"], [859, 24, 831, 66], [860, 10, 831, 66], [860, 17, 832, 28], [860, 18, 832, 29], [861, 8, 832, 29], [862, 10, 832, 29, "fileName"], [862, 18, 832, 29], [862, 20, 832, 29, "_jsxFileName"], [862, 32, 832, 29], [863, 10, 832, 29, "lineNumber"], [863, 20, 832, 29], [864, 10, 832, 29, "columnNumber"], [864, 22, 832, 29], [865, 8, 832, 29], [865, 15, 833, 14], [866, 6, 833, 15], [867, 8, 833, 15, "fileName"], [867, 16, 833, 15], [867, 18, 833, 15, "_jsxFileName"], [867, 30, 833, 15], [868, 8, 833, 15, "lineNumber"], [868, 18, 833, 15], [869, 8, 833, 15, "columnNumber"], [869, 20, 833, 15], [870, 6, 833, 15], [870, 13, 834, 12], [870, 14, 834, 13], [871, 4, 836, 2], [872, 4, 837, 2], [873, 4, 838, 2, "console"], [873, 11, 838, 9], [873, 12, 838, 10, "log"], [873, 15, 838, 13], [873, 16, 838, 14], [873, 55, 838, 53], [873, 56, 838, 54], [874, 4, 840, 2], [874, 24, 841, 4], [874, 28, 841, 4, "_jsxDevRuntime"], [874, 42, 841, 4], [874, 43, 841, 4, "jsxDEV"], [874, 49, 841, 4], [874, 51, 841, 5, "_View"], [874, 56, 841, 5], [874, 57, 841, 5, "default"], [874, 64, 841, 9], [875, 6, 841, 10, "style"], [875, 11, 841, 15], [875, 13, 841, 17, "styles"], [875, 19, 841, 23], [875, 20, 841, 24, "container"], [875, 29, 841, 34], [876, 6, 841, 34, "children"], [876, 14, 841, 34], [876, 30, 843, 6], [876, 34, 843, 6, "_jsxDevRuntime"], [876, 48, 843, 6], [876, 49, 843, 6, "jsxDEV"], [876, 55, 843, 6], [876, 57, 843, 7, "_View"], [876, 62, 843, 7], [876, 63, 843, 7, "default"], [876, 70, 843, 11], [877, 8, 843, 12, "style"], [877, 13, 843, 17], [877, 15, 843, 19, "styles"], [877, 21, 843, 25], [877, 22, 843, 26, "cameraContainer"], [877, 37, 843, 42], [878, 8, 843, 43, "id"], [878, 10, 843, 45], [878, 12, 843, 46], [878, 29, 843, 63], [879, 8, 843, 63, "children"], [879, 16, 843, 63], [879, 32, 844, 8], [879, 36, 844, 8, "_jsxDevRuntime"], [879, 50, 844, 8], [879, 51, 844, 8, "jsxDEV"], [879, 57, 844, 8], [879, 59, 844, 9, "_expoCamera"], [879, 70, 844, 9], [879, 71, 844, 9, "CameraView"], [879, 81, 844, 19], [880, 10, 845, 10, "ref"], [880, 13, 845, 13], [880, 15, 845, 15, "cameraRef"], [880, 24, 845, 25], [881, 10, 846, 10, "style"], [881, 15, 846, 15], [881, 17, 846, 17], [881, 18, 846, 18, "styles"], [881, 24, 846, 24], [881, 25, 846, 25, "camera"], [881, 31, 846, 31], [881, 33, 846, 33], [882, 12, 846, 35, "backgroundColor"], [882, 27, 846, 50], [882, 29, 846, 52], [883, 10, 846, 62], [883, 11, 846, 63], [883, 12, 846, 65], [884, 10, 847, 10, "facing"], [884, 16, 847, 16], [884, 18, 847, 17], [884, 24, 847, 23], [885, 10, 848, 10, "onLayout"], [885, 18, 848, 18], [885, 20, 848, 21, "e"], [885, 21, 848, 22], [885, 25, 848, 27], [886, 12, 849, 12, "console"], [886, 19, 849, 19], [886, 20, 849, 20, "log"], [886, 23, 849, 23], [886, 24, 849, 24], [886, 56, 849, 56], [886, 58, 849, 58, "e"], [886, 59, 849, 59], [886, 60, 849, 60, "nativeEvent"], [886, 71, 849, 71], [886, 72, 849, 72, "layout"], [886, 78, 849, 78], [886, 79, 849, 79], [887, 12, 850, 12, "setViewSize"], [887, 23, 850, 23], [887, 24, 850, 24], [888, 14, 850, 26, "width"], [888, 19, 850, 31], [888, 21, 850, 33, "e"], [888, 22, 850, 34], [888, 23, 850, 35, "nativeEvent"], [888, 34, 850, 46], [888, 35, 850, 47, "layout"], [888, 41, 850, 53], [888, 42, 850, 54, "width"], [888, 47, 850, 59], [889, 14, 850, 61, "height"], [889, 20, 850, 67], [889, 22, 850, 69, "e"], [889, 23, 850, 70], [889, 24, 850, 71, "nativeEvent"], [889, 35, 850, 82], [889, 36, 850, 83, "layout"], [889, 42, 850, 89], [889, 43, 850, 90, "height"], [890, 12, 850, 97], [890, 13, 850, 98], [890, 14, 850, 99], [891, 10, 851, 10], [891, 11, 851, 12], [892, 10, 852, 10, "onCameraReady"], [892, 23, 852, 23], [892, 25, 852, 25, "onCameraReady"], [892, 26, 852, 25], [892, 31, 852, 31], [893, 12, 853, 12, "console"], [893, 19, 853, 19], [893, 20, 853, 20, "log"], [893, 23, 853, 23], [893, 24, 853, 24], [893, 55, 853, 55], [893, 56, 853, 56], [894, 12, 854, 12, "setIsCameraReady"], [894, 28, 854, 28], [894, 29, 854, 29], [894, 33, 854, 33], [894, 34, 854, 34], [894, 35, 854, 35], [894, 36, 854, 36], [895, 10, 855, 10], [895, 11, 855, 12], [896, 10, 856, 10, "onMountError"], [896, 22, 856, 22], [896, 24, 856, 25, "error"], [896, 29, 856, 30], [896, 33, 856, 35], [897, 12, 857, 12, "console"], [897, 19, 857, 19], [897, 20, 857, 20, "error"], [897, 25, 857, 25], [897, 26, 857, 26], [897, 63, 857, 63], [897, 65, 857, 65, "error"], [897, 70, 857, 70], [897, 71, 857, 71], [898, 12, 858, 12, "setErrorMessage"], [898, 27, 858, 27], [898, 28, 858, 28], [898, 57, 858, 57], [898, 58, 858, 58], [899, 12, 859, 12, "setProcessingState"], [899, 30, 859, 30], [899, 31, 859, 31], [899, 38, 859, 38], [899, 39, 859, 39], [900, 10, 860, 10], [901, 8, 860, 12], [902, 10, 860, 12, "fileName"], [902, 18, 860, 12], [902, 20, 860, 12, "_jsxFileName"], [902, 32, 860, 12], [903, 10, 860, 12, "lineNumber"], [903, 20, 860, 12], [904, 10, 860, 12, "columnNumber"], [904, 22, 860, 12], [905, 8, 860, 12], [905, 15, 861, 9], [905, 16, 861, 10], [905, 18, 863, 9], [905, 19, 863, 10, "isCameraReady"], [905, 32, 863, 23], [905, 49, 864, 10], [905, 53, 864, 10, "_jsxDevRuntime"], [905, 67, 864, 10], [905, 68, 864, 10, "jsxDEV"], [905, 74, 864, 10], [905, 76, 864, 11, "_View"], [905, 81, 864, 11], [905, 82, 864, 11, "default"], [905, 89, 864, 15], [906, 10, 864, 16, "style"], [906, 15, 864, 21], [906, 17, 864, 23], [906, 18, 864, 24, "StyleSheet"], [906, 37, 864, 34], [906, 38, 864, 35, "absoluteFill"], [906, 50, 864, 47], [906, 52, 864, 49], [907, 12, 864, 51, "backgroundColor"], [907, 27, 864, 66], [907, 29, 864, 68], [907, 49, 864, 88], [908, 12, 864, 90, "justifyContent"], [908, 26, 864, 104], [908, 28, 864, 106], [908, 36, 864, 114], [909, 12, 864, 116, "alignItems"], [909, 22, 864, 126], [909, 24, 864, 128], [909, 32, 864, 136], [910, 12, 864, 138, "zIndex"], [910, 18, 864, 144], [910, 20, 864, 146], [911, 10, 864, 151], [911, 11, 864, 152], [911, 12, 864, 154], [912, 10, 864, 154, "children"], [912, 18, 864, 154], [912, 33, 865, 12], [912, 37, 865, 12, "_jsxDevRuntime"], [912, 51, 865, 12], [912, 52, 865, 12, "jsxDEV"], [912, 58, 865, 12], [912, 60, 865, 13, "_View"], [912, 65, 865, 13], [912, 66, 865, 13, "default"], [912, 73, 865, 17], [913, 12, 865, 18, "style"], [913, 17, 865, 23], [913, 19, 865, 25], [914, 14, 865, 27, "backgroundColor"], [914, 29, 865, 42], [914, 31, 865, 44], [914, 51, 865, 64], [915, 14, 865, 66, "padding"], [915, 21, 865, 73], [915, 23, 865, 75], [915, 25, 865, 77], [916, 14, 865, 79, "borderRadius"], [916, 26, 865, 91], [916, 28, 865, 93], [916, 30, 865, 95], [917, 14, 865, 97, "alignItems"], [917, 24, 865, 107], [917, 26, 865, 109], [918, 12, 865, 118], [918, 13, 865, 120], [919, 12, 865, 120, "children"], [919, 20, 865, 120], [919, 36, 866, 14], [919, 40, 866, 14, "_jsxDevRuntime"], [919, 54, 866, 14], [919, 55, 866, 14, "jsxDEV"], [919, 61, 866, 14], [919, 63, 866, 15, "_ActivityIndicator"], [919, 81, 866, 15], [919, 82, 866, 15, "default"], [919, 89, 866, 32], [920, 14, 866, 33, "size"], [920, 18, 866, 37], [920, 20, 866, 38], [920, 27, 866, 45], [921, 14, 866, 46, "color"], [921, 19, 866, 51], [921, 21, 866, 52], [921, 30, 866, 61], [922, 14, 866, 62, "style"], [922, 19, 866, 67], [922, 21, 866, 69], [923, 16, 866, 71, "marginBottom"], [923, 28, 866, 83], [923, 30, 866, 85], [924, 14, 866, 88], [925, 12, 866, 90], [926, 14, 866, 90, "fileName"], [926, 22, 866, 90], [926, 24, 866, 90, "_jsxFileName"], [926, 36, 866, 90], [927, 14, 866, 90, "lineNumber"], [927, 24, 866, 90], [928, 14, 866, 90, "columnNumber"], [928, 26, 866, 90], [929, 12, 866, 90], [929, 19, 866, 92], [929, 20, 866, 93], [929, 35, 867, 14], [929, 39, 867, 14, "_jsxDevRuntime"], [929, 53, 867, 14], [929, 54, 867, 14, "jsxDEV"], [929, 60, 867, 14], [929, 62, 867, 15, "_Text"], [929, 67, 867, 15], [929, 68, 867, 15, "default"], [929, 75, 867, 19], [930, 14, 867, 20, "style"], [930, 19, 867, 25], [930, 21, 867, 27], [931, 16, 867, 29, "color"], [931, 21, 867, 34], [931, 23, 867, 36], [931, 29, 867, 42], [932, 16, 867, 44, "fontSize"], [932, 24, 867, 52], [932, 26, 867, 54], [932, 28, 867, 56], [933, 16, 867, 58, "fontWeight"], [933, 26, 867, 68], [933, 28, 867, 70], [934, 14, 867, 76], [934, 15, 867, 78], [935, 14, 867, 78, "children"], [935, 22, 867, 78], [935, 24, 867, 79], [936, 12, 867, 101], [937, 14, 867, 101, "fileName"], [937, 22, 867, 101], [937, 24, 867, 101, "_jsxFileName"], [937, 36, 867, 101], [938, 14, 867, 101, "lineNumber"], [938, 24, 867, 101], [939, 14, 867, 101, "columnNumber"], [939, 26, 867, 101], [940, 12, 867, 101], [940, 19, 867, 107], [940, 20, 867, 108], [940, 35, 868, 14], [940, 39, 868, 14, "_jsxDevRuntime"], [940, 53, 868, 14], [940, 54, 868, 14, "jsxDEV"], [940, 60, 868, 14], [940, 62, 868, 15, "_Text"], [940, 67, 868, 15], [940, 68, 868, 15, "default"], [940, 75, 868, 19], [941, 14, 868, 20, "style"], [941, 19, 868, 25], [941, 21, 868, 27], [942, 16, 868, 29, "color"], [942, 21, 868, 34], [942, 23, 868, 36], [942, 32, 868, 45], [943, 16, 868, 47, "fontSize"], [943, 24, 868, 55], [943, 26, 868, 57], [943, 28, 868, 59], [944, 16, 868, 61, "marginTop"], [944, 25, 868, 70], [944, 27, 868, 72], [945, 14, 868, 74], [945, 15, 868, 76], [946, 14, 868, 76, "children"], [946, 22, 868, 76], [946, 24, 868, 77], [947, 12, 868, 88], [948, 14, 868, 88, "fileName"], [948, 22, 868, 88], [948, 24, 868, 88, "_jsxFileName"], [948, 36, 868, 88], [949, 14, 868, 88, "lineNumber"], [949, 24, 868, 88], [950, 14, 868, 88, "columnNumber"], [950, 26, 868, 88], [951, 12, 868, 88], [951, 19, 868, 94], [951, 20, 868, 95], [952, 10, 868, 95], [953, 12, 868, 95, "fileName"], [953, 20, 868, 95], [953, 22, 868, 95, "_jsxFileName"], [953, 34, 868, 95], [954, 12, 868, 95, "lineNumber"], [954, 22, 868, 95], [955, 12, 868, 95, "columnNumber"], [955, 24, 868, 95], [956, 10, 868, 95], [956, 17, 869, 18], [957, 8, 869, 19], [958, 10, 869, 19, "fileName"], [958, 18, 869, 19], [958, 20, 869, 19, "_jsxFileName"], [958, 32, 869, 19], [959, 10, 869, 19, "lineNumber"], [959, 20, 869, 19], [960, 10, 869, 19, "columnNumber"], [960, 22, 869, 19], [961, 8, 869, 19], [961, 15, 870, 16], [961, 16, 871, 9], [961, 18, 874, 9, "isCameraReady"], [961, 31, 874, 22], [961, 35, 874, 26, "previewBlurEnabled"], [961, 53, 874, 44], [961, 57, 874, 48, "viewSize"], [961, 65, 874, 56], [961, 66, 874, 57, "width"], [961, 71, 874, 62], [961, 74, 874, 65], [961, 75, 874, 66], [961, 92, 875, 10], [961, 96, 875, 10, "_jsxDevRuntime"], [961, 110, 875, 10], [961, 111, 875, 10, "jsxDEV"], [961, 117, 875, 10], [961, 119, 875, 10, "_jsxDevRuntime"], [961, 133, 875, 10], [961, 134, 875, 10, "Fragment"], [961, 142, 875, 10], [962, 10, 875, 10, "children"], [962, 18, 875, 10], [962, 34, 877, 12], [962, 38, 877, 12, "_jsxDevRuntime"], [962, 52, 877, 12], [962, 53, 877, 12, "jsxDEV"], [962, 59, 877, 12], [962, 61, 877, 13, "_LiveFaceCanvas"], [962, 76, 877, 13], [962, 77, 877, 13, "default"], [962, 84, 877, 27], [963, 12, 877, 28, "containerId"], [963, 23, 877, 39], [963, 25, 877, 40], [963, 42, 877, 57], [964, 12, 877, 58, "width"], [964, 17, 877, 63], [964, 19, 877, 65, "viewSize"], [964, 27, 877, 73], [964, 28, 877, 74, "width"], [964, 33, 877, 80], [965, 12, 877, 81, "height"], [965, 18, 877, 87], [965, 20, 877, 89, "viewSize"], [965, 28, 877, 97], [965, 29, 877, 98, "height"], [966, 10, 877, 105], [967, 12, 877, 105, "fileName"], [967, 20, 877, 105], [967, 22, 877, 105, "_jsxFileName"], [967, 34, 877, 105], [968, 12, 877, 105, "lineNumber"], [968, 22, 877, 105], [969, 12, 877, 105, "columnNumber"], [969, 24, 877, 105], [970, 10, 877, 105], [970, 17, 877, 107], [970, 18, 877, 108], [970, 33, 878, 12], [970, 37, 878, 12, "_jsxDevRuntime"], [970, 51, 878, 12], [970, 52, 878, 12, "jsxDEV"], [970, 58, 878, 12], [970, 60, 878, 13, "_View"], [970, 65, 878, 13], [970, 66, 878, 13, "default"], [970, 73, 878, 17], [971, 12, 878, 18, "style"], [971, 17, 878, 23], [971, 19, 878, 25], [971, 20, 878, 26, "StyleSheet"], [971, 39, 878, 36], [971, 40, 878, 37, "absoluteFill"], [971, 52, 878, 49], [971, 54, 878, 51], [972, 14, 878, 53, "pointerEvents"], [972, 27, 878, 66], [972, 29, 878, 68], [973, 12, 878, 75], [973, 13, 878, 76], [973, 14, 878, 78], [974, 12, 878, 78, "children"], [974, 20, 878, 78], [974, 36, 880, 12], [974, 40, 880, 12, "_jsxDevRuntime"], [974, 54, 880, 12], [974, 55, 880, 12, "jsxDEV"], [974, 61, 880, 12], [974, 63, 880, 13, "_expoBlur"], [974, 72, 880, 13], [974, 73, 880, 13, "BlurView"], [974, 81, 880, 21], [975, 14, 880, 22, "intensity"], [975, 23, 880, 31], [975, 25, 880, 33], [975, 27, 880, 36], [976, 14, 880, 37, "tint"], [976, 18, 880, 41], [976, 20, 880, 42], [976, 26, 880, 48], [977, 14, 880, 49, "style"], [977, 19, 880, 54], [977, 21, 880, 56], [977, 22, 880, 57, "styles"], [977, 28, 880, 63], [977, 29, 880, 64, "blurZone"], [977, 37, 880, 72], [977, 39, 880, 74], [978, 16, 881, 14, "left"], [978, 20, 881, 18], [978, 22, 881, 20], [978, 23, 881, 21], [979, 16, 882, 14, "top"], [979, 19, 882, 17], [979, 21, 882, 19, "viewSize"], [979, 29, 882, 27], [979, 30, 882, 28, "height"], [979, 36, 882, 34], [979, 39, 882, 37], [979, 42, 882, 40], [980, 16, 883, 14, "width"], [980, 21, 883, 19], [980, 23, 883, 21, "viewSize"], [980, 31, 883, 29], [980, 32, 883, 30, "width"], [980, 37, 883, 35], [981, 16, 884, 14, "height"], [981, 22, 884, 20], [981, 24, 884, 22, "viewSize"], [981, 32, 884, 30], [981, 33, 884, 31, "height"], [981, 39, 884, 37], [981, 42, 884, 40], [981, 46, 884, 44], [982, 16, 885, 14, "borderRadius"], [982, 28, 885, 26], [982, 30, 885, 28], [983, 14, 886, 12], [983, 15, 886, 13], [984, 12, 886, 15], [985, 14, 886, 15, "fileName"], [985, 22, 886, 15], [985, 24, 886, 15, "_jsxFileName"], [985, 36, 886, 15], [986, 14, 886, 15, "lineNumber"], [986, 24, 886, 15], [987, 14, 886, 15, "columnNumber"], [987, 26, 886, 15], [988, 12, 886, 15], [988, 19, 886, 17], [988, 20, 886, 18], [988, 35, 888, 12], [988, 39, 888, 12, "_jsxDevRuntime"], [988, 53, 888, 12], [988, 54, 888, 12, "jsxDEV"], [988, 60, 888, 12], [988, 62, 888, 13, "_expoBlur"], [988, 71, 888, 13], [988, 72, 888, 13, "BlurView"], [988, 80, 888, 21], [989, 14, 888, 22, "intensity"], [989, 23, 888, 31], [989, 25, 888, 33], [989, 27, 888, 36], [990, 14, 888, 37, "tint"], [990, 18, 888, 41], [990, 20, 888, 42], [990, 26, 888, 48], [991, 14, 888, 49, "style"], [991, 19, 888, 54], [991, 21, 888, 56], [991, 22, 888, 57, "styles"], [991, 28, 888, 63], [991, 29, 888, 64, "blurZone"], [991, 37, 888, 72], [991, 39, 888, 74], [992, 16, 889, 14, "left"], [992, 20, 889, 18], [992, 22, 889, 20], [992, 23, 889, 21], [993, 16, 890, 14, "top"], [993, 19, 890, 17], [993, 21, 890, 19], [993, 22, 890, 20], [994, 16, 891, 14, "width"], [994, 21, 891, 19], [994, 23, 891, 21, "viewSize"], [994, 31, 891, 29], [994, 32, 891, 30, "width"], [994, 37, 891, 35], [995, 16, 892, 14, "height"], [995, 22, 892, 20], [995, 24, 892, 22, "viewSize"], [995, 32, 892, 30], [995, 33, 892, 31, "height"], [995, 39, 892, 37], [995, 42, 892, 40], [995, 45, 892, 43], [996, 16, 893, 14, "borderRadius"], [996, 28, 893, 26], [996, 30, 893, 28], [997, 14, 894, 12], [997, 15, 894, 13], [998, 12, 894, 15], [999, 14, 894, 15, "fileName"], [999, 22, 894, 15], [999, 24, 894, 15, "_jsxFileName"], [999, 36, 894, 15], [1000, 14, 894, 15, "lineNumber"], [1000, 24, 894, 15], [1001, 14, 894, 15, "columnNumber"], [1001, 26, 894, 15], [1002, 12, 894, 15], [1002, 19, 894, 17], [1002, 20, 894, 18], [1002, 35, 896, 12], [1002, 39, 896, 12, "_jsxDevRuntime"], [1002, 53, 896, 12], [1002, 54, 896, 12, "jsxDEV"], [1002, 60, 896, 12], [1002, 62, 896, 13, "_expoBlur"], [1002, 71, 896, 13], [1002, 72, 896, 13, "BlurView"], [1002, 80, 896, 21], [1003, 14, 896, 22, "intensity"], [1003, 23, 896, 31], [1003, 25, 896, 33], [1003, 27, 896, 36], [1004, 14, 896, 37, "tint"], [1004, 18, 896, 41], [1004, 20, 896, 42], [1004, 26, 896, 48], [1005, 14, 896, 49, "style"], [1005, 19, 896, 54], [1005, 21, 896, 56], [1005, 22, 896, 57, "styles"], [1005, 28, 896, 63], [1005, 29, 896, 64, "blurZone"], [1005, 37, 896, 72], [1005, 39, 896, 74], [1006, 16, 897, 14, "left"], [1006, 20, 897, 18], [1006, 22, 897, 20, "viewSize"], [1006, 30, 897, 28], [1006, 31, 897, 29, "width"], [1006, 36, 897, 34], [1006, 39, 897, 37], [1006, 42, 897, 40], [1006, 45, 897, 44, "viewSize"], [1006, 53, 897, 52], [1006, 54, 897, 53, "width"], [1006, 59, 897, 58], [1006, 62, 897, 61], [1006, 66, 897, 66], [1007, 16, 898, 14, "top"], [1007, 19, 898, 17], [1007, 21, 898, 19, "viewSize"], [1007, 29, 898, 27], [1007, 30, 898, 28, "height"], [1007, 36, 898, 34], [1007, 39, 898, 37], [1007, 43, 898, 41], [1007, 46, 898, 45, "viewSize"], [1007, 54, 898, 53], [1007, 55, 898, 54, "width"], [1007, 60, 898, 59], [1007, 63, 898, 62], [1007, 67, 898, 67], [1008, 16, 899, 14, "width"], [1008, 21, 899, 19], [1008, 23, 899, 21, "viewSize"], [1008, 31, 899, 29], [1008, 32, 899, 30, "width"], [1008, 37, 899, 35], [1008, 40, 899, 38], [1008, 43, 899, 41], [1009, 16, 900, 14, "height"], [1009, 22, 900, 20], [1009, 24, 900, 22, "viewSize"], [1009, 32, 900, 30], [1009, 33, 900, 31, "width"], [1009, 38, 900, 36], [1009, 41, 900, 39], [1009, 44, 900, 42], [1010, 16, 901, 14, "borderRadius"], [1010, 28, 901, 26], [1010, 30, 901, 29, "viewSize"], [1010, 38, 901, 37], [1010, 39, 901, 38, "width"], [1010, 44, 901, 43], [1010, 47, 901, 46], [1010, 50, 901, 49], [1010, 53, 901, 53], [1011, 14, 902, 12], [1011, 15, 902, 13], [1012, 12, 902, 15], [1013, 14, 902, 15, "fileName"], [1013, 22, 902, 15], [1013, 24, 902, 15, "_jsxFileName"], [1013, 36, 902, 15], [1014, 14, 902, 15, "lineNumber"], [1014, 24, 902, 15], [1015, 14, 902, 15, "columnNumber"], [1015, 26, 902, 15], [1016, 12, 902, 15], [1016, 19, 902, 17], [1016, 20, 902, 18], [1016, 35, 903, 12], [1016, 39, 903, 12, "_jsxDevRuntime"], [1016, 53, 903, 12], [1016, 54, 903, 12, "jsxDEV"], [1016, 60, 903, 12], [1016, 62, 903, 13, "_expoBlur"], [1016, 71, 903, 13], [1016, 72, 903, 13, "BlurView"], [1016, 80, 903, 21], [1017, 14, 903, 22, "intensity"], [1017, 23, 903, 31], [1017, 25, 903, 33], [1017, 27, 903, 36], [1018, 14, 903, 37, "tint"], [1018, 18, 903, 41], [1018, 20, 903, 42], [1018, 26, 903, 48], [1019, 14, 903, 49, "style"], [1019, 19, 903, 54], [1019, 21, 903, 56], [1019, 22, 903, 57, "styles"], [1019, 28, 903, 63], [1019, 29, 903, 64, "blurZone"], [1019, 37, 903, 72], [1019, 39, 903, 74], [1020, 16, 904, 14, "left"], [1020, 20, 904, 18], [1020, 22, 904, 20, "viewSize"], [1020, 30, 904, 28], [1020, 31, 904, 29, "width"], [1020, 36, 904, 34], [1020, 39, 904, 37], [1020, 42, 904, 40], [1020, 45, 904, 44, "viewSize"], [1020, 53, 904, 52], [1020, 54, 904, 53, "width"], [1020, 59, 904, 58], [1020, 62, 904, 61], [1020, 66, 904, 66], [1021, 16, 905, 14, "top"], [1021, 19, 905, 17], [1021, 21, 905, 19, "viewSize"], [1021, 29, 905, 27], [1021, 30, 905, 28, "height"], [1021, 36, 905, 34], [1021, 39, 905, 37], [1021, 42, 905, 40], [1021, 45, 905, 44, "viewSize"], [1021, 53, 905, 52], [1021, 54, 905, 53, "width"], [1021, 59, 905, 58], [1021, 62, 905, 61], [1021, 66, 905, 66], [1022, 16, 906, 14, "width"], [1022, 21, 906, 19], [1022, 23, 906, 21, "viewSize"], [1022, 31, 906, 29], [1022, 32, 906, 30, "width"], [1022, 37, 906, 35], [1022, 40, 906, 38], [1022, 43, 906, 41], [1023, 16, 907, 14, "height"], [1023, 22, 907, 20], [1023, 24, 907, 22, "viewSize"], [1023, 32, 907, 30], [1023, 33, 907, 31, "width"], [1023, 38, 907, 36], [1023, 41, 907, 39], [1023, 44, 907, 42], [1024, 16, 908, 14, "borderRadius"], [1024, 28, 908, 26], [1024, 30, 908, 29, "viewSize"], [1024, 38, 908, 37], [1024, 39, 908, 38, "width"], [1024, 44, 908, 43], [1024, 47, 908, 46], [1024, 50, 908, 49], [1024, 53, 908, 53], [1025, 14, 909, 12], [1025, 15, 909, 13], [1026, 12, 909, 15], [1027, 14, 909, 15, "fileName"], [1027, 22, 909, 15], [1027, 24, 909, 15, "_jsxFileName"], [1027, 36, 909, 15], [1028, 14, 909, 15, "lineNumber"], [1028, 24, 909, 15], [1029, 14, 909, 15, "columnNumber"], [1029, 26, 909, 15], [1030, 12, 909, 15], [1030, 19, 909, 17], [1030, 20, 909, 18], [1030, 35, 910, 12], [1030, 39, 910, 12, "_jsxDevRuntime"], [1030, 53, 910, 12], [1030, 54, 910, 12, "jsxDEV"], [1030, 60, 910, 12], [1030, 62, 910, 13, "_expoBlur"], [1030, 71, 910, 13], [1030, 72, 910, 13, "BlurView"], [1030, 80, 910, 21], [1031, 14, 910, 22, "intensity"], [1031, 23, 910, 31], [1031, 25, 910, 33], [1031, 27, 910, 36], [1032, 14, 910, 37, "tint"], [1032, 18, 910, 41], [1032, 20, 910, 42], [1032, 26, 910, 48], [1033, 14, 910, 49, "style"], [1033, 19, 910, 54], [1033, 21, 910, 56], [1033, 22, 910, 57, "styles"], [1033, 28, 910, 63], [1033, 29, 910, 64, "blurZone"], [1033, 37, 910, 72], [1033, 39, 910, 74], [1034, 16, 911, 14, "left"], [1034, 20, 911, 18], [1034, 22, 911, 20, "viewSize"], [1034, 30, 911, 28], [1034, 31, 911, 29, "width"], [1034, 36, 911, 34], [1034, 39, 911, 37], [1034, 42, 911, 40], [1034, 45, 911, 44, "viewSize"], [1034, 53, 911, 52], [1034, 54, 911, 53, "width"], [1034, 59, 911, 58], [1034, 62, 911, 61], [1034, 66, 911, 66], [1035, 16, 912, 14, "top"], [1035, 19, 912, 17], [1035, 21, 912, 19, "viewSize"], [1035, 29, 912, 27], [1035, 30, 912, 28, "height"], [1035, 36, 912, 34], [1035, 39, 912, 37], [1035, 42, 912, 40], [1035, 45, 912, 44, "viewSize"], [1035, 53, 912, 52], [1035, 54, 912, 53, "width"], [1035, 59, 912, 58], [1035, 62, 912, 61], [1035, 66, 912, 66], [1036, 16, 913, 14, "width"], [1036, 21, 913, 19], [1036, 23, 913, 21, "viewSize"], [1036, 31, 913, 29], [1036, 32, 913, 30, "width"], [1036, 37, 913, 35], [1036, 40, 913, 38], [1036, 43, 913, 41], [1037, 16, 914, 14, "height"], [1037, 22, 914, 20], [1037, 24, 914, 22, "viewSize"], [1037, 32, 914, 30], [1037, 33, 914, 31, "width"], [1037, 38, 914, 36], [1037, 41, 914, 39], [1037, 44, 914, 42], [1038, 16, 915, 14, "borderRadius"], [1038, 28, 915, 26], [1038, 30, 915, 29, "viewSize"], [1038, 38, 915, 37], [1038, 39, 915, 38, "width"], [1038, 44, 915, 43], [1038, 47, 915, 46], [1038, 50, 915, 49], [1038, 53, 915, 53], [1039, 14, 916, 12], [1039, 15, 916, 13], [1040, 12, 916, 15], [1041, 14, 916, 15, "fileName"], [1041, 22, 916, 15], [1041, 24, 916, 15, "_jsxFileName"], [1041, 36, 916, 15], [1042, 14, 916, 15, "lineNumber"], [1042, 24, 916, 15], [1043, 14, 916, 15, "columnNumber"], [1043, 26, 916, 15], [1044, 12, 916, 15], [1044, 19, 916, 17], [1044, 20, 916, 18], [1044, 22, 918, 13, "__DEV__"], [1044, 29, 918, 20], [1044, 46, 919, 14], [1044, 50, 919, 14, "_jsxDevRuntime"], [1044, 64, 919, 14], [1044, 65, 919, 14, "jsxDEV"], [1044, 71, 919, 14], [1044, 73, 919, 15, "_View"], [1044, 78, 919, 15], [1044, 79, 919, 15, "default"], [1044, 86, 919, 19], [1045, 14, 919, 20, "style"], [1045, 19, 919, 25], [1045, 21, 919, 27, "styles"], [1045, 27, 919, 33], [1045, 28, 919, 34, "previewChip"], [1045, 39, 919, 46], [1046, 14, 919, 46, "children"], [1046, 22, 919, 46], [1046, 37, 920, 16], [1046, 41, 920, 16, "_jsxDevRuntime"], [1046, 55, 920, 16], [1046, 56, 920, 16, "jsxDEV"], [1046, 62, 920, 16], [1046, 64, 920, 17, "_Text"], [1046, 69, 920, 17], [1046, 70, 920, 17, "default"], [1046, 77, 920, 21], [1047, 16, 920, 22, "style"], [1047, 21, 920, 27], [1047, 23, 920, 29, "styles"], [1047, 29, 920, 35], [1047, 30, 920, 36, "previewChipText"], [1047, 45, 920, 52], [1048, 16, 920, 52, "children"], [1048, 24, 920, 52], [1048, 26, 920, 53], [1049, 14, 920, 73], [1050, 16, 920, 73, "fileName"], [1050, 24, 920, 73], [1050, 26, 920, 73, "_jsxFileName"], [1050, 38, 920, 73], [1051, 16, 920, 73, "lineNumber"], [1051, 26, 920, 73], [1052, 16, 920, 73, "columnNumber"], [1052, 28, 920, 73], [1053, 14, 920, 73], [1053, 21, 920, 79], [1054, 12, 920, 80], [1055, 14, 920, 80, "fileName"], [1055, 22, 920, 80], [1055, 24, 920, 80, "_jsxFileName"], [1055, 36, 920, 80], [1056, 14, 920, 80, "lineNumber"], [1056, 24, 920, 80], [1057, 14, 920, 80, "columnNumber"], [1057, 26, 920, 80], [1058, 12, 920, 80], [1058, 19, 921, 20], [1058, 20, 922, 13], [1059, 10, 922, 13], [1060, 12, 922, 13, "fileName"], [1060, 20, 922, 13], [1060, 22, 922, 13, "_jsxFileName"], [1060, 34, 922, 13], [1061, 12, 922, 13, "lineNumber"], [1061, 22, 922, 13], [1062, 12, 922, 13, "columnNumber"], [1062, 24, 922, 13], [1063, 10, 922, 13], [1063, 17, 923, 18], [1063, 18, 923, 19], [1064, 8, 923, 19], [1064, 23, 924, 12], [1064, 24, 925, 9], [1064, 26, 927, 9, "isCameraReady"], [1064, 39, 927, 22], [1064, 56, 928, 10], [1064, 60, 928, 10, "_jsxDevRuntime"], [1064, 74, 928, 10], [1064, 75, 928, 10, "jsxDEV"], [1064, 81, 928, 10], [1064, 83, 928, 10, "_jsxDevRuntime"], [1064, 97, 928, 10], [1064, 98, 928, 10, "Fragment"], [1064, 106, 928, 10], [1065, 10, 928, 10, "children"], [1065, 18, 928, 10], [1065, 34, 930, 12], [1065, 38, 930, 12, "_jsxDevRuntime"], [1065, 52, 930, 12], [1065, 53, 930, 12, "jsxDEV"], [1065, 59, 930, 12], [1065, 61, 930, 13, "_View"], [1065, 66, 930, 13], [1065, 67, 930, 13, "default"], [1065, 74, 930, 17], [1066, 12, 930, 18, "style"], [1066, 17, 930, 23], [1066, 19, 930, 25, "styles"], [1066, 25, 930, 31], [1066, 26, 930, 32, "headerOverlay"], [1066, 39, 930, 46], [1067, 12, 930, 46, "children"], [1067, 20, 930, 46], [1067, 35, 931, 14], [1067, 39, 931, 14, "_jsxDevRuntime"], [1067, 53, 931, 14], [1067, 54, 931, 14, "jsxDEV"], [1067, 60, 931, 14], [1067, 62, 931, 15, "_View"], [1067, 67, 931, 15], [1067, 68, 931, 15, "default"], [1067, 75, 931, 19], [1068, 14, 931, 20, "style"], [1068, 19, 931, 25], [1068, 21, 931, 27, "styles"], [1068, 27, 931, 33], [1068, 28, 931, 34, "headerContent"], [1068, 41, 931, 48], [1069, 14, 931, 48, "children"], [1069, 22, 931, 48], [1069, 38, 932, 16], [1069, 42, 932, 16, "_jsxDevRuntime"], [1069, 56, 932, 16], [1069, 57, 932, 16, "jsxDEV"], [1069, 63, 932, 16], [1069, 65, 932, 17, "_View"], [1069, 70, 932, 17], [1069, 71, 932, 17, "default"], [1069, 78, 932, 21], [1070, 16, 932, 22, "style"], [1070, 21, 932, 27], [1070, 23, 932, 29, "styles"], [1070, 29, 932, 35], [1070, 30, 932, 36, "headerLeft"], [1070, 40, 932, 47], [1071, 16, 932, 47, "children"], [1071, 24, 932, 47], [1071, 40, 933, 18], [1071, 44, 933, 18, "_jsxDevRuntime"], [1071, 58, 933, 18], [1071, 59, 933, 18, "jsxDEV"], [1071, 65, 933, 18], [1071, 67, 933, 19, "_Text"], [1071, 72, 933, 19], [1071, 73, 933, 19, "default"], [1071, 80, 933, 23], [1072, 18, 933, 24, "style"], [1072, 23, 933, 29], [1072, 25, 933, 31, "styles"], [1072, 31, 933, 37], [1072, 32, 933, 38, "headerTitle"], [1072, 43, 933, 50], [1073, 18, 933, 50, "children"], [1073, 26, 933, 50], [1073, 28, 933, 51], [1074, 16, 933, 62], [1075, 18, 933, 62, "fileName"], [1075, 26, 933, 62], [1075, 28, 933, 62, "_jsxFileName"], [1075, 40, 933, 62], [1076, 18, 933, 62, "lineNumber"], [1076, 28, 933, 62], [1077, 18, 933, 62, "columnNumber"], [1077, 30, 933, 62], [1078, 16, 933, 62], [1078, 23, 933, 68], [1078, 24, 933, 69], [1078, 39, 934, 18], [1078, 43, 934, 18, "_jsxDevRuntime"], [1078, 57, 934, 18], [1078, 58, 934, 18, "jsxDEV"], [1078, 64, 934, 18], [1078, 66, 934, 19, "_View"], [1078, 71, 934, 19], [1078, 72, 934, 19, "default"], [1078, 79, 934, 23], [1079, 18, 934, 24, "style"], [1079, 23, 934, 29], [1079, 25, 934, 31, "styles"], [1079, 31, 934, 37], [1079, 32, 934, 38, "subtitleRow"], [1079, 43, 934, 50], [1080, 18, 934, 50, "children"], [1080, 26, 934, 50], [1080, 42, 935, 20], [1080, 46, 935, 20, "_jsxDevRuntime"], [1080, 60, 935, 20], [1080, 61, 935, 20, "jsxDEV"], [1080, 67, 935, 20], [1080, 69, 935, 21, "_Text"], [1080, 74, 935, 21], [1080, 75, 935, 21, "default"], [1080, 82, 935, 25], [1081, 20, 935, 26, "style"], [1081, 25, 935, 31], [1081, 27, 935, 33, "styles"], [1081, 33, 935, 39], [1081, 34, 935, 40, "webIcon"], [1081, 41, 935, 48], [1082, 20, 935, 48, "children"], [1082, 28, 935, 48], [1082, 30, 935, 49], [1083, 18, 935, 51], [1084, 20, 935, 51, "fileName"], [1084, 28, 935, 51], [1084, 30, 935, 51, "_jsxFileName"], [1084, 42, 935, 51], [1085, 20, 935, 51, "lineNumber"], [1085, 30, 935, 51], [1086, 20, 935, 51, "columnNumber"], [1086, 32, 935, 51], [1087, 18, 935, 51], [1087, 25, 935, 57], [1087, 26, 935, 58], [1087, 41, 936, 20], [1087, 45, 936, 20, "_jsxDevRuntime"], [1087, 59, 936, 20], [1087, 60, 936, 20, "jsxDEV"], [1087, 66, 936, 20], [1087, 68, 936, 21, "_Text"], [1087, 73, 936, 21], [1087, 74, 936, 21, "default"], [1087, 81, 936, 25], [1088, 20, 936, 26, "style"], [1088, 25, 936, 31], [1088, 27, 936, 33, "styles"], [1088, 33, 936, 39], [1088, 34, 936, 40, "headerSubtitle"], [1088, 48, 936, 55], [1089, 20, 936, 55, "children"], [1089, 28, 936, 55], [1089, 30, 936, 56], [1090, 18, 936, 71], [1091, 20, 936, 71, "fileName"], [1091, 28, 936, 71], [1091, 30, 936, 71, "_jsxFileName"], [1091, 42, 936, 71], [1092, 20, 936, 71, "lineNumber"], [1092, 30, 936, 71], [1093, 20, 936, 71, "columnNumber"], [1093, 32, 936, 71], [1094, 18, 936, 71], [1094, 25, 936, 77], [1094, 26, 936, 78], [1095, 16, 936, 78], [1096, 18, 936, 78, "fileName"], [1096, 26, 936, 78], [1096, 28, 936, 78, "_jsxFileName"], [1096, 40, 936, 78], [1097, 18, 936, 78, "lineNumber"], [1097, 28, 936, 78], [1098, 18, 936, 78, "columnNumber"], [1098, 30, 936, 78], [1099, 16, 936, 78], [1099, 23, 937, 24], [1099, 24, 937, 25], [1099, 26, 938, 19, "challengeCode"], [1099, 39, 938, 32], [1099, 56, 939, 20], [1099, 60, 939, 20, "_jsxDevRuntime"], [1099, 74, 939, 20], [1099, 75, 939, 20, "jsxDEV"], [1099, 81, 939, 20], [1099, 83, 939, 21, "_View"], [1099, 88, 939, 21], [1099, 89, 939, 21, "default"], [1099, 96, 939, 25], [1100, 18, 939, 26, "style"], [1100, 23, 939, 31], [1100, 25, 939, 33, "styles"], [1100, 31, 939, 39], [1100, 32, 939, 40, "challengeRow"], [1100, 44, 939, 53], [1101, 18, 939, 53, "children"], [1101, 26, 939, 53], [1101, 42, 940, 22], [1101, 46, 940, 22, "_jsxDevRuntime"], [1101, 60, 940, 22], [1101, 61, 940, 22, "jsxDEV"], [1101, 67, 940, 22], [1101, 69, 940, 23, "_lucideReactNative"], [1101, 87, 940, 23], [1101, 88, 940, 23, "Shield"], [1101, 94, 940, 29], [1102, 20, 940, 30, "size"], [1102, 24, 940, 34], [1102, 26, 940, 36], [1102, 28, 940, 39], [1103, 20, 940, 40, "color"], [1103, 25, 940, 45], [1103, 27, 940, 46], [1104, 18, 940, 52], [1105, 20, 940, 52, "fileName"], [1105, 28, 940, 52], [1105, 30, 940, 52, "_jsxFileName"], [1105, 42, 940, 52], [1106, 20, 940, 52, "lineNumber"], [1106, 30, 940, 52], [1107, 20, 940, 52, "columnNumber"], [1107, 32, 940, 52], [1108, 18, 940, 52], [1108, 25, 940, 54], [1108, 26, 940, 55], [1108, 41, 941, 22], [1108, 45, 941, 22, "_jsxDevRuntime"], [1108, 59, 941, 22], [1108, 60, 941, 22, "jsxDEV"], [1108, 66, 941, 22], [1108, 68, 941, 23, "_Text"], [1108, 73, 941, 23], [1108, 74, 941, 23, "default"], [1108, 81, 941, 27], [1109, 20, 941, 28, "style"], [1109, 25, 941, 33], [1109, 27, 941, 35, "styles"], [1109, 33, 941, 41], [1109, 34, 941, 42, "challengeCode"], [1109, 47, 941, 56], [1110, 20, 941, 56, "children"], [1110, 28, 941, 56], [1110, 30, 941, 58, "challengeCode"], [1111, 18, 941, 71], [1112, 20, 941, 71, "fileName"], [1112, 28, 941, 71], [1112, 30, 941, 71, "_jsxFileName"], [1112, 42, 941, 71], [1113, 20, 941, 71, "lineNumber"], [1113, 30, 941, 71], [1114, 20, 941, 71, "columnNumber"], [1114, 32, 941, 71], [1115, 18, 941, 71], [1115, 25, 941, 78], [1115, 26, 941, 79], [1116, 16, 941, 79], [1117, 18, 941, 79, "fileName"], [1117, 26, 941, 79], [1117, 28, 941, 79, "_jsxFileName"], [1117, 40, 941, 79], [1118, 18, 941, 79, "lineNumber"], [1118, 28, 941, 79], [1119, 18, 941, 79, "columnNumber"], [1119, 30, 941, 79], [1120, 16, 941, 79], [1120, 23, 942, 26], [1120, 24, 943, 19], [1121, 14, 943, 19], [1122, 16, 943, 19, "fileName"], [1122, 24, 943, 19], [1122, 26, 943, 19, "_jsxFileName"], [1122, 38, 943, 19], [1123, 16, 943, 19, "lineNumber"], [1123, 26, 943, 19], [1124, 16, 943, 19, "columnNumber"], [1124, 28, 943, 19], [1125, 14, 943, 19], [1125, 21, 944, 22], [1125, 22, 944, 23], [1125, 37, 945, 16], [1125, 41, 945, 16, "_jsxDevRuntime"], [1125, 55, 945, 16], [1125, 56, 945, 16, "jsxDEV"], [1125, 62, 945, 16], [1125, 64, 945, 17, "_TouchableOpacity"], [1125, 81, 945, 17], [1125, 82, 945, 17, "default"], [1125, 89, 945, 33], [1126, 16, 945, 34, "onPress"], [1126, 23, 945, 41], [1126, 25, 945, 43, "onCancel"], [1126, 33, 945, 52], [1127, 16, 945, 53, "style"], [1127, 21, 945, 58], [1127, 23, 945, 60, "styles"], [1127, 29, 945, 66], [1127, 30, 945, 67, "closeButton"], [1127, 41, 945, 79], [1128, 16, 945, 79, "children"], [1128, 24, 945, 79], [1128, 39, 946, 18], [1128, 43, 946, 18, "_jsxDevRuntime"], [1128, 57, 946, 18], [1128, 58, 946, 18, "jsxDEV"], [1128, 64, 946, 18], [1128, 66, 946, 19, "_lucideReactNative"], [1128, 84, 946, 19], [1128, 85, 946, 19, "X"], [1128, 86, 946, 20], [1129, 18, 946, 21, "size"], [1129, 22, 946, 25], [1129, 24, 946, 27], [1129, 26, 946, 30], [1130, 18, 946, 31, "color"], [1130, 23, 946, 36], [1130, 25, 946, 37], [1131, 16, 946, 43], [1132, 18, 946, 43, "fileName"], [1132, 26, 946, 43], [1132, 28, 946, 43, "_jsxFileName"], [1132, 40, 946, 43], [1133, 18, 946, 43, "lineNumber"], [1133, 28, 946, 43], [1134, 18, 946, 43, "columnNumber"], [1134, 30, 946, 43], [1135, 16, 946, 43], [1135, 23, 946, 45], [1136, 14, 946, 46], [1137, 16, 946, 46, "fileName"], [1137, 24, 946, 46], [1137, 26, 946, 46, "_jsxFileName"], [1137, 38, 946, 46], [1138, 16, 946, 46, "lineNumber"], [1138, 26, 946, 46], [1139, 16, 946, 46, "columnNumber"], [1139, 28, 946, 46], [1140, 14, 946, 46], [1140, 21, 947, 34], [1140, 22, 947, 35], [1141, 12, 947, 35], [1142, 14, 947, 35, "fileName"], [1142, 22, 947, 35], [1142, 24, 947, 35, "_jsxFileName"], [1142, 36, 947, 35], [1143, 14, 947, 35, "lineNumber"], [1143, 24, 947, 35], [1144, 14, 947, 35, "columnNumber"], [1144, 26, 947, 35], [1145, 12, 947, 35], [1145, 19, 948, 20], [1146, 10, 948, 21], [1147, 12, 948, 21, "fileName"], [1147, 20, 948, 21], [1147, 22, 948, 21, "_jsxFileName"], [1147, 34, 948, 21], [1148, 12, 948, 21, "lineNumber"], [1148, 22, 948, 21], [1149, 12, 948, 21, "columnNumber"], [1149, 24, 948, 21], [1150, 10, 948, 21], [1150, 17, 949, 18], [1150, 18, 949, 19], [1150, 33, 951, 12], [1150, 37, 951, 12, "_jsxDevRuntime"], [1150, 51, 951, 12], [1150, 52, 951, 12, "jsxDEV"], [1150, 58, 951, 12], [1150, 60, 951, 13, "_View"], [1150, 65, 951, 13], [1150, 66, 951, 13, "default"], [1150, 73, 951, 17], [1151, 12, 951, 18, "style"], [1151, 17, 951, 23], [1151, 19, 951, 25, "styles"], [1151, 25, 951, 31], [1151, 26, 951, 32, "privacyNotice"], [1151, 39, 951, 46], [1152, 12, 951, 46, "children"], [1152, 20, 951, 46], [1152, 36, 952, 14], [1152, 40, 952, 14, "_jsxDevRuntime"], [1152, 54, 952, 14], [1152, 55, 952, 14, "jsxDEV"], [1152, 61, 952, 14], [1152, 63, 952, 15, "_lucideReactNative"], [1152, 81, 952, 15], [1152, 82, 952, 15, "Shield"], [1152, 88, 952, 21], [1153, 14, 952, 22, "size"], [1153, 18, 952, 26], [1153, 20, 952, 28], [1153, 22, 952, 31], [1154, 14, 952, 32, "color"], [1154, 19, 952, 37], [1154, 21, 952, 38], [1155, 12, 952, 47], [1156, 14, 952, 47, "fileName"], [1156, 22, 952, 47], [1156, 24, 952, 47, "_jsxFileName"], [1156, 36, 952, 47], [1157, 14, 952, 47, "lineNumber"], [1157, 24, 952, 47], [1158, 14, 952, 47, "columnNumber"], [1158, 26, 952, 47], [1159, 12, 952, 47], [1159, 19, 952, 49], [1159, 20, 952, 50], [1159, 35, 953, 14], [1159, 39, 953, 14, "_jsxDevRuntime"], [1159, 53, 953, 14], [1159, 54, 953, 14, "jsxDEV"], [1159, 60, 953, 14], [1159, 62, 953, 15, "_Text"], [1159, 67, 953, 15], [1159, 68, 953, 15, "default"], [1159, 75, 953, 19], [1160, 14, 953, 20, "style"], [1160, 19, 953, 25], [1160, 21, 953, 27, "styles"], [1160, 27, 953, 33], [1160, 28, 953, 34, "privacyText"], [1160, 39, 953, 46], [1161, 14, 953, 46, "children"], [1161, 22, 953, 46], [1161, 24, 953, 47], [1162, 12, 955, 14], [1163, 14, 955, 14, "fileName"], [1163, 22, 955, 14], [1163, 24, 955, 14, "_jsxFileName"], [1163, 36, 955, 14], [1164, 14, 955, 14, "lineNumber"], [1164, 24, 955, 14], [1165, 14, 955, 14, "columnNumber"], [1165, 26, 955, 14], [1166, 12, 955, 14], [1166, 19, 955, 20], [1166, 20, 955, 21], [1167, 10, 955, 21], [1168, 12, 955, 21, "fileName"], [1168, 20, 955, 21], [1168, 22, 955, 21, "_jsxFileName"], [1168, 34, 955, 21], [1169, 12, 955, 21, "lineNumber"], [1169, 22, 955, 21], [1170, 12, 955, 21, "columnNumber"], [1170, 24, 955, 21], [1171, 10, 955, 21], [1171, 17, 956, 18], [1171, 18, 956, 19], [1171, 33, 958, 12], [1171, 37, 958, 12, "_jsxDevRuntime"], [1171, 51, 958, 12], [1171, 52, 958, 12, "jsxDEV"], [1171, 58, 958, 12], [1171, 60, 958, 13, "_View"], [1171, 65, 958, 13], [1171, 66, 958, 13, "default"], [1171, 73, 958, 17], [1172, 12, 958, 18, "style"], [1172, 17, 958, 23], [1172, 19, 958, 25, "styles"], [1172, 25, 958, 31], [1172, 26, 958, 32, "footer<PERSON><PERSON><PERSON>"], [1172, 39, 958, 46], [1173, 12, 958, 46, "children"], [1173, 20, 958, 46], [1173, 36, 959, 14], [1173, 40, 959, 14, "_jsxDevRuntime"], [1173, 54, 959, 14], [1173, 55, 959, 14, "jsxDEV"], [1173, 61, 959, 14], [1173, 63, 959, 15, "_Text"], [1173, 68, 959, 15], [1173, 69, 959, 15, "default"], [1173, 76, 959, 19], [1174, 14, 959, 20, "style"], [1174, 19, 959, 25], [1174, 21, 959, 27, "styles"], [1174, 27, 959, 33], [1174, 28, 959, 34, "instruction"], [1174, 39, 959, 46], [1175, 14, 959, 46, "children"], [1175, 22, 959, 46], [1175, 24, 959, 47], [1176, 12, 961, 14], [1177, 14, 961, 14, "fileName"], [1177, 22, 961, 14], [1177, 24, 961, 14, "_jsxFileName"], [1177, 36, 961, 14], [1178, 14, 961, 14, "lineNumber"], [1178, 24, 961, 14], [1179, 14, 961, 14, "columnNumber"], [1179, 26, 961, 14], [1180, 12, 961, 14], [1180, 19, 961, 20], [1180, 20, 961, 21], [1180, 35, 963, 14], [1180, 39, 963, 14, "_jsxDevRuntime"], [1180, 53, 963, 14], [1180, 54, 963, 14, "jsxDEV"], [1180, 60, 963, 14], [1180, 62, 963, 15, "_TouchableOpacity"], [1180, 79, 963, 15], [1180, 80, 963, 15, "default"], [1180, 87, 963, 31], [1181, 14, 964, 16, "onPress"], [1181, 21, 964, 23], [1181, 23, 964, 25, "capturePhoto"], [1181, 35, 964, 38], [1182, 14, 965, 16, "disabled"], [1182, 22, 965, 24], [1182, 24, 965, 26, "processingState"], [1182, 39, 965, 41], [1182, 44, 965, 46], [1182, 50, 965, 52], [1182, 54, 965, 56], [1182, 55, 965, 57, "isCameraReady"], [1182, 68, 965, 71], [1183, 14, 966, 16, "style"], [1183, 19, 966, 21], [1183, 21, 966, 23], [1183, 22, 967, 18, "styles"], [1183, 28, 967, 24], [1183, 29, 967, 25, "shutterButton"], [1183, 42, 967, 38], [1183, 44, 968, 18, "processingState"], [1183, 59, 968, 33], [1183, 64, 968, 38], [1183, 70, 968, 44], [1183, 74, 968, 48, "styles"], [1183, 80, 968, 54], [1183, 81, 968, 55, "shutterButtonDisabled"], [1183, 102, 968, 76], [1183, 103, 969, 18], [1184, 14, 969, 18, "children"], [1184, 22, 969, 18], [1184, 24, 971, 17, "processingState"], [1184, 39, 971, 32], [1184, 44, 971, 37], [1184, 50, 971, 43], [1184, 66, 972, 18], [1184, 70, 972, 18, "_jsxDevRuntime"], [1184, 84, 972, 18], [1184, 85, 972, 18, "jsxDEV"], [1184, 91, 972, 18], [1184, 93, 972, 19, "_View"], [1184, 98, 972, 19], [1184, 99, 972, 19, "default"], [1184, 106, 972, 23], [1185, 16, 972, 24, "style"], [1185, 21, 972, 29], [1185, 23, 972, 31, "styles"], [1185, 29, 972, 37], [1185, 30, 972, 38, "shutterInner"], [1186, 14, 972, 51], [1187, 16, 972, 51, "fileName"], [1187, 24, 972, 51], [1187, 26, 972, 51, "_jsxFileName"], [1187, 38, 972, 51], [1188, 16, 972, 51, "lineNumber"], [1188, 26, 972, 51], [1189, 16, 972, 51, "columnNumber"], [1189, 28, 972, 51], [1190, 14, 972, 51], [1190, 21, 972, 53], [1190, 22, 972, 54], [1190, 38, 974, 18], [1190, 42, 974, 18, "_jsxDevRuntime"], [1190, 56, 974, 18], [1190, 57, 974, 18, "jsxDEV"], [1190, 63, 974, 18], [1190, 65, 974, 19, "_ActivityIndicator"], [1190, 83, 974, 19], [1190, 84, 974, 19, "default"], [1190, 91, 974, 36], [1191, 16, 974, 37, "size"], [1191, 20, 974, 41], [1191, 22, 974, 42], [1191, 29, 974, 49], [1192, 16, 974, 50, "color"], [1192, 21, 974, 55], [1192, 23, 974, 56], [1193, 14, 974, 65], [1194, 16, 974, 65, "fileName"], [1194, 24, 974, 65], [1194, 26, 974, 65, "_jsxFileName"], [1194, 38, 974, 65], [1195, 16, 974, 65, "lineNumber"], [1195, 26, 974, 65], [1196, 16, 974, 65, "columnNumber"], [1196, 28, 974, 65], [1197, 14, 974, 65], [1197, 21, 974, 67], [1198, 12, 975, 17], [1199, 14, 975, 17, "fileName"], [1199, 22, 975, 17], [1199, 24, 975, 17, "_jsxFileName"], [1199, 36, 975, 17], [1200, 14, 975, 17, "lineNumber"], [1200, 24, 975, 17], [1201, 14, 975, 17, "columnNumber"], [1201, 26, 975, 17], [1202, 12, 975, 17], [1202, 19, 976, 32], [1202, 20, 976, 33], [1202, 35, 977, 14], [1202, 39, 977, 14, "_jsxDevRuntime"], [1202, 53, 977, 14], [1202, 54, 977, 14, "jsxDEV"], [1202, 60, 977, 14], [1202, 62, 977, 15, "_Text"], [1202, 67, 977, 15], [1202, 68, 977, 15, "default"], [1202, 75, 977, 19], [1203, 14, 977, 20, "style"], [1203, 19, 977, 25], [1203, 21, 977, 27, "styles"], [1203, 27, 977, 33], [1203, 28, 977, 34, "privacyNote"], [1203, 39, 977, 46], [1204, 14, 977, 46, "children"], [1204, 22, 977, 46], [1204, 24, 977, 47], [1205, 12, 979, 14], [1206, 14, 979, 14, "fileName"], [1206, 22, 979, 14], [1206, 24, 979, 14, "_jsxFileName"], [1206, 36, 979, 14], [1207, 14, 979, 14, "lineNumber"], [1207, 24, 979, 14], [1208, 14, 979, 14, "columnNumber"], [1208, 26, 979, 14], [1209, 12, 979, 14], [1209, 19, 979, 20], [1209, 20, 979, 21], [1210, 10, 979, 21], [1211, 12, 979, 21, "fileName"], [1211, 20, 979, 21], [1211, 22, 979, 21, "_jsxFileName"], [1211, 34, 979, 21], [1212, 12, 979, 21, "lineNumber"], [1212, 22, 979, 21], [1213, 12, 979, 21, "columnNumber"], [1213, 24, 979, 21], [1214, 10, 979, 21], [1214, 17, 980, 18], [1214, 18, 980, 19], [1215, 8, 980, 19], [1215, 23, 981, 12], [1215, 24, 982, 9], [1216, 6, 982, 9], [1217, 8, 982, 9, "fileName"], [1217, 16, 982, 9], [1217, 18, 982, 9, "_jsxFileName"], [1217, 30, 982, 9], [1218, 8, 982, 9, "lineNumber"], [1218, 18, 982, 9], [1219, 8, 982, 9, "columnNumber"], [1219, 20, 982, 9], [1220, 6, 982, 9], [1220, 13, 983, 12], [1220, 14, 983, 13], [1220, 29, 985, 6], [1220, 33, 985, 6, "_jsxDevRuntime"], [1220, 47, 985, 6], [1220, 48, 985, 6, "jsxDEV"], [1220, 54, 985, 6], [1220, 56, 985, 7, "_Modal"], [1220, 62, 985, 7], [1220, 63, 985, 7, "default"], [1220, 70, 985, 12], [1221, 8, 986, 8, "visible"], [1221, 15, 986, 15], [1221, 17, 986, 17, "processingState"], [1221, 32, 986, 32], [1221, 37, 986, 37], [1221, 43, 986, 43], [1221, 47, 986, 47, "processingState"], [1221, 62, 986, 62], [1221, 67, 986, 67], [1221, 74, 986, 75], [1222, 8, 987, 8, "transparent"], [1222, 19, 987, 19], [1223, 8, 988, 8, "animationType"], [1223, 21, 988, 21], [1223, 23, 988, 22], [1223, 29, 988, 28], [1224, 8, 988, 28, "children"], [1224, 16, 988, 28], [1224, 31, 990, 8], [1224, 35, 990, 8, "_jsxDevRuntime"], [1224, 49, 990, 8], [1224, 50, 990, 8, "jsxDEV"], [1224, 56, 990, 8], [1224, 58, 990, 9, "_View"], [1224, 63, 990, 9], [1224, 64, 990, 9, "default"], [1224, 71, 990, 13], [1225, 10, 990, 14, "style"], [1225, 15, 990, 19], [1225, 17, 990, 21, "styles"], [1225, 23, 990, 27], [1225, 24, 990, 28, "processingModal"], [1225, 39, 990, 44], [1226, 10, 990, 44, "children"], [1226, 18, 990, 44], [1226, 33, 991, 10], [1226, 37, 991, 10, "_jsxDevRuntime"], [1226, 51, 991, 10], [1226, 52, 991, 10, "jsxDEV"], [1226, 58, 991, 10], [1226, 60, 991, 11, "_View"], [1226, 65, 991, 11], [1226, 66, 991, 11, "default"], [1226, 73, 991, 15], [1227, 12, 991, 16, "style"], [1227, 17, 991, 21], [1227, 19, 991, 23, "styles"], [1227, 25, 991, 29], [1227, 26, 991, 30, "processingContent"], [1227, 43, 991, 48], [1228, 12, 991, 48, "children"], [1228, 20, 991, 48], [1228, 36, 992, 12], [1228, 40, 992, 12, "_jsxDevRuntime"], [1228, 54, 992, 12], [1228, 55, 992, 12, "jsxDEV"], [1228, 61, 992, 12], [1228, 63, 992, 13, "_ActivityIndicator"], [1228, 81, 992, 13], [1228, 82, 992, 13, "default"], [1228, 89, 992, 30], [1229, 14, 992, 31, "size"], [1229, 18, 992, 35], [1229, 20, 992, 36], [1229, 27, 992, 43], [1230, 14, 992, 44, "color"], [1230, 19, 992, 49], [1230, 21, 992, 50], [1231, 12, 992, 59], [1232, 14, 992, 59, "fileName"], [1232, 22, 992, 59], [1232, 24, 992, 59, "_jsxFileName"], [1232, 36, 992, 59], [1233, 14, 992, 59, "lineNumber"], [1233, 24, 992, 59], [1234, 14, 992, 59, "columnNumber"], [1234, 26, 992, 59], [1235, 12, 992, 59], [1235, 19, 992, 61], [1235, 20, 992, 62], [1235, 35, 994, 12], [1235, 39, 994, 12, "_jsxDevRuntime"], [1235, 53, 994, 12], [1235, 54, 994, 12, "jsxDEV"], [1235, 60, 994, 12], [1235, 62, 994, 13, "_Text"], [1235, 67, 994, 13], [1235, 68, 994, 13, "default"], [1235, 75, 994, 17], [1236, 14, 994, 18, "style"], [1236, 19, 994, 23], [1236, 21, 994, 25, "styles"], [1236, 27, 994, 31], [1236, 28, 994, 32, "processingTitle"], [1236, 43, 994, 48], [1237, 14, 994, 48, "children"], [1237, 22, 994, 48], [1237, 25, 995, 15, "processingState"], [1237, 40, 995, 30], [1237, 45, 995, 35], [1237, 56, 995, 46], [1237, 60, 995, 50], [1237, 80, 995, 70], [1237, 82, 996, 15, "processingState"], [1237, 97, 996, 30], [1237, 102, 996, 35], [1237, 113, 996, 46], [1237, 117, 996, 50], [1237, 146, 996, 79], [1237, 148, 997, 15, "processingState"], [1237, 163, 997, 30], [1237, 168, 997, 35], [1237, 180, 997, 47], [1237, 184, 997, 51], [1237, 216, 997, 83], [1237, 218, 998, 15, "processingState"], [1237, 233, 998, 30], [1237, 238, 998, 35], [1237, 249, 998, 46], [1237, 253, 998, 50], [1237, 275, 998, 72], [1238, 12, 998, 72], [1239, 14, 998, 72, "fileName"], [1239, 22, 998, 72], [1239, 24, 998, 72, "_jsxFileName"], [1239, 36, 998, 72], [1240, 14, 998, 72, "lineNumber"], [1240, 24, 998, 72], [1241, 14, 998, 72, "columnNumber"], [1241, 26, 998, 72], [1242, 12, 998, 72], [1242, 19, 999, 18], [1242, 20, 999, 19], [1242, 35, 1000, 12], [1242, 39, 1000, 12, "_jsxDevRuntime"], [1242, 53, 1000, 12], [1242, 54, 1000, 12, "jsxDEV"], [1242, 60, 1000, 12], [1242, 62, 1000, 13, "_View"], [1242, 67, 1000, 13], [1242, 68, 1000, 13, "default"], [1242, 75, 1000, 17], [1243, 14, 1000, 18, "style"], [1243, 19, 1000, 23], [1243, 21, 1000, 25, "styles"], [1243, 27, 1000, 31], [1243, 28, 1000, 32, "progressBar"], [1243, 39, 1000, 44], [1244, 14, 1000, 44, "children"], [1244, 22, 1000, 44], [1244, 37, 1001, 14], [1244, 41, 1001, 14, "_jsxDevRuntime"], [1244, 55, 1001, 14], [1244, 56, 1001, 14, "jsxDEV"], [1244, 62, 1001, 14], [1244, 64, 1001, 15, "_View"], [1244, 69, 1001, 15], [1244, 70, 1001, 15, "default"], [1244, 77, 1001, 19], [1245, 16, 1002, 16, "style"], [1245, 21, 1002, 21], [1245, 23, 1002, 23], [1245, 24, 1003, 18, "styles"], [1245, 30, 1003, 24], [1245, 31, 1003, 25, "progressFill"], [1245, 43, 1003, 37], [1245, 45, 1004, 18], [1246, 18, 1004, 20, "width"], [1246, 23, 1004, 25], [1246, 25, 1004, 27], [1246, 28, 1004, 30, "processingProgress"], [1246, 46, 1004, 48], [1247, 16, 1004, 52], [1247, 17, 1004, 53], [1248, 14, 1005, 18], [1249, 16, 1005, 18, "fileName"], [1249, 24, 1005, 18], [1249, 26, 1005, 18, "_jsxFileName"], [1249, 38, 1005, 18], [1250, 16, 1005, 18, "lineNumber"], [1250, 26, 1005, 18], [1251, 16, 1005, 18, "columnNumber"], [1251, 28, 1005, 18], [1252, 14, 1005, 18], [1252, 21, 1006, 15], [1253, 12, 1006, 16], [1254, 14, 1006, 16, "fileName"], [1254, 22, 1006, 16], [1254, 24, 1006, 16, "_jsxFileName"], [1254, 36, 1006, 16], [1255, 14, 1006, 16, "lineNumber"], [1255, 24, 1006, 16], [1256, 14, 1006, 16, "columnNumber"], [1256, 26, 1006, 16], [1257, 12, 1006, 16], [1257, 19, 1007, 18], [1257, 20, 1007, 19], [1257, 35, 1008, 12], [1257, 39, 1008, 12, "_jsxDevRuntime"], [1257, 53, 1008, 12], [1257, 54, 1008, 12, "jsxDEV"], [1257, 60, 1008, 12], [1257, 62, 1008, 13, "_Text"], [1257, 67, 1008, 13], [1257, 68, 1008, 13, "default"], [1257, 75, 1008, 17], [1258, 14, 1008, 18, "style"], [1258, 19, 1008, 23], [1258, 21, 1008, 25, "styles"], [1258, 27, 1008, 31], [1258, 28, 1008, 32, "processingDescription"], [1258, 49, 1008, 54], [1259, 14, 1008, 54, "children"], [1259, 22, 1008, 54], [1259, 25, 1009, 15, "processingState"], [1259, 40, 1009, 30], [1259, 45, 1009, 35], [1259, 56, 1009, 46], [1259, 60, 1009, 50], [1259, 89, 1009, 79], [1259, 91, 1010, 15, "processingState"], [1259, 106, 1010, 30], [1259, 111, 1010, 35], [1259, 122, 1010, 46], [1259, 126, 1010, 50], [1259, 164, 1010, 88], [1259, 166, 1011, 15, "processingState"], [1259, 181, 1011, 30], [1259, 186, 1011, 35], [1259, 198, 1011, 47], [1259, 202, 1011, 51], [1259, 247, 1011, 96], [1259, 249, 1012, 15, "processingState"], [1259, 264, 1012, 30], [1259, 269, 1012, 35], [1259, 280, 1012, 46], [1259, 284, 1012, 50], [1259, 325, 1012, 91], [1260, 12, 1012, 91], [1261, 14, 1012, 91, "fileName"], [1261, 22, 1012, 91], [1261, 24, 1012, 91, "_jsxFileName"], [1261, 36, 1012, 91], [1262, 14, 1012, 91, "lineNumber"], [1262, 24, 1012, 91], [1263, 14, 1012, 91, "columnNumber"], [1263, 26, 1012, 91], [1264, 12, 1012, 91], [1264, 19, 1013, 18], [1264, 20, 1013, 19], [1264, 22, 1014, 13, "processingState"], [1264, 37, 1014, 28], [1264, 42, 1014, 33], [1264, 53, 1014, 44], [1264, 70, 1015, 14], [1264, 74, 1015, 14, "_jsxDevRuntime"], [1264, 88, 1015, 14], [1264, 89, 1015, 14, "jsxDEV"], [1264, 95, 1015, 14], [1264, 97, 1015, 15, "_lucideReactNative"], [1264, 115, 1015, 15], [1264, 116, 1015, 15, "CheckCircle"], [1264, 127, 1015, 26], [1265, 14, 1015, 27, "size"], [1265, 18, 1015, 31], [1265, 20, 1015, 33], [1265, 22, 1015, 36], [1266, 14, 1015, 37, "color"], [1266, 19, 1015, 42], [1266, 21, 1015, 43], [1266, 30, 1015, 52], [1267, 14, 1015, 53, "style"], [1267, 19, 1015, 58], [1267, 21, 1015, 60, "styles"], [1267, 27, 1015, 66], [1267, 28, 1015, 67, "successIcon"], [1268, 12, 1015, 79], [1269, 14, 1015, 79, "fileName"], [1269, 22, 1015, 79], [1269, 24, 1015, 79, "_jsxFileName"], [1269, 36, 1015, 79], [1270, 14, 1015, 79, "lineNumber"], [1270, 24, 1015, 79], [1271, 14, 1015, 79, "columnNumber"], [1271, 26, 1015, 79], [1272, 12, 1015, 79], [1272, 19, 1015, 81], [1272, 20, 1016, 13], [1273, 10, 1016, 13], [1274, 12, 1016, 13, "fileName"], [1274, 20, 1016, 13], [1274, 22, 1016, 13, "_jsxFileName"], [1274, 34, 1016, 13], [1275, 12, 1016, 13, "lineNumber"], [1275, 22, 1016, 13], [1276, 12, 1016, 13, "columnNumber"], [1276, 24, 1016, 13], [1277, 10, 1016, 13], [1277, 17, 1017, 16], [1278, 8, 1017, 17], [1279, 10, 1017, 17, "fileName"], [1279, 18, 1017, 17], [1279, 20, 1017, 17, "_jsxFileName"], [1279, 32, 1017, 17], [1280, 10, 1017, 17, "lineNumber"], [1280, 20, 1017, 17], [1281, 10, 1017, 17, "columnNumber"], [1281, 22, 1017, 17], [1282, 8, 1017, 17], [1282, 15, 1018, 14], [1283, 6, 1018, 15], [1284, 8, 1018, 15, "fileName"], [1284, 16, 1018, 15], [1284, 18, 1018, 15, "_jsxFileName"], [1284, 30, 1018, 15], [1285, 8, 1018, 15, "lineNumber"], [1285, 18, 1018, 15], [1286, 8, 1018, 15, "columnNumber"], [1286, 20, 1018, 15], [1287, 6, 1018, 15], [1287, 13, 1019, 13], [1287, 14, 1019, 14], [1287, 29, 1021, 6], [1287, 33, 1021, 6, "_jsxDevRuntime"], [1287, 47, 1021, 6], [1287, 48, 1021, 6, "jsxDEV"], [1287, 54, 1021, 6], [1287, 56, 1021, 7, "_Modal"], [1287, 62, 1021, 7], [1287, 63, 1021, 7, "default"], [1287, 70, 1021, 12], [1288, 8, 1022, 8, "visible"], [1288, 15, 1022, 15], [1288, 17, 1022, 17, "processingState"], [1288, 32, 1022, 32], [1288, 37, 1022, 37], [1288, 44, 1022, 45], [1289, 8, 1023, 8, "transparent"], [1289, 19, 1023, 19], [1290, 8, 1024, 8, "animationType"], [1290, 21, 1024, 21], [1290, 23, 1024, 22], [1290, 29, 1024, 28], [1291, 8, 1024, 28, "children"], [1291, 16, 1024, 28], [1291, 31, 1026, 8], [1291, 35, 1026, 8, "_jsxDevRuntime"], [1291, 49, 1026, 8], [1291, 50, 1026, 8, "jsxDEV"], [1291, 56, 1026, 8], [1291, 58, 1026, 9, "_View"], [1291, 63, 1026, 9], [1291, 64, 1026, 9, "default"], [1291, 71, 1026, 13], [1292, 10, 1026, 14, "style"], [1292, 15, 1026, 19], [1292, 17, 1026, 21, "styles"], [1292, 23, 1026, 27], [1292, 24, 1026, 28, "processingModal"], [1292, 39, 1026, 44], [1293, 10, 1026, 44, "children"], [1293, 18, 1026, 44], [1293, 33, 1027, 10], [1293, 37, 1027, 10, "_jsxDevRuntime"], [1293, 51, 1027, 10], [1293, 52, 1027, 10, "jsxDEV"], [1293, 58, 1027, 10], [1293, 60, 1027, 11, "_View"], [1293, 65, 1027, 11], [1293, 66, 1027, 11, "default"], [1293, 73, 1027, 15], [1294, 12, 1027, 16, "style"], [1294, 17, 1027, 21], [1294, 19, 1027, 23, "styles"], [1294, 25, 1027, 29], [1294, 26, 1027, 30, "errorContent"], [1294, 38, 1027, 43], [1295, 12, 1027, 43, "children"], [1295, 20, 1027, 43], [1295, 36, 1028, 12], [1295, 40, 1028, 12, "_jsxDevRuntime"], [1295, 54, 1028, 12], [1295, 55, 1028, 12, "jsxDEV"], [1295, 61, 1028, 12], [1295, 63, 1028, 13, "_lucideReactNative"], [1295, 81, 1028, 13], [1295, 82, 1028, 13, "X"], [1295, 83, 1028, 14], [1296, 14, 1028, 15, "size"], [1296, 18, 1028, 19], [1296, 20, 1028, 21], [1296, 22, 1028, 24], [1297, 14, 1028, 25, "color"], [1297, 19, 1028, 30], [1297, 21, 1028, 31], [1298, 12, 1028, 40], [1299, 14, 1028, 40, "fileName"], [1299, 22, 1028, 40], [1299, 24, 1028, 40, "_jsxFileName"], [1299, 36, 1028, 40], [1300, 14, 1028, 40, "lineNumber"], [1300, 24, 1028, 40], [1301, 14, 1028, 40, "columnNumber"], [1301, 26, 1028, 40], [1302, 12, 1028, 40], [1302, 19, 1028, 42], [1302, 20, 1028, 43], [1302, 35, 1029, 12], [1302, 39, 1029, 12, "_jsxDevRuntime"], [1302, 53, 1029, 12], [1302, 54, 1029, 12, "jsxDEV"], [1302, 60, 1029, 12], [1302, 62, 1029, 13, "_Text"], [1302, 67, 1029, 13], [1302, 68, 1029, 13, "default"], [1302, 75, 1029, 17], [1303, 14, 1029, 18, "style"], [1303, 19, 1029, 23], [1303, 21, 1029, 25, "styles"], [1303, 27, 1029, 31], [1303, 28, 1029, 32, "errorTitle"], [1303, 38, 1029, 43], [1304, 14, 1029, 43, "children"], [1304, 22, 1029, 43], [1304, 24, 1029, 44], [1305, 12, 1029, 61], [1306, 14, 1029, 61, "fileName"], [1306, 22, 1029, 61], [1306, 24, 1029, 61, "_jsxFileName"], [1306, 36, 1029, 61], [1307, 14, 1029, 61, "lineNumber"], [1307, 24, 1029, 61], [1308, 14, 1029, 61, "columnNumber"], [1308, 26, 1029, 61], [1309, 12, 1029, 61], [1309, 19, 1029, 67], [1309, 20, 1029, 68], [1309, 35, 1030, 12], [1309, 39, 1030, 12, "_jsxDevRuntime"], [1309, 53, 1030, 12], [1309, 54, 1030, 12, "jsxDEV"], [1309, 60, 1030, 12], [1309, 62, 1030, 13, "_Text"], [1309, 67, 1030, 13], [1309, 68, 1030, 13, "default"], [1309, 75, 1030, 17], [1310, 14, 1030, 18, "style"], [1310, 19, 1030, 23], [1310, 21, 1030, 25, "styles"], [1310, 27, 1030, 31], [1310, 28, 1030, 32, "errorMessage"], [1310, 40, 1030, 45], [1311, 14, 1030, 45, "children"], [1311, 22, 1030, 45], [1311, 24, 1030, 47, "errorMessage"], [1312, 12, 1030, 59], [1313, 14, 1030, 59, "fileName"], [1313, 22, 1030, 59], [1313, 24, 1030, 59, "_jsxFileName"], [1313, 36, 1030, 59], [1314, 14, 1030, 59, "lineNumber"], [1314, 24, 1030, 59], [1315, 14, 1030, 59, "columnNumber"], [1315, 26, 1030, 59], [1316, 12, 1030, 59], [1316, 19, 1030, 66], [1316, 20, 1030, 67], [1316, 35, 1031, 12], [1316, 39, 1031, 12, "_jsxDevRuntime"], [1316, 53, 1031, 12], [1316, 54, 1031, 12, "jsxDEV"], [1316, 60, 1031, 12], [1316, 62, 1031, 13, "_TouchableOpacity"], [1316, 79, 1031, 13], [1316, 80, 1031, 13, "default"], [1316, 87, 1031, 29], [1317, 14, 1032, 14, "onPress"], [1317, 21, 1032, 21], [1317, 23, 1032, 23, "retryCapture"], [1317, 35, 1032, 36], [1318, 14, 1033, 14, "style"], [1318, 19, 1033, 19], [1318, 21, 1033, 21, "styles"], [1318, 27, 1033, 27], [1318, 28, 1033, 28, "primaryButton"], [1318, 41, 1033, 42], [1319, 14, 1033, 42, "children"], [1319, 22, 1033, 42], [1319, 37, 1035, 14], [1319, 41, 1035, 14, "_jsxDevRuntime"], [1319, 55, 1035, 14], [1319, 56, 1035, 14, "jsxDEV"], [1319, 62, 1035, 14], [1319, 64, 1035, 15, "_Text"], [1319, 69, 1035, 15], [1319, 70, 1035, 15, "default"], [1319, 77, 1035, 19], [1320, 16, 1035, 20, "style"], [1320, 21, 1035, 25], [1320, 23, 1035, 27, "styles"], [1320, 29, 1035, 33], [1320, 30, 1035, 34, "primaryButtonText"], [1320, 47, 1035, 52], [1321, 16, 1035, 52, "children"], [1321, 24, 1035, 52], [1321, 26, 1035, 53], [1322, 14, 1035, 62], [1323, 16, 1035, 62, "fileName"], [1323, 24, 1035, 62], [1323, 26, 1035, 62, "_jsxFileName"], [1323, 38, 1035, 62], [1324, 16, 1035, 62, "lineNumber"], [1324, 26, 1035, 62], [1325, 16, 1035, 62, "columnNumber"], [1325, 28, 1035, 62], [1326, 14, 1035, 62], [1326, 21, 1035, 68], [1327, 12, 1035, 69], [1328, 14, 1035, 69, "fileName"], [1328, 22, 1035, 69], [1328, 24, 1035, 69, "_jsxFileName"], [1328, 36, 1035, 69], [1329, 14, 1035, 69, "lineNumber"], [1329, 24, 1035, 69], [1330, 14, 1035, 69, "columnNumber"], [1330, 26, 1035, 69], [1331, 12, 1035, 69], [1331, 19, 1036, 30], [1331, 20, 1036, 31], [1331, 35, 1037, 12], [1331, 39, 1037, 12, "_jsxDevRuntime"], [1331, 53, 1037, 12], [1331, 54, 1037, 12, "jsxDEV"], [1331, 60, 1037, 12], [1331, 62, 1037, 13, "_TouchableOpacity"], [1331, 79, 1037, 13], [1331, 80, 1037, 13, "default"], [1331, 87, 1037, 29], [1332, 14, 1038, 14, "onPress"], [1332, 21, 1038, 21], [1332, 23, 1038, 23, "onCancel"], [1332, 31, 1038, 32], [1333, 14, 1039, 14, "style"], [1333, 19, 1039, 19], [1333, 21, 1039, 21, "styles"], [1333, 27, 1039, 27], [1333, 28, 1039, 28, "secondaryButton"], [1333, 43, 1039, 44], [1334, 14, 1039, 44, "children"], [1334, 22, 1039, 44], [1334, 37, 1041, 14], [1334, 41, 1041, 14, "_jsxDevRuntime"], [1334, 55, 1041, 14], [1334, 56, 1041, 14, "jsxDEV"], [1334, 62, 1041, 14], [1334, 64, 1041, 15, "_Text"], [1334, 69, 1041, 15], [1334, 70, 1041, 15, "default"], [1334, 77, 1041, 19], [1335, 16, 1041, 20, "style"], [1335, 21, 1041, 25], [1335, 23, 1041, 27, "styles"], [1335, 29, 1041, 33], [1335, 30, 1041, 34, "secondaryButtonText"], [1335, 49, 1041, 54], [1336, 16, 1041, 54, "children"], [1336, 24, 1041, 54], [1336, 26, 1041, 55], [1337, 14, 1041, 61], [1338, 16, 1041, 61, "fileName"], [1338, 24, 1041, 61], [1338, 26, 1041, 61, "_jsxFileName"], [1338, 38, 1041, 61], [1339, 16, 1041, 61, "lineNumber"], [1339, 26, 1041, 61], [1340, 16, 1041, 61, "columnNumber"], [1340, 28, 1041, 61], [1341, 14, 1041, 61], [1341, 21, 1041, 67], [1342, 12, 1041, 68], [1343, 14, 1041, 68, "fileName"], [1343, 22, 1041, 68], [1343, 24, 1041, 68, "_jsxFileName"], [1343, 36, 1041, 68], [1344, 14, 1041, 68, "lineNumber"], [1344, 24, 1041, 68], [1345, 14, 1041, 68, "columnNumber"], [1345, 26, 1041, 68], [1346, 12, 1041, 68], [1346, 19, 1042, 30], [1346, 20, 1042, 31], [1347, 10, 1042, 31], [1348, 12, 1042, 31, "fileName"], [1348, 20, 1042, 31], [1348, 22, 1042, 31, "_jsxFileName"], [1348, 34, 1042, 31], [1349, 12, 1042, 31, "lineNumber"], [1349, 22, 1042, 31], [1350, 12, 1042, 31, "columnNumber"], [1350, 24, 1042, 31], [1351, 10, 1042, 31], [1351, 17, 1043, 16], [1352, 8, 1043, 17], [1353, 10, 1043, 17, "fileName"], [1353, 18, 1043, 17], [1353, 20, 1043, 17, "_jsxFileName"], [1353, 32, 1043, 17], [1354, 10, 1043, 17, "lineNumber"], [1354, 20, 1043, 17], [1355, 10, 1043, 17, "columnNumber"], [1355, 22, 1043, 17], [1356, 8, 1043, 17], [1356, 15, 1044, 14], [1357, 6, 1044, 15], [1358, 8, 1044, 15, "fileName"], [1358, 16, 1044, 15], [1358, 18, 1044, 15, "_jsxFileName"], [1358, 30, 1044, 15], [1359, 8, 1044, 15, "lineNumber"], [1359, 18, 1044, 15], [1360, 8, 1044, 15, "columnNumber"], [1360, 20, 1044, 15], [1361, 6, 1044, 15], [1361, 13, 1045, 13], [1361, 14, 1045, 14], [1362, 4, 1045, 14], [1363, 6, 1045, 14, "fileName"], [1363, 14, 1045, 14], [1363, 16, 1045, 14, "_jsxFileName"], [1363, 28, 1045, 14], [1364, 6, 1045, 14, "lineNumber"], [1364, 16, 1045, 14], [1365, 6, 1045, 14, "columnNumber"], [1365, 18, 1045, 14], [1366, 4, 1045, 14], [1366, 11, 1046, 10], [1366, 12, 1046, 11], [1367, 2, 1048, 0], [1368, 2, 1048, 1, "_s"], [1368, 4, 1048, 1], [1368, 5, 51, 24, "EchoCameraWeb"], [1368, 18, 51, 37], [1369, 4, 51, 37], [1369, 12, 58, 42, "useCameraPermissions"], [1369, 44, 58, 62], [1369, 46, 72, 19, "useUpload"], [1369, 64, 72, 28], [1370, 2, 72, 28], [1371, 2, 72, 28, "_c"], [1371, 4, 72, 28], [1371, 7, 51, 24, "EchoCameraWeb"], [1371, 20, 51, 37], [1372, 2, 1049, 0], [1372, 8, 1049, 6, "styles"], [1372, 14, 1049, 12], [1372, 17, 1049, 15, "StyleSheet"], [1372, 36, 1049, 25], [1372, 37, 1049, 26, "create"], [1372, 43, 1049, 32], [1372, 44, 1049, 33], [1373, 4, 1050, 2, "container"], [1373, 13, 1050, 11], [1373, 15, 1050, 13], [1374, 6, 1051, 4, "flex"], [1374, 10, 1051, 8], [1374, 12, 1051, 10], [1374, 13, 1051, 11], [1375, 6, 1052, 4, "backgroundColor"], [1375, 21, 1052, 19], [1375, 23, 1052, 21], [1376, 4, 1053, 2], [1376, 5, 1053, 3], [1377, 4, 1054, 2, "cameraContainer"], [1377, 19, 1054, 17], [1377, 21, 1054, 19], [1378, 6, 1055, 4, "flex"], [1378, 10, 1055, 8], [1378, 12, 1055, 10], [1378, 13, 1055, 11], [1379, 6, 1056, 4, "max<PERSON><PERSON><PERSON>"], [1379, 14, 1056, 12], [1379, 16, 1056, 14], [1379, 19, 1056, 17], [1380, 6, 1057, 4, "alignSelf"], [1380, 15, 1057, 13], [1380, 17, 1057, 15], [1380, 25, 1057, 23], [1381, 6, 1058, 4, "width"], [1381, 11, 1058, 9], [1381, 13, 1058, 11], [1382, 4, 1059, 2], [1382, 5, 1059, 3], [1383, 4, 1060, 2, "camera"], [1383, 10, 1060, 8], [1383, 12, 1060, 10], [1384, 6, 1061, 4, "flex"], [1384, 10, 1061, 8], [1384, 12, 1061, 10], [1385, 4, 1062, 2], [1385, 5, 1062, 3], [1386, 4, 1063, 2, "headerOverlay"], [1386, 17, 1063, 15], [1386, 19, 1063, 17], [1387, 6, 1064, 4, "position"], [1387, 14, 1064, 12], [1387, 16, 1064, 14], [1387, 26, 1064, 24], [1388, 6, 1065, 4, "top"], [1388, 9, 1065, 7], [1388, 11, 1065, 9], [1388, 12, 1065, 10], [1389, 6, 1066, 4, "left"], [1389, 10, 1066, 8], [1389, 12, 1066, 10], [1389, 13, 1066, 11], [1390, 6, 1067, 4, "right"], [1390, 11, 1067, 9], [1390, 13, 1067, 11], [1390, 14, 1067, 12], [1391, 6, 1068, 4, "backgroundColor"], [1391, 21, 1068, 19], [1391, 23, 1068, 21], [1391, 36, 1068, 34], [1392, 6, 1069, 4, "paddingTop"], [1392, 16, 1069, 14], [1392, 18, 1069, 16], [1392, 20, 1069, 18], [1393, 6, 1070, 4, "paddingHorizontal"], [1393, 23, 1070, 21], [1393, 25, 1070, 23], [1393, 27, 1070, 25], [1394, 6, 1071, 4, "paddingBottom"], [1394, 19, 1071, 17], [1394, 21, 1071, 19], [1395, 4, 1072, 2], [1395, 5, 1072, 3], [1396, 4, 1073, 2, "headerContent"], [1396, 17, 1073, 15], [1396, 19, 1073, 17], [1397, 6, 1074, 4, "flexDirection"], [1397, 19, 1074, 17], [1397, 21, 1074, 19], [1397, 26, 1074, 24], [1398, 6, 1075, 4, "justifyContent"], [1398, 20, 1075, 18], [1398, 22, 1075, 20], [1398, 37, 1075, 35], [1399, 6, 1076, 4, "alignItems"], [1399, 16, 1076, 14], [1399, 18, 1076, 16], [1400, 4, 1077, 2], [1400, 5, 1077, 3], [1401, 4, 1078, 2, "headerLeft"], [1401, 14, 1078, 12], [1401, 16, 1078, 14], [1402, 6, 1079, 4, "flex"], [1402, 10, 1079, 8], [1402, 12, 1079, 10], [1403, 4, 1080, 2], [1403, 5, 1080, 3], [1404, 4, 1081, 2, "headerTitle"], [1404, 15, 1081, 13], [1404, 17, 1081, 15], [1405, 6, 1082, 4, "fontSize"], [1405, 14, 1082, 12], [1405, 16, 1082, 14], [1405, 18, 1082, 16], [1406, 6, 1083, 4, "fontWeight"], [1406, 16, 1083, 14], [1406, 18, 1083, 16], [1406, 23, 1083, 21], [1407, 6, 1084, 4, "color"], [1407, 11, 1084, 9], [1407, 13, 1084, 11], [1407, 19, 1084, 17], [1408, 6, 1085, 4, "marginBottom"], [1408, 18, 1085, 16], [1408, 20, 1085, 18], [1409, 4, 1086, 2], [1409, 5, 1086, 3], [1410, 4, 1087, 2, "subtitleRow"], [1410, 15, 1087, 13], [1410, 17, 1087, 15], [1411, 6, 1088, 4, "flexDirection"], [1411, 19, 1088, 17], [1411, 21, 1088, 19], [1411, 26, 1088, 24], [1412, 6, 1089, 4, "alignItems"], [1412, 16, 1089, 14], [1412, 18, 1089, 16], [1412, 26, 1089, 24], [1413, 6, 1090, 4, "marginBottom"], [1413, 18, 1090, 16], [1413, 20, 1090, 18], [1414, 4, 1091, 2], [1414, 5, 1091, 3], [1415, 4, 1092, 2, "webIcon"], [1415, 11, 1092, 9], [1415, 13, 1092, 11], [1416, 6, 1093, 4, "fontSize"], [1416, 14, 1093, 12], [1416, 16, 1093, 14], [1416, 18, 1093, 16], [1417, 6, 1094, 4, "marginRight"], [1417, 17, 1094, 15], [1417, 19, 1094, 17], [1418, 4, 1095, 2], [1418, 5, 1095, 3], [1419, 4, 1096, 2, "headerSubtitle"], [1419, 18, 1096, 16], [1419, 20, 1096, 18], [1420, 6, 1097, 4, "fontSize"], [1420, 14, 1097, 12], [1420, 16, 1097, 14], [1420, 18, 1097, 16], [1421, 6, 1098, 4, "color"], [1421, 11, 1098, 9], [1421, 13, 1098, 11], [1421, 19, 1098, 17], [1422, 6, 1099, 4, "opacity"], [1422, 13, 1099, 11], [1422, 15, 1099, 13], [1423, 4, 1100, 2], [1423, 5, 1100, 3], [1424, 4, 1101, 2, "challengeRow"], [1424, 16, 1101, 14], [1424, 18, 1101, 16], [1425, 6, 1102, 4, "flexDirection"], [1425, 19, 1102, 17], [1425, 21, 1102, 19], [1425, 26, 1102, 24], [1426, 6, 1103, 4, "alignItems"], [1426, 16, 1103, 14], [1426, 18, 1103, 16], [1427, 4, 1104, 2], [1427, 5, 1104, 3], [1428, 4, 1105, 2, "challengeCode"], [1428, 17, 1105, 15], [1428, 19, 1105, 17], [1429, 6, 1106, 4, "fontSize"], [1429, 14, 1106, 12], [1429, 16, 1106, 14], [1429, 18, 1106, 16], [1430, 6, 1107, 4, "color"], [1430, 11, 1107, 9], [1430, 13, 1107, 11], [1430, 19, 1107, 17], [1431, 6, 1108, 4, "marginLeft"], [1431, 16, 1108, 14], [1431, 18, 1108, 16], [1431, 19, 1108, 17], [1432, 6, 1109, 4, "fontFamily"], [1432, 16, 1109, 14], [1432, 18, 1109, 16], [1433, 4, 1110, 2], [1433, 5, 1110, 3], [1434, 4, 1111, 2, "closeButton"], [1434, 15, 1111, 13], [1434, 17, 1111, 15], [1435, 6, 1112, 4, "padding"], [1435, 13, 1112, 11], [1435, 15, 1112, 13], [1436, 4, 1113, 2], [1436, 5, 1113, 3], [1437, 4, 1114, 2, "privacyNotice"], [1437, 17, 1114, 15], [1437, 19, 1114, 17], [1438, 6, 1115, 4, "position"], [1438, 14, 1115, 12], [1438, 16, 1115, 14], [1438, 26, 1115, 24], [1439, 6, 1116, 4, "top"], [1439, 9, 1116, 7], [1439, 11, 1116, 9], [1439, 14, 1116, 12], [1440, 6, 1117, 4, "left"], [1440, 10, 1117, 8], [1440, 12, 1117, 10], [1440, 14, 1117, 12], [1441, 6, 1118, 4, "right"], [1441, 11, 1118, 9], [1441, 13, 1118, 11], [1441, 15, 1118, 13], [1442, 6, 1119, 4, "backgroundColor"], [1442, 21, 1119, 19], [1442, 23, 1119, 21], [1442, 48, 1119, 46], [1443, 6, 1120, 4, "borderRadius"], [1443, 18, 1120, 16], [1443, 20, 1120, 18], [1443, 21, 1120, 19], [1444, 6, 1121, 4, "padding"], [1444, 13, 1121, 11], [1444, 15, 1121, 13], [1444, 17, 1121, 15], [1445, 6, 1122, 4, "flexDirection"], [1445, 19, 1122, 17], [1445, 21, 1122, 19], [1445, 26, 1122, 24], [1446, 6, 1123, 4, "alignItems"], [1446, 16, 1123, 14], [1446, 18, 1123, 16], [1447, 4, 1124, 2], [1447, 5, 1124, 3], [1448, 4, 1125, 2, "privacyText"], [1448, 15, 1125, 13], [1448, 17, 1125, 15], [1449, 6, 1126, 4, "color"], [1449, 11, 1126, 9], [1449, 13, 1126, 11], [1449, 19, 1126, 17], [1450, 6, 1127, 4, "fontSize"], [1450, 14, 1127, 12], [1450, 16, 1127, 14], [1450, 18, 1127, 16], [1451, 6, 1128, 4, "marginLeft"], [1451, 16, 1128, 14], [1451, 18, 1128, 16], [1451, 19, 1128, 17], [1452, 6, 1129, 4, "flex"], [1452, 10, 1129, 8], [1452, 12, 1129, 10], [1453, 4, 1130, 2], [1453, 5, 1130, 3], [1454, 4, 1131, 2, "footer<PERSON><PERSON><PERSON>"], [1454, 17, 1131, 15], [1454, 19, 1131, 17], [1455, 6, 1132, 4, "position"], [1455, 14, 1132, 12], [1455, 16, 1132, 14], [1455, 26, 1132, 24], [1456, 6, 1133, 4, "bottom"], [1456, 12, 1133, 10], [1456, 14, 1133, 12], [1456, 15, 1133, 13], [1457, 6, 1134, 4, "left"], [1457, 10, 1134, 8], [1457, 12, 1134, 10], [1457, 13, 1134, 11], [1458, 6, 1135, 4, "right"], [1458, 11, 1135, 9], [1458, 13, 1135, 11], [1458, 14, 1135, 12], [1459, 6, 1136, 4, "backgroundColor"], [1459, 21, 1136, 19], [1459, 23, 1136, 21], [1459, 36, 1136, 34], [1460, 6, 1137, 4, "paddingBottom"], [1460, 19, 1137, 17], [1460, 21, 1137, 19], [1460, 23, 1137, 21], [1461, 6, 1138, 4, "paddingTop"], [1461, 16, 1138, 14], [1461, 18, 1138, 16], [1461, 20, 1138, 18], [1462, 6, 1139, 4, "alignItems"], [1462, 16, 1139, 14], [1462, 18, 1139, 16], [1463, 4, 1140, 2], [1463, 5, 1140, 3], [1464, 4, 1141, 2, "instruction"], [1464, 15, 1141, 13], [1464, 17, 1141, 15], [1465, 6, 1142, 4, "fontSize"], [1465, 14, 1142, 12], [1465, 16, 1142, 14], [1465, 18, 1142, 16], [1466, 6, 1143, 4, "color"], [1466, 11, 1143, 9], [1466, 13, 1143, 11], [1466, 19, 1143, 17], [1467, 6, 1144, 4, "marginBottom"], [1467, 18, 1144, 16], [1467, 20, 1144, 18], [1468, 4, 1145, 2], [1468, 5, 1145, 3], [1469, 4, 1146, 2, "shutterButton"], [1469, 17, 1146, 15], [1469, 19, 1146, 17], [1470, 6, 1147, 4, "width"], [1470, 11, 1147, 9], [1470, 13, 1147, 11], [1470, 15, 1147, 13], [1471, 6, 1148, 4, "height"], [1471, 12, 1148, 10], [1471, 14, 1148, 12], [1471, 16, 1148, 14], [1472, 6, 1149, 4, "borderRadius"], [1472, 18, 1149, 16], [1472, 20, 1149, 18], [1472, 22, 1149, 20], [1473, 6, 1150, 4, "backgroundColor"], [1473, 21, 1150, 19], [1473, 23, 1150, 21], [1473, 29, 1150, 27], [1474, 6, 1151, 4, "justifyContent"], [1474, 20, 1151, 18], [1474, 22, 1151, 20], [1474, 30, 1151, 28], [1475, 6, 1152, 4, "alignItems"], [1475, 16, 1152, 14], [1475, 18, 1152, 16], [1475, 26, 1152, 24], [1476, 6, 1153, 4, "marginBottom"], [1476, 18, 1153, 16], [1476, 20, 1153, 18], [1476, 22, 1153, 20], [1477, 6, 1154, 4], [1477, 9, 1154, 7, "Platform"], [1477, 26, 1154, 15], [1477, 27, 1154, 16, "select"], [1477, 33, 1154, 22], [1477, 34, 1154, 23], [1478, 8, 1155, 6, "ios"], [1478, 11, 1155, 9], [1478, 13, 1155, 11], [1479, 10, 1156, 8, "shadowColor"], [1479, 21, 1156, 19], [1479, 23, 1156, 21], [1479, 32, 1156, 30], [1480, 10, 1157, 8, "shadowOffset"], [1480, 22, 1157, 20], [1480, 24, 1157, 22], [1481, 12, 1157, 24, "width"], [1481, 17, 1157, 29], [1481, 19, 1157, 31], [1481, 20, 1157, 32], [1482, 12, 1157, 34, "height"], [1482, 18, 1157, 40], [1482, 20, 1157, 42], [1483, 10, 1157, 44], [1483, 11, 1157, 45], [1484, 10, 1158, 8, "shadowOpacity"], [1484, 23, 1158, 21], [1484, 25, 1158, 23], [1484, 28, 1158, 26], [1485, 10, 1159, 8, "shadowRadius"], [1485, 22, 1159, 20], [1485, 24, 1159, 22], [1486, 8, 1160, 6], [1486, 9, 1160, 7], [1487, 8, 1161, 6, "android"], [1487, 15, 1161, 13], [1487, 17, 1161, 15], [1488, 10, 1162, 8, "elevation"], [1488, 19, 1162, 17], [1488, 21, 1162, 19], [1489, 8, 1163, 6], [1489, 9, 1163, 7], [1490, 8, 1164, 6, "web"], [1490, 11, 1164, 9], [1490, 13, 1164, 11], [1491, 10, 1165, 8, "boxShadow"], [1491, 19, 1165, 17], [1491, 21, 1165, 19], [1492, 8, 1166, 6], [1493, 6, 1167, 4], [1493, 7, 1167, 5], [1494, 4, 1168, 2], [1494, 5, 1168, 3], [1495, 4, 1169, 2, "shutterButtonDisabled"], [1495, 25, 1169, 23], [1495, 27, 1169, 25], [1496, 6, 1170, 4, "opacity"], [1496, 13, 1170, 11], [1496, 15, 1170, 13], [1497, 4, 1171, 2], [1497, 5, 1171, 3], [1498, 4, 1172, 2, "shutterInner"], [1498, 16, 1172, 14], [1498, 18, 1172, 16], [1499, 6, 1173, 4, "width"], [1499, 11, 1173, 9], [1499, 13, 1173, 11], [1499, 15, 1173, 13], [1500, 6, 1174, 4, "height"], [1500, 12, 1174, 10], [1500, 14, 1174, 12], [1500, 16, 1174, 14], [1501, 6, 1175, 4, "borderRadius"], [1501, 18, 1175, 16], [1501, 20, 1175, 18], [1501, 22, 1175, 20], [1502, 6, 1176, 4, "backgroundColor"], [1502, 21, 1176, 19], [1502, 23, 1176, 21], [1502, 29, 1176, 27], [1503, 6, 1177, 4, "borderWidth"], [1503, 17, 1177, 15], [1503, 19, 1177, 17], [1503, 20, 1177, 18], [1504, 6, 1178, 4, "borderColor"], [1504, 17, 1178, 15], [1504, 19, 1178, 17], [1505, 4, 1179, 2], [1505, 5, 1179, 3], [1506, 4, 1180, 2, "privacyNote"], [1506, 15, 1180, 13], [1506, 17, 1180, 15], [1507, 6, 1181, 4, "fontSize"], [1507, 14, 1181, 12], [1507, 16, 1181, 14], [1507, 18, 1181, 16], [1508, 6, 1182, 4, "color"], [1508, 11, 1182, 9], [1508, 13, 1182, 11], [1509, 4, 1183, 2], [1509, 5, 1183, 3], [1510, 4, 1184, 2, "processingModal"], [1510, 19, 1184, 17], [1510, 21, 1184, 19], [1511, 6, 1185, 4, "flex"], [1511, 10, 1185, 8], [1511, 12, 1185, 10], [1511, 13, 1185, 11], [1512, 6, 1186, 4, "backgroundColor"], [1512, 21, 1186, 19], [1512, 23, 1186, 21], [1512, 43, 1186, 41], [1513, 6, 1187, 4, "justifyContent"], [1513, 20, 1187, 18], [1513, 22, 1187, 20], [1513, 30, 1187, 28], [1514, 6, 1188, 4, "alignItems"], [1514, 16, 1188, 14], [1514, 18, 1188, 16], [1515, 4, 1189, 2], [1515, 5, 1189, 3], [1516, 4, 1190, 2, "processingContent"], [1516, 21, 1190, 19], [1516, 23, 1190, 21], [1517, 6, 1191, 4, "backgroundColor"], [1517, 21, 1191, 19], [1517, 23, 1191, 21], [1517, 29, 1191, 27], [1518, 6, 1192, 4, "borderRadius"], [1518, 18, 1192, 16], [1518, 20, 1192, 18], [1518, 22, 1192, 20], [1519, 6, 1193, 4, "padding"], [1519, 13, 1193, 11], [1519, 15, 1193, 13], [1519, 17, 1193, 15], [1520, 6, 1194, 4, "width"], [1520, 11, 1194, 9], [1520, 13, 1194, 11], [1520, 18, 1194, 16], [1521, 6, 1195, 4, "max<PERSON><PERSON><PERSON>"], [1521, 14, 1195, 12], [1521, 16, 1195, 14], [1521, 19, 1195, 17], [1522, 6, 1196, 4, "alignItems"], [1522, 16, 1196, 14], [1522, 18, 1196, 16], [1523, 4, 1197, 2], [1523, 5, 1197, 3], [1524, 4, 1198, 2, "processingTitle"], [1524, 19, 1198, 17], [1524, 21, 1198, 19], [1525, 6, 1199, 4, "fontSize"], [1525, 14, 1199, 12], [1525, 16, 1199, 14], [1525, 18, 1199, 16], [1526, 6, 1200, 4, "fontWeight"], [1526, 16, 1200, 14], [1526, 18, 1200, 16], [1526, 23, 1200, 21], [1527, 6, 1201, 4, "color"], [1527, 11, 1201, 9], [1527, 13, 1201, 11], [1527, 22, 1201, 20], [1528, 6, 1202, 4, "marginTop"], [1528, 15, 1202, 13], [1528, 17, 1202, 15], [1528, 19, 1202, 17], [1529, 6, 1203, 4, "marginBottom"], [1529, 18, 1203, 16], [1529, 20, 1203, 18], [1530, 4, 1204, 2], [1530, 5, 1204, 3], [1531, 4, 1205, 2, "progressBar"], [1531, 15, 1205, 13], [1531, 17, 1205, 15], [1532, 6, 1206, 4, "width"], [1532, 11, 1206, 9], [1532, 13, 1206, 11], [1532, 19, 1206, 17], [1533, 6, 1207, 4, "height"], [1533, 12, 1207, 10], [1533, 14, 1207, 12], [1533, 15, 1207, 13], [1534, 6, 1208, 4, "backgroundColor"], [1534, 21, 1208, 19], [1534, 23, 1208, 21], [1534, 32, 1208, 30], [1535, 6, 1209, 4, "borderRadius"], [1535, 18, 1209, 16], [1535, 20, 1209, 18], [1535, 21, 1209, 19], [1536, 6, 1210, 4, "overflow"], [1536, 14, 1210, 12], [1536, 16, 1210, 14], [1536, 24, 1210, 22], [1537, 6, 1211, 4, "marginBottom"], [1537, 18, 1211, 16], [1537, 20, 1211, 18], [1538, 4, 1212, 2], [1538, 5, 1212, 3], [1539, 4, 1213, 2, "progressFill"], [1539, 16, 1213, 14], [1539, 18, 1213, 16], [1540, 6, 1214, 4, "height"], [1540, 12, 1214, 10], [1540, 14, 1214, 12], [1540, 20, 1214, 18], [1541, 6, 1215, 4, "backgroundColor"], [1541, 21, 1215, 19], [1541, 23, 1215, 21], [1541, 32, 1215, 30], [1542, 6, 1216, 4, "borderRadius"], [1542, 18, 1216, 16], [1542, 20, 1216, 18], [1543, 4, 1217, 2], [1543, 5, 1217, 3], [1544, 4, 1218, 2, "processingDescription"], [1544, 25, 1218, 23], [1544, 27, 1218, 25], [1545, 6, 1219, 4, "fontSize"], [1545, 14, 1219, 12], [1545, 16, 1219, 14], [1545, 18, 1219, 16], [1546, 6, 1220, 4, "color"], [1546, 11, 1220, 9], [1546, 13, 1220, 11], [1546, 22, 1220, 20], [1547, 6, 1221, 4, "textAlign"], [1547, 15, 1221, 13], [1547, 17, 1221, 15], [1548, 4, 1222, 2], [1548, 5, 1222, 3], [1549, 4, 1223, 2, "successIcon"], [1549, 15, 1223, 13], [1549, 17, 1223, 15], [1550, 6, 1224, 4, "marginTop"], [1550, 15, 1224, 13], [1550, 17, 1224, 15], [1551, 4, 1225, 2], [1551, 5, 1225, 3], [1552, 4, 1226, 2, "errorContent"], [1552, 16, 1226, 14], [1552, 18, 1226, 16], [1553, 6, 1227, 4, "backgroundColor"], [1553, 21, 1227, 19], [1553, 23, 1227, 21], [1553, 29, 1227, 27], [1554, 6, 1228, 4, "borderRadius"], [1554, 18, 1228, 16], [1554, 20, 1228, 18], [1554, 22, 1228, 20], [1555, 6, 1229, 4, "padding"], [1555, 13, 1229, 11], [1555, 15, 1229, 13], [1555, 17, 1229, 15], [1556, 6, 1230, 4, "width"], [1556, 11, 1230, 9], [1556, 13, 1230, 11], [1556, 18, 1230, 16], [1557, 6, 1231, 4, "max<PERSON><PERSON><PERSON>"], [1557, 14, 1231, 12], [1557, 16, 1231, 14], [1557, 19, 1231, 17], [1558, 6, 1232, 4, "alignItems"], [1558, 16, 1232, 14], [1558, 18, 1232, 16], [1559, 4, 1233, 2], [1559, 5, 1233, 3], [1560, 4, 1234, 2, "errorTitle"], [1560, 14, 1234, 12], [1560, 16, 1234, 14], [1561, 6, 1235, 4, "fontSize"], [1561, 14, 1235, 12], [1561, 16, 1235, 14], [1561, 18, 1235, 16], [1562, 6, 1236, 4, "fontWeight"], [1562, 16, 1236, 14], [1562, 18, 1236, 16], [1562, 23, 1236, 21], [1563, 6, 1237, 4, "color"], [1563, 11, 1237, 9], [1563, 13, 1237, 11], [1563, 22, 1237, 20], [1564, 6, 1238, 4, "marginTop"], [1564, 15, 1238, 13], [1564, 17, 1238, 15], [1564, 19, 1238, 17], [1565, 6, 1239, 4, "marginBottom"], [1565, 18, 1239, 16], [1565, 20, 1239, 18], [1566, 4, 1240, 2], [1566, 5, 1240, 3], [1567, 4, 1241, 2, "errorMessage"], [1567, 16, 1241, 14], [1567, 18, 1241, 16], [1568, 6, 1242, 4, "fontSize"], [1568, 14, 1242, 12], [1568, 16, 1242, 14], [1568, 18, 1242, 16], [1569, 6, 1243, 4, "color"], [1569, 11, 1243, 9], [1569, 13, 1243, 11], [1569, 22, 1243, 20], [1570, 6, 1244, 4, "textAlign"], [1570, 15, 1244, 13], [1570, 17, 1244, 15], [1570, 25, 1244, 23], [1571, 6, 1245, 4, "marginBottom"], [1571, 18, 1245, 16], [1571, 20, 1245, 18], [1572, 4, 1246, 2], [1572, 5, 1246, 3], [1573, 4, 1247, 2, "primaryButton"], [1573, 17, 1247, 15], [1573, 19, 1247, 17], [1574, 6, 1248, 4, "backgroundColor"], [1574, 21, 1248, 19], [1574, 23, 1248, 21], [1574, 32, 1248, 30], [1575, 6, 1249, 4, "paddingHorizontal"], [1575, 23, 1249, 21], [1575, 25, 1249, 23], [1575, 27, 1249, 25], [1576, 6, 1250, 4, "paddingVertical"], [1576, 21, 1250, 19], [1576, 23, 1250, 21], [1576, 25, 1250, 23], [1577, 6, 1251, 4, "borderRadius"], [1577, 18, 1251, 16], [1577, 20, 1251, 18], [1577, 21, 1251, 19], [1578, 6, 1252, 4, "marginTop"], [1578, 15, 1252, 13], [1578, 17, 1252, 15], [1579, 4, 1253, 2], [1579, 5, 1253, 3], [1580, 4, 1254, 2, "primaryButtonText"], [1580, 21, 1254, 19], [1580, 23, 1254, 21], [1581, 6, 1255, 4, "color"], [1581, 11, 1255, 9], [1581, 13, 1255, 11], [1581, 19, 1255, 17], [1582, 6, 1256, 4, "fontSize"], [1582, 14, 1256, 12], [1582, 16, 1256, 14], [1582, 18, 1256, 16], [1583, 6, 1257, 4, "fontWeight"], [1583, 16, 1257, 14], [1583, 18, 1257, 16], [1584, 4, 1258, 2], [1584, 5, 1258, 3], [1585, 4, 1259, 2, "secondaryButton"], [1585, 19, 1259, 17], [1585, 21, 1259, 19], [1586, 6, 1260, 4, "paddingHorizontal"], [1586, 23, 1260, 21], [1586, 25, 1260, 23], [1586, 27, 1260, 25], [1587, 6, 1261, 4, "paddingVertical"], [1587, 21, 1261, 19], [1587, 23, 1261, 21], [1587, 25, 1261, 23], [1588, 6, 1262, 4, "marginTop"], [1588, 15, 1262, 13], [1588, 17, 1262, 15], [1589, 4, 1263, 2], [1589, 5, 1263, 3], [1590, 4, 1264, 2, "secondaryButtonText"], [1590, 23, 1264, 21], [1590, 25, 1264, 23], [1591, 6, 1265, 4, "color"], [1591, 11, 1265, 9], [1591, 13, 1265, 11], [1591, 22, 1265, 20], [1592, 6, 1266, 4, "fontSize"], [1592, 14, 1266, 12], [1592, 16, 1266, 14], [1593, 4, 1267, 2], [1593, 5, 1267, 3], [1594, 4, 1268, 2, "permissionContent"], [1594, 21, 1268, 19], [1594, 23, 1268, 21], [1595, 6, 1269, 4, "flex"], [1595, 10, 1269, 8], [1595, 12, 1269, 10], [1595, 13, 1269, 11], [1596, 6, 1270, 4, "justifyContent"], [1596, 20, 1270, 18], [1596, 22, 1270, 20], [1596, 30, 1270, 28], [1597, 6, 1271, 4, "alignItems"], [1597, 16, 1271, 14], [1597, 18, 1271, 16], [1597, 26, 1271, 24], [1598, 6, 1272, 4, "padding"], [1598, 13, 1272, 11], [1598, 15, 1272, 13], [1599, 4, 1273, 2], [1599, 5, 1273, 3], [1600, 4, 1274, 2, "permissionTitle"], [1600, 19, 1274, 17], [1600, 21, 1274, 19], [1601, 6, 1275, 4, "fontSize"], [1601, 14, 1275, 12], [1601, 16, 1275, 14], [1601, 18, 1275, 16], [1602, 6, 1276, 4, "fontWeight"], [1602, 16, 1276, 14], [1602, 18, 1276, 16], [1602, 23, 1276, 21], [1603, 6, 1277, 4, "color"], [1603, 11, 1277, 9], [1603, 13, 1277, 11], [1603, 22, 1277, 20], [1604, 6, 1278, 4, "marginTop"], [1604, 15, 1278, 13], [1604, 17, 1278, 15], [1604, 19, 1278, 17], [1605, 6, 1279, 4, "marginBottom"], [1605, 18, 1279, 16], [1605, 20, 1279, 18], [1606, 4, 1280, 2], [1606, 5, 1280, 3], [1607, 4, 1281, 2, "permissionDescription"], [1607, 25, 1281, 23], [1607, 27, 1281, 25], [1608, 6, 1282, 4, "fontSize"], [1608, 14, 1282, 12], [1608, 16, 1282, 14], [1608, 18, 1282, 16], [1609, 6, 1283, 4, "color"], [1609, 11, 1283, 9], [1609, 13, 1283, 11], [1609, 22, 1283, 20], [1610, 6, 1284, 4, "textAlign"], [1610, 15, 1284, 13], [1610, 17, 1284, 15], [1610, 25, 1284, 23], [1611, 6, 1285, 4, "marginBottom"], [1611, 18, 1285, 16], [1611, 20, 1285, 18], [1612, 4, 1286, 2], [1612, 5, 1286, 3], [1613, 4, 1287, 2, "loadingText"], [1613, 15, 1287, 13], [1613, 17, 1287, 15], [1614, 6, 1288, 4, "color"], [1614, 11, 1288, 9], [1614, 13, 1288, 11], [1614, 22, 1288, 20], [1615, 6, 1289, 4, "marginTop"], [1615, 15, 1289, 13], [1615, 17, 1289, 15], [1616, 4, 1290, 2], [1616, 5, 1290, 3], [1617, 4, 1291, 2], [1618, 4, 1292, 2, "blurZone"], [1618, 12, 1292, 10], [1618, 14, 1292, 12], [1619, 6, 1293, 4, "position"], [1619, 14, 1293, 12], [1619, 16, 1293, 14], [1619, 26, 1293, 24], [1620, 6, 1294, 4, "overflow"], [1620, 14, 1294, 12], [1620, 16, 1294, 14], [1621, 4, 1295, 2], [1621, 5, 1295, 3], [1622, 4, 1296, 2, "previewChip"], [1622, 15, 1296, 13], [1622, 17, 1296, 15], [1623, 6, 1297, 4, "position"], [1623, 14, 1297, 12], [1623, 16, 1297, 14], [1623, 26, 1297, 24], [1624, 6, 1298, 4, "top"], [1624, 9, 1298, 7], [1624, 11, 1298, 9], [1624, 12, 1298, 10], [1625, 6, 1299, 4, "right"], [1625, 11, 1299, 9], [1625, 13, 1299, 11], [1625, 14, 1299, 12], [1626, 6, 1300, 4, "backgroundColor"], [1626, 21, 1300, 19], [1626, 23, 1300, 21], [1626, 40, 1300, 38], [1627, 6, 1301, 4, "paddingHorizontal"], [1627, 23, 1301, 21], [1627, 25, 1301, 23], [1627, 27, 1301, 25], [1628, 6, 1302, 4, "paddingVertical"], [1628, 21, 1302, 19], [1628, 23, 1302, 21], [1628, 24, 1302, 22], [1629, 6, 1303, 4, "borderRadius"], [1629, 18, 1303, 16], [1629, 20, 1303, 18], [1630, 4, 1304, 2], [1630, 5, 1304, 3], [1631, 4, 1305, 2, "previewChipText"], [1631, 19, 1305, 17], [1631, 21, 1305, 19], [1632, 6, 1306, 4, "color"], [1632, 11, 1306, 9], [1632, 13, 1306, 11], [1632, 19, 1306, 17], [1633, 6, 1307, 4, "fontSize"], [1633, 14, 1307, 12], [1633, 16, 1307, 14], [1633, 18, 1307, 16], [1634, 6, 1308, 4, "fontWeight"], [1634, 16, 1308, 14], [1634, 18, 1308, 16], [1635, 4, 1309, 2], [1636, 2, 1310, 0], [1636, 3, 1310, 1], [1636, 4, 1310, 2], [1637, 2, 1310, 3], [1637, 6, 1310, 3, "_c"], [1637, 8, 1310, 3], [1638, 2, 1310, 3, "$RefreshReg$"], [1638, 14, 1310, 3], [1638, 15, 1310, 3, "_c"], [1638, 17, 1310, 3], [1639, 0, 1310, 3], [1639, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdmE;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}