{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 993,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1010,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1014,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1027,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1032,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1064,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1104,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1105,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1100,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1095,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 915,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1713, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 72, 383, 70], [357, 73, 383, 71], [358, 8, 384, 6], [359, 6, 385, 4], [361, 6, 387, 4], [362, 6, 388, 4], [362, 12, 388, 10, "imageData"], [362, 21, 388, 19], [362, 24, 388, 22, "ctx"], [362, 27, 388, 25], [362, 28, 388, 26, "getImageData"], [362, 40, 388, 38], [362, 41, 388, 39, "clampedX"], [362, 49, 388, 47], [362, 51, 388, 49, "clampedY"], [362, 59, 388, 57], [362, 61, 388, 59, "<PERSON><PERSON><PERSON><PERSON>"], [362, 73, 388, 71], [362, 75, 388, 73, "clampedHeight"], [362, 88, 388, 86], [362, 89, 388, 87], [363, 6, 389, 4], [363, 12, 389, 10, "data"], [363, 16, 389, 14], [363, 19, 389, 17, "imageData"], [363, 28, 389, 26], [363, 29, 389, 27, "data"], [363, 33, 389, 31], [365, 6, 391, 4], [366, 6, 392, 4], [366, 12, 392, 10, "pixelSize"], [366, 21, 392, 19], [366, 24, 392, 22, "Math"], [366, 28, 392, 26], [366, 29, 392, 27, "max"], [366, 32, 392, 30], [366, 33, 392, 31], [366, 35, 392, 33], [366, 37, 392, 35, "Math"], [366, 41, 392, 39], [366, 42, 392, 40, "min"], [366, 45, 392, 43], [366, 46, 392, 44, "<PERSON><PERSON><PERSON><PERSON>"], [366, 58, 392, 56], [366, 60, 392, 58, "clampedHeight"], [366, 73, 392, 71], [366, 74, 392, 72], [366, 77, 392, 75], [366, 78, 392, 76], [366, 79, 392, 77], [367, 6, 394, 4], [367, 11, 394, 9], [367, 15, 394, 13, "py"], [367, 17, 394, 15], [367, 20, 394, 18], [367, 21, 394, 19], [367, 23, 394, 21, "py"], [367, 25, 394, 23], [367, 28, 394, 26, "clampedHeight"], [367, 41, 394, 39], [367, 43, 394, 41, "py"], [367, 45, 394, 43], [367, 49, 394, 47, "pixelSize"], [367, 58, 394, 56], [367, 60, 394, 58], [368, 8, 395, 6], [368, 13, 395, 11], [368, 17, 395, 15, "px"], [368, 19, 395, 17], [368, 22, 395, 20], [368, 23, 395, 21], [368, 25, 395, 23, "px"], [368, 27, 395, 25], [368, 30, 395, 28, "<PERSON><PERSON><PERSON><PERSON>"], [368, 42, 395, 40], [368, 44, 395, 42, "px"], [368, 46, 395, 44], [368, 50, 395, 48, "pixelSize"], [368, 59, 395, 57], [368, 61, 395, 59], [369, 10, 396, 8], [370, 10, 397, 8], [370, 14, 397, 12, "r"], [370, 15, 397, 13], [370, 18, 397, 16], [370, 19, 397, 17], [371, 12, 397, 19, "g"], [371, 13, 397, 20], [371, 16, 397, 23], [371, 17, 397, 24], [372, 12, 397, 26, "b"], [372, 13, 397, 27], [372, 16, 397, 30], [372, 17, 397, 31], [373, 12, 397, 33, "count"], [373, 17, 397, 38], [373, 20, 397, 41], [373, 21, 397, 42], [374, 10, 399, 8], [374, 15, 399, 13], [374, 19, 399, 17, "dy"], [374, 21, 399, 19], [374, 24, 399, 22], [374, 25, 399, 23], [374, 27, 399, 25, "dy"], [374, 29, 399, 27], [374, 32, 399, 30, "pixelSize"], [374, 41, 399, 39], [374, 45, 399, 43, "py"], [374, 47, 399, 45], [374, 50, 399, 48, "dy"], [374, 52, 399, 50], [374, 55, 399, 53, "clampedHeight"], [374, 68, 399, 66], [374, 70, 399, 68, "dy"], [374, 72, 399, 70], [374, 74, 399, 72], [374, 76, 399, 74], [375, 12, 400, 10], [375, 17, 400, 15], [375, 21, 400, 19, "dx"], [375, 23, 400, 21], [375, 26, 400, 24], [375, 27, 400, 25], [375, 29, 400, 27, "dx"], [375, 31, 400, 29], [375, 34, 400, 32, "pixelSize"], [375, 43, 400, 41], [375, 47, 400, 45, "px"], [375, 49, 400, 47], [375, 52, 400, 50, "dx"], [375, 54, 400, 52], [375, 57, 400, 55, "<PERSON><PERSON><PERSON><PERSON>"], [375, 69, 400, 67], [375, 71, 400, 69, "dx"], [375, 73, 400, 71], [375, 75, 400, 73], [375, 77, 400, 75], [376, 14, 401, 12], [376, 20, 401, 18, "index"], [376, 25, 401, 23], [376, 28, 401, 26], [376, 29, 401, 27], [376, 30, 401, 28, "py"], [376, 32, 401, 30], [376, 35, 401, 33, "dy"], [376, 37, 401, 35], [376, 41, 401, 39, "<PERSON><PERSON><PERSON><PERSON>"], [376, 53, 401, 51], [376, 57, 401, 55, "px"], [376, 59, 401, 57], [376, 62, 401, 60, "dx"], [376, 64, 401, 62], [376, 65, 401, 63], [376, 69, 401, 67], [376, 70, 401, 68], [377, 14, 402, 12, "r"], [377, 15, 402, 13], [377, 19, 402, 17, "data"], [377, 23, 402, 21], [377, 24, 402, 22, "index"], [377, 29, 402, 27], [377, 30, 402, 28], [378, 14, 403, 12, "g"], [378, 15, 403, 13], [378, 19, 403, 17, "data"], [378, 23, 403, 21], [378, 24, 403, 22, "index"], [378, 29, 403, 27], [378, 32, 403, 30], [378, 33, 403, 31], [378, 34, 403, 32], [379, 14, 404, 12, "b"], [379, 15, 404, 13], [379, 19, 404, 17, "data"], [379, 23, 404, 21], [379, 24, 404, 22, "index"], [379, 29, 404, 27], [379, 32, 404, 30], [379, 33, 404, 31], [379, 34, 404, 32], [380, 14, 405, 12, "count"], [380, 19, 405, 17], [380, 21, 405, 19], [381, 12, 406, 10], [382, 10, 407, 8], [383, 10, 409, 8], [383, 14, 409, 12, "count"], [383, 19, 409, 17], [383, 22, 409, 20], [383, 23, 409, 21], [383, 25, 409, 23], [384, 12, 410, 10, "r"], [384, 13, 410, 11], [384, 16, 410, 14, "Math"], [384, 20, 410, 18], [384, 21, 410, 19, "floor"], [384, 26, 410, 24], [384, 27, 410, 25, "r"], [384, 28, 410, 26], [384, 31, 410, 29, "count"], [384, 36, 410, 34], [384, 37, 410, 35], [385, 12, 411, 10, "g"], [385, 13, 411, 11], [385, 16, 411, 14, "Math"], [385, 20, 411, 18], [385, 21, 411, 19, "floor"], [385, 26, 411, 24], [385, 27, 411, 25, "g"], [385, 28, 411, 26], [385, 31, 411, 29, "count"], [385, 36, 411, 34], [385, 37, 411, 35], [386, 12, 412, 10, "b"], [386, 13, 412, 11], [386, 16, 412, 14, "Math"], [386, 20, 412, 18], [386, 21, 412, 19, "floor"], [386, 26, 412, 24], [386, 27, 412, 25, "b"], [386, 28, 412, 26], [386, 31, 412, 29, "count"], [386, 36, 412, 34], [386, 37, 412, 35], [388, 12, 414, 10], [389, 12, 415, 10], [389, 17, 415, 15], [389, 21, 415, 19, "dy"], [389, 23, 415, 21], [389, 26, 415, 24], [389, 27, 415, 25], [389, 29, 415, 27, "dy"], [389, 31, 415, 29], [389, 34, 415, 32, "pixelSize"], [389, 43, 415, 41], [389, 47, 415, 45, "py"], [389, 49, 415, 47], [389, 52, 415, 50, "dy"], [389, 54, 415, 52], [389, 57, 415, 55, "clampedHeight"], [389, 70, 415, 68], [389, 72, 415, 70, "dy"], [389, 74, 415, 72], [389, 76, 415, 74], [389, 78, 415, 76], [390, 14, 416, 12], [390, 19, 416, 17], [390, 23, 416, 21, "dx"], [390, 25, 416, 23], [390, 28, 416, 26], [390, 29, 416, 27], [390, 31, 416, 29, "dx"], [390, 33, 416, 31], [390, 36, 416, 34, "pixelSize"], [390, 45, 416, 43], [390, 49, 416, 47, "px"], [390, 51, 416, 49], [390, 54, 416, 52, "dx"], [390, 56, 416, 54], [390, 59, 416, 57, "<PERSON><PERSON><PERSON><PERSON>"], [390, 71, 416, 69], [390, 73, 416, 71, "dx"], [390, 75, 416, 73], [390, 77, 416, 75], [390, 79, 416, 77], [391, 16, 417, 14], [391, 22, 417, 20, "index"], [391, 27, 417, 25], [391, 30, 417, 28], [391, 31, 417, 29], [391, 32, 417, 30, "py"], [391, 34, 417, 32], [391, 37, 417, 35, "dy"], [391, 39, 417, 37], [391, 43, 417, 41, "<PERSON><PERSON><PERSON><PERSON>"], [391, 55, 417, 53], [391, 59, 417, 57, "px"], [391, 61, 417, 59], [391, 64, 417, 62, "dx"], [391, 66, 417, 64], [391, 67, 417, 65], [391, 71, 417, 69], [391, 72, 417, 70], [392, 16, 418, 14, "data"], [392, 20, 418, 18], [392, 21, 418, 19, "index"], [392, 26, 418, 24], [392, 27, 418, 25], [392, 30, 418, 28, "r"], [392, 31, 418, 29], [393, 16, 419, 14, "data"], [393, 20, 419, 18], [393, 21, 419, 19, "index"], [393, 26, 419, 24], [393, 29, 419, 27], [393, 30, 419, 28], [393, 31, 419, 29], [393, 34, 419, 32, "g"], [393, 35, 419, 33], [394, 16, 420, 14, "data"], [394, 20, 420, 18], [394, 21, 420, 19, "index"], [394, 26, 420, 24], [394, 29, 420, 27], [394, 30, 420, 28], [394, 31, 420, 29], [394, 34, 420, 32, "b"], [394, 35, 420, 33], [395, 16, 421, 14], [396, 14, 422, 12], [397, 12, 423, 10], [398, 10, 424, 8], [399, 8, 425, 6], [400, 6, 426, 4], [402, 6, 428, 4], [403, 6, 429, 4], [403, 11, 429, 9], [403, 15, 429, 13, "i"], [403, 16, 429, 14], [403, 19, 429, 17], [403, 20, 429, 18], [403, 22, 429, 20, "i"], [403, 23, 429, 21], [403, 26, 429, 24], [403, 27, 429, 25], [403, 29, 429, 27, "i"], [403, 30, 429, 28], [403, 32, 429, 30], [403, 34, 429, 32], [404, 8, 430, 6, "applySimpleBlur"], [404, 23, 430, 21], [404, 24, 430, 22, "data"], [404, 28, 430, 26], [404, 30, 430, 28, "<PERSON><PERSON><PERSON><PERSON>"], [404, 42, 430, 40], [404, 44, 430, 42, "clampedHeight"], [404, 57, 430, 55], [404, 58, 430, 56], [405, 6, 431, 4], [407, 6, 433, 4], [408, 6, 434, 4, "ctx"], [408, 9, 434, 7], [408, 10, 434, 8, "putImageData"], [408, 22, 434, 20], [408, 23, 434, 21, "imageData"], [408, 32, 434, 30], [408, 34, 434, 32, "clampedX"], [408, 42, 434, 40], [408, 44, 434, 42, "clampedY"], [408, 52, 434, 50], [408, 53, 434, 51], [410, 6, 436, 4], [411, 6, 437, 4, "ctx"], [411, 9, 437, 7], [411, 10, 437, 8, "fillStyle"], [411, 19, 437, 17], [411, 22, 437, 20], [411, 48, 437, 46], [412, 6, 438, 4, "ctx"], [412, 9, 438, 7], [412, 10, 438, 8, "fillRect"], [412, 18, 438, 16], [412, 19, 438, 17, "clampedX"], [412, 27, 438, 25], [412, 29, 438, 27, "clampedY"], [412, 37, 438, 35], [412, 39, 438, 37, "<PERSON><PERSON><PERSON><PERSON>"], [412, 51, 438, 49], [412, 53, 438, 51, "clampedHeight"], [412, 66, 438, 64], [412, 67, 438, 65], [413, 4, 439, 2], [413, 5, 439, 3], [414, 4, 441, 2], [414, 10, 441, 8, "applySimpleBlur"], [414, 25, 441, 23], [414, 28, 441, 26, "applySimpleBlur"], [414, 29, 441, 27, "data"], [414, 33, 441, 50], [414, 35, 441, 52, "width"], [414, 40, 441, 65], [414, 42, 441, 67, "height"], [414, 48, 441, 81], [414, 53, 441, 86], [415, 6, 442, 4], [415, 12, 442, 10, "original"], [415, 20, 442, 18], [415, 23, 442, 21], [415, 27, 442, 25, "Uint8ClampedArray"], [415, 44, 442, 42], [415, 45, 442, 43, "data"], [415, 49, 442, 47], [415, 50, 442, 48], [416, 6, 444, 4], [416, 11, 444, 9], [416, 15, 444, 13, "y"], [416, 16, 444, 14], [416, 19, 444, 17], [416, 20, 444, 18], [416, 22, 444, 20, "y"], [416, 23, 444, 21], [416, 26, 444, 24, "height"], [416, 32, 444, 30], [416, 35, 444, 33], [416, 36, 444, 34], [416, 38, 444, 36, "y"], [416, 39, 444, 37], [416, 41, 444, 39], [416, 43, 444, 41], [417, 8, 445, 6], [417, 13, 445, 11], [417, 17, 445, 15, "x"], [417, 18, 445, 16], [417, 21, 445, 19], [417, 22, 445, 20], [417, 24, 445, 22, "x"], [417, 25, 445, 23], [417, 28, 445, 26, "width"], [417, 33, 445, 31], [417, 36, 445, 34], [417, 37, 445, 35], [417, 39, 445, 37, "x"], [417, 40, 445, 38], [417, 42, 445, 40], [417, 44, 445, 42], [418, 10, 446, 8], [418, 16, 446, 14, "index"], [418, 21, 446, 19], [418, 24, 446, 22], [418, 25, 446, 23, "y"], [418, 26, 446, 24], [418, 29, 446, 27, "width"], [418, 34, 446, 32], [418, 37, 446, 35, "x"], [418, 38, 446, 36], [418, 42, 446, 40], [418, 43, 446, 41], [420, 10, 448, 8], [421, 10, 449, 8], [421, 14, 449, 12, "r"], [421, 15, 449, 13], [421, 18, 449, 16], [421, 19, 449, 17], [422, 12, 449, 19, "g"], [422, 13, 449, 20], [422, 16, 449, 23], [422, 17, 449, 24], [423, 12, 449, 26, "b"], [423, 13, 449, 27], [423, 16, 449, 30], [423, 17, 449, 31], [424, 10, 450, 8], [424, 15, 450, 13], [424, 19, 450, 17, "dy"], [424, 21, 450, 19], [424, 24, 450, 22], [424, 25, 450, 23], [424, 26, 450, 24], [424, 28, 450, 26, "dy"], [424, 30, 450, 28], [424, 34, 450, 32], [424, 35, 450, 33], [424, 37, 450, 35, "dy"], [424, 39, 450, 37], [424, 41, 450, 39], [424, 43, 450, 41], [425, 12, 451, 10], [425, 17, 451, 15], [425, 21, 451, 19, "dx"], [425, 23, 451, 21], [425, 26, 451, 24], [425, 27, 451, 25], [425, 28, 451, 26], [425, 30, 451, 28, "dx"], [425, 32, 451, 30], [425, 36, 451, 34], [425, 37, 451, 35], [425, 39, 451, 37, "dx"], [425, 41, 451, 39], [425, 43, 451, 41], [425, 45, 451, 43], [426, 14, 452, 12], [426, 20, 452, 18, "neighborIndex"], [426, 33, 452, 31], [426, 36, 452, 34], [426, 37, 452, 35], [426, 38, 452, 36, "y"], [426, 39, 452, 37], [426, 42, 452, 40, "dy"], [426, 44, 452, 42], [426, 48, 452, 46, "width"], [426, 53, 452, 51], [426, 57, 452, 55, "x"], [426, 58, 452, 56], [426, 61, 452, 59, "dx"], [426, 63, 452, 61], [426, 64, 452, 62], [426, 68, 452, 66], [426, 69, 452, 67], [427, 14, 453, 12, "r"], [427, 15, 453, 13], [427, 19, 453, 17, "original"], [427, 27, 453, 25], [427, 28, 453, 26, "neighborIndex"], [427, 41, 453, 39], [427, 42, 453, 40], [428, 14, 454, 12, "g"], [428, 15, 454, 13], [428, 19, 454, 17, "original"], [428, 27, 454, 25], [428, 28, 454, 26, "neighborIndex"], [428, 41, 454, 39], [428, 44, 454, 42], [428, 45, 454, 43], [428, 46, 454, 44], [429, 14, 455, 12, "b"], [429, 15, 455, 13], [429, 19, 455, 17, "original"], [429, 27, 455, 25], [429, 28, 455, 26, "neighborIndex"], [429, 41, 455, 39], [429, 44, 455, 42], [429, 45, 455, 43], [429, 46, 455, 44], [430, 12, 456, 10], [431, 10, 457, 8], [432, 10, 459, 8, "data"], [432, 14, 459, 12], [432, 15, 459, 13, "index"], [432, 20, 459, 18], [432, 21, 459, 19], [432, 24, 459, 22, "r"], [432, 25, 459, 23], [432, 28, 459, 26], [432, 29, 459, 27], [433, 10, 460, 8, "data"], [433, 14, 460, 12], [433, 15, 460, 13, "index"], [433, 20, 460, 18], [433, 23, 460, 21], [433, 24, 460, 22], [433, 25, 460, 23], [433, 28, 460, 26, "g"], [433, 29, 460, 27], [433, 32, 460, 30], [433, 33, 460, 31], [434, 10, 461, 8, "data"], [434, 14, 461, 12], [434, 15, 461, 13, "index"], [434, 20, 461, 18], [434, 23, 461, 21], [434, 24, 461, 22], [434, 25, 461, 23], [434, 28, 461, 26, "b"], [434, 29, 461, 27], [434, 32, 461, 30], [434, 33, 461, 31], [435, 8, 462, 6], [436, 6, 463, 4], [437, 4, 464, 2], [437, 5, 464, 3], [438, 4, 466, 2], [438, 10, 466, 8, "applyFallbackFaceBlur"], [438, 31, 466, 29], [438, 34, 466, 32, "applyFallbackFaceBlur"], [438, 35, 466, 33, "ctx"], [438, 38, 466, 62], [438, 40, 466, 64, "imgWidth"], [438, 48, 466, 80], [438, 50, 466, 82, "imgHeight"], [438, 59, 466, 99], [438, 64, 466, 104], [439, 6, 467, 4, "console"], [439, 13, 467, 11], [439, 14, 467, 12, "log"], [439, 17, 467, 15], [439, 18, 467, 16], [439, 90, 467, 88], [439, 91, 467, 89], [441, 6, 469, 4], [442, 6, 470, 4], [442, 12, 470, 10, "areas"], [442, 17, 470, 15], [442, 20, 470, 18], [443, 6, 471, 6], [444, 6, 472, 6], [445, 8, 472, 8, "x"], [445, 9, 472, 9], [445, 11, 472, 11, "imgWidth"], [445, 19, 472, 19], [445, 22, 472, 22], [445, 26, 472, 26], [446, 8, 472, 28, "y"], [446, 9, 472, 29], [446, 11, 472, 31, "imgHeight"], [446, 20, 472, 40], [446, 23, 472, 43], [446, 27, 472, 47], [447, 8, 472, 49, "w"], [447, 9, 472, 50], [447, 11, 472, 52, "imgWidth"], [447, 19, 472, 60], [447, 22, 472, 63], [447, 25, 472, 66], [448, 8, 472, 68, "h"], [448, 9, 472, 69], [448, 11, 472, 71, "imgHeight"], [448, 20, 472, 80], [448, 23, 472, 83], [449, 6, 472, 87], [449, 7, 472, 88], [450, 6, 473, 6], [451, 6, 474, 6], [452, 8, 474, 8, "x"], [452, 9, 474, 9], [452, 11, 474, 11, "imgWidth"], [452, 19, 474, 19], [452, 22, 474, 22], [452, 25, 474, 25], [453, 8, 474, 27, "y"], [453, 9, 474, 28], [453, 11, 474, 30, "imgHeight"], [453, 20, 474, 39], [453, 23, 474, 42], [453, 26, 474, 45], [454, 8, 474, 47, "w"], [454, 9, 474, 48], [454, 11, 474, 50, "imgWidth"], [454, 19, 474, 58], [454, 22, 474, 61], [454, 26, 474, 65], [455, 8, 474, 67, "h"], [455, 9, 474, 68], [455, 11, 474, 70, "imgHeight"], [455, 20, 474, 79], [455, 23, 474, 82], [456, 6, 474, 86], [456, 7, 474, 87], [457, 6, 475, 6], [458, 6, 476, 6], [459, 8, 476, 8, "x"], [459, 9, 476, 9], [459, 11, 476, 11, "imgWidth"], [459, 19, 476, 19], [459, 22, 476, 22], [459, 26, 476, 26], [460, 8, 476, 28, "y"], [460, 9, 476, 29], [460, 11, 476, 31, "imgHeight"], [460, 20, 476, 40], [460, 23, 476, 43], [460, 26, 476, 46], [461, 8, 476, 48, "w"], [461, 9, 476, 49], [461, 11, 476, 51, "imgWidth"], [461, 19, 476, 59], [461, 22, 476, 62], [461, 26, 476, 66], [462, 8, 476, 68, "h"], [462, 9, 476, 69], [462, 11, 476, 71, "imgHeight"], [462, 20, 476, 80], [462, 23, 476, 83], [463, 6, 476, 87], [463, 7, 476, 88], [463, 8, 477, 5], [464, 6, 479, 4, "areas"], [464, 11, 479, 9], [464, 12, 479, 10, "for<PERSON>ach"], [464, 19, 479, 17], [464, 20, 479, 18], [464, 21, 479, 19, "area"], [464, 25, 479, 23], [464, 27, 479, 25, "index"], [464, 32, 479, 30], [464, 37, 479, 35], [465, 8, 480, 6, "console"], [465, 15, 480, 13], [465, 16, 480, 14, "log"], [465, 19, 480, 17], [465, 20, 480, 18], [465, 65, 480, 63, "index"], [465, 70, 480, 68], [465, 73, 480, 71], [465, 74, 480, 72], [465, 77, 480, 75], [465, 79, 480, 77, "area"], [465, 83, 480, 81], [465, 84, 480, 82], [466, 8, 481, 6, "applyStrongBlur"], [466, 23, 481, 21], [466, 24, 481, 22, "ctx"], [466, 27, 481, 25], [466, 29, 481, 27, "area"], [466, 33, 481, 31], [466, 34, 481, 32, "x"], [466, 35, 481, 33], [466, 37, 481, 35, "area"], [466, 41, 481, 39], [466, 42, 481, 40, "y"], [466, 43, 481, 41], [466, 45, 481, 43, "area"], [466, 49, 481, 47], [466, 50, 481, 48, "w"], [466, 51, 481, 49], [466, 53, 481, 51, "area"], [466, 57, 481, 55], [466, 58, 481, 56, "h"], [466, 59, 481, 57], [466, 60, 481, 58], [467, 6, 482, 4], [467, 7, 482, 5], [467, 8, 482, 6], [468, 4, 483, 2], [468, 5, 483, 3], [470, 4, 485, 2], [471, 4, 486, 2], [471, 10, 486, 8, "capturePhoto"], [471, 22, 486, 20], [471, 25, 486, 23], [471, 29, 486, 23, "useCallback"], [471, 47, 486, 34], [471, 49, 486, 35], [471, 61, 486, 47], [472, 6, 487, 4], [473, 6, 488, 4], [473, 12, 488, 10, "isDev"], [473, 17, 488, 15], [473, 20, 488, 18, "process"], [473, 27, 488, 25], [473, 28, 488, 26, "env"], [473, 31, 488, 29], [473, 32, 488, 30, "NODE_ENV"], [473, 40, 488, 38], [473, 45, 488, 43], [473, 58, 488, 56], [473, 62, 488, 60, "__DEV__"], [473, 69, 488, 67], [474, 6, 490, 4], [474, 10, 490, 8], [474, 11, 490, 9, "cameraRef"], [474, 20, 490, 18], [474, 21, 490, 19, "current"], [474, 28, 490, 26], [474, 32, 490, 30], [474, 33, 490, 31, "isDev"], [474, 38, 490, 36], [474, 40, 490, 38], [475, 8, 491, 6, "<PERSON><PERSON>"], [475, 22, 491, 11], [475, 23, 491, 12, "alert"], [475, 28, 491, 17], [475, 29, 491, 18], [475, 36, 491, 25], [475, 38, 491, 27], [475, 56, 491, 45], [475, 57, 491, 46], [476, 8, 492, 6], [477, 6, 493, 4], [478, 6, 494, 4], [478, 10, 494, 8], [479, 8, 495, 6, "setProcessingState"], [479, 26, 495, 24], [479, 27, 495, 25], [479, 38, 495, 36], [479, 39, 495, 37], [480, 8, 496, 6, "setProcessingProgress"], [480, 29, 496, 27], [480, 30, 496, 28], [480, 32, 496, 30], [480, 33, 496, 31], [481, 8, 497, 6], [482, 8, 498, 6], [483, 8, 499, 6], [484, 8, 500, 6], [484, 14, 500, 12], [484, 18, 500, 16, "Promise"], [484, 25, 500, 23], [484, 26, 500, 24, "resolve"], [484, 33, 500, 31], [484, 37, 500, 35, "setTimeout"], [484, 47, 500, 45], [484, 48, 500, 46, "resolve"], [484, 55, 500, 53], [484, 57, 500, 55], [484, 59, 500, 57], [484, 60, 500, 58], [484, 61, 500, 59], [485, 8, 501, 6], [486, 8, 502, 6], [486, 12, 502, 10, "photo"], [486, 17, 502, 15], [487, 8, 504, 6], [487, 12, 504, 10], [488, 10, 505, 8, "photo"], [488, 15, 505, 13], [488, 18, 505, 16], [488, 24, 505, 22, "cameraRef"], [488, 33, 505, 31], [488, 34, 505, 32, "current"], [488, 41, 505, 39], [488, 42, 505, 40, "takePictureAsync"], [488, 58, 505, 56], [488, 59, 505, 57], [489, 12, 506, 10, "quality"], [489, 19, 506, 17], [489, 21, 506, 19], [489, 24, 506, 22], [490, 12, 507, 10, "base64"], [490, 18, 507, 16], [490, 20, 507, 18], [490, 25, 507, 23], [491, 12, 508, 10, "skipProcessing"], [491, 26, 508, 24], [491, 28, 508, 26], [491, 32, 508, 30], [491, 33, 508, 32], [492, 10, 509, 8], [492, 11, 509, 9], [492, 12, 509, 10], [493, 8, 510, 6], [493, 9, 510, 7], [493, 10, 510, 8], [493, 17, 510, 15, "cameraError"], [493, 28, 510, 26], [493, 30, 510, 28], [494, 10, 511, 8, "console"], [494, 17, 511, 15], [494, 18, 511, 16, "log"], [494, 21, 511, 19], [494, 22, 511, 20], [494, 82, 511, 80], [494, 84, 511, 82, "cameraError"], [494, 95, 511, 93], [494, 96, 511, 94], [495, 10, 512, 8], [496, 10, 513, 8], [496, 14, 513, 12, "isDev"], [496, 19, 513, 17], [496, 21, 513, 19], [497, 12, 514, 10, "photo"], [497, 17, 514, 15], [497, 20, 514, 18], [498, 14, 515, 12, "uri"], [498, 17, 515, 15], [498, 19, 515, 17], [499, 12, 516, 10], [499, 13, 516, 11], [500, 10, 517, 8], [500, 11, 517, 9], [500, 17, 517, 15], [501, 12, 518, 10], [501, 18, 518, 16, "cameraError"], [501, 29, 518, 27], [502, 10, 519, 8], [503, 8, 520, 6], [504, 8, 521, 6], [504, 12, 521, 10], [504, 13, 521, 11, "photo"], [504, 18, 521, 16], [504, 20, 521, 18], [505, 10, 522, 8], [505, 16, 522, 14], [505, 20, 522, 18, "Error"], [505, 25, 522, 23], [505, 26, 522, 24], [505, 51, 522, 49], [505, 52, 522, 50], [506, 8, 523, 6], [507, 8, 524, 6, "console"], [507, 15, 524, 13], [507, 16, 524, 14, "log"], [507, 19, 524, 17], [507, 20, 524, 18], [507, 56, 524, 54], [507, 58, 524, 56, "photo"], [507, 63, 524, 61], [507, 64, 524, 62, "uri"], [507, 67, 524, 65], [507, 68, 524, 66], [508, 8, 525, 6, "setCapturedPhoto"], [508, 24, 525, 22], [508, 25, 525, 23, "photo"], [508, 30, 525, 28], [508, 31, 525, 29, "uri"], [508, 34, 525, 32], [508, 35, 525, 33], [509, 8, 526, 6, "setProcessingProgress"], [509, 29, 526, 27], [509, 30, 526, 28], [509, 32, 526, 30], [509, 33, 526, 31], [510, 8, 527, 6], [511, 8, 528, 6, "console"], [511, 15, 528, 13], [511, 16, 528, 14, "log"], [511, 19, 528, 17], [511, 20, 528, 18], [511, 73, 528, 71], [511, 74, 528, 72], [512, 8, 529, 6], [512, 14, 529, 12, "processImageWithFaceBlur"], [512, 38, 529, 36], [512, 39, 529, 37, "photo"], [512, 44, 529, 42], [512, 45, 529, 43, "uri"], [512, 48, 529, 46], [512, 49, 529, 47], [513, 8, 530, 6, "console"], [513, 15, 530, 13], [513, 16, 530, 14, "log"], [513, 19, 530, 17], [513, 20, 530, 18], [513, 71, 530, 69], [513, 72, 530, 70], [514, 6, 531, 4], [514, 7, 531, 5], [514, 8, 531, 6], [514, 15, 531, 13, "error"], [514, 20, 531, 18], [514, 22, 531, 20], [515, 8, 532, 6, "console"], [515, 15, 532, 13], [515, 16, 532, 14, "error"], [515, 21, 532, 19], [515, 22, 532, 20], [515, 54, 532, 52], [515, 56, 532, 54, "error"], [515, 61, 532, 59], [515, 62, 532, 60], [516, 8, 533, 6, "setErrorMessage"], [516, 23, 533, 21], [516, 24, 533, 22], [516, 68, 533, 66], [516, 69, 533, 67], [517, 8, 534, 6, "setProcessingState"], [517, 26, 534, 24], [517, 27, 534, 25], [517, 34, 534, 32], [517, 35, 534, 33], [518, 6, 535, 4], [519, 4, 536, 2], [519, 5, 536, 3], [519, 7, 536, 5], [519, 9, 536, 7], [519, 10, 536, 8], [520, 4, 537, 2], [521, 4, 538, 2], [521, 10, 538, 8, "processImageWithFaceBlur"], [521, 34, 538, 32], [521, 37, 538, 35], [521, 43, 538, 42, "photoUri"], [521, 51, 538, 58], [521, 55, 538, 63], [522, 6, 539, 4], [522, 10, 539, 8], [523, 8, 540, 6, "console"], [523, 15, 540, 13], [523, 16, 540, 14, "log"], [523, 19, 540, 17], [523, 20, 540, 18], [523, 84, 540, 82], [523, 85, 540, 83], [524, 8, 541, 6, "setProcessingState"], [524, 26, 541, 24], [524, 27, 541, 25], [524, 39, 541, 37], [524, 40, 541, 38], [525, 8, 542, 6, "setProcessingProgress"], [525, 29, 542, 27], [525, 30, 542, 28], [525, 32, 542, 30], [525, 33, 542, 31], [527, 8, 544, 6], [528, 8, 545, 6], [528, 14, 545, 12, "canvas"], [528, 20, 545, 18], [528, 23, 545, 21, "document"], [528, 31, 545, 29], [528, 32, 545, 30, "createElement"], [528, 45, 545, 43], [528, 46, 545, 44], [528, 54, 545, 52], [528, 55, 545, 53], [529, 8, 546, 6], [529, 14, 546, 12, "ctx"], [529, 17, 546, 15], [529, 20, 546, 18, "canvas"], [529, 26, 546, 24], [529, 27, 546, 25, "getContext"], [529, 37, 546, 35], [529, 38, 546, 36], [529, 42, 546, 40], [529, 43, 546, 41], [530, 8, 547, 6], [530, 12, 547, 10], [530, 13, 547, 11, "ctx"], [530, 16, 547, 14], [530, 18, 547, 16], [530, 24, 547, 22], [530, 28, 547, 26, "Error"], [530, 33, 547, 31], [530, 34, 547, 32], [530, 64, 547, 62], [530, 65, 547, 63], [532, 8, 549, 6], [533, 8, 550, 6], [533, 14, 550, 12, "img"], [533, 17, 550, 15], [533, 20, 550, 18], [533, 24, 550, 22, "Image"], [533, 29, 550, 27], [533, 30, 550, 28], [533, 31, 550, 29], [534, 8, 551, 6], [534, 14, 551, 12], [534, 18, 551, 16, "Promise"], [534, 25, 551, 23], [534, 26, 551, 24], [534, 27, 551, 25, "resolve"], [534, 34, 551, 32], [534, 36, 551, 34, "reject"], [534, 42, 551, 40], [534, 47, 551, 45], [535, 10, 552, 8, "img"], [535, 13, 552, 11], [535, 14, 552, 12, "onload"], [535, 20, 552, 18], [535, 23, 552, 21, "resolve"], [535, 30, 552, 28], [536, 10, 553, 8, "img"], [536, 13, 553, 11], [536, 14, 553, 12, "onerror"], [536, 21, 553, 19], [536, 24, 553, 22, "reject"], [536, 30, 553, 28], [537, 10, 554, 8, "img"], [537, 13, 554, 11], [537, 14, 554, 12, "src"], [537, 17, 554, 15], [537, 20, 554, 18, "photoUri"], [537, 28, 554, 26], [538, 8, 555, 6], [538, 9, 555, 7], [538, 10, 555, 8], [540, 8, 557, 6], [541, 8, 558, 6, "canvas"], [541, 14, 558, 12], [541, 15, 558, 13, "width"], [541, 20, 558, 18], [541, 23, 558, 21, "img"], [541, 26, 558, 24], [541, 27, 558, 25, "width"], [541, 32, 558, 30], [542, 8, 559, 6, "canvas"], [542, 14, 559, 12], [542, 15, 559, 13, "height"], [542, 21, 559, 19], [542, 24, 559, 22, "img"], [542, 27, 559, 25], [542, 28, 559, 26, "height"], [542, 34, 559, 32], [543, 8, 560, 6, "console"], [543, 15, 560, 13], [543, 16, 560, 14, "log"], [543, 19, 560, 17], [543, 20, 560, 18], [543, 54, 560, 52], [543, 56, 560, 54], [544, 10, 560, 56, "width"], [544, 15, 560, 61], [544, 17, 560, 63, "img"], [544, 20, 560, 66], [544, 21, 560, 67, "width"], [544, 26, 560, 72], [545, 10, 560, 74, "height"], [545, 16, 560, 80], [545, 18, 560, 82, "img"], [545, 21, 560, 85], [545, 22, 560, 86, "height"], [546, 8, 560, 93], [546, 9, 560, 94], [546, 10, 560, 95], [548, 8, 562, 6], [549, 8, 563, 6, "ctx"], [549, 11, 563, 9], [549, 12, 563, 10, "drawImage"], [549, 21, 563, 19], [549, 22, 563, 20, "img"], [549, 25, 563, 23], [549, 27, 563, 25], [549, 28, 563, 26], [549, 30, 563, 28], [549, 31, 563, 29], [549, 32, 563, 30], [550, 8, 564, 6, "console"], [550, 15, 564, 13], [550, 16, 564, 14, "log"], [550, 19, 564, 17], [550, 20, 564, 18], [550, 72, 564, 70], [550, 73, 564, 71], [551, 8, 566, 6, "setProcessingProgress"], [551, 29, 566, 27], [551, 30, 566, 28], [551, 32, 566, 30], [551, 33, 566, 31], [553, 8, 568, 6], [554, 8, 569, 6], [554, 12, 569, 10, "detectedFaces"], [554, 25, 569, 23], [554, 28, 569, 26], [554, 30, 569, 28], [555, 8, 571, 6, "console"], [555, 15, 571, 13], [555, 16, 571, 14, "log"], [555, 19, 571, 17], [555, 20, 571, 18], [555, 81, 571, 79], [555, 82, 571, 80], [557, 8, 573, 6], [558, 8, 574, 6], [558, 12, 574, 10], [559, 10, 575, 8, "console"], [559, 17, 575, 15], [559, 18, 575, 16, "log"], [559, 21, 575, 19], [559, 22, 575, 20], [559, 81, 575, 79], [559, 82, 575, 80], [560, 10, 576, 8], [560, 16, 576, 14, "loadTensorFlowFaceDetection"], [560, 43, 576, 41], [560, 44, 576, 42], [560, 45, 576, 43], [561, 10, 577, 8, "console"], [561, 17, 577, 15], [561, 18, 577, 16, "log"], [561, 21, 577, 19], [561, 22, 577, 20], [561, 90, 577, 88], [561, 91, 577, 89], [562, 10, 578, 8, "detectedFaces"], [562, 23, 578, 21], [562, 26, 578, 24], [562, 32, 578, 30, "detectFacesWithTensorFlow"], [562, 57, 578, 55], [562, 58, 578, 56, "img"], [562, 61, 578, 59], [562, 62, 578, 60], [563, 10, 579, 8, "console"], [563, 17, 579, 15], [563, 18, 579, 16, "log"], [563, 21, 579, 19], [563, 22, 579, 20], [563, 70, 579, 68, "detectedFaces"], [563, 83, 579, 81], [563, 84, 579, 82, "length"], [563, 90, 579, 88], [563, 98, 579, 96], [563, 99, 579, 97], [564, 8, 580, 6], [564, 9, 580, 7], [564, 10, 580, 8], [564, 17, 580, 15, "tensorFlowError"], [564, 32, 580, 30], [564, 34, 580, 32], [565, 10, 581, 8, "console"], [565, 17, 581, 15], [565, 18, 581, 16, "warn"], [565, 22, 581, 20], [565, 23, 581, 21], [565, 61, 581, 59], [565, 63, 581, 61, "tensorFlowError"], [565, 78, 581, 76], [565, 79, 581, 77], [566, 10, 582, 8, "console"], [566, 17, 582, 15], [566, 18, 582, 16, "warn"], [566, 22, 582, 20], [566, 23, 582, 21], [566, 68, 582, 66], [566, 70, 582, 68], [567, 12, 583, 10, "message"], [567, 19, 583, 17], [567, 21, 583, 19, "tensorFlowError"], [567, 36, 583, 34], [567, 37, 583, 35, "message"], [567, 44, 583, 42], [568, 12, 584, 10, "stack"], [568, 17, 584, 15], [568, 19, 584, 17, "tensorFlowError"], [568, 34, 584, 32], [568, 35, 584, 33, "stack"], [569, 10, 585, 8], [569, 11, 585, 9], [569, 12, 585, 10], [571, 10, 587, 8], [572, 10, 588, 8, "console"], [572, 17, 588, 15], [572, 18, 588, 16, "log"], [572, 21, 588, 19], [572, 22, 588, 20], [572, 86, 588, 84], [572, 87, 588, 85], [573, 10, 589, 8, "detectedFaces"], [573, 23, 589, 21], [573, 26, 589, 24, "detectFacesHeuristic"], [573, 46, 589, 44], [573, 47, 589, 45, "img"], [573, 50, 589, 48], [573, 52, 589, 50, "ctx"], [573, 55, 589, 53], [573, 56, 589, 54], [574, 10, 590, 8, "console"], [574, 17, 590, 15], [574, 18, 590, 16, "log"], [574, 21, 590, 19], [574, 22, 590, 20], [574, 70, 590, 68, "detectedFaces"], [574, 83, 590, 81], [574, 84, 590, 82, "length"], [574, 90, 590, 88], [574, 98, 590, 96], [574, 99, 590, 97], [575, 8, 591, 6], [577, 8, 593, 6], [578, 8, 594, 6], [578, 12, 594, 10, "detectedFaces"], [578, 25, 594, 23], [578, 26, 594, 24, "length"], [578, 32, 594, 30], [578, 37, 594, 35], [578, 38, 594, 36], [578, 40, 594, 38], [579, 10, 595, 8, "console"], [579, 17, 595, 15], [579, 18, 595, 16, "log"], [579, 21, 595, 19], [579, 22, 595, 20], [579, 89, 595, 87], [579, 90, 595, 88], [580, 10, 596, 8, "detectedFaces"], [580, 23, 596, 21], [580, 26, 596, 24, "detectFacesAggressive"], [580, 47, 596, 45], [580, 48, 596, 46, "img"], [580, 51, 596, 49], [580, 53, 596, 51, "ctx"], [580, 56, 596, 54], [580, 57, 596, 55], [581, 10, 597, 8, "console"], [581, 17, 597, 15], [581, 18, 597, 16, "log"], [581, 21, 597, 19], [581, 22, 597, 20], [581, 71, 597, 69, "detectedFaces"], [581, 84, 597, 82], [581, 85, 597, 83, "length"], [581, 91, 597, 89], [581, 99, 597, 97], [581, 100, 597, 98], [582, 8, 598, 6], [583, 8, 600, 6, "console"], [583, 15, 600, 13], [583, 16, 600, 14, "log"], [583, 19, 600, 17], [583, 20, 600, 18], [583, 72, 600, 70, "detectedFaces"], [583, 85, 600, 83], [583, 86, 600, 84, "length"], [583, 92, 600, 90], [583, 100, 600, 98], [583, 101, 600, 99], [584, 8, 601, 6], [584, 12, 601, 10, "detectedFaces"], [584, 25, 601, 23], [584, 26, 601, 24, "length"], [584, 32, 601, 30], [584, 35, 601, 33], [584, 36, 601, 34], [584, 38, 601, 36], [585, 10, 602, 8, "console"], [585, 17, 602, 15], [585, 18, 602, 16, "log"], [585, 21, 602, 19], [585, 22, 602, 20], [585, 66, 602, 64], [585, 68, 602, 66, "detectedFaces"], [585, 81, 602, 79], [585, 82, 602, 80, "map"], [585, 85, 602, 83], [585, 86, 602, 84], [585, 87, 602, 85, "face"], [585, 91, 602, 89], [585, 93, 602, 91, "i"], [585, 94, 602, 92], [585, 100, 602, 98], [586, 12, 603, 10, "faceNumber"], [586, 22, 603, 20], [586, 24, 603, 22, "i"], [586, 25, 603, 23], [586, 28, 603, 26], [586, 29, 603, 27], [587, 12, 604, 10, "centerX"], [587, 19, 604, 17], [587, 21, 604, 19, "face"], [587, 25, 604, 23], [587, 26, 604, 24, "boundingBox"], [587, 37, 604, 35], [587, 38, 604, 36, "xCenter"], [587, 45, 604, 43], [588, 12, 605, 10, "centerY"], [588, 19, 605, 17], [588, 21, 605, 19, "face"], [588, 25, 605, 23], [588, 26, 605, 24, "boundingBox"], [588, 37, 605, 35], [588, 38, 605, 36, "yCenter"], [588, 45, 605, 43], [589, 12, 606, 10, "width"], [589, 17, 606, 15], [589, 19, 606, 17, "face"], [589, 23, 606, 21], [589, 24, 606, 22, "boundingBox"], [589, 35, 606, 33], [589, 36, 606, 34, "width"], [589, 41, 606, 39], [590, 12, 607, 10, "height"], [590, 18, 607, 16], [590, 20, 607, 18, "face"], [590, 24, 607, 22], [590, 25, 607, 23, "boundingBox"], [590, 36, 607, 34], [590, 37, 607, 35, "height"], [591, 10, 608, 8], [591, 11, 608, 9], [591, 12, 608, 10], [591, 13, 608, 11], [591, 14, 608, 12], [592, 8, 609, 6], [592, 9, 609, 7], [592, 15, 609, 13], [593, 10, 610, 8, "console"], [593, 17, 610, 15], [593, 18, 610, 16, "log"], [593, 21, 610, 19], [593, 22, 610, 20], [593, 74, 610, 72], [593, 75, 610, 73], [594, 10, 611, 8, "console"], [594, 17, 611, 15], [594, 18, 611, 16, "log"], [594, 21, 611, 19], [594, 22, 611, 20], [594, 95, 611, 93], [594, 96, 611, 94], [596, 10, 613, 8], [597, 10, 614, 8], [597, 16, 614, 14, "centerX"], [597, 23, 614, 21], [597, 26, 614, 24, "img"], [597, 29, 614, 27], [597, 30, 614, 28, "width"], [597, 35, 614, 33], [597, 38, 614, 36], [597, 41, 614, 39], [598, 10, 615, 8], [598, 16, 615, 14, "centerY"], [598, 23, 615, 21], [598, 26, 615, 24, "img"], [598, 29, 615, 27], [598, 30, 615, 28, "height"], [598, 36, 615, 34], [598, 39, 615, 37], [598, 42, 615, 40], [599, 10, 616, 8], [599, 16, 616, 14, "centerWidth"], [599, 27, 616, 25], [599, 30, 616, 28, "img"], [599, 33, 616, 31], [599, 34, 616, 32, "width"], [599, 39, 616, 37], [599, 42, 616, 40], [599, 45, 616, 43], [600, 10, 617, 8], [600, 16, 617, 14, "centerHeight"], [600, 28, 617, 26], [600, 31, 617, 29, "img"], [600, 34, 617, 32], [600, 35, 617, 33, "height"], [600, 41, 617, 39], [600, 44, 617, 42], [600, 47, 617, 45], [601, 10, 619, 8, "detectedFaces"], [601, 23, 619, 21], [601, 26, 619, 24], [601, 27, 619, 25], [602, 12, 620, 10, "boundingBox"], [602, 23, 620, 21], [602, 25, 620, 23], [603, 14, 621, 12, "xCenter"], [603, 21, 621, 19], [603, 23, 621, 21], [603, 26, 621, 24], [604, 14, 622, 12, "yCenter"], [604, 21, 622, 19], [604, 23, 622, 21], [604, 26, 622, 24], [605, 14, 623, 12, "width"], [605, 19, 623, 17], [605, 21, 623, 19], [605, 24, 623, 22], [606, 14, 624, 12, "height"], [606, 20, 624, 18], [606, 22, 624, 20], [607, 12, 625, 10], [607, 13, 625, 11], [608, 12, 626, 10, "confidence"], [608, 22, 626, 20], [608, 24, 626, 22], [609, 10, 627, 8], [609, 11, 627, 9], [609, 12, 627, 10], [610, 10, 629, 8, "console"], [610, 17, 629, 15], [610, 18, 629, 16, "log"], [610, 21, 629, 19], [610, 22, 629, 20], [610, 100, 629, 98], [610, 101, 629, 99], [611, 8, 630, 6], [612, 8, 632, 6, "setProcessingProgress"], [612, 29, 632, 27], [612, 30, 632, 28], [612, 32, 632, 30], [612, 33, 632, 31], [614, 8, 634, 6], [615, 8, 635, 6], [615, 12, 635, 10, "detectedFaces"], [615, 25, 635, 23], [615, 26, 635, 24, "length"], [615, 32, 635, 30], [615, 35, 635, 33], [615, 36, 635, 34], [615, 38, 635, 36], [616, 10, 636, 8, "console"], [616, 17, 636, 15], [616, 18, 636, 16, "log"], [616, 21, 636, 19], [616, 22, 636, 20], [616, 61, 636, 59, "detectedFaces"], [616, 74, 636, 72], [616, 75, 636, 73, "length"], [616, 81, 636, 79], [616, 101, 636, 99], [616, 102, 636, 100], [617, 10, 638, 8, "detectedFaces"], [617, 23, 638, 21], [617, 24, 638, 22, "for<PERSON>ach"], [617, 31, 638, 29], [617, 32, 638, 30], [617, 33, 638, 31, "detection"], [617, 42, 638, 40], [617, 44, 638, 42, "index"], [617, 49, 638, 47], [617, 54, 638, 52], [618, 12, 639, 10], [618, 18, 639, 16, "bbox"], [618, 22, 639, 20], [618, 25, 639, 23, "detection"], [618, 34, 639, 32], [618, 35, 639, 33, "boundingBox"], [618, 46, 639, 44], [620, 12, 641, 10], [621, 12, 642, 10], [621, 18, 642, 16, "faceX"], [621, 23, 642, 21], [621, 26, 642, 24, "bbox"], [621, 30, 642, 28], [621, 31, 642, 29, "xCenter"], [621, 38, 642, 36], [621, 41, 642, 39, "img"], [621, 44, 642, 42], [621, 45, 642, 43, "width"], [621, 50, 642, 48], [621, 53, 642, 52, "bbox"], [621, 57, 642, 56], [621, 58, 642, 57, "width"], [621, 63, 642, 62], [621, 66, 642, 65, "img"], [621, 69, 642, 68], [621, 70, 642, 69, "width"], [621, 75, 642, 74], [621, 78, 642, 78], [621, 79, 642, 79], [622, 12, 643, 10], [622, 18, 643, 16, "faceY"], [622, 23, 643, 21], [622, 26, 643, 24, "bbox"], [622, 30, 643, 28], [622, 31, 643, 29, "yCenter"], [622, 38, 643, 36], [622, 41, 643, 39, "img"], [622, 44, 643, 42], [622, 45, 643, 43, "height"], [622, 51, 643, 49], [622, 54, 643, 53, "bbox"], [622, 58, 643, 57], [622, 59, 643, 58, "height"], [622, 65, 643, 64], [622, 68, 643, 67, "img"], [622, 71, 643, 70], [622, 72, 643, 71, "height"], [622, 78, 643, 77], [622, 81, 643, 81], [622, 82, 643, 82], [623, 12, 644, 10], [623, 18, 644, 16, "faceWidth"], [623, 27, 644, 25], [623, 30, 644, 28, "bbox"], [623, 34, 644, 32], [623, 35, 644, 33, "width"], [623, 40, 644, 38], [623, 43, 644, 41, "img"], [623, 46, 644, 44], [623, 47, 644, 45, "width"], [623, 52, 644, 50], [624, 12, 645, 10], [624, 18, 645, 16, "faceHeight"], [624, 28, 645, 26], [624, 31, 645, 29, "bbox"], [624, 35, 645, 33], [624, 36, 645, 34, "height"], [624, 42, 645, 40], [624, 45, 645, 43, "img"], [624, 48, 645, 46], [624, 49, 645, 47, "height"], [624, 55, 645, 53], [626, 12, 647, 10], [627, 12, 648, 10], [627, 18, 648, 16, "padding"], [627, 25, 648, 23], [627, 28, 648, 26], [627, 31, 648, 29], [628, 12, 649, 10], [628, 18, 649, 16, "paddedX"], [628, 25, 649, 23], [628, 28, 649, 26, "Math"], [628, 32, 649, 30], [628, 33, 649, 31, "max"], [628, 36, 649, 34], [628, 37, 649, 35], [628, 38, 649, 36], [628, 40, 649, 38, "faceX"], [628, 45, 649, 43], [628, 48, 649, 46, "faceWidth"], [628, 57, 649, 55], [628, 60, 649, 58, "padding"], [628, 67, 649, 65], [628, 68, 649, 66], [629, 12, 650, 10], [629, 18, 650, 16, "paddedY"], [629, 25, 650, 23], [629, 28, 650, 26, "Math"], [629, 32, 650, 30], [629, 33, 650, 31, "max"], [629, 36, 650, 34], [629, 37, 650, 35], [629, 38, 650, 36], [629, 40, 650, 38, "faceY"], [629, 45, 650, 43], [629, 48, 650, 46, "faceHeight"], [629, 58, 650, 56], [629, 61, 650, 59, "padding"], [629, 68, 650, 66], [629, 69, 650, 67], [630, 12, 651, 10], [630, 18, 651, 16, "<PERSON><PERSON><PERSON><PERSON>"], [630, 29, 651, 27], [630, 32, 651, 30, "Math"], [630, 36, 651, 34], [630, 37, 651, 35, "min"], [630, 40, 651, 38], [630, 41, 651, 39, "img"], [630, 44, 651, 42], [630, 45, 651, 43, "width"], [630, 50, 651, 48], [630, 53, 651, 51, "paddedX"], [630, 60, 651, 58], [630, 62, 651, 60, "faceWidth"], [630, 71, 651, 69], [630, 75, 651, 73], [630, 76, 651, 74], [630, 79, 651, 77], [630, 80, 651, 78], [630, 83, 651, 81, "padding"], [630, 90, 651, 88], [630, 91, 651, 89], [630, 92, 651, 90], [631, 12, 652, 10], [631, 18, 652, 16, "paddedHeight"], [631, 30, 652, 28], [631, 33, 652, 31, "Math"], [631, 37, 652, 35], [631, 38, 652, 36, "min"], [631, 41, 652, 39], [631, 42, 652, 40, "img"], [631, 45, 652, 43], [631, 46, 652, 44, "height"], [631, 52, 652, 50], [631, 55, 652, 53, "paddedY"], [631, 62, 652, 60], [631, 64, 652, 62, "faceHeight"], [631, 74, 652, 72], [631, 78, 652, 76], [631, 79, 652, 77], [631, 82, 652, 80], [631, 83, 652, 81], [631, 86, 652, 84, "padding"], [631, 93, 652, 91], [631, 94, 652, 92], [631, 95, 652, 93], [632, 12, 654, 10, "console"], [632, 19, 654, 17], [632, 20, 654, 18, "log"], [632, 23, 654, 21], [632, 24, 654, 22], [632, 60, 654, 58, "index"], [632, 65, 654, 63], [632, 68, 654, 66], [632, 69, 654, 67], [632, 72, 654, 70], [632, 74, 654, 72], [633, 14, 655, 12, "original"], [633, 22, 655, 20], [633, 24, 655, 22], [634, 16, 655, 24, "x"], [634, 17, 655, 25], [634, 19, 655, 27, "Math"], [634, 23, 655, 31], [634, 24, 655, 32, "round"], [634, 29, 655, 37], [634, 30, 655, 38, "faceX"], [634, 35, 655, 43], [634, 36, 655, 44], [635, 16, 655, 46, "y"], [635, 17, 655, 47], [635, 19, 655, 49, "Math"], [635, 23, 655, 53], [635, 24, 655, 54, "round"], [635, 29, 655, 59], [635, 30, 655, 60, "faceY"], [635, 35, 655, 65], [635, 36, 655, 66], [636, 16, 655, 68, "w"], [636, 17, 655, 69], [636, 19, 655, 71, "Math"], [636, 23, 655, 75], [636, 24, 655, 76, "round"], [636, 29, 655, 81], [636, 30, 655, 82, "faceWidth"], [636, 39, 655, 91], [636, 40, 655, 92], [637, 16, 655, 94, "h"], [637, 17, 655, 95], [637, 19, 655, 97, "Math"], [637, 23, 655, 101], [637, 24, 655, 102, "round"], [637, 29, 655, 107], [637, 30, 655, 108, "faceHeight"], [637, 40, 655, 118], [638, 14, 655, 120], [638, 15, 655, 121], [639, 14, 656, 12, "padded"], [639, 20, 656, 18], [639, 22, 656, 20], [640, 16, 656, 22, "x"], [640, 17, 656, 23], [640, 19, 656, 25, "Math"], [640, 23, 656, 29], [640, 24, 656, 30, "round"], [640, 29, 656, 35], [640, 30, 656, 36, "paddedX"], [640, 37, 656, 43], [640, 38, 656, 44], [641, 16, 656, 46, "y"], [641, 17, 656, 47], [641, 19, 656, 49, "Math"], [641, 23, 656, 53], [641, 24, 656, 54, "round"], [641, 29, 656, 59], [641, 30, 656, 60, "paddedY"], [641, 37, 656, 67], [641, 38, 656, 68], [642, 16, 656, 70, "w"], [642, 17, 656, 71], [642, 19, 656, 73, "Math"], [642, 23, 656, 77], [642, 24, 656, 78, "round"], [642, 29, 656, 83], [642, 30, 656, 84, "<PERSON><PERSON><PERSON><PERSON>"], [642, 41, 656, 95], [642, 42, 656, 96], [643, 16, 656, 98, "h"], [643, 17, 656, 99], [643, 19, 656, 101, "Math"], [643, 23, 656, 105], [643, 24, 656, 106, "round"], [643, 29, 656, 111], [643, 30, 656, 112, "paddedHeight"], [643, 42, 656, 124], [644, 14, 656, 126], [645, 12, 657, 10], [645, 13, 657, 11], [645, 14, 657, 12], [647, 12, 659, 10], [648, 12, 660, 10, "console"], [648, 19, 660, 17], [648, 20, 660, 18, "log"], [648, 23, 660, 21], [648, 24, 660, 22], [648, 70, 660, 68], [648, 72, 660, 70], [649, 14, 661, 12, "width"], [649, 19, 661, 17], [649, 21, 661, 19, "canvas"], [649, 27, 661, 25], [649, 28, 661, 26, "width"], [649, 33, 661, 31], [650, 14, 662, 12, "height"], [650, 20, 662, 18], [650, 22, 662, 20, "canvas"], [650, 28, 662, 26], [650, 29, 662, 27, "height"], [650, 35, 662, 33], [651, 14, 663, 12, "contextValid"], [651, 26, 663, 24], [651, 28, 663, 26], [651, 29, 663, 27], [651, 30, 663, 28, "ctx"], [652, 12, 664, 10], [652, 13, 664, 11], [652, 14, 664, 12], [654, 12, 666, 10], [655, 12, 667, 10, "applyStrongBlur"], [655, 27, 667, 25], [655, 28, 667, 26, "ctx"], [655, 31, 667, 29], [655, 33, 667, 31, "paddedX"], [655, 40, 667, 38], [655, 42, 667, 40, "paddedY"], [655, 49, 667, 47], [655, 51, 667, 49, "<PERSON><PERSON><PERSON><PERSON>"], [655, 62, 667, 60], [655, 64, 667, 62, "paddedHeight"], [655, 76, 667, 74], [655, 77, 667, 75], [657, 12, 669, 10], [658, 12, 670, 10, "console"], [658, 19, 670, 17], [658, 20, 670, 18, "log"], [658, 23, 670, 21], [658, 24, 670, 22], [658, 102, 670, 100], [658, 103, 670, 101], [660, 12, 672, 10], [661, 12, 673, 10], [661, 18, 673, 16, "testImageData"], [661, 31, 673, 29], [661, 34, 673, 32, "ctx"], [661, 37, 673, 35], [661, 38, 673, 36, "getImageData"], [661, 50, 673, 48], [661, 51, 673, 49, "paddedX"], [661, 58, 673, 56], [661, 61, 673, 59], [661, 63, 673, 61], [661, 65, 673, 63, "paddedY"], [661, 72, 673, 70], [661, 75, 673, 73], [661, 77, 673, 75], [661, 79, 673, 77], [661, 81, 673, 79], [661, 83, 673, 81], [661, 85, 673, 83], [661, 86, 673, 84], [662, 12, 674, 10, "console"], [662, 19, 674, 17], [662, 20, 674, 18, "log"], [662, 23, 674, 21], [662, 24, 674, 22], [662, 70, 674, 68], [662, 72, 674, 70], [663, 14, 675, 12, "firstPixel"], [663, 24, 675, 22], [663, 26, 675, 24], [663, 27, 675, 25, "testImageData"], [663, 40, 675, 38], [663, 41, 675, 39, "data"], [663, 45, 675, 43], [663, 46, 675, 44], [663, 47, 675, 45], [663, 48, 675, 46], [663, 50, 675, 48, "testImageData"], [663, 63, 675, 61], [663, 64, 675, 62, "data"], [663, 68, 675, 66], [663, 69, 675, 67], [663, 70, 675, 68], [663, 71, 675, 69], [663, 73, 675, 71, "testImageData"], [663, 86, 675, 84], [663, 87, 675, 85, "data"], [663, 91, 675, 89], [663, 92, 675, 90], [663, 93, 675, 91], [663, 94, 675, 92], [663, 95, 675, 93], [664, 14, 676, 12, "secondPixel"], [664, 25, 676, 23], [664, 27, 676, 25], [664, 28, 676, 26, "testImageData"], [664, 41, 676, 39], [664, 42, 676, 40, "data"], [664, 46, 676, 44], [664, 47, 676, 45], [664, 48, 676, 46], [664, 49, 676, 47], [664, 51, 676, 49, "testImageData"], [664, 64, 676, 62], [664, 65, 676, 63, "data"], [664, 69, 676, 67], [664, 70, 676, 68], [664, 71, 676, 69], [664, 72, 676, 70], [664, 74, 676, 72, "testImageData"], [664, 87, 676, 85], [664, 88, 676, 86, "data"], [664, 92, 676, 90], [664, 93, 676, 91], [664, 94, 676, 92], [664, 95, 676, 93], [665, 12, 677, 10], [665, 13, 677, 11], [665, 14, 677, 12], [666, 12, 679, 10, "console"], [666, 19, 679, 17], [666, 20, 679, 18, "log"], [666, 23, 679, 21], [666, 24, 679, 22], [666, 50, 679, 48, "index"], [666, 55, 679, 53], [666, 58, 679, 56], [666, 59, 679, 57], [666, 79, 679, 77], [666, 80, 679, 78], [667, 10, 680, 8], [667, 11, 680, 9], [667, 12, 680, 10], [668, 10, 682, 8, "console"], [668, 17, 682, 15], [668, 18, 682, 16, "log"], [668, 21, 682, 19], [668, 22, 682, 20], [668, 48, 682, 46, "detectedFaces"], [668, 61, 682, 59], [668, 62, 682, 60, "length"], [668, 68, 682, 66], [668, 104, 682, 102], [668, 105, 682, 103], [669, 8, 683, 6], [669, 9, 683, 7], [669, 15, 683, 13], [670, 10, 684, 8, "console"], [670, 17, 684, 15], [670, 18, 684, 16, "log"], [670, 21, 684, 19], [670, 22, 684, 20], [670, 109, 684, 107], [670, 110, 684, 108], [671, 10, 685, 8], [672, 10, 686, 8, "applyFallbackFaceBlur"], [672, 31, 686, 29], [672, 32, 686, 30, "ctx"], [672, 35, 686, 33], [672, 37, 686, 35, "img"], [672, 40, 686, 38], [672, 41, 686, 39, "width"], [672, 46, 686, 44], [672, 48, 686, 46, "img"], [672, 51, 686, 49], [672, 52, 686, 50, "height"], [672, 58, 686, 56], [672, 59, 686, 57], [673, 8, 687, 6], [674, 8, 689, 6, "setProcessingProgress"], [674, 29, 689, 27], [674, 30, 689, 28], [674, 32, 689, 30], [674, 33, 689, 31], [676, 8, 691, 6], [677, 8, 692, 6, "console"], [677, 15, 692, 13], [677, 16, 692, 14, "log"], [677, 19, 692, 17], [677, 20, 692, 18], [677, 85, 692, 83], [677, 86, 692, 84], [678, 8, 693, 6], [678, 14, 693, 12, "blurredImageBlob"], [678, 30, 693, 28], [678, 33, 693, 31], [678, 39, 693, 37], [678, 43, 693, 41, "Promise"], [678, 50, 693, 48], [678, 51, 693, 56, "resolve"], [678, 58, 693, 63], [678, 62, 693, 68], [679, 10, 694, 8, "canvas"], [679, 16, 694, 14], [679, 17, 694, 15, "toBlob"], [679, 23, 694, 21], [679, 24, 694, 23, "blob"], [679, 28, 694, 27], [679, 32, 694, 32, "resolve"], [679, 39, 694, 39], [679, 40, 694, 40, "blob"], [679, 44, 694, 45], [679, 45, 694, 46], [679, 47, 694, 48], [679, 59, 694, 60], [679, 61, 694, 62], [679, 64, 694, 65], [679, 65, 694, 66], [680, 8, 695, 6], [680, 9, 695, 7], [680, 10, 695, 8], [681, 8, 697, 6], [681, 14, 697, 12, "blurredImageUrl"], [681, 29, 697, 27], [681, 32, 697, 30, "URL"], [681, 35, 697, 33], [681, 36, 697, 34, "createObjectURL"], [681, 51, 697, 49], [681, 52, 697, 50, "blurredImageBlob"], [681, 68, 697, 66], [681, 69, 697, 67], [682, 8, 698, 6, "console"], [682, 15, 698, 13], [682, 16, 698, 14, "log"], [682, 19, 698, 17], [682, 20, 698, 18], [682, 66, 698, 64], [682, 68, 698, 66, "blurredImageUrl"], [682, 83, 698, 81], [682, 84, 698, 82, "substring"], [682, 93, 698, 91], [682, 94, 698, 92], [682, 95, 698, 93], [682, 97, 698, 95], [682, 99, 698, 97], [682, 100, 698, 98], [682, 103, 698, 101], [682, 108, 698, 106], [682, 109, 698, 107], [684, 8, 700, 6], [685, 8, 701, 6, "setCapturedPhoto"], [685, 24, 701, 22], [685, 25, 701, 23, "blurredImageUrl"], [685, 40, 701, 38], [685, 41, 701, 39], [686, 8, 702, 6, "console"], [686, 15, 702, 13], [686, 16, 702, 14, "log"], [686, 19, 702, 17], [686, 20, 702, 18], [686, 87, 702, 85], [686, 88, 702, 86], [687, 8, 704, 6, "setProcessingProgress"], [687, 29, 704, 27], [687, 30, 704, 28], [687, 33, 704, 31], [687, 34, 704, 32], [689, 8, 706, 6], [690, 8, 707, 6], [690, 14, 707, 12, "completeProcessing"], [690, 32, 707, 30], [690, 33, 707, 31, "blurredImageUrl"], [690, 48, 707, 46], [690, 49, 707, 47], [691, 6, 709, 4], [691, 7, 709, 5], [691, 8, 709, 6], [691, 15, 709, 13, "error"], [691, 20, 709, 18], [691, 22, 709, 20], [692, 8, 710, 6, "console"], [692, 15, 710, 13], [692, 16, 710, 14, "error"], [692, 21, 710, 19], [692, 22, 710, 20], [692, 57, 710, 55], [692, 59, 710, 57, "error"], [692, 64, 710, 62], [692, 65, 710, 63], [693, 8, 711, 6, "setErrorMessage"], [693, 23, 711, 21], [693, 24, 711, 22], [693, 50, 711, 48], [693, 51, 711, 49], [694, 8, 712, 6, "setProcessingState"], [694, 26, 712, 24], [694, 27, 712, 25], [694, 34, 712, 32], [694, 35, 712, 33], [695, 6, 713, 4], [696, 4, 714, 2], [696, 5, 714, 3], [698, 4, 716, 2], [699, 4, 717, 2], [699, 10, 717, 8, "completeProcessing"], [699, 28, 717, 26], [699, 31, 717, 29], [699, 37, 717, 36, "blurredImageUrl"], [699, 52, 717, 59], [699, 56, 717, 64], [700, 6, 718, 4], [700, 10, 718, 8], [701, 8, 719, 6, "setProcessingState"], [701, 26, 719, 24], [701, 27, 719, 25], [701, 37, 719, 35], [701, 38, 719, 36], [703, 8, 721, 6], [704, 8, 722, 6], [704, 14, 722, 12, "timestamp"], [704, 23, 722, 21], [704, 26, 722, 24, "Date"], [704, 30, 722, 28], [704, 31, 722, 29, "now"], [704, 34, 722, 32], [704, 35, 722, 33], [704, 36, 722, 34], [705, 8, 723, 6], [705, 14, 723, 12, "result"], [705, 20, 723, 18], [705, 23, 723, 21], [706, 10, 724, 8, "imageUrl"], [706, 18, 724, 16], [706, 20, 724, 18, "blurredImageUrl"], [706, 35, 724, 33], [707, 10, 725, 8, "localUri"], [707, 18, 725, 16], [707, 20, 725, 18, "blurredImageUrl"], [707, 35, 725, 33], [708, 10, 726, 8, "challengeCode"], [708, 23, 726, 21], [708, 25, 726, 23, "challengeCode"], [708, 38, 726, 36], [708, 42, 726, 40], [708, 44, 726, 42], [709, 10, 727, 8, "timestamp"], [709, 19, 727, 17], [710, 10, 728, 8, "jobId"], [710, 15, 728, 13], [710, 17, 728, 15], [710, 27, 728, 25, "timestamp"], [710, 36, 728, 34], [710, 38, 728, 36], [711, 10, 729, 8, "status"], [711, 16, 729, 14], [711, 18, 729, 16], [712, 8, 730, 6], [712, 9, 730, 7], [713, 8, 732, 6, "console"], [713, 15, 732, 13], [713, 16, 732, 14, "log"], [713, 19, 732, 17], [713, 20, 732, 18], [713, 100, 732, 98], [713, 102, 732, 100], [714, 10, 733, 8, "imageUrl"], [714, 18, 733, 16], [714, 20, 733, 18, "blurredImageUrl"], [714, 35, 733, 33], [714, 36, 733, 34, "substring"], [714, 45, 733, 43], [714, 46, 733, 44], [714, 47, 733, 45], [714, 49, 733, 47], [714, 51, 733, 49], [714, 52, 733, 50], [714, 55, 733, 53], [714, 60, 733, 58], [715, 10, 734, 8, "timestamp"], [715, 19, 734, 17], [716, 10, 735, 8, "jobId"], [716, 15, 735, 13], [716, 17, 735, 15, "result"], [716, 23, 735, 21], [716, 24, 735, 22, "jobId"], [717, 8, 736, 6], [717, 9, 736, 7], [717, 10, 736, 8], [719, 8, 738, 6], [720, 8, 739, 6, "onComplete"], [720, 18, 739, 16], [720, 19, 739, 17, "result"], [720, 25, 739, 23], [720, 26, 739, 24], [721, 6, 741, 4], [721, 7, 741, 5], [721, 8, 741, 6], [721, 15, 741, 13, "error"], [721, 20, 741, 18], [721, 22, 741, 20], [722, 8, 742, 6, "console"], [722, 15, 742, 13], [722, 16, 742, 14, "error"], [722, 21, 742, 19], [722, 22, 742, 20], [722, 57, 742, 55], [722, 59, 742, 57, "error"], [722, 64, 742, 62], [722, 65, 742, 63], [723, 8, 743, 6, "setErrorMessage"], [723, 23, 743, 21], [723, 24, 743, 22], [723, 56, 743, 54], [723, 57, 743, 55], [724, 8, 744, 6, "setProcessingState"], [724, 26, 744, 24], [724, 27, 744, 25], [724, 34, 744, 32], [724, 35, 744, 33], [725, 6, 745, 4], [726, 4, 746, 2], [726, 5, 746, 3], [728, 4, 748, 2], [729, 4, 749, 2], [729, 10, 749, 8, "triggerServerProcessing"], [729, 33, 749, 31], [729, 36, 749, 34], [729, 42, 749, 34, "triggerServerProcessing"], [729, 43, 749, 41, "privateImageUrl"], [729, 58, 749, 64], [729, 60, 749, 66, "timestamp"], [729, 69, 749, 83], [729, 74, 749, 88], [730, 6, 750, 4], [730, 10, 750, 8], [731, 8, 751, 6, "console"], [731, 15, 751, 13], [731, 16, 751, 14, "log"], [731, 19, 751, 17], [731, 20, 751, 18], [731, 74, 751, 72], [731, 76, 751, 74, "privateImageUrl"], [731, 91, 751, 89], [731, 92, 751, 90], [732, 8, 752, 6, "setProcessingState"], [732, 26, 752, 24], [732, 27, 752, 25], [732, 39, 752, 37], [732, 40, 752, 38], [733, 8, 753, 6, "setProcessingProgress"], [733, 29, 753, 27], [733, 30, 753, 28], [733, 32, 753, 30], [733, 33, 753, 31], [734, 8, 755, 6], [734, 14, 755, 12, "requestBody"], [734, 25, 755, 23], [734, 28, 755, 26], [735, 10, 756, 8, "imageUrl"], [735, 18, 756, 16], [735, 20, 756, 18, "privateImageUrl"], [735, 35, 756, 33], [736, 10, 757, 8, "userId"], [736, 16, 757, 14], [737, 10, 758, 8, "requestId"], [737, 19, 758, 17], [738, 10, 759, 8, "timestamp"], [738, 19, 759, 17], [739, 10, 760, 8, "platform"], [739, 18, 760, 16], [739, 20, 760, 18], [740, 8, 761, 6], [740, 9, 761, 7], [741, 8, 763, 6, "console"], [741, 15, 763, 13], [741, 16, 763, 14, "log"], [741, 19, 763, 17], [741, 20, 763, 18], [741, 65, 763, 63], [741, 67, 763, 65, "requestBody"], [741, 78, 763, 76], [741, 79, 763, 77], [743, 8, 765, 6], [744, 8, 766, 6], [744, 14, 766, 12, "response"], [744, 22, 766, 20], [744, 25, 766, 23], [744, 31, 766, 29, "fetch"], [744, 36, 766, 34], [744, 37, 766, 35], [744, 40, 766, 38, "API_BASE_URL"], [744, 52, 766, 50], [744, 72, 766, 70], [744, 74, 766, 72], [745, 10, 767, 8, "method"], [745, 16, 767, 14], [745, 18, 767, 16], [745, 24, 767, 22], [746, 10, 768, 8, "headers"], [746, 17, 768, 15], [746, 19, 768, 17], [747, 12, 769, 10], [747, 26, 769, 24], [747, 28, 769, 26], [747, 46, 769, 44], [748, 12, 770, 10], [748, 27, 770, 25], [748, 29, 770, 27], [748, 39, 770, 37], [748, 45, 770, 43, "getAuthToken"], [748, 57, 770, 55], [748, 58, 770, 56], [748, 59, 770, 57], [749, 10, 771, 8], [749, 11, 771, 9], [750, 10, 772, 8, "body"], [750, 14, 772, 12], [750, 16, 772, 14, "JSON"], [750, 20, 772, 18], [750, 21, 772, 19, "stringify"], [750, 30, 772, 28], [750, 31, 772, 29, "requestBody"], [750, 42, 772, 40], [751, 8, 773, 6], [751, 9, 773, 7], [751, 10, 773, 8], [752, 8, 775, 6], [752, 12, 775, 10], [752, 13, 775, 11, "response"], [752, 21, 775, 19], [752, 22, 775, 20, "ok"], [752, 24, 775, 22], [752, 26, 775, 24], [753, 10, 776, 8], [753, 16, 776, 14, "errorText"], [753, 25, 776, 23], [753, 28, 776, 26], [753, 34, 776, 32, "response"], [753, 42, 776, 40], [753, 43, 776, 41, "text"], [753, 47, 776, 45], [753, 48, 776, 46], [753, 49, 776, 47], [754, 10, 777, 8, "console"], [754, 17, 777, 15], [754, 18, 777, 16, "error"], [754, 23, 777, 21], [754, 24, 777, 22], [754, 68, 777, 66], [754, 70, 777, 68, "response"], [754, 78, 777, 76], [754, 79, 777, 77, "status"], [754, 85, 777, 83], [754, 87, 777, 85, "errorText"], [754, 96, 777, 94], [754, 97, 777, 95], [755, 10, 778, 8], [755, 16, 778, 14], [755, 20, 778, 18, "Error"], [755, 25, 778, 23], [755, 26, 778, 24], [755, 48, 778, 46, "response"], [755, 56, 778, 54], [755, 57, 778, 55, "status"], [755, 63, 778, 61], [755, 67, 778, 65, "response"], [755, 75, 778, 73], [755, 76, 778, 74, "statusText"], [755, 86, 778, 84], [755, 88, 778, 86], [755, 89, 778, 87], [756, 8, 779, 6], [757, 8, 781, 6], [757, 14, 781, 12, "result"], [757, 20, 781, 18], [757, 23, 781, 21], [757, 29, 781, 27, "response"], [757, 37, 781, 35], [757, 38, 781, 36, "json"], [757, 42, 781, 40], [757, 43, 781, 41], [757, 44, 781, 42], [758, 8, 782, 6, "console"], [758, 15, 782, 13], [758, 16, 782, 14, "log"], [758, 19, 782, 17], [758, 20, 782, 18], [758, 68, 782, 66], [758, 70, 782, 68, "result"], [758, 76, 782, 74], [758, 77, 782, 75], [759, 8, 784, 6], [759, 12, 784, 10], [759, 13, 784, 11, "result"], [759, 19, 784, 17], [759, 20, 784, 18, "jobId"], [759, 25, 784, 23], [759, 27, 784, 25], [760, 10, 785, 8], [760, 16, 785, 14], [760, 20, 785, 18, "Error"], [760, 25, 785, 23], [760, 26, 785, 24], [760, 70, 785, 68], [760, 71, 785, 69], [761, 8, 786, 6], [763, 8, 788, 6], [764, 8, 789, 6], [764, 14, 789, 12, "pollForCompletion"], [764, 31, 789, 29], [764, 32, 789, 30, "result"], [764, 38, 789, 36], [764, 39, 789, 37, "jobId"], [764, 44, 789, 42], [764, 46, 789, 44, "timestamp"], [764, 55, 789, 53], [764, 56, 789, 54], [765, 6, 790, 4], [765, 7, 790, 5], [765, 8, 790, 6], [765, 15, 790, 13, "error"], [765, 20, 790, 18], [765, 22, 790, 20], [766, 8, 791, 6, "console"], [766, 15, 791, 13], [766, 16, 791, 14, "error"], [766, 21, 791, 19], [766, 22, 791, 20], [766, 57, 791, 55], [766, 59, 791, 57, "error"], [766, 64, 791, 62], [766, 65, 791, 63], [767, 8, 792, 6, "setErrorMessage"], [767, 23, 792, 21], [767, 24, 792, 22], [767, 52, 792, 50, "error"], [767, 57, 792, 55], [767, 58, 792, 56, "message"], [767, 65, 792, 63], [767, 67, 792, 65], [767, 68, 792, 66], [768, 8, 793, 6, "setProcessingState"], [768, 26, 793, 24], [768, 27, 793, 25], [768, 34, 793, 32], [768, 35, 793, 33], [769, 6, 794, 4], [770, 4, 795, 2], [770, 5, 795, 3], [771, 4, 796, 2], [772, 4, 797, 2], [772, 10, 797, 8, "pollForCompletion"], [772, 27, 797, 25], [772, 30, 797, 28], [772, 36, 797, 28, "pollForCompletion"], [772, 37, 797, 35, "jobId"], [772, 42, 797, 48], [772, 44, 797, 50, "timestamp"], [772, 53, 797, 67], [772, 55, 797, 69, "attempts"], [772, 63, 797, 77], [772, 66, 797, 80], [772, 67, 797, 81], [772, 72, 797, 86], [773, 6, 798, 4], [773, 12, 798, 10, "MAX_ATTEMPTS"], [773, 24, 798, 22], [773, 27, 798, 25], [773, 29, 798, 27], [773, 30, 798, 28], [773, 31, 798, 29], [774, 6, 799, 4], [774, 12, 799, 10, "POLL_INTERVAL"], [774, 25, 799, 23], [774, 28, 799, 26], [774, 32, 799, 30], [774, 33, 799, 31], [774, 34, 799, 32], [776, 6, 801, 4, "console"], [776, 13, 801, 11], [776, 14, 801, 12, "log"], [776, 17, 801, 15], [776, 18, 801, 16], [776, 53, 801, 51, "attempts"], [776, 61, 801, 59], [776, 64, 801, 62], [776, 65, 801, 63], [776, 69, 801, 67, "MAX_ATTEMPTS"], [776, 81, 801, 79], [776, 93, 801, 91, "jobId"], [776, 98, 801, 96], [776, 100, 801, 98], [776, 101, 801, 99], [777, 6, 803, 4], [777, 10, 803, 8, "attempts"], [777, 18, 803, 16], [777, 22, 803, 20, "MAX_ATTEMPTS"], [777, 34, 803, 32], [777, 36, 803, 34], [778, 8, 804, 6, "console"], [778, 15, 804, 13], [778, 16, 804, 14, "error"], [778, 21, 804, 19], [778, 22, 804, 20], [778, 75, 804, 73], [778, 76, 804, 74], [779, 8, 805, 6, "setErrorMessage"], [779, 23, 805, 21], [779, 24, 805, 22], [779, 63, 805, 61], [779, 64, 805, 62], [780, 8, 806, 6, "setProcessingState"], [780, 26, 806, 24], [780, 27, 806, 25], [780, 34, 806, 32], [780, 35, 806, 33], [781, 8, 807, 6], [782, 6, 808, 4], [783, 6, 810, 4], [783, 10, 810, 8], [784, 8, 811, 6], [784, 14, 811, 12, "response"], [784, 22, 811, 20], [784, 25, 811, 23], [784, 31, 811, 29, "fetch"], [784, 36, 811, 34], [784, 37, 811, 35], [784, 40, 811, 38, "API_BASE_URL"], [784, 52, 811, 50], [784, 75, 811, 73, "jobId"], [784, 80, 811, 78], [784, 82, 811, 80], [784, 84, 811, 82], [785, 10, 812, 8, "headers"], [785, 17, 812, 15], [785, 19, 812, 17], [786, 12, 813, 10], [786, 27, 813, 25], [786, 29, 813, 27], [786, 39, 813, 37], [786, 45, 813, 43, "getAuthToken"], [786, 57, 813, 55], [786, 58, 813, 56], [786, 59, 813, 57], [787, 10, 814, 8], [788, 8, 815, 6], [788, 9, 815, 7], [788, 10, 815, 8], [789, 8, 817, 6], [789, 12, 817, 10], [789, 13, 817, 11, "response"], [789, 21, 817, 19], [789, 22, 817, 20, "ok"], [789, 24, 817, 22], [789, 26, 817, 24], [790, 10, 818, 8], [790, 16, 818, 14], [790, 20, 818, 18, "Error"], [790, 25, 818, 23], [790, 26, 818, 24], [790, 34, 818, 32, "response"], [790, 42, 818, 40], [790, 43, 818, 41, "status"], [790, 49, 818, 47], [790, 54, 818, 52, "response"], [790, 62, 818, 60], [790, 63, 818, 61, "statusText"], [790, 73, 818, 71], [790, 75, 818, 73], [790, 76, 818, 74], [791, 8, 819, 6], [792, 8, 821, 6], [792, 14, 821, 12, "status"], [792, 20, 821, 18], [792, 23, 821, 21], [792, 29, 821, 27, "response"], [792, 37, 821, 35], [792, 38, 821, 36, "json"], [792, 42, 821, 40], [792, 43, 821, 41], [792, 44, 821, 42], [793, 8, 822, 6, "console"], [793, 15, 822, 13], [793, 16, 822, 14, "log"], [793, 19, 822, 17], [793, 20, 822, 18], [793, 54, 822, 52], [793, 56, 822, 54, "status"], [793, 62, 822, 60], [793, 63, 822, 61], [794, 8, 824, 6], [794, 12, 824, 10, "status"], [794, 18, 824, 16], [794, 19, 824, 17, "status"], [794, 25, 824, 23], [794, 30, 824, 28], [794, 41, 824, 39], [794, 43, 824, 41], [795, 10, 825, 8, "console"], [795, 17, 825, 15], [795, 18, 825, 16, "log"], [795, 21, 825, 19], [795, 22, 825, 20], [795, 73, 825, 71], [795, 74, 825, 72], [796, 10, 826, 8, "setProcessingProgress"], [796, 31, 826, 29], [796, 32, 826, 30], [796, 35, 826, 33], [796, 36, 826, 34], [797, 10, 827, 8, "setProcessingState"], [797, 28, 827, 26], [797, 29, 827, 27], [797, 40, 827, 38], [797, 41, 827, 39], [798, 10, 828, 8], [799, 10, 829, 8], [799, 16, 829, 14, "result"], [799, 22, 829, 20], [799, 25, 829, 23], [800, 12, 830, 10, "imageUrl"], [800, 20, 830, 18], [800, 22, 830, 20, "status"], [800, 28, 830, 26], [800, 29, 830, 27, "publicUrl"], [800, 38, 830, 36], [801, 12, 830, 38], [802, 12, 831, 10, "localUri"], [802, 20, 831, 18], [802, 22, 831, 20, "capturedPhoto"], [802, 35, 831, 33], [802, 39, 831, 37, "status"], [802, 45, 831, 43], [802, 46, 831, 44, "publicUrl"], [802, 55, 831, 53], [803, 12, 831, 55], [804, 12, 832, 10, "challengeCode"], [804, 25, 832, 23], [804, 27, 832, 25, "challengeCode"], [804, 40, 832, 38], [804, 44, 832, 42], [804, 46, 832, 44], [805, 12, 833, 10, "timestamp"], [805, 21, 833, 19], [806, 12, 834, 10, "processingStatus"], [806, 28, 834, 26], [806, 30, 834, 28], [807, 10, 835, 8], [807, 11, 835, 9], [808, 10, 836, 8, "console"], [808, 17, 836, 15], [808, 18, 836, 16, "log"], [808, 21, 836, 19], [808, 22, 836, 20], [808, 57, 836, 55], [808, 59, 836, 57, "result"], [808, 65, 836, 63], [808, 66, 836, 64], [809, 10, 837, 8, "onComplete"], [809, 20, 837, 18], [809, 21, 837, 19, "result"], [809, 27, 837, 25], [809, 28, 837, 26], [810, 10, 838, 8], [811, 8, 839, 6], [811, 9, 839, 7], [811, 15, 839, 13], [811, 19, 839, 17, "status"], [811, 25, 839, 23], [811, 26, 839, 24, "status"], [811, 32, 839, 30], [811, 37, 839, 35], [811, 45, 839, 43], [811, 47, 839, 45], [812, 10, 840, 8, "console"], [812, 17, 840, 15], [812, 18, 840, 16, "error"], [812, 23, 840, 21], [812, 24, 840, 22], [812, 60, 840, 58], [812, 62, 840, 60, "status"], [812, 68, 840, 66], [812, 69, 840, 67, "error"], [812, 74, 840, 72], [812, 75, 840, 73], [813, 10, 841, 8], [813, 16, 841, 14], [813, 20, 841, 18, "Error"], [813, 25, 841, 23], [813, 26, 841, 24, "status"], [813, 32, 841, 30], [813, 33, 841, 31, "error"], [813, 38, 841, 36], [813, 42, 841, 40], [813, 61, 841, 59], [813, 62, 841, 60], [814, 8, 842, 6], [814, 9, 842, 7], [814, 15, 842, 13], [815, 10, 843, 8], [816, 10, 844, 8], [816, 16, 844, 14, "progressValue"], [816, 29, 844, 27], [816, 32, 844, 30], [816, 34, 844, 32], [816, 37, 844, 36, "attempts"], [816, 45, 844, 44], [816, 48, 844, 47, "MAX_ATTEMPTS"], [816, 60, 844, 59], [816, 63, 844, 63], [816, 65, 844, 65], [817, 10, 845, 8, "console"], [817, 17, 845, 15], [817, 18, 845, 16, "log"], [817, 21, 845, 19], [817, 22, 845, 20], [817, 71, 845, 69, "progressValue"], [817, 84, 845, 82], [817, 87, 845, 85], [817, 88, 845, 86], [818, 10, 846, 8, "setProcessingProgress"], [818, 31, 846, 29], [818, 32, 846, 30, "progressValue"], [818, 45, 846, 43], [818, 46, 846, 44], [819, 10, 848, 8, "setTimeout"], [819, 20, 848, 18], [819, 21, 848, 19], [819, 27, 848, 25], [820, 12, 849, 10, "pollForCompletion"], [820, 29, 849, 27], [820, 30, 849, 28, "jobId"], [820, 35, 849, 33], [820, 37, 849, 35, "timestamp"], [820, 46, 849, 44], [820, 48, 849, 46, "attempts"], [820, 56, 849, 54], [820, 59, 849, 57], [820, 60, 849, 58], [820, 61, 849, 59], [821, 10, 850, 8], [821, 11, 850, 9], [821, 13, 850, 11, "POLL_INTERVAL"], [821, 26, 850, 24], [821, 27, 850, 25], [822, 8, 851, 6], [823, 6, 852, 4], [823, 7, 852, 5], [823, 8, 852, 6], [823, 15, 852, 13, "error"], [823, 20, 852, 18], [823, 22, 852, 20], [824, 8, 853, 6, "console"], [824, 15, 853, 13], [824, 16, 853, 14, "error"], [824, 21, 853, 19], [824, 22, 853, 20], [824, 54, 853, 52], [824, 56, 853, 54, "error"], [824, 61, 853, 59], [824, 62, 853, 60], [825, 8, 854, 6, "setErrorMessage"], [825, 23, 854, 21], [825, 24, 854, 22], [825, 62, 854, 60, "error"], [825, 67, 854, 65], [825, 68, 854, 66, "message"], [825, 75, 854, 73], [825, 77, 854, 75], [825, 78, 854, 76], [826, 8, 855, 6, "setProcessingState"], [826, 26, 855, 24], [826, 27, 855, 25], [826, 34, 855, 32], [826, 35, 855, 33], [827, 6, 856, 4], [828, 4, 857, 2], [828, 5, 857, 3], [829, 4, 858, 2], [830, 4, 859, 2], [830, 10, 859, 8, "getAuthToken"], [830, 22, 859, 20], [830, 25, 859, 23], [830, 31, 859, 23, "getAuthToken"], [830, 32, 859, 23], [830, 37, 859, 52], [831, 6, 860, 4], [832, 6, 861, 4], [833, 6, 862, 4], [833, 13, 862, 11], [833, 30, 862, 28], [834, 4, 863, 2], [834, 5, 863, 3], [836, 4, 865, 2], [837, 4, 866, 2], [837, 10, 866, 8, "retryCapture"], [837, 22, 866, 20], [837, 25, 866, 23], [837, 29, 866, 23, "useCallback"], [837, 47, 866, 34], [837, 49, 866, 35], [837, 55, 866, 41], [838, 6, 867, 4, "console"], [838, 13, 867, 11], [838, 14, 867, 12, "log"], [838, 17, 867, 15], [838, 18, 867, 16], [838, 55, 867, 53], [838, 56, 867, 54], [839, 6, 868, 4, "setProcessingState"], [839, 24, 868, 22], [839, 25, 868, 23], [839, 31, 868, 29], [839, 32, 868, 30], [840, 6, 869, 4, "setErrorMessage"], [840, 21, 869, 19], [840, 22, 869, 20], [840, 24, 869, 22], [840, 25, 869, 23], [841, 6, 870, 4, "setCapturedPhoto"], [841, 22, 870, 20], [841, 23, 870, 21], [841, 25, 870, 23], [841, 26, 870, 24], [842, 6, 871, 4, "setProcessingProgress"], [842, 27, 871, 25], [842, 28, 871, 26], [842, 29, 871, 27], [842, 30, 871, 28], [843, 4, 872, 2], [843, 5, 872, 3], [843, 7, 872, 5], [843, 9, 872, 7], [843, 10, 872, 8], [844, 4, 873, 2], [845, 4, 874, 2], [845, 8, 874, 2, "useEffect"], [845, 24, 874, 11], [845, 26, 874, 12], [845, 32, 874, 18], [846, 6, 875, 4, "console"], [846, 13, 875, 11], [846, 14, 875, 12, "log"], [846, 17, 875, 15], [846, 18, 875, 16], [846, 53, 875, 51], [846, 55, 875, 53, "permission"], [846, 65, 875, 63], [846, 66, 875, 64], [847, 6, 876, 4], [847, 10, 876, 8, "permission"], [847, 20, 876, 18], [847, 22, 876, 20], [848, 8, 877, 6, "console"], [848, 15, 877, 13], [848, 16, 877, 14, "log"], [848, 19, 877, 17], [848, 20, 877, 18], [848, 57, 877, 55], [848, 59, 877, 57, "permission"], [848, 69, 877, 67], [848, 70, 877, 68, "granted"], [848, 77, 877, 75], [848, 78, 877, 76], [849, 6, 878, 4], [850, 4, 879, 2], [850, 5, 879, 3], [850, 7, 879, 5], [850, 8, 879, 6, "permission"], [850, 18, 879, 16], [850, 19, 879, 17], [850, 20, 879, 18], [851, 4, 880, 2], [852, 4, 881, 2], [852, 8, 881, 6], [852, 9, 881, 7, "permission"], [852, 19, 881, 17], [852, 21, 881, 19], [853, 6, 882, 4, "console"], [853, 13, 882, 11], [853, 14, 882, 12, "log"], [853, 17, 882, 15], [853, 18, 882, 16], [853, 67, 882, 65], [853, 68, 882, 66], [854, 6, 883, 4], [854, 26, 884, 6], [854, 30, 884, 6, "_jsxDevRuntime"], [854, 44, 884, 6], [854, 45, 884, 6, "jsxDEV"], [854, 51, 884, 6], [854, 53, 884, 7, "_View"], [854, 58, 884, 7], [854, 59, 884, 7, "default"], [854, 66, 884, 11], [855, 8, 884, 12, "style"], [855, 13, 884, 17], [855, 15, 884, 19, "styles"], [855, 21, 884, 25], [855, 22, 884, 26, "container"], [855, 31, 884, 36], [856, 8, 884, 36, "children"], [856, 16, 884, 36], [856, 32, 885, 8], [856, 36, 885, 8, "_jsxDevRuntime"], [856, 50, 885, 8], [856, 51, 885, 8, "jsxDEV"], [856, 57, 885, 8], [856, 59, 885, 9, "_ActivityIndicator"], [856, 77, 885, 9], [856, 78, 885, 9, "default"], [856, 85, 885, 26], [857, 10, 885, 27, "size"], [857, 14, 885, 31], [857, 16, 885, 32], [857, 23, 885, 39], [858, 10, 885, 40, "color"], [858, 15, 885, 45], [858, 17, 885, 46], [859, 8, 885, 55], [860, 10, 885, 55, "fileName"], [860, 18, 885, 55], [860, 20, 885, 55, "_jsxFileName"], [860, 32, 885, 55], [861, 10, 885, 55, "lineNumber"], [861, 20, 885, 55], [862, 10, 885, 55, "columnNumber"], [862, 22, 885, 55], [863, 8, 885, 55], [863, 15, 885, 57], [863, 16, 885, 58], [863, 31, 886, 8], [863, 35, 886, 8, "_jsxDevRuntime"], [863, 49, 886, 8], [863, 50, 886, 8, "jsxDEV"], [863, 56, 886, 8], [863, 58, 886, 9, "_Text"], [863, 63, 886, 9], [863, 64, 886, 9, "default"], [863, 71, 886, 13], [864, 10, 886, 14, "style"], [864, 15, 886, 19], [864, 17, 886, 21, "styles"], [864, 23, 886, 27], [864, 24, 886, 28, "loadingText"], [864, 35, 886, 40], [865, 10, 886, 40, "children"], [865, 18, 886, 40], [865, 20, 886, 41], [866, 8, 886, 58], [867, 10, 886, 58, "fileName"], [867, 18, 886, 58], [867, 20, 886, 58, "_jsxFileName"], [867, 32, 886, 58], [868, 10, 886, 58, "lineNumber"], [868, 20, 886, 58], [869, 10, 886, 58, "columnNumber"], [869, 22, 886, 58], [870, 8, 886, 58], [870, 15, 886, 64], [870, 16, 886, 65], [871, 6, 886, 65], [872, 8, 886, 65, "fileName"], [872, 16, 886, 65], [872, 18, 886, 65, "_jsxFileName"], [872, 30, 886, 65], [873, 8, 886, 65, "lineNumber"], [873, 18, 886, 65], [874, 8, 886, 65, "columnNumber"], [874, 20, 886, 65], [875, 6, 886, 65], [875, 13, 887, 12], [875, 14, 887, 13], [876, 4, 889, 2], [877, 4, 890, 2], [877, 8, 890, 6], [877, 9, 890, 7, "permission"], [877, 19, 890, 17], [877, 20, 890, 18, "granted"], [877, 27, 890, 25], [877, 29, 890, 27], [878, 6, 891, 4, "console"], [878, 13, 891, 11], [878, 14, 891, 12, "log"], [878, 17, 891, 15], [878, 18, 891, 16], [878, 93, 891, 91], [878, 94, 891, 92], [879, 6, 892, 4], [879, 26, 893, 6], [879, 30, 893, 6, "_jsxDevRuntime"], [879, 44, 893, 6], [879, 45, 893, 6, "jsxDEV"], [879, 51, 893, 6], [879, 53, 893, 7, "_View"], [879, 58, 893, 7], [879, 59, 893, 7, "default"], [879, 66, 893, 11], [880, 8, 893, 12, "style"], [880, 13, 893, 17], [880, 15, 893, 19, "styles"], [880, 21, 893, 25], [880, 22, 893, 26, "container"], [880, 31, 893, 36], [881, 8, 893, 36, "children"], [881, 16, 893, 36], [881, 31, 894, 8], [881, 35, 894, 8, "_jsxDevRuntime"], [881, 49, 894, 8], [881, 50, 894, 8, "jsxDEV"], [881, 56, 894, 8], [881, 58, 894, 9, "_View"], [881, 63, 894, 9], [881, 64, 894, 9, "default"], [881, 71, 894, 13], [882, 10, 894, 14, "style"], [882, 15, 894, 19], [882, 17, 894, 21, "styles"], [882, 23, 894, 27], [882, 24, 894, 28, "permissionContent"], [882, 41, 894, 46], [883, 10, 894, 46, "children"], [883, 18, 894, 46], [883, 34, 895, 10], [883, 38, 895, 10, "_jsxDevRuntime"], [883, 52, 895, 10], [883, 53, 895, 10, "jsxDEV"], [883, 59, 895, 10], [883, 61, 895, 11, "_lucideReactNative"], [883, 79, 895, 11], [883, 80, 895, 11, "Camera"], [883, 86, 895, 21], [884, 12, 895, 22, "size"], [884, 16, 895, 26], [884, 18, 895, 28], [884, 20, 895, 31], [885, 12, 895, 32, "color"], [885, 17, 895, 37], [885, 19, 895, 38], [886, 10, 895, 47], [887, 12, 895, 47, "fileName"], [887, 20, 895, 47], [887, 22, 895, 47, "_jsxFileName"], [887, 34, 895, 47], [888, 12, 895, 47, "lineNumber"], [888, 22, 895, 47], [889, 12, 895, 47, "columnNumber"], [889, 24, 895, 47], [890, 10, 895, 47], [890, 17, 895, 49], [890, 18, 895, 50], [890, 33, 896, 10], [890, 37, 896, 10, "_jsxDevRuntime"], [890, 51, 896, 10], [890, 52, 896, 10, "jsxDEV"], [890, 58, 896, 10], [890, 60, 896, 11, "_Text"], [890, 65, 896, 11], [890, 66, 896, 11, "default"], [890, 73, 896, 15], [891, 12, 896, 16, "style"], [891, 17, 896, 21], [891, 19, 896, 23, "styles"], [891, 25, 896, 29], [891, 26, 896, 30, "permissionTitle"], [891, 41, 896, 46], [892, 12, 896, 46, "children"], [892, 20, 896, 46], [892, 22, 896, 47], [893, 10, 896, 73], [894, 12, 896, 73, "fileName"], [894, 20, 896, 73], [894, 22, 896, 73, "_jsxFileName"], [894, 34, 896, 73], [895, 12, 896, 73, "lineNumber"], [895, 22, 896, 73], [896, 12, 896, 73, "columnNumber"], [896, 24, 896, 73], [897, 10, 896, 73], [897, 17, 896, 79], [897, 18, 896, 80], [897, 33, 897, 10], [897, 37, 897, 10, "_jsxDevRuntime"], [897, 51, 897, 10], [897, 52, 897, 10, "jsxDEV"], [897, 58, 897, 10], [897, 60, 897, 11, "_Text"], [897, 65, 897, 11], [897, 66, 897, 11, "default"], [897, 73, 897, 15], [898, 12, 897, 16, "style"], [898, 17, 897, 21], [898, 19, 897, 23, "styles"], [898, 25, 897, 29], [898, 26, 897, 30, "permissionDescription"], [898, 47, 897, 52], [899, 12, 897, 52, "children"], [899, 20, 897, 52], [899, 22, 897, 53], [900, 10, 900, 10], [901, 12, 900, 10, "fileName"], [901, 20, 900, 10], [901, 22, 900, 10, "_jsxFileName"], [901, 34, 900, 10], [902, 12, 900, 10, "lineNumber"], [902, 22, 900, 10], [903, 12, 900, 10, "columnNumber"], [903, 24, 900, 10], [904, 10, 900, 10], [904, 17, 900, 16], [904, 18, 900, 17], [904, 33, 901, 10], [904, 37, 901, 10, "_jsxDevRuntime"], [904, 51, 901, 10], [904, 52, 901, 10, "jsxDEV"], [904, 58, 901, 10], [904, 60, 901, 11, "_TouchableOpacity"], [904, 77, 901, 11], [904, 78, 901, 11, "default"], [904, 85, 901, 27], [905, 12, 901, 28, "onPress"], [905, 19, 901, 35], [905, 21, 901, 37, "requestPermission"], [905, 38, 901, 55], [906, 12, 901, 56, "style"], [906, 17, 901, 61], [906, 19, 901, 63, "styles"], [906, 25, 901, 69], [906, 26, 901, 70, "primaryButton"], [906, 39, 901, 84], [907, 12, 901, 84, "children"], [907, 20, 901, 84], [907, 35, 902, 12], [907, 39, 902, 12, "_jsxDevRuntime"], [907, 53, 902, 12], [907, 54, 902, 12, "jsxDEV"], [907, 60, 902, 12], [907, 62, 902, 13, "_Text"], [907, 67, 902, 13], [907, 68, 902, 13, "default"], [907, 75, 902, 17], [908, 14, 902, 18, "style"], [908, 19, 902, 23], [908, 21, 902, 25, "styles"], [908, 27, 902, 31], [908, 28, 902, 32, "primaryButtonText"], [908, 45, 902, 50], [909, 14, 902, 50, "children"], [909, 22, 902, 50], [909, 24, 902, 51], [910, 12, 902, 67], [911, 14, 902, 67, "fileName"], [911, 22, 902, 67], [911, 24, 902, 67, "_jsxFileName"], [911, 36, 902, 67], [912, 14, 902, 67, "lineNumber"], [912, 24, 902, 67], [913, 14, 902, 67, "columnNumber"], [913, 26, 902, 67], [914, 12, 902, 67], [914, 19, 902, 73], [915, 10, 902, 74], [916, 12, 902, 74, "fileName"], [916, 20, 902, 74], [916, 22, 902, 74, "_jsxFileName"], [916, 34, 902, 74], [917, 12, 902, 74, "lineNumber"], [917, 22, 902, 74], [918, 12, 902, 74, "columnNumber"], [918, 24, 902, 74], [919, 10, 902, 74], [919, 17, 903, 28], [919, 18, 903, 29], [919, 33, 904, 10], [919, 37, 904, 10, "_jsxDevRuntime"], [919, 51, 904, 10], [919, 52, 904, 10, "jsxDEV"], [919, 58, 904, 10], [919, 60, 904, 11, "_TouchableOpacity"], [919, 77, 904, 11], [919, 78, 904, 11, "default"], [919, 85, 904, 27], [920, 12, 904, 28, "onPress"], [920, 19, 904, 35], [920, 21, 904, 37, "onCancel"], [920, 29, 904, 46], [921, 12, 904, 47, "style"], [921, 17, 904, 52], [921, 19, 904, 54, "styles"], [921, 25, 904, 60], [921, 26, 904, 61, "secondaryButton"], [921, 41, 904, 77], [922, 12, 904, 77, "children"], [922, 20, 904, 77], [922, 35, 905, 12], [922, 39, 905, 12, "_jsxDevRuntime"], [922, 53, 905, 12], [922, 54, 905, 12, "jsxDEV"], [922, 60, 905, 12], [922, 62, 905, 13, "_Text"], [922, 67, 905, 13], [922, 68, 905, 13, "default"], [922, 75, 905, 17], [923, 14, 905, 18, "style"], [923, 19, 905, 23], [923, 21, 905, 25, "styles"], [923, 27, 905, 31], [923, 28, 905, 32, "secondaryButtonText"], [923, 47, 905, 52], [924, 14, 905, 52, "children"], [924, 22, 905, 52], [924, 24, 905, 53], [925, 12, 905, 59], [926, 14, 905, 59, "fileName"], [926, 22, 905, 59], [926, 24, 905, 59, "_jsxFileName"], [926, 36, 905, 59], [927, 14, 905, 59, "lineNumber"], [927, 24, 905, 59], [928, 14, 905, 59, "columnNumber"], [928, 26, 905, 59], [929, 12, 905, 59], [929, 19, 905, 65], [930, 10, 905, 66], [931, 12, 905, 66, "fileName"], [931, 20, 905, 66], [931, 22, 905, 66, "_jsxFileName"], [931, 34, 905, 66], [932, 12, 905, 66, "lineNumber"], [932, 22, 905, 66], [933, 12, 905, 66, "columnNumber"], [933, 24, 905, 66], [934, 10, 905, 66], [934, 17, 906, 28], [934, 18, 906, 29], [935, 8, 906, 29], [936, 10, 906, 29, "fileName"], [936, 18, 906, 29], [936, 20, 906, 29, "_jsxFileName"], [936, 32, 906, 29], [937, 10, 906, 29, "lineNumber"], [937, 20, 906, 29], [938, 10, 906, 29, "columnNumber"], [938, 22, 906, 29], [939, 8, 906, 29], [939, 15, 907, 14], [940, 6, 907, 15], [941, 8, 907, 15, "fileName"], [941, 16, 907, 15], [941, 18, 907, 15, "_jsxFileName"], [941, 30, 907, 15], [942, 8, 907, 15, "lineNumber"], [942, 18, 907, 15], [943, 8, 907, 15, "columnNumber"], [943, 20, 907, 15], [944, 6, 907, 15], [944, 13, 908, 12], [944, 14, 908, 13], [945, 4, 910, 2], [946, 4, 911, 2], [947, 4, 912, 2, "console"], [947, 11, 912, 9], [947, 12, 912, 10, "log"], [947, 15, 912, 13], [947, 16, 912, 14], [947, 55, 912, 53], [947, 56, 912, 54], [948, 4, 914, 2], [948, 24, 915, 4], [948, 28, 915, 4, "_jsxDevRuntime"], [948, 42, 915, 4], [948, 43, 915, 4, "jsxDEV"], [948, 49, 915, 4], [948, 51, 915, 5, "_View"], [948, 56, 915, 5], [948, 57, 915, 5, "default"], [948, 64, 915, 9], [949, 6, 915, 10, "style"], [949, 11, 915, 15], [949, 13, 915, 17, "styles"], [949, 19, 915, 23], [949, 20, 915, 24, "container"], [949, 29, 915, 34], [950, 6, 915, 34, "children"], [950, 14, 915, 34], [950, 30, 917, 6], [950, 34, 917, 6, "_jsxDevRuntime"], [950, 48, 917, 6], [950, 49, 917, 6, "jsxDEV"], [950, 55, 917, 6], [950, 57, 917, 7, "_View"], [950, 62, 917, 7], [950, 63, 917, 7, "default"], [950, 70, 917, 11], [951, 8, 917, 12, "style"], [951, 13, 917, 17], [951, 15, 917, 19, "styles"], [951, 21, 917, 25], [951, 22, 917, 26, "cameraContainer"], [951, 37, 917, 42], [952, 8, 917, 43, "id"], [952, 10, 917, 45], [952, 12, 917, 46], [952, 29, 917, 63], [953, 8, 917, 63, "children"], [953, 16, 917, 63], [953, 32, 918, 8], [953, 36, 918, 8, "_jsxDevRuntime"], [953, 50, 918, 8], [953, 51, 918, 8, "jsxDEV"], [953, 57, 918, 8], [953, 59, 918, 9, "_expoCamera"], [953, 70, 918, 9], [953, 71, 918, 9, "CameraView"], [953, 81, 918, 19], [954, 10, 919, 10, "ref"], [954, 13, 919, 13], [954, 15, 919, 15, "cameraRef"], [954, 24, 919, 25], [955, 10, 920, 10, "style"], [955, 15, 920, 15], [955, 17, 920, 17], [955, 18, 920, 18, "styles"], [955, 24, 920, 24], [955, 25, 920, 25, "camera"], [955, 31, 920, 31], [955, 33, 920, 33], [956, 12, 920, 35, "backgroundColor"], [956, 27, 920, 50], [956, 29, 920, 52], [957, 10, 920, 62], [957, 11, 920, 63], [957, 12, 920, 65], [958, 10, 921, 10, "facing"], [958, 16, 921, 16], [958, 18, 921, 17], [958, 24, 921, 23], [959, 10, 922, 10, "onLayout"], [959, 18, 922, 18], [959, 20, 922, 21, "e"], [959, 21, 922, 22], [959, 25, 922, 27], [960, 12, 923, 12, "console"], [960, 19, 923, 19], [960, 20, 923, 20, "log"], [960, 23, 923, 23], [960, 24, 923, 24], [960, 56, 923, 56], [960, 58, 923, 58, "e"], [960, 59, 923, 59], [960, 60, 923, 60, "nativeEvent"], [960, 71, 923, 71], [960, 72, 923, 72, "layout"], [960, 78, 923, 78], [960, 79, 923, 79], [961, 12, 924, 12, "setViewSize"], [961, 23, 924, 23], [961, 24, 924, 24], [962, 14, 924, 26, "width"], [962, 19, 924, 31], [962, 21, 924, 33, "e"], [962, 22, 924, 34], [962, 23, 924, 35, "nativeEvent"], [962, 34, 924, 46], [962, 35, 924, 47, "layout"], [962, 41, 924, 53], [962, 42, 924, 54, "width"], [962, 47, 924, 59], [963, 14, 924, 61, "height"], [963, 20, 924, 67], [963, 22, 924, 69, "e"], [963, 23, 924, 70], [963, 24, 924, 71, "nativeEvent"], [963, 35, 924, 82], [963, 36, 924, 83, "layout"], [963, 42, 924, 89], [963, 43, 924, 90, "height"], [964, 12, 924, 97], [964, 13, 924, 98], [964, 14, 924, 99], [965, 10, 925, 10], [965, 11, 925, 12], [966, 10, 926, 10, "onCameraReady"], [966, 23, 926, 23], [966, 25, 926, 25, "onCameraReady"], [966, 26, 926, 25], [966, 31, 926, 31], [967, 12, 927, 12, "console"], [967, 19, 927, 19], [967, 20, 927, 20, "log"], [967, 23, 927, 23], [967, 24, 927, 24], [967, 55, 927, 55], [967, 56, 927, 56], [968, 12, 928, 12, "setIsCameraReady"], [968, 28, 928, 28], [968, 29, 928, 29], [968, 33, 928, 33], [968, 34, 928, 34], [968, 35, 928, 35], [968, 36, 928, 36], [969, 10, 929, 10], [969, 11, 929, 12], [970, 10, 930, 10, "onMountError"], [970, 22, 930, 22], [970, 24, 930, 25, "error"], [970, 29, 930, 30], [970, 33, 930, 35], [971, 12, 931, 12, "console"], [971, 19, 931, 19], [971, 20, 931, 20, "error"], [971, 25, 931, 25], [971, 26, 931, 26], [971, 63, 931, 63], [971, 65, 931, 65, "error"], [971, 70, 931, 70], [971, 71, 931, 71], [972, 12, 932, 12, "setErrorMessage"], [972, 27, 932, 27], [972, 28, 932, 28], [972, 57, 932, 57], [972, 58, 932, 58], [973, 12, 933, 12, "setProcessingState"], [973, 30, 933, 30], [973, 31, 933, 31], [973, 38, 933, 38], [973, 39, 933, 39], [974, 10, 934, 10], [975, 8, 934, 12], [976, 10, 934, 12, "fileName"], [976, 18, 934, 12], [976, 20, 934, 12, "_jsxFileName"], [976, 32, 934, 12], [977, 10, 934, 12, "lineNumber"], [977, 20, 934, 12], [978, 10, 934, 12, "columnNumber"], [978, 22, 934, 12], [979, 8, 934, 12], [979, 15, 935, 9], [979, 16, 935, 10], [979, 18, 937, 9], [979, 19, 937, 10, "isCameraReady"], [979, 32, 937, 23], [979, 49, 938, 10], [979, 53, 938, 10, "_jsxDevRuntime"], [979, 67, 938, 10], [979, 68, 938, 10, "jsxDEV"], [979, 74, 938, 10], [979, 76, 938, 11, "_View"], [979, 81, 938, 11], [979, 82, 938, 11, "default"], [979, 89, 938, 15], [980, 10, 938, 16, "style"], [980, 15, 938, 21], [980, 17, 938, 23], [980, 18, 938, 24, "StyleSheet"], [980, 37, 938, 34], [980, 38, 938, 35, "absoluteFill"], [980, 50, 938, 47], [980, 52, 938, 49], [981, 12, 938, 51, "backgroundColor"], [981, 27, 938, 66], [981, 29, 938, 68], [981, 49, 938, 88], [982, 12, 938, 90, "justifyContent"], [982, 26, 938, 104], [982, 28, 938, 106], [982, 36, 938, 114], [983, 12, 938, 116, "alignItems"], [983, 22, 938, 126], [983, 24, 938, 128], [983, 32, 938, 136], [984, 12, 938, 138, "zIndex"], [984, 18, 938, 144], [984, 20, 938, 146], [985, 10, 938, 151], [985, 11, 938, 152], [985, 12, 938, 154], [986, 10, 938, 154, "children"], [986, 18, 938, 154], [986, 33, 939, 12], [986, 37, 939, 12, "_jsxDevRuntime"], [986, 51, 939, 12], [986, 52, 939, 12, "jsxDEV"], [986, 58, 939, 12], [986, 60, 939, 13, "_View"], [986, 65, 939, 13], [986, 66, 939, 13, "default"], [986, 73, 939, 17], [987, 12, 939, 18, "style"], [987, 17, 939, 23], [987, 19, 939, 25], [988, 14, 939, 27, "backgroundColor"], [988, 29, 939, 42], [988, 31, 939, 44], [988, 51, 939, 64], [989, 14, 939, 66, "padding"], [989, 21, 939, 73], [989, 23, 939, 75], [989, 25, 939, 77], [990, 14, 939, 79, "borderRadius"], [990, 26, 939, 91], [990, 28, 939, 93], [990, 30, 939, 95], [991, 14, 939, 97, "alignItems"], [991, 24, 939, 107], [991, 26, 939, 109], [992, 12, 939, 118], [992, 13, 939, 120], [993, 12, 939, 120, "children"], [993, 20, 939, 120], [993, 36, 940, 14], [993, 40, 940, 14, "_jsxDevRuntime"], [993, 54, 940, 14], [993, 55, 940, 14, "jsxDEV"], [993, 61, 940, 14], [993, 63, 940, 15, "_ActivityIndicator"], [993, 81, 940, 15], [993, 82, 940, 15, "default"], [993, 89, 940, 32], [994, 14, 940, 33, "size"], [994, 18, 940, 37], [994, 20, 940, 38], [994, 27, 940, 45], [995, 14, 940, 46, "color"], [995, 19, 940, 51], [995, 21, 940, 52], [995, 30, 940, 61], [996, 14, 940, 62, "style"], [996, 19, 940, 67], [996, 21, 940, 69], [997, 16, 940, 71, "marginBottom"], [997, 28, 940, 83], [997, 30, 940, 85], [998, 14, 940, 88], [999, 12, 940, 90], [1000, 14, 940, 90, "fileName"], [1000, 22, 940, 90], [1000, 24, 940, 90, "_jsxFileName"], [1000, 36, 940, 90], [1001, 14, 940, 90, "lineNumber"], [1001, 24, 940, 90], [1002, 14, 940, 90, "columnNumber"], [1002, 26, 940, 90], [1003, 12, 940, 90], [1003, 19, 940, 92], [1003, 20, 940, 93], [1003, 35, 941, 14], [1003, 39, 941, 14, "_jsxDevRuntime"], [1003, 53, 941, 14], [1003, 54, 941, 14, "jsxDEV"], [1003, 60, 941, 14], [1003, 62, 941, 15, "_Text"], [1003, 67, 941, 15], [1003, 68, 941, 15, "default"], [1003, 75, 941, 19], [1004, 14, 941, 20, "style"], [1004, 19, 941, 25], [1004, 21, 941, 27], [1005, 16, 941, 29, "color"], [1005, 21, 941, 34], [1005, 23, 941, 36], [1005, 29, 941, 42], [1006, 16, 941, 44, "fontSize"], [1006, 24, 941, 52], [1006, 26, 941, 54], [1006, 28, 941, 56], [1007, 16, 941, 58, "fontWeight"], [1007, 26, 941, 68], [1007, 28, 941, 70], [1008, 14, 941, 76], [1008, 15, 941, 78], [1009, 14, 941, 78, "children"], [1009, 22, 941, 78], [1009, 24, 941, 79], [1010, 12, 941, 101], [1011, 14, 941, 101, "fileName"], [1011, 22, 941, 101], [1011, 24, 941, 101, "_jsxFileName"], [1011, 36, 941, 101], [1012, 14, 941, 101, "lineNumber"], [1012, 24, 941, 101], [1013, 14, 941, 101, "columnNumber"], [1013, 26, 941, 101], [1014, 12, 941, 101], [1014, 19, 941, 107], [1014, 20, 941, 108], [1014, 35, 942, 14], [1014, 39, 942, 14, "_jsxDevRuntime"], [1014, 53, 942, 14], [1014, 54, 942, 14, "jsxDEV"], [1014, 60, 942, 14], [1014, 62, 942, 15, "_Text"], [1014, 67, 942, 15], [1014, 68, 942, 15, "default"], [1014, 75, 942, 19], [1015, 14, 942, 20, "style"], [1015, 19, 942, 25], [1015, 21, 942, 27], [1016, 16, 942, 29, "color"], [1016, 21, 942, 34], [1016, 23, 942, 36], [1016, 32, 942, 45], [1017, 16, 942, 47, "fontSize"], [1017, 24, 942, 55], [1017, 26, 942, 57], [1017, 28, 942, 59], [1018, 16, 942, 61, "marginTop"], [1018, 25, 942, 70], [1018, 27, 942, 72], [1019, 14, 942, 74], [1019, 15, 942, 76], [1020, 14, 942, 76, "children"], [1020, 22, 942, 76], [1020, 24, 942, 77], [1021, 12, 942, 88], [1022, 14, 942, 88, "fileName"], [1022, 22, 942, 88], [1022, 24, 942, 88, "_jsxFileName"], [1022, 36, 942, 88], [1023, 14, 942, 88, "lineNumber"], [1023, 24, 942, 88], [1024, 14, 942, 88, "columnNumber"], [1024, 26, 942, 88], [1025, 12, 942, 88], [1025, 19, 942, 94], [1025, 20, 942, 95], [1026, 10, 942, 95], [1027, 12, 942, 95, "fileName"], [1027, 20, 942, 95], [1027, 22, 942, 95, "_jsxFileName"], [1027, 34, 942, 95], [1028, 12, 942, 95, "lineNumber"], [1028, 22, 942, 95], [1029, 12, 942, 95, "columnNumber"], [1029, 24, 942, 95], [1030, 10, 942, 95], [1030, 17, 943, 18], [1031, 8, 943, 19], [1032, 10, 943, 19, "fileName"], [1032, 18, 943, 19], [1032, 20, 943, 19, "_jsxFileName"], [1032, 32, 943, 19], [1033, 10, 943, 19, "lineNumber"], [1033, 20, 943, 19], [1034, 10, 943, 19, "columnNumber"], [1034, 22, 943, 19], [1035, 8, 943, 19], [1035, 15, 944, 16], [1035, 16, 945, 9], [1035, 18, 948, 9, "isCameraReady"], [1035, 31, 948, 22], [1035, 35, 948, 26, "previewBlurEnabled"], [1035, 53, 948, 44], [1035, 57, 948, 48, "viewSize"], [1035, 65, 948, 56], [1035, 66, 948, 57, "width"], [1035, 71, 948, 62], [1035, 74, 948, 65], [1035, 75, 948, 66], [1035, 92, 949, 10], [1035, 96, 949, 10, "_jsxDevRuntime"], [1035, 110, 949, 10], [1035, 111, 949, 10, "jsxDEV"], [1035, 117, 949, 10], [1035, 119, 949, 10, "_jsxDevRuntime"], [1035, 133, 949, 10], [1035, 134, 949, 10, "Fragment"], [1035, 142, 949, 10], [1036, 10, 949, 10, "children"], [1036, 18, 949, 10], [1036, 34, 951, 12], [1036, 38, 951, 12, "_jsxDevRuntime"], [1036, 52, 951, 12], [1036, 53, 951, 12, "jsxDEV"], [1036, 59, 951, 12], [1036, 61, 951, 13, "_LiveFaceCanvas"], [1036, 76, 951, 13], [1036, 77, 951, 13, "default"], [1036, 84, 951, 27], [1037, 12, 951, 28, "containerId"], [1037, 23, 951, 39], [1037, 25, 951, 40], [1037, 42, 951, 57], [1038, 12, 951, 58, "width"], [1038, 17, 951, 63], [1038, 19, 951, 65, "viewSize"], [1038, 27, 951, 73], [1038, 28, 951, 74, "width"], [1038, 33, 951, 80], [1039, 12, 951, 81, "height"], [1039, 18, 951, 87], [1039, 20, 951, 89, "viewSize"], [1039, 28, 951, 97], [1039, 29, 951, 98, "height"], [1040, 10, 951, 105], [1041, 12, 951, 105, "fileName"], [1041, 20, 951, 105], [1041, 22, 951, 105, "_jsxFileName"], [1041, 34, 951, 105], [1042, 12, 951, 105, "lineNumber"], [1042, 22, 951, 105], [1043, 12, 951, 105, "columnNumber"], [1043, 24, 951, 105], [1044, 10, 951, 105], [1044, 17, 951, 107], [1044, 18, 951, 108], [1044, 33, 952, 12], [1044, 37, 952, 12, "_jsxDevRuntime"], [1044, 51, 952, 12], [1044, 52, 952, 12, "jsxDEV"], [1044, 58, 952, 12], [1044, 60, 952, 13, "_View"], [1044, 65, 952, 13], [1044, 66, 952, 13, "default"], [1044, 73, 952, 17], [1045, 12, 952, 18, "style"], [1045, 17, 952, 23], [1045, 19, 952, 25], [1045, 20, 952, 26, "StyleSheet"], [1045, 39, 952, 36], [1045, 40, 952, 37, "absoluteFill"], [1045, 52, 952, 49], [1045, 54, 952, 51], [1046, 14, 952, 53, "pointerEvents"], [1046, 27, 952, 66], [1046, 29, 952, 68], [1047, 12, 952, 75], [1047, 13, 952, 76], [1047, 14, 952, 78], [1048, 12, 952, 78, "children"], [1048, 20, 952, 78], [1048, 36, 954, 12], [1048, 40, 954, 12, "_jsxDevRuntime"], [1048, 54, 954, 12], [1048, 55, 954, 12, "jsxDEV"], [1048, 61, 954, 12], [1048, 63, 954, 13, "_expoBlur"], [1048, 72, 954, 13], [1048, 73, 954, 13, "BlurView"], [1048, 81, 954, 21], [1049, 14, 954, 22, "intensity"], [1049, 23, 954, 31], [1049, 25, 954, 33], [1049, 27, 954, 36], [1050, 14, 954, 37, "tint"], [1050, 18, 954, 41], [1050, 20, 954, 42], [1050, 26, 954, 48], [1051, 14, 954, 49, "style"], [1051, 19, 954, 54], [1051, 21, 954, 56], [1051, 22, 954, 57, "styles"], [1051, 28, 954, 63], [1051, 29, 954, 64, "blurZone"], [1051, 37, 954, 72], [1051, 39, 954, 74], [1052, 16, 955, 14, "left"], [1052, 20, 955, 18], [1052, 22, 955, 20], [1052, 23, 955, 21], [1053, 16, 956, 14, "top"], [1053, 19, 956, 17], [1053, 21, 956, 19, "viewSize"], [1053, 29, 956, 27], [1053, 30, 956, 28, "height"], [1053, 36, 956, 34], [1053, 39, 956, 37], [1053, 42, 956, 40], [1054, 16, 957, 14, "width"], [1054, 21, 957, 19], [1054, 23, 957, 21, "viewSize"], [1054, 31, 957, 29], [1054, 32, 957, 30, "width"], [1054, 37, 957, 35], [1055, 16, 958, 14, "height"], [1055, 22, 958, 20], [1055, 24, 958, 22, "viewSize"], [1055, 32, 958, 30], [1055, 33, 958, 31, "height"], [1055, 39, 958, 37], [1055, 42, 958, 40], [1055, 46, 958, 44], [1056, 16, 959, 14, "borderRadius"], [1056, 28, 959, 26], [1056, 30, 959, 28], [1057, 14, 960, 12], [1057, 15, 960, 13], [1058, 12, 960, 15], [1059, 14, 960, 15, "fileName"], [1059, 22, 960, 15], [1059, 24, 960, 15, "_jsxFileName"], [1059, 36, 960, 15], [1060, 14, 960, 15, "lineNumber"], [1060, 24, 960, 15], [1061, 14, 960, 15, "columnNumber"], [1061, 26, 960, 15], [1062, 12, 960, 15], [1062, 19, 960, 17], [1062, 20, 960, 18], [1062, 35, 962, 12], [1062, 39, 962, 12, "_jsxDevRuntime"], [1062, 53, 962, 12], [1062, 54, 962, 12, "jsxDEV"], [1062, 60, 962, 12], [1062, 62, 962, 13, "_expoBlur"], [1062, 71, 962, 13], [1062, 72, 962, 13, "BlurView"], [1062, 80, 962, 21], [1063, 14, 962, 22, "intensity"], [1063, 23, 962, 31], [1063, 25, 962, 33], [1063, 27, 962, 36], [1064, 14, 962, 37, "tint"], [1064, 18, 962, 41], [1064, 20, 962, 42], [1064, 26, 962, 48], [1065, 14, 962, 49, "style"], [1065, 19, 962, 54], [1065, 21, 962, 56], [1065, 22, 962, 57, "styles"], [1065, 28, 962, 63], [1065, 29, 962, 64, "blurZone"], [1065, 37, 962, 72], [1065, 39, 962, 74], [1066, 16, 963, 14, "left"], [1066, 20, 963, 18], [1066, 22, 963, 20], [1066, 23, 963, 21], [1067, 16, 964, 14, "top"], [1067, 19, 964, 17], [1067, 21, 964, 19], [1067, 22, 964, 20], [1068, 16, 965, 14, "width"], [1068, 21, 965, 19], [1068, 23, 965, 21, "viewSize"], [1068, 31, 965, 29], [1068, 32, 965, 30, "width"], [1068, 37, 965, 35], [1069, 16, 966, 14, "height"], [1069, 22, 966, 20], [1069, 24, 966, 22, "viewSize"], [1069, 32, 966, 30], [1069, 33, 966, 31, "height"], [1069, 39, 966, 37], [1069, 42, 966, 40], [1069, 45, 966, 43], [1070, 16, 967, 14, "borderRadius"], [1070, 28, 967, 26], [1070, 30, 967, 28], [1071, 14, 968, 12], [1071, 15, 968, 13], [1072, 12, 968, 15], [1073, 14, 968, 15, "fileName"], [1073, 22, 968, 15], [1073, 24, 968, 15, "_jsxFileName"], [1073, 36, 968, 15], [1074, 14, 968, 15, "lineNumber"], [1074, 24, 968, 15], [1075, 14, 968, 15, "columnNumber"], [1075, 26, 968, 15], [1076, 12, 968, 15], [1076, 19, 968, 17], [1076, 20, 968, 18], [1076, 35, 970, 12], [1076, 39, 970, 12, "_jsxDevRuntime"], [1076, 53, 970, 12], [1076, 54, 970, 12, "jsxDEV"], [1076, 60, 970, 12], [1076, 62, 970, 13, "_expoBlur"], [1076, 71, 970, 13], [1076, 72, 970, 13, "BlurView"], [1076, 80, 970, 21], [1077, 14, 970, 22, "intensity"], [1077, 23, 970, 31], [1077, 25, 970, 33], [1077, 27, 970, 36], [1078, 14, 970, 37, "tint"], [1078, 18, 970, 41], [1078, 20, 970, 42], [1078, 26, 970, 48], [1079, 14, 970, 49, "style"], [1079, 19, 970, 54], [1079, 21, 970, 56], [1079, 22, 970, 57, "styles"], [1079, 28, 970, 63], [1079, 29, 970, 64, "blurZone"], [1079, 37, 970, 72], [1079, 39, 970, 74], [1080, 16, 971, 14, "left"], [1080, 20, 971, 18], [1080, 22, 971, 20, "viewSize"], [1080, 30, 971, 28], [1080, 31, 971, 29, "width"], [1080, 36, 971, 34], [1080, 39, 971, 37], [1080, 42, 971, 40], [1080, 45, 971, 44, "viewSize"], [1080, 53, 971, 52], [1080, 54, 971, 53, "width"], [1080, 59, 971, 58], [1080, 62, 971, 61], [1080, 66, 971, 66], [1081, 16, 972, 14, "top"], [1081, 19, 972, 17], [1081, 21, 972, 19, "viewSize"], [1081, 29, 972, 27], [1081, 30, 972, 28, "height"], [1081, 36, 972, 34], [1081, 39, 972, 37], [1081, 43, 972, 41], [1081, 46, 972, 45, "viewSize"], [1081, 54, 972, 53], [1081, 55, 972, 54, "width"], [1081, 60, 972, 59], [1081, 63, 972, 62], [1081, 67, 972, 67], [1082, 16, 973, 14, "width"], [1082, 21, 973, 19], [1082, 23, 973, 21, "viewSize"], [1082, 31, 973, 29], [1082, 32, 973, 30, "width"], [1082, 37, 973, 35], [1082, 40, 973, 38], [1082, 43, 973, 41], [1083, 16, 974, 14, "height"], [1083, 22, 974, 20], [1083, 24, 974, 22, "viewSize"], [1083, 32, 974, 30], [1083, 33, 974, 31, "width"], [1083, 38, 974, 36], [1083, 41, 974, 39], [1083, 44, 974, 42], [1084, 16, 975, 14, "borderRadius"], [1084, 28, 975, 26], [1084, 30, 975, 29, "viewSize"], [1084, 38, 975, 37], [1084, 39, 975, 38, "width"], [1084, 44, 975, 43], [1084, 47, 975, 46], [1084, 50, 975, 49], [1084, 53, 975, 53], [1085, 14, 976, 12], [1085, 15, 976, 13], [1086, 12, 976, 15], [1087, 14, 976, 15, "fileName"], [1087, 22, 976, 15], [1087, 24, 976, 15, "_jsxFileName"], [1087, 36, 976, 15], [1088, 14, 976, 15, "lineNumber"], [1088, 24, 976, 15], [1089, 14, 976, 15, "columnNumber"], [1089, 26, 976, 15], [1090, 12, 976, 15], [1090, 19, 976, 17], [1090, 20, 976, 18], [1090, 35, 977, 12], [1090, 39, 977, 12, "_jsxDevRuntime"], [1090, 53, 977, 12], [1090, 54, 977, 12, "jsxDEV"], [1090, 60, 977, 12], [1090, 62, 977, 13, "_expoBlur"], [1090, 71, 977, 13], [1090, 72, 977, 13, "BlurView"], [1090, 80, 977, 21], [1091, 14, 977, 22, "intensity"], [1091, 23, 977, 31], [1091, 25, 977, 33], [1091, 27, 977, 36], [1092, 14, 977, 37, "tint"], [1092, 18, 977, 41], [1092, 20, 977, 42], [1092, 26, 977, 48], [1093, 14, 977, 49, "style"], [1093, 19, 977, 54], [1093, 21, 977, 56], [1093, 22, 977, 57, "styles"], [1093, 28, 977, 63], [1093, 29, 977, 64, "blurZone"], [1093, 37, 977, 72], [1093, 39, 977, 74], [1094, 16, 978, 14, "left"], [1094, 20, 978, 18], [1094, 22, 978, 20, "viewSize"], [1094, 30, 978, 28], [1094, 31, 978, 29, "width"], [1094, 36, 978, 34], [1094, 39, 978, 37], [1094, 42, 978, 40], [1094, 45, 978, 44, "viewSize"], [1094, 53, 978, 52], [1094, 54, 978, 53, "width"], [1094, 59, 978, 58], [1094, 62, 978, 61], [1094, 66, 978, 66], [1095, 16, 979, 14, "top"], [1095, 19, 979, 17], [1095, 21, 979, 19, "viewSize"], [1095, 29, 979, 27], [1095, 30, 979, 28, "height"], [1095, 36, 979, 34], [1095, 39, 979, 37], [1095, 42, 979, 40], [1095, 45, 979, 44, "viewSize"], [1095, 53, 979, 52], [1095, 54, 979, 53, "width"], [1095, 59, 979, 58], [1095, 62, 979, 61], [1095, 66, 979, 66], [1096, 16, 980, 14, "width"], [1096, 21, 980, 19], [1096, 23, 980, 21, "viewSize"], [1096, 31, 980, 29], [1096, 32, 980, 30, "width"], [1096, 37, 980, 35], [1096, 40, 980, 38], [1096, 43, 980, 41], [1097, 16, 981, 14, "height"], [1097, 22, 981, 20], [1097, 24, 981, 22, "viewSize"], [1097, 32, 981, 30], [1097, 33, 981, 31, "width"], [1097, 38, 981, 36], [1097, 41, 981, 39], [1097, 44, 981, 42], [1098, 16, 982, 14, "borderRadius"], [1098, 28, 982, 26], [1098, 30, 982, 29, "viewSize"], [1098, 38, 982, 37], [1098, 39, 982, 38, "width"], [1098, 44, 982, 43], [1098, 47, 982, 46], [1098, 50, 982, 49], [1098, 53, 982, 53], [1099, 14, 983, 12], [1099, 15, 983, 13], [1100, 12, 983, 15], [1101, 14, 983, 15, "fileName"], [1101, 22, 983, 15], [1101, 24, 983, 15, "_jsxFileName"], [1101, 36, 983, 15], [1102, 14, 983, 15, "lineNumber"], [1102, 24, 983, 15], [1103, 14, 983, 15, "columnNumber"], [1103, 26, 983, 15], [1104, 12, 983, 15], [1104, 19, 983, 17], [1104, 20, 983, 18], [1104, 35, 984, 12], [1104, 39, 984, 12, "_jsxDevRuntime"], [1104, 53, 984, 12], [1104, 54, 984, 12, "jsxDEV"], [1104, 60, 984, 12], [1104, 62, 984, 13, "_expoBlur"], [1104, 71, 984, 13], [1104, 72, 984, 13, "BlurView"], [1104, 80, 984, 21], [1105, 14, 984, 22, "intensity"], [1105, 23, 984, 31], [1105, 25, 984, 33], [1105, 27, 984, 36], [1106, 14, 984, 37, "tint"], [1106, 18, 984, 41], [1106, 20, 984, 42], [1106, 26, 984, 48], [1107, 14, 984, 49, "style"], [1107, 19, 984, 54], [1107, 21, 984, 56], [1107, 22, 984, 57, "styles"], [1107, 28, 984, 63], [1107, 29, 984, 64, "blurZone"], [1107, 37, 984, 72], [1107, 39, 984, 74], [1108, 16, 985, 14, "left"], [1108, 20, 985, 18], [1108, 22, 985, 20, "viewSize"], [1108, 30, 985, 28], [1108, 31, 985, 29, "width"], [1108, 36, 985, 34], [1108, 39, 985, 37], [1108, 42, 985, 40], [1108, 45, 985, 44, "viewSize"], [1108, 53, 985, 52], [1108, 54, 985, 53, "width"], [1108, 59, 985, 58], [1108, 62, 985, 61], [1108, 66, 985, 66], [1109, 16, 986, 14, "top"], [1109, 19, 986, 17], [1109, 21, 986, 19, "viewSize"], [1109, 29, 986, 27], [1109, 30, 986, 28, "height"], [1109, 36, 986, 34], [1109, 39, 986, 37], [1109, 42, 986, 40], [1109, 45, 986, 44, "viewSize"], [1109, 53, 986, 52], [1109, 54, 986, 53, "width"], [1109, 59, 986, 58], [1109, 62, 986, 61], [1109, 66, 986, 66], [1110, 16, 987, 14, "width"], [1110, 21, 987, 19], [1110, 23, 987, 21, "viewSize"], [1110, 31, 987, 29], [1110, 32, 987, 30, "width"], [1110, 37, 987, 35], [1110, 40, 987, 38], [1110, 43, 987, 41], [1111, 16, 988, 14, "height"], [1111, 22, 988, 20], [1111, 24, 988, 22, "viewSize"], [1111, 32, 988, 30], [1111, 33, 988, 31, "width"], [1111, 38, 988, 36], [1111, 41, 988, 39], [1111, 44, 988, 42], [1112, 16, 989, 14, "borderRadius"], [1112, 28, 989, 26], [1112, 30, 989, 29, "viewSize"], [1112, 38, 989, 37], [1112, 39, 989, 38, "width"], [1112, 44, 989, 43], [1112, 47, 989, 46], [1112, 50, 989, 49], [1112, 53, 989, 53], [1113, 14, 990, 12], [1113, 15, 990, 13], [1114, 12, 990, 15], [1115, 14, 990, 15, "fileName"], [1115, 22, 990, 15], [1115, 24, 990, 15, "_jsxFileName"], [1115, 36, 990, 15], [1116, 14, 990, 15, "lineNumber"], [1116, 24, 990, 15], [1117, 14, 990, 15, "columnNumber"], [1117, 26, 990, 15], [1118, 12, 990, 15], [1118, 19, 990, 17], [1118, 20, 990, 18], [1118, 22, 992, 13, "__DEV__"], [1118, 29, 992, 20], [1118, 46, 993, 14], [1118, 50, 993, 14, "_jsxDevRuntime"], [1118, 64, 993, 14], [1118, 65, 993, 14, "jsxDEV"], [1118, 71, 993, 14], [1118, 73, 993, 15, "_View"], [1118, 78, 993, 15], [1118, 79, 993, 15, "default"], [1118, 86, 993, 19], [1119, 14, 993, 20, "style"], [1119, 19, 993, 25], [1119, 21, 993, 27, "styles"], [1119, 27, 993, 33], [1119, 28, 993, 34, "previewChip"], [1119, 39, 993, 46], [1120, 14, 993, 46, "children"], [1120, 22, 993, 46], [1120, 37, 994, 16], [1120, 41, 994, 16, "_jsxDevRuntime"], [1120, 55, 994, 16], [1120, 56, 994, 16, "jsxDEV"], [1120, 62, 994, 16], [1120, 64, 994, 17, "_Text"], [1120, 69, 994, 17], [1120, 70, 994, 17, "default"], [1120, 77, 994, 21], [1121, 16, 994, 22, "style"], [1121, 21, 994, 27], [1121, 23, 994, 29, "styles"], [1121, 29, 994, 35], [1121, 30, 994, 36, "previewChipText"], [1121, 45, 994, 52], [1122, 16, 994, 52, "children"], [1122, 24, 994, 52], [1122, 26, 994, 53], [1123, 14, 994, 73], [1124, 16, 994, 73, "fileName"], [1124, 24, 994, 73], [1124, 26, 994, 73, "_jsxFileName"], [1124, 38, 994, 73], [1125, 16, 994, 73, "lineNumber"], [1125, 26, 994, 73], [1126, 16, 994, 73, "columnNumber"], [1126, 28, 994, 73], [1127, 14, 994, 73], [1127, 21, 994, 79], [1128, 12, 994, 80], [1129, 14, 994, 80, "fileName"], [1129, 22, 994, 80], [1129, 24, 994, 80, "_jsxFileName"], [1129, 36, 994, 80], [1130, 14, 994, 80, "lineNumber"], [1130, 24, 994, 80], [1131, 14, 994, 80, "columnNumber"], [1131, 26, 994, 80], [1132, 12, 994, 80], [1132, 19, 995, 20], [1132, 20, 996, 13], [1133, 10, 996, 13], [1134, 12, 996, 13, "fileName"], [1134, 20, 996, 13], [1134, 22, 996, 13, "_jsxFileName"], [1134, 34, 996, 13], [1135, 12, 996, 13, "lineNumber"], [1135, 22, 996, 13], [1136, 12, 996, 13, "columnNumber"], [1136, 24, 996, 13], [1137, 10, 996, 13], [1137, 17, 997, 18], [1137, 18, 997, 19], [1138, 8, 997, 19], [1138, 23, 998, 12], [1138, 24, 999, 9], [1138, 26, 1001, 9, "isCameraReady"], [1138, 39, 1001, 22], [1138, 56, 1002, 10], [1138, 60, 1002, 10, "_jsxDevRuntime"], [1138, 74, 1002, 10], [1138, 75, 1002, 10, "jsxDEV"], [1138, 81, 1002, 10], [1138, 83, 1002, 10, "_jsxDevRuntime"], [1138, 97, 1002, 10], [1138, 98, 1002, 10, "Fragment"], [1138, 106, 1002, 10], [1139, 10, 1002, 10, "children"], [1139, 18, 1002, 10], [1139, 34, 1004, 12], [1139, 38, 1004, 12, "_jsxDevRuntime"], [1139, 52, 1004, 12], [1139, 53, 1004, 12, "jsxDEV"], [1139, 59, 1004, 12], [1139, 61, 1004, 13, "_View"], [1139, 66, 1004, 13], [1139, 67, 1004, 13, "default"], [1139, 74, 1004, 17], [1140, 12, 1004, 18, "style"], [1140, 17, 1004, 23], [1140, 19, 1004, 25, "styles"], [1140, 25, 1004, 31], [1140, 26, 1004, 32, "headerOverlay"], [1140, 39, 1004, 46], [1141, 12, 1004, 46, "children"], [1141, 20, 1004, 46], [1141, 35, 1005, 14], [1141, 39, 1005, 14, "_jsxDevRuntime"], [1141, 53, 1005, 14], [1141, 54, 1005, 14, "jsxDEV"], [1141, 60, 1005, 14], [1141, 62, 1005, 15, "_View"], [1141, 67, 1005, 15], [1141, 68, 1005, 15, "default"], [1141, 75, 1005, 19], [1142, 14, 1005, 20, "style"], [1142, 19, 1005, 25], [1142, 21, 1005, 27, "styles"], [1142, 27, 1005, 33], [1142, 28, 1005, 34, "headerContent"], [1142, 41, 1005, 48], [1143, 14, 1005, 48, "children"], [1143, 22, 1005, 48], [1143, 38, 1006, 16], [1143, 42, 1006, 16, "_jsxDevRuntime"], [1143, 56, 1006, 16], [1143, 57, 1006, 16, "jsxDEV"], [1143, 63, 1006, 16], [1143, 65, 1006, 17, "_View"], [1143, 70, 1006, 17], [1143, 71, 1006, 17, "default"], [1143, 78, 1006, 21], [1144, 16, 1006, 22, "style"], [1144, 21, 1006, 27], [1144, 23, 1006, 29, "styles"], [1144, 29, 1006, 35], [1144, 30, 1006, 36, "headerLeft"], [1144, 40, 1006, 47], [1145, 16, 1006, 47, "children"], [1145, 24, 1006, 47], [1145, 40, 1007, 18], [1145, 44, 1007, 18, "_jsxDevRuntime"], [1145, 58, 1007, 18], [1145, 59, 1007, 18, "jsxDEV"], [1145, 65, 1007, 18], [1145, 67, 1007, 19, "_Text"], [1145, 72, 1007, 19], [1145, 73, 1007, 19, "default"], [1145, 80, 1007, 23], [1146, 18, 1007, 24, "style"], [1146, 23, 1007, 29], [1146, 25, 1007, 31, "styles"], [1146, 31, 1007, 37], [1146, 32, 1007, 38, "headerTitle"], [1146, 43, 1007, 50], [1147, 18, 1007, 50, "children"], [1147, 26, 1007, 50], [1147, 28, 1007, 51], [1148, 16, 1007, 62], [1149, 18, 1007, 62, "fileName"], [1149, 26, 1007, 62], [1149, 28, 1007, 62, "_jsxFileName"], [1149, 40, 1007, 62], [1150, 18, 1007, 62, "lineNumber"], [1150, 28, 1007, 62], [1151, 18, 1007, 62, "columnNumber"], [1151, 30, 1007, 62], [1152, 16, 1007, 62], [1152, 23, 1007, 68], [1152, 24, 1007, 69], [1152, 39, 1008, 18], [1152, 43, 1008, 18, "_jsxDevRuntime"], [1152, 57, 1008, 18], [1152, 58, 1008, 18, "jsxDEV"], [1152, 64, 1008, 18], [1152, 66, 1008, 19, "_View"], [1152, 71, 1008, 19], [1152, 72, 1008, 19, "default"], [1152, 79, 1008, 23], [1153, 18, 1008, 24, "style"], [1153, 23, 1008, 29], [1153, 25, 1008, 31, "styles"], [1153, 31, 1008, 37], [1153, 32, 1008, 38, "subtitleRow"], [1153, 43, 1008, 50], [1154, 18, 1008, 50, "children"], [1154, 26, 1008, 50], [1154, 42, 1009, 20], [1154, 46, 1009, 20, "_jsxDevRuntime"], [1154, 60, 1009, 20], [1154, 61, 1009, 20, "jsxDEV"], [1154, 67, 1009, 20], [1154, 69, 1009, 21, "_Text"], [1154, 74, 1009, 21], [1154, 75, 1009, 21, "default"], [1154, 82, 1009, 25], [1155, 20, 1009, 26, "style"], [1155, 25, 1009, 31], [1155, 27, 1009, 33, "styles"], [1155, 33, 1009, 39], [1155, 34, 1009, 40, "webIcon"], [1155, 41, 1009, 48], [1156, 20, 1009, 48, "children"], [1156, 28, 1009, 48], [1156, 30, 1009, 49], [1157, 18, 1009, 51], [1158, 20, 1009, 51, "fileName"], [1158, 28, 1009, 51], [1158, 30, 1009, 51, "_jsxFileName"], [1158, 42, 1009, 51], [1159, 20, 1009, 51, "lineNumber"], [1159, 30, 1009, 51], [1160, 20, 1009, 51, "columnNumber"], [1160, 32, 1009, 51], [1161, 18, 1009, 51], [1161, 25, 1009, 57], [1161, 26, 1009, 58], [1161, 41, 1010, 20], [1161, 45, 1010, 20, "_jsxDevRuntime"], [1161, 59, 1010, 20], [1161, 60, 1010, 20, "jsxDEV"], [1161, 66, 1010, 20], [1161, 68, 1010, 21, "_Text"], [1161, 73, 1010, 21], [1161, 74, 1010, 21, "default"], [1161, 81, 1010, 25], [1162, 20, 1010, 26, "style"], [1162, 25, 1010, 31], [1162, 27, 1010, 33, "styles"], [1162, 33, 1010, 39], [1162, 34, 1010, 40, "headerSubtitle"], [1162, 48, 1010, 55], [1163, 20, 1010, 55, "children"], [1163, 28, 1010, 55], [1163, 30, 1010, 56], [1164, 18, 1010, 71], [1165, 20, 1010, 71, "fileName"], [1165, 28, 1010, 71], [1165, 30, 1010, 71, "_jsxFileName"], [1165, 42, 1010, 71], [1166, 20, 1010, 71, "lineNumber"], [1166, 30, 1010, 71], [1167, 20, 1010, 71, "columnNumber"], [1167, 32, 1010, 71], [1168, 18, 1010, 71], [1168, 25, 1010, 77], [1168, 26, 1010, 78], [1169, 16, 1010, 78], [1170, 18, 1010, 78, "fileName"], [1170, 26, 1010, 78], [1170, 28, 1010, 78, "_jsxFileName"], [1170, 40, 1010, 78], [1171, 18, 1010, 78, "lineNumber"], [1171, 28, 1010, 78], [1172, 18, 1010, 78, "columnNumber"], [1172, 30, 1010, 78], [1173, 16, 1010, 78], [1173, 23, 1011, 24], [1173, 24, 1011, 25], [1173, 26, 1012, 19, "challengeCode"], [1173, 39, 1012, 32], [1173, 56, 1013, 20], [1173, 60, 1013, 20, "_jsxDevRuntime"], [1173, 74, 1013, 20], [1173, 75, 1013, 20, "jsxDEV"], [1173, 81, 1013, 20], [1173, 83, 1013, 21, "_View"], [1173, 88, 1013, 21], [1173, 89, 1013, 21, "default"], [1173, 96, 1013, 25], [1174, 18, 1013, 26, "style"], [1174, 23, 1013, 31], [1174, 25, 1013, 33, "styles"], [1174, 31, 1013, 39], [1174, 32, 1013, 40, "challengeRow"], [1174, 44, 1013, 53], [1175, 18, 1013, 53, "children"], [1175, 26, 1013, 53], [1175, 42, 1014, 22], [1175, 46, 1014, 22, "_jsxDevRuntime"], [1175, 60, 1014, 22], [1175, 61, 1014, 22, "jsxDEV"], [1175, 67, 1014, 22], [1175, 69, 1014, 23, "_lucideReactNative"], [1175, 87, 1014, 23], [1175, 88, 1014, 23, "Shield"], [1175, 94, 1014, 29], [1176, 20, 1014, 30, "size"], [1176, 24, 1014, 34], [1176, 26, 1014, 36], [1176, 28, 1014, 39], [1177, 20, 1014, 40, "color"], [1177, 25, 1014, 45], [1177, 27, 1014, 46], [1178, 18, 1014, 52], [1179, 20, 1014, 52, "fileName"], [1179, 28, 1014, 52], [1179, 30, 1014, 52, "_jsxFileName"], [1179, 42, 1014, 52], [1180, 20, 1014, 52, "lineNumber"], [1180, 30, 1014, 52], [1181, 20, 1014, 52, "columnNumber"], [1181, 32, 1014, 52], [1182, 18, 1014, 52], [1182, 25, 1014, 54], [1182, 26, 1014, 55], [1182, 41, 1015, 22], [1182, 45, 1015, 22, "_jsxDevRuntime"], [1182, 59, 1015, 22], [1182, 60, 1015, 22, "jsxDEV"], [1182, 66, 1015, 22], [1182, 68, 1015, 23, "_Text"], [1182, 73, 1015, 23], [1182, 74, 1015, 23, "default"], [1182, 81, 1015, 27], [1183, 20, 1015, 28, "style"], [1183, 25, 1015, 33], [1183, 27, 1015, 35, "styles"], [1183, 33, 1015, 41], [1183, 34, 1015, 42, "challengeCode"], [1183, 47, 1015, 56], [1184, 20, 1015, 56, "children"], [1184, 28, 1015, 56], [1184, 30, 1015, 58, "challengeCode"], [1185, 18, 1015, 71], [1186, 20, 1015, 71, "fileName"], [1186, 28, 1015, 71], [1186, 30, 1015, 71, "_jsxFileName"], [1186, 42, 1015, 71], [1187, 20, 1015, 71, "lineNumber"], [1187, 30, 1015, 71], [1188, 20, 1015, 71, "columnNumber"], [1188, 32, 1015, 71], [1189, 18, 1015, 71], [1189, 25, 1015, 78], [1189, 26, 1015, 79], [1190, 16, 1015, 79], [1191, 18, 1015, 79, "fileName"], [1191, 26, 1015, 79], [1191, 28, 1015, 79, "_jsxFileName"], [1191, 40, 1015, 79], [1192, 18, 1015, 79, "lineNumber"], [1192, 28, 1015, 79], [1193, 18, 1015, 79, "columnNumber"], [1193, 30, 1015, 79], [1194, 16, 1015, 79], [1194, 23, 1016, 26], [1194, 24, 1017, 19], [1195, 14, 1017, 19], [1196, 16, 1017, 19, "fileName"], [1196, 24, 1017, 19], [1196, 26, 1017, 19, "_jsxFileName"], [1196, 38, 1017, 19], [1197, 16, 1017, 19, "lineNumber"], [1197, 26, 1017, 19], [1198, 16, 1017, 19, "columnNumber"], [1198, 28, 1017, 19], [1199, 14, 1017, 19], [1199, 21, 1018, 22], [1199, 22, 1018, 23], [1199, 37, 1019, 16], [1199, 41, 1019, 16, "_jsxDevRuntime"], [1199, 55, 1019, 16], [1199, 56, 1019, 16, "jsxDEV"], [1199, 62, 1019, 16], [1199, 64, 1019, 17, "_TouchableOpacity"], [1199, 81, 1019, 17], [1199, 82, 1019, 17, "default"], [1199, 89, 1019, 33], [1200, 16, 1019, 34, "onPress"], [1200, 23, 1019, 41], [1200, 25, 1019, 43, "onCancel"], [1200, 33, 1019, 52], [1201, 16, 1019, 53, "style"], [1201, 21, 1019, 58], [1201, 23, 1019, 60, "styles"], [1201, 29, 1019, 66], [1201, 30, 1019, 67, "closeButton"], [1201, 41, 1019, 79], [1202, 16, 1019, 79, "children"], [1202, 24, 1019, 79], [1202, 39, 1020, 18], [1202, 43, 1020, 18, "_jsxDevRuntime"], [1202, 57, 1020, 18], [1202, 58, 1020, 18, "jsxDEV"], [1202, 64, 1020, 18], [1202, 66, 1020, 19, "_lucideReactNative"], [1202, 84, 1020, 19], [1202, 85, 1020, 19, "X"], [1202, 86, 1020, 20], [1203, 18, 1020, 21, "size"], [1203, 22, 1020, 25], [1203, 24, 1020, 27], [1203, 26, 1020, 30], [1204, 18, 1020, 31, "color"], [1204, 23, 1020, 36], [1204, 25, 1020, 37], [1205, 16, 1020, 43], [1206, 18, 1020, 43, "fileName"], [1206, 26, 1020, 43], [1206, 28, 1020, 43, "_jsxFileName"], [1206, 40, 1020, 43], [1207, 18, 1020, 43, "lineNumber"], [1207, 28, 1020, 43], [1208, 18, 1020, 43, "columnNumber"], [1208, 30, 1020, 43], [1209, 16, 1020, 43], [1209, 23, 1020, 45], [1210, 14, 1020, 46], [1211, 16, 1020, 46, "fileName"], [1211, 24, 1020, 46], [1211, 26, 1020, 46, "_jsxFileName"], [1211, 38, 1020, 46], [1212, 16, 1020, 46, "lineNumber"], [1212, 26, 1020, 46], [1213, 16, 1020, 46, "columnNumber"], [1213, 28, 1020, 46], [1214, 14, 1020, 46], [1214, 21, 1021, 34], [1214, 22, 1021, 35], [1215, 12, 1021, 35], [1216, 14, 1021, 35, "fileName"], [1216, 22, 1021, 35], [1216, 24, 1021, 35, "_jsxFileName"], [1216, 36, 1021, 35], [1217, 14, 1021, 35, "lineNumber"], [1217, 24, 1021, 35], [1218, 14, 1021, 35, "columnNumber"], [1218, 26, 1021, 35], [1219, 12, 1021, 35], [1219, 19, 1022, 20], [1220, 10, 1022, 21], [1221, 12, 1022, 21, "fileName"], [1221, 20, 1022, 21], [1221, 22, 1022, 21, "_jsxFileName"], [1221, 34, 1022, 21], [1222, 12, 1022, 21, "lineNumber"], [1222, 22, 1022, 21], [1223, 12, 1022, 21, "columnNumber"], [1223, 24, 1022, 21], [1224, 10, 1022, 21], [1224, 17, 1023, 18], [1224, 18, 1023, 19], [1224, 33, 1025, 12], [1224, 37, 1025, 12, "_jsxDevRuntime"], [1224, 51, 1025, 12], [1224, 52, 1025, 12, "jsxDEV"], [1224, 58, 1025, 12], [1224, 60, 1025, 13, "_View"], [1224, 65, 1025, 13], [1224, 66, 1025, 13, "default"], [1224, 73, 1025, 17], [1225, 12, 1025, 18, "style"], [1225, 17, 1025, 23], [1225, 19, 1025, 25, "styles"], [1225, 25, 1025, 31], [1225, 26, 1025, 32, "privacyNotice"], [1225, 39, 1025, 46], [1226, 12, 1025, 46, "children"], [1226, 20, 1025, 46], [1226, 36, 1026, 14], [1226, 40, 1026, 14, "_jsxDevRuntime"], [1226, 54, 1026, 14], [1226, 55, 1026, 14, "jsxDEV"], [1226, 61, 1026, 14], [1226, 63, 1026, 15, "_lucideReactNative"], [1226, 81, 1026, 15], [1226, 82, 1026, 15, "Shield"], [1226, 88, 1026, 21], [1227, 14, 1026, 22, "size"], [1227, 18, 1026, 26], [1227, 20, 1026, 28], [1227, 22, 1026, 31], [1228, 14, 1026, 32, "color"], [1228, 19, 1026, 37], [1228, 21, 1026, 38], [1229, 12, 1026, 47], [1230, 14, 1026, 47, "fileName"], [1230, 22, 1026, 47], [1230, 24, 1026, 47, "_jsxFileName"], [1230, 36, 1026, 47], [1231, 14, 1026, 47, "lineNumber"], [1231, 24, 1026, 47], [1232, 14, 1026, 47, "columnNumber"], [1232, 26, 1026, 47], [1233, 12, 1026, 47], [1233, 19, 1026, 49], [1233, 20, 1026, 50], [1233, 35, 1027, 14], [1233, 39, 1027, 14, "_jsxDevRuntime"], [1233, 53, 1027, 14], [1233, 54, 1027, 14, "jsxDEV"], [1233, 60, 1027, 14], [1233, 62, 1027, 15, "_Text"], [1233, 67, 1027, 15], [1233, 68, 1027, 15, "default"], [1233, 75, 1027, 19], [1234, 14, 1027, 20, "style"], [1234, 19, 1027, 25], [1234, 21, 1027, 27, "styles"], [1234, 27, 1027, 33], [1234, 28, 1027, 34, "privacyText"], [1234, 39, 1027, 46], [1235, 14, 1027, 46, "children"], [1235, 22, 1027, 46], [1235, 24, 1027, 47], [1236, 12, 1029, 14], [1237, 14, 1029, 14, "fileName"], [1237, 22, 1029, 14], [1237, 24, 1029, 14, "_jsxFileName"], [1237, 36, 1029, 14], [1238, 14, 1029, 14, "lineNumber"], [1238, 24, 1029, 14], [1239, 14, 1029, 14, "columnNumber"], [1239, 26, 1029, 14], [1240, 12, 1029, 14], [1240, 19, 1029, 20], [1240, 20, 1029, 21], [1241, 10, 1029, 21], [1242, 12, 1029, 21, "fileName"], [1242, 20, 1029, 21], [1242, 22, 1029, 21, "_jsxFileName"], [1242, 34, 1029, 21], [1243, 12, 1029, 21, "lineNumber"], [1243, 22, 1029, 21], [1244, 12, 1029, 21, "columnNumber"], [1244, 24, 1029, 21], [1245, 10, 1029, 21], [1245, 17, 1030, 18], [1245, 18, 1030, 19], [1245, 33, 1032, 12], [1245, 37, 1032, 12, "_jsxDevRuntime"], [1245, 51, 1032, 12], [1245, 52, 1032, 12, "jsxDEV"], [1245, 58, 1032, 12], [1245, 60, 1032, 13, "_View"], [1245, 65, 1032, 13], [1245, 66, 1032, 13, "default"], [1245, 73, 1032, 17], [1246, 12, 1032, 18, "style"], [1246, 17, 1032, 23], [1246, 19, 1032, 25, "styles"], [1246, 25, 1032, 31], [1246, 26, 1032, 32, "footer<PERSON><PERSON><PERSON>"], [1246, 39, 1032, 46], [1247, 12, 1032, 46, "children"], [1247, 20, 1032, 46], [1247, 36, 1033, 14], [1247, 40, 1033, 14, "_jsxDevRuntime"], [1247, 54, 1033, 14], [1247, 55, 1033, 14, "jsxDEV"], [1247, 61, 1033, 14], [1247, 63, 1033, 15, "_Text"], [1247, 68, 1033, 15], [1247, 69, 1033, 15, "default"], [1247, 76, 1033, 19], [1248, 14, 1033, 20, "style"], [1248, 19, 1033, 25], [1248, 21, 1033, 27, "styles"], [1248, 27, 1033, 33], [1248, 28, 1033, 34, "instruction"], [1248, 39, 1033, 46], [1249, 14, 1033, 46, "children"], [1249, 22, 1033, 46], [1249, 24, 1033, 47], [1250, 12, 1035, 14], [1251, 14, 1035, 14, "fileName"], [1251, 22, 1035, 14], [1251, 24, 1035, 14, "_jsxFileName"], [1251, 36, 1035, 14], [1252, 14, 1035, 14, "lineNumber"], [1252, 24, 1035, 14], [1253, 14, 1035, 14, "columnNumber"], [1253, 26, 1035, 14], [1254, 12, 1035, 14], [1254, 19, 1035, 20], [1254, 20, 1035, 21], [1254, 35, 1037, 14], [1254, 39, 1037, 14, "_jsxDevRuntime"], [1254, 53, 1037, 14], [1254, 54, 1037, 14, "jsxDEV"], [1254, 60, 1037, 14], [1254, 62, 1037, 15, "_TouchableOpacity"], [1254, 79, 1037, 15], [1254, 80, 1037, 15, "default"], [1254, 87, 1037, 31], [1255, 14, 1038, 16, "onPress"], [1255, 21, 1038, 23], [1255, 23, 1038, 25, "capturePhoto"], [1255, 35, 1038, 38], [1256, 14, 1039, 16, "disabled"], [1256, 22, 1039, 24], [1256, 24, 1039, 26, "processingState"], [1256, 39, 1039, 41], [1256, 44, 1039, 46], [1256, 50, 1039, 52], [1256, 54, 1039, 56], [1256, 55, 1039, 57, "isCameraReady"], [1256, 68, 1039, 71], [1257, 14, 1040, 16, "style"], [1257, 19, 1040, 21], [1257, 21, 1040, 23], [1257, 22, 1041, 18, "styles"], [1257, 28, 1041, 24], [1257, 29, 1041, 25, "shutterButton"], [1257, 42, 1041, 38], [1257, 44, 1042, 18, "processingState"], [1257, 59, 1042, 33], [1257, 64, 1042, 38], [1257, 70, 1042, 44], [1257, 74, 1042, 48, "styles"], [1257, 80, 1042, 54], [1257, 81, 1042, 55, "shutterButtonDisabled"], [1257, 102, 1042, 76], [1257, 103, 1043, 18], [1258, 14, 1043, 18, "children"], [1258, 22, 1043, 18], [1258, 24, 1045, 17, "processingState"], [1258, 39, 1045, 32], [1258, 44, 1045, 37], [1258, 50, 1045, 43], [1258, 66, 1046, 18], [1258, 70, 1046, 18, "_jsxDevRuntime"], [1258, 84, 1046, 18], [1258, 85, 1046, 18, "jsxDEV"], [1258, 91, 1046, 18], [1258, 93, 1046, 19, "_View"], [1258, 98, 1046, 19], [1258, 99, 1046, 19, "default"], [1258, 106, 1046, 23], [1259, 16, 1046, 24, "style"], [1259, 21, 1046, 29], [1259, 23, 1046, 31, "styles"], [1259, 29, 1046, 37], [1259, 30, 1046, 38, "shutterInner"], [1260, 14, 1046, 51], [1261, 16, 1046, 51, "fileName"], [1261, 24, 1046, 51], [1261, 26, 1046, 51, "_jsxFileName"], [1261, 38, 1046, 51], [1262, 16, 1046, 51, "lineNumber"], [1262, 26, 1046, 51], [1263, 16, 1046, 51, "columnNumber"], [1263, 28, 1046, 51], [1264, 14, 1046, 51], [1264, 21, 1046, 53], [1264, 22, 1046, 54], [1264, 38, 1048, 18], [1264, 42, 1048, 18, "_jsxDevRuntime"], [1264, 56, 1048, 18], [1264, 57, 1048, 18, "jsxDEV"], [1264, 63, 1048, 18], [1264, 65, 1048, 19, "_ActivityIndicator"], [1264, 83, 1048, 19], [1264, 84, 1048, 19, "default"], [1264, 91, 1048, 36], [1265, 16, 1048, 37, "size"], [1265, 20, 1048, 41], [1265, 22, 1048, 42], [1265, 29, 1048, 49], [1266, 16, 1048, 50, "color"], [1266, 21, 1048, 55], [1266, 23, 1048, 56], [1267, 14, 1048, 65], [1268, 16, 1048, 65, "fileName"], [1268, 24, 1048, 65], [1268, 26, 1048, 65, "_jsxFileName"], [1268, 38, 1048, 65], [1269, 16, 1048, 65, "lineNumber"], [1269, 26, 1048, 65], [1270, 16, 1048, 65, "columnNumber"], [1270, 28, 1048, 65], [1271, 14, 1048, 65], [1271, 21, 1048, 67], [1272, 12, 1049, 17], [1273, 14, 1049, 17, "fileName"], [1273, 22, 1049, 17], [1273, 24, 1049, 17, "_jsxFileName"], [1273, 36, 1049, 17], [1274, 14, 1049, 17, "lineNumber"], [1274, 24, 1049, 17], [1275, 14, 1049, 17, "columnNumber"], [1275, 26, 1049, 17], [1276, 12, 1049, 17], [1276, 19, 1050, 32], [1276, 20, 1050, 33], [1276, 35, 1051, 14], [1276, 39, 1051, 14, "_jsxDevRuntime"], [1276, 53, 1051, 14], [1276, 54, 1051, 14, "jsxDEV"], [1276, 60, 1051, 14], [1276, 62, 1051, 15, "_Text"], [1276, 67, 1051, 15], [1276, 68, 1051, 15, "default"], [1276, 75, 1051, 19], [1277, 14, 1051, 20, "style"], [1277, 19, 1051, 25], [1277, 21, 1051, 27, "styles"], [1277, 27, 1051, 33], [1277, 28, 1051, 34, "privacyNote"], [1277, 39, 1051, 46], [1278, 14, 1051, 46, "children"], [1278, 22, 1051, 46], [1278, 24, 1051, 47], [1279, 12, 1053, 14], [1280, 14, 1053, 14, "fileName"], [1280, 22, 1053, 14], [1280, 24, 1053, 14, "_jsxFileName"], [1280, 36, 1053, 14], [1281, 14, 1053, 14, "lineNumber"], [1281, 24, 1053, 14], [1282, 14, 1053, 14, "columnNumber"], [1282, 26, 1053, 14], [1283, 12, 1053, 14], [1283, 19, 1053, 20], [1283, 20, 1053, 21], [1284, 10, 1053, 21], [1285, 12, 1053, 21, "fileName"], [1285, 20, 1053, 21], [1285, 22, 1053, 21, "_jsxFileName"], [1285, 34, 1053, 21], [1286, 12, 1053, 21, "lineNumber"], [1286, 22, 1053, 21], [1287, 12, 1053, 21, "columnNumber"], [1287, 24, 1053, 21], [1288, 10, 1053, 21], [1288, 17, 1054, 18], [1288, 18, 1054, 19], [1289, 8, 1054, 19], [1289, 23, 1055, 12], [1289, 24, 1056, 9], [1290, 6, 1056, 9], [1291, 8, 1056, 9, "fileName"], [1291, 16, 1056, 9], [1291, 18, 1056, 9, "_jsxFileName"], [1291, 30, 1056, 9], [1292, 8, 1056, 9, "lineNumber"], [1292, 18, 1056, 9], [1293, 8, 1056, 9, "columnNumber"], [1293, 20, 1056, 9], [1294, 6, 1056, 9], [1294, 13, 1057, 12], [1294, 14, 1057, 13], [1294, 29, 1059, 6], [1294, 33, 1059, 6, "_jsxDevRuntime"], [1294, 47, 1059, 6], [1294, 48, 1059, 6, "jsxDEV"], [1294, 54, 1059, 6], [1294, 56, 1059, 7, "_Modal"], [1294, 62, 1059, 7], [1294, 63, 1059, 7, "default"], [1294, 70, 1059, 12], [1295, 8, 1060, 8, "visible"], [1295, 15, 1060, 15], [1295, 17, 1060, 17, "processingState"], [1295, 32, 1060, 32], [1295, 37, 1060, 37], [1295, 43, 1060, 43], [1295, 47, 1060, 47, "processingState"], [1295, 62, 1060, 62], [1295, 67, 1060, 67], [1295, 74, 1060, 75], [1296, 8, 1061, 8, "transparent"], [1296, 19, 1061, 19], [1297, 8, 1062, 8, "animationType"], [1297, 21, 1062, 21], [1297, 23, 1062, 22], [1297, 29, 1062, 28], [1298, 8, 1062, 28, "children"], [1298, 16, 1062, 28], [1298, 31, 1064, 8], [1298, 35, 1064, 8, "_jsxDevRuntime"], [1298, 49, 1064, 8], [1298, 50, 1064, 8, "jsxDEV"], [1298, 56, 1064, 8], [1298, 58, 1064, 9, "_View"], [1298, 63, 1064, 9], [1298, 64, 1064, 9, "default"], [1298, 71, 1064, 13], [1299, 10, 1064, 14, "style"], [1299, 15, 1064, 19], [1299, 17, 1064, 21, "styles"], [1299, 23, 1064, 27], [1299, 24, 1064, 28, "processingModal"], [1299, 39, 1064, 44], [1300, 10, 1064, 44, "children"], [1300, 18, 1064, 44], [1300, 33, 1065, 10], [1300, 37, 1065, 10, "_jsxDevRuntime"], [1300, 51, 1065, 10], [1300, 52, 1065, 10, "jsxDEV"], [1300, 58, 1065, 10], [1300, 60, 1065, 11, "_View"], [1300, 65, 1065, 11], [1300, 66, 1065, 11, "default"], [1300, 73, 1065, 15], [1301, 12, 1065, 16, "style"], [1301, 17, 1065, 21], [1301, 19, 1065, 23, "styles"], [1301, 25, 1065, 29], [1301, 26, 1065, 30, "processingContent"], [1301, 43, 1065, 48], [1302, 12, 1065, 48, "children"], [1302, 20, 1065, 48], [1302, 36, 1066, 12], [1302, 40, 1066, 12, "_jsxDevRuntime"], [1302, 54, 1066, 12], [1302, 55, 1066, 12, "jsxDEV"], [1302, 61, 1066, 12], [1302, 63, 1066, 13, "_ActivityIndicator"], [1302, 81, 1066, 13], [1302, 82, 1066, 13, "default"], [1302, 89, 1066, 30], [1303, 14, 1066, 31, "size"], [1303, 18, 1066, 35], [1303, 20, 1066, 36], [1303, 27, 1066, 43], [1304, 14, 1066, 44, "color"], [1304, 19, 1066, 49], [1304, 21, 1066, 50], [1305, 12, 1066, 59], [1306, 14, 1066, 59, "fileName"], [1306, 22, 1066, 59], [1306, 24, 1066, 59, "_jsxFileName"], [1306, 36, 1066, 59], [1307, 14, 1066, 59, "lineNumber"], [1307, 24, 1066, 59], [1308, 14, 1066, 59, "columnNumber"], [1308, 26, 1066, 59], [1309, 12, 1066, 59], [1309, 19, 1066, 61], [1309, 20, 1066, 62], [1309, 35, 1068, 12], [1309, 39, 1068, 12, "_jsxDevRuntime"], [1309, 53, 1068, 12], [1309, 54, 1068, 12, "jsxDEV"], [1309, 60, 1068, 12], [1309, 62, 1068, 13, "_Text"], [1309, 67, 1068, 13], [1309, 68, 1068, 13, "default"], [1309, 75, 1068, 17], [1310, 14, 1068, 18, "style"], [1310, 19, 1068, 23], [1310, 21, 1068, 25, "styles"], [1310, 27, 1068, 31], [1310, 28, 1068, 32, "processingTitle"], [1310, 43, 1068, 48], [1311, 14, 1068, 48, "children"], [1311, 22, 1068, 48], [1311, 25, 1069, 15, "processingState"], [1311, 40, 1069, 30], [1311, 45, 1069, 35], [1311, 56, 1069, 46], [1311, 60, 1069, 50], [1311, 80, 1069, 70], [1311, 82, 1070, 15, "processingState"], [1311, 97, 1070, 30], [1311, 102, 1070, 35], [1311, 113, 1070, 46], [1311, 117, 1070, 50], [1311, 146, 1070, 79], [1311, 148, 1071, 15, "processingState"], [1311, 163, 1071, 30], [1311, 168, 1071, 35], [1311, 180, 1071, 47], [1311, 184, 1071, 51], [1311, 216, 1071, 83], [1311, 218, 1072, 15, "processingState"], [1311, 233, 1072, 30], [1311, 238, 1072, 35], [1311, 249, 1072, 46], [1311, 253, 1072, 50], [1311, 275, 1072, 72], [1312, 12, 1072, 72], [1313, 14, 1072, 72, "fileName"], [1313, 22, 1072, 72], [1313, 24, 1072, 72, "_jsxFileName"], [1313, 36, 1072, 72], [1314, 14, 1072, 72, "lineNumber"], [1314, 24, 1072, 72], [1315, 14, 1072, 72, "columnNumber"], [1315, 26, 1072, 72], [1316, 12, 1072, 72], [1316, 19, 1073, 18], [1316, 20, 1073, 19], [1316, 35, 1074, 12], [1316, 39, 1074, 12, "_jsxDevRuntime"], [1316, 53, 1074, 12], [1316, 54, 1074, 12, "jsxDEV"], [1316, 60, 1074, 12], [1316, 62, 1074, 13, "_View"], [1316, 67, 1074, 13], [1316, 68, 1074, 13, "default"], [1316, 75, 1074, 17], [1317, 14, 1074, 18, "style"], [1317, 19, 1074, 23], [1317, 21, 1074, 25, "styles"], [1317, 27, 1074, 31], [1317, 28, 1074, 32, "progressBar"], [1317, 39, 1074, 44], [1318, 14, 1074, 44, "children"], [1318, 22, 1074, 44], [1318, 37, 1075, 14], [1318, 41, 1075, 14, "_jsxDevRuntime"], [1318, 55, 1075, 14], [1318, 56, 1075, 14, "jsxDEV"], [1318, 62, 1075, 14], [1318, 64, 1075, 15, "_View"], [1318, 69, 1075, 15], [1318, 70, 1075, 15, "default"], [1318, 77, 1075, 19], [1319, 16, 1076, 16, "style"], [1319, 21, 1076, 21], [1319, 23, 1076, 23], [1319, 24, 1077, 18, "styles"], [1319, 30, 1077, 24], [1319, 31, 1077, 25, "progressFill"], [1319, 43, 1077, 37], [1319, 45, 1078, 18], [1320, 18, 1078, 20, "width"], [1320, 23, 1078, 25], [1320, 25, 1078, 27], [1320, 28, 1078, 30, "processingProgress"], [1320, 46, 1078, 48], [1321, 16, 1078, 52], [1321, 17, 1078, 53], [1322, 14, 1079, 18], [1323, 16, 1079, 18, "fileName"], [1323, 24, 1079, 18], [1323, 26, 1079, 18, "_jsxFileName"], [1323, 38, 1079, 18], [1324, 16, 1079, 18, "lineNumber"], [1324, 26, 1079, 18], [1325, 16, 1079, 18, "columnNumber"], [1325, 28, 1079, 18], [1326, 14, 1079, 18], [1326, 21, 1080, 15], [1327, 12, 1080, 16], [1328, 14, 1080, 16, "fileName"], [1328, 22, 1080, 16], [1328, 24, 1080, 16, "_jsxFileName"], [1328, 36, 1080, 16], [1329, 14, 1080, 16, "lineNumber"], [1329, 24, 1080, 16], [1330, 14, 1080, 16, "columnNumber"], [1330, 26, 1080, 16], [1331, 12, 1080, 16], [1331, 19, 1081, 18], [1331, 20, 1081, 19], [1331, 35, 1082, 12], [1331, 39, 1082, 12, "_jsxDevRuntime"], [1331, 53, 1082, 12], [1331, 54, 1082, 12, "jsxDEV"], [1331, 60, 1082, 12], [1331, 62, 1082, 13, "_Text"], [1331, 67, 1082, 13], [1331, 68, 1082, 13, "default"], [1331, 75, 1082, 17], [1332, 14, 1082, 18, "style"], [1332, 19, 1082, 23], [1332, 21, 1082, 25, "styles"], [1332, 27, 1082, 31], [1332, 28, 1082, 32, "processingDescription"], [1332, 49, 1082, 54], [1333, 14, 1082, 54, "children"], [1333, 22, 1082, 54], [1333, 25, 1083, 15, "processingState"], [1333, 40, 1083, 30], [1333, 45, 1083, 35], [1333, 56, 1083, 46], [1333, 60, 1083, 50], [1333, 89, 1083, 79], [1333, 91, 1084, 15, "processingState"], [1333, 106, 1084, 30], [1333, 111, 1084, 35], [1333, 122, 1084, 46], [1333, 126, 1084, 50], [1333, 164, 1084, 88], [1333, 166, 1085, 15, "processingState"], [1333, 181, 1085, 30], [1333, 186, 1085, 35], [1333, 198, 1085, 47], [1333, 202, 1085, 51], [1333, 247, 1085, 96], [1333, 249, 1086, 15, "processingState"], [1333, 264, 1086, 30], [1333, 269, 1086, 35], [1333, 280, 1086, 46], [1333, 284, 1086, 50], [1333, 325, 1086, 91], [1334, 12, 1086, 91], [1335, 14, 1086, 91, "fileName"], [1335, 22, 1086, 91], [1335, 24, 1086, 91, "_jsxFileName"], [1335, 36, 1086, 91], [1336, 14, 1086, 91, "lineNumber"], [1336, 24, 1086, 91], [1337, 14, 1086, 91, "columnNumber"], [1337, 26, 1086, 91], [1338, 12, 1086, 91], [1338, 19, 1087, 18], [1338, 20, 1087, 19], [1338, 22, 1088, 13, "processingState"], [1338, 37, 1088, 28], [1338, 42, 1088, 33], [1338, 53, 1088, 44], [1338, 70, 1089, 14], [1338, 74, 1089, 14, "_jsxDevRuntime"], [1338, 88, 1089, 14], [1338, 89, 1089, 14, "jsxDEV"], [1338, 95, 1089, 14], [1338, 97, 1089, 15, "_lucideReactNative"], [1338, 115, 1089, 15], [1338, 116, 1089, 15, "CheckCircle"], [1338, 127, 1089, 26], [1339, 14, 1089, 27, "size"], [1339, 18, 1089, 31], [1339, 20, 1089, 33], [1339, 22, 1089, 36], [1340, 14, 1089, 37, "color"], [1340, 19, 1089, 42], [1340, 21, 1089, 43], [1340, 30, 1089, 52], [1341, 14, 1089, 53, "style"], [1341, 19, 1089, 58], [1341, 21, 1089, 60, "styles"], [1341, 27, 1089, 66], [1341, 28, 1089, 67, "successIcon"], [1342, 12, 1089, 79], [1343, 14, 1089, 79, "fileName"], [1343, 22, 1089, 79], [1343, 24, 1089, 79, "_jsxFileName"], [1343, 36, 1089, 79], [1344, 14, 1089, 79, "lineNumber"], [1344, 24, 1089, 79], [1345, 14, 1089, 79, "columnNumber"], [1345, 26, 1089, 79], [1346, 12, 1089, 79], [1346, 19, 1089, 81], [1346, 20, 1090, 13], [1347, 10, 1090, 13], [1348, 12, 1090, 13, "fileName"], [1348, 20, 1090, 13], [1348, 22, 1090, 13, "_jsxFileName"], [1348, 34, 1090, 13], [1349, 12, 1090, 13, "lineNumber"], [1349, 22, 1090, 13], [1350, 12, 1090, 13, "columnNumber"], [1350, 24, 1090, 13], [1351, 10, 1090, 13], [1351, 17, 1091, 16], [1352, 8, 1091, 17], [1353, 10, 1091, 17, "fileName"], [1353, 18, 1091, 17], [1353, 20, 1091, 17, "_jsxFileName"], [1353, 32, 1091, 17], [1354, 10, 1091, 17, "lineNumber"], [1354, 20, 1091, 17], [1355, 10, 1091, 17, "columnNumber"], [1355, 22, 1091, 17], [1356, 8, 1091, 17], [1356, 15, 1092, 14], [1357, 6, 1092, 15], [1358, 8, 1092, 15, "fileName"], [1358, 16, 1092, 15], [1358, 18, 1092, 15, "_jsxFileName"], [1358, 30, 1092, 15], [1359, 8, 1092, 15, "lineNumber"], [1359, 18, 1092, 15], [1360, 8, 1092, 15, "columnNumber"], [1360, 20, 1092, 15], [1361, 6, 1092, 15], [1361, 13, 1093, 13], [1361, 14, 1093, 14], [1361, 29, 1095, 6], [1361, 33, 1095, 6, "_jsxDevRuntime"], [1361, 47, 1095, 6], [1361, 48, 1095, 6, "jsxDEV"], [1361, 54, 1095, 6], [1361, 56, 1095, 7, "_Modal"], [1361, 62, 1095, 7], [1361, 63, 1095, 7, "default"], [1361, 70, 1095, 12], [1362, 8, 1096, 8, "visible"], [1362, 15, 1096, 15], [1362, 17, 1096, 17, "processingState"], [1362, 32, 1096, 32], [1362, 37, 1096, 37], [1362, 44, 1096, 45], [1363, 8, 1097, 8, "transparent"], [1363, 19, 1097, 19], [1364, 8, 1098, 8, "animationType"], [1364, 21, 1098, 21], [1364, 23, 1098, 22], [1364, 29, 1098, 28], [1365, 8, 1098, 28, "children"], [1365, 16, 1098, 28], [1365, 31, 1100, 8], [1365, 35, 1100, 8, "_jsxDevRuntime"], [1365, 49, 1100, 8], [1365, 50, 1100, 8, "jsxDEV"], [1365, 56, 1100, 8], [1365, 58, 1100, 9, "_View"], [1365, 63, 1100, 9], [1365, 64, 1100, 9, "default"], [1365, 71, 1100, 13], [1366, 10, 1100, 14, "style"], [1366, 15, 1100, 19], [1366, 17, 1100, 21, "styles"], [1366, 23, 1100, 27], [1366, 24, 1100, 28, "processingModal"], [1366, 39, 1100, 44], [1367, 10, 1100, 44, "children"], [1367, 18, 1100, 44], [1367, 33, 1101, 10], [1367, 37, 1101, 10, "_jsxDevRuntime"], [1367, 51, 1101, 10], [1367, 52, 1101, 10, "jsxDEV"], [1367, 58, 1101, 10], [1367, 60, 1101, 11, "_View"], [1367, 65, 1101, 11], [1367, 66, 1101, 11, "default"], [1367, 73, 1101, 15], [1368, 12, 1101, 16, "style"], [1368, 17, 1101, 21], [1368, 19, 1101, 23, "styles"], [1368, 25, 1101, 29], [1368, 26, 1101, 30, "errorContent"], [1368, 38, 1101, 43], [1369, 12, 1101, 43, "children"], [1369, 20, 1101, 43], [1369, 36, 1102, 12], [1369, 40, 1102, 12, "_jsxDevRuntime"], [1369, 54, 1102, 12], [1369, 55, 1102, 12, "jsxDEV"], [1369, 61, 1102, 12], [1369, 63, 1102, 13, "_lucideReactNative"], [1369, 81, 1102, 13], [1369, 82, 1102, 13, "X"], [1369, 83, 1102, 14], [1370, 14, 1102, 15, "size"], [1370, 18, 1102, 19], [1370, 20, 1102, 21], [1370, 22, 1102, 24], [1371, 14, 1102, 25, "color"], [1371, 19, 1102, 30], [1371, 21, 1102, 31], [1372, 12, 1102, 40], [1373, 14, 1102, 40, "fileName"], [1373, 22, 1102, 40], [1373, 24, 1102, 40, "_jsxFileName"], [1373, 36, 1102, 40], [1374, 14, 1102, 40, "lineNumber"], [1374, 24, 1102, 40], [1375, 14, 1102, 40, "columnNumber"], [1375, 26, 1102, 40], [1376, 12, 1102, 40], [1376, 19, 1102, 42], [1376, 20, 1102, 43], [1376, 35, 1103, 12], [1376, 39, 1103, 12, "_jsxDevRuntime"], [1376, 53, 1103, 12], [1376, 54, 1103, 12, "jsxDEV"], [1376, 60, 1103, 12], [1376, 62, 1103, 13, "_Text"], [1376, 67, 1103, 13], [1376, 68, 1103, 13, "default"], [1376, 75, 1103, 17], [1377, 14, 1103, 18, "style"], [1377, 19, 1103, 23], [1377, 21, 1103, 25, "styles"], [1377, 27, 1103, 31], [1377, 28, 1103, 32, "errorTitle"], [1377, 38, 1103, 43], [1378, 14, 1103, 43, "children"], [1378, 22, 1103, 43], [1378, 24, 1103, 44], [1379, 12, 1103, 61], [1380, 14, 1103, 61, "fileName"], [1380, 22, 1103, 61], [1380, 24, 1103, 61, "_jsxFileName"], [1380, 36, 1103, 61], [1381, 14, 1103, 61, "lineNumber"], [1381, 24, 1103, 61], [1382, 14, 1103, 61, "columnNumber"], [1382, 26, 1103, 61], [1383, 12, 1103, 61], [1383, 19, 1103, 67], [1383, 20, 1103, 68], [1383, 35, 1104, 12], [1383, 39, 1104, 12, "_jsxDevRuntime"], [1383, 53, 1104, 12], [1383, 54, 1104, 12, "jsxDEV"], [1383, 60, 1104, 12], [1383, 62, 1104, 13, "_Text"], [1383, 67, 1104, 13], [1383, 68, 1104, 13, "default"], [1383, 75, 1104, 17], [1384, 14, 1104, 18, "style"], [1384, 19, 1104, 23], [1384, 21, 1104, 25, "styles"], [1384, 27, 1104, 31], [1384, 28, 1104, 32, "errorMessage"], [1384, 40, 1104, 45], [1385, 14, 1104, 45, "children"], [1385, 22, 1104, 45], [1385, 24, 1104, 47, "errorMessage"], [1386, 12, 1104, 59], [1387, 14, 1104, 59, "fileName"], [1387, 22, 1104, 59], [1387, 24, 1104, 59, "_jsxFileName"], [1387, 36, 1104, 59], [1388, 14, 1104, 59, "lineNumber"], [1388, 24, 1104, 59], [1389, 14, 1104, 59, "columnNumber"], [1389, 26, 1104, 59], [1390, 12, 1104, 59], [1390, 19, 1104, 66], [1390, 20, 1104, 67], [1390, 35, 1105, 12], [1390, 39, 1105, 12, "_jsxDevRuntime"], [1390, 53, 1105, 12], [1390, 54, 1105, 12, "jsxDEV"], [1390, 60, 1105, 12], [1390, 62, 1105, 13, "_TouchableOpacity"], [1390, 79, 1105, 13], [1390, 80, 1105, 13, "default"], [1390, 87, 1105, 29], [1391, 14, 1106, 14, "onPress"], [1391, 21, 1106, 21], [1391, 23, 1106, 23, "retryCapture"], [1391, 35, 1106, 36], [1392, 14, 1107, 14, "style"], [1392, 19, 1107, 19], [1392, 21, 1107, 21, "styles"], [1392, 27, 1107, 27], [1392, 28, 1107, 28, "primaryButton"], [1392, 41, 1107, 42], [1393, 14, 1107, 42, "children"], [1393, 22, 1107, 42], [1393, 37, 1109, 14], [1393, 41, 1109, 14, "_jsxDevRuntime"], [1393, 55, 1109, 14], [1393, 56, 1109, 14, "jsxDEV"], [1393, 62, 1109, 14], [1393, 64, 1109, 15, "_Text"], [1393, 69, 1109, 15], [1393, 70, 1109, 15, "default"], [1393, 77, 1109, 19], [1394, 16, 1109, 20, "style"], [1394, 21, 1109, 25], [1394, 23, 1109, 27, "styles"], [1394, 29, 1109, 33], [1394, 30, 1109, 34, "primaryButtonText"], [1394, 47, 1109, 52], [1395, 16, 1109, 52, "children"], [1395, 24, 1109, 52], [1395, 26, 1109, 53], [1396, 14, 1109, 62], [1397, 16, 1109, 62, "fileName"], [1397, 24, 1109, 62], [1397, 26, 1109, 62, "_jsxFileName"], [1397, 38, 1109, 62], [1398, 16, 1109, 62, "lineNumber"], [1398, 26, 1109, 62], [1399, 16, 1109, 62, "columnNumber"], [1399, 28, 1109, 62], [1400, 14, 1109, 62], [1400, 21, 1109, 68], [1401, 12, 1109, 69], [1402, 14, 1109, 69, "fileName"], [1402, 22, 1109, 69], [1402, 24, 1109, 69, "_jsxFileName"], [1402, 36, 1109, 69], [1403, 14, 1109, 69, "lineNumber"], [1403, 24, 1109, 69], [1404, 14, 1109, 69, "columnNumber"], [1404, 26, 1109, 69], [1405, 12, 1109, 69], [1405, 19, 1110, 30], [1405, 20, 1110, 31], [1405, 35, 1111, 12], [1405, 39, 1111, 12, "_jsxDevRuntime"], [1405, 53, 1111, 12], [1405, 54, 1111, 12, "jsxDEV"], [1405, 60, 1111, 12], [1405, 62, 1111, 13, "_TouchableOpacity"], [1405, 79, 1111, 13], [1405, 80, 1111, 13, "default"], [1405, 87, 1111, 29], [1406, 14, 1112, 14, "onPress"], [1406, 21, 1112, 21], [1406, 23, 1112, 23, "onCancel"], [1406, 31, 1112, 32], [1407, 14, 1113, 14, "style"], [1407, 19, 1113, 19], [1407, 21, 1113, 21, "styles"], [1407, 27, 1113, 27], [1407, 28, 1113, 28, "secondaryButton"], [1407, 43, 1113, 44], [1408, 14, 1113, 44, "children"], [1408, 22, 1113, 44], [1408, 37, 1115, 14], [1408, 41, 1115, 14, "_jsxDevRuntime"], [1408, 55, 1115, 14], [1408, 56, 1115, 14, "jsxDEV"], [1408, 62, 1115, 14], [1408, 64, 1115, 15, "_Text"], [1408, 69, 1115, 15], [1408, 70, 1115, 15, "default"], [1408, 77, 1115, 19], [1409, 16, 1115, 20, "style"], [1409, 21, 1115, 25], [1409, 23, 1115, 27, "styles"], [1409, 29, 1115, 33], [1409, 30, 1115, 34, "secondaryButtonText"], [1409, 49, 1115, 54], [1410, 16, 1115, 54, "children"], [1410, 24, 1115, 54], [1410, 26, 1115, 55], [1411, 14, 1115, 61], [1412, 16, 1115, 61, "fileName"], [1412, 24, 1115, 61], [1412, 26, 1115, 61, "_jsxFileName"], [1412, 38, 1115, 61], [1413, 16, 1115, 61, "lineNumber"], [1413, 26, 1115, 61], [1414, 16, 1115, 61, "columnNumber"], [1414, 28, 1115, 61], [1415, 14, 1115, 61], [1415, 21, 1115, 67], [1416, 12, 1115, 68], [1417, 14, 1115, 68, "fileName"], [1417, 22, 1115, 68], [1417, 24, 1115, 68, "_jsxFileName"], [1417, 36, 1115, 68], [1418, 14, 1115, 68, "lineNumber"], [1418, 24, 1115, 68], [1419, 14, 1115, 68, "columnNumber"], [1419, 26, 1115, 68], [1420, 12, 1115, 68], [1420, 19, 1116, 30], [1420, 20, 1116, 31], [1421, 10, 1116, 31], [1422, 12, 1116, 31, "fileName"], [1422, 20, 1116, 31], [1422, 22, 1116, 31, "_jsxFileName"], [1422, 34, 1116, 31], [1423, 12, 1116, 31, "lineNumber"], [1423, 22, 1116, 31], [1424, 12, 1116, 31, "columnNumber"], [1424, 24, 1116, 31], [1425, 10, 1116, 31], [1425, 17, 1117, 16], [1426, 8, 1117, 17], [1427, 10, 1117, 17, "fileName"], [1427, 18, 1117, 17], [1427, 20, 1117, 17, "_jsxFileName"], [1427, 32, 1117, 17], [1428, 10, 1117, 17, "lineNumber"], [1428, 20, 1117, 17], [1429, 10, 1117, 17, "columnNumber"], [1429, 22, 1117, 17], [1430, 8, 1117, 17], [1430, 15, 1118, 14], [1431, 6, 1118, 15], [1432, 8, 1118, 15, "fileName"], [1432, 16, 1118, 15], [1432, 18, 1118, 15, "_jsxFileName"], [1432, 30, 1118, 15], [1433, 8, 1118, 15, "lineNumber"], [1433, 18, 1118, 15], [1434, 8, 1118, 15, "columnNumber"], [1434, 20, 1118, 15], [1435, 6, 1118, 15], [1435, 13, 1119, 13], [1435, 14, 1119, 14], [1436, 4, 1119, 14], [1437, 6, 1119, 14, "fileName"], [1437, 14, 1119, 14], [1437, 16, 1119, 14, "_jsxFileName"], [1437, 28, 1119, 14], [1438, 6, 1119, 14, "lineNumber"], [1438, 16, 1119, 14], [1439, 6, 1119, 14, "columnNumber"], [1439, 18, 1119, 14], [1440, 4, 1119, 14], [1440, 11, 1120, 10], [1440, 12, 1120, 11], [1441, 2, 1122, 0], [1442, 2, 1122, 1, "_s"], [1442, 4, 1122, 1], [1442, 5, 51, 24, "EchoCameraWeb"], [1442, 18, 51, 37], [1443, 4, 51, 37], [1443, 12, 58, 42, "useCameraPermissions"], [1443, 44, 58, 62], [1443, 46, 72, 19, "useUpload"], [1443, 64, 72, 28], [1444, 2, 72, 28], [1445, 2, 72, 28, "_c"], [1445, 4, 72, 28], [1445, 7, 51, 24, "EchoCameraWeb"], [1445, 20, 51, 37], [1446, 2, 1123, 0], [1446, 8, 1123, 6, "styles"], [1446, 14, 1123, 12], [1446, 17, 1123, 15, "StyleSheet"], [1446, 36, 1123, 25], [1446, 37, 1123, 26, "create"], [1446, 43, 1123, 32], [1446, 44, 1123, 33], [1447, 4, 1124, 2, "container"], [1447, 13, 1124, 11], [1447, 15, 1124, 13], [1448, 6, 1125, 4, "flex"], [1448, 10, 1125, 8], [1448, 12, 1125, 10], [1448, 13, 1125, 11], [1449, 6, 1126, 4, "backgroundColor"], [1449, 21, 1126, 19], [1449, 23, 1126, 21], [1450, 4, 1127, 2], [1450, 5, 1127, 3], [1451, 4, 1128, 2, "cameraContainer"], [1451, 19, 1128, 17], [1451, 21, 1128, 19], [1452, 6, 1129, 4, "flex"], [1452, 10, 1129, 8], [1452, 12, 1129, 10], [1452, 13, 1129, 11], [1453, 6, 1130, 4, "max<PERSON><PERSON><PERSON>"], [1453, 14, 1130, 12], [1453, 16, 1130, 14], [1453, 19, 1130, 17], [1454, 6, 1131, 4, "alignSelf"], [1454, 15, 1131, 13], [1454, 17, 1131, 15], [1454, 25, 1131, 23], [1455, 6, 1132, 4, "width"], [1455, 11, 1132, 9], [1455, 13, 1132, 11], [1456, 4, 1133, 2], [1456, 5, 1133, 3], [1457, 4, 1134, 2, "camera"], [1457, 10, 1134, 8], [1457, 12, 1134, 10], [1458, 6, 1135, 4, "flex"], [1458, 10, 1135, 8], [1458, 12, 1135, 10], [1459, 4, 1136, 2], [1459, 5, 1136, 3], [1460, 4, 1137, 2, "headerOverlay"], [1460, 17, 1137, 15], [1460, 19, 1137, 17], [1461, 6, 1138, 4, "position"], [1461, 14, 1138, 12], [1461, 16, 1138, 14], [1461, 26, 1138, 24], [1462, 6, 1139, 4, "top"], [1462, 9, 1139, 7], [1462, 11, 1139, 9], [1462, 12, 1139, 10], [1463, 6, 1140, 4, "left"], [1463, 10, 1140, 8], [1463, 12, 1140, 10], [1463, 13, 1140, 11], [1464, 6, 1141, 4, "right"], [1464, 11, 1141, 9], [1464, 13, 1141, 11], [1464, 14, 1141, 12], [1465, 6, 1142, 4, "backgroundColor"], [1465, 21, 1142, 19], [1465, 23, 1142, 21], [1465, 36, 1142, 34], [1466, 6, 1143, 4, "paddingTop"], [1466, 16, 1143, 14], [1466, 18, 1143, 16], [1466, 20, 1143, 18], [1467, 6, 1144, 4, "paddingHorizontal"], [1467, 23, 1144, 21], [1467, 25, 1144, 23], [1467, 27, 1144, 25], [1468, 6, 1145, 4, "paddingBottom"], [1468, 19, 1145, 17], [1468, 21, 1145, 19], [1469, 4, 1146, 2], [1469, 5, 1146, 3], [1470, 4, 1147, 2, "headerContent"], [1470, 17, 1147, 15], [1470, 19, 1147, 17], [1471, 6, 1148, 4, "flexDirection"], [1471, 19, 1148, 17], [1471, 21, 1148, 19], [1471, 26, 1148, 24], [1472, 6, 1149, 4, "justifyContent"], [1472, 20, 1149, 18], [1472, 22, 1149, 20], [1472, 37, 1149, 35], [1473, 6, 1150, 4, "alignItems"], [1473, 16, 1150, 14], [1473, 18, 1150, 16], [1474, 4, 1151, 2], [1474, 5, 1151, 3], [1475, 4, 1152, 2, "headerLeft"], [1475, 14, 1152, 12], [1475, 16, 1152, 14], [1476, 6, 1153, 4, "flex"], [1476, 10, 1153, 8], [1476, 12, 1153, 10], [1477, 4, 1154, 2], [1477, 5, 1154, 3], [1478, 4, 1155, 2, "headerTitle"], [1478, 15, 1155, 13], [1478, 17, 1155, 15], [1479, 6, 1156, 4, "fontSize"], [1479, 14, 1156, 12], [1479, 16, 1156, 14], [1479, 18, 1156, 16], [1480, 6, 1157, 4, "fontWeight"], [1480, 16, 1157, 14], [1480, 18, 1157, 16], [1480, 23, 1157, 21], [1481, 6, 1158, 4, "color"], [1481, 11, 1158, 9], [1481, 13, 1158, 11], [1481, 19, 1158, 17], [1482, 6, 1159, 4, "marginBottom"], [1482, 18, 1159, 16], [1482, 20, 1159, 18], [1483, 4, 1160, 2], [1483, 5, 1160, 3], [1484, 4, 1161, 2, "subtitleRow"], [1484, 15, 1161, 13], [1484, 17, 1161, 15], [1485, 6, 1162, 4, "flexDirection"], [1485, 19, 1162, 17], [1485, 21, 1162, 19], [1485, 26, 1162, 24], [1486, 6, 1163, 4, "alignItems"], [1486, 16, 1163, 14], [1486, 18, 1163, 16], [1486, 26, 1163, 24], [1487, 6, 1164, 4, "marginBottom"], [1487, 18, 1164, 16], [1487, 20, 1164, 18], [1488, 4, 1165, 2], [1488, 5, 1165, 3], [1489, 4, 1166, 2, "webIcon"], [1489, 11, 1166, 9], [1489, 13, 1166, 11], [1490, 6, 1167, 4, "fontSize"], [1490, 14, 1167, 12], [1490, 16, 1167, 14], [1490, 18, 1167, 16], [1491, 6, 1168, 4, "marginRight"], [1491, 17, 1168, 15], [1491, 19, 1168, 17], [1492, 4, 1169, 2], [1492, 5, 1169, 3], [1493, 4, 1170, 2, "headerSubtitle"], [1493, 18, 1170, 16], [1493, 20, 1170, 18], [1494, 6, 1171, 4, "fontSize"], [1494, 14, 1171, 12], [1494, 16, 1171, 14], [1494, 18, 1171, 16], [1495, 6, 1172, 4, "color"], [1495, 11, 1172, 9], [1495, 13, 1172, 11], [1495, 19, 1172, 17], [1496, 6, 1173, 4, "opacity"], [1496, 13, 1173, 11], [1496, 15, 1173, 13], [1497, 4, 1174, 2], [1497, 5, 1174, 3], [1498, 4, 1175, 2, "challengeRow"], [1498, 16, 1175, 14], [1498, 18, 1175, 16], [1499, 6, 1176, 4, "flexDirection"], [1499, 19, 1176, 17], [1499, 21, 1176, 19], [1499, 26, 1176, 24], [1500, 6, 1177, 4, "alignItems"], [1500, 16, 1177, 14], [1500, 18, 1177, 16], [1501, 4, 1178, 2], [1501, 5, 1178, 3], [1502, 4, 1179, 2, "challengeCode"], [1502, 17, 1179, 15], [1502, 19, 1179, 17], [1503, 6, 1180, 4, "fontSize"], [1503, 14, 1180, 12], [1503, 16, 1180, 14], [1503, 18, 1180, 16], [1504, 6, 1181, 4, "color"], [1504, 11, 1181, 9], [1504, 13, 1181, 11], [1504, 19, 1181, 17], [1505, 6, 1182, 4, "marginLeft"], [1505, 16, 1182, 14], [1505, 18, 1182, 16], [1505, 19, 1182, 17], [1506, 6, 1183, 4, "fontFamily"], [1506, 16, 1183, 14], [1506, 18, 1183, 16], [1507, 4, 1184, 2], [1507, 5, 1184, 3], [1508, 4, 1185, 2, "closeButton"], [1508, 15, 1185, 13], [1508, 17, 1185, 15], [1509, 6, 1186, 4, "padding"], [1509, 13, 1186, 11], [1509, 15, 1186, 13], [1510, 4, 1187, 2], [1510, 5, 1187, 3], [1511, 4, 1188, 2, "privacyNotice"], [1511, 17, 1188, 15], [1511, 19, 1188, 17], [1512, 6, 1189, 4, "position"], [1512, 14, 1189, 12], [1512, 16, 1189, 14], [1512, 26, 1189, 24], [1513, 6, 1190, 4, "top"], [1513, 9, 1190, 7], [1513, 11, 1190, 9], [1513, 14, 1190, 12], [1514, 6, 1191, 4, "left"], [1514, 10, 1191, 8], [1514, 12, 1191, 10], [1514, 14, 1191, 12], [1515, 6, 1192, 4, "right"], [1515, 11, 1192, 9], [1515, 13, 1192, 11], [1515, 15, 1192, 13], [1516, 6, 1193, 4, "backgroundColor"], [1516, 21, 1193, 19], [1516, 23, 1193, 21], [1516, 48, 1193, 46], [1517, 6, 1194, 4, "borderRadius"], [1517, 18, 1194, 16], [1517, 20, 1194, 18], [1517, 21, 1194, 19], [1518, 6, 1195, 4, "padding"], [1518, 13, 1195, 11], [1518, 15, 1195, 13], [1518, 17, 1195, 15], [1519, 6, 1196, 4, "flexDirection"], [1519, 19, 1196, 17], [1519, 21, 1196, 19], [1519, 26, 1196, 24], [1520, 6, 1197, 4, "alignItems"], [1520, 16, 1197, 14], [1520, 18, 1197, 16], [1521, 4, 1198, 2], [1521, 5, 1198, 3], [1522, 4, 1199, 2, "privacyText"], [1522, 15, 1199, 13], [1522, 17, 1199, 15], [1523, 6, 1200, 4, "color"], [1523, 11, 1200, 9], [1523, 13, 1200, 11], [1523, 19, 1200, 17], [1524, 6, 1201, 4, "fontSize"], [1524, 14, 1201, 12], [1524, 16, 1201, 14], [1524, 18, 1201, 16], [1525, 6, 1202, 4, "marginLeft"], [1525, 16, 1202, 14], [1525, 18, 1202, 16], [1525, 19, 1202, 17], [1526, 6, 1203, 4, "flex"], [1526, 10, 1203, 8], [1526, 12, 1203, 10], [1527, 4, 1204, 2], [1527, 5, 1204, 3], [1528, 4, 1205, 2, "footer<PERSON><PERSON><PERSON>"], [1528, 17, 1205, 15], [1528, 19, 1205, 17], [1529, 6, 1206, 4, "position"], [1529, 14, 1206, 12], [1529, 16, 1206, 14], [1529, 26, 1206, 24], [1530, 6, 1207, 4, "bottom"], [1530, 12, 1207, 10], [1530, 14, 1207, 12], [1530, 15, 1207, 13], [1531, 6, 1208, 4, "left"], [1531, 10, 1208, 8], [1531, 12, 1208, 10], [1531, 13, 1208, 11], [1532, 6, 1209, 4, "right"], [1532, 11, 1209, 9], [1532, 13, 1209, 11], [1532, 14, 1209, 12], [1533, 6, 1210, 4, "backgroundColor"], [1533, 21, 1210, 19], [1533, 23, 1210, 21], [1533, 36, 1210, 34], [1534, 6, 1211, 4, "paddingBottom"], [1534, 19, 1211, 17], [1534, 21, 1211, 19], [1534, 23, 1211, 21], [1535, 6, 1212, 4, "paddingTop"], [1535, 16, 1212, 14], [1535, 18, 1212, 16], [1535, 20, 1212, 18], [1536, 6, 1213, 4, "alignItems"], [1536, 16, 1213, 14], [1536, 18, 1213, 16], [1537, 4, 1214, 2], [1537, 5, 1214, 3], [1538, 4, 1215, 2, "instruction"], [1538, 15, 1215, 13], [1538, 17, 1215, 15], [1539, 6, 1216, 4, "fontSize"], [1539, 14, 1216, 12], [1539, 16, 1216, 14], [1539, 18, 1216, 16], [1540, 6, 1217, 4, "color"], [1540, 11, 1217, 9], [1540, 13, 1217, 11], [1540, 19, 1217, 17], [1541, 6, 1218, 4, "marginBottom"], [1541, 18, 1218, 16], [1541, 20, 1218, 18], [1542, 4, 1219, 2], [1542, 5, 1219, 3], [1543, 4, 1220, 2, "shutterButton"], [1543, 17, 1220, 15], [1543, 19, 1220, 17], [1544, 6, 1221, 4, "width"], [1544, 11, 1221, 9], [1544, 13, 1221, 11], [1544, 15, 1221, 13], [1545, 6, 1222, 4, "height"], [1545, 12, 1222, 10], [1545, 14, 1222, 12], [1545, 16, 1222, 14], [1546, 6, 1223, 4, "borderRadius"], [1546, 18, 1223, 16], [1546, 20, 1223, 18], [1546, 22, 1223, 20], [1547, 6, 1224, 4, "backgroundColor"], [1547, 21, 1224, 19], [1547, 23, 1224, 21], [1547, 29, 1224, 27], [1548, 6, 1225, 4, "justifyContent"], [1548, 20, 1225, 18], [1548, 22, 1225, 20], [1548, 30, 1225, 28], [1549, 6, 1226, 4, "alignItems"], [1549, 16, 1226, 14], [1549, 18, 1226, 16], [1549, 26, 1226, 24], [1550, 6, 1227, 4, "marginBottom"], [1550, 18, 1227, 16], [1550, 20, 1227, 18], [1550, 22, 1227, 20], [1551, 6, 1228, 4], [1551, 9, 1228, 7, "Platform"], [1551, 26, 1228, 15], [1551, 27, 1228, 16, "select"], [1551, 33, 1228, 22], [1551, 34, 1228, 23], [1552, 8, 1229, 6, "ios"], [1552, 11, 1229, 9], [1552, 13, 1229, 11], [1553, 10, 1230, 8, "shadowColor"], [1553, 21, 1230, 19], [1553, 23, 1230, 21], [1553, 32, 1230, 30], [1554, 10, 1231, 8, "shadowOffset"], [1554, 22, 1231, 20], [1554, 24, 1231, 22], [1555, 12, 1231, 24, "width"], [1555, 17, 1231, 29], [1555, 19, 1231, 31], [1555, 20, 1231, 32], [1556, 12, 1231, 34, "height"], [1556, 18, 1231, 40], [1556, 20, 1231, 42], [1557, 10, 1231, 44], [1557, 11, 1231, 45], [1558, 10, 1232, 8, "shadowOpacity"], [1558, 23, 1232, 21], [1558, 25, 1232, 23], [1558, 28, 1232, 26], [1559, 10, 1233, 8, "shadowRadius"], [1559, 22, 1233, 20], [1559, 24, 1233, 22], [1560, 8, 1234, 6], [1560, 9, 1234, 7], [1561, 8, 1235, 6, "android"], [1561, 15, 1235, 13], [1561, 17, 1235, 15], [1562, 10, 1236, 8, "elevation"], [1562, 19, 1236, 17], [1562, 21, 1236, 19], [1563, 8, 1237, 6], [1563, 9, 1237, 7], [1564, 8, 1238, 6, "web"], [1564, 11, 1238, 9], [1564, 13, 1238, 11], [1565, 10, 1239, 8, "boxShadow"], [1565, 19, 1239, 17], [1565, 21, 1239, 19], [1566, 8, 1240, 6], [1567, 6, 1241, 4], [1567, 7, 1241, 5], [1568, 4, 1242, 2], [1568, 5, 1242, 3], [1569, 4, 1243, 2, "shutterButtonDisabled"], [1569, 25, 1243, 23], [1569, 27, 1243, 25], [1570, 6, 1244, 4, "opacity"], [1570, 13, 1244, 11], [1570, 15, 1244, 13], [1571, 4, 1245, 2], [1571, 5, 1245, 3], [1572, 4, 1246, 2, "shutterInner"], [1572, 16, 1246, 14], [1572, 18, 1246, 16], [1573, 6, 1247, 4, "width"], [1573, 11, 1247, 9], [1573, 13, 1247, 11], [1573, 15, 1247, 13], [1574, 6, 1248, 4, "height"], [1574, 12, 1248, 10], [1574, 14, 1248, 12], [1574, 16, 1248, 14], [1575, 6, 1249, 4, "borderRadius"], [1575, 18, 1249, 16], [1575, 20, 1249, 18], [1575, 22, 1249, 20], [1576, 6, 1250, 4, "backgroundColor"], [1576, 21, 1250, 19], [1576, 23, 1250, 21], [1576, 29, 1250, 27], [1577, 6, 1251, 4, "borderWidth"], [1577, 17, 1251, 15], [1577, 19, 1251, 17], [1577, 20, 1251, 18], [1578, 6, 1252, 4, "borderColor"], [1578, 17, 1252, 15], [1578, 19, 1252, 17], [1579, 4, 1253, 2], [1579, 5, 1253, 3], [1580, 4, 1254, 2, "privacyNote"], [1580, 15, 1254, 13], [1580, 17, 1254, 15], [1581, 6, 1255, 4, "fontSize"], [1581, 14, 1255, 12], [1581, 16, 1255, 14], [1581, 18, 1255, 16], [1582, 6, 1256, 4, "color"], [1582, 11, 1256, 9], [1582, 13, 1256, 11], [1583, 4, 1257, 2], [1583, 5, 1257, 3], [1584, 4, 1258, 2, "processingModal"], [1584, 19, 1258, 17], [1584, 21, 1258, 19], [1585, 6, 1259, 4, "flex"], [1585, 10, 1259, 8], [1585, 12, 1259, 10], [1585, 13, 1259, 11], [1586, 6, 1260, 4, "backgroundColor"], [1586, 21, 1260, 19], [1586, 23, 1260, 21], [1586, 43, 1260, 41], [1587, 6, 1261, 4, "justifyContent"], [1587, 20, 1261, 18], [1587, 22, 1261, 20], [1587, 30, 1261, 28], [1588, 6, 1262, 4, "alignItems"], [1588, 16, 1262, 14], [1588, 18, 1262, 16], [1589, 4, 1263, 2], [1589, 5, 1263, 3], [1590, 4, 1264, 2, "processingContent"], [1590, 21, 1264, 19], [1590, 23, 1264, 21], [1591, 6, 1265, 4, "backgroundColor"], [1591, 21, 1265, 19], [1591, 23, 1265, 21], [1591, 29, 1265, 27], [1592, 6, 1266, 4, "borderRadius"], [1592, 18, 1266, 16], [1592, 20, 1266, 18], [1592, 22, 1266, 20], [1593, 6, 1267, 4, "padding"], [1593, 13, 1267, 11], [1593, 15, 1267, 13], [1593, 17, 1267, 15], [1594, 6, 1268, 4, "width"], [1594, 11, 1268, 9], [1594, 13, 1268, 11], [1594, 18, 1268, 16], [1595, 6, 1269, 4, "max<PERSON><PERSON><PERSON>"], [1595, 14, 1269, 12], [1595, 16, 1269, 14], [1595, 19, 1269, 17], [1596, 6, 1270, 4, "alignItems"], [1596, 16, 1270, 14], [1596, 18, 1270, 16], [1597, 4, 1271, 2], [1597, 5, 1271, 3], [1598, 4, 1272, 2, "processingTitle"], [1598, 19, 1272, 17], [1598, 21, 1272, 19], [1599, 6, 1273, 4, "fontSize"], [1599, 14, 1273, 12], [1599, 16, 1273, 14], [1599, 18, 1273, 16], [1600, 6, 1274, 4, "fontWeight"], [1600, 16, 1274, 14], [1600, 18, 1274, 16], [1600, 23, 1274, 21], [1601, 6, 1275, 4, "color"], [1601, 11, 1275, 9], [1601, 13, 1275, 11], [1601, 22, 1275, 20], [1602, 6, 1276, 4, "marginTop"], [1602, 15, 1276, 13], [1602, 17, 1276, 15], [1602, 19, 1276, 17], [1603, 6, 1277, 4, "marginBottom"], [1603, 18, 1277, 16], [1603, 20, 1277, 18], [1604, 4, 1278, 2], [1604, 5, 1278, 3], [1605, 4, 1279, 2, "progressBar"], [1605, 15, 1279, 13], [1605, 17, 1279, 15], [1606, 6, 1280, 4, "width"], [1606, 11, 1280, 9], [1606, 13, 1280, 11], [1606, 19, 1280, 17], [1607, 6, 1281, 4, "height"], [1607, 12, 1281, 10], [1607, 14, 1281, 12], [1607, 15, 1281, 13], [1608, 6, 1282, 4, "backgroundColor"], [1608, 21, 1282, 19], [1608, 23, 1282, 21], [1608, 32, 1282, 30], [1609, 6, 1283, 4, "borderRadius"], [1609, 18, 1283, 16], [1609, 20, 1283, 18], [1609, 21, 1283, 19], [1610, 6, 1284, 4, "overflow"], [1610, 14, 1284, 12], [1610, 16, 1284, 14], [1610, 24, 1284, 22], [1611, 6, 1285, 4, "marginBottom"], [1611, 18, 1285, 16], [1611, 20, 1285, 18], [1612, 4, 1286, 2], [1612, 5, 1286, 3], [1613, 4, 1287, 2, "progressFill"], [1613, 16, 1287, 14], [1613, 18, 1287, 16], [1614, 6, 1288, 4, "height"], [1614, 12, 1288, 10], [1614, 14, 1288, 12], [1614, 20, 1288, 18], [1615, 6, 1289, 4, "backgroundColor"], [1615, 21, 1289, 19], [1615, 23, 1289, 21], [1615, 32, 1289, 30], [1616, 6, 1290, 4, "borderRadius"], [1616, 18, 1290, 16], [1616, 20, 1290, 18], [1617, 4, 1291, 2], [1617, 5, 1291, 3], [1618, 4, 1292, 2, "processingDescription"], [1618, 25, 1292, 23], [1618, 27, 1292, 25], [1619, 6, 1293, 4, "fontSize"], [1619, 14, 1293, 12], [1619, 16, 1293, 14], [1619, 18, 1293, 16], [1620, 6, 1294, 4, "color"], [1620, 11, 1294, 9], [1620, 13, 1294, 11], [1620, 22, 1294, 20], [1621, 6, 1295, 4, "textAlign"], [1621, 15, 1295, 13], [1621, 17, 1295, 15], [1622, 4, 1296, 2], [1622, 5, 1296, 3], [1623, 4, 1297, 2, "successIcon"], [1623, 15, 1297, 13], [1623, 17, 1297, 15], [1624, 6, 1298, 4, "marginTop"], [1624, 15, 1298, 13], [1624, 17, 1298, 15], [1625, 4, 1299, 2], [1625, 5, 1299, 3], [1626, 4, 1300, 2, "errorContent"], [1626, 16, 1300, 14], [1626, 18, 1300, 16], [1627, 6, 1301, 4, "backgroundColor"], [1627, 21, 1301, 19], [1627, 23, 1301, 21], [1627, 29, 1301, 27], [1628, 6, 1302, 4, "borderRadius"], [1628, 18, 1302, 16], [1628, 20, 1302, 18], [1628, 22, 1302, 20], [1629, 6, 1303, 4, "padding"], [1629, 13, 1303, 11], [1629, 15, 1303, 13], [1629, 17, 1303, 15], [1630, 6, 1304, 4, "width"], [1630, 11, 1304, 9], [1630, 13, 1304, 11], [1630, 18, 1304, 16], [1631, 6, 1305, 4, "max<PERSON><PERSON><PERSON>"], [1631, 14, 1305, 12], [1631, 16, 1305, 14], [1631, 19, 1305, 17], [1632, 6, 1306, 4, "alignItems"], [1632, 16, 1306, 14], [1632, 18, 1306, 16], [1633, 4, 1307, 2], [1633, 5, 1307, 3], [1634, 4, 1308, 2, "errorTitle"], [1634, 14, 1308, 12], [1634, 16, 1308, 14], [1635, 6, 1309, 4, "fontSize"], [1635, 14, 1309, 12], [1635, 16, 1309, 14], [1635, 18, 1309, 16], [1636, 6, 1310, 4, "fontWeight"], [1636, 16, 1310, 14], [1636, 18, 1310, 16], [1636, 23, 1310, 21], [1637, 6, 1311, 4, "color"], [1637, 11, 1311, 9], [1637, 13, 1311, 11], [1637, 22, 1311, 20], [1638, 6, 1312, 4, "marginTop"], [1638, 15, 1312, 13], [1638, 17, 1312, 15], [1638, 19, 1312, 17], [1639, 6, 1313, 4, "marginBottom"], [1639, 18, 1313, 16], [1639, 20, 1313, 18], [1640, 4, 1314, 2], [1640, 5, 1314, 3], [1641, 4, 1315, 2, "errorMessage"], [1641, 16, 1315, 14], [1641, 18, 1315, 16], [1642, 6, 1316, 4, "fontSize"], [1642, 14, 1316, 12], [1642, 16, 1316, 14], [1642, 18, 1316, 16], [1643, 6, 1317, 4, "color"], [1643, 11, 1317, 9], [1643, 13, 1317, 11], [1643, 22, 1317, 20], [1644, 6, 1318, 4, "textAlign"], [1644, 15, 1318, 13], [1644, 17, 1318, 15], [1644, 25, 1318, 23], [1645, 6, 1319, 4, "marginBottom"], [1645, 18, 1319, 16], [1645, 20, 1319, 18], [1646, 4, 1320, 2], [1646, 5, 1320, 3], [1647, 4, 1321, 2, "primaryButton"], [1647, 17, 1321, 15], [1647, 19, 1321, 17], [1648, 6, 1322, 4, "backgroundColor"], [1648, 21, 1322, 19], [1648, 23, 1322, 21], [1648, 32, 1322, 30], [1649, 6, 1323, 4, "paddingHorizontal"], [1649, 23, 1323, 21], [1649, 25, 1323, 23], [1649, 27, 1323, 25], [1650, 6, 1324, 4, "paddingVertical"], [1650, 21, 1324, 19], [1650, 23, 1324, 21], [1650, 25, 1324, 23], [1651, 6, 1325, 4, "borderRadius"], [1651, 18, 1325, 16], [1651, 20, 1325, 18], [1651, 21, 1325, 19], [1652, 6, 1326, 4, "marginTop"], [1652, 15, 1326, 13], [1652, 17, 1326, 15], [1653, 4, 1327, 2], [1653, 5, 1327, 3], [1654, 4, 1328, 2, "primaryButtonText"], [1654, 21, 1328, 19], [1654, 23, 1328, 21], [1655, 6, 1329, 4, "color"], [1655, 11, 1329, 9], [1655, 13, 1329, 11], [1655, 19, 1329, 17], [1656, 6, 1330, 4, "fontSize"], [1656, 14, 1330, 12], [1656, 16, 1330, 14], [1656, 18, 1330, 16], [1657, 6, 1331, 4, "fontWeight"], [1657, 16, 1331, 14], [1657, 18, 1331, 16], [1658, 4, 1332, 2], [1658, 5, 1332, 3], [1659, 4, 1333, 2, "secondaryButton"], [1659, 19, 1333, 17], [1659, 21, 1333, 19], [1660, 6, 1334, 4, "paddingHorizontal"], [1660, 23, 1334, 21], [1660, 25, 1334, 23], [1660, 27, 1334, 25], [1661, 6, 1335, 4, "paddingVertical"], [1661, 21, 1335, 19], [1661, 23, 1335, 21], [1661, 25, 1335, 23], [1662, 6, 1336, 4, "marginTop"], [1662, 15, 1336, 13], [1662, 17, 1336, 15], [1663, 4, 1337, 2], [1663, 5, 1337, 3], [1664, 4, 1338, 2, "secondaryButtonText"], [1664, 23, 1338, 21], [1664, 25, 1338, 23], [1665, 6, 1339, 4, "color"], [1665, 11, 1339, 9], [1665, 13, 1339, 11], [1665, 22, 1339, 20], [1666, 6, 1340, 4, "fontSize"], [1666, 14, 1340, 12], [1666, 16, 1340, 14], [1667, 4, 1341, 2], [1667, 5, 1341, 3], [1668, 4, 1342, 2, "permissionContent"], [1668, 21, 1342, 19], [1668, 23, 1342, 21], [1669, 6, 1343, 4, "flex"], [1669, 10, 1343, 8], [1669, 12, 1343, 10], [1669, 13, 1343, 11], [1670, 6, 1344, 4, "justifyContent"], [1670, 20, 1344, 18], [1670, 22, 1344, 20], [1670, 30, 1344, 28], [1671, 6, 1345, 4, "alignItems"], [1671, 16, 1345, 14], [1671, 18, 1345, 16], [1671, 26, 1345, 24], [1672, 6, 1346, 4, "padding"], [1672, 13, 1346, 11], [1672, 15, 1346, 13], [1673, 4, 1347, 2], [1673, 5, 1347, 3], [1674, 4, 1348, 2, "permissionTitle"], [1674, 19, 1348, 17], [1674, 21, 1348, 19], [1675, 6, 1349, 4, "fontSize"], [1675, 14, 1349, 12], [1675, 16, 1349, 14], [1675, 18, 1349, 16], [1676, 6, 1350, 4, "fontWeight"], [1676, 16, 1350, 14], [1676, 18, 1350, 16], [1676, 23, 1350, 21], [1677, 6, 1351, 4, "color"], [1677, 11, 1351, 9], [1677, 13, 1351, 11], [1677, 22, 1351, 20], [1678, 6, 1352, 4, "marginTop"], [1678, 15, 1352, 13], [1678, 17, 1352, 15], [1678, 19, 1352, 17], [1679, 6, 1353, 4, "marginBottom"], [1679, 18, 1353, 16], [1679, 20, 1353, 18], [1680, 4, 1354, 2], [1680, 5, 1354, 3], [1681, 4, 1355, 2, "permissionDescription"], [1681, 25, 1355, 23], [1681, 27, 1355, 25], [1682, 6, 1356, 4, "fontSize"], [1682, 14, 1356, 12], [1682, 16, 1356, 14], [1682, 18, 1356, 16], [1683, 6, 1357, 4, "color"], [1683, 11, 1357, 9], [1683, 13, 1357, 11], [1683, 22, 1357, 20], [1684, 6, 1358, 4, "textAlign"], [1684, 15, 1358, 13], [1684, 17, 1358, 15], [1684, 25, 1358, 23], [1685, 6, 1359, 4, "marginBottom"], [1685, 18, 1359, 16], [1685, 20, 1359, 18], [1686, 4, 1360, 2], [1686, 5, 1360, 3], [1687, 4, 1361, 2, "loadingText"], [1687, 15, 1361, 13], [1687, 17, 1361, 15], [1688, 6, 1362, 4, "color"], [1688, 11, 1362, 9], [1688, 13, 1362, 11], [1688, 22, 1362, 20], [1689, 6, 1363, 4, "marginTop"], [1689, 15, 1363, 13], [1689, 17, 1363, 15], [1690, 4, 1364, 2], [1690, 5, 1364, 3], [1691, 4, 1365, 2], [1692, 4, 1366, 2, "blurZone"], [1692, 12, 1366, 10], [1692, 14, 1366, 12], [1693, 6, 1367, 4, "position"], [1693, 14, 1367, 12], [1693, 16, 1367, 14], [1693, 26, 1367, 24], [1694, 6, 1368, 4, "overflow"], [1694, 14, 1368, 12], [1694, 16, 1368, 14], [1695, 4, 1369, 2], [1695, 5, 1369, 3], [1696, 4, 1370, 2, "previewChip"], [1696, 15, 1370, 13], [1696, 17, 1370, 15], [1697, 6, 1371, 4, "position"], [1697, 14, 1371, 12], [1697, 16, 1371, 14], [1697, 26, 1371, 24], [1698, 6, 1372, 4, "top"], [1698, 9, 1372, 7], [1698, 11, 1372, 9], [1698, 12, 1372, 10], [1699, 6, 1373, 4, "right"], [1699, 11, 1373, 9], [1699, 13, 1373, 11], [1699, 14, 1373, 12], [1700, 6, 1374, 4, "backgroundColor"], [1700, 21, 1374, 19], [1700, 23, 1374, 21], [1700, 40, 1374, 38], [1701, 6, 1375, 4, "paddingHorizontal"], [1701, 23, 1375, 21], [1701, 25, 1375, 23], [1701, 27, 1375, 25], [1702, 6, 1376, 4, "paddingVertical"], [1702, 21, 1376, 19], [1702, 23, 1376, 21], [1702, 24, 1376, 22], [1703, 6, 1377, 4, "borderRadius"], [1703, 18, 1377, 16], [1703, 20, 1377, 18], [1704, 4, 1378, 2], [1704, 5, 1378, 3], [1705, 4, 1379, 2, "previewChipText"], [1705, 19, 1379, 17], [1705, 21, 1379, 19], [1706, 6, 1380, 4, "color"], [1706, 11, 1380, 9], [1706, 13, 1380, 11], [1706, 19, 1380, 17], [1707, 6, 1381, 4, "fontSize"], [1707, 14, 1381, 12], [1707, 16, 1381, 14], [1707, 18, 1381, 16], [1708, 6, 1382, 4, "fontWeight"], [1708, 16, 1382, 14], [1708, 18, 1382, 16], [1709, 4, 1383, 2], [1710, 2, 1384, 0], [1710, 3, 1384, 1], [1710, 4, 1384, 2], [1711, 2, 1384, 3], [1711, 6, 1384, 3, "_c"], [1711, 8, 1384, 3], [1712, 2, 1384, 3, "$RefreshReg$"], [1712, 14, 1384, 3], [1712, 15, 1384, 3, "_c"], [1712, 17, 1384, 3], [1713, 0, 1384, 3], [1713, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfmE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBa;OgBI;oFC+C;UDM;8BE8B;SF0C;uDhBa;sBmBC,wBnB;OgBC;GpBmB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}