{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   *\n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation matches the working solution from test-blazeface-integration.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height,\n    onReady\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        return;\n      }\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size to match video dimensions when available\n      const updateCanvasSize = () => {\n        if (video.videoWidth && video.videoHeight) {\n          canvas.width = video.videoWidth;\n          canvas.height = video.videoHeight;\n          canvas.style.width = '100%';\n          canvas.style.height = '100%';\n          console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);\n        } else {\n          // Fallback to provided dimensions\n          canvas.width = width;\n          canvas.height = height;\n          console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);\n        }\n      };\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Helper function to load scripts\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n\n      // Load TensorFlow.js and BlazeFace model - matching working test implementation\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await window.blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Update canvas size once video is ready\n          updateCanvasSize();\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Check if video has valid dimensions\n          if (!video.videoWidth || !video.videoHeight) {\n            rafRef.current = requestAnimationFrame(detectLoop);\n            return;\n          }\n\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces with same confidence threshold as working test\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas and draw the original video frame first\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            setFaceCount(predictions.length);\n\n            // Process each detected face - REAL-TIME BLURRING like in test\n            predictions.forEach((prediction, index) => {\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect)\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply REAL-TIME elliptical blur - this creates the live preview blur\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(25px)'; // Stronger blur for better privacy\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n\n              // Add debug rectangle to show detection area (optional)\n              if (__DEV__) {\n                ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n                ctx.lineWidth = 2;\n                ctx.strokeRect(minX, minY, faceWidth, faceHeight);\n              }\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Wait for video to be ready before starting\n      const waitForVideoAndStart = () => {\n        if (video.readyState >= 2) {\n          // HAVE_CURRENT_DATA\n          loadModel();\n        } else {\n          video.addEventListener('loadeddata', loadModel, {\n            once: true\n          });\n        }\n      };\n\n      // Start the process\n      waitForVideoAndStart();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 15,\n          // Higher z-index to be above video\n          objectFit: 'cover',\n          backgroundColor: 'transparent'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(59, 130, 246, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(59, 130, 246, 0.3)'\n        },\n        children: \"Loading BlazeFace model...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(16, 185, 129, 0.3)'\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), !isLoading && faceCount === 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(107, 114, 128, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(107, 114, 128, 0.3)'\n        },\n        children: \"\\uD83D\\uDC40 Looking for faces...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 310, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 2, 17, 15], [18, 11, 17, 24, "BlazeFaceCanvas"], [18, 26, 17, 39, "BlazeFaceCanvas"], [18, 27, 17, 40], [19, 4, 17, 42, "containerId"], [19, 15, 17, 53], [20, 4, 17, 55, "width"], [20, 9, 17, 60], [21, 4, 17, 62, "height"], [21, 10, 17, 68], [22, 4, 17, 70, "onReady"], [23, 2, 17, 100], [23, 3, 17, 101], [23, 5, 17, 103], [24, 4, 17, 103, "_s"], [24, 6, 17, 103], [25, 4, 18, 2], [25, 10, 18, 8, "canvasRef"], [25, 19, 18, 17], [25, 22, 18, 20], [25, 26, 18, 20, "useRef"], [25, 39, 18, 26], [25, 41, 18, 53], [25, 45, 18, 57], [25, 46, 18, 58], [26, 4, 19, 2], [26, 10, 19, 8, "rafRef"], [26, 16, 19, 14], [26, 19, 19, 17], [26, 23, 19, 17, "useRef"], [26, 36, 19, 23], [26, 38, 19, 39], [26, 42, 19, 43], [26, 43, 19, 44], [27, 4, 20, 2], [27, 10, 20, 8, "modelRef"], [27, 18, 20, 16], [27, 21, 20, 19], [27, 25, 20, 19, "useRef"], [27, 38, 20, 25], [27, 40, 20, 38], [27, 44, 20, 42], [27, 45, 20, 43], [28, 4, 21, 2], [28, 10, 21, 8], [28, 11, 21, 9, "isLoading"], [28, 20, 21, 18], [28, 22, 21, 20, "setIsLoading"], [28, 34, 21, 32], [28, 35, 21, 33], [28, 38, 21, 36], [28, 42, 21, 36, "useState"], [28, 57, 21, 44], [28, 59, 21, 45], [28, 63, 21, 49], [28, 64, 21, 50], [29, 4, 22, 2], [29, 10, 22, 8], [29, 11, 22, 9, "faceCount"], [29, 20, 22, 18], [29, 22, 22, 20, "setFaceCount"], [29, 34, 22, 32], [29, 35, 22, 33], [29, 38, 22, 36], [29, 42, 22, 36, "useState"], [29, 57, 22, 44], [29, 59, 22, 45], [29, 60, 22, 46], [29, 61, 22, 47], [30, 4, 24, 2], [30, 8, 24, 2, "useEffect"], [30, 24, 24, 11], [30, 26, 24, 12], [30, 32, 24, 18], [31, 6, 25, 4, "console"], [31, 13, 25, 11], [31, 14, 25, 12, "log"], [31, 17, 25, 15], [31, 18, 25, 16], [31, 64, 25, 62], [31, 66, 25, 64], [32, 8, 25, 66, "containerId"], [32, 19, 25, 77], [33, 8, 25, 79, "width"], [33, 13, 25, 84], [34, 8, 25, 86, "height"], [35, 6, 25, 93], [35, 7, 25, 94], [35, 8, 25, 95], [36, 6, 27, 4], [36, 12, 27, 10, "container"], [36, 21, 27, 19], [36, 24, 27, 22, "document"], [36, 32, 27, 30], [36, 33, 27, 31, "getElementById"], [36, 47, 27, 45], [36, 48, 27, 46, "containerId"], [36, 59, 27, 57], [36, 60, 27, 58], [37, 6, 28, 4], [37, 10, 28, 8], [37, 11, 28, 9, "container"], [37, 20, 28, 18], [37, 22, 28, 20], [38, 8, 29, 6, "console"], [38, 15, 29, 13], [38, 16, 29, 14, "error"], [38, 21, 29, 19], [38, 22, 29, 20], [38, 62, 29, 60], [38, 64, 29, 62, "containerId"], [38, 75, 29, 73], [38, 76, 29, 74], [39, 8, 30, 6], [40, 6, 31, 4], [41, 6, 33, 4], [41, 12, 33, 10, "video"], [41, 17, 33, 40], [41, 20, 33, 43, "container"], [41, 29, 33, 52], [41, 30, 33, 53, "querySelector"], [41, 43, 33, 66], [41, 44, 33, 67], [41, 51, 33, 74], [41, 52, 33, 75], [42, 6, 34, 4], [42, 10, 34, 8], [42, 11, 34, 9, "video"], [42, 16, 34, 14], [42, 18, 34, 16], [43, 8, 35, 6, "console"], [43, 15, 35, 13], [43, 16, 35, 14, "error"], [43, 21, 35, 19], [43, 22, 35, 20], [43, 78, 35, 76], [43, 79, 35, 77], [44, 8, 36, 6], [45, 6, 37, 4], [46, 6, 39, 4], [46, 12, 39, 10, "canvas"], [46, 18, 39, 16], [46, 21, 39, 19, "canvasRef"], [46, 30, 39, 28], [46, 31, 39, 29, "current"], [46, 38, 39, 36], [47, 6, 40, 4], [47, 10, 40, 8], [47, 11, 40, 9, "canvas"], [47, 17, 40, 15], [47, 19, 40, 17], [48, 8, 41, 6, "console"], [48, 15, 41, 13], [48, 16, 41, 14, "error"], [48, 21, 41, 19], [48, 22, 41, 20], [48, 66, 41, 64], [48, 67, 41, 65], [49, 8, 42, 6], [50, 6, 43, 4], [52, 6, 45, 4], [53, 6, 46, 4], [53, 12, 46, 10, "updateCanvasSize"], [53, 28, 46, 26], [53, 31, 46, 29, "updateCanvasSize"], [53, 32, 46, 29], [53, 37, 46, 35], [54, 8, 47, 6], [54, 12, 47, 10, "video"], [54, 17, 47, 15], [54, 18, 47, 16, "videoWidth"], [54, 28, 47, 26], [54, 32, 47, 30, "video"], [54, 37, 47, 35], [54, 38, 47, 36, "videoHeight"], [54, 49, 47, 47], [54, 51, 47, 49], [55, 10, 48, 8, "canvas"], [55, 16, 48, 14], [55, 17, 48, 15, "width"], [55, 22, 48, 20], [55, 25, 48, 23, "video"], [55, 30, 48, 28], [55, 31, 48, 29, "videoWidth"], [55, 41, 48, 39], [56, 10, 49, 8, "canvas"], [56, 16, 49, 14], [56, 17, 49, 15, "height"], [56, 23, 49, 21], [56, 26, 49, 24, "video"], [56, 31, 49, 29], [56, 32, 49, 30, "videoHeight"], [56, 43, 49, 41], [57, 10, 50, 8, "canvas"], [57, 16, 50, 14], [57, 17, 50, 15, "style"], [57, 22, 50, 20], [57, 23, 50, 21, "width"], [57, 28, 50, 26], [57, 31, 50, 29], [57, 37, 50, 35], [58, 10, 51, 8, "canvas"], [58, 16, 51, 14], [58, 17, 51, 15, "style"], [58, 22, 51, 20], [58, 23, 51, 21, "height"], [58, 29, 51, 27], [58, 32, 51, 30], [58, 38, 51, 36], [59, 10, 52, 8, "console"], [59, 17, 52, 15], [59, 18, 52, 16, "log"], [59, 21, 52, 19], [59, 22, 52, 20], [59, 72, 52, 70], [59, 74, 52, 72, "video"], [59, 79, 52, 77], [59, 80, 52, 78, "videoWidth"], [59, 90, 52, 88], [59, 92, 52, 90], [59, 95, 52, 93], [59, 97, 52, 95, "video"], [59, 102, 52, 100], [59, 103, 52, 101, "videoHeight"], [59, 114, 52, 112], [59, 115, 52, 113], [60, 8, 53, 6], [60, 9, 53, 7], [60, 15, 53, 13], [61, 10, 54, 8], [62, 10, 55, 8, "canvas"], [62, 16, 55, 14], [62, 17, 55, 15, "width"], [62, 22, 55, 20], [62, 25, 55, 23, "width"], [62, 30, 55, 28], [63, 10, 56, 8, "canvas"], [63, 16, 56, 14], [63, 17, 56, 15, "height"], [63, 23, 56, 21], [63, 26, 56, 24, "height"], [63, 32, 56, 30], [64, 10, 57, 8, "console"], [64, 17, 57, 15], [64, 18, 57, 16, "log"], [64, 21, 57, 19], [64, 22, 57, 20], [64, 80, 57, 78], [64, 82, 57, 80, "width"], [64, 87, 57, 85], [64, 89, 57, 87], [64, 92, 57, 90], [64, 94, 57, 92, "height"], [64, 100, 57, 98], [64, 101, 57, 99], [65, 8, 58, 6], [66, 6, 59, 4], [66, 7, 59, 5], [67, 6, 61, 4], [67, 12, 61, 10, "ctx"], [67, 15, 61, 13], [67, 18, 61, 16, "canvas"], [67, 24, 61, 22], [67, 25, 61, 23, "getContext"], [67, 35, 61, 33], [67, 36, 61, 34], [67, 40, 61, 38], [67, 41, 61, 39], [68, 6, 62, 4], [68, 10, 62, 8], [68, 11, 62, 9, "ctx"], [68, 14, 62, 12], [68, 16, 62, 14], [69, 8, 63, 6, "console"], [69, 15, 63, 13], [69, 16, 63, 14, "error"], [69, 21, 63, 19], [69, 22, 63, 20], [69, 70, 63, 68], [69, 71, 63, 69], [70, 8, 64, 6], [71, 6, 65, 4], [72, 6, 67, 4], [72, 10, 67, 8, "isDetecting"], [72, 21, 67, 19], [72, 24, 67, 22], [72, 28, 67, 26], [74, 6, 69, 4], [75, 6, 70, 4], [75, 12, 70, 10, "loadScript"], [75, 22, 70, 20], [75, 25, 70, 24, "src"], [75, 28, 70, 35], [75, 32, 70, 55], [76, 8, 71, 6], [76, 15, 71, 13], [76, 19, 71, 17, "Promise"], [76, 26, 71, 24], [76, 27, 71, 25], [76, 28, 71, 26, "resolve"], [76, 35, 71, 33], [76, 37, 71, 35, "reject"], [76, 43, 71, 41], [76, 48, 71, 46], [77, 10, 72, 8], [77, 16, 72, 14, "script"], [77, 22, 72, 20], [77, 25, 72, 23, "document"], [77, 33, 72, 31], [77, 34, 72, 32, "createElement"], [77, 47, 72, 45], [77, 48, 72, 46], [77, 56, 72, 54], [77, 57, 72, 55], [78, 10, 73, 8, "script"], [78, 16, 73, 14], [78, 17, 73, 15, "src"], [78, 20, 73, 18], [78, 23, 73, 21, "src"], [78, 26, 73, 24], [79, 10, 74, 8, "script"], [79, 16, 74, 14], [79, 17, 74, 15, "onload"], [79, 23, 74, 21], [79, 26, 74, 24], [79, 32, 74, 30, "resolve"], [79, 39, 74, 37], [79, 40, 74, 38], [79, 41, 74, 39], [80, 10, 75, 8, "script"], [80, 16, 75, 14], [80, 17, 75, 15, "onerror"], [80, 24, 75, 22], [80, 27, 75, 25], [80, 33, 75, 31, "reject"], [80, 39, 75, 37], [80, 40, 75, 38], [80, 44, 75, 42, "Error"], [80, 49, 75, 47], [80, 50, 75, 48], [80, 68, 75, 66, "src"], [80, 71, 75, 69], [80, 73, 75, 71], [80, 74, 75, 72], [80, 75, 75, 73], [81, 10, 76, 8, "document"], [81, 18, 76, 16], [81, 19, 76, 17, "head"], [81, 23, 76, 21], [81, 24, 76, 22, "append<PERSON><PERSON><PERSON>"], [81, 35, 76, 33], [81, 36, 76, 34, "script"], [81, 42, 76, 40], [81, 43, 76, 41], [82, 8, 77, 6], [82, 9, 77, 7], [82, 10, 77, 8], [83, 6, 78, 4], [83, 7, 78, 5], [85, 6, 80, 4], [86, 6, 81, 4], [86, 12, 81, 10, "loadModel"], [86, 21, 81, 19], [86, 24, 81, 22], [86, 30, 81, 22, "loadModel"], [86, 31, 81, 22], [86, 36, 81, 34], [87, 8, 82, 6], [87, 12, 82, 10], [88, 10, 83, 8, "console"], [88, 17, 83, 15], [88, 18, 83, 16, "log"], [88, 21, 83, 19], [88, 22, 83, 20], [88, 66, 83, 64], [88, 67, 83, 65], [90, 10, 85, 8], [91, 10, 86, 8], [91, 14, 86, 12], [91, 15, 86, 14, "window"], [91, 21, 86, 20], [91, 22, 86, 29, "tf"], [91, 24, 86, 31], [91, 26, 86, 33], [92, 12, 87, 10], [92, 18, 87, 16, "loadScript"], [92, 28, 87, 26], [92, 29, 87, 27], [92, 98, 87, 96], [92, 99, 87, 97], [93, 10, 88, 8], [94, 10, 90, 8, "console"], [94, 17, 90, 15], [94, 18, 90, 16, "log"], [94, 21, 90, 19], [94, 22, 90, 20], [94, 68, 90, 66], [94, 69, 90, 67], [96, 10, 92, 8], [97, 10, 93, 8], [97, 14, 93, 12], [97, 15, 93, 14, "window"], [97, 21, 93, 20], [97, 22, 93, 29, "blazeface"], [97, 31, 93, 38], [97, 33, 93, 40], [98, 12, 94, 10], [98, 18, 94, 16, "loadScript"], [98, 28, 94, 26], [98, 29, 94, 27], [98, 112, 94, 110], [98, 113, 94, 111], [99, 10, 95, 8], [101, 10, 97, 8], [102, 10, 98, 8, "console"], [102, 17, 98, 15], [102, 18, 98, 16, "log"], [102, 21, 98, 19], [102, 22, 98, 20], [102, 73, 98, 71], [102, 74, 98, 72], [103, 10, 99, 8, "modelRef"], [103, 18, 99, 16], [103, 19, 99, 17, "current"], [103, 26, 99, 24], [103, 29, 99, 27], [103, 35, 99, 34, "window"], [103, 41, 99, 40], [103, 42, 99, 49, "blazeface"], [103, 51, 99, 58], [103, 52, 99, 59, "load"], [103, 56, 99, 63], [103, 57, 99, 64], [103, 58, 99, 65], [104, 10, 100, 8, "console"], [104, 17, 100, 15], [104, 18, 100, 16, "log"], [104, 21, 100, 19], [104, 22, 100, 20], [104, 79, 100, 77], [104, 80, 100, 78], [105, 10, 102, 8, "setIsLoading"], [105, 22, 102, 20], [105, 23, 102, 21], [105, 28, 102, 26], [105, 29, 102, 27], [107, 10, 104, 8], [108, 10, 105, 8, "updateCanvasSize"], [108, 26, 105, 24], [108, 27, 105, 25], [108, 28, 105, 26], [110, 10, 107, 8], [111, 10, 108, 8, "detectLoop"], [111, 20, 108, 18], [111, 21, 108, 19], [111, 22, 108, 20], [112, 8, 110, 6], [112, 9, 110, 7], [112, 10, 110, 8], [112, 17, 110, 15, "error"], [112, 22, 110, 20], [112, 24, 110, 22], [113, 10, 111, 8, "console"], [113, 17, 111, 15], [113, 18, 111, 16, "error"], [113, 23, 111, 21], [113, 24, 111, 22], [113, 67, 111, 65], [113, 69, 111, 67, "error"], [113, 74, 111, 72], [113, 75, 111, 73], [114, 10, 112, 8, "setIsLoading"], [114, 22, 112, 20], [114, 23, 112, 21], [114, 28, 112, 26], [114, 29, 112, 27], [115, 8, 113, 6], [116, 6, 114, 4], [116, 7, 114, 5], [117, 6, 116, 4], [117, 12, 116, 10, "detectLoop"], [117, 22, 116, 20], [117, 25, 116, 23], [117, 31, 116, 23, "detectLoop"], [117, 32, 116, 23], [117, 37, 116, 35], [118, 8, 117, 6], [118, 12, 117, 10], [118, 13, 117, 11, "isDetecting"], [118, 24, 117, 22], [118, 28, 117, 26], [118, 29, 117, 27, "modelRef"], [118, 37, 117, 35], [118, 38, 117, 36, "current"], [118, 45, 117, 43], [118, 47, 117, 45], [119, 8, 119, 6], [119, 12, 119, 10], [120, 10, 120, 8], [121, 10, 121, 8], [121, 14, 121, 12], [121, 15, 121, 13, "video"], [121, 20, 121, 18], [121, 21, 121, 19, "videoWidth"], [121, 31, 121, 29], [121, 35, 121, 33], [121, 36, 121, 34, "video"], [121, 41, 121, 39], [121, 42, 121, 40, "videoHeight"], [121, 53, 121, 51], [121, 55, 121, 53], [122, 12, 122, 10, "rafRef"], [122, 18, 122, 16], [122, 19, 122, 17, "current"], [122, 26, 122, 24], [122, 29, 122, 27, "requestAnimationFrame"], [122, 50, 122, 48], [122, 51, 122, 49, "detectLoop"], [122, 61, 122, 59], [122, 62, 122, 60], [123, 12, 123, 10], [124, 10, 124, 8], [126, 10, 126, 8], [127, 10, 127, 8], [127, 16, 127, 14, "tf"], [127, 18, 127, 16], [127, 21, 127, 20, "window"], [127, 27, 127, 26], [127, 28, 127, 35, "tf"], [127, 30, 127, 37], [128, 10, 128, 8], [128, 16, 128, 14, "tensor"], [128, 22, 128, 20], [128, 25, 128, 23, "tf"], [128, 27, 128, 25], [128, 28, 128, 26, "browser"], [128, 35, 128, 33], [128, 36, 128, 34, "fromPixels"], [128, 46, 128, 44], [128, 47, 128, 45, "video"], [128, 52, 128, 50], [128, 53, 128, 51], [130, 10, 130, 8], [131, 10, 131, 8], [131, 16, 131, 14, "predictions"], [131, 27, 131, 25], [131, 30, 131, 28], [131, 36, 131, 34, "modelRef"], [131, 44, 131, 42], [131, 45, 131, 43, "current"], [131, 52, 131, 50], [131, 53, 131, 51, "estimateFaces"], [131, 66, 131, 64], [131, 67, 131, 65, "tensor"], [131, 73, 131, 71], [131, 75, 131, 73], [131, 80, 131, 78], [131, 82, 131, 80], [131, 85, 131, 83], [131, 86, 131, 84], [132, 10, 132, 8, "tensor"], [132, 16, 132, 14], [132, 17, 132, 15, "dispose"], [132, 24, 132, 22], [132, 25, 132, 23], [132, 26, 132, 24], [134, 10, 134, 8], [135, 10, 135, 8, "ctx"], [135, 13, 135, 11], [135, 14, 135, 12, "clearRect"], [135, 23, 135, 21], [135, 24, 135, 22], [135, 25, 135, 23], [135, 27, 135, 25], [135, 28, 135, 26], [135, 30, 135, 28, "canvas"], [135, 36, 135, 34], [135, 37, 135, 35, "width"], [135, 42, 135, 40], [135, 44, 135, 42, "canvas"], [135, 50, 135, 48], [135, 51, 135, 49, "height"], [135, 57, 135, 55], [135, 58, 135, 56], [136, 10, 136, 8, "ctx"], [136, 13, 136, 11], [136, 14, 136, 12, "drawImage"], [136, 23, 136, 21], [136, 24, 136, 22, "video"], [136, 29, 136, 27], [136, 31, 136, 29], [136, 32, 136, 30], [136, 34, 136, 32], [136, 35, 136, 33], [136, 37, 136, 35, "canvas"], [136, 43, 136, 41], [136, 44, 136, 42, "width"], [136, 49, 136, 47], [136, 51, 136, 49, "canvas"], [136, 57, 136, 55], [136, 58, 136, 56, "height"], [136, 64, 136, 62], [136, 65, 136, 63], [137, 10, 138, 8], [137, 14, 138, 12, "predictions"], [137, 25, 138, 23], [137, 26, 138, 24, "length"], [137, 32, 138, 30], [137, 35, 138, 33], [137, 36, 138, 34], [137, 38, 138, 36], [138, 12, 139, 10, "setFaceCount"], [138, 24, 139, 22], [138, 25, 139, 23, "predictions"], [138, 36, 139, 34], [138, 37, 139, 35, "length"], [138, 43, 139, 41], [138, 44, 139, 42], [140, 12, 141, 10], [141, 12, 142, 10, "predictions"], [141, 23, 142, 21], [141, 24, 142, 22, "for<PERSON>ach"], [141, 31, 142, 29], [141, 32, 142, 30], [141, 33, 142, 31, "prediction"], [141, 43, 142, 46], [141, 45, 142, 48, "index"], [141, 50, 142, 61], [141, 55, 142, 66], [142, 14, 143, 12], [142, 20, 143, 18], [142, 21, 143, 19, "x1"], [142, 23, 143, 21], [142, 25, 143, 23, "y1"], [142, 27, 143, 25], [142, 28, 143, 26], [142, 31, 143, 29, "prediction"], [142, 41, 143, 39], [142, 42, 143, 40, "topLeft"], [142, 49, 143, 47], [143, 14, 144, 12], [143, 20, 144, 18], [143, 21, 144, 19, "x2"], [143, 23, 144, 21], [143, 25, 144, 23, "y2"], [143, 27, 144, 25], [143, 28, 144, 26], [143, 31, 144, 29, "prediction"], [143, 41, 144, 39], [143, 42, 144, 40, "bottomRight"], [143, 53, 144, 51], [145, 14, 146, 12], [146, 14, 147, 12], [146, 18, 147, 16, "minX"], [146, 22, 147, 20], [146, 25, 147, 23, "Math"], [146, 29, 147, 27], [146, 30, 147, 28, "min"], [146, 33, 147, 31], [146, 34, 147, 32, "x1"], [146, 36, 147, 34], [146, 38, 147, 36, "x2"], [146, 40, 147, 38], [146, 41, 147, 39], [147, 14, 148, 12], [147, 18, 148, 16, "maxX"], [147, 22, 148, 20], [147, 25, 148, 23, "Math"], [147, 29, 148, 27], [147, 30, 148, 28, "max"], [147, 33, 148, 31], [147, 34, 148, 32, "x1"], [147, 36, 148, 34], [147, 38, 148, 36, "x2"], [147, 40, 148, 38], [147, 41, 148, 39], [148, 14, 149, 12], [148, 20, 149, 18, "minY"], [148, 24, 149, 22], [148, 27, 149, 25, "Math"], [148, 31, 149, 29], [148, 32, 149, 30, "min"], [148, 35, 149, 33], [148, 36, 149, 34, "y1"], [148, 38, 149, 36], [148, 40, 149, 38, "y2"], [148, 42, 149, 40], [148, 43, 149, 41], [149, 14, 150, 12], [149, 20, 150, 18, "maxY"], [149, 24, 150, 22], [149, 27, 150, 25, "Math"], [149, 31, 150, 29], [149, 32, 150, 30, "max"], [149, 35, 150, 33], [149, 36, 150, 34, "y1"], [149, 38, 150, 36], [149, 40, 150, 38, "y2"], [149, 42, 150, 40], [149, 43, 150, 41], [151, 14, 152, 12], [152, 14, 153, 12], [152, 20, 153, 18, "canvasWidth"], [152, 31, 153, 29], [152, 34, 153, 32, "canvas"], [152, 40, 153, 38], [152, 41, 153, 39, "width"], [152, 46, 153, 44], [153, 14, 154, 12], [153, 20, 154, 18, "flippedMinX"], [153, 31, 154, 29], [153, 34, 154, 32, "canvasWidth"], [153, 45, 154, 43], [153, 48, 154, 46, "maxX"], [153, 52, 154, 50], [154, 14, 155, 12], [154, 20, 155, 18, "flippedMaxX"], [154, 31, 155, 29], [154, 34, 155, 32, "canvasWidth"], [154, 45, 155, 43], [154, 48, 155, 46, "minX"], [154, 52, 155, 50], [155, 14, 156, 12, "minX"], [155, 18, 156, 16], [155, 21, 156, 19, "flippedMinX"], [155, 32, 156, 30], [156, 14, 157, 12, "maxX"], [156, 18, 157, 16], [156, 21, 157, 19, "flippedMaxX"], [156, 32, 157, 30], [158, 14, 159, 12], [159, 14, 160, 12], [159, 20, 160, 18, "faceWidth"], [159, 29, 160, 27], [159, 32, 160, 30, "maxX"], [159, 36, 160, 34], [159, 39, 160, 37, "minX"], [159, 43, 160, 41], [160, 14, 161, 12], [160, 20, 161, 18, "faceHeight"], [160, 30, 161, 28], [160, 33, 161, 31, "maxY"], [160, 37, 161, 35], [160, 40, 161, 38, "minY"], [160, 44, 161, 42], [161, 14, 163, 12], [161, 18, 163, 16, "faceWidth"], [161, 27, 163, 25], [161, 31, 163, 29], [161, 32, 163, 30], [161, 36, 163, 34, "faceHeight"], [161, 46, 163, 44], [161, 50, 163, 48], [161, 51, 163, 49], [161, 53, 163, 51], [162, 16, 164, 14], [163, 14, 165, 12], [165, 14, 167, 12], [166, 14, 168, 12], [166, 20, 168, 18, "centerX"], [166, 27, 168, 25], [166, 30, 168, 28], [166, 31, 168, 29, "minX"], [166, 35, 168, 33], [166, 38, 168, 36, "maxX"], [166, 42, 168, 40], [166, 46, 168, 44], [166, 47, 168, 45], [167, 14, 169, 12], [167, 20, 169, 18, "centerY"], [167, 27, 169, 25], [167, 30, 169, 28], [167, 31, 169, 29, "minY"], [167, 35, 169, 33], [167, 38, 169, 36, "maxY"], [167, 42, 169, 40], [167, 46, 169, 44], [167, 47, 169, 45], [168, 14, 170, 12], [168, 20, 170, 18, "expandedWidth"], [168, 33, 170, 31], [168, 36, 170, 34, "faceWidth"], [168, 45, 170, 43], [168, 48, 170, 46], [168, 51, 170, 49], [169, 14, 171, 12], [169, 20, 171, 18, "expandedHeight"], [169, 34, 171, 32], [169, 37, 171, 35, "faceHeight"], [169, 47, 171, 45], [169, 50, 171, 48], [169, 53, 171, 51], [171, 14, 173, 12], [172, 14, 174, 12], [172, 20, 174, 18, "radiusX"], [172, 27, 174, 25], [172, 30, 174, 28, "Math"], [172, 34, 174, 32], [172, 35, 174, 33, "max"], [172, 38, 174, 36], [172, 39, 174, 37, "expandedWidth"], [172, 52, 174, 50], [172, 55, 174, 53], [172, 56, 174, 54], [172, 58, 174, 56], [172, 60, 174, 58], [172, 61, 174, 59], [173, 14, 175, 12], [173, 20, 175, 18, "radiusY"], [173, 27, 175, 25], [173, 30, 175, 28, "Math"], [173, 34, 175, 32], [173, 35, 175, 33, "max"], [173, 38, 175, 36], [173, 39, 175, 37, "expandedHeight"], [173, 53, 175, 51], [173, 56, 175, 54], [173, 57, 175, 55], [173, 59, 175, 57], [173, 61, 175, 59], [173, 62, 175, 60], [175, 14, 177, 12], [176, 14, 178, 12, "ctx"], [176, 17, 178, 15], [176, 18, 178, 16, "save"], [176, 22, 178, 20], [176, 23, 178, 21], [176, 24, 178, 22], [177, 14, 179, 12, "ctx"], [177, 17, 179, 15], [177, 18, 179, 16, "beginPath"], [177, 27, 179, 25], [177, 28, 179, 26], [177, 29, 179, 27], [178, 14, 180, 12, "ctx"], [178, 17, 180, 15], [178, 18, 180, 16, "ellipse"], [178, 25, 180, 23], [178, 26, 180, 24, "centerX"], [178, 33, 180, 31], [178, 35, 180, 33, "centerY"], [178, 42, 180, 40], [178, 44, 180, 42, "radiusX"], [178, 51, 180, 49], [178, 53, 180, 51, "radiusY"], [178, 60, 180, 58], [178, 62, 180, 60], [178, 63, 180, 61], [178, 65, 180, 63], [178, 66, 180, 64], [178, 68, 180, 66, "Math"], [178, 72, 180, 70], [178, 73, 180, 71, "PI"], [178, 75, 180, 73], [178, 78, 180, 76], [178, 79, 180, 77], [178, 80, 180, 78], [179, 14, 181, 12, "ctx"], [179, 17, 181, 15], [179, 18, 181, 16, "clip"], [179, 22, 181, 20], [179, 23, 181, 21], [179, 24, 181, 22], [180, 14, 182, 12, "ctx"], [180, 17, 182, 15], [180, 18, 182, 16, "filter"], [180, 24, 182, 22], [180, 27, 182, 25], [180, 39, 182, 37], [180, 40, 182, 38], [180, 41, 182, 39], [181, 14, 183, 12, "ctx"], [181, 17, 183, 15], [181, 18, 183, 16, "drawImage"], [181, 27, 183, 25], [181, 28, 183, 26, "video"], [181, 33, 183, 31], [181, 35, 183, 33], [181, 36, 183, 34], [181, 38, 183, 36], [181, 39, 183, 37], [181, 41, 183, 39, "canvas"], [181, 47, 183, 45], [181, 48, 183, 46, "width"], [181, 53, 183, 51], [181, 55, 183, 53, "canvas"], [181, 61, 183, 59], [181, 62, 183, 60, "height"], [181, 68, 183, 66], [181, 69, 183, 67], [182, 14, 184, 12, "ctx"], [182, 17, 184, 15], [182, 18, 184, 16, "restore"], [182, 25, 184, 23], [182, 26, 184, 24], [182, 27, 184, 25], [184, 14, 186, 12], [185, 14, 187, 12], [185, 18, 187, 16, "__DEV__"], [185, 25, 187, 23], [185, 27, 187, 25], [186, 16, 188, 14, "ctx"], [186, 19, 188, 17], [186, 20, 188, 18, "strokeStyle"], [186, 31, 188, 29], [186, 34, 188, 32], [186, 56, 188, 54], [187, 16, 189, 14, "ctx"], [187, 19, 189, 17], [187, 20, 189, 18, "lineWidth"], [187, 29, 189, 27], [187, 32, 189, 30], [187, 33, 189, 31], [188, 16, 190, 14, "ctx"], [188, 19, 190, 17], [188, 20, 190, 18, "strokeRect"], [188, 30, 190, 28], [188, 31, 190, 29, "minX"], [188, 35, 190, 33], [188, 37, 190, 35, "minY"], [188, 41, 190, 39], [188, 43, 190, 41, "faceWidth"], [188, 52, 190, 50], [188, 54, 190, 52, "faceHeight"], [188, 64, 190, 62], [188, 65, 190, 63], [189, 14, 191, 12], [190, 12, 192, 10], [190, 13, 192, 11], [190, 14, 192, 12], [191, 10, 193, 8], [191, 11, 193, 9], [191, 17, 193, 15], [192, 12, 194, 10, "setFaceCount"], [192, 24, 194, 22], [192, 25, 194, 23], [192, 26, 194, 24], [192, 27, 194, 25], [193, 10, 195, 8], [194, 8, 197, 6], [194, 9, 197, 7], [194, 10, 197, 8], [194, 17, 197, 15, "error"], [194, 22, 197, 20], [194, 24, 197, 22], [195, 10, 198, 8, "console"], [195, 17, 198, 15], [195, 18, 198, 16, "error"], [195, 23, 198, 21], [195, 24, 198, 22], [195, 60, 198, 58], [195, 62, 198, 60, "error"], [195, 67, 198, 65], [195, 68, 198, 66], [196, 8, 199, 6], [198, 8, 201, 6], [199, 8, 202, 6], [199, 12, 202, 10, "isDetecting"], [199, 23, 202, 21], [199, 25, 202, 23], [200, 10, 203, 8, "rafRef"], [200, 16, 203, 14], [200, 17, 203, 15, "current"], [200, 24, 203, 22], [200, 27, 203, 25, "requestAnimationFrame"], [200, 48, 203, 46], [200, 49, 203, 47, "detectLoop"], [200, 59, 203, 57], [200, 60, 203, 58], [201, 8, 204, 6], [202, 6, 205, 4], [202, 7, 205, 5], [204, 6, 207, 4], [205, 6, 208, 4], [205, 12, 208, 10, "waitForVideoAndStart"], [205, 32, 208, 30], [205, 35, 208, 33, "waitForVideoAndStart"], [205, 36, 208, 33], [205, 41, 208, 39], [206, 8, 209, 6], [206, 12, 209, 10, "video"], [206, 17, 209, 15], [206, 18, 209, 16, "readyState"], [206, 28, 209, 26], [206, 32, 209, 30], [206, 33, 209, 31], [206, 35, 209, 33], [207, 10, 209, 35], [208, 10, 210, 8, "loadModel"], [208, 19, 210, 17], [208, 20, 210, 18], [208, 21, 210, 19], [209, 8, 211, 6], [209, 9, 211, 7], [209, 15, 211, 13], [210, 10, 212, 8, "video"], [210, 15, 212, 13], [210, 16, 212, 14, "addEventListener"], [210, 32, 212, 30], [210, 33, 212, 31], [210, 45, 212, 43], [210, 47, 212, 45, "loadModel"], [210, 56, 212, 54], [210, 58, 212, 56], [211, 12, 212, 58, "once"], [211, 16, 212, 62], [211, 18, 212, 64], [212, 10, 212, 69], [212, 11, 212, 70], [212, 12, 212, 71], [213, 8, 213, 6], [214, 6, 214, 4], [214, 7, 214, 5], [216, 6, 216, 4], [217, 6, 217, 4, "waitForVideoAndStart"], [217, 26, 217, 24], [217, 27, 217, 25], [217, 28, 217, 26], [219, 6, 219, 4], [220, 6, 220, 4], [220, 13, 220, 11], [220, 19, 220, 17], [221, 8, 221, 6, "isDetecting"], [221, 19, 221, 17], [221, 22, 221, 20], [221, 27, 221, 25], [222, 8, 222, 6], [222, 12, 222, 10, "rafRef"], [222, 18, 222, 16], [222, 19, 222, 17, "current"], [222, 26, 222, 24], [222, 28, 222, 26], [223, 10, 223, 8, "cancelAnimationFrame"], [223, 30, 223, 28], [223, 31, 223, 29, "rafRef"], [223, 37, 223, 35], [223, 38, 223, 36, "current"], [223, 45, 223, 43], [223, 46, 223, 44], [224, 8, 224, 6], [225, 6, 225, 4], [225, 7, 225, 5], [226, 4, 226, 2], [226, 5, 226, 3], [226, 7, 226, 5], [226, 8, 226, 6, "containerId"], [226, 19, 226, 17], [226, 21, 226, 19, "width"], [226, 26, 226, 24], [226, 28, 226, 26, "height"], [226, 34, 226, 32], [226, 35, 226, 33], [226, 36, 226, 34], [227, 4, 228, 2], [227, 24, 229, 4], [227, 28, 229, 4, "_jsxDevRuntime"], [227, 42, 229, 4], [227, 43, 229, 4, "jsxDEV"], [227, 49, 229, 4], [227, 51, 229, 4, "_jsxDevRuntime"], [227, 65, 229, 4], [227, 66, 229, 4, "Fragment"], [227, 74, 229, 4], [228, 6, 229, 4, "children"], [228, 14, 229, 4], [228, 30, 230, 6], [228, 34, 230, 6, "_jsxDevRuntime"], [228, 48, 230, 6], [228, 49, 230, 6, "jsxDEV"], [228, 55, 230, 6], [229, 8, 231, 8, "ref"], [229, 11, 231, 11], [229, 13, 231, 13, "canvasRef"], [229, 22, 231, 23], [230, 8, 232, 8, "style"], [230, 13, 232, 13], [230, 15, 232, 15], [231, 10, 233, 10, "position"], [231, 18, 233, 18], [231, 20, 233, 20], [231, 30, 233, 30], [232, 10, 234, 10, "left"], [232, 14, 234, 14], [232, 16, 234, 16], [232, 17, 234, 17], [233, 10, 235, 10, "top"], [233, 13, 235, 13], [233, 15, 235, 15], [233, 16, 235, 16], [234, 10, 236, 10, "width"], [234, 15, 236, 15], [234, 17, 236, 17], [234, 23, 236, 23], [235, 10, 237, 10, "height"], [235, 16, 237, 16], [235, 18, 237, 18], [235, 24, 237, 24], [236, 10, 238, 10, "pointerEvents"], [236, 23, 238, 23], [236, 25, 238, 25], [236, 31, 238, 31], [237, 10, 239, 10, "zIndex"], [237, 16, 239, 16], [237, 18, 239, 18], [237, 20, 239, 20], [238, 10, 239, 22], [239, 10, 240, 10, "objectFit"], [239, 19, 240, 19], [239, 21, 240, 21], [239, 28, 240, 28], [240, 10, 241, 10, "backgroundColor"], [240, 25, 241, 25], [240, 27, 241, 27], [241, 8, 242, 8], [242, 6, 242, 10], [243, 8, 242, 10, "fileName"], [243, 16, 242, 10], [243, 18, 242, 10, "_jsxFileName"], [243, 30, 242, 10], [244, 8, 242, 10, "lineNumber"], [244, 18, 242, 10], [245, 8, 242, 10, "columnNumber"], [245, 20, 242, 10], [246, 6, 242, 10], [246, 13, 243, 7], [246, 14, 243, 8], [246, 16, 245, 7, "isLoading"], [246, 25, 245, 16], [246, 42, 246, 8], [246, 46, 246, 8, "_jsxDevRuntime"], [246, 60, 246, 8], [246, 61, 246, 8, "jsxDEV"], [246, 67, 246, 8], [247, 8, 246, 13, "style"], [247, 13, 246, 18], [247, 15, 246, 20], [248, 10, 247, 10, "position"], [248, 18, 247, 18], [248, 20, 247, 20], [248, 30, 247, 30], [249, 10, 248, 10, "top"], [249, 13, 248, 13], [249, 15, 248, 15], [249, 17, 248, 17], [250, 10, 249, 10, "left"], [250, 14, 249, 14], [250, 16, 249, 16], [250, 18, 249, 18], [251, 10, 250, 10, "background"], [251, 20, 250, 20], [251, 22, 250, 22], [251, 47, 250, 47], [252, 10, 251, 10, "color"], [252, 15, 251, 15], [252, 17, 251, 17], [252, 24, 251, 24], [253, 10, 252, 10, "padding"], [253, 17, 252, 17], [253, 19, 252, 19], [253, 29, 252, 29], [254, 10, 253, 10, "borderRadius"], [254, 22, 253, 22], [254, 24, 253, 24], [254, 29, 253, 29], [255, 10, 254, 10, "fontSize"], [255, 18, 254, 18], [255, 20, 254, 20], [255, 26, 254, 26], [256, 10, 255, 10, "fontWeight"], [256, 20, 255, 20], [256, 22, 255, 22], [256, 27, 255, 27], [257, 10, 256, 10, "zIndex"], [257, 16, 256, 16], [257, 18, 256, 18], [257, 20, 256, 20], [258, 10, 257, 10, "border"], [258, 16, 257, 16], [258, 18, 257, 18], [259, 8, 258, 8], [259, 9, 258, 10], [260, 8, 258, 10, "children"], [260, 16, 258, 10], [260, 18, 258, 11], [261, 6, 260, 8], [262, 8, 260, 8, "fileName"], [262, 16, 260, 8], [262, 18, 260, 8, "_jsxFileName"], [262, 30, 260, 8], [263, 8, 260, 8, "lineNumber"], [263, 18, 260, 8], [264, 8, 260, 8, "columnNumber"], [264, 20, 260, 8], [265, 6, 260, 8], [265, 13, 260, 13], [265, 14, 261, 7], [265, 16, 262, 7], [265, 17, 262, 8, "isLoading"], [265, 26, 262, 17], [265, 30, 262, 21, "faceCount"], [265, 39, 262, 30], [265, 42, 262, 33], [265, 43, 262, 34], [265, 60, 263, 8], [265, 64, 263, 8, "_jsxDevRuntime"], [265, 78, 263, 8], [265, 79, 263, 8, "jsxDEV"], [265, 85, 263, 8], [266, 8, 263, 13, "style"], [266, 13, 263, 18], [266, 15, 263, 20], [267, 10, 264, 10, "position"], [267, 18, 264, 18], [267, 20, 264, 20], [267, 30, 264, 30], [268, 10, 265, 10, "top"], [268, 13, 265, 13], [268, 15, 265, 15], [268, 17, 265, 17], [269, 10, 266, 10, "left"], [269, 14, 266, 14], [269, 16, 266, 16], [269, 18, 266, 18], [270, 10, 267, 10, "background"], [270, 20, 267, 20], [270, 22, 267, 22], [270, 47, 267, 47], [271, 10, 268, 10, "color"], [271, 15, 268, 15], [271, 17, 268, 17], [271, 24, 268, 24], [272, 10, 269, 10, "padding"], [272, 17, 269, 17], [272, 19, 269, 19], [272, 29, 269, 29], [273, 10, 270, 10, "borderRadius"], [273, 22, 270, 22], [273, 24, 270, 24], [273, 29, 270, 29], [274, 10, 271, 10, "fontSize"], [274, 18, 271, 18], [274, 20, 271, 20], [274, 26, 271, 26], [275, 10, 272, 10, "fontWeight"], [275, 20, 272, 20], [275, 22, 272, 22], [275, 27, 272, 27], [276, 10, 273, 10, "zIndex"], [276, 16, 273, 16], [276, 18, 273, 18], [276, 20, 273, 20], [277, 10, 274, 10, "border"], [277, 16, 274, 16], [277, 18, 274, 18], [278, 8, 275, 8], [278, 9, 275, 10], [279, 8, 275, 10, "children"], [279, 16, 275, 10], [279, 19, 275, 11], [279, 51, 276, 25], [279, 53, 276, 26, "faceCount"], [279, 62, 276, 35], [279, 64, 276, 36], [279, 71, 276, 41], [279, 73, 276, 42, "faceCount"], [279, 82, 276, 51], [279, 85, 276, 54], [279, 86, 276, 55], [279, 89, 276, 58], [279, 92, 276, 61], [279, 95, 276, 64], [279, 97, 276, 66], [280, 6, 276, 66], [281, 8, 276, 66, "fileName"], [281, 16, 276, 66], [281, 18, 276, 66, "_jsxFileName"], [281, 30, 276, 66], [282, 8, 276, 66, "lineNumber"], [282, 18, 276, 66], [283, 8, 276, 66, "columnNumber"], [283, 20, 276, 66], [284, 6, 276, 66], [284, 13, 277, 13], [284, 14, 278, 7], [284, 16, 279, 7], [284, 17, 279, 8, "isLoading"], [284, 26, 279, 17], [284, 30, 279, 21, "faceCount"], [284, 39, 279, 30], [284, 44, 279, 35], [284, 45, 279, 36], [284, 62, 280, 8], [284, 66, 280, 8, "_jsxDevRuntime"], [284, 80, 280, 8], [284, 81, 280, 8, "jsxDEV"], [284, 87, 280, 8], [285, 8, 280, 13, "style"], [285, 13, 280, 18], [285, 15, 280, 20], [286, 10, 281, 10, "position"], [286, 18, 281, 18], [286, 20, 281, 20], [286, 30, 281, 30], [287, 10, 282, 10, "top"], [287, 13, 282, 13], [287, 15, 282, 15], [287, 17, 282, 17], [288, 10, 283, 10, "left"], [288, 14, 283, 14], [288, 16, 283, 16], [288, 18, 283, 18], [289, 10, 284, 10, "background"], [289, 20, 284, 20], [289, 22, 284, 22], [289, 48, 284, 48], [290, 10, 285, 10, "color"], [290, 15, 285, 15], [290, 17, 285, 17], [290, 24, 285, 24], [291, 10, 286, 10, "padding"], [291, 17, 286, 17], [291, 19, 286, 19], [291, 29, 286, 29], [292, 10, 287, 10, "borderRadius"], [292, 22, 287, 22], [292, 24, 287, 24], [292, 29, 287, 29], [293, 10, 288, 10, "fontSize"], [293, 18, 288, 18], [293, 20, 288, 20], [293, 26, 288, 26], [294, 10, 289, 10, "fontWeight"], [294, 20, 289, 20], [294, 22, 289, 22], [294, 27, 289, 27], [295, 10, 290, 10, "zIndex"], [295, 16, 290, 16], [295, 18, 290, 18], [295, 20, 290, 20], [296, 10, 291, 10, "border"], [296, 16, 291, 16], [296, 18, 291, 18], [297, 8, 292, 8], [297, 9, 292, 10], [298, 8, 292, 10, "children"], [298, 16, 292, 10], [298, 18, 292, 11], [299, 6, 294, 8], [300, 8, 294, 8, "fileName"], [300, 16, 294, 8], [300, 18, 294, 8, "_jsxFileName"], [300, 30, 294, 8], [301, 8, 294, 8, "lineNumber"], [301, 18, 294, 8], [302, 8, 294, 8, "columnNumber"], [302, 20, 294, 8], [303, 6, 294, 8], [303, 13, 294, 13], [303, 14, 295, 7], [304, 4, 295, 7], [304, 19, 296, 6], [304, 20, 296, 7], [305, 2, 298, 0], [306, 2, 298, 1, "_s"], [306, 4, 298, 1], [306, 5, 17, 24, "BlazeFaceCanvas"], [306, 20, 17, 39], [307, 2, 17, 39, "_c"], [307, 4, 17, 39], [307, 7, 17, 24, "BlazeFaceCanvas"], [307, 22, 17, 39], [308, 2, 17, 39], [308, 6, 17, 39, "_c"], [308, 8, 17, 39], [309, 2, 17, 39, "$RefreshReg$"], [309, 14, 17, 39], [309, 15, 17, 39, "_c"], [309, 17, 17, 39], [310, 0, 17, 39], [310, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "updateCanvasSize", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "loadModel", "detectLoop", "predictions.forEach$argument_0", "waitForVideoAndStart", "<anonymous>"], "mappings": "AAA;eCgB;YCO;6BCsB;KDa;uBEW;yBCC;wBCG,eD;yBEC,gDF;ODE;KFC;sBMG;KNiC;uBOE;8BC0B;WDkD;KPa;iCSG;KTM;WUM;KVK;GDC;CDwE"}}, "type": "js/module"}]}