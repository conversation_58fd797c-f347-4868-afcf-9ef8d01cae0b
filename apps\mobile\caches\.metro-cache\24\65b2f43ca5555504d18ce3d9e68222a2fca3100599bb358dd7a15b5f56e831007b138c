{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = TensorFlowFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\TensorFlowFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  // TensorFlow.js BlazeFace implementation for reliable cross-browser face detection\n  function TensorFlowFaceCanvas({\n    containerId,\n    width,\n    height\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const debugCounterRef = (0, _react.useRef)(0);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [error, setError] = (0, _react.useState)(null);\n    (0, _react.useEffect)(() => {\n      console.log('[TensorFlowFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[TensorFlowFaceCanvas] Container not found:', containerId);\n        setError('Container not found');\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[TensorFlowFaceCanvas] Video element not found in container');\n        setError('Video element not found');\n        return;\n      }\n\n      // Resize canvas\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[TensorFlowFaceCanvas] Canvas ref not available');\n        setError('Canvas not available');\n        return;\n      }\n      canvas.width = width;\n      canvas.height = height;\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[TensorFlowFaceCanvas] Canvas context not available');\n        setError('Canvas context not available');\n        return;\n      }\n      let running = true;\n      debugCounterRef.current = 0;\n      const loadTensorFlowAndBlazeFace = async () => {\n        try {\n          console.log('[TensorFlowFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js');\n            console.log('[TensorFlowFaceCanvas] TensorFlow.js loaded');\n          }\n\n          // Load BlazeFace\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n            console.log('[TensorFlowFaceCanvas] BlazeFace loaded');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[TensorFlowFaceCanvas] Initializing BlazeFace model...');\n          const blazeface = window.blazeface;\n          modelRef.current = await blazeface.load();\n          console.log('[TensorFlowFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n          setError(null);\n        } catch (error) {\n          console.error('[TensorFlowFaceCanvas] ❌ Failed to load TensorFlow/BlazeFace:', error);\n          setError(`Failed to load face detection: ${error.message}`);\n          setIsLoading(false);\n        }\n      };\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.async = true;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load script: ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n      const applyFallbackBlur = (ctx, videoW, videoH, canvasW, canvasH) => {\n        // Apply blur to common face areas when no faces are detected\n        const scale = Math.max(canvasW / videoW, canvasH / videoH);\n        const scaledW = videoW * scale;\n        const scaledH = videoH * scale;\n        const offsetX = (canvasW - scaledW) / 2;\n        const offsetY = (canvasH - scaledH) / 2;\n\n        // Common face areas (center-upper region for selfies)\n        const faceAreas = [{\n          x: 0.25,\n          y: 0.15,\n          w: 0.5,\n          h: 0.5\n        },\n        // Center face\n        {\n          x: 0.1,\n          y: 0.2,\n          w: 0.35,\n          h: 0.4\n        },\n        // Left side\n        {\n          x: 0.55,\n          y: 0.2,\n          w: 0.35,\n          h: 0.4\n        } // Right side\n        ];\n        faceAreas.forEach(area => {\n          const x = area.x * scaledW + offsetX;\n          const y = area.y * scaledH + offsetY;\n          const w = area.w * scaledW;\n          const h = area.h * scaledH;\n          ctx.save();\n          ctx.beginPath();\n          ctx.ellipse(x + w / 2, y + h / 2, w / 2, h / 2, 0, 0, Math.PI * 2);\n          ctx.clip();\n          ctx.filter = 'blur(25px)';\n          ctx.drawImage(video, 0, 0, canvasW, canvasH);\n          ctx.restore();\n        });\n      };\n      const loop = async () => {\n        if (!running) return;\n        rafRef.current = requestAnimationFrame(loop);\n        if (!modelRef.current) {\n          // Log occasionally that model is not ready\n          debugCounterRef.current++;\n          if (debugCounterRef.current % 60 === 0) {\n            // Every ~1 second at 60fps\n            console.log('[TensorFlowFaceCanvas] Waiting for BlazeFace model to load...');\n          }\n          return;\n        }\n        try {\n          const videoW = video.videoWidth || width;\n          const videoH = video.videoHeight || height;\n\n          // Clear canvas\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n          // Log video status occasionally\n          debugCounterRef.current++;\n          if (debugCounterRef.current % 300 === 0) {\n            // Every ~5 seconds\n            console.log('[TensorFlowFaceCanvas] Video status:', {\n              dimensions: `${videoW}x${videoH}`,\n              readyState: video.readyState,\n              paused: video.paused,\n              currentTime: video.currentTime\n            });\n          }\n          if (videoW === 0 || videoH === 0) {\n            if (debugCounterRef.current % 60 === 0) {\n              console.log('[TensorFlowFaceCanvas] Video not ready - dimensions:', videoW, 'x', videoH);\n            }\n            return;\n          }\n\n          // Detect faces using BlazeFace\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6); // Lower threshold for better detection\n          tensor.dispose(); // Clean up tensor\n\n          // Debug logging for face detection\n          if (predictions.length > 0) {\n            console.log(`[TensorFlowFaceCanvas] 🎯 Detected ${predictions.length} face(s) at frame ${debugCounterRef.current}`);\n          } else if (debugCounterRef.current % 120 === 0) {\n            // Every ~2 seconds\n            console.log(`[TensorFlowFaceCanvas] No faces detected (frame ${debugCounterRef.current})`);\n          }\n          if (predictions.length === 0) {\n            // Apply fallback blur to common face areas\n            if (debugCounterRef.current > 60) {\n              // Only after model has had time to initialize\n              applyFallbackBlur(ctx, videoW, videoH, width, height);\n            }\n            return;\n          }\n\n          // Compute scale from video to canvas\n          const scale = Math.max(width / videoW, height / videoH);\n          const scaledW = videoW * scale;\n          const scaledH = videoH * scale;\n          const offsetX = (width - scaledW) / 2;\n          const offsetY = (height - scaledH) / 2;\n\n          // Draw blur for each detected face\n          predictions.forEach((prediction, index) => {\n            const [x1, y1] = prediction.topLeft;\n            const [x2, y2] = prediction.bottomRight;\n\n            // Expand the bounding box for better coverage\n            const centerX = (x1 + x2) / 2;\n            const centerY = (y1 + y2) / 2;\n            const faceWidth = (x2 - x1) * 1.4; // Expand by 40%\n            const faceHeight = (y2 - y1) * 1.6; // Expand by 60%\n\n            const expandedX1 = centerX - faceWidth / 2;\n            const expandedY1 = centerY - faceHeight / 2;\n\n            // Map to canvas coordinates\n            const canvasX = expandedX1 * scale + offsetX;\n            const canvasY = expandedY1 * scale + offsetY;\n            const canvasW = faceWidth * scale;\n            const canvasH = faceHeight * scale;\n\n            // Draw blurred oval patch\n            ctx.save();\n            ctx.beginPath();\n            ctx.ellipse(canvasX + canvasW / 2, canvasY + canvasH / 2, canvasW / 2, canvasH / 2, 0, 0, Math.PI * 2);\n            ctx.clip();\n            ctx.filter = 'blur(30px)';\n            ctx.drawImage(video, 0, 0, width, height);\n            ctx.restore();\n            if (debugCounterRef.current % 60 === 0) {\n              console.log(`[TensorFlowFaceCanvas] Blurred face ${index + 1} at (${Math.round(canvasX)}, ${Math.round(canvasY)})`);\n            }\n          });\n        } catch (error) {\n          // Log errors but keep the loop running\n          if (debugCounterRef.current % 300 === 0) {\n            console.error('[TensorFlowFaceCanvas] Detection error:', error);\n          }\n        }\n      };\n\n      // Start loading and then begin the loop\n      loadTensorFlowAndBlazeFace().then(() => {\n        if (running) {\n          rafRef.current = requestAnimationFrame(loop);\n        }\n      });\n      return () => {\n        running = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width,\n          height,\n          pointerEvents: 'none',\n          zIndex: 10\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(0,0,0,0.7)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: \"Loading face detection...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(255,0,0,0.7)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: [\"Face detection error: \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(TensorFlowFaceCanvas, \"6DhjEcQmE6gVnJWZHklLharJPbw=\");\n  _c = TensorFlowFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"TensorFlowFaceCanvas\");\n});", "lineCount": 319, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 9, 0], [12, 2, 10, 15], [12, 11, 10, 24, "TensorFlowFaceCanvas"], [12, 31, 10, 44, "TensorFlowFaceCanvas"], [12, 32, 10, 45], [13, 4, 10, 47, "containerId"], [13, 15, 10, 58], [14, 4, 10, 60, "width"], [14, 9, 10, 65], [15, 4, 10, 67, "height"], [16, 2, 10, 101], [16, 3, 10, 102], [16, 5, 10, 104], [17, 4, 10, 104, "_s"], [17, 6, 10, 104], [18, 4, 11, 2], [18, 10, 11, 8, "canvasRef"], [18, 19, 11, 17], [18, 22, 11, 20], [18, 26, 11, 20, "useRef"], [18, 39, 11, 26], [18, 41, 11, 53], [18, 45, 11, 57], [18, 46, 11, 58], [19, 4, 12, 2], [19, 10, 12, 8, "rafRef"], [19, 16, 12, 14], [19, 19, 12, 17], [19, 23, 12, 17, "useRef"], [19, 36, 12, 23], [19, 38, 12, 39], [19, 42, 12, 43], [19, 43, 12, 44], [20, 4, 13, 2], [20, 10, 13, 8, "modelRef"], [20, 18, 13, 16], [20, 21, 13, 19], [20, 25, 13, 19, "useRef"], [20, 38, 13, 25], [20, 40, 13, 38], [20, 44, 13, 42], [20, 45, 13, 43], [21, 4, 14, 2], [21, 10, 14, 8, "debugCounterRef"], [21, 25, 14, 23], [21, 28, 14, 26], [21, 32, 14, 26, "useRef"], [21, 45, 14, 32], [21, 47, 14, 41], [21, 48, 14, 42], [21, 49, 14, 43], [22, 4, 15, 2], [22, 10, 15, 8], [22, 11, 15, 9, "isLoading"], [22, 20, 15, 18], [22, 22, 15, 20, "setIsLoading"], [22, 34, 15, 32], [22, 35, 15, 33], [22, 38, 15, 36], [22, 42, 15, 36, "useState"], [22, 57, 15, 44], [22, 59, 15, 45], [22, 63, 15, 49], [22, 64, 15, 50], [23, 4, 16, 2], [23, 10, 16, 8], [23, 11, 16, 9, "error"], [23, 16, 16, 14], [23, 18, 16, 16, "setError"], [23, 26, 16, 24], [23, 27, 16, 25], [23, 30, 16, 28], [23, 34, 16, 28, "useState"], [23, 49, 16, 36], [23, 51, 16, 52], [23, 55, 16, 56], [23, 56, 16, 57], [24, 4, 18, 2], [24, 8, 18, 2, "useEffect"], [24, 24, 18, 11], [24, 26, 18, 12], [24, 32, 18, 18], [25, 6, 19, 4, "console"], [25, 13, 19, 11], [25, 14, 19, 12, "log"], [25, 17, 19, 15], [25, 18, 19, 16], [25, 69, 19, 67], [25, 71, 19, 69], [26, 8, 19, 71, "containerId"], [26, 19, 19, 82], [27, 8, 19, 84, "width"], [27, 13, 19, 89], [28, 8, 19, 91, "height"], [29, 6, 19, 98], [29, 7, 19, 99], [29, 8, 19, 100], [30, 6, 21, 4], [30, 12, 21, 10, "container"], [30, 21, 21, 19], [30, 24, 21, 22, "document"], [30, 32, 21, 30], [30, 33, 21, 31, "getElementById"], [30, 47, 21, 45], [30, 48, 21, 46, "containerId"], [30, 59, 21, 57], [30, 60, 21, 58], [31, 6, 22, 4], [31, 10, 22, 8], [31, 11, 22, 9, "container"], [31, 20, 22, 18], [31, 22, 22, 20], [32, 8, 23, 6, "console"], [32, 15, 23, 13], [32, 16, 23, 14, "error"], [32, 21, 23, 19], [32, 22, 23, 20], [32, 67, 23, 65], [32, 69, 23, 67, "containerId"], [32, 80, 23, 78], [32, 81, 23, 79], [33, 8, 24, 6, "setError"], [33, 16, 24, 14], [33, 17, 24, 15], [33, 38, 24, 36], [33, 39, 24, 37], [34, 8, 25, 6], [35, 6, 26, 4], [36, 6, 28, 4], [36, 12, 28, 10, "video"], [36, 17, 28, 40], [36, 20, 28, 43, "container"], [36, 29, 28, 52], [36, 30, 28, 53, "querySelector"], [36, 43, 28, 66], [36, 44, 28, 67], [36, 51, 28, 74], [36, 52, 28, 75], [37, 6, 29, 4], [37, 10, 29, 8], [37, 11, 29, 9, "video"], [37, 16, 29, 14], [37, 18, 29, 16], [38, 8, 30, 6, "console"], [38, 15, 30, 13], [38, 16, 30, 14, "error"], [38, 21, 30, 19], [38, 22, 30, 20], [38, 83, 30, 81], [38, 84, 30, 82], [39, 8, 31, 6, "setError"], [39, 16, 31, 14], [39, 17, 31, 15], [39, 42, 31, 40], [39, 43, 31, 41], [40, 8, 32, 6], [41, 6, 33, 4], [43, 6, 35, 4], [44, 6, 36, 4], [44, 12, 36, 10, "canvas"], [44, 18, 36, 16], [44, 21, 36, 19, "canvasRef"], [44, 30, 36, 28], [44, 31, 36, 29, "current"], [44, 38, 36, 36], [45, 6, 37, 4], [45, 10, 37, 8], [45, 11, 37, 9, "canvas"], [45, 17, 37, 15], [45, 19, 37, 17], [46, 8, 38, 6, "console"], [46, 15, 38, 13], [46, 16, 38, 14, "error"], [46, 21, 38, 19], [46, 22, 38, 20], [46, 71, 38, 69], [46, 72, 38, 70], [47, 8, 39, 6, "setError"], [47, 16, 39, 14], [47, 17, 39, 15], [47, 39, 39, 37], [47, 40, 39, 38], [48, 8, 40, 6], [49, 6, 41, 4], [50, 6, 42, 4, "canvas"], [50, 12, 42, 10], [50, 13, 42, 11, "width"], [50, 18, 42, 16], [50, 21, 42, 19, "width"], [50, 26, 42, 24], [51, 6, 43, 4, "canvas"], [51, 12, 43, 10], [51, 13, 43, 11, "height"], [51, 19, 43, 17], [51, 22, 43, 20, "height"], [51, 28, 43, 26], [52, 6, 45, 4], [52, 12, 45, 10, "ctx"], [52, 15, 45, 13], [52, 18, 45, 16, "canvas"], [52, 24, 45, 22], [52, 25, 45, 23, "getContext"], [52, 35, 45, 33], [52, 36, 45, 34], [52, 40, 45, 38], [52, 41, 45, 39], [53, 6, 46, 4], [53, 10, 46, 8], [53, 11, 46, 9, "ctx"], [53, 14, 46, 12], [53, 16, 46, 14], [54, 8, 47, 6, "console"], [54, 15, 47, 13], [54, 16, 47, 14, "error"], [54, 21, 47, 19], [54, 22, 47, 20], [54, 75, 47, 73], [54, 76, 47, 74], [55, 8, 48, 6, "setError"], [55, 16, 48, 14], [55, 17, 48, 15], [55, 47, 48, 45], [55, 48, 48, 46], [56, 8, 49, 6], [57, 6, 50, 4], [58, 6, 52, 4], [58, 10, 52, 8, "running"], [58, 17, 52, 15], [58, 20, 52, 18], [58, 24, 52, 22], [59, 6, 53, 4, "debugCounterRef"], [59, 21, 53, 19], [59, 22, 53, 20, "current"], [59, 29, 53, 27], [59, 32, 53, 30], [59, 33, 53, 31], [60, 6, 55, 4], [60, 12, 55, 10, "loadTensorFlowAndBlazeFace"], [60, 38, 55, 36], [60, 41, 55, 39], [60, 47, 55, 39, "loadTensorFlowAndBlazeFace"], [60, 48, 55, 39], [60, 53, 55, 51], [61, 8, 56, 6], [61, 12, 56, 10], [62, 10, 57, 8, "console"], [62, 17, 57, 15], [62, 18, 57, 16, "log"], [62, 21, 57, 19], [62, 22, 57, 20], [62, 71, 57, 69], [62, 72, 57, 70], [64, 10, 59, 8], [65, 10, 60, 8], [65, 14, 60, 12], [65, 15, 60, 14, "window"], [65, 21, 60, 20], [65, 22, 60, 29, "tf"], [65, 24, 60, 31], [65, 26, 60, 33], [66, 12, 61, 10], [66, 18, 61, 16, "loadScript"], [66, 28, 61, 26], [66, 29, 61, 27], [66, 98, 61, 96], [66, 99, 61, 97], [67, 12, 62, 10, "console"], [67, 19, 62, 17], [67, 20, 62, 18, "log"], [67, 23, 62, 21], [67, 24, 62, 22], [67, 69, 62, 67], [67, 70, 62, 68], [68, 10, 63, 8], [70, 10, 65, 8], [71, 10, 66, 8], [71, 14, 66, 12], [71, 15, 66, 14, "window"], [71, 21, 66, 20], [71, 22, 66, 29, "blazeface"], [71, 31, 66, 38], [71, 33, 66, 40], [72, 12, 67, 10], [72, 18, 67, 16, "loadScript"], [72, 28, 67, 26], [72, 29, 67, 27], [72, 112, 67, 110], [72, 113, 67, 111], [73, 12, 68, 10, "console"], [73, 19, 68, 17], [73, 20, 68, 18, "log"], [73, 23, 68, 21], [73, 24, 68, 22], [73, 65, 68, 63], [73, 66, 68, 64], [74, 10, 69, 8], [76, 10, 71, 8], [77, 10, 72, 8, "console"], [77, 17, 72, 15], [77, 18, 72, 16, "log"], [77, 21, 72, 19], [77, 22, 72, 20], [77, 78, 72, 76], [77, 79, 72, 77], [78, 10, 73, 8], [78, 16, 73, 14, "blazeface"], [78, 25, 73, 23], [78, 28, 73, 27, "window"], [78, 34, 73, 33], [78, 35, 73, 42, "blazeface"], [78, 44, 73, 51], [79, 10, 74, 8, "modelRef"], [79, 18, 74, 16], [79, 19, 74, 17, "current"], [79, 26, 74, 24], [79, 29, 74, 27], [79, 35, 74, 33, "blazeface"], [79, 44, 74, 42], [79, 45, 74, 43, "load"], [79, 49, 74, 47], [79, 50, 74, 48], [79, 51, 74, 49], [80, 10, 75, 8, "console"], [80, 17, 75, 15], [80, 18, 75, 16, "log"], [80, 21, 75, 19], [80, 22, 75, 20], [80, 84, 75, 82], [80, 85, 75, 83], [81, 10, 77, 8, "setIsLoading"], [81, 22, 77, 20], [81, 23, 77, 21], [81, 28, 77, 26], [81, 29, 77, 27], [82, 10, 78, 8, "setError"], [82, 18, 78, 16], [82, 19, 78, 17], [82, 23, 78, 21], [82, 24, 78, 22], [83, 8, 80, 6], [83, 9, 80, 7], [83, 10, 80, 8], [83, 17, 80, 15, "error"], [83, 22, 80, 20], [83, 24, 80, 22], [84, 10, 81, 8, "console"], [84, 17, 81, 15], [84, 18, 81, 16, "error"], [84, 23, 81, 21], [84, 24, 81, 22], [84, 87, 81, 85], [84, 89, 81, 87, "error"], [84, 94, 81, 92], [84, 95, 81, 93], [85, 10, 82, 8, "setError"], [85, 18, 82, 16], [85, 19, 82, 17], [85, 53, 82, 51, "error"], [85, 58, 82, 56], [85, 59, 82, 57, "message"], [85, 66, 82, 64], [85, 68, 82, 66], [85, 69, 82, 67], [86, 10, 83, 8, "setIsLoading"], [86, 22, 83, 20], [86, 23, 83, 21], [86, 28, 83, 26], [86, 29, 83, 27], [87, 8, 84, 6], [88, 6, 85, 4], [88, 7, 85, 5], [89, 6, 87, 4], [89, 12, 87, 10, "loadScript"], [89, 22, 87, 20], [89, 25, 87, 24, "src"], [89, 28, 87, 35], [89, 32, 87, 55], [90, 8, 88, 6], [90, 15, 88, 13], [90, 19, 88, 17, "Promise"], [90, 26, 88, 24], [90, 27, 88, 25], [90, 28, 88, 26, "resolve"], [90, 35, 88, 33], [90, 37, 88, 35, "reject"], [90, 43, 88, 41], [90, 48, 88, 46], [91, 10, 89, 8], [91, 16, 89, 14, "script"], [91, 22, 89, 20], [91, 25, 89, 23, "document"], [91, 33, 89, 31], [91, 34, 89, 32, "createElement"], [91, 47, 89, 45], [91, 48, 89, 46], [91, 56, 89, 54], [91, 57, 89, 55], [92, 10, 90, 8, "script"], [92, 16, 90, 14], [92, 17, 90, 15, "src"], [92, 20, 90, 18], [92, 23, 90, 21, "src"], [92, 26, 90, 24], [93, 10, 91, 8, "script"], [93, 16, 91, 14], [93, 17, 91, 15, "async"], [93, 22, 91, 20], [93, 25, 91, 23], [93, 29, 91, 27], [94, 10, 92, 8, "script"], [94, 16, 92, 14], [94, 17, 92, 15, "onload"], [94, 23, 92, 21], [94, 26, 92, 24], [94, 32, 92, 30, "resolve"], [94, 39, 92, 37], [94, 40, 92, 38], [94, 41, 92, 39], [95, 10, 93, 8, "script"], [95, 16, 93, 14], [95, 17, 93, 15, "onerror"], [95, 24, 93, 22], [95, 27, 93, 25], [95, 33, 93, 31, "reject"], [95, 39, 93, 37], [95, 40, 93, 38], [95, 44, 93, 42, "Error"], [95, 49, 93, 47], [95, 50, 93, 48], [95, 76, 93, 74, "src"], [95, 79, 93, 77], [95, 81, 93, 79], [95, 82, 93, 80], [95, 83, 93, 81], [96, 10, 94, 8, "document"], [96, 18, 94, 16], [96, 19, 94, 17, "head"], [96, 23, 94, 21], [96, 24, 94, 22, "append<PERSON><PERSON><PERSON>"], [96, 35, 94, 33], [96, 36, 94, 34, "script"], [96, 42, 94, 40], [96, 43, 94, 41], [97, 8, 95, 6], [97, 9, 95, 7], [97, 10, 95, 8], [98, 6, 96, 4], [98, 7, 96, 5], [99, 6, 98, 4], [99, 12, 98, 10, "applyFallbackBlur"], [99, 29, 98, 27], [99, 32, 98, 30, "applyFallbackBlur"], [99, 33, 98, 31, "ctx"], [99, 36, 98, 60], [99, 38, 98, 62, "videoW"], [99, 44, 98, 76], [99, 46, 98, 78, "videoH"], [99, 52, 98, 92], [99, 54, 98, 94, "canvasW"], [99, 61, 98, 109], [99, 63, 98, 111, "canvasH"], [99, 70, 98, 126], [99, 75, 98, 131], [100, 8, 99, 6], [101, 8, 100, 6], [101, 14, 100, 12, "scale"], [101, 19, 100, 17], [101, 22, 100, 20, "Math"], [101, 26, 100, 24], [101, 27, 100, 25, "max"], [101, 30, 100, 28], [101, 31, 100, 29, "canvasW"], [101, 38, 100, 36], [101, 41, 100, 39, "videoW"], [101, 47, 100, 45], [101, 49, 100, 47, "canvasH"], [101, 56, 100, 54], [101, 59, 100, 57, "videoH"], [101, 65, 100, 63], [101, 66, 100, 64], [102, 8, 101, 6], [102, 14, 101, 12, "scaledW"], [102, 21, 101, 19], [102, 24, 101, 22, "videoW"], [102, 30, 101, 28], [102, 33, 101, 31, "scale"], [102, 38, 101, 36], [103, 8, 102, 6], [103, 14, 102, 12, "scaledH"], [103, 21, 102, 19], [103, 24, 102, 22, "videoH"], [103, 30, 102, 28], [103, 33, 102, 31, "scale"], [103, 38, 102, 36], [104, 8, 103, 6], [104, 14, 103, 12, "offsetX"], [104, 21, 103, 19], [104, 24, 103, 22], [104, 25, 103, 23, "canvasW"], [104, 32, 103, 30], [104, 35, 103, 33, "scaledW"], [104, 42, 103, 40], [104, 46, 103, 44], [104, 47, 103, 45], [105, 8, 104, 6], [105, 14, 104, 12, "offsetY"], [105, 21, 104, 19], [105, 24, 104, 22], [105, 25, 104, 23, "canvasH"], [105, 32, 104, 30], [105, 35, 104, 33, "scaledH"], [105, 42, 104, 40], [105, 46, 104, 44], [105, 47, 104, 45], [107, 8, 106, 6], [108, 8, 107, 6], [108, 14, 107, 12, "faceAreas"], [108, 23, 107, 21], [108, 26, 107, 24], [108, 27, 108, 8], [109, 10, 108, 10, "x"], [109, 11, 108, 11], [109, 13, 108, 13], [109, 17, 108, 17], [110, 10, 108, 19, "y"], [110, 11, 108, 20], [110, 13, 108, 22], [110, 17, 108, 26], [111, 10, 108, 28, "w"], [111, 11, 108, 29], [111, 13, 108, 31], [111, 16, 108, 34], [112, 10, 108, 36, "h"], [112, 11, 108, 37], [112, 13, 108, 39], [113, 8, 108, 43], [113, 9, 108, 44], [114, 8, 108, 46], [115, 8, 109, 8], [116, 10, 109, 10, "x"], [116, 11, 109, 11], [116, 13, 109, 13], [116, 16, 109, 16], [117, 10, 109, 18, "y"], [117, 11, 109, 19], [117, 13, 109, 21], [117, 16, 109, 24], [118, 10, 109, 26, "w"], [118, 11, 109, 27], [118, 13, 109, 29], [118, 17, 109, 33], [119, 10, 109, 35, "h"], [119, 11, 109, 36], [119, 13, 109, 38], [120, 8, 109, 42], [120, 9, 109, 43], [121, 8, 109, 46], [122, 8, 110, 8], [123, 10, 110, 10, "x"], [123, 11, 110, 11], [123, 13, 110, 13], [123, 17, 110, 17], [124, 10, 110, 19, "y"], [124, 11, 110, 20], [124, 13, 110, 22], [124, 16, 110, 25], [125, 10, 110, 27, "w"], [125, 11, 110, 28], [125, 13, 110, 30], [125, 17, 110, 34], [126, 10, 110, 36, "h"], [126, 11, 110, 37], [126, 13, 110, 39], [127, 8, 110, 43], [127, 9, 110, 44], [127, 10, 110, 46], [128, 8, 110, 46], [128, 9, 111, 7], [129, 8, 113, 6, "faceAreas"], [129, 17, 113, 15], [129, 18, 113, 16, "for<PERSON>ach"], [129, 25, 113, 23], [129, 26, 113, 24, "area"], [129, 30, 113, 28], [129, 34, 113, 32], [130, 10, 114, 8], [130, 16, 114, 14, "x"], [130, 17, 114, 15], [130, 20, 114, 18, "area"], [130, 24, 114, 22], [130, 25, 114, 23, "x"], [130, 26, 114, 24], [130, 29, 114, 27, "scaledW"], [130, 36, 114, 34], [130, 39, 114, 37, "offsetX"], [130, 46, 114, 44], [131, 10, 115, 8], [131, 16, 115, 14, "y"], [131, 17, 115, 15], [131, 20, 115, 18, "area"], [131, 24, 115, 22], [131, 25, 115, 23, "y"], [131, 26, 115, 24], [131, 29, 115, 27, "scaledH"], [131, 36, 115, 34], [131, 39, 115, 37, "offsetY"], [131, 46, 115, 44], [132, 10, 116, 8], [132, 16, 116, 14, "w"], [132, 17, 116, 15], [132, 20, 116, 18, "area"], [132, 24, 116, 22], [132, 25, 116, 23, "w"], [132, 26, 116, 24], [132, 29, 116, 27, "scaledW"], [132, 36, 116, 34], [133, 10, 117, 8], [133, 16, 117, 14, "h"], [133, 17, 117, 15], [133, 20, 117, 18, "area"], [133, 24, 117, 22], [133, 25, 117, 23, "h"], [133, 26, 117, 24], [133, 29, 117, 27, "scaledH"], [133, 36, 117, 34], [134, 10, 119, 8, "ctx"], [134, 13, 119, 11], [134, 14, 119, 12, "save"], [134, 18, 119, 16], [134, 19, 119, 17], [134, 20, 119, 18], [135, 10, 120, 8, "ctx"], [135, 13, 120, 11], [135, 14, 120, 12, "beginPath"], [135, 23, 120, 21], [135, 24, 120, 22], [135, 25, 120, 23], [136, 10, 121, 8, "ctx"], [136, 13, 121, 11], [136, 14, 121, 12, "ellipse"], [136, 21, 121, 19], [136, 22, 121, 20, "x"], [136, 23, 121, 21], [136, 26, 121, 24, "w"], [136, 27, 121, 25], [136, 30, 121, 28], [136, 31, 121, 29], [136, 33, 121, 31, "y"], [136, 34, 121, 32], [136, 37, 121, 35, "h"], [136, 38, 121, 36], [136, 41, 121, 39], [136, 42, 121, 40], [136, 44, 121, 42, "w"], [136, 45, 121, 43], [136, 48, 121, 46], [136, 49, 121, 47], [136, 51, 121, 49, "h"], [136, 52, 121, 50], [136, 55, 121, 53], [136, 56, 121, 54], [136, 58, 121, 56], [136, 59, 121, 57], [136, 61, 121, 59], [136, 62, 121, 60], [136, 64, 121, 62, "Math"], [136, 68, 121, 66], [136, 69, 121, 67, "PI"], [136, 71, 121, 69], [136, 74, 121, 72], [136, 75, 121, 73], [136, 76, 121, 74], [137, 10, 122, 8, "ctx"], [137, 13, 122, 11], [137, 14, 122, 12, "clip"], [137, 18, 122, 16], [137, 19, 122, 17], [137, 20, 122, 18], [138, 10, 123, 8, "ctx"], [138, 13, 123, 11], [138, 14, 123, 12, "filter"], [138, 20, 123, 18], [138, 23, 123, 21], [138, 35, 123, 33], [139, 10, 124, 8, "ctx"], [139, 13, 124, 11], [139, 14, 124, 12, "drawImage"], [139, 23, 124, 21], [139, 24, 124, 22, "video"], [139, 29, 124, 27], [139, 31, 124, 29], [139, 32, 124, 30], [139, 34, 124, 32], [139, 35, 124, 33], [139, 37, 124, 35, "canvasW"], [139, 44, 124, 42], [139, 46, 124, 44, "canvasH"], [139, 53, 124, 51], [139, 54, 124, 52], [140, 10, 125, 8, "ctx"], [140, 13, 125, 11], [140, 14, 125, 12, "restore"], [140, 21, 125, 19], [140, 22, 125, 20], [140, 23, 125, 21], [141, 8, 126, 6], [141, 9, 126, 7], [141, 10, 126, 8], [142, 6, 127, 4], [142, 7, 127, 5], [143, 6, 129, 4], [143, 12, 129, 10, "loop"], [143, 16, 129, 14], [143, 19, 129, 17], [143, 25, 129, 17, "loop"], [143, 26, 129, 17], [143, 31, 129, 29], [144, 8, 130, 6], [144, 12, 130, 10], [144, 13, 130, 11, "running"], [144, 20, 130, 18], [144, 22, 130, 20], [145, 8, 131, 6, "rafRef"], [145, 14, 131, 12], [145, 15, 131, 13, "current"], [145, 22, 131, 20], [145, 25, 131, 23, "requestAnimationFrame"], [145, 46, 131, 44], [145, 47, 131, 45, "loop"], [145, 51, 131, 49], [145, 52, 131, 50], [146, 8, 133, 6], [146, 12, 133, 10], [146, 13, 133, 11, "modelRef"], [146, 21, 133, 19], [146, 22, 133, 20, "current"], [146, 29, 133, 27], [146, 31, 133, 29], [147, 10, 134, 8], [148, 10, 135, 8, "debugCounterRef"], [148, 25, 135, 23], [148, 26, 135, 24, "current"], [148, 33, 135, 31], [148, 35, 135, 33], [149, 10, 136, 8], [149, 14, 136, 12, "debugCounterRef"], [149, 29, 136, 27], [149, 30, 136, 28, "current"], [149, 37, 136, 35], [149, 40, 136, 38], [149, 42, 136, 40], [149, 47, 136, 45], [149, 48, 136, 46], [149, 50, 136, 48], [150, 12, 136, 50], [151, 12, 137, 10, "console"], [151, 19, 137, 17], [151, 20, 137, 18, "log"], [151, 23, 137, 21], [151, 24, 137, 22], [151, 87, 137, 85], [151, 88, 137, 86], [152, 10, 138, 8], [153, 10, 139, 8], [154, 8, 140, 6], [155, 8, 142, 6], [155, 12, 142, 10], [156, 10, 143, 8], [156, 16, 143, 14, "videoW"], [156, 22, 143, 20], [156, 25, 143, 23, "video"], [156, 30, 143, 28], [156, 31, 143, 29, "videoWidth"], [156, 41, 143, 39], [156, 45, 143, 43, "width"], [156, 50, 143, 48], [157, 10, 144, 8], [157, 16, 144, 14, "videoH"], [157, 22, 144, 20], [157, 25, 144, 23, "video"], [157, 30, 144, 28], [157, 31, 144, 29, "videoHeight"], [157, 42, 144, 40], [157, 46, 144, 44, "height"], [157, 52, 144, 50], [159, 10, 146, 8], [160, 10, 147, 8, "ctx"], [160, 13, 147, 11], [160, 14, 147, 12, "clearRect"], [160, 23, 147, 21], [160, 24, 147, 22], [160, 25, 147, 23], [160, 27, 147, 25], [160, 28, 147, 26], [160, 30, 147, 28, "canvas"], [160, 36, 147, 34], [160, 37, 147, 35, "width"], [160, 42, 147, 40], [160, 44, 147, 42, "canvas"], [160, 50, 147, 48], [160, 51, 147, 49, "height"], [160, 57, 147, 55], [160, 58, 147, 56], [162, 10, 149, 8], [163, 10, 150, 8, "debugCounterRef"], [163, 25, 150, 23], [163, 26, 150, 24, "current"], [163, 33, 150, 31], [163, 35, 150, 33], [164, 10, 151, 8], [164, 14, 151, 12, "debugCounterRef"], [164, 29, 151, 27], [164, 30, 151, 28, "current"], [164, 37, 151, 35], [164, 40, 151, 38], [164, 43, 151, 41], [164, 48, 151, 46], [164, 49, 151, 47], [164, 51, 151, 49], [165, 12, 151, 51], [166, 12, 152, 10, "console"], [166, 19, 152, 17], [166, 20, 152, 18, "log"], [166, 23, 152, 21], [166, 24, 152, 22], [166, 62, 152, 60], [166, 64, 152, 62], [167, 14, 153, 12, "dimensions"], [167, 24, 153, 22], [167, 26, 153, 24], [167, 29, 153, 27, "videoW"], [167, 35, 153, 33], [167, 39, 153, 37, "videoH"], [167, 45, 153, 43], [167, 47, 153, 45], [168, 14, 154, 12, "readyState"], [168, 24, 154, 22], [168, 26, 154, 24, "video"], [168, 31, 154, 29], [168, 32, 154, 30, "readyState"], [168, 42, 154, 40], [169, 14, 155, 12, "paused"], [169, 20, 155, 18], [169, 22, 155, 20, "video"], [169, 27, 155, 25], [169, 28, 155, 26, "paused"], [169, 34, 155, 32], [170, 14, 156, 12, "currentTime"], [170, 25, 156, 23], [170, 27, 156, 25, "video"], [170, 32, 156, 30], [170, 33, 156, 31, "currentTime"], [171, 12, 157, 10], [171, 13, 157, 11], [171, 14, 157, 12], [172, 10, 158, 8], [173, 10, 160, 8], [173, 14, 160, 12, "videoW"], [173, 20, 160, 18], [173, 25, 160, 23], [173, 26, 160, 24], [173, 30, 160, 28, "videoH"], [173, 36, 160, 34], [173, 41, 160, 39], [173, 42, 160, 40], [173, 44, 160, 42], [174, 12, 161, 10], [174, 16, 161, 14, "debugCounterRef"], [174, 31, 161, 29], [174, 32, 161, 30, "current"], [174, 39, 161, 37], [174, 42, 161, 40], [174, 44, 161, 42], [174, 49, 161, 47], [174, 50, 161, 48], [174, 52, 161, 50], [175, 14, 162, 12, "console"], [175, 21, 162, 19], [175, 22, 162, 20, "log"], [175, 25, 162, 23], [175, 26, 162, 24], [175, 80, 162, 78], [175, 82, 162, 80, "videoW"], [175, 88, 162, 86], [175, 90, 162, 88], [175, 93, 162, 91], [175, 95, 162, 93, "videoH"], [175, 101, 162, 99], [175, 102, 162, 100], [176, 12, 163, 10], [177, 12, 164, 10], [178, 10, 165, 8], [180, 10, 167, 8], [181, 10, 168, 8], [181, 16, 168, 14, "tf"], [181, 18, 168, 16], [181, 21, 168, 20, "window"], [181, 27, 168, 26], [181, 28, 168, 35, "tf"], [181, 30, 168, 37], [182, 10, 169, 8], [182, 16, 169, 14, "tensor"], [182, 22, 169, 20], [182, 25, 169, 23, "tf"], [182, 27, 169, 25], [182, 28, 169, 26, "browser"], [182, 35, 169, 33], [182, 36, 169, 34, "fromPixels"], [182, 46, 169, 44], [182, 47, 169, 45, "video"], [182, 52, 169, 50], [182, 53, 169, 51], [183, 10, 170, 8], [183, 16, 170, 14, "predictions"], [183, 27, 170, 25], [183, 30, 170, 28], [183, 36, 170, 34, "modelRef"], [183, 44, 170, 42], [183, 45, 170, 43, "current"], [183, 52, 170, 50], [183, 53, 170, 51, "estimateFaces"], [183, 66, 170, 64], [183, 67, 170, 65, "tensor"], [183, 73, 170, 71], [183, 75, 170, 73], [183, 80, 170, 78], [183, 82, 170, 80], [183, 85, 170, 83], [183, 86, 170, 84], [183, 87, 170, 85], [183, 88, 170, 86], [184, 10, 171, 8, "tensor"], [184, 16, 171, 14], [184, 17, 171, 15, "dispose"], [184, 24, 171, 22], [184, 25, 171, 23], [184, 26, 171, 24], [184, 27, 171, 25], [184, 28, 171, 26], [186, 10, 173, 8], [187, 10, 174, 8], [187, 14, 174, 12, "predictions"], [187, 25, 174, 23], [187, 26, 174, 24, "length"], [187, 32, 174, 30], [187, 35, 174, 33], [187, 36, 174, 34], [187, 38, 174, 36], [188, 12, 175, 10, "console"], [188, 19, 175, 17], [188, 20, 175, 18, "log"], [188, 23, 175, 21], [188, 24, 175, 22], [188, 62, 175, 60, "predictions"], [188, 73, 175, 71], [188, 74, 175, 72, "length"], [188, 80, 175, 78], [188, 101, 175, 99, "debugCounterRef"], [188, 116, 175, 114], [188, 117, 175, 115, "current"], [188, 124, 175, 122], [188, 126, 175, 124], [188, 127, 175, 125], [189, 10, 176, 8], [189, 11, 176, 9], [189, 17, 176, 15], [189, 21, 176, 19, "debugCounterRef"], [189, 36, 176, 34], [189, 37, 176, 35, "current"], [189, 44, 176, 42], [189, 47, 176, 45], [189, 50, 176, 48], [189, 55, 176, 53], [189, 56, 176, 54], [189, 58, 176, 56], [190, 12, 176, 58], [191, 12, 177, 10, "console"], [191, 19, 177, 17], [191, 20, 177, 18, "log"], [191, 23, 177, 21], [191, 24, 177, 22], [191, 75, 177, 73, "debugCounterRef"], [191, 90, 177, 88], [191, 91, 177, 89, "current"], [191, 98, 177, 96], [191, 101, 177, 99], [191, 102, 177, 100], [192, 10, 178, 8], [193, 10, 180, 8], [193, 14, 180, 12, "predictions"], [193, 25, 180, 23], [193, 26, 180, 24, "length"], [193, 32, 180, 30], [193, 37, 180, 35], [193, 38, 180, 36], [193, 40, 180, 38], [194, 12, 181, 10], [195, 12, 182, 10], [195, 16, 182, 14, "debugCounterRef"], [195, 31, 182, 29], [195, 32, 182, 30, "current"], [195, 39, 182, 37], [195, 42, 182, 40], [195, 44, 182, 42], [195, 46, 182, 44], [196, 14, 182, 46], [197, 14, 183, 12, "applyFallbackBlur"], [197, 31, 183, 29], [197, 32, 183, 30, "ctx"], [197, 35, 183, 33], [197, 37, 183, 35, "videoW"], [197, 43, 183, 41], [197, 45, 183, 43, "videoH"], [197, 51, 183, 49], [197, 53, 183, 51, "width"], [197, 58, 183, 56], [197, 60, 183, 58, "height"], [197, 66, 183, 64], [197, 67, 183, 65], [198, 12, 184, 10], [199, 12, 185, 10], [200, 10, 186, 8], [202, 10, 188, 8], [203, 10, 189, 8], [203, 16, 189, 14, "scale"], [203, 21, 189, 19], [203, 24, 189, 22, "Math"], [203, 28, 189, 26], [203, 29, 189, 27, "max"], [203, 32, 189, 30], [203, 33, 189, 31, "width"], [203, 38, 189, 36], [203, 41, 189, 39, "videoW"], [203, 47, 189, 45], [203, 49, 189, 47, "height"], [203, 55, 189, 53], [203, 58, 189, 56, "videoH"], [203, 64, 189, 62], [203, 65, 189, 63], [204, 10, 190, 8], [204, 16, 190, 14, "scaledW"], [204, 23, 190, 21], [204, 26, 190, 24, "videoW"], [204, 32, 190, 30], [204, 35, 190, 33, "scale"], [204, 40, 190, 38], [205, 10, 191, 8], [205, 16, 191, 14, "scaledH"], [205, 23, 191, 21], [205, 26, 191, 24, "videoH"], [205, 32, 191, 30], [205, 35, 191, 33, "scale"], [205, 40, 191, 38], [206, 10, 192, 8], [206, 16, 192, 14, "offsetX"], [206, 23, 192, 21], [206, 26, 192, 24], [206, 27, 192, 25, "width"], [206, 32, 192, 30], [206, 35, 192, 33, "scaledW"], [206, 42, 192, 40], [206, 46, 192, 44], [206, 47, 192, 45], [207, 10, 193, 8], [207, 16, 193, 14, "offsetY"], [207, 23, 193, 21], [207, 26, 193, 24], [207, 27, 193, 25, "height"], [207, 33, 193, 31], [207, 36, 193, 34, "scaledH"], [207, 43, 193, 41], [207, 47, 193, 45], [207, 48, 193, 46], [209, 10, 195, 8], [210, 10, 196, 8, "predictions"], [210, 21, 196, 19], [210, 22, 196, 20, "for<PERSON>ach"], [210, 29, 196, 27], [210, 30, 196, 28], [210, 31, 196, 29, "prediction"], [210, 41, 196, 44], [210, 43, 196, 46, "index"], [210, 48, 196, 59], [210, 53, 196, 64], [211, 12, 197, 10], [211, 18, 197, 16], [211, 19, 197, 17, "x1"], [211, 21, 197, 19], [211, 23, 197, 21, "y1"], [211, 25, 197, 23], [211, 26, 197, 24], [211, 29, 197, 27, "prediction"], [211, 39, 197, 37], [211, 40, 197, 38, "topLeft"], [211, 47, 197, 45], [212, 12, 198, 10], [212, 18, 198, 16], [212, 19, 198, 17, "x2"], [212, 21, 198, 19], [212, 23, 198, 21, "y2"], [212, 25, 198, 23], [212, 26, 198, 24], [212, 29, 198, 27, "prediction"], [212, 39, 198, 37], [212, 40, 198, 38, "bottomRight"], [212, 51, 198, 49], [214, 12, 200, 10], [215, 12, 201, 10], [215, 18, 201, 16, "centerX"], [215, 25, 201, 23], [215, 28, 201, 26], [215, 29, 201, 27, "x1"], [215, 31, 201, 29], [215, 34, 201, 32, "x2"], [215, 36, 201, 34], [215, 40, 201, 38], [215, 41, 201, 39], [216, 12, 202, 10], [216, 18, 202, 16, "centerY"], [216, 25, 202, 23], [216, 28, 202, 26], [216, 29, 202, 27, "y1"], [216, 31, 202, 29], [216, 34, 202, 32, "y2"], [216, 36, 202, 34], [216, 40, 202, 38], [216, 41, 202, 39], [217, 12, 203, 10], [217, 18, 203, 16, "faceWidth"], [217, 27, 203, 25], [217, 30, 203, 28], [217, 31, 203, 29, "x2"], [217, 33, 203, 31], [217, 36, 203, 34, "x1"], [217, 38, 203, 36], [217, 42, 203, 40], [217, 45, 203, 43], [217, 46, 203, 44], [217, 47, 203, 45], [218, 12, 204, 10], [218, 18, 204, 16, "faceHeight"], [218, 28, 204, 26], [218, 31, 204, 29], [218, 32, 204, 30, "y2"], [218, 34, 204, 32], [218, 37, 204, 35, "y1"], [218, 39, 204, 37], [218, 43, 204, 41], [218, 46, 204, 44], [218, 47, 204, 45], [218, 48, 204, 46], [220, 12, 206, 10], [220, 18, 206, 16, "expandedX1"], [220, 28, 206, 26], [220, 31, 206, 29, "centerX"], [220, 38, 206, 36], [220, 41, 206, 39, "faceWidth"], [220, 50, 206, 48], [220, 53, 206, 51], [220, 54, 206, 52], [221, 12, 207, 10], [221, 18, 207, 16, "expandedY1"], [221, 28, 207, 26], [221, 31, 207, 29, "centerY"], [221, 38, 207, 36], [221, 41, 207, 39, "faceHeight"], [221, 51, 207, 49], [221, 54, 207, 52], [221, 55, 207, 53], [223, 12, 209, 10], [224, 12, 210, 10], [224, 18, 210, 16, "canvasX"], [224, 25, 210, 23], [224, 28, 210, 26, "expandedX1"], [224, 38, 210, 36], [224, 41, 210, 39, "scale"], [224, 46, 210, 44], [224, 49, 210, 47, "offsetX"], [224, 56, 210, 54], [225, 12, 211, 10], [225, 18, 211, 16, "canvasY"], [225, 25, 211, 23], [225, 28, 211, 26, "expandedY1"], [225, 38, 211, 36], [225, 41, 211, 39, "scale"], [225, 46, 211, 44], [225, 49, 211, 47, "offsetY"], [225, 56, 211, 54], [226, 12, 212, 10], [226, 18, 212, 16, "canvasW"], [226, 25, 212, 23], [226, 28, 212, 26, "faceWidth"], [226, 37, 212, 35], [226, 40, 212, 38, "scale"], [226, 45, 212, 43], [227, 12, 213, 10], [227, 18, 213, 16, "canvasH"], [227, 25, 213, 23], [227, 28, 213, 26, "faceHeight"], [227, 38, 213, 36], [227, 41, 213, 39, "scale"], [227, 46, 213, 44], [229, 12, 215, 10], [230, 12, 216, 10, "ctx"], [230, 15, 216, 13], [230, 16, 216, 14, "save"], [230, 20, 216, 18], [230, 21, 216, 19], [230, 22, 216, 20], [231, 12, 217, 10, "ctx"], [231, 15, 217, 13], [231, 16, 217, 14, "beginPath"], [231, 25, 217, 23], [231, 26, 217, 24], [231, 27, 217, 25], [232, 12, 218, 10, "ctx"], [232, 15, 218, 13], [232, 16, 218, 14, "ellipse"], [232, 23, 218, 21], [232, 24, 218, 22, "canvasX"], [232, 31, 218, 29], [232, 34, 218, 32, "canvasW"], [232, 41, 218, 39], [232, 44, 218, 42], [232, 45, 218, 43], [232, 47, 218, 45, "canvasY"], [232, 54, 218, 52], [232, 57, 218, 55, "canvasH"], [232, 64, 218, 62], [232, 67, 218, 65], [232, 68, 218, 66], [232, 70, 218, 68, "canvasW"], [232, 77, 218, 75], [232, 80, 218, 78], [232, 81, 218, 79], [232, 83, 218, 81, "canvasH"], [232, 90, 218, 88], [232, 93, 218, 91], [232, 94, 218, 92], [232, 96, 218, 94], [232, 97, 218, 95], [232, 99, 218, 97], [232, 100, 218, 98], [232, 102, 218, 100, "Math"], [232, 106, 218, 104], [232, 107, 218, 105, "PI"], [232, 109, 218, 107], [232, 112, 218, 110], [232, 113, 218, 111], [232, 114, 218, 112], [233, 12, 219, 10, "ctx"], [233, 15, 219, 13], [233, 16, 219, 14, "clip"], [233, 20, 219, 18], [233, 21, 219, 19], [233, 22, 219, 20], [234, 12, 220, 10, "ctx"], [234, 15, 220, 13], [234, 16, 220, 14, "filter"], [234, 22, 220, 20], [234, 25, 220, 23], [234, 37, 220, 35], [235, 12, 221, 10, "ctx"], [235, 15, 221, 13], [235, 16, 221, 14, "drawImage"], [235, 25, 221, 23], [235, 26, 221, 24, "video"], [235, 31, 221, 29], [235, 33, 221, 31], [235, 34, 221, 32], [235, 36, 221, 34], [235, 37, 221, 35], [235, 39, 221, 37, "width"], [235, 44, 221, 42], [235, 46, 221, 44, "height"], [235, 52, 221, 50], [235, 53, 221, 51], [236, 12, 222, 10, "ctx"], [236, 15, 222, 13], [236, 16, 222, 14, "restore"], [236, 23, 222, 21], [236, 24, 222, 22], [236, 25, 222, 23], [237, 12, 224, 10], [237, 16, 224, 14, "debugCounterRef"], [237, 31, 224, 29], [237, 32, 224, 30, "current"], [237, 39, 224, 37], [237, 42, 224, 40], [237, 44, 224, 42], [237, 49, 224, 47], [237, 50, 224, 48], [237, 52, 224, 50], [238, 14, 225, 12, "console"], [238, 21, 225, 19], [238, 22, 225, 20, "log"], [238, 25, 225, 23], [238, 26, 225, 24], [238, 65, 225, 63, "index"], [238, 70, 225, 68], [238, 73, 225, 71], [238, 74, 225, 72], [238, 82, 225, 80, "Math"], [238, 86, 225, 84], [238, 87, 225, 85, "round"], [238, 92, 225, 90], [238, 93, 225, 91, "canvasX"], [238, 100, 225, 98], [238, 101, 225, 99], [238, 106, 225, 104, "Math"], [238, 110, 225, 108], [238, 111, 225, 109, "round"], [238, 116, 225, 114], [238, 117, 225, 115, "canvasY"], [238, 124, 225, 122], [238, 125, 225, 123], [238, 128, 225, 126], [238, 129, 225, 127], [239, 12, 226, 10], [240, 10, 227, 8], [240, 11, 227, 9], [240, 12, 227, 10], [241, 8, 229, 6], [241, 9, 229, 7], [241, 10, 229, 8], [241, 17, 229, 15, "error"], [241, 22, 229, 20], [241, 24, 229, 22], [242, 10, 230, 8], [243, 10, 231, 8], [243, 14, 231, 12, "debugCounterRef"], [243, 29, 231, 27], [243, 30, 231, 28, "current"], [243, 37, 231, 35], [243, 40, 231, 38], [243, 43, 231, 41], [243, 48, 231, 46], [243, 49, 231, 47], [243, 51, 231, 49], [244, 12, 232, 10, "console"], [244, 19, 232, 17], [244, 20, 232, 18, "error"], [244, 25, 232, 23], [244, 26, 232, 24], [244, 67, 232, 65], [244, 69, 232, 67, "error"], [244, 74, 232, 72], [244, 75, 232, 73], [245, 10, 233, 8], [246, 8, 234, 6], [247, 6, 235, 4], [247, 7, 235, 5], [249, 6, 237, 4], [250, 6, 238, 4, "loadTensorFlowAndBlazeFace"], [250, 32, 238, 30], [250, 33, 238, 31], [250, 34, 238, 32], [250, 35, 238, 33, "then"], [250, 39, 238, 37], [250, 40, 238, 38], [250, 46, 238, 44], [251, 8, 239, 6], [251, 12, 239, 10, "running"], [251, 19, 239, 17], [251, 21, 239, 19], [252, 10, 240, 8, "rafRef"], [252, 16, 240, 14], [252, 17, 240, 15, "current"], [252, 24, 240, 22], [252, 27, 240, 25, "requestAnimationFrame"], [252, 48, 240, 46], [252, 49, 240, 47, "loop"], [252, 53, 240, 51], [252, 54, 240, 52], [253, 8, 241, 6], [254, 6, 242, 4], [254, 7, 242, 5], [254, 8, 242, 6], [255, 6, 244, 4], [255, 13, 244, 11], [255, 19, 244, 17], [256, 8, 245, 6, "running"], [256, 15, 245, 13], [256, 18, 245, 16], [256, 23, 245, 21], [257, 8, 246, 6], [257, 12, 246, 10, "rafRef"], [257, 18, 246, 16], [257, 19, 246, 17, "current"], [257, 26, 246, 24], [257, 28, 246, 26], [258, 10, 247, 8, "cancelAnimationFrame"], [258, 30, 247, 28], [258, 31, 247, 29, "rafRef"], [258, 37, 247, 35], [258, 38, 247, 36, "current"], [258, 45, 247, 43], [258, 46, 247, 44], [259, 8, 248, 6], [260, 6, 249, 4], [260, 7, 249, 5], [261, 4, 250, 2], [261, 5, 250, 3], [261, 7, 250, 5], [261, 8, 250, 6, "containerId"], [261, 19, 250, 17], [261, 21, 250, 19, "width"], [261, 26, 250, 24], [261, 28, 250, 26, "height"], [261, 34, 250, 32], [261, 35, 250, 33], [261, 36, 250, 34], [262, 4, 252, 2], [262, 24, 253, 4], [262, 28, 253, 4, "_jsxDevRuntime"], [262, 42, 253, 4], [262, 43, 253, 4, "jsxDEV"], [262, 49, 253, 4], [262, 51, 253, 4, "_jsxDevRuntime"], [262, 65, 253, 4], [262, 66, 253, 4, "Fragment"], [262, 74, 253, 4], [263, 6, 253, 4, "children"], [263, 14, 253, 4], [263, 30, 254, 6], [263, 34, 254, 6, "_jsxDevRuntime"], [263, 48, 254, 6], [263, 49, 254, 6, "jsxDEV"], [263, 55, 254, 6], [264, 8, 255, 8, "ref"], [264, 11, 255, 11], [264, 13, 255, 13, "canvasRef"], [264, 22, 255, 23], [265, 8, 256, 8, "style"], [265, 13, 256, 13], [265, 15, 256, 15], [266, 10, 257, 10, "position"], [266, 18, 257, 18], [266, 20, 257, 20], [266, 30, 257, 30], [267, 10, 258, 10, "left"], [267, 14, 258, 14], [267, 16, 258, 16], [267, 17, 258, 17], [268, 10, 259, 10, "top"], [268, 13, 259, 13], [268, 15, 259, 15], [268, 16, 259, 16], [269, 10, 260, 10, "width"], [269, 15, 260, 15], [270, 10, 261, 10, "height"], [270, 16, 261, 16], [271, 10, 262, 10, "pointerEvents"], [271, 23, 262, 23], [271, 25, 262, 25], [271, 31, 262, 31], [272, 10, 263, 10, "zIndex"], [272, 16, 263, 16], [272, 18, 263, 18], [273, 8, 264, 8], [274, 6, 264, 10], [275, 8, 264, 10, "fileName"], [275, 16, 264, 10], [275, 18, 264, 10, "_jsxFileName"], [275, 30, 264, 10], [276, 8, 264, 10, "lineNumber"], [276, 18, 264, 10], [277, 8, 264, 10, "columnNumber"], [277, 20, 264, 10], [278, 6, 264, 10], [278, 13, 265, 7], [278, 14, 265, 8], [278, 16, 266, 7, "isLoading"], [278, 25, 266, 16], [278, 42, 267, 8], [278, 46, 267, 8, "_jsxDevRuntime"], [278, 60, 267, 8], [278, 61, 267, 8, "jsxDEV"], [278, 67, 267, 8], [279, 8, 267, 13, "style"], [279, 13, 267, 18], [279, 15, 267, 20], [280, 10, 268, 10, "position"], [280, 18, 268, 18], [280, 20, 268, 20], [280, 30, 268, 30], [281, 10, 269, 10, "top"], [281, 13, 269, 13], [281, 15, 269, 15], [281, 17, 269, 17], [282, 10, 270, 10, "left"], [282, 14, 270, 14], [282, 16, 270, 16], [282, 18, 270, 18], [283, 10, 271, 10, "background"], [283, 20, 271, 20], [283, 22, 271, 22], [283, 39, 271, 39], [284, 10, 272, 10, "color"], [284, 15, 272, 15], [284, 17, 272, 17], [284, 24, 272, 24], [285, 10, 273, 10, "padding"], [285, 17, 273, 17], [285, 19, 273, 19], [285, 29, 273, 29], [286, 10, 274, 10, "borderRadius"], [286, 22, 274, 22], [286, 24, 274, 24], [286, 29, 274, 29], [287, 10, 275, 10, "fontSize"], [287, 18, 275, 18], [287, 20, 275, 20], [287, 26, 275, 26], [288, 10, 276, 10, "zIndex"], [288, 16, 276, 16], [288, 18, 276, 18], [289, 8, 277, 8], [289, 9, 277, 10], [290, 8, 277, 10, "children"], [290, 16, 277, 10], [290, 18, 277, 11], [291, 6, 279, 8], [292, 8, 279, 8, "fileName"], [292, 16, 279, 8], [292, 18, 279, 8, "_jsxFileName"], [292, 30, 279, 8], [293, 8, 279, 8, "lineNumber"], [293, 18, 279, 8], [294, 8, 279, 8, "columnNumber"], [294, 20, 279, 8], [295, 6, 279, 8], [295, 13, 279, 13], [295, 14, 280, 7], [295, 16, 281, 7, "error"], [295, 21, 281, 12], [295, 38, 282, 8], [295, 42, 282, 8, "_jsxDevRuntime"], [295, 56, 282, 8], [295, 57, 282, 8, "jsxDEV"], [295, 63, 282, 8], [296, 8, 282, 13, "style"], [296, 13, 282, 18], [296, 15, 282, 20], [297, 10, 283, 10, "position"], [297, 18, 283, 18], [297, 20, 283, 20], [297, 30, 283, 30], [298, 10, 284, 10, "top"], [298, 13, 284, 13], [298, 15, 284, 15], [298, 17, 284, 17], [299, 10, 285, 10, "left"], [299, 14, 285, 14], [299, 16, 285, 16], [299, 18, 285, 18], [300, 10, 286, 10, "background"], [300, 20, 286, 20], [300, 22, 286, 22], [300, 41, 286, 41], [301, 10, 287, 10, "color"], [301, 15, 287, 15], [301, 17, 287, 17], [301, 24, 287, 24], [302, 10, 288, 10, "padding"], [302, 17, 288, 17], [302, 19, 288, 19], [302, 29, 288, 29], [303, 10, 289, 10, "borderRadius"], [303, 22, 289, 22], [303, 24, 289, 24], [303, 29, 289, 29], [304, 10, 290, 10, "fontSize"], [304, 18, 290, 18], [304, 20, 290, 20], [304, 26, 290, 26], [305, 10, 291, 10, "zIndex"], [305, 16, 291, 16], [305, 18, 291, 18], [306, 8, 292, 8], [306, 9, 292, 10], [307, 8, 292, 10, "children"], [307, 16, 292, 10], [307, 19, 292, 11], [307, 43, 293, 32], [307, 45, 293, 33, "error"], [307, 50, 293, 38], [308, 6, 293, 38], [309, 8, 293, 38, "fileName"], [309, 16, 293, 38], [309, 18, 293, 38, "_jsxFileName"], [309, 30, 293, 38], [310, 8, 293, 38, "lineNumber"], [310, 18, 293, 38], [311, 8, 293, 38, "columnNumber"], [311, 20, 293, 38], [312, 6, 293, 38], [312, 13, 294, 13], [312, 14, 295, 7], [313, 4, 295, 7], [313, 19, 296, 6], [313, 20, 296, 7], [314, 2, 298, 0], [315, 2, 298, 1, "_s"], [315, 4, 298, 1], [315, 5, 10, 24, "TensorFlowFaceCanvas"], [315, 25, 10, 44], [316, 2, 10, 44, "_c"], [316, 4, 10, 44], [316, 7, 10, 24, "TensorFlowFaceCanvas"], [316, 27, 10, 44], [317, 2, 10, 44], [317, 6, 10, 44, "_c"], [317, 8, 10, 44], [318, 2, 10, 44, "$RefreshReg$"], [318, 14, 10, 44], [318, 15, 10, 44, "_c"], [318, 17, 10, 44], [319, 0, 10, 44], [319, 3]], "functionMap": {"names": ["<global>", "TensorFlowFaceCanvas", "useEffect$argument_0", "loadTensorFlowAndBlazeFace", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "applyFallbackBlur", "faceAreas.forEach$argument_0", "loop", "predictions.forEach$argument_0", "loadTensorFlowAndBlazeFace.then$argument_0", "<anonymous>"], "mappings": "AAA;eCS;YCQ;uCCqC;KD8B;uBEE;yBCC;wBCI,eD;yBEC,wDF;ODE;KFC;8BME;wBCe;ODa;KNC;iBQE;4BCmE;SD+B;KRQ;sCUG;KVI;WWE;KXK;GDC;CDgD"}}, "type": "js/module"}]}