{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 941,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1042,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1028,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1646, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "analyzeRegionForFace"], [206, 30, 215, 28], [206, 33, 215, 31, "analyzeRegionForFace"], [206, 34, 215, 32, "data"], [206, 38, 215, 55], [206, 40, 215, 57, "startX"], [206, 46, 215, 71], [206, 48, 215, 73, "startY"], [206, 54, 215, 87], [206, 56, 215, 89, "size"], [206, 60, 215, 101], [206, 62, 215, 103, "imageWidth"], [206, 72, 215, 121], [206, 74, 215, 123, "imageHeight"], [206, 85, 215, 142], [206, 90, 215, 147], [207, 6, 216, 4], [207, 10, 216, 8, "skinPixels"], [207, 20, 216, 18], [207, 23, 216, 21], [207, 24, 216, 22], [208, 6, 217, 4], [208, 10, 217, 8, "totalPixels"], [208, 21, 217, 19], [208, 24, 217, 22], [208, 25, 217, 23], [209, 6, 218, 4], [209, 10, 218, 8, "totalBrightness"], [209, 25, 218, 23], [209, 28, 218, 26], [209, 29, 218, 27], [210, 6, 219, 4], [210, 10, 219, 8, "colorVariations"], [210, 25, 219, 23], [210, 28, 219, 26], [210, 29, 219, 27], [211, 6, 220, 4], [211, 10, 220, 8, "prevR"], [211, 15, 220, 13], [211, 18, 220, 16], [211, 19, 220, 17], [212, 8, 220, 19, "prevG"], [212, 13, 220, 24], [212, 16, 220, 27], [212, 17, 220, 28], [213, 8, 220, 30, "prevB"], [213, 13, 220, 35], [213, 16, 220, 38], [213, 17, 220, 39], [214, 6, 222, 4], [214, 11, 222, 9], [214, 15, 222, 13, "y"], [214, 16, 222, 14], [214, 19, 222, 17, "startY"], [214, 25, 222, 23], [214, 27, 222, 25, "y"], [214, 28, 222, 26], [214, 31, 222, 29, "startY"], [214, 37, 222, 35], [214, 40, 222, 38, "size"], [214, 44, 222, 42], [214, 48, 222, 46, "y"], [214, 49, 222, 47], [214, 52, 222, 50, "imageHeight"], [214, 63, 222, 61], [214, 65, 222, 63, "y"], [214, 66, 222, 64], [214, 68, 222, 66], [214, 70, 222, 68], [215, 8, 223, 6], [215, 13, 223, 11], [215, 17, 223, 15, "x"], [215, 18, 223, 16], [215, 21, 223, 19, "startX"], [215, 27, 223, 25], [215, 29, 223, 27, "x"], [215, 30, 223, 28], [215, 33, 223, 31, "startX"], [215, 39, 223, 37], [215, 42, 223, 40, "size"], [215, 46, 223, 44], [215, 50, 223, 48, "x"], [215, 51, 223, 49], [215, 54, 223, 52, "imageWidth"], [215, 64, 223, 62], [215, 66, 223, 64, "x"], [215, 67, 223, 65], [215, 69, 223, 67], [215, 71, 223, 69], [216, 10, 224, 8], [216, 16, 224, 14, "index"], [216, 21, 224, 19], [216, 24, 224, 22], [216, 25, 224, 23, "y"], [216, 26, 224, 24], [216, 29, 224, 27, "imageWidth"], [216, 39, 224, 37], [216, 42, 224, 40, "x"], [216, 43, 224, 41], [216, 47, 224, 45], [216, 48, 224, 46], [217, 10, 225, 8], [217, 16, 225, 14, "r"], [217, 17, 225, 15], [217, 20, 225, 18, "data"], [217, 24, 225, 22], [217, 25, 225, 23, "index"], [217, 30, 225, 28], [217, 31, 225, 29], [218, 10, 226, 8], [218, 16, 226, 14, "g"], [218, 17, 226, 15], [218, 20, 226, 18, "data"], [218, 24, 226, 22], [218, 25, 226, 23, "index"], [218, 30, 226, 28], [218, 33, 226, 31], [218, 34, 226, 32], [218, 35, 226, 33], [219, 10, 227, 8], [219, 16, 227, 14, "b"], [219, 17, 227, 15], [219, 20, 227, 18, "data"], [219, 24, 227, 22], [219, 25, 227, 23, "index"], [219, 30, 227, 28], [219, 33, 227, 31], [219, 34, 227, 32], [219, 35, 227, 33], [221, 10, 229, 8], [222, 10, 230, 8], [222, 14, 230, 12, "isSkinTone"], [222, 24, 230, 22], [222, 25, 230, 23, "r"], [222, 26, 230, 24], [222, 28, 230, 26, "g"], [222, 29, 230, 27], [222, 31, 230, 29, "b"], [222, 32, 230, 30], [222, 33, 230, 31], [222, 35, 230, 33], [223, 12, 231, 10, "skinPixels"], [223, 22, 231, 20], [223, 24, 231, 22], [224, 10, 232, 8], [226, 10, 234, 8], [227, 10, 235, 8], [227, 16, 235, 14, "brightness"], [227, 26, 235, 24], [227, 29, 235, 27], [227, 30, 235, 28, "r"], [227, 31, 235, 29], [227, 34, 235, 32, "g"], [227, 35, 235, 33], [227, 38, 235, 36, "b"], [227, 39, 235, 37], [227, 44, 235, 42], [227, 45, 235, 43], [227, 48, 235, 46], [227, 51, 235, 49], [227, 52, 235, 50], [228, 10, 236, 8, "totalBrightness"], [228, 25, 236, 23], [228, 29, 236, 27, "brightness"], [228, 39, 236, 37], [230, 10, 238, 8], [231, 10, 239, 8], [231, 14, 239, 12, "totalPixels"], [231, 25, 239, 23], [231, 28, 239, 26], [231, 29, 239, 27], [231, 31, 239, 29], [232, 12, 240, 10], [232, 18, 240, 16, "colorDiff"], [232, 27, 240, 25], [232, 30, 240, 28, "Math"], [232, 34, 240, 32], [232, 35, 240, 33, "abs"], [232, 38, 240, 36], [232, 39, 240, 37, "r"], [232, 40, 240, 38], [232, 43, 240, 41, "prevR"], [232, 48, 240, 46], [232, 49, 240, 47], [232, 52, 240, 50, "Math"], [232, 56, 240, 54], [232, 57, 240, 55, "abs"], [232, 60, 240, 58], [232, 61, 240, 59, "g"], [232, 62, 240, 60], [232, 65, 240, 63, "prevG"], [232, 70, 240, 68], [232, 71, 240, 69], [232, 74, 240, 72, "Math"], [232, 78, 240, 76], [232, 79, 240, 77, "abs"], [232, 82, 240, 80], [232, 83, 240, 81, "b"], [232, 84, 240, 82], [232, 87, 240, 85, "prevB"], [232, 92, 240, 90], [232, 93, 240, 91], [233, 12, 241, 10], [233, 16, 241, 14, "colorDiff"], [233, 25, 241, 23], [233, 28, 241, 26], [233, 30, 241, 28], [233, 32, 241, 30], [234, 14, 241, 32], [235, 14, 242, 12, "colorVariations"], [235, 29, 242, 27], [235, 31, 242, 29], [236, 12, 243, 10], [237, 10, 244, 8], [238, 10, 246, 8, "prevR"], [238, 15, 246, 13], [238, 18, 246, 16, "r"], [238, 19, 246, 17], [239, 10, 246, 19, "prevG"], [239, 15, 246, 24], [239, 18, 246, 27, "g"], [239, 19, 246, 28], [240, 10, 246, 30, "prevB"], [240, 15, 246, 35], [240, 18, 246, 38, "b"], [240, 19, 246, 39], [241, 10, 247, 8, "totalPixels"], [241, 21, 247, 19], [241, 23, 247, 21], [242, 8, 248, 6], [243, 6, 249, 4], [244, 6, 251, 4], [244, 13, 251, 11], [245, 8, 252, 6, "skinRatio"], [245, 17, 252, 15], [245, 19, 252, 17, "skinPixels"], [245, 29, 252, 27], [245, 32, 252, 30, "totalPixels"], [245, 43, 252, 41], [246, 8, 253, 6, "brightness"], [246, 18, 253, 16], [246, 20, 253, 18, "totalBrightness"], [246, 35, 253, 33], [246, 38, 253, 36, "totalPixels"], [246, 49, 253, 47], [247, 8, 254, 6, "variation"], [247, 17, 254, 15], [247, 19, 254, 17, "colorVariations"], [247, 34, 254, 32], [247, 37, 254, 35, "totalPixels"], [247, 48, 254, 46], [248, 8, 255, 6, "hasVariation"], [248, 20, 255, 18], [248, 22, 255, 20, "colorVariations"], [248, 37, 255, 35], [248, 40, 255, 38, "totalPixels"], [248, 51, 255, 49], [248, 54, 255, 52], [248, 57, 255, 55], [248, 58, 255, 56], [249, 6, 256, 4], [249, 7, 256, 5], [250, 4, 257, 2], [250, 5, 257, 3], [251, 4, 259, 2], [251, 10, 259, 8, "isSkinTone"], [251, 20, 259, 18], [251, 23, 259, 21, "isSkinTone"], [251, 24, 259, 22, "r"], [251, 25, 259, 31], [251, 27, 259, 33, "g"], [251, 28, 259, 42], [251, 30, 259, 44, "b"], [251, 31, 259, 53], [251, 36, 259, 58], [252, 6, 260, 4], [253, 6, 261, 4], [253, 13, 262, 6, "r"], [253, 14, 262, 7], [253, 17, 262, 10], [253, 19, 262, 12], [253, 23, 262, 16, "g"], [253, 24, 262, 17], [253, 27, 262, 20], [253, 29, 262, 22], [253, 33, 262, 26, "b"], [253, 34, 262, 27], [253, 37, 262, 30], [253, 39, 262, 32], [253, 43, 263, 6, "r"], [253, 44, 263, 7], [253, 47, 263, 10, "g"], [253, 48, 263, 11], [253, 52, 263, 15, "r"], [253, 53, 263, 16], [253, 56, 263, 19, "b"], [253, 57, 263, 20], [253, 61, 264, 6, "Math"], [253, 65, 264, 10], [253, 66, 264, 11, "abs"], [253, 69, 264, 14], [253, 70, 264, 15, "r"], [253, 71, 264, 16], [253, 74, 264, 19, "g"], [253, 75, 264, 20], [253, 76, 264, 21], [253, 79, 264, 24], [253, 81, 264, 26], [253, 85, 265, 6, "Math"], [253, 89, 265, 10], [253, 90, 265, 11, "max"], [253, 93, 265, 14], [253, 94, 265, 15, "r"], [253, 95, 265, 16], [253, 97, 265, 18, "g"], [253, 98, 265, 19], [253, 100, 265, 21, "b"], [253, 101, 265, 22], [253, 102, 265, 23], [253, 105, 265, 26, "Math"], [253, 109, 265, 30], [253, 110, 265, 31, "min"], [253, 113, 265, 34], [253, 114, 265, 35, "r"], [253, 115, 265, 36], [253, 117, 265, 38, "g"], [253, 118, 265, 39], [253, 120, 265, 41, "b"], [253, 121, 265, 42], [253, 122, 265, 43], [253, 125, 265, 46], [253, 127, 265, 48], [254, 4, 267, 2], [254, 5, 267, 3], [255, 4, 269, 2], [255, 10, 269, 8, "mergeFaceDetections"], [255, 29, 269, 27], [255, 32, 269, 31, "faces"], [255, 37, 269, 43], [255, 41, 269, 48], [256, 6, 270, 4], [256, 10, 270, 8, "faces"], [256, 15, 270, 13], [256, 16, 270, 14, "length"], [256, 22, 270, 20], [256, 26, 270, 24], [256, 27, 270, 25], [256, 29, 270, 27], [256, 36, 270, 34, "faces"], [256, 41, 270, 39], [257, 6, 272, 4], [257, 12, 272, 10, "merged"], [257, 18, 272, 16], [257, 21, 272, 19], [257, 23, 272, 21], [258, 6, 273, 4], [258, 12, 273, 10, "used"], [258, 16, 273, 14], [258, 19, 273, 17], [258, 23, 273, 21, "Set"], [258, 26, 273, 24], [258, 27, 273, 25], [258, 28, 273, 26], [259, 6, 275, 4], [259, 11, 275, 9], [259, 15, 275, 13, "i"], [259, 16, 275, 14], [259, 19, 275, 17], [259, 20, 275, 18], [259, 22, 275, 20, "i"], [259, 23, 275, 21], [259, 26, 275, 24, "faces"], [259, 31, 275, 29], [259, 32, 275, 30, "length"], [259, 38, 275, 36], [259, 40, 275, 38, "i"], [259, 41, 275, 39], [259, 43, 275, 41], [259, 45, 275, 43], [260, 8, 276, 6], [260, 12, 276, 10, "used"], [260, 16, 276, 14], [260, 17, 276, 15, "has"], [260, 20, 276, 18], [260, 21, 276, 19, "i"], [260, 22, 276, 20], [260, 23, 276, 21], [260, 25, 276, 23], [261, 8, 278, 6], [261, 12, 278, 10, "currentFace"], [261, 23, 278, 21], [261, 26, 278, 24, "faces"], [261, 31, 278, 29], [261, 32, 278, 30, "i"], [261, 33, 278, 31], [261, 34, 278, 32], [262, 8, 279, 6, "used"], [262, 12, 279, 10], [262, 13, 279, 11, "add"], [262, 16, 279, 14], [262, 17, 279, 15, "i"], [262, 18, 279, 16], [262, 19, 279, 17], [264, 8, 281, 6], [265, 8, 282, 6], [265, 13, 282, 11], [265, 17, 282, 15, "j"], [265, 18, 282, 16], [265, 21, 282, 19, "i"], [265, 22, 282, 20], [265, 25, 282, 23], [265, 26, 282, 24], [265, 28, 282, 26, "j"], [265, 29, 282, 27], [265, 32, 282, 30, "faces"], [265, 37, 282, 35], [265, 38, 282, 36, "length"], [265, 44, 282, 42], [265, 46, 282, 44, "j"], [265, 47, 282, 45], [265, 49, 282, 47], [265, 51, 282, 49], [266, 10, 283, 8], [266, 14, 283, 12, "used"], [266, 18, 283, 16], [266, 19, 283, 17, "has"], [266, 22, 283, 20], [266, 23, 283, 21, "j"], [266, 24, 283, 22], [266, 25, 283, 23], [266, 27, 283, 25], [267, 10, 285, 8], [267, 16, 285, 14, "overlap"], [267, 23, 285, 21], [267, 26, 285, 24, "calculateOverlap"], [267, 42, 285, 40], [267, 43, 285, 41, "currentFace"], [267, 54, 285, 52], [267, 55, 285, 53, "boundingBox"], [267, 66, 285, 64], [267, 68, 285, 66, "faces"], [267, 73, 285, 71], [267, 74, 285, 72, "j"], [267, 75, 285, 73], [267, 76, 285, 74], [267, 77, 285, 75, "boundingBox"], [267, 88, 285, 86], [267, 89, 285, 87], [268, 10, 286, 8], [268, 14, 286, 12, "overlap"], [268, 21, 286, 19], [268, 24, 286, 22], [268, 27, 286, 25], [268, 29, 286, 27], [269, 12, 286, 29], [270, 12, 287, 10], [271, 12, 288, 10, "currentFace"], [271, 23, 288, 21], [271, 26, 288, 24, "mergeTwoFaces"], [271, 39, 288, 37], [271, 40, 288, 38, "currentFace"], [271, 51, 288, 49], [271, 53, 288, 51, "faces"], [271, 58, 288, 56], [271, 59, 288, 57, "j"], [271, 60, 288, 58], [271, 61, 288, 59], [271, 62, 288, 60], [272, 12, 289, 10, "used"], [272, 16, 289, 14], [272, 17, 289, 15, "add"], [272, 20, 289, 18], [272, 21, 289, 19, "j"], [272, 22, 289, 20], [272, 23, 289, 21], [273, 10, 290, 8], [274, 8, 291, 6], [275, 8, 293, 6, "merged"], [275, 14, 293, 12], [275, 15, 293, 13, "push"], [275, 19, 293, 17], [275, 20, 293, 18, "currentFace"], [275, 31, 293, 29], [275, 32, 293, 30], [276, 6, 294, 4], [277, 6, 296, 4], [277, 13, 296, 11, "merged"], [277, 19, 296, 17], [278, 4, 297, 2], [278, 5, 297, 3], [279, 4, 299, 2], [279, 10, 299, 8, "calculateOverlap"], [279, 26, 299, 24], [279, 29, 299, 27, "calculateOverlap"], [279, 30, 299, 28, "box1"], [279, 34, 299, 37], [279, 36, 299, 39, "box2"], [279, 40, 299, 48], [279, 45, 299, 53], [280, 6, 300, 4], [280, 12, 300, 10, "x1"], [280, 14, 300, 12], [280, 17, 300, 15, "Math"], [280, 21, 300, 19], [280, 22, 300, 20, "max"], [280, 25, 300, 23], [280, 26, 300, 24, "box1"], [280, 30, 300, 28], [280, 31, 300, 29, "xCenter"], [280, 38, 300, 36], [280, 41, 300, 39, "box1"], [280, 45, 300, 43], [280, 46, 300, 44, "width"], [280, 51, 300, 49], [280, 54, 300, 50], [280, 55, 300, 51], [280, 57, 300, 53, "box2"], [280, 61, 300, 57], [280, 62, 300, 58, "xCenter"], [280, 69, 300, 65], [280, 72, 300, 68, "box2"], [280, 76, 300, 72], [280, 77, 300, 73, "width"], [280, 82, 300, 78], [280, 85, 300, 79], [280, 86, 300, 80], [280, 87, 300, 81], [281, 6, 301, 4], [281, 12, 301, 10, "y1"], [281, 14, 301, 12], [281, 17, 301, 15, "Math"], [281, 21, 301, 19], [281, 22, 301, 20, "max"], [281, 25, 301, 23], [281, 26, 301, 24, "box1"], [281, 30, 301, 28], [281, 31, 301, 29, "yCenter"], [281, 38, 301, 36], [281, 41, 301, 39, "box1"], [281, 45, 301, 43], [281, 46, 301, 44, "height"], [281, 52, 301, 50], [281, 55, 301, 51], [281, 56, 301, 52], [281, 58, 301, 54, "box2"], [281, 62, 301, 58], [281, 63, 301, 59, "yCenter"], [281, 70, 301, 66], [281, 73, 301, 69, "box2"], [281, 77, 301, 73], [281, 78, 301, 74, "height"], [281, 84, 301, 80], [281, 87, 301, 81], [281, 88, 301, 82], [281, 89, 301, 83], [282, 6, 302, 4], [282, 12, 302, 10, "x2"], [282, 14, 302, 12], [282, 17, 302, 15, "Math"], [282, 21, 302, 19], [282, 22, 302, 20, "min"], [282, 25, 302, 23], [282, 26, 302, 24, "box1"], [282, 30, 302, 28], [282, 31, 302, 29, "xCenter"], [282, 38, 302, 36], [282, 41, 302, 39, "box1"], [282, 45, 302, 43], [282, 46, 302, 44, "width"], [282, 51, 302, 49], [282, 54, 302, 50], [282, 55, 302, 51], [282, 57, 302, 53, "box2"], [282, 61, 302, 57], [282, 62, 302, 58, "xCenter"], [282, 69, 302, 65], [282, 72, 302, 68, "box2"], [282, 76, 302, 72], [282, 77, 302, 73, "width"], [282, 82, 302, 78], [282, 85, 302, 79], [282, 86, 302, 80], [282, 87, 302, 81], [283, 6, 303, 4], [283, 12, 303, 10, "y2"], [283, 14, 303, 12], [283, 17, 303, 15, "Math"], [283, 21, 303, 19], [283, 22, 303, 20, "min"], [283, 25, 303, 23], [283, 26, 303, 24, "box1"], [283, 30, 303, 28], [283, 31, 303, 29, "yCenter"], [283, 38, 303, 36], [283, 41, 303, 39, "box1"], [283, 45, 303, 43], [283, 46, 303, 44, "height"], [283, 52, 303, 50], [283, 55, 303, 51], [283, 56, 303, 52], [283, 58, 303, 54, "box2"], [283, 62, 303, 58], [283, 63, 303, 59, "yCenter"], [283, 70, 303, 66], [283, 73, 303, 69, "box2"], [283, 77, 303, 73], [283, 78, 303, 74, "height"], [283, 84, 303, 80], [283, 87, 303, 81], [283, 88, 303, 82], [283, 89, 303, 83], [284, 6, 305, 4], [284, 10, 305, 8, "x2"], [284, 12, 305, 10], [284, 16, 305, 14, "x1"], [284, 18, 305, 16], [284, 22, 305, 20, "y2"], [284, 24, 305, 22], [284, 28, 305, 26, "y1"], [284, 30, 305, 28], [284, 32, 305, 30], [284, 39, 305, 37], [284, 40, 305, 38], [285, 6, 307, 4], [285, 12, 307, 10, "overlapArea"], [285, 23, 307, 21], [285, 26, 307, 24], [285, 27, 307, 25, "x2"], [285, 29, 307, 27], [285, 32, 307, 30, "x1"], [285, 34, 307, 32], [285, 39, 307, 37, "y2"], [285, 41, 307, 39], [285, 44, 307, 42, "y1"], [285, 46, 307, 44], [285, 47, 307, 45], [286, 6, 308, 4], [286, 12, 308, 10, "box1Area"], [286, 20, 308, 18], [286, 23, 308, 21, "box1"], [286, 27, 308, 25], [286, 28, 308, 26, "width"], [286, 33, 308, 31], [286, 36, 308, 34, "box1"], [286, 40, 308, 38], [286, 41, 308, 39, "height"], [286, 47, 308, 45], [287, 6, 309, 4], [287, 12, 309, 10, "box2Area"], [287, 20, 309, 18], [287, 23, 309, 21, "box2"], [287, 27, 309, 25], [287, 28, 309, 26, "width"], [287, 33, 309, 31], [287, 36, 309, 34, "box2"], [287, 40, 309, 38], [287, 41, 309, 39, "height"], [287, 47, 309, 45], [288, 6, 311, 4], [288, 13, 311, 11, "overlapArea"], [288, 24, 311, 22], [288, 27, 311, 25, "Math"], [288, 31, 311, 29], [288, 32, 311, 30, "min"], [288, 35, 311, 33], [288, 36, 311, 34, "box1Area"], [288, 44, 311, 42], [288, 46, 311, 44, "box2Area"], [288, 54, 311, 52], [288, 55, 311, 53], [289, 4, 312, 2], [289, 5, 312, 3], [290, 4, 314, 2], [290, 10, 314, 8, "mergeTwoFaces"], [290, 23, 314, 21], [290, 26, 314, 24, "mergeTwoFaces"], [290, 27, 314, 25, "face1"], [290, 32, 314, 35], [290, 34, 314, 37, "face2"], [290, 39, 314, 47], [290, 44, 314, 52], [291, 6, 315, 4], [291, 12, 315, 10, "box1"], [291, 16, 315, 14], [291, 19, 315, 17, "face1"], [291, 24, 315, 22], [291, 25, 315, 23, "boundingBox"], [291, 36, 315, 34], [292, 6, 316, 4], [292, 12, 316, 10, "box2"], [292, 16, 316, 14], [292, 19, 316, 17, "face2"], [292, 24, 316, 22], [292, 25, 316, 23, "boundingBox"], [292, 36, 316, 34], [293, 6, 318, 4], [293, 12, 318, 10, "left"], [293, 16, 318, 14], [293, 19, 318, 17, "Math"], [293, 23, 318, 21], [293, 24, 318, 22, "min"], [293, 27, 318, 25], [293, 28, 318, 26, "box1"], [293, 32, 318, 30], [293, 33, 318, 31, "xCenter"], [293, 40, 318, 38], [293, 43, 318, 41, "box1"], [293, 47, 318, 45], [293, 48, 318, 46, "width"], [293, 53, 318, 51], [293, 56, 318, 52], [293, 57, 318, 53], [293, 59, 318, 55, "box2"], [293, 63, 318, 59], [293, 64, 318, 60, "xCenter"], [293, 71, 318, 67], [293, 74, 318, 70, "box2"], [293, 78, 318, 74], [293, 79, 318, 75, "width"], [293, 84, 318, 80], [293, 87, 318, 81], [293, 88, 318, 82], [293, 89, 318, 83], [294, 6, 319, 4], [294, 12, 319, 10, "right"], [294, 17, 319, 15], [294, 20, 319, 18, "Math"], [294, 24, 319, 22], [294, 25, 319, 23, "max"], [294, 28, 319, 26], [294, 29, 319, 27, "box1"], [294, 33, 319, 31], [294, 34, 319, 32, "xCenter"], [294, 41, 319, 39], [294, 44, 319, 42, "box1"], [294, 48, 319, 46], [294, 49, 319, 47, "width"], [294, 54, 319, 52], [294, 57, 319, 53], [294, 58, 319, 54], [294, 60, 319, 56, "box2"], [294, 64, 319, 60], [294, 65, 319, 61, "xCenter"], [294, 72, 319, 68], [294, 75, 319, 71, "box2"], [294, 79, 319, 75], [294, 80, 319, 76, "width"], [294, 85, 319, 81], [294, 88, 319, 82], [294, 89, 319, 83], [294, 90, 319, 84], [295, 6, 320, 4], [295, 12, 320, 10, "top"], [295, 15, 320, 13], [295, 18, 320, 16, "Math"], [295, 22, 320, 20], [295, 23, 320, 21, "min"], [295, 26, 320, 24], [295, 27, 320, 25, "box1"], [295, 31, 320, 29], [295, 32, 320, 30, "yCenter"], [295, 39, 320, 37], [295, 42, 320, 40, "box1"], [295, 46, 320, 44], [295, 47, 320, 45, "height"], [295, 53, 320, 51], [295, 56, 320, 52], [295, 57, 320, 53], [295, 59, 320, 55, "box2"], [295, 63, 320, 59], [295, 64, 320, 60, "yCenter"], [295, 71, 320, 67], [295, 74, 320, 70, "box2"], [295, 78, 320, 74], [295, 79, 320, 75, "height"], [295, 85, 320, 81], [295, 88, 320, 82], [295, 89, 320, 83], [295, 90, 320, 84], [296, 6, 321, 4], [296, 12, 321, 10, "bottom"], [296, 18, 321, 16], [296, 21, 321, 19, "Math"], [296, 25, 321, 23], [296, 26, 321, 24, "max"], [296, 29, 321, 27], [296, 30, 321, 28, "box1"], [296, 34, 321, 32], [296, 35, 321, 33, "yCenter"], [296, 42, 321, 40], [296, 45, 321, 43, "box1"], [296, 49, 321, 47], [296, 50, 321, 48, "height"], [296, 56, 321, 54], [296, 59, 321, 55], [296, 60, 321, 56], [296, 62, 321, 58, "box2"], [296, 66, 321, 62], [296, 67, 321, 63, "yCenter"], [296, 74, 321, 70], [296, 77, 321, 73, "box2"], [296, 81, 321, 77], [296, 82, 321, 78, "height"], [296, 88, 321, 84], [296, 91, 321, 85], [296, 92, 321, 86], [296, 93, 321, 87], [297, 6, 323, 4], [297, 13, 323, 11], [298, 8, 324, 6, "boundingBox"], [298, 19, 324, 17], [298, 21, 324, 19], [299, 10, 325, 8, "xCenter"], [299, 17, 325, 15], [299, 19, 325, 17], [299, 20, 325, 18, "left"], [299, 24, 325, 22], [299, 27, 325, 25, "right"], [299, 32, 325, 30], [299, 36, 325, 34], [299, 37, 325, 35], [300, 10, 326, 8, "yCenter"], [300, 17, 326, 15], [300, 19, 326, 17], [300, 20, 326, 18, "top"], [300, 23, 326, 21], [300, 26, 326, 24, "bottom"], [300, 32, 326, 30], [300, 36, 326, 34], [300, 37, 326, 35], [301, 10, 327, 8, "width"], [301, 15, 327, 13], [301, 17, 327, 15, "right"], [301, 22, 327, 20], [301, 25, 327, 23, "left"], [301, 29, 327, 27], [302, 10, 328, 8, "height"], [302, 16, 328, 14], [302, 18, 328, 16, "bottom"], [302, 24, 328, 22], [302, 27, 328, 25, "top"], [303, 8, 329, 6], [304, 6, 330, 4], [304, 7, 330, 5], [305, 4, 331, 2], [305, 5, 331, 3], [307, 4, 333, 2], [308, 4, 334, 2], [308, 10, 334, 8, "applyStrongBlur"], [308, 25, 334, 23], [308, 28, 334, 26, "applyStrongBlur"], [308, 29, 334, 27, "ctx"], [308, 32, 334, 56], [308, 34, 334, 58, "x"], [308, 35, 334, 67], [308, 37, 334, 69, "y"], [308, 38, 334, 78], [308, 40, 334, 80, "width"], [308, 45, 334, 93], [308, 47, 334, 95, "height"], [308, 53, 334, 109], [308, 58, 334, 114], [309, 6, 335, 4], [310, 6, 336, 4], [310, 12, 336, 10, "canvasWidth"], [310, 23, 336, 21], [310, 26, 336, 24, "ctx"], [310, 29, 336, 27], [310, 30, 336, 28, "canvas"], [310, 36, 336, 34], [310, 37, 336, 35, "width"], [310, 42, 336, 40], [311, 6, 337, 4], [311, 12, 337, 10, "canvasHeight"], [311, 24, 337, 22], [311, 27, 337, 25, "ctx"], [311, 30, 337, 28], [311, 31, 337, 29, "canvas"], [311, 37, 337, 35], [311, 38, 337, 36, "height"], [311, 44, 337, 42], [312, 6, 339, 4], [312, 12, 339, 10, "clampedX"], [312, 20, 339, 18], [312, 23, 339, 21, "Math"], [312, 27, 339, 25], [312, 28, 339, 26, "max"], [312, 31, 339, 29], [312, 32, 339, 30], [312, 33, 339, 31], [312, 35, 339, 33, "Math"], [312, 39, 339, 37], [312, 40, 339, 38, "min"], [312, 43, 339, 41], [312, 44, 339, 42, "Math"], [312, 48, 339, 46], [312, 49, 339, 47, "floor"], [312, 54, 339, 52], [312, 55, 339, 53, "x"], [312, 56, 339, 54], [312, 57, 339, 55], [312, 59, 339, 57, "canvasWidth"], [312, 70, 339, 68], [312, 73, 339, 71], [312, 74, 339, 72], [312, 75, 339, 73], [312, 76, 339, 74], [313, 6, 340, 4], [313, 12, 340, 10, "clampedY"], [313, 20, 340, 18], [313, 23, 340, 21, "Math"], [313, 27, 340, 25], [313, 28, 340, 26, "max"], [313, 31, 340, 29], [313, 32, 340, 30], [313, 33, 340, 31], [313, 35, 340, 33, "Math"], [313, 39, 340, 37], [313, 40, 340, 38, "min"], [313, 43, 340, 41], [313, 44, 340, 42, "Math"], [313, 48, 340, 46], [313, 49, 340, 47, "floor"], [313, 54, 340, 52], [313, 55, 340, 53, "y"], [313, 56, 340, 54], [313, 57, 340, 55], [313, 59, 340, 57, "canvasHeight"], [313, 71, 340, 69], [313, 74, 340, 72], [313, 75, 340, 73], [313, 76, 340, 74], [313, 77, 340, 75], [314, 6, 341, 4], [314, 12, 341, 10, "<PERSON><PERSON><PERSON><PERSON>"], [314, 24, 341, 22], [314, 27, 341, 25, "Math"], [314, 31, 341, 29], [314, 32, 341, 30, "min"], [314, 35, 341, 33], [314, 36, 341, 34, "Math"], [314, 40, 341, 38], [314, 41, 341, 39, "floor"], [314, 46, 341, 44], [314, 47, 341, 45, "width"], [314, 52, 341, 50], [314, 53, 341, 51], [314, 55, 341, 53, "canvasWidth"], [314, 66, 341, 64], [314, 69, 341, 67, "clampedX"], [314, 77, 341, 75], [314, 78, 341, 76], [315, 6, 342, 4], [315, 12, 342, 10, "clampedHeight"], [315, 25, 342, 23], [315, 28, 342, 26, "Math"], [315, 32, 342, 30], [315, 33, 342, 31, "min"], [315, 36, 342, 34], [315, 37, 342, 35, "Math"], [315, 41, 342, 39], [315, 42, 342, 40, "floor"], [315, 47, 342, 45], [315, 48, 342, 46, "height"], [315, 54, 342, 52], [315, 55, 342, 53], [315, 57, 342, 55, "canvasHeight"], [315, 69, 342, 67], [315, 72, 342, 70, "clampedY"], [315, 80, 342, 78], [315, 81, 342, 79], [316, 6, 344, 4], [316, 10, 344, 8, "<PERSON><PERSON><PERSON><PERSON>"], [316, 22, 344, 20], [316, 26, 344, 24], [316, 27, 344, 25], [316, 31, 344, 29, "clampedHeight"], [316, 44, 344, 42], [316, 48, 344, 46], [316, 49, 344, 47], [316, 51, 344, 49], [317, 8, 345, 6, "console"], [317, 15, 345, 13], [317, 16, 345, 14, "warn"], [317, 20, 345, 18], [317, 21, 345, 19], [317, 72, 345, 70], [317, 73, 345, 71], [318, 8, 346, 6], [319, 6, 347, 4], [321, 6, 349, 4], [322, 6, 350, 4], [322, 12, 350, 10, "imageData"], [322, 21, 350, 19], [322, 24, 350, 22, "ctx"], [322, 27, 350, 25], [322, 28, 350, 26, "getImageData"], [322, 40, 350, 38], [322, 41, 350, 39, "clampedX"], [322, 49, 350, 47], [322, 51, 350, 49, "clampedY"], [322, 59, 350, 57], [322, 61, 350, 59, "<PERSON><PERSON><PERSON><PERSON>"], [322, 73, 350, 71], [322, 75, 350, 73, "clampedHeight"], [322, 88, 350, 86], [322, 89, 350, 87], [323, 6, 351, 4], [323, 12, 351, 10, "data"], [323, 16, 351, 14], [323, 19, 351, 17, "imageData"], [323, 28, 351, 26], [323, 29, 351, 27, "data"], [323, 33, 351, 31], [325, 6, 353, 4], [326, 6, 354, 4], [326, 12, 354, 10, "pixelSize"], [326, 21, 354, 19], [326, 24, 354, 22, "Math"], [326, 28, 354, 26], [326, 29, 354, 27, "max"], [326, 32, 354, 30], [326, 33, 354, 31], [326, 35, 354, 33], [326, 37, 354, 35, "Math"], [326, 41, 354, 39], [326, 42, 354, 40, "min"], [326, 45, 354, 43], [326, 46, 354, 44, "<PERSON><PERSON><PERSON><PERSON>"], [326, 58, 354, 56], [326, 60, 354, 58, "clampedHeight"], [326, 73, 354, 71], [326, 74, 354, 72], [326, 77, 354, 75], [326, 78, 354, 76], [326, 79, 354, 77], [327, 6, 356, 4], [327, 11, 356, 9], [327, 15, 356, 13, "py"], [327, 17, 356, 15], [327, 20, 356, 18], [327, 21, 356, 19], [327, 23, 356, 21, "py"], [327, 25, 356, 23], [327, 28, 356, 26, "clampedHeight"], [327, 41, 356, 39], [327, 43, 356, 41, "py"], [327, 45, 356, 43], [327, 49, 356, 47, "pixelSize"], [327, 58, 356, 56], [327, 60, 356, 58], [328, 8, 357, 6], [328, 13, 357, 11], [328, 17, 357, 15, "px"], [328, 19, 357, 17], [328, 22, 357, 20], [328, 23, 357, 21], [328, 25, 357, 23, "px"], [328, 27, 357, 25], [328, 30, 357, 28, "<PERSON><PERSON><PERSON><PERSON>"], [328, 42, 357, 40], [328, 44, 357, 42, "px"], [328, 46, 357, 44], [328, 50, 357, 48, "pixelSize"], [328, 59, 357, 57], [328, 61, 357, 59], [329, 10, 358, 8], [330, 10, 359, 8], [330, 14, 359, 12, "r"], [330, 15, 359, 13], [330, 18, 359, 16], [330, 19, 359, 17], [331, 12, 359, 19, "g"], [331, 13, 359, 20], [331, 16, 359, 23], [331, 17, 359, 24], [332, 12, 359, 26, "b"], [332, 13, 359, 27], [332, 16, 359, 30], [332, 17, 359, 31], [333, 12, 359, 33, "count"], [333, 17, 359, 38], [333, 20, 359, 41], [333, 21, 359, 42], [334, 10, 361, 8], [334, 15, 361, 13], [334, 19, 361, 17, "dy"], [334, 21, 361, 19], [334, 24, 361, 22], [334, 25, 361, 23], [334, 27, 361, 25, "dy"], [334, 29, 361, 27], [334, 32, 361, 30, "pixelSize"], [334, 41, 361, 39], [334, 45, 361, 43, "py"], [334, 47, 361, 45], [334, 50, 361, 48, "dy"], [334, 52, 361, 50], [334, 55, 361, 53, "clampedHeight"], [334, 68, 361, 66], [334, 70, 361, 68, "dy"], [334, 72, 361, 70], [334, 74, 361, 72], [334, 76, 361, 74], [335, 12, 362, 10], [335, 17, 362, 15], [335, 21, 362, 19, "dx"], [335, 23, 362, 21], [335, 26, 362, 24], [335, 27, 362, 25], [335, 29, 362, 27, "dx"], [335, 31, 362, 29], [335, 34, 362, 32, "pixelSize"], [335, 43, 362, 41], [335, 47, 362, 45, "px"], [335, 49, 362, 47], [335, 52, 362, 50, "dx"], [335, 54, 362, 52], [335, 57, 362, 55, "<PERSON><PERSON><PERSON><PERSON>"], [335, 69, 362, 67], [335, 71, 362, 69, "dx"], [335, 73, 362, 71], [335, 75, 362, 73], [335, 77, 362, 75], [336, 14, 363, 12], [336, 20, 363, 18, "index"], [336, 25, 363, 23], [336, 28, 363, 26], [336, 29, 363, 27], [336, 30, 363, 28, "py"], [336, 32, 363, 30], [336, 35, 363, 33, "dy"], [336, 37, 363, 35], [336, 41, 363, 39, "<PERSON><PERSON><PERSON><PERSON>"], [336, 53, 363, 51], [336, 57, 363, 55, "px"], [336, 59, 363, 57], [336, 62, 363, 60, "dx"], [336, 64, 363, 62], [336, 65, 363, 63], [336, 69, 363, 67], [336, 70, 363, 68], [337, 14, 364, 12, "r"], [337, 15, 364, 13], [337, 19, 364, 17, "data"], [337, 23, 364, 21], [337, 24, 364, 22, "index"], [337, 29, 364, 27], [337, 30, 364, 28], [338, 14, 365, 12, "g"], [338, 15, 365, 13], [338, 19, 365, 17, "data"], [338, 23, 365, 21], [338, 24, 365, 22, "index"], [338, 29, 365, 27], [338, 32, 365, 30], [338, 33, 365, 31], [338, 34, 365, 32], [339, 14, 366, 12, "b"], [339, 15, 366, 13], [339, 19, 366, 17, "data"], [339, 23, 366, 21], [339, 24, 366, 22, "index"], [339, 29, 366, 27], [339, 32, 366, 30], [339, 33, 366, 31], [339, 34, 366, 32], [340, 14, 367, 12, "count"], [340, 19, 367, 17], [340, 21, 367, 19], [341, 12, 368, 10], [342, 10, 369, 8], [343, 10, 371, 8], [343, 14, 371, 12, "count"], [343, 19, 371, 17], [343, 22, 371, 20], [343, 23, 371, 21], [343, 25, 371, 23], [344, 12, 372, 10, "r"], [344, 13, 372, 11], [344, 16, 372, 14, "Math"], [344, 20, 372, 18], [344, 21, 372, 19, "floor"], [344, 26, 372, 24], [344, 27, 372, 25, "r"], [344, 28, 372, 26], [344, 31, 372, 29, "count"], [344, 36, 372, 34], [344, 37, 372, 35], [345, 12, 373, 10, "g"], [345, 13, 373, 11], [345, 16, 373, 14, "Math"], [345, 20, 373, 18], [345, 21, 373, 19, "floor"], [345, 26, 373, 24], [345, 27, 373, 25, "g"], [345, 28, 373, 26], [345, 31, 373, 29, "count"], [345, 36, 373, 34], [345, 37, 373, 35], [346, 12, 374, 10, "b"], [346, 13, 374, 11], [346, 16, 374, 14, "Math"], [346, 20, 374, 18], [346, 21, 374, 19, "floor"], [346, 26, 374, 24], [346, 27, 374, 25, "b"], [346, 28, 374, 26], [346, 31, 374, 29, "count"], [346, 36, 374, 34], [346, 37, 374, 35], [348, 12, 376, 10], [349, 12, 377, 10], [349, 17, 377, 15], [349, 21, 377, 19, "dy"], [349, 23, 377, 21], [349, 26, 377, 24], [349, 27, 377, 25], [349, 29, 377, 27, "dy"], [349, 31, 377, 29], [349, 34, 377, 32, "pixelSize"], [349, 43, 377, 41], [349, 47, 377, 45, "py"], [349, 49, 377, 47], [349, 52, 377, 50, "dy"], [349, 54, 377, 52], [349, 57, 377, 55, "clampedHeight"], [349, 70, 377, 68], [349, 72, 377, 70, "dy"], [349, 74, 377, 72], [349, 76, 377, 74], [349, 78, 377, 76], [350, 14, 378, 12], [350, 19, 378, 17], [350, 23, 378, 21, "dx"], [350, 25, 378, 23], [350, 28, 378, 26], [350, 29, 378, 27], [350, 31, 378, 29, "dx"], [350, 33, 378, 31], [350, 36, 378, 34, "pixelSize"], [350, 45, 378, 43], [350, 49, 378, 47, "px"], [350, 51, 378, 49], [350, 54, 378, 52, "dx"], [350, 56, 378, 54], [350, 59, 378, 57, "<PERSON><PERSON><PERSON><PERSON>"], [350, 71, 378, 69], [350, 73, 378, 71, "dx"], [350, 75, 378, 73], [350, 77, 378, 75], [350, 79, 378, 77], [351, 16, 379, 14], [351, 22, 379, 20, "index"], [351, 27, 379, 25], [351, 30, 379, 28], [351, 31, 379, 29], [351, 32, 379, 30, "py"], [351, 34, 379, 32], [351, 37, 379, 35, "dy"], [351, 39, 379, 37], [351, 43, 379, 41, "<PERSON><PERSON><PERSON><PERSON>"], [351, 55, 379, 53], [351, 59, 379, 57, "px"], [351, 61, 379, 59], [351, 64, 379, 62, "dx"], [351, 66, 379, 64], [351, 67, 379, 65], [351, 71, 379, 69], [351, 72, 379, 70], [352, 16, 380, 14, "data"], [352, 20, 380, 18], [352, 21, 380, 19, "index"], [352, 26, 380, 24], [352, 27, 380, 25], [352, 30, 380, 28, "r"], [352, 31, 380, 29], [353, 16, 381, 14, "data"], [353, 20, 381, 18], [353, 21, 381, 19, "index"], [353, 26, 381, 24], [353, 29, 381, 27], [353, 30, 381, 28], [353, 31, 381, 29], [353, 34, 381, 32, "g"], [353, 35, 381, 33], [354, 16, 382, 14, "data"], [354, 20, 382, 18], [354, 21, 382, 19, "index"], [354, 26, 382, 24], [354, 29, 382, 27], [354, 30, 382, 28], [354, 31, 382, 29], [354, 34, 382, 32, "b"], [354, 35, 382, 33], [355, 16, 383, 14], [356, 14, 384, 12], [357, 12, 385, 10], [358, 10, 386, 8], [359, 8, 387, 6], [360, 6, 388, 4], [362, 6, 390, 4], [363, 6, 391, 4], [363, 11, 391, 9], [363, 15, 391, 13, "i"], [363, 16, 391, 14], [363, 19, 391, 17], [363, 20, 391, 18], [363, 22, 391, 20, "i"], [363, 23, 391, 21], [363, 26, 391, 24], [363, 27, 391, 25], [363, 29, 391, 27, "i"], [363, 30, 391, 28], [363, 32, 391, 30], [363, 34, 391, 32], [364, 8, 392, 6, "applySimpleBlur"], [364, 23, 392, 21], [364, 24, 392, 22, "data"], [364, 28, 392, 26], [364, 30, 392, 28, "<PERSON><PERSON><PERSON><PERSON>"], [364, 42, 392, 40], [364, 44, 392, 42, "clampedHeight"], [364, 57, 392, 55], [364, 58, 392, 56], [365, 6, 393, 4], [367, 6, 395, 4], [368, 6, 396, 4, "ctx"], [368, 9, 396, 7], [368, 10, 396, 8, "putImageData"], [368, 22, 396, 20], [368, 23, 396, 21, "imageData"], [368, 32, 396, 30], [368, 34, 396, 32, "clampedX"], [368, 42, 396, 40], [368, 44, 396, 42, "clampedY"], [368, 52, 396, 50], [368, 53, 396, 51], [370, 6, 398, 4], [371, 6, 399, 4, "ctx"], [371, 9, 399, 7], [371, 10, 399, 8, "fillStyle"], [371, 19, 399, 17], [371, 22, 399, 20], [371, 48, 399, 46], [372, 6, 400, 4, "ctx"], [372, 9, 400, 7], [372, 10, 400, 8, "fillRect"], [372, 18, 400, 16], [372, 19, 400, 17, "clampedX"], [372, 27, 400, 25], [372, 29, 400, 27, "clampedY"], [372, 37, 400, 35], [372, 39, 400, 37, "<PERSON><PERSON><PERSON><PERSON>"], [372, 51, 400, 49], [372, 53, 400, 51, "clampedHeight"], [372, 66, 400, 64], [372, 67, 400, 65], [373, 4, 401, 2], [373, 5, 401, 3], [374, 4, 403, 2], [374, 10, 403, 8, "applySimpleBlur"], [374, 25, 403, 23], [374, 28, 403, 26, "applySimpleBlur"], [374, 29, 403, 27, "data"], [374, 33, 403, 50], [374, 35, 403, 52, "width"], [374, 40, 403, 65], [374, 42, 403, 67, "height"], [374, 48, 403, 81], [374, 53, 403, 86], [375, 6, 404, 4], [375, 12, 404, 10, "original"], [375, 20, 404, 18], [375, 23, 404, 21], [375, 27, 404, 25, "Uint8ClampedArray"], [375, 44, 404, 42], [375, 45, 404, 43, "data"], [375, 49, 404, 47], [375, 50, 404, 48], [376, 6, 406, 4], [376, 11, 406, 9], [376, 15, 406, 13, "y"], [376, 16, 406, 14], [376, 19, 406, 17], [376, 20, 406, 18], [376, 22, 406, 20, "y"], [376, 23, 406, 21], [376, 26, 406, 24, "height"], [376, 32, 406, 30], [376, 35, 406, 33], [376, 36, 406, 34], [376, 38, 406, 36, "y"], [376, 39, 406, 37], [376, 41, 406, 39], [376, 43, 406, 41], [377, 8, 407, 6], [377, 13, 407, 11], [377, 17, 407, 15, "x"], [377, 18, 407, 16], [377, 21, 407, 19], [377, 22, 407, 20], [377, 24, 407, 22, "x"], [377, 25, 407, 23], [377, 28, 407, 26, "width"], [377, 33, 407, 31], [377, 36, 407, 34], [377, 37, 407, 35], [377, 39, 407, 37, "x"], [377, 40, 407, 38], [377, 42, 407, 40], [377, 44, 407, 42], [378, 10, 408, 8], [378, 16, 408, 14, "index"], [378, 21, 408, 19], [378, 24, 408, 22], [378, 25, 408, 23, "y"], [378, 26, 408, 24], [378, 29, 408, 27, "width"], [378, 34, 408, 32], [378, 37, 408, 35, "x"], [378, 38, 408, 36], [378, 42, 408, 40], [378, 43, 408, 41], [380, 10, 410, 8], [381, 10, 411, 8], [381, 14, 411, 12, "r"], [381, 15, 411, 13], [381, 18, 411, 16], [381, 19, 411, 17], [382, 12, 411, 19, "g"], [382, 13, 411, 20], [382, 16, 411, 23], [382, 17, 411, 24], [383, 12, 411, 26, "b"], [383, 13, 411, 27], [383, 16, 411, 30], [383, 17, 411, 31], [384, 10, 412, 8], [384, 15, 412, 13], [384, 19, 412, 17, "dy"], [384, 21, 412, 19], [384, 24, 412, 22], [384, 25, 412, 23], [384, 26, 412, 24], [384, 28, 412, 26, "dy"], [384, 30, 412, 28], [384, 34, 412, 32], [384, 35, 412, 33], [384, 37, 412, 35, "dy"], [384, 39, 412, 37], [384, 41, 412, 39], [384, 43, 412, 41], [385, 12, 413, 10], [385, 17, 413, 15], [385, 21, 413, 19, "dx"], [385, 23, 413, 21], [385, 26, 413, 24], [385, 27, 413, 25], [385, 28, 413, 26], [385, 30, 413, 28, "dx"], [385, 32, 413, 30], [385, 36, 413, 34], [385, 37, 413, 35], [385, 39, 413, 37, "dx"], [385, 41, 413, 39], [385, 43, 413, 41], [385, 45, 413, 43], [386, 14, 414, 12], [386, 20, 414, 18, "neighborIndex"], [386, 33, 414, 31], [386, 36, 414, 34], [386, 37, 414, 35], [386, 38, 414, 36, "y"], [386, 39, 414, 37], [386, 42, 414, 40, "dy"], [386, 44, 414, 42], [386, 48, 414, 46, "width"], [386, 53, 414, 51], [386, 57, 414, 55, "x"], [386, 58, 414, 56], [386, 61, 414, 59, "dx"], [386, 63, 414, 61], [386, 64, 414, 62], [386, 68, 414, 66], [386, 69, 414, 67], [387, 14, 415, 12, "r"], [387, 15, 415, 13], [387, 19, 415, 17, "original"], [387, 27, 415, 25], [387, 28, 415, 26, "neighborIndex"], [387, 41, 415, 39], [387, 42, 415, 40], [388, 14, 416, 12, "g"], [388, 15, 416, 13], [388, 19, 416, 17, "original"], [388, 27, 416, 25], [388, 28, 416, 26, "neighborIndex"], [388, 41, 416, 39], [388, 44, 416, 42], [388, 45, 416, 43], [388, 46, 416, 44], [389, 14, 417, 12, "b"], [389, 15, 417, 13], [389, 19, 417, 17, "original"], [389, 27, 417, 25], [389, 28, 417, 26, "neighborIndex"], [389, 41, 417, 39], [389, 44, 417, 42], [389, 45, 417, 43], [389, 46, 417, 44], [390, 12, 418, 10], [391, 10, 419, 8], [392, 10, 421, 8, "data"], [392, 14, 421, 12], [392, 15, 421, 13, "index"], [392, 20, 421, 18], [392, 21, 421, 19], [392, 24, 421, 22, "r"], [392, 25, 421, 23], [392, 28, 421, 26], [392, 29, 421, 27], [393, 10, 422, 8, "data"], [393, 14, 422, 12], [393, 15, 422, 13, "index"], [393, 20, 422, 18], [393, 23, 422, 21], [393, 24, 422, 22], [393, 25, 422, 23], [393, 28, 422, 26, "g"], [393, 29, 422, 27], [393, 32, 422, 30], [393, 33, 422, 31], [394, 10, 423, 8, "data"], [394, 14, 423, 12], [394, 15, 423, 13, "index"], [394, 20, 423, 18], [394, 23, 423, 21], [394, 24, 423, 22], [394, 25, 423, 23], [394, 28, 423, 26, "b"], [394, 29, 423, 27], [394, 32, 423, 30], [394, 33, 423, 31], [395, 8, 424, 6], [396, 6, 425, 4], [397, 4, 426, 2], [397, 5, 426, 3], [398, 4, 428, 2], [398, 10, 428, 8, "applyFallbackFaceBlur"], [398, 31, 428, 29], [398, 34, 428, 32, "applyFallbackFaceBlur"], [398, 35, 428, 33, "ctx"], [398, 38, 428, 62], [398, 40, 428, 64, "imgWidth"], [398, 48, 428, 80], [398, 50, 428, 82, "imgHeight"], [398, 59, 428, 99], [398, 64, 428, 104], [399, 6, 429, 4, "console"], [399, 13, 429, 11], [399, 14, 429, 12, "log"], [399, 17, 429, 15], [399, 18, 429, 16], [399, 90, 429, 88], [399, 91, 429, 89], [401, 6, 431, 4], [402, 6, 432, 4], [402, 12, 432, 10, "areas"], [402, 17, 432, 15], [402, 20, 432, 18], [403, 6, 433, 6], [404, 6, 434, 6], [405, 8, 434, 8, "x"], [405, 9, 434, 9], [405, 11, 434, 11, "imgWidth"], [405, 19, 434, 19], [405, 22, 434, 22], [405, 26, 434, 26], [406, 8, 434, 28, "y"], [406, 9, 434, 29], [406, 11, 434, 31, "imgHeight"], [406, 20, 434, 40], [406, 23, 434, 43], [406, 27, 434, 47], [407, 8, 434, 49, "w"], [407, 9, 434, 50], [407, 11, 434, 52, "imgWidth"], [407, 19, 434, 60], [407, 22, 434, 63], [407, 25, 434, 66], [408, 8, 434, 68, "h"], [408, 9, 434, 69], [408, 11, 434, 71, "imgHeight"], [408, 20, 434, 80], [408, 23, 434, 83], [409, 6, 434, 87], [409, 7, 434, 88], [410, 6, 435, 6], [411, 6, 436, 6], [412, 8, 436, 8, "x"], [412, 9, 436, 9], [412, 11, 436, 11, "imgWidth"], [412, 19, 436, 19], [412, 22, 436, 22], [412, 25, 436, 25], [413, 8, 436, 27, "y"], [413, 9, 436, 28], [413, 11, 436, 30, "imgHeight"], [413, 20, 436, 39], [413, 23, 436, 42], [413, 26, 436, 45], [414, 8, 436, 47, "w"], [414, 9, 436, 48], [414, 11, 436, 50, "imgWidth"], [414, 19, 436, 58], [414, 22, 436, 61], [414, 26, 436, 65], [415, 8, 436, 67, "h"], [415, 9, 436, 68], [415, 11, 436, 70, "imgHeight"], [415, 20, 436, 79], [415, 23, 436, 82], [416, 6, 436, 86], [416, 7, 436, 87], [417, 6, 437, 6], [418, 6, 438, 6], [419, 8, 438, 8, "x"], [419, 9, 438, 9], [419, 11, 438, 11, "imgWidth"], [419, 19, 438, 19], [419, 22, 438, 22], [419, 26, 438, 26], [420, 8, 438, 28, "y"], [420, 9, 438, 29], [420, 11, 438, 31, "imgHeight"], [420, 20, 438, 40], [420, 23, 438, 43], [420, 26, 438, 46], [421, 8, 438, 48, "w"], [421, 9, 438, 49], [421, 11, 438, 51, "imgWidth"], [421, 19, 438, 59], [421, 22, 438, 62], [421, 26, 438, 66], [422, 8, 438, 68, "h"], [422, 9, 438, 69], [422, 11, 438, 71, "imgHeight"], [422, 20, 438, 80], [422, 23, 438, 83], [423, 6, 438, 87], [423, 7, 438, 88], [423, 8, 439, 5], [424, 6, 441, 4, "areas"], [424, 11, 441, 9], [424, 12, 441, 10, "for<PERSON>ach"], [424, 19, 441, 17], [424, 20, 441, 18], [424, 21, 441, 19, "area"], [424, 25, 441, 23], [424, 27, 441, 25, "index"], [424, 32, 441, 30], [424, 37, 441, 35], [425, 8, 442, 6, "console"], [425, 15, 442, 13], [425, 16, 442, 14, "log"], [425, 19, 442, 17], [425, 20, 442, 18], [425, 65, 442, 63, "index"], [425, 70, 442, 68], [425, 73, 442, 71], [425, 74, 442, 72], [425, 77, 442, 75], [425, 79, 442, 77, "area"], [425, 83, 442, 81], [425, 84, 442, 82], [426, 8, 443, 6, "applyStrongBlur"], [426, 23, 443, 21], [426, 24, 443, 22, "ctx"], [426, 27, 443, 25], [426, 29, 443, 27, "area"], [426, 33, 443, 31], [426, 34, 443, 32, "x"], [426, 35, 443, 33], [426, 37, 443, 35, "area"], [426, 41, 443, 39], [426, 42, 443, 40, "y"], [426, 43, 443, 41], [426, 45, 443, 43, "area"], [426, 49, 443, 47], [426, 50, 443, 48, "w"], [426, 51, 443, 49], [426, 53, 443, 51, "area"], [426, 57, 443, 55], [426, 58, 443, 56, "h"], [426, 59, 443, 57], [426, 60, 443, 58], [427, 6, 444, 4], [427, 7, 444, 5], [427, 8, 444, 6], [428, 4, 445, 2], [428, 5, 445, 3], [430, 4, 447, 2], [431, 4, 448, 2], [431, 10, 448, 8, "capturePhoto"], [431, 22, 448, 20], [431, 25, 448, 23], [431, 29, 448, 23, "useCallback"], [431, 47, 448, 34], [431, 49, 448, 35], [431, 61, 448, 47], [432, 6, 449, 4], [433, 6, 450, 4], [433, 12, 450, 10, "isDev"], [433, 17, 450, 15], [433, 20, 450, 18, "process"], [433, 27, 450, 25], [433, 28, 450, 26, "env"], [433, 31, 450, 29], [433, 32, 450, 30, "NODE_ENV"], [433, 40, 450, 38], [433, 45, 450, 43], [433, 58, 450, 56], [433, 62, 450, 60, "__DEV__"], [433, 69, 450, 67], [434, 6, 452, 4], [434, 10, 452, 8], [434, 11, 452, 9, "cameraRef"], [434, 20, 452, 18], [434, 21, 452, 19, "current"], [434, 28, 452, 26], [434, 32, 452, 30], [434, 33, 452, 31, "isDev"], [434, 38, 452, 36], [434, 40, 452, 38], [435, 8, 453, 6, "<PERSON><PERSON>"], [435, 22, 453, 11], [435, 23, 453, 12, "alert"], [435, 28, 453, 17], [435, 29, 453, 18], [435, 36, 453, 25], [435, 38, 453, 27], [435, 56, 453, 45], [435, 57, 453, 46], [436, 8, 454, 6], [437, 6, 455, 4], [438, 6, 456, 4], [438, 10, 456, 8], [439, 8, 457, 6, "setProcessingState"], [439, 26, 457, 24], [439, 27, 457, 25], [439, 38, 457, 36], [439, 39, 457, 37], [440, 8, 458, 6, "setProcessingProgress"], [440, 29, 458, 27], [440, 30, 458, 28], [440, 32, 458, 30], [440, 33, 458, 31], [441, 8, 459, 6], [442, 8, 460, 6], [443, 8, 461, 6], [444, 8, 462, 6], [444, 14, 462, 12], [444, 18, 462, 16, "Promise"], [444, 25, 462, 23], [444, 26, 462, 24, "resolve"], [444, 33, 462, 31], [444, 37, 462, 35, "setTimeout"], [444, 47, 462, 45], [444, 48, 462, 46, "resolve"], [444, 55, 462, 53], [444, 57, 462, 55], [444, 59, 462, 57], [444, 60, 462, 58], [444, 61, 462, 59], [445, 8, 463, 6], [446, 8, 464, 6], [446, 12, 464, 10, "photo"], [446, 17, 464, 15], [447, 8, 466, 6], [447, 12, 466, 10], [448, 10, 467, 8, "photo"], [448, 15, 467, 13], [448, 18, 467, 16], [448, 24, 467, 22, "cameraRef"], [448, 33, 467, 31], [448, 34, 467, 32, "current"], [448, 41, 467, 39], [448, 42, 467, 40, "takePictureAsync"], [448, 58, 467, 56], [448, 59, 467, 57], [449, 12, 468, 10, "quality"], [449, 19, 468, 17], [449, 21, 468, 19], [449, 24, 468, 22], [450, 12, 469, 10, "base64"], [450, 18, 469, 16], [450, 20, 469, 18], [450, 25, 469, 23], [451, 12, 470, 10, "skipProcessing"], [451, 26, 470, 24], [451, 28, 470, 26], [451, 32, 470, 30], [451, 33, 470, 32], [452, 10, 471, 8], [452, 11, 471, 9], [452, 12, 471, 10], [453, 8, 472, 6], [453, 9, 472, 7], [453, 10, 472, 8], [453, 17, 472, 15, "cameraError"], [453, 28, 472, 26], [453, 30, 472, 28], [454, 10, 473, 8, "console"], [454, 17, 473, 15], [454, 18, 473, 16, "log"], [454, 21, 473, 19], [454, 22, 473, 20], [454, 82, 473, 80], [454, 84, 473, 82, "cameraError"], [454, 95, 473, 93], [454, 96, 473, 94], [455, 10, 474, 8], [456, 10, 475, 8], [456, 14, 475, 12, "isDev"], [456, 19, 475, 17], [456, 21, 475, 19], [457, 12, 476, 10, "photo"], [457, 17, 476, 15], [457, 20, 476, 18], [458, 14, 477, 12, "uri"], [458, 17, 477, 15], [458, 19, 477, 17], [459, 12, 478, 10], [459, 13, 478, 11], [460, 10, 479, 8], [460, 11, 479, 9], [460, 17, 479, 15], [461, 12, 480, 10], [461, 18, 480, 16, "cameraError"], [461, 29, 480, 27], [462, 10, 481, 8], [463, 8, 482, 6], [464, 8, 483, 6], [464, 12, 483, 10], [464, 13, 483, 11, "photo"], [464, 18, 483, 16], [464, 20, 483, 18], [465, 10, 484, 8], [465, 16, 484, 14], [465, 20, 484, 18, "Error"], [465, 25, 484, 23], [465, 26, 484, 24], [465, 51, 484, 49], [465, 52, 484, 50], [466, 8, 485, 6], [467, 8, 486, 6, "console"], [467, 15, 486, 13], [467, 16, 486, 14, "log"], [467, 19, 486, 17], [467, 20, 486, 18], [467, 56, 486, 54], [467, 58, 486, 56, "photo"], [467, 63, 486, 61], [467, 64, 486, 62, "uri"], [467, 67, 486, 65], [467, 68, 486, 66], [468, 8, 487, 6, "setCapturedPhoto"], [468, 24, 487, 22], [468, 25, 487, 23, "photo"], [468, 30, 487, 28], [468, 31, 487, 29, "uri"], [468, 34, 487, 32], [468, 35, 487, 33], [469, 8, 488, 6, "setProcessingProgress"], [469, 29, 488, 27], [469, 30, 488, 28], [469, 32, 488, 30], [469, 33, 488, 31], [470, 8, 489, 6], [471, 8, 490, 6, "console"], [471, 15, 490, 13], [471, 16, 490, 14, "log"], [471, 19, 490, 17], [471, 20, 490, 18], [471, 73, 490, 71], [471, 74, 490, 72], [472, 8, 491, 6], [472, 14, 491, 12, "processImageWithFaceBlur"], [472, 38, 491, 36], [472, 39, 491, 37, "photo"], [472, 44, 491, 42], [472, 45, 491, 43, "uri"], [472, 48, 491, 46], [472, 49, 491, 47], [473, 8, 492, 6, "console"], [473, 15, 492, 13], [473, 16, 492, 14, "log"], [473, 19, 492, 17], [473, 20, 492, 18], [473, 71, 492, 69], [473, 72, 492, 70], [474, 6, 493, 4], [474, 7, 493, 5], [474, 8, 493, 6], [474, 15, 493, 13, "error"], [474, 20, 493, 18], [474, 22, 493, 20], [475, 8, 494, 6, "console"], [475, 15, 494, 13], [475, 16, 494, 14, "error"], [475, 21, 494, 19], [475, 22, 494, 20], [475, 54, 494, 52], [475, 56, 494, 54, "error"], [475, 61, 494, 59], [475, 62, 494, 60], [476, 8, 495, 6, "setErrorMessage"], [476, 23, 495, 21], [476, 24, 495, 22], [476, 68, 495, 66], [476, 69, 495, 67], [477, 8, 496, 6, "setProcessingState"], [477, 26, 496, 24], [477, 27, 496, 25], [477, 34, 496, 32], [477, 35, 496, 33], [478, 6, 497, 4], [479, 4, 498, 2], [479, 5, 498, 3], [479, 7, 498, 5], [479, 9, 498, 7], [479, 10, 498, 8], [480, 4, 499, 2], [481, 4, 500, 2], [481, 10, 500, 8, "processImageWithFaceBlur"], [481, 34, 500, 32], [481, 37, 500, 35], [481, 43, 500, 42, "photoUri"], [481, 51, 500, 58], [481, 55, 500, 63], [482, 6, 501, 4], [482, 10, 501, 8], [483, 8, 502, 6, "console"], [483, 15, 502, 13], [483, 16, 502, 14, "log"], [483, 19, 502, 17], [483, 20, 502, 18], [483, 84, 502, 82], [483, 85, 502, 83], [484, 8, 503, 6, "setProcessingState"], [484, 26, 503, 24], [484, 27, 503, 25], [484, 39, 503, 37], [484, 40, 503, 38], [485, 8, 504, 6, "setProcessingProgress"], [485, 29, 504, 27], [485, 30, 504, 28], [485, 32, 504, 30], [485, 33, 504, 31], [487, 8, 506, 6], [488, 8, 507, 6], [488, 14, 507, 12, "canvas"], [488, 20, 507, 18], [488, 23, 507, 21, "document"], [488, 31, 507, 29], [488, 32, 507, 30, "createElement"], [488, 45, 507, 43], [488, 46, 507, 44], [488, 54, 507, 52], [488, 55, 507, 53], [489, 8, 508, 6], [489, 14, 508, 12, "ctx"], [489, 17, 508, 15], [489, 20, 508, 18, "canvas"], [489, 26, 508, 24], [489, 27, 508, 25, "getContext"], [489, 37, 508, 35], [489, 38, 508, 36], [489, 42, 508, 40], [489, 43, 508, 41], [490, 8, 509, 6], [490, 12, 509, 10], [490, 13, 509, 11, "ctx"], [490, 16, 509, 14], [490, 18, 509, 16], [490, 24, 509, 22], [490, 28, 509, 26, "Error"], [490, 33, 509, 31], [490, 34, 509, 32], [490, 64, 509, 62], [490, 65, 509, 63], [492, 8, 511, 6], [493, 8, 512, 6], [493, 14, 512, 12, "img"], [493, 17, 512, 15], [493, 20, 512, 18], [493, 24, 512, 22, "Image"], [493, 29, 512, 27], [493, 30, 512, 28], [493, 31, 512, 29], [494, 8, 513, 6], [494, 14, 513, 12], [494, 18, 513, 16, "Promise"], [494, 25, 513, 23], [494, 26, 513, 24], [494, 27, 513, 25, "resolve"], [494, 34, 513, 32], [494, 36, 513, 34, "reject"], [494, 42, 513, 40], [494, 47, 513, 45], [495, 10, 514, 8, "img"], [495, 13, 514, 11], [495, 14, 514, 12, "onload"], [495, 20, 514, 18], [495, 23, 514, 21, "resolve"], [495, 30, 514, 28], [496, 10, 515, 8, "img"], [496, 13, 515, 11], [496, 14, 515, 12, "onerror"], [496, 21, 515, 19], [496, 24, 515, 22, "reject"], [496, 30, 515, 28], [497, 10, 516, 8, "img"], [497, 13, 516, 11], [497, 14, 516, 12, "src"], [497, 17, 516, 15], [497, 20, 516, 18, "photoUri"], [497, 28, 516, 26], [498, 8, 517, 6], [498, 9, 517, 7], [498, 10, 517, 8], [500, 8, 519, 6], [501, 8, 520, 6, "canvas"], [501, 14, 520, 12], [501, 15, 520, 13, "width"], [501, 20, 520, 18], [501, 23, 520, 21, "img"], [501, 26, 520, 24], [501, 27, 520, 25, "width"], [501, 32, 520, 30], [502, 8, 521, 6, "canvas"], [502, 14, 521, 12], [502, 15, 521, 13, "height"], [502, 21, 521, 19], [502, 24, 521, 22, "img"], [502, 27, 521, 25], [502, 28, 521, 26, "height"], [502, 34, 521, 32], [503, 8, 522, 6, "console"], [503, 15, 522, 13], [503, 16, 522, 14, "log"], [503, 19, 522, 17], [503, 20, 522, 18], [503, 54, 522, 52], [503, 56, 522, 54], [504, 10, 522, 56, "width"], [504, 15, 522, 61], [504, 17, 522, 63, "img"], [504, 20, 522, 66], [504, 21, 522, 67, "width"], [504, 26, 522, 72], [505, 10, 522, 74, "height"], [505, 16, 522, 80], [505, 18, 522, 82, "img"], [505, 21, 522, 85], [505, 22, 522, 86, "height"], [506, 8, 522, 93], [506, 9, 522, 94], [506, 10, 522, 95], [508, 8, 524, 6], [509, 8, 525, 6, "ctx"], [509, 11, 525, 9], [509, 12, 525, 10, "drawImage"], [509, 21, 525, 19], [509, 22, 525, 20, "img"], [509, 25, 525, 23], [509, 27, 525, 25], [509, 28, 525, 26], [509, 30, 525, 28], [509, 31, 525, 29], [509, 32, 525, 30], [510, 8, 526, 6, "console"], [510, 15, 526, 13], [510, 16, 526, 14, "log"], [510, 19, 526, 17], [510, 20, 526, 18], [510, 72, 526, 70], [510, 73, 526, 71], [511, 8, 528, 6, "setProcessingProgress"], [511, 29, 528, 27], [511, 30, 528, 28], [511, 32, 528, 30], [511, 33, 528, 31], [513, 8, 530, 6], [514, 8, 531, 6], [514, 12, 531, 10, "detectedFaces"], [514, 25, 531, 23], [514, 28, 531, 26], [514, 30, 531, 28], [515, 8, 533, 6, "console"], [515, 15, 533, 13], [515, 16, 533, 14, "log"], [515, 19, 533, 17], [515, 20, 533, 18], [515, 81, 533, 79], [515, 82, 533, 80], [517, 8, 535, 6], [518, 8, 536, 6], [518, 12, 536, 10], [519, 10, 537, 8], [519, 16, 537, 14, "loadTensorFlowFaceDetection"], [519, 43, 537, 41], [519, 44, 537, 42], [519, 45, 537, 43], [520, 10, 538, 8, "detectedFaces"], [520, 23, 538, 21], [520, 26, 538, 24], [520, 32, 538, 30, "detectFacesWithTensorFlow"], [520, 57, 538, 55], [520, 58, 538, 56, "img"], [520, 61, 538, 59], [520, 62, 538, 60], [521, 10, 539, 8, "console"], [521, 17, 539, 15], [521, 18, 539, 16, "log"], [521, 21, 539, 19], [521, 22, 539, 20], [521, 70, 539, 68, "detectedFaces"], [521, 83, 539, 81], [521, 84, 539, 82, "length"], [521, 90, 539, 88], [521, 98, 539, 96], [521, 99, 539, 97], [522, 8, 540, 6], [522, 9, 540, 7], [522, 10, 540, 8], [522, 17, 540, 15, "tensorFlowError"], [522, 32, 540, 30], [522, 34, 540, 32], [523, 10, 541, 8, "console"], [523, 17, 541, 15], [523, 18, 541, 16, "warn"], [523, 22, 541, 20], [523, 23, 541, 21], [523, 61, 541, 59], [523, 63, 541, 61, "tensorFlowError"], [523, 78, 541, 76], [523, 79, 541, 77], [525, 10, 543, 8], [526, 10, 544, 8, "console"], [526, 17, 544, 15], [526, 18, 544, 16, "log"], [526, 21, 544, 19], [526, 22, 544, 20], [526, 86, 544, 84], [526, 87, 544, 85], [527, 10, 545, 8, "detectedFaces"], [527, 23, 545, 21], [527, 26, 545, 24, "detectFacesHeuristic"], [527, 46, 545, 44], [527, 47, 545, 45, "img"], [527, 50, 545, 48], [527, 52, 545, 50, "ctx"], [527, 55, 545, 53], [527, 56, 545, 54], [528, 10, 546, 8, "console"], [528, 17, 546, 15], [528, 18, 546, 16, "log"], [528, 21, 546, 19], [528, 22, 546, 20], [528, 70, 546, 68, "detectedFaces"], [528, 83, 546, 81], [528, 84, 546, 82, "length"], [528, 90, 546, 88], [528, 98, 546, 96], [528, 99, 546, 97], [529, 8, 547, 6], [531, 8, 549, 6], [532, 8, 550, 6], [532, 12, 550, 10, "detectedFaces"], [532, 25, 550, 23], [532, 26, 550, 24, "length"], [532, 32, 550, 30], [532, 37, 550, 35], [532, 38, 550, 36], [532, 40, 550, 38], [533, 10, 551, 8, "console"], [533, 17, 551, 15], [533, 18, 551, 16, "log"], [533, 21, 551, 19], [533, 22, 551, 20], [533, 89, 551, 87], [533, 90, 551, 88], [534, 10, 552, 8, "detectedFaces"], [534, 23, 552, 21], [534, 26, 552, 24, "detectFacesAggressive"], [534, 47, 552, 45], [534, 48, 552, 46, "img"], [534, 51, 552, 49], [534, 53, 552, 51, "ctx"], [534, 56, 552, 54], [534, 57, 552, 55], [535, 10, 553, 8, "console"], [535, 17, 553, 15], [535, 18, 553, 16, "log"], [535, 21, 553, 19], [535, 22, 553, 20], [535, 71, 553, 69, "detectedFaces"], [535, 84, 553, 82], [535, 85, 553, 83, "length"], [535, 91, 553, 89], [535, 99, 553, 97], [535, 100, 553, 98], [536, 8, 554, 6], [537, 8, 556, 6, "console"], [537, 15, 556, 13], [537, 16, 556, 14, "log"], [537, 19, 556, 17], [537, 20, 556, 18], [537, 72, 556, 70, "detectedFaces"], [537, 85, 556, 83], [537, 86, 556, 84, "length"], [537, 92, 556, 90], [537, 100, 556, 98], [537, 101, 556, 99], [538, 8, 557, 6], [538, 12, 557, 10, "detectedFaces"], [538, 25, 557, 23], [538, 26, 557, 24, "length"], [538, 32, 557, 30], [538, 35, 557, 33], [538, 36, 557, 34], [538, 38, 557, 36], [539, 10, 558, 8, "console"], [539, 17, 558, 15], [539, 18, 558, 16, "log"], [539, 21, 558, 19], [539, 22, 558, 20], [539, 66, 558, 64], [539, 68, 558, 66, "detectedFaces"], [539, 81, 558, 79], [539, 82, 558, 80, "map"], [539, 85, 558, 83], [539, 86, 558, 84], [539, 87, 558, 85, "face"], [539, 91, 558, 89], [539, 93, 558, 91, "i"], [539, 94, 558, 92], [539, 100, 558, 98], [540, 12, 559, 10, "faceNumber"], [540, 22, 559, 20], [540, 24, 559, 22, "i"], [540, 25, 559, 23], [540, 28, 559, 26], [540, 29, 559, 27], [541, 12, 560, 10, "centerX"], [541, 19, 560, 17], [541, 21, 560, 19, "face"], [541, 25, 560, 23], [541, 26, 560, 24, "boundingBox"], [541, 37, 560, 35], [541, 38, 560, 36, "xCenter"], [541, 45, 560, 43], [542, 12, 561, 10, "centerY"], [542, 19, 561, 17], [542, 21, 561, 19, "face"], [542, 25, 561, 23], [542, 26, 561, 24, "boundingBox"], [542, 37, 561, 35], [542, 38, 561, 36, "yCenter"], [542, 45, 561, 43], [543, 12, 562, 10, "width"], [543, 17, 562, 15], [543, 19, 562, 17, "face"], [543, 23, 562, 21], [543, 24, 562, 22, "boundingBox"], [543, 35, 562, 33], [543, 36, 562, 34, "width"], [543, 41, 562, 39], [544, 12, 563, 10, "height"], [544, 18, 563, 16], [544, 20, 563, 18, "face"], [544, 24, 563, 22], [544, 25, 563, 23, "boundingBox"], [544, 36, 563, 34], [544, 37, 563, 35, "height"], [545, 10, 564, 8], [545, 11, 564, 9], [545, 12, 564, 10], [545, 13, 564, 11], [545, 14, 564, 12], [546, 8, 565, 6], [546, 9, 565, 7], [546, 15, 565, 13], [547, 10, 566, 8, "console"], [547, 17, 566, 15], [547, 18, 566, 16, "log"], [547, 21, 566, 19], [547, 22, 566, 20], [547, 91, 566, 89], [547, 92, 566, 90], [548, 8, 567, 6], [549, 8, 569, 6, "setProcessingProgress"], [549, 29, 569, 27], [549, 30, 569, 28], [549, 32, 569, 30], [549, 33, 569, 31], [551, 8, 571, 6], [552, 8, 572, 6], [552, 12, 572, 10, "detectedFaces"], [552, 25, 572, 23], [552, 26, 572, 24, "length"], [552, 32, 572, 30], [552, 35, 572, 33], [552, 36, 572, 34], [552, 38, 572, 36], [553, 10, 573, 8, "console"], [553, 17, 573, 15], [553, 18, 573, 16, "log"], [553, 21, 573, 19], [553, 22, 573, 20], [553, 61, 573, 59, "detectedFaces"], [553, 74, 573, 72], [553, 75, 573, 73, "length"], [553, 81, 573, 79], [553, 101, 573, 99], [553, 102, 573, 100], [554, 10, 575, 8, "detectedFaces"], [554, 23, 575, 21], [554, 24, 575, 22, "for<PERSON>ach"], [554, 31, 575, 29], [554, 32, 575, 30], [554, 33, 575, 31, "detection"], [554, 42, 575, 40], [554, 44, 575, 42, "index"], [554, 49, 575, 47], [554, 54, 575, 52], [555, 12, 576, 10], [555, 18, 576, 16, "bbox"], [555, 22, 576, 20], [555, 25, 576, 23, "detection"], [555, 34, 576, 32], [555, 35, 576, 33, "boundingBox"], [555, 46, 576, 44], [557, 12, 578, 10], [558, 12, 579, 10], [558, 18, 579, 16, "faceX"], [558, 23, 579, 21], [558, 26, 579, 24, "bbox"], [558, 30, 579, 28], [558, 31, 579, 29, "xCenter"], [558, 38, 579, 36], [558, 41, 579, 39, "img"], [558, 44, 579, 42], [558, 45, 579, 43, "width"], [558, 50, 579, 48], [558, 53, 579, 52, "bbox"], [558, 57, 579, 56], [558, 58, 579, 57, "width"], [558, 63, 579, 62], [558, 66, 579, 65, "img"], [558, 69, 579, 68], [558, 70, 579, 69, "width"], [558, 75, 579, 74], [558, 78, 579, 78], [558, 79, 579, 79], [559, 12, 580, 10], [559, 18, 580, 16, "faceY"], [559, 23, 580, 21], [559, 26, 580, 24, "bbox"], [559, 30, 580, 28], [559, 31, 580, 29, "yCenter"], [559, 38, 580, 36], [559, 41, 580, 39, "img"], [559, 44, 580, 42], [559, 45, 580, 43, "height"], [559, 51, 580, 49], [559, 54, 580, 53, "bbox"], [559, 58, 580, 57], [559, 59, 580, 58, "height"], [559, 65, 580, 64], [559, 68, 580, 67, "img"], [559, 71, 580, 70], [559, 72, 580, 71, "height"], [559, 78, 580, 77], [559, 81, 580, 81], [559, 82, 580, 82], [560, 12, 581, 10], [560, 18, 581, 16, "faceWidth"], [560, 27, 581, 25], [560, 30, 581, 28, "bbox"], [560, 34, 581, 32], [560, 35, 581, 33, "width"], [560, 40, 581, 38], [560, 43, 581, 41, "img"], [560, 46, 581, 44], [560, 47, 581, 45, "width"], [560, 52, 581, 50], [561, 12, 582, 10], [561, 18, 582, 16, "faceHeight"], [561, 28, 582, 26], [561, 31, 582, 29, "bbox"], [561, 35, 582, 33], [561, 36, 582, 34, "height"], [561, 42, 582, 40], [561, 45, 582, 43, "img"], [561, 48, 582, 46], [561, 49, 582, 47, "height"], [561, 55, 582, 53], [563, 12, 584, 10], [564, 12, 585, 10], [564, 18, 585, 16, "padding"], [564, 25, 585, 23], [564, 28, 585, 26], [564, 31, 585, 29], [565, 12, 586, 10], [565, 18, 586, 16, "paddedX"], [565, 25, 586, 23], [565, 28, 586, 26, "Math"], [565, 32, 586, 30], [565, 33, 586, 31, "max"], [565, 36, 586, 34], [565, 37, 586, 35], [565, 38, 586, 36], [565, 40, 586, 38, "faceX"], [565, 45, 586, 43], [565, 48, 586, 46, "faceWidth"], [565, 57, 586, 55], [565, 60, 586, 58, "padding"], [565, 67, 586, 65], [565, 68, 586, 66], [566, 12, 587, 10], [566, 18, 587, 16, "paddedY"], [566, 25, 587, 23], [566, 28, 587, 26, "Math"], [566, 32, 587, 30], [566, 33, 587, 31, "max"], [566, 36, 587, 34], [566, 37, 587, 35], [566, 38, 587, 36], [566, 40, 587, 38, "faceY"], [566, 45, 587, 43], [566, 48, 587, 46, "faceHeight"], [566, 58, 587, 56], [566, 61, 587, 59, "padding"], [566, 68, 587, 66], [566, 69, 587, 67], [567, 12, 588, 10], [567, 18, 588, 16, "<PERSON><PERSON><PERSON><PERSON>"], [567, 29, 588, 27], [567, 32, 588, 30, "Math"], [567, 36, 588, 34], [567, 37, 588, 35, "min"], [567, 40, 588, 38], [567, 41, 588, 39, "img"], [567, 44, 588, 42], [567, 45, 588, 43, "width"], [567, 50, 588, 48], [567, 53, 588, 51, "paddedX"], [567, 60, 588, 58], [567, 62, 588, 60, "faceWidth"], [567, 71, 588, 69], [567, 75, 588, 73], [567, 76, 588, 74], [567, 79, 588, 77], [567, 80, 588, 78], [567, 83, 588, 81, "padding"], [567, 90, 588, 88], [567, 91, 588, 89], [567, 92, 588, 90], [568, 12, 589, 10], [568, 18, 589, 16, "paddedHeight"], [568, 30, 589, 28], [568, 33, 589, 31, "Math"], [568, 37, 589, 35], [568, 38, 589, 36, "min"], [568, 41, 589, 39], [568, 42, 589, 40, "img"], [568, 45, 589, 43], [568, 46, 589, 44, "height"], [568, 52, 589, 50], [568, 55, 589, 53, "paddedY"], [568, 62, 589, 60], [568, 64, 589, 62, "faceHeight"], [568, 74, 589, 72], [568, 78, 589, 76], [568, 79, 589, 77], [568, 82, 589, 80], [568, 83, 589, 81], [568, 86, 589, 84, "padding"], [568, 93, 589, 91], [568, 94, 589, 92], [568, 95, 589, 93], [569, 12, 591, 10, "console"], [569, 19, 591, 17], [569, 20, 591, 18, "log"], [569, 23, 591, 21], [569, 24, 591, 22], [569, 60, 591, 58, "index"], [569, 65, 591, 63], [569, 68, 591, 66], [569, 69, 591, 67], [569, 72, 591, 70], [569, 74, 591, 72], [570, 14, 592, 12, "original"], [570, 22, 592, 20], [570, 24, 592, 22], [571, 16, 592, 24, "x"], [571, 17, 592, 25], [571, 19, 592, 27, "Math"], [571, 23, 592, 31], [571, 24, 592, 32, "round"], [571, 29, 592, 37], [571, 30, 592, 38, "faceX"], [571, 35, 592, 43], [571, 36, 592, 44], [572, 16, 592, 46, "y"], [572, 17, 592, 47], [572, 19, 592, 49, "Math"], [572, 23, 592, 53], [572, 24, 592, 54, "round"], [572, 29, 592, 59], [572, 30, 592, 60, "faceY"], [572, 35, 592, 65], [572, 36, 592, 66], [573, 16, 592, 68, "w"], [573, 17, 592, 69], [573, 19, 592, 71, "Math"], [573, 23, 592, 75], [573, 24, 592, 76, "round"], [573, 29, 592, 81], [573, 30, 592, 82, "faceWidth"], [573, 39, 592, 91], [573, 40, 592, 92], [574, 16, 592, 94, "h"], [574, 17, 592, 95], [574, 19, 592, 97, "Math"], [574, 23, 592, 101], [574, 24, 592, 102, "round"], [574, 29, 592, 107], [574, 30, 592, 108, "faceHeight"], [574, 40, 592, 118], [575, 14, 592, 120], [575, 15, 592, 121], [576, 14, 593, 12, "padded"], [576, 20, 593, 18], [576, 22, 593, 20], [577, 16, 593, 22, "x"], [577, 17, 593, 23], [577, 19, 593, 25, "Math"], [577, 23, 593, 29], [577, 24, 593, 30, "round"], [577, 29, 593, 35], [577, 30, 593, 36, "paddedX"], [577, 37, 593, 43], [577, 38, 593, 44], [578, 16, 593, 46, "y"], [578, 17, 593, 47], [578, 19, 593, 49, "Math"], [578, 23, 593, 53], [578, 24, 593, 54, "round"], [578, 29, 593, 59], [578, 30, 593, 60, "paddedY"], [578, 37, 593, 67], [578, 38, 593, 68], [579, 16, 593, 70, "w"], [579, 17, 593, 71], [579, 19, 593, 73, "Math"], [579, 23, 593, 77], [579, 24, 593, 78, "round"], [579, 29, 593, 83], [579, 30, 593, 84, "<PERSON><PERSON><PERSON><PERSON>"], [579, 41, 593, 95], [579, 42, 593, 96], [580, 16, 593, 98, "h"], [580, 17, 593, 99], [580, 19, 593, 101, "Math"], [580, 23, 593, 105], [580, 24, 593, 106, "round"], [580, 29, 593, 111], [580, 30, 593, 112, "paddedHeight"], [580, 42, 593, 124], [581, 14, 593, 126], [582, 12, 594, 10], [582, 13, 594, 11], [582, 14, 594, 12], [584, 12, 596, 10], [585, 12, 597, 10, "console"], [585, 19, 597, 17], [585, 20, 597, 18, "log"], [585, 23, 597, 21], [585, 24, 597, 22], [585, 70, 597, 68], [585, 72, 597, 70], [586, 14, 598, 12, "width"], [586, 19, 598, 17], [586, 21, 598, 19, "canvas"], [586, 27, 598, 25], [586, 28, 598, 26, "width"], [586, 33, 598, 31], [587, 14, 599, 12, "height"], [587, 20, 599, 18], [587, 22, 599, 20, "canvas"], [587, 28, 599, 26], [587, 29, 599, 27, "height"], [587, 35, 599, 33], [588, 14, 600, 12, "contextValid"], [588, 26, 600, 24], [588, 28, 600, 26], [588, 29, 600, 27], [588, 30, 600, 28, "ctx"], [589, 12, 601, 10], [589, 13, 601, 11], [589, 14, 601, 12], [591, 12, 603, 10], [592, 12, 604, 10, "applyStrongBlur"], [592, 27, 604, 25], [592, 28, 604, 26, "ctx"], [592, 31, 604, 29], [592, 33, 604, 31, "paddedX"], [592, 40, 604, 38], [592, 42, 604, 40, "paddedY"], [592, 49, 604, 47], [592, 51, 604, 49, "<PERSON><PERSON><PERSON><PERSON>"], [592, 62, 604, 60], [592, 64, 604, 62, "paddedHeight"], [592, 76, 604, 74], [592, 77, 604, 75], [594, 12, 606, 10], [595, 12, 607, 10, "console"], [595, 19, 607, 17], [595, 20, 607, 18, "log"], [595, 23, 607, 21], [595, 24, 607, 22], [595, 102, 607, 100], [595, 103, 607, 101], [597, 12, 609, 10], [598, 12, 610, 10], [598, 18, 610, 16, "testImageData"], [598, 31, 610, 29], [598, 34, 610, 32, "ctx"], [598, 37, 610, 35], [598, 38, 610, 36, "getImageData"], [598, 50, 610, 48], [598, 51, 610, 49, "paddedX"], [598, 58, 610, 56], [598, 61, 610, 59], [598, 63, 610, 61], [598, 65, 610, 63, "paddedY"], [598, 72, 610, 70], [598, 75, 610, 73], [598, 77, 610, 75], [598, 79, 610, 77], [598, 81, 610, 79], [598, 83, 610, 81], [598, 85, 610, 83], [598, 86, 610, 84], [599, 12, 611, 10, "console"], [599, 19, 611, 17], [599, 20, 611, 18, "log"], [599, 23, 611, 21], [599, 24, 611, 22], [599, 70, 611, 68], [599, 72, 611, 70], [600, 14, 612, 12, "firstPixel"], [600, 24, 612, 22], [600, 26, 612, 24], [600, 27, 612, 25, "testImageData"], [600, 40, 612, 38], [600, 41, 612, 39, "data"], [600, 45, 612, 43], [600, 46, 612, 44], [600, 47, 612, 45], [600, 48, 612, 46], [600, 50, 612, 48, "testImageData"], [600, 63, 612, 61], [600, 64, 612, 62, "data"], [600, 68, 612, 66], [600, 69, 612, 67], [600, 70, 612, 68], [600, 71, 612, 69], [600, 73, 612, 71, "testImageData"], [600, 86, 612, 84], [600, 87, 612, 85, "data"], [600, 91, 612, 89], [600, 92, 612, 90], [600, 93, 612, 91], [600, 94, 612, 92], [600, 95, 612, 93], [601, 14, 613, 12, "secondPixel"], [601, 25, 613, 23], [601, 27, 613, 25], [601, 28, 613, 26, "testImageData"], [601, 41, 613, 39], [601, 42, 613, 40, "data"], [601, 46, 613, 44], [601, 47, 613, 45], [601, 48, 613, 46], [601, 49, 613, 47], [601, 51, 613, 49, "testImageData"], [601, 64, 613, 62], [601, 65, 613, 63, "data"], [601, 69, 613, 67], [601, 70, 613, 68], [601, 71, 613, 69], [601, 72, 613, 70], [601, 74, 613, 72, "testImageData"], [601, 87, 613, 85], [601, 88, 613, 86, "data"], [601, 92, 613, 90], [601, 93, 613, 91], [601, 94, 613, 92], [601, 95, 613, 93], [602, 12, 614, 10], [602, 13, 614, 11], [602, 14, 614, 12], [603, 12, 616, 10, "console"], [603, 19, 616, 17], [603, 20, 616, 18, "log"], [603, 23, 616, 21], [603, 24, 616, 22], [603, 50, 616, 48, "index"], [603, 55, 616, 53], [603, 58, 616, 56], [603, 59, 616, 57], [603, 79, 616, 77], [603, 80, 616, 78], [604, 10, 617, 8], [604, 11, 617, 9], [604, 12, 617, 10], [605, 10, 619, 8, "console"], [605, 17, 619, 15], [605, 18, 619, 16, "log"], [605, 21, 619, 19], [605, 22, 619, 20], [605, 48, 619, 46, "detectedFaces"], [605, 61, 619, 59], [605, 62, 619, 60, "length"], [605, 68, 619, 66], [605, 104, 619, 102], [605, 105, 619, 103], [606, 8, 620, 6], [606, 9, 620, 7], [606, 15, 620, 13], [607, 10, 621, 8, "console"], [607, 17, 621, 15], [607, 18, 621, 16, "log"], [607, 21, 621, 19], [607, 22, 621, 20], [607, 109, 621, 107], [607, 110, 621, 108], [608, 10, 622, 8], [609, 10, 623, 8, "applyFallbackFaceBlur"], [609, 31, 623, 29], [609, 32, 623, 30, "ctx"], [609, 35, 623, 33], [609, 37, 623, 35, "img"], [609, 40, 623, 38], [609, 41, 623, 39, "width"], [609, 46, 623, 44], [609, 48, 623, 46, "img"], [609, 51, 623, 49], [609, 52, 623, 50, "height"], [609, 58, 623, 56], [609, 59, 623, 57], [610, 8, 624, 6], [611, 8, 626, 6, "setProcessingProgress"], [611, 29, 626, 27], [611, 30, 626, 28], [611, 32, 626, 30], [611, 33, 626, 31], [613, 8, 628, 6], [614, 8, 629, 6, "console"], [614, 15, 629, 13], [614, 16, 629, 14, "log"], [614, 19, 629, 17], [614, 20, 629, 18], [614, 85, 629, 83], [614, 86, 629, 84], [615, 8, 630, 6], [615, 14, 630, 12, "blurredImageBlob"], [615, 30, 630, 28], [615, 33, 630, 31], [615, 39, 630, 37], [615, 43, 630, 41, "Promise"], [615, 50, 630, 48], [615, 51, 630, 56, "resolve"], [615, 58, 630, 63], [615, 62, 630, 68], [616, 10, 631, 8, "canvas"], [616, 16, 631, 14], [616, 17, 631, 15, "toBlob"], [616, 23, 631, 21], [616, 24, 631, 23, "blob"], [616, 28, 631, 27], [616, 32, 631, 32, "resolve"], [616, 39, 631, 39], [616, 40, 631, 40, "blob"], [616, 44, 631, 45], [616, 45, 631, 46], [616, 47, 631, 48], [616, 59, 631, 60], [616, 61, 631, 62], [616, 64, 631, 65], [616, 65, 631, 66], [617, 8, 632, 6], [617, 9, 632, 7], [617, 10, 632, 8], [618, 8, 634, 6], [618, 14, 634, 12, "blurredImageUrl"], [618, 29, 634, 27], [618, 32, 634, 30, "URL"], [618, 35, 634, 33], [618, 36, 634, 34, "createObjectURL"], [618, 51, 634, 49], [618, 52, 634, 50, "blurredImageBlob"], [618, 68, 634, 66], [618, 69, 634, 67], [619, 8, 635, 6, "console"], [619, 15, 635, 13], [619, 16, 635, 14, "log"], [619, 19, 635, 17], [619, 20, 635, 18], [619, 66, 635, 64], [619, 68, 635, 66, "blurredImageUrl"], [619, 83, 635, 81], [619, 84, 635, 82, "substring"], [619, 93, 635, 91], [619, 94, 635, 92], [619, 95, 635, 93], [619, 97, 635, 95], [619, 99, 635, 97], [619, 100, 635, 98], [619, 103, 635, 101], [619, 108, 635, 106], [619, 109, 635, 107], [620, 8, 637, 6, "setProcessingProgress"], [620, 29, 637, 27], [620, 30, 637, 28], [620, 33, 637, 31], [620, 34, 637, 32], [622, 8, 639, 6], [623, 8, 640, 6], [623, 14, 640, 12, "completeProcessing"], [623, 32, 640, 30], [623, 33, 640, 31, "blurredImageUrl"], [623, 48, 640, 46], [623, 49, 640, 47], [624, 6, 642, 4], [624, 7, 642, 5], [624, 8, 642, 6], [624, 15, 642, 13, "error"], [624, 20, 642, 18], [624, 22, 642, 20], [625, 8, 643, 6, "console"], [625, 15, 643, 13], [625, 16, 643, 14, "error"], [625, 21, 643, 19], [625, 22, 643, 20], [625, 57, 643, 55], [625, 59, 643, 57, "error"], [625, 64, 643, 62], [625, 65, 643, 63], [626, 8, 644, 6, "setErrorMessage"], [626, 23, 644, 21], [626, 24, 644, 22], [626, 50, 644, 48], [626, 51, 644, 49], [627, 8, 645, 6, "setProcessingState"], [627, 26, 645, 24], [627, 27, 645, 25], [627, 34, 645, 32], [627, 35, 645, 33], [628, 6, 646, 4], [629, 4, 647, 2], [629, 5, 647, 3], [631, 4, 649, 2], [632, 4, 650, 2], [632, 10, 650, 8, "completeProcessing"], [632, 28, 650, 26], [632, 31, 650, 29], [632, 37, 650, 36, "blurredImageUrl"], [632, 52, 650, 59], [632, 56, 650, 64], [633, 6, 651, 4], [633, 10, 651, 8], [634, 8, 652, 6, "setProcessingState"], [634, 26, 652, 24], [634, 27, 652, 25], [634, 37, 652, 35], [634, 38, 652, 36], [636, 8, 654, 6], [637, 8, 655, 6], [637, 14, 655, 12, "timestamp"], [637, 23, 655, 21], [637, 26, 655, 24, "Date"], [637, 30, 655, 28], [637, 31, 655, 29, "now"], [637, 34, 655, 32], [637, 35, 655, 33], [637, 36, 655, 34], [638, 8, 656, 6], [638, 14, 656, 12, "result"], [638, 20, 656, 18], [638, 23, 656, 21], [639, 10, 657, 8, "imageUrl"], [639, 18, 657, 16], [639, 20, 657, 18, "blurredImageUrl"], [639, 35, 657, 33], [640, 10, 658, 8, "localUri"], [640, 18, 658, 16], [640, 20, 658, 18, "blurredImageUrl"], [640, 35, 658, 33], [641, 10, 659, 8, "challengeCode"], [641, 23, 659, 21], [641, 25, 659, 23, "challengeCode"], [641, 38, 659, 36], [641, 42, 659, 40], [641, 44, 659, 42], [642, 10, 660, 8, "timestamp"], [642, 19, 660, 17], [643, 10, 661, 8, "jobId"], [643, 15, 661, 13], [643, 17, 661, 15], [643, 27, 661, 25, "timestamp"], [643, 36, 661, 34], [643, 38, 661, 36], [644, 10, 662, 8, "status"], [644, 16, 662, 14], [644, 18, 662, 16], [645, 8, 663, 6], [645, 9, 663, 7], [646, 8, 665, 6, "console"], [646, 15, 665, 13], [646, 16, 665, 14, "log"], [646, 19, 665, 17], [646, 20, 665, 18], [646, 100, 665, 98], [646, 102, 665, 100], [647, 10, 666, 8, "imageUrl"], [647, 18, 666, 16], [647, 20, 666, 18, "blurredImageUrl"], [647, 35, 666, 33], [647, 36, 666, 34, "substring"], [647, 45, 666, 43], [647, 46, 666, 44], [647, 47, 666, 45], [647, 49, 666, 47], [647, 51, 666, 49], [647, 52, 666, 50], [647, 55, 666, 53], [647, 60, 666, 58], [648, 10, 667, 8, "timestamp"], [648, 19, 667, 17], [649, 10, 668, 8, "jobId"], [649, 15, 668, 13], [649, 17, 668, 15, "result"], [649, 23, 668, 21], [649, 24, 668, 22, "jobId"], [650, 8, 669, 6], [650, 9, 669, 7], [650, 10, 669, 8], [652, 8, 671, 6], [653, 8, 672, 6, "onComplete"], [653, 18, 672, 16], [653, 19, 672, 17, "result"], [653, 25, 672, 23], [653, 26, 672, 24], [654, 6, 674, 4], [654, 7, 674, 5], [654, 8, 674, 6], [654, 15, 674, 13, "error"], [654, 20, 674, 18], [654, 22, 674, 20], [655, 8, 675, 6, "console"], [655, 15, 675, 13], [655, 16, 675, 14, "error"], [655, 21, 675, 19], [655, 22, 675, 20], [655, 57, 675, 55], [655, 59, 675, 57, "error"], [655, 64, 675, 62], [655, 65, 675, 63], [656, 8, 676, 6, "setErrorMessage"], [656, 23, 676, 21], [656, 24, 676, 22], [656, 56, 676, 54], [656, 57, 676, 55], [657, 8, 677, 6, "setProcessingState"], [657, 26, 677, 24], [657, 27, 677, 25], [657, 34, 677, 32], [657, 35, 677, 33], [658, 6, 678, 4], [659, 4, 679, 2], [659, 5, 679, 3], [661, 4, 681, 2], [662, 4, 682, 2], [662, 10, 682, 8, "triggerServerProcessing"], [662, 33, 682, 31], [662, 36, 682, 34], [662, 42, 682, 34, "triggerServerProcessing"], [662, 43, 682, 41, "privateImageUrl"], [662, 58, 682, 64], [662, 60, 682, 66, "timestamp"], [662, 69, 682, 83], [662, 74, 682, 88], [663, 6, 683, 4], [663, 10, 683, 8], [664, 8, 684, 6, "console"], [664, 15, 684, 13], [664, 16, 684, 14, "log"], [664, 19, 684, 17], [664, 20, 684, 18], [664, 74, 684, 72], [664, 76, 684, 74, "privateImageUrl"], [664, 91, 684, 89], [664, 92, 684, 90], [665, 8, 685, 6, "setProcessingState"], [665, 26, 685, 24], [665, 27, 685, 25], [665, 39, 685, 37], [665, 40, 685, 38], [666, 8, 686, 6, "setProcessingProgress"], [666, 29, 686, 27], [666, 30, 686, 28], [666, 32, 686, 30], [666, 33, 686, 31], [667, 8, 688, 6], [667, 14, 688, 12, "requestBody"], [667, 25, 688, 23], [667, 28, 688, 26], [668, 10, 689, 8, "imageUrl"], [668, 18, 689, 16], [668, 20, 689, 18, "privateImageUrl"], [668, 35, 689, 33], [669, 10, 690, 8, "userId"], [669, 16, 690, 14], [670, 10, 691, 8, "requestId"], [670, 19, 691, 17], [671, 10, 692, 8, "timestamp"], [671, 19, 692, 17], [672, 10, 693, 8, "platform"], [672, 18, 693, 16], [672, 20, 693, 18], [673, 8, 694, 6], [673, 9, 694, 7], [674, 8, 696, 6, "console"], [674, 15, 696, 13], [674, 16, 696, 14, "log"], [674, 19, 696, 17], [674, 20, 696, 18], [674, 65, 696, 63], [674, 67, 696, 65, "requestBody"], [674, 78, 696, 76], [674, 79, 696, 77], [676, 8, 698, 6], [677, 8, 699, 6], [677, 14, 699, 12, "response"], [677, 22, 699, 20], [677, 25, 699, 23], [677, 31, 699, 29, "fetch"], [677, 36, 699, 34], [677, 37, 699, 35], [677, 40, 699, 38, "API_BASE_URL"], [677, 52, 699, 50], [677, 72, 699, 70], [677, 74, 699, 72], [678, 10, 700, 8, "method"], [678, 16, 700, 14], [678, 18, 700, 16], [678, 24, 700, 22], [679, 10, 701, 8, "headers"], [679, 17, 701, 15], [679, 19, 701, 17], [680, 12, 702, 10], [680, 26, 702, 24], [680, 28, 702, 26], [680, 46, 702, 44], [681, 12, 703, 10], [681, 27, 703, 25], [681, 29, 703, 27], [681, 39, 703, 37], [681, 45, 703, 43, "getAuthToken"], [681, 57, 703, 55], [681, 58, 703, 56], [681, 59, 703, 57], [682, 10, 704, 8], [682, 11, 704, 9], [683, 10, 705, 8, "body"], [683, 14, 705, 12], [683, 16, 705, 14, "JSON"], [683, 20, 705, 18], [683, 21, 705, 19, "stringify"], [683, 30, 705, 28], [683, 31, 705, 29, "requestBody"], [683, 42, 705, 40], [684, 8, 706, 6], [684, 9, 706, 7], [684, 10, 706, 8], [685, 8, 708, 6], [685, 12, 708, 10], [685, 13, 708, 11, "response"], [685, 21, 708, 19], [685, 22, 708, 20, "ok"], [685, 24, 708, 22], [685, 26, 708, 24], [686, 10, 709, 8], [686, 16, 709, 14, "errorText"], [686, 25, 709, 23], [686, 28, 709, 26], [686, 34, 709, 32, "response"], [686, 42, 709, 40], [686, 43, 709, 41, "text"], [686, 47, 709, 45], [686, 48, 709, 46], [686, 49, 709, 47], [687, 10, 710, 8, "console"], [687, 17, 710, 15], [687, 18, 710, 16, "error"], [687, 23, 710, 21], [687, 24, 710, 22], [687, 68, 710, 66], [687, 70, 710, 68, "response"], [687, 78, 710, 76], [687, 79, 710, 77, "status"], [687, 85, 710, 83], [687, 87, 710, 85, "errorText"], [687, 96, 710, 94], [687, 97, 710, 95], [688, 10, 711, 8], [688, 16, 711, 14], [688, 20, 711, 18, "Error"], [688, 25, 711, 23], [688, 26, 711, 24], [688, 48, 711, 46, "response"], [688, 56, 711, 54], [688, 57, 711, 55, "status"], [688, 63, 711, 61], [688, 67, 711, 65, "response"], [688, 75, 711, 73], [688, 76, 711, 74, "statusText"], [688, 86, 711, 84], [688, 88, 711, 86], [688, 89, 711, 87], [689, 8, 712, 6], [690, 8, 714, 6], [690, 14, 714, 12, "result"], [690, 20, 714, 18], [690, 23, 714, 21], [690, 29, 714, 27, "response"], [690, 37, 714, 35], [690, 38, 714, 36, "json"], [690, 42, 714, 40], [690, 43, 714, 41], [690, 44, 714, 42], [691, 8, 715, 6, "console"], [691, 15, 715, 13], [691, 16, 715, 14, "log"], [691, 19, 715, 17], [691, 20, 715, 18], [691, 68, 715, 66], [691, 70, 715, 68, "result"], [691, 76, 715, 74], [691, 77, 715, 75], [692, 8, 717, 6], [692, 12, 717, 10], [692, 13, 717, 11, "result"], [692, 19, 717, 17], [692, 20, 717, 18, "jobId"], [692, 25, 717, 23], [692, 27, 717, 25], [693, 10, 718, 8], [693, 16, 718, 14], [693, 20, 718, 18, "Error"], [693, 25, 718, 23], [693, 26, 718, 24], [693, 70, 718, 68], [693, 71, 718, 69], [694, 8, 719, 6], [696, 8, 721, 6], [697, 8, 722, 6], [697, 14, 722, 12, "pollForCompletion"], [697, 31, 722, 29], [697, 32, 722, 30, "result"], [697, 38, 722, 36], [697, 39, 722, 37, "jobId"], [697, 44, 722, 42], [697, 46, 722, 44, "timestamp"], [697, 55, 722, 53], [697, 56, 722, 54], [698, 6, 723, 4], [698, 7, 723, 5], [698, 8, 723, 6], [698, 15, 723, 13, "error"], [698, 20, 723, 18], [698, 22, 723, 20], [699, 8, 724, 6, "console"], [699, 15, 724, 13], [699, 16, 724, 14, "error"], [699, 21, 724, 19], [699, 22, 724, 20], [699, 57, 724, 55], [699, 59, 724, 57, "error"], [699, 64, 724, 62], [699, 65, 724, 63], [700, 8, 725, 6, "setErrorMessage"], [700, 23, 725, 21], [700, 24, 725, 22], [700, 52, 725, 50, "error"], [700, 57, 725, 55], [700, 58, 725, 56, "message"], [700, 65, 725, 63], [700, 67, 725, 65], [700, 68, 725, 66], [701, 8, 726, 6, "setProcessingState"], [701, 26, 726, 24], [701, 27, 726, 25], [701, 34, 726, 32], [701, 35, 726, 33], [702, 6, 727, 4], [703, 4, 728, 2], [703, 5, 728, 3], [704, 4, 729, 2], [705, 4, 730, 2], [705, 10, 730, 8, "pollForCompletion"], [705, 27, 730, 25], [705, 30, 730, 28], [705, 36, 730, 28, "pollForCompletion"], [705, 37, 730, 35, "jobId"], [705, 42, 730, 48], [705, 44, 730, 50, "timestamp"], [705, 53, 730, 67], [705, 55, 730, 69, "attempts"], [705, 63, 730, 77], [705, 66, 730, 80], [705, 67, 730, 81], [705, 72, 730, 86], [706, 6, 731, 4], [706, 12, 731, 10, "MAX_ATTEMPTS"], [706, 24, 731, 22], [706, 27, 731, 25], [706, 29, 731, 27], [706, 30, 731, 28], [706, 31, 731, 29], [707, 6, 732, 4], [707, 12, 732, 10, "POLL_INTERVAL"], [707, 25, 732, 23], [707, 28, 732, 26], [707, 32, 732, 30], [707, 33, 732, 31], [707, 34, 732, 32], [709, 6, 734, 4, "console"], [709, 13, 734, 11], [709, 14, 734, 12, "log"], [709, 17, 734, 15], [709, 18, 734, 16], [709, 53, 734, 51, "attempts"], [709, 61, 734, 59], [709, 64, 734, 62], [709, 65, 734, 63], [709, 69, 734, 67, "MAX_ATTEMPTS"], [709, 81, 734, 79], [709, 93, 734, 91, "jobId"], [709, 98, 734, 96], [709, 100, 734, 98], [709, 101, 734, 99], [710, 6, 736, 4], [710, 10, 736, 8, "attempts"], [710, 18, 736, 16], [710, 22, 736, 20, "MAX_ATTEMPTS"], [710, 34, 736, 32], [710, 36, 736, 34], [711, 8, 737, 6, "console"], [711, 15, 737, 13], [711, 16, 737, 14, "error"], [711, 21, 737, 19], [711, 22, 737, 20], [711, 75, 737, 73], [711, 76, 737, 74], [712, 8, 738, 6, "setErrorMessage"], [712, 23, 738, 21], [712, 24, 738, 22], [712, 63, 738, 61], [712, 64, 738, 62], [713, 8, 739, 6, "setProcessingState"], [713, 26, 739, 24], [713, 27, 739, 25], [713, 34, 739, 32], [713, 35, 739, 33], [714, 8, 740, 6], [715, 6, 741, 4], [716, 6, 743, 4], [716, 10, 743, 8], [717, 8, 744, 6], [717, 14, 744, 12, "response"], [717, 22, 744, 20], [717, 25, 744, 23], [717, 31, 744, 29, "fetch"], [717, 36, 744, 34], [717, 37, 744, 35], [717, 40, 744, 38, "API_BASE_URL"], [717, 52, 744, 50], [717, 75, 744, 73, "jobId"], [717, 80, 744, 78], [717, 82, 744, 80], [717, 84, 744, 82], [718, 10, 745, 8, "headers"], [718, 17, 745, 15], [718, 19, 745, 17], [719, 12, 746, 10], [719, 27, 746, 25], [719, 29, 746, 27], [719, 39, 746, 37], [719, 45, 746, 43, "getAuthToken"], [719, 57, 746, 55], [719, 58, 746, 56], [719, 59, 746, 57], [720, 10, 747, 8], [721, 8, 748, 6], [721, 9, 748, 7], [721, 10, 748, 8], [722, 8, 750, 6], [722, 12, 750, 10], [722, 13, 750, 11, "response"], [722, 21, 750, 19], [722, 22, 750, 20, "ok"], [722, 24, 750, 22], [722, 26, 750, 24], [723, 10, 751, 8], [723, 16, 751, 14], [723, 20, 751, 18, "Error"], [723, 25, 751, 23], [723, 26, 751, 24], [723, 34, 751, 32, "response"], [723, 42, 751, 40], [723, 43, 751, 41, "status"], [723, 49, 751, 47], [723, 54, 751, 52, "response"], [723, 62, 751, 60], [723, 63, 751, 61, "statusText"], [723, 73, 751, 71], [723, 75, 751, 73], [723, 76, 751, 74], [724, 8, 752, 6], [725, 8, 754, 6], [725, 14, 754, 12, "status"], [725, 20, 754, 18], [725, 23, 754, 21], [725, 29, 754, 27, "response"], [725, 37, 754, 35], [725, 38, 754, 36, "json"], [725, 42, 754, 40], [725, 43, 754, 41], [725, 44, 754, 42], [726, 8, 755, 6, "console"], [726, 15, 755, 13], [726, 16, 755, 14, "log"], [726, 19, 755, 17], [726, 20, 755, 18], [726, 54, 755, 52], [726, 56, 755, 54, "status"], [726, 62, 755, 60], [726, 63, 755, 61], [727, 8, 757, 6], [727, 12, 757, 10, "status"], [727, 18, 757, 16], [727, 19, 757, 17, "status"], [727, 25, 757, 23], [727, 30, 757, 28], [727, 41, 757, 39], [727, 43, 757, 41], [728, 10, 758, 8, "console"], [728, 17, 758, 15], [728, 18, 758, 16, "log"], [728, 21, 758, 19], [728, 22, 758, 20], [728, 73, 758, 71], [728, 74, 758, 72], [729, 10, 759, 8, "setProcessingProgress"], [729, 31, 759, 29], [729, 32, 759, 30], [729, 35, 759, 33], [729, 36, 759, 34], [730, 10, 760, 8, "setProcessingState"], [730, 28, 760, 26], [730, 29, 760, 27], [730, 40, 760, 38], [730, 41, 760, 39], [731, 10, 761, 8], [732, 10, 762, 8], [732, 16, 762, 14, "result"], [732, 22, 762, 20], [732, 25, 762, 23], [733, 12, 763, 10, "imageUrl"], [733, 20, 763, 18], [733, 22, 763, 20, "status"], [733, 28, 763, 26], [733, 29, 763, 27, "publicUrl"], [733, 38, 763, 36], [734, 12, 763, 38], [735, 12, 764, 10, "localUri"], [735, 20, 764, 18], [735, 22, 764, 20, "capturedPhoto"], [735, 35, 764, 33], [735, 39, 764, 37, "status"], [735, 45, 764, 43], [735, 46, 764, 44, "publicUrl"], [735, 55, 764, 53], [736, 12, 764, 55], [737, 12, 765, 10, "challengeCode"], [737, 25, 765, 23], [737, 27, 765, 25, "challengeCode"], [737, 40, 765, 38], [737, 44, 765, 42], [737, 46, 765, 44], [738, 12, 766, 10, "timestamp"], [738, 21, 766, 19], [739, 12, 767, 10, "processingStatus"], [739, 28, 767, 26], [739, 30, 767, 28], [740, 10, 768, 8], [740, 11, 768, 9], [741, 10, 769, 8, "console"], [741, 17, 769, 15], [741, 18, 769, 16, "log"], [741, 21, 769, 19], [741, 22, 769, 20], [741, 57, 769, 55], [741, 59, 769, 57, "result"], [741, 65, 769, 63], [741, 66, 769, 64], [742, 10, 770, 8, "onComplete"], [742, 20, 770, 18], [742, 21, 770, 19, "result"], [742, 27, 770, 25], [742, 28, 770, 26], [743, 10, 771, 8], [744, 8, 772, 6], [744, 9, 772, 7], [744, 15, 772, 13], [744, 19, 772, 17, "status"], [744, 25, 772, 23], [744, 26, 772, 24, "status"], [744, 32, 772, 30], [744, 37, 772, 35], [744, 45, 772, 43], [744, 47, 772, 45], [745, 10, 773, 8, "console"], [745, 17, 773, 15], [745, 18, 773, 16, "error"], [745, 23, 773, 21], [745, 24, 773, 22], [745, 60, 773, 58], [745, 62, 773, 60, "status"], [745, 68, 773, 66], [745, 69, 773, 67, "error"], [745, 74, 773, 72], [745, 75, 773, 73], [746, 10, 774, 8], [746, 16, 774, 14], [746, 20, 774, 18, "Error"], [746, 25, 774, 23], [746, 26, 774, 24, "status"], [746, 32, 774, 30], [746, 33, 774, 31, "error"], [746, 38, 774, 36], [746, 42, 774, 40], [746, 61, 774, 59], [746, 62, 774, 60], [747, 8, 775, 6], [747, 9, 775, 7], [747, 15, 775, 13], [748, 10, 776, 8], [749, 10, 777, 8], [749, 16, 777, 14, "progressValue"], [749, 29, 777, 27], [749, 32, 777, 30], [749, 34, 777, 32], [749, 37, 777, 36, "attempts"], [749, 45, 777, 44], [749, 48, 777, 47, "MAX_ATTEMPTS"], [749, 60, 777, 59], [749, 63, 777, 63], [749, 65, 777, 65], [750, 10, 778, 8, "console"], [750, 17, 778, 15], [750, 18, 778, 16, "log"], [750, 21, 778, 19], [750, 22, 778, 20], [750, 71, 778, 69, "progressValue"], [750, 84, 778, 82], [750, 87, 778, 85], [750, 88, 778, 86], [751, 10, 779, 8, "setProcessingProgress"], [751, 31, 779, 29], [751, 32, 779, 30, "progressValue"], [751, 45, 779, 43], [751, 46, 779, 44], [752, 10, 781, 8, "setTimeout"], [752, 20, 781, 18], [752, 21, 781, 19], [752, 27, 781, 25], [753, 12, 782, 10, "pollForCompletion"], [753, 29, 782, 27], [753, 30, 782, 28, "jobId"], [753, 35, 782, 33], [753, 37, 782, 35, "timestamp"], [753, 46, 782, 44], [753, 48, 782, 46, "attempts"], [753, 56, 782, 54], [753, 59, 782, 57], [753, 60, 782, 58], [753, 61, 782, 59], [754, 10, 783, 8], [754, 11, 783, 9], [754, 13, 783, 11, "POLL_INTERVAL"], [754, 26, 783, 24], [754, 27, 783, 25], [755, 8, 784, 6], [756, 6, 785, 4], [756, 7, 785, 5], [756, 8, 785, 6], [756, 15, 785, 13, "error"], [756, 20, 785, 18], [756, 22, 785, 20], [757, 8, 786, 6, "console"], [757, 15, 786, 13], [757, 16, 786, 14, "error"], [757, 21, 786, 19], [757, 22, 786, 20], [757, 54, 786, 52], [757, 56, 786, 54, "error"], [757, 61, 786, 59], [757, 62, 786, 60], [758, 8, 787, 6, "setErrorMessage"], [758, 23, 787, 21], [758, 24, 787, 22], [758, 62, 787, 60, "error"], [758, 67, 787, 65], [758, 68, 787, 66, "message"], [758, 75, 787, 73], [758, 77, 787, 75], [758, 78, 787, 76], [759, 8, 788, 6, "setProcessingState"], [759, 26, 788, 24], [759, 27, 788, 25], [759, 34, 788, 32], [759, 35, 788, 33], [760, 6, 789, 4], [761, 4, 790, 2], [761, 5, 790, 3], [762, 4, 791, 2], [763, 4, 792, 2], [763, 10, 792, 8, "getAuthToken"], [763, 22, 792, 20], [763, 25, 792, 23], [763, 31, 792, 23, "getAuthToken"], [763, 32, 792, 23], [763, 37, 792, 52], [764, 6, 793, 4], [765, 6, 794, 4], [766, 6, 795, 4], [766, 13, 795, 11], [766, 30, 795, 28], [767, 4, 796, 2], [767, 5, 796, 3], [769, 4, 798, 2], [770, 4, 799, 2], [770, 10, 799, 8, "retryCapture"], [770, 22, 799, 20], [770, 25, 799, 23], [770, 29, 799, 23, "useCallback"], [770, 47, 799, 34], [770, 49, 799, 35], [770, 55, 799, 41], [771, 6, 800, 4, "console"], [771, 13, 800, 11], [771, 14, 800, 12, "log"], [771, 17, 800, 15], [771, 18, 800, 16], [771, 55, 800, 53], [771, 56, 800, 54], [772, 6, 801, 4, "setProcessingState"], [772, 24, 801, 22], [772, 25, 801, 23], [772, 31, 801, 29], [772, 32, 801, 30], [773, 6, 802, 4, "setErrorMessage"], [773, 21, 802, 19], [773, 22, 802, 20], [773, 24, 802, 22], [773, 25, 802, 23], [774, 6, 803, 4, "setCapturedPhoto"], [774, 22, 803, 20], [774, 23, 803, 21], [774, 25, 803, 23], [774, 26, 803, 24], [775, 6, 804, 4, "setProcessingProgress"], [775, 27, 804, 25], [775, 28, 804, 26], [775, 29, 804, 27], [775, 30, 804, 28], [776, 4, 805, 2], [776, 5, 805, 3], [776, 7, 805, 5], [776, 9, 805, 7], [776, 10, 805, 8], [777, 4, 806, 2], [778, 4, 807, 2], [778, 8, 807, 2, "useEffect"], [778, 24, 807, 11], [778, 26, 807, 12], [778, 32, 807, 18], [779, 6, 808, 4, "console"], [779, 13, 808, 11], [779, 14, 808, 12, "log"], [779, 17, 808, 15], [779, 18, 808, 16], [779, 53, 808, 51], [779, 55, 808, 53, "permission"], [779, 65, 808, 63], [779, 66, 808, 64], [780, 6, 809, 4], [780, 10, 809, 8, "permission"], [780, 20, 809, 18], [780, 22, 809, 20], [781, 8, 810, 6, "console"], [781, 15, 810, 13], [781, 16, 810, 14, "log"], [781, 19, 810, 17], [781, 20, 810, 18], [781, 57, 810, 55], [781, 59, 810, 57, "permission"], [781, 69, 810, 67], [781, 70, 810, 68, "granted"], [781, 77, 810, 75], [781, 78, 810, 76], [782, 6, 811, 4], [783, 4, 812, 2], [783, 5, 812, 3], [783, 7, 812, 5], [783, 8, 812, 6, "permission"], [783, 18, 812, 16], [783, 19, 812, 17], [783, 20, 812, 18], [784, 4, 813, 2], [785, 4, 814, 2], [785, 8, 814, 6], [785, 9, 814, 7, "permission"], [785, 19, 814, 17], [785, 21, 814, 19], [786, 6, 815, 4, "console"], [786, 13, 815, 11], [786, 14, 815, 12, "log"], [786, 17, 815, 15], [786, 18, 815, 16], [786, 67, 815, 65], [786, 68, 815, 66], [787, 6, 816, 4], [787, 26, 817, 6], [787, 30, 817, 6, "_jsxDevRuntime"], [787, 44, 817, 6], [787, 45, 817, 6, "jsxDEV"], [787, 51, 817, 6], [787, 53, 817, 7, "_View"], [787, 58, 817, 7], [787, 59, 817, 7, "default"], [787, 66, 817, 11], [788, 8, 817, 12, "style"], [788, 13, 817, 17], [788, 15, 817, 19, "styles"], [788, 21, 817, 25], [788, 22, 817, 26, "container"], [788, 31, 817, 36], [789, 8, 817, 36, "children"], [789, 16, 817, 36], [789, 32, 818, 8], [789, 36, 818, 8, "_jsxDevRuntime"], [789, 50, 818, 8], [789, 51, 818, 8, "jsxDEV"], [789, 57, 818, 8], [789, 59, 818, 9, "_ActivityIndicator"], [789, 77, 818, 9], [789, 78, 818, 9, "default"], [789, 85, 818, 26], [790, 10, 818, 27, "size"], [790, 14, 818, 31], [790, 16, 818, 32], [790, 23, 818, 39], [791, 10, 818, 40, "color"], [791, 15, 818, 45], [791, 17, 818, 46], [792, 8, 818, 55], [793, 10, 818, 55, "fileName"], [793, 18, 818, 55], [793, 20, 818, 55, "_jsxFileName"], [793, 32, 818, 55], [794, 10, 818, 55, "lineNumber"], [794, 20, 818, 55], [795, 10, 818, 55, "columnNumber"], [795, 22, 818, 55], [796, 8, 818, 55], [796, 15, 818, 57], [796, 16, 818, 58], [796, 31, 819, 8], [796, 35, 819, 8, "_jsxDevRuntime"], [796, 49, 819, 8], [796, 50, 819, 8, "jsxDEV"], [796, 56, 819, 8], [796, 58, 819, 9, "_Text"], [796, 63, 819, 9], [796, 64, 819, 9, "default"], [796, 71, 819, 13], [797, 10, 819, 14, "style"], [797, 15, 819, 19], [797, 17, 819, 21, "styles"], [797, 23, 819, 27], [797, 24, 819, 28, "loadingText"], [797, 35, 819, 40], [798, 10, 819, 40, "children"], [798, 18, 819, 40], [798, 20, 819, 41], [799, 8, 819, 58], [800, 10, 819, 58, "fileName"], [800, 18, 819, 58], [800, 20, 819, 58, "_jsxFileName"], [800, 32, 819, 58], [801, 10, 819, 58, "lineNumber"], [801, 20, 819, 58], [802, 10, 819, 58, "columnNumber"], [802, 22, 819, 58], [803, 8, 819, 58], [803, 15, 819, 64], [803, 16, 819, 65], [804, 6, 819, 65], [805, 8, 819, 65, "fileName"], [805, 16, 819, 65], [805, 18, 819, 65, "_jsxFileName"], [805, 30, 819, 65], [806, 8, 819, 65, "lineNumber"], [806, 18, 819, 65], [807, 8, 819, 65, "columnNumber"], [807, 20, 819, 65], [808, 6, 819, 65], [808, 13, 820, 12], [808, 14, 820, 13], [809, 4, 822, 2], [810, 4, 823, 2], [810, 8, 823, 6], [810, 9, 823, 7, "permission"], [810, 19, 823, 17], [810, 20, 823, 18, "granted"], [810, 27, 823, 25], [810, 29, 823, 27], [811, 6, 824, 4, "console"], [811, 13, 824, 11], [811, 14, 824, 12, "log"], [811, 17, 824, 15], [811, 18, 824, 16], [811, 93, 824, 91], [811, 94, 824, 92], [812, 6, 825, 4], [812, 26, 826, 6], [812, 30, 826, 6, "_jsxDevRuntime"], [812, 44, 826, 6], [812, 45, 826, 6, "jsxDEV"], [812, 51, 826, 6], [812, 53, 826, 7, "_View"], [812, 58, 826, 7], [812, 59, 826, 7, "default"], [812, 66, 826, 11], [813, 8, 826, 12, "style"], [813, 13, 826, 17], [813, 15, 826, 19, "styles"], [813, 21, 826, 25], [813, 22, 826, 26, "container"], [813, 31, 826, 36], [814, 8, 826, 36, "children"], [814, 16, 826, 36], [814, 31, 827, 8], [814, 35, 827, 8, "_jsxDevRuntime"], [814, 49, 827, 8], [814, 50, 827, 8, "jsxDEV"], [814, 56, 827, 8], [814, 58, 827, 9, "_View"], [814, 63, 827, 9], [814, 64, 827, 9, "default"], [814, 71, 827, 13], [815, 10, 827, 14, "style"], [815, 15, 827, 19], [815, 17, 827, 21, "styles"], [815, 23, 827, 27], [815, 24, 827, 28, "permissionContent"], [815, 41, 827, 46], [816, 10, 827, 46, "children"], [816, 18, 827, 46], [816, 34, 828, 10], [816, 38, 828, 10, "_jsxDevRuntime"], [816, 52, 828, 10], [816, 53, 828, 10, "jsxDEV"], [816, 59, 828, 10], [816, 61, 828, 11, "_lucideReactNative"], [816, 79, 828, 11], [816, 80, 828, 11, "Camera"], [816, 86, 828, 21], [817, 12, 828, 22, "size"], [817, 16, 828, 26], [817, 18, 828, 28], [817, 20, 828, 31], [818, 12, 828, 32, "color"], [818, 17, 828, 37], [818, 19, 828, 38], [819, 10, 828, 47], [820, 12, 828, 47, "fileName"], [820, 20, 828, 47], [820, 22, 828, 47, "_jsxFileName"], [820, 34, 828, 47], [821, 12, 828, 47, "lineNumber"], [821, 22, 828, 47], [822, 12, 828, 47, "columnNumber"], [822, 24, 828, 47], [823, 10, 828, 47], [823, 17, 828, 49], [823, 18, 828, 50], [823, 33, 829, 10], [823, 37, 829, 10, "_jsxDevRuntime"], [823, 51, 829, 10], [823, 52, 829, 10, "jsxDEV"], [823, 58, 829, 10], [823, 60, 829, 11, "_Text"], [823, 65, 829, 11], [823, 66, 829, 11, "default"], [823, 73, 829, 15], [824, 12, 829, 16, "style"], [824, 17, 829, 21], [824, 19, 829, 23, "styles"], [824, 25, 829, 29], [824, 26, 829, 30, "permissionTitle"], [824, 41, 829, 46], [825, 12, 829, 46, "children"], [825, 20, 829, 46], [825, 22, 829, 47], [826, 10, 829, 73], [827, 12, 829, 73, "fileName"], [827, 20, 829, 73], [827, 22, 829, 73, "_jsxFileName"], [827, 34, 829, 73], [828, 12, 829, 73, "lineNumber"], [828, 22, 829, 73], [829, 12, 829, 73, "columnNumber"], [829, 24, 829, 73], [830, 10, 829, 73], [830, 17, 829, 79], [830, 18, 829, 80], [830, 33, 830, 10], [830, 37, 830, 10, "_jsxDevRuntime"], [830, 51, 830, 10], [830, 52, 830, 10, "jsxDEV"], [830, 58, 830, 10], [830, 60, 830, 11, "_Text"], [830, 65, 830, 11], [830, 66, 830, 11, "default"], [830, 73, 830, 15], [831, 12, 830, 16, "style"], [831, 17, 830, 21], [831, 19, 830, 23, "styles"], [831, 25, 830, 29], [831, 26, 830, 30, "permissionDescription"], [831, 47, 830, 52], [832, 12, 830, 52, "children"], [832, 20, 830, 52], [832, 22, 830, 53], [833, 10, 833, 10], [834, 12, 833, 10, "fileName"], [834, 20, 833, 10], [834, 22, 833, 10, "_jsxFileName"], [834, 34, 833, 10], [835, 12, 833, 10, "lineNumber"], [835, 22, 833, 10], [836, 12, 833, 10, "columnNumber"], [836, 24, 833, 10], [837, 10, 833, 10], [837, 17, 833, 16], [837, 18, 833, 17], [837, 33, 834, 10], [837, 37, 834, 10, "_jsxDevRuntime"], [837, 51, 834, 10], [837, 52, 834, 10, "jsxDEV"], [837, 58, 834, 10], [837, 60, 834, 11, "_TouchableOpacity"], [837, 77, 834, 11], [837, 78, 834, 11, "default"], [837, 85, 834, 27], [838, 12, 834, 28, "onPress"], [838, 19, 834, 35], [838, 21, 834, 37, "requestPermission"], [838, 38, 834, 55], [839, 12, 834, 56, "style"], [839, 17, 834, 61], [839, 19, 834, 63, "styles"], [839, 25, 834, 69], [839, 26, 834, 70, "primaryButton"], [839, 39, 834, 84], [840, 12, 834, 84, "children"], [840, 20, 834, 84], [840, 35, 835, 12], [840, 39, 835, 12, "_jsxDevRuntime"], [840, 53, 835, 12], [840, 54, 835, 12, "jsxDEV"], [840, 60, 835, 12], [840, 62, 835, 13, "_Text"], [840, 67, 835, 13], [840, 68, 835, 13, "default"], [840, 75, 835, 17], [841, 14, 835, 18, "style"], [841, 19, 835, 23], [841, 21, 835, 25, "styles"], [841, 27, 835, 31], [841, 28, 835, 32, "primaryButtonText"], [841, 45, 835, 50], [842, 14, 835, 50, "children"], [842, 22, 835, 50], [842, 24, 835, 51], [843, 12, 835, 67], [844, 14, 835, 67, "fileName"], [844, 22, 835, 67], [844, 24, 835, 67, "_jsxFileName"], [844, 36, 835, 67], [845, 14, 835, 67, "lineNumber"], [845, 24, 835, 67], [846, 14, 835, 67, "columnNumber"], [846, 26, 835, 67], [847, 12, 835, 67], [847, 19, 835, 73], [848, 10, 835, 74], [849, 12, 835, 74, "fileName"], [849, 20, 835, 74], [849, 22, 835, 74, "_jsxFileName"], [849, 34, 835, 74], [850, 12, 835, 74, "lineNumber"], [850, 22, 835, 74], [851, 12, 835, 74, "columnNumber"], [851, 24, 835, 74], [852, 10, 835, 74], [852, 17, 836, 28], [852, 18, 836, 29], [852, 33, 837, 10], [852, 37, 837, 10, "_jsxDevRuntime"], [852, 51, 837, 10], [852, 52, 837, 10, "jsxDEV"], [852, 58, 837, 10], [852, 60, 837, 11, "_TouchableOpacity"], [852, 77, 837, 11], [852, 78, 837, 11, "default"], [852, 85, 837, 27], [853, 12, 837, 28, "onPress"], [853, 19, 837, 35], [853, 21, 837, 37, "onCancel"], [853, 29, 837, 46], [854, 12, 837, 47, "style"], [854, 17, 837, 52], [854, 19, 837, 54, "styles"], [854, 25, 837, 60], [854, 26, 837, 61, "secondaryButton"], [854, 41, 837, 77], [855, 12, 837, 77, "children"], [855, 20, 837, 77], [855, 35, 838, 12], [855, 39, 838, 12, "_jsxDevRuntime"], [855, 53, 838, 12], [855, 54, 838, 12, "jsxDEV"], [855, 60, 838, 12], [855, 62, 838, 13, "_Text"], [855, 67, 838, 13], [855, 68, 838, 13, "default"], [855, 75, 838, 17], [856, 14, 838, 18, "style"], [856, 19, 838, 23], [856, 21, 838, 25, "styles"], [856, 27, 838, 31], [856, 28, 838, 32, "secondaryButtonText"], [856, 47, 838, 52], [857, 14, 838, 52, "children"], [857, 22, 838, 52], [857, 24, 838, 53], [858, 12, 838, 59], [859, 14, 838, 59, "fileName"], [859, 22, 838, 59], [859, 24, 838, 59, "_jsxFileName"], [859, 36, 838, 59], [860, 14, 838, 59, "lineNumber"], [860, 24, 838, 59], [861, 14, 838, 59, "columnNumber"], [861, 26, 838, 59], [862, 12, 838, 59], [862, 19, 838, 65], [863, 10, 838, 66], [864, 12, 838, 66, "fileName"], [864, 20, 838, 66], [864, 22, 838, 66, "_jsxFileName"], [864, 34, 838, 66], [865, 12, 838, 66, "lineNumber"], [865, 22, 838, 66], [866, 12, 838, 66, "columnNumber"], [866, 24, 838, 66], [867, 10, 838, 66], [867, 17, 839, 28], [867, 18, 839, 29], [868, 8, 839, 29], [869, 10, 839, 29, "fileName"], [869, 18, 839, 29], [869, 20, 839, 29, "_jsxFileName"], [869, 32, 839, 29], [870, 10, 839, 29, "lineNumber"], [870, 20, 839, 29], [871, 10, 839, 29, "columnNumber"], [871, 22, 839, 29], [872, 8, 839, 29], [872, 15, 840, 14], [873, 6, 840, 15], [874, 8, 840, 15, "fileName"], [874, 16, 840, 15], [874, 18, 840, 15, "_jsxFileName"], [874, 30, 840, 15], [875, 8, 840, 15, "lineNumber"], [875, 18, 840, 15], [876, 8, 840, 15, "columnNumber"], [876, 20, 840, 15], [877, 6, 840, 15], [877, 13, 841, 12], [877, 14, 841, 13], [878, 4, 843, 2], [879, 4, 844, 2], [880, 4, 845, 2, "console"], [880, 11, 845, 9], [880, 12, 845, 10, "log"], [880, 15, 845, 13], [880, 16, 845, 14], [880, 55, 845, 53], [880, 56, 845, 54], [881, 4, 847, 2], [881, 24, 848, 4], [881, 28, 848, 4, "_jsxDevRuntime"], [881, 42, 848, 4], [881, 43, 848, 4, "jsxDEV"], [881, 49, 848, 4], [881, 51, 848, 5, "_View"], [881, 56, 848, 5], [881, 57, 848, 5, "default"], [881, 64, 848, 9], [882, 6, 848, 10, "style"], [882, 11, 848, 15], [882, 13, 848, 17, "styles"], [882, 19, 848, 23], [882, 20, 848, 24, "container"], [882, 29, 848, 34], [883, 6, 848, 34, "children"], [883, 14, 848, 34], [883, 30, 850, 6], [883, 34, 850, 6, "_jsxDevRuntime"], [883, 48, 850, 6], [883, 49, 850, 6, "jsxDEV"], [883, 55, 850, 6], [883, 57, 850, 7, "_View"], [883, 62, 850, 7], [883, 63, 850, 7, "default"], [883, 70, 850, 11], [884, 8, 850, 12, "style"], [884, 13, 850, 17], [884, 15, 850, 19, "styles"], [884, 21, 850, 25], [884, 22, 850, 26, "cameraContainer"], [884, 37, 850, 42], [885, 8, 850, 43, "id"], [885, 10, 850, 45], [885, 12, 850, 46], [885, 29, 850, 63], [886, 8, 850, 63, "children"], [886, 16, 850, 63], [886, 32, 851, 8], [886, 36, 851, 8, "_jsxDevRuntime"], [886, 50, 851, 8], [886, 51, 851, 8, "jsxDEV"], [886, 57, 851, 8], [886, 59, 851, 9, "_expoCamera"], [886, 70, 851, 9], [886, 71, 851, 9, "CameraView"], [886, 81, 851, 19], [887, 10, 852, 10, "ref"], [887, 13, 852, 13], [887, 15, 852, 15, "cameraRef"], [887, 24, 852, 25], [888, 10, 853, 10, "style"], [888, 15, 853, 15], [888, 17, 853, 17], [888, 18, 853, 18, "styles"], [888, 24, 853, 24], [888, 25, 853, 25, "camera"], [888, 31, 853, 31], [888, 33, 853, 33], [889, 12, 853, 35, "backgroundColor"], [889, 27, 853, 50], [889, 29, 853, 52], [890, 10, 853, 62], [890, 11, 853, 63], [890, 12, 853, 65], [891, 10, 854, 10, "facing"], [891, 16, 854, 16], [891, 18, 854, 17], [891, 24, 854, 23], [892, 10, 855, 10, "onLayout"], [892, 18, 855, 18], [892, 20, 855, 21, "e"], [892, 21, 855, 22], [892, 25, 855, 27], [893, 12, 856, 12, "console"], [893, 19, 856, 19], [893, 20, 856, 20, "log"], [893, 23, 856, 23], [893, 24, 856, 24], [893, 56, 856, 56], [893, 58, 856, 58, "e"], [893, 59, 856, 59], [893, 60, 856, 60, "nativeEvent"], [893, 71, 856, 71], [893, 72, 856, 72, "layout"], [893, 78, 856, 78], [893, 79, 856, 79], [894, 12, 857, 12, "setViewSize"], [894, 23, 857, 23], [894, 24, 857, 24], [895, 14, 857, 26, "width"], [895, 19, 857, 31], [895, 21, 857, 33, "e"], [895, 22, 857, 34], [895, 23, 857, 35, "nativeEvent"], [895, 34, 857, 46], [895, 35, 857, 47, "layout"], [895, 41, 857, 53], [895, 42, 857, 54, "width"], [895, 47, 857, 59], [896, 14, 857, 61, "height"], [896, 20, 857, 67], [896, 22, 857, 69, "e"], [896, 23, 857, 70], [896, 24, 857, 71, "nativeEvent"], [896, 35, 857, 82], [896, 36, 857, 83, "layout"], [896, 42, 857, 89], [896, 43, 857, 90, "height"], [897, 12, 857, 97], [897, 13, 857, 98], [897, 14, 857, 99], [898, 10, 858, 10], [898, 11, 858, 12], [899, 10, 859, 10, "onCameraReady"], [899, 23, 859, 23], [899, 25, 859, 25, "onCameraReady"], [899, 26, 859, 25], [899, 31, 859, 31], [900, 12, 860, 12, "console"], [900, 19, 860, 19], [900, 20, 860, 20, "log"], [900, 23, 860, 23], [900, 24, 860, 24], [900, 55, 860, 55], [900, 56, 860, 56], [901, 12, 861, 12, "setIsCameraReady"], [901, 28, 861, 28], [901, 29, 861, 29], [901, 33, 861, 33], [901, 34, 861, 34], [901, 35, 861, 35], [901, 36, 861, 36], [902, 10, 862, 10], [902, 11, 862, 12], [903, 10, 863, 10, "onMountError"], [903, 22, 863, 22], [903, 24, 863, 25, "error"], [903, 29, 863, 30], [903, 33, 863, 35], [904, 12, 864, 12, "console"], [904, 19, 864, 19], [904, 20, 864, 20, "error"], [904, 25, 864, 25], [904, 26, 864, 26], [904, 63, 864, 63], [904, 65, 864, 65, "error"], [904, 70, 864, 70], [904, 71, 864, 71], [905, 12, 865, 12, "setErrorMessage"], [905, 27, 865, 27], [905, 28, 865, 28], [905, 57, 865, 57], [905, 58, 865, 58], [906, 12, 866, 12, "setProcessingState"], [906, 30, 866, 30], [906, 31, 866, 31], [906, 38, 866, 38], [906, 39, 866, 39], [907, 10, 867, 10], [908, 8, 867, 12], [909, 10, 867, 12, "fileName"], [909, 18, 867, 12], [909, 20, 867, 12, "_jsxFileName"], [909, 32, 867, 12], [910, 10, 867, 12, "lineNumber"], [910, 20, 867, 12], [911, 10, 867, 12, "columnNumber"], [911, 22, 867, 12], [912, 8, 867, 12], [912, 15, 868, 9], [912, 16, 868, 10], [912, 18, 870, 9], [912, 19, 870, 10, "isCameraReady"], [912, 32, 870, 23], [912, 49, 871, 10], [912, 53, 871, 10, "_jsxDevRuntime"], [912, 67, 871, 10], [912, 68, 871, 10, "jsxDEV"], [912, 74, 871, 10], [912, 76, 871, 11, "_View"], [912, 81, 871, 11], [912, 82, 871, 11, "default"], [912, 89, 871, 15], [913, 10, 871, 16, "style"], [913, 15, 871, 21], [913, 17, 871, 23], [913, 18, 871, 24, "StyleSheet"], [913, 37, 871, 34], [913, 38, 871, 35, "absoluteFill"], [913, 50, 871, 47], [913, 52, 871, 49], [914, 12, 871, 51, "backgroundColor"], [914, 27, 871, 66], [914, 29, 871, 68], [914, 49, 871, 88], [915, 12, 871, 90, "justifyContent"], [915, 26, 871, 104], [915, 28, 871, 106], [915, 36, 871, 114], [916, 12, 871, 116, "alignItems"], [916, 22, 871, 126], [916, 24, 871, 128], [916, 32, 871, 136], [917, 12, 871, 138, "zIndex"], [917, 18, 871, 144], [917, 20, 871, 146], [918, 10, 871, 151], [918, 11, 871, 152], [918, 12, 871, 154], [919, 10, 871, 154, "children"], [919, 18, 871, 154], [919, 33, 872, 12], [919, 37, 872, 12, "_jsxDevRuntime"], [919, 51, 872, 12], [919, 52, 872, 12, "jsxDEV"], [919, 58, 872, 12], [919, 60, 872, 13, "_View"], [919, 65, 872, 13], [919, 66, 872, 13, "default"], [919, 73, 872, 17], [920, 12, 872, 18, "style"], [920, 17, 872, 23], [920, 19, 872, 25], [921, 14, 872, 27, "backgroundColor"], [921, 29, 872, 42], [921, 31, 872, 44], [921, 51, 872, 64], [922, 14, 872, 66, "padding"], [922, 21, 872, 73], [922, 23, 872, 75], [922, 25, 872, 77], [923, 14, 872, 79, "borderRadius"], [923, 26, 872, 91], [923, 28, 872, 93], [923, 30, 872, 95], [924, 14, 872, 97, "alignItems"], [924, 24, 872, 107], [924, 26, 872, 109], [925, 12, 872, 118], [925, 13, 872, 120], [926, 12, 872, 120, "children"], [926, 20, 872, 120], [926, 36, 873, 14], [926, 40, 873, 14, "_jsxDevRuntime"], [926, 54, 873, 14], [926, 55, 873, 14, "jsxDEV"], [926, 61, 873, 14], [926, 63, 873, 15, "_ActivityIndicator"], [926, 81, 873, 15], [926, 82, 873, 15, "default"], [926, 89, 873, 32], [927, 14, 873, 33, "size"], [927, 18, 873, 37], [927, 20, 873, 38], [927, 27, 873, 45], [928, 14, 873, 46, "color"], [928, 19, 873, 51], [928, 21, 873, 52], [928, 30, 873, 61], [929, 14, 873, 62, "style"], [929, 19, 873, 67], [929, 21, 873, 69], [930, 16, 873, 71, "marginBottom"], [930, 28, 873, 83], [930, 30, 873, 85], [931, 14, 873, 88], [932, 12, 873, 90], [933, 14, 873, 90, "fileName"], [933, 22, 873, 90], [933, 24, 873, 90, "_jsxFileName"], [933, 36, 873, 90], [934, 14, 873, 90, "lineNumber"], [934, 24, 873, 90], [935, 14, 873, 90, "columnNumber"], [935, 26, 873, 90], [936, 12, 873, 90], [936, 19, 873, 92], [936, 20, 873, 93], [936, 35, 874, 14], [936, 39, 874, 14, "_jsxDevRuntime"], [936, 53, 874, 14], [936, 54, 874, 14, "jsxDEV"], [936, 60, 874, 14], [936, 62, 874, 15, "_Text"], [936, 67, 874, 15], [936, 68, 874, 15, "default"], [936, 75, 874, 19], [937, 14, 874, 20, "style"], [937, 19, 874, 25], [937, 21, 874, 27], [938, 16, 874, 29, "color"], [938, 21, 874, 34], [938, 23, 874, 36], [938, 29, 874, 42], [939, 16, 874, 44, "fontSize"], [939, 24, 874, 52], [939, 26, 874, 54], [939, 28, 874, 56], [940, 16, 874, 58, "fontWeight"], [940, 26, 874, 68], [940, 28, 874, 70], [941, 14, 874, 76], [941, 15, 874, 78], [942, 14, 874, 78, "children"], [942, 22, 874, 78], [942, 24, 874, 79], [943, 12, 874, 101], [944, 14, 874, 101, "fileName"], [944, 22, 874, 101], [944, 24, 874, 101, "_jsxFileName"], [944, 36, 874, 101], [945, 14, 874, 101, "lineNumber"], [945, 24, 874, 101], [946, 14, 874, 101, "columnNumber"], [946, 26, 874, 101], [947, 12, 874, 101], [947, 19, 874, 107], [947, 20, 874, 108], [947, 35, 875, 14], [947, 39, 875, 14, "_jsxDevRuntime"], [947, 53, 875, 14], [947, 54, 875, 14, "jsxDEV"], [947, 60, 875, 14], [947, 62, 875, 15, "_Text"], [947, 67, 875, 15], [947, 68, 875, 15, "default"], [947, 75, 875, 19], [948, 14, 875, 20, "style"], [948, 19, 875, 25], [948, 21, 875, 27], [949, 16, 875, 29, "color"], [949, 21, 875, 34], [949, 23, 875, 36], [949, 32, 875, 45], [950, 16, 875, 47, "fontSize"], [950, 24, 875, 55], [950, 26, 875, 57], [950, 28, 875, 59], [951, 16, 875, 61, "marginTop"], [951, 25, 875, 70], [951, 27, 875, 72], [952, 14, 875, 74], [952, 15, 875, 76], [953, 14, 875, 76, "children"], [953, 22, 875, 76], [953, 24, 875, 77], [954, 12, 875, 88], [955, 14, 875, 88, "fileName"], [955, 22, 875, 88], [955, 24, 875, 88, "_jsxFileName"], [955, 36, 875, 88], [956, 14, 875, 88, "lineNumber"], [956, 24, 875, 88], [957, 14, 875, 88, "columnNumber"], [957, 26, 875, 88], [958, 12, 875, 88], [958, 19, 875, 94], [958, 20, 875, 95], [959, 10, 875, 95], [960, 12, 875, 95, "fileName"], [960, 20, 875, 95], [960, 22, 875, 95, "_jsxFileName"], [960, 34, 875, 95], [961, 12, 875, 95, "lineNumber"], [961, 22, 875, 95], [962, 12, 875, 95, "columnNumber"], [962, 24, 875, 95], [963, 10, 875, 95], [963, 17, 876, 18], [964, 8, 876, 19], [965, 10, 876, 19, "fileName"], [965, 18, 876, 19], [965, 20, 876, 19, "_jsxFileName"], [965, 32, 876, 19], [966, 10, 876, 19, "lineNumber"], [966, 20, 876, 19], [967, 10, 876, 19, "columnNumber"], [967, 22, 876, 19], [968, 8, 876, 19], [968, 15, 877, 16], [968, 16, 878, 9], [968, 18, 881, 9, "isCameraReady"], [968, 31, 881, 22], [968, 35, 881, 26, "previewBlurEnabled"], [968, 53, 881, 44], [968, 57, 881, 48, "viewSize"], [968, 65, 881, 56], [968, 66, 881, 57, "width"], [968, 71, 881, 62], [968, 74, 881, 65], [968, 75, 881, 66], [968, 92, 882, 10], [968, 96, 882, 10, "_jsxDevRuntime"], [968, 110, 882, 10], [968, 111, 882, 10, "jsxDEV"], [968, 117, 882, 10], [968, 119, 882, 10, "_jsxDevRuntime"], [968, 133, 882, 10], [968, 134, 882, 10, "Fragment"], [968, 142, 882, 10], [969, 10, 882, 10, "children"], [969, 18, 882, 10], [969, 34, 884, 12], [969, 38, 884, 12, "_jsxDevRuntime"], [969, 52, 884, 12], [969, 53, 884, 12, "jsxDEV"], [969, 59, 884, 12], [969, 61, 884, 13, "_LiveFaceCanvas"], [969, 76, 884, 13], [969, 77, 884, 13, "default"], [969, 84, 884, 27], [970, 12, 884, 28, "containerId"], [970, 23, 884, 39], [970, 25, 884, 40], [970, 42, 884, 57], [971, 12, 884, 58, "width"], [971, 17, 884, 63], [971, 19, 884, 65, "viewSize"], [971, 27, 884, 73], [971, 28, 884, 74, "width"], [971, 33, 884, 80], [972, 12, 884, 81, "height"], [972, 18, 884, 87], [972, 20, 884, 89, "viewSize"], [972, 28, 884, 97], [972, 29, 884, 98, "height"], [973, 10, 884, 105], [974, 12, 884, 105, "fileName"], [974, 20, 884, 105], [974, 22, 884, 105, "_jsxFileName"], [974, 34, 884, 105], [975, 12, 884, 105, "lineNumber"], [975, 22, 884, 105], [976, 12, 884, 105, "columnNumber"], [976, 24, 884, 105], [977, 10, 884, 105], [977, 17, 884, 107], [977, 18, 884, 108], [977, 33, 885, 12], [977, 37, 885, 12, "_jsxDevRuntime"], [977, 51, 885, 12], [977, 52, 885, 12, "jsxDEV"], [977, 58, 885, 12], [977, 60, 885, 13, "_View"], [977, 65, 885, 13], [977, 66, 885, 13, "default"], [977, 73, 885, 17], [978, 12, 885, 18, "style"], [978, 17, 885, 23], [978, 19, 885, 25], [978, 20, 885, 26, "StyleSheet"], [978, 39, 885, 36], [978, 40, 885, 37, "absoluteFill"], [978, 52, 885, 49], [978, 54, 885, 51], [979, 14, 885, 53, "pointerEvents"], [979, 27, 885, 66], [979, 29, 885, 68], [980, 12, 885, 75], [980, 13, 885, 76], [980, 14, 885, 78], [981, 12, 885, 78, "children"], [981, 20, 885, 78], [981, 36, 887, 12], [981, 40, 887, 12, "_jsxDevRuntime"], [981, 54, 887, 12], [981, 55, 887, 12, "jsxDEV"], [981, 61, 887, 12], [981, 63, 887, 13, "_expoBlur"], [981, 72, 887, 13], [981, 73, 887, 13, "BlurView"], [981, 81, 887, 21], [982, 14, 887, 22, "intensity"], [982, 23, 887, 31], [982, 25, 887, 33], [982, 27, 887, 36], [983, 14, 887, 37, "tint"], [983, 18, 887, 41], [983, 20, 887, 42], [983, 26, 887, 48], [984, 14, 887, 49, "style"], [984, 19, 887, 54], [984, 21, 887, 56], [984, 22, 887, 57, "styles"], [984, 28, 887, 63], [984, 29, 887, 64, "blurZone"], [984, 37, 887, 72], [984, 39, 887, 74], [985, 16, 888, 14, "left"], [985, 20, 888, 18], [985, 22, 888, 20], [985, 23, 888, 21], [986, 16, 889, 14, "top"], [986, 19, 889, 17], [986, 21, 889, 19, "viewSize"], [986, 29, 889, 27], [986, 30, 889, 28, "height"], [986, 36, 889, 34], [986, 39, 889, 37], [986, 42, 889, 40], [987, 16, 890, 14, "width"], [987, 21, 890, 19], [987, 23, 890, 21, "viewSize"], [987, 31, 890, 29], [987, 32, 890, 30, "width"], [987, 37, 890, 35], [988, 16, 891, 14, "height"], [988, 22, 891, 20], [988, 24, 891, 22, "viewSize"], [988, 32, 891, 30], [988, 33, 891, 31, "height"], [988, 39, 891, 37], [988, 42, 891, 40], [988, 46, 891, 44], [989, 16, 892, 14, "borderRadius"], [989, 28, 892, 26], [989, 30, 892, 28], [990, 14, 893, 12], [990, 15, 893, 13], [991, 12, 893, 15], [992, 14, 893, 15, "fileName"], [992, 22, 893, 15], [992, 24, 893, 15, "_jsxFileName"], [992, 36, 893, 15], [993, 14, 893, 15, "lineNumber"], [993, 24, 893, 15], [994, 14, 893, 15, "columnNumber"], [994, 26, 893, 15], [995, 12, 893, 15], [995, 19, 893, 17], [995, 20, 893, 18], [995, 35, 895, 12], [995, 39, 895, 12, "_jsxDevRuntime"], [995, 53, 895, 12], [995, 54, 895, 12, "jsxDEV"], [995, 60, 895, 12], [995, 62, 895, 13, "_expoBlur"], [995, 71, 895, 13], [995, 72, 895, 13, "BlurView"], [995, 80, 895, 21], [996, 14, 895, 22, "intensity"], [996, 23, 895, 31], [996, 25, 895, 33], [996, 27, 895, 36], [997, 14, 895, 37, "tint"], [997, 18, 895, 41], [997, 20, 895, 42], [997, 26, 895, 48], [998, 14, 895, 49, "style"], [998, 19, 895, 54], [998, 21, 895, 56], [998, 22, 895, 57, "styles"], [998, 28, 895, 63], [998, 29, 895, 64, "blurZone"], [998, 37, 895, 72], [998, 39, 895, 74], [999, 16, 896, 14, "left"], [999, 20, 896, 18], [999, 22, 896, 20], [999, 23, 896, 21], [1000, 16, 897, 14, "top"], [1000, 19, 897, 17], [1000, 21, 897, 19], [1000, 22, 897, 20], [1001, 16, 898, 14, "width"], [1001, 21, 898, 19], [1001, 23, 898, 21, "viewSize"], [1001, 31, 898, 29], [1001, 32, 898, 30, "width"], [1001, 37, 898, 35], [1002, 16, 899, 14, "height"], [1002, 22, 899, 20], [1002, 24, 899, 22, "viewSize"], [1002, 32, 899, 30], [1002, 33, 899, 31, "height"], [1002, 39, 899, 37], [1002, 42, 899, 40], [1002, 45, 899, 43], [1003, 16, 900, 14, "borderRadius"], [1003, 28, 900, 26], [1003, 30, 900, 28], [1004, 14, 901, 12], [1004, 15, 901, 13], [1005, 12, 901, 15], [1006, 14, 901, 15, "fileName"], [1006, 22, 901, 15], [1006, 24, 901, 15, "_jsxFileName"], [1006, 36, 901, 15], [1007, 14, 901, 15, "lineNumber"], [1007, 24, 901, 15], [1008, 14, 901, 15, "columnNumber"], [1008, 26, 901, 15], [1009, 12, 901, 15], [1009, 19, 901, 17], [1009, 20, 901, 18], [1009, 35, 903, 12], [1009, 39, 903, 12, "_jsxDevRuntime"], [1009, 53, 903, 12], [1009, 54, 903, 12, "jsxDEV"], [1009, 60, 903, 12], [1009, 62, 903, 13, "_expoBlur"], [1009, 71, 903, 13], [1009, 72, 903, 13, "BlurView"], [1009, 80, 903, 21], [1010, 14, 903, 22, "intensity"], [1010, 23, 903, 31], [1010, 25, 903, 33], [1010, 27, 903, 36], [1011, 14, 903, 37, "tint"], [1011, 18, 903, 41], [1011, 20, 903, 42], [1011, 26, 903, 48], [1012, 14, 903, 49, "style"], [1012, 19, 903, 54], [1012, 21, 903, 56], [1012, 22, 903, 57, "styles"], [1012, 28, 903, 63], [1012, 29, 903, 64, "blurZone"], [1012, 37, 903, 72], [1012, 39, 903, 74], [1013, 16, 904, 14, "left"], [1013, 20, 904, 18], [1013, 22, 904, 20, "viewSize"], [1013, 30, 904, 28], [1013, 31, 904, 29, "width"], [1013, 36, 904, 34], [1013, 39, 904, 37], [1013, 42, 904, 40], [1013, 45, 904, 44, "viewSize"], [1013, 53, 904, 52], [1013, 54, 904, 53, "width"], [1013, 59, 904, 58], [1013, 62, 904, 61], [1013, 66, 904, 66], [1014, 16, 905, 14, "top"], [1014, 19, 905, 17], [1014, 21, 905, 19, "viewSize"], [1014, 29, 905, 27], [1014, 30, 905, 28, "height"], [1014, 36, 905, 34], [1014, 39, 905, 37], [1014, 43, 905, 41], [1014, 46, 905, 45, "viewSize"], [1014, 54, 905, 53], [1014, 55, 905, 54, "width"], [1014, 60, 905, 59], [1014, 63, 905, 62], [1014, 67, 905, 67], [1015, 16, 906, 14, "width"], [1015, 21, 906, 19], [1015, 23, 906, 21, "viewSize"], [1015, 31, 906, 29], [1015, 32, 906, 30, "width"], [1015, 37, 906, 35], [1015, 40, 906, 38], [1015, 43, 906, 41], [1016, 16, 907, 14, "height"], [1016, 22, 907, 20], [1016, 24, 907, 22, "viewSize"], [1016, 32, 907, 30], [1016, 33, 907, 31, "width"], [1016, 38, 907, 36], [1016, 41, 907, 39], [1016, 44, 907, 42], [1017, 16, 908, 14, "borderRadius"], [1017, 28, 908, 26], [1017, 30, 908, 29, "viewSize"], [1017, 38, 908, 37], [1017, 39, 908, 38, "width"], [1017, 44, 908, 43], [1017, 47, 908, 46], [1017, 50, 908, 49], [1017, 53, 908, 53], [1018, 14, 909, 12], [1018, 15, 909, 13], [1019, 12, 909, 15], [1020, 14, 909, 15, "fileName"], [1020, 22, 909, 15], [1020, 24, 909, 15, "_jsxFileName"], [1020, 36, 909, 15], [1021, 14, 909, 15, "lineNumber"], [1021, 24, 909, 15], [1022, 14, 909, 15, "columnNumber"], [1022, 26, 909, 15], [1023, 12, 909, 15], [1023, 19, 909, 17], [1023, 20, 909, 18], [1023, 35, 910, 12], [1023, 39, 910, 12, "_jsxDevRuntime"], [1023, 53, 910, 12], [1023, 54, 910, 12, "jsxDEV"], [1023, 60, 910, 12], [1023, 62, 910, 13, "_expoBlur"], [1023, 71, 910, 13], [1023, 72, 910, 13, "BlurView"], [1023, 80, 910, 21], [1024, 14, 910, 22, "intensity"], [1024, 23, 910, 31], [1024, 25, 910, 33], [1024, 27, 910, 36], [1025, 14, 910, 37, "tint"], [1025, 18, 910, 41], [1025, 20, 910, 42], [1025, 26, 910, 48], [1026, 14, 910, 49, "style"], [1026, 19, 910, 54], [1026, 21, 910, 56], [1026, 22, 910, 57, "styles"], [1026, 28, 910, 63], [1026, 29, 910, 64, "blurZone"], [1026, 37, 910, 72], [1026, 39, 910, 74], [1027, 16, 911, 14, "left"], [1027, 20, 911, 18], [1027, 22, 911, 20, "viewSize"], [1027, 30, 911, 28], [1027, 31, 911, 29, "width"], [1027, 36, 911, 34], [1027, 39, 911, 37], [1027, 42, 911, 40], [1027, 45, 911, 44, "viewSize"], [1027, 53, 911, 52], [1027, 54, 911, 53, "width"], [1027, 59, 911, 58], [1027, 62, 911, 61], [1027, 66, 911, 66], [1028, 16, 912, 14, "top"], [1028, 19, 912, 17], [1028, 21, 912, 19, "viewSize"], [1028, 29, 912, 27], [1028, 30, 912, 28, "height"], [1028, 36, 912, 34], [1028, 39, 912, 37], [1028, 42, 912, 40], [1028, 45, 912, 44, "viewSize"], [1028, 53, 912, 52], [1028, 54, 912, 53, "width"], [1028, 59, 912, 58], [1028, 62, 912, 61], [1028, 66, 912, 66], [1029, 16, 913, 14, "width"], [1029, 21, 913, 19], [1029, 23, 913, 21, "viewSize"], [1029, 31, 913, 29], [1029, 32, 913, 30, "width"], [1029, 37, 913, 35], [1029, 40, 913, 38], [1029, 43, 913, 41], [1030, 16, 914, 14, "height"], [1030, 22, 914, 20], [1030, 24, 914, 22, "viewSize"], [1030, 32, 914, 30], [1030, 33, 914, 31, "width"], [1030, 38, 914, 36], [1030, 41, 914, 39], [1030, 44, 914, 42], [1031, 16, 915, 14, "borderRadius"], [1031, 28, 915, 26], [1031, 30, 915, 29, "viewSize"], [1031, 38, 915, 37], [1031, 39, 915, 38, "width"], [1031, 44, 915, 43], [1031, 47, 915, 46], [1031, 50, 915, 49], [1031, 53, 915, 53], [1032, 14, 916, 12], [1032, 15, 916, 13], [1033, 12, 916, 15], [1034, 14, 916, 15, "fileName"], [1034, 22, 916, 15], [1034, 24, 916, 15, "_jsxFileName"], [1034, 36, 916, 15], [1035, 14, 916, 15, "lineNumber"], [1035, 24, 916, 15], [1036, 14, 916, 15, "columnNumber"], [1036, 26, 916, 15], [1037, 12, 916, 15], [1037, 19, 916, 17], [1037, 20, 916, 18], [1037, 35, 917, 12], [1037, 39, 917, 12, "_jsxDevRuntime"], [1037, 53, 917, 12], [1037, 54, 917, 12, "jsxDEV"], [1037, 60, 917, 12], [1037, 62, 917, 13, "_expoBlur"], [1037, 71, 917, 13], [1037, 72, 917, 13, "BlurView"], [1037, 80, 917, 21], [1038, 14, 917, 22, "intensity"], [1038, 23, 917, 31], [1038, 25, 917, 33], [1038, 27, 917, 36], [1039, 14, 917, 37, "tint"], [1039, 18, 917, 41], [1039, 20, 917, 42], [1039, 26, 917, 48], [1040, 14, 917, 49, "style"], [1040, 19, 917, 54], [1040, 21, 917, 56], [1040, 22, 917, 57, "styles"], [1040, 28, 917, 63], [1040, 29, 917, 64, "blurZone"], [1040, 37, 917, 72], [1040, 39, 917, 74], [1041, 16, 918, 14, "left"], [1041, 20, 918, 18], [1041, 22, 918, 20, "viewSize"], [1041, 30, 918, 28], [1041, 31, 918, 29, "width"], [1041, 36, 918, 34], [1041, 39, 918, 37], [1041, 42, 918, 40], [1041, 45, 918, 44, "viewSize"], [1041, 53, 918, 52], [1041, 54, 918, 53, "width"], [1041, 59, 918, 58], [1041, 62, 918, 61], [1041, 66, 918, 66], [1042, 16, 919, 14, "top"], [1042, 19, 919, 17], [1042, 21, 919, 19, "viewSize"], [1042, 29, 919, 27], [1042, 30, 919, 28, "height"], [1042, 36, 919, 34], [1042, 39, 919, 37], [1042, 42, 919, 40], [1042, 45, 919, 44, "viewSize"], [1042, 53, 919, 52], [1042, 54, 919, 53, "width"], [1042, 59, 919, 58], [1042, 62, 919, 61], [1042, 66, 919, 66], [1043, 16, 920, 14, "width"], [1043, 21, 920, 19], [1043, 23, 920, 21, "viewSize"], [1043, 31, 920, 29], [1043, 32, 920, 30, "width"], [1043, 37, 920, 35], [1043, 40, 920, 38], [1043, 43, 920, 41], [1044, 16, 921, 14, "height"], [1044, 22, 921, 20], [1044, 24, 921, 22, "viewSize"], [1044, 32, 921, 30], [1044, 33, 921, 31, "width"], [1044, 38, 921, 36], [1044, 41, 921, 39], [1044, 44, 921, 42], [1045, 16, 922, 14, "borderRadius"], [1045, 28, 922, 26], [1045, 30, 922, 29, "viewSize"], [1045, 38, 922, 37], [1045, 39, 922, 38, "width"], [1045, 44, 922, 43], [1045, 47, 922, 46], [1045, 50, 922, 49], [1045, 53, 922, 53], [1046, 14, 923, 12], [1046, 15, 923, 13], [1047, 12, 923, 15], [1048, 14, 923, 15, "fileName"], [1048, 22, 923, 15], [1048, 24, 923, 15, "_jsxFileName"], [1048, 36, 923, 15], [1049, 14, 923, 15, "lineNumber"], [1049, 24, 923, 15], [1050, 14, 923, 15, "columnNumber"], [1050, 26, 923, 15], [1051, 12, 923, 15], [1051, 19, 923, 17], [1051, 20, 923, 18], [1051, 22, 925, 13, "__DEV__"], [1051, 29, 925, 20], [1051, 46, 926, 14], [1051, 50, 926, 14, "_jsxDevRuntime"], [1051, 64, 926, 14], [1051, 65, 926, 14, "jsxDEV"], [1051, 71, 926, 14], [1051, 73, 926, 15, "_View"], [1051, 78, 926, 15], [1051, 79, 926, 15, "default"], [1051, 86, 926, 19], [1052, 14, 926, 20, "style"], [1052, 19, 926, 25], [1052, 21, 926, 27, "styles"], [1052, 27, 926, 33], [1052, 28, 926, 34, "previewChip"], [1052, 39, 926, 46], [1053, 14, 926, 46, "children"], [1053, 22, 926, 46], [1053, 37, 927, 16], [1053, 41, 927, 16, "_jsxDevRuntime"], [1053, 55, 927, 16], [1053, 56, 927, 16, "jsxDEV"], [1053, 62, 927, 16], [1053, 64, 927, 17, "_Text"], [1053, 69, 927, 17], [1053, 70, 927, 17, "default"], [1053, 77, 927, 21], [1054, 16, 927, 22, "style"], [1054, 21, 927, 27], [1054, 23, 927, 29, "styles"], [1054, 29, 927, 35], [1054, 30, 927, 36, "previewChipText"], [1054, 45, 927, 52], [1055, 16, 927, 52, "children"], [1055, 24, 927, 52], [1055, 26, 927, 53], [1056, 14, 927, 73], [1057, 16, 927, 73, "fileName"], [1057, 24, 927, 73], [1057, 26, 927, 73, "_jsxFileName"], [1057, 38, 927, 73], [1058, 16, 927, 73, "lineNumber"], [1058, 26, 927, 73], [1059, 16, 927, 73, "columnNumber"], [1059, 28, 927, 73], [1060, 14, 927, 73], [1060, 21, 927, 79], [1061, 12, 927, 80], [1062, 14, 927, 80, "fileName"], [1062, 22, 927, 80], [1062, 24, 927, 80, "_jsxFileName"], [1062, 36, 927, 80], [1063, 14, 927, 80, "lineNumber"], [1063, 24, 927, 80], [1064, 14, 927, 80, "columnNumber"], [1064, 26, 927, 80], [1065, 12, 927, 80], [1065, 19, 928, 20], [1065, 20, 929, 13], [1066, 10, 929, 13], [1067, 12, 929, 13, "fileName"], [1067, 20, 929, 13], [1067, 22, 929, 13, "_jsxFileName"], [1067, 34, 929, 13], [1068, 12, 929, 13, "lineNumber"], [1068, 22, 929, 13], [1069, 12, 929, 13, "columnNumber"], [1069, 24, 929, 13], [1070, 10, 929, 13], [1070, 17, 930, 18], [1070, 18, 930, 19], [1071, 8, 930, 19], [1071, 23, 931, 12], [1071, 24, 932, 9], [1071, 26, 934, 9, "isCameraReady"], [1071, 39, 934, 22], [1071, 56, 935, 10], [1071, 60, 935, 10, "_jsxDevRuntime"], [1071, 74, 935, 10], [1071, 75, 935, 10, "jsxDEV"], [1071, 81, 935, 10], [1071, 83, 935, 10, "_jsxDevRuntime"], [1071, 97, 935, 10], [1071, 98, 935, 10, "Fragment"], [1071, 106, 935, 10], [1072, 10, 935, 10, "children"], [1072, 18, 935, 10], [1072, 34, 937, 12], [1072, 38, 937, 12, "_jsxDevRuntime"], [1072, 52, 937, 12], [1072, 53, 937, 12, "jsxDEV"], [1072, 59, 937, 12], [1072, 61, 937, 13, "_View"], [1072, 66, 937, 13], [1072, 67, 937, 13, "default"], [1072, 74, 937, 17], [1073, 12, 937, 18, "style"], [1073, 17, 937, 23], [1073, 19, 937, 25, "styles"], [1073, 25, 937, 31], [1073, 26, 937, 32, "headerOverlay"], [1073, 39, 937, 46], [1074, 12, 937, 46, "children"], [1074, 20, 937, 46], [1074, 35, 938, 14], [1074, 39, 938, 14, "_jsxDevRuntime"], [1074, 53, 938, 14], [1074, 54, 938, 14, "jsxDEV"], [1074, 60, 938, 14], [1074, 62, 938, 15, "_View"], [1074, 67, 938, 15], [1074, 68, 938, 15, "default"], [1074, 75, 938, 19], [1075, 14, 938, 20, "style"], [1075, 19, 938, 25], [1075, 21, 938, 27, "styles"], [1075, 27, 938, 33], [1075, 28, 938, 34, "headerContent"], [1075, 41, 938, 48], [1076, 14, 938, 48, "children"], [1076, 22, 938, 48], [1076, 38, 939, 16], [1076, 42, 939, 16, "_jsxDevRuntime"], [1076, 56, 939, 16], [1076, 57, 939, 16, "jsxDEV"], [1076, 63, 939, 16], [1076, 65, 939, 17, "_View"], [1076, 70, 939, 17], [1076, 71, 939, 17, "default"], [1076, 78, 939, 21], [1077, 16, 939, 22, "style"], [1077, 21, 939, 27], [1077, 23, 939, 29, "styles"], [1077, 29, 939, 35], [1077, 30, 939, 36, "headerLeft"], [1077, 40, 939, 47], [1078, 16, 939, 47, "children"], [1078, 24, 939, 47], [1078, 40, 940, 18], [1078, 44, 940, 18, "_jsxDevRuntime"], [1078, 58, 940, 18], [1078, 59, 940, 18, "jsxDEV"], [1078, 65, 940, 18], [1078, 67, 940, 19, "_Text"], [1078, 72, 940, 19], [1078, 73, 940, 19, "default"], [1078, 80, 940, 23], [1079, 18, 940, 24, "style"], [1079, 23, 940, 29], [1079, 25, 940, 31, "styles"], [1079, 31, 940, 37], [1079, 32, 940, 38, "headerTitle"], [1079, 43, 940, 50], [1080, 18, 940, 50, "children"], [1080, 26, 940, 50], [1080, 28, 940, 51], [1081, 16, 940, 62], [1082, 18, 940, 62, "fileName"], [1082, 26, 940, 62], [1082, 28, 940, 62, "_jsxFileName"], [1082, 40, 940, 62], [1083, 18, 940, 62, "lineNumber"], [1083, 28, 940, 62], [1084, 18, 940, 62, "columnNumber"], [1084, 30, 940, 62], [1085, 16, 940, 62], [1085, 23, 940, 68], [1085, 24, 940, 69], [1085, 39, 941, 18], [1085, 43, 941, 18, "_jsxDevRuntime"], [1085, 57, 941, 18], [1085, 58, 941, 18, "jsxDEV"], [1085, 64, 941, 18], [1085, 66, 941, 19, "_View"], [1085, 71, 941, 19], [1085, 72, 941, 19, "default"], [1085, 79, 941, 23], [1086, 18, 941, 24, "style"], [1086, 23, 941, 29], [1086, 25, 941, 31, "styles"], [1086, 31, 941, 37], [1086, 32, 941, 38, "subtitleRow"], [1086, 43, 941, 50], [1087, 18, 941, 50, "children"], [1087, 26, 941, 50], [1087, 42, 942, 20], [1087, 46, 942, 20, "_jsxDevRuntime"], [1087, 60, 942, 20], [1087, 61, 942, 20, "jsxDEV"], [1087, 67, 942, 20], [1087, 69, 942, 21, "_Text"], [1087, 74, 942, 21], [1087, 75, 942, 21, "default"], [1087, 82, 942, 25], [1088, 20, 942, 26, "style"], [1088, 25, 942, 31], [1088, 27, 942, 33, "styles"], [1088, 33, 942, 39], [1088, 34, 942, 40, "webIcon"], [1088, 41, 942, 48], [1089, 20, 942, 48, "children"], [1089, 28, 942, 48], [1089, 30, 942, 49], [1090, 18, 942, 51], [1091, 20, 942, 51, "fileName"], [1091, 28, 942, 51], [1091, 30, 942, 51, "_jsxFileName"], [1091, 42, 942, 51], [1092, 20, 942, 51, "lineNumber"], [1092, 30, 942, 51], [1093, 20, 942, 51, "columnNumber"], [1093, 32, 942, 51], [1094, 18, 942, 51], [1094, 25, 942, 57], [1094, 26, 942, 58], [1094, 41, 943, 20], [1094, 45, 943, 20, "_jsxDevRuntime"], [1094, 59, 943, 20], [1094, 60, 943, 20, "jsxDEV"], [1094, 66, 943, 20], [1094, 68, 943, 21, "_Text"], [1094, 73, 943, 21], [1094, 74, 943, 21, "default"], [1094, 81, 943, 25], [1095, 20, 943, 26, "style"], [1095, 25, 943, 31], [1095, 27, 943, 33, "styles"], [1095, 33, 943, 39], [1095, 34, 943, 40, "headerSubtitle"], [1095, 48, 943, 55], [1096, 20, 943, 55, "children"], [1096, 28, 943, 55], [1096, 30, 943, 56], [1097, 18, 943, 71], [1098, 20, 943, 71, "fileName"], [1098, 28, 943, 71], [1098, 30, 943, 71, "_jsxFileName"], [1098, 42, 943, 71], [1099, 20, 943, 71, "lineNumber"], [1099, 30, 943, 71], [1100, 20, 943, 71, "columnNumber"], [1100, 32, 943, 71], [1101, 18, 943, 71], [1101, 25, 943, 77], [1101, 26, 943, 78], [1102, 16, 943, 78], [1103, 18, 943, 78, "fileName"], [1103, 26, 943, 78], [1103, 28, 943, 78, "_jsxFileName"], [1103, 40, 943, 78], [1104, 18, 943, 78, "lineNumber"], [1104, 28, 943, 78], [1105, 18, 943, 78, "columnNumber"], [1105, 30, 943, 78], [1106, 16, 943, 78], [1106, 23, 944, 24], [1106, 24, 944, 25], [1106, 26, 945, 19, "challengeCode"], [1106, 39, 945, 32], [1106, 56, 946, 20], [1106, 60, 946, 20, "_jsxDevRuntime"], [1106, 74, 946, 20], [1106, 75, 946, 20, "jsxDEV"], [1106, 81, 946, 20], [1106, 83, 946, 21, "_View"], [1106, 88, 946, 21], [1106, 89, 946, 21, "default"], [1106, 96, 946, 25], [1107, 18, 946, 26, "style"], [1107, 23, 946, 31], [1107, 25, 946, 33, "styles"], [1107, 31, 946, 39], [1107, 32, 946, 40, "challengeRow"], [1107, 44, 946, 53], [1108, 18, 946, 53, "children"], [1108, 26, 946, 53], [1108, 42, 947, 22], [1108, 46, 947, 22, "_jsxDevRuntime"], [1108, 60, 947, 22], [1108, 61, 947, 22, "jsxDEV"], [1108, 67, 947, 22], [1108, 69, 947, 23, "_lucideReactNative"], [1108, 87, 947, 23], [1108, 88, 947, 23, "Shield"], [1108, 94, 947, 29], [1109, 20, 947, 30, "size"], [1109, 24, 947, 34], [1109, 26, 947, 36], [1109, 28, 947, 39], [1110, 20, 947, 40, "color"], [1110, 25, 947, 45], [1110, 27, 947, 46], [1111, 18, 947, 52], [1112, 20, 947, 52, "fileName"], [1112, 28, 947, 52], [1112, 30, 947, 52, "_jsxFileName"], [1112, 42, 947, 52], [1113, 20, 947, 52, "lineNumber"], [1113, 30, 947, 52], [1114, 20, 947, 52, "columnNumber"], [1114, 32, 947, 52], [1115, 18, 947, 52], [1115, 25, 947, 54], [1115, 26, 947, 55], [1115, 41, 948, 22], [1115, 45, 948, 22, "_jsxDevRuntime"], [1115, 59, 948, 22], [1115, 60, 948, 22, "jsxDEV"], [1115, 66, 948, 22], [1115, 68, 948, 23, "_Text"], [1115, 73, 948, 23], [1115, 74, 948, 23, "default"], [1115, 81, 948, 27], [1116, 20, 948, 28, "style"], [1116, 25, 948, 33], [1116, 27, 948, 35, "styles"], [1116, 33, 948, 41], [1116, 34, 948, 42, "challengeCode"], [1116, 47, 948, 56], [1117, 20, 948, 56, "children"], [1117, 28, 948, 56], [1117, 30, 948, 58, "challengeCode"], [1118, 18, 948, 71], [1119, 20, 948, 71, "fileName"], [1119, 28, 948, 71], [1119, 30, 948, 71, "_jsxFileName"], [1119, 42, 948, 71], [1120, 20, 948, 71, "lineNumber"], [1120, 30, 948, 71], [1121, 20, 948, 71, "columnNumber"], [1121, 32, 948, 71], [1122, 18, 948, 71], [1122, 25, 948, 78], [1122, 26, 948, 79], [1123, 16, 948, 79], [1124, 18, 948, 79, "fileName"], [1124, 26, 948, 79], [1124, 28, 948, 79, "_jsxFileName"], [1124, 40, 948, 79], [1125, 18, 948, 79, "lineNumber"], [1125, 28, 948, 79], [1126, 18, 948, 79, "columnNumber"], [1126, 30, 948, 79], [1127, 16, 948, 79], [1127, 23, 949, 26], [1127, 24, 950, 19], [1128, 14, 950, 19], [1129, 16, 950, 19, "fileName"], [1129, 24, 950, 19], [1129, 26, 950, 19, "_jsxFileName"], [1129, 38, 950, 19], [1130, 16, 950, 19, "lineNumber"], [1130, 26, 950, 19], [1131, 16, 950, 19, "columnNumber"], [1131, 28, 950, 19], [1132, 14, 950, 19], [1132, 21, 951, 22], [1132, 22, 951, 23], [1132, 37, 952, 16], [1132, 41, 952, 16, "_jsxDevRuntime"], [1132, 55, 952, 16], [1132, 56, 952, 16, "jsxDEV"], [1132, 62, 952, 16], [1132, 64, 952, 17, "_TouchableOpacity"], [1132, 81, 952, 17], [1132, 82, 952, 17, "default"], [1132, 89, 952, 33], [1133, 16, 952, 34, "onPress"], [1133, 23, 952, 41], [1133, 25, 952, 43, "onCancel"], [1133, 33, 952, 52], [1134, 16, 952, 53, "style"], [1134, 21, 952, 58], [1134, 23, 952, 60, "styles"], [1134, 29, 952, 66], [1134, 30, 952, 67, "closeButton"], [1134, 41, 952, 79], [1135, 16, 952, 79, "children"], [1135, 24, 952, 79], [1135, 39, 953, 18], [1135, 43, 953, 18, "_jsxDevRuntime"], [1135, 57, 953, 18], [1135, 58, 953, 18, "jsxDEV"], [1135, 64, 953, 18], [1135, 66, 953, 19, "_lucideReactNative"], [1135, 84, 953, 19], [1135, 85, 953, 19, "X"], [1135, 86, 953, 20], [1136, 18, 953, 21, "size"], [1136, 22, 953, 25], [1136, 24, 953, 27], [1136, 26, 953, 30], [1137, 18, 953, 31, "color"], [1137, 23, 953, 36], [1137, 25, 953, 37], [1138, 16, 953, 43], [1139, 18, 953, 43, "fileName"], [1139, 26, 953, 43], [1139, 28, 953, 43, "_jsxFileName"], [1139, 40, 953, 43], [1140, 18, 953, 43, "lineNumber"], [1140, 28, 953, 43], [1141, 18, 953, 43, "columnNumber"], [1141, 30, 953, 43], [1142, 16, 953, 43], [1142, 23, 953, 45], [1143, 14, 953, 46], [1144, 16, 953, 46, "fileName"], [1144, 24, 953, 46], [1144, 26, 953, 46, "_jsxFileName"], [1144, 38, 953, 46], [1145, 16, 953, 46, "lineNumber"], [1145, 26, 953, 46], [1146, 16, 953, 46, "columnNumber"], [1146, 28, 953, 46], [1147, 14, 953, 46], [1147, 21, 954, 34], [1147, 22, 954, 35], [1148, 12, 954, 35], [1149, 14, 954, 35, "fileName"], [1149, 22, 954, 35], [1149, 24, 954, 35, "_jsxFileName"], [1149, 36, 954, 35], [1150, 14, 954, 35, "lineNumber"], [1150, 24, 954, 35], [1151, 14, 954, 35, "columnNumber"], [1151, 26, 954, 35], [1152, 12, 954, 35], [1152, 19, 955, 20], [1153, 10, 955, 21], [1154, 12, 955, 21, "fileName"], [1154, 20, 955, 21], [1154, 22, 955, 21, "_jsxFileName"], [1154, 34, 955, 21], [1155, 12, 955, 21, "lineNumber"], [1155, 22, 955, 21], [1156, 12, 955, 21, "columnNumber"], [1156, 24, 955, 21], [1157, 10, 955, 21], [1157, 17, 956, 18], [1157, 18, 956, 19], [1157, 33, 958, 12], [1157, 37, 958, 12, "_jsxDevRuntime"], [1157, 51, 958, 12], [1157, 52, 958, 12, "jsxDEV"], [1157, 58, 958, 12], [1157, 60, 958, 13, "_View"], [1157, 65, 958, 13], [1157, 66, 958, 13, "default"], [1157, 73, 958, 17], [1158, 12, 958, 18, "style"], [1158, 17, 958, 23], [1158, 19, 958, 25, "styles"], [1158, 25, 958, 31], [1158, 26, 958, 32, "privacyNotice"], [1158, 39, 958, 46], [1159, 12, 958, 46, "children"], [1159, 20, 958, 46], [1159, 36, 959, 14], [1159, 40, 959, 14, "_jsxDevRuntime"], [1159, 54, 959, 14], [1159, 55, 959, 14, "jsxDEV"], [1159, 61, 959, 14], [1159, 63, 959, 15, "_lucideReactNative"], [1159, 81, 959, 15], [1159, 82, 959, 15, "Shield"], [1159, 88, 959, 21], [1160, 14, 959, 22, "size"], [1160, 18, 959, 26], [1160, 20, 959, 28], [1160, 22, 959, 31], [1161, 14, 959, 32, "color"], [1161, 19, 959, 37], [1161, 21, 959, 38], [1162, 12, 959, 47], [1163, 14, 959, 47, "fileName"], [1163, 22, 959, 47], [1163, 24, 959, 47, "_jsxFileName"], [1163, 36, 959, 47], [1164, 14, 959, 47, "lineNumber"], [1164, 24, 959, 47], [1165, 14, 959, 47, "columnNumber"], [1165, 26, 959, 47], [1166, 12, 959, 47], [1166, 19, 959, 49], [1166, 20, 959, 50], [1166, 35, 960, 14], [1166, 39, 960, 14, "_jsxDevRuntime"], [1166, 53, 960, 14], [1166, 54, 960, 14, "jsxDEV"], [1166, 60, 960, 14], [1166, 62, 960, 15, "_Text"], [1166, 67, 960, 15], [1166, 68, 960, 15, "default"], [1166, 75, 960, 19], [1167, 14, 960, 20, "style"], [1167, 19, 960, 25], [1167, 21, 960, 27, "styles"], [1167, 27, 960, 33], [1167, 28, 960, 34, "privacyText"], [1167, 39, 960, 46], [1168, 14, 960, 46, "children"], [1168, 22, 960, 46], [1168, 24, 960, 47], [1169, 12, 962, 14], [1170, 14, 962, 14, "fileName"], [1170, 22, 962, 14], [1170, 24, 962, 14, "_jsxFileName"], [1170, 36, 962, 14], [1171, 14, 962, 14, "lineNumber"], [1171, 24, 962, 14], [1172, 14, 962, 14, "columnNumber"], [1172, 26, 962, 14], [1173, 12, 962, 14], [1173, 19, 962, 20], [1173, 20, 962, 21], [1174, 10, 962, 21], [1175, 12, 962, 21, "fileName"], [1175, 20, 962, 21], [1175, 22, 962, 21, "_jsxFileName"], [1175, 34, 962, 21], [1176, 12, 962, 21, "lineNumber"], [1176, 22, 962, 21], [1177, 12, 962, 21, "columnNumber"], [1177, 24, 962, 21], [1178, 10, 962, 21], [1178, 17, 963, 18], [1178, 18, 963, 19], [1178, 33, 965, 12], [1178, 37, 965, 12, "_jsxDevRuntime"], [1178, 51, 965, 12], [1178, 52, 965, 12, "jsxDEV"], [1178, 58, 965, 12], [1178, 60, 965, 13, "_View"], [1178, 65, 965, 13], [1178, 66, 965, 13, "default"], [1178, 73, 965, 17], [1179, 12, 965, 18, "style"], [1179, 17, 965, 23], [1179, 19, 965, 25, "styles"], [1179, 25, 965, 31], [1179, 26, 965, 32, "footer<PERSON><PERSON><PERSON>"], [1179, 39, 965, 46], [1180, 12, 965, 46, "children"], [1180, 20, 965, 46], [1180, 36, 966, 14], [1180, 40, 966, 14, "_jsxDevRuntime"], [1180, 54, 966, 14], [1180, 55, 966, 14, "jsxDEV"], [1180, 61, 966, 14], [1180, 63, 966, 15, "_Text"], [1180, 68, 966, 15], [1180, 69, 966, 15, "default"], [1180, 76, 966, 19], [1181, 14, 966, 20, "style"], [1181, 19, 966, 25], [1181, 21, 966, 27, "styles"], [1181, 27, 966, 33], [1181, 28, 966, 34, "instruction"], [1181, 39, 966, 46], [1182, 14, 966, 46, "children"], [1182, 22, 966, 46], [1182, 24, 966, 47], [1183, 12, 968, 14], [1184, 14, 968, 14, "fileName"], [1184, 22, 968, 14], [1184, 24, 968, 14, "_jsxFileName"], [1184, 36, 968, 14], [1185, 14, 968, 14, "lineNumber"], [1185, 24, 968, 14], [1186, 14, 968, 14, "columnNumber"], [1186, 26, 968, 14], [1187, 12, 968, 14], [1187, 19, 968, 20], [1187, 20, 968, 21], [1187, 35, 970, 14], [1187, 39, 970, 14, "_jsxDevRuntime"], [1187, 53, 970, 14], [1187, 54, 970, 14, "jsxDEV"], [1187, 60, 970, 14], [1187, 62, 970, 15, "_TouchableOpacity"], [1187, 79, 970, 15], [1187, 80, 970, 15, "default"], [1187, 87, 970, 31], [1188, 14, 971, 16, "onPress"], [1188, 21, 971, 23], [1188, 23, 971, 25, "capturePhoto"], [1188, 35, 971, 38], [1189, 14, 972, 16, "disabled"], [1189, 22, 972, 24], [1189, 24, 972, 26, "processingState"], [1189, 39, 972, 41], [1189, 44, 972, 46], [1189, 50, 972, 52], [1189, 54, 972, 56], [1189, 55, 972, 57, "isCameraReady"], [1189, 68, 972, 71], [1190, 14, 973, 16, "style"], [1190, 19, 973, 21], [1190, 21, 973, 23], [1190, 22, 974, 18, "styles"], [1190, 28, 974, 24], [1190, 29, 974, 25, "shutterButton"], [1190, 42, 974, 38], [1190, 44, 975, 18, "processingState"], [1190, 59, 975, 33], [1190, 64, 975, 38], [1190, 70, 975, 44], [1190, 74, 975, 48, "styles"], [1190, 80, 975, 54], [1190, 81, 975, 55, "shutterButtonDisabled"], [1190, 102, 975, 76], [1190, 103, 976, 18], [1191, 14, 976, 18, "children"], [1191, 22, 976, 18], [1191, 24, 978, 17, "processingState"], [1191, 39, 978, 32], [1191, 44, 978, 37], [1191, 50, 978, 43], [1191, 66, 979, 18], [1191, 70, 979, 18, "_jsxDevRuntime"], [1191, 84, 979, 18], [1191, 85, 979, 18, "jsxDEV"], [1191, 91, 979, 18], [1191, 93, 979, 19, "_View"], [1191, 98, 979, 19], [1191, 99, 979, 19, "default"], [1191, 106, 979, 23], [1192, 16, 979, 24, "style"], [1192, 21, 979, 29], [1192, 23, 979, 31, "styles"], [1192, 29, 979, 37], [1192, 30, 979, 38, "shutterInner"], [1193, 14, 979, 51], [1194, 16, 979, 51, "fileName"], [1194, 24, 979, 51], [1194, 26, 979, 51, "_jsxFileName"], [1194, 38, 979, 51], [1195, 16, 979, 51, "lineNumber"], [1195, 26, 979, 51], [1196, 16, 979, 51, "columnNumber"], [1196, 28, 979, 51], [1197, 14, 979, 51], [1197, 21, 979, 53], [1197, 22, 979, 54], [1197, 38, 981, 18], [1197, 42, 981, 18, "_jsxDevRuntime"], [1197, 56, 981, 18], [1197, 57, 981, 18, "jsxDEV"], [1197, 63, 981, 18], [1197, 65, 981, 19, "_ActivityIndicator"], [1197, 83, 981, 19], [1197, 84, 981, 19, "default"], [1197, 91, 981, 36], [1198, 16, 981, 37, "size"], [1198, 20, 981, 41], [1198, 22, 981, 42], [1198, 29, 981, 49], [1199, 16, 981, 50, "color"], [1199, 21, 981, 55], [1199, 23, 981, 56], [1200, 14, 981, 65], [1201, 16, 981, 65, "fileName"], [1201, 24, 981, 65], [1201, 26, 981, 65, "_jsxFileName"], [1201, 38, 981, 65], [1202, 16, 981, 65, "lineNumber"], [1202, 26, 981, 65], [1203, 16, 981, 65, "columnNumber"], [1203, 28, 981, 65], [1204, 14, 981, 65], [1204, 21, 981, 67], [1205, 12, 982, 17], [1206, 14, 982, 17, "fileName"], [1206, 22, 982, 17], [1206, 24, 982, 17, "_jsxFileName"], [1206, 36, 982, 17], [1207, 14, 982, 17, "lineNumber"], [1207, 24, 982, 17], [1208, 14, 982, 17, "columnNumber"], [1208, 26, 982, 17], [1209, 12, 982, 17], [1209, 19, 983, 32], [1209, 20, 983, 33], [1209, 35, 984, 14], [1209, 39, 984, 14, "_jsxDevRuntime"], [1209, 53, 984, 14], [1209, 54, 984, 14, "jsxDEV"], [1209, 60, 984, 14], [1209, 62, 984, 15, "_Text"], [1209, 67, 984, 15], [1209, 68, 984, 15, "default"], [1209, 75, 984, 19], [1210, 14, 984, 20, "style"], [1210, 19, 984, 25], [1210, 21, 984, 27, "styles"], [1210, 27, 984, 33], [1210, 28, 984, 34, "privacyNote"], [1210, 39, 984, 46], [1211, 14, 984, 46, "children"], [1211, 22, 984, 46], [1211, 24, 984, 47], [1212, 12, 986, 14], [1213, 14, 986, 14, "fileName"], [1213, 22, 986, 14], [1213, 24, 986, 14, "_jsxFileName"], [1213, 36, 986, 14], [1214, 14, 986, 14, "lineNumber"], [1214, 24, 986, 14], [1215, 14, 986, 14, "columnNumber"], [1215, 26, 986, 14], [1216, 12, 986, 14], [1216, 19, 986, 20], [1216, 20, 986, 21], [1217, 10, 986, 21], [1218, 12, 986, 21, "fileName"], [1218, 20, 986, 21], [1218, 22, 986, 21, "_jsxFileName"], [1218, 34, 986, 21], [1219, 12, 986, 21, "lineNumber"], [1219, 22, 986, 21], [1220, 12, 986, 21, "columnNumber"], [1220, 24, 986, 21], [1221, 10, 986, 21], [1221, 17, 987, 18], [1221, 18, 987, 19], [1222, 8, 987, 19], [1222, 23, 988, 12], [1222, 24, 989, 9], [1223, 6, 989, 9], [1224, 8, 989, 9, "fileName"], [1224, 16, 989, 9], [1224, 18, 989, 9, "_jsxFileName"], [1224, 30, 989, 9], [1225, 8, 989, 9, "lineNumber"], [1225, 18, 989, 9], [1226, 8, 989, 9, "columnNumber"], [1226, 20, 989, 9], [1227, 6, 989, 9], [1227, 13, 990, 12], [1227, 14, 990, 13], [1227, 29, 992, 6], [1227, 33, 992, 6, "_jsxDevRuntime"], [1227, 47, 992, 6], [1227, 48, 992, 6, "jsxDEV"], [1227, 54, 992, 6], [1227, 56, 992, 7, "_Modal"], [1227, 62, 992, 7], [1227, 63, 992, 7, "default"], [1227, 70, 992, 12], [1228, 8, 993, 8, "visible"], [1228, 15, 993, 15], [1228, 17, 993, 17, "processingState"], [1228, 32, 993, 32], [1228, 37, 993, 37], [1228, 43, 993, 43], [1228, 47, 993, 47, "processingState"], [1228, 62, 993, 62], [1228, 67, 993, 67], [1228, 74, 993, 75], [1229, 8, 994, 8, "transparent"], [1229, 19, 994, 19], [1230, 8, 995, 8, "animationType"], [1230, 21, 995, 21], [1230, 23, 995, 22], [1230, 29, 995, 28], [1231, 8, 995, 28, "children"], [1231, 16, 995, 28], [1231, 31, 997, 8], [1231, 35, 997, 8, "_jsxDevRuntime"], [1231, 49, 997, 8], [1231, 50, 997, 8, "jsxDEV"], [1231, 56, 997, 8], [1231, 58, 997, 9, "_View"], [1231, 63, 997, 9], [1231, 64, 997, 9, "default"], [1231, 71, 997, 13], [1232, 10, 997, 14, "style"], [1232, 15, 997, 19], [1232, 17, 997, 21, "styles"], [1232, 23, 997, 27], [1232, 24, 997, 28, "processingModal"], [1232, 39, 997, 44], [1233, 10, 997, 44, "children"], [1233, 18, 997, 44], [1233, 33, 998, 10], [1233, 37, 998, 10, "_jsxDevRuntime"], [1233, 51, 998, 10], [1233, 52, 998, 10, "jsxDEV"], [1233, 58, 998, 10], [1233, 60, 998, 11, "_View"], [1233, 65, 998, 11], [1233, 66, 998, 11, "default"], [1233, 73, 998, 15], [1234, 12, 998, 16, "style"], [1234, 17, 998, 21], [1234, 19, 998, 23, "styles"], [1234, 25, 998, 29], [1234, 26, 998, 30, "processingContent"], [1234, 43, 998, 48], [1235, 12, 998, 48, "children"], [1235, 20, 998, 48], [1235, 36, 999, 12], [1235, 40, 999, 12, "_jsxDevRuntime"], [1235, 54, 999, 12], [1235, 55, 999, 12, "jsxDEV"], [1235, 61, 999, 12], [1235, 63, 999, 13, "_ActivityIndicator"], [1235, 81, 999, 13], [1235, 82, 999, 13, "default"], [1235, 89, 999, 30], [1236, 14, 999, 31, "size"], [1236, 18, 999, 35], [1236, 20, 999, 36], [1236, 27, 999, 43], [1237, 14, 999, 44, "color"], [1237, 19, 999, 49], [1237, 21, 999, 50], [1238, 12, 999, 59], [1239, 14, 999, 59, "fileName"], [1239, 22, 999, 59], [1239, 24, 999, 59, "_jsxFileName"], [1239, 36, 999, 59], [1240, 14, 999, 59, "lineNumber"], [1240, 24, 999, 59], [1241, 14, 999, 59, "columnNumber"], [1241, 26, 999, 59], [1242, 12, 999, 59], [1242, 19, 999, 61], [1242, 20, 999, 62], [1242, 35, 1001, 12], [1242, 39, 1001, 12, "_jsxDevRuntime"], [1242, 53, 1001, 12], [1242, 54, 1001, 12, "jsxDEV"], [1242, 60, 1001, 12], [1242, 62, 1001, 13, "_Text"], [1242, 67, 1001, 13], [1242, 68, 1001, 13, "default"], [1242, 75, 1001, 17], [1243, 14, 1001, 18, "style"], [1243, 19, 1001, 23], [1243, 21, 1001, 25, "styles"], [1243, 27, 1001, 31], [1243, 28, 1001, 32, "processingTitle"], [1243, 43, 1001, 48], [1244, 14, 1001, 48, "children"], [1244, 22, 1001, 48], [1244, 25, 1002, 15, "processingState"], [1244, 40, 1002, 30], [1244, 45, 1002, 35], [1244, 56, 1002, 46], [1244, 60, 1002, 50], [1244, 80, 1002, 70], [1244, 82, 1003, 15, "processingState"], [1244, 97, 1003, 30], [1244, 102, 1003, 35], [1244, 113, 1003, 46], [1244, 117, 1003, 50], [1244, 146, 1003, 79], [1244, 148, 1004, 15, "processingState"], [1244, 163, 1004, 30], [1244, 168, 1004, 35], [1244, 180, 1004, 47], [1244, 184, 1004, 51], [1244, 216, 1004, 83], [1244, 218, 1005, 15, "processingState"], [1244, 233, 1005, 30], [1244, 238, 1005, 35], [1244, 249, 1005, 46], [1244, 253, 1005, 50], [1244, 275, 1005, 72], [1245, 12, 1005, 72], [1246, 14, 1005, 72, "fileName"], [1246, 22, 1005, 72], [1246, 24, 1005, 72, "_jsxFileName"], [1246, 36, 1005, 72], [1247, 14, 1005, 72, "lineNumber"], [1247, 24, 1005, 72], [1248, 14, 1005, 72, "columnNumber"], [1248, 26, 1005, 72], [1249, 12, 1005, 72], [1249, 19, 1006, 18], [1249, 20, 1006, 19], [1249, 35, 1007, 12], [1249, 39, 1007, 12, "_jsxDevRuntime"], [1249, 53, 1007, 12], [1249, 54, 1007, 12, "jsxDEV"], [1249, 60, 1007, 12], [1249, 62, 1007, 13, "_View"], [1249, 67, 1007, 13], [1249, 68, 1007, 13, "default"], [1249, 75, 1007, 17], [1250, 14, 1007, 18, "style"], [1250, 19, 1007, 23], [1250, 21, 1007, 25, "styles"], [1250, 27, 1007, 31], [1250, 28, 1007, 32, "progressBar"], [1250, 39, 1007, 44], [1251, 14, 1007, 44, "children"], [1251, 22, 1007, 44], [1251, 37, 1008, 14], [1251, 41, 1008, 14, "_jsxDevRuntime"], [1251, 55, 1008, 14], [1251, 56, 1008, 14, "jsxDEV"], [1251, 62, 1008, 14], [1251, 64, 1008, 15, "_View"], [1251, 69, 1008, 15], [1251, 70, 1008, 15, "default"], [1251, 77, 1008, 19], [1252, 16, 1009, 16, "style"], [1252, 21, 1009, 21], [1252, 23, 1009, 23], [1252, 24, 1010, 18, "styles"], [1252, 30, 1010, 24], [1252, 31, 1010, 25, "progressFill"], [1252, 43, 1010, 37], [1252, 45, 1011, 18], [1253, 18, 1011, 20, "width"], [1253, 23, 1011, 25], [1253, 25, 1011, 27], [1253, 28, 1011, 30, "processingProgress"], [1253, 46, 1011, 48], [1254, 16, 1011, 52], [1254, 17, 1011, 53], [1255, 14, 1012, 18], [1256, 16, 1012, 18, "fileName"], [1256, 24, 1012, 18], [1256, 26, 1012, 18, "_jsxFileName"], [1256, 38, 1012, 18], [1257, 16, 1012, 18, "lineNumber"], [1257, 26, 1012, 18], [1258, 16, 1012, 18, "columnNumber"], [1258, 28, 1012, 18], [1259, 14, 1012, 18], [1259, 21, 1013, 15], [1260, 12, 1013, 16], [1261, 14, 1013, 16, "fileName"], [1261, 22, 1013, 16], [1261, 24, 1013, 16, "_jsxFileName"], [1261, 36, 1013, 16], [1262, 14, 1013, 16, "lineNumber"], [1262, 24, 1013, 16], [1263, 14, 1013, 16, "columnNumber"], [1263, 26, 1013, 16], [1264, 12, 1013, 16], [1264, 19, 1014, 18], [1264, 20, 1014, 19], [1264, 35, 1015, 12], [1264, 39, 1015, 12, "_jsxDevRuntime"], [1264, 53, 1015, 12], [1264, 54, 1015, 12, "jsxDEV"], [1264, 60, 1015, 12], [1264, 62, 1015, 13, "_Text"], [1264, 67, 1015, 13], [1264, 68, 1015, 13, "default"], [1264, 75, 1015, 17], [1265, 14, 1015, 18, "style"], [1265, 19, 1015, 23], [1265, 21, 1015, 25, "styles"], [1265, 27, 1015, 31], [1265, 28, 1015, 32, "processingDescription"], [1265, 49, 1015, 54], [1266, 14, 1015, 54, "children"], [1266, 22, 1015, 54], [1266, 25, 1016, 15, "processingState"], [1266, 40, 1016, 30], [1266, 45, 1016, 35], [1266, 56, 1016, 46], [1266, 60, 1016, 50], [1266, 89, 1016, 79], [1266, 91, 1017, 15, "processingState"], [1266, 106, 1017, 30], [1266, 111, 1017, 35], [1266, 122, 1017, 46], [1266, 126, 1017, 50], [1266, 164, 1017, 88], [1266, 166, 1018, 15, "processingState"], [1266, 181, 1018, 30], [1266, 186, 1018, 35], [1266, 198, 1018, 47], [1266, 202, 1018, 51], [1266, 247, 1018, 96], [1266, 249, 1019, 15, "processingState"], [1266, 264, 1019, 30], [1266, 269, 1019, 35], [1266, 280, 1019, 46], [1266, 284, 1019, 50], [1266, 325, 1019, 91], [1267, 12, 1019, 91], [1268, 14, 1019, 91, "fileName"], [1268, 22, 1019, 91], [1268, 24, 1019, 91, "_jsxFileName"], [1268, 36, 1019, 91], [1269, 14, 1019, 91, "lineNumber"], [1269, 24, 1019, 91], [1270, 14, 1019, 91, "columnNumber"], [1270, 26, 1019, 91], [1271, 12, 1019, 91], [1271, 19, 1020, 18], [1271, 20, 1020, 19], [1271, 22, 1021, 13, "processingState"], [1271, 37, 1021, 28], [1271, 42, 1021, 33], [1271, 53, 1021, 44], [1271, 70, 1022, 14], [1271, 74, 1022, 14, "_jsxDevRuntime"], [1271, 88, 1022, 14], [1271, 89, 1022, 14, "jsxDEV"], [1271, 95, 1022, 14], [1271, 97, 1022, 15, "_lucideReactNative"], [1271, 115, 1022, 15], [1271, 116, 1022, 15, "CheckCircle"], [1271, 127, 1022, 26], [1272, 14, 1022, 27, "size"], [1272, 18, 1022, 31], [1272, 20, 1022, 33], [1272, 22, 1022, 36], [1273, 14, 1022, 37, "color"], [1273, 19, 1022, 42], [1273, 21, 1022, 43], [1273, 30, 1022, 52], [1274, 14, 1022, 53, "style"], [1274, 19, 1022, 58], [1274, 21, 1022, 60, "styles"], [1274, 27, 1022, 66], [1274, 28, 1022, 67, "successIcon"], [1275, 12, 1022, 79], [1276, 14, 1022, 79, "fileName"], [1276, 22, 1022, 79], [1276, 24, 1022, 79, "_jsxFileName"], [1276, 36, 1022, 79], [1277, 14, 1022, 79, "lineNumber"], [1277, 24, 1022, 79], [1278, 14, 1022, 79, "columnNumber"], [1278, 26, 1022, 79], [1279, 12, 1022, 79], [1279, 19, 1022, 81], [1279, 20, 1023, 13], [1280, 10, 1023, 13], [1281, 12, 1023, 13, "fileName"], [1281, 20, 1023, 13], [1281, 22, 1023, 13, "_jsxFileName"], [1281, 34, 1023, 13], [1282, 12, 1023, 13, "lineNumber"], [1282, 22, 1023, 13], [1283, 12, 1023, 13, "columnNumber"], [1283, 24, 1023, 13], [1284, 10, 1023, 13], [1284, 17, 1024, 16], [1285, 8, 1024, 17], [1286, 10, 1024, 17, "fileName"], [1286, 18, 1024, 17], [1286, 20, 1024, 17, "_jsxFileName"], [1286, 32, 1024, 17], [1287, 10, 1024, 17, "lineNumber"], [1287, 20, 1024, 17], [1288, 10, 1024, 17, "columnNumber"], [1288, 22, 1024, 17], [1289, 8, 1024, 17], [1289, 15, 1025, 14], [1290, 6, 1025, 15], [1291, 8, 1025, 15, "fileName"], [1291, 16, 1025, 15], [1291, 18, 1025, 15, "_jsxFileName"], [1291, 30, 1025, 15], [1292, 8, 1025, 15, "lineNumber"], [1292, 18, 1025, 15], [1293, 8, 1025, 15, "columnNumber"], [1293, 20, 1025, 15], [1294, 6, 1025, 15], [1294, 13, 1026, 13], [1294, 14, 1026, 14], [1294, 29, 1028, 6], [1294, 33, 1028, 6, "_jsxDevRuntime"], [1294, 47, 1028, 6], [1294, 48, 1028, 6, "jsxDEV"], [1294, 54, 1028, 6], [1294, 56, 1028, 7, "_Modal"], [1294, 62, 1028, 7], [1294, 63, 1028, 7, "default"], [1294, 70, 1028, 12], [1295, 8, 1029, 8, "visible"], [1295, 15, 1029, 15], [1295, 17, 1029, 17, "processingState"], [1295, 32, 1029, 32], [1295, 37, 1029, 37], [1295, 44, 1029, 45], [1296, 8, 1030, 8, "transparent"], [1296, 19, 1030, 19], [1297, 8, 1031, 8, "animationType"], [1297, 21, 1031, 21], [1297, 23, 1031, 22], [1297, 29, 1031, 28], [1298, 8, 1031, 28, "children"], [1298, 16, 1031, 28], [1298, 31, 1033, 8], [1298, 35, 1033, 8, "_jsxDevRuntime"], [1298, 49, 1033, 8], [1298, 50, 1033, 8, "jsxDEV"], [1298, 56, 1033, 8], [1298, 58, 1033, 9, "_View"], [1298, 63, 1033, 9], [1298, 64, 1033, 9, "default"], [1298, 71, 1033, 13], [1299, 10, 1033, 14, "style"], [1299, 15, 1033, 19], [1299, 17, 1033, 21, "styles"], [1299, 23, 1033, 27], [1299, 24, 1033, 28, "processingModal"], [1299, 39, 1033, 44], [1300, 10, 1033, 44, "children"], [1300, 18, 1033, 44], [1300, 33, 1034, 10], [1300, 37, 1034, 10, "_jsxDevRuntime"], [1300, 51, 1034, 10], [1300, 52, 1034, 10, "jsxDEV"], [1300, 58, 1034, 10], [1300, 60, 1034, 11, "_View"], [1300, 65, 1034, 11], [1300, 66, 1034, 11, "default"], [1300, 73, 1034, 15], [1301, 12, 1034, 16, "style"], [1301, 17, 1034, 21], [1301, 19, 1034, 23, "styles"], [1301, 25, 1034, 29], [1301, 26, 1034, 30, "errorContent"], [1301, 38, 1034, 43], [1302, 12, 1034, 43, "children"], [1302, 20, 1034, 43], [1302, 36, 1035, 12], [1302, 40, 1035, 12, "_jsxDevRuntime"], [1302, 54, 1035, 12], [1302, 55, 1035, 12, "jsxDEV"], [1302, 61, 1035, 12], [1302, 63, 1035, 13, "_lucideReactNative"], [1302, 81, 1035, 13], [1302, 82, 1035, 13, "X"], [1302, 83, 1035, 14], [1303, 14, 1035, 15, "size"], [1303, 18, 1035, 19], [1303, 20, 1035, 21], [1303, 22, 1035, 24], [1304, 14, 1035, 25, "color"], [1304, 19, 1035, 30], [1304, 21, 1035, 31], [1305, 12, 1035, 40], [1306, 14, 1035, 40, "fileName"], [1306, 22, 1035, 40], [1306, 24, 1035, 40, "_jsxFileName"], [1306, 36, 1035, 40], [1307, 14, 1035, 40, "lineNumber"], [1307, 24, 1035, 40], [1308, 14, 1035, 40, "columnNumber"], [1308, 26, 1035, 40], [1309, 12, 1035, 40], [1309, 19, 1035, 42], [1309, 20, 1035, 43], [1309, 35, 1036, 12], [1309, 39, 1036, 12, "_jsxDevRuntime"], [1309, 53, 1036, 12], [1309, 54, 1036, 12, "jsxDEV"], [1309, 60, 1036, 12], [1309, 62, 1036, 13, "_Text"], [1309, 67, 1036, 13], [1309, 68, 1036, 13, "default"], [1309, 75, 1036, 17], [1310, 14, 1036, 18, "style"], [1310, 19, 1036, 23], [1310, 21, 1036, 25, "styles"], [1310, 27, 1036, 31], [1310, 28, 1036, 32, "errorTitle"], [1310, 38, 1036, 43], [1311, 14, 1036, 43, "children"], [1311, 22, 1036, 43], [1311, 24, 1036, 44], [1312, 12, 1036, 61], [1313, 14, 1036, 61, "fileName"], [1313, 22, 1036, 61], [1313, 24, 1036, 61, "_jsxFileName"], [1313, 36, 1036, 61], [1314, 14, 1036, 61, "lineNumber"], [1314, 24, 1036, 61], [1315, 14, 1036, 61, "columnNumber"], [1315, 26, 1036, 61], [1316, 12, 1036, 61], [1316, 19, 1036, 67], [1316, 20, 1036, 68], [1316, 35, 1037, 12], [1316, 39, 1037, 12, "_jsxDevRuntime"], [1316, 53, 1037, 12], [1316, 54, 1037, 12, "jsxDEV"], [1316, 60, 1037, 12], [1316, 62, 1037, 13, "_Text"], [1316, 67, 1037, 13], [1316, 68, 1037, 13, "default"], [1316, 75, 1037, 17], [1317, 14, 1037, 18, "style"], [1317, 19, 1037, 23], [1317, 21, 1037, 25, "styles"], [1317, 27, 1037, 31], [1317, 28, 1037, 32, "errorMessage"], [1317, 40, 1037, 45], [1318, 14, 1037, 45, "children"], [1318, 22, 1037, 45], [1318, 24, 1037, 47, "errorMessage"], [1319, 12, 1037, 59], [1320, 14, 1037, 59, "fileName"], [1320, 22, 1037, 59], [1320, 24, 1037, 59, "_jsxFileName"], [1320, 36, 1037, 59], [1321, 14, 1037, 59, "lineNumber"], [1321, 24, 1037, 59], [1322, 14, 1037, 59, "columnNumber"], [1322, 26, 1037, 59], [1323, 12, 1037, 59], [1323, 19, 1037, 66], [1323, 20, 1037, 67], [1323, 35, 1038, 12], [1323, 39, 1038, 12, "_jsxDevRuntime"], [1323, 53, 1038, 12], [1323, 54, 1038, 12, "jsxDEV"], [1323, 60, 1038, 12], [1323, 62, 1038, 13, "_TouchableOpacity"], [1323, 79, 1038, 13], [1323, 80, 1038, 13, "default"], [1323, 87, 1038, 29], [1324, 14, 1039, 14, "onPress"], [1324, 21, 1039, 21], [1324, 23, 1039, 23, "retryCapture"], [1324, 35, 1039, 36], [1325, 14, 1040, 14, "style"], [1325, 19, 1040, 19], [1325, 21, 1040, 21, "styles"], [1325, 27, 1040, 27], [1325, 28, 1040, 28, "primaryButton"], [1325, 41, 1040, 42], [1326, 14, 1040, 42, "children"], [1326, 22, 1040, 42], [1326, 37, 1042, 14], [1326, 41, 1042, 14, "_jsxDevRuntime"], [1326, 55, 1042, 14], [1326, 56, 1042, 14, "jsxDEV"], [1326, 62, 1042, 14], [1326, 64, 1042, 15, "_Text"], [1326, 69, 1042, 15], [1326, 70, 1042, 15, "default"], [1326, 77, 1042, 19], [1327, 16, 1042, 20, "style"], [1327, 21, 1042, 25], [1327, 23, 1042, 27, "styles"], [1327, 29, 1042, 33], [1327, 30, 1042, 34, "primaryButtonText"], [1327, 47, 1042, 52], [1328, 16, 1042, 52, "children"], [1328, 24, 1042, 52], [1328, 26, 1042, 53], [1329, 14, 1042, 62], [1330, 16, 1042, 62, "fileName"], [1330, 24, 1042, 62], [1330, 26, 1042, 62, "_jsxFileName"], [1330, 38, 1042, 62], [1331, 16, 1042, 62, "lineNumber"], [1331, 26, 1042, 62], [1332, 16, 1042, 62, "columnNumber"], [1332, 28, 1042, 62], [1333, 14, 1042, 62], [1333, 21, 1042, 68], [1334, 12, 1042, 69], [1335, 14, 1042, 69, "fileName"], [1335, 22, 1042, 69], [1335, 24, 1042, 69, "_jsxFileName"], [1335, 36, 1042, 69], [1336, 14, 1042, 69, "lineNumber"], [1336, 24, 1042, 69], [1337, 14, 1042, 69, "columnNumber"], [1337, 26, 1042, 69], [1338, 12, 1042, 69], [1338, 19, 1043, 30], [1338, 20, 1043, 31], [1338, 35, 1044, 12], [1338, 39, 1044, 12, "_jsxDevRuntime"], [1338, 53, 1044, 12], [1338, 54, 1044, 12, "jsxDEV"], [1338, 60, 1044, 12], [1338, 62, 1044, 13, "_TouchableOpacity"], [1338, 79, 1044, 13], [1338, 80, 1044, 13, "default"], [1338, 87, 1044, 29], [1339, 14, 1045, 14, "onPress"], [1339, 21, 1045, 21], [1339, 23, 1045, 23, "onCancel"], [1339, 31, 1045, 32], [1340, 14, 1046, 14, "style"], [1340, 19, 1046, 19], [1340, 21, 1046, 21, "styles"], [1340, 27, 1046, 27], [1340, 28, 1046, 28, "secondaryButton"], [1340, 43, 1046, 44], [1341, 14, 1046, 44, "children"], [1341, 22, 1046, 44], [1341, 37, 1048, 14], [1341, 41, 1048, 14, "_jsxDevRuntime"], [1341, 55, 1048, 14], [1341, 56, 1048, 14, "jsxDEV"], [1341, 62, 1048, 14], [1341, 64, 1048, 15, "_Text"], [1341, 69, 1048, 15], [1341, 70, 1048, 15, "default"], [1341, 77, 1048, 19], [1342, 16, 1048, 20, "style"], [1342, 21, 1048, 25], [1342, 23, 1048, 27, "styles"], [1342, 29, 1048, 33], [1342, 30, 1048, 34, "secondaryButtonText"], [1342, 49, 1048, 54], [1343, 16, 1048, 54, "children"], [1343, 24, 1048, 54], [1343, 26, 1048, 55], [1344, 14, 1048, 61], [1345, 16, 1048, 61, "fileName"], [1345, 24, 1048, 61], [1345, 26, 1048, 61, "_jsxFileName"], [1345, 38, 1048, 61], [1346, 16, 1048, 61, "lineNumber"], [1346, 26, 1048, 61], [1347, 16, 1048, 61, "columnNumber"], [1347, 28, 1048, 61], [1348, 14, 1048, 61], [1348, 21, 1048, 67], [1349, 12, 1048, 68], [1350, 14, 1048, 68, "fileName"], [1350, 22, 1048, 68], [1350, 24, 1048, 68, "_jsxFileName"], [1350, 36, 1048, 68], [1351, 14, 1048, 68, "lineNumber"], [1351, 24, 1048, 68], [1352, 14, 1048, 68, "columnNumber"], [1352, 26, 1048, 68], [1353, 12, 1048, 68], [1353, 19, 1049, 30], [1353, 20, 1049, 31], [1354, 10, 1049, 31], [1355, 12, 1049, 31, "fileName"], [1355, 20, 1049, 31], [1355, 22, 1049, 31, "_jsxFileName"], [1355, 34, 1049, 31], [1356, 12, 1049, 31, "lineNumber"], [1356, 22, 1049, 31], [1357, 12, 1049, 31, "columnNumber"], [1357, 24, 1049, 31], [1358, 10, 1049, 31], [1358, 17, 1050, 16], [1359, 8, 1050, 17], [1360, 10, 1050, 17, "fileName"], [1360, 18, 1050, 17], [1360, 20, 1050, 17, "_jsxFileName"], [1360, 32, 1050, 17], [1361, 10, 1050, 17, "lineNumber"], [1361, 20, 1050, 17], [1362, 10, 1050, 17, "columnNumber"], [1362, 22, 1050, 17], [1363, 8, 1050, 17], [1363, 15, 1051, 14], [1364, 6, 1051, 15], [1365, 8, 1051, 15, "fileName"], [1365, 16, 1051, 15], [1365, 18, 1051, 15, "_jsxFileName"], [1365, 30, 1051, 15], [1366, 8, 1051, 15, "lineNumber"], [1366, 18, 1051, 15], [1367, 8, 1051, 15, "columnNumber"], [1367, 20, 1051, 15], [1368, 6, 1051, 15], [1368, 13, 1052, 13], [1368, 14, 1052, 14], [1369, 4, 1052, 14], [1370, 6, 1052, 14, "fileName"], [1370, 14, 1052, 14], [1370, 16, 1052, 14, "_jsxFileName"], [1370, 28, 1052, 14], [1371, 6, 1052, 14, "lineNumber"], [1371, 16, 1052, 14], [1372, 6, 1052, 14, "columnNumber"], [1372, 18, 1052, 14], [1373, 4, 1052, 14], [1373, 11, 1053, 10], [1373, 12, 1053, 11], [1374, 2, 1055, 0], [1375, 2, 1055, 1, "_s"], [1375, 4, 1055, 1], [1375, 5, 51, 24, "EchoCameraWeb"], [1375, 18, 51, 37], [1376, 4, 51, 37], [1376, 12, 58, 42, "useCameraPermissions"], [1376, 44, 58, 62], [1376, 46, 72, 19, "useUpload"], [1376, 64, 72, 28], [1377, 2, 72, 28], [1378, 2, 72, 28, "_c"], [1378, 4, 72, 28], [1378, 7, 51, 24, "EchoCameraWeb"], [1378, 20, 51, 37], [1379, 2, 1056, 0], [1379, 8, 1056, 6, "styles"], [1379, 14, 1056, 12], [1379, 17, 1056, 15, "StyleSheet"], [1379, 36, 1056, 25], [1379, 37, 1056, 26, "create"], [1379, 43, 1056, 32], [1379, 44, 1056, 33], [1380, 4, 1057, 2, "container"], [1380, 13, 1057, 11], [1380, 15, 1057, 13], [1381, 6, 1058, 4, "flex"], [1381, 10, 1058, 8], [1381, 12, 1058, 10], [1381, 13, 1058, 11], [1382, 6, 1059, 4, "backgroundColor"], [1382, 21, 1059, 19], [1382, 23, 1059, 21], [1383, 4, 1060, 2], [1383, 5, 1060, 3], [1384, 4, 1061, 2, "cameraContainer"], [1384, 19, 1061, 17], [1384, 21, 1061, 19], [1385, 6, 1062, 4, "flex"], [1385, 10, 1062, 8], [1385, 12, 1062, 10], [1385, 13, 1062, 11], [1386, 6, 1063, 4, "max<PERSON><PERSON><PERSON>"], [1386, 14, 1063, 12], [1386, 16, 1063, 14], [1386, 19, 1063, 17], [1387, 6, 1064, 4, "alignSelf"], [1387, 15, 1064, 13], [1387, 17, 1064, 15], [1387, 25, 1064, 23], [1388, 6, 1065, 4, "width"], [1388, 11, 1065, 9], [1388, 13, 1065, 11], [1389, 4, 1066, 2], [1389, 5, 1066, 3], [1390, 4, 1067, 2, "camera"], [1390, 10, 1067, 8], [1390, 12, 1067, 10], [1391, 6, 1068, 4, "flex"], [1391, 10, 1068, 8], [1391, 12, 1068, 10], [1392, 4, 1069, 2], [1392, 5, 1069, 3], [1393, 4, 1070, 2, "headerOverlay"], [1393, 17, 1070, 15], [1393, 19, 1070, 17], [1394, 6, 1071, 4, "position"], [1394, 14, 1071, 12], [1394, 16, 1071, 14], [1394, 26, 1071, 24], [1395, 6, 1072, 4, "top"], [1395, 9, 1072, 7], [1395, 11, 1072, 9], [1395, 12, 1072, 10], [1396, 6, 1073, 4, "left"], [1396, 10, 1073, 8], [1396, 12, 1073, 10], [1396, 13, 1073, 11], [1397, 6, 1074, 4, "right"], [1397, 11, 1074, 9], [1397, 13, 1074, 11], [1397, 14, 1074, 12], [1398, 6, 1075, 4, "backgroundColor"], [1398, 21, 1075, 19], [1398, 23, 1075, 21], [1398, 36, 1075, 34], [1399, 6, 1076, 4, "paddingTop"], [1399, 16, 1076, 14], [1399, 18, 1076, 16], [1399, 20, 1076, 18], [1400, 6, 1077, 4, "paddingHorizontal"], [1400, 23, 1077, 21], [1400, 25, 1077, 23], [1400, 27, 1077, 25], [1401, 6, 1078, 4, "paddingBottom"], [1401, 19, 1078, 17], [1401, 21, 1078, 19], [1402, 4, 1079, 2], [1402, 5, 1079, 3], [1403, 4, 1080, 2, "headerContent"], [1403, 17, 1080, 15], [1403, 19, 1080, 17], [1404, 6, 1081, 4, "flexDirection"], [1404, 19, 1081, 17], [1404, 21, 1081, 19], [1404, 26, 1081, 24], [1405, 6, 1082, 4, "justifyContent"], [1405, 20, 1082, 18], [1405, 22, 1082, 20], [1405, 37, 1082, 35], [1406, 6, 1083, 4, "alignItems"], [1406, 16, 1083, 14], [1406, 18, 1083, 16], [1407, 4, 1084, 2], [1407, 5, 1084, 3], [1408, 4, 1085, 2, "headerLeft"], [1408, 14, 1085, 12], [1408, 16, 1085, 14], [1409, 6, 1086, 4, "flex"], [1409, 10, 1086, 8], [1409, 12, 1086, 10], [1410, 4, 1087, 2], [1410, 5, 1087, 3], [1411, 4, 1088, 2, "headerTitle"], [1411, 15, 1088, 13], [1411, 17, 1088, 15], [1412, 6, 1089, 4, "fontSize"], [1412, 14, 1089, 12], [1412, 16, 1089, 14], [1412, 18, 1089, 16], [1413, 6, 1090, 4, "fontWeight"], [1413, 16, 1090, 14], [1413, 18, 1090, 16], [1413, 23, 1090, 21], [1414, 6, 1091, 4, "color"], [1414, 11, 1091, 9], [1414, 13, 1091, 11], [1414, 19, 1091, 17], [1415, 6, 1092, 4, "marginBottom"], [1415, 18, 1092, 16], [1415, 20, 1092, 18], [1416, 4, 1093, 2], [1416, 5, 1093, 3], [1417, 4, 1094, 2, "subtitleRow"], [1417, 15, 1094, 13], [1417, 17, 1094, 15], [1418, 6, 1095, 4, "flexDirection"], [1418, 19, 1095, 17], [1418, 21, 1095, 19], [1418, 26, 1095, 24], [1419, 6, 1096, 4, "alignItems"], [1419, 16, 1096, 14], [1419, 18, 1096, 16], [1419, 26, 1096, 24], [1420, 6, 1097, 4, "marginBottom"], [1420, 18, 1097, 16], [1420, 20, 1097, 18], [1421, 4, 1098, 2], [1421, 5, 1098, 3], [1422, 4, 1099, 2, "webIcon"], [1422, 11, 1099, 9], [1422, 13, 1099, 11], [1423, 6, 1100, 4, "fontSize"], [1423, 14, 1100, 12], [1423, 16, 1100, 14], [1423, 18, 1100, 16], [1424, 6, 1101, 4, "marginRight"], [1424, 17, 1101, 15], [1424, 19, 1101, 17], [1425, 4, 1102, 2], [1425, 5, 1102, 3], [1426, 4, 1103, 2, "headerSubtitle"], [1426, 18, 1103, 16], [1426, 20, 1103, 18], [1427, 6, 1104, 4, "fontSize"], [1427, 14, 1104, 12], [1427, 16, 1104, 14], [1427, 18, 1104, 16], [1428, 6, 1105, 4, "color"], [1428, 11, 1105, 9], [1428, 13, 1105, 11], [1428, 19, 1105, 17], [1429, 6, 1106, 4, "opacity"], [1429, 13, 1106, 11], [1429, 15, 1106, 13], [1430, 4, 1107, 2], [1430, 5, 1107, 3], [1431, 4, 1108, 2, "challengeRow"], [1431, 16, 1108, 14], [1431, 18, 1108, 16], [1432, 6, 1109, 4, "flexDirection"], [1432, 19, 1109, 17], [1432, 21, 1109, 19], [1432, 26, 1109, 24], [1433, 6, 1110, 4, "alignItems"], [1433, 16, 1110, 14], [1433, 18, 1110, 16], [1434, 4, 1111, 2], [1434, 5, 1111, 3], [1435, 4, 1112, 2, "challengeCode"], [1435, 17, 1112, 15], [1435, 19, 1112, 17], [1436, 6, 1113, 4, "fontSize"], [1436, 14, 1113, 12], [1436, 16, 1113, 14], [1436, 18, 1113, 16], [1437, 6, 1114, 4, "color"], [1437, 11, 1114, 9], [1437, 13, 1114, 11], [1437, 19, 1114, 17], [1438, 6, 1115, 4, "marginLeft"], [1438, 16, 1115, 14], [1438, 18, 1115, 16], [1438, 19, 1115, 17], [1439, 6, 1116, 4, "fontFamily"], [1439, 16, 1116, 14], [1439, 18, 1116, 16], [1440, 4, 1117, 2], [1440, 5, 1117, 3], [1441, 4, 1118, 2, "closeButton"], [1441, 15, 1118, 13], [1441, 17, 1118, 15], [1442, 6, 1119, 4, "padding"], [1442, 13, 1119, 11], [1442, 15, 1119, 13], [1443, 4, 1120, 2], [1443, 5, 1120, 3], [1444, 4, 1121, 2, "privacyNotice"], [1444, 17, 1121, 15], [1444, 19, 1121, 17], [1445, 6, 1122, 4, "position"], [1445, 14, 1122, 12], [1445, 16, 1122, 14], [1445, 26, 1122, 24], [1446, 6, 1123, 4, "top"], [1446, 9, 1123, 7], [1446, 11, 1123, 9], [1446, 14, 1123, 12], [1447, 6, 1124, 4, "left"], [1447, 10, 1124, 8], [1447, 12, 1124, 10], [1447, 14, 1124, 12], [1448, 6, 1125, 4, "right"], [1448, 11, 1125, 9], [1448, 13, 1125, 11], [1448, 15, 1125, 13], [1449, 6, 1126, 4, "backgroundColor"], [1449, 21, 1126, 19], [1449, 23, 1126, 21], [1449, 48, 1126, 46], [1450, 6, 1127, 4, "borderRadius"], [1450, 18, 1127, 16], [1450, 20, 1127, 18], [1450, 21, 1127, 19], [1451, 6, 1128, 4, "padding"], [1451, 13, 1128, 11], [1451, 15, 1128, 13], [1451, 17, 1128, 15], [1452, 6, 1129, 4, "flexDirection"], [1452, 19, 1129, 17], [1452, 21, 1129, 19], [1452, 26, 1129, 24], [1453, 6, 1130, 4, "alignItems"], [1453, 16, 1130, 14], [1453, 18, 1130, 16], [1454, 4, 1131, 2], [1454, 5, 1131, 3], [1455, 4, 1132, 2, "privacyText"], [1455, 15, 1132, 13], [1455, 17, 1132, 15], [1456, 6, 1133, 4, "color"], [1456, 11, 1133, 9], [1456, 13, 1133, 11], [1456, 19, 1133, 17], [1457, 6, 1134, 4, "fontSize"], [1457, 14, 1134, 12], [1457, 16, 1134, 14], [1457, 18, 1134, 16], [1458, 6, 1135, 4, "marginLeft"], [1458, 16, 1135, 14], [1458, 18, 1135, 16], [1458, 19, 1135, 17], [1459, 6, 1136, 4, "flex"], [1459, 10, 1136, 8], [1459, 12, 1136, 10], [1460, 4, 1137, 2], [1460, 5, 1137, 3], [1461, 4, 1138, 2, "footer<PERSON><PERSON><PERSON>"], [1461, 17, 1138, 15], [1461, 19, 1138, 17], [1462, 6, 1139, 4, "position"], [1462, 14, 1139, 12], [1462, 16, 1139, 14], [1462, 26, 1139, 24], [1463, 6, 1140, 4, "bottom"], [1463, 12, 1140, 10], [1463, 14, 1140, 12], [1463, 15, 1140, 13], [1464, 6, 1141, 4, "left"], [1464, 10, 1141, 8], [1464, 12, 1141, 10], [1464, 13, 1141, 11], [1465, 6, 1142, 4, "right"], [1465, 11, 1142, 9], [1465, 13, 1142, 11], [1465, 14, 1142, 12], [1466, 6, 1143, 4, "backgroundColor"], [1466, 21, 1143, 19], [1466, 23, 1143, 21], [1466, 36, 1143, 34], [1467, 6, 1144, 4, "paddingBottom"], [1467, 19, 1144, 17], [1467, 21, 1144, 19], [1467, 23, 1144, 21], [1468, 6, 1145, 4, "paddingTop"], [1468, 16, 1145, 14], [1468, 18, 1145, 16], [1468, 20, 1145, 18], [1469, 6, 1146, 4, "alignItems"], [1469, 16, 1146, 14], [1469, 18, 1146, 16], [1470, 4, 1147, 2], [1470, 5, 1147, 3], [1471, 4, 1148, 2, "instruction"], [1471, 15, 1148, 13], [1471, 17, 1148, 15], [1472, 6, 1149, 4, "fontSize"], [1472, 14, 1149, 12], [1472, 16, 1149, 14], [1472, 18, 1149, 16], [1473, 6, 1150, 4, "color"], [1473, 11, 1150, 9], [1473, 13, 1150, 11], [1473, 19, 1150, 17], [1474, 6, 1151, 4, "marginBottom"], [1474, 18, 1151, 16], [1474, 20, 1151, 18], [1475, 4, 1152, 2], [1475, 5, 1152, 3], [1476, 4, 1153, 2, "shutterButton"], [1476, 17, 1153, 15], [1476, 19, 1153, 17], [1477, 6, 1154, 4, "width"], [1477, 11, 1154, 9], [1477, 13, 1154, 11], [1477, 15, 1154, 13], [1478, 6, 1155, 4, "height"], [1478, 12, 1155, 10], [1478, 14, 1155, 12], [1478, 16, 1155, 14], [1479, 6, 1156, 4, "borderRadius"], [1479, 18, 1156, 16], [1479, 20, 1156, 18], [1479, 22, 1156, 20], [1480, 6, 1157, 4, "backgroundColor"], [1480, 21, 1157, 19], [1480, 23, 1157, 21], [1480, 29, 1157, 27], [1481, 6, 1158, 4, "justifyContent"], [1481, 20, 1158, 18], [1481, 22, 1158, 20], [1481, 30, 1158, 28], [1482, 6, 1159, 4, "alignItems"], [1482, 16, 1159, 14], [1482, 18, 1159, 16], [1482, 26, 1159, 24], [1483, 6, 1160, 4, "marginBottom"], [1483, 18, 1160, 16], [1483, 20, 1160, 18], [1483, 22, 1160, 20], [1484, 6, 1161, 4], [1484, 9, 1161, 7, "Platform"], [1484, 26, 1161, 15], [1484, 27, 1161, 16, "select"], [1484, 33, 1161, 22], [1484, 34, 1161, 23], [1485, 8, 1162, 6, "ios"], [1485, 11, 1162, 9], [1485, 13, 1162, 11], [1486, 10, 1163, 8, "shadowColor"], [1486, 21, 1163, 19], [1486, 23, 1163, 21], [1486, 32, 1163, 30], [1487, 10, 1164, 8, "shadowOffset"], [1487, 22, 1164, 20], [1487, 24, 1164, 22], [1488, 12, 1164, 24, "width"], [1488, 17, 1164, 29], [1488, 19, 1164, 31], [1488, 20, 1164, 32], [1489, 12, 1164, 34, "height"], [1489, 18, 1164, 40], [1489, 20, 1164, 42], [1490, 10, 1164, 44], [1490, 11, 1164, 45], [1491, 10, 1165, 8, "shadowOpacity"], [1491, 23, 1165, 21], [1491, 25, 1165, 23], [1491, 28, 1165, 26], [1492, 10, 1166, 8, "shadowRadius"], [1492, 22, 1166, 20], [1492, 24, 1166, 22], [1493, 8, 1167, 6], [1493, 9, 1167, 7], [1494, 8, 1168, 6, "android"], [1494, 15, 1168, 13], [1494, 17, 1168, 15], [1495, 10, 1169, 8, "elevation"], [1495, 19, 1169, 17], [1495, 21, 1169, 19], [1496, 8, 1170, 6], [1496, 9, 1170, 7], [1497, 8, 1171, 6, "web"], [1497, 11, 1171, 9], [1497, 13, 1171, 11], [1498, 10, 1172, 8, "boxShadow"], [1498, 19, 1172, 17], [1498, 21, 1172, 19], [1499, 8, 1173, 6], [1500, 6, 1174, 4], [1500, 7, 1174, 5], [1501, 4, 1175, 2], [1501, 5, 1175, 3], [1502, 4, 1176, 2, "shutterButtonDisabled"], [1502, 25, 1176, 23], [1502, 27, 1176, 25], [1503, 6, 1177, 4, "opacity"], [1503, 13, 1177, 11], [1503, 15, 1177, 13], [1504, 4, 1178, 2], [1504, 5, 1178, 3], [1505, 4, 1179, 2, "shutterInner"], [1505, 16, 1179, 14], [1505, 18, 1179, 16], [1506, 6, 1180, 4, "width"], [1506, 11, 1180, 9], [1506, 13, 1180, 11], [1506, 15, 1180, 13], [1507, 6, 1181, 4, "height"], [1507, 12, 1181, 10], [1507, 14, 1181, 12], [1507, 16, 1181, 14], [1508, 6, 1182, 4, "borderRadius"], [1508, 18, 1182, 16], [1508, 20, 1182, 18], [1508, 22, 1182, 20], [1509, 6, 1183, 4, "backgroundColor"], [1509, 21, 1183, 19], [1509, 23, 1183, 21], [1509, 29, 1183, 27], [1510, 6, 1184, 4, "borderWidth"], [1510, 17, 1184, 15], [1510, 19, 1184, 17], [1510, 20, 1184, 18], [1511, 6, 1185, 4, "borderColor"], [1511, 17, 1185, 15], [1511, 19, 1185, 17], [1512, 4, 1186, 2], [1512, 5, 1186, 3], [1513, 4, 1187, 2, "privacyNote"], [1513, 15, 1187, 13], [1513, 17, 1187, 15], [1514, 6, 1188, 4, "fontSize"], [1514, 14, 1188, 12], [1514, 16, 1188, 14], [1514, 18, 1188, 16], [1515, 6, 1189, 4, "color"], [1515, 11, 1189, 9], [1515, 13, 1189, 11], [1516, 4, 1190, 2], [1516, 5, 1190, 3], [1517, 4, 1191, 2, "processingModal"], [1517, 19, 1191, 17], [1517, 21, 1191, 19], [1518, 6, 1192, 4, "flex"], [1518, 10, 1192, 8], [1518, 12, 1192, 10], [1518, 13, 1192, 11], [1519, 6, 1193, 4, "backgroundColor"], [1519, 21, 1193, 19], [1519, 23, 1193, 21], [1519, 43, 1193, 41], [1520, 6, 1194, 4, "justifyContent"], [1520, 20, 1194, 18], [1520, 22, 1194, 20], [1520, 30, 1194, 28], [1521, 6, 1195, 4, "alignItems"], [1521, 16, 1195, 14], [1521, 18, 1195, 16], [1522, 4, 1196, 2], [1522, 5, 1196, 3], [1523, 4, 1197, 2, "processingContent"], [1523, 21, 1197, 19], [1523, 23, 1197, 21], [1524, 6, 1198, 4, "backgroundColor"], [1524, 21, 1198, 19], [1524, 23, 1198, 21], [1524, 29, 1198, 27], [1525, 6, 1199, 4, "borderRadius"], [1525, 18, 1199, 16], [1525, 20, 1199, 18], [1525, 22, 1199, 20], [1526, 6, 1200, 4, "padding"], [1526, 13, 1200, 11], [1526, 15, 1200, 13], [1526, 17, 1200, 15], [1527, 6, 1201, 4, "width"], [1527, 11, 1201, 9], [1527, 13, 1201, 11], [1527, 18, 1201, 16], [1528, 6, 1202, 4, "max<PERSON><PERSON><PERSON>"], [1528, 14, 1202, 12], [1528, 16, 1202, 14], [1528, 19, 1202, 17], [1529, 6, 1203, 4, "alignItems"], [1529, 16, 1203, 14], [1529, 18, 1203, 16], [1530, 4, 1204, 2], [1530, 5, 1204, 3], [1531, 4, 1205, 2, "processingTitle"], [1531, 19, 1205, 17], [1531, 21, 1205, 19], [1532, 6, 1206, 4, "fontSize"], [1532, 14, 1206, 12], [1532, 16, 1206, 14], [1532, 18, 1206, 16], [1533, 6, 1207, 4, "fontWeight"], [1533, 16, 1207, 14], [1533, 18, 1207, 16], [1533, 23, 1207, 21], [1534, 6, 1208, 4, "color"], [1534, 11, 1208, 9], [1534, 13, 1208, 11], [1534, 22, 1208, 20], [1535, 6, 1209, 4, "marginTop"], [1535, 15, 1209, 13], [1535, 17, 1209, 15], [1535, 19, 1209, 17], [1536, 6, 1210, 4, "marginBottom"], [1536, 18, 1210, 16], [1536, 20, 1210, 18], [1537, 4, 1211, 2], [1537, 5, 1211, 3], [1538, 4, 1212, 2, "progressBar"], [1538, 15, 1212, 13], [1538, 17, 1212, 15], [1539, 6, 1213, 4, "width"], [1539, 11, 1213, 9], [1539, 13, 1213, 11], [1539, 19, 1213, 17], [1540, 6, 1214, 4, "height"], [1540, 12, 1214, 10], [1540, 14, 1214, 12], [1540, 15, 1214, 13], [1541, 6, 1215, 4, "backgroundColor"], [1541, 21, 1215, 19], [1541, 23, 1215, 21], [1541, 32, 1215, 30], [1542, 6, 1216, 4, "borderRadius"], [1542, 18, 1216, 16], [1542, 20, 1216, 18], [1542, 21, 1216, 19], [1543, 6, 1217, 4, "overflow"], [1543, 14, 1217, 12], [1543, 16, 1217, 14], [1543, 24, 1217, 22], [1544, 6, 1218, 4, "marginBottom"], [1544, 18, 1218, 16], [1544, 20, 1218, 18], [1545, 4, 1219, 2], [1545, 5, 1219, 3], [1546, 4, 1220, 2, "progressFill"], [1546, 16, 1220, 14], [1546, 18, 1220, 16], [1547, 6, 1221, 4, "height"], [1547, 12, 1221, 10], [1547, 14, 1221, 12], [1547, 20, 1221, 18], [1548, 6, 1222, 4, "backgroundColor"], [1548, 21, 1222, 19], [1548, 23, 1222, 21], [1548, 32, 1222, 30], [1549, 6, 1223, 4, "borderRadius"], [1549, 18, 1223, 16], [1549, 20, 1223, 18], [1550, 4, 1224, 2], [1550, 5, 1224, 3], [1551, 4, 1225, 2, "processingDescription"], [1551, 25, 1225, 23], [1551, 27, 1225, 25], [1552, 6, 1226, 4, "fontSize"], [1552, 14, 1226, 12], [1552, 16, 1226, 14], [1552, 18, 1226, 16], [1553, 6, 1227, 4, "color"], [1553, 11, 1227, 9], [1553, 13, 1227, 11], [1553, 22, 1227, 20], [1554, 6, 1228, 4, "textAlign"], [1554, 15, 1228, 13], [1554, 17, 1228, 15], [1555, 4, 1229, 2], [1555, 5, 1229, 3], [1556, 4, 1230, 2, "successIcon"], [1556, 15, 1230, 13], [1556, 17, 1230, 15], [1557, 6, 1231, 4, "marginTop"], [1557, 15, 1231, 13], [1557, 17, 1231, 15], [1558, 4, 1232, 2], [1558, 5, 1232, 3], [1559, 4, 1233, 2, "errorContent"], [1559, 16, 1233, 14], [1559, 18, 1233, 16], [1560, 6, 1234, 4, "backgroundColor"], [1560, 21, 1234, 19], [1560, 23, 1234, 21], [1560, 29, 1234, 27], [1561, 6, 1235, 4, "borderRadius"], [1561, 18, 1235, 16], [1561, 20, 1235, 18], [1561, 22, 1235, 20], [1562, 6, 1236, 4, "padding"], [1562, 13, 1236, 11], [1562, 15, 1236, 13], [1562, 17, 1236, 15], [1563, 6, 1237, 4, "width"], [1563, 11, 1237, 9], [1563, 13, 1237, 11], [1563, 18, 1237, 16], [1564, 6, 1238, 4, "max<PERSON><PERSON><PERSON>"], [1564, 14, 1238, 12], [1564, 16, 1238, 14], [1564, 19, 1238, 17], [1565, 6, 1239, 4, "alignItems"], [1565, 16, 1239, 14], [1565, 18, 1239, 16], [1566, 4, 1240, 2], [1566, 5, 1240, 3], [1567, 4, 1241, 2, "errorTitle"], [1567, 14, 1241, 12], [1567, 16, 1241, 14], [1568, 6, 1242, 4, "fontSize"], [1568, 14, 1242, 12], [1568, 16, 1242, 14], [1568, 18, 1242, 16], [1569, 6, 1243, 4, "fontWeight"], [1569, 16, 1243, 14], [1569, 18, 1243, 16], [1569, 23, 1243, 21], [1570, 6, 1244, 4, "color"], [1570, 11, 1244, 9], [1570, 13, 1244, 11], [1570, 22, 1244, 20], [1571, 6, 1245, 4, "marginTop"], [1571, 15, 1245, 13], [1571, 17, 1245, 15], [1571, 19, 1245, 17], [1572, 6, 1246, 4, "marginBottom"], [1572, 18, 1246, 16], [1572, 20, 1246, 18], [1573, 4, 1247, 2], [1573, 5, 1247, 3], [1574, 4, 1248, 2, "errorMessage"], [1574, 16, 1248, 14], [1574, 18, 1248, 16], [1575, 6, 1249, 4, "fontSize"], [1575, 14, 1249, 12], [1575, 16, 1249, 14], [1575, 18, 1249, 16], [1576, 6, 1250, 4, "color"], [1576, 11, 1250, 9], [1576, 13, 1250, 11], [1576, 22, 1250, 20], [1577, 6, 1251, 4, "textAlign"], [1577, 15, 1251, 13], [1577, 17, 1251, 15], [1577, 25, 1251, 23], [1578, 6, 1252, 4, "marginBottom"], [1578, 18, 1252, 16], [1578, 20, 1252, 18], [1579, 4, 1253, 2], [1579, 5, 1253, 3], [1580, 4, 1254, 2, "primaryButton"], [1580, 17, 1254, 15], [1580, 19, 1254, 17], [1581, 6, 1255, 4, "backgroundColor"], [1581, 21, 1255, 19], [1581, 23, 1255, 21], [1581, 32, 1255, 30], [1582, 6, 1256, 4, "paddingHorizontal"], [1582, 23, 1256, 21], [1582, 25, 1256, 23], [1582, 27, 1256, 25], [1583, 6, 1257, 4, "paddingVertical"], [1583, 21, 1257, 19], [1583, 23, 1257, 21], [1583, 25, 1257, 23], [1584, 6, 1258, 4, "borderRadius"], [1584, 18, 1258, 16], [1584, 20, 1258, 18], [1584, 21, 1258, 19], [1585, 6, 1259, 4, "marginTop"], [1585, 15, 1259, 13], [1585, 17, 1259, 15], [1586, 4, 1260, 2], [1586, 5, 1260, 3], [1587, 4, 1261, 2, "primaryButtonText"], [1587, 21, 1261, 19], [1587, 23, 1261, 21], [1588, 6, 1262, 4, "color"], [1588, 11, 1262, 9], [1588, 13, 1262, 11], [1588, 19, 1262, 17], [1589, 6, 1263, 4, "fontSize"], [1589, 14, 1263, 12], [1589, 16, 1263, 14], [1589, 18, 1263, 16], [1590, 6, 1264, 4, "fontWeight"], [1590, 16, 1264, 14], [1590, 18, 1264, 16], [1591, 4, 1265, 2], [1591, 5, 1265, 3], [1592, 4, 1266, 2, "secondaryButton"], [1592, 19, 1266, 17], [1592, 21, 1266, 19], [1593, 6, 1267, 4, "paddingHorizontal"], [1593, 23, 1267, 21], [1593, 25, 1267, 23], [1593, 27, 1267, 25], [1594, 6, 1268, 4, "paddingVertical"], [1594, 21, 1268, 19], [1594, 23, 1268, 21], [1594, 25, 1268, 23], [1595, 6, 1269, 4, "marginTop"], [1595, 15, 1269, 13], [1595, 17, 1269, 15], [1596, 4, 1270, 2], [1596, 5, 1270, 3], [1597, 4, 1271, 2, "secondaryButtonText"], [1597, 23, 1271, 21], [1597, 25, 1271, 23], [1598, 6, 1272, 4, "color"], [1598, 11, 1272, 9], [1598, 13, 1272, 11], [1598, 22, 1272, 20], [1599, 6, 1273, 4, "fontSize"], [1599, 14, 1273, 12], [1599, 16, 1273, 14], [1600, 4, 1274, 2], [1600, 5, 1274, 3], [1601, 4, 1275, 2, "permissionContent"], [1601, 21, 1275, 19], [1601, 23, 1275, 21], [1602, 6, 1276, 4, "flex"], [1602, 10, 1276, 8], [1602, 12, 1276, 10], [1602, 13, 1276, 11], [1603, 6, 1277, 4, "justifyContent"], [1603, 20, 1277, 18], [1603, 22, 1277, 20], [1603, 30, 1277, 28], [1604, 6, 1278, 4, "alignItems"], [1604, 16, 1278, 14], [1604, 18, 1278, 16], [1604, 26, 1278, 24], [1605, 6, 1279, 4, "padding"], [1605, 13, 1279, 11], [1605, 15, 1279, 13], [1606, 4, 1280, 2], [1606, 5, 1280, 3], [1607, 4, 1281, 2, "permissionTitle"], [1607, 19, 1281, 17], [1607, 21, 1281, 19], [1608, 6, 1282, 4, "fontSize"], [1608, 14, 1282, 12], [1608, 16, 1282, 14], [1608, 18, 1282, 16], [1609, 6, 1283, 4, "fontWeight"], [1609, 16, 1283, 14], [1609, 18, 1283, 16], [1609, 23, 1283, 21], [1610, 6, 1284, 4, "color"], [1610, 11, 1284, 9], [1610, 13, 1284, 11], [1610, 22, 1284, 20], [1611, 6, 1285, 4, "marginTop"], [1611, 15, 1285, 13], [1611, 17, 1285, 15], [1611, 19, 1285, 17], [1612, 6, 1286, 4, "marginBottom"], [1612, 18, 1286, 16], [1612, 20, 1286, 18], [1613, 4, 1287, 2], [1613, 5, 1287, 3], [1614, 4, 1288, 2, "permissionDescription"], [1614, 25, 1288, 23], [1614, 27, 1288, 25], [1615, 6, 1289, 4, "fontSize"], [1615, 14, 1289, 12], [1615, 16, 1289, 14], [1615, 18, 1289, 16], [1616, 6, 1290, 4, "color"], [1616, 11, 1290, 9], [1616, 13, 1290, 11], [1616, 22, 1290, 20], [1617, 6, 1291, 4, "textAlign"], [1617, 15, 1291, 13], [1617, 17, 1291, 15], [1617, 25, 1291, 23], [1618, 6, 1292, 4, "marginBottom"], [1618, 18, 1292, 16], [1618, 20, 1292, 18], [1619, 4, 1293, 2], [1619, 5, 1293, 3], [1620, 4, 1294, 2, "loadingText"], [1620, 15, 1294, 13], [1620, 17, 1294, 15], [1621, 6, 1295, 4, "color"], [1621, 11, 1295, 9], [1621, 13, 1295, 11], [1621, 22, 1295, 20], [1622, 6, 1296, 4, "marginTop"], [1622, 15, 1296, 13], [1622, 17, 1296, 15], [1623, 4, 1297, 2], [1623, 5, 1297, 3], [1624, 4, 1298, 2], [1625, 4, 1299, 2, "blurZone"], [1625, 12, 1299, 10], [1625, 14, 1299, 12], [1626, 6, 1300, 4, "position"], [1626, 14, 1300, 12], [1626, 16, 1300, 14], [1626, 26, 1300, 24], [1627, 6, 1301, 4, "overflow"], [1627, 14, 1301, 12], [1627, 16, 1301, 14], [1628, 4, 1302, 2], [1628, 5, 1302, 3], [1629, 4, 1303, 2, "previewChip"], [1629, 15, 1303, 13], [1629, 17, 1303, 15], [1630, 6, 1304, 4, "position"], [1630, 14, 1304, 12], [1630, 16, 1304, 14], [1630, 26, 1304, 24], [1631, 6, 1305, 4, "top"], [1631, 9, 1305, 7], [1631, 11, 1305, 9], [1631, 12, 1305, 10], [1632, 6, 1306, 4, "right"], [1632, 11, 1306, 9], [1632, 13, 1306, 11], [1632, 14, 1306, 12], [1633, 6, 1307, 4, "backgroundColor"], [1633, 21, 1307, 19], [1633, 23, 1307, 21], [1633, 40, 1307, 38], [1634, 6, 1308, 4, "paddingHorizontal"], [1634, 23, 1308, 21], [1634, 25, 1308, 23], [1634, 27, 1308, 25], [1635, 6, 1309, 4, "paddingVertical"], [1635, 21, 1309, 19], [1635, 23, 1309, 21], [1635, 24, 1309, 22], [1636, 6, 1310, 4, "borderRadius"], [1636, 18, 1310, 16], [1636, 20, 1310, 18], [1637, 4, 1311, 2], [1637, 5, 1311, 3], [1638, 4, 1312, 2, "previewChipText"], [1638, 19, 1312, 17], [1638, 21, 1312, 19], [1639, 6, 1313, 4, "color"], [1639, 11, 1313, 9], [1639, 13, 1313, 11], [1639, 19, 1313, 17], [1640, 6, 1314, 4, "fontSize"], [1640, 14, 1314, 12], [1640, 16, 1314, 14], [1640, 18, 1314, 16], [1641, 6, 1315, 4, "fontWeight"], [1641, 16, 1315, 14], [1641, 18, 1315, 16], [1642, 4, 1316, 2], [1643, 2, 1317, 0], [1643, 3, 1317, 1], [1643, 4, 1317, 2], [1644, 2, 1317, 3], [1644, 6, 1317, 3, "_c"], [1644, 8, 1317, 3], [1645, 2, 1317, 3, "$RefreshReg$"], [1645, 14, 1317, 3], [1645, 15, 1317, 3, "_c"], [1645, 17, 1317, 3], [1646, 0, 1317, 3], [1646, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdmE;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCyC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}