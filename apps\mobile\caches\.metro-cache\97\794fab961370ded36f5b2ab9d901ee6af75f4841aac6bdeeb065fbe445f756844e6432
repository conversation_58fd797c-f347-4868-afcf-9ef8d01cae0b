{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 877,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 935,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1029,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1021,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 841,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1633, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4], [304, 6, 336, 4], [304, 12, 336, 10, "canvasWidth"], [304, 23, 336, 21], [304, 26, 336, 24, "ctx"], [304, 29, 336, 27], [304, 30, 336, 28, "canvas"], [304, 36, 336, 34], [304, 37, 336, 35, "width"], [304, 42, 336, 40], [305, 6, 337, 4], [305, 12, 337, 10, "canvasHeight"], [305, 24, 337, 22], [305, 27, 337, 25, "ctx"], [305, 30, 337, 28], [305, 31, 337, 29, "canvas"], [305, 37, 337, 35], [305, 38, 337, 36, "height"], [305, 44, 337, 42], [306, 6, 339, 4], [306, 12, 339, 10, "clampedX"], [306, 20, 339, 18], [306, 23, 339, 21, "Math"], [306, 27, 339, 25], [306, 28, 339, 26, "max"], [306, 31, 339, 29], [306, 32, 339, 30], [306, 33, 339, 31], [306, 35, 339, 33, "Math"], [306, 39, 339, 37], [306, 40, 339, 38, "min"], [306, 43, 339, 41], [306, 44, 339, 42, "Math"], [306, 48, 339, 46], [306, 49, 339, 47, "floor"], [306, 54, 339, 52], [306, 55, 339, 53, "x"], [306, 56, 339, 54], [306, 57, 339, 55], [306, 59, 339, 57, "canvasWidth"], [306, 70, 339, 68], [306, 73, 339, 71], [306, 74, 339, 72], [306, 75, 339, 73], [306, 76, 339, 74], [307, 6, 340, 4], [307, 12, 340, 10, "clampedY"], [307, 20, 340, 18], [307, 23, 340, 21, "Math"], [307, 27, 340, 25], [307, 28, 340, 26, "max"], [307, 31, 340, 29], [307, 32, 340, 30], [307, 33, 340, 31], [307, 35, 340, 33, "Math"], [307, 39, 340, 37], [307, 40, 340, 38, "min"], [307, 43, 340, 41], [307, 44, 340, 42, "Math"], [307, 48, 340, 46], [307, 49, 340, 47, "floor"], [307, 54, 340, 52], [307, 55, 340, 53, "y"], [307, 56, 340, 54], [307, 57, 340, 55], [307, 59, 340, 57, "canvasHeight"], [307, 71, 340, 69], [307, 74, 340, 72], [307, 75, 340, 73], [307, 76, 340, 74], [307, 77, 340, 75], [308, 6, 341, 4], [308, 12, 341, 10, "<PERSON><PERSON><PERSON><PERSON>"], [308, 24, 341, 22], [308, 27, 341, 25, "Math"], [308, 31, 341, 29], [308, 32, 341, 30, "min"], [308, 35, 341, 33], [308, 36, 341, 34, "Math"], [308, 40, 341, 38], [308, 41, 341, 39, "floor"], [308, 46, 341, 44], [308, 47, 341, 45, "width"], [308, 52, 341, 50], [308, 53, 341, 51], [308, 55, 341, 53, "canvasWidth"], [308, 66, 341, 64], [308, 69, 341, 67, "clampedX"], [308, 77, 341, 75], [308, 78, 341, 76], [309, 6, 342, 4], [309, 12, 342, 10, "clampedHeight"], [309, 25, 342, 23], [309, 28, 342, 26, "Math"], [309, 32, 342, 30], [309, 33, 342, 31, "min"], [309, 36, 342, 34], [309, 37, 342, 35, "Math"], [309, 41, 342, 39], [309, 42, 342, 40, "floor"], [309, 47, 342, 45], [309, 48, 342, 46, "height"], [309, 54, 342, 52], [309, 55, 342, 53], [309, 57, 342, 55, "canvasHeight"], [309, 69, 342, 67], [309, 72, 342, 70, "clampedY"], [309, 80, 342, 78], [309, 81, 342, 79], [310, 6, 344, 4], [310, 10, 344, 8, "<PERSON><PERSON><PERSON><PERSON>"], [310, 22, 344, 20], [310, 26, 344, 24], [310, 27, 344, 25], [310, 31, 344, 29, "clampedHeight"], [310, 44, 344, 42], [310, 48, 344, 46], [310, 49, 344, 47], [310, 51, 344, 49], [311, 8, 345, 6, "console"], [311, 15, 345, 13], [311, 16, 345, 14, "warn"], [311, 20, 345, 18], [311, 21, 345, 19], [311, 72, 345, 70], [311, 73, 345, 71], [312, 8, 346, 6], [313, 6, 347, 4], [315, 6, 349, 4], [316, 6, 350, 4], [316, 12, 350, 10, "imageData"], [316, 21, 350, 19], [316, 24, 350, 22, "ctx"], [316, 27, 350, 25], [316, 28, 350, 26, "getImageData"], [316, 40, 350, 38], [316, 41, 350, 39, "clampedX"], [316, 49, 350, 47], [316, 51, 350, 49, "clampedY"], [316, 59, 350, 57], [316, 61, 350, 59, "<PERSON><PERSON><PERSON><PERSON>"], [316, 73, 350, 71], [316, 75, 350, 73, "clampedHeight"], [316, 88, 350, 86], [316, 89, 350, 87], [317, 6, 351, 4], [317, 12, 351, 10, "data"], [317, 16, 351, 14], [317, 19, 351, 17, "imageData"], [317, 28, 351, 26], [317, 29, 351, 27, "data"], [317, 33, 351, 31], [319, 6, 353, 4], [320, 6, 354, 4], [320, 12, 354, 10, "pixelSize"], [320, 21, 354, 19], [320, 24, 354, 22, "Math"], [320, 28, 354, 26], [320, 29, 354, 27, "max"], [320, 32, 354, 30], [320, 33, 354, 31], [320, 35, 354, 33], [320, 37, 354, 35, "Math"], [320, 41, 354, 39], [320, 42, 354, 40, "min"], [320, 45, 354, 43], [320, 46, 354, 44, "<PERSON><PERSON><PERSON><PERSON>"], [320, 58, 354, 56], [320, 60, 354, 58, "clampedHeight"], [320, 73, 354, 71], [320, 74, 354, 72], [320, 77, 354, 75], [320, 78, 354, 76], [320, 79, 354, 77], [321, 6, 356, 4], [321, 11, 356, 9], [321, 15, 356, 13, "py"], [321, 17, 356, 15], [321, 20, 356, 18], [321, 21, 356, 19], [321, 23, 356, 21, "py"], [321, 25, 356, 23], [321, 28, 356, 26, "clampedHeight"], [321, 41, 356, 39], [321, 43, 356, 41, "py"], [321, 45, 356, 43], [321, 49, 356, 47, "pixelSize"], [321, 58, 356, 56], [321, 60, 356, 58], [322, 8, 357, 6], [322, 13, 357, 11], [322, 17, 357, 15, "px"], [322, 19, 357, 17], [322, 22, 357, 20], [322, 23, 357, 21], [322, 25, 357, 23, "px"], [322, 27, 357, 25], [322, 30, 357, 28, "<PERSON><PERSON><PERSON><PERSON>"], [322, 42, 357, 40], [322, 44, 357, 42, "px"], [322, 46, 357, 44], [322, 50, 357, 48, "pixelSize"], [322, 59, 357, 57], [322, 61, 357, 59], [323, 10, 358, 8], [324, 10, 359, 8], [324, 14, 359, 12, "r"], [324, 15, 359, 13], [324, 18, 359, 16], [324, 19, 359, 17], [325, 12, 359, 19, "g"], [325, 13, 359, 20], [325, 16, 359, 23], [325, 17, 359, 24], [326, 12, 359, 26, "b"], [326, 13, 359, 27], [326, 16, 359, 30], [326, 17, 359, 31], [327, 12, 359, 33, "count"], [327, 17, 359, 38], [327, 20, 359, 41], [327, 21, 359, 42], [328, 10, 361, 8], [328, 15, 361, 13], [328, 19, 361, 17, "dy"], [328, 21, 361, 19], [328, 24, 361, 22], [328, 25, 361, 23], [328, 27, 361, 25, "dy"], [328, 29, 361, 27], [328, 32, 361, 30, "pixelSize"], [328, 41, 361, 39], [328, 45, 361, 43, "py"], [328, 47, 361, 45], [328, 50, 361, 48, "dy"], [328, 52, 361, 50], [328, 55, 361, 53, "clampedHeight"], [328, 68, 361, 66], [328, 70, 361, 68, "dy"], [328, 72, 361, 70], [328, 74, 361, 72], [328, 76, 361, 74], [329, 12, 362, 10], [329, 17, 362, 15], [329, 21, 362, 19, "dx"], [329, 23, 362, 21], [329, 26, 362, 24], [329, 27, 362, 25], [329, 29, 362, 27, "dx"], [329, 31, 362, 29], [329, 34, 362, 32, "pixelSize"], [329, 43, 362, 41], [329, 47, 362, 45, "px"], [329, 49, 362, 47], [329, 52, 362, 50, "dx"], [329, 54, 362, 52], [329, 57, 362, 55, "<PERSON><PERSON><PERSON><PERSON>"], [329, 69, 362, 67], [329, 71, 362, 69, "dx"], [329, 73, 362, 71], [329, 75, 362, 73], [329, 77, 362, 75], [330, 14, 363, 12], [330, 20, 363, 18, "index"], [330, 25, 363, 23], [330, 28, 363, 26], [330, 29, 363, 27], [330, 30, 363, 28, "py"], [330, 32, 363, 30], [330, 35, 363, 33, "dy"], [330, 37, 363, 35], [330, 41, 363, 39, "<PERSON><PERSON><PERSON><PERSON>"], [330, 53, 363, 51], [330, 57, 363, 55, "px"], [330, 59, 363, 57], [330, 62, 363, 60, "dx"], [330, 64, 363, 62], [330, 65, 363, 63], [330, 69, 363, 67], [330, 70, 363, 68], [331, 14, 364, 12, "r"], [331, 15, 364, 13], [331, 19, 364, 17, "data"], [331, 23, 364, 21], [331, 24, 364, 22, "index"], [331, 29, 364, 27], [331, 30, 364, 28], [332, 14, 365, 12, "g"], [332, 15, 365, 13], [332, 19, 365, 17, "data"], [332, 23, 365, 21], [332, 24, 365, 22, "index"], [332, 29, 365, 27], [332, 32, 365, 30], [332, 33, 365, 31], [332, 34, 365, 32], [333, 14, 366, 12, "b"], [333, 15, 366, 13], [333, 19, 366, 17, "data"], [333, 23, 366, 21], [333, 24, 366, 22, "index"], [333, 29, 366, 27], [333, 32, 366, 30], [333, 33, 366, 31], [333, 34, 366, 32], [334, 14, 367, 12, "count"], [334, 19, 367, 17], [334, 21, 367, 19], [335, 12, 368, 10], [336, 10, 369, 8], [337, 10, 371, 8], [337, 14, 371, 12, "count"], [337, 19, 371, 17], [337, 22, 371, 20], [337, 23, 371, 21], [337, 25, 371, 23], [338, 12, 372, 10, "r"], [338, 13, 372, 11], [338, 16, 372, 14, "Math"], [338, 20, 372, 18], [338, 21, 372, 19, "floor"], [338, 26, 372, 24], [338, 27, 372, 25, "r"], [338, 28, 372, 26], [338, 31, 372, 29, "count"], [338, 36, 372, 34], [338, 37, 372, 35], [339, 12, 373, 10, "g"], [339, 13, 373, 11], [339, 16, 373, 14, "Math"], [339, 20, 373, 18], [339, 21, 373, 19, "floor"], [339, 26, 373, 24], [339, 27, 373, 25, "g"], [339, 28, 373, 26], [339, 31, 373, 29, "count"], [339, 36, 373, 34], [339, 37, 373, 35], [340, 12, 374, 10, "b"], [340, 13, 374, 11], [340, 16, 374, 14, "Math"], [340, 20, 374, 18], [340, 21, 374, 19, "floor"], [340, 26, 374, 24], [340, 27, 374, 25, "b"], [340, 28, 374, 26], [340, 31, 374, 29, "count"], [340, 36, 374, 34], [340, 37, 374, 35], [342, 12, 376, 10], [343, 12, 377, 10], [343, 17, 377, 15], [343, 21, 377, 19, "dy"], [343, 23, 377, 21], [343, 26, 377, 24], [343, 27, 377, 25], [343, 29, 377, 27, "dy"], [343, 31, 377, 29], [343, 34, 377, 32, "pixelSize"], [343, 43, 377, 41], [343, 47, 377, 45, "py"], [343, 49, 377, 47], [343, 52, 377, 50, "dy"], [343, 54, 377, 52], [343, 57, 377, 55, "clampedHeight"], [343, 70, 377, 68], [343, 72, 377, 70, "dy"], [343, 74, 377, 72], [343, 76, 377, 74], [343, 78, 377, 76], [344, 14, 378, 12], [344, 19, 378, 17], [344, 23, 378, 21, "dx"], [344, 25, 378, 23], [344, 28, 378, 26], [344, 29, 378, 27], [344, 31, 378, 29, "dx"], [344, 33, 378, 31], [344, 36, 378, 34, "pixelSize"], [344, 45, 378, 43], [344, 49, 378, 47, "px"], [344, 51, 378, 49], [344, 54, 378, 52, "dx"], [344, 56, 378, 54], [344, 59, 378, 57, "<PERSON><PERSON><PERSON><PERSON>"], [344, 71, 378, 69], [344, 73, 378, 71, "dx"], [344, 75, 378, 73], [344, 77, 378, 75], [344, 79, 378, 77], [345, 16, 379, 14], [345, 22, 379, 20, "index"], [345, 27, 379, 25], [345, 30, 379, 28], [345, 31, 379, 29], [345, 32, 379, 30, "py"], [345, 34, 379, 32], [345, 37, 379, 35, "dy"], [345, 39, 379, 37], [345, 43, 379, 41, "<PERSON><PERSON><PERSON><PERSON>"], [345, 55, 379, 53], [345, 59, 379, 57, "px"], [345, 61, 379, 59], [345, 64, 379, 62, "dx"], [345, 66, 379, 64], [345, 67, 379, 65], [345, 71, 379, 69], [345, 72, 379, 70], [346, 16, 380, 14, "data"], [346, 20, 380, 18], [346, 21, 380, 19, "index"], [346, 26, 380, 24], [346, 27, 380, 25], [346, 30, 380, 28, "r"], [346, 31, 380, 29], [347, 16, 381, 14, "data"], [347, 20, 381, 18], [347, 21, 381, 19, "index"], [347, 26, 381, 24], [347, 29, 381, 27], [347, 30, 381, 28], [347, 31, 381, 29], [347, 34, 381, 32, "g"], [347, 35, 381, 33], [348, 16, 382, 14, "data"], [348, 20, 382, 18], [348, 21, 382, 19, "index"], [348, 26, 382, 24], [348, 29, 382, 27], [348, 30, 382, 28], [348, 31, 382, 29], [348, 34, 382, 32, "b"], [348, 35, 382, 33], [349, 16, 383, 14], [350, 14, 384, 12], [351, 12, 385, 10], [352, 10, 386, 8], [353, 8, 387, 6], [354, 6, 388, 4], [356, 6, 390, 4], [357, 6, 391, 4], [357, 11, 391, 9], [357, 15, 391, 13, "i"], [357, 16, 391, 14], [357, 19, 391, 17], [357, 20, 391, 18], [357, 22, 391, 20, "i"], [357, 23, 391, 21], [357, 26, 391, 24], [357, 27, 391, 25], [357, 29, 391, 27, "i"], [357, 30, 391, 28], [357, 32, 391, 30], [357, 34, 391, 32], [358, 8, 392, 6, "applySimpleBlur"], [358, 23, 392, 21], [358, 24, 392, 22, "data"], [358, 28, 392, 26], [358, 30, 392, 28, "<PERSON><PERSON><PERSON><PERSON>"], [358, 42, 392, 40], [358, 44, 392, 42, "clampedHeight"], [358, 57, 392, 55], [358, 58, 392, 56], [359, 6, 393, 4], [361, 6, 395, 4], [362, 6, 396, 4, "ctx"], [362, 9, 396, 7], [362, 10, 396, 8, "putImageData"], [362, 22, 396, 20], [362, 23, 396, 21, "imageData"], [362, 32, 396, 30], [362, 34, 396, 32, "clampedX"], [362, 42, 396, 40], [362, 44, 396, 42, "clampedY"], [362, 52, 396, 50], [362, 53, 396, 51], [364, 6, 398, 4], [365, 6, 399, 4, "ctx"], [365, 9, 399, 7], [365, 10, 399, 8, "fillStyle"], [365, 19, 399, 17], [365, 22, 399, 20], [365, 48, 399, 46], [366, 6, 400, 4, "ctx"], [366, 9, 400, 7], [366, 10, 400, 8, "fillRect"], [366, 18, 400, 16], [366, 19, 400, 17, "clampedX"], [366, 27, 400, 25], [366, 29, 400, 27, "clampedY"], [366, 37, 400, 35], [366, 39, 400, 37, "<PERSON><PERSON><PERSON><PERSON>"], [366, 51, 400, 49], [366, 53, 400, 51, "clampedHeight"], [366, 66, 400, 64], [366, 67, 400, 65], [367, 4, 401, 2], [367, 5, 401, 3], [368, 4, 403, 2], [368, 10, 403, 8, "applySimpleBlur"], [368, 25, 403, 23], [368, 28, 403, 26, "applySimpleBlur"], [368, 29, 403, 27, "data"], [368, 33, 403, 50], [368, 35, 403, 52, "width"], [368, 40, 403, 65], [368, 42, 403, 67, "height"], [368, 48, 403, 81], [368, 53, 403, 86], [369, 6, 404, 4], [369, 12, 404, 10, "original"], [369, 20, 404, 18], [369, 23, 404, 21], [369, 27, 404, 25, "Uint8ClampedArray"], [369, 44, 404, 42], [369, 45, 404, 43, "data"], [369, 49, 404, 47], [369, 50, 404, 48], [370, 6, 406, 4], [370, 11, 406, 9], [370, 15, 406, 13, "y"], [370, 16, 406, 14], [370, 19, 406, 17], [370, 20, 406, 18], [370, 22, 406, 20, "y"], [370, 23, 406, 21], [370, 26, 406, 24, "height"], [370, 32, 406, 30], [370, 35, 406, 33], [370, 36, 406, 34], [370, 38, 406, 36, "y"], [370, 39, 406, 37], [370, 41, 406, 39], [370, 43, 406, 41], [371, 8, 407, 6], [371, 13, 407, 11], [371, 17, 407, 15, "x"], [371, 18, 407, 16], [371, 21, 407, 19], [371, 22, 407, 20], [371, 24, 407, 22, "x"], [371, 25, 407, 23], [371, 28, 407, 26, "width"], [371, 33, 407, 31], [371, 36, 407, 34], [371, 37, 407, 35], [371, 39, 407, 37, "x"], [371, 40, 407, 38], [371, 42, 407, 40], [371, 44, 407, 42], [372, 10, 408, 8], [372, 16, 408, 14, "index"], [372, 21, 408, 19], [372, 24, 408, 22], [372, 25, 408, 23, "y"], [372, 26, 408, 24], [372, 29, 408, 27, "width"], [372, 34, 408, 32], [372, 37, 408, 35, "x"], [372, 38, 408, 36], [372, 42, 408, 40], [372, 43, 408, 41], [374, 10, 410, 8], [375, 10, 411, 8], [375, 14, 411, 12, "r"], [375, 15, 411, 13], [375, 18, 411, 16], [375, 19, 411, 17], [376, 12, 411, 19, "g"], [376, 13, 411, 20], [376, 16, 411, 23], [376, 17, 411, 24], [377, 12, 411, 26, "b"], [377, 13, 411, 27], [377, 16, 411, 30], [377, 17, 411, 31], [378, 10, 412, 8], [378, 15, 412, 13], [378, 19, 412, 17, "dy"], [378, 21, 412, 19], [378, 24, 412, 22], [378, 25, 412, 23], [378, 26, 412, 24], [378, 28, 412, 26, "dy"], [378, 30, 412, 28], [378, 34, 412, 32], [378, 35, 412, 33], [378, 37, 412, 35, "dy"], [378, 39, 412, 37], [378, 41, 412, 39], [378, 43, 412, 41], [379, 12, 413, 10], [379, 17, 413, 15], [379, 21, 413, 19, "dx"], [379, 23, 413, 21], [379, 26, 413, 24], [379, 27, 413, 25], [379, 28, 413, 26], [379, 30, 413, 28, "dx"], [379, 32, 413, 30], [379, 36, 413, 34], [379, 37, 413, 35], [379, 39, 413, 37, "dx"], [379, 41, 413, 39], [379, 43, 413, 41], [379, 45, 413, 43], [380, 14, 414, 12], [380, 20, 414, 18, "neighborIndex"], [380, 33, 414, 31], [380, 36, 414, 34], [380, 37, 414, 35], [380, 38, 414, 36, "y"], [380, 39, 414, 37], [380, 42, 414, 40, "dy"], [380, 44, 414, 42], [380, 48, 414, 46, "width"], [380, 53, 414, 51], [380, 57, 414, 55, "x"], [380, 58, 414, 56], [380, 61, 414, 59, "dx"], [380, 63, 414, 61], [380, 64, 414, 62], [380, 68, 414, 66], [380, 69, 414, 67], [381, 14, 415, 12, "r"], [381, 15, 415, 13], [381, 19, 415, 17, "original"], [381, 27, 415, 25], [381, 28, 415, 26, "neighborIndex"], [381, 41, 415, 39], [381, 42, 415, 40], [382, 14, 416, 12, "g"], [382, 15, 416, 13], [382, 19, 416, 17, "original"], [382, 27, 416, 25], [382, 28, 416, 26, "neighborIndex"], [382, 41, 416, 39], [382, 44, 416, 42], [382, 45, 416, 43], [382, 46, 416, 44], [383, 14, 417, 12, "b"], [383, 15, 417, 13], [383, 19, 417, 17, "original"], [383, 27, 417, 25], [383, 28, 417, 26, "neighborIndex"], [383, 41, 417, 39], [383, 44, 417, 42], [383, 45, 417, 43], [383, 46, 417, 44], [384, 12, 418, 10], [385, 10, 419, 8], [386, 10, 421, 8, "data"], [386, 14, 421, 12], [386, 15, 421, 13, "index"], [386, 20, 421, 18], [386, 21, 421, 19], [386, 24, 421, 22, "r"], [386, 25, 421, 23], [386, 28, 421, 26], [386, 29, 421, 27], [387, 10, 422, 8, "data"], [387, 14, 422, 12], [387, 15, 422, 13, "index"], [387, 20, 422, 18], [387, 23, 422, 21], [387, 24, 422, 22], [387, 25, 422, 23], [387, 28, 422, 26, "g"], [387, 29, 422, 27], [387, 32, 422, 30], [387, 33, 422, 31], [388, 10, 423, 8, "data"], [388, 14, 423, 12], [388, 15, 423, 13, "index"], [388, 20, 423, 18], [388, 23, 423, 21], [388, 24, 423, 22], [388, 25, 423, 23], [388, 28, 423, 26, "b"], [388, 29, 423, 27], [388, 32, 423, 30], [388, 33, 423, 31], [389, 8, 424, 6], [390, 6, 425, 4], [391, 4, 426, 2], [391, 5, 426, 3], [392, 4, 428, 2], [392, 10, 428, 8, "applyFallbackFaceBlur"], [392, 31, 428, 29], [392, 34, 428, 32, "applyFallbackFaceBlur"], [392, 35, 428, 33, "ctx"], [392, 38, 428, 62], [392, 40, 428, 64, "imgWidth"], [392, 48, 428, 80], [392, 50, 428, 82, "imgHeight"], [392, 59, 428, 99], [392, 64, 428, 104], [393, 6, 429, 4, "console"], [393, 13, 429, 11], [393, 14, 429, 12, "log"], [393, 17, 429, 15], [393, 18, 429, 16], [393, 90, 429, 88], [393, 91, 429, 89], [395, 6, 431, 4], [396, 6, 432, 4], [396, 12, 432, 10, "areas"], [396, 17, 432, 15], [396, 20, 432, 18], [397, 6, 433, 6], [398, 6, 434, 6], [399, 8, 434, 8, "x"], [399, 9, 434, 9], [399, 11, 434, 11, "imgWidth"], [399, 19, 434, 19], [399, 22, 434, 22], [399, 26, 434, 26], [400, 8, 434, 28, "y"], [400, 9, 434, 29], [400, 11, 434, 31, "imgHeight"], [400, 20, 434, 40], [400, 23, 434, 43], [400, 27, 434, 47], [401, 8, 434, 49, "w"], [401, 9, 434, 50], [401, 11, 434, 52, "imgWidth"], [401, 19, 434, 60], [401, 22, 434, 63], [401, 25, 434, 66], [402, 8, 434, 68, "h"], [402, 9, 434, 69], [402, 11, 434, 71, "imgHeight"], [402, 20, 434, 80], [402, 23, 434, 83], [403, 6, 434, 87], [403, 7, 434, 88], [404, 6, 435, 6], [405, 6, 436, 6], [406, 8, 436, 8, "x"], [406, 9, 436, 9], [406, 11, 436, 11, "imgWidth"], [406, 19, 436, 19], [406, 22, 436, 22], [406, 25, 436, 25], [407, 8, 436, 27, "y"], [407, 9, 436, 28], [407, 11, 436, 30, "imgHeight"], [407, 20, 436, 39], [407, 23, 436, 42], [407, 26, 436, 45], [408, 8, 436, 47, "w"], [408, 9, 436, 48], [408, 11, 436, 50, "imgWidth"], [408, 19, 436, 58], [408, 22, 436, 61], [408, 26, 436, 65], [409, 8, 436, 67, "h"], [409, 9, 436, 68], [409, 11, 436, 70, "imgHeight"], [409, 20, 436, 79], [409, 23, 436, 82], [410, 6, 436, 86], [410, 7, 436, 87], [411, 6, 437, 6], [412, 6, 438, 6], [413, 8, 438, 8, "x"], [413, 9, 438, 9], [413, 11, 438, 11, "imgWidth"], [413, 19, 438, 19], [413, 22, 438, 22], [413, 26, 438, 26], [414, 8, 438, 28, "y"], [414, 9, 438, 29], [414, 11, 438, 31, "imgHeight"], [414, 20, 438, 40], [414, 23, 438, 43], [414, 26, 438, 46], [415, 8, 438, 48, "w"], [415, 9, 438, 49], [415, 11, 438, 51, "imgWidth"], [415, 19, 438, 59], [415, 22, 438, 62], [415, 26, 438, 66], [416, 8, 438, 68, "h"], [416, 9, 438, 69], [416, 11, 438, 71, "imgHeight"], [416, 20, 438, 80], [416, 23, 438, 83], [417, 6, 438, 87], [417, 7, 438, 88], [417, 8, 439, 5], [418, 6, 441, 4, "areas"], [418, 11, 441, 9], [418, 12, 441, 10, "for<PERSON>ach"], [418, 19, 441, 17], [418, 20, 441, 18], [418, 21, 441, 19, "area"], [418, 25, 441, 23], [418, 27, 441, 25, "index"], [418, 32, 441, 30], [418, 37, 441, 35], [419, 8, 442, 6, "console"], [419, 15, 442, 13], [419, 16, 442, 14, "log"], [419, 19, 442, 17], [419, 20, 442, 18], [419, 65, 442, 63, "index"], [419, 70, 442, 68], [419, 73, 442, 71], [419, 74, 442, 72], [419, 77, 442, 75], [419, 79, 442, 77, "area"], [419, 83, 442, 81], [419, 84, 442, 82], [420, 8, 443, 6, "applyStrongBlur"], [420, 23, 443, 21], [420, 24, 443, 22, "ctx"], [420, 27, 443, 25], [420, 29, 443, 27, "area"], [420, 33, 443, 31], [420, 34, 443, 32, "x"], [420, 35, 443, 33], [420, 37, 443, 35, "area"], [420, 41, 443, 39], [420, 42, 443, 40, "y"], [420, 43, 443, 41], [420, 45, 443, 43, "area"], [420, 49, 443, 47], [420, 50, 443, 48, "w"], [420, 51, 443, 49], [420, 53, 443, 51, "area"], [420, 57, 443, 55], [420, 58, 443, 56, "h"], [420, 59, 443, 57], [420, 60, 443, 58], [421, 6, 444, 4], [421, 7, 444, 5], [421, 8, 444, 6], [422, 4, 445, 2], [422, 5, 445, 3], [424, 4, 447, 2], [425, 4, 448, 2], [425, 10, 448, 8, "capturePhoto"], [425, 22, 448, 20], [425, 25, 448, 23], [425, 29, 448, 23, "useCallback"], [425, 47, 448, 34], [425, 49, 448, 35], [425, 61, 448, 47], [426, 6, 449, 4], [427, 6, 450, 4], [427, 12, 450, 10, "isDev"], [427, 17, 450, 15], [427, 20, 450, 18, "process"], [427, 27, 450, 25], [427, 28, 450, 26, "env"], [427, 31, 450, 29], [427, 32, 450, 30, "NODE_ENV"], [427, 40, 450, 38], [427, 45, 450, 43], [427, 58, 450, 56], [427, 62, 450, 60, "__DEV__"], [427, 69, 450, 67], [428, 6, 452, 4], [428, 10, 452, 8], [428, 11, 452, 9, "cameraRef"], [428, 20, 452, 18], [428, 21, 452, 19, "current"], [428, 28, 452, 26], [428, 32, 452, 30], [428, 33, 452, 31, "isDev"], [428, 38, 452, 36], [428, 40, 452, 38], [429, 8, 453, 6, "<PERSON><PERSON>"], [429, 22, 453, 11], [429, 23, 453, 12, "alert"], [429, 28, 453, 17], [429, 29, 453, 18], [429, 36, 453, 25], [429, 38, 453, 27], [429, 56, 453, 45], [429, 57, 453, 46], [430, 8, 454, 6], [431, 6, 455, 4], [432, 6, 456, 4], [432, 10, 456, 8], [433, 8, 457, 6, "setProcessingState"], [433, 26, 457, 24], [433, 27, 457, 25], [433, 38, 457, 36], [433, 39, 457, 37], [434, 8, 458, 6, "setProcessingProgress"], [434, 29, 458, 27], [434, 30, 458, 28], [434, 32, 458, 30], [434, 33, 458, 31], [435, 8, 459, 6], [436, 8, 460, 6], [437, 8, 461, 6], [438, 8, 462, 6], [438, 14, 462, 12], [438, 18, 462, 16, "Promise"], [438, 25, 462, 23], [438, 26, 462, 24, "resolve"], [438, 33, 462, 31], [438, 37, 462, 35, "setTimeout"], [438, 47, 462, 45], [438, 48, 462, 46, "resolve"], [438, 55, 462, 53], [438, 57, 462, 55], [438, 59, 462, 57], [438, 60, 462, 58], [438, 61, 462, 59], [439, 8, 463, 6], [440, 8, 464, 6], [440, 12, 464, 10, "photo"], [440, 17, 464, 15], [441, 8, 466, 6], [441, 12, 466, 10], [442, 10, 467, 8, "photo"], [442, 15, 467, 13], [442, 18, 467, 16], [442, 24, 467, 22, "cameraRef"], [442, 33, 467, 31], [442, 34, 467, 32, "current"], [442, 41, 467, 39], [442, 42, 467, 40, "takePictureAsync"], [442, 58, 467, 56], [442, 59, 467, 57], [443, 12, 468, 10, "quality"], [443, 19, 468, 17], [443, 21, 468, 19], [443, 24, 468, 22], [444, 12, 469, 10, "base64"], [444, 18, 469, 16], [444, 20, 469, 18], [444, 25, 469, 23], [445, 12, 470, 10, "skipProcessing"], [445, 26, 470, 24], [445, 28, 470, 26], [445, 32, 470, 30], [445, 33, 470, 32], [446, 10, 471, 8], [446, 11, 471, 9], [446, 12, 471, 10], [447, 8, 472, 6], [447, 9, 472, 7], [447, 10, 472, 8], [447, 17, 472, 15, "cameraError"], [447, 28, 472, 26], [447, 30, 472, 28], [448, 10, 473, 8, "console"], [448, 17, 473, 15], [448, 18, 473, 16, "log"], [448, 21, 473, 19], [448, 22, 473, 20], [448, 82, 473, 80], [448, 84, 473, 82, "cameraError"], [448, 95, 473, 93], [448, 96, 473, 94], [449, 10, 474, 8], [450, 10, 475, 8], [450, 14, 475, 12, "isDev"], [450, 19, 475, 17], [450, 21, 475, 19], [451, 12, 476, 10, "photo"], [451, 17, 476, 15], [451, 20, 476, 18], [452, 14, 477, 12, "uri"], [452, 17, 477, 15], [452, 19, 477, 17], [453, 12, 478, 10], [453, 13, 478, 11], [454, 10, 479, 8], [454, 11, 479, 9], [454, 17, 479, 15], [455, 12, 480, 10], [455, 18, 480, 16, "cameraError"], [455, 29, 480, 27], [456, 10, 481, 8], [457, 8, 482, 6], [458, 8, 483, 6], [458, 12, 483, 10], [458, 13, 483, 11, "photo"], [458, 18, 483, 16], [458, 20, 483, 18], [459, 10, 484, 8], [459, 16, 484, 14], [459, 20, 484, 18, "Error"], [459, 25, 484, 23], [459, 26, 484, 24], [459, 51, 484, 49], [459, 52, 484, 50], [460, 8, 485, 6], [461, 8, 486, 6, "console"], [461, 15, 486, 13], [461, 16, 486, 14, "log"], [461, 19, 486, 17], [461, 20, 486, 18], [461, 56, 486, 54], [461, 58, 486, 56, "photo"], [461, 63, 486, 61], [461, 64, 486, 62, "uri"], [461, 67, 486, 65], [461, 68, 486, 66], [462, 8, 487, 6, "setCapturedPhoto"], [462, 24, 487, 22], [462, 25, 487, 23, "photo"], [462, 30, 487, 28], [462, 31, 487, 29, "uri"], [462, 34, 487, 32], [462, 35, 487, 33], [463, 8, 488, 6, "setProcessingProgress"], [463, 29, 488, 27], [463, 30, 488, 28], [463, 32, 488, 30], [463, 33, 488, 31], [464, 8, 489, 6], [465, 8, 490, 6, "console"], [465, 15, 490, 13], [465, 16, 490, 14, "log"], [465, 19, 490, 17], [465, 20, 490, 18], [465, 73, 490, 71], [465, 74, 490, 72], [466, 8, 491, 6], [466, 14, 491, 12, "processImageWithFaceBlur"], [466, 38, 491, 36], [466, 39, 491, 37, "photo"], [466, 44, 491, 42], [466, 45, 491, 43, "uri"], [466, 48, 491, 46], [466, 49, 491, 47], [467, 8, 492, 6, "console"], [467, 15, 492, 13], [467, 16, 492, 14, "log"], [467, 19, 492, 17], [467, 20, 492, 18], [467, 71, 492, 69], [467, 72, 492, 70], [468, 6, 493, 4], [468, 7, 493, 5], [468, 8, 493, 6], [468, 15, 493, 13, "error"], [468, 20, 493, 18], [468, 22, 493, 20], [469, 8, 494, 6, "console"], [469, 15, 494, 13], [469, 16, 494, 14, "error"], [469, 21, 494, 19], [469, 22, 494, 20], [469, 54, 494, 52], [469, 56, 494, 54, "error"], [469, 61, 494, 59], [469, 62, 494, 60], [470, 8, 495, 6, "setErrorMessage"], [470, 23, 495, 21], [470, 24, 495, 22], [470, 68, 495, 66], [470, 69, 495, 67], [471, 8, 496, 6, "setProcessingState"], [471, 26, 496, 24], [471, 27, 496, 25], [471, 34, 496, 32], [471, 35, 496, 33], [472, 6, 497, 4], [473, 4, 498, 2], [473, 5, 498, 3], [473, 7, 498, 5], [473, 9, 498, 7], [473, 10, 498, 8], [474, 4, 499, 2], [475, 4, 500, 2], [475, 10, 500, 8, "processImageWithFaceBlur"], [475, 34, 500, 32], [475, 37, 500, 35], [475, 43, 500, 42, "photoUri"], [475, 51, 500, 58], [475, 55, 500, 63], [476, 6, 501, 4], [476, 10, 501, 8], [477, 8, 502, 6, "console"], [477, 15, 502, 13], [477, 16, 502, 14, "log"], [477, 19, 502, 17], [477, 20, 502, 18], [477, 84, 502, 82], [477, 85, 502, 83], [478, 8, 503, 6, "setProcessingState"], [478, 26, 503, 24], [478, 27, 503, 25], [478, 39, 503, 37], [478, 40, 503, 38], [479, 8, 504, 6, "setProcessingProgress"], [479, 29, 504, 27], [479, 30, 504, 28], [479, 32, 504, 30], [479, 33, 504, 31], [481, 8, 506, 6], [482, 8, 507, 6], [482, 14, 507, 12, "canvas"], [482, 20, 507, 18], [482, 23, 507, 21, "document"], [482, 31, 507, 29], [482, 32, 507, 30, "createElement"], [482, 45, 507, 43], [482, 46, 507, 44], [482, 54, 507, 52], [482, 55, 507, 53], [483, 8, 508, 6], [483, 14, 508, 12, "ctx"], [483, 17, 508, 15], [483, 20, 508, 18, "canvas"], [483, 26, 508, 24], [483, 27, 508, 25, "getContext"], [483, 37, 508, 35], [483, 38, 508, 36], [483, 42, 508, 40], [483, 43, 508, 41], [484, 8, 509, 6], [484, 12, 509, 10], [484, 13, 509, 11, "ctx"], [484, 16, 509, 14], [484, 18, 509, 16], [484, 24, 509, 22], [484, 28, 509, 26, "Error"], [484, 33, 509, 31], [484, 34, 509, 32], [484, 64, 509, 62], [484, 65, 509, 63], [486, 8, 511, 6], [487, 8, 512, 6], [487, 14, 512, 12, "img"], [487, 17, 512, 15], [487, 20, 512, 18], [487, 24, 512, 22, "Image"], [487, 29, 512, 27], [487, 30, 512, 28], [487, 31, 512, 29], [488, 8, 513, 6], [488, 14, 513, 12], [488, 18, 513, 16, "Promise"], [488, 25, 513, 23], [488, 26, 513, 24], [488, 27, 513, 25, "resolve"], [488, 34, 513, 32], [488, 36, 513, 34, "reject"], [488, 42, 513, 40], [488, 47, 513, 45], [489, 10, 514, 8, "img"], [489, 13, 514, 11], [489, 14, 514, 12, "onload"], [489, 20, 514, 18], [489, 23, 514, 21, "resolve"], [489, 30, 514, 28], [490, 10, 515, 8, "img"], [490, 13, 515, 11], [490, 14, 515, 12, "onerror"], [490, 21, 515, 19], [490, 24, 515, 22, "reject"], [490, 30, 515, 28], [491, 10, 516, 8, "img"], [491, 13, 516, 11], [491, 14, 516, 12, "src"], [491, 17, 516, 15], [491, 20, 516, 18, "photoUri"], [491, 28, 516, 26], [492, 8, 517, 6], [492, 9, 517, 7], [492, 10, 517, 8], [494, 8, 519, 6], [495, 8, 520, 6, "canvas"], [495, 14, 520, 12], [495, 15, 520, 13, "width"], [495, 20, 520, 18], [495, 23, 520, 21, "img"], [495, 26, 520, 24], [495, 27, 520, 25, "width"], [495, 32, 520, 30], [496, 8, 521, 6, "canvas"], [496, 14, 521, 12], [496, 15, 521, 13, "height"], [496, 21, 521, 19], [496, 24, 521, 22, "img"], [496, 27, 521, 25], [496, 28, 521, 26, "height"], [496, 34, 521, 32], [497, 8, 522, 6, "console"], [497, 15, 522, 13], [497, 16, 522, 14, "log"], [497, 19, 522, 17], [497, 20, 522, 18], [497, 54, 522, 52], [497, 56, 522, 54], [498, 10, 522, 56, "width"], [498, 15, 522, 61], [498, 17, 522, 63, "img"], [498, 20, 522, 66], [498, 21, 522, 67, "width"], [498, 26, 522, 72], [499, 10, 522, 74, "height"], [499, 16, 522, 80], [499, 18, 522, 82, "img"], [499, 21, 522, 85], [499, 22, 522, 86, "height"], [500, 8, 522, 93], [500, 9, 522, 94], [500, 10, 522, 95], [502, 8, 524, 6], [503, 8, 525, 6, "ctx"], [503, 11, 525, 9], [503, 12, 525, 10, "drawImage"], [503, 21, 525, 19], [503, 22, 525, 20, "img"], [503, 25, 525, 23], [503, 27, 525, 25], [503, 28, 525, 26], [503, 30, 525, 28], [503, 31, 525, 29], [503, 32, 525, 30], [504, 8, 526, 6, "console"], [504, 15, 526, 13], [504, 16, 526, 14, "log"], [504, 19, 526, 17], [504, 20, 526, 18], [504, 72, 526, 70], [504, 73, 526, 71], [505, 8, 528, 6, "setProcessingProgress"], [505, 29, 528, 27], [505, 30, 528, 28], [505, 32, 528, 30], [505, 33, 528, 31], [507, 8, 530, 6], [508, 8, 531, 6], [508, 12, 531, 10, "detectedFaces"], [508, 25, 531, 23], [508, 28, 531, 26], [508, 30, 531, 28], [509, 8, 533, 6, "console"], [509, 15, 533, 13], [509, 16, 533, 14, "log"], [509, 19, 533, 17], [509, 20, 533, 18], [509, 81, 533, 79], [509, 82, 533, 80], [511, 8, 535, 6], [512, 8, 536, 6], [512, 12, 536, 10], [513, 10, 537, 8], [513, 16, 537, 14, "loadTensorFlowFaceDetection"], [513, 43, 537, 41], [513, 44, 537, 42], [513, 45, 537, 43], [514, 10, 538, 8, "detectedFaces"], [514, 23, 538, 21], [514, 26, 538, 24], [514, 32, 538, 30, "detectFacesWithTensorFlow"], [514, 57, 538, 55], [514, 58, 538, 56, "img"], [514, 61, 538, 59], [514, 62, 538, 60], [515, 10, 539, 8, "console"], [515, 17, 539, 15], [515, 18, 539, 16, "log"], [515, 21, 539, 19], [515, 22, 539, 20], [515, 70, 539, 68, "detectedFaces"], [515, 83, 539, 81], [515, 84, 539, 82, "length"], [515, 90, 539, 88], [515, 98, 539, 96], [515, 99, 539, 97], [516, 8, 540, 6], [516, 9, 540, 7], [516, 10, 540, 8], [516, 17, 540, 15, "tensorFlowError"], [516, 32, 540, 30], [516, 34, 540, 32], [517, 10, 541, 8, "console"], [517, 17, 541, 15], [517, 18, 541, 16, "warn"], [517, 22, 541, 20], [517, 23, 541, 21], [517, 61, 541, 59], [517, 63, 541, 61, "tensorFlowError"], [517, 78, 541, 76], [517, 79, 541, 77], [519, 10, 543, 8], [520, 10, 544, 8, "console"], [520, 17, 544, 15], [520, 18, 544, 16, "log"], [520, 21, 544, 19], [520, 22, 544, 20], [520, 86, 544, 84], [520, 87, 544, 85], [521, 10, 545, 8, "detectedFaces"], [521, 23, 545, 21], [521, 26, 545, 24, "detectFacesHeuristic"], [521, 46, 545, 44], [521, 47, 545, 45, "img"], [521, 50, 545, 48], [521, 52, 545, 50, "ctx"], [521, 55, 545, 53], [521, 56, 545, 54], [522, 10, 546, 8, "console"], [522, 17, 546, 15], [522, 18, 546, 16, "log"], [522, 21, 546, 19], [522, 22, 546, 20], [522, 70, 546, 68, "detectedFaces"], [522, 83, 546, 81], [522, 84, 546, 82, "length"], [522, 90, 546, 88], [522, 98, 546, 96], [522, 99, 546, 97], [523, 8, 547, 6], [524, 8, 549, 6, "console"], [524, 15, 549, 13], [524, 16, 549, 14, "log"], [524, 19, 549, 17], [524, 20, 549, 18], [524, 72, 549, 70, "detectedFaces"], [524, 85, 549, 83], [524, 86, 549, 84, "length"], [524, 92, 549, 90], [524, 100, 549, 98], [524, 101, 549, 99], [525, 8, 550, 6], [525, 12, 550, 10, "detectedFaces"], [525, 25, 550, 23], [525, 26, 550, 24, "length"], [525, 32, 550, 30], [525, 35, 550, 33], [525, 36, 550, 34], [525, 38, 550, 36], [526, 10, 551, 8, "console"], [526, 17, 551, 15], [526, 18, 551, 16, "log"], [526, 21, 551, 19], [526, 22, 551, 20], [526, 66, 551, 64], [526, 68, 551, 66, "detectedFaces"], [526, 81, 551, 79], [526, 82, 551, 80, "map"], [526, 85, 551, 83], [526, 86, 551, 84], [526, 87, 551, 85, "face"], [526, 91, 551, 89], [526, 93, 551, 91, "i"], [526, 94, 551, 92], [526, 100, 551, 98], [527, 12, 552, 10, "faceNumber"], [527, 22, 552, 20], [527, 24, 552, 22, "i"], [527, 25, 552, 23], [527, 28, 552, 26], [527, 29, 552, 27], [528, 12, 553, 10, "centerX"], [528, 19, 553, 17], [528, 21, 553, 19, "face"], [528, 25, 553, 23], [528, 26, 553, 24, "boundingBox"], [528, 37, 553, 35], [528, 38, 553, 36, "xCenter"], [528, 45, 553, 43], [529, 12, 554, 10, "centerY"], [529, 19, 554, 17], [529, 21, 554, 19, "face"], [529, 25, 554, 23], [529, 26, 554, 24, "boundingBox"], [529, 37, 554, 35], [529, 38, 554, 36, "yCenter"], [529, 45, 554, 43], [530, 12, 555, 10, "width"], [530, 17, 555, 15], [530, 19, 555, 17, "face"], [530, 23, 555, 21], [530, 24, 555, 22, "boundingBox"], [530, 35, 555, 33], [530, 36, 555, 34, "width"], [530, 41, 555, 39], [531, 12, 556, 10, "height"], [531, 18, 556, 16], [531, 20, 556, 18, "face"], [531, 24, 556, 22], [531, 25, 556, 23, "boundingBox"], [531, 36, 556, 34], [531, 37, 556, 35, "height"], [532, 10, 557, 8], [532, 11, 557, 9], [532, 12, 557, 10], [532, 13, 557, 11], [532, 14, 557, 12], [533, 8, 558, 6], [533, 9, 558, 7], [533, 15, 558, 13], [534, 10, 559, 8, "console"], [534, 17, 559, 15], [534, 18, 559, 16, "log"], [534, 21, 559, 19], [534, 22, 559, 20], [534, 91, 559, 89], [534, 92, 559, 90], [535, 8, 560, 6], [536, 8, 562, 6, "setProcessingProgress"], [536, 29, 562, 27], [536, 30, 562, 28], [536, 32, 562, 30], [536, 33, 562, 31], [538, 8, 564, 6], [539, 8, 565, 6], [539, 12, 565, 10, "detectedFaces"], [539, 25, 565, 23], [539, 26, 565, 24, "length"], [539, 32, 565, 30], [539, 35, 565, 33], [539, 36, 565, 34], [539, 38, 565, 36], [540, 10, 566, 8, "console"], [540, 17, 566, 15], [540, 18, 566, 16, "log"], [540, 21, 566, 19], [540, 22, 566, 20], [540, 61, 566, 59, "detectedFaces"], [540, 74, 566, 72], [540, 75, 566, 73, "length"], [540, 81, 566, 79], [540, 101, 566, 99], [540, 102, 566, 100], [541, 10, 568, 8, "detectedFaces"], [541, 23, 568, 21], [541, 24, 568, 22, "for<PERSON>ach"], [541, 31, 568, 29], [541, 32, 568, 30], [541, 33, 568, 31, "detection"], [541, 42, 568, 40], [541, 44, 568, 42, "index"], [541, 49, 568, 47], [541, 54, 568, 52], [542, 12, 569, 10], [542, 18, 569, 16, "bbox"], [542, 22, 569, 20], [542, 25, 569, 23, "detection"], [542, 34, 569, 32], [542, 35, 569, 33, "boundingBox"], [542, 46, 569, 44], [544, 12, 571, 10], [545, 12, 572, 10], [545, 18, 572, 16, "faceX"], [545, 23, 572, 21], [545, 26, 572, 24, "bbox"], [545, 30, 572, 28], [545, 31, 572, 29, "xCenter"], [545, 38, 572, 36], [545, 41, 572, 39, "img"], [545, 44, 572, 42], [545, 45, 572, 43, "width"], [545, 50, 572, 48], [545, 53, 572, 52, "bbox"], [545, 57, 572, 56], [545, 58, 572, 57, "width"], [545, 63, 572, 62], [545, 66, 572, 65, "img"], [545, 69, 572, 68], [545, 70, 572, 69, "width"], [545, 75, 572, 74], [545, 78, 572, 78], [545, 79, 572, 79], [546, 12, 573, 10], [546, 18, 573, 16, "faceY"], [546, 23, 573, 21], [546, 26, 573, 24, "bbox"], [546, 30, 573, 28], [546, 31, 573, 29, "yCenter"], [546, 38, 573, 36], [546, 41, 573, 39, "img"], [546, 44, 573, 42], [546, 45, 573, 43, "height"], [546, 51, 573, 49], [546, 54, 573, 53, "bbox"], [546, 58, 573, 57], [546, 59, 573, 58, "height"], [546, 65, 573, 64], [546, 68, 573, 67, "img"], [546, 71, 573, 70], [546, 72, 573, 71, "height"], [546, 78, 573, 77], [546, 81, 573, 81], [546, 82, 573, 82], [547, 12, 574, 10], [547, 18, 574, 16, "faceWidth"], [547, 27, 574, 25], [547, 30, 574, 28, "bbox"], [547, 34, 574, 32], [547, 35, 574, 33, "width"], [547, 40, 574, 38], [547, 43, 574, 41, "img"], [547, 46, 574, 44], [547, 47, 574, 45, "width"], [547, 52, 574, 50], [548, 12, 575, 10], [548, 18, 575, 16, "faceHeight"], [548, 28, 575, 26], [548, 31, 575, 29, "bbox"], [548, 35, 575, 33], [548, 36, 575, 34, "height"], [548, 42, 575, 40], [548, 45, 575, 43, "img"], [548, 48, 575, 46], [548, 49, 575, 47, "height"], [548, 55, 575, 53], [550, 12, 577, 10], [551, 12, 578, 10], [551, 18, 578, 16, "padding"], [551, 25, 578, 23], [551, 28, 578, 26], [551, 31, 578, 29], [552, 12, 579, 10], [552, 18, 579, 16, "paddedX"], [552, 25, 579, 23], [552, 28, 579, 26, "Math"], [552, 32, 579, 30], [552, 33, 579, 31, "max"], [552, 36, 579, 34], [552, 37, 579, 35], [552, 38, 579, 36], [552, 40, 579, 38, "faceX"], [552, 45, 579, 43], [552, 48, 579, 46, "faceWidth"], [552, 57, 579, 55], [552, 60, 579, 58, "padding"], [552, 67, 579, 65], [552, 68, 579, 66], [553, 12, 580, 10], [553, 18, 580, 16, "paddedY"], [553, 25, 580, 23], [553, 28, 580, 26, "Math"], [553, 32, 580, 30], [553, 33, 580, 31, "max"], [553, 36, 580, 34], [553, 37, 580, 35], [553, 38, 580, 36], [553, 40, 580, 38, "faceY"], [553, 45, 580, 43], [553, 48, 580, 46, "faceHeight"], [553, 58, 580, 56], [553, 61, 580, 59, "padding"], [553, 68, 580, 66], [553, 69, 580, 67], [554, 12, 581, 10], [554, 18, 581, 16, "<PERSON><PERSON><PERSON><PERSON>"], [554, 29, 581, 27], [554, 32, 581, 30, "Math"], [554, 36, 581, 34], [554, 37, 581, 35, "min"], [554, 40, 581, 38], [554, 41, 581, 39, "img"], [554, 44, 581, 42], [554, 45, 581, 43, "width"], [554, 50, 581, 48], [554, 53, 581, 51, "paddedX"], [554, 60, 581, 58], [554, 62, 581, 60, "faceWidth"], [554, 71, 581, 69], [554, 75, 581, 73], [554, 76, 581, 74], [554, 79, 581, 77], [554, 80, 581, 78], [554, 83, 581, 81, "padding"], [554, 90, 581, 88], [554, 91, 581, 89], [554, 92, 581, 90], [555, 12, 582, 10], [555, 18, 582, 16, "paddedHeight"], [555, 30, 582, 28], [555, 33, 582, 31, "Math"], [555, 37, 582, 35], [555, 38, 582, 36, "min"], [555, 41, 582, 39], [555, 42, 582, 40, "img"], [555, 45, 582, 43], [555, 46, 582, 44, "height"], [555, 52, 582, 50], [555, 55, 582, 53, "paddedY"], [555, 62, 582, 60], [555, 64, 582, 62, "faceHeight"], [555, 74, 582, 72], [555, 78, 582, 76], [555, 79, 582, 77], [555, 82, 582, 80], [555, 83, 582, 81], [555, 86, 582, 84, "padding"], [555, 93, 582, 91], [555, 94, 582, 92], [555, 95, 582, 93], [556, 12, 584, 10, "console"], [556, 19, 584, 17], [556, 20, 584, 18, "log"], [556, 23, 584, 21], [556, 24, 584, 22], [556, 60, 584, 58, "index"], [556, 65, 584, 63], [556, 68, 584, 66], [556, 69, 584, 67], [556, 72, 584, 70], [556, 74, 584, 72], [557, 14, 585, 12, "original"], [557, 22, 585, 20], [557, 24, 585, 22], [558, 16, 585, 24, "x"], [558, 17, 585, 25], [558, 19, 585, 27, "Math"], [558, 23, 585, 31], [558, 24, 585, 32, "round"], [558, 29, 585, 37], [558, 30, 585, 38, "faceX"], [558, 35, 585, 43], [558, 36, 585, 44], [559, 16, 585, 46, "y"], [559, 17, 585, 47], [559, 19, 585, 49, "Math"], [559, 23, 585, 53], [559, 24, 585, 54, "round"], [559, 29, 585, 59], [559, 30, 585, 60, "faceY"], [559, 35, 585, 65], [559, 36, 585, 66], [560, 16, 585, 68, "w"], [560, 17, 585, 69], [560, 19, 585, 71, "Math"], [560, 23, 585, 75], [560, 24, 585, 76, "round"], [560, 29, 585, 81], [560, 30, 585, 82, "faceWidth"], [560, 39, 585, 91], [560, 40, 585, 92], [561, 16, 585, 94, "h"], [561, 17, 585, 95], [561, 19, 585, 97, "Math"], [561, 23, 585, 101], [561, 24, 585, 102, "round"], [561, 29, 585, 107], [561, 30, 585, 108, "faceHeight"], [561, 40, 585, 118], [562, 14, 585, 120], [562, 15, 585, 121], [563, 14, 586, 12, "padded"], [563, 20, 586, 18], [563, 22, 586, 20], [564, 16, 586, 22, "x"], [564, 17, 586, 23], [564, 19, 586, 25, "Math"], [564, 23, 586, 29], [564, 24, 586, 30, "round"], [564, 29, 586, 35], [564, 30, 586, 36, "paddedX"], [564, 37, 586, 43], [564, 38, 586, 44], [565, 16, 586, 46, "y"], [565, 17, 586, 47], [565, 19, 586, 49, "Math"], [565, 23, 586, 53], [565, 24, 586, 54, "round"], [565, 29, 586, 59], [565, 30, 586, 60, "paddedY"], [565, 37, 586, 67], [565, 38, 586, 68], [566, 16, 586, 70, "w"], [566, 17, 586, 71], [566, 19, 586, 73, "Math"], [566, 23, 586, 77], [566, 24, 586, 78, "round"], [566, 29, 586, 83], [566, 30, 586, 84, "<PERSON><PERSON><PERSON><PERSON>"], [566, 41, 586, 95], [566, 42, 586, 96], [567, 16, 586, 98, "h"], [567, 17, 586, 99], [567, 19, 586, 101, "Math"], [567, 23, 586, 105], [567, 24, 586, 106, "round"], [567, 29, 586, 111], [567, 30, 586, 112, "paddedHeight"], [567, 42, 586, 124], [568, 14, 586, 126], [569, 12, 587, 10], [569, 13, 587, 11], [569, 14, 587, 12], [571, 12, 589, 10], [572, 12, 590, 10, "console"], [572, 19, 590, 17], [572, 20, 590, 18, "log"], [572, 23, 590, 21], [572, 24, 590, 22], [572, 70, 590, 68], [572, 72, 590, 70], [573, 14, 591, 12, "width"], [573, 19, 591, 17], [573, 21, 591, 19, "canvas"], [573, 27, 591, 25], [573, 28, 591, 26, "width"], [573, 33, 591, 31], [574, 14, 592, 12, "height"], [574, 20, 592, 18], [574, 22, 592, 20, "canvas"], [574, 28, 592, 26], [574, 29, 592, 27, "height"], [574, 35, 592, 33], [575, 14, 593, 12, "contextValid"], [575, 26, 593, 24], [575, 28, 593, 26], [575, 29, 593, 27], [575, 30, 593, 28, "ctx"], [576, 12, 594, 10], [576, 13, 594, 11], [576, 14, 594, 12], [578, 12, 596, 10], [579, 12, 597, 10, "applyStrongBlur"], [579, 27, 597, 25], [579, 28, 597, 26, "ctx"], [579, 31, 597, 29], [579, 33, 597, 31, "paddedX"], [579, 40, 597, 38], [579, 42, 597, 40, "paddedY"], [579, 49, 597, 47], [579, 51, 597, 49, "<PERSON><PERSON><PERSON><PERSON>"], [579, 62, 597, 60], [579, 64, 597, 62, "paddedHeight"], [579, 76, 597, 74], [579, 77, 597, 75], [581, 12, 599, 10], [582, 12, 600, 10, "console"], [582, 19, 600, 17], [582, 20, 600, 18, "log"], [582, 23, 600, 21], [582, 24, 600, 22], [582, 102, 600, 100], [582, 103, 600, 101], [584, 12, 602, 10], [585, 12, 603, 10], [585, 18, 603, 16, "testImageData"], [585, 31, 603, 29], [585, 34, 603, 32, "ctx"], [585, 37, 603, 35], [585, 38, 603, 36, "getImageData"], [585, 50, 603, 48], [585, 51, 603, 49, "paddedX"], [585, 58, 603, 56], [585, 61, 603, 59], [585, 63, 603, 61], [585, 65, 603, 63, "paddedY"], [585, 72, 603, 70], [585, 75, 603, 73], [585, 77, 603, 75], [585, 79, 603, 77], [585, 81, 603, 79], [585, 83, 603, 81], [585, 85, 603, 83], [585, 86, 603, 84], [586, 12, 604, 10, "console"], [586, 19, 604, 17], [586, 20, 604, 18, "log"], [586, 23, 604, 21], [586, 24, 604, 22], [586, 70, 604, 68], [586, 72, 604, 70], [587, 14, 605, 12, "firstPixel"], [587, 24, 605, 22], [587, 26, 605, 24], [587, 27, 605, 25, "testImageData"], [587, 40, 605, 38], [587, 41, 605, 39, "data"], [587, 45, 605, 43], [587, 46, 605, 44], [587, 47, 605, 45], [587, 48, 605, 46], [587, 50, 605, 48, "testImageData"], [587, 63, 605, 61], [587, 64, 605, 62, "data"], [587, 68, 605, 66], [587, 69, 605, 67], [587, 70, 605, 68], [587, 71, 605, 69], [587, 73, 605, 71, "testImageData"], [587, 86, 605, 84], [587, 87, 605, 85, "data"], [587, 91, 605, 89], [587, 92, 605, 90], [587, 93, 605, 91], [587, 94, 605, 92], [587, 95, 605, 93], [588, 14, 606, 12, "secondPixel"], [588, 25, 606, 23], [588, 27, 606, 25], [588, 28, 606, 26, "testImageData"], [588, 41, 606, 39], [588, 42, 606, 40, "data"], [588, 46, 606, 44], [588, 47, 606, 45], [588, 48, 606, 46], [588, 49, 606, 47], [588, 51, 606, 49, "testImageData"], [588, 64, 606, 62], [588, 65, 606, 63, "data"], [588, 69, 606, 67], [588, 70, 606, 68], [588, 71, 606, 69], [588, 72, 606, 70], [588, 74, 606, 72, "testImageData"], [588, 87, 606, 85], [588, 88, 606, 86, "data"], [588, 92, 606, 90], [588, 93, 606, 91], [588, 94, 606, 92], [588, 95, 606, 93], [589, 12, 607, 10], [589, 13, 607, 11], [589, 14, 607, 12], [590, 12, 609, 10, "console"], [590, 19, 609, 17], [590, 20, 609, 18, "log"], [590, 23, 609, 21], [590, 24, 609, 22], [590, 50, 609, 48, "index"], [590, 55, 609, 53], [590, 58, 609, 56], [590, 59, 609, 57], [590, 79, 609, 77], [590, 80, 609, 78], [591, 10, 610, 8], [591, 11, 610, 9], [591, 12, 610, 10], [592, 10, 612, 8, "console"], [592, 17, 612, 15], [592, 18, 612, 16, "log"], [592, 21, 612, 19], [592, 22, 612, 20], [592, 48, 612, 46, "detectedFaces"], [592, 61, 612, 59], [592, 62, 612, 60, "length"], [592, 68, 612, 66], [592, 104, 612, 102], [592, 105, 612, 103], [593, 8, 613, 6], [593, 9, 613, 7], [593, 15, 613, 13], [594, 10, 614, 8, "console"], [594, 17, 614, 15], [594, 18, 614, 16, "log"], [594, 21, 614, 19], [594, 22, 614, 20], [594, 109, 614, 107], [594, 110, 614, 108], [595, 10, 615, 8], [596, 10, 616, 8, "applyFallbackFaceBlur"], [596, 31, 616, 29], [596, 32, 616, 30, "ctx"], [596, 35, 616, 33], [596, 37, 616, 35, "img"], [596, 40, 616, 38], [596, 41, 616, 39, "width"], [596, 46, 616, 44], [596, 48, 616, 46, "img"], [596, 51, 616, 49], [596, 52, 616, 50, "height"], [596, 58, 616, 56], [596, 59, 616, 57], [597, 8, 617, 6], [598, 8, 619, 6, "setProcessingProgress"], [598, 29, 619, 27], [598, 30, 619, 28], [598, 32, 619, 30], [598, 33, 619, 31], [600, 8, 621, 6], [601, 8, 622, 6, "console"], [601, 15, 622, 13], [601, 16, 622, 14, "log"], [601, 19, 622, 17], [601, 20, 622, 18], [601, 85, 622, 83], [601, 86, 622, 84], [602, 8, 623, 6], [602, 14, 623, 12, "blurredImageBlob"], [602, 30, 623, 28], [602, 33, 623, 31], [602, 39, 623, 37], [602, 43, 623, 41, "Promise"], [602, 50, 623, 48], [602, 51, 623, 56, "resolve"], [602, 58, 623, 63], [602, 62, 623, 68], [603, 10, 624, 8, "canvas"], [603, 16, 624, 14], [603, 17, 624, 15, "toBlob"], [603, 23, 624, 21], [603, 24, 624, 23, "blob"], [603, 28, 624, 27], [603, 32, 624, 32, "resolve"], [603, 39, 624, 39], [603, 40, 624, 40, "blob"], [603, 44, 624, 45], [603, 45, 624, 46], [603, 47, 624, 48], [603, 59, 624, 60], [603, 61, 624, 62], [603, 64, 624, 65], [603, 65, 624, 66], [604, 8, 625, 6], [604, 9, 625, 7], [604, 10, 625, 8], [605, 8, 627, 6], [605, 14, 627, 12, "blurredImageUrl"], [605, 29, 627, 27], [605, 32, 627, 30, "URL"], [605, 35, 627, 33], [605, 36, 627, 34, "createObjectURL"], [605, 51, 627, 49], [605, 52, 627, 50, "blurredImageBlob"], [605, 68, 627, 66], [605, 69, 627, 67], [606, 8, 628, 6, "console"], [606, 15, 628, 13], [606, 16, 628, 14, "log"], [606, 19, 628, 17], [606, 20, 628, 18], [606, 66, 628, 64], [606, 68, 628, 66, "blurredImageUrl"], [606, 83, 628, 81], [606, 84, 628, 82, "substring"], [606, 93, 628, 91], [606, 94, 628, 92], [606, 95, 628, 93], [606, 97, 628, 95], [606, 99, 628, 97], [606, 100, 628, 98], [606, 103, 628, 101], [606, 108, 628, 106], [606, 109, 628, 107], [607, 8, 630, 6, "setProcessingProgress"], [607, 29, 630, 27], [607, 30, 630, 28], [607, 33, 630, 31], [607, 34, 630, 32], [609, 8, 632, 6], [610, 8, 633, 6], [610, 14, 633, 12, "completeProcessing"], [610, 32, 633, 30], [610, 33, 633, 31, "blurredImageUrl"], [610, 48, 633, 46], [610, 49, 633, 47], [611, 6, 635, 4], [611, 7, 635, 5], [611, 8, 635, 6], [611, 15, 635, 13, "error"], [611, 20, 635, 18], [611, 22, 635, 20], [612, 8, 636, 6, "console"], [612, 15, 636, 13], [612, 16, 636, 14, "error"], [612, 21, 636, 19], [612, 22, 636, 20], [612, 57, 636, 55], [612, 59, 636, 57, "error"], [612, 64, 636, 62], [612, 65, 636, 63], [613, 8, 637, 6, "setErrorMessage"], [613, 23, 637, 21], [613, 24, 637, 22], [613, 50, 637, 48], [613, 51, 637, 49], [614, 8, 638, 6, "setProcessingState"], [614, 26, 638, 24], [614, 27, 638, 25], [614, 34, 638, 32], [614, 35, 638, 33], [615, 6, 639, 4], [616, 4, 640, 2], [616, 5, 640, 3], [618, 4, 642, 2], [619, 4, 643, 2], [619, 10, 643, 8, "completeProcessing"], [619, 28, 643, 26], [619, 31, 643, 29], [619, 37, 643, 36, "blurredImageUrl"], [619, 52, 643, 59], [619, 56, 643, 64], [620, 6, 644, 4], [620, 10, 644, 8], [621, 8, 645, 6, "setProcessingState"], [621, 26, 645, 24], [621, 27, 645, 25], [621, 37, 645, 35], [621, 38, 645, 36], [623, 8, 647, 6], [624, 8, 648, 6], [624, 14, 648, 12, "timestamp"], [624, 23, 648, 21], [624, 26, 648, 24, "Date"], [624, 30, 648, 28], [624, 31, 648, 29, "now"], [624, 34, 648, 32], [624, 35, 648, 33], [624, 36, 648, 34], [625, 8, 649, 6], [625, 14, 649, 12, "result"], [625, 20, 649, 18], [625, 23, 649, 21], [626, 10, 650, 8, "imageUrl"], [626, 18, 650, 16], [626, 20, 650, 18, "blurredImageUrl"], [626, 35, 650, 33], [627, 10, 651, 8, "localUri"], [627, 18, 651, 16], [627, 20, 651, 18, "blurredImageUrl"], [627, 35, 651, 33], [628, 10, 652, 8, "challengeCode"], [628, 23, 652, 21], [628, 25, 652, 23, "challengeCode"], [628, 38, 652, 36], [628, 42, 652, 40], [628, 44, 652, 42], [629, 10, 653, 8, "timestamp"], [629, 19, 653, 17], [630, 10, 654, 8, "jobId"], [630, 15, 654, 13], [630, 17, 654, 15], [630, 27, 654, 25, "timestamp"], [630, 36, 654, 34], [630, 38, 654, 36], [631, 10, 655, 8, "status"], [631, 16, 655, 14], [631, 18, 655, 16], [632, 8, 656, 6], [632, 9, 656, 7], [633, 8, 658, 6, "console"], [633, 15, 658, 13], [633, 16, 658, 14, "log"], [633, 19, 658, 17], [633, 20, 658, 18], [633, 100, 658, 98], [633, 102, 658, 100], [634, 10, 659, 8, "imageUrl"], [634, 18, 659, 16], [634, 20, 659, 18, "blurredImageUrl"], [634, 35, 659, 33], [634, 36, 659, 34, "substring"], [634, 45, 659, 43], [634, 46, 659, 44], [634, 47, 659, 45], [634, 49, 659, 47], [634, 51, 659, 49], [634, 52, 659, 50], [634, 55, 659, 53], [634, 60, 659, 58], [635, 10, 660, 8, "timestamp"], [635, 19, 660, 17], [636, 10, 661, 8, "jobId"], [636, 15, 661, 13], [636, 17, 661, 15, "result"], [636, 23, 661, 21], [636, 24, 661, 22, "jobId"], [637, 8, 662, 6], [637, 9, 662, 7], [637, 10, 662, 8], [639, 8, 664, 6], [640, 8, 665, 6, "onComplete"], [640, 18, 665, 16], [640, 19, 665, 17, "result"], [640, 25, 665, 23], [640, 26, 665, 24], [641, 6, 667, 4], [641, 7, 667, 5], [641, 8, 667, 6], [641, 15, 667, 13, "error"], [641, 20, 667, 18], [641, 22, 667, 20], [642, 8, 668, 6, "console"], [642, 15, 668, 13], [642, 16, 668, 14, "error"], [642, 21, 668, 19], [642, 22, 668, 20], [642, 57, 668, 55], [642, 59, 668, 57, "error"], [642, 64, 668, 62], [642, 65, 668, 63], [643, 8, 669, 6, "setErrorMessage"], [643, 23, 669, 21], [643, 24, 669, 22], [643, 56, 669, 54], [643, 57, 669, 55], [644, 8, 670, 6, "setProcessingState"], [644, 26, 670, 24], [644, 27, 670, 25], [644, 34, 670, 32], [644, 35, 670, 33], [645, 6, 671, 4], [646, 4, 672, 2], [646, 5, 672, 3], [648, 4, 674, 2], [649, 4, 675, 2], [649, 10, 675, 8, "triggerServerProcessing"], [649, 33, 675, 31], [649, 36, 675, 34], [649, 42, 675, 34, "triggerServerProcessing"], [649, 43, 675, 41, "privateImageUrl"], [649, 58, 675, 64], [649, 60, 675, 66, "timestamp"], [649, 69, 675, 83], [649, 74, 675, 88], [650, 6, 676, 4], [650, 10, 676, 8], [651, 8, 677, 6, "console"], [651, 15, 677, 13], [651, 16, 677, 14, "log"], [651, 19, 677, 17], [651, 20, 677, 18], [651, 74, 677, 72], [651, 76, 677, 74, "privateImageUrl"], [651, 91, 677, 89], [651, 92, 677, 90], [652, 8, 678, 6, "setProcessingState"], [652, 26, 678, 24], [652, 27, 678, 25], [652, 39, 678, 37], [652, 40, 678, 38], [653, 8, 679, 6, "setProcessingProgress"], [653, 29, 679, 27], [653, 30, 679, 28], [653, 32, 679, 30], [653, 33, 679, 31], [654, 8, 681, 6], [654, 14, 681, 12, "requestBody"], [654, 25, 681, 23], [654, 28, 681, 26], [655, 10, 682, 8, "imageUrl"], [655, 18, 682, 16], [655, 20, 682, 18, "privateImageUrl"], [655, 35, 682, 33], [656, 10, 683, 8, "userId"], [656, 16, 683, 14], [657, 10, 684, 8, "requestId"], [657, 19, 684, 17], [658, 10, 685, 8, "timestamp"], [658, 19, 685, 17], [659, 10, 686, 8, "platform"], [659, 18, 686, 16], [659, 20, 686, 18], [660, 8, 687, 6], [660, 9, 687, 7], [661, 8, 689, 6, "console"], [661, 15, 689, 13], [661, 16, 689, 14, "log"], [661, 19, 689, 17], [661, 20, 689, 18], [661, 65, 689, 63], [661, 67, 689, 65, "requestBody"], [661, 78, 689, 76], [661, 79, 689, 77], [663, 8, 691, 6], [664, 8, 692, 6], [664, 14, 692, 12, "response"], [664, 22, 692, 20], [664, 25, 692, 23], [664, 31, 692, 29, "fetch"], [664, 36, 692, 34], [664, 37, 692, 35], [664, 40, 692, 38, "API_BASE_URL"], [664, 52, 692, 50], [664, 72, 692, 70], [664, 74, 692, 72], [665, 10, 693, 8, "method"], [665, 16, 693, 14], [665, 18, 693, 16], [665, 24, 693, 22], [666, 10, 694, 8, "headers"], [666, 17, 694, 15], [666, 19, 694, 17], [667, 12, 695, 10], [667, 26, 695, 24], [667, 28, 695, 26], [667, 46, 695, 44], [668, 12, 696, 10], [668, 27, 696, 25], [668, 29, 696, 27], [668, 39, 696, 37], [668, 45, 696, 43, "getAuthToken"], [668, 57, 696, 55], [668, 58, 696, 56], [668, 59, 696, 57], [669, 10, 697, 8], [669, 11, 697, 9], [670, 10, 698, 8, "body"], [670, 14, 698, 12], [670, 16, 698, 14, "JSON"], [670, 20, 698, 18], [670, 21, 698, 19, "stringify"], [670, 30, 698, 28], [670, 31, 698, 29, "requestBody"], [670, 42, 698, 40], [671, 8, 699, 6], [671, 9, 699, 7], [671, 10, 699, 8], [672, 8, 701, 6], [672, 12, 701, 10], [672, 13, 701, 11, "response"], [672, 21, 701, 19], [672, 22, 701, 20, "ok"], [672, 24, 701, 22], [672, 26, 701, 24], [673, 10, 702, 8], [673, 16, 702, 14, "errorText"], [673, 25, 702, 23], [673, 28, 702, 26], [673, 34, 702, 32, "response"], [673, 42, 702, 40], [673, 43, 702, 41, "text"], [673, 47, 702, 45], [673, 48, 702, 46], [673, 49, 702, 47], [674, 10, 703, 8, "console"], [674, 17, 703, 15], [674, 18, 703, 16, "error"], [674, 23, 703, 21], [674, 24, 703, 22], [674, 68, 703, 66], [674, 70, 703, 68, "response"], [674, 78, 703, 76], [674, 79, 703, 77, "status"], [674, 85, 703, 83], [674, 87, 703, 85, "errorText"], [674, 96, 703, 94], [674, 97, 703, 95], [675, 10, 704, 8], [675, 16, 704, 14], [675, 20, 704, 18, "Error"], [675, 25, 704, 23], [675, 26, 704, 24], [675, 48, 704, 46, "response"], [675, 56, 704, 54], [675, 57, 704, 55, "status"], [675, 63, 704, 61], [675, 67, 704, 65, "response"], [675, 75, 704, 73], [675, 76, 704, 74, "statusText"], [675, 86, 704, 84], [675, 88, 704, 86], [675, 89, 704, 87], [676, 8, 705, 6], [677, 8, 707, 6], [677, 14, 707, 12, "result"], [677, 20, 707, 18], [677, 23, 707, 21], [677, 29, 707, 27, "response"], [677, 37, 707, 35], [677, 38, 707, 36, "json"], [677, 42, 707, 40], [677, 43, 707, 41], [677, 44, 707, 42], [678, 8, 708, 6, "console"], [678, 15, 708, 13], [678, 16, 708, 14, "log"], [678, 19, 708, 17], [678, 20, 708, 18], [678, 68, 708, 66], [678, 70, 708, 68, "result"], [678, 76, 708, 74], [678, 77, 708, 75], [679, 8, 710, 6], [679, 12, 710, 10], [679, 13, 710, 11, "result"], [679, 19, 710, 17], [679, 20, 710, 18, "jobId"], [679, 25, 710, 23], [679, 27, 710, 25], [680, 10, 711, 8], [680, 16, 711, 14], [680, 20, 711, 18, "Error"], [680, 25, 711, 23], [680, 26, 711, 24], [680, 70, 711, 68], [680, 71, 711, 69], [681, 8, 712, 6], [683, 8, 714, 6], [684, 8, 715, 6], [684, 14, 715, 12, "pollForCompletion"], [684, 31, 715, 29], [684, 32, 715, 30, "result"], [684, 38, 715, 36], [684, 39, 715, 37, "jobId"], [684, 44, 715, 42], [684, 46, 715, 44, "timestamp"], [684, 55, 715, 53], [684, 56, 715, 54], [685, 6, 716, 4], [685, 7, 716, 5], [685, 8, 716, 6], [685, 15, 716, 13, "error"], [685, 20, 716, 18], [685, 22, 716, 20], [686, 8, 717, 6, "console"], [686, 15, 717, 13], [686, 16, 717, 14, "error"], [686, 21, 717, 19], [686, 22, 717, 20], [686, 57, 717, 55], [686, 59, 717, 57, "error"], [686, 64, 717, 62], [686, 65, 717, 63], [687, 8, 718, 6, "setErrorMessage"], [687, 23, 718, 21], [687, 24, 718, 22], [687, 52, 718, 50, "error"], [687, 57, 718, 55], [687, 58, 718, 56, "message"], [687, 65, 718, 63], [687, 67, 718, 65], [687, 68, 718, 66], [688, 8, 719, 6, "setProcessingState"], [688, 26, 719, 24], [688, 27, 719, 25], [688, 34, 719, 32], [688, 35, 719, 33], [689, 6, 720, 4], [690, 4, 721, 2], [690, 5, 721, 3], [691, 4, 722, 2], [692, 4, 723, 2], [692, 10, 723, 8, "pollForCompletion"], [692, 27, 723, 25], [692, 30, 723, 28], [692, 36, 723, 28, "pollForCompletion"], [692, 37, 723, 35, "jobId"], [692, 42, 723, 48], [692, 44, 723, 50, "timestamp"], [692, 53, 723, 67], [692, 55, 723, 69, "attempts"], [692, 63, 723, 77], [692, 66, 723, 80], [692, 67, 723, 81], [692, 72, 723, 86], [693, 6, 724, 4], [693, 12, 724, 10, "MAX_ATTEMPTS"], [693, 24, 724, 22], [693, 27, 724, 25], [693, 29, 724, 27], [693, 30, 724, 28], [693, 31, 724, 29], [694, 6, 725, 4], [694, 12, 725, 10, "POLL_INTERVAL"], [694, 25, 725, 23], [694, 28, 725, 26], [694, 32, 725, 30], [694, 33, 725, 31], [694, 34, 725, 32], [696, 6, 727, 4, "console"], [696, 13, 727, 11], [696, 14, 727, 12, "log"], [696, 17, 727, 15], [696, 18, 727, 16], [696, 53, 727, 51, "attempts"], [696, 61, 727, 59], [696, 64, 727, 62], [696, 65, 727, 63], [696, 69, 727, 67, "MAX_ATTEMPTS"], [696, 81, 727, 79], [696, 93, 727, 91, "jobId"], [696, 98, 727, 96], [696, 100, 727, 98], [696, 101, 727, 99], [697, 6, 729, 4], [697, 10, 729, 8, "attempts"], [697, 18, 729, 16], [697, 22, 729, 20, "MAX_ATTEMPTS"], [697, 34, 729, 32], [697, 36, 729, 34], [698, 8, 730, 6, "console"], [698, 15, 730, 13], [698, 16, 730, 14, "error"], [698, 21, 730, 19], [698, 22, 730, 20], [698, 75, 730, 73], [698, 76, 730, 74], [699, 8, 731, 6, "setErrorMessage"], [699, 23, 731, 21], [699, 24, 731, 22], [699, 63, 731, 61], [699, 64, 731, 62], [700, 8, 732, 6, "setProcessingState"], [700, 26, 732, 24], [700, 27, 732, 25], [700, 34, 732, 32], [700, 35, 732, 33], [701, 8, 733, 6], [702, 6, 734, 4], [703, 6, 736, 4], [703, 10, 736, 8], [704, 8, 737, 6], [704, 14, 737, 12, "response"], [704, 22, 737, 20], [704, 25, 737, 23], [704, 31, 737, 29, "fetch"], [704, 36, 737, 34], [704, 37, 737, 35], [704, 40, 737, 38, "API_BASE_URL"], [704, 52, 737, 50], [704, 75, 737, 73, "jobId"], [704, 80, 737, 78], [704, 82, 737, 80], [704, 84, 737, 82], [705, 10, 738, 8, "headers"], [705, 17, 738, 15], [705, 19, 738, 17], [706, 12, 739, 10], [706, 27, 739, 25], [706, 29, 739, 27], [706, 39, 739, 37], [706, 45, 739, 43, "getAuthToken"], [706, 57, 739, 55], [706, 58, 739, 56], [706, 59, 739, 57], [707, 10, 740, 8], [708, 8, 741, 6], [708, 9, 741, 7], [708, 10, 741, 8], [709, 8, 743, 6], [709, 12, 743, 10], [709, 13, 743, 11, "response"], [709, 21, 743, 19], [709, 22, 743, 20, "ok"], [709, 24, 743, 22], [709, 26, 743, 24], [710, 10, 744, 8], [710, 16, 744, 14], [710, 20, 744, 18, "Error"], [710, 25, 744, 23], [710, 26, 744, 24], [710, 34, 744, 32, "response"], [710, 42, 744, 40], [710, 43, 744, 41, "status"], [710, 49, 744, 47], [710, 54, 744, 52, "response"], [710, 62, 744, 60], [710, 63, 744, 61, "statusText"], [710, 73, 744, 71], [710, 75, 744, 73], [710, 76, 744, 74], [711, 8, 745, 6], [712, 8, 747, 6], [712, 14, 747, 12, "status"], [712, 20, 747, 18], [712, 23, 747, 21], [712, 29, 747, 27, "response"], [712, 37, 747, 35], [712, 38, 747, 36, "json"], [712, 42, 747, 40], [712, 43, 747, 41], [712, 44, 747, 42], [713, 8, 748, 6, "console"], [713, 15, 748, 13], [713, 16, 748, 14, "log"], [713, 19, 748, 17], [713, 20, 748, 18], [713, 54, 748, 52], [713, 56, 748, 54, "status"], [713, 62, 748, 60], [713, 63, 748, 61], [714, 8, 750, 6], [714, 12, 750, 10, "status"], [714, 18, 750, 16], [714, 19, 750, 17, "status"], [714, 25, 750, 23], [714, 30, 750, 28], [714, 41, 750, 39], [714, 43, 750, 41], [715, 10, 751, 8, "console"], [715, 17, 751, 15], [715, 18, 751, 16, "log"], [715, 21, 751, 19], [715, 22, 751, 20], [715, 73, 751, 71], [715, 74, 751, 72], [716, 10, 752, 8, "setProcessingProgress"], [716, 31, 752, 29], [716, 32, 752, 30], [716, 35, 752, 33], [716, 36, 752, 34], [717, 10, 753, 8, "setProcessingState"], [717, 28, 753, 26], [717, 29, 753, 27], [717, 40, 753, 38], [717, 41, 753, 39], [718, 10, 754, 8], [719, 10, 755, 8], [719, 16, 755, 14, "result"], [719, 22, 755, 20], [719, 25, 755, 23], [720, 12, 756, 10, "imageUrl"], [720, 20, 756, 18], [720, 22, 756, 20, "status"], [720, 28, 756, 26], [720, 29, 756, 27, "publicUrl"], [720, 38, 756, 36], [721, 12, 756, 38], [722, 12, 757, 10, "localUri"], [722, 20, 757, 18], [722, 22, 757, 20, "capturedPhoto"], [722, 35, 757, 33], [722, 39, 757, 37, "status"], [722, 45, 757, 43], [722, 46, 757, 44, "publicUrl"], [722, 55, 757, 53], [723, 12, 757, 55], [724, 12, 758, 10, "challengeCode"], [724, 25, 758, 23], [724, 27, 758, 25, "challengeCode"], [724, 40, 758, 38], [724, 44, 758, 42], [724, 46, 758, 44], [725, 12, 759, 10, "timestamp"], [725, 21, 759, 19], [726, 12, 760, 10, "processingStatus"], [726, 28, 760, 26], [726, 30, 760, 28], [727, 10, 761, 8], [727, 11, 761, 9], [728, 10, 762, 8, "console"], [728, 17, 762, 15], [728, 18, 762, 16, "log"], [728, 21, 762, 19], [728, 22, 762, 20], [728, 57, 762, 55], [728, 59, 762, 57, "result"], [728, 65, 762, 63], [728, 66, 762, 64], [729, 10, 763, 8, "onComplete"], [729, 20, 763, 18], [729, 21, 763, 19, "result"], [729, 27, 763, 25], [729, 28, 763, 26], [730, 10, 764, 8], [731, 8, 765, 6], [731, 9, 765, 7], [731, 15, 765, 13], [731, 19, 765, 17, "status"], [731, 25, 765, 23], [731, 26, 765, 24, "status"], [731, 32, 765, 30], [731, 37, 765, 35], [731, 45, 765, 43], [731, 47, 765, 45], [732, 10, 766, 8, "console"], [732, 17, 766, 15], [732, 18, 766, 16, "error"], [732, 23, 766, 21], [732, 24, 766, 22], [732, 60, 766, 58], [732, 62, 766, 60, "status"], [732, 68, 766, 66], [732, 69, 766, 67, "error"], [732, 74, 766, 72], [732, 75, 766, 73], [733, 10, 767, 8], [733, 16, 767, 14], [733, 20, 767, 18, "Error"], [733, 25, 767, 23], [733, 26, 767, 24, "status"], [733, 32, 767, 30], [733, 33, 767, 31, "error"], [733, 38, 767, 36], [733, 42, 767, 40], [733, 61, 767, 59], [733, 62, 767, 60], [734, 8, 768, 6], [734, 9, 768, 7], [734, 15, 768, 13], [735, 10, 769, 8], [736, 10, 770, 8], [736, 16, 770, 14, "progressValue"], [736, 29, 770, 27], [736, 32, 770, 30], [736, 34, 770, 32], [736, 37, 770, 36, "attempts"], [736, 45, 770, 44], [736, 48, 770, 47, "MAX_ATTEMPTS"], [736, 60, 770, 59], [736, 63, 770, 63], [736, 65, 770, 65], [737, 10, 771, 8, "console"], [737, 17, 771, 15], [737, 18, 771, 16, "log"], [737, 21, 771, 19], [737, 22, 771, 20], [737, 71, 771, 69, "progressValue"], [737, 84, 771, 82], [737, 87, 771, 85], [737, 88, 771, 86], [738, 10, 772, 8, "setProcessingProgress"], [738, 31, 772, 29], [738, 32, 772, 30, "progressValue"], [738, 45, 772, 43], [738, 46, 772, 44], [739, 10, 774, 8, "setTimeout"], [739, 20, 774, 18], [739, 21, 774, 19], [739, 27, 774, 25], [740, 12, 775, 10, "pollForCompletion"], [740, 29, 775, 27], [740, 30, 775, 28, "jobId"], [740, 35, 775, 33], [740, 37, 775, 35, "timestamp"], [740, 46, 775, 44], [740, 48, 775, 46, "attempts"], [740, 56, 775, 54], [740, 59, 775, 57], [740, 60, 775, 58], [740, 61, 775, 59], [741, 10, 776, 8], [741, 11, 776, 9], [741, 13, 776, 11, "POLL_INTERVAL"], [741, 26, 776, 24], [741, 27, 776, 25], [742, 8, 777, 6], [743, 6, 778, 4], [743, 7, 778, 5], [743, 8, 778, 6], [743, 15, 778, 13, "error"], [743, 20, 778, 18], [743, 22, 778, 20], [744, 8, 779, 6, "console"], [744, 15, 779, 13], [744, 16, 779, 14, "error"], [744, 21, 779, 19], [744, 22, 779, 20], [744, 54, 779, 52], [744, 56, 779, 54, "error"], [744, 61, 779, 59], [744, 62, 779, 60], [745, 8, 780, 6, "setErrorMessage"], [745, 23, 780, 21], [745, 24, 780, 22], [745, 62, 780, 60, "error"], [745, 67, 780, 65], [745, 68, 780, 66, "message"], [745, 75, 780, 73], [745, 77, 780, 75], [745, 78, 780, 76], [746, 8, 781, 6, "setProcessingState"], [746, 26, 781, 24], [746, 27, 781, 25], [746, 34, 781, 32], [746, 35, 781, 33], [747, 6, 782, 4], [748, 4, 783, 2], [748, 5, 783, 3], [749, 4, 784, 2], [750, 4, 785, 2], [750, 10, 785, 8, "getAuthToken"], [750, 22, 785, 20], [750, 25, 785, 23], [750, 31, 785, 23, "getAuthToken"], [750, 32, 785, 23], [750, 37, 785, 52], [751, 6, 786, 4], [752, 6, 787, 4], [753, 6, 788, 4], [753, 13, 788, 11], [753, 30, 788, 28], [754, 4, 789, 2], [754, 5, 789, 3], [756, 4, 791, 2], [757, 4, 792, 2], [757, 10, 792, 8, "retryCapture"], [757, 22, 792, 20], [757, 25, 792, 23], [757, 29, 792, 23, "useCallback"], [757, 47, 792, 34], [757, 49, 792, 35], [757, 55, 792, 41], [758, 6, 793, 4, "console"], [758, 13, 793, 11], [758, 14, 793, 12, "log"], [758, 17, 793, 15], [758, 18, 793, 16], [758, 55, 793, 53], [758, 56, 793, 54], [759, 6, 794, 4, "setProcessingState"], [759, 24, 794, 22], [759, 25, 794, 23], [759, 31, 794, 29], [759, 32, 794, 30], [760, 6, 795, 4, "setErrorMessage"], [760, 21, 795, 19], [760, 22, 795, 20], [760, 24, 795, 22], [760, 25, 795, 23], [761, 6, 796, 4, "setCapturedPhoto"], [761, 22, 796, 20], [761, 23, 796, 21], [761, 25, 796, 23], [761, 26, 796, 24], [762, 6, 797, 4, "setProcessingProgress"], [762, 27, 797, 25], [762, 28, 797, 26], [762, 29, 797, 27], [762, 30, 797, 28], [763, 4, 798, 2], [763, 5, 798, 3], [763, 7, 798, 5], [763, 9, 798, 7], [763, 10, 798, 8], [764, 4, 799, 2], [765, 4, 800, 2], [765, 8, 800, 2, "useEffect"], [765, 24, 800, 11], [765, 26, 800, 12], [765, 32, 800, 18], [766, 6, 801, 4, "console"], [766, 13, 801, 11], [766, 14, 801, 12, "log"], [766, 17, 801, 15], [766, 18, 801, 16], [766, 53, 801, 51], [766, 55, 801, 53, "permission"], [766, 65, 801, 63], [766, 66, 801, 64], [767, 6, 802, 4], [767, 10, 802, 8, "permission"], [767, 20, 802, 18], [767, 22, 802, 20], [768, 8, 803, 6, "console"], [768, 15, 803, 13], [768, 16, 803, 14, "log"], [768, 19, 803, 17], [768, 20, 803, 18], [768, 57, 803, 55], [768, 59, 803, 57, "permission"], [768, 69, 803, 67], [768, 70, 803, 68, "granted"], [768, 77, 803, 75], [768, 78, 803, 76], [769, 6, 804, 4], [770, 4, 805, 2], [770, 5, 805, 3], [770, 7, 805, 5], [770, 8, 805, 6, "permission"], [770, 18, 805, 16], [770, 19, 805, 17], [770, 20, 805, 18], [771, 4, 806, 2], [772, 4, 807, 2], [772, 8, 807, 6], [772, 9, 807, 7, "permission"], [772, 19, 807, 17], [772, 21, 807, 19], [773, 6, 808, 4, "console"], [773, 13, 808, 11], [773, 14, 808, 12, "log"], [773, 17, 808, 15], [773, 18, 808, 16], [773, 67, 808, 65], [773, 68, 808, 66], [774, 6, 809, 4], [774, 26, 810, 6], [774, 30, 810, 6, "_jsxDevRuntime"], [774, 44, 810, 6], [774, 45, 810, 6, "jsxDEV"], [774, 51, 810, 6], [774, 53, 810, 7, "_View"], [774, 58, 810, 7], [774, 59, 810, 7, "default"], [774, 66, 810, 11], [775, 8, 810, 12, "style"], [775, 13, 810, 17], [775, 15, 810, 19, "styles"], [775, 21, 810, 25], [775, 22, 810, 26, "container"], [775, 31, 810, 36], [776, 8, 810, 36, "children"], [776, 16, 810, 36], [776, 32, 811, 8], [776, 36, 811, 8, "_jsxDevRuntime"], [776, 50, 811, 8], [776, 51, 811, 8, "jsxDEV"], [776, 57, 811, 8], [776, 59, 811, 9, "_ActivityIndicator"], [776, 77, 811, 9], [776, 78, 811, 9, "default"], [776, 85, 811, 26], [777, 10, 811, 27, "size"], [777, 14, 811, 31], [777, 16, 811, 32], [777, 23, 811, 39], [778, 10, 811, 40, "color"], [778, 15, 811, 45], [778, 17, 811, 46], [779, 8, 811, 55], [780, 10, 811, 55, "fileName"], [780, 18, 811, 55], [780, 20, 811, 55, "_jsxFileName"], [780, 32, 811, 55], [781, 10, 811, 55, "lineNumber"], [781, 20, 811, 55], [782, 10, 811, 55, "columnNumber"], [782, 22, 811, 55], [783, 8, 811, 55], [783, 15, 811, 57], [783, 16, 811, 58], [783, 31, 812, 8], [783, 35, 812, 8, "_jsxDevRuntime"], [783, 49, 812, 8], [783, 50, 812, 8, "jsxDEV"], [783, 56, 812, 8], [783, 58, 812, 9, "_Text"], [783, 63, 812, 9], [783, 64, 812, 9, "default"], [783, 71, 812, 13], [784, 10, 812, 14, "style"], [784, 15, 812, 19], [784, 17, 812, 21, "styles"], [784, 23, 812, 27], [784, 24, 812, 28, "loadingText"], [784, 35, 812, 40], [785, 10, 812, 40, "children"], [785, 18, 812, 40], [785, 20, 812, 41], [786, 8, 812, 58], [787, 10, 812, 58, "fileName"], [787, 18, 812, 58], [787, 20, 812, 58, "_jsxFileName"], [787, 32, 812, 58], [788, 10, 812, 58, "lineNumber"], [788, 20, 812, 58], [789, 10, 812, 58, "columnNumber"], [789, 22, 812, 58], [790, 8, 812, 58], [790, 15, 812, 64], [790, 16, 812, 65], [791, 6, 812, 65], [792, 8, 812, 65, "fileName"], [792, 16, 812, 65], [792, 18, 812, 65, "_jsxFileName"], [792, 30, 812, 65], [793, 8, 812, 65, "lineNumber"], [793, 18, 812, 65], [794, 8, 812, 65, "columnNumber"], [794, 20, 812, 65], [795, 6, 812, 65], [795, 13, 813, 12], [795, 14, 813, 13], [796, 4, 815, 2], [797, 4, 816, 2], [797, 8, 816, 6], [797, 9, 816, 7, "permission"], [797, 19, 816, 17], [797, 20, 816, 18, "granted"], [797, 27, 816, 25], [797, 29, 816, 27], [798, 6, 817, 4, "console"], [798, 13, 817, 11], [798, 14, 817, 12, "log"], [798, 17, 817, 15], [798, 18, 817, 16], [798, 93, 817, 91], [798, 94, 817, 92], [799, 6, 818, 4], [799, 26, 819, 6], [799, 30, 819, 6, "_jsxDevRuntime"], [799, 44, 819, 6], [799, 45, 819, 6, "jsxDEV"], [799, 51, 819, 6], [799, 53, 819, 7, "_View"], [799, 58, 819, 7], [799, 59, 819, 7, "default"], [799, 66, 819, 11], [800, 8, 819, 12, "style"], [800, 13, 819, 17], [800, 15, 819, 19, "styles"], [800, 21, 819, 25], [800, 22, 819, 26, "container"], [800, 31, 819, 36], [801, 8, 819, 36, "children"], [801, 16, 819, 36], [801, 31, 820, 8], [801, 35, 820, 8, "_jsxDevRuntime"], [801, 49, 820, 8], [801, 50, 820, 8, "jsxDEV"], [801, 56, 820, 8], [801, 58, 820, 9, "_View"], [801, 63, 820, 9], [801, 64, 820, 9, "default"], [801, 71, 820, 13], [802, 10, 820, 14, "style"], [802, 15, 820, 19], [802, 17, 820, 21, "styles"], [802, 23, 820, 27], [802, 24, 820, 28, "permissionContent"], [802, 41, 820, 46], [803, 10, 820, 46, "children"], [803, 18, 820, 46], [803, 34, 821, 10], [803, 38, 821, 10, "_jsxDevRuntime"], [803, 52, 821, 10], [803, 53, 821, 10, "jsxDEV"], [803, 59, 821, 10], [803, 61, 821, 11, "_lucideReactNative"], [803, 79, 821, 11], [803, 80, 821, 11, "Camera"], [803, 86, 821, 21], [804, 12, 821, 22, "size"], [804, 16, 821, 26], [804, 18, 821, 28], [804, 20, 821, 31], [805, 12, 821, 32, "color"], [805, 17, 821, 37], [805, 19, 821, 38], [806, 10, 821, 47], [807, 12, 821, 47, "fileName"], [807, 20, 821, 47], [807, 22, 821, 47, "_jsxFileName"], [807, 34, 821, 47], [808, 12, 821, 47, "lineNumber"], [808, 22, 821, 47], [809, 12, 821, 47, "columnNumber"], [809, 24, 821, 47], [810, 10, 821, 47], [810, 17, 821, 49], [810, 18, 821, 50], [810, 33, 822, 10], [810, 37, 822, 10, "_jsxDevRuntime"], [810, 51, 822, 10], [810, 52, 822, 10, "jsxDEV"], [810, 58, 822, 10], [810, 60, 822, 11, "_Text"], [810, 65, 822, 11], [810, 66, 822, 11, "default"], [810, 73, 822, 15], [811, 12, 822, 16, "style"], [811, 17, 822, 21], [811, 19, 822, 23, "styles"], [811, 25, 822, 29], [811, 26, 822, 30, "permissionTitle"], [811, 41, 822, 46], [812, 12, 822, 46, "children"], [812, 20, 822, 46], [812, 22, 822, 47], [813, 10, 822, 73], [814, 12, 822, 73, "fileName"], [814, 20, 822, 73], [814, 22, 822, 73, "_jsxFileName"], [814, 34, 822, 73], [815, 12, 822, 73, "lineNumber"], [815, 22, 822, 73], [816, 12, 822, 73, "columnNumber"], [816, 24, 822, 73], [817, 10, 822, 73], [817, 17, 822, 79], [817, 18, 822, 80], [817, 33, 823, 10], [817, 37, 823, 10, "_jsxDevRuntime"], [817, 51, 823, 10], [817, 52, 823, 10, "jsxDEV"], [817, 58, 823, 10], [817, 60, 823, 11, "_Text"], [817, 65, 823, 11], [817, 66, 823, 11, "default"], [817, 73, 823, 15], [818, 12, 823, 16, "style"], [818, 17, 823, 21], [818, 19, 823, 23, "styles"], [818, 25, 823, 29], [818, 26, 823, 30, "permissionDescription"], [818, 47, 823, 52], [819, 12, 823, 52, "children"], [819, 20, 823, 52], [819, 22, 823, 53], [820, 10, 826, 10], [821, 12, 826, 10, "fileName"], [821, 20, 826, 10], [821, 22, 826, 10, "_jsxFileName"], [821, 34, 826, 10], [822, 12, 826, 10, "lineNumber"], [822, 22, 826, 10], [823, 12, 826, 10, "columnNumber"], [823, 24, 826, 10], [824, 10, 826, 10], [824, 17, 826, 16], [824, 18, 826, 17], [824, 33, 827, 10], [824, 37, 827, 10, "_jsxDevRuntime"], [824, 51, 827, 10], [824, 52, 827, 10, "jsxDEV"], [824, 58, 827, 10], [824, 60, 827, 11, "_TouchableOpacity"], [824, 77, 827, 11], [824, 78, 827, 11, "default"], [824, 85, 827, 27], [825, 12, 827, 28, "onPress"], [825, 19, 827, 35], [825, 21, 827, 37, "requestPermission"], [825, 38, 827, 55], [826, 12, 827, 56, "style"], [826, 17, 827, 61], [826, 19, 827, 63, "styles"], [826, 25, 827, 69], [826, 26, 827, 70, "primaryButton"], [826, 39, 827, 84], [827, 12, 827, 84, "children"], [827, 20, 827, 84], [827, 35, 828, 12], [827, 39, 828, 12, "_jsxDevRuntime"], [827, 53, 828, 12], [827, 54, 828, 12, "jsxDEV"], [827, 60, 828, 12], [827, 62, 828, 13, "_Text"], [827, 67, 828, 13], [827, 68, 828, 13, "default"], [827, 75, 828, 17], [828, 14, 828, 18, "style"], [828, 19, 828, 23], [828, 21, 828, 25, "styles"], [828, 27, 828, 31], [828, 28, 828, 32, "primaryButtonText"], [828, 45, 828, 50], [829, 14, 828, 50, "children"], [829, 22, 828, 50], [829, 24, 828, 51], [830, 12, 828, 67], [831, 14, 828, 67, "fileName"], [831, 22, 828, 67], [831, 24, 828, 67, "_jsxFileName"], [831, 36, 828, 67], [832, 14, 828, 67, "lineNumber"], [832, 24, 828, 67], [833, 14, 828, 67, "columnNumber"], [833, 26, 828, 67], [834, 12, 828, 67], [834, 19, 828, 73], [835, 10, 828, 74], [836, 12, 828, 74, "fileName"], [836, 20, 828, 74], [836, 22, 828, 74, "_jsxFileName"], [836, 34, 828, 74], [837, 12, 828, 74, "lineNumber"], [837, 22, 828, 74], [838, 12, 828, 74, "columnNumber"], [838, 24, 828, 74], [839, 10, 828, 74], [839, 17, 829, 28], [839, 18, 829, 29], [839, 33, 830, 10], [839, 37, 830, 10, "_jsxDevRuntime"], [839, 51, 830, 10], [839, 52, 830, 10, "jsxDEV"], [839, 58, 830, 10], [839, 60, 830, 11, "_TouchableOpacity"], [839, 77, 830, 11], [839, 78, 830, 11, "default"], [839, 85, 830, 27], [840, 12, 830, 28, "onPress"], [840, 19, 830, 35], [840, 21, 830, 37, "onCancel"], [840, 29, 830, 46], [841, 12, 830, 47, "style"], [841, 17, 830, 52], [841, 19, 830, 54, "styles"], [841, 25, 830, 60], [841, 26, 830, 61, "secondaryButton"], [841, 41, 830, 77], [842, 12, 830, 77, "children"], [842, 20, 830, 77], [842, 35, 831, 12], [842, 39, 831, 12, "_jsxDevRuntime"], [842, 53, 831, 12], [842, 54, 831, 12, "jsxDEV"], [842, 60, 831, 12], [842, 62, 831, 13, "_Text"], [842, 67, 831, 13], [842, 68, 831, 13, "default"], [842, 75, 831, 17], [843, 14, 831, 18, "style"], [843, 19, 831, 23], [843, 21, 831, 25, "styles"], [843, 27, 831, 31], [843, 28, 831, 32, "secondaryButtonText"], [843, 47, 831, 52], [844, 14, 831, 52, "children"], [844, 22, 831, 52], [844, 24, 831, 53], [845, 12, 831, 59], [846, 14, 831, 59, "fileName"], [846, 22, 831, 59], [846, 24, 831, 59, "_jsxFileName"], [846, 36, 831, 59], [847, 14, 831, 59, "lineNumber"], [847, 24, 831, 59], [848, 14, 831, 59, "columnNumber"], [848, 26, 831, 59], [849, 12, 831, 59], [849, 19, 831, 65], [850, 10, 831, 66], [851, 12, 831, 66, "fileName"], [851, 20, 831, 66], [851, 22, 831, 66, "_jsxFileName"], [851, 34, 831, 66], [852, 12, 831, 66, "lineNumber"], [852, 22, 831, 66], [853, 12, 831, 66, "columnNumber"], [853, 24, 831, 66], [854, 10, 831, 66], [854, 17, 832, 28], [854, 18, 832, 29], [855, 8, 832, 29], [856, 10, 832, 29, "fileName"], [856, 18, 832, 29], [856, 20, 832, 29, "_jsxFileName"], [856, 32, 832, 29], [857, 10, 832, 29, "lineNumber"], [857, 20, 832, 29], [858, 10, 832, 29, "columnNumber"], [858, 22, 832, 29], [859, 8, 832, 29], [859, 15, 833, 14], [860, 6, 833, 15], [861, 8, 833, 15, "fileName"], [861, 16, 833, 15], [861, 18, 833, 15, "_jsxFileName"], [861, 30, 833, 15], [862, 8, 833, 15, "lineNumber"], [862, 18, 833, 15], [863, 8, 833, 15, "columnNumber"], [863, 20, 833, 15], [864, 6, 833, 15], [864, 13, 834, 12], [864, 14, 834, 13], [865, 4, 836, 2], [866, 4, 837, 2], [867, 4, 838, 2, "console"], [867, 11, 838, 9], [867, 12, 838, 10, "log"], [867, 15, 838, 13], [867, 16, 838, 14], [867, 55, 838, 53], [867, 56, 838, 54], [868, 4, 840, 2], [868, 24, 841, 4], [868, 28, 841, 4, "_jsxDevRuntime"], [868, 42, 841, 4], [868, 43, 841, 4, "jsxDEV"], [868, 49, 841, 4], [868, 51, 841, 5, "_View"], [868, 56, 841, 5], [868, 57, 841, 5, "default"], [868, 64, 841, 9], [869, 6, 841, 10, "style"], [869, 11, 841, 15], [869, 13, 841, 17, "styles"], [869, 19, 841, 23], [869, 20, 841, 24, "container"], [869, 29, 841, 34], [870, 6, 841, 34, "children"], [870, 14, 841, 34], [870, 30, 843, 6], [870, 34, 843, 6, "_jsxDevRuntime"], [870, 48, 843, 6], [870, 49, 843, 6, "jsxDEV"], [870, 55, 843, 6], [870, 57, 843, 7, "_View"], [870, 62, 843, 7], [870, 63, 843, 7, "default"], [870, 70, 843, 11], [871, 8, 843, 12, "style"], [871, 13, 843, 17], [871, 15, 843, 19, "styles"], [871, 21, 843, 25], [871, 22, 843, 26, "cameraContainer"], [871, 37, 843, 42], [872, 8, 843, 43, "id"], [872, 10, 843, 45], [872, 12, 843, 46], [872, 29, 843, 63], [873, 8, 843, 63, "children"], [873, 16, 843, 63], [873, 32, 844, 8], [873, 36, 844, 8, "_jsxDevRuntime"], [873, 50, 844, 8], [873, 51, 844, 8, "jsxDEV"], [873, 57, 844, 8], [873, 59, 844, 9, "_expoCamera"], [873, 70, 844, 9], [873, 71, 844, 9, "CameraView"], [873, 81, 844, 19], [874, 10, 845, 10, "ref"], [874, 13, 845, 13], [874, 15, 845, 15, "cameraRef"], [874, 24, 845, 25], [875, 10, 846, 10, "style"], [875, 15, 846, 15], [875, 17, 846, 17], [875, 18, 846, 18, "styles"], [875, 24, 846, 24], [875, 25, 846, 25, "camera"], [875, 31, 846, 31], [875, 33, 846, 33], [876, 12, 846, 35, "backgroundColor"], [876, 27, 846, 50], [876, 29, 846, 52], [877, 10, 846, 62], [877, 11, 846, 63], [877, 12, 846, 65], [878, 10, 847, 10, "facing"], [878, 16, 847, 16], [878, 18, 847, 17], [878, 24, 847, 23], [879, 10, 848, 10, "onLayout"], [879, 18, 848, 18], [879, 20, 848, 21, "e"], [879, 21, 848, 22], [879, 25, 848, 27], [880, 12, 849, 12, "console"], [880, 19, 849, 19], [880, 20, 849, 20, "log"], [880, 23, 849, 23], [880, 24, 849, 24], [880, 56, 849, 56], [880, 58, 849, 58, "e"], [880, 59, 849, 59], [880, 60, 849, 60, "nativeEvent"], [880, 71, 849, 71], [880, 72, 849, 72, "layout"], [880, 78, 849, 78], [880, 79, 849, 79], [881, 12, 850, 12, "setViewSize"], [881, 23, 850, 23], [881, 24, 850, 24], [882, 14, 850, 26, "width"], [882, 19, 850, 31], [882, 21, 850, 33, "e"], [882, 22, 850, 34], [882, 23, 850, 35, "nativeEvent"], [882, 34, 850, 46], [882, 35, 850, 47, "layout"], [882, 41, 850, 53], [882, 42, 850, 54, "width"], [882, 47, 850, 59], [883, 14, 850, 61, "height"], [883, 20, 850, 67], [883, 22, 850, 69, "e"], [883, 23, 850, 70], [883, 24, 850, 71, "nativeEvent"], [883, 35, 850, 82], [883, 36, 850, 83, "layout"], [883, 42, 850, 89], [883, 43, 850, 90, "height"], [884, 12, 850, 97], [884, 13, 850, 98], [884, 14, 850, 99], [885, 10, 851, 10], [885, 11, 851, 12], [886, 10, 852, 10, "onCameraReady"], [886, 23, 852, 23], [886, 25, 852, 25, "onCameraReady"], [886, 26, 852, 25], [886, 31, 852, 31], [887, 12, 853, 12, "console"], [887, 19, 853, 19], [887, 20, 853, 20, "log"], [887, 23, 853, 23], [887, 24, 853, 24], [887, 55, 853, 55], [887, 56, 853, 56], [888, 12, 854, 12, "setIsCameraReady"], [888, 28, 854, 28], [888, 29, 854, 29], [888, 33, 854, 33], [888, 34, 854, 34], [888, 35, 854, 35], [888, 36, 854, 36], [889, 10, 855, 10], [889, 11, 855, 12], [890, 10, 856, 10, "onMountError"], [890, 22, 856, 22], [890, 24, 856, 25, "error"], [890, 29, 856, 30], [890, 33, 856, 35], [891, 12, 857, 12, "console"], [891, 19, 857, 19], [891, 20, 857, 20, "error"], [891, 25, 857, 25], [891, 26, 857, 26], [891, 63, 857, 63], [891, 65, 857, 65, "error"], [891, 70, 857, 70], [891, 71, 857, 71], [892, 12, 858, 12, "setErrorMessage"], [892, 27, 858, 27], [892, 28, 858, 28], [892, 57, 858, 57], [892, 58, 858, 58], [893, 12, 859, 12, "setProcessingState"], [893, 30, 859, 30], [893, 31, 859, 31], [893, 38, 859, 38], [893, 39, 859, 39], [894, 10, 860, 10], [895, 8, 860, 12], [896, 10, 860, 12, "fileName"], [896, 18, 860, 12], [896, 20, 860, 12, "_jsxFileName"], [896, 32, 860, 12], [897, 10, 860, 12, "lineNumber"], [897, 20, 860, 12], [898, 10, 860, 12, "columnNumber"], [898, 22, 860, 12], [899, 8, 860, 12], [899, 15, 861, 9], [899, 16, 861, 10], [899, 18, 863, 9], [899, 19, 863, 10, "isCameraReady"], [899, 32, 863, 23], [899, 49, 864, 10], [899, 53, 864, 10, "_jsxDevRuntime"], [899, 67, 864, 10], [899, 68, 864, 10, "jsxDEV"], [899, 74, 864, 10], [899, 76, 864, 11, "_View"], [899, 81, 864, 11], [899, 82, 864, 11, "default"], [899, 89, 864, 15], [900, 10, 864, 16, "style"], [900, 15, 864, 21], [900, 17, 864, 23], [900, 18, 864, 24, "StyleSheet"], [900, 37, 864, 34], [900, 38, 864, 35, "absoluteFill"], [900, 50, 864, 47], [900, 52, 864, 49], [901, 12, 864, 51, "backgroundColor"], [901, 27, 864, 66], [901, 29, 864, 68], [901, 49, 864, 88], [902, 12, 864, 90, "justifyContent"], [902, 26, 864, 104], [902, 28, 864, 106], [902, 36, 864, 114], [903, 12, 864, 116, "alignItems"], [903, 22, 864, 126], [903, 24, 864, 128], [903, 32, 864, 136], [904, 12, 864, 138, "zIndex"], [904, 18, 864, 144], [904, 20, 864, 146], [905, 10, 864, 151], [905, 11, 864, 152], [905, 12, 864, 154], [906, 10, 864, 154, "children"], [906, 18, 864, 154], [906, 33, 865, 12], [906, 37, 865, 12, "_jsxDevRuntime"], [906, 51, 865, 12], [906, 52, 865, 12, "jsxDEV"], [906, 58, 865, 12], [906, 60, 865, 13, "_View"], [906, 65, 865, 13], [906, 66, 865, 13, "default"], [906, 73, 865, 17], [907, 12, 865, 18, "style"], [907, 17, 865, 23], [907, 19, 865, 25], [908, 14, 865, 27, "backgroundColor"], [908, 29, 865, 42], [908, 31, 865, 44], [908, 51, 865, 64], [909, 14, 865, 66, "padding"], [909, 21, 865, 73], [909, 23, 865, 75], [909, 25, 865, 77], [910, 14, 865, 79, "borderRadius"], [910, 26, 865, 91], [910, 28, 865, 93], [910, 30, 865, 95], [911, 14, 865, 97, "alignItems"], [911, 24, 865, 107], [911, 26, 865, 109], [912, 12, 865, 118], [912, 13, 865, 120], [913, 12, 865, 120, "children"], [913, 20, 865, 120], [913, 36, 866, 14], [913, 40, 866, 14, "_jsxDevRuntime"], [913, 54, 866, 14], [913, 55, 866, 14, "jsxDEV"], [913, 61, 866, 14], [913, 63, 866, 15, "_ActivityIndicator"], [913, 81, 866, 15], [913, 82, 866, 15, "default"], [913, 89, 866, 32], [914, 14, 866, 33, "size"], [914, 18, 866, 37], [914, 20, 866, 38], [914, 27, 866, 45], [915, 14, 866, 46, "color"], [915, 19, 866, 51], [915, 21, 866, 52], [915, 30, 866, 61], [916, 14, 866, 62, "style"], [916, 19, 866, 67], [916, 21, 866, 69], [917, 16, 866, 71, "marginBottom"], [917, 28, 866, 83], [917, 30, 866, 85], [918, 14, 866, 88], [919, 12, 866, 90], [920, 14, 866, 90, "fileName"], [920, 22, 866, 90], [920, 24, 866, 90, "_jsxFileName"], [920, 36, 866, 90], [921, 14, 866, 90, "lineNumber"], [921, 24, 866, 90], [922, 14, 866, 90, "columnNumber"], [922, 26, 866, 90], [923, 12, 866, 90], [923, 19, 866, 92], [923, 20, 866, 93], [923, 35, 867, 14], [923, 39, 867, 14, "_jsxDevRuntime"], [923, 53, 867, 14], [923, 54, 867, 14, "jsxDEV"], [923, 60, 867, 14], [923, 62, 867, 15, "_Text"], [923, 67, 867, 15], [923, 68, 867, 15, "default"], [923, 75, 867, 19], [924, 14, 867, 20, "style"], [924, 19, 867, 25], [924, 21, 867, 27], [925, 16, 867, 29, "color"], [925, 21, 867, 34], [925, 23, 867, 36], [925, 29, 867, 42], [926, 16, 867, 44, "fontSize"], [926, 24, 867, 52], [926, 26, 867, 54], [926, 28, 867, 56], [927, 16, 867, 58, "fontWeight"], [927, 26, 867, 68], [927, 28, 867, 70], [928, 14, 867, 76], [928, 15, 867, 78], [929, 14, 867, 78, "children"], [929, 22, 867, 78], [929, 24, 867, 79], [930, 12, 867, 101], [931, 14, 867, 101, "fileName"], [931, 22, 867, 101], [931, 24, 867, 101, "_jsxFileName"], [931, 36, 867, 101], [932, 14, 867, 101, "lineNumber"], [932, 24, 867, 101], [933, 14, 867, 101, "columnNumber"], [933, 26, 867, 101], [934, 12, 867, 101], [934, 19, 867, 107], [934, 20, 867, 108], [934, 35, 868, 14], [934, 39, 868, 14, "_jsxDevRuntime"], [934, 53, 868, 14], [934, 54, 868, 14, "jsxDEV"], [934, 60, 868, 14], [934, 62, 868, 15, "_Text"], [934, 67, 868, 15], [934, 68, 868, 15, "default"], [934, 75, 868, 19], [935, 14, 868, 20, "style"], [935, 19, 868, 25], [935, 21, 868, 27], [936, 16, 868, 29, "color"], [936, 21, 868, 34], [936, 23, 868, 36], [936, 32, 868, 45], [937, 16, 868, 47, "fontSize"], [937, 24, 868, 55], [937, 26, 868, 57], [937, 28, 868, 59], [938, 16, 868, 61, "marginTop"], [938, 25, 868, 70], [938, 27, 868, 72], [939, 14, 868, 74], [939, 15, 868, 76], [940, 14, 868, 76, "children"], [940, 22, 868, 76], [940, 24, 868, 77], [941, 12, 868, 88], [942, 14, 868, 88, "fileName"], [942, 22, 868, 88], [942, 24, 868, 88, "_jsxFileName"], [942, 36, 868, 88], [943, 14, 868, 88, "lineNumber"], [943, 24, 868, 88], [944, 14, 868, 88, "columnNumber"], [944, 26, 868, 88], [945, 12, 868, 88], [945, 19, 868, 94], [945, 20, 868, 95], [946, 10, 868, 95], [947, 12, 868, 95, "fileName"], [947, 20, 868, 95], [947, 22, 868, 95, "_jsxFileName"], [947, 34, 868, 95], [948, 12, 868, 95, "lineNumber"], [948, 22, 868, 95], [949, 12, 868, 95, "columnNumber"], [949, 24, 868, 95], [950, 10, 868, 95], [950, 17, 869, 18], [951, 8, 869, 19], [952, 10, 869, 19, "fileName"], [952, 18, 869, 19], [952, 20, 869, 19, "_jsxFileName"], [952, 32, 869, 19], [953, 10, 869, 19, "lineNumber"], [953, 20, 869, 19], [954, 10, 869, 19, "columnNumber"], [954, 22, 869, 19], [955, 8, 869, 19], [955, 15, 870, 16], [955, 16, 871, 9], [955, 18, 874, 9, "isCameraReady"], [955, 31, 874, 22], [955, 35, 874, 26, "previewBlurEnabled"], [955, 53, 874, 44], [955, 57, 874, 48, "viewSize"], [955, 65, 874, 56], [955, 66, 874, 57, "width"], [955, 71, 874, 62], [955, 74, 874, 65], [955, 75, 874, 66], [955, 92, 875, 10], [955, 96, 875, 10, "_jsxDevRuntime"], [955, 110, 875, 10], [955, 111, 875, 10, "jsxDEV"], [955, 117, 875, 10], [955, 119, 875, 10, "_jsxDevRuntime"], [955, 133, 875, 10], [955, 134, 875, 10, "Fragment"], [955, 142, 875, 10], [956, 10, 875, 10, "children"], [956, 18, 875, 10], [956, 34, 877, 12], [956, 38, 877, 12, "_jsxDevRuntime"], [956, 52, 877, 12], [956, 53, 877, 12, "jsxDEV"], [956, 59, 877, 12], [956, 61, 877, 13, "_LiveFaceCanvas"], [956, 76, 877, 13], [956, 77, 877, 13, "default"], [956, 84, 877, 27], [957, 12, 877, 28, "containerId"], [957, 23, 877, 39], [957, 25, 877, 40], [957, 42, 877, 57], [958, 12, 877, 58, "width"], [958, 17, 877, 63], [958, 19, 877, 65, "viewSize"], [958, 27, 877, 73], [958, 28, 877, 74, "width"], [958, 33, 877, 80], [959, 12, 877, 81, "height"], [959, 18, 877, 87], [959, 20, 877, 89, "viewSize"], [959, 28, 877, 97], [959, 29, 877, 98, "height"], [960, 10, 877, 105], [961, 12, 877, 105, "fileName"], [961, 20, 877, 105], [961, 22, 877, 105, "_jsxFileName"], [961, 34, 877, 105], [962, 12, 877, 105, "lineNumber"], [962, 22, 877, 105], [963, 12, 877, 105, "columnNumber"], [963, 24, 877, 105], [964, 10, 877, 105], [964, 17, 877, 107], [964, 18, 877, 108], [964, 33, 878, 12], [964, 37, 878, 12, "_jsxDevRuntime"], [964, 51, 878, 12], [964, 52, 878, 12, "jsxDEV"], [964, 58, 878, 12], [964, 60, 878, 13, "_View"], [964, 65, 878, 13], [964, 66, 878, 13, "default"], [964, 73, 878, 17], [965, 12, 878, 18, "style"], [965, 17, 878, 23], [965, 19, 878, 25], [965, 20, 878, 26, "StyleSheet"], [965, 39, 878, 36], [965, 40, 878, 37, "absoluteFill"], [965, 52, 878, 49], [965, 54, 878, 51], [966, 14, 878, 53, "pointerEvents"], [966, 27, 878, 66], [966, 29, 878, 68], [967, 12, 878, 75], [967, 13, 878, 76], [967, 14, 878, 78], [968, 12, 878, 78, "children"], [968, 20, 878, 78], [968, 36, 880, 12], [968, 40, 880, 12, "_jsxDevRuntime"], [968, 54, 880, 12], [968, 55, 880, 12, "jsxDEV"], [968, 61, 880, 12], [968, 63, 880, 13, "_expoBlur"], [968, 72, 880, 13], [968, 73, 880, 13, "BlurView"], [968, 81, 880, 21], [969, 14, 880, 22, "intensity"], [969, 23, 880, 31], [969, 25, 880, 33], [969, 27, 880, 36], [970, 14, 880, 37, "tint"], [970, 18, 880, 41], [970, 20, 880, 42], [970, 26, 880, 48], [971, 14, 880, 49, "style"], [971, 19, 880, 54], [971, 21, 880, 56], [971, 22, 880, 57, "styles"], [971, 28, 880, 63], [971, 29, 880, 64, "blurZone"], [971, 37, 880, 72], [971, 39, 880, 74], [972, 16, 881, 14, "left"], [972, 20, 881, 18], [972, 22, 881, 20], [972, 23, 881, 21], [973, 16, 882, 14, "top"], [973, 19, 882, 17], [973, 21, 882, 19, "viewSize"], [973, 29, 882, 27], [973, 30, 882, 28, "height"], [973, 36, 882, 34], [973, 39, 882, 37], [973, 42, 882, 40], [974, 16, 883, 14, "width"], [974, 21, 883, 19], [974, 23, 883, 21, "viewSize"], [974, 31, 883, 29], [974, 32, 883, 30, "width"], [974, 37, 883, 35], [975, 16, 884, 14, "height"], [975, 22, 884, 20], [975, 24, 884, 22, "viewSize"], [975, 32, 884, 30], [975, 33, 884, 31, "height"], [975, 39, 884, 37], [975, 42, 884, 40], [975, 46, 884, 44], [976, 16, 885, 14, "borderRadius"], [976, 28, 885, 26], [976, 30, 885, 28], [977, 14, 886, 12], [977, 15, 886, 13], [978, 12, 886, 15], [979, 14, 886, 15, "fileName"], [979, 22, 886, 15], [979, 24, 886, 15, "_jsxFileName"], [979, 36, 886, 15], [980, 14, 886, 15, "lineNumber"], [980, 24, 886, 15], [981, 14, 886, 15, "columnNumber"], [981, 26, 886, 15], [982, 12, 886, 15], [982, 19, 886, 17], [982, 20, 886, 18], [982, 35, 888, 12], [982, 39, 888, 12, "_jsxDevRuntime"], [982, 53, 888, 12], [982, 54, 888, 12, "jsxDEV"], [982, 60, 888, 12], [982, 62, 888, 13, "_expoBlur"], [982, 71, 888, 13], [982, 72, 888, 13, "BlurView"], [982, 80, 888, 21], [983, 14, 888, 22, "intensity"], [983, 23, 888, 31], [983, 25, 888, 33], [983, 27, 888, 36], [984, 14, 888, 37, "tint"], [984, 18, 888, 41], [984, 20, 888, 42], [984, 26, 888, 48], [985, 14, 888, 49, "style"], [985, 19, 888, 54], [985, 21, 888, 56], [985, 22, 888, 57, "styles"], [985, 28, 888, 63], [985, 29, 888, 64, "blurZone"], [985, 37, 888, 72], [985, 39, 888, 74], [986, 16, 889, 14, "left"], [986, 20, 889, 18], [986, 22, 889, 20], [986, 23, 889, 21], [987, 16, 890, 14, "top"], [987, 19, 890, 17], [987, 21, 890, 19], [987, 22, 890, 20], [988, 16, 891, 14, "width"], [988, 21, 891, 19], [988, 23, 891, 21, "viewSize"], [988, 31, 891, 29], [988, 32, 891, 30, "width"], [988, 37, 891, 35], [989, 16, 892, 14, "height"], [989, 22, 892, 20], [989, 24, 892, 22, "viewSize"], [989, 32, 892, 30], [989, 33, 892, 31, "height"], [989, 39, 892, 37], [989, 42, 892, 40], [989, 45, 892, 43], [990, 16, 893, 14, "borderRadius"], [990, 28, 893, 26], [990, 30, 893, 28], [991, 14, 894, 12], [991, 15, 894, 13], [992, 12, 894, 15], [993, 14, 894, 15, "fileName"], [993, 22, 894, 15], [993, 24, 894, 15, "_jsxFileName"], [993, 36, 894, 15], [994, 14, 894, 15, "lineNumber"], [994, 24, 894, 15], [995, 14, 894, 15, "columnNumber"], [995, 26, 894, 15], [996, 12, 894, 15], [996, 19, 894, 17], [996, 20, 894, 18], [996, 35, 896, 12], [996, 39, 896, 12, "_jsxDevRuntime"], [996, 53, 896, 12], [996, 54, 896, 12, "jsxDEV"], [996, 60, 896, 12], [996, 62, 896, 13, "_expoBlur"], [996, 71, 896, 13], [996, 72, 896, 13, "BlurView"], [996, 80, 896, 21], [997, 14, 896, 22, "intensity"], [997, 23, 896, 31], [997, 25, 896, 33], [997, 27, 896, 36], [998, 14, 896, 37, "tint"], [998, 18, 896, 41], [998, 20, 896, 42], [998, 26, 896, 48], [999, 14, 896, 49, "style"], [999, 19, 896, 54], [999, 21, 896, 56], [999, 22, 896, 57, "styles"], [999, 28, 896, 63], [999, 29, 896, 64, "blurZone"], [999, 37, 896, 72], [999, 39, 896, 74], [1000, 16, 897, 14, "left"], [1000, 20, 897, 18], [1000, 22, 897, 20, "viewSize"], [1000, 30, 897, 28], [1000, 31, 897, 29, "width"], [1000, 36, 897, 34], [1000, 39, 897, 37], [1000, 42, 897, 40], [1000, 45, 897, 44, "viewSize"], [1000, 53, 897, 52], [1000, 54, 897, 53, "width"], [1000, 59, 897, 58], [1000, 62, 897, 61], [1000, 66, 897, 66], [1001, 16, 898, 14, "top"], [1001, 19, 898, 17], [1001, 21, 898, 19, "viewSize"], [1001, 29, 898, 27], [1001, 30, 898, 28, "height"], [1001, 36, 898, 34], [1001, 39, 898, 37], [1001, 43, 898, 41], [1001, 46, 898, 45, "viewSize"], [1001, 54, 898, 53], [1001, 55, 898, 54, "width"], [1001, 60, 898, 59], [1001, 63, 898, 62], [1001, 67, 898, 67], [1002, 16, 899, 14, "width"], [1002, 21, 899, 19], [1002, 23, 899, 21, "viewSize"], [1002, 31, 899, 29], [1002, 32, 899, 30, "width"], [1002, 37, 899, 35], [1002, 40, 899, 38], [1002, 43, 899, 41], [1003, 16, 900, 14, "height"], [1003, 22, 900, 20], [1003, 24, 900, 22, "viewSize"], [1003, 32, 900, 30], [1003, 33, 900, 31, "width"], [1003, 38, 900, 36], [1003, 41, 900, 39], [1003, 44, 900, 42], [1004, 16, 901, 14, "borderRadius"], [1004, 28, 901, 26], [1004, 30, 901, 29, "viewSize"], [1004, 38, 901, 37], [1004, 39, 901, 38, "width"], [1004, 44, 901, 43], [1004, 47, 901, 46], [1004, 50, 901, 49], [1004, 53, 901, 53], [1005, 14, 902, 12], [1005, 15, 902, 13], [1006, 12, 902, 15], [1007, 14, 902, 15, "fileName"], [1007, 22, 902, 15], [1007, 24, 902, 15, "_jsxFileName"], [1007, 36, 902, 15], [1008, 14, 902, 15, "lineNumber"], [1008, 24, 902, 15], [1009, 14, 902, 15, "columnNumber"], [1009, 26, 902, 15], [1010, 12, 902, 15], [1010, 19, 902, 17], [1010, 20, 902, 18], [1010, 35, 903, 12], [1010, 39, 903, 12, "_jsxDevRuntime"], [1010, 53, 903, 12], [1010, 54, 903, 12, "jsxDEV"], [1010, 60, 903, 12], [1010, 62, 903, 13, "_expoBlur"], [1010, 71, 903, 13], [1010, 72, 903, 13, "BlurView"], [1010, 80, 903, 21], [1011, 14, 903, 22, "intensity"], [1011, 23, 903, 31], [1011, 25, 903, 33], [1011, 27, 903, 36], [1012, 14, 903, 37, "tint"], [1012, 18, 903, 41], [1012, 20, 903, 42], [1012, 26, 903, 48], [1013, 14, 903, 49, "style"], [1013, 19, 903, 54], [1013, 21, 903, 56], [1013, 22, 903, 57, "styles"], [1013, 28, 903, 63], [1013, 29, 903, 64, "blurZone"], [1013, 37, 903, 72], [1013, 39, 903, 74], [1014, 16, 904, 14, "left"], [1014, 20, 904, 18], [1014, 22, 904, 20, "viewSize"], [1014, 30, 904, 28], [1014, 31, 904, 29, "width"], [1014, 36, 904, 34], [1014, 39, 904, 37], [1014, 42, 904, 40], [1014, 45, 904, 44, "viewSize"], [1014, 53, 904, 52], [1014, 54, 904, 53, "width"], [1014, 59, 904, 58], [1014, 62, 904, 61], [1014, 66, 904, 66], [1015, 16, 905, 14, "top"], [1015, 19, 905, 17], [1015, 21, 905, 19, "viewSize"], [1015, 29, 905, 27], [1015, 30, 905, 28, "height"], [1015, 36, 905, 34], [1015, 39, 905, 37], [1015, 42, 905, 40], [1015, 45, 905, 44, "viewSize"], [1015, 53, 905, 52], [1015, 54, 905, 53, "width"], [1015, 59, 905, 58], [1015, 62, 905, 61], [1015, 66, 905, 66], [1016, 16, 906, 14, "width"], [1016, 21, 906, 19], [1016, 23, 906, 21, "viewSize"], [1016, 31, 906, 29], [1016, 32, 906, 30, "width"], [1016, 37, 906, 35], [1016, 40, 906, 38], [1016, 43, 906, 41], [1017, 16, 907, 14, "height"], [1017, 22, 907, 20], [1017, 24, 907, 22, "viewSize"], [1017, 32, 907, 30], [1017, 33, 907, 31, "width"], [1017, 38, 907, 36], [1017, 41, 907, 39], [1017, 44, 907, 42], [1018, 16, 908, 14, "borderRadius"], [1018, 28, 908, 26], [1018, 30, 908, 29, "viewSize"], [1018, 38, 908, 37], [1018, 39, 908, 38, "width"], [1018, 44, 908, 43], [1018, 47, 908, 46], [1018, 50, 908, 49], [1018, 53, 908, 53], [1019, 14, 909, 12], [1019, 15, 909, 13], [1020, 12, 909, 15], [1021, 14, 909, 15, "fileName"], [1021, 22, 909, 15], [1021, 24, 909, 15, "_jsxFileName"], [1021, 36, 909, 15], [1022, 14, 909, 15, "lineNumber"], [1022, 24, 909, 15], [1023, 14, 909, 15, "columnNumber"], [1023, 26, 909, 15], [1024, 12, 909, 15], [1024, 19, 909, 17], [1024, 20, 909, 18], [1024, 35, 910, 12], [1024, 39, 910, 12, "_jsxDevRuntime"], [1024, 53, 910, 12], [1024, 54, 910, 12, "jsxDEV"], [1024, 60, 910, 12], [1024, 62, 910, 13, "_expoBlur"], [1024, 71, 910, 13], [1024, 72, 910, 13, "BlurView"], [1024, 80, 910, 21], [1025, 14, 910, 22, "intensity"], [1025, 23, 910, 31], [1025, 25, 910, 33], [1025, 27, 910, 36], [1026, 14, 910, 37, "tint"], [1026, 18, 910, 41], [1026, 20, 910, 42], [1026, 26, 910, 48], [1027, 14, 910, 49, "style"], [1027, 19, 910, 54], [1027, 21, 910, 56], [1027, 22, 910, 57, "styles"], [1027, 28, 910, 63], [1027, 29, 910, 64, "blurZone"], [1027, 37, 910, 72], [1027, 39, 910, 74], [1028, 16, 911, 14, "left"], [1028, 20, 911, 18], [1028, 22, 911, 20, "viewSize"], [1028, 30, 911, 28], [1028, 31, 911, 29, "width"], [1028, 36, 911, 34], [1028, 39, 911, 37], [1028, 42, 911, 40], [1028, 45, 911, 44, "viewSize"], [1028, 53, 911, 52], [1028, 54, 911, 53, "width"], [1028, 59, 911, 58], [1028, 62, 911, 61], [1028, 66, 911, 66], [1029, 16, 912, 14, "top"], [1029, 19, 912, 17], [1029, 21, 912, 19, "viewSize"], [1029, 29, 912, 27], [1029, 30, 912, 28, "height"], [1029, 36, 912, 34], [1029, 39, 912, 37], [1029, 42, 912, 40], [1029, 45, 912, 44, "viewSize"], [1029, 53, 912, 52], [1029, 54, 912, 53, "width"], [1029, 59, 912, 58], [1029, 62, 912, 61], [1029, 66, 912, 66], [1030, 16, 913, 14, "width"], [1030, 21, 913, 19], [1030, 23, 913, 21, "viewSize"], [1030, 31, 913, 29], [1030, 32, 913, 30, "width"], [1030, 37, 913, 35], [1030, 40, 913, 38], [1030, 43, 913, 41], [1031, 16, 914, 14, "height"], [1031, 22, 914, 20], [1031, 24, 914, 22, "viewSize"], [1031, 32, 914, 30], [1031, 33, 914, 31, "width"], [1031, 38, 914, 36], [1031, 41, 914, 39], [1031, 44, 914, 42], [1032, 16, 915, 14, "borderRadius"], [1032, 28, 915, 26], [1032, 30, 915, 29, "viewSize"], [1032, 38, 915, 37], [1032, 39, 915, 38, "width"], [1032, 44, 915, 43], [1032, 47, 915, 46], [1032, 50, 915, 49], [1032, 53, 915, 53], [1033, 14, 916, 12], [1033, 15, 916, 13], [1034, 12, 916, 15], [1035, 14, 916, 15, "fileName"], [1035, 22, 916, 15], [1035, 24, 916, 15, "_jsxFileName"], [1035, 36, 916, 15], [1036, 14, 916, 15, "lineNumber"], [1036, 24, 916, 15], [1037, 14, 916, 15, "columnNumber"], [1037, 26, 916, 15], [1038, 12, 916, 15], [1038, 19, 916, 17], [1038, 20, 916, 18], [1038, 22, 918, 13, "__DEV__"], [1038, 29, 918, 20], [1038, 46, 919, 14], [1038, 50, 919, 14, "_jsxDevRuntime"], [1038, 64, 919, 14], [1038, 65, 919, 14, "jsxDEV"], [1038, 71, 919, 14], [1038, 73, 919, 15, "_View"], [1038, 78, 919, 15], [1038, 79, 919, 15, "default"], [1038, 86, 919, 19], [1039, 14, 919, 20, "style"], [1039, 19, 919, 25], [1039, 21, 919, 27, "styles"], [1039, 27, 919, 33], [1039, 28, 919, 34, "previewChip"], [1039, 39, 919, 46], [1040, 14, 919, 46, "children"], [1040, 22, 919, 46], [1040, 37, 920, 16], [1040, 41, 920, 16, "_jsxDevRuntime"], [1040, 55, 920, 16], [1040, 56, 920, 16, "jsxDEV"], [1040, 62, 920, 16], [1040, 64, 920, 17, "_Text"], [1040, 69, 920, 17], [1040, 70, 920, 17, "default"], [1040, 77, 920, 21], [1041, 16, 920, 22, "style"], [1041, 21, 920, 27], [1041, 23, 920, 29, "styles"], [1041, 29, 920, 35], [1041, 30, 920, 36, "previewChipText"], [1041, 45, 920, 52], [1042, 16, 920, 52, "children"], [1042, 24, 920, 52], [1042, 26, 920, 53], [1043, 14, 920, 73], [1044, 16, 920, 73, "fileName"], [1044, 24, 920, 73], [1044, 26, 920, 73, "_jsxFileName"], [1044, 38, 920, 73], [1045, 16, 920, 73, "lineNumber"], [1045, 26, 920, 73], [1046, 16, 920, 73, "columnNumber"], [1046, 28, 920, 73], [1047, 14, 920, 73], [1047, 21, 920, 79], [1048, 12, 920, 80], [1049, 14, 920, 80, "fileName"], [1049, 22, 920, 80], [1049, 24, 920, 80, "_jsxFileName"], [1049, 36, 920, 80], [1050, 14, 920, 80, "lineNumber"], [1050, 24, 920, 80], [1051, 14, 920, 80, "columnNumber"], [1051, 26, 920, 80], [1052, 12, 920, 80], [1052, 19, 921, 20], [1052, 20, 922, 13], [1053, 10, 922, 13], [1054, 12, 922, 13, "fileName"], [1054, 20, 922, 13], [1054, 22, 922, 13, "_jsxFileName"], [1054, 34, 922, 13], [1055, 12, 922, 13, "lineNumber"], [1055, 22, 922, 13], [1056, 12, 922, 13, "columnNumber"], [1056, 24, 922, 13], [1057, 10, 922, 13], [1057, 17, 923, 18], [1057, 18, 923, 19], [1058, 8, 923, 19], [1058, 23, 924, 12], [1058, 24, 925, 9], [1058, 26, 927, 9, "isCameraReady"], [1058, 39, 927, 22], [1058, 56, 928, 10], [1058, 60, 928, 10, "_jsxDevRuntime"], [1058, 74, 928, 10], [1058, 75, 928, 10, "jsxDEV"], [1058, 81, 928, 10], [1058, 83, 928, 10, "_jsxDevRuntime"], [1058, 97, 928, 10], [1058, 98, 928, 10, "Fragment"], [1058, 106, 928, 10], [1059, 10, 928, 10, "children"], [1059, 18, 928, 10], [1059, 34, 930, 12], [1059, 38, 930, 12, "_jsxDevRuntime"], [1059, 52, 930, 12], [1059, 53, 930, 12, "jsxDEV"], [1059, 59, 930, 12], [1059, 61, 930, 13, "_View"], [1059, 66, 930, 13], [1059, 67, 930, 13, "default"], [1059, 74, 930, 17], [1060, 12, 930, 18, "style"], [1060, 17, 930, 23], [1060, 19, 930, 25, "styles"], [1060, 25, 930, 31], [1060, 26, 930, 32, "headerOverlay"], [1060, 39, 930, 46], [1061, 12, 930, 46, "children"], [1061, 20, 930, 46], [1061, 35, 931, 14], [1061, 39, 931, 14, "_jsxDevRuntime"], [1061, 53, 931, 14], [1061, 54, 931, 14, "jsxDEV"], [1061, 60, 931, 14], [1061, 62, 931, 15, "_View"], [1061, 67, 931, 15], [1061, 68, 931, 15, "default"], [1061, 75, 931, 19], [1062, 14, 931, 20, "style"], [1062, 19, 931, 25], [1062, 21, 931, 27, "styles"], [1062, 27, 931, 33], [1062, 28, 931, 34, "headerContent"], [1062, 41, 931, 48], [1063, 14, 931, 48, "children"], [1063, 22, 931, 48], [1063, 38, 932, 16], [1063, 42, 932, 16, "_jsxDevRuntime"], [1063, 56, 932, 16], [1063, 57, 932, 16, "jsxDEV"], [1063, 63, 932, 16], [1063, 65, 932, 17, "_View"], [1063, 70, 932, 17], [1063, 71, 932, 17, "default"], [1063, 78, 932, 21], [1064, 16, 932, 22, "style"], [1064, 21, 932, 27], [1064, 23, 932, 29, "styles"], [1064, 29, 932, 35], [1064, 30, 932, 36, "headerLeft"], [1064, 40, 932, 47], [1065, 16, 932, 47, "children"], [1065, 24, 932, 47], [1065, 40, 933, 18], [1065, 44, 933, 18, "_jsxDevRuntime"], [1065, 58, 933, 18], [1065, 59, 933, 18, "jsxDEV"], [1065, 65, 933, 18], [1065, 67, 933, 19, "_Text"], [1065, 72, 933, 19], [1065, 73, 933, 19, "default"], [1065, 80, 933, 23], [1066, 18, 933, 24, "style"], [1066, 23, 933, 29], [1066, 25, 933, 31, "styles"], [1066, 31, 933, 37], [1066, 32, 933, 38, "headerTitle"], [1066, 43, 933, 50], [1067, 18, 933, 50, "children"], [1067, 26, 933, 50], [1067, 28, 933, 51], [1068, 16, 933, 62], [1069, 18, 933, 62, "fileName"], [1069, 26, 933, 62], [1069, 28, 933, 62, "_jsxFileName"], [1069, 40, 933, 62], [1070, 18, 933, 62, "lineNumber"], [1070, 28, 933, 62], [1071, 18, 933, 62, "columnNumber"], [1071, 30, 933, 62], [1072, 16, 933, 62], [1072, 23, 933, 68], [1072, 24, 933, 69], [1072, 39, 934, 18], [1072, 43, 934, 18, "_jsxDevRuntime"], [1072, 57, 934, 18], [1072, 58, 934, 18, "jsxDEV"], [1072, 64, 934, 18], [1072, 66, 934, 19, "_View"], [1072, 71, 934, 19], [1072, 72, 934, 19, "default"], [1072, 79, 934, 23], [1073, 18, 934, 24, "style"], [1073, 23, 934, 29], [1073, 25, 934, 31, "styles"], [1073, 31, 934, 37], [1073, 32, 934, 38, "subtitleRow"], [1073, 43, 934, 50], [1074, 18, 934, 50, "children"], [1074, 26, 934, 50], [1074, 42, 935, 20], [1074, 46, 935, 20, "_jsxDevRuntime"], [1074, 60, 935, 20], [1074, 61, 935, 20, "jsxDEV"], [1074, 67, 935, 20], [1074, 69, 935, 21, "_Text"], [1074, 74, 935, 21], [1074, 75, 935, 21, "default"], [1074, 82, 935, 25], [1075, 20, 935, 26, "style"], [1075, 25, 935, 31], [1075, 27, 935, 33, "styles"], [1075, 33, 935, 39], [1075, 34, 935, 40, "webIcon"], [1075, 41, 935, 48], [1076, 20, 935, 48, "children"], [1076, 28, 935, 48], [1076, 30, 935, 49], [1077, 18, 935, 51], [1078, 20, 935, 51, "fileName"], [1078, 28, 935, 51], [1078, 30, 935, 51, "_jsxFileName"], [1078, 42, 935, 51], [1079, 20, 935, 51, "lineNumber"], [1079, 30, 935, 51], [1080, 20, 935, 51, "columnNumber"], [1080, 32, 935, 51], [1081, 18, 935, 51], [1081, 25, 935, 57], [1081, 26, 935, 58], [1081, 41, 936, 20], [1081, 45, 936, 20, "_jsxDevRuntime"], [1081, 59, 936, 20], [1081, 60, 936, 20, "jsxDEV"], [1081, 66, 936, 20], [1081, 68, 936, 21, "_Text"], [1081, 73, 936, 21], [1081, 74, 936, 21, "default"], [1081, 81, 936, 25], [1082, 20, 936, 26, "style"], [1082, 25, 936, 31], [1082, 27, 936, 33, "styles"], [1082, 33, 936, 39], [1082, 34, 936, 40, "headerSubtitle"], [1082, 48, 936, 55], [1083, 20, 936, 55, "children"], [1083, 28, 936, 55], [1083, 30, 936, 56], [1084, 18, 936, 71], [1085, 20, 936, 71, "fileName"], [1085, 28, 936, 71], [1085, 30, 936, 71, "_jsxFileName"], [1085, 42, 936, 71], [1086, 20, 936, 71, "lineNumber"], [1086, 30, 936, 71], [1087, 20, 936, 71, "columnNumber"], [1087, 32, 936, 71], [1088, 18, 936, 71], [1088, 25, 936, 77], [1088, 26, 936, 78], [1089, 16, 936, 78], [1090, 18, 936, 78, "fileName"], [1090, 26, 936, 78], [1090, 28, 936, 78, "_jsxFileName"], [1090, 40, 936, 78], [1091, 18, 936, 78, "lineNumber"], [1091, 28, 936, 78], [1092, 18, 936, 78, "columnNumber"], [1092, 30, 936, 78], [1093, 16, 936, 78], [1093, 23, 937, 24], [1093, 24, 937, 25], [1093, 26, 938, 19, "challengeCode"], [1093, 39, 938, 32], [1093, 56, 939, 20], [1093, 60, 939, 20, "_jsxDevRuntime"], [1093, 74, 939, 20], [1093, 75, 939, 20, "jsxDEV"], [1093, 81, 939, 20], [1093, 83, 939, 21, "_View"], [1093, 88, 939, 21], [1093, 89, 939, 21, "default"], [1093, 96, 939, 25], [1094, 18, 939, 26, "style"], [1094, 23, 939, 31], [1094, 25, 939, 33, "styles"], [1094, 31, 939, 39], [1094, 32, 939, 40, "challengeRow"], [1094, 44, 939, 53], [1095, 18, 939, 53, "children"], [1095, 26, 939, 53], [1095, 42, 940, 22], [1095, 46, 940, 22, "_jsxDevRuntime"], [1095, 60, 940, 22], [1095, 61, 940, 22, "jsxDEV"], [1095, 67, 940, 22], [1095, 69, 940, 23, "_lucideReactNative"], [1095, 87, 940, 23], [1095, 88, 940, 23, "Shield"], [1095, 94, 940, 29], [1096, 20, 940, 30, "size"], [1096, 24, 940, 34], [1096, 26, 940, 36], [1096, 28, 940, 39], [1097, 20, 940, 40, "color"], [1097, 25, 940, 45], [1097, 27, 940, 46], [1098, 18, 940, 52], [1099, 20, 940, 52, "fileName"], [1099, 28, 940, 52], [1099, 30, 940, 52, "_jsxFileName"], [1099, 42, 940, 52], [1100, 20, 940, 52, "lineNumber"], [1100, 30, 940, 52], [1101, 20, 940, 52, "columnNumber"], [1101, 32, 940, 52], [1102, 18, 940, 52], [1102, 25, 940, 54], [1102, 26, 940, 55], [1102, 41, 941, 22], [1102, 45, 941, 22, "_jsxDevRuntime"], [1102, 59, 941, 22], [1102, 60, 941, 22, "jsxDEV"], [1102, 66, 941, 22], [1102, 68, 941, 23, "_Text"], [1102, 73, 941, 23], [1102, 74, 941, 23, "default"], [1102, 81, 941, 27], [1103, 20, 941, 28, "style"], [1103, 25, 941, 33], [1103, 27, 941, 35, "styles"], [1103, 33, 941, 41], [1103, 34, 941, 42, "challengeCode"], [1103, 47, 941, 56], [1104, 20, 941, 56, "children"], [1104, 28, 941, 56], [1104, 30, 941, 58, "challengeCode"], [1105, 18, 941, 71], [1106, 20, 941, 71, "fileName"], [1106, 28, 941, 71], [1106, 30, 941, 71, "_jsxFileName"], [1106, 42, 941, 71], [1107, 20, 941, 71, "lineNumber"], [1107, 30, 941, 71], [1108, 20, 941, 71, "columnNumber"], [1108, 32, 941, 71], [1109, 18, 941, 71], [1109, 25, 941, 78], [1109, 26, 941, 79], [1110, 16, 941, 79], [1111, 18, 941, 79, "fileName"], [1111, 26, 941, 79], [1111, 28, 941, 79, "_jsxFileName"], [1111, 40, 941, 79], [1112, 18, 941, 79, "lineNumber"], [1112, 28, 941, 79], [1113, 18, 941, 79, "columnNumber"], [1113, 30, 941, 79], [1114, 16, 941, 79], [1114, 23, 942, 26], [1114, 24, 943, 19], [1115, 14, 943, 19], [1116, 16, 943, 19, "fileName"], [1116, 24, 943, 19], [1116, 26, 943, 19, "_jsxFileName"], [1116, 38, 943, 19], [1117, 16, 943, 19, "lineNumber"], [1117, 26, 943, 19], [1118, 16, 943, 19, "columnNumber"], [1118, 28, 943, 19], [1119, 14, 943, 19], [1119, 21, 944, 22], [1119, 22, 944, 23], [1119, 37, 945, 16], [1119, 41, 945, 16, "_jsxDevRuntime"], [1119, 55, 945, 16], [1119, 56, 945, 16, "jsxDEV"], [1119, 62, 945, 16], [1119, 64, 945, 17, "_TouchableOpacity"], [1119, 81, 945, 17], [1119, 82, 945, 17, "default"], [1119, 89, 945, 33], [1120, 16, 945, 34, "onPress"], [1120, 23, 945, 41], [1120, 25, 945, 43, "onCancel"], [1120, 33, 945, 52], [1121, 16, 945, 53, "style"], [1121, 21, 945, 58], [1121, 23, 945, 60, "styles"], [1121, 29, 945, 66], [1121, 30, 945, 67, "closeButton"], [1121, 41, 945, 79], [1122, 16, 945, 79, "children"], [1122, 24, 945, 79], [1122, 39, 946, 18], [1122, 43, 946, 18, "_jsxDevRuntime"], [1122, 57, 946, 18], [1122, 58, 946, 18, "jsxDEV"], [1122, 64, 946, 18], [1122, 66, 946, 19, "_lucideReactNative"], [1122, 84, 946, 19], [1122, 85, 946, 19, "X"], [1122, 86, 946, 20], [1123, 18, 946, 21, "size"], [1123, 22, 946, 25], [1123, 24, 946, 27], [1123, 26, 946, 30], [1124, 18, 946, 31, "color"], [1124, 23, 946, 36], [1124, 25, 946, 37], [1125, 16, 946, 43], [1126, 18, 946, 43, "fileName"], [1126, 26, 946, 43], [1126, 28, 946, 43, "_jsxFileName"], [1126, 40, 946, 43], [1127, 18, 946, 43, "lineNumber"], [1127, 28, 946, 43], [1128, 18, 946, 43, "columnNumber"], [1128, 30, 946, 43], [1129, 16, 946, 43], [1129, 23, 946, 45], [1130, 14, 946, 46], [1131, 16, 946, 46, "fileName"], [1131, 24, 946, 46], [1131, 26, 946, 46, "_jsxFileName"], [1131, 38, 946, 46], [1132, 16, 946, 46, "lineNumber"], [1132, 26, 946, 46], [1133, 16, 946, 46, "columnNumber"], [1133, 28, 946, 46], [1134, 14, 946, 46], [1134, 21, 947, 34], [1134, 22, 947, 35], [1135, 12, 947, 35], [1136, 14, 947, 35, "fileName"], [1136, 22, 947, 35], [1136, 24, 947, 35, "_jsxFileName"], [1136, 36, 947, 35], [1137, 14, 947, 35, "lineNumber"], [1137, 24, 947, 35], [1138, 14, 947, 35, "columnNumber"], [1138, 26, 947, 35], [1139, 12, 947, 35], [1139, 19, 948, 20], [1140, 10, 948, 21], [1141, 12, 948, 21, "fileName"], [1141, 20, 948, 21], [1141, 22, 948, 21, "_jsxFileName"], [1141, 34, 948, 21], [1142, 12, 948, 21, "lineNumber"], [1142, 22, 948, 21], [1143, 12, 948, 21, "columnNumber"], [1143, 24, 948, 21], [1144, 10, 948, 21], [1144, 17, 949, 18], [1144, 18, 949, 19], [1144, 33, 951, 12], [1144, 37, 951, 12, "_jsxDevRuntime"], [1144, 51, 951, 12], [1144, 52, 951, 12, "jsxDEV"], [1144, 58, 951, 12], [1144, 60, 951, 13, "_View"], [1144, 65, 951, 13], [1144, 66, 951, 13, "default"], [1144, 73, 951, 17], [1145, 12, 951, 18, "style"], [1145, 17, 951, 23], [1145, 19, 951, 25, "styles"], [1145, 25, 951, 31], [1145, 26, 951, 32, "privacyNotice"], [1145, 39, 951, 46], [1146, 12, 951, 46, "children"], [1146, 20, 951, 46], [1146, 36, 952, 14], [1146, 40, 952, 14, "_jsxDevRuntime"], [1146, 54, 952, 14], [1146, 55, 952, 14, "jsxDEV"], [1146, 61, 952, 14], [1146, 63, 952, 15, "_lucideReactNative"], [1146, 81, 952, 15], [1146, 82, 952, 15, "Shield"], [1146, 88, 952, 21], [1147, 14, 952, 22, "size"], [1147, 18, 952, 26], [1147, 20, 952, 28], [1147, 22, 952, 31], [1148, 14, 952, 32, "color"], [1148, 19, 952, 37], [1148, 21, 952, 38], [1149, 12, 952, 47], [1150, 14, 952, 47, "fileName"], [1150, 22, 952, 47], [1150, 24, 952, 47, "_jsxFileName"], [1150, 36, 952, 47], [1151, 14, 952, 47, "lineNumber"], [1151, 24, 952, 47], [1152, 14, 952, 47, "columnNumber"], [1152, 26, 952, 47], [1153, 12, 952, 47], [1153, 19, 952, 49], [1153, 20, 952, 50], [1153, 35, 953, 14], [1153, 39, 953, 14, "_jsxDevRuntime"], [1153, 53, 953, 14], [1153, 54, 953, 14, "jsxDEV"], [1153, 60, 953, 14], [1153, 62, 953, 15, "_Text"], [1153, 67, 953, 15], [1153, 68, 953, 15, "default"], [1153, 75, 953, 19], [1154, 14, 953, 20, "style"], [1154, 19, 953, 25], [1154, 21, 953, 27, "styles"], [1154, 27, 953, 33], [1154, 28, 953, 34, "privacyText"], [1154, 39, 953, 46], [1155, 14, 953, 46, "children"], [1155, 22, 953, 46], [1155, 24, 953, 47], [1156, 12, 955, 14], [1157, 14, 955, 14, "fileName"], [1157, 22, 955, 14], [1157, 24, 955, 14, "_jsxFileName"], [1157, 36, 955, 14], [1158, 14, 955, 14, "lineNumber"], [1158, 24, 955, 14], [1159, 14, 955, 14, "columnNumber"], [1159, 26, 955, 14], [1160, 12, 955, 14], [1160, 19, 955, 20], [1160, 20, 955, 21], [1161, 10, 955, 21], [1162, 12, 955, 21, "fileName"], [1162, 20, 955, 21], [1162, 22, 955, 21, "_jsxFileName"], [1162, 34, 955, 21], [1163, 12, 955, 21, "lineNumber"], [1163, 22, 955, 21], [1164, 12, 955, 21, "columnNumber"], [1164, 24, 955, 21], [1165, 10, 955, 21], [1165, 17, 956, 18], [1165, 18, 956, 19], [1165, 33, 958, 12], [1165, 37, 958, 12, "_jsxDevRuntime"], [1165, 51, 958, 12], [1165, 52, 958, 12, "jsxDEV"], [1165, 58, 958, 12], [1165, 60, 958, 13, "_View"], [1165, 65, 958, 13], [1165, 66, 958, 13, "default"], [1165, 73, 958, 17], [1166, 12, 958, 18, "style"], [1166, 17, 958, 23], [1166, 19, 958, 25, "styles"], [1166, 25, 958, 31], [1166, 26, 958, 32, "footer<PERSON><PERSON><PERSON>"], [1166, 39, 958, 46], [1167, 12, 958, 46, "children"], [1167, 20, 958, 46], [1167, 36, 959, 14], [1167, 40, 959, 14, "_jsxDevRuntime"], [1167, 54, 959, 14], [1167, 55, 959, 14, "jsxDEV"], [1167, 61, 959, 14], [1167, 63, 959, 15, "_Text"], [1167, 68, 959, 15], [1167, 69, 959, 15, "default"], [1167, 76, 959, 19], [1168, 14, 959, 20, "style"], [1168, 19, 959, 25], [1168, 21, 959, 27, "styles"], [1168, 27, 959, 33], [1168, 28, 959, 34, "instruction"], [1168, 39, 959, 46], [1169, 14, 959, 46, "children"], [1169, 22, 959, 46], [1169, 24, 959, 47], [1170, 12, 961, 14], [1171, 14, 961, 14, "fileName"], [1171, 22, 961, 14], [1171, 24, 961, 14, "_jsxFileName"], [1171, 36, 961, 14], [1172, 14, 961, 14, "lineNumber"], [1172, 24, 961, 14], [1173, 14, 961, 14, "columnNumber"], [1173, 26, 961, 14], [1174, 12, 961, 14], [1174, 19, 961, 20], [1174, 20, 961, 21], [1174, 35, 963, 14], [1174, 39, 963, 14, "_jsxDevRuntime"], [1174, 53, 963, 14], [1174, 54, 963, 14, "jsxDEV"], [1174, 60, 963, 14], [1174, 62, 963, 15, "_TouchableOpacity"], [1174, 79, 963, 15], [1174, 80, 963, 15, "default"], [1174, 87, 963, 31], [1175, 14, 964, 16, "onPress"], [1175, 21, 964, 23], [1175, 23, 964, 25, "capturePhoto"], [1175, 35, 964, 38], [1176, 14, 965, 16, "disabled"], [1176, 22, 965, 24], [1176, 24, 965, 26, "processingState"], [1176, 39, 965, 41], [1176, 44, 965, 46], [1176, 50, 965, 52], [1176, 54, 965, 56], [1176, 55, 965, 57, "isCameraReady"], [1176, 68, 965, 71], [1177, 14, 966, 16, "style"], [1177, 19, 966, 21], [1177, 21, 966, 23], [1177, 22, 967, 18, "styles"], [1177, 28, 967, 24], [1177, 29, 967, 25, "shutterButton"], [1177, 42, 967, 38], [1177, 44, 968, 18, "processingState"], [1177, 59, 968, 33], [1177, 64, 968, 38], [1177, 70, 968, 44], [1177, 74, 968, 48, "styles"], [1177, 80, 968, 54], [1177, 81, 968, 55, "shutterButtonDisabled"], [1177, 102, 968, 76], [1177, 103, 969, 18], [1178, 14, 969, 18, "children"], [1178, 22, 969, 18], [1178, 24, 971, 17, "processingState"], [1178, 39, 971, 32], [1178, 44, 971, 37], [1178, 50, 971, 43], [1178, 66, 972, 18], [1178, 70, 972, 18, "_jsxDevRuntime"], [1178, 84, 972, 18], [1178, 85, 972, 18, "jsxDEV"], [1178, 91, 972, 18], [1178, 93, 972, 19, "_View"], [1178, 98, 972, 19], [1178, 99, 972, 19, "default"], [1178, 106, 972, 23], [1179, 16, 972, 24, "style"], [1179, 21, 972, 29], [1179, 23, 972, 31, "styles"], [1179, 29, 972, 37], [1179, 30, 972, 38, "shutterInner"], [1180, 14, 972, 51], [1181, 16, 972, 51, "fileName"], [1181, 24, 972, 51], [1181, 26, 972, 51, "_jsxFileName"], [1181, 38, 972, 51], [1182, 16, 972, 51, "lineNumber"], [1182, 26, 972, 51], [1183, 16, 972, 51, "columnNumber"], [1183, 28, 972, 51], [1184, 14, 972, 51], [1184, 21, 972, 53], [1184, 22, 972, 54], [1184, 38, 974, 18], [1184, 42, 974, 18, "_jsxDevRuntime"], [1184, 56, 974, 18], [1184, 57, 974, 18, "jsxDEV"], [1184, 63, 974, 18], [1184, 65, 974, 19, "_ActivityIndicator"], [1184, 83, 974, 19], [1184, 84, 974, 19, "default"], [1184, 91, 974, 36], [1185, 16, 974, 37, "size"], [1185, 20, 974, 41], [1185, 22, 974, 42], [1185, 29, 974, 49], [1186, 16, 974, 50, "color"], [1186, 21, 974, 55], [1186, 23, 974, 56], [1187, 14, 974, 65], [1188, 16, 974, 65, "fileName"], [1188, 24, 974, 65], [1188, 26, 974, 65, "_jsxFileName"], [1188, 38, 974, 65], [1189, 16, 974, 65, "lineNumber"], [1189, 26, 974, 65], [1190, 16, 974, 65, "columnNumber"], [1190, 28, 974, 65], [1191, 14, 974, 65], [1191, 21, 974, 67], [1192, 12, 975, 17], [1193, 14, 975, 17, "fileName"], [1193, 22, 975, 17], [1193, 24, 975, 17, "_jsxFileName"], [1193, 36, 975, 17], [1194, 14, 975, 17, "lineNumber"], [1194, 24, 975, 17], [1195, 14, 975, 17, "columnNumber"], [1195, 26, 975, 17], [1196, 12, 975, 17], [1196, 19, 976, 32], [1196, 20, 976, 33], [1196, 35, 977, 14], [1196, 39, 977, 14, "_jsxDevRuntime"], [1196, 53, 977, 14], [1196, 54, 977, 14, "jsxDEV"], [1196, 60, 977, 14], [1196, 62, 977, 15, "_Text"], [1196, 67, 977, 15], [1196, 68, 977, 15, "default"], [1196, 75, 977, 19], [1197, 14, 977, 20, "style"], [1197, 19, 977, 25], [1197, 21, 977, 27, "styles"], [1197, 27, 977, 33], [1197, 28, 977, 34, "privacyNote"], [1197, 39, 977, 46], [1198, 14, 977, 46, "children"], [1198, 22, 977, 46], [1198, 24, 977, 47], [1199, 12, 979, 14], [1200, 14, 979, 14, "fileName"], [1200, 22, 979, 14], [1200, 24, 979, 14, "_jsxFileName"], [1200, 36, 979, 14], [1201, 14, 979, 14, "lineNumber"], [1201, 24, 979, 14], [1202, 14, 979, 14, "columnNumber"], [1202, 26, 979, 14], [1203, 12, 979, 14], [1203, 19, 979, 20], [1203, 20, 979, 21], [1204, 10, 979, 21], [1205, 12, 979, 21, "fileName"], [1205, 20, 979, 21], [1205, 22, 979, 21, "_jsxFileName"], [1205, 34, 979, 21], [1206, 12, 979, 21, "lineNumber"], [1206, 22, 979, 21], [1207, 12, 979, 21, "columnNumber"], [1207, 24, 979, 21], [1208, 10, 979, 21], [1208, 17, 980, 18], [1208, 18, 980, 19], [1209, 8, 980, 19], [1209, 23, 981, 12], [1209, 24, 982, 9], [1210, 6, 982, 9], [1211, 8, 982, 9, "fileName"], [1211, 16, 982, 9], [1211, 18, 982, 9, "_jsxFileName"], [1211, 30, 982, 9], [1212, 8, 982, 9, "lineNumber"], [1212, 18, 982, 9], [1213, 8, 982, 9, "columnNumber"], [1213, 20, 982, 9], [1214, 6, 982, 9], [1214, 13, 983, 12], [1214, 14, 983, 13], [1214, 29, 985, 6], [1214, 33, 985, 6, "_jsxDevRuntime"], [1214, 47, 985, 6], [1214, 48, 985, 6, "jsxDEV"], [1214, 54, 985, 6], [1214, 56, 985, 7, "_Modal"], [1214, 62, 985, 7], [1214, 63, 985, 7, "default"], [1214, 70, 985, 12], [1215, 8, 986, 8, "visible"], [1215, 15, 986, 15], [1215, 17, 986, 17, "processingState"], [1215, 32, 986, 32], [1215, 37, 986, 37], [1215, 43, 986, 43], [1215, 47, 986, 47, "processingState"], [1215, 62, 986, 62], [1215, 67, 986, 67], [1215, 74, 986, 75], [1216, 8, 987, 8, "transparent"], [1216, 19, 987, 19], [1217, 8, 988, 8, "animationType"], [1217, 21, 988, 21], [1217, 23, 988, 22], [1217, 29, 988, 28], [1218, 8, 988, 28, "children"], [1218, 16, 988, 28], [1218, 31, 990, 8], [1218, 35, 990, 8, "_jsxDevRuntime"], [1218, 49, 990, 8], [1218, 50, 990, 8, "jsxDEV"], [1218, 56, 990, 8], [1218, 58, 990, 9, "_View"], [1218, 63, 990, 9], [1218, 64, 990, 9, "default"], [1218, 71, 990, 13], [1219, 10, 990, 14, "style"], [1219, 15, 990, 19], [1219, 17, 990, 21, "styles"], [1219, 23, 990, 27], [1219, 24, 990, 28, "processingModal"], [1219, 39, 990, 44], [1220, 10, 990, 44, "children"], [1220, 18, 990, 44], [1220, 33, 991, 10], [1220, 37, 991, 10, "_jsxDevRuntime"], [1220, 51, 991, 10], [1220, 52, 991, 10, "jsxDEV"], [1220, 58, 991, 10], [1220, 60, 991, 11, "_View"], [1220, 65, 991, 11], [1220, 66, 991, 11, "default"], [1220, 73, 991, 15], [1221, 12, 991, 16, "style"], [1221, 17, 991, 21], [1221, 19, 991, 23, "styles"], [1221, 25, 991, 29], [1221, 26, 991, 30, "processingContent"], [1221, 43, 991, 48], [1222, 12, 991, 48, "children"], [1222, 20, 991, 48], [1222, 36, 992, 12], [1222, 40, 992, 12, "_jsxDevRuntime"], [1222, 54, 992, 12], [1222, 55, 992, 12, "jsxDEV"], [1222, 61, 992, 12], [1222, 63, 992, 13, "_ActivityIndicator"], [1222, 81, 992, 13], [1222, 82, 992, 13, "default"], [1222, 89, 992, 30], [1223, 14, 992, 31, "size"], [1223, 18, 992, 35], [1223, 20, 992, 36], [1223, 27, 992, 43], [1224, 14, 992, 44, "color"], [1224, 19, 992, 49], [1224, 21, 992, 50], [1225, 12, 992, 59], [1226, 14, 992, 59, "fileName"], [1226, 22, 992, 59], [1226, 24, 992, 59, "_jsxFileName"], [1226, 36, 992, 59], [1227, 14, 992, 59, "lineNumber"], [1227, 24, 992, 59], [1228, 14, 992, 59, "columnNumber"], [1228, 26, 992, 59], [1229, 12, 992, 59], [1229, 19, 992, 61], [1229, 20, 992, 62], [1229, 35, 994, 12], [1229, 39, 994, 12, "_jsxDevRuntime"], [1229, 53, 994, 12], [1229, 54, 994, 12, "jsxDEV"], [1229, 60, 994, 12], [1229, 62, 994, 13, "_Text"], [1229, 67, 994, 13], [1229, 68, 994, 13, "default"], [1229, 75, 994, 17], [1230, 14, 994, 18, "style"], [1230, 19, 994, 23], [1230, 21, 994, 25, "styles"], [1230, 27, 994, 31], [1230, 28, 994, 32, "processingTitle"], [1230, 43, 994, 48], [1231, 14, 994, 48, "children"], [1231, 22, 994, 48], [1231, 25, 995, 15, "processingState"], [1231, 40, 995, 30], [1231, 45, 995, 35], [1231, 56, 995, 46], [1231, 60, 995, 50], [1231, 80, 995, 70], [1231, 82, 996, 15, "processingState"], [1231, 97, 996, 30], [1231, 102, 996, 35], [1231, 113, 996, 46], [1231, 117, 996, 50], [1231, 146, 996, 79], [1231, 148, 997, 15, "processingState"], [1231, 163, 997, 30], [1231, 168, 997, 35], [1231, 180, 997, 47], [1231, 184, 997, 51], [1231, 216, 997, 83], [1231, 218, 998, 15, "processingState"], [1231, 233, 998, 30], [1231, 238, 998, 35], [1231, 249, 998, 46], [1231, 253, 998, 50], [1231, 275, 998, 72], [1232, 12, 998, 72], [1233, 14, 998, 72, "fileName"], [1233, 22, 998, 72], [1233, 24, 998, 72, "_jsxFileName"], [1233, 36, 998, 72], [1234, 14, 998, 72, "lineNumber"], [1234, 24, 998, 72], [1235, 14, 998, 72, "columnNumber"], [1235, 26, 998, 72], [1236, 12, 998, 72], [1236, 19, 999, 18], [1236, 20, 999, 19], [1236, 35, 1000, 12], [1236, 39, 1000, 12, "_jsxDevRuntime"], [1236, 53, 1000, 12], [1236, 54, 1000, 12, "jsxDEV"], [1236, 60, 1000, 12], [1236, 62, 1000, 13, "_View"], [1236, 67, 1000, 13], [1236, 68, 1000, 13, "default"], [1236, 75, 1000, 17], [1237, 14, 1000, 18, "style"], [1237, 19, 1000, 23], [1237, 21, 1000, 25, "styles"], [1237, 27, 1000, 31], [1237, 28, 1000, 32, "progressBar"], [1237, 39, 1000, 44], [1238, 14, 1000, 44, "children"], [1238, 22, 1000, 44], [1238, 37, 1001, 14], [1238, 41, 1001, 14, "_jsxDevRuntime"], [1238, 55, 1001, 14], [1238, 56, 1001, 14, "jsxDEV"], [1238, 62, 1001, 14], [1238, 64, 1001, 15, "_View"], [1238, 69, 1001, 15], [1238, 70, 1001, 15, "default"], [1238, 77, 1001, 19], [1239, 16, 1002, 16, "style"], [1239, 21, 1002, 21], [1239, 23, 1002, 23], [1239, 24, 1003, 18, "styles"], [1239, 30, 1003, 24], [1239, 31, 1003, 25, "progressFill"], [1239, 43, 1003, 37], [1239, 45, 1004, 18], [1240, 18, 1004, 20, "width"], [1240, 23, 1004, 25], [1240, 25, 1004, 27], [1240, 28, 1004, 30, "processingProgress"], [1240, 46, 1004, 48], [1241, 16, 1004, 52], [1241, 17, 1004, 53], [1242, 14, 1005, 18], [1243, 16, 1005, 18, "fileName"], [1243, 24, 1005, 18], [1243, 26, 1005, 18, "_jsxFileName"], [1243, 38, 1005, 18], [1244, 16, 1005, 18, "lineNumber"], [1244, 26, 1005, 18], [1245, 16, 1005, 18, "columnNumber"], [1245, 28, 1005, 18], [1246, 14, 1005, 18], [1246, 21, 1006, 15], [1247, 12, 1006, 16], [1248, 14, 1006, 16, "fileName"], [1248, 22, 1006, 16], [1248, 24, 1006, 16, "_jsxFileName"], [1248, 36, 1006, 16], [1249, 14, 1006, 16, "lineNumber"], [1249, 24, 1006, 16], [1250, 14, 1006, 16, "columnNumber"], [1250, 26, 1006, 16], [1251, 12, 1006, 16], [1251, 19, 1007, 18], [1251, 20, 1007, 19], [1251, 35, 1008, 12], [1251, 39, 1008, 12, "_jsxDevRuntime"], [1251, 53, 1008, 12], [1251, 54, 1008, 12, "jsxDEV"], [1251, 60, 1008, 12], [1251, 62, 1008, 13, "_Text"], [1251, 67, 1008, 13], [1251, 68, 1008, 13, "default"], [1251, 75, 1008, 17], [1252, 14, 1008, 18, "style"], [1252, 19, 1008, 23], [1252, 21, 1008, 25, "styles"], [1252, 27, 1008, 31], [1252, 28, 1008, 32, "processingDescription"], [1252, 49, 1008, 54], [1253, 14, 1008, 54, "children"], [1253, 22, 1008, 54], [1253, 25, 1009, 15, "processingState"], [1253, 40, 1009, 30], [1253, 45, 1009, 35], [1253, 56, 1009, 46], [1253, 60, 1009, 50], [1253, 89, 1009, 79], [1253, 91, 1010, 15, "processingState"], [1253, 106, 1010, 30], [1253, 111, 1010, 35], [1253, 122, 1010, 46], [1253, 126, 1010, 50], [1253, 164, 1010, 88], [1253, 166, 1011, 15, "processingState"], [1253, 181, 1011, 30], [1253, 186, 1011, 35], [1253, 198, 1011, 47], [1253, 202, 1011, 51], [1253, 247, 1011, 96], [1253, 249, 1012, 15, "processingState"], [1253, 264, 1012, 30], [1253, 269, 1012, 35], [1253, 280, 1012, 46], [1253, 284, 1012, 50], [1253, 325, 1012, 91], [1254, 12, 1012, 91], [1255, 14, 1012, 91, "fileName"], [1255, 22, 1012, 91], [1255, 24, 1012, 91, "_jsxFileName"], [1255, 36, 1012, 91], [1256, 14, 1012, 91, "lineNumber"], [1256, 24, 1012, 91], [1257, 14, 1012, 91, "columnNumber"], [1257, 26, 1012, 91], [1258, 12, 1012, 91], [1258, 19, 1013, 18], [1258, 20, 1013, 19], [1258, 22, 1014, 13, "processingState"], [1258, 37, 1014, 28], [1258, 42, 1014, 33], [1258, 53, 1014, 44], [1258, 70, 1015, 14], [1258, 74, 1015, 14, "_jsxDevRuntime"], [1258, 88, 1015, 14], [1258, 89, 1015, 14, "jsxDEV"], [1258, 95, 1015, 14], [1258, 97, 1015, 15, "_lucideReactNative"], [1258, 115, 1015, 15], [1258, 116, 1015, 15, "CheckCircle"], [1258, 127, 1015, 26], [1259, 14, 1015, 27, "size"], [1259, 18, 1015, 31], [1259, 20, 1015, 33], [1259, 22, 1015, 36], [1260, 14, 1015, 37, "color"], [1260, 19, 1015, 42], [1260, 21, 1015, 43], [1260, 30, 1015, 52], [1261, 14, 1015, 53, "style"], [1261, 19, 1015, 58], [1261, 21, 1015, 60, "styles"], [1261, 27, 1015, 66], [1261, 28, 1015, 67, "successIcon"], [1262, 12, 1015, 79], [1263, 14, 1015, 79, "fileName"], [1263, 22, 1015, 79], [1263, 24, 1015, 79, "_jsxFileName"], [1263, 36, 1015, 79], [1264, 14, 1015, 79, "lineNumber"], [1264, 24, 1015, 79], [1265, 14, 1015, 79, "columnNumber"], [1265, 26, 1015, 79], [1266, 12, 1015, 79], [1266, 19, 1015, 81], [1266, 20, 1016, 13], [1267, 10, 1016, 13], [1268, 12, 1016, 13, "fileName"], [1268, 20, 1016, 13], [1268, 22, 1016, 13, "_jsxFileName"], [1268, 34, 1016, 13], [1269, 12, 1016, 13, "lineNumber"], [1269, 22, 1016, 13], [1270, 12, 1016, 13, "columnNumber"], [1270, 24, 1016, 13], [1271, 10, 1016, 13], [1271, 17, 1017, 16], [1272, 8, 1017, 17], [1273, 10, 1017, 17, "fileName"], [1273, 18, 1017, 17], [1273, 20, 1017, 17, "_jsxFileName"], [1273, 32, 1017, 17], [1274, 10, 1017, 17, "lineNumber"], [1274, 20, 1017, 17], [1275, 10, 1017, 17, "columnNumber"], [1275, 22, 1017, 17], [1276, 8, 1017, 17], [1276, 15, 1018, 14], [1277, 6, 1018, 15], [1278, 8, 1018, 15, "fileName"], [1278, 16, 1018, 15], [1278, 18, 1018, 15, "_jsxFileName"], [1278, 30, 1018, 15], [1279, 8, 1018, 15, "lineNumber"], [1279, 18, 1018, 15], [1280, 8, 1018, 15, "columnNumber"], [1280, 20, 1018, 15], [1281, 6, 1018, 15], [1281, 13, 1019, 13], [1281, 14, 1019, 14], [1281, 29, 1021, 6], [1281, 33, 1021, 6, "_jsxDevRuntime"], [1281, 47, 1021, 6], [1281, 48, 1021, 6, "jsxDEV"], [1281, 54, 1021, 6], [1281, 56, 1021, 7, "_Modal"], [1281, 62, 1021, 7], [1281, 63, 1021, 7, "default"], [1281, 70, 1021, 12], [1282, 8, 1022, 8, "visible"], [1282, 15, 1022, 15], [1282, 17, 1022, 17, "processingState"], [1282, 32, 1022, 32], [1282, 37, 1022, 37], [1282, 44, 1022, 45], [1283, 8, 1023, 8, "transparent"], [1283, 19, 1023, 19], [1284, 8, 1024, 8, "animationType"], [1284, 21, 1024, 21], [1284, 23, 1024, 22], [1284, 29, 1024, 28], [1285, 8, 1024, 28, "children"], [1285, 16, 1024, 28], [1285, 31, 1026, 8], [1285, 35, 1026, 8, "_jsxDevRuntime"], [1285, 49, 1026, 8], [1285, 50, 1026, 8, "jsxDEV"], [1285, 56, 1026, 8], [1285, 58, 1026, 9, "_View"], [1285, 63, 1026, 9], [1285, 64, 1026, 9, "default"], [1285, 71, 1026, 13], [1286, 10, 1026, 14, "style"], [1286, 15, 1026, 19], [1286, 17, 1026, 21, "styles"], [1286, 23, 1026, 27], [1286, 24, 1026, 28, "processingModal"], [1286, 39, 1026, 44], [1287, 10, 1026, 44, "children"], [1287, 18, 1026, 44], [1287, 33, 1027, 10], [1287, 37, 1027, 10, "_jsxDevRuntime"], [1287, 51, 1027, 10], [1287, 52, 1027, 10, "jsxDEV"], [1287, 58, 1027, 10], [1287, 60, 1027, 11, "_View"], [1287, 65, 1027, 11], [1287, 66, 1027, 11, "default"], [1287, 73, 1027, 15], [1288, 12, 1027, 16, "style"], [1288, 17, 1027, 21], [1288, 19, 1027, 23, "styles"], [1288, 25, 1027, 29], [1288, 26, 1027, 30, "errorContent"], [1288, 38, 1027, 43], [1289, 12, 1027, 43, "children"], [1289, 20, 1027, 43], [1289, 36, 1028, 12], [1289, 40, 1028, 12, "_jsxDevRuntime"], [1289, 54, 1028, 12], [1289, 55, 1028, 12, "jsxDEV"], [1289, 61, 1028, 12], [1289, 63, 1028, 13, "_lucideReactNative"], [1289, 81, 1028, 13], [1289, 82, 1028, 13, "X"], [1289, 83, 1028, 14], [1290, 14, 1028, 15, "size"], [1290, 18, 1028, 19], [1290, 20, 1028, 21], [1290, 22, 1028, 24], [1291, 14, 1028, 25, "color"], [1291, 19, 1028, 30], [1291, 21, 1028, 31], [1292, 12, 1028, 40], [1293, 14, 1028, 40, "fileName"], [1293, 22, 1028, 40], [1293, 24, 1028, 40, "_jsxFileName"], [1293, 36, 1028, 40], [1294, 14, 1028, 40, "lineNumber"], [1294, 24, 1028, 40], [1295, 14, 1028, 40, "columnNumber"], [1295, 26, 1028, 40], [1296, 12, 1028, 40], [1296, 19, 1028, 42], [1296, 20, 1028, 43], [1296, 35, 1029, 12], [1296, 39, 1029, 12, "_jsxDevRuntime"], [1296, 53, 1029, 12], [1296, 54, 1029, 12, "jsxDEV"], [1296, 60, 1029, 12], [1296, 62, 1029, 13, "_Text"], [1296, 67, 1029, 13], [1296, 68, 1029, 13, "default"], [1296, 75, 1029, 17], [1297, 14, 1029, 18, "style"], [1297, 19, 1029, 23], [1297, 21, 1029, 25, "styles"], [1297, 27, 1029, 31], [1297, 28, 1029, 32, "errorTitle"], [1297, 38, 1029, 43], [1298, 14, 1029, 43, "children"], [1298, 22, 1029, 43], [1298, 24, 1029, 44], [1299, 12, 1029, 61], [1300, 14, 1029, 61, "fileName"], [1300, 22, 1029, 61], [1300, 24, 1029, 61, "_jsxFileName"], [1300, 36, 1029, 61], [1301, 14, 1029, 61, "lineNumber"], [1301, 24, 1029, 61], [1302, 14, 1029, 61, "columnNumber"], [1302, 26, 1029, 61], [1303, 12, 1029, 61], [1303, 19, 1029, 67], [1303, 20, 1029, 68], [1303, 35, 1030, 12], [1303, 39, 1030, 12, "_jsxDevRuntime"], [1303, 53, 1030, 12], [1303, 54, 1030, 12, "jsxDEV"], [1303, 60, 1030, 12], [1303, 62, 1030, 13, "_Text"], [1303, 67, 1030, 13], [1303, 68, 1030, 13, "default"], [1303, 75, 1030, 17], [1304, 14, 1030, 18, "style"], [1304, 19, 1030, 23], [1304, 21, 1030, 25, "styles"], [1304, 27, 1030, 31], [1304, 28, 1030, 32, "errorMessage"], [1304, 40, 1030, 45], [1305, 14, 1030, 45, "children"], [1305, 22, 1030, 45], [1305, 24, 1030, 47, "errorMessage"], [1306, 12, 1030, 59], [1307, 14, 1030, 59, "fileName"], [1307, 22, 1030, 59], [1307, 24, 1030, 59, "_jsxFileName"], [1307, 36, 1030, 59], [1308, 14, 1030, 59, "lineNumber"], [1308, 24, 1030, 59], [1309, 14, 1030, 59, "columnNumber"], [1309, 26, 1030, 59], [1310, 12, 1030, 59], [1310, 19, 1030, 66], [1310, 20, 1030, 67], [1310, 35, 1031, 12], [1310, 39, 1031, 12, "_jsxDevRuntime"], [1310, 53, 1031, 12], [1310, 54, 1031, 12, "jsxDEV"], [1310, 60, 1031, 12], [1310, 62, 1031, 13, "_TouchableOpacity"], [1310, 79, 1031, 13], [1310, 80, 1031, 13, "default"], [1310, 87, 1031, 29], [1311, 14, 1032, 14, "onPress"], [1311, 21, 1032, 21], [1311, 23, 1032, 23, "retryCapture"], [1311, 35, 1032, 36], [1312, 14, 1033, 14, "style"], [1312, 19, 1033, 19], [1312, 21, 1033, 21, "styles"], [1312, 27, 1033, 27], [1312, 28, 1033, 28, "primaryButton"], [1312, 41, 1033, 42], [1313, 14, 1033, 42, "children"], [1313, 22, 1033, 42], [1313, 37, 1035, 14], [1313, 41, 1035, 14, "_jsxDevRuntime"], [1313, 55, 1035, 14], [1313, 56, 1035, 14, "jsxDEV"], [1313, 62, 1035, 14], [1313, 64, 1035, 15, "_Text"], [1313, 69, 1035, 15], [1313, 70, 1035, 15, "default"], [1313, 77, 1035, 19], [1314, 16, 1035, 20, "style"], [1314, 21, 1035, 25], [1314, 23, 1035, 27, "styles"], [1314, 29, 1035, 33], [1314, 30, 1035, 34, "primaryButtonText"], [1314, 47, 1035, 52], [1315, 16, 1035, 52, "children"], [1315, 24, 1035, 52], [1315, 26, 1035, 53], [1316, 14, 1035, 62], [1317, 16, 1035, 62, "fileName"], [1317, 24, 1035, 62], [1317, 26, 1035, 62, "_jsxFileName"], [1317, 38, 1035, 62], [1318, 16, 1035, 62, "lineNumber"], [1318, 26, 1035, 62], [1319, 16, 1035, 62, "columnNumber"], [1319, 28, 1035, 62], [1320, 14, 1035, 62], [1320, 21, 1035, 68], [1321, 12, 1035, 69], [1322, 14, 1035, 69, "fileName"], [1322, 22, 1035, 69], [1322, 24, 1035, 69, "_jsxFileName"], [1322, 36, 1035, 69], [1323, 14, 1035, 69, "lineNumber"], [1323, 24, 1035, 69], [1324, 14, 1035, 69, "columnNumber"], [1324, 26, 1035, 69], [1325, 12, 1035, 69], [1325, 19, 1036, 30], [1325, 20, 1036, 31], [1325, 35, 1037, 12], [1325, 39, 1037, 12, "_jsxDevRuntime"], [1325, 53, 1037, 12], [1325, 54, 1037, 12, "jsxDEV"], [1325, 60, 1037, 12], [1325, 62, 1037, 13, "_TouchableOpacity"], [1325, 79, 1037, 13], [1325, 80, 1037, 13, "default"], [1325, 87, 1037, 29], [1326, 14, 1038, 14, "onPress"], [1326, 21, 1038, 21], [1326, 23, 1038, 23, "onCancel"], [1326, 31, 1038, 32], [1327, 14, 1039, 14, "style"], [1327, 19, 1039, 19], [1327, 21, 1039, 21, "styles"], [1327, 27, 1039, 27], [1327, 28, 1039, 28, "secondaryButton"], [1327, 43, 1039, 44], [1328, 14, 1039, 44, "children"], [1328, 22, 1039, 44], [1328, 37, 1041, 14], [1328, 41, 1041, 14, "_jsxDevRuntime"], [1328, 55, 1041, 14], [1328, 56, 1041, 14, "jsxDEV"], [1328, 62, 1041, 14], [1328, 64, 1041, 15, "_Text"], [1328, 69, 1041, 15], [1328, 70, 1041, 15, "default"], [1328, 77, 1041, 19], [1329, 16, 1041, 20, "style"], [1329, 21, 1041, 25], [1329, 23, 1041, 27, "styles"], [1329, 29, 1041, 33], [1329, 30, 1041, 34, "secondaryButtonText"], [1329, 49, 1041, 54], [1330, 16, 1041, 54, "children"], [1330, 24, 1041, 54], [1330, 26, 1041, 55], [1331, 14, 1041, 61], [1332, 16, 1041, 61, "fileName"], [1332, 24, 1041, 61], [1332, 26, 1041, 61, "_jsxFileName"], [1332, 38, 1041, 61], [1333, 16, 1041, 61, "lineNumber"], [1333, 26, 1041, 61], [1334, 16, 1041, 61, "columnNumber"], [1334, 28, 1041, 61], [1335, 14, 1041, 61], [1335, 21, 1041, 67], [1336, 12, 1041, 68], [1337, 14, 1041, 68, "fileName"], [1337, 22, 1041, 68], [1337, 24, 1041, 68, "_jsxFileName"], [1337, 36, 1041, 68], [1338, 14, 1041, 68, "lineNumber"], [1338, 24, 1041, 68], [1339, 14, 1041, 68, "columnNumber"], [1339, 26, 1041, 68], [1340, 12, 1041, 68], [1340, 19, 1042, 30], [1340, 20, 1042, 31], [1341, 10, 1042, 31], [1342, 12, 1042, 31, "fileName"], [1342, 20, 1042, 31], [1342, 22, 1042, 31, "_jsxFileName"], [1342, 34, 1042, 31], [1343, 12, 1042, 31, "lineNumber"], [1343, 22, 1042, 31], [1344, 12, 1042, 31, "columnNumber"], [1344, 24, 1042, 31], [1345, 10, 1042, 31], [1345, 17, 1043, 16], [1346, 8, 1043, 17], [1347, 10, 1043, 17, "fileName"], [1347, 18, 1043, 17], [1347, 20, 1043, 17, "_jsxFileName"], [1347, 32, 1043, 17], [1348, 10, 1043, 17, "lineNumber"], [1348, 20, 1043, 17], [1349, 10, 1043, 17, "columnNumber"], [1349, 22, 1043, 17], [1350, 8, 1043, 17], [1350, 15, 1044, 14], [1351, 6, 1044, 15], [1352, 8, 1044, 15, "fileName"], [1352, 16, 1044, 15], [1352, 18, 1044, 15, "_jsxFileName"], [1352, 30, 1044, 15], [1353, 8, 1044, 15, "lineNumber"], [1353, 18, 1044, 15], [1354, 8, 1044, 15, "columnNumber"], [1354, 20, 1044, 15], [1355, 6, 1044, 15], [1355, 13, 1045, 13], [1355, 14, 1045, 14], [1356, 4, 1045, 14], [1357, 6, 1045, 14, "fileName"], [1357, 14, 1045, 14], [1357, 16, 1045, 14, "_jsxFileName"], [1357, 28, 1045, 14], [1358, 6, 1045, 14, "lineNumber"], [1358, 16, 1045, 14], [1359, 6, 1045, 14, "columnNumber"], [1359, 18, 1045, 14], [1360, 4, 1045, 14], [1360, 11, 1046, 10], [1360, 12, 1046, 11], [1361, 2, 1048, 0], [1362, 2, 1048, 1, "_s"], [1362, 4, 1048, 1], [1362, 5, 51, 24, "EchoCameraWeb"], [1362, 18, 51, 37], [1363, 4, 51, 37], [1363, 12, 58, 42, "useCameraPermissions"], [1363, 44, 58, 62], [1363, 46, 72, 19, "useUpload"], [1363, 64, 72, 28], [1364, 2, 72, 28], [1365, 2, 72, 28, "_c"], [1365, 4, 72, 28], [1365, 7, 51, 24, "EchoCameraWeb"], [1365, 20, 51, 37], [1366, 2, 1049, 0], [1366, 8, 1049, 6, "styles"], [1366, 14, 1049, 12], [1366, 17, 1049, 15, "StyleSheet"], [1366, 36, 1049, 25], [1366, 37, 1049, 26, "create"], [1366, 43, 1049, 32], [1366, 44, 1049, 33], [1367, 4, 1050, 2, "container"], [1367, 13, 1050, 11], [1367, 15, 1050, 13], [1368, 6, 1051, 4, "flex"], [1368, 10, 1051, 8], [1368, 12, 1051, 10], [1368, 13, 1051, 11], [1369, 6, 1052, 4, "backgroundColor"], [1369, 21, 1052, 19], [1369, 23, 1052, 21], [1370, 4, 1053, 2], [1370, 5, 1053, 3], [1371, 4, 1054, 2, "cameraContainer"], [1371, 19, 1054, 17], [1371, 21, 1054, 19], [1372, 6, 1055, 4, "flex"], [1372, 10, 1055, 8], [1372, 12, 1055, 10], [1372, 13, 1055, 11], [1373, 6, 1056, 4, "max<PERSON><PERSON><PERSON>"], [1373, 14, 1056, 12], [1373, 16, 1056, 14], [1373, 19, 1056, 17], [1374, 6, 1057, 4, "alignSelf"], [1374, 15, 1057, 13], [1374, 17, 1057, 15], [1374, 25, 1057, 23], [1375, 6, 1058, 4, "width"], [1375, 11, 1058, 9], [1375, 13, 1058, 11], [1376, 4, 1059, 2], [1376, 5, 1059, 3], [1377, 4, 1060, 2, "camera"], [1377, 10, 1060, 8], [1377, 12, 1060, 10], [1378, 6, 1061, 4, "flex"], [1378, 10, 1061, 8], [1378, 12, 1061, 10], [1379, 4, 1062, 2], [1379, 5, 1062, 3], [1380, 4, 1063, 2, "headerOverlay"], [1380, 17, 1063, 15], [1380, 19, 1063, 17], [1381, 6, 1064, 4, "position"], [1381, 14, 1064, 12], [1381, 16, 1064, 14], [1381, 26, 1064, 24], [1382, 6, 1065, 4, "top"], [1382, 9, 1065, 7], [1382, 11, 1065, 9], [1382, 12, 1065, 10], [1383, 6, 1066, 4, "left"], [1383, 10, 1066, 8], [1383, 12, 1066, 10], [1383, 13, 1066, 11], [1384, 6, 1067, 4, "right"], [1384, 11, 1067, 9], [1384, 13, 1067, 11], [1384, 14, 1067, 12], [1385, 6, 1068, 4, "backgroundColor"], [1385, 21, 1068, 19], [1385, 23, 1068, 21], [1385, 36, 1068, 34], [1386, 6, 1069, 4, "paddingTop"], [1386, 16, 1069, 14], [1386, 18, 1069, 16], [1386, 20, 1069, 18], [1387, 6, 1070, 4, "paddingHorizontal"], [1387, 23, 1070, 21], [1387, 25, 1070, 23], [1387, 27, 1070, 25], [1388, 6, 1071, 4, "paddingBottom"], [1388, 19, 1071, 17], [1388, 21, 1071, 19], [1389, 4, 1072, 2], [1389, 5, 1072, 3], [1390, 4, 1073, 2, "headerContent"], [1390, 17, 1073, 15], [1390, 19, 1073, 17], [1391, 6, 1074, 4, "flexDirection"], [1391, 19, 1074, 17], [1391, 21, 1074, 19], [1391, 26, 1074, 24], [1392, 6, 1075, 4, "justifyContent"], [1392, 20, 1075, 18], [1392, 22, 1075, 20], [1392, 37, 1075, 35], [1393, 6, 1076, 4, "alignItems"], [1393, 16, 1076, 14], [1393, 18, 1076, 16], [1394, 4, 1077, 2], [1394, 5, 1077, 3], [1395, 4, 1078, 2, "headerLeft"], [1395, 14, 1078, 12], [1395, 16, 1078, 14], [1396, 6, 1079, 4, "flex"], [1396, 10, 1079, 8], [1396, 12, 1079, 10], [1397, 4, 1080, 2], [1397, 5, 1080, 3], [1398, 4, 1081, 2, "headerTitle"], [1398, 15, 1081, 13], [1398, 17, 1081, 15], [1399, 6, 1082, 4, "fontSize"], [1399, 14, 1082, 12], [1399, 16, 1082, 14], [1399, 18, 1082, 16], [1400, 6, 1083, 4, "fontWeight"], [1400, 16, 1083, 14], [1400, 18, 1083, 16], [1400, 23, 1083, 21], [1401, 6, 1084, 4, "color"], [1401, 11, 1084, 9], [1401, 13, 1084, 11], [1401, 19, 1084, 17], [1402, 6, 1085, 4, "marginBottom"], [1402, 18, 1085, 16], [1402, 20, 1085, 18], [1403, 4, 1086, 2], [1403, 5, 1086, 3], [1404, 4, 1087, 2, "subtitleRow"], [1404, 15, 1087, 13], [1404, 17, 1087, 15], [1405, 6, 1088, 4, "flexDirection"], [1405, 19, 1088, 17], [1405, 21, 1088, 19], [1405, 26, 1088, 24], [1406, 6, 1089, 4, "alignItems"], [1406, 16, 1089, 14], [1406, 18, 1089, 16], [1406, 26, 1089, 24], [1407, 6, 1090, 4, "marginBottom"], [1407, 18, 1090, 16], [1407, 20, 1090, 18], [1408, 4, 1091, 2], [1408, 5, 1091, 3], [1409, 4, 1092, 2, "webIcon"], [1409, 11, 1092, 9], [1409, 13, 1092, 11], [1410, 6, 1093, 4, "fontSize"], [1410, 14, 1093, 12], [1410, 16, 1093, 14], [1410, 18, 1093, 16], [1411, 6, 1094, 4, "marginRight"], [1411, 17, 1094, 15], [1411, 19, 1094, 17], [1412, 4, 1095, 2], [1412, 5, 1095, 3], [1413, 4, 1096, 2, "headerSubtitle"], [1413, 18, 1096, 16], [1413, 20, 1096, 18], [1414, 6, 1097, 4, "fontSize"], [1414, 14, 1097, 12], [1414, 16, 1097, 14], [1414, 18, 1097, 16], [1415, 6, 1098, 4, "color"], [1415, 11, 1098, 9], [1415, 13, 1098, 11], [1415, 19, 1098, 17], [1416, 6, 1099, 4, "opacity"], [1416, 13, 1099, 11], [1416, 15, 1099, 13], [1417, 4, 1100, 2], [1417, 5, 1100, 3], [1418, 4, 1101, 2, "challengeRow"], [1418, 16, 1101, 14], [1418, 18, 1101, 16], [1419, 6, 1102, 4, "flexDirection"], [1419, 19, 1102, 17], [1419, 21, 1102, 19], [1419, 26, 1102, 24], [1420, 6, 1103, 4, "alignItems"], [1420, 16, 1103, 14], [1420, 18, 1103, 16], [1421, 4, 1104, 2], [1421, 5, 1104, 3], [1422, 4, 1105, 2, "challengeCode"], [1422, 17, 1105, 15], [1422, 19, 1105, 17], [1423, 6, 1106, 4, "fontSize"], [1423, 14, 1106, 12], [1423, 16, 1106, 14], [1423, 18, 1106, 16], [1424, 6, 1107, 4, "color"], [1424, 11, 1107, 9], [1424, 13, 1107, 11], [1424, 19, 1107, 17], [1425, 6, 1108, 4, "marginLeft"], [1425, 16, 1108, 14], [1425, 18, 1108, 16], [1425, 19, 1108, 17], [1426, 6, 1109, 4, "fontFamily"], [1426, 16, 1109, 14], [1426, 18, 1109, 16], [1427, 4, 1110, 2], [1427, 5, 1110, 3], [1428, 4, 1111, 2, "closeButton"], [1428, 15, 1111, 13], [1428, 17, 1111, 15], [1429, 6, 1112, 4, "padding"], [1429, 13, 1112, 11], [1429, 15, 1112, 13], [1430, 4, 1113, 2], [1430, 5, 1113, 3], [1431, 4, 1114, 2, "privacyNotice"], [1431, 17, 1114, 15], [1431, 19, 1114, 17], [1432, 6, 1115, 4, "position"], [1432, 14, 1115, 12], [1432, 16, 1115, 14], [1432, 26, 1115, 24], [1433, 6, 1116, 4, "top"], [1433, 9, 1116, 7], [1433, 11, 1116, 9], [1433, 14, 1116, 12], [1434, 6, 1117, 4, "left"], [1434, 10, 1117, 8], [1434, 12, 1117, 10], [1434, 14, 1117, 12], [1435, 6, 1118, 4, "right"], [1435, 11, 1118, 9], [1435, 13, 1118, 11], [1435, 15, 1118, 13], [1436, 6, 1119, 4, "backgroundColor"], [1436, 21, 1119, 19], [1436, 23, 1119, 21], [1436, 48, 1119, 46], [1437, 6, 1120, 4, "borderRadius"], [1437, 18, 1120, 16], [1437, 20, 1120, 18], [1437, 21, 1120, 19], [1438, 6, 1121, 4, "padding"], [1438, 13, 1121, 11], [1438, 15, 1121, 13], [1438, 17, 1121, 15], [1439, 6, 1122, 4, "flexDirection"], [1439, 19, 1122, 17], [1439, 21, 1122, 19], [1439, 26, 1122, 24], [1440, 6, 1123, 4, "alignItems"], [1440, 16, 1123, 14], [1440, 18, 1123, 16], [1441, 4, 1124, 2], [1441, 5, 1124, 3], [1442, 4, 1125, 2, "privacyText"], [1442, 15, 1125, 13], [1442, 17, 1125, 15], [1443, 6, 1126, 4, "color"], [1443, 11, 1126, 9], [1443, 13, 1126, 11], [1443, 19, 1126, 17], [1444, 6, 1127, 4, "fontSize"], [1444, 14, 1127, 12], [1444, 16, 1127, 14], [1444, 18, 1127, 16], [1445, 6, 1128, 4, "marginLeft"], [1445, 16, 1128, 14], [1445, 18, 1128, 16], [1445, 19, 1128, 17], [1446, 6, 1129, 4, "flex"], [1446, 10, 1129, 8], [1446, 12, 1129, 10], [1447, 4, 1130, 2], [1447, 5, 1130, 3], [1448, 4, 1131, 2, "footer<PERSON><PERSON><PERSON>"], [1448, 17, 1131, 15], [1448, 19, 1131, 17], [1449, 6, 1132, 4, "position"], [1449, 14, 1132, 12], [1449, 16, 1132, 14], [1449, 26, 1132, 24], [1450, 6, 1133, 4, "bottom"], [1450, 12, 1133, 10], [1450, 14, 1133, 12], [1450, 15, 1133, 13], [1451, 6, 1134, 4, "left"], [1451, 10, 1134, 8], [1451, 12, 1134, 10], [1451, 13, 1134, 11], [1452, 6, 1135, 4, "right"], [1452, 11, 1135, 9], [1452, 13, 1135, 11], [1452, 14, 1135, 12], [1453, 6, 1136, 4, "backgroundColor"], [1453, 21, 1136, 19], [1453, 23, 1136, 21], [1453, 36, 1136, 34], [1454, 6, 1137, 4, "paddingBottom"], [1454, 19, 1137, 17], [1454, 21, 1137, 19], [1454, 23, 1137, 21], [1455, 6, 1138, 4, "paddingTop"], [1455, 16, 1138, 14], [1455, 18, 1138, 16], [1455, 20, 1138, 18], [1456, 6, 1139, 4, "alignItems"], [1456, 16, 1139, 14], [1456, 18, 1139, 16], [1457, 4, 1140, 2], [1457, 5, 1140, 3], [1458, 4, 1141, 2, "instruction"], [1458, 15, 1141, 13], [1458, 17, 1141, 15], [1459, 6, 1142, 4, "fontSize"], [1459, 14, 1142, 12], [1459, 16, 1142, 14], [1459, 18, 1142, 16], [1460, 6, 1143, 4, "color"], [1460, 11, 1143, 9], [1460, 13, 1143, 11], [1460, 19, 1143, 17], [1461, 6, 1144, 4, "marginBottom"], [1461, 18, 1144, 16], [1461, 20, 1144, 18], [1462, 4, 1145, 2], [1462, 5, 1145, 3], [1463, 4, 1146, 2, "shutterButton"], [1463, 17, 1146, 15], [1463, 19, 1146, 17], [1464, 6, 1147, 4, "width"], [1464, 11, 1147, 9], [1464, 13, 1147, 11], [1464, 15, 1147, 13], [1465, 6, 1148, 4, "height"], [1465, 12, 1148, 10], [1465, 14, 1148, 12], [1465, 16, 1148, 14], [1466, 6, 1149, 4, "borderRadius"], [1466, 18, 1149, 16], [1466, 20, 1149, 18], [1466, 22, 1149, 20], [1467, 6, 1150, 4, "backgroundColor"], [1467, 21, 1150, 19], [1467, 23, 1150, 21], [1467, 29, 1150, 27], [1468, 6, 1151, 4, "justifyContent"], [1468, 20, 1151, 18], [1468, 22, 1151, 20], [1468, 30, 1151, 28], [1469, 6, 1152, 4, "alignItems"], [1469, 16, 1152, 14], [1469, 18, 1152, 16], [1469, 26, 1152, 24], [1470, 6, 1153, 4, "marginBottom"], [1470, 18, 1153, 16], [1470, 20, 1153, 18], [1470, 22, 1153, 20], [1471, 6, 1154, 4], [1471, 9, 1154, 7, "Platform"], [1471, 26, 1154, 15], [1471, 27, 1154, 16, "select"], [1471, 33, 1154, 22], [1471, 34, 1154, 23], [1472, 8, 1155, 6, "ios"], [1472, 11, 1155, 9], [1472, 13, 1155, 11], [1473, 10, 1156, 8, "shadowColor"], [1473, 21, 1156, 19], [1473, 23, 1156, 21], [1473, 32, 1156, 30], [1474, 10, 1157, 8, "shadowOffset"], [1474, 22, 1157, 20], [1474, 24, 1157, 22], [1475, 12, 1157, 24, "width"], [1475, 17, 1157, 29], [1475, 19, 1157, 31], [1475, 20, 1157, 32], [1476, 12, 1157, 34, "height"], [1476, 18, 1157, 40], [1476, 20, 1157, 42], [1477, 10, 1157, 44], [1477, 11, 1157, 45], [1478, 10, 1158, 8, "shadowOpacity"], [1478, 23, 1158, 21], [1478, 25, 1158, 23], [1478, 28, 1158, 26], [1479, 10, 1159, 8, "shadowRadius"], [1479, 22, 1159, 20], [1479, 24, 1159, 22], [1480, 8, 1160, 6], [1480, 9, 1160, 7], [1481, 8, 1161, 6, "android"], [1481, 15, 1161, 13], [1481, 17, 1161, 15], [1482, 10, 1162, 8, "elevation"], [1482, 19, 1162, 17], [1482, 21, 1162, 19], [1483, 8, 1163, 6], [1483, 9, 1163, 7], [1484, 8, 1164, 6, "web"], [1484, 11, 1164, 9], [1484, 13, 1164, 11], [1485, 10, 1165, 8, "boxShadow"], [1485, 19, 1165, 17], [1485, 21, 1165, 19], [1486, 8, 1166, 6], [1487, 6, 1167, 4], [1487, 7, 1167, 5], [1488, 4, 1168, 2], [1488, 5, 1168, 3], [1489, 4, 1169, 2, "shutterButtonDisabled"], [1489, 25, 1169, 23], [1489, 27, 1169, 25], [1490, 6, 1170, 4, "opacity"], [1490, 13, 1170, 11], [1490, 15, 1170, 13], [1491, 4, 1171, 2], [1491, 5, 1171, 3], [1492, 4, 1172, 2, "shutterInner"], [1492, 16, 1172, 14], [1492, 18, 1172, 16], [1493, 6, 1173, 4, "width"], [1493, 11, 1173, 9], [1493, 13, 1173, 11], [1493, 15, 1173, 13], [1494, 6, 1174, 4, "height"], [1494, 12, 1174, 10], [1494, 14, 1174, 12], [1494, 16, 1174, 14], [1495, 6, 1175, 4, "borderRadius"], [1495, 18, 1175, 16], [1495, 20, 1175, 18], [1495, 22, 1175, 20], [1496, 6, 1176, 4, "backgroundColor"], [1496, 21, 1176, 19], [1496, 23, 1176, 21], [1496, 29, 1176, 27], [1497, 6, 1177, 4, "borderWidth"], [1497, 17, 1177, 15], [1497, 19, 1177, 17], [1497, 20, 1177, 18], [1498, 6, 1178, 4, "borderColor"], [1498, 17, 1178, 15], [1498, 19, 1178, 17], [1499, 4, 1179, 2], [1499, 5, 1179, 3], [1500, 4, 1180, 2, "privacyNote"], [1500, 15, 1180, 13], [1500, 17, 1180, 15], [1501, 6, 1181, 4, "fontSize"], [1501, 14, 1181, 12], [1501, 16, 1181, 14], [1501, 18, 1181, 16], [1502, 6, 1182, 4, "color"], [1502, 11, 1182, 9], [1502, 13, 1182, 11], [1503, 4, 1183, 2], [1503, 5, 1183, 3], [1504, 4, 1184, 2, "processingModal"], [1504, 19, 1184, 17], [1504, 21, 1184, 19], [1505, 6, 1185, 4, "flex"], [1505, 10, 1185, 8], [1505, 12, 1185, 10], [1505, 13, 1185, 11], [1506, 6, 1186, 4, "backgroundColor"], [1506, 21, 1186, 19], [1506, 23, 1186, 21], [1506, 43, 1186, 41], [1507, 6, 1187, 4, "justifyContent"], [1507, 20, 1187, 18], [1507, 22, 1187, 20], [1507, 30, 1187, 28], [1508, 6, 1188, 4, "alignItems"], [1508, 16, 1188, 14], [1508, 18, 1188, 16], [1509, 4, 1189, 2], [1509, 5, 1189, 3], [1510, 4, 1190, 2, "processingContent"], [1510, 21, 1190, 19], [1510, 23, 1190, 21], [1511, 6, 1191, 4, "backgroundColor"], [1511, 21, 1191, 19], [1511, 23, 1191, 21], [1511, 29, 1191, 27], [1512, 6, 1192, 4, "borderRadius"], [1512, 18, 1192, 16], [1512, 20, 1192, 18], [1512, 22, 1192, 20], [1513, 6, 1193, 4, "padding"], [1513, 13, 1193, 11], [1513, 15, 1193, 13], [1513, 17, 1193, 15], [1514, 6, 1194, 4, "width"], [1514, 11, 1194, 9], [1514, 13, 1194, 11], [1514, 18, 1194, 16], [1515, 6, 1195, 4, "max<PERSON><PERSON><PERSON>"], [1515, 14, 1195, 12], [1515, 16, 1195, 14], [1515, 19, 1195, 17], [1516, 6, 1196, 4, "alignItems"], [1516, 16, 1196, 14], [1516, 18, 1196, 16], [1517, 4, 1197, 2], [1517, 5, 1197, 3], [1518, 4, 1198, 2, "processingTitle"], [1518, 19, 1198, 17], [1518, 21, 1198, 19], [1519, 6, 1199, 4, "fontSize"], [1519, 14, 1199, 12], [1519, 16, 1199, 14], [1519, 18, 1199, 16], [1520, 6, 1200, 4, "fontWeight"], [1520, 16, 1200, 14], [1520, 18, 1200, 16], [1520, 23, 1200, 21], [1521, 6, 1201, 4, "color"], [1521, 11, 1201, 9], [1521, 13, 1201, 11], [1521, 22, 1201, 20], [1522, 6, 1202, 4, "marginTop"], [1522, 15, 1202, 13], [1522, 17, 1202, 15], [1522, 19, 1202, 17], [1523, 6, 1203, 4, "marginBottom"], [1523, 18, 1203, 16], [1523, 20, 1203, 18], [1524, 4, 1204, 2], [1524, 5, 1204, 3], [1525, 4, 1205, 2, "progressBar"], [1525, 15, 1205, 13], [1525, 17, 1205, 15], [1526, 6, 1206, 4, "width"], [1526, 11, 1206, 9], [1526, 13, 1206, 11], [1526, 19, 1206, 17], [1527, 6, 1207, 4, "height"], [1527, 12, 1207, 10], [1527, 14, 1207, 12], [1527, 15, 1207, 13], [1528, 6, 1208, 4, "backgroundColor"], [1528, 21, 1208, 19], [1528, 23, 1208, 21], [1528, 32, 1208, 30], [1529, 6, 1209, 4, "borderRadius"], [1529, 18, 1209, 16], [1529, 20, 1209, 18], [1529, 21, 1209, 19], [1530, 6, 1210, 4, "overflow"], [1530, 14, 1210, 12], [1530, 16, 1210, 14], [1530, 24, 1210, 22], [1531, 6, 1211, 4, "marginBottom"], [1531, 18, 1211, 16], [1531, 20, 1211, 18], [1532, 4, 1212, 2], [1532, 5, 1212, 3], [1533, 4, 1213, 2, "progressFill"], [1533, 16, 1213, 14], [1533, 18, 1213, 16], [1534, 6, 1214, 4, "height"], [1534, 12, 1214, 10], [1534, 14, 1214, 12], [1534, 20, 1214, 18], [1535, 6, 1215, 4, "backgroundColor"], [1535, 21, 1215, 19], [1535, 23, 1215, 21], [1535, 32, 1215, 30], [1536, 6, 1216, 4, "borderRadius"], [1536, 18, 1216, 16], [1536, 20, 1216, 18], [1537, 4, 1217, 2], [1537, 5, 1217, 3], [1538, 4, 1218, 2, "processingDescription"], [1538, 25, 1218, 23], [1538, 27, 1218, 25], [1539, 6, 1219, 4, "fontSize"], [1539, 14, 1219, 12], [1539, 16, 1219, 14], [1539, 18, 1219, 16], [1540, 6, 1220, 4, "color"], [1540, 11, 1220, 9], [1540, 13, 1220, 11], [1540, 22, 1220, 20], [1541, 6, 1221, 4, "textAlign"], [1541, 15, 1221, 13], [1541, 17, 1221, 15], [1542, 4, 1222, 2], [1542, 5, 1222, 3], [1543, 4, 1223, 2, "successIcon"], [1543, 15, 1223, 13], [1543, 17, 1223, 15], [1544, 6, 1224, 4, "marginTop"], [1544, 15, 1224, 13], [1544, 17, 1224, 15], [1545, 4, 1225, 2], [1545, 5, 1225, 3], [1546, 4, 1226, 2, "errorContent"], [1546, 16, 1226, 14], [1546, 18, 1226, 16], [1547, 6, 1227, 4, "backgroundColor"], [1547, 21, 1227, 19], [1547, 23, 1227, 21], [1547, 29, 1227, 27], [1548, 6, 1228, 4, "borderRadius"], [1548, 18, 1228, 16], [1548, 20, 1228, 18], [1548, 22, 1228, 20], [1549, 6, 1229, 4, "padding"], [1549, 13, 1229, 11], [1549, 15, 1229, 13], [1549, 17, 1229, 15], [1550, 6, 1230, 4, "width"], [1550, 11, 1230, 9], [1550, 13, 1230, 11], [1550, 18, 1230, 16], [1551, 6, 1231, 4, "max<PERSON><PERSON><PERSON>"], [1551, 14, 1231, 12], [1551, 16, 1231, 14], [1551, 19, 1231, 17], [1552, 6, 1232, 4, "alignItems"], [1552, 16, 1232, 14], [1552, 18, 1232, 16], [1553, 4, 1233, 2], [1553, 5, 1233, 3], [1554, 4, 1234, 2, "errorTitle"], [1554, 14, 1234, 12], [1554, 16, 1234, 14], [1555, 6, 1235, 4, "fontSize"], [1555, 14, 1235, 12], [1555, 16, 1235, 14], [1555, 18, 1235, 16], [1556, 6, 1236, 4, "fontWeight"], [1556, 16, 1236, 14], [1556, 18, 1236, 16], [1556, 23, 1236, 21], [1557, 6, 1237, 4, "color"], [1557, 11, 1237, 9], [1557, 13, 1237, 11], [1557, 22, 1237, 20], [1558, 6, 1238, 4, "marginTop"], [1558, 15, 1238, 13], [1558, 17, 1238, 15], [1558, 19, 1238, 17], [1559, 6, 1239, 4, "marginBottom"], [1559, 18, 1239, 16], [1559, 20, 1239, 18], [1560, 4, 1240, 2], [1560, 5, 1240, 3], [1561, 4, 1241, 2, "errorMessage"], [1561, 16, 1241, 14], [1561, 18, 1241, 16], [1562, 6, 1242, 4, "fontSize"], [1562, 14, 1242, 12], [1562, 16, 1242, 14], [1562, 18, 1242, 16], [1563, 6, 1243, 4, "color"], [1563, 11, 1243, 9], [1563, 13, 1243, 11], [1563, 22, 1243, 20], [1564, 6, 1244, 4, "textAlign"], [1564, 15, 1244, 13], [1564, 17, 1244, 15], [1564, 25, 1244, 23], [1565, 6, 1245, 4, "marginBottom"], [1565, 18, 1245, 16], [1565, 20, 1245, 18], [1566, 4, 1246, 2], [1566, 5, 1246, 3], [1567, 4, 1247, 2, "primaryButton"], [1567, 17, 1247, 15], [1567, 19, 1247, 17], [1568, 6, 1248, 4, "backgroundColor"], [1568, 21, 1248, 19], [1568, 23, 1248, 21], [1568, 32, 1248, 30], [1569, 6, 1249, 4, "paddingHorizontal"], [1569, 23, 1249, 21], [1569, 25, 1249, 23], [1569, 27, 1249, 25], [1570, 6, 1250, 4, "paddingVertical"], [1570, 21, 1250, 19], [1570, 23, 1250, 21], [1570, 25, 1250, 23], [1571, 6, 1251, 4, "borderRadius"], [1571, 18, 1251, 16], [1571, 20, 1251, 18], [1571, 21, 1251, 19], [1572, 6, 1252, 4, "marginTop"], [1572, 15, 1252, 13], [1572, 17, 1252, 15], [1573, 4, 1253, 2], [1573, 5, 1253, 3], [1574, 4, 1254, 2, "primaryButtonText"], [1574, 21, 1254, 19], [1574, 23, 1254, 21], [1575, 6, 1255, 4, "color"], [1575, 11, 1255, 9], [1575, 13, 1255, 11], [1575, 19, 1255, 17], [1576, 6, 1256, 4, "fontSize"], [1576, 14, 1256, 12], [1576, 16, 1256, 14], [1576, 18, 1256, 16], [1577, 6, 1257, 4, "fontWeight"], [1577, 16, 1257, 14], [1577, 18, 1257, 16], [1578, 4, 1258, 2], [1578, 5, 1258, 3], [1579, 4, 1259, 2, "secondaryButton"], [1579, 19, 1259, 17], [1579, 21, 1259, 19], [1580, 6, 1260, 4, "paddingHorizontal"], [1580, 23, 1260, 21], [1580, 25, 1260, 23], [1580, 27, 1260, 25], [1581, 6, 1261, 4, "paddingVertical"], [1581, 21, 1261, 19], [1581, 23, 1261, 21], [1581, 25, 1261, 23], [1582, 6, 1262, 4, "marginTop"], [1582, 15, 1262, 13], [1582, 17, 1262, 15], [1583, 4, 1263, 2], [1583, 5, 1263, 3], [1584, 4, 1264, 2, "secondaryButtonText"], [1584, 23, 1264, 21], [1584, 25, 1264, 23], [1585, 6, 1265, 4, "color"], [1585, 11, 1265, 9], [1585, 13, 1265, 11], [1585, 22, 1265, 20], [1586, 6, 1266, 4, "fontSize"], [1586, 14, 1266, 12], [1586, 16, 1266, 14], [1587, 4, 1267, 2], [1587, 5, 1267, 3], [1588, 4, 1268, 2, "permissionContent"], [1588, 21, 1268, 19], [1588, 23, 1268, 21], [1589, 6, 1269, 4, "flex"], [1589, 10, 1269, 8], [1589, 12, 1269, 10], [1589, 13, 1269, 11], [1590, 6, 1270, 4, "justifyContent"], [1590, 20, 1270, 18], [1590, 22, 1270, 20], [1590, 30, 1270, 28], [1591, 6, 1271, 4, "alignItems"], [1591, 16, 1271, 14], [1591, 18, 1271, 16], [1591, 26, 1271, 24], [1592, 6, 1272, 4, "padding"], [1592, 13, 1272, 11], [1592, 15, 1272, 13], [1593, 4, 1273, 2], [1593, 5, 1273, 3], [1594, 4, 1274, 2, "permissionTitle"], [1594, 19, 1274, 17], [1594, 21, 1274, 19], [1595, 6, 1275, 4, "fontSize"], [1595, 14, 1275, 12], [1595, 16, 1275, 14], [1595, 18, 1275, 16], [1596, 6, 1276, 4, "fontWeight"], [1596, 16, 1276, 14], [1596, 18, 1276, 16], [1596, 23, 1276, 21], [1597, 6, 1277, 4, "color"], [1597, 11, 1277, 9], [1597, 13, 1277, 11], [1597, 22, 1277, 20], [1598, 6, 1278, 4, "marginTop"], [1598, 15, 1278, 13], [1598, 17, 1278, 15], [1598, 19, 1278, 17], [1599, 6, 1279, 4, "marginBottom"], [1599, 18, 1279, 16], [1599, 20, 1279, 18], [1600, 4, 1280, 2], [1600, 5, 1280, 3], [1601, 4, 1281, 2, "permissionDescription"], [1601, 25, 1281, 23], [1601, 27, 1281, 25], [1602, 6, 1282, 4, "fontSize"], [1602, 14, 1282, 12], [1602, 16, 1282, 14], [1602, 18, 1282, 16], [1603, 6, 1283, 4, "color"], [1603, 11, 1283, 9], [1603, 13, 1283, 11], [1603, 22, 1283, 20], [1604, 6, 1284, 4, "textAlign"], [1604, 15, 1284, 13], [1604, 17, 1284, 15], [1604, 25, 1284, 23], [1605, 6, 1285, 4, "marginBottom"], [1605, 18, 1285, 16], [1605, 20, 1285, 18], [1606, 4, 1286, 2], [1606, 5, 1286, 3], [1607, 4, 1287, 2, "loadingText"], [1607, 15, 1287, 13], [1607, 17, 1287, 15], [1608, 6, 1288, 4, "color"], [1608, 11, 1288, 9], [1608, 13, 1288, 11], [1608, 22, 1288, 20], [1609, 6, 1289, 4, "marginTop"], [1609, 15, 1289, 13], [1609, 17, 1289, 15], [1610, 4, 1290, 2], [1610, 5, 1290, 3], [1611, 4, 1291, 2], [1612, 4, 1292, 2, "blurZone"], [1612, 12, 1292, 10], [1612, 14, 1292, 12], [1613, 6, 1293, 4, "position"], [1613, 14, 1293, 12], [1613, 16, 1293, 14], [1613, 26, 1293, 24], [1614, 6, 1294, 4, "overflow"], [1614, 14, 1294, 12], [1614, 16, 1294, 14], [1615, 4, 1295, 2], [1615, 5, 1295, 3], [1616, 4, 1296, 2, "previewChip"], [1616, 15, 1296, 13], [1616, 17, 1296, 15], [1617, 6, 1297, 4, "position"], [1617, 14, 1297, 12], [1617, 16, 1297, 14], [1617, 26, 1297, 24], [1618, 6, 1298, 4, "top"], [1618, 9, 1298, 7], [1618, 11, 1298, 9], [1618, 12, 1298, 10], [1619, 6, 1299, 4, "right"], [1619, 11, 1299, 9], [1619, 13, 1299, 11], [1619, 14, 1299, 12], [1620, 6, 1300, 4, "backgroundColor"], [1620, 21, 1300, 19], [1620, 23, 1300, 21], [1620, 40, 1300, 38], [1621, 6, 1301, 4, "paddingHorizontal"], [1621, 23, 1301, 21], [1621, 25, 1301, 23], [1621, 27, 1301, 25], [1622, 6, 1302, 4, "paddingVertical"], [1622, 21, 1302, 19], [1622, 23, 1302, 21], [1622, 24, 1302, 22], [1623, 6, 1303, 4, "borderRadius"], [1623, 18, 1303, 16], [1623, 20, 1303, 18], [1624, 4, 1304, 2], [1624, 5, 1304, 3], [1625, 4, 1305, 2, "previewChipText"], [1625, 19, 1305, 17], [1625, 21, 1305, 19], [1626, 6, 1306, 4, "color"], [1626, 11, 1306, 9], [1626, 13, 1306, 11], [1626, 19, 1306, 17], [1627, 6, 1307, 4, "fontSize"], [1627, 14, 1307, 12], [1627, 16, 1307, 14], [1627, 18, 1307, 16], [1628, 6, 1308, 4, "fontWeight"], [1628, 16, 1308, 14], [1628, 18, 1308, 16], [1629, 4, 1309, 2], [1630, 2, 1310, 0], [1630, 3, 1310, 1], [1630, 4, 1310, 2], [1631, 2, 1310, 3], [1631, 6, 1310, 3, "_c"], [1631, 8, 1310, 3], [1632, 2, 1310, 3, "$RefreshReg$"], [1632, 14, 1310, 3], [1632, 15, 1310, 3, "_c"], [1632, 17, 1310, 3], [1633, 0, 1310, 3], [1633, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdmE;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}