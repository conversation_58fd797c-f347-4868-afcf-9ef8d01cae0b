{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkTypefaceFontProvider", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 72, "index": 103}}], "key": "r+Ns9adQjK48AIzdjTmnDHoPemU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkTypefaceFontProviderFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkTypefaceFontProvider = require(_dependencyMap[1], \"./JsiSkTypefaceFontProvider\");\n  class JsiSkTypefaceFontProviderFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    Make() {\n      const tf = this.CanvasKit.TypefaceFontProvider.Make();\n      return new _JsiSkTypefaceFontProvider.JsiSkTypefaceFontProvider(this.CanvasKit, tf);\n    }\n  }\n  exports.JsiSkTypefaceFontProviderFactory = JsiSkTypefaceFontProviderFactory;\n});", "lineCount": 18, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkTypefaceFontProvider"], [7, 32, 2, 0], [7, 35, 2, 0, "require"], [7, 42, 2, 0], [7, 43, 2, 0, "_dependencyMap"], [7, 57, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkTypefaceFontProviderFactory"], [8, 40, 3, 45], [8, 49, 3, 54, "Host"], [8, 59, 3, 58], [8, 60, 3, 59], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "Make"], [12, 8, 7, 6, "Make"], [12, 9, 7, 6], [12, 11, 7, 9], [13, 6, 8, 4], [13, 12, 8, 10, "tf"], [13, 14, 8, 12], [13, 17, 8, 15], [13, 21, 8, 19], [13, 22, 8, 20, "CanvasKit"], [13, 31, 8, 29], [13, 32, 8, 30, "TypefaceFontProvider"], [13, 52, 8, 50], [13, 53, 8, 51, "Make"], [13, 57, 8, 55], [13, 58, 8, 56], [13, 59, 8, 57], [14, 6, 9, 4], [14, 13, 9, 11], [14, 17, 9, 15, "JsiSkTypefaceFontProvider"], [14, 69, 9, 40], [14, 70, 9, 41], [14, 74, 9, 45], [14, 75, 9, 46, "CanvasKit"], [14, 84, 9, 55], [14, 86, 9, 57, "tf"], [14, 88, 9, 59], [14, 89, 9, 60], [15, 4, 10, 2], [16, 2, 11, 0], [17, 2, 11, 1, "exports"], [17, 9, 11, 1], [17, 10, 11, 1, "JsiSkTypefaceFontProviderFactory"], [17, 42, 11, 1], [17, 45, 11, 1, "JsiSkTypefaceFontProviderFactory"], [17, 77, 11, 1], [18, 0, 11, 1], [18, 3]], "functionMap": {"names": ["<global>", "JsiSkTypefaceFontProviderFactory", "constructor", "Make"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFG;CDC"}}, "type": "js/module"}]}