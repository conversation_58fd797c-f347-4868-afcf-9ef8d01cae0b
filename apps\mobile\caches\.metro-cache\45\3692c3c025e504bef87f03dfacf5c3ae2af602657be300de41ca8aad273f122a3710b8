{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/PixelRatio", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5rdRioKC4qvLVlVTyxLOiQm3IeU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _PixelRatio = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/PixelRatio\"));\n  // Returns the Metro dev server-specific asset location.\n  function getScaledAssetPath(asset) {\n    const scale = AssetSourceResolver.pickScale(asset.scales, _PixelRatio.default.get());\n    const scaleSuffix = scale === 1 ? '' : '@' + scale + 'x';\n    const type = !asset.type ? '' : `.${asset.type}`;\n    if (__DEV__) {\n      return asset.httpServerLocation + '/' + asset.name + scaleSuffix + type;\n    } else {\n      return asset.httpServerLocation.replace(/\\.\\.\\//g, '_') + '/' + asset.name + scaleSuffix + type;\n    }\n  }\n  class AssetSourceResolver {\n    // where the jsbundle is being run from\n    // NOTE(EvanBacon): Never defined on web.\n    // @ts-expect-error: Never read locally\n\n    // the asset to resolve\n\n    constructor(serverUrl, jsbundleUrl, asset) {\n      this.serverUrl = serverUrl || 'https://expo.dev';\n      this.jsbundleUrl = null;\n      this.asset = asset;\n    }\n    // Always true for web runtimes\n    isLoadedFromServer() {\n      return true;\n    }\n    // Always false for web runtimes\n    isLoadedFromFileSystem() {\n      return false;\n    }\n    defaultAsset() {\n      return this.assetServerURL();\n    }\n    /**\n     * @returns absolute remote URL for the hosted asset.\n     */\n    assetServerURL() {\n      const fromUrl = new URL(getScaledAssetPath(this.asset), this.serverUrl);\n      fromUrl.searchParams.set('platform', _expoModulesCore.Platform.OS);\n      fromUrl.searchParams.set('hash', this.asset.hash);\n      return this.fromSource(\n      // Relative on web\n      fromUrl.toString().replace(fromUrl.origin, ''));\n    }\n    fromSource(source) {\n      return {\n        __packager_asset: true,\n        width: this.asset.width ?? undefined,\n        height: this.asset.height ?? undefined,\n        uri: source,\n        scale: AssetSourceResolver.pickScale(this.asset.scales, _PixelRatio.default.get())\n      };\n    }\n    static pickScale(scales, deviceScale) {\n      for (let i = 0; i < scales.length; i++) {\n        if (scales[i] >= deviceScale) {\n          return scales[i];\n        }\n      }\n      return scales[scales.length - 1] || 1;\n    }\n  }\n  exports.default = AssetSourceResolver;\n});", "lineCount": 73, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_expoModulesCore"], [7, 22, 1, 0], [7, 25, 1, 0, "require"], [7, 32, 1, 0], [7, 33, 1, 0, "_dependencyMap"], [7, 47, 1, 0], [8, 2, 1, 45], [8, 6, 1, 45, "_PixelRatio"], [8, 17, 1, 45], [8, 20, 1, 45, "_interopRequireDefault"], [8, 42, 1, 45], [8, 43, 1, 45, "require"], [8, 50, 1, 45], [8, 51, 1, 45, "_dependencyMap"], [8, 65, 1, 45], [9, 2, 3, 0], [10, 2, 4, 0], [10, 11, 4, 9, "getScaledAssetPath"], [10, 29, 4, 27, "getScaledAssetPath"], [10, 30, 4, 28, "asset"], [10, 35, 4, 33], [10, 37, 4, 35], [11, 4, 5, 4], [11, 10, 5, 10, "scale"], [11, 15, 5, 15], [11, 18, 5, 18, "AssetSourceResolver"], [11, 37, 5, 37], [11, 38, 5, 38, "pickScale"], [11, 47, 5, 47], [11, 48, 5, 48, "asset"], [11, 53, 5, 53], [11, 54, 5, 54, "scales"], [11, 60, 5, 60], [11, 62, 5, 62, "PixelRatio"], [11, 81, 5, 72], [11, 82, 5, 73, "get"], [11, 85, 5, 76], [11, 86, 5, 77], [11, 87, 5, 78], [11, 88, 5, 79], [12, 4, 6, 4], [12, 10, 6, 10, "scaleSuffix"], [12, 21, 6, 21], [12, 24, 6, 24, "scale"], [12, 29, 6, 29], [12, 34, 6, 34], [12, 35, 6, 35], [12, 38, 6, 38], [12, 40, 6, 40], [12, 43, 6, 43], [12, 46, 6, 46], [12, 49, 6, 49, "scale"], [12, 54, 6, 54], [12, 57, 6, 57], [12, 60, 6, 60], [13, 4, 7, 4], [13, 10, 7, 10, "type"], [13, 14, 7, 14], [13, 17, 7, 17], [13, 18, 7, 18, "asset"], [13, 23, 7, 23], [13, 24, 7, 24, "type"], [13, 28, 7, 28], [13, 31, 7, 31], [13, 33, 7, 33], [13, 36, 7, 36], [13, 40, 7, 40, "asset"], [13, 45, 7, 45], [13, 46, 7, 46, "type"], [13, 50, 7, 50], [13, 52, 7, 52], [14, 4, 8, 4], [14, 8, 8, 8, "__DEV__"], [14, 15, 8, 15], [14, 17, 8, 17], [15, 6, 9, 8], [15, 13, 9, 15, "asset"], [15, 18, 9, 20], [15, 19, 9, 21, "httpServerLocation"], [15, 37, 9, 39], [15, 40, 9, 42], [15, 43, 9, 45], [15, 46, 9, 48, "asset"], [15, 51, 9, 53], [15, 52, 9, 54, "name"], [15, 56, 9, 58], [15, 59, 9, 61, "scaleSuffix"], [15, 70, 9, 72], [15, 73, 9, 75, "type"], [15, 77, 9, 79], [16, 4, 10, 4], [16, 5, 10, 5], [16, 11, 11, 9], [17, 6, 12, 8], [17, 13, 12, 15, "asset"], [17, 18, 12, 20], [17, 19, 12, 21, "httpServerLocation"], [17, 37, 12, 39], [17, 38, 12, 40, "replace"], [17, 45, 12, 47], [17, 46, 12, 48], [17, 55, 12, 57], [17, 57, 12, 59], [17, 60, 12, 62], [17, 61, 12, 63], [17, 64, 12, 66], [17, 67, 12, 69], [17, 70, 12, 72, "asset"], [17, 75, 12, 77], [17, 76, 12, 78, "name"], [17, 80, 12, 82], [17, 83, 12, 85, "scaleSuffix"], [17, 94, 12, 96], [17, 97, 12, 99, "type"], [17, 101, 12, 103], [18, 4, 13, 4], [19, 2, 14, 0], [20, 2, 15, 15], [20, 8, 15, 21, "AssetSourceResolver"], [20, 27, 15, 40], [20, 28, 15, 41], [21, 4, 17, 4], [22, 4, 18, 4], [23, 4, 19, 4], [25, 4, 21, 4], [27, 4, 23, 4, "constructor"], [27, 15, 23, 15, "constructor"], [27, 16, 23, 16, "serverUrl"], [27, 25, 23, 25], [27, 27, 23, 27, "jsbundleUrl"], [27, 38, 23, 38], [27, 40, 23, 40, "asset"], [27, 45, 23, 45], [27, 47, 23, 47], [28, 6, 24, 8], [28, 10, 24, 12], [28, 11, 24, 13, "serverUrl"], [28, 20, 24, 22], [28, 23, 24, 25, "serverUrl"], [28, 32, 24, 34], [28, 36, 24, 38], [28, 54, 24, 56], [29, 6, 25, 8], [29, 10, 25, 12], [29, 11, 25, 13, "jsbundleUrl"], [29, 22, 25, 24], [29, 25, 25, 27], [29, 29, 25, 31], [30, 6, 26, 8], [30, 10, 26, 12], [30, 11, 26, 13, "asset"], [30, 16, 26, 18], [30, 19, 26, 21, "asset"], [30, 24, 26, 26], [31, 4, 27, 4], [32, 4, 28, 4], [33, 4, 29, 4, "isLoadedFromServer"], [33, 22, 29, 22, "isLoadedFromServer"], [33, 23, 29, 22], [33, 25, 29, 25], [34, 6, 30, 8], [34, 13, 30, 15], [34, 17, 30, 19], [35, 4, 31, 4], [36, 4, 32, 4], [37, 4, 33, 4, "isLoadedFromFileSystem"], [37, 26, 33, 26, "isLoadedFromFileSystem"], [37, 27, 33, 26], [37, 29, 33, 29], [38, 6, 34, 8], [38, 13, 34, 15], [38, 18, 34, 20], [39, 4, 35, 4], [40, 4, 36, 4, "defaultAsset"], [40, 16, 36, 16, "defaultAsset"], [40, 17, 36, 16], [40, 19, 36, 19], [41, 6, 37, 8], [41, 13, 37, 15], [41, 17, 37, 19], [41, 18, 37, 20, "assetServerURL"], [41, 32, 37, 34], [41, 33, 37, 35], [41, 34, 37, 36], [42, 4, 38, 4], [43, 4, 39, 4], [44, 0, 40, 0], [45, 0, 41, 0], [46, 4, 42, 4, "assetServerURL"], [46, 18, 42, 18, "assetServerURL"], [46, 19, 42, 18], [46, 21, 42, 21], [47, 6, 43, 8], [47, 12, 43, 14, "fromUrl"], [47, 19, 43, 21], [47, 22, 43, 24], [47, 26, 43, 28, "URL"], [47, 29, 43, 31], [47, 30, 43, 32, "getScaledAssetPath"], [47, 48, 43, 50], [47, 49, 43, 51], [47, 53, 43, 55], [47, 54, 43, 56, "asset"], [47, 59, 43, 61], [47, 60, 43, 62], [47, 62, 43, 64], [47, 66, 43, 68], [47, 67, 43, 69, "serverUrl"], [47, 76, 43, 78], [47, 77, 43, 79], [48, 6, 44, 8, "fromUrl"], [48, 13, 44, 15], [48, 14, 44, 16, "searchParams"], [48, 26, 44, 28], [48, 27, 44, 29, "set"], [48, 30, 44, 32], [48, 31, 44, 33], [48, 41, 44, 43], [48, 43, 44, 45, "Platform"], [48, 68, 44, 53], [48, 69, 44, 54, "OS"], [48, 71, 44, 56], [48, 72, 44, 57], [49, 6, 45, 8, "fromUrl"], [49, 13, 45, 15], [49, 14, 45, 16, "searchParams"], [49, 26, 45, 28], [49, 27, 45, 29, "set"], [49, 30, 45, 32], [49, 31, 45, 33], [49, 37, 45, 39], [49, 39, 45, 41], [49, 43, 45, 45], [49, 44, 45, 46, "asset"], [49, 49, 45, 51], [49, 50, 45, 52, "hash"], [49, 54, 45, 56], [49, 55, 45, 57], [50, 6, 46, 8], [50, 13, 46, 15], [50, 17, 46, 19], [50, 18, 46, 20, "fromSource"], [50, 28, 46, 30], [51, 6, 47, 8], [52, 6, 48, 8, "fromUrl"], [52, 13, 48, 15], [52, 14, 48, 16, "toString"], [52, 22, 48, 24], [52, 23, 48, 25], [52, 24, 48, 26], [52, 25, 48, 27, "replace"], [52, 32, 48, 34], [52, 33, 48, 35, "fromUrl"], [52, 40, 48, 42], [52, 41, 48, 43, "origin"], [52, 47, 48, 49], [52, 49, 48, 51], [52, 51, 48, 53], [52, 52, 48, 54], [52, 53, 48, 55], [53, 4, 49, 4], [54, 4, 50, 4, "fromSource"], [54, 14, 50, 14, "fromSource"], [54, 15, 50, 15, "source"], [54, 21, 50, 21], [54, 23, 50, 23], [55, 6, 51, 8], [55, 13, 51, 15], [56, 8, 52, 12, "__packager_asset"], [56, 24, 52, 28], [56, 26, 52, 30], [56, 30, 52, 34], [57, 8, 53, 12, "width"], [57, 13, 53, 17], [57, 15, 53, 19], [57, 19, 53, 23], [57, 20, 53, 24, "asset"], [57, 25, 53, 29], [57, 26, 53, 30, "width"], [57, 31, 53, 35], [57, 35, 53, 39, "undefined"], [57, 44, 53, 48], [58, 8, 54, 12, "height"], [58, 14, 54, 18], [58, 16, 54, 20], [58, 20, 54, 24], [58, 21, 54, 25, "asset"], [58, 26, 54, 30], [58, 27, 54, 31, "height"], [58, 33, 54, 37], [58, 37, 54, 41, "undefined"], [58, 46, 54, 50], [59, 8, 55, 12, "uri"], [59, 11, 55, 15], [59, 13, 55, 17, "source"], [59, 19, 55, 23], [60, 8, 56, 12, "scale"], [60, 13, 56, 17], [60, 15, 56, 19, "AssetSourceResolver"], [60, 34, 56, 38], [60, 35, 56, 39, "pickScale"], [60, 44, 56, 48], [60, 45, 56, 49], [60, 49, 56, 53], [60, 50, 56, 54, "asset"], [60, 55, 56, 59], [60, 56, 56, 60, "scales"], [60, 62, 56, 66], [60, 64, 56, 68, "PixelRatio"], [60, 83, 56, 78], [60, 84, 56, 79, "get"], [60, 87, 56, 82], [60, 88, 56, 83], [60, 89, 56, 84], [61, 6, 57, 8], [61, 7, 57, 9], [62, 4, 58, 4], [63, 4, 59, 4], [63, 11, 59, 11, "pickScale"], [63, 20, 59, 20, "pickScale"], [63, 21, 59, 21, "scales"], [63, 27, 59, 27], [63, 29, 59, 29, "deviceScale"], [63, 40, 59, 40], [63, 42, 59, 42], [64, 6, 60, 8], [64, 11, 60, 13], [64, 15, 60, 17, "i"], [64, 16, 60, 18], [64, 19, 60, 21], [64, 20, 60, 22], [64, 22, 60, 24, "i"], [64, 23, 60, 25], [64, 26, 60, 28, "scales"], [64, 32, 60, 34], [64, 33, 60, 35, "length"], [64, 39, 60, 41], [64, 41, 60, 43, "i"], [64, 42, 60, 44], [64, 44, 60, 46], [64, 46, 60, 48], [65, 8, 61, 12], [65, 12, 61, 16, "scales"], [65, 18, 61, 22], [65, 19, 61, 23, "i"], [65, 20, 61, 24], [65, 21, 61, 25], [65, 25, 61, 29, "deviceScale"], [65, 36, 61, 40], [65, 38, 61, 42], [66, 10, 62, 16], [66, 17, 62, 23, "scales"], [66, 23, 62, 29], [66, 24, 62, 30, "i"], [66, 25, 62, 31], [66, 26, 62, 32], [67, 8, 63, 12], [68, 6, 64, 8], [69, 6, 65, 8], [69, 13, 65, 15, "scales"], [69, 19, 65, 21], [69, 20, 65, 22, "scales"], [69, 26, 65, 28], [69, 27, 65, 29, "length"], [69, 33, 65, 35], [69, 36, 65, 38], [69, 37, 65, 39], [69, 38, 65, 40], [69, 42, 65, 44], [69, 43, 65, 45], [70, 4, 66, 4], [71, 2, 67, 0], [72, 2, 67, 1, "exports"], [72, 9, 67, 1], [72, 10, 67, 1, "default"], [72, 17, 67, 1], [72, 20, 67, 1, "AssetSourceResolver"], [72, 39, 67, 1], [73, 0, 67, 1], [73, 3]], "functionMap": {"names": ["<global>", "getScaledAssetPath", "AssetSourceResolver", "constructor", "isLoadedFromServer", "isLoadedFromFileSystem", "defaultAsset", "assetServerURL", "fromSource", "pickScale"], "mappings": "AAA;ACG;CDU;eEC;ICQ;KDI;IEE;KFE;IGE;KHE;IIC;KJE;IKI;KLO;IMC;KNQ;IOC;KPO;CFC"}}, "type": "js/module"}]}