{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 73, "index": 675}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkPaint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 676}, "end": {"line": 5, "column": 42, "index": 718}}], "key": "tZL5XO67L1lBKN/ngQFuxeWOGxA=", "exportNames": ["*"]}}, {"name": "./JsiSkPoint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 719}, "end": {"line": 6, "column": 42, "index": 761}}], "key": "t00LaVO/wJ2FJvJQ0krGRNiisCQ=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 762}, "end": {"line": 7, "column": 40, "index": 802}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}, {"name": "./JsiSkTypeface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 803}, "end": {"line": 8, "column": 48, "index": 851}}], "key": "oqqQaxz4M2TUiGCEbbh7Ll+rY0E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkFont = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkPaint = require(_dependencyMap[1], \"./JsiSkPaint\");\n  var _JsiSkPoint = require(_dependencyMap[2], \"./JsiSkPoint\");\n  var _JsiSkRect = require(_dependencyMap[3], \"./JsiSkRect\");\n  var _JsiSkTypeface = require(_dependencyMap[4], \"./JsiSkTypeface\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkFont extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Font\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    measureText(_text, _paint) {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n    getTextWidth(text, paint) {\n      const ids = this.getGlyphIDs(text);\n      const widths = this.getGlyphWidths(ids, paint);\n      return widths.reduce((a, b) => a + b, 0);\n    }\n    getMetrics() {\n      const result = this.ref.getMetrics();\n      return {\n        ascent: result.ascent,\n        descent: result.descent,\n        leading: result.leading,\n        bounds: result.bounds ? new _JsiSkRect.JsiSkRect(this.CanvasKit, result.bounds) : undefined\n      };\n    }\n    getGlyphIDs(str, numCodePoints) {\n      // TODO: Fix return value in the C++ implementation\n      return [...this.ref.getGlyphIDs(str, numCodePoints)];\n    }\n\n    // TODO: Fix return value in the C++ implementation, it return float32\n    getGlyphWidths(glyphs, paint) {\n      return [...this.ref.getGlyphWidths(glyphs, paint ? _JsiSkPaint.JsiSkPaint.fromValue(paint) : null)];\n    }\n    getGlyphIntercepts(glyphs, positions, top, bottom) {\n      return [...this.ref.getGlyphIntercepts(glyphs, positions.map(p => Array.from(_JsiSkPoint.JsiSkPoint.fromValue(p))).flat(), top, bottom)];\n    }\n    getScaleX() {\n      return this.ref.getScaleX();\n    }\n    getSize() {\n      return this.ref.getSize();\n    }\n    getSkewX() {\n      return this.ref.getSkewX();\n    }\n    isEmbolden() {\n      return this.ref.isEmbolden();\n    }\n    getTypeface() {\n      const tf = this.ref.getTypeface();\n      return tf ? new _JsiSkTypeface.JsiSkTypeface(this.CanvasKit, tf) : null;\n    }\n    setEdging(edging) {\n      this.ref.setEdging((0, _Host.getEnum)(this.CanvasKit, \"FontEdging\", edging));\n    }\n    setEmbeddedBitmaps(embeddedBitmaps) {\n      this.ref.setEmbeddedBitmaps(embeddedBitmaps);\n    }\n    setHinting(hinting) {\n      this.ref.setHinting((0, _Host.getEnum)(this.CanvasKit, \"FontHinting\", hinting));\n    }\n    setLinearMetrics(linearMetrics) {\n      this.ref.setLinearMetrics(linearMetrics);\n    }\n    setScaleX(sx) {\n      this.ref.setScaleX(sx);\n    }\n    setSize(points) {\n      this.ref.setSize(points);\n    }\n    setSkewX(sx) {\n      this.ref.setSkewX(sx);\n    }\n    setEmbolden(embolden) {\n      this.ref.setEmbolden(embolden);\n    }\n    setSubpixel(subpixel) {\n      this.ref.setSubpixel(subpixel);\n    }\n    setTypeface(face) {\n      this.ref.setTypeface(face ? _JsiSkTypeface.JsiSkTypeface.fromValue(face) : null);\n    }\n  }\n  exports.JsiSkFont = JsiSkFont;\n});", "lineCount": 117, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkPaint"], [7, 17, 5, 0], [7, 20, 5, 0, "require"], [7, 27, 5, 0], [7, 28, 5, 0, "_dependencyMap"], [7, 42, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkPoint"], [8, 17, 6, 0], [8, 20, 6, 0, "require"], [8, 27, 6, 0], [8, 28, 6, 0, "_dependencyMap"], [8, 42, 6, 0], [9, 2, 7, 0], [9, 6, 7, 0, "_JsiSkRect"], [9, 16, 7, 0], [9, 19, 7, 0, "require"], [9, 26, 7, 0], [9, 27, 7, 0, "_dependencyMap"], [9, 41, 7, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_JsiSkTypeface"], [10, 20, 8, 0], [10, 23, 8, 0, "require"], [10, 30, 8, 0], [10, 31, 8, 0, "_dependencyMap"], [10, 45, 8, 0], [11, 2, 1, 0], [11, 11, 1, 9, "_defineProperty"], [11, 26, 1, 24, "_defineProperty"], [11, 27, 1, 25, "e"], [11, 28, 1, 26], [11, 30, 1, 28, "r"], [11, 31, 1, 29], [11, 33, 1, 31, "t"], [11, 34, 1, 32], [11, 36, 1, 34], [12, 4, 1, 36], [12, 11, 1, 43], [12, 12, 1, 44, "r"], [12, 13, 1, 45], [12, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [12, 30, 1, 62], [12, 31, 1, 63, "r"], [12, 32, 1, 64], [12, 33, 1, 65], [12, 38, 1, 70, "e"], [12, 39, 1, 71], [12, 42, 1, 74, "Object"], [12, 48, 1, 80], [12, 49, 1, 81, "defineProperty"], [12, 63, 1, 95], [12, 64, 1, 96, "e"], [12, 65, 1, 97], [12, 67, 1, 99, "r"], [12, 68, 1, 100], [12, 70, 1, 102], [13, 6, 1, 104, "value"], [13, 11, 1, 109], [13, 13, 1, 111, "t"], [13, 14, 1, 112], [14, 6, 1, 114, "enumerable"], [14, 16, 1, 124], [14, 18, 1, 126], [14, 19, 1, 127], [14, 20, 1, 128], [15, 6, 1, 130, "configurable"], [15, 18, 1, 142], [15, 20, 1, 144], [15, 21, 1, 145], [15, 22, 1, 146], [16, 6, 1, 148, "writable"], [16, 14, 1, 156], [16, 16, 1, 158], [16, 17, 1, 159], [17, 4, 1, 161], [17, 5, 1, 162], [17, 6, 1, 163], [17, 9, 1, 166, "e"], [17, 10, 1, 167], [17, 11, 1, 168, "r"], [17, 12, 1, 169], [17, 13, 1, 170], [17, 16, 1, 173, "t"], [17, 17, 1, 174], [17, 19, 1, 176, "e"], [17, 20, 1, 177], [18, 2, 1, 179], [19, 2, 2, 0], [19, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 26, 2, 24, "t"], [19, 27, 2, 25], [19, 29, 2, 27], [20, 4, 2, 29], [20, 8, 2, 33, "i"], [20, 9, 2, 34], [20, 12, 2, 37, "_toPrimitive"], [20, 24, 2, 49], [20, 25, 2, 50, "t"], [20, 26, 2, 51], [20, 28, 2, 53], [20, 36, 2, 61], [20, 37, 2, 62], [21, 4, 2, 64], [21, 11, 2, 71], [21, 19, 2, 79], [21, 23, 2, 83], [21, 30, 2, 90, "i"], [21, 31, 2, 91], [21, 34, 2, 94, "i"], [21, 35, 2, 95], [21, 38, 2, 98, "i"], [21, 39, 2, 99], [21, 42, 2, 102], [21, 44, 2, 104], [22, 2, 2, 106], [23, 2, 3, 0], [23, 11, 3, 9, "_toPrimitive"], [23, 23, 3, 21, "_toPrimitive"], [23, 24, 3, 22, "t"], [23, 25, 3, 23], [23, 27, 3, 25, "r"], [23, 28, 3, 26], [23, 30, 3, 28], [24, 4, 3, 30], [24, 8, 3, 34], [24, 16, 3, 42], [24, 20, 3, 46], [24, 27, 3, 53, "t"], [24, 28, 3, 54], [24, 32, 3, 58], [24, 33, 3, 59, "t"], [24, 34, 3, 60], [24, 36, 3, 62], [24, 43, 3, 69, "t"], [24, 44, 3, 70], [25, 4, 3, 72], [25, 8, 3, 76, "e"], [25, 9, 3, 77], [25, 12, 3, 80, "t"], [25, 13, 3, 81], [25, 14, 3, 82, "Symbol"], [25, 20, 3, 88], [25, 21, 3, 89, "toPrimitive"], [25, 32, 3, 100], [25, 33, 3, 101], [26, 4, 3, 103], [26, 8, 3, 107], [26, 13, 3, 112], [26, 14, 3, 113], [26, 19, 3, 118, "e"], [26, 20, 3, 119], [26, 22, 3, 121], [27, 6, 3, 123], [27, 10, 3, 127, "i"], [27, 11, 3, 128], [27, 14, 3, 131, "e"], [27, 15, 3, 132], [27, 16, 3, 133, "call"], [27, 20, 3, 137], [27, 21, 3, 138, "t"], [27, 22, 3, 139], [27, 24, 3, 141, "r"], [27, 25, 3, 142], [27, 29, 3, 146], [27, 38, 3, 155], [27, 39, 3, 156], [28, 6, 3, 158], [28, 10, 3, 162], [28, 18, 3, 170], [28, 22, 3, 174], [28, 29, 3, 181, "i"], [28, 30, 3, 182], [28, 32, 3, 184], [28, 39, 3, 191, "i"], [28, 40, 3, 192], [29, 6, 3, 194], [29, 12, 3, 200], [29, 16, 3, 204, "TypeError"], [29, 25, 3, 213], [29, 26, 3, 214], [29, 72, 3, 260], [29, 73, 3, 261], [30, 4, 3, 263], [31, 4, 3, 265], [31, 11, 3, 272], [31, 12, 3, 273], [31, 20, 3, 281], [31, 25, 3, 286, "r"], [31, 26, 3, 287], [31, 29, 3, 290, "String"], [31, 35, 3, 296], [31, 38, 3, 299, "Number"], [31, 44, 3, 305], [31, 46, 3, 307, "t"], [31, 47, 3, 308], [31, 48, 3, 309], [32, 2, 3, 311], [33, 2, 9, 7], [33, 8, 9, 13, "JsiSkFont"], [33, 17, 9, 22], [33, 26, 9, 31, "HostObject"], [33, 42, 9, 41], [33, 43, 9, 42], [34, 4, 10, 2, "constructor"], [34, 15, 10, 13, "constructor"], [34, 16, 10, 14, "CanvasKit"], [34, 25, 10, 23], [34, 27, 10, 25, "ref"], [34, 30, 10, 28], [34, 32, 10, 30], [35, 6, 11, 4], [35, 11, 11, 9], [35, 12, 11, 10, "CanvasKit"], [35, 21, 11, 19], [35, 23, 11, 21, "ref"], [35, 26, 11, 24], [35, 28, 11, 26], [35, 34, 11, 32], [35, 35, 11, 33], [36, 6, 12, 4, "_defineProperty"], [36, 21, 12, 19], [36, 22, 12, 20], [36, 26, 12, 24], [36, 28, 12, 26], [36, 37, 12, 35], [36, 39, 12, 37], [36, 45, 12, 43], [37, 8, 13, 6], [37, 12, 13, 10], [37, 13, 13, 11, "ref"], [37, 16, 13, 14], [37, 17, 13, 15, "delete"], [37, 23, 13, 21], [37, 24, 13, 22], [37, 25, 13, 23], [38, 6, 14, 4], [38, 7, 14, 5], [38, 8, 14, 6], [39, 4, 15, 2], [40, 4, 16, 2, "measureText"], [40, 15, 16, 13, "measureText"], [40, 16, 16, 14, "_text"], [40, 21, 16, 19], [40, 23, 16, 21, "_paint"], [40, 29, 16, 27], [40, 31, 16, 29], [41, 6, 17, 4], [41, 13, 17, 11], [41, 17, 17, 11, "throwNotImplementedOnRNWeb"], [41, 49, 17, 37], [41, 51, 17, 38], [41, 52, 17, 39], [42, 4, 18, 2], [43, 4, 19, 2, "getTextWidth"], [43, 16, 19, 14, "getTextWidth"], [43, 17, 19, 15, "text"], [43, 21, 19, 19], [43, 23, 19, 21, "paint"], [43, 28, 19, 26], [43, 30, 19, 28], [44, 6, 20, 4], [44, 12, 20, 10, "ids"], [44, 15, 20, 13], [44, 18, 20, 16], [44, 22, 20, 20], [44, 23, 20, 21, "getGlyphIDs"], [44, 34, 20, 32], [44, 35, 20, 33, "text"], [44, 39, 20, 37], [44, 40, 20, 38], [45, 6, 21, 4], [45, 12, 21, 10, "widths"], [45, 18, 21, 16], [45, 21, 21, 19], [45, 25, 21, 23], [45, 26, 21, 24, "getGlyphWidths"], [45, 40, 21, 38], [45, 41, 21, 39, "ids"], [45, 44, 21, 42], [45, 46, 21, 44, "paint"], [45, 51, 21, 49], [45, 52, 21, 50], [46, 6, 22, 4], [46, 13, 22, 11, "widths"], [46, 19, 22, 17], [46, 20, 22, 18, "reduce"], [46, 26, 22, 24], [46, 27, 22, 25], [46, 28, 22, 26, "a"], [46, 29, 22, 27], [46, 31, 22, 29, "b"], [46, 32, 22, 30], [46, 37, 22, 35, "a"], [46, 38, 22, 36], [46, 41, 22, 39, "b"], [46, 42, 22, 40], [46, 44, 22, 42], [46, 45, 22, 43], [46, 46, 22, 44], [47, 4, 23, 2], [48, 4, 24, 2, "getMetrics"], [48, 14, 24, 12, "getMetrics"], [48, 15, 24, 12], [48, 17, 24, 15], [49, 6, 25, 4], [49, 12, 25, 10, "result"], [49, 18, 25, 16], [49, 21, 25, 19], [49, 25, 25, 23], [49, 26, 25, 24, "ref"], [49, 29, 25, 27], [49, 30, 25, 28, "getMetrics"], [49, 40, 25, 38], [49, 41, 25, 39], [49, 42, 25, 40], [50, 6, 26, 4], [50, 13, 26, 11], [51, 8, 27, 6, "ascent"], [51, 14, 27, 12], [51, 16, 27, 14, "result"], [51, 22, 27, 20], [51, 23, 27, 21, "ascent"], [51, 29, 27, 27], [52, 8, 28, 6, "descent"], [52, 15, 28, 13], [52, 17, 28, 15, "result"], [52, 23, 28, 21], [52, 24, 28, 22, "descent"], [52, 31, 28, 29], [53, 8, 29, 6, "leading"], [53, 15, 29, 13], [53, 17, 29, 15, "result"], [53, 23, 29, 21], [53, 24, 29, 22, "leading"], [53, 31, 29, 29], [54, 8, 30, 6, "bounds"], [54, 14, 30, 12], [54, 16, 30, 14, "result"], [54, 22, 30, 20], [54, 23, 30, 21, "bounds"], [54, 29, 30, 27], [54, 32, 30, 30], [54, 36, 30, 34, "JsiSkRect"], [54, 56, 30, 43], [54, 57, 30, 44], [54, 61, 30, 48], [54, 62, 30, 49, "CanvasKit"], [54, 71, 30, 58], [54, 73, 30, 60, "result"], [54, 79, 30, 66], [54, 80, 30, 67, "bounds"], [54, 86, 30, 73], [54, 87, 30, 74], [54, 90, 30, 77, "undefined"], [55, 6, 31, 4], [55, 7, 31, 5], [56, 4, 32, 2], [57, 4, 33, 2, "getGlyphIDs"], [57, 15, 33, 13, "getGlyphIDs"], [57, 16, 33, 14, "str"], [57, 19, 33, 17], [57, 21, 33, 19, "numCodePoints"], [57, 34, 33, 32], [57, 36, 33, 34], [58, 6, 34, 4], [59, 6, 35, 4], [59, 13, 35, 11], [59, 14, 35, 12], [59, 17, 35, 15], [59, 21, 35, 19], [59, 22, 35, 20, "ref"], [59, 25, 35, 23], [59, 26, 35, 24, "getGlyphIDs"], [59, 37, 35, 35], [59, 38, 35, 36, "str"], [59, 41, 35, 39], [59, 43, 35, 41, "numCodePoints"], [59, 56, 35, 54], [59, 57, 35, 55], [59, 58, 35, 56], [60, 4, 36, 2], [62, 4, 38, 2], [63, 4, 39, 2, "getGlyphWidths"], [63, 18, 39, 16, "getGlyphWidths"], [63, 19, 39, 17, "glyphs"], [63, 25, 39, 23], [63, 27, 39, 25, "paint"], [63, 32, 39, 30], [63, 34, 39, 32], [64, 6, 40, 4], [64, 13, 40, 11], [64, 14, 40, 12], [64, 17, 40, 15], [64, 21, 40, 19], [64, 22, 40, 20, "ref"], [64, 25, 40, 23], [64, 26, 40, 24, "getGlyphWidths"], [64, 40, 40, 38], [64, 41, 40, 39, "glyphs"], [64, 47, 40, 45], [64, 49, 40, 47, "paint"], [64, 54, 40, 52], [64, 57, 40, 55, "JsiSkPaint"], [64, 79, 40, 65], [64, 80, 40, 66, "fromValue"], [64, 89, 40, 75], [64, 90, 40, 76, "paint"], [64, 95, 40, 81], [64, 96, 40, 82], [64, 99, 40, 85], [64, 103, 40, 89], [64, 104, 40, 90], [64, 105, 40, 91], [65, 4, 41, 2], [66, 4, 42, 2, "getGlyphIntercepts"], [66, 22, 42, 20, "getGlyphIntercepts"], [66, 23, 42, 21, "glyphs"], [66, 29, 42, 27], [66, 31, 42, 29, "positions"], [66, 40, 42, 38], [66, 42, 42, 40, "top"], [66, 45, 42, 43], [66, 47, 42, 45, "bottom"], [66, 53, 42, 51], [66, 55, 42, 53], [67, 6, 43, 4], [67, 13, 43, 11], [67, 14, 43, 12], [67, 17, 43, 15], [67, 21, 43, 19], [67, 22, 43, 20, "ref"], [67, 25, 43, 23], [67, 26, 43, 24, "getGlyphIntercepts"], [67, 44, 43, 42], [67, 45, 43, 43, "glyphs"], [67, 51, 43, 49], [67, 53, 43, 51, "positions"], [67, 62, 43, 60], [67, 63, 43, 61, "map"], [67, 66, 43, 64], [67, 67, 43, 65, "p"], [67, 68, 43, 66], [67, 72, 43, 70, "Array"], [67, 77, 43, 75], [67, 78, 43, 76, "from"], [67, 82, 43, 80], [67, 83, 43, 81, "JsiSkPoint"], [67, 105, 43, 91], [67, 106, 43, 92, "fromValue"], [67, 115, 43, 101], [67, 116, 43, 102, "p"], [67, 117, 43, 103], [67, 118, 43, 104], [67, 119, 43, 105], [67, 120, 43, 106], [67, 121, 43, 107, "flat"], [67, 125, 43, 111], [67, 126, 43, 112], [67, 127, 43, 113], [67, 129, 43, 115, "top"], [67, 132, 43, 118], [67, 134, 43, 120, "bottom"], [67, 140, 43, 126], [67, 141, 43, 127], [67, 142, 43, 128], [68, 4, 44, 2], [69, 4, 45, 2, "getScaleX"], [69, 13, 45, 11, "getScaleX"], [69, 14, 45, 11], [69, 16, 45, 14], [70, 6, 46, 4], [70, 13, 46, 11], [70, 17, 46, 15], [70, 18, 46, 16, "ref"], [70, 21, 46, 19], [70, 22, 46, 20, "getScaleX"], [70, 31, 46, 29], [70, 32, 46, 30], [70, 33, 46, 31], [71, 4, 47, 2], [72, 4, 48, 2, "getSize"], [72, 11, 48, 9, "getSize"], [72, 12, 48, 9], [72, 14, 48, 12], [73, 6, 49, 4], [73, 13, 49, 11], [73, 17, 49, 15], [73, 18, 49, 16, "ref"], [73, 21, 49, 19], [73, 22, 49, 20, "getSize"], [73, 29, 49, 27], [73, 30, 49, 28], [73, 31, 49, 29], [74, 4, 50, 2], [75, 4, 51, 2, "getSkewX"], [75, 12, 51, 10, "getSkewX"], [75, 13, 51, 10], [75, 15, 51, 13], [76, 6, 52, 4], [76, 13, 52, 11], [76, 17, 52, 15], [76, 18, 52, 16, "ref"], [76, 21, 52, 19], [76, 22, 52, 20, "getSkewX"], [76, 30, 52, 28], [76, 31, 52, 29], [76, 32, 52, 30], [77, 4, 53, 2], [78, 4, 54, 2, "isEmbolden"], [78, 14, 54, 12, "isEmbolden"], [78, 15, 54, 12], [78, 17, 54, 15], [79, 6, 55, 4], [79, 13, 55, 11], [79, 17, 55, 15], [79, 18, 55, 16, "ref"], [79, 21, 55, 19], [79, 22, 55, 20, "isEmbolden"], [79, 32, 55, 30], [79, 33, 55, 31], [79, 34, 55, 32], [80, 4, 56, 2], [81, 4, 57, 2, "getTypeface"], [81, 15, 57, 13, "getTypeface"], [81, 16, 57, 13], [81, 18, 57, 16], [82, 6, 58, 4], [82, 12, 58, 10, "tf"], [82, 14, 58, 12], [82, 17, 58, 15], [82, 21, 58, 19], [82, 22, 58, 20, "ref"], [82, 25, 58, 23], [82, 26, 58, 24, "getTypeface"], [82, 37, 58, 35], [82, 38, 58, 36], [82, 39, 58, 37], [83, 6, 59, 4], [83, 13, 59, 11, "tf"], [83, 15, 59, 13], [83, 18, 59, 16], [83, 22, 59, 20, "JsiSkTypeface"], [83, 50, 59, 33], [83, 51, 59, 34], [83, 55, 59, 38], [83, 56, 59, 39, "CanvasKit"], [83, 65, 59, 48], [83, 67, 59, 50, "tf"], [83, 69, 59, 52], [83, 70, 59, 53], [83, 73, 59, 56], [83, 77, 59, 60], [84, 4, 60, 2], [85, 4, 61, 2, "setEdging"], [85, 13, 61, 11, "setEdging"], [85, 14, 61, 12, "edging"], [85, 20, 61, 18], [85, 22, 61, 20], [86, 6, 62, 4], [86, 10, 62, 8], [86, 11, 62, 9, "ref"], [86, 14, 62, 12], [86, 15, 62, 13, "setEdging"], [86, 24, 62, 22], [86, 25, 62, 23], [86, 29, 62, 23, "getEnum"], [86, 42, 62, 30], [86, 44, 62, 31], [86, 48, 62, 35], [86, 49, 62, 36, "CanvasKit"], [86, 58, 62, 45], [86, 60, 62, 47], [86, 72, 62, 59], [86, 74, 62, 61, "edging"], [86, 80, 62, 67], [86, 81, 62, 68], [86, 82, 62, 69], [87, 4, 63, 2], [88, 4, 64, 2, "setEmbeddedBitmaps"], [88, 22, 64, 20, "setEmbeddedBitmaps"], [88, 23, 64, 21, "embeddedBitmaps"], [88, 38, 64, 36], [88, 40, 64, 38], [89, 6, 65, 4], [89, 10, 65, 8], [89, 11, 65, 9, "ref"], [89, 14, 65, 12], [89, 15, 65, 13, "setEmbeddedBitmaps"], [89, 33, 65, 31], [89, 34, 65, 32, "embeddedBitmaps"], [89, 49, 65, 47], [89, 50, 65, 48], [90, 4, 66, 2], [91, 4, 67, 2, "setHinting"], [91, 14, 67, 12, "setHinting"], [91, 15, 67, 13, "hinting"], [91, 22, 67, 20], [91, 24, 67, 22], [92, 6, 68, 4], [92, 10, 68, 8], [92, 11, 68, 9, "ref"], [92, 14, 68, 12], [92, 15, 68, 13, "setHinting"], [92, 25, 68, 23], [92, 26, 68, 24], [92, 30, 68, 24, "getEnum"], [92, 43, 68, 31], [92, 45, 68, 32], [92, 49, 68, 36], [92, 50, 68, 37, "CanvasKit"], [92, 59, 68, 46], [92, 61, 68, 48], [92, 74, 68, 61], [92, 76, 68, 63, "hinting"], [92, 83, 68, 70], [92, 84, 68, 71], [92, 85, 68, 72], [93, 4, 69, 2], [94, 4, 70, 2, "setLinearMetrics"], [94, 20, 70, 18, "setLinearMetrics"], [94, 21, 70, 19, "linearMetrics"], [94, 34, 70, 32], [94, 36, 70, 34], [95, 6, 71, 4], [95, 10, 71, 8], [95, 11, 71, 9, "ref"], [95, 14, 71, 12], [95, 15, 71, 13, "setLinearMetrics"], [95, 31, 71, 29], [95, 32, 71, 30, "linearMetrics"], [95, 45, 71, 43], [95, 46, 71, 44], [96, 4, 72, 2], [97, 4, 73, 2, "setScaleX"], [97, 13, 73, 11, "setScaleX"], [97, 14, 73, 12, "sx"], [97, 16, 73, 14], [97, 18, 73, 16], [98, 6, 74, 4], [98, 10, 74, 8], [98, 11, 74, 9, "ref"], [98, 14, 74, 12], [98, 15, 74, 13, "setScaleX"], [98, 24, 74, 22], [98, 25, 74, 23, "sx"], [98, 27, 74, 25], [98, 28, 74, 26], [99, 4, 75, 2], [100, 4, 76, 2, "setSize"], [100, 11, 76, 9, "setSize"], [100, 12, 76, 10, "points"], [100, 18, 76, 16], [100, 20, 76, 18], [101, 6, 77, 4], [101, 10, 77, 8], [101, 11, 77, 9, "ref"], [101, 14, 77, 12], [101, 15, 77, 13, "setSize"], [101, 22, 77, 20], [101, 23, 77, 21, "points"], [101, 29, 77, 27], [101, 30, 77, 28], [102, 4, 78, 2], [103, 4, 79, 2, "setSkewX"], [103, 12, 79, 10, "setSkewX"], [103, 13, 79, 11, "sx"], [103, 15, 79, 13], [103, 17, 79, 15], [104, 6, 80, 4], [104, 10, 80, 8], [104, 11, 80, 9, "ref"], [104, 14, 80, 12], [104, 15, 80, 13, "setSkewX"], [104, 23, 80, 21], [104, 24, 80, 22, "sx"], [104, 26, 80, 24], [104, 27, 80, 25], [105, 4, 81, 2], [106, 4, 82, 2, "setEmbolden"], [106, 15, 82, 13, "setEmbolden"], [106, 16, 82, 14, "embolden"], [106, 24, 82, 22], [106, 26, 82, 24], [107, 6, 83, 4], [107, 10, 83, 8], [107, 11, 83, 9, "ref"], [107, 14, 83, 12], [107, 15, 83, 13, "setEmbolden"], [107, 26, 83, 24], [107, 27, 83, 25, "embolden"], [107, 35, 83, 33], [107, 36, 83, 34], [108, 4, 84, 2], [109, 4, 85, 2, "setSubpixel"], [109, 15, 85, 13, "setSubpixel"], [109, 16, 85, 14, "subpixel"], [109, 24, 85, 22], [109, 26, 85, 24], [110, 6, 86, 4], [110, 10, 86, 8], [110, 11, 86, 9, "ref"], [110, 14, 86, 12], [110, 15, 86, 13, "setSubpixel"], [110, 26, 86, 24], [110, 27, 86, 25, "subpixel"], [110, 35, 86, 33], [110, 36, 86, 34], [111, 4, 87, 2], [112, 4, 88, 2, "setTypeface"], [112, 15, 88, 13, "setTypeface"], [112, 16, 88, 14, "face"], [112, 20, 88, 18], [112, 22, 88, 20], [113, 6, 89, 4], [113, 10, 89, 8], [113, 11, 89, 9, "ref"], [113, 14, 89, 12], [113, 15, 89, 13, "setTypeface"], [113, 26, 89, 24], [113, 27, 89, 25, "face"], [113, 31, 89, 29], [113, 34, 89, 32, "JsiSkTypeface"], [113, 62, 89, 45], [113, 63, 89, 46, "fromValue"], [113, 72, 89, 55], [113, 73, 89, 56, "face"], [113, 77, 89, 60], [113, 78, 89, 61], [113, 81, 89, 64], [113, 85, 89, 68], [113, 86, 89, 69], [114, 4, 90, 2], [115, 2, 91, 0], [116, 2, 91, 1, "exports"], [116, 9, 91, 1], [116, 10, 91, 1, "JsiSkFont"], [116, 19, 91, 1], [116, 22, 91, 1, "JsiSkFont"], [116, 31, 91, 1], [117, 0, 91, 1], [117, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkFont", "constructor", "_defineProperty$argument_2", "measureText", "getTextWidth", "widths.reduce$argument_0", "getMetrics", "getGlyphIDs", "getGlyphWidths", "getGlyphIntercepts", "positions.map$argument_0", "getScaleX", "getSize", "getSkewX", "isEmbolden", "getTypeface", "setEdging", "setEmbeddedBitmaps", "setHinting", "setLinearMetrics", "setScaleX", "setSize", "setSkewX", "setEmbolden", "setSubpixel", "setTypeface"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGM;ECC;qCCE;KDE;GDC;EGC;GHE;EIC;yBCG,eD;GJC;EMC;GNQ;EOC;GPG;EQG;GRE;ESC;iECC,wCD;GTC;EWC;GXE;EYC;GZE;EaC;GbE;EcC;GdE;EeC;GfG;EgBC;GhBE;EiBC;GjBE;EkBC;GlBE;EmBC;GnBE;EoBC;GpBE;EqBC;GrBE;EsBC;GtBE;EuBC;GvBE;EwBC;GxBE;EyBC;GzBE;CHC"}}, "type": "js/module"}]}