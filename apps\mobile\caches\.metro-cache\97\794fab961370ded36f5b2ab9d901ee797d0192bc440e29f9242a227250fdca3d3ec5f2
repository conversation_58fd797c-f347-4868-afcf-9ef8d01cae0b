{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Simple skin tone detection and face-like region finding\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 20; // Adaptive block size\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize) {\n        for (let x = 0; x < img.width - blockSize; x += blockSize) {\n          const skinPixels = countSkinPixelsInRegion(data, x, y, blockSize, img.width);\n          const skinRatio = skinPixels / (blockSize * blockSize);\n\n          // If this region has a high concentration of skin-like pixels\n          if (skinRatio > 0.3) {\n            // Check if it's in a face-like position (upper 2/3 of image)\n            if (y < img.height * 0.67) {\n              faces.push({\n                boundingBox: {\n                  xCenter: (x + blockSize / 2) / img.width,\n                  yCenter: (y + blockSize / 2) / img.height,\n                  width: blockSize * 2 / img.width,\n                  // Make bounding box larger\n                  height: blockSize * 2.5 / img.height\n                }\n              });\n              console.log(`[EchoCameraWeb] 🎯 Found face-like region at (${x}, ${y}) with ${(skinRatio * 100).toFixed(1)}% skin pixels`);\n            }\n          }\n        }\n      }\n\n      // Merge overlapping detections and limit to most likely faces\n      const mergedFaces = mergeFaceDetections(faces);\n      return mergedFaces.slice(0, 5); // Max 5 faces\n    };\n    const countSkinPixelsInRegion = (data, startX, startY, size, imageWidth) => {\n      let skinPixels = 0;\n      for (let y = startY; y < startY + size; y++) {\n        for (let x = startX; x < startX + size; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Simple skin tone detection\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n        }\n      }\n      return skinPixels;\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Get the region to blur\n      const imageData = ctx.getImageData(x, y, width, height);\n      const data = imageData.data;\n\n      // Apply multiple blur effects\n\n      // 1. Heavy pixelation\n      const pixelSize = Math.max(25, Math.min(width, height) / 6);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < height; py += pixelSize) {\n        for (let px = 0; px < width; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n              const index = ((py + dy) * width + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n                const index = ((py + dy) * width + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // 2. Additional gaussian-like blur\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur effect`);\n      for (let i = 0; i < 3; i++) {\n        // Multiple passes\n        applySimpleBlur(data, width, height);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, x, y);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 876,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 912,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1577, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 74, 170, 72], [162, 75, 170, 73], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4], [172, 11, 180, 9], [172, 15, 180, 13, "y"], [172, 16, 180, 14], [172, 19, 180, 17], [172, 20, 180, 18], [172, 22, 180, 20, "y"], [172, 23, 180, 21], [172, 26, 180, 24, "img"], [172, 29, 180, 27], [172, 30, 180, 28, "height"], [172, 36, 180, 34], [172, 39, 180, 37, "blockSize"], [172, 48, 180, 46], [172, 50, 180, 48, "y"], [172, 51, 180, 49], [172, 55, 180, 53, "blockSize"], [172, 64, 180, 62], [172, 66, 180, 64], [173, 8, 181, 6], [173, 13, 181, 11], [173, 17, 181, 15, "x"], [173, 18, 181, 16], [173, 21, 181, 19], [173, 22, 181, 20], [173, 24, 181, 22, "x"], [173, 25, 181, 23], [173, 28, 181, 26, "img"], [173, 31, 181, 29], [173, 32, 181, 30, "width"], [173, 37, 181, 35], [173, 40, 181, 38, "blockSize"], [173, 49, 181, 47], [173, 51, 181, 49, "x"], [173, 52, 181, 50], [173, 56, 181, 54, "blockSize"], [173, 65, 181, 63], [173, 67, 181, 65], [174, 10, 182, 8], [174, 16, 182, 14, "skinPixels"], [174, 26, 182, 24], [174, 29, 182, 27, "countSkinPixelsInRegion"], [174, 52, 182, 50], [174, 53, 182, 51, "data"], [174, 57, 182, 55], [174, 59, 182, 57, "x"], [174, 60, 182, 58], [174, 62, 182, 60, "y"], [174, 63, 182, 61], [174, 65, 182, 63, "blockSize"], [174, 74, 182, 72], [174, 76, 182, 74, "img"], [174, 79, 182, 77], [174, 80, 182, 78, "width"], [174, 85, 182, 83], [174, 86, 182, 84], [175, 10, 183, 8], [175, 16, 183, 14, "skinRatio"], [175, 25, 183, 23], [175, 28, 183, 26, "skinPixels"], [175, 38, 183, 36], [175, 42, 183, 40, "blockSize"], [175, 51, 183, 49], [175, 54, 183, 52, "blockSize"], [175, 63, 183, 61], [175, 64, 183, 62], [177, 10, 185, 8], [178, 10, 186, 8], [178, 14, 186, 12, "skinRatio"], [178, 23, 186, 21], [178, 26, 186, 24], [178, 29, 186, 27], [178, 31, 186, 29], [179, 12, 187, 10], [180, 12, 188, 10], [180, 16, 188, 14, "y"], [180, 17, 188, 15], [180, 20, 188, 18, "img"], [180, 23, 188, 21], [180, 24, 188, 22, "height"], [180, 30, 188, 28], [180, 33, 188, 31], [180, 37, 188, 35], [180, 39, 188, 37], [181, 14, 189, 12, "faces"], [181, 19, 189, 17], [181, 20, 189, 18, "push"], [181, 24, 189, 22], [181, 25, 189, 23], [182, 16, 190, 14, "boundingBox"], [182, 27, 190, 25], [182, 29, 190, 27], [183, 18, 191, 16, "xCenter"], [183, 25, 191, 23], [183, 27, 191, 25], [183, 28, 191, 26, "x"], [183, 29, 191, 27], [183, 32, 191, 30, "blockSize"], [183, 41, 191, 39], [183, 44, 191, 42], [183, 45, 191, 43], [183, 49, 191, 47, "img"], [183, 52, 191, 50], [183, 53, 191, 51, "width"], [183, 58, 191, 56], [184, 18, 192, 16, "yCenter"], [184, 25, 192, 23], [184, 27, 192, 25], [184, 28, 192, 26, "y"], [184, 29, 192, 27], [184, 32, 192, 30, "blockSize"], [184, 41, 192, 39], [184, 44, 192, 42], [184, 45, 192, 43], [184, 49, 192, 47, "img"], [184, 52, 192, 50], [184, 53, 192, 51, "height"], [184, 59, 192, 57], [185, 18, 193, 16, "width"], [185, 23, 193, 21], [185, 25, 193, 24, "blockSize"], [185, 34, 193, 33], [185, 37, 193, 36], [185, 38, 193, 37], [185, 41, 193, 41, "img"], [185, 44, 193, 44], [185, 45, 193, 45, "width"], [185, 50, 193, 50], [186, 18, 193, 53], [187, 18, 194, 16, "height"], [187, 24, 194, 22], [187, 26, 194, 25, "blockSize"], [187, 35, 194, 34], [187, 38, 194, 37], [187, 41, 194, 40], [187, 44, 194, 44, "img"], [187, 47, 194, 47], [187, 48, 194, 48, "height"], [188, 16, 195, 14], [189, 14, 196, 12], [189, 15, 196, 13], [189, 16, 196, 14], [190, 14, 197, 12, "console"], [190, 21, 197, 19], [190, 22, 197, 20, "log"], [190, 25, 197, 23], [190, 26, 197, 24], [190, 75, 197, 73, "x"], [190, 76, 197, 74], [190, 81, 197, 79, "y"], [190, 82, 197, 80], [190, 92, 197, 90], [190, 93, 197, 91, "skinRatio"], [190, 102, 197, 100], [190, 105, 197, 103], [190, 108, 197, 106], [190, 110, 197, 108, "toFixed"], [190, 117, 197, 115], [190, 118, 197, 116], [190, 119, 197, 117], [190, 120, 197, 118], [190, 135, 197, 133], [190, 136, 197, 134], [191, 12, 198, 10], [192, 10, 199, 8], [193, 8, 200, 6], [194, 6, 201, 4], [196, 6, 203, 4], [197, 6, 204, 4], [197, 12, 204, 10, "mergedFaces"], [197, 23, 204, 21], [197, 26, 204, 24, "mergeFaceDetections"], [197, 45, 204, 43], [197, 46, 204, 44, "faces"], [197, 51, 204, 49], [197, 52, 204, 50], [198, 6, 205, 4], [198, 13, 205, 11, "mergedFaces"], [198, 24, 205, 22], [198, 25, 205, 23, "slice"], [198, 30, 205, 28], [198, 31, 205, 29], [198, 32, 205, 30], [198, 34, 205, 32], [198, 35, 205, 33], [198, 36, 205, 34], [198, 37, 205, 35], [198, 38, 205, 36], [199, 4, 206, 2], [199, 5, 206, 3], [200, 4, 208, 2], [200, 10, 208, 8, "countSkinPixelsInRegion"], [200, 33, 208, 31], [200, 36, 208, 34, "countSkinPixelsInRegion"], [200, 37, 208, 35, "data"], [200, 41, 208, 58], [200, 43, 208, 60, "startX"], [200, 49, 208, 74], [200, 51, 208, 76, "startY"], [200, 57, 208, 90], [200, 59, 208, 92, "size"], [200, 63, 208, 104], [200, 65, 208, 106, "imageWidth"], [200, 75, 208, 124], [200, 80, 208, 129], [201, 6, 209, 4], [201, 10, 209, 8, "skinPixels"], [201, 20, 209, 18], [201, 23, 209, 21], [201, 24, 209, 22], [202, 6, 210, 4], [202, 11, 210, 9], [202, 15, 210, 13, "y"], [202, 16, 210, 14], [202, 19, 210, 17, "startY"], [202, 25, 210, 23], [202, 27, 210, 25, "y"], [202, 28, 210, 26], [202, 31, 210, 29, "startY"], [202, 37, 210, 35], [202, 40, 210, 38, "size"], [202, 44, 210, 42], [202, 46, 210, 44, "y"], [202, 47, 210, 45], [202, 49, 210, 47], [202, 51, 210, 49], [203, 8, 211, 6], [203, 13, 211, 11], [203, 17, 211, 15, "x"], [203, 18, 211, 16], [203, 21, 211, 19, "startX"], [203, 27, 211, 25], [203, 29, 211, 27, "x"], [203, 30, 211, 28], [203, 33, 211, 31, "startX"], [203, 39, 211, 37], [203, 42, 211, 40, "size"], [203, 46, 211, 44], [203, 48, 211, 46, "x"], [203, 49, 211, 47], [203, 51, 211, 49], [203, 53, 211, 51], [204, 10, 212, 8], [204, 16, 212, 14, "index"], [204, 21, 212, 19], [204, 24, 212, 22], [204, 25, 212, 23, "y"], [204, 26, 212, 24], [204, 29, 212, 27, "imageWidth"], [204, 39, 212, 37], [204, 42, 212, 40, "x"], [204, 43, 212, 41], [204, 47, 212, 45], [204, 48, 212, 46], [205, 10, 213, 8], [205, 16, 213, 14, "r"], [205, 17, 213, 15], [205, 20, 213, 18, "data"], [205, 24, 213, 22], [205, 25, 213, 23, "index"], [205, 30, 213, 28], [205, 31, 213, 29], [206, 10, 214, 8], [206, 16, 214, 14, "g"], [206, 17, 214, 15], [206, 20, 214, 18, "data"], [206, 24, 214, 22], [206, 25, 214, 23, "index"], [206, 30, 214, 28], [206, 33, 214, 31], [206, 34, 214, 32], [206, 35, 214, 33], [207, 10, 215, 8], [207, 16, 215, 14, "b"], [207, 17, 215, 15], [207, 20, 215, 18, "data"], [207, 24, 215, 22], [207, 25, 215, 23, "index"], [207, 30, 215, 28], [207, 33, 215, 31], [207, 34, 215, 32], [207, 35, 215, 33], [209, 10, 217, 8], [210, 10, 218, 8], [210, 14, 218, 12, "isSkinTone"], [210, 24, 218, 22], [210, 25, 218, 23, "r"], [210, 26, 218, 24], [210, 28, 218, 26, "g"], [210, 29, 218, 27], [210, 31, 218, 29, "b"], [210, 32, 218, 30], [210, 33, 218, 31], [210, 35, 218, 33], [211, 12, 219, 10, "skinPixels"], [211, 22, 219, 20], [211, 24, 219, 22], [212, 10, 220, 8], [213, 8, 221, 6], [214, 6, 222, 4], [215, 6, 223, 4], [215, 13, 223, 11, "skinPixels"], [215, 23, 223, 21], [216, 4, 224, 2], [216, 5, 224, 3], [217, 4, 226, 2], [217, 10, 226, 8, "isSkinTone"], [217, 20, 226, 18], [217, 23, 226, 21, "isSkinTone"], [217, 24, 226, 22, "r"], [217, 25, 226, 31], [217, 27, 226, 33, "g"], [217, 28, 226, 42], [217, 30, 226, 44, "b"], [217, 31, 226, 53], [217, 36, 226, 58], [218, 6, 227, 4], [219, 6, 228, 4], [219, 13, 229, 6, "r"], [219, 14, 229, 7], [219, 17, 229, 10], [219, 19, 229, 12], [219, 23, 229, 16, "g"], [219, 24, 229, 17], [219, 27, 229, 20], [219, 29, 229, 22], [219, 33, 229, 26, "b"], [219, 34, 229, 27], [219, 37, 229, 30], [219, 39, 229, 32], [219, 43, 230, 6, "r"], [219, 44, 230, 7], [219, 47, 230, 10, "g"], [219, 48, 230, 11], [219, 52, 230, 15, "r"], [219, 53, 230, 16], [219, 56, 230, 19, "b"], [219, 57, 230, 20], [219, 61, 231, 6, "Math"], [219, 65, 231, 10], [219, 66, 231, 11, "abs"], [219, 69, 231, 14], [219, 70, 231, 15, "r"], [219, 71, 231, 16], [219, 74, 231, 19, "g"], [219, 75, 231, 20], [219, 76, 231, 21], [219, 79, 231, 24], [219, 81, 231, 26], [219, 85, 232, 6, "Math"], [219, 89, 232, 10], [219, 90, 232, 11, "max"], [219, 93, 232, 14], [219, 94, 232, 15, "r"], [219, 95, 232, 16], [219, 97, 232, 18, "g"], [219, 98, 232, 19], [219, 100, 232, 21, "b"], [219, 101, 232, 22], [219, 102, 232, 23], [219, 105, 232, 26, "Math"], [219, 109, 232, 30], [219, 110, 232, 31, "min"], [219, 113, 232, 34], [219, 114, 232, 35, "r"], [219, 115, 232, 36], [219, 117, 232, 38, "g"], [219, 118, 232, 39], [219, 120, 232, 41, "b"], [219, 121, 232, 42], [219, 122, 232, 43], [219, 125, 232, 46], [219, 127, 232, 48], [220, 4, 234, 2], [220, 5, 234, 3], [221, 4, 236, 2], [221, 10, 236, 8, "mergeFaceDetections"], [221, 29, 236, 27], [221, 32, 236, 31, "faces"], [221, 37, 236, 43], [221, 41, 236, 48], [222, 6, 237, 4], [222, 10, 237, 8, "faces"], [222, 15, 237, 13], [222, 16, 237, 14, "length"], [222, 22, 237, 20], [222, 26, 237, 24], [222, 27, 237, 25], [222, 29, 237, 27], [222, 36, 237, 34, "faces"], [222, 41, 237, 39], [223, 6, 239, 4], [223, 12, 239, 10, "merged"], [223, 18, 239, 16], [223, 21, 239, 19], [223, 23, 239, 21], [224, 6, 240, 4], [224, 12, 240, 10, "used"], [224, 16, 240, 14], [224, 19, 240, 17], [224, 23, 240, 21, "Set"], [224, 26, 240, 24], [224, 27, 240, 25], [224, 28, 240, 26], [225, 6, 242, 4], [225, 11, 242, 9], [225, 15, 242, 13, "i"], [225, 16, 242, 14], [225, 19, 242, 17], [225, 20, 242, 18], [225, 22, 242, 20, "i"], [225, 23, 242, 21], [225, 26, 242, 24, "faces"], [225, 31, 242, 29], [225, 32, 242, 30, "length"], [225, 38, 242, 36], [225, 40, 242, 38, "i"], [225, 41, 242, 39], [225, 43, 242, 41], [225, 45, 242, 43], [226, 8, 243, 6], [226, 12, 243, 10, "used"], [226, 16, 243, 14], [226, 17, 243, 15, "has"], [226, 20, 243, 18], [226, 21, 243, 19, "i"], [226, 22, 243, 20], [226, 23, 243, 21], [226, 25, 243, 23], [227, 8, 245, 6], [227, 12, 245, 10, "currentFace"], [227, 23, 245, 21], [227, 26, 245, 24, "faces"], [227, 31, 245, 29], [227, 32, 245, 30, "i"], [227, 33, 245, 31], [227, 34, 245, 32], [228, 8, 246, 6, "used"], [228, 12, 246, 10], [228, 13, 246, 11, "add"], [228, 16, 246, 14], [228, 17, 246, 15, "i"], [228, 18, 246, 16], [228, 19, 246, 17], [230, 8, 248, 6], [231, 8, 249, 6], [231, 13, 249, 11], [231, 17, 249, 15, "j"], [231, 18, 249, 16], [231, 21, 249, 19, "i"], [231, 22, 249, 20], [231, 25, 249, 23], [231, 26, 249, 24], [231, 28, 249, 26, "j"], [231, 29, 249, 27], [231, 32, 249, 30, "faces"], [231, 37, 249, 35], [231, 38, 249, 36, "length"], [231, 44, 249, 42], [231, 46, 249, 44, "j"], [231, 47, 249, 45], [231, 49, 249, 47], [231, 51, 249, 49], [232, 10, 250, 8], [232, 14, 250, 12, "used"], [232, 18, 250, 16], [232, 19, 250, 17, "has"], [232, 22, 250, 20], [232, 23, 250, 21, "j"], [232, 24, 250, 22], [232, 25, 250, 23], [232, 27, 250, 25], [233, 10, 252, 8], [233, 16, 252, 14, "overlap"], [233, 23, 252, 21], [233, 26, 252, 24, "calculateOverlap"], [233, 42, 252, 40], [233, 43, 252, 41, "currentFace"], [233, 54, 252, 52], [233, 55, 252, 53, "boundingBox"], [233, 66, 252, 64], [233, 68, 252, 66, "faces"], [233, 73, 252, 71], [233, 74, 252, 72, "j"], [233, 75, 252, 73], [233, 76, 252, 74], [233, 77, 252, 75, "boundingBox"], [233, 88, 252, 86], [233, 89, 252, 87], [234, 10, 253, 8], [234, 14, 253, 12, "overlap"], [234, 21, 253, 19], [234, 24, 253, 22], [234, 27, 253, 25], [234, 29, 253, 27], [235, 12, 253, 29], [236, 12, 254, 10], [237, 12, 255, 10, "currentFace"], [237, 23, 255, 21], [237, 26, 255, 24, "mergeTwoFaces"], [237, 39, 255, 37], [237, 40, 255, 38, "currentFace"], [237, 51, 255, 49], [237, 53, 255, 51, "faces"], [237, 58, 255, 56], [237, 59, 255, 57, "j"], [237, 60, 255, 58], [237, 61, 255, 59], [237, 62, 255, 60], [238, 12, 256, 10, "used"], [238, 16, 256, 14], [238, 17, 256, 15, "add"], [238, 20, 256, 18], [238, 21, 256, 19, "j"], [238, 22, 256, 20], [238, 23, 256, 21], [239, 10, 257, 8], [240, 8, 258, 6], [241, 8, 260, 6, "merged"], [241, 14, 260, 12], [241, 15, 260, 13, "push"], [241, 19, 260, 17], [241, 20, 260, 18, "currentFace"], [241, 31, 260, 29], [241, 32, 260, 30], [242, 6, 261, 4], [243, 6, 263, 4], [243, 13, 263, 11, "merged"], [243, 19, 263, 17], [244, 4, 264, 2], [244, 5, 264, 3], [245, 4, 266, 2], [245, 10, 266, 8, "calculateOverlap"], [245, 26, 266, 24], [245, 29, 266, 27, "calculateOverlap"], [245, 30, 266, 28, "box1"], [245, 34, 266, 37], [245, 36, 266, 39, "box2"], [245, 40, 266, 48], [245, 45, 266, 53], [246, 6, 267, 4], [246, 12, 267, 10, "x1"], [246, 14, 267, 12], [246, 17, 267, 15, "Math"], [246, 21, 267, 19], [246, 22, 267, 20, "max"], [246, 25, 267, 23], [246, 26, 267, 24, "box1"], [246, 30, 267, 28], [246, 31, 267, 29, "xCenter"], [246, 38, 267, 36], [246, 41, 267, 39, "box1"], [246, 45, 267, 43], [246, 46, 267, 44, "width"], [246, 51, 267, 49], [246, 54, 267, 50], [246, 55, 267, 51], [246, 57, 267, 53, "box2"], [246, 61, 267, 57], [246, 62, 267, 58, "xCenter"], [246, 69, 267, 65], [246, 72, 267, 68, "box2"], [246, 76, 267, 72], [246, 77, 267, 73, "width"], [246, 82, 267, 78], [246, 85, 267, 79], [246, 86, 267, 80], [246, 87, 267, 81], [247, 6, 268, 4], [247, 12, 268, 10, "y1"], [247, 14, 268, 12], [247, 17, 268, 15, "Math"], [247, 21, 268, 19], [247, 22, 268, 20, "max"], [247, 25, 268, 23], [247, 26, 268, 24, "box1"], [247, 30, 268, 28], [247, 31, 268, 29, "yCenter"], [247, 38, 268, 36], [247, 41, 268, 39, "box1"], [247, 45, 268, 43], [247, 46, 268, 44, "height"], [247, 52, 268, 50], [247, 55, 268, 51], [247, 56, 268, 52], [247, 58, 268, 54, "box2"], [247, 62, 268, 58], [247, 63, 268, 59, "yCenter"], [247, 70, 268, 66], [247, 73, 268, 69, "box2"], [247, 77, 268, 73], [247, 78, 268, 74, "height"], [247, 84, 268, 80], [247, 87, 268, 81], [247, 88, 268, 82], [247, 89, 268, 83], [248, 6, 269, 4], [248, 12, 269, 10, "x2"], [248, 14, 269, 12], [248, 17, 269, 15, "Math"], [248, 21, 269, 19], [248, 22, 269, 20, "min"], [248, 25, 269, 23], [248, 26, 269, 24, "box1"], [248, 30, 269, 28], [248, 31, 269, 29, "xCenter"], [248, 38, 269, 36], [248, 41, 269, 39, "box1"], [248, 45, 269, 43], [248, 46, 269, 44, "width"], [248, 51, 269, 49], [248, 54, 269, 50], [248, 55, 269, 51], [248, 57, 269, 53, "box2"], [248, 61, 269, 57], [248, 62, 269, 58, "xCenter"], [248, 69, 269, 65], [248, 72, 269, 68, "box2"], [248, 76, 269, 72], [248, 77, 269, 73, "width"], [248, 82, 269, 78], [248, 85, 269, 79], [248, 86, 269, 80], [248, 87, 269, 81], [249, 6, 270, 4], [249, 12, 270, 10, "y2"], [249, 14, 270, 12], [249, 17, 270, 15, "Math"], [249, 21, 270, 19], [249, 22, 270, 20, "min"], [249, 25, 270, 23], [249, 26, 270, 24, "box1"], [249, 30, 270, 28], [249, 31, 270, 29, "yCenter"], [249, 38, 270, 36], [249, 41, 270, 39, "box1"], [249, 45, 270, 43], [249, 46, 270, 44, "height"], [249, 52, 270, 50], [249, 55, 270, 51], [249, 56, 270, 52], [249, 58, 270, 54, "box2"], [249, 62, 270, 58], [249, 63, 270, 59, "yCenter"], [249, 70, 270, 66], [249, 73, 270, 69, "box2"], [249, 77, 270, 73], [249, 78, 270, 74, "height"], [249, 84, 270, 80], [249, 87, 270, 81], [249, 88, 270, 82], [249, 89, 270, 83], [250, 6, 272, 4], [250, 10, 272, 8, "x2"], [250, 12, 272, 10], [250, 16, 272, 14, "x1"], [250, 18, 272, 16], [250, 22, 272, 20, "y2"], [250, 24, 272, 22], [250, 28, 272, 26, "y1"], [250, 30, 272, 28], [250, 32, 272, 30], [250, 39, 272, 37], [250, 40, 272, 38], [251, 6, 274, 4], [251, 12, 274, 10, "overlapArea"], [251, 23, 274, 21], [251, 26, 274, 24], [251, 27, 274, 25, "x2"], [251, 29, 274, 27], [251, 32, 274, 30, "x1"], [251, 34, 274, 32], [251, 39, 274, 37, "y2"], [251, 41, 274, 39], [251, 44, 274, 42, "y1"], [251, 46, 274, 44], [251, 47, 274, 45], [252, 6, 275, 4], [252, 12, 275, 10, "box1Area"], [252, 20, 275, 18], [252, 23, 275, 21, "box1"], [252, 27, 275, 25], [252, 28, 275, 26, "width"], [252, 33, 275, 31], [252, 36, 275, 34, "box1"], [252, 40, 275, 38], [252, 41, 275, 39, "height"], [252, 47, 275, 45], [253, 6, 276, 4], [253, 12, 276, 10, "box2Area"], [253, 20, 276, 18], [253, 23, 276, 21, "box2"], [253, 27, 276, 25], [253, 28, 276, 26, "width"], [253, 33, 276, 31], [253, 36, 276, 34, "box2"], [253, 40, 276, 38], [253, 41, 276, 39, "height"], [253, 47, 276, 45], [254, 6, 278, 4], [254, 13, 278, 11, "overlapArea"], [254, 24, 278, 22], [254, 27, 278, 25, "Math"], [254, 31, 278, 29], [254, 32, 278, 30, "min"], [254, 35, 278, 33], [254, 36, 278, 34, "box1Area"], [254, 44, 278, 42], [254, 46, 278, 44, "box2Area"], [254, 54, 278, 52], [254, 55, 278, 53], [255, 4, 279, 2], [255, 5, 279, 3], [256, 4, 281, 2], [256, 10, 281, 8, "mergeTwoFaces"], [256, 23, 281, 21], [256, 26, 281, 24, "mergeTwoFaces"], [256, 27, 281, 25, "face1"], [256, 32, 281, 35], [256, 34, 281, 37, "face2"], [256, 39, 281, 47], [256, 44, 281, 52], [257, 6, 282, 4], [257, 12, 282, 10, "box1"], [257, 16, 282, 14], [257, 19, 282, 17, "face1"], [257, 24, 282, 22], [257, 25, 282, 23, "boundingBox"], [257, 36, 282, 34], [258, 6, 283, 4], [258, 12, 283, 10, "box2"], [258, 16, 283, 14], [258, 19, 283, 17, "face2"], [258, 24, 283, 22], [258, 25, 283, 23, "boundingBox"], [258, 36, 283, 34], [259, 6, 285, 4], [259, 12, 285, 10, "left"], [259, 16, 285, 14], [259, 19, 285, 17, "Math"], [259, 23, 285, 21], [259, 24, 285, 22, "min"], [259, 27, 285, 25], [259, 28, 285, 26, "box1"], [259, 32, 285, 30], [259, 33, 285, 31, "xCenter"], [259, 40, 285, 38], [259, 43, 285, 41, "box1"], [259, 47, 285, 45], [259, 48, 285, 46, "width"], [259, 53, 285, 51], [259, 56, 285, 52], [259, 57, 285, 53], [259, 59, 285, 55, "box2"], [259, 63, 285, 59], [259, 64, 285, 60, "xCenter"], [259, 71, 285, 67], [259, 74, 285, 70, "box2"], [259, 78, 285, 74], [259, 79, 285, 75, "width"], [259, 84, 285, 80], [259, 87, 285, 81], [259, 88, 285, 82], [259, 89, 285, 83], [260, 6, 286, 4], [260, 12, 286, 10, "right"], [260, 17, 286, 15], [260, 20, 286, 18, "Math"], [260, 24, 286, 22], [260, 25, 286, 23, "max"], [260, 28, 286, 26], [260, 29, 286, 27, "box1"], [260, 33, 286, 31], [260, 34, 286, 32, "xCenter"], [260, 41, 286, 39], [260, 44, 286, 42, "box1"], [260, 48, 286, 46], [260, 49, 286, 47, "width"], [260, 54, 286, 52], [260, 57, 286, 53], [260, 58, 286, 54], [260, 60, 286, 56, "box2"], [260, 64, 286, 60], [260, 65, 286, 61, "xCenter"], [260, 72, 286, 68], [260, 75, 286, 71, "box2"], [260, 79, 286, 75], [260, 80, 286, 76, "width"], [260, 85, 286, 81], [260, 88, 286, 82], [260, 89, 286, 83], [260, 90, 286, 84], [261, 6, 287, 4], [261, 12, 287, 10, "top"], [261, 15, 287, 13], [261, 18, 287, 16, "Math"], [261, 22, 287, 20], [261, 23, 287, 21, "min"], [261, 26, 287, 24], [261, 27, 287, 25, "box1"], [261, 31, 287, 29], [261, 32, 287, 30, "yCenter"], [261, 39, 287, 37], [261, 42, 287, 40, "box1"], [261, 46, 287, 44], [261, 47, 287, 45, "height"], [261, 53, 287, 51], [261, 56, 287, 52], [261, 57, 287, 53], [261, 59, 287, 55, "box2"], [261, 63, 287, 59], [261, 64, 287, 60, "yCenter"], [261, 71, 287, 67], [261, 74, 287, 70, "box2"], [261, 78, 287, 74], [261, 79, 287, 75, "height"], [261, 85, 287, 81], [261, 88, 287, 82], [261, 89, 287, 83], [261, 90, 287, 84], [262, 6, 288, 4], [262, 12, 288, 10, "bottom"], [262, 18, 288, 16], [262, 21, 288, 19, "Math"], [262, 25, 288, 23], [262, 26, 288, 24, "max"], [262, 29, 288, 27], [262, 30, 288, 28, "box1"], [262, 34, 288, 32], [262, 35, 288, 33, "yCenter"], [262, 42, 288, 40], [262, 45, 288, 43, "box1"], [262, 49, 288, 47], [262, 50, 288, 48, "height"], [262, 56, 288, 54], [262, 59, 288, 55], [262, 60, 288, 56], [262, 62, 288, 58, "box2"], [262, 66, 288, 62], [262, 67, 288, 63, "yCenter"], [262, 74, 288, 70], [262, 77, 288, 73, "box2"], [262, 81, 288, 77], [262, 82, 288, 78, "height"], [262, 88, 288, 84], [262, 91, 288, 85], [262, 92, 288, 86], [262, 93, 288, 87], [263, 6, 290, 4], [263, 13, 290, 11], [264, 8, 291, 6, "boundingBox"], [264, 19, 291, 17], [264, 21, 291, 19], [265, 10, 292, 8, "xCenter"], [265, 17, 292, 15], [265, 19, 292, 17], [265, 20, 292, 18, "left"], [265, 24, 292, 22], [265, 27, 292, 25, "right"], [265, 32, 292, 30], [265, 36, 292, 34], [265, 37, 292, 35], [266, 10, 293, 8, "yCenter"], [266, 17, 293, 15], [266, 19, 293, 17], [266, 20, 293, 18, "top"], [266, 23, 293, 21], [266, 26, 293, 24, "bottom"], [266, 32, 293, 30], [266, 36, 293, 34], [266, 37, 293, 35], [267, 10, 294, 8, "width"], [267, 15, 294, 13], [267, 17, 294, 15, "right"], [267, 22, 294, 20], [267, 25, 294, 23, "left"], [267, 29, 294, 27], [268, 10, 295, 8, "height"], [268, 16, 295, 14], [268, 18, 295, 16, "bottom"], [268, 24, 295, 22], [268, 27, 295, 25, "top"], [269, 8, 296, 6], [270, 6, 297, 4], [270, 7, 297, 5], [271, 4, 298, 2], [271, 5, 298, 3], [273, 4, 300, 2], [274, 4, 301, 2], [274, 10, 301, 8, "applyStrongBlur"], [274, 25, 301, 23], [274, 28, 301, 26, "applyStrongBlur"], [274, 29, 301, 27, "ctx"], [274, 32, 301, 56], [274, 34, 301, 58, "x"], [274, 35, 301, 67], [274, 37, 301, 69, "y"], [274, 38, 301, 78], [274, 40, 301, 80, "width"], [274, 45, 301, 93], [274, 47, 301, 95, "height"], [274, 53, 301, 109], [274, 58, 301, 114], [275, 6, 302, 4], [276, 6, 303, 4], [276, 12, 303, 10, "imageData"], [276, 21, 303, 19], [276, 24, 303, 22, "ctx"], [276, 27, 303, 25], [276, 28, 303, 26, "getImageData"], [276, 40, 303, 38], [276, 41, 303, 39, "x"], [276, 42, 303, 40], [276, 44, 303, 42, "y"], [276, 45, 303, 43], [276, 47, 303, 45, "width"], [276, 52, 303, 50], [276, 54, 303, 52, "height"], [276, 60, 303, 58], [276, 61, 303, 59], [277, 6, 304, 4], [277, 12, 304, 10, "data"], [277, 16, 304, 14], [277, 19, 304, 17, "imageData"], [277, 28, 304, 26], [277, 29, 304, 27, "data"], [277, 33, 304, 31], [279, 6, 306, 4], [281, 6, 308, 4], [282, 6, 309, 4], [282, 12, 309, 10, "pixelSize"], [282, 21, 309, 19], [282, 24, 309, 22, "Math"], [282, 28, 309, 26], [282, 29, 309, 27, "max"], [282, 32, 309, 30], [282, 33, 309, 31], [282, 35, 309, 33], [282, 37, 309, 35, "Math"], [282, 41, 309, 39], [282, 42, 309, 40, "min"], [282, 45, 309, 43], [282, 46, 309, 44, "width"], [282, 51, 309, 49], [282, 53, 309, 51, "height"], [282, 59, 309, 57], [282, 60, 309, 58], [282, 63, 309, 61], [282, 64, 309, 62], [282, 65, 309, 63], [283, 6, 310, 4, "console"], [283, 13, 310, 11], [283, 14, 310, 12, "log"], [283, 17, 310, 15], [283, 18, 310, 16], [283, 77, 310, 75, "pixelSize"], [283, 86, 310, 84], [283, 90, 310, 88], [283, 91, 310, 89], [284, 6, 312, 4], [284, 11, 312, 9], [284, 15, 312, 13, "py"], [284, 17, 312, 15], [284, 20, 312, 18], [284, 21, 312, 19], [284, 23, 312, 21, "py"], [284, 25, 312, 23], [284, 28, 312, 26, "height"], [284, 34, 312, 32], [284, 36, 312, 34, "py"], [284, 38, 312, 36], [284, 42, 312, 40, "pixelSize"], [284, 51, 312, 49], [284, 53, 312, 51], [285, 8, 313, 6], [285, 13, 313, 11], [285, 17, 313, 15, "px"], [285, 19, 313, 17], [285, 22, 313, 20], [285, 23, 313, 21], [285, 25, 313, 23, "px"], [285, 27, 313, 25], [285, 30, 313, 28, "width"], [285, 35, 313, 33], [285, 37, 313, 35, "px"], [285, 39, 313, 37], [285, 43, 313, 41, "pixelSize"], [285, 52, 313, 50], [285, 54, 313, 52], [286, 10, 314, 8], [287, 10, 315, 8], [287, 14, 315, 12, "r"], [287, 15, 315, 13], [287, 18, 315, 16], [287, 19, 315, 17], [288, 12, 315, 19, "g"], [288, 13, 315, 20], [288, 16, 315, 23], [288, 17, 315, 24], [289, 12, 315, 26, "b"], [289, 13, 315, 27], [289, 16, 315, 30], [289, 17, 315, 31], [290, 12, 315, 33, "count"], [290, 17, 315, 38], [290, 20, 315, 41], [290, 21, 315, 42], [291, 10, 317, 8], [291, 15, 317, 13], [291, 19, 317, 17, "dy"], [291, 21, 317, 19], [291, 24, 317, 22], [291, 25, 317, 23], [291, 27, 317, 25, "dy"], [291, 29, 317, 27], [291, 32, 317, 30, "pixelSize"], [291, 41, 317, 39], [291, 45, 317, 43, "py"], [291, 47, 317, 45], [291, 50, 317, 48, "dy"], [291, 52, 317, 50], [291, 55, 317, 53, "height"], [291, 61, 317, 59], [291, 63, 317, 61, "dy"], [291, 65, 317, 63], [291, 67, 317, 65], [291, 69, 317, 67], [292, 12, 318, 10], [292, 17, 318, 15], [292, 21, 318, 19, "dx"], [292, 23, 318, 21], [292, 26, 318, 24], [292, 27, 318, 25], [292, 29, 318, 27, "dx"], [292, 31, 318, 29], [292, 34, 318, 32, "pixelSize"], [292, 43, 318, 41], [292, 47, 318, 45, "px"], [292, 49, 318, 47], [292, 52, 318, 50, "dx"], [292, 54, 318, 52], [292, 57, 318, 55, "width"], [292, 62, 318, 60], [292, 64, 318, 62, "dx"], [292, 66, 318, 64], [292, 68, 318, 66], [292, 70, 318, 68], [293, 14, 319, 12], [293, 20, 319, 18, "index"], [293, 25, 319, 23], [293, 28, 319, 26], [293, 29, 319, 27], [293, 30, 319, 28, "py"], [293, 32, 319, 30], [293, 35, 319, 33, "dy"], [293, 37, 319, 35], [293, 41, 319, 39, "width"], [293, 46, 319, 44], [293, 50, 319, 48, "px"], [293, 52, 319, 50], [293, 55, 319, 53, "dx"], [293, 57, 319, 55], [293, 58, 319, 56], [293, 62, 319, 60], [293, 63, 319, 61], [294, 14, 320, 12, "r"], [294, 15, 320, 13], [294, 19, 320, 17, "data"], [294, 23, 320, 21], [294, 24, 320, 22, "index"], [294, 29, 320, 27], [294, 30, 320, 28], [295, 14, 321, 12, "g"], [295, 15, 321, 13], [295, 19, 321, 17, "data"], [295, 23, 321, 21], [295, 24, 321, 22, "index"], [295, 29, 321, 27], [295, 32, 321, 30], [295, 33, 321, 31], [295, 34, 321, 32], [296, 14, 322, 12, "b"], [296, 15, 322, 13], [296, 19, 322, 17, "data"], [296, 23, 322, 21], [296, 24, 322, 22, "index"], [296, 29, 322, 27], [296, 32, 322, 30], [296, 33, 322, 31], [296, 34, 322, 32], [297, 14, 323, 12, "count"], [297, 19, 323, 17], [297, 21, 323, 19], [298, 12, 324, 10], [299, 10, 325, 8], [300, 10, 327, 8], [300, 14, 327, 12, "count"], [300, 19, 327, 17], [300, 22, 327, 20], [300, 23, 327, 21], [300, 25, 327, 23], [301, 12, 328, 10, "r"], [301, 13, 328, 11], [301, 16, 328, 14, "Math"], [301, 20, 328, 18], [301, 21, 328, 19, "floor"], [301, 26, 328, 24], [301, 27, 328, 25, "r"], [301, 28, 328, 26], [301, 31, 328, 29, "count"], [301, 36, 328, 34], [301, 37, 328, 35], [302, 12, 329, 10, "g"], [302, 13, 329, 11], [302, 16, 329, 14, "Math"], [302, 20, 329, 18], [302, 21, 329, 19, "floor"], [302, 26, 329, 24], [302, 27, 329, 25, "g"], [302, 28, 329, 26], [302, 31, 329, 29, "count"], [302, 36, 329, 34], [302, 37, 329, 35], [303, 12, 330, 10, "b"], [303, 13, 330, 11], [303, 16, 330, 14, "Math"], [303, 20, 330, 18], [303, 21, 330, 19, "floor"], [303, 26, 330, 24], [303, 27, 330, 25, "b"], [303, 28, 330, 26], [303, 31, 330, 29, "count"], [303, 36, 330, 34], [303, 37, 330, 35], [305, 12, 332, 10], [306, 12, 333, 10], [306, 17, 333, 15], [306, 21, 333, 19, "dy"], [306, 23, 333, 21], [306, 26, 333, 24], [306, 27, 333, 25], [306, 29, 333, 27, "dy"], [306, 31, 333, 29], [306, 34, 333, 32, "pixelSize"], [306, 43, 333, 41], [306, 47, 333, 45, "py"], [306, 49, 333, 47], [306, 52, 333, 50, "dy"], [306, 54, 333, 52], [306, 57, 333, 55, "height"], [306, 63, 333, 61], [306, 65, 333, 63, "dy"], [306, 67, 333, 65], [306, 69, 333, 67], [306, 71, 333, 69], [307, 14, 334, 12], [307, 19, 334, 17], [307, 23, 334, 21, "dx"], [307, 25, 334, 23], [307, 28, 334, 26], [307, 29, 334, 27], [307, 31, 334, 29, "dx"], [307, 33, 334, 31], [307, 36, 334, 34, "pixelSize"], [307, 45, 334, 43], [307, 49, 334, 47, "px"], [307, 51, 334, 49], [307, 54, 334, 52, "dx"], [307, 56, 334, 54], [307, 59, 334, 57, "width"], [307, 64, 334, 62], [307, 66, 334, 64, "dx"], [307, 68, 334, 66], [307, 70, 334, 68], [307, 72, 334, 70], [308, 16, 335, 14], [308, 22, 335, 20, "index"], [308, 27, 335, 25], [308, 30, 335, 28], [308, 31, 335, 29], [308, 32, 335, 30, "py"], [308, 34, 335, 32], [308, 37, 335, 35, "dy"], [308, 39, 335, 37], [308, 43, 335, 41, "width"], [308, 48, 335, 46], [308, 52, 335, 50, "px"], [308, 54, 335, 52], [308, 57, 335, 55, "dx"], [308, 59, 335, 57], [308, 60, 335, 58], [308, 64, 335, 62], [308, 65, 335, 63], [309, 16, 336, 14, "data"], [309, 20, 336, 18], [309, 21, 336, 19, "index"], [309, 26, 336, 24], [309, 27, 336, 25], [309, 30, 336, 28, "r"], [309, 31, 336, 29], [310, 16, 337, 14, "data"], [310, 20, 337, 18], [310, 21, 337, 19, "index"], [310, 26, 337, 24], [310, 29, 337, 27], [310, 30, 337, 28], [310, 31, 337, 29], [310, 34, 337, 32, "g"], [310, 35, 337, 33], [311, 16, 338, 14, "data"], [311, 20, 338, 18], [311, 21, 338, 19, "index"], [311, 26, 338, 24], [311, 29, 338, 27], [311, 30, 338, 28], [311, 31, 338, 29], [311, 34, 338, 32, "b"], [311, 35, 338, 33], [312, 16, 339, 14], [313, 14, 340, 12], [314, 12, 341, 10], [315, 10, 342, 8], [316, 8, 343, 6], [317, 6, 344, 4], [319, 6, 346, 4], [320, 6, 347, 4, "console"], [320, 13, 347, 11], [320, 14, 347, 12, "log"], [320, 17, 347, 15], [320, 18, 347, 16], [320, 71, 347, 69], [320, 72, 347, 70], [321, 6, 348, 4], [321, 11, 348, 9], [321, 15, 348, 13, "i"], [321, 16, 348, 14], [321, 19, 348, 17], [321, 20, 348, 18], [321, 22, 348, 20, "i"], [321, 23, 348, 21], [321, 26, 348, 24], [321, 27, 348, 25], [321, 29, 348, 27, "i"], [321, 30, 348, 28], [321, 32, 348, 30], [321, 34, 348, 32], [322, 8, 348, 34], [323, 8, 349, 6, "applySimpleBlur"], [323, 23, 349, 21], [323, 24, 349, 22, "data"], [323, 28, 349, 26], [323, 30, 349, 28, "width"], [323, 35, 349, 33], [323, 37, 349, 35, "height"], [323, 43, 349, 41], [323, 44, 349, 42], [324, 6, 350, 4], [326, 6, 352, 4], [327, 6, 353, 4, "ctx"], [327, 9, 353, 7], [327, 10, 353, 8, "putImageData"], [327, 22, 353, 20], [327, 23, 353, 21, "imageData"], [327, 32, 353, 30], [327, 34, 353, 32, "x"], [327, 35, 353, 33], [327, 37, 353, 35, "y"], [327, 38, 353, 36], [327, 39, 353, 37], [328, 4, 354, 2], [328, 5, 354, 3], [329, 4, 356, 2], [329, 10, 356, 8, "applySimpleBlur"], [329, 25, 356, 23], [329, 28, 356, 26, "applySimpleBlur"], [329, 29, 356, 27, "data"], [329, 33, 356, 50], [329, 35, 356, 52, "width"], [329, 40, 356, 65], [329, 42, 356, 67, "height"], [329, 48, 356, 81], [329, 53, 356, 86], [330, 6, 357, 4], [330, 12, 357, 10, "original"], [330, 20, 357, 18], [330, 23, 357, 21], [330, 27, 357, 25, "Uint8ClampedArray"], [330, 44, 357, 42], [330, 45, 357, 43, "data"], [330, 49, 357, 47], [330, 50, 357, 48], [331, 6, 359, 4], [331, 11, 359, 9], [331, 15, 359, 13, "y"], [331, 16, 359, 14], [331, 19, 359, 17], [331, 20, 359, 18], [331, 22, 359, 20, "y"], [331, 23, 359, 21], [331, 26, 359, 24, "height"], [331, 32, 359, 30], [331, 35, 359, 33], [331, 36, 359, 34], [331, 38, 359, 36, "y"], [331, 39, 359, 37], [331, 41, 359, 39], [331, 43, 359, 41], [332, 8, 360, 6], [332, 13, 360, 11], [332, 17, 360, 15, "x"], [332, 18, 360, 16], [332, 21, 360, 19], [332, 22, 360, 20], [332, 24, 360, 22, "x"], [332, 25, 360, 23], [332, 28, 360, 26, "width"], [332, 33, 360, 31], [332, 36, 360, 34], [332, 37, 360, 35], [332, 39, 360, 37, "x"], [332, 40, 360, 38], [332, 42, 360, 40], [332, 44, 360, 42], [333, 10, 361, 8], [333, 16, 361, 14, "index"], [333, 21, 361, 19], [333, 24, 361, 22], [333, 25, 361, 23, "y"], [333, 26, 361, 24], [333, 29, 361, 27, "width"], [333, 34, 361, 32], [333, 37, 361, 35, "x"], [333, 38, 361, 36], [333, 42, 361, 40], [333, 43, 361, 41], [335, 10, 363, 8], [336, 10, 364, 8], [336, 14, 364, 12, "r"], [336, 15, 364, 13], [336, 18, 364, 16], [336, 19, 364, 17], [337, 12, 364, 19, "g"], [337, 13, 364, 20], [337, 16, 364, 23], [337, 17, 364, 24], [338, 12, 364, 26, "b"], [338, 13, 364, 27], [338, 16, 364, 30], [338, 17, 364, 31], [339, 10, 365, 8], [339, 15, 365, 13], [339, 19, 365, 17, "dy"], [339, 21, 365, 19], [339, 24, 365, 22], [339, 25, 365, 23], [339, 26, 365, 24], [339, 28, 365, 26, "dy"], [339, 30, 365, 28], [339, 34, 365, 32], [339, 35, 365, 33], [339, 37, 365, 35, "dy"], [339, 39, 365, 37], [339, 41, 365, 39], [339, 43, 365, 41], [340, 12, 366, 10], [340, 17, 366, 15], [340, 21, 366, 19, "dx"], [340, 23, 366, 21], [340, 26, 366, 24], [340, 27, 366, 25], [340, 28, 366, 26], [340, 30, 366, 28, "dx"], [340, 32, 366, 30], [340, 36, 366, 34], [340, 37, 366, 35], [340, 39, 366, 37, "dx"], [340, 41, 366, 39], [340, 43, 366, 41], [340, 45, 366, 43], [341, 14, 367, 12], [341, 20, 367, 18, "neighborIndex"], [341, 33, 367, 31], [341, 36, 367, 34], [341, 37, 367, 35], [341, 38, 367, 36, "y"], [341, 39, 367, 37], [341, 42, 367, 40, "dy"], [341, 44, 367, 42], [341, 48, 367, 46, "width"], [341, 53, 367, 51], [341, 57, 367, 55, "x"], [341, 58, 367, 56], [341, 61, 367, 59, "dx"], [341, 63, 367, 61], [341, 64, 367, 62], [341, 68, 367, 66], [341, 69, 367, 67], [342, 14, 368, 12, "r"], [342, 15, 368, 13], [342, 19, 368, 17, "original"], [342, 27, 368, 25], [342, 28, 368, 26, "neighborIndex"], [342, 41, 368, 39], [342, 42, 368, 40], [343, 14, 369, 12, "g"], [343, 15, 369, 13], [343, 19, 369, 17, "original"], [343, 27, 369, 25], [343, 28, 369, 26, "neighborIndex"], [343, 41, 369, 39], [343, 44, 369, 42], [343, 45, 369, 43], [343, 46, 369, 44], [344, 14, 370, 12, "b"], [344, 15, 370, 13], [344, 19, 370, 17, "original"], [344, 27, 370, 25], [344, 28, 370, 26, "neighborIndex"], [344, 41, 370, 39], [344, 44, 370, 42], [344, 45, 370, 43], [344, 46, 370, 44], [345, 12, 371, 10], [346, 10, 372, 8], [347, 10, 374, 8, "data"], [347, 14, 374, 12], [347, 15, 374, 13, "index"], [347, 20, 374, 18], [347, 21, 374, 19], [347, 24, 374, 22, "r"], [347, 25, 374, 23], [347, 28, 374, 26], [347, 29, 374, 27], [348, 10, 375, 8, "data"], [348, 14, 375, 12], [348, 15, 375, 13, "index"], [348, 20, 375, 18], [348, 23, 375, 21], [348, 24, 375, 22], [348, 25, 375, 23], [348, 28, 375, 26, "g"], [348, 29, 375, 27], [348, 32, 375, 30], [348, 33, 375, 31], [349, 10, 376, 8, "data"], [349, 14, 376, 12], [349, 15, 376, 13, "index"], [349, 20, 376, 18], [349, 23, 376, 21], [349, 24, 376, 22], [349, 25, 376, 23], [349, 28, 376, 26, "b"], [349, 29, 376, 27], [349, 32, 376, 30], [349, 33, 376, 31], [350, 8, 377, 6], [351, 6, 378, 4], [352, 4, 379, 2], [352, 5, 379, 3], [353, 4, 381, 2], [353, 10, 381, 8, "applyFallbackFaceBlur"], [353, 31, 381, 29], [353, 34, 381, 32, "applyFallbackFaceBlur"], [353, 35, 381, 33, "ctx"], [353, 38, 381, 62], [353, 40, 381, 64, "imgWidth"], [353, 48, 381, 80], [353, 50, 381, 82, "imgHeight"], [353, 59, 381, 99], [353, 64, 381, 104], [354, 6, 382, 4, "console"], [354, 13, 382, 11], [354, 14, 382, 12, "log"], [354, 17, 382, 15], [354, 18, 382, 16], [354, 90, 382, 88], [354, 91, 382, 89], [356, 6, 384, 4], [357, 6, 385, 4], [357, 12, 385, 10, "areas"], [357, 17, 385, 15], [357, 20, 385, 18], [358, 6, 386, 6], [359, 6, 387, 6], [360, 8, 387, 8, "x"], [360, 9, 387, 9], [360, 11, 387, 11, "imgWidth"], [360, 19, 387, 19], [360, 22, 387, 22], [360, 26, 387, 26], [361, 8, 387, 28, "y"], [361, 9, 387, 29], [361, 11, 387, 31, "imgHeight"], [361, 20, 387, 40], [361, 23, 387, 43], [361, 27, 387, 47], [362, 8, 387, 49, "w"], [362, 9, 387, 50], [362, 11, 387, 52, "imgWidth"], [362, 19, 387, 60], [362, 22, 387, 63], [362, 25, 387, 66], [363, 8, 387, 68, "h"], [363, 9, 387, 69], [363, 11, 387, 71, "imgHeight"], [363, 20, 387, 80], [363, 23, 387, 83], [364, 6, 387, 87], [364, 7, 387, 88], [365, 6, 388, 6], [366, 6, 389, 6], [367, 8, 389, 8, "x"], [367, 9, 389, 9], [367, 11, 389, 11, "imgWidth"], [367, 19, 389, 19], [367, 22, 389, 22], [367, 25, 389, 25], [368, 8, 389, 27, "y"], [368, 9, 389, 28], [368, 11, 389, 30, "imgHeight"], [368, 20, 389, 39], [368, 23, 389, 42], [368, 26, 389, 45], [369, 8, 389, 47, "w"], [369, 9, 389, 48], [369, 11, 389, 50, "imgWidth"], [369, 19, 389, 58], [369, 22, 389, 61], [369, 26, 389, 65], [370, 8, 389, 67, "h"], [370, 9, 389, 68], [370, 11, 389, 70, "imgHeight"], [370, 20, 389, 79], [370, 23, 389, 82], [371, 6, 389, 86], [371, 7, 389, 87], [372, 6, 390, 6], [373, 6, 391, 6], [374, 8, 391, 8, "x"], [374, 9, 391, 9], [374, 11, 391, 11, "imgWidth"], [374, 19, 391, 19], [374, 22, 391, 22], [374, 26, 391, 26], [375, 8, 391, 28, "y"], [375, 9, 391, 29], [375, 11, 391, 31, "imgHeight"], [375, 20, 391, 40], [375, 23, 391, 43], [375, 26, 391, 46], [376, 8, 391, 48, "w"], [376, 9, 391, 49], [376, 11, 391, 51, "imgWidth"], [376, 19, 391, 59], [376, 22, 391, 62], [376, 26, 391, 66], [377, 8, 391, 68, "h"], [377, 9, 391, 69], [377, 11, 391, 71, "imgHeight"], [377, 20, 391, 80], [377, 23, 391, 83], [378, 6, 391, 87], [378, 7, 391, 88], [378, 8, 392, 5], [379, 6, 394, 4, "areas"], [379, 11, 394, 9], [379, 12, 394, 10, "for<PERSON>ach"], [379, 19, 394, 17], [379, 20, 394, 18], [379, 21, 394, 19, "area"], [379, 25, 394, 23], [379, 27, 394, 25, "index"], [379, 32, 394, 30], [379, 37, 394, 35], [380, 8, 395, 6, "console"], [380, 15, 395, 13], [380, 16, 395, 14, "log"], [380, 19, 395, 17], [380, 20, 395, 18], [380, 65, 395, 63, "index"], [380, 70, 395, 68], [380, 73, 395, 71], [380, 74, 395, 72], [380, 77, 395, 75], [380, 79, 395, 77, "area"], [380, 83, 395, 81], [380, 84, 395, 82], [381, 8, 396, 6, "applyStrongBlur"], [381, 23, 396, 21], [381, 24, 396, 22, "ctx"], [381, 27, 396, 25], [381, 29, 396, 27, "area"], [381, 33, 396, 31], [381, 34, 396, 32, "x"], [381, 35, 396, 33], [381, 37, 396, 35, "area"], [381, 41, 396, 39], [381, 42, 396, 40, "y"], [381, 43, 396, 41], [381, 45, 396, 43, "area"], [381, 49, 396, 47], [381, 50, 396, 48, "w"], [381, 51, 396, 49], [381, 53, 396, 51, "area"], [381, 57, 396, 55], [381, 58, 396, 56, "h"], [381, 59, 396, 57], [381, 60, 396, 58], [382, 6, 397, 4], [382, 7, 397, 5], [382, 8, 397, 6], [383, 4, 398, 2], [383, 5, 398, 3], [385, 4, 400, 2], [386, 4, 401, 2], [386, 10, 401, 8, "capturePhoto"], [386, 22, 401, 20], [386, 25, 401, 23], [386, 29, 401, 23, "useCallback"], [386, 47, 401, 34], [386, 49, 401, 35], [386, 61, 401, 47], [387, 6, 402, 4], [388, 6, 403, 4], [388, 12, 403, 10, "isDev"], [388, 17, 403, 15], [388, 20, 403, 18, "process"], [388, 27, 403, 25], [388, 28, 403, 26, "env"], [388, 31, 403, 29], [388, 32, 403, 30, "NODE_ENV"], [388, 40, 403, 38], [388, 45, 403, 43], [388, 58, 403, 56], [388, 62, 403, 60, "__DEV__"], [388, 69, 403, 67], [389, 6, 405, 4], [389, 10, 405, 8], [389, 11, 405, 9, "cameraRef"], [389, 20, 405, 18], [389, 21, 405, 19, "current"], [389, 28, 405, 26], [389, 32, 405, 30], [389, 33, 405, 31, "isDev"], [389, 38, 405, 36], [389, 40, 405, 38], [390, 8, 406, 6, "<PERSON><PERSON>"], [390, 22, 406, 11], [390, 23, 406, 12, "alert"], [390, 28, 406, 17], [390, 29, 406, 18], [390, 36, 406, 25], [390, 38, 406, 27], [390, 56, 406, 45], [390, 57, 406, 46], [391, 8, 407, 6], [392, 6, 408, 4], [393, 6, 409, 4], [393, 10, 409, 8], [394, 8, 410, 6, "setProcessingState"], [394, 26, 410, 24], [394, 27, 410, 25], [394, 38, 410, 36], [394, 39, 410, 37], [395, 8, 411, 6, "setProcessingProgress"], [395, 29, 411, 27], [395, 30, 411, 28], [395, 32, 411, 30], [395, 33, 411, 31], [396, 8, 412, 6], [397, 8, 413, 6], [398, 8, 414, 6], [399, 8, 415, 6], [399, 14, 415, 12], [399, 18, 415, 16, "Promise"], [399, 25, 415, 23], [399, 26, 415, 24, "resolve"], [399, 33, 415, 31], [399, 37, 415, 35, "setTimeout"], [399, 47, 415, 45], [399, 48, 415, 46, "resolve"], [399, 55, 415, 53], [399, 57, 415, 55], [399, 59, 415, 57], [399, 60, 415, 58], [399, 61, 415, 59], [400, 8, 416, 6], [401, 8, 417, 6], [401, 12, 417, 10, "photo"], [401, 17, 417, 15], [402, 8, 419, 6], [402, 12, 419, 10], [403, 10, 420, 8, "photo"], [403, 15, 420, 13], [403, 18, 420, 16], [403, 24, 420, 22, "cameraRef"], [403, 33, 420, 31], [403, 34, 420, 32, "current"], [403, 41, 420, 39], [403, 42, 420, 40, "takePictureAsync"], [403, 58, 420, 56], [403, 59, 420, 57], [404, 12, 421, 10, "quality"], [404, 19, 421, 17], [404, 21, 421, 19], [404, 24, 421, 22], [405, 12, 422, 10, "base64"], [405, 18, 422, 16], [405, 20, 422, 18], [405, 25, 422, 23], [406, 12, 423, 10, "skipProcessing"], [406, 26, 423, 24], [406, 28, 423, 26], [406, 32, 423, 30], [406, 33, 423, 32], [407, 10, 424, 8], [407, 11, 424, 9], [407, 12, 424, 10], [408, 8, 425, 6], [408, 9, 425, 7], [408, 10, 425, 8], [408, 17, 425, 15, "cameraError"], [408, 28, 425, 26], [408, 30, 425, 28], [409, 10, 426, 8, "console"], [409, 17, 426, 15], [409, 18, 426, 16, "log"], [409, 21, 426, 19], [409, 22, 426, 20], [409, 82, 426, 80], [409, 84, 426, 82, "cameraError"], [409, 95, 426, 93], [409, 96, 426, 94], [410, 10, 427, 8], [411, 10, 428, 8], [411, 14, 428, 12, "isDev"], [411, 19, 428, 17], [411, 21, 428, 19], [412, 12, 429, 10, "photo"], [412, 17, 429, 15], [412, 20, 429, 18], [413, 14, 430, 12, "uri"], [413, 17, 430, 15], [413, 19, 430, 17], [414, 12, 431, 10], [414, 13, 431, 11], [415, 10, 432, 8], [415, 11, 432, 9], [415, 17, 432, 15], [416, 12, 433, 10], [416, 18, 433, 16, "cameraError"], [416, 29, 433, 27], [417, 10, 434, 8], [418, 8, 435, 6], [419, 8, 436, 6], [419, 12, 436, 10], [419, 13, 436, 11, "photo"], [419, 18, 436, 16], [419, 20, 436, 18], [420, 10, 437, 8], [420, 16, 437, 14], [420, 20, 437, 18, "Error"], [420, 25, 437, 23], [420, 26, 437, 24], [420, 51, 437, 49], [420, 52, 437, 50], [421, 8, 438, 6], [422, 8, 439, 6, "console"], [422, 15, 439, 13], [422, 16, 439, 14, "log"], [422, 19, 439, 17], [422, 20, 439, 18], [422, 56, 439, 54], [422, 58, 439, 56, "photo"], [422, 63, 439, 61], [422, 64, 439, 62, "uri"], [422, 67, 439, 65], [422, 68, 439, 66], [423, 8, 440, 6, "setCapturedPhoto"], [423, 24, 440, 22], [423, 25, 440, 23, "photo"], [423, 30, 440, 28], [423, 31, 440, 29, "uri"], [423, 34, 440, 32], [423, 35, 440, 33], [424, 8, 441, 6, "setProcessingProgress"], [424, 29, 441, 27], [424, 30, 441, 28], [424, 32, 441, 30], [424, 33, 441, 31], [425, 8, 442, 6], [426, 8, 443, 6, "console"], [426, 15, 443, 13], [426, 16, 443, 14, "log"], [426, 19, 443, 17], [426, 20, 443, 18], [426, 73, 443, 71], [426, 74, 443, 72], [427, 8, 444, 6], [427, 14, 444, 12, "processImageWithFaceBlur"], [427, 38, 444, 36], [427, 39, 444, 37, "photo"], [427, 44, 444, 42], [427, 45, 444, 43, "uri"], [427, 48, 444, 46], [427, 49, 444, 47], [428, 8, 445, 6, "console"], [428, 15, 445, 13], [428, 16, 445, 14, "log"], [428, 19, 445, 17], [428, 20, 445, 18], [428, 71, 445, 69], [428, 72, 445, 70], [429, 6, 446, 4], [429, 7, 446, 5], [429, 8, 446, 6], [429, 15, 446, 13, "error"], [429, 20, 446, 18], [429, 22, 446, 20], [430, 8, 447, 6, "console"], [430, 15, 447, 13], [430, 16, 447, 14, "error"], [430, 21, 447, 19], [430, 22, 447, 20], [430, 54, 447, 52], [430, 56, 447, 54, "error"], [430, 61, 447, 59], [430, 62, 447, 60], [431, 8, 448, 6, "setErrorMessage"], [431, 23, 448, 21], [431, 24, 448, 22], [431, 68, 448, 66], [431, 69, 448, 67], [432, 8, 449, 6, "setProcessingState"], [432, 26, 449, 24], [432, 27, 449, 25], [432, 34, 449, 32], [432, 35, 449, 33], [433, 6, 450, 4], [434, 4, 451, 2], [434, 5, 451, 3], [434, 7, 451, 5], [434, 9, 451, 7], [434, 10, 451, 8], [435, 4, 452, 2], [436, 4, 453, 2], [436, 10, 453, 8, "processImageWithFaceBlur"], [436, 34, 453, 32], [436, 37, 453, 35], [436, 43, 453, 42, "photoUri"], [436, 51, 453, 58], [436, 55, 453, 63], [437, 6, 454, 4], [437, 10, 454, 8], [438, 8, 455, 6, "console"], [438, 15, 455, 13], [438, 16, 455, 14, "log"], [438, 19, 455, 17], [438, 20, 455, 18], [438, 84, 455, 82], [438, 85, 455, 83], [439, 8, 456, 6, "setProcessingState"], [439, 26, 456, 24], [439, 27, 456, 25], [439, 39, 456, 37], [439, 40, 456, 38], [440, 8, 457, 6, "setProcessingProgress"], [440, 29, 457, 27], [440, 30, 457, 28], [440, 32, 457, 30], [440, 33, 457, 31], [442, 8, 459, 6], [443, 8, 460, 6], [443, 14, 460, 12, "canvas"], [443, 20, 460, 18], [443, 23, 460, 21, "document"], [443, 31, 460, 29], [443, 32, 460, 30, "createElement"], [443, 45, 460, 43], [443, 46, 460, 44], [443, 54, 460, 52], [443, 55, 460, 53], [444, 8, 461, 6], [444, 14, 461, 12, "ctx"], [444, 17, 461, 15], [444, 20, 461, 18, "canvas"], [444, 26, 461, 24], [444, 27, 461, 25, "getContext"], [444, 37, 461, 35], [444, 38, 461, 36], [444, 42, 461, 40], [444, 43, 461, 41], [445, 8, 462, 6], [445, 12, 462, 10], [445, 13, 462, 11, "ctx"], [445, 16, 462, 14], [445, 18, 462, 16], [445, 24, 462, 22], [445, 28, 462, 26, "Error"], [445, 33, 462, 31], [445, 34, 462, 32], [445, 64, 462, 62], [445, 65, 462, 63], [447, 8, 464, 6], [448, 8, 465, 6], [448, 14, 465, 12, "img"], [448, 17, 465, 15], [448, 20, 465, 18], [448, 24, 465, 22, "Image"], [448, 29, 465, 27], [448, 30, 465, 28], [448, 31, 465, 29], [449, 8, 466, 6], [449, 14, 466, 12], [449, 18, 466, 16, "Promise"], [449, 25, 466, 23], [449, 26, 466, 24], [449, 27, 466, 25, "resolve"], [449, 34, 466, 32], [449, 36, 466, 34, "reject"], [449, 42, 466, 40], [449, 47, 466, 45], [450, 10, 467, 8, "img"], [450, 13, 467, 11], [450, 14, 467, 12, "onload"], [450, 20, 467, 18], [450, 23, 467, 21, "resolve"], [450, 30, 467, 28], [451, 10, 468, 8, "img"], [451, 13, 468, 11], [451, 14, 468, 12, "onerror"], [451, 21, 468, 19], [451, 24, 468, 22, "reject"], [451, 30, 468, 28], [452, 10, 469, 8, "img"], [452, 13, 469, 11], [452, 14, 469, 12, "src"], [452, 17, 469, 15], [452, 20, 469, 18, "photoUri"], [452, 28, 469, 26], [453, 8, 470, 6], [453, 9, 470, 7], [453, 10, 470, 8], [455, 8, 472, 6], [456, 8, 473, 6, "canvas"], [456, 14, 473, 12], [456, 15, 473, 13, "width"], [456, 20, 473, 18], [456, 23, 473, 21, "img"], [456, 26, 473, 24], [456, 27, 473, 25, "width"], [456, 32, 473, 30], [457, 8, 474, 6, "canvas"], [457, 14, 474, 12], [457, 15, 474, 13, "height"], [457, 21, 474, 19], [457, 24, 474, 22, "img"], [457, 27, 474, 25], [457, 28, 474, 26, "height"], [457, 34, 474, 32], [458, 8, 475, 6, "console"], [458, 15, 475, 13], [458, 16, 475, 14, "log"], [458, 19, 475, 17], [458, 20, 475, 18], [458, 54, 475, 52], [458, 56, 475, 54], [459, 10, 475, 56, "width"], [459, 15, 475, 61], [459, 17, 475, 63, "img"], [459, 20, 475, 66], [459, 21, 475, 67, "width"], [459, 26, 475, 72], [460, 10, 475, 74, "height"], [460, 16, 475, 80], [460, 18, 475, 82, "img"], [460, 21, 475, 85], [460, 22, 475, 86, "height"], [461, 8, 475, 93], [461, 9, 475, 94], [461, 10, 475, 95], [463, 8, 477, 6], [464, 8, 478, 6, "ctx"], [464, 11, 478, 9], [464, 12, 478, 10, "drawImage"], [464, 21, 478, 19], [464, 22, 478, 20, "img"], [464, 25, 478, 23], [464, 27, 478, 25], [464, 28, 478, 26], [464, 30, 478, 28], [464, 31, 478, 29], [464, 32, 478, 30], [465, 8, 479, 6, "console"], [465, 15, 479, 13], [465, 16, 479, 14, "log"], [465, 19, 479, 17], [465, 20, 479, 18], [465, 72, 479, 70], [465, 73, 479, 71], [466, 8, 481, 6, "setProcessingProgress"], [466, 29, 481, 27], [466, 30, 481, 28], [466, 32, 481, 30], [466, 33, 481, 31], [468, 8, 483, 6], [469, 8, 484, 6], [469, 12, 484, 10, "detectedFaces"], [469, 25, 484, 23], [469, 28, 484, 26], [469, 30, 484, 28], [470, 8, 486, 6, "console"], [470, 15, 486, 13], [470, 16, 486, 14, "log"], [470, 19, 486, 17], [470, 20, 486, 18], [470, 81, 486, 79], [470, 82, 486, 80], [472, 8, 488, 6], [473, 8, 489, 6], [473, 12, 489, 10], [474, 10, 490, 8], [474, 16, 490, 14, "loadTensorFlowFaceDetection"], [474, 43, 490, 41], [474, 44, 490, 42], [474, 45, 490, 43], [475, 10, 491, 8, "detectedFaces"], [475, 23, 491, 21], [475, 26, 491, 24], [475, 32, 491, 30, "detectFacesWithTensorFlow"], [475, 57, 491, 55], [475, 58, 491, 56, "img"], [475, 61, 491, 59], [475, 62, 491, 60], [476, 10, 492, 8, "console"], [476, 17, 492, 15], [476, 18, 492, 16, "log"], [476, 21, 492, 19], [476, 22, 492, 20], [476, 70, 492, 68, "detectedFaces"], [476, 83, 492, 81], [476, 84, 492, 82, "length"], [476, 90, 492, 88], [476, 98, 492, 96], [476, 99, 492, 97], [477, 8, 493, 6], [477, 9, 493, 7], [477, 10, 493, 8], [477, 17, 493, 15, "tensorFlowError"], [477, 32, 493, 30], [477, 34, 493, 32], [478, 10, 494, 8, "console"], [478, 17, 494, 15], [478, 18, 494, 16, "warn"], [478, 22, 494, 20], [478, 23, 494, 21], [478, 61, 494, 59], [478, 63, 494, 61, "tensorFlowError"], [478, 78, 494, 76], [478, 79, 494, 77], [480, 10, 496, 8], [481, 10, 497, 8, "console"], [481, 17, 497, 15], [481, 18, 497, 16, "log"], [481, 21, 497, 19], [481, 22, 497, 20], [481, 86, 497, 84], [481, 87, 497, 85], [482, 10, 498, 8, "detectedFaces"], [482, 23, 498, 21], [482, 26, 498, 24, "detectFacesHeuristic"], [482, 46, 498, 44], [482, 47, 498, 45, "img"], [482, 50, 498, 48], [482, 52, 498, 50, "ctx"], [482, 55, 498, 53], [482, 56, 498, 54], [483, 10, 499, 8, "console"], [483, 17, 499, 15], [483, 18, 499, 16, "log"], [483, 21, 499, 19], [483, 22, 499, 20], [483, 70, 499, 68, "detectedFaces"], [483, 83, 499, 81], [483, 84, 499, 82, "length"], [483, 90, 499, 88], [483, 98, 499, 96], [483, 99, 499, 97], [484, 8, 500, 6], [485, 8, 502, 6, "console"], [485, 15, 502, 13], [485, 16, 502, 14, "log"], [485, 19, 502, 17], [485, 20, 502, 18], [485, 72, 502, 70, "detectedFaces"], [485, 85, 502, 83], [485, 86, 502, 84, "length"], [485, 92, 502, 90], [485, 100, 502, 98], [485, 101, 502, 99], [486, 8, 503, 6], [486, 12, 503, 10, "detectedFaces"], [486, 25, 503, 23], [486, 26, 503, 24, "length"], [486, 32, 503, 30], [486, 35, 503, 33], [486, 36, 503, 34], [486, 38, 503, 36], [487, 10, 504, 8, "console"], [487, 17, 504, 15], [487, 18, 504, 16, "log"], [487, 21, 504, 19], [487, 22, 504, 20], [487, 66, 504, 64], [487, 68, 504, 66, "detectedFaces"], [487, 81, 504, 79], [487, 82, 504, 80, "map"], [487, 85, 504, 83], [487, 86, 504, 84], [487, 87, 504, 85, "face"], [487, 91, 504, 89], [487, 93, 504, 91, "i"], [487, 94, 504, 92], [487, 100, 504, 98], [488, 12, 505, 10, "faceNumber"], [488, 22, 505, 20], [488, 24, 505, 22, "i"], [488, 25, 505, 23], [488, 28, 505, 26], [488, 29, 505, 27], [489, 12, 506, 10, "centerX"], [489, 19, 506, 17], [489, 21, 506, 19, "face"], [489, 25, 506, 23], [489, 26, 506, 24, "boundingBox"], [489, 37, 506, 35], [489, 38, 506, 36, "xCenter"], [489, 45, 506, 43], [490, 12, 507, 10, "centerY"], [490, 19, 507, 17], [490, 21, 507, 19, "face"], [490, 25, 507, 23], [490, 26, 507, 24, "boundingBox"], [490, 37, 507, 35], [490, 38, 507, 36, "yCenter"], [490, 45, 507, 43], [491, 12, 508, 10, "width"], [491, 17, 508, 15], [491, 19, 508, 17, "face"], [491, 23, 508, 21], [491, 24, 508, 22, "boundingBox"], [491, 35, 508, 33], [491, 36, 508, 34, "width"], [491, 41, 508, 39], [492, 12, 509, 10, "height"], [492, 18, 509, 16], [492, 20, 509, 18, "face"], [492, 24, 509, 22], [492, 25, 509, 23, "boundingBox"], [492, 36, 509, 34], [492, 37, 509, 35, "height"], [493, 10, 510, 8], [493, 11, 510, 9], [493, 12, 510, 10], [493, 13, 510, 11], [493, 14, 510, 12], [494, 8, 511, 6], [494, 9, 511, 7], [494, 15, 511, 13], [495, 10, 512, 8, "console"], [495, 17, 512, 15], [495, 18, 512, 16, "log"], [495, 21, 512, 19], [495, 22, 512, 20], [495, 91, 512, 89], [495, 92, 512, 90], [496, 8, 513, 6], [497, 8, 515, 6, "setProcessingProgress"], [497, 29, 515, 27], [497, 30, 515, 28], [497, 32, 515, 30], [497, 33, 515, 31], [499, 8, 517, 6], [500, 8, 518, 6], [500, 12, 518, 10, "detectedFaces"], [500, 25, 518, 23], [500, 26, 518, 24, "length"], [500, 32, 518, 30], [500, 35, 518, 33], [500, 36, 518, 34], [500, 38, 518, 36], [501, 10, 519, 8, "console"], [501, 17, 519, 15], [501, 18, 519, 16, "log"], [501, 21, 519, 19], [501, 22, 519, 20], [501, 61, 519, 59, "detectedFaces"], [501, 74, 519, 72], [501, 75, 519, 73, "length"], [501, 81, 519, 79], [501, 101, 519, 99], [501, 102, 519, 100], [502, 10, 521, 8, "detectedFaces"], [502, 23, 521, 21], [502, 24, 521, 22, "for<PERSON>ach"], [502, 31, 521, 29], [502, 32, 521, 30], [502, 33, 521, 31, "detection"], [502, 42, 521, 40], [502, 44, 521, 42, "index"], [502, 49, 521, 47], [502, 54, 521, 52], [503, 12, 522, 10], [503, 18, 522, 16, "bbox"], [503, 22, 522, 20], [503, 25, 522, 23, "detection"], [503, 34, 522, 32], [503, 35, 522, 33, "boundingBox"], [503, 46, 522, 44], [505, 12, 524, 10], [506, 12, 525, 10], [506, 18, 525, 16, "faceX"], [506, 23, 525, 21], [506, 26, 525, 24, "bbox"], [506, 30, 525, 28], [506, 31, 525, 29, "xCenter"], [506, 38, 525, 36], [506, 41, 525, 39, "img"], [506, 44, 525, 42], [506, 45, 525, 43, "width"], [506, 50, 525, 48], [506, 53, 525, 52, "bbox"], [506, 57, 525, 56], [506, 58, 525, 57, "width"], [506, 63, 525, 62], [506, 66, 525, 65, "img"], [506, 69, 525, 68], [506, 70, 525, 69, "width"], [506, 75, 525, 74], [506, 78, 525, 78], [506, 79, 525, 79], [507, 12, 526, 10], [507, 18, 526, 16, "faceY"], [507, 23, 526, 21], [507, 26, 526, 24, "bbox"], [507, 30, 526, 28], [507, 31, 526, 29, "yCenter"], [507, 38, 526, 36], [507, 41, 526, 39, "img"], [507, 44, 526, 42], [507, 45, 526, 43, "height"], [507, 51, 526, 49], [507, 54, 526, 53, "bbox"], [507, 58, 526, 57], [507, 59, 526, 58, "height"], [507, 65, 526, 64], [507, 68, 526, 67, "img"], [507, 71, 526, 70], [507, 72, 526, 71, "height"], [507, 78, 526, 77], [507, 81, 526, 81], [507, 82, 526, 82], [508, 12, 527, 10], [508, 18, 527, 16, "faceWidth"], [508, 27, 527, 25], [508, 30, 527, 28, "bbox"], [508, 34, 527, 32], [508, 35, 527, 33, "width"], [508, 40, 527, 38], [508, 43, 527, 41, "img"], [508, 46, 527, 44], [508, 47, 527, 45, "width"], [508, 52, 527, 50], [509, 12, 528, 10], [509, 18, 528, 16, "faceHeight"], [509, 28, 528, 26], [509, 31, 528, 29, "bbox"], [509, 35, 528, 33], [509, 36, 528, 34, "height"], [509, 42, 528, 40], [509, 45, 528, 43, "img"], [509, 48, 528, 46], [509, 49, 528, 47, "height"], [509, 55, 528, 53], [511, 12, 530, 10], [512, 12, 531, 10], [512, 18, 531, 16, "padding"], [512, 25, 531, 23], [512, 28, 531, 26], [512, 31, 531, 29], [513, 12, 532, 10], [513, 18, 532, 16, "paddedX"], [513, 25, 532, 23], [513, 28, 532, 26, "Math"], [513, 32, 532, 30], [513, 33, 532, 31, "max"], [513, 36, 532, 34], [513, 37, 532, 35], [513, 38, 532, 36], [513, 40, 532, 38, "faceX"], [513, 45, 532, 43], [513, 48, 532, 46, "faceWidth"], [513, 57, 532, 55], [513, 60, 532, 58, "padding"], [513, 67, 532, 65], [513, 68, 532, 66], [514, 12, 533, 10], [514, 18, 533, 16, "paddedY"], [514, 25, 533, 23], [514, 28, 533, 26, "Math"], [514, 32, 533, 30], [514, 33, 533, 31, "max"], [514, 36, 533, 34], [514, 37, 533, 35], [514, 38, 533, 36], [514, 40, 533, 38, "faceY"], [514, 45, 533, 43], [514, 48, 533, 46, "faceHeight"], [514, 58, 533, 56], [514, 61, 533, 59, "padding"], [514, 68, 533, 66], [514, 69, 533, 67], [515, 12, 534, 10], [515, 18, 534, 16, "<PERSON><PERSON><PERSON><PERSON>"], [515, 29, 534, 27], [515, 32, 534, 30, "Math"], [515, 36, 534, 34], [515, 37, 534, 35, "min"], [515, 40, 534, 38], [515, 41, 534, 39, "img"], [515, 44, 534, 42], [515, 45, 534, 43, "width"], [515, 50, 534, 48], [515, 53, 534, 51, "paddedX"], [515, 60, 534, 58], [515, 62, 534, 60, "faceWidth"], [515, 71, 534, 69], [515, 75, 534, 73], [515, 76, 534, 74], [515, 79, 534, 77], [515, 80, 534, 78], [515, 83, 534, 81, "padding"], [515, 90, 534, 88], [515, 91, 534, 89], [515, 92, 534, 90], [516, 12, 535, 10], [516, 18, 535, 16, "paddedHeight"], [516, 30, 535, 28], [516, 33, 535, 31, "Math"], [516, 37, 535, 35], [516, 38, 535, 36, "min"], [516, 41, 535, 39], [516, 42, 535, 40, "img"], [516, 45, 535, 43], [516, 46, 535, 44, "height"], [516, 52, 535, 50], [516, 55, 535, 53, "paddedY"], [516, 62, 535, 60], [516, 64, 535, 62, "faceHeight"], [516, 74, 535, 72], [516, 78, 535, 76], [516, 79, 535, 77], [516, 82, 535, 80], [516, 83, 535, 81], [516, 86, 535, 84, "padding"], [516, 93, 535, 91], [516, 94, 535, 92], [516, 95, 535, 93], [517, 12, 537, 10, "console"], [517, 19, 537, 17], [517, 20, 537, 18, "log"], [517, 23, 537, 21], [517, 24, 537, 22], [517, 60, 537, 58, "index"], [517, 65, 537, 63], [517, 68, 537, 66], [517, 69, 537, 67], [517, 72, 537, 70], [517, 74, 537, 72], [518, 14, 538, 12, "original"], [518, 22, 538, 20], [518, 24, 538, 22], [519, 16, 538, 24, "x"], [519, 17, 538, 25], [519, 19, 538, 27, "Math"], [519, 23, 538, 31], [519, 24, 538, 32, "round"], [519, 29, 538, 37], [519, 30, 538, 38, "faceX"], [519, 35, 538, 43], [519, 36, 538, 44], [520, 16, 538, 46, "y"], [520, 17, 538, 47], [520, 19, 538, 49, "Math"], [520, 23, 538, 53], [520, 24, 538, 54, "round"], [520, 29, 538, 59], [520, 30, 538, 60, "faceY"], [520, 35, 538, 65], [520, 36, 538, 66], [521, 16, 538, 68, "w"], [521, 17, 538, 69], [521, 19, 538, 71, "Math"], [521, 23, 538, 75], [521, 24, 538, 76, "round"], [521, 29, 538, 81], [521, 30, 538, 82, "faceWidth"], [521, 39, 538, 91], [521, 40, 538, 92], [522, 16, 538, 94, "h"], [522, 17, 538, 95], [522, 19, 538, 97, "Math"], [522, 23, 538, 101], [522, 24, 538, 102, "round"], [522, 29, 538, 107], [522, 30, 538, 108, "faceHeight"], [522, 40, 538, 118], [523, 14, 538, 120], [523, 15, 538, 121], [524, 14, 539, 12, "padded"], [524, 20, 539, 18], [524, 22, 539, 20], [525, 16, 539, 22, "x"], [525, 17, 539, 23], [525, 19, 539, 25, "Math"], [525, 23, 539, 29], [525, 24, 539, 30, "round"], [525, 29, 539, 35], [525, 30, 539, 36, "paddedX"], [525, 37, 539, 43], [525, 38, 539, 44], [526, 16, 539, 46, "y"], [526, 17, 539, 47], [526, 19, 539, 49, "Math"], [526, 23, 539, 53], [526, 24, 539, 54, "round"], [526, 29, 539, 59], [526, 30, 539, 60, "paddedY"], [526, 37, 539, 67], [526, 38, 539, 68], [527, 16, 539, 70, "w"], [527, 17, 539, 71], [527, 19, 539, 73, "Math"], [527, 23, 539, 77], [527, 24, 539, 78, "round"], [527, 29, 539, 83], [527, 30, 539, 84, "<PERSON><PERSON><PERSON><PERSON>"], [527, 41, 539, 95], [527, 42, 539, 96], [528, 16, 539, 98, "h"], [528, 17, 539, 99], [528, 19, 539, 101, "Math"], [528, 23, 539, 105], [528, 24, 539, 106, "round"], [528, 29, 539, 111], [528, 30, 539, 112, "paddedHeight"], [528, 42, 539, 124], [529, 14, 539, 126], [530, 12, 540, 10], [530, 13, 540, 11], [530, 14, 540, 12], [532, 12, 542, 10], [533, 12, 543, 10, "applyStrongBlur"], [533, 27, 543, 25], [533, 28, 543, 26, "ctx"], [533, 31, 543, 29], [533, 33, 543, 31, "paddedX"], [533, 40, 543, 38], [533, 42, 543, 40, "paddedY"], [533, 49, 543, 47], [533, 51, 543, 49, "<PERSON><PERSON><PERSON><PERSON>"], [533, 62, 543, 60], [533, 64, 543, 62, "paddedHeight"], [533, 76, 543, 74], [533, 77, 543, 75], [534, 12, 544, 10, "console"], [534, 19, 544, 17], [534, 20, 544, 18, "log"], [534, 23, 544, 21], [534, 24, 544, 22], [534, 50, 544, 48, "index"], [534, 55, 544, 53], [534, 58, 544, 56], [534, 59, 544, 57], [534, 79, 544, 77], [534, 80, 544, 78], [535, 10, 545, 8], [535, 11, 545, 9], [535, 12, 545, 10], [536, 10, 547, 8, "console"], [536, 17, 547, 15], [536, 18, 547, 16, "log"], [536, 21, 547, 19], [536, 22, 547, 20], [536, 48, 547, 46, "detectedFaces"], [536, 61, 547, 59], [536, 62, 547, 60, "length"], [536, 68, 547, 66], [536, 104, 547, 102], [536, 105, 547, 103], [537, 8, 548, 6], [537, 9, 548, 7], [537, 15, 548, 13], [538, 10, 549, 8, "console"], [538, 17, 549, 15], [538, 18, 549, 16, "log"], [538, 21, 549, 19], [538, 22, 549, 20], [538, 109, 549, 107], [538, 110, 549, 108], [539, 10, 550, 8], [540, 10, 551, 8, "applyFallbackFaceBlur"], [540, 31, 551, 29], [540, 32, 551, 30, "ctx"], [540, 35, 551, 33], [540, 37, 551, 35, "img"], [540, 40, 551, 38], [540, 41, 551, 39, "width"], [540, 46, 551, 44], [540, 48, 551, 46, "img"], [540, 51, 551, 49], [540, 52, 551, 50, "height"], [540, 58, 551, 56], [540, 59, 551, 57], [541, 8, 552, 6], [542, 8, 554, 6, "setProcessingProgress"], [542, 29, 554, 27], [542, 30, 554, 28], [542, 32, 554, 30], [542, 33, 554, 31], [544, 8, 556, 6], [545, 8, 557, 6, "console"], [545, 15, 557, 13], [545, 16, 557, 14, "log"], [545, 19, 557, 17], [545, 20, 557, 18], [545, 85, 557, 83], [545, 86, 557, 84], [546, 8, 558, 6], [546, 14, 558, 12, "blurredImageBlob"], [546, 30, 558, 28], [546, 33, 558, 31], [546, 39, 558, 37], [546, 43, 558, 41, "Promise"], [546, 50, 558, 48], [546, 51, 558, 56, "resolve"], [546, 58, 558, 63], [546, 62, 558, 68], [547, 10, 559, 8, "canvas"], [547, 16, 559, 14], [547, 17, 559, 15, "toBlob"], [547, 23, 559, 21], [547, 24, 559, 23, "blob"], [547, 28, 559, 27], [547, 32, 559, 32, "resolve"], [547, 39, 559, 39], [547, 40, 559, 40, "blob"], [547, 44, 559, 45], [547, 45, 559, 46], [547, 47, 559, 48], [547, 59, 559, 60], [547, 61, 559, 62], [547, 64, 559, 65], [547, 65, 559, 66], [548, 8, 560, 6], [548, 9, 560, 7], [548, 10, 560, 8], [549, 8, 562, 6], [549, 14, 562, 12, "blurredImageUrl"], [549, 29, 562, 27], [549, 32, 562, 30, "URL"], [549, 35, 562, 33], [549, 36, 562, 34, "createObjectURL"], [549, 51, 562, 49], [549, 52, 562, 50, "blurredImageBlob"], [549, 68, 562, 66], [549, 69, 562, 67], [550, 8, 563, 6, "console"], [550, 15, 563, 13], [550, 16, 563, 14, "log"], [550, 19, 563, 17], [550, 20, 563, 18], [550, 66, 563, 64], [550, 68, 563, 66, "blurredImageUrl"], [550, 83, 563, 81], [550, 84, 563, 82, "substring"], [550, 93, 563, 91], [550, 94, 563, 92], [550, 95, 563, 93], [550, 97, 563, 95], [550, 99, 563, 97], [550, 100, 563, 98], [550, 103, 563, 101], [550, 108, 563, 106], [550, 109, 563, 107], [551, 8, 565, 6, "setProcessingProgress"], [551, 29, 565, 27], [551, 30, 565, 28], [551, 33, 565, 31], [551, 34, 565, 32], [553, 8, 567, 6], [554, 8, 568, 6], [554, 14, 568, 12, "completeProcessing"], [554, 32, 568, 30], [554, 33, 568, 31, "blurredImageUrl"], [554, 48, 568, 46], [554, 49, 568, 47], [555, 6, 570, 4], [555, 7, 570, 5], [555, 8, 570, 6], [555, 15, 570, 13, "error"], [555, 20, 570, 18], [555, 22, 570, 20], [556, 8, 571, 6, "console"], [556, 15, 571, 13], [556, 16, 571, 14, "error"], [556, 21, 571, 19], [556, 22, 571, 20], [556, 57, 571, 55], [556, 59, 571, 57, "error"], [556, 64, 571, 62], [556, 65, 571, 63], [557, 8, 572, 6, "setErrorMessage"], [557, 23, 572, 21], [557, 24, 572, 22], [557, 50, 572, 48], [557, 51, 572, 49], [558, 8, 573, 6, "setProcessingState"], [558, 26, 573, 24], [558, 27, 573, 25], [558, 34, 573, 32], [558, 35, 573, 33], [559, 6, 574, 4], [560, 4, 575, 2], [560, 5, 575, 3], [562, 4, 577, 2], [563, 4, 578, 2], [563, 10, 578, 8, "completeProcessing"], [563, 28, 578, 26], [563, 31, 578, 29], [563, 37, 578, 36, "blurredImageUrl"], [563, 52, 578, 59], [563, 56, 578, 64], [564, 6, 579, 4], [564, 10, 579, 8], [565, 8, 580, 6, "setProcessingState"], [565, 26, 580, 24], [565, 27, 580, 25], [565, 37, 580, 35], [565, 38, 580, 36], [567, 8, 582, 6], [568, 8, 583, 6], [568, 14, 583, 12, "timestamp"], [568, 23, 583, 21], [568, 26, 583, 24, "Date"], [568, 30, 583, 28], [568, 31, 583, 29, "now"], [568, 34, 583, 32], [568, 35, 583, 33], [568, 36, 583, 34], [569, 8, 584, 6], [569, 14, 584, 12, "result"], [569, 20, 584, 18], [569, 23, 584, 21], [570, 10, 585, 8, "imageUrl"], [570, 18, 585, 16], [570, 20, 585, 18, "blurredImageUrl"], [570, 35, 585, 33], [571, 10, 586, 8, "localUri"], [571, 18, 586, 16], [571, 20, 586, 18, "blurredImageUrl"], [571, 35, 586, 33], [572, 10, 587, 8, "challengeCode"], [572, 23, 587, 21], [572, 25, 587, 23, "challengeCode"], [572, 38, 587, 36], [572, 42, 587, 40], [572, 44, 587, 42], [573, 10, 588, 8, "timestamp"], [573, 19, 588, 17], [574, 10, 589, 8, "jobId"], [574, 15, 589, 13], [574, 17, 589, 15], [574, 27, 589, 25, "timestamp"], [574, 36, 589, 34], [574, 38, 589, 36], [575, 10, 590, 8, "status"], [575, 16, 590, 14], [575, 18, 590, 16], [576, 8, 591, 6], [576, 9, 591, 7], [577, 8, 593, 6, "console"], [577, 15, 593, 13], [577, 16, 593, 14, "log"], [577, 19, 593, 17], [577, 20, 593, 18], [577, 100, 593, 98], [577, 102, 593, 100], [578, 10, 594, 8, "imageUrl"], [578, 18, 594, 16], [578, 20, 594, 18, "blurredImageUrl"], [578, 35, 594, 33], [578, 36, 594, 34, "substring"], [578, 45, 594, 43], [578, 46, 594, 44], [578, 47, 594, 45], [578, 49, 594, 47], [578, 51, 594, 49], [578, 52, 594, 50], [578, 55, 594, 53], [578, 60, 594, 58], [579, 10, 595, 8, "timestamp"], [579, 19, 595, 17], [580, 10, 596, 8, "jobId"], [580, 15, 596, 13], [580, 17, 596, 15, "result"], [580, 23, 596, 21], [580, 24, 596, 22, "jobId"], [581, 8, 597, 6], [581, 9, 597, 7], [581, 10, 597, 8], [583, 8, 599, 6], [584, 8, 600, 6, "onComplete"], [584, 18, 600, 16], [584, 19, 600, 17, "result"], [584, 25, 600, 23], [584, 26, 600, 24], [585, 6, 602, 4], [585, 7, 602, 5], [585, 8, 602, 6], [585, 15, 602, 13, "error"], [585, 20, 602, 18], [585, 22, 602, 20], [586, 8, 603, 6, "console"], [586, 15, 603, 13], [586, 16, 603, 14, "error"], [586, 21, 603, 19], [586, 22, 603, 20], [586, 57, 603, 55], [586, 59, 603, 57, "error"], [586, 64, 603, 62], [586, 65, 603, 63], [587, 8, 604, 6, "setErrorMessage"], [587, 23, 604, 21], [587, 24, 604, 22], [587, 56, 604, 54], [587, 57, 604, 55], [588, 8, 605, 6, "setProcessingState"], [588, 26, 605, 24], [588, 27, 605, 25], [588, 34, 605, 32], [588, 35, 605, 33], [589, 6, 606, 4], [590, 4, 607, 2], [590, 5, 607, 3], [592, 4, 609, 2], [593, 4, 610, 2], [593, 10, 610, 8, "triggerServerProcessing"], [593, 33, 610, 31], [593, 36, 610, 34], [593, 42, 610, 34, "triggerServerProcessing"], [593, 43, 610, 41, "privateImageUrl"], [593, 58, 610, 64], [593, 60, 610, 66, "timestamp"], [593, 69, 610, 83], [593, 74, 610, 88], [594, 6, 611, 4], [594, 10, 611, 8], [595, 8, 612, 6, "console"], [595, 15, 612, 13], [595, 16, 612, 14, "log"], [595, 19, 612, 17], [595, 20, 612, 18], [595, 74, 612, 72], [595, 76, 612, 74, "privateImageUrl"], [595, 91, 612, 89], [595, 92, 612, 90], [596, 8, 613, 6, "setProcessingState"], [596, 26, 613, 24], [596, 27, 613, 25], [596, 39, 613, 37], [596, 40, 613, 38], [597, 8, 614, 6, "setProcessingProgress"], [597, 29, 614, 27], [597, 30, 614, 28], [597, 32, 614, 30], [597, 33, 614, 31], [598, 8, 616, 6], [598, 14, 616, 12, "requestBody"], [598, 25, 616, 23], [598, 28, 616, 26], [599, 10, 617, 8, "imageUrl"], [599, 18, 617, 16], [599, 20, 617, 18, "privateImageUrl"], [599, 35, 617, 33], [600, 10, 618, 8, "userId"], [600, 16, 618, 14], [601, 10, 619, 8, "requestId"], [601, 19, 619, 17], [602, 10, 620, 8, "timestamp"], [602, 19, 620, 17], [603, 10, 621, 8, "platform"], [603, 18, 621, 16], [603, 20, 621, 18], [604, 8, 622, 6], [604, 9, 622, 7], [605, 8, 624, 6, "console"], [605, 15, 624, 13], [605, 16, 624, 14, "log"], [605, 19, 624, 17], [605, 20, 624, 18], [605, 65, 624, 63], [605, 67, 624, 65, "requestBody"], [605, 78, 624, 76], [605, 79, 624, 77], [607, 8, 626, 6], [608, 8, 627, 6], [608, 14, 627, 12, "response"], [608, 22, 627, 20], [608, 25, 627, 23], [608, 31, 627, 29, "fetch"], [608, 36, 627, 34], [608, 37, 627, 35], [608, 40, 627, 38, "API_BASE_URL"], [608, 52, 627, 50], [608, 72, 627, 70], [608, 74, 627, 72], [609, 10, 628, 8, "method"], [609, 16, 628, 14], [609, 18, 628, 16], [609, 24, 628, 22], [610, 10, 629, 8, "headers"], [610, 17, 629, 15], [610, 19, 629, 17], [611, 12, 630, 10], [611, 26, 630, 24], [611, 28, 630, 26], [611, 46, 630, 44], [612, 12, 631, 10], [612, 27, 631, 25], [612, 29, 631, 27], [612, 39, 631, 37], [612, 45, 631, 43, "getAuthToken"], [612, 57, 631, 55], [612, 58, 631, 56], [612, 59, 631, 57], [613, 10, 632, 8], [613, 11, 632, 9], [614, 10, 633, 8, "body"], [614, 14, 633, 12], [614, 16, 633, 14, "JSON"], [614, 20, 633, 18], [614, 21, 633, 19, "stringify"], [614, 30, 633, 28], [614, 31, 633, 29, "requestBody"], [614, 42, 633, 40], [615, 8, 634, 6], [615, 9, 634, 7], [615, 10, 634, 8], [616, 8, 636, 6], [616, 12, 636, 10], [616, 13, 636, 11, "response"], [616, 21, 636, 19], [616, 22, 636, 20, "ok"], [616, 24, 636, 22], [616, 26, 636, 24], [617, 10, 637, 8], [617, 16, 637, 14, "errorText"], [617, 25, 637, 23], [617, 28, 637, 26], [617, 34, 637, 32, "response"], [617, 42, 637, 40], [617, 43, 637, 41, "text"], [617, 47, 637, 45], [617, 48, 637, 46], [617, 49, 637, 47], [618, 10, 638, 8, "console"], [618, 17, 638, 15], [618, 18, 638, 16, "error"], [618, 23, 638, 21], [618, 24, 638, 22], [618, 68, 638, 66], [618, 70, 638, 68, "response"], [618, 78, 638, 76], [618, 79, 638, 77, "status"], [618, 85, 638, 83], [618, 87, 638, 85, "errorText"], [618, 96, 638, 94], [618, 97, 638, 95], [619, 10, 639, 8], [619, 16, 639, 14], [619, 20, 639, 18, "Error"], [619, 25, 639, 23], [619, 26, 639, 24], [619, 48, 639, 46, "response"], [619, 56, 639, 54], [619, 57, 639, 55, "status"], [619, 63, 639, 61], [619, 67, 639, 65, "response"], [619, 75, 639, 73], [619, 76, 639, 74, "statusText"], [619, 86, 639, 84], [619, 88, 639, 86], [619, 89, 639, 87], [620, 8, 640, 6], [621, 8, 642, 6], [621, 14, 642, 12, "result"], [621, 20, 642, 18], [621, 23, 642, 21], [621, 29, 642, 27, "response"], [621, 37, 642, 35], [621, 38, 642, 36, "json"], [621, 42, 642, 40], [621, 43, 642, 41], [621, 44, 642, 42], [622, 8, 643, 6, "console"], [622, 15, 643, 13], [622, 16, 643, 14, "log"], [622, 19, 643, 17], [622, 20, 643, 18], [622, 68, 643, 66], [622, 70, 643, 68, "result"], [622, 76, 643, 74], [622, 77, 643, 75], [623, 8, 645, 6], [623, 12, 645, 10], [623, 13, 645, 11, "result"], [623, 19, 645, 17], [623, 20, 645, 18, "jobId"], [623, 25, 645, 23], [623, 27, 645, 25], [624, 10, 646, 8], [624, 16, 646, 14], [624, 20, 646, 18, "Error"], [624, 25, 646, 23], [624, 26, 646, 24], [624, 70, 646, 68], [624, 71, 646, 69], [625, 8, 647, 6], [627, 8, 649, 6], [628, 8, 650, 6], [628, 14, 650, 12, "pollForCompletion"], [628, 31, 650, 29], [628, 32, 650, 30, "result"], [628, 38, 650, 36], [628, 39, 650, 37, "jobId"], [628, 44, 650, 42], [628, 46, 650, 44, "timestamp"], [628, 55, 650, 53], [628, 56, 650, 54], [629, 6, 651, 4], [629, 7, 651, 5], [629, 8, 651, 6], [629, 15, 651, 13, "error"], [629, 20, 651, 18], [629, 22, 651, 20], [630, 8, 652, 6, "console"], [630, 15, 652, 13], [630, 16, 652, 14, "error"], [630, 21, 652, 19], [630, 22, 652, 20], [630, 57, 652, 55], [630, 59, 652, 57, "error"], [630, 64, 652, 62], [630, 65, 652, 63], [631, 8, 653, 6, "setErrorMessage"], [631, 23, 653, 21], [631, 24, 653, 22], [631, 52, 653, 50, "error"], [631, 57, 653, 55], [631, 58, 653, 56, "message"], [631, 65, 653, 63], [631, 67, 653, 65], [631, 68, 653, 66], [632, 8, 654, 6, "setProcessingState"], [632, 26, 654, 24], [632, 27, 654, 25], [632, 34, 654, 32], [632, 35, 654, 33], [633, 6, 655, 4], [634, 4, 656, 2], [634, 5, 656, 3], [635, 4, 657, 2], [636, 4, 658, 2], [636, 10, 658, 8, "pollForCompletion"], [636, 27, 658, 25], [636, 30, 658, 28], [636, 36, 658, 28, "pollForCompletion"], [636, 37, 658, 35, "jobId"], [636, 42, 658, 48], [636, 44, 658, 50, "timestamp"], [636, 53, 658, 67], [636, 55, 658, 69, "attempts"], [636, 63, 658, 77], [636, 66, 658, 80], [636, 67, 658, 81], [636, 72, 658, 86], [637, 6, 659, 4], [637, 12, 659, 10, "MAX_ATTEMPTS"], [637, 24, 659, 22], [637, 27, 659, 25], [637, 29, 659, 27], [637, 30, 659, 28], [637, 31, 659, 29], [638, 6, 660, 4], [638, 12, 660, 10, "POLL_INTERVAL"], [638, 25, 660, 23], [638, 28, 660, 26], [638, 32, 660, 30], [638, 33, 660, 31], [638, 34, 660, 32], [640, 6, 662, 4, "console"], [640, 13, 662, 11], [640, 14, 662, 12, "log"], [640, 17, 662, 15], [640, 18, 662, 16], [640, 53, 662, 51, "attempts"], [640, 61, 662, 59], [640, 64, 662, 62], [640, 65, 662, 63], [640, 69, 662, 67, "MAX_ATTEMPTS"], [640, 81, 662, 79], [640, 93, 662, 91, "jobId"], [640, 98, 662, 96], [640, 100, 662, 98], [640, 101, 662, 99], [641, 6, 664, 4], [641, 10, 664, 8, "attempts"], [641, 18, 664, 16], [641, 22, 664, 20, "MAX_ATTEMPTS"], [641, 34, 664, 32], [641, 36, 664, 34], [642, 8, 665, 6, "console"], [642, 15, 665, 13], [642, 16, 665, 14, "error"], [642, 21, 665, 19], [642, 22, 665, 20], [642, 75, 665, 73], [642, 76, 665, 74], [643, 8, 666, 6, "setErrorMessage"], [643, 23, 666, 21], [643, 24, 666, 22], [643, 63, 666, 61], [643, 64, 666, 62], [644, 8, 667, 6, "setProcessingState"], [644, 26, 667, 24], [644, 27, 667, 25], [644, 34, 667, 32], [644, 35, 667, 33], [645, 8, 668, 6], [646, 6, 669, 4], [647, 6, 671, 4], [647, 10, 671, 8], [648, 8, 672, 6], [648, 14, 672, 12, "response"], [648, 22, 672, 20], [648, 25, 672, 23], [648, 31, 672, 29, "fetch"], [648, 36, 672, 34], [648, 37, 672, 35], [648, 40, 672, 38, "API_BASE_URL"], [648, 52, 672, 50], [648, 75, 672, 73, "jobId"], [648, 80, 672, 78], [648, 82, 672, 80], [648, 84, 672, 82], [649, 10, 673, 8, "headers"], [649, 17, 673, 15], [649, 19, 673, 17], [650, 12, 674, 10], [650, 27, 674, 25], [650, 29, 674, 27], [650, 39, 674, 37], [650, 45, 674, 43, "getAuthToken"], [650, 57, 674, 55], [650, 58, 674, 56], [650, 59, 674, 57], [651, 10, 675, 8], [652, 8, 676, 6], [652, 9, 676, 7], [652, 10, 676, 8], [653, 8, 678, 6], [653, 12, 678, 10], [653, 13, 678, 11, "response"], [653, 21, 678, 19], [653, 22, 678, 20, "ok"], [653, 24, 678, 22], [653, 26, 678, 24], [654, 10, 679, 8], [654, 16, 679, 14], [654, 20, 679, 18, "Error"], [654, 25, 679, 23], [654, 26, 679, 24], [654, 34, 679, 32, "response"], [654, 42, 679, 40], [654, 43, 679, 41, "status"], [654, 49, 679, 47], [654, 54, 679, 52, "response"], [654, 62, 679, 60], [654, 63, 679, 61, "statusText"], [654, 73, 679, 71], [654, 75, 679, 73], [654, 76, 679, 74], [655, 8, 680, 6], [656, 8, 682, 6], [656, 14, 682, 12, "status"], [656, 20, 682, 18], [656, 23, 682, 21], [656, 29, 682, 27, "response"], [656, 37, 682, 35], [656, 38, 682, 36, "json"], [656, 42, 682, 40], [656, 43, 682, 41], [656, 44, 682, 42], [657, 8, 683, 6, "console"], [657, 15, 683, 13], [657, 16, 683, 14, "log"], [657, 19, 683, 17], [657, 20, 683, 18], [657, 54, 683, 52], [657, 56, 683, 54, "status"], [657, 62, 683, 60], [657, 63, 683, 61], [658, 8, 685, 6], [658, 12, 685, 10, "status"], [658, 18, 685, 16], [658, 19, 685, 17, "status"], [658, 25, 685, 23], [658, 30, 685, 28], [658, 41, 685, 39], [658, 43, 685, 41], [659, 10, 686, 8, "console"], [659, 17, 686, 15], [659, 18, 686, 16, "log"], [659, 21, 686, 19], [659, 22, 686, 20], [659, 73, 686, 71], [659, 74, 686, 72], [660, 10, 687, 8, "setProcessingProgress"], [660, 31, 687, 29], [660, 32, 687, 30], [660, 35, 687, 33], [660, 36, 687, 34], [661, 10, 688, 8, "setProcessingState"], [661, 28, 688, 26], [661, 29, 688, 27], [661, 40, 688, 38], [661, 41, 688, 39], [662, 10, 689, 8], [663, 10, 690, 8], [663, 16, 690, 14, "result"], [663, 22, 690, 20], [663, 25, 690, 23], [664, 12, 691, 10, "imageUrl"], [664, 20, 691, 18], [664, 22, 691, 20, "status"], [664, 28, 691, 26], [664, 29, 691, 27, "publicUrl"], [664, 38, 691, 36], [665, 12, 691, 38], [666, 12, 692, 10, "localUri"], [666, 20, 692, 18], [666, 22, 692, 20, "capturedPhoto"], [666, 35, 692, 33], [666, 39, 692, 37, "status"], [666, 45, 692, 43], [666, 46, 692, 44, "publicUrl"], [666, 55, 692, 53], [667, 12, 692, 55], [668, 12, 693, 10, "challengeCode"], [668, 25, 693, 23], [668, 27, 693, 25, "challengeCode"], [668, 40, 693, 38], [668, 44, 693, 42], [668, 46, 693, 44], [669, 12, 694, 10, "timestamp"], [669, 21, 694, 19], [670, 12, 695, 10, "processingStatus"], [670, 28, 695, 26], [670, 30, 695, 28], [671, 10, 696, 8], [671, 11, 696, 9], [672, 10, 697, 8, "console"], [672, 17, 697, 15], [672, 18, 697, 16, "log"], [672, 21, 697, 19], [672, 22, 697, 20], [672, 57, 697, 55], [672, 59, 697, 57, "result"], [672, 65, 697, 63], [672, 66, 697, 64], [673, 10, 698, 8, "onComplete"], [673, 20, 698, 18], [673, 21, 698, 19, "result"], [673, 27, 698, 25], [673, 28, 698, 26], [674, 10, 699, 8], [675, 8, 700, 6], [675, 9, 700, 7], [675, 15, 700, 13], [675, 19, 700, 17, "status"], [675, 25, 700, 23], [675, 26, 700, 24, "status"], [675, 32, 700, 30], [675, 37, 700, 35], [675, 45, 700, 43], [675, 47, 700, 45], [676, 10, 701, 8, "console"], [676, 17, 701, 15], [676, 18, 701, 16, "error"], [676, 23, 701, 21], [676, 24, 701, 22], [676, 60, 701, 58], [676, 62, 701, 60, "status"], [676, 68, 701, 66], [676, 69, 701, 67, "error"], [676, 74, 701, 72], [676, 75, 701, 73], [677, 10, 702, 8], [677, 16, 702, 14], [677, 20, 702, 18, "Error"], [677, 25, 702, 23], [677, 26, 702, 24, "status"], [677, 32, 702, 30], [677, 33, 702, 31, "error"], [677, 38, 702, 36], [677, 42, 702, 40], [677, 61, 702, 59], [677, 62, 702, 60], [678, 8, 703, 6], [678, 9, 703, 7], [678, 15, 703, 13], [679, 10, 704, 8], [680, 10, 705, 8], [680, 16, 705, 14, "progressValue"], [680, 29, 705, 27], [680, 32, 705, 30], [680, 34, 705, 32], [680, 37, 705, 36, "attempts"], [680, 45, 705, 44], [680, 48, 705, 47, "MAX_ATTEMPTS"], [680, 60, 705, 59], [680, 63, 705, 63], [680, 65, 705, 65], [681, 10, 706, 8, "console"], [681, 17, 706, 15], [681, 18, 706, 16, "log"], [681, 21, 706, 19], [681, 22, 706, 20], [681, 71, 706, 69, "progressValue"], [681, 84, 706, 82], [681, 87, 706, 85], [681, 88, 706, 86], [682, 10, 707, 8, "setProcessingProgress"], [682, 31, 707, 29], [682, 32, 707, 30, "progressValue"], [682, 45, 707, 43], [682, 46, 707, 44], [683, 10, 709, 8, "setTimeout"], [683, 20, 709, 18], [683, 21, 709, 19], [683, 27, 709, 25], [684, 12, 710, 10, "pollForCompletion"], [684, 29, 710, 27], [684, 30, 710, 28, "jobId"], [684, 35, 710, 33], [684, 37, 710, 35, "timestamp"], [684, 46, 710, 44], [684, 48, 710, 46, "attempts"], [684, 56, 710, 54], [684, 59, 710, 57], [684, 60, 710, 58], [684, 61, 710, 59], [685, 10, 711, 8], [685, 11, 711, 9], [685, 13, 711, 11, "POLL_INTERVAL"], [685, 26, 711, 24], [685, 27, 711, 25], [686, 8, 712, 6], [687, 6, 713, 4], [687, 7, 713, 5], [687, 8, 713, 6], [687, 15, 713, 13, "error"], [687, 20, 713, 18], [687, 22, 713, 20], [688, 8, 714, 6, "console"], [688, 15, 714, 13], [688, 16, 714, 14, "error"], [688, 21, 714, 19], [688, 22, 714, 20], [688, 54, 714, 52], [688, 56, 714, 54, "error"], [688, 61, 714, 59], [688, 62, 714, 60], [689, 8, 715, 6, "setErrorMessage"], [689, 23, 715, 21], [689, 24, 715, 22], [689, 62, 715, 60, "error"], [689, 67, 715, 65], [689, 68, 715, 66, "message"], [689, 75, 715, 73], [689, 77, 715, 75], [689, 78, 715, 76], [690, 8, 716, 6, "setProcessingState"], [690, 26, 716, 24], [690, 27, 716, 25], [690, 34, 716, 32], [690, 35, 716, 33], [691, 6, 717, 4], [692, 4, 718, 2], [692, 5, 718, 3], [693, 4, 719, 2], [694, 4, 720, 2], [694, 10, 720, 8, "getAuthToken"], [694, 22, 720, 20], [694, 25, 720, 23], [694, 31, 720, 23, "getAuthToken"], [694, 32, 720, 23], [694, 37, 720, 52], [695, 6, 721, 4], [696, 6, 722, 4], [697, 6, 723, 4], [697, 13, 723, 11], [697, 30, 723, 28], [698, 4, 724, 2], [698, 5, 724, 3], [700, 4, 726, 2], [701, 4, 727, 2], [701, 10, 727, 8, "retryCapture"], [701, 22, 727, 20], [701, 25, 727, 23], [701, 29, 727, 23, "useCallback"], [701, 47, 727, 34], [701, 49, 727, 35], [701, 55, 727, 41], [702, 6, 728, 4, "console"], [702, 13, 728, 11], [702, 14, 728, 12, "log"], [702, 17, 728, 15], [702, 18, 728, 16], [702, 55, 728, 53], [702, 56, 728, 54], [703, 6, 729, 4, "setProcessingState"], [703, 24, 729, 22], [703, 25, 729, 23], [703, 31, 729, 29], [703, 32, 729, 30], [704, 6, 730, 4, "setErrorMessage"], [704, 21, 730, 19], [704, 22, 730, 20], [704, 24, 730, 22], [704, 25, 730, 23], [705, 6, 731, 4, "setCapturedPhoto"], [705, 22, 731, 20], [705, 23, 731, 21], [705, 25, 731, 23], [705, 26, 731, 24], [706, 6, 732, 4, "setProcessingProgress"], [706, 27, 732, 25], [706, 28, 732, 26], [706, 29, 732, 27], [706, 30, 732, 28], [707, 4, 733, 2], [707, 5, 733, 3], [707, 7, 733, 5], [707, 9, 733, 7], [707, 10, 733, 8], [708, 4, 734, 2], [709, 4, 735, 2], [709, 8, 735, 2, "useEffect"], [709, 24, 735, 11], [709, 26, 735, 12], [709, 32, 735, 18], [710, 6, 736, 4, "console"], [710, 13, 736, 11], [710, 14, 736, 12, "log"], [710, 17, 736, 15], [710, 18, 736, 16], [710, 53, 736, 51], [710, 55, 736, 53, "permission"], [710, 65, 736, 63], [710, 66, 736, 64], [711, 6, 737, 4], [711, 10, 737, 8, "permission"], [711, 20, 737, 18], [711, 22, 737, 20], [712, 8, 738, 6, "console"], [712, 15, 738, 13], [712, 16, 738, 14, "log"], [712, 19, 738, 17], [712, 20, 738, 18], [712, 57, 738, 55], [712, 59, 738, 57, "permission"], [712, 69, 738, 67], [712, 70, 738, 68, "granted"], [712, 77, 738, 75], [712, 78, 738, 76], [713, 6, 739, 4], [714, 4, 740, 2], [714, 5, 740, 3], [714, 7, 740, 5], [714, 8, 740, 6, "permission"], [714, 18, 740, 16], [714, 19, 740, 17], [714, 20, 740, 18], [715, 4, 741, 2], [716, 4, 742, 2], [716, 8, 742, 6], [716, 9, 742, 7, "permission"], [716, 19, 742, 17], [716, 21, 742, 19], [717, 6, 743, 4, "console"], [717, 13, 743, 11], [717, 14, 743, 12, "log"], [717, 17, 743, 15], [717, 18, 743, 16], [717, 67, 743, 65], [717, 68, 743, 66], [718, 6, 744, 4], [718, 26, 745, 6], [718, 30, 745, 6, "_jsxDevRuntime"], [718, 44, 745, 6], [718, 45, 745, 6, "jsxDEV"], [718, 51, 745, 6], [718, 53, 745, 7, "_View"], [718, 58, 745, 7], [718, 59, 745, 7, "default"], [718, 66, 745, 11], [719, 8, 745, 12, "style"], [719, 13, 745, 17], [719, 15, 745, 19, "styles"], [719, 21, 745, 25], [719, 22, 745, 26, "container"], [719, 31, 745, 36], [720, 8, 745, 36, "children"], [720, 16, 745, 36], [720, 32, 746, 8], [720, 36, 746, 8, "_jsxDevRuntime"], [720, 50, 746, 8], [720, 51, 746, 8, "jsxDEV"], [720, 57, 746, 8], [720, 59, 746, 9, "_ActivityIndicator"], [720, 77, 746, 9], [720, 78, 746, 9, "default"], [720, 85, 746, 26], [721, 10, 746, 27, "size"], [721, 14, 746, 31], [721, 16, 746, 32], [721, 23, 746, 39], [722, 10, 746, 40, "color"], [722, 15, 746, 45], [722, 17, 746, 46], [723, 8, 746, 55], [724, 10, 746, 55, "fileName"], [724, 18, 746, 55], [724, 20, 746, 55, "_jsxFileName"], [724, 32, 746, 55], [725, 10, 746, 55, "lineNumber"], [725, 20, 746, 55], [726, 10, 746, 55, "columnNumber"], [726, 22, 746, 55], [727, 8, 746, 55], [727, 15, 746, 57], [727, 16, 746, 58], [727, 31, 747, 8], [727, 35, 747, 8, "_jsxDevRuntime"], [727, 49, 747, 8], [727, 50, 747, 8, "jsxDEV"], [727, 56, 747, 8], [727, 58, 747, 9, "_Text"], [727, 63, 747, 9], [727, 64, 747, 9, "default"], [727, 71, 747, 13], [728, 10, 747, 14, "style"], [728, 15, 747, 19], [728, 17, 747, 21, "styles"], [728, 23, 747, 27], [728, 24, 747, 28, "loadingText"], [728, 35, 747, 40], [729, 10, 747, 40, "children"], [729, 18, 747, 40], [729, 20, 747, 41], [730, 8, 747, 58], [731, 10, 747, 58, "fileName"], [731, 18, 747, 58], [731, 20, 747, 58, "_jsxFileName"], [731, 32, 747, 58], [732, 10, 747, 58, "lineNumber"], [732, 20, 747, 58], [733, 10, 747, 58, "columnNumber"], [733, 22, 747, 58], [734, 8, 747, 58], [734, 15, 747, 64], [734, 16, 747, 65], [735, 6, 747, 65], [736, 8, 747, 65, "fileName"], [736, 16, 747, 65], [736, 18, 747, 65, "_jsxFileName"], [736, 30, 747, 65], [737, 8, 747, 65, "lineNumber"], [737, 18, 747, 65], [738, 8, 747, 65, "columnNumber"], [738, 20, 747, 65], [739, 6, 747, 65], [739, 13, 748, 12], [739, 14, 748, 13], [740, 4, 750, 2], [741, 4, 751, 2], [741, 8, 751, 6], [741, 9, 751, 7, "permission"], [741, 19, 751, 17], [741, 20, 751, 18, "granted"], [741, 27, 751, 25], [741, 29, 751, 27], [742, 6, 752, 4, "console"], [742, 13, 752, 11], [742, 14, 752, 12, "log"], [742, 17, 752, 15], [742, 18, 752, 16], [742, 93, 752, 91], [742, 94, 752, 92], [743, 6, 753, 4], [743, 26, 754, 6], [743, 30, 754, 6, "_jsxDevRuntime"], [743, 44, 754, 6], [743, 45, 754, 6, "jsxDEV"], [743, 51, 754, 6], [743, 53, 754, 7, "_View"], [743, 58, 754, 7], [743, 59, 754, 7, "default"], [743, 66, 754, 11], [744, 8, 754, 12, "style"], [744, 13, 754, 17], [744, 15, 754, 19, "styles"], [744, 21, 754, 25], [744, 22, 754, 26, "container"], [744, 31, 754, 36], [745, 8, 754, 36, "children"], [745, 16, 754, 36], [745, 31, 755, 8], [745, 35, 755, 8, "_jsxDevRuntime"], [745, 49, 755, 8], [745, 50, 755, 8, "jsxDEV"], [745, 56, 755, 8], [745, 58, 755, 9, "_View"], [745, 63, 755, 9], [745, 64, 755, 9, "default"], [745, 71, 755, 13], [746, 10, 755, 14, "style"], [746, 15, 755, 19], [746, 17, 755, 21, "styles"], [746, 23, 755, 27], [746, 24, 755, 28, "permissionContent"], [746, 41, 755, 46], [747, 10, 755, 46, "children"], [747, 18, 755, 46], [747, 34, 756, 10], [747, 38, 756, 10, "_jsxDevRuntime"], [747, 52, 756, 10], [747, 53, 756, 10, "jsxDEV"], [747, 59, 756, 10], [747, 61, 756, 11, "_lucideReactNative"], [747, 79, 756, 11], [747, 80, 756, 11, "Camera"], [747, 86, 756, 21], [748, 12, 756, 22, "size"], [748, 16, 756, 26], [748, 18, 756, 28], [748, 20, 756, 31], [749, 12, 756, 32, "color"], [749, 17, 756, 37], [749, 19, 756, 38], [750, 10, 756, 47], [751, 12, 756, 47, "fileName"], [751, 20, 756, 47], [751, 22, 756, 47, "_jsxFileName"], [751, 34, 756, 47], [752, 12, 756, 47, "lineNumber"], [752, 22, 756, 47], [753, 12, 756, 47, "columnNumber"], [753, 24, 756, 47], [754, 10, 756, 47], [754, 17, 756, 49], [754, 18, 756, 50], [754, 33, 757, 10], [754, 37, 757, 10, "_jsxDevRuntime"], [754, 51, 757, 10], [754, 52, 757, 10, "jsxDEV"], [754, 58, 757, 10], [754, 60, 757, 11, "_Text"], [754, 65, 757, 11], [754, 66, 757, 11, "default"], [754, 73, 757, 15], [755, 12, 757, 16, "style"], [755, 17, 757, 21], [755, 19, 757, 23, "styles"], [755, 25, 757, 29], [755, 26, 757, 30, "permissionTitle"], [755, 41, 757, 46], [756, 12, 757, 46, "children"], [756, 20, 757, 46], [756, 22, 757, 47], [757, 10, 757, 73], [758, 12, 757, 73, "fileName"], [758, 20, 757, 73], [758, 22, 757, 73, "_jsxFileName"], [758, 34, 757, 73], [759, 12, 757, 73, "lineNumber"], [759, 22, 757, 73], [760, 12, 757, 73, "columnNumber"], [760, 24, 757, 73], [761, 10, 757, 73], [761, 17, 757, 79], [761, 18, 757, 80], [761, 33, 758, 10], [761, 37, 758, 10, "_jsxDevRuntime"], [761, 51, 758, 10], [761, 52, 758, 10, "jsxDEV"], [761, 58, 758, 10], [761, 60, 758, 11, "_Text"], [761, 65, 758, 11], [761, 66, 758, 11, "default"], [761, 73, 758, 15], [762, 12, 758, 16, "style"], [762, 17, 758, 21], [762, 19, 758, 23, "styles"], [762, 25, 758, 29], [762, 26, 758, 30, "permissionDescription"], [762, 47, 758, 52], [763, 12, 758, 52, "children"], [763, 20, 758, 52], [763, 22, 758, 53], [764, 10, 761, 10], [765, 12, 761, 10, "fileName"], [765, 20, 761, 10], [765, 22, 761, 10, "_jsxFileName"], [765, 34, 761, 10], [766, 12, 761, 10, "lineNumber"], [766, 22, 761, 10], [767, 12, 761, 10, "columnNumber"], [767, 24, 761, 10], [768, 10, 761, 10], [768, 17, 761, 16], [768, 18, 761, 17], [768, 33, 762, 10], [768, 37, 762, 10, "_jsxDevRuntime"], [768, 51, 762, 10], [768, 52, 762, 10, "jsxDEV"], [768, 58, 762, 10], [768, 60, 762, 11, "_TouchableOpacity"], [768, 77, 762, 11], [768, 78, 762, 11, "default"], [768, 85, 762, 27], [769, 12, 762, 28, "onPress"], [769, 19, 762, 35], [769, 21, 762, 37, "requestPermission"], [769, 38, 762, 55], [770, 12, 762, 56, "style"], [770, 17, 762, 61], [770, 19, 762, 63, "styles"], [770, 25, 762, 69], [770, 26, 762, 70, "primaryButton"], [770, 39, 762, 84], [771, 12, 762, 84, "children"], [771, 20, 762, 84], [771, 35, 763, 12], [771, 39, 763, 12, "_jsxDevRuntime"], [771, 53, 763, 12], [771, 54, 763, 12, "jsxDEV"], [771, 60, 763, 12], [771, 62, 763, 13, "_Text"], [771, 67, 763, 13], [771, 68, 763, 13, "default"], [771, 75, 763, 17], [772, 14, 763, 18, "style"], [772, 19, 763, 23], [772, 21, 763, 25, "styles"], [772, 27, 763, 31], [772, 28, 763, 32, "primaryButtonText"], [772, 45, 763, 50], [773, 14, 763, 50, "children"], [773, 22, 763, 50], [773, 24, 763, 51], [774, 12, 763, 67], [775, 14, 763, 67, "fileName"], [775, 22, 763, 67], [775, 24, 763, 67, "_jsxFileName"], [775, 36, 763, 67], [776, 14, 763, 67, "lineNumber"], [776, 24, 763, 67], [777, 14, 763, 67, "columnNumber"], [777, 26, 763, 67], [778, 12, 763, 67], [778, 19, 763, 73], [779, 10, 763, 74], [780, 12, 763, 74, "fileName"], [780, 20, 763, 74], [780, 22, 763, 74, "_jsxFileName"], [780, 34, 763, 74], [781, 12, 763, 74, "lineNumber"], [781, 22, 763, 74], [782, 12, 763, 74, "columnNumber"], [782, 24, 763, 74], [783, 10, 763, 74], [783, 17, 764, 28], [783, 18, 764, 29], [783, 33, 765, 10], [783, 37, 765, 10, "_jsxDevRuntime"], [783, 51, 765, 10], [783, 52, 765, 10, "jsxDEV"], [783, 58, 765, 10], [783, 60, 765, 11, "_TouchableOpacity"], [783, 77, 765, 11], [783, 78, 765, 11, "default"], [783, 85, 765, 27], [784, 12, 765, 28, "onPress"], [784, 19, 765, 35], [784, 21, 765, 37, "onCancel"], [784, 29, 765, 46], [785, 12, 765, 47, "style"], [785, 17, 765, 52], [785, 19, 765, 54, "styles"], [785, 25, 765, 60], [785, 26, 765, 61, "secondaryButton"], [785, 41, 765, 77], [786, 12, 765, 77, "children"], [786, 20, 765, 77], [786, 35, 766, 12], [786, 39, 766, 12, "_jsxDevRuntime"], [786, 53, 766, 12], [786, 54, 766, 12, "jsxDEV"], [786, 60, 766, 12], [786, 62, 766, 13, "_Text"], [786, 67, 766, 13], [786, 68, 766, 13, "default"], [786, 75, 766, 17], [787, 14, 766, 18, "style"], [787, 19, 766, 23], [787, 21, 766, 25, "styles"], [787, 27, 766, 31], [787, 28, 766, 32, "secondaryButtonText"], [787, 47, 766, 52], [788, 14, 766, 52, "children"], [788, 22, 766, 52], [788, 24, 766, 53], [789, 12, 766, 59], [790, 14, 766, 59, "fileName"], [790, 22, 766, 59], [790, 24, 766, 59, "_jsxFileName"], [790, 36, 766, 59], [791, 14, 766, 59, "lineNumber"], [791, 24, 766, 59], [792, 14, 766, 59, "columnNumber"], [792, 26, 766, 59], [793, 12, 766, 59], [793, 19, 766, 65], [794, 10, 766, 66], [795, 12, 766, 66, "fileName"], [795, 20, 766, 66], [795, 22, 766, 66, "_jsxFileName"], [795, 34, 766, 66], [796, 12, 766, 66, "lineNumber"], [796, 22, 766, 66], [797, 12, 766, 66, "columnNumber"], [797, 24, 766, 66], [798, 10, 766, 66], [798, 17, 767, 28], [798, 18, 767, 29], [799, 8, 767, 29], [800, 10, 767, 29, "fileName"], [800, 18, 767, 29], [800, 20, 767, 29, "_jsxFileName"], [800, 32, 767, 29], [801, 10, 767, 29, "lineNumber"], [801, 20, 767, 29], [802, 10, 767, 29, "columnNumber"], [802, 22, 767, 29], [803, 8, 767, 29], [803, 15, 768, 14], [804, 6, 768, 15], [805, 8, 768, 15, "fileName"], [805, 16, 768, 15], [805, 18, 768, 15, "_jsxFileName"], [805, 30, 768, 15], [806, 8, 768, 15, "lineNumber"], [806, 18, 768, 15], [807, 8, 768, 15, "columnNumber"], [807, 20, 768, 15], [808, 6, 768, 15], [808, 13, 769, 12], [808, 14, 769, 13], [809, 4, 771, 2], [810, 4, 772, 2], [811, 4, 773, 2, "console"], [811, 11, 773, 9], [811, 12, 773, 10, "log"], [811, 15, 773, 13], [811, 16, 773, 14], [811, 55, 773, 53], [811, 56, 773, 54], [812, 4, 775, 2], [812, 24, 776, 4], [812, 28, 776, 4, "_jsxDevRuntime"], [812, 42, 776, 4], [812, 43, 776, 4, "jsxDEV"], [812, 49, 776, 4], [812, 51, 776, 5, "_View"], [812, 56, 776, 5], [812, 57, 776, 5, "default"], [812, 64, 776, 9], [813, 6, 776, 10, "style"], [813, 11, 776, 15], [813, 13, 776, 17, "styles"], [813, 19, 776, 23], [813, 20, 776, 24, "container"], [813, 29, 776, 34], [814, 6, 776, 34, "children"], [814, 14, 776, 34], [814, 30, 778, 6], [814, 34, 778, 6, "_jsxDevRuntime"], [814, 48, 778, 6], [814, 49, 778, 6, "jsxDEV"], [814, 55, 778, 6], [814, 57, 778, 7, "_View"], [814, 62, 778, 7], [814, 63, 778, 7, "default"], [814, 70, 778, 11], [815, 8, 778, 12, "style"], [815, 13, 778, 17], [815, 15, 778, 19, "styles"], [815, 21, 778, 25], [815, 22, 778, 26, "cameraContainer"], [815, 37, 778, 42], [816, 8, 778, 43, "id"], [816, 10, 778, 45], [816, 12, 778, 46], [816, 29, 778, 63], [817, 8, 778, 63, "children"], [817, 16, 778, 63], [817, 32, 779, 8], [817, 36, 779, 8, "_jsxDevRuntime"], [817, 50, 779, 8], [817, 51, 779, 8, "jsxDEV"], [817, 57, 779, 8], [817, 59, 779, 9, "_expoCamera"], [817, 70, 779, 9], [817, 71, 779, 9, "CameraView"], [817, 81, 779, 19], [818, 10, 780, 10, "ref"], [818, 13, 780, 13], [818, 15, 780, 15, "cameraRef"], [818, 24, 780, 25], [819, 10, 781, 10, "style"], [819, 15, 781, 15], [819, 17, 781, 17], [819, 18, 781, 18, "styles"], [819, 24, 781, 24], [819, 25, 781, 25, "camera"], [819, 31, 781, 31], [819, 33, 781, 33], [820, 12, 781, 35, "backgroundColor"], [820, 27, 781, 50], [820, 29, 781, 52], [821, 10, 781, 62], [821, 11, 781, 63], [821, 12, 781, 65], [822, 10, 782, 10, "facing"], [822, 16, 782, 16], [822, 18, 782, 17], [822, 24, 782, 23], [823, 10, 783, 10, "onLayout"], [823, 18, 783, 18], [823, 20, 783, 21, "e"], [823, 21, 783, 22], [823, 25, 783, 27], [824, 12, 784, 12, "console"], [824, 19, 784, 19], [824, 20, 784, 20, "log"], [824, 23, 784, 23], [824, 24, 784, 24], [824, 56, 784, 56], [824, 58, 784, 58, "e"], [824, 59, 784, 59], [824, 60, 784, 60, "nativeEvent"], [824, 71, 784, 71], [824, 72, 784, 72, "layout"], [824, 78, 784, 78], [824, 79, 784, 79], [825, 12, 785, 12, "setViewSize"], [825, 23, 785, 23], [825, 24, 785, 24], [826, 14, 785, 26, "width"], [826, 19, 785, 31], [826, 21, 785, 33, "e"], [826, 22, 785, 34], [826, 23, 785, 35, "nativeEvent"], [826, 34, 785, 46], [826, 35, 785, 47, "layout"], [826, 41, 785, 53], [826, 42, 785, 54, "width"], [826, 47, 785, 59], [827, 14, 785, 61, "height"], [827, 20, 785, 67], [827, 22, 785, 69, "e"], [827, 23, 785, 70], [827, 24, 785, 71, "nativeEvent"], [827, 35, 785, 82], [827, 36, 785, 83, "layout"], [827, 42, 785, 89], [827, 43, 785, 90, "height"], [828, 12, 785, 97], [828, 13, 785, 98], [828, 14, 785, 99], [829, 10, 786, 10], [829, 11, 786, 12], [830, 10, 787, 10, "onCameraReady"], [830, 23, 787, 23], [830, 25, 787, 25, "onCameraReady"], [830, 26, 787, 25], [830, 31, 787, 31], [831, 12, 788, 12, "console"], [831, 19, 788, 19], [831, 20, 788, 20, "log"], [831, 23, 788, 23], [831, 24, 788, 24], [831, 55, 788, 55], [831, 56, 788, 56], [832, 12, 789, 12, "setIsCameraReady"], [832, 28, 789, 28], [832, 29, 789, 29], [832, 33, 789, 33], [832, 34, 789, 34], [832, 35, 789, 35], [832, 36, 789, 36], [833, 10, 790, 10], [833, 11, 790, 12], [834, 10, 791, 10, "onMountError"], [834, 22, 791, 22], [834, 24, 791, 25, "error"], [834, 29, 791, 30], [834, 33, 791, 35], [835, 12, 792, 12, "console"], [835, 19, 792, 19], [835, 20, 792, 20, "error"], [835, 25, 792, 25], [835, 26, 792, 26], [835, 63, 792, 63], [835, 65, 792, 65, "error"], [835, 70, 792, 70], [835, 71, 792, 71], [836, 12, 793, 12, "setErrorMessage"], [836, 27, 793, 27], [836, 28, 793, 28], [836, 57, 793, 57], [836, 58, 793, 58], [837, 12, 794, 12, "setProcessingState"], [837, 30, 794, 30], [837, 31, 794, 31], [837, 38, 794, 38], [837, 39, 794, 39], [838, 10, 795, 10], [839, 8, 795, 12], [840, 10, 795, 12, "fileName"], [840, 18, 795, 12], [840, 20, 795, 12, "_jsxFileName"], [840, 32, 795, 12], [841, 10, 795, 12, "lineNumber"], [841, 20, 795, 12], [842, 10, 795, 12, "columnNumber"], [842, 22, 795, 12], [843, 8, 795, 12], [843, 15, 796, 9], [843, 16, 796, 10], [843, 18, 798, 9], [843, 19, 798, 10, "isCameraReady"], [843, 32, 798, 23], [843, 49, 799, 10], [843, 53, 799, 10, "_jsxDevRuntime"], [843, 67, 799, 10], [843, 68, 799, 10, "jsxDEV"], [843, 74, 799, 10], [843, 76, 799, 11, "_View"], [843, 81, 799, 11], [843, 82, 799, 11, "default"], [843, 89, 799, 15], [844, 10, 799, 16, "style"], [844, 15, 799, 21], [844, 17, 799, 23], [844, 18, 799, 24, "StyleSheet"], [844, 37, 799, 34], [844, 38, 799, 35, "absoluteFill"], [844, 50, 799, 47], [844, 52, 799, 49], [845, 12, 799, 51, "backgroundColor"], [845, 27, 799, 66], [845, 29, 799, 68], [845, 49, 799, 88], [846, 12, 799, 90, "justifyContent"], [846, 26, 799, 104], [846, 28, 799, 106], [846, 36, 799, 114], [847, 12, 799, 116, "alignItems"], [847, 22, 799, 126], [847, 24, 799, 128], [847, 32, 799, 136], [848, 12, 799, 138, "zIndex"], [848, 18, 799, 144], [848, 20, 799, 146], [849, 10, 799, 151], [849, 11, 799, 152], [849, 12, 799, 154], [850, 10, 799, 154, "children"], [850, 18, 799, 154], [850, 33, 800, 12], [850, 37, 800, 12, "_jsxDevRuntime"], [850, 51, 800, 12], [850, 52, 800, 12, "jsxDEV"], [850, 58, 800, 12], [850, 60, 800, 13, "_View"], [850, 65, 800, 13], [850, 66, 800, 13, "default"], [850, 73, 800, 17], [851, 12, 800, 18, "style"], [851, 17, 800, 23], [851, 19, 800, 25], [852, 14, 800, 27, "backgroundColor"], [852, 29, 800, 42], [852, 31, 800, 44], [852, 51, 800, 64], [853, 14, 800, 66, "padding"], [853, 21, 800, 73], [853, 23, 800, 75], [853, 25, 800, 77], [854, 14, 800, 79, "borderRadius"], [854, 26, 800, 91], [854, 28, 800, 93], [854, 30, 800, 95], [855, 14, 800, 97, "alignItems"], [855, 24, 800, 107], [855, 26, 800, 109], [856, 12, 800, 118], [856, 13, 800, 120], [857, 12, 800, 120, "children"], [857, 20, 800, 120], [857, 36, 801, 14], [857, 40, 801, 14, "_jsxDevRuntime"], [857, 54, 801, 14], [857, 55, 801, 14, "jsxDEV"], [857, 61, 801, 14], [857, 63, 801, 15, "_ActivityIndicator"], [857, 81, 801, 15], [857, 82, 801, 15, "default"], [857, 89, 801, 32], [858, 14, 801, 33, "size"], [858, 18, 801, 37], [858, 20, 801, 38], [858, 27, 801, 45], [859, 14, 801, 46, "color"], [859, 19, 801, 51], [859, 21, 801, 52], [859, 30, 801, 61], [860, 14, 801, 62, "style"], [860, 19, 801, 67], [860, 21, 801, 69], [861, 16, 801, 71, "marginBottom"], [861, 28, 801, 83], [861, 30, 801, 85], [862, 14, 801, 88], [863, 12, 801, 90], [864, 14, 801, 90, "fileName"], [864, 22, 801, 90], [864, 24, 801, 90, "_jsxFileName"], [864, 36, 801, 90], [865, 14, 801, 90, "lineNumber"], [865, 24, 801, 90], [866, 14, 801, 90, "columnNumber"], [866, 26, 801, 90], [867, 12, 801, 90], [867, 19, 801, 92], [867, 20, 801, 93], [867, 35, 802, 14], [867, 39, 802, 14, "_jsxDevRuntime"], [867, 53, 802, 14], [867, 54, 802, 14, "jsxDEV"], [867, 60, 802, 14], [867, 62, 802, 15, "_Text"], [867, 67, 802, 15], [867, 68, 802, 15, "default"], [867, 75, 802, 19], [868, 14, 802, 20, "style"], [868, 19, 802, 25], [868, 21, 802, 27], [869, 16, 802, 29, "color"], [869, 21, 802, 34], [869, 23, 802, 36], [869, 29, 802, 42], [870, 16, 802, 44, "fontSize"], [870, 24, 802, 52], [870, 26, 802, 54], [870, 28, 802, 56], [871, 16, 802, 58, "fontWeight"], [871, 26, 802, 68], [871, 28, 802, 70], [872, 14, 802, 76], [872, 15, 802, 78], [873, 14, 802, 78, "children"], [873, 22, 802, 78], [873, 24, 802, 79], [874, 12, 802, 101], [875, 14, 802, 101, "fileName"], [875, 22, 802, 101], [875, 24, 802, 101, "_jsxFileName"], [875, 36, 802, 101], [876, 14, 802, 101, "lineNumber"], [876, 24, 802, 101], [877, 14, 802, 101, "columnNumber"], [877, 26, 802, 101], [878, 12, 802, 101], [878, 19, 802, 107], [878, 20, 802, 108], [878, 35, 803, 14], [878, 39, 803, 14, "_jsxDevRuntime"], [878, 53, 803, 14], [878, 54, 803, 14, "jsxDEV"], [878, 60, 803, 14], [878, 62, 803, 15, "_Text"], [878, 67, 803, 15], [878, 68, 803, 15, "default"], [878, 75, 803, 19], [879, 14, 803, 20, "style"], [879, 19, 803, 25], [879, 21, 803, 27], [880, 16, 803, 29, "color"], [880, 21, 803, 34], [880, 23, 803, 36], [880, 32, 803, 45], [881, 16, 803, 47, "fontSize"], [881, 24, 803, 55], [881, 26, 803, 57], [881, 28, 803, 59], [882, 16, 803, 61, "marginTop"], [882, 25, 803, 70], [882, 27, 803, 72], [883, 14, 803, 74], [883, 15, 803, 76], [884, 14, 803, 76, "children"], [884, 22, 803, 76], [884, 24, 803, 77], [885, 12, 803, 88], [886, 14, 803, 88, "fileName"], [886, 22, 803, 88], [886, 24, 803, 88, "_jsxFileName"], [886, 36, 803, 88], [887, 14, 803, 88, "lineNumber"], [887, 24, 803, 88], [888, 14, 803, 88, "columnNumber"], [888, 26, 803, 88], [889, 12, 803, 88], [889, 19, 803, 94], [889, 20, 803, 95], [890, 10, 803, 95], [891, 12, 803, 95, "fileName"], [891, 20, 803, 95], [891, 22, 803, 95, "_jsxFileName"], [891, 34, 803, 95], [892, 12, 803, 95, "lineNumber"], [892, 22, 803, 95], [893, 12, 803, 95, "columnNumber"], [893, 24, 803, 95], [894, 10, 803, 95], [894, 17, 804, 18], [895, 8, 804, 19], [896, 10, 804, 19, "fileName"], [896, 18, 804, 19], [896, 20, 804, 19, "_jsxFileName"], [896, 32, 804, 19], [897, 10, 804, 19, "lineNumber"], [897, 20, 804, 19], [898, 10, 804, 19, "columnNumber"], [898, 22, 804, 19], [899, 8, 804, 19], [899, 15, 805, 16], [899, 16, 806, 9], [899, 18, 809, 9, "isCameraReady"], [899, 31, 809, 22], [899, 35, 809, 26, "previewBlurEnabled"], [899, 53, 809, 44], [899, 57, 809, 48, "viewSize"], [899, 65, 809, 56], [899, 66, 809, 57, "width"], [899, 71, 809, 62], [899, 74, 809, 65], [899, 75, 809, 66], [899, 92, 810, 10], [899, 96, 810, 10, "_jsxDevRuntime"], [899, 110, 810, 10], [899, 111, 810, 10, "jsxDEV"], [899, 117, 810, 10], [899, 119, 810, 10, "_jsxDevRuntime"], [899, 133, 810, 10], [899, 134, 810, 10, "Fragment"], [899, 142, 810, 10], [900, 10, 810, 10, "children"], [900, 18, 810, 10], [900, 34, 812, 12], [900, 38, 812, 12, "_jsxDevRuntime"], [900, 52, 812, 12], [900, 53, 812, 12, "jsxDEV"], [900, 59, 812, 12], [900, 61, 812, 13, "_LiveFaceCanvas"], [900, 76, 812, 13], [900, 77, 812, 13, "default"], [900, 84, 812, 27], [901, 12, 812, 28, "containerId"], [901, 23, 812, 39], [901, 25, 812, 40], [901, 42, 812, 57], [902, 12, 812, 58, "width"], [902, 17, 812, 63], [902, 19, 812, 65, "viewSize"], [902, 27, 812, 73], [902, 28, 812, 74, "width"], [902, 33, 812, 80], [903, 12, 812, 81, "height"], [903, 18, 812, 87], [903, 20, 812, 89, "viewSize"], [903, 28, 812, 97], [903, 29, 812, 98, "height"], [904, 10, 812, 105], [905, 12, 812, 105, "fileName"], [905, 20, 812, 105], [905, 22, 812, 105, "_jsxFileName"], [905, 34, 812, 105], [906, 12, 812, 105, "lineNumber"], [906, 22, 812, 105], [907, 12, 812, 105, "columnNumber"], [907, 24, 812, 105], [908, 10, 812, 105], [908, 17, 812, 107], [908, 18, 812, 108], [908, 33, 813, 12], [908, 37, 813, 12, "_jsxDevRuntime"], [908, 51, 813, 12], [908, 52, 813, 12, "jsxDEV"], [908, 58, 813, 12], [908, 60, 813, 13, "_View"], [908, 65, 813, 13], [908, 66, 813, 13, "default"], [908, 73, 813, 17], [909, 12, 813, 18, "style"], [909, 17, 813, 23], [909, 19, 813, 25], [909, 20, 813, 26, "StyleSheet"], [909, 39, 813, 36], [909, 40, 813, 37, "absoluteFill"], [909, 52, 813, 49], [909, 54, 813, 51], [910, 14, 813, 53, "pointerEvents"], [910, 27, 813, 66], [910, 29, 813, 68], [911, 12, 813, 75], [911, 13, 813, 76], [911, 14, 813, 78], [912, 12, 813, 78, "children"], [912, 20, 813, 78], [912, 36, 815, 12], [912, 40, 815, 12, "_jsxDevRuntime"], [912, 54, 815, 12], [912, 55, 815, 12, "jsxDEV"], [912, 61, 815, 12], [912, 63, 815, 13, "_expoBlur"], [912, 72, 815, 13], [912, 73, 815, 13, "BlurView"], [912, 81, 815, 21], [913, 14, 815, 22, "intensity"], [913, 23, 815, 31], [913, 25, 815, 33], [913, 27, 815, 36], [914, 14, 815, 37, "tint"], [914, 18, 815, 41], [914, 20, 815, 42], [914, 26, 815, 48], [915, 14, 815, 49, "style"], [915, 19, 815, 54], [915, 21, 815, 56], [915, 22, 815, 57, "styles"], [915, 28, 815, 63], [915, 29, 815, 64, "blurZone"], [915, 37, 815, 72], [915, 39, 815, 74], [916, 16, 816, 14, "left"], [916, 20, 816, 18], [916, 22, 816, 20], [916, 23, 816, 21], [917, 16, 817, 14, "top"], [917, 19, 817, 17], [917, 21, 817, 19, "viewSize"], [917, 29, 817, 27], [917, 30, 817, 28, "height"], [917, 36, 817, 34], [917, 39, 817, 37], [917, 42, 817, 40], [918, 16, 818, 14, "width"], [918, 21, 818, 19], [918, 23, 818, 21, "viewSize"], [918, 31, 818, 29], [918, 32, 818, 30, "width"], [918, 37, 818, 35], [919, 16, 819, 14, "height"], [919, 22, 819, 20], [919, 24, 819, 22, "viewSize"], [919, 32, 819, 30], [919, 33, 819, 31, "height"], [919, 39, 819, 37], [919, 42, 819, 40], [919, 46, 819, 44], [920, 16, 820, 14, "borderRadius"], [920, 28, 820, 26], [920, 30, 820, 28], [921, 14, 821, 12], [921, 15, 821, 13], [922, 12, 821, 15], [923, 14, 821, 15, "fileName"], [923, 22, 821, 15], [923, 24, 821, 15, "_jsxFileName"], [923, 36, 821, 15], [924, 14, 821, 15, "lineNumber"], [924, 24, 821, 15], [925, 14, 821, 15, "columnNumber"], [925, 26, 821, 15], [926, 12, 821, 15], [926, 19, 821, 17], [926, 20, 821, 18], [926, 35, 823, 12], [926, 39, 823, 12, "_jsxDevRuntime"], [926, 53, 823, 12], [926, 54, 823, 12, "jsxDEV"], [926, 60, 823, 12], [926, 62, 823, 13, "_expoBlur"], [926, 71, 823, 13], [926, 72, 823, 13, "BlurView"], [926, 80, 823, 21], [927, 14, 823, 22, "intensity"], [927, 23, 823, 31], [927, 25, 823, 33], [927, 27, 823, 36], [928, 14, 823, 37, "tint"], [928, 18, 823, 41], [928, 20, 823, 42], [928, 26, 823, 48], [929, 14, 823, 49, "style"], [929, 19, 823, 54], [929, 21, 823, 56], [929, 22, 823, 57, "styles"], [929, 28, 823, 63], [929, 29, 823, 64, "blurZone"], [929, 37, 823, 72], [929, 39, 823, 74], [930, 16, 824, 14, "left"], [930, 20, 824, 18], [930, 22, 824, 20], [930, 23, 824, 21], [931, 16, 825, 14, "top"], [931, 19, 825, 17], [931, 21, 825, 19], [931, 22, 825, 20], [932, 16, 826, 14, "width"], [932, 21, 826, 19], [932, 23, 826, 21, "viewSize"], [932, 31, 826, 29], [932, 32, 826, 30, "width"], [932, 37, 826, 35], [933, 16, 827, 14, "height"], [933, 22, 827, 20], [933, 24, 827, 22, "viewSize"], [933, 32, 827, 30], [933, 33, 827, 31, "height"], [933, 39, 827, 37], [933, 42, 827, 40], [933, 45, 827, 43], [934, 16, 828, 14, "borderRadius"], [934, 28, 828, 26], [934, 30, 828, 28], [935, 14, 829, 12], [935, 15, 829, 13], [936, 12, 829, 15], [937, 14, 829, 15, "fileName"], [937, 22, 829, 15], [937, 24, 829, 15, "_jsxFileName"], [937, 36, 829, 15], [938, 14, 829, 15, "lineNumber"], [938, 24, 829, 15], [939, 14, 829, 15, "columnNumber"], [939, 26, 829, 15], [940, 12, 829, 15], [940, 19, 829, 17], [940, 20, 829, 18], [940, 35, 831, 12], [940, 39, 831, 12, "_jsxDevRuntime"], [940, 53, 831, 12], [940, 54, 831, 12, "jsxDEV"], [940, 60, 831, 12], [940, 62, 831, 13, "_expoBlur"], [940, 71, 831, 13], [940, 72, 831, 13, "BlurView"], [940, 80, 831, 21], [941, 14, 831, 22, "intensity"], [941, 23, 831, 31], [941, 25, 831, 33], [941, 27, 831, 36], [942, 14, 831, 37, "tint"], [942, 18, 831, 41], [942, 20, 831, 42], [942, 26, 831, 48], [943, 14, 831, 49, "style"], [943, 19, 831, 54], [943, 21, 831, 56], [943, 22, 831, 57, "styles"], [943, 28, 831, 63], [943, 29, 831, 64, "blurZone"], [943, 37, 831, 72], [943, 39, 831, 74], [944, 16, 832, 14, "left"], [944, 20, 832, 18], [944, 22, 832, 20, "viewSize"], [944, 30, 832, 28], [944, 31, 832, 29, "width"], [944, 36, 832, 34], [944, 39, 832, 37], [944, 42, 832, 40], [944, 45, 832, 44, "viewSize"], [944, 53, 832, 52], [944, 54, 832, 53, "width"], [944, 59, 832, 58], [944, 62, 832, 61], [944, 66, 832, 66], [945, 16, 833, 14, "top"], [945, 19, 833, 17], [945, 21, 833, 19, "viewSize"], [945, 29, 833, 27], [945, 30, 833, 28, "height"], [945, 36, 833, 34], [945, 39, 833, 37], [945, 43, 833, 41], [945, 46, 833, 45, "viewSize"], [945, 54, 833, 53], [945, 55, 833, 54, "width"], [945, 60, 833, 59], [945, 63, 833, 62], [945, 67, 833, 67], [946, 16, 834, 14, "width"], [946, 21, 834, 19], [946, 23, 834, 21, "viewSize"], [946, 31, 834, 29], [946, 32, 834, 30, "width"], [946, 37, 834, 35], [946, 40, 834, 38], [946, 43, 834, 41], [947, 16, 835, 14, "height"], [947, 22, 835, 20], [947, 24, 835, 22, "viewSize"], [947, 32, 835, 30], [947, 33, 835, 31, "width"], [947, 38, 835, 36], [947, 41, 835, 39], [947, 44, 835, 42], [948, 16, 836, 14, "borderRadius"], [948, 28, 836, 26], [948, 30, 836, 29, "viewSize"], [948, 38, 836, 37], [948, 39, 836, 38, "width"], [948, 44, 836, 43], [948, 47, 836, 46], [948, 50, 836, 49], [948, 53, 836, 53], [949, 14, 837, 12], [949, 15, 837, 13], [950, 12, 837, 15], [951, 14, 837, 15, "fileName"], [951, 22, 837, 15], [951, 24, 837, 15, "_jsxFileName"], [951, 36, 837, 15], [952, 14, 837, 15, "lineNumber"], [952, 24, 837, 15], [953, 14, 837, 15, "columnNumber"], [953, 26, 837, 15], [954, 12, 837, 15], [954, 19, 837, 17], [954, 20, 837, 18], [954, 35, 838, 12], [954, 39, 838, 12, "_jsxDevRuntime"], [954, 53, 838, 12], [954, 54, 838, 12, "jsxDEV"], [954, 60, 838, 12], [954, 62, 838, 13, "_expoBlur"], [954, 71, 838, 13], [954, 72, 838, 13, "BlurView"], [954, 80, 838, 21], [955, 14, 838, 22, "intensity"], [955, 23, 838, 31], [955, 25, 838, 33], [955, 27, 838, 36], [956, 14, 838, 37, "tint"], [956, 18, 838, 41], [956, 20, 838, 42], [956, 26, 838, 48], [957, 14, 838, 49, "style"], [957, 19, 838, 54], [957, 21, 838, 56], [957, 22, 838, 57, "styles"], [957, 28, 838, 63], [957, 29, 838, 64, "blurZone"], [957, 37, 838, 72], [957, 39, 838, 74], [958, 16, 839, 14, "left"], [958, 20, 839, 18], [958, 22, 839, 20, "viewSize"], [958, 30, 839, 28], [958, 31, 839, 29, "width"], [958, 36, 839, 34], [958, 39, 839, 37], [958, 42, 839, 40], [958, 45, 839, 44, "viewSize"], [958, 53, 839, 52], [958, 54, 839, 53, "width"], [958, 59, 839, 58], [958, 62, 839, 61], [958, 66, 839, 66], [959, 16, 840, 14, "top"], [959, 19, 840, 17], [959, 21, 840, 19, "viewSize"], [959, 29, 840, 27], [959, 30, 840, 28, "height"], [959, 36, 840, 34], [959, 39, 840, 37], [959, 42, 840, 40], [959, 45, 840, 44, "viewSize"], [959, 53, 840, 52], [959, 54, 840, 53, "width"], [959, 59, 840, 58], [959, 62, 840, 61], [959, 66, 840, 66], [960, 16, 841, 14, "width"], [960, 21, 841, 19], [960, 23, 841, 21, "viewSize"], [960, 31, 841, 29], [960, 32, 841, 30, "width"], [960, 37, 841, 35], [960, 40, 841, 38], [960, 43, 841, 41], [961, 16, 842, 14, "height"], [961, 22, 842, 20], [961, 24, 842, 22, "viewSize"], [961, 32, 842, 30], [961, 33, 842, 31, "width"], [961, 38, 842, 36], [961, 41, 842, 39], [961, 44, 842, 42], [962, 16, 843, 14, "borderRadius"], [962, 28, 843, 26], [962, 30, 843, 29, "viewSize"], [962, 38, 843, 37], [962, 39, 843, 38, "width"], [962, 44, 843, 43], [962, 47, 843, 46], [962, 50, 843, 49], [962, 53, 843, 53], [963, 14, 844, 12], [963, 15, 844, 13], [964, 12, 844, 15], [965, 14, 844, 15, "fileName"], [965, 22, 844, 15], [965, 24, 844, 15, "_jsxFileName"], [965, 36, 844, 15], [966, 14, 844, 15, "lineNumber"], [966, 24, 844, 15], [967, 14, 844, 15, "columnNumber"], [967, 26, 844, 15], [968, 12, 844, 15], [968, 19, 844, 17], [968, 20, 844, 18], [968, 35, 845, 12], [968, 39, 845, 12, "_jsxDevRuntime"], [968, 53, 845, 12], [968, 54, 845, 12, "jsxDEV"], [968, 60, 845, 12], [968, 62, 845, 13, "_expoBlur"], [968, 71, 845, 13], [968, 72, 845, 13, "BlurView"], [968, 80, 845, 21], [969, 14, 845, 22, "intensity"], [969, 23, 845, 31], [969, 25, 845, 33], [969, 27, 845, 36], [970, 14, 845, 37, "tint"], [970, 18, 845, 41], [970, 20, 845, 42], [970, 26, 845, 48], [971, 14, 845, 49, "style"], [971, 19, 845, 54], [971, 21, 845, 56], [971, 22, 845, 57, "styles"], [971, 28, 845, 63], [971, 29, 845, 64, "blurZone"], [971, 37, 845, 72], [971, 39, 845, 74], [972, 16, 846, 14, "left"], [972, 20, 846, 18], [972, 22, 846, 20, "viewSize"], [972, 30, 846, 28], [972, 31, 846, 29, "width"], [972, 36, 846, 34], [972, 39, 846, 37], [972, 42, 846, 40], [972, 45, 846, 44, "viewSize"], [972, 53, 846, 52], [972, 54, 846, 53, "width"], [972, 59, 846, 58], [972, 62, 846, 61], [972, 66, 846, 66], [973, 16, 847, 14, "top"], [973, 19, 847, 17], [973, 21, 847, 19, "viewSize"], [973, 29, 847, 27], [973, 30, 847, 28, "height"], [973, 36, 847, 34], [973, 39, 847, 37], [973, 42, 847, 40], [973, 45, 847, 44, "viewSize"], [973, 53, 847, 52], [973, 54, 847, 53, "width"], [973, 59, 847, 58], [973, 62, 847, 61], [973, 66, 847, 66], [974, 16, 848, 14, "width"], [974, 21, 848, 19], [974, 23, 848, 21, "viewSize"], [974, 31, 848, 29], [974, 32, 848, 30, "width"], [974, 37, 848, 35], [974, 40, 848, 38], [974, 43, 848, 41], [975, 16, 849, 14, "height"], [975, 22, 849, 20], [975, 24, 849, 22, "viewSize"], [975, 32, 849, 30], [975, 33, 849, 31, "width"], [975, 38, 849, 36], [975, 41, 849, 39], [975, 44, 849, 42], [976, 16, 850, 14, "borderRadius"], [976, 28, 850, 26], [976, 30, 850, 29, "viewSize"], [976, 38, 850, 37], [976, 39, 850, 38, "width"], [976, 44, 850, 43], [976, 47, 850, 46], [976, 50, 850, 49], [976, 53, 850, 53], [977, 14, 851, 12], [977, 15, 851, 13], [978, 12, 851, 15], [979, 14, 851, 15, "fileName"], [979, 22, 851, 15], [979, 24, 851, 15, "_jsxFileName"], [979, 36, 851, 15], [980, 14, 851, 15, "lineNumber"], [980, 24, 851, 15], [981, 14, 851, 15, "columnNumber"], [981, 26, 851, 15], [982, 12, 851, 15], [982, 19, 851, 17], [982, 20, 851, 18], [982, 22, 853, 13, "__DEV__"], [982, 29, 853, 20], [982, 46, 854, 14], [982, 50, 854, 14, "_jsxDevRuntime"], [982, 64, 854, 14], [982, 65, 854, 14, "jsxDEV"], [982, 71, 854, 14], [982, 73, 854, 15, "_View"], [982, 78, 854, 15], [982, 79, 854, 15, "default"], [982, 86, 854, 19], [983, 14, 854, 20, "style"], [983, 19, 854, 25], [983, 21, 854, 27, "styles"], [983, 27, 854, 33], [983, 28, 854, 34, "previewChip"], [983, 39, 854, 46], [984, 14, 854, 46, "children"], [984, 22, 854, 46], [984, 37, 855, 16], [984, 41, 855, 16, "_jsxDevRuntime"], [984, 55, 855, 16], [984, 56, 855, 16, "jsxDEV"], [984, 62, 855, 16], [984, 64, 855, 17, "_Text"], [984, 69, 855, 17], [984, 70, 855, 17, "default"], [984, 77, 855, 21], [985, 16, 855, 22, "style"], [985, 21, 855, 27], [985, 23, 855, 29, "styles"], [985, 29, 855, 35], [985, 30, 855, 36, "previewChipText"], [985, 45, 855, 52], [986, 16, 855, 52, "children"], [986, 24, 855, 52], [986, 26, 855, 53], [987, 14, 855, 73], [988, 16, 855, 73, "fileName"], [988, 24, 855, 73], [988, 26, 855, 73, "_jsxFileName"], [988, 38, 855, 73], [989, 16, 855, 73, "lineNumber"], [989, 26, 855, 73], [990, 16, 855, 73, "columnNumber"], [990, 28, 855, 73], [991, 14, 855, 73], [991, 21, 855, 79], [992, 12, 855, 80], [993, 14, 855, 80, "fileName"], [993, 22, 855, 80], [993, 24, 855, 80, "_jsxFileName"], [993, 36, 855, 80], [994, 14, 855, 80, "lineNumber"], [994, 24, 855, 80], [995, 14, 855, 80, "columnNumber"], [995, 26, 855, 80], [996, 12, 855, 80], [996, 19, 856, 20], [996, 20, 857, 13], [997, 10, 857, 13], [998, 12, 857, 13, "fileName"], [998, 20, 857, 13], [998, 22, 857, 13, "_jsxFileName"], [998, 34, 857, 13], [999, 12, 857, 13, "lineNumber"], [999, 22, 857, 13], [1000, 12, 857, 13, "columnNumber"], [1000, 24, 857, 13], [1001, 10, 857, 13], [1001, 17, 858, 18], [1001, 18, 858, 19], [1002, 8, 858, 19], [1002, 23, 859, 12], [1002, 24, 860, 9], [1002, 26, 862, 9, "isCameraReady"], [1002, 39, 862, 22], [1002, 56, 863, 10], [1002, 60, 863, 10, "_jsxDevRuntime"], [1002, 74, 863, 10], [1002, 75, 863, 10, "jsxDEV"], [1002, 81, 863, 10], [1002, 83, 863, 10, "_jsxDevRuntime"], [1002, 97, 863, 10], [1002, 98, 863, 10, "Fragment"], [1002, 106, 863, 10], [1003, 10, 863, 10, "children"], [1003, 18, 863, 10], [1003, 34, 865, 12], [1003, 38, 865, 12, "_jsxDevRuntime"], [1003, 52, 865, 12], [1003, 53, 865, 12, "jsxDEV"], [1003, 59, 865, 12], [1003, 61, 865, 13, "_View"], [1003, 66, 865, 13], [1003, 67, 865, 13, "default"], [1003, 74, 865, 17], [1004, 12, 865, 18, "style"], [1004, 17, 865, 23], [1004, 19, 865, 25, "styles"], [1004, 25, 865, 31], [1004, 26, 865, 32, "headerOverlay"], [1004, 39, 865, 46], [1005, 12, 865, 46, "children"], [1005, 20, 865, 46], [1005, 35, 866, 14], [1005, 39, 866, 14, "_jsxDevRuntime"], [1005, 53, 866, 14], [1005, 54, 866, 14, "jsxDEV"], [1005, 60, 866, 14], [1005, 62, 866, 15, "_View"], [1005, 67, 866, 15], [1005, 68, 866, 15, "default"], [1005, 75, 866, 19], [1006, 14, 866, 20, "style"], [1006, 19, 866, 25], [1006, 21, 866, 27, "styles"], [1006, 27, 866, 33], [1006, 28, 866, 34, "headerContent"], [1006, 41, 866, 48], [1007, 14, 866, 48, "children"], [1007, 22, 866, 48], [1007, 38, 867, 16], [1007, 42, 867, 16, "_jsxDevRuntime"], [1007, 56, 867, 16], [1007, 57, 867, 16, "jsxDEV"], [1007, 63, 867, 16], [1007, 65, 867, 17, "_View"], [1007, 70, 867, 17], [1007, 71, 867, 17, "default"], [1007, 78, 867, 21], [1008, 16, 867, 22, "style"], [1008, 21, 867, 27], [1008, 23, 867, 29, "styles"], [1008, 29, 867, 35], [1008, 30, 867, 36, "headerLeft"], [1008, 40, 867, 47], [1009, 16, 867, 47, "children"], [1009, 24, 867, 47], [1009, 40, 868, 18], [1009, 44, 868, 18, "_jsxDevRuntime"], [1009, 58, 868, 18], [1009, 59, 868, 18, "jsxDEV"], [1009, 65, 868, 18], [1009, 67, 868, 19, "_Text"], [1009, 72, 868, 19], [1009, 73, 868, 19, "default"], [1009, 80, 868, 23], [1010, 18, 868, 24, "style"], [1010, 23, 868, 29], [1010, 25, 868, 31, "styles"], [1010, 31, 868, 37], [1010, 32, 868, 38, "headerTitle"], [1010, 43, 868, 50], [1011, 18, 868, 50, "children"], [1011, 26, 868, 50], [1011, 28, 868, 51], [1012, 16, 868, 62], [1013, 18, 868, 62, "fileName"], [1013, 26, 868, 62], [1013, 28, 868, 62, "_jsxFileName"], [1013, 40, 868, 62], [1014, 18, 868, 62, "lineNumber"], [1014, 28, 868, 62], [1015, 18, 868, 62, "columnNumber"], [1015, 30, 868, 62], [1016, 16, 868, 62], [1016, 23, 868, 68], [1016, 24, 868, 69], [1016, 39, 869, 18], [1016, 43, 869, 18, "_jsxDevRuntime"], [1016, 57, 869, 18], [1016, 58, 869, 18, "jsxDEV"], [1016, 64, 869, 18], [1016, 66, 869, 19, "_View"], [1016, 71, 869, 19], [1016, 72, 869, 19, "default"], [1016, 79, 869, 23], [1017, 18, 869, 24, "style"], [1017, 23, 869, 29], [1017, 25, 869, 31, "styles"], [1017, 31, 869, 37], [1017, 32, 869, 38, "subtitleRow"], [1017, 43, 869, 50], [1018, 18, 869, 50, "children"], [1018, 26, 869, 50], [1018, 42, 870, 20], [1018, 46, 870, 20, "_jsxDevRuntime"], [1018, 60, 870, 20], [1018, 61, 870, 20, "jsxDEV"], [1018, 67, 870, 20], [1018, 69, 870, 21, "_Text"], [1018, 74, 870, 21], [1018, 75, 870, 21, "default"], [1018, 82, 870, 25], [1019, 20, 870, 26, "style"], [1019, 25, 870, 31], [1019, 27, 870, 33, "styles"], [1019, 33, 870, 39], [1019, 34, 870, 40, "webIcon"], [1019, 41, 870, 48], [1020, 20, 870, 48, "children"], [1020, 28, 870, 48], [1020, 30, 870, 49], [1021, 18, 870, 51], [1022, 20, 870, 51, "fileName"], [1022, 28, 870, 51], [1022, 30, 870, 51, "_jsxFileName"], [1022, 42, 870, 51], [1023, 20, 870, 51, "lineNumber"], [1023, 30, 870, 51], [1024, 20, 870, 51, "columnNumber"], [1024, 32, 870, 51], [1025, 18, 870, 51], [1025, 25, 870, 57], [1025, 26, 870, 58], [1025, 41, 871, 20], [1025, 45, 871, 20, "_jsxDevRuntime"], [1025, 59, 871, 20], [1025, 60, 871, 20, "jsxDEV"], [1025, 66, 871, 20], [1025, 68, 871, 21, "_Text"], [1025, 73, 871, 21], [1025, 74, 871, 21, "default"], [1025, 81, 871, 25], [1026, 20, 871, 26, "style"], [1026, 25, 871, 31], [1026, 27, 871, 33, "styles"], [1026, 33, 871, 39], [1026, 34, 871, 40, "headerSubtitle"], [1026, 48, 871, 55], [1027, 20, 871, 55, "children"], [1027, 28, 871, 55], [1027, 30, 871, 56], [1028, 18, 871, 71], [1029, 20, 871, 71, "fileName"], [1029, 28, 871, 71], [1029, 30, 871, 71, "_jsxFileName"], [1029, 42, 871, 71], [1030, 20, 871, 71, "lineNumber"], [1030, 30, 871, 71], [1031, 20, 871, 71, "columnNumber"], [1031, 32, 871, 71], [1032, 18, 871, 71], [1032, 25, 871, 77], [1032, 26, 871, 78], [1033, 16, 871, 78], [1034, 18, 871, 78, "fileName"], [1034, 26, 871, 78], [1034, 28, 871, 78, "_jsxFileName"], [1034, 40, 871, 78], [1035, 18, 871, 78, "lineNumber"], [1035, 28, 871, 78], [1036, 18, 871, 78, "columnNumber"], [1036, 30, 871, 78], [1037, 16, 871, 78], [1037, 23, 872, 24], [1037, 24, 872, 25], [1037, 26, 873, 19, "challengeCode"], [1037, 39, 873, 32], [1037, 56, 874, 20], [1037, 60, 874, 20, "_jsxDevRuntime"], [1037, 74, 874, 20], [1037, 75, 874, 20, "jsxDEV"], [1037, 81, 874, 20], [1037, 83, 874, 21, "_View"], [1037, 88, 874, 21], [1037, 89, 874, 21, "default"], [1037, 96, 874, 25], [1038, 18, 874, 26, "style"], [1038, 23, 874, 31], [1038, 25, 874, 33, "styles"], [1038, 31, 874, 39], [1038, 32, 874, 40, "challengeRow"], [1038, 44, 874, 53], [1039, 18, 874, 53, "children"], [1039, 26, 874, 53], [1039, 42, 875, 22], [1039, 46, 875, 22, "_jsxDevRuntime"], [1039, 60, 875, 22], [1039, 61, 875, 22, "jsxDEV"], [1039, 67, 875, 22], [1039, 69, 875, 23, "_lucideReactNative"], [1039, 87, 875, 23], [1039, 88, 875, 23, "Shield"], [1039, 94, 875, 29], [1040, 20, 875, 30, "size"], [1040, 24, 875, 34], [1040, 26, 875, 36], [1040, 28, 875, 39], [1041, 20, 875, 40, "color"], [1041, 25, 875, 45], [1041, 27, 875, 46], [1042, 18, 875, 52], [1043, 20, 875, 52, "fileName"], [1043, 28, 875, 52], [1043, 30, 875, 52, "_jsxFileName"], [1043, 42, 875, 52], [1044, 20, 875, 52, "lineNumber"], [1044, 30, 875, 52], [1045, 20, 875, 52, "columnNumber"], [1045, 32, 875, 52], [1046, 18, 875, 52], [1046, 25, 875, 54], [1046, 26, 875, 55], [1046, 41, 876, 22], [1046, 45, 876, 22, "_jsxDevRuntime"], [1046, 59, 876, 22], [1046, 60, 876, 22, "jsxDEV"], [1046, 66, 876, 22], [1046, 68, 876, 23, "_Text"], [1046, 73, 876, 23], [1046, 74, 876, 23, "default"], [1046, 81, 876, 27], [1047, 20, 876, 28, "style"], [1047, 25, 876, 33], [1047, 27, 876, 35, "styles"], [1047, 33, 876, 41], [1047, 34, 876, 42, "challengeCode"], [1047, 47, 876, 56], [1048, 20, 876, 56, "children"], [1048, 28, 876, 56], [1048, 30, 876, 58, "challengeCode"], [1049, 18, 876, 71], [1050, 20, 876, 71, "fileName"], [1050, 28, 876, 71], [1050, 30, 876, 71, "_jsxFileName"], [1050, 42, 876, 71], [1051, 20, 876, 71, "lineNumber"], [1051, 30, 876, 71], [1052, 20, 876, 71, "columnNumber"], [1052, 32, 876, 71], [1053, 18, 876, 71], [1053, 25, 876, 78], [1053, 26, 876, 79], [1054, 16, 876, 79], [1055, 18, 876, 79, "fileName"], [1055, 26, 876, 79], [1055, 28, 876, 79, "_jsxFileName"], [1055, 40, 876, 79], [1056, 18, 876, 79, "lineNumber"], [1056, 28, 876, 79], [1057, 18, 876, 79, "columnNumber"], [1057, 30, 876, 79], [1058, 16, 876, 79], [1058, 23, 877, 26], [1058, 24, 878, 19], [1059, 14, 878, 19], [1060, 16, 878, 19, "fileName"], [1060, 24, 878, 19], [1060, 26, 878, 19, "_jsxFileName"], [1060, 38, 878, 19], [1061, 16, 878, 19, "lineNumber"], [1061, 26, 878, 19], [1062, 16, 878, 19, "columnNumber"], [1062, 28, 878, 19], [1063, 14, 878, 19], [1063, 21, 879, 22], [1063, 22, 879, 23], [1063, 37, 880, 16], [1063, 41, 880, 16, "_jsxDevRuntime"], [1063, 55, 880, 16], [1063, 56, 880, 16, "jsxDEV"], [1063, 62, 880, 16], [1063, 64, 880, 17, "_TouchableOpacity"], [1063, 81, 880, 17], [1063, 82, 880, 17, "default"], [1063, 89, 880, 33], [1064, 16, 880, 34, "onPress"], [1064, 23, 880, 41], [1064, 25, 880, 43, "onCancel"], [1064, 33, 880, 52], [1065, 16, 880, 53, "style"], [1065, 21, 880, 58], [1065, 23, 880, 60, "styles"], [1065, 29, 880, 66], [1065, 30, 880, 67, "closeButton"], [1065, 41, 880, 79], [1066, 16, 880, 79, "children"], [1066, 24, 880, 79], [1066, 39, 881, 18], [1066, 43, 881, 18, "_jsxDevRuntime"], [1066, 57, 881, 18], [1066, 58, 881, 18, "jsxDEV"], [1066, 64, 881, 18], [1066, 66, 881, 19, "_lucideReactNative"], [1066, 84, 881, 19], [1066, 85, 881, 19, "X"], [1066, 86, 881, 20], [1067, 18, 881, 21, "size"], [1067, 22, 881, 25], [1067, 24, 881, 27], [1067, 26, 881, 30], [1068, 18, 881, 31, "color"], [1068, 23, 881, 36], [1068, 25, 881, 37], [1069, 16, 881, 43], [1070, 18, 881, 43, "fileName"], [1070, 26, 881, 43], [1070, 28, 881, 43, "_jsxFileName"], [1070, 40, 881, 43], [1071, 18, 881, 43, "lineNumber"], [1071, 28, 881, 43], [1072, 18, 881, 43, "columnNumber"], [1072, 30, 881, 43], [1073, 16, 881, 43], [1073, 23, 881, 45], [1074, 14, 881, 46], [1075, 16, 881, 46, "fileName"], [1075, 24, 881, 46], [1075, 26, 881, 46, "_jsxFileName"], [1075, 38, 881, 46], [1076, 16, 881, 46, "lineNumber"], [1076, 26, 881, 46], [1077, 16, 881, 46, "columnNumber"], [1077, 28, 881, 46], [1078, 14, 881, 46], [1078, 21, 882, 34], [1078, 22, 882, 35], [1079, 12, 882, 35], [1080, 14, 882, 35, "fileName"], [1080, 22, 882, 35], [1080, 24, 882, 35, "_jsxFileName"], [1080, 36, 882, 35], [1081, 14, 882, 35, "lineNumber"], [1081, 24, 882, 35], [1082, 14, 882, 35, "columnNumber"], [1082, 26, 882, 35], [1083, 12, 882, 35], [1083, 19, 883, 20], [1084, 10, 883, 21], [1085, 12, 883, 21, "fileName"], [1085, 20, 883, 21], [1085, 22, 883, 21, "_jsxFileName"], [1085, 34, 883, 21], [1086, 12, 883, 21, "lineNumber"], [1086, 22, 883, 21], [1087, 12, 883, 21, "columnNumber"], [1087, 24, 883, 21], [1088, 10, 883, 21], [1088, 17, 884, 18], [1088, 18, 884, 19], [1088, 33, 886, 12], [1088, 37, 886, 12, "_jsxDevRuntime"], [1088, 51, 886, 12], [1088, 52, 886, 12, "jsxDEV"], [1088, 58, 886, 12], [1088, 60, 886, 13, "_View"], [1088, 65, 886, 13], [1088, 66, 886, 13, "default"], [1088, 73, 886, 17], [1089, 12, 886, 18, "style"], [1089, 17, 886, 23], [1089, 19, 886, 25, "styles"], [1089, 25, 886, 31], [1089, 26, 886, 32, "privacyNotice"], [1089, 39, 886, 46], [1090, 12, 886, 46, "children"], [1090, 20, 886, 46], [1090, 36, 887, 14], [1090, 40, 887, 14, "_jsxDevRuntime"], [1090, 54, 887, 14], [1090, 55, 887, 14, "jsxDEV"], [1090, 61, 887, 14], [1090, 63, 887, 15, "_lucideReactNative"], [1090, 81, 887, 15], [1090, 82, 887, 15, "Shield"], [1090, 88, 887, 21], [1091, 14, 887, 22, "size"], [1091, 18, 887, 26], [1091, 20, 887, 28], [1091, 22, 887, 31], [1092, 14, 887, 32, "color"], [1092, 19, 887, 37], [1092, 21, 887, 38], [1093, 12, 887, 47], [1094, 14, 887, 47, "fileName"], [1094, 22, 887, 47], [1094, 24, 887, 47, "_jsxFileName"], [1094, 36, 887, 47], [1095, 14, 887, 47, "lineNumber"], [1095, 24, 887, 47], [1096, 14, 887, 47, "columnNumber"], [1096, 26, 887, 47], [1097, 12, 887, 47], [1097, 19, 887, 49], [1097, 20, 887, 50], [1097, 35, 888, 14], [1097, 39, 888, 14, "_jsxDevRuntime"], [1097, 53, 888, 14], [1097, 54, 888, 14, "jsxDEV"], [1097, 60, 888, 14], [1097, 62, 888, 15, "_Text"], [1097, 67, 888, 15], [1097, 68, 888, 15, "default"], [1097, 75, 888, 19], [1098, 14, 888, 20, "style"], [1098, 19, 888, 25], [1098, 21, 888, 27, "styles"], [1098, 27, 888, 33], [1098, 28, 888, 34, "privacyText"], [1098, 39, 888, 46], [1099, 14, 888, 46, "children"], [1099, 22, 888, 46], [1099, 24, 888, 47], [1100, 12, 890, 14], [1101, 14, 890, 14, "fileName"], [1101, 22, 890, 14], [1101, 24, 890, 14, "_jsxFileName"], [1101, 36, 890, 14], [1102, 14, 890, 14, "lineNumber"], [1102, 24, 890, 14], [1103, 14, 890, 14, "columnNumber"], [1103, 26, 890, 14], [1104, 12, 890, 14], [1104, 19, 890, 20], [1104, 20, 890, 21], [1105, 10, 890, 21], [1106, 12, 890, 21, "fileName"], [1106, 20, 890, 21], [1106, 22, 890, 21, "_jsxFileName"], [1106, 34, 890, 21], [1107, 12, 890, 21, "lineNumber"], [1107, 22, 890, 21], [1108, 12, 890, 21, "columnNumber"], [1108, 24, 890, 21], [1109, 10, 890, 21], [1109, 17, 891, 18], [1109, 18, 891, 19], [1109, 33, 893, 12], [1109, 37, 893, 12, "_jsxDevRuntime"], [1109, 51, 893, 12], [1109, 52, 893, 12, "jsxDEV"], [1109, 58, 893, 12], [1109, 60, 893, 13, "_View"], [1109, 65, 893, 13], [1109, 66, 893, 13, "default"], [1109, 73, 893, 17], [1110, 12, 893, 18, "style"], [1110, 17, 893, 23], [1110, 19, 893, 25, "styles"], [1110, 25, 893, 31], [1110, 26, 893, 32, "footer<PERSON><PERSON><PERSON>"], [1110, 39, 893, 46], [1111, 12, 893, 46, "children"], [1111, 20, 893, 46], [1111, 36, 894, 14], [1111, 40, 894, 14, "_jsxDevRuntime"], [1111, 54, 894, 14], [1111, 55, 894, 14, "jsxDEV"], [1111, 61, 894, 14], [1111, 63, 894, 15, "_Text"], [1111, 68, 894, 15], [1111, 69, 894, 15, "default"], [1111, 76, 894, 19], [1112, 14, 894, 20, "style"], [1112, 19, 894, 25], [1112, 21, 894, 27, "styles"], [1112, 27, 894, 33], [1112, 28, 894, 34, "instruction"], [1112, 39, 894, 46], [1113, 14, 894, 46, "children"], [1113, 22, 894, 46], [1113, 24, 894, 47], [1114, 12, 896, 14], [1115, 14, 896, 14, "fileName"], [1115, 22, 896, 14], [1115, 24, 896, 14, "_jsxFileName"], [1115, 36, 896, 14], [1116, 14, 896, 14, "lineNumber"], [1116, 24, 896, 14], [1117, 14, 896, 14, "columnNumber"], [1117, 26, 896, 14], [1118, 12, 896, 14], [1118, 19, 896, 20], [1118, 20, 896, 21], [1118, 35, 898, 14], [1118, 39, 898, 14, "_jsxDevRuntime"], [1118, 53, 898, 14], [1118, 54, 898, 14, "jsxDEV"], [1118, 60, 898, 14], [1118, 62, 898, 15, "_TouchableOpacity"], [1118, 79, 898, 15], [1118, 80, 898, 15, "default"], [1118, 87, 898, 31], [1119, 14, 899, 16, "onPress"], [1119, 21, 899, 23], [1119, 23, 899, 25, "capturePhoto"], [1119, 35, 899, 38], [1120, 14, 900, 16, "disabled"], [1120, 22, 900, 24], [1120, 24, 900, 26, "processingState"], [1120, 39, 900, 41], [1120, 44, 900, 46], [1120, 50, 900, 52], [1120, 54, 900, 56], [1120, 55, 900, 57, "isCameraReady"], [1120, 68, 900, 71], [1121, 14, 901, 16, "style"], [1121, 19, 901, 21], [1121, 21, 901, 23], [1121, 22, 902, 18, "styles"], [1121, 28, 902, 24], [1121, 29, 902, 25, "shutterButton"], [1121, 42, 902, 38], [1121, 44, 903, 18, "processingState"], [1121, 59, 903, 33], [1121, 64, 903, 38], [1121, 70, 903, 44], [1121, 74, 903, 48, "styles"], [1121, 80, 903, 54], [1121, 81, 903, 55, "shutterButtonDisabled"], [1121, 102, 903, 76], [1121, 103, 904, 18], [1122, 14, 904, 18, "children"], [1122, 22, 904, 18], [1122, 24, 906, 17, "processingState"], [1122, 39, 906, 32], [1122, 44, 906, 37], [1122, 50, 906, 43], [1122, 66, 907, 18], [1122, 70, 907, 18, "_jsxDevRuntime"], [1122, 84, 907, 18], [1122, 85, 907, 18, "jsxDEV"], [1122, 91, 907, 18], [1122, 93, 907, 19, "_View"], [1122, 98, 907, 19], [1122, 99, 907, 19, "default"], [1122, 106, 907, 23], [1123, 16, 907, 24, "style"], [1123, 21, 907, 29], [1123, 23, 907, 31, "styles"], [1123, 29, 907, 37], [1123, 30, 907, 38, "shutterInner"], [1124, 14, 907, 51], [1125, 16, 907, 51, "fileName"], [1125, 24, 907, 51], [1125, 26, 907, 51, "_jsxFileName"], [1125, 38, 907, 51], [1126, 16, 907, 51, "lineNumber"], [1126, 26, 907, 51], [1127, 16, 907, 51, "columnNumber"], [1127, 28, 907, 51], [1128, 14, 907, 51], [1128, 21, 907, 53], [1128, 22, 907, 54], [1128, 38, 909, 18], [1128, 42, 909, 18, "_jsxDevRuntime"], [1128, 56, 909, 18], [1128, 57, 909, 18, "jsxDEV"], [1128, 63, 909, 18], [1128, 65, 909, 19, "_ActivityIndicator"], [1128, 83, 909, 19], [1128, 84, 909, 19, "default"], [1128, 91, 909, 36], [1129, 16, 909, 37, "size"], [1129, 20, 909, 41], [1129, 22, 909, 42], [1129, 29, 909, 49], [1130, 16, 909, 50, "color"], [1130, 21, 909, 55], [1130, 23, 909, 56], [1131, 14, 909, 65], [1132, 16, 909, 65, "fileName"], [1132, 24, 909, 65], [1132, 26, 909, 65, "_jsxFileName"], [1132, 38, 909, 65], [1133, 16, 909, 65, "lineNumber"], [1133, 26, 909, 65], [1134, 16, 909, 65, "columnNumber"], [1134, 28, 909, 65], [1135, 14, 909, 65], [1135, 21, 909, 67], [1136, 12, 910, 17], [1137, 14, 910, 17, "fileName"], [1137, 22, 910, 17], [1137, 24, 910, 17, "_jsxFileName"], [1137, 36, 910, 17], [1138, 14, 910, 17, "lineNumber"], [1138, 24, 910, 17], [1139, 14, 910, 17, "columnNumber"], [1139, 26, 910, 17], [1140, 12, 910, 17], [1140, 19, 911, 32], [1140, 20, 911, 33], [1140, 35, 912, 14], [1140, 39, 912, 14, "_jsxDevRuntime"], [1140, 53, 912, 14], [1140, 54, 912, 14, "jsxDEV"], [1140, 60, 912, 14], [1140, 62, 912, 15, "_Text"], [1140, 67, 912, 15], [1140, 68, 912, 15, "default"], [1140, 75, 912, 19], [1141, 14, 912, 20, "style"], [1141, 19, 912, 25], [1141, 21, 912, 27, "styles"], [1141, 27, 912, 33], [1141, 28, 912, 34, "privacyNote"], [1141, 39, 912, 46], [1142, 14, 912, 46, "children"], [1142, 22, 912, 46], [1142, 24, 912, 47], [1143, 12, 914, 14], [1144, 14, 914, 14, "fileName"], [1144, 22, 914, 14], [1144, 24, 914, 14, "_jsxFileName"], [1144, 36, 914, 14], [1145, 14, 914, 14, "lineNumber"], [1145, 24, 914, 14], [1146, 14, 914, 14, "columnNumber"], [1146, 26, 914, 14], [1147, 12, 914, 14], [1147, 19, 914, 20], [1147, 20, 914, 21], [1148, 10, 914, 21], [1149, 12, 914, 21, "fileName"], [1149, 20, 914, 21], [1149, 22, 914, 21, "_jsxFileName"], [1149, 34, 914, 21], [1150, 12, 914, 21, "lineNumber"], [1150, 22, 914, 21], [1151, 12, 914, 21, "columnNumber"], [1151, 24, 914, 21], [1152, 10, 914, 21], [1152, 17, 915, 18], [1152, 18, 915, 19], [1153, 8, 915, 19], [1153, 23, 916, 12], [1153, 24, 917, 9], [1154, 6, 917, 9], [1155, 8, 917, 9, "fileName"], [1155, 16, 917, 9], [1155, 18, 917, 9, "_jsxFileName"], [1155, 30, 917, 9], [1156, 8, 917, 9, "lineNumber"], [1156, 18, 917, 9], [1157, 8, 917, 9, "columnNumber"], [1157, 20, 917, 9], [1158, 6, 917, 9], [1158, 13, 918, 12], [1158, 14, 918, 13], [1158, 29, 920, 6], [1158, 33, 920, 6, "_jsxDevRuntime"], [1158, 47, 920, 6], [1158, 48, 920, 6, "jsxDEV"], [1158, 54, 920, 6], [1158, 56, 920, 7, "_Modal"], [1158, 62, 920, 7], [1158, 63, 920, 7, "default"], [1158, 70, 920, 12], [1159, 8, 921, 8, "visible"], [1159, 15, 921, 15], [1159, 17, 921, 17, "processingState"], [1159, 32, 921, 32], [1159, 37, 921, 37], [1159, 43, 921, 43], [1159, 47, 921, 47, "processingState"], [1159, 62, 921, 62], [1159, 67, 921, 67], [1159, 74, 921, 75], [1160, 8, 922, 8, "transparent"], [1160, 19, 922, 19], [1161, 8, 923, 8, "animationType"], [1161, 21, 923, 21], [1161, 23, 923, 22], [1161, 29, 923, 28], [1162, 8, 923, 28, "children"], [1162, 16, 923, 28], [1162, 31, 925, 8], [1162, 35, 925, 8, "_jsxDevRuntime"], [1162, 49, 925, 8], [1162, 50, 925, 8, "jsxDEV"], [1162, 56, 925, 8], [1162, 58, 925, 9, "_View"], [1162, 63, 925, 9], [1162, 64, 925, 9, "default"], [1162, 71, 925, 13], [1163, 10, 925, 14, "style"], [1163, 15, 925, 19], [1163, 17, 925, 21, "styles"], [1163, 23, 925, 27], [1163, 24, 925, 28, "processingModal"], [1163, 39, 925, 44], [1164, 10, 925, 44, "children"], [1164, 18, 925, 44], [1164, 33, 926, 10], [1164, 37, 926, 10, "_jsxDevRuntime"], [1164, 51, 926, 10], [1164, 52, 926, 10, "jsxDEV"], [1164, 58, 926, 10], [1164, 60, 926, 11, "_View"], [1164, 65, 926, 11], [1164, 66, 926, 11, "default"], [1164, 73, 926, 15], [1165, 12, 926, 16, "style"], [1165, 17, 926, 21], [1165, 19, 926, 23, "styles"], [1165, 25, 926, 29], [1165, 26, 926, 30, "processingContent"], [1165, 43, 926, 48], [1166, 12, 926, 48, "children"], [1166, 20, 926, 48], [1166, 36, 927, 12], [1166, 40, 927, 12, "_jsxDevRuntime"], [1166, 54, 927, 12], [1166, 55, 927, 12, "jsxDEV"], [1166, 61, 927, 12], [1166, 63, 927, 13, "_ActivityIndicator"], [1166, 81, 927, 13], [1166, 82, 927, 13, "default"], [1166, 89, 927, 30], [1167, 14, 927, 31, "size"], [1167, 18, 927, 35], [1167, 20, 927, 36], [1167, 27, 927, 43], [1168, 14, 927, 44, "color"], [1168, 19, 927, 49], [1168, 21, 927, 50], [1169, 12, 927, 59], [1170, 14, 927, 59, "fileName"], [1170, 22, 927, 59], [1170, 24, 927, 59, "_jsxFileName"], [1170, 36, 927, 59], [1171, 14, 927, 59, "lineNumber"], [1171, 24, 927, 59], [1172, 14, 927, 59, "columnNumber"], [1172, 26, 927, 59], [1173, 12, 927, 59], [1173, 19, 927, 61], [1173, 20, 927, 62], [1173, 35, 929, 12], [1173, 39, 929, 12, "_jsxDevRuntime"], [1173, 53, 929, 12], [1173, 54, 929, 12, "jsxDEV"], [1173, 60, 929, 12], [1173, 62, 929, 13, "_Text"], [1173, 67, 929, 13], [1173, 68, 929, 13, "default"], [1173, 75, 929, 17], [1174, 14, 929, 18, "style"], [1174, 19, 929, 23], [1174, 21, 929, 25, "styles"], [1174, 27, 929, 31], [1174, 28, 929, 32, "processingTitle"], [1174, 43, 929, 48], [1175, 14, 929, 48, "children"], [1175, 22, 929, 48], [1175, 25, 930, 15, "processingState"], [1175, 40, 930, 30], [1175, 45, 930, 35], [1175, 56, 930, 46], [1175, 60, 930, 50], [1175, 80, 930, 70], [1175, 82, 931, 15, "processingState"], [1175, 97, 931, 30], [1175, 102, 931, 35], [1175, 113, 931, 46], [1175, 117, 931, 50], [1175, 146, 931, 79], [1175, 148, 932, 15, "processingState"], [1175, 163, 932, 30], [1175, 168, 932, 35], [1175, 180, 932, 47], [1175, 184, 932, 51], [1175, 216, 932, 83], [1175, 218, 933, 15, "processingState"], [1175, 233, 933, 30], [1175, 238, 933, 35], [1175, 249, 933, 46], [1175, 253, 933, 50], [1175, 275, 933, 72], [1176, 12, 933, 72], [1177, 14, 933, 72, "fileName"], [1177, 22, 933, 72], [1177, 24, 933, 72, "_jsxFileName"], [1177, 36, 933, 72], [1178, 14, 933, 72, "lineNumber"], [1178, 24, 933, 72], [1179, 14, 933, 72, "columnNumber"], [1179, 26, 933, 72], [1180, 12, 933, 72], [1180, 19, 934, 18], [1180, 20, 934, 19], [1180, 35, 935, 12], [1180, 39, 935, 12, "_jsxDevRuntime"], [1180, 53, 935, 12], [1180, 54, 935, 12, "jsxDEV"], [1180, 60, 935, 12], [1180, 62, 935, 13, "_View"], [1180, 67, 935, 13], [1180, 68, 935, 13, "default"], [1180, 75, 935, 17], [1181, 14, 935, 18, "style"], [1181, 19, 935, 23], [1181, 21, 935, 25, "styles"], [1181, 27, 935, 31], [1181, 28, 935, 32, "progressBar"], [1181, 39, 935, 44], [1182, 14, 935, 44, "children"], [1182, 22, 935, 44], [1182, 37, 936, 14], [1182, 41, 936, 14, "_jsxDevRuntime"], [1182, 55, 936, 14], [1182, 56, 936, 14, "jsxDEV"], [1182, 62, 936, 14], [1182, 64, 936, 15, "_View"], [1182, 69, 936, 15], [1182, 70, 936, 15, "default"], [1182, 77, 936, 19], [1183, 16, 937, 16, "style"], [1183, 21, 937, 21], [1183, 23, 937, 23], [1183, 24, 938, 18, "styles"], [1183, 30, 938, 24], [1183, 31, 938, 25, "progressFill"], [1183, 43, 938, 37], [1183, 45, 939, 18], [1184, 18, 939, 20, "width"], [1184, 23, 939, 25], [1184, 25, 939, 27], [1184, 28, 939, 30, "processingProgress"], [1184, 46, 939, 48], [1185, 16, 939, 52], [1185, 17, 939, 53], [1186, 14, 940, 18], [1187, 16, 940, 18, "fileName"], [1187, 24, 940, 18], [1187, 26, 940, 18, "_jsxFileName"], [1187, 38, 940, 18], [1188, 16, 940, 18, "lineNumber"], [1188, 26, 940, 18], [1189, 16, 940, 18, "columnNumber"], [1189, 28, 940, 18], [1190, 14, 940, 18], [1190, 21, 941, 15], [1191, 12, 941, 16], [1192, 14, 941, 16, "fileName"], [1192, 22, 941, 16], [1192, 24, 941, 16, "_jsxFileName"], [1192, 36, 941, 16], [1193, 14, 941, 16, "lineNumber"], [1193, 24, 941, 16], [1194, 14, 941, 16, "columnNumber"], [1194, 26, 941, 16], [1195, 12, 941, 16], [1195, 19, 942, 18], [1195, 20, 942, 19], [1195, 35, 943, 12], [1195, 39, 943, 12, "_jsxDevRuntime"], [1195, 53, 943, 12], [1195, 54, 943, 12, "jsxDEV"], [1195, 60, 943, 12], [1195, 62, 943, 13, "_Text"], [1195, 67, 943, 13], [1195, 68, 943, 13, "default"], [1195, 75, 943, 17], [1196, 14, 943, 18, "style"], [1196, 19, 943, 23], [1196, 21, 943, 25, "styles"], [1196, 27, 943, 31], [1196, 28, 943, 32, "processingDescription"], [1196, 49, 943, 54], [1197, 14, 943, 54, "children"], [1197, 22, 943, 54], [1197, 25, 944, 15, "processingState"], [1197, 40, 944, 30], [1197, 45, 944, 35], [1197, 56, 944, 46], [1197, 60, 944, 50], [1197, 89, 944, 79], [1197, 91, 945, 15, "processingState"], [1197, 106, 945, 30], [1197, 111, 945, 35], [1197, 122, 945, 46], [1197, 126, 945, 50], [1197, 164, 945, 88], [1197, 166, 946, 15, "processingState"], [1197, 181, 946, 30], [1197, 186, 946, 35], [1197, 198, 946, 47], [1197, 202, 946, 51], [1197, 247, 946, 96], [1197, 249, 947, 15, "processingState"], [1197, 264, 947, 30], [1197, 269, 947, 35], [1197, 280, 947, 46], [1197, 284, 947, 50], [1197, 325, 947, 91], [1198, 12, 947, 91], [1199, 14, 947, 91, "fileName"], [1199, 22, 947, 91], [1199, 24, 947, 91, "_jsxFileName"], [1199, 36, 947, 91], [1200, 14, 947, 91, "lineNumber"], [1200, 24, 947, 91], [1201, 14, 947, 91, "columnNumber"], [1201, 26, 947, 91], [1202, 12, 947, 91], [1202, 19, 948, 18], [1202, 20, 948, 19], [1202, 22, 949, 13, "processingState"], [1202, 37, 949, 28], [1202, 42, 949, 33], [1202, 53, 949, 44], [1202, 70, 950, 14], [1202, 74, 950, 14, "_jsxDevRuntime"], [1202, 88, 950, 14], [1202, 89, 950, 14, "jsxDEV"], [1202, 95, 950, 14], [1202, 97, 950, 15, "_lucideReactNative"], [1202, 115, 950, 15], [1202, 116, 950, 15, "CheckCircle"], [1202, 127, 950, 26], [1203, 14, 950, 27, "size"], [1203, 18, 950, 31], [1203, 20, 950, 33], [1203, 22, 950, 36], [1204, 14, 950, 37, "color"], [1204, 19, 950, 42], [1204, 21, 950, 43], [1204, 30, 950, 52], [1205, 14, 950, 53, "style"], [1205, 19, 950, 58], [1205, 21, 950, 60, "styles"], [1205, 27, 950, 66], [1205, 28, 950, 67, "successIcon"], [1206, 12, 950, 79], [1207, 14, 950, 79, "fileName"], [1207, 22, 950, 79], [1207, 24, 950, 79, "_jsxFileName"], [1207, 36, 950, 79], [1208, 14, 950, 79, "lineNumber"], [1208, 24, 950, 79], [1209, 14, 950, 79, "columnNumber"], [1209, 26, 950, 79], [1210, 12, 950, 79], [1210, 19, 950, 81], [1210, 20, 951, 13], [1211, 10, 951, 13], [1212, 12, 951, 13, "fileName"], [1212, 20, 951, 13], [1212, 22, 951, 13, "_jsxFileName"], [1212, 34, 951, 13], [1213, 12, 951, 13, "lineNumber"], [1213, 22, 951, 13], [1214, 12, 951, 13, "columnNumber"], [1214, 24, 951, 13], [1215, 10, 951, 13], [1215, 17, 952, 16], [1216, 8, 952, 17], [1217, 10, 952, 17, "fileName"], [1217, 18, 952, 17], [1217, 20, 952, 17, "_jsxFileName"], [1217, 32, 952, 17], [1218, 10, 952, 17, "lineNumber"], [1218, 20, 952, 17], [1219, 10, 952, 17, "columnNumber"], [1219, 22, 952, 17], [1220, 8, 952, 17], [1220, 15, 953, 14], [1221, 6, 953, 15], [1222, 8, 953, 15, "fileName"], [1222, 16, 953, 15], [1222, 18, 953, 15, "_jsxFileName"], [1222, 30, 953, 15], [1223, 8, 953, 15, "lineNumber"], [1223, 18, 953, 15], [1224, 8, 953, 15, "columnNumber"], [1224, 20, 953, 15], [1225, 6, 953, 15], [1225, 13, 954, 13], [1225, 14, 954, 14], [1225, 29, 956, 6], [1225, 33, 956, 6, "_jsxDevRuntime"], [1225, 47, 956, 6], [1225, 48, 956, 6, "jsxDEV"], [1225, 54, 956, 6], [1225, 56, 956, 7, "_Modal"], [1225, 62, 956, 7], [1225, 63, 956, 7, "default"], [1225, 70, 956, 12], [1226, 8, 957, 8, "visible"], [1226, 15, 957, 15], [1226, 17, 957, 17, "processingState"], [1226, 32, 957, 32], [1226, 37, 957, 37], [1226, 44, 957, 45], [1227, 8, 958, 8, "transparent"], [1227, 19, 958, 19], [1228, 8, 959, 8, "animationType"], [1228, 21, 959, 21], [1228, 23, 959, 22], [1228, 29, 959, 28], [1229, 8, 959, 28, "children"], [1229, 16, 959, 28], [1229, 31, 961, 8], [1229, 35, 961, 8, "_jsxDevRuntime"], [1229, 49, 961, 8], [1229, 50, 961, 8, "jsxDEV"], [1229, 56, 961, 8], [1229, 58, 961, 9, "_View"], [1229, 63, 961, 9], [1229, 64, 961, 9, "default"], [1229, 71, 961, 13], [1230, 10, 961, 14, "style"], [1230, 15, 961, 19], [1230, 17, 961, 21, "styles"], [1230, 23, 961, 27], [1230, 24, 961, 28, "processingModal"], [1230, 39, 961, 44], [1231, 10, 961, 44, "children"], [1231, 18, 961, 44], [1231, 33, 962, 10], [1231, 37, 962, 10, "_jsxDevRuntime"], [1231, 51, 962, 10], [1231, 52, 962, 10, "jsxDEV"], [1231, 58, 962, 10], [1231, 60, 962, 11, "_View"], [1231, 65, 962, 11], [1231, 66, 962, 11, "default"], [1231, 73, 962, 15], [1232, 12, 962, 16, "style"], [1232, 17, 962, 21], [1232, 19, 962, 23, "styles"], [1232, 25, 962, 29], [1232, 26, 962, 30, "errorContent"], [1232, 38, 962, 43], [1233, 12, 962, 43, "children"], [1233, 20, 962, 43], [1233, 36, 963, 12], [1233, 40, 963, 12, "_jsxDevRuntime"], [1233, 54, 963, 12], [1233, 55, 963, 12, "jsxDEV"], [1233, 61, 963, 12], [1233, 63, 963, 13, "_lucideReactNative"], [1233, 81, 963, 13], [1233, 82, 963, 13, "X"], [1233, 83, 963, 14], [1234, 14, 963, 15, "size"], [1234, 18, 963, 19], [1234, 20, 963, 21], [1234, 22, 963, 24], [1235, 14, 963, 25, "color"], [1235, 19, 963, 30], [1235, 21, 963, 31], [1236, 12, 963, 40], [1237, 14, 963, 40, "fileName"], [1237, 22, 963, 40], [1237, 24, 963, 40, "_jsxFileName"], [1237, 36, 963, 40], [1238, 14, 963, 40, "lineNumber"], [1238, 24, 963, 40], [1239, 14, 963, 40, "columnNumber"], [1239, 26, 963, 40], [1240, 12, 963, 40], [1240, 19, 963, 42], [1240, 20, 963, 43], [1240, 35, 964, 12], [1240, 39, 964, 12, "_jsxDevRuntime"], [1240, 53, 964, 12], [1240, 54, 964, 12, "jsxDEV"], [1240, 60, 964, 12], [1240, 62, 964, 13, "_Text"], [1240, 67, 964, 13], [1240, 68, 964, 13, "default"], [1240, 75, 964, 17], [1241, 14, 964, 18, "style"], [1241, 19, 964, 23], [1241, 21, 964, 25, "styles"], [1241, 27, 964, 31], [1241, 28, 964, 32, "errorTitle"], [1241, 38, 964, 43], [1242, 14, 964, 43, "children"], [1242, 22, 964, 43], [1242, 24, 964, 44], [1243, 12, 964, 61], [1244, 14, 964, 61, "fileName"], [1244, 22, 964, 61], [1244, 24, 964, 61, "_jsxFileName"], [1244, 36, 964, 61], [1245, 14, 964, 61, "lineNumber"], [1245, 24, 964, 61], [1246, 14, 964, 61, "columnNumber"], [1246, 26, 964, 61], [1247, 12, 964, 61], [1247, 19, 964, 67], [1247, 20, 964, 68], [1247, 35, 965, 12], [1247, 39, 965, 12, "_jsxDevRuntime"], [1247, 53, 965, 12], [1247, 54, 965, 12, "jsxDEV"], [1247, 60, 965, 12], [1247, 62, 965, 13, "_Text"], [1247, 67, 965, 13], [1247, 68, 965, 13, "default"], [1247, 75, 965, 17], [1248, 14, 965, 18, "style"], [1248, 19, 965, 23], [1248, 21, 965, 25, "styles"], [1248, 27, 965, 31], [1248, 28, 965, 32, "errorMessage"], [1248, 40, 965, 45], [1249, 14, 965, 45, "children"], [1249, 22, 965, 45], [1249, 24, 965, 47, "errorMessage"], [1250, 12, 965, 59], [1251, 14, 965, 59, "fileName"], [1251, 22, 965, 59], [1251, 24, 965, 59, "_jsxFileName"], [1251, 36, 965, 59], [1252, 14, 965, 59, "lineNumber"], [1252, 24, 965, 59], [1253, 14, 965, 59, "columnNumber"], [1253, 26, 965, 59], [1254, 12, 965, 59], [1254, 19, 965, 66], [1254, 20, 965, 67], [1254, 35, 966, 12], [1254, 39, 966, 12, "_jsxDevRuntime"], [1254, 53, 966, 12], [1254, 54, 966, 12, "jsxDEV"], [1254, 60, 966, 12], [1254, 62, 966, 13, "_TouchableOpacity"], [1254, 79, 966, 13], [1254, 80, 966, 13, "default"], [1254, 87, 966, 29], [1255, 14, 967, 14, "onPress"], [1255, 21, 967, 21], [1255, 23, 967, 23, "retryCapture"], [1255, 35, 967, 36], [1256, 14, 968, 14, "style"], [1256, 19, 968, 19], [1256, 21, 968, 21, "styles"], [1256, 27, 968, 27], [1256, 28, 968, 28, "primaryButton"], [1256, 41, 968, 42], [1257, 14, 968, 42, "children"], [1257, 22, 968, 42], [1257, 37, 970, 14], [1257, 41, 970, 14, "_jsxDevRuntime"], [1257, 55, 970, 14], [1257, 56, 970, 14, "jsxDEV"], [1257, 62, 970, 14], [1257, 64, 970, 15, "_Text"], [1257, 69, 970, 15], [1257, 70, 970, 15, "default"], [1257, 77, 970, 19], [1258, 16, 970, 20, "style"], [1258, 21, 970, 25], [1258, 23, 970, 27, "styles"], [1258, 29, 970, 33], [1258, 30, 970, 34, "primaryButtonText"], [1258, 47, 970, 52], [1259, 16, 970, 52, "children"], [1259, 24, 970, 52], [1259, 26, 970, 53], [1260, 14, 970, 62], [1261, 16, 970, 62, "fileName"], [1261, 24, 970, 62], [1261, 26, 970, 62, "_jsxFileName"], [1261, 38, 970, 62], [1262, 16, 970, 62, "lineNumber"], [1262, 26, 970, 62], [1263, 16, 970, 62, "columnNumber"], [1263, 28, 970, 62], [1264, 14, 970, 62], [1264, 21, 970, 68], [1265, 12, 970, 69], [1266, 14, 970, 69, "fileName"], [1266, 22, 970, 69], [1266, 24, 970, 69, "_jsxFileName"], [1266, 36, 970, 69], [1267, 14, 970, 69, "lineNumber"], [1267, 24, 970, 69], [1268, 14, 970, 69, "columnNumber"], [1268, 26, 970, 69], [1269, 12, 970, 69], [1269, 19, 971, 30], [1269, 20, 971, 31], [1269, 35, 972, 12], [1269, 39, 972, 12, "_jsxDevRuntime"], [1269, 53, 972, 12], [1269, 54, 972, 12, "jsxDEV"], [1269, 60, 972, 12], [1269, 62, 972, 13, "_TouchableOpacity"], [1269, 79, 972, 13], [1269, 80, 972, 13, "default"], [1269, 87, 972, 29], [1270, 14, 973, 14, "onPress"], [1270, 21, 973, 21], [1270, 23, 973, 23, "onCancel"], [1270, 31, 973, 32], [1271, 14, 974, 14, "style"], [1271, 19, 974, 19], [1271, 21, 974, 21, "styles"], [1271, 27, 974, 27], [1271, 28, 974, 28, "secondaryButton"], [1271, 43, 974, 44], [1272, 14, 974, 44, "children"], [1272, 22, 974, 44], [1272, 37, 976, 14], [1272, 41, 976, 14, "_jsxDevRuntime"], [1272, 55, 976, 14], [1272, 56, 976, 14, "jsxDEV"], [1272, 62, 976, 14], [1272, 64, 976, 15, "_Text"], [1272, 69, 976, 15], [1272, 70, 976, 15, "default"], [1272, 77, 976, 19], [1273, 16, 976, 20, "style"], [1273, 21, 976, 25], [1273, 23, 976, 27, "styles"], [1273, 29, 976, 33], [1273, 30, 976, 34, "secondaryButtonText"], [1273, 49, 976, 54], [1274, 16, 976, 54, "children"], [1274, 24, 976, 54], [1274, 26, 976, 55], [1275, 14, 976, 61], [1276, 16, 976, 61, "fileName"], [1276, 24, 976, 61], [1276, 26, 976, 61, "_jsxFileName"], [1276, 38, 976, 61], [1277, 16, 976, 61, "lineNumber"], [1277, 26, 976, 61], [1278, 16, 976, 61, "columnNumber"], [1278, 28, 976, 61], [1279, 14, 976, 61], [1279, 21, 976, 67], [1280, 12, 976, 68], [1281, 14, 976, 68, "fileName"], [1281, 22, 976, 68], [1281, 24, 976, 68, "_jsxFileName"], [1281, 36, 976, 68], [1282, 14, 976, 68, "lineNumber"], [1282, 24, 976, 68], [1283, 14, 976, 68, "columnNumber"], [1283, 26, 976, 68], [1284, 12, 976, 68], [1284, 19, 977, 30], [1284, 20, 977, 31], [1285, 10, 977, 31], [1286, 12, 977, 31, "fileName"], [1286, 20, 977, 31], [1286, 22, 977, 31, "_jsxFileName"], [1286, 34, 977, 31], [1287, 12, 977, 31, "lineNumber"], [1287, 22, 977, 31], [1288, 12, 977, 31, "columnNumber"], [1288, 24, 977, 31], [1289, 10, 977, 31], [1289, 17, 978, 16], [1290, 8, 978, 17], [1291, 10, 978, 17, "fileName"], [1291, 18, 978, 17], [1291, 20, 978, 17, "_jsxFileName"], [1291, 32, 978, 17], [1292, 10, 978, 17, "lineNumber"], [1292, 20, 978, 17], [1293, 10, 978, 17, "columnNumber"], [1293, 22, 978, 17], [1294, 8, 978, 17], [1294, 15, 979, 14], [1295, 6, 979, 15], [1296, 8, 979, 15, "fileName"], [1296, 16, 979, 15], [1296, 18, 979, 15, "_jsxFileName"], [1296, 30, 979, 15], [1297, 8, 979, 15, "lineNumber"], [1297, 18, 979, 15], [1298, 8, 979, 15, "columnNumber"], [1298, 20, 979, 15], [1299, 6, 979, 15], [1299, 13, 980, 13], [1299, 14, 980, 14], [1300, 4, 980, 14], [1301, 6, 980, 14, "fileName"], [1301, 14, 980, 14], [1301, 16, 980, 14, "_jsxFileName"], [1301, 28, 980, 14], [1302, 6, 980, 14, "lineNumber"], [1302, 16, 980, 14], [1303, 6, 980, 14, "columnNumber"], [1303, 18, 980, 14], [1304, 4, 980, 14], [1304, 11, 981, 10], [1304, 12, 981, 11], [1305, 2, 983, 0], [1306, 2, 983, 1, "_s"], [1306, 4, 983, 1], [1306, 5, 51, 24, "EchoCameraWeb"], [1306, 18, 51, 37], [1307, 4, 51, 37], [1307, 12, 58, 42, "useCameraPermissions"], [1307, 44, 58, 62], [1307, 46, 72, 19, "useUpload"], [1307, 64, 72, 28], [1308, 2, 72, 28], [1309, 2, 72, 28, "_c"], [1309, 4, 72, 28], [1309, 7, 51, 24, "EchoCameraWeb"], [1309, 20, 51, 37], [1310, 2, 984, 0], [1310, 8, 984, 6, "styles"], [1310, 14, 984, 12], [1310, 17, 984, 15, "StyleSheet"], [1310, 36, 984, 25], [1310, 37, 984, 26, "create"], [1310, 43, 984, 32], [1310, 44, 984, 33], [1311, 4, 985, 2, "container"], [1311, 13, 985, 11], [1311, 15, 985, 13], [1312, 6, 986, 4, "flex"], [1312, 10, 986, 8], [1312, 12, 986, 10], [1312, 13, 986, 11], [1313, 6, 987, 4, "backgroundColor"], [1313, 21, 987, 19], [1313, 23, 987, 21], [1314, 4, 988, 2], [1314, 5, 988, 3], [1315, 4, 989, 2, "cameraContainer"], [1315, 19, 989, 17], [1315, 21, 989, 19], [1316, 6, 990, 4, "flex"], [1316, 10, 990, 8], [1316, 12, 990, 10], [1316, 13, 990, 11], [1317, 6, 991, 4, "max<PERSON><PERSON><PERSON>"], [1317, 14, 991, 12], [1317, 16, 991, 14], [1317, 19, 991, 17], [1318, 6, 992, 4, "alignSelf"], [1318, 15, 992, 13], [1318, 17, 992, 15], [1318, 25, 992, 23], [1319, 6, 993, 4, "width"], [1319, 11, 993, 9], [1319, 13, 993, 11], [1320, 4, 994, 2], [1320, 5, 994, 3], [1321, 4, 995, 2, "camera"], [1321, 10, 995, 8], [1321, 12, 995, 10], [1322, 6, 996, 4, "flex"], [1322, 10, 996, 8], [1322, 12, 996, 10], [1323, 4, 997, 2], [1323, 5, 997, 3], [1324, 4, 998, 2, "headerOverlay"], [1324, 17, 998, 15], [1324, 19, 998, 17], [1325, 6, 999, 4, "position"], [1325, 14, 999, 12], [1325, 16, 999, 14], [1325, 26, 999, 24], [1326, 6, 1000, 4, "top"], [1326, 9, 1000, 7], [1326, 11, 1000, 9], [1326, 12, 1000, 10], [1327, 6, 1001, 4, "left"], [1327, 10, 1001, 8], [1327, 12, 1001, 10], [1327, 13, 1001, 11], [1328, 6, 1002, 4, "right"], [1328, 11, 1002, 9], [1328, 13, 1002, 11], [1328, 14, 1002, 12], [1329, 6, 1003, 4, "backgroundColor"], [1329, 21, 1003, 19], [1329, 23, 1003, 21], [1329, 36, 1003, 34], [1330, 6, 1004, 4, "paddingTop"], [1330, 16, 1004, 14], [1330, 18, 1004, 16], [1330, 20, 1004, 18], [1331, 6, 1005, 4, "paddingHorizontal"], [1331, 23, 1005, 21], [1331, 25, 1005, 23], [1331, 27, 1005, 25], [1332, 6, 1006, 4, "paddingBottom"], [1332, 19, 1006, 17], [1332, 21, 1006, 19], [1333, 4, 1007, 2], [1333, 5, 1007, 3], [1334, 4, 1008, 2, "headerContent"], [1334, 17, 1008, 15], [1334, 19, 1008, 17], [1335, 6, 1009, 4, "flexDirection"], [1335, 19, 1009, 17], [1335, 21, 1009, 19], [1335, 26, 1009, 24], [1336, 6, 1010, 4, "justifyContent"], [1336, 20, 1010, 18], [1336, 22, 1010, 20], [1336, 37, 1010, 35], [1337, 6, 1011, 4, "alignItems"], [1337, 16, 1011, 14], [1337, 18, 1011, 16], [1338, 4, 1012, 2], [1338, 5, 1012, 3], [1339, 4, 1013, 2, "headerLeft"], [1339, 14, 1013, 12], [1339, 16, 1013, 14], [1340, 6, 1014, 4, "flex"], [1340, 10, 1014, 8], [1340, 12, 1014, 10], [1341, 4, 1015, 2], [1341, 5, 1015, 3], [1342, 4, 1016, 2, "headerTitle"], [1342, 15, 1016, 13], [1342, 17, 1016, 15], [1343, 6, 1017, 4, "fontSize"], [1343, 14, 1017, 12], [1343, 16, 1017, 14], [1343, 18, 1017, 16], [1344, 6, 1018, 4, "fontWeight"], [1344, 16, 1018, 14], [1344, 18, 1018, 16], [1344, 23, 1018, 21], [1345, 6, 1019, 4, "color"], [1345, 11, 1019, 9], [1345, 13, 1019, 11], [1345, 19, 1019, 17], [1346, 6, 1020, 4, "marginBottom"], [1346, 18, 1020, 16], [1346, 20, 1020, 18], [1347, 4, 1021, 2], [1347, 5, 1021, 3], [1348, 4, 1022, 2, "subtitleRow"], [1348, 15, 1022, 13], [1348, 17, 1022, 15], [1349, 6, 1023, 4, "flexDirection"], [1349, 19, 1023, 17], [1349, 21, 1023, 19], [1349, 26, 1023, 24], [1350, 6, 1024, 4, "alignItems"], [1350, 16, 1024, 14], [1350, 18, 1024, 16], [1350, 26, 1024, 24], [1351, 6, 1025, 4, "marginBottom"], [1351, 18, 1025, 16], [1351, 20, 1025, 18], [1352, 4, 1026, 2], [1352, 5, 1026, 3], [1353, 4, 1027, 2, "webIcon"], [1353, 11, 1027, 9], [1353, 13, 1027, 11], [1354, 6, 1028, 4, "fontSize"], [1354, 14, 1028, 12], [1354, 16, 1028, 14], [1354, 18, 1028, 16], [1355, 6, 1029, 4, "marginRight"], [1355, 17, 1029, 15], [1355, 19, 1029, 17], [1356, 4, 1030, 2], [1356, 5, 1030, 3], [1357, 4, 1031, 2, "headerSubtitle"], [1357, 18, 1031, 16], [1357, 20, 1031, 18], [1358, 6, 1032, 4, "fontSize"], [1358, 14, 1032, 12], [1358, 16, 1032, 14], [1358, 18, 1032, 16], [1359, 6, 1033, 4, "color"], [1359, 11, 1033, 9], [1359, 13, 1033, 11], [1359, 19, 1033, 17], [1360, 6, 1034, 4, "opacity"], [1360, 13, 1034, 11], [1360, 15, 1034, 13], [1361, 4, 1035, 2], [1361, 5, 1035, 3], [1362, 4, 1036, 2, "challengeRow"], [1362, 16, 1036, 14], [1362, 18, 1036, 16], [1363, 6, 1037, 4, "flexDirection"], [1363, 19, 1037, 17], [1363, 21, 1037, 19], [1363, 26, 1037, 24], [1364, 6, 1038, 4, "alignItems"], [1364, 16, 1038, 14], [1364, 18, 1038, 16], [1365, 4, 1039, 2], [1365, 5, 1039, 3], [1366, 4, 1040, 2, "challengeCode"], [1366, 17, 1040, 15], [1366, 19, 1040, 17], [1367, 6, 1041, 4, "fontSize"], [1367, 14, 1041, 12], [1367, 16, 1041, 14], [1367, 18, 1041, 16], [1368, 6, 1042, 4, "color"], [1368, 11, 1042, 9], [1368, 13, 1042, 11], [1368, 19, 1042, 17], [1369, 6, 1043, 4, "marginLeft"], [1369, 16, 1043, 14], [1369, 18, 1043, 16], [1369, 19, 1043, 17], [1370, 6, 1044, 4, "fontFamily"], [1370, 16, 1044, 14], [1370, 18, 1044, 16], [1371, 4, 1045, 2], [1371, 5, 1045, 3], [1372, 4, 1046, 2, "closeButton"], [1372, 15, 1046, 13], [1372, 17, 1046, 15], [1373, 6, 1047, 4, "padding"], [1373, 13, 1047, 11], [1373, 15, 1047, 13], [1374, 4, 1048, 2], [1374, 5, 1048, 3], [1375, 4, 1049, 2, "privacyNotice"], [1375, 17, 1049, 15], [1375, 19, 1049, 17], [1376, 6, 1050, 4, "position"], [1376, 14, 1050, 12], [1376, 16, 1050, 14], [1376, 26, 1050, 24], [1377, 6, 1051, 4, "top"], [1377, 9, 1051, 7], [1377, 11, 1051, 9], [1377, 14, 1051, 12], [1378, 6, 1052, 4, "left"], [1378, 10, 1052, 8], [1378, 12, 1052, 10], [1378, 14, 1052, 12], [1379, 6, 1053, 4, "right"], [1379, 11, 1053, 9], [1379, 13, 1053, 11], [1379, 15, 1053, 13], [1380, 6, 1054, 4, "backgroundColor"], [1380, 21, 1054, 19], [1380, 23, 1054, 21], [1380, 48, 1054, 46], [1381, 6, 1055, 4, "borderRadius"], [1381, 18, 1055, 16], [1381, 20, 1055, 18], [1381, 21, 1055, 19], [1382, 6, 1056, 4, "padding"], [1382, 13, 1056, 11], [1382, 15, 1056, 13], [1382, 17, 1056, 15], [1383, 6, 1057, 4, "flexDirection"], [1383, 19, 1057, 17], [1383, 21, 1057, 19], [1383, 26, 1057, 24], [1384, 6, 1058, 4, "alignItems"], [1384, 16, 1058, 14], [1384, 18, 1058, 16], [1385, 4, 1059, 2], [1385, 5, 1059, 3], [1386, 4, 1060, 2, "privacyText"], [1386, 15, 1060, 13], [1386, 17, 1060, 15], [1387, 6, 1061, 4, "color"], [1387, 11, 1061, 9], [1387, 13, 1061, 11], [1387, 19, 1061, 17], [1388, 6, 1062, 4, "fontSize"], [1388, 14, 1062, 12], [1388, 16, 1062, 14], [1388, 18, 1062, 16], [1389, 6, 1063, 4, "marginLeft"], [1389, 16, 1063, 14], [1389, 18, 1063, 16], [1389, 19, 1063, 17], [1390, 6, 1064, 4, "flex"], [1390, 10, 1064, 8], [1390, 12, 1064, 10], [1391, 4, 1065, 2], [1391, 5, 1065, 3], [1392, 4, 1066, 2, "footer<PERSON><PERSON><PERSON>"], [1392, 17, 1066, 15], [1392, 19, 1066, 17], [1393, 6, 1067, 4, "position"], [1393, 14, 1067, 12], [1393, 16, 1067, 14], [1393, 26, 1067, 24], [1394, 6, 1068, 4, "bottom"], [1394, 12, 1068, 10], [1394, 14, 1068, 12], [1394, 15, 1068, 13], [1395, 6, 1069, 4, "left"], [1395, 10, 1069, 8], [1395, 12, 1069, 10], [1395, 13, 1069, 11], [1396, 6, 1070, 4, "right"], [1396, 11, 1070, 9], [1396, 13, 1070, 11], [1396, 14, 1070, 12], [1397, 6, 1071, 4, "backgroundColor"], [1397, 21, 1071, 19], [1397, 23, 1071, 21], [1397, 36, 1071, 34], [1398, 6, 1072, 4, "paddingBottom"], [1398, 19, 1072, 17], [1398, 21, 1072, 19], [1398, 23, 1072, 21], [1399, 6, 1073, 4, "paddingTop"], [1399, 16, 1073, 14], [1399, 18, 1073, 16], [1399, 20, 1073, 18], [1400, 6, 1074, 4, "alignItems"], [1400, 16, 1074, 14], [1400, 18, 1074, 16], [1401, 4, 1075, 2], [1401, 5, 1075, 3], [1402, 4, 1076, 2, "instruction"], [1402, 15, 1076, 13], [1402, 17, 1076, 15], [1403, 6, 1077, 4, "fontSize"], [1403, 14, 1077, 12], [1403, 16, 1077, 14], [1403, 18, 1077, 16], [1404, 6, 1078, 4, "color"], [1404, 11, 1078, 9], [1404, 13, 1078, 11], [1404, 19, 1078, 17], [1405, 6, 1079, 4, "marginBottom"], [1405, 18, 1079, 16], [1405, 20, 1079, 18], [1406, 4, 1080, 2], [1406, 5, 1080, 3], [1407, 4, 1081, 2, "shutterButton"], [1407, 17, 1081, 15], [1407, 19, 1081, 17], [1408, 6, 1082, 4, "width"], [1408, 11, 1082, 9], [1408, 13, 1082, 11], [1408, 15, 1082, 13], [1409, 6, 1083, 4, "height"], [1409, 12, 1083, 10], [1409, 14, 1083, 12], [1409, 16, 1083, 14], [1410, 6, 1084, 4, "borderRadius"], [1410, 18, 1084, 16], [1410, 20, 1084, 18], [1410, 22, 1084, 20], [1411, 6, 1085, 4, "backgroundColor"], [1411, 21, 1085, 19], [1411, 23, 1085, 21], [1411, 29, 1085, 27], [1412, 6, 1086, 4, "justifyContent"], [1412, 20, 1086, 18], [1412, 22, 1086, 20], [1412, 30, 1086, 28], [1413, 6, 1087, 4, "alignItems"], [1413, 16, 1087, 14], [1413, 18, 1087, 16], [1413, 26, 1087, 24], [1414, 6, 1088, 4, "marginBottom"], [1414, 18, 1088, 16], [1414, 20, 1088, 18], [1414, 22, 1088, 20], [1415, 6, 1089, 4], [1415, 9, 1089, 7, "Platform"], [1415, 26, 1089, 15], [1415, 27, 1089, 16, "select"], [1415, 33, 1089, 22], [1415, 34, 1089, 23], [1416, 8, 1090, 6, "ios"], [1416, 11, 1090, 9], [1416, 13, 1090, 11], [1417, 10, 1091, 8, "shadowColor"], [1417, 21, 1091, 19], [1417, 23, 1091, 21], [1417, 32, 1091, 30], [1418, 10, 1092, 8, "shadowOffset"], [1418, 22, 1092, 20], [1418, 24, 1092, 22], [1419, 12, 1092, 24, "width"], [1419, 17, 1092, 29], [1419, 19, 1092, 31], [1419, 20, 1092, 32], [1420, 12, 1092, 34, "height"], [1420, 18, 1092, 40], [1420, 20, 1092, 42], [1421, 10, 1092, 44], [1421, 11, 1092, 45], [1422, 10, 1093, 8, "shadowOpacity"], [1422, 23, 1093, 21], [1422, 25, 1093, 23], [1422, 28, 1093, 26], [1423, 10, 1094, 8, "shadowRadius"], [1423, 22, 1094, 20], [1423, 24, 1094, 22], [1424, 8, 1095, 6], [1424, 9, 1095, 7], [1425, 8, 1096, 6, "android"], [1425, 15, 1096, 13], [1425, 17, 1096, 15], [1426, 10, 1097, 8, "elevation"], [1426, 19, 1097, 17], [1426, 21, 1097, 19], [1427, 8, 1098, 6], [1427, 9, 1098, 7], [1428, 8, 1099, 6, "web"], [1428, 11, 1099, 9], [1428, 13, 1099, 11], [1429, 10, 1100, 8, "boxShadow"], [1429, 19, 1100, 17], [1429, 21, 1100, 19], [1430, 8, 1101, 6], [1431, 6, 1102, 4], [1431, 7, 1102, 5], [1432, 4, 1103, 2], [1432, 5, 1103, 3], [1433, 4, 1104, 2, "shutterButtonDisabled"], [1433, 25, 1104, 23], [1433, 27, 1104, 25], [1434, 6, 1105, 4, "opacity"], [1434, 13, 1105, 11], [1434, 15, 1105, 13], [1435, 4, 1106, 2], [1435, 5, 1106, 3], [1436, 4, 1107, 2, "shutterInner"], [1436, 16, 1107, 14], [1436, 18, 1107, 16], [1437, 6, 1108, 4, "width"], [1437, 11, 1108, 9], [1437, 13, 1108, 11], [1437, 15, 1108, 13], [1438, 6, 1109, 4, "height"], [1438, 12, 1109, 10], [1438, 14, 1109, 12], [1438, 16, 1109, 14], [1439, 6, 1110, 4, "borderRadius"], [1439, 18, 1110, 16], [1439, 20, 1110, 18], [1439, 22, 1110, 20], [1440, 6, 1111, 4, "backgroundColor"], [1440, 21, 1111, 19], [1440, 23, 1111, 21], [1440, 29, 1111, 27], [1441, 6, 1112, 4, "borderWidth"], [1441, 17, 1112, 15], [1441, 19, 1112, 17], [1441, 20, 1112, 18], [1442, 6, 1113, 4, "borderColor"], [1442, 17, 1113, 15], [1442, 19, 1113, 17], [1443, 4, 1114, 2], [1443, 5, 1114, 3], [1444, 4, 1115, 2, "privacyNote"], [1444, 15, 1115, 13], [1444, 17, 1115, 15], [1445, 6, 1116, 4, "fontSize"], [1445, 14, 1116, 12], [1445, 16, 1116, 14], [1445, 18, 1116, 16], [1446, 6, 1117, 4, "color"], [1446, 11, 1117, 9], [1446, 13, 1117, 11], [1447, 4, 1118, 2], [1447, 5, 1118, 3], [1448, 4, 1119, 2, "processingModal"], [1448, 19, 1119, 17], [1448, 21, 1119, 19], [1449, 6, 1120, 4, "flex"], [1449, 10, 1120, 8], [1449, 12, 1120, 10], [1449, 13, 1120, 11], [1450, 6, 1121, 4, "backgroundColor"], [1450, 21, 1121, 19], [1450, 23, 1121, 21], [1450, 43, 1121, 41], [1451, 6, 1122, 4, "justifyContent"], [1451, 20, 1122, 18], [1451, 22, 1122, 20], [1451, 30, 1122, 28], [1452, 6, 1123, 4, "alignItems"], [1452, 16, 1123, 14], [1452, 18, 1123, 16], [1453, 4, 1124, 2], [1453, 5, 1124, 3], [1454, 4, 1125, 2, "processingContent"], [1454, 21, 1125, 19], [1454, 23, 1125, 21], [1455, 6, 1126, 4, "backgroundColor"], [1455, 21, 1126, 19], [1455, 23, 1126, 21], [1455, 29, 1126, 27], [1456, 6, 1127, 4, "borderRadius"], [1456, 18, 1127, 16], [1456, 20, 1127, 18], [1456, 22, 1127, 20], [1457, 6, 1128, 4, "padding"], [1457, 13, 1128, 11], [1457, 15, 1128, 13], [1457, 17, 1128, 15], [1458, 6, 1129, 4, "width"], [1458, 11, 1129, 9], [1458, 13, 1129, 11], [1458, 18, 1129, 16], [1459, 6, 1130, 4, "max<PERSON><PERSON><PERSON>"], [1459, 14, 1130, 12], [1459, 16, 1130, 14], [1459, 19, 1130, 17], [1460, 6, 1131, 4, "alignItems"], [1460, 16, 1131, 14], [1460, 18, 1131, 16], [1461, 4, 1132, 2], [1461, 5, 1132, 3], [1462, 4, 1133, 2, "processingTitle"], [1462, 19, 1133, 17], [1462, 21, 1133, 19], [1463, 6, 1134, 4, "fontSize"], [1463, 14, 1134, 12], [1463, 16, 1134, 14], [1463, 18, 1134, 16], [1464, 6, 1135, 4, "fontWeight"], [1464, 16, 1135, 14], [1464, 18, 1135, 16], [1464, 23, 1135, 21], [1465, 6, 1136, 4, "color"], [1465, 11, 1136, 9], [1465, 13, 1136, 11], [1465, 22, 1136, 20], [1466, 6, 1137, 4, "marginTop"], [1466, 15, 1137, 13], [1466, 17, 1137, 15], [1466, 19, 1137, 17], [1467, 6, 1138, 4, "marginBottom"], [1467, 18, 1138, 16], [1467, 20, 1138, 18], [1468, 4, 1139, 2], [1468, 5, 1139, 3], [1469, 4, 1140, 2, "progressBar"], [1469, 15, 1140, 13], [1469, 17, 1140, 15], [1470, 6, 1141, 4, "width"], [1470, 11, 1141, 9], [1470, 13, 1141, 11], [1470, 19, 1141, 17], [1471, 6, 1142, 4, "height"], [1471, 12, 1142, 10], [1471, 14, 1142, 12], [1471, 15, 1142, 13], [1472, 6, 1143, 4, "backgroundColor"], [1472, 21, 1143, 19], [1472, 23, 1143, 21], [1472, 32, 1143, 30], [1473, 6, 1144, 4, "borderRadius"], [1473, 18, 1144, 16], [1473, 20, 1144, 18], [1473, 21, 1144, 19], [1474, 6, 1145, 4, "overflow"], [1474, 14, 1145, 12], [1474, 16, 1145, 14], [1474, 24, 1145, 22], [1475, 6, 1146, 4, "marginBottom"], [1475, 18, 1146, 16], [1475, 20, 1146, 18], [1476, 4, 1147, 2], [1476, 5, 1147, 3], [1477, 4, 1148, 2, "progressFill"], [1477, 16, 1148, 14], [1477, 18, 1148, 16], [1478, 6, 1149, 4, "height"], [1478, 12, 1149, 10], [1478, 14, 1149, 12], [1478, 20, 1149, 18], [1479, 6, 1150, 4, "backgroundColor"], [1479, 21, 1150, 19], [1479, 23, 1150, 21], [1479, 32, 1150, 30], [1480, 6, 1151, 4, "borderRadius"], [1480, 18, 1151, 16], [1480, 20, 1151, 18], [1481, 4, 1152, 2], [1481, 5, 1152, 3], [1482, 4, 1153, 2, "processingDescription"], [1482, 25, 1153, 23], [1482, 27, 1153, 25], [1483, 6, 1154, 4, "fontSize"], [1483, 14, 1154, 12], [1483, 16, 1154, 14], [1483, 18, 1154, 16], [1484, 6, 1155, 4, "color"], [1484, 11, 1155, 9], [1484, 13, 1155, 11], [1484, 22, 1155, 20], [1485, 6, 1156, 4, "textAlign"], [1485, 15, 1156, 13], [1485, 17, 1156, 15], [1486, 4, 1157, 2], [1486, 5, 1157, 3], [1487, 4, 1158, 2, "successIcon"], [1487, 15, 1158, 13], [1487, 17, 1158, 15], [1488, 6, 1159, 4, "marginTop"], [1488, 15, 1159, 13], [1488, 17, 1159, 15], [1489, 4, 1160, 2], [1489, 5, 1160, 3], [1490, 4, 1161, 2, "errorContent"], [1490, 16, 1161, 14], [1490, 18, 1161, 16], [1491, 6, 1162, 4, "backgroundColor"], [1491, 21, 1162, 19], [1491, 23, 1162, 21], [1491, 29, 1162, 27], [1492, 6, 1163, 4, "borderRadius"], [1492, 18, 1163, 16], [1492, 20, 1163, 18], [1492, 22, 1163, 20], [1493, 6, 1164, 4, "padding"], [1493, 13, 1164, 11], [1493, 15, 1164, 13], [1493, 17, 1164, 15], [1494, 6, 1165, 4, "width"], [1494, 11, 1165, 9], [1494, 13, 1165, 11], [1494, 18, 1165, 16], [1495, 6, 1166, 4, "max<PERSON><PERSON><PERSON>"], [1495, 14, 1166, 12], [1495, 16, 1166, 14], [1495, 19, 1166, 17], [1496, 6, 1167, 4, "alignItems"], [1496, 16, 1167, 14], [1496, 18, 1167, 16], [1497, 4, 1168, 2], [1497, 5, 1168, 3], [1498, 4, 1169, 2, "errorTitle"], [1498, 14, 1169, 12], [1498, 16, 1169, 14], [1499, 6, 1170, 4, "fontSize"], [1499, 14, 1170, 12], [1499, 16, 1170, 14], [1499, 18, 1170, 16], [1500, 6, 1171, 4, "fontWeight"], [1500, 16, 1171, 14], [1500, 18, 1171, 16], [1500, 23, 1171, 21], [1501, 6, 1172, 4, "color"], [1501, 11, 1172, 9], [1501, 13, 1172, 11], [1501, 22, 1172, 20], [1502, 6, 1173, 4, "marginTop"], [1502, 15, 1173, 13], [1502, 17, 1173, 15], [1502, 19, 1173, 17], [1503, 6, 1174, 4, "marginBottom"], [1503, 18, 1174, 16], [1503, 20, 1174, 18], [1504, 4, 1175, 2], [1504, 5, 1175, 3], [1505, 4, 1176, 2, "errorMessage"], [1505, 16, 1176, 14], [1505, 18, 1176, 16], [1506, 6, 1177, 4, "fontSize"], [1506, 14, 1177, 12], [1506, 16, 1177, 14], [1506, 18, 1177, 16], [1507, 6, 1178, 4, "color"], [1507, 11, 1178, 9], [1507, 13, 1178, 11], [1507, 22, 1178, 20], [1508, 6, 1179, 4, "textAlign"], [1508, 15, 1179, 13], [1508, 17, 1179, 15], [1508, 25, 1179, 23], [1509, 6, 1180, 4, "marginBottom"], [1509, 18, 1180, 16], [1509, 20, 1180, 18], [1510, 4, 1181, 2], [1510, 5, 1181, 3], [1511, 4, 1182, 2, "primaryButton"], [1511, 17, 1182, 15], [1511, 19, 1182, 17], [1512, 6, 1183, 4, "backgroundColor"], [1512, 21, 1183, 19], [1512, 23, 1183, 21], [1512, 32, 1183, 30], [1513, 6, 1184, 4, "paddingHorizontal"], [1513, 23, 1184, 21], [1513, 25, 1184, 23], [1513, 27, 1184, 25], [1514, 6, 1185, 4, "paddingVertical"], [1514, 21, 1185, 19], [1514, 23, 1185, 21], [1514, 25, 1185, 23], [1515, 6, 1186, 4, "borderRadius"], [1515, 18, 1186, 16], [1515, 20, 1186, 18], [1515, 21, 1186, 19], [1516, 6, 1187, 4, "marginTop"], [1516, 15, 1187, 13], [1516, 17, 1187, 15], [1517, 4, 1188, 2], [1517, 5, 1188, 3], [1518, 4, 1189, 2, "primaryButtonText"], [1518, 21, 1189, 19], [1518, 23, 1189, 21], [1519, 6, 1190, 4, "color"], [1519, 11, 1190, 9], [1519, 13, 1190, 11], [1519, 19, 1190, 17], [1520, 6, 1191, 4, "fontSize"], [1520, 14, 1191, 12], [1520, 16, 1191, 14], [1520, 18, 1191, 16], [1521, 6, 1192, 4, "fontWeight"], [1521, 16, 1192, 14], [1521, 18, 1192, 16], [1522, 4, 1193, 2], [1522, 5, 1193, 3], [1523, 4, 1194, 2, "secondaryButton"], [1523, 19, 1194, 17], [1523, 21, 1194, 19], [1524, 6, 1195, 4, "paddingHorizontal"], [1524, 23, 1195, 21], [1524, 25, 1195, 23], [1524, 27, 1195, 25], [1525, 6, 1196, 4, "paddingVertical"], [1525, 21, 1196, 19], [1525, 23, 1196, 21], [1525, 25, 1196, 23], [1526, 6, 1197, 4, "marginTop"], [1526, 15, 1197, 13], [1526, 17, 1197, 15], [1527, 4, 1198, 2], [1527, 5, 1198, 3], [1528, 4, 1199, 2, "secondaryButtonText"], [1528, 23, 1199, 21], [1528, 25, 1199, 23], [1529, 6, 1200, 4, "color"], [1529, 11, 1200, 9], [1529, 13, 1200, 11], [1529, 22, 1200, 20], [1530, 6, 1201, 4, "fontSize"], [1530, 14, 1201, 12], [1530, 16, 1201, 14], [1531, 4, 1202, 2], [1531, 5, 1202, 3], [1532, 4, 1203, 2, "permissionContent"], [1532, 21, 1203, 19], [1532, 23, 1203, 21], [1533, 6, 1204, 4, "flex"], [1533, 10, 1204, 8], [1533, 12, 1204, 10], [1533, 13, 1204, 11], [1534, 6, 1205, 4, "justifyContent"], [1534, 20, 1205, 18], [1534, 22, 1205, 20], [1534, 30, 1205, 28], [1535, 6, 1206, 4, "alignItems"], [1535, 16, 1206, 14], [1535, 18, 1206, 16], [1535, 26, 1206, 24], [1536, 6, 1207, 4, "padding"], [1536, 13, 1207, 11], [1536, 15, 1207, 13], [1537, 4, 1208, 2], [1537, 5, 1208, 3], [1538, 4, 1209, 2, "permissionTitle"], [1538, 19, 1209, 17], [1538, 21, 1209, 19], [1539, 6, 1210, 4, "fontSize"], [1539, 14, 1210, 12], [1539, 16, 1210, 14], [1539, 18, 1210, 16], [1540, 6, 1211, 4, "fontWeight"], [1540, 16, 1211, 14], [1540, 18, 1211, 16], [1540, 23, 1211, 21], [1541, 6, 1212, 4, "color"], [1541, 11, 1212, 9], [1541, 13, 1212, 11], [1541, 22, 1212, 20], [1542, 6, 1213, 4, "marginTop"], [1542, 15, 1213, 13], [1542, 17, 1213, 15], [1542, 19, 1213, 17], [1543, 6, 1214, 4, "marginBottom"], [1543, 18, 1214, 16], [1543, 20, 1214, 18], [1544, 4, 1215, 2], [1544, 5, 1215, 3], [1545, 4, 1216, 2, "permissionDescription"], [1545, 25, 1216, 23], [1545, 27, 1216, 25], [1546, 6, 1217, 4, "fontSize"], [1546, 14, 1217, 12], [1546, 16, 1217, 14], [1546, 18, 1217, 16], [1547, 6, 1218, 4, "color"], [1547, 11, 1218, 9], [1547, 13, 1218, 11], [1547, 22, 1218, 20], [1548, 6, 1219, 4, "textAlign"], [1548, 15, 1219, 13], [1548, 17, 1219, 15], [1548, 25, 1219, 23], [1549, 6, 1220, 4, "marginBottom"], [1549, 18, 1220, 16], [1549, 20, 1220, 18], [1550, 4, 1221, 2], [1550, 5, 1221, 3], [1551, 4, 1222, 2, "loadingText"], [1551, 15, 1222, 13], [1551, 17, 1222, 15], [1552, 6, 1223, 4, "color"], [1552, 11, 1223, 9], [1552, 13, 1223, 11], [1552, 22, 1223, 20], [1553, 6, 1224, 4, "marginTop"], [1553, 15, 1224, 13], [1553, 17, 1224, 15], [1554, 4, 1225, 2], [1554, 5, 1225, 3], [1555, 4, 1226, 2], [1556, 4, 1227, 2, "blurZone"], [1556, 12, 1227, 10], [1556, 14, 1227, 12], [1557, 6, 1228, 4, "position"], [1557, 14, 1228, 12], [1557, 16, 1228, 14], [1557, 26, 1228, 24], [1558, 6, 1229, 4, "overflow"], [1558, 14, 1229, 12], [1558, 16, 1229, 14], [1559, 4, 1230, 2], [1559, 5, 1230, 3], [1560, 4, 1231, 2, "previewChip"], [1560, 15, 1231, 13], [1560, 17, 1231, 15], [1561, 6, 1232, 4, "position"], [1561, 14, 1232, 12], [1561, 16, 1232, 14], [1561, 26, 1232, 24], [1562, 6, 1233, 4, "top"], [1562, 9, 1233, 7], [1562, 11, 1233, 9], [1562, 12, 1233, 10], [1563, 6, 1234, 4, "right"], [1563, 11, 1234, 9], [1563, 13, 1234, 11], [1563, 14, 1234, 12], [1564, 6, 1235, 4, "backgroundColor"], [1564, 21, 1235, 19], [1564, 23, 1235, 21], [1564, 40, 1235, 38], [1565, 6, 1236, 4, "paddingHorizontal"], [1565, 23, 1236, 21], [1565, 25, 1236, 23], [1565, 27, 1236, 25], [1566, 6, 1237, 4, "paddingVertical"], [1566, 21, 1237, 19], [1566, 23, 1237, 21], [1566, 24, 1237, 22], [1567, 6, 1238, 4, "borderRadius"], [1567, 18, 1238, 16], [1567, 20, 1238, 18], [1568, 4, 1239, 2], [1568, 5, 1239, 3], [1569, 4, 1240, 2, "previewChipText"], [1569, 19, 1240, 17], [1569, 21, 1240, 19], [1570, 6, 1241, 4, "color"], [1570, 11, 1241, 9], [1570, 13, 1241, 11], [1570, 19, 1241, 17], [1571, 6, 1242, 4, "fontSize"], [1571, 14, 1242, 12], [1571, 16, 1242, 14], [1571, 18, 1242, 16], [1572, 6, 1243, 4, "fontWeight"], [1572, 16, 1243, 14], [1572, 18, 1243, 16], [1573, 4, 1244, 2], [1574, 2, 1245, 0], [1574, 3, 1245, 1], [1574, 4, 1245, 2], [1575, 2, 1245, 3], [1575, 6, 1245, 3, "_c"], [1575, 8, 1245, 3], [1576, 2, 1245, 3, "$RefreshReg$"], [1576, 14, 1245, 3], [1576, 15, 1245, 3, "_c"], [1576, 17, 1245, 3], [1577, 0, 1245, 3], [1577, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "countSkinPixelsInRegion", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;GPqC;kCQE;GRgB;qBSE;GTQ;8BUE;GV4B;2BWE;GXa;wBYE;GZiB;0BaG;GbqD;0BcE;GduB;gCeE;kBCa;KDG;GfC;mCiBG;wBbc,kCa;GjBoC;mCkBE;wBda;OcI;oFCkC;UDM;8BEW;SFwB;uDda;sBiBC,wBjB;OcC;GlBe;6BsBG;GtB6B;kCuBG;GvB8C;4BwBE;mBCmD;SDE;GxBO;uB0BE;G1BI;mC2BG;G3BM;YCE;GDK;oB4B2C;W5BG;yB6BC;W7BG;wB8BC;W9BI;CD4L"}}, "type": "js/module"}]}