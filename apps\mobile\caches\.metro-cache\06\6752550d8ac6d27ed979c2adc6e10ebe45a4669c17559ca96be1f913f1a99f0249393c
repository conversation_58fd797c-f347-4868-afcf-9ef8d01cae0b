{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 89}, "end": {"line": 5, "column": 45, "index": 134}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.canGetUserMedia = canGetUserMedia;\n  exports.getAnyUserMediaAsync = getAnyUserMediaAsync;\n  exports.getUserMediaAsync = getUserMediaAsync;\n  exports.isBackCameraAvailableAsync = isBackCameraAvailableAsync;\n  exports.isFrontCameraAvailableAsync = isFrontCameraAvailableAsync;\n  exports.mountedInstances = void 0;\n  exports.requestUserMediaAsync = requestUserMediaAsync;\n  exports.userMediaRequested = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  /* eslint-env browser */\n  /**\n   * A web-only module for ponyfilling the UserMedia API.\n   */\n\n  const userMediaRequested = exports.userMediaRequested = false;\n  const mountedInstances = exports.mountedInstances = [];\n  async function requestLegacyUserMediaAsync(\n  // TODO(@kitten): Type this properly\n  props) {\n    // TODO(@kitten): This is never type checked against DOM types\n    const optionalSource = id => ({\n      optional: [{\n        sourceId: id\n      }]\n    });\n    const constraintToSourceId = constraint => {\n      const {\n        deviceId\n      } = constraint;\n      if (typeof deviceId === 'string') {\n        return deviceId;\n      }\n      if (Array.isArray(deviceId)) {\n        return deviceId[0] ?? null;\n      } else if (typeof deviceId === 'object' && deviceId.ideal) {\n        return deviceId.ideal;\n      }\n      return null;\n    };\n    const sources = await new Promise(resolve =>\n    // @ts-ignore: https://caniuse.com/#search=getSources Chrome for Android (78) & Samsung Internet (10.1) use this\n    MediaStreamTrack.getSources(sources => resolve(sources)));\n    let audioSource = null;\n    let videoSource = null;\n    sources.forEach(source => {\n      if (source.kind === 'audio') {\n        audioSource = source.id;\n      } else if (source.kind === 'video') {\n        videoSource = source.id;\n      }\n    });\n    // NOTE(@kitten): This doesn't seem right. The types that should be used here don't contain `audioConstraints`\n    // If this is legacy, the type shouldn't have been dropped but marked as `@deprecated`. Alternatively, remove this code path\n    const audioSourceId = constraintToSourceId(props.audioConstraints);\n    if (audioSourceId) {\n      audioSource = audioSourceId;\n    }\n    // NOTE(@kitten): This doesn't seem right. The types that should be used here don't contain `videoConstraints`\n    // If this is legacy, the type shouldn't have been dropped but marked as `@deprecated`. Alternatively, remove this code path\n    const videoSourceId = constraintToSourceId(props.videoConstraints);\n    if (videoSourceId) {\n      videoSource = videoSourceId;\n    }\n    return [optionalSource(audioSource), optionalSource(videoSource)];\n  }\n  async function sourceSelectedAsync(isMuted, audioConstraints, videoConstraints) {\n    const constraints = {\n      video: typeof videoConstraints !== 'undefined' ? videoConstraints : true\n    };\n    if (!isMuted) {\n      constraints.audio = typeof audioConstraints !== 'undefined' ? audioConstraints : true;\n    }\n    return await getAnyUserMediaAsync(constraints);\n  }\n  async function requestUserMediaAsync(\n  // TODO(@kitten): Type this properly\n  props, isMuted = true) {\n    if (canGetUserMedia()) {\n      return await sourceSelectedAsync(isMuted, props.audio, props.video);\n    }\n    // NOTE(@kitten): This doesn't seem right. The types that should be used here don't contain `videoConstraints`\n    // If this is legacy, the type shouldn't have been dropped but marked as `@deprecated`. Alternatively, remove this code path\n    const [audio, video] = await requestLegacyUserMediaAsync(props);\n    return await sourceSelectedAsync(isMuted, audio, video);\n  }\n  async function getAnyUserMediaAsync(constraints, ignoreConstraints = false) {\n    try {\n      return await getUserMediaAsync({\n        ...constraints,\n        video: ignoreConstraints || constraints.video\n      });\n    } catch (error) {\n      if (!ignoreConstraints && typeof error === 'object' && error?.name === 'ConstraintNotSatisfiedError') {\n        return await getAnyUserMediaAsync(constraints, true);\n      }\n      throw error;\n    }\n  }\n  async function getUserMediaAsync(constraints) {\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n      return navigator.mediaDevices.getUserMedia(constraints);\n    }\n    const _getUserMedia = navigator['mozGetUserMedia'] || navigator['webkitGetUserMedia'] ||\n    // @ts-expect-error: TODO(@kitten): Remove / Drop IE support\n    navigator['msGetUserMedia'];\n    return new Promise((resolve, reject) => _getUserMedia.call(navigator, constraints, resolve, reject));\n  }\n  function canGetUserMedia() {\n    // TODO(@kitten): This is misaligned with the implementations in `expo-audio/src/AudioModule.web.ts` and `expo-av`\n    return (\n      // SSR\n      _expoModulesCore.Platform.isDOMAvailable &&\n      // Has any form of media API\n      !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia || navigator['mozGetUserMedia'] || navigator['webkitGetUserMedia'] ||\n      // @ts-expect-error: TODO(@kitten): Remove / Drop IE support\n      navigator['msGetUserMedia'])\n    );\n  }\n  async function isFrontCameraAvailableAsync(devices) {\n    return await supportsCameraType(['front', 'user', 'facetime'], 'user', devices);\n  }\n  async function isBackCameraAvailableAsync(devices) {\n    return await supportsCameraType(['back', 'rear'], 'environment', devices);\n  }\n  async function supportsCameraType(labels, type, devices) {\n    if (!devices) {\n      if (!navigator.mediaDevices.enumerateDevices) {\n        return null;\n      }\n      devices = await navigator.mediaDevices.enumerateDevices();\n    }\n    const cameras = devices.filter(t => t.kind === 'videoinput');\n    const [hasCamera] = cameras.filter(camera => labels.some(label => camera.label.toLowerCase().includes(label)));\n    const [isCapable] = cameras.filter(camera => {\n      if (!('getCapabilities' in camera)) {\n        return null;\n      }\n      const capabilities = camera.getCapabilities();\n      if (!capabilities.facingMode) {\n        return null;\n      }\n      return capabilities.facingMode.find(_ => type);\n    });\n    return isCapable?.deviceId || hasCamera?.deviceId || null;\n  }\n});", "lineCount": 150, "map": [[13, 2, 5, 0], [13, 6, 5, 0, "_expoModulesCore"], [13, 22, 5, 0], [13, 25, 5, 0, "require"], [13, 32, 5, 0], [13, 33, 5, 0, "_dependencyMap"], [13, 47, 5, 0], [14, 2, 1, 0], [15, 2, 2, 0], [16, 0, 3, 0], [17, 0, 4, 0], [19, 2, 6, 7], [19, 8, 6, 13, "userMediaRequested"], [19, 26, 6, 31], [19, 29, 6, 31, "exports"], [19, 36, 6, 31], [19, 37, 6, 31, "userMediaRequested"], [19, 55, 6, 31], [19, 58, 6, 34], [19, 63, 6, 39], [20, 2, 7, 7], [20, 8, 7, 13, "mountedInstances"], [20, 24, 7, 29], [20, 27, 7, 29, "exports"], [20, 34, 7, 29], [20, 35, 7, 29, "mountedInstances"], [20, 51, 7, 29], [20, 54, 7, 32], [20, 56, 7, 34], [21, 2, 8, 0], [21, 17, 8, 15, "requestLegacyUserMediaAsync"], [21, 44, 8, 42, "requestLegacyUserMediaAsync"], [22, 2, 9, 0], [23, 2, 10, 0, "props"], [23, 7, 10, 5], [23, 9, 10, 7], [24, 4, 11, 4], [25, 4, 12, 4], [25, 10, 12, 10, "optionalSource"], [25, 24, 12, 24], [25, 27, 12, 28, "id"], [25, 29, 12, 30], [25, 34, 12, 36], [26, 6, 12, 38, "optional"], [26, 14, 12, 46], [26, 16, 12, 48], [26, 17, 12, 49], [27, 8, 12, 51, "sourceId"], [27, 16, 12, 59], [27, 18, 12, 61, "id"], [28, 6, 12, 64], [28, 7, 12, 65], [29, 4, 12, 67], [29, 5, 12, 68], [29, 6, 12, 69], [30, 4, 13, 4], [30, 10, 13, 10, "constraintToSourceId"], [30, 30, 13, 30], [30, 33, 13, 34, "constraint"], [30, 43, 13, 44], [30, 47, 13, 49], [31, 6, 14, 8], [31, 12, 14, 14], [32, 8, 14, 16, "deviceId"], [33, 6, 14, 25], [33, 7, 14, 26], [33, 10, 14, 29, "constraint"], [33, 20, 14, 39], [34, 6, 15, 8], [34, 10, 15, 12], [34, 17, 15, 19, "deviceId"], [34, 25, 15, 27], [34, 30, 15, 32], [34, 38, 15, 40], [34, 40, 15, 42], [35, 8, 16, 12], [35, 15, 16, 19, "deviceId"], [35, 23, 16, 27], [36, 6, 17, 8], [37, 6, 18, 8], [37, 10, 18, 12, "Array"], [37, 15, 18, 17], [37, 16, 18, 18, "isArray"], [37, 23, 18, 25], [37, 24, 18, 26, "deviceId"], [37, 32, 18, 34], [37, 33, 18, 35], [37, 35, 18, 37], [38, 8, 19, 12], [38, 15, 19, 19, "deviceId"], [38, 23, 19, 27], [38, 24, 19, 28], [38, 25, 19, 29], [38, 26, 19, 30], [38, 30, 19, 34], [38, 34, 19, 38], [39, 6, 20, 8], [39, 7, 20, 9], [39, 13, 21, 13], [39, 17, 21, 17], [39, 24, 21, 24, "deviceId"], [39, 32, 21, 32], [39, 37, 21, 37], [39, 45, 21, 45], [39, 49, 21, 49, "deviceId"], [39, 57, 21, 57], [39, 58, 21, 58, "ideal"], [39, 63, 21, 63], [39, 65, 21, 65], [40, 8, 22, 12], [40, 15, 22, 19, "deviceId"], [40, 23, 22, 27], [40, 24, 22, 28, "ideal"], [40, 29, 22, 33], [41, 6, 23, 8], [42, 6, 24, 8], [42, 13, 24, 15], [42, 17, 24, 19], [43, 4, 25, 4], [43, 5, 25, 5], [44, 4, 26, 4], [44, 10, 26, 10, "sources"], [44, 17, 26, 17], [44, 20, 26, 20], [44, 26, 26, 26], [44, 30, 26, 30, "Promise"], [44, 37, 26, 37], [44, 38, 26, 39, "resolve"], [44, 45, 26, 46], [45, 4, 27, 4], [46, 4, 28, 4, "MediaStreamTrack"], [46, 20, 28, 20], [46, 21, 28, 21, "getSources"], [46, 31, 28, 31], [46, 32, 28, 33, "sources"], [46, 39, 28, 40], [46, 43, 28, 45, "resolve"], [46, 50, 28, 52], [46, 51, 28, 53, "sources"], [46, 58, 28, 60], [46, 59, 28, 61], [46, 60, 28, 62], [46, 61, 28, 63], [47, 4, 29, 4], [47, 8, 29, 8, "audioSource"], [47, 19, 29, 19], [47, 22, 29, 22], [47, 26, 29, 26], [48, 4, 30, 4], [48, 8, 30, 8, "videoSource"], [48, 19, 30, 19], [48, 22, 30, 22], [48, 26, 30, 26], [49, 4, 31, 4, "sources"], [49, 11, 31, 11], [49, 12, 31, 12, "for<PERSON>ach"], [49, 19, 31, 19], [49, 20, 31, 21, "source"], [49, 26, 31, 27], [49, 30, 31, 32], [50, 6, 32, 8], [50, 10, 32, 12, "source"], [50, 16, 32, 18], [50, 17, 32, 19, "kind"], [50, 21, 32, 23], [50, 26, 32, 28], [50, 33, 32, 35], [50, 35, 32, 37], [51, 8, 33, 12, "audioSource"], [51, 19, 33, 23], [51, 22, 33, 26, "source"], [51, 28, 33, 32], [51, 29, 33, 33, "id"], [51, 31, 33, 35], [52, 6, 34, 8], [52, 7, 34, 9], [52, 13, 35, 13], [52, 17, 35, 17, "source"], [52, 23, 35, 23], [52, 24, 35, 24, "kind"], [52, 28, 35, 28], [52, 33, 35, 33], [52, 40, 35, 40], [52, 42, 35, 42], [53, 8, 36, 12, "videoSource"], [53, 19, 36, 23], [53, 22, 36, 26, "source"], [53, 28, 36, 32], [53, 29, 36, 33, "id"], [53, 31, 36, 35], [54, 6, 37, 8], [55, 4, 38, 4], [55, 5, 38, 5], [55, 6, 38, 6], [56, 4, 39, 4], [57, 4, 40, 4], [58, 4, 41, 4], [58, 10, 41, 10, "audioSourceId"], [58, 23, 41, 23], [58, 26, 41, 26, "constraintToSourceId"], [58, 46, 41, 46], [58, 47, 41, 47, "props"], [58, 52, 41, 52], [58, 53, 41, 53, "audioConstraints"], [58, 69, 41, 69], [58, 70, 41, 70], [59, 4, 42, 4], [59, 8, 42, 8, "audioSourceId"], [59, 21, 42, 21], [59, 23, 42, 23], [60, 6, 43, 8, "audioSource"], [60, 17, 43, 19], [60, 20, 43, 22, "audioSourceId"], [60, 33, 43, 35], [61, 4, 44, 4], [62, 4, 45, 4], [63, 4, 46, 4], [64, 4, 47, 4], [64, 10, 47, 10, "videoSourceId"], [64, 23, 47, 23], [64, 26, 47, 26, "constraintToSourceId"], [64, 46, 47, 46], [64, 47, 47, 47, "props"], [64, 52, 47, 52], [64, 53, 47, 53, "videoConstraints"], [64, 69, 47, 69], [64, 70, 47, 70], [65, 4, 48, 4], [65, 8, 48, 8, "videoSourceId"], [65, 21, 48, 21], [65, 23, 48, 23], [66, 6, 49, 8, "videoSource"], [66, 17, 49, 19], [66, 20, 49, 22, "videoSourceId"], [66, 33, 49, 35], [67, 4, 50, 4], [68, 4, 51, 4], [68, 11, 51, 11], [68, 12, 51, 12, "optionalSource"], [68, 26, 51, 26], [68, 27, 51, 27, "audioSource"], [68, 38, 51, 38], [68, 39, 51, 39], [68, 41, 51, 41, "optionalSource"], [68, 55, 51, 55], [68, 56, 51, 56, "videoSource"], [68, 67, 51, 67], [68, 68, 51, 68], [68, 69, 51, 69], [69, 2, 52, 0], [70, 2, 53, 0], [70, 17, 53, 15, "sourceSelectedAsync"], [70, 36, 53, 34, "sourceSelectedAsync"], [70, 37, 53, 35, "isMuted"], [70, 44, 53, 42], [70, 46, 53, 44, "audioConstraints"], [70, 62, 53, 60], [70, 64, 53, 62, "videoConstraints"], [70, 80, 53, 78], [70, 82, 53, 80], [71, 4, 54, 4], [71, 10, 54, 10, "constraints"], [71, 21, 54, 21], [71, 24, 54, 24], [72, 6, 55, 8, "video"], [72, 11, 55, 13], [72, 13, 55, 15], [72, 20, 55, 22, "videoConstraints"], [72, 36, 55, 38], [72, 41, 55, 43], [72, 52, 55, 54], [72, 55, 55, 57, "videoConstraints"], [72, 71, 55, 73], [72, 74, 55, 76], [73, 4, 56, 4], [73, 5, 56, 5], [74, 4, 57, 4], [74, 8, 57, 8], [74, 9, 57, 9, "isMuted"], [74, 16, 57, 16], [74, 18, 57, 18], [75, 6, 58, 8, "constraints"], [75, 17, 58, 19], [75, 18, 58, 20, "audio"], [75, 23, 58, 25], [75, 26, 58, 28], [75, 33, 58, 35, "audioConstraints"], [75, 49, 58, 51], [75, 54, 58, 56], [75, 65, 58, 67], [75, 68, 58, 70, "audioConstraints"], [75, 84, 58, 86], [75, 87, 58, 89], [75, 91, 58, 93], [76, 4, 59, 4], [77, 4, 60, 4], [77, 11, 60, 11], [77, 17, 60, 17, "getAnyUserMediaAsync"], [77, 37, 60, 37], [77, 38, 60, 38, "constraints"], [77, 49, 60, 49], [77, 50, 60, 50], [78, 2, 61, 0], [79, 2, 62, 7], [79, 17, 62, 22, "requestUserMediaAsync"], [79, 38, 62, 43, "requestUserMediaAsync"], [80, 2, 63, 0], [81, 2, 64, 0, "props"], [81, 7, 64, 5], [81, 9, 64, 7, "isMuted"], [81, 16, 64, 14], [81, 19, 64, 17], [81, 23, 64, 21], [81, 25, 64, 23], [82, 4, 65, 4], [82, 8, 65, 8, "canGetUserMedia"], [82, 23, 65, 23], [82, 24, 65, 24], [82, 25, 65, 25], [82, 27, 65, 27], [83, 6, 66, 8], [83, 13, 66, 15], [83, 19, 66, 21, "sourceSelectedAsync"], [83, 38, 66, 40], [83, 39, 66, 41, "isMuted"], [83, 46, 66, 48], [83, 48, 66, 50, "props"], [83, 53, 66, 55], [83, 54, 66, 56, "audio"], [83, 59, 66, 61], [83, 61, 66, 63, "props"], [83, 66, 66, 68], [83, 67, 66, 69, "video"], [83, 72, 66, 74], [83, 73, 66, 75], [84, 4, 67, 4], [85, 4, 68, 4], [86, 4, 69, 4], [87, 4, 70, 4], [87, 10, 70, 10], [87, 11, 70, 11, "audio"], [87, 16, 70, 16], [87, 18, 70, 18, "video"], [87, 23, 70, 23], [87, 24, 70, 24], [87, 27, 70, 27], [87, 33, 70, 33, "requestLegacyUserMediaAsync"], [87, 60, 70, 60], [87, 61, 70, 61, "props"], [87, 66, 70, 66], [87, 67, 70, 67], [88, 4, 71, 4], [88, 11, 71, 11], [88, 17, 71, 17, "sourceSelectedAsync"], [88, 36, 71, 36], [88, 37, 71, 37, "isMuted"], [88, 44, 71, 44], [88, 46, 71, 46, "audio"], [88, 51, 71, 51], [88, 53, 71, 53, "video"], [88, 58, 71, 58], [88, 59, 71, 59], [89, 2, 72, 0], [90, 2, 73, 7], [90, 17, 73, 22, "getAnyUserMediaAsync"], [90, 37, 73, 42, "getAnyUserMediaAsync"], [90, 38, 73, 43, "constraints"], [90, 49, 73, 54], [90, 51, 73, 56, "ignoreConstraints"], [90, 68, 73, 73], [90, 71, 73, 76], [90, 76, 73, 81], [90, 78, 73, 83], [91, 4, 74, 4], [91, 8, 74, 8], [92, 6, 75, 8], [92, 13, 75, 15], [92, 19, 75, 21, "getUserMediaAsync"], [92, 36, 75, 38], [92, 37, 75, 39], [93, 8, 76, 12], [93, 11, 76, 15, "constraints"], [93, 22, 76, 26], [94, 8, 77, 12, "video"], [94, 13, 77, 17], [94, 15, 77, 19, "ignoreConstraints"], [94, 32, 77, 36], [94, 36, 77, 40, "constraints"], [94, 47, 77, 51], [94, 48, 77, 52, "video"], [95, 6, 78, 8], [95, 7, 78, 9], [95, 8, 78, 10], [96, 4, 79, 4], [96, 5, 79, 5], [96, 6, 80, 4], [96, 13, 80, 11, "error"], [96, 18, 80, 16], [96, 20, 80, 18], [97, 6, 81, 8], [97, 10, 81, 12], [97, 11, 81, 13, "ignoreConstraints"], [97, 28, 81, 30], [97, 32, 82, 12], [97, 39, 82, 19, "error"], [97, 44, 82, 24], [97, 49, 82, 29], [97, 57, 82, 37], [97, 61, 83, 12, "error"], [97, 66, 83, 17], [97, 68, 83, 19, "name"], [97, 72, 83, 23], [97, 77, 83, 28], [97, 106, 83, 57], [97, 108, 83, 59], [98, 8, 84, 12], [98, 15, 84, 19], [98, 21, 84, 25, "getAnyUserMediaAsync"], [98, 41, 84, 45], [98, 42, 84, 46, "constraints"], [98, 53, 84, 57], [98, 55, 84, 59], [98, 59, 84, 63], [98, 60, 84, 64], [99, 6, 85, 8], [100, 6, 86, 8], [100, 12, 86, 14, "error"], [100, 17, 86, 19], [101, 4, 87, 4], [102, 2, 88, 0], [103, 2, 89, 7], [103, 17, 89, 22, "getUserMediaAsync"], [103, 34, 89, 39, "getUserMediaAsync"], [103, 35, 89, 40, "constraints"], [103, 46, 89, 51], [103, 48, 89, 53], [104, 4, 90, 4], [104, 8, 90, 8, "navigator"], [104, 17, 90, 17], [104, 18, 90, 18, "mediaDevices"], [104, 30, 90, 30], [104, 34, 90, 34, "navigator"], [104, 43, 90, 43], [104, 44, 90, 44, "mediaDevices"], [104, 56, 90, 56], [104, 57, 90, 57, "getUserMedia"], [104, 69, 90, 69], [104, 71, 90, 71], [105, 6, 91, 8], [105, 13, 91, 15, "navigator"], [105, 22, 91, 24], [105, 23, 91, 25, "mediaDevices"], [105, 35, 91, 37], [105, 36, 91, 38, "getUserMedia"], [105, 48, 91, 50], [105, 49, 91, 51, "constraints"], [105, 60, 91, 62], [105, 61, 91, 63], [106, 4, 92, 4], [107, 4, 93, 4], [107, 10, 93, 10, "_getUserMedia"], [107, 23, 93, 23], [107, 26, 93, 26, "navigator"], [107, 35, 93, 35], [107, 36, 93, 36], [107, 53, 93, 53], [107, 54, 93, 54], [107, 58, 94, 8, "navigator"], [107, 67, 94, 17], [107, 68, 94, 18], [107, 88, 94, 38], [107, 89, 94, 39], [108, 4, 95, 8], [109, 4, 96, 8, "navigator"], [109, 13, 96, 17], [109, 14, 96, 18], [109, 30, 96, 34], [109, 31, 96, 35], [110, 4, 97, 4], [110, 11, 97, 11], [110, 15, 97, 15, "Promise"], [110, 22, 97, 22], [110, 23, 97, 23], [110, 24, 97, 24, "resolve"], [110, 31, 97, 31], [110, 33, 97, 33, "reject"], [110, 39, 97, 39], [110, 44, 97, 44, "_getUserMedia"], [110, 57, 97, 57], [110, 58, 97, 58, "call"], [110, 62, 97, 62], [110, 63, 97, 63, "navigator"], [110, 72, 97, 72], [110, 74, 97, 74, "constraints"], [110, 85, 97, 85], [110, 87, 97, 87, "resolve"], [110, 94, 97, 94], [110, 96, 97, 96, "reject"], [110, 102, 97, 102], [110, 103, 97, 103], [110, 104, 97, 104], [111, 2, 98, 0], [112, 2, 99, 7], [112, 11, 99, 16, "canGetUserMedia"], [112, 26, 99, 31, "canGetUserMedia"], [112, 27, 99, 31], [112, 29, 99, 34], [113, 4, 100, 4], [114, 4, 101, 4], [115, 6, 102, 4], [116, 6, 103, 4, "Platform"], [116, 31, 103, 12], [116, 32, 103, 13, "isDOMAvailable"], [116, 46, 103, 27], [117, 6, 104, 8], [118, 6, 105, 8], [118, 7, 105, 9], [118, 9, 105, 12, "navigator"], [118, 18, 105, 21], [118, 19, 105, 22, "mediaDevices"], [118, 31, 105, 34], [118, 35, 105, 38, "navigator"], [118, 44, 105, 47], [118, 45, 105, 48, "mediaDevices"], [118, 57, 105, 60], [118, 58, 105, 61, "getUserMedia"], [118, 70, 105, 73], [118, 74, 106, 12, "navigator"], [118, 83, 106, 21], [118, 84, 106, 22], [118, 101, 106, 39], [118, 102, 106, 40], [118, 106, 107, 12, "navigator"], [118, 115, 107, 21], [118, 116, 107, 22], [118, 136, 107, 42], [118, 137, 107, 43], [119, 6, 108, 12], [120, 6, 109, 12, "navigator"], [120, 15, 109, 21], [120, 16, 109, 22], [120, 32, 109, 38], [120, 33, 109, 39], [121, 4, 109, 40], [122, 2, 110, 0], [123, 2, 111, 7], [123, 17, 111, 22, "isFrontCameraAvailableAsync"], [123, 44, 111, 49, "isFrontCameraAvailableAsync"], [123, 45, 111, 50, "devices"], [123, 52, 111, 57], [123, 54, 111, 59], [124, 4, 112, 4], [124, 11, 112, 11], [124, 17, 112, 17, "supportsCameraType"], [124, 35, 112, 35], [124, 36, 112, 36], [124, 37, 112, 37], [124, 44, 112, 44], [124, 46, 112, 46], [124, 52, 112, 52], [124, 54, 112, 54], [124, 64, 112, 64], [124, 65, 112, 65], [124, 67, 112, 67], [124, 73, 112, 73], [124, 75, 112, 75, "devices"], [124, 82, 112, 82], [124, 83, 112, 83], [125, 2, 113, 0], [126, 2, 114, 7], [126, 17, 114, 22, "isBackCameraAvailableAsync"], [126, 43, 114, 48, "isBackCameraAvailableAsync"], [126, 44, 114, 49, "devices"], [126, 51, 114, 56], [126, 53, 114, 58], [127, 4, 115, 4], [127, 11, 115, 11], [127, 17, 115, 17, "supportsCameraType"], [127, 35, 115, 35], [127, 36, 115, 36], [127, 37, 115, 37], [127, 43, 115, 43], [127, 45, 115, 45], [127, 51, 115, 51], [127, 52, 115, 52], [127, 54, 115, 54], [127, 67, 115, 67], [127, 69, 115, 69, "devices"], [127, 76, 115, 76], [127, 77, 115, 77], [128, 2, 116, 0], [129, 2, 117, 0], [129, 17, 117, 15, "supportsCameraType"], [129, 35, 117, 33, "supportsCameraType"], [129, 36, 117, 34, "labels"], [129, 42, 117, 40], [129, 44, 117, 42, "type"], [129, 48, 117, 46], [129, 50, 117, 48, "devices"], [129, 57, 117, 55], [129, 59, 117, 57], [130, 4, 118, 4], [130, 8, 118, 8], [130, 9, 118, 9, "devices"], [130, 16, 118, 16], [130, 18, 118, 18], [131, 6, 119, 8], [131, 10, 119, 12], [131, 11, 119, 13, "navigator"], [131, 20, 119, 22], [131, 21, 119, 23, "mediaDevices"], [131, 33, 119, 35], [131, 34, 119, 36, "enumerateDevices"], [131, 50, 119, 52], [131, 52, 119, 54], [132, 8, 120, 12], [132, 15, 120, 19], [132, 19, 120, 23], [133, 6, 121, 8], [134, 6, 122, 8, "devices"], [134, 13, 122, 15], [134, 16, 122, 18], [134, 22, 122, 24, "navigator"], [134, 31, 122, 33], [134, 32, 122, 34, "mediaDevices"], [134, 44, 122, 46], [134, 45, 122, 47, "enumerateDevices"], [134, 61, 122, 63], [134, 62, 122, 64], [134, 63, 122, 65], [135, 4, 123, 4], [136, 4, 124, 4], [136, 10, 124, 10, "cameras"], [136, 17, 124, 17], [136, 20, 124, 20, "devices"], [136, 27, 124, 27], [136, 28, 124, 28, "filter"], [136, 34, 124, 34], [136, 35, 124, 36, "t"], [136, 36, 124, 37], [136, 40, 124, 42, "t"], [136, 41, 124, 43], [136, 42, 124, 44, "kind"], [136, 46, 124, 48], [136, 51, 124, 53], [136, 63, 124, 65], [136, 64, 124, 66], [137, 4, 125, 4], [137, 10, 125, 10], [137, 11, 125, 11, "hasCamera"], [137, 20, 125, 20], [137, 21, 125, 21], [137, 24, 125, 24, "cameras"], [137, 31, 125, 31], [137, 32, 125, 32, "filter"], [137, 38, 125, 38], [137, 39, 125, 40, "camera"], [137, 45, 125, 46], [137, 49, 125, 51, "labels"], [137, 55, 125, 57], [137, 56, 125, 58, "some"], [137, 60, 125, 62], [137, 61, 125, 64, "label"], [137, 66, 125, 69], [137, 70, 125, 74, "camera"], [137, 76, 125, 80], [137, 77, 125, 81, "label"], [137, 82, 125, 86], [137, 83, 125, 87, "toLowerCase"], [137, 94, 125, 98], [137, 95, 125, 99], [137, 96, 125, 100], [137, 97, 125, 101, "includes"], [137, 105, 125, 109], [137, 106, 125, 110, "label"], [137, 111, 125, 115], [137, 112, 125, 116], [137, 113, 125, 117], [137, 114, 125, 118], [138, 4, 126, 4], [138, 10, 126, 10], [138, 11, 126, 11, "isCapable"], [138, 20, 126, 20], [138, 21, 126, 21], [138, 24, 126, 24, "cameras"], [138, 31, 126, 31], [138, 32, 126, 32, "filter"], [138, 38, 126, 38], [138, 39, 126, 40, "camera"], [138, 45, 126, 46], [138, 49, 126, 51], [139, 6, 127, 8], [139, 10, 127, 12], [139, 12, 127, 14], [139, 29, 127, 31], [139, 33, 127, 35, "camera"], [139, 39, 127, 41], [139, 40, 127, 42], [139, 42, 127, 44], [140, 8, 128, 12], [140, 15, 128, 19], [140, 19, 128, 23], [141, 6, 129, 8], [142, 6, 130, 8], [142, 12, 130, 14, "capabilities"], [142, 24, 130, 26], [142, 27, 130, 29, "camera"], [142, 33, 130, 35], [142, 34, 130, 36, "getCapabilities"], [142, 49, 130, 51], [142, 50, 130, 52], [142, 51, 130, 53], [143, 6, 131, 8], [143, 10, 131, 12], [143, 11, 131, 13, "capabilities"], [143, 23, 131, 25], [143, 24, 131, 26, "facingMode"], [143, 34, 131, 36], [143, 36, 131, 38], [144, 8, 132, 12], [144, 15, 132, 19], [144, 19, 132, 23], [145, 6, 133, 8], [146, 6, 134, 8], [146, 13, 134, 15, "capabilities"], [146, 25, 134, 27], [146, 26, 134, 28, "facingMode"], [146, 36, 134, 38], [146, 37, 134, 39, "find"], [146, 41, 134, 43], [146, 42, 134, 45, "_"], [146, 43, 134, 46], [146, 47, 134, 51, "type"], [146, 51, 134, 55], [146, 52, 134, 56], [147, 4, 135, 4], [147, 5, 135, 5], [147, 6, 135, 6], [148, 4, 136, 4], [148, 11, 136, 11, "isCapable"], [148, 20, 136, 20], [148, 22, 136, 22, "deviceId"], [148, 30, 136, 30], [148, 34, 136, 34, "hasCamera"], [148, 43, 136, 43], [148, 45, 136, 45, "deviceId"], [148, 53, 136, 53], [148, 57, 136, 57], [148, 61, 136, 61], [149, 2, 137, 0], [150, 0, 137, 1], [150, 3]], "functionMap": {"names": ["<global>", "requestLegacyUserMediaAsync", "optionalSource", "constraintToSourceId", "Promise$argument_0", "MediaStreamTrack.getSources$argument_0", "sources.forEach$argument_0", "sourceSelectedAsync", "requestUserMediaAsync", "getAnyUserMediaAsync", "getUserMediaAsync", "canGetUserMedia", "isFrontCameraAvailableAsync", "isBackCameraAvailableAsync", "supportsCameraType", "devices.filter$argument_0", "cameras.filter$argument_0", "labels.some$argument_0", "capabilities.facingMode.find$argument_0"], "mappings": "AAA;ACO;2BCI,0CD;iCEC;KFY;sCGC;gCCE,6BD,CH;oBKG;KLO;CDc;AOC;CPQ;OQC;CRU;OSC;CTe;OUC;uBNQ,gFM;CVC;OWC;CXW;OYC;CZE;OaC;CbE;AcC;mCCO,8BD;uCEC,wBC,qDD,CF;uCEC;4CEQ,WF;KFC;CdE"}}, "type": "js/module"}]}