{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 67, "index": 67}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkMatrix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 68}, "end": {"line": 2, "column": 44, "index": 112}}], "key": "aOVfjZgmz4R2ci39pV6HZujK8og=", "exportNames": ["*"]}}, {"name": "./JsiSkPath", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 113}, "end": {"line": 3, "column": 40, "index": 153}}], "key": "h12LvMRBvFyvLJVrX3awiGHZPFU=", "exportNames": ["*"]}}, {"name": "./JsiSkPathEffect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 154}, "end": {"line": 4, "column": 52, "index": 206}}], "key": "vX5UcJL7b59BiVotyhwArfDZk24=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPathEffectFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkMatrix = require(_dependencyMap[1], \"./JsiSkMatrix\");\n  var _JsiSkPath = require(_dependencyMap[2], \"./JsiSkPath\");\n  var _JsiSkPathEffect = require(_dependencyMap[3], \"./JsiSkPathEffect\");\n  class JsiSkPathEffectFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeCorner(radius) {\n      const pe = this.CanvasKit.PathEffect.MakeCorner(radius);\n      if (pe === null) {\n        return null;\n      }\n      return new _JsiSkPathEffect.JsiSkPathEffect(this.CanvasKit, pe);\n    }\n    MakeDash(intervals, phase) {\n      const pe = this.CanvasKit.PathEffect.MakeDash(intervals, phase);\n      return new _JsiSkPathEffect.JsiSkPathEffect(this.CanvasKit, pe);\n    }\n    MakeDiscrete(segLength, dev, seedAssist) {\n      const pe = this.CanvasKit.PathEffect.MakeDiscrete(segLength, dev, seedAssist);\n      return new _JsiSkPathEffect.JsiSkPathEffect(this.CanvasKit, pe);\n    }\n    MakeCompose(_outer, _inner) {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n    MakeSum(_outer, _inner) {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n    MakeLine2D(width, matrix) {\n      const pe = this.CanvasKit.PathEffect.MakeLine2D(width, _JsiSkMatrix.JsiSkMatrix.fromValue(matrix));\n      if (pe === null) {\n        return null;\n      }\n      return new _JsiSkPathEffect.JsiSkPathEffect(this.CanvasKit, pe);\n    }\n    MakePath1D(path, advance, phase, style) {\n      const pe = this.CanvasKit.PathEffect.MakePath1D(_JsiSkPath.JsiSkPath.fromValue(path), advance, phase, (0, _Host.getEnum)(this.CanvasKit, \"Path1DEffect\", style));\n      if (pe === null) {\n        return null;\n      }\n      return new _JsiSkPathEffect.JsiSkPathEffect(this.CanvasKit, pe);\n    }\n    MakePath2D(matrix, path) {\n      const pe = this.CanvasKit.PathEffect.MakePath2D(_JsiSkMatrix.JsiSkMatrix.fromValue(matrix), _JsiSkPath.JsiSkPath.fromValue(path));\n      if (pe === null) {\n        return null;\n      }\n      return new _JsiSkPathEffect.JsiSkPathEffect(this.CanvasKit, pe);\n    }\n  }\n  exports.JsiSkPathEffectFactory = JsiSkPathEffectFactory;\n});", "lineCount": 58, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkMatrix"], [7, 18, 2, 0], [7, 21, 2, 0, "require"], [7, 28, 2, 0], [7, 29, 2, 0, "_dependencyMap"], [7, 43, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkPath"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JsiSkPathEffect"], [9, 22, 4, 0], [9, 25, 4, 0, "require"], [9, 32, 4, 0], [9, 33, 4, 0, "_dependencyMap"], [9, 47, 4, 0], [10, 2, 5, 7], [10, 8, 5, 13, "JsiSkPathEffectFactory"], [10, 30, 5, 35], [10, 39, 5, 44, "Host"], [10, 49, 5, 48], [10, 50, 5, 49], [11, 4, 6, 2, "constructor"], [11, 15, 6, 13, "constructor"], [11, 16, 6, 14, "CanvasKit"], [11, 25, 6, 23], [11, 27, 6, 25], [12, 6, 7, 4], [12, 11, 7, 9], [12, 12, 7, 10, "CanvasKit"], [12, 21, 7, 19], [12, 22, 7, 20], [13, 4, 8, 2], [14, 4, 9, 2, "<PERSON><PERSON><PERSON><PERSON>"], [14, 14, 9, 12, "<PERSON><PERSON><PERSON><PERSON>"], [14, 15, 9, 13, "radius"], [14, 21, 9, 19], [14, 23, 9, 21], [15, 6, 10, 4], [15, 12, 10, 10, "pe"], [15, 14, 10, 12], [15, 17, 10, 15], [15, 21, 10, 19], [15, 22, 10, 20, "CanvasKit"], [15, 31, 10, 29], [15, 32, 10, 30, "PathEffect"], [15, 42, 10, 40], [15, 43, 10, 41, "<PERSON><PERSON><PERSON><PERSON>"], [15, 53, 10, 51], [15, 54, 10, 52, "radius"], [15, 60, 10, 58], [15, 61, 10, 59], [16, 6, 11, 4], [16, 10, 11, 8, "pe"], [16, 12, 11, 10], [16, 17, 11, 15], [16, 21, 11, 19], [16, 23, 11, 21], [17, 8, 12, 6], [17, 15, 12, 13], [17, 19, 12, 17], [18, 6, 13, 4], [19, 6, 14, 4], [19, 13, 14, 11], [19, 17, 14, 15, "JsiSkPathEffect"], [19, 49, 14, 30], [19, 50, 14, 31], [19, 54, 14, 35], [19, 55, 14, 36, "CanvasKit"], [19, 64, 14, 45], [19, 66, 14, 47, "pe"], [19, 68, 14, 49], [19, 69, 14, 50], [20, 4, 15, 2], [21, 4, 16, 2, "MakeDash"], [21, 12, 16, 10, "MakeDash"], [21, 13, 16, 11, "intervals"], [21, 22, 16, 20], [21, 24, 16, 22, "phase"], [21, 29, 16, 27], [21, 31, 16, 29], [22, 6, 17, 4], [22, 12, 17, 10, "pe"], [22, 14, 17, 12], [22, 17, 17, 15], [22, 21, 17, 19], [22, 22, 17, 20, "CanvasKit"], [22, 31, 17, 29], [22, 32, 17, 30, "PathEffect"], [22, 42, 17, 40], [22, 43, 17, 41, "MakeDash"], [22, 51, 17, 49], [22, 52, 17, 50, "intervals"], [22, 61, 17, 59], [22, 63, 17, 61, "phase"], [22, 68, 17, 66], [22, 69, 17, 67], [23, 6, 18, 4], [23, 13, 18, 11], [23, 17, 18, 15, "JsiSkPathEffect"], [23, 49, 18, 30], [23, 50, 18, 31], [23, 54, 18, 35], [23, 55, 18, 36, "CanvasKit"], [23, 64, 18, 45], [23, 66, 18, 47, "pe"], [23, 68, 18, 49], [23, 69, 18, 50], [24, 4, 19, 2], [25, 4, 20, 2, "MakeDiscrete"], [25, 16, 20, 14, "MakeDiscrete"], [25, 17, 20, 15, "seg<PERSON><PERSON><PERSON>"], [25, 26, 20, 24], [25, 28, 20, 26, "dev"], [25, 31, 20, 29], [25, 33, 20, 31, "seedAssist"], [25, 43, 20, 41], [25, 45, 20, 43], [26, 6, 21, 4], [26, 12, 21, 10, "pe"], [26, 14, 21, 12], [26, 17, 21, 15], [26, 21, 21, 19], [26, 22, 21, 20, "CanvasKit"], [26, 31, 21, 29], [26, 32, 21, 30, "PathEffect"], [26, 42, 21, 40], [26, 43, 21, 41, "MakeDiscrete"], [26, 55, 21, 53], [26, 56, 21, 54, "seg<PERSON><PERSON><PERSON>"], [26, 65, 21, 63], [26, 67, 21, 65, "dev"], [26, 70, 21, 68], [26, 72, 21, 70, "seedAssist"], [26, 82, 21, 80], [26, 83, 21, 81], [27, 6, 22, 4], [27, 13, 22, 11], [27, 17, 22, 15, "JsiSkPathEffect"], [27, 49, 22, 30], [27, 50, 22, 31], [27, 54, 22, 35], [27, 55, 22, 36, "CanvasKit"], [27, 64, 22, 45], [27, 66, 22, 47, "pe"], [27, 68, 22, 49], [27, 69, 22, 50], [28, 4, 23, 2], [29, 4, 24, 2, "MakeCompose"], [29, 15, 24, 13, "MakeCompose"], [29, 16, 24, 14, "_outer"], [29, 22, 24, 20], [29, 24, 24, 22, "_inner"], [29, 30, 24, 28], [29, 32, 24, 30], [30, 6, 25, 4], [30, 13, 25, 11], [30, 17, 25, 11, "throwNotImplementedOnRNWeb"], [30, 49, 25, 37], [30, 51, 25, 38], [30, 52, 25, 39], [31, 4, 26, 2], [32, 4, 27, 2, "MakeSum"], [32, 11, 27, 9, "MakeSum"], [32, 12, 27, 10, "_outer"], [32, 18, 27, 16], [32, 20, 27, 18, "_inner"], [32, 26, 27, 24], [32, 28, 27, 26], [33, 6, 28, 4], [33, 13, 28, 11], [33, 17, 28, 11, "throwNotImplementedOnRNWeb"], [33, 49, 28, 37], [33, 51, 28, 38], [33, 52, 28, 39], [34, 4, 29, 2], [35, 4, 30, 2, "MakeLine2D"], [35, 14, 30, 12, "MakeLine2D"], [35, 15, 30, 13, "width"], [35, 20, 30, 18], [35, 22, 30, 20, "matrix"], [35, 28, 30, 26], [35, 30, 30, 28], [36, 6, 31, 4], [36, 12, 31, 10, "pe"], [36, 14, 31, 12], [36, 17, 31, 15], [36, 21, 31, 19], [36, 22, 31, 20, "CanvasKit"], [36, 31, 31, 29], [36, 32, 31, 30, "PathEffect"], [36, 42, 31, 40], [36, 43, 31, 41, "MakeLine2D"], [36, 53, 31, 51], [36, 54, 31, 52, "width"], [36, 59, 31, 57], [36, 61, 31, 59, "JsiSkMatrix"], [36, 85, 31, 70], [36, 86, 31, 71, "fromValue"], [36, 95, 31, 80], [36, 96, 31, 81, "matrix"], [36, 102, 31, 87], [36, 103, 31, 88], [36, 104, 31, 89], [37, 6, 32, 4], [37, 10, 32, 8, "pe"], [37, 12, 32, 10], [37, 17, 32, 15], [37, 21, 32, 19], [37, 23, 32, 21], [38, 8, 33, 6], [38, 15, 33, 13], [38, 19, 33, 17], [39, 6, 34, 4], [40, 6, 35, 4], [40, 13, 35, 11], [40, 17, 35, 15, "JsiSkPathEffect"], [40, 49, 35, 30], [40, 50, 35, 31], [40, 54, 35, 35], [40, 55, 35, 36, "CanvasKit"], [40, 64, 35, 45], [40, 66, 35, 47, "pe"], [40, 68, 35, 49], [40, 69, 35, 50], [41, 4, 36, 2], [42, 4, 37, 2, "MakePath1D"], [42, 14, 37, 12, "MakePath1D"], [42, 15, 37, 13, "path"], [42, 19, 37, 17], [42, 21, 37, 19, "advance"], [42, 28, 37, 26], [42, 30, 37, 28, "phase"], [42, 35, 37, 33], [42, 37, 37, 35, "style"], [42, 42, 37, 40], [42, 44, 37, 42], [43, 6, 38, 4], [43, 12, 38, 10, "pe"], [43, 14, 38, 12], [43, 17, 38, 15], [43, 21, 38, 19], [43, 22, 38, 20, "CanvasKit"], [43, 31, 38, 29], [43, 32, 38, 30, "PathEffect"], [43, 42, 38, 40], [43, 43, 38, 41, "MakePath1D"], [43, 53, 38, 51], [43, 54, 38, 52, "JsiSkPath"], [43, 74, 38, 61], [43, 75, 38, 62, "fromValue"], [43, 84, 38, 71], [43, 85, 38, 72, "path"], [43, 89, 38, 76], [43, 90, 38, 77], [43, 92, 38, 79, "advance"], [43, 99, 38, 86], [43, 101, 38, 88, "phase"], [43, 106, 38, 93], [43, 108, 38, 95], [43, 112, 38, 95, "getEnum"], [43, 125, 38, 102], [43, 127, 38, 103], [43, 131, 38, 107], [43, 132, 38, 108, "CanvasKit"], [43, 141, 38, 117], [43, 143, 38, 119], [43, 157, 38, 133], [43, 159, 38, 135, "style"], [43, 164, 38, 140], [43, 165, 38, 141], [43, 166, 38, 142], [44, 6, 39, 4], [44, 10, 39, 8, "pe"], [44, 12, 39, 10], [44, 17, 39, 15], [44, 21, 39, 19], [44, 23, 39, 21], [45, 8, 40, 6], [45, 15, 40, 13], [45, 19, 40, 17], [46, 6, 41, 4], [47, 6, 42, 4], [47, 13, 42, 11], [47, 17, 42, 15, "JsiSkPathEffect"], [47, 49, 42, 30], [47, 50, 42, 31], [47, 54, 42, 35], [47, 55, 42, 36, "CanvasKit"], [47, 64, 42, 45], [47, 66, 42, 47, "pe"], [47, 68, 42, 49], [47, 69, 42, 50], [48, 4, 43, 2], [49, 4, 44, 2, "MakePath2D"], [49, 14, 44, 12, "MakePath2D"], [49, 15, 44, 13, "matrix"], [49, 21, 44, 19], [49, 23, 44, 21, "path"], [49, 27, 44, 25], [49, 29, 44, 27], [50, 6, 45, 4], [50, 12, 45, 10, "pe"], [50, 14, 45, 12], [50, 17, 45, 15], [50, 21, 45, 19], [50, 22, 45, 20, "CanvasKit"], [50, 31, 45, 29], [50, 32, 45, 30, "PathEffect"], [50, 42, 45, 40], [50, 43, 45, 41, "MakePath2D"], [50, 53, 45, 51], [50, 54, 45, 52, "JsiSkMatrix"], [50, 78, 45, 63], [50, 79, 45, 64, "fromValue"], [50, 88, 45, 73], [50, 89, 45, 74, "matrix"], [50, 95, 45, 80], [50, 96, 45, 81], [50, 98, 45, 83, "JsiSkPath"], [50, 118, 45, 92], [50, 119, 45, 93, "fromValue"], [50, 128, 45, 102], [50, 129, 45, 103, "path"], [50, 133, 45, 107], [50, 134, 45, 108], [50, 135, 45, 109], [51, 6, 46, 4], [51, 10, 46, 8, "pe"], [51, 12, 46, 10], [51, 17, 46, 15], [51, 21, 46, 19], [51, 23, 46, 21], [52, 8, 47, 6], [52, 15, 47, 13], [52, 19, 47, 17], [53, 6, 48, 4], [54, 6, 49, 4], [54, 13, 49, 11], [54, 17, 49, 15, "JsiSkPathEffect"], [54, 49, 49, 30], [54, 50, 49, 31], [54, 54, 49, 35], [54, 55, 49, 36, "CanvasKit"], [54, 64, 49, 45], [54, 66, 49, 47, "pe"], [54, 68, 49, 49], [54, 69, 49, 50], [55, 4, 50, 2], [56, 2, 51, 0], [57, 2, 51, 1, "exports"], [57, 9, 51, 1], [57, 10, 51, 1, "JsiSkPathEffectFactory"], [57, 32, 51, 1], [57, 35, 51, 1, "JsiSkPathEffectFactory"], [57, 57, 51, 1], [58, 0, 51, 1], [58, 3]], "functionMap": {"names": ["<global>", "JsiSkPathEffectFactory", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "MakeDash", "MakeDiscrete", "MakeCompose", "MakeSum", "MakeLine2D", "MakePath1D", "MakePath2D"], "mappings": "AAA;OCI;ECC;GDE;EEC;GFM;EGC;GHG;EIC;GJG;EKC;GLE;EMC;GNE;EOC;GPM;EQC;GRM;ESC;GTM;CDC"}}, "type": "js/module"}]}