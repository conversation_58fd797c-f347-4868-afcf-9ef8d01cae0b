{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 64, "index": 666}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkTypefaceFontProvider = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkTypefaceFontProvider extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"FontMgr\");\n      _defineProperty(this, \"allocatedPointers\", []);\n    }\n    matchFamilyStyle(_name, _style) {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n    countFamilies() {\n      return this.ref.countFamilies();\n    }\n    getFamilyName(index) {\n      return this.ref.getFamilyName(index);\n    }\n    registerFont(typeface, familyName) {\n      const strLen = lengthBytesUTF8(familyName) + 1;\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-expect-error\n      const strPtr = this.CanvasKit._malloc(strLen);\n      stringToUTF8(this.CanvasKit, familyName, strPtr, strLen);\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-expect-error\n      this.ref._registerFont(typeface.ref, strPtr);\n    }\n    dispose() {\n      for (const ptr of this.allocatedPointers) {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-expect-error\n        this.CanvasKit._free(ptr);\n      }\n      this.ref.delete();\n    }\n  }\n  exports.JsiSkTypefaceFontProvider = JsiSkTypefaceFontProvider;\n  const lengthBytesUTF8 = str => {\n    // TextEncoder will give us the byte length in UTF8 form\n    const encoder = new TextEncoder();\n    const utf8 = encoder.encode(str);\n    return utf8.length;\n  };\n  const stringToUTF8 = (CanvasKit, str, outPtr, maxBytesToWrite) => {\n    // TextEncoder will give us the byte array in UTF8 form\n    const encoder = new TextEncoder();\n    const utf8 = encoder.encode(str);\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-expect-error\n    const heap = CanvasKit.HEAPU8;\n\n    // Check if there's enough space\n    if (utf8.length > maxBytesToWrite) {\n      throw new Error(\"Not enough space to write UTF8 encoded string\");\n    }\n\n    // Copy the bytes\n    for (let i = 0; i < utf8.length; i++) {\n      heap[outPtr + i] = utf8[i];\n    }\n\n    // Null terminate\n    if (utf8.length < maxBytesToWrite) {\n      heap[outPtr + utf8.length] = 0;\n    }\n  };\n});", "lineCount": 94, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "e"], [7, 28, 1, 26], [7, 30, 1, 28, "r"], [7, 31, 1, 29], [7, 33, 1, 31, "t"], [7, 34, 1, 32], [7, 36, 1, 34], [8, 4, 1, 36], [8, 11, 1, 43], [8, 12, 1, 44, "r"], [8, 13, 1, 45], [8, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 30, 1, 62], [8, 31, 1, 63, "r"], [8, 32, 1, 64], [8, 33, 1, 65], [8, 38, 1, 70, "e"], [8, 39, 1, 71], [8, 42, 1, 74, "Object"], [8, 48, 1, 80], [8, 49, 1, 81, "defineProperty"], [8, 63, 1, 95], [8, 64, 1, 96, "e"], [8, 65, 1, 97], [8, 67, 1, 99, "r"], [8, 68, 1, 100], [8, 70, 1, 102], [9, 6, 1, 104, "value"], [9, 11, 1, 109], [9, 13, 1, 111, "t"], [9, 14, 1, 112], [10, 6, 1, 114, "enumerable"], [10, 16, 1, 124], [10, 18, 1, 126], [10, 19, 1, 127], [10, 20, 1, 128], [11, 6, 1, 130, "configurable"], [11, 18, 1, 142], [11, 20, 1, 144], [11, 21, 1, 145], [11, 22, 1, 146], [12, 6, 1, 148, "writable"], [12, 14, 1, 156], [12, 16, 1, 158], [12, 17, 1, 159], [13, 4, 1, 161], [13, 5, 1, 162], [13, 6, 1, 163], [13, 9, 1, 166, "e"], [13, 10, 1, 167], [13, 11, 1, 168, "r"], [13, 12, 1, 169], [13, 13, 1, 170], [13, 16, 1, 173, "t"], [13, 17, 1, 174], [13, 19, 1, 176, "e"], [13, 20, 1, 177], [14, 2, 1, 179], [15, 2, 2, 0], [15, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 26, 2, 24, "t"], [15, 27, 2, 25], [15, 29, 2, 27], [16, 4, 2, 29], [16, 8, 2, 33, "i"], [16, 9, 2, 34], [16, 12, 2, 37, "_toPrimitive"], [16, 24, 2, 49], [16, 25, 2, 50, "t"], [16, 26, 2, 51], [16, 28, 2, 53], [16, 36, 2, 61], [16, 37, 2, 62], [17, 4, 2, 64], [17, 11, 2, 71], [17, 19, 2, 79], [17, 23, 2, 83], [17, 30, 2, 90, "i"], [17, 31, 2, 91], [17, 34, 2, 94, "i"], [17, 35, 2, 95], [17, 38, 2, 98, "i"], [17, 39, 2, 99], [17, 42, 2, 102], [17, 44, 2, 104], [18, 2, 2, 106], [19, 2, 3, 0], [19, 11, 3, 9, "_toPrimitive"], [19, 23, 3, 21, "_toPrimitive"], [19, 24, 3, 22, "t"], [19, 25, 3, 23], [19, 27, 3, 25, "r"], [19, 28, 3, 26], [19, 30, 3, 28], [20, 4, 3, 30], [20, 8, 3, 34], [20, 16, 3, 42], [20, 20, 3, 46], [20, 27, 3, 53, "t"], [20, 28, 3, 54], [20, 32, 3, 58], [20, 33, 3, 59, "t"], [20, 34, 3, 60], [20, 36, 3, 62], [20, 43, 3, 69, "t"], [20, 44, 3, 70], [21, 4, 3, 72], [21, 8, 3, 76, "e"], [21, 9, 3, 77], [21, 12, 3, 80, "t"], [21, 13, 3, 81], [21, 14, 3, 82, "Symbol"], [21, 20, 3, 88], [21, 21, 3, 89, "toPrimitive"], [21, 32, 3, 100], [21, 33, 3, 101], [22, 4, 3, 103], [22, 8, 3, 107], [22, 13, 3, 112], [22, 14, 3, 113], [22, 19, 3, 118, "e"], [22, 20, 3, 119], [22, 22, 3, 121], [23, 6, 3, 123], [23, 10, 3, 127, "i"], [23, 11, 3, 128], [23, 14, 3, 131, "e"], [23, 15, 3, 132], [23, 16, 3, 133, "call"], [23, 20, 3, 137], [23, 21, 3, 138, "t"], [23, 22, 3, 139], [23, 24, 3, 141, "r"], [23, 25, 3, 142], [23, 29, 3, 146], [23, 38, 3, 155], [23, 39, 3, 156], [24, 6, 3, 158], [24, 10, 3, 162], [24, 18, 3, 170], [24, 22, 3, 174], [24, 29, 3, 181, "i"], [24, 30, 3, 182], [24, 32, 3, 184], [24, 39, 3, 191, "i"], [24, 40, 3, 192], [25, 6, 3, 194], [25, 12, 3, 200], [25, 16, 3, 204, "TypeError"], [25, 25, 3, 213], [25, 26, 3, 214], [25, 72, 3, 260], [25, 73, 3, 261], [26, 4, 3, 263], [27, 4, 3, 265], [27, 11, 3, 272], [27, 12, 3, 273], [27, 20, 3, 281], [27, 25, 3, 286, "r"], [27, 26, 3, 287], [27, 29, 3, 290, "String"], [27, 35, 3, 296], [27, 38, 3, 299, "Number"], [27, 44, 3, 305], [27, 46, 3, 307, "t"], [27, 47, 3, 308], [27, 48, 3, 309], [28, 2, 3, 311], [29, 2, 5, 7], [29, 8, 5, 13, "JsiSkTypefaceFontProvider"], [29, 33, 5, 38], [29, 42, 5, 47, "HostObject"], [29, 58, 5, 57], [29, 59, 5, 58], [30, 4, 6, 2, "constructor"], [30, 15, 6, 13, "constructor"], [30, 16, 6, 14, "CanvasKit"], [30, 25, 6, 23], [30, 27, 6, 25, "ref"], [30, 30, 6, 28], [30, 32, 6, 30], [31, 6, 7, 4], [31, 11, 7, 9], [31, 12, 7, 10, "CanvasKit"], [31, 21, 7, 19], [31, 23, 7, 21, "ref"], [31, 26, 7, 24], [31, 28, 7, 26], [31, 37, 7, 35], [31, 38, 7, 36], [32, 6, 8, 4, "_defineProperty"], [32, 21, 8, 19], [32, 22, 8, 20], [32, 26, 8, 24], [32, 28, 8, 26], [32, 47, 8, 45], [32, 49, 8, 47], [32, 51, 8, 49], [32, 52, 8, 50], [33, 4, 9, 2], [34, 4, 10, 2, "matchFamilyStyle"], [34, 20, 10, 18, "matchFamilyStyle"], [34, 21, 10, 19, "_name"], [34, 26, 10, 24], [34, 28, 10, 26, "_style"], [34, 34, 10, 32], [34, 36, 10, 34], [35, 6, 11, 4], [35, 13, 11, 11], [35, 17, 11, 11, "throwNotImplementedOnRNWeb"], [35, 49, 11, 37], [35, 51, 11, 38], [35, 52, 11, 39], [36, 4, 12, 2], [37, 4, 13, 2, "countFamilies"], [37, 17, 13, 15, "countFamilies"], [37, 18, 13, 15], [37, 20, 13, 18], [38, 6, 14, 4], [38, 13, 14, 11], [38, 17, 14, 15], [38, 18, 14, 16, "ref"], [38, 21, 14, 19], [38, 22, 14, 20, "countFamilies"], [38, 35, 14, 33], [38, 36, 14, 34], [38, 37, 14, 35], [39, 4, 15, 2], [40, 4, 16, 2, "getFamilyName"], [40, 17, 16, 15, "getFamilyName"], [40, 18, 16, 16, "index"], [40, 23, 16, 21], [40, 25, 16, 23], [41, 6, 17, 4], [41, 13, 17, 11], [41, 17, 17, 15], [41, 18, 17, 16, "ref"], [41, 21, 17, 19], [41, 22, 17, 20, "getFamilyName"], [41, 35, 17, 33], [41, 36, 17, 34, "index"], [41, 41, 17, 39], [41, 42, 17, 40], [42, 4, 18, 2], [43, 4, 19, 2, "registerFont"], [43, 16, 19, 14, "registerFont"], [43, 17, 19, 15, "typeface"], [43, 25, 19, 23], [43, 27, 19, 25, "<PERSON><PERSON>ame"], [43, 37, 19, 35], [43, 39, 19, 37], [44, 6, 20, 4], [44, 12, 20, 10, "strLen"], [44, 18, 20, 16], [44, 21, 20, 19, "lengthBytesUTF8"], [44, 36, 20, 34], [44, 37, 20, 35, "<PERSON><PERSON>ame"], [44, 47, 20, 45], [44, 48, 20, 46], [44, 51, 20, 49], [44, 52, 20, 50], [46, 6, 22, 4], [47, 6, 23, 4], [48, 6, 24, 4], [48, 12, 24, 10, "strPtr"], [48, 18, 24, 16], [48, 21, 24, 19], [48, 25, 24, 23], [48, 26, 24, 24, "CanvasKit"], [48, 35, 24, 33], [48, 36, 24, 34, "_malloc"], [48, 43, 24, 41], [48, 44, 24, 42, "strLen"], [48, 50, 24, 48], [48, 51, 24, 49], [49, 6, 25, 4, "stringToUTF8"], [49, 18, 25, 16], [49, 19, 25, 17], [49, 23, 25, 21], [49, 24, 25, 22, "CanvasKit"], [49, 33, 25, 31], [49, 35, 25, 33, "<PERSON><PERSON>ame"], [49, 45, 25, 43], [49, 47, 25, 45, "strPtr"], [49, 53, 25, 51], [49, 55, 25, 53, "strLen"], [49, 61, 25, 59], [49, 62, 25, 60], [51, 6, 27, 4], [52, 6, 28, 4], [53, 6, 29, 4], [53, 10, 29, 8], [53, 11, 29, 9, "ref"], [53, 14, 29, 12], [53, 15, 29, 13, "_registerFont"], [53, 28, 29, 26], [53, 29, 29, 27, "typeface"], [53, 37, 29, 35], [53, 38, 29, 36, "ref"], [53, 41, 29, 39], [53, 43, 29, 41, "strPtr"], [53, 49, 29, 47], [53, 50, 29, 48], [54, 4, 30, 2], [55, 4, 31, 2, "dispose"], [55, 11, 31, 9, "dispose"], [55, 12, 31, 9], [55, 14, 31, 12], [56, 6, 32, 4], [56, 11, 32, 9], [56, 17, 32, 15, "ptr"], [56, 20, 32, 18], [56, 24, 32, 22], [56, 28, 32, 26], [56, 29, 32, 27, "allocatedPointers"], [56, 46, 32, 44], [56, 48, 32, 46], [57, 8, 33, 6], [58, 8, 34, 6], [59, 8, 35, 6], [59, 12, 35, 10], [59, 13, 35, 11, "CanvasKit"], [59, 22, 35, 20], [59, 23, 35, 21, "_free"], [59, 28, 35, 26], [59, 29, 35, 27, "ptr"], [59, 32, 35, 30], [59, 33, 35, 31], [60, 6, 36, 4], [61, 6, 37, 4], [61, 10, 37, 8], [61, 11, 37, 9, "ref"], [61, 14, 37, 12], [61, 15, 37, 13, "delete"], [61, 21, 37, 19], [61, 22, 37, 20], [61, 23, 37, 21], [62, 4, 38, 2], [63, 2, 39, 0], [64, 2, 39, 1, "exports"], [64, 9, 39, 1], [64, 10, 39, 1, "JsiSkTypefaceFontProvider"], [64, 35, 39, 1], [64, 38, 39, 1, "JsiSkTypefaceFontProvider"], [64, 63, 39, 1], [65, 2, 40, 0], [65, 8, 40, 6, "lengthBytesUTF8"], [65, 23, 40, 21], [65, 26, 40, 24, "str"], [65, 29, 40, 27], [65, 33, 40, 31], [66, 4, 41, 2], [67, 4, 42, 2], [67, 10, 42, 8, "encoder"], [67, 17, 42, 15], [67, 20, 42, 18], [67, 24, 42, 22, "TextEncoder"], [67, 35, 42, 33], [67, 36, 42, 34], [67, 37, 42, 35], [68, 4, 43, 2], [68, 10, 43, 8, "utf8"], [68, 14, 43, 12], [68, 17, 43, 15, "encoder"], [68, 24, 43, 22], [68, 25, 43, 23, "encode"], [68, 31, 43, 29], [68, 32, 43, 30, "str"], [68, 35, 43, 33], [68, 36, 43, 34], [69, 4, 44, 2], [69, 11, 44, 9, "utf8"], [69, 15, 44, 13], [69, 16, 44, 14, "length"], [69, 22, 44, 20], [70, 2, 45, 0], [70, 3, 45, 1], [71, 2, 46, 0], [71, 8, 46, 6, "stringToUTF8"], [71, 20, 46, 18], [71, 23, 46, 21, "stringToUTF8"], [71, 24, 46, 22, "CanvasKit"], [71, 33, 46, 31], [71, 35, 46, 33, "str"], [71, 38, 46, 36], [71, 40, 46, 38, "outPtr"], [71, 46, 46, 44], [71, 48, 46, 46, "maxBytesToWrite"], [71, 63, 46, 61], [71, 68, 46, 66], [72, 4, 47, 2], [73, 4, 48, 2], [73, 10, 48, 8, "encoder"], [73, 17, 48, 15], [73, 20, 48, 18], [73, 24, 48, 22, "TextEncoder"], [73, 35, 48, 33], [73, 36, 48, 34], [73, 37, 48, 35], [74, 4, 49, 2], [74, 10, 49, 8, "utf8"], [74, 14, 49, 12], [74, 17, 49, 15, "encoder"], [74, 24, 49, 22], [74, 25, 49, 23, "encode"], [74, 31, 49, 29], [74, 32, 49, 30, "str"], [74, 35, 49, 33], [74, 36, 49, 34], [75, 4, 50, 2], [76, 4, 51, 2], [77, 4, 52, 2], [77, 10, 52, 8, "heap"], [77, 14, 52, 12], [77, 17, 52, 15, "CanvasKit"], [77, 26, 52, 24], [77, 27, 52, 25, "HEAPU8"], [77, 33, 52, 31], [79, 4, 54, 2], [80, 4, 55, 2], [80, 8, 55, 6, "utf8"], [80, 12, 55, 10], [80, 13, 55, 11, "length"], [80, 19, 55, 17], [80, 22, 55, 20, "maxBytesToWrite"], [80, 37, 55, 35], [80, 39, 55, 37], [81, 6, 56, 4], [81, 12, 56, 10], [81, 16, 56, 14, "Error"], [81, 21, 56, 19], [81, 22, 56, 20], [81, 69, 56, 67], [81, 70, 56, 68], [82, 4, 57, 2], [84, 4, 59, 2], [85, 4, 60, 2], [85, 9, 60, 7], [85, 13, 60, 11, "i"], [85, 14, 60, 12], [85, 17, 60, 15], [85, 18, 60, 16], [85, 20, 60, 18, "i"], [85, 21, 60, 19], [85, 24, 60, 22, "utf8"], [85, 28, 60, 26], [85, 29, 60, 27, "length"], [85, 35, 60, 33], [85, 37, 60, 35, "i"], [85, 38, 60, 36], [85, 40, 60, 38], [85, 42, 60, 40], [86, 6, 61, 4, "heap"], [86, 10, 61, 8], [86, 11, 61, 9, "outPtr"], [86, 17, 61, 15], [86, 20, 61, 18, "i"], [86, 21, 61, 19], [86, 22, 61, 20], [86, 25, 61, 23, "utf8"], [86, 29, 61, 27], [86, 30, 61, 28, "i"], [86, 31, 61, 29], [86, 32, 61, 30], [87, 4, 62, 2], [89, 4, 64, 2], [90, 4, 65, 2], [90, 8, 65, 6, "utf8"], [90, 12, 65, 10], [90, 13, 65, 11, "length"], [90, 19, 65, 17], [90, 22, 65, 20, "maxBytesToWrite"], [90, 37, 65, 35], [90, 39, 65, 37], [91, 6, 66, 4, "heap"], [91, 10, 66, 8], [91, 11, 66, 9, "outPtr"], [91, 17, 66, 15], [91, 20, 66, 18, "utf8"], [91, 24, 66, 22], [91, 25, 66, 23, "length"], [91, 31, 66, 29], [91, 32, 66, 30], [91, 35, 66, 33], [91, 36, 66, 34], [92, 4, 67, 2], [93, 2, 68, 0], [93, 3, 68, 1], [94, 0, 68, 2], [94, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkTypefaceFontProvider", "constructor", "matchFamilyStyle", "countFamilies", "getFamilyName", "registerFont", "dispose", "lengthBytesUTF8", "stringToUTF8"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGE;ECC;GDG;EEC;GFE;EGC;GHE;EIC;GJE;EKC;GLW;EMC;GNO;CHC;wBUC;CVK;qBWC;CXsB"}}, "type": "js/module"}]}