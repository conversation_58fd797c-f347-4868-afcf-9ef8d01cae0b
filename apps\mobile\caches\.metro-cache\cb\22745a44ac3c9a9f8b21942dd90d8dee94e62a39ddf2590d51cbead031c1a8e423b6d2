{"dependencies": [{"name": "./CanvasKitWebGLBufferImpl", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 70, "index": 672}}], "key": "y3rJnmArlf7NGDqFDnjogJJQDw4=", "exportNames": ["*"]}}, {"name": "./JsiSkImageFactory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 673}, "end": {"line": 5, "column": 56, "index": 729}}], "key": "orJVjKA7eKpKgOM8dE8JQ/Gzh98=", "exportNames": ["*"]}}, {"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 730}, "end": {"line": 6, "column": 52, "index": 782}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createVideo = exports.JsiVideo = void 0;\n  var _CanvasKitWebGLBufferImpl = require(_dependencyMap[0], \"./CanvasKitWebGLBufferImpl\");\n  var _JsiSkImageFactory = require(_dependencyMap[1], \"./JsiSkImageFactory\");\n  var _Host = require(_dependencyMap[2], \"./Host\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  const createVideo = async (CanvasKit, url) => {\n    const video = document.createElement(\"video\");\n    return new Promise((resolve, reject) => {\n      video.src = url;\n      video.style.display = \"none\";\n      video.crossOrigin = \"anonymous\";\n      video.volume = 0;\n      video.addEventListener(\"loadedmetadata\", () => {\n        document.body.appendChild(video);\n        resolve(new JsiVideo(new _JsiSkImageFactory.JsiSkImageFactory(CanvasKit), video));\n      });\n      video.addEventListener(\"error\", () => {\n        reject(new Error(`Failed to load video from URL: ${url}`));\n      });\n    });\n  };\n  exports.createVideo = createVideo;\n  class JsiVideo {\n    constructor(ImageFactory, videoElement) {\n      this.ImageFactory = ImageFactory;\n      this.videoElement = videoElement;\n      _defineProperty(this, \"__typename__\", \"Video\");\n      _defineProperty(this, \"webglBuffer\", null);\n      document.body.appendChild(this.videoElement);\n    }\n    duration() {\n      return this.videoElement.duration * 1000;\n    }\n    framerate() {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n    setSurface(surface) {\n      // If we have the surface, we can use the WebGL buffer which is slightly faster\n      // This is because WebGL cannot be shared across contextes.\n      // This can be removed with WebGPU\n      this.webglBuffer = new _CanvasKitWebGLBufferImpl.CanvasKitWebGLBufferImpl(surface, this.videoElement);\n    }\n    nextImage() {\n      return this.ImageFactory.MakeImageFromNativeBuffer(this.webglBuffer ? this.webglBuffer : this.videoElement);\n    }\n    seek(time) {\n      if (isNaN(time)) {\n        throw new Error(`Invalid time: ${time}`);\n      }\n      this.videoElement.currentTime = time / 1000;\n    }\n    rotation() {\n      return 0;\n    }\n    size() {\n      return {\n        width: this.videoElement.videoWidth,\n        height: this.videoElement.videoHeight\n      };\n    }\n    pause() {\n      this.videoElement.pause();\n    }\n    play() {\n      this.videoElement.play();\n    }\n    setVolume(volume) {\n      this.videoElement.volume = volume;\n    }\n    dispose() {\n      if (this.videoElement.parentNode) {\n        this.videoElement.parentNode.removeChild(this.videoElement);\n      }\n    }\n  }\n  exports.JsiVideo = JsiVideo;\n});", "lineCount": 102, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_CanvasKitWebGLBufferImpl"], [6, 31, 4, 0], [6, 34, 4, 0, "require"], [6, 41, 4, 0], [6, 42, 4, 0, "_dependencyMap"], [6, 56, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkImageFactory"], [7, 24, 5, 0], [7, 27, 5, 0, "require"], [7, 34, 5, 0], [7, 35, 5, 0, "_dependencyMap"], [7, 49, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_Host"], [8, 11, 6, 0], [8, 14, 6, 0, "require"], [8, 21, 6, 0], [8, 22, 6, 0, "_dependencyMap"], [8, 36, 6, 0], [9, 2, 1, 0], [9, 11, 1, 9, "_defineProperty"], [9, 26, 1, 24, "_defineProperty"], [9, 27, 1, 25, "e"], [9, 28, 1, 26], [9, 30, 1, 28, "r"], [9, 31, 1, 29], [9, 33, 1, 31, "t"], [9, 34, 1, 32], [9, 36, 1, 34], [10, 4, 1, 36], [10, 11, 1, 43], [10, 12, 1, 44, "r"], [10, 13, 1, 45], [10, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 30, 1, 62], [10, 31, 1, 63, "r"], [10, 32, 1, 64], [10, 33, 1, 65], [10, 38, 1, 70, "e"], [10, 39, 1, 71], [10, 42, 1, 74, "Object"], [10, 48, 1, 80], [10, 49, 1, 81, "defineProperty"], [10, 63, 1, 95], [10, 64, 1, 96, "e"], [10, 65, 1, 97], [10, 67, 1, 99, "r"], [10, 68, 1, 100], [10, 70, 1, 102], [11, 6, 1, 104, "value"], [11, 11, 1, 109], [11, 13, 1, 111, "t"], [11, 14, 1, 112], [12, 6, 1, 114, "enumerable"], [12, 16, 1, 124], [12, 18, 1, 126], [12, 19, 1, 127], [12, 20, 1, 128], [13, 6, 1, 130, "configurable"], [13, 18, 1, 142], [13, 20, 1, 144], [13, 21, 1, 145], [13, 22, 1, 146], [14, 6, 1, 148, "writable"], [14, 14, 1, 156], [14, 16, 1, 158], [14, 17, 1, 159], [15, 4, 1, 161], [15, 5, 1, 162], [15, 6, 1, 163], [15, 9, 1, 166, "e"], [15, 10, 1, 167], [15, 11, 1, 168, "r"], [15, 12, 1, 169], [15, 13, 1, 170], [15, 16, 1, 173, "t"], [15, 17, 1, 174], [15, 19, 1, 176, "e"], [15, 20, 1, 177], [16, 2, 1, 179], [17, 2, 2, 0], [17, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [17, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [17, 26, 2, 24, "t"], [17, 27, 2, 25], [17, 29, 2, 27], [18, 4, 2, 29], [18, 8, 2, 33, "i"], [18, 9, 2, 34], [18, 12, 2, 37, "_toPrimitive"], [18, 24, 2, 49], [18, 25, 2, 50, "t"], [18, 26, 2, 51], [18, 28, 2, 53], [18, 36, 2, 61], [18, 37, 2, 62], [19, 4, 2, 64], [19, 11, 2, 71], [19, 19, 2, 79], [19, 23, 2, 83], [19, 30, 2, 90, "i"], [19, 31, 2, 91], [19, 34, 2, 94, "i"], [19, 35, 2, 95], [19, 38, 2, 98, "i"], [19, 39, 2, 99], [19, 42, 2, 102], [19, 44, 2, 104], [20, 2, 2, 106], [21, 2, 3, 0], [21, 11, 3, 9, "_toPrimitive"], [21, 23, 3, 21, "_toPrimitive"], [21, 24, 3, 22, "t"], [21, 25, 3, 23], [21, 27, 3, 25, "r"], [21, 28, 3, 26], [21, 30, 3, 28], [22, 4, 3, 30], [22, 8, 3, 34], [22, 16, 3, 42], [22, 20, 3, 46], [22, 27, 3, 53, "t"], [22, 28, 3, 54], [22, 32, 3, 58], [22, 33, 3, 59, "t"], [22, 34, 3, 60], [22, 36, 3, 62], [22, 43, 3, 69, "t"], [22, 44, 3, 70], [23, 4, 3, 72], [23, 8, 3, 76, "e"], [23, 9, 3, 77], [23, 12, 3, 80, "t"], [23, 13, 3, 81], [23, 14, 3, 82, "Symbol"], [23, 20, 3, 88], [23, 21, 3, 89, "toPrimitive"], [23, 32, 3, 100], [23, 33, 3, 101], [24, 4, 3, 103], [24, 8, 3, 107], [24, 13, 3, 112], [24, 14, 3, 113], [24, 19, 3, 118, "e"], [24, 20, 3, 119], [24, 22, 3, 121], [25, 6, 3, 123], [25, 10, 3, 127, "i"], [25, 11, 3, 128], [25, 14, 3, 131, "e"], [25, 15, 3, 132], [25, 16, 3, 133, "call"], [25, 20, 3, 137], [25, 21, 3, 138, "t"], [25, 22, 3, 139], [25, 24, 3, 141, "r"], [25, 25, 3, 142], [25, 29, 3, 146], [25, 38, 3, 155], [25, 39, 3, 156], [26, 6, 3, 158], [26, 10, 3, 162], [26, 18, 3, 170], [26, 22, 3, 174], [26, 29, 3, 181, "i"], [26, 30, 3, 182], [26, 32, 3, 184], [26, 39, 3, 191, "i"], [26, 40, 3, 192], [27, 6, 3, 194], [27, 12, 3, 200], [27, 16, 3, 204, "TypeError"], [27, 25, 3, 213], [27, 26, 3, 214], [27, 72, 3, 260], [27, 73, 3, 261], [28, 4, 3, 263], [29, 4, 3, 265], [29, 11, 3, 272], [29, 12, 3, 273], [29, 20, 3, 281], [29, 25, 3, 286, "r"], [29, 26, 3, 287], [29, 29, 3, 290, "String"], [29, 35, 3, 296], [29, 38, 3, 299, "Number"], [29, 44, 3, 305], [29, 46, 3, 307, "t"], [29, 47, 3, 308], [29, 48, 3, 309], [30, 2, 3, 311], [31, 2, 7, 7], [31, 8, 7, 13, "createVideo"], [31, 19, 7, 24], [31, 22, 7, 27], [31, 28, 7, 27, "createVideo"], [31, 29, 7, 34, "CanvasKit"], [31, 38, 7, 43], [31, 40, 7, 45, "url"], [31, 43, 7, 48], [31, 48, 7, 53], [32, 4, 8, 2], [32, 10, 8, 8, "video"], [32, 15, 8, 13], [32, 18, 8, 16, "document"], [32, 26, 8, 24], [32, 27, 8, 25, "createElement"], [32, 40, 8, 38], [32, 41, 8, 39], [32, 48, 8, 46], [32, 49, 8, 47], [33, 4, 9, 2], [33, 11, 9, 9], [33, 15, 9, 13, "Promise"], [33, 22, 9, 20], [33, 23, 9, 21], [33, 24, 9, 22, "resolve"], [33, 31, 9, 29], [33, 33, 9, 31, "reject"], [33, 39, 9, 37], [33, 44, 9, 42], [34, 6, 10, 4, "video"], [34, 11, 10, 9], [34, 12, 10, 10, "src"], [34, 15, 10, 13], [34, 18, 10, 16, "url"], [34, 21, 10, 19], [35, 6, 11, 4, "video"], [35, 11, 11, 9], [35, 12, 11, 10, "style"], [35, 17, 11, 15], [35, 18, 11, 16, "display"], [35, 25, 11, 23], [35, 28, 11, 26], [35, 34, 11, 32], [36, 6, 12, 4, "video"], [36, 11, 12, 9], [36, 12, 12, 10, "crossOrigin"], [36, 23, 12, 21], [36, 26, 12, 24], [36, 37, 12, 35], [37, 6, 13, 4, "video"], [37, 11, 13, 9], [37, 12, 13, 10, "volume"], [37, 18, 13, 16], [37, 21, 13, 19], [37, 22, 13, 20], [38, 6, 14, 4, "video"], [38, 11, 14, 9], [38, 12, 14, 10, "addEventListener"], [38, 28, 14, 26], [38, 29, 14, 27], [38, 45, 14, 43], [38, 47, 14, 45], [38, 53, 14, 51], [39, 8, 15, 6, "document"], [39, 16, 15, 14], [39, 17, 15, 15, "body"], [39, 21, 15, 19], [39, 22, 15, 20, "append<PERSON><PERSON><PERSON>"], [39, 33, 15, 31], [39, 34, 15, 32, "video"], [39, 39, 15, 37], [39, 40, 15, 38], [40, 8, 16, 6, "resolve"], [40, 15, 16, 13], [40, 16, 16, 14], [40, 20, 16, 18, "JsiVideo"], [40, 28, 16, 26], [40, 29, 16, 27], [40, 33, 16, 31, "JsiSkImageFactory"], [40, 69, 16, 48], [40, 70, 16, 49, "CanvasKit"], [40, 79, 16, 58], [40, 80, 16, 59], [40, 82, 16, 61, "video"], [40, 87, 16, 66], [40, 88, 16, 67], [40, 89, 16, 68], [41, 6, 17, 4], [41, 7, 17, 5], [41, 8, 17, 6], [42, 6, 18, 4, "video"], [42, 11, 18, 9], [42, 12, 18, 10, "addEventListener"], [42, 28, 18, 26], [42, 29, 18, 27], [42, 36, 18, 34], [42, 38, 18, 36], [42, 44, 18, 42], [43, 8, 19, 6, "reject"], [43, 14, 19, 12], [43, 15, 19, 13], [43, 19, 19, 17, "Error"], [43, 24, 19, 22], [43, 25, 19, 23], [43, 59, 19, 57, "url"], [43, 62, 19, 60], [43, 64, 19, 62], [43, 65, 19, 63], [43, 66, 19, 64], [44, 6, 20, 4], [44, 7, 20, 5], [44, 8, 20, 6], [45, 4, 21, 2], [45, 5, 21, 3], [45, 6, 21, 4], [46, 2, 22, 0], [46, 3, 22, 1], [47, 2, 22, 2, "exports"], [47, 9, 22, 2], [47, 10, 22, 2, "createVideo"], [47, 21, 22, 2], [47, 24, 22, 2, "createVideo"], [47, 35, 22, 2], [48, 2, 23, 7], [48, 8, 23, 13, "JsiVideo"], [48, 16, 23, 21], [48, 17, 23, 22], [49, 4, 24, 2, "constructor"], [49, 15, 24, 13, "constructor"], [49, 16, 24, 14, "ImageFactory"], [49, 28, 24, 26], [49, 30, 24, 28, "videoElement"], [49, 42, 24, 40], [49, 44, 24, 42], [50, 6, 25, 4], [50, 10, 25, 8], [50, 11, 25, 9, "ImageFactory"], [50, 23, 25, 21], [50, 26, 25, 24, "ImageFactory"], [50, 38, 25, 36], [51, 6, 26, 4], [51, 10, 26, 8], [51, 11, 26, 9, "videoElement"], [51, 23, 26, 21], [51, 26, 26, 24, "videoElement"], [51, 38, 26, 36], [52, 6, 27, 4, "_defineProperty"], [52, 21, 27, 19], [52, 22, 27, 20], [52, 26, 27, 24], [52, 28, 27, 26], [52, 42, 27, 40], [52, 44, 27, 42], [52, 51, 27, 49], [52, 52, 27, 50], [53, 6, 28, 4, "_defineProperty"], [53, 21, 28, 19], [53, 22, 28, 20], [53, 26, 28, 24], [53, 28, 28, 26], [53, 41, 28, 39], [53, 43, 28, 41], [53, 47, 28, 45], [53, 48, 28, 46], [54, 6, 29, 4, "document"], [54, 14, 29, 12], [54, 15, 29, 13, "body"], [54, 19, 29, 17], [54, 20, 29, 18, "append<PERSON><PERSON><PERSON>"], [54, 31, 29, 29], [54, 32, 29, 30], [54, 36, 29, 34], [54, 37, 29, 35, "videoElement"], [54, 49, 29, 47], [54, 50, 29, 48], [55, 4, 30, 2], [56, 4, 31, 2, "duration"], [56, 12, 31, 10, "duration"], [56, 13, 31, 10], [56, 15, 31, 13], [57, 6, 32, 4], [57, 13, 32, 11], [57, 17, 32, 15], [57, 18, 32, 16, "videoElement"], [57, 30, 32, 28], [57, 31, 32, 29, "duration"], [57, 39, 32, 37], [57, 42, 32, 40], [57, 46, 32, 44], [58, 4, 33, 2], [59, 4, 34, 2, "framerate"], [59, 13, 34, 11, "framerate"], [59, 14, 34, 11], [59, 16, 34, 14], [60, 6, 35, 4], [60, 13, 35, 11], [60, 17, 35, 11, "throwNotImplementedOnRNWeb"], [60, 49, 35, 37], [60, 51, 35, 38], [60, 52, 35, 39], [61, 4, 36, 2], [62, 4, 37, 2, "setSurface"], [62, 14, 37, 12, "setSurface"], [62, 15, 37, 13, "surface"], [62, 22, 37, 20], [62, 24, 37, 22], [63, 6, 38, 4], [64, 6, 39, 4], [65, 6, 40, 4], [66, 6, 41, 4], [66, 10, 41, 8], [66, 11, 41, 9, "webglBuffer"], [66, 22, 41, 20], [66, 25, 41, 23], [66, 29, 41, 27, "CanvasKitWebGLBufferImpl"], [66, 79, 41, 51], [66, 80, 41, 52, "surface"], [66, 87, 41, 59], [66, 89, 41, 61], [66, 93, 41, 65], [66, 94, 41, 66, "videoElement"], [66, 106, 41, 78], [66, 107, 41, 79], [67, 4, 42, 2], [68, 4, 43, 2, "nextImage"], [68, 13, 43, 11, "nextImage"], [68, 14, 43, 11], [68, 16, 43, 14], [69, 6, 44, 4], [69, 13, 44, 11], [69, 17, 44, 15], [69, 18, 44, 16, "ImageFactory"], [69, 30, 44, 28], [69, 31, 44, 29, "MakeImageFromNativeBuffer"], [69, 56, 44, 54], [69, 57, 44, 55], [69, 61, 44, 59], [69, 62, 44, 60, "webglBuffer"], [69, 73, 44, 71], [69, 76, 44, 74], [69, 80, 44, 78], [69, 81, 44, 79, "webglBuffer"], [69, 92, 44, 90], [69, 95, 44, 93], [69, 99, 44, 97], [69, 100, 44, 98, "videoElement"], [69, 112, 44, 110], [69, 113, 44, 111], [70, 4, 45, 2], [71, 4, 46, 2, "seek"], [71, 8, 46, 6, "seek"], [71, 9, 46, 7, "time"], [71, 13, 46, 11], [71, 15, 46, 13], [72, 6, 47, 4], [72, 10, 47, 8, "isNaN"], [72, 15, 47, 13], [72, 16, 47, 14, "time"], [72, 20, 47, 18], [72, 21, 47, 19], [72, 23, 47, 21], [73, 8, 48, 6], [73, 14, 48, 12], [73, 18, 48, 16, "Error"], [73, 23, 48, 21], [73, 24, 48, 22], [73, 41, 48, 39, "time"], [73, 45, 48, 43], [73, 47, 48, 45], [73, 48, 48, 46], [74, 6, 49, 4], [75, 6, 50, 4], [75, 10, 50, 8], [75, 11, 50, 9, "videoElement"], [75, 23, 50, 21], [75, 24, 50, 22, "currentTime"], [75, 35, 50, 33], [75, 38, 50, 36, "time"], [75, 42, 50, 40], [75, 45, 50, 43], [75, 49, 50, 47], [76, 4, 51, 2], [77, 4, 52, 2, "rotation"], [77, 12, 52, 10, "rotation"], [77, 13, 52, 10], [77, 15, 52, 13], [78, 6, 53, 4], [78, 13, 53, 11], [78, 14, 53, 12], [79, 4, 54, 2], [80, 4, 55, 2, "size"], [80, 8, 55, 6, "size"], [80, 9, 55, 6], [80, 11, 55, 9], [81, 6, 56, 4], [81, 13, 56, 11], [82, 8, 57, 6, "width"], [82, 13, 57, 11], [82, 15, 57, 13], [82, 19, 57, 17], [82, 20, 57, 18, "videoElement"], [82, 32, 57, 30], [82, 33, 57, 31, "videoWidth"], [82, 43, 57, 41], [83, 8, 58, 6, "height"], [83, 14, 58, 12], [83, 16, 58, 14], [83, 20, 58, 18], [83, 21, 58, 19, "videoElement"], [83, 33, 58, 31], [83, 34, 58, 32, "videoHeight"], [84, 6, 59, 4], [84, 7, 59, 5], [85, 4, 60, 2], [86, 4, 61, 2, "pause"], [86, 9, 61, 7, "pause"], [86, 10, 61, 7], [86, 12, 61, 10], [87, 6, 62, 4], [87, 10, 62, 8], [87, 11, 62, 9, "videoElement"], [87, 23, 62, 21], [87, 24, 62, 22, "pause"], [87, 29, 62, 27], [87, 30, 62, 28], [87, 31, 62, 29], [88, 4, 63, 2], [89, 4, 64, 2, "play"], [89, 8, 64, 6, "play"], [89, 9, 64, 6], [89, 11, 64, 9], [90, 6, 65, 4], [90, 10, 65, 8], [90, 11, 65, 9, "videoElement"], [90, 23, 65, 21], [90, 24, 65, 22, "play"], [90, 28, 65, 26], [90, 29, 65, 27], [90, 30, 65, 28], [91, 4, 66, 2], [92, 4, 67, 2, "setVolume"], [92, 13, 67, 11, "setVolume"], [92, 14, 67, 12, "volume"], [92, 20, 67, 18], [92, 22, 67, 20], [93, 6, 68, 4], [93, 10, 68, 8], [93, 11, 68, 9, "videoElement"], [93, 23, 68, 21], [93, 24, 68, 22, "volume"], [93, 30, 68, 28], [93, 33, 68, 31, "volume"], [93, 39, 68, 37], [94, 4, 69, 2], [95, 4, 70, 2, "dispose"], [95, 11, 70, 9, "dispose"], [95, 12, 70, 9], [95, 14, 70, 12], [96, 6, 71, 4], [96, 10, 71, 8], [96, 14, 71, 12], [96, 15, 71, 13, "videoElement"], [96, 27, 71, 25], [96, 28, 71, 26, "parentNode"], [96, 38, 71, 36], [96, 40, 71, 38], [97, 8, 72, 6], [97, 12, 72, 10], [97, 13, 72, 11, "videoElement"], [97, 25, 72, 23], [97, 26, 72, 24, "parentNode"], [97, 36, 72, 34], [97, 37, 72, 35, "<PERSON><PERSON><PERSON><PERSON>"], [97, 48, 72, 46], [97, 49, 72, 47], [97, 53, 72, 51], [97, 54, 72, 52, "videoElement"], [97, 66, 72, 64], [97, 67, 72, 65], [98, 6, 73, 4], [99, 4, 74, 2], [100, 2, 75, 0], [101, 2, 75, 1, "exports"], [101, 9, 75, 1], [101, 10, 75, 1, "JsiVideo"], [101, 18, 75, 1], [101, 21, 75, 1, "JsiVideo"], [101, 29, 75, 1], [102, 0, 75, 1], [102, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "createVideo", "Promise$argument_0", "video.addEventListener$argument_1", "JsiVideo", "constructor", "duration", "framerate", "setSurface", "nextImage", "seek", "rotation", "size", "pause", "play", "setVolume", "dispose"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;2BGI;qBCE;6CCK;KDG;oCCC;KDE;GDC;CHC;OMC;ECC;GDM;EEC;GFE;EGC;GHE;EIC;GJK;EKC;GLE;EMC;GNK;EOC;GPE;EQC;GRK;ESC;GTE;EUC;GVE;EWC;GXE;EYC;GZI;CNC"}}, "type": "js/module"}]}