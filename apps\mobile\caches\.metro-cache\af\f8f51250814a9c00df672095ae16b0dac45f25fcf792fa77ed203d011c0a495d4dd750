{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   *\n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation matches the working solution from test-blazeface-integration.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height,\n    onReady\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        return;\n      }\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size to match video dimensions when available\n      const updateCanvasSize = () => {\n        if (video.videoWidth && video.videoHeight) {\n          canvas.width = video.videoWidth;\n          canvas.height = video.videoHeight;\n          canvas.style.width = '100%';\n          canvas.style.height = '100%';\n          console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);\n        } else {\n          // Fallback to provided dimensions\n          canvas.width = width;\n          canvas.height = height;\n          console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);\n        }\n      };\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Helper function to load scripts\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n\n      // Load TensorFlow.js and BlazeFace model - matching working test implementation\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await window.blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Update canvas size once video is ready\n          updateCanvasSize();\n\n          // Notify parent that BlazeFace is ready\n          if (onReady) {\n            onReady();\n          }\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Check if video has valid dimensions\n          if (!video.videoWidth || !video.videoHeight) {\n            rafRef.current = requestAnimationFrame(detectLoop);\n            return;\n          }\n\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces with same confidence threshold as working test\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas - DON'T draw the original video, just clear for overlay\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            setFaceCount(predictions.length);\n\n            // Process each detected face - OVERLAY BLURRED PATCHES like in working test\n            predictions.forEach((prediction, index) => {\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect) - CRITICAL for front camera\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply OVERLAY elliptical blur patch - like working test\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(20px)'; // Match working test blur intensity\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n\n              // Add debug rectangle to show detection area (optional)\n              if (__DEV__) {\n                ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n                ctx.lineWidth = 2;\n                ctx.strokeRect(minX, minY, faceWidth, faceHeight);\n              }\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Wait for video to be ready before starting\n      const waitForVideoAndStart = () => {\n        if (video.readyState >= 2) {\n          // HAVE_CURRENT_DATA\n          loadModel();\n        } else {\n          video.addEventListener('loadeddata', loadModel, {\n            once: true\n          });\n        }\n      };\n\n      // Start the process\n      waitForVideoAndStart();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 15,\n          // Higher z-index to be above video\n          objectFit: 'cover',\n          backgroundColor: 'transparent'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(59, 130, 246, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(59, 130, 246, 0.3)'\n        },\n        children: \"Loading BlazeFace model...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(16, 185, 129, 0.3)'\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), !isLoading && faceCount === 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(107, 114, 128, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(107, 114, 128, 0.3)'\n        },\n        children: \"\\uD83D\\uDC40 Looking for faces...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 314, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 2, 17, 15], [18, 11, 17, 24, "BlazeFaceCanvas"], [18, 26, 17, 39, "BlazeFaceCanvas"], [18, 27, 17, 40], [19, 4, 17, 42, "containerId"], [19, 15, 17, 53], [20, 4, 17, 55, "width"], [20, 9, 17, 60], [21, 4, 17, 62, "height"], [21, 10, 17, 68], [22, 4, 17, 70, "onReady"], [23, 2, 17, 100], [23, 3, 17, 101], [23, 5, 17, 103], [24, 4, 17, 103, "_s"], [24, 6, 17, 103], [25, 4, 18, 2], [25, 10, 18, 8, "canvasRef"], [25, 19, 18, 17], [25, 22, 18, 20], [25, 26, 18, 20, "useRef"], [25, 39, 18, 26], [25, 41, 18, 53], [25, 45, 18, 57], [25, 46, 18, 58], [26, 4, 19, 2], [26, 10, 19, 8, "rafRef"], [26, 16, 19, 14], [26, 19, 19, 17], [26, 23, 19, 17, "useRef"], [26, 36, 19, 23], [26, 38, 19, 39], [26, 42, 19, 43], [26, 43, 19, 44], [27, 4, 20, 2], [27, 10, 20, 8, "modelRef"], [27, 18, 20, 16], [27, 21, 20, 19], [27, 25, 20, 19, "useRef"], [27, 38, 20, 25], [27, 40, 20, 38], [27, 44, 20, 42], [27, 45, 20, 43], [28, 4, 21, 2], [28, 10, 21, 8], [28, 11, 21, 9, "isLoading"], [28, 20, 21, 18], [28, 22, 21, 20, "setIsLoading"], [28, 34, 21, 32], [28, 35, 21, 33], [28, 38, 21, 36], [28, 42, 21, 36, "useState"], [28, 57, 21, 44], [28, 59, 21, 45], [28, 63, 21, 49], [28, 64, 21, 50], [29, 4, 22, 2], [29, 10, 22, 8], [29, 11, 22, 9, "faceCount"], [29, 20, 22, 18], [29, 22, 22, 20, "setFaceCount"], [29, 34, 22, 32], [29, 35, 22, 33], [29, 38, 22, 36], [29, 42, 22, 36, "useState"], [29, 57, 22, 44], [29, 59, 22, 45], [29, 60, 22, 46], [29, 61, 22, 47], [30, 4, 24, 2], [30, 8, 24, 2, "useEffect"], [30, 24, 24, 11], [30, 26, 24, 12], [30, 32, 24, 18], [31, 6, 25, 4, "console"], [31, 13, 25, 11], [31, 14, 25, 12, "log"], [31, 17, 25, 15], [31, 18, 25, 16], [31, 64, 25, 62], [31, 66, 25, 64], [32, 8, 25, 66, "containerId"], [32, 19, 25, 77], [33, 8, 25, 79, "width"], [33, 13, 25, 84], [34, 8, 25, 86, "height"], [35, 6, 25, 93], [35, 7, 25, 94], [35, 8, 25, 95], [36, 6, 27, 4], [36, 12, 27, 10, "container"], [36, 21, 27, 19], [36, 24, 27, 22, "document"], [36, 32, 27, 30], [36, 33, 27, 31, "getElementById"], [36, 47, 27, 45], [36, 48, 27, 46, "containerId"], [36, 59, 27, 57], [36, 60, 27, 58], [37, 6, 28, 4], [37, 10, 28, 8], [37, 11, 28, 9, "container"], [37, 20, 28, 18], [37, 22, 28, 20], [38, 8, 29, 6, "console"], [38, 15, 29, 13], [38, 16, 29, 14, "error"], [38, 21, 29, 19], [38, 22, 29, 20], [38, 62, 29, 60], [38, 64, 29, 62, "containerId"], [38, 75, 29, 73], [38, 76, 29, 74], [39, 8, 30, 6], [40, 6, 31, 4], [41, 6, 33, 4], [41, 12, 33, 10, "video"], [41, 17, 33, 40], [41, 20, 33, 43, "container"], [41, 29, 33, 52], [41, 30, 33, 53, "querySelector"], [41, 43, 33, 66], [41, 44, 33, 67], [41, 51, 33, 74], [41, 52, 33, 75], [42, 6, 34, 4], [42, 10, 34, 8], [42, 11, 34, 9, "video"], [42, 16, 34, 14], [42, 18, 34, 16], [43, 8, 35, 6, "console"], [43, 15, 35, 13], [43, 16, 35, 14, "error"], [43, 21, 35, 19], [43, 22, 35, 20], [43, 78, 35, 76], [43, 79, 35, 77], [44, 8, 36, 6], [45, 6, 37, 4], [46, 6, 39, 4], [46, 12, 39, 10, "canvas"], [46, 18, 39, 16], [46, 21, 39, 19, "canvasRef"], [46, 30, 39, 28], [46, 31, 39, 29, "current"], [46, 38, 39, 36], [47, 6, 40, 4], [47, 10, 40, 8], [47, 11, 40, 9, "canvas"], [47, 17, 40, 15], [47, 19, 40, 17], [48, 8, 41, 6, "console"], [48, 15, 41, 13], [48, 16, 41, 14, "error"], [48, 21, 41, 19], [48, 22, 41, 20], [48, 66, 41, 64], [48, 67, 41, 65], [49, 8, 42, 6], [50, 6, 43, 4], [52, 6, 45, 4], [53, 6, 46, 4], [53, 12, 46, 10, "updateCanvasSize"], [53, 28, 46, 26], [53, 31, 46, 29, "updateCanvasSize"], [53, 32, 46, 29], [53, 37, 46, 35], [54, 8, 47, 6], [54, 12, 47, 10, "video"], [54, 17, 47, 15], [54, 18, 47, 16, "videoWidth"], [54, 28, 47, 26], [54, 32, 47, 30, "video"], [54, 37, 47, 35], [54, 38, 47, 36, "videoHeight"], [54, 49, 47, 47], [54, 51, 47, 49], [55, 10, 48, 8, "canvas"], [55, 16, 48, 14], [55, 17, 48, 15, "width"], [55, 22, 48, 20], [55, 25, 48, 23, "video"], [55, 30, 48, 28], [55, 31, 48, 29, "videoWidth"], [55, 41, 48, 39], [56, 10, 49, 8, "canvas"], [56, 16, 49, 14], [56, 17, 49, 15, "height"], [56, 23, 49, 21], [56, 26, 49, 24, "video"], [56, 31, 49, 29], [56, 32, 49, 30, "videoHeight"], [56, 43, 49, 41], [57, 10, 50, 8, "canvas"], [57, 16, 50, 14], [57, 17, 50, 15, "style"], [57, 22, 50, 20], [57, 23, 50, 21, "width"], [57, 28, 50, 26], [57, 31, 50, 29], [57, 37, 50, 35], [58, 10, 51, 8, "canvas"], [58, 16, 51, 14], [58, 17, 51, 15, "style"], [58, 22, 51, 20], [58, 23, 51, 21, "height"], [58, 29, 51, 27], [58, 32, 51, 30], [58, 38, 51, 36], [59, 10, 52, 8, "console"], [59, 17, 52, 15], [59, 18, 52, 16, "log"], [59, 21, 52, 19], [59, 22, 52, 20], [59, 72, 52, 70], [59, 74, 52, 72, "video"], [59, 79, 52, 77], [59, 80, 52, 78, "videoWidth"], [59, 90, 52, 88], [59, 92, 52, 90], [59, 95, 52, 93], [59, 97, 52, 95, "video"], [59, 102, 52, 100], [59, 103, 52, 101, "videoHeight"], [59, 114, 52, 112], [59, 115, 52, 113], [60, 8, 53, 6], [60, 9, 53, 7], [60, 15, 53, 13], [61, 10, 54, 8], [62, 10, 55, 8, "canvas"], [62, 16, 55, 14], [62, 17, 55, 15, "width"], [62, 22, 55, 20], [62, 25, 55, 23, "width"], [62, 30, 55, 28], [63, 10, 56, 8, "canvas"], [63, 16, 56, 14], [63, 17, 56, 15, "height"], [63, 23, 56, 21], [63, 26, 56, 24, "height"], [63, 32, 56, 30], [64, 10, 57, 8, "console"], [64, 17, 57, 15], [64, 18, 57, 16, "log"], [64, 21, 57, 19], [64, 22, 57, 20], [64, 80, 57, 78], [64, 82, 57, 80, "width"], [64, 87, 57, 85], [64, 89, 57, 87], [64, 92, 57, 90], [64, 94, 57, 92, "height"], [64, 100, 57, 98], [64, 101, 57, 99], [65, 8, 58, 6], [66, 6, 59, 4], [66, 7, 59, 5], [67, 6, 61, 4], [67, 12, 61, 10, "ctx"], [67, 15, 61, 13], [67, 18, 61, 16, "canvas"], [67, 24, 61, 22], [67, 25, 61, 23, "getContext"], [67, 35, 61, 33], [67, 36, 61, 34], [67, 40, 61, 38], [67, 41, 61, 39], [68, 6, 62, 4], [68, 10, 62, 8], [68, 11, 62, 9, "ctx"], [68, 14, 62, 12], [68, 16, 62, 14], [69, 8, 63, 6, "console"], [69, 15, 63, 13], [69, 16, 63, 14, "error"], [69, 21, 63, 19], [69, 22, 63, 20], [69, 70, 63, 68], [69, 71, 63, 69], [70, 8, 64, 6], [71, 6, 65, 4], [72, 6, 67, 4], [72, 10, 67, 8, "isDetecting"], [72, 21, 67, 19], [72, 24, 67, 22], [72, 28, 67, 26], [74, 6, 69, 4], [75, 6, 70, 4], [75, 12, 70, 10, "loadScript"], [75, 22, 70, 20], [75, 25, 70, 24, "src"], [75, 28, 70, 35], [75, 32, 70, 55], [76, 8, 71, 6], [76, 15, 71, 13], [76, 19, 71, 17, "Promise"], [76, 26, 71, 24], [76, 27, 71, 25], [76, 28, 71, 26, "resolve"], [76, 35, 71, 33], [76, 37, 71, 35, "reject"], [76, 43, 71, 41], [76, 48, 71, 46], [77, 10, 72, 8], [77, 16, 72, 14, "script"], [77, 22, 72, 20], [77, 25, 72, 23, "document"], [77, 33, 72, 31], [77, 34, 72, 32, "createElement"], [77, 47, 72, 45], [77, 48, 72, 46], [77, 56, 72, 54], [77, 57, 72, 55], [78, 10, 73, 8, "script"], [78, 16, 73, 14], [78, 17, 73, 15, "src"], [78, 20, 73, 18], [78, 23, 73, 21, "src"], [78, 26, 73, 24], [79, 10, 74, 8, "script"], [79, 16, 74, 14], [79, 17, 74, 15, "onload"], [79, 23, 74, 21], [79, 26, 74, 24], [79, 32, 74, 30, "resolve"], [79, 39, 74, 37], [79, 40, 74, 38], [79, 41, 74, 39], [80, 10, 75, 8, "script"], [80, 16, 75, 14], [80, 17, 75, 15, "onerror"], [80, 24, 75, 22], [80, 27, 75, 25], [80, 33, 75, 31, "reject"], [80, 39, 75, 37], [80, 40, 75, 38], [80, 44, 75, 42, "Error"], [80, 49, 75, 47], [80, 50, 75, 48], [80, 68, 75, 66, "src"], [80, 71, 75, 69], [80, 73, 75, 71], [80, 74, 75, 72], [80, 75, 75, 73], [81, 10, 76, 8, "document"], [81, 18, 76, 16], [81, 19, 76, 17, "head"], [81, 23, 76, 21], [81, 24, 76, 22, "append<PERSON><PERSON><PERSON>"], [81, 35, 76, 33], [81, 36, 76, 34, "script"], [81, 42, 76, 40], [81, 43, 76, 41], [82, 8, 77, 6], [82, 9, 77, 7], [82, 10, 77, 8], [83, 6, 78, 4], [83, 7, 78, 5], [85, 6, 80, 4], [86, 6, 81, 4], [86, 12, 81, 10, "loadModel"], [86, 21, 81, 19], [86, 24, 81, 22], [86, 30, 81, 22, "loadModel"], [86, 31, 81, 22], [86, 36, 81, 34], [87, 8, 82, 6], [87, 12, 82, 10], [88, 10, 83, 8, "console"], [88, 17, 83, 15], [88, 18, 83, 16, "log"], [88, 21, 83, 19], [88, 22, 83, 20], [88, 66, 83, 64], [88, 67, 83, 65], [90, 10, 85, 8], [91, 10, 86, 8], [91, 14, 86, 12], [91, 15, 86, 14, "window"], [91, 21, 86, 20], [91, 22, 86, 29, "tf"], [91, 24, 86, 31], [91, 26, 86, 33], [92, 12, 87, 10], [92, 18, 87, 16, "loadScript"], [92, 28, 87, 26], [92, 29, 87, 27], [92, 98, 87, 96], [92, 99, 87, 97], [93, 10, 88, 8], [94, 10, 90, 8, "console"], [94, 17, 90, 15], [94, 18, 90, 16, "log"], [94, 21, 90, 19], [94, 22, 90, 20], [94, 68, 90, 66], [94, 69, 90, 67], [96, 10, 92, 8], [97, 10, 93, 8], [97, 14, 93, 12], [97, 15, 93, 14, "window"], [97, 21, 93, 20], [97, 22, 93, 29, "blazeface"], [97, 31, 93, 38], [97, 33, 93, 40], [98, 12, 94, 10], [98, 18, 94, 16, "loadScript"], [98, 28, 94, 26], [98, 29, 94, 27], [98, 112, 94, 110], [98, 113, 94, 111], [99, 10, 95, 8], [101, 10, 97, 8], [102, 10, 98, 8, "console"], [102, 17, 98, 15], [102, 18, 98, 16, "log"], [102, 21, 98, 19], [102, 22, 98, 20], [102, 73, 98, 71], [102, 74, 98, 72], [103, 10, 99, 8, "modelRef"], [103, 18, 99, 16], [103, 19, 99, 17, "current"], [103, 26, 99, 24], [103, 29, 99, 27], [103, 35, 99, 34, "window"], [103, 41, 99, 40], [103, 42, 99, 49, "blazeface"], [103, 51, 99, 58], [103, 52, 99, 59, "load"], [103, 56, 99, 63], [103, 57, 99, 64], [103, 58, 99, 65], [104, 10, 100, 8, "console"], [104, 17, 100, 15], [104, 18, 100, 16, "log"], [104, 21, 100, 19], [104, 22, 100, 20], [104, 79, 100, 77], [104, 80, 100, 78], [105, 10, 102, 8, "setIsLoading"], [105, 22, 102, 20], [105, 23, 102, 21], [105, 28, 102, 26], [105, 29, 102, 27], [107, 10, 104, 8], [108, 10, 105, 8, "updateCanvasSize"], [108, 26, 105, 24], [108, 27, 105, 25], [108, 28, 105, 26], [110, 10, 107, 8], [111, 10, 108, 8], [111, 14, 108, 12, "onReady"], [111, 21, 108, 19], [111, 23, 108, 21], [112, 12, 109, 10, "onReady"], [112, 19, 109, 17], [112, 20, 109, 18], [112, 21, 109, 19], [113, 10, 110, 8], [115, 10, 112, 8], [116, 10, 113, 8, "detectLoop"], [116, 20, 113, 18], [116, 21, 113, 19], [116, 22, 113, 20], [117, 8, 115, 6], [117, 9, 115, 7], [117, 10, 115, 8], [117, 17, 115, 15, "error"], [117, 22, 115, 20], [117, 24, 115, 22], [118, 10, 116, 8, "console"], [118, 17, 116, 15], [118, 18, 116, 16, "error"], [118, 23, 116, 21], [118, 24, 116, 22], [118, 67, 116, 65], [118, 69, 116, 67, "error"], [118, 74, 116, 72], [118, 75, 116, 73], [119, 10, 117, 8, "setIsLoading"], [119, 22, 117, 20], [119, 23, 117, 21], [119, 28, 117, 26], [119, 29, 117, 27], [120, 8, 118, 6], [121, 6, 119, 4], [121, 7, 119, 5], [122, 6, 121, 4], [122, 12, 121, 10, "detectLoop"], [122, 22, 121, 20], [122, 25, 121, 23], [122, 31, 121, 23, "detectLoop"], [122, 32, 121, 23], [122, 37, 121, 35], [123, 8, 122, 6], [123, 12, 122, 10], [123, 13, 122, 11, "isDetecting"], [123, 24, 122, 22], [123, 28, 122, 26], [123, 29, 122, 27, "modelRef"], [123, 37, 122, 35], [123, 38, 122, 36, "current"], [123, 45, 122, 43], [123, 47, 122, 45], [124, 8, 124, 6], [124, 12, 124, 10], [125, 10, 125, 8], [126, 10, 126, 8], [126, 14, 126, 12], [126, 15, 126, 13, "video"], [126, 20, 126, 18], [126, 21, 126, 19, "videoWidth"], [126, 31, 126, 29], [126, 35, 126, 33], [126, 36, 126, 34, "video"], [126, 41, 126, 39], [126, 42, 126, 40, "videoHeight"], [126, 53, 126, 51], [126, 55, 126, 53], [127, 12, 127, 10, "rafRef"], [127, 18, 127, 16], [127, 19, 127, 17, "current"], [127, 26, 127, 24], [127, 29, 127, 27, "requestAnimationFrame"], [127, 50, 127, 48], [127, 51, 127, 49, "detectLoop"], [127, 61, 127, 59], [127, 62, 127, 60], [128, 12, 128, 10], [129, 10, 129, 8], [131, 10, 131, 8], [132, 10, 132, 8], [132, 16, 132, 14, "tf"], [132, 18, 132, 16], [132, 21, 132, 20, "window"], [132, 27, 132, 26], [132, 28, 132, 35, "tf"], [132, 30, 132, 37], [133, 10, 133, 8], [133, 16, 133, 14, "tensor"], [133, 22, 133, 20], [133, 25, 133, 23, "tf"], [133, 27, 133, 25], [133, 28, 133, 26, "browser"], [133, 35, 133, 33], [133, 36, 133, 34, "fromPixels"], [133, 46, 133, 44], [133, 47, 133, 45, "video"], [133, 52, 133, 50], [133, 53, 133, 51], [135, 10, 135, 8], [136, 10, 136, 8], [136, 16, 136, 14, "predictions"], [136, 27, 136, 25], [136, 30, 136, 28], [136, 36, 136, 34, "modelRef"], [136, 44, 136, 42], [136, 45, 136, 43, "current"], [136, 52, 136, 50], [136, 53, 136, 51, "estimateFaces"], [136, 66, 136, 64], [136, 67, 136, 65, "tensor"], [136, 73, 136, 71], [136, 75, 136, 73], [136, 80, 136, 78], [136, 82, 136, 80], [136, 85, 136, 83], [136, 86, 136, 84], [137, 10, 137, 8, "tensor"], [137, 16, 137, 14], [137, 17, 137, 15, "dispose"], [137, 24, 137, 22], [137, 25, 137, 23], [137, 26, 137, 24], [139, 10, 139, 8], [140, 10, 140, 8, "ctx"], [140, 13, 140, 11], [140, 14, 140, 12, "clearRect"], [140, 23, 140, 21], [140, 24, 140, 22], [140, 25, 140, 23], [140, 27, 140, 25], [140, 28, 140, 26], [140, 30, 140, 28, "canvas"], [140, 36, 140, 34], [140, 37, 140, 35, "width"], [140, 42, 140, 40], [140, 44, 140, 42, "canvas"], [140, 50, 140, 48], [140, 51, 140, 49, "height"], [140, 57, 140, 55], [140, 58, 140, 56], [141, 10, 142, 8], [141, 14, 142, 12, "predictions"], [141, 25, 142, 23], [141, 26, 142, 24, "length"], [141, 32, 142, 30], [141, 35, 142, 33], [141, 36, 142, 34], [141, 38, 142, 36], [142, 12, 143, 10, "setFaceCount"], [142, 24, 143, 22], [142, 25, 143, 23, "predictions"], [142, 36, 143, 34], [142, 37, 143, 35, "length"], [142, 43, 143, 41], [142, 44, 143, 42], [144, 12, 145, 10], [145, 12, 146, 10, "predictions"], [145, 23, 146, 21], [145, 24, 146, 22, "for<PERSON>ach"], [145, 31, 146, 29], [145, 32, 146, 30], [145, 33, 146, 31, "prediction"], [145, 43, 146, 46], [145, 45, 146, 48, "index"], [145, 50, 146, 61], [145, 55, 146, 66], [146, 14, 147, 12], [146, 20, 147, 18], [146, 21, 147, 19, "x1"], [146, 23, 147, 21], [146, 25, 147, 23, "y1"], [146, 27, 147, 25], [146, 28, 147, 26], [146, 31, 147, 29, "prediction"], [146, 41, 147, 39], [146, 42, 147, 40, "topLeft"], [146, 49, 147, 47], [147, 14, 148, 12], [147, 20, 148, 18], [147, 21, 148, 19, "x2"], [147, 23, 148, 21], [147, 25, 148, 23, "y2"], [147, 27, 148, 25], [147, 28, 148, 26], [147, 31, 148, 29, "prediction"], [147, 41, 148, 39], [147, 42, 148, 40, "bottomRight"], [147, 53, 148, 51], [149, 14, 150, 12], [150, 14, 151, 12], [150, 18, 151, 16, "minX"], [150, 22, 151, 20], [150, 25, 151, 23, "Math"], [150, 29, 151, 27], [150, 30, 151, 28, "min"], [150, 33, 151, 31], [150, 34, 151, 32, "x1"], [150, 36, 151, 34], [150, 38, 151, 36, "x2"], [150, 40, 151, 38], [150, 41, 151, 39], [151, 14, 152, 12], [151, 18, 152, 16, "maxX"], [151, 22, 152, 20], [151, 25, 152, 23, "Math"], [151, 29, 152, 27], [151, 30, 152, 28, "max"], [151, 33, 152, 31], [151, 34, 152, 32, "x1"], [151, 36, 152, 34], [151, 38, 152, 36, "x2"], [151, 40, 152, 38], [151, 41, 152, 39], [152, 14, 153, 12], [152, 20, 153, 18, "minY"], [152, 24, 153, 22], [152, 27, 153, 25, "Math"], [152, 31, 153, 29], [152, 32, 153, 30, "min"], [152, 35, 153, 33], [152, 36, 153, 34, "y1"], [152, 38, 153, 36], [152, 40, 153, 38, "y2"], [152, 42, 153, 40], [152, 43, 153, 41], [153, 14, 154, 12], [153, 20, 154, 18, "maxY"], [153, 24, 154, 22], [153, 27, 154, 25, "Math"], [153, 31, 154, 29], [153, 32, 154, 30, "max"], [153, 35, 154, 33], [153, 36, 154, 34, "y1"], [153, 38, 154, 36], [153, 40, 154, 38, "y2"], [153, 42, 154, 40], [153, 43, 154, 41], [155, 14, 156, 12], [156, 14, 157, 12], [156, 20, 157, 18, "canvasWidth"], [156, 31, 157, 29], [156, 34, 157, 32, "canvas"], [156, 40, 157, 38], [156, 41, 157, 39, "width"], [156, 46, 157, 44], [157, 14, 158, 12], [157, 20, 158, 18, "flippedMinX"], [157, 31, 158, 29], [157, 34, 158, 32, "canvasWidth"], [157, 45, 158, 43], [157, 48, 158, 46, "maxX"], [157, 52, 158, 50], [158, 14, 159, 12], [158, 20, 159, 18, "flippedMaxX"], [158, 31, 159, 29], [158, 34, 159, 32, "canvasWidth"], [158, 45, 159, 43], [158, 48, 159, 46, "minX"], [158, 52, 159, 50], [159, 14, 160, 12, "minX"], [159, 18, 160, 16], [159, 21, 160, 19, "flippedMinX"], [159, 32, 160, 30], [160, 14, 161, 12, "maxX"], [160, 18, 161, 16], [160, 21, 161, 19, "flippedMaxX"], [160, 32, 161, 30], [162, 14, 163, 12], [163, 14, 164, 12], [163, 20, 164, 18, "faceWidth"], [163, 29, 164, 27], [163, 32, 164, 30, "maxX"], [163, 36, 164, 34], [163, 39, 164, 37, "minX"], [163, 43, 164, 41], [164, 14, 165, 12], [164, 20, 165, 18, "faceHeight"], [164, 30, 165, 28], [164, 33, 165, 31, "maxY"], [164, 37, 165, 35], [164, 40, 165, 38, "minY"], [164, 44, 165, 42], [165, 14, 167, 12], [165, 18, 167, 16, "faceWidth"], [165, 27, 167, 25], [165, 31, 167, 29], [165, 32, 167, 30], [165, 36, 167, 34, "faceHeight"], [165, 46, 167, 44], [165, 50, 167, 48], [165, 51, 167, 49], [165, 53, 167, 51], [166, 16, 168, 14], [167, 14, 169, 12], [169, 14, 171, 12], [170, 14, 172, 12], [170, 20, 172, 18, "centerX"], [170, 27, 172, 25], [170, 30, 172, 28], [170, 31, 172, 29, "minX"], [170, 35, 172, 33], [170, 38, 172, 36, "maxX"], [170, 42, 172, 40], [170, 46, 172, 44], [170, 47, 172, 45], [171, 14, 173, 12], [171, 20, 173, 18, "centerY"], [171, 27, 173, 25], [171, 30, 173, 28], [171, 31, 173, 29, "minY"], [171, 35, 173, 33], [171, 38, 173, 36, "maxY"], [171, 42, 173, 40], [171, 46, 173, 44], [171, 47, 173, 45], [172, 14, 174, 12], [172, 20, 174, 18, "expandedWidth"], [172, 33, 174, 31], [172, 36, 174, 34, "faceWidth"], [172, 45, 174, 43], [172, 48, 174, 46], [172, 51, 174, 49], [173, 14, 175, 12], [173, 20, 175, 18, "expandedHeight"], [173, 34, 175, 32], [173, 37, 175, 35, "faceHeight"], [173, 47, 175, 45], [173, 50, 175, 48], [173, 53, 175, 51], [175, 14, 177, 12], [176, 14, 178, 12], [176, 20, 178, 18, "radiusX"], [176, 27, 178, 25], [176, 30, 178, 28, "Math"], [176, 34, 178, 32], [176, 35, 178, 33, "max"], [176, 38, 178, 36], [176, 39, 178, 37, "expandedWidth"], [176, 52, 178, 50], [176, 55, 178, 53], [176, 56, 178, 54], [176, 58, 178, 56], [176, 60, 178, 58], [176, 61, 178, 59], [177, 14, 179, 12], [177, 20, 179, 18, "radiusY"], [177, 27, 179, 25], [177, 30, 179, 28, "Math"], [177, 34, 179, 32], [177, 35, 179, 33, "max"], [177, 38, 179, 36], [177, 39, 179, 37, "expandedHeight"], [177, 53, 179, 51], [177, 56, 179, 54], [177, 57, 179, 55], [177, 59, 179, 57], [177, 61, 179, 59], [177, 62, 179, 60], [179, 14, 181, 12], [180, 14, 182, 12, "ctx"], [180, 17, 182, 15], [180, 18, 182, 16, "save"], [180, 22, 182, 20], [180, 23, 182, 21], [180, 24, 182, 22], [181, 14, 183, 12, "ctx"], [181, 17, 183, 15], [181, 18, 183, 16, "beginPath"], [181, 27, 183, 25], [181, 28, 183, 26], [181, 29, 183, 27], [182, 14, 184, 12, "ctx"], [182, 17, 184, 15], [182, 18, 184, 16, "ellipse"], [182, 25, 184, 23], [182, 26, 184, 24, "centerX"], [182, 33, 184, 31], [182, 35, 184, 33, "centerY"], [182, 42, 184, 40], [182, 44, 184, 42, "radiusX"], [182, 51, 184, 49], [182, 53, 184, 51, "radiusY"], [182, 60, 184, 58], [182, 62, 184, 60], [182, 63, 184, 61], [182, 65, 184, 63], [182, 66, 184, 64], [182, 68, 184, 66, "Math"], [182, 72, 184, 70], [182, 73, 184, 71, "PI"], [182, 75, 184, 73], [182, 78, 184, 76], [182, 79, 184, 77], [182, 80, 184, 78], [183, 14, 185, 12, "ctx"], [183, 17, 185, 15], [183, 18, 185, 16, "clip"], [183, 22, 185, 20], [183, 23, 185, 21], [183, 24, 185, 22], [184, 14, 186, 12, "ctx"], [184, 17, 186, 15], [184, 18, 186, 16, "filter"], [184, 24, 186, 22], [184, 27, 186, 25], [184, 39, 186, 37], [184, 40, 186, 38], [184, 41, 186, 39], [185, 14, 187, 12, "ctx"], [185, 17, 187, 15], [185, 18, 187, 16, "drawImage"], [185, 27, 187, 25], [185, 28, 187, 26, "video"], [185, 33, 187, 31], [185, 35, 187, 33], [185, 36, 187, 34], [185, 38, 187, 36], [185, 39, 187, 37], [185, 41, 187, 39, "canvas"], [185, 47, 187, 45], [185, 48, 187, 46, "width"], [185, 53, 187, 51], [185, 55, 187, 53, "canvas"], [185, 61, 187, 59], [185, 62, 187, 60, "height"], [185, 68, 187, 66], [185, 69, 187, 67], [186, 14, 188, 12, "ctx"], [186, 17, 188, 15], [186, 18, 188, 16, "restore"], [186, 25, 188, 23], [186, 26, 188, 24], [186, 27, 188, 25], [188, 14, 190, 12], [189, 14, 191, 12], [189, 18, 191, 16, "__DEV__"], [189, 25, 191, 23], [189, 27, 191, 25], [190, 16, 192, 14, "ctx"], [190, 19, 192, 17], [190, 20, 192, 18, "strokeStyle"], [190, 31, 192, 29], [190, 34, 192, 32], [190, 56, 192, 54], [191, 16, 193, 14, "ctx"], [191, 19, 193, 17], [191, 20, 193, 18, "lineWidth"], [191, 29, 193, 27], [191, 32, 193, 30], [191, 33, 193, 31], [192, 16, 194, 14, "ctx"], [192, 19, 194, 17], [192, 20, 194, 18, "strokeRect"], [192, 30, 194, 28], [192, 31, 194, 29, "minX"], [192, 35, 194, 33], [192, 37, 194, 35, "minY"], [192, 41, 194, 39], [192, 43, 194, 41, "faceWidth"], [192, 52, 194, 50], [192, 54, 194, 52, "faceHeight"], [192, 64, 194, 62], [192, 65, 194, 63], [193, 14, 195, 12], [194, 12, 196, 10], [194, 13, 196, 11], [194, 14, 196, 12], [195, 10, 197, 8], [195, 11, 197, 9], [195, 17, 197, 15], [196, 12, 198, 10, "setFaceCount"], [196, 24, 198, 22], [196, 25, 198, 23], [196, 26, 198, 24], [196, 27, 198, 25], [197, 10, 199, 8], [198, 8, 201, 6], [198, 9, 201, 7], [198, 10, 201, 8], [198, 17, 201, 15, "error"], [198, 22, 201, 20], [198, 24, 201, 22], [199, 10, 202, 8, "console"], [199, 17, 202, 15], [199, 18, 202, 16, "error"], [199, 23, 202, 21], [199, 24, 202, 22], [199, 60, 202, 58], [199, 62, 202, 60, "error"], [199, 67, 202, 65], [199, 68, 202, 66], [200, 8, 203, 6], [202, 8, 205, 6], [203, 8, 206, 6], [203, 12, 206, 10, "isDetecting"], [203, 23, 206, 21], [203, 25, 206, 23], [204, 10, 207, 8, "rafRef"], [204, 16, 207, 14], [204, 17, 207, 15, "current"], [204, 24, 207, 22], [204, 27, 207, 25, "requestAnimationFrame"], [204, 48, 207, 46], [204, 49, 207, 47, "detectLoop"], [204, 59, 207, 57], [204, 60, 207, 58], [205, 8, 208, 6], [206, 6, 209, 4], [206, 7, 209, 5], [208, 6, 211, 4], [209, 6, 212, 4], [209, 12, 212, 10, "waitForVideoAndStart"], [209, 32, 212, 30], [209, 35, 212, 33, "waitForVideoAndStart"], [209, 36, 212, 33], [209, 41, 212, 39], [210, 8, 213, 6], [210, 12, 213, 10, "video"], [210, 17, 213, 15], [210, 18, 213, 16, "readyState"], [210, 28, 213, 26], [210, 32, 213, 30], [210, 33, 213, 31], [210, 35, 213, 33], [211, 10, 213, 35], [212, 10, 214, 8, "loadModel"], [212, 19, 214, 17], [212, 20, 214, 18], [212, 21, 214, 19], [213, 8, 215, 6], [213, 9, 215, 7], [213, 15, 215, 13], [214, 10, 216, 8, "video"], [214, 15, 216, 13], [214, 16, 216, 14, "addEventListener"], [214, 32, 216, 30], [214, 33, 216, 31], [214, 45, 216, 43], [214, 47, 216, 45, "loadModel"], [214, 56, 216, 54], [214, 58, 216, 56], [215, 12, 216, 58, "once"], [215, 16, 216, 62], [215, 18, 216, 64], [216, 10, 216, 69], [216, 11, 216, 70], [216, 12, 216, 71], [217, 8, 217, 6], [218, 6, 218, 4], [218, 7, 218, 5], [220, 6, 220, 4], [221, 6, 221, 4, "waitForVideoAndStart"], [221, 26, 221, 24], [221, 27, 221, 25], [221, 28, 221, 26], [223, 6, 223, 4], [224, 6, 224, 4], [224, 13, 224, 11], [224, 19, 224, 17], [225, 8, 225, 6, "isDetecting"], [225, 19, 225, 17], [225, 22, 225, 20], [225, 27, 225, 25], [226, 8, 226, 6], [226, 12, 226, 10, "rafRef"], [226, 18, 226, 16], [226, 19, 226, 17, "current"], [226, 26, 226, 24], [226, 28, 226, 26], [227, 10, 227, 8, "cancelAnimationFrame"], [227, 30, 227, 28], [227, 31, 227, 29, "rafRef"], [227, 37, 227, 35], [227, 38, 227, 36, "current"], [227, 45, 227, 43], [227, 46, 227, 44], [228, 8, 228, 6], [229, 6, 229, 4], [229, 7, 229, 5], [230, 4, 230, 2], [230, 5, 230, 3], [230, 7, 230, 5], [230, 8, 230, 6, "containerId"], [230, 19, 230, 17], [230, 21, 230, 19, "width"], [230, 26, 230, 24], [230, 28, 230, 26, "height"], [230, 34, 230, 32], [230, 35, 230, 33], [230, 36, 230, 34], [231, 4, 232, 2], [231, 24, 233, 4], [231, 28, 233, 4, "_jsxDevRuntime"], [231, 42, 233, 4], [231, 43, 233, 4, "jsxDEV"], [231, 49, 233, 4], [231, 51, 233, 4, "_jsxDevRuntime"], [231, 65, 233, 4], [231, 66, 233, 4, "Fragment"], [231, 74, 233, 4], [232, 6, 233, 4, "children"], [232, 14, 233, 4], [232, 30, 234, 6], [232, 34, 234, 6, "_jsxDevRuntime"], [232, 48, 234, 6], [232, 49, 234, 6, "jsxDEV"], [232, 55, 234, 6], [233, 8, 235, 8, "ref"], [233, 11, 235, 11], [233, 13, 235, 13, "canvasRef"], [233, 22, 235, 23], [234, 8, 236, 8, "style"], [234, 13, 236, 13], [234, 15, 236, 15], [235, 10, 237, 10, "position"], [235, 18, 237, 18], [235, 20, 237, 20], [235, 30, 237, 30], [236, 10, 238, 10, "left"], [236, 14, 238, 14], [236, 16, 238, 16], [236, 17, 238, 17], [237, 10, 239, 10, "top"], [237, 13, 239, 13], [237, 15, 239, 15], [237, 16, 239, 16], [238, 10, 240, 10, "width"], [238, 15, 240, 15], [238, 17, 240, 17], [238, 23, 240, 23], [239, 10, 241, 10, "height"], [239, 16, 241, 16], [239, 18, 241, 18], [239, 24, 241, 24], [240, 10, 242, 10, "pointerEvents"], [240, 23, 242, 23], [240, 25, 242, 25], [240, 31, 242, 31], [241, 10, 243, 10, "zIndex"], [241, 16, 243, 16], [241, 18, 243, 18], [241, 20, 243, 20], [242, 10, 243, 22], [243, 10, 244, 10, "objectFit"], [243, 19, 244, 19], [243, 21, 244, 21], [243, 28, 244, 28], [244, 10, 245, 10, "backgroundColor"], [244, 25, 245, 25], [244, 27, 245, 27], [245, 8, 246, 8], [246, 6, 246, 10], [247, 8, 246, 10, "fileName"], [247, 16, 246, 10], [247, 18, 246, 10, "_jsxFileName"], [247, 30, 246, 10], [248, 8, 246, 10, "lineNumber"], [248, 18, 246, 10], [249, 8, 246, 10, "columnNumber"], [249, 20, 246, 10], [250, 6, 246, 10], [250, 13, 247, 7], [250, 14, 247, 8], [250, 16, 249, 7, "isLoading"], [250, 25, 249, 16], [250, 42, 250, 8], [250, 46, 250, 8, "_jsxDevRuntime"], [250, 60, 250, 8], [250, 61, 250, 8, "jsxDEV"], [250, 67, 250, 8], [251, 8, 250, 13, "style"], [251, 13, 250, 18], [251, 15, 250, 20], [252, 10, 251, 10, "position"], [252, 18, 251, 18], [252, 20, 251, 20], [252, 30, 251, 30], [253, 10, 252, 10, "top"], [253, 13, 252, 13], [253, 15, 252, 15], [253, 17, 252, 17], [254, 10, 253, 10, "left"], [254, 14, 253, 14], [254, 16, 253, 16], [254, 18, 253, 18], [255, 10, 254, 10, "background"], [255, 20, 254, 20], [255, 22, 254, 22], [255, 47, 254, 47], [256, 10, 255, 10, "color"], [256, 15, 255, 15], [256, 17, 255, 17], [256, 24, 255, 24], [257, 10, 256, 10, "padding"], [257, 17, 256, 17], [257, 19, 256, 19], [257, 29, 256, 29], [258, 10, 257, 10, "borderRadius"], [258, 22, 257, 22], [258, 24, 257, 24], [258, 29, 257, 29], [259, 10, 258, 10, "fontSize"], [259, 18, 258, 18], [259, 20, 258, 20], [259, 26, 258, 26], [260, 10, 259, 10, "fontWeight"], [260, 20, 259, 20], [260, 22, 259, 22], [260, 27, 259, 27], [261, 10, 260, 10, "zIndex"], [261, 16, 260, 16], [261, 18, 260, 18], [261, 20, 260, 20], [262, 10, 261, 10, "border"], [262, 16, 261, 16], [262, 18, 261, 18], [263, 8, 262, 8], [263, 9, 262, 10], [264, 8, 262, 10, "children"], [264, 16, 262, 10], [264, 18, 262, 11], [265, 6, 264, 8], [266, 8, 264, 8, "fileName"], [266, 16, 264, 8], [266, 18, 264, 8, "_jsxFileName"], [266, 30, 264, 8], [267, 8, 264, 8, "lineNumber"], [267, 18, 264, 8], [268, 8, 264, 8, "columnNumber"], [268, 20, 264, 8], [269, 6, 264, 8], [269, 13, 264, 13], [269, 14, 265, 7], [269, 16, 266, 7], [269, 17, 266, 8, "isLoading"], [269, 26, 266, 17], [269, 30, 266, 21, "faceCount"], [269, 39, 266, 30], [269, 42, 266, 33], [269, 43, 266, 34], [269, 60, 267, 8], [269, 64, 267, 8, "_jsxDevRuntime"], [269, 78, 267, 8], [269, 79, 267, 8, "jsxDEV"], [269, 85, 267, 8], [270, 8, 267, 13, "style"], [270, 13, 267, 18], [270, 15, 267, 20], [271, 10, 268, 10, "position"], [271, 18, 268, 18], [271, 20, 268, 20], [271, 30, 268, 30], [272, 10, 269, 10, "top"], [272, 13, 269, 13], [272, 15, 269, 15], [272, 17, 269, 17], [273, 10, 270, 10, "left"], [273, 14, 270, 14], [273, 16, 270, 16], [273, 18, 270, 18], [274, 10, 271, 10, "background"], [274, 20, 271, 20], [274, 22, 271, 22], [274, 47, 271, 47], [275, 10, 272, 10, "color"], [275, 15, 272, 15], [275, 17, 272, 17], [275, 24, 272, 24], [276, 10, 273, 10, "padding"], [276, 17, 273, 17], [276, 19, 273, 19], [276, 29, 273, 29], [277, 10, 274, 10, "borderRadius"], [277, 22, 274, 22], [277, 24, 274, 24], [277, 29, 274, 29], [278, 10, 275, 10, "fontSize"], [278, 18, 275, 18], [278, 20, 275, 20], [278, 26, 275, 26], [279, 10, 276, 10, "fontWeight"], [279, 20, 276, 20], [279, 22, 276, 22], [279, 27, 276, 27], [280, 10, 277, 10, "zIndex"], [280, 16, 277, 16], [280, 18, 277, 18], [280, 20, 277, 20], [281, 10, 278, 10, "border"], [281, 16, 278, 16], [281, 18, 278, 18], [282, 8, 279, 8], [282, 9, 279, 10], [283, 8, 279, 10, "children"], [283, 16, 279, 10], [283, 19, 279, 11], [283, 51, 280, 25], [283, 53, 280, 26, "faceCount"], [283, 62, 280, 35], [283, 64, 280, 36], [283, 71, 280, 41], [283, 73, 280, 42, "faceCount"], [283, 82, 280, 51], [283, 85, 280, 54], [283, 86, 280, 55], [283, 89, 280, 58], [283, 92, 280, 61], [283, 95, 280, 64], [283, 97, 280, 66], [284, 6, 280, 66], [285, 8, 280, 66, "fileName"], [285, 16, 280, 66], [285, 18, 280, 66, "_jsxFileName"], [285, 30, 280, 66], [286, 8, 280, 66, "lineNumber"], [286, 18, 280, 66], [287, 8, 280, 66, "columnNumber"], [287, 20, 280, 66], [288, 6, 280, 66], [288, 13, 281, 13], [288, 14, 282, 7], [288, 16, 283, 7], [288, 17, 283, 8, "isLoading"], [288, 26, 283, 17], [288, 30, 283, 21, "faceCount"], [288, 39, 283, 30], [288, 44, 283, 35], [288, 45, 283, 36], [288, 62, 284, 8], [288, 66, 284, 8, "_jsxDevRuntime"], [288, 80, 284, 8], [288, 81, 284, 8, "jsxDEV"], [288, 87, 284, 8], [289, 8, 284, 13, "style"], [289, 13, 284, 18], [289, 15, 284, 20], [290, 10, 285, 10, "position"], [290, 18, 285, 18], [290, 20, 285, 20], [290, 30, 285, 30], [291, 10, 286, 10, "top"], [291, 13, 286, 13], [291, 15, 286, 15], [291, 17, 286, 17], [292, 10, 287, 10, "left"], [292, 14, 287, 14], [292, 16, 287, 16], [292, 18, 287, 18], [293, 10, 288, 10, "background"], [293, 20, 288, 20], [293, 22, 288, 22], [293, 48, 288, 48], [294, 10, 289, 10, "color"], [294, 15, 289, 15], [294, 17, 289, 17], [294, 24, 289, 24], [295, 10, 290, 10, "padding"], [295, 17, 290, 17], [295, 19, 290, 19], [295, 29, 290, 29], [296, 10, 291, 10, "borderRadius"], [296, 22, 291, 22], [296, 24, 291, 24], [296, 29, 291, 29], [297, 10, 292, 10, "fontSize"], [297, 18, 292, 18], [297, 20, 292, 20], [297, 26, 292, 26], [298, 10, 293, 10, "fontWeight"], [298, 20, 293, 20], [298, 22, 293, 22], [298, 27, 293, 27], [299, 10, 294, 10, "zIndex"], [299, 16, 294, 16], [299, 18, 294, 18], [299, 20, 294, 20], [300, 10, 295, 10, "border"], [300, 16, 295, 16], [300, 18, 295, 18], [301, 8, 296, 8], [301, 9, 296, 10], [302, 8, 296, 10, "children"], [302, 16, 296, 10], [302, 18, 296, 11], [303, 6, 298, 8], [304, 8, 298, 8, "fileName"], [304, 16, 298, 8], [304, 18, 298, 8, "_jsxFileName"], [304, 30, 298, 8], [305, 8, 298, 8, "lineNumber"], [305, 18, 298, 8], [306, 8, 298, 8, "columnNumber"], [306, 20, 298, 8], [307, 6, 298, 8], [307, 13, 298, 13], [307, 14, 299, 7], [308, 4, 299, 7], [308, 19, 300, 6], [308, 20, 300, 7], [309, 2, 302, 0], [310, 2, 302, 1, "_s"], [310, 4, 302, 1], [310, 5, 17, 24, "BlazeFaceCanvas"], [310, 20, 17, 39], [311, 2, 17, 39, "_c"], [311, 4, 17, 39], [311, 7, 17, 24, "BlazeFaceCanvas"], [311, 22, 17, 39], [312, 2, 17, 39], [312, 6, 17, 39, "_c"], [312, 8, 17, 39], [313, 2, 17, 39, "$RefreshReg$"], [313, 14, 17, 39], [313, 15, 17, 39, "_c"], [313, 17, 17, 39], [314, 0, 17, 39], [314, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "updateCanvasSize", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "loadModel", "detectLoop", "predictions.forEach$argument_0", "waitForVideoAndStart", "<anonymous>"], "mappings": "AAA;eCgB;YCO;6BCsB;KDa;uBEW;yBCC;wBCG,eD;yBEC,gDF;ODE;KFC;sBMG;KNsC;uBOE;8BCyB;WDkD;KPa;iCSG;KTM;WUM;KVK;GDC;CDwE"}}, "type": "js/module"}]}