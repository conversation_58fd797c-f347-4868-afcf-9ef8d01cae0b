{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: Helper functions for robust face detection\n    const loadMediaPipeFaceDetection = async () => {\n      // Try to load MediaPipe Face Detection from CDN\n      if (!window.FaceDetection) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/face_detection@0.4.1646425229/face_detection.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n    };\n    const detectFacesWithMediaPipe = async img => {\n      // This is a placeholder - MediaPipe integration would be complex\n      // For now, return empty array to fall back to heuristic\n      return [];\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Simple skin tone detection and face-like region finding\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 20; // Adaptive block size\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize) {\n        for (let x = 0; x < img.width - blockSize; x += blockSize) {\n          const skinPixels = countSkinPixelsInRegion(data, x, y, blockSize, img.width);\n          const skinRatio = skinPixels / (blockSize * blockSize);\n\n          // If this region has a high concentration of skin-like pixels\n          if (skinRatio > 0.3) {\n            // Check if it's in a face-like position (upper 2/3 of image)\n            if (y < img.height * 0.67) {\n              faces.push({\n                boundingBox: {\n                  xCenter: (x + blockSize / 2) / img.width,\n                  yCenter: (y + blockSize / 2) / img.height,\n                  width: blockSize * 2 / img.width,\n                  // Make bounding box larger\n                  height: blockSize * 2.5 / img.height\n                }\n              });\n              console.log(`[EchoCameraWeb] 🎯 Found face-like region at (${x}, ${y}) with ${(skinRatio * 100).toFixed(1)}% skin pixels`);\n            }\n          }\n        }\n      }\n\n      // Merge overlapping detections and limit to most likely faces\n      const mergedFaces = mergeFaceDetections(faces);\n      return mergedFaces.slice(0, 5); // Max 5 faces\n    };\n    const countSkinPixelsInRegion = (data, startX, startY, size, imageWidth) => {\n      let skinPixels = 0;\n      for (let y = startY; y < startY + size; y++) {\n        for (let x = startX; x < startX + size; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Simple skin tone detection\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n        }\n      }\n      return skinPixels;\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Simple but effective face detection using multiple strategies\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting NEW face detection system...');\n\n        // Strategy 1: Try MediaPipe Face Detection (more reliable)\n        try {\n          await loadMediaPipeFaceDetection();\n          detectedFaces = await detectFacesWithMediaPipe(img);\n          console.log(`[EchoCameraWeb] ✅ MediaPipe found ${detectedFaces.length} faces`);\n        } catch (mediaPipeError) {\n          console.warn('[EchoCameraWeb] ❌ MediaPipe failed:', mediaPipeError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Using intelligent heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect - VERY AGGRESSIVE for testing\n            const pixelSize = Math.max(20, Math.min(paddedWidth, paddedHeight) / 8); // Much larger pixels for obvious effect\n            console.log(`[EchoCameraWeb] 🔲 Using LARGE pixel size: ${pixelSize}px for obvious blurring effect`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} blurring applied successfully`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1426, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadMediaPipeFaceDetection"], [91, 36, 91, 34], [91, 39, 91, 37], [91, 45, 91, 37, "loadMediaPipeFaceDetection"], [91, 46, 91, 37], [91, 51, 91, 49], [92, 6, 92, 4], [93, 6, 93, 4], [93, 10, 93, 8], [93, 11, 93, 10, "window"], [93, 17, 93, 16], [93, 18, 93, 25, "FaceDetection"], [93, 31, 93, 38], [93, 33, 93, 40], [94, 8, 94, 6], [94, 14, 94, 12], [94, 18, 94, 16, "Promise"], [94, 25, 94, 23], [94, 26, 94, 24], [94, 27, 94, 25, "resolve"], [94, 34, 94, 32], [94, 36, 94, 34, "reject"], [94, 42, 94, 40], [94, 47, 94, 45], [95, 10, 95, 8], [95, 16, 95, 14, "script"], [95, 22, 95, 20], [95, 25, 95, 23, "document"], [95, 33, 95, 31], [95, 34, 95, 32, "createElement"], [95, 47, 95, 45], [95, 48, 95, 46], [95, 56, 95, 54], [95, 57, 95, 55], [96, 10, 96, 8, "script"], [96, 16, 96, 14], [96, 17, 96, 15, "src"], [96, 20, 96, 18], [96, 23, 96, 21], [96, 112, 96, 110], [97, 10, 97, 8, "script"], [97, 16, 97, 14], [97, 17, 97, 15, "onload"], [97, 23, 97, 21], [97, 26, 97, 24, "resolve"], [97, 33, 97, 31], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "onerror"], [98, 24, 98, 22], [98, 27, 98, 25, "reject"], [98, 33, 98, 31], [99, 10, 99, 8, "document"], [99, 18, 99, 16], [99, 19, 99, 17, "head"], [99, 23, 99, 21], [99, 24, 99, 22, "append<PERSON><PERSON><PERSON>"], [99, 35, 99, 33], [99, 36, 99, 34, "script"], [99, 42, 99, 40], [99, 43, 99, 41], [100, 8, 100, 6], [100, 9, 100, 7], [100, 10, 100, 8], [101, 6, 101, 4], [102, 4, 102, 2], [102, 5, 102, 3], [103, 4, 104, 2], [103, 10, 104, 8, "detectFacesWithMediaPipe"], [103, 34, 104, 32], [103, 37, 104, 35], [103, 43, 104, 42, "img"], [103, 46, 104, 63], [103, 50, 104, 68], [104, 6, 105, 4], [105, 6, 106, 4], [106, 6, 107, 4], [106, 13, 107, 11], [106, 15, 107, 13], [107, 4, 108, 2], [107, 5, 108, 3], [108, 4, 110, 2], [108, 10, 110, 8, "detectFacesHeuristic"], [108, 30, 110, 28], [108, 33, 110, 31, "detectFacesHeuristic"], [108, 34, 110, 32, "img"], [108, 37, 110, 53], [108, 39, 110, 55, "ctx"], [108, 42, 110, 84], [108, 47, 110, 89], [109, 6, 111, 4, "console"], [109, 13, 111, 11], [109, 14, 111, 12, "log"], [109, 17, 111, 15], [109, 18, 111, 16], [109, 74, 111, 72], [109, 75, 111, 73], [111, 6, 113, 4], [112, 6, 114, 4], [112, 12, 114, 10, "imageData"], [112, 21, 114, 19], [112, 24, 114, 22, "ctx"], [112, 27, 114, 25], [112, 28, 114, 26, "getImageData"], [112, 40, 114, 38], [112, 41, 114, 39], [112, 42, 114, 40], [112, 44, 114, 42], [112, 45, 114, 43], [112, 47, 114, 45, "img"], [112, 50, 114, 48], [112, 51, 114, 49, "width"], [112, 56, 114, 54], [112, 58, 114, 56, "img"], [112, 61, 114, 59], [112, 62, 114, 60, "height"], [112, 68, 114, 66], [112, 69, 114, 67], [113, 6, 115, 4], [113, 12, 115, 10, "data"], [113, 16, 115, 14], [113, 19, 115, 17, "imageData"], [113, 28, 115, 26], [113, 29, 115, 27, "data"], [113, 33, 115, 31], [115, 6, 117, 4], [116, 6, 118, 4], [116, 12, 118, 10, "faces"], [116, 17, 118, 15], [116, 20, 118, 18], [116, 22, 118, 20], [117, 6, 119, 4], [117, 12, 119, 10, "blockSize"], [117, 21, 119, 19], [117, 24, 119, 22, "Math"], [117, 28, 119, 26], [117, 29, 119, 27, "min"], [117, 32, 119, 30], [117, 33, 119, 31, "img"], [117, 36, 119, 34], [117, 37, 119, 35, "width"], [117, 42, 119, 40], [117, 44, 119, 42, "img"], [117, 47, 119, 45], [117, 48, 119, 46, "height"], [117, 54, 119, 52], [117, 55, 119, 53], [117, 58, 119, 56], [117, 60, 119, 58], [117, 61, 119, 59], [117, 62, 119, 60], [119, 6, 121, 4], [119, 11, 121, 9], [119, 15, 121, 13, "y"], [119, 16, 121, 14], [119, 19, 121, 17], [119, 20, 121, 18], [119, 22, 121, 20, "y"], [119, 23, 121, 21], [119, 26, 121, 24, "img"], [119, 29, 121, 27], [119, 30, 121, 28, "height"], [119, 36, 121, 34], [119, 39, 121, 37, "blockSize"], [119, 48, 121, 46], [119, 50, 121, 48, "y"], [119, 51, 121, 49], [119, 55, 121, 53, "blockSize"], [119, 64, 121, 62], [119, 66, 121, 64], [120, 8, 122, 6], [120, 13, 122, 11], [120, 17, 122, 15, "x"], [120, 18, 122, 16], [120, 21, 122, 19], [120, 22, 122, 20], [120, 24, 122, 22, "x"], [120, 25, 122, 23], [120, 28, 122, 26, "img"], [120, 31, 122, 29], [120, 32, 122, 30, "width"], [120, 37, 122, 35], [120, 40, 122, 38, "blockSize"], [120, 49, 122, 47], [120, 51, 122, 49, "x"], [120, 52, 122, 50], [120, 56, 122, 54, "blockSize"], [120, 65, 122, 63], [120, 67, 122, 65], [121, 10, 123, 8], [121, 16, 123, 14, "skinPixels"], [121, 26, 123, 24], [121, 29, 123, 27, "countSkinPixelsInRegion"], [121, 52, 123, 50], [121, 53, 123, 51, "data"], [121, 57, 123, 55], [121, 59, 123, 57, "x"], [121, 60, 123, 58], [121, 62, 123, 60, "y"], [121, 63, 123, 61], [121, 65, 123, 63, "blockSize"], [121, 74, 123, 72], [121, 76, 123, 74, "img"], [121, 79, 123, 77], [121, 80, 123, 78, "width"], [121, 85, 123, 83], [121, 86, 123, 84], [122, 10, 124, 8], [122, 16, 124, 14, "skinRatio"], [122, 25, 124, 23], [122, 28, 124, 26, "skinPixels"], [122, 38, 124, 36], [122, 42, 124, 40, "blockSize"], [122, 51, 124, 49], [122, 54, 124, 52, "blockSize"], [122, 63, 124, 61], [122, 64, 124, 62], [124, 10, 126, 8], [125, 10, 127, 8], [125, 14, 127, 12, "skinRatio"], [125, 23, 127, 21], [125, 26, 127, 24], [125, 29, 127, 27], [125, 31, 127, 29], [126, 12, 128, 10], [127, 12, 129, 10], [127, 16, 129, 14, "y"], [127, 17, 129, 15], [127, 20, 129, 18, "img"], [127, 23, 129, 21], [127, 24, 129, 22, "height"], [127, 30, 129, 28], [127, 33, 129, 31], [127, 37, 129, 35], [127, 39, 129, 37], [128, 14, 130, 12, "faces"], [128, 19, 130, 17], [128, 20, 130, 18, "push"], [128, 24, 130, 22], [128, 25, 130, 23], [129, 16, 131, 14, "boundingBox"], [129, 27, 131, 25], [129, 29, 131, 27], [130, 18, 132, 16, "xCenter"], [130, 25, 132, 23], [130, 27, 132, 25], [130, 28, 132, 26, "x"], [130, 29, 132, 27], [130, 32, 132, 30, "blockSize"], [130, 41, 132, 39], [130, 44, 132, 42], [130, 45, 132, 43], [130, 49, 132, 47, "img"], [130, 52, 132, 50], [130, 53, 132, 51, "width"], [130, 58, 132, 56], [131, 18, 133, 16, "yCenter"], [131, 25, 133, 23], [131, 27, 133, 25], [131, 28, 133, 26, "y"], [131, 29, 133, 27], [131, 32, 133, 30, "blockSize"], [131, 41, 133, 39], [131, 44, 133, 42], [131, 45, 133, 43], [131, 49, 133, 47, "img"], [131, 52, 133, 50], [131, 53, 133, 51, "height"], [131, 59, 133, 57], [132, 18, 134, 16, "width"], [132, 23, 134, 21], [132, 25, 134, 24, "blockSize"], [132, 34, 134, 33], [132, 37, 134, 36], [132, 38, 134, 37], [132, 41, 134, 41, "img"], [132, 44, 134, 44], [132, 45, 134, 45, "width"], [132, 50, 134, 50], [133, 18, 134, 53], [134, 18, 135, 16, "height"], [134, 24, 135, 22], [134, 26, 135, 25, "blockSize"], [134, 35, 135, 34], [134, 38, 135, 37], [134, 41, 135, 40], [134, 44, 135, 44, "img"], [134, 47, 135, 47], [134, 48, 135, 48, "height"], [135, 16, 136, 14], [136, 14, 137, 12], [136, 15, 137, 13], [136, 16, 137, 14], [137, 14, 138, 12, "console"], [137, 21, 138, 19], [137, 22, 138, 20, "log"], [137, 25, 138, 23], [137, 26, 138, 24], [137, 75, 138, 73, "x"], [137, 76, 138, 74], [137, 81, 138, 79, "y"], [137, 82, 138, 80], [137, 92, 138, 90], [137, 93, 138, 91, "skinRatio"], [137, 102, 138, 100], [137, 105, 138, 103], [137, 108, 138, 106], [137, 110, 138, 108, "toFixed"], [137, 117, 138, 115], [137, 118, 138, 116], [137, 119, 138, 117], [137, 120, 138, 118], [137, 135, 138, 133], [137, 136, 138, 134], [138, 12, 139, 10], [139, 10, 140, 8], [140, 8, 141, 6], [141, 6, 142, 4], [143, 6, 144, 4], [144, 6, 145, 4], [144, 12, 145, 10, "mergedFaces"], [144, 23, 145, 21], [144, 26, 145, 24, "mergeFaceDetections"], [144, 45, 145, 43], [144, 46, 145, 44, "faces"], [144, 51, 145, 49], [144, 52, 145, 50], [145, 6, 146, 4], [145, 13, 146, 11, "mergedFaces"], [145, 24, 146, 22], [145, 25, 146, 23, "slice"], [145, 30, 146, 28], [145, 31, 146, 29], [145, 32, 146, 30], [145, 34, 146, 32], [145, 35, 146, 33], [145, 36, 146, 34], [145, 37, 146, 35], [145, 38, 146, 36], [146, 4, 147, 2], [146, 5, 147, 3], [147, 4, 149, 2], [147, 10, 149, 8, "countSkinPixelsInRegion"], [147, 33, 149, 31], [147, 36, 149, 34, "countSkinPixelsInRegion"], [147, 37, 149, 35, "data"], [147, 41, 149, 58], [147, 43, 149, 60, "startX"], [147, 49, 149, 74], [147, 51, 149, 76, "startY"], [147, 57, 149, 90], [147, 59, 149, 92, "size"], [147, 63, 149, 104], [147, 65, 149, 106, "imageWidth"], [147, 75, 149, 124], [147, 80, 149, 129], [148, 6, 150, 4], [148, 10, 150, 8, "skinPixels"], [148, 20, 150, 18], [148, 23, 150, 21], [148, 24, 150, 22], [149, 6, 151, 4], [149, 11, 151, 9], [149, 15, 151, 13, "y"], [149, 16, 151, 14], [149, 19, 151, 17, "startY"], [149, 25, 151, 23], [149, 27, 151, 25, "y"], [149, 28, 151, 26], [149, 31, 151, 29, "startY"], [149, 37, 151, 35], [149, 40, 151, 38, "size"], [149, 44, 151, 42], [149, 46, 151, 44, "y"], [149, 47, 151, 45], [149, 49, 151, 47], [149, 51, 151, 49], [150, 8, 152, 6], [150, 13, 152, 11], [150, 17, 152, 15, "x"], [150, 18, 152, 16], [150, 21, 152, 19, "startX"], [150, 27, 152, 25], [150, 29, 152, 27, "x"], [150, 30, 152, 28], [150, 33, 152, 31, "startX"], [150, 39, 152, 37], [150, 42, 152, 40, "size"], [150, 46, 152, 44], [150, 48, 152, 46, "x"], [150, 49, 152, 47], [150, 51, 152, 49], [150, 53, 152, 51], [151, 10, 153, 8], [151, 16, 153, 14, "index"], [151, 21, 153, 19], [151, 24, 153, 22], [151, 25, 153, 23, "y"], [151, 26, 153, 24], [151, 29, 153, 27, "imageWidth"], [151, 39, 153, 37], [151, 42, 153, 40, "x"], [151, 43, 153, 41], [151, 47, 153, 45], [151, 48, 153, 46], [152, 10, 154, 8], [152, 16, 154, 14, "r"], [152, 17, 154, 15], [152, 20, 154, 18, "data"], [152, 24, 154, 22], [152, 25, 154, 23, "index"], [152, 30, 154, 28], [152, 31, 154, 29], [153, 10, 155, 8], [153, 16, 155, 14, "g"], [153, 17, 155, 15], [153, 20, 155, 18, "data"], [153, 24, 155, 22], [153, 25, 155, 23, "index"], [153, 30, 155, 28], [153, 33, 155, 31], [153, 34, 155, 32], [153, 35, 155, 33], [154, 10, 156, 8], [154, 16, 156, 14, "b"], [154, 17, 156, 15], [154, 20, 156, 18, "data"], [154, 24, 156, 22], [154, 25, 156, 23, "index"], [154, 30, 156, 28], [154, 33, 156, 31], [154, 34, 156, 32], [154, 35, 156, 33], [156, 10, 158, 8], [157, 10, 159, 8], [157, 14, 159, 12, "isSkinTone"], [157, 24, 159, 22], [157, 25, 159, 23, "r"], [157, 26, 159, 24], [157, 28, 159, 26, "g"], [157, 29, 159, 27], [157, 31, 159, 29, "b"], [157, 32, 159, 30], [157, 33, 159, 31], [157, 35, 159, 33], [158, 12, 160, 10, "skinPixels"], [158, 22, 160, 20], [158, 24, 160, 22], [159, 10, 161, 8], [160, 8, 162, 6], [161, 6, 163, 4], [162, 6, 164, 4], [162, 13, 164, 11, "skinPixels"], [162, 23, 164, 21], [163, 4, 165, 2], [163, 5, 165, 3], [164, 4, 167, 2], [164, 10, 167, 8, "isSkinTone"], [164, 20, 167, 18], [164, 23, 167, 21, "isSkinTone"], [164, 24, 167, 22, "r"], [164, 25, 167, 31], [164, 27, 167, 33, "g"], [164, 28, 167, 42], [164, 30, 167, 44, "b"], [164, 31, 167, 53], [164, 36, 167, 58], [165, 6, 168, 4], [166, 6, 169, 4], [166, 13, 170, 6, "r"], [166, 14, 170, 7], [166, 17, 170, 10], [166, 19, 170, 12], [166, 23, 170, 16, "g"], [166, 24, 170, 17], [166, 27, 170, 20], [166, 29, 170, 22], [166, 33, 170, 26, "b"], [166, 34, 170, 27], [166, 37, 170, 30], [166, 39, 170, 32], [166, 43, 171, 6, "r"], [166, 44, 171, 7], [166, 47, 171, 10, "g"], [166, 48, 171, 11], [166, 52, 171, 15, "r"], [166, 53, 171, 16], [166, 56, 171, 19, "b"], [166, 57, 171, 20], [166, 61, 172, 6, "Math"], [166, 65, 172, 10], [166, 66, 172, 11, "abs"], [166, 69, 172, 14], [166, 70, 172, 15, "r"], [166, 71, 172, 16], [166, 74, 172, 19, "g"], [166, 75, 172, 20], [166, 76, 172, 21], [166, 79, 172, 24], [166, 81, 172, 26], [166, 85, 173, 6, "Math"], [166, 89, 173, 10], [166, 90, 173, 11, "max"], [166, 93, 173, 14], [166, 94, 173, 15, "r"], [166, 95, 173, 16], [166, 97, 173, 18, "g"], [166, 98, 173, 19], [166, 100, 173, 21, "b"], [166, 101, 173, 22], [166, 102, 173, 23], [166, 105, 173, 26, "Math"], [166, 109, 173, 30], [166, 110, 173, 31, "min"], [166, 113, 173, 34], [166, 114, 173, 35, "r"], [166, 115, 173, 36], [166, 117, 173, 38, "g"], [166, 118, 173, 39], [166, 120, 173, 41, "b"], [166, 121, 173, 42], [166, 122, 173, 43], [166, 125, 173, 46], [166, 127, 173, 48], [167, 4, 175, 2], [167, 5, 175, 3], [168, 4, 177, 2], [168, 10, 177, 8, "mergeFaceDetections"], [168, 29, 177, 27], [168, 32, 177, 31, "faces"], [168, 37, 177, 43], [168, 41, 177, 48], [169, 6, 178, 4], [169, 10, 178, 8, "faces"], [169, 15, 178, 13], [169, 16, 178, 14, "length"], [169, 22, 178, 20], [169, 26, 178, 24], [169, 27, 178, 25], [169, 29, 178, 27], [169, 36, 178, 34, "faces"], [169, 41, 178, 39], [170, 6, 180, 4], [170, 12, 180, 10, "merged"], [170, 18, 180, 16], [170, 21, 180, 19], [170, 23, 180, 21], [171, 6, 181, 4], [171, 12, 181, 10, "used"], [171, 16, 181, 14], [171, 19, 181, 17], [171, 23, 181, 21, "Set"], [171, 26, 181, 24], [171, 27, 181, 25], [171, 28, 181, 26], [172, 6, 183, 4], [172, 11, 183, 9], [172, 15, 183, 13, "i"], [172, 16, 183, 14], [172, 19, 183, 17], [172, 20, 183, 18], [172, 22, 183, 20, "i"], [172, 23, 183, 21], [172, 26, 183, 24, "faces"], [172, 31, 183, 29], [172, 32, 183, 30, "length"], [172, 38, 183, 36], [172, 40, 183, 38, "i"], [172, 41, 183, 39], [172, 43, 183, 41], [172, 45, 183, 43], [173, 8, 184, 6], [173, 12, 184, 10, "used"], [173, 16, 184, 14], [173, 17, 184, 15, "has"], [173, 20, 184, 18], [173, 21, 184, 19, "i"], [173, 22, 184, 20], [173, 23, 184, 21], [173, 25, 184, 23], [174, 8, 186, 6], [174, 12, 186, 10, "currentFace"], [174, 23, 186, 21], [174, 26, 186, 24, "faces"], [174, 31, 186, 29], [174, 32, 186, 30, "i"], [174, 33, 186, 31], [174, 34, 186, 32], [175, 8, 187, 6, "used"], [175, 12, 187, 10], [175, 13, 187, 11, "add"], [175, 16, 187, 14], [175, 17, 187, 15, "i"], [175, 18, 187, 16], [175, 19, 187, 17], [177, 8, 189, 6], [178, 8, 190, 6], [178, 13, 190, 11], [178, 17, 190, 15, "j"], [178, 18, 190, 16], [178, 21, 190, 19, "i"], [178, 22, 190, 20], [178, 25, 190, 23], [178, 26, 190, 24], [178, 28, 190, 26, "j"], [178, 29, 190, 27], [178, 32, 190, 30, "faces"], [178, 37, 190, 35], [178, 38, 190, 36, "length"], [178, 44, 190, 42], [178, 46, 190, 44, "j"], [178, 47, 190, 45], [178, 49, 190, 47], [178, 51, 190, 49], [179, 10, 191, 8], [179, 14, 191, 12, "used"], [179, 18, 191, 16], [179, 19, 191, 17, "has"], [179, 22, 191, 20], [179, 23, 191, 21, "j"], [179, 24, 191, 22], [179, 25, 191, 23], [179, 27, 191, 25], [180, 10, 193, 8], [180, 16, 193, 14, "overlap"], [180, 23, 193, 21], [180, 26, 193, 24, "calculateOverlap"], [180, 42, 193, 40], [180, 43, 193, 41, "currentFace"], [180, 54, 193, 52], [180, 55, 193, 53, "boundingBox"], [180, 66, 193, 64], [180, 68, 193, 66, "faces"], [180, 73, 193, 71], [180, 74, 193, 72, "j"], [180, 75, 193, 73], [180, 76, 193, 74], [180, 77, 193, 75, "boundingBox"], [180, 88, 193, 86], [180, 89, 193, 87], [181, 10, 194, 8], [181, 14, 194, 12, "overlap"], [181, 21, 194, 19], [181, 24, 194, 22], [181, 27, 194, 25], [181, 29, 194, 27], [182, 12, 194, 29], [183, 12, 195, 10], [184, 12, 196, 10, "currentFace"], [184, 23, 196, 21], [184, 26, 196, 24, "mergeTwoFaces"], [184, 39, 196, 37], [184, 40, 196, 38, "currentFace"], [184, 51, 196, 49], [184, 53, 196, 51, "faces"], [184, 58, 196, 56], [184, 59, 196, 57, "j"], [184, 60, 196, 58], [184, 61, 196, 59], [184, 62, 196, 60], [185, 12, 197, 10, "used"], [185, 16, 197, 14], [185, 17, 197, 15, "add"], [185, 20, 197, 18], [185, 21, 197, 19, "j"], [185, 22, 197, 20], [185, 23, 197, 21], [186, 10, 198, 8], [187, 8, 199, 6], [188, 8, 201, 6, "merged"], [188, 14, 201, 12], [188, 15, 201, 13, "push"], [188, 19, 201, 17], [188, 20, 201, 18, "currentFace"], [188, 31, 201, 29], [188, 32, 201, 30], [189, 6, 202, 4], [190, 6, 204, 4], [190, 13, 204, 11, "merged"], [190, 19, 204, 17], [191, 4, 205, 2], [191, 5, 205, 3], [192, 4, 207, 2], [192, 10, 207, 8, "calculateOverlap"], [192, 26, 207, 24], [192, 29, 207, 27, "calculateOverlap"], [192, 30, 207, 28, "box1"], [192, 34, 207, 37], [192, 36, 207, 39, "box2"], [192, 40, 207, 48], [192, 45, 207, 53], [193, 6, 208, 4], [193, 12, 208, 10, "x1"], [193, 14, 208, 12], [193, 17, 208, 15, "Math"], [193, 21, 208, 19], [193, 22, 208, 20, "max"], [193, 25, 208, 23], [193, 26, 208, 24, "box1"], [193, 30, 208, 28], [193, 31, 208, 29, "xCenter"], [193, 38, 208, 36], [193, 41, 208, 39, "box1"], [193, 45, 208, 43], [193, 46, 208, 44, "width"], [193, 51, 208, 49], [193, 54, 208, 50], [193, 55, 208, 51], [193, 57, 208, 53, "box2"], [193, 61, 208, 57], [193, 62, 208, 58, "xCenter"], [193, 69, 208, 65], [193, 72, 208, 68, "box2"], [193, 76, 208, 72], [193, 77, 208, 73, "width"], [193, 82, 208, 78], [193, 85, 208, 79], [193, 86, 208, 80], [193, 87, 208, 81], [194, 6, 209, 4], [194, 12, 209, 10, "y1"], [194, 14, 209, 12], [194, 17, 209, 15, "Math"], [194, 21, 209, 19], [194, 22, 209, 20, "max"], [194, 25, 209, 23], [194, 26, 209, 24, "box1"], [194, 30, 209, 28], [194, 31, 209, 29, "yCenter"], [194, 38, 209, 36], [194, 41, 209, 39, "box1"], [194, 45, 209, 43], [194, 46, 209, 44, "height"], [194, 52, 209, 50], [194, 55, 209, 51], [194, 56, 209, 52], [194, 58, 209, 54, "box2"], [194, 62, 209, 58], [194, 63, 209, 59, "yCenter"], [194, 70, 209, 66], [194, 73, 209, 69, "box2"], [194, 77, 209, 73], [194, 78, 209, 74, "height"], [194, 84, 209, 80], [194, 87, 209, 81], [194, 88, 209, 82], [194, 89, 209, 83], [195, 6, 210, 4], [195, 12, 210, 10, "x2"], [195, 14, 210, 12], [195, 17, 210, 15, "Math"], [195, 21, 210, 19], [195, 22, 210, 20, "min"], [195, 25, 210, 23], [195, 26, 210, 24, "box1"], [195, 30, 210, 28], [195, 31, 210, 29, "xCenter"], [195, 38, 210, 36], [195, 41, 210, 39, "box1"], [195, 45, 210, 43], [195, 46, 210, 44, "width"], [195, 51, 210, 49], [195, 54, 210, 50], [195, 55, 210, 51], [195, 57, 210, 53, "box2"], [195, 61, 210, 57], [195, 62, 210, 58, "xCenter"], [195, 69, 210, 65], [195, 72, 210, 68, "box2"], [195, 76, 210, 72], [195, 77, 210, 73, "width"], [195, 82, 210, 78], [195, 85, 210, 79], [195, 86, 210, 80], [195, 87, 210, 81], [196, 6, 211, 4], [196, 12, 211, 10, "y2"], [196, 14, 211, 12], [196, 17, 211, 15, "Math"], [196, 21, 211, 19], [196, 22, 211, 20, "min"], [196, 25, 211, 23], [196, 26, 211, 24, "box1"], [196, 30, 211, 28], [196, 31, 211, 29, "yCenter"], [196, 38, 211, 36], [196, 41, 211, 39, "box1"], [196, 45, 211, 43], [196, 46, 211, 44, "height"], [196, 52, 211, 50], [196, 55, 211, 51], [196, 56, 211, 52], [196, 58, 211, 54, "box2"], [196, 62, 211, 58], [196, 63, 211, 59, "yCenter"], [196, 70, 211, 66], [196, 73, 211, 69, "box2"], [196, 77, 211, 73], [196, 78, 211, 74, "height"], [196, 84, 211, 80], [196, 87, 211, 81], [196, 88, 211, 82], [196, 89, 211, 83], [197, 6, 213, 4], [197, 10, 213, 8, "x2"], [197, 12, 213, 10], [197, 16, 213, 14, "x1"], [197, 18, 213, 16], [197, 22, 213, 20, "y2"], [197, 24, 213, 22], [197, 28, 213, 26, "y1"], [197, 30, 213, 28], [197, 32, 213, 30], [197, 39, 213, 37], [197, 40, 213, 38], [198, 6, 215, 4], [198, 12, 215, 10, "overlapArea"], [198, 23, 215, 21], [198, 26, 215, 24], [198, 27, 215, 25, "x2"], [198, 29, 215, 27], [198, 32, 215, 30, "x1"], [198, 34, 215, 32], [198, 39, 215, 37, "y2"], [198, 41, 215, 39], [198, 44, 215, 42, "y1"], [198, 46, 215, 44], [198, 47, 215, 45], [199, 6, 216, 4], [199, 12, 216, 10, "box1Area"], [199, 20, 216, 18], [199, 23, 216, 21, "box1"], [199, 27, 216, 25], [199, 28, 216, 26, "width"], [199, 33, 216, 31], [199, 36, 216, 34, "box1"], [199, 40, 216, 38], [199, 41, 216, 39, "height"], [199, 47, 216, 45], [200, 6, 217, 4], [200, 12, 217, 10, "box2Area"], [200, 20, 217, 18], [200, 23, 217, 21, "box2"], [200, 27, 217, 25], [200, 28, 217, 26, "width"], [200, 33, 217, 31], [200, 36, 217, 34, "box2"], [200, 40, 217, 38], [200, 41, 217, 39, "height"], [200, 47, 217, 45], [201, 6, 219, 4], [201, 13, 219, 11, "overlapArea"], [201, 24, 219, 22], [201, 27, 219, 25, "Math"], [201, 31, 219, 29], [201, 32, 219, 30, "min"], [201, 35, 219, 33], [201, 36, 219, 34, "box1Area"], [201, 44, 219, 42], [201, 46, 219, 44, "box2Area"], [201, 54, 219, 52], [201, 55, 219, 53], [202, 4, 220, 2], [202, 5, 220, 3], [203, 4, 222, 2], [203, 10, 222, 8, "mergeTwoFaces"], [203, 23, 222, 21], [203, 26, 222, 24, "mergeTwoFaces"], [203, 27, 222, 25, "face1"], [203, 32, 222, 35], [203, 34, 222, 37, "face2"], [203, 39, 222, 47], [203, 44, 222, 52], [204, 6, 223, 4], [204, 12, 223, 10, "box1"], [204, 16, 223, 14], [204, 19, 223, 17, "face1"], [204, 24, 223, 22], [204, 25, 223, 23, "boundingBox"], [204, 36, 223, 34], [205, 6, 224, 4], [205, 12, 224, 10, "box2"], [205, 16, 224, 14], [205, 19, 224, 17, "face2"], [205, 24, 224, 22], [205, 25, 224, 23, "boundingBox"], [205, 36, 224, 34], [206, 6, 226, 4], [206, 12, 226, 10, "left"], [206, 16, 226, 14], [206, 19, 226, 17, "Math"], [206, 23, 226, 21], [206, 24, 226, 22, "min"], [206, 27, 226, 25], [206, 28, 226, 26, "box1"], [206, 32, 226, 30], [206, 33, 226, 31, "xCenter"], [206, 40, 226, 38], [206, 43, 226, 41, "box1"], [206, 47, 226, 45], [206, 48, 226, 46, "width"], [206, 53, 226, 51], [206, 56, 226, 52], [206, 57, 226, 53], [206, 59, 226, 55, "box2"], [206, 63, 226, 59], [206, 64, 226, 60, "xCenter"], [206, 71, 226, 67], [206, 74, 226, 70, "box2"], [206, 78, 226, 74], [206, 79, 226, 75, "width"], [206, 84, 226, 80], [206, 87, 226, 81], [206, 88, 226, 82], [206, 89, 226, 83], [207, 6, 227, 4], [207, 12, 227, 10, "right"], [207, 17, 227, 15], [207, 20, 227, 18, "Math"], [207, 24, 227, 22], [207, 25, 227, 23, "max"], [207, 28, 227, 26], [207, 29, 227, 27, "box1"], [207, 33, 227, 31], [207, 34, 227, 32, "xCenter"], [207, 41, 227, 39], [207, 44, 227, 42, "box1"], [207, 48, 227, 46], [207, 49, 227, 47, "width"], [207, 54, 227, 52], [207, 57, 227, 53], [207, 58, 227, 54], [207, 60, 227, 56, "box2"], [207, 64, 227, 60], [207, 65, 227, 61, "xCenter"], [207, 72, 227, 68], [207, 75, 227, 71, "box2"], [207, 79, 227, 75], [207, 80, 227, 76, "width"], [207, 85, 227, 81], [207, 88, 227, 82], [207, 89, 227, 83], [207, 90, 227, 84], [208, 6, 228, 4], [208, 12, 228, 10, "top"], [208, 15, 228, 13], [208, 18, 228, 16, "Math"], [208, 22, 228, 20], [208, 23, 228, 21, "min"], [208, 26, 228, 24], [208, 27, 228, 25, "box1"], [208, 31, 228, 29], [208, 32, 228, 30, "yCenter"], [208, 39, 228, 37], [208, 42, 228, 40, "box1"], [208, 46, 228, 44], [208, 47, 228, 45, "height"], [208, 53, 228, 51], [208, 56, 228, 52], [208, 57, 228, 53], [208, 59, 228, 55, "box2"], [208, 63, 228, 59], [208, 64, 228, 60, "yCenter"], [208, 71, 228, 67], [208, 74, 228, 70, "box2"], [208, 78, 228, 74], [208, 79, 228, 75, "height"], [208, 85, 228, 81], [208, 88, 228, 82], [208, 89, 228, 83], [208, 90, 228, 84], [209, 6, 229, 4], [209, 12, 229, 10, "bottom"], [209, 18, 229, 16], [209, 21, 229, 19, "Math"], [209, 25, 229, 23], [209, 26, 229, 24, "max"], [209, 29, 229, 27], [209, 30, 229, 28, "box1"], [209, 34, 229, 32], [209, 35, 229, 33, "yCenter"], [209, 42, 229, 40], [209, 45, 229, 43, "box1"], [209, 49, 229, 47], [209, 50, 229, 48, "height"], [209, 56, 229, 54], [209, 59, 229, 55], [209, 60, 229, 56], [209, 62, 229, 58, "box2"], [209, 66, 229, 62], [209, 67, 229, 63, "yCenter"], [209, 74, 229, 70], [209, 77, 229, 73, "box2"], [209, 81, 229, 77], [209, 82, 229, 78, "height"], [209, 88, 229, 84], [209, 91, 229, 85], [209, 92, 229, 86], [209, 93, 229, 87], [210, 6, 231, 4], [210, 13, 231, 11], [211, 8, 232, 6, "boundingBox"], [211, 19, 232, 17], [211, 21, 232, 19], [212, 10, 233, 8, "xCenter"], [212, 17, 233, 15], [212, 19, 233, 17], [212, 20, 233, 18, "left"], [212, 24, 233, 22], [212, 27, 233, 25, "right"], [212, 32, 233, 30], [212, 36, 233, 34], [212, 37, 233, 35], [213, 10, 234, 8, "yCenter"], [213, 17, 234, 15], [213, 19, 234, 17], [213, 20, 234, 18, "top"], [213, 23, 234, 21], [213, 26, 234, 24, "bottom"], [213, 32, 234, 30], [213, 36, 234, 34], [213, 37, 234, 35], [214, 10, 235, 8, "width"], [214, 15, 235, 13], [214, 17, 235, 15, "right"], [214, 22, 235, 20], [214, 25, 235, 23, "left"], [214, 29, 235, 27], [215, 10, 236, 8, "height"], [215, 16, 236, 14], [215, 18, 236, 16, "bottom"], [215, 24, 236, 22], [215, 27, 236, 25, "top"], [216, 8, 237, 6], [217, 6, 238, 4], [217, 7, 238, 5], [218, 4, 239, 2], [218, 5, 239, 3], [220, 4, 241, 2], [221, 4, 242, 2], [221, 10, 242, 8, "capturePhoto"], [221, 22, 242, 20], [221, 25, 242, 23], [221, 29, 242, 23, "useCallback"], [221, 47, 242, 34], [221, 49, 242, 35], [221, 61, 242, 47], [222, 6, 243, 4], [223, 6, 244, 4], [223, 12, 244, 10, "isDev"], [223, 17, 244, 15], [223, 20, 244, 18, "process"], [223, 27, 244, 25], [223, 28, 244, 26, "env"], [223, 31, 244, 29], [223, 32, 244, 30, "NODE_ENV"], [223, 40, 244, 38], [223, 45, 244, 43], [223, 58, 244, 56], [223, 62, 244, 60, "__DEV__"], [223, 69, 244, 67], [224, 6, 246, 4], [224, 10, 246, 8], [224, 11, 246, 9, "cameraRef"], [224, 20, 246, 18], [224, 21, 246, 19, "current"], [224, 28, 246, 26], [224, 32, 246, 30], [224, 33, 246, 31, "isDev"], [224, 38, 246, 36], [224, 40, 246, 38], [225, 8, 247, 6, "<PERSON><PERSON>"], [225, 22, 247, 11], [225, 23, 247, 12, "alert"], [225, 28, 247, 17], [225, 29, 247, 18], [225, 36, 247, 25], [225, 38, 247, 27], [225, 56, 247, 45], [225, 57, 247, 46], [226, 8, 248, 6], [227, 6, 249, 4], [228, 6, 250, 4], [228, 10, 250, 8], [229, 8, 251, 6, "setProcessingState"], [229, 26, 251, 24], [229, 27, 251, 25], [229, 38, 251, 36], [229, 39, 251, 37], [230, 8, 252, 6, "setProcessingProgress"], [230, 29, 252, 27], [230, 30, 252, 28], [230, 32, 252, 30], [230, 33, 252, 31], [231, 8, 253, 6], [232, 8, 254, 6], [233, 8, 255, 6], [234, 8, 256, 6], [234, 14, 256, 12], [234, 18, 256, 16, "Promise"], [234, 25, 256, 23], [234, 26, 256, 24, "resolve"], [234, 33, 256, 31], [234, 37, 256, 35, "setTimeout"], [234, 47, 256, 45], [234, 48, 256, 46, "resolve"], [234, 55, 256, 53], [234, 57, 256, 55], [234, 59, 256, 57], [234, 60, 256, 58], [234, 61, 256, 59], [235, 8, 257, 6], [236, 8, 258, 6], [236, 12, 258, 10, "photo"], [236, 17, 258, 15], [237, 8, 260, 6], [237, 12, 260, 10], [238, 10, 261, 8, "photo"], [238, 15, 261, 13], [238, 18, 261, 16], [238, 24, 261, 22, "cameraRef"], [238, 33, 261, 31], [238, 34, 261, 32, "current"], [238, 41, 261, 39], [238, 42, 261, 40, "takePictureAsync"], [238, 58, 261, 56], [238, 59, 261, 57], [239, 12, 262, 10, "quality"], [239, 19, 262, 17], [239, 21, 262, 19], [239, 24, 262, 22], [240, 12, 263, 10, "base64"], [240, 18, 263, 16], [240, 20, 263, 18], [240, 25, 263, 23], [241, 12, 264, 10, "skipProcessing"], [241, 26, 264, 24], [241, 28, 264, 26], [241, 32, 264, 30], [241, 33, 264, 32], [242, 10, 265, 8], [242, 11, 265, 9], [242, 12, 265, 10], [243, 8, 266, 6], [243, 9, 266, 7], [243, 10, 266, 8], [243, 17, 266, 15, "cameraError"], [243, 28, 266, 26], [243, 30, 266, 28], [244, 10, 267, 8, "console"], [244, 17, 267, 15], [244, 18, 267, 16, "log"], [244, 21, 267, 19], [244, 22, 267, 20], [244, 82, 267, 80], [244, 84, 267, 82, "cameraError"], [244, 95, 267, 93], [244, 96, 267, 94], [245, 10, 268, 8], [246, 10, 269, 8], [246, 14, 269, 12, "isDev"], [246, 19, 269, 17], [246, 21, 269, 19], [247, 12, 270, 10, "photo"], [247, 17, 270, 15], [247, 20, 270, 18], [248, 14, 271, 12, "uri"], [248, 17, 271, 15], [248, 19, 271, 17], [249, 12, 272, 10], [249, 13, 272, 11], [250, 10, 273, 8], [250, 11, 273, 9], [250, 17, 273, 15], [251, 12, 274, 10], [251, 18, 274, 16, "cameraError"], [251, 29, 274, 27], [252, 10, 275, 8], [253, 8, 276, 6], [254, 8, 277, 6], [254, 12, 277, 10], [254, 13, 277, 11, "photo"], [254, 18, 277, 16], [254, 20, 277, 18], [255, 10, 278, 8], [255, 16, 278, 14], [255, 20, 278, 18, "Error"], [255, 25, 278, 23], [255, 26, 278, 24], [255, 51, 278, 49], [255, 52, 278, 50], [256, 8, 279, 6], [257, 8, 280, 6, "console"], [257, 15, 280, 13], [257, 16, 280, 14, "log"], [257, 19, 280, 17], [257, 20, 280, 18], [257, 56, 280, 54], [257, 58, 280, 56, "photo"], [257, 63, 280, 61], [257, 64, 280, 62, "uri"], [257, 67, 280, 65], [257, 68, 280, 66], [258, 8, 281, 6, "setCapturedPhoto"], [258, 24, 281, 22], [258, 25, 281, 23, "photo"], [258, 30, 281, 28], [258, 31, 281, 29, "uri"], [258, 34, 281, 32], [258, 35, 281, 33], [259, 8, 282, 6, "setProcessingProgress"], [259, 29, 282, 27], [259, 30, 282, 28], [259, 32, 282, 30], [259, 33, 282, 31], [260, 8, 283, 6], [261, 8, 284, 6, "console"], [261, 15, 284, 13], [261, 16, 284, 14, "log"], [261, 19, 284, 17], [261, 20, 284, 18], [261, 73, 284, 71], [261, 74, 284, 72], [262, 8, 285, 6], [262, 14, 285, 12, "processImageWithFaceBlur"], [262, 38, 285, 36], [262, 39, 285, 37, "photo"], [262, 44, 285, 42], [262, 45, 285, 43, "uri"], [262, 48, 285, 46], [262, 49, 285, 47], [263, 8, 286, 6, "console"], [263, 15, 286, 13], [263, 16, 286, 14, "log"], [263, 19, 286, 17], [263, 20, 286, 18], [263, 71, 286, 69], [263, 72, 286, 70], [264, 6, 287, 4], [264, 7, 287, 5], [264, 8, 287, 6], [264, 15, 287, 13, "error"], [264, 20, 287, 18], [264, 22, 287, 20], [265, 8, 288, 6, "console"], [265, 15, 288, 13], [265, 16, 288, 14, "error"], [265, 21, 288, 19], [265, 22, 288, 20], [265, 54, 288, 52], [265, 56, 288, 54, "error"], [265, 61, 288, 59], [265, 62, 288, 60], [266, 8, 289, 6, "setErrorMessage"], [266, 23, 289, 21], [266, 24, 289, 22], [266, 68, 289, 66], [266, 69, 289, 67], [267, 8, 290, 6, "setProcessingState"], [267, 26, 290, 24], [267, 27, 290, 25], [267, 34, 290, 32], [267, 35, 290, 33], [268, 6, 291, 4], [269, 4, 292, 2], [269, 5, 292, 3], [269, 7, 292, 5], [269, 9, 292, 7], [269, 10, 292, 8], [270, 4, 293, 2], [271, 4, 294, 2], [271, 10, 294, 8, "processImageWithFaceBlur"], [271, 34, 294, 32], [271, 37, 294, 35], [271, 43, 294, 42, "photoUri"], [271, 51, 294, 58], [271, 55, 294, 63], [272, 6, 295, 4], [272, 10, 295, 8], [273, 8, 296, 6, "console"], [273, 15, 296, 13], [273, 16, 296, 14, "log"], [273, 19, 296, 17], [273, 20, 296, 18], [273, 84, 296, 82], [273, 85, 296, 83], [274, 8, 297, 6, "setProcessingState"], [274, 26, 297, 24], [274, 27, 297, 25], [274, 39, 297, 37], [274, 40, 297, 38], [275, 8, 298, 6, "setProcessingProgress"], [275, 29, 298, 27], [275, 30, 298, 28], [275, 32, 298, 30], [275, 33, 298, 31], [277, 8, 300, 6], [278, 8, 301, 6], [278, 14, 301, 12, "canvas"], [278, 20, 301, 18], [278, 23, 301, 21, "document"], [278, 31, 301, 29], [278, 32, 301, 30, "createElement"], [278, 45, 301, 43], [278, 46, 301, 44], [278, 54, 301, 52], [278, 55, 301, 53], [279, 8, 302, 6], [279, 14, 302, 12, "ctx"], [279, 17, 302, 15], [279, 20, 302, 18, "canvas"], [279, 26, 302, 24], [279, 27, 302, 25, "getContext"], [279, 37, 302, 35], [279, 38, 302, 36], [279, 42, 302, 40], [279, 43, 302, 41], [280, 8, 303, 6], [280, 12, 303, 10], [280, 13, 303, 11, "ctx"], [280, 16, 303, 14], [280, 18, 303, 16], [280, 24, 303, 22], [280, 28, 303, 26, "Error"], [280, 33, 303, 31], [280, 34, 303, 32], [280, 64, 303, 62], [280, 65, 303, 63], [282, 8, 305, 6], [283, 8, 306, 6], [283, 14, 306, 12, "img"], [283, 17, 306, 15], [283, 20, 306, 18], [283, 24, 306, 22, "Image"], [283, 29, 306, 27], [283, 30, 306, 28], [283, 31, 306, 29], [284, 8, 307, 6], [284, 14, 307, 12], [284, 18, 307, 16, "Promise"], [284, 25, 307, 23], [284, 26, 307, 24], [284, 27, 307, 25, "resolve"], [284, 34, 307, 32], [284, 36, 307, 34, "reject"], [284, 42, 307, 40], [284, 47, 307, 45], [285, 10, 308, 8, "img"], [285, 13, 308, 11], [285, 14, 308, 12, "onload"], [285, 20, 308, 18], [285, 23, 308, 21, "resolve"], [285, 30, 308, 28], [286, 10, 309, 8, "img"], [286, 13, 309, 11], [286, 14, 309, 12, "onerror"], [286, 21, 309, 19], [286, 24, 309, 22, "reject"], [286, 30, 309, 28], [287, 10, 310, 8, "img"], [287, 13, 310, 11], [287, 14, 310, 12, "src"], [287, 17, 310, 15], [287, 20, 310, 18, "photoUri"], [287, 28, 310, 26], [288, 8, 311, 6], [288, 9, 311, 7], [288, 10, 311, 8], [290, 8, 313, 6], [291, 8, 314, 6, "canvas"], [291, 14, 314, 12], [291, 15, 314, 13, "width"], [291, 20, 314, 18], [291, 23, 314, 21, "img"], [291, 26, 314, 24], [291, 27, 314, 25, "width"], [291, 32, 314, 30], [292, 8, 315, 6, "canvas"], [292, 14, 315, 12], [292, 15, 315, 13, "height"], [292, 21, 315, 19], [292, 24, 315, 22, "img"], [292, 27, 315, 25], [292, 28, 315, 26, "height"], [292, 34, 315, 32], [293, 8, 316, 6, "console"], [293, 15, 316, 13], [293, 16, 316, 14, "log"], [293, 19, 316, 17], [293, 20, 316, 18], [293, 54, 316, 52], [293, 56, 316, 54], [294, 10, 316, 56, "width"], [294, 15, 316, 61], [294, 17, 316, 63, "img"], [294, 20, 316, 66], [294, 21, 316, 67, "width"], [294, 26, 316, 72], [295, 10, 316, 74, "height"], [295, 16, 316, 80], [295, 18, 316, 82, "img"], [295, 21, 316, 85], [295, 22, 316, 86, "height"], [296, 8, 316, 93], [296, 9, 316, 94], [296, 10, 316, 95], [298, 8, 318, 6], [299, 8, 319, 6, "ctx"], [299, 11, 319, 9], [299, 12, 319, 10, "drawImage"], [299, 21, 319, 19], [299, 22, 319, 20, "img"], [299, 25, 319, 23], [299, 27, 319, 25], [299, 28, 319, 26], [299, 30, 319, 28], [299, 31, 319, 29], [299, 32, 319, 30], [300, 8, 320, 6, "console"], [300, 15, 320, 13], [300, 16, 320, 14, "log"], [300, 19, 320, 17], [300, 20, 320, 18], [300, 72, 320, 70], [300, 73, 320, 71], [301, 8, 322, 6, "setProcessingProgress"], [301, 29, 322, 27], [301, 30, 322, 28], [301, 32, 322, 30], [301, 33, 322, 31], [303, 8, 324, 6], [304, 8, 325, 6], [304, 12, 325, 10, "detectedFaces"], [304, 25, 325, 23], [304, 28, 325, 26], [304, 30, 325, 28], [305, 8, 327, 6, "console"], [305, 15, 327, 13], [305, 16, 327, 14, "log"], [305, 19, 327, 17], [305, 20, 327, 18], [305, 78, 327, 76], [305, 79, 327, 77], [307, 8, 329, 6], [308, 8, 330, 6], [308, 12, 330, 10], [309, 10, 331, 8], [309, 16, 331, 14, "loadMediaPipeFaceDetection"], [309, 42, 331, 40], [309, 43, 331, 41], [309, 44, 331, 42], [310, 10, 332, 8, "detectedFaces"], [310, 23, 332, 21], [310, 26, 332, 24], [310, 32, 332, 30, "detectFacesWithMediaPipe"], [310, 56, 332, 54], [310, 57, 332, 55, "img"], [310, 60, 332, 58], [310, 61, 332, 59], [311, 10, 333, 8, "console"], [311, 17, 333, 15], [311, 18, 333, 16, "log"], [311, 21, 333, 19], [311, 22, 333, 20], [311, 59, 333, 57, "detectedFaces"], [311, 72, 333, 70], [311, 73, 333, 71, "length"], [311, 79, 333, 77], [311, 87, 333, 85], [311, 88, 333, 86], [312, 8, 334, 6], [312, 9, 334, 7], [312, 10, 334, 8], [312, 17, 334, 15, "mediaPipeError"], [312, 31, 334, 29], [312, 33, 334, 31], [313, 10, 335, 8, "console"], [313, 17, 335, 15], [313, 18, 335, 16, "warn"], [313, 22, 335, 20], [313, 23, 335, 21], [313, 60, 335, 58], [313, 62, 335, 60, "mediaPipeError"], [313, 76, 335, 74], [313, 77, 335, 75], [315, 10, 337, 8], [316, 10, 338, 8, "console"], [316, 17, 338, 15], [316, 18, 338, 16, "log"], [316, 21, 338, 19], [316, 22, 338, 20], [316, 88, 338, 86], [316, 89, 338, 87], [317, 10, 339, 8, "detectedFaces"], [317, 23, 339, 21], [317, 26, 339, 24, "detectFacesHeuristic"], [317, 46, 339, 44], [317, 47, 339, 45, "img"], [317, 50, 339, 48], [317, 52, 339, 50, "ctx"], [317, 55, 339, 53], [317, 56, 339, 54], [318, 10, 340, 8, "console"], [318, 17, 340, 15], [318, 18, 340, 16, "log"], [318, 21, 340, 19], [318, 22, 340, 20], [318, 70, 340, 68, "detectedFaces"], [318, 83, 340, 81], [318, 84, 340, 82, "length"], [318, 90, 340, 88], [318, 98, 340, 96], [318, 99, 340, 97], [319, 8, 341, 6], [320, 8, 343, 6, "console"], [320, 15, 343, 13], [320, 16, 343, 14, "log"], [320, 19, 343, 17], [320, 20, 343, 18], [320, 72, 343, 70, "detectedFaces"], [320, 85, 343, 83], [320, 86, 343, 84, "length"], [320, 92, 343, 90], [320, 100, 343, 98], [320, 101, 343, 99], [321, 8, 344, 6], [321, 12, 344, 10, "detectedFaces"], [321, 25, 344, 23], [321, 26, 344, 24, "length"], [321, 32, 344, 30], [321, 35, 344, 33], [321, 36, 344, 34], [321, 38, 344, 36], [322, 10, 345, 8, "console"], [322, 17, 345, 15], [322, 18, 345, 16, "log"], [322, 21, 345, 19], [322, 22, 345, 20], [322, 66, 345, 64], [322, 68, 345, 66, "detectedFaces"], [322, 81, 345, 79], [322, 82, 345, 80, "map"], [322, 85, 345, 83], [322, 86, 345, 84], [322, 87, 345, 85, "face"], [322, 91, 345, 89], [322, 93, 345, 91, "i"], [322, 94, 345, 92], [322, 100, 345, 98], [323, 12, 346, 10, "faceNumber"], [323, 22, 346, 20], [323, 24, 346, 22, "i"], [323, 25, 346, 23], [323, 28, 346, 26], [323, 29, 346, 27], [324, 12, 347, 10, "centerX"], [324, 19, 347, 17], [324, 21, 347, 19, "face"], [324, 25, 347, 23], [324, 26, 347, 24, "boundingBox"], [324, 37, 347, 35], [324, 38, 347, 36, "xCenter"], [324, 45, 347, 43], [325, 12, 348, 10, "centerY"], [325, 19, 348, 17], [325, 21, 348, 19, "face"], [325, 25, 348, 23], [325, 26, 348, 24, "boundingBox"], [325, 37, 348, 35], [325, 38, 348, 36, "yCenter"], [325, 45, 348, 43], [326, 12, 349, 10, "width"], [326, 17, 349, 15], [326, 19, 349, 17, "face"], [326, 23, 349, 21], [326, 24, 349, 22, "boundingBox"], [326, 35, 349, 33], [326, 36, 349, 34, "width"], [326, 41, 349, 39], [327, 12, 350, 10, "height"], [327, 18, 350, 16], [327, 20, 350, 18, "face"], [327, 24, 350, 22], [327, 25, 350, 23, "boundingBox"], [327, 36, 350, 34], [327, 37, 350, 35, "height"], [328, 10, 351, 8], [328, 11, 351, 9], [328, 12, 351, 10], [328, 13, 351, 11], [328, 14, 351, 12], [329, 8, 352, 6], [329, 9, 352, 7], [329, 15, 352, 13], [330, 10, 353, 8, "console"], [330, 17, 353, 15], [330, 18, 353, 16, "log"], [330, 21, 353, 19], [330, 22, 353, 20], [330, 91, 353, 89], [330, 92, 353, 90], [331, 8, 354, 6], [332, 8, 356, 6, "setProcessingProgress"], [332, 29, 356, 27], [332, 30, 356, 28], [332, 32, 356, 30], [332, 33, 356, 31], [334, 8, 358, 6], [335, 8, 359, 6], [335, 12, 359, 10, "detectedFaces"], [335, 25, 359, 23], [335, 26, 359, 24, "length"], [335, 32, 359, 30], [335, 35, 359, 33], [335, 36, 359, 34], [335, 38, 359, 36], [336, 10, 360, 8, "detectedFaces"], [336, 23, 360, 21], [336, 24, 360, 22, "for<PERSON>ach"], [336, 31, 360, 29], [336, 32, 360, 30], [336, 33, 360, 31, "detection"], [336, 42, 360, 40], [336, 44, 360, 42, "index"], [336, 49, 360, 47], [336, 54, 360, 52], [337, 12, 361, 10], [337, 18, 361, 16, "bbox"], [337, 22, 361, 20], [337, 25, 361, 23, "detection"], [337, 34, 361, 32], [337, 35, 361, 33, "boundingBox"], [337, 46, 361, 44], [339, 12, 363, 10], [340, 12, 364, 10], [340, 18, 364, 16, "faceX"], [340, 23, 364, 21], [340, 26, 364, 24, "bbox"], [340, 30, 364, 28], [340, 31, 364, 29, "xCenter"], [340, 38, 364, 36], [340, 41, 364, 39, "img"], [340, 44, 364, 42], [340, 45, 364, 43, "width"], [340, 50, 364, 48], [340, 53, 364, 52, "bbox"], [340, 57, 364, 56], [340, 58, 364, 57, "width"], [340, 63, 364, 62], [340, 66, 364, 65, "img"], [340, 69, 364, 68], [340, 70, 364, 69, "width"], [340, 75, 364, 74], [340, 78, 364, 78], [340, 79, 364, 79], [341, 12, 365, 10], [341, 18, 365, 16, "faceY"], [341, 23, 365, 21], [341, 26, 365, 24, "bbox"], [341, 30, 365, 28], [341, 31, 365, 29, "yCenter"], [341, 38, 365, 36], [341, 41, 365, 39, "img"], [341, 44, 365, 42], [341, 45, 365, 43, "height"], [341, 51, 365, 49], [341, 54, 365, 53, "bbox"], [341, 58, 365, 57], [341, 59, 365, 58, "height"], [341, 65, 365, 64], [341, 68, 365, 67, "img"], [341, 71, 365, 70], [341, 72, 365, 71, "height"], [341, 78, 365, 77], [341, 81, 365, 81], [341, 82, 365, 82], [342, 12, 366, 10], [342, 18, 366, 16, "faceWidth"], [342, 27, 366, 25], [342, 30, 366, 28, "bbox"], [342, 34, 366, 32], [342, 35, 366, 33, "width"], [342, 40, 366, 38], [342, 43, 366, 41, "img"], [342, 46, 366, 44], [342, 47, 366, 45, "width"], [342, 52, 366, 50], [343, 12, 367, 10], [343, 18, 367, 16, "faceHeight"], [343, 28, 367, 26], [343, 31, 367, 29, "bbox"], [343, 35, 367, 33], [343, 36, 367, 34, "height"], [343, 42, 367, 40], [343, 45, 367, 43, "img"], [343, 48, 367, 46], [343, 49, 367, 47, "height"], [343, 55, 367, 53], [345, 12, 369, 10], [346, 12, 370, 10], [346, 18, 370, 16, "padding"], [346, 25, 370, 23], [346, 28, 370, 26], [346, 31, 370, 29], [346, 32, 370, 30], [346, 33, 370, 31], [347, 12, 371, 10], [347, 18, 371, 16, "paddedX"], [347, 25, 371, 23], [347, 28, 371, 26, "Math"], [347, 32, 371, 30], [347, 33, 371, 31, "max"], [347, 36, 371, 34], [347, 37, 371, 35], [347, 38, 371, 36], [347, 40, 371, 38, "faceX"], [347, 45, 371, 43], [347, 48, 371, 46, "faceWidth"], [347, 57, 371, 55], [347, 60, 371, 58, "padding"], [347, 67, 371, 65], [347, 68, 371, 66], [348, 12, 372, 10], [348, 18, 372, 16, "paddedY"], [348, 25, 372, 23], [348, 28, 372, 26, "Math"], [348, 32, 372, 30], [348, 33, 372, 31, "max"], [348, 36, 372, 34], [348, 37, 372, 35], [348, 38, 372, 36], [348, 40, 372, 38, "faceY"], [348, 45, 372, 43], [348, 48, 372, 46, "faceHeight"], [348, 58, 372, 56], [348, 61, 372, 59, "padding"], [348, 68, 372, 66], [348, 69, 372, 67], [349, 12, 373, 10], [349, 18, 373, 16, "<PERSON><PERSON><PERSON><PERSON>"], [349, 29, 373, 27], [349, 32, 373, 30, "Math"], [349, 36, 373, 34], [349, 37, 373, 35, "min"], [349, 40, 373, 38], [349, 41, 373, 39, "img"], [349, 44, 373, 42], [349, 45, 373, 43, "width"], [349, 50, 373, 48], [349, 53, 373, 51, "paddedX"], [349, 60, 373, 58], [349, 62, 373, 60, "faceWidth"], [349, 71, 373, 69], [349, 75, 373, 73], [349, 76, 373, 74], [349, 79, 373, 77], [349, 80, 373, 78], [349, 83, 373, 81, "padding"], [349, 90, 373, 88], [349, 91, 373, 89], [349, 92, 373, 90], [350, 12, 374, 10], [350, 18, 374, 16, "paddedHeight"], [350, 30, 374, 28], [350, 33, 374, 31, "Math"], [350, 37, 374, 35], [350, 38, 374, 36, "min"], [350, 41, 374, 39], [350, 42, 374, 40, "img"], [350, 45, 374, 43], [350, 46, 374, 44, "height"], [350, 52, 374, 50], [350, 55, 374, 53, "paddedY"], [350, 62, 374, 60], [350, 64, 374, 62, "faceHeight"], [350, 74, 374, 72], [350, 78, 374, 76], [350, 79, 374, 77], [350, 82, 374, 80], [350, 83, 374, 81], [350, 86, 374, 84, "padding"], [350, 93, 374, 91], [350, 94, 374, 92], [350, 95, 374, 93], [351, 12, 376, 10, "console"], [351, 19, 376, 17], [351, 20, 376, 18, "log"], [351, 23, 376, 21], [351, 24, 376, 22], [351, 60, 376, 58, "index"], [351, 65, 376, 63], [351, 68, 376, 66], [351, 69, 376, 67], [351, 77, 376, 75, "Math"], [351, 81, 376, 79], [351, 82, 376, 80, "round"], [351, 87, 376, 85], [351, 88, 376, 86, "paddedX"], [351, 95, 376, 93], [351, 96, 376, 94], [351, 101, 376, 99, "Math"], [351, 105, 376, 103], [351, 106, 376, 104, "round"], [351, 111, 376, 109], [351, 112, 376, 110, "paddedY"], [351, 119, 376, 117], [351, 120, 376, 118], [351, 130, 376, 128, "Math"], [351, 134, 376, 132], [351, 135, 376, 133, "round"], [351, 140, 376, 138], [351, 141, 376, 139, "<PERSON><PERSON><PERSON><PERSON>"], [351, 152, 376, 150], [351, 153, 376, 151], [351, 157, 376, 155, "Math"], [351, 161, 376, 159], [351, 162, 376, 160, "round"], [351, 167, 376, 165], [351, 168, 376, 166, "paddedHeight"], [351, 180, 376, 178], [351, 181, 376, 179], [351, 183, 376, 181], [351, 184, 376, 182], [353, 12, 378, 10], [354, 12, 379, 10], [354, 18, 379, 16, "faceImageData"], [354, 31, 379, 29], [354, 34, 379, 32, "ctx"], [354, 37, 379, 35], [354, 38, 379, 36, "getImageData"], [354, 50, 379, 48], [354, 51, 379, 49, "paddedX"], [354, 58, 379, 56], [354, 60, 379, 58, "paddedY"], [354, 67, 379, 65], [354, 69, 379, 67, "<PERSON><PERSON><PERSON><PERSON>"], [354, 80, 379, 78], [354, 82, 379, 80, "paddedHeight"], [354, 94, 379, 92], [354, 95, 379, 93], [355, 12, 380, 10], [355, 18, 380, 16, "data"], [355, 22, 380, 20], [355, 25, 380, 23, "faceImageData"], [355, 38, 380, 36], [355, 39, 380, 37, "data"], [355, 43, 380, 41], [356, 12, 382, 10, "console"], [356, 19, 382, 17], [356, 20, 382, 18, "log"], [356, 23, 382, 21], [356, 24, 382, 22], [356, 64, 382, 62, "data"], [356, 68, 382, 66], [356, 69, 382, 67, "length"], [356, 75, 382, 73], [356, 86, 382, 84, "<PERSON><PERSON><PERSON><PERSON>"], [356, 97, 382, 95], [356, 101, 382, 99, "paddedHeight"], [356, 113, 382, 111], [356, 122, 382, 120], [356, 123, 382, 121], [358, 12, 384, 10], [359, 12, 385, 10], [359, 18, 385, 16, "pixelSize"], [359, 27, 385, 25], [359, 30, 385, 28, "Math"], [359, 34, 385, 32], [359, 35, 385, 33, "max"], [359, 38, 385, 36], [359, 39, 385, 37], [359, 41, 385, 39], [359, 43, 385, 41, "Math"], [359, 47, 385, 45], [359, 48, 385, 46, "min"], [359, 51, 385, 49], [359, 52, 385, 50, "<PERSON><PERSON><PERSON><PERSON>"], [359, 63, 385, 61], [359, 65, 385, 63, "paddedHeight"], [359, 77, 385, 75], [359, 78, 385, 76], [359, 81, 385, 79], [359, 82, 385, 80], [359, 83, 385, 81], [359, 84, 385, 82], [359, 85, 385, 83], [360, 12, 386, 10, "console"], [360, 19, 386, 17], [360, 20, 386, 18, "log"], [360, 23, 386, 21], [360, 24, 386, 22], [360, 70, 386, 68, "pixelSize"], [360, 79, 386, 77], [360, 111, 386, 109], [360, 112, 386, 110], [361, 12, 387, 10], [361, 17, 387, 15], [361, 21, 387, 19, "y"], [361, 22, 387, 20], [361, 25, 387, 23], [361, 26, 387, 24], [361, 28, 387, 26, "y"], [361, 29, 387, 27], [361, 32, 387, 30, "paddedHeight"], [361, 44, 387, 42], [361, 46, 387, 44, "y"], [361, 47, 387, 45], [361, 51, 387, 49, "pixelSize"], [361, 60, 387, 58], [361, 62, 387, 60], [362, 14, 388, 12], [362, 19, 388, 17], [362, 23, 388, 21, "x"], [362, 24, 388, 22], [362, 27, 388, 25], [362, 28, 388, 26], [362, 30, 388, 28, "x"], [362, 31, 388, 29], [362, 34, 388, 32, "<PERSON><PERSON><PERSON><PERSON>"], [362, 45, 388, 43], [362, 47, 388, 45, "x"], [362, 48, 388, 46], [362, 52, 388, 50, "pixelSize"], [362, 61, 388, 59], [362, 63, 388, 61], [363, 16, 389, 14], [364, 16, 390, 14], [364, 22, 390, 20, "pixelIndex"], [364, 32, 390, 30], [364, 35, 390, 33], [364, 36, 390, 34, "y"], [364, 37, 390, 35], [364, 40, 390, 38, "<PERSON><PERSON><PERSON><PERSON>"], [364, 51, 390, 49], [364, 54, 390, 52, "x"], [364, 55, 390, 53], [364, 59, 390, 57], [364, 60, 390, 58], [365, 16, 391, 14], [365, 22, 391, 20, "r"], [365, 23, 391, 21], [365, 26, 391, 24, "data"], [365, 30, 391, 28], [365, 31, 391, 29, "pixelIndex"], [365, 41, 391, 39], [365, 42, 391, 40], [366, 16, 392, 14], [366, 22, 392, 20, "g"], [366, 23, 392, 21], [366, 26, 392, 24, "data"], [366, 30, 392, 28], [366, 31, 392, 29, "pixelIndex"], [366, 41, 392, 39], [366, 44, 392, 42], [366, 45, 392, 43], [366, 46, 392, 44], [367, 16, 393, 14], [367, 22, 393, 20, "b"], [367, 23, 393, 21], [367, 26, 393, 24, "data"], [367, 30, 393, 28], [367, 31, 393, 29, "pixelIndex"], [367, 41, 393, 39], [367, 44, 393, 42], [367, 45, 393, 43], [367, 46, 393, 44], [368, 16, 394, 14], [368, 22, 394, 20, "a"], [368, 23, 394, 21], [368, 26, 394, 24, "data"], [368, 30, 394, 28], [368, 31, 394, 29, "pixelIndex"], [368, 41, 394, 39], [368, 44, 394, 42], [368, 45, 394, 43], [368, 46, 394, 44], [370, 16, 396, 14], [371, 16, 397, 14], [371, 21, 397, 19], [371, 25, 397, 23, "dy"], [371, 27, 397, 25], [371, 30, 397, 28], [371, 31, 397, 29], [371, 33, 397, 31, "dy"], [371, 35, 397, 33], [371, 38, 397, 36, "pixelSize"], [371, 47, 397, 45], [371, 51, 397, 49, "y"], [371, 52, 397, 50], [371, 55, 397, 53, "dy"], [371, 57, 397, 55], [371, 60, 397, 58, "paddedHeight"], [371, 72, 397, 70], [371, 74, 397, 72, "dy"], [371, 76, 397, 74], [371, 78, 397, 76], [371, 80, 397, 78], [372, 18, 398, 16], [372, 23, 398, 21], [372, 27, 398, 25, "dx"], [372, 29, 398, 27], [372, 32, 398, 30], [372, 33, 398, 31], [372, 35, 398, 33, "dx"], [372, 37, 398, 35], [372, 40, 398, 38, "pixelSize"], [372, 49, 398, 47], [372, 53, 398, 51, "x"], [372, 54, 398, 52], [372, 57, 398, 55, "dx"], [372, 59, 398, 57], [372, 62, 398, 60, "<PERSON><PERSON><PERSON><PERSON>"], [372, 73, 398, 71], [372, 75, 398, 73, "dx"], [372, 77, 398, 75], [372, 79, 398, 77], [372, 81, 398, 79], [373, 20, 399, 18], [373, 26, 399, 24, "blockPixelIndex"], [373, 41, 399, 39], [373, 44, 399, 42], [373, 45, 399, 43], [373, 46, 399, 44, "y"], [373, 47, 399, 45], [373, 50, 399, 48, "dy"], [373, 52, 399, 50], [373, 56, 399, 54, "<PERSON><PERSON><PERSON><PERSON>"], [373, 67, 399, 65], [373, 71, 399, 69, "x"], [373, 72, 399, 70], [373, 75, 399, 73, "dx"], [373, 77, 399, 75], [373, 78, 399, 76], [373, 82, 399, 80], [373, 83, 399, 81], [374, 20, 400, 18, "data"], [374, 24, 400, 22], [374, 25, 400, 23, "blockPixelIndex"], [374, 40, 400, 38], [374, 41, 400, 39], [374, 44, 400, 42, "r"], [374, 45, 400, 43], [375, 20, 401, 18, "data"], [375, 24, 401, 22], [375, 25, 401, 23, "blockPixelIndex"], [375, 40, 401, 38], [375, 43, 401, 41], [375, 44, 401, 42], [375, 45, 401, 43], [375, 48, 401, 46, "g"], [375, 49, 401, 47], [376, 20, 402, 18, "data"], [376, 24, 402, 22], [376, 25, 402, 23, "blockPixelIndex"], [376, 40, 402, 38], [376, 43, 402, 41], [376, 44, 402, 42], [376, 45, 402, 43], [376, 48, 402, 46, "b"], [376, 49, 402, 47], [377, 20, 403, 18, "data"], [377, 24, 403, 22], [377, 25, 403, 23, "blockPixelIndex"], [377, 40, 403, 38], [377, 43, 403, 41], [377, 44, 403, 42], [377, 45, 403, 43], [377, 48, 403, 46, "a"], [377, 49, 403, 47], [378, 18, 404, 16], [379, 16, 405, 14], [380, 14, 406, 12], [381, 12, 407, 10], [383, 12, 409, 10], [384, 12, 410, 10, "ctx"], [384, 15, 410, 13], [384, 16, 410, 14, "putImageData"], [384, 28, 410, 26], [384, 29, 410, 27, "faceImageData"], [384, 42, 410, 40], [384, 44, 410, 42, "paddedX"], [384, 51, 410, 49], [384, 53, 410, 51, "paddedY"], [384, 60, 410, 58], [384, 61, 410, 59], [385, 12, 411, 10, "console"], [385, 19, 411, 17], [385, 20, 411, 18, "log"], [385, 23, 411, 21], [385, 24, 411, 22], [385, 50, 411, 48, "index"], [385, 55, 411, 53], [385, 58, 411, 56], [385, 59, 411, 57], [385, 91, 411, 89], [385, 92, 411, 90], [386, 10, 412, 8], [386, 11, 412, 9], [386, 12, 412, 10], [387, 10, 413, 8, "console"], [387, 17, 413, 15], [387, 18, 413, 16, "log"], [387, 21, 413, 19], [387, 22, 413, 20], [387, 48, 413, 46, "detectedFaces"], [387, 61, 413, 59], [387, 62, 413, 60, "length"], [387, 68, 413, 66], [387, 95, 413, 93], [387, 96, 413, 94], [388, 8, 414, 6], [388, 9, 414, 7], [388, 15, 414, 13], [389, 10, 415, 8, "console"], [389, 17, 415, 15], [389, 18, 415, 16, "log"], [389, 21, 415, 19], [389, 22, 415, 20], [389, 91, 415, 89], [389, 92, 415, 90], [390, 8, 416, 6], [391, 8, 418, 6, "setProcessingProgress"], [391, 29, 418, 27], [391, 30, 418, 28], [391, 32, 418, 30], [391, 33, 418, 31], [393, 8, 420, 6], [394, 8, 421, 6, "console"], [394, 15, 421, 13], [394, 16, 421, 14, "log"], [394, 19, 421, 17], [394, 20, 421, 18], [394, 85, 421, 83], [394, 86, 421, 84], [395, 8, 422, 6], [395, 14, 422, 12, "blurredImageBlob"], [395, 30, 422, 28], [395, 33, 422, 31], [395, 39, 422, 37], [395, 43, 422, 41, "Promise"], [395, 50, 422, 48], [395, 51, 422, 56, "resolve"], [395, 58, 422, 63], [395, 62, 422, 68], [396, 10, 423, 8, "canvas"], [396, 16, 423, 14], [396, 17, 423, 15, "toBlob"], [396, 23, 423, 21], [396, 24, 423, 23, "blob"], [396, 28, 423, 27], [396, 32, 423, 32, "resolve"], [396, 39, 423, 39], [396, 40, 423, 40, "blob"], [396, 44, 423, 45], [396, 45, 423, 46], [396, 47, 423, 48], [396, 59, 423, 60], [396, 61, 423, 62], [396, 64, 423, 65], [396, 65, 423, 66], [397, 8, 424, 6], [397, 9, 424, 7], [397, 10, 424, 8], [398, 8, 426, 6], [398, 14, 426, 12, "blurredImageUrl"], [398, 29, 426, 27], [398, 32, 426, 30, "URL"], [398, 35, 426, 33], [398, 36, 426, 34, "createObjectURL"], [398, 51, 426, 49], [398, 52, 426, 50, "blurredImageBlob"], [398, 68, 426, 66], [398, 69, 426, 67], [399, 8, 427, 6, "console"], [399, 15, 427, 13], [399, 16, 427, 14, "log"], [399, 19, 427, 17], [399, 20, 427, 18], [399, 66, 427, 64], [399, 68, 427, 66, "blurredImageUrl"], [399, 83, 427, 81], [399, 84, 427, 82, "substring"], [399, 93, 427, 91], [399, 94, 427, 92], [399, 95, 427, 93], [399, 97, 427, 95], [399, 99, 427, 97], [399, 100, 427, 98], [399, 103, 427, 101], [399, 108, 427, 106], [399, 109, 427, 107], [400, 8, 429, 6, "setProcessingProgress"], [400, 29, 429, 27], [400, 30, 429, 28], [400, 33, 429, 31], [400, 34, 429, 32], [402, 8, 431, 6], [403, 8, 432, 6], [403, 14, 432, 12, "completeProcessing"], [403, 32, 432, 30], [403, 33, 432, 31, "blurredImageUrl"], [403, 48, 432, 46], [403, 49, 432, 47], [404, 6, 434, 4], [404, 7, 434, 5], [404, 8, 434, 6], [404, 15, 434, 13, "error"], [404, 20, 434, 18], [404, 22, 434, 20], [405, 8, 435, 6, "console"], [405, 15, 435, 13], [405, 16, 435, 14, "error"], [405, 21, 435, 19], [405, 22, 435, 20], [405, 57, 435, 55], [405, 59, 435, 57, "error"], [405, 64, 435, 62], [405, 65, 435, 63], [406, 8, 436, 6, "setErrorMessage"], [406, 23, 436, 21], [406, 24, 436, 22], [406, 50, 436, 48], [406, 51, 436, 49], [407, 8, 437, 6, "setProcessingState"], [407, 26, 437, 24], [407, 27, 437, 25], [407, 34, 437, 32], [407, 35, 437, 33], [408, 6, 438, 4], [409, 4, 439, 2], [409, 5, 439, 3], [411, 4, 441, 2], [412, 4, 442, 2], [412, 10, 442, 8, "completeProcessing"], [412, 28, 442, 26], [412, 31, 442, 29], [412, 37, 442, 36, "blurredImageUrl"], [412, 52, 442, 59], [412, 56, 442, 64], [413, 6, 443, 4], [413, 10, 443, 8], [414, 8, 444, 6, "setProcessingState"], [414, 26, 444, 24], [414, 27, 444, 25], [414, 37, 444, 35], [414, 38, 444, 36], [416, 8, 446, 6], [417, 8, 447, 6], [417, 14, 447, 12, "timestamp"], [417, 23, 447, 21], [417, 26, 447, 24, "Date"], [417, 30, 447, 28], [417, 31, 447, 29, "now"], [417, 34, 447, 32], [417, 35, 447, 33], [417, 36, 447, 34], [418, 8, 448, 6], [418, 14, 448, 12, "result"], [418, 20, 448, 18], [418, 23, 448, 21], [419, 10, 449, 8, "imageUrl"], [419, 18, 449, 16], [419, 20, 449, 18, "blurredImageUrl"], [419, 35, 449, 33], [420, 10, 450, 8, "localUri"], [420, 18, 450, 16], [420, 20, 450, 18, "blurredImageUrl"], [420, 35, 450, 33], [421, 10, 451, 8, "challengeCode"], [421, 23, 451, 21], [421, 25, 451, 23, "challengeCode"], [421, 38, 451, 36], [421, 42, 451, 40], [421, 44, 451, 42], [422, 10, 452, 8, "timestamp"], [422, 19, 452, 17], [423, 10, 453, 8, "jobId"], [423, 15, 453, 13], [423, 17, 453, 15], [423, 27, 453, 25, "timestamp"], [423, 36, 453, 34], [423, 38, 453, 36], [424, 10, 454, 8, "status"], [424, 16, 454, 14], [424, 18, 454, 16], [425, 8, 455, 6], [425, 9, 455, 7], [426, 8, 457, 6, "console"], [426, 15, 457, 13], [426, 16, 457, 14, "log"], [426, 19, 457, 17], [426, 20, 457, 18], [426, 100, 457, 98], [426, 102, 457, 100], [427, 10, 458, 8, "imageUrl"], [427, 18, 458, 16], [427, 20, 458, 18, "blurredImageUrl"], [427, 35, 458, 33], [427, 36, 458, 34, "substring"], [427, 45, 458, 43], [427, 46, 458, 44], [427, 47, 458, 45], [427, 49, 458, 47], [427, 51, 458, 49], [427, 52, 458, 50], [427, 55, 458, 53], [427, 60, 458, 58], [428, 10, 459, 8, "timestamp"], [428, 19, 459, 17], [429, 10, 460, 8, "jobId"], [429, 15, 460, 13], [429, 17, 460, 15, "result"], [429, 23, 460, 21], [429, 24, 460, 22, "jobId"], [430, 8, 461, 6], [430, 9, 461, 7], [430, 10, 461, 8], [432, 8, 463, 6], [433, 8, 464, 6, "onComplete"], [433, 18, 464, 16], [433, 19, 464, 17, "result"], [433, 25, 464, 23], [433, 26, 464, 24], [434, 6, 466, 4], [434, 7, 466, 5], [434, 8, 466, 6], [434, 15, 466, 13, "error"], [434, 20, 466, 18], [434, 22, 466, 20], [435, 8, 467, 6, "console"], [435, 15, 467, 13], [435, 16, 467, 14, "error"], [435, 21, 467, 19], [435, 22, 467, 20], [435, 57, 467, 55], [435, 59, 467, 57, "error"], [435, 64, 467, 62], [435, 65, 467, 63], [436, 8, 468, 6, "setErrorMessage"], [436, 23, 468, 21], [436, 24, 468, 22], [436, 56, 468, 54], [436, 57, 468, 55], [437, 8, 469, 6, "setProcessingState"], [437, 26, 469, 24], [437, 27, 469, 25], [437, 34, 469, 32], [437, 35, 469, 33], [438, 6, 470, 4], [439, 4, 471, 2], [439, 5, 471, 3], [441, 4, 473, 2], [442, 4, 474, 2], [442, 10, 474, 8, "triggerServerProcessing"], [442, 33, 474, 31], [442, 36, 474, 34], [442, 42, 474, 34, "triggerServerProcessing"], [442, 43, 474, 41, "privateImageUrl"], [442, 58, 474, 64], [442, 60, 474, 66, "timestamp"], [442, 69, 474, 83], [442, 74, 474, 88], [443, 6, 475, 4], [443, 10, 475, 8], [444, 8, 476, 6, "console"], [444, 15, 476, 13], [444, 16, 476, 14, "log"], [444, 19, 476, 17], [444, 20, 476, 18], [444, 74, 476, 72], [444, 76, 476, 74, "privateImageUrl"], [444, 91, 476, 89], [444, 92, 476, 90], [445, 8, 477, 6, "setProcessingState"], [445, 26, 477, 24], [445, 27, 477, 25], [445, 39, 477, 37], [445, 40, 477, 38], [446, 8, 478, 6, "setProcessingProgress"], [446, 29, 478, 27], [446, 30, 478, 28], [446, 32, 478, 30], [446, 33, 478, 31], [447, 8, 480, 6], [447, 14, 480, 12, "requestBody"], [447, 25, 480, 23], [447, 28, 480, 26], [448, 10, 481, 8, "imageUrl"], [448, 18, 481, 16], [448, 20, 481, 18, "privateImageUrl"], [448, 35, 481, 33], [449, 10, 482, 8, "userId"], [449, 16, 482, 14], [450, 10, 483, 8, "requestId"], [450, 19, 483, 17], [451, 10, 484, 8, "timestamp"], [451, 19, 484, 17], [452, 10, 485, 8, "platform"], [452, 18, 485, 16], [452, 20, 485, 18], [453, 8, 486, 6], [453, 9, 486, 7], [454, 8, 488, 6, "console"], [454, 15, 488, 13], [454, 16, 488, 14, "log"], [454, 19, 488, 17], [454, 20, 488, 18], [454, 65, 488, 63], [454, 67, 488, 65, "requestBody"], [454, 78, 488, 76], [454, 79, 488, 77], [456, 8, 490, 6], [457, 8, 491, 6], [457, 14, 491, 12, "response"], [457, 22, 491, 20], [457, 25, 491, 23], [457, 31, 491, 29, "fetch"], [457, 36, 491, 34], [457, 37, 491, 35], [457, 40, 491, 38, "API_BASE_URL"], [457, 52, 491, 50], [457, 72, 491, 70], [457, 74, 491, 72], [458, 10, 492, 8, "method"], [458, 16, 492, 14], [458, 18, 492, 16], [458, 24, 492, 22], [459, 10, 493, 8, "headers"], [459, 17, 493, 15], [459, 19, 493, 17], [460, 12, 494, 10], [460, 26, 494, 24], [460, 28, 494, 26], [460, 46, 494, 44], [461, 12, 495, 10], [461, 27, 495, 25], [461, 29, 495, 27], [461, 39, 495, 37], [461, 45, 495, 43, "getAuthToken"], [461, 57, 495, 55], [461, 58, 495, 56], [461, 59, 495, 57], [462, 10, 496, 8], [462, 11, 496, 9], [463, 10, 497, 8, "body"], [463, 14, 497, 12], [463, 16, 497, 14, "JSON"], [463, 20, 497, 18], [463, 21, 497, 19, "stringify"], [463, 30, 497, 28], [463, 31, 497, 29, "requestBody"], [463, 42, 497, 40], [464, 8, 498, 6], [464, 9, 498, 7], [464, 10, 498, 8], [465, 8, 500, 6], [465, 12, 500, 10], [465, 13, 500, 11, "response"], [465, 21, 500, 19], [465, 22, 500, 20, "ok"], [465, 24, 500, 22], [465, 26, 500, 24], [466, 10, 501, 8], [466, 16, 501, 14, "errorText"], [466, 25, 501, 23], [466, 28, 501, 26], [466, 34, 501, 32, "response"], [466, 42, 501, 40], [466, 43, 501, 41, "text"], [466, 47, 501, 45], [466, 48, 501, 46], [466, 49, 501, 47], [467, 10, 502, 8, "console"], [467, 17, 502, 15], [467, 18, 502, 16, "error"], [467, 23, 502, 21], [467, 24, 502, 22], [467, 68, 502, 66], [467, 70, 502, 68, "response"], [467, 78, 502, 76], [467, 79, 502, 77, "status"], [467, 85, 502, 83], [467, 87, 502, 85, "errorText"], [467, 96, 502, 94], [467, 97, 502, 95], [468, 10, 503, 8], [468, 16, 503, 14], [468, 20, 503, 18, "Error"], [468, 25, 503, 23], [468, 26, 503, 24], [468, 48, 503, 46, "response"], [468, 56, 503, 54], [468, 57, 503, 55, "status"], [468, 63, 503, 61], [468, 67, 503, 65, "response"], [468, 75, 503, 73], [468, 76, 503, 74, "statusText"], [468, 86, 503, 84], [468, 88, 503, 86], [468, 89, 503, 87], [469, 8, 504, 6], [470, 8, 506, 6], [470, 14, 506, 12, "result"], [470, 20, 506, 18], [470, 23, 506, 21], [470, 29, 506, 27, "response"], [470, 37, 506, 35], [470, 38, 506, 36, "json"], [470, 42, 506, 40], [470, 43, 506, 41], [470, 44, 506, 42], [471, 8, 507, 6, "console"], [471, 15, 507, 13], [471, 16, 507, 14, "log"], [471, 19, 507, 17], [471, 20, 507, 18], [471, 68, 507, 66], [471, 70, 507, 68, "result"], [471, 76, 507, 74], [471, 77, 507, 75], [472, 8, 509, 6], [472, 12, 509, 10], [472, 13, 509, 11, "result"], [472, 19, 509, 17], [472, 20, 509, 18, "jobId"], [472, 25, 509, 23], [472, 27, 509, 25], [473, 10, 510, 8], [473, 16, 510, 14], [473, 20, 510, 18, "Error"], [473, 25, 510, 23], [473, 26, 510, 24], [473, 70, 510, 68], [473, 71, 510, 69], [474, 8, 511, 6], [476, 8, 513, 6], [477, 8, 514, 6], [477, 14, 514, 12, "pollForCompletion"], [477, 31, 514, 29], [477, 32, 514, 30, "result"], [477, 38, 514, 36], [477, 39, 514, 37, "jobId"], [477, 44, 514, 42], [477, 46, 514, 44, "timestamp"], [477, 55, 514, 53], [477, 56, 514, 54], [478, 6, 515, 4], [478, 7, 515, 5], [478, 8, 515, 6], [478, 15, 515, 13, "error"], [478, 20, 515, 18], [478, 22, 515, 20], [479, 8, 516, 6, "console"], [479, 15, 516, 13], [479, 16, 516, 14, "error"], [479, 21, 516, 19], [479, 22, 516, 20], [479, 57, 516, 55], [479, 59, 516, 57, "error"], [479, 64, 516, 62], [479, 65, 516, 63], [480, 8, 517, 6, "setErrorMessage"], [480, 23, 517, 21], [480, 24, 517, 22], [480, 52, 517, 50, "error"], [480, 57, 517, 55], [480, 58, 517, 56, "message"], [480, 65, 517, 63], [480, 67, 517, 65], [480, 68, 517, 66], [481, 8, 518, 6, "setProcessingState"], [481, 26, 518, 24], [481, 27, 518, 25], [481, 34, 518, 32], [481, 35, 518, 33], [482, 6, 519, 4], [483, 4, 520, 2], [483, 5, 520, 3], [484, 4, 521, 2], [485, 4, 522, 2], [485, 10, 522, 8, "pollForCompletion"], [485, 27, 522, 25], [485, 30, 522, 28], [485, 36, 522, 28, "pollForCompletion"], [485, 37, 522, 35, "jobId"], [485, 42, 522, 48], [485, 44, 522, 50, "timestamp"], [485, 53, 522, 67], [485, 55, 522, 69, "attempts"], [485, 63, 522, 77], [485, 66, 522, 80], [485, 67, 522, 81], [485, 72, 522, 86], [486, 6, 523, 4], [486, 12, 523, 10, "MAX_ATTEMPTS"], [486, 24, 523, 22], [486, 27, 523, 25], [486, 29, 523, 27], [486, 30, 523, 28], [486, 31, 523, 29], [487, 6, 524, 4], [487, 12, 524, 10, "POLL_INTERVAL"], [487, 25, 524, 23], [487, 28, 524, 26], [487, 32, 524, 30], [487, 33, 524, 31], [487, 34, 524, 32], [489, 6, 526, 4, "console"], [489, 13, 526, 11], [489, 14, 526, 12, "log"], [489, 17, 526, 15], [489, 18, 526, 16], [489, 53, 526, 51, "attempts"], [489, 61, 526, 59], [489, 64, 526, 62], [489, 65, 526, 63], [489, 69, 526, 67, "MAX_ATTEMPTS"], [489, 81, 526, 79], [489, 93, 526, 91, "jobId"], [489, 98, 526, 96], [489, 100, 526, 98], [489, 101, 526, 99], [490, 6, 528, 4], [490, 10, 528, 8, "attempts"], [490, 18, 528, 16], [490, 22, 528, 20, "MAX_ATTEMPTS"], [490, 34, 528, 32], [490, 36, 528, 34], [491, 8, 529, 6, "console"], [491, 15, 529, 13], [491, 16, 529, 14, "error"], [491, 21, 529, 19], [491, 22, 529, 20], [491, 75, 529, 73], [491, 76, 529, 74], [492, 8, 530, 6, "setErrorMessage"], [492, 23, 530, 21], [492, 24, 530, 22], [492, 63, 530, 61], [492, 64, 530, 62], [493, 8, 531, 6, "setProcessingState"], [493, 26, 531, 24], [493, 27, 531, 25], [493, 34, 531, 32], [493, 35, 531, 33], [494, 8, 532, 6], [495, 6, 533, 4], [496, 6, 535, 4], [496, 10, 535, 8], [497, 8, 536, 6], [497, 14, 536, 12, "response"], [497, 22, 536, 20], [497, 25, 536, 23], [497, 31, 536, 29, "fetch"], [497, 36, 536, 34], [497, 37, 536, 35], [497, 40, 536, 38, "API_BASE_URL"], [497, 52, 536, 50], [497, 75, 536, 73, "jobId"], [497, 80, 536, 78], [497, 82, 536, 80], [497, 84, 536, 82], [498, 10, 537, 8, "headers"], [498, 17, 537, 15], [498, 19, 537, 17], [499, 12, 538, 10], [499, 27, 538, 25], [499, 29, 538, 27], [499, 39, 538, 37], [499, 45, 538, 43, "getAuthToken"], [499, 57, 538, 55], [499, 58, 538, 56], [499, 59, 538, 57], [500, 10, 539, 8], [501, 8, 540, 6], [501, 9, 540, 7], [501, 10, 540, 8], [502, 8, 542, 6], [502, 12, 542, 10], [502, 13, 542, 11, "response"], [502, 21, 542, 19], [502, 22, 542, 20, "ok"], [502, 24, 542, 22], [502, 26, 542, 24], [503, 10, 543, 8], [503, 16, 543, 14], [503, 20, 543, 18, "Error"], [503, 25, 543, 23], [503, 26, 543, 24], [503, 34, 543, 32, "response"], [503, 42, 543, 40], [503, 43, 543, 41, "status"], [503, 49, 543, 47], [503, 54, 543, 52, "response"], [503, 62, 543, 60], [503, 63, 543, 61, "statusText"], [503, 73, 543, 71], [503, 75, 543, 73], [503, 76, 543, 74], [504, 8, 544, 6], [505, 8, 546, 6], [505, 14, 546, 12, "status"], [505, 20, 546, 18], [505, 23, 546, 21], [505, 29, 546, 27, "response"], [505, 37, 546, 35], [505, 38, 546, 36, "json"], [505, 42, 546, 40], [505, 43, 546, 41], [505, 44, 546, 42], [506, 8, 547, 6, "console"], [506, 15, 547, 13], [506, 16, 547, 14, "log"], [506, 19, 547, 17], [506, 20, 547, 18], [506, 54, 547, 52], [506, 56, 547, 54, "status"], [506, 62, 547, 60], [506, 63, 547, 61], [507, 8, 549, 6], [507, 12, 549, 10, "status"], [507, 18, 549, 16], [507, 19, 549, 17, "status"], [507, 25, 549, 23], [507, 30, 549, 28], [507, 41, 549, 39], [507, 43, 549, 41], [508, 10, 550, 8, "console"], [508, 17, 550, 15], [508, 18, 550, 16, "log"], [508, 21, 550, 19], [508, 22, 550, 20], [508, 73, 550, 71], [508, 74, 550, 72], [509, 10, 551, 8, "setProcessingProgress"], [509, 31, 551, 29], [509, 32, 551, 30], [509, 35, 551, 33], [509, 36, 551, 34], [510, 10, 552, 8, "setProcessingState"], [510, 28, 552, 26], [510, 29, 552, 27], [510, 40, 552, 38], [510, 41, 552, 39], [511, 10, 553, 8], [512, 10, 554, 8], [512, 16, 554, 14, "result"], [512, 22, 554, 20], [512, 25, 554, 23], [513, 12, 555, 10, "imageUrl"], [513, 20, 555, 18], [513, 22, 555, 20, "status"], [513, 28, 555, 26], [513, 29, 555, 27, "publicUrl"], [513, 38, 555, 36], [514, 12, 555, 38], [515, 12, 556, 10, "localUri"], [515, 20, 556, 18], [515, 22, 556, 20, "capturedPhoto"], [515, 35, 556, 33], [515, 39, 556, 37, "status"], [515, 45, 556, 43], [515, 46, 556, 44, "publicUrl"], [515, 55, 556, 53], [516, 12, 556, 55], [517, 12, 557, 10, "challengeCode"], [517, 25, 557, 23], [517, 27, 557, 25, "challengeCode"], [517, 40, 557, 38], [517, 44, 557, 42], [517, 46, 557, 44], [518, 12, 558, 10, "timestamp"], [518, 21, 558, 19], [519, 12, 559, 10, "processingStatus"], [519, 28, 559, 26], [519, 30, 559, 28], [520, 10, 560, 8], [520, 11, 560, 9], [521, 10, 561, 8, "console"], [521, 17, 561, 15], [521, 18, 561, 16, "log"], [521, 21, 561, 19], [521, 22, 561, 20], [521, 57, 561, 55], [521, 59, 561, 57, "result"], [521, 65, 561, 63], [521, 66, 561, 64], [522, 10, 562, 8, "onComplete"], [522, 20, 562, 18], [522, 21, 562, 19, "result"], [522, 27, 562, 25], [522, 28, 562, 26], [523, 10, 563, 8], [524, 8, 564, 6], [524, 9, 564, 7], [524, 15, 564, 13], [524, 19, 564, 17, "status"], [524, 25, 564, 23], [524, 26, 564, 24, "status"], [524, 32, 564, 30], [524, 37, 564, 35], [524, 45, 564, 43], [524, 47, 564, 45], [525, 10, 565, 8, "console"], [525, 17, 565, 15], [525, 18, 565, 16, "error"], [525, 23, 565, 21], [525, 24, 565, 22], [525, 60, 565, 58], [525, 62, 565, 60, "status"], [525, 68, 565, 66], [525, 69, 565, 67, "error"], [525, 74, 565, 72], [525, 75, 565, 73], [526, 10, 566, 8], [526, 16, 566, 14], [526, 20, 566, 18, "Error"], [526, 25, 566, 23], [526, 26, 566, 24, "status"], [526, 32, 566, 30], [526, 33, 566, 31, "error"], [526, 38, 566, 36], [526, 42, 566, 40], [526, 61, 566, 59], [526, 62, 566, 60], [527, 8, 567, 6], [527, 9, 567, 7], [527, 15, 567, 13], [528, 10, 568, 8], [529, 10, 569, 8], [529, 16, 569, 14, "progressValue"], [529, 29, 569, 27], [529, 32, 569, 30], [529, 34, 569, 32], [529, 37, 569, 36, "attempts"], [529, 45, 569, 44], [529, 48, 569, 47, "MAX_ATTEMPTS"], [529, 60, 569, 59], [529, 63, 569, 63], [529, 65, 569, 65], [530, 10, 570, 8, "console"], [530, 17, 570, 15], [530, 18, 570, 16, "log"], [530, 21, 570, 19], [530, 22, 570, 20], [530, 71, 570, 69, "progressValue"], [530, 84, 570, 82], [530, 87, 570, 85], [530, 88, 570, 86], [531, 10, 571, 8, "setProcessingProgress"], [531, 31, 571, 29], [531, 32, 571, 30, "progressValue"], [531, 45, 571, 43], [531, 46, 571, 44], [532, 10, 573, 8, "setTimeout"], [532, 20, 573, 18], [532, 21, 573, 19], [532, 27, 573, 25], [533, 12, 574, 10, "pollForCompletion"], [533, 29, 574, 27], [533, 30, 574, 28, "jobId"], [533, 35, 574, 33], [533, 37, 574, 35, "timestamp"], [533, 46, 574, 44], [533, 48, 574, 46, "attempts"], [533, 56, 574, 54], [533, 59, 574, 57], [533, 60, 574, 58], [533, 61, 574, 59], [534, 10, 575, 8], [534, 11, 575, 9], [534, 13, 575, 11, "POLL_INTERVAL"], [534, 26, 575, 24], [534, 27, 575, 25], [535, 8, 576, 6], [536, 6, 577, 4], [536, 7, 577, 5], [536, 8, 577, 6], [536, 15, 577, 13, "error"], [536, 20, 577, 18], [536, 22, 577, 20], [537, 8, 578, 6, "console"], [537, 15, 578, 13], [537, 16, 578, 14, "error"], [537, 21, 578, 19], [537, 22, 578, 20], [537, 54, 578, 52], [537, 56, 578, 54, "error"], [537, 61, 578, 59], [537, 62, 578, 60], [538, 8, 579, 6, "setErrorMessage"], [538, 23, 579, 21], [538, 24, 579, 22], [538, 62, 579, 60, "error"], [538, 67, 579, 65], [538, 68, 579, 66, "message"], [538, 75, 579, 73], [538, 77, 579, 75], [538, 78, 579, 76], [539, 8, 580, 6, "setProcessingState"], [539, 26, 580, 24], [539, 27, 580, 25], [539, 34, 580, 32], [539, 35, 580, 33], [540, 6, 581, 4], [541, 4, 582, 2], [541, 5, 582, 3], [542, 4, 583, 2], [543, 4, 584, 2], [543, 10, 584, 8, "getAuthToken"], [543, 22, 584, 20], [543, 25, 584, 23], [543, 31, 584, 23, "getAuthToken"], [543, 32, 584, 23], [543, 37, 584, 52], [544, 6, 585, 4], [545, 6, 586, 4], [546, 6, 587, 4], [546, 13, 587, 11], [546, 30, 587, 28], [547, 4, 588, 2], [547, 5, 588, 3], [549, 4, 590, 2], [550, 4, 591, 2], [550, 10, 591, 8, "retryCapture"], [550, 22, 591, 20], [550, 25, 591, 23], [550, 29, 591, 23, "useCallback"], [550, 47, 591, 34], [550, 49, 591, 35], [550, 55, 591, 41], [551, 6, 592, 4, "console"], [551, 13, 592, 11], [551, 14, 592, 12, "log"], [551, 17, 592, 15], [551, 18, 592, 16], [551, 55, 592, 53], [551, 56, 592, 54], [552, 6, 593, 4, "setProcessingState"], [552, 24, 593, 22], [552, 25, 593, 23], [552, 31, 593, 29], [552, 32, 593, 30], [553, 6, 594, 4, "setErrorMessage"], [553, 21, 594, 19], [553, 22, 594, 20], [553, 24, 594, 22], [553, 25, 594, 23], [554, 6, 595, 4, "setCapturedPhoto"], [554, 22, 595, 20], [554, 23, 595, 21], [554, 25, 595, 23], [554, 26, 595, 24], [555, 6, 596, 4, "setProcessingProgress"], [555, 27, 596, 25], [555, 28, 596, 26], [555, 29, 596, 27], [555, 30, 596, 28], [556, 4, 597, 2], [556, 5, 597, 3], [556, 7, 597, 5], [556, 9, 597, 7], [556, 10, 597, 8], [557, 4, 598, 2], [558, 4, 599, 2], [558, 8, 599, 2, "useEffect"], [558, 24, 599, 11], [558, 26, 599, 12], [558, 32, 599, 18], [559, 6, 600, 4, "console"], [559, 13, 600, 11], [559, 14, 600, 12, "log"], [559, 17, 600, 15], [559, 18, 600, 16], [559, 53, 600, 51], [559, 55, 600, 53, "permission"], [559, 65, 600, 63], [559, 66, 600, 64], [560, 6, 601, 4], [560, 10, 601, 8, "permission"], [560, 20, 601, 18], [560, 22, 601, 20], [561, 8, 602, 6, "console"], [561, 15, 602, 13], [561, 16, 602, 14, "log"], [561, 19, 602, 17], [561, 20, 602, 18], [561, 57, 602, 55], [561, 59, 602, 57, "permission"], [561, 69, 602, 67], [561, 70, 602, 68, "granted"], [561, 77, 602, 75], [561, 78, 602, 76], [562, 6, 603, 4], [563, 4, 604, 2], [563, 5, 604, 3], [563, 7, 604, 5], [563, 8, 604, 6, "permission"], [563, 18, 604, 16], [563, 19, 604, 17], [563, 20, 604, 18], [564, 4, 605, 2], [565, 4, 606, 2], [565, 8, 606, 6], [565, 9, 606, 7, "permission"], [565, 19, 606, 17], [565, 21, 606, 19], [566, 6, 607, 4, "console"], [566, 13, 607, 11], [566, 14, 607, 12, "log"], [566, 17, 607, 15], [566, 18, 607, 16], [566, 67, 607, 65], [566, 68, 607, 66], [567, 6, 608, 4], [567, 26, 609, 6], [567, 30, 609, 6, "_jsxDevRuntime"], [567, 44, 609, 6], [567, 45, 609, 6, "jsxDEV"], [567, 51, 609, 6], [567, 53, 609, 7, "_View"], [567, 58, 609, 7], [567, 59, 609, 7, "default"], [567, 66, 609, 11], [568, 8, 609, 12, "style"], [568, 13, 609, 17], [568, 15, 609, 19, "styles"], [568, 21, 609, 25], [568, 22, 609, 26, "container"], [568, 31, 609, 36], [569, 8, 609, 36, "children"], [569, 16, 609, 36], [569, 32, 610, 8], [569, 36, 610, 8, "_jsxDevRuntime"], [569, 50, 610, 8], [569, 51, 610, 8, "jsxDEV"], [569, 57, 610, 8], [569, 59, 610, 9, "_ActivityIndicator"], [569, 77, 610, 9], [569, 78, 610, 9, "default"], [569, 85, 610, 26], [570, 10, 610, 27, "size"], [570, 14, 610, 31], [570, 16, 610, 32], [570, 23, 610, 39], [571, 10, 610, 40, "color"], [571, 15, 610, 45], [571, 17, 610, 46], [572, 8, 610, 55], [573, 10, 610, 55, "fileName"], [573, 18, 610, 55], [573, 20, 610, 55, "_jsxFileName"], [573, 32, 610, 55], [574, 10, 610, 55, "lineNumber"], [574, 20, 610, 55], [575, 10, 610, 55, "columnNumber"], [575, 22, 610, 55], [576, 8, 610, 55], [576, 15, 610, 57], [576, 16, 610, 58], [576, 31, 611, 8], [576, 35, 611, 8, "_jsxDevRuntime"], [576, 49, 611, 8], [576, 50, 611, 8, "jsxDEV"], [576, 56, 611, 8], [576, 58, 611, 9, "_Text"], [576, 63, 611, 9], [576, 64, 611, 9, "default"], [576, 71, 611, 13], [577, 10, 611, 14, "style"], [577, 15, 611, 19], [577, 17, 611, 21, "styles"], [577, 23, 611, 27], [577, 24, 611, 28, "loadingText"], [577, 35, 611, 40], [578, 10, 611, 40, "children"], [578, 18, 611, 40], [578, 20, 611, 41], [579, 8, 611, 58], [580, 10, 611, 58, "fileName"], [580, 18, 611, 58], [580, 20, 611, 58, "_jsxFileName"], [580, 32, 611, 58], [581, 10, 611, 58, "lineNumber"], [581, 20, 611, 58], [582, 10, 611, 58, "columnNumber"], [582, 22, 611, 58], [583, 8, 611, 58], [583, 15, 611, 64], [583, 16, 611, 65], [584, 6, 611, 65], [585, 8, 611, 65, "fileName"], [585, 16, 611, 65], [585, 18, 611, 65, "_jsxFileName"], [585, 30, 611, 65], [586, 8, 611, 65, "lineNumber"], [586, 18, 611, 65], [587, 8, 611, 65, "columnNumber"], [587, 20, 611, 65], [588, 6, 611, 65], [588, 13, 612, 12], [588, 14, 612, 13], [589, 4, 614, 2], [590, 4, 615, 2], [590, 8, 615, 6], [590, 9, 615, 7, "permission"], [590, 19, 615, 17], [590, 20, 615, 18, "granted"], [590, 27, 615, 25], [590, 29, 615, 27], [591, 6, 616, 4, "console"], [591, 13, 616, 11], [591, 14, 616, 12, "log"], [591, 17, 616, 15], [591, 18, 616, 16], [591, 93, 616, 91], [591, 94, 616, 92], [592, 6, 617, 4], [592, 26, 618, 6], [592, 30, 618, 6, "_jsxDevRuntime"], [592, 44, 618, 6], [592, 45, 618, 6, "jsxDEV"], [592, 51, 618, 6], [592, 53, 618, 7, "_View"], [592, 58, 618, 7], [592, 59, 618, 7, "default"], [592, 66, 618, 11], [593, 8, 618, 12, "style"], [593, 13, 618, 17], [593, 15, 618, 19, "styles"], [593, 21, 618, 25], [593, 22, 618, 26, "container"], [593, 31, 618, 36], [594, 8, 618, 36, "children"], [594, 16, 618, 36], [594, 31, 619, 8], [594, 35, 619, 8, "_jsxDevRuntime"], [594, 49, 619, 8], [594, 50, 619, 8, "jsxDEV"], [594, 56, 619, 8], [594, 58, 619, 9, "_View"], [594, 63, 619, 9], [594, 64, 619, 9, "default"], [594, 71, 619, 13], [595, 10, 619, 14, "style"], [595, 15, 619, 19], [595, 17, 619, 21, "styles"], [595, 23, 619, 27], [595, 24, 619, 28, "permissionContent"], [595, 41, 619, 46], [596, 10, 619, 46, "children"], [596, 18, 619, 46], [596, 34, 620, 10], [596, 38, 620, 10, "_jsxDevRuntime"], [596, 52, 620, 10], [596, 53, 620, 10, "jsxDEV"], [596, 59, 620, 10], [596, 61, 620, 11, "_lucideReactNative"], [596, 79, 620, 11], [596, 80, 620, 11, "Camera"], [596, 86, 620, 21], [597, 12, 620, 22, "size"], [597, 16, 620, 26], [597, 18, 620, 28], [597, 20, 620, 31], [598, 12, 620, 32, "color"], [598, 17, 620, 37], [598, 19, 620, 38], [599, 10, 620, 47], [600, 12, 620, 47, "fileName"], [600, 20, 620, 47], [600, 22, 620, 47, "_jsxFileName"], [600, 34, 620, 47], [601, 12, 620, 47, "lineNumber"], [601, 22, 620, 47], [602, 12, 620, 47, "columnNumber"], [602, 24, 620, 47], [603, 10, 620, 47], [603, 17, 620, 49], [603, 18, 620, 50], [603, 33, 621, 10], [603, 37, 621, 10, "_jsxDevRuntime"], [603, 51, 621, 10], [603, 52, 621, 10, "jsxDEV"], [603, 58, 621, 10], [603, 60, 621, 11, "_Text"], [603, 65, 621, 11], [603, 66, 621, 11, "default"], [603, 73, 621, 15], [604, 12, 621, 16, "style"], [604, 17, 621, 21], [604, 19, 621, 23, "styles"], [604, 25, 621, 29], [604, 26, 621, 30, "permissionTitle"], [604, 41, 621, 46], [605, 12, 621, 46, "children"], [605, 20, 621, 46], [605, 22, 621, 47], [606, 10, 621, 73], [607, 12, 621, 73, "fileName"], [607, 20, 621, 73], [607, 22, 621, 73, "_jsxFileName"], [607, 34, 621, 73], [608, 12, 621, 73, "lineNumber"], [608, 22, 621, 73], [609, 12, 621, 73, "columnNumber"], [609, 24, 621, 73], [610, 10, 621, 73], [610, 17, 621, 79], [610, 18, 621, 80], [610, 33, 622, 10], [610, 37, 622, 10, "_jsxDevRuntime"], [610, 51, 622, 10], [610, 52, 622, 10, "jsxDEV"], [610, 58, 622, 10], [610, 60, 622, 11, "_Text"], [610, 65, 622, 11], [610, 66, 622, 11, "default"], [610, 73, 622, 15], [611, 12, 622, 16, "style"], [611, 17, 622, 21], [611, 19, 622, 23, "styles"], [611, 25, 622, 29], [611, 26, 622, 30, "permissionDescription"], [611, 47, 622, 52], [612, 12, 622, 52, "children"], [612, 20, 622, 52], [612, 22, 622, 53], [613, 10, 625, 10], [614, 12, 625, 10, "fileName"], [614, 20, 625, 10], [614, 22, 625, 10, "_jsxFileName"], [614, 34, 625, 10], [615, 12, 625, 10, "lineNumber"], [615, 22, 625, 10], [616, 12, 625, 10, "columnNumber"], [616, 24, 625, 10], [617, 10, 625, 10], [617, 17, 625, 16], [617, 18, 625, 17], [617, 33, 626, 10], [617, 37, 626, 10, "_jsxDevRuntime"], [617, 51, 626, 10], [617, 52, 626, 10, "jsxDEV"], [617, 58, 626, 10], [617, 60, 626, 11, "_TouchableOpacity"], [617, 77, 626, 11], [617, 78, 626, 11, "default"], [617, 85, 626, 27], [618, 12, 626, 28, "onPress"], [618, 19, 626, 35], [618, 21, 626, 37, "requestPermission"], [618, 38, 626, 55], [619, 12, 626, 56, "style"], [619, 17, 626, 61], [619, 19, 626, 63, "styles"], [619, 25, 626, 69], [619, 26, 626, 70, "primaryButton"], [619, 39, 626, 84], [620, 12, 626, 84, "children"], [620, 20, 626, 84], [620, 35, 627, 12], [620, 39, 627, 12, "_jsxDevRuntime"], [620, 53, 627, 12], [620, 54, 627, 12, "jsxDEV"], [620, 60, 627, 12], [620, 62, 627, 13, "_Text"], [620, 67, 627, 13], [620, 68, 627, 13, "default"], [620, 75, 627, 17], [621, 14, 627, 18, "style"], [621, 19, 627, 23], [621, 21, 627, 25, "styles"], [621, 27, 627, 31], [621, 28, 627, 32, "primaryButtonText"], [621, 45, 627, 50], [622, 14, 627, 50, "children"], [622, 22, 627, 50], [622, 24, 627, 51], [623, 12, 627, 67], [624, 14, 627, 67, "fileName"], [624, 22, 627, 67], [624, 24, 627, 67, "_jsxFileName"], [624, 36, 627, 67], [625, 14, 627, 67, "lineNumber"], [625, 24, 627, 67], [626, 14, 627, 67, "columnNumber"], [626, 26, 627, 67], [627, 12, 627, 67], [627, 19, 627, 73], [628, 10, 627, 74], [629, 12, 627, 74, "fileName"], [629, 20, 627, 74], [629, 22, 627, 74, "_jsxFileName"], [629, 34, 627, 74], [630, 12, 627, 74, "lineNumber"], [630, 22, 627, 74], [631, 12, 627, 74, "columnNumber"], [631, 24, 627, 74], [632, 10, 627, 74], [632, 17, 628, 28], [632, 18, 628, 29], [632, 33, 629, 10], [632, 37, 629, 10, "_jsxDevRuntime"], [632, 51, 629, 10], [632, 52, 629, 10, "jsxDEV"], [632, 58, 629, 10], [632, 60, 629, 11, "_TouchableOpacity"], [632, 77, 629, 11], [632, 78, 629, 11, "default"], [632, 85, 629, 27], [633, 12, 629, 28, "onPress"], [633, 19, 629, 35], [633, 21, 629, 37, "onCancel"], [633, 29, 629, 46], [634, 12, 629, 47, "style"], [634, 17, 629, 52], [634, 19, 629, 54, "styles"], [634, 25, 629, 60], [634, 26, 629, 61, "secondaryButton"], [634, 41, 629, 77], [635, 12, 629, 77, "children"], [635, 20, 629, 77], [635, 35, 630, 12], [635, 39, 630, 12, "_jsxDevRuntime"], [635, 53, 630, 12], [635, 54, 630, 12, "jsxDEV"], [635, 60, 630, 12], [635, 62, 630, 13, "_Text"], [635, 67, 630, 13], [635, 68, 630, 13, "default"], [635, 75, 630, 17], [636, 14, 630, 18, "style"], [636, 19, 630, 23], [636, 21, 630, 25, "styles"], [636, 27, 630, 31], [636, 28, 630, 32, "secondaryButtonText"], [636, 47, 630, 52], [637, 14, 630, 52, "children"], [637, 22, 630, 52], [637, 24, 630, 53], [638, 12, 630, 59], [639, 14, 630, 59, "fileName"], [639, 22, 630, 59], [639, 24, 630, 59, "_jsxFileName"], [639, 36, 630, 59], [640, 14, 630, 59, "lineNumber"], [640, 24, 630, 59], [641, 14, 630, 59, "columnNumber"], [641, 26, 630, 59], [642, 12, 630, 59], [642, 19, 630, 65], [643, 10, 630, 66], [644, 12, 630, 66, "fileName"], [644, 20, 630, 66], [644, 22, 630, 66, "_jsxFileName"], [644, 34, 630, 66], [645, 12, 630, 66, "lineNumber"], [645, 22, 630, 66], [646, 12, 630, 66, "columnNumber"], [646, 24, 630, 66], [647, 10, 630, 66], [647, 17, 631, 28], [647, 18, 631, 29], [648, 8, 631, 29], [649, 10, 631, 29, "fileName"], [649, 18, 631, 29], [649, 20, 631, 29, "_jsxFileName"], [649, 32, 631, 29], [650, 10, 631, 29, "lineNumber"], [650, 20, 631, 29], [651, 10, 631, 29, "columnNumber"], [651, 22, 631, 29], [652, 8, 631, 29], [652, 15, 632, 14], [653, 6, 632, 15], [654, 8, 632, 15, "fileName"], [654, 16, 632, 15], [654, 18, 632, 15, "_jsxFileName"], [654, 30, 632, 15], [655, 8, 632, 15, "lineNumber"], [655, 18, 632, 15], [656, 8, 632, 15, "columnNumber"], [656, 20, 632, 15], [657, 6, 632, 15], [657, 13, 633, 12], [657, 14, 633, 13], [658, 4, 635, 2], [659, 4, 636, 2], [660, 4, 637, 2, "console"], [660, 11, 637, 9], [660, 12, 637, 10, "log"], [660, 15, 637, 13], [660, 16, 637, 14], [660, 55, 637, 53], [660, 56, 637, 54], [661, 4, 639, 2], [661, 24, 640, 4], [661, 28, 640, 4, "_jsxDevRuntime"], [661, 42, 640, 4], [661, 43, 640, 4, "jsxDEV"], [661, 49, 640, 4], [661, 51, 640, 5, "_View"], [661, 56, 640, 5], [661, 57, 640, 5, "default"], [661, 64, 640, 9], [662, 6, 640, 10, "style"], [662, 11, 640, 15], [662, 13, 640, 17, "styles"], [662, 19, 640, 23], [662, 20, 640, 24, "container"], [662, 29, 640, 34], [663, 6, 640, 34, "children"], [663, 14, 640, 34], [663, 30, 642, 6], [663, 34, 642, 6, "_jsxDevRuntime"], [663, 48, 642, 6], [663, 49, 642, 6, "jsxDEV"], [663, 55, 642, 6], [663, 57, 642, 7, "_View"], [663, 62, 642, 7], [663, 63, 642, 7, "default"], [663, 70, 642, 11], [664, 8, 642, 12, "style"], [664, 13, 642, 17], [664, 15, 642, 19, "styles"], [664, 21, 642, 25], [664, 22, 642, 26, "cameraContainer"], [664, 37, 642, 42], [665, 8, 642, 43, "id"], [665, 10, 642, 45], [665, 12, 642, 46], [665, 29, 642, 63], [666, 8, 642, 63, "children"], [666, 16, 642, 63], [666, 32, 643, 8], [666, 36, 643, 8, "_jsxDevRuntime"], [666, 50, 643, 8], [666, 51, 643, 8, "jsxDEV"], [666, 57, 643, 8], [666, 59, 643, 9, "_expoCamera"], [666, 70, 643, 9], [666, 71, 643, 9, "CameraView"], [666, 81, 643, 19], [667, 10, 644, 10, "ref"], [667, 13, 644, 13], [667, 15, 644, 15, "cameraRef"], [667, 24, 644, 25], [668, 10, 645, 10, "style"], [668, 15, 645, 15], [668, 17, 645, 17], [668, 18, 645, 18, "styles"], [668, 24, 645, 24], [668, 25, 645, 25, "camera"], [668, 31, 645, 31], [668, 33, 645, 33], [669, 12, 645, 35, "backgroundColor"], [669, 27, 645, 50], [669, 29, 645, 52], [670, 10, 645, 62], [670, 11, 645, 63], [670, 12, 645, 65], [671, 10, 646, 10, "facing"], [671, 16, 646, 16], [671, 18, 646, 17], [671, 24, 646, 23], [672, 10, 647, 10, "onLayout"], [672, 18, 647, 18], [672, 20, 647, 21, "e"], [672, 21, 647, 22], [672, 25, 647, 27], [673, 12, 648, 12, "console"], [673, 19, 648, 19], [673, 20, 648, 20, "log"], [673, 23, 648, 23], [673, 24, 648, 24], [673, 56, 648, 56], [673, 58, 648, 58, "e"], [673, 59, 648, 59], [673, 60, 648, 60, "nativeEvent"], [673, 71, 648, 71], [673, 72, 648, 72, "layout"], [673, 78, 648, 78], [673, 79, 648, 79], [674, 12, 649, 12, "setViewSize"], [674, 23, 649, 23], [674, 24, 649, 24], [675, 14, 649, 26, "width"], [675, 19, 649, 31], [675, 21, 649, 33, "e"], [675, 22, 649, 34], [675, 23, 649, 35, "nativeEvent"], [675, 34, 649, 46], [675, 35, 649, 47, "layout"], [675, 41, 649, 53], [675, 42, 649, 54, "width"], [675, 47, 649, 59], [676, 14, 649, 61, "height"], [676, 20, 649, 67], [676, 22, 649, 69, "e"], [676, 23, 649, 70], [676, 24, 649, 71, "nativeEvent"], [676, 35, 649, 82], [676, 36, 649, 83, "layout"], [676, 42, 649, 89], [676, 43, 649, 90, "height"], [677, 12, 649, 97], [677, 13, 649, 98], [677, 14, 649, 99], [678, 10, 650, 10], [678, 11, 650, 12], [679, 10, 651, 10, "onCameraReady"], [679, 23, 651, 23], [679, 25, 651, 25, "onCameraReady"], [679, 26, 651, 25], [679, 31, 651, 31], [680, 12, 652, 12, "console"], [680, 19, 652, 19], [680, 20, 652, 20, "log"], [680, 23, 652, 23], [680, 24, 652, 24], [680, 55, 652, 55], [680, 56, 652, 56], [681, 12, 653, 12, "setIsCameraReady"], [681, 28, 653, 28], [681, 29, 653, 29], [681, 33, 653, 33], [681, 34, 653, 34], [681, 35, 653, 35], [681, 36, 653, 36], [682, 10, 654, 10], [682, 11, 654, 12], [683, 10, 655, 10, "onMountError"], [683, 22, 655, 22], [683, 24, 655, 25, "error"], [683, 29, 655, 30], [683, 33, 655, 35], [684, 12, 656, 12, "console"], [684, 19, 656, 19], [684, 20, 656, 20, "error"], [684, 25, 656, 25], [684, 26, 656, 26], [684, 63, 656, 63], [684, 65, 656, 65, "error"], [684, 70, 656, 70], [684, 71, 656, 71], [685, 12, 657, 12, "setErrorMessage"], [685, 27, 657, 27], [685, 28, 657, 28], [685, 57, 657, 57], [685, 58, 657, 58], [686, 12, 658, 12, "setProcessingState"], [686, 30, 658, 30], [686, 31, 658, 31], [686, 38, 658, 38], [686, 39, 658, 39], [687, 10, 659, 10], [688, 8, 659, 12], [689, 10, 659, 12, "fileName"], [689, 18, 659, 12], [689, 20, 659, 12, "_jsxFileName"], [689, 32, 659, 12], [690, 10, 659, 12, "lineNumber"], [690, 20, 659, 12], [691, 10, 659, 12, "columnNumber"], [691, 22, 659, 12], [692, 8, 659, 12], [692, 15, 660, 9], [692, 16, 660, 10], [692, 18, 662, 9], [692, 19, 662, 10, "isCameraReady"], [692, 32, 662, 23], [692, 49, 663, 10], [692, 53, 663, 10, "_jsxDevRuntime"], [692, 67, 663, 10], [692, 68, 663, 10, "jsxDEV"], [692, 74, 663, 10], [692, 76, 663, 11, "_View"], [692, 81, 663, 11], [692, 82, 663, 11, "default"], [692, 89, 663, 15], [693, 10, 663, 16, "style"], [693, 15, 663, 21], [693, 17, 663, 23], [693, 18, 663, 24, "StyleSheet"], [693, 37, 663, 34], [693, 38, 663, 35, "absoluteFill"], [693, 50, 663, 47], [693, 52, 663, 49], [694, 12, 663, 51, "backgroundColor"], [694, 27, 663, 66], [694, 29, 663, 68], [694, 49, 663, 88], [695, 12, 663, 90, "justifyContent"], [695, 26, 663, 104], [695, 28, 663, 106], [695, 36, 663, 114], [696, 12, 663, 116, "alignItems"], [696, 22, 663, 126], [696, 24, 663, 128], [696, 32, 663, 136], [697, 12, 663, 138, "zIndex"], [697, 18, 663, 144], [697, 20, 663, 146], [698, 10, 663, 151], [698, 11, 663, 152], [698, 12, 663, 154], [699, 10, 663, 154, "children"], [699, 18, 663, 154], [699, 33, 664, 12], [699, 37, 664, 12, "_jsxDevRuntime"], [699, 51, 664, 12], [699, 52, 664, 12, "jsxDEV"], [699, 58, 664, 12], [699, 60, 664, 13, "_View"], [699, 65, 664, 13], [699, 66, 664, 13, "default"], [699, 73, 664, 17], [700, 12, 664, 18, "style"], [700, 17, 664, 23], [700, 19, 664, 25], [701, 14, 664, 27, "backgroundColor"], [701, 29, 664, 42], [701, 31, 664, 44], [701, 51, 664, 64], [702, 14, 664, 66, "padding"], [702, 21, 664, 73], [702, 23, 664, 75], [702, 25, 664, 77], [703, 14, 664, 79, "borderRadius"], [703, 26, 664, 91], [703, 28, 664, 93], [703, 30, 664, 95], [704, 14, 664, 97, "alignItems"], [704, 24, 664, 107], [704, 26, 664, 109], [705, 12, 664, 118], [705, 13, 664, 120], [706, 12, 664, 120, "children"], [706, 20, 664, 120], [706, 36, 665, 14], [706, 40, 665, 14, "_jsxDevRuntime"], [706, 54, 665, 14], [706, 55, 665, 14, "jsxDEV"], [706, 61, 665, 14], [706, 63, 665, 15, "_ActivityIndicator"], [706, 81, 665, 15], [706, 82, 665, 15, "default"], [706, 89, 665, 32], [707, 14, 665, 33, "size"], [707, 18, 665, 37], [707, 20, 665, 38], [707, 27, 665, 45], [708, 14, 665, 46, "color"], [708, 19, 665, 51], [708, 21, 665, 52], [708, 30, 665, 61], [709, 14, 665, 62, "style"], [709, 19, 665, 67], [709, 21, 665, 69], [710, 16, 665, 71, "marginBottom"], [710, 28, 665, 83], [710, 30, 665, 85], [711, 14, 665, 88], [712, 12, 665, 90], [713, 14, 665, 90, "fileName"], [713, 22, 665, 90], [713, 24, 665, 90, "_jsxFileName"], [713, 36, 665, 90], [714, 14, 665, 90, "lineNumber"], [714, 24, 665, 90], [715, 14, 665, 90, "columnNumber"], [715, 26, 665, 90], [716, 12, 665, 90], [716, 19, 665, 92], [716, 20, 665, 93], [716, 35, 666, 14], [716, 39, 666, 14, "_jsxDevRuntime"], [716, 53, 666, 14], [716, 54, 666, 14, "jsxDEV"], [716, 60, 666, 14], [716, 62, 666, 15, "_Text"], [716, 67, 666, 15], [716, 68, 666, 15, "default"], [716, 75, 666, 19], [717, 14, 666, 20, "style"], [717, 19, 666, 25], [717, 21, 666, 27], [718, 16, 666, 29, "color"], [718, 21, 666, 34], [718, 23, 666, 36], [718, 29, 666, 42], [719, 16, 666, 44, "fontSize"], [719, 24, 666, 52], [719, 26, 666, 54], [719, 28, 666, 56], [720, 16, 666, 58, "fontWeight"], [720, 26, 666, 68], [720, 28, 666, 70], [721, 14, 666, 76], [721, 15, 666, 78], [722, 14, 666, 78, "children"], [722, 22, 666, 78], [722, 24, 666, 79], [723, 12, 666, 101], [724, 14, 666, 101, "fileName"], [724, 22, 666, 101], [724, 24, 666, 101, "_jsxFileName"], [724, 36, 666, 101], [725, 14, 666, 101, "lineNumber"], [725, 24, 666, 101], [726, 14, 666, 101, "columnNumber"], [726, 26, 666, 101], [727, 12, 666, 101], [727, 19, 666, 107], [727, 20, 666, 108], [727, 35, 667, 14], [727, 39, 667, 14, "_jsxDevRuntime"], [727, 53, 667, 14], [727, 54, 667, 14, "jsxDEV"], [727, 60, 667, 14], [727, 62, 667, 15, "_Text"], [727, 67, 667, 15], [727, 68, 667, 15, "default"], [727, 75, 667, 19], [728, 14, 667, 20, "style"], [728, 19, 667, 25], [728, 21, 667, 27], [729, 16, 667, 29, "color"], [729, 21, 667, 34], [729, 23, 667, 36], [729, 32, 667, 45], [730, 16, 667, 47, "fontSize"], [730, 24, 667, 55], [730, 26, 667, 57], [730, 28, 667, 59], [731, 16, 667, 61, "marginTop"], [731, 25, 667, 70], [731, 27, 667, 72], [732, 14, 667, 74], [732, 15, 667, 76], [733, 14, 667, 76, "children"], [733, 22, 667, 76], [733, 24, 667, 77], [734, 12, 667, 88], [735, 14, 667, 88, "fileName"], [735, 22, 667, 88], [735, 24, 667, 88, "_jsxFileName"], [735, 36, 667, 88], [736, 14, 667, 88, "lineNumber"], [736, 24, 667, 88], [737, 14, 667, 88, "columnNumber"], [737, 26, 667, 88], [738, 12, 667, 88], [738, 19, 667, 94], [738, 20, 667, 95], [739, 10, 667, 95], [740, 12, 667, 95, "fileName"], [740, 20, 667, 95], [740, 22, 667, 95, "_jsxFileName"], [740, 34, 667, 95], [741, 12, 667, 95, "lineNumber"], [741, 22, 667, 95], [742, 12, 667, 95, "columnNumber"], [742, 24, 667, 95], [743, 10, 667, 95], [743, 17, 668, 18], [744, 8, 668, 19], [745, 10, 668, 19, "fileName"], [745, 18, 668, 19], [745, 20, 668, 19, "_jsxFileName"], [745, 32, 668, 19], [746, 10, 668, 19, "lineNumber"], [746, 20, 668, 19], [747, 10, 668, 19, "columnNumber"], [747, 22, 668, 19], [748, 8, 668, 19], [748, 15, 669, 16], [748, 16, 670, 9], [748, 18, 673, 9, "isCameraReady"], [748, 31, 673, 22], [748, 35, 673, 26, "previewBlurEnabled"], [748, 53, 673, 44], [748, 57, 673, 48, "viewSize"], [748, 65, 673, 56], [748, 66, 673, 57, "width"], [748, 71, 673, 62], [748, 74, 673, 65], [748, 75, 673, 66], [748, 92, 674, 10], [748, 96, 674, 10, "_jsxDevRuntime"], [748, 110, 674, 10], [748, 111, 674, 10, "jsxDEV"], [748, 117, 674, 10], [748, 119, 674, 10, "_jsxDevRuntime"], [748, 133, 674, 10], [748, 134, 674, 10, "Fragment"], [748, 142, 674, 10], [749, 10, 674, 10, "children"], [749, 18, 674, 10], [749, 34, 676, 12], [749, 38, 676, 12, "_jsxDevRuntime"], [749, 52, 676, 12], [749, 53, 676, 12, "jsxDEV"], [749, 59, 676, 12], [749, 61, 676, 13, "_LiveFaceCanvas"], [749, 76, 676, 13], [749, 77, 676, 13, "default"], [749, 84, 676, 27], [750, 12, 676, 28, "containerId"], [750, 23, 676, 39], [750, 25, 676, 40], [750, 42, 676, 57], [751, 12, 676, 58, "width"], [751, 17, 676, 63], [751, 19, 676, 65, "viewSize"], [751, 27, 676, 73], [751, 28, 676, 74, "width"], [751, 33, 676, 80], [752, 12, 676, 81, "height"], [752, 18, 676, 87], [752, 20, 676, 89, "viewSize"], [752, 28, 676, 97], [752, 29, 676, 98, "height"], [753, 10, 676, 105], [754, 12, 676, 105, "fileName"], [754, 20, 676, 105], [754, 22, 676, 105, "_jsxFileName"], [754, 34, 676, 105], [755, 12, 676, 105, "lineNumber"], [755, 22, 676, 105], [756, 12, 676, 105, "columnNumber"], [756, 24, 676, 105], [757, 10, 676, 105], [757, 17, 676, 107], [757, 18, 676, 108], [757, 33, 677, 12], [757, 37, 677, 12, "_jsxDevRuntime"], [757, 51, 677, 12], [757, 52, 677, 12, "jsxDEV"], [757, 58, 677, 12], [757, 60, 677, 13, "_View"], [757, 65, 677, 13], [757, 66, 677, 13, "default"], [757, 73, 677, 17], [758, 12, 677, 18, "style"], [758, 17, 677, 23], [758, 19, 677, 25], [758, 20, 677, 26, "StyleSheet"], [758, 39, 677, 36], [758, 40, 677, 37, "absoluteFill"], [758, 52, 677, 49], [758, 54, 677, 51], [759, 14, 677, 53, "pointerEvents"], [759, 27, 677, 66], [759, 29, 677, 68], [760, 12, 677, 75], [760, 13, 677, 76], [760, 14, 677, 78], [761, 12, 677, 78, "children"], [761, 20, 677, 78], [761, 36, 679, 12], [761, 40, 679, 12, "_jsxDevRuntime"], [761, 54, 679, 12], [761, 55, 679, 12, "jsxDEV"], [761, 61, 679, 12], [761, 63, 679, 13, "_expoBlur"], [761, 72, 679, 13], [761, 73, 679, 13, "BlurView"], [761, 81, 679, 21], [762, 14, 679, 22, "intensity"], [762, 23, 679, 31], [762, 25, 679, 33], [762, 27, 679, 36], [763, 14, 679, 37, "tint"], [763, 18, 679, 41], [763, 20, 679, 42], [763, 26, 679, 48], [764, 14, 679, 49, "style"], [764, 19, 679, 54], [764, 21, 679, 56], [764, 22, 679, 57, "styles"], [764, 28, 679, 63], [764, 29, 679, 64, "blurZone"], [764, 37, 679, 72], [764, 39, 679, 74], [765, 16, 680, 14, "left"], [765, 20, 680, 18], [765, 22, 680, 20], [765, 23, 680, 21], [766, 16, 681, 14, "top"], [766, 19, 681, 17], [766, 21, 681, 19, "viewSize"], [766, 29, 681, 27], [766, 30, 681, 28, "height"], [766, 36, 681, 34], [766, 39, 681, 37], [766, 42, 681, 40], [767, 16, 682, 14, "width"], [767, 21, 682, 19], [767, 23, 682, 21, "viewSize"], [767, 31, 682, 29], [767, 32, 682, 30, "width"], [767, 37, 682, 35], [768, 16, 683, 14, "height"], [768, 22, 683, 20], [768, 24, 683, 22, "viewSize"], [768, 32, 683, 30], [768, 33, 683, 31, "height"], [768, 39, 683, 37], [768, 42, 683, 40], [768, 46, 683, 44], [769, 16, 684, 14, "borderRadius"], [769, 28, 684, 26], [769, 30, 684, 28], [770, 14, 685, 12], [770, 15, 685, 13], [771, 12, 685, 15], [772, 14, 685, 15, "fileName"], [772, 22, 685, 15], [772, 24, 685, 15, "_jsxFileName"], [772, 36, 685, 15], [773, 14, 685, 15, "lineNumber"], [773, 24, 685, 15], [774, 14, 685, 15, "columnNumber"], [774, 26, 685, 15], [775, 12, 685, 15], [775, 19, 685, 17], [775, 20, 685, 18], [775, 35, 687, 12], [775, 39, 687, 12, "_jsxDevRuntime"], [775, 53, 687, 12], [775, 54, 687, 12, "jsxDEV"], [775, 60, 687, 12], [775, 62, 687, 13, "_expoBlur"], [775, 71, 687, 13], [775, 72, 687, 13, "BlurView"], [775, 80, 687, 21], [776, 14, 687, 22, "intensity"], [776, 23, 687, 31], [776, 25, 687, 33], [776, 27, 687, 36], [777, 14, 687, 37, "tint"], [777, 18, 687, 41], [777, 20, 687, 42], [777, 26, 687, 48], [778, 14, 687, 49, "style"], [778, 19, 687, 54], [778, 21, 687, 56], [778, 22, 687, 57, "styles"], [778, 28, 687, 63], [778, 29, 687, 64, "blurZone"], [778, 37, 687, 72], [778, 39, 687, 74], [779, 16, 688, 14, "left"], [779, 20, 688, 18], [779, 22, 688, 20], [779, 23, 688, 21], [780, 16, 689, 14, "top"], [780, 19, 689, 17], [780, 21, 689, 19], [780, 22, 689, 20], [781, 16, 690, 14, "width"], [781, 21, 690, 19], [781, 23, 690, 21, "viewSize"], [781, 31, 690, 29], [781, 32, 690, 30, "width"], [781, 37, 690, 35], [782, 16, 691, 14, "height"], [782, 22, 691, 20], [782, 24, 691, 22, "viewSize"], [782, 32, 691, 30], [782, 33, 691, 31, "height"], [782, 39, 691, 37], [782, 42, 691, 40], [782, 45, 691, 43], [783, 16, 692, 14, "borderRadius"], [783, 28, 692, 26], [783, 30, 692, 28], [784, 14, 693, 12], [784, 15, 693, 13], [785, 12, 693, 15], [786, 14, 693, 15, "fileName"], [786, 22, 693, 15], [786, 24, 693, 15, "_jsxFileName"], [786, 36, 693, 15], [787, 14, 693, 15, "lineNumber"], [787, 24, 693, 15], [788, 14, 693, 15, "columnNumber"], [788, 26, 693, 15], [789, 12, 693, 15], [789, 19, 693, 17], [789, 20, 693, 18], [789, 35, 695, 12], [789, 39, 695, 12, "_jsxDevRuntime"], [789, 53, 695, 12], [789, 54, 695, 12, "jsxDEV"], [789, 60, 695, 12], [789, 62, 695, 13, "_expoBlur"], [789, 71, 695, 13], [789, 72, 695, 13, "BlurView"], [789, 80, 695, 21], [790, 14, 695, 22, "intensity"], [790, 23, 695, 31], [790, 25, 695, 33], [790, 27, 695, 36], [791, 14, 695, 37, "tint"], [791, 18, 695, 41], [791, 20, 695, 42], [791, 26, 695, 48], [792, 14, 695, 49, "style"], [792, 19, 695, 54], [792, 21, 695, 56], [792, 22, 695, 57, "styles"], [792, 28, 695, 63], [792, 29, 695, 64, "blurZone"], [792, 37, 695, 72], [792, 39, 695, 74], [793, 16, 696, 14, "left"], [793, 20, 696, 18], [793, 22, 696, 20, "viewSize"], [793, 30, 696, 28], [793, 31, 696, 29, "width"], [793, 36, 696, 34], [793, 39, 696, 37], [793, 42, 696, 40], [793, 45, 696, 44, "viewSize"], [793, 53, 696, 52], [793, 54, 696, 53, "width"], [793, 59, 696, 58], [793, 62, 696, 61], [793, 66, 696, 66], [794, 16, 697, 14, "top"], [794, 19, 697, 17], [794, 21, 697, 19, "viewSize"], [794, 29, 697, 27], [794, 30, 697, 28, "height"], [794, 36, 697, 34], [794, 39, 697, 37], [794, 43, 697, 41], [794, 46, 697, 45, "viewSize"], [794, 54, 697, 53], [794, 55, 697, 54, "width"], [794, 60, 697, 59], [794, 63, 697, 62], [794, 67, 697, 67], [795, 16, 698, 14, "width"], [795, 21, 698, 19], [795, 23, 698, 21, "viewSize"], [795, 31, 698, 29], [795, 32, 698, 30, "width"], [795, 37, 698, 35], [795, 40, 698, 38], [795, 43, 698, 41], [796, 16, 699, 14, "height"], [796, 22, 699, 20], [796, 24, 699, 22, "viewSize"], [796, 32, 699, 30], [796, 33, 699, 31, "width"], [796, 38, 699, 36], [796, 41, 699, 39], [796, 44, 699, 42], [797, 16, 700, 14, "borderRadius"], [797, 28, 700, 26], [797, 30, 700, 29, "viewSize"], [797, 38, 700, 37], [797, 39, 700, 38, "width"], [797, 44, 700, 43], [797, 47, 700, 46], [797, 50, 700, 49], [797, 53, 700, 53], [798, 14, 701, 12], [798, 15, 701, 13], [799, 12, 701, 15], [800, 14, 701, 15, "fileName"], [800, 22, 701, 15], [800, 24, 701, 15, "_jsxFileName"], [800, 36, 701, 15], [801, 14, 701, 15, "lineNumber"], [801, 24, 701, 15], [802, 14, 701, 15, "columnNumber"], [802, 26, 701, 15], [803, 12, 701, 15], [803, 19, 701, 17], [803, 20, 701, 18], [803, 35, 702, 12], [803, 39, 702, 12, "_jsxDevRuntime"], [803, 53, 702, 12], [803, 54, 702, 12, "jsxDEV"], [803, 60, 702, 12], [803, 62, 702, 13, "_expoBlur"], [803, 71, 702, 13], [803, 72, 702, 13, "BlurView"], [803, 80, 702, 21], [804, 14, 702, 22, "intensity"], [804, 23, 702, 31], [804, 25, 702, 33], [804, 27, 702, 36], [805, 14, 702, 37, "tint"], [805, 18, 702, 41], [805, 20, 702, 42], [805, 26, 702, 48], [806, 14, 702, 49, "style"], [806, 19, 702, 54], [806, 21, 702, 56], [806, 22, 702, 57, "styles"], [806, 28, 702, 63], [806, 29, 702, 64, "blurZone"], [806, 37, 702, 72], [806, 39, 702, 74], [807, 16, 703, 14, "left"], [807, 20, 703, 18], [807, 22, 703, 20, "viewSize"], [807, 30, 703, 28], [807, 31, 703, 29, "width"], [807, 36, 703, 34], [807, 39, 703, 37], [807, 42, 703, 40], [807, 45, 703, 44, "viewSize"], [807, 53, 703, 52], [807, 54, 703, 53, "width"], [807, 59, 703, 58], [807, 62, 703, 61], [807, 66, 703, 66], [808, 16, 704, 14, "top"], [808, 19, 704, 17], [808, 21, 704, 19, "viewSize"], [808, 29, 704, 27], [808, 30, 704, 28, "height"], [808, 36, 704, 34], [808, 39, 704, 37], [808, 42, 704, 40], [808, 45, 704, 44, "viewSize"], [808, 53, 704, 52], [808, 54, 704, 53, "width"], [808, 59, 704, 58], [808, 62, 704, 61], [808, 66, 704, 66], [809, 16, 705, 14, "width"], [809, 21, 705, 19], [809, 23, 705, 21, "viewSize"], [809, 31, 705, 29], [809, 32, 705, 30, "width"], [809, 37, 705, 35], [809, 40, 705, 38], [809, 43, 705, 41], [810, 16, 706, 14, "height"], [810, 22, 706, 20], [810, 24, 706, 22, "viewSize"], [810, 32, 706, 30], [810, 33, 706, 31, "width"], [810, 38, 706, 36], [810, 41, 706, 39], [810, 44, 706, 42], [811, 16, 707, 14, "borderRadius"], [811, 28, 707, 26], [811, 30, 707, 29, "viewSize"], [811, 38, 707, 37], [811, 39, 707, 38, "width"], [811, 44, 707, 43], [811, 47, 707, 46], [811, 50, 707, 49], [811, 53, 707, 53], [812, 14, 708, 12], [812, 15, 708, 13], [813, 12, 708, 15], [814, 14, 708, 15, "fileName"], [814, 22, 708, 15], [814, 24, 708, 15, "_jsxFileName"], [814, 36, 708, 15], [815, 14, 708, 15, "lineNumber"], [815, 24, 708, 15], [816, 14, 708, 15, "columnNumber"], [816, 26, 708, 15], [817, 12, 708, 15], [817, 19, 708, 17], [817, 20, 708, 18], [817, 35, 709, 12], [817, 39, 709, 12, "_jsxDevRuntime"], [817, 53, 709, 12], [817, 54, 709, 12, "jsxDEV"], [817, 60, 709, 12], [817, 62, 709, 13, "_expoBlur"], [817, 71, 709, 13], [817, 72, 709, 13, "BlurView"], [817, 80, 709, 21], [818, 14, 709, 22, "intensity"], [818, 23, 709, 31], [818, 25, 709, 33], [818, 27, 709, 36], [819, 14, 709, 37, "tint"], [819, 18, 709, 41], [819, 20, 709, 42], [819, 26, 709, 48], [820, 14, 709, 49, "style"], [820, 19, 709, 54], [820, 21, 709, 56], [820, 22, 709, 57, "styles"], [820, 28, 709, 63], [820, 29, 709, 64, "blurZone"], [820, 37, 709, 72], [820, 39, 709, 74], [821, 16, 710, 14, "left"], [821, 20, 710, 18], [821, 22, 710, 20, "viewSize"], [821, 30, 710, 28], [821, 31, 710, 29, "width"], [821, 36, 710, 34], [821, 39, 710, 37], [821, 42, 710, 40], [821, 45, 710, 44, "viewSize"], [821, 53, 710, 52], [821, 54, 710, 53, "width"], [821, 59, 710, 58], [821, 62, 710, 61], [821, 66, 710, 66], [822, 16, 711, 14, "top"], [822, 19, 711, 17], [822, 21, 711, 19, "viewSize"], [822, 29, 711, 27], [822, 30, 711, 28, "height"], [822, 36, 711, 34], [822, 39, 711, 37], [822, 42, 711, 40], [822, 45, 711, 44, "viewSize"], [822, 53, 711, 52], [822, 54, 711, 53, "width"], [822, 59, 711, 58], [822, 62, 711, 61], [822, 66, 711, 66], [823, 16, 712, 14, "width"], [823, 21, 712, 19], [823, 23, 712, 21, "viewSize"], [823, 31, 712, 29], [823, 32, 712, 30, "width"], [823, 37, 712, 35], [823, 40, 712, 38], [823, 43, 712, 41], [824, 16, 713, 14, "height"], [824, 22, 713, 20], [824, 24, 713, 22, "viewSize"], [824, 32, 713, 30], [824, 33, 713, 31, "width"], [824, 38, 713, 36], [824, 41, 713, 39], [824, 44, 713, 42], [825, 16, 714, 14, "borderRadius"], [825, 28, 714, 26], [825, 30, 714, 29, "viewSize"], [825, 38, 714, 37], [825, 39, 714, 38, "width"], [825, 44, 714, 43], [825, 47, 714, 46], [825, 50, 714, 49], [825, 53, 714, 53], [826, 14, 715, 12], [826, 15, 715, 13], [827, 12, 715, 15], [828, 14, 715, 15, "fileName"], [828, 22, 715, 15], [828, 24, 715, 15, "_jsxFileName"], [828, 36, 715, 15], [829, 14, 715, 15, "lineNumber"], [829, 24, 715, 15], [830, 14, 715, 15, "columnNumber"], [830, 26, 715, 15], [831, 12, 715, 15], [831, 19, 715, 17], [831, 20, 715, 18], [831, 22, 717, 13, "__DEV__"], [831, 29, 717, 20], [831, 46, 718, 14], [831, 50, 718, 14, "_jsxDevRuntime"], [831, 64, 718, 14], [831, 65, 718, 14, "jsxDEV"], [831, 71, 718, 14], [831, 73, 718, 15, "_View"], [831, 78, 718, 15], [831, 79, 718, 15, "default"], [831, 86, 718, 19], [832, 14, 718, 20, "style"], [832, 19, 718, 25], [832, 21, 718, 27, "styles"], [832, 27, 718, 33], [832, 28, 718, 34, "previewChip"], [832, 39, 718, 46], [833, 14, 718, 46, "children"], [833, 22, 718, 46], [833, 37, 719, 16], [833, 41, 719, 16, "_jsxDevRuntime"], [833, 55, 719, 16], [833, 56, 719, 16, "jsxDEV"], [833, 62, 719, 16], [833, 64, 719, 17, "_Text"], [833, 69, 719, 17], [833, 70, 719, 17, "default"], [833, 77, 719, 21], [834, 16, 719, 22, "style"], [834, 21, 719, 27], [834, 23, 719, 29, "styles"], [834, 29, 719, 35], [834, 30, 719, 36, "previewChipText"], [834, 45, 719, 52], [835, 16, 719, 52, "children"], [835, 24, 719, 52], [835, 26, 719, 53], [836, 14, 719, 73], [837, 16, 719, 73, "fileName"], [837, 24, 719, 73], [837, 26, 719, 73, "_jsxFileName"], [837, 38, 719, 73], [838, 16, 719, 73, "lineNumber"], [838, 26, 719, 73], [839, 16, 719, 73, "columnNumber"], [839, 28, 719, 73], [840, 14, 719, 73], [840, 21, 719, 79], [841, 12, 719, 80], [842, 14, 719, 80, "fileName"], [842, 22, 719, 80], [842, 24, 719, 80, "_jsxFileName"], [842, 36, 719, 80], [843, 14, 719, 80, "lineNumber"], [843, 24, 719, 80], [844, 14, 719, 80, "columnNumber"], [844, 26, 719, 80], [845, 12, 719, 80], [845, 19, 720, 20], [845, 20, 721, 13], [846, 10, 721, 13], [847, 12, 721, 13, "fileName"], [847, 20, 721, 13], [847, 22, 721, 13, "_jsxFileName"], [847, 34, 721, 13], [848, 12, 721, 13, "lineNumber"], [848, 22, 721, 13], [849, 12, 721, 13, "columnNumber"], [849, 24, 721, 13], [850, 10, 721, 13], [850, 17, 722, 18], [850, 18, 722, 19], [851, 8, 722, 19], [851, 23, 723, 12], [851, 24, 724, 9], [851, 26, 726, 9, "isCameraReady"], [851, 39, 726, 22], [851, 56, 727, 10], [851, 60, 727, 10, "_jsxDevRuntime"], [851, 74, 727, 10], [851, 75, 727, 10, "jsxDEV"], [851, 81, 727, 10], [851, 83, 727, 10, "_jsxDevRuntime"], [851, 97, 727, 10], [851, 98, 727, 10, "Fragment"], [851, 106, 727, 10], [852, 10, 727, 10, "children"], [852, 18, 727, 10], [852, 34, 729, 12], [852, 38, 729, 12, "_jsxDevRuntime"], [852, 52, 729, 12], [852, 53, 729, 12, "jsxDEV"], [852, 59, 729, 12], [852, 61, 729, 13, "_View"], [852, 66, 729, 13], [852, 67, 729, 13, "default"], [852, 74, 729, 17], [853, 12, 729, 18, "style"], [853, 17, 729, 23], [853, 19, 729, 25, "styles"], [853, 25, 729, 31], [853, 26, 729, 32, "headerOverlay"], [853, 39, 729, 46], [854, 12, 729, 46, "children"], [854, 20, 729, 46], [854, 35, 730, 14], [854, 39, 730, 14, "_jsxDevRuntime"], [854, 53, 730, 14], [854, 54, 730, 14, "jsxDEV"], [854, 60, 730, 14], [854, 62, 730, 15, "_View"], [854, 67, 730, 15], [854, 68, 730, 15, "default"], [854, 75, 730, 19], [855, 14, 730, 20, "style"], [855, 19, 730, 25], [855, 21, 730, 27, "styles"], [855, 27, 730, 33], [855, 28, 730, 34, "headerContent"], [855, 41, 730, 48], [856, 14, 730, 48, "children"], [856, 22, 730, 48], [856, 38, 731, 16], [856, 42, 731, 16, "_jsxDevRuntime"], [856, 56, 731, 16], [856, 57, 731, 16, "jsxDEV"], [856, 63, 731, 16], [856, 65, 731, 17, "_View"], [856, 70, 731, 17], [856, 71, 731, 17, "default"], [856, 78, 731, 21], [857, 16, 731, 22, "style"], [857, 21, 731, 27], [857, 23, 731, 29, "styles"], [857, 29, 731, 35], [857, 30, 731, 36, "headerLeft"], [857, 40, 731, 47], [858, 16, 731, 47, "children"], [858, 24, 731, 47], [858, 40, 732, 18], [858, 44, 732, 18, "_jsxDevRuntime"], [858, 58, 732, 18], [858, 59, 732, 18, "jsxDEV"], [858, 65, 732, 18], [858, 67, 732, 19, "_Text"], [858, 72, 732, 19], [858, 73, 732, 19, "default"], [858, 80, 732, 23], [859, 18, 732, 24, "style"], [859, 23, 732, 29], [859, 25, 732, 31, "styles"], [859, 31, 732, 37], [859, 32, 732, 38, "headerTitle"], [859, 43, 732, 50], [860, 18, 732, 50, "children"], [860, 26, 732, 50], [860, 28, 732, 51], [861, 16, 732, 62], [862, 18, 732, 62, "fileName"], [862, 26, 732, 62], [862, 28, 732, 62, "_jsxFileName"], [862, 40, 732, 62], [863, 18, 732, 62, "lineNumber"], [863, 28, 732, 62], [864, 18, 732, 62, "columnNumber"], [864, 30, 732, 62], [865, 16, 732, 62], [865, 23, 732, 68], [865, 24, 732, 69], [865, 39, 733, 18], [865, 43, 733, 18, "_jsxDevRuntime"], [865, 57, 733, 18], [865, 58, 733, 18, "jsxDEV"], [865, 64, 733, 18], [865, 66, 733, 19, "_View"], [865, 71, 733, 19], [865, 72, 733, 19, "default"], [865, 79, 733, 23], [866, 18, 733, 24, "style"], [866, 23, 733, 29], [866, 25, 733, 31, "styles"], [866, 31, 733, 37], [866, 32, 733, 38, "subtitleRow"], [866, 43, 733, 50], [867, 18, 733, 50, "children"], [867, 26, 733, 50], [867, 42, 734, 20], [867, 46, 734, 20, "_jsxDevRuntime"], [867, 60, 734, 20], [867, 61, 734, 20, "jsxDEV"], [867, 67, 734, 20], [867, 69, 734, 21, "_Text"], [867, 74, 734, 21], [867, 75, 734, 21, "default"], [867, 82, 734, 25], [868, 20, 734, 26, "style"], [868, 25, 734, 31], [868, 27, 734, 33, "styles"], [868, 33, 734, 39], [868, 34, 734, 40, "webIcon"], [868, 41, 734, 48], [869, 20, 734, 48, "children"], [869, 28, 734, 48], [869, 30, 734, 49], [870, 18, 734, 51], [871, 20, 734, 51, "fileName"], [871, 28, 734, 51], [871, 30, 734, 51, "_jsxFileName"], [871, 42, 734, 51], [872, 20, 734, 51, "lineNumber"], [872, 30, 734, 51], [873, 20, 734, 51, "columnNumber"], [873, 32, 734, 51], [874, 18, 734, 51], [874, 25, 734, 57], [874, 26, 734, 58], [874, 41, 735, 20], [874, 45, 735, 20, "_jsxDevRuntime"], [874, 59, 735, 20], [874, 60, 735, 20, "jsxDEV"], [874, 66, 735, 20], [874, 68, 735, 21, "_Text"], [874, 73, 735, 21], [874, 74, 735, 21, "default"], [874, 81, 735, 25], [875, 20, 735, 26, "style"], [875, 25, 735, 31], [875, 27, 735, 33, "styles"], [875, 33, 735, 39], [875, 34, 735, 40, "headerSubtitle"], [875, 48, 735, 55], [876, 20, 735, 55, "children"], [876, 28, 735, 55], [876, 30, 735, 56], [877, 18, 735, 71], [878, 20, 735, 71, "fileName"], [878, 28, 735, 71], [878, 30, 735, 71, "_jsxFileName"], [878, 42, 735, 71], [879, 20, 735, 71, "lineNumber"], [879, 30, 735, 71], [880, 20, 735, 71, "columnNumber"], [880, 32, 735, 71], [881, 18, 735, 71], [881, 25, 735, 77], [881, 26, 735, 78], [882, 16, 735, 78], [883, 18, 735, 78, "fileName"], [883, 26, 735, 78], [883, 28, 735, 78, "_jsxFileName"], [883, 40, 735, 78], [884, 18, 735, 78, "lineNumber"], [884, 28, 735, 78], [885, 18, 735, 78, "columnNumber"], [885, 30, 735, 78], [886, 16, 735, 78], [886, 23, 736, 24], [886, 24, 736, 25], [886, 26, 737, 19, "challengeCode"], [886, 39, 737, 32], [886, 56, 738, 20], [886, 60, 738, 20, "_jsxDevRuntime"], [886, 74, 738, 20], [886, 75, 738, 20, "jsxDEV"], [886, 81, 738, 20], [886, 83, 738, 21, "_View"], [886, 88, 738, 21], [886, 89, 738, 21, "default"], [886, 96, 738, 25], [887, 18, 738, 26, "style"], [887, 23, 738, 31], [887, 25, 738, 33, "styles"], [887, 31, 738, 39], [887, 32, 738, 40, "challengeRow"], [887, 44, 738, 53], [888, 18, 738, 53, "children"], [888, 26, 738, 53], [888, 42, 739, 22], [888, 46, 739, 22, "_jsxDevRuntime"], [888, 60, 739, 22], [888, 61, 739, 22, "jsxDEV"], [888, 67, 739, 22], [888, 69, 739, 23, "_lucideReactNative"], [888, 87, 739, 23], [888, 88, 739, 23, "Shield"], [888, 94, 739, 29], [889, 20, 739, 30, "size"], [889, 24, 739, 34], [889, 26, 739, 36], [889, 28, 739, 39], [890, 20, 739, 40, "color"], [890, 25, 739, 45], [890, 27, 739, 46], [891, 18, 739, 52], [892, 20, 739, 52, "fileName"], [892, 28, 739, 52], [892, 30, 739, 52, "_jsxFileName"], [892, 42, 739, 52], [893, 20, 739, 52, "lineNumber"], [893, 30, 739, 52], [894, 20, 739, 52, "columnNumber"], [894, 32, 739, 52], [895, 18, 739, 52], [895, 25, 739, 54], [895, 26, 739, 55], [895, 41, 740, 22], [895, 45, 740, 22, "_jsxDevRuntime"], [895, 59, 740, 22], [895, 60, 740, 22, "jsxDEV"], [895, 66, 740, 22], [895, 68, 740, 23, "_Text"], [895, 73, 740, 23], [895, 74, 740, 23, "default"], [895, 81, 740, 27], [896, 20, 740, 28, "style"], [896, 25, 740, 33], [896, 27, 740, 35, "styles"], [896, 33, 740, 41], [896, 34, 740, 42, "challengeCode"], [896, 47, 740, 56], [897, 20, 740, 56, "children"], [897, 28, 740, 56], [897, 30, 740, 58, "challengeCode"], [898, 18, 740, 71], [899, 20, 740, 71, "fileName"], [899, 28, 740, 71], [899, 30, 740, 71, "_jsxFileName"], [899, 42, 740, 71], [900, 20, 740, 71, "lineNumber"], [900, 30, 740, 71], [901, 20, 740, 71, "columnNumber"], [901, 32, 740, 71], [902, 18, 740, 71], [902, 25, 740, 78], [902, 26, 740, 79], [903, 16, 740, 79], [904, 18, 740, 79, "fileName"], [904, 26, 740, 79], [904, 28, 740, 79, "_jsxFileName"], [904, 40, 740, 79], [905, 18, 740, 79, "lineNumber"], [905, 28, 740, 79], [906, 18, 740, 79, "columnNumber"], [906, 30, 740, 79], [907, 16, 740, 79], [907, 23, 741, 26], [907, 24, 742, 19], [908, 14, 742, 19], [909, 16, 742, 19, "fileName"], [909, 24, 742, 19], [909, 26, 742, 19, "_jsxFileName"], [909, 38, 742, 19], [910, 16, 742, 19, "lineNumber"], [910, 26, 742, 19], [911, 16, 742, 19, "columnNumber"], [911, 28, 742, 19], [912, 14, 742, 19], [912, 21, 743, 22], [912, 22, 743, 23], [912, 37, 744, 16], [912, 41, 744, 16, "_jsxDevRuntime"], [912, 55, 744, 16], [912, 56, 744, 16, "jsxDEV"], [912, 62, 744, 16], [912, 64, 744, 17, "_TouchableOpacity"], [912, 81, 744, 17], [912, 82, 744, 17, "default"], [912, 89, 744, 33], [913, 16, 744, 34, "onPress"], [913, 23, 744, 41], [913, 25, 744, 43, "onCancel"], [913, 33, 744, 52], [914, 16, 744, 53, "style"], [914, 21, 744, 58], [914, 23, 744, 60, "styles"], [914, 29, 744, 66], [914, 30, 744, 67, "closeButton"], [914, 41, 744, 79], [915, 16, 744, 79, "children"], [915, 24, 744, 79], [915, 39, 745, 18], [915, 43, 745, 18, "_jsxDevRuntime"], [915, 57, 745, 18], [915, 58, 745, 18, "jsxDEV"], [915, 64, 745, 18], [915, 66, 745, 19, "_lucideReactNative"], [915, 84, 745, 19], [915, 85, 745, 19, "X"], [915, 86, 745, 20], [916, 18, 745, 21, "size"], [916, 22, 745, 25], [916, 24, 745, 27], [916, 26, 745, 30], [917, 18, 745, 31, "color"], [917, 23, 745, 36], [917, 25, 745, 37], [918, 16, 745, 43], [919, 18, 745, 43, "fileName"], [919, 26, 745, 43], [919, 28, 745, 43, "_jsxFileName"], [919, 40, 745, 43], [920, 18, 745, 43, "lineNumber"], [920, 28, 745, 43], [921, 18, 745, 43, "columnNumber"], [921, 30, 745, 43], [922, 16, 745, 43], [922, 23, 745, 45], [923, 14, 745, 46], [924, 16, 745, 46, "fileName"], [924, 24, 745, 46], [924, 26, 745, 46, "_jsxFileName"], [924, 38, 745, 46], [925, 16, 745, 46, "lineNumber"], [925, 26, 745, 46], [926, 16, 745, 46, "columnNumber"], [926, 28, 745, 46], [927, 14, 745, 46], [927, 21, 746, 34], [927, 22, 746, 35], [928, 12, 746, 35], [929, 14, 746, 35, "fileName"], [929, 22, 746, 35], [929, 24, 746, 35, "_jsxFileName"], [929, 36, 746, 35], [930, 14, 746, 35, "lineNumber"], [930, 24, 746, 35], [931, 14, 746, 35, "columnNumber"], [931, 26, 746, 35], [932, 12, 746, 35], [932, 19, 747, 20], [933, 10, 747, 21], [934, 12, 747, 21, "fileName"], [934, 20, 747, 21], [934, 22, 747, 21, "_jsxFileName"], [934, 34, 747, 21], [935, 12, 747, 21, "lineNumber"], [935, 22, 747, 21], [936, 12, 747, 21, "columnNumber"], [936, 24, 747, 21], [937, 10, 747, 21], [937, 17, 748, 18], [937, 18, 748, 19], [937, 33, 750, 12], [937, 37, 750, 12, "_jsxDevRuntime"], [937, 51, 750, 12], [937, 52, 750, 12, "jsxDEV"], [937, 58, 750, 12], [937, 60, 750, 13, "_View"], [937, 65, 750, 13], [937, 66, 750, 13, "default"], [937, 73, 750, 17], [938, 12, 750, 18, "style"], [938, 17, 750, 23], [938, 19, 750, 25, "styles"], [938, 25, 750, 31], [938, 26, 750, 32, "privacyNotice"], [938, 39, 750, 46], [939, 12, 750, 46, "children"], [939, 20, 750, 46], [939, 36, 751, 14], [939, 40, 751, 14, "_jsxDevRuntime"], [939, 54, 751, 14], [939, 55, 751, 14, "jsxDEV"], [939, 61, 751, 14], [939, 63, 751, 15, "_lucideReactNative"], [939, 81, 751, 15], [939, 82, 751, 15, "Shield"], [939, 88, 751, 21], [940, 14, 751, 22, "size"], [940, 18, 751, 26], [940, 20, 751, 28], [940, 22, 751, 31], [941, 14, 751, 32, "color"], [941, 19, 751, 37], [941, 21, 751, 38], [942, 12, 751, 47], [943, 14, 751, 47, "fileName"], [943, 22, 751, 47], [943, 24, 751, 47, "_jsxFileName"], [943, 36, 751, 47], [944, 14, 751, 47, "lineNumber"], [944, 24, 751, 47], [945, 14, 751, 47, "columnNumber"], [945, 26, 751, 47], [946, 12, 751, 47], [946, 19, 751, 49], [946, 20, 751, 50], [946, 35, 752, 14], [946, 39, 752, 14, "_jsxDevRuntime"], [946, 53, 752, 14], [946, 54, 752, 14, "jsxDEV"], [946, 60, 752, 14], [946, 62, 752, 15, "_Text"], [946, 67, 752, 15], [946, 68, 752, 15, "default"], [946, 75, 752, 19], [947, 14, 752, 20, "style"], [947, 19, 752, 25], [947, 21, 752, 27, "styles"], [947, 27, 752, 33], [947, 28, 752, 34, "privacyText"], [947, 39, 752, 46], [948, 14, 752, 46, "children"], [948, 22, 752, 46], [948, 24, 752, 47], [949, 12, 754, 14], [950, 14, 754, 14, "fileName"], [950, 22, 754, 14], [950, 24, 754, 14, "_jsxFileName"], [950, 36, 754, 14], [951, 14, 754, 14, "lineNumber"], [951, 24, 754, 14], [952, 14, 754, 14, "columnNumber"], [952, 26, 754, 14], [953, 12, 754, 14], [953, 19, 754, 20], [953, 20, 754, 21], [954, 10, 754, 21], [955, 12, 754, 21, "fileName"], [955, 20, 754, 21], [955, 22, 754, 21, "_jsxFileName"], [955, 34, 754, 21], [956, 12, 754, 21, "lineNumber"], [956, 22, 754, 21], [957, 12, 754, 21, "columnNumber"], [957, 24, 754, 21], [958, 10, 754, 21], [958, 17, 755, 18], [958, 18, 755, 19], [958, 33, 757, 12], [958, 37, 757, 12, "_jsxDevRuntime"], [958, 51, 757, 12], [958, 52, 757, 12, "jsxDEV"], [958, 58, 757, 12], [958, 60, 757, 13, "_View"], [958, 65, 757, 13], [958, 66, 757, 13, "default"], [958, 73, 757, 17], [959, 12, 757, 18, "style"], [959, 17, 757, 23], [959, 19, 757, 25, "styles"], [959, 25, 757, 31], [959, 26, 757, 32, "footer<PERSON><PERSON><PERSON>"], [959, 39, 757, 46], [960, 12, 757, 46, "children"], [960, 20, 757, 46], [960, 36, 758, 14], [960, 40, 758, 14, "_jsxDevRuntime"], [960, 54, 758, 14], [960, 55, 758, 14, "jsxDEV"], [960, 61, 758, 14], [960, 63, 758, 15, "_Text"], [960, 68, 758, 15], [960, 69, 758, 15, "default"], [960, 76, 758, 19], [961, 14, 758, 20, "style"], [961, 19, 758, 25], [961, 21, 758, 27, "styles"], [961, 27, 758, 33], [961, 28, 758, 34, "instruction"], [961, 39, 758, 46], [962, 14, 758, 46, "children"], [962, 22, 758, 46], [962, 24, 758, 47], [963, 12, 760, 14], [964, 14, 760, 14, "fileName"], [964, 22, 760, 14], [964, 24, 760, 14, "_jsxFileName"], [964, 36, 760, 14], [965, 14, 760, 14, "lineNumber"], [965, 24, 760, 14], [966, 14, 760, 14, "columnNumber"], [966, 26, 760, 14], [967, 12, 760, 14], [967, 19, 760, 20], [967, 20, 760, 21], [967, 35, 762, 14], [967, 39, 762, 14, "_jsxDevRuntime"], [967, 53, 762, 14], [967, 54, 762, 14, "jsxDEV"], [967, 60, 762, 14], [967, 62, 762, 15, "_TouchableOpacity"], [967, 79, 762, 15], [967, 80, 762, 15, "default"], [967, 87, 762, 31], [968, 14, 763, 16, "onPress"], [968, 21, 763, 23], [968, 23, 763, 25, "capturePhoto"], [968, 35, 763, 38], [969, 14, 764, 16, "disabled"], [969, 22, 764, 24], [969, 24, 764, 26, "processingState"], [969, 39, 764, 41], [969, 44, 764, 46], [969, 50, 764, 52], [969, 54, 764, 56], [969, 55, 764, 57, "isCameraReady"], [969, 68, 764, 71], [970, 14, 765, 16, "style"], [970, 19, 765, 21], [970, 21, 765, 23], [970, 22, 766, 18, "styles"], [970, 28, 766, 24], [970, 29, 766, 25, "shutterButton"], [970, 42, 766, 38], [970, 44, 767, 18, "processingState"], [970, 59, 767, 33], [970, 64, 767, 38], [970, 70, 767, 44], [970, 74, 767, 48, "styles"], [970, 80, 767, 54], [970, 81, 767, 55, "shutterButtonDisabled"], [970, 102, 767, 76], [970, 103, 768, 18], [971, 14, 768, 18, "children"], [971, 22, 768, 18], [971, 24, 770, 17, "processingState"], [971, 39, 770, 32], [971, 44, 770, 37], [971, 50, 770, 43], [971, 66, 771, 18], [971, 70, 771, 18, "_jsxDevRuntime"], [971, 84, 771, 18], [971, 85, 771, 18, "jsxDEV"], [971, 91, 771, 18], [971, 93, 771, 19, "_View"], [971, 98, 771, 19], [971, 99, 771, 19, "default"], [971, 106, 771, 23], [972, 16, 771, 24, "style"], [972, 21, 771, 29], [972, 23, 771, 31, "styles"], [972, 29, 771, 37], [972, 30, 771, 38, "shutterInner"], [973, 14, 771, 51], [974, 16, 771, 51, "fileName"], [974, 24, 771, 51], [974, 26, 771, 51, "_jsxFileName"], [974, 38, 771, 51], [975, 16, 771, 51, "lineNumber"], [975, 26, 771, 51], [976, 16, 771, 51, "columnNumber"], [976, 28, 771, 51], [977, 14, 771, 51], [977, 21, 771, 53], [977, 22, 771, 54], [977, 38, 773, 18], [977, 42, 773, 18, "_jsxDevRuntime"], [977, 56, 773, 18], [977, 57, 773, 18, "jsxDEV"], [977, 63, 773, 18], [977, 65, 773, 19, "_ActivityIndicator"], [977, 83, 773, 19], [977, 84, 773, 19, "default"], [977, 91, 773, 36], [978, 16, 773, 37, "size"], [978, 20, 773, 41], [978, 22, 773, 42], [978, 29, 773, 49], [979, 16, 773, 50, "color"], [979, 21, 773, 55], [979, 23, 773, 56], [980, 14, 773, 65], [981, 16, 773, 65, "fileName"], [981, 24, 773, 65], [981, 26, 773, 65, "_jsxFileName"], [981, 38, 773, 65], [982, 16, 773, 65, "lineNumber"], [982, 26, 773, 65], [983, 16, 773, 65, "columnNumber"], [983, 28, 773, 65], [984, 14, 773, 65], [984, 21, 773, 67], [985, 12, 774, 17], [986, 14, 774, 17, "fileName"], [986, 22, 774, 17], [986, 24, 774, 17, "_jsxFileName"], [986, 36, 774, 17], [987, 14, 774, 17, "lineNumber"], [987, 24, 774, 17], [988, 14, 774, 17, "columnNumber"], [988, 26, 774, 17], [989, 12, 774, 17], [989, 19, 775, 32], [989, 20, 775, 33], [989, 35, 776, 14], [989, 39, 776, 14, "_jsxDevRuntime"], [989, 53, 776, 14], [989, 54, 776, 14, "jsxDEV"], [989, 60, 776, 14], [989, 62, 776, 15, "_Text"], [989, 67, 776, 15], [989, 68, 776, 15, "default"], [989, 75, 776, 19], [990, 14, 776, 20, "style"], [990, 19, 776, 25], [990, 21, 776, 27, "styles"], [990, 27, 776, 33], [990, 28, 776, 34, "privacyNote"], [990, 39, 776, 46], [991, 14, 776, 46, "children"], [991, 22, 776, 46], [991, 24, 776, 47], [992, 12, 778, 14], [993, 14, 778, 14, "fileName"], [993, 22, 778, 14], [993, 24, 778, 14, "_jsxFileName"], [993, 36, 778, 14], [994, 14, 778, 14, "lineNumber"], [994, 24, 778, 14], [995, 14, 778, 14, "columnNumber"], [995, 26, 778, 14], [996, 12, 778, 14], [996, 19, 778, 20], [996, 20, 778, 21], [997, 10, 778, 21], [998, 12, 778, 21, "fileName"], [998, 20, 778, 21], [998, 22, 778, 21, "_jsxFileName"], [998, 34, 778, 21], [999, 12, 778, 21, "lineNumber"], [999, 22, 778, 21], [1000, 12, 778, 21, "columnNumber"], [1000, 24, 778, 21], [1001, 10, 778, 21], [1001, 17, 779, 18], [1001, 18, 779, 19], [1002, 8, 779, 19], [1002, 23, 780, 12], [1002, 24, 781, 9], [1003, 6, 781, 9], [1004, 8, 781, 9, "fileName"], [1004, 16, 781, 9], [1004, 18, 781, 9, "_jsxFileName"], [1004, 30, 781, 9], [1005, 8, 781, 9, "lineNumber"], [1005, 18, 781, 9], [1006, 8, 781, 9, "columnNumber"], [1006, 20, 781, 9], [1007, 6, 781, 9], [1007, 13, 782, 12], [1007, 14, 782, 13], [1007, 29, 784, 6], [1007, 33, 784, 6, "_jsxDevRuntime"], [1007, 47, 784, 6], [1007, 48, 784, 6, "jsxDEV"], [1007, 54, 784, 6], [1007, 56, 784, 7, "_Modal"], [1007, 62, 784, 7], [1007, 63, 784, 7, "default"], [1007, 70, 784, 12], [1008, 8, 785, 8, "visible"], [1008, 15, 785, 15], [1008, 17, 785, 17, "processingState"], [1008, 32, 785, 32], [1008, 37, 785, 37], [1008, 43, 785, 43], [1008, 47, 785, 47, "processingState"], [1008, 62, 785, 62], [1008, 67, 785, 67], [1008, 74, 785, 75], [1009, 8, 786, 8, "transparent"], [1009, 19, 786, 19], [1010, 8, 787, 8, "animationType"], [1010, 21, 787, 21], [1010, 23, 787, 22], [1010, 29, 787, 28], [1011, 8, 787, 28, "children"], [1011, 16, 787, 28], [1011, 31, 789, 8], [1011, 35, 789, 8, "_jsxDevRuntime"], [1011, 49, 789, 8], [1011, 50, 789, 8, "jsxDEV"], [1011, 56, 789, 8], [1011, 58, 789, 9, "_View"], [1011, 63, 789, 9], [1011, 64, 789, 9, "default"], [1011, 71, 789, 13], [1012, 10, 789, 14, "style"], [1012, 15, 789, 19], [1012, 17, 789, 21, "styles"], [1012, 23, 789, 27], [1012, 24, 789, 28, "processingModal"], [1012, 39, 789, 44], [1013, 10, 789, 44, "children"], [1013, 18, 789, 44], [1013, 33, 790, 10], [1013, 37, 790, 10, "_jsxDevRuntime"], [1013, 51, 790, 10], [1013, 52, 790, 10, "jsxDEV"], [1013, 58, 790, 10], [1013, 60, 790, 11, "_View"], [1013, 65, 790, 11], [1013, 66, 790, 11, "default"], [1013, 73, 790, 15], [1014, 12, 790, 16, "style"], [1014, 17, 790, 21], [1014, 19, 790, 23, "styles"], [1014, 25, 790, 29], [1014, 26, 790, 30, "processingContent"], [1014, 43, 790, 48], [1015, 12, 790, 48, "children"], [1015, 20, 790, 48], [1015, 36, 791, 12], [1015, 40, 791, 12, "_jsxDevRuntime"], [1015, 54, 791, 12], [1015, 55, 791, 12, "jsxDEV"], [1015, 61, 791, 12], [1015, 63, 791, 13, "_ActivityIndicator"], [1015, 81, 791, 13], [1015, 82, 791, 13, "default"], [1015, 89, 791, 30], [1016, 14, 791, 31, "size"], [1016, 18, 791, 35], [1016, 20, 791, 36], [1016, 27, 791, 43], [1017, 14, 791, 44, "color"], [1017, 19, 791, 49], [1017, 21, 791, 50], [1018, 12, 791, 59], [1019, 14, 791, 59, "fileName"], [1019, 22, 791, 59], [1019, 24, 791, 59, "_jsxFileName"], [1019, 36, 791, 59], [1020, 14, 791, 59, "lineNumber"], [1020, 24, 791, 59], [1021, 14, 791, 59, "columnNumber"], [1021, 26, 791, 59], [1022, 12, 791, 59], [1022, 19, 791, 61], [1022, 20, 791, 62], [1022, 35, 793, 12], [1022, 39, 793, 12, "_jsxDevRuntime"], [1022, 53, 793, 12], [1022, 54, 793, 12, "jsxDEV"], [1022, 60, 793, 12], [1022, 62, 793, 13, "_Text"], [1022, 67, 793, 13], [1022, 68, 793, 13, "default"], [1022, 75, 793, 17], [1023, 14, 793, 18, "style"], [1023, 19, 793, 23], [1023, 21, 793, 25, "styles"], [1023, 27, 793, 31], [1023, 28, 793, 32, "processingTitle"], [1023, 43, 793, 48], [1024, 14, 793, 48, "children"], [1024, 22, 793, 48], [1024, 25, 794, 15, "processingState"], [1024, 40, 794, 30], [1024, 45, 794, 35], [1024, 56, 794, 46], [1024, 60, 794, 50], [1024, 80, 794, 70], [1024, 82, 795, 15, "processingState"], [1024, 97, 795, 30], [1024, 102, 795, 35], [1024, 113, 795, 46], [1024, 117, 795, 50], [1024, 146, 795, 79], [1024, 148, 796, 15, "processingState"], [1024, 163, 796, 30], [1024, 168, 796, 35], [1024, 180, 796, 47], [1024, 184, 796, 51], [1024, 216, 796, 83], [1024, 218, 797, 15, "processingState"], [1024, 233, 797, 30], [1024, 238, 797, 35], [1024, 249, 797, 46], [1024, 253, 797, 50], [1024, 275, 797, 72], [1025, 12, 797, 72], [1026, 14, 797, 72, "fileName"], [1026, 22, 797, 72], [1026, 24, 797, 72, "_jsxFileName"], [1026, 36, 797, 72], [1027, 14, 797, 72, "lineNumber"], [1027, 24, 797, 72], [1028, 14, 797, 72, "columnNumber"], [1028, 26, 797, 72], [1029, 12, 797, 72], [1029, 19, 798, 18], [1029, 20, 798, 19], [1029, 35, 799, 12], [1029, 39, 799, 12, "_jsxDevRuntime"], [1029, 53, 799, 12], [1029, 54, 799, 12, "jsxDEV"], [1029, 60, 799, 12], [1029, 62, 799, 13, "_View"], [1029, 67, 799, 13], [1029, 68, 799, 13, "default"], [1029, 75, 799, 17], [1030, 14, 799, 18, "style"], [1030, 19, 799, 23], [1030, 21, 799, 25, "styles"], [1030, 27, 799, 31], [1030, 28, 799, 32, "progressBar"], [1030, 39, 799, 44], [1031, 14, 799, 44, "children"], [1031, 22, 799, 44], [1031, 37, 800, 14], [1031, 41, 800, 14, "_jsxDevRuntime"], [1031, 55, 800, 14], [1031, 56, 800, 14, "jsxDEV"], [1031, 62, 800, 14], [1031, 64, 800, 15, "_View"], [1031, 69, 800, 15], [1031, 70, 800, 15, "default"], [1031, 77, 800, 19], [1032, 16, 801, 16, "style"], [1032, 21, 801, 21], [1032, 23, 801, 23], [1032, 24, 802, 18, "styles"], [1032, 30, 802, 24], [1032, 31, 802, 25, "progressFill"], [1032, 43, 802, 37], [1032, 45, 803, 18], [1033, 18, 803, 20, "width"], [1033, 23, 803, 25], [1033, 25, 803, 27], [1033, 28, 803, 30, "processingProgress"], [1033, 46, 803, 48], [1034, 16, 803, 52], [1034, 17, 803, 53], [1035, 14, 804, 18], [1036, 16, 804, 18, "fileName"], [1036, 24, 804, 18], [1036, 26, 804, 18, "_jsxFileName"], [1036, 38, 804, 18], [1037, 16, 804, 18, "lineNumber"], [1037, 26, 804, 18], [1038, 16, 804, 18, "columnNumber"], [1038, 28, 804, 18], [1039, 14, 804, 18], [1039, 21, 805, 15], [1040, 12, 805, 16], [1041, 14, 805, 16, "fileName"], [1041, 22, 805, 16], [1041, 24, 805, 16, "_jsxFileName"], [1041, 36, 805, 16], [1042, 14, 805, 16, "lineNumber"], [1042, 24, 805, 16], [1043, 14, 805, 16, "columnNumber"], [1043, 26, 805, 16], [1044, 12, 805, 16], [1044, 19, 806, 18], [1044, 20, 806, 19], [1044, 35, 807, 12], [1044, 39, 807, 12, "_jsxDevRuntime"], [1044, 53, 807, 12], [1044, 54, 807, 12, "jsxDEV"], [1044, 60, 807, 12], [1044, 62, 807, 13, "_Text"], [1044, 67, 807, 13], [1044, 68, 807, 13, "default"], [1044, 75, 807, 17], [1045, 14, 807, 18, "style"], [1045, 19, 807, 23], [1045, 21, 807, 25, "styles"], [1045, 27, 807, 31], [1045, 28, 807, 32, "processingDescription"], [1045, 49, 807, 54], [1046, 14, 807, 54, "children"], [1046, 22, 807, 54], [1046, 25, 808, 15, "processingState"], [1046, 40, 808, 30], [1046, 45, 808, 35], [1046, 56, 808, 46], [1046, 60, 808, 50], [1046, 89, 808, 79], [1046, 91, 809, 15, "processingState"], [1046, 106, 809, 30], [1046, 111, 809, 35], [1046, 122, 809, 46], [1046, 126, 809, 50], [1046, 164, 809, 88], [1046, 166, 810, 15, "processingState"], [1046, 181, 810, 30], [1046, 186, 810, 35], [1046, 198, 810, 47], [1046, 202, 810, 51], [1046, 247, 810, 96], [1046, 249, 811, 15, "processingState"], [1046, 264, 811, 30], [1046, 269, 811, 35], [1046, 280, 811, 46], [1046, 284, 811, 50], [1046, 325, 811, 91], [1047, 12, 811, 91], [1048, 14, 811, 91, "fileName"], [1048, 22, 811, 91], [1048, 24, 811, 91, "_jsxFileName"], [1048, 36, 811, 91], [1049, 14, 811, 91, "lineNumber"], [1049, 24, 811, 91], [1050, 14, 811, 91, "columnNumber"], [1050, 26, 811, 91], [1051, 12, 811, 91], [1051, 19, 812, 18], [1051, 20, 812, 19], [1051, 22, 813, 13, "processingState"], [1051, 37, 813, 28], [1051, 42, 813, 33], [1051, 53, 813, 44], [1051, 70, 814, 14], [1051, 74, 814, 14, "_jsxDevRuntime"], [1051, 88, 814, 14], [1051, 89, 814, 14, "jsxDEV"], [1051, 95, 814, 14], [1051, 97, 814, 15, "_lucideReactNative"], [1051, 115, 814, 15], [1051, 116, 814, 15, "CheckCircle"], [1051, 127, 814, 26], [1052, 14, 814, 27, "size"], [1052, 18, 814, 31], [1052, 20, 814, 33], [1052, 22, 814, 36], [1053, 14, 814, 37, "color"], [1053, 19, 814, 42], [1053, 21, 814, 43], [1053, 30, 814, 52], [1054, 14, 814, 53, "style"], [1054, 19, 814, 58], [1054, 21, 814, 60, "styles"], [1054, 27, 814, 66], [1054, 28, 814, 67, "successIcon"], [1055, 12, 814, 79], [1056, 14, 814, 79, "fileName"], [1056, 22, 814, 79], [1056, 24, 814, 79, "_jsxFileName"], [1056, 36, 814, 79], [1057, 14, 814, 79, "lineNumber"], [1057, 24, 814, 79], [1058, 14, 814, 79, "columnNumber"], [1058, 26, 814, 79], [1059, 12, 814, 79], [1059, 19, 814, 81], [1059, 20, 815, 13], [1060, 10, 815, 13], [1061, 12, 815, 13, "fileName"], [1061, 20, 815, 13], [1061, 22, 815, 13, "_jsxFileName"], [1061, 34, 815, 13], [1062, 12, 815, 13, "lineNumber"], [1062, 22, 815, 13], [1063, 12, 815, 13, "columnNumber"], [1063, 24, 815, 13], [1064, 10, 815, 13], [1064, 17, 816, 16], [1065, 8, 816, 17], [1066, 10, 816, 17, "fileName"], [1066, 18, 816, 17], [1066, 20, 816, 17, "_jsxFileName"], [1066, 32, 816, 17], [1067, 10, 816, 17, "lineNumber"], [1067, 20, 816, 17], [1068, 10, 816, 17, "columnNumber"], [1068, 22, 816, 17], [1069, 8, 816, 17], [1069, 15, 817, 14], [1070, 6, 817, 15], [1071, 8, 817, 15, "fileName"], [1071, 16, 817, 15], [1071, 18, 817, 15, "_jsxFileName"], [1071, 30, 817, 15], [1072, 8, 817, 15, "lineNumber"], [1072, 18, 817, 15], [1073, 8, 817, 15, "columnNumber"], [1073, 20, 817, 15], [1074, 6, 817, 15], [1074, 13, 818, 13], [1074, 14, 818, 14], [1074, 29, 820, 6], [1074, 33, 820, 6, "_jsxDevRuntime"], [1074, 47, 820, 6], [1074, 48, 820, 6, "jsxDEV"], [1074, 54, 820, 6], [1074, 56, 820, 7, "_Modal"], [1074, 62, 820, 7], [1074, 63, 820, 7, "default"], [1074, 70, 820, 12], [1075, 8, 821, 8, "visible"], [1075, 15, 821, 15], [1075, 17, 821, 17, "processingState"], [1075, 32, 821, 32], [1075, 37, 821, 37], [1075, 44, 821, 45], [1076, 8, 822, 8, "transparent"], [1076, 19, 822, 19], [1077, 8, 823, 8, "animationType"], [1077, 21, 823, 21], [1077, 23, 823, 22], [1077, 29, 823, 28], [1078, 8, 823, 28, "children"], [1078, 16, 823, 28], [1078, 31, 825, 8], [1078, 35, 825, 8, "_jsxDevRuntime"], [1078, 49, 825, 8], [1078, 50, 825, 8, "jsxDEV"], [1078, 56, 825, 8], [1078, 58, 825, 9, "_View"], [1078, 63, 825, 9], [1078, 64, 825, 9, "default"], [1078, 71, 825, 13], [1079, 10, 825, 14, "style"], [1079, 15, 825, 19], [1079, 17, 825, 21, "styles"], [1079, 23, 825, 27], [1079, 24, 825, 28, "processingModal"], [1079, 39, 825, 44], [1080, 10, 825, 44, "children"], [1080, 18, 825, 44], [1080, 33, 826, 10], [1080, 37, 826, 10, "_jsxDevRuntime"], [1080, 51, 826, 10], [1080, 52, 826, 10, "jsxDEV"], [1080, 58, 826, 10], [1080, 60, 826, 11, "_View"], [1080, 65, 826, 11], [1080, 66, 826, 11, "default"], [1080, 73, 826, 15], [1081, 12, 826, 16, "style"], [1081, 17, 826, 21], [1081, 19, 826, 23, "styles"], [1081, 25, 826, 29], [1081, 26, 826, 30, "errorContent"], [1081, 38, 826, 43], [1082, 12, 826, 43, "children"], [1082, 20, 826, 43], [1082, 36, 827, 12], [1082, 40, 827, 12, "_jsxDevRuntime"], [1082, 54, 827, 12], [1082, 55, 827, 12, "jsxDEV"], [1082, 61, 827, 12], [1082, 63, 827, 13, "_lucideReactNative"], [1082, 81, 827, 13], [1082, 82, 827, 13, "X"], [1082, 83, 827, 14], [1083, 14, 827, 15, "size"], [1083, 18, 827, 19], [1083, 20, 827, 21], [1083, 22, 827, 24], [1084, 14, 827, 25, "color"], [1084, 19, 827, 30], [1084, 21, 827, 31], [1085, 12, 827, 40], [1086, 14, 827, 40, "fileName"], [1086, 22, 827, 40], [1086, 24, 827, 40, "_jsxFileName"], [1086, 36, 827, 40], [1087, 14, 827, 40, "lineNumber"], [1087, 24, 827, 40], [1088, 14, 827, 40, "columnNumber"], [1088, 26, 827, 40], [1089, 12, 827, 40], [1089, 19, 827, 42], [1089, 20, 827, 43], [1089, 35, 828, 12], [1089, 39, 828, 12, "_jsxDevRuntime"], [1089, 53, 828, 12], [1089, 54, 828, 12, "jsxDEV"], [1089, 60, 828, 12], [1089, 62, 828, 13, "_Text"], [1089, 67, 828, 13], [1089, 68, 828, 13, "default"], [1089, 75, 828, 17], [1090, 14, 828, 18, "style"], [1090, 19, 828, 23], [1090, 21, 828, 25, "styles"], [1090, 27, 828, 31], [1090, 28, 828, 32, "errorTitle"], [1090, 38, 828, 43], [1091, 14, 828, 43, "children"], [1091, 22, 828, 43], [1091, 24, 828, 44], [1092, 12, 828, 61], [1093, 14, 828, 61, "fileName"], [1093, 22, 828, 61], [1093, 24, 828, 61, "_jsxFileName"], [1093, 36, 828, 61], [1094, 14, 828, 61, "lineNumber"], [1094, 24, 828, 61], [1095, 14, 828, 61, "columnNumber"], [1095, 26, 828, 61], [1096, 12, 828, 61], [1096, 19, 828, 67], [1096, 20, 828, 68], [1096, 35, 829, 12], [1096, 39, 829, 12, "_jsxDevRuntime"], [1096, 53, 829, 12], [1096, 54, 829, 12, "jsxDEV"], [1096, 60, 829, 12], [1096, 62, 829, 13, "_Text"], [1096, 67, 829, 13], [1096, 68, 829, 13, "default"], [1096, 75, 829, 17], [1097, 14, 829, 18, "style"], [1097, 19, 829, 23], [1097, 21, 829, 25, "styles"], [1097, 27, 829, 31], [1097, 28, 829, 32, "errorMessage"], [1097, 40, 829, 45], [1098, 14, 829, 45, "children"], [1098, 22, 829, 45], [1098, 24, 829, 47, "errorMessage"], [1099, 12, 829, 59], [1100, 14, 829, 59, "fileName"], [1100, 22, 829, 59], [1100, 24, 829, 59, "_jsxFileName"], [1100, 36, 829, 59], [1101, 14, 829, 59, "lineNumber"], [1101, 24, 829, 59], [1102, 14, 829, 59, "columnNumber"], [1102, 26, 829, 59], [1103, 12, 829, 59], [1103, 19, 829, 66], [1103, 20, 829, 67], [1103, 35, 830, 12], [1103, 39, 830, 12, "_jsxDevRuntime"], [1103, 53, 830, 12], [1103, 54, 830, 12, "jsxDEV"], [1103, 60, 830, 12], [1103, 62, 830, 13, "_TouchableOpacity"], [1103, 79, 830, 13], [1103, 80, 830, 13, "default"], [1103, 87, 830, 29], [1104, 14, 831, 14, "onPress"], [1104, 21, 831, 21], [1104, 23, 831, 23, "retryCapture"], [1104, 35, 831, 36], [1105, 14, 832, 14, "style"], [1105, 19, 832, 19], [1105, 21, 832, 21, "styles"], [1105, 27, 832, 27], [1105, 28, 832, 28, "primaryButton"], [1105, 41, 832, 42], [1106, 14, 832, 42, "children"], [1106, 22, 832, 42], [1106, 37, 834, 14], [1106, 41, 834, 14, "_jsxDevRuntime"], [1106, 55, 834, 14], [1106, 56, 834, 14, "jsxDEV"], [1106, 62, 834, 14], [1106, 64, 834, 15, "_Text"], [1106, 69, 834, 15], [1106, 70, 834, 15, "default"], [1106, 77, 834, 19], [1107, 16, 834, 20, "style"], [1107, 21, 834, 25], [1107, 23, 834, 27, "styles"], [1107, 29, 834, 33], [1107, 30, 834, 34, "primaryButtonText"], [1107, 47, 834, 52], [1108, 16, 834, 52, "children"], [1108, 24, 834, 52], [1108, 26, 834, 53], [1109, 14, 834, 62], [1110, 16, 834, 62, "fileName"], [1110, 24, 834, 62], [1110, 26, 834, 62, "_jsxFileName"], [1110, 38, 834, 62], [1111, 16, 834, 62, "lineNumber"], [1111, 26, 834, 62], [1112, 16, 834, 62, "columnNumber"], [1112, 28, 834, 62], [1113, 14, 834, 62], [1113, 21, 834, 68], [1114, 12, 834, 69], [1115, 14, 834, 69, "fileName"], [1115, 22, 834, 69], [1115, 24, 834, 69, "_jsxFileName"], [1115, 36, 834, 69], [1116, 14, 834, 69, "lineNumber"], [1116, 24, 834, 69], [1117, 14, 834, 69, "columnNumber"], [1117, 26, 834, 69], [1118, 12, 834, 69], [1118, 19, 835, 30], [1118, 20, 835, 31], [1118, 35, 836, 12], [1118, 39, 836, 12, "_jsxDevRuntime"], [1118, 53, 836, 12], [1118, 54, 836, 12, "jsxDEV"], [1118, 60, 836, 12], [1118, 62, 836, 13, "_TouchableOpacity"], [1118, 79, 836, 13], [1118, 80, 836, 13, "default"], [1118, 87, 836, 29], [1119, 14, 837, 14, "onPress"], [1119, 21, 837, 21], [1119, 23, 837, 23, "onCancel"], [1119, 31, 837, 32], [1120, 14, 838, 14, "style"], [1120, 19, 838, 19], [1120, 21, 838, 21, "styles"], [1120, 27, 838, 27], [1120, 28, 838, 28, "secondaryButton"], [1120, 43, 838, 44], [1121, 14, 838, 44, "children"], [1121, 22, 838, 44], [1121, 37, 840, 14], [1121, 41, 840, 14, "_jsxDevRuntime"], [1121, 55, 840, 14], [1121, 56, 840, 14, "jsxDEV"], [1121, 62, 840, 14], [1121, 64, 840, 15, "_Text"], [1121, 69, 840, 15], [1121, 70, 840, 15, "default"], [1121, 77, 840, 19], [1122, 16, 840, 20, "style"], [1122, 21, 840, 25], [1122, 23, 840, 27, "styles"], [1122, 29, 840, 33], [1122, 30, 840, 34, "secondaryButtonText"], [1122, 49, 840, 54], [1123, 16, 840, 54, "children"], [1123, 24, 840, 54], [1123, 26, 840, 55], [1124, 14, 840, 61], [1125, 16, 840, 61, "fileName"], [1125, 24, 840, 61], [1125, 26, 840, 61, "_jsxFileName"], [1125, 38, 840, 61], [1126, 16, 840, 61, "lineNumber"], [1126, 26, 840, 61], [1127, 16, 840, 61, "columnNumber"], [1127, 28, 840, 61], [1128, 14, 840, 61], [1128, 21, 840, 67], [1129, 12, 840, 68], [1130, 14, 840, 68, "fileName"], [1130, 22, 840, 68], [1130, 24, 840, 68, "_jsxFileName"], [1130, 36, 840, 68], [1131, 14, 840, 68, "lineNumber"], [1131, 24, 840, 68], [1132, 14, 840, 68, "columnNumber"], [1132, 26, 840, 68], [1133, 12, 840, 68], [1133, 19, 841, 30], [1133, 20, 841, 31], [1134, 10, 841, 31], [1135, 12, 841, 31, "fileName"], [1135, 20, 841, 31], [1135, 22, 841, 31, "_jsxFileName"], [1135, 34, 841, 31], [1136, 12, 841, 31, "lineNumber"], [1136, 22, 841, 31], [1137, 12, 841, 31, "columnNumber"], [1137, 24, 841, 31], [1138, 10, 841, 31], [1138, 17, 842, 16], [1139, 8, 842, 17], [1140, 10, 842, 17, "fileName"], [1140, 18, 842, 17], [1140, 20, 842, 17, "_jsxFileName"], [1140, 32, 842, 17], [1141, 10, 842, 17, "lineNumber"], [1141, 20, 842, 17], [1142, 10, 842, 17, "columnNumber"], [1142, 22, 842, 17], [1143, 8, 842, 17], [1143, 15, 843, 14], [1144, 6, 843, 15], [1145, 8, 843, 15, "fileName"], [1145, 16, 843, 15], [1145, 18, 843, 15, "_jsxFileName"], [1145, 30, 843, 15], [1146, 8, 843, 15, "lineNumber"], [1146, 18, 843, 15], [1147, 8, 843, 15, "columnNumber"], [1147, 20, 843, 15], [1148, 6, 843, 15], [1148, 13, 844, 13], [1148, 14, 844, 14], [1149, 4, 844, 14], [1150, 6, 844, 14, "fileName"], [1150, 14, 844, 14], [1150, 16, 844, 14, "_jsxFileName"], [1150, 28, 844, 14], [1151, 6, 844, 14, "lineNumber"], [1151, 16, 844, 14], [1152, 6, 844, 14, "columnNumber"], [1152, 18, 844, 14], [1153, 4, 844, 14], [1153, 11, 845, 10], [1153, 12, 845, 11], [1154, 2, 847, 0], [1155, 2, 847, 1, "_s"], [1155, 4, 847, 1], [1155, 5, 51, 24, "EchoCameraWeb"], [1155, 18, 51, 37], [1156, 4, 51, 37], [1156, 12, 58, 42, "useCameraPermissions"], [1156, 44, 58, 62], [1156, 46, 72, 19, "useUpload"], [1156, 64, 72, 28], [1157, 2, 72, 28], [1158, 2, 72, 28, "_c"], [1158, 4, 72, 28], [1158, 7, 51, 24, "EchoCameraWeb"], [1158, 20, 51, 37], [1159, 2, 848, 0], [1159, 8, 848, 6, "styles"], [1159, 14, 848, 12], [1159, 17, 848, 15, "StyleSheet"], [1159, 36, 848, 25], [1159, 37, 848, 26, "create"], [1159, 43, 848, 32], [1159, 44, 848, 33], [1160, 4, 849, 2, "container"], [1160, 13, 849, 11], [1160, 15, 849, 13], [1161, 6, 850, 4, "flex"], [1161, 10, 850, 8], [1161, 12, 850, 10], [1161, 13, 850, 11], [1162, 6, 851, 4, "backgroundColor"], [1162, 21, 851, 19], [1162, 23, 851, 21], [1163, 4, 852, 2], [1163, 5, 852, 3], [1164, 4, 853, 2, "cameraContainer"], [1164, 19, 853, 17], [1164, 21, 853, 19], [1165, 6, 854, 4, "flex"], [1165, 10, 854, 8], [1165, 12, 854, 10], [1165, 13, 854, 11], [1166, 6, 855, 4, "max<PERSON><PERSON><PERSON>"], [1166, 14, 855, 12], [1166, 16, 855, 14], [1166, 19, 855, 17], [1167, 6, 856, 4, "alignSelf"], [1167, 15, 856, 13], [1167, 17, 856, 15], [1167, 25, 856, 23], [1168, 6, 857, 4, "width"], [1168, 11, 857, 9], [1168, 13, 857, 11], [1169, 4, 858, 2], [1169, 5, 858, 3], [1170, 4, 859, 2, "camera"], [1170, 10, 859, 8], [1170, 12, 859, 10], [1171, 6, 860, 4, "flex"], [1171, 10, 860, 8], [1171, 12, 860, 10], [1172, 4, 861, 2], [1172, 5, 861, 3], [1173, 4, 862, 2, "headerOverlay"], [1173, 17, 862, 15], [1173, 19, 862, 17], [1174, 6, 863, 4, "position"], [1174, 14, 863, 12], [1174, 16, 863, 14], [1174, 26, 863, 24], [1175, 6, 864, 4, "top"], [1175, 9, 864, 7], [1175, 11, 864, 9], [1175, 12, 864, 10], [1176, 6, 865, 4, "left"], [1176, 10, 865, 8], [1176, 12, 865, 10], [1176, 13, 865, 11], [1177, 6, 866, 4, "right"], [1177, 11, 866, 9], [1177, 13, 866, 11], [1177, 14, 866, 12], [1178, 6, 867, 4, "backgroundColor"], [1178, 21, 867, 19], [1178, 23, 867, 21], [1178, 36, 867, 34], [1179, 6, 868, 4, "paddingTop"], [1179, 16, 868, 14], [1179, 18, 868, 16], [1179, 20, 868, 18], [1180, 6, 869, 4, "paddingHorizontal"], [1180, 23, 869, 21], [1180, 25, 869, 23], [1180, 27, 869, 25], [1181, 6, 870, 4, "paddingBottom"], [1181, 19, 870, 17], [1181, 21, 870, 19], [1182, 4, 871, 2], [1182, 5, 871, 3], [1183, 4, 872, 2, "headerContent"], [1183, 17, 872, 15], [1183, 19, 872, 17], [1184, 6, 873, 4, "flexDirection"], [1184, 19, 873, 17], [1184, 21, 873, 19], [1184, 26, 873, 24], [1185, 6, 874, 4, "justifyContent"], [1185, 20, 874, 18], [1185, 22, 874, 20], [1185, 37, 874, 35], [1186, 6, 875, 4, "alignItems"], [1186, 16, 875, 14], [1186, 18, 875, 16], [1187, 4, 876, 2], [1187, 5, 876, 3], [1188, 4, 877, 2, "headerLeft"], [1188, 14, 877, 12], [1188, 16, 877, 14], [1189, 6, 878, 4, "flex"], [1189, 10, 878, 8], [1189, 12, 878, 10], [1190, 4, 879, 2], [1190, 5, 879, 3], [1191, 4, 880, 2, "headerTitle"], [1191, 15, 880, 13], [1191, 17, 880, 15], [1192, 6, 881, 4, "fontSize"], [1192, 14, 881, 12], [1192, 16, 881, 14], [1192, 18, 881, 16], [1193, 6, 882, 4, "fontWeight"], [1193, 16, 882, 14], [1193, 18, 882, 16], [1193, 23, 882, 21], [1194, 6, 883, 4, "color"], [1194, 11, 883, 9], [1194, 13, 883, 11], [1194, 19, 883, 17], [1195, 6, 884, 4, "marginBottom"], [1195, 18, 884, 16], [1195, 20, 884, 18], [1196, 4, 885, 2], [1196, 5, 885, 3], [1197, 4, 886, 2, "subtitleRow"], [1197, 15, 886, 13], [1197, 17, 886, 15], [1198, 6, 887, 4, "flexDirection"], [1198, 19, 887, 17], [1198, 21, 887, 19], [1198, 26, 887, 24], [1199, 6, 888, 4, "alignItems"], [1199, 16, 888, 14], [1199, 18, 888, 16], [1199, 26, 888, 24], [1200, 6, 889, 4, "marginBottom"], [1200, 18, 889, 16], [1200, 20, 889, 18], [1201, 4, 890, 2], [1201, 5, 890, 3], [1202, 4, 891, 2, "webIcon"], [1202, 11, 891, 9], [1202, 13, 891, 11], [1203, 6, 892, 4, "fontSize"], [1203, 14, 892, 12], [1203, 16, 892, 14], [1203, 18, 892, 16], [1204, 6, 893, 4, "marginRight"], [1204, 17, 893, 15], [1204, 19, 893, 17], [1205, 4, 894, 2], [1205, 5, 894, 3], [1206, 4, 895, 2, "headerSubtitle"], [1206, 18, 895, 16], [1206, 20, 895, 18], [1207, 6, 896, 4, "fontSize"], [1207, 14, 896, 12], [1207, 16, 896, 14], [1207, 18, 896, 16], [1208, 6, 897, 4, "color"], [1208, 11, 897, 9], [1208, 13, 897, 11], [1208, 19, 897, 17], [1209, 6, 898, 4, "opacity"], [1209, 13, 898, 11], [1209, 15, 898, 13], [1210, 4, 899, 2], [1210, 5, 899, 3], [1211, 4, 900, 2, "challengeRow"], [1211, 16, 900, 14], [1211, 18, 900, 16], [1212, 6, 901, 4, "flexDirection"], [1212, 19, 901, 17], [1212, 21, 901, 19], [1212, 26, 901, 24], [1213, 6, 902, 4, "alignItems"], [1213, 16, 902, 14], [1213, 18, 902, 16], [1214, 4, 903, 2], [1214, 5, 903, 3], [1215, 4, 904, 2, "challengeCode"], [1215, 17, 904, 15], [1215, 19, 904, 17], [1216, 6, 905, 4, "fontSize"], [1216, 14, 905, 12], [1216, 16, 905, 14], [1216, 18, 905, 16], [1217, 6, 906, 4, "color"], [1217, 11, 906, 9], [1217, 13, 906, 11], [1217, 19, 906, 17], [1218, 6, 907, 4, "marginLeft"], [1218, 16, 907, 14], [1218, 18, 907, 16], [1218, 19, 907, 17], [1219, 6, 908, 4, "fontFamily"], [1219, 16, 908, 14], [1219, 18, 908, 16], [1220, 4, 909, 2], [1220, 5, 909, 3], [1221, 4, 910, 2, "closeButton"], [1221, 15, 910, 13], [1221, 17, 910, 15], [1222, 6, 911, 4, "padding"], [1222, 13, 911, 11], [1222, 15, 911, 13], [1223, 4, 912, 2], [1223, 5, 912, 3], [1224, 4, 913, 2, "privacyNotice"], [1224, 17, 913, 15], [1224, 19, 913, 17], [1225, 6, 914, 4, "position"], [1225, 14, 914, 12], [1225, 16, 914, 14], [1225, 26, 914, 24], [1226, 6, 915, 4, "top"], [1226, 9, 915, 7], [1226, 11, 915, 9], [1226, 14, 915, 12], [1227, 6, 916, 4, "left"], [1227, 10, 916, 8], [1227, 12, 916, 10], [1227, 14, 916, 12], [1228, 6, 917, 4, "right"], [1228, 11, 917, 9], [1228, 13, 917, 11], [1228, 15, 917, 13], [1229, 6, 918, 4, "backgroundColor"], [1229, 21, 918, 19], [1229, 23, 918, 21], [1229, 48, 918, 46], [1230, 6, 919, 4, "borderRadius"], [1230, 18, 919, 16], [1230, 20, 919, 18], [1230, 21, 919, 19], [1231, 6, 920, 4, "padding"], [1231, 13, 920, 11], [1231, 15, 920, 13], [1231, 17, 920, 15], [1232, 6, 921, 4, "flexDirection"], [1232, 19, 921, 17], [1232, 21, 921, 19], [1232, 26, 921, 24], [1233, 6, 922, 4, "alignItems"], [1233, 16, 922, 14], [1233, 18, 922, 16], [1234, 4, 923, 2], [1234, 5, 923, 3], [1235, 4, 924, 2, "privacyText"], [1235, 15, 924, 13], [1235, 17, 924, 15], [1236, 6, 925, 4, "color"], [1236, 11, 925, 9], [1236, 13, 925, 11], [1236, 19, 925, 17], [1237, 6, 926, 4, "fontSize"], [1237, 14, 926, 12], [1237, 16, 926, 14], [1237, 18, 926, 16], [1238, 6, 927, 4, "marginLeft"], [1238, 16, 927, 14], [1238, 18, 927, 16], [1238, 19, 927, 17], [1239, 6, 928, 4, "flex"], [1239, 10, 928, 8], [1239, 12, 928, 10], [1240, 4, 929, 2], [1240, 5, 929, 3], [1241, 4, 930, 2, "footer<PERSON><PERSON><PERSON>"], [1241, 17, 930, 15], [1241, 19, 930, 17], [1242, 6, 931, 4, "position"], [1242, 14, 931, 12], [1242, 16, 931, 14], [1242, 26, 931, 24], [1243, 6, 932, 4, "bottom"], [1243, 12, 932, 10], [1243, 14, 932, 12], [1243, 15, 932, 13], [1244, 6, 933, 4, "left"], [1244, 10, 933, 8], [1244, 12, 933, 10], [1244, 13, 933, 11], [1245, 6, 934, 4, "right"], [1245, 11, 934, 9], [1245, 13, 934, 11], [1245, 14, 934, 12], [1246, 6, 935, 4, "backgroundColor"], [1246, 21, 935, 19], [1246, 23, 935, 21], [1246, 36, 935, 34], [1247, 6, 936, 4, "paddingBottom"], [1247, 19, 936, 17], [1247, 21, 936, 19], [1247, 23, 936, 21], [1248, 6, 937, 4, "paddingTop"], [1248, 16, 937, 14], [1248, 18, 937, 16], [1248, 20, 937, 18], [1249, 6, 938, 4, "alignItems"], [1249, 16, 938, 14], [1249, 18, 938, 16], [1250, 4, 939, 2], [1250, 5, 939, 3], [1251, 4, 940, 2, "instruction"], [1251, 15, 940, 13], [1251, 17, 940, 15], [1252, 6, 941, 4, "fontSize"], [1252, 14, 941, 12], [1252, 16, 941, 14], [1252, 18, 941, 16], [1253, 6, 942, 4, "color"], [1253, 11, 942, 9], [1253, 13, 942, 11], [1253, 19, 942, 17], [1254, 6, 943, 4, "marginBottom"], [1254, 18, 943, 16], [1254, 20, 943, 18], [1255, 4, 944, 2], [1255, 5, 944, 3], [1256, 4, 945, 2, "shutterButton"], [1256, 17, 945, 15], [1256, 19, 945, 17], [1257, 6, 946, 4, "width"], [1257, 11, 946, 9], [1257, 13, 946, 11], [1257, 15, 946, 13], [1258, 6, 947, 4, "height"], [1258, 12, 947, 10], [1258, 14, 947, 12], [1258, 16, 947, 14], [1259, 6, 948, 4, "borderRadius"], [1259, 18, 948, 16], [1259, 20, 948, 18], [1259, 22, 948, 20], [1260, 6, 949, 4, "backgroundColor"], [1260, 21, 949, 19], [1260, 23, 949, 21], [1260, 29, 949, 27], [1261, 6, 950, 4, "justifyContent"], [1261, 20, 950, 18], [1261, 22, 950, 20], [1261, 30, 950, 28], [1262, 6, 951, 4, "alignItems"], [1262, 16, 951, 14], [1262, 18, 951, 16], [1262, 26, 951, 24], [1263, 6, 952, 4, "marginBottom"], [1263, 18, 952, 16], [1263, 20, 952, 18], [1263, 22, 952, 20], [1264, 6, 953, 4], [1264, 9, 953, 7, "Platform"], [1264, 26, 953, 15], [1264, 27, 953, 16, "select"], [1264, 33, 953, 22], [1264, 34, 953, 23], [1265, 8, 954, 6, "ios"], [1265, 11, 954, 9], [1265, 13, 954, 11], [1266, 10, 955, 8, "shadowColor"], [1266, 21, 955, 19], [1266, 23, 955, 21], [1266, 32, 955, 30], [1267, 10, 956, 8, "shadowOffset"], [1267, 22, 956, 20], [1267, 24, 956, 22], [1268, 12, 956, 24, "width"], [1268, 17, 956, 29], [1268, 19, 956, 31], [1268, 20, 956, 32], [1269, 12, 956, 34, "height"], [1269, 18, 956, 40], [1269, 20, 956, 42], [1270, 10, 956, 44], [1270, 11, 956, 45], [1271, 10, 957, 8, "shadowOpacity"], [1271, 23, 957, 21], [1271, 25, 957, 23], [1271, 28, 957, 26], [1272, 10, 958, 8, "shadowRadius"], [1272, 22, 958, 20], [1272, 24, 958, 22], [1273, 8, 959, 6], [1273, 9, 959, 7], [1274, 8, 960, 6, "android"], [1274, 15, 960, 13], [1274, 17, 960, 15], [1275, 10, 961, 8, "elevation"], [1275, 19, 961, 17], [1275, 21, 961, 19], [1276, 8, 962, 6], [1276, 9, 962, 7], [1277, 8, 963, 6, "web"], [1277, 11, 963, 9], [1277, 13, 963, 11], [1278, 10, 964, 8, "boxShadow"], [1278, 19, 964, 17], [1278, 21, 964, 19], [1279, 8, 965, 6], [1280, 6, 966, 4], [1280, 7, 966, 5], [1281, 4, 967, 2], [1281, 5, 967, 3], [1282, 4, 968, 2, "shutterButtonDisabled"], [1282, 25, 968, 23], [1282, 27, 968, 25], [1283, 6, 969, 4, "opacity"], [1283, 13, 969, 11], [1283, 15, 969, 13], [1284, 4, 970, 2], [1284, 5, 970, 3], [1285, 4, 971, 2, "shutterInner"], [1285, 16, 971, 14], [1285, 18, 971, 16], [1286, 6, 972, 4, "width"], [1286, 11, 972, 9], [1286, 13, 972, 11], [1286, 15, 972, 13], [1287, 6, 973, 4, "height"], [1287, 12, 973, 10], [1287, 14, 973, 12], [1287, 16, 973, 14], [1288, 6, 974, 4, "borderRadius"], [1288, 18, 974, 16], [1288, 20, 974, 18], [1288, 22, 974, 20], [1289, 6, 975, 4, "backgroundColor"], [1289, 21, 975, 19], [1289, 23, 975, 21], [1289, 29, 975, 27], [1290, 6, 976, 4, "borderWidth"], [1290, 17, 976, 15], [1290, 19, 976, 17], [1290, 20, 976, 18], [1291, 6, 977, 4, "borderColor"], [1291, 17, 977, 15], [1291, 19, 977, 17], [1292, 4, 978, 2], [1292, 5, 978, 3], [1293, 4, 979, 2, "privacyNote"], [1293, 15, 979, 13], [1293, 17, 979, 15], [1294, 6, 980, 4, "fontSize"], [1294, 14, 980, 12], [1294, 16, 980, 14], [1294, 18, 980, 16], [1295, 6, 981, 4, "color"], [1295, 11, 981, 9], [1295, 13, 981, 11], [1296, 4, 982, 2], [1296, 5, 982, 3], [1297, 4, 983, 2, "processingModal"], [1297, 19, 983, 17], [1297, 21, 983, 19], [1298, 6, 984, 4, "flex"], [1298, 10, 984, 8], [1298, 12, 984, 10], [1298, 13, 984, 11], [1299, 6, 985, 4, "backgroundColor"], [1299, 21, 985, 19], [1299, 23, 985, 21], [1299, 43, 985, 41], [1300, 6, 986, 4, "justifyContent"], [1300, 20, 986, 18], [1300, 22, 986, 20], [1300, 30, 986, 28], [1301, 6, 987, 4, "alignItems"], [1301, 16, 987, 14], [1301, 18, 987, 16], [1302, 4, 988, 2], [1302, 5, 988, 3], [1303, 4, 989, 2, "processingContent"], [1303, 21, 989, 19], [1303, 23, 989, 21], [1304, 6, 990, 4, "backgroundColor"], [1304, 21, 990, 19], [1304, 23, 990, 21], [1304, 29, 990, 27], [1305, 6, 991, 4, "borderRadius"], [1305, 18, 991, 16], [1305, 20, 991, 18], [1305, 22, 991, 20], [1306, 6, 992, 4, "padding"], [1306, 13, 992, 11], [1306, 15, 992, 13], [1306, 17, 992, 15], [1307, 6, 993, 4, "width"], [1307, 11, 993, 9], [1307, 13, 993, 11], [1307, 18, 993, 16], [1308, 6, 994, 4, "max<PERSON><PERSON><PERSON>"], [1308, 14, 994, 12], [1308, 16, 994, 14], [1308, 19, 994, 17], [1309, 6, 995, 4, "alignItems"], [1309, 16, 995, 14], [1309, 18, 995, 16], [1310, 4, 996, 2], [1310, 5, 996, 3], [1311, 4, 997, 2, "processingTitle"], [1311, 19, 997, 17], [1311, 21, 997, 19], [1312, 6, 998, 4, "fontSize"], [1312, 14, 998, 12], [1312, 16, 998, 14], [1312, 18, 998, 16], [1313, 6, 999, 4, "fontWeight"], [1313, 16, 999, 14], [1313, 18, 999, 16], [1313, 23, 999, 21], [1314, 6, 1000, 4, "color"], [1314, 11, 1000, 9], [1314, 13, 1000, 11], [1314, 22, 1000, 20], [1315, 6, 1001, 4, "marginTop"], [1315, 15, 1001, 13], [1315, 17, 1001, 15], [1315, 19, 1001, 17], [1316, 6, 1002, 4, "marginBottom"], [1316, 18, 1002, 16], [1316, 20, 1002, 18], [1317, 4, 1003, 2], [1317, 5, 1003, 3], [1318, 4, 1004, 2, "progressBar"], [1318, 15, 1004, 13], [1318, 17, 1004, 15], [1319, 6, 1005, 4, "width"], [1319, 11, 1005, 9], [1319, 13, 1005, 11], [1319, 19, 1005, 17], [1320, 6, 1006, 4, "height"], [1320, 12, 1006, 10], [1320, 14, 1006, 12], [1320, 15, 1006, 13], [1321, 6, 1007, 4, "backgroundColor"], [1321, 21, 1007, 19], [1321, 23, 1007, 21], [1321, 32, 1007, 30], [1322, 6, 1008, 4, "borderRadius"], [1322, 18, 1008, 16], [1322, 20, 1008, 18], [1322, 21, 1008, 19], [1323, 6, 1009, 4, "overflow"], [1323, 14, 1009, 12], [1323, 16, 1009, 14], [1323, 24, 1009, 22], [1324, 6, 1010, 4, "marginBottom"], [1324, 18, 1010, 16], [1324, 20, 1010, 18], [1325, 4, 1011, 2], [1325, 5, 1011, 3], [1326, 4, 1012, 2, "progressFill"], [1326, 16, 1012, 14], [1326, 18, 1012, 16], [1327, 6, 1013, 4, "height"], [1327, 12, 1013, 10], [1327, 14, 1013, 12], [1327, 20, 1013, 18], [1328, 6, 1014, 4, "backgroundColor"], [1328, 21, 1014, 19], [1328, 23, 1014, 21], [1328, 32, 1014, 30], [1329, 6, 1015, 4, "borderRadius"], [1329, 18, 1015, 16], [1329, 20, 1015, 18], [1330, 4, 1016, 2], [1330, 5, 1016, 3], [1331, 4, 1017, 2, "processingDescription"], [1331, 25, 1017, 23], [1331, 27, 1017, 25], [1332, 6, 1018, 4, "fontSize"], [1332, 14, 1018, 12], [1332, 16, 1018, 14], [1332, 18, 1018, 16], [1333, 6, 1019, 4, "color"], [1333, 11, 1019, 9], [1333, 13, 1019, 11], [1333, 22, 1019, 20], [1334, 6, 1020, 4, "textAlign"], [1334, 15, 1020, 13], [1334, 17, 1020, 15], [1335, 4, 1021, 2], [1335, 5, 1021, 3], [1336, 4, 1022, 2, "successIcon"], [1336, 15, 1022, 13], [1336, 17, 1022, 15], [1337, 6, 1023, 4, "marginTop"], [1337, 15, 1023, 13], [1337, 17, 1023, 15], [1338, 4, 1024, 2], [1338, 5, 1024, 3], [1339, 4, 1025, 2, "errorContent"], [1339, 16, 1025, 14], [1339, 18, 1025, 16], [1340, 6, 1026, 4, "backgroundColor"], [1340, 21, 1026, 19], [1340, 23, 1026, 21], [1340, 29, 1026, 27], [1341, 6, 1027, 4, "borderRadius"], [1341, 18, 1027, 16], [1341, 20, 1027, 18], [1341, 22, 1027, 20], [1342, 6, 1028, 4, "padding"], [1342, 13, 1028, 11], [1342, 15, 1028, 13], [1342, 17, 1028, 15], [1343, 6, 1029, 4, "width"], [1343, 11, 1029, 9], [1343, 13, 1029, 11], [1343, 18, 1029, 16], [1344, 6, 1030, 4, "max<PERSON><PERSON><PERSON>"], [1344, 14, 1030, 12], [1344, 16, 1030, 14], [1344, 19, 1030, 17], [1345, 6, 1031, 4, "alignItems"], [1345, 16, 1031, 14], [1345, 18, 1031, 16], [1346, 4, 1032, 2], [1346, 5, 1032, 3], [1347, 4, 1033, 2, "errorTitle"], [1347, 14, 1033, 12], [1347, 16, 1033, 14], [1348, 6, 1034, 4, "fontSize"], [1348, 14, 1034, 12], [1348, 16, 1034, 14], [1348, 18, 1034, 16], [1349, 6, 1035, 4, "fontWeight"], [1349, 16, 1035, 14], [1349, 18, 1035, 16], [1349, 23, 1035, 21], [1350, 6, 1036, 4, "color"], [1350, 11, 1036, 9], [1350, 13, 1036, 11], [1350, 22, 1036, 20], [1351, 6, 1037, 4, "marginTop"], [1351, 15, 1037, 13], [1351, 17, 1037, 15], [1351, 19, 1037, 17], [1352, 6, 1038, 4, "marginBottom"], [1352, 18, 1038, 16], [1352, 20, 1038, 18], [1353, 4, 1039, 2], [1353, 5, 1039, 3], [1354, 4, 1040, 2, "errorMessage"], [1354, 16, 1040, 14], [1354, 18, 1040, 16], [1355, 6, 1041, 4, "fontSize"], [1355, 14, 1041, 12], [1355, 16, 1041, 14], [1355, 18, 1041, 16], [1356, 6, 1042, 4, "color"], [1356, 11, 1042, 9], [1356, 13, 1042, 11], [1356, 22, 1042, 20], [1357, 6, 1043, 4, "textAlign"], [1357, 15, 1043, 13], [1357, 17, 1043, 15], [1357, 25, 1043, 23], [1358, 6, 1044, 4, "marginBottom"], [1358, 18, 1044, 16], [1358, 20, 1044, 18], [1359, 4, 1045, 2], [1359, 5, 1045, 3], [1360, 4, 1046, 2, "primaryButton"], [1360, 17, 1046, 15], [1360, 19, 1046, 17], [1361, 6, 1047, 4, "backgroundColor"], [1361, 21, 1047, 19], [1361, 23, 1047, 21], [1361, 32, 1047, 30], [1362, 6, 1048, 4, "paddingHorizontal"], [1362, 23, 1048, 21], [1362, 25, 1048, 23], [1362, 27, 1048, 25], [1363, 6, 1049, 4, "paddingVertical"], [1363, 21, 1049, 19], [1363, 23, 1049, 21], [1363, 25, 1049, 23], [1364, 6, 1050, 4, "borderRadius"], [1364, 18, 1050, 16], [1364, 20, 1050, 18], [1364, 21, 1050, 19], [1365, 6, 1051, 4, "marginTop"], [1365, 15, 1051, 13], [1365, 17, 1051, 15], [1366, 4, 1052, 2], [1366, 5, 1052, 3], [1367, 4, 1053, 2, "primaryButtonText"], [1367, 21, 1053, 19], [1367, 23, 1053, 21], [1368, 6, 1054, 4, "color"], [1368, 11, 1054, 9], [1368, 13, 1054, 11], [1368, 19, 1054, 17], [1369, 6, 1055, 4, "fontSize"], [1369, 14, 1055, 12], [1369, 16, 1055, 14], [1369, 18, 1055, 16], [1370, 6, 1056, 4, "fontWeight"], [1370, 16, 1056, 14], [1370, 18, 1056, 16], [1371, 4, 1057, 2], [1371, 5, 1057, 3], [1372, 4, 1058, 2, "secondaryButton"], [1372, 19, 1058, 17], [1372, 21, 1058, 19], [1373, 6, 1059, 4, "paddingHorizontal"], [1373, 23, 1059, 21], [1373, 25, 1059, 23], [1373, 27, 1059, 25], [1374, 6, 1060, 4, "paddingVertical"], [1374, 21, 1060, 19], [1374, 23, 1060, 21], [1374, 25, 1060, 23], [1375, 6, 1061, 4, "marginTop"], [1375, 15, 1061, 13], [1375, 17, 1061, 15], [1376, 4, 1062, 2], [1376, 5, 1062, 3], [1377, 4, 1063, 2, "secondaryButtonText"], [1377, 23, 1063, 21], [1377, 25, 1063, 23], [1378, 6, 1064, 4, "color"], [1378, 11, 1064, 9], [1378, 13, 1064, 11], [1378, 22, 1064, 20], [1379, 6, 1065, 4, "fontSize"], [1379, 14, 1065, 12], [1379, 16, 1065, 14], [1380, 4, 1066, 2], [1380, 5, 1066, 3], [1381, 4, 1067, 2, "permissionContent"], [1381, 21, 1067, 19], [1381, 23, 1067, 21], [1382, 6, 1068, 4, "flex"], [1382, 10, 1068, 8], [1382, 12, 1068, 10], [1382, 13, 1068, 11], [1383, 6, 1069, 4, "justifyContent"], [1383, 20, 1069, 18], [1383, 22, 1069, 20], [1383, 30, 1069, 28], [1384, 6, 1070, 4, "alignItems"], [1384, 16, 1070, 14], [1384, 18, 1070, 16], [1384, 26, 1070, 24], [1385, 6, 1071, 4, "padding"], [1385, 13, 1071, 11], [1385, 15, 1071, 13], [1386, 4, 1072, 2], [1386, 5, 1072, 3], [1387, 4, 1073, 2, "permissionTitle"], [1387, 19, 1073, 17], [1387, 21, 1073, 19], [1388, 6, 1074, 4, "fontSize"], [1388, 14, 1074, 12], [1388, 16, 1074, 14], [1388, 18, 1074, 16], [1389, 6, 1075, 4, "fontWeight"], [1389, 16, 1075, 14], [1389, 18, 1075, 16], [1389, 23, 1075, 21], [1390, 6, 1076, 4, "color"], [1390, 11, 1076, 9], [1390, 13, 1076, 11], [1390, 22, 1076, 20], [1391, 6, 1077, 4, "marginTop"], [1391, 15, 1077, 13], [1391, 17, 1077, 15], [1391, 19, 1077, 17], [1392, 6, 1078, 4, "marginBottom"], [1392, 18, 1078, 16], [1392, 20, 1078, 18], [1393, 4, 1079, 2], [1393, 5, 1079, 3], [1394, 4, 1080, 2, "permissionDescription"], [1394, 25, 1080, 23], [1394, 27, 1080, 25], [1395, 6, 1081, 4, "fontSize"], [1395, 14, 1081, 12], [1395, 16, 1081, 14], [1395, 18, 1081, 16], [1396, 6, 1082, 4, "color"], [1396, 11, 1082, 9], [1396, 13, 1082, 11], [1396, 22, 1082, 20], [1397, 6, 1083, 4, "textAlign"], [1397, 15, 1083, 13], [1397, 17, 1083, 15], [1397, 25, 1083, 23], [1398, 6, 1084, 4, "marginBottom"], [1398, 18, 1084, 16], [1398, 20, 1084, 18], [1399, 4, 1085, 2], [1399, 5, 1085, 3], [1400, 4, 1086, 2, "loadingText"], [1400, 15, 1086, 13], [1400, 17, 1086, 15], [1401, 6, 1087, 4, "color"], [1401, 11, 1087, 9], [1401, 13, 1087, 11], [1401, 22, 1087, 20], [1402, 6, 1088, 4, "marginTop"], [1402, 15, 1088, 13], [1402, 17, 1088, 15], [1403, 4, 1089, 2], [1403, 5, 1089, 3], [1404, 4, 1090, 2], [1405, 4, 1091, 2, "blurZone"], [1405, 12, 1091, 10], [1405, 14, 1091, 12], [1406, 6, 1092, 4, "position"], [1406, 14, 1092, 12], [1406, 16, 1092, 14], [1406, 26, 1092, 24], [1407, 6, 1093, 4, "overflow"], [1407, 14, 1093, 12], [1407, 16, 1093, 14], [1408, 4, 1094, 2], [1408, 5, 1094, 3], [1409, 4, 1095, 2, "previewChip"], [1409, 15, 1095, 13], [1409, 17, 1095, 15], [1410, 6, 1096, 4, "position"], [1410, 14, 1096, 12], [1410, 16, 1096, 14], [1410, 26, 1096, 24], [1411, 6, 1097, 4, "top"], [1411, 9, 1097, 7], [1411, 11, 1097, 9], [1411, 12, 1097, 10], [1412, 6, 1098, 4, "right"], [1412, 11, 1098, 9], [1412, 13, 1098, 11], [1412, 14, 1098, 12], [1413, 6, 1099, 4, "backgroundColor"], [1413, 21, 1099, 19], [1413, 23, 1099, 21], [1413, 40, 1099, 38], [1414, 6, 1100, 4, "paddingHorizontal"], [1414, 23, 1100, 21], [1414, 25, 1100, 23], [1414, 27, 1100, 25], [1415, 6, 1101, 4, "paddingVertical"], [1415, 21, 1101, 19], [1415, 23, 1101, 21], [1415, 24, 1101, 22], [1416, 6, 1102, 4, "borderRadius"], [1416, 18, 1102, 16], [1416, 20, 1102, 18], [1417, 4, 1103, 2], [1417, 5, 1103, 3], [1418, 4, 1104, 2, "previewChipText"], [1418, 19, 1104, 17], [1418, 21, 1104, 19], [1419, 6, 1105, 4, "color"], [1419, 11, 1105, 9], [1419, 13, 1105, 11], [1419, 19, 1105, 17], [1420, 6, 1106, 4, "fontSize"], [1420, 14, 1106, 12], [1420, 16, 1106, 14], [1420, 18, 1106, 16], [1421, 6, 1107, 4, "fontWeight"], [1421, 16, 1107, 14], [1421, 18, 1107, 16], [1422, 4, 1108, 2], [1423, 2, 1109, 0], [1423, 3, 1109, 1], [1423, 4, 1109, 2], [1424, 2, 1109, 3], [1424, 6, 1109, 3, "_c"], [1424, 8, 1109, 3], [1425, 2, 1109, 3, "$RefreshReg$"], [1425, 14, 1109, 3], [1425, 15, 1109, 3, "_c"], [1425, 17, 1109, 3], [1426, 0, 1109, 3], [1426, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadMediaPipeFaceDetection", "Promise$argument_0", "detectFacesWithMediaPipe", "detectFacesHeuristic", "countSkinPixelsInRegion", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;qCGG;wBCG;ODM;GHE;mCKE;GLI;+BME;GNqC;kCOE;GPgB;qBQE;GRQ;8BSE;GT4B;2BUE;GVa;wBWE;GXiB;mCYG;wBRc,kCQ;GZoC;mCaE;wBTa;OSI;oFCkC;UDM;8BES;SFoD;uDTU;sBYC,wBZ;OSC;Gbe;6BiBG;GjB6B;kCkBG;GlB8C;4BmBE;mBCmD;SDE;GnBO;uBqBE;GrBI;mCsBG;GtBM;YCE;GDK;oBuB2C;WvBG;yBwBC;WxBG;wByBC;WzBI;CD4L"}}, "type": "js/module"}]}