{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Simple but effective face detection using multiple strategies\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting NEW face detection system...');\n\n        // Strategy 1: Try MediaPipe Face Detection (more reliable)\n        try {\n          await loadMediaPipeFaceDetection();\n          detectedFaces = await detectFacesWithMediaPipe(img);\n          console.log(`[EchoCameraWeb] ✅ MediaPipe found ${detectedFaces.length} faces`);\n        } catch (mediaPipeError) {\n          console.warn('[EchoCameraWeb] ❌ MediaPipe failed:', mediaPipeError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Using intelligent heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect - VERY AGGRESSIVE for testing\n            const pixelSize = Math.max(20, Math.min(paddedWidth, paddedHeight) / 8); // Much larger pixels for obvious effect\n            console.log(`[EchoCameraWeb] 🔲 Using LARGE pixel size: ${pixelSize}px for obvious blurring effect`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} blurring applied successfully`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1295, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 56, 128, 54], [126, 58, 128, 56, "photo"], [126, 63, 128, 61], [126, 64, 128, 62, "uri"], [126, 67, 128, 65], [126, 68, 128, 66], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6, "console"], [130, 15, 132, 13], [130, 16, 132, 14, "log"], [130, 19, 132, 17], [130, 20, 132, 18], [130, 73, 132, 71], [130, 74, 132, 72], [131, 8, 133, 6], [131, 14, 133, 12, "processImageWithFaceBlur"], [131, 38, 133, 36], [131, 39, 133, 37, "photo"], [131, 44, 133, 42], [131, 45, 133, 43, "uri"], [131, 48, 133, 46], [131, 49, 133, 47], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "log"], [132, 19, 134, 17], [132, 20, 134, 18], [132, 71, 134, 69], [132, 72, 134, 70], [133, 6, 135, 4], [133, 7, 135, 5], [133, 8, 135, 6], [133, 15, 135, 13, "error"], [133, 20, 135, 18], [133, 22, 135, 20], [134, 8, 136, 6, "console"], [134, 15, 136, 13], [134, 16, 136, 14, "error"], [134, 21, 136, 19], [134, 22, 136, 20], [134, 54, 136, 52], [134, 56, 136, 54, "error"], [134, 61, 136, 59], [134, 62, 136, 60], [135, 8, 137, 6, "setErrorMessage"], [135, 23, 137, 21], [135, 24, 137, 22], [135, 68, 137, 66], [135, 69, 137, 67], [136, 8, 138, 6, "setProcessingState"], [136, 26, 138, 24], [136, 27, 138, 25], [136, 34, 138, 32], [136, 35, 138, 33], [137, 6, 139, 4], [138, 4, 140, 2], [138, 5, 140, 3], [138, 7, 140, 5], [138, 9, 140, 7], [138, 10, 140, 8], [139, 4, 141, 2], [140, 4, 142, 2], [140, 10, 142, 8, "processImageWithFaceBlur"], [140, 34, 142, 32], [140, 37, 142, 35], [140, 43, 142, 42, "photoUri"], [140, 51, 142, 58], [140, 55, 142, 63], [141, 6, 143, 4], [141, 10, 143, 8], [142, 8, 144, 6, "console"], [142, 15, 144, 13], [142, 16, 144, 14, "log"], [142, 19, 144, 17], [142, 20, 144, 18], [142, 84, 144, 82], [142, 85, 144, 83], [143, 8, 145, 6, "setProcessingState"], [143, 26, 145, 24], [143, 27, 145, 25], [143, 39, 145, 37], [143, 40, 145, 38], [144, 8, 146, 6, "setProcessingProgress"], [144, 29, 146, 27], [144, 30, 146, 28], [144, 32, 146, 30], [144, 33, 146, 31], [146, 8, 148, 6], [147, 8, 149, 6], [147, 14, 149, 12, "canvas"], [147, 20, 149, 18], [147, 23, 149, 21, "document"], [147, 31, 149, 29], [147, 32, 149, 30, "createElement"], [147, 45, 149, 43], [147, 46, 149, 44], [147, 54, 149, 52], [147, 55, 149, 53], [148, 8, 150, 6], [148, 14, 150, 12, "ctx"], [148, 17, 150, 15], [148, 20, 150, 18, "canvas"], [148, 26, 150, 24], [148, 27, 150, 25, "getContext"], [148, 37, 150, 35], [148, 38, 150, 36], [148, 42, 150, 40], [148, 43, 150, 41], [149, 8, 151, 6], [149, 12, 151, 10], [149, 13, 151, 11, "ctx"], [149, 16, 151, 14], [149, 18, 151, 16], [149, 24, 151, 22], [149, 28, 151, 26, "Error"], [149, 33, 151, 31], [149, 34, 151, 32], [149, 64, 151, 62], [149, 65, 151, 63], [151, 8, 153, 6], [152, 8, 154, 6], [152, 14, 154, 12, "img"], [152, 17, 154, 15], [152, 20, 154, 18], [152, 24, 154, 22, "Image"], [152, 29, 154, 27], [152, 30, 154, 28], [152, 31, 154, 29], [153, 8, 155, 6], [153, 14, 155, 12], [153, 18, 155, 16, "Promise"], [153, 25, 155, 23], [153, 26, 155, 24], [153, 27, 155, 25, "resolve"], [153, 34, 155, 32], [153, 36, 155, 34, "reject"], [153, 42, 155, 40], [153, 47, 155, 45], [154, 10, 156, 8, "img"], [154, 13, 156, 11], [154, 14, 156, 12, "onload"], [154, 20, 156, 18], [154, 23, 156, 21, "resolve"], [154, 30, 156, 28], [155, 10, 157, 8, "img"], [155, 13, 157, 11], [155, 14, 157, 12, "onerror"], [155, 21, 157, 19], [155, 24, 157, 22, "reject"], [155, 30, 157, 28], [156, 10, 158, 8, "img"], [156, 13, 158, 11], [156, 14, 158, 12, "src"], [156, 17, 158, 15], [156, 20, 158, 18, "photoUri"], [156, 28, 158, 26], [157, 8, 159, 6], [157, 9, 159, 7], [157, 10, 159, 8], [159, 8, 161, 6], [160, 8, 162, 6, "canvas"], [160, 14, 162, 12], [160, 15, 162, 13, "width"], [160, 20, 162, 18], [160, 23, 162, 21, "img"], [160, 26, 162, 24], [160, 27, 162, 25, "width"], [160, 32, 162, 30], [161, 8, 163, 6, "canvas"], [161, 14, 163, 12], [161, 15, 163, 13, "height"], [161, 21, 163, 19], [161, 24, 163, 22, "img"], [161, 27, 163, 25], [161, 28, 163, 26, "height"], [161, 34, 163, 32], [162, 8, 164, 6, "console"], [162, 15, 164, 13], [162, 16, 164, 14, "log"], [162, 19, 164, 17], [162, 20, 164, 18], [162, 54, 164, 52], [162, 56, 164, 54], [163, 10, 164, 56, "width"], [163, 15, 164, 61], [163, 17, 164, 63, "img"], [163, 20, 164, 66], [163, 21, 164, 67, "width"], [163, 26, 164, 72], [164, 10, 164, 74, "height"], [164, 16, 164, 80], [164, 18, 164, 82, "img"], [164, 21, 164, 85], [164, 22, 164, 86, "height"], [165, 8, 164, 93], [165, 9, 164, 94], [165, 10, 164, 95], [167, 8, 166, 6], [168, 8, 167, 6, "ctx"], [168, 11, 167, 9], [168, 12, 167, 10, "drawImage"], [168, 21, 167, 19], [168, 22, 167, 20, "img"], [168, 25, 167, 23], [168, 27, 167, 25], [168, 28, 167, 26], [168, 30, 167, 28], [168, 31, 167, 29], [168, 32, 167, 30], [169, 8, 168, 6, "console"], [169, 15, 168, 13], [169, 16, 168, 14, "log"], [169, 19, 168, 17], [169, 20, 168, 18], [169, 72, 168, 70], [169, 73, 168, 71], [170, 8, 170, 6, "setProcessingProgress"], [170, 29, 170, 27], [170, 30, 170, 28], [170, 32, 170, 30], [170, 33, 170, 31], [172, 8, 172, 6], [173, 8, 173, 6], [173, 12, 173, 10, "detectedFaces"], [173, 25, 173, 23], [173, 28, 173, 26], [173, 30, 173, 28], [174, 8, 175, 6, "console"], [174, 15, 175, 13], [174, 16, 175, 14, "log"], [174, 19, 175, 17], [174, 20, 175, 18], [174, 78, 175, 76], [174, 79, 175, 77], [176, 8, 177, 6], [177, 8, 178, 6], [177, 12, 178, 10], [178, 10, 179, 8], [178, 16, 179, 14, "loadMediaPipeFaceDetection"], [178, 42, 179, 40], [178, 43, 179, 41], [178, 44, 179, 42], [179, 10, 180, 8, "detectedFaces"], [179, 23, 180, 21], [179, 26, 180, 24], [179, 32, 180, 30, "detectFacesWithMediaPipe"], [179, 56, 180, 54], [179, 57, 180, 55, "img"], [179, 60, 180, 58], [179, 61, 180, 59], [180, 10, 181, 8, "console"], [180, 17, 181, 15], [180, 18, 181, 16, "log"], [180, 21, 181, 19], [180, 22, 181, 20], [180, 59, 181, 57, "detectedFaces"], [180, 72, 181, 70], [180, 73, 181, 71, "length"], [180, 79, 181, 77], [180, 87, 181, 85], [180, 88, 181, 86], [181, 8, 182, 6], [181, 9, 182, 7], [181, 10, 182, 8], [181, 17, 182, 15, "mediaPipeError"], [181, 31, 182, 29], [181, 33, 182, 31], [182, 10, 183, 8, "console"], [182, 17, 183, 15], [182, 18, 183, 16, "warn"], [182, 22, 183, 20], [182, 23, 183, 21], [182, 60, 183, 58], [182, 62, 183, 60, "mediaPipeError"], [182, 76, 183, 74], [182, 77, 183, 75], [184, 10, 185, 8], [185, 10, 186, 8, "console"], [185, 17, 186, 15], [185, 18, 186, 16, "log"], [185, 21, 186, 19], [185, 22, 186, 20], [185, 88, 186, 86], [185, 89, 186, 87], [186, 10, 187, 8, "detectedFaces"], [186, 23, 187, 21], [186, 26, 187, 24, "detectFacesHeuristic"], [186, 46, 187, 44], [186, 47, 187, 45, "img"], [186, 50, 187, 48], [186, 52, 187, 50, "ctx"], [186, 55, 187, 53], [186, 56, 187, 54], [187, 10, 188, 8, "console"], [187, 17, 188, 15], [187, 18, 188, 16, "log"], [187, 21, 188, 19], [187, 22, 188, 20], [187, 70, 188, 68, "detectedFaces"], [187, 83, 188, 81], [187, 84, 188, 82, "length"], [187, 90, 188, 88], [187, 98, 188, 96], [187, 99, 188, 97], [188, 8, 189, 6], [189, 8, 191, 6, "console"], [189, 15, 191, 13], [189, 16, 191, 14, "log"], [189, 19, 191, 17], [189, 20, 191, 18], [189, 72, 191, 70, "detectedFaces"], [189, 85, 191, 83], [189, 86, 191, 84, "length"], [189, 92, 191, 90], [189, 100, 191, 98], [189, 101, 191, 99], [190, 8, 192, 6], [190, 12, 192, 10, "detectedFaces"], [190, 25, 192, 23], [190, 26, 192, 24, "length"], [190, 32, 192, 30], [190, 35, 192, 33], [190, 36, 192, 34], [190, 38, 192, 36], [191, 10, 193, 8, "console"], [191, 17, 193, 15], [191, 18, 193, 16, "log"], [191, 21, 193, 19], [191, 22, 193, 20], [191, 66, 193, 64], [191, 68, 193, 66, "detectedFaces"], [191, 81, 193, 79], [191, 82, 193, 80, "map"], [191, 85, 193, 83], [191, 86, 193, 84], [191, 87, 193, 85, "face"], [191, 91, 193, 89], [191, 93, 193, 91, "i"], [191, 94, 193, 92], [191, 100, 193, 98], [192, 12, 194, 10, "faceNumber"], [192, 22, 194, 20], [192, 24, 194, 22, "i"], [192, 25, 194, 23], [192, 28, 194, 26], [192, 29, 194, 27], [193, 12, 195, 10, "centerX"], [193, 19, 195, 17], [193, 21, 195, 19, "face"], [193, 25, 195, 23], [193, 26, 195, 24, "boundingBox"], [193, 37, 195, 35], [193, 38, 195, 36, "xCenter"], [193, 45, 195, 43], [194, 12, 196, 10, "centerY"], [194, 19, 196, 17], [194, 21, 196, 19, "face"], [194, 25, 196, 23], [194, 26, 196, 24, "boundingBox"], [194, 37, 196, 35], [194, 38, 196, 36, "yCenter"], [194, 45, 196, 43], [195, 12, 197, 10, "width"], [195, 17, 197, 15], [195, 19, 197, 17, "face"], [195, 23, 197, 21], [195, 24, 197, 22, "boundingBox"], [195, 35, 197, 33], [195, 36, 197, 34, "width"], [195, 41, 197, 39], [196, 12, 198, 10, "height"], [196, 18, 198, 16], [196, 20, 198, 18, "face"], [196, 24, 198, 22], [196, 25, 198, 23, "boundingBox"], [196, 36, 198, 34], [196, 37, 198, 35, "height"], [197, 10, 199, 8], [197, 11, 199, 9], [197, 12, 199, 10], [197, 13, 199, 11], [197, 14, 199, 12], [198, 8, 200, 6], [198, 9, 200, 7], [198, 15, 200, 13], [199, 10, 201, 8, "console"], [199, 17, 201, 15], [199, 18, 201, 16, "log"], [199, 21, 201, 19], [199, 22, 201, 20], [199, 91, 201, 89], [199, 92, 201, 90], [200, 8, 202, 6], [201, 8, 204, 6, "setProcessingProgress"], [201, 29, 204, 27], [201, 30, 204, 28], [201, 32, 204, 30], [201, 33, 204, 31], [203, 8, 206, 6], [204, 8, 207, 6], [204, 12, 207, 10, "detectedFaces"], [204, 25, 207, 23], [204, 26, 207, 24, "length"], [204, 32, 207, 30], [204, 35, 207, 33], [204, 36, 207, 34], [204, 38, 207, 36], [205, 10, 208, 8, "detectedFaces"], [205, 23, 208, 21], [205, 24, 208, 22, "for<PERSON>ach"], [205, 31, 208, 29], [205, 32, 208, 30], [205, 33, 208, 31, "detection"], [205, 42, 208, 40], [205, 44, 208, 42, "index"], [205, 49, 208, 47], [205, 54, 208, 52], [206, 12, 209, 10], [206, 18, 209, 16, "bbox"], [206, 22, 209, 20], [206, 25, 209, 23, "detection"], [206, 34, 209, 32], [206, 35, 209, 33, "boundingBox"], [206, 46, 209, 44], [208, 12, 211, 10], [209, 12, 212, 10], [209, 18, 212, 16, "faceX"], [209, 23, 212, 21], [209, 26, 212, 24, "bbox"], [209, 30, 212, 28], [209, 31, 212, 29, "xCenter"], [209, 38, 212, 36], [209, 41, 212, 39, "img"], [209, 44, 212, 42], [209, 45, 212, 43, "width"], [209, 50, 212, 48], [209, 53, 212, 52, "bbox"], [209, 57, 212, 56], [209, 58, 212, 57, "width"], [209, 63, 212, 62], [209, 66, 212, 65, "img"], [209, 69, 212, 68], [209, 70, 212, 69, "width"], [209, 75, 212, 74], [209, 78, 212, 78], [209, 79, 212, 79], [210, 12, 213, 10], [210, 18, 213, 16, "faceY"], [210, 23, 213, 21], [210, 26, 213, 24, "bbox"], [210, 30, 213, 28], [210, 31, 213, 29, "yCenter"], [210, 38, 213, 36], [210, 41, 213, 39, "img"], [210, 44, 213, 42], [210, 45, 213, 43, "height"], [210, 51, 213, 49], [210, 54, 213, 53, "bbox"], [210, 58, 213, 57], [210, 59, 213, 58, "height"], [210, 65, 213, 64], [210, 68, 213, 67, "img"], [210, 71, 213, 70], [210, 72, 213, 71, "height"], [210, 78, 213, 77], [210, 81, 213, 81], [210, 82, 213, 82], [211, 12, 214, 10], [211, 18, 214, 16, "faceWidth"], [211, 27, 214, 25], [211, 30, 214, 28, "bbox"], [211, 34, 214, 32], [211, 35, 214, 33, "width"], [211, 40, 214, 38], [211, 43, 214, 41, "img"], [211, 46, 214, 44], [211, 47, 214, 45, "width"], [211, 52, 214, 50], [212, 12, 215, 10], [212, 18, 215, 16, "faceHeight"], [212, 28, 215, 26], [212, 31, 215, 29, "bbox"], [212, 35, 215, 33], [212, 36, 215, 34, "height"], [212, 42, 215, 40], [212, 45, 215, 43, "img"], [212, 48, 215, 46], [212, 49, 215, 47, "height"], [212, 55, 215, 53], [214, 12, 217, 10], [215, 12, 218, 10], [215, 18, 218, 16, "padding"], [215, 25, 218, 23], [215, 28, 218, 26], [215, 31, 218, 29], [215, 32, 218, 30], [215, 33, 218, 31], [216, 12, 219, 10], [216, 18, 219, 16, "paddedX"], [216, 25, 219, 23], [216, 28, 219, 26, "Math"], [216, 32, 219, 30], [216, 33, 219, 31, "max"], [216, 36, 219, 34], [216, 37, 219, 35], [216, 38, 219, 36], [216, 40, 219, 38, "faceX"], [216, 45, 219, 43], [216, 48, 219, 46, "faceWidth"], [216, 57, 219, 55], [216, 60, 219, 58, "padding"], [216, 67, 219, 65], [216, 68, 219, 66], [217, 12, 220, 10], [217, 18, 220, 16, "paddedY"], [217, 25, 220, 23], [217, 28, 220, 26, "Math"], [217, 32, 220, 30], [217, 33, 220, 31, "max"], [217, 36, 220, 34], [217, 37, 220, 35], [217, 38, 220, 36], [217, 40, 220, 38, "faceY"], [217, 45, 220, 43], [217, 48, 220, 46, "faceHeight"], [217, 58, 220, 56], [217, 61, 220, 59, "padding"], [217, 68, 220, 66], [217, 69, 220, 67], [218, 12, 221, 10], [218, 18, 221, 16, "<PERSON><PERSON><PERSON><PERSON>"], [218, 29, 221, 27], [218, 32, 221, 30, "Math"], [218, 36, 221, 34], [218, 37, 221, 35, "min"], [218, 40, 221, 38], [218, 41, 221, 39, "img"], [218, 44, 221, 42], [218, 45, 221, 43, "width"], [218, 50, 221, 48], [218, 53, 221, 51, "paddedX"], [218, 60, 221, 58], [218, 62, 221, 60, "faceWidth"], [218, 71, 221, 69], [218, 75, 221, 73], [218, 76, 221, 74], [218, 79, 221, 77], [218, 80, 221, 78], [218, 83, 221, 81, "padding"], [218, 90, 221, 88], [218, 91, 221, 89], [218, 92, 221, 90], [219, 12, 222, 10], [219, 18, 222, 16, "paddedHeight"], [219, 30, 222, 28], [219, 33, 222, 31, "Math"], [219, 37, 222, 35], [219, 38, 222, 36, "min"], [219, 41, 222, 39], [219, 42, 222, 40, "img"], [219, 45, 222, 43], [219, 46, 222, 44, "height"], [219, 52, 222, 50], [219, 55, 222, 53, "paddedY"], [219, 62, 222, 60], [219, 64, 222, 62, "faceHeight"], [219, 74, 222, 72], [219, 78, 222, 76], [219, 79, 222, 77], [219, 82, 222, 80], [219, 83, 222, 81], [219, 86, 222, 84, "padding"], [219, 93, 222, 91], [219, 94, 222, 92], [219, 95, 222, 93], [220, 12, 224, 10, "console"], [220, 19, 224, 17], [220, 20, 224, 18, "log"], [220, 23, 224, 21], [220, 24, 224, 22], [220, 60, 224, 58, "index"], [220, 65, 224, 63], [220, 68, 224, 66], [220, 69, 224, 67], [220, 77, 224, 75, "Math"], [220, 81, 224, 79], [220, 82, 224, 80, "round"], [220, 87, 224, 85], [220, 88, 224, 86, "paddedX"], [220, 95, 224, 93], [220, 96, 224, 94], [220, 101, 224, 99, "Math"], [220, 105, 224, 103], [220, 106, 224, 104, "round"], [220, 111, 224, 109], [220, 112, 224, 110, "paddedY"], [220, 119, 224, 117], [220, 120, 224, 118], [220, 130, 224, 128, "Math"], [220, 134, 224, 132], [220, 135, 224, 133, "round"], [220, 140, 224, 138], [220, 141, 224, 139, "<PERSON><PERSON><PERSON><PERSON>"], [220, 152, 224, 150], [220, 153, 224, 151], [220, 157, 224, 155, "Math"], [220, 161, 224, 159], [220, 162, 224, 160, "round"], [220, 167, 224, 165], [220, 168, 224, 166, "paddedHeight"], [220, 180, 224, 178], [220, 181, 224, 179], [220, 183, 224, 181], [220, 184, 224, 182], [222, 12, 226, 10], [223, 12, 227, 10], [223, 18, 227, 16, "faceImageData"], [223, 31, 227, 29], [223, 34, 227, 32, "ctx"], [223, 37, 227, 35], [223, 38, 227, 36, "getImageData"], [223, 50, 227, 48], [223, 51, 227, 49, "paddedX"], [223, 58, 227, 56], [223, 60, 227, 58, "paddedY"], [223, 67, 227, 65], [223, 69, 227, 67, "<PERSON><PERSON><PERSON><PERSON>"], [223, 80, 227, 78], [223, 82, 227, 80, "paddedHeight"], [223, 94, 227, 92], [223, 95, 227, 93], [224, 12, 228, 10], [224, 18, 228, 16, "data"], [224, 22, 228, 20], [224, 25, 228, 23, "faceImageData"], [224, 38, 228, 36], [224, 39, 228, 37, "data"], [224, 43, 228, 41], [225, 12, 230, 10, "console"], [225, 19, 230, 17], [225, 20, 230, 18, "log"], [225, 23, 230, 21], [225, 24, 230, 22], [225, 64, 230, 62, "data"], [225, 68, 230, 66], [225, 69, 230, 67, "length"], [225, 75, 230, 73], [225, 86, 230, 84, "<PERSON><PERSON><PERSON><PERSON>"], [225, 97, 230, 95], [225, 101, 230, 99, "paddedHeight"], [225, 113, 230, 111], [225, 122, 230, 120], [225, 123, 230, 121], [227, 12, 232, 10], [228, 12, 233, 10], [228, 18, 233, 16, "pixelSize"], [228, 27, 233, 25], [228, 30, 233, 28, "Math"], [228, 34, 233, 32], [228, 35, 233, 33, "max"], [228, 38, 233, 36], [228, 39, 233, 37], [228, 41, 233, 39], [228, 43, 233, 41, "Math"], [228, 47, 233, 45], [228, 48, 233, 46, "min"], [228, 51, 233, 49], [228, 52, 233, 50, "<PERSON><PERSON><PERSON><PERSON>"], [228, 63, 233, 61], [228, 65, 233, 63, "paddedHeight"], [228, 77, 233, 75], [228, 78, 233, 76], [228, 81, 233, 79], [228, 82, 233, 80], [228, 83, 233, 81], [228, 84, 233, 82], [228, 85, 233, 83], [229, 12, 234, 10, "console"], [229, 19, 234, 17], [229, 20, 234, 18, "log"], [229, 23, 234, 21], [229, 24, 234, 22], [229, 70, 234, 68, "pixelSize"], [229, 79, 234, 77], [229, 111, 234, 109], [229, 112, 234, 110], [230, 12, 235, 10], [230, 17, 235, 15], [230, 21, 235, 19, "y"], [230, 22, 235, 20], [230, 25, 235, 23], [230, 26, 235, 24], [230, 28, 235, 26, "y"], [230, 29, 235, 27], [230, 32, 235, 30, "paddedHeight"], [230, 44, 235, 42], [230, 46, 235, 44, "y"], [230, 47, 235, 45], [230, 51, 235, 49, "pixelSize"], [230, 60, 235, 58], [230, 62, 235, 60], [231, 14, 236, 12], [231, 19, 236, 17], [231, 23, 236, 21, "x"], [231, 24, 236, 22], [231, 27, 236, 25], [231, 28, 236, 26], [231, 30, 236, 28, "x"], [231, 31, 236, 29], [231, 34, 236, 32, "<PERSON><PERSON><PERSON><PERSON>"], [231, 45, 236, 43], [231, 47, 236, 45, "x"], [231, 48, 236, 46], [231, 52, 236, 50, "pixelSize"], [231, 61, 236, 59], [231, 63, 236, 61], [232, 16, 237, 14], [233, 16, 238, 14], [233, 22, 238, 20, "pixelIndex"], [233, 32, 238, 30], [233, 35, 238, 33], [233, 36, 238, 34, "y"], [233, 37, 238, 35], [233, 40, 238, 38, "<PERSON><PERSON><PERSON><PERSON>"], [233, 51, 238, 49], [233, 54, 238, 52, "x"], [233, 55, 238, 53], [233, 59, 238, 57], [233, 60, 238, 58], [234, 16, 239, 14], [234, 22, 239, 20, "r"], [234, 23, 239, 21], [234, 26, 239, 24, "data"], [234, 30, 239, 28], [234, 31, 239, 29, "pixelIndex"], [234, 41, 239, 39], [234, 42, 239, 40], [235, 16, 240, 14], [235, 22, 240, 20, "g"], [235, 23, 240, 21], [235, 26, 240, 24, "data"], [235, 30, 240, 28], [235, 31, 240, 29, "pixelIndex"], [235, 41, 240, 39], [235, 44, 240, 42], [235, 45, 240, 43], [235, 46, 240, 44], [236, 16, 241, 14], [236, 22, 241, 20, "b"], [236, 23, 241, 21], [236, 26, 241, 24, "data"], [236, 30, 241, 28], [236, 31, 241, 29, "pixelIndex"], [236, 41, 241, 39], [236, 44, 241, 42], [236, 45, 241, 43], [236, 46, 241, 44], [237, 16, 242, 14], [237, 22, 242, 20, "a"], [237, 23, 242, 21], [237, 26, 242, 24, "data"], [237, 30, 242, 28], [237, 31, 242, 29, "pixelIndex"], [237, 41, 242, 39], [237, 44, 242, 42], [237, 45, 242, 43], [237, 46, 242, 44], [239, 16, 244, 14], [240, 16, 245, 14], [240, 21, 245, 19], [240, 25, 245, 23, "dy"], [240, 27, 245, 25], [240, 30, 245, 28], [240, 31, 245, 29], [240, 33, 245, 31, "dy"], [240, 35, 245, 33], [240, 38, 245, 36, "pixelSize"], [240, 47, 245, 45], [240, 51, 245, 49, "y"], [240, 52, 245, 50], [240, 55, 245, 53, "dy"], [240, 57, 245, 55], [240, 60, 245, 58, "paddedHeight"], [240, 72, 245, 70], [240, 74, 245, 72, "dy"], [240, 76, 245, 74], [240, 78, 245, 76], [240, 80, 245, 78], [241, 18, 246, 16], [241, 23, 246, 21], [241, 27, 246, 25, "dx"], [241, 29, 246, 27], [241, 32, 246, 30], [241, 33, 246, 31], [241, 35, 246, 33, "dx"], [241, 37, 246, 35], [241, 40, 246, 38, "pixelSize"], [241, 49, 246, 47], [241, 53, 246, 51, "x"], [241, 54, 246, 52], [241, 57, 246, 55, "dx"], [241, 59, 246, 57], [241, 62, 246, 60, "<PERSON><PERSON><PERSON><PERSON>"], [241, 73, 246, 71], [241, 75, 246, 73, "dx"], [241, 77, 246, 75], [241, 79, 246, 77], [241, 81, 246, 79], [242, 20, 247, 18], [242, 26, 247, 24, "blockPixelIndex"], [242, 41, 247, 39], [242, 44, 247, 42], [242, 45, 247, 43], [242, 46, 247, 44, "y"], [242, 47, 247, 45], [242, 50, 247, 48, "dy"], [242, 52, 247, 50], [242, 56, 247, 54, "<PERSON><PERSON><PERSON><PERSON>"], [242, 67, 247, 65], [242, 71, 247, 69, "x"], [242, 72, 247, 70], [242, 75, 247, 73, "dx"], [242, 77, 247, 75], [242, 78, 247, 76], [242, 82, 247, 80], [242, 83, 247, 81], [243, 20, 248, 18, "data"], [243, 24, 248, 22], [243, 25, 248, 23, "blockPixelIndex"], [243, 40, 248, 38], [243, 41, 248, 39], [243, 44, 248, 42, "r"], [243, 45, 248, 43], [244, 20, 249, 18, "data"], [244, 24, 249, 22], [244, 25, 249, 23, "blockPixelIndex"], [244, 40, 249, 38], [244, 43, 249, 41], [244, 44, 249, 42], [244, 45, 249, 43], [244, 48, 249, 46, "g"], [244, 49, 249, 47], [245, 20, 250, 18, "data"], [245, 24, 250, 22], [245, 25, 250, 23, "blockPixelIndex"], [245, 40, 250, 38], [245, 43, 250, 41], [245, 44, 250, 42], [245, 45, 250, 43], [245, 48, 250, 46, "b"], [245, 49, 250, 47], [246, 20, 251, 18, "data"], [246, 24, 251, 22], [246, 25, 251, 23, "blockPixelIndex"], [246, 40, 251, 38], [246, 43, 251, 41], [246, 44, 251, 42], [246, 45, 251, 43], [246, 48, 251, 46, "a"], [246, 49, 251, 47], [247, 18, 252, 16], [248, 16, 253, 14], [249, 14, 254, 12], [250, 12, 255, 10], [252, 12, 257, 10], [253, 12, 258, 10, "ctx"], [253, 15, 258, 13], [253, 16, 258, 14, "putImageData"], [253, 28, 258, 26], [253, 29, 258, 27, "faceImageData"], [253, 42, 258, 40], [253, 44, 258, 42, "paddedX"], [253, 51, 258, 49], [253, 53, 258, 51, "paddedY"], [253, 60, 258, 58], [253, 61, 258, 59], [254, 12, 259, 10, "console"], [254, 19, 259, 17], [254, 20, 259, 18, "log"], [254, 23, 259, 21], [254, 24, 259, 22], [254, 50, 259, 48, "index"], [254, 55, 259, 53], [254, 58, 259, 56], [254, 59, 259, 57], [254, 91, 259, 89], [254, 92, 259, 90], [255, 10, 260, 8], [255, 11, 260, 9], [255, 12, 260, 10], [256, 10, 261, 8, "console"], [256, 17, 261, 15], [256, 18, 261, 16, "log"], [256, 21, 261, 19], [256, 22, 261, 20], [256, 48, 261, 46, "detectedFaces"], [256, 61, 261, 59], [256, 62, 261, 60, "length"], [256, 68, 261, 66], [256, 95, 261, 93], [256, 96, 261, 94], [257, 8, 262, 6], [257, 9, 262, 7], [257, 15, 262, 13], [258, 10, 263, 8, "console"], [258, 17, 263, 15], [258, 18, 263, 16, "log"], [258, 21, 263, 19], [258, 22, 263, 20], [258, 91, 263, 89], [258, 92, 263, 90], [259, 8, 264, 6], [260, 8, 266, 6, "setProcessingProgress"], [260, 29, 266, 27], [260, 30, 266, 28], [260, 32, 266, 30], [260, 33, 266, 31], [262, 8, 268, 6], [263, 8, 269, 6, "console"], [263, 15, 269, 13], [263, 16, 269, 14, "log"], [263, 19, 269, 17], [263, 20, 269, 18], [263, 85, 269, 83], [263, 86, 269, 84], [264, 8, 270, 6], [264, 14, 270, 12, "blurredImageBlob"], [264, 30, 270, 28], [264, 33, 270, 31], [264, 39, 270, 37], [264, 43, 270, 41, "Promise"], [264, 50, 270, 48], [264, 51, 270, 56, "resolve"], [264, 58, 270, 63], [264, 62, 270, 68], [265, 10, 271, 8, "canvas"], [265, 16, 271, 14], [265, 17, 271, 15, "toBlob"], [265, 23, 271, 21], [265, 24, 271, 23, "blob"], [265, 28, 271, 27], [265, 32, 271, 32, "resolve"], [265, 39, 271, 39], [265, 40, 271, 40, "blob"], [265, 44, 271, 45], [265, 45, 271, 46], [265, 47, 271, 48], [265, 59, 271, 60], [265, 61, 271, 62], [265, 64, 271, 65], [265, 65, 271, 66], [266, 8, 272, 6], [266, 9, 272, 7], [266, 10, 272, 8], [267, 8, 274, 6], [267, 14, 274, 12, "blurredImageUrl"], [267, 29, 274, 27], [267, 32, 274, 30, "URL"], [267, 35, 274, 33], [267, 36, 274, 34, "createObjectURL"], [267, 51, 274, 49], [267, 52, 274, 50, "blurredImageBlob"], [267, 68, 274, 66], [267, 69, 274, 67], [268, 8, 275, 6, "console"], [268, 15, 275, 13], [268, 16, 275, 14, "log"], [268, 19, 275, 17], [268, 20, 275, 18], [268, 66, 275, 64], [268, 68, 275, 66, "blurredImageUrl"], [268, 83, 275, 81], [268, 84, 275, 82, "substring"], [268, 93, 275, 91], [268, 94, 275, 92], [268, 95, 275, 93], [268, 97, 275, 95], [268, 99, 275, 97], [268, 100, 275, 98], [268, 103, 275, 101], [268, 108, 275, 106], [268, 109, 275, 107], [269, 8, 277, 6, "setProcessingProgress"], [269, 29, 277, 27], [269, 30, 277, 28], [269, 33, 277, 31], [269, 34, 277, 32], [271, 8, 279, 6], [272, 8, 280, 6], [272, 14, 280, 12, "completeProcessing"], [272, 32, 280, 30], [272, 33, 280, 31, "blurredImageUrl"], [272, 48, 280, 46], [272, 49, 280, 47], [273, 6, 282, 4], [273, 7, 282, 5], [273, 8, 282, 6], [273, 15, 282, 13, "error"], [273, 20, 282, 18], [273, 22, 282, 20], [274, 8, 283, 6, "console"], [274, 15, 283, 13], [274, 16, 283, 14, "error"], [274, 21, 283, 19], [274, 22, 283, 20], [274, 57, 283, 55], [274, 59, 283, 57, "error"], [274, 64, 283, 62], [274, 65, 283, 63], [275, 8, 284, 6, "setErrorMessage"], [275, 23, 284, 21], [275, 24, 284, 22], [275, 50, 284, 48], [275, 51, 284, 49], [276, 8, 285, 6, "setProcessingState"], [276, 26, 285, 24], [276, 27, 285, 25], [276, 34, 285, 32], [276, 35, 285, 33], [277, 6, 286, 4], [278, 4, 287, 2], [278, 5, 287, 3], [280, 4, 289, 2], [281, 4, 290, 2], [281, 10, 290, 8, "completeProcessing"], [281, 28, 290, 26], [281, 31, 290, 29], [281, 37, 290, 36, "blurredImageUrl"], [281, 52, 290, 59], [281, 56, 290, 64], [282, 6, 291, 4], [282, 10, 291, 8], [283, 8, 292, 6, "setProcessingState"], [283, 26, 292, 24], [283, 27, 292, 25], [283, 37, 292, 35], [283, 38, 292, 36], [285, 8, 294, 6], [286, 8, 295, 6], [286, 14, 295, 12, "timestamp"], [286, 23, 295, 21], [286, 26, 295, 24, "Date"], [286, 30, 295, 28], [286, 31, 295, 29, "now"], [286, 34, 295, 32], [286, 35, 295, 33], [286, 36, 295, 34], [287, 8, 296, 6], [287, 14, 296, 12, "result"], [287, 20, 296, 18], [287, 23, 296, 21], [288, 10, 297, 8, "imageUrl"], [288, 18, 297, 16], [288, 20, 297, 18, "blurredImageUrl"], [288, 35, 297, 33], [289, 10, 298, 8, "localUri"], [289, 18, 298, 16], [289, 20, 298, 18, "blurredImageUrl"], [289, 35, 298, 33], [290, 10, 299, 8, "challengeCode"], [290, 23, 299, 21], [290, 25, 299, 23, "challengeCode"], [290, 38, 299, 36], [290, 42, 299, 40], [290, 44, 299, 42], [291, 10, 300, 8, "timestamp"], [291, 19, 300, 17], [292, 10, 301, 8, "jobId"], [292, 15, 301, 13], [292, 17, 301, 15], [292, 27, 301, 25, "timestamp"], [292, 36, 301, 34], [292, 38, 301, 36], [293, 10, 302, 8, "status"], [293, 16, 302, 14], [293, 18, 302, 16], [294, 8, 303, 6], [294, 9, 303, 7], [295, 8, 305, 6, "console"], [295, 15, 305, 13], [295, 16, 305, 14, "log"], [295, 19, 305, 17], [295, 20, 305, 18], [295, 100, 305, 98], [295, 102, 305, 100], [296, 10, 306, 8, "imageUrl"], [296, 18, 306, 16], [296, 20, 306, 18, "blurredImageUrl"], [296, 35, 306, 33], [296, 36, 306, 34, "substring"], [296, 45, 306, 43], [296, 46, 306, 44], [296, 47, 306, 45], [296, 49, 306, 47], [296, 51, 306, 49], [296, 52, 306, 50], [296, 55, 306, 53], [296, 60, 306, 58], [297, 10, 307, 8, "timestamp"], [297, 19, 307, 17], [298, 10, 308, 8, "jobId"], [298, 15, 308, 13], [298, 17, 308, 15, "result"], [298, 23, 308, 21], [298, 24, 308, 22, "jobId"], [299, 8, 309, 6], [299, 9, 309, 7], [299, 10, 309, 8], [301, 8, 311, 6], [302, 8, 312, 6, "onComplete"], [302, 18, 312, 16], [302, 19, 312, 17, "result"], [302, 25, 312, 23], [302, 26, 312, 24], [303, 6, 314, 4], [303, 7, 314, 5], [303, 8, 314, 6], [303, 15, 314, 13, "error"], [303, 20, 314, 18], [303, 22, 314, 20], [304, 8, 315, 6, "console"], [304, 15, 315, 13], [304, 16, 315, 14, "error"], [304, 21, 315, 19], [304, 22, 315, 20], [304, 57, 315, 55], [304, 59, 315, 57, "error"], [304, 64, 315, 62], [304, 65, 315, 63], [305, 8, 316, 6, "setErrorMessage"], [305, 23, 316, 21], [305, 24, 316, 22], [305, 56, 316, 54], [305, 57, 316, 55], [306, 8, 317, 6, "setProcessingState"], [306, 26, 317, 24], [306, 27, 317, 25], [306, 34, 317, 32], [306, 35, 317, 33], [307, 6, 318, 4], [308, 4, 319, 2], [308, 5, 319, 3], [310, 4, 321, 2], [311, 4, 322, 2], [311, 10, 322, 8, "triggerServerProcessing"], [311, 33, 322, 31], [311, 36, 322, 34], [311, 42, 322, 34, "triggerServerProcessing"], [311, 43, 322, 41, "privateImageUrl"], [311, 58, 322, 64], [311, 60, 322, 66, "timestamp"], [311, 69, 322, 83], [311, 74, 322, 88], [312, 6, 323, 4], [312, 10, 323, 8], [313, 8, 324, 6, "console"], [313, 15, 324, 13], [313, 16, 324, 14, "log"], [313, 19, 324, 17], [313, 20, 324, 18], [313, 74, 324, 72], [313, 76, 324, 74, "privateImageUrl"], [313, 91, 324, 89], [313, 92, 324, 90], [314, 8, 325, 6, "setProcessingState"], [314, 26, 325, 24], [314, 27, 325, 25], [314, 39, 325, 37], [314, 40, 325, 38], [315, 8, 326, 6, "setProcessingProgress"], [315, 29, 326, 27], [315, 30, 326, 28], [315, 32, 326, 30], [315, 33, 326, 31], [316, 8, 328, 6], [316, 14, 328, 12, "requestBody"], [316, 25, 328, 23], [316, 28, 328, 26], [317, 10, 329, 8, "imageUrl"], [317, 18, 329, 16], [317, 20, 329, 18, "privateImageUrl"], [317, 35, 329, 33], [318, 10, 330, 8, "userId"], [318, 16, 330, 14], [319, 10, 331, 8, "requestId"], [319, 19, 331, 17], [320, 10, 332, 8, "timestamp"], [320, 19, 332, 17], [321, 10, 333, 8, "platform"], [321, 18, 333, 16], [321, 20, 333, 18], [322, 8, 334, 6], [322, 9, 334, 7], [323, 8, 336, 6, "console"], [323, 15, 336, 13], [323, 16, 336, 14, "log"], [323, 19, 336, 17], [323, 20, 336, 18], [323, 65, 336, 63], [323, 67, 336, 65, "requestBody"], [323, 78, 336, 76], [323, 79, 336, 77], [325, 8, 338, 6], [326, 8, 339, 6], [326, 14, 339, 12, "response"], [326, 22, 339, 20], [326, 25, 339, 23], [326, 31, 339, 29, "fetch"], [326, 36, 339, 34], [326, 37, 339, 35], [326, 40, 339, 38, "API_BASE_URL"], [326, 52, 339, 50], [326, 72, 339, 70], [326, 74, 339, 72], [327, 10, 340, 8, "method"], [327, 16, 340, 14], [327, 18, 340, 16], [327, 24, 340, 22], [328, 10, 341, 8, "headers"], [328, 17, 341, 15], [328, 19, 341, 17], [329, 12, 342, 10], [329, 26, 342, 24], [329, 28, 342, 26], [329, 46, 342, 44], [330, 12, 343, 10], [330, 27, 343, 25], [330, 29, 343, 27], [330, 39, 343, 37], [330, 45, 343, 43, "getAuthToken"], [330, 57, 343, 55], [330, 58, 343, 56], [330, 59, 343, 57], [331, 10, 344, 8], [331, 11, 344, 9], [332, 10, 345, 8, "body"], [332, 14, 345, 12], [332, 16, 345, 14, "JSON"], [332, 20, 345, 18], [332, 21, 345, 19, "stringify"], [332, 30, 345, 28], [332, 31, 345, 29, "requestBody"], [332, 42, 345, 40], [333, 8, 346, 6], [333, 9, 346, 7], [333, 10, 346, 8], [334, 8, 348, 6], [334, 12, 348, 10], [334, 13, 348, 11, "response"], [334, 21, 348, 19], [334, 22, 348, 20, "ok"], [334, 24, 348, 22], [334, 26, 348, 24], [335, 10, 349, 8], [335, 16, 349, 14, "errorText"], [335, 25, 349, 23], [335, 28, 349, 26], [335, 34, 349, 32, "response"], [335, 42, 349, 40], [335, 43, 349, 41, "text"], [335, 47, 349, 45], [335, 48, 349, 46], [335, 49, 349, 47], [336, 10, 350, 8, "console"], [336, 17, 350, 15], [336, 18, 350, 16, "error"], [336, 23, 350, 21], [336, 24, 350, 22], [336, 68, 350, 66], [336, 70, 350, 68, "response"], [336, 78, 350, 76], [336, 79, 350, 77, "status"], [336, 85, 350, 83], [336, 87, 350, 85, "errorText"], [336, 96, 350, 94], [336, 97, 350, 95], [337, 10, 351, 8], [337, 16, 351, 14], [337, 20, 351, 18, "Error"], [337, 25, 351, 23], [337, 26, 351, 24], [337, 48, 351, 46, "response"], [337, 56, 351, 54], [337, 57, 351, 55, "status"], [337, 63, 351, 61], [337, 67, 351, 65, "response"], [337, 75, 351, 73], [337, 76, 351, 74, "statusText"], [337, 86, 351, 84], [337, 88, 351, 86], [337, 89, 351, 87], [338, 8, 352, 6], [339, 8, 354, 6], [339, 14, 354, 12, "result"], [339, 20, 354, 18], [339, 23, 354, 21], [339, 29, 354, 27, "response"], [339, 37, 354, 35], [339, 38, 354, 36, "json"], [339, 42, 354, 40], [339, 43, 354, 41], [339, 44, 354, 42], [340, 8, 355, 6, "console"], [340, 15, 355, 13], [340, 16, 355, 14, "log"], [340, 19, 355, 17], [340, 20, 355, 18], [340, 68, 355, 66], [340, 70, 355, 68, "result"], [340, 76, 355, 74], [340, 77, 355, 75], [341, 8, 357, 6], [341, 12, 357, 10], [341, 13, 357, 11, "result"], [341, 19, 357, 17], [341, 20, 357, 18, "jobId"], [341, 25, 357, 23], [341, 27, 357, 25], [342, 10, 358, 8], [342, 16, 358, 14], [342, 20, 358, 18, "Error"], [342, 25, 358, 23], [342, 26, 358, 24], [342, 70, 358, 68], [342, 71, 358, 69], [343, 8, 359, 6], [345, 8, 361, 6], [346, 8, 362, 6], [346, 14, 362, 12, "pollForCompletion"], [346, 31, 362, 29], [346, 32, 362, 30, "result"], [346, 38, 362, 36], [346, 39, 362, 37, "jobId"], [346, 44, 362, 42], [346, 46, 362, 44, "timestamp"], [346, 55, 362, 53], [346, 56, 362, 54], [347, 6, 363, 4], [347, 7, 363, 5], [347, 8, 363, 6], [347, 15, 363, 13, "error"], [347, 20, 363, 18], [347, 22, 363, 20], [348, 8, 364, 6, "console"], [348, 15, 364, 13], [348, 16, 364, 14, "error"], [348, 21, 364, 19], [348, 22, 364, 20], [348, 57, 364, 55], [348, 59, 364, 57, "error"], [348, 64, 364, 62], [348, 65, 364, 63], [349, 8, 365, 6, "setErrorMessage"], [349, 23, 365, 21], [349, 24, 365, 22], [349, 52, 365, 50, "error"], [349, 57, 365, 55], [349, 58, 365, 56, "message"], [349, 65, 365, 63], [349, 67, 365, 65], [349, 68, 365, 66], [350, 8, 366, 6, "setProcessingState"], [350, 26, 366, 24], [350, 27, 366, 25], [350, 34, 366, 32], [350, 35, 366, 33], [351, 6, 367, 4], [352, 4, 368, 2], [352, 5, 368, 3], [353, 4, 369, 2], [354, 4, 370, 2], [354, 10, 370, 8, "pollForCompletion"], [354, 27, 370, 25], [354, 30, 370, 28], [354, 36, 370, 28, "pollForCompletion"], [354, 37, 370, 35, "jobId"], [354, 42, 370, 48], [354, 44, 370, 50, "timestamp"], [354, 53, 370, 67], [354, 55, 370, 69, "attempts"], [354, 63, 370, 77], [354, 66, 370, 80], [354, 67, 370, 81], [354, 72, 370, 86], [355, 6, 371, 4], [355, 12, 371, 10, "MAX_ATTEMPTS"], [355, 24, 371, 22], [355, 27, 371, 25], [355, 29, 371, 27], [355, 30, 371, 28], [355, 31, 371, 29], [356, 6, 372, 4], [356, 12, 372, 10, "POLL_INTERVAL"], [356, 25, 372, 23], [356, 28, 372, 26], [356, 32, 372, 30], [356, 33, 372, 31], [356, 34, 372, 32], [358, 6, 374, 4, "console"], [358, 13, 374, 11], [358, 14, 374, 12, "log"], [358, 17, 374, 15], [358, 18, 374, 16], [358, 53, 374, 51, "attempts"], [358, 61, 374, 59], [358, 64, 374, 62], [358, 65, 374, 63], [358, 69, 374, 67, "MAX_ATTEMPTS"], [358, 81, 374, 79], [358, 93, 374, 91, "jobId"], [358, 98, 374, 96], [358, 100, 374, 98], [358, 101, 374, 99], [359, 6, 376, 4], [359, 10, 376, 8, "attempts"], [359, 18, 376, 16], [359, 22, 376, 20, "MAX_ATTEMPTS"], [359, 34, 376, 32], [359, 36, 376, 34], [360, 8, 377, 6, "console"], [360, 15, 377, 13], [360, 16, 377, 14, "error"], [360, 21, 377, 19], [360, 22, 377, 20], [360, 75, 377, 73], [360, 76, 377, 74], [361, 8, 378, 6, "setErrorMessage"], [361, 23, 378, 21], [361, 24, 378, 22], [361, 63, 378, 61], [361, 64, 378, 62], [362, 8, 379, 6, "setProcessingState"], [362, 26, 379, 24], [362, 27, 379, 25], [362, 34, 379, 32], [362, 35, 379, 33], [363, 8, 380, 6], [364, 6, 381, 4], [365, 6, 383, 4], [365, 10, 383, 8], [366, 8, 384, 6], [366, 14, 384, 12, "response"], [366, 22, 384, 20], [366, 25, 384, 23], [366, 31, 384, 29, "fetch"], [366, 36, 384, 34], [366, 37, 384, 35], [366, 40, 384, 38, "API_BASE_URL"], [366, 52, 384, 50], [366, 75, 384, 73, "jobId"], [366, 80, 384, 78], [366, 82, 384, 80], [366, 84, 384, 82], [367, 10, 385, 8, "headers"], [367, 17, 385, 15], [367, 19, 385, 17], [368, 12, 386, 10], [368, 27, 386, 25], [368, 29, 386, 27], [368, 39, 386, 37], [368, 45, 386, 43, "getAuthToken"], [368, 57, 386, 55], [368, 58, 386, 56], [368, 59, 386, 57], [369, 10, 387, 8], [370, 8, 388, 6], [370, 9, 388, 7], [370, 10, 388, 8], [371, 8, 390, 6], [371, 12, 390, 10], [371, 13, 390, 11, "response"], [371, 21, 390, 19], [371, 22, 390, 20, "ok"], [371, 24, 390, 22], [371, 26, 390, 24], [372, 10, 391, 8], [372, 16, 391, 14], [372, 20, 391, 18, "Error"], [372, 25, 391, 23], [372, 26, 391, 24], [372, 34, 391, 32, "response"], [372, 42, 391, 40], [372, 43, 391, 41, "status"], [372, 49, 391, 47], [372, 54, 391, 52, "response"], [372, 62, 391, 60], [372, 63, 391, 61, "statusText"], [372, 73, 391, 71], [372, 75, 391, 73], [372, 76, 391, 74], [373, 8, 392, 6], [374, 8, 394, 6], [374, 14, 394, 12, "status"], [374, 20, 394, 18], [374, 23, 394, 21], [374, 29, 394, 27, "response"], [374, 37, 394, 35], [374, 38, 394, 36, "json"], [374, 42, 394, 40], [374, 43, 394, 41], [374, 44, 394, 42], [375, 8, 395, 6, "console"], [375, 15, 395, 13], [375, 16, 395, 14, "log"], [375, 19, 395, 17], [375, 20, 395, 18], [375, 54, 395, 52], [375, 56, 395, 54, "status"], [375, 62, 395, 60], [375, 63, 395, 61], [376, 8, 397, 6], [376, 12, 397, 10, "status"], [376, 18, 397, 16], [376, 19, 397, 17, "status"], [376, 25, 397, 23], [376, 30, 397, 28], [376, 41, 397, 39], [376, 43, 397, 41], [377, 10, 398, 8, "console"], [377, 17, 398, 15], [377, 18, 398, 16, "log"], [377, 21, 398, 19], [377, 22, 398, 20], [377, 73, 398, 71], [377, 74, 398, 72], [378, 10, 399, 8, "setProcessingProgress"], [378, 31, 399, 29], [378, 32, 399, 30], [378, 35, 399, 33], [378, 36, 399, 34], [379, 10, 400, 8, "setProcessingState"], [379, 28, 400, 26], [379, 29, 400, 27], [379, 40, 400, 38], [379, 41, 400, 39], [380, 10, 401, 8], [381, 10, 402, 8], [381, 16, 402, 14, "result"], [381, 22, 402, 20], [381, 25, 402, 23], [382, 12, 403, 10, "imageUrl"], [382, 20, 403, 18], [382, 22, 403, 20, "status"], [382, 28, 403, 26], [382, 29, 403, 27, "publicUrl"], [382, 38, 403, 36], [383, 12, 403, 38], [384, 12, 404, 10, "localUri"], [384, 20, 404, 18], [384, 22, 404, 20, "capturedPhoto"], [384, 35, 404, 33], [384, 39, 404, 37, "status"], [384, 45, 404, 43], [384, 46, 404, 44, "publicUrl"], [384, 55, 404, 53], [385, 12, 404, 55], [386, 12, 405, 10, "challengeCode"], [386, 25, 405, 23], [386, 27, 405, 25, "challengeCode"], [386, 40, 405, 38], [386, 44, 405, 42], [386, 46, 405, 44], [387, 12, 406, 10, "timestamp"], [387, 21, 406, 19], [388, 12, 407, 10, "processingStatus"], [388, 28, 407, 26], [388, 30, 407, 28], [389, 10, 408, 8], [389, 11, 408, 9], [390, 10, 409, 8, "console"], [390, 17, 409, 15], [390, 18, 409, 16, "log"], [390, 21, 409, 19], [390, 22, 409, 20], [390, 57, 409, 55], [390, 59, 409, 57, "result"], [390, 65, 409, 63], [390, 66, 409, 64], [391, 10, 410, 8, "onComplete"], [391, 20, 410, 18], [391, 21, 410, 19, "result"], [391, 27, 410, 25], [391, 28, 410, 26], [392, 10, 411, 8], [393, 8, 412, 6], [393, 9, 412, 7], [393, 15, 412, 13], [393, 19, 412, 17, "status"], [393, 25, 412, 23], [393, 26, 412, 24, "status"], [393, 32, 412, 30], [393, 37, 412, 35], [393, 45, 412, 43], [393, 47, 412, 45], [394, 10, 413, 8, "console"], [394, 17, 413, 15], [394, 18, 413, 16, "error"], [394, 23, 413, 21], [394, 24, 413, 22], [394, 60, 413, 58], [394, 62, 413, 60, "status"], [394, 68, 413, 66], [394, 69, 413, 67, "error"], [394, 74, 413, 72], [394, 75, 413, 73], [395, 10, 414, 8], [395, 16, 414, 14], [395, 20, 414, 18, "Error"], [395, 25, 414, 23], [395, 26, 414, 24, "status"], [395, 32, 414, 30], [395, 33, 414, 31, "error"], [395, 38, 414, 36], [395, 42, 414, 40], [395, 61, 414, 59], [395, 62, 414, 60], [396, 8, 415, 6], [396, 9, 415, 7], [396, 15, 415, 13], [397, 10, 416, 8], [398, 10, 417, 8], [398, 16, 417, 14, "progressValue"], [398, 29, 417, 27], [398, 32, 417, 30], [398, 34, 417, 32], [398, 37, 417, 36, "attempts"], [398, 45, 417, 44], [398, 48, 417, 47, "MAX_ATTEMPTS"], [398, 60, 417, 59], [398, 63, 417, 63], [398, 65, 417, 65], [399, 10, 418, 8, "console"], [399, 17, 418, 15], [399, 18, 418, 16, "log"], [399, 21, 418, 19], [399, 22, 418, 20], [399, 71, 418, 69, "progressValue"], [399, 84, 418, 82], [399, 87, 418, 85], [399, 88, 418, 86], [400, 10, 419, 8, "setProcessingProgress"], [400, 31, 419, 29], [400, 32, 419, 30, "progressValue"], [400, 45, 419, 43], [400, 46, 419, 44], [401, 10, 421, 8, "setTimeout"], [401, 20, 421, 18], [401, 21, 421, 19], [401, 27, 421, 25], [402, 12, 422, 10, "pollForCompletion"], [402, 29, 422, 27], [402, 30, 422, 28, "jobId"], [402, 35, 422, 33], [402, 37, 422, 35, "timestamp"], [402, 46, 422, 44], [402, 48, 422, 46, "attempts"], [402, 56, 422, 54], [402, 59, 422, 57], [402, 60, 422, 58], [402, 61, 422, 59], [403, 10, 423, 8], [403, 11, 423, 9], [403, 13, 423, 11, "POLL_INTERVAL"], [403, 26, 423, 24], [403, 27, 423, 25], [404, 8, 424, 6], [405, 6, 425, 4], [405, 7, 425, 5], [405, 8, 425, 6], [405, 15, 425, 13, "error"], [405, 20, 425, 18], [405, 22, 425, 20], [406, 8, 426, 6, "console"], [406, 15, 426, 13], [406, 16, 426, 14, "error"], [406, 21, 426, 19], [406, 22, 426, 20], [406, 54, 426, 52], [406, 56, 426, 54, "error"], [406, 61, 426, 59], [406, 62, 426, 60], [407, 8, 427, 6, "setErrorMessage"], [407, 23, 427, 21], [407, 24, 427, 22], [407, 62, 427, 60, "error"], [407, 67, 427, 65], [407, 68, 427, 66, "message"], [407, 75, 427, 73], [407, 77, 427, 75], [407, 78, 427, 76], [408, 8, 428, 6, "setProcessingState"], [408, 26, 428, 24], [408, 27, 428, 25], [408, 34, 428, 32], [408, 35, 428, 33], [409, 6, 429, 4], [410, 4, 430, 2], [410, 5, 430, 3], [411, 4, 431, 2], [412, 4, 432, 2], [412, 10, 432, 8, "getAuthToken"], [412, 22, 432, 20], [412, 25, 432, 23], [412, 31, 432, 23, "getAuthToken"], [412, 32, 432, 23], [412, 37, 432, 52], [413, 6, 433, 4], [414, 6, 434, 4], [415, 6, 435, 4], [415, 13, 435, 11], [415, 30, 435, 28], [416, 4, 436, 2], [416, 5, 436, 3], [418, 4, 438, 2], [419, 4, 439, 2], [419, 10, 439, 8, "retryCapture"], [419, 22, 439, 20], [419, 25, 439, 23], [419, 29, 439, 23, "useCallback"], [419, 47, 439, 34], [419, 49, 439, 35], [419, 55, 439, 41], [420, 6, 440, 4, "console"], [420, 13, 440, 11], [420, 14, 440, 12, "log"], [420, 17, 440, 15], [420, 18, 440, 16], [420, 55, 440, 53], [420, 56, 440, 54], [421, 6, 441, 4, "setProcessingState"], [421, 24, 441, 22], [421, 25, 441, 23], [421, 31, 441, 29], [421, 32, 441, 30], [422, 6, 442, 4, "setErrorMessage"], [422, 21, 442, 19], [422, 22, 442, 20], [422, 24, 442, 22], [422, 25, 442, 23], [423, 6, 443, 4, "setCapturedPhoto"], [423, 22, 443, 20], [423, 23, 443, 21], [423, 25, 443, 23], [423, 26, 443, 24], [424, 6, 444, 4, "setProcessingProgress"], [424, 27, 444, 25], [424, 28, 444, 26], [424, 29, 444, 27], [424, 30, 444, 28], [425, 4, 445, 2], [425, 5, 445, 3], [425, 7, 445, 5], [425, 9, 445, 7], [425, 10, 445, 8], [426, 4, 446, 2], [427, 4, 447, 2], [427, 8, 447, 2, "useEffect"], [427, 24, 447, 11], [427, 26, 447, 12], [427, 32, 447, 18], [428, 6, 448, 4, "console"], [428, 13, 448, 11], [428, 14, 448, 12, "log"], [428, 17, 448, 15], [428, 18, 448, 16], [428, 53, 448, 51], [428, 55, 448, 53, "permission"], [428, 65, 448, 63], [428, 66, 448, 64], [429, 6, 449, 4], [429, 10, 449, 8, "permission"], [429, 20, 449, 18], [429, 22, 449, 20], [430, 8, 450, 6, "console"], [430, 15, 450, 13], [430, 16, 450, 14, "log"], [430, 19, 450, 17], [430, 20, 450, 18], [430, 57, 450, 55], [430, 59, 450, 57, "permission"], [430, 69, 450, 67], [430, 70, 450, 68, "granted"], [430, 77, 450, 75], [430, 78, 450, 76], [431, 6, 451, 4], [432, 4, 452, 2], [432, 5, 452, 3], [432, 7, 452, 5], [432, 8, 452, 6, "permission"], [432, 18, 452, 16], [432, 19, 452, 17], [432, 20, 452, 18], [433, 4, 453, 2], [434, 4, 454, 2], [434, 8, 454, 6], [434, 9, 454, 7, "permission"], [434, 19, 454, 17], [434, 21, 454, 19], [435, 6, 455, 4, "console"], [435, 13, 455, 11], [435, 14, 455, 12, "log"], [435, 17, 455, 15], [435, 18, 455, 16], [435, 67, 455, 65], [435, 68, 455, 66], [436, 6, 456, 4], [436, 26, 457, 6], [436, 30, 457, 6, "_jsxDevRuntime"], [436, 44, 457, 6], [436, 45, 457, 6, "jsxDEV"], [436, 51, 457, 6], [436, 53, 457, 7, "_View"], [436, 58, 457, 7], [436, 59, 457, 7, "default"], [436, 66, 457, 11], [437, 8, 457, 12, "style"], [437, 13, 457, 17], [437, 15, 457, 19, "styles"], [437, 21, 457, 25], [437, 22, 457, 26, "container"], [437, 31, 457, 36], [438, 8, 457, 36, "children"], [438, 16, 457, 36], [438, 32, 458, 8], [438, 36, 458, 8, "_jsxDevRuntime"], [438, 50, 458, 8], [438, 51, 458, 8, "jsxDEV"], [438, 57, 458, 8], [438, 59, 458, 9, "_ActivityIndicator"], [438, 77, 458, 9], [438, 78, 458, 9, "default"], [438, 85, 458, 26], [439, 10, 458, 27, "size"], [439, 14, 458, 31], [439, 16, 458, 32], [439, 23, 458, 39], [440, 10, 458, 40, "color"], [440, 15, 458, 45], [440, 17, 458, 46], [441, 8, 458, 55], [442, 10, 458, 55, "fileName"], [442, 18, 458, 55], [442, 20, 458, 55, "_jsxFileName"], [442, 32, 458, 55], [443, 10, 458, 55, "lineNumber"], [443, 20, 458, 55], [444, 10, 458, 55, "columnNumber"], [444, 22, 458, 55], [445, 8, 458, 55], [445, 15, 458, 57], [445, 16, 458, 58], [445, 31, 459, 8], [445, 35, 459, 8, "_jsxDevRuntime"], [445, 49, 459, 8], [445, 50, 459, 8, "jsxDEV"], [445, 56, 459, 8], [445, 58, 459, 9, "_Text"], [445, 63, 459, 9], [445, 64, 459, 9, "default"], [445, 71, 459, 13], [446, 10, 459, 14, "style"], [446, 15, 459, 19], [446, 17, 459, 21, "styles"], [446, 23, 459, 27], [446, 24, 459, 28, "loadingText"], [446, 35, 459, 40], [447, 10, 459, 40, "children"], [447, 18, 459, 40], [447, 20, 459, 41], [448, 8, 459, 58], [449, 10, 459, 58, "fileName"], [449, 18, 459, 58], [449, 20, 459, 58, "_jsxFileName"], [449, 32, 459, 58], [450, 10, 459, 58, "lineNumber"], [450, 20, 459, 58], [451, 10, 459, 58, "columnNumber"], [451, 22, 459, 58], [452, 8, 459, 58], [452, 15, 459, 64], [452, 16, 459, 65], [453, 6, 459, 65], [454, 8, 459, 65, "fileName"], [454, 16, 459, 65], [454, 18, 459, 65, "_jsxFileName"], [454, 30, 459, 65], [455, 8, 459, 65, "lineNumber"], [455, 18, 459, 65], [456, 8, 459, 65, "columnNumber"], [456, 20, 459, 65], [457, 6, 459, 65], [457, 13, 460, 12], [457, 14, 460, 13], [458, 4, 462, 2], [459, 4, 463, 2], [459, 8, 463, 6], [459, 9, 463, 7, "permission"], [459, 19, 463, 17], [459, 20, 463, 18, "granted"], [459, 27, 463, 25], [459, 29, 463, 27], [460, 6, 464, 4, "console"], [460, 13, 464, 11], [460, 14, 464, 12, "log"], [460, 17, 464, 15], [460, 18, 464, 16], [460, 93, 464, 91], [460, 94, 464, 92], [461, 6, 465, 4], [461, 26, 466, 6], [461, 30, 466, 6, "_jsxDevRuntime"], [461, 44, 466, 6], [461, 45, 466, 6, "jsxDEV"], [461, 51, 466, 6], [461, 53, 466, 7, "_View"], [461, 58, 466, 7], [461, 59, 466, 7, "default"], [461, 66, 466, 11], [462, 8, 466, 12, "style"], [462, 13, 466, 17], [462, 15, 466, 19, "styles"], [462, 21, 466, 25], [462, 22, 466, 26, "container"], [462, 31, 466, 36], [463, 8, 466, 36, "children"], [463, 16, 466, 36], [463, 31, 467, 8], [463, 35, 467, 8, "_jsxDevRuntime"], [463, 49, 467, 8], [463, 50, 467, 8, "jsxDEV"], [463, 56, 467, 8], [463, 58, 467, 9, "_View"], [463, 63, 467, 9], [463, 64, 467, 9, "default"], [463, 71, 467, 13], [464, 10, 467, 14, "style"], [464, 15, 467, 19], [464, 17, 467, 21, "styles"], [464, 23, 467, 27], [464, 24, 467, 28, "permissionContent"], [464, 41, 467, 46], [465, 10, 467, 46, "children"], [465, 18, 467, 46], [465, 34, 468, 10], [465, 38, 468, 10, "_jsxDevRuntime"], [465, 52, 468, 10], [465, 53, 468, 10, "jsxDEV"], [465, 59, 468, 10], [465, 61, 468, 11, "_lucideReactNative"], [465, 79, 468, 11], [465, 80, 468, 11, "Camera"], [465, 86, 468, 21], [466, 12, 468, 22, "size"], [466, 16, 468, 26], [466, 18, 468, 28], [466, 20, 468, 31], [467, 12, 468, 32, "color"], [467, 17, 468, 37], [467, 19, 468, 38], [468, 10, 468, 47], [469, 12, 468, 47, "fileName"], [469, 20, 468, 47], [469, 22, 468, 47, "_jsxFileName"], [469, 34, 468, 47], [470, 12, 468, 47, "lineNumber"], [470, 22, 468, 47], [471, 12, 468, 47, "columnNumber"], [471, 24, 468, 47], [472, 10, 468, 47], [472, 17, 468, 49], [472, 18, 468, 50], [472, 33, 469, 10], [472, 37, 469, 10, "_jsxDevRuntime"], [472, 51, 469, 10], [472, 52, 469, 10, "jsxDEV"], [472, 58, 469, 10], [472, 60, 469, 11, "_Text"], [472, 65, 469, 11], [472, 66, 469, 11, "default"], [472, 73, 469, 15], [473, 12, 469, 16, "style"], [473, 17, 469, 21], [473, 19, 469, 23, "styles"], [473, 25, 469, 29], [473, 26, 469, 30, "permissionTitle"], [473, 41, 469, 46], [474, 12, 469, 46, "children"], [474, 20, 469, 46], [474, 22, 469, 47], [475, 10, 469, 73], [476, 12, 469, 73, "fileName"], [476, 20, 469, 73], [476, 22, 469, 73, "_jsxFileName"], [476, 34, 469, 73], [477, 12, 469, 73, "lineNumber"], [477, 22, 469, 73], [478, 12, 469, 73, "columnNumber"], [478, 24, 469, 73], [479, 10, 469, 73], [479, 17, 469, 79], [479, 18, 469, 80], [479, 33, 470, 10], [479, 37, 470, 10, "_jsxDevRuntime"], [479, 51, 470, 10], [479, 52, 470, 10, "jsxDEV"], [479, 58, 470, 10], [479, 60, 470, 11, "_Text"], [479, 65, 470, 11], [479, 66, 470, 11, "default"], [479, 73, 470, 15], [480, 12, 470, 16, "style"], [480, 17, 470, 21], [480, 19, 470, 23, "styles"], [480, 25, 470, 29], [480, 26, 470, 30, "permissionDescription"], [480, 47, 470, 52], [481, 12, 470, 52, "children"], [481, 20, 470, 52], [481, 22, 470, 53], [482, 10, 473, 10], [483, 12, 473, 10, "fileName"], [483, 20, 473, 10], [483, 22, 473, 10, "_jsxFileName"], [483, 34, 473, 10], [484, 12, 473, 10, "lineNumber"], [484, 22, 473, 10], [485, 12, 473, 10, "columnNumber"], [485, 24, 473, 10], [486, 10, 473, 10], [486, 17, 473, 16], [486, 18, 473, 17], [486, 33, 474, 10], [486, 37, 474, 10, "_jsxDevRuntime"], [486, 51, 474, 10], [486, 52, 474, 10, "jsxDEV"], [486, 58, 474, 10], [486, 60, 474, 11, "_TouchableOpacity"], [486, 77, 474, 11], [486, 78, 474, 11, "default"], [486, 85, 474, 27], [487, 12, 474, 28, "onPress"], [487, 19, 474, 35], [487, 21, 474, 37, "requestPermission"], [487, 38, 474, 55], [488, 12, 474, 56, "style"], [488, 17, 474, 61], [488, 19, 474, 63, "styles"], [488, 25, 474, 69], [488, 26, 474, 70, "primaryButton"], [488, 39, 474, 84], [489, 12, 474, 84, "children"], [489, 20, 474, 84], [489, 35, 475, 12], [489, 39, 475, 12, "_jsxDevRuntime"], [489, 53, 475, 12], [489, 54, 475, 12, "jsxDEV"], [489, 60, 475, 12], [489, 62, 475, 13, "_Text"], [489, 67, 475, 13], [489, 68, 475, 13, "default"], [489, 75, 475, 17], [490, 14, 475, 18, "style"], [490, 19, 475, 23], [490, 21, 475, 25, "styles"], [490, 27, 475, 31], [490, 28, 475, 32, "primaryButtonText"], [490, 45, 475, 50], [491, 14, 475, 50, "children"], [491, 22, 475, 50], [491, 24, 475, 51], [492, 12, 475, 67], [493, 14, 475, 67, "fileName"], [493, 22, 475, 67], [493, 24, 475, 67, "_jsxFileName"], [493, 36, 475, 67], [494, 14, 475, 67, "lineNumber"], [494, 24, 475, 67], [495, 14, 475, 67, "columnNumber"], [495, 26, 475, 67], [496, 12, 475, 67], [496, 19, 475, 73], [497, 10, 475, 74], [498, 12, 475, 74, "fileName"], [498, 20, 475, 74], [498, 22, 475, 74, "_jsxFileName"], [498, 34, 475, 74], [499, 12, 475, 74, "lineNumber"], [499, 22, 475, 74], [500, 12, 475, 74, "columnNumber"], [500, 24, 475, 74], [501, 10, 475, 74], [501, 17, 476, 28], [501, 18, 476, 29], [501, 33, 477, 10], [501, 37, 477, 10, "_jsxDevRuntime"], [501, 51, 477, 10], [501, 52, 477, 10, "jsxDEV"], [501, 58, 477, 10], [501, 60, 477, 11, "_TouchableOpacity"], [501, 77, 477, 11], [501, 78, 477, 11, "default"], [501, 85, 477, 27], [502, 12, 477, 28, "onPress"], [502, 19, 477, 35], [502, 21, 477, 37, "onCancel"], [502, 29, 477, 46], [503, 12, 477, 47, "style"], [503, 17, 477, 52], [503, 19, 477, 54, "styles"], [503, 25, 477, 60], [503, 26, 477, 61, "secondaryButton"], [503, 41, 477, 77], [504, 12, 477, 77, "children"], [504, 20, 477, 77], [504, 35, 478, 12], [504, 39, 478, 12, "_jsxDevRuntime"], [504, 53, 478, 12], [504, 54, 478, 12, "jsxDEV"], [504, 60, 478, 12], [504, 62, 478, 13, "_Text"], [504, 67, 478, 13], [504, 68, 478, 13, "default"], [504, 75, 478, 17], [505, 14, 478, 18, "style"], [505, 19, 478, 23], [505, 21, 478, 25, "styles"], [505, 27, 478, 31], [505, 28, 478, 32, "secondaryButtonText"], [505, 47, 478, 52], [506, 14, 478, 52, "children"], [506, 22, 478, 52], [506, 24, 478, 53], [507, 12, 478, 59], [508, 14, 478, 59, "fileName"], [508, 22, 478, 59], [508, 24, 478, 59, "_jsxFileName"], [508, 36, 478, 59], [509, 14, 478, 59, "lineNumber"], [509, 24, 478, 59], [510, 14, 478, 59, "columnNumber"], [510, 26, 478, 59], [511, 12, 478, 59], [511, 19, 478, 65], [512, 10, 478, 66], [513, 12, 478, 66, "fileName"], [513, 20, 478, 66], [513, 22, 478, 66, "_jsxFileName"], [513, 34, 478, 66], [514, 12, 478, 66, "lineNumber"], [514, 22, 478, 66], [515, 12, 478, 66, "columnNumber"], [515, 24, 478, 66], [516, 10, 478, 66], [516, 17, 479, 28], [516, 18, 479, 29], [517, 8, 479, 29], [518, 10, 479, 29, "fileName"], [518, 18, 479, 29], [518, 20, 479, 29, "_jsxFileName"], [518, 32, 479, 29], [519, 10, 479, 29, "lineNumber"], [519, 20, 479, 29], [520, 10, 479, 29, "columnNumber"], [520, 22, 479, 29], [521, 8, 479, 29], [521, 15, 480, 14], [522, 6, 480, 15], [523, 8, 480, 15, "fileName"], [523, 16, 480, 15], [523, 18, 480, 15, "_jsxFileName"], [523, 30, 480, 15], [524, 8, 480, 15, "lineNumber"], [524, 18, 480, 15], [525, 8, 480, 15, "columnNumber"], [525, 20, 480, 15], [526, 6, 480, 15], [526, 13, 481, 12], [526, 14, 481, 13], [527, 4, 483, 2], [528, 4, 484, 2], [529, 4, 485, 2, "console"], [529, 11, 485, 9], [529, 12, 485, 10, "log"], [529, 15, 485, 13], [529, 16, 485, 14], [529, 55, 485, 53], [529, 56, 485, 54], [530, 4, 487, 2], [530, 24, 488, 4], [530, 28, 488, 4, "_jsxDevRuntime"], [530, 42, 488, 4], [530, 43, 488, 4, "jsxDEV"], [530, 49, 488, 4], [530, 51, 488, 5, "_View"], [530, 56, 488, 5], [530, 57, 488, 5, "default"], [530, 64, 488, 9], [531, 6, 488, 10, "style"], [531, 11, 488, 15], [531, 13, 488, 17, "styles"], [531, 19, 488, 23], [531, 20, 488, 24, "container"], [531, 29, 488, 34], [532, 6, 488, 34, "children"], [532, 14, 488, 34], [532, 30, 490, 6], [532, 34, 490, 6, "_jsxDevRuntime"], [532, 48, 490, 6], [532, 49, 490, 6, "jsxDEV"], [532, 55, 490, 6], [532, 57, 490, 7, "_View"], [532, 62, 490, 7], [532, 63, 490, 7, "default"], [532, 70, 490, 11], [533, 8, 490, 12, "style"], [533, 13, 490, 17], [533, 15, 490, 19, "styles"], [533, 21, 490, 25], [533, 22, 490, 26, "cameraContainer"], [533, 37, 490, 42], [534, 8, 490, 43, "id"], [534, 10, 490, 45], [534, 12, 490, 46], [534, 29, 490, 63], [535, 8, 490, 63, "children"], [535, 16, 490, 63], [535, 32, 491, 8], [535, 36, 491, 8, "_jsxDevRuntime"], [535, 50, 491, 8], [535, 51, 491, 8, "jsxDEV"], [535, 57, 491, 8], [535, 59, 491, 9, "_expoCamera"], [535, 70, 491, 9], [535, 71, 491, 9, "CameraView"], [535, 81, 491, 19], [536, 10, 492, 10, "ref"], [536, 13, 492, 13], [536, 15, 492, 15, "cameraRef"], [536, 24, 492, 25], [537, 10, 493, 10, "style"], [537, 15, 493, 15], [537, 17, 493, 17], [537, 18, 493, 18, "styles"], [537, 24, 493, 24], [537, 25, 493, 25, "camera"], [537, 31, 493, 31], [537, 33, 493, 33], [538, 12, 493, 35, "backgroundColor"], [538, 27, 493, 50], [538, 29, 493, 52], [539, 10, 493, 62], [539, 11, 493, 63], [539, 12, 493, 65], [540, 10, 494, 10, "facing"], [540, 16, 494, 16], [540, 18, 494, 17], [540, 24, 494, 23], [541, 10, 495, 10, "onLayout"], [541, 18, 495, 18], [541, 20, 495, 21, "e"], [541, 21, 495, 22], [541, 25, 495, 27], [542, 12, 496, 12, "console"], [542, 19, 496, 19], [542, 20, 496, 20, "log"], [542, 23, 496, 23], [542, 24, 496, 24], [542, 56, 496, 56], [542, 58, 496, 58, "e"], [542, 59, 496, 59], [542, 60, 496, 60, "nativeEvent"], [542, 71, 496, 71], [542, 72, 496, 72, "layout"], [542, 78, 496, 78], [542, 79, 496, 79], [543, 12, 497, 12, "setViewSize"], [543, 23, 497, 23], [543, 24, 497, 24], [544, 14, 497, 26, "width"], [544, 19, 497, 31], [544, 21, 497, 33, "e"], [544, 22, 497, 34], [544, 23, 497, 35, "nativeEvent"], [544, 34, 497, 46], [544, 35, 497, 47, "layout"], [544, 41, 497, 53], [544, 42, 497, 54, "width"], [544, 47, 497, 59], [545, 14, 497, 61, "height"], [545, 20, 497, 67], [545, 22, 497, 69, "e"], [545, 23, 497, 70], [545, 24, 497, 71, "nativeEvent"], [545, 35, 497, 82], [545, 36, 497, 83, "layout"], [545, 42, 497, 89], [545, 43, 497, 90, "height"], [546, 12, 497, 97], [546, 13, 497, 98], [546, 14, 497, 99], [547, 10, 498, 10], [547, 11, 498, 12], [548, 10, 499, 10, "onCameraReady"], [548, 23, 499, 23], [548, 25, 499, 25, "onCameraReady"], [548, 26, 499, 25], [548, 31, 499, 31], [549, 12, 500, 12, "console"], [549, 19, 500, 19], [549, 20, 500, 20, "log"], [549, 23, 500, 23], [549, 24, 500, 24], [549, 55, 500, 55], [549, 56, 500, 56], [550, 12, 501, 12, "setIsCameraReady"], [550, 28, 501, 28], [550, 29, 501, 29], [550, 33, 501, 33], [550, 34, 501, 34], [550, 35, 501, 35], [550, 36, 501, 36], [551, 10, 502, 10], [551, 11, 502, 12], [552, 10, 503, 10, "onMountError"], [552, 22, 503, 22], [552, 24, 503, 25, "error"], [552, 29, 503, 30], [552, 33, 503, 35], [553, 12, 504, 12, "console"], [553, 19, 504, 19], [553, 20, 504, 20, "error"], [553, 25, 504, 25], [553, 26, 504, 26], [553, 63, 504, 63], [553, 65, 504, 65, "error"], [553, 70, 504, 70], [553, 71, 504, 71], [554, 12, 505, 12, "setErrorMessage"], [554, 27, 505, 27], [554, 28, 505, 28], [554, 57, 505, 57], [554, 58, 505, 58], [555, 12, 506, 12, "setProcessingState"], [555, 30, 506, 30], [555, 31, 506, 31], [555, 38, 506, 38], [555, 39, 506, 39], [556, 10, 507, 10], [557, 8, 507, 12], [558, 10, 507, 12, "fileName"], [558, 18, 507, 12], [558, 20, 507, 12, "_jsxFileName"], [558, 32, 507, 12], [559, 10, 507, 12, "lineNumber"], [559, 20, 507, 12], [560, 10, 507, 12, "columnNumber"], [560, 22, 507, 12], [561, 8, 507, 12], [561, 15, 508, 9], [561, 16, 508, 10], [561, 18, 510, 9], [561, 19, 510, 10, "isCameraReady"], [561, 32, 510, 23], [561, 49, 511, 10], [561, 53, 511, 10, "_jsxDevRuntime"], [561, 67, 511, 10], [561, 68, 511, 10, "jsxDEV"], [561, 74, 511, 10], [561, 76, 511, 11, "_View"], [561, 81, 511, 11], [561, 82, 511, 11, "default"], [561, 89, 511, 15], [562, 10, 511, 16, "style"], [562, 15, 511, 21], [562, 17, 511, 23], [562, 18, 511, 24, "StyleSheet"], [562, 37, 511, 34], [562, 38, 511, 35, "absoluteFill"], [562, 50, 511, 47], [562, 52, 511, 49], [563, 12, 511, 51, "backgroundColor"], [563, 27, 511, 66], [563, 29, 511, 68], [563, 49, 511, 88], [564, 12, 511, 90, "justifyContent"], [564, 26, 511, 104], [564, 28, 511, 106], [564, 36, 511, 114], [565, 12, 511, 116, "alignItems"], [565, 22, 511, 126], [565, 24, 511, 128], [565, 32, 511, 136], [566, 12, 511, 138, "zIndex"], [566, 18, 511, 144], [566, 20, 511, 146], [567, 10, 511, 151], [567, 11, 511, 152], [567, 12, 511, 154], [568, 10, 511, 154, "children"], [568, 18, 511, 154], [568, 33, 512, 12], [568, 37, 512, 12, "_jsxDevRuntime"], [568, 51, 512, 12], [568, 52, 512, 12, "jsxDEV"], [568, 58, 512, 12], [568, 60, 512, 13, "_View"], [568, 65, 512, 13], [568, 66, 512, 13, "default"], [568, 73, 512, 17], [569, 12, 512, 18, "style"], [569, 17, 512, 23], [569, 19, 512, 25], [570, 14, 512, 27, "backgroundColor"], [570, 29, 512, 42], [570, 31, 512, 44], [570, 51, 512, 64], [571, 14, 512, 66, "padding"], [571, 21, 512, 73], [571, 23, 512, 75], [571, 25, 512, 77], [572, 14, 512, 79, "borderRadius"], [572, 26, 512, 91], [572, 28, 512, 93], [572, 30, 512, 95], [573, 14, 512, 97, "alignItems"], [573, 24, 512, 107], [573, 26, 512, 109], [574, 12, 512, 118], [574, 13, 512, 120], [575, 12, 512, 120, "children"], [575, 20, 512, 120], [575, 36, 513, 14], [575, 40, 513, 14, "_jsxDevRuntime"], [575, 54, 513, 14], [575, 55, 513, 14, "jsxDEV"], [575, 61, 513, 14], [575, 63, 513, 15, "_ActivityIndicator"], [575, 81, 513, 15], [575, 82, 513, 15, "default"], [575, 89, 513, 32], [576, 14, 513, 33, "size"], [576, 18, 513, 37], [576, 20, 513, 38], [576, 27, 513, 45], [577, 14, 513, 46, "color"], [577, 19, 513, 51], [577, 21, 513, 52], [577, 30, 513, 61], [578, 14, 513, 62, "style"], [578, 19, 513, 67], [578, 21, 513, 69], [579, 16, 513, 71, "marginBottom"], [579, 28, 513, 83], [579, 30, 513, 85], [580, 14, 513, 88], [581, 12, 513, 90], [582, 14, 513, 90, "fileName"], [582, 22, 513, 90], [582, 24, 513, 90, "_jsxFileName"], [582, 36, 513, 90], [583, 14, 513, 90, "lineNumber"], [583, 24, 513, 90], [584, 14, 513, 90, "columnNumber"], [584, 26, 513, 90], [585, 12, 513, 90], [585, 19, 513, 92], [585, 20, 513, 93], [585, 35, 514, 14], [585, 39, 514, 14, "_jsxDevRuntime"], [585, 53, 514, 14], [585, 54, 514, 14, "jsxDEV"], [585, 60, 514, 14], [585, 62, 514, 15, "_Text"], [585, 67, 514, 15], [585, 68, 514, 15, "default"], [585, 75, 514, 19], [586, 14, 514, 20, "style"], [586, 19, 514, 25], [586, 21, 514, 27], [587, 16, 514, 29, "color"], [587, 21, 514, 34], [587, 23, 514, 36], [587, 29, 514, 42], [588, 16, 514, 44, "fontSize"], [588, 24, 514, 52], [588, 26, 514, 54], [588, 28, 514, 56], [589, 16, 514, 58, "fontWeight"], [589, 26, 514, 68], [589, 28, 514, 70], [590, 14, 514, 76], [590, 15, 514, 78], [591, 14, 514, 78, "children"], [591, 22, 514, 78], [591, 24, 514, 79], [592, 12, 514, 101], [593, 14, 514, 101, "fileName"], [593, 22, 514, 101], [593, 24, 514, 101, "_jsxFileName"], [593, 36, 514, 101], [594, 14, 514, 101, "lineNumber"], [594, 24, 514, 101], [595, 14, 514, 101, "columnNumber"], [595, 26, 514, 101], [596, 12, 514, 101], [596, 19, 514, 107], [596, 20, 514, 108], [596, 35, 515, 14], [596, 39, 515, 14, "_jsxDevRuntime"], [596, 53, 515, 14], [596, 54, 515, 14, "jsxDEV"], [596, 60, 515, 14], [596, 62, 515, 15, "_Text"], [596, 67, 515, 15], [596, 68, 515, 15, "default"], [596, 75, 515, 19], [597, 14, 515, 20, "style"], [597, 19, 515, 25], [597, 21, 515, 27], [598, 16, 515, 29, "color"], [598, 21, 515, 34], [598, 23, 515, 36], [598, 32, 515, 45], [599, 16, 515, 47, "fontSize"], [599, 24, 515, 55], [599, 26, 515, 57], [599, 28, 515, 59], [600, 16, 515, 61, "marginTop"], [600, 25, 515, 70], [600, 27, 515, 72], [601, 14, 515, 74], [601, 15, 515, 76], [602, 14, 515, 76, "children"], [602, 22, 515, 76], [602, 24, 515, 77], [603, 12, 515, 88], [604, 14, 515, 88, "fileName"], [604, 22, 515, 88], [604, 24, 515, 88, "_jsxFileName"], [604, 36, 515, 88], [605, 14, 515, 88, "lineNumber"], [605, 24, 515, 88], [606, 14, 515, 88, "columnNumber"], [606, 26, 515, 88], [607, 12, 515, 88], [607, 19, 515, 94], [607, 20, 515, 95], [608, 10, 515, 95], [609, 12, 515, 95, "fileName"], [609, 20, 515, 95], [609, 22, 515, 95, "_jsxFileName"], [609, 34, 515, 95], [610, 12, 515, 95, "lineNumber"], [610, 22, 515, 95], [611, 12, 515, 95, "columnNumber"], [611, 24, 515, 95], [612, 10, 515, 95], [612, 17, 516, 18], [613, 8, 516, 19], [614, 10, 516, 19, "fileName"], [614, 18, 516, 19], [614, 20, 516, 19, "_jsxFileName"], [614, 32, 516, 19], [615, 10, 516, 19, "lineNumber"], [615, 20, 516, 19], [616, 10, 516, 19, "columnNumber"], [616, 22, 516, 19], [617, 8, 516, 19], [617, 15, 517, 16], [617, 16, 518, 9], [617, 18, 521, 9, "isCameraReady"], [617, 31, 521, 22], [617, 35, 521, 26, "previewBlurEnabled"], [617, 53, 521, 44], [617, 57, 521, 48, "viewSize"], [617, 65, 521, 56], [617, 66, 521, 57, "width"], [617, 71, 521, 62], [617, 74, 521, 65], [617, 75, 521, 66], [617, 92, 522, 10], [617, 96, 522, 10, "_jsxDevRuntime"], [617, 110, 522, 10], [617, 111, 522, 10, "jsxDEV"], [617, 117, 522, 10], [617, 119, 522, 10, "_jsxDevRuntime"], [617, 133, 522, 10], [617, 134, 522, 10, "Fragment"], [617, 142, 522, 10], [618, 10, 522, 10, "children"], [618, 18, 522, 10], [618, 34, 524, 12], [618, 38, 524, 12, "_jsxDevRuntime"], [618, 52, 524, 12], [618, 53, 524, 12, "jsxDEV"], [618, 59, 524, 12], [618, 61, 524, 13, "_LiveFaceCanvas"], [618, 76, 524, 13], [618, 77, 524, 13, "default"], [618, 84, 524, 27], [619, 12, 524, 28, "containerId"], [619, 23, 524, 39], [619, 25, 524, 40], [619, 42, 524, 57], [620, 12, 524, 58, "width"], [620, 17, 524, 63], [620, 19, 524, 65, "viewSize"], [620, 27, 524, 73], [620, 28, 524, 74, "width"], [620, 33, 524, 80], [621, 12, 524, 81, "height"], [621, 18, 524, 87], [621, 20, 524, 89, "viewSize"], [621, 28, 524, 97], [621, 29, 524, 98, "height"], [622, 10, 524, 105], [623, 12, 524, 105, "fileName"], [623, 20, 524, 105], [623, 22, 524, 105, "_jsxFileName"], [623, 34, 524, 105], [624, 12, 524, 105, "lineNumber"], [624, 22, 524, 105], [625, 12, 524, 105, "columnNumber"], [625, 24, 524, 105], [626, 10, 524, 105], [626, 17, 524, 107], [626, 18, 524, 108], [626, 33, 525, 12], [626, 37, 525, 12, "_jsxDevRuntime"], [626, 51, 525, 12], [626, 52, 525, 12, "jsxDEV"], [626, 58, 525, 12], [626, 60, 525, 13, "_View"], [626, 65, 525, 13], [626, 66, 525, 13, "default"], [626, 73, 525, 17], [627, 12, 525, 18, "style"], [627, 17, 525, 23], [627, 19, 525, 25], [627, 20, 525, 26, "StyleSheet"], [627, 39, 525, 36], [627, 40, 525, 37, "absoluteFill"], [627, 52, 525, 49], [627, 54, 525, 51], [628, 14, 525, 53, "pointerEvents"], [628, 27, 525, 66], [628, 29, 525, 68], [629, 12, 525, 75], [629, 13, 525, 76], [629, 14, 525, 78], [630, 12, 525, 78, "children"], [630, 20, 525, 78], [630, 36, 527, 12], [630, 40, 527, 12, "_jsxDevRuntime"], [630, 54, 527, 12], [630, 55, 527, 12, "jsxDEV"], [630, 61, 527, 12], [630, 63, 527, 13, "_expoBlur"], [630, 72, 527, 13], [630, 73, 527, 13, "BlurView"], [630, 81, 527, 21], [631, 14, 527, 22, "intensity"], [631, 23, 527, 31], [631, 25, 527, 33], [631, 27, 527, 36], [632, 14, 527, 37, "tint"], [632, 18, 527, 41], [632, 20, 527, 42], [632, 26, 527, 48], [633, 14, 527, 49, "style"], [633, 19, 527, 54], [633, 21, 527, 56], [633, 22, 527, 57, "styles"], [633, 28, 527, 63], [633, 29, 527, 64, "blurZone"], [633, 37, 527, 72], [633, 39, 527, 74], [634, 16, 528, 14, "left"], [634, 20, 528, 18], [634, 22, 528, 20], [634, 23, 528, 21], [635, 16, 529, 14, "top"], [635, 19, 529, 17], [635, 21, 529, 19, "viewSize"], [635, 29, 529, 27], [635, 30, 529, 28, "height"], [635, 36, 529, 34], [635, 39, 529, 37], [635, 42, 529, 40], [636, 16, 530, 14, "width"], [636, 21, 530, 19], [636, 23, 530, 21, "viewSize"], [636, 31, 530, 29], [636, 32, 530, 30, "width"], [636, 37, 530, 35], [637, 16, 531, 14, "height"], [637, 22, 531, 20], [637, 24, 531, 22, "viewSize"], [637, 32, 531, 30], [637, 33, 531, 31, "height"], [637, 39, 531, 37], [637, 42, 531, 40], [637, 46, 531, 44], [638, 16, 532, 14, "borderRadius"], [638, 28, 532, 26], [638, 30, 532, 28], [639, 14, 533, 12], [639, 15, 533, 13], [640, 12, 533, 15], [641, 14, 533, 15, "fileName"], [641, 22, 533, 15], [641, 24, 533, 15, "_jsxFileName"], [641, 36, 533, 15], [642, 14, 533, 15, "lineNumber"], [642, 24, 533, 15], [643, 14, 533, 15, "columnNumber"], [643, 26, 533, 15], [644, 12, 533, 15], [644, 19, 533, 17], [644, 20, 533, 18], [644, 35, 535, 12], [644, 39, 535, 12, "_jsxDevRuntime"], [644, 53, 535, 12], [644, 54, 535, 12, "jsxDEV"], [644, 60, 535, 12], [644, 62, 535, 13, "_expoBlur"], [644, 71, 535, 13], [644, 72, 535, 13, "BlurView"], [644, 80, 535, 21], [645, 14, 535, 22, "intensity"], [645, 23, 535, 31], [645, 25, 535, 33], [645, 27, 535, 36], [646, 14, 535, 37, "tint"], [646, 18, 535, 41], [646, 20, 535, 42], [646, 26, 535, 48], [647, 14, 535, 49, "style"], [647, 19, 535, 54], [647, 21, 535, 56], [647, 22, 535, 57, "styles"], [647, 28, 535, 63], [647, 29, 535, 64, "blurZone"], [647, 37, 535, 72], [647, 39, 535, 74], [648, 16, 536, 14, "left"], [648, 20, 536, 18], [648, 22, 536, 20], [648, 23, 536, 21], [649, 16, 537, 14, "top"], [649, 19, 537, 17], [649, 21, 537, 19], [649, 22, 537, 20], [650, 16, 538, 14, "width"], [650, 21, 538, 19], [650, 23, 538, 21, "viewSize"], [650, 31, 538, 29], [650, 32, 538, 30, "width"], [650, 37, 538, 35], [651, 16, 539, 14, "height"], [651, 22, 539, 20], [651, 24, 539, 22, "viewSize"], [651, 32, 539, 30], [651, 33, 539, 31, "height"], [651, 39, 539, 37], [651, 42, 539, 40], [651, 45, 539, 43], [652, 16, 540, 14, "borderRadius"], [652, 28, 540, 26], [652, 30, 540, 28], [653, 14, 541, 12], [653, 15, 541, 13], [654, 12, 541, 15], [655, 14, 541, 15, "fileName"], [655, 22, 541, 15], [655, 24, 541, 15, "_jsxFileName"], [655, 36, 541, 15], [656, 14, 541, 15, "lineNumber"], [656, 24, 541, 15], [657, 14, 541, 15, "columnNumber"], [657, 26, 541, 15], [658, 12, 541, 15], [658, 19, 541, 17], [658, 20, 541, 18], [658, 35, 543, 12], [658, 39, 543, 12, "_jsxDevRuntime"], [658, 53, 543, 12], [658, 54, 543, 12, "jsxDEV"], [658, 60, 543, 12], [658, 62, 543, 13, "_expoBlur"], [658, 71, 543, 13], [658, 72, 543, 13, "BlurView"], [658, 80, 543, 21], [659, 14, 543, 22, "intensity"], [659, 23, 543, 31], [659, 25, 543, 33], [659, 27, 543, 36], [660, 14, 543, 37, "tint"], [660, 18, 543, 41], [660, 20, 543, 42], [660, 26, 543, 48], [661, 14, 543, 49, "style"], [661, 19, 543, 54], [661, 21, 543, 56], [661, 22, 543, 57, "styles"], [661, 28, 543, 63], [661, 29, 543, 64, "blurZone"], [661, 37, 543, 72], [661, 39, 543, 74], [662, 16, 544, 14, "left"], [662, 20, 544, 18], [662, 22, 544, 20, "viewSize"], [662, 30, 544, 28], [662, 31, 544, 29, "width"], [662, 36, 544, 34], [662, 39, 544, 37], [662, 42, 544, 40], [662, 45, 544, 44, "viewSize"], [662, 53, 544, 52], [662, 54, 544, 53, "width"], [662, 59, 544, 58], [662, 62, 544, 61], [662, 66, 544, 66], [663, 16, 545, 14, "top"], [663, 19, 545, 17], [663, 21, 545, 19, "viewSize"], [663, 29, 545, 27], [663, 30, 545, 28, "height"], [663, 36, 545, 34], [663, 39, 545, 37], [663, 43, 545, 41], [663, 46, 545, 45, "viewSize"], [663, 54, 545, 53], [663, 55, 545, 54, "width"], [663, 60, 545, 59], [663, 63, 545, 62], [663, 67, 545, 67], [664, 16, 546, 14, "width"], [664, 21, 546, 19], [664, 23, 546, 21, "viewSize"], [664, 31, 546, 29], [664, 32, 546, 30, "width"], [664, 37, 546, 35], [664, 40, 546, 38], [664, 43, 546, 41], [665, 16, 547, 14, "height"], [665, 22, 547, 20], [665, 24, 547, 22, "viewSize"], [665, 32, 547, 30], [665, 33, 547, 31, "width"], [665, 38, 547, 36], [665, 41, 547, 39], [665, 44, 547, 42], [666, 16, 548, 14, "borderRadius"], [666, 28, 548, 26], [666, 30, 548, 29, "viewSize"], [666, 38, 548, 37], [666, 39, 548, 38, "width"], [666, 44, 548, 43], [666, 47, 548, 46], [666, 50, 548, 49], [666, 53, 548, 53], [667, 14, 549, 12], [667, 15, 549, 13], [668, 12, 549, 15], [669, 14, 549, 15, "fileName"], [669, 22, 549, 15], [669, 24, 549, 15, "_jsxFileName"], [669, 36, 549, 15], [670, 14, 549, 15, "lineNumber"], [670, 24, 549, 15], [671, 14, 549, 15, "columnNumber"], [671, 26, 549, 15], [672, 12, 549, 15], [672, 19, 549, 17], [672, 20, 549, 18], [672, 35, 550, 12], [672, 39, 550, 12, "_jsxDevRuntime"], [672, 53, 550, 12], [672, 54, 550, 12, "jsxDEV"], [672, 60, 550, 12], [672, 62, 550, 13, "_expoBlur"], [672, 71, 550, 13], [672, 72, 550, 13, "BlurView"], [672, 80, 550, 21], [673, 14, 550, 22, "intensity"], [673, 23, 550, 31], [673, 25, 550, 33], [673, 27, 550, 36], [674, 14, 550, 37, "tint"], [674, 18, 550, 41], [674, 20, 550, 42], [674, 26, 550, 48], [675, 14, 550, 49, "style"], [675, 19, 550, 54], [675, 21, 550, 56], [675, 22, 550, 57, "styles"], [675, 28, 550, 63], [675, 29, 550, 64, "blurZone"], [675, 37, 550, 72], [675, 39, 550, 74], [676, 16, 551, 14, "left"], [676, 20, 551, 18], [676, 22, 551, 20, "viewSize"], [676, 30, 551, 28], [676, 31, 551, 29, "width"], [676, 36, 551, 34], [676, 39, 551, 37], [676, 42, 551, 40], [676, 45, 551, 44, "viewSize"], [676, 53, 551, 52], [676, 54, 551, 53, "width"], [676, 59, 551, 58], [676, 62, 551, 61], [676, 66, 551, 66], [677, 16, 552, 14, "top"], [677, 19, 552, 17], [677, 21, 552, 19, "viewSize"], [677, 29, 552, 27], [677, 30, 552, 28, "height"], [677, 36, 552, 34], [677, 39, 552, 37], [677, 42, 552, 40], [677, 45, 552, 44, "viewSize"], [677, 53, 552, 52], [677, 54, 552, 53, "width"], [677, 59, 552, 58], [677, 62, 552, 61], [677, 66, 552, 66], [678, 16, 553, 14, "width"], [678, 21, 553, 19], [678, 23, 553, 21, "viewSize"], [678, 31, 553, 29], [678, 32, 553, 30, "width"], [678, 37, 553, 35], [678, 40, 553, 38], [678, 43, 553, 41], [679, 16, 554, 14, "height"], [679, 22, 554, 20], [679, 24, 554, 22, "viewSize"], [679, 32, 554, 30], [679, 33, 554, 31, "width"], [679, 38, 554, 36], [679, 41, 554, 39], [679, 44, 554, 42], [680, 16, 555, 14, "borderRadius"], [680, 28, 555, 26], [680, 30, 555, 29, "viewSize"], [680, 38, 555, 37], [680, 39, 555, 38, "width"], [680, 44, 555, 43], [680, 47, 555, 46], [680, 50, 555, 49], [680, 53, 555, 53], [681, 14, 556, 12], [681, 15, 556, 13], [682, 12, 556, 15], [683, 14, 556, 15, "fileName"], [683, 22, 556, 15], [683, 24, 556, 15, "_jsxFileName"], [683, 36, 556, 15], [684, 14, 556, 15, "lineNumber"], [684, 24, 556, 15], [685, 14, 556, 15, "columnNumber"], [685, 26, 556, 15], [686, 12, 556, 15], [686, 19, 556, 17], [686, 20, 556, 18], [686, 35, 557, 12], [686, 39, 557, 12, "_jsxDevRuntime"], [686, 53, 557, 12], [686, 54, 557, 12, "jsxDEV"], [686, 60, 557, 12], [686, 62, 557, 13, "_expoBlur"], [686, 71, 557, 13], [686, 72, 557, 13, "BlurView"], [686, 80, 557, 21], [687, 14, 557, 22, "intensity"], [687, 23, 557, 31], [687, 25, 557, 33], [687, 27, 557, 36], [688, 14, 557, 37, "tint"], [688, 18, 557, 41], [688, 20, 557, 42], [688, 26, 557, 48], [689, 14, 557, 49, "style"], [689, 19, 557, 54], [689, 21, 557, 56], [689, 22, 557, 57, "styles"], [689, 28, 557, 63], [689, 29, 557, 64, "blurZone"], [689, 37, 557, 72], [689, 39, 557, 74], [690, 16, 558, 14, "left"], [690, 20, 558, 18], [690, 22, 558, 20, "viewSize"], [690, 30, 558, 28], [690, 31, 558, 29, "width"], [690, 36, 558, 34], [690, 39, 558, 37], [690, 42, 558, 40], [690, 45, 558, 44, "viewSize"], [690, 53, 558, 52], [690, 54, 558, 53, "width"], [690, 59, 558, 58], [690, 62, 558, 61], [690, 66, 558, 66], [691, 16, 559, 14, "top"], [691, 19, 559, 17], [691, 21, 559, 19, "viewSize"], [691, 29, 559, 27], [691, 30, 559, 28, "height"], [691, 36, 559, 34], [691, 39, 559, 37], [691, 42, 559, 40], [691, 45, 559, 44, "viewSize"], [691, 53, 559, 52], [691, 54, 559, 53, "width"], [691, 59, 559, 58], [691, 62, 559, 61], [691, 66, 559, 66], [692, 16, 560, 14, "width"], [692, 21, 560, 19], [692, 23, 560, 21, "viewSize"], [692, 31, 560, 29], [692, 32, 560, 30, "width"], [692, 37, 560, 35], [692, 40, 560, 38], [692, 43, 560, 41], [693, 16, 561, 14, "height"], [693, 22, 561, 20], [693, 24, 561, 22, "viewSize"], [693, 32, 561, 30], [693, 33, 561, 31, "width"], [693, 38, 561, 36], [693, 41, 561, 39], [693, 44, 561, 42], [694, 16, 562, 14, "borderRadius"], [694, 28, 562, 26], [694, 30, 562, 29, "viewSize"], [694, 38, 562, 37], [694, 39, 562, 38, "width"], [694, 44, 562, 43], [694, 47, 562, 46], [694, 50, 562, 49], [694, 53, 562, 53], [695, 14, 563, 12], [695, 15, 563, 13], [696, 12, 563, 15], [697, 14, 563, 15, "fileName"], [697, 22, 563, 15], [697, 24, 563, 15, "_jsxFileName"], [697, 36, 563, 15], [698, 14, 563, 15, "lineNumber"], [698, 24, 563, 15], [699, 14, 563, 15, "columnNumber"], [699, 26, 563, 15], [700, 12, 563, 15], [700, 19, 563, 17], [700, 20, 563, 18], [700, 22, 565, 13, "__DEV__"], [700, 29, 565, 20], [700, 46, 566, 14], [700, 50, 566, 14, "_jsxDevRuntime"], [700, 64, 566, 14], [700, 65, 566, 14, "jsxDEV"], [700, 71, 566, 14], [700, 73, 566, 15, "_View"], [700, 78, 566, 15], [700, 79, 566, 15, "default"], [700, 86, 566, 19], [701, 14, 566, 20, "style"], [701, 19, 566, 25], [701, 21, 566, 27, "styles"], [701, 27, 566, 33], [701, 28, 566, 34, "previewChip"], [701, 39, 566, 46], [702, 14, 566, 46, "children"], [702, 22, 566, 46], [702, 37, 567, 16], [702, 41, 567, 16, "_jsxDevRuntime"], [702, 55, 567, 16], [702, 56, 567, 16, "jsxDEV"], [702, 62, 567, 16], [702, 64, 567, 17, "_Text"], [702, 69, 567, 17], [702, 70, 567, 17, "default"], [702, 77, 567, 21], [703, 16, 567, 22, "style"], [703, 21, 567, 27], [703, 23, 567, 29, "styles"], [703, 29, 567, 35], [703, 30, 567, 36, "previewChipText"], [703, 45, 567, 52], [704, 16, 567, 52, "children"], [704, 24, 567, 52], [704, 26, 567, 53], [705, 14, 567, 73], [706, 16, 567, 73, "fileName"], [706, 24, 567, 73], [706, 26, 567, 73, "_jsxFileName"], [706, 38, 567, 73], [707, 16, 567, 73, "lineNumber"], [707, 26, 567, 73], [708, 16, 567, 73, "columnNumber"], [708, 28, 567, 73], [709, 14, 567, 73], [709, 21, 567, 79], [710, 12, 567, 80], [711, 14, 567, 80, "fileName"], [711, 22, 567, 80], [711, 24, 567, 80, "_jsxFileName"], [711, 36, 567, 80], [712, 14, 567, 80, "lineNumber"], [712, 24, 567, 80], [713, 14, 567, 80, "columnNumber"], [713, 26, 567, 80], [714, 12, 567, 80], [714, 19, 568, 20], [714, 20, 569, 13], [715, 10, 569, 13], [716, 12, 569, 13, "fileName"], [716, 20, 569, 13], [716, 22, 569, 13, "_jsxFileName"], [716, 34, 569, 13], [717, 12, 569, 13, "lineNumber"], [717, 22, 569, 13], [718, 12, 569, 13, "columnNumber"], [718, 24, 569, 13], [719, 10, 569, 13], [719, 17, 570, 18], [719, 18, 570, 19], [720, 8, 570, 19], [720, 23, 571, 12], [720, 24, 572, 9], [720, 26, 574, 9, "isCameraReady"], [720, 39, 574, 22], [720, 56, 575, 10], [720, 60, 575, 10, "_jsxDevRuntime"], [720, 74, 575, 10], [720, 75, 575, 10, "jsxDEV"], [720, 81, 575, 10], [720, 83, 575, 10, "_jsxDevRuntime"], [720, 97, 575, 10], [720, 98, 575, 10, "Fragment"], [720, 106, 575, 10], [721, 10, 575, 10, "children"], [721, 18, 575, 10], [721, 34, 577, 12], [721, 38, 577, 12, "_jsxDevRuntime"], [721, 52, 577, 12], [721, 53, 577, 12, "jsxDEV"], [721, 59, 577, 12], [721, 61, 577, 13, "_View"], [721, 66, 577, 13], [721, 67, 577, 13, "default"], [721, 74, 577, 17], [722, 12, 577, 18, "style"], [722, 17, 577, 23], [722, 19, 577, 25, "styles"], [722, 25, 577, 31], [722, 26, 577, 32, "headerOverlay"], [722, 39, 577, 46], [723, 12, 577, 46, "children"], [723, 20, 577, 46], [723, 35, 578, 14], [723, 39, 578, 14, "_jsxDevRuntime"], [723, 53, 578, 14], [723, 54, 578, 14, "jsxDEV"], [723, 60, 578, 14], [723, 62, 578, 15, "_View"], [723, 67, 578, 15], [723, 68, 578, 15, "default"], [723, 75, 578, 19], [724, 14, 578, 20, "style"], [724, 19, 578, 25], [724, 21, 578, 27, "styles"], [724, 27, 578, 33], [724, 28, 578, 34, "headerContent"], [724, 41, 578, 48], [725, 14, 578, 48, "children"], [725, 22, 578, 48], [725, 38, 579, 16], [725, 42, 579, 16, "_jsxDevRuntime"], [725, 56, 579, 16], [725, 57, 579, 16, "jsxDEV"], [725, 63, 579, 16], [725, 65, 579, 17, "_View"], [725, 70, 579, 17], [725, 71, 579, 17, "default"], [725, 78, 579, 21], [726, 16, 579, 22, "style"], [726, 21, 579, 27], [726, 23, 579, 29, "styles"], [726, 29, 579, 35], [726, 30, 579, 36, "headerLeft"], [726, 40, 579, 47], [727, 16, 579, 47, "children"], [727, 24, 579, 47], [727, 40, 580, 18], [727, 44, 580, 18, "_jsxDevRuntime"], [727, 58, 580, 18], [727, 59, 580, 18, "jsxDEV"], [727, 65, 580, 18], [727, 67, 580, 19, "_Text"], [727, 72, 580, 19], [727, 73, 580, 19, "default"], [727, 80, 580, 23], [728, 18, 580, 24, "style"], [728, 23, 580, 29], [728, 25, 580, 31, "styles"], [728, 31, 580, 37], [728, 32, 580, 38, "headerTitle"], [728, 43, 580, 50], [729, 18, 580, 50, "children"], [729, 26, 580, 50], [729, 28, 580, 51], [730, 16, 580, 62], [731, 18, 580, 62, "fileName"], [731, 26, 580, 62], [731, 28, 580, 62, "_jsxFileName"], [731, 40, 580, 62], [732, 18, 580, 62, "lineNumber"], [732, 28, 580, 62], [733, 18, 580, 62, "columnNumber"], [733, 30, 580, 62], [734, 16, 580, 62], [734, 23, 580, 68], [734, 24, 580, 69], [734, 39, 581, 18], [734, 43, 581, 18, "_jsxDevRuntime"], [734, 57, 581, 18], [734, 58, 581, 18, "jsxDEV"], [734, 64, 581, 18], [734, 66, 581, 19, "_View"], [734, 71, 581, 19], [734, 72, 581, 19, "default"], [734, 79, 581, 23], [735, 18, 581, 24, "style"], [735, 23, 581, 29], [735, 25, 581, 31, "styles"], [735, 31, 581, 37], [735, 32, 581, 38, "subtitleRow"], [735, 43, 581, 50], [736, 18, 581, 50, "children"], [736, 26, 581, 50], [736, 42, 582, 20], [736, 46, 582, 20, "_jsxDevRuntime"], [736, 60, 582, 20], [736, 61, 582, 20, "jsxDEV"], [736, 67, 582, 20], [736, 69, 582, 21, "_Text"], [736, 74, 582, 21], [736, 75, 582, 21, "default"], [736, 82, 582, 25], [737, 20, 582, 26, "style"], [737, 25, 582, 31], [737, 27, 582, 33, "styles"], [737, 33, 582, 39], [737, 34, 582, 40, "webIcon"], [737, 41, 582, 48], [738, 20, 582, 48, "children"], [738, 28, 582, 48], [738, 30, 582, 49], [739, 18, 582, 51], [740, 20, 582, 51, "fileName"], [740, 28, 582, 51], [740, 30, 582, 51, "_jsxFileName"], [740, 42, 582, 51], [741, 20, 582, 51, "lineNumber"], [741, 30, 582, 51], [742, 20, 582, 51, "columnNumber"], [742, 32, 582, 51], [743, 18, 582, 51], [743, 25, 582, 57], [743, 26, 582, 58], [743, 41, 583, 20], [743, 45, 583, 20, "_jsxDevRuntime"], [743, 59, 583, 20], [743, 60, 583, 20, "jsxDEV"], [743, 66, 583, 20], [743, 68, 583, 21, "_Text"], [743, 73, 583, 21], [743, 74, 583, 21, "default"], [743, 81, 583, 25], [744, 20, 583, 26, "style"], [744, 25, 583, 31], [744, 27, 583, 33, "styles"], [744, 33, 583, 39], [744, 34, 583, 40, "headerSubtitle"], [744, 48, 583, 55], [745, 20, 583, 55, "children"], [745, 28, 583, 55], [745, 30, 583, 56], [746, 18, 583, 71], [747, 20, 583, 71, "fileName"], [747, 28, 583, 71], [747, 30, 583, 71, "_jsxFileName"], [747, 42, 583, 71], [748, 20, 583, 71, "lineNumber"], [748, 30, 583, 71], [749, 20, 583, 71, "columnNumber"], [749, 32, 583, 71], [750, 18, 583, 71], [750, 25, 583, 77], [750, 26, 583, 78], [751, 16, 583, 78], [752, 18, 583, 78, "fileName"], [752, 26, 583, 78], [752, 28, 583, 78, "_jsxFileName"], [752, 40, 583, 78], [753, 18, 583, 78, "lineNumber"], [753, 28, 583, 78], [754, 18, 583, 78, "columnNumber"], [754, 30, 583, 78], [755, 16, 583, 78], [755, 23, 584, 24], [755, 24, 584, 25], [755, 26, 585, 19, "challengeCode"], [755, 39, 585, 32], [755, 56, 586, 20], [755, 60, 586, 20, "_jsxDevRuntime"], [755, 74, 586, 20], [755, 75, 586, 20, "jsxDEV"], [755, 81, 586, 20], [755, 83, 586, 21, "_View"], [755, 88, 586, 21], [755, 89, 586, 21, "default"], [755, 96, 586, 25], [756, 18, 586, 26, "style"], [756, 23, 586, 31], [756, 25, 586, 33, "styles"], [756, 31, 586, 39], [756, 32, 586, 40, "challengeRow"], [756, 44, 586, 53], [757, 18, 586, 53, "children"], [757, 26, 586, 53], [757, 42, 587, 22], [757, 46, 587, 22, "_jsxDevRuntime"], [757, 60, 587, 22], [757, 61, 587, 22, "jsxDEV"], [757, 67, 587, 22], [757, 69, 587, 23, "_lucideReactNative"], [757, 87, 587, 23], [757, 88, 587, 23, "Shield"], [757, 94, 587, 29], [758, 20, 587, 30, "size"], [758, 24, 587, 34], [758, 26, 587, 36], [758, 28, 587, 39], [759, 20, 587, 40, "color"], [759, 25, 587, 45], [759, 27, 587, 46], [760, 18, 587, 52], [761, 20, 587, 52, "fileName"], [761, 28, 587, 52], [761, 30, 587, 52, "_jsxFileName"], [761, 42, 587, 52], [762, 20, 587, 52, "lineNumber"], [762, 30, 587, 52], [763, 20, 587, 52, "columnNumber"], [763, 32, 587, 52], [764, 18, 587, 52], [764, 25, 587, 54], [764, 26, 587, 55], [764, 41, 588, 22], [764, 45, 588, 22, "_jsxDevRuntime"], [764, 59, 588, 22], [764, 60, 588, 22, "jsxDEV"], [764, 66, 588, 22], [764, 68, 588, 23, "_Text"], [764, 73, 588, 23], [764, 74, 588, 23, "default"], [764, 81, 588, 27], [765, 20, 588, 28, "style"], [765, 25, 588, 33], [765, 27, 588, 35, "styles"], [765, 33, 588, 41], [765, 34, 588, 42, "challengeCode"], [765, 47, 588, 56], [766, 20, 588, 56, "children"], [766, 28, 588, 56], [766, 30, 588, 58, "challengeCode"], [767, 18, 588, 71], [768, 20, 588, 71, "fileName"], [768, 28, 588, 71], [768, 30, 588, 71, "_jsxFileName"], [768, 42, 588, 71], [769, 20, 588, 71, "lineNumber"], [769, 30, 588, 71], [770, 20, 588, 71, "columnNumber"], [770, 32, 588, 71], [771, 18, 588, 71], [771, 25, 588, 78], [771, 26, 588, 79], [772, 16, 588, 79], [773, 18, 588, 79, "fileName"], [773, 26, 588, 79], [773, 28, 588, 79, "_jsxFileName"], [773, 40, 588, 79], [774, 18, 588, 79, "lineNumber"], [774, 28, 588, 79], [775, 18, 588, 79, "columnNumber"], [775, 30, 588, 79], [776, 16, 588, 79], [776, 23, 589, 26], [776, 24, 590, 19], [777, 14, 590, 19], [778, 16, 590, 19, "fileName"], [778, 24, 590, 19], [778, 26, 590, 19, "_jsxFileName"], [778, 38, 590, 19], [779, 16, 590, 19, "lineNumber"], [779, 26, 590, 19], [780, 16, 590, 19, "columnNumber"], [780, 28, 590, 19], [781, 14, 590, 19], [781, 21, 591, 22], [781, 22, 591, 23], [781, 37, 592, 16], [781, 41, 592, 16, "_jsxDevRuntime"], [781, 55, 592, 16], [781, 56, 592, 16, "jsxDEV"], [781, 62, 592, 16], [781, 64, 592, 17, "_TouchableOpacity"], [781, 81, 592, 17], [781, 82, 592, 17, "default"], [781, 89, 592, 33], [782, 16, 592, 34, "onPress"], [782, 23, 592, 41], [782, 25, 592, 43, "onCancel"], [782, 33, 592, 52], [783, 16, 592, 53, "style"], [783, 21, 592, 58], [783, 23, 592, 60, "styles"], [783, 29, 592, 66], [783, 30, 592, 67, "closeButton"], [783, 41, 592, 79], [784, 16, 592, 79, "children"], [784, 24, 592, 79], [784, 39, 593, 18], [784, 43, 593, 18, "_jsxDevRuntime"], [784, 57, 593, 18], [784, 58, 593, 18, "jsxDEV"], [784, 64, 593, 18], [784, 66, 593, 19, "_lucideReactNative"], [784, 84, 593, 19], [784, 85, 593, 19, "X"], [784, 86, 593, 20], [785, 18, 593, 21, "size"], [785, 22, 593, 25], [785, 24, 593, 27], [785, 26, 593, 30], [786, 18, 593, 31, "color"], [786, 23, 593, 36], [786, 25, 593, 37], [787, 16, 593, 43], [788, 18, 593, 43, "fileName"], [788, 26, 593, 43], [788, 28, 593, 43, "_jsxFileName"], [788, 40, 593, 43], [789, 18, 593, 43, "lineNumber"], [789, 28, 593, 43], [790, 18, 593, 43, "columnNumber"], [790, 30, 593, 43], [791, 16, 593, 43], [791, 23, 593, 45], [792, 14, 593, 46], [793, 16, 593, 46, "fileName"], [793, 24, 593, 46], [793, 26, 593, 46, "_jsxFileName"], [793, 38, 593, 46], [794, 16, 593, 46, "lineNumber"], [794, 26, 593, 46], [795, 16, 593, 46, "columnNumber"], [795, 28, 593, 46], [796, 14, 593, 46], [796, 21, 594, 34], [796, 22, 594, 35], [797, 12, 594, 35], [798, 14, 594, 35, "fileName"], [798, 22, 594, 35], [798, 24, 594, 35, "_jsxFileName"], [798, 36, 594, 35], [799, 14, 594, 35, "lineNumber"], [799, 24, 594, 35], [800, 14, 594, 35, "columnNumber"], [800, 26, 594, 35], [801, 12, 594, 35], [801, 19, 595, 20], [802, 10, 595, 21], [803, 12, 595, 21, "fileName"], [803, 20, 595, 21], [803, 22, 595, 21, "_jsxFileName"], [803, 34, 595, 21], [804, 12, 595, 21, "lineNumber"], [804, 22, 595, 21], [805, 12, 595, 21, "columnNumber"], [805, 24, 595, 21], [806, 10, 595, 21], [806, 17, 596, 18], [806, 18, 596, 19], [806, 33, 598, 12], [806, 37, 598, 12, "_jsxDevRuntime"], [806, 51, 598, 12], [806, 52, 598, 12, "jsxDEV"], [806, 58, 598, 12], [806, 60, 598, 13, "_View"], [806, 65, 598, 13], [806, 66, 598, 13, "default"], [806, 73, 598, 17], [807, 12, 598, 18, "style"], [807, 17, 598, 23], [807, 19, 598, 25, "styles"], [807, 25, 598, 31], [807, 26, 598, 32, "privacyNotice"], [807, 39, 598, 46], [808, 12, 598, 46, "children"], [808, 20, 598, 46], [808, 36, 599, 14], [808, 40, 599, 14, "_jsxDevRuntime"], [808, 54, 599, 14], [808, 55, 599, 14, "jsxDEV"], [808, 61, 599, 14], [808, 63, 599, 15, "_lucideReactNative"], [808, 81, 599, 15], [808, 82, 599, 15, "Shield"], [808, 88, 599, 21], [809, 14, 599, 22, "size"], [809, 18, 599, 26], [809, 20, 599, 28], [809, 22, 599, 31], [810, 14, 599, 32, "color"], [810, 19, 599, 37], [810, 21, 599, 38], [811, 12, 599, 47], [812, 14, 599, 47, "fileName"], [812, 22, 599, 47], [812, 24, 599, 47, "_jsxFileName"], [812, 36, 599, 47], [813, 14, 599, 47, "lineNumber"], [813, 24, 599, 47], [814, 14, 599, 47, "columnNumber"], [814, 26, 599, 47], [815, 12, 599, 47], [815, 19, 599, 49], [815, 20, 599, 50], [815, 35, 600, 14], [815, 39, 600, 14, "_jsxDevRuntime"], [815, 53, 600, 14], [815, 54, 600, 14, "jsxDEV"], [815, 60, 600, 14], [815, 62, 600, 15, "_Text"], [815, 67, 600, 15], [815, 68, 600, 15, "default"], [815, 75, 600, 19], [816, 14, 600, 20, "style"], [816, 19, 600, 25], [816, 21, 600, 27, "styles"], [816, 27, 600, 33], [816, 28, 600, 34, "privacyText"], [816, 39, 600, 46], [817, 14, 600, 46, "children"], [817, 22, 600, 46], [817, 24, 600, 47], [818, 12, 602, 14], [819, 14, 602, 14, "fileName"], [819, 22, 602, 14], [819, 24, 602, 14, "_jsxFileName"], [819, 36, 602, 14], [820, 14, 602, 14, "lineNumber"], [820, 24, 602, 14], [821, 14, 602, 14, "columnNumber"], [821, 26, 602, 14], [822, 12, 602, 14], [822, 19, 602, 20], [822, 20, 602, 21], [823, 10, 602, 21], [824, 12, 602, 21, "fileName"], [824, 20, 602, 21], [824, 22, 602, 21, "_jsxFileName"], [824, 34, 602, 21], [825, 12, 602, 21, "lineNumber"], [825, 22, 602, 21], [826, 12, 602, 21, "columnNumber"], [826, 24, 602, 21], [827, 10, 602, 21], [827, 17, 603, 18], [827, 18, 603, 19], [827, 33, 605, 12], [827, 37, 605, 12, "_jsxDevRuntime"], [827, 51, 605, 12], [827, 52, 605, 12, "jsxDEV"], [827, 58, 605, 12], [827, 60, 605, 13, "_View"], [827, 65, 605, 13], [827, 66, 605, 13, "default"], [827, 73, 605, 17], [828, 12, 605, 18, "style"], [828, 17, 605, 23], [828, 19, 605, 25, "styles"], [828, 25, 605, 31], [828, 26, 605, 32, "footer<PERSON><PERSON><PERSON>"], [828, 39, 605, 46], [829, 12, 605, 46, "children"], [829, 20, 605, 46], [829, 36, 606, 14], [829, 40, 606, 14, "_jsxDevRuntime"], [829, 54, 606, 14], [829, 55, 606, 14, "jsxDEV"], [829, 61, 606, 14], [829, 63, 606, 15, "_Text"], [829, 68, 606, 15], [829, 69, 606, 15, "default"], [829, 76, 606, 19], [830, 14, 606, 20, "style"], [830, 19, 606, 25], [830, 21, 606, 27, "styles"], [830, 27, 606, 33], [830, 28, 606, 34, "instruction"], [830, 39, 606, 46], [831, 14, 606, 46, "children"], [831, 22, 606, 46], [831, 24, 606, 47], [832, 12, 608, 14], [833, 14, 608, 14, "fileName"], [833, 22, 608, 14], [833, 24, 608, 14, "_jsxFileName"], [833, 36, 608, 14], [834, 14, 608, 14, "lineNumber"], [834, 24, 608, 14], [835, 14, 608, 14, "columnNumber"], [835, 26, 608, 14], [836, 12, 608, 14], [836, 19, 608, 20], [836, 20, 608, 21], [836, 35, 610, 14], [836, 39, 610, 14, "_jsxDevRuntime"], [836, 53, 610, 14], [836, 54, 610, 14, "jsxDEV"], [836, 60, 610, 14], [836, 62, 610, 15, "_TouchableOpacity"], [836, 79, 610, 15], [836, 80, 610, 15, "default"], [836, 87, 610, 31], [837, 14, 611, 16, "onPress"], [837, 21, 611, 23], [837, 23, 611, 25, "capturePhoto"], [837, 35, 611, 38], [838, 14, 612, 16, "disabled"], [838, 22, 612, 24], [838, 24, 612, 26, "processingState"], [838, 39, 612, 41], [838, 44, 612, 46], [838, 50, 612, 52], [838, 54, 612, 56], [838, 55, 612, 57, "isCameraReady"], [838, 68, 612, 71], [839, 14, 613, 16, "style"], [839, 19, 613, 21], [839, 21, 613, 23], [839, 22, 614, 18, "styles"], [839, 28, 614, 24], [839, 29, 614, 25, "shutterButton"], [839, 42, 614, 38], [839, 44, 615, 18, "processingState"], [839, 59, 615, 33], [839, 64, 615, 38], [839, 70, 615, 44], [839, 74, 615, 48, "styles"], [839, 80, 615, 54], [839, 81, 615, 55, "shutterButtonDisabled"], [839, 102, 615, 76], [839, 103, 616, 18], [840, 14, 616, 18, "children"], [840, 22, 616, 18], [840, 24, 618, 17, "processingState"], [840, 39, 618, 32], [840, 44, 618, 37], [840, 50, 618, 43], [840, 66, 619, 18], [840, 70, 619, 18, "_jsxDevRuntime"], [840, 84, 619, 18], [840, 85, 619, 18, "jsxDEV"], [840, 91, 619, 18], [840, 93, 619, 19, "_View"], [840, 98, 619, 19], [840, 99, 619, 19, "default"], [840, 106, 619, 23], [841, 16, 619, 24, "style"], [841, 21, 619, 29], [841, 23, 619, 31, "styles"], [841, 29, 619, 37], [841, 30, 619, 38, "shutterInner"], [842, 14, 619, 51], [843, 16, 619, 51, "fileName"], [843, 24, 619, 51], [843, 26, 619, 51, "_jsxFileName"], [843, 38, 619, 51], [844, 16, 619, 51, "lineNumber"], [844, 26, 619, 51], [845, 16, 619, 51, "columnNumber"], [845, 28, 619, 51], [846, 14, 619, 51], [846, 21, 619, 53], [846, 22, 619, 54], [846, 38, 621, 18], [846, 42, 621, 18, "_jsxDevRuntime"], [846, 56, 621, 18], [846, 57, 621, 18, "jsxDEV"], [846, 63, 621, 18], [846, 65, 621, 19, "_ActivityIndicator"], [846, 83, 621, 19], [846, 84, 621, 19, "default"], [846, 91, 621, 36], [847, 16, 621, 37, "size"], [847, 20, 621, 41], [847, 22, 621, 42], [847, 29, 621, 49], [848, 16, 621, 50, "color"], [848, 21, 621, 55], [848, 23, 621, 56], [849, 14, 621, 65], [850, 16, 621, 65, "fileName"], [850, 24, 621, 65], [850, 26, 621, 65, "_jsxFileName"], [850, 38, 621, 65], [851, 16, 621, 65, "lineNumber"], [851, 26, 621, 65], [852, 16, 621, 65, "columnNumber"], [852, 28, 621, 65], [853, 14, 621, 65], [853, 21, 621, 67], [854, 12, 622, 17], [855, 14, 622, 17, "fileName"], [855, 22, 622, 17], [855, 24, 622, 17, "_jsxFileName"], [855, 36, 622, 17], [856, 14, 622, 17, "lineNumber"], [856, 24, 622, 17], [857, 14, 622, 17, "columnNumber"], [857, 26, 622, 17], [858, 12, 622, 17], [858, 19, 623, 32], [858, 20, 623, 33], [858, 35, 624, 14], [858, 39, 624, 14, "_jsxDevRuntime"], [858, 53, 624, 14], [858, 54, 624, 14, "jsxDEV"], [858, 60, 624, 14], [858, 62, 624, 15, "_Text"], [858, 67, 624, 15], [858, 68, 624, 15, "default"], [858, 75, 624, 19], [859, 14, 624, 20, "style"], [859, 19, 624, 25], [859, 21, 624, 27, "styles"], [859, 27, 624, 33], [859, 28, 624, 34, "privacyNote"], [859, 39, 624, 46], [860, 14, 624, 46, "children"], [860, 22, 624, 46], [860, 24, 624, 47], [861, 12, 626, 14], [862, 14, 626, 14, "fileName"], [862, 22, 626, 14], [862, 24, 626, 14, "_jsxFileName"], [862, 36, 626, 14], [863, 14, 626, 14, "lineNumber"], [863, 24, 626, 14], [864, 14, 626, 14, "columnNumber"], [864, 26, 626, 14], [865, 12, 626, 14], [865, 19, 626, 20], [865, 20, 626, 21], [866, 10, 626, 21], [867, 12, 626, 21, "fileName"], [867, 20, 626, 21], [867, 22, 626, 21, "_jsxFileName"], [867, 34, 626, 21], [868, 12, 626, 21, "lineNumber"], [868, 22, 626, 21], [869, 12, 626, 21, "columnNumber"], [869, 24, 626, 21], [870, 10, 626, 21], [870, 17, 627, 18], [870, 18, 627, 19], [871, 8, 627, 19], [871, 23, 628, 12], [871, 24, 629, 9], [872, 6, 629, 9], [873, 8, 629, 9, "fileName"], [873, 16, 629, 9], [873, 18, 629, 9, "_jsxFileName"], [873, 30, 629, 9], [874, 8, 629, 9, "lineNumber"], [874, 18, 629, 9], [875, 8, 629, 9, "columnNumber"], [875, 20, 629, 9], [876, 6, 629, 9], [876, 13, 630, 12], [876, 14, 630, 13], [876, 29, 632, 6], [876, 33, 632, 6, "_jsxDevRuntime"], [876, 47, 632, 6], [876, 48, 632, 6, "jsxDEV"], [876, 54, 632, 6], [876, 56, 632, 7, "_Modal"], [876, 62, 632, 7], [876, 63, 632, 7, "default"], [876, 70, 632, 12], [877, 8, 633, 8, "visible"], [877, 15, 633, 15], [877, 17, 633, 17, "processingState"], [877, 32, 633, 32], [877, 37, 633, 37], [877, 43, 633, 43], [877, 47, 633, 47, "processingState"], [877, 62, 633, 62], [877, 67, 633, 67], [877, 74, 633, 75], [878, 8, 634, 8, "transparent"], [878, 19, 634, 19], [879, 8, 635, 8, "animationType"], [879, 21, 635, 21], [879, 23, 635, 22], [879, 29, 635, 28], [880, 8, 635, 28, "children"], [880, 16, 635, 28], [880, 31, 637, 8], [880, 35, 637, 8, "_jsxDevRuntime"], [880, 49, 637, 8], [880, 50, 637, 8, "jsxDEV"], [880, 56, 637, 8], [880, 58, 637, 9, "_View"], [880, 63, 637, 9], [880, 64, 637, 9, "default"], [880, 71, 637, 13], [881, 10, 637, 14, "style"], [881, 15, 637, 19], [881, 17, 637, 21, "styles"], [881, 23, 637, 27], [881, 24, 637, 28, "processingModal"], [881, 39, 637, 44], [882, 10, 637, 44, "children"], [882, 18, 637, 44], [882, 33, 638, 10], [882, 37, 638, 10, "_jsxDevRuntime"], [882, 51, 638, 10], [882, 52, 638, 10, "jsxDEV"], [882, 58, 638, 10], [882, 60, 638, 11, "_View"], [882, 65, 638, 11], [882, 66, 638, 11, "default"], [882, 73, 638, 15], [883, 12, 638, 16, "style"], [883, 17, 638, 21], [883, 19, 638, 23, "styles"], [883, 25, 638, 29], [883, 26, 638, 30, "processingContent"], [883, 43, 638, 48], [884, 12, 638, 48, "children"], [884, 20, 638, 48], [884, 36, 639, 12], [884, 40, 639, 12, "_jsxDevRuntime"], [884, 54, 639, 12], [884, 55, 639, 12, "jsxDEV"], [884, 61, 639, 12], [884, 63, 639, 13, "_ActivityIndicator"], [884, 81, 639, 13], [884, 82, 639, 13, "default"], [884, 89, 639, 30], [885, 14, 639, 31, "size"], [885, 18, 639, 35], [885, 20, 639, 36], [885, 27, 639, 43], [886, 14, 639, 44, "color"], [886, 19, 639, 49], [886, 21, 639, 50], [887, 12, 639, 59], [888, 14, 639, 59, "fileName"], [888, 22, 639, 59], [888, 24, 639, 59, "_jsxFileName"], [888, 36, 639, 59], [889, 14, 639, 59, "lineNumber"], [889, 24, 639, 59], [890, 14, 639, 59, "columnNumber"], [890, 26, 639, 59], [891, 12, 639, 59], [891, 19, 639, 61], [891, 20, 639, 62], [891, 35, 641, 12], [891, 39, 641, 12, "_jsxDevRuntime"], [891, 53, 641, 12], [891, 54, 641, 12, "jsxDEV"], [891, 60, 641, 12], [891, 62, 641, 13, "_Text"], [891, 67, 641, 13], [891, 68, 641, 13, "default"], [891, 75, 641, 17], [892, 14, 641, 18, "style"], [892, 19, 641, 23], [892, 21, 641, 25, "styles"], [892, 27, 641, 31], [892, 28, 641, 32, "processingTitle"], [892, 43, 641, 48], [893, 14, 641, 48, "children"], [893, 22, 641, 48], [893, 25, 642, 15, "processingState"], [893, 40, 642, 30], [893, 45, 642, 35], [893, 56, 642, 46], [893, 60, 642, 50], [893, 80, 642, 70], [893, 82, 643, 15, "processingState"], [893, 97, 643, 30], [893, 102, 643, 35], [893, 113, 643, 46], [893, 117, 643, 50], [893, 146, 643, 79], [893, 148, 644, 15, "processingState"], [893, 163, 644, 30], [893, 168, 644, 35], [893, 180, 644, 47], [893, 184, 644, 51], [893, 216, 644, 83], [893, 218, 645, 15, "processingState"], [893, 233, 645, 30], [893, 238, 645, 35], [893, 249, 645, 46], [893, 253, 645, 50], [893, 275, 645, 72], [894, 12, 645, 72], [895, 14, 645, 72, "fileName"], [895, 22, 645, 72], [895, 24, 645, 72, "_jsxFileName"], [895, 36, 645, 72], [896, 14, 645, 72, "lineNumber"], [896, 24, 645, 72], [897, 14, 645, 72, "columnNumber"], [897, 26, 645, 72], [898, 12, 645, 72], [898, 19, 646, 18], [898, 20, 646, 19], [898, 35, 647, 12], [898, 39, 647, 12, "_jsxDevRuntime"], [898, 53, 647, 12], [898, 54, 647, 12, "jsxDEV"], [898, 60, 647, 12], [898, 62, 647, 13, "_View"], [898, 67, 647, 13], [898, 68, 647, 13, "default"], [898, 75, 647, 17], [899, 14, 647, 18, "style"], [899, 19, 647, 23], [899, 21, 647, 25, "styles"], [899, 27, 647, 31], [899, 28, 647, 32, "progressBar"], [899, 39, 647, 44], [900, 14, 647, 44, "children"], [900, 22, 647, 44], [900, 37, 648, 14], [900, 41, 648, 14, "_jsxDevRuntime"], [900, 55, 648, 14], [900, 56, 648, 14, "jsxDEV"], [900, 62, 648, 14], [900, 64, 648, 15, "_View"], [900, 69, 648, 15], [900, 70, 648, 15, "default"], [900, 77, 648, 19], [901, 16, 649, 16, "style"], [901, 21, 649, 21], [901, 23, 649, 23], [901, 24, 650, 18, "styles"], [901, 30, 650, 24], [901, 31, 650, 25, "progressFill"], [901, 43, 650, 37], [901, 45, 651, 18], [902, 18, 651, 20, "width"], [902, 23, 651, 25], [902, 25, 651, 27], [902, 28, 651, 30, "processingProgress"], [902, 46, 651, 48], [903, 16, 651, 52], [903, 17, 651, 53], [904, 14, 652, 18], [905, 16, 652, 18, "fileName"], [905, 24, 652, 18], [905, 26, 652, 18, "_jsxFileName"], [905, 38, 652, 18], [906, 16, 652, 18, "lineNumber"], [906, 26, 652, 18], [907, 16, 652, 18, "columnNumber"], [907, 28, 652, 18], [908, 14, 652, 18], [908, 21, 653, 15], [909, 12, 653, 16], [910, 14, 653, 16, "fileName"], [910, 22, 653, 16], [910, 24, 653, 16, "_jsxFileName"], [910, 36, 653, 16], [911, 14, 653, 16, "lineNumber"], [911, 24, 653, 16], [912, 14, 653, 16, "columnNumber"], [912, 26, 653, 16], [913, 12, 653, 16], [913, 19, 654, 18], [913, 20, 654, 19], [913, 35, 655, 12], [913, 39, 655, 12, "_jsxDevRuntime"], [913, 53, 655, 12], [913, 54, 655, 12, "jsxDEV"], [913, 60, 655, 12], [913, 62, 655, 13, "_Text"], [913, 67, 655, 13], [913, 68, 655, 13, "default"], [913, 75, 655, 17], [914, 14, 655, 18, "style"], [914, 19, 655, 23], [914, 21, 655, 25, "styles"], [914, 27, 655, 31], [914, 28, 655, 32, "processingDescription"], [914, 49, 655, 54], [915, 14, 655, 54, "children"], [915, 22, 655, 54], [915, 25, 656, 15, "processingState"], [915, 40, 656, 30], [915, 45, 656, 35], [915, 56, 656, 46], [915, 60, 656, 50], [915, 89, 656, 79], [915, 91, 657, 15, "processingState"], [915, 106, 657, 30], [915, 111, 657, 35], [915, 122, 657, 46], [915, 126, 657, 50], [915, 164, 657, 88], [915, 166, 658, 15, "processingState"], [915, 181, 658, 30], [915, 186, 658, 35], [915, 198, 658, 47], [915, 202, 658, 51], [915, 247, 658, 96], [915, 249, 659, 15, "processingState"], [915, 264, 659, 30], [915, 269, 659, 35], [915, 280, 659, 46], [915, 284, 659, 50], [915, 325, 659, 91], [916, 12, 659, 91], [917, 14, 659, 91, "fileName"], [917, 22, 659, 91], [917, 24, 659, 91, "_jsxFileName"], [917, 36, 659, 91], [918, 14, 659, 91, "lineNumber"], [918, 24, 659, 91], [919, 14, 659, 91, "columnNumber"], [919, 26, 659, 91], [920, 12, 659, 91], [920, 19, 660, 18], [920, 20, 660, 19], [920, 22, 661, 13, "processingState"], [920, 37, 661, 28], [920, 42, 661, 33], [920, 53, 661, 44], [920, 70, 662, 14], [920, 74, 662, 14, "_jsxDevRuntime"], [920, 88, 662, 14], [920, 89, 662, 14, "jsxDEV"], [920, 95, 662, 14], [920, 97, 662, 15, "_lucideReactNative"], [920, 115, 662, 15], [920, 116, 662, 15, "CheckCircle"], [920, 127, 662, 26], [921, 14, 662, 27, "size"], [921, 18, 662, 31], [921, 20, 662, 33], [921, 22, 662, 36], [922, 14, 662, 37, "color"], [922, 19, 662, 42], [922, 21, 662, 43], [922, 30, 662, 52], [923, 14, 662, 53, "style"], [923, 19, 662, 58], [923, 21, 662, 60, "styles"], [923, 27, 662, 66], [923, 28, 662, 67, "successIcon"], [924, 12, 662, 79], [925, 14, 662, 79, "fileName"], [925, 22, 662, 79], [925, 24, 662, 79, "_jsxFileName"], [925, 36, 662, 79], [926, 14, 662, 79, "lineNumber"], [926, 24, 662, 79], [927, 14, 662, 79, "columnNumber"], [927, 26, 662, 79], [928, 12, 662, 79], [928, 19, 662, 81], [928, 20, 663, 13], [929, 10, 663, 13], [930, 12, 663, 13, "fileName"], [930, 20, 663, 13], [930, 22, 663, 13, "_jsxFileName"], [930, 34, 663, 13], [931, 12, 663, 13, "lineNumber"], [931, 22, 663, 13], [932, 12, 663, 13, "columnNumber"], [932, 24, 663, 13], [933, 10, 663, 13], [933, 17, 664, 16], [934, 8, 664, 17], [935, 10, 664, 17, "fileName"], [935, 18, 664, 17], [935, 20, 664, 17, "_jsxFileName"], [935, 32, 664, 17], [936, 10, 664, 17, "lineNumber"], [936, 20, 664, 17], [937, 10, 664, 17, "columnNumber"], [937, 22, 664, 17], [938, 8, 664, 17], [938, 15, 665, 14], [939, 6, 665, 15], [940, 8, 665, 15, "fileName"], [940, 16, 665, 15], [940, 18, 665, 15, "_jsxFileName"], [940, 30, 665, 15], [941, 8, 665, 15, "lineNumber"], [941, 18, 665, 15], [942, 8, 665, 15, "columnNumber"], [942, 20, 665, 15], [943, 6, 665, 15], [943, 13, 666, 13], [943, 14, 666, 14], [943, 29, 668, 6], [943, 33, 668, 6, "_jsxDevRuntime"], [943, 47, 668, 6], [943, 48, 668, 6, "jsxDEV"], [943, 54, 668, 6], [943, 56, 668, 7, "_Modal"], [943, 62, 668, 7], [943, 63, 668, 7, "default"], [943, 70, 668, 12], [944, 8, 669, 8, "visible"], [944, 15, 669, 15], [944, 17, 669, 17, "processingState"], [944, 32, 669, 32], [944, 37, 669, 37], [944, 44, 669, 45], [945, 8, 670, 8, "transparent"], [945, 19, 670, 19], [946, 8, 671, 8, "animationType"], [946, 21, 671, 21], [946, 23, 671, 22], [946, 29, 671, 28], [947, 8, 671, 28, "children"], [947, 16, 671, 28], [947, 31, 673, 8], [947, 35, 673, 8, "_jsxDevRuntime"], [947, 49, 673, 8], [947, 50, 673, 8, "jsxDEV"], [947, 56, 673, 8], [947, 58, 673, 9, "_View"], [947, 63, 673, 9], [947, 64, 673, 9, "default"], [947, 71, 673, 13], [948, 10, 673, 14, "style"], [948, 15, 673, 19], [948, 17, 673, 21, "styles"], [948, 23, 673, 27], [948, 24, 673, 28, "processingModal"], [948, 39, 673, 44], [949, 10, 673, 44, "children"], [949, 18, 673, 44], [949, 33, 674, 10], [949, 37, 674, 10, "_jsxDevRuntime"], [949, 51, 674, 10], [949, 52, 674, 10, "jsxDEV"], [949, 58, 674, 10], [949, 60, 674, 11, "_View"], [949, 65, 674, 11], [949, 66, 674, 11, "default"], [949, 73, 674, 15], [950, 12, 674, 16, "style"], [950, 17, 674, 21], [950, 19, 674, 23, "styles"], [950, 25, 674, 29], [950, 26, 674, 30, "errorContent"], [950, 38, 674, 43], [951, 12, 674, 43, "children"], [951, 20, 674, 43], [951, 36, 675, 12], [951, 40, 675, 12, "_jsxDevRuntime"], [951, 54, 675, 12], [951, 55, 675, 12, "jsxDEV"], [951, 61, 675, 12], [951, 63, 675, 13, "_lucideReactNative"], [951, 81, 675, 13], [951, 82, 675, 13, "X"], [951, 83, 675, 14], [952, 14, 675, 15, "size"], [952, 18, 675, 19], [952, 20, 675, 21], [952, 22, 675, 24], [953, 14, 675, 25, "color"], [953, 19, 675, 30], [953, 21, 675, 31], [954, 12, 675, 40], [955, 14, 675, 40, "fileName"], [955, 22, 675, 40], [955, 24, 675, 40, "_jsxFileName"], [955, 36, 675, 40], [956, 14, 675, 40, "lineNumber"], [956, 24, 675, 40], [957, 14, 675, 40, "columnNumber"], [957, 26, 675, 40], [958, 12, 675, 40], [958, 19, 675, 42], [958, 20, 675, 43], [958, 35, 676, 12], [958, 39, 676, 12, "_jsxDevRuntime"], [958, 53, 676, 12], [958, 54, 676, 12, "jsxDEV"], [958, 60, 676, 12], [958, 62, 676, 13, "_Text"], [958, 67, 676, 13], [958, 68, 676, 13, "default"], [958, 75, 676, 17], [959, 14, 676, 18, "style"], [959, 19, 676, 23], [959, 21, 676, 25, "styles"], [959, 27, 676, 31], [959, 28, 676, 32, "errorTitle"], [959, 38, 676, 43], [960, 14, 676, 43, "children"], [960, 22, 676, 43], [960, 24, 676, 44], [961, 12, 676, 61], [962, 14, 676, 61, "fileName"], [962, 22, 676, 61], [962, 24, 676, 61, "_jsxFileName"], [962, 36, 676, 61], [963, 14, 676, 61, "lineNumber"], [963, 24, 676, 61], [964, 14, 676, 61, "columnNumber"], [964, 26, 676, 61], [965, 12, 676, 61], [965, 19, 676, 67], [965, 20, 676, 68], [965, 35, 677, 12], [965, 39, 677, 12, "_jsxDevRuntime"], [965, 53, 677, 12], [965, 54, 677, 12, "jsxDEV"], [965, 60, 677, 12], [965, 62, 677, 13, "_Text"], [965, 67, 677, 13], [965, 68, 677, 13, "default"], [965, 75, 677, 17], [966, 14, 677, 18, "style"], [966, 19, 677, 23], [966, 21, 677, 25, "styles"], [966, 27, 677, 31], [966, 28, 677, 32, "errorMessage"], [966, 40, 677, 45], [967, 14, 677, 45, "children"], [967, 22, 677, 45], [967, 24, 677, 47, "errorMessage"], [968, 12, 677, 59], [969, 14, 677, 59, "fileName"], [969, 22, 677, 59], [969, 24, 677, 59, "_jsxFileName"], [969, 36, 677, 59], [970, 14, 677, 59, "lineNumber"], [970, 24, 677, 59], [971, 14, 677, 59, "columnNumber"], [971, 26, 677, 59], [972, 12, 677, 59], [972, 19, 677, 66], [972, 20, 677, 67], [972, 35, 678, 12], [972, 39, 678, 12, "_jsxDevRuntime"], [972, 53, 678, 12], [972, 54, 678, 12, "jsxDEV"], [972, 60, 678, 12], [972, 62, 678, 13, "_TouchableOpacity"], [972, 79, 678, 13], [972, 80, 678, 13, "default"], [972, 87, 678, 29], [973, 14, 679, 14, "onPress"], [973, 21, 679, 21], [973, 23, 679, 23, "retryCapture"], [973, 35, 679, 36], [974, 14, 680, 14, "style"], [974, 19, 680, 19], [974, 21, 680, 21, "styles"], [974, 27, 680, 27], [974, 28, 680, 28, "primaryButton"], [974, 41, 680, 42], [975, 14, 680, 42, "children"], [975, 22, 680, 42], [975, 37, 682, 14], [975, 41, 682, 14, "_jsxDevRuntime"], [975, 55, 682, 14], [975, 56, 682, 14, "jsxDEV"], [975, 62, 682, 14], [975, 64, 682, 15, "_Text"], [975, 69, 682, 15], [975, 70, 682, 15, "default"], [975, 77, 682, 19], [976, 16, 682, 20, "style"], [976, 21, 682, 25], [976, 23, 682, 27, "styles"], [976, 29, 682, 33], [976, 30, 682, 34, "primaryButtonText"], [976, 47, 682, 52], [977, 16, 682, 52, "children"], [977, 24, 682, 52], [977, 26, 682, 53], [978, 14, 682, 62], [979, 16, 682, 62, "fileName"], [979, 24, 682, 62], [979, 26, 682, 62, "_jsxFileName"], [979, 38, 682, 62], [980, 16, 682, 62, "lineNumber"], [980, 26, 682, 62], [981, 16, 682, 62, "columnNumber"], [981, 28, 682, 62], [982, 14, 682, 62], [982, 21, 682, 68], [983, 12, 682, 69], [984, 14, 682, 69, "fileName"], [984, 22, 682, 69], [984, 24, 682, 69, "_jsxFileName"], [984, 36, 682, 69], [985, 14, 682, 69, "lineNumber"], [985, 24, 682, 69], [986, 14, 682, 69, "columnNumber"], [986, 26, 682, 69], [987, 12, 682, 69], [987, 19, 683, 30], [987, 20, 683, 31], [987, 35, 684, 12], [987, 39, 684, 12, "_jsxDevRuntime"], [987, 53, 684, 12], [987, 54, 684, 12, "jsxDEV"], [987, 60, 684, 12], [987, 62, 684, 13, "_TouchableOpacity"], [987, 79, 684, 13], [987, 80, 684, 13, "default"], [987, 87, 684, 29], [988, 14, 685, 14, "onPress"], [988, 21, 685, 21], [988, 23, 685, 23, "onCancel"], [988, 31, 685, 32], [989, 14, 686, 14, "style"], [989, 19, 686, 19], [989, 21, 686, 21, "styles"], [989, 27, 686, 27], [989, 28, 686, 28, "secondaryButton"], [989, 43, 686, 44], [990, 14, 686, 44, "children"], [990, 22, 686, 44], [990, 37, 688, 14], [990, 41, 688, 14, "_jsxDevRuntime"], [990, 55, 688, 14], [990, 56, 688, 14, "jsxDEV"], [990, 62, 688, 14], [990, 64, 688, 15, "_Text"], [990, 69, 688, 15], [990, 70, 688, 15, "default"], [990, 77, 688, 19], [991, 16, 688, 20, "style"], [991, 21, 688, 25], [991, 23, 688, 27, "styles"], [991, 29, 688, 33], [991, 30, 688, 34, "secondaryButtonText"], [991, 49, 688, 54], [992, 16, 688, 54, "children"], [992, 24, 688, 54], [992, 26, 688, 55], [993, 14, 688, 61], [994, 16, 688, 61, "fileName"], [994, 24, 688, 61], [994, 26, 688, 61, "_jsxFileName"], [994, 38, 688, 61], [995, 16, 688, 61, "lineNumber"], [995, 26, 688, 61], [996, 16, 688, 61, "columnNumber"], [996, 28, 688, 61], [997, 14, 688, 61], [997, 21, 688, 67], [998, 12, 688, 68], [999, 14, 688, 68, "fileName"], [999, 22, 688, 68], [999, 24, 688, 68, "_jsxFileName"], [999, 36, 688, 68], [1000, 14, 688, 68, "lineNumber"], [1000, 24, 688, 68], [1001, 14, 688, 68, "columnNumber"], [1001, 26, 688, 68], [1002, 12, 688, 68], [1002, 19, 689, 30], [1002, 20, 689, 31], [1003, 10, 689, 31], [1004, 12, 689, 31, "fileName"], [1004, 20, 689, 31], [1004, 22, 689, 31, "_jsxFileName"], [1004, 34, 689, 31], [1005, 12, 689, 31, "lineNumber"], [1005, 22, 689, 31], [1006, 12, 689, 31, "columnNumber"], [1006, 24, 689, 31], [1007, 10, 689, 31], [1007, 17, 690, 16], [1008, 8, 690, 17], [1009, 10, 690, 17, "fileName"], [1009, 18, 690, 17], [1009, 20, 690, 17, "_jsxFileName"], [1009, 32, 690, 17], [1010, 10, 690, 17, "lineNumber"], [1010, 20, 690, 17], [1011, 10, 690, 17, "columnNumber"], [1011, 22, 690, 17], [1012, 8, 690, 17], [1012, 15, 691, 14], [1013, 6, 691, 15], [1014, 8, 691, 15, "fileName"], [1014, 16, 691, 15], [1014, 18, 691, 15, "_jsxFileName"], [1014, 30, 691, 15], [1015, 8, 691, 15, "lineNumber"], [1015, 18, 691, 15], [1016, 8, 691, 15, "columnNumber"], [1016, 20, 691, 15], [1017, 6, 691, 15], [1017, 13, 692, 13], [1017, 14, 692, 14], [1018, 4, 692, 14], [1019, 6, 692, 14, "fileName"], [1019, 14, 692, 14], [1019, 16, 692, 14, "_jsxFileName"], [1019, 28, 692, 14], [1020, 6, 692, 14, "lineNumber"], [1020, 16, 692, 14], [1021, 6, 692, 14, "columnNumber"], [1021, 18, 692, 14], [1022, 4, 692, 14], [1022, 11, 693, 10], [1022, 12, 693, 11], [1023, 2, 695, 0], [1024, 2, 695, 1, "_s"], [1024, 4, 695, 1], [1024, 5, 51, 24, "EchoCameraWeb"], [1024, 18, 51, 37], [1025, 4, 51, 37], [1025, 12, 58, 42, "useCameraPermissions"], [1025, 44, 58, 62], [1025, 46, 72, 19, "useUpload"], [1025, 64, 72, 28], [1026, 2, 72, 28], [1027, 2, 72, 28, "_c"], [1027, 4, 72, 28], [1027, 7, 51, 24, "EchoCameraWeb"], [1027, 20, 51, 37], [1028, 2, 696, 0], [1028, 8, 696, 6, "styles"], [1028, 14, 696, 12], [1028, 17, 696, 15, "StyleSheet"], [1028, 36, 696, 25], [1028, 37, 696, 26, "create"], [1028, 43, 696, 32], [1028, 44, 696, 33], [1029, 4, 697, 2, "container"], [1029, 13, 697, 11], [1029, 15, 697, 13], [1030, 6, 698, 4, "flex"], [1030, 10, 698, 8], [1030, 12, 698, 10], [1030, 13, 698, 11], [1031, 6, 699, 4, "backgroundColor"], [1031, 21, 699, 19], [1031, 23, 699, 21], [1032, 4, 700, 2], [1032, 5, 700, 3], [1033, 4, 701, 2, "cameraContainer"], [1033, 19, 701, 17], [1033, 21, 701, 19], [1034, 6, 702, 4, "flex"], [1034, 10, 702, 8], [1034, 12, 702, 10], [1034, 13, 702, 11], [1035, 6, 703, 4, "max<PERSON><PERSON><PERSON>"], [1035, 14, 703, 12], [1035, 16, 703, 14], [1035, 19, 703, 17], [1036, 6, 704, 4, "alignSelf"], [1036, 15, 704, 13], [1036, 17, 704, 15], [1036, 25, 704, 23], [1037, 6, 705, 4, "width"], [1037, 11, 705, 9], [1037, 13, 705, 11], [1038, 4, 706, 2], [1038, 5, 706, 3], [1039, 4, 707, 2, "camera"], [1039, 10, 707, 8], [1039, 12, 707, 10], [1040, 6, 708, 4, "flex"], [1040, 10, 708, 8], [1040, 12, 708, 10], [1041, 4, 709, 2], [1041, 5, 709, 3], [1042, 4, 710, 2, "headerOverlay"], [1042, 17, 710, 15], [1042, 19, 710, 17], [1043, 6, 711, 4, "position"], [1043, 14, 711, 12], [1043, 16, 711, 14], [1043, 26, 711, 24], [1044, 6, 712, 4, "top"], [1044, 9, 712, 7], [1044, 11, 712, 9], [1044, 12, 712, 10], [1045, 6, 713, 4, "left"], [1045, 10, 713, 8], [1045, 12, 713, 10], [1045, 13, 713, 11], [1046, 6, 714, 4, "right"], [1046, 11, 714, 9], [1046, 13, 714, 11], [1046, 14, 714, 12], [1047, 6, 715, 4, "backgroundColor"], [1047, 21, 715, 19], [1047, 23, 715, 21], [1047, 36, 715, 34], [1048, 6, 716, 4, "paddingTop"], [1048, 16, 716, 14], [1048, 18, 716, 16], [1048, 20, 716, 18], [1049, 6, 717, 4, "paddingHorizontal"], [1049, 23, 717, 21], [1049, 25, 717, 23], [1049, 27, 717, 25], [1050, 6, 718, 4, "paddingBottom"], [1050, 19, 718, 17], [1050, 21, 718, 19], [1051, 4, 719, 2], [1051, 5, 719, 3], [1052, 4, 720, 2, "headerContent"], [1052, 17, 720, 15], [1052, 19, 720, 17], [1053, 6, 721, 4, "flexDirection"], [1053, 19, 721, 17], [1053, 21, 721, 19], [1053, 26, 721, 24], [1054, 6, 722, 4, "justifyContent"], [1054, 20, 722, 18], [1054, 22, 722, 20], [1054, 37, 722, 35], [1055, 6, 723, 4, "alignItems"], [1055, 16, 723, 14], [1055, 18, 723, 16], [1056, 4, 724, 2], [1056, 5, 724, 3], [1057, 4, 725, 2, "headerLeft"], [1057, 14, 725, 12], [1057, 16, 725, 14], [1058, 6, 726, 4, "flex"], [1058, 10, 726, 8], [1058, 12, 726, 10], [1059, 4, 727, 2], [1059, 5, 727, 3], [1060, 4, 728, 2, "headerTitle"], [1060, 15, 728, 13], [1060, 17, 728, 15], [1061, 6, 729, 4, "fontSize"], [1061, 14, 729, 12], [1061, 16, 729, 14], [1061, 18, 729, 16], [1062, 6, 730, 4, "fontWeight"], [1062, 16, 730, 14], [1062, 18, 730, 16], [1062, 23, 730, 21], [1063, 6, 731, 4, "color"], [1063, 11, 731, 9], [1063, 13, 731, 11], [1063, 19, 731, 17], [1064, 6, 732, 4, "marginBottom"], [1064, 18, 732, 16], [1064, 20, 732, 18], [1065, 4, 733, 2], [1065, 5, 733, 3], [1066, 4, 734, 2, "subtitleRow"], [1066, 15, 734, 13], [1066, 17, 734, 15], [1067, 6, 735, 4, "flexDirection"], [1067, 19, 735, 17], [1067, 21, 735, 19], [1067, 26, 735, 24], [1068, 6, 736, 4, "alignItems"], [1068, 16, 736, 14], [1068, 18, 736, 16], [1068, 26, 736, 24], [1069, 6, 737, 4, "marginBottom"], [1069, 18, 737, 16], [1069, 20, 737, 18], [1070, 4, 738, 2], [1070, 5, 738, 3], [1071, 4, 739, 2, "webIcon"], [1071, 11, 739, 9], [1071, 13, 739, 11], [1072, 6, 740, 4, "fontSize"], [1072, 14, 740, 12], [1072, 16, 740, 14], [1072, 18, 740, 16], [1073, 6, 741, 4, "marginRight"], [1073, 17, 741, 15], [1073, 19, 741, 17], [1074, 4, 742, 2], [1074, 5, 742, 3], [1075, 4, 743, 2, "headerSubtitle"], [1075, 18, 743, 16], [1075, 20, 743, 18], [1076, 6, 744, 4, "fontSize"], [1076, 14, 744, 12], [1076, 16, 744, 14], [1076, 18, 744, 16], [1077, 6, 745, 4, "color"], [1077, 11, 745, 9], [1077, 13, 745, 11], [1077, 19, 745, 17], [1078, 6, 746, 4, "opacity"], [1078, 13, 746, 11], [1078, 15, 746, 13], [1079, 4, 747, 2], [1079, 5, 747, 3], [1080, 4, 748, 2, "challengeRow"], [1080, 16, 748, 14], [1080, 18, 748, 16], [1081, 6, 749, 4, "flexDirection"], [1081, 19, 749, 17], [1081, 21, 749, 19], [1081, 26, 749, 24], [1082, 6, 750, 4, "alignItems"], [1082, 16, 750, 14], [1082, 18, 750, 16], [1083, 4, 751, 2], [1083, 5, 751, 3], [1084, 4, 752, 2, "challengeCode"], [1084, 17, 752, 15], [1084, 19, 752, 17], [1085, 6, 753, 4, "fontSize"], [1085, 14, 753, 12], [1085, 16, 753, 14], [1085, 18, 753, 16], [1086, 6, 754, 4, "color"], [1086, 11, 754, 9], [1086, 13, 754, 11], [1086, 19, 754, 17], [1087, 6, 755, 4, "marginLeft"], [1087, 16, 755, 14], [1087, 18, 755, 16], [1087, 19, 755, 17], [1088, 6, 756, 4, "fontFamily"], [1088, 16, 756, 14], [1088, 18, 756, 16], [1089, 4, 757, 2], [1089, 5, 757, 3], [1090, 4, 758, 2, "closeButton"], [1090, 15, 758, 13], [1090, 17, 758, 15], [1091, 6, 759, 4, "padding"], [1091, 13, 759, 11], [1091, 15, 759, 13], [1092, 4, 760, 2], [1092, 5, 760, 3], [1093, 4, 761, 2, "privacyNotice"], [1093, 17, 761, 15], [1093, 19, 761, 17], [1094, 6, 762, 4, "position"], [1094, 14, 762, 12], [1094, 16, 762, 14], [1094, 26, 762, 24], [1095, 6, 763, 4, "top"], [1095, 9, 763, 7], [1095, 11, 763, 9], [1095, 14, 763, 12], [1096, 6, 764, 4, "left"], [1096, 10, 764, 8], [1096, 12, 764, 10], [1096, 14, 764, 12], [1097, 6, 765, 4, "right"], [1097, 11, 765, 9], [1097, 13, 765, 11], [1097, 15, 765, 13], [1098, 6, 766, 4, "backgroundColor"], [1098, 21, 766, 19], [1098, 23, 766, 21], [1098, 48, 766, 46], [1099, 6, 767, 4, "borderRadius"], [1099, 18, 767, 16], [1099, 20, 767, 18], [1099, 21, 767, 19], [1100, 6, 768, 4, "padding"], [1100, 13, 768, 11], [1100, 15, 768, 13], [1100, 17, 768, 15], [1101, 6, 769, 4, "flexDirection"], [1101, 19, 769, 17], [1101, 21, 769, 19], [1101, 26, 769, 24], [1102, 6, 770, 4, "alignItems"], [1102, 16, 770, 14], [1102, 18, 770, 16], [1103, 4, 771, 2], [1103, 5, 771, 3], [1104, 4, 772, 2, "privacyText"], [1104, 15, 772, 13], [1104, 17, 772, 15], [1105, 6, 773, 4, "color"], [1105, 11, 773, 9], [1105, 13, 773, 11], [1105, 19, 773, 17], [1106, 6, 774, 4, "fontSize"], [1106, 14, 774, 12], [1106, 16, 774, 14], [1106, 18, 774, 16], [1107, 6, 775, 4, "marginLeft"], [1107, 16, 775, 14], [1107, 18, 775, 16], [1107, 19, 775, 17], [1108, 6, 776, 4, "flex"], [1108, 10, 776, 8], [1108, 12, 776, 10], [1109, 4, 777, 2], [1109, 5, 777, 3], [1110, 4, 778, 2, "footer<PERSON><PERSON><PERSON>"], [1110, 17, 778, 15], [1110, 19, 778, 17], [1111, 6, 779, 4, "position"], [1111, 14, 779, 12], [1111, 16, 779, 14], [1111, 26, 779, 24], [1112, 6, 780, 4, "bottom"], [1112, 12, 780, 10], [1112, 14, 780, 12], [1112, 15, 780, 13], [1113, 6, 781, 4, "left"], [1113, 10, 781, 8], [1113, 12, 781, 10], [1113, 13, 781, 11], [1114, 6, 782, 4, "right"], [1114, 11, 782, 9], [1114, 13, 782, 11], [1114, 14, 782, 12], [1115, 6, 783, 4, "backgroundColor"], [1115, 21, 783, 19], [1115, 23, 783, 21], [1115, 36, 783, 34], [1116, 6, 784, 4, "paddingBottom"], [1116, 19, 784, 17], [1116, 21, 784, 19], [1116, 23, 784, 21], [1117, 6, 785, 4, "paddingTop"], [1117, 16, 785, 14], [1117, 18, 785, 16], [1117, 20, 785, 18], [1118, 6, 786, 4, "alignItems"], [1118, 16, 786, 14], [1118, 18, 786, 16], [1119, 4, 787, 2], [1119, 5, 787, 3], [1120, 4, 788, 2, "instruction"], [1120, 15, 788, 13], [1120, 17, 788, 15], [1121, 6, 789, 4, "fontSize"], [1121, 14, 789, 12], [1121, 16, 789, 14], [1121, 18, 789, 16], [1122, 6, 790, 4, "color"], [1122, 11, 790, 9], [1122, 13, 790, 11], [1122, 19, 790, 17], [1123, 6, 791, 4, "marginBottom"], [1123, 18, 791, 16], [1123, 20, 791, 18], [1124, 4, 792, 2], [1124, 5, 792, 3], [1125, 4, 793, 2, "shutterButton"], [1125, 17, 793, 15], [1125, 19, 793, 17], [1126, 6, 794, 4, "width"], [1126, 11, 794, 9], [1126, 13, 794, 11], [1126, 15, 794, 13], [1127, 6, 795, 4, "height"], [1127, 12, 795, 10], [1127, 14, 795, 12], [1127, 16, 795, 14], [1128, 6, 796, 4, "borderRadius"], [1128, 18, 796, 16], [1128, 20, 796, 18], [1128, 22, 796, 20], [1129, 6, 797, 4, "backgroundColor"], [1129, 21, 797, 19], [1129, 23, 797, 21], [1129, 29, 797, 27], [1130, 6, 798, 4, "justifyContent"], [1130, 20, 798, 18], [1130, 22, 798, 20], [1130, 30, 798, 28], [1131, 6, 799, 4, "alignItems"], [1131, 16, 799, 14], [1131, 18, 799, 16], [1131, 26, 799, 24], [1132, 6, 800, 4, "marginBottom"], [1132, 18, 800, 16], [1132, 20, 800, 18], [1132, 22, 800, 20], [1133, 6, 801, 4], [1133, 9, 801, 7, "Platform"], [1133, 26, 801, 15], [1133, 27, 801, 16, "select"], [1133, 33, 801, 22], [1133, 34, 801, 23], [1134, 8, 802, 6, "ios"], [1134, 11, 802, 9], [1134, 13, 802, 11], [1135, 10, 803, 8, "shadowColor"], [1135, 21, 803, 19], [1135, 23, 803, 21], [1135, 32, 803, 30], [1136, 10, 804, 8, "shadowOffset"], [1136, 22, 804, 20], [1136, 24, 804, 22], [1137, 12, 804, 24, "width"], [1137, 17, 804, 29], [1137, 19, 804, 31], [1137, 20, 804, 32], [1138, 12, 804, 34, "height"], [1138, 18, 804, 40], [1138, 20, 804, 42], [1139, 10, 804, 44], [1139, 11, 804, 45], [1140, 10, 805, 8, "shadowOpacity"], [1140, 23, 805, 21], [1140, 25, 805, 23], [1140, 28, 805, 26], [1141, 10, 806, 8, "shadowRadius"], [1141, 22, 806, 20], [1141, 24, 806, 22], [1142, 8, 807, 6], [1142, 9, 807, 7], [1143, 8, 808, 6, "android"], [1143, 15, 808, 13], [1143, 17, 808, 15], [1144, 10, 809, 8, "elevation"], [1144, 19, 809, 17], [1144, 21, 809, 19], [1145, 8, 810, 6], [1145, 9, 810, 7], [1146, 8, 811, 6, "web"], [1146, 11, 811, 9], [1146, 13, 811, 11], [1147, 10, 812, 8, "boxShadow"], [1147, 19, 812, 17], [1147, 21, 812, 19], [1148, 8, 813, 6], [1149, 6, 814, 4], [1149, 7, 814, 5], [1150, 4, 815, 2], [1150, 5, 815, 3], [1151, 4, 816, 2, "shutterButtonDisabled"], [1151, 25, 816, 23], [1151, 27, 816, 25], [1152, 6, 817, 4, "opacity"], [1152, 13, 817, 11], [1152, 15, 817, 13], [1153, 4, 818, 2], [1153, 5, 818, 3], [1154, 4, 819, 2, "shutterInner"], [1154, 16, 819, 14], [1154, 18, 819, 16], [1155, 6, 820, 4, "width"], [1155, 11, 820, 9], [1155, 13, 820, 11], [1155, 15, 820, 13], [1156, 6, 821, 4, "height"], [1156, 12, 821, 10], [1156, 14, 821, 12], [1156, 16, 821, 14], [1157, 6, 822, 4, "borderRadius"], [1157, 18, 822, 16], [1157, 20, 822, 18], [1157, 22, 822, 20], [1158, 6, 823, 4, "backgroundColor"], [1158, 21, 823, 19], [1158, 23, 823, 21], [1158, 29, 823, 27], [1159, 6, 824, 4, "borderWidth"], [1159, 17, 824, 15], [1159, 19, 824, 17], [1159, 20, 824, 18], [1160, 6, 825, 4, "borderColor"], [1160, 17, 825, 15], [1160, 19, 825, 17], [1161, 4, 826, 2], [1161, 5, 826, 3], [1162, 4, 827, 2, "privacyNote"], [1162, 15, 827, 13], [1162, 17, 827, 15], [1163, 6, 828, 4, "fontSize"], [1163, 14, 828, 12], [1163, 16, 828, 14], [1163, 18, 828, 16], [1164, 6, 829, 4, "color"], [1164, 11, 829, 9], [1164, 13, 829, 11], [1165, 4, 830, 2], [1165, 5, 830, 3], [1166, 4, 831, 2, "processingModal"], [1166, 19, 831, 17], [1166, 21, 831, 19], [1167, 6, 832, 4, "flex"], [1167, 10, 832, 8], [1167, 12, 832, 10], [1167, 13, 832, 11], [1168, 6, 833, 4, "backgroundColor"], [1168, 21, 833, 19], [1168, 23, 833, 21], [1168, 43, 833, 41], [1169, 6, 834, 4, "justifyContent"], [1169, 20, 834, 18], [1169, 22, 834, 20], [1169, 30, 834, 28], [1170, 6, 835, 4, "alignItems"], [1170, 16, 835, 14], [1170, 18, 835, 16], [1171, 4, 836, 2], [1171, 5, 836, 3], [1172, 4, 837, 2, "processingContent"], [1172, 21, 837, 19], [1172, 23, 837, 21], [1173, 6, 838, 4, "backgroundColor"], [1173, 21, 838, 19], [1173, 23, 838, 21], [1173, 29, 838, 27], [1174, 6, 839, 4, "borderRadius"], [1174, 18, 839, 16], [1174, 20, 839, 18], [1174, 22, 839, 20], [1175, 6, 840, 4, "padding"], [1175, 13, 840, 11], [1175, 15, 840, 13], [1175, 17, 840, 15], [1176, 6, 841, 4, "width"], [1176, 11, 841, 9], [1176, 13, 841, 11], [1176, 18, 841, 16], [1177, 6, 842, 4, "max<PERSON><PERSON><PERSON>"], [1177, 14, 842, 12], [1177, 16, 842, 14], [1177, 19, 842, 17], [1178, 6, 843, 4, "alignItems"], [1178, 16, 843, 14], [1178, 18, 843, 16], [1179, 4, 844, 2], [1179, 5, 844, 3], [1180, 4, 845, 2, "processingTitle"], [1180, 19, 845, 17], [1180, 21, 845, 19], [1181, 6, 846, 4, "fontSize"], [1181, 14, 846, 12], [1181, 16, 846, 14], [1181, 18, 846, 16], [1182, 6, 847, 4, "fontWeight"], [1182, 16, 847, 14], [1182, 18, 847, 16], [1182, 23, 847, 21], [1183, 6, 848, 4, "color"], [1183, 11, 848, 9], [1183, 13, 848, 11], [1183, 22, 848, 20], [1184, 6, 849, 4, "marginTop"], [1184, 15, 849, 13], [1184, 17, 849, 15], [1184, 19, 849, 17], [1185, 6, 850, 4, "marginBottom"], [1185, 18, 850, 16], [1185, 20, 850, 18], [1186, 4, 851, 2], [1186, 5, 851, 3], [1187, 4, 852, 2, "progressBar"], [1187, 15, 852, 13], [1187, 17, 852, 15], [1188, 6, 853, 4, "width"], [1188, 11, 853, 9], [1188, 13, 853, 11], [1188, 19, 853, 17], [1189, 6, 854, 4, "height"], [1189, 12, 854, 10], [1189, 14, 854, 12], [1189, 15, 854, 13], [1190, 6, 855, 4, "backgroundColor"], [1190, 21, 855, 19], [1190, 23, 855, 21], [1190, 32, 855, 30], [1191, 6, 856, 4, "borderRadius"], [1191, 18, 856, 16], [1191, 20, 856, 18], [1191, 21, 856, 19], [1192, 6, 857, 4, "overflow"], [1192, 14, 857, 12], [1192, 16, 857, 14], [1192, 24, 857, 22], [1193, 6, 858, 4, "marginBottom"], [1193, 18, 858, 16], [1193, 20, 858, 18], [1194, 4, 859, 2], [1194, 5, 859, 3], [1195, 4, 860, 2, "progressFill"], [1195, 16, 860, 14], [1195, 18, 860, 16], [1196, 6, 861, 4, "height"], [1196, 12, 861, 10], [1196, 14, 861, 12], [1196, 20, 861, 18], [1197, 6, 862, 4, "backgroundColor"], [1197, 21, 862, 19], [1197, 23, 862, 21], [1197, 32, 862, 30], [1198, 6, 863, 4, "borderRadius"], [1198, 18, 863, 16], [1198, 20, 863, 18], [1199, 4, 864, 2], [1199, 5, 864, 3], [1200, 4, 865, 2, "processingDescription"], [1200, 25, 865, 23], [1200, 27, 865, 25], [1201, 6, 866, 4, "fontSize"], [1201, 14, 866, 12], [1201, 16, 866, 14], [1201, 18, 866, 16], [1202, 6, 867, 4, "color"], [1202, 11, 867, 9], [1202, 13, 867, 11], [1202, 22, 867, 20], [1203, 6, 868, 4, "textAlign"], [1203, 15, 868, 13], [1203, 17, 868, 15], [1204, 4, 869, 2], [1204, 5, 869, 3], [1205, 4, 870, 2, "successIcon"], [1205, 15, 870, 13], [1205, 17, 870, 15], [1206, 6, 871, 4, "marginTop"], [1206, 15, 871, 13], [1206, 17, 871, 15], [1207, 4, 872, 2], [1207, 5, 872, 3], [1208, 4, 873, 2, "errorContent"], [1208, 16, 873, 14], [1208, 18, 873, 16], [1209, 6, 874, 4, "backgroundColor"], [1209, 21, 874, 19], [1209, 23, 874, 21], [1209, 29, 874, 27], [1210, 6, 875, 4, "borderRadius"], [1210, 18, 875, 16], [1210, 20, 875, 18], [1210, 22, 875, 20], [1211, 6, 876, 4, "padding"], [1211, 13, 876, 11], [1211, 15, 876, 13], [1211, 17, 876, 15], [1212, 6, 877, 4, "width"], [1212, 11, 877, 9], [1212, 13, 877, 11], [1212, 18, 877, 16], [1213, 6, 878, 4, "max<PERSON><PERSON><PERSON>"], [1213, 14, 878, 12], [1213, 16, 878, 14], [1213, 19, 878, 17], [1214, 6, 879, 4, "alignItems"], [1214, 16, 879, 14], [1214, 18, 879, 16], [1215, 4, 880, 2], [1215, 5, 880, 3], [1216, 4, 881, 2, "errorTitle"], [1216, 14, 881, 12], [1216, 16, 881, 14], [1217, 6, 882, 4, "fontSize"], [1217, 14, 882, 12], [1217, 16, 882, 14], [1217, 18, 882, 16], [1218, 6, 883, 4, "fontWeight"], [1218, 16, 883, 14], [1218, 18, 883, 16], [1218, 23, 883, 21], [1219, 6, 884, 4, "color"], [1219, 11, 884, 9], [1219, 13, 884, 11], [1219, 22, 884, 20], [1220, 6, 885, 4, "marginTop"], [1220, 15, 885, 13], [1220, 17, 885, 15], [1220, 19, 885, 17], [1221, 6, 886, 4, "marginBottom"], [1221, 18, 886, 16], [1221, 20, 886, 18], [1222, 4, 887, 2], [1222, 5, 887, 3], [1223, 4, 888, 2, "errorMessage"], [1223, 16, 888, 14], [1223, 18, 888, 16], [1224, 6, 889, 4, "fontSize"], [1224, 14, 889, 12], [1224, 16, 889, 14], [1224, 18, 889, 16], [1225, 6, 890, 4, "color"], [1225, 11, 890, 9], [1225, 13, 890, 11], [1225, 22, 890, 20], [1226, 6, 891, 4, "textAlign"], [1226, 15, 891, 13], [1226, 17, 891, 15], [1226, 25, 891, 23], [1227, 6, 892, 4, "marginBottom"], [1227, 18, 892, 16], [1227, 20, 892, 18], [1228, 4, 893, 2], [1228, 5, 893, 3], [1229, 4, 894, 2, "primaryButton"], [1229, 17, 894, 15], [1229, 19, 894, 17], [1230, 6, 895, 4, "backgroundColor"], [1230, 21, 895, 19], [1230, 23, 895, 21], [1230, 32, 895, 30], [1231, 6, 896, 4, "paddingHorizontal"], [1231, 23, 896, 21], [1231, 25, 896, 23], [1231, 27, 896, 25], [1232, 6, 897, 4, "paddingVertical"], [1232, 21, 897, 19], [1232, 23, 897, 21], [1232, 25, 897, 23], [1233, 6, 898, 4, "borderRadius"], [1233, 18, 898, 16], [1233, 20, 898, 18], [1233, 21, 898, 19], [1234, 6, 899, 4, "marginTop"], [1234, 15, 899, 13], [1234, 17, 899, 15], [1235, 4, 900, 2], [1235, 5, 900, 3], [1236, 4, 901, 2, "primaryButtonText"], [1236, 21, 901, 19], [1236, 23, 901, 21], [1237, 6, 902, 4, "color"], [1237, 11, 902, 9], [1237, 13, 902, 11], [1237, 19, 902, 17], [1238, 6, 903, 4, "fontSize"], [1238, 14, 903, 12], [1238, 16, 903, 14], [1238, 18, 903, 16], [1239, 6, 904, 4, "fontWeight"], [1239, 16, 904, 14], [1239, 18, 904, 16], [1240, 4, 905, 2], [1240, 5, 905, 3], [1241, 4, 906, 2, "secondaryButton"], [1241, 19, 906, 17], [1241, 21, 906, 19], [1242, 6, 907, 4, "paddingHorizontal"], [1242, 23, 907, 21], [1242, 25, 907, 23], [1242, 27, 907, 25], [1243, 6, 908, 4, "paddingVertical"], [1243, 21, 908, 19], [1243, 23, 908, 21], [1243, 25, 908, 23], [1244, 6, 909, 4, "marginTop"], [1244, 15, 909, 13], [1244, 17, 909, 15], [1245, 4, 910, 2], [1245, 5, 910, 3], [1246, 4, 911, 2, "secondaryButtonText"], [1246, 23, 911, 21], [1246, 25, 911, 23], [1247, 6, 912, 4, "color"], [1247, 11, 912, 9], [1247, 13, 912, 11], [1247, 22, 912, 20], [1248, 6, 913, 4, "fontSize"], [1248, 14, 913, 12], [1248, 16, 913, 14], [1249, 4, 914, 2], [1249, 5, 914, 3], [1250, 4, 915, 2, "permissionContent"], [1250, 21, 915, 19], [1250, 23, 915, 21], [1251, 6, 916, 4, "flex"], [1251, 10, 916, 8], [1251, 12, 916, 10], [1251, 13, 916, 11], [1252, 6, 917, 4, "justifyContent"], [1252, 20, 917, 18], [1252, 22, 917, 20], [1252, 30, 917, 28], [1253, 6, 918, 4, "alignItems"], [1253, 16, 918, 14], [1253, 18, 918, 16], [1253, 26, 918, 24], [1254, 6, 919, 4, "padding"], [1254, 13, 919, 11], [1254, 15, 919, 13], [1255, 4, 920, 2], [1255, 5, 920, 3], [1256, 4, 921, 2, "permissionTitle"], [1256, 19, 921, 17], [1256, 21, 921, 19], [1257, 6, 922, 4, "fontSize"], [1257, 14, 922, 12], [1257, 16, 922, 14], [1257, 18, 922, 16], [1258, 6, 923, 4, "fontWeight"], [1258, 16, 923, 14], [1258, 18, 923, 16], [1258, 23, 923, 21], [1259, 6, 924, 4, "color"], [1259, 11, 924, 9], [1259, 13, 924, 11], [1259, 22, 924, 20], [1260, 6, 925, 4, "marginTop"], [1260, 15, 925, 13], [1260, 17, 925, 15], [1260, 19, 925, 17], [1261, 6, 926, 4, "marginBottom"], [1261, 18, 926, 16], [1261, 20, 926, 18], [1262, 4, 927, 2], [1262, 5, 927, 3], [1263, 4, 928, 2, "permissionDescription"], [1263, 25, 928, 23], [1263, 27, 928, 25], [1264, 6, 929, 4, "fontSize"], [1264, 14, 929, 12], [1264, 16, 929, 14], [1264, 18, 929, 16], [1265, 6, 930, 4, "color"], [1265, 11, 930, 9], [1265, 13, 930, 11], [1265, 22, 930, 20], [1266, 6, 931, 4, "textAlign"], [1266, 15, 931, 13], [1266, 17, 931, 15], [1266, 25, 931, 23], [1267, 6, 932, 4, "marginBottom"], [1267, 18, 932, 16], [1267, 20, 932, 18], [1268, 4, 933, 2], [1268, 5, 933, 3], [1269, 4, 934, 2, "loadingText"], [1269, 15, 934, 13], [1269, 17, 934, 15], [1270, 6, 935, 4, "color"], [1270, 11, 935, 9], [1270, 13, 935, 11], [1270, 22, 935, 20], [1271, 6, 936, 4, "marginTop"], [1271, 15, 936, 13], [1271, 17, 936, 15], [1272, 4, 937, 2], [1272, 5, 937, 3], [1273, 4, 938, 2], [1274, 4, 939, 2, "blurZone"], [1274, 12, 939, 10], [1274, 14, 939, 12], [1275, 6, 940, 4, "position"], [1275, 14, 940, 12], [1275, 16, 940, 14], [1275, 26, 940, 24], [1276, 6, 941, 4, "overflow"], [1276, 14, 941, 12], [1276, 16, 941, 14], [1277, 4, 942, 2], [1277, 5, 942, 3], [1278, 4, 943, 2, "previewChip"], [1278, 15, 943, 13], [1278, 17, 943, 15], [1279, 6, 944, 4, "position"], [1279, 14, 944, 12], [1279, 16, 944, 14], [1279, 26, 944, 24], [1280, 6, 945, 4, "top"], [1280, 9, 945, 7], [1280, 11, 945, 9], [1280, 12, 945, 10], [1281, 6, 946, 4, "right"], [1281, 11, 946, 9], [1281, 13, 946, 11], [1281, 14, 946, 12], [1282, 6, 947, 4, "backgroundColor"], [1282, 21, 947, 19], [1282, 23, 947, 21], [1282, 40, 947, 38], [1283, 6, 948, 4, "paddingHorizontal"], [1283, 23, 948, 21], [1283, 25, 948, 23], [1283, 27, 948, 25], [1284, 6, 949, 4, "paddingVertical"], [1284, 21, 949, 19], [1284, 23, 949, 21], [1284, 24, 949, 22], [1285, 6, 950, 4, "borderRadius"], [1285, 18, 950, 16], [1285, 20, 950, 18], [1286, 4, 951, 2], [1286, 5, 951, 3], [1287, 4, 952, 2, "previewChipText"], [1287, 19, 952, 17], [1287, 21, 952, 19], [1288, 6, 953, 4, "color"], [1288, 11, 953, 9], [1288, 13, 953, 11], [1288, 19, 953, 17], [1289, 6, 954, 4, "fontSize"], [1289, 14, 954, 12], [1289, 16, 954, 14], [1289, 18, 954, 16], [1290, 6, 955, 4, "fontWeight"], [1290, 16, 955, 14], [1290, 18, 955, 16], [1291, 4, 956, 2], [1292, 2, 957, 0], [1292, 3, 957, 1], [1292, 4, 957, 2], [1293, 2, 957, 3], [1293, 6, 957, 3, "_c"], [1293, 8, 957, 3], [1294, 2, 957, 3, "$RefreshReg$"], [1294, 14, 957, 3], [1294, 15, 957, 3, "_c"], [1294, 17, 957, 3], [1295, 0, 957, 3], [1295, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHoC;mCKE;wBDa;OCI;oFCkC;UDM;8BES;SFoD;uDDU;sBIC,wBJ;OCC;GLe;6BSG;GT6B;kCUG;GV8C;4BWE;mBCmD;SDE;GXO;uBaE;GbI;mCcG;GdM;YCE;GDK;oBe2C;WfG;yBgBC;WhBG;wBiBC;WjBI;CD4L"}}, "type": "js/module"}]}