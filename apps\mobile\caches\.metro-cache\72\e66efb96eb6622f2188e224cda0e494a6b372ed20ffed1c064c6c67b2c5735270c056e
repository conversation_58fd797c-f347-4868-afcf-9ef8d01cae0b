{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.VIDEO_ASPECT_RATIOS = exports.PictureSizes = exports.MinimumConstraints = exports.ImageTypeFormat = exports.FacingModeToCameraType = exports.CameraTypeToFacingMode = void 0;\n  // https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints/aspectRatio\n  const VIDEO_ASPECT_RATIOS = exports.VIDEO_ASPECT_RATIOS = {\n    '3840x2160': 3840 / 2160,\n    '1920x1080': 1920 / 1080,\n    '1280x720': 1280 / 720,\n    '640x480': 640 / 480,\n    '352x288': 352 / 288\n  };\n  const PictureSizes = exports.PictureSizes = Object.keys(VIDEO_ASPECT_RATIOS);\n  const ImageTypeFormat = exports.ImageTypeFormat = {\n    jpg: 'image/jpeg',\n    png: 'image/png'\n  };\n  const MinimumConstraints = exports.MinimumConstraints = {\n    audio: false,\n    video: true\n  };\n  const CameraTypeToFacingMode = exports.CameraTypeToFacingMode = {\n    front: 'user',\n    back: 'environment'\n  };\n  const FacingModeToCameraType = exports.FacingModeToCameraType = {\n    user: 'front',\n    environment: 'back'\n  };\n});", "lineCount": 31, "map": [[6, 2, 1, 0], [7, 2, 2, 7], [7, 8, 2, 13, "VIDEO_ASPECT_RATIOS"], [7, 27, 2, 32], [7, 30, 2, 32, "exports"], [7, 37, 2, 32], [7, 38, 2, 32, "VIDEO_ASPECT_RATIOS"], [7, 57, 2, 32], [7, 60, 2, 35], [8, 4, 3, 4], [8, 15, 3, 15], [8, 17, 3, 17], [8, 21, 3, 21], [8, 24, 3, 24], [8, 28, 3, 28], [9, 4, 4, 4], [9, 15, 4, 15], [9, 17, 4, 17], [9, 21, 4, 21], [9, 24, 4, 24], [9, 28, 4, 28], [10, 4, 5, 4], [10, 14, 5, 14], [10, 16, 5, 16], [10, 20, 5, 20], [10, 23, 5, 23], [10, 26, 5, 26], [11, 4, 6, 4], [11, 13, 6, 13], [11, 15, 6, 15], [11, 18, 6, 18], [11, 21, 6, 21], [11, 24, 6, 24], [12, 4, 7, 4], [12, 13, 7, 13], [12, 15, 7, 15], [12, 18, 7, 18], [12, 21, 7, 21], [13, 2, 8, 0], [13, 3, 8, 1], [14, 2, 9, 7], [14, 8, 9, 13, "PictureSizes"], [14, 20, 9, 25], [14, 23, 9, 25, "exports"], [14, 30, 9, 25], [14, 31, 9, 25, "PictureSizes"], [14, 43, 9, 25], [14, 46, 9, 28, "Object"], [14, 52, 9, 34], [14, 53, 9, 35, "keys"], [14, 57, 9, 39], [14, 58, 9, 40, "VIDEO_ASPECT_RATIOS"], [14, 77, 9, 59], [14, 78, 9, 60], [15, 2, 10, 7], [15, 8, 10, 13, "ImageTypeFormat"], [15, 23, 10, 28], [15, 26, 10, 28, "exports"], [15, 33, 10, 28], [15, 34, 10, 28, "ImageTypeFormat"], [15, 49, 10, 28], [15, 52, 10, 31], [16, 4, 11, 4, "jpg"], [16, 7, 11, 7], [16, 9, 11, 9], [16, 21, 11, 21], [17, 4, 12, 4, "png"], [17, 7, 12, 7], [17, 9, 12, 9], [18, 2, 13, 0], [18, 3, 13, 1], [19, 2, 14, 7], [19, 8, 14, 13, "MinimumConstraints"], [19, 26, 14, 31], [19, 29, 14, 31, "exports"], [19, 36, 14, 31], [19, 37, 14, 31, "MinimumConstraints"], [19, 55, 14, 31], [19, 58, 14, 34], [20, 4, 15, 4, "audio"], [20, 9, 15, 9], [20, 11, 15, 11], [20, 16, 15, 16], [21, 4, 16, 4, "video"], [21, 9, 16, 9], [21, 11, 16, 11], [22, 2, 17, 0], [22, 3, 17, 1], [23, 2, 18, 7], [23, 8, 18, 13, "CameraTypeToFacingMode"], [23, 30, 18, 35], [23, 33, 18, 35, "exports"], [23, 40, 18, 35], [23, 41, 18, 35, "CameraTypeToFacingMode"], [23, 63, 18, 35], [23, 66, 18, 38], [24, 4, 19, 4, "front"], [24, 9, 19, 9], [24, 11, 19, 11], [24, 17, 19, 17], [25, 4, 20, 4, "back"], [25, 8, 20, 8], [25, 10, 20, 10], [26, 2, 21, 0], [26, 3, 21, 1], [27, 2, 22, 7], [27, 8, 22, 13, "FacingModeToCameraType"], [27, 30, 22, 35], [27, 33, 22, 35, "exports"], [27, 40, 22, 35], [27, 41, 22, 35, "FacingModeToCameraType"], [27, 63, 22, 35], [27, 66, 22, 38], [28, 4, 23, 4, "user"], [28, 8, 23, 8], [28, 10, 23, 10], [28, 17, 23, 17], [29, 4, 24, 4, "environment"], [29, 15, 24, 15], [29, 17, 24, 17], [30, 2, 25, 0], [30, 3, 25, 1], [31, 0, 25, 2], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}