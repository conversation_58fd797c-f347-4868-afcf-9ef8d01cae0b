<!doctype html>
      <title>RollUp Visualizer</title>
      <meta charset="utf-8">
      <style>html {
  background-color: #f7eedf;
  color: #333;
}

body {
  font-family: sans-serif;
  margin: 10px auto 0;
  width: 700px;
  padding: 0 10px;
}

a {
  color: #347ab7;
}

p {
  margin-top: 0.5em;
}

svg {
  vertical-align: middle;
}

h1 {
  font-family: "Oswald", "HelveticaNeue-CondensedBold", "Arial Narrow", sans-serif;
  font-weight: 500;
  font-size: 70px;
  text-transform: uppercase;
  text-align: center;
}

hr {
  border: 0 none;
  border-top: 1px solid #aaa;
}

.breadcrumbs {
  height: 1em;
  margin: 1em 0;
}

.chart {
  position: relative;
  margin: 0 auto;
  min-height: 350px;
}
.chart--large {
  width: 950px;
  margin-left: -100px;
}

.chart path {
  stroke: #fff;
}

.details {
  position: absolute;
  top: 350px;
  left: 50%;
  width: 170px;
  margin-left: -85px;
  font-size: 14px;
  text-align: center;
  color: #eee;
  z-index: 100;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart--large .details {
  top: 425px;
}

.details-size {
  font-size: 0.8em;
  margin-top: 1em;
}

.details-name {
  font-weight: bold;
}

.details-size::before {
  content: "(";
}
.details-size::after {
  content: ")";
}

.details-percentage {
  margin: 0.4em 0 0em;
  font-size: 2.4em;
  line-height: 1em;
}

footer {
  margin-top: 4em;
}

footer h2 {
  margin: 1.5em 0 0.5em;
  font-size: 1.3em;
}


    @font-face {
      font-family: 'Oswald';
      font-display: swap;
      font-style: normal;
      font-weight: 500;
      src:
        url(data:font/woff2;charset=utf-8;base64,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) format('woff2'),
        url(data:application/font-woff;charset=utf-8;base64,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) format('woff');
    }</style>
      <div>
      <div>
          <h1>RollUp Visualizer</h1>

          <div id="charts">
          </div>
      </div>
      </div>
      <script>window.nodesData = [{"id":"tf-core.min.js","root":{"name":"root","children":[{"name":"\u0000tslib","children":[],"size":0,"originalSize":9047},{"name":"src","children":[{"name":"environment.ts","children":[],"size":2252,"originalSize":5487},{"name":"kernel_registry.ts","children":[],"size":862,"originalSize":5786},{"name":"util.ts","children":[],"size":7465,"originalSize":21049},{"name":"profiler.ts","children":[],"size":1034,"originalSize":3667},{"name":"tape.ts","children":[],"size":1577,"originalSize":6376},{"name":"tensor_format.ts","children":[],"size":1846,"originalSize":6638},{"name":"tensor.ts","children":[],"size":18765,"originalSize":52351},{"name":"types.ts","children":[],"size":797,"originalSize":4997},{"name":"tensor_util.ts","children":[],"size":581,"originalSize":2870},{"name":"engine.ts","children":[],"size":16848,"originalSize":37724},{"name":"device_util.ts","children":[],"size":2124,"originalSize":3135},{"name":"flags.ts","children":[],"size":882,"originalSize":2412},{"name":"backends","children":[{"name":"webgl","children":[{"name":"canvas_util.ts","children":[],"size":1027,"originalSize":2754},{"name":"tex_util.ts","children":[],"size":1193,"originalSize":7161},{"name":"webgl_util.ts","children":[],"size":10384,"originalSize":23945},{"name":"flags_webgl.ts","children":[],"size":2371,"originalSize":5651},{"name":"addn_gpu.ts","children":[],"size":436,"originalSize":1660},{"name":"addn_packed_gpu.ts","children":[],"size":469,"originalSize":1710},{"name":"argminmax_gpu.ts","children":[],"size":856,"originalSize":2079},{"name":"glsl_version.ts","children":[],"size":1671,"originalSize":3714},{"name":"shader_compiler_util.ts","children":[],"size":1365,"originalSize":3999},{"name":"shader_compiler.ts","children":[],"size":24470,"originalSize":43229},{"name":"argminmax_packed_gpu.ts","children":[],"size":3327,"originalSize":5773},{"name":"avg_pool_backprop_gpu.ts","children":[],"size":3659,"originalSize":5980},{"name":"batchnorm_gpu.ts","children":[],"size":689,"originalSize":2211},{"name":"batchnorm_packed_gpu.ts","children":[],"size":698,"originalSize":2245},{"name":"binaryop_complex_gpu.ts","children":[],"size":607,"originalSize":1802},{"name":"binaryop_gpu.ts","children":[],"size":1438,"originalSize":3293},{"name":"binaryop_packed_gpu.ts","children":[],"size":4937,"originalSize":6408},{"name":"clip_gpu.ts","children":[],"size":628,"originalSize":1797},{"name":"clip_packed_gpu.ts","children":[],"size":677,"originalSize":1866},{"name":"complex_abs_gpu.ts","children":[],"size":537,"originalSize":1417},{"name":"concat_gpu.ts","children":[],"size":636,"originalSize":2037},{"name":"concat_packed_gpu.ts","children":[],"size":1649,"originalSize":4382},{"name":"conv_backprop_gpu.ts","children":[],"size":6750,"originalSize":9340},{"name":"conv_backprop_gpu_depthwise.ts","children":[],"size":2731,"originalSize":4328},{"name":"conv_gpu.ts","children":[],"size":8047,"originalSize":11300},{"name":"conv_gpu_depthwise.ts","children":[],"size":2041,"originalSize":3769},{"name":"conv_packed_gpu_depthwise.ts","children":[],"size":6611,"originalSize":11827},{"name":"crop_and_resize_gpu.ts","children":[],"size":2665,"originalSize":4542},{"name":"cumsum_gpu.ts","children":[],"size":1098,"originalSize":2585},{"name":"decode_matrix_gpu.ts","children":[],"size":743,"originalSize":2006},{"name":"decode_matrix_packed_gpu.ts","children":[],"size":777,"originalSize":2041},{"name":"depth_to_space_gpu.ts","children":[],"size":1281,"originalSize":2578},{"name":"diag_gpu.ts","children":[],"size":260,"originalSize":1140},{"name":"encode_float_gpu.ts","children":[],"size":248,"originalSize":1356},{"name":"encode_float_packed_gpu.ts","children":[],"size":371,"originalSize":1505},{"name":"encode_matrix_gpu.ts","children":[],"size":939,"originalSize":2154},{"name":"encode_matrix_packed_gpu.ts","children":[],"size":1507,"originalSize":3436},{"name":"fft_gpu.ts","children":[],"size":1214,"originalSize":2321},{"name":"fill_gpu.ts","children":[],"size":433,"originalSize":1525},{"name":"gather_gpu.ts","children":[],"size":553,"originalSize":2017},{"name":"gather_nd_gpu.ts","children":[],"size":606,"originalSize":1684},{"name":"gpgpu_util.ts","children":[],"size":4087,"originalSize":11270},{"name":"gpgpu_context.ts","children":[],"size":11365,"originalSize":22682},{"name":"gpgpu_math.ts","children":[],"size":2590,"originalSize":8129},{"name":"im2col_packed_gpu.ts","children":[],"size":1720,"originalSize":3297},{"name":"lrn_gpu.ts","children":[],"size":777,"originalSize":2127},{"name":"lrn_grad_gpu.ts","children":[],"size":1872,"originalSize":2952},{"name":"lrn_packed_gpu.ts","children":[],"size":2562,"originalSize":3877},{"name":"max_pool_backprop_gpu.ts","children":[],"size":4142,"originalSize":6366},{"name":"mulmat_packed_gpu.ts","children":[],"size":1517,"originalSize":3128},{"name":"multinomial_gpu.ts","children":[],"size":763,"originalSize":1923},{"name":"onehot_gpu.ts","children":[],"size":324,"originalSize":1356},{"name":"pack_gpu.ts","children":[],"size":1351,"originalSize":3634},{"name":"pad_gpu.ts","children":[],"size":992,"originalSize":2233},{"name":"pad_packed_gpu.ts","children":[],"size":1255,"originalSize":2890},{"name":"pool_gpu.ts","children":[],"size":10280,"originalSize":14109},{"name":"reduce_gpu.ts","children":[],"size":3066,"originalSize":5404},{"name":"reshape_packed_gpu.ts","children":[],"size":1049,"originalSize":2502},{"name":"resize_bilinear_backprop_gpu.ts","children":[],"size":3084,"originalSize":4968},{"name":"resize_bilinear_gpu.ts","children":[],"size":1455,"originalSize":2832},{"name":"resize_bilinear_packed_gpu.ts","children":[],"size":3398,"originalSize":4786},{"name":"resize_nearest_neighbor_backprop_gpu.ts","children":[],"size":2579,"originalSize":4554},{"name":"resize_nearest_neighbor_gpu.ts","children":[],"size":965,"originalSize":2471},{"name":"reverse_gpu.ts","children":[],"size":612,"originalSize":1816},{"name":"reverse_packed_gpu.ts","children":[],"size":1559,"originalSize":3648},{"name":"scatter_gpu.ts","children":[],"size":951,"originalSize":2484},{"name":"segment_gpu.ts","children":[],"size":3280,"originalSize":4950},{"name":"select_gpu.ts","children":[],"size":598,"originalSize":1967},{"name":"slice_gpu.ts","children":[],"size":1007,"originalSize":2708},{"name":"slice_packed_gpu.ts","children":[],"size":1618,"originalSize":3462},{"name":"strided_slice_gpu.ts","children":[],"size":532,"originalSize":1872},{"name":"texture_manager.ts","children":[],"size":2466,"originalSize":5941},{"name":"tile_gpu.ts","children":[],"size":569,"originalSize":1959},{"name":"transpose_gpu.ts","children":[],"size":523,"originalSize":1909},{"name":"transpose_packed_gpu.ts","children":[],"size":911,"originalSize":2412},{"name":"unaryop_gpu.ts","children":[],"size":3252,"originalSize":5851},{"name":"unaryop_packed_gpu.ts","children":[],"size":1446,"originalSize":2418},{"name":"unpack_gpu.ts","children":[],"size":389,"originalSize":1618},{"name":"backend_webgl.ts","children":[],"size":42623,"originalSize":103616},{"name":"kernels","children":[{"name":"FromPixels_utils","children":[{"name":"from_pixels_gpu.ts","children":[],"size":729,"originalSize":1732},{"name":"from_pixels_packed_gpu.ts","children":[],"size":1107,"originalSize":2118}]},{"name":"FromPixels.ts","children":[],"size":752,"originalSize":3104},{"name":"NonMaxSuppressionV5.ts","children":[],"size":410,"originalSize":2159},{"name":"Square.ts","children":[],"size":151,"originalSize":1278},{"name":"SquaredDifference.ts","children":[],"size":290,"originalSize":1638}]},{"name":"register_all_kernels.ts","children":[],"size":37,"originalSize":1263}]},{"name":"backend.ts","children":[],"size":9513,"originalSize":19615},{"name":"backend_util.ts","children":[],"size":1306,"originalSize":3006},{"name":"complex_util.ts","children":[],"size":1086,"originalSize":4918},{"name":"array_util.ts","children":[],"size":258,"originalSize":3602},{"name":"non_max_suppression_impl.ts","children":[],"size":1465,"originalSize":7620},{"name":"split_shared.ts","children":[],"size":141,"originalSize":1118},{"name":"tile_impl.ts","children":[],"size":288,"originalSize":1648},{"name":"topk_impl.ts","children":[],"size":419,"originalSize":2345},{"name":"where_impl.ts","children":[],"size":210,"originalSize":1426},{"name":"packing_util.ts","children":[],"size":220,"originalSize":1274},{"name":"cpu","children":[{"name":"cpu_util.ts","children":[],"size":156,"originalSize":1118},{"name":"backend_cpu.ts","children":[],"size":49864,"originalSize":137800},{"name":"kernels","children":[{"name":"NonMaxSuppressionV5.ts","children":[],"size":348,"originalSize":2106},{"name":"Square.ts","children":[],"size":284,"originalSize":1539},{"name":"SquaredDifference.ts","children":[],"size":275,"originalSize":1821}]},{"name":"utils","children":[{"name":"kernel_utils.ts","children":[],"size":507,"originalSize":2402}]},{"name":"register_all_kernels.ts","children":[],"size":37,"originalSize":1414}]}]},{"name":"globals.ts","children":[],"size":3363,"originalSize":12640},{"name":"log.ts","children":[],"size":126,"originalSize":969},{"name":"tensor_util_env.ts","children":[],"size":1737,"originalSize":4806},{"name":"ops","children":[{"name":"axis_util.ts","children":[],"size":881,"originalSize":3283},{"name":"concat_util.ts","children":[],"size":616,"originalSize":1841},{"name":"operation.ts","children":[],"size":578,"originalSize":1964},{"name":"complex_ops.ts","children":[],"size":521,"originalSize":3413},{"name":"tensor_ops.ts","children":[],"size":4670,"originalSize":20232},{"name":"concat_split.ts","children":[],"size":1691,"originalSize":8449},{"name":"rand.ts","children":[],"size":2123,"originalSize":6074},{"name":"array_ops.ts","children":[],"size":11354,"originalSize":42835},{"name":"array_ops_util.ts","children":[],"size":836,"originalSize":4941},{"name":"gather_nd_util.ts","children":[],"size":902,"originalSize":2726},{"name":"reduce_util.ts","children":[],"size":75,"originalSize":1186},{"name":"scatter_nd_util.ts","children":[],"size":1751,"originalSize":5117},{"name":"segment_util.ts","children":[],"size":428,"originalSize":2444},{"name":"slice_util.ts","children":[],"size":1394,"originalSize":5066},{"name":"softmax.ts","children":[],"size":920,"originalSize":3952},{"name":"broadcast_util.ts","children":[],"size":586,"originalSize":2633},{"name":"conv_util.ts","children":[],"size":5095,"originalSize":18535},{"name":"erf_util.ts","children":[],"size":71,"originalSize":913},{"name":"selu_util.ts","children":[],"size":47,"originalSize":831},{"name":"square.ts","children":[],"size":157,"originalSize":1568},{"name":"squared_difference.ts","children":[],"size":428,"originalSize":3033},{"name":"unary_ops.ts","children":[],"size":7687,"originalSize":26025},{"name":"batchnorm.ts","children":[],"size":5091,"originalSize":15817},{"name":"logical_ops.ts","children":[],"size":1579,"originalSize":7417},{"name":"binary_ops.ts","children":[],"size":6960,"originalSize":27427},{"name":"compare.ts","children":[],"size":2423,"originalSize":9658},{"name":"segment_ops.ts","children":[],"size":1324,"originalSize":6538},{"name":"boolean_mask.ts","children":[],"size":652,"originalSize":2982},{"name":"conv.ts","children":[],"size":12006,"originalSize":40809},{"name":"matmul.ts","children":[],"size":2448,"originalSize":6975},{"name":"reverse.ts","children":[],"size":943,"originalSize":4189},{"name":"pool.ts","children":[],"size":8762,"originalSize":37580},{"name":"slice.ts","children":[],"size":1667,"originalSize":6402},{"name":"reduction_ops.ts","children":[],"size":4043,"originalSize":20794},{"name":"relu_ops.ts","children":[],"size":1578,"originalSize":6561},{"name":"transpose.ts","children":[],"size":586,"originalSize":2560},{"name":"lrn.ts","children":[],"size":831,"originalSize":2950},{"name":"norm.ts","children":[],"size":968,"originalSize":4499},{"name":"lstm.ts","children":[],"size":821,"originalSize":4284},{"name":"moving_average.ts","children":[],"size":440,"originalSize":2936},{"name":"strided_slice.ts","children":[],"size":731,"originalSize":4303},{"name":"topk.ts","children":[],"size":408,"originalSize":2489},{"name":"scatter_nd.ts","children":[],"size":238,"originalSize":2316},{"name":"spectral_ops.ts","children":[],"size":1648,"originalSize":7032},{"name":"sparse_to_dense_util.ts","children":[],"size":641,"originalSize":2495},{"name":"sparse_to_dense.ts","children":[],"size":345,"originalSize":3345},{"name":"gather_nd.ts","children":[],"size":197,"originalSize":2777},{"name":"diag.ts","children":[],"size":166,"originalSize":1657},{"name":"dropout_util.ts","children":[],"size":211,"originalSize":1582},{"name":"dropout.ts","children":[],"size":438,"originalSize":2593},{"name":"signal_ops.ts","children":[],"size":837,"originalSize":4957},{"name":"in_top_k.ts","children":[],"size":1181,"originalSize":3800},{"name":"loss_ops.ts","children":[],"size":4641,"originalSize":19925},{"name":"linalg_ops.ts","children":[],"size":3244,"originalSize":12191},{"name":"image_ops.ts","children":[],"size":5526,"originalSize":17107},{"name":"fused_util.ts","children":[],"size":45,"originalSize":1453},{"name":"fused_ops.ts","children":[],"size":9459,"originalSize":23490},{"name":"ops.ts","children":[],"size":0,"originalSize":2288},{"name":"confusion_matrix.ts","children":[],"size":867,"originalSize":3726},{"name":"browser.ts","children":[],"size":3149,"originalSize":8914}]},{"name":"gradients.ts","children":[],"size":4174,"originalSize":13183},{"name":"kernel_names.ts","children":[],"size":111,"originalSize":1745},{"name":"gradients","children":[{"name":"Square_grad.ts","children":[],"size":112,"originalSize":1021},{"name":"SquaredDifference_grad.ts","children":[],"size":157,"originalSize":1257}]},{"name":"register_all_gradients.ts","children":[],"size":37,"originalSize":1190},{"name":"platforms","children":[{"name":"platform_browser.ts","children":[],"size":498,"originalSize":1720},{"name":"platform_node.ts","children":[],"size":665,"originalSize":2750}]},{"name":"io","children":[{"name":"types.ts","children":[],"size":51,"originalSize":13512},{"name":"io_utils.ts","children":[],"size":3863,"originalSize":11866},{"name":"router_registry.ts","children":[],"size":945,"originalSize":3776},{"name":"model_management.ts","children":[],"size":2785,"originalSize":11924},{"name":"indexed_db.ts","children":[],"size":4343,"originalSize":13852},{"name":"local_storage.ts","children":[],"size":4506,"originalSize":12429},{"name":"browser_files.ts","children":[],"size":4164,"originalSize":13429},{"name":"progress.ts","children":[],"size":636,"originalSize":2750},{"name":"weights_loader.ts","children":[],"size":2227,"originalSize":9073},{"name":"http.ts","children":[],"size":4392,"originalSize":13043},{"name":"passthrough.ts","children":[],"size":1132,"originalSize":4272},{"name":"io.ts","children":[],"size":0,"originalSize":2227}]},{"name":"math.ts","children":[],"size":0,"originalSize":838},{"name":"serialization.ts","children":[],"size":834,"originalSize":5928},{"name":"test_util.ts","children":[],"size":1834,"originalSize":5170},{"name":"version.ts","children":[],"size":124,"originalSize":139},{"name":"webgl.ts","children":[],"size":112,"originalSize":1424},{"name":"optimizers","children":[{"name":"optimizer.ts","children":[],"size":1739,"originalSize":5765},{"name":"adadelta_optimizer.ts","children":[],"size":2278,"originalSize":5123},{"name":"adagrad_optimizer.ts","children":[],"size":1740,"originalSize":3763},{"name":"adam_optimizer.ts","children":[],"size":2772,"originalSize":6114},{"name":"adamax_optimizer.ts","children":[],"size":2215,"originalSize":4943},{"name":"sgd_optimizer.ts","children":[],"size":1223,"originalSize":2911},{"name":"momentum_optimizer.ts","children":[],"size":1760,"originalSize":4083},{"name":"rmsprop_optimizer.ts","children":[],"size":3337,"originalSize":7566},{"name":"optimizer_constructors.ts","children":[],"size":741,"originalSize":7331}]},{"name":"train.ts","children":[],"size":126,"originalSize":1732},{"name":"browser_util.ts","children":[],"size":464,"originalSize":1416},{"name":"public","children":[{"name":"chained_ops","children":[{"name":"squared_difference.ts","children":[],"size":62,"originalSize":1197},{"name":"register_all_chained_ops.ts","children":[],"size":0,"originalSize":735}]}]},{"name":"index.ts","children":[],"size":470,"originalSize":4215}]},{"name":"node_modules","children":[{"name":"seedrandom","children":[{"name":"lib","children":[{"name":"alea.js","children":[],"size":0,"originalSize":3241},{"name":"xor128.js","children":[],"size":0,"originalSize":1748},{"name":"xorwow.js","children":[],"size":0,"originalSize":1919},{"name":"xorshift7.js","children":[],"size":0,"originalSize":2417},{"name":"xor4096.js","children":[],"size":0,"originalSize":4559},{"name":"tychei.js","children":[],"size":0,"originalSize":2525}]},{"name":"seedrandom.js","children":[],"size":0,"originalSize":8358},{"name":"index.js","children":[],"size":106,"originalSize":2108}]}]},{"name":"\u0000commonjsHelpers.js","children":[],"size":0,"originalSize":686},{"name":"\u0000commonjs-proxy-/Users/<USER>/Documents/projects/tfjs/tfjs-core/node_modules/seedrandom/lib/alea.js","children":[],"size":0,"originalSize":151},{"name":"\u0000commonjs-proxy-/Users/<USER>/Documents/projects/tfjs/tfjs-core/node_modules/seedrandom/lib/xor128.js","children":[],"size":0,"originalSize":153},{"name":"\u0000commonjs-proxy-/Users/<USER>/Documents/projects/tfjs/tfjs-core/node_modules/seedrandom/lib/xorwow.js","children":[],"size":0,"originalSize":153},{"name":"\u0000commonjs-proxy-/Users/<USER>/Documents/projects/tfjs/tfjs-core/node_modules/seedrandom/lib/xorshift7.js","children":[],"size":0,"originalSize":156},{"name":"\u0000commonjs-proxy-/Users/<USER>/Documents/projects/tfjs/tfjs-core/node_modules/seedrandom/lib/xor4096.js","children":[],"size":0,"originalSize":154},{"name":"\u0000commonjs-proxy-/Users/<USER>/Documents/projects/tfjs/tfjs-core/node_modules/seedrandom/lib/tychei.js","children":[],"size":0,"originalSize":153},{"name":"\u0000commonjs-proxy-/Users/<USER>/Documents/projects/tfjs/tfjs-core/node_modules/seedrandom/seedrandom.js","children":[],"size":0,"originalSize":153}]}}];</script>
      <script charset="UTF-8">
        (function () {
  'use strict';

  var xhtml = "http://www.w3.org/1999/xhtml";

  var namespaces = {
    svg: "http://www.w3.org/2000/svg",
    xhtml: xhtml,
    xlink: "http://www.w3.org/1999/xlink",
    xml: "http://www.w3.org/XML/1998/namespace",
    xmlns: "http://www.w3.org/2000/xmlns/"
  };

  function namespace(name) {
    var prefix = name += "", i = prefix.indexOf(":");
    if (i >= 0 && (prefix = name.slice(0, i)) !== "xmlns") name = name.slice(i + 1);
    return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name;
  }

  function creatorInherit(name) {
    return function() {
      var document = this.ownerDocument,
          uri = this.namespaceURI;
      return uri === xhtml && document.documentElement.namespaceURI === xhtml
          ? document.createElement(name)
          : document.createElementNS(uri, name);
    };
  }

  function creatorFixed(fullname) {
    return function() {
      return this.ownerDocument.createElementNS(fullname.space, fullname.local);
    };
  }

  function creator(name) {
    var fullname = namespace(name);
    return (fullname.local
        ? creatorFixed
        : creatorInherit)(fullname);
  }

  function none() {}

  function selector(selector) {
    return selector == null ? none : function() {
      return this.querySelector(selector);
    };
  }

  function selection_select(select) {
    if (typeof select !== "function") select = selector(select);

    for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {
      for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {
        if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {
          if ("__data__" in node) subnode.__data__ = node.__data__;
          subgroup[i] = subnode;
        }
      }
    }

    return new Selection(subgroups, this._parents);
  }

  function empty() {
    return [];
  }

  function selectorAll(selector) {
    return selector == null ? empty : function() {
      return this.querySelectorAll(selector);
    };
  }

  function selection_selectAll(select) {
    if (typeof select !== "function") select = selectorAll(select);

    for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {
      for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {
        if (node = group[i]) {
          subgroups.push(select.call(node, node.__data__, i, group));
          parents.push(node);
        }
      }
    }

    return new Selection(subgroups, parents);
  }

  function matcher(selector) {
    return function() {
      return this.matches(selector);
    };
  }

  function selection_filter(match) {
    if (typeof match !== "function") match = matcher(match);

    for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {
      for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {
        if ((node = group[i]) && match.call(node, node.__data__, i, group)) {
          subgroup.push(node);
        }
      }
    }

    return new Selection(subgroups, this._parents);
  }

  function sparse(update) {
    return new Array(update.length);
  }

  function selection_enter() {
    return new Selection(this._enter || this._groups.map(sparse), this._parents);
  }

  function EnterNode(parent, datum) {
    this.ownerDocument = parent.ownerDocument;
    this.namespaceURI = parent.namespaceURI;
    this._next = null;
    this._parent = parent;
    this.__data__ = datum;
  }

  EnterNode.prototype = {
    constructor: EnterNode,
    appendChild: function(child) { return this._parent.insertBefore(child, this._next); },
    insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },
    querySelector: function(selector) { return this._parent.querySelector(selector); },
    querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }
  };

  function constant(x) {
    return function() {
      return x;
    };
  }

  var keyPrefix = "$"; // Protect against keys like “__proto__”.

  function bindIndex(parent, group, enter, update, exit, data) {
    var i = 0,
        node,
        groupLength = group.length,
        dataLength = data.length;

    // Put any non-null nodes that fit into update.
    // Put any null nodes into enter.
    // Put any remaining data into enter.
    for (; i < dataLength; ++i) {
      if (node = group[i]) {
        node.__data__ = data[i];
        update[i] = node;
      } else {
        enter[i] = new EnterNode(parent, data[i]);
      }
    }

    // Put any non-null nodes that don’t fit into exit.
    for (; i < groupLength; ++i) {
      if (node = group[i]) {
        exit[i] = node;
      }
    }
  }

  function bindKey(parent, group, enter, update, exit, data, key) {
    var i,
        node,
        nodeByKeyValue = {},
        groupLength = group.length,
        dataLength = data.length,
        keyValues = new Array(groupLength),
        keyValue;

    // Compute the key for each node.
    // If multiple nodes have the same key, the duplicates are added to exit.
    for (i = 0; i < groupLength; ++i) {
      if (node = group[i]) {
        keyValues[i] = keyValue = keyPrefix + key.call(node, node.__data__, i, group);
        if (keyValue in nodeByKeyValue) {
          exit[i] = node;
        } else {
          nodeByKeyValue[keyValue] = node;
        }
      }
    }

    // Compute the key for each datum.
    // If there a node associated with this key, join and add it to update.
    // If there is not (or the key is a duplicate), add it to enter.
    for (i = 0; i < dataLength; ++i) {
      keyValue = keyPrefix + key.call(parent, data[i], i, data);
      if (node = nodeByKeyValue[keyValue]) {
        update[i] = node;
        node.__data__ = data[i];
        nodeByKeyValue[keyValue] = null;
      } else {
        enter[i] = new EnterNode(parent, data[i]);
      }
    }

    // Add any remaining nodes that were not bound to data to exit.
    for (i = 0; i < groupLength; ++i) {
      if ((node = group[i]) && (nodeByKeyValue[keyValues[i]] === node)) {
        exit[i] = node;
      }
    }
  }

  function selection_data(value, key) {
    if (!value) {
      data = new Array(this.size()), j = -1;
      this.each(function(d) { data[++j] = d; });
      return data;
    }

    var bind = key ? bindKey : bindIndex,
        parents = this._parents,
        groups = this._groups;

    if (typeof value !== "function") value = constant(value);

    for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {
      var parent = parents[j],
          group = groups[j],
          groupLength = group.length,
          data = value.call(parent, parent && parent.__data__, j, parents),
          dataLength = data.length,
          enterGroup = enter[j] = new Array(dataLength),
          updateGroup = update[j] = new Array(dataLength),
          exitGroup = exit[j] = new Array(groupLength);

      bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);

      // Now connect the enter nodes to their following update node, such that
      // appendChild can insert the materialized enter node before this node,
      // rather than at the end of the parent node.
      for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {
        if (previous = enterGroup[i0]) {
          if (i0 >= i1) i1 = i0 + 1;
          while (!(next = updateGroup[i1]) && ++i1 < dataLength);
          previous._next = next || null;
        }
      }
    }

    update = new Selection(update, parents);
    update._enter = enter;
    update._exit = exit;
    return update;
  }

  function selection_exit() {
    return new Selection(this._exit || this._groups.map(sparse), this._parents);
  }

  function selection_join(onenter, onupdate, onexit) {
    var enter = this.enter(), update = this, exit = this.exit();
    enter = typeof onenter === "function" ? onenter(enter) : enter.append(onenter + "");
    if (onupdate != null) update = onupdate(update);
    if (onexit == null) exit.remove(); else onexit(exit);
    return enter && update ? enter.merge(update).order() : update;
  }

  function selection_merge(selection) {

    for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {
      for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {
        if (node = group0[i] || group1[i]) {
          merge[i] = node;
        }
      }
    }

    for (; j < m0; ++j) {
      merges[j] = groups0[j];
    }

    return new Selection(merges, this._parents);
  }

  function selection_order() {

    for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {
      for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {
        if (node = group[i]) {
          if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);
          next = node;
        }
      }
    }

    return this;
  }

  function selection_sort(compare) {
    if (!compare) compare = ascending;

    function compareNode(a, b) {
      return a && b ? compare(a.__data__, b.__data__) : !a - !b;
    }

    for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {
      for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {
        if (node = group[i]) {
          sortgroup[i] = node;
        }
      }
      sortgroup.sort(compareNode);
    }

    return new Selection(sortgroups, this._parents).order();
  }

  function ascending(a, b) {
    return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
  }

  function selection_call() {
    var callback = arguments[0];
    arguments[0] = this;
    callback.apply(null, arguments);
    return this;
  }

  function selection_nodes() {
    var nodes = new Array(this.size()), i = -1;
    this.each(function() { nodes[++i] = this; });
    return nodes;
  }

  function selection_node() {

    for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {
      for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {
        var node = group[i];
        if (node) return node;
      }
    }

    return null;
  }

  function selection_size() {
    var size = 0;
    this.each(function() { ++size; });
    return size;
  }

  function selection_empty() {
    return !this.node();
  }

  function selection_each(callback) {

    for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {
      for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {
        if (node = group[i]) callback.call(node, node.__data__, i, group);
      }
    }

    return this;
  }

  function attrRemove(name) {
    return function() {
      this.removeAttribute(name);
    };
  }

  function attrRemoveNS(fullname) {
    return function() {
      this.removeAttributeNS(fullname.space, fullname.local);
    };
  }

  function attrConstant(name, value) {
    return function() {
      this.setAttribute(name, value);
    };
  }

  function attrConstantNS(fullname, value) {
    return function() {
      this.setAttributeNS(fullname.space, fullname.local, value);
    };
  }

  function attrFunction(name, value) {
    return function() {
      var v = value.apply(this, arguments);
      if (v == null) this.removeAttribute(name);
      else this.setAttribute(name, v);
    };
  }

  function attrFunctionNS(fullname, value) {
    return function() {
      var v = value.apply(this, arguments);
      if (v == null) this.removeAttributeNS(fullname.space, fullname.local);
      else this.setAttributeNS(fullname.space, fullname.local, v);
    };
  }

  function selection_attr(name, value) {
    var fullname = namespace(name);

    if (arguments.length < 2) {
      var node = this.node();
      return fullname.local
          ? node.getAttributeNS(fullname.space, fullname.local)
          : node.getAttribute(fullname);
    }

    return this.each((value == null
        ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === "function"
        ? (fullname.local ? attrFunctionNS : attrFunction)
        : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));
  }

  function defaultView(node) {
    return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node
        || (node.document && node) // node is a Window
        || node.defaultView; // node is a Document
  }

  function styleRemove(name) {
    return function() {
      this.style.removeProperty(name);
    };
  }

  function styleConstant(name, value, priority) {
    return function() {
      this.style.setProperty(name, value, priority);
    };
  }

  function styleFunction(name, value, priority) {
    return function() {
      var v = value.apply(this, arguments);
      if (v == null) this.style.removeProperty(name);
      else this.style.setProperty(name, v, priority);
    };
  }

  function selection_style(name, value, priority) {
    return arguments.length > 1
        ? this.each((value == null
              ? styleRemove : typeof value === "function"
              ? styleFunction
              : styleConstant)(name, value, priority == null ? "" : priority))
        : styleValue(this.node(), name);
  }

  function styleValue(node, name) {
    return node.style.getPropertyValue(name)
        || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);
  }

  function propertyRemove(name) {
    return function() {
      delete this[name];
    };
  }

  function propertyConstant(name, value) {
    return function() {
      this[name] = value;
    };
  }

  function propertyFunction(name, value) {
    return function() {
      var v = value.apply(this, arguments);
      if (v == null) delete this[name];
      else this[name] = v;
    };
  }

  function selection_property(name, value) {
    return arguments.length > 1
        ? this.each((value == null
            ? propertyRemove : typeof value === "function"
            ? propertyFunction
            : propertyConstant)(name, value))
        : this.node()[name];
  }

  function classArray(string) {
    return string.trim().split(/^|\s+/);
  }

  function classList(node) {
    return node.classList || new ClassList(node);
  }

  function ClassList(node) {
    this._node = node;
    this._names = classArray(node.getAttribute("class") || "");
  }

  ClassList.prototype = {
    add: function(name) {
      var i = this._names.indexOf(name);
      if (i < 0) {
        this._names.push(name);
        this._node.setAttribute("class", this._names.join(" "));
      }
    },
    remove: function(name) {
      var i = this._names.indexOf(name);
      if (i >= 0) {
        this._names.splice(i, 1);
        this._node.setAttribute("class", this._names.join(" "));
      }
    },
    contains: function(name) {
      return this._names.indexOf(name) >= 0;
    }
  };

  function classedAdd(node, names) {
    var list = classList(node), i = -1, n = names.length;
    while (++i < n) list.add(names[i]);
  }

  function classedRemove(node, names) {
    var list = classList(node), i = -1, n = names.length;
    while (++i < n) list.remove(names[i]);
  }

  function classedTrue(names) {
    return function() {
      classedAdd(this, names);
    };
  }

  function classedFalse(names) {
    return function() {
      classedRemove(this, names);
    };
  }

  function classedFunction(names, value) {
    return function() {
      (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);
    };
  }

  function selection_classed(name, value) {
    var names = classArray(name + "");

    if (arguments.length < 2) {
      var list = classList(this.node()), i = -1, n = names.length;
      while (++i < n) if (!list.contains(names[i])) return false;
      return true;
    }

    return this.each((typeof value === "function"
        ? classedFunction : value
        ? classedTrue
        : classedFalse)(names, value));
  }

  function textRemove() {
    this.textContent = "";
  }

  function textConstant(value) {
    return function() {
      this.textContent = value;
    };
  }

  function textFunction(value) {
    return function() {
      var v = value.apply(this, arguments);
      this.textContent = v == null ? "" : v;
    };
  }

  function selection_text(value) {
    return arguments.length
        ? this.each(value == null
            ? textRemove : (typeof value === "function"
            ? textFunction
            : textConstant)(value))
        : this.node().textContent;
  }

  function htmlRemove() {
    this.innerHTML = "";
  }

  function htmlConstant(value) {
    return function() {
      this.innerHTML = value;
    };
  }

  function htmlFunction(value) {
    return function() {
      var v = value.apply(this, arguments);
      this.innerHTML = v == null ? "" : v;
    };
  }

  function selection_html(value) {
    return arguments.length
        ? this.each(value == null
            ? htmlRemove : (typeof value === "function"
            ? htmlFunction
            : htmlConstant)(value))
        : this.node().innerHTML;
  }

  function raise() {
    if (this.nextSibling) this.parentNode.appendChild(this);
  }

  function selection_raise() {
    return this.each(raise);
  }

  function lower() {
    if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);
  }

  function selection_lower() {
    return this.each(lower);
  }

  function selection_append(name) {
    var create = typeof name === "function" ? name : creator(name);
    return this.select(function() {
      return this.appendChild(create.apply(this, arguments));
    });
  }

  function constantNull() {
    return null;
  }

  function selection_insert(name, before) {
    var create = typeof name === "function" ? name : creator(name),
        select = before == null ? constantNull : typeof before === "function" ? before : selector(before);
    return this.select(function() {
      return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);
    });
  }

  function remove() {
    var parent = this.parentNode;
    if (parent) parent.removeChild(this);
  }

  function selection_remove() {
    return this.each(remove);
  }

  function selection_cloneShallow() {
    return this.parentNode.insertBefore(this.cloneNode(false), this.nextSibling);
  }

  function selection_cloneDeep() {
    return this.parentNode.insertBefore(this.cloneNode(true), this.nextSibling);
  }

  function selection_clone(deep) {
    return this.select(deep ? selection_cloneDeep : selection_cloneShallow);
  }

  function selection_datum(value) {
    return arguments.length
        ? this.property("__data__", value)
        : this.node().__data__;
  }

  var filterEvents = {};

  if (typeof document !== "undefined") {
    var element = document.documentElement;
    if (!("onmouseenter" in element)) {
      filterEvents = {mouseenter: "mouseover", mouseleave: "mouseout"};
    }
  }

  function filterContextListener(listener, index, group) {
    listener = contextListener(listener, index, group);
    return function(event) {
      var related = event.relatedTarget;
      if (!related || (related !== this && !(related.compareDocumentPosition(this) & 8))) {
        listener.call(this, event);
      }
    };
  }

  function contextListener(listener, index, group) {
    return function(event1) {
      try {
        listener.call(this, this.__data__, index, group);
      } finally {
      }
    };
  }

  function parseTypenames(typenames) {
    return typenames.trim().split(/^|\s+/).map(function(t) {
      var name = "", i = t.indexOf(".");
      if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);
      return {type: t, name: name};
    });
  }

  function onRemove(typename) {
    return function() {
      var on = this.__on;
      if (!on) return;
      for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {
        if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {
          this.removeEventListener(o.type, o.listener, o.capture);
        } else {
          on[++i] = o;
        }
      }
      if (++i) on.length = i;
      else delete this.__on;
    };
  }

  function onAdd(typename, value, capture) {
    var wrap = filterEvents.hasOwnProperty(typename.type) ? filterContextListener : contextListener;
    return function(d, i, group) {
      var on = this.__on, o, listener = wrap(value, i, group);
      if (on) for (var j = 0, m = on.length; j < m; ++j) {
        if ((o = on[j]).type === typename.type && o.name === typename.name) {
          this.removeEventListener(o.type, o.listener, o.capture);
          this.addEventListener(o.type, o.listener = listener, o.capture = capture);
          o.value = value;
          return;
        }
      }
      this.addEventListener(typename.type, listener, capture);
      o = {type: typename.type, name: typename.name, value: value, listener: listener, capture: capture};
      if (!on) this.__on = [o];
      else on.push(o);
    };
  }

  function selection_on(typename, value, capture) {
    var typenames = parseTypenames(typename + ""), i, n = typenames.length, t;

    if (arguments.length < 2) {
      var on = this.node().__on;
      if (on) for (var j = 0, m = on.length, o; j < m; ++j) {
        for (i = 0, o = on[j]; i < n; ++i) {
          if ((t = typenames[i]).type === o.type && t.name === o.name) {
            return o.value;
          }
        }
      }
      return;
    }

    on = value ? onAdd : onRemove;
    if (capture == null) capture = false;
    for (i = 0; i < n; ++i) this.each(on(typenames[i], value, capture));
    return this;
  }

  function dispatchEvent(node, type, params) {
    var window = defaultView(node),
        event = window.CustomEvent;

    if (typeof event === "function") {
      event = new event(type, params);
    } else {
      event = window.document.createEvent("Event");
      if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;
      else event.initEvent(type, false, false);
    }

    node.dispatchEvent(event);
  }

  function dispatchConstant(type, params) {
    return function() {
      return dispatchEvent(this, type, params);
    };
  }

  function dispatchFunction(type, params) {
    return function() {
      return dispatchEvent(this, type, params.apply(this, arguments));
    };
  }

  function selection_dispatch(type, params) {
    return this.each((typeof params === "function"
        ? dispatchFunction
        : dispatchConstant)(type, params));
  }

  var root = [null];

  function Selection(groups, parents) {
    this._groups = groups;
    this._parents = parents;
  }

  function selection() {
    return new Selection([[document.documentElement]], root);
  }

  Selection.prototype = selection.prototype = {
    constructor: Selection,
    select: selection_select,
    selectAll: selection_selectAll,
    filter: selection_filter,
    data: selection_data,
    enter: selection_enter,
    exit: selection_exit,
    join: selection_join,
    merge: selection_merge,
    order: selection_order,
    sort: selection_sort,
    call: selection_call,
    nodes: selection_nodes,
    node: selection_node,
    size: selection_size,
    empty: selection_empty,
    each: selection_each,
    attr: selection_attr,
    style: selection_style,
    property: selection_property,
    classed: selection_classed,
    text: selection_text,
    html: selection_html,
    raise: selection_raise,
    lower: selection_lower,
    append: selection_append,
    insert: selection_insert,
    remove: selection_remove,
    clone: selection_clone,
    datum: selection_datum,
    on: selection_on,
    dispatch: selection_dispatch
  };

  function select(selector) {
    return typeof selector === "string"
        ? new Selection([[document.querySelector(selector)]], [document.documentElement])
        : new Selection([[selector]], root);
  }

  function count(node) {
    var sum = 0,
        children = node.children,
        i = children && children.length;
    if (!i) sum = 1;
    else while (--i >= 0) sum += children[i].value;
    node.value = sum;
  }

  function node_count() {
    return this.eachAfter(count);
  }

  function node_each(callback) {
    var node = this, current, next = [node], children, i, n;
    do {
      current = next.reverse(), next = [];
      while (node = current.pop()) {
        callback(node), children = node.children;
        if (children) for (i = 0, n = children.length; i < n; ++i) {
          next.push(children[i]);
        }
      }
    } while (next.length);
    return this;
  }

  function node_eachBefore(callback) {
    var node = this, nodes = [node], children, i;
    while (node = nodes.pop()) {
      callback(node), children = node.children;
      if (children) for (i = children.length - 1; i >= 0; --i) {
        nodes.push(children[i]);
      }
    }
    return this;
  }

  function node_eachAfter(callback) {
    var node = this, nodes = [node], next = [], children, i, n;
    while (node = nodes.pop()) {
      next.push(node), children = node.children;
      if (children) for (i = 0, n = children.length; i < n; ++i) {
        nodes.push(children[i]);
      }
    }
    while (node = next.pop()) {
      callback(node);
    }
    return this;
  }

  function node_sum(value) {
    return this.eachAfter(function(node) {
      var sum = +value(node.data) || 0,
          children = node.children,
          i = children && children.length;
      while (--i >= 0) sum += children[i].value;
      node.value = sum;
    });
  }

  function node_sort(compare) {
    return this.eachBefore(function(node) {
      if (node.children) {
        node.children.sort(compare);
      }
    });
  }

  function node_path(end) {
    var start = this,
        ancestor = leastCommonAncestor(start, end),
        nodes = [start];
    while (start !== ancestor) {
      start = start.parent;
      nodes.push(start);
    }
    var k = nodes.length;
    while (end !== ancestor) {
      nodes.splice(k, 0, end);
      end = end.parent;
    }
    return nodes;
  }

  function leastCommonAncestor(a, b) {
    if (a === b) return a;
    var aNodes = a.ancestors(),
        bNodes = b.ancestors(),
        c = null;
    a = aNodes.pop();
    b = bNodes.pop();
    while (a === b) {
      c = a;
      a = aNodes.pop();
      b = bNodes.pop();
    }
    return c;
  }

  function node_ancestors() {
    var node = this, nodes = [node];
    while (node = node.parent) {
      nodes.push(node);
    }
    return nodes;
  }

  function node_descendants() {
    var nodes = [];
    this.each(function(node) {
      nodes.push(node);
    });
    return nodes;
  }

  function node_leaves() {
    var leaves = [];
    this.eachBefore(function(node) {
      if (!node.children) {
        leaves.push(node);
      }
    });
    return leaves;
  }

  function node_links() {
    var root = this, links = [];
    root.each(function(node) {
      if (node !== root) { // Don’t include the root’s parent, if any.
        links.push({source: node.parent, target: node});
      }
    });
    return links;
  }

  function hierarchy(data, children) {
    var root = new Node(data),
        valued = +data.value && (root.value = data.value),
        node,
        nodes = [root],
        child,
        childs,
        i,
        n;

    if (children == null) children = defaultChildren;

    while (node = nodes.pop()) {
      if (valued) node.value = +node.data.value;
      if ((childs = children(node.data)) && (n = childs.length)) {
        node.children = new Array(n);
        for (i = n - 1; i >= 0; --i) {
          nodes.push(child = node.children[i] = new Node(childs[i]));
          child.parent = node;
          child.depth = node.depth + 1;
        }
      }
    }

    return root.eachBefore(computeHeight);
  }

  function node_copy() {
    return hierarchy(this).eachBefore(copyData);
  }

  function defaultChildren(d) {
    return d.children;
  }

  function copyData(node) {
    node.data = node.data.data;
  }

  function computeHeight(node) {
    var height = 0;
    do node.height = height;
    while ((node = node.parent) && (node.height < ++height));
  }

  function Node(data) {
    this.data = data;
    this.depth =
    this.height = 0;
    this.parent = null;
  }

  Node.prototype = hierarchy.prototype = {
    constructor: Node,
    count: node_count,
    each: node_each,
    eachAfter: node_eachAfter,
    eachBefore: node_eachBefore,
    sum: node_sum,
    sort: node_sort,
    path: node_path,
    ancestors: node_ancestors,
    descendants: node_descendants,
    leaves: node_leaves,
    links: node_links,
    copy: node_copy
  };

  function roundNode(node) {
    node.x0 = Math.round(node.x0);
    node.y0 = Math.round(node.y0);
    node.x1 = Math.round(node.x1);
    node.y1 = Math.round(node.y1);
  }

  function treemapDice(parent, x0, y0, x1, y1) {
    var nodes = parent.children,
        node,
        i = -1,
        n = nodes.length,
        k = parent.value && (x1 - x0) / parent.value;

    while (++i < n) {
      node = nodes[i], node.y0 = y0, node.y1 = y1;
      node.x0 = x0, node.x1 = x0 += node.value * k;
    }
  }

  function d3partition() {
    var dx = 1,
        dy = 1,
        padding = 0,
        round = false;

    function partition(root) {
      var n = root.height + 1;
      root.x0 =
      root.y0 = padding;
      root.x1 = dx;
      root.y1 = dy / n;
      root.eachBefore(positionNode(dy, n));
      if (round) root.eachBefore(roundNode);
      return root;
    }

    function positionNode(dy, n) {
      return function(node) {
        if (node.children) {
          treemapDice(node, node.x0, dy * (node.depth + 1) / n, node.x1, dy * (node.depth + 2) / n);
        }
        var x0 = node.x0,
            y0 = node.y0,
            x1 = node.x1 - padding,
            y1 = node.y1 - padding;
        if (x1 < x0) x0 = x1 = (x0 + x1) / 2;
        if (y1 < y0) y0 = y1 = (y0 + y1) / 2;
        node.x0 = x0;
        node.y0 = y0;
        node.x1 = x1;
        node.y1 = y1;
      };
    }

    partition.round = function(x) {
      return arguments.length ? (round = !!x, partition) : round;
    };

    partition.size = function(x) {
      return arguments.length ? (dx = +x[0], dy = +x[1], partition) : [dx, dy];
    };

    partition.padding = function(x) {
      return arguments.length ? (padding = +x, partition) : padding;
    };

    return partition;
  }

  var pi = Math.PI,
      tau = 2 * pi,
      epsilon = 1e-6,
      tauEpsilon = tau - epsilon;

  function Path() {
    this._x0 = this._y0 = // start of current subpath
    this._x1 = this._y1 = null; // end of current subpath
    this._ = "";
  }

  function path() {
    return new Path;
  }

  Path.prototype = path.prototype = {
    constructor: Path,
    moveTo: function(x, y) {
      this._ += "M" + (this._x0 = this._x1 = +x) + "," + (this._y0 = this._y1 = +y);
    },
    closePath: function() {
      if (this._x1 !== null) {
        this._x1 = this._x0, this._y1 = this._y0;
        this._ += "Z";
      }
    },
    lineTo: function(x, y) {
      this._ += "L" + (this._x1 = +x) + "," + (this._y1 = +y);
    },
    quadraticCurveTo: function(x1, y1, x, y) {
      this._ += "Q" + (+x1) + "," + (+y1) + "," + (this._x1 = +x) + "," + (this._y1 = +y);
    },
    bezierCurveTo: function(x1, y1, x2, y2, x, y) {
      this._ += "C" + (+x1) + "," + (+y1) + "," + (+x2) + "," + (+y2) + "," + (this._x1 = +x) + "," + (this._y1 = +y);
    },
    arcTo: function(x1, y1, x2, y2, r) {
      x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;
      var x0 = this._x1,
          y0 = this._y1,
          x21 = x2 - x1,
          y21 = y2 - y1,
          x01 = x0 - x1,
          y01 = y0 - y1,
          l01_2 = x01 * x01 + y01 * y01;

      // Is the radius negative? Error.
      if (r < 0) throw new Error("negative radius: " + r);

      // Is this path empty? Move to (x1,y1).
      if (this._x1 === null) {
        this._ += "M" + (this._x1 = x1) + "," + (this._y1 = y1);
      }

      // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.
      else if (!(l01_2 > epsilon));

      // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?
      // Equivalently, is (x1,y1) coincident with (x2,y2)?
      // Or, is the radius zero? Line to (x1,y1).
      else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {
        this._ += "L" + (this._x1 = x1) + "," + (this._y1 = y1);
      }

      // Otherwise, draw an arc!
      else {
        var x20 = x2 - x0,
            y20 = y2 - y0,
            l21_2 = x21 * x21 + y21 * y21,
            l20_2 = x20 * x20 + y20 * y20,
            l21 = Math.sqrt(l21_2),
            l01 = Math.sqrt(l01_2),
            l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),
            t01 = l / l01,
            t21 = l / l21;

        // If the start tangent is not coincident with (x0,y0), line to.
        if (Math.abs(t01 - 1) > epsilon) {
          this._ += "L" + (x1 + t01 * x01) + "," + (y1 + t01 * y01);
        }

        this._ += "A" + r + "," + r + ",0,0," + (+(y01 * x20 > x01 * y20)) + "," + (this._x1 = x1 + t21 * x21) + "," + (this._y1 = y1 + t21 * y21);
      }
    },
    arc: function(x, y, r, a0, a1, ccw) {
      x = +x, y = +y, r = +r;
      var dx = r * Math.cos(a0),
          dy = r * Math.sin(a0),
          x0 = x + dx,
          y0 = y + dy,
          cw = 1 ^ ccw,
          da = ccw ? a0 - a1 : a1 - a0;

      // Is the radius negative? Error.
      if (r < 0) throw new Error("negative radius: " + r);

      // Is this path empty? Move to (x0,y0).
      if (this._x1 === null) {
        this._ += "M" + x0 + "," + y0;
      }

      // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).
      else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {
        this._ += "L" + x0 + "," + y0;
      }

      // Is this arc empty? We’re done.
      if (!r) return;

      // Does the angle go the wrong way? Flip the direction.
      if (da < 0) da = da % tau + tau;

      // Is this a complete circle? Draw two arcs to complete the circle.
      if (da > tauEpsilon) {
        this._ += "A" + r + "," + r + ",0,1," + cw + "," + (x - dx) + "," + (y - dy) + "A" + r + "," + r + ",0,1," + cw + "," + (this._x1 = x0) + "," + (this._y1 = y0);
      }

      // Is this arc non-empty? Draw an arc!
      else if (da > epsilon) {
        this._ += "A" + r + "," + r + ",0," + (+(da >= pi)) + "," + cw + "," + (this._x1 = x + r * Math.cos(a1)) + "," + (this._y1 = y + r * Math.sin(a1));
      }
    },
    rect: function(x, y, w, h) {
      this._ += "M" + (this._x0 = this._x1 = +x) + "," + (this._y0 = this._y1 = +y) + "h" + (+w) + "v" + (+h) + "h" + (-w) + "Z";
    },
    toString: function() {
      return this._;
    }
  };

  function constant$1(x) {
    return function constant() {
      return x;
    };
  }

  var abs = Math.abs;
  var atan2 = Math.atan2;
  var cos = Math.cos;
  var max = Math.max;
  var min = Math.min;
  var sin = Math.sin;
  var sqrt = Math.sqrt;

  var epsilon$1 = 1e-12;
  var pi$1 = Math.PI;
  var halfPi = pi$1 / 2;
  var tau$1 = 2 * pi$1;

  function acos(x) {
    return x > 1 ? 0 : x < -1 ? pi$1 : Math.acos(x);
  }

  function asin(x) {
    return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);
  }

  function arcInnerRadius(d) {
    return d.innerRadius;
  }

  function arcOuterRadius(d) {
    return d.outerRadius;
  }

  function arcStartAngle(d) {
    return d.startAngle;
  }

  function arcEndAngle(d) {
    return d.endAngle;
  }

  function arcPadAngle(d) {
    return d && d.padAngle; // Note: optional!
  }

  function intersect(x0, y0, x1, y1, x2, y2, x3, y3) {
    var x10 = x1 - x0, y10 = y1 - y0,
        x32 = x3 - x2, y32 = y3 - y2,
        t = y32 * x10 - x32 * y10;
    if (t * t < epsilon$1) return;
    t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;
    return [x0 + t * x10, y0 + t * y10];
  }

  // Compute perpendicular offset line of length rc.
  // http://mathworld.wolfram.com/Circle-LineIntersection.html
  function cornerTangents(x0, y0, x1, y1, r1, rc, cw) {
    var x01 = x0 - x1,
        y01 = y0 - y1,
        lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),
        ox = lo * y01,
        oy = -lo * x01,
        x11 = x0 + ox,
        y11 = y0 + oy,
        x10 = x1 + ox,
        y10 = y1 + oy,
        x00 = (x11 + x10) / 2,
        y00 = (y11 + y10) / 2,
        dx = x10 - x11,
        dy = y10 - y11,
        d2 = dx * dx + dy * dy,
        r = r1 - rc,
        D = x11 * y10 - x10 * y11,
        d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),
        cx0 = (D * dy - dx * d) / d2,
        cy0 = (-D * dx - dy * d) / d2,
        cx1 = (D * dy + dx * d) / d2,
        cy1 = (-D * dx + dy * d) / d2,
        dx0 = cx0 - x00,
        dy0 = cy0 - y00,
        dx1 = cx1 - x00,
        dy1 = cy1 - y00;

    // Pick the closer of the two intersection points.
    // TODO Is there a faster way to determine which intersection to use?
    if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;

    return {
      cx: cx0,
      cy: cy0,
      x01: -ox,
      y01: -oy,
      x11: cx0 * (r1 / r - 1),
      y11: cy0 * (r1 / r - 1)
    };
  }

  function d3arc() {
    var innerRadius = arcInnerRadius,
        outerRadius = arcOuterRadius,
        cornerRadius = constant$1(0),
        padRadius = null,
        startAngle = arcStartAngle,
        endAngle = arcEndAngle,
        padAngle = arcPadAngle,
        context = null;

    function arc() {
      var buffer,
          r,
          r0 = +innerRadius.apply(this, arguments),
          r1 = +outerRadius.apply(this, arguments),
          a0 = startAngle.apply(this, arguments) - halfPi,
          a1 = endAngle.apply(this, arguments) - halfPi,
          da = abs(a1 - a0),
          cw = a1 > a0;

      if (!context) context = buffer = path();

      // Ensure that the outer radius is always larger than the inner radius.
      if (r1 < r0) r = r1, r1 = r0, r0 = r;

      // Is it a point?
      if (!(r1 > epsilon$1)) context.moveTo(0, 0);

      // Or is it a circle or annulus?
      else if (da > tau$1 - epsilon$1) {
        context.moveTo(r1 * cos(a0), r1 * sin(a0));
        context.arc(0, 0, r1, a0, a1, !cw);
        if (r0 > epsilon$1) {
          context.moveTo(r0 * cos(a1), r0 * sin(a1));
          context.arc(0, 0, r0, a1, a0, cw);
        }
      }

      // Or is it a circular or annular sector?
      else {
        var a01 = a0,
            a11 = a1,
            a00 = a0,
            a10 = a1,
            da0 = da,
            da1 = da,
            ap = padAngle.apply(this, arguments) / 2,
            rp = (ap > epsilon$1) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),
            rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),
            rc0 = rc,
            rc1 = rc,
            t0,
            t1;

        // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.
        if (rp > epsilon$1) {
          var p0 = asin(rp / r0 * sin(ap)),
              p1 = asin(rp / r1 * sin(ap));
          if ((da0 -= p0 * 2) > epsilon$1) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;
          else da0 = 0, a00 = a10 = (a0 + a1) / 2;
          if ((da1 -= p1 * 2) > epsilon$1) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;
          else da1 = 0, a01 = a11 = (a0 + a1) / 2;
        }

        var x01 = r1 * cos(a01),
            y01 = r1 * sin(a01),
            x10 = r0 * cos(a10),
            y10 = r0 * sin(a10);

        // Apply rounded corners?
        if (rc > epsilon$1) {
          var x11 = r1 * cos(a11),
              y11 = r1 * sin(a11),
              x00 = r0 * cos(a00),
              y00 = r0 * sin(a00),
              oc;

          // Restrict the corner radius according to the sector angle.
          if (da < pi$1 && (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10))) {
            var ax = x01 - oc[0],
                ay = y01 - oc[1],
                bx = x11 - oc[0],
                by = y11 - oc[1],
                kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),
                lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);
            rc0 = min(rc, (r0 - lc) / (kc - 1));
            rc1 = min(rc, (r1 - lc) / (kc + 1));
          }
        }

        // Is the sector collapsed to a line?
        if (!(da1 > epsilon$1)) context.moveTo(x01, y01);

        // Does the sector’s outer ring have rounded corners?
        else if (rc1 > epsilon$1) {
          t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);
          t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);

          context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);

          // Have the corners merged?
          if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);

          // Otherwise, draw the two corners and the ring.
          else {
            context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);
            context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);
            context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);
          }
        }

        // Or is the outer ring just a circular arc?
        else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);

        // Is there no inner ring, and it’s a circular sector?
        // Or perhaps it’s an annular sector collapsed due to padding?
        if (!(r0 > epsilon$1) || !(da0 > epsilon$1)) context.lineTo(x10, y10);

        // Does the sector’s inner ring (or point) have rounded corners?
        else if (rc0 > epsilon$1) {
          t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);
          t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);

          context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);

          // Have the corners merged?
          if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);

          // Otherwise, draw the two corners and the ring.
          else {
            context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);
            context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);
            context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);
          }
        }

        // Or is the inner ring just a circular arc?
        else context.arc(0, 0, r0, a10, a00, cw);
      }

      context.closePath();

      if (buffer) return context = null, buffer + "" || null;
    }

    arc.centroid = function() {
      var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,
          a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi$1 / 2;
      return [cos(a) * r, sin(a) * r];
    };

    arc.innerRadius = function(_) {
      return arguments.length ? (innerRadius = typeof _ === "function" ? _ : constant$1(+_), arc) : innerRadius;
    };

    arc.outerRadius = function(_) {
      return arguments.length ? (outerRadius = typeof _ === "function" ? _ : constant$1(+_), arc) : outerRadius;
    };

    arc.cornerRadius = function(_) {
      return arguments.length ? (cornerRadius = typeof _ === "function" ? _ : constant$1(+_), arc) : cornerRadius;
    };

    arc.padRadius = function(_) {
      return arguments.length ? (padRadius = _ == null ? null : typeof _ === "function" ? _ : constant$1(+_), arc) : padRadius;
    };

    arc.startAngle = function(_) {
      return arguments.length ? (startAngle = typeof _ === "function" ? _ : constant$1(+_), arc) : startAngle;
    };

    arc.endAngle = function(_) {
      return arguments.length ? (endAngle = typeof _ === "function" ? _ : constant$1(+_), arc) : endAngle;
    };

    arc.padAngle = function(_) {
      return arguments.length ? (padAngle = typeof _ === "function" ? _ : constant$1(+_), arc) : padAngle;
    };

    arc.context = function(_) {
      return arguments.length ? ((context = _ == null ? null : _), arc) : context;
    };

    return arc;
  }

  function sign(x) {
    return x < 0 ? -1 : 1;
  }

  // Calculate the slopes of the tangents (Hermite-type interpolation) based on
  // the following paper: Steffen, M. 1990. A Simple Method for Monotonic
  // Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.
  // NOV(II), P. 443, 1990.
  function slope3(that, x2, y2) {
    var h0 = that._x1 - that._x0,
        h1 = x2 - that._x1,
        s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),
        s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),
        p = (s0 * h1 + s1 * h0) / (h0 + h1);
    return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;
  }

  // Calculate a one-sided slope.
  function slope2(that, t) {
    var h = that._x1 - that._x0;
    return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;
  }

  // According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations
  // "you can express cubic Hermite interpolation in terms of cubic Bézier curves
  // with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1".
  function point(that, t0, t1) {
    var x0 = that._x0,
        y0 = that._y0,
        x1 = that._x1,
        y1 = that._y1,
        dx = (x1 - x0) / 3;
    that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);
  }

  function MonotoneX(context) {
    this._context = context;
  }

  MonotoneX.prototype = {
    areaStart: function() {
      this._line = 0;
    },
    areaEnd: function() {
      this._line = NaN;
    },
    lineStart: function() {
      this._x0 = this._x1 =
      this._y0 = this._y1 =
      this._t0 = NaN;
      this._point = 0;
    },
    lineEnd: function() {
      switch (this._point) {
        case 2: this._context.lineTo(this._x1, this._y1); break;
        case 3: point(this, this._t0, slope2(this, this._t0)); break;
      }
      if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();
      this._line = 1 - this._line;
    },
    point: function(x, y) {
      var t1 = NaN;

      x = +x, y = +y;
      if (x === this._x1 && y === this._y1) return; // Ignore coincident points.
      switch (this._point) {
        case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;
        case 1: this._point = 2; break;
        case 2: this._point = 3; point(this, slope2(this, t1 = slope3(this, x, y)), t1); break;
        default: point(this, this._t0, t1 = slope3(this, x, y)); break;
      }

      this._x0 = this._x1, this._x1 = x;
      this._y0 = this._y1, this._y1 = y;
      this._t0 = t1;
    }
  };

  function MonotoneY(context) {
    this._context = new ReflectContext(context);
  }

  (MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x, y) {
    MonotoneX.prototype.point.call(this, y, x);
  };

  function ReflectContext(context) {
    this._context = context;
  }

  ReflectContext.prototype = {
    moveTo: function(x, y) { this._context.moveTo(y, x); },
    closePath: function() { this._context.closePath(); },
    lineTo: function(x, y) { this._context.lineTo(y, x); },
    bezierCurveTo: function(x1, y1, x2, y2, x, y) { this._context.bezierCurveTo(y1, x1, y2, x2, y, x); }
  };

  function ascending$1(a, b) {
    return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
  }

  function bisector(compare) {
    if (compare.length === 1) compare = ascendingComparator(compare);
    return {
      left: function(a, x, lo, hi) {
        if (lo == null) lo = 0;
        if (hi == null) hi = a.length;
        while (lo < hi) {
          var mid = lo + hi >>> 1;
          if (compare(a[mid], x) < 0) lo = mid + 1;
          else hi = mid;
        }
        return lo;
      },
      right: function(a, x, lo, hi) {
        if (lo == null) lo = 0;
        if (hi == null) hi = a.length;
        while (lo < hi) {
          var mid = lo + hi >>> 1;
          if (compare(a[mid], x) > 0) hi = mid;
          else lo = mid + 1;
        }
        return lo;
      }
    };
  }

  function ascendingComparator(f) {
    return function(d, x) {
      return ascending$1(f(d), x);
    };
  }

  var ascendingBisect = bisector(ascending$1);
  var bisectRight = ascendingBisect.right;

  var e10 = Math.sqrt(50),
      e5 = Math.sqrt(10),
      e2 = Math.sqrt(2);

  function ticks(start, stop, count) {
    var reverse,
        i = -1,
        n,
        ticks,
        step;

    stop = +stop, start = +start, count = +count;
    if (start === stop && count > 0) return [start];
    if (reverse = stop < start) n = start, start = stop, stop = n;
    if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];

    if (step > 0) {
      start = Math.ceil(start / step);
      stop = Math.floor(stop / step);
      ticks = new Array(n = Math.ceil(stop - start + 1));
      while (++i < n) ticks[i] = (start + i) * step;
    } else {
      start = Math.floor(start * step);
      stop = Math.ceil(stop * step);
      ticks = new Array(n = Math.ceil(start - stop + 1));
      while (++i < n) ticks[i] = (start - i) / step;
    }

    if (reverse) ticks.reverse();

    return ticks;
  }

  function tickIncrement(start, stop, count) {
    var step = (stop - start) / Math.max(0, count),
        power = Math.floor(Math.log(step) / Math.LN10),
        error = step / Math.pow(10, power);
    return power >= 0
        ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power)
        : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);
  }

  function tickStep(start, stop, count) {
    var step0 = Math.abs(stop - start) / Math.max(0, count),
        step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),
        error = step0 / step1;
    if (error >= e10) step1 *= 10;
    else if (error >= e5) step1 *= 5;
    else if (error >= e2) step1 *= 2;
    return stop < start ? -step1 : step1;
  }

  function initRange(domain, range) {
    switch (arguments.length) {
      case 0: break;
      case 1: this.range(domain); break;
      default: this.range(range).domain(domain); break;
    }
    return this;
  }

  var prefix = "$";

  function Map() {}

  Map.prototype = map.prototype = {
    constructor: Map,
    has: function(key) {
      return (prefix + key) in this;
    },
    get: function(key) {
      return this[prefix + key];
    },
    set: function(key, value) {
      this[prefix + key] = value;
      return this;
    },
    remove: function(key) {
      var property = prefix + key;
      return property in this && delete this[property];
    },
    clear: function() {
      for (var property in this) if (property[0] === prefix) delete this[property];
    },
    keys: function() {
      var keys = [];
      for (var property in this) if (property[0] === prefix) keys.push(property.slice(1));
      return keys;
    },
    values: function() {
      var values = [];
      for (var property in this) if (property[0] === prefix) values.push(this[property]);
      return values;
    },
    entries: function() {
      var entries = [];
      for (var property in this) if (property[0] === prefix) entries.push({key: property.slice(1), value: this[property]});
      return entries;
    },
    size: function() {
      var size = 0;
      for (var property in this) if (property[0] === prefix) ++size;
      return size;
    },
    empty: function() {
      for (var property in this) if (property[0] === prefix) return false;
      return true;
    },
    each: function(f) {
      for (var property in this) if (property[0] === prefix) f(this[property], property.slice(1), this);
    }
  };

  function map(object, f) {
    var map = new Map;

    // Copy constructor.
    if (object instanceof Map) object.each(function(value, key) { map.set(key, value); });

    // Index array by numeric index or specified key function.
    else if (Array.isArray(object)) {
      var i = -1,
          n = object.length,
          o;

      if (f == null) while (++i < n) map.set(i, object[i]);
      else while (++i < n) map.set(f(o = object[i], i, object), o);
    }

    // Convert object to map.
    else if (object) for (var key in object) map.set(key, object[key]);

    return map;
  }

  function Set() {}

  var proto = map.prototype;

  Set.prototype = set.prototype = {
    constructor: Set,
    has: proto.has,
    add: function(value) {
      value += "";
      this[prefix + value] = value;
      return this;
    },
    remove: proto.remove,
    clear: proto.clear,
    values: proto.keys,
    size: proto.size,
    empty: proto.empty,
    each: proto.each
  };

  function set(object, f) {
    var set = new Set;

    // Copy constructor.
    if (object instanceof Set) object.each(function(value) { set.add(value); });

    // Otherwise, assume it’s an array.
    else if (object) {
      var i = -1, n = object.length;
      if (f == null) while (++i < n) set.add(object[i]);
      else while (++i < n) set.add(f(object[i], i, object));
    }

    return set;
  }

  var array = Array.prototype;

  var map$1 = array.map;
  var slice = array.slice;

  function define(constructor, factory, prototype) {
    constructor.prototype = factory.prototype = prototype;
    prototype.constructor = constructor;
  }

  function extend(parent, definition) {
    var prototype = Object.create(parent.prototype);
    for (var key in definition) prototype[key] = definition[key];
    return prototype;
  }

  function Color() {}

  var darker = 0.7;
  var brighter = 1 / darker;

  var reI = "\\s*([+-]?\\d+)\\s*",
      reN = "\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",
      reP = "\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",
      reHex3 = /^#([0-9a-f]{3})$/,
      reHex6 = /^#([0-9a-f]{6})$/,
      reRgbInteger = new RegExp("^rgb\\(" + [reI, reI, reI] + "\\)$"),
      reRgbPercent = new RegExp("^rgb\\(" + [reP, reP, reP] + "\\)$"),
      reRgbaInteger = new RegExp("^rgba\\(" + [reI, reI, reI, reN] + "\\)$"),
      reRgbaPercent = new RegExp("^rgba\\(" + [reP, reP, reP, reN] + "\\)$"),
      reHslPercent = new RegExp("^hsl\\(" + [reN, reP, reP] + "\\)$"),
      reHslaPercent = new RegExp("^hsla\\(" + [reN, reP, reP, reN] + "\\)$");

  var named = {
    aliceblue: 0xf0f8ff,
    antiquewhite: 0xfaebd7,
    aqua: 0x00ffff,
    aquamarine: 0x7fffd4,
    azure: 0xf0ffff,
    beige: 0xf5f5dc,
    bisque: 0xffe4c4,
    black: 0x000000,
    blanchedalmond: 0xffebcd,
    blue: 0x0000ff,
    blueviolet: 0x8a2be2,
    brown: 0xa52a2a,
    burlywood: 0xdeb887,
    cadetblue: 0x5f9ea0,
    chartreuse: 0x7fff00,
    chocolate: 0xd2691e,
    coral: 0xff7f50,
    cornflowerblue: 0x6495ed,
    cornsilk: 0xfff8dc,
    crimson: 0xdc143c,
    cyan: 0x00ffff,
    darkblue: 0x00008b,
    darkcyan: 0x008b8b,
    darkgoldenrod: 0xb8860b,
    darkgray: 0xa9a9a9,
    darkgreen: 0x006400,
    darkgrey: 0xa9a9a9,
    darkkhaki: 0xbdb76b,
    darkmagenta: 0x8b008b,
    darkolivegreen: 0x556b2f,
    darkorange: 0xff8c00,
    darkorchid: 0x9932cc,
    darkred: 0x8b0000,
    darksalmon: 0xe9967a,
    darkseagreen: 0x8fbc8f,
    darkslateblue: 0x483d8b,
    darkslategray: 0x2f4f4f,
    darkslategrey: 0x2f4f4f,
    darkturquoise: 0x00ced1,
    darkviolet: 0x9400d3,
    deeppink: 0xff1493,
    deepskyblue: 0x00bfff,
    dimgray: 0x696969,
    dimgrey: 0x696969,
    dodgerblue: 0x1e90ff,
    firebrick: 0xb22222,
    floralwhite: 0xfffaf0,
    forestgreen: 0x228b22,
    fuchsia: 0xff00ff,
    gainsboro: 0xdcdcdc,
    ghostwhite: 0xf8f8ff,
    gold: 0xffd700,
    goldenrod: 0xdaa520,
    gray: 0x808080,
    green: 0x008000,
    greenyellow: 0xadff2f,
    grey: 0x808080,
    honeydew: 0xf0fff0,
    hotpink: 0xff69b4,
    indianred: 0xcd5c5c,
    indigo: 0x4b0082,
    ivory: 0xfffff0,
    khaki: 0xf0e68c,
    lavender: 0xe6e6fa,
    lavenderblush: 0xfff0f5,
    lawngreen: 0x7cfc00,
    lemonchiffon: 0xfffacd,
    lightblue: 0xadd8e6,
    lightcoral: 0xf08080,
    lightcyan: 0xe0ffff,
    lightgoldenrodyellow: 0xfafad2,
    lightgray: 0xd3d3d3,
    lightgreen: 0x90ee90,
    lightgrey: 0xd3d3d3,
    lightpink: 0xffb6c1,
    lightsalmon: 0xffa07a,
    lightseagreen: 0x20b2aa,
    lightskyblue: 0x87cefa,
    lightslategray: 0x778899,
    lightslategrey: 0x778899,
    lightsteelblue: 0xb0c4de,
    lightyellow: 0xffffe0,
    lime: 0x00ff00,
    limegreen: 0x32cd32,
    linen: 0xfaf0e6,
    magenta: 0xff00ff,
    maroon: 0x800000,
    mediumaquamarine: 0x66cdaa,
    mediumblue: 0x0000cd,
    mediumorchid: 0xba55d3,
    mediumpurple: 0x9370db,
    mediumseagreen: 0x3cb371,
    mediumslateblue: 0x7b68ee,
    mediumspringgreen: 0x00fa9a,
    mediumturquoise: 0x48d1cc,
    mediumvioletred: 0xc71585,
    midnightblue: 0x191970,
    mintcream: 0xf5fffa,
    mistyrose: 0xffe4e1,
    moccasin: 0xffe4b5,
    navajowhite: 0xffdead,
    navy: 0x000080,
    oldlace: 0xfdf5e6,
    olive: 0x808000,
    olivedrab: 0x6b8e23,
    orange: 0xffa500,
    orangered: 0xff4500,
    orchid: 0xda70d6,
    palegoldenrod: 0xeee8aa,
    palegreen: 0x98fb98,
    paleturquoise: 0xafeeee,
    palevioletred: 0xdb7093,
    papayawhip: 0xffefd5,
    peachpuff: 0xffdab9,
    peru: 0xcd853f,
    pink: 0xffc0cb,
    plum: 0xdda0dd,
    powderblue: 0xb0e0e6,
    purple: 0x800080,
    rebeccapurple: 0x663399,
    red: 0xff0000,
    rosybrown: 0xbc8f8f,
    royalblue: 0x4169e1,
    saddlebrown: 0x8b4513,
    salmon: 0xfa8072,
    sandybrown: 0xf4a460,
    seagreen: 0x2e8b57,
    seashell: 0xfff5ee,
    sienna: 0xa0522d,
    silver: 0xc0c0c0,
    skyblue: 0x87ceeb,
    slateblue: 0x6a5acd,
    slategray: 0x708090,
    slategrey: 0x708090,
    snow: 0xfffafa,
    springgreen: 0x00ff7f,
    steelblue: 0x4682b4,
    tan: 0xd2b48c,
    teal: 0x008080,
    thistle: 0xd8bfd8,
    tomato: 0xff6347,
    turquoise: 0x40e0d0,
    violet: 0xee82ee,
    wheat: 0xf5deb3,
    white: 0xffffff,
    whitesmoke: 0xf5f5f5,
    yellow: 0xffff00,
    yellowgreen: 0x9acd32
  };

  define(Color, color, {
    displayable: function() {
      return this.rgb().displayable();
    },
    hex: function() {
      return this.rgb().hex();
    },
    toString: function() {
      return this.rgb() + "";
    }
  });

  function color(format) {
    var m;
    format = (format + "").trim().toLowerCase();
    return (m = reHex3.exec(format)) ? (m = parseInt(m[1], 16), new Rgb((m >> 8 & 0xf) | (m >> 4 & 0x0f0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1)) // #f00
        : (m = reHex6.exec(format)) ? rgbn(parseInt(m[1], 16)) // #ff0000
        : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)
        : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)
        : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)
        : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)
        : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)
        : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)
        : named.hasOwnProperty(format) ? rgbn(named[format])
        : format === "transparent" ? new Rgb(NaN, NaN, NaN, 0)
        : null;
  }

  function rgbn(n) {
    return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);
  }

  function rgba(r, g, b, a) {
    if (a <= 0) r = g = b = NaN;
    return new Rgb(r, g, b, a);
  }

  function rgbConvert(o) {
    if (!(o instanceof Color)) o = color(o);
    if (!o) return new Rgb;
    o = o.rgb();
    return new Rgb(o.r, o.g, o.b, o.opacity);
  }

  function rgb(r, g, b, opacity) {
    return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);
  }

  function Rgb(r, g, b, opacity) {
    this.r = +r;
    this.g = +g;
    this.b = +b;
    this.opacity = +opacity;
  }

  define(Rgb, rgb, extend(Color, {
    brighter: function(k) {
      k = k == null ? brighter : Math.pow(brighter, k);
      return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);
    },
    darker: function(k) {
      k = k == null ? darker : Math.pow(darker, k);
      return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);
    },
    rgb: function() {
      return this;
    },
    displayable: function() {
      return (0 <= this.r && this.r <= 255)
          && (0 <= this.g && this.g <= 255)
          && (0 <= this.b && this.b <= 255)
          && (0 <= this.opacity && this.opacity <= 1);
    },
    hex: function() {
      return "#" + hex(this.r) + hex(this.g) + hex(this.b);
    },
    toString: function() {
      var a = this.opacity; a = isNaN(a) ? 1 : Math.max(0, Math.min(1, a));
      return (a === 1 ? "rgb(" : "rgba(")
          + Math.max(0, Math.min(255, Math.round(this.r) || 0)) + ", "
          + Math.max(0, Math.min(255, Math.round(this.g) || 0)) + ", "
          + Math.max(0, Math.min(255, Math.round(this.b) || 0))
          + (a === 1 ? ")" : ", " + a + ")");
    }
  }));

  function hex(value) {
    value = Math.max(0, Math.min(255, Math.round(value) || 0));
    return (value < 16 ? "0" : "") + value.toString(16);
  }

  function hsla(h, s, l, a) {
    if (a <= 0) h = s = l = NaN;
    else if (l <= 0 || l >= 1) h = s = NaN;
    else if (s <= 0) h = NaN;
    return new Hsl(h, s, l, a);
  }

  function hslConvert(o) {
    if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);
    if (!(o instanceof Color)) o = color(o);
    if (!o) return new Hsl;
    if (o instanceof Hsl) return o;
    o = o.rgb();
    var r = o.r / 255,
        g = o.g / 255,
        b = o.b / 255,
        min = Math.min(r, g, b),
        max = Math.max(r, g, b),
        h = NaN,
        s = max - min,
        l = (max + min) / 2;
    if (s) {
      if (r === max) h = (g - b) / s + (g < b) * 6;
      else if (g === max) h = (b - r) / s + 2;
      else h = (r - g) / s + 4;
      s /= l < 0.5 ? max + min : 2 - max - min;
      h *= 60;
    } else {
      s = l > 0 && l < 1 ? 0 : h;
    }
    return new Hsl(h, s, l, o.opacity);
  }

  function hsl(h, s, l, opacity) {
    return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);
  }

  function Hsl(h, s, l, opacity) {
    this.h = +h;
    this.s = +s;
    this.l = +l;
    this.opacity = +opacity;
  }

  define(Hsl, hsl, extend(Color, {
    brighter: function(k) {
      k = k == null ? brighter : Math.pow(brighter, k);
      return new Hsl(this.h, this.s, this.l * k, this.opacity);
    },
    darker: function(k) {
      k = k == null ? darker : Math.pow(darker, k);
      return new Hsl(this.h, this.s, this.l * k, this.opacity);
    },
    rgb: function() {
      var h = this.h % 360 + (this.h < 0) * 360,
          s = isNaN(h) || isNaN(this.s) ? 0 : this.s,
          l = this.l,
          m2 = l + (l < 0.5 ? l : 1 - l) * s,
          m1 = 2 * l - m2;
      return new Rgb(
        hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),
        hsl2rgb(h, m1, m2),
        hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),
        this.opacity
      );
    },
    displayable: function() {
      return (0 <= this.s && this.s <= 1 || isNaN(this.s))
          && (0 <= this.l && this.l <= 1)
          && (0 <= this.opacity && this.opacity <= 1);
    }
  }));

  /* From FvD 13.37, CSS Color Module Level 3 */
  function hsl2rgb(h, m1, m2) {
    return (h < 60 ? m1 + (m2 - m1) * h / 60
        : h < 180 ? m2
        : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60
        : m1) * 255;
  }

  var deg2rad = Math.PI / 180;
  var rad2deg = 180 / Math.PI;

  // https://beta.observablehq.com/@mbostock/lab-and-rgb
  var K = 18,
      Xn = 0.96422,
      Yn = 1,
      Zn = 0.82521,
      t0 = 4 / 29,
      t1 = 6 / 29,
      t2 = 3 * t1 * t1,
      t3 = t1 * t1 * t1;

  function labConvert(o) {
    if (o instanceof Lab) return new Lab(o.l, o.a, o.b, o.opacity);
    if (o instanceof Hcl) {
      if (isNaN(o.h)) return new Lab(o.l, 0, 0, o.opacity);
      var h = o.h * deg2rad;
      return new Lab(o.l, Math.cos(h) * o.c, Math.sin(h) * o.c, o.opacity);
    }
    if (!(o instanceof Rgb)) o = rgbConvert(o);
    var r = rgb2lrgb(o.r),
        g = rgb2lrgb(o.g),
        b = rgb2lrgb(o.b),
        y = xyz2lab((0.2225045 * r + 0.7168786 * g + 0.0606169 * b) / Yn), x, z;
    if (r === g && g === b) x = z = y; else {
      x = xyz2lab((0.4360747 * r + 0.3850649 * g + 0.1430804 * b) / Xn);
      z = xyz2lab((0.0139322 * r + 0.0971045 * g + 0.7141733 * b) / Zn);
    }
    return new Lab(116 * y - 16, 500 * (x - y), 200 * (y - z), o.opacity);
  }

  function lab(l, a, b, opacity) {
    return arguments.length === 1 ? labConvert(l) : new Lab(l, a, b, opacity == null ? 1 : opacity);
  }

  function Lab(l, a, b, opacity) {
    this.l = +l;
    this.a = +a;
    this.b = +b;
    this.opacity = +opacity;
  }

  define(Lab, lab, extend(Color, {
    brighter: function(k) {
      return new Lab(this.l + K * (k == null ? 1 : k), this.a, this.b, this.opacity);
    },
    darker: function(k) {
      return new Lab(this.l - K * (k == null ? 1 : k), this.a, this.b, this.opacity);
    },
    rgb: function() {
      var y = (this.l + 16) / 116,
          x = isNaN(this.a) ? y : y + this.a / 500,
          z = isNaN(this.b) ? y : y - this.b / 200;
      x = Xn * lab2xyz(x);
      y = Yn * lab2xyz(y);
      z = Zn * lab2xyz(z);
      return new Rgb(
        lrgb2rgb( 3.1338561 * x - 1.6168667 * y - 0.4906146 * z),
        lrgb2rgb(-0.9787684 * x + 1.9161415 * y + 0.0334540 * z),
        lrgb2rgb( 0.0719453 * x - 0.2289914 * y + 1.4052427 * z),
        this.opacity
      );
    }
  }));

  function xyz2lab(t) {
    return t > t3 ? Math.pow(t, 1 / 3) : t / t2 + t0;
  }

  function lab2xyz(t) {
    return t > t1 ? t * t * t : t2 * (t - t0);
  }

  function lrgb2rgb(x) {
    return 255 * (x <= 0.0031308 ? 12.92 * x : 1.055 * Math.pow(x, 1 / 2.4) - 0.055);
  }

  function rgb2lrgb(x) {
    return (x /= 255) <= 0.04045 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);
  }

  function hclConvert(o) {
    if (o instanceof Hcl) return new Hcl(o.h, o.c, o.l, o.opacity);
    if (!(o instanceof Lab)) o = labConvert(o);
    if (o.a === 0 && o.b === 0) return new Hcl(NaN, 0, o.l, o.opacity);
    var h = Math.atan2(o.b, o.a) * rad2deg;
    return new Hcl(h < 0 ? h + 360 : h, Math.sqrt(o.a * o.a + o.b * o.b), o.l, o.opacity);
  }

  function hcl(h, c, l, opacity) {
    return arguments.length === 1 ? hclConvert(h) : new Hcl(h, c, l, opacity == null ? 1 : opacity);
  }

  function Hcl(h, c, l, opacity) {
    this.h = +h;
    this.c = +c;
    this.l = +l;
    this.opacity = +opacity;
  }

  define(Hcl, hcl, extend(Color, {
    brighter: function(k) {
      return new Hcl(this.h, this.c, this.l + K * (k == null ? 1 : k), this.opacity);
    },
    darker: function(k) {
      return new Hcl(this.h, this.c, this.l - K * (k == null ? 1 : k), this.opacity);
    },
    rgb: function() {
      return labConvert(this).rgb();
    }
  }));

  var A = -0.14861,
      B = +1.78277,
      C = -0.29227,
      D = -0.90649,
      E = +1.97294,
      ED = E * D,
      EB = E * B,
      BC_DA = B * C - D * A;

  function cubehelixConvert(o) {
    if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);
    if (!(o instanceof Rgb)) o = rgbConvert(o);
    var r = o.r / 255,
        g = o.g / 255,
        b = o.b / 255,
        l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),
        bl = b - l,
        k = (E * (g - l) - C * bl) / D,
        s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)), // NaN if l=0 or l=1
        h = s ? Math.atan2(k, bl) * rad2deg - 120 : NaN;
    return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);
  }

  function cubehelix(h, s, l, opacity) {
    return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);
  }

  function Cubehelix(h, s, l, opacity) {
    this.h = +h;
    this.s = +s;
    this.l = +l;
    this.opacity = +opacity;
  }

  define(Cubehelix, cubehelix, extend(Color, {
    brighter: function(k) {
      k = k == null ? brighter : Math.pow(brighter, k);
      return new Cubehelix(this.h, this.s, this.l * k, this.opacity);
    },
    darker: function(k) {
      k = k == null ? darker : Math.pow(darker, k);
      return new Cubehelix(this.h, this.s, this.l * k, this.opacity);
    },
    rgb: function() {
      var h = isNaN(this.h) ? 0 : (this.h + 120) * deg2rad,
          l = +this.l,
          a = isNaN(this.s) ? 0 : this.s * l * (1 - l),
          cosh = Math.cos(h),
          sinh = Math.sin(h);
      return new Rgb(
        255 * (l + a * (A * cosh + B * sinh)),
        255 * (l + a * (C * cosh + D * sinh)),
        255 * (l + a * (E * cosh)),
        this.opacity
      );
    }
  }));

  function constant$2(x) {
    return function() {
      return x;
    };
  }

  function linear(a, d) {
    return function(t) {
      return a + t * d;
    };
  }

  function exponential(a, b, y) {
    return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {
      return Math.pow(a + t * b, y);
    };
  }

  function gamma(y) {
    return (y = +y) === 1 ? nogamma : function(a, b) {
      return b - a ? exponential(a, b, y) : constant$2(isNaN(a) ? b : a);
    };
  }

  function nogamma(a, b) {
    var d = b - a;
    return d ? linear(a, d) : constant$2(isNaN(a) ? b : a);
  }

  var rgb$1 = (function rgbGamma(y) {
    var color = gamma(y);

    function rgb$1(start, end) {
      var r = color((start = rgb(start)).r, (end = rgb(end)).r),
          g = color(start.g, end.g),
          b = color(start.b, end.b),
          opacity = nogamma(start.opacity, end.opacity);
      return function(t) {
        start.r = r(t);
        start.g = g(t);
        start.b = b(t);
        start.opacity = opacity(t);
        return start + "";
      };
    }

    rgb$1.gamma = rgbGamma;

    return rgb$1;
  })(1);

  function array$1(a, b) {
    var nb = b ? b.length : 0,
        na = a ? Math.min(nb, a.length) : 0,
        x = new Array(na),
        c = new Array(nb),
        i;

    for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);
    for (; i < nb; ++i) c[i] = b[i];

    return function(t) {
      for (i = 0; i < na; ++i) c[i] = x[i](t);
      return c;
    };
  }

  function date(a, b) {
    var d = new Date;
    return a = +a, b -= a, function(t) {
      return d.setTime(a + b * t), d;
    };
  }

  function number(a, b) {
    return a = +a, b -= a, function(t) {
      return a + b * t;
    };
  }

  function object(a, b) {
    var i = {},
        c = {},
        k;

    if (a === null || typeof a !== "object") a = {};
    if (b === null || typeof b !== "object") b = {};

    for (k in b) {
      if (k in a) {
        i[k] = value(a[k], b[k]);
      } else {
        c[k] = b[k];
      }
    }

    return function(t) {
      for (k in i) c[k] = i[k](t);
      return c;
    };
  }

  var reA = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,
      reB = new RegExp(reA.source, "g");

  function zero(b) {
    return function() {
      return b;
    };
  }

  function one(b) {
    return function(t) {
      return b(t) + "";
    };
  }

  function string(a, b) {
    var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b
        am, // current match in a
        bm, // current match in b
        bs, // string preceding current number in b, if any
        i = -1, // index in s
        s = [], // string constants and placeholders
        q = []; // number interpolators

    // Coerce inputs to strings.
    a = a + "", b = b + "";

    // Interpolate pairs of numbers in a & b.
    while ((am = reA.exec(a))
        && (bm = reB.exec(b))) {
      if ((bs = bm.index) > bi) { // a string precedes the next number in b
        bs = b.slice(bi, bs);
        if (s[i]) s[i] += bs; // coalesce with previous string
        else s[++i] = bs;
      }
      if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match
        if (s[i]) s[i] += bm; // coalesce with previous string
        else s[++i] = bm;
      } else { // interpolate non-matching numbers
        s[++i] = null;
        q.push({i: i, x: number(am, bm)});
      }
      bi = reB.lastIndex;
    }

    // Add remains of b.
    if (bi < b.length) {
      bs = b.slice(bi);
      if (s[i]) s[i] += bs; // coalesce with previous string
      else s[++i] = bs;
    }

    // Special optimization for only a single match.
    // Otherwise, interpolate each of the numbers and rejoin the string.
    return s.length < 2 ? (q[0]
        ? one(q[0].x)
        : zero(b))
        : (b = q.length, function(t) {
            for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);
            return s.join("");
          });
  }

  function value(a, b) {
    var t = typeof b, c;
    return b == null || t === "boolean" ? constant$2(b)
        : (t === "number" ? number
        : t === "string" ? ((c = color(b)) ? (b = c, rgb$1) : string)
        : b instanceof color ? rgb$1
        : b instanceof Date ? date
        : Array.isArray(b) ? array$1
        : typeof b.valueOf !== "function" && typeof b.toString !== "function" || isNaN(b) ? object
        : number)(a, b);
  }

  function interpolateRound(a, b) {
    return a = +a, b -= a, function(t) {
      return Math.round(a + b * t);
    };
  }

  var degrees = 180 / Math.PI;

  var rho = Math.SQRT2;

  function constant$3(x) {
    return function() {
      return x;
    };
  }

  function number$1(x) {
    return +x;
  }

  var unit = [0, 1];

  function identity(x) {
    return x;
  }

  function normalize(a, b) {
    return (b -= (a = +a))
        ? function(x) { return (x - a) / b; }
        : constant$3(isNaN(b) ? NaN : 0.5);
  }

  function clamper(domain) {
    var a = domain[0], b = domain[domain.length - 1], t;
    if (a > b) t = a, a = b, b = t;
    return function(x) { return Math.max(a, Math.min(b, x)); };
  }

  // normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].
  // interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].
  function bimap(domain, range, interpolate) {
    var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];
    if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);
    else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);
    return function(x) { return r0(d0(x)); };
  }

  function polymap(domain, range, interpolate) {
    var j = Math.min(domain.length, range.length) - 1,
        d = new Array(j),
        r = new Array(j),
        i = -1;

    // Reverse descending domains.
    if (domain[j] < domain[0]) {
      domain = domain.slice().reverse();
      range = range.slice().reverse();
    }

    while (++i < j) {
      d[i] = normalize(domain[i], domain[i + 1]);
      r[i] = interpolate(range[i], range[i + 1]);
    }

    return function(x) {
      var i = bisectRight(domain, x, 1, j) - 1;
      return r[i](d[i](x));
    };
  }

  function copy(source, target) {
    return target
        .domain(source.domain())
        .range(source.range())
        .interpolate(source.interpolate())
        .clamp(source.clamp())
        .unknown(source.unknown());
  }

  function transformer() {
    var domain = unit,
        range = unit,
        interpolate = value,
        transform,
        untransform,
        unknown,
        clamp = identity,
        piecewise,
        output,
        input;

    function rescale() {
      piecewise = Math.min(domain.length, range.length) > 2 ? polymap : bimap;
      output = input = null;
      return scale;
    }

    function scale(x) {
      return isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));
    }

    scale.invert = function(y) {
      return clamp(untransform((input || (input = piecewise(range, domain.map(transform), number)))(y)));
    };

    scale.domain = function(_) {
      return arguments.length ? (domain = map$1.call(_, number$1), clamp === identity || (clamp = clamper(domain)), rescale()) : domain.slice();
    };

    scale.range = function(_) {
      return arguments.length ? (range = slice.call(_), rescale()) : range.slice();
    };

    scale.rangeRound = function(_) {
      return range = slice.call(_), interpolate = interpolateRound, rescale();
    };

    scale.clamp = function(_) {
      return arguments.length ? (clamp = _ ? clamper(domain) : identity, scale) : clamp !== identity;
    };

    scale.interpolate = function(_) {
      return arguments.length ? (interpolate = _, rescale()) : interpolate;
    };

    scale.unknown = function(_) {
      return arguments.length ? (unknown = _, scale) : unknown;
    };

    return function(t, u) {
      transform = t, untransform = u;
      return rescale();
    };
  }

  function continuous(transform, untransform) {
    return transformer()(transform, untransform);
  }

  // Computes the decimal coefficient and exponent of the specified number x with
  // significant digits p, where x is positive and p is in [1, 21] or undefined.
  // For example, formatDecimal(1.23) returns ["123", 0].
  function formatDecimal(x, p) {
    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf("e")) < 0) return null; // NaN, ±Infinity
    var i, coefficient = x.slice(0, i);

    // The string returned by toExponential either has the form \d\.\d+e[-+]\d+
    // (e.g., 1.2e+3) or the form \de[-+]\d+ (e.g., 1e+3).
    return [
      coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
      +x.slice(i + 1)
    ];
  }

  function exponent(x) {
    return x = formatDecimal(Math.abs(x)), x ? x[1] : NaN;
  }

  function formatGroup(grouping, thousands) {
    return function(value, width) {
      var i = value.length,
          t = [],
          j = 0,
          g = grouping[0],
          length = 0;

      while (i > 0 && g > 0) {
        if (length + g + 1 > width) g = Math.max(1, width - length);
        t.push(value.substring(i -= g, i + g));
        if ((length += g + 1) > width) break;
        g = grouping[j = (j + 1) % grouping.length];
      }

      return t.reverse().join(thousands);
    };
  }

  function formatNumerals(numerals) {
    return function(value) {
      return value.replace(/[0-9]/g, function(i) {
        return numerals[+i];
      });
    };
  }

  // [[fill]align][sign][symbol][0][width][,][.precision][~][type]
  var re = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;

  function formatSpecifier(specifier) {
    return new FormatSpecifier(specifier);
  }

  formatSpecifier.prototype = FormatSpecifier.prototype; // instanceof

  function FormatSpecifier(specifier) {
    if (!(match = re.exec(specifier))) throw new Error("invalid format: " + specifier);
    var match;
    this.fill = match[1] || " ";
    this.align = match[2] || ">";
    this.sign = match[3] || "-";
    this.symbol = match[4] || "";
    this.zero = !!match[5];
    this.width = match[6] && +match[6];
    this.comma = !!match[7];
    this.precision = match[8] && +match[8].slice(1);
    this.trim = !!match[9];
    this.type = match[10] || "";
  }

  FormatSpecifier.prototype.toString = function() {
    return this.fill
        + this.align
        + this.sign
        + this.symbol
        + (this.zero ? "0" : "")
        + (this.width == null ? "" : Math.max(1, this.width | 0))
        + (this.comma ? "," : "")
        + (this.precision == null ? "" : "." + Math.max(0, this.precision | 0))
        + (this.trim ? "~" : "")
        + this.type;
  };

  // Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.
  function formatTrim(s) {
    out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {
      switch (s[i]) {
        case ".": i0 = i1 = i; break;
        case "0": if (i0 === 0) i0 = i; i1 = i; break;
        default: if (i0 > 0) { if (!+s[i]) break out; i0 = 0; } break;
      }
    }
    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;
  }

  var prefixExponent;

  function formatPrefixAuto(x, p) {
    var d = formatDecimal(x, p);
    if (!d) return x + "";
    var coefficient = d[0],
        exponent = d[1],
        i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,
        n = coefficient.length;
    return i === n ? coefficient
        : i > n ? coefficient + new Array(i - n + 1).join("0")
        : i > 0 ? coefficient.slice(0, i) + "." + coefficient.slice(i)
        : "0." + new Array(1 - i).join("0") + formatDecimal(x, Math.max(0, p + i - 1))[0]; // less than 1y!
  }

  function formatRounded(x, p) {
    var d = formatDecimal(x, p);
    if (!d) return x + "";
    var coefficient = d[0],
        exponent = d[1];
    return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient
        : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1)
        : coefficient + new Array(exponent - coefficient.length + 2).join("0");
  }

  var formatTypes = {
    "%": function(x, p) { return (x * 100).toFixed(p); },
    "b": function(x) { return Math.round(x).toString(2); },
    "c": function(x) { return x + ""; },
    "d": function(x) { return Math.round(x).toString(10); },
    "e": function(x, p) { return x.toExponential(p); },
    "f": function(x, p) { return x.toFixed(p); },
    "g": function(x, p) { return x.toPrecision(p); },
    "o": function(x) { return Math.round(x).toString(8); },
    "p": function(x, p) { return formatRounded(x * 100, p); },
    "r": formatRounded,
    "s": formatPrefixAuto,
    "X": function(x) { return Math.round(x).toString(16).toUpperCase(); },
    "x": function(x) { return Math.round(x).toString(16); }
  };

  function identity$1(x) {
    return x;
  }

  var prefixes = ["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];

  function formatLocale(locale) {
    var group = locale.grouping && locale.thousands ? formatGroup(locale.grouping, locale.thousands) : identity$1,
        currency = locale.currency,
        decimal = locale.decimal,
        numerals = locale.numerals ? formatNumerals(locale.numerals) : identity$1,
        percent = locale.percent || "%";

    function newFormat(specifier) {
      specifier = formatSpecifier(specifier);

      var fill = specifier.fill,
          align = specifier.align,
          sign = specifier.sign,
          symbol = specifier.symbol,
          zero = specifier.zero,
          width = specifier.width,
          comma = specifier.comma,
          precision = specifier.precision,
          trim = specifier.trim,
          type = specifier.type;

      // The "n" type is an alias for ",g".
      if (type === "n") comma = true, type = "g";

      // The "" type, and any invalid type, is an alias for ".12~g".
      else if (!formatTypes[type]) precision == null && (precision = 12), trim = true, type = "g";

      // If zero fill is specified, padding goes after sign and before digits.
      if (zero || (fill === "0" && align === "=")) zero = true, fill = "0", align = "=";

      // Compute the prefix and suffix.
      // For SI-prefix, the suffix is lazily computed.
      var prefix = symbol === "$" ? currency[0] : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "",
          suffix = symbol === "$" ? currency[1] : /[%p]/.test(type) ? percent : "";

      // What format function should we use?
      // Is this an integer type?
      // Can this type generate exponential notation?
      var formatType = formatTypes[type],
          maybeSuffix = /[defgprs%]/.test(type);

      // Set the default precision if not specified,
      // or clamp the specified precision to the supported range.
      // For significant precision, it must be in [1, 21].
      // For fixed precision, it must be in [0, 20].
      precision = precision == null ? 6
          : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))
          : Math.max(0, Math.min(20, precision));

      function format(value) {
        var valuePrefix = prefix,
            valueSuffix = suffix,
            i, n, c;

        if (type === "c") {
          valueSuffix = formatType(value) + valueSuffix;
          value = "";
        } else {
          value = +value;

          // Perform the initial formatting.
          var valueNegative = value < 0;
          value = formatType(Math.abs(value), precision);

          // Trim insignificant zeros.
          if (trim) value = formatTrim(value);

          // If a negative value rounds to zero during formatting, treat as positive.
          if (valueNegative && +value === 0) valueNegative = false;

          // Compute the prefix and suffix.
          valuePrefix = (valueNegative ? (sign === "(" ? sign : "-") : sign === "-" || sign === "(" ? "" : sign) + valuePrefix;
          valueSuffix = (type === "s" ? prefixes[8 + prefixExponent / 3] : "") + valueSuffix + (valueNegative && sign === "(" ? ")" : "");

          // Break the formatted value into the integer “value” part that can be
          // grouped, and fractional or exponential “suffix” part that is not.
          if (maybeSuffix) {
            i = -1, n = value.length;
            while (++i < n) {
              if (c = value.charCodeAt(i), 48 > c || c > 57) {
                valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;
                value = value.slice(0, i);
                break;
              }
            }
          }
        }

        // If the fill character is not "0", grouping is applied before padding.
        if (comma && !zero) value = group(value, Infinity);

        // Compute the padding.
        var length = valuePrefix.length + value.length + valueSuffix.length,
            padding = length < width ? new Array(width - length + 1).join(fill) : "";

        // If the fill character is "0", grouping is applied after padding.
        if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";

        // Reconstruct the final output based on the desired alignment.
        switch (align) {
          case "<": value = valuePrefix + value + valueSuffix + padding; break;
          case "=": value = valuePrefix + padding + value + valueSuffix; break;
          case "^": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;
          default: value = padding + valuePrefix + value + valueSuffix; break;
        }

        return numerals(value);
      }

      format.toString = function() {
        return specifier + "";
      };

      return format;
    }

    function formatPrefix(specifier, value) {
      var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = "f", specifier)),
          e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,
          k = Math.pow(10, -e),
          prefix = prefixes[8 + e / 3];
      return function(value) {
        return f(k * value) + prefix;
      };
    }

    return {
      format: newFormat,
      formatPrefix: formatPrefix
    };
  }

  var locale;
  var format;
  var formatPrefix;

  defaultLocale({
    decimal: ".",
    thousands: ",",
    grouping: [3],
    currency: ["$", ""]
  });

  function defaultLocale(definition) {
    locale = formatLocale(definition);
    format = locale.format;
    formatPrefix = locale.formatPrefix;
    return locale;
  }

  function precisionFixed(step) {
    return Math.max(0, -exponent(Math.abs(step)));
  }

  function precisionPrefix(step, value) {
    return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));
  }

  function precisionRound(step, max) {
    step = Math.abs(step), max = Math.abs(max) - step;
    return Math.max(0, exponent(max) - exponent(step)) + 1;
  }

  function tickFormat(start, stop, count, specifier) {
    var step = tickStep(start, stop, count),
        precision;
    specifier = formatSpecifier(specifier == null ? ",f" : specifier);
    switch (specifier.type) {
      case "s": {
        var value = Math.max(Math.abs(start), Math.abs(stop));
        if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;
        return formatPrefix(specifier, value);
      }
      case "":
      case "e":
      case "g":
      case "p":
      case "r": {
        if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === "e");
        break;
      }
      case "f":
      case "%": {
        if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === "%") * 2;
        break;
      }
    }
    return format(specifier);
  }

  function linearish(scale) {
    var domain = scale.domain;

    scale.ticks = function(count) {
      var d = domain();
      return ticks(d[0], d[d.length - 1], count == null ? 10 : count);
    };

    scale.tickFormat = function(count, specifier) {
      var d = domain();
      return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);
    };

    scale.nice = function(count) {
      if (count == null) count = 10;

      var d = domain(),
          i0 = 0,
          i1 = d.length - 1,
          start = d[i0],
          stop = d[i1],
          step;

      if (stop < start) {
        step = start, start = stop, stop = step;
        step = i0, i0 = i1, i1 = step;
      }

      step = tickIncrement(start, stop, count);

      if (step > 0) {
        start = Math.floor(start / step) * step;
        stop = Math.ceil(stop / step) * step;
        step = tickIncrement(start, stop, count);
      } else if (step < 0) {
        start = Math.ceil(start * step) / step;
        stop = Math.floor(stop * step) / step;
        step = tickIncrement(start, stop, count);
      }

      if (step > 0) {
        d[i0] = Math.floor(start / step) * step;
        d[i1] = Math.ceil(stop / step) * step;
        domain(d);
      } else if (step < 0) {
        d[i0] = Math.ceil(start * step) / step;
        d[i1] = Math.floor(stop * step) / step;
        domain(d);
      }

      return scale;
    };

    return scale;
  }

  function linear$1() {
    var scale = continuous(identity, identity);

    scale.copy = function() {
      return copy(scale, linear$1());
    };

    initRange.apply(scale, arguments);

    return linearish(scale);
  }

  function transformPow(exponent) {
    return function(x) {
      return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);
    };
  }

  function transformSqrt(x) {
    return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);
  }

  function transformSquare(x) {
    return x < 0 ? -x * x : x * x;
  }

  function powish(transform) {
    var scale = transform(identity, identity),
        exponent = 1;

    function rescale() {
      return exponent === 1 ? transform(identity, identity)
          : exponent === 0.5 ? transform(transformSqrt, transformSquare)
          : transform(transformPow(exponent), transformPow(1 / exponent));
    }

    scale.exponent = function(_) {
      return arguments.length ? (exponent = +_, rescale()) : exponent;
    };

    return linearish(scale);
  }

  function pow() {
    var scale = powish(transformer());

    scale.copy = function() {
      return copy(scale, pow()).exponent(scale.exponent());
    };

    initRange.apply(scale, arguments);

    return scale;
  }

  function sqrt$1() {
    return pow.apply(null, arguments).exponent(0.5);
  }

  var t0$1 = new Date,
      t1$1 = new Date;

  function newInterval(floori, offseti, count, field) {

    function interval(date) {
      return floori(date = new Date(+date)), date;
    }

    interval.floor = interval;

    interval.ceil = function(date) {
      return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;
    };

    interval.round = function(date) {
      var d0 = interval(date),
          d1 = interval.ceil(date);
      return date - d0 < d1 - date ? d0 : d1;
    };

    interval.offset = function(date, step) {
      return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;
    };

    interval.range = function(start, stop, step) {
      var range = [], previous;
      start = interval.ceil(start);
      step = step == null ? 1 : Math.floor(step);
      if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date
      do range.push(previous = new Date(+start)), offseti(start, step), floori(start);
      while (previous < start && start < stop);
      return range;
    };

    interval.filter = function(test) {
      return newInterval(function(date) {
        if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);
      }, function(date, step) {
        if (date >= date) {
          if (step < 0) while (++step <= 0) {
            while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty
          } else while (--step >= 0) {
            while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty
          }
        }
      });
    };

    if (count) {
      interval.count = function(start, end) {
        t0$1.setTime(+start), t1$1.setTime(+end);
        floori(t0$1), floori(t1$1);
        return Math.floor(count(t0$1, t1$1));
      };

      interval.every = function(step) {
        step = Math.floor(step);
        return !isFinite(step) || !(step > 0) ? null
            : !(step > 1) ? interval
            : interval.filter(field
                ? function(d) { return field(d) % step === 0; }
                : function(d) { return interval.count(0, d) % step === 0; });
      };
    }

    return interval;
  }

  var millisecond = newInterval(function() {
    // noop
  }, function(date, step) {
    date.setTime(+date + step);
  }, function(start, end) {
    return end - start;
  });

  // An optimized implementation for this simple case.
  millisecond.every = function(k) {
    k = Math.floor(k);
    if (!isFinite(k) || !(k > 0)) return null;
    if (!(k > 1)) return millisecond;
    return newInterval(function(date) {
      date.setTime(Math.floor(date / k) * k);
    }, function(date, step) {
      date.setTime(+date + step * k);
    }, function(start, end) {
      return (end - start) / k;
    });
  };
  var milliseconds = millisecond.range;

  var durationSecond = 1e3;
  var durationMinute = 6e4;
  var durationHour = 36e5;
  var durationDay = 864e5;
  var durationWeek = 6048e5;

  var second = newInterval(function(date) {
    date.setTime(date - date.getMilliseconds());
  }, function(date, step) {
    date.setTime(+date + step * durationSecond);
  }, function(start, end) {
    return (end - start) / durationSecond;
  }, function(date) {
    return date.getUTCSeconds();
  });
  var seconds = second.range;

  var minute = newInterval(function(date) {
    date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);
  }, function(date, step) {
    date.setTime(+date + step * durationMinute);
  }, function(start, end) {
    return (end - start) / durationMinute;
  }, function(date) {
    return date.getMinutes();
  });
  var minutes = minute.range;

  var hour = newInterval(function(date) {
    date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);
  }, function(date, step) {
    date.setTime(+date + step * durationHour);
  }, function(start, end) {
    return (end - start) / durationHour;
  }, function(date) {
    return date.getHours();
  });
  var hours = hour.range;

  var day = newInterval(function(date) {
    date.setHours(0, 0, 0, 0);
  }, function(date, step) {
    date.setDate(date.getDate() + step);
  }, function(start, end) {
    return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay;
  }, function(date) {
    return date.getDate() - 1;
  });
  var days = day.range;

  function weekday(i) {
    return newInterval(function(date) {
      date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);
      date.setHours(0, 0, 0, 0);
    }, function(date, step) {
      date.setDate(date.getDate() + step * 7);
    }, function(start, end) {
      return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationWeek;
    });
  }

  var sunday = weekday(0);
  var monday = weekday(1);
  var tuesday = weekday(2);
  var wednesday = weekday(3);
  var thursday = weekday(4);
  var friday = weekday(5);
  var saturday = weekday(6);

  var sundays = sunday.range;
  var mondays = monday.range;
  var thursdays = thursday.range;

  var month = newInterval(function(date) {
    date.setDate(1);
    date.setHours(0, 0, 0, 0);
  }, function(date, step) {
    date.setMonth(date.getMonth() + step);
  }, function(start, end) {
    return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;
  }, function(date) {
    return date.getMonth();
  });
  var months = month.range;

  var year = newInterval(function(date) {
    date.setMonth(0, 1);
    date.setHours(0, 0, 0, 0);
  }, function(date, step) {
    date.setFullYear(date.getFullYear() + step);
  }, function(start, end) {
    return end.getFullYear() - start.getFullYear();
  }, function(date) {
    return date.getFullYear();
  });

  // An optimized implementation for this simple case.
  year.every = function(k) {
    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : newInterval(function(date) {
      date.setFullYear(Math.floor(date.getFullYear() / k) * k);
      date.setMonth(0, 1);
      date.setHours(0, 0, 0, 0);
    }, function(date, step) {
      date.setFullYear(date.getFullYear() + step * k);
    });
  };
  var years = year.range;

  var utcMinute = newInterval(function(date) {
    date.setUTCSeconds(0, 0);
  }, function(date, step) {
    date.setTime(+date + step * durationMinute);
  }, function(start, end) {
    return (end - start) / durationMinute;
  }, function(date) {
    return date.getUTCMinutes();
  });
  var utcMinutes = utcMinute.range;

  var utcHour = newInterval(function(date) {
    date.setUTCMinutes(0, 0, 0);
  }, function(date, step) {
    date.setTime(+date + step * durationHour);
  }, function(start, end) {
    return (end - start) / durationHour;
  }, function(date) {
    return date.getUTCHours();
  });
  var utcHours = utcHour.range;

  var utcDay = newInterval(function(date) {
    date.setUTCHours(0, 0, 0, 0);
  }, function(date, step) {
    date.setUTCDate(date.getUTCDate() + step);
  }, function(start, end) {
    return (end - start) / durationDay;
  }, function(date) {
    return date.getUTCDate() - 1;
  });
  var utcDays = utcDay.range;

  function utcWeekday(i) {
    return newInterval(function(date) {
      date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);
      date.setUTCHours(0, 0, 0, 0);
    }, function(date, step) {
      date.setUTCDate(date.getUTCDate() + step * 7);
    }, function(start, end) {
      return (end - start) / durationWeek;
    });
  }

  var utcSunday = utcWeekday(0);
  var utcMonday = utcWeekday(1);
  var utcTuesday = utcWeekday(2);
  var utcWednesday = utcWeekday(3);
  var utcThursday = utcWeekday(4);
  var utcFriday = utcWeekday(5);
  var utcSaturday = utcWeekday(6);

  var utcSundays = utcSunday.range;
  var utcMondays = utcMonday.range;
  var utcThursdays = utcThursday.range;

  var utcMonth = newInterval(function(date) {
    date.setUTCDate(1);
    date.setUTCHours(0, 0, 0, 0);
  }, function(date, step) {
    date.setUTCMonth(date.getUTCMonth() + step);
  }, function(start, end) {
    return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;
  }, function(date) {
    return date.getUTCMonth();
  });
  var utcMonths = utcMonth.range;

  var utcYear = newInterval(function(date) {
    date.setUTCMonth(0, 1);
    date.setUTCHours(0, 0, 0, 0);
  }, function(date, step) {
    date.setUTCFullYear(date.getUTCFullYear() + step);
  }, function(start, end) {
    return end.getUTCFullYear() - start.getUTCFullYear();
  }, function(date) {
    return date.getUTCFullYear();
  });

  // An optimized implementation for this simple case.
  utcYear.every = function(k) {
    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : newInterval(function(date) {
      date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);
      date.setUTCMonth(0, 1);
      date.setUTCHours(0, 0, 0, 0);
    }, function(date, step) {
      date.setUTCFullYear(date.getUTCFullYear() + step * k);
    });
  };
  var utcYears = utcYear.range;

  function localDate(d) {
    if (0 <= d.y && d.y < 100) {
      var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);
      date.setFullYear(d.y);
      return date;
    }
    return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);
  }

  function utcDate(d) {
    if (0 <= d.y && d.y < 100) {
      var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));
      date.setUTCFullYear(d.y);
      return date;
    }
    return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));
  }

  function newYear(y) {
    return {y: y, m: 0, d: 1, H: 0, M: 0, S: 0, L: 0};
  }

  function formatLocale$1(locale) {
    var locale_dateTime = locale.dateTime,
        locale_date = locale.date,
        locale_time = locale.time,
        locale_periods = locale.periods,
        locale_weekdays = locale.days,
        locale_shortWeekdays = locale.shortDays,
        locale_months = locale.months,
        locale_shortMonths = locale.shortMonths;

    var periodRe = formatRe(locale_periods),
        periodLookup = formatLookup(locale_periods),
        weekdayRe = formatRe(locale_weekdays),
        weekdayLookup = formatLookup(locale_weekdays),
        shortWeekdayRe = formatRe(locale_shortWeekdays),
        shortWeekdayLookup = formatLookup(locale_shortWeekdays),
        monthRe = formatRe(locale_months),
        monthLookup = formatLookup(locale_months),
        shortMonthRe = formatRe(locale_shortMonths),
        shortMonthLookup = formatLookup(locale_shortMonths);

    var formats = {
      "a": formatShortWeekday,
      "A": formatWeekday,
      "b": formatShortMonth,
      "B": formatMonth,
      "c": null,
      "d": formatDayOfMonth,
      "e": formatDayOfMonth,
      "f": formatMicroseconds,
      "H": formatHour24,
      "I": formatHour12,
      "j": formatDayOfYear,
      "L": formatMilliseconds,
      "m": formatMonthNumber,
      "M": formatMinutes,
      "p": formatPeriod,
      "Q": formatUnixTimestamp,
      "s": formatUnixTimestampSeconds,
      "S": formatSeconds,
      "u": formatWeekdayNumberMonday,
      "U": formatWeekNumberSunday,
      "V": formatWeekNumberISO,
      "w": formatWeekdayNumberSunday,
      "W": formatWeekNumberMonday,
      "x": null,
      "X": null,
      "y": formatYear,
      "Y": formatFullYear,
      "Z": formatZone,
      "%": formatLiteralPercent
    };

    var utcFormats = {
      "a": formatUTCShortWeekday,
      "A": formatUTCWeekday,
      "b": formatUTCShortMonth,
      "B": formatUTCMonth,
      "c": null,
      "d": formatUTCDayOfMonth,
      "e": formatUTCDayOfMonth,
      "f": formatUTCMicroseconds,
      "H": formatUTCHour24,
      "I": formatUTCHour12,
      "j": formatUTCDayOfYear,
      "L": formatUTCMilliseconds,
      "m": formatUTCMonthNumber,
      "M": formatUTCMinutes,
      "p": formatUTCPeriod,
      "Q": formatUnixTimestamp,
      "s": formatUnixTimestampSeconds,
      "S": formatUTCSeconds,
      "u": formatUTCWeekdayNumberMonday,
      "U": formatUTCWeekNumberSunday,
      "V": formatUTCWeekNumberISO,
      "w": formatUTCWeekdayNumberSunday,
      "W": formatUTCWeekNumberMonday,
      "x": null,
      "X": null,
      "y": formatUTCYear,
      "Y": formatUTCFullYear,
      "Z": formatUTCZone,
      "%": formatLiteralPercent
    };

    var parses = {
      "a": parseShortWeekday,
      "A": parseWeekday,
      "b": parseShortMonth,
      "B": parseMonth,
      "c": parseLocaleDateTime,
      "d": parseDayOfMonth,
      "e": parseDayOfMonth,
      "f": parseMicroseconds,
      "H": parseHour24,
      "I": parseHour24,
      "j": parseDayOfYear,
      "L": parseMilliseconds,
      "m": parseMonthNumber,
      "M": parseMinutes,
      "p": parsePeriod,
      "Q": parseUnixTimestamp,
      "s": parseUnixTimestampSeconds,
      "S": parseSeconds,
      "u": parseWeekdayNumberMonday,
      "U": parseWeekNumberSunday,
      "V": parseWeekNumberISO,
      "w": parseWeekdayNumberSunday,
      "W": parseWeekNumberMonday,
      "x": parseLocaleDate,
      "X": parseLocaleTime,
      "y": parseYear,
      "Y": parseFullYear,
      "Z": parseZone,
      "%": parseLiteralPercent
    };

    // These recursive directive definitions must be deferred.
    formats.x = newFormat(locale_date, formats);
    formats.X = newFormat(locale_time, formats);
    formats.c = newFormat(locale_dateTime, formats);
    utcFormats.x = newFormat(locale_date, utcFormats);
    utcFormats.X = newFormat(locale_time, utcFormats);
    utcFormats.c = newFormat(locale_dateTime, utcFormats);

    function newFormat(specifier, formats) {
      return function(date) {
        var string = [],
            i = -1,
            j = 0,
            n = specifier.length,
            c,
            pad,
            format;

        if (!(date instanceof Date)) date = new Date(+date);

        while (++i < n) {
          if (specifier.charCodeAt(i) === 37) {
            string.push(specifier.slice(j, i));
            if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);
            else pad = c === "e" ? " " : "0";
            if (format = formats[c]) c = format(date, pad);
            string.push(c);
            j = i + 1;
          }
        }

        string.push(specifier.slice(j, i));
        return string.join("");
      };
    }

    function newParse(specifier, newDate) {
      return function(string) {
        var d = newYear(1900),
            i = parseSpecifier(d, specifier, string += "", 0),
            week, day$1;
        if (i != string.length) return null;

        // If a UNIX timestamp is specified, return it.
        if ("Q" in d) return new Date(d.Q);

        // The am-pm flag is 0 for AM, and 1 for PM.
        if ("p" in d) d.H = d.H % 12 + d.p * 12;

        // Convert day-of-week and week-of-year to day-of-year.
        if ("V" in d) {
          if (d.V < 1 || d.V > 53) return null;
          if (!("w" in d)) d.w = 1;
          if ("Z" in d) {
            week = utcDate(newYear(d.y)), day$1 = week.getUTCDay();
            week = day$1 > 4 || day$1 === 0 ? utcMonday.ceil(week) : utcMonday(week);
            week = utcDay.offset(week, (d.V - 1) * 7);
            d.y = week.getUTCFullYear();
            d.m = week.getUTCMonth();
            d.d = week.getUTCDate() + (d.w + 6) % 7;
          } else {
            week = newDate(newYear(d.y)), day$1 = week.getDay();
            week = day$1 > 4 || day$1 === 0 ? monday.ceil(week) : monday(week);
            week = day.offset(week, (d.V - 1) * 7);
            d.y = week.getFullYear();
            d.m = week.getMonth();
            d.d = week.getDate() + (d.w + 6) % 7;
          }
        } else if ("W" in d || "U" in d) {
          if (!("w" in d)) d.w = "u" in d ? d.u % 7 : "W" in d ? 1 : 0;
          day$1 = "Z" in d ? utcDate(newYear(d.y)).getUTCDay() : newDate(newYear(d.y)).getDay();
          d.m = 0;
          d.d = "W" in d ? (d.w + 6) % 7 + d.W * 7 - (day$1 + 5) % 7 : d.w + d.U * 7 - (day$1 + 6) % 7;
        }

        // If a time zone is specified, all fields are interpreted as UTC and then
        // offset according to the specified time zone.
        if ("Z" in d) {
          d.H += d.Z / 100 | 0;
          d.M += d.Z % 100;
          return utcDate(d);
        }

        // Otherwise, all fields are in local time.
        return newDate(d);
      };
    }

    function parseSpecifier(d, specifier, string, j) {
      var i = 0,
          n = specifier.length,
          m = string.length,
          c,
          parse;

      while (i < n) {
        if (j >= m) return -1;
        c = specifier.charCodeAt(i++);
        if (c === 37) {
          c = specifier.charAt(i++);
          parse = parses[c in pads ? specifier.charAt(i++) : c];
          if (!parse || ((j = parse(d, string, j)) < 0)) return -1;
        } else if (c != string.charCodeAt(j++)) {
          return -1;
        }
      }

      return j;
    }

    function parsePeriod(d, string, i) {
      var n = periodRe.exec(string.slice(i));
      return n ? (d.p = periodLookup[n[0].toLowerCase()], i + n[0].length) : -1;
    }

    function parseShortWeekday(d, string, i) {
      var n = shortWeekdayRe.exec(string.slice(i));
      return n ? (d.w = shortWeekdayLookup[n[0].toLowerCase()], i + n[0].length) : -1;
    }

    function parseWeekday(d, string, i) {
      var n = weekdayRe.exec(string.slice(i));
      return n ? (d.w = weekdayLookup[n[0].toLowerCase()], i + n[0].length) : -1;
    }

    function parseShortMonth(d, string, i) {
      var n = shortMonthRe.exec(string.slice(i));
      return n ? (d.m = shortMonthLookup[n[0].toLowerCase()], i + n[0].length) : -1;
    }

    function parseMonth(d, string, i) {
      var n = monthRe.exec(string.slice(i));
      return n ? (d.m = monthLookup[n[0].toLowerCase()], i + n[0].length) : -1;
    }

    function parseLocaleDateTime(d, string, i) {
      return parseSpecifier(d, locale_dateTime, string, i);
    }

    function parseLocaleDate(d, string, i) {
      return parseSpecifier(d, locale_date, string, i);
    }

    function parseLocaleTime(d, string, i) {
      return parseSpecifier(d, locale_time, string, i);
    }

    function formatShortWeekday(d) {
      return locale_shortWeekdays[d.getDay()];
    }

    function formatWeekday(d) {
      return locale_weekdays[d.getDay()];
    }

    function formatShortMonth(d) {
      return locale_shortMonths[d.getMonth()];
    }

    function formatMonth(d) {
      return locale_months[d.getMonth()];
    }

    function formatPeriod(d) {
      return locale_periods[+(d.getHours() >= 12)];
    }

    function formatUTCShortWeekday(d) {
      return locale_shortWeekdays[d.getUTCDay()];
    }

    function formatUTCWeekday(d) {
      return locale_weekdays[d.getUTCDay()];
    }

    function formatUTCShortMonth(d) {
      return locale_shortMonths[d.getUTCMonth()];
    }

    function formatUTCMonth(d) {
      return locale_months[d.getUTCMonth()];
    }

    function formatUTCPeriod(d) {
      return locale_periods[+(d.getUTCHours() >= 12)];
    }

    return {
      format: function(specifier) {
        var f = newFormat(specifier += "", formats);
        f.toString = function() { return specifier; };
        return f;
      },
      parse: function(specifier) {
        var p = newParse(specifier += "", localDate);
        p.toString = function() { return specifier; };
        return p;
      },
      utcFormat: function(specifier) {
        var f = newFormat(specifier += "", utcFormats);
        f.toString = function() { return specifier; };
        return f;
      },
      utcParse: function(specifier) {
        var p = newParse(specifier, utcDate);
        p.toString = function() { return specifier; };
        return p;
      }
    };
  }

  var pads = {"-": "", "_": " ", "0": "0"},
      numberRe = /^\s*\d+/, // note: ignores next directive
      percentRe = /^%/,
      requoteRe = /[\\^$*+?|[\]().{}]/g;

  function pad(value, fill, width) {
    var sign = value < 0 ? "-" : "",
        string = (sign ? -value : value) + "",
        length = string.length;
    return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);
  }

  function requote(s) {
    return s.replace(requoteRe, "\\$&");
  }

  function formatRe(names) {
    return new RegExp("^(?:" + names.map(requote).join("|") + ")", "i");
  }

  function formatLookup(names) {
    var map = {}, i = -1, n = names.length;
    while (++i < n) map[names[i].toLowerCase()] = i;
    return map;
  }

  function parseWeekdayNumberSunday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 1));
    return n ? (d.w = +n[0], i + n[0].length) : -1;
  }

  function parseWeekdayNumberMonday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 1));
    return n ? (d.u = +n[0], i + n[0].length) : -1;
  }

  function parseWeekNumberSunday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.U = +n[0], i + n[0].length) : -1;
  }

  function parseWeekNumberISO(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.V = +n[0], i + n[0].length) : -1;
  }

  function parseWeekNumberMonday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.W = +n[0], i + n[0].length) : -1;
  }

  function parseFullYear(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 4));
    return n ? (d.y = +n[0], i + n[0].length) : -1;
  }

  function parseYear(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;
  }

  function parseZone(d, string, i) {
    var n = /^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(string.slice(i, i + 6));
    return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || "00")), i + n[0].length) : -1;
  }

  function parseMonthNumber(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.m = n[0] - 1, i + n[0].length) : -1;
  }

  function parseDayOfMonth(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.d = +n[0], i + n[0].length) : -1;
  }

  function parseDayOfYear(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 3));
    return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;
  }

  function parseHour24(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.H = +n[0], i + n[0].length) : -1;
  }

  function parseMinutes(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.M = +n[0], i + n[0].length) : -1;
  }

  function parseSeconds(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.S = +n[0], i + n[0].length) : -1;
  }

  function parseMilliseconds(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 3));
    return n ? (d.L = +n[0], i + n[0].length) : -1;
  }

  function parseMicroseconds(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 6));
    return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;
  }

  function parseLiteralPercent(d, string, i) {
    var n = percentRe.exec(string.slice(i, i + 1));
    return n ? i + n[0].length : -1;
  }

  function parseUnixTimestamp(d, string, i) {
    var n = numberRe.exec(string.slice(i));
    return n ? (d.Q = +n[0], i + n[0].length) : -1;
  }

  function parseUnixTimestampSeconds(d, string, i) {
    var n = numberRe.exec(string.slice(i));
    return n ? (d.Q = (+n[0]) * 1000, i + n[0].length) : -1;
  }

  function formatDayOfMonth(d, p) {
    return pad(d.getDate(), p, 2);
  }

  function formatHour24(d, p) {
    return pad(d.getHours(), p, 2);
  }

  function formatHour12(d, p) {
    return pad(d.getHours() % 12 || 12, p, 2);
  }

  function formatDayOfYear(d, p) {
    return pad(1 + day.count(year(d), d), p, 3);
  }

  function formatMilliseconds(d, p) {
    return pad(d.getMilliseconds(), p, 3);
  }

  function formatMicroseconds(d, p) {
    return formatMilliseconds(d, p) + "000";
  }

  function formatMonthNumber(d, p) {
    return pad(d.getMonth() + 1, p, 2);
  }

  function formatMinutes(d, p) {
    return pad(d.getMinutes(), p, 2);
  }

  function formatSeconds(d, p) {
    return pad(d.getSeconds(), p, 2);
  }

  function formatWeekdayNumberMonday(d) {
    var day = d.getDay();
    return day === 0 ? 7 : day;
  }

  function formatWeekNumberSunday(d, p) {
    return pad(sunday.count(year(d), d), p, 2);
  }

  function formatWeekNumberISO(d, p) {
    var day = d.getDay();
    d = (day >= 4 || day === 0) ? thursday(d) : thursday.ceil(d);
    return pad(thursday.count(year(d), d) + (year(d).getDay() === 4), p, 2);
  }

  function formatWeekdayNumberSunday(d) {
    return d.getDay();
  }

  function formatWeekNumberMonday(d, p) {
    return pad(monday.count(year(d), d), p, 2);
  }

  function formatYear(d, p) {
    return pad(d.getFullYear() % 100, p, 2);
  }

  function formatFullYear(d, p) {
    return pad(d.getFullYear() % 10000, p, 4);
  }

  function formatZone(d) {
    var z = d.getTimezoneOffset();
    return (z > 0 ? "-" : (z *= -1, "+"))
        + pad(z / 60 | 0, "0", 2)
        + pad(z % 60, "0", 2);
  }

  function formatUTCDayOfMonth(d, p) {
    return pad(d.getUTCDate(), p, 2);
  }

  function formatUTCHour24(d, p) {
    return pad(d.getUTCHours(), p, 2);
  }

  function formatUTCHour12(d, p) {
    return pad(d.getUTCHours() % 12 || 12, p, 2);
  }

  function formatUTCDayOfYear(d, p) {
    return pad(1 + utcDay.count(utcYear(d), d), p, 3);
  }

  function formatUTCMilliseconds(d, p) {
    return pad(d.getUTCMilliseconds(), p, 3);
  }

  function formatUTCMicroseconds(d, p) {
    return formatUTCMilliseconds(d, p) + "000";
  }

  function formatUTCMonthNumber(d, p) {
    return pad(d.getUTCMonth() + 1, p, 2);
  }

  function formatUTCMinutes(d, p) {
    return pad(d.getUTCMinutes(), p, 2);
  }

  function formatUTCSeconds(d, p) {
    return pad(d.getUTCSeconds(), p, 2);
  }

  function formatUTCWeekdayNumberMonday(d) {
    var dow = d.getUTCDay();
    return dow === 0 ? 7 : dow;
  }

  function formatUTCWeekNumberSunday(d, p) {
    return pad(utcSunday.count(utcYear(d), d), p, 2);
  }

  function formatUTCWeekNumberISO(d, p) {
    var day = d.getUTCDay();
    d = (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);
    return pad(utcThursday.count(utcYear(d), d) + (utcYear(d).getUTCDay() === 4), p, 2);
  }

  function formatUTCWeekdayNumberSunday(d) {
    return d.getUTCDay();
  }

  function formatUTCWeekNumberMonday(d, p) {
    return pad(utcMonday.count(utcYear(d), d), p, 2);
  }

  function formatUTCYear(d, p) {
    return pad(d.getUTCFullYear() % 100, p, 2);
  }

  function formatUTCFullYear(d, p) {
    return pad(d.getUTCFullYear() % 10000, p, 4);
  }

  function formatUTCZone() {
    return "+0000";
  }

  function formatLiteralPercent() {
    return "%";
  }

  function formatUnixTimestamp(d) {
    return +d;
  }

  function formatUnixTimestampSeconds(d) {
    return Math.floor(+d / 1000);
  }

  var locale$1;
  var timeFormat;
  var timeParse;
  var utcFormat;
  var utcParse;

  defaultLocale$1({
    dateTime: "%x, %X",
    date: "%-m/%-d/%Y",
    time: "%-I:%M:%S %p",
    periods: ["AM", "PM"],
    days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
    shortDays: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
    months: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
    shortMonths: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
  });

  function defaultLocale$1(definition) {
    locale$1 = formatLocale$1(definition);
    timeFormat = locale$1.format;
    timeParse = locale$1.parse;
    utcFormat = locale$1.utcFormat;
    utcParse = locale$1.utcParse;
    return locale$1;
  }

  var isoSpecifier = "%Y-%m-%dT%H:%M:%S.%LZ";

  function formatIsoNative(date) {
    return date.toISOString();
  }

  var formatIso = Date.prototype.toISOString
      ? formatIsoNative
      : utcFormat(isoSpecifier);

  function parseIsoNative(string) {
    var date = new Date(string);
    return isNaN(date) ? null : date;
  }

  var parseIso = +new Date("2000-01-01T00:00:00.000Z")
      ? parseIsoNative
      : utcParse(isoSpecifier);

  /*!
   * bytes
   * Copyright(c) 2012-2014 TJ Holowaychuk
   * Copyright(c) 2015 Jed Watson
   * MIT Licensed
   */
  var format_1 = format$1;

  /**
   * Module variables.
   * @private
   */

  var formatThousandsRegExp = /\B(?=(\d{3})+(?!\d))/g;

  var formatDecimalsRegExp = /(?:\.0*|(\.[^0]+)0+)$/;

  var map$2 = {
    b:  1,
    kb: 1 << 10,
    mb: 1 << 20,
    gb: 1 << 30,
    tb: Math.pow(1024, 4),
    pb: Math.pow(1024, 5),
  };

  /**
   * Format the given value in bytes into a string.
   *
   * If the value is negative, it is kept as such. If it is a float,
   * it is rounded.
   *
   * @param {number} value
   * @param {object} [options]
   * @param {number} [options.decimalPlaces=2]
   * @param {number} [options.fixedDecimals=false]
   * @param {string} [options.thousandsSeparator=]
   * @param {string} [options.unit=]
   * @param {string} [options.unitSeparator=]
   *
   * @returns {string|null}
   * @public
   */

  function format$1(value, options) {
    if (!Number.isFinite(value)) {
      return null;
    }

    var mag = Math.abs(value);
    var thousandsSeparator = (options && options.thousandsSeparator) || '';
    var unitSeparator = (options && options.unitSeparator) || '';
    var decimalPlaces = (options && options.decimalPlaces !== undefined) ? options.decimalPlaces : 2;
    var fixedDecimals = Boolean(options && options.fixedDecimals);
    var unit = (options && options.unit) || '';

    if (!unit || !map$2[unit.toLowerCase()]) {
      if (mag >= map$2.pb) {
        unit = 'PB';
      } else if (mag >= map$2.tb) {
        unit = 'TB';
      } else if (mag >= map$2.gb) {
        unit = 'GB';
      } else if (mag >= map$2.mb) {
        unit = 'MB';
      } else if (mag >= map$2.kb) {
        unit = 'KB';
      } else {
        unit = 'B';
      }
    }

    var val = value / map$2[unit.toLowerCase()];
    var str = val.toFixed(decimalPlaces);

    if (!fixedDecimals) {
      str = str.replace(formatDecimalsRegExp, '$1');
    }

    if (thousandsSeparator) {
      str = str.replace(formatThousandsRegExp, thousandsSeparator);
    }

    return str + unitSeparator + unit;
  }

  const WIDTH = 700;
  const HEIGHT = 700;
  const RADIUS = Math.min(WIDTH, HEIGHT) / 2 - 10;

  function getAncestors(node) {
    const parents = [];
    while (node != null) {
      parents.push(node);
      node = node.parent;
    }
    return parents;
  }

  function color$1(node) {
    if (node.children && node.children.length) {
      const parents = getAncestors(node);
      const hasNodeModules = !!parents.find(n => {
        return n.data.name === "node_modules";
      });
      return hasNodeModules ? "#599e59" : "#487ea4";
    } else {
      return "#db7100";
    }
  }

  const x = linear$1().range([0, 2 * Math.PI]);
  const y = sqrt$1().range([0, RADIUS]);

  const chartsContainer = document.querySelector("#charts");

  window.nodesData.forEach(({ id, root: data }) => {
    const wrapper = document.createElement("div");
    wrapper.innerHTML = `
    <div class="chart">
      <h3>${id}</h3>
      <div class="details" style="display: none;">
        <div class="details-name" ></div>
        <div class="details-percentage" ></div>
        of bundle size
        <div class="details-size" ></div>
      </div>
    </div>
    `;
    const chartNode = wrapper.querySelector(".chart");
    chartsContainer.appendChild(chartNode);

    const g = select(chartNode)
      .append("svg")
      .attr("width", WIDTH)
      .attr("height", HEIGHT)
      .append("g")
      .attr("transform", "translate(" + WIDTH / 2 + "," + HEIGHT / 2 + ")");

    const arc = d3arc()
      .startAngle(d => Math.max(0, Math.min(2 * Math.PI, x(d.x0))))
      .endAngle(d => Math.max(0, Math.min(2 * Math.PI, x(d.x1))))
      .innerRadius(d => y(d.y0))
      .outerRadius(d => y(d.y1));

    const root = hierarchy(data)
      .sum(d => {
        if (d.children && d.children.length) {
          return 0;
        } else {
          return d.size;
        }
      })
      .sort();

    const partition = d3partition();

    partition(root);

    g.selectAll("path")
      .data(partition(root).descendants())
      .enter()
      .append("path")

      .attr("d", arc)
      .attr("fill-rule", "evenodd")
      .style("stroke", "#fff")
      .style("fill", d => color$1(d))
      .on("mouseover", mouseover);

    const totalSize = root.value;

    select(chartNode).on("mouseleave", mouseleave);

    function mouseover(d) {
      const percentageNum = (100 * d.value) / totalSize;
      const percentage = percentageNum.toFixed(2);
      const percentageString = percentage + "%";

      select(chartNode)
        .select(".details-name")
        .text(d.data.name);

      select(chartNode)
        .select(".details-percentage")
        .text(percentageString);

      select(chartNode)
        .select(".details-size")
        .text(format_1(d.value));

      select(chartNode)
        .select(".details")
        .style("display", "block");

      const sequenceArray = getAncestors(d);
      //updateBreadcrumbs(sequenceArray, percentageString);

      // Fade all the segments.
      g.selectAll("path").style("opacity", 0.3);

      // Then highlight only those that are an ancestor of the current segment.
      g.selectAll("path")
        .filter(node => sequenceArray.indexOf(node) >= 0)
        .style("opacity", 1);
    }

    function mouseleave() {
      g.selectAll("path").style("opacity", 1);

      select(".details").style("display", "none");
    }
  });

}());

      </script>
  