{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   *\n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation matches the working solution from test-blazeface-integration.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        return;\n      }\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size to match video dimensions when available\n      const updateCanvasSize = () => {\n        if (video.videoWidth && video.videoHeight) {\n          canvas.width = video.videoWidth;\n          canvas.height = video.videoHeight;\n          canvas.style.width = '100%';\n          canvas.style.height = '100%';\n          console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);\n        } else {\n          // Fallback to provided dimensions\n          canvas.width = width;\n          canvas.height = height;\n          console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);\n        }\n      };\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Helper function to load scripts\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n\n      // Load TensorFlow.js and BlazeFace model - matching working test implementation\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await window.blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Update canvas size once video is ready\n          updateCanvasSize();\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Check if video has valid dimensions\n          if (!video.videoWidth || !video.videoHeight) {\n            rafRef.current = requestAnimationFrame(detectLoop);\n            return;\n          }\n\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces with same confidence threshold as working test\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas and draw the original video frame first\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            setFaceCount(predictions.length);\n\n            // Process each detected face - REAL-TIME BLURRING like in test\n            predictions.forEach((prediction, index) => {\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect)\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply REAL-TIME elliptical blur - this creates the live preview blur\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(25px)'; // Stronger blur for better privacy\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n\n              // Add debug rectangle to show detection area (optional)\n              if (__DEV__) {\n                ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n                ctx.lineWidth = 2;\n                ctx.strokeRect(minX, minY, faceWidth, faceHeight);\n              }\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Wait for video to be ready before starting\n      const waitForVideoAndStart = () => {\n        if (video.readyState >= 2) {\n          // HAVE_CURRENT_DATA\n          loadModel();\n        } else {\n          video.addEventListener('loadeddata', loadModel, {\n            once: true\n          });\n        }\n      };\n\n      // Start the process\n      waitForVideoAndStart();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 15,\n          // Higher z-index to be above video\n          objectFit: 'cover',\n          backgroundColor: 'transparent'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(59, 130, 246, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(59, 130, 246, 0.3)'\n        },\n        children: \"Loading BlazeFace model...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(16, 185, 129, 0.3)'\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), !isLoading && faceCount === 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(107, 114, 128, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(107, 114, 128, 0.3)'\n        },\n        children: \"\\uD83D\\uDC40 Looking for faces...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 309, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 9, 0], [12, 0, 10, 0], [13, 0, 11, 0], [14, 0, 12, 0], [15, 0, 13, 0], [16, 0, 14, 0], [17, 0, 15, 0], [18, 2, 16, 15], [18, 11, 16, 24, "BlazeFaceCanvas"], [18, 26, 16, 39, "BlazeFaceCanvas"], [18, 27, 16, 40], [19, 4, 16, 42, "containerId"], [19, 15, 16, 53], [20, 4, 16, 55, "width"], [20, 9, 16, 60], [21, 4, 16, 62, "height"], [22, 2, 16, 91], [22, 3, 16, 92], [22, 5, 16, 94], [23, 4, 16, 94, "_s"], [23, 6, 16, 94], [24, 4, 17, 2], [24, 10, 17, 8, "canvasRef"], [24, 19, 17, 17], [24, 22, 17, 20], [24, 26, 17, 20, "useRef"], [24, 39, 17, 26], [24, 41, 17, 53], [24, 45, 17, 57], [24, 46, 17, 58], [25, 4, 18, 2], [25, 10, 18, 8, "rafRef"], [25, 16, 18, 14], [25, 19, 18, 17], [25, 23, 18, 17, "useRef"], [25, 36, 18, 23], [25, 38, 18, 39], [25, 42, 18, 43], [25, 43, 18, 44], [26, 4, 19, 2], [26, 10, 19, 8, "modelRef"], [26, 18, 19, 16], [26, 21, 19, 19], [26, 25, 19, 19, "useRef"], [26, 38, 19, 25], [26, 40, 19, 38], [26, 44, 19, 42], [26, 45, 19, 43], [27, 4, 20, 2], [27, 10, 20, 8], [27, 11, 20, 9, "isLoading"], [27, 20, 20, 18], [27, 22, 20, 20, "setIsLoading"], [27, 34, 20, 32], [27, 35, 20, 33], [27, 38, 20, 36], [27, 42, 20, 36, "useState"], [27, 57, 20, 44], [27, 59, 20, 45], [27, 63, 20, 49], [27, 64, 20, 50], [28, 4, 21, 2], [28, 10, 21, 8], [28, 11, 21, 9, "faceCount"], [28, 20, 21, 18], [28, 22, 21, 20, "setFaceCount"], [28, 34, 21, 32], [28, 35, 21, 33], [28, 38, 21, 36], [28, 42, 21, 36, "useState"], [28, 57, 21, 44], [28, 59, 21, 45], [28, 60, 21, 46], [28, 61, 21, 47], [29, 4, 23, 2], [29, 8, 23, 2, "useEffect"], [29, 24, 23, 11], [29, 26, 23, 12], [29, 32, 23, 18], [30, 6, 24, 4, "console"], [30, 13, 24, 11], [30, 14, 24, 12, "log"], [30, 17, 24, 15], [30, 18, 24, 16], [30, 64, 24, 62], [30, 66, 24, 64], [31, 8, 24, 66, "containerId"], [31, 19, 24, 77], [32, 8, 24, 79, "width"], [32, 13, 24, 84], [33, 8, 24, 86, "height"], [34, 6, 24, 93], [34, 7, 24, 94], [34, 8, 24, 95], [35, 6, 26, 4], [35, 12, 26, 10, "container"], [35, 21, 26, 19], [35, 24, 26, 22, "document"], [35, 32, 26, 30], [35, 33, 26, 31, "getElementById"], [35, 47, 26, 45], [35, 48, 26, 46, "containerId"], [35, 59, 26, 57], [35, 60, 26, 58], [36, 6, 27, 4], [36, 10, 27, 8], [36, 11, 27, 9, "container"], [36, 20, 27, 18], [36, 22, 27, 20], [37, 8, 28, 6, "console"], [37, 15, 28, 13], [37, 16, 28, 14, "error"], [37, 21, 28, 19], [37, 22, 28, 20], [37, 62, 28, 60], [37, 64, 28, 62, "containerId"], [37, 75, 28, 73], [37, 76, 28, 74], [38, 8, 29, 6], [39, 6, 30, 4], [40, 6, 32, 4], [40, 12, 32, 10, "video"], [40, 17, 32, 40], [40, 20, 32, 43, "container"], [40, 29, 32, 52], [40, 30, 32, 53, "querySelector"], [40, 43, 32, 66], [40, 44, 32, 67], [40, 51, 32, 74], [40, 52, 32, 75], [41, 6, 33, 4], [41, 10, 33, 8], [41, 11, 33, 9, "video"], [41, 16, 33, 14], [41, 18, 33, 16], [42, 8, 34, 6, "console"], [42, 15, 34, 13], [42, 16, 34, 14, "error"], [42, 21, 34, 19], [42, 22, 34, 20], [42, 78, 34, 76], [42, 79, 34, 77], [43, 8, 35, 6], [44, 6, 36, 4], [45, 6, 38, 4], [45, 12, 38, 10, "canvas"], [45, 18, 38, 16], [45, 21, 38, 19, "canvasRef"], [45, 30, 38, 28], [45, 31, 38, 29, "current"], [45, 38, 38, 36], [46, 6, 39, 4], [46, 10, 39, 8], [46, 11, 39, 9, "canvas"], [46, 17, 39, 15], [46, 19, 39, 17], [47, 8, 40, 6, "console"], [47, 15, 40, 13], [47, 16, 40, 14, "error"], [47, 21, 40, 19], [47, 22, 40, 20], [47, 66, 40, 64], [47, 67, 40, 65], [48, 8, 41, 6], [49, 6, 42, 4], [51, 6, 44, 4], [52, 6, 45, 4], [52, 12, 45, 10, "updateCanvasSize"], [52, 28, 45, 26], [52, 31, 45, 29, "updateCanvasSize"], [52, 32, 45, 29], [52, 37, 45, 35], [53, 8, 46, 6], [53, 12, 46, 10, "video"], [53, 17, 46, 15], [53, 18, 46, 16, "videoWidth"], [53, 28, 46, 26], [53, 32, 46, 30, "video"], [53, 37, 46, 35], [53, 38, 46, 36, "videoHeight"], [53, 49, 46, 47], [53, 51, 46, 49], [54, 10, 47, 8, "canvas"], [54, 16, 47, 14], [54, 17, 47, 15, "width"], [54, 22, 47, 20], [54, 25, 47, 23, "video"], [54, 30, 47, 28], [54, 31, 47, 29, "videoWidth"], [54, 41, 47, 39], [55, 10, 48, 8, "canvas"], [55, 16, 48, 14], [55, 17, 48, 15, "height"], [55, 23, 48, 21], [55, 26, 48, 24, "video"], [55, 31, 48, 29], [55, 32, 48, 30, "videoHeight"], [55, 43, 48, 41], [56, 10, 49, 8, "canvas"], [56, 16, 49, 14], [56, 17, 49, 15, "style"], [56, 22, 49, 20], [56, 23, 49, 21, "width"], [56, 28, 49, 26], [56, 31, 49, 29], [56, 37, 49, 35], [57, 10, 50, 8, "canvas"], [57, 16, 50, 14], [57, 17, 50, 15, "style"], [57, 22, 50, 20], [57, 23, 50, 21, "height"], [57, 29, 50, 27], [57, 32, 50, 30], [57, 38, 50, 36], [58, 10, 51, 8, "console"], [58, 17, 51, 15], [58, 18, 51, 16, "log"], [58, 21, 51, 19], [58, 22, 51, 20], [58, 72, 51, 70], [58, 74, 51, 72, "video"], [58, 79, 51, 77], [58, 80, 51, 78, "videoWidth"], [58, 90, 51, 88], [58, 92, 51, 90], [58, 95, 51, 93], [58, 97, 51, 95, "video"], [58, 102, 51, 100], [58, 103, 51, 101, "videoHeight"], [58, 114, 51, 112], [58, 115, 51, 113], [59, 8, 52, 6], [59, 9, 52, 7], [59, 15, 52, 13], [60, 10, 53, 8], [61, 10, 54, 8, "canvas"], [61, 16, 54, 14], [61, 17, 54, 15, "width"], [61, 22, 54, 20], [61, 25, 54, 23, "width"], [61, 30, 54, 28], [62, 10, 55, 8, "canvas"], [62, 16, 55, 14], [62, 17, 55, 15, "height"], [62, 23, 55, 21], [62, 26, 55, 24, "height"], [62, 32, 55, 30], [63, 10, 56, 8, "console"], [63, 17, 56, 15], [63, 18, 56, 16, "log"], [63, 21, 56, 19], [63, 22, 56, 20], [63, 80, 56, 78], [63, 82, 56, 80, "width"], [63, 87, 56, 85], [63, 89, 56, 87], [63, 92, 56, 90], [63, 94, 56, 92, "height"], [63, 100, 56, 98], [63, 101, 56, 99], [64, 8, 57, 6], [65, 6, 58, 4], [65, 7, 58, 5], [66, 6, 60, 4], [66, 12, 60, 10, "ctx"], [66, 15, 60, 13], [66, 18, 60, 16, "canvas"], [66, 24, 60, 22], [66, 25, 60, 23, "getContext"], [66, 35, 60, 33], [66, 36, 60, 34], [66, 40, 60, 38], [66, 41, 60, 39], [67, 6, 61, 4], [67, 10, 61, 8], [67, 11, 61, 9, "ctx"], [67, 14, 61, 12], [67, 16, 61, 14], [68, 8, 62, 6, "console"], [68, 15, 62, 13], [68, 16, 62, 14, "error"], [68, 21, 62, 19], [68, 22, 62, 20], [68, 70, 62, 68], [68, 71, 62, 69], [69, 8, 63, 6], [70, 6, 64, 4], [71, 6, 66, 4], [71, 10, 66, 8, "isDetecting"], [71, 21, 66, 19], [71, 24, 66, 22], [71, 28, 66, 26], [73, 6, 68, 4], [74, 6, 69, 4], [74, 12, 69, 10, "loadScript"], [74, 22, 69, 20], [74, 25, 69, 24, "src"], [74, 28, 69, 35], [74, 32, 69, 55], [75, 8, 70, 6], [75, 15, 70, 13], [75, 19, 70, 17, "Promise"], [75, 26, 70, 24], [75, 27, 70, 25], [75, 28, 70, 26, "resolve"], [75, 35, 70, 33], [75, 37, 70, 35, "reject"], [75, 43, 70, 41], [75, 48, 70, 46], [76, 10, 71, 8], [76, 16, 71, 14, "script"], [76, 22, 71, 20], [76, 25, 71, 23, "document"], [76, 33, 71, 31], [76, 34, 71, 32, "createElement"], [76, 47, 71, 45], [76, 48, 71, 46], [76, 56, 71, 54], [76, 57, 71, 55], [77, 10, 72, 8, "script"], [77, 16, 72, 14], [77, 17, 72, 15, "src"], [77, 20, 72, 18], [77, 23, 72, 21, "src"], [77, 26, 72, 24], [78, 10, 73, 8, "script"], [78, 16, 73, 14], [78, 17, 73, 15, "onload"], [78, 23, 73, 21], [78, 26, 73, 24], [78, 32, 73, 30, "resolve"], [78, 39, 73, 37], [78, 40, 73, 38], [78, 41, 73, 39], [79, 10, 74, 8, "script"], [79, 16, 74, 14], [79, 17, 74, 15, "onerror"], [79, 24, 74, 22], [79, 27, 74, 25], [79, 33, 74, 31, "reject"], [79, 39, 74, 37], [79, 40, 74, 38], [79, 44, 74, 42, "Error"], [79, 49, 74, 47], [79, 50, 74, 48], [79, 68, 74, 66, "src"], [79, 71, 74, 69], [79, 73, 74, 71], [79, 74, 74, 72], [79, 75, 74, 73], [80, 10, 75, 8, "document"], [80, 18, 75, 16], [80, 19, 75, 17, "head"], [80, 23, 75, 21], [80, 24, 75, 22, "append<PERSON><PERSON><PERSON>"], [80, 35, 75, 33], [80, 36, 75, 34, "script"], [80, 42, 75, 40], [80, 43, 75, 41], [81, 8, 76, 6], [81, 9, 76, 7], [81, 10, 76, 8], [82, 6, 77, 4], [82, 7, 77, 5], [84, 6, 79, 4], [85, 6, 80, 4], [85, 12, 80, 10, "loadModel"], [85, 21, 80, 19], [85, 24, 80, 22], [85, 30, 80, 22, "loadModel"], [85, 31, 80, 22], [85, 36, 80, 34], [86, 8, 81, 6], [86, 12, 81, 10], [87, 10, 82, 8, "console"], [87, 17, 82, 15], [87, 18, 82, 16, "log"], [87, 21, 82, 19], [87, 22, 82, 20], [87, 66, 82, 64], [87, 67, 82, 65], [89, 10, 84, 8], [90, 10, 85, 8], [90, 14, 85, 12], [90, 15, 85, 14, "window"], [90, 21, 85, 20], [90, 22, 85, 29, "tf"], [90, 24, 85, 31], [90, 26, 85, 33], [91, 12, 86, 10], [91, 18, 86, 16, "loadScript"], [91, 28, 86, 26], [91, 29, 86, 27], [91, 98, 86, 96], [91, 99, 86, 97], [92, 10, 87, 8], [93, 10, 89, 8, "console"], [93, 17, 89, 15], [93, 18, 89, 16, "log"], [93, 21, 89, 19], [93, 22, 89, 20], [93, 68, 89, 66], [93, 69, 89, 67], [95, 10, 91, 8], [96, 10, 92, 8], [96, 14, 92, 12], [96, 15, 92, 14, "window"], [96, 21, 92, 20], [96, 22, 92, 29, "blazeface"], [96, 31, 92, 38], [96, 33, 92, 40], [97, 12, 93, 10], [97, 18, 93, 16, "loadScript"], [97, 28, 93, 26], [97, 29, 93, 27], [97, 112, 93, 110], [97, 113, 93, 111], [98, 10, 94, 8], [100, 10, 96, 8], [101, 10, 97, 8, "console"], [101, 17, 97, 15], [101, 18, 97, 16, "log"], [101, 21, 97, 19], [101, 22, 97, 20], [101, 73, 97, 71], [101, 74, 97, 72], [102, 10, 98, 8, "modelRef"], [102, 18, 98, 16], [102, 19, 98, 17, "current"], [102, 26, 98, 24], [102, 29, 98, 27], [102, 35, 98, 34, "window"], [102, 41, 98, 40], [102, 42, 98, 49, "blazeface"], [102, 51, 98, 58], [102, 52, 98, 59, "load"], [102, 56, 98, 63], [102, 57, 98, 64], [102, 58, 98, 65], [103, 10, 99, 8, "console"], [103, 17, 99, 15], [103, 18, 99, 16, "log"], [103, 21, 99, 19], [103, 22, 99, 20], [103, 79, 99, 77], [103, 80, 99, 78], [104, 10, 101, 8, "setIsLoading"], [104, 22, 101, 20], [104, 23, 101, 21], [104, 28, 101, 26], [104, 29, 101, 27], [106, 10, 103, 8], [107, 10, 104, 8, "updateCanvasSize"], [107, 26, 104, 24], [107, 27, 104, 25], [107, 28, 104, 26], [109, 10, 106, 8], [110, 10, 107, 8, "detectLoop"], [110, 20, 107, 18], [110, 21, 107, 19], [110, 22, 107, 20], [111, 8, 109, 6], [111, 9, 109, 7], [111, 10, 109, 8], [111, 17, 109, 15, "error"], [111, 22, 109, 20], [111, 24, 109, 22], [112, 10, 110, 8, "console"], [112, 17, 110, 15], [112, 18, 110, 16, "error"], [112, 23, 110, 21], [112, 24, 110, 22], [112, 67, 110, 65], [112, 69, 110, 67, "error"], [112, 74, 110, 72], [112, 75, 110, 73], [113, 10, 111, 8, "setIsLoading"], [113, 22, 111, 20], [113, 23, 111, 21], [113, 28, 111, 26], [113, 29, 111, 27], [114, 8, 112, 6], [115, 6, 113, 4], [115, 7, 113, 5], [116, 6, 115, 4], [116, 12, 115, 10, "detectLoop"], [116, 22, 115, 20], [116, 25, 115, 23], [116, 31, 115, 23, "detectLoop"], [116, 32, 115, 23], [116, 37, 115, 35], [117, 8, 116, 6], [117, 12, 116, 10], [117, 13, 116, 11, "isDetecting"], [117, 24, 116, 22], [117, 28, 116, 26], [117, 29, 116, 27, "modelRef"], [117, 37, 116, 35], [117, 38, 116, 36, "current"], [117, 45, 116, 43], [117, 47, 116, 45], [118, 8, 118, 6], [118, 12, 118, 10], [119, 10, 119, 8], [120, 10, 120, 8], [120, 14, 120, 12], [120, 15, 120, 13, "video"], [120, 20, 120, 18], [120, 21, 120, 19, "videoWidth"], [120, 31, 120, 29], [120, 35, 120, 33], [120, 36, 120, 34, "video"], [120, 41, 120, 39], [120, 42, 120, 40, "videoHeight"], [120, 53, 120, 51], [120, 55, 120, 53], [121, 12, 121, 10, "rafRef"], [121, 18, 121, 16], [121, 19, 121, 17, "current"], [121, 26, 121, 24], [121, 29, 121, 27, "requestAnimationFrame"], [121, 50, 121, 48], [121, 51, 121, 49, "detectLoop"], [121, 61, 121, 59], [121, 62, 121, 60], [122, 12, 122, 10], [123, 10, 123, 8], [125, 10, 125, 8], [126, 10, 126, 8], [126, 16, 126, 14, "tf"], [126, 18, 126, 16], [126, 21, 126, 20, "window"], [126, 27, 126, 26], [126, 28, 126, 35, "tf"], [126, 30, 126, 37], [127, 10, 127, 8], [127, 16, 127, 14, "tensor"], [127, 22, 127, 20], [127, 25, 127, 23, "tf"], [127, 27, 127, 25], [127, 28, 127, 26, "browser"], [127, 35, 127, 33], [127, 36, 127, 34, "fromPixels"], [127, 46, 127, 44], [127, 47, 127, 45, "video"], [127, 52, 127, 50], [127, 53, 127, 51], [129, 10, 129, 8], [130, 10, 130, 8], [130, 16, 130, 14, "predictions"], [130, 27, 130, 25], [130, 30, 130, 28], [130, 36, 130, 34, "modelRef"], [130, 44, 130, 42], [130, 45, 130, 43, "current"], [130, 52, 130, 50], [130, 53, 130, 51, "estimateFaces"], [130, 66, 130, 64], [130, 67, 130, 65, "tensor"], [130, 73, 130, 71], [130, 75, 130, 73], [130, 80, 130, 78], [130, 82, 130, 80], [130, 85, 130, 83], [130, 86, 130, 84], [131, 10, 131, 8, "tensor"], [131, 16, 131, 14], [131, 17, 131, 15, "dispose"], [131, 24, 131, 22], [131, 25, 131, 23], [131, 26, 131, 24], [133, 10, 133, 8], [134, 10, 134, 8, "ctx"], [134, 13, 134, 11], [134, 14, 134, 12, "clearRect"], [134, 23, 134, 21], [134, 24, 134, 22], [134, 25, 134, 23], [134, 27, 134, 25], [134, 28, 134, 26], [134, 30, 134, 28, "canvas"], [134, 36, 134, 34], [134, 37, 134, 35, "width"], [134, 42, 134, 40], [134, 44, 134, 42, "canvas"], [134, 50, 134, 48], [134, 51, 134, 49, "height"], [134, 57, 134, 55], [134, 58, 134, 56], [135, 10, 135, 8, "ctx"], [135, 13, 135, 11], [135, 14, 135, 12, "drawImage"], [135, 23, 135, 21], [135, 24, 135, 22, "video"], [135, 29, 135, 27], [135, 31, 135, 29], [135, 32, 135, 30], [135, 34, 135, 32], [135, 35, 135, 33], [135, 37, 135, 35, "canvas"], [135, 43, 135, 41], [135, 44, 135, 42, "width"], [135, 49, 135, 47], [135, 51, 135, 49, "canvas"], [135, 57, 135, 55], [135, 58, 135, 56, "height"], [135, 64, 135, 62], [135, 65, 135, 63], [136, 10, 137, 8], [136, 14, 137, 12, "predictions"], [136, 25, 137, 23], [136, 26, 137, 24, "length"], [136, 32, 137, 30], [136, 35, 137, 33], [136, 36, 137, 34], [136, 38, 137, 36], [137, 12, 138, 10, "setFaceCount"], [137, 24, 138, 22], [137, 25, 138, 23, "predictions"], [137, 36, 138, 34], [137, 37, 138, 35, "length"], [137, 43, 138, 41], [137, 44, 138, 42], [139, 12, 140, 10], [140, 12, 141, 10, "predictions"], [140, 23, 141, 21], [140, 24, 141, 22, "for<PERSON>ach"], [140, 31, 141, 29], [140, 32, 141, 30], [140, 33, 141, 31, "prediction"], [140, 43, 141, 46], [140, 45, 141, 48, "index"], [140, 50, 141, 61], [140, 55, 141, 66], [141, 14, 142, 12], [141, 20, 142, 18], [141, 21, 142, 19, "x1"], [141, 23, 142, 21], [141, 25, 142, 23, "y1"], [141, 27, 142, 25], [141, 28, 142, 26], [141, 31, 142, 29, "prediction"], [141, 41, 142, 39], [141, 42, 142, 40, "topLeft"], [141, 49, 142, 47], [142, 14, 143, 12], [142, 20, 143, 18], [142, 21, 143, 19, "x2"], [142, 23, 143, 21], [142, 25, 143, 23, "y2"], [142, 27, 143, 25], [142, 28, 143, 26], [142, 31, 143, 29, "prediction"], [142, 41, 143, 39], [142, 42, 143, 40, "bottomRight"], [142, 53, 143, 51], [144, 14, 145, 12], [145, 14, 146, 12], [145, 18, 146, 16, "minX"], [145, 22, 146, 20], [145, 25, 146, 23, "Math"], [145, 29, 146, 27], [145, 30, 146, 28, "min"], [145, 33, 146, 31], [145, 34, 146, 32, "x1"], [145, 36, 146, 34], [145, 38, 146, 36, "x2"], [145, 40, 146, 38], [145, 41, 146, 39], [146, 14, 147, 12], [146, 18, 147, 16, "maxX"], [146, 22, 147, 20], [146, 25, 147, 23, "Math"], [146, 29, 147, 27], [146, 30, 147, 28, "max"], [146, 33, 147, 31], [146, 34, 147, 32, "x1"], [146, 36, 147, 34], [146, 38, 147, 36, "x2"], [146, 40, 147, 38], [146, 41, 147, 39], [147, 14, 148, 12], [147, 20, 148, 18, "minY"], [147, 24, 148, 22], [147, 27, 148, 25, "Math"], [147, 31, 148, 29], [147, 32, 148, 30, "min"], [147, 35, 148, 33], [147, 36, 148, 34, "y1"], [147, 38, 148, 36], [147, 40, 148, 38, "y2"], [147, 42, 148, 40], [147, 43, 148, 41], [148, 14, 149, 12], [148, 20, 149, 18, "maxY"], [148, 24, 149, 22], [148, 27, 149, 25, "Math"], [148, 31, 149, 29], [148, 32, 149, 30, "max"], [148, 35, 149, 33], [148, 36, 149, 34, "y1"], [148, 38, 149, 36], [148, 40, 149, 38, "y2"], [148, 42, 149, 40], [148, 43, 149, 41], [150, 14, 151, 12], [151, 14, 152, 12], [151, 20, 152, 18, "canvasWidth"], [151, 31, 152, 29], [151, 34, 152, 32, "canvas"], [151, 40, 152, 38], [151, 41, 152, 39, "width"], [151, 46, 152, 44], [152, 14, 153, 12], [152, 20, 153, 18, "flippedMinX"], [152, 31, 153, 29], [152, 34, 153, 32, "canvasWidth"], [152, 45, 153, 43], [152, 48, 153, 46, "maxX"], [152, 52, 153, 50], [153, 14, 154, 12], [153, 20, 154, 18, "flippedMaxX"], [153, 31, 154, 29], [153, 34, 154, 32, "canvasWidth"], [153, 45, 154, 43], [153, 48, 154, 46, "minX"], [153, 52, 154, 50], [154, 14, 155, 12, "minX"], [154, 18, 155, 16], [154, 21, 155, 19, "flippedMinX"], [154, 32, 155, 30], [155, 14, 156, 12, "maxX"], [155, 18, 156, 16], [155, 21, 156, 19, "flippedMaxX"], [155, 32, 156, 30], [157, 14, 158, 12], [158, 14, 159, 12], [158, 20, 159, 18, "faceWidth"], [158, 29, 159, 27], [158, 32, 159, 30, "maxX"], [158, 36, 159, 34], [158, 39, 159, 37, "minX"], [158, 43, 159, 41], [159, 14, 160, 12], [159, 20, 160, 18, "faceHeight"], [159, 30, 160, 28], [159, 33, 160, 31, "maxY"], [159, 37, 160, 35], [159, 40, 160, 38, "minY"], [159, 44, 160, 42], [160, 14, 162, 12], [160, 18, 162, 16, "faceWidth"], [160, 27, 162, 25], [160, 31, 162, 29], [160, 32, 162, 30], [160, 36, 162, 34, "faceHeight"], [160, 46, 162, 44], [160, 50, 162, 48], [160, 51, 162, 49], [160, 53, 162, 51], [161, 16, 163, 14], [162, 14, 164, 12], [164, 14, 166, 12], [165, 14, 167, 12], [165, 20, 167, 18, "centerX"], [165, 27, 167, 25], [165, 30, 167, 28], [165, 31, 167, 29, "minX"], [165, 35, 167, 33], [165, 38, 167, 36, "maxX"], [165, 42, 167, 40], [165, 46, 167, 44], [165, 47, 167, 45], [166, 14, 168, 12], [166, 20, 168, 18, "centerY"], [166, 27, 168, 25], [166, 30, 168, 28], [166, 31, 168, 29, "minY"], [166, 35, 168, 33], [166, 38, 168, 36, "maxY"], [166, 42, 168, 40], [166, 46, 168, 44], [166, 47, 168, 45], [167, 14, 169, 12], [167, 20, 169, 18, "expandedWidth"], [167, 33, 169, 31], [167, 36, 169, 34, "faceWidth"], [167, 45, 169, 43], [167, 48, 169, 46], [167, 51, 169, 49], [168, 14, 170, 12], [168, 20, 170, 18, "expandedHeight"], [168, 34, 170, 32], [168, 37, 170, 35, "faceHeight"], [168, 47, 170, 45], [168, 50, 170, 48], [168, 53, 170, 51], [170, 14, 172, 12], [171, 14, 173, 12], [171, 20, 173, 18, "radiusX"], [171, 27, 173, 25], [171, 30, 173, 28, "Math"], [171, 34, 173, 32], [171, 35, 173, 33, "max"], [171, 38, 173, 36], [171, 39, 173, 37, "expandedWidth"], [171, 52, 173, 50], [171, 55, 173, 53], [171, 56, 173, 54], [171, 58, 173, 56], [171, 60, 173, 58], [171, 61, 173, 59], [172, 14, 174, 12], [172, 20, 174, 18, "radiusY"], [172, 27, 174, 25], [172, 30, 174, 28, "Math"], [172, 34, 174, 32], [172, 35, 174, 33, "max"], [172, 38, 174, 36], [172, 39, 174, 37, "expandedHeight"], [172, 53, 174, 51], [172, 56, 174, 54], [172, 57, 174, 55], [172, 59, 174, 57], [172, 61, 174, 59], [172, 62, 174, 60], [174, 14, 176, 12], [175, 14, 177, 12, "ctx"], [175, 17, 177, 15], [175, 18, 177, 16, "save"], [175, 22, 177, 20], [175, 23, 177, 21], [175, 24, 177, 22], [176, 14, 178, 12, "ctx"], [176, 17, 178, 15], [176, 18, 178, 16, "beginPath"], [176, 27, 178, 25], [176, 28, 178, 26], [176, 29, 178, 27], [177, 14, 179, 12, "ctx"], [177, 17, 179, 15], [177, 18, 179, 16, "ellipse"], [177, 25, 179, 23], [177, 26, 179, 24, "centerX"], [177, 33, 179, 31], [177, 35, 179, 33, "centerY"], [177, 42, 179, 40], [177, 44, 179, 42, "radiusX"], [177, 51, 179, 49], [177, 53, 179, 51, "radiusY"], [177, 60, 179, 58], [177, 62, 179, 60], [177, 63, 179, 61], [177, 65, 179, 63], [177, 66, 179, 64], [177, 68, 179, 66, "Math"], [177, 72, 179, 70], [177, 73, 179, 71, "PI"], [177, 75, 179, 73], [177, 78, 179, 76], [177, 79, 179, 77], [177, 80, 179, 78], [178, 14, 180, 12, "ctx"], [178, 17, 180, 15], [178, 18, 180, 16, "clip"], [178, 22, 180, 20], [178, 23, 180, 21], [178, 24, 180, 22], [179, 14, 181, 12, "ctx"], [179, 17, 181, 15], [179, 18, 181, 16, "filter"], [179, 24, 181, 22], [179, 27, 181, 25], [179, 39, 181, 37], [179, 40, 181, 38], [179, 41, 181, 39], [180, 14, 182, 12, "ctx"], [180, 17, 182, 15], [180, 18, 182, 16, "drawImage"], [180, 27, 182, 25], [180, 28, 182, 26, "video"], [180, 33, 182, 31], [180, 35, 182, 33], [180, 36, 182, 34], [180, 38, 182, 36], [180, 39, 182, 37], [180, 41, 182, 39, "canvas"], [180, 47, 182, 45], [180, 48, 182, 46, "width"], [180, 53, 182, 51], [180, 55, 182, 53, "canvas"], [180, 61, 182, 59], [180, 62, 182, 60, "height"], [180, 68, 182, 66], [180, 69, 182, 67], [181, 14, 183, 12, "ctx"], [181, 17, 183, 15], [181, 18, 183, 16, "restore"], [181, 25, 183, 23], [181, 26, 183, 24], [181, 27, 183, 25], [183, 14, 185, 12], [184, 14, 186, 12], [184, 18, 186, 16, "__DEV__"], [184, 25, 186, 23], [184, 27, 186, 25], [185, 16, 187, 14, "ctx"], [185, 19, 187, 17], [185, 20, 187, 18, "strokeStyle"], [185, 31, 187, 29], [185, 34, 187, 32], [185, 56, 187, 54], [186, 16, 188, 14, "ctx"], [186, 19, 188, 17], [186, 20, 188, 18, "lineWidth"], [186, 29, 188, 27], [186, 32, 188, 30], [186, 33, 188, 31], [187, 16, 189, 14, "ctx"], [187, 19, 189, 17], [187, 20, 189, 18, "strokeRect"], [187, 30, 189, 28], [187, 31, 189, 29, "minX"], [187, 35, 189, 33], [187, 37, 189, 35, "minY"], [187, 41, 189, 39], [187, 43, 189, 41, "faceWidth"], [187, 52, 189, 50], [187, 54, 189, 52, "faceHeight"], [187, 64, 189, 62], [187, 65, 189, 63], [188, 14, 190, 12], [189, 12, 191, 10], [189, 13, 191, 11], [189, 14, 191, 12], [190, 10, 192, 8], [190, 11, 192, 9], [190, 17, 192, 15], [191, 12, 193, 10, "setFaceCount"], [191, 24, 193, 22], [191, 25, 193, 23], [191, 26, 193, 24], [191, 27, 193, 25], [192, 10, 194, 8], [193, 8, 196, 6], [193, 9, 196, 7], [193, 10, 196, 8], [193, 17, 196, 15, "error"], [193, 22, 196, 20], [193, 24, 196, 22], [194, 10, 197, 8, "console"], [194, 17, 197, 15], [194, 18, 197, 16, "error"], [194, 23, 197, 21], [194, 24, 197, 22], [194, 60, 197, 58], [194, 62, 197, 60, "error"], [194, 67, 197, 65], [194, 68, 197, 66], [195, 8, 198, 6], [197, 8, 200, 6], [198, 8, 201, 6], [198, 12, 201, 10, "isDetecting"], [198, 23, 201, 21], [198, 25, 201, 23], [199, 10, 202, 8, "rafRef"], [199, 16, 202, 14], [199, 17, 202, 15, "current"], [199, 24, 202, 22], [199, 27, 202, 25, "requestAnimationFrame"], [199, 48, 202, 46], [199, 49, 202, 47, "detectLoop"], [199, 59, 202, 57], [199, 60, 202, 58], [200, 8, 203, 6], [201, 6, 204, 4], [201, 7, 204, 5], [203, 6, 206, 4], [204, 6, 207, 4], [204, 12, 207, 10, "waitForVideoAndStart"], [204, 32, 207, 30], [204, 35, 207, 33, "waitForVideoAndStart"], [204, 36, 207, 33], [204, 41, 207, 39], [205, 8, 208, 6], [205, 12, 208, 10, "video"], [205, 17, 208, 15], [205, 18, 208, 16, "readyState"], [205, 28, 208, 26], [205, 32, 208, 30], [205, 33, 208, 31], [205, 35, 208, 33], [206, 10, 208, 35], [207, 10, 209, 8, "loadModel"], [207, 19, 209, 17], [207, 20, 209, 18], [207, 21, 209, 19], [208, 8, 210, 6], [208, 9, 210, 7], [208, 15, 210, 13], [209, 10, 211, 8, "video"], [209, 15, 211, 13], [209, 16, 211, 14, "addEventListener"], [209, 32, 211, 30], [209, 33, 211, 31], [209, 45, 211, 43], [209, 47, 211, 45, "loadModel"], [209, 56, 211, 54], [209, 58, 211, 56], [210, 12, 211, 58, "once"], [210, 16, 211, 62], [210, 18, 211, 64], [211, 10, 211, 69], [211, 11, 211, 70], [211, 12, 211, 71], [212, 8, 212, 6], [213, 6, 213, 4], [213, 7, 213, 5], [215, 6, 215, 4], [216, 6, 216, 4, "waitForVideoAndStart"], [216, 26, 216, 24], [216, 27, 216, 25], [216, 28, 216, 26], [218, 6, 218, 4], [219, 6, 219, 4], [219, 13, 219, 11], [219, 19, 219, 17], [220, 8, 220, 6, "isDetecting"], [220, 19, 220, 17], [220, 22, 220, 20], [220, 27, 220, 25], [221, 8, 221, 6], [221, 12, 221, 10, "rafRef"], [221, 18, 221, 16], [221, 19, 221, 17, "current"], [221, 26, 221, 24], [221, 28, 221, 26], [222, 10, 222, 8, "cancelAnimationFrame"], [222, 30, 222, 28], [222, 31, 222, 29, "rafRef"], [222, 37, 222, 35], [222, 38, 222, 36, "current"], [222, 45, 222, 43], [222, 46, 222, 44], [223, 8, 223, 6], [224, 6, 224, 4], [224, 7, 224, 5], [225, 4, 225, 2], [225, 5, 225, 3], [225, 7, 225, 5], [225, 8, 225, 6, "containerId"], [225, 19, 225, 17], [225, 21, 225, 19, "width"], [225, 26, 225, 24], [225, 28, 225, 26, "height"], [225, 34, 225, 32], [225, 35, 225, 33], [225, 36, 225, 34], [226, 4, 227, 2], [226, 24, 228, 4], [226, 28, 228, 4, "_jsxDevRuntime"], [226, 42, 228, 4], [226, 43, 228, 4, "jsxDEV"], [226, 49, 228, 4], [226, 51, 228, 4, "_jsxDevRuntime"], [226, 65, 228, 4], [226, 66, 228, 4, "Fragment"], [226, 74, 228, 4], [227, 6, 228, 4, "children"], [227, 14, 228, 4], [227, 30, 229, 6], [227, 34, 229, 6, "_jsxDevRuntime"], [227, 48, 229, 6], [227, 49, 229, 6, "jsxDEV"], [227, 55, 229, 6], [228, 8, 230, 8, "ref"], [228, 11, 230, 11], [228, 13, 230, 13, "canvasRef"], [228, 22, 230, 23], [229, 8, 231, 8, "style"], [229, 13, 231, 13], [229, 15, 231, 15], [230, 10, 232, 10, "position"], [230, 18, 232, 18], [230, 20, 232, 20], [230, 30, 232, 30], [231, 10, 233, 10, "left"], [231, 14, 233, 14], [231, 16, 233, 16], [231, 17, 233, 17], [232, 10, 234, 10, "top"], [232, 13, 234, 13], [232, 15, 234, 15], [232, 16, 234, 16], [233, 10, 235, 10, "width"], [233, 15, 235, 15], [233, 17, 235, 17], [233, 23, 235, 23], [234, 10, 236, 10, "height"], [234, 16, 236, 16], [234, 18, 236, 18], [234, 24, 236, 24], [235, 10, 237, 10, "pointerEvents"], [235, 23, 237, 23], [235, 25, 237, 25], [235, 31, 237, 31], [236, 10, 238, 10, "zIndex"], [236, 16, 238, 16], [236, 18, 238, 18], [236, 20, 238, 20], [237, 10, 238, 22], [238, 10, 239, 10, "objectFit"], [238, 19, 239, 19], [238, 21, 239, 21], [238, 28, 239, 28], [239, 10, 240, 10, "backgroundColor"], [239, 25, 240, 25], [239, 27, 240, 27], [240, 8, 241, 8], [241, 6, 241, 10], [242, 8, 241, 10, "fileName"], [242, 16, 241, 10], [242, 18, 241, 10, "_jsxFileName"], [242, 30, 241, 10], [243, 8, 241, 10, "lineNumber"], [243, 18, 241, 10], [244, 8, 241, 10, "columnNumber"], [244, 20, 241, 10], [245, 6, 241, 10], [245, 13, 242, 7], [245, 14, 242, 8], [245, 16, 244, 7, "isLoading"], [245, 25, 244, 16], [245, 42, 245, 8], [245, 46, 245, 8, "_jsxDevRuntime"], [245, 60, 245, 8], [245, 61, 245, 8, "jsxDEV"], [245, 67, 245, 8], [246, 8, 245, 13, "style"], [246, 13, 245, 18], [246, 15, 245, 20], [247, 10, 246, 10, "position"], [247, 18, 246, 18], [247, 20, 246, 20], [247, 30, 246, 30], [248, 10, 247, 10, "top"], [248, 13, 247, 13], [248, 15, 247, 15], [248, 17, 247, 17], [249, 10, 248, 10, "left"], [249, 14, 248, 14], [249, 16, 248, 16], [249, 18, 248, 18], [250, 10, 249, 10, "background"], [250, 20, 249, 20], [250, 22, 249, 22], [250, 47, 249, 47], [251, 10, 250, 10, "color"], [251, 15, 250, 15], [251, 17, 250, 17], [251, 24, 250, 24], [252, 10, 251, 10, "padding"], [252, 17, 251, 17], [252, 19, 251, 19], [252, 29, 251, 29], [253, 10, 252, 10, "borderRadius"], [253, 22, 252, 22], [253, 24, 252, 24], [253, 29, 252, 29], [254, 10, 253, 10, "fontSize"], [254, 18, 253, 18], [254, 20, 253, 20], [254, 26, 253, 26], [255, 10, 254, 10, "fontWeight"], [255, 20, 254, 20], [255, 22, 254, 22], [255, 27, 254, 27], [256, 10, 255, 10, "zIndex"], [256, 16, 255, 16], [256, 18, 255, 18], [256, 20, 255, 20], [257, 10, 256, 10, "border"], [257, 16, 256, 16], [257, 18, 256, 18], [258, 8, 257, 8], [258, 9, 257, 10], [259, 8, 257, 10, "children"], [259, 16, 257, 10], [259, 18, 257, 11], [260, 6, 259, 8], [261, 8, 259, 8, "fileName"], [261, 16, 259, 8], [261, 18, 259, 8, "_jsxFileName"], [261, 30, 259, 8], [262, 8, 259, 8, "lineNumber"], [262, 18, 259, 8], [263, 8, 259, 8, "columnNumber"], [263, 20, 259, 8], [264, 6, 259, 8], [264, 13, 259, 13], [264, 14, 260, 7], [264, 16, 261, 7], [264, 17, 261, 8, "isLoading"], [264, 26, 261, 17], [264, 30, 261, 21, "faceCount"], [264, 39, 261, 30], [264, 42, 261, 33], [264, 43, 261, 34], [264, 60, 262, 8], [264, 64, 262, 8, "_jsxDevRuntime"], [264, 78, 262, 8], [264, 79, 262, 8, "jsxDEV"], [264, 85, 262, 8], [265, 8, 262, 13, "style"], [265, 13, 262, 18], [265, 15, 262, 20], [266, 10, 263, 10, "position"], [266, 18, 263, 18], [266, 20, 263, 20], [266, 30, 263, 30], [267, 10, 264, 10, "top"], [267, 13, 264, 13], [267, 15, 264, 15], [267, 17, 264, 17], [268, 10, 265, 10, "left"], [268, 14, 265, 14], [268, 16, 265, 16], [268, 18, 265, 18], [269, 10, 266, 10, "background"], [269, 20, 266, 20], [269, 22, 266, 22], [269, 47, 266, 47], [270, 10, 267, 10, "color"], [270, 15, 267, 15], [270, 17, 267, 17], [270, 24, 267, 24], [271, 10, 268, 10, "padding"], [271, 17, 268, 17], [271, 19, 268, 19], [271, 29, 268, 29], [272, 10, 269, 10, "borderRadius"], [272, 22, 269, 22], [272, 24, 269, 24], [272, 29, 269, 29], [273, 10, 270, 10, "fontSize"], [273, 18, 270, 18], [273, 20, 270, 20], [273, 26, 270, 26], [274, 10, 271, 10, "fontWeight"], [274, 20, 271, 20], [274, 22, 271, 22], [274, 27, 271, 27], [275, 10, 272, 10, "zIndex"], [275, 16, 272, 16], [275, 18, 272, 18], [275, 20, 272, 20], [276, 10, 273, 10, "border"], [276, 16, 273, 16], [276, 18, 273, 18], [277, 8, 274, 8], [277, 9, 274, 10], [278, 8, 274, 10, "children"], [278, 16, 274, 10], [278, 19, 274, 11], [278, 51, 275, 25], [278, 53, 275, 26, "faceCount"], [278, 62, 275, 35], [278, 64, 275, 36], [278, 71, 275, 41], [278, 73, 275, 42, "faceCount"], [278, 82, 275, 51], [278, 85, 275, 54], [278, 86, 275, 55], [278, 89, 275, 58], [278, 92, 275, 61], [278, 95, 275, 64], [278, 97, 275, 66], [279, 6, 275, 66], [280, 8, 275, 66, "fileName"], [280, 16, 275, 66], [280, 18, 275, 66, "_jsxFileName"], [280, 30, 275, 66], [281, 8, 275, 66, "lineNumber"], [281, 18, 275, 66], [282, 8, 275, 66, "columnNumber"], [282, 20, 275, 66], [283, 6, 275, 66], [283, 13, 276, 13], [283, 14, 277, 7], [283, 16, 278, 7], [283, 17, 278, 8, "isLoading"], [283, 26, 278, 17], [283, 30, 278, 21, "faceCount"], [283, 39, 278, 30], [283, 44, 278, 35], [283, 45, 278, 36], [283, 62, 279, 8], [283, 66, 279, 8, "_jsxDevRuntime"], [283, 80, 279, 8], [283, 81, 279, 8, "jsxDEV"], [283, 87, 279, 8], [284, 8, 279, 13, "style"], [284, 13, 279, 18], [284, 15, 279, 20], [285, 10, 280, 10, "position"], [285, 18, 280, 18], [285, 20, 280, 20], [285, 30, 280, 30], [286, 10, 281, 10, "top"], [286, 13, 281, 13], [286, 15, 281, 15], [286, 17, 281, 17], [287, 10, 282, 10, "left"], [287, 14, 282, 14], [287, 16, 282, 16], [287, 18, 282, 18], [288, 10, 283, 10, "background"], [288, 20, 283, 20], [288, 22, 283, 22], [288, 48, 283, 48], [289, 10, 284, 10, "color"], [289, 15, 284, 15], [289, 17, 284, 17], [289, 24, 284, 24], [290, 10, 285, 10, "padding"], [290, 17, 285, 17], [290, 19, 285, 19], [290, 29, 285, 29], [291, 10, 286, 10, "borderRadius"], [291, 22, 286, 22], [291, 24, 286, 24], [291, 29, 286, 29], [292, 10, 287, 10, "fontSize"], [292, 18, 287, 18], [292, 20, 287, 20], [292, 26, 287, 26], [293, 10, 288, 10, "fontWeight"], [293, 20, 288, 20], [293, 22, 288, 22], [293, 27, 288, 27], [294, 10, 289, 10, "zIndex"], [294, 16, 289, 16], [294, 18, 289, 18], [294, 20, 289, 20], [295, 10, 290, 10, "border"], [295, 16, 290, 16], [295, 18, 290, 18], [296, 8, 291, 8], [296, 9, 291, 10], [297, 8, 291, 10, "children"], [297, 16, 291, 10], [297, 18, 291, 11], [298, 6, 293, 8], [299, 8, 293, 8, "fileName"], [299, 16, 293, 8], [299, 18, 293, 8, "_jsxFileName"], [299, 30, 293, 8], [300, 8, 293, 8, "lineNumber"], [300, 18, 293, 8], [301, 8, 293, 8, "columnNumber"], [301, 20, 293, 8], [302, 6, 293, 8], [302, 13, 293, 13], [302, 14, 294, 7], [303, 4, 294, 7], [303, 19, 295, 6], [303, 20, 295, 7], [304, 2, 297, 0], [305, 2, 297, 1, "_s"], [305, 4, 297, 1], [305, 5, 16, 24, "BlazeFaceCanvas"], [305, 20, 16, 39], [306, 2, 16, 39, "_c"], [306, 4, 16, 39], [306, 7, 16, 24, "BlazeFaceCanvas"], [306, 22, 16, 39], [307, 2, 16, 39], [307, 6, 16, 39, "_c"], [307, 8, 16, 39], [308, 2, 16, 39, "$RefreshReg$"], [308, 14, 16, 39], [308, 15, 16, 39, "_c"], [308, 17, 16, 39], [309, 0, 16, 39], [309, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "updateCanvasSize", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "loadModel", "detectLoop", "predictions.forEach$argument_0", "waitForVideoAndStart", "<anonymous>"], "mappings": "AAA;eCe;YCO;6BCsB;KDa;uBEW;yBCC;wBCG,eD;yBEC,gDF;ODE;KFC;sBMG;KNiC;uBOE;8BC0B;WDkD;KPa;iCSG;KTM;WUM;KVK;GDC;CDwE"}}, "type": "js/module"}]}