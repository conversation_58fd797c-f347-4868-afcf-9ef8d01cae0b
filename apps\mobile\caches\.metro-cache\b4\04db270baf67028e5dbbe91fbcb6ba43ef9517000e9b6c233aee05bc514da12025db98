{"dependencies": [{"name": "../../../dom/nodes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 84, "index": 84}}], "key": "Z+GW5Ist+DDyIe4BLHPS68wWHKY=", "exportNames": ["*"]}}, {"name": "../../../skia/types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 85}, "end": {"line": 2, "column": 54, "index": 139}}], "key": "hnxlDT1tba4gQfvf2h/i6nte9KM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.saveCTM = void 0;\n  var _nodes = require(_dependencyMap[0], \"../../../dom/nodes\");\n  var _types = require(_dependencyMap[1], \"../../../skia/types\");\n  const _worklet_15774729423578_init_data = {\n    code: \"function CTMJs1(Skia,clip){const{isPathDef,processPath,isRRect}=this.__closure;if(clip){if(isPathDef(clip)){return{clipPath:processPath(Skia,clip)};}else if(isRRect(clip)){return{clipRRect:clip};}else{return{clipRect:clip};}}return undefined;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Recorder\\\\commands\\\\CTM.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"CTMJs1\\\",\\\"Skia\\\",\\\"clip\\\",\\\"isPathDef\\\",\\\"processPath\\\",\\\"isRRect\\\",\\\"__closure\\\",\\\"clipPath\\\",\\\"clipRRect\\\",\\\"clipRect\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Recorder/commands/CTM.js\\\"],\\\"mappings\\\":\\\"AAEoB,QAAC,CAAAA,MAAIA,CAAAC,IAAE,CAAIC,IAAK,QAAAC,SAAA,CAAAC,WAAA,CAAAC,OAAA,OAAAC,SAAA,CAGlC,GAAIJ,IAAI,CAAE,CACR,GAAIC,SAAS,CAACD,IAAI,CAAC,CAAE,CACnB,MAAO,CACLK,QAAQ,CAAEH,WAAW,CAACH,IAAI,CAAEC,IAAI,CAClC,CAAC,CACH,CAAC,IAAM,IAAIG,OAAO,CAACH,IAAI,CAAC,CAAE,CACxB,MAAO,CACLM,SAAS,CAAEN,IACb,CAAC,CACH,CAAC,IAAM,CACL,MAAO,CACLO,QAAQ,CAAEP,IACZ,CAAC,CACH,CACF,CACA,MAAO,CAAAQ,SAAS,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const computeClip = function () {\n    const _e = [new global.Error(), -4, -27];\n    const CTMJs1 = function (Skia, clip) {\n      if (clip) {\n        if ((0, _nodes.isPathDef)(clip)) {\n          return {\n            clipPath: (0, _nodes.processPath)(Skia, clip)\n          };\n        } else if ((0, _types.isRRect)(clip)) {\n          return {\n            clipRRect: clip\n          };\n        } else {\n          return {\n            clipRect: clip\n          };\n        }\n      }\n      return undefined;\n    };\n    CTMJs1.__closure = {\n      isPathDef: _nodes.isPathDef,\n      processPath: _nodes.processPath,\n      isRRect: _types.isRRect\n    };\n    CTMJs1.__workletHash = 15774729423578;\n    CTMJs1.__initData = _worklet_15774729423578_init_data;\n    CTMJs1.__stackDetails = _e;\n    return CTMJs1;\n  }();\n  const _worklet_17552438720068_init_data = {\n    code: \"function CTMJs2(ctx,props){const{computeClip,ClipOp,processTransformProps2}=this.__closure;const{canvas:canvas,Skia:Skia}=ctx;const{clip:rawClip,invertClip:invertClip,matrix:matrix,transform:transform,origin:origin,layer:layer}=props;const hasTransform=matrix!==undefined||transform!==undefined;const clip=computeClip(Skia,rawClip);const hasClip=clip!==undefined;const op=invertClip?ClipOp.Difference:ClipOp.Intersect;const m3=processTransformProps2(Skia,{matrix:matrix,transform:transform,origin:origin});const shouldSave=hasTransform||hasClip||!!layer;if(shouldSave){if(layer){if(typeof layer===\\\"boolean\\\"){canvas.saveLayer();}else{canvas.saveLayer(layer);}}else{canvas.save();}}if(m3){canvas.concat(m3);}if(clip){if(\\\"clipRect\\\"in clip){canvas.clipRect(clip.clipRect,op,true);}else if(\\\"clipRRect\\\"in clip){canvas.clipRRect(clip.clipRRect,op,true);}else{canvas.clipPath(clip.clipPath,op,true);}}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Recorder\\\\commands\\\\CTM.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"CTMJs2\\\",\\\"ctx\\\",\\\"props\\\",\\\"computeClip\\\",\\\"ClipOp\\\",\\\"processTransformProps2\\\",\\\"__closure\\\",\\\"canvas\\\",\\\"Skia\\\",\\\"clip\\\",\\\"rawClip\\\",\\\"invertClip\\\",\\\"matrix\\\",\\\"transform\\\",\\\"origin\\\",\\\"layer\\\",\\\"hasTransform\\\",\\\"undefined\\\",\\\"hasClip\\\",\\\"op\\\",\\\"Difference\\\",\\\"Intersect\\\",\\\"m3\\\",\\\"shouldSave\\\",\\\"saveLayer\\\",\\\"save\\\",\\\"concat\\\",\\\"clipRect\\\",\\\"clipRRect\\\",\\\"clipPath\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Recorder/commands/CTM.js\\\"],\\\"mappings\\\":\\\"AAsBuB,QAAC,CAAAA,MAAGA,CAAEC,GAAA,CAAKC,KAAK,QAAAC,WAAA,CAAAC,MAAA,CAAAC,sBAAA,OAAAC,SAAA,CAGrC,KAAM,CACJC,MAAM,CAANA,MAAM,CACNC,IAAA,CAAAA,IACF,CAAC,CAAGP,GAAG,CACP,KAAM,CACJQ,IAAI,CAAEC,OAAO,CACbC,UAAU,CAAVA,UAAU,CACVC,MAAM,CAANA,MAAM,CACNC,SAAS,CAATA,SAAS,CACTC,MAAM,CAANA,MAAM,CACNC,KAAA,CAAAA,KACF,CAAC,CAAGb,KAAK,CACT,KAAM,CAAAc,YAAY,CAAGJ,MAAM,GAAKK,SAAS,EAAIJ,SAAS,GAAKI,SAAS,CACpE,KAAM,CAAAR,IAAI,CAAGN,WAAW,CAACK,IAAI,CAAEE,OAAO,CAAC,CACvC,KAAM,CAAAQ,OAAO,CAAGT,IAAI,GAAKQ,SAAS,CAClC,KAAM,CAAAE,EAAE,CAAGR,UAAU,CAAGP,MAAM,CAACgB,UAAU,CAAGhB,MAAM,CAACiB,SAAS,CAC5D,KAAM,CAAAC,EAAE,CAAGjB,sBAAsB,CAACG,IAAI,CAAE,CACtCI,MAAM,CAANA,MAAM,CACNC,SAAS,CAATA,SAAS,CACTC,MAAA,CAAAA,MACF,CAAC,CAAC,CACF,KAAM,CAAAS,UAAU,CAAGP,YAAY,EAAIE,OAAO,EAAI,CAAC,CAACH,KAAK,CACrD,GAAIQ,UAAU,CAAE,CACd,GAAIR,KAAK,CAAE,CACT,GAAI,MAAO,CAAAA,KAAK,GAAK,SAAS,CAAE,CAC9BR,MAAM,CAACiB,SAAS,CAAC,CAAC,CACpB,CAAC,IAAM,CACLjB,MAAM,CAACiB,SAAS,CAACT,KAAK,CAAC,CACzB,CACF,CAAC,IAAM,CACLR,MAAM,CAACkB,IAAI,CAAC,CAAC,CACf,CACF,CACA,GAAIH,EAAE,CAAE,CACNf,MAAM,CAACmB,MAAM,CAACJ,EAAE,CAAC,CACnB,CACA,GAAIb,IAAI,CAAE,CACR,GAAI,UAAU,EAAI,CAAAA,IAAI,CAAE,CACtBF,MAAM,CAACoB,QAAQ,CAAClB,IAAI,CAACkB,QAAQ,CAAER,EAAE,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,IAAI,WAAW,EAAI,CAAAV,IAAI,CAAE,CAC9BF,MAAM,CAACqB,SAAS,CAACnB,IAAI,CAACmB,SAAS,CAAET,EAAE,CAAE,IAAI,CAAC,CAC5C,CAAC,IAAM,CACLZ,MAAM,CAACsB,QAAQ,CAACpB,IAAI,CAACoB,QAAQ,CAAEV,EAAE,CAAE,IAAI,CAAC,CAC1C,CACF,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const saveCTM = exports.saveCTM = function () {\n    const _e = [new global.Error(), -4, -27];\n    const CTMJs2 = function (ctx, props) {\n      const {\n        canvas,\n        Skia\n      } = ctx;\n      const {\n        clip: rawClip,\n        invertClip,\n        matrix,\n        transform,\n        origin,\n        layer\n      } = props;\n      const hasTransform = matrix !== undefined || transform !== undefined;\n      const clip = computeClip(Skia, rawClip);\n      const hasClip = clip !== undefined;\n      const op = invertClip ? _types.ClipOp.Difference : _types.ClipOp.Intersect;\n      const m3 = (0, _nodes.processTransformProps2)(Skia, {\n        matrix,\n        transform,\n        origin\n      });\n      const shouldSave = hasTransform || hasClip || !!layer;\n      if (shouldSave) {\n        if (layer) {\n          if (typeof layer === \"boolean\") {\n            canvas.saveLayer();\n          } else {\n            canvas.saveLayer(layer);\n          }\n        } else {\n          canvas.save();\n        }\n      }\n      if (m3) {\n        canvas.concat(m3);\n      }\n      if (clip) {\n        if (\"clipRect\" in clip) {\n          canvas.clipRect(clip.clipRect, op, true);\n        } else if (\"clipRRect\" in clip) {\n          canvas.clipRRect(clip.clipRRect, op, true);\n        } else {\n          canvas.clipPath(clip.clipPath, op, true);\n        }\n      }\n    };\n    CTMJs2.__closure = {\n      computeClip,\n      ClipOp: _types.ClipOp,\n      processTransformProps2: _nodes.processTransformProps2\n    };\n    CTMJs2.__workletHash = 17552438720068;\n    CTMJs2.__initData = _worklet_17552438720068_init_data;\n    CTMJs2.__stackDetails = _e;\n    return CTMJs2;\n  }();\n});", "lineCount": 109, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_nodes"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_types"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 2, 54], [8, 8, 2, 54, "_worklet_15774729423578_init_data"], [8, 41, 2, 54], [9, 4, 2, 54, "code"], [9, 8, 2, 54], [10, 4, 2, 54, "location"], [10, 12, 2, 54], [11, 4, 2, 54, "sourceMap"], [11, 13, 2, 54], [12, 4, 2, 54, "version"], [12, 11, 2, 54], [13, 2, 2, 54], [14, 2, 3, 0], [14, 8, 3, 6, "computeClip"], [14, 19, 3, 17], [14, 22, 3, 20], [15, 4, 3, 20], [15, 10, 3, 20, "_e"], [15, 12, 3, 20], [15, 20, 3, 20, "global"], [15, 26, 3, 20], [15, 27, 3, 20, "Error"], [15, 32, 3, 20], [16, 4, 3, 20], [16, 10, 3, 20, "CTMJs1"], [16, 16, 3, 20], [16, 28, 3, 20, "CTMJs1"], [16, 29, 3, 21, "Skia"], [16, 33, 3, 25], [16, 35, 3, 27, "clip"], [16, 39, 3, 31], [16, 41, 3, 36], [17, 6, 6, 2], [17, 10, 6, 6, "clip"], [17, 14, 6, 10], [17, 16, 6, 12], [18, 8, 7, 4], [18, 12, 7, 8], [18, 16, 7, 8, "isPathDef"], [18, 32, 7, 17], [18, 34, 7, 18, "clip"], [18, 38, 7, 22], [18, 39, 7, 23], [18, 41, 7, 25], [19, 10, 8, 6], [19, 17, 8, 13], [20, 12, 9, 8, "clipPath"], [20, 20, 9, 16], [20, 22, 9, 18], [20, 26, 9, 18, "processPath"], [20, 44, 9, 29], [20, 46, 9, 30, "Skia"], [20, 50, 9, 34], [20, 52, 9, 36, "clip"], [20, 56, 9, 40], [21, 10, 10, 6], [21, 11, 10, 7], [22, 8, 11, 4], [22, 9, 11, 5], [22, 15, 11, 11], [22, 19, 11, 15], [22, 23, 11, 15, "isRRect"], [22, 37, 11, 22], [22, 39, 11, 23, "clip"], [22, 43, 11, 27], [22, 44, 11, 28], [22, 46, 11, 30], [23, 10, 12, 6], [23, 17, 12, 13], [24, 12, 13, 8, "clipRRect"], [24, 21, 13, 17], [24, 23, 13, 19, "clip"], [25, 10, 14, 6], [25, 11, 14, 7], [26, 8, 15, 4], [26, 9, 15, 5], [26, 15, 15, 11], [27, 10, 16, 6], [27, 17, 16, 13], [28, 12, 17, 8, "clipRect"], [28, 20, 17, 16], [28, 22, 17, 18, "clip"], [29, 10, 18, 6], [29, 11, 18, 7], [30, 8, 19, 4], [31, 6, 20, 2], [32, 6, 21, 2], [32, 13, 21, 9, "undefined"], [32, 22, 21, 18], [33, 4, 22, 0], [33, 5, 22, 1], [34, 4, 22, 1, "CTMJs1"], [34, 10, 22, 1], [34, 11, 22, 1, "__closure"], [34, 20, 22, 1], [35, 6, 22, 1, "isPathDef"], [35, 15, 22, 1], [35, 17, 7, 8, "isPathDef"], [35, 33, 7, 17], [36, 6, 7, 17, "processPath"], [36, 17, 7, 17], [36, 19, 9, 18, "processPath"], [36, 37, 9, 29], [37, 6, 9, 29, "isRRect"], [37, 13, 9, 29], [37, 15, 11, 15, "isRRect"], [38, 4, 11, 22], [39, 4, 11, 22, "CTMJs1"], [39, 10, 11, 22], [39, 11, 11, 22, "__workletHash"], [39, 24, 11, 22], [40, 4, 11, 22, "CTMJs1"], [40, 10, 11, 22], [40, 11, 11, 22, "__initData"], [40, 21, 11, 22], [40, 24, 11, 22, "_worklet_15774729423578_init_data"], [40, 57, 11, 22], [41, 4, 11, 22, "CTMJs1"], [41, 10, 11, 22], [41, 11, 11, 22, "__stackDetails"], [41, 25, 11, 22], [41, 28, 11, 22, "_e"], [41, 30, 11, 22], [42, 4, 11, 22], [42, 11, 11, 22, "CTMJs1"], [42, 17, 11, 22], [43, 2, 11, 22], [43, 3, 3, 20], [43, 5, 22, 1], [44, 2, 22, 2], [44, 8, 22, 2, "_worklet_17552438720068_init_data"], [44, 41, 22, 2], [45, 4, 22, 2, "code"], [45, 8, 22, 2], [46, 4, 22, 2, "location"], [46, 12, 22, 2], [47, 4, 22, 2, "sourceMap"], [47, 13, 22, 2], [48, 4, 22, 2, "version"], [48, 11, 22, 2], [49, 2, 22, 2], [50, 2, 23, 7], [50, 8, 23, 13, "saveCTM"], [50, 15, 23, 20], [50, 18, 23, 20, "exports"], [50, 25, 23, 20], [50, 26, 23, 20, "saveCTM"], [50, 33, 23, 20], [50, 36, 23, 23], [51, 4, 23, 23], [51, 10, 23, 23, "_e"], [51, 12, 23, 23], [51, 20, 23, 23, "global"], [51, 26, 23, 23], [51, 27, 23, 23, "Error"], [51, 32, 23, 23], [52, 4, 23, 23], [52, 10, 23, 23, "CTMJs2"], [52, 16, 23, 23], [52, 28, 23, 23, "CTMJs2"], [52, 29, 23, 24, "ctx"], [52, 32, 23, 27], [52, 34, 23, 29, "props"], [52, 39, 23, 34], [52, 41, 23, 39], [53, 6, 26, 2], [53, 12, 26, 8], [54, 8, 27, 4, "canvas"], [54, 14, 27, 10], [55, 8, 28, 4, "Skia"], [56, 6, 29, 2], [56, 7, 29, 3], [56, 10, 29, 6, "ctx"], [56, 13, 29, 9], [57, 6, 30, 2], [57, 12, 30, 8], [58, 8, 31, 4, "clip"], [58, 12, 31, 8], [58, 14, 31, 10, "rawClip"], [58, 21, 31, 17], [59, 8, 32, 4, "invertClip"], [59, 18, 32, 14], [60, 8, 33, 4, "matrix"], [60, 14, 33, 10], [61, 8, 34, 4, "transform"], [61, 17, 34, 13], [62, 8, 35, 4, "origin"], [62, 14, 35, 10], [63, 8, 36, 4, "layer"], [64, 6, 37, 2], [64, 7, 37, 3], [64, 10, 37, 6, "props"], [64, 15, 37, 11], [65, 6, 38, 2], [65, 12, 38, 8, "hasTransform"], [65, 24, 38, 20], [65, 27, 38, 23, "matrix"], [65, 33, 38, 29], [65, 38, 38, 34, "undefined"], [65, 47, 38, 43], [65, 51, 38, 47, "transform"], [65, 60, 38, 56], [65, 65, 38, 61, "undefined"], [65, 74, 38, 70], [66, 6, 39, 2], [66, 12, 39, 8, "clip"], [66, 16, 39, 12], [66, 19, 39, 15, "computeClip"], [66, 30, 39, 26], [66, 31, 39, 27, "Skia"], [66, 35, 39, 31], [66, 37, 39, 33, "rawClip"], [66, 44, 39, 40], [66, 45, 39, 41], [67, 6, 40, 2], [67, 12, 40, 8, "<PERSON><PERSON><PERSON>"], [67, 19, 40, 15], [67, 22, 40, 18, "clip"], [67, 26, 40, 22], [67, 31, 40, 27, "undefined"], [67, 40, 40, 36], [68, 6, 41, 2], [68, 12, 41, 8, "op"], [68, 14, 41, 10], [68, 17, 41, 13, "invertClip"], [68, 27, 41, 23], [68, 30, 41, 26, "ClipOp"], [68, 43, 41, 32], [68, 44, 41, 33, "Difference"], [68, 54, 41, 43], [68, 57, 41, 46, "ClipOp"], [68, 70, 41, 52], [68, 71, 41, 53, "Intersect"], [68, 80, 41, 62], [69, 6, 42, 2], [69, 12, 42, 8, "m3"], [69, 14, 42, 10], [69, 17, 42, 13], [69, 21, 42, 13, "processTransformProps2"], [69, 50, 42, 35], [69, 52, 42, 36, "Skia"], [69, 56, 42, 40], [69, 58, 42, 42], [70, 8, 43, 4, "matrix"], [70, 14, 43, 10], [71, 8, 44, 4, "transform"], [71, 17, 44, 13], [72, 8, 45, 4, "origin"], [73, 6, 46, 2], [73, 7, 46, 3], [73, 8, 46, 4], [74, 6, 47, 2], [74, 12, 47, 8, "shouldSave"], [74, 22, 47, 18], [74, 25, 47, 21, "hasTransform"], [74, 37, 47, 33], [74, 41, 47, 37, "<PERSON><PERSON><PERSON>"], [74, 48, 47, 44], [74, 52, 47, 48], [74, 53, 47, 49], [74, 54, 47, 50, "layer"], [74, 59, 47, 55], [75, 6, 48, 2], [75, 10, 48, 6, "shouldSave"], [75, 20, 48, 16], [75, 22, 48, 18], [76, 8, 49, 4], [76, 12, 49, 8, "layer"], [76, 17, 49, 13], [76, 19, 49, 15], [77, 10, 50, 6], [77, 14, 50, 10], [77, 21, 50, 17, "layer"], [77, 26, 50, 22], [77, 31, 50, 27], [77, 40, 50, 36], [77, 42, 50, 38], [78, 12, 51, 8, "canvas"], [78, 18, 51, 14], [78, 19, 51, 15, "save<PERSON><PERSON><PERSON>"], [78, 28, 51, 24], [78, 29, 51, 25], [78, 30, 51, 26], [79, 10, 52, 6], [79, 11, 52, 7], [79, 17, 52, 13], [80, 12, 53, 8, "canvas"], [80, 18, 53, 14], [80, 19, 53, 15, "save<PERSON><PERSON><PERSON>"], [80, 28, 53, 24], [80, 29, 53, 25, "layer"], [80, 34, 53, 30], [80, 35, 53, 31], [81, 10, 54, 6], [82, 8, 55, 4], [82, 9, 55, 5], [82, 15, 55, 11], [83, 10, 56, 6, "canvas"], [83, 16, 56, 12], [83, 17, 56, 13, "save"], [83, 21, 56, 17], [83, 22, 56, 18], [83, 23, 56, 19], [84, 8, 57, 4], [85, 6, 58, 2], [86, 6, 59, 2], [86, 10, 59, 6, "m3"], [86, 12, 59, 8], [86, 14, 59, 10], [87, 8, 60, 4, "canvas"], [87, 14, 60, 10], [87, 15, 60, 11, "concat"], [87, 21, 60, 17], [87, 22, 60, 18, "m3"], [87, 24, 60, 20], [87, 25, 60, 21], [88, 6, 61, 2], [89, 6, 62, 2], [89, 10, 62, 6, "clip"], [89, 14, 62, 10], [89, 16, 62, 12], [90, 8, 63, 4], [90, 12, 63, 8], [90, 22, 63, 18], [90, 26, 63, 22, "clip"], [90, 30, 63, 26], [90, 32, 63, 28], [91, 10, 64, 6, "canvas"], [91, 16, 64, 12], [91, 17, 64, 13, "clipRect"], [91, 25, 64, 21], [91, 26, 64, 22, "clip"], [91, 30, 64, 26], [91, 31, 64, 27, "clipRect"], [91, 39, 64, 35], [91, 41, 64, 37, "op"], [91, 43, 64, 39], [91, 45, 64, 41], [91, 49, 64, 45], [91, 50, 64, 46], [92, 8, 65, 4], [92, 9, 65, 5], [92, 15, 65, 11], [92, 19, 65, 15], [92, 30, 65, 26], [92, 34, 65, 30, "clip"], [92, 38, 65, 34], [92, 40, 65, 36], [93, 10, 66, 6, "canvas"], [93, 16, 66, 12], [93, 17, 66, 13, "clipRRect"], [93, 26, 66, 22], [93, 27, 66, 23, "clip"], [93, 31, 66, 27], [93, 32, 66, 28, "clipRRect"], [93, 41, 66, 37], [93, 43, 66, 39, "op"], [93, 45, 66, 41], [93, 47, 66, 43], [93, 51, 66, 47], [93, 52, 66, 48], [94, 8, 67, 4], [94, 9, 67, 5], [94, 15, 67, 11], [95, 10, 68, 6, "canvas"], [95, 16, 68, 12], [95, 17, 68, 13, "clipPath"], [95, 25, 68, 21], [95, 26, 68, 22, "clip"], [95, 30, 68, 26], [95, 31, 68, 27, "clipPath"], [95, 39, 68, 35], [95, 41, 68, 37, "op"], [95, 43, 68, 39], [95, 45, 68, 41], [95, 49, 68, 45], [95, 50, 68, 46], [96, 8, 69, 4], [97, 6, 70, 2], [98, 4, 71, 0], [98, 5, 71, 1], [99, 4, 71, 1, "CTMJs2"], [99, 10, 71, 1], [99, 11, 71, 1, "__closure"], [99, 20, 71, 1], [100, 6, 71, 1, "computeClip"], [100, 17, 71, 1], [101, 6, 71, 1, "ClipOp"], [101, 12, 71, 1], [101, 14, 41, 26, "ClipOp"], [101, 27, 41, 32], [102, 6, 41, 32, "processTransformProps2"], [102, 28, 41, 32], [102, 30, 42, 13, "processTransformProps2"], [103, 4, 42, 35], [104, 4, 42, 35, "CTMJs2"], [104, 10, 42, 35], [104, 11, 42, 35, "__workletHash"], [104, 24, 42, 35], [105, 4, 42, 35, "CTMJs2"], [105, 10, 42, 35], [105, 11, 42, 35, "__initData"], [105, 21, 42, 35], [105, 24, 42, 35, "_worklet_17552438720068_init_data"], [105, 57, 42, 35], [106, 4, 42, 35, "CTMJs2"], [106, 10, 42, 35], [106, 11, 42, 35, "__stackDetails"], [106, 25, 42, 35], [106, 28, 42, 35, "_e"], [106, 30, 42, 35], [107, 4, 42, 35], [107, 11, 42, 35, "CTMJs2"], [107, 17, 42, 35], [108, 2, 42, 35], [108, 3, 23, 23], [108, 5, 71, 1], [109, 0, 71, 2], [109, 3]], "functionMap": {"names": ["<global>", "computeClip", "saveCTM"], "mappings": "AAA;oBCE;CDmB;uBEC;CFgD"}}, "type": "js/module"}]}