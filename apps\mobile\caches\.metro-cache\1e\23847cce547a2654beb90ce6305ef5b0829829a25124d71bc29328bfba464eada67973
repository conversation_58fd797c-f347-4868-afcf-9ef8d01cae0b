{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.processRadius = void 0;\n  const _worklet_3456207825550_init_data = {\n    code: \"function RadiusJs1(Skia,radius){if(typeof radius===\\\"number\\\"){return Skia.Point(radius,radius);}return radius;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Radius.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"RadiusJs1\\\",\\\"Skia\\\",\\\"radius\\\",\\\"Point\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Radius.js\\\"],\\\"mappings\\\":\\\"AAA6B,QAAC,CAAAA,SAAMA,CAAAC,IAAM,CAAKC,MAAA,EAG7C,GAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CAC9B,MAAO,CAAAD,IAAI,CAACE,KAAK,CAACD,MAAM,CAAEA,MAAM,CAAC,CACnC,CACA,MAAO,CAAAA,MAAM,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processRadius = exports.processRadius = function () {\n    const _e = [new global.Error(), 1, -27];\n    const RadiusJs1 = function (Skia, radius) {\n      if (typeof radius === \"number\") {\n        return Skia.Point(radius, radius);\n      }\n      return radius;\n    };\n    RadiusJs1.__closure = {};\n    RadiusJs1.__workletHash = 3456207825550;\n    RadiusJs1.__initData = _worklet_3456207825550_init_data;\n    RadiusJs1.__stackDetails = _e;\n    return RadiusJs1;\n  }();\n});", "lineCount": 26, "map": [[12, 2, 1, 7], [12, 8, 1, 13, "processRadius"], [12, 21, 1, 26], [12, 24, 1, 26, "exports"], [12, 31, 1, 26], [12, 32, 1, 26, "processRadius"], [12, 45, 1, 26], [12, 48, 1, 29], [13, 4, 1, 29], [13, 10, 1, 29, "_e"], [13, 12, 1, 29], [13, 20, 1, 29, "global"], [13, 26, 1, 29], [13, 27, 1, 29, "Error"], [13, 32, 1, 29], [14, 4, 1, 29], [14, 10, 1, 29, "RadiusJs1"], [14, 19, 1, 29], [14, 31, 1, 29, "RadiusJs1"], [14, 32, 1, 30, "Skia"], [14, 36, 1, 34], [14, 38, 1, 36, "radius"], [14, 44, 1, 42], [14, 46, 1, 47], [15, 6, 4, 2], [15, 10, 4, 6], [15, 17, 4, 13, "radius"], [15, 23, 4, 19], [15, 28, 4, 24], [15, 36, 4, 32], [15, 38, 4, 34], [16, 8, 5, 4], [16, 15, 5, 11, "Skia"], [16, 19, 5, 15], [16, 20, 5, 16, "Point"], [16, 25, 5, 21], [16, 26, 5, 22, "radius"], [16, 32, 5, 28], [16, 34, 5, 30, "radius"], [16, 40, 5, 36], [16, 41, 5, 37], [17, 6, 6, 2], [18, 6, 7, 2], [18, 13, 7, 9, "radius"], [18, 19, 7, 15], [19, 4, 8, 0], [19, 5, 8, 1], [20, 4, 8, 1, "RadiusJs1"], [20, 13, 8, 1], [20, 14, 8, 1, "__closure"], [20, 23, 8, 1], [21, 4, 8, 1, "RadiusJs1"], [21, 13, 8, 1], [21, 14, 8, 1, "__workletHash"], [21, 27, 8, 1], [22, 4, 8, 1, "RadiusJs1"], [22, 13, 8, 1], [22, 14, 8, 1, "__initData"], [22, 24, 8, 1], [22, 27, 8, 1, "_worklet_3456207825550_init_data"], [22, 59, 8, 1], [23, 4, 8, 1, "RadiusJs1"], [23, 13, 8, 1], [23, 14, 8, 1, "__stackDetails"], [23, 28, 8, 1], [23, 31, 8, 1, "_e"], [23, 33, 8, 1], [24, 4, 8, 1], [24, 11, 8, 1, "RadiusJs1"], [24, 20, 8, 1], [25, 2, 8, 1], [25, 3, 1, 29], [25, 5, 8, 1], [26, 0, 8, 2], [26, 3]], "functionMap": {"names": ["<global>", "processRadius"], "mappings": "AAA,6BC;CDO"}}, "type": "js/module"}]}