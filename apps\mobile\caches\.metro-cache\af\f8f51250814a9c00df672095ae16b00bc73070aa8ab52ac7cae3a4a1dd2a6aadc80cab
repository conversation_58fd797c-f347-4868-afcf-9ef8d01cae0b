{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   *\n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation matches the working solution from test-blazeface-integration.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        return;\n      }\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size to match video dimensions when available\n      const updateCanvasSize = () => {\n        if (video.videoWidth && video.videoHeight) {\n          canvas.width = video.videoWidth;\n          canvas.height = video.videoHeight;\n          canvas.style.width = '100%';\n          canvas.style.height = '100%';\n          console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);\n        } else {\n          // Fallback to provided dimensions\n          canvas.width = width;\n          canvas.height = height;\n          console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);\n        }\n      };\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Helper function to load scripts\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n\n      // Load TensorFlow.js and BlazeFace model - matching working test implementation\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await window.blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Update canvas size once video is ready\n          updateCanvasSize();\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Check if video has valid dimensions\n          if (!video.videoWidth || !video.videoHeight) {\n            rafRef.current = requestAnimationFrame(detectLoop);\n            return;\n          }\n\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces with same confidence threshold as working test\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas and draw the original video frame first\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            setFaceCount(predictions.length);\n\n            // Process each detected face - REAL-TIME BLURRING like in test\n            predictions.forEach((prediction, index) => {\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect)\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply REAL-TIME elliptical blur - this creates the live preview blur\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(25px)'; // Stronger blur for better privacy\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n\n              // Add debug rectangle to show detection area (optional)\n              if (__DEV__) {\n                ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n                ctx.lineWidth = 2;\n                ctx.strokeRect(minX, minY, faceWidth, faceHeight);\n              }\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Wait for video to be ready before starting\n      const waitForVideoAndStart = () => {\n        if (video.readyState >= 2) {\n          // HAVE_CURRENT_DATA\n          loadModel();\n        } else {\n          video.addEventListener('loadeddata', loadModel, {\n            once: true\n          });\n        }\n      };\n\n      // Start the process\n      waitForVideoAndStart();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 15,\n          // Higher z-index to be above video\n          objectFit: 'cover',\n          backgroundColor: 'transparent'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(59, 130, 246, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(59, 130, 246, 0.3)'\n        },\n        children: \"Loading BlazeFace model...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(16, 185, 129, 0.3)'\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), !isLoading && faceCount === 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(107, 114, 128, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(107, 114, 128, 0.3)'\n        },\n        children: \"\\uD83D\\uDC40 Looking for faces...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 309, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 2, 17, 15], [18, 11, 17, 24, "BlazeFaceCanvas"], [18, 26, 17, 39, "BlazeFaceCanvas"], [18, 27, 17, 40], [19, 4, 17, 42, "containerId"], [19, 15, 17, 53], [20, 4, 17, 55, "width"], [20, 9, 17, 60], [21, 4, 17, 62, "height"], [22, 2, 17, 91], [22, 3, 17, 92], [22, 5, 17, 94], [23, 4, 17, 94, "_s"], [23, 6, 17, 94], [24, 4, 18, 2], [24, 10, 18, 8, "canvasRef"], [24, 19, 18, 17], [24, 22, 18, 20], [24, 26, 18, 20, "useRef"], [24, 39, 18, 26], [24, 41, 18, 53], [24, 45, 18, 57], [24, 46, 18, 58], [25, 4, 19, 2], [25, 10, 19, 8, "rafRef"], [25, 16, 19, 14], [25, 19, 19, 17], [25, 23, 19, 17, "useRef"], [25, 36, 19, 23], [25, 38, 19, 39], [25, 42, 19, 43], [25, 43, 19, 44], [26, 4, 20, 2], [26, 10, 20, 8, "modelRef"], [26, 18, 20, 16], [26, 21, 20, 19], [26, 25, 20, 19, "useRef"], [26, 38, 20, 25], [26, 40, 20, 38], [26, 44, 20, 42], [26, 45, 20, 43], [27, 4, 21, 2], [27, 10, 21, 8], [27, 11, 21, 9, "isLoading"], [27, 20, 21, 18], [27, 22, 21, 20, "setIsLoading"], [27, 34, 21, 32], [27, 35, 21, 33], [27, 38, 21, 36], [27, 42, 21, 36, "useState"], [27, 57, 21, 44], [27, 59, 21, 45], [27, 63, 21, 49], [27, 64, 21, 50], [28, 4, 22, 2], [28, 10, 22, 8], [28, 11, 22, 9, "faceCount"], [28, 20, 22, 18], [28, 22, 22, 20, "setFaceCount"], [28, 34, 22, 32], [28, 35, 22, 33], [28, 38, 22, 36], [28, 42, 22, 36, "useState"], [28, 57, 22, 44], [28, 59, 22, 45], [28, 60, 22, 46], [28, 61, 22, 47], [29, 4, 24, 2], [29, 8, 24, 2, "useEffect"], [29, 24, 24, 11], [29, 26, 24, 12], [29, 32, 24, 18], [30, 6, 25, 4, "console"], [30, 13, 25, 11], [30, 14, 25, 12, "log"], [30, 17, 25, 15], [30, 18, 25, 16], [30, 64, 25, 62], [30, 66, 25, 64], [31, 8, 25, 66, "containerId"], [31, 19, 25, 77], [32, 8, 25, 79, "width"], [32, 13, 25, 84], [33, 8, 25, 86, "height"], [34, 6, 25, 93], [34, 7, 25, 94], [34, 8, 25, 95], [35, 6, 27, 4], [35, 12, 27, 10, "container"], [35, 21, 27, 19], [35, 24, 27, 22, "document"], [35, 32, 27, 30], [35, 33, 27, 31, "getElementById"], [35, 47, 27, 45], [35, 48, 27, 46, "containerId"], [35, 59, 27, 57], [35, 60, 27, 58], [36, 6, 28, 4], [36, 10, 28, 8], [36, 11, 28, 9, "container"], [36, 20, 28, 18], [36, 22, 28, 20], [37, 8, 29, 6, "console"], [37, 15, 29, 13], [37, 16, 29, 14, "error"], [37, 21, 29, 19], [37, 22, 29, 20], [37, 62, 29, 60], [37, 64, 29, 62, "containerId"], [37, 75, 29, 73], [37, 76, 29, 74], [38, 8, 30, 6], [39, 6, 31, 4], [40, 6, 33, 4], [40, 12, 33, 10, "video"], [40, 17, 33, 40], [40, 20, 33, 43, "container"], [40, 29, 33, 52], [40, 30, 33, 53, "querySelector"], [40, 43, 33, 66], [40, 44, 33, 67], [40, 51, 33, 74], [40, 52, 33, 75], [41, 6, 34, 4], [41, 10, 34, 8], [41, 11, 34, 9, "video"], [41, 16, 34, 14], [41, 18, 34, 16], [42, 8, 35, 6, "console"], [42, 15, 35, 13], [42, 16, 35, 14, "error"], [42, 21, 35, 19], [42, 22, 35, 20], [42, 78, 35, 76], [42, 79, 35, 77], [43, 8, 36, 6], [44, 6, 37, 4], [45, 6, 39, 4], [45, 12, 39, 10, "canvas"], [45, 18, 39, 16], [45, 21, 39, 19, "canvasRef"], [45, 30, 39, 28], [45, 31, 39, 29, "current"], [45, 38, 39, 36], [46, 6, 40, 4], [46, 10, 40, 8], [46, 11, 40, 9, "canvas"], [46, 17, 40, 15], [46, 19, 40, 17], [47, 8, 41, 6, "console"], [47, 15, 41, 13], [47, 16, 41, 14, "error"], [47, 21, 41, 19], [47, 22, 41, 20], [47, 66, 41, 64], [47, 67, 41, 65], [48, 8, 42, 6], [49, 6, 43, 4], [51, 6, 45, 4], [52, 6, 46, 4], [52, 12, 46, 10, "updateCanvasSize"], [52, 28, 46, 26], [52, 31, 46, 29, "updateCanvasSize"], [52, 32, 46, 29], [52, 37, 46, 35], [53, 8, 47, 6], [53, 12, 47, 10, "video"], [53, 17, 47, 15], [53, 18, 47, 16, "videoWidth"], [53, 28, 47, 26], [53, 32, 47, 30, "video"], [53, 37, 47, 35], [53, 38, 47, 36, "videoHeight"], [53, 49, 47, 47], [53, 51, 47, 49], [54, 10, 48, 8, "canvas"], [54, 16, 48, 14], [54, 17, 48, 15, "width"], [54, 22, 48, 20], [54, 25, 48, 23, "video"], [54, 30, 48, 28], [54, 31, 48, 29, "videoWidth"], [54, 41, 48, 39], [55, 10, 49, 8, "canvas"], [55, 16, 49, 14], [55, 17, 49, 15, "height"], [55, 23, 49, 21], [55, 26, 49, 24, "video"], [55, 31, 49, 29], [55, 32, 49, 30, "videoHeight"], [55, 43, 49, 41], [56, 10, 50, 8, "canvas"], [56, 16, 50, 14], [56, 17, 50, 15, "style"], [56, 22, 50, 20], [56, 23, 50, 21, "width"], [56, 28, 50, 26], [56, 31, 50, 29], [56, 37, 50, 35], [57, 10, 51, 8, "canvas"], [57, 16, 51, 14], [57, 17, 51, 15, "style"], [57, 22, 51, 20], [57, 23, 51, 21, "height"], [57, 29, 51, 27], [57, 32, 51, 30], [57, 38, 51, 36], [58, 10, 52, 8, "console"], [58, 17, 52, 15], [58, 18, 52, 16, "log"], [58, 21, 52, 19], [58, 22, 52, 20], [58, 72, 52, 70], [58, 74, 52, 72, "video"], [58, 79, 52, 77], [58, 80, 52, 78, "videoWidth"], [58, 90, 52, 88], [58, 92, 52, 90], [58, 95, 52, 93], [58, 97, 52, 95, "video"], [58, 102, 52, 100], [58, 103, 52, 101, "videoHeight"], [58, 114, 52, 112], [58, 115, 52, 113], [59, 8, 53, 6], [59, 9, 53, 7], [59, 15, 53, 13], [60, 10, 54, 8], [61, 10, 55, 8, "canvas"], [61, 16, 55, 14], [61, 17, 55, 15, "width"], [61, 22, 55, 20], [61, 25, 55, 23, "width"], [61, 30, 55, 28], [62, 10, 56, 8, "canvas"], [62, 16, 56, 14], [62, 17, 56, 15, "height"], [62, 23, 56, 21], [62, 26, 56, 24, "height"], [62, 32, 56, 30], [63, 10, 57, 8, "console"], [63, 17, 57, 15], [63, 18, 57, 16, "log"], [63, 21, 57, 19], [63, 22, 57, 20], [63, 80, 57, 78], [63, 82, 57, 80, "width"], [63, 87, 57, 85], [63, 89, 57, 87], [63, 92, 57, 90], [63, 94, 57, 92, "height"], [63, 100, 57, 98], [63, 101, 57, 99], [64, 8, 58, 6], [65, 6, 59, 4], [65, 7, 59, 5], [66, 6, 61, 4], [66, 12, 61, 10, "ctx"], [66, 15, 61, 13], [66, 18, 61, 16, "canvas"], [66, 24, 61, 22], [66, 25, 61, 23, "getContext"], [66, 35, 61, 33], [66, 36, 61, 34], [66, 40, 61, 38], [66, 41, 61, 39], [67, 6, 62, 4], [67, 10, 62, 8], [67, 11, 62, 9, "ctx"], [67, 14, 62, 12], [67, 16, 62, 14], [68, 8, 63, 6, "console"], [68, 15, 63, 13], [68, 16, 63, 14, "error"], [68, 21, 63, 19], [68, 22, 63, 20], [68, 70, 63, 68], [68, 71, 63, 69], [69, 8, 64, 6], [70, 6, 65, 4], [71, 6, 67, 4], [71, 10, 67, 8, "isDetecting"], [71, 21, 67, 19], [71, 24, 67, 22], [71, 28, 67, 26], [73, 6, 69, 4], [74, 6, 70, 4], [74, 12, 70, 10, "loadScript"], [74, 22, 70, 20], [74, 25, 70, 24, "src"], [74, 28, 70, 35], [74, 32, 70, 55], [75, 8, 71, 6], [75, 15, 71, 13], [75, 19, 71, 17, "Promise"], [75, 26, 71, 24], [75, 27, 71, 25], [75, 28, 71, 26, "resolve"], [75, 35, 71, 33], [75, 37, 71, 35, "reject"], [75, 43, 71, 41], [75, 48, 71, 46], [76, 10, 72, 8], [76, 16, 72, 14, "script"], [76, 22, 72, 20], [76, 25, 72, 23, "document"], [76, 33, 72, 31], [76, 34, 72, 32, "createElement"], [76, 47, 72, 45], [76, 48, 72, 46], [76, 56, 72, 54], [76, 57, 72, 55], [77, 10, 73, 8, "script"], [77, 16, 73, 14], [77, 17, 73, 15, "src"], [77, 20, 73, 18], [77, 23, 73, 21, "src"], [77, 26, 73, 24], [78, 10, 74, 8, "script"], [78, 16, 74, 14], [78, 17, 74, 15, "onload"], [78, 23, 74, 21], [78, 26, 74, 24], [78, 32, 74, 30, "resolve"], [78, 39, 74, 37], [78, 40, 74, 38], [78, 41, 74, 39], [79, 10, 75, 8, "script"], [79, 16, 75, 14], [79, 17, 75, 15, "onerror"], [79, 24, 75, 22], [79, 27, 75, 25], [79, 33, 75, 31, "reject"], [79, 39, 75, 37], [79, 40, 75, 38], [79, 44, 75, 42, "Error"], [79, 49, 75, 47], [79, 50, 75, 48], [79, 68, 75, 66, "src"], [79, 71, 75, 69], [79, 73, 75, 71], [79, 74, 75, 72], [79, 75, 75, 73], [80, 10, 76, 8, "document"], [80, 18, 76, 16], [80, 19, 76, 17, "head"], [80, 23, 76, 21], [80, 24, 76, 22, "append<PERSON><PERSON><PERSON>"], [80, 35, 76, 33], [80, 36, 76, 34, "script"], [80, 42, 76, 40], [80, 43, 76, 41], [81, 8, 77, 6], [81, 9, 77, 7], [81, 10, 77, 8], [82, 6, 78, 4], [82, 7, 78, 5], [84, 6, 80, 4], [85, 6, 81, 4], [85, 12, 81, 10, "loadModel"], [85, 21, 81, 19], [85, 24, 81, 22], [85, 30, 81, 22, "loadModel"], [85, 31, 81, 22], [85, 36, 81, 34], [86, 8, 82, 6], [86, 12, 82, 10], [87, 10, 83, 8, "console"], [87, 17, 83, 15], [87, 18, 83, 16, "log"], [87, 21, 83, 19], [87, 22, 83, 20], [87, 66, 83, 64], [87, 67, 83, 65], [89, 10, 85, 8], [90, 10, 86, 8], [90, 14, 86, 12], [90, 15, 86, 14, "window"], [90, 21, 86, 20], [90, 22, 86, 29, "tf"], [90, 24, 86, 31], [90, 26, 86, 33], [91, 12, 87, 10], [91, 18, 87, 16, "loadScript"], [91, 28, 87, 26], [91, 29, 87, 27], [91, 98, 87, 96], [91, 99, 87, 97], [92, 10, 88, 8], [93, 10, 90, 8, "console"], [93, 17, 90, 15], [93, 18, 90, 16, "log"], [93, 21, 90, 19], [93, 22, 90, 20], [93, 68, 90, 66], [93, 69, 90, 67], [95, 10, 92, 8], [96, 10, 93, 8], [96, 14, 93, 12], [96, 15, 93, 14, "window"], [96, 21, 93, 20], [96, 22, 93, 29, "blazeface"], [96, 31, 93, 38], [96, 33, 93, 40], [97, 12, 94, 10], [97, 18, 94, 16, "loadScript"], [97, 28, 94, 26], [97, 29, 94, 27], [97, 112, 94, 110], [97, 113, 94, 111], [98, 10, 95, 8], [100, 10, 97, 8], [101, 10, 98, 8, "console"], [101, 17, 98, 15], [101, 18, 98, 16, "log"], [101, 21, 98, 19], [101, 22, 98, 20], [101, 73, 98, 71], [101, 74, 98, 72], [102, 10, 99, 8, "modelRef"], [102, 18, 99, 16], [102, 19, 99, 17, "current"], [102, 26, 99, 24], [102, 29, 99, 27], [102, 35, 99, 34, "window"], [102, 41, 99, 40], [102, 42, 99, 49, "blazeface"], [102, 51, 99, 58], [102, 52, 99, 59, "load"], [102, 56, 99, 63], [102, 57, 99, 64], [102, 58, 99, 65], [103, 10, 100, 8, "console"], [103, 17, 100, 15], [103, 18, 100, 16, "log"], [103, 21, 100, 19], [103, 22, 100, 20], [103, 79, 100, 77], [103, 80, 100, 78], [104, 10, 102, 8, "setIsLoading"], [104, 22, 102, 20], [104, 23, 102, 21], [104, 28, 102, 26], [104, 29, 102, 27], [106, 10, 104, 8], [107, 10, 105, 8, "updateCanvasSize"], [107, 26, 105, 24], [107, 27, 105, 25], [107, 28, 105, 26], [109, 10, 107, 8], [110, 10, 108, 8, "detectLoop"], [110, 20, 108, 18], [110, 21, 108, 19], [110, 22, 108, 20], [111, 8, 110, 6], [111, 9, 110, 7], [111, 10, 110, 8], [111, 17, 110, 15, "error"], [111, 22, 110, 20], [111, 24, 110, 22], [112, 10, 111, 8, "console"], [112, 17, 111, 15], [112, 18, 111, 16, "error"], [112, 23, 111, 21], [112, 24, 111, 22], [112, 67, 111, 65], [112, 69, 111, 67, "error"], [112, 74, 111, 72], [112, 75, 111, 73], [113, 10, 112, 8, "setIsLoading"], [113, 22, 112, 20], [113, 23, 112, 21], [113, 28, 112, 26], [113, 29, 112, 27], [114, 8, 113, 6], [115, 6, 114, 4], [115, 7, 114, 5], [116, 6, 116, 4], [116, 12, 116, 10, "detectLoop"], [116, 22, 116, 20], [116, 25, 116, 23], [116, 31, 116, 23, "detectLoop"], [116, 32, 116, 23], [116, 37, 116, 35], [117, 8, 117, 6], [117, 12, 117, 10], [117, 13, 117, 11, "isDetecting"], [117, 24, 117, 22], [117, 28, 117, 26], [117, 29, 117, 27, "modelRef"], [117, 37, 117, 35], [117, 38, 117, 36, "current"], [117, 45, 117, 43], [117, 47, 117, 45], [118, 8, 119, 6], [118, 12, 119, 10], [119, 10, 120, 8], [120, 10, 121, 8], [120, 14, 121, 12], [120, 15, 121, 13, "video"], [120, 20, 121, 18], [120, 21, 121, 19, "videoWidth"], [120, 31, 121, 29], [120, 35, 121, 33], [120, 36, 121, 34, "video"], [120, 41, 121, 39], [120, 42, 121, 40, "videoHeight"], [120, 53, 121, 51], [120, 55, 121, 53], [121, 12, 122, 10, "rafRef"], [121, 18, 122, 16], [121, 19, 122, 17, "current"], [121, 26, 122, 24], [121, 29, 122, 27, "requestAnimationFrame"], [121, 50, 122, 48], [121, 51, 122, 49, "detectLoop"], [121, 61, 122, 59], [121, 62, 122, 60], [122, 12, 123, 10], [123, 10, 124, 8], [125, 10, 126, 8], [126, 10, 127, 8], [126, 16, 127, 14, "tf"], [126, 18, 127, 16], [126, 21, 127, 20, "window"], [126, 27, 127, 26], [126, 28, 127, 35, "tf"], [126, 30, 127, 37], [127, 10, 128, 8], [127, 16, 128, 14, "tensor"], [127, 22, 128, 20], [127, 25, 128, 23, "tf"], [127, 27, 128, 25], [127, 28, 128, 26, "browser"], [127, 35, 128, 33], [127, 36, 128, 34, "fromPixels"], [127, 46, 128, 44], [127, 47, 128, 45, "video"], [127, 52, 128, 50], [127, 53, 128, 51], [129, 10, 130, 8], [130, 10, 131, 8], [130, 16, 131, 14, "predictions"], [130, 27, 131, 25], [130, 30, 131, 28], [130, 36, 131, 34, "modelRef"], [130, 44, 131, 42], [130, 45, 131, 43, "current"], [130, 52, 131, 50], [130, 53, 131, 51, "estimateFaces"], [130, 66, 131, 64], [130, 67, 131, 65, "tensor"], [130, 73, 131, 71], [130, 75, 131, 73], [130, 80, 131, 78], [130, 82, 131, 80], [130, 85, 131, 83], [130, 86, 131, 84], [131, 10, 132, 8, "tensor"], [131, 16, 132, 14], [131, 17, 132, 15, "dispose"], [131, 24, 132, 22], [131, 25, 132, 23], [131, 26, 132, 24], [133, 10, 134, 8], [134, 10, 135, 8, "ctx"], [134, 13, 135, 11], [134, 14, 135, 12, "clearRect"], [134, 23, 135, 21], [134, 24, 135, 22], [134, 25, 135, 23], [134, 27, 135, 25], [134, 28, 135, 26], [134, 30, 135, 28, "canvas"], [134, 36, 135, 34], [134, 37, 135, 35, "width"], [134, 42, 135, 40], [134, 44, 135, 42, "canvas"], [134, 50, 135, 48], [134, 51, 135, 49, "height"], [134, 57, 135, 55], [134, 58, 135, 56], [135, 10, 136, 8, "ctx"], [135, 13, 136, 11], [135, 14, 136, 12, "drawImage"], [135, 23, 136, 21], [135, 24, 136, 22, "video"], [135, 29, 136, 27], [135, 31, 136, 29], [135, 32, 136, 30], [135, 34, 136, 32], [135, 35, 136, 33], [135, 37, 136, 35, "canvas"], [135, 43, 136, 41], [135, 44, 136, 42, "width"], [135, 49, 136, 47], [135, 51, 136, 49, "canvas"], [135, 57, 136, 55], [135, 58, 136, 56, "height"], [135, 64, 136, 62], [135, 65, 136, 63], [136, 10, 138, 8], [136, 14, 138, 12, "predictions"], [136, 25, 138, 23], [136, 26, 138, 24, "length"], [136, 32, 138, 30], [136, 35, 138, 33], [136, 36, 138, 34], [136, 38, 138, 36], [137, 12, 139, 10, "setFaceCount"], [137, 24, 139, 22], [137, 25, 139, 23, "predictions"], [137, 36, 139, 34], [137, 37, 139, 35, "length"], [137, 43, 139, 41], [137, 44, 139, 42], [139, 12, 141, 10], [140, 12, 142, 10, "predictions"], [140, 23, 142, 21], [140, 24, 142, 22, "for<PERSON>ach"], [140, 31, 142, 29], [140, 32, 142, 30], [140, 33, 142, 31, "prediction"], [140, 43, 142, 46], [140, 45, 142, 48, "index"], [140, 50, 142, 61], [140, 55, 142, 66], [141, 14, 143, 12], [141, 20, 143, 18], [141, 21, 143, 19, "x1"], [141, 23, 143, 21], [141, 25, 143, 23, "y1"], [141, 27, 143, 25], [141, 28, 143, 26], [141, 31, 143, 29, "prediction"], [141, 41, 143, 39], [141, 42, 143, 40, "topLeft"], [141, 49, 143, 47], [142, 14, 144, 12], [142, 20, 144, 18], [142, 21, 144, 19, "x2"], [142, 23, 144, 21], [142, 25, 144, 23, "y2"], [142, 27, 144, 25], [142, 28, 144, 26], [142, 31, 144, 29, "prediction"], [142, 41, 144, 39], [142, 42, 144, 40, "bottomRight"], [142, 53, 144, 51], [144, 14, 146, 12], [145, 14, 147, 12], [145, 18, 147, 16, "minX"], [145, 22, 147, 20], [145, 25, 147, 23, "Math"], [145, 29, 147, 27], [145, 30, 147, 28, "min"], [145, 33, 147, 31], [145, 34, 147, 32, "x1"], [145, 36, 147, 34], [145, 38, 147, 36, "x2"], [145, 40, 147, 38], [145, 41, 147, 39], [146, 14, 148, 12], [146, 18, 148, 16, "maxX"], [146, 22, 148, 20], [146, 25, 148, 23, "Math"], [146, 29, 148, 27], [146, 30, 148, 28, "max"], [146, 33, 148, 31], [146, 34, 148, 32, "x1"], [146, 36, 148, 34], [146, 38, 148, 36, "x2"], [146, 40, 148, 38], [146, 41, 148, 39], [147, 14, 149, 12], [147, 20, 149, 18, "minY"], [147, 24, 149, 22], [147, 27, 149, 25, "Math"], [147, 31, 149, 29], [147, 32, 149, 30, "min"], [147, 35, 149, 33], [147, 36, 149, 34, "y1"], [147, 38, 149, 36], [147, 40, 149, 38, "y2"], [147, 42, 149, 40], [147, 43, 149, 41], [148, 14, 150, 12], [148, 20, 150, 18, "maxY"], [148, 24, 150, 22], [148, 27, 150, 25, "Math"], [148, 31, 150, 29], [148, 32, 150, 30, "max"], [148, 35, 150, 33], [148, 36, 150, 34, "y1"], [148, 38, 150, 36], [148, 40, 150, 38, "y2"], [148, 42, 150, 40], [148, 43, 150, 41], [150, 14, 152, 12], [151, 14, 153, 12], [151, 20, 153, 18, "canvasWidth"], [151, 31, 153, 29], [151, 34, 153, 32, "canvas"], [151, 40, 153, 38], [151, 41, 153, 39, "width"], [151, 46, 153, 44], [152, 14, 154, 12], [152, 20, 154, 18, "flippedMinX"], [152, 31, 154, 29], [152, 34, 154, 32, "canvasWidth"], [152, 45, 154, 43], [152, 48, 154, 46, "maxX"], [152, 52, 154, 50], [153, 14, 155, 12], [153, 20, 155, 18, "flippedMaxX"], [153, 31, 155, 29], [153, 34, 155, 32, "canvasWidth"], [153, 45, 155, 43], [153, 48, 155, 46, "minX"], [153, 52, 155, 50], [154, 14, 156, 12, "minX"], [154, 18, 156, 16], [154, 21, 156, 19, "flippedMinX"], [154, 32, 156, 30], [155, 14, 157, 12, "maxX"], [155, 18, 157, 16], [155, 21, 157, 19, "flippedMaxX"], [155, 32, 157, 30], [157, 14, 159, 12], [158, 14, 160, 12], [158, 20, 160, 18, "faceWidth"], [158, 29, 160, 27], [158, 32, 160, 30, "maxX"], [158, 36, 160, 34], [158, 39, 160, 37, "minX"], [158, 43, 160, 41], [159, 14, 161, 12], [159, 20, 161, 18, "faceHeight"], [159, 30, 161, 28], [159, 33, 161, 31, "maxY"], [159, 37, 161, 35], [159, 40, 161, 38, "minY"], [159, 44, 161, 42], [160, 14, 163, 12], [160, 18, 163, 16, "faceWidth"], [160, 27, 163, 25], [160, 31, 163, 29], [160, 32, 163, 30], [160, 36, 163, 34, "faceHeight"], [160, 46, 163, 44], [160, 50, 163, 48], [160, 51, 163, 49], [160, 53, 163, 51], [161, 16, 164, 14], [162, 14, 165, 12], [164, 14, 167, 12], [165, 14, 168, 12], [165, 20, 168, 18, "centerX"], [165, 27, 168, 25], [165, 30, 168, 28], [165, 31, 168, 29, "minX"], [165, 35, 168, 33], [165, 38, 168, 36, "maxX"], [165, 42, 168, 40], [165, 46, 168, 44], [165, 47, 168, 45], [166, 14, 169, 12], [166, 20, 169, 18, "centerY"], [166, 27, 169, 25], [166, 30, 169, 28], [166, 31, 169, 29, "minY"], [166, 35, 169, 33], [166, 38, 169, 36, "maxY"], [166, 42, 169, 40], [166, 46, 169, 44], [166, 47, 169, 45], [167, 14, 170, 12], [167, 20, 170, 18, "expandedWidth"], [167, 33, 170, 31], [167, 36, 170, 34, "faceWidth"], [167, 45, 170, 43], [167, 48, 170, 46], [167, 51, 170, 49], [168, 14, 171, 12], [168, 20, 171, 18, "expandedHeight"], [168, 34, 171, 32], [168, 37, 171, 35, "faceHeight"], [168, 47, 171, 45], [168, 50, 171, 48], [168, 53, 171, 51], [170, 14, 173, 12], [171, 14, 174, 12], [171, 20, 174, 18, "radiusX"], [171, 27, 174, 25], [171, 30, 174, 28, "Math"], [171, 34, 174, 32], [171, 35, 174, 33, "max"], [171, 38, 174, 36], [171, 39, 174, 37, "expandedWidth"], [171, 52, 174, 50], [171, 55, 174, 53], [171, 56, 174, 54], [171, 58, 174, 56], [171, 60, 174, 58], [171, 61, 174, 59], [172, 14, 175, 12], [172, 20, 175, 18, "radiusY"], [172, 27, 175, 25], [172, 30, 175, 28, "Math"], [172, 34, 175, 32], [172, 35, 175, 33, "max"], [172, 38, 175, 36], [172, 39, 175, 37, "expandedHeight"], [172, 53, 175, 51], [172, 56, 175, 54], [172, 57, 175, 55], [172, 59, 175, 57], [172, 61, 175, 59], [172, 62, 175, 60], [174, 14, 177, 12], [175, 14, 178, 12, "ctx"], [175, 17, 178, 15], [175, 18, 178, 16, "save"], [175, 22, 178, 20], [175, 23, 178, 21], [175, 24, 178, 22], [176, 14, 179, 12, "ctx"], [176, 17, 179, 15], [176, 18, 179, 16, "beginPath"], [176, 27, 179, 25], [176, 28, 179, 26], [176, 29, 179, 27], [177, 14, 180, 12, "ctx"], [177, 17, 180, 15], [177, 18, 180, 16, "ellipse"], [177, 25, 180, 23], [177, 26, 180, 24, "centerX"], [177, 33, 180, 31], [177, 35, 180, 33, "centerY"], [177, 42, 180, 40], [177, 44, 180, 42, "radiusX"], [177, 51, 180, 49], [177, 53, 180, 51, "radiusY"], [177, 60, 180, 58], [177, 62, 180, 60], [177, 63, 180, 61], [177, 65, 180, 63], [177, 66, 180, 64], [177, 68, 180, 66, "Math"], [177, 72, 180, 70], [177, 73, 180, 71, "PI"], [177, 75, 180, 73], [177, 78, 180, 76], [177, 79, 180, 77], [177, 80, 180, 78], [178, 14, 181, 12, "ctx"], [178, 17, 181, 15], [178, 18, 181, 16, "clip"], [178, 22, 181, 20], [178, 23, 181, 21], [178, 24, 181, 22], [179, 14, 182, 12, "ctx"], [179, 17, 182, 15], [179, 18, 182, 16, "filter"], [179, 24, 182, 22], [179, 27, 182, 25], [179, 39, 182, 37], [179, 40, 182, 38], [179, 41, 182, 39], [180, 14, 183, 12, "ctx"], [180, 17, 183, 15], [180, 18, 183, 16, "drawImage"], [180, 27, 183, 25], [180, 28, 183, 26, "video"], [180, 33, 183, 31], [180, 35, 183, 33], [180, 36, 183, 34], [180, 38, 183, 36], [180, 39, 183, 37], [180, 41, 183, 39, "canvas"], [180, 47, 183, 45], [180, 48, 183, 46, "width"], [180, 53, 183, 51], [180, 55, 183, 53, "canvas"], [180, 61, 183, 59], [180, 62, 183, 60, "height"], [180, 68, 183, 66], [180, 69, 183, 67], [181, 14, 184, 12, "ctx"], [181, 17, 184, 15], [181, 18, 184, 16, "restore"], [181, 25, 184, 23], [181, 26, 184, 24], [181, 27, 184, 25], [183, 14, 186, 12], [184, 14, 187, 12], [184, 18, 187, 16, "__DEV__"], [184, 25, 187, 23], [184, 27, 187, 25], [185, 16, 188, 14, "ctx"], [185, 19, 188, 17], [185, 20, 188, 18, "strokeStyle"], [185, 31, 188, 29], [185, 34, 188, 32], [185, 56, 188, 54], [186, 16, 189, 14, "ctx"], [186, 19, 189, 17], [186, 20, 189, 18, "lineWidth"], [186, 29, 189, 27], [186, 32, 189, 30], [186, 33, 189, 31], [187, 16, 190, 14, "ctx"], [187, 19, 190, 17], [187, 20, 190, 18, "strokeRect"], [187, 30, 190, 28], [187, 31, 190, 29, "minX"], [187, 35, 190, 33], [187, 37, 190, 35, "minY"], [187, 41, 190, 39], [187, 43, 190, 41, "faceWidth"], [187, 52, 190, 50], [187, 54, 190, 52, "faceHeight"], [187, 64, 190, 62], [187, 65, 190, 63], [188, 14, 191, 12], [189, 12, 192, 10], [189, 13, 192, 11], [189, 14, 192, 12], [190, 10, 193, 8], [190, 11, 193, 9], [190, 17, 193, 15], [191, 12, 194, 10, "setFaceCount"], [191, 24, 194, 22], [191, 25, 194, 23], [191, 26, 194, 24], [191, 27, 194, 25], [192, 10, 195, 8], [193, 8, 197, 6], [193, 9, 197, 7], [193, 10, 197, 8], [193, 17, 197, 15, "error"], [193, 22, 197, 20], [193, 24, 197, 22], [194, 10, 198, 8, "console"], [194, 17, 198, 15], [194, 18, 198, 16, "error"], [194, 23, 198, 21], [194, 24, 198, 22], [194, 60, 198, 58], [194, 62, 198, 60, "error"], [194, 67, 198, 65], [194, 68, 198, 66], [195, 8, 199, 6], [197, 8, 201, 6], [198, 8, 202, 6], [198, 12, 202, 10, "isDetecting"], [198, 23, 202, 21], [198, 25, 202, 23], [199, 10, 203, 8, "rafRef"], [199, 16, 203, 14], [199, 17, 203, 15, "current"], [199, 24, 203, 22], [199, 27, 203, 25, "requestAnimationFrame"], [199, 48, 203, 46], [199, 49, 203, 47, "detectLoop"], [199, 59, 203, 57], [199, 60, 203, 58], [200, 8, 204, 6], [201, 6, 205, 4], [201, 7, 205, 5], [203, 6, 207, 4], [204, 6, 208, 4], [204, 12, 208, 10, "waitForVideoAndStart"], [204, 32, 208, 30], [204, 35, 208, 33, "waitForVideoAndStart"], [204, 36, 208, 33], [204, 41, 208, 39], [205, 8, 209, 6], [205, 12, 209, 10, "video"], [205, 17, 209, 15], [205, 18, 209, 16, "readyState"], [205, 28, 209, 26], [205, 32, 209, 30], [205, 33, 209, 31], [205, 35, 209, 33], [206, 10, 209, 35], [207, 10, 210, 8, "loadModel"], [207, 19, 210, 17], [207, 20, 210, 18], [207, 21, 210, 19], [208, 8, 211, 6], [208, 9, 211, 7], [208, 15, 211, 13], [209, 10, 212, 8, "video"], [209, 15, 212, 13], [209, 16, 212, 14, "addEventListener"], [209, 32, 212, 30], [209, 33, 212, 31], [209, 45, 212, 43], [209, 47, 212, 45, "loadModel"], [209, 56, 212, 54], [209, 58, 212, 56], [210, 12, 212, 58, "once"], [210, 16, 212, 62], [210, 18, 212, 64], [211, 10, 212, 69], [211, 11, 212, 70], [211, 12, 212, 71], [212, 8, 213, 6], [213, 6, 214, 4], [213, 7, 214, 5], [215, 6, 216, 4], [216, 6, 217, 4, "waitForVideoAndStart"], [216, 26, 217, 24], [216, 27, 217, 25], [216, 28, 217, 26], [218, 6, 219, 4], [219, 6, 220, 4], [219, 13, 220, 11], [219, 19, 220, 17], [220, 8, 221, 6, "isDetecting"], [220, 19, 221, 17], [220, 22, 221, 20], [220, 27, 221, 25], [221, 8, 222, 6], [221, 12, 222, 10, "rafRef"], [221, 18, 222, 16], [221, 19, 222, 17, "current"], [221, 26, 222, 24], [221, 28, 222, 26], [222, 10, 223, 8, "cancelAnimationFrame"], [222, 30, 223, 28], [222, 31, 223, 29, "rafRef"], [222, 37, 223, 35], [222, 38, 223, 36, "current"], [222, 45, 223, 43], [222, 46, 223, 44], [223, 8, 224, 6], [224, 6, 225, 4], [224, 7, 225, 5], [225, 4, 226, 2], [225, 5, 226, 3], [225, 7, 226, 5], [225, 8, 226, 6, "containerId"], [225, 19, 226, 17], [225, 21, 226, 19, "width"], [225, 26, 226, 24], [225, 28, 226, 26, "height"], [225, 34, 226, 32], [225, 35, 226, 33], [225, 36, 226, 34], [226, 4, 228, 2], [226, 24, 229, 4], [226, 28, 229, 4, "_jsxDevRuntime"], [226, 42, 229, 4], [226, 43, 229, 4, "jsxDEV"], [226, 49, 229, 4], [226, 51, 229, 4, "_jsxDevRuntime"], [226, 65, 229, 4], [226, 66, 229, 4, "Fragment"], [226, 74, 229, 4], [227, 6, 229, 4, "children"], [227, 14, 229, 4], [227, 30, 230, 6], [227, 34, 230, 6, "_jsxDevRuntime"], [227, 48, 230, 6], [227, 49, 230, 6, "jsxDEV"], [227, 55, 230, 6], [228, 8, 231, 8, "ref"], [228, 11, 231, 11], [228, 13, 231, 13, "canvasRef"], [228, 22, 231, 23], [229, 8, 232, 8, "style"], [229, 13, 232, 13], [229, 15, 232, 15], [230, 10, 233, 10, "position"], [230, 18, 233, 18], [230, 20, 233, 20], [230, 30, 233, 30], [231, 10, 234, 10, "left"], [231, 14, 234, 14], [231, 16, 234, 16], [231, 17, 234, 17], [232, 10, 235, 10, "top"], [232, 13, 235, 13], [232, 15, 235, 15], [232, 16, 235, 16], [233, 10, 236, 10, "width"], [233, 15, 236, 15], [233, 17, 236, 17], [233, 23, 236, 23], [234, 10, 237, 10, "height"], [234, 16, 237, 16], [234, 18, 237, 18], [234, 24, 237, 24], [235, 10, 238, 10, "pointerEvents"], [235, 23, 238, 23], [235, 25, 238, 25], [235, 31, 238, 31], [236, 10, 239, 10, "zIndex"], [236, 16, 239, 16], [236, 18, 239, 18], [236, 20, 239, 20], [237, 10, 239, 22], [238, 10, 240, 10, "objectFit"], [238, 19, 240, 19], [238, 21, 240, 21], [238, 28, 240, 28], [239, 10, 241, 10, "backgroundColor"], [239, 25, 241, 25], [239, 27, 241, 27], [240, 8, 242, 8], [241, 6, 242, 10], [242, 8, 242, 10, "fileName"], [242, 16, 242, 10], [242, 18, 242, 10, "_jsxFileName"], [242, 30, 242, 10], [243, 8, 242, 10, "lineNumber"], [243, 18, 242, 10], [244, 8, 242, 10, "columnNumber"], [244, 20, 242, 10], [245, 6, 242, 10], [245, 13, 243, 7], [245, 14, 243, 8], [245, 16, 245, 7, "isLoading"], [245, 25, 245, 16], [245, 42, 246, 8], [245, 46, 246, 8, "_jsxDevRuntime"], [245, 60, 246, 8], [245, 61, 246, 8, "jsxDEV"], [245, 67, 246, 8], [246, 8, 246, 13, "style"], [246, 13, 246, 18], [246, 15, 246, 20], [247, 10, 247, 10, "position"], [247, 18, 247, 18], [247, 20, 247, 20], [247, 30, 247, 30], [248, 10, 248, 10, "top"], [248, 13, 248, 13], [248, 15, 248, 15], [248, 17, 248, 17], [249, 10, 249, 10, "left"], [249, 14, 249, 14], [249, 16, 249, 16], [249, 18, 249, 18], [250, 10, 250, 10, "background"], [250, 20, 250, 20], [250, 22, 250, 22], [250, 47, 250, 47], [251, 10, 251, 10, "color"], [251, 15, 251, 15], [251, 17, 251, 17], [251, 24, 251, 24], [252, 10, 252, 10, "padding"], [252, 17, 252, 17], [252, 19, 252, 19], [252, 29, 252, 29], [253, 10, 253, 10, "borderRadius"], [253, 22, 253, 22], [253, 24, 253, 24], [253, 29, 253, 29], [254, 10, 254, 10, "fontSize"], [254, 18, 254, 18], [254, 20, 254, 20], [254, 26, 254, 26], [255, 10, 255, 10, "fontWeight"], [255, 20, 255, 20], [255, 22, 255, 22], [255, 27, 255, 27], [256, 10, 256, 10, "zIndex"], [256, 16, 256, 16], [256, 18, 256, 18], [256, 20, 256, 20], [257, 10, 257, 10, "border"], [257, 16, 257, 16], [257, 18, 257, 18], [258, 8, 258, 8], [258, 9, 258, 10], [259, 8, 258, 10, "children"], [259, 16, 258, 10], [259, 18, 258, 11], [260, 6, 260, 8], [261, 8, 260, 8, "fileName"], [261, 16, 260, 8], [261, 18, 260, 8, "_jsxFileName"], [261, 30, 260, 8], [262, 8, 260, 8, "lineNumber"], [262, 18, 260, 8], [263, 8, 260, 8, "columnNumber"], [263, 20, 260, 8], [264, 6, 260, 8], [264, 13, 260, 13], [264, 14, 261, 7], [264, 16, 262, 7], [264, 17, 262, 8, "isLoading"], [264, 26, 262, 17], [264, 30, 262, 21, "faceCount"], [264, 39, 262, 30], [264, 42, 262, 33], [264, 43, 262, 34], [264, 60, 263, 8], [264, 64, 263, 8, "_jsxDevRuntime"], [264, 78, 263, 8], [264, 79, 263, 8, "jsxDEV"], [264, 85, 263, 8], [265, 8, 263, 13, "style"], [265, 13, 263, 18], [265, 15, 263, 20], [266, 10, 264, 10, "position"], [266, 18, 264, 18], [266, 20, 264, 20], [266, 30, 264, 30], [267, 10, 265, 10, "top"], [267, 13, 265, 13], [267, 15, 265, 15], [267, 17, 265, 17], [268, 10, 266, 10, "left"], [268, 14, 266, 14], [268, 16, 266, 16], [268, 18, 266, 18], [269, 10, 267, 10, "background"], [269, 20, 267, 20], [269, 22, 267, 22], [269, 47, 267, 47], [270, 10, 268, 10, "color"], [270, 15, 268, 15], [270, 17, 268, 17], [270, 24, 268, 24], [271, 10, 269, 10, "padding"], [271, 17, 269, 17], [271, 19, 269, 19], [271, 29, 269, 29], [272, 10, 270, 10, "borderRadius"], [272, 22, 270, 22], [272, 24, 270, 24], [272, 29, 270, 29], [273, 10, 271, 10, "fontSize"], [273, 18, 271, 18], [273, 20, 271, 20], [273, 26, 271, 26], [274, 10, 272, 10, "fontWeight"], [274, 20, 272, 20], [274, 22, 272, 22], [274, 27, 272, 27], [275, 10, 273, 10, "zIndex"], [275, 16, 273, 16], [275, 18, 273, 18], [275, 20, 273, 20], [276, 10, 274, 10, "border"], [276, 16, 274, 16], [276, 18, 274, 18], [277, 8, 275, 8], [277, 9, 275, 10], [278, 8, 275, 10, "children"], [278, 16, 275, 10], [278, 19, 275, 11], [278, 51, 276, 25], [278, 53, 276, 26, "faceCount"], [278, 62, 276, 35], [278, 64, 276, 36], [278, 71, 276, 41], [278, 73, 276, 42, "faceCount"], [278, 82, 276, 51], [278, 85, 276, 54], [278, 86, 276, 55], [278, 89, 276, 58], [278, 92, 276, 61], [278, 95, 276, 64], [278, 97, 276, 66], [279, 6, 276, 66], [280, 8, 276, 66, "fileName"], [280, 16, 276, 66], [280, 18, 276, 66, "_jsxFileName"], [280, 30, 276, 66], [281, 8, 276, 66, "lineNumber"], [281, 18, 276, 66], [282, 8, 276, 66, "columnNumber"], [282, 20, 276, 66], [283, 6, 276, 66], [283, 13, 277, 13], [283, 14, 278, 7], [283, 16, 279, 7], [283, 17, 279, 8, "isLoading"], [283, 26, 279, 17], [283, 30, 279, 21, "faceCount"], [283, 39, 279, 30], [283, 44, 279, 35], [283, 45, 279, 36], [283, 62, 280, 8], [283, 66, 280, 8, "_jsxDevRuntime"], [283, 80, 280, 8], [283, 81, 280, 8, "jsxDEV"], [283, 87, 280, 8], [284, 8, 280, 13, "style"], [284, 13, 280, 18], [284, 15, 280, 20], [285, 10, 281, 10, "position"], [285, 18, 281, 18], [285, 20, 281, 20], [285, 30, 281, 30], [286, 10, 282, 10, "top"], [286, 13, 282, 13], [286, 15, 282, 15], [286, 17, 282, 17], [287, 10, 283, 10, "left"], [287, 14, 283, 14], [287, 16, 283, 16], [287, 18, 283, 18], [288, 10, 284, 10, "background"], [288, 20, 284, 20], [288, 22, 284, 22], [288, 48, 284, 48], [289, 10, 285, 10, "color"], [289, 15, 285, 15], [289, 17, 285, 17], [289, 24, 285, 24], [290, 10, 286, 10, "padding"], [290, 17, 286, 17], [290, 19, 286, 19], [290, 29, 286, 29], [291, 10, 287, 10, "borderRadius"], [291, 22, 287, 22], [291, 24, 287, 24], [291, 29, 287, 29], [292, 10, 288, 10, "fontSize"], [292, 18, 288, 18], [292, 20, 288, 20], [292, 26, 288, 26], [293, 10, 289, 10, "fontWeight"], [293, 20, 289, 20], [293, 22, 289, 22], [293, 27, 289, 27], [294, 10, 290, 10, "zIndex"], [294, 16, 290, 16], [294, 18, 290, 18], [294, 20, 290, 20], [295, 10, 291, 10, "border"], [295, 16, 291, 16], [295, 18, 291, 18], [296, 8, 292, 8], [296, 9, 292, 10], [297, 8, 292, 10, "children"], [297, 16, 292, 10], [297, 18, 292, 11], [298, 6, 294, 8], [299, 8, 294, 8, "fileName"], [299, 16, 294, 8], [299, 18, 294, 8, "_jsxFileName"], [299, 30, 294, 8], [300, 8, 294, 8, "lineNumber"], [300, 18, 294, 8], [301, 8, 294, 8, "columnNumber"], [301, 20, 294, 8], [302, 6, 294, 8], [302, 13, 294, 13], [302, 14, 295, 7], [303, 4, 295, 7], [303, 19, 296, 6], [303, 20, 296, 7], [304, 2, 298, 0], [305, 2, 298, 1, "_s"], [305, 4, 298, 1], [305, 5, 17, 24, "BlazeFaceCanvas"], [305, 20, 17, 39], [306, 2, 17, 39, "_c"], [306, 4, 17, 39], [306, 7, 17, 24, "BlazeFaceCanvas"], [306, 22, 17, 39], [307, 2, 17, 39], [307, 6, 17, 39, "_c"], [307, 8, 17, 39], [308, 2, 17, 39, "$RefreshReg$"], [308, 14, 17, 39], [308, 15, 17, 39, "_c"], [308, 17, 17, 39], [309, 0, 17, 39], [309, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "updateCanvasSize", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "loadModel", "detectLoop", "predictions.forEach$argument_0", "waitForVideoAndStart", "<anonymous>"], "mappings": "AAA;eCgB;YCO;6BCsB;KDa;uBEW;yBCC;wBCG,eD;yBEC,gDF;ODE;KFC;sBMG;KNiC;uBOE;8BC0B;WDkD;KPa;iCSG;KTM;WUM;KVK;GDC;CDwE"}}, "type": "js/module"}]}