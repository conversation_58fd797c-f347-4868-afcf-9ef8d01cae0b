{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 912,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 965,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1080,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1076,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 886,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1686, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 72, 383, 70], [357, 73, 383, 71], [358, 8, 384, 6], [359, 6, 385, 4], [361, 6, 387, 4], [362, 6, 388, 4], [362, 12, 388, 10, "imageData"], [362, 21, 388, 19], [362, 24, 388, 22, "ctx"], [362, 27, 388, 25], [362, 28, 388, 26, "getImageData"], [362, 40, 388, 38], [362, 41, 388, 39, "clampedX"], [362, 49, 388, 47], [362, 51, 388, 49, "clampedY"], [362, 59, 388, 57], [362, 61, 388, 59, "<PERSON><PERSON><PERSON><PERSON>"], [362, 73, 388, 71], [362, 75, 388, 73, "clampedHeight"], [362, 88, 388, 86], [362, 89, 388, 87], [363, 6, 389, 4], [363, 12, 389, 10, "data"], [363, 16, 389, 14], [363, 19, 389, 17, "imageData"], [363, 28, 389, 26], [363, 29, 389, 27, "data"], [363, 33, 389, 31], [365, 6, 391, 4], [366, 6, 392, 4], [366, 12, 392, 10, "pixelSize"], [366, 21, 392, 19], [366, 24, 392, 22, "Math"], [366, 28, 392, 26], [366, 29, 392, 27, "max"], [366, 32, 392, 30], [366, 33, 392, 31], [366, 35, 392, 33], [366, 37, 392, 35, "Math"], [366, 41, 392, 39], [366, 42, 392, 40, "min"], [366, 45, 392, 43], [366, 46, 392, 44, "<PERSON><PERSON><PERSON><PERSON>"], [366, 58, 392, 56], [366, 60, 392, 58, "clampedHeight"], [366, 73, 392, 71], [366, 74, 392, 72], [366, 77, 392, 75], [366, 78, 392, 76], [366, 79, 392, 77], [367, 6, 394, 4], [367, 11, 394, 9], [367, 15, 394, 13, "py"], [367, 17, 394, 15], [367, 20, 394, 18], [367, 21, 394, 19], [367, 23, 394, 21, "py"], [367, 25, 394, 23], [367, 28, 394, 26, "clampedHeight"], [367, 41, 394, 39], [367, 43, 394, 41, "py"], [367, 45, 394, 43], [367, 49, 394, 47, "pixelSize"], [367, 58, 394, 56], [367, 60, 394, 58], [368, 8, 395, 6], [368, 13, 395, 11], [368, 17, 395, 15, "px"], [368, 19, 395, 17], [368, 22, 395, 20], [368, 23, 395, 21], [368, 25, 395, 23, "px"], [368, 27, 395, 25], [368, 30, 395, 28, "<PERSON><PERSON><PERSON><PERSON>"], [368, 42, 395, 40], [368, 44, 395, 42, "px"], [368, 46, 395, 44], [368, 50, 395, 48, "pixelSize"], [368, 59, 395, 57], [368, 61, 395, 59], [369, 10, 396, 8], [370, 10, 397, 8], [370, 14, 397, 12, "r"], [370, 15, 397, 13], [370, 18, 397, 16], [370, 19, 397, 17], [371, 12, 397, 19, "g"], [371, 13, 397, 20], [371, 16, 397, 23], [371, 17, 397, 24], [372, 12, 397, 26, "b"], [372, 13, 397, 27], [372, 16, 397, 30], [372, 17, 397, 31], [373, 12, 397, 33, "count"], [373, 17, 397, 38], [373, 20, 397, 41], [373, 21, 397, 42], [374, 10, 399, 8], [374, 15, 399, 13], [374, 19, 399, 17, "dy"], [374, 21, 399, 19], [374, 24, 399, 22], [374, 25, 399, 23], [374, 27, 399, 25, "dy"], [374, 29, 399, 27], [374, 32, 399, 30, "pixelSize"], [374, 41, 399, 39], [374, 45, 399, 43, "py"], [374, 47, 399, 45], [374, 50, 399, 48, "dy"], [374, 52, 399, 50], [374, 55, 399, 53, "clampedHeight"], [374, 68, 399, 66], [374, 70, 399, 68, "dy"], [374, 72, 399, 70], [374, 74, 399, 72], [374, 76, 399, 74], [375, 12, 400, 10], [375, 17, 400, 15], [375, 21, 400, 19, "dx"], [375, 23, 400, 21], [375, 26, 400, 24], [375, 27, 400, 25], [375, 29, 400, 27, "dx"], [375, 31, 400, 29], [375, 34, 400, 32, "pixelSize"], [375, 43, 400, 41], [375, 47, 400, 45, "px"], [375, 49, 400, 47], [375, 52, 400, 50, "dx"], [375, 54, 400, 52], [375, 57, 400, 55, "<PERSON><PERSON><PERSON><PERSON>"], [375, 69, 400, 67], [375, 71, 400, 69, "dx"], [375, 73, 400, 71], [375, 75, 400, 73], [375, 77, 400, 75], [376, 14, 401, 12], [376, 20, 401, 18, "index"], [376, 25, 401, 23], [376, 28, 401, 26], [376, 29, 401, 27], [376, 30, 401, 28, "py"], [376, 32, 401, 30], [376, 35, 401, 33, "dy"], [376, 37, 401, 35], [376, 41, 401, 39, "<PERSON><PERSON><PERSON><PERSON>"], [376, 53, 401, 51], [376, 57, 401, 55, "px"], [376, 59, 401, 57], [376, 62, 401, 60, "dx"], [376, 64, 401, 62], [376, 65, 401, 63], [376, 69, 401, 67], [376, 70, 401, 68], [377, 14, 402, 12, "r"], [377, 15, 402, 13], [377, 19, 402, 17, "data"], [377, 23, 402, 21], [377, 24, 402, 22, "index"], [377, 29, 402, 27], [377, 30, 402, 28], [378, 14, 403, 12, "g"], [378, 15, 403, 13], [378, 19, 403, 17, "data"], [378, 23, 403, 21], [378, 24, 403, 22, "index"], [378, 29, 403, 27], [378, 32, 403, 30], [378, 33, 403, 31], [378, 34, 403, 32], [379, 14, 404, 12, "b"], [379, 15, 404, 13], [379, 19, 404, 17, "data"], [379, 23, 404, 21], [379, 24, 404, 22, "index"], [379, 29, 404, 27], [379, 32, 404, 30], [379, 33, 404, 31], [379, 34, 404, 32], [380, 14, 405, 12, "count"], [380, 19, 405, 17], [380, 21, 405, 19], [381, 12, 406, 10], [382, 10, 407, 8], [383, 10, 409, 8], [383, 14, 409, 12, "count"], [383, 19, 409, 17], [383, 22, 409, 20], [383, 23, 409, 21], [383, 25, 409, 23], [384, 12, 410, 10, "r"], [384, 13, 410, 11], [384, 16, 410, 14, "Math"], [384, 20, 410, 18], [384, 21, 410, 19, "floor"], [384, 26, 410, 24], [384, 27, 410, 25, "r"], [384, 28, 410, 26], [384, 31, 410, 29, "count"], [384, 36, 410, 34], [384, 37, 410, 35], [385, 12, 411, 10, "g"], [385, 13, 411, 11], [385, 16, 411, 14, "Math"], [385, 20, 411, 18], [385, 21, 411, 19, "floor"], [385, 26, 411, 24], [385, 27, 411, 25, "g"], [385, 28, 411, 26], [385, 31, 411, 29, "count"], [385, 36, 411, 34], [385, 37, 411, 35], [386, 12, 412, 10, "b"], [386, 13, 412, 11], [386, 16, 412, 14, "Math"], [386, 20, 412, 18], [386, 21, 412, 19, "floor"], [386, 26, 412, 24], [386, 27, 412, 25, "b"], [386, 28, 412, 26], [386, 31, 412, 29, "count"], [386, 36, 412, 34], [386, 37, 412, 35], [388, 12, 414, 10], [389, 12, 415, 10], [389, 17, 415, 15], [389, 21, 415, 19, "dy"], [389, 23, 415, 21], [389, 26, 415, 24], [389, 27, 415, 25], [389, 29, 415, 27, "dy"], [389, 31, 415, 29], [389, 34, 415, 32, "pixelSize"], [389, 43, 415, 41], [389, 47, 415, 45, "py"], [389, 49, 415, 47], [389, 52, 415, 50, "dy"], [389, 54, 415, 52], [389, 57, 415, 55, "clampedHeight"], [389, 70, 415, 68], [389, 72, 415, 70, "dy"], [389, 74, 415, 72], [389, 76, 415, 74], [389, 78, 415, 76], [390, 14, 416, 12], [390, 19, 416, 17], [390, 23, 416, 21, "dx"], [390, 25, 416, 23], [390, 28, 416, 26], [390, 29, 416, 27], [390, 31, 416, 29, "dx"], [390, 33, 416, 31], [390, 36, 416, 34, "pixelSize"], [390, 45, 416, 43], [390, 49, 416, 47, "px"], [390, 51, 416, 49], [390, 54, 416, 52, "dx"], [390, 56, 416, 54], [390, 59, 416, 57, "<PERSON><PERSON><PERSON><PERSON>"], [390, 71, 416, 69], [390, 73, 416, 71, "dx"], [390, 75, 416, 73], [390, 77, 416, 75], [390, 79, 416, 77], [391, 16, 417, 14], [391, 22, 417, 20, "index"], [391, 27, 417, 25], [391, 30, 417, 28], [391, 31, 417, 29], [391, 32, 417, 30, "py"], [391, 34, 417, 32], [391, 37, 417, 35, "dy"], [391, 39, 417, 37], [391, 43, 417, 41, "<PERSON><PERSON><PERSON><PERSON>"], [391, 55, 417, 53], [391, 59, 417, 57, "px"], [391, 61, 417, 59], [391, 64, 417, 62, "dx"], [391, 66, 417, 64], [391, 67, 417, 65], [391, 71, 417, 69], [391, 72, 417, 70], [392, 16, 418, 14, "data"], [392, 20, 418, 18], [392, 21, 418, 19, "index"], [392, 26, 418, 24], [392, 27, 418, 25], [392, 30, 418, 28, "r"], [392, 31, 418, 29], [393, 16, 419, 14, "data"], [393, 20, 419, 18], [393, 21, 419, 19, "index"], [393, 26, 419, 24], [393, 29, 419, 27], [393, 30, 419, 28], [393, 31, 419, 29], [393, 34, 419, 32, "g"], [393, 35, 419, 33], [394, 16, 420, 14, "data"], [394, 20, 420, 18], [394, 21, 420, 19, "index"], [394, 26, 420, 24], [394, 29, 420, 27], [394, 30, 420, 28], [394, 31, 420, 29], [394, 34, 420, 32, "b"], [394, 35, 420, 33], [395, 16, 421, 14], [396, 14, 422, 12], [397, 12, 423, 10], [398, 10, 424, 8], [399, 8, 425, 6], [400, 6, 426, 4], [402, 6, 428, 4], [403, 6, 429, 4], [403, 11, 429, 9], [403, 15, 429, 13, "i"], [403, 16, 429, 14], [403, 19, 429, 17], [403, 20, 429, 18], [403, 22, 429, 20, "i"], [403, 23, 429, 21], [403, 26, 429, 24], [403, 27, 429, 25], [403, 29, 429, 27, "i"], [403, 30, 429, 28], [403, 32, 429, 30], [403, 34, 429, 32], [404, 8, 430, 6, "applySimpleBlur"], [404, 23, 430, 21], [404, 24, 430, 22, "data"], [404, 28, 430, 26], [404, 30, 430, 28, "<PERSON><PERSON><PERSON><PERSON>"], [404, 42, 430, 40], [404, 44, 430, 42, "clampedHeight"], [404, 57, 430, 55], [404, 58, 430, 56], [405, 6, 431, 4], [407, 6, 433, 4], [408, 6, 434, 4, "ctx"], [408, 9, 434, 7], [408, 10, 434, 8, "putImageData"], [408, 22, 434, 20], [408, 23, 434, 21, "imageData"], [408, 32, 434, 30], [408, 34, 434, 32, "clampedX"], [408, 42, 434, 40], [408, 44, 434, 42, "clampedY"], [408, 52, 434, 50], [408, 53, 434, 51], [410, 6, 436, 4], [411, 6, 437, 4, "ctx"], [411, 9, 437, 7], [411, 10, 437, 8, "fillStyle"], [411, 19, 437, 17], [411, 22, 437, 20], [411, 48, 437, 46], [412, 6, 438, 4, "ctx"], [412, 9, 438, 7], [412, 10, 438, 8, "fillRect"], [412, 18, 438, 16], [412, 19, 438, 17, "clampedX"], [412, 27, 438, 25], [412, 29, 438, 27, "clampedY"], [412, 37, 438, 35], [412, 39, 438, 37, "<PERSON><PERSON><PERSON><PERSON>"], [412, 51, 438, 49], [412, 53, 438, 51, "clampedHeight"], [412, 66, 438, 64], [412, 67, 438, 65], [413, 4, 439, 2], [413, 5, 439, 3], [414, 4, 441, 2], [414, 10, 441, 8, "applySimpleBlur"], [414, 25, 441, 23], [414, 28, 441, 26, "applySimpleBlur"], [414, 29, 441, 27, "data"], [414, 33, 441, 50], [414, 35, 441, 52, "width"], [414, 40, 441, 65], [414, 42, 441, 67, "height"], [414, 48, 441, 81], [414, 53, 441, 86], [415, 6, 442, 4], [415, 12, 442, 10, "original"], [415, 20, 442, 18], [415, 23, 442, 21], [415, 27, 442, 25, "Uint8ClampedArray"], [415, 44, 442, 42], [415, 45, 442, 43, "data"], [415, 49, 442, 47], [415, 50, 442, 48], [416, 6, 444, 4], [416, 11, 444, 9], [416, 15, 444, 13, "y"], [416, 16, 444, 14], [416, 19, 444, 17], [416, 20, 444, 18], [416, 22, 444, 20, "y"], [416, 23, 444, 21], [416, 26, 444, 24, "height"], [416, 32, 444, 30], [416, 35, 444, 33], [416, 36, 444, 34], [416, 38, 444, 36, "y"], [416, 39, 444, 37], [416, 41, 444, 39], [416, 43, 444, 41], [417, 8, 445, 6], [417, 13, 445, 11], [417, 17, 445, 15, "x"], [417, 18, 445, 16], [417, 21, 445, 19], [417, 22, 445, 20], [417, 24, 445, 22, "x"], [417, 25, 445, 23], [417, 28, 445, 26, "width"], [417, 33, 445, 31], [417, 36, 445, 34], [417, 37, 445, 35], [417, 39, 445, 37, "x"], [417, 40, 445, 38], [417, 42, 445, 40], [417, 44, 445, 42], [418, 10, 446, 8], [418, 16, 446, 14, "index"], [418, 21, 446, 19], [418, 24, 446, 22], [418, 25, 446, 23, "y"], [418, 26, 446, 24], [418, 29, 446, 27, "width"], [418, 34, 446, 32], [418, 37, 446, 35, "x"], [418, 38, 446, 36], [418, 42, 446, 40], [418, 43, 446, 41], [420, 10, 448, 8], [421, 10, 449, 8], [421, 14, 449, 12, "r"], [421, 15, 449, 13], [421, 18, 449, 16], [421, 19, 449, 17], [422, 12, 449, 19, "g"], [422, 13, 449, 20], [422, 16, 449, 23], [422, 17, 449, 24], [423, 12, 449, 26, "b"], [423, 13, 449, 27], [423, 16, 449, 30], [423, 17, 449, 31], [424, 10, 450, 8], [424, 15, 450, 13], [424, 19, 450, 17, "dy"], [424, 21, 450, 19], [424, 24, 450, 22], [424, 25, 450, 23], [424, 26, 450, 24], [424, 28, 450, 26, "dy"], [424, 30, 450, 28], [424, 34, 450, 32], [424, 35, 450, 33], [424, 37, 450, 35, "dy"], [424, 39, 450, 37], [424, 41, 450, 39], [424, 43, 450, 41], [425, 12, 451, 10], [425, 17, 451, 15], [425, 21, 451, 19, "dx"], [425, 23, 451, 21], [425, 26, 451, 24], [425, 27, 451, 25], [425, 28, 451, 26], [425, 30, 451, 28, "dx"], [425, 32, 451, 30], [425, 36, 451, 34], [425, 37, 451, 35], [425, 39, 451, 37, "dx"], [425, 41, 451, 39], [425, 43, 451, 41], [425, 45, 451, 43], [426, 14, 452, 12], [426, 20, 452, 18, "neighborIndex"], [426, 33, 452, 31], [426, 36, 452, 34], [426, 37, 452, 35], [426, 38, 452, 36, "y"], [426, 39, 452, 37], [426, 42, 452, 40, "dy"], [426, 44, 452, 42], [426, 48, 452, 46, "width"], [426, 53, 452, 51], [426, 57, 452, 55, "x"], [426, 58, 452, 56], [426, 61, 452, 59, "dx"], [426, 63, 452, 61], [426, 64, 452, 62], [426, 68, 452, 66], [426, 69, 452, 67], [427, 14, 453, 12, "r"], [427, 15, 453, 13], [427, 19, 453, 17, "original"], [427, 27, 453, 25], [427, 28, 453, 26, "neighborIndex"], [427, 41, 453, 39], [427, 42, 453, 40], [428, 14, 454, 12, "g"], [428, 15, 454, 13], [428, 19, 454, 17, "original"], [428, 27, 454, 25], [428, 28, 454, 26, "neighborIndex"], [428, 41, 454, 39], [428, 44, 454, 42], [428, 45, 454, 43], [428, 46, 454, 44], [429, 14, 455, 12, "b"], [429, 15, 455, 13], [429, 19, 455, 17, "original"], [429, 27, 455, 25], [429, 28, 455, 26, "neighborIndex"], [429, 41, 455, 39], [429, 44, 455, 42], [429, 45, 455, 43], [429, 46, 455, 44], [430, 12, 456, 10], [431, 10, 457, 8], [432, 10, 459, 8, "data"], [432, 14, 459, 12], [432, 15, 459, 13, "index"], [432, 20, 459, 18], [432, 21, 459, 19], [432, 24, 459, 22, "r"], [432, 25, 459, 23], [432, 28, 459, 26], [432, 29, 459, 27], [433, 10, 460, 8, "data"], [433, 14, 460, 12], [433, 15, 460, 13, "index"], [433, 20, 460, 18], [433, 23, 460, 21], [433, 24, 460, 22], [433, 25, 460, 23], [433, 28, 460, 26, "g"], [433, 29, 460, 27], [433, 32, 460, 30], [433, 33, 460, 31], [434, 10, 461, 8, "data"], [434, 14, 461, 12], [434, 15, 461, 13, "index"], [434, 20, 461, 18], [434, 23, 461, 21], [434, 24, 461, 22], [434, 25, 461, 23], [434, 28, 461, 26, "b"], [434, 29, 461, 27], [434, 32, 461, 30], [434, 33, 461, 31], [435, 8, 462, 6], [436, 6, 463, 4], [437, 4, 464, 2], [437, 5, 464, 3], [438, 4, 466, 2], [438, 10, 466, 8, "applyFallbackFaceBlur"], [438, 31, 466, 29], [438, 34, 466, 32, "applyFallbackFaceBlur"], [438, 35, 466, 33, "ctx"], [438, 38, 466, 62], [438, 40, 466, 64, "imgWidth"], [438, 48, 466, 80], [438, 50, 466, 82, "imgHeight"], [438, 59, 466, 99], [438, 64, 466, 104], [439, 6, 467, 4, "console"], [439, 13, 467, 11], [439, 14, 467, 12, "log"], [439, 17, 467, 15], [439, 18, 467, 16], [439, 90, 467, 88], [439, 91, 467, 89], [441, 6, 469, 4], [442, 6, 470, 4], [442, 12, 470, 10, "areas"], [442, 17, 470, 15], [442, 20, 470, 18], [443, 6, 471, 6], [444, 6, 472, 6], [445, 8, 472, 8, "x"], [445, 9, 472, 9], [445, 11, 472, 11, "imgWidth"], [445, 19, 472, 19], [445, 22, 472, 22], [445, 26, 472, 26], [446, 8, 472, 28, "y"], [446, 9, 472, 29], [446, 11, 472, 31, "imgHeight"], [446, 20, 472, 40], [446, 23, 472, 43], [446, 27, 472, 47], [447, 8, 472, 49, "w"], [447, 9, 472, 50], [447, 11, 472, 52, "imgWidth"], [447, 19, 472, 60], [447, 22, 472, 63], [447, 25, 472, 66], [448, 8, 472, 68, "h"], [448, 9, 472, 69], [448, 11, 472, 71, "imgHeight"], [448, 20, 472, 80], [448, 23, 472, 83], [449, 6, 472, 87], [449, 7, 472, 88], [450, 6, 473, 6], [451, 6, 474, 6], [452, 8, 474, 8, "x"], [452, 9, 474, 9], [452, 11, 474, 11, "imgWidth"], [452, 19, 474, 19], [452, 22, 474, 22], [452, 25, 474, 25], [453, 8, 474, 27, "y"], [453, 9, 474, 28], [453, 11, 474, 30, "imgHeight"], [453, 20, 474, 39], [453, 23, 474, 42], [453, 26, 474, 45], [454, 8, 474, 47, "w"], [454, 9, 474, 48], [454, 11, 474, 50, "imgWidth"], [454, 19, 474, 58], [454, 22, 474, 61], [454, 26, 474, 65], [455, 8, 474, 67, "h"], [455, 9, 474, 68], [455, 11, 474, 70, "imgHeight"], [455, 20, 474, 79], [455, 23, 474, 82], [456, 6, 474, 86], [456, 7, 474, 87], [457, 6, 475, 6], [458, 6, 476, 6], [459, 8, 476, 8, "x"], [459, 9, 476, 9], [459, 11, 476, 11, "imgWidth"], [459, 19, 476, 19], [459, 22, 476, 22], [459, 26, 476, 26], [460, 8, 476, 28, "y"], [460, 9, 476, 29], [460, 11, 476, 31, "imgHeight"], [460, 20, 476, 40], [460, 23, 476, 43], [460, 26, 476, 46], [461, 8, 476, 48, "w"], [461, 9, 476, 49], [461, 11, 476, 51, "imgWidth"], [461, 19, 476, 59], [461, 22, 476, 62], [461, 26, 476, 66], [462, 8, 476, 68, "h"], [462, 9, 476, 69], [462, 11, 476, 71, "imgHeight"], [462, 20, 476, 80], [462, 23, 476, 83], [463, 6, 476, 87], [463, 7, 476, 88], [463, 8, 477, 5], [464, 6, 479, 4, "areas"], [464, 11, 479, 9], [464, 12, 479, 10, "for<PERSON>ach"], [464, 19, 479, 17], [464, 20, 479, 18], [464, 21, 479, 19, "area"], [464, 25, 479, 23], [464, 27, 479, 25, "index"], [464, 32, 479, 30], [464, 37, 479, 35], [465, 8, 480, 6, "console"], [465, 15, 480, 13], [465, 16, 480, 14, "log"], [465, 19, 480, 17], [465, 20, 480, 18], [465, 65, 480, 63, "index"], [465, 70, 480, 68], [465, 73, 480, 71], [465, 74, 480, 72], [465, 77, 480, 75], [465, 79, 480, 77, "area"], [465, 83, 480, 81], [465, 84, 480, 82], [466, 8, 481, 6, "applyStrongBlur"], [466, 23, 481, 21], [466, 24, 481, 22, "ctx"], [466, 27, 481, 25], [466, 29, 481, 27, "area"], [466, 33, 481, 31], [466, 34, 481, 32, "x"], [466, 35, 481, 33], [466, 37, 481, 35, "area"], [466, 41, 481, 39], [466, 42, 481, 40, "y"], [466, 43, 481, 41], [466, 45, 481, 43, "area"], [466, 49, 481, 47], [466, 50, 481, 48, "w"], [466, 51, 481, 49], [466, 53, 481, 51, "area"], [466, 57, 481, 55], [466, 58, 481, 56, "h"], [466, 59, 481, 57], [466, 60, 481, 58], [467, 6, 482, 4], [467, 7, 482, 5], [467, 8, 482, 6], [468, 4, 483, 2], [468, 5, 483, 3], [470, 4, 485, 2], [471, 4, 486, 2], [471, 10, 486, 8, "capturePhoto"], [471, 22, 486, 20], [471, 25, 486, 23], [471, 29, 486, 23, "useCallback"], [471, 47, 486, 34], [471, 49, 486, 35], [471, 61, 486, 47], [472, 6, 487, 4], [473, 6, 488, 4], [473, 12, 488, 10, "isDev"], [473, 17, 488, 15], [473, 20, 488, 18, "process"], [473, 27, 488, 25], [473, 28, 488, 26, "env"], [473, 31, 488, 29], [473, 32, 488, 30, "NODE_ENV"], [473, 40, 488, 38], [473, 45, 488, 43], [473, 58, 488, 56], [473, 62, 488, 60, "__DEV__"], [473, 69, 488, 67], [474, 6, 490, 4], [474, 10, 490, 8], [474, 11, 490, 9, "cameraRef"], [474, 20, 490, 18], [474, 21, 490, 19, "current"], [474, 28, 490, 26], [474, 32, 490, 30], [474, 33, 490, 31, "isDev"], [474, 38, 490, 36], [474, 40, 490, 38], [475, 8, 491, 6, "<PERSON><PERSON>"], [475, 22, 491, 11], [475, 23, 491, 12, "alert"], [475, 28, 491, 17], [475, 29, 491, 18], [475, 36, 491, 25], [475, 38, 491, 27], [475, 56, 491, 45], [475, 57, 491, 46], [476, 8, 492, 6], [477, 6, 493, 4], [478, 6, 494, 4], [478, 10, 494, 8], [479, 8, 495, 6, "setProcessingState"], [479, 26, 495, 24], [479, 27, 495, 25], [479, 38, 495, 36], [479, 39, 495, 37], [480, 8, 496, 6, "setProcessingProgress"], [480, 29, 496, 27], [480, 30, 496, 28], [480, 32, 496, 30], [480, 33, 496, 31], [481, 8, 497, 6], [482, 8, 498, 6], [483, 8, 499, 6], [484, 8, 500, 6], [484, 14, 500, 12], [484, 18, 500, 16, "Promise"], [484, 25, 500, 23], [484, 26, 500, 24, "resolve"], [484, 33, 500, 31], [484, 37, 500, 35, "setTimeout"], [484, 47, 500, 45], [484, 48, 500, 46, "resolve"], [484, 55, 500, 53], [484, 57, 500, 55], [484, 59, 500, 57], [484, 60, 500, 58], [484, 61, 500, 59], [485, 8, 501, 6], [486, 8, 502, 6], [486, 12, 502, 10, "photo"], [486, 17, 502, 15], [487, 8, 504, 6], [487, 12, 504, 10], [488, 10, 505, 8, "photo"], [488, 15, 505, 13], [488, 18, 505, 16], [488, 24, 505, 22, "cameraRef"], [488, 33, 505, 31], [488, 34, 505, 32, "current"], [488, 41, 505, 39], [488, 42, 505, 40, "takePictureAsync"], [488, 58, 505, 56], [488, 59, 505, 57], [489, 12, 506, 10, "quality"], [489, 19, 506, 17], [489, 21, 506, 19], [489, 24, 506, 22], [490, 12, 507, 10, "base64"], [490, 18, 507, 16], [490, 20, 507, 18], [490, 25, 507, 23], [491, 12, 508, 10, "skipProcessing"], [491, 26, 508, 24], [491, 28, 508, 26], [491, 32, 508, 30], [491, 33, 508, 32], [492, 10, 509, 8], [492, 11, 509, 9], [492, 12, 509, 10], [493, 8, 510, 6], [493, 9, 510, 7], [493, 10, 510, 8], [493, 17, 510, 15, "cameraError"], [493, 28, 510, 26], [493, 30, 510, 28], [494, 10, 511, 8, "console"], [494, 17, 511, 15], [494, 18, 511, 16, "log"], [494, 21, 511, 19], [494, 22, 511, 20], [494, 82, 511, 80], [494, 84, 511, 82, "cameraError"], [494, 95, 511, 93], [494, 96, 511, 94], [495, 10, 512, 8], [496, 10, 513, 8], [496, 14, 513, 12, "isDev"], [496, 19, 513, 17], [496, 21, 513, 19], [497, 12, 514, 10, "photo"], [497, 17, 514, 15], [497, 20, 514, 18], [498, 14, 515, 12, "uri"], [498, 17, 515, 15], [498, 19, 515, 17], [499, 12, 516, 10], [499, 13, 516, 11], [500, 10, 517, 8], [500, 11, 517, 9], [500, 17, 517, 15], [501, 12, 518, 10], [501, 18, 518, 16, "cameraError"], [501, 29, 518, 27], [502, 10, 519, 8], [503, 8, 520, 6], [504, 8, 521, 6], [504, 12, 521, 10], [504, 13, 521, 11, "photo"], [504, 18, 521, 16], [504, 20, 521, 18], [505, 10, 522, 8], [505, 16, 522, 14], [505, 20, 522, 18, "Error"], [505, 25, 522, 23], [505, 26, 522, 24], [505, 51, 522, 49], [505, 52, 522, 50], [506, 8, 523, 6], [507, 8, 524, 6, "console"], [507, 15, 524, 13], [507, 16, 524, 14, "log"], [507, 19, 524, 17], [507, 20, 524, 18], [507, 56, 524, 54], [507, 58, 524, 56, "photo"], [507, 63, 524, 61], [507, 64, 524, 62, "uri"], [507, 67, 524, 65], [507, 68, 524, 66], [508, 8, 525, 6, "setCapturedPhoto"], [508, 24, 525, 22], [508, 25, 525, 23, "photo"], [508, 30, 525, 28], [508, 31, 525, 29, "uri"], [508, 34, 525, 32], [508, 35, 525, 33], [509, 8, 526, 6, "setProcessingProgress"], [509, 29, 526, 27], [509, 30, 526, 28], [509, 32, 526, 30], [509, 33, 526, 31], [510, 8, 527, 6], [511, 8, 528, 6, "console"], [511, 15, 528, 13], [511, 16, 528, 14, "log"], [511, 19, 528, 17], [511, 20, 528, 18], [511, 73, 528, 71], [511, 74, 528, 72], [512, 8, 529, 6], [512, 14, 529, 12, "processImageWithFaceBlur"], [512, 38, 529, 36], [512, 39, 529, 37, "photo"], [512, 44, 529, 42], [512, 45, 529, 43, "uri"], [512, 48, 529, 46], [512, 49, 529, 47], [513, 8, 530, 6, "console"], [513, 15, 530, 13], [513, 16, 530, 14, "log"], [513, 19, 530, 17], [513, 20, 530, 18], [513, 71, 530, 69], [513, 72, 530, 70], [514, 6, 531, 4], [514, 7, 531, 5], [514, 8, 531, 6], [514, 15, 531, 13, "error"], [514, 20, 531, 18], [514, 22, 531, 20], [515, 8, 532, 6, "console"], [515, 15, 532, 13], [515, 16, 532, 14, "error"], [515, 21, 532, 19], [515, 22, 532, 20], [515, 54, 532, 52], [515, 56, 532, 54, "error"], [515, 61, 532, 59], [515, 62, 532, 60], [516, 8, 533, 6, "setErrorMessage"], [516, 23, 533, 21], [516, 24, 533, 22], [516, 68, 533, 66], [516, 69, 533, 67], [517, 8, 534, 6, "setProcessingState"], [517, 26, 534, 24], [517, 27, 534, 25], [517, 34, 534, 32], [517, 35, 534, 33], [518, 6, 535, 4], [519, 4, 536, 2], [519, 5, 536, 3], [519, 7, 536, 5], [519, 9, 536, 7], [519, 10, 536, 8], [520, 4, 537, 2], [521, 4, 538, 2], [521, 10, 538, 8, "processImageWithFaceBlur"], [521, 34, 538, 32], [521, 37, 538, 35], [521, 43, 538, 42, "photoUri"], [521, 51, 538, 58], [521, 55, 538, 63], [522, 6, 539, 4], [522, 10, 539, 8], [523, 8, 540, 6, "console"], [523, 15, 540, 13], [523, 16, 540, 14, "log"], [523, 19, 540, 17], [523, 20, 540, 18], [523, 84, 540, 82], [523, 85, 540, 83], [524, 8, 541, 6, "setProcessingState"], [524, 26, 541, 24], [524, 27, 541, 25], [524, 39, 541, 37], [524, 40, 541, 38], [525, 8, 542, 6, "setProcessingProgress"], [525, 29, 542, 27], [525, 30, 542, 28], [525, 32, 542, 30], [525, 33, 542, 31], [527, 8, 544, 6], [528, 8, 545, 6], [528, 14, 545, 12, "canvas"], [528, 20, 545, 18], [528, 23, 545, 21, "document"], [528, 31, 545, 29], [528, 32, 545, 30, "createElement"], [528, 45, 545, 43], [528, 46, 545, 44], [528, 54, 545, 52], [528, 55, 545, 53], [529, 8, 546, 6], [529, 14, 546, 12, "ctx"], [529, 17, 546, 15], [529, 20, 546, 18, "canvas"], [529, 26, 546, 24], [529, 27, 546, 25, "getContext"], [529, 37, 546, 35], [529, 38, 546, 36], [529, 42, 546, 40], [529, 43, 546, 41], [530, 8, 547, 6], [530, 12, 547, 10], [530, 13, 547, 11, "ctx"], [530, 16, 547, 14], [530, 18, 547, 16], [530, 24, 547, 22], [530, 28, 547, 26, "Error"], [530, 33, 547, 31], [530, 34, 547, 32], [530, 64, 547, 62], [530, 65, 547, 63], [532, 8, 549, 6], [533, 8, 550, 6], [533, 14, 550, 12, "img"], [533, 17, 550, 15], [533, 20, 550, 18], [533, 24, 550, 22, "Image"], [533, 29, 550, 27], [533, 30, 550, 28], [533, 31, 550, 29], [534, 8, 551, 6], [534, 14, 551, 12], [534, 18, 551, 16, "Promise"], [534, 25, 551, 23], [534, 26, 551, 24], [534, 27, 551, 25, "resolve"], [534, 34, 551, 32], [534, 36, 551, 34, "reject"], [534, 42, 551, 40], [534, 47, 551, 45], [535, 10, 552, 8, "img"], [535, 13, 552, 11], [535, 14, 552, 12, "onload"], [535, 20, 552, 18], [535, 23, 552, 21, "resolve"], [535, 30, 552, 28], [536, 10, 553, 8, "img"], [536, 13, 553, 11], [536, 14, 553, 12, "onerror"], [536, 21, 553, 19], [536, 24, 553, 22, "reject"], [536, 30, 553, 28], [537, 10, 554, 8, "img"], [537, 13, 554, 11], [537, 14, 554, 12, "src"], [537, 17, 554, 15], [537, 20, 554, 18, "photoUri"], [537, 28, 554, 26], [538, 8, 555, 6], [538, 9, 555, 7], [538, 10, 555, 8], [540, 8, 557, 6], [541, 8, 558, 6, "canvas"], [541, 14, 558, 12], [541, 15, 558, 13, "width"], [541, 20, 558, 18], [541, 23, 558, 21, "img"], [541, 26, 558, 24], [541, 27, 558, 25, "width"], [541, 32, 558, 30], [542, 8, 559, 6, "canvas"], [542, 14, 559, 12], [542, 15, 559, 13, "height"], [542, 21, 559, 19], [542, 24, 559, 22, "img"], [542, 27, 559, 25], [542, 28, 559, 26, "height"], [542, 34, 559, 32], [543, 8, 560, 6, "console"], [543, 15, 560, 13], [543, 16, 560, 14, "log"], [543, 19, 560, 17], [543, 20, 560, 18], [543, 54, 560, 52], [543, 56, 560, 54], [544, 10, 560, 56, "width"], [544, 15, 560, 61], [544, 17, 560, 63, "img"], [544, 20, 560, 66], [544, 21, 560, 67, "width"], [544, 26, 560, 72], [545, 10, 560, 74, "height"], [545, 16, 560, 80], [545, 18, 560, 82, "img"], [545, 21, 560, 85], [545, 22, 560, 86, "height"], [546, 8, 560, 93], [546, 9, 560, 94], [546, 10, 560, 95], [548, 8, 562, 6], [549, 8, 563, 6, "ctx"], [549, 11, 563, 9], [549, 12, 563, 10, "drawImage"], [549, 21, 563, 19], [549, 22, 563, 20, "img"], [549, 25, 563, 23], [549, 27, 563, 25], [549, 28, 563, 26], [549, 30, 563, 28], [549, 31, 563, 29], [549, 32, 563, 30], [550, 8, 564, 6, "console"], [550, 15, 564, 13], [550, 16, 564, 14, "log"], [550, 19, 564, 17], [550, 20, 564, 18], [550, 72, 564, 70], [550, 73, 564, 71], [551, 8, 566, 6, "setProcessingProgress"], [551, 29, 566, 27], [551, 30, 566, 28], [551, 32, 566, 30], [551, 33, 566, 31], [553, 8, 568, 6], [554, 8, 569, 6], [554, 12, 569, 10, "detectedFaces"], [554, 25, 569, 23], [554, 28, 569, 26], [554, 30, 569, 28], [555, 8, 571, 6, "console"], [555, 15, 571, 13], [555, 16, 571, 14, "log"], [555, 19, 571, 17], [555, 20, 571, 18], [555, 81, 571, 79], [555, 82, 571, 80], [557, 8, 573, 6], [558, 8, 574, 6], [558, 12, 574, 10], [559, 10, 575, 8], [559, 16, 575, 14, "loadTensorFlowFaceDetection"], [559, 43, 575, 41], [559, 44, 575, 42], [559, 45, 575, 43], [560, 10, 576, 8, "detectedFaces"], [560, 23, 576, 21], [560, 26, 576, 24], [560, 32, 576, 30, "detectFacesWithTensorFlow"], [560, 57, 576, 55], [560, 58, 576, 56, "img"], [560, 61, 576, 59], [560, 62, 576, 60], [561, 10, 577, 8, "console"], [561, 17, 577, 15], [561, 18, 577, 16, "log"], [561, 21, 577, 19], [561, 22, 577, 20], [561, 70, 577, 68, "detectedFaces"], [561, 83, 577, 81], [561, 84, 577, 82, "length"], [561, 90, 577, 88], [561, 98, 577, 96], [561, 99, 577, 97], [562, 8, 578, 6], [562, 9, 578, 7], [562, 10, 578, 8], [562, 17, 578, 15, "tensorFlowError"], [562, 32, 578, 30], [562, 34, 578, 32], [563, 10, 579, 8, "console"], [563, 17, 579, 15], [563, 18, 579, 16, "warn"], [563, 22, 579, 20], [563, 23, 579, 21], [563, 61, 579, 59], [563, 63, 579, 61, "tensorFlowError"], [563, 78, 579, 76], [563, 79, 579, 77], [565, 10, 581, 8], [566, 10, 582, 8, "console"], [566, 17, 582, 15], [566, 18, 582, 16, "log"], [566, 21, 582, 19], [566, 22, 582, 20], [566, 86, 582, 84], [566, 87, 582, 85], [567, 10, 583, 8, "detectedFaces"], [567, 23, 583, 21], [567, 26, 583, 24, "detectFacesHeuristic"], [567, 46, 583, 44], [567, 47, 583, 45, "img"], [567, 50, 583, 48], [567, 52, 583, 50, "ctx"], [567, 55, 583, 53], [567, 56, 583, 54], [568, 10, 584, 8, "console"], [568, 17, 584, 15], [568, 18, 584, 16, "log"], [568, 21, 584, 19], [568, 22, 584, 20], [568, 70, 584, 68, "detectedFaces"], [568, 83, 584, 81], [568, 84, 584, 82, "length"], [568, 90, 584, 88], [568, 98, 584, 96], [568, 99, 584, 97], [569, 8, 585, 6], [571, 8, 587, 6], [572, 8, 588, 6], [572, 12, 588, 10, "detectedFaces"], [572, 25, 588, 23], [572, 26, 588, 24, "length"], [572, 32, 588, 30], [572, 37, 588, 35], [572, 38, 588, 36], [572, 40, 588, 38], [573, 10, 589, 8, "console"], [573, 17, 589, 15], [573, 18, 589, 16, "log"], [573, 21, 589, 19], [573, 22, 589, 20], [573, 89, 589, 87], [573, 90, 589, 88], [574, 10, 590, 8, "detectedFaces"], [574, 23, 590, 21], [574, 26, 590, 24, "detectFacesAggressive"], [574, 47, 590, 45], [574, 48, 590, 46, "img"], [574, 51, 590, 49], [574, 53, 590, 51, "ctx"], [574, 56, 590, 54], [574, 57, 590, 55], [575, 10, 591, 8, "console"], [575, 17, 591, 15], [575, 18, 591, 16, "log"], [575, 21, 591, 19], [575, 22, 591, 20], [575, 71, 591, 69, "detectedFaces"], [575, 84, 591, 82], [575, 85, 591, 83, "length"], [575, 91, 591, 89], [575, 99, 591, 97], [575, 100, 591, 98], [576, 8, 592, 6], [577, 8, 594, 6, "console"], [577, 15, 594, 13], [577, 16, 594, 14, "log"], [577, 19, 594, 17], [577, 20, 594, 18], [577, 72, 594, 70, "detectedFaces"], [577, 85, 594, 83], [577, 86, 594, 84, "length"], [577, 92, 594, 90], [577, 100, 594, 98], [577, 101, 594, 99], [578, 8, 595, 6], [578, 12, 595, 10, "detectedFaces"], [578, 25, 595, 23], [578, 26, 595, 24, "length"], [578, 32, 595, 30], [578, 35, 595, 33], [578, 36, 595, 34], [578, 38, 595, 36], [579, 10, 596, 8, "console"], [579, 17, 596, 15], [579, 18, 596, 16, "log"], [579, 21, 596, 19], [579, 22, 596, 20], [579, 66, 596, 64], [579, 68, 596, 66, "detectedFaces"], [579, 81, 596, 79], [579, 82, 596, 80, "map"], [579, 85, 596, 83], [579, 86, 596, 84], [579, 87, 596, 85, "face"], [579, 91, 596, 89], [579, 93, 596, 91, "i"], [579, 94, 596, 92], [579, 100, 596, 98], [580, 12, 597, 10, "faceNumber"], [580, 22, 597, 20], [580, 24, 597, 22, "i"], [580, 25, 597, 23], [580, 28, 597, 26], [580, 29, 597, 27], [581, 12, 598, 10, "centerX"], [581, 19, 598, 17], [581, 21, 598, 19, "face"], [581, 25, 598, 23], [581, 26, 598, 24, "boundingBox"], [581, 37, 598, 35], [581, 38, 598, 36, "xCenter"], [581, 45, 598, 43], [582, 12, 599, 10, "centerY"], [582, 19, 599, 17], [582, 21, 599, 19, "face"], [582, 25, 599, 23], [582, 26, 599, 24, "boundingBox"], [582, 37, 599, 35], [582, 38, 599, 36, "yCenter"], [582, 45, 599, 43], [583, 12, 600, 10, "width"], [583, 17, 600, 15], [583, 19, 600, 17, "face"], [583, 23, 600, 21], [583, 24, 600, 22, "boundingBox"], [583, 35, 600, 33], [583, 36, 600, 34, "width"], [583, 41, 600, 39], [584, 12, 601, 10, "height"], [584, 18, 601, 16], [584, 20, 601, 18, "face"], [584, 24, 601, 22], [584, 25, 601, 23, "boundingBox"], [584, 36, 601, 34], [584, 37, 601, 35, "height"], [585, 10, 602, 8], [585, 11, 602, 9], [585, 12, 602, 10], [585, 13, 602, 11], [585, 14, 602, 12], [586, 8, 603, 6], [586, 9, 603, 7], [586, 15, 603, 13], [587, 10, 604, 8, "console"], [587, 17, 604, 15], [587, 18, 604, 16, "log"], [587, 21, 604, 19], [587, 22, 604, 20], [587, 91, 604, 89], [587, 92, 604, 90], [588, 8, 605, 6], [589, 8, 607, 6, "setProcessingProgress"], [589, 29, 607, 27], [589, 30, 607, 28], [589, 32, 607, 30], [589, 33, 607, 31], [591, 8, 609, 6], [592, 8, 610, 6], [592, 12, 610, 10, "detectedFaces"], [592, 25, 610, 23], [592, 26, 610, 24, "length"], [592, 32, 610, 30], [592, 35, 610, 33], [592, 36, 610, 34], [592, 38, 610, 36], [593, 10, 611, 8, "console"], [593, 17, 611, 15], [593, 18, 611, 16, "log"], [593, 21, 611, 19], [593, 22, 611, 20], [593, 61, 611, 59, "detectedFaces"], [593, 74, 611, 72], [593, 75, 611, 73, "length"], [593, 81, 611, 79], [593, 101, 611, 99], [593, 102, 611, 100], [594, 10, 613, 8, "detectedFaces"], [594, 23, 613, 21], [594, 24, 613, 22, "for<PERSON>ach"], [594, 31, 613, 29], [594, 32, 613, 30], [594, 33, 613, 31, "detection"], [594, 42, 613, 40], [594, 44, 613, 42, "index"], [594, 49, 613, 47], [594, 54, 613, 52], [595, 12, 614, 10], [595, 18, 614, 16, "bbox"], [595, 22, 614, 20], [595, 25, 614, 23, "detection"], [595, 34, 614, 32], [595, 35, 614, 33, "boundingBox"], [595, 46, 614, 44], [597, 12, 616, 10], [598, 12, 617, 10], [598, 18, 617, 16, "faceX"], [598, 23, 617, 21], [598, 26, 617, 24, "bbox"], [598, 30, 617, 28], [598, 31, 617, 29, "xCenter"], [598, 38, 617, 36], [598, 41, 617, 39, "img"], [598, 44, 617, 42], [598, 45, 617, 43, "width"], [598, 50, 617, 48], [598, 53, 617, 52, "bbox"], [598, 57, 617, 56], [598, 58, 617, 57, "width"], [598, 63, 617, 62], [598, 66, 617, 65, "img"], [598, 69, 617, 68], [598, 70, 617, 69, "width"], [598, 75, 617, 74], [598, 78, 617, 78], [598, 79, 617, 79], [599, 12, 618, 10], [599, 18, 618, 16, "faceY"], [599, 23, 618, 21], [599, 26, 618, 24, "bbox"], [599, 30, 618, 28], [599, 31, 618, 29, "yCenter"], [599, 38, 618, 36], [599, 41, 618, 39, "img"], [599, 44, 618, 42], [599, 45, 618, 43, "height"], [599, 51, 618, 49], [599, 54, 618, 53, "bbox"], [599, 58, 618, 57], [599, 59, 618, 58, "height"], [599, 65, 618, 64], [599, 68, 618, 67, "img"], [599, 71, 618, 70], [599, 72, 618, 71, "height"], [599, 78, 618, 77], [599, 81, 618, 81], [599, 82, 618, 82], [600, 12, 619, 10], [600, 18, 619, 16, "faceWidth"], [600, 27, 619, 25], [600, 30, 619, 28, "bbox"], [600, 34, 619, 32], [600, 35, 619, 33, "width"], [600, 40, 619, 38], [600, 43, 619, 41, "img"], [600, 46, 619, 44], [600, 47, 619, 45, "width"], [600, 52, 619, 50], [601, 12, 620, 10], [601, 18, 620, 16, "faceHeight"], [601, 28, 620, 26], [601, 31, 620, 29, "bbox"], [601, 35, 620, 33], [601, 36, 620, 34, "height"], [601, 42, 620, 40], [601, 45, 620, 43, "img"], [601, 48, 620, 46], [601, 49, 620, 47, "height"], [601, 55, 620, 53], [603, 12, 622, 10], [604, 12, 623, 10], [604, 18, 623, 16, "padding"], [604, 25, 623, 23], [604, 28, 623, 26], [604, 31, 623, 29], [605, 12, 624, 10], [605, 18, 624, 16, "paddedX"], [605, 25, 624, 23], [605, 28, 624, 26, "Math"], [605, 32, 624, 30], [605, 33, 624, 31, "max"], [605, 36, 624, 34], [605, 37, 624, 35], [605, 38, 624, 36], [605, 40, 624, 38, "faceX"], [605, 45, 624, 43], [605, 48, 624, 46, "faceWidth"], [605, 57, 624, 55], [605, 60, 624, 58, "padding"], [605, 67, 624, 65], [605, 68, 624, 66], [606, 12, 625, 10], [606, 18, 625, 16, "paddedY"], [606, 25, 625, 23], [606, 28, 625, 26, "Math"], [606, 32, 625, 30], [606, 33, 625, 31, "max"], [606, 36, 625, 34], [606, 37, 625, 35], [606, 38, 625, 36], [606, 40, 625, 38, "faceY"], [606, 45, 625, 43], [606, 48, 625, 46, "faceHeight"], [606, 58, 625, 56], [606, 61, 625, 59, "padding"], [606, 68, 625, 66], [606, 69, 625, 67], [607, 12, 626, 10], [607, 18, 626, 16, "<PERSON><PERSON><PERSON><PERSON>"], [607, 29, 626, 27], [607, 32, 626, 30, "Math"], [607, 36, 626, 34], [607, 37, 626, 35, "min"], [607, 40, 626, 38], [607, 41, 626, 39, "img"], [607, 44, 626, 42], [607, 45, 626, 43, "width"], [607, 50, 626, 48], [607, 53, 626, 51, "paddedX"], [607, 60, 626, 58], [607, 62, 626, 60, "faceWidth"], [607, 71, 626, 69], [607, 75, 626, 73], [607, 76, 626, 74], [607, 79, 626, 77], [607, 80, 626, 78], [607, 83, 626, 81, "padding"], [607, 90, 626, 88], [607, 91, 626, 89], [607, 92, 626, 90], [608, 12, 627, 10], [608, 18, 627, 16, "paddedHeight"], [608, 30, 627, 28], [608, 33, 627, 31, "Math"], [608, 37, 627, 35], [608, 38, 627, 36, "min"], [608, 41, 627, 39], [608, 42, 627, 40, "img"], [608, 45, 627, 43], [608, 46, 627, 44, "height"], [608, 52, 627, 50], [608, 55, 627, 53, "paddedY"], [608, 62, 627, 60], [608, 64, 627, 62, "faceHeight"], [608, 74, 627, 72], [608, 78, 627, 76], [608, 79, 627, 77], [608, 82, 627, 80], [608, 83, 627, 81], [608, 86, 627, 84, "padding"], [608, 93, 627, 91], [608, 94, 627, 92], [608, 95, 627, 93], [609, 12, 629, 10, "console"], [609, 19, 629, 17], [609, 20, 629, 18, "log"], [609, 23, 629, 21], [609, 24, 629, 22], [609, 60, 629, 58, "index"], [609, 65, 629, 63], [609, 68, 629, 66], [609, 69, 629, 67], [609, 72, 629, 70], [609, 74, 629, 72], [610, 14, 630, 12, "original"], [610, 22, 630, 20], [610, 24, 630, 22], [611, 16, 630, 24, "x"], [611, 17, 630, 25], [611, 19, 630, 27, "Math"], [611, 23, 630, 31], [611, 24, 630, 32, "round"], [611, 29, 630, 37], [611, 30, 630, 38, "faceX"], [611, 35, 630, 43], [611, 36, 630, 44], [612, 16, 630, 46, "y"], [612, 17, 630, 47], [612, 19, 630, 49, "Math"], [612, 23, 630, 53], [612, 24, 630, 54, "round"], [612, 29, 630, 59], [612, 30, 630, 60, "faceY"], [612, 35, 630, 65], [612, 36, 630, 66], [613, 16, 630, 68, "w"], [613, 17, 630, 69], [613, 19, 630, 71, "Math"], [613, 23, 630, 75], [613, 24, 630, 76, "round"], [613, 29, 630, 81], [613, 30, 630, 82, "faceWidth"], [613, 39, 630, 91], [613, 40, 630, 92], [614, 16, 630, 94, "h"], [614, 17, 630, 95], [614, 19, 630, 97, "Math"], [614, 23, 630, 101], [614, 24, 630, 102, "round"], [614, 29, 630, 107], [614, 30, 630, 108, "faceHeight"], [614, 40, 630, 118], [615, 14, 630, 120], [615, 15, 630, 121], [616, 14, 631, 12, "padded"], [616, 20, 631, 18], [616, 22, 631, 20], [617, 16, 631, 22, "x"], [617, 17, 631, 23], [617, 19, 631, 25, "Math"], [617, 23, 631, 29], [617, 24, 631, 30, "round"], [617, 29, 631, 35], [617, 30, 631, 36, "paddedX"], [617, 37, 631, 43], [617, 38, 631, 44], [618, 16, 631, 46, "y"], [618, 17, 631, 47], [618, 19, 631, 49, "Math"], [618, 23, 631, 53], [618, 24, 631, 54, "round"], [618, 29, 631, 59], [618, 30, 631, 60, "paddedY"], [618, 37, 631, 67], [618, 38, 631, 68], [619, 16, 631, 70, "w"], [619, 17, 631, 71], [619, 19, 631, 73, "Math"], [619, 23, 631, 77], [619, 24, 631, 78, "round"], [619, 29, 631, 83], [619, 30, 631, 84, "<PERSON><PERSON><PERSON><PERSON>"], [619, 41, 631, 95], [619, 42, 631, 96], [620, 16, 631, 98, "h"], [620, 17, 631, 99], [620, 19, 631, 101, "Math"], [620, 23, 631, 105], [620, 24, 631, 106, "round"], [620, 29, 631, 111], [620, 30, 631, 112, "paddedHeight"], [620, 42, 631, 124], [621, 14, 631, 126], [622, 12, 632, 10], [622, 13, 632, 11], [622, 14, 632, 12], [624, 12, 634, 10], [625, 12, 635, 10, "console"], [625, 19, 635, 17], [625, 20, 635, 18, "log"], [625, 23, 635, 21], [625, 24, 635, 22], [625, 70, 635, 68], [625, 72, 635, 70], [626, 14, 636, 12, "width"], [626, 19, 636, 17], [626, 21, 636, 19, "canvas"], [626, 27, 636, 25], [626, 28, 636, 26, "width"], [626, 33, 636, 31], [627, 14, 637, 12, "height"], [627, 20, 637, 18], [627, 22, 637, 20, "canvas"], [627, 28, 637, 26], [627, 29, 637, 27, "height"], [627, 35, 637, 33], [628, 14, 638, 12, "contextValid"], [628, 26, 638, 24], [628, 28, 638, 26], [628, 29, 638, 27], [628, 30, 638, 28, "ctx"], [629, 12, 639, 10], [629, 13, 639, 11], [629, 14, 639, 12], [631, 12, 641, 10], [632, 12, 642, 10, "applyStrongBlur"], [632, 27, 642, 25], [632, 28, 642, 26, "ctx"], [632, 31, 642, 29], [632, 33, 642, 31, "paddedX"], [632, 40, 642, 38], [632, 42, 642, 40, "paddedY"], [632, 49, 642, 47], [632, 51, 642, 49, "<PERSON><PERSON><PERSON><PERSON>"], [632, 62, 642, 60], [632, 64, 642, 62, "paddedHeight"], [632, 76, 642, 74], [632, 77, 642, 75], [634, 12, 644, 10], [635, 12, 645, 10, "console"], [635, 19, 645, 17], [635, 20, 645, 18, "log"], [635, 23, 645, 21], [635, 24, 645, 22], [635, 102, 645, 100], [635, 103, 645, 101], [637, 12, 647, 10], [638, 12, 648, 10], [638, 18, 648, 16, "testImageData"], [638, 31, 648, 29], [638, 34, 648, 32, "ctx"], [638, 37, 648, 35], [638, 38, 648, 36, "getImageData"], [638, 50, 648, 48], [638, 51, 648, 49, "paddedX"], [638, 58, 648, 56], [638, 61, 648, 59], [638, 63, 648, 61], [638, 65, 648, 63, "paddedY"], [638, 72, 648, 70], [638, 75, 648, 73], [638, 77, 648, 75], [638, 79, 648, 77], [638, 81, 648, 79], [638, 83, 648, 81], [638, 85, 648, 83], [638, 86, 648, 84], [639, 12, 649, 10, "console"], [639, 19, 649, 17], [639, 20, 649, 18, "log"], [639, 23, 649, 21], [639, 24, 649, 22], [639, 70, 649, 68], [639, 72, 649, 70], [640, 14, 650, 12, "firstPixel"], [640, 24, 650, 22], [640, 26, 650, 24], [640, 27, 650, 25, "testImageData"], [640, 40, 650, 38], [640, 41, 650, 39, "data"], [640, 45, 650, 43], [640, 46, 650, 44], [640, 47, 650, 45], [640, 48, 650, 46], [640, 50, 650, 48, "testImageData"], [640, 63, 650, 61], [640, 64, 650, 62, "data"], [640, 68, 650, 66], [640, 69, 650, 67], [640, 70, 650, 68], [640, 71, 650, 69], [640, 73, 650, 71, "testImageData"], [640, 86, 650, 84], [640, 87, 650, 85, "data"], [640, 91, 650, 89], [640, 92, 650, 90], [640, 93, 650, 91], [640, 94, 650, 92], [640, 95, 650, 93], [641, 14, 651, 12, "secondPixel"], [641, 25, 651, 23], [641, 27, 651, 25], [641, 28, 651, 26, "testImageData"], [641, 41, 651, 39], [641, 42, 651, 40, "data"], [641, 46, 651, 44], [641, 47, 651, 45], [641, 48, 651, 46], [641, 49, 651, 47], [641, 51, 651, 49, "testImageData"], [641, 64, 651, 62], [641, 65, 651, 63, "data"], [641, 69, 651, 67], [641, 70, 651, 68], [641, 71, 651, 69], [641, 72, 651, 70], [641, 74, 651, 72, "testImageData"], [641, 87, 651, 85], [641, 88, 651, 86, "data"], [641, 92, 651, 90], [641, 93, 651, 91], [641, 94, 651, 92], [641, 95, 651, 93], [642, 12, 652, 10], [642, 13, 652, 11], [642, 14, 652, 12], [643, 12, 654, 10, "console"], [643, 19, 654, 17], [643, 20, 654, 18, "log"], [643, 23, 654, 21], [643, 24, 654, 22], [643, 50, 654, 48, "index"], [643, 55, 654, 53], [643, 58, 654, 56], [643, 59, 654, 57], [643, 79, 654, 77], [643, 80, 654, 78], [644, 10, 655, 8], [644, 11, 655, 9], [644, 12, 655, 10], [645, 10, 657, 8, "console"], [645, 17, 657, 15], [645, 18, 657, 16, "log"], [645, 21, 657, 19], [645, 22, 657, 20], [645, 48, 657, 46, "detectedFaces"], [645, 61, 657, 59], [645, 62, 657, 60, "length"], [645, 68, 657, 66], [645, 104, 657, 102], [645, 105, 657, 103], [646, 8, 658, 6], [646, 9, 658, 7], [646, 15, 658, 13], [647, 10, 659, 8, "console"], [647, 17, 659, 15], [647, 18, 659, 16, "log"], [647, 21, 659, 19], [647, 22, 659, 20], [647, 109, 659, 107], [647, 110, 659, 108], [648, 10, 660, 8], [649, 10, 661, 8, "applyFallbackFaceBlur"], [649, 31, 661, 29], [649, 32, 661, 30, "ctx"], [649, 35, 661, 33], [649, 37, 661, 35, "img"], [649, 40, 661, 38], [649, 41, 661, 39, "width"], [649, 46, 661, 44], [649, 48, 661, 46, "img"], [649, 51, 661, 49], [649, 52, 661, 50, "height"], [649, 58, 661, 56], [649, 59, 661, 57], [650, 8, 662, 6], [651, 8, 664, 6, "setProcessingProgress"], [651, 29, 664, 27], [651, 30, 664, 28], [651, 32, 664, 30], [651, 33, 664, 31], [653, 8, 666, 6], [654, 8, 667, 6, "console"], [654, 15, 667, 13], [654, 16, 667, 14, "log"], [654, 19, 667, 17], [654, 20, 667, 18], [654, 85, 667, 83], [654, 86, 667, 84], [655, 8, 668, 6], [655, 14, 668, 12, "blurredImageBlob"], [655, 30, 668, 28], [655, 33, 668, 31], [655, 39, 668, 37], [655, 43, 668, 41, "Promise"], [655, 50, 668, 48], [655, 51, 668, 56, "resolve"], [655, 58, 668, 63], [655, 62, 668, 68], [656, 10, 669, 8, "canvas"], [656, 16, 669, 14], [656, 17, 669, 15, "toBlob"], [656, 23, 669, 21], [656, 24, 669, 23, "blob"], [656, 28, 669, 27], [656, 32, 669, 32, "resolve"], [656, 39, 669, 39], [656, 40, 669, 40, "blob"], [656, 44, 669, 45], [656, 45, 669, 46], [656, 47, 669, 48], [656, 59, 669, 60], [656, 61, 669, 62], [656, 64, 669, 65], [656, 65, 669, 66], [657, 8, 670, 6], [657, 9, 670, 7], [657, 10, 670, 8], [658, 8, 672, 6], [658, 14, 672, 12, "blurredImageUrl"], [658, 29, 672, 27], [658, 32, 672, 30, "URL"], [658, 35, 672, 33], [658, 36, 672, 34, "createObjectURL"], [658, 51, 672, 49], [658, 52, 672, 50, "blurredImageBlob"], [658, 68, 672, 66], [658, 69, 672, 67], [659, 8, 673, 6, "console"], [659, 15, 673, 13], [659, 16, 673, 14, "log"], [659, 19, 673, 17], [659, 20, 673, 18], [659, 66, 673, 64], [659, 68, 673, 66, "blurredImageUrl"], [659, 83, 673, 81], [659, 84, 673, 82, "substring"], [659, 93, 673, 91], [659, 94, 673, 92], [659, 95, 673, 93], [659, 97, 673, 95], [659, 99, 673, 97], [659, 100, 673, 98], [659, 103, 673, 101], [659, 108, 673, 106], [659, 109, 673, 107], [660, 8, 675, 6, "setProcessingProgress"], [660, 29, 675, 27], [660, 30, 675, 28], [660, 33, 675, 31], [660, 34, 675, 32], [662, 8, 677, 6], [663, 8, 678, 6], [663, 14, 678, 12, "completeProcessing"], [663, 32, 678, 30], [663, 33, 678, 31, "blurredImageUrl"], [663, 48, 678, 46], [663, 49, 678, 47], [664, 6, 680, 4], [664, 7, 680, 5], [664, 8, 680, 6], [664, 15, 680, 13, "error"], [664, 20, 680, 18], [664, 22, 680, 20], [665, 8, 681, 6, "console"], [665, 15, 681, 13], [665, 16, 681, 14, "error"], [665, 21, 681, 19], [665, 22, 681, 20], [665, 57, 681, 55], [665, 59, 681, 57, "error"], [665, 64, 681, 62], [665, 65, 681, 63], [666, 8, 682, 6, "setErrorMessage"], [666, 23, 682, 21], [666, 24, 682, 22], [666, 50, 682, 48], [666, 51, 682, 49], [667, 8, 683, 6, "setProcessingState"], [667, 26, 683, 24], [667, 27, 683, 25], [667, 34, 683, 32], [667, 35, 683, 33], [668, 6, 684, 4], [669, 4, 685, 2], [669, 5, 685, 3], [671, 4, 687, 2], [672, 4, 688, 2], [672, 10, 688, 8, "completeProcessing"], [672, 28, 688, 26], [672, 31, 688, 29], [672, 37, 688, 36, "blurredImageUrl"], [672, 52, 688, 59], [672, 56, 688, 64], [673, 6, 689, 4], [673, 10, 689, 8], [674, 8, 690, 6, "setProcessingState"], [674, 26, 690, 24], [674, 27, 690, 25], [674, 37, 690, 35], [674, 38, 690, 36], [676, 8, 692, 6], [677, 8, 693, 6], [677, 14, 693, 12, "timestamp"], [677, 23, 693, 21], [677, 26, 693, 24, "Date"], [677, 30, 693, 28], [677, 31, 693, 29, "now"], [677, 34, 693, 32], [677, 35, 693, 33], [677, 36, 693, 34], [678, 8, 694, 6], [678, 14, 694, 12, "result"], [678, 20, 694, 18], [678, 23, 694, 21], [679, 10, 695, 8, "imageUrl"], [679, 18, 695, 16], [679, 20, 695, 18, "blurredImageUrl"], [679, 35, 695, 33], [680, 10, 696, 8, "localUri"], [680, 18, 696, 16], [680, 20, 696, 18, "blurredImageUrl"], [680, 35, 696, 33], [681, 10, 697, 8, "challengeCode"], [681, 23, 697, 21], [681, 25, 697, 23, "challengeCode"], [681, 38, 697, 36], [681, 42, 697, 40], [681, 44, 697, 42], [682, 10, 698, 8, "timestamp"], [682, 19, 698, 17], [683, 10, 699, 8, "jobId"], [683, 15, 699, 13], [683, 17, 699, 15], [683, 27, 699, 25, "timestamp"], [683, 36, 699, 34], [683, 38, 699, 36], [684, 10, 700, 8, "status"], [684, 16, 700, 14], [684, 18, 700, 16], [685, 8, 701, 6], [685, 9, 701, 7], [686, 8, 703, 6, "console"], [686, 15, 703, 13], [686, 16, 703, 14, "log"], [686, 19, 703, 17], [686, 20, 703, 18], [686, 100, 703, 98], [686, 102, 703, 100], [687, 10, 704, 8, "imageUrl"], [687, 18, 704, 16], [687, 20, 704, 18, "blurredImageUrl"], [687, 35, 704, 33], [687, 36, 704, 34, "substring"], [687, 45, 704, 43], [687, 46, 704, 44], [687, 47, 704, 45], [687, 49, 704, 47], [687, 51, 704, 49], [687, 52, 704, 50], [687, 55, 704, 53], [687, 60, 704, 58], [688, 10, 705, 8, "timestamp"], [688, 19, 705, 17], [689, 10, 706, 8, "jobId"], [689, 15, 706, 13], [689, 17, 706, 15, "result"], [689, 23, 706, 21], [689, 24, 706, 22, "jobId"], [690, 8, 707, 6], [690, 9, 707, 7], [690, 10, 707, 8], [692, 8, 709, 6], [693, 8, 710, 6, "onComplete"], [693, 18, 710, 16], [693, 19, 710, 17, "result"], [693, 25, 710, 23], [693, 26, 710, 24], [694, 6, 712, 4], [694, 7, 712, 5], [694, 8, 712, 6], [694, 15, 712, 13, "error"], [694, 20, 712, 18], [694, 22, 712, 20], [695, 8, 713, 6, "console"], [695, 15, 713, 13], [695, 16, 713, 14, "error"], [695, 21, 713, 19], [695, 22, 713, 20], [695, 57, 713, 55], [695, 59, 713, 57, "error"], [695, 64, 713, 62], [695, 65, 713, 63], [696, 8, 714, 6, "setErrorMessage"], [696, 23, 714, 21], [696, 24, 714, 22], [696, 56, 714, 54], [696, 57, 714, 55], [697, 8, 715, 6, "setProcessingState"], [697, 26, 715, 24], [697, 27, 715, 25], [697, 34, 715, 32], [697, 35, 715, 33], [698, 6, 716, 4], [699, 4, 717, 2], [699, 5, 717, 3], [701, 4, 719, 2], [702, 4, 720, 2], [702, 10, 720, 8, "triggerServerProcessing"], [702, 33, 720, 31], [702, 36, 720, 34], [702, 42, 720, 34, "triggerServerProcessing"], [702, 43, 720, 41, "privateImageUrl"], [702, 58, 720, 64], [702, 60, 720, 66, "timestamp"], [702, 69, 720, 83], [702, 74, 720, 88], [703, 6, 721, 4], [703, 10, 721, 8], [704, 8, 722, 6, "console"], [704, 15, 722, 13], [704, 16, 722, 14, "log"], [704, 19, 722, 17], [704, 20, 722, 18], [704, 74, 722, 72], [704, 76, 722, 74, "privateImageUrl"], [704, 91, 722, 89], [704, 92, 722, 90], [705, 8, 723, 6, "setProcessingState"], [705, 26, 723, 24], [705, 27, 723, 25], [705, 39, 723, 37], [705, 40, 723, 38], [706, 8, 724, 6, "setProcessingProgress"], [706, 29, 724, 27], [706, 30, 724, 28], [706, 32, 724, 30], [706, 33, 724, 31], [707, 8, 726, 6], [707, 14, 726, 12, "requestBody"], [707, 25, 726, 23], [707, 28, 726, 26], [708, 10, 727, 8, "imageUrl"], [708, 18, 727, 16], [708, 20, 727, 18, "privateImageUrl"], [708, 35, 727, 33], [709, 10, 728, 8, "userId"], [709, 16, 728, 14], [710, 10, 729, 8, "requestId"], [710, 19, 729, 17], [711, 10, 730, 8, "timestamp"], [711, 19, 730, 17], [712, 10, 731, 8, "platform"], [712, 18, 731, 16], [712, 20, 731, 18], [713, 8, 732, 6], [713, 9, 732, 7], [714, 8, 734, 6, "console"], [714, 15, 734, 13], [714, 16, 734, 14, "log"], [714, 19, 734, 17], [714, 20, 734, 18], [714, 65, 734, 63], [714, 67, 734, 65, "requestBody"], [714, 78, 734, 76], [714, 79, 734, 77], [716, 8, 736, 6], [717, 8, 737, 6], [717, 14, 737, 12, "response"], [717, 22, 737, 20], [717, 25, 737, 23], [717, 31, 737, 29, "fetch"], [717, 36, 737, 34], [717, 37, 737, 35], [717, 40, 737, 38, "API_BASE_URL"], [717, 52, 737, 50], [717, 72, 737, 70], [717, 74, 737, 72], [718, 10, 738, 8, "method"], [718, 16, 738, 14], [718, 18, 738, 16], [718, 24, 738, 22], [719, 10, 739, 8, "headers"], [719, 17, 739, 15], [719, 19, 739, 17], [720, 12, 740, 10], [720, 26, 740, 24], [720, 28, 740, 26], [720, 46, 740, 44], [721, 12, 741, 10], [721, 27, 741, 25], [721, 29, 741, 27], [721, 39, 741, 37], [721, 45, 741, 43, "getAuthToken"], [721, 57, 741, 55], [721, 58, 741, 56], [721, 59, 741, 57], [722, 10, 742, 8], [722, 11, 742, 9], [723, 10, 743, 8, "body"], [723, 14, 743, 12], [723, 16, 743, 14, "JSON"], [723, 20, 743, 18], [723, 21, 743, 19, "stringify"], [723, 30, 743, 28], [723, 31, 743, 29, "requestBody"], [723, 42, 743, 40], [724, 8, 744, 6], [724, 9, 744, 7], [724, 10, 744, 8], [725, 8, 746, 6], [725, 12, 746, 10], [725, 13, 746, 11, "response"], [725, 21, 746, 19], [725, 22, 746, 20, "ok"], [725, 24, 746, 22], [725, 26, 746, 24], [726, 10, 747, 8], [726, 16, 747, 14, "errorText"], [726, 25, 747, 23], [726, 28, 747, 26], [726, 34, 747, 32, "response"], [726, 42, 747, 40], [726, 43, 747, 41, "text"], [726, 47, 747, 45], [726, 48, 747, 46], [726, 49, 747, 47], [727, 10, 748, 8, "console"], [727, 17, 748, 15], [727, 18, 748, 16, "error"], [727, 23, 748, 21], [727, 24, 748, 22], [727, 68, 748, 66], [727, 70, 748, 68, "response"], [727, 78, 748, 76], [727, 79, 748, 77, "status"], [727, 85, 748, 83], [727, 87, 748, 85, "errorText"], [727, 96, 748, 94], [727, 97, 748, 95], [728, 10, 749, 8], [728, 16, 749, 14], [728, 20, 749, 18, "Error"], [728, 25, 749, 23], [728, 26, 749, 24], [728, 48, 749, 46, "response"], [728, 56, 749, 54], [728, 57, 749, 55, "status"], [728, 63, 749, 61], [728, 67, 749, 65, "response"], [728, 75, 749, 73], [728, 76, 749, 74, "statusText"], [728, 86, 749, 84], [728, 88, 749, 86], [728, 89, 749, 87], [729, 8, 750, 6], [730, 8, 752, 6], [730, 14, 752, 12, "result"], [730, 20, 752, 18], [730, 23, 752, 21], [730, 29, 752, 27, "response"], [730, 37, 752, 35], [730, 38, 752, 36, "json"], [730, 42, 752, 40], [730, 43, 752, 41], [730, 44, 752, 42], [731, 8, 753, 6, "console"], [731, 15, 753, 13], [731, 16, 753, 14, "log"], [731, 19, 753, 17], [731, 20, 753, 18], [731, 68, 753, 66], [731, 70, 753, 68, "result"], [731, 76, 753, 74], [731, 77, 753, 75], [732, 8, 755, 6], [732, 12, 755, 10], [732, 13, 755, 11, "result"], [732, 19, 755, 17], [732, 20, 755, 18, "jobId"], [732, 25, 755, 23], [732, 27, 755, 25], [733, 10, 756, 8], [733, 16, 756, 14], [733, 20, 756, 18, "Error"], [733, 25, 756, 23], [733, 26, 756, 24], [733, 70, 756, 68], [733, 71, 756, 69], [734, 8, 757, 6], [736, 8, 759, 6], [737, 8, 760, 6], [737, 14, 760, 12, "pollForCompletion"], [737, 31, 760, 29], [737, 32, 760, 30, "result"], [737, 38, 760, 36], [737, 39, 760, 37, "jobId"], [737, 44, 760, 42], [737, 46, 760, 44, "timestamp"], [737, 55, 760, 53], [737, 56, 760, 54], [738, 6, 761, 4], [738, 7, 761, 5], [738, 8, 761, 6], [738, 15, 761, 13, "error"], [738, 20, 761, 18], [738, 22, 761, 20], [739, 8, 762, 6, "console"], [739, 15, 762, 13], [739, 16, 762, 14, "error"], [739, 21, 762, 19], [739, 22, 762, 20], [739, 57, 762, 55], [739, 59, 762, 57, "error"], [739, 64, 762, 62], [739, 65, 762, 63], [740, 8, 763, 6, "setErrorMessage"], [740, 23, 763, 21], [740, 24, 763, 22], [740, 52, 763, 50, "error"], [740, 57, 763, 55], [740, 58, 763, 56, "message"], [740, 65, 763, 63], [740, 67, 763, 65], [740, 68, 763, 66], [741, 8, 764, 6, "setProcessingState"], [741, 26, 764, 24], [741, 27, 764, 25], [741, 34, 764, 32], [741, 35, 764, 33], [742, 6, 765, 4], [743, 4, 766, 2], [743, 5, 766, 3], [744, 4, 767, 2], [745, 4, 768, 2], [745, 10, 768, 8, "pollForCompletion"], [745, 27, 768, 25], [745, 30, 768, 28], [745, 36, 768, 28, "pollForCompletion"], [745, 37, 768, 35, "jobId"], [745, 42, 768, 48], [745, 44, 768, 50, "timestamp"], [745, 53, 768, 67], [745, 55, 768, 69, "attempts"], [745, 63, 768, 77], [745, 66, 768, 80], [745, 67, 768, 81], [745, 72, 768, 86], [746, 6, 769, 4], [746, 12, 769, 10, "MAX_ATTEMPTS"], [746, 24, 769, 22], [746, 27, 769, 25], [746, 29, 769, 27], [746, 30, 769, 28], [746, 31, 769, 29], [747, 6, 770, 4], [747, 12, 770, 10, "POLL_INTERVAL"], [747, 25, 770, 23], [747, 28, 770, 26], [747, 32, 770, 30], [747, 33, 770, 31], [747, 34, 770, 32], [749, 6, 772, 4, "console"], [749, 13, 772, 11], [749, 14, 772, 12, "log"], [749, 17, 772, 15], [749, 18, 772, 16], [749, 53, 772, 51, "attempts"], [749, 61, 772, 59], [749, 64, 772, 62], [749, 65, 772, 63], [749, 69, 772, 67, "MAX_ATTEMPTS"], [749, 81, 772, 79], [749, 93, 772, 91, "jobId"], [749, 98, 772, 96], [749, 100, 772, 98], [749, 101, 772, 99], [750, 6, 774, 4], [750, 10, 774, 8, "attempts"], [750, 18, 774, 16], [750, 22, 774, 20, "MAX_ATTEMPTS"], [750, 34, 774, 32], [750, 36, 774, 34], [751, 8, 775, 6, "console"], [751, 15, 775, 13], [751, 16, 775, 14, "error"], [751, 21, 775, 19], [751, 22, 775, 20], [751, 75, 775, 73], [751, 76, 775, 74], [752, 8, 776, 6, "setErrorMessage"], [752, 23, 776, 21], [752, 24, 776, 22], [752, 63, 776, 61], [752, 64, 776, 62], [753, 8, 777, 6, "setProcessingState"], [753, 26, 777, 24], [753, 27, 777, 25], [753, 34, 777, 32], [753, 35, 777, 33], [754, 8, 778, 6], [755, 6, 779, 4], [756, 6, 781, 4], [756, 10, 781, 8], [757, 8, 782, 6], [757, 14, 782, 12, "response"], [757, 22, 782, 20], [757, 25, 782, 23], [757, 31, 782, 29, "fetch"], [757, 36, 782, 34], [757, 37, 782, 35], [757, 40, 782, 38, "API_BASE_URL"], [757, 52, 782, 50], [757, 75, 782, 73, "jobId"], [757, 80, 782, 78], [757, 82, 782, 80], [757, 84, 782, 82], [758, 10, 783, 8, "headers"], [758, 17, 783, 15], [758, 19, 783, 17], [759, 12, 784, 10], [759, 27, 784, 25], [759, 29, 784, 27], [759, 39, 784, 37], [759, 45, 784, 43, "getAuthToken"], [759, 57, 784, 55], [759, 58, 784, 56], [759, 59, 784, 57], [760, 10, 785, 8], [761, 8, 786, 6], [761, 9, 786, 7], [761, 10, 786, 8], [762, 8, 788, 6], [762, 12, 788, 10], [762, 13, 788, 11, "response"], [762, 21, 788, 19], [762, 22, 788, 20, "ok"], [762, 24, 788, 22], [762, 26, 788, 24], [763, 10, 789, 8], [763, 16, 789, 14], [763, 20, 789, 18, "Error"], [763, 25, 789, 23], [763, 26, 789, 24], [763, 34, 789, 32, "response"], [763, 42, 789, 40], [763, 43, 789, 41, "status"], [763, 49, 789, 47], [763, 54, 789, 52, "response"], [763, 62, 789, 60], [763, 63, 789, 61, "statusText"], [763, 73, 789, 71], [763, 75, 789, 73], [763, 76, 789, 74], [764, 8, 790, 6], [765, 8, 792, 6], [765, 14, 792, 12, "status"], [765, 20, 792, 18], [765, 23, 792, 21], [765, 29, 792, 27, "response"], [765, 37, 792, 35], [765, 38, 792, 36, "json"], [765, 42, 792, 40], [765, 43, 792, 41], [765, 44, 792, 42], [766, 8, 793, 6, "console"], [766, 15, 793, 13], [766, 16, 793, 14, "log"], [766, 19, 793, 17], [766, 20, 793, 18], [766, 54, 793, 52], [766, 56, 793, 54, "status"], [766, 62, 793, 60], [766, 63, 793, 61], [767, 8, 795, 6], [767, 12, 795, 10, "status"], [767, 18, 795, 16], [767, 19, 795, 17, "status"], [767, 25, 795, 23], [767, 30, 795, 28], [767, 41, 795, 39], [767, 43, 795, 41], [768, 10, 796, 8, "console"], [768, 17, 796, 15], [768, 18, 796, 16, "log"], [768, 21, 796, 19], [768, 22, 796, 20], [768, 73, 796, 71], [768, 74, 796, 72], [769, 10, 797, 8, "setProcessingProgress"], [769, 31, 797, 29], [769, 32, 797, 30], [769, 35, 797, 33], [769, 36, 797, 34], [770, 10, 798, 8, "setProcessingState"], [770, 28, 798, 26], [770, 29, 798, 27], [770, 40, 798, 38], [770, 41, 798, 39], [771, 10, 799, 8], [772, 10, 800, 8], [772, 16, 800, 14, "result"], [772, 22, 800, 20], [772, 25, 800, 23], [773, 12, 801, 10, "imageUrl"], [773, 20, 801, 18], [773, 22, 801, 20, "status"], [773, 28, 801, 26], [773, 29, 801, 27, "publicUrl"], [773, 38, 801, 36], [774, 12, 801, 38], [775, 12, 802, 10, "localUri"], [775, 20, 802, 18], [775, 22, 802, 20, "capturedPhoto"], [775, 35, 802, 33], [775, 39, 802, 37, "status"], [775, 45, 802, 43], [775, 46, 802, 44, "publicUrl"], [775, 55, 802, 53], [776, 12, 802, 55], [777, 12, 803, 10, "challengeCode"], [777, 25, 803, 23], [777, 27, 803, 25, "challengeCode"], [777, 40, 803, 38], [777, 44, 803, 42], [777, 46, 803, 44], [778, 12, 804, 10, "timestamp"], [778, 21, 804, 19], [779, 12, 805, 10, "processingStatus"], [779, 28, 805, 26], [779, 30, 805, 28], [780, 10, 806, 8], [780, 11, 806, 9], [781, 10, 807, 8, "console"], [781, 17, 807, 15], [781, 18, 807, 16, "log"], [781, 21, 807, 19], [781, 22, 807, 20], [781, 57, 807, 55], [781, 59, 807, 57, "result"], [781, 65, 807, 63], [781, 66, 807, 64], [782, 10, 808, 8, "onComplete"], [782, 20, 808, 18], [782, 21, 808, 19, "result"], [782, 27, 808, 25], [782, 28, 808, 26], [783, 10, 809, 8], [784, 8, 810, 6], [784, 9, 810, 7], [784, 15, 810, 13], [784, 19, 810, 17, "status"], [784, 25, 810, 23], [784, 26, 810, 24, "status"], [784, 32, 810, 30], [784, 37, 810, 35], [784, 45, 810, 43], [784, 47, 810, 45], [785, 10, 811, 8, "console"], [785, 17, 811, 15], [785, 18, 811, 16, "error"], [785, 23, 811, 21], [785, 24, 811, 22], [785, 60, 811, 58], [785, 62, 811, 60, "status"], [785, 68, 811, 66], [785, 69, 811, 67, "error"], [785, 74, 811, 72], [785, 75, 811, 73], [786, 10, 812, 8], [786, 16, 812, 14], [786, 20, 812, 18, "Error"], [786, 25, 812, 23], [786, 26, 812, 24, "status"], [786, 32, 812, 30], [786, 33, 812, 31, "error"], [786, 38, 812, 36], [786, 42, 812, 40], [786, 61, 812, 59], [786, 62, 812, 60], [787, 8, 813, 6], [787, 9, 813, 7], [787, 15, 813, 13], [788, 10, 814, 8], [789, 10, 815, 8], [789, 16, 815, 14, "progressValue"], [789, 29, 815, 27], [789, 32, 815, 30], [789, 34, 815, 32], [789, 37, 815, 36, "attempts"], [789, 45, 815, 44], [789, 48, 815, 47, "MAX_ATTEMPTS"], [789, 60, 815, 59], [789, 63, 815, 63], [789, 65, 815, 65], [790, 10, 816, 8, "console"], [790, 17, 816, 15], [790, 18, 816, 16, "log"], [790, 21, 816, 19], [790, 22, 816, 20], [790, 71, 816, 69, "progressValue"], [790, 84, 816, 82], [790, 87, 816, 85], [790, 88, 816, 86], [791, 10, 817, 8, "setProcessingProgress"], [791, 31, 817, 29], [791, 32, 817, 30, "progressValue"], [791, 45, 817, 43], [791, 46, 817, 44], [792, 10, 819, 8, "setTimeout"], [792, 20, 819, 18], [792, 21, 819, 19], [792, 27, 819, 25], [793, 12, 820, 10, "pollForCompletion"], [793, 29, 820, 27], [793, 30, 820, 28, "jobId"], [793, 35, 820, 33], [793, 37, 820, 35, "timestamp"], [793, 46, 820, 44], [793, 48, 820, 46, "attempts"], [793, 56, 820, 54], [793, 59, 820, 57], [793, 60, 820, 58], [793, 61, 820, 59], [794, 10, 821, 8], [794, 11, 821, 9], [794, 13, 821, 11, "POLL_INTERVAL"], [794, 26, 821, 24], [794, 27, 821, 25], [795, 8, 822, 6], [796, 6, 823, 4], [796, 7, 823, 5], [796, 8, 823, 6], [796, 15, 823, 13, "error"], [796, 20, 823, 18], [796, 22, 823, 20], [797, 8, 824, 6, "console"], [797, 15, 824, 13], [797, 16, 824, 14, "error"], [797, 21, 824, 19], [797, 22, 824, 20], [797, 54, 824, 52], [797, 56, 824, 54, "error"], [797, 61, 824, 59], [797, 62, 824, 60], [798, 8, 825, 6, "setErrorMessage"], [798, 23, 825, 21], [798, 24, 825, 22], [798, 62, 825, 60, "error"], [798, 67, 825, 65], [798, 68, 825, 66, "message"], [798, 75, 825, 73], [798, 77, 825, 75], [798, 78, 825, 76], [799, 8, 826, 6, "setProcessingState"], [799, 26, 826, 24], [799, 27, 826, 25], [799, 34, 826, 32], [799, 35, 826, 33], [800, 6, 827, 4], [801, 4, 828, 2], [801, 5, 828, 3], [802, 4, 829, 2], [803, 4, 830, 2], [803, 10, 830, 8, "getAuthToken"], [803, 22, 830, 20], [803, 25, 830, 23], [803, 31, 830, 23, "getAuthToken"], [803, 32, 830, 23], [803, 37, 830, 52], [804, 6, 831, 4], [805, 6, 832, 4], [806, 6, 833, 4], [806, 13, 833, 11], [806, 30, 833, 28], [807, 4, 834, 2], [807, 5, 834, 3], [809, 4, 836, 2], [810, 4, 837, 2], [810, 10, 837, 8, "retryCapture"], [810, 22, 837, 20], [810, 25, 837, 23], [810, 29, 837, 23, "useCallback"], [810, 47, 837, 34], [810, 49, 837, 35], [810, 55, 837, 41], [811, 6, 838, 4, "console"], [811, 13, 838, 11], [811, 14, 838, 12, "log"], [811, 17, 838, 15], [811, 18, 838, 16], [811, 55, 838, 53], [811, 56, 838, 54], [812, 6, 839, 4, "setProcessingState"], [812, 24, 839, 22], [812, 25, 839, 23], [812, 31, 839, 29], [812, 32, 839, 30], [813, 6, 840, 4, "setErrorMessage"], [813, 21, 840, 19], [813, 22, 840, 20], [813, 24, 840, 22], [813, 25, 840, 23], [814, 6, 841, 4, "setCapturedPhoto"], [814, 22, 841, 20], [814, 23, 841, 21], [814, 25, 841, 23], [814, 26, 841, 24], [815, 6, 842, 4, "setProcessingProgress"], [815, 27, 842, 25], [815, 28, 842, 26], [815, 29, 842, 27], [815, 30, 842, 28], [816, 4, 843, 2], [816, 5, 843, 3], [816, 7, 843, 5], [816, 9, 843, 7], [816, 10, 843, 8], [817, 4, 844, 2], [818, 4, 845, 2], [818, 8, 845, 2, "useEffect"], [818, 24, 845, 11], [818, 26, 845, 12], [818, 32, 845, 18], [819, 6, 846, 4, "console"], [819, 13, 846, 11], [819, 14, 846, 12, "log"], [819, 17, 846, 15], [819, 18, 846, 16], [819, 53, 846, 51], [819, 55, 846, 53, "permission"], [819, 65, 846, 63], [819, 66, 846, 64], [820, 6, 847, 4], [820, 10, 847, 8, "permission"], [820, 20, 847, 18], [820, 22, 847, 20], [821, 8, 848, 6, "console"], [821, 15, 848, 13], [821, 16, 848, 14, "log"], [821, 19, 848, 17], [821, 20, 848, 18], [821, 57, 848, 55], [821, 59, 848, 57, "permission"], [821, 69, 848, 67], [821, 70, 848, 68, "granted"], [821, 77, 848, 75], [821, 78, 848, 76], [822, 6, 849, 4], [823, 4, 850, 2], [823, 5, 850, 3], [823, 7, 850, 5], [823, 8, 850, 6, "permission"], [823, 18, 850, 16], [823, 19, 850, 17], [823, 20, 850, 18], [824, 4, 851, 2], [825, 4, 852, 2], [825, 8, 852, 6], [825, 9, 852, 7, "permission"], [825, 19, 852, 17], [825, 21, 852, 19], [826, 6, 853, 4, "console"], [826, 13, 853, 11], [826, 14, 853, 12, "log"], [826, 17, 853, 15], [826, 18, 853, 16], [826, 67, 853, 65], [826, 68, 853, 66], [827, 6, 854, 4], [827, 26, 855, 6], [827, 30, 855, 6, "_jsxDevRuntime"], [827, 44, 855, 6], [827, 45, 855, 6, "jsxDEV"], [827, 51, 855, 6], [827, 53, 855, 7, "_View"], [827, 58, 855, 7], [827, 59, 855, 7, "default"], [827, 66, 855, 11], [828, 8, 855, 12, "style"], [828, 13, 855, 17], [828, 15, 855, 19, "styles"], [828, 21, 855, 25], [828, 22, 855, 26, "container"], [828, 31, 855, 36], [829, 8, 855, 36, "children"], [829, 16, 855, 36], [829, 32, 856, 8], [829, 36, 856, 8, "_jsxDevRuntime"], [829, 50, 856, 8], [829, 51, 856, 8, "jsxDEV"], [829, 57, 856, 8], [829, 59, 856, 9, "_ActivityIndicator"], [829, 77, 856, 9], [829, 78, 856, 9, "default"], [829, 85, 856, 26], [830, 10, 856, 27, "size"], [830, 14, 856, 31], [830, 16, 856, 32], [830, 23, 856, 39], [831, 10, 856, 40, "color"], [831, 15, 856, 45], [831, 17, 856, 46], [832, 8, 856, 55], [833, 10, 856, 55, "fileName"], [833, 18, 856, 55], [833, 20, 856, 55, "_jsxFileName"], [833, 32, 856, 55], [834, 10, 856, 55, "lineNumber"], [834, 20, 856, 55], [835, 10, 856, 55, "columnNumber"], [835, 22, 856, 55], [836, 8, 856, 55], [836, 15, 856, 57], [836, 16, 856, 58], [836, 31, 857, 8], [836, 35, 857, 8, "_jsxDevRuntime"], [836, 49, 857, 8], [836, 50, 857, 8, "jsxDEV"], [836, 56, 857, 8], [836, 58, 857, 9, "_Text"], [836, 63, 857, 9], [836, 64, 857, 9, "default"], [836, 71, 857, 13], [837, 10, 857, 14, "style"], [837, 15, 857, 19], [837, 17, 857, 21, "styles"], [837, 23, 857, 27], [837, 24, 857, 28, "loadingText"], [837, 35, 857, 40], [838, 10, 857, 40, "children"], [838, 18, 857, 40], [838, 20, 857, 41], [839, 8, 857, 58], [840, 10, 857, 58, "fileName"], [840, 18, 857, 58], [840, 20, 857, 58, "_jsxFileName"], [840, 32, 857, 58], [841, 10, 857, 58, "lineNumber"], [841, 20, 857, 58], [842, 10, 857, 58, "columnNumber"], [842, 22, 857, 58], [843, 8, 857, 58], [843, 15, 857, 64], [843, 16, 857, 65], [844, 6, 857, 65], [845, 8, 857, 65, "fileName"], [845, 16, 857, 65], [845, 18, 857, 65, "_jsxFileName"], [845, 30, 857, 65], [846, 8, 857, 65, "lineNumber"], [846, 18, 857, 65], [847, 8, 857, 65, "columnNumber"], [847, 20, 857, 65], [848, 6, 857, 65], [848, 13, 858, 12], [848, 14, 858, 13], [849, 4, 860, 2], [850, 4, 861, 2], [850, 8, 861, 6], [850, 9, 861, 7, "permission"], [850, 19, 861, 17], [850, 20, 861, 18, "granted"], [850, 27, 861, 25], [850, 29, 861, 27], [851, 6, 862, 4, "console"], [851, 13, 862, 11], [851, 14, 862, 12, "log"], [851, 17, 862, 15], [851, 18, 862, 16], [851, 93, 862, 91], [851, 94, 862, 92], [852, 6, 863, 4], [852, 26, 864, 6], [852, 30, 864, 6, "_jsxDevRuntime"], [852, 44, 864, 6], [852, 45, 864, 6, "jsxDEV"], [852, 51, 864, 6], [852, 53, 864, 7, "_View"], [852, 58, 864, 7], [852, 59, 864, 7, "default"], [852, 66, 864, 11], [853, 8, 864, 12, "style"], [853, 13, 864, 17], [853, 15, 864, 19, "styles"], [853, 21, 864, 25], [853, 22, 864, 26, "container"], [853, 31, 864, 36], [854, 8, 864, 36, "children"], [854, 16, 864, 36], [854, 31, 865, 8], [854, 35, 865, 8, "_jsxDevRuntime"], [854, 49, 865, 8], [854, 50, 865, 8, "jsxDEV"], [854, 56, 865, 8], [854, 58, 865, 9, "_View"], [854, 63, 865, 9], [854, 64, 865, 9, "default"], [854, 71, 865, 13], [855, 10, 865, 14, "style"], [855, 15, 865, 19], [855, 17, 865, 21, "styles"], [855, 23, 865, 27], [855, 24, 865, 28, "permissionContent"], [855, 41, 865, 46], [856, 10, 865, 46, "children"], [856, 18, 865, 46], [856, 34, 866, 10], [856, 38, 866, 10, "_jsxDevRuntime"], [856, 52, 866, 10], [856, 53, 866, 10, "jsxDEV"], [856, 59, 866, 10], [856, 61, 866, 11, "_lucideReactNative"], [856, 79, 866, 11], [856, 80, 866, 11, "Camera"], [856, 86, 866, 21], [857, 12, 866, 22, "size"], [857, 16, 866, 26], [857, 18, 866, 28], [857, 20, 866, 31], [858, 12, 866, 32, "color"], [858, 17, 866, 37], [858, 19, 866, 38], [859, 10, 866, 47], [860, 12, 866, 47, "fileName"], [860, 20, 866, 47], [860, 22, 866, 47, "_jsxFileName"], [860, 34, 866, 47], [861, 12, 866, 47, "lineNumber"], [861, 22, 866, 47], [862, 12, 866, 47, "columnNumber"], [862, 24, 866, 47], [863, 10, 866, 47], [863, 17, 866, 49], [863, 18, 866, 50], [863, 33, 867, 10], [863, 37, 867, 10, "_jsxDevRuntime"], [863, 51, 867, 10], [863, 52, 867, 10, "jsxDEV"], [863, 58, 867, 10], [863, 60, 867, 11, "_Text"], [863, 65, 867, 11], [863, 66, 867, 11, "default"], [863, 73, 867, 15], [864, 12, 867, 16, "style"], [864, 17, 867, 21], [864, 19, 867, 23, "styles"], [864, 25, 867, 29], [864, 26, 867, 30, "permissionTitle"], [864, 41, 867, 46], [865, 12, 867, 46, "children"], [865, 20, 867, 46], [865, 22, 867, 47], [866, 10, 867, 73], [867, 12, 867, 73, "fileName"], [867, 20, 867, 73], [867, 22, 867, 73, "_jsxFileName"], [867, 34, 867, 73], [868, 12, 867, 73, "lineNumber"], [868, 22, 867, 73], [869, 12, 867, 73, "columnNumber"], [869, 24, 867, 73], [870, 10, 867, 73], [870, 17, 867, 79], [870, 18, 867, 80], [870, 33, 868, 10], [870, 37, 868, 10, "_jsxDevRuntime"], [870, 51, 868, 10], [870, 52, 868, 10, "jsxDEV"], [870, 58, 868, 10], [870, 60, 868, 11, "_Text"], [870, 65, 868, 11], [870, 66, 868, 11, "default"], [870, 73, 868, 15], [871, 12, 868, 16, "style"], [871, 17, 868, 21], [871, 19, 868, 23, "styles"], [871, 25, 868, 29], [871, 26, 868, 30, "permissionDescription"], [871, 47, 868, 52], [872, 12, 868, 52, "children"], [872, 20, 868, 52], [872, 22, 868, 53], [873, 10, 871, 10], [874, 12, 871, 10, "fileName"], [874, 20, 871, 10], [874, 22, 871, 10, "_jsxFileName"], [874, 34, 871, 10], [875, 12, 871, 10, "lineNumber"], [875, 22, 871, 10], [876, 12, 871, 10, "columnNumber"], [876, 24, 871, 10], [877, 10, 871, 10], [877, 17, 871, 16], [877, 18, 871, 17], [877, 33, 872, 10], [877, 37, 872, 10, "_jsxDevRuntime"], [877, 51, 872, 10], [877, 52, 872, 10, "jsxDEV"], [877, 58, 872, 10], [877, 60, 872, 11, "_TouchableOpacity"], [877, 77, 872, 11], [877, 78, 872, 11, "default"], [877, 85, 872, 27], [878, 12, 872, 28, "onPress"], [878, 19, 872, 35], [878, 21, 872, 37, "requestPermission"], [878, 38, 872, 55], [879, 12, 872, 56, "style"], [879, 17, 872, 61], [879, 19, 872, 63, "styles"], [879, 25, 872, 69], [879, 26, 872, 70, "primaryButton"], [879, 39, 872, 84], [880, 12, 872, 84, "children"], [880, 20, 872, 84], [880, 35, 873, 12], [880, 39, 873, 12, "_jsxDevRuntime"], [880, 53, 873, 12], [880, 54, 873, 12, "jsxDEV"], [880, 60, 873, 12], [880, 62, 873, 13, "_Text"], [880, 67, 873, 13], [880, 68, 873, 13, "default"], [880, 75, 873, 17], [881, 14, 873, 18, "style"], [881, 19, 873, 23], [881, 21, 873, 25, "styles"], [881, 27, 873, 31], [881, 28, 873, 32, "primaryButtonText"], [881, 45, 873, 50], [882, 14, 873, 50, "children"], [882, 22, 873, 50], [882, 24, 873, 51], [883, 12, 873, 67], [884, 14, 873, 67, "fileName"], [884, 22, 873, 67], [884, 24, 873, 67, "_jsxFileName"], [884, 36, 873, 67], [885, 14, 873, 67, "lineNumber"], [885, 24, 873, 67], [886, 14, 873, 67, "columnNumber"], [886, 26, 873, 67], [887, 12, 873, 67], [887, 19, 873, 73], [888, 10, 873, 74], [889, 12, 873, 74, "fileName"], [889, 20, 873, 74], [889, 22, 873, 74, "_jsxFileName"], [889, 34, 873, 74], [890, 12, 873, 74, "lineNumber"], [890, 22, 873, 74], [891, 12, 873, 74, "columnNumber"], [891, 24, 873, 74], [892, 10, 873, 74], [892, 17, 874, 28], [892, 18, 874, 29], [892, 33, 875, 10], [892, 37, 875, 10, "_jsxDevRuntime"], [892, 51, 875, 10], [892, 52, 875, 10, "jsxDEV"], [892, 58, 875, 10], [892, 60, 875, 11, "_TouchableOpacity"], [892, 77, 875, 11], [892, 78, 875, 11, "default"], [892, 85, 875, 27], [893, 12, 875, 28, "onPress"], [893, 19, 875, 35], [893, 21, 875, 37, "onCancel"], [893, 29, 875, 46], [894, 12, 875, 47, "style"], [894, 17, 875, 52], [894, 19, 875, 54, "styles"], [894, 25, 875, 60], [894, 26, 875, 61, "secondaryButton"], [894, 41, 875, 77], [895, 12, 875, 77, "children"], [895, 20, 875, 77], [895, 35, 876, 12], [895, 39, 876, 12, "_jsxDevRuntime"], [895, 53, 876, 12], [895, 54, 876, 12, "jsxDEV"], [895, 60, 876, 12], [895, 62, 876, 13, "_Text"], [895, 67, 876, 13], [895, 68, 876, 13, "default"], [895, 75, 876, 17], [896, 14, 876, 18, "style"], [896, 19, 876, 23], [896, 21, 876, 25, "styles"], [896, 27, 876, 31], [896, 28, 876, 32, "secondaryButtonText"], [896, 47, 876, 52], [897, 14, 876, 52, "children"], [897, 22, 876, 52], [897, 24, 876, 53], [898, 12, 876, 59], [899, 14, 876, 59, "fileName"], [899, 22, 876, 59], [899, 24, 876, 59, "_jsxFileName"], [899, 36, 876, 59], [900, 14, 876, 59, "lineNumber"], [900, 24, 876, 59], [901, 14, 876, 59, "columnNumber"], [901, 26, 876, 59], [902, 12, 876, 59], [902, 19, 876, 65], [903, 10, 876, 66], [904, 12, 876, 66, "fileName"], [904, 20, 876, 66], [904, 22, 876, 66, "_jsxFileName"], [904, 34, 876, 66], [905, 12, 876, 66, "lineNumber"], [905, 22, 876, 66], [906, 12, 876, 66, "columnNumber"], [906, 24, 876, 66], [907, 10, 876, 66], [907, 17, 877, 28], [907, 18, 877, 29], [908, 8, 877, 29], [909, 10, 877, 29, "fileName"], [909, 18, 877, 29], [909, 20, 877, 29, "_jsxFileName"], [909, 32, 877, 29], [910, 10, 877, 29, "lineNumber"], [910, 20, 877, 29], [911, 10, 877, 29, "columnNumber"], [911, 22, 877, 29], [912, 8, 877, 29], [912, 15, 878, 14], [913, 6, 878, 15], [914, 8, 878, 15, "fileName"], [914, 16, 878, 15], [914, 18, 878, 15, "_jsxFileName"], [914, 30, 878, 15], [915, 8, 878, 15, "lineNumber"], [915, 18, 878, 15], [916, 8, 878, 15, "columnNumber"], [916, 20, 878, 15], [917, 6, 878, 15], [917, 13, 879, 12], [917, 14, 879, 13], [918, 4, 881, 2], [919, 4, 882, 2], [920, 4, 883, 2, "console"], [920, 11, 883, 9], [920, 12, 883, 10, "log"], [920, 15, 883, 13], [920, 16, 883, 14], [920, 55, 883, 53], [920, 56, 883, 54], [921, 4, 885, 2], [921, 24, 886, 4], [921, 28, 886, 4, "_jsxDevRuntime"], [921, 42, 886, 4], [921, 43, 886, 4, "jsxDEV"], [921, 49, 886, 4], [921, 51, 886, 5, "_View"], [921, 56, 886, 5], [921, 57, 886, 5, "default"], [921, 64, 886, 9], [922, 6, 886, 10, "style"], [922, 11, 886, 15], [922, 13, 886, 17, "styles"], [922, 19, 886, 23], [922, 20, 886, 24, "container"], [922, 29, 886, 34], [923, 6, 886, 34, "children"], [923, 14, 886, 34], [923, 30, 888, 6], [923, 34, 888, 6, "_jsxDevRuntime"], [923, 48, 888, 6], [923, 49, 888, 6, "jsxDEV"], [923, 55, 888, 6], [923, 57, 888, 7, "_View"], [923, 62, 888, 7], [923, 63, 888, 7, "default"], [923, 70, 888, 11], [924, 8, 888, 12, "style"], [924, 13, 888, 17], [924, 15, 888, 19, "styles"], [924, 21, 888, 25], [924, 22, 888, 26, "cameraContainer"], [924, 37, 888, 42], [925, 8, 888, 43, "id"], [925, 10, 888, 45], [925, 12, 888, 46], [925, 29, 888, 63], [926, 8, 888, 63, "children"], [926, 16, 888, 63], [926, 32, 889, 8], [926, 36, 889, 8, "_jsxDevRuntime"], [926, 50, 889, 8], [926, 51, 889, 8, "jsxDEV"], [926, 57, 889, 8], [926, 59, 889, 9, "_expoCamera"], [926, 70, 889, 9], [926, 71, 889, 9, "CameraView"], [926, 81, 889, 19], [927, 10, 890, 10, "ref"], [927, 13, 890, 13], [927, 15, 890, 15, "cameraRef"], [927, 24, 890, 25], [928, 10, 891, 10, "style"], [928, 15, 891, 15], [928, 17, 891, 17], [928, 18, 891, 18, "styles"], [928, 24, 891, 24], [928, 25, 891, 25, "camera"], [928, 31, 891, 31], [928, 33, 891, 33], [929, 12, 891, 35, "backgroundColor"], [929, 27, 891, 50], [929, 29, 891, 52], [930, 10, 891, 62], [930, 11, 891, 63], [930, 12, 891, 65], [931, 10, 892, 10, "facing"], [931, 16, 892, 16], [931, 18, 892, 17], [931, 24, 892, 23], [932, 10, 893, 10, "onLayout"], [932, 18, 893, 18], [932, 20, 893, 21, "e"], [932, 21, 893, 22], [932, 25, 893, 27], [933, 12, 894, 12, "console"], [933, 19, 894, 19], [933, 20, 894, 20, "log"], [933, 23, 894, 23], [933, 24, 894, 24], [933, 56, 894, 56], [933, 58, 894, 58, "e"], [933, 59, 894, 59], [933, 60, 894, 60, "nativeEvent"], [933, 71, 894, 71], [933, 72, 894, 72, "layout"], [933, 78, 894, 78], [933, 79, 894, 79], [934, 12, 895, 12, "setViewSize"], [934, 23, 895, 23], [934, 24, 895, 24], [935, 14, 895, 26, "width"], [935, 19, 895, 31], [935, 21, 895, 33, "e"], [935, 22, 895, 34], [935, 23, 895, 35, "nativeEvent"], [935, 34, 895, 46], [935, 35, 895, 47, "layout"], [935, 41, 895, 53], [935, 42, 895, 54, "width"], [935, 47, 895, 59], [936, 14, 895, 61, "height"], [936, 20, 895, 67], [936, 22, 895, 69, "e"], [936, 23, 895, 70], [936, 24, 895, 71, "nativeEvent"], [936, 35, 895, 82], [936, 36, 895, 83, "layout"], [936, 42, 895, 89], [936, 43, 895, 90, "height"], [937, 12, 895, 97], [937, 13, 895, 98], [937, 14, 895, 99], [938, 10, 896, 10], [938, 11, 896, 12], [939, 10, 897, 10, "onCameraReady"], [939, 23, 897, 23], [939, 25, 897, 25, "onCameraReady"], [939, 26, 897, 25], [939, 31, 897, 31], [940, 12, 898, 12, "console"], [940, 19, 898, 19], [940, 20, 898, 20, "log"], [940, 23, 898, 23], [940, 24, 898, 24], [940, 55, 898, 55], [940, 56, 898, 56], [941, 12, 899, 12, "setIsCameraReady"], [941, 28, 899, 28], [941, 29, 899, 29], [941, 33, 899, 33], [941, 34, 899, 34], [941, 35, 899, 35], [941, 36, 899, 36], [942, 10, 900, 10], [942, 11, 900, 12], [943, 10, 901, 10, "onMountError"], [943, 22, 901, 22], [943, 24, 901, 25, "error"], [943, 29, 901, 30], [943, 33, 901, 35], [944, 12, 902, 12, "console"], [944, 19, 902, 19], [944, 20, 902, 20, "error"], [944, 25, 902, 25], [944, 26, 902, 26], [944, 63, 902, 63], [944, 65, 902, 65, "error"], [944, 70, 902, 70], [944, 71, 902, 71], [945, 12, 903, 12, "setErrorMessage"], [945, 27, 903, 27], [945, 28, 903, 28], [945, 57, 903, 57], [945, 58, 903, 58], [946, 12, 904, 12, "setProcessingState"], [946, 30, 904, 30], [946, 31, 904, 31], [946, 38, 904, 38], [946, 39, 904, 39], [947, 10, 905, 10], [948, 8, 905, 12], [949, 10, 905, 12, "fileName"], [949, 18, 905, 12], [949, 20, 905, 12, "_jsxFileName"], [949, 32, 905, 12], [950, 10, 905, 12, "lineNumber"], [950, 20, 905, 12], [951, 10, 905, 12, "columnNumber"], [951, 22, 905, 12], [952, 8, 905, 12], [952, 15, 906, 9], [952, 16, 906, 10], [952, 18, 908, 9], [952, 19, 908, 10, "isCameraReady"], [952, 32, 908, 23], [952, 49, 909, 10], [952, 53, 909, 10, "_jsxDevRuntime"], [952, 67, 909, 10], [952, 68, 909, 10, "jsxDEV"], [952, 74, 909, 10], [952, 76, 909, 11, "_View"], [952, 81, 909, 11], [952, 82, 909, 11, "default"], [952, 89, 909, 15], [953, 10, 909, 16, "style"], [953, 15, 909, 21], [953, 17, 909, 23], [953, 18, 909, 24, "StyleSheet"], [953, 37, 909, 34], [953, 38, 909, 35, "absoluteFill"], [953, 50, 909, 47], [953, 52, 909, 49], [954, 12, 909, 51, "backgroundColor"], [954, 27, 909, 66], [954, 29, 909, 68], [954, 49, 909, 88], [955, 12, 909, 90, "justifyContent"], [955, 26, 909, 104], [955, 28, 909, 106], [955, 36, 909, 114], [956, 12, 909, 116, "alignItems"], [956, 22, 909, 126], [956, 24, 909, 128], [956, 32, 909, 136], [957, 12, 909, 138, "zIndex"], [957, 18, 909, 144], [957, 20, 909, 146], [958, 10, 909, 151], [958, 11, 909, 152], [958, 12, 909, 154], [959, 10, 909, 154, "children"], [959, 18, 909, 154], [959, 33, 910, 12], [959, 37, 910, 12, "_jsxDevRuntime"], [959, 51, 910, 12], [959, 52, 910, 12, "jsxDEV"], [959, 58, 910, 12], [959, 60, 910, 13, "_View"], [959, 65, 910, 13], [959, 66, 910, 13, "default"], [959, 73, 910, 17], [960, 12, 910, 18, "style"], [960, 17, 910, 23], [960, 19, 910, 25], [961, 14, 910, 27, "backgroundColor"], [961, 29, 910, 42], [961, 31, 910, 44], [961, 51, 910, 64], [962, 14, 910, 66, "padding"], [962, 21, 910, 73], [962, 23, 910, 75], [962, 25, 910, 77], [963, 14, 910, 79, "borderRadius"], [963, 26, 910, 91], [963, 28, 910, 93], [963, 30, 910, 95], [964, 14, 910, 97, "alignItems"], [964, 24, 910, 107], [964, 26, 910, 109], [965, 12, 910, 118], [965, 13, 910, 120], [966, 12, 910, 120, "children"], [966, 20, 910, 120], [966, 36, 911, 14], [966, 40, 911, 14, "_jsxDevRuntime"], [966, 54, 911, 14], [966, 55, 911, 14, "jsxDEV"], [966, 61, 911, 14], [966, 63, 911, 15, "_ActivityIndicator"], [966, 81, 911, 15], [966, 82, 911, 15, "default"], [966, 89, 911, 32], [967, 14, 911, 33, "size"], [967, 18, 911, 37], [967, 20, 911, 38], [967, 27, 911, 45], [968, 14, 911, 46, "color"], [968, 19, 911, 51], [968, 21, 911, 52], [968, 30, 911, 61], [969, 14, 911, 62, "style"], [969, 19, 911, 67], [969, 21, 911, 69], [970, 16, 911, 71, "marginBottom"], [970, 28, 911, 83], [970, 30, 911, 85], [971, 14, 911, 88], [972, 12, 911, 90], [973, 14, 911, 90, "fileName"], [973, 22, 911, 90], [973, 24, 911, 90, "_jsxFileName"], [973, 36, 911, 90], [974, 14, 911, 90, "lineNumber"], [974, 24, 911, 90], [975, 14, 911, 90, "columnNumber"], [975, 26, 911, 90], [976, 12, 911, 90], [976, 19, 911, 92], [976, 20, 911, 93], [976, 35, 912, 14], [976, 39, 912, 14, "_jsxDevRuntime"], [976, 53, 912, 14], [976, 54, 912, 14, "jsxDEV"], [976, 60, 912, 14], [976, 62, 912, 15, "_Text"], [976, 67, 912, 15], [976, 68, 912, 15, "default"], [976, 75, 912, 19], [977, 14, 912, 20, "style"], [977, 19, 912, 25], [977, 21, 912, 27], [978, 16, 912, 29, "color"], [978, 21, 912, 34], [978, 23, 912, 36], [978, 29, 912, 42], [979, 16, 912, 44, "fontSize"], [979, 24, 912, 52], [979, 26, 912, 54], [979, 28, 912, 56], [980, 16, 912, 58, "fontWeight"], [980, 26, 912, 68], [980, 28, 912, 70], [981, 14, 912, 76], [981, 15, 912, 78], [982, 14, 912, 78, "children"], [982, 22, 912, 78], [982, 24, 912, 79], [983, 12, 912, 101], [984, 14, 912, 101, "fileName"], [984, 22, 912, 101], [984, 24, 912, 101, "_jsxFileName"], [984, 36, 912, 101], [985, 14, 912, 101, "lineNumber"], [985, 24, 912, 101], [986, 14, 912, 101, "columnNumber"], [986, 26, 912, 101], [987, 12, 912, 101], [987, 19, 912, 107], [987, 20, 912, 108], [987, 35, 913, 14], [987, 39, 913, 14, "_jsxDevRuntime"], [987, 53, 913, 14], [987, 54, 913, 14, "jsxDEV"], [987, 60, 913, 14], [987, 62, 913, 15, "_Text"], [987, 67, 913, 15], [987, 68, 913, 15, "default"], [987, 75, 913, 19], [988, 14, 913, 20, "style"], [988, 19, 913, 25], [988, 21, 913, 27], [989, 16, 913, 29, "color"], [989, 21, 913, 34], [989, 23, 913, 36], [989, 32, 913, 45], [990, 16, 913, 47, "fontSize"], [990, 24, 913, 55], [990, 26, 913, 57], [990, 28, 913, 59], [991, 16, 913, 61, "marginTop"], [991, 25, 913, 70], [991, 27, 913, 72], [992, 14, 913, 74], [992, 15, 913, 76], [993, 14, 913, 76, "children"], [993, 22, 913, 76], [993, 24, 913, 77], [994, 12, 913, 88], [995, 14, 913, 88, "fileName"], [995, 22, 913, 88], [995, 24, 913, 88, "_jsxFileName"], [995, 36, 913, 88], [996, 14, 913, 88, "lineNumber"], [996, 24, 913, 88], [997, 14, 913, 88, "columnNumber"], [997, 26, 913, 88], [998, 12, 913, 88], [998, 19, 913, 94], [998, 20, 913, 95], [999, 10, 913, 95], [1000, 12, 913, 95, "fileName"], [1000, 20, 913, 95], [1000, 22, 913, 95, "_jsxFileName"], [1000, 34, 913, 95], [1001, 12, 913, 95, "lineNumber"], [1001, 22, 913, 95], [1002, 12, 913, 95, "columnNumber"], [1002, 24, 913, 95], [1003, 10, 913, 95], [1003, 17, 914, 18], [1004, 8, 914, 19], [1005, 10, 914, 19, "fileName"], [1005, 18, 914, 19], [1005, 20, 914, 19, "_jsxFileName"], [1005, 32, 914, 19], [1006, 10, 914, 19, "lineNumber"], [1006, 20, 914, 19], [1007, 10, 914, 19, "columnNumber"], [1007, 22, 914, 19], [1008, 8, 914, 19], [1008, 15, 915, 16], [1008, 16, 916, 9], [1008, 18, 919, 9, "isCameraReady"], [1008, 31, 919, 22], [1008, 35, 919, 26, "previewBlurEnabled"], [1008, 53, 919, 44], [1008, 57, 919, 48, "viewSize"], [1008, 65, 919, 56], [1008, 66, 919, 57, "width"], [1008, 71, 919, 62], [1008, 74, 919, 65], [1008, 75, 919, 66], [1008, 92, 920, 10], [1008, 96, 920, 10, "_jsxDevRuntime"], [1008, 110, 920, 10], [1008, 111, 920, 10, "jsxDEV"], [1008, 117, 920, 10], [1008, 119, 920, 10, "_jsxDevRuntime"], [1008, 133, 920, 10], [1008, 134, 920, 10, "Fragment"], [1008, 142, 920, 10], [1009, 10, 920, 10, "children"], [1009, 18, 920, 10], [1009, 34, 922, 12], [1009, 38, 922, 12, "_jsxDevRuntime"], [1009, 52, 922, 12], [1009, 53, 922, 12, "jsxDEV"], [1009, 59, 922, 12], [1009, 61, 922, 13, "_LiveFaceCanvas"], [1009, 76, 922, 13], [1009, 77, 922, 13, "default"], [1009, 84, 922, 27], [1010, 12, 922, 28, "containerId"], [1010, 23, 922, 39], [1010, 25, 922, 40], [1010, 42, 922, 57], [1011, 12, 922, 58, "width"], [1011, 17, 922, 63], [1011, 19, 922, 65, "viewSize"], [1011, 27, 922, 73], [1011, 28, 922, 74, "width"], [1011, 33, 922, 80], [1012, 12, 922, 81, "height"], [1012, 18, 922, 87], [1012, 20, 922, 89, "viewSize"], [1012, 28, 922, 97], [1012, 29, 922, 98, "height"], [1013, 10, 922, 105], [1014, 12, 922, 105, "fileName"], [1014, 20, 922, 105], [1014, 22, 922, 105, "_jsxFileName"], [1014, 34, 922, 105], [1015, 12, 922, 105, "lineNumber"], [1015, 22, 922, 105], [1016, 12, 922, 105, "columnNumber"], [1016, 24, 922, 105], [1017, 10, 922, 105], [1017, 17, 922, 107], [1017, 18, 922, 108], [1017, 33, 923, 12], [1017, 37, 923, 12, "_jsxDevRuntime"], [1017, 51, 923, 12], [1017, 52, 923, 12, "jsxDEV"], [1017, 58, 923, 12], [1017, 60, 923, 13, "_View"], [1017, 65, 923, 13], [1017, 66, 923, 13, "default"], [1017, 73, 923, 17], [1018, 12, 923, 18, "style"], [1018, 17, 923, 23], [1018, 19, 923, 25], [1018, 20, 923, 26, "StyleSheet"], [1018, 39, 923, 36], [1018, 40, 923, 37, "absoluteFill"], [1018, 52, 923, 49], [1018, 54, 923, 51], [1019, 14, 923, 53, "pointerEvents"], [1019, 27, 923, 66], [1019, 29, 923, 68], [1020, 12, 923, 75], [1020, 13, 923, 76], [1020, 14, 923, 78], [1021, 12, 923, 78, "children"], [1021, 20, 923, 78], [1021, 36, 925, 12], [1021, 40, 925, 12, "_jsxDevRuntime"], [1021, 54, 925, 12], [1021, 55, 925, 12, "jsxDEV"], [1021, 61, 925, 12], [1021, 63, 925, 13, "_expoBlur"], [1021, 72, 925, 13], [1021, 73, 925, 13, "BlurView"], [1021, 81, 925, 21], [1022, 14, 925, 22, "intensity"], [1022, 23, 925, 31], [1022, 25, 925, 33], [1022, 27, 925, 36], [1023, 14, 925, 37, "tint"], [1023, 18, 925, 41], [1023, 20, 925, 42], [1023, 26, 925, 48], [1024, 14, 925, 49, "style"], [1024, 19, 925, 54], [1024, 21, 925, 56], [1024, 22, 925, 57, "styles"], [1024, 28, 925, 63], [1024, 29, 925, 64, "blurZone"], [1024, 37, 925, 72], [1024, 39, 925, 74], [1025, 16, 926, 14, "left"], [1025, 20, 926, 18], [1025, 22, 926, 20], [1025, 23, 926, 21], [1026, 16, 927, 14, "top"], [1026, 19, 927, 17], [1026, 21, 927, 19, "viewSize"], [1026, 29, 927, 27], [1026, 30, 927, 28, "height"], [1026, 36, 927, 34], [1026, 39, 927, 37], [1026, 42, 927, 40], [1027, 16, 928, 14, "width"], [1027, 21, 928, 19], [1027, 23, 928, 21, "viewSize"], [1027, 31, 928, 29], [1027, 32, 928, 30, "width"], [1027, 37, 928, 35], [1028, 16, 929, 14, "height"], [1028, 22, 929, 20], [1028, 24, 929, 22, "viewSize"], [1028, 32, 929, 30], [1028, 33, 929, 31, "height"], [1028, 39, 929, 37], [1028, 42, 929, 40], [1028, 46, 929, 44], [1029, 16, 930, 14, "borderRadius"], [1029, 28, 930, 26], [1029, 30, 930, 28], [1030, 14, 931, 12], [1030, 15, 931, 13], [1031, 12, 931, 15], [1032, 14, 931, 15, "fileName"], [1032, 22, 931, 15], [1032, 24, 931, 15, "_jsxFileName"], [1032, 36, 931, 15], [1033, 14, 931, 15, "lineNumber"], [1033, 24, 931, 15], [1034, 14, 931, 15, "columnNumber"], [1034, 26, 931, 15], [1035, 12, 931, 15], [1035, 19, 931, 17], [1035, 20, 931, 18], [1035, 35, 933, 12], [1035, 39, 933, 12, "_jsxDevRuntime"], [1035, 53, 933, 12], [1035, 54, 933, 12, "jsxDEV"], [1035, 60, 933, 12], [1035, 62, 933, 13, "_expoBlur"], [1035, 71, 933, 13], [1035, 72, 933, 13, "BlurView"], [1035, 80, 933, 21], [1036, 14, 933, 22, "intensity"], [1036, 23, 933, 31], [1036, 25, 933, 33], [1036, 27, 933, 36], [1037, 14, 933, 37, "tint"], [1037, 18, 933, 41], [1037, 20, 933, 42], [1037, 26, 933, 48], [1038, 14, 933, 49, "style"], [1038, 19, 933, 54], [1038, 21, 933, 56], [1038, 22, 933, 57, "styles"], [1038, 28, 933, 63], [1038, 29, 933, 64, "blurZone"], [1038, 37, 933, 72], [1038, 39, 933, 74], [1039, 16, 934, 14, "left"], [1039, 20, 934, 18], [1039, 22, 934, 20], [1039, 23, 934, 21], [1040, 16, 935, 14, "top"], [1040, 19, 935, 17], [1040, 21, 935, 19], [1040, 22, 935, 20], [1041, 16, 936, 14, "width"], [1041, 21, 936, 19], [1041, 23, 936, 21, "viewSize"], [1041, 31, 936, 29], [1041, 32, 936, 30, "width"], [1041, 37, 936, 35], [1042, 16, 937, 14, "height"], [1042, 22, 937, 20], [1042, 24, 937, 22, "viewSize"], [1042, 32, 937, 30], [1042, 33, 937, 31, "height"], [1042, 39, 937, 37], [1042, 42, 937, 40], [1042, 45, 937, 43], [1043, 16, 938, 14, "borderRadius"], [1043, 28, 938, 26], [1043, 30, 938, 28], [1044, 14, 939, 12], [1044, 15, 939, 13], [1045, 12, 939, 15], [1046, 14, 939, 15, "fileName"], [1046, 22, 939, 15], [1046, 24, 939, 15, "_jsxFileName"], [1046, 36, 939, 15], [1047, 14, 939, 15, "lineNumber"], [1047, 24, 939, 15], [1048, 14, 939, 15, "columnNumber"], [1048, 26, 939, 15], [1049, 12, 939, 15], [1049, 19, 939, 17], [1049, 20, 939, 18], [1049, 35, 941, 12], [1049, 39, 941, 12, "_jsxDevRuntime"], [1049, 53, 941, 12], [1049, 54, 941, 12, "jsxDEV"], [1049, 60, 941, 12], [1049, 62, 941, 13, "_expoBlur"], [1049, 71, 941, 13], [1049, 72, 941, 13, "BlurView"], [1049, 80, 941, 21], [1050, 14, 941, 22, "intensity"], [1050, 23, 941, 31], [1050, 25, 941, 33], [1050, 27, 941, 36], [1051, 14, 941, 37, "tint"], [1051, 18, 941, 41], [1051, 20, 941, 42], [1051, 26, 941, 48], [1052, 14, 941, 49, "style"], [1052, 19, 941, 54], [1052, 21, 941, 56], [1052, 22, 941, 57, "styles"], [1052, 28, 941, 63], [1052, 29, 941, 64, "blurZone"], [1052, 37, 941, 72], [1052, 39, 941, 74], [1053, 16, 942, 14, "left"], [1053, 20, 942, 18], [1053, 22, 942, 20, "viewSize"], [1053, 30, 942, 28], [1053, 31, 942, 29, "width"], [1053, 36, 942, 34], [1053, 39, 942, 37], [1053, 42, 942, 40], [1053, 45, 942, 44, "viewSize"], [1053, 53, 942, 52], [1053, 54, 942, 53, "width"], [1053, 59, 942, 58], [1053, 62, 942, 61], [1053, 66, 942, 66], [1054, 16, 943, 14, "top"], [1054, 19, 943, 17], [1054, 21, 943, 19, "viewSize"], [1054, 29, 943, 27], [1054, 30, 943, 28, "height"], [1054, 36, 943, 34], [1054, 39, 943, 37], [1054, 43, 943, 41], [1054, 46, 943, 45, "viewSize"], [1054, 54, 943, 53], [1054, 55, 943, 54, "width"], [1054, 60, 943, 59], [1054, 63, 943, 62], [1054, 67, 943, 67], [1055, 16, 944, 14, "width"], [1055, 21, 944, 19], [1055, 23, 944, 21, "viewSize"], [1055, 31, 944, 29], [1055, 32, 944, 30, "width"], [1055, 37, 944, 35], [1055, 40, 944, 38], [1055, 43, 944, 41], [1056, 16, 945, 14, "height"], [1056, 22, 945, 20], [1056, 24, 945, 22, "viewSize"], [1056, 32, 945, 30], [1056, 33, 945, 31, "width"], [1056, 38, 945, 36], [1056, 41, 945, 39], [1056, 44, 945, 42], [1057, 16, 946, 14, "borderRadius"], [1057, 28, 946, 26], [1057, 30, 946, 29, "viewSize"], [1057, 38, 946, 37], [1057, 39, 946, 38, "width"], [1057, 44, 946, 43], [1057, 47, 946, 46], [1057, 50, 946, 49], [1057, 53, 946, 53], [1058, 14, 947, 12], [1058, 15, 947, 13], [1059, 12, 947, 15], [1060, 14, 947, 15, "fileName"], [1060, 22, 947, 15], [1060, 24, 947, 15, "_jsxFileName"], [1060, 36, 947, 15], [1061, 14, 947, 15, "lineNumber"], [1061, 24, 947, 15], [1062, 14, 947, 15, "columnNumber"], [1062, 26, 947, 15], [1063, 12, 947, 15], [1063, 19, 947, 17], [1063, 20, 947, 18], [1063, 35, 948, 12], [1063, 39, 948, 12, "_jsxDevRuntime"], [1063, 53, 948, 12], [1063, 54, 948, 12, "jsxDEV"], [1063, 60, 948, 12], [1063, 62, 948, 13, "_expoBlur"], [1063, 71, 948, 13], [1063, 72, 948, 13, "BlurView"], [1063, 80, 948, 21], [1064, 14, 948, 22, "intensity"], [1064, 23, 948, 31], [1064, 25, 948, 33], [1064, 27, 948, 36], [1065, 14, 948, 37, "tint"], [1065, 18, 948, 41], [1065, 20, 948, 42], [1065, 26, 948, 48], [1066, 14, 948, 49, "style"], [1066, 19, 948, 54], [1066, 21, 948, 56], [1066, 22, 948, 57, "styles"], [1066, 28, 948, 63], [1066, 29, 948, 64, "blurZone"], [1066, 37, 948, 72], [1066, 39, 948, 74], [1067, 16, 949, 14, "left"], [1067, 20, 949, 18], [1067, 22, 949, 20, "viewSize"], [1067, 30, 949, 28], [1067, 31, 949, 29, "width"], [1067, 36, 949, 34], [1067, 39, 949, 37], [1067, 42, 949, 40], [1067, 45, 949, 44, "viewSize"], [1067, 53, 949, 52], [1067, 54, 949, 53, "width"], [1067, 59, 949, 58], [1067, 62, 949, 61], [1067, 66, 949, 66], [1068, 16, 950, 14, "top"], [1068, 19, 950, 17], [1068, 21, 950, 19, "viewSize"], [1068, 29, 950, 27], [1068, 30, 950, 28, "height"], [1068, 36, 950, 34], [1068, 39, 950, 37], [1068, 42, 950, 40], [1068, 45, 950, 44, "viewSize"], [1068, 53, 950, 52], [1068, 54, 950, 53, "width"], [1068, 59, 950, 58], [1068, 62, 950, 61], [1068, 66, 950, 66], [1069, 16, 951, 14, "width"], [1069, 21, 951, 19], [1069, 23, 951, 21, "viewSize"], [1069, 31, 951, 29], [1069, 32, 951, 30, "width"], [1069, 37, 951, 35], [1069, 40, 951, 38], [1069, 43, 951, 41], [1070, 16, 952, 14, "height"], [1070, 22, 952, 20], [1070, 24, 952, 22, "viewSize"], [1070, 32, 952, 30], [1070, 33, 952, 31, "width"], [1070, 38, 952, 36], [1070, 41, 952, 39], [1070, 44, 952, 42], [1071, 16, 953, 14, "borderRadius"], [1071, 28, 953, 26], [1071, 30, 953, 29, "viewSize"], [1071, 38, 953, 37], [1071, 39, 953, 38, "width"], [1071, 44, 953, 43], [1071, 47, 953, 46], [1071, 50, 953, 49], [1071, 53, 953, 53], [1072, 14, 954, 12], [1072, 15, 954, 13], [1073, 12, 954, 15], [1074, 14, 954, 15, "fileName"], [1074, 22, 954, 15], [1074, 24, 954, 15, "_jsxFileName"], [1074, 36, 954, 15], [1075, 14, 954, 15, "lineNumber"], [1075, 24, 954, 15], [1076, 14, 954, 15, "columnNumber"], [1076, 26, 954, 15], [1077, 12, 954, 15], [1077, 19, 954, 17], [1077, 20, 954, 18], [1077, 35, 955, 12], [1077, 39, 955, 12, "_jsxDevRuntime"], [1077, 53, 955, 12], [1077, 54, 955, 12, "jsxDEV"], [1077, 60, 955, 12], [1077, 62, 955, 13, "_expoBlur"], [1077, 71, 955, 13], [1077, 72, 955, 13, "BlurView"], [1077, 80, 955, 21], [1078, 14, 955, 22, "intensity"], [1078, 23, 955, 31], [1078, 25, 955, 33], [1078, 27, 955, 36], [1079, 14, 955, 37, "tint"], [1079, 18, 955, 41], [1079, 20, 955, 42], [1079, 26, 955, 48], [1080, 14, 955, 49, "style"], [1080, 19, 955, 54], [1080, 21, 955, 56], [1080, 22, 955, 57, "styles"], [1080, 28, 955, 63], [1080, 29, 955, 64, "blurZone"], [1080, 37, 955, 72], [1080, 39, 955, 74], [1081, 16, 956, 14, "left"], [1081, 20, 956, 18], [1081, 22, 956, 20, "viewSize"], [1081, 30, 956, 28], [1081, 31, 956, 29, "width"], [1081, 36, 956, 34], [1081, 39, 956, 37], [1081, 42, 956, 40], [1081, 45, 956, 44, "viewSize"], [1081, 53, 956, 52], [1081, 54, 956, 53, "width"], [1081, 59, 956, 58], [1081, 62, 956, 61], [1081, 66, 956, 66], [1082, 16, 957, 14, "top"], [1082, 19, 957, 17], [1082, 21, 957, 19, "viewSize"], [1082, 29, 957, 27], [1082, 30, 957, 28, "height"], [1082, 36, 957, 34], [1082, 39, 957, 37], [1082, 42, 957, 40], [1082, 45, 957, 44, "viewSize"], [1082, 53, 957, 52], [1082, 54, 957, 53, "width"], [1082, 59, 957, 58], [1082, 62, 957, 61], [1082, 66, 957, 66], [1083, 16, 958, 14, "width"], [1083, 21, 958, 19], [1083, 23, 958, 21, "viewSize"], [1083, 31, 958, 29], [1083, 32, 958, 30, "width"], [1083, 37, 958, 35], [1083, 40, 958, 38], [1083, 43, 958, 41], [1084, 16, 959, 14, "height"], [1084, 22, 959, 20], [1084, 24, 959, 22, "viewSize"], [1084, 32, 959, 30], [1084, 33, 959, 31, "width"], [1084, 38, 959, 36], [1084, 41, 959, 39], [1084, 44, 959, 42], [1085, 16, 960, 14, "borderRadius"], [1085, 28, 960, 26], [1085, 30, 960, 29, "viewSize"], [1085, 38, 960, 37], [1085, 39, 960, 38, "width"], [1085, 44, 960, 43], [1085, 47, 960, 46], [1085, 50, 960, 49], [1085, 53, 960, 53], [1086, 14, 961, 12], [1086, 15, 961, 13], [1087, 12, 961, 15], [1088, 14, 961, 15, "fileName"], [1088, 22, 961, 15], [1088, 24, 961, 15, "_jsxFileName"], [1088, 36, 961, 15], [1089, 14, 961, 15, "lineNumber"], [1089, 24, 961, 15], [1090, 14, 961, 15, "columnNumber"], [1090, 26, 961, 15], [1091, 12, 961, 15], [1091, 19, 961, 17], [1091, 20, 961, 18], [1091, 22, 963, 13, "__DEV__"], [1091, 29, 963, 20], [1091, 46, 964, 14], [1091, 50, 964, 14, "_jsxDevRuntime"], [1091, 64, 964, 14], [1091, 65, 964, 14, "jsxDEV"], [1091, 71, 964, 14], [1091, 73, 964, 15, "_View"], [1091, 78, 964, 15], [1091, 79, 964, 15, "default"], [1091, 86, 964, 19], [1092, 14, 964, 20, "style"], [1092, 19, 964, 25], [1092, 21, 964, 27, "styles"], [1092, 27, 964, 33], [1092, 28, 964, 34, "previewChip"], [1092, 39, 964, 46], [1093, 14, 964, 46, "children"], [1093, 22, 964, 46], [1093, 37, 965, 16], [1093, 41, 965, 16, "_jsxDevRuntime"], [1093, 55, 965, 16], [1093, 56, 965, 16, "jsxDEV"], [1093, 62, 965, 16], [1093, 64, 965, 17, "_Text"], [1093, 69, 965, 17], [1093, 70, 965, 17, "default"], [1093, 77, 965, 21], [1094, 16, 965, 22, "style"], [1094, 21, 965, 27], [1094, 23, 965, 29, "styles"], [1094, 29, 965, 35], [1094, 30, 965, 36, "previewChipText"], [1094, 45, 965, 52], [1095, 16, 965, 52, "children"], [1095, 24, 965, 52], [1095, 26, 965, 53], [1096, 14, 965, 73], [1097, 16, 965, 73, "fileName"], [1097, 24, 965, 73], [1097, 26, 965, 73, "_jsxFileName"], [1097, 38, 965, 73], [1098, 16, 965, 73, "lineNumber"], [1098, 26, 965, 73], [1099, 16, 965, 73, "columnNumber"], [1099, 28, 965, 73], [1100, 14, 965, 73], [1100, 21, 965, 79], [1101, 12, 965, 80], [1102, 14, 965, 80, "fileName"], [1102, 22, 965, 80], [1102, 24, 965, 80, "_jsxFileName"], [1102, 36, 965, 80], [1103, 14, 965, 80, "lineNumber"], [1103, 24, 965, 80], [1104, 14, 965, 80, "columnNumber"], [1104, 26, 965, 80], [1105, 12, 965, 80], [1105, 19, 966, 20], [1105, 20, 967, 13], [1106, 10, 967, 13], [1107, 12, 967, 13, "fileName"], [1107, 20, 967, 13], [1107, 22, 967, 13, "_jsxFileName"], [1107, 34, 967, 13], [1108, 12, 967, 13, "lineNumber"], [1108, 22, 967, 13], [1109, 12, 967, 13, "columnNumber"], [1109, 24, 967, 13], [1110, 10, 967, 13], [1110, 17, 968, 18], [1110, 18, 968, 19], [1111, 8, 968, 19], [1111, 23, 969, 12], [1111, 24, 970, 9], [1111, 26, 972, 9, "isCameraReady"], [1111, 39, 972, 22], [1111, 56, 973, 10], [1111, 60, 973, 10, "_jsxDevRuntime"], [1111, 74, 973, 10], [1111, 75, 973, 10, "jsxDEV"], [1111, 81, 973, 10], [1111, 83, 973, 10, "_jsxDevRuntime"], [1111, 97, 973, 10], [1111, 98, 973, 10, "Fragment"], [1111, 106, 973, 10], [1112, 10, 973, 10, "children"], [1112, 18, 973, 10], [1112, 34, 975, 12], [1112, 38, 975, 12, "_jsxDevRuntime"], [1112, 52, 975, 12], [1112, 53, 975, 12, "jsxDEV"], [1112, 59, 975, 12], [1112, 61, 975, 13, "_View"], [1112, 66, 975, 13], [1112, 67, 975, 13, "default"], [1112, 74, 975, 17], [1113, 12, 975, 18, "style"], [1113, 17, 975, 23], [1113, 19, 975, 25, "styles"], [1113, 25, 975, 31], [1113, 26, 975, 32, "headerOverlay"], [1113, 39, 975, 46], [1114, 12, 975, 46, "children"], [1114, 20, 975, 46], [1114, 35, 976, 14], [1114, 39, 976, 14, "_jsxDevRuntime"], [1114, 53, 976, 14], [1114, 54, 976, 14, "jsxDEV"], [1114, 60, 976, 14], [1114, 62, 976, 15, "_View"], [1114, 67, 976, 15], [1114, 68, 976, 15, "default"], [1114, 75, 976, 19], [1115, 14, 976, 20, "style"], [1115, 19, 976, 25], [1115, 21, 976, 27, "styles"], [1115, 27, 976, 33], [1115, 28, 976, 34, "headerContent"], [1115, 41, 976, 48], [1116, 14, 976, 48, "children"], [1116, 22, 976, 48], [1116, 38, 977, 16], [1116, 42, 977, 16, "_jsxDevRuntime"], [1116, 56, 977, 16], [1116, 57, 977, 16, "jsxDEV"], [1116, 63, 977, 16], [1116, 65, 977, 17, "_View"], [1116, 70, 977, 17], [1116, 71, 977, 17, "default"], [1116, 78, 977, 21], [1117, 16, 977, 22, "style"], [1117, 21, 977, 27], [1117, 23, 977, 29, "styles"], [1117, 29, 977, 35], [1117, 30, 977, 36, "headerLeft"], [1117, 40, 977, 47], [1118, 16, 977, 47, "children"], [1118, 24, 977, 47], [1118, 40, 978, 18], [1118, 44, 978, 18, "_jsxDevRuntime"], [1118, 58, 978, 18], [1118, 59, 978, 18, "jsxDEV"], [1118, 65, 978, 18], [1118, 67, 978, 19, "_Text"], [1118, 72, 978, 19], [1118, 73, 978, 19, "default"], [1118, 80, 978, 23], [1119, 18, 978, 24, "style"], [1119, 23, 978, 29], [1119, 25, 978, 31, "styles"], [1119, 31, 978, 37], [1119, 32, 978, 38, "headerTitle"], [1119, 43, 978, 50], [1120, 18, 978, 50, "children"], [1120, 26, 978, 50], [1120, 28, 978, 51], [1121, 16, 978, 62], [1122, 18, 978, 62, "fileName"], [1122, 26, 978, 62], [1122, 28, 978, 62, "_jsxFileName"], [1122, 40, 978, 62], [1123, 18, 978, 62, "lineNumber"], [1123, 28, 978, 62], [1124, 18, 978, 62, "columnNumber"], [1124, 30, 978, 62], [1125, 16, 978, 62], [1125, 23, 978, 68], [1125, 24, 978, 69], [1125, 39, 979, 18], [1125, 43, 979, 18, "_jsxDevRuntime"], [1125, 57, 979, 18], [1125, 58, 979, 18, "jsxDEV"], [1125, 64, 979, 18], [1125, 66, 979, 19, "_View"], [1125, 71, 979, 19], [1125, 72, 979, 19, "default"], [1125, 79, 979, 23], [1126, 18, 979, 24, "style"], [1126, 23, 979, 29], [1126, 25, 979, 31, "styles"], [1126, 31, 979, 37], [1126, 32, 979, 38, "subtitleRow"], [1126, 43, 979, 50], [1127, 18, 979, 50, "children"], [1127, 26, 979, 50], [1127, 42, 980, 20], [1127, 46, 980, 20, "_jsxDevRuntime"], [1127, 60, 980, 20], [1127, 61, 980, 20, "jsxDEV"], [1127, 67, 980, 20], [1127, 69, 980, 21, "_Text"], [1127, 74, 980, 21], [1127, 75, 980, 21, "default"], [1127, 82, 980, 25], [1128, 20, 980, 26, "style"], [1128, 25, 980, 31], [1128, 27, 980, 33, "styles"], [1128, 33, 980, 39], [1128, 34, 980, 40, "webIcon"], [1128, 41, 980, 48], [1129, 20, 980, 48, "children"], [1129, 28, 980, 48], [1129, 30, 980, 49], [1130, 18, 980, 51], [1131, 20, 980, 51, "fileName"], [1131, 28, 980, 51], [1131, 30, 980, 51, "_jsxFileName"], [1131, 42, 980, 51], [1132, 20, 980, 51, "lineNumber"], [1132, 30, 980, 51], [1133, 20, 980, 51, "columnNumber"], [1133, 32, 980, 51], [1134, 18, 980, 51], [1134, 25, 980, 57], [1134, 26, 980, 58], [1134, 41, 981, 20], [1134, 45, 981, 20, "_jsxDevRuntime"], [1134, 59, 981, 20], [1134, 60, 981, 20, "jsxDEV"], [1134, 66, 981, 20], [1134, 68, 981, 21, "_Text"], [1134, 73, 981, 21], [1134, 74, 981, 21, "default"], [1134, 81, 981, 25], [1135, 20, 981, 26, "style"], [1135, 25, 981, 31], [1135, 27, 981, 33, "styles"], [1135, 33, 981, 39], [1135, 34, 981, 40, "headerSubtitle"], [1135, 48, 981, 55], [1136, 20, 981, 55, "children"], [1136, 28, 981, 55], [1136, 30, 981, 56], [1137, 18, 981, 71], [1138, 20, 981, 71, "fileName"], [1138, 28, 981, 71], [1138, 30, 981, 71, "_jsxFileName"], [1138, 42, 981, 71], [1139, 20, 981, 71, "lineNumber"], [1139, 30, 981, 71], [1140, 20, 981, 71, "columnNumber"], [1140, 32, 981, 71], [1141, 18, 981, 71], [1141, 25, 981, 77], [1141, 26, 981, 78], [1142, 16, 981, 78], [1143, 18, 981, 78, "fileName"], [1143, 26, 981, 78], [1143, 28, 981, 78, "_jsxFileName"], [1143, 40, 981, 78], [1144, 18, 981, 78, "lineNumber"], [1144, 28, 981, 78], [1145, 18, 981, 78, "columnNumber"], [1145, 30, 981, 78], [1146, 16, 981, 78], [1146, 23, 982, 24], [1146, 24, 982, 25], [1146, 26, 983, 19, "challengeCode"], [1146, 39, 983, 32], [1146, 56, 984, 20], [1146, 60, 984, 20, "_jsxDevRuntime"], [1146, 74, 984, 20], [1146, 75, 984, 20, "jsxDEV"], [1146, 81, 984, 20], [1146, 83, 984, 21, "_View"], [1146, 88, 984, 21], [1146, 89, 984, 21, "default"], [1146, 96, 984, 25], [1147, 18, 984, 26, "style"], [1147, 23, 984, 31], [1147, 25, 984, 33, "styles"], [1147, 31, 984, 39], [1147, 32, 984, 40, "challengeRow"], [1147, 44, 984, 53], [1148, 18, 984, 53, "children"], [1148, 26, 984, 53], [1148, 42, 985, 22], [1148, 46, 985, 22, "_jsxDevRuntime"], [1148, 60, 985, 22], [1148, 61, 985, 22, "jsxDEV"], [1148, 67, 985, 22], [1148, 69, 985, 23, "_lucideReactNative"], [1148, 87, 985, 23], [1148, 88, 985, 23, "Shield"], [1148, 94, 985, 29], [1149, 20, 985, 30, "size"], [1149, 24, 985, 34], [1149, 26, 985, 36], [1149, 28, 985, 39], [1150, 20, 985, 40, "color"], [1150, 25, 985, 45], [1150, 27, 985, 46], [1151, 18, 985, 52], [1152, 20, 985, 52, "fileName"], [1152, 28, 985, 52], [1152, 30, 985, 52, "_jsxFileName"], [1152, 42, 985, 52], [1153, 20, 985, 52, "lineNumber"], [1153, 30, 985, 52], [1154, 20, 985, 52, "columnNumber"], [1154, 32, 985, 52], [1155, 18, 985, 52], [1155, 25, 985, 54], [1155, 26, 985, 55], [1155, 41, 986, 22], [1155, 45, 986, 22, "_jsxDevRuntime"], [1155, 59, 986, 22], [1155, 60, 986, 22, "jsxDEV"], [1155, 66, 986, 22], [1155, 68, 986, 23, "_Text"], [1155, 73, 986, 23], [1155, 74, 986, 23, "default"], [1155, 81, 986, 27], [1156, 20, 986, 28, "style"], [1156, 25, 986, 33], [1156, 27, 986, 35, "styles"], [1156, 33, 986, 41], [1156, 34, 986, 42, "challengeCode"], [1156, 47, 986, 56], [1157, 20, 986, 56, "children"], [1157, 28, 986, 56], [1157, 30, 986, 58, "challengeCode"], [1158, 18, 986, 71], [1159, 20, 986, 71, "fileName"], [1159, 28, 986, 71], [1159, 30, 986, 71, "_jsxFileName"], [1159, 42, 986, 71], [1160, 20, 986, 71, "lineNumber"], [1160, 30, 986, 71], [1161, 20, 986, 71, "columnNumber"], [1161, 32, 986, 71], [1162, 18, 986, 71], [1162, 25, 986, 78], [1162, 26, 986, 79], [1163, 16, 986, 79], [1164, 18, 986, 79, "fileName"], [1164, 26, 986, 79], [1164, 28, 986, 79, "_jsxFileName"], [1164, 40, 986, 79], [1165, 18, 986, 79, "lineNumber"], [1165, 28, 986, 79], [1166, 18, 986, 79, "columnNumber"], [1166, 30, 986, 79], [1167, 16, 986, 79], [1167, 23, 987, 26], [1167, 24, 988, 19], [1168, 14, 988, 19], [1169, 16, 988, 19, "fileName"], [1169, 24, 988, 19], [1169, 26, 988, 19, "_jsxFileName"], [1169, 38, 988, 19], [1170, 16, 988, 19, "lineNumber"], [1170, 26, 988, 19], [1171, 16, 988, 19, "columnNumber"], [1171, 28, 988, 19], [1172, 14, 988, 19], [1172, 21, 989, 22], [1172, 22, 989, 23], [1172, 37, 990, 16], [1172, 41, 990, 16, "_jsxDevRuntime"], [1172, 55, 990, 16], [1172, 56, 990, 16, "jsxDEV"], [1172, 62, 990, 16], [1172, 64, 990, 17, "_TouchableOpacity"], [1172, 81, 990, 17], [1172, 82, 990, 17, "default"], [1172, 89, 990, 33], [1173, 16, 990, 34, "onPress"], [1173, 23, 990, 41], [1173, 25, 990, 43, "onCancel"], [1173, 33, 990, 52], [1174, 16, 990, 53, "style"], [1174, 21, 990, 58], [1174, 23, 990, 60, "styles"], [1174, 29, 990, 66], [1174, 30, 990, 67, "closeButton"], [1174, 41, 990, 79], [1175, 16, 990, 79, "children"], [1175, 24, 990, 79], [1175, 39, 991, 18], [1175, 43, 991, 18, "_jsxDevRuntime"], [1175, 57, 991, 18], [1175, 58, 991, 18, "jsxDEV"], [1175, 64, 991, 18], [1175, 66, 991, 19, "_lucideReactNative"], [1175, 84, 991, 19], [1175, 85, 991, 19, "X"], [1175, 86, 991, 20], [1176, 18, 991, 21, "size"], [1176, 22, 991, 25], [1176, 24, 991, 27], [1176, 26, 991, 30], [1177, 18, 991, 31, "color"], [1177, 23, 991, 36], [1177, 25, 991, 37], [1178, 16, 991, 43], [1179, 18, 991, 43, "fileName"], [1179, 26, 991, 43], [1179, 28, 991, 43, "_jsxFileName"], [1179, 40, 991, 43], [1180, 18, 991, 43, "lineNumber"], [1180, 28, 991, 43], [1181, 18, 991, 43, "columnNumber"], [1181, 30, 991, 43], [1182, 16, 991, 43], [1182, 23, 991, 45], [1183, 14, 991, 46], [1184, 16, 991, 46, "fileName"], [1184, 24, 991, 46], [1184, 26, 991, 46, "_jsxFileName"], [1184, 38, 991, 46], [1185, 16, 991, 46, "lineNumber"], [1185, 26, 991, 46], [1186, 16, 991, 46, "columnNumber"], [1186, 28, 991, 46], [1187, 14, 991, 46], [1187, 21, 992, 34], [1187, 22, 992, 35], [1188, 12, 992, 35], [1189, 14, 992, 35, "fileName"], [1189, 22, 992, 35], [1189, 24, 992, 35, "_jsxFileName"], [1189, 36, 992, 35], [1190, 14, 992, 35, "lineNumber"], [1190, 24, 992, 35], [1191, 14, 992, 35, "columnNumber"], [1191, 26, 992, 35], [1192, 12, 992, 35], [1192, 19, 993, 20], [1193, 10, 993, 21], [1194, 12, 993, 21, "fileName"], [1194, 20, 993, 21], [1194, 22, 993, 21, "_jsxFileName"], [1194, 34, 993, 21], [1195, 12, 993, 21, "lineNumber"], [1195, 22, 993, 21], [1196, 12, 993, 21, "columnNumber"], [1196, 24, 993, 21], [1197, 10, 993, 21], [1197, 17, 994, 18], [1197, 18, 994, 19], [1197, 33, 996, 12], [1197, 37, 996, 12, "_jsxDevRuntime"], [1197, 51, 996, 12], [1197, 52, 996, 12, "jsxDEV"], [1197, 58, 996, 12], [1197, 60, 996, 13, "_View"], [1197, 65, 996, 13], [1197, 66, 996, 13, "default"], [1197, 73, 996, 17], [1198, 12, 996, 18, "style"], [1198, 17, 996, 23], [1198, 19, 996, 25, "styles"], [1198, 25, 996, 31], [1198, 26, 996, 32, "privacyNotice"], [1198, 39, 996, 46], [1199, 12, 996, 46, "children"], [1199, 20, 996, 46], [1199, 36, 997, 14], [1199, 40, 997, 14, "_jsxDevRuntime"], [1199, 54, 997, 14], [1199, 55, 997, 14, "jsxDEV"], [1199, 61, 997, 14], [1199, 63, 997, 15, "_lucideReactNative"], [1199, 81, 997, 15], [1199, 82, 997, 15, "Shield"], [1199, 88, 997, 21], [1200, 14, 997, 22, "size"], [1200, 18, 997, 26], [1200, 20, 997, 28], [1200, 22, 997, 31], [1201, 14, 997, 32, "color"], [1201, 19, 997, 37], [1201, 21, 997, 38], [1202, 12, 997, 47], [1203, 14, 997, 47, "fileName"], [1203, 22, 997, 47], [1203, 24, 997, 47, "_jsxFileName"], [1203, 36, 997, 47], [1204, 14, 997, 47, "lineNumber"], [1204, 24, 997, 47], [1205, 14, 997, 47, "columnNumber"], [1205, 26, 997, 47], [1206, 12, 997, 47], [1206, 19, 997, 49], [1206, 20, 997, 50], [1206, 35, 998, 14], [1206, 39, 998, 14, "_jsxDevRuntime"], [1206, 53, 998, 14], [1206, 54, 998, 14, "jsxDEV"], [1206, 60, 998, 14], [1206, 62, 998, 15, "_Text"], [1206, 67, 998, 15], [1206, 68, 998, 15, "default"], [1206, 75, 998, 19], [1207, 14, 998, 20, "style"], [1207, 19, 998, 25], [1207, 21, 998, 27, "styles"], [1207, 27, 998, 33], [1207, 28, 998, 34, "privacyText"], [1207, 39, 998, 46], [1208, 14, 998, 46, "children"], [1208, 22, 998, 46], [1208, 24, 998, 47], [1209, 12, 1000, 14], [1210, 14, 1000, 14, "fileName"], [1210, 22, 1000, 14], [1210, 24, 1000, 14, "_jsxFileName"], [1210, 36, 1000, 14], [1211, 14, 1000, 14, "lineNumber"], [1211, 24, 1000, 14], [1212, 14, 1000, 14, "columnNumber"], [1212, 26, 1000, 14], [1213, 12, 1000, 14], [1213, 19, 1000, 20], [1213, 20, 1000, 21], [1214, 10, 1000, 21], [1215, 12, 1000, 21, "fileName"], [1215, 20, 1000, 21], [1215, 22, 1000, 21, "_jsxFileName"], [1215, 34, 1000, 21], [1216, 12, 1000, 21, "lineNumber"], [1216, 22, 1000, 21], [1217, 12, 1000, 21, "columnNumber"], [1217, 24, 1000, 21], [1218, 10, 1000, 21], [1218, 17, 1001, 18], [1218, 18, 1001, 19], [1218, 33, 1003, 12], [1218, 37, 1003, 12, "_jsxDevRuntime"], [1218, 51, 1003, 12], [1218, 52, 1003, 12, "jsxDEV"], [1218, 58, 1003, 12], [1218, 60, 1003, 13, "_View"], [1218, 65, 1003, 13], [1218, 66, 1003, 13, "default"], [1218, 73, 1003, 17], [1219, 12, 1003, 18, "style"], [1219, 17, 1003, 23], [1219, 19, 1003, 25, "styles"], [1219, 25, 1003, 31], [1219, 26, 1003, 32, "footer<PERSON><PERSON><PERSON>"], [1219, 39, 1003, 46], [1220, 12, 1003, 46, "children"], [1220, 20, 1003, 46], [1220, 36, 1004, 14], [1220, 40, 1004, 14, "_jsxDevRuntime"], [1220, 54, 1004, 14], [1220, 55, 1004, 14, "jsxDEV"], [1220, 61, 1004, 14], [1220, 63, 1004, 15, "_Text"], [1220, 68, 1004, 15], [1220, 69, 1004, 15, "default"], [1220, 76, 1004, 19], [1221, 14, 1004, 20, "style"], [1221, 19, 1004, 25], [1221, 21, 1004, 27, "styles"], [1221, 27, 1004, 33], [1221, 28, 1004, 34, "instruction"], [1221, 39, 1004, 46], [1222, 14, 1004, 46, "children"], [1222, 22, 1004, 46], [1222, 24, 1004, 47], [1223, 12, 1006, 14], [1224, 14, 1006, 14, "fileName"], [1224, 22, 1006, 14], [1224, 24, 1006, 14, "_jsxFileName"], [1224, 36, 1006, 14], [1225, 14, 1006, 14, "lineNumber"], [1225, 24, 1006, 14], [1226, 14, 1006, 14, "columnNumber"], [1226, 26, 1006, 14], [1227, 12, 1006, 14], [1227, 19, 1006, 20], [1227, 20, 1006, 21], [1227, 35, 1008, 14], [1227, 39, 1008, 14, "_jsxDevRuntime"], [1227, 53, 1008, 14], [1227, 54, 1008, 14, "jsxDEV"], [1227, 60, 1008, 14], [1227, 62, 1008, 15, "_TouchableOpacity"], [1227, 79, 1008, 15], [1227, 80, 1008, 15, "default"], [1227, 87, 1008, 31], [1228, 14, 1009, 16, "onPress"], [1228, 21, 1009, 23], [1228, 23, 1009, 25, "capturePhoto"], [1228, 35, 1009, 38], [1229, 14, 1010, 16, "disabled"], [1229, 22, 1010, 24], [1229, 24, 1010, 26, "processingState"], [1229, 39, 1010, 41], [1229, 44, 1010, 46], [1229, 50, 1010, 52], [1229, 54, 1010, 56], [1229, 55, 1010, 57, "isCameraReady"], [1229, 68, 1010, 71], [1230, 14, 1011, 16, "style"], [1230, 19, 1011, 21], [1230, 21, 1011, 23], [1230, 22, 1012, 18, "styles"], [1230, 28, 1012, 24], [1230, 29, 1012, 25, "shutterButton"], [1230, 42, 1012, 38], [1230, 44, 1013, 18, "processingState"], [1230, 59, 1013, 33], [1230, 64, 1013, 38], [1230, 70, 1013, 44], [1230, 74, 1013, 48, "styles"], [1230, 80, 1013, 54], [1230, 81, 1013, 55, "shutterButtonDisabled"], [1230, 102, 1013, 76], [1230, 103, 1014, 18], [1231, 14, 1014, 18, "children"], [1231, 22, 1014, 18], [1231, 24, 1016, 17, "processingState"], [1231, 39, 1016, 32], [1231, 44, 1016, 37], [1231, 50, 1016, 43], [1231, 66, 1017, 18], [1231, 70, 1017, 18, "_jsxDevRuntime"], [1231, 84, 1017, 18], [1231, 85, 1017, 18, "jsxDEV"], [1231, 91, 1017, 18], [1231, 93, 1017, 19, "_View"], [1231, 98, 1017, 19], [1231, 99, 1017, 19, "default"], [1231, 106, 1017, 23], [1232, 16, 1017, 24, "style"], [1232, 21, 1017, 29], [1232, 23, 1017, 31, "styles"], [1232, 29, 1017, 37], [1232, 30, 1017, 38, "shutterInner"], [1233, 14, 1017, 51], [1234, 16, 1017, 51, "fileName"], [1234, 24, 1017, 51], [1234, 26, 1017, 51, "_jsxFileName"], [1234, 38, 1017, 51], [1235, 16, 1017, 51, "lineNumber"], [1235, 26, 1017, 51], [1236, 16, 1017, 51, "columnNumber"], [1236, 28, 1017, 51], [1237, 14, 1017, 51], [1237, 21, 1017, 53], [1237, 22, 1017, 54], [1237, 38, 1019, 18], [1237, 42, 1019, 18, "_jsxDevRuntime"], [1237, 56, 1019, 18], [1237, 57, 1019, 18, "jsxDEV"], [1237, 63, 1019, 18], [1237, 65, 1019, 19, "_ActivityIndicator"], [1237, 83, 1019, 19], [1237, 84, 1019, 19, "default"], [1237, 91, 1019, 36], [1238, 16, 1019, 37, "size"], [1238, 20, 1019, 41], [1238, 22, 1019, 42], [1238, 29, 1019, 49], [1239, 16, 1019, 50, "color"], [1239, 21, 1019, 55], [1239, 23, 1019, 56], [1240, 14, 1019, 65], [1241, 16, 1019, 65, "fileName"], [1241, 24, 1019, 65], [1241, 26, 1019, 65, "_jsxFileName"], [1241, 38, 1019, 65], [1242, 16, 1019, 65, "lineNumber"], [1242, 26, 1019, 65], [1243, 16, 1019, 65, "columnNumber"], [1243, 28, 1019, 65], [1244, 14, 1019, 65], [1244, 21, 1019, 67], [1245, 12, 1020, 17], [1246, 14, 1020, 17, "fileName"], [1246, 22, 1020, 17], [1246, 24, 1020, 17, "_jsxFileName"], [1246, 36, 1020, 17], [1247, 14, 1020, 17, "lineNumber"], [1247, 24, 1020, 17], [1248, 14, 1020, 17, "columnNumber"], [1248, 26, 1020, 17], [1249, 12, 1020, 17], [1249, 19, 1021, 32], [1249, 20, 1021, 33], [1249, 35, 1022, 14], [1249, 39, 1022, 14, "_jsxDevRuntime"], [1249, 53, 1022, 14], [1249, 54, 1022, 14, "jsxDEV"], [1249, 60, 1022, 14], [1249, 62, 1022, 15, "_Text"], [1249, 67, 1022, 15], [1249, 68, 1022, 15, "default"], [1249, 75, 1022, 19], [1250, 14, 1022, 20, "style"], [1250, 19, 1022, 25], [1250, 21, 1022, 27, "styles"], [1250, 27, 1022, 33], [1250, 28, 1022, 34, "privacyNote"], [1250, 39, 1022, 46], [1251, 14, 1022, 46, "children"], [1251, 22, 1022, 46], [1251, 24, 1022, 47], [1252, 12, 1024, 14], [1253, 14, 1024, 14, "fileName"], [1253, 22, 1024, 14], [1253, 24, 1024, 14, "_jsxFileName"], [1253, 36, 1024, 14], [1254, 14, 1024, 14, "lineNumber"], [1254, 24, 1024, 14], [1255, 14, 1024, 14, "columnNumber"], [1255, 26, 1024, 14], [1256, 12, 1024, 14], [1256, 19, 1024, 20], [1256, 20, 1024, 21], [1257, 10, 1024, 21], [1258, 12, 1024, 21, "fileName"], [1258, 20, 1024, 21], [1258, 22, 1024, 21, "_jsxFileName"], [1258, 34, 1024, 21], [1259, 12, 1024, 21, "lineNumber"], [1259, 22, 1024, 21], [1260, 12, 1024, 21, "columnNumber"], [1260, 24, 1024, 21], [1261, 10, 1024, 21], [1261, 17, 1025, 18], [1261, 18, 1025, 19], [1262, 8, 1025, 19], [1262, 23, 1026, 12], [1262, 24, 1027, 9], [1263, 6, 1027, 9], [1264, 8, 1027, 9, "fileName"], [1264, 16, 1027, 9], [1264, 18, 1027, 9, "_jsxFileName"], [1264, 30, 1027, 9], [1265, 8, 1027, 9, "lineNumber"], [1265, 18, 1027, 9], [1266, 8, 1027, 9, "columnNumber"], [1266, 20, 1027, 9], [1267, 6, 1027, 9], [1267, 13, 1028, 12], [1267, 14, 1028, 13], [1267, 29, 1030, 6], [1267, 33, 1030, 6, "_jsxDevRuntime"], [1267, 47, 1030, 6], [1267, 48, 1030, 6, "jsxDEV"], [1267, 54, 1030, 6], [1267, 56, 1030, 7, "_Modal"], [1267, 62, 1030, 7], [1267, 63, 1030, 7, "default"], [1267, 70, 1030, 12], [1268, 8, 1031, 8, "visible"], [1268, 15, 1031, 15], [1268, 17, 1031, 17, "processingState"], [1268, 32, 1031, 32], [1268, 37, 1031, 37], [1268, 43, 1031, 43], [1268, 47, 1031, 47, "processingState"], [1268, 62, 1031, 62], [1268, 67, 1031, 67], [1268, 74, 1031, 75], [1269, 8, 1032, 8, "transparent"], [1269, 19, 1032, 19], [1270, 8, 1033, 8, "animationType"], [1270, 21, 1033, 21], [1270, 23, 1033, 22], [1270, 29, 1033, 28], [1271, 8, 1033, 28, "children"], [1271, 16, 1033, 28], [1271, 31, 1035, 8], [1271, 35, 1035, 8, "_jsxDevRuntime"], [1271, 49, 1035, 8], [1271, 50, 1035, 8, "jsxDEV"], [1271, 56, 1035, 8], [1271, 58, 1035, 9, "_View"], [1271, 63, 1035, 9], [1271, 64, 1035, 9, "default"], [1271, 71, 1035, 13], [1272, 10, 1035, 14, "style"], [1272, 15, 1035, 19], [1272, 17, 1035, 21, "styles"], [1272, 23, 1035, 27], [1272, 24, 1035, 28, "processingModal"], [1272, 39, 1035, 44], [1273, 10, 1035, 44, "children"], [1273, 18, 1035, 44], [1273, 33, 1036, 10], [1273, 37, 1036, 10, "_jsxDevRuntime"], [1273, 51, 1036, 10], [1273, 52, 1036, 10, "jsxDEV"], [1273, 58, 1036, 10], [1273, 60, 1036, 11, "_View"], [1273, 65, 1036, 11], [1273, 66, 1036, 11, "default"], [1273, 73, 1036, 15], [1274, 12, 1036, 16, "style"], [1274, 17, 1036, 21], [1274, 19, 1036, 23, "styles"], [1274, 25, 1036, 29], [1274, 26, 1036, 30, "processingContent"], [1274, 43, 1036, 48], [1275, 12, 1036, 48, "children"], [1275, 20, 1036, 48], [1275, 36, 1037, 12], [1275, 40, 1037, 12, "_jsxDevRuntime"], [1275, 54, 1037, 12], [1275, 55, 1037, 12, "jsxDEV"], [1275, 61, 1037, 12], [1275, 63, 1037, 13, "_ActivityIndicator"], [1275, 81, 1037, 13], [1275, 82, 1037, 13, "default"], [1275, 89, 1037, 30], [1276, 14, 1037, 31, "size"], [1276, 18, 1037, 35], [1276, 20, 1037, 36], [1276, 27, 1037, 43], [1277, 14, 1037, 44, "color"], [1277, 19, 1037, 49], [1277, 21, 1037, 50], [1278, 12, 1037, 59], [1279, 14, 1037, 59, "fileName"], [1279, 22, 1037, 59], [1279, 24, 1037, 59, "_jsxFileName"], [1279, 36, 1037, 59], [1280, 14, 1037, 59, "lineNumber"], [1280, 24, 1037, 59], [1281, 14, 1037, 59, "columnNumber"], [1281, 26, 1037, 59], [1282, 12, 1037, 59], [1282, 19, 1037, 61], [1282, 20, 1037, 62], [1282, 35, 1039, 12], [1282, 39, 1039, 12, "_jsxDevRuntime"], [1282, 53, 1039, 12], [1282, 54, 1039, 12, "jsxDEV"], [1282, 60, 1039, 12], [1282, 62, 1039, 13, "_Text"], [1282, 67, 1039, 13], [1282, 68, 1039, 13, "default"], [1282, 75, 1039, 17], [1283, 14, 1039, 18, "style"], [1283, 19, 1039, 23], [1283, 21, 1039, 25, "styles"], [1283, 27, 1039, 31], [1283, 28, 1039, 32, "processingTitle"], [1283, 43, 1039, 48], [1284, 14, 1039, 48, "children"], [1284, 22, 1039, 48], [1284, 25, 1040, 15, "processingState"], [1284, 40, 1040, 30], [1284, 45, 1040, 35], [1284, 56, 1040, 46], [1284, 60, 1040, 50], [1284, 80, 1040, 70], [1284, 82, 1041, 15, "processingState"], [1284, 97, 1041, 30], [1284, 102, 1041, 35], [1284, 113, 1041, 46], [1284, 117, 1041, 50], [1284, 146, 1041, 79], [1284, 148, 1042, 15, "processingState"], [1284, 163, 1042, 30], [1284, 168, 1042, 35], [1284, 180, 1042, 47], [1284, 184, 1042, 51], [1284, 216, 1042, 83], [1284, 218, 1043, 15, "processingState"], [1284, 233, 1043, 30], [1284, 238, 1043, 35], [1284, 249, 1043, 46], [1284, 253, 1043, 50], [1284, 275, 1043, 72], [1285, 12, 1043, 72], [1286, 14, 1043, 72, "fileName"], [1286, 22, 1043, 72], [1286, 24, 1043, 72, "_jsxFileName"], [1286, 36, 1043, 72], [1287, 14, 1043, 72, "lineNumber"], [1287, 24, 1043, 72], [1288, 14, 1043, 72, "columnNumber"], [1288, 26, 1043, 72], [1289, 12, 1043, 72], [1289, 19, 1044, 18], [1289, 20, 1044, 19], [1289, 35, 1045, 12], [1289, 39, 1045, 12, "_jsxDevRuntime"], [1289, 53, 1045, 12], [1289, 54, 1045, 12, "jsxDEV"], [1289, 60, 1045, 12], [1289, 62, 1045, 13, "_View"], [1289, 67, 1045, 13], [1289, 68, 1045, 13, "default"], [1289, 75, 1045, 17], [1290, 14, 1045, 18, "style"], [1290, 19, 1045, 23], [1290, 21, 1045, 25, "styles"], [1290, 27, 1045, 31], [1290, 28, 1045, 32, "progressBar"], [1290, 39, 1045, 44], [1291, 14, 1045, 44, "children"], [1291, 22, 1045, 44], [1291, 37, 1046, 14], [1291, 41, 1046, 14, "_jsxDevRuntime"], [1291, 55, 1046, 14], [1291, 56, 1046, 14, "jsxDEV"], [1291, 62, 1046, 14], [1291, 64, 1046, 15, "_View"], [1291, 69, 1046, 15], [1291, 70, 1046, 15, "default"], [1291, 77, 1046, 19], [1292, 16, 1047, 16, "style"], [1292, 21, 1047, 21], [1292, 23, 1047, 23], [1292, 24, 1048, 18, "styles"], [1292, 30, 1048, 24], [1292, 31, 1048, 25, "progressFill"], [1292, 43, 1048, 37], [1292, 45, 1049, 18], [1293, 18, 1049, 20, "width"], [1293, 23, 1049, 25], [1293, 25, 1049, 27], [1293, 28, 1049, 30, "processingProgress"], [1293, 46, 1049, 48], [1294, 16, 1049, 52], [1294, 17, 1049, 53], [1295, 14, 1050, 18], [1296, 16, 1050, 18, "fileName"], [1296, 24, 1050, 18], [1296, 26, 1050, 18, "_jsxFileName"], [1296, 38, 1050, 18], [1297, 16, 1050, 18, "lineNumber"], [1297, 26, 1050, 18], [1298, 16, 1050, 18, "columnNumber"], [1298, 28, 1050, 18], [1299, 14, 1050, 18], [1299, 21, 1051, 15], [1300, 12, 1051, 16], [1301, 14, 1051, 16, "fileName"], [1301, 22, 1051, 16], [1301, 24, 1051, 16, "_jsxFileName"], [1301, 36, 1051, 16], [1302, 14, 1051, 16, "lineNumber"], [1302, 24, 1051, 16], [1303, 14, 1051, 16, "columnNumber"], [1303, 26, 1051, 16], [1304, 12, 1051, 16], [1304, 19, 1052, 18], [1304, 20, 1052, 19], [1304, 35, 1053, 12], [1304, 39, 1053, 12, "_jsxDevRuntime"], [1304, 53, 1053, 12], [1304, 54, 1053, 12, "jsxDEV"], [1304, 60, 1053, 12], [1304, 62, 1053, 13, "_Text"], [1304, 67, 1053, 13], [1304, 68, 1053, 13, "default"], [1304, 75, 1053, 17], [1305, 14, 1053, 18, "style"], [1305, 19, 1053, 23], [1305, 21, 1053, 25, "styles"], [1305, 27, 1053, 31], [1305, 28, 1053, 32, "processingDescription"], [1305, 49, 1053, 54], [1306, 14, 1053, 54, "children"], [1306, 22, 1053, 54], [1306, 25, 1054, 15, "processingState"], [1306, 40, 1054, 30], [1306, 45, 1054, 35], [1306, 56, 1054, 46], [1306, 60, 1054, 50], [1306, 89, 1054, 79], [1306, 91, 1055, 15, "processingState"], [1306, 106, 1055, 30], [1306, 111, 1055, 35], [1306, 122, 1055, 46], [1306, 126, 1055, 50], [1306, 164, 1055, 88], [1306, 166, 1056, 15, "processingState"], [1306, 181, 1056, 30], [1306, 186, 1056, 35], [1306, 198, 1056, 47], [1306, 202, 1056, 51], [1306, 247, 1056, 96], [1306, 249, 1057, 15, "processingState"], [1306, 264, 1057, 30], [1306, 269, 1057, 35], [1306, 280, 1057, 46], [1306, 284, 1057, 50], [1306, 325, 1057, 91], [1307, 12, 1057, 91], [1308, 14, 1057, 91, "fileName"], [1308, 22, 1057, 91], [1308, 24, 1057, 91, "_jsxFileName"], [1308, 36, 1057, 91], [1309, 14, 1057, 91, "lineNumber"], [1309, 24, 1057, 91], [1310, 14, 1057, 91, "columnNumber"], [1310, 26, 1057, 91], [1311, 12, 1057, 91], [1311, 19, 1058, 18], [1311, 20, 1058, 19], [1311, 22, 1059, 13, "processingState"], [1311, 37, 1059, 28], [1311, 42, 1059, 33], [1311, 53, 1059, 44], [1311, 70, 1060, 14], [1311, 74, 1060, 14, "_jsxDevRuntime"], [1311, 88, 1060, 14], [1311, 89, 1060, 14, "jsxDEV"], [1311, 95, 1060, 14], [1311, 97, 1060, 15, "_lucideReactNative"], [1311, 115, 1060, 15], [1311, 116, 1060, 15, "CheckCircle"], [1311, 127, 1060, 26], [1312, 14, 1060, 27, "size"], [1312, 18, 1060, 31], [1312, 20, 1060, 33], [1312, 22, 1060, 36], [1313, 14, 1060, 37, "color"], [1313, 19, 1060, 42], [1313, 21, 1060, 43], [1313, 30, 1060, 52], [1314, 14, 1060, 53, "style"], [1314, 19, 1060, 58], [1314, 21, 1060, 60, "styles"], [1314, 27, 1060, 66], [1314, 28, 1060, 67, "successIcon"], [1315, 12, 1060, 79], [1316, 14, 1060, 79, "fileName"], [1316, 22, 1060, 79], [1316, 24, 1060, 79, "_jsxFileName"], [1316, 36, 1060, 79], [1317, 14, 1060, 79, "lineNumber"], [1317, 24, 1060, 79], [1318, 14, 1060, 79, "columnNumber"], [1318, 26, 1060, 79], [1319, 12, 1060, 79], [1319, 19, 1060, 81], [1319, 20, 1061, 13], [1320, 10, 1061, 13], [1321, 12, 1061, 13, "fileName"], [1321, 20, 1061, 13], [1321, 22, 1061, 13, "_jsxFileName"], [1321, 34, 1061, 13], [1322, 12, 1061, 13, "lineNumber"], [1322, 22, 1061, 13], [1323, 12, 1061, 13, "columnNumber"], [1323, 24, 1061, 13], [1324, 10, 1061, 13], [1324, 17, 1062, 16], [1325, 8, 1062, 17], [1326, 10, 1062, 17, "fileName"], [1326, 18, 1062, 17], [1326, 20, 1062, 17, "_jsxFileName"], [1326, 32, 1062, 17], [1327, 10, 1062, 17, "lineNumber"], [1327, 20, 1062, 17], [1328, 10, 1062, 17, "columnNumber"], [1328, 22, 1062, 17], [1329, 8, 1062, 17], [1329, 15, 1063, 14], [1330, 6, 1063, 15], [1331, 8, 1063, 15, "fileName"], [1331, 16, 1063, 15], [1331, 18, 1063, 15, "_jsxFileName"], [1331, 30, 1063, 15], [1332, 8, 1063, 15, "lineNumber"], [1332, 18, 1063, 15], [1333, 8, 1063, 15, "columnNumber"], [1333, 20, 1063, 15], [1334, 6, 1063, 15], [1334, 13, 1064, 13], [1334, 14, 1064, 14], [1334, 29, 1066, 6], [1334, 33, 1066, 6, "_jsxDevRuntime"], [1334, 47, 1066, 6], [1334, 48, 1066, 6, "jsxDEV"], [1334, 54, 1066, 6], [1334, 56, 1066, 7, "_Modal"], [1334, 62, 1066, 7], [1334, 63, 1066, 7, "default"], [1334, 70, 1066, 12], [1335, 8, 1067, 8, "visible"], [1335, 15, 1067, 15], [1335, 17, 1067, 17, "processingState"], [1335, 32, 1067, 32], [1335, 37, 1067, 37], [1335, 44, 1067, 45], [1336, 8, 1068, 8, "transparent"], [1336, 19, 1068, 19], [1337, 8, 1069, 8, "animationType"], [1337, 21, 1069, 21], [1337, 23, 1069, 22], [1337, 29, 1069, 28], [1338, 8, 1069, 28, "children"], [1338, 16, 1069, 28], [1338, 31, 1071, 8], [1338, 35, 1071, 8, "_jsxDevRuntime"], [1338, 49, 1071, 8], [1338, 50, 1071, 8, "jsxDEV"], [1338, 56, 1071, 8], [1338, 58, 1071, 9, "_View"], [1338, 63, 1071, 9], [1338, 64, 1071, 9, "default"], [1338, 71, 1071, 13], [1339, 10, 1071, 14, "style"], [1339, 15, 1071, 19], [1339, 17, 1071, 21, "styles"], [1339, 23, 1071, 27], [1339, 24, 1071, 28, "processingModal"], [1339, 39, 1071, 44], [1340, 10, 1071, 44, "children"], [1340, 18, 1071, 44], [1340, 33, 1072, 10], [1340, 37, 1072, 10, "_jsxDevRuntime"], [1340, 51, 1072, 10], [1340, 52, 1072, 10, "jsxDEV"], [1340, 58, 1072, 10], [1340, 60, 1072, 11, "_View"], [1340, 65, 1072, 11], [1340, 66, 1072, 11, "default"], [1340, 73, 1072, 15], [1341, 12, 1072, 16, "style"], [1341, 17, 1072, 21], [1341, 19, 1072, 23, "styles"], [1341, 25, 1072, 29], [1341, 26, 1072, 30, "errorContent"], [1341, 38, 1072, 43], [1342, 12, 1072, 43, "children"], [1342, 20, 1072, 43], [1342, 36, 1073, 12], [1342, 40, 1073, 12, "_jsxDevRuntime"], [1342, 54, 1073, 12], [1342, 55, 1073, 12, "jsxDEV"], [1342, 61, 1073, 12], [1342, 63, 1073, 13, "_lucideReactNative"], [1342, 81, 1073, 13], [1342, 82, 1073, 13, "X"], [1342, 83, 1073, 14], [1343, 14, 1073, 15, "size"], [1343, 18, 1073, 19], [1343, 20, 1073, 21], [1343, 22, 1073, 24], [1344, 14, 1073, 25, "color"], [1344, 19, 1073, 30], [1344, 21, 1073, 31], [1345, 12, 1073, 40], [1346, 14, 1073, 40, "fileName"], [1346, 22, 1073, 40], [1346, 24, 1073, 40, "_jsxFileName"], [1346, 36, 1073, 40], [1347, 14, 1073, 40, "lineNumber"], [1347, 24, 1073, 40], [1348, 14, 1073, 40, "columnNumber"], [1348, 26, 1073, 40], [1349, 12, 1073, 40], [1349, 19, 1073, 42], [1349, 20, 1073, 43], [1349, 35, 1074, 12], [1349, 39, 1074, 12, "_jsxDevRuntime"], [1349, 53, 1074, 12], [1349, 54, 1074, 12, "jsxDEV"], [1349, 60, 1074, 12], [1349, 62, 1074, 13, "_Text"], [1349, 67, 1074, 13], [1349, 68, 1074, 13, "default"], [1349, 75, 1074, 17], [1350, 14, 1074, 18, "style"], [1350, 19, 1074, 23], [1350, 21, 1074, 25, "styles"], [1350, 27, 1074, 31], [1350, 28, 1074, 32, "errorTitle"], [1350, 38, 1074, 43], [1351, 14, 1074, 43, "children"], [1351, 22, 1074, 43], [1351, 24, 1074, 44], [1352, 12, 1074, 61], [1353, 14, 1074, 61, "fileName"], [1353, 22, 1074, 61], [1353, 24, 1074, 61, "_jsxFileName"], [1353, 36, 1074, 61], [1354, 14, 1074, 61, "lineNumber"], [1354, 24, 1074, 61], [1355, 14, 1074, 61, "columnNumber"], [1355, 26, 1074, 61], [1356, 12, 1074, 61], [1356, 19, 1074, 67], [1356, 20, 1074, 68], [1356, 35, 1075, 12], [1356, 39, 1075, 12, "_jsxDevRuntime"], [1356, 53, 1075, 12], [1356, 54, 1075, 12, "jsxDEV"], [1356, 60, 1075, 12], [1356, 62, 1075, 13, "_Text"], [1356, 67, 1075, 13], [1356, 68, 1075, 13, "default"], [1356, 75, 1075, 17], [1357, 14, 1075, 18, "style"], [1357, 19, 1075, 23], [1357, 21, 1075, 25, "styles"], [1357, 27, 1075, 31], [1357, 28, 1075, 32, "errorMessage"], [1357, 40, 1075, 45], [1358, 14, 1075, 45, "children"], [1358, 22, 1075, 45], [1358, 24, 1075, 47, "errorMessage"], [1359, 12, 1075, 59], [1360, 14, 1075, 59, "fileName"], [1360, 22, 1075, 59], [1360, 24, 1075, 59, "_jsxFileName"], [1360, 36, 1075, 59], [1361, 14, 1075, 59, "lineNumber"], [1361, 24, 1075, 59], [1362, 14, 1075, 59, "columnNumber"], [1362, 26, 1075, 59], [1363, 12, 1075, 59], [1363, 19, 1075, 66], [1363, 20, 1075, 67], [1363, 35, 1076, 12], [1363, 39, 1076, 12, "_jsxDevRuntime"], [1363, 53, 1076, 12], [1363, 54, 1076, 12, "jsxDEV"], [1363, 60, 1076, 12], [1363, 62, 1076, 13, "_TouchableOpacity"], [1363, 79, 1076, 13], [1363, 80, 1076, 13, "default"], [1363, 87, 1076, 29], [1364, 14, 1077, 14, "onPress"], [1364, 21, 1077, 21], [1364, 23, 1077, 23, "retryCapture"], [1364, 35, 1077, 36], [1365, 14, 1078, 14, "style"], [1365, 19, 1078, 19], [1365, 21, 1078, 21, "styles"], [1365, 27, 1078, 27], [1365, 28, 1078, 28, "primaryButton"], [1365, 41, 1078, 42], [1366, 14, 1078, 42, "children"], [1366, 22, 1078, 42], [1366, 37, 1080, 14], [1366, 41, 1080, 14, "_jsxDevRuntime"], [1366, 55, 1080, 14], [1366, 56, 1080, 14, "jsxDEV"], [1366, 62, 1080, 14], [1366, 64, 1080, 15, "_Text"], [1366, 69, 1080, 15], [1366, 70, 1080, 15, "default"], [1366, 77, 1080, 19], [1367, 16, 1080, 20, "style"], [1367, 21, 1080, 25], [1367, 23, 1080, 27, "styles"], [1367, 29, 1080, 33], [1367, 30, 1080, 34, "primaryButtonText"], [1367, 47, 1080, 52], [1368, 16, 1080, 52, "children"], [1368, 24, 1080, 52], [1368, 26, 1080, 53], [1369, 14, 1080, 62], [1370, 16, 1080, 62, "fileName"], [1370, 24, 1080, 62], [1370, 26, 1080, 62, "_jsxFileName"], [1370, 38, 1080, 62], [1371, 16, 1080, 62, "lineNumber"], [1371, 26, 1080, 62], [1372, 16, 1080, 62, "columnNumber"], [1372, 28, 1080, 62], [1373, 14, 1080, 62], [1373, 21, 1080, 68], [1374, 12, 1080, 69], [1375, 14, 1080, 69, "fileName"], [1375, 22, 1080, 69], [1375, 24, 1080, 69, "_jsxFileName"], [1375, 36, 1080, 69], [1376, 14, 1080, 69, "lineNumber"], [1376, 24, 1080, 69], [1377, 14, 1080, 69, "columnNumber"], [1377, 26, 1080, 69], [1378, 12, 1080, 69], [1378, 19, 1081, 30], [1378, 20, 1081, 31], [1378, 35, 1082, 12], [1378, 39, 1082, 12, "_jsxDevRuntime"], [1378, 53, 1082, 12], [1378, 54, 1082, 12, "jsxDEV"], [1378, 60, 1082, 12], [1378, 62, 1082, 13, "_TouchableOpacity"], [1378, 79, 1082, 13], [1378, 80, 1082, 13, "default"], [1378, 87, 1082, 29], [1379, 14, 1083, 14, "onPress"], [1379, 21, 1083, 21], [1379, 23, 1083, 23, "onCancel"], [1379, 31, 1083, 32], [1380, 14, 1084, 14, "style"], [1380, 19, 1084, 19], [1380, 21, 1084, 21, "styles"], [1380, 27, 1084, 27], [1380, 28, 1084, 28, "secondaryButton"], [1380, 43, 1084, 44], [1381, 14, 1084, 44, "children"], [1381, 22, 1084, 44], [1381, 37, 1086, 14], [1381, 41, 1086, 14, "_jsxDevRuntime"], [1381, 55, 1086, 14], [1381, 56, 1086, 14, "jsxDEV"], [1381, 62, 1086, 14], [1381, 64, 1086, 15, "_Text"], [1381, 69, 1086, 15], [1381, 70, 1086, 15, "default"], [1381, 77, 1086, 19], [1382, 16, 1086, 20, "style"], [1382, 21, 1086, 25], [1382, 23, 1086, 27, "styles"], [1382, 29, 1086, 33], [1382, 30, 1086, 34, "secondaryButtonText"], [1382, 49, 1086, 54], [1383, 16, 1086, 54, "children"], [1383, 24, 1086, 54], [1383, 26, 1086, 55], [1384, 14, 1086, 61], [1385, 16, 1086, 61, "fileName"], [1385, 24, 1086, 61], [1385, 26, 1086, 61, "_jsxFileName"], [1385, 38, 1086, 61], [1386, 16, 1086, 61, "lineNumber"], [1386, 26, 1086, 61], [1387, 16, 1086, 61, "columnNumber"], [1387, 28, 1086, 61], [1388, 14, 1086, 61], [1388, 21, 1086, 67], [1389, 12, 1086, 68], [1390, 14, 1086, 68, "fileName"], [1390, 22, 1086, 68], [1390, 24, 1086, 68, "_jsxFileName"], [1390, 36, 1086, 68], [1391, 14, 1086, 68, "lineNumber"], [1391, 24, 1086, 68], [1392, 14, 1086, 68, "columnNumber"], [1392, 26, 1086, 68], [1393, 12, 1086, 68], [1393, 19, 1087, 30], [1393, 20, 1087, 31], [1394, 10, 1087, 31], [1395, 12, 1087, 31, "fileName"], [1395, 20, 1087, 31], [1395, 22, 1087, 31, "_jsxFileName"], [1395, 34, 1087, 31], [1396, 12, 1087, 31, "lineNumber"], [1396, 22, 1087, 31], [1397, 12, 1087, 31, "columnNumber"], [1397, 24, 1087, 31], [1398, 10, 1087, 31], [1398, 17, 1088, 16], [1399, 8, 1088, 17], [1400, 10, 1088, 17, "fileName"], [1400, 18, 1088, 17], [1400, 20, 1088, 17, "_jsxFileName"], [1400, 32, 1088, 17], [1401, 10, 1088, 17, "lineNumber"], [1401, 20, 1088, 17], [1402, 10, 1088, 17, "columnNumber"], [1402, 22, 1088, 17], [1403, 8, 1088, 17], [1403, 15, 1089, 14], [1404, 6, 1089, 15], [1405, 8, 1089, 15, "fileName"], [1405, 16, 1089, 15], [1405, 18, 1089, 15, "_jsxFileName"], [1405, 30, 1089, 15], [1406, 8, 1089, 15, "lineNumber"], [1406, 18, 1089, 15], [1407, 8, 1089, 15, "columnNumber"], [1407, 20, 1089, 15], [1408, 6, 1089, 15], [1408, 13, 1090, 13], [1408, 14, 1090, 14], [1409, 4, 1090, 14], [1410, 6, 1090, 14, "fileName"], [1410, 14, 1090, 14], [1410, 16, 1090, 14, "_jsxFileName"], [1410, 28, 1090, 14], [1411, 6, 1090, 14, "lineNumber"], [1411, 16, 1090, 14], [1412, 6, 1090, 14, "columnNumber"], [1412, 18, 1090, 14], [1413, 4, 1090, 14], [1413, 11, 1091, 10], [1413, 12, 1091, 11], [1414, 2, 1093, 0], [1415, 2, 1093, 1, "_s"], [1415, 4, 1093, 1], [1415, 5, 51, 24, "EchoCameraWeb"], [1415, 18, 51, 37], [1416, 4, 51, 37], [1416, 12, 58, 42, "useCameraPermissions"], [1416, 44, 58, 62], [1416, 46, 72, 19, "useUpload"], [1416, 64, 72, 28], [1417, 2, 72, 28], [1418, 2, 72, 28, "_c"], [1418, 4, 72, 28], [1418, 7, 51, 24, "EchoCameraWeb"], [1418, 20, 51, 37], [1419, 2, 1094, 0], [1419, 8, 1094, 6, "styles"], [1419, 14, 1094, 12], [1419, 17, 1094, 15, "StyleSheet"], [1419, 36, 1094, 25], [1419, 37, 1094, 26, "create"], [1419, 43, 1094, 32], [1419, 44, 1094, 33], [1420, 4, 1095, 2, "container"], [1420, 13, 1095, 11], [1420, 15, 1095, 13], [1421, 6, 1096, 4, "flex"], [1421, 10, 1096, 8], [1421, 12, 1096, 10], [1421, 13, 1096, 11], [1422, 6, 1097, 4, "backgroundColor"], [1422, 21, 1097, 19], [1422, 23, 1097, 21], [1423, 4, 1098, 2], [1423, 5, 1098, 3], [1424, 4, 1099, 2, "cameraContainer"], [1424, 19, 1099, 17], [1424, 21, 1099, 19], [1425, 6, 1100, 4, "flex"], [1425, 10, 1100, 8], [1425, 12, 1100, 10], [1425, 13, 1100, 11], [1426, 6, 1101, 4, "max<PERSON><PERSON><PERSON>"], [1426, 14, 1101, 12], [1426, 16, 1101, 14], [1426, 19, 1101, 17], [1427, 6, 1102, 4, "alignSelf"], [1427, 15, 1102, 13], [1427, 17, 1102, 15], [1427, 25, 1102, 23], [1428, 6, 1103, 4, "width"], [1428, 11, 1103, 9], [1428, 13, 1103, 11], [1429, 4, 1104, 2], [1429, 5, 1104, 3], [1430, 4, 1105, 2, "camera"], [1430, 10, 1105, 8], [1430, 12, 1105, 10], [1431, 6, 1106, 4, "flex"], [1431, 10, 1106, 8], [1431, 12, 1106, 10], [1432, 4, 1107, 2], [1432, 5, 1107, 3], [1433, 4, 1108, 2, "headerOverlay"], [1433, 17, 1108, 15], [1433, 19, 1108, 17], [1434, 6, 1109, 4, "position"], [1434, 14, 1109, 12], [1434, 16, 1109, 14], [1434, 26, 1109, 24], [1435, 6, 1110, 4, "top"], [1435, 9, 1110, 7], [1435, 11, 1110, 9], [1435, 12, 1110, 10], [1436, 6, 1111, 4, "left"], [1436, 10, 1111, 8], [1436, 12, 1111, 10], [1436, 13, 1111, 11], [1437, 6, 1112, 4, "right"], [1437, 11, 1112, 9], [1437, 13, 1112, 11], [1437, 14, 1112, 12], [1438, 6, 1113, 4, "backgroundColor"], [1438, 21, 1113, 19], [1438, 23, 1113, 21], [1438, 36, 1113, 34], [1439, 6, 1114, 4, "paddingTop"], [1439, 16, 1114, 14], [1439, 18, 1114, 16], [1439, 20, 1114, 18], [1440, 6, 1115, 4, "paddingHorizontal"], [1440, 23, 1115, 21], [1440, 25, 1115, 23], [1440, 27, 1115, 25], [1441, 6, 1116, 4, "paddingBottom"], [1441, 19, 1116, 17], [1441, 21, 1116, 19], [1442, 4, 1117, 2], [1442, 5, 1117, 3], [1443, 4, 1118, 2, "headerContent"], [1443, 17, 1118, 15], [1443, 19, 1118, 17], [1444, 6, 1119, 4, "flexDirection"], [1444, 19, 1119, 17], [1444, 21, 1119, 19], [1444, 26, 1119, 24], [1445, 6, 1120, 4, "justifyContent"], [1445, 20, 1120, 18], [1445, 22, 1120, 20], [1445, 37, 1120, 35], [1446, 6, 1121, 4, "alignItems"], [1446, 16, 1121, 14], [1446, 18, 1121, 16], [1447, 4, 1122, 2], [1447, 5, 1122, 3], [1448, 4, 1123, 2, "headerLeft"], [1448, 14, 1123, 12], [1448, 16, 1123, 14], [1449, 6, 1124, 4, "flex"], [1449, 10, 1124, 8], [1449, 12, 1124, 10], [1450, 4, 1125, 2], [1450, 5, 1125, 3], [1451, 4, 1126, 2, "headerTitle"], [1451, 15, 1126, 13], [1451, 17, 1126, 15], [1452, 6, 1127, 4, "fontSize"], [1452, 14, 1127, 12], [1452, 16, 1127, 14], [1452, 18, 1127, 16], [1453, 6, 1128, 4, "fontWeight"], [1453, 16, 1128, 14], [1453, 18, 1128, 16], [1453, 23, 1128, 21], [1454, 6, 1129, 4, "color"], [1454, 11, 1129, 9], [1454, 13, 1129, 11], [1454, 19, 1129, 17], [1455, 6, 1130, 4, "marginBottom"], [1455, 18, 1130, 16], [1455, 20, 1130, 18], [1456, 4, 1131, 2], [1456, 5, 1131, 3], [1457, 4, 1132, 2, "subtitleRow"], [1457, 15, 1132, 13], [1457, 17, 1132, 15], [1458, 6, 1133, 4, "flexDirection"], [1458, 19, 1133, 17], [1458, 21, 1133, 19], [1458, 26, 1133, 24], [1459, 6, 1134, 4, "alignItems"], [1459, 16, 1134, 14], [1459, 18, 1134, 16], [1459, 26, 1134, 24], [1460, 6, 1135, 4, "marginBottom"], [1460, 18, 1135, 16], [1460, 20, 1135, 18], [1461, 4, 1136, 2], [1461, 5, 1136, 3], [1462, 4, 1137, 2, "webIcon"], [1462, 11, 1137, 9], [1462, 13, 1137, 11], [1463, 6, 1138, 4, "fontSize"], [1463, 14, 1138, 12], [1463, 16, 1138, 14], [1463, 18, 1138, 16], [1464, 6, 1139, 4, "marginRight"], [1464, 17, 1139, 15], [1464, 19, 1139, 17], [1465, 4, 1140, 2], [1465, 5, 1140, 3], [1466, 4, 1141, 2, "headerSubtitle"], [1466, 18, 1141, 16], [1466, 20, 1141, 18], [1467, 6, 1142, 4, "fontSize"], [1467, 14, 1142, 12], [1467, 16, 1142, 14], [1467, 18, 1142, 16], [1468, 6, 1143, 4, "color"], [1468, 11, 1143, 9], [1468, 13, 1143, 11], [1468, 19, 1143, 17], [1469, 6, 1144, 4, "opacity"], [1469, 13, 1144, 11], [1469, 15, 1144, 13], [1470, 4, 1145, 2], [1470, 5, 1145, 3], [1471, 4, 1146, 2, "challengeRow"], [1471, 16, 1146, 14], [1471, 18, 1146, 16], [1472, 6, 1147, 4, "flexDirection"], [1472, 19, 1147, 17], [1472, 21, 1147, 19], [1472, 26, 1147, 24], [1473, 6, 1148, 4, "alignItems"], [1473, 16, 1148, 14], [1473, 18, 1148, 16], [1474, 4, 1149, 2], [1474, 5, 1149, 3], [1475, 4, 1150, 2, "challengeCode"], [1475, 17, 1150, 15], [1475, 19, 1150, 17], [1476, 6, 1151, 4, "fontSize"], [1476, 14, 1151, 12], [1476, 16, 1151, 14], [1476, 18, 1151, 16], [1477, 6, 1152, 4, "color"], [1477, 11, 1152, 9], [1477, 13, 1152, 11], [1477, 19, 1152, 17], [1478, 6, 1153, 4, "marginLeft"], [1478, 16, 1153, 14], [1478, 18, 1153, 16], [1478, 19, 1153, 17], [1479, 6, 1154, 4, "fontFamily"], [1479, 16, 1154, 14], [1479, 18, 1154, 16], [1480, 4, 1155, 2], [1480, 5, 1155, 3], [1481, 4, 1156, 2, "closeButton"], [1481, 15, 1156, 13], [1481, 17, 1156, 15], [1482, 6, 1157, 4, "padding"], [1482, 13, 1157, 11], [1482, 15, 1157, 13], [1483, 4, 1158, 2], [1483, 5, 1158, 3], [1484, 4, 1159, 2, "privacyNotice"], [1484, 17, 1159, 15], [1484, 19, 1159, 17], [1485, 6, 1160, 4, "position"], [1485, 14, 1160, 12], [1485, 16, 1160, 14], [1485, 26, 1160, 24], [1486, 6, 1161, 4, "top"], [1486, 9, 1161, 7], [1486, 11, 1161, 9], [1486, 14, 1161, 12], [1487, 6, 1162, 4, "left"], [1487, 10, 1162, 8], [1487, 12, 1162, 10], [1487, 14, 1162, 12], [1488, 6, 1163, 4, "right"], [1488, 11, 1163, 9], [1488, 13, 1163, 11], [1488, 15, 1163, 13], [1489, 6, 1164, 4, "backgroundColor"], [1489, 21, 1164, 19], [1489, 23, 1164, 21], [1489, 48, 1164, 46], [1490, 6, 1165, 4, "borderRadius"], [1490, 18, 1165, 16], [1490, 20, 1165, 18], [1490, 21, 1165, 19], [1491, 6, 1166, 4, "padding"], [1491, 13, 1166, 11], [1491, 15, 1166, 13], [1491, 17, 1166, 15], [1492, 6, 1167, 4, "flexDirection"], [1492, 19, 1167, 17], [1492, 21, 1167, 19], [1492, 26, 1167, 24], [1493, 6, 1168, 4, "alignItems"], [1493, 16, 1168, 14], [1493, 18, 1168, 16], [1494, 4, 1169, 2], [1494, 5, 1169, 3], [1495, 4, 1170, 2, "privacyText"], [1495, 15, 1170, 13], [1495, 17, 1170, 15], [1496, 6, 1171, 4, "color"], [1496, 11, 1171, 9], [1496, 13, 1171, 11], [1496, 19, 1171, 17], [1497, 6, 1172, 4, "fontSize"], [1497, 14, 1172, 12], [1497, 16, 1172, 14], [1497, 18, 1172, 16], [1498, 6, 1173, 4, "marginLeft"], [1498, 16, 1173, 14], [1498, 18, 1173, 16], [1498, 19, 1173, 17], [1499, 6, 1174, 4, "flex"], [1499, 10, 1174, 8], [1499, 12, 1174, 10], [1500, 4, 1175, 2], [1500, 5, 1175, 3], [1501, 4, 1176, 2, "footer<PERSON><PERSON><PERSON>"], [1501, 17, 1176, 15], [1501, 19, 1176, 17], [1502, 6, 1177, 4, "position"], [1502, 14, 1177, 12], [1502, 16, 1177, 14], [1502, 26, 1177, 24], [1503, 6, 1178, 4, "bottom"], [1503, 12, 1178, 10], [1503, 14, 1178, 12], [1503, 15, 1178, 13], [1504, 6, 1179, 4, "left"], [1504, 10, 1179, 8], [1504, 12, 1179, 10], [1504, 13, 1179, 11], [1505, 6, 1180, 4, "right"], [1505, 11, 1180, 9], [1505, 13, 1180, 11], [1505, 14, 1180, 12], [1506, 6, 1181, 4, "backgroundColor"], [1506, 21, 1181, 19], [1506, 23, 1181, 21], [1506, 36, 1181, 34], [1507, 6, 1182, 4, "paddingBottom"], [1507, 19, 1182, 17], [1507, 21, 1182, 19], [1507, 23, 1182, 21], [1508, 6, 1183, 4, "paddingTop"], [1508, 16, 1183, 14], [1508, 18, 1183, 16], [1508, 20, 1183, 18], [1509, 6, 1184, 4, "alignItems"], [1509, 16, 1184, 14], [1509, 18, 1184, 16], [1510, 4, 1185, 2], [1510, 5, 1185, 3], [1511, 4, 1186, 2, "instruction"], [1511, 15, 1186, 13], [1511, 17, 1186, 15], [1512, 6, 1187, 4, "fontSize"], [1512, 14, 1187, 12], [1512, 16, 1187, 14], [1512, 18, 1187, 16], [1513, 6, 1188, 4, "color"], [1513, 11, 1188, 9], [1513, 13, 1188, 11], [1513, 19, 1188, 17], [1514, 6, 1189, 4, "marginBottom"], [1514, 18, 1189, 16], [1514, 20, 1189, 18], [1515, 4, 1190, 2], [1515, 5, 1190, 3], [1516, 4, 1191, 2, "shutterButton"], [1516, 17, 1191, 15], [1516, 19, 1191, 17], [1517, 6, 1192, 4, "width"], [1517, 11, 1192, 9], [1517, 13, 1192, 11], [1517, 15, 1192, 13], [1518, 6, 1193, 4, "height"], [1518, 12, 1193, 10], [1518, 14, 1193, 12], [1518, 16, 1193, 14], [1519, 6, 1194, 4, "borderRadius"], [1519, 18, 1194, 16], [1519, 20, 1194, 18], [1519, 22, 1194, 20], [1520, 6, 1195, 4, "backgroundColor"], [1520, 21, 1195, 19], [1520, 23, 1195, 21], [1520, 29, 1195, 27], [1521, 6, 1196, 4, "justifyContent"], [1521, 20, 1196, 18], [1521, 22, 1196, 20], [1521, 30, 1196, 28], [1522, 6, 1197, 4, "alignItems"], [1522, 16, 1197, 14], [1522, 18, 1197, 16], [1522, 26, 1197, 24], [1523, 6, 1198, 4, "marginBottom"], [1523, 18, 1198, 16], [1523, 20, 1198, 18], [1523, 22, 1198, 20], [1524, 6, 1199, 4], [1524, 9, 1199, 7, "Platform"], [1524, 26, 1199, 15], [1524, 27, 1199, 16, "select"], [1524, 33, 1199, 22], [1524, 34, 1199, 23], [1525, 8, 1200, 6, "ios"], [1525, 11, 1200, 9], [1525, 13, 1200, 11], [1526, 10, 1201, 8, "shadowColor"], [1526, 21, 1201, 19], [1526, 23, 1201, 21], [1526, 32, 1201, 30], [1527, 10, 1202, 8, "shadowOffset"], [1527, 22, 1202, 20], [1527, 24, 1202, 22], [1528, 12, 1202, 24, "width"], [1528, 17, 1202, 29], [1528, 19, 1202, 31], [1528, 20, 1202, 32], [1529, 12, 1202, 34, "height"], [1529, 18, 1202, 40], [1529, 20, 1202, 42], [1530, 10, 1202, 44], [1530, 11, 1202, 45], [1531, 10, 1203, 8, "shadowOpacity"], [1531, 23, 1203, 21], [1531, 25, 1203, 23], [1531, 28, 1203, 26], [1532, 10, 1204, 8, "shadowRadius"], [1532, 22, 1204, 20], [1532, 24, 1204, 22], [1533, 8, 1205, 6], [1533, 9, 1205, 7], [1534, 8, 1206, 6, "android"], [1534, 15, 1206, 13], [1534, 17, 1206, 15], [1535, 10, 1207, 8, "elevation"], [1535, 19, 1207, 17], [1535, 21, 1207, 19], [1536, 8, 1208, 6], [1536, 9, 1208, 7], [1537, 8, 1209, 6, "web"], [1537, 11, 1209, 9], [1537, 13, 1209, 11], [1538, 10, 1210, 8, "boxShadow"], [1538, 19, 1210, 17], [1538, 21, 1210, 19], [1539, 8, 1211, 6], [1540, 6, 1212, 4], [1540, 7, 1212, 5], [1541, 4, 1213, 2], [1541, 5, 1213, 3], [1542, 4, 1214, 2, "shutterButtonDisabled"], [1542, 25, 1214, 23], [1542, 27, 1214, 25], [1543, 6, 1215, 4, "opacity"], [1543, 13, 1215, 11], [1543, 15, 1215, 13], [1544, 4, 1216, 2], [1544, 5, 1216, 3], [1545, 4, 1217, 2, "shutterInner"], [1545, 16, 1217, 14], [1545, 18, 1217, 16], [1546, 6, 1218, 4, "width"], [1546, 11, 1218, 9], [1546, 13, 1218, 11], [1546, 15, 1218, 13], [1547, 6, 1219, 4, "height"], [1547, 12, 1219, 10], [1547, 14, 1219, 12], [1547, 16, 1219, 14], [1548, 6, 1220, 4, "borderRadius"], [1548, 18, 1220, 16], [1548, 20, 1220, 18], [1548, 22, 1220, 20], [1549, 6, 1221, 4, "backgroundColor"], [1549, 21, 1221, 19], [1549, 23, 1221, 21], [1549, 29, 1221, 27], [1550, 6, 1222, 4, "borderWidth"], [1550, 17, 1222, 15], [1550, 19, 1222, 17], [1550, 20, 1222, 18], [1551, 6, 1223, 4, "borderColor"], [1551, 17, 1223, 15], [1551, 19, 1223, 17], [1552, 4, 1224, 2], [1552, 5, 1224, 3], [1553, 4, 1225, 2, "privacyNote"], [1553, 15, 1225, 13], [1553, 17, 1225, 15], [1554, 6, 1226, 4, "fontSize"], [1554, 14, 1226, 12], [1554, 16, 1226, 14], [1554, 18, 1226, 16], [1555, 6, 1227, 4, "color"], [1555, 11, 1227, 9], [1555, 13, 1227, 11], [1556, 4, 1228, 2], [1556, 5, 1228, 3], [1557, 4, 1229, 2, "processingModal"], [1557, 19, 1229, 17], [1557, 21, 1229, 19], [1558, 6, 1230, 4, "flex"], [1558, 10, 1230, 8], [1558, 12, 1230, 10], [1558, 13, 1230, 11], [1559, 6, 1231, 4, "backgroundColor"], [1559, 21, 1231, 19], [1559, 23, 1231, 21], [1559, 43, 1231, 41], [1560, 6, 1232, 4, "justifyContent"], [1560, 20, 1232, 18], [1560, 22, 1232, 20], [1560, 30, 1232, 28], [1561, 6, 1233, 4, "alignItems"], [1561, 16, 1233, 14], [1561, 18, 1233, 16], [1562, 4, 1234, 2], [1562, 5, 1234, 3], [1563, 4, 1235, 2, "processingContent"], [1563, 21, 1235, 19], [1563, 23, 1235, 21], [1564, 6, 1236, 4, "backgroundColor"], [1564, 21, 1236, 19], [1564, 23, 1236, 21], [1564, 29, 1236, 27], [1565, 6, 1237, 4, "borderRadius"], [1565, 18, 1237, 16], [1565, 20, 1237, 18], [1565, 22, 1237, 20], [1566, 6, 1238, 4, "padding"], [1566, 13, 1238, 11], [1566, 15, 1238, 13], [1566, 17, 1238, 15], [1567, 6, 1239, 4, "width"], [1567, 11, 1239, 9], [1567, 13, 1239, 11], [1567, 18, 1239, 16], [1568, 6, 1240, 4, "max<PERSON><PERSON><PERSON>"], [1568, 14, 1240, 12], [1568, 16, 1240, 14], [1568, 19, 1240, 17], [1569, 6, 1241, 4, "alignItems"], [1569, 16, 1241, 14], [1569, 18, 1241, 16], [1570, 4, 1242, 2], [1570, 5, 1242, 3], [1571, 4, 1243, 2, "processingTitle"], [1571, 19, 1243, 17], [1571, 21, 1243, 19], [1572, 6, 1244, 4, "fontSize"], [1572, 14, 1244, 12], [1572, 16, 1244, 14], [1572, 18, 1244, 16], [1573, 6, 1245, 4, "fontWeight"], [1573, 16, 1245, 14], [1573, 18, 1245, 16], [1573, 23, 1245, 21], [1574, 6, 1246, 4, "color"], [1574, 11, 1246, 9], [1574, 13, 1246, 11], [1574, 22, 1246, 20], [1575, 6, 1247, 4, "marginTop"], [1575, 15, 1247, 13], [1575, 17, 1247, 15], [1575, 19, 1247, 17], [1576, 6, 1248, 4, "marginBottom"], [1576, 18, 1248, 16], [1576, 20, 1248, 18], [1577, 4, 1249, 2], [1577, 5, 1249, 3], [1578, 4, 1250, 2, "progressBar"], [1578, 15, 1250, 13], [1578, 17, 1250, 15], [1579, 6, 1251, 4, "width"], [1579, 11, 1251, 9], [1579, 13, 1251, 11], [1579, 19, 1251, 17], [1580, 6, 1252, 4, "height"], [1580, 12, 1252, 10], [1580, 14, 1252, 12], [1580, 15, 1252, 13], [1581, 6, 1253, 4, "backgroundColor"], [1581, 21, 1253, 19], [1581, 23, 1253, 21], [1581, 32, 1253, 30], [1582, 6, 1254, 4, "borderRadius"], [1582, 18, 1254, 16], [1582, 20, 1254, 18], [1582, 21, 1254, 19], [1583, 6, 1255, 4, "overflow"], [1583, 14, 1255, 12], [1583, 16, 1255, 14], [1583, 24, 1255, 22], [1584, 6, 1256, 4, "marginBottom"], [1584, 18, 1256, 16], [1584, 20, 1256, 18], [1585, 4, 1257, 2], [1585, 5, 1257, 3], [1586, 4, 1258, 2, "progressFill"], [1586, 16, 1258, 14], [1586, 18, 1258, 16], [1587, 6, 1259, 4, "height"], [1587, 12, 1259, 10], [1587, 14, 1259, 12], [1587, 20, 1259, 18], [1588, 6, 1260, 4, "backgroundColor"], [1588, 21, 1260, 19], [1588, 23, 1260, 21], [1588, 32, 1260, 30], [1589, 6, 1261, 4, "borderRadius"], [1589, 18, 1261, 16], [1589, 20, 1261, 18], [1590, 4, 1262, 2], [1590, 5, 1262, 3], [1591, 4, 1263, 2, "processingDescription"], [1591, 25, 1263, 23], [1591, 27, 1263, 25], [1592, 6, 1264, 4, "fontSize"], [1592, 14, 1264, 12], [1592, 16, 1264, 14], [1592, 18, 1264, 16], [1593, 6, 1265, 4, "color"], [1593, 11, 1265, 9], [1593, 13, 1265, 11], [1593, 22, 1265, 20], [1594, 6, 1266, 4, "textAlign"], [1594, 15, 1266, 13], [1594, 17, 1266, 15], [1595, 4, 1267, 2], [1595, 5, 1267, 3], [1596, 4, 1268, 2, "successIcon"], [1596, 15, 1268, 13], [1596, 17, 1268, 15], [1597, 6, 1269, 4, "marginTop"], [1597, 15, 1269, 13], [1597, 17, 1269, 15], [1598, 4, 1270, 2], [1598, 5, 1270, 3], [1599, 4, 1271, 2, "errorContent"], [1599, 16, 1271, 14], [1599, 18, 1271, 16], [1600, 6, 1272, 4, "backgroundColor"], [1600, 21, 1272, 19], [1600, 23, 1272, 21], [1600, 29, 1272, 27], [1601, 6, 1273, 4, "borderRadius"], [1601, 18, 1273, 16], [1601, 20, 1273, 18], [1601, 22, 1273, 20], [1602, 6, 1274, 4, "padding"], [1602, 13, 1274, 11], [1602, 15, 1274, 13], [1602, 17, 1274, 15], [1603, 6, 1275, 4, "width"], [1603, 11, 1275, 9], [1603, 13, 1275, 11], [1603, 18, 1275, 16], [1604, 6, 1276, 4, "max<PERSON><PERSON><PERSON>"], [1604, 14, 1276, 12], [1604, 16, 1276, 14], [1604, 19, 1276, 17], [1605, 6, 1277, 4, "alignItems"], [1605, 16, 1277, 14], [1605, 18, 1277, 16], [1606, 4, 1278, 2], [1606, 5, 1278, 3], [1607, 4, 1279, 2, "errorTitle"], [1607, 14, 1279, 12], [1607, 16, 1279, 14], [1608, 6, 1280, 4, "fontSize"], [1608, 14, 1280, 12], [1608, 16, 1280, 14], [1608, 18, 1280, 16], [1609, 6, 1281, 4, "fontWeight"], [1609, 16, 1281, 14], [1609, 18, 1281, 16], [1609, 23, 1281, 21], [1610, 6, 1282, 4, "color"], [1610, 11, 1282, 9], [1610, 13, 1282, 11], [1610, 22, 1282, 20], [1611, 6, 1283, 4, "marginTop"], [1611, 15, 1283, 13], [1611, 17, 1283, 15], [1611, 19, 1283, 17], [1612, 6, 1284, 4, "marginBottom"], [1612, 18, 1284, 16], [1612, 20, 1284, 18], [1613, 4, 1285, 2], [1613, 5, 1285, 3], [1614, 4, 1286, 2, "errorMessage"], [1614, 16, 1286, 14], [1614, 18, 1286, 16], [1615, 6, 1287, 4, "fontSize"], [1615, 14, 1287, 12], [1615, 16, 1287, 14], [1615, 18, 1287, 16], [1616, 6, 1288, 4, "color"], [1616, 11, 1288, 9], [1616, 13, 1288, 11], [1616, 22, 1288, 20], [1617, 6, 1289, 4, "textAlign"], [1617, 15, 1289, 13], [1617, 17, 1289, 15], [1617, 25, 1289, 23], [1618, 6, 1290, 4, "marginBottom"], [1618, 18, 1290, 16], [1618, 20, 1290, 18], [1619, 4, 1291, 2], [1619, 5, 1291, 3], [1620, 4, 1292, 2, "primaryButton"], [1620, 17, 1292, 15], [1620, 19, 1292, 17], [1621, 6, 1293, 4, "backgroundColor"], [1621, 21, 1293, 19], [1621, 23, 1293, 21], [1621, 32, 1293, 30], [1622, 6, 1294, 4, "paddingHorizontal"], [1622, 23, 1294, 21], [1622, 25, 1294, 23], [1622, 27, 1294, 25], [1623, 6, 1295, 4, "paddingVertical"], [1623, 21, 1295, 19], [1623, 23, 1295, 21], [1623, 25, 1295, 23], [1624, 6, 1296, 4, "borderRadius"], [1624, 18, 1296, 16], [1624, 20, 1296, 18], [1624, 21, 1296, 19], [1625, 6, 1297, 4, "marginTop"], [1625, 15, 1297, 13], [1625, 17, 1297, 15], [1626, 4, 1298, 2], [1626, 5, 1298, 3], [1627, 4, 1299, 2, "primaryButtonText"], [1627, 21, 1299, 19], [1627, 23, 1299, 21], [1628, 6, 1300, 4, "color"], [1628, 11, 1300, 9], [1628, 13, 1300, 11], [1628, 19, 1300, 17], [1629, 6, 1301, 4, "fontSize"], [1629, 14, 1301, 12], [1629, 16, 1301, 14], [1629, 18, 1301, 16], [1630, 6, 1302, 4, "fontWeight"], [1630, 16, 1302, 14], [1630, 18, 1302, 16], [1631, 4, 1303, 2], [1631, 5, 1303, 3], [1632, 4, 1304, 2, "secondaryButton"], [1632, 19, 1304, 17], [1632, 21, 1304, 19], [1633, 6, 1305, 4, "paddingHorizontal"], [1633, 23, 1305, 21], [1633, 25, 1305, 23], [1633, 27, 1305, 25], [1634, 6, 1306, 4, "paddingVertical"], [1634, 21, 1306, 19], [1634, 23, 1306, 21], [1634, 25, 1306, 23], [1635, 6, 1307, 4, "marginTop"], [1635, 15, 1307, 13], [1635, 17, 1307, 15], [1636, 4, 1308, 2], [1636, 5, 1308, 3], [1637, 4, 1309, 2, "secondaryButtonText"], [1637, 23, 1309, 21], [1637, 25, 1309, 23], [1638, 6, 1310, 4, "color"], [1638, 11, 1310, 9], [1638, 13, 1310, 11], [1638, 22, 1310, 20], [1639, 6, 1311, 4, "fontSize"], [1639, 14, 1311, 12], [1639, 16, 1311, 14], [1640, 4, 1312, 2], [1640, 5, 1312, 3], [1641, 4, 1313, 2, "permissionContent"], [1641, 21, 1313, 19], [1641, 23, 1313, 21], [1642, 6, 1314, 4, "flex"], [1642, 10, 1314, 8], [1642, 12, 1314, 10], [1642, 13, 1314, 11], [1643, 6, 1315, 4, "justifyContent"], [1643, 20, 1315, 18], [1643, 22, 1315, 20], [1643, 30, 1315, 28], [1644, 6, 1316, 4, "alignItems"], [1644, 16, 1316, 14], [1644, 18, 1316, 16], [1644, 26, 1316, 24], [1645, 6, 1317, 4, "padding"], [1645, 13, 1317, 11], [1645, 15, 1317, 13], [1646, 4, 1318, 2], [1646, 5, 1318, 3], [1647, 4, 1319, 2, "permissionTitle"], [1647, 19, 1319, 17], [1647, 21, 1319, 19], [1648, 6, 1320, 4, "fontSize"], [1648, 14, 1320, 12], [1648, 16, 1320, 14], [1648, 18, 1320, 16], [1649, 6, 1321, 4, "fontWeight"], [1649, 16, 1321, 14], [1649, 18, 1321, 16], [1649, 23, 1321, 21], [1650, 6, 1322, 4, "color"], [1650, 11, 1322, 9], [1650, 13, 1322, 11], [1650, 22, 1322, 20], [1651, 6, 1323, 4, "marginTop"], [1651, 15, 1323, 13], [1651, 17, 1323, 15], [1651, 19, 1323, 17], [1652, 6, 1324, 4, "marginBottom"], [1652, 18, 1324, 16], [1652, 20, 1324, 18], [1653, 4, 1325, 2], [1653, 5, 1325, 3], [1654, 4, 1326, 2, "permissionDescription"], [1654, 25, 1326, 23], [1654, 27, 1326, 25], [1655, 6, 1327, 4, "fontSize"], [1655, 14, 1327, 12], [1655, 16, 1327, 14], [1655, 18, 1327, 16], [1656, 6, 1328, 4, "color"], [1656, 11, 1328, 9], [1656, 13, 1328, 11], [1656, 22, 1328, 20], [1657, 6, 1329, 4, "textAlign"], [1657, 15, 1329, 13], [1657, 17, 1329, 15], [1657, 25, 1329, 23], [1658, 6, 1330, 4, "marginBottom"], [1658, 18, 1330, 16], [1658, 20, 1330, 18], [1659, 4, 1331, 2], [1659, 5, 1331, 3], [1660, 4, 1332, 2, "loadingText"], [1660, 15, 1332, 13], [1660, 17, 1332, 15], [1661, 6, 1333, 4, "color"], [1661, 11, 1333, 9], [1661, 13, 1333, 11], [1661, 22, 1333, 20], [1662, 6, 1334, 4, "marginTop"], [1662, 15, 1334, 13], [1662, 17, 1334, 15], [1663, 4, 1335, 2], [1663, 5, 1335, 3], [1664, 4, 1336, 2], [1665, 4, 1337, 2, "blurZone"], [1665, 12, 1337, 10], [1665, 14, 1337, 12], [1666, 6, 1338, 4, "position"], [1666, 14, 1338, 12], [1666, 16, 1338, 14], [1666, 26, 1338, 24], [1667, 6, 1339, 4, "overflow"], [1667, 14, 1339, 12], [1667, 16, 1339, 14], [1668, 4, 1340, 2], [1668, 5, 1340, 3], [1669, 4, 1341, 2, "previewChip"], [1669, 15, 1341, 13], [1669, 17, 1341, 15], [1670, 6, 1342, 4, "position"], [1670, 14, 1342, 12], [1670, 16, 1342, 14], [1670, 26, 1342, 24], [1671, 6, 1343, 4, "top"], [1671, 9, 1343, 7], [1671, 11, 1343, 9], [1671, 12, 1343, 10], [1672, 6, 1344, 4, "right"], [1672, 11, 1344, 9], [1672, 13, 1344, 11], [1672, 14, 1344, 12], [1673, 6, 1345, 4, "backgroundColor"], [1673, 21, 1345, 19], [1673, 23, 1345, 21], [1673, 40, 1345, 38], [1674, 6, 1346, 4, "paddingHorizontal"], [1674, 23, 1346, 21], [1674, 25, 1346, 23], [1674, 27, 1346, 25], [1675, 6, 1347, 4, "paddingVertical"], [1675, 21, 1347, 19], [1675, 23, 1347, 21], [1675, 24, 1347, 22], [1676, 6, 1348, 4, "borderRadius"], [1676, 18, 1348, 16], [1676, 20, 1348, 18], [1677, 4, 1349, 2], [1677, 5, 1349, 3], [1678, 4, 1350, 2, "previewChipText"], [1678, 19, 1350, 17], [1678, 21, 1350, 19], [1679, 6, 1351, 4, "color"], [1679, 11, 1351, 9], [1679, 13, 1351, 11], [1679, 19, 1351, 17], [1680, 6, 1352, 4, "fontSize"], [1680, 14, 1352, 12], [1680, 16, 1352, 14], [1680, 18, 1352, 16], [1681, 6, 1353, 4, "fontWeight"], [1681, 16, 1353, 14], [1681, 18, 1353, 16], [1682, 4, 1354, 2], [1683, 2, 1355, 0], [1683, 3, 1355, 1], [1683, 4, 1355, 2], [1684, 2, 1355, 3], [1684, 6, 1355, 3, "_c"], [1684, 8, 1355, 3], [1685, 2, 1355, 3, "$RefreshReg$"], [1685, 14, 1355, 3], [1685, 15, 1355, 3, "_c"], [1685, 17, 1355, 3], [1686, 0, 1355, 3], [1686, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfmE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBa;OgBI;oFCyC;UDM;8BEW;SF0C;uDhBa;sBmBC,wBnB;OgBC;GpBe;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}