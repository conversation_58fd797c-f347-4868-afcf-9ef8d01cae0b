/**
 * Echo Camera Web Implementation
 * Server-side face blurring for web platform
 * 
 * Workflow:
 * 1. Capture unblurred photo with expo-camera
 * 2. Upload to private Supabase bucket
 * 3. Server processes and blurs faces
 * 4. Final blurred image available in public bucket
 */
import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { CameraView, useCameraPermissions, CameraType } from 'expo-camera';
import { X, Camera as CameraIcon, Upload, Shield, CheckCircle } from 'lucide-react-native';
import { BlurView } from 'expo-blur';
import BlazeFaceCanvas from './web/BlazeFaceCanvas';
// Removed TensorFlowFaceCanvas - using BlazeFaceCanvas instead
import useUpload from '@/utils/useUpload';
import * as ImageManipulator from 'expo-image-manipulator';
import { fetchChallengeCode } from '@/utils/cameraChallenge';
import { Platform } from 'react-native';
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const API_BASE_URL = (
  process.env.EXPO_PUBLIC_BASE_URL ||
  process.env.EXPO_PUBLIC_PROXY_BASE_URL ||
  process.env.EXPO_PUBLIC_HOST ||
  ''
).replace(/\/$/, '');
interface EchoCameraWebProps {
  userId: string;
  requestId: string;
  onComplete: (result: {
    imageUrl: string;
    localUri: string;
    challengeCode: string;
    timestamp: number;
    processingStatus: 'pending' | 'completed';
  }) => void;
  onCancel: () => void;
}
// Processing states for server-side blur
type ProcessingState = 'idle' | 'capturing' | 'uploading' | 'processing' | 'completed' | 'error';
export default function EchoCameraWeb({
  userId,
  requestId,
  onComplete,
  onCancel,
}: EchoCameraWebProps) {
  const cameraRef = useRef<CameraView>(null);
  const [permission, requestPermission] = useCameraPermissions();
  
  // State management
  const [processingState, setProcessingState] = useState<ProcessingState>('idle');
  const [challengeCode, setChallengeCode] = useState<string | null>(null);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  // Live preview blur overlay state
  const [previewBlurEnabled, setPreviewBlurEnabled] = useState(true);
  const [viewSize, setViewSize] = useState({ width: 0, height: 0 });
  // Camera ready state - CRITICAL for showing/hiding loading UI
  const [isCameraReady, setIsCameraReady] = useState(false);
  
  const [upload] = useUpload();
  // Fetch challenge code on mount
  useEffect(() => {
    const controller = new AbortController();
    
    (async () => {
      try {
        const code = await fetchChallengeCode({ userId, requestId, signal: controller.signal });
        setChallengeCode(code);
      } catch (e) {
        console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);
        setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);
      }
    })();
    
    return () => controller.abort();
  }, [userId, requestId]);

  // NEW: TensorFlow.js face detection
  const loadTensorFlowFaceDetection = async () => {
    console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');

    // Load TensorFlow.js
    if (!(window as any).tf) {
      await new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }

    // Load BlazeFace model
    if (!(window as any).blazeface) {
      await new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }

    console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');
  };

  const detectFacesWithTensorFlow = async (img: HTMLImageElement) => {
    try {
      const blazeface = (window as any).blazeface;
      const tf = (window as any).tf;

      console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');
      const model = await blazeface.load();
      console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');

      // Convert image to tensor
      const tensor = tf.browser.fromPixels(img);

      // Detect faces with lower confidence threshold to catch more faces
      const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9

      // Clean up tensor
      tensor.dispose();

      console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);

      // Convert predictions to our format
      const faces = predictions.map((prediction: any, index: number) => {
        const [x, y] = prediction.topLeft;
        const [x2, y2] = prediction.bottomRight;
        const width = x2 - x;
        const height = y2 - y;

        console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {
          topLeft: [x, y],
          bottomRight: [x2, y2],
          size: [width, height]
        });

        return {
          boundingBox: {
            xCenter: (x + width / 2) / img.width,
            yCenter: (y + height / 2) / img.height,
            width: width / img.width,
            height: height / img.height
          }
        };
      });

      return faces;
    } catch (error) {
      console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);
      return [];
    }
  };

  const detectFacesHeuristic = (img: HTMLImageElement, ctx: CanvasRenderingContext2D) => {
    console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');

    // Get image data for analysis
    const imageData = ctx.getImageData(0, 0, img.width, img.height);
    const data = imageData.data;

    // Improved face detection with multiple criteria
    const faces = [];
    const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision

    console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);

    for (let y = 0; y < img.height - blockSize; y += blockSize / 2) { // Overlap blocks
      for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {
        const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);

        // More sensitive face detection criteria
        if (analysis.skinRatio > 0.15 &&  // Lower skin ratio threshold
            analysis.hasVariation &&
            analysis.brightness > 0.15 &&  // Lower brightness threshold
            analysis.brightness < 0.9) {   // Higher max brightness

          faces.push({
            boundingBox: {
              xCenter: (x + blockSize / 2) / img.width,
              yCenter: (y + blockSize / 2) / img.height,
              width: (blockSize * 1.8) / img.width,
              height: (blockSize * 2.2) / img.height
            },
            confidence: analysis.skinRatio * analysis.variation
          });

          console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);
        }
      }
    }

    // Sort by confidence and merge overlapping detections
    faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));
    const mergedFaces = mergeFaceDetections(faces);

    console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);
    return mergedFaces.slice(0, 3); // Max 3 faces
  };

  const detectFacesAggressive = (img: HTMLImageElement, ctx: CanvasRenderingContext2D) => {
    console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');

    // Get image data for analysis
    const imageData = ctx.getImageData(0, 0, img.width, img.height);
    const data = imageData.data;

    const faces = [];
    const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection

    for (let y = 0; y < img.height - blockSize; y += blockSize / 2) { // More overlap
      for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {
        const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);

        // Very relaxed criteria - catch anything that might be a face
        if (analysis.skinRatio > 0.08 &&  // Very low skin ratio
            analysis.brightness > 0.1 &&  // Very low brightness threshold
            analysis.brightness < 0.95) {  // High max brightness

          faces.push({
            boundingBox: {
              xCenter: (x + blockSize / 2) / img.width,
              yCenter: (y + blockSize / 2) / img.height,
              width: blockSize / img.width,
              height: blockSize / img.height
            },
            confidence: 0.4 // Lower confidence for aggressive detection
          });
        }
      }
    }

    // Merge overlapping detections
    const mergedFaces = mergeFaceDetections(faces);
    console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);
    return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode
  };

  const analyzeRegionForFace = (data: Uint8ClampedArray, startX: number, startY: number, size: number, imageWidth: number, imageHeight: number) => {
    let skinPixels = 0;
    let totalPixels = 0;
    let totalBrightness = 0;
    let colorVariations = 0;
    let prevR = 0, prevG = 0, prevB = 0;

    for (let y = startY; y < startY + size && y < imageHeight; y++) {
      for (let x = startX; x < startX + size && x < imageWidth; x++) {
        const index = (y * imageWidth + x) * 4;
        const r = data[index];
        const g = data[index + 1];
        const b = data[index + 2];

        // Count skin pixels
        if (isSkinTone(r, g, b)) {
          skinPixels++;
        }

        // Calculate brightness
        const brightness = (r + g + b) / (3 * 255);
        totalBrightness += brightness;

        // Calculate color variation (indicates features like eyes, mouth)
        if (totalPixels > 0) {
          const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);
          if (colorDiff > 30) { // Threshold for significant color change
            colorVariations++;
          }
        }

        prevR = r; prevG = g; prevB = b;
        totalPixels++;
      }
    }

    return {
      skinRatio: skinPixels / totalPixels,
      brightness: totalBrightness / totalPixels,
      variation: colorVariations / totalPixels,
      hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation
    };
  };

  const isSkinTone = (r: number, g: number, b: number) => {
    // Simple skin tone detection algorithm
    return (
      r > 95 && g > 40 && b > 20 &&
      r > g && r > b &&
      Math.abs(r - g) > 15 &&
      Math.max(r, g, b) - Math.min(r, g, b) > 15
    );
  };

  const mergeFaceDetections = (faces: any[]) => {
    if (faces.length <= 1) return faces;

    const merged = [];
    const used = new Set();

    for (let i = 0; i < faces.length; i++) {
      if (used.has(i)) continue;

      let currentFace = faces[i];
      used.add(i);

      // Check for overlapping faces
      for (let j = i + 1; j < faces.length; j++) {
        if (used.has(j)) continue;

        const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);
        if (overlap > 0.3) { // 30% overlap threshold
          // Merge the faces by taking the larger bounding box
          currentFace = mergeTwoFaces(currentFace, faces[j]);
          used.add(j);
        }
      }

      merged.push(currentFace);
    }

    return merged;
  };

  const calculateOverlap = (box1: any, box2: any) => {
    const x1 = Math.max(box1.xCenter - box1.width/2, box2.xCenter - box2.width/2);
    const y1 = Math.max(box1.yCenter - box1.height/2, box2.yCenter - box2.height/2);
    const x2 = Math.min(box1.xCenter + box1.width/2, box2.xCenter + box2.width/2);
    const y2 = Math.min(box1.yCenter + box1.height/2, box2.yCenter + box2.height/2);

    if (x2 <= x1 || y2 <= y1) return 0;

    const overlapArea = (x2 - x1) * (y2 - y1);
    const box1Area = box1.width * box1.height;
    const box2Area = box2.width * box2.height;

    return overlapArea / Math.min(box1Area, box2Area);
  };

  const mergeTwoFaces = (face1: any, face2: any) => {
    const box1 = face1.boundingBox;
    const box2 = face2.boundingBox;

    const left = Math.min(box1.xCenter - box1.width/2, box2.xCenter - box2.width/2);
    const right = Math.max(box1.xCenter + box1.width/2, box2.xCenter + box2.width/2);
    const top = Math.min(box1.yCenter - box1.height/2, box2.yCenter - box2.height/2);
    const bottom = Math.max(box1.yCenter + box1.height/2, box2.yCenter + box2.height/2);

    return {
      boundingBox: {
        xCenter: (left + right) / 2,
        yCenter: (top + bottom) / 2,
        width: right - left,
        height: bottom - top
      }
    };
  };

  // NEW: Advanced blur functions
  const applyStrongBlur = (ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number) => {
    // Ensure coordinates are valid
    const canvasWidth = ctx.canvas.width;
    const canvasHeight = ctx.canvas.height;

    const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));
    const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));
    const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);
    const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);

    if (clampedWidth <= 0 || clampedHeight <= 0) {
      console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {
        original: { x, y, width, height },
        canvas: { width: canvasWidth, height: canvasHeight },
        clamped: { x: clampedX, y: clampedY, width: clampedWidth, height: clampedHeight }
      });
      return;
    }

    // Get the region to blur
    const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);
    const data = imageData.data;

    // Apply heavy pixelation
    const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);

    for (let py = 0; py < clampedHeight; py += pixelSize) {
      for (let px = 0; px < clampedWidth; px += pixelSize) {
        // Get average color of the block
        let r = 0, g = 0, b = 0, count = 0;

        for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {
          for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {
            const index = ((py + dy) * clampedWidth + (px + dx)) * 4;
            r += data[index];
            g += data[index + 1];
            b += data[index + 2];
            count++;
          }
        }

        if (count > 0) {
          r = Math.floor(r / count);
          g = Math.floor(g / count);
          b = Math.floor(b / count);

          // Apply averaged color to entire block
          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {
            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {
              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;
              data[index] = r;
              data[index + 1] = g;
              data[index + 2] = b;
              // Keep original alpha
            }
          }
        }
      }
    }

    // Apply additional blur passes
    for (let i = 0; i < 3; i++) {
      applySimpleBlur(data, clampedWidth, clampedHeight);
    }

    // Put the blurred data back
    ctx.putImageData(imageData, clampedX, clampedY);

    // Add an additional privacy overlay for extra security
    ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';
    ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);
  };

  const applySimpleBlur = (data: Uint8ClampedArray, width: number, height: number) => {
    const original = new Uint8ClampedArray(data);

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const index = (y * width + x) * 4;

        // Average with surrounding pixels
        let r = 0, g = 0, b = 0;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIndex = ((y + dy) * width + (x + dx)) * 4;
            r += original[neighborIndex];
            g += original[neighborIndex + 1];
            b += original[neighborIndex + 2];
          }
        }

        data[index] = r / 9;
        data[index + 1] = g / 9;
        data[index + 2] = b / 9;
      }
    }
  };

  const applyFallbackFaceBlur = (ctx: CanvasRenderingContext2D, imgWidth: number, imgHeight: number) => {
    console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');

    // Blur common face areas (center-upper region, typical selfie positions)
    const areas = [
      // Center face area
      { x: imgWidth * 0.25, y: imgHeight * 0.15, w: imgWidth * 0.5, h: imgHeight * 0.5 },
      // Left side face area
      { x: imgWidth * 0.1, y: imgHeight * 0.2, w: imgWidth * 0.35, h: imgHeight * 0.4 },
      // Right side face area
      { x: imgWidth * 0.55, y: imgHeight * 0.2, w: imgWidth * 0.35, h: imgHeight * 0.4 }
    ];

    areas.forEach((area, index) => {
      console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);
      applyStrongBlur(ctx, area.x, area.y, area.w, area.h);
    });
  };

  // Capture photo
  const capturePhoto = useCallback(async () => {
    // Development fallback for testing without camera
    const isDev = process.env.NODE_ENV === 'development' || __DEV__;
    
    if (!cameraRef.current && !isDev) {
      Alert.alert('Error', 'Camera not ready');
      return;
    }
    try {
      setProcessingState('capturing');
      setProcessingProgress(10);
      // Force live preview blur on capture for UX consistency
      // (Server-side still performs the real face blurring)
      // Small delay to ensure overlay is visible in preview at click time
      await new Promise(resolve => setTimeout(resolve, 80));
      // Capture the photo
      let photo;
      
      try {
        photo = await cameraRef.current.takePictureAsync({
          quality: 0.9,
          base64: false,
          skipProcessing: true, // Important: Get raw image for server processing
        });
      } catch (cameraError) {
        console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);
        // Development fallback - use a placeholder image
        if (isDev) {
          photo = {
            uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo',
          };
        } else {
          throw cameraError;
        }
      }
      if (!photo) {
        throw new Error('Failed to capture photo');
      }
      console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);
      setCapturedPhoto(photo.uri);
      setProcessingProgress(25);
      // Process image with client-side face blurring
      console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');
      console.log('[EchoCameraWeb] 🔍 DEBUGGING: About to call processImageWithFaceBlur with:', photo.uri);
      console.log('[EchoCameraWeb] 🔍 DEBUGGING: Function exists?', typeof processImageWithFaceBlur);
      await processImageWithFaceBlur(photo.uri);
      console.log('[EchoCameraWeb] ✅ Face blur processing completed!');
    } catch (error) {
      console.error('[EchoCameraWeb] Capture error:', error);
      setErrorMessage('Failed to capture photo. Please try again.');
      setProcessingState('error');
    }
  }, []);
  // NEW: Robust face detection and blurring system
  const processImageWithFaceBlur = async (photoUri: string) => {
    console.log('[EchoCameraWeb] 🚀 ENTRY: Starting face blur processing system...');
    try {
      console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');
      setProcessingState('processing');
      setProcessingProgress(40);

      // Create a canvas to process the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Canvas context not available');

      // Load the image
      const img = new Image();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = photoUri;
      });

      // Set canvas size to match image
      canvas.width = img.width;
      canvas.height = img.height;
      console.log('[EchoCameraWeb] 📐 Canvas setup:', { width: img.width, height: img.height });

      // Draw the original image
      ctx.drawImage(img, 0, 0);
      console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');

      setProcessingProgress(60);

      // NEW: Robust face detection with TensorFlow.js BlazeFace
      let detectedFaces = [];

      console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');

      // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)
      try {
        console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');
        await loadTensorFlowFaceDetection();
        console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');
        detectedFaces = await detectFacesWithTensorFlow(img);
        console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);
      } catch (tensorFlowError) {
        console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);
        console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {
          message: tensorFlowError.message,
          stack: tensorFlowError.stack
        });

        // Strategy 2: Use intelligent heuristic detection
        console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');
        detectedFaces = detectFacesHeuristic(img, ctx);
        console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);
      }

      // Strategy 3: If still no faces found, use aggressive detection
      if (detectedFaces.length === 0) {
        console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');
        detectedFaces = detectFacesAggressive(img, ctx);
        console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);
      }

      console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);
      if (detectedFaces.length > 0) {
        console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({
          faceNumber: i + 1,
          centerX: face.boundingBox.xCenter,
          centerY: face.boundingBox.yCenter,
          width: face.boundingBox.width,
          height: face.boundingBox.height
        })));
      } else {
        console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');
        console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');

        // Privacy-first fallback: blur the center region where faces are most likely
        const centerX = img.width * 0.3;
        const centerY = img.height * 0.2;
        const centerWidth = img.width * 0.4;
        const centerHeight = img.height * 0.6;

        detectedFaces = [{
          boundingBox: {
            xCenter: 0.5,
            yCenter: 0.5,
            width: 0.4,
            height: 0.6
          },
          confidence: 0.3
        }];

        console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');
      }

      setProcessingProgress(70);

      // NEW: Apply advanced blurring to each detected face
      if (detectedFaces.length > 0) {
        console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);

        detectedFaces.forEach((detection, index) => {
          const bbox = detection.boundingBox;

          console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {
            bbox,
            imageSize: { width: img.width, height: img.height }
          });

          // Convert normalized coordinates to pixel coordinates with generous padding
          const faceX = bbox.xCenter * img.width - (bbox.width * img.width) / 2;
          const faceY = bbox.yCenter * img.height - (bbox.height * img.height) / 2;
          const faceWidth = bbox.width * img.width;
          const faceHeight = bbox.height * img.height;

          console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {
            faceX, faceY, faceWidth, faceHeight,
            isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0
          });

          // Add generous padding around the face (50% padding)
          const padding = 0.5;
          const paddedX = Math.max(0, faceX - faceWidth * padding);
          const paddedY = Math.max(0, faceY - faceHeight * padding);
          const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));
          const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));

          console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {
            original: { x: Math.round(faceX), y: Math.round(faceY), w: Math.round(faceWidth), h: Math.round(faceHeight) },
            padded: { x: Math.round(paddedX), y: Math.round(paddedY), w: Math.round(paddedWidth), h: Math.round(paddedHeight) }
          });

          // DEBUGGING: Check canvas state before blur
          console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {
            width: canvas.width,
            height: canvas.height,
            contextValid: !!ctx
          });

          // Apply multiple blur effects for maximum privacy
          applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);

          // DEBUGGING: Check canvas state after blur
          console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);

          // Verify blur was applied by checking a few pixels
          const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);
          console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {
            firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],
            secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]
          });

          console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);
        });

        console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);
      } else {
        console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');
        // Apply fallback blur to common face areas
        applyFallbackFaceBlur(ctx, img.width, img.height);
      }

      setProcessingProgress(90);

      // Convert canvas to blob and create URL
      console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');
      const blurredImageBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/jpeg', 0.9);
      });

      const blurredImageUrl = URL.createObjectURL(blurredImageBlob);
      console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');

      // CRITICAL: Update the captured photo state with the blurred version
      setCapturedPhoto(blurredImageUrl);
      console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');

      setProcessingProgress(100);

      // Complete the processing
      await completeProcessing(blurredImageUrl);

    } catch (error) {
      console.error('[EchoCameraWeb] 🚨 CRITICAL ERROR in processImageWithFaceBlur:', error);
      console.error('[EchoCameraWeb] 🚨 Error stack:', error.stack);
      console.error('[EchoCameraWeb] 🚨 Error details:', {
        name: error.name,
        message: error.message,
        photoUri
      });
      setErrorMessage('Failed to process photo.');
      setProcessingState('error');
    }
  };

  // Complete processing with blurred image
  const completeProcessing = async (blurredImageUrl: string) => {
    try {
      setProcessingState('complete');

      // Generate a mock job result
      const timestamp = Date.now();
      const result = {
        imageUrl: blurredImageUrl,
        localUri: blurredImageUrl,
        challengeCode: challengeCode || '',
        timestamp,
        jobId: `client-${timestamp}`,
        status: 'completed'
      };

      console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {
        imageUrl: blurredImageUrl.substring(0, 50) + '...',
        timestamp,
        jobId: result.jobId
      });

      // Call the completion callback
      onComplete(result);

    } catch (error) {
      console.error('[EchoCameraWeb] Completion error:', error);
      setErrorMessage('Failed to complete processing.');
      setProcessingState('error');
    }
  };

  // Trigger server-side face blurring (now unused but kept for compatibility)
  const triggerServerProcessing = async (privateImageUrl: string, timestamp: number) => {
    try {
      console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);
      setProcessingState('processing');
      setProcessingProgress(70);

      const requestBody = {
        imageUrl: privateImageUrl,
        userId,
        requestId,
        timestamp,
        platform: 'web',
      };

      console.log('[EchoCameraWeb] Sending processing request:', requestBody);

      // Call backend API to process image
      const response = await fetch(`${API_BASE_URL}/api/process-image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await getAuthToken()}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);
        throw new Error(`Processing failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('[EchoCameraWeb] Processing request successful:', result);

      if (!result.jobId) {
        throw new Error('No job ID returned from processing request');
      }

      // Poll for processing completion
      await pollForCompletion(result.jobId, timestamp);
    } catch (error) {
      console.error('[EchoCameraWeb] Processing error:', error);
      setErrorMessage(`Failed to process image: ${error.message}`);
      setProcessingState('error');
    }
  };
  // Poll for server-side processing completion
  const pollForCompletion = async (jobId: string, timestamp: number, attempts = 0) => {
    const MAX_ATTEMPTS = 30; // 30 seconds max
    const POLL_INTERVAL = 1000; // 1 second

    console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);

    if (attempts >= MAX_ATTEMPTS) {
      console.error('[EchoCameraWeb] Processing timeout after 30 seconds');
      setErrorMessage('Processing timeout. Please try again.');
      setProcessingState('error');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${await getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const status = await response.json();
      console.log(`[EchoCameraWeb] Status response:`, status);

      if (status.status === 'completed') {
        console.log('[EchoCameraWeb] Processing completed successfully');
        setProcessingProgress(100);
        setProcessingState('completed');
        // Return the processed image URL
        const result = {
          imageUrl: status.publicUrl, // Blurred image in public bucket
          localUri: capturedPhoto || status.publicUrl, // Fallback to publicUrl if no localUri
          challengeCode: challengeCode || '',
          timestamp,
          processingStatus: 'completed',
        };
        console.log('[EchoCameraWeb] Returning result:', result);
        onComplete(result);
        // Removed redundant Alert - parent component will handle UI update
      } else if (status.status === 'failed') {
        console.error('[EchoCameraWeb] Processing failed:', status.error);
        throw new Error(status.error || 'Processing failed');
      } else {
        // Still processing, update progress and poll again
        const progressValue = 70 + (attempts / MAX_ATTEMPTS) * 25;
        console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);
        setProcessingProgress(progressValue);

        setTimeout(() => {
          pollForCompletion(jobId, timestamp, attempts + 1);
        }, POLL_INTERVAL);
      }
    } catch (error) {
      console.error('[EchoCameraWeb] Polling error:', error);
      setErrorMessage(`Failed to check processing status: ${error.message}`);
      setProcessingState('error');
    }
  };
  // Get auth token helper
  const getAuthToken = async (): Promise<string> => {
    // Implement based on your auth system
    // This is a placeholder
    return 'user-auth-token';
  };

  // Retry mechanism for failed operations
  const retryCapture = useCallback(() => {
    console.log('[EchoCameraWeb] Retrying capture...');
    setProcessingState('idle');
    setErrorMessage('');
    setCapturedPhoto('');
    setProcessingProgress(0);
  }, []);
  // Debug logging
  useEffect(() => {
    console.log('[EchoCameraWeb] Permission state:', permission);
    if (permission) {
      console.log('[EchoCameraWeb] Permission granted:', permission.granted);
    }
  }, [permission]);
  // Handle permission states
  if (!permission) {
    console.log('[EchoCameraWeb] Waiting for permission state...');
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text style={styles.loadingText}>Loading camera...</Text>
      </View>
    );
  }
  if (!permission.granted) {
    console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');
    return (
      <View style={styles.container}>
        <View style={styles.permissionContent}>
          <CameraIcon size={64} color="#3B82F6" />
          <Text style={styles.permissionTitle}>Camera Permission Required</Text>
          <Text style={styles.permissionDescription}>
            Echo needs camera access to capture photos. Your privacy is protected - 
            faces will be automatically blurred after capture.
          </Text>
          <TouchableOpacity onPress={requestPermission} style={styles.primaryButton}>
            <Text style={styles.primaryButtonText}>Grant Permission</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={onCancel} style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  // Main camera UI with processing states
  console.log('[EchoCameraWeb] Rendering camera view');
  
  return (
    <View style={styles.container}>
      {/* Camera View */}
      <View style={styles.cameraContainer} id="echo-web-camera">
        <CameraView 
          ref={cameraRef}
          style={[styles.camera, { backgroundColor: '#1a1a1a' }]}
          facing="back"
          onLayout={(e) => {
            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);
            setViewSize({ width: e.nativeEvent.layout.width, height: e.nativeEvent.layout.height });
          }}
          onCameraReady={() => {
            console.log('[EchoCameraWeb] Camera ready!');
            setIsCameraReady(true); // CRITICAL: Update state when camera is ready
          }}
          onMountError={(error) => {
            console.error('[EchoCameraWeb] Camera mount error:', error);
            setErrorMessage('Camera failed to initialize');
            setProcessingState('error');
          }}
        />
        {/* Loading overlay - Show ONLY when camera is not ready */}
        {!isCameraReady && (
          <View style={[StyleSheet.absoluteFill, { backgroundColor: 'rgba(0, 0, 0, 0.8)', justifyContent: 'center', alignItems: 'center', zIndex: 1000 }]}>
            <View style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)', padding: 24, borderRadius: 16, alignItems: 'center' }}>
              <ActivityIndicator size="large" color="#3B82F6" style={{ marginBottom: 16 }} />
              <Text style={{ color: '#fff', fontSize: 18, fontWeight: '600' }}>Initializing Camera...</Text>
              <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 8 }}>Please wait</Text>
            </View>
          </View>
        )}
        
        {/* Live Preview Blur Overlay - Show only when camera is ready */}
        {isCameraReady && previewBlurEnabled && viewSize.width > 0 && (
          <>
            {/* BlazeFace detector overlay with accurate coordinate mapping */}
            <BlazeFaceCanvas containerId="echo-web-camera" width={viewSize.width} height={viewSize.height} />
            <View style={[StyleSheet.absoluteFill, { pointerEvents: 'none' }]}>
              {/* Light fallback coverage zone (reduced since we have dynamic detection) */}
            <BlurView intensity={15} tint="dark" style={[styles.blurZone, {
              left: 0,
              top: viewSize.height * 0.2,
              width: viewSize.width,
              height: viewSize.height * 0.6,
              borderRadius: 20,
              opacity: 0.4, // Reduced opacity since we have dynamic detection
            }]} />
            {/* Light circular zones for typical face spots (reduced intensity) */}
            <BlurView intensity={10} tint="dark" style={[styles.blurZone, {
              left: viewSize.width * 0.5 - (viewSize.width * 0.35),
              top: viewSize.height * 0.35 - (viewSize.width * 0.35),
              width: viewSize.width * 0.7,
              height: viewSize.width * 0.7,
              borderRadius: (viewSize.width * 0.7) / 2,
              opacity: 0.3,
            }]} />
            <BlurView intensity={10} tint="dark" style={[styles.blurZone, {
              left: viewSize.width * 0.2 - (viewSize.width * 0.25),
              top: viewSize.height * 0.4 - (viewSize.width * 0.25),
              width: viewSize.width * 0.5,
              height: viewSize.width * 0.5,
              borderRadius: (viewSize.width * 0.5) / 2,
              opacity: 0.3,
            }]} />
            <BlurView intensity={10} tint="dark" style={[styles.blurZone, {
              left: viewSize.width * 0.8 - (viewSize.width * 0.25),
              top: viewSize.height * 0.4 - (viewSize.width * 0.25),
              width: viewSize.width * 0.5,
              height: viewSize.width * 0.5,
              borderRadius: (viewSize.width * 0.5) / 2,
              opacity: 0.3,
            }]} />
            {/* Status chip */}
            {__DEV__ && (
              <View style={styles.previewChip}>
                <Text style={styles.previewChipText}>Live Privacy Preview</Text>
              </View>
            )}
            </View>
          </>
        )}
        {/* UI Controls - Show only when camera is ready */}
        {isCameraReady && (
          <>
            {/* Header Overlay */}
            <View style={styles.headerOverlay}>
              <View style={styles.headerContent}>
                <View style={styles.headerLeft}>
                  <Text style={styles.headerTitle}>Echo Camera</Text>
                  <View style={styles.subtitleRow}>
                    <Text style={styles.webIcon}>??</Text>
                    <Text style={styles.headerSubtitle}>Web Camera Mode</Text>
                  </View>
                  {challengeCode && (
                    <View style={styles.challengeRow}>
                      <Shield size={14} color="#fff" />
                      <Text style={styles.challengeCode}>{challengeCode}</Text>
                    </View>
                  )}
                </View>
                <TouchableOpacity onPress={onCancel} style={styles.closeButton}>
                  <X size={24} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
            {/* Privacy Notice */}
            <View style={styles.privacyNotice}>
              <Shield size={16} color="#3B82F6" />
              <Text style={styles.privacyText}>
                For your privacy, faces will be automatically blurred after upload
              </Text>
            </View>
            {/* Footer Controls */}
            <View style={styles.footerOverlay}>
              <Text style={styles.instruction}>
                Center the subject and click to capture
              </Text>
              
              <TouchableOpacity
                onPress={capturePhoto}
                disabled={processingState !== 'idle' || !isCameraReady}
                style={[
                  styles.shutterButton,
                  processingState !== 'idle' && styles.shutterButtonDisabled
                ]}
              >
                {processingState === 'idle' ? (
                  <View style={styles.shutterInner} />
                ) : (
                  <ActivityIndicator size="small" color="#3B82F6" />
                )}
              </TouchableOpacity>
              <Text style={styles.privacyNote}>
                ?? Server-side privacy protection
              </Text>
            </View>
          </>
        )}
      </View>
      {/* Processing Modal */}
      <Modal
        visible={processingState !== 'idle' && processingState !== 'error'}
        transparent
        animationType="fade"
      >
        <View style={styles.processingModal}>
          <View style={styles.processingContent}>
            <ActivityIndicator size="large" color="#3B82F6" />
            
            <Text style={styles.processingTitle}>
              {processingState === 'capturing' && 'Capturing Photo...'}
              {processingState === 'uploading' && 'Uploading for Processing...'}
              {processingState === 'processing' && 'Applying Privacy Protection...'}
              {processingState === 'completed' && 'Processing Complete!'}
            </Text>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${processingProgress}%` }
                ]} 
              />
            </View>
            <Text style={styles.processingDescription}>
              {processingState === 'capturing' && 'Getting your photo ready...'}
              {processingState === 'uploading' && 'Securely uploading to our servers...'}
              {processingState === 'processing' && 'Detecting and blurring faces for privacy...'}
              {processingState === 'completed' && 'Your photo is ready with faces blurred!'}
            </Text>
            {processingState === 'completed' && (
              <CheckCircle size={48} color="#10B981" style={styles.successIcon} />
            )}
          </View>
        </View>
      </Modal>
      {/* Error Modal */}
      <Modal
        visible={processingState === 'error'}
        transparent
        animationType="fade"
      >
        <View style={styles.processingModal}>
          <View style={styles.errorContent}>
            <X size={48} color="#EF4444" />
            <Text style={styles.errorTitle}>Processing Failed</Text>
            <Text style={styles.errorMessage}>{errorMessage}</Text>
            <TouchableOpacity
              onPress={retryCapture}
              style={styles.primaryButton}
            >
              <Text style={styles.primaryButtonText}>Try Again</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onCancel}
              style={styles.secondaryButton}
            >
              <Text style={styles.secondaryButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  cameraContainer: {
    flex: 1,
    maxWidth: 600,
    alignSelf: 'center',
    width: '100%',
  },
  camera: {
    flex: 1,
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 4,
  },
  subtitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  webIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
  challengeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  challengeCode: {
    fontSize: 12,
    color: '#fff',
    marginLeft: 6,
    fontFamily: 'monospace',
  },
  closeButton: {
    padding: 8,
  },
  privacyNotice: {
    position: 'absolute',
    top: 140,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  privacyText: {
    color: '#fff',
    fontSize: 13,
    marginLeft: 8,
    flex: 1,
  },
  footerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    paddingBottom: 40,
    paddingTop: 30,
    alignItems: 'center',
  },
  instruction: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 20,
  },
  shutterButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#3B82F6',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.5,
        shadowRadius: 20,
      },
      android: {
        elevation: 10,
      },
      web: {
        boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)',
      },
    }),
  },
  shutterButtonDisabled: {
    opacity: 0.5,
  },
  shutterInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#fff',
    borderWidth: 3,
    borderColor: '#3B82F6',
  },
  privacyNote: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  processingModal: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 32,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
  },
  processingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 20,
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 16,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 3,
  },
  processingDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  successIcon: {
    marginTop: 16,
  },
  errorContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 32,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#EF4444',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  primaryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    marginTop: 8,
  },
  secondaryButtonText: {
    color: '#6B7280',
    fontSize: 16,
  },
  permissionContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  permissionDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  loadingText: {
    color: '#6B7280',
    marginTop: 16,
  },
  // Live blur overlay
  blurZone: {
    position: 'absolute',
    overflow: 'hidden',
  },
  previewChip: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  previewChipText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: '600',
  },
});

