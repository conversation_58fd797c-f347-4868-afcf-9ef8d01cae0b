{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "../ExpoCameraManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 46}, "end": {"line": 2, "column": 49, "index": 95}}], "key": "VKPG+vaOqe/dc4mXp87MIqCzsmc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ConversionTables = void 0;\n  exports.convertNativeProps = convertNativeProps;\n  exports.ensureNativeProps = ensureNativeProps;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _ExpoCameraManager = _interopRequireDefault(require(_dependencyMap[2], \"../ExpoCameraManager\"));\n  // Values under keys from this object will be transformed to native options\n  const ConversionTables = exports.ConversionTables = {\n    type: _ExpoCameraManager.default.Type,\n    flash: _ExpoCameraManager.default.FlashMode\n  };\n  function convertNativeProps(props) {\n    if (!props || typeof props !== 'object') {\n      return {};\n    }\n    const nativeProps = {};\n    for (const [key, value] of Object.entries(props)) {\n      const prop = key;\n      if (typeof value === 'string' && ConversionTables[prop]) {\n        nativeProps[key] = ConversionTables[prop][value];\n      } else {\n        nativeProps[key] = value;\n      }\n    }\n    return nativeProps;\n  }\n  function ensureNativeProps(props) {\n    const newProps = convertNativeProps(props);\n    newProps.barcodeScannerEnabled = !!props?.onBarcodeScanned;\n    newProps.flashMode = props?.flash ?? 'off';\n    newProps.mute = props?.mute ?? false;\n    newProps.autoFocus = props?.autofocus ?? 'off';\n    if (_expoModulesCore.Platform.OS !== 'web') {\n      delete newProps.poster;\n    }\n    return newProps;\n  }\n});", "lineCount": 42, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_expoModulesCore"], [9, 22, 1, 0], [9, 25, 1, 0, "require"], [9, 32, 1, 0], [9, 33, 1, 0, "_dependencyMap"], [9, 47, 1, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_ExpoCameraManager"], [10, 24, 2, 0], [10, 27, 2, 0, "_interopRequireDefault"], [10, 49, 2, 0], [10, 50, 2, 0, "require"], [10, 57, 2, 0], [10, 58, 2, 0, "_dependencyMap"], [10, 72, 2, 0], [11, 2, 3, 0], [12, 2, 4, 7], [12, 8, 4, 13, "ConversionTables"], [12, 24, 4, 29], [12, 27, 4, 29, "exports"], [12, 34, 4, 29], [12, 35, 4, 29, "ConversionTables"], [12, 51, 4, 29], [12, 54, 4, 32], [13, 4, 5, 4, "type"], [13, 8, 5, 8], [13, 10, 5, 10, "CameraManager"], [13, 36, 5, 23], [13, 37, 5, 24, "Type"], [13, 41, 5, 28], [14, 4, 6, 4, "flash"], [14, 9, 6, 9], [14, 11, 6, 11, "CameraManager"], [14, 37, 6, 24], [14, 38, 6, 25, "FlashMode"], [15, 2, 7, 0], [15, 3, 7, 1], [16, 2, 8, 7], [16, 11, 8, 16, "convertNativeProps"], [16, 29, 8, 34, "convertNativeProps"], [16, 30, 8, 35, "props"], [16, 35, 8, 40], [16, 37, 8, 42], [17, 4, 9, 4], [17, 8, 9, 8], [17, 9, 9, 9, "props"], [17, 14, 9, 14], [17, 18, 9, 18], [17, 25, 9, 25, "props"], [17, 30, 9, 30], [17, 35, 9, 35], [17, 43, 9, 43], [17, 45, 9, 45], [18, 6, 10, 8], [18, 13, 10, 15], [18, 14, 10, 16], [18, 15, 10, 17], [19, 4, 11, 4], [20, 4, 12, 4], [20, 10, 12, 10, "nativeProps"], [20, 21, 12, 21], [20, 24, 12, 24], [20, 25, 12, 25], [20, 26, 12, 26], [21, 4, 13, 4], [21, 9, 13, 9], [21, 15, 13, 15], [21, 16, 13, 16, "key"], [21, 19, 13, 19], [21, 21, 13, 21, "value"], [21, 26, 13, 26], [21, 27, 13, 27], [21, 31, 13, 31, "Object"], [21, 37, 13, 37], [21, 38, 13, 38, "entries"], [21, 45, 13, 45], [21, 46, 13, 46, "props"], [21, 51, 13, 51], [21, 52, 13, 52], [21, 54, 13, 54], [22, 6, 14, 8], [22, 12, 14, 14, "prop"], [22, 16, 14, 18], [22, 19, 14, 21, "key"], [22, 22, 14, 24], [23, 6, 15, 8], [23, 10, 15, 12], [23, 17, 15, 19, "value"], [23, 22, 15, 24], [23, 27, 15, 29], [23, 35, 15, 37], [23, 39, 15, 41, "ConversionTables"], [23, 55, 15, 57], [23, 56, 15, 58, "prop"], [23, 60, 15, 62], [23, 61, 15, 63], [23, 63, 15, 65], [24, 8, 16, 12, "nativeProps"], [24, 19, 16, 23], [24, 20, 16, 24, "key"], [24, 23, 16, 27], [24, 24, 16, 28], [24, 27, 17, 16, "ConversionTables"], [24, 43, 17, 32], [24, 44, 17, 33, "prop"], [24, 48, 17, 37], [24, 49, 17, 38], [24, 50, 17, 39, "value"], [24, 55, 17, 44], [24, 56, 17, 45], [25, 6, 18, 8], [25, 7, 18, 9], [25, 13, 19, 13], [26, 8, 20, 12, "nativeProps"], [26, 19, 20, 23], [26, 20, 20, 24, "key"], [26, 23, 20, 27], [26, 24, 20, 28], [26, 27, 20, 31, "value"], [26, 32, 20, 36], [27, 6, 21, 8], [28, 4, 22, 4], [29, 4, 23, 4], [29, 11, 23, 11, "nativeProps"], [29, 22, 23, 22], [30, 2, 24, 0], [31, 2, 25, 7], [31, 11, 25, 16, "ensureNativeProps"], [31, 28, 25, 33, "ensureNativeProps"], [31, 29, 25, 34, "props"], [31, 34, 25, 39], [31, 36, 25, 41], [32, 4, 26, 4], [32, 10, 26, 10, "newProps"], [32, 18, 26, 18], [32, 21, 26, 21, "convertNativeProps"], [32, 39, 26, 39], [32, 40, 26, 40, "props"], [32, 45, 26, 45], [32, 46, 26, 46], [33, 4, 27, 4, "newProps"], [33, 12, 27, 12], [33, 13, 27, 13, "barcodeScannerEnabled"], [33, 34, 27, 34], [33, 37, 27, 37], [33, 38, 27, 38], [33, 39, 27, 39, "props"], [33, 44, 27, 44], [33, 46, 27, 46, "onBarcodeScanned"], [33, 62, 27, 62], [34, 4, 28, 4, "newProps"], [34, 12, 28, 12], [34, 13, 28, 13, "flashMode"], [34, 22, 28, 22], [34, 25, 28, 25, "props"], [34, 30, 28, 30], [34, 32, 28, 32, "flash"], [34, 37, 28, 37], [34, 41, 28, 41], [34, 46, 28, 46], [35, 4, 29, 4, "newProps"], [35, 12, 29, 12], [35, 13, 29, 13, "mute"], [35, 17, 29, 17], [35, 20, 29, 20, "props"], [35, 25, 29, 25], [35, 27, 29, 27, "mute"], [35, 31, 29, 31], [35, 35, 29, 35], [35, 40, 29, 40], [36, 4, 30, 4, "newProps"], [36, 12, 30, 12], [36, 13, 30, 13, "autoFocus"], [36, 22, 30, 22], [36, 25, 30, 25, "props"], [36, 30, 30, 30], [36, 32, 30, 32, "autofocus"], [36, 41, 30, 41], [36, 45, 30, 45], [36, 50, 30, 50], [37, 4, 31, 4], [37, 8, 31, 8, "Platform"], [37, 33, 31, 16], [37, 34, 31, 17, "OS"], [37, 36, 31, 19], [37, 41, 31, 24], [37, 46, 31, 29], [37, 48, 31, 31], [38, 6, 32, 8], [38, 13, 32, 15, "newProps"], [38, 21, 32, 23], [38, 22, 32, 24, "poster"], [38, 28, 32, 30], [39, 4, 33, 4], [40, 4, 34, 4], [40, 11, 34, 11, "newProps"], [40, 19, 34, 19], [41, 2, 35, 0], [42, 0, 35, 1], [42, 3]], "functionMap": {"names": ["<global>", "convertNativeProps", "ensureNativeProps"], "mappings": "AAA;OCO;CDgB;OEC;CFU"}}, "type": "js/module"}]}