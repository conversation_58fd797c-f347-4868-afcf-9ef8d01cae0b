{"dependencies": [{"name": "../renderer/typeddash", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 48, "index": 48}}], "key": "vgGalf47jbvzGv0xbcZeWzlKG3c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.materialize = exports.isSharedValue = exports.composeDeclarations = void 0;\n  var _typeddash = require(_dependencyMap[0], \"../renderer/typeddash\");\n  const _worklet_13700093922265_init_data = {\n    code: \"function utilsJs1(value){return(value===null||value===void 0?void 0:value._isReanimatedSharedValue)===true;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\utils.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"utilsJs1\\\",\\\"value\\\",\\\"_isReanimatedSharedValue\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/utils.js\\\"],\\\"mappings\\\":\\\"AAC6B,SAAAA,QAAKA,CAAIC,KAAA,EAIpC,MAAO,CAACA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAK,EAAC,CAAG,IAAK,EAAC,CAAGA,KAAK,CAACC,wBAAwB,IAAM,IAAI,CAChG\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const isSharedValue = exports.isSharedValue = function () {\n    const _e = [new global.Error(), 1, -27];\n    const utilsJs1 = function (value) {\n      // We cannot use `in` operator here because `value` could be a HostObject and therefore we cast.\n      return (value === null || value === void 0 ? void 0 : value._isReanimatedSharedValue) === true;\n    };\n    utilsJs1.__closure = {};\n    utilsJs1.__workletHash = 13700093922265;\n    utilsJs1.__initData = _worklet_13700093922265_init_data;\n    utilsJs1.__stackDetails = _e;\n    return utilsJs1;\n  }();\n  const _worklet_2879236495669_init_data = {\n    code: \"function utilsJs2(props){const{mapKeys,isSharedValue}=this.__closure;const result=Object.assign({},props);mapKeys(result).forEach(function(key){const value=result[key];if(isSharedValue(value)){result[key]=value.value;}});return result;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\utils.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"utilsJs2\\\",\\\"props\\\",\\\"mapKeys\\\",\\\"isSharedValue\\\",\\\"__closure\\\",\\\"result\\\",\\\"Object\\\",\\\"assign\\\",\\\"forEach\\\",\\\"key\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/utils.js\\\"],\\\"mappings\\\":\\\"AAO2B,SAAAA,QAAKA,CAAIC,KAAA,QAAAC,OAAA,CAAAC,aAAA,OAAAC,SAAA,CAGlC,KAAM,CAAAC,MAAM,CAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAEN,KAAK,CAAC,CACvCC,OAAO,CAACG,MAAM,CAAC,CAACG,OAAO,CAAC,SAAAC,GAAG,CAAI,CAC7B,KAAM,CAAAC,KAAK,CAAGL,MAAM,CAACI,GAAG,CAAC,CACzB,GAAIN,aAAa,CAACO,KAAK,CAAC,CAAE,CACxBL,MAAM,CAACI,GAAG,CAAC,CAAGC,KAAK,CAACA,KAAK,CAC3B,CACF,CAAC,CAAC,CACF,MAAO,CAAAL,MAAM,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const materialize = exports.materialize = function () {\n    const _e = [new global.Error(), -3, -27];\n    const utilsJs2 = function (props) {\n      const result = Object.assign({}, props);\n      (0, _typeddash.mapKeys)(result).forEach(key => {\n        const value = result[key];\n        if (isSharedValue(value)) {\n          result[key] = value.value;\n        }\n      });\n      return result;\n    };\n    utilsJs2.__closure = {\n      mapKeys: _typeddash.mapKeys,\n      isSharedValue\n    };\n    utilsJs2.__workletHash = 2879236495669;\n    utilsJs2.__initData = _worklet_2879236495669_init_data;\n    utilsJs2.__stackDetails = _e;\n    return utilsJs2;\n  }();\n  const _worklet_5389924226996_init_data = {\n    code: \"function utilsJs3(filters,composer){const len=filters.length;if(len<=1){return filters[0];}return filters.reduceRight(function(inner,outer){return inner?composer(outer,inner):outer;});}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\utils.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"utilsJs3\\\",\\\"filters\\\",\\\"composer\\\",\\\"len\\\",\\\"length\\\",\\\"reduceRight\\\",\\\"inner\\\",\\\"outer\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/utils.js\\\"],\\\"mappings\\\":\\\"AAmBmC,QAAC,CAAAA,SAAOC,OAAE,CAAAC,QAAa,EAGxD,KAAM,CAAAC,GAAG,CAAGF,OAAO,CAACG,MAAM,CAC1B,GAAID,GAAG,EAAI,CAAC,CAAE,CACZ,MAAO,CAAAF,OAAO,CAAC,CAAC,CAAC,CACnB,CACA,MAAO,CAAAA,OAAO,CAACI,WAAW,CAAC,SAACC,KAAK,CAAEC,KAAK,QAAK,CAAAD,KAAK,CAAGJ,QAAQ,CAACK,KAAK,CAAED,KAAK,CAAC,CAAGC,KAAK,GAAC,CACtF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const composeDeclarations = exports.composeDeclarations = function () {\n    const _e = [new global.Error(), 1, -27];\n    const utilsJs3 = function (filters, composer) {\n      const len = filters.length;\n      if (len <= 1) {\n        return filters[0];\n      }\n      return filters.reduceRight((inner, outer) => inner ? composer(outer, inner) : outer);\n    };\n    utilsJs3.__closure = {};\n    utilsJs3.__workletHash = 5389924226996;\n    utilsJs3.__initData = _worklet_5389924226996_init_data;\n    utilsJs3.__stackDetails = _e;\n    return utilsJs3;\n  }();\n});", "lineCount": 73, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_typeddash"], [6, 16, 1, 0], [6, 19, 1, 0, "require"], [6, 26, 1, 0], [6, 27, 1, 0, "_dependencyMap"], [6, 41, 1, 0], [7, 2, 1, 48], [7, 8, 1, 48, "_worklet_13700093922265_init_data"], [7, 41, 1, 48], [8, 4, 1, 48, "code"], [8, 8, 1, 48], [9, 4, 1, 48, "location"], [9, 12, 1, 48], [10, 4, 1, 48, "sourceMap"], [10, 13, 1, 48], [11, 4, 1, 48, "version"], [11, 11, 1, 48], [12, 2, 1, 48], [13, 2, 2, 7], [13, 8, 2, 13, "isSharedValue"], [13, 21, 2, 26], [13, 24, 2, 26, "exports"], [13, 31, 2, 26], [13, 32, 2, 26, "isSharedValue"], [13, 45, 2, 26], [13, 48, 2, 29], [14, 4, 2, 29], [14, 10, 2, 29, "_e"], [14, 12, 2, 29], [14, 20, 2, 29, "global"], [14, 26, 2, 29], [14, 27, 2, 29, "Error"], [14, 32, 2, 29], [15, 4, 2, 29], [15, 10, 2, 29, "utilsJs1"], [15, 18, 2, 29], [15, 30, 2, 29, "utilsJs1"], [15, 31, 2, 29, "value"], [15, 36, 2, 34], [15, 38, 2, 38], [16, 6, 5, 2], [17, 6, 6, 2], [17, 13, 6, 9], [17, 14, 6, 10, "value"], [17, 19, 6, 15], [17, 24, 6, 20], [17, 28, 6, 24], [17, 32, 6, 28, "value"], [17, 37, 6, 33], [17, 42, 6, 38], [17, 47, 6, 43], [17, 48, 6, 44], [17, 51, 6, 47], [17, 56, 6, 52], [17, 57, 6, 53], [17, 60, 6, 56, "value"], [17, 65, 6, 61], [17, 66, 6, 62, "_isReanimatedSharedValue"], [17, 90, 6, 86], [17, 96, 6, 92], [17, 100, 6, 96], [18, 4, 7, 0], [18, 5, 7, 1], [19, 4, 7, 1, "utilsJs1"], [19, 12, 7, 1], [19, 13, 7, 1, "__closure"], [19, 22, 7, 1], [20, 4, 7, 1, "utilsJs1"], [20, 12, 7, 1], [20, 13, 7, 1, "__workletHash"], [20, 26, 7, 1], [21, 4, 7, 1, "utilsJs1"], [21, 12, 7, 1], [21, 13, 7, 1, "__initData"], [21, 23, 7, 1], [21, 26, 7, 1, "_worklet_13700093922265_init_data"], [21, 59, 7, 1], [22, 4, 7, 1, "utilsJs1"], [22, 12, 7, 1], [22, 13, 7, 1, "__stackDetails"], [22, 27, 7, 1], [22, 30, 7, 1, "_e"], [22, 32, 7, 1], [23, 4, 7, 1], [23, 11, 7, 1, "utilsJs1"], [23, 19, 7, 1], [24, 2, 7, 1], [24, 3, 2, 29], [24, 5, 7, 1], [25, 2, 7, 2], [25, 8, 7, 2, "_worklet_2879236495669_init_data"], [25, 40, 7, 2], [26, 4, 7, 2, "code"], [26, 8, 7, 2], [27, 4, 7, 2, "location"], [27, 12, 7, 2], [28, 4, 7, 2, "sourceMap"], [28, 13, 7, 2], [29, 4, 7, 2, "version"], [29, 11, 7, 2], [30, 2, 7, 2], [31, 2, 8, 7], [31, 8, 8, 13, "materialize"], [31, 19, 8, 24], [31, 22, 8, 24, "exports"], [31, 29, 8, 24], [31, 30, 8, 24, "materialize"], [31, 41, 8, 24], [31, 44, 8, 27], [32, 4, 8, 27], [32, 10, 8, 27, "_e"], [32, 12, 8, 27], [32, 20, 8, 27, "global"], [32, 26, 8, 27], [32, 27, 8, 27, "Error"], [32, 32, 8, 27], [33, 4, 8, 27], [33, 10, 8, 27, "utilsJs2"], [33, 18, 8, 27], [33, 30, 8, 27, "utilsJs2"], [33, 31, 8, 27, "props"], [33, 36, 8, 32], [33, 38, 8, 36], [34, 6, 11, 2], [34, 12, 11, 8, "result"], [34, 18, 11, 14], [34, 21, 11, 17, "Object"], [34, 27, 11, 23], [34, 28, 11, 24, "assign"], [34, 34, 11, 30], [34, 35, 11, 31], [34, 36, 11, 32], [34, 37, 11, 33], [34, 39, 11, 35, "props"], [34, 44, 11, 40], [34, 45, 11, 41], [35, 6, 12, 2], [35, 10, 12, 2, "mapKeys"], [35, 28, 12, 9], [35, 30, 12, 10, "result"], [35, 36, 12, 16], [35, 37, 12, 17], [35, 38, 12, 18, "for<PERSON>ach"], [35, 45, 12, 25], [35, 46, 12, 26, "key"], [35, 49, 12, 29], [35, 53, 12, 33], [36, 8, 13, 4], [36, 14, 13, 10, "value"], [36, 19, 13, 15], [36, 22, 13, 18, "result"], [36, 28, 13, 24], [36, 29, 13, 25, "key"], [36, 32, 13, 28], [36, 33, 13, 29], [37, 8, 14, 4], [37, 12, 14, 8, "isSharedValue"], [37, 25, 14, 21], [37, 26, 14, 22, "value"], [37, 31, 14, 27], [37, 32, 14, 28], [37, 34, 14, 30], [38, 10, 15, 6, "result"], [38, 16, 15, 12], [38, 17, 15, 13, "key"], [38, 20, 15, 16], [38, 21, 15, 17], [38, 24, 15, 20, "value"], [38, 29, 15, 25], [38, 30, 15, 26, "value"], [38, 35, 15, 31], [39, 8, 16, 4], [40, 6, 17, 2], [40, 7, 17, 3], [40, 8, 17, 4], [41, 6, 18, 2], [41, 13, 18, 9, "result"], [41, 19, 18, 15], [42, 4, 19, 0], [42, 5, 19, 1], [43, 4, 19, 1, "utilsJs2"], [43, 12, 19, 1], [43, 13, 19, 1, "__closure"], [43, 22, 19, 1], [44, 6, 19, 1, "mapKeys"], [44, 13, 19, 1], [44, 15, 12, 2, "mapKeys"], [44, 33, 12, 9], [45, 6, 12, 9, "isSharedValue"], [46, 4, 12, 9], [47, 4, 12, 9, "utilsJs2"], [47, 12, 12, 9], [47, 13, 12, 9, "__workletHash"], [47, 26, 12, 9], [48, 4, 12, 9, "utilsJs2"], [48, 12, 12, 9], [48, 13, 12, 9, "__initData"], [48, 23, 12, 9], [48, 26, 12, 9, "_worklet_2879236495669_init_data"], [48, 58, 12, 9], [49, 4, 12, 9, "utilsJs2"], [49, 12, 12, 9], [49, 13, 12, 9, "__stackDetails"], [49, 27, 12, 9], [49, 30, 12, 9, "_e"], [49, 32, 12, 9], [50, 4, 12, 9], [50, 11, 12, 9, "utilsJs2"], [50, 19, 12, 9], [51, 2, 12, 9], [51, 3, 8, 27], [51, 5, 19, 1], [52, 2, 19, 2], [52, 8, 19, 2, "_worklet_5389924226996_init_data"], [52, 40, 19, 2], [53, 4, 19, 2, "code"], [53, 8, 19, 2], [54, 4, 19, 2, "location"], [54, 12, 19, 2], [55, 4, 19, 2, "sourceMap"], [55, 13, 19, 2], [56, 4, 19, 2, "version"], [56, 11, 19, 2], [57, 2, 19, 2], [58, 2, 20, 7], [58, 8, 20, 13, "composeDeclarations"], [58, 27, 20, 32], [58, 30, 20, 32, "exports"], [58, 37, 20, 32], [58, 38, 20, 32, "composeDeclarations"], [58, 57, 20, 32], [58, 60, 20, 35], [59, 4, 20, 35], [59, 10, 20, 35, "_e"], [59, 12, 20, 35], [59, 20, 20, 35, "global"], [59, 26, 20, 35], [59, 27, 20, 35, "Error"], [59, 32, 20, 35], [60, 4, 20, 35], [60, 10, 20, 35, "utilsJs3"], [60, 18, 20, 35], [60, 30, 20, 35, "utilsJs3"], [60, 31, 20, 36, "filters"], [60, 38, 20, 43], [60, 40, 20, 45, "composer"], [60, 48, 20, 53], [60, 50, 20, 58], [61, 6, 23, 2], [61, 12, 23, 8, "len"], [61, 15, 23, 11], [61, 18, 23, 14, "filters"], [61, 25, 23, 21], [61, 26, 23, 22, "length"], [61, 32, 23, 28], [62, 6, 24, 2], [62, 10, 24, 6, "len"], [62, 13, 24, 9], [62, 17, 24, 13], [62, 18, 24, 14], [62, 20, 24, 16], [63, 8, 25, 4], [63, 15, 25, 11, "filters"], [63, 22, 25, 18], [63, 23, 25, 19], [63, 24, 25, 20], [63, 25, 25, 21], [64, 6, 26, 2], [65, 6, 27, 2], [65, 13, 27, 9, "filters"], [65, 20, 27, 16], [65, 21, 27, 17, "reduceRight"], [65, 32, 27, 28], [65, 33, 27, 29], [65, 34, 27, 30, "inner"], [65, 39, 27, 35], [65, 41, 27, 37, "outer"], [65, 46, 27, 42], [65, 51, 27, 47, "inner"], [65, 56, 27, 52], [65, 59, 27, 55, "composer"], [65, 67, 27, 63], [65, 68, 27, 64, "outer"], [65, 73, 27, 69], [65, 75, 27, 71, "inner"], [65, 80, 27, 76], [65, 81, 27, 77], [65, 84, 27, 80, "outer"], [65, 89, 27, 85], [65, 90, 27, 86], [66, 4, 28, 0], [66, 5, 28, 1], [67, 4, 28, 1, "utilsJs3"], [67, 12, 28, 1], [67, 13, 28, 1, "__closure"], [67, 22, 28, 1], [68, 4, 28, 1, "utilsJs3"], [68, 12, 28, 1], [68, 13, 28, 1, "__workletHash"], [68, 26, 28, 1], [69, 4, 28, 1, "utilsJs3"], [69, 12, 28, 1], [69, 13, 28, 1, "__initData"], [69, 23, 28, 1], [69, 26, 28, 1, "_worklet_5389924226996_init_data"], [69, 58, 28, 1], [70, 4, 28, 1, "utilsJs3"], [70, 12, 28, 1], [70, 13, 28, 1, "__stackDetails"], [70, 27, 28, 1], [70, 30, 28, 1, "_e"], [70, 32, 28, 1], [71, 4, 28, 1], [71, 11, 28, 1, "utilsJs3"], [71, 19, 28, 1], [72, 2, 28, 1], [72, 3, 20, 35], [72, 5, 28, 1], [73, 0, 28, 2], [73, 3]], "functionMap": {"names": ["<global>", "isSharedValue", "materialize", "mapKeys.forEach$argument_0", "composeDeclarations", "filters.reduceRight$argument_0"], "mappings": "AAA;6BCC;CDK;2BEC;0BCI;GDK;CFE;mCIC;6BCO,wDD;CJC"}}, "type": "js/module"}]}