{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: Helper functions for robust face detection\n    const loadMediaPipeFaceDetection = async () => {\n      // Try to load MediaPipe Face Detection from CDN\n      if (!window.FaceDetection) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/face_detection@0.4.1646425229/face_detection.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n    };\n    const detectFacesWithMediaPipe = async img => {\n      // This is a placeholder - MediaPipe integration would be complex\n      // For now, return empty array to fall back to heuristic\n      return [];\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Simple skin tone detection and face-like region finding\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 20; // Adaptive block size\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize) {\n        for (let x = 0; x < img.width - blockSize; x += blockSize) {\n          const skinPixels = countSkinPixelsInRegion(data, x, y, blockSize, img.width);\n          const skinRatio = skinPixels / (blockSize * blockSize);\n\n          // If this region has a high concentration of skin-like pixels\n          if (skinRatio > 0.3) {\n            // Check if it's in a face-like position (upper 2/3 of image)\n            if (y < img.height * 0.67) {\n              faces.push({\n                boundingBox: {\n                  xCenter: (x + blockSize / 2) / img.width,\n                  yCenter: (y + blockSize / 2) / img.height,\n                  width: blockSize * 2 / img.width,\n                  // Make bounding box larger\n                  height: blockSize * 2.5 / img.height\n                }\n              });\n              console.log(`[EchoCameraWeb] 🎯 Found face-like region at (${x}, ${y}) with ${(skinRatio * 100).toFixed(1)}% skin pixels`);\n            }\n          }\n        }\n      }\n\n      // Merge overlapping detections and limit to most likely faces\n      const mergedFaces = mergeFaceDetections(faces);\n      return mergedFaces.slice(0, 5); // Max 5 faces\n    };\n    const countSkinPixelsInRegion = (data, startX, startY, size, imageWidth) => {\n      let skinPixels = 0;\n      for (let y = startY; y < startY + size; y++) {\n        for (let x = startX; x < startX + size; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Simple skin tone detection\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n        }\n      }\n      return skinPixels;\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Get the region to blur\n      const imageData = ctx.getImageData(x, y, width, height);\n      const data = imageData.data;\n\n      // Apply multiple blur effects\n\n      // 1. Heavy pixelation\n      const pixelSize = Math.max(25, Math.min(width, height) / 6);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < height; py += pixelSize) {\n        for (let px = 0; px < width; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n              const index = ((py + dy) * width + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n                const index = ((py + dy) * width + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // 2. Additional gaussian-like blur\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur effect`);\n      for (let i = 0; i < 3; i++) {\n        // Multiple passes\n        applySimpleBlur(data, width, height);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, x, y);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Simple but effective face detection using multiple strategies\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting NEW face detection system...');\n\n        // Strategy 1: Try MediaPipe Face Detection (more reliable)\n        try {\n          await loadMediaPipeFaceDetection();\n          detectedFaces = await detectFacesWithMediaPipe(img);\n          console.log(`[EchoCameraWeb] ✅ MediaPipe found ${detectedFaces.length} faces`);\n        } catch (mediaPipeError) {\n          console.warn('[EchoCameraWeb] ❌ MediaPipe failed:', mediaPipeError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Using intelligent heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1524, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadMediaPipeFaceDetection"], [91, 36, 91, 34], [91, 39, 91, 37], [91, 45, 91, 37, "loadMediaPipeFaceDetection"], [91, 46, 91, 37], [91, 51, 91, 49], [92, 6, 92, 4], [93, 6, 93, 4], [93, 10, 93, 8], [93, 11, 93, 10, "window"], [93, 17, 93, 16], [93, 18, 93, 25, "FaceDetection"], [93, 31, 93, 38], [93, 33, 93, 40], [94, 8, 94, 6], [94, 14, 94, 12], [94, 18, 94, 16, "Promise"], [94, 25, 94, 23], [94, 26, 94, 24], [94, 27, 94, 25, "resolve"], [94, 34, 94, 32], [94, 36, 94, 34, "reject"], [94, 42, 94, 40], [94, 47, 94, 45], [95, 10, 95, 8], [95, 16, 95, 14, "script"], [95, 22, 95, 20], [95, 25, 95, 23, "document"], [95, 33, 95, 31], [95, 34, 95, 32, "createElement"], [95, 47, 95, 45], [95, 48, 95, 46], [95, 56, 95, 54], [95, 57, 95, 55], [96, 10, 96, 8, "script"], [96, 16, 96, 14], [96, 17, 96, 15, "src"], [96, 20, 96, 18], [96, 23, 96, 21], [96, 112, 96, 110], [97, 10, 97, 8, "script"], [97, 16, 97, 14], [97, 17, 97, 15, "onload"], [97, 23, 97, 21], [97, 26, 97, 24, "resolve"], [97, 33, 97, 31], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "onerror"], [98, 24, 98, 22], [98, 27, 98, 25, "reject"], [98, 33, 98, 31], [99, 10, 99, 8, "document"], [99, 18, 99, 16], [99, 19, 99, 17, "head"], [99, 23, 99, 21], [99, 24, 99, 22, "append<PERSON><PERSON><PERSON>"], [99, 35, 99, 33], [99, 36, 99, 34, "script"], [99, 42, 99, 40], [99, 43, 99, 41], [100, 8, 100, 6], [100, 9, 100, 7], [100, 10, 100, 8], [101, 6, 101, 4], [102, 4, 102, 2], [102, 5, 102, 3], [103, 4, 104, 2], [103, 10, 104, 8, "detectFacesWithMediaPipe"], [103, 34, 104, 32], [103, 37, 104, 35], [103, 43, 104, 42, "img"], [103, 46, 104, 63], [103, 50, 104, 68], [104, 6, 105, 4], [105, 6, 106, 4], [106, 6, 107, 4], [106, 13, 107, 11], [106, 15, 107, 13], [107, 4, 108, 2], [107, 5, 108, 3], [108, 4, 110, 2], [108, 10, 110, 8, "detectFacesHeuristic"], [108, 30, 110, 28], [108, 33, 110, 31, "detectFacesHeuristic"], [108, 34, 110, 32, "img"], [108, 37, 110, 53], [108, 39, 110, 55, "ctx"], [108, 42, 110, 84], [108, 47, 110, 89], [109, 6, 111, 4, "console"], [109, 13, 111, 11], [109, 14, 111, 12, "log"], [109, 17, 111, 15], [109, 18, 111, 16], [109, 74, 111, 72], [109, 75, 111, 73], [111, 6, 113, 4], [112, 6, 114, 4], [112, 12, 114, 10, "imageData"], [112, 21, 114, 19], [112, 24, 114, 22, "ctx"], [112, 27, 114, 25], [112, 28, 114, 26, "getImageData"], [112, 40, 114, 38], [112, 41, 114, 39], [112, 42, 114, 40], [112, 44, 114, 42], [112, 45, 114, 43], [112, 47, 114, 45, "img"], [112, 50, 114, 48], [112, 51, 114, 49, "width"], [112, 56, 114, 54], [112, 58, 114, 56, "img"], [112, 61, 114, 59], [112, 62, 114, 60, "height"], [112, 68, 114, 66], [112, 69, 114, 67], [113, 6, 115, 4], [113, 12, 115, 10, "data"], [113, 16, 115, 14], [113, 19, 115, 17, "imageData"], [113, 28, 115, 26], [113, 29, 115, 27, "data"], [113, 33, 115, 31], [115, 6, 117, 4], [116, 6, 118, 4], [116, 12, 118, 10, "faces"], [116, 17, 118, 15], [116, 20, 118, 18], [116, 22, 118, 20], [117, 6, 119, 4], [117, 12, 119, 10, "blockSize"], [117, 21, 119, 19], [117, 24, 119, 22, "Math"], [117, 28, 119, 26], [117, 29, 119, 27, "min"], [117, 32, 119, 30], [117, 33, 119, 31, "img"], [117, 36, 119, 34], [117, 37, 119, 35, "width"], [117, 42, 119, 40], [117, 44, 119, 42, "img"], [117, 47, 119, 45], [117, 48, 119, 46, "height"], [117, 54, 119, 52], [117, 55, 119, 53], [117, 58, 119, 56], [117, 60, 119, 58], [117, 61, 119, 59], [117, 62, 119, 60], [119, 6, 121, 4], [119, 11, 121, 9], [119, 15, 121, 13, "y"], [119, 16, 121, 14], [119, 19, 121, 17], [119, 20, 121, 18], [119, 22, 121, 20, "y"], [119, 23, 121, 21], [119, 26, 121, 24, "img"], [119, 29, 121, 27], [119, 30, 121, 28, "height"], [119, 36, 121, 34], [119, 39, 121, 37, "blockSize"], [119, 48, 121, 46], [119, 50, 121, 48, "y"], [119, 51, 121, 49], [119, 55, 121, 53, "blockSize"], [119, 64, 121, 62], [119, 66, 121, 64], [120, 8, 122, 6], [120, 13, 122, 11], [120, 17, 122, 15, "x"], [120, 18, 122, 16], [120, 21, 122, 19], [120, 22, 122, 20], [120, 24, 122, 22, "x"], [120, 25, 122, 23], [120, 28, 122, 26, "img"], [120, 31, 122, 29], [120, 32, 122, 30, "width"], [120, 37, 122, 35], [120, 40, 122, 38, "blockSize"], [120, 49, 122, 47], [120, 51, 122, 49, "x"], [120, 52, 122, 50], [120, 56, 122, 54, "blockSize"], [120, 65, 122, 63], [120, 67, 122, 65], [121, 10, 123, 8], [121, 16, 123, 14, "skinPixels"], [121, 26, 123, 24], [121, 29, 123, 27, "countSkinPixelsInRegion"], [121, 52, 123, 50], [121, 53, 123, 51, "data"], [121, 57, 123, 55], [121, 59, 123, 57, "x"], [121, 60, 123, 58], [121, 62, 123, 60, "y"], [121, 63, 123, 61], [121, 65, 123, 63, "blockSize"], [121, 74, 123, 72], [121, 76, 123, 74, "img"], [121, 79, 123, 77], [121, 80, 123, 78, "width"], [121, 85, 123, 83], [121, 86, 123, 84], [122, 10, 124, 8], [122, 16, 124, 14, "skinRatio"], [122, 25, 124, 23], [122, 28, 124, 26, "skinPixels"], [122, 38, 124, 36], [122, 42, 124, 40, "blockSize"], [122, 51, 124, 49], [122, 54, 124, 52, "blockSize"], [122, 63, 124, 61], [122, 64, 124, 62], [124, 10, 126, 8], [125, 10, 127, 8], [125, 14, 127, 12, "skinRatio"], [125, 23, 127, 21], [125, 26, 127, 24], [125, 29, 127, 27], [125, 31, 127, 29], [126, 12, 128, 10], [127, 12, 129, 10], [127, 16, 129, 14, "y"], [127, 17, 129, 15], [127, 20, 129, 18, "img"], [127, 23, 129, 21], [127, 24, 129, 22, "height"], [127, 30, 129, 28], [127, 33, 129, 31], [127, 37, 129, 35], [127, 39, 129, 37], [128, 14, 130, 12, "faces"], [128, 19, 130, 17], [128, 20, 130, 18, "push"], [128, 24, 130, 22], [128, 25, 130, 23], [129, 16, 131, 14, "boundingBox"], [129, 27, 131, 25], [129, 29, 131, 27], [130, 18, 132, 16, "xCenter"], [130, 25, 132, 23], [130, 27, 132, 25], [130, 28, 132, 26, "x"], [130, 29, 132, 27], [130, 32, 132, 30, "blockSize"], [130, 41, 132, 39], [130, 44, 132, 42], [130, 45, 132, 43], [130, 49, 132, 47, "img"], [130, 52, 132, 50], [130, 53, 132, 51, "width"], [130, 58, 132, 56], [131, 18, 133, 16, "yCenter"], [131, 25, 133, 23], [131, 27, 133, 25], [131, 28, 133, 26, "y"], [131, 29, 133, 27], [131, 32, 133, 30, "blockSize"], [131, 41, 133, 39], [131, 44, 133, 42], [131, 45, 133, 43], [131, 49, 133, 47, "img"], [131, 52, 133, 50], [131, 53, 133, 51, "height"], [131, 59, 133, 57], [132, 18, 134, 16, "width"], [132, 23, 134, 21], [132, 25, 134, 24, "blockSize"], [132, 34, 134, 33], [132, 37, 134, 36], [132, 38, 134, 37], [132, 41, 134, 41, "img"], [132, 44, 134, 44], [132, 45, 134, 45, "width"], [132, 50, 134, 50], [133, 18, 134, 53], [134, 18, 135, 16, "height"], [134, 24, 135, 22], [134, 26, 135, 25, "blockSize"], [134, 35, 135, 34], [134, 38, 135, 37], [134, 41, 135, 40], [134, 44, 135, 44, "img"], [134, 47, 135, 47], [134, 48, 135, 48, "height"], [135, 16, 136, 14], [136, 14, 137, 12], [136, 15, 137, 13], [136, 16, 137, 14], [137, 14, 138, 12, "console"], [137, 21, 138, 19], [137, 22, 138, 20, "log"], [137, 25, 138, 23], [137, 26, 138, 24], [137, 75, 138, 73, "x"], [137, 76, 138, 74], [137, 81, 138, 79, "y"], [137, 82, 138, 80], [137, 92, 138, 90], [137, 93, 138, 91, "skinRatio"], [137, 102, 138, 100], [137, 105, 138, 103], [137, 108, 138, 106], [137, 110, 138, 108, "toFixed"], [137, 117, 138, 115], [137, 118, 138, 116], [137, 119, 138, 117], [137, 120, 138, 118], [137, 135, 138, 133], [137, 136, 138, 134], [138, 12, 139, 10], [139, 10, 140, 8], [140, 8, 141, 6], [141, 6, 142, 4], [143, 6, 144, 4], [144, 6, 145, 4], [144, 12, 145, 10, "mergedFaces"], [144, 23, 145, 21], [144, 26, 145, 24, "mergeFaceDetections"], [144, 45, 145, 43], [144, 46, 145, 44, "faces"], [144, 51, 145, 49], [144, 52, 145, 50], [145, 6, 146, 4], [145, 13, 146, 11, "mergedFaces"], [145, 24, 146, 22], [145, 25, 146, 23, "slice"], [145, 30, 146, 28], [145, 31, 146, 29], [145, 32, 146, 30], [145, 34, 146, 32], [145, 35, 146, 33], [145, 36, 146, 34], [145, 37, 146, 35], [145, 38, 146, 36], [146, 4, 147, 2], [146, 5, 147, 3], [147, 4, 149, 2], [147, 10, 149, 8, "countSkinPixelsInRegion"], [147, 33, 149, 31], [147, 36, 149, 34, "countSkinPixelsInRegion"], [147, 37, 149, 35, "data"], [147, 41, 149, 58], [147, 43, 149, 60, "startX"], [147, 49, 149, 74], [147, 51, 149, 76, "startY"], [147, 57, 149, 90], [147, 59, 149, 92, "size"], [147, 63, 149, 104], [147, 65, 149, 106, "imageWidth"], [147, 75, 149, 124], [147, 80, 149, 129], [148, 6, 150, 4], [148, 10, 150, 8, "skinPixels"], [148, 20, 150, 18], [148, 23, 150, 21], [148, 24, 150, 22], [149, 6, 151, 4], [149, 11, 151, 9], [149, 15, 151, 13, "y"], [149, 16, 151, 14], [149, 19, 151, 17, "startY"], [149, 25, 151, 23], [149, 27, 151, 25, "y"], [149, 28, 151, 26], [149, 31, 151, 29, "startY"], [149, 37, 151, 35], [149, 40, 151, 38, "size"], [149, 44, 151, 42], [149, 46, 151, 44, "y"], [149, 47, 151, 45], [149, 49, 151, 47], [149, 51, 151, 49], [150, 8, 152, 6], [150, 13, 152, 11], [150, 17, 152, 15, "x"], [150, 18, 152, 16], [150, 21, 152, 19, "startX"], [150, 27, 152, 25], [150, 29, 152, 27, "x"], [150, 30, 152, 28], [150, 33, 152, 31, "startX"], [150, 39, 152, 37], [150, 42, 152, 40, "size"], [150, 46, 152, 44], [150, 48, 152, 46, "x"], [150, 49, 152, 47], [150, 51, 152, 49], [150, 53, 152, 51], [151, 10, 153, 8], [151, 16, 153, 14, "index"], [151, 21, 153, 19], [151, 24, 153, 22], [151, 25, 153, 23, "y"], [151, 26, 153, 24], [151, 29, 153, 27, "imageWidth"], [151, 39, 153, 37], [151, 42, 153, 40, "x"], [151, 43, 153, 41], [151, 47, 153, 45], [151, 48, 153, 46], [152, 10, 154, 8], [152, 16, 154, 14, "r"], [152, 17, 154, 15], [152, 20, 154, 18, "data"], [152, 24, 154, 22], [152, 25, 154, 23, "index"], [152, 30, 154, 28], [152, 31, 154, 29], [153, 10, 155, 8], [153, 16, 155, 14, "g"], [153, 17, 155, 15], [153, 20, 155, 18, "data"], [153, 24, 155, 22], [153, 25, 155, 23, "index"], [153, 30, 155, 28], [153, 33, 155, 31], [153, 34, 155, 32], [153, 35, 155, 33], [154, 10, 156, 8], [154, 16, 156, 14, "b"], [154, 17, 156, 15], [154, 20, 156, 18, "data"], [154, 24, 156, 22], [154, 25, 156, 23, "index"], [154, 30, 156, 28], [154, 33, 156, 31], [154, 34, 156, 32], [154, 35, 156, 33], [156, 10, 158, 8], [157, 10, 159, 8], [157, 14, 159, 12, "isSkinTone"], [157, 24, 159, 22], [157, 25, 159, 23, "r"], [157, 26, 159, 24], [157, 28, 159, 26, "g"], [157, 29, 159, 27], [157, 31, 159, 29, "b"], [157, 32, 159, 30], [157, 33, 159, 31], [157, 35, 159, 33], [158, 12, 160, 10, "skinPixels"], [158, 22, 160, 20], [158, 24, 160, 22], [159, 10, 161, 8], [160, 8, 162, 6], [161, 6, 163, 4], [162, 6, 164, 4], [162, 13, 164, 11, "skinPixels"], [162, 23, 164, 21], [163, 4, 165, 2], [163, 5, 165, 3], [164, 4, 167, 2], [164, 10, 167, 8, "isSkinTone"], [164, 20, 167, 18], [164, 23, 167, 21, "isSkinTone"], [164, 24, 167, 22, "r"], [164, 25, 167, 31], [164, 27, 167, 33, "g"], [164, 28, 167, 42], [164, 30, 167, 44, "b"], [164, 31, 167, 53], [164, 36, 167, 58], [165, 6, 168, 4], [166, 6, 169, 4], [166, 13, 170, 6, "r"], [166, 14, 170, 7], [166, 17, 170, 10], [166, 19, 170, 12], [166, 23, 170, 16, "g"], [166, 24, 170, 17], [166, 27, 170, 20], [166, 29, 170, 22], [166, 33, 170, 26, "b"], [166, 34, 170, 27], [166, 37, 170, 30], [166, 39, 170, 32], [166, 43, 171, 6, "r"], [166, 44, 171, 7], [166, 47, 171, 10, "g"], [166, 48, 171, 11], [166, 52, 171, 15, "r"], [166, 53, 171, 16], [166, 56, 171, 19, "b"], [166, 57, 171, 20], [166, 61, 172, 6, "Math"], [166, 65, 172, 10], [166, 66, 172, 11, "abs"], [166, 69, 172, 14], [166, 70, 172, 15, "r"], [166, 71, 172, 16], [166, 74, 172, 19, "g"], [166, 75, 172, 20], [166, 76, 172, 21], [166, 79, 172, 24], [166, 81, 172, 26], [166, 85, 173, 6, "Math"], [166, 89, 173, 10], [166, 90, 173, 11, "max"], [166, 93, 173, 14], [166, 94, 173, 15, "r"], [166, 95, 173, 16], [166, 97, 173, 18, "g"], [166, 98, 173, 19], [166, 100, 173, 21, "b"], [166, 101, 173, 22], [166, 102, 173, 23], [166, 105, 173, 26, "Math"], [166, 109, 173, 30], [166, 110, 173, 31, "min"], [166, 113, 173, 34], [166, 114, 173, 35, "r"], [166, 115, 173, 36], [166, 117, 173, 38, "g"], [166, 118, 173, 39], [166, 120, 173, 41, "b"], [166, 121, 173, 42], [166, 122, 173, 43], [166, 125, 173, 46], [166, 127, 173, 48], [167, 4, 175, 2], [167, 5, 175, 3], [168, 4, 177, 2], [168, 10, 177, 8, "mergeFaceDetections"], [168, 29, 177, 27], [168, 32, 177, 31, "faces"], [168, 37, 177, 43], [168, 41, 177, 48], [169, 6, 178, 4], [169, 10, 178, 8, "faces"], [169, 15, 178, 13], [169, 16, 178, 14, "length"], [169, 22, 178, 20], [169, 26, 178, 24], [169, 27, 178, 25], [169, 29, 178, 27], [169, 36, 178, 34, "faces"], [169, 41, 178, 39], [170, 6, 180, 4], [170, 12, 180, 10, "merged"], [170, 18, 180, 16], [170, 21, 180, 19], [170, 23, 180, 21], [171, 6, 181, 4], [171, 12, 181, 10, "used"], [171, 16, 181, 14], [171, 19, 181, 17], [171, 23, 181, 21, "Set"], [171, 26, 181, 24], [171, 27, 181, 25], [171, 28, 181, 26], [172, 6, 183, 4], [172, 11, 183, 9], [172, 15, 183, 13, "i"], [172, 16, 183, 14], [172, 19, 183, 17], [172, 20, 183, 18], [172, 22, 183, 20, "i"], [172, 23, 183, 21], [172, 26, 183, 24, "faces"], [172, 31, 183, 29], [172, 32, 183, 30, "length"], [172, 38, 183, 36], [172, 40, 183, 38, "i"], [172, 41, 183, 39], [172, 43, 183, 41], [172, 45, 183, 43], [173, 8, 184, 6], [173, 12, 184, 10, "used"], [173, 16, 184, 14], [173, 17, 184, 15, "has"], [173, 20, 184, 18], [173, 21, 184, 19, "i"], [173, 22, 184, 20], [173, 23, 184, 21], [173, 25, 184, 23], [174, 8, 186, 6], [174, 12, 186, 10, "currentFace"], [174, 23, 186, 21], [174, 26, 186, 24, "faces"], [174, 31, 186, 29], [174, 32, 186, 30, "i"], [174, 33, 186, 31], [174, 34, 186, 32], [175, 8, 187, 6, "used"], [175, 12, 187, 10], [175, 13, 187, 11, "add"], [175, 16, 187, 14], [175, 17, 187, 15, "i"], [175, 18, 187, 16], [175, 19, 187, 17], [177, 8, 189, 6], [178, 8, 190, 6], [178, 13, 190, 11], [178, 17, 190, 15, "j"], [178, 18, 190, 16], [178, 21, 190, 19, "i"], [178, 22, 190, 20], [178, 25, 190, 23], [178, 26, 190, 24], [178, 28, 190, 26, "j"], [178, 29, 190, 27], [178, 32, 190, 30, "faces"], [178, 37, 190, 35], [178, 38, 190, 36, "length"], [178, 44, 190, 42], [178, 46, 190, 44, "j"], [178, 47, 190, 45], [178, 49, 190, 47], [178, 51, 190, 49], [179, 10, 191, 8], [179, 14, 191, 12, "used"], [179, 18, 191, 16], [179, 19, 191, 17, "has"], [179, 22, 191, 20], [179, 23, 191, 21, "j"], [179, 24, 191, 22], [179, 25, 191, 23], [179, 27, 191, 25], [180, 10, 193, 8], [180, 16, 193, 14, "overlap"], [180, 23, 193, 21], [180, 26, 193, 24, "calculateOverlap"], [180, 42, 193, 40], [180, 43, 193, 41, "currentFace"], [180, 54, 193, 52], [180, 55, 193, 53, "boundingBox"], [180, 66, 193, 64], [180, 68, 193, 66, "faces"], [180, 73, 193, 71], [180, 74, 193, 72, "j"], [180, 75, 193, 73], [180, 76, 193, 74], [180, 77, 193, 75, "boundingBox"], [180, 88, 193, 86], [180, 89, 193, 87], [181, 10, 194, 8], [181, 14, 194, 12, "overlap"], [181, 21, 194, 19], [181, 24, 194, 22], [181, 27, 194, 25], [181, 29, 194, 27], [182, 12, 194, 29], [183, 12, 195, 10], [184, 12, 196, 10, "currentFace"], [184, 23, 196, 21], [184, 26, 196, 24, "mergeTwoFaces"], [184, 39, 196, 37], [184, 40, 196, 38, "currentFace"], [184, 51, 196, 49], [184, 53, 196, 51, "faces"], [184, 58, 196, 56], [184, 59, 196, 57, "j"], [184, 60, 196, 58], [184, 61, 196, 59], [184, 62, 196, 60], [185, 12, 197, 10, "used"], [185, 16, 197, 14], [185, 17, 197, 15, "add"], [185, 20, 197, 18], [185, 21, 197, 19, "j"], [185, 22, 197, 20], [185, 23, 197, 21], [186, 10, 198, 8], [187, 8, 199, 6], [188, 8, 201, 6, "merged"], [188, 14, 201, 12], [188, 15, 201, 13, "push"], [188, 19, 201, 17], [188, 20, 201, 18, "currentFace"], [188, 31, 201, 29], [188, 32, 201, 30], [189, 6, 202, 4], [190, 6, 204, 4], [190, 13, 204, 11, "merged"], [190, 19, 204, 17], [191, 4, 205, 2], [191, 5, 205, 3], [192, 4, 207, 2], [192, 10, 207, 8, "calculateOverlap"], [192, 26, 207, 24], [192, 29, 207, 27, "calculateOverlap"], [192, 30, 207, 28, "box1"], [192, 34, 207, 37], [192, 36, 207, 39, "box2"], [192, 40, 207, 48], [192, 45, 207, 53], [193, 6, 208, 4], [193, 12, 208, 10, "x1"], [193, 14, 208, 12], [193, 17, 208, 15, "Math"], [193, 21, 208, 19], [193, 22, 208, 20, "max"], [193, 25, 208, 23], [193, 26, 208, 24, "box1"], [193, 30, 208, 28], [193, 31, 208, 29, "xCenter"], [193, 38, 208, 36], [193, 41, 208, 39, "box1"], [193, 45, 208, 43], [193, 46, 208, 44, "width"], [193, 51, 208, 49], [193, 54, 208, 50], [193, 55, 208, 51], [193, 57, 208, 53, "box2"], [193, 61, 208, 57], [193, 62, 208, 58, "xCenter"], [193, 69, 208, 65], [193, 72, 208, 68, "box2"], [193, 76, 208, 72], [193, 77, 208, 73, "width"], [193, 82, 208, 78], [193, 85, 208, 79], [193, 86, 208, 80], [193, 87, 208, 81], [194, 6, 209, 4], [194, 12, 209, 10, "y1"], [194, 14, 209, 12], [194, 17, 209, 15, "Math"], [194, 21, 209, 19], [194, 22, 209, 20, "max"], [194, 25, 209, 23], [194, 26, 209, 24, "box1"], [194, 30, 209, 28], [194, 31, 209, 29, "yCenter"], [194, 38, 209, 36], [194, 41, 209, 39, "box1"], [194, 45, 209, 43], [194, 46, 209, 44, "height"], [194, 52, 209, 50], [194, 55, 209, 51], [194, 56, 209, 52], [194, 58, 209, 54, "box2"], [194, 62, 209, 58], [194, 63, 209, 59, "yCenter"], [194, 70, 209, 66], [194, 73, 209, 69, "box2"], [194, 77, 209, 73], [194, 78, 209, 74, "height"], [194, 84, 209, 80], [194, 87, 209, 81], [194, 88, 209, 82], [194, 89, 209, 83], [195, 6, 210, 4], [195, 12, 210, 10, "x2"], [195, 14, 210, 12], [195, 17, 210, 15, "Math"], [195, 21, 210, 19], [195, 22, 210, 20, "min"], [195, 25, 210, 23], [195, 26, 210, 24, "box1"], [195, 30, 210, 28], [195, 31, 210, 29, "xCenter"], [195, 38, 210, 36], [195, 41, 210, 39, "box1"], [195, 45, 210, 43], [195, 46, 210, 44, "width"], [195, 51, 210, 49], [195, 54, 210, 50], [195, 55, 210, 51], [195, 57, 210, 53, "box2"], [195, 61, 210, 57], [195, 62, 210, 58, "xCenter"], [195, 69, 210, 65], [195, 72, 210, 68, "box2"], [195, 76, 210, 72], [195, 77, 210, 73, "width"], [195, 82, 210, 78], [195, 85, 210, 79], [195, 86, 210, 80], [195, 87, 210, 81], [196, 6, 211, 4], [196, 12, 211, 10, "y2"], [196, 14, 211, 12], [196, 17, 211, 15, "Math"], [196, 21, 211, 19], [196, 22, 211, 20, "min"], [196, 25, 211, 23], [196, 26, 211, 24, "box1"], [196, 30, 211, 28], [196, 31, 211, 29, "yCenter"], [196, 38, 211, 36], [196, 41, 211, 39, "box1"], [196, 45, 211, 43], [196, 46, 211, 44, "height"], [196, 52, 211, 50], [196, 55, 211, 51], [196, 56, 211, 52], [196, 58, 211, 54, "box2"], [196, 62, 211, 58], [196, 63, 211, 59, "yCenter"], [196, 70, 211, 66], [196, 73, 211, 69, "box2"], [196, 77, 211, 73], [196, 78, 211, 74, "height"], [196, 84, 211, 80], [196, 87, 211, 81], [196, 88, 211, 82], [196, 89, 211, 83], [197, 6, 213, 4], [197, 10, 213, 8, "x2"], [197, 12, 213, 10], [197, 16, 213, 14, "x1"], [197, 18, 213, 16], [197, 22, 213, 20, "y2"], [197, 24, 213, 22], [197, 28, 213, 26, "y1"], [197, 30, 213, 28], [197, 32, 213, 30], [197, 39, 213, 37], [197, 40, 213, 38], [198, 6, 215, 4], [198, 12, 215, 10, "overlapArea"], [198, 23, 215, 21], [198, 26, 215, 24], [198, 27, 215, 25, "x2"], [198, 29, 215, 27], [198, 32, 215, 30, "x1"], [198, 34, 215, 32], [198, 39, 215, 37, "y2"], [198, 41, 215, 39], [198, 44, 215, 42, "y1"], [198, 46, 215, 44], [198, 47, 215, 45], [199, 6, 216, 4], [199, 12, 216, 10, "box1Area"], [199, 20, 216, 18], [199, 23, 216, 21, "box1"], [199, 27, 216, 25], [199, 28, 216, 26, "width"], [199, 33, 216, 31], [199, 36, 216, 34, "box1"], [199, 40, 216, 38], [199, 41, 216, 39, "height"], [199, 47, 216, 45], [200, 6, 217, 4], [200, 12, 217, 10, "box2Area"], [200, 20, 217, 18], [200, 23, 217, 21, "box2"], [200, 27, 217, 25], [200, 28, 217, 26, "width"], [200, 33, 217, 31], [200, 36, 217, 34, "box2"], [200, 40, 217, 38], [200, 41, 217, 39, "height"], [200, 47, 217, 45], [201, 6, 219, 4], [201, 13, 219, 11, "overlapArea"], [201, 24, 219, 22], [201, 27, 219, 25, "Math"], [201, 31, 219, 29], [201, 32, 219, 30, "min"], [201, 35, 219, 33], [201, 36, 219, 34, "box1Area"], [201, 44, 219, 42], [201, 46, 219, 44, "box2Area"], [201, 54, 219, 52], [201, 55, 219, 53], [202, 4, 220, 2], [202, 5, 220, 3], [203, 4, 222, 2], [203, 10, 222, 8, "mergeTwoFaces"], [203, 23, 222, 21], [203, 26, 222, 24, "mergeTwoFaces"], [203, 27, 222, 25, "face1"], [203, 32, 222, 35], [203, 34, 222, 37, "face2"], [203, 39, 222, 47], [203, 44, 222, 52], [204, 6, 223, 4], [204, 12, 223, 10, "box1"], [204, 16, 223, 14], [204, 19, 223, 17, "face1"], [204, 24, 223, 22], [204, 25, 223, 23, "boundingBox"], [204, 36, 223, 34], [205, 6, 224, 4], [205, 12, 224, 10, "box2"], [205, 16, 224, 14], [205, 19, 224, 17, "face2"], [205, 24, 224, 22], [205, 25, 224, 23, "boundingBox"], [205, 36, 224, 34], [206, 6, 226, 4], [206, 12, 226, 10, "left"], [206, 16, 226, 14], [206, 19, 226, 17, "Math"], [206, 23, 226, 21], [206, 24, 226, 22, "min"], [206, 27, 226, 25], [206, 28, 226, 26, "box1"], [206, 32, 226, 30], [206, 33, 226, 31, "xCenter"], [206, 40, 226, 38], [206, 43, 226, 41, "box1"], [206, 47, 226, 45], [206, 48, 226, 46, "width"], [206, 53, 226, 51], [206, 56, 226, 52], [206, 57, 226, 53], [206, 59, 226, 55, "box2"], [206, 63, 226, 59], [206, 64, 226, 60, "xCenter"], [206, 71, 226, 67], [206, 74, 226, 70, "box2"], [206, 78, 226, 74], [206, 79, 226, 75, "width"], [206, 84, 226, 80], [206, 87, 226, 81], [206, 88, 226, 82], [206, 89, 226, 83], [207, 6, 227, 4], [207, 12, 227, 10, "right"], [207, 17, 227, 15], [207, 20, 227, 18, "Math"], [207, 24, 227, 22], [207, 25, 227, 23, "max"], [207, 28, 227, 26], [207, 29, 227, 27, "box1"], [207, 33, 227, 31], [207, 34, 227, 32, "xCenter"], [207, 41, 227, 39], [207, 44, 227, 42, "box1"], [207, 48, 227, 46], [207, 49, 227, 47, "width"], [207, 54, 227, 52], [207, 57, 227, 53], [207, 58, 227, 54], [207, 60, 227, 56, "box2"], [207, 64, 227, 60], [207, 65, 227, 61, "xCenter"], [207, 72, 227, 68], [207, 75, 227, 71, "box2"], [207, 79, 227, 75], [207, 80, 227, 76, "width"], [207, 85, 227, 81], [207, 88, 227, 82], [207, 89, 227, 83], [207, 90, 227, 84], [208, 6, 228, 4], [208, 12, 228, 10, "top"], [208, 15, 228, 13], [208, 18, 228, 16, "Math"], [208, 22, 228, 20], [208, 23, 228, 21, "min"], [208, 26, 228, 24], [208, 27, 228, 25, "box1"], [208, 31, 228, 29], [208, 32, 228, 30, "yCenter"], [208, 39, 228, 37], [208, 42, 228, 40, "box1"], [208, 46, 228, 44], [208, 47, 228, 45, "height"], [208, 53, 228, 51], [208, 56, 228, 52], [208, 57, 228, 53], [208, 59, 228, 55, "box2"], [208, 63, 228, 59], [208, 64, 228, 60, "yCenter"], [208, 71, 228, 67], [208, 74, 228, 70, "box2"], [208, 78, 228, 74], [208, 79, 228, 75, "height"], [208, 85, 228, 81], [208, 88, 228, 82], [208, 89, 228, 83], [208, 90, 228, 84], [209, 6, 229, 4], [209, 12, 229, 10, "bottom"], [209, 18, 229, 16], [209, 21, 229, 19, "Math"], [209, 25, 229, 23], [209, 26, 229, 24, "max"], [209, 29, 229, 27], [209, 30, 229, 28, "box1"], [209, 34, 229, 32], [209, 35, 229, 33, "yCenter"], [209, 42, 229, 40], [209, 45, 229, 43, "box1"], [209, 49, 229, 47], [209, 50, 229, 48, "height"], [209, 56, 229, 54], [209, 59, 229, 55], [209, 60, 229, 56], [209, 62, 229, 58, "box2"], [209, 66, 229, 62], [209, 67, 229, 63, "yCenter"], [209, 74, 229, 70], [209, 77, 229, 73, "box2"], [209, 81, 229, 77], [209, 82, 229, 78, "height"], [209, 88, 229, 84], [209, 91, 229, 85], [209, 92, 229, 86], [209, 93, 229, 87], [210, 6, 231, 4], [210, 13, 231, 11], [211, 8, 232, 6, "boundingBox"], [211, 19, 232, 17], [211, 21, 232, 19], [212, 10, 233, 8, "xCenter"], [212, 17, 233, 15], [212, 19, 233, 17], [212, 20, 233, 18, "left"], [212, 24, 233, 22], [212, 27, 233, 25, "right"], [212, 32, 233, 30], [212, 36, 233, 34], [212, 37, 233, 35], [213, 10, 234, 8, "yCenter"], [213, 17, 234, 15], [213, 19, 234, 17], [213, 20, 234, 18, "top"], [213, 23, 234, 21], [213, 26, 234, 24, "bottom"], [213, 32, 234, 30], [213, 36, 234, 34], [213, 37, 234, 35], [214, 10, 235, 8, "width"], [214, 15, 235, 13], [214, 17, 235, 15, "right"], [214, 22, 235, 20], [214, 25, 235, 23, "left"], [214, 29, 235, 27], [215, 10, 236, 8, "height"], [215, 16, 236, 14], [215, 18, 236, 16, "bottom"], [215, 24, 236, 22], [215, 27, 236, 25, "top"], [216, 8, 237, 6], [217, 6, 238, 4], [217, 7, 238, 5], [218, 4, 239, 2], [218, 5, 239, 3], [220, 4, 241, 2], [221, 4, 242, 2], [221, 10, 242, 8, "applyStrongBlur"], [221, 25, 242, 23], [221, 28, 242, 26, "applyStrongBlur"], [221, 29, 242, 27, "ctx"], [221, 32, 242, 56], [221, 34, 242, 58, "x"], [221, 35, 242, 67], [221, 37, 242, 69, "y"], [221, 38, 242, 78], [221, 40, 242, 80, "width"], [221, 45, 242, 93], [221, 47, 242, 95, "height"], [221, 53, 242, 109], [221, 58, 242, 114], [222, 6, 243, 4], [223, 6, 244, 4], [223, 12, 244, 10, "imageData"], [223, 21, 244, 19], [223, 24, 244, 22, "ctx"], [223, 27, 244, 25], [223, 28, 244, 26, "getImageData"], [223, 40, 244, 38], [223, 41, 244, 39, "x"], [223, 42, 244, 40], [223, 44, 244, 42, "y"], [223, 45, 244, 43], [223, 47, 244, 45, "width"], [223, 52, 244, 50], [223, 54, 244, 52, "height"], [223, 60, 244, 58], [223, 61, 244, 59], [224, 6, 245, 4], [224, 12, 245, 10, "data"], [224, 16, 245, 14], [224, 19, 245, 17, "imageData"], [224, 28, 245, 26], [224, 29, 245, 27, "data"], [224, 33, 245, 31], [226, 6, 247, 4], [228, 6, 249, 4], [229, 6, 250, 4], [229, 12, 250, 10, "pixelSize"], [229, 21, 250, 19], [229, 24, 250, 22, "Math"], [229, 28, 250, 26], [229, 29, 250, 27, "max"], [229, 32, 250, 30], [229, 33, 250, 31], [229, 35, 250, 33], [229, 37, 250, 35, "Math"], [229, 41, 250, 39], [229, 42, 250, 40, "min"], [229, 45, 250, 43], [229, 46, 250, 44, "width"], [229, 51, 250, 49], [229, 53, 250, 51, "height"], [229, 59, 250, 57], [229, 60, 250, 58], [229, 63, 250, 61], [229, 64, 250, 62], [229, 65, 250, 63], [230, 6, 251, 4, "console"], [230, 13, 251, 11], [230, 14, 251, 12, "log"], [230, 17, 251, 15], [230, 18, 251, 16], [230, 77, 251, 75, "pixelSize"], [230, 86, 251, 84], [230, 90, 251, 88], [230, 91, 251, 89], [231, 6, 253, 4], [231, 11, 253, 9], [231, 15, 253, 13, "py"], [231, 17, 253, 15], [231, 20, 253, 18], [231, 21, 253, 19], [231, 23, 253, 21, "py"], [231, 25, 253, 23], [231, 28, 253, 26, "height"], [231, 34, 253, 32], [231, 36, 253, 34, "py"], [231, 38, 253, 36], [231, 42, 253, 40, "pixelSize"], [231, 51, 253, 49], [231, 53, 253, 51], [232, 8, 254, 6], [232, 13, 254, 11], [232, 17, 254, 15, "px"], [232, 19, 254, 17], [232, 22, 254, 20], [232, 23, 254, 21], [232, 25, 254, 23, "px"], [232, 27, 254, 25], [232, 30, 254, 28, "width"], [232, 35, 254, 33], [232, 37, 254, 35, "px"], [232, 39, 254, 37], [232, 43, 254, 41, "pixelSize"], [232, 52, 254, 50], [232, 54, 254, 52], [233, 10, 255, 8], [234, 10, 256, 8], [234, 14, 256, 12, "r"], [234, 15, 256, 13], [234, 18, 256, 16], [234, 19, 256, 17], [235, 12, 256, 19, "g"], [235, 13, 256, 20], [235, 16, 256, 23], [235, 17, 256, 24], [236, 12, 256, 26, "b"], [236, 13, 256, 27], [236, 16, 256, 30], [236, 17, 256, 31], [237, 12, 256, 33, "count"], [237, 17, 256, 38], [237, 20, 256, 41], [237, 21, 256, 42], [238, 10, 258, 8], [238, 15, 258, 13], [238, 19, 258, 17, "dy"], [238, 21, 258, 19], [238, 24, 258, 22], [238, 25, 258, 23], [238, 27, 258, 25, "dy"], [238, 29, 258, 27], [238, 32, 258, 30, "pixelSize"], [238, 41, 258, 39], [238, 45, 258, 43, "py"], [238, 47, 258, 45], [238, 50, 258, 48, "dy"], [238, 52, 258, 50], [238, 55, 258, 53, "height"], [238, 61, 258, 59], [238, 63, 258, 61, "dy"], [238, 65, 258, 63], [238, 67, 258, 65], [238, 69, 258, 67], [239, 12, 259, 10], [239, 17, 259, 15], [239, 21, 259, 19, "dx"], [239, 23, 259, 21], [239, 26, 259, 24], [239, 27, 259, 25], [239, 29, 259, 27, "dx"], [239, 31, 259, 29], [239, 34, 259, 32, "pixelSize"], [239, 43, 259, 41], [239, 47, 259, 45, "px"], [239, 49, 259, 47], [239, 52, 259, 50, "dx"], [239, 54, 259, 52], [239, 57, 259, 55, "width"], [239, 62, 259, 60], [239, 64, 259, 62, "dx"], [239, 66, 259, 64], [239, 68, 259, 66], [239, 70, 259, 68], [240, 14, 260, 12], [240, 20, 260, 18, "index"], [240, 25, 260, 23], [240, 28, 260, 26], [240, 29, 260, 27], [240, 30, 260, 28, "py"], [240, 32, 260, 30], [240, 35, 260, 33, "dy"], [240, 37, 260, 35], [240, 41, 260, 39, "width"], [240, 46, 260, 44], [240, 50, 260, 48, "px"], [240, 52, 260, 50], [240, 55, 260, 53, "dx"], [240, 57, 260, 55], [240, 58, 260, 56], [240, 62, 260, 60], [240, 63, 260, 61], [241, 14, 261, 12, "r"], [241, 15, 261, 13], [241, 19, 261, 17, "data"], [241, 23, 261, 21], [241, 24, 261, 22, "index"], [241, 29, 261, 27], [241, 30, 261, 28], [242, 14, 262, 12, "g"], [242, 15, 262, 13], [242, 19, 262, 17, "data"], [242, 23, 262, 21], [242, 24, 262, 22, "index"], [242, 29, 262, 27], [242, 32, 262, 30], [242, 33, 262, 31], [242, 34, 262, 32], [243, 14, 263, 12, "b"], [243, 15, 263, 13], [243, 19, 263, 17, "data"], [243, 23, 263, 21], [243, 24, 263, 22, "index"], [243, 29, 263, 27], [243, 32, 263, 30], [243, 33, 263, 31], [243, 34, 263, 32], [244, 14, 264, 12, "count"], [244, 19, 264, 17], [244, 21, 264, 19], [245, 12, 265, 10], [246, 10, 266, 8], [247, 10, 268, 8], [247, 14, 268, 12, "count"], [247, 19, 268, 17], [247, 22, 268, 20], [247, 23, 268, 21], [247, 25, 268, 23], [248, 12, 269, 10, "r"], [248, 13, 269, 11], [248, 16, 269, 14, "Math"], [248, 20, 269, 18], [248, 21, 269, 19, "floor"], [248, 26, 269, 24], [248, 27, 269, 25, "r"], [248, 28, 269, 26], [248, 31, 269, 29, "count"], [248, 36, 269, 34], [248, 37, 269, 35], [249, 12, 270, 10, "g"], [249, 13, 270, 11], [249, 16, 270, 14, "Math"], [249, 20, 270, 18], [249, 21, 270, 19, "floor"], [249, 26, 270, 24], [249, 27, 270, 25, "g"], [249, 28, 270, 26], [249, 31, 270, 29, "count"], [249, 36, 270, 34], [249, 37, 270, 35], [250, 12, 271, 10, "b"], [250, 13, 271, 11], [250, 16, 271, 14, "Math"], [250, 20, 271, 18], [250, 21, 271, 19, "floor"], [250, 26, 271, 24], [250, 27, 271, 25, "b"], [250, 28, 271, 26], [250, 31, 271, 29, "count"], [250, 36, 271, 34], [250, 37, 271, 35], [252, 12, 273, 10], [253, 12, 274, 10], [253, 17, 274, 15], [253, 21, 274, 19, "dy"], [253, 23, 274, 21], [253, 26, 274, 24], [253, 27, 274, 25], [253, 29, 274, 27, "dy"], [253, 31, 274, 29], [253, 34, 274, 32, "pixelSize"], [253, 43, 274, 41], [253, 47, 274, 45, "py"], [253, 49, 274, 47], [253, 52, 274, 50, "dy"], [253, 54, 274, 52], [253, 57, 274, 55, "height"], [253, 63, 274, 61], [253, 65, 274, 63, "dy"], [253, 67, 274, 65], [253, 69, 274, 67], [253, 71, 274, 69], [254, 14, 275, 12], [254, 19, 275, 17], [254, 23, 275, 21, "dx"], [254, 25, 275, 23], [254, 28, 275, 26], [254, 29, 275, 27], [254, 31, 275, 29, "dx"], [254, 33, 275, 31], [254, 36, 275, 34, "pixelSize"], [254, 45, 275, 43], [254, 49, 275, 47, "px"], [254, 51, 275, 49], [254, 54, 275, 52, "dx"], [254, 56, 275, 54], [254, 59, 275, 57, "width"], [254, 64, 275, 62], [254, 66, 275, 64, "dx"], [254, 68, 275, 66], [254, 70, 275, 68], [254, 72, 275, 70], [255, 16, 276, 14], [255, 22, 276, 20, "index"], [255, 27, 276, 25], [255, 30, 276, 28], [255, 31, 276, 29], [255, 32, 276, 30, "py"], [255, 34, 276, 32], [255, 37, 276, 35, "dy"], [255, 39, 276, 37], [255, 43, 276, 41, "width"], [255, 48, 276, 46], [255, 52, 276, 50, "px"], [255, 54, 276, 52], [255, 57, 276, 55, "dx"], [255, 59, 276, 57], [255, 60, 276, 58], [255, 64, 276, 62], [255, 65, 276, 63], [256, 16, 277, 14, "data"], [256, 20, 277, 18], [256, 21, 277, 19, "index"], [256, 26, 277, 24], [256, 27, 277, 25], [256, 30, 277, 28, "r"], [256, 31, 277, 29], [257, 16, 278, 14, "data"], [257, 20, 278, 18], [257, 21, 278, 19, "index"], [257, 26, 278, 24], [257, 29, 278, 27], [257, 30, 278, 28], [257, 31, 278, 29], [257, 34, 278, 32, "g"], [257, 35, 278, 33], [258, 16, 279, 14, "data"], [258, 20, 279, 18], [258, 21, 279, 19, "index"], [258, 26, 279, 24], [258, 29, 279, 27], [258, 30, 279, 28], [258, 31, 279, 29], [258, 34, 279, 32, "b"], [258, 35, 279, 33], [259, 16, 280, 14], [260, 14, 281, 12], [261, 12, 282, 10], [262, 10, 283, 8], [263, 8, 284, 6], [264, 6, 285, 4], [266, 6, 287, 4], [267, 6, 288, 4, "console"], [267, 13, 288, 11], [267, 14, 288, 12, "log"], [267, 17, 288, 15], [267, 18, 288, 16], [267, 71, 288, 69], [267, 72, 288, 70], [268, 6, 289, 4], [268, 11, 289, 9], [268, 15, 289, 13, "i"], [268, 16, 289, 14], [268, 19, 289, 17], [268, 20, 289, 18], [268, 22, 289, 20, "i"], [268, 23, 289, 21], [268, 26, 289, 24], [268, 27, 289, 25], [268, 29, 289, 27, "i"], [268, 30, 289, 28], [268, 32, 289, 30], [268, 34, 289, 32], [269, 8, 289, 34], [270, 8, 290, 6, "applySimpleBlur"], [270, 23, 290, 21], [270, 24, 290, 22, "data"], [270, 28, 290, 26], [270, 30, 290, 28, "width"], [270, 35, 290, 33], [270, 37, 290, 35, "height"], [270, 43, 290, 41], [270, 44, 290, 42], [271, 6, 291, 4], [273, 6, 293, 4], [274, 6, 294, 4, "ctx"], [274, 9, 294, 7], [274, 10, 294, 8, "putImageData"], [274, 22, 294, 20], [274, 23, 294, 21, "imageData"], [274, 32, 294, 30], [274, 34, 294, 32, "x"], [274, 35, 294, 33], [274, 37, 294, 35, "y"], [274, 38, 294, 36], [274, 39, 294, 37], [275, 4, 295, 2], [275, 5, 295, 3], [276, 4, 297, 2], [276, 10, 297, 8, "applySimpleBlur"], [276, 25, 297, 23], [276, 28, 297, 26, "applySimpleBlur"], [276, 29, 297, 27, "data"], [276, 33, 297, 50], [276, 35, 297, 52, "width"], [276, 40, 297, 65], [276, 42, 297, 67, "height"], [276, 48, 297, 81], [276, 53, 297, 86], [277, 6, 298, 4], [277, 12, 298, 10, "original"], [277, 20, 298, 18], [277, 23, 298, 21], [277, 27, 298, 25, "Uint8ClampedArray"], [277, 44, 298, 42], [277, 45, 298, 43, "data"], [277, 49, 298, 47], [277, 50, 298, 48], [278, 6, 300, 4], [278, 11, 300, 9], [278, 15, 300, 13, "y"], [278, 16, 300, 14], [278, 19, 300, 17], [278, 20, 300, 18], [278, 22, 300, 20, "y"], [278, 23, 300, 21], [278, 26, 300, 24, "height"], [278, 32, 300, 30], [278, 35, 300, 33], [278, 36, 300, 34], [278, 38, 300, 36, "y"], [278, 39, 300, 37], [278, 41, 300, 39], [278, 43, 300, 41], [279, 8, 301, 6], [279, 13, 301, 11], [279, 17, 301, 15, "x"], [279, 18, 301, 16], [279, 21, 301, 19], [279, 22, 301, 20], [279, 24, 301, 22, "x"], [279, 25, 301, 23], [279, 28, 301, 26, "width"], [279, 33, 301, 31], [279, 36, 301, 34], [279, 37, 301, 35], [279, 39, 301, 37, "x"], [279, 40, 301, 38], [279, 42, 301, 40], [279, 44, 301, 42], [280, 10, 302, 8], [280, 16, 302, 14, "index"], [280, 21, 302, 19], [280, 24, 302, 22], [280, 25, 302, 23, "y"], [280, 26, 302, 24], [280, 29, 302, 27, "width"], [280, 34, 302, 32], [280, 37, 302, 35, "x"], [280, 38, 302, 36], [280, 42, 302, 40], [280, 43, 302, 41], [282, 10, 304, 8], [283, 10, 305, 8], [283, 14, 305, 12, "r"], [283, 15, 305, 13], [283, 18, 305, 16], [283, 19, 305, 17], [284, 12, 305, 19, "g"], [284, 13, 305, 20], [284, 16, 305, 23], [284, 17, 305, 24], [285, 12, 305, 26, "b"], [285, 13, 305, 27], [285, 16, 305, 30], [285, 17, 305, 31], [286, 10, 306, 8], [286, 15, 306, 13], [286, 19, 306, 17, "dy"], [286, 21, 306, 19], [286, 24, 306, 22], [286, 25, 306, 23], [286, 26, 306, 24], [286, 28, 306, 26, "dy"], [286, 30, 306, 28], [286, 34, 306, 32], [286, 35, 306, 33], [286, 37, 306, 35, "dy"], [286, 39, 306, 37], [286, 41, 306, 39], [286, 43, 306, 41], [287, 12, 307, 10], [287, 17, 307, 15], [287, 21, 307, 19, "dx"], [287, 23, 307, 21], [287, 26, 307, 24], [287, 27, 307, 25], [287, 28, 307, 26], [287, 30, 307, 28, "dx"], [287, 32, 307, 30], [287, 36, 307, 34], [287, 37, 307, 35], [287, 39, 307, 37, "dx"], [287, 41, 307, 39], [287, 43, 307, 41], [287, 45, 307, 43], [288, 14, 308, 12], [288, 20, 308, 18, "neighborIndex"], [288, 33, 308, 31], [288, 36, 308, 34], [288, 37, 308, 35], [288, 38, 308, 36, "y"], [288, 39, 308, 37], [288, 42, 308, 40, "dy"], [288, 44, 308, 42], [288, 48, 308, 46, "width"], [288, 53, 308, 51], [288, 57, 308, 55, "x"], [288, 58, 308, 56], [288, 61, 308, 59, "dx"], [288, 63, 308, 61], [288, 64, 308, 62], [288, 68, 308, 66], [288, 69, 308, 67], [289, 14, 309, 12, "r"], [289, 15, 309, 13], [289, 19, 309, 17, "original"], [289, 27, 309, 25], [289, 28, 309, 26, "neighborIndex"], [289, 41, 309, 39], [289, 42, 309, 40], [290, 14, 310, 12, "g"], [290, 15, 310, 13], [290, 19, 310, 17, "original"], [290, 27, 310, 25], [290, 28, 310, 26, "neighborIndex"], [290, 41, 310, 39], [290, 44, 310, 42], [290, 45, 310, 43], [290, 46, 310, 44], [291, 14, 311, 12, "b"], [291, 15, 311, 13], [291, 19, 311, 17, "original"], [291, 27, 311, 25], [291, 28, 311, 26, "neighborIndex"], [291, 41, 311, 39], [291, 44, 311, 42], [291, 45, 311, 43], [291, 46, 311, 44], [292, 12, 312, 10], [293, 10, 313, 8], [294, 10, 315, 8, "data"], [294, 14, 315, 12], [294, 15, 315, 13, "index"], [294, 20, 315, 18], [294, 21, 315, 19], [294, 24, 315, 22, "r"], [294, 25, 315, 23], [294, 28, 315, 26], [294, 29, 315, 27], [295, 10, 316, 8, "data"], [295, 14, 316, 12], [295, 15, 316, 13, "index"], [295, 20, 316, 18], [295, 23, 316, 21], [295, 24, 316, 22], [295, 25, 316, 23], [295, 28, 316, 26, "g"], [295, 29, 316, 27], [295, 32, 316, 30], [295, 33, 316, 31], [296, 10, 317, 8, "data"], [296, 14, 317, 12], [296, 15, 317, 13, "index"], [296, 20, 317, 18], [296, 23, 317, 21], [296, 24, 317, 22], [296, 25, 317, 23], [296, 28, 317, 26, "b"], [296, 29, 317, 27], [296, 32, 317, 30], [296, 33, 317, 31], [297, 8, 318, 6], [298, 6, 319, 4], [299, 4, 320, 2], [299, 5, 320, 3], [300, 4, 322, 2], [300, 10, 322, 8, "applyFallbackFaceBlur"], [300, 31, 322, 29], [300, 34, 322, 32, "applyFallbackFaceBlur"], [300, 35, 322, 33, "ctx"], [300, 38, 322, 62], [300, 40, 322, 64, "imgWidth"], [300, 48, 322, 80], [300, 50, 322, 82, "imgHeight"], [300, 59, 322, 99], [300, 64, 322, 104], [301, 6, 323, 4, "console"], [301, 13, 323, 11], [301, 14, 323, 12, "log"], [301, 17, 323, 15], [301, 18, 323, 16], [301, 90, 323, 88], [301, 91, 323, 89], [303, 6, 325, 4], [304, 6, 326, 4], [304, 12, 326, 10, "areas"], [304, 17, 326, 15], [304, 20, 326, 18], [305, 6, 327, 6], [306, 6, 328, 6], [307, 8, 328, 8, "x"], [307, 9, 328, 9], [307, 11, 328, 11, "imgWidth"], [307, 19, 328, 19], [307, 22, 328, 22], [307, 26, 328, 26], [308, 8, 328, 28, "y"], [308, 9, 328, 29], [308, 11, 328, 31, "imgHeight"], [308, 20, 328, 40], [308, 23, 328, 43], [308, 27, 328, 47], [309, 8, 328, 49, "w"], [309, 9, 328, 50], [309, 11, 328, 52, "imgWidth"], [309, 19, 328, 60], [309, 22, 328, 63], [309, 25, 328, 66], [310, 8, 328, 68, "h"], [310, 9, 328, 69], [310, 11, 328, 71, "imgHeight"], [310, 20, 328, 80], [310, 23, 328, 83], [311, 6, 328, 87], [311, 7, 328, 88], [312, 6, 329, 6], [313, 6, 330, 6], [314, 8, 330, 8, "x"], [314, 9, 330, 9], [314, 11, 330, 11, "imgWidth"], [314, 19, 330, 19], [314, 22, 330, 22], [314, 25, 330, 25], [315, 8, 330, 27, "y"], [315, 9, 330, 28], [315, 11, 330, 30, "imgHeight"], [315, 20, 330, 39], [315, 23, 330, 42], [315, 26, 330, 45], [316, 8, 330, 47, "w"], [316, 9, 330, 48], [316, 11, 330, 50, "imgWidth"], [316, 19, 330, 58], [316, 22, 330, 61], [316, 26, 330, 65], [317, 8, 330, 67, "h"], [317, 9, 330, 68], [317, 11, 330, 70, "imgHeight"], [317, 20, 330, 79], [317, 23, 330, 82], [318, 6, 330, 86], [318, 7, 330, 87], [319, 6, 331, 6], [320, 6, 332, 6], [321, 8, 332, 8, "x"], [321, 9, 332, 9], [321, 11, 332, 11, "imgWidth"], [321, 19, 332, 19], [321, 22, 332, 22], [321, 26, 332, 26], [322, 8, 332, 28, "y"], [322, 9, 332, 29], [322, 11, 332, 31, "imgHeight"], [322, 20, 332, 40], [322, 23, 332, 43], [322, 26, 332, 46], [323, 8, 332, 48, "w"], [323, 9, 332, 49], [323, 11, 332, 51, "imgWidth"], [323, 19, 332, 59], [323, 22, 332, 62], [323, 26, 332, 66], [324, 8, 332, 68, "h"], [324, 9, 332, 69], [324, 11, 332, 71, "imgHeight"], [324, 20, 332, 80], [324, 23, 332, 83], [325, 6, 332, 87], [325, 7, 332, 88], [325, 8, 333, 5], [326, 6, 335, 4, "areas"], [326, 11, 335, 9], [326, 12, 335, 10, "for<PERSON>ach"], [326, 19, 335, 17], [326, 20, 335, 18], [326, 21, 335, 19, "area"], [326, 25, 335, 23], [326, 27, 335, 25, "index"], [326, 32, 335, 30], [326, 37, 335, 35], [327, 8, 336, 6, "console"], [327, 15, 336, 13], [327, 16, 336, 14, "log"], [327, 19, 336, 17], [327, 20, 336, 18], [327, 65, 336, 63, "index"], [327, 70, 336, 68], [327, 73, 336, 71], [327, 74, 336, 72], [327, 77, 336, 75], [327, 79, 336, 77, "area"], [327, 83, 336, 81], [327, 84, 336, 82], [328, 8, 337, 6, "applyStrongBlur"], [328, 23, 337, 21], [328, 24, 337, 22, "ctx"], [328, 27, 337, 25], [328, 29, 337, 27, "area"], [328, 33, 337, 31], [328, 34, 337, 32, "x"], [328, 35, 337, 33], [328, 37, 337, 35, "area"], [328, 41, 337, 39], [328, 42, 337, 40, "y"], [328, 43, 337, 41], [328, 45, 337, 43, "area"], [328, 49, 337, 47], [328, 50, 337, 48, "w"], [328, 51, 337, 49], [328, 53, 337, 51, "area"], [328, 57, 337, 55], [328, 58, 337, 56, "h"], [328, 59, 337, 57], [328, 60, 337, 58], [329, 6, 338, 4], [329, 7, 338, 5], [329, 8, 338, 6], [330, 4, 339, 2], [330, 5, 339, 3], [332, 4, 341, 2], [333, 4, 342, 2], [333, 10, 342, 8, "capturePhoto"], [333, 22, 342, 20], [333, 25, 342, 23], [333, 29, 342, 23, "useCallback"], [333, 47, 342, 34], [333, 49, 342, 35], [333, 61, 342, 47], [334, 6, 343, 4], [335, 6, 344, 4], [335, 12, 344, 10, "isDev"], [335, 17, 344, 15], [335, 20, 344, 18, "process"], [335, 27, 344, 25], [335, 28, 344, 26, "env"], [335, 31, 344, 29], [335, 32, 344, 30, "NODE_ENV"], [335, 40, 344, 38], [335, 45, 344, 43], [335, 58, 344, 56], [335, 62, 344, 60, "__DEV__"], [335, 69, 344, 67], [336, 6, 346, 4], [336, 10, 346, 8], [336, 11, 346, 9, "cameraRef"], [336, 20, 346, 18], [336, 21, 346, 19, "current"], [336, 28, 346, 26], [336, 32, 346, 30], [336, 33, 346, 31, "isDev"], [336, 38, 346, 36], [336, 40, 346, 38], [337, 8, 347, 6, "<PERSON><PERSON>"], [337, 22, 347, 11], [337, 23, 347, 12, "alert"], [337, 28, 347, 17], [337, 29, 347, 18], [337, 36, 347, 25], [337, 38, 347, 27], [337, 56, 347, 45], [337, 57, 347, 46], [338, 8, 348, 6], [339, 6, 349, 4], [340, 6, 350, 4], [340, 10, 350, 8], [341, 8, 351, 6, "setProcessingState"], [341, 26, 351, 24], [341, 27, 351, 25], [341, 38, 351, 36], [341, 39, 351, 37], [342, 8, 352, 6, "setProcessingProgress"], [342, 29, 352, 27], [342, 30, 352, 28], [342, 32, 352, 30], [342, 33, 352, 31], [343, 8, 353, 6], [344, 8, 354, 6], [345, 8, 355, 6], [346, 8, 356, 6], [346, 14, 356, 12], [346, 18, 356, 16, "Promise"], [346, 25, 356, 23], [346, 26, 356, 24, "resolve"], [346, 33, 356, 31], [346, 37, 356, 35, "setTimeout"], [346, 47, 356, 45], [346, 48, 356, 46, "resolve"], [346, 55, 356, 53], [346, 57, 356, 55], [346, 59, 356, 57], [346, 60, 356, 58], [346, 61, 356, 59], [347, 8, 357, 6], [348, 8, 358, 6], [348, 12, 358, 10, "photo"], [348, 17, 358, 15], [349, 8, 360, 6], [349, 12, 360, 10], [350, 10, 361, 8, "photo"], [350, 15, 361, 13], [350, 18, 361, 16], [350, 24, 361, 22, "cameraRef"], [350, 33, 361, 31], [350, 34, 361, 32, "current"], [350, 41, 361, 39], [350, 42, 361, 40, "takePictureAsync"], [350, 58, 361, 56], [350, 59, 361, 57], [351, 12, 362, 10, "quality"], [351, 19, 362, 17], [351, 21, 362, 19], [351, 24, 362, 22], [352, 12, 363, 10, "base64"], [352, 18, 363, 16], [352, 20, 363, 18], [352, 25, 363, 23], [353, 12, 364, 10, "skipProcessing"], [353, 26, 364, 24], [353, 28, 364, 26], [353, 32, 364, 30], [353, 33, 364, 32], [354, 10, 365, 8], [354, 11, 365, 9], [354, 12, 365, 10], [355, 8, 366, 6], [355, 9, 366, 7], [355, 10, 366, 8], [355, 17, 366, 15, "cameraError"], [355, 28, 366, 26], [355, 30, 366, 28], [356, 10, 367, 8, "console"], [356, 17, 367, 15], [356, 18, 367, 16, "log"], [356, 21, 367, 19], [356, 22, 367, 20], [356, 82, 367, 80], [356, 84, 367, 82, "cameraError"], [356, 95, 367, 93], [356, 96, 367, 94], [357, 10, 368, 8], [358, 10, 369, 8], [358, 14, 369, 12, "isDev"], [358, 19, 369, 17], [358, 21, 369, 19], [359, 12, 370, 10, "photo"], [359, 17, 370, 15], [359, 20, 370, 18], [360, 14, 371, 12, "uri"], [360, 17, 371, 15], [360, 19, 371, 17], [361, 12, 372, 10], [361, 13, 372, 11], [362, 10, 373, 8], [362, 11, 373, 9], [362, 17, 373, 15], [363, 12, 374, 10], [363, 18, 374, 16, "cameraError"], [363, 29, 374, 27], [364, 10, 375, 8], [365, 8, 376, 6], [366, 8, 377, 6], [366, 12, 377, 10], [366, 13, 377, 11, "photo"], [366, 18, 377, 16], [366, 20, 377, 18], [367, 10, 378, 8], [367, 16, 378, 14], [367, 20, 378, 18, "Error"], [367, 25, 378, 23], [367, 26, 378, 24], [367, 51, 378, 49], [367, 52, 378, 50], [368, 8, 379, 6], [369, 8, 380, 6, "console"], [369, 15, 380, 13], [369, 16, 380, 14, "log"], [369, 19, 380, 17], [369, 20, 380, 18], [369, 56, 380, 54], [369, 58, 380, 56, "photo"], [369, 63, 380, 61], [369, 64, 380, 62, "uri"], [369, 67, 380, 65], [369, 68, 380, 66], [370, 8, 381, 6, "setCapturedPhoto"], [370, 24, 381, 22], [370, 25, 381, 23, "photo"], [370, 30, 381, 28], [370, 31, 381, 29, "uri"], [370, 34, 381, 32], [370, 35, 381, 33], [371, 8, 382, 6, "setProcessingProgress"], [371, 29, 382, 27], [371, 30, 382, 28], [371, 32, 382, 30], [371, 33, 382, 31], [372, 8, 383, 6], [373, 8, 384, 6, "console"], [373, 15, 384, 13], [373, 16, 384, 14, "log"], [373, 19, 384, 17], [373, 20, 384, 18], [373, 73, 384, 71], [373, 74, 384, 72], [374, 8, 385, 6], [374, 14, 385, 12, "processImageWithFaceBlur"], [374, 38, 385, 36], [374, 39, 385, 37, "photo"], [374, 44, 385, 42], [374, 45, 385, 43, "uri"], [374, 48, 385, 46], [374, 49, 385, 47], [375, 8, 386, 6, "console"], [375, 15, 386, 13], [375, 16, 386, 14, "log"], [375, 19, 386, 17], [375, 20, 386, 18], [375, 71, 386, 69], [375, 72, 386, 70], [376, 6, 387, 4], [376, 7, 387, 5], [376, 8, 387, 6], [376, 15, 387, 13, "error"], [376, 20, 387, 18], [376, 22, 387, 20], [377, 8, 388, 6, "console"], [377, 15, 388, 13], [377, 16, 388, 14, "error"], [377, 21, 388, 19], [377, 22, 388, 20], [377, 54, 388, 52], [377, 56, 388, 54, "error"], [377, 61, 388, 59], [377, 62, 388, 60], [378, 8, 389, 6, "setErrorMessage"], [378, 23, 389, 21], [378, 24, 389, 22], [378, 68, 389, 66], [378, 69, 389, 67], [379, 8, 390, 6, "setProcessingState"], [379, 26, 390, 24], [379, 27, 390, 25], [379, 34, 390, 32], [379, 35, 390, 33], [380, 6, 391, 4], [381, 4, 392, 2], [381, 5, 392, 3], [381, 7, 392, 5], [381, 9, 392, 7], [381, 10, 392, 8], [382, 4, 393, 2], [383, 4, 394, 2], [383, 10, 394, 8, "processImageWithFaceBlur"], [383, 34, 394, 32], [383, 37, 394, 35], [383, 43, 394, 42, "photoUri"], [383, 51, 394, 58], [383, 55, 394, 63], [384, 6, 395, 4], [384, 10, 395, 8], [385, 8, 396, 6, "console"], [385, 15, 396, 13], [385, 16, 396, 14, "log"], [385, 19, 396, 17], [385, 20, 396, 18], [385, 84, 396, 82], [385, 85, 396, 83], [386, 8, 397, 6, "setProcessingState"], [386, 26, 397, 24], [386, 27, 397, 25], [386, 39, 397, 37], [386, 40, 397, 38], [387, 8, 398, 6, "setProcessingProgress"], [387, 29, 398, 27], [387, 30, 398, 28], [387, 32, 398, 30], [387, 33, 398, 31], [389, 8, 400, 6], [390, 8, 401, 6], [390, 14, 401, 12, "canvas"], [390, 20, 401, 18], [390, 23, 401, 21, "document"], [390, 31, 401, 29], [390, 32, 401, 30, "createElement"], [390, 45, 401, 43], [390, 46, 401, 44], [390, 54, 401, 52], [390, 55, 401, 53], [391, 8, 402, 6], [391, 14, 402, 12, "ctx"], [391, 17, 402, 15], [391, 20, 402, 18, "canvas"], [391, 26, 402, 24], [391, 27, 402, 25, "getContext"], [391, 37, 402, 35], [391, 38, 402, 36], [391, 42, 402, 40], [391, 43, 402, 41], [392, 8, 403, 6], [392, 12, 403, 10], [392, 13, 403, 11, "ctx"], [392, 16, 403, 14], [392, 18, 403, 16], [392, 24, 403, 22], [392, 28, 403, 26, "Error"], [392, 33, 403, 31], [392, 34, 403, 32], [392, 64, 403, 62], [392, 65, 403, 63], [394, 8, 405, 6], [395, 8, 406, 6], [395, 14, 406, 12, "img"], [395, 17, 406, 15], [395, 20, 406, 18], [395, 24, 406, 22, "Image"], [395, 29, 406, 27], [395, 30, 406, 28], [395, 31, 406, 29], [396, 8, 407, 6], [396, 14, 407, 12], [396, 18, 407, 16, "Promise"], [396, 25, 407, 23], [396, 26, 407, 24], [396, 27, 407, 25, "resolve"], [396, 34, 407, 32], [396, 36, 407, 34, "reject"], [396, 42, 407, 40], [396, 47, 407, 45], [397, 10, 408, 8, "img"], [397, 13, 408, 11], [397, 14, 408, 12, "onload"], [397, 20, 408, 18], [397, 23, 408, 21, "resolve"], [397, 30, 408, 28], [398, 10, 409, 8, "img"], [398, 13, 409, 11], [398, 14, 409, 12, "onerror"], [398, 21, 409, 19], [398, 24, 409, 22, "reject"], [398, 30, 409, 28], [399, 10, 410, 8, "img"], [399, 13, 410, 11], [399, 14, 410, 12, "src"], [399, 17, 410, 15], [399, 20, 410, 18, "photoUri"], [399, 28, 410, 26], [400, 8, 411, 6], [400, 9, 411, 7], [400, 10, 411, 8], [402, 8, 413, 6], [403, 8, 414, 6, "canvas"], [403, 14, 414, 12], [403, 15, 414, 13, "width"], [403, 20, 414, 18], [403, 23, 414, 21, "img"], [403, 26, 414, 24], [403, 27, 414, 25, "width"], [403, 32, 414, 30], [404, 8, 415, 6, "canvas"], [404, 14, 415, 12], [404, 15, 415, 13, "height"], [404, 21, 415, 19], [404, 24, 415, 22, "img"], [404, 27, 415, 25], [404, 28, 415, 26, "height"], [404, 34, 415, 32], [405, 8, 416, 6, "console"], [405, 15, 416, 13], [405, 16, 416, 14, "log"], [405, 19, 416, 17], [405, 20, 416, 18], [405, 54, 416, 52], [405, 56, 416, 54], [406, 10, 416, 56, "width"], [406, 15, 416, 61], [406, 17, 416, 63, "img"], [406, 20, 416, 66], [406, 21, 416, 67, "width"], [406, 26, 416, 72], [407, 10, 416, 74, "height"], [407, 16, 416, 80], [407, 18, 416, 82, "img"], [407, 21, 416, 85], [407, 22, 416, 86, "height"], [408, 8, 416, 93], [408, 9, 416, 94], [408, 10, 416, 95], [410, 8, 418, 6], [411, 8, 419, 6, "ctx"], [411, 11, 419, 9], [411, 12, 419, 10, "drawImage"], [411, 21, 419, 19], [411, 22, 419, 20, "img"], [411, 25, 419, 23], [411, 27, 419, 25], [411, 28, 419, 26], [411, 30, 419, 28], [411, 31, 419, 29], [411, 32, 419, 30], [412, 8, 420, 6, "console"], [412, 15, 420, 13], [412, 16, 420, 14, "log"], [412, 19, 420, 17], [412, 20, 420, 18], [412, 72, 420, 70], [412, 73, 420, 71], [413, 8, 422, 6, "setProcessingProgress"], [413, 29, 422, 27], [413, 30, 422, 28], [413, 32, 422, 30], [413, 33, 422, 31], [415, 8, 424, 6], [416, 8, 425, 6], [416, 12, 425, 10, "detectedFaces"], [416, 25, 425, 23], [416, 28, 425, 26], [416, 30, 425, 28], [417, 8, 427, 6, "console"], [417, 15, 427, 13], [417, 16, 427, 14, "log"], [417, 19, 427, 17], [417, 20, 427, 18], [417, 78, 427, 76], [417, 79, 427, 77], [419, 8, 429, 6], [420, 8, 430, 6], [420, 12, 430, 10], [421, 10, 431, 8], [421, 16, 431, 14, "loadMediaPipeFaceDetection"], [421, 42, 431, 40], [421, 43, 431, 41], [421, 44, 431, 42], [422, 10, 432, 8, "detectedFaces"], [422, 23, 432, 21], [422, 26, 432, 24], [422, 32, 432, 30, "detectFacesWithMediaPipe"], [422, 56, 432, 54], [422, 57, 432, 55, "img"], [422, 60, 432, 58], [422, 61, 432, 59], [423, 10, 433, 8, "console"], [423, 17, 433, 15], [423, 18, 433, 16, "log"], [423, 21, 433, 19], [423, 22, 433, 20], [423, 59, 433, 57, "detectedFaces"], [423, 72, 433, 70], [423, 73, 433, 71, "length"], [423, 79, 433, 77], [423, 87, 433, 85], [423, 88, 433, 86], [424, 8, 434, 6], [424, 9, 434, 7], [424, 10, 434, 8], [424, 17, 434, 15, "mediaPipeError"], [424, 31, 434, 29], [424, 33, 434, 31], [425, 10, 435, 8, "console"], [425, 17, 435, 15], [425, 18, 435, 16, "warn"], [425, 22, 435, 20], [425, 23, 435, 21], [425, 60, 435, 58], [425, 62, 435, 60, "mediaPipeError"], [425, 76, 435, 74], [425, 77, 435, 75], [427, 10, 437, 8], [428, 10, 438, 8, "console"], [428, 17, 438, 15], [428, 18, 438, 16, "log"], [428, 21, 438, 19], [428, 22, 438, 20], [428, 88, 438, 86], [428, 89, 438, 87], [429, 10, 439, 8, "detectedFaces"], [429, 23, 439, 21], [429, 26, 439, 24, "detectFacesHeuristic"], [429, 46, 439, 44], [429, 47, 439, 45, "img"], [429, 50, 439, 48], [429, 52, 439, 50, "ctx"], [429, 55, 439, 53], [429, 56, 439, 54], [430, 10, 440, 8, "console"], [430, 17, 440, 15], [430, 18, 440, 16, "log"], [430, 21, 440, 19], [430, 22, 440, 20], [430, 70, 440, 68, "detectedFaces"], [430, 83, 440, 81], [430, 84, 440, 82, "length"], [430, 90, 440, 88], [430, 98, 440, 96], [430, 99, 440, 97], [431, 8, 441, 6], [432, 8, 443, 6, "console"], [432, 15, 443, 13], [432, 16, 443, 14, "log"], [432, 19, 443, 17], [432, 20, 443, 18], [432, 72, 443, 70, "detectedFaces"], [432, 85, 443, 83], [432, 86, 443, 84, "length"], [432, 92, 443, 90], [432, 100, 443, 98], [432, 101, 443, 99], [433, 8, 444, 6], [433, 12, 444, 10, "detectedFaces"], [433, 25, 444, 23], [433, 26, 444, 24, "length"], [433, 32, 444, 30], [433, 35, 444, 33], [433, 36, 444, 34], [433, 38, 444, 36], [434, 10, 445, 8, "console"], [434, 17, 445, 15], [434, 18, 445, 16, "log"], [434, 21, 445, 19], [434, 22, 445, 20], [434, 66, 445, 64], [434, 68, 445, 66, "detectedFaces"], [434, 81, 445, 79], [434, 82, 445, 80, "map"], [434, 85, 445, 83], [434, 86, 445, 84], [434, 87, 445, 85, "face"], [434, 91, 445, 89], [434, 93, 445, 91, "i"], [434, 94, 445, 92], [434, 100, 445, 98], [435, 12, 446, 10, "faceNumber"], [435, 22, 446, 20], [435, 24, 446, 22, "i"], [435, 25, 446, 23], [435, 28, 446, 26], [435, 29, 446, 27], [436, 12, 447, 10, "centerX"], [436, 19, 447, 17], [436, 21, 447, 19, "face"], [436, 25, 447, 23], [436, 26, 447, 24, "boundingBox"], [436, 37, 447, 35], [436, 38, 447, 36, "xCenter"], [436, 45, 447, 43], [437, 12, 448, 10, "centerY"], [437, 19, 448, 17], [437, 21, 448, 19, "face"], [437, 25, 448, 23], [437, 26, 448, 24, "boundingBox"], [437, 37, 448, 35], [437, 38, 448, 36, "yCenter"], [437, 45, 448, 43], [438, 12, 449, 10, "width"], [438, 17, 449, 15], [438, 19, 449, 17, "face"], [438, 23, 449, 21], [438, 24, 449, 22, "boundingBox"], [438, 35, 449, 33], [438, 36, 449, 34, "width"], [438, 41, 449, 39], [439, 12, 450, 10, "height"], [439, 18, 450, 16], [439, 20, 450, 18, "face"], [439, 24, 450, 22], [439, 25, 450, 23, "boundingBox"], [439, 36, 450, 34], [439, 37, 450, 35, "height"], [440, 10, 451, 8], [440, 11, 451, 9], [440, 12, 451, 10], [440, 13, 451, 11], [440, 14, 451, 12], [441, 8, 452, 6], [441, 9, 452, 7], [441, 15, 452, 13], [442, 10, 453, 8, "console"], [442, 17, 453, 15], [442, 18, 453, 16, "log"], [442, 21, 453, 19], [442, 22, 453, 20], [442, 91, 453, 89], [442, 92, 453, 90], [443, 8, 454, 6], [444, 8, 456, 6, "setProcessingProgress"], [444, 29, 456, 27], [444, 30, 456, 28], [444, 32, 456, 30], [444, 33, 456, 31], [446, 8, 458, 6], [447, 8, 459, 6], [447, 12, 459, 10, "detectedFaces"], [447, 25, 459, 23], [447, 26, 459, 24, "length"], [447, 32, 459, 30], [447, 35, 459, 33], [447, 36, 459, 34], [447, 38, 459, 36], [448, 10, 460, 8, "console"], [448, 17, 460, 15], [448, 18, 460, 16, "log"], [448, 21, 460, 19], [448, 22, 460, 20], [448, 61, 460, 59, "detectedFaces"], [448, 74, 460, 72], [448, 75, 460, 73, "length"], [448, 81, 460, 79], [448, 101, 460, 99], [448, 102, 460, 100], [449, 10, 462, 8, "detectedFaces"], [449, 23, 462, 21], [449, 24, 462, 22, "for<PERSON>ach"], [449, 31, 462, 29], [449, 32, 462, 30], [449, 33, 462, 31, "detection"], [449, 42, 462, 40], [449, 44, 462, 42, "index"], [449, 49, 462, 47], [449, 54, 462, 52], [450, 12, 463, 10], [450, 18, 463, 16, "bbox"], [450, 22, 463, 20], [450, 25, 463, 23, "detection"], [450, 34, 463, 32], [450, 35, 463, 33, "boundingBox"], [450, 46, 463, 44], [452, 12, 465, 10], [453, 12, 466, 10], [453, 18, 466, 16, "faceX"], [453, 23, 466, 21], [453, 26, 466, 24, "bbox"], [453, 30, 466, 28], [453, 31, 466, 29, "xCenter"], [453, 38, 466, 36], [453, 41, 466, 39, "img"], [453, 44, 466, 42], [453, 45, 466, 43, "width"], [453, 50, 466, 48], [453, 53, 466, 52, "bbox"], [453, 57, 466, 56], [453, 58, 466, 57, "width"], [453, 63, 466, 62], [453, 66, 466, 65, "img"], [453, 69, 466, 68], [453, 70, 466, 69, "width"], [453, 75, 466, 74], [453, 78, 466, 78], [453, 79, 466, 79], [454, 12, 467, 10], [454, 18, 467, 16, "faceY"], [454, 23, 467, 21], [454, 26, 467, 24, "bbox"], [454, 30, 467, 28], [454, 31, 467, 29, "yCenter"], [454, 38, 467, 36], [454, 41, 467, 39, "img"], [454, 44, 467, 42], [454, 45, 467, 43, "height"], [454, 51, 467, 49], [454, 54, 467, 53, "bbox"], [454, 58, 467, 57], [454, 59, 467, 58, "height"], [454, 65, 467, 64], [454, 68, 467, 67, "img"], [454, 71, 467, 70], [454, 72, 467, 71, "height"], [454, 78, 467, 77], [454, 81, 467, 81], [454, 82, 467, 82], [455, 12, 468, 10], [455, 18, 468, 16, "faceWidth"], [455, 27, 468, 25], [455, 30, 468, 28, "bbox"], [455, 34, 468, 32], [455, 35, 468, 33, "width"], [455, 40, 468, 38], [455, 43, 468, 41, "img"], [455, 46, 468, 44], [455, 47, 468, 45, "width"], [455, 52, 468, 50], [456, 12, 469, 10], [456, 18, 469, 16, "faceHeight"], [456, 28, 469, 26], [456, 31, 469, 29, "bbox"], [456, 35, 469, 33], [456, 36, 469, 34, "height"], [456, 42, 469, 40], [456, 45, 469, 43, "img"], [456, 48, 469, 46], [456, 49, 469, 47, "height"], [456, 55, 469, 53], [458, 12, 471, 10], [459, 12, 472, 10], [459, 18, 472, 16, "padding"], [459, 25, 472, 23], [459, 28, 472, 26], [459, 31, 472, 29], [460, 12, 473, 10], [460, 18, 473, 16, "paddedX"], [460, 25, 473, 23], [460, 28, 473, 26, "Math"], [460, 32, 473, 30], [460, 33, 473, 31, "max"], [460, 36, 473, 34], [460, 37, 473, 35], [460, 38, 473, 36], [460, 40, 473, 38, "faceX"], [460, 45, 473, 43], [460, 48, 473, 46, "faceWidth"], [460, 57, 473, 55], [460, 60, 473, 58, "padding"], [460, 67, 473, 65], [460, 68, 473, 66], [461, 12, 474, 10], [461, 18, 474, 16, "paddedY"], [461, 25, 474, 23], [461, 28, 474, 26, "Math"], [461, 32, 474, 30], [461, 33, 474, 31, "max"], [461, 36, 474, 34], [461, 37, 474, 35], [461, 38, 474, 36], [461, 40, 474, 38, "faceY"], [461, 45, 474, 43], [461, 48, 474, 46, "faceHeight"], [461, 58, 474, 56], [461, 61, 474, 59, "padding"], [461, 68, 474, 66], [461, 69, 474, 67], [462, 12, 475, 10], [462, 18, 475, 16, "<PERSON><PERSON><PERSON><PERSON>"], [462, 29, 475, 27], [462, 32, 475, 30, "Math"], [462, 36, 475, 34], [462, 37, 475, 35, "min"], [462, 40, 475, 38], [462, 41, 475, 39, "img"], [462, 44, 475, 42], [462, 45, 475, 43, "width"], [462, 50, 475, 48], [462, 53, 475, 51, "paddedX"], [462, 60, 475, 58], [462, 62, 475, 60, "faceWidth"], [462, 71, 475, 69], [462, 75, 475, 73], [462, 76, 475, 74], [462, 79, 475, 77], [462, 80, 475, 78], [462, 83, 475, 81, "padding"], [462, 90, 475, 88], [462, 91, 475, 89], [462, 92, 475, 90], [463, 12, 476, 10], [463, 18, 476, 16, "paddedHeight"], [463, 30, 476, 28], [463, 33, 476, 31, "Math"], [463, 37, 476, 35], [463, 38, 476, 36, "min"], [463, 41, 476, 39], [463, 42, 476, 40, "img"], [463, 45, 476, 43], [463, 46, 476, 44, "height"], [463, 52, 476, 50], [463, 55, 476, 53, "paddedY"], [463, 62, 476, 60], [463, 64, 476, 62, "faceHeight"], [463, 74, 476, 72], [463, 78, 476, 76], [463, 79, 476, 77], [463, 82, 476, 80], [463, 83, 476, 81], [463, 86, 476, 84, "padding"], [463, 93, 476, 91], [463, 94, 476, 92], [463, 95, 476, 93], [464, 12, 478, 10, "console"], [464, 19, 478, 17], [464, 20, 478, 18, "log"], [464, 23, 478, 21], [464, 24, 478, 22], [464, 60, 478, 58, "index"], [464, 65, 478, 63], [464, 68, 478, 66], [464, 69, 478, 67], [464, 72, 478, 70], [464, 74, 478, 72], [465, 14, 479, 12, "original"], [465, 22, 479, 20], [465, 24, 479, 22], [466, 16, 479, 24, "x"], [466, 17, 479, 25], [466, 19, 479, 27, "Math"], [466, 23, 479, 31], [466, 24, 479, 32, "round"], [466, 29, 479, 37], [466, 30, 479, 38, "faceX"], [466, 35, 479, 43], [466, 36, 479, 44], [467, 16, 479, 46, "y"], [467, 17, 479, 47], [467, 19, 479, 49, "Math"], [467, 23, 479, 53], [467, 24, 479, 54, "round"], [467, 29, 479, 59], [467, 30, 479, 60, "faceY"], [467, 35, 479, 65], [467, 36, 479, 66], [468, 16, 479, 68, "w"], [468, 17, 479, 69], [468, 19, 479, 71, "Math"], [468, 23, 479, 75], [468, 24, 479, 76, "round"], [468, 29, 479, 81], [468, 30, 479, 82, "faceWidth"], [468, 39, 479, 91], [468, 40, 479, 92], [469, 16, 479, 94, "h"], [469, 17, 479, 95], [469, 19, 479, 97, "Math"], [469, 23, 479, 101], [469, 24, 479, 102, "round"], [469, 29, 479, 107], [469, 30, 479, 108, "faceHeight"], [469, 40, 479, 118], [470, 14, 479, 120], [470, 15, 479, 121], [471, 14, 480, 12, "padded"], [471, 20, 480, 18], [471, 22, 480, 20], [472, 16, 480, 22, "x"], [472, 17, 480, 23], [472, 19, 480, 25, "Math"], [472, 23, 480, 29], [472, 24, 480, 30, "round"], [472, 29, 480, 35], [472, 30, 480, 36, "paddedX"], [472, 37, 480, 43], [472, 38, 480, 44], [473, 16, 480, 46, "y"], [473, 17, 480, 47], [473, 19, 480, 49, "Math"], [473, 23, 480, 53], [473, 24, 480, 54, "round"], [473, 29, 480, 59], [473, 30, 480, 60, "paddedY"], [473, 37, 480, 67], [473, 38, 480, 68], [474, 16, 480, 70, "w"], [474, 17, 480, 71], [474, 19, 480, 73, "Math"], [474, 23, 480, 77], [474, 24, 480, 78, "round"], [474, 29, 480, 83], [474, 30, 480, 84, "<PERSON><PERSON><PERSON><PERSON>"], [474, 41, 480, 95], [474, 42, 480, 96], [475, 16, 480, 98, "h"], [475, 17, 480, 99], [475, 19, 480, 101, "Math"], [475, 23, 480, 105], [475, 24, 480, 106, "round"], [475, 29, 480, 111], [475, 30, 480, 112, "paddedHeight"], [475, 42, 480, 124], [476, 14, 480, 126], [477, 12, 481, 10], [477, 13, 481, 11], [477, 14, 481, 12], [479, 12, 483, 10], [480, 12, 484, 10, "applyStrongBlur"], [480, 27, 484, 25], [480, 28, 484, 26, "ctx"], [480, 31, 484, 29], [480, 33, 484, 31, "paddedX"], [480, 40, 484, 38], [480, 42, 484, 40, "paddedY"], [480, 49, 484, 47], [480, 51, 484, 49, "<PERSON><PERSON><PERSON><PERSON>"], [480, 62, 484, 60], [480, 64, 484, 62, "paddedHeight"], [480, 76, 484, 74], [480, 77, 484, 75], [481, 12, 485, 10, "console"], [481, 19, 485, 17], [481, 20, 485, 18, "log"], [481, 23, 485, 21], [481, 24, 485, 22], [481, 50, 485, 48, "index"], [481, 55, 485, 53], [481, 58, 485, 56], [481, 59, 485, 57], [481, 79, 485, 77], [481, 80, 485, 78], [482, 10, 486, 8], [482, 11, 486, 9], [482, 12, 486, 10], [483, 10, 488, 8, "console"], [483, 17, 488, 15], [483, 18, 488, 16, "log"], [483, 21, 488, 19], [483, 22, 488, 20], [483, 48, 488, 46, "detectedFaces"], [483, 61, 488, 59], [483, 62, 488, 60, "length"], [483, 68, 488, 66], [483, 104, 488, 102], [483, 105, 488, 103], [484, 8, 489, 6], [484, 9, 489, 7], [484, 15, 489, 13], [485, 10, 490, 8, "console"], [485, 17, 490, 15], [485, 18, 490, 16, "log"], [485, 21, 490, 19], [485, 22, 490, 20], [485, 109, 490, 107], [485, 110, 490, 108], [486, 10, 491, 8], [487, 10, 492, 8, "applyFallbackFaceBlur"], [487, 31, 492, 29], [487, 32, 492, 30, "ctx"], [487, 35, 492, 33], [487, 37, 492, 35, "img"], [487, 40, 492, 38], [487, 41, 492, 39, "width"], [487, 46, 492, 44], [487, 48, 492, 46, "img"], [487, 51, 492, 49], [487, 52, 492, 50, "height"], [487, 58, 492, 56], [487, 59, 492, 57], [488, 8, 493, 6], [489, 8, 495, 6, "setProcessingProgress"], [489, 29, 495, 27], [489, 30, 495, 28], [489, 32, 495, 30], [489, 33, 495, 31], [491, 8, 497, 6], [492, 8, 498, 6, "console"], [492, 15, 498, 13], [492, 16, 498, 14, "log"], [492, 19, 498, 17], [492, 20, 498, 18], [492, 85, 498, 83], [492, 86, 498, 84], [493, 8, 499, 6], [493, 14, 499, 12, "blurredImageBlob"], [493, 30, 499, 28], [493, 33, 499, 31], [493, 39, 499, 37], [493, 43, 499, 41, "Promise"], [493, 50, 499, 48], [493, 51, 499, 56, "resolve"], [493, 58, 499, 63], [493, 62, 499, 68], [494, 10, 500, 8, "canvas"], [494, 16, 500, 14], [494, 17, 500, 15, "toBlob"], [494, 23, 500, 21], [494, 24, 500, 23, "blob"], [494, 28, 500, 27], [494, 32, 500, 32, "resolve"], [494, 39, 500, 39], [494, 40, 500, 40, "blob"], [494, 44, 500, 45], [494, 45, 500, 46], [494, 47, 500, 48], [494, 59, 500, 60], [494, 61, 500, 62], [494, 64, 500, 65], [494, 65, 500, 66], [495, 8, 501, 6], [495, 9, 501, 7], [495, 10, 501, 8], [496, 8, 503, 6], [496, 14, 503, 12, "blurredImageUrl"], [496, 29, 503, 27], [496, 32, 503, 30, "URL"], [496, 35, 503, 33], [496, 36, 503, 34, "createObjectURL"], [496, 51, 503, 49], [496, 52, 503, 50, "blurredImageBlob"], [496, 68, 503, 66], [496, 69, 503, 67], [497, 8, 504, 6, "console"], [497, 15, 504, 13], [497, 16, 504, 14, "log"], [497, 19, 504, 17], [497, 20, 504, 18], [497, 66, 504, 64], [497, 68, 504, 66, "blurredImageUrl"], [497, 83, 504, 81], [497, 84, 504, 82, "substring"], [497, 93, 504, 91], [497, 94, 504, 92], [497, 95, 504, 93], [497, 97, 504, 95], [497, 99, 504, 97], [497, 100, 504, 98], [497, 103, 504, 101], [497, 108, 504, 106], [497, 109, 504, 107], [498, 8, 506, 6, "setProcessingProgress"], [498, 29, 506, 27], [498, 30, 506, 28], [498, 33, 506, 31], [498, 34, 506, 32], [500, 8, 508, 6], [501, 8, 509, 6], [501, 14, 509, 12, "completeProcessing"], [501, 32, 509, 30], [501, 33, 509, 31, "blurredImageUrl"], [501, 48, 509, 46], [501, 49, 509, 47], [502, 6, 511, 4], [502, 7, 511, 5], [502, 8, 511, 6], [502, 15, 511, 13, "error"], [502, 20, 511, 18], [502, 22, 511, 20], [503, 8, 512, 6, "console"], [503, 15, 512, 13], [503, 16, 512, 14, "error"], [503, 21, 512, 19], [503, 22, 512, 20], [503, 57, 512, 55], [503, 59, 512, 57, "error"], [503, 64, 512, 62], [503, 65, 512, 63], [504, 8, 513, 6, "setErrorMessage"], [504, 23, 513, 21], [504, 24, 513, 22], [504, 50, 513, 48], [504, 51, 513, 49], [505, 8, 514, 6, "setProcessingState"], [505, 26, 514, 24], [505, 27, 514, 25], [505, 34, 514, 32], [505, 35, 514, 33], [506, 6, 515, 4], [507, 4, 516, 2], [507, 5, 516, 3], [509, 4, 518, 2], [510, 4, 519, 2], [510, 10, 519, 8, "completeProcessing"], [510, 28, 519, 26], [510, 31, 519, 29], [510, 37, 519, 36, "blurredImageUrl"], [510, 52, 519, 59], [510, 56, 519, 64], [511, 6, 520, 4], [511, 10, 520, 8], [512, 8, 521, 6, "setProcessingState"], [512, 26, 521, 24], [512, 27, 521, 25], [512, 37, 521, 35], [512, 38, 521, 36], [514, 8, 523, 6], [515, 8, 524, 6], [515, 14, 524, 12, "timestamp"], [515, 23, 524, 21], [515, 26, 524, 24, "Date"], [515, 30, 524, 28], [515, 31, 524, 29, "now"], [515, 34, 524, 32], [515, 35, 524, 33], [515, 36, 524, 34], [516, 8, 525, 6], [516, 14, 525, 12, "result"], [516, 20, 525, 18], [516, 23, 525, 21], [517, 10, 526, 8, "imageUrl"], [517, 18, 526, 16], [517, 20, 526, 18, "blurredImageUrl"], [517, 35, 526, 33], [518, 10, 527, 8, "localUri"], [518, 18, 527, 16], [518, 20, 527, 18, "blurredImageUrl"], [518, 35, 527, 33], [519, 10, 528, 8, "challengeCode"], [519, 23, 528, 21], [519, 25, 528, 23, "challengeCode"], [519, 38, 528, 36], [519, 42, 528, 40], [519, 44, 528, 42], [520, 10, 529, 8, "timestamp"], [520, 19, 529, 17], [521, 10, 530, 8, "jobId"], [521, 15, 530, 13], [521, 17, 530, 15], [521, 27, 530, 25, "timestamp"], [521, 36, 530, 34], [521, 38, 530, 36], [522, 10, 531, 8, "status"], [522, 16, 531, 14], [522, 18, 531, 16], [523, 8, 532, 6], [523, 9, 532, 7], [524, 8, 534, 6, "console"], [524, 15, 534, 13], [524, 16, 534, 14, "log"], [524, 19, 534, 17], [524, 20, 534, 18], [524, 100, 534, 98], [524, 102, 534, 100], [525, 10, 535, 8, "imageUrl"], [525, 18, 535, 16], [525, 20, 535, 18, "blurredImageUrl"], [525, 35, 535, 33], [525, 36, 535, 34, "substring"], [525, 45, 535, 43], [525, 46, 535, 44], [525, 47, 535, 45], [525, 49, 535, 47], [525, 51, 535, 49], [525, 52, 535, 50], [525, 55, 535, 53], [525, 60, 535, 58], [526, 10, 536, 8, "timestamp"], [526, 19, 536, 17], [527, 10, 537, 8, "jobId"], [527, 15, 537, 13], [527, 17, 537, 15, "result"], [527, 23, 537, 21], [527, 24, 537, 22, "jobId"], [528, 8, 538, 6], [528, 9, 538, 7], [528, 10, 538, 8], [530, 8, 540, 6], [531, 8, 541, 6, "onComplete"], [531, 18, 541, 16], [531, 19, 541, 17, "result"], [531, 25, 541, 23], [531, 26, 541, 24], [532, 6, 543, 4], [532, 7, 543, 5], [532, 8, 543, 6], [532, 15, 543, 13, "error"], [532, 20, 543, 18], [532, 22, 543, 20], [533, 8, 544, 6, "console"], [533, 15, 544, 13], [533, 16, 544, 14, "error"], [533, 21, 544, 19], [533, 22, 544, 20], [533, 57, 544, 55], [533, 59, 544, 57, "error"], [533, 64, 544, 62], [533, 65, 544, 63], [534, 8, 545, 6, "setErrorMessage"], [534, 23, 545, 21], [534, 24, 545, 22], [534, 56, 545, 54], [534, 57, 545, 55], [535, 8, 546, 6, "setProcessingState"], [535, 26, 546, 24], [535, 27, 546, 25], [535, 34, 546, 32], [535, 35, 546, 33], [536, 6, 547, 4], [537, 4, 548, 2], [537, 5, 548, 3], [539, 4, 550, 2], [540, 4, 551, 2], [540, 10, 551, 8, "triggerServerProcessing"], [540, 33, 551, 31], [540, 36, 551, 34], [540, 42, 551, 34, "triggerServerProcessing"], [540, 43, 551, 41, "privateImageUrl"], [540, 58, 551, 64], [540, 60, 551, 66, "timestamp"], [540, 69, 551, 83], [540, 74, 551, 88], [541, 6, 552, 4], [541, 10, 552, 8], [542, 8, 553, 6, "console"], [542, 15, 553, 13], [542, 16, 553, 14, "log"], [542, 19, 553, 17], [542, 20, 553, 18], [542, 74, 553, 72], [542, 76, 553, 74, "privateImageUrl"], [542, 91, 553, 89], [542, 92, 553, 90], [543, 8, 554, 6, "setProcessingState"], [543, 26, 554, 24], [543, 27, 554, 25], [543, 39, 554, 37], [543, 40, 554, 38], [544, 8, 555, 6, "setProcessingProgress"], [544, 29, 555, 27], [544, 30, 555, 28], [544, 32, 555, 30], [544, 33, 555, 31], [545, 8, 557, 6], [545, 14, 557, 12, "requestBody"], [545, 25, 557, 23], [545, 28, 557, 26], [546, 10, 558, 8, "imageUrl"], [546, 18, 558, 16], [546, 20, 558, 18, "privateImageUrl"], [546, 35, 558, 33], [547, 10, 559, 8, "userId"], [547, 16, 559, 14], [548, 10, 560, 8, "requestId"], [548, 19, 560, 17], [549, 10, 561, 8, "timestamp"], [549, 19, 561, 17], [550, 10, 562, 8, "platform"], [550, 18, 562, 16], [550, 20, 562, 18], [551, 8, 563, 6], [551, 9, 563, 7], [552, 8, 565, 6, "console"], [552, 15, 565, 13], [552, 16, 565, 14, "log"], [552, 19, 565, 17], [552, 20, 565, 18], [552, 65, 565, 63], [552, 67, 565, 65, "requestBody"], [552, 78, 565, 76], [552, 79, 565, 77], [554, 8, 567, 6], [555, 8, 568, 6], [555, 14, 568, 12, "response"], [555, 22, 568, 20], [555, 25, 568, 23], [555, 31, 568, 29, "fetch"], [555, 36, 568, 34], [555, 37, 568, 35], [555, 40, 568, 38, "API_BASE_URL"], [555, 52, 568, 50], [555, 72, 568, 70], [555, 74, 568, 72], [556, 10, 569, 8, "method"], [556, 16, 569, 14], [556, 18, 569, 16], [556, 24, 569, 22], [557, 10, 570, 8, "headers"], [557, 17, 570, 15], [557, 19, 570, 17], [558, 12, 571, 10], [558, 26, 571, 24], [558, 28, 571, 26], [558, 46, 571, 44], [559, 12, 572, 10], [559, 27, 572, 25], [559, 29, 572, 27], [559, 39, 572, 37], [559, 45, 572, 43, "getAuthToken"], [559, 57, 572, 55], [559, 58, 572, 56], [559, 59, 572, 57], [560, 10, 573, 8], [560, 11, 573, 9], [561, 10, 574, 8, "body"], [561, 14, 574, 12], [561, 16, 574, 14, "JSON"], [561, 20, 574, 18], [561, 21, 574, 19, "stringify"], [561, 30, 574, 28], [561, 31, 574, 29, "requestBody"], [561, 42, 574, 40], [562, 8, 575, 6], [562, 9, 575, 7], [562, 10, 575, 8], [563, 8, 577, 6], [563, 12, 577, 10], [563, 13, 577, 11, "response"], [563, 21, 577, 19], [563, 22, 577, 20, "ok"], [563, 24, 577, 22], [563, 26, 577, 24], [564, 10, 578, 8], [564, 16, 578, 14, "errorText"], [564, 25, 578, 23], [564, 28, 578, 26], [564, 34, 578, 32, "response"], [564, 42, 578, 40], [564, 43, 578, 41, "text"], [564, 47, 578, 45], [564, 48, 578, 46], [564, 49, 578, 47], [565, 10, 579, 8, "console"], [565, 17, 579, 15], [565, 18, 579, 16, "error"], [565, 23, 579, 21], [565, 24, 579, 22], [565, 68, 579, 66], [565, 70, 579, 68, "response"], [565, 78, 579, 76], [565, 79, 579, 77, "status"], [565, 85, 579, 83], [565, 87, 579, 85, "errorText"], [565, 96, 579, 94], [565, 97, 579, 95], [566, 10, 580, 8], [566, 16, 580, 14], [566, 20, 580, 18, "Error"], [566, 25, 580, 23], [566, 26, 580, 24], [566, 48, 580, 46, "response"], [566, 56, 580, 54], [566, 57, 580, 55, "status"], [566, 63, 580, 61], [566, 67, 580, 65, "response"], [566, 75, 580, 73], [566, 76, 580, 74, "statusText"], [566, 86, 580, 84], [566, 88, 580, 86], [566, 89, 580, 87], [567, 8, 581, 6], [568, 8, 583, 6], [568, 14, 583, 12, "result"], [568, 20, 583, 18], [568, 23, 583, 21], [568, 29, 583, 27, "response"], [568, 37, 583, 35], [568, 38, 583, 36, "json"], [568, 42, 583, 40], [568, 43, 583, 41], [568, 44, 583, 42], [569, 8, 584, 6, "console"], [569, 15, 584, 13], [569, 16, 584, 14, "log"], [569, 19, 584, 17], [569, 20, 584, 18], [569, 68, 584, 66], [569, 70, 584, 68, "result"], [569, 76, 584, 74], [569, 77, 584, 75], [570, 8, 586, 6], [570, 12, 586, 10], [570, 13, 586, 11, "result"], [570, 19, 586, 17], [570, 20, 586, 18, "jobId"], [570, 25, 586, 23], [570, 27, 586, 25], [571, 10, 587, 8], [571, 16, 587, 14], [571, 20, 587, 18, "Error"], [571, 25, 587, 23], [571, 26, 587, 24], [571, 70, 587, 68], [571, 71, 587, 69], [572, 8, 588, 6], [574, 8, 590, 6], [575, 8, 591, 6], [575, 14, 591, 12, "pollForCompletion"], [575, 31, 591, 29], [575, 32, 591, 30, "result"], [575, 38, 591, 36], [575, 39, 591, 37, "jobId"], [575, 44, 591, 42], [575, 46, 591, 44, "timestamp"], [575, 55, 591, 53], [575, 56, 591, 54], [576, 6, 592, 4], [576, 7, 592, 5], [576, 8, 592, 6], [576, 15, 592, 13, "error"], [576, 20, 592, 18], [576, 22, 592, 20], [577, 8, 593, 6, "console"], [577, 15, 593, 13], [577, 16, 593, 14, "error"], [577, 21, 593, 19], [577, 22, 593, 20], [577, 57, 593, 55], [577, 59, 593, 57, "error"], [577, 64, 593, 62], [577, 65, 593, 63], [578, 8, 594, 6, "setErrorMessage"], [578, 23, 594, 21], [578, 24, 594, 22], [578, 52, 594, 50, "error"], [578, 57, 594, 55], [578, 58, 594, 56, "message"], [578, 65, 594, 63], [578, 67, 594, 65], [578, 68, 594, 66], [579, 8, 595, 6, "setProcessingState"], [579, 26, 595, 24], [579, 27, 595, 25], [579, 34, 595, 32], [579, 35, 595, 33], [580, 6, 596, 4], [581, 4, 597, 2], [581, 5, 597, 3], [582, 4, 598, 2], [583, 4, 599, 2], [583, 10, 599, 8, "pollForCompletion"], [583, 27, 599, 25], [583, 30, 599, 28], [583, 36, 599, 28, "pollForCompletion"], [583, 37, 599, 35, "jobId"], [583, 42, 599, 48], [583, 44, 599, 50, "timestamp"], [583, 53, 599, 67], [583, 55, 599, 69, "attempts"], [583, 63, 599, 77], [583, 66, 599, 80], [583, 67, 599, 81], [583, 72, 599, 86], [584, 6, 600, 4], [584, 12, 600, 10, "MAX_ATTEMPTS"], [584, 24, 600, 22], [584, 27, 600, 25], [584, 29, 600, 27], [584, 30, 600, 28], [584, 31, 600, 29], [585, 6, 601, 4], [585, 12, 601, 10, "POLL_INTERVAL"], [585, 25, 601, 23], [585, 28, 601, 26], [585, 32, 601, 30], [585, 33, 601, 31], [585, 34, 601, 32], [587, 6, 603, 4, "console"], [587, 13, 603, 11], [587, 14, 603, 12, "log"], [587, 17, 603, 15], [587, 18, 603, 16], [587, 53, 603, 51, "attempts"], [587, 61, 603, 59], [587, 64, 603, 62], [587, 65, 603, 63], [587, 69, 603, 67, "MAX_ATTEMPTS"], [587, 81, 603, 79], [587, 93, 603, 91, "jobId"], [587, 98, 603, 96], [587, 100, 603, 98], [587, 101, 603, 99], [588, 6, 605, 4], [588, 10, 605, 8, "attempts"], [588, 18, 605, 16], [588, 22, 605, 20, "MAX_ATTEMPTS"], [588, 34, 605, 32], [588, 36, 605, 34], [589, 8, 606, 6, "console"], [589, 15, 606, 13], [589, 16, 606, 14, "error"], [589, 21, 606, 19], [589, 22, 606, 20], [589, 75, 606, 73], [589, 76, 606, 74], [590, 8, 607, 6, "setErrorMessage"], [590, 23, 607, 21], [590, 24, 607, 22], [590, 63, 607, 61], [590, 64, 607, 62], [591, 8, 608, 6, "setProcessingState"], [591, 26, 608, 24], [591, 27, 608, 25], [591, 34, 608, 32], [591, 35, 608, 33], [592, 8, 609, 6], [593, 6, 610, 4], [594, 6, 612, 4], [594, 10, 612, 8], [595, 8, 613, 6], [595, 14, 613, 12, "response"], [595, 22, 613, 20], [595, 25, 613, 23], [595, 31, 613, 29, "fetch"], [595, 36, 613, 34], [595, 37, 613, 35], [595, 40, 613, 38, "API_BASE_URL"], [595, 52, 613, 50], [595, 75, 613, 73, "jobId"], [595, 80, 613, 78], [595, 82, 613, 80], [595, 84, 613, 82], [596, 10, 614, 8, "headers"], [596, 17, 614, 15], [596, 19, 614, 17], [597, 12, 615, 10], [597, 27, 615, 25], [597, 29, 615, 27], [597, 39, 615, 37], [597, 45, 615, 43, "getAuthToken"], [597, 57, 615, 55], [597, 58, 615, 56], [597, 59, 615, 57], [598, 10, 616, 8], [599, 8, 617, 6], [599, 9, 617, 7], [599, 10, 617, 8], [600, 8, 619, 6], [600, 12, 619, 10], [600, 13, 619, 11, "response"], [600, 21, 619, 19], [600, 22, 619, 20, "ok"], [600, 24, 619, 22], [600, 26, 619, 24], [601, 10, 620, 8], [601, 16, 620, 14], [601, 20, 620, 18, "Error"], [601, 25, 620, 23], [601, 26, 620, 24], [601, 34, 620, 32, "response"], [601, 42, 620, 40], [601, 43, 620, 41, "status"], [601, 49, 620, 47], [601, 54, 620, 52, "response"], [601, 62, 620, 60], [601, 63, 620, 61, "statusText"], [601, 73, 620, 71], [601, 75, 620, 73], [601, 76, 620, 74], [602, 8, 621, 6], [603, 8, 623, 6], [603, 14, 623, 12, "status"], [603, 20, 623, 18], [603, 23, 623, 21], [603, 29, 623, 27, "response"], [603, 37, 623, 35], [603, 38, 623, 36, "json"], [603, 42, 623, 40], [603, 43, 623, 41], [603, 44, 623, 42], [604, 8, 624, 6, "console"], [604, 15, 624, 13], [604, 16, 624, 14, "log"], [604, 19, 624, 17], [604, 20, 624, 18], [604, 54, 624, 52], [604, 56, 624, 54, "status"], [604, 62, 624, 60], [604, 63, 624, 61], [605, 8, 626, 6], [605, 12, 626, 10, "status"], [605, 18, 626, 16], [605, 19, 626, 17, "status"], [605, 25, 626, 23], [605, 30, 626, 28], [605, 41, 626, 39], [605, 43, 626, 41], [606, 10, 627, 8, "console"], [606, 17, 627, 15], [606, 18, 627, 16, "log"], [606, 21, 627, 19], [606, 22, 627, 20], [606, 73, 627, 71], [606, 74, 627, 72], [607, 10, 628, 8, "setProcessingProgress"], [607, 31, 628, 29], [607, 32, 628, 30], [607, 35, 628, 33], [607, 36, 628, 34], [608, 10, 629, 8, "setProcessingState"], [608, 28, 629, 26], [608, 29, 629, 27], [608, 40, 629, 38], [608, 41, 629, 39], [609, 10, 630, 8], [610, 10, 631, 8], [610, 16, 631, 14, "result"], [610, 22, 631, 20], [610, 25, 631, 23], [611, 12, 632, 10, "imageUrl"], [611, 20, 632, 18], [611, 22, 632, 20, "status"], [611, 28, 632, 26], [611, 29, 632, 27, "publicUrl"], [611, 38, 632, 36], [612, 12, 632, 38], [613, 12, 633, 10, "localUri"], [613, 20, 633, 18], [613, 22, 633, 20, "capturedPhoto"], [613, 35, 633, 33], [613, 39, 633, 37, "status"], [613, 45, 633, 43], [613, 46, 633, 44, "publicUrl"], [613, 55, 633, 53], [614, 12, 633, 55], [615, 12, 634, 10, "challengeCode"], [615, 25, 634, 23], [615, 27, 634, 25, "challengeCode"], [615, 40, 634, 38], [615, 44, 634, 42], [615, 46, 634, 44], [616, 12, 635, 10, "timestamp"], [616, 21, 635, 19], [617, 12, 636, 10, "processingStatus"], [617, 28, 636, 26], [617, 30, 636, 28], [618, 10, 637, 8], [618, 11, 637, 9], [619, 10, 638, 8, "console"], [619, 17, 638, 15], [619, 18, 638, 16, "log"], [619, 21, 638, 19], [619, 22, 638, 20], [619, 57, 638, 55], [619, 59, 638, 57, "result"], [619, 65, 638, 63], [619, 66, 638, 64], [620, 10, 639, 8, "onComplete"], [620, 20, 639, 18], [620, 21, 639, 19, "result"], [620, 27, 639, 25], [620, 28, 639, 26], [621, 10, 640, 8], [622, 8, 641, 6], [622, 9, 641, 7], [622, 15, 641, 13], [622, 19, 641, 17, "status"], [622, 25, 641, 23], [622, 26, 641, 24, "status"], [622, 32, 641, 30], [622, 37, 641, 35], [622, 45, 641, 43], [622, 47, 641, 45], [623, 10, 642, 8, "console"], [623, 17, 642, 15], [623, 18, 642, 16, "error"], [623, 23, 642, 21], [623, 24, 642, 22], [623, 60, 642, 58], [623, 62, 642, 60, "status"], [623, 68, 642, 66], [623, 69, 642, 67, "error"], [623, 74, 642, 72], [623, 75, 642, 73], [624, 10, 643, 8], [624, 16, 643, 14], [624, 20, 643, 18, "Error"], [624, 25, 643, 23], [624, 26, 643, 24, "status"], [624, 32, 643, 30], [624, 33, 643, 31, "error"], [624, 38, 643, 36], [624, 42, 643, 40], [624, 61, 643, 59], [624, 62, 643, 60], [625, 8, 644, 6], [625, 9, 644, 7], [625, 15, 644, 13], [626, 10, 645, 8], [627, 10, 646, 8], [627, 16, 646, 14, "progressValue"], [627, 29, 646, 27], [627, 32, 646, 30], [627, 34, 646, 32], [627, 37, 646, 36, "attempts"], [627, 45, 646, 44], [627, 48, 646, 47, "MAX_ATTEMPTS"], [627, 60, 646, 59], [627, 63, 646, 63], [627, 65, 646, 65], [628, 10, 647, 8, "console"], [628, 17, 647, 15], [628, 18, 647, 16, "log"], [628, 21, 647, 19], [628, 22, 647, 20], [628, 71, 647, 69, "progressValue"], [628, 84, 647, 82], [628, 87, 647, 85], [628, 88, 647, 86], [629, 10, 648, 8, "setProcessingProgress"], [629, 31, 648, 29], [629, 32, 648, 30, "progressValue"], [629, 45, 648, 43], [629, 46, 648, 44], [630, 10, 650, 8, "setTimeout"], [630, 20, 650, 18], [630, 21, 650, 19], [630, 27, 650, 25], [631, 12, 651, 10, "pollForCompletion"], [631, 29, 651, 27], [631, 30, 651, 28, "jobId"], [631, 35, 651, 33], [631, 37, 651, 35, "timestamp"], [631, 46, 651, 44], [631, 48, 651, 46, "attempts"], [631, 56, 651, 54], [631, 59, 651, 57], [631, 60, 651, 58], [631, 61, 651, 59], [632, 10, 652, 8], [632, 11, 652, 9], [632, 13, 652, 11, "POLL_INTERVAL"], [632, 26, 652, 24], [632, 27, 652, 25], [633, 8, 653, 6], [634, 6, 654, 4], [634, 7, 654, 5], [634, 8, 654, 6], [634, 15, 654, 13, "error"], [634, 20, 654, 18], [634, 22, 654, 20], [635, 8, 655, 6, "console"], [635, 15, 655, 13], [635, 16, 655, 14, "error"], [635, 21, 655, 19], [635, 22, 655, 20], [635, 54, 655, 52], [635, 56, 655, 54, "error"], [635, 61, 655, 59], [635, 62, 655, 60], [636, 8, 656, 6, "setErrorMessage"], [636, 23, 656, 21], [636, 24, 656, 22], [636, 62, 656, 60, "error"], [636, 67, 656, 65], [636, 68, 656, 66, "message"], [636, 75, 656, 73], [636, 77, 656, 75], [636, 78, 656, 76], [637, 8, 657, 6, "setProcessingState"], [637, 26, 657, 24], [637, 27, 657, 25], [637, 34, 657, 32], [637, 35, 657, 33], [638, 6, 658, 4], [639, 4, 659, 2], [639, 5, 659, 3], [640, 4, 660, 2], [641, 4, 661, 2], [641, 10, 661, 8, "getAuthToken"], [641, 22, 661, 20], [641, 25, 661, 23], [641, 31, 661, 23, "getAuthToken"], [641, 32, 661, 23], [641, 37, 661, 52], [642, 6, 662, 4], [643, 6, 663, 4], [644, 6, 664, 4], [644, 13, 664, 11], [644, 30, 664, 28], [645, 4, 665, 2], [645, 5, 665, 3], [647, 4, 667, 2], [648, 4, 668, 2], [648, 10, 668, 8, "retryCapture"], [648, 22, 668, 20], [648, 25, 668, 23], [648, 29, 668, 23, "useCallback"], [648, 47, 668, 34], [648, 49, 668, 35], [648, 55, 668, 41], [649, 6, 669, 4, "console"], [649, 13, 669, 11], [649, 14, 669, 12, "log"], [649, 17, 669, 15], [649, 18, 669, 16], [649, 55, 669, 53], [649, 56, 669, 54], [650, 6, 670, 4, "setProcessingState"], [650, 24, 670, 22], [650, 25, 670, 23], [650, 31, 670, 29], [650, 32, 670, 30], [651, 6, 671, 4, "setErrorMessage"], [651, 21, 671, 19], [651, 22, 671, 20], [651, 24, 671, 22], [651, 25, 671, 23], [652, 6, 672, 4, "setCapturedPhoto"], [652, 22, 672, 20], [652, 23, 672, 21], [652, 25, 672, 23], [652, 26, 672, 24], [653, 6, 673, 4, "setProcessingProgress"], [653, 27, 673, 25], [653, 28, 673, 26], [653, 29, 673, 27], [653, 30, 673, 28], [654, 4, 674, 2], [654, 5, 674, 3], [654, 7, 674, 5], [654, 9, 674, 7], [654, 10, 674, 8], [655, 4, 675, 2], [656, 4, 676, 2], [656, 8, 676, 2, "useEffect"], [656, 24, 676, 11], [656, 26, 676, 12], [656, 32, 676, 18], [657, 6, 677, 4, "console"], [657, 13, 677, 11], [657, 14, 677, 12, "log"], [657, 17, 677, 15], [657, 18, 677, 16], [657, 53, 677, 51], [657, 55, 677, 53, "permission"], [657, 65, 677, 63], [657, 66, 677, 64], [658, 6, 678, 4], [658, 10, 678, 8, "permission"], [658, 20, 678, 18], [658, 22, 678, 20], [659, 8, 679, 6, "console"], [659, 15, 679, 13], [659, 16, 679, 14, "log"], [659, 19, 679, 17], [659, 20, 679, 18], [659, 57, 679, 55], [659, 59, 679, 57, "permission"], [659, 69, 679, 67], [659, 70, 679, 68, "granted"], [659, 77, 679, 75], [659, 78, 679, 76], [660, 6, 680, 4], [661, 4, 681, 2], [661, 5, 681, 3], [661, 7, 681, 5], [661, 8, 681, 6, "permission"], [661, 18, 681, 16], [661, 19, 681, 17], [661, 20, 681, 18], [662, 4, 682, 2], [663, 4, 683, 2], [663, 8, 683, 6], [663, 9, 683, 7, "permission"], [663, 19, 683, 17], [663, 21, 683, 19], [664, 6, 684, 4, "console"], [664, 13, 684, 11], [664, 14, 684, 12, "log"], [664, 17, 684, 15], [664, 18, 684, 16], [664, 67, 684, 65], [664, 68, 684, 66], [665, 6, 685, 4], [665, 26, 686, 6], [665, 30, 686, 6, "_jsxDevRuntime"], [665, 44, 686, 6], [665, 45, 686, 6, "jsxDEV"], [665, 51, 686, 6], [665, 53, 686, 7, "_View"], [665, 58, 686, 7], [665, 59, 686, 7, "default"], [665, 66, 686, 11], [666, 8, 686, 12, "style"], [666, 13, 686, 17], [666, 15, 686, 19, "styles"], [666, 21, 686, 25], [666, 22, 686, 26, "container"], [666, 31, 686, 36], [667, 8, 686, 36, "children"], [667, 16, 686, 36], [667, 32, 687, 8], [667, 36, 687, 8, "_jsxDevRuntime"], [667, 50, 687, 8], [667, 51, 687, 8, "jsxDEV"], [667, 57, 687, 8], [667, 59, 687, 9, "_ActivityIndicator"], [667, 77, 687, 9], [667, 78, 687, 9, "default"], [667, 85, 687, 26], [668, 10, 687, 27, "size"], [668, 14, 687, 31], [668, 16, 687, 32], [668, 23, 687, 39], [669, 10, 687, 40, "color"], [669, 15, 687, 45], [669, 17, 687, 46], [670, 8, 687, 55], [671, 10, 687, 55, "fileName"], [671, 18, 687, 55], [671, 20, 687, 55, "_jsxFileName"], [671, 32, 687, 55], [672, 10, 687, 55, "lineNumber"], [672, 20, 687, 55], [673, 10, 687, 55, "columnNumber"], [673, 22, 687, 55], [674, 8, 687, 55], [674, 15, 687, 57], [674, 16, 687, 58], [674, 31, 688, 8], [674, 35, 688, 8, "_jsxDevRuntime"], [674, 49, 688, 8], [674, 50, 688, 8, "jsxDEV"], [674, 56, 688, 8], [674, 58, 688, 9, "_Text"], [674, 63, 688, 9], [674, 64, 688, 9, "default"], [674, 71, 688, 13], [675, 10, 688, 14, "style"], [675, 15, 688, 19], [675, 17, 688, 21, "styles"], [675, 23, 688, 27], [675, 24, 688, 28, "loadingText"], [675, 35, 688, 40], [676, 10, 688, 40, "children"], [676, 18, 688, 40], [676, 20, 688, 41], [677, 8, 688, 58], [678, 10, 688, 58, "fileName"], [678, 18, 688, 58], [678, 20, 688, 58, "_jsxFileName"], [678, 32, 688, 58], [679, 10, 688, 58, "lineNumber"], [679, 20, 688, 58], [680, 10, 688, 58, "columnNumber"], [680, 22, 688, 58], [681, 8, 688, 58], [681, 15, 688, 64], [681, 16, 688, 65], [682, 6, 688, 65], [683, 8, 688, 65, "fileName"], [683, 16, 688, 65], [683, 18, 688, 65, "_jsxFileName"], [683, 30, 688, 65], [684, 8, 688, 65, "lineNumber"], [684, 18, 688, 65], [685, 8, 688, 65, "columnNumber"], [685, 20, 688, 65], [686, 6, 688, 65], [686, 13, 689, 12], [686, 14, 689, 13], [687, 4, 691, 2], [688, 4, 692, 2], [688, 8, 692, 6], [688, 9, 692, 7, "permission"], [688, 19, 692, 17], [688, 20, 692, 18, "granted"], [688, 27, 692, 25], [688, 29, 692, 27], [689, 6, 693, 4, "console"], [689, 13, 693, 11], [689, 14, 693, 12, "log"], [689, 17, 693, 15], [689, 18, 693, 16], [689, 93, 693, 91], [689, 94, 693, 92], [690, 6, 694, 4], [690, 26, 695, 6], [690, 30, 695, 6, "_jsxDevRuntime"], [690, 44, 695, 6], [690, 45, 695, 6, "jsxDEV"], [690, 51, 695, 6], [690, 53, 695, 7, "_View"], [690, 58, 695, 7], [690, 59, 695, 7, "default"], [690, 66, 695, 11], [691, 8, 695, 12, "style"], [691, 13, 695, 17], [691, 15, 695, 19, "styles"], [691, 21, 695, 25], [691, 22, 695, 26, "container"], [691, 31, 695, 36], [692, 8, 695, 36, "children"], [692, 16, 695, 36], [692, 31, 696, 8], [692, 35, 696, 8, "_jsxDevRuntime"], [692, 49, 696, 8], [692, 50, 696, 8, "jsxDEV"], [692, 56, 696, 8], [692, 58, 696, 9, "_View"], [692, 63, 696, 9], [692, 64, 696, 9, "default"], [692, 71, 696, 13], [693, 10, 696, 14, "style"], [693, 15, 696, 19], [693, 17, 696, 21, "styles"], [693, 23, 696, 27], [693, 24, 696, 28, "permissionContent"], [693, 41, 696, 46], [694, 10, 696, 46, "children"], [694, 18, 696, 46], [694, 34, 697, 10], [694, 38, 697, 10, "_jsxDevRuntime"], [694, 52, 697, 10], [694, 53, 697, 10, "jsxDEV"], [694, 59, 697, 10], [694, 61, 697, 11, "_lucideReactNative"], [694, 79, 697, 11], [694, 80, 697, 11, "Camera"], [694, 86, 697, 21], [695, 12, 697, 22, "size"], [695, 16, 697, 26], [695, 18, 697, 28], [695, 20, 697, 31], [696, 12, 697, 32, "color"], [696, 17, 697, 37], [696, 19, 697, 38], [697, 10, 697, 47], [698, 12, 697, 47, "fileName"], [698, 20, 697, 47], [698, 22, 697, 47, "_jsxFileName"], [698, 34, 697, 47], [699, 12, 697, 47, "lineNumber"], [699, 22, 697, 47], [700, 12, 697, 47, "columnNumber"], [700, 24, 697, 47], [701, 10, 697, 47], [701, 17, 697, 49], [701, 18, 697, 50], [701, 33, 698, 10], [701, 37, 698, 10, "_jsxDevRuntime"], [701, 51, 698, 10], [701, 52, 698, 10, "jsxDEV"], [701, 58, 698, 10], [701, 60, 698, 11, "_Text"], [701, 65, 698, 11], [701, 66, 698, 11, "default"], [701, 73, 698, 15], [702, 12, 698, 16, "style"], [702, 17, 698, 21], [702, 19, 698, 23, "styles"], [702, 25, 698, 29], [702, 26, 698, 30, "permissionTitle"], [702, 41, 698, 46], [703, 12, 698, 46, "children"], [703, 20, 698, 46], [703, 22, 698, 47], [704, 10, 698, 73], [705, 12, 698, 73, "fileName"], [705, 20, 698, 73], [705, 22, 698, 73, "_jsxFileName"], [705, 34, 698, 73], [706, 12, 698, 73, "lineNumber"], [706, 22, 698, 73], [707, 12, 698, 73, "columnNumber"], [707, 24, 698, 73], [708, 10, 698, 73], [708, 17, 698, 79], [708, 18, 698, 80], [708, 33, 699, 10], [708, 37, 699, 10, "_jsxDevRuntime"], [708, 51, 699, 10], [708, 52, 699, 10, "jsxDEV"], [708, 58, 699, 10], [708, 60, 699, 11, "_Text"], [708, 65, 699, 11], [708, 66, 699, 11, "default"], [708, 73, 699, 15], [709, 12, 699, 16, "style"], [709, 17, 699, 21], [709, 19, 699, 23, "styles"], [709, 25, 699, 29], [709, 26, 699, 30, "permissionDescription"], [709, 47, 699, 52], [710, 12, 699, 52, "children"], [710, 20, 699, 52], [710, 22, 699, 53], [711, 10, 702, 10], [712, 12, 702, 10, "fileName"], [712, 20, 702, 10], [712, 22, 702, 10, "_jsxFileName"], [712, 34, 702, 10], [713, 12, 702, 10, "lineNumber"], [713, 22, 702, 10], [714, 12, 702, 10, "columnNumber"], [714, 24, 702, 10], [715, 10, 702, 10], [715, 17, 702, 16], [715, 18, 702, 17], [715, 33, 703, 10], [715, 37, 703, 10, "_jsxDevRuntime"], [715, 51, 703, 10], [715, 52, 703, 10, "jsxDEV"], [715, 58, 703, 10], [715, 60, 703, 11, "_TouchableOpacity"], [715, 77, 703, 11], [715, 78, 703, 11, "default"], [715, 85, 703, 27], [716, 12, 703, 28, "onPress"], [716, 19, 703, 35], [716, 21, 703, 37, "requestPermission"], [716, 38, 703, 55], [717, 12, 703, 56, "style"], [717, 17, 703, 61], [717, 19, 703, 63, "styles"], [717, 25, 703, 69], [717, 26, 703, 70, "primaryButton"], [717, 39, 703, 84], [718, 12, 703, 84, "children"], [718, 20, 703, 84], [718, 35, 704, 12], [718, 39, 704, 12, "_jsxDevRuntime"], [718, 53, 704, 12], [718, 54, 704, 12, "jsxDEV"], [718, 60, 704, 12], [718, 62, 704, 13, "_Text"], [718, 67, 704, 13], [718, 68, 704, 13, "default"], [718, 75, 704, 17], [719, 14, 704, 18, "style"], [719, 19, 704, 23], [719, 21, 704, 25, "styles"], [719, 27, 704, 31], [719, 28, 704, 32, "primaryButtonText"], [719, 45, 704, 50], [720, 14, 704, 50, "children"], [720, 22, 704, 50], [720, 24, 704, 51], [721, 12, 704, 67], [722, 14, 704, 67, "fileName"], [722, 22, 704, 67], [722, 24, 704, 67, "_jsxFileName"], [722, 36, 704, 67], [723, 14, 704, 67, "lineNumber"], [723, 24, 704, 67], [724, 14, 704, 67, "columnNumber"], [724, 26, 704, 67], [725, 12, 704, 67], [725, 19, 704, 73], [726, 10, 704, 74], [727, 12, 704, 74, "fileName"], [727, 20, 704, 74], [727, 22, 704, 74, "_jsxFileName"], [727, 34, 704, 74], [728, 12, 704, 74, "lineNumber"], [728, 22, 704, 74], [729, 12, 704, 74, "columnNumber"], [729, 24, 704, 74], [730, 10, 704, 74], [730, 17, 705, 28], [730, 18, 705, 29], [730, 33, 706, 10], [730, 37, 706, 10, "_jsxDevRuntime"], [730, 51, 706, 10], [730, 52, 706, 10, "jsxDEV"], [730, 58, 706, 10], [730, 60, 706, 11, "_TouchableOpacity"], [730, 77, 706, 11], [730, 78, 706, 11, "default"], [730, 85, 706, 27], [731, 12, 706, 28, "onPress"], [731, 19, 706, 35], [731, 21, 706, 37, "onCancel"], [731, 29, 706, 46], [732, 12, 706, 47, "style"], [732, 17, 706, 52], [732, 19, 706, 54, "styles"], [732, 25, 706, 60], [732, 26, 706, 61, "secondaryButton"], [732, 41, 706, 77], [733, 12, 706, 77, "children"], [733, 20, 706, 77], [733, 35, 707, 12], [733, 39, 707, 12, "_jsxDevRuntime"], [733, 53, 707, 12], [733, 54, 707, 12, "jsxDEV"], [733, 60, 707, 12], [733, 62, 707, 13, "_Text"], [733, 67, 707, 13], [733, 68, 707, 13, "default"], [733, 75, 707, 17], [734, 14, 707, 18, "style"], [734, 19, 707, 23], [734, 21, 707, 25, "styles"], [734, 27, 707, 31], [734, 28, 707, 32, "secondaryButtonText"], [734, 47, 707, 52], [735, 14, 707, 52, "children"], [735, 22, 707, 52], [735, 24, 707, 53], [736, 12, 707, 59], [737, 14, 707, 59, "fileName"], [737, 22, 707, 59], [737, 24, 707, 59, "_jsxFileName"], [737, 36, 707, 59], [738, 14, 707, 59, "lineNumber"], [738, 24, 707, 59], [739, 14, 707, 59, "columnNumber"], [739, 26, 707, 59], [740, 12, 707, 59], [740, 19, 707, 65], [741, 10, 707, 66], [742, 12, 707, 66, "fileName"], [742, 20, 707, 66], [742, 22, 707, 66, "_jsxFileName"], [742, 34, 707, 66], [743, 12, 707, 66, "lineNumber"], [743, 22, 707, 66], [744, 12, 707, 66, "columnNumber"], [744, 24, 707, 66], [745, 10, 707, 66], [745, 17, 708, 28], [745, 18, 708, 29], [746, 8, 708, 29], [747, 10, 708, 29, "fileName"], [747, 18, 708, 29], [747, 20, 708, 29, "_jsxFileName"], [747, 32, 708, 29], [748, 10, 708, 29, "lineNumber"], [748, 20, 708, 29], [749, 10, 708, 29, "columnNumber"], [749, 22, 708, 29], [750, 8, 708, 29], [750, 15, 709, 14], [751, 6, 709, 15], [752, 8, 709, 15, "fileName"], [752, 16, 709, 15], [752, 18, 709, 15, "_jsxFileName"], [752, 30, 709, 15], [753, 8, 709, 15, "lineNumber"], [753, 18, 709, 15], [754, 8, 709, 15, "columnNumber"], [754, 20, 709, 15], [755, 6, 709, 15], [755, 13, 710, 12], [755, 14, 710, 13], [756, 4, 712, 2], [757, 4, 713, 2], [758, 4, 714, 2, "console"], [758, 11, 714, 9], [758, 12, 714, 10, "log"], [758, 15, 714, 13], [758, 16, 714, 14], [758, 55, 714, 53], [758, 56, 714, 54], [759, 4, 716, 2], [759, 24, 717, 4], [759, 28, 717, 4, "_jsxDevRuntime"], [759, 42, 717, 4], [759, 43, 717, 4, "jsxDEV"], [759, 49, 717, 4], [759, 51, 717, 5, "_View"], [759, 56, 717, 5], [759, 57, 717, 5, "default"], [759, 64, 717, 9], [760, 6, 717, 10, "style"], [760, 11, 717, 15], [760, 13, 717, 17, "styles"], [760, 19, 717, 23], [760, 20, 717, 24, "container"], [760, 29, 717, 34], [761, 6, 717, 34, "children"], [761, 14, 717, 34], [761, 30, 719, 6], [761, 34, 719, 6, "_jsxDevRuntime"], [761, 48, 719, 6], [761, 49, 719, 6, "jsxDEV"], [761, 55, 719, 6], [761, 57, 719, 7, "_View"], [761, 62, 719, 7], [761, 63, 719, 7, "default"], [761, 70, 719, 11], [762, 8, 719, 12, "style"], [762, 13, 719, 17], [762, 15, 719, 19, "styles"], [762, 21, 719, 25], [762, 22, 719, 26, "cameraContainer"], [762, 37, 719, 42], [763, 8, 719, 43, "id"], [763, 10, 719, 45], [763, 12, 719, 46], [763, 29, 719, 63], [764, 8, 719, 63, "children"], [764, 16, 719, 63], [764, 32, 720, 8], [764, 36, 720, 8, "_jsxDevRuntime"], [764, 50, 720, 8], [764, 51, 720, 8, "jsxDEV"], [764, 57, 720, 8], [764, 59, 720, 9, "_expoCamera"], [764, 70, 720, 9], [764, 71, 720, 9, "CameraView"], [764, 81, 720, 19], [765, 10, 721, 10, "ref"], [765, 13, 721, 13], [765, 15, 721, 15, "cameraRef"], [765, 24, 721, 25], [766, 10, 722, 10, "style"], [766, 15, 722, 15], [766, 17, 722, 17], [766, 18, 722, 18, "styles"], [766, 24, 722, 24], [766, 25, 722, 25, "camera"], [766, 31, 722, 31], [766, 33, 722, 33], [767, 12, 722, 35, "backgroundColor"], [767, 27, 722, 50], [767, 29, 722, 52], [768, 10, 722, 62], [768, 11, 722, 63], [768, 12, 722, 65], [769, 10, 723, 10, "facing"], [769, 16, 723, 16], [769, 18, 723, 17], [769, 24, 723, 23], [770, 10, 724, 10, "onLayout"], [770, 18, 724, 18], [770, 20, 724, 21, "e"], [770, 21, 724, 22], [770, 25, 724, 27], [771, 12, 725, 12, "console"], [771, 19, 725, 19], [771, 20, 725, 20, "log"], [771, 23, 725, 23], [771, 24, 725, 24], [771, 56, 725, 56], [771, 58, 725, 58, "e"], [771, 59, 725, 59], [771, 60, 725, 60, "nativeEvent"], [771, 71, 725, 71], [771, 72, 725, 72, "layout"], [771, 78, 725, 78], [771, 79, 725, 79], [772, 12, 726, 12, "setViewSize"], [772, 23, 726, 23], [772, 24, 726, 24], [773, 14, 726, 26, "width"], [773, 19, 726, 31], [773, 21, 726, 33, "e"], [773, 22, 726, 34], [773, 23, 726, 35, "nativeEvent"], [773, 34, 726, 46], [773, 35, 726, 47, "layout"], [773, 41, 726, 53], [773, 42, 726, 54, "width"], [773, 47, 726, 59], [774, 14, 726, 61, "height"], [774, 20, 726, 67], [774, 22, 726, 69, "e"], [774, 23, 726, 70], [774, 24, 726, 71, "nativeEvent"], [774, 35, 726, 82], [774, 36, 726, 83, "layout"], [774, 42, 726, 89], [774, 43, 726, 90, "height"], [775, 12, 726, 97], [775, 13, 726, 98], [775, 14, 726, 99], [776, 10, 727, 10], [776, 11, 727, 12], [777, 10, 728, 10, "onCameraReady"], [777, 23, 728, 23], [777, 25, 728, 25, "onCameraReady"], [777, 26, 728, 25], [777, 31, 728, 31], [778, 12, 729, 12, "console"], [778, 19, 729, 19], [778, 20, 729, 20, "log"], [778, 23, 729, 23], [778, 24, 729, 24], [778, 55, 729, 55], [778, 56, 729, 56], [779, 12, 730, 12, "setIsCameraReady"], [779, 28, 730, 28], [779, 29, 730, 29], [779, 33, 730, 33], [779, 34, 730, 34], [779, 35, 730, 35], [779, 36, 730, 36], [780, 10, 731, 10], [780, 11, 731, 12], [781, 10, 732, 10, "onMountError"], [781, 22, 732, 22], [781, 24, 732, 25, "error"], [781, 29, 732, 30], [781, 33, 732, 35], [782, 12, 733, 12, "console"], [782, 19, 733, 19], [782, 20, 733, 20, "error"], [782, 25, 733, 25], [782, 26, 733, 26], [782, 63, 733, 63], [782, 65, 733, 65, "error"], [782, 70, 733, 70], [782, 71, 733, 71], [783, 12, 734, 12, "setErrorMessage"], [783, 27, 734, 27], [783, 28, 734, 28], [783, 57, 734, 57], [783, 58, 734, 58], [784, 12, 735, 12, "setProcessingState"], [784, 30, 735, 30], [784, 31, 735, 31], [784, 38, 735, 38], [784, 39, 735, 39], [785, 10, 736, 10], [786, 8, 736, 12], [787, 10, 736, 12, "fileName"], [787, 18, 736, 12], [787, 20, 736, 12, "_jsxFileName"], [787, 32, 736, 12], [788, 10, 736, 12, "lineNumber"], [788, 20, 736, 12], [789, 10, 736, 12, "columnNumber"], [789, 22, 736, 12], [790, 8, 736, 12], [790, 15, 737, 9], [790, 16, 737, 10], [790, 18, 739, 9], [790, 19, 739, 10, "isCameraReady"], [790, 32, 739, 23], [790, 49, 740, 10], [790, 53, 740, 10, "_jsxDevRuntime"], [790, 67, 740, 10], [790, 68, 740, 10, "jsxDEV"], [790, 74, 740, 10], [790, 76, 740, 11, "_View"], [790, 81, 740, 11], [790, 82, 740, 11, "default"], [790, 89, 740, 15], [791, 10, 740, 16, "style"], [791, 15, 740, 21], [791, 17, 740, 23], [791, 18, 740, 24, "StyleSheet"], [791, 37, 740, 34], [791, 38, 740, 35, "absoluteFill"], [791, 50, 740, 47], [791, 52, 740, 49], [792, 12, 740, 51, "backgroundColor"], [792, 27, 740, 66], [792, 29, 740, 68], [792, 49, 740, 88], [793, 12, 740, 90, "justifyContent"], [793, 26, 740, 104], [793, 28, 740, 106], [793, 36, 740, 114], [794, 12, 740, 116, "alignItems"], [794, 22, 740, 126], [794, 24, 740, 128], [794, 32, 740, 136], [795, 12, 740, 138, "zIndex"], [795, 18, 740, 144], [795, 20, 740, 146], [796, 10, 740, 151], [796, 11, 740, 152], [796, 12, 740, 154], [797, 10, 740, 154, "children"], [797, 18, 740, 154], [797, 33, 741, 12], [797, 37, 741, 12, "_jsxDevRuntime"], [797, 51, 741, 12], [797, 52, 741, 12, "jsxDEV"], [797, 58, 741, 12], [797, 60, 741, 13, "_View"], [797, 65, 741, 13], [797, 66, 741, 13, "default"], [797, 73, 741, 17], [798, 12, 741, 18, "style"], [798, 17, 741, 23], [798, 19, 741, 25], [799, 14, 741, 27, "backgroundColor"], [799, 29, 741, 42], [799, 31, 741, 44], [799, 51, 741, 64], [800, 14, 741, 66, "padding"], [800, 21, 741, 73], [800, 23, 741, 75], [800, 25, 741, 77], [801, 14, 741, 79, "borderRadius"], [801, 26, 741, 91], [801, 28, 741, 93], [801, 30, 741, 95], [802, 14, 741, 97, "alignItems"], [802, 24, 741, 107], [802, 26, 741, 109], [803, 12, 741, 118], [803, 13, 741, 120], [804, 12, 741, 120, "children"], [804, 20, 741, 120], [804, 36, 742, 14], [804, 40, 742, 14, "_jsxDevRuntime"], [804, 54, 742, 14], [804, 55, 742, 14, "jsxDEV"], [804, 61, 742, 14], [804, 63, 742, 15, "_ActivityIndicator"], [804, 81, 742, 15], [804, 82, 742, 15, "default"], [804, 89, 742, 32], [805, 14, 742, 33, "size"], [805, 18, 742, 37], [805, 20, 742, 38], [805, 27, 742, 45], [806, 14, 742, 46, "color"], [806, 19, 742, 51], [806, 21, 742, 52], [806, 30, 742, 61], [807, 14, 742, 62, "style"], [807, 19, 742, 67], [807, 21, 742, 69], [808, 16, 742, 71, "marginBottom"], [808, 28, 742, 83], [808, 30, 742, 85], [809, 14, 742, 88], [810, 12, 742, 90], [811, 14, 742, 90, "fileName"], [811, 22, 742, 90], [811, 24, 742, 90, "_jsxFileName"], [811, 36, 742, 90], [812, 14, 742, 90, "lineNumber"], [812, 24, 742, 90], [813, 14, 742, 90, "columnNumber"], [813, 26, 742, 90], [814, 12, 742, 90], [814, 19, 742, 92], [814, 20, 742, 93], [814, 35, 743, 14], [814, 39, 743, 14, "_jsxDevRuntime"], [814, 53, 743, 14], [814, 54, 743, 14, "jsxDEV"], [814, 60, 743, 14], [814, 62, 743, 15, "_Text"], [814, 67, 743, 15], [814, 68, 743, 15, "default"], [814, 75, 743, 19], [815, 14, 743, 20, "style"], [815, 19, 743, 25], [815, 21, 743, 27], [816, 16, 743, 29, "color"], [816, 21, 743, 34], [816, 23, 743, 36], [816, 29, 743, 42], [817, 16, 743, 44, "fontSize"], [817, 24, 743, 52], [817, 26, 743, 54], [817, 28, 743, 56], [818, 16, 743, 58, "fontWeight"], [818, 26, 743, 68], [818, 28, 743, 70], [819, 14, 743, 76], [819, 15, 743, 78], [820, 14, 743, 78, "children"], [820, 22, 743, 78], [820, 24, 743, 79], [821, 12, 743, 101], [822, 14, 743, 101, "fileName"], [822, 22, 743, 101], [822, 24, 743, 101, "_jsxFileName"], [822, 36, 743, 101], [823, 14, 743, 101, "lineNumber"], [823, 24, 743, 101], [824, 14, 743, 101, "columnNumber"], [824, 26, 743, 101], [825, 12, 743, 101], [825, 19, 743, 107], [825, 20, 743, 108], [825, 35, 744, 14], [825, 39, 744, 14, "_jsxDevRuntime"], [825, 53, 744, 14], [825, 54, 744, 14, "jsxDEV"], [825, 60, 744, 14], [825, 62, 744, 15, "_Text"], [825, 67, 744, 15], [825, 68, 744, 15, "default"], [825, 75, 744, 19], [826, 14, 744, 20, "style"], [826, 19, 744, 25], [826, 21, 744, 27], [827, 16, 744, 29, "color"], [827, 21, 744, 34], [827, 23, 744, 36], [827, 32, 744, 45], [828, 16, 744, 47, "fontSize"], [828, 24, 744, 55], [828, 26, 744, 57], [828, 28, 744, 59], [829, 16, 744, 61, "marginTop"], [829, 25, 744, 70], [829, 27, 744, 72], [830, 14, 744, 74], [830, 15, 744, 76], [831, 14, 744, 76, "children"], [831, 22, 744, 76], [831, 24, 744, 77], [832, 12, 744, 88], [833, 14, 744, 88, "fileName"], [833, 22, 744, 88], [833, 24, 744, 88, "_jsxFileName"], [833, 36, 744, 88], [834, 14, 744, 88, "lineNumber"], [834, 24, 744, 88], [835, 14, 744, 88, "columnNumber"], [835, 26, 744, 88], [836, 12, 744, 88], [836, 19, 744, 94], [836, 20, 744, 95], [837, 10, 744, 95], [838, 12, 744, 95, "fileName"], [838, 20, 744, 95], [838, 22, 744, 95, "_jsxFileName"], [838, 34, 744, 95], [839, 12, 744, 95, "lineNumber"], [839, 22, 744, 95], [840, 12, 744, 95, "columnNumber"], [840, 24, 744, 95], [841, 10, 744, 95], [841, 17, 745, 18], [842, 8, 745, 19], [843, 10, 745, 19, "fileName"], [843, 18, 745, 19], [843, 20, 745, 19, "_jsxFileName"], [843, 32, 745, 19], [844, 10, 745, 19, "lineNumber"], [844, 20, 745, 19], [845, 10, 745, 19, "columnNumber"], [845, 22, 745, 19], [846, 8, 745, 19], [846, 15, 746, 16], [846, 16, 747, 9], [846, 18, 750, 9, "isCameraReady"], [846, 31, 750, 22], [846, 35, 750, 26, "previewBlurEnabled"], [846, 53, 750, 44], [846, 57, 750, 48, "viewSize"], [846, 65, 750, 56], [846, 66, 750, 57, "width"], [846, 71, 750, 62], [846, 74, 750, 65], [846, 75, 750, 66], [846, 92, 751, 10], [846, 96, 751, 10, "_jsxDevRuntime"], [846, 110, 751, 10], [846, 111, 751, 10, "jsxDEV"], [846, 117, 751, 10], [846, 119, 751, 10, "_jsxDevRuntime"], [846, 133, 751, 10], [846, 134, 751, 10, "Fragment"], [846, 142, 751, 10], [847, 10, 751, 10, "children"], [847, 18, 751, 10], [847, 34, 753, 12], [847, 38, 753, 12, "_jsxDevRuntime"], [847, 52, 753, 12], [847, 53, 753, 12, "jsxDEV"], [847, 59, 753, 12], [847, 61, 753, 13, "_LiveFaceCanvas"], [847, 76, 753, 13], [847, 77, 753, 13, "default"], [847, 84, 753, 27], [848, 12, 753, 28, "containerId"], [848, 23, 753, 39], [848, 25, 753, 40], [848, 42, 753, 57], [849, 12, 753, 58, "width"], [849, 17, 753, 63], [849, 19, 753, 65, "viewSize"], [849, 27, 753, 73], [849, 28, 753, 74, "width"], [849, 33, 753, 80], [850, 12, 753, 81, "height"], [850, 18, 753, 87], [850, 20, 753, 89, "viewSize"], [850, 28, 753, 97], [850, 29, 753, 98, "height"], [851, 10, 753, 105], [852, 12, 753, 105, "fileName"], [852, 20, 753, 105], [852, 22, 753, 105, "_jsxFileName"], [852, 34, 753, 105], [853, 12, 753, 105, "lineNumber"], [853, 22, 753, 105], [854, 12, 753, 105, "columnNumber"], [854, 24, 753, 105], [855, 10, 753, 105], [855, 17, 753, 107], [855, 18, 753, 108], [855, 33, 754, 12], [855, 37, 754, 12, "_jsxDevRuntime"], [855, 51, 754, 12], [855, 52, 754, 12, "jsxDEV"], [855, 58, 754, 12], [855, 60, 754, 13, "_View"], [855, 65, 754, 13], [855, 66, 754, 13, "default"], [855, 73, 754, 17], [856, 12, 754, 18, "style"], [856, 17, 754, 23], [856, 19, 754, 25], [856, 20, 754, 26, "StyleSheet"], [856, 39, 754, 36], [856, 40, 754, 37, "absoluteFill"], [856, 52, 754, 49], [856, 54, 754, 51], [857, 14, 754, 53, "pointerEvents"], [857, 27, 754, 66], [857, 29, 754, 68], [858, 12, 754, 75], [858, 13, 754, 76], [858, 14, 754, 78], [859, 12, 754, 78, "children"], [859, 20, 754, 78], [859, 36, 756, 12], [859, 40, 756, 12, "_jsxDevRuntime"], [859, 54, 756, 12], [859, 55, 756, 12, "jsxDEV"], [859, 61, 756, 12], [859, 63, 756, 13, "_expoBlur"], [859, 72, 756, 13], [859, 73, 756, 13, "BlurView"], [859, 81, 756, 21], [860, 14, 756, 22, "intensity"], [860, 23, 756, 31], [860, 25, 756, 33], [860, 27, 756, 36], [861, 14, 756, 37, "tint"], [861, 18, 756, 41], [861, 20, 756, 42], [861, 26, 756, 48], [862, 14, 756, 49, "style"], [862, 19, 756, 54], [862, 21, 756, 56], [862, 22, 756, 57, "styles"], [862, 28, 756, 63], [862, 29, 756, 64, "blurZone"], [862, 37, 756, 72], [862, 39, 756, 74], [863, 16, 757, 14, "left"], [863, 20, 757, 18], [863, 22, 757, 20], [863, 23, 757, 21], [864, 16, 758, 14, "top"], [864, 19, 758, 17], [864, 21, 758, 19, "viewSize"], [864, 29, 758, 27], [864, 30, 758, 28, "height"], [864, 36, 758, 34], [864, 39, 758, 37], [864, 42, 758, 40], [865, 16, 759, 14, "width"], [865, 21, 759, 19], [865, 23, 759, 21, "viewSize"], [865, 31, 759, 29], [865, 32, 759, 30, "width"], [865, 37, 759, 35], [866, 16, 760, 14, "height"], [866, 22, 760, 20], [866, 24, 760, 22, "viewSize"], [866, 32, 760, 30], [866, 33, 760, 31, "height"], [866, 39, 760, 37], [866, 42, 760, 40], [866, 46, 760, 44], [867, 16, 761, 14, "borderRadius"], [867, 28, 761, 26], [867, 30, 761, 28], [868, 14, 762, 12], [868, 15, 762, 13], [869, 12, 762, 15], [870, 14, 762, 15, "fileName"], [870, 22, 762, 15], [870, 24, 762, 15, "_jsxFileName"], [870, 36, 762, 15], [871, 14, 762, 15, "lineNumber"], [871, 24, 762, 15], [872, 14, 762, 15, "columnNumber"], [872, 26, 762, 15], [873, 12, 762, 15], [873, 19, 762, 17], [873, 20, 762, 18], [873, 35, 764, 12], [873, 39, 764, 12, "_jsxDevRuntime"], [873, 53, 764, 12], [873, 54, 764, 12, "jsxDEV"], [873, 60, 764, 12], [873, 62, 764, 13, "_expoBlur"], [873, 71, 764, 13], [873, 72, 764, 13, "BlurView"], [873, 80, 764, 21], [874, 14, 764, 22, "intensity"], [874, 23, 764, 31], [874, 25, 764, 33], [874, 27, 764, 36], [875, 14, 764, 37, "tint"], [875, 18, 764, 41], [875, 20, 764, 42], [875, 26, 764, 48], [876, 14, 764, 49, "style"], [876, 19, 764, 54], [876, 21, 764, 56], [876, 22, 764, 57, "styles"], [876, 28, 764, 63], [876, 29, 764, 64, "blurZone"], [876, 37, 764, 72], [876, 39, 764, 74], [877, 16, 765, 14, "left"], [877, 20, 765, 18], [877, 22, 765, 20], [877, 23, 765, 21], [878, 16, 766, 14, "top"], [878, 19, 766, 17], [878, 21, 766, 19], [878, 22, 766, 20], [879, 16, 767, 14, "width"], [879, 21, 767, 19], [879, 23, 767, 21, "viewSize"], [879, 31, 767, 29], [879, 32, 767, 30, "width"], [879, 37, 767, 35], [880, 16, 768, 14, "height"], [880, 22, 768, 20], [880, 24, 768, 22, "viewSize"], [880, 32, 768, 30], [880, 33, 768, 31, "height"], [880, 39, 768, 37], [880, 42, 768, 40], [880, 45, 768, 43], [881, 16, 769, 14, "borderRadius"], [881, 28, 769, 26], [881, 30, 769, 28], [882, 14, 770, 12], [882, 15, 770, 13], [883, 12, 770, 15], [884, 14, 770, 15, "fileName"], [884, 22, 770, 15], [884, 24, 770, 15, "_jsxFileName"], [884, 36, 770, 15], [885, 14, 770, 15, "lineNumber"], [885, 24, 770, 15], [886, 14, 770, 15, "columnNumber"], [886, 26, 770, 15], [887, 12, 770, 15], [887, 19, 770, 17], [887, 20, 770, 18], [887, 35, 772, 12], [887, 39, 772, 12, "_jsxDevRuntime"], [887, 53, 772, 12], [887, 54, 772, 12, "jsxDEV"], [887, 60, 772, 12], [887, 62, 772, 13, "_expoBlur"], [887, 71, 772, 13], [887, 72, 772, 13, "BlurView"], [887, 80, 772, 21], [888, 14, 772, 22, "intensity"], [888, 23, 772, 31], [888, 25, 772, 33], [888, 27, 772, 36], [889, 14, 772, 37, "tint"], [889, 18, 772, 41], [889, 20, 772, 42], [889, 26, 772, 48], [890, 14, 772, 49, "style"], [890, 19, 772, 54], [890, 21, 772, 56], [890, 22, 772, 57, "styles"], [890, 28, 772, 63], [890, 29, 772, 64, "blurZone"], [890, 37, 772, 72], [890, 39, 772, 74], [891, 16, 773, 14, "left"], [891, 20, 773, 18], [891, 22, 773, 20, "viewSize"], [891, 30, 773, 28], [891, 31, 773, 29, "width"], [891, 36, 773, 34], [891, 39, 773, 37], [891, 42, 773, 40], [891, 45, 773, 44, "viewSize"], [891, 53, 773, 52], [891, 54, 773, 53, "width"], [891, 59, 773, 58], [891, 62, 773, 61], [891, 66, 773, 66], [892, 16, 774, 14, "top"], [892, 19, 774, 17], [892, 21, 774, 19, "viewSize"], [892, 29, 774, 27], [892, 30, 774, 28, "height"], [892, 36, 774, 34], [892, 39, 774, 37], [892, 43, 774, 41], [892, 46, 774, 45, "viewSize"], [892, 54, 774, 53], [892, 55, 774, 54, "width"], [892, 60, 774, 59], [892, 63, 774, 62], [892, 67, 774, 67], [893, 16, 775, 14, "width"], [893, 21, 775, 19], [893, 23, 775, 21, "viewSize"], [893, 31, 775, 29], [893, 32, 775, 30, "width"], [893, 37, 775, 35], [893, 40, 775, 38], [893, 43, 775, 41], [894, 16, 776, 14, "height"], [894, 22, 776, 20], [894, 24, 776, 22, "viewSize"], [894, 32, 776, 30], [894, 33, 776, 31, "width"], [894, 38, 776, 36], [894, 41, 776, 39], [894, 44, 776, 42], [895, 16, 777, 14, "borderRadius"], [895, 28, 777, 26], [895, 30, 777, 29, "viewSize"], [895, 38, 777, 37], [895, 39, 777, 38, "width"], [895, 44, 777, 43], [895, 47, 777, 46], [895, 50, 777, 49], [895, 53, 777, 53], [896, 14, 778, 12], [896, 15, 778, 13], [897, 12, 778, 15], [898, 14, 778, 15, "fileName"], [898, 22, 778, 15], [898, 24, 778, 15, "_jsxFileName"], [898, 36, 778, 15], [899, 14, 778, 15, "lineNumber"], [899, 24, 778, 15], [900, 14, 778, 15, "columnNumber"], [900, 26, 778, 15], [901, 12, 778, 15], [901, 19, 778, 17], [901, 20, 778, 18], [901, 35, 779, 12], [901, 39, 779, 12, "_jsxDevRuntime"], [901, 53, 779, 12], [901, 54, 779, 12, "jsxDEV"], [901, 60, 779, 12], [901, 62, 779, 13, "_expoBlur"], [901, 71, 779, 13], [901, 72, 779, 13, "BlurView"], [901, 80, 779, 21], [902, 14, 779, 22, "intensity"], [902, 23, 779, 31], [902, 25, 779, 33], [902, 27, 779, 36], [903, 14, 779, 37, "tint"], [903, 18, 779, 41], [903, 20, 779, 42], [903, 26, 779, 48], [904, 14, 779, 49, "style"], [904, 19, 779, 54], [904, 21, 779, 56], [904, 22, 779, 57, "styles"], [904, 28, 779, 63], [904, 29, 779, 64, "blurZone"], [904, 37, 779, 72], [904, 39, 779, 74], [905, 16, 780, 14, "left"], [905, 20, 780, 18], [905, 22, 780, 20, "viewSize"], [905, 30, 780, 28], [905, 31, 780, 29, "width"], [905, 36, 780, 34], [905, 39, 780, 37], [905, 42, 780, 40], [905, 45, 780, 44, "viewSize"], [905, 53, 780, 52], [905, 54, 780, 53, "width"], [905, 59, 780, 58], [905, 62, 780, 61], [905, 66, 780, 66], [906, 16, 781, 14, "top"], [906, 19, 781, 17], [906, 21, 781, 19, "viewSize"], [906, 29, 781, 27], [906, 30, 781, 28, "height"], [906, 36, 781, 34], [906, 39, 781, 37], [906, 42, 781, 40], [906, 45, 781, 44, "viewSize"], [906, 53, 781, 52], [906, 54, 781, 53, "width"], [906, 59, 781, 58], [906, 62, 781, 61], [906, 66, 781, 66], [907, 16, 782, 14, "width"], [907, 21, 782, 19], [907, 23, 782, 21, "viewSize"], [907, 31, 782, 29], [907, 32, 782, 30, "width"], [907, 37, 782, 35], [907, 40, 782, 38], [907, 43, 782, 41], [908, 16, 783, 14, "height"], [908, 22, 783, 20], [908, 24, 783, 22, "viewSize"], [908, 32, 783, 30], [908, 33, 783, 31, "width"], [908, 38, 783, 36], [908, 41, 783, 39], [908, 44, 783, 42], [909, 16, 784, 14, "borderRadius"], [909, 28, 784, 26], [909, 30, 784, 29, "viewSize"], [909, 38, 784, 37], [909, 39, 784, 38, "width"], [909, 44, 784, 43], [909, 47, 784, 46], [909, 50, 784, 49], [909, 53, 784, 53], [910, 14, 785, 12], [910, 15, 785, 13], [911, 12, 785, 15], [912, 14, 785, 15, "fileName"], [912, 22, 785, 15], [912, 24, 785, 15, "_jsxFileName"], [912, 36, 785, 15], [913, 14, 785, 15, "lineNumber"], [913, 24, 785, 15], [914, 14, 785, 15, "columnNumber"], [914, 26, 785, 15], [915, 12, 785, 15], [915, 19, 785, 17], [915, 20, 785, 18], [915, 35, 786, 12], [915, 39, 786, 12, "_jsxDevRuntime"], [915, 53, 786, 12], [915, 54, 786, 12, "jsxDEV"], [915, 60, 786, 12], [915, 62, 786, 13, "_expoBlur"], [915, 71, 786, 13], [915, 72, 786, 13, "BlurView"], [915, 80, 786, 21], [916, 14, 786, 22, "intensity"], [916, 23, 786, 31], [916, 25, 786, 33], [916, 27, 786, 36], [917, 14, 786, 37, "tint"], [917, 18, 786, 41], [917, 20, 786, 42], [917, 26, 786, 48], [918, 14, 786, 49, "style"], [918, 19, 786, 54], [918, 21, 786, 56], [918, 22, 786, 57, "styles"], [918, 28, 786, 63], [918, 29, 786, 64, "blurZone"], [918, 37, 786, 72], [918, 39, 786, 74], [919, 16, 787, 14, "left"], [919, 20, 787, 18], [919, 22, 787, 20, "viewSize"], [919, 30, 787, 28], [919, 31, 787, 29, "width"], [919, 36, 787, 34], [919, 39, 787, 37], [919, 42, 787, 40], [919, 45, 787, 44, "viewSize"], [919, 53, 787, 52], [919, 54, 787, 53, "width"], [919, 59, 787, 58], [919, 62, 787, 61], [919, 66, 787, 66], [920, 16, 788, 14, "top"], [920, 19, 788, 17], [920, 21, 788, 19, "viewSize"], [920, 29, 788, 27], [920, 30, 788, 28, "height"], [920, 36, 788, 34], [920, 39, 788, 37], [920, 42, 788, 40], [920, 45, 788, 44, "viewSize"], [920, 53, 788, 52], [920, 54, 788, 53, "width"], [920, 59, 788, 58], [920, 62, 788, 61], [920, 66, 788, 66], [921, 16, 789, 14, "width"], [921, 21, 789, 19], [921, 23, 789, 21, "viewSize"], [921, 31, 789, 29], [921, 32, 789, 30, "width"], [921, 37, 789, 35], [921, 40, 789, 38], [921, 43, 789, 41], [922, 16, 790, 14, "height"], [922, 22, 790, 20], [922, 24, 790, 22, "viewSize"], [922, 32, 790, 30], [922, 33, 790, 31, "width"], [922, 38, 790, 36], [922, 41, 790, 39], [922, 44, 790, 42], [923, 16, 791, 14, "borderRadius"], [923, 28, 791, 26], [923, 30, 791, 29, "viewSize"], [923, 38, 791, 37], [923, 39, 791, 38, "width"], [923, 44, 791, 43], [923, 47, 791, 46], [923, 50, 791, 49], [923, 53, 791, 53], [924, 14, 792, 12], [924, 15, 792, 13], [925, 12, 792, 15], [926, 14, 792, 15, "fileName"], [926, 22, 792, 15], [926, 24, 792, 15, "_jsxFileName"], [926, 36, 792, 15], [927, 14, 792, 15, "lineNumber"], [927, 24, 792, 15], [928, 14, 792, 15, "columnNumber"], [928, 26, 792, 15], [929, 12, 792, 15], [929, 19, 792, 17], [929, 20, 792, 18], [929, 22, 794, 13, "__DEV__"], [929, 29, 794, 20], [929, 46, 795, 14], [929, 50, 795, 14, "_jsxDevRuntime"], [929, 64, 795, 14], [929, 65, 795, 14, "jsxDEV"], [929, 71, 795, 14], [929, 73, 795, 15, "_View"], [929, 78, 795, 15], [929, 79, 795, 15, "default"], [929, 86, 795, 19], [930, 14, 795, 20, "style"], [930, 19, 795, 25], [930, 21, 795, 27, "styles"], [930, 27, 795, 33], [930, 28, 795, 34, "previewChip"], [930, 39, 795, 46], [931, 14, 795, 46, "children"], [931, 22, 795, 46], [931, 37, 796, 16], [931, 41, 796, 16, "_jsxDevRuntime"], [931, 55, 796, 16], [931, 56, 796, 16, "jsxDEV"], [931, 62, 796, 16], [931, 64, 796, 17, "_Text"], [931, 69, 796, 17], [931, 70, 796, 17, "default"], [931, 77, 796, 21], [932, 16, 796, 22, "style"], [932, 21, 796, 27], [932, 23, 796, 29, "styles"], [932, 29, 796, 35], [932, 30, 796, 36, "previewChipText"], [932, 45, 796, 52], [933, 16, 796, 52, "children"], [933, 24, 796, 52], [933, 26, 796, 53], [934, 14, 796, 73], [935, 16, 796, 73, "fileName"], [935, 24, 796, 73], [935, 26, 796, 73, "_jsxFileName"], [935, 38, 796, 73], [936, 16, 796, 73, "lineNumber"], [936, 26, 796, 73], [937, 16, 796, 73, "columnNumber"], [937, 28, 796, 73], [938, 14, 796, 73], [938, 21, 796, 79], [939, 12, 796, 80], [940, 14, 796, 80, "fileName"], [940, 22, 796, 80], [940, 24, 796, 80, "_jsxFileName"], [940, 36, 796, 80], [941, 14, 796, 80, "lineNumber"], [941, 24, 796, 80], [942, 14, 796, 80, "columnNumber"], [942, 26, 796, 80], [943, 12, 796, 80], [943, 19, 797, 20], [943, 20, 798, 13], [944, 10, 798, 13], [945, 12, 798, 13, "fileName"], [945, 20, 798, 13], [945, 22, 798, 13, "_jsxFileName"], [945, 34, 798, 13], [946, 12, 798, 13, "lineNumber"], [946, 22, 798, 13], [947, 12, 798, 13, "columnNumber"], [947, 24, 798, 13], [948, 10, 798, 13], [948, 17, 799, 18], [948, 18, 799, 19], [949, 8, 799, 19], [949, 23, 800, 12], [949, 24, 801, 9], [949, 26, 803, 9, "isCameraReady"], [949, 39, 803, 22], [949, 56, 804, 10], [949, 60, 804, 10, "_jsxDevRuntime"], [949, 74, 804, 10], [949, 75, 804, 10, "jsxDEV"], [949, 81, 804, 10], [949, 83, 804, 10, "_jsxDevRuntime"], [949, 97, 804, 10], [949, 98, 804, 10, "Fragment"], [949, 106, 804, 10], [950, 10, 804, 10, "children"], [950, 18, 804, 10], [950, 34, 806, 12], [950, 38, 806, 12, "_jsxDevRuntime"], [950, 52, 806, 12], [950, 53, 806, 12, "jsxDEV"], [950, 59, 806, 12], [950, 61, 806, 13, "_View"], [950, 66, 806, 13], [950, 67, 806, 13, "default"], [950, 74, 806, 17], [951, 12, 806, 18, "style"], [951, 17, 806, 23], [951, 19, 806, 25, "styles"], [951, 25, 806, 31], [951, 26, 806, 32, "headerOverlay"], [951, 39, 806, 46], [952, 12, 806, 46, "children"], [952, 20, 806, 46], [952, 35, 807, 14], [952, 39, 807, 14, "_jsxDevRuntime"], [952, 53, 807, 14], [952, 54, 807, 14, "jsxDEV"], [952, 60, 807, 14], [952, 62, 807, 15, "_View"], [952, 67, 807, 15], [952, 68, 807, 15, "default"], [952, 75, 807, 19], [953, 14, 807, 20, "style"], [953, 19, 807, 25], [953, 21, 807, 27, "styles"], [953, 27, 807, 33], [953, 28, 807, 34, "headerContent"], [953, 41, 807, 48], [954, 14, 807, 48, "children"], [954, 22, 807, 48], [954, 38, 808, 16], [954, 42, 808, 16, "_jsxDevRuntime"], [954, 56, 808, 16], [954, 57, 808, 16, "jsxDEV"], [954, 63, 808, 16], [954, 65, 808, 17, "_View"], [954, 70, 808, 17], [954, 71, 808, 17, "default"], [954, 78, 808, 21], [955, 16, 808, 22, "style"], [955, 21, 808, 27], [955, 23, 808, 29, "styles"], [955, 29, 808, 35], [955, 30, 808, 36, "headerLeft"], [955, 40, 808, 47], [956, 16, 808, 47, "children"], [956, 24, 808, 47], [956, 40, 809, 18], [956, 44, 809, 18, "_jsxDevRuntime"], [956, 58, 809, 18], [956, 59, 809, 18, "jsxDEV"], [956, 65, 809, 18], [956, 67, 809, 19, "_Text"], [956, 72, 809, 19], [956, 73, 809, 19, "default"], [956, 80, 809, 23], [957, 18, 809, 24, "style"], [957, 23, 809, 29], [957, 25, 809, 31, "styles"], [957, 31, 809, 37], [957, 32, 809, 38, "headerTitle"], [957, 43, 809, 50], [958, 18, 809, 50, "children"], [958, 26, 809, 50], [958, 28, 809, 51], [959, 16, 809, 62], [960, 18, 809, 62, "fileName"], [960, 26, 809, 62], [960, 28, 809, 62, "_jsxFileName"], [960, 40, 809, 62], [961, 18, 809, 62, "lineNumber"], [961, 28, 809, 62], [962, 18, 809, 62, "columnNumber"], [962, 30, 809, 62], [963, 16, 809, 62], [963, 23, 809, 68], [963, 24, 809, 69], [963, 39, 810, 18], [963, 43, 810, 18, "_jsxDevRuntime"], [963, 57, 810, 18], [963, 58, 810, 18, "jsxDEV"], [963, 64, 810, 18], [963, 66, 810, 19, "_View"], [963, 71, 810, 19], [963, 72, 810, 19, "default"], [963, 79, 810, 23], [964, 18, 810, 24, "style"], [964, 23, 810, 29], [964, 25, 810, 31, "styles"], [964, 31, 810, 37], [964, 32, 810, 38, "subtitleRow"], [964, 43, 810, 50], [965, 18, 810, 50, "children"], [965, 26, 810, 50], [965, 42, 811, 20], [965, 46, 811, 20, "_jsxDevRuntime"], [965, 60, 811, 20], [965, 61, 811, 20, "jsxDEV"], [965, 67, 811, 20], [965, 69, 811, 21, "_Text"], [965, 74, 811, 21], [965, 75, 811, 21, "default"], [965, 82, 811, 25], [966, 20, 811, 26, "style"], [966, 25, 811, 31], [966, 27, 811, 33, "styles"], [966, 33, 811, 39], [966, 34, 811, 40, "webIcon"], [966, 41, 811, 48], [967, 20, 811, 48, "children"], [967, 28, 811, 48], [967, 30, 811, 49], [968, 18, 811, 51], [969, 20, 811, 51, "fileName"], [969, 28, 811, 51], [969, 30, 811, 51, "_jsxFileName"], [969, 42, 811, 51], [970, 20, 811, 51, "lineNumber"], [970, 30, 811, 51], [971, 20, 811, 51, "columnNumber"], [971, 32, 811, 51], [972, 18, 811, 51], [972, 25, 811, 57], [972, 26, 811, 58], [972, 41, 812, 20], [972, 45, 812, 20, "_jsxDevRuntime"], [972, 59, 812, 20], [972, 60, 812, 20, "jsxDEV"], [972, 66, 812, 20], [972, 68, 812, 21, "_Text"], [972, 73, 812, 21], [972, 74, 812, 21, "default"], [972, 81, 812, 25], [973, 20, 812, 26, "style"], [973, 25, 812, 31], [973, 27, 812, 33, "styles"], [973, 33, 812, 39], [973, 34, 812, 40, "headerSubtitle"], [973, 48, 812, 55], [974, 20, 812, 55, "children"], [974, 28, 812, 55], [974, 30, 812, 56], [975, 18, 812, 71], [976, 20, 812, 71, "fileName"], [976, 28, 812, 71], [976, 30, 812, 71, "_jsxFileName"], [976, 42, 812, 71], [977, 20, 812, 71, "lineNumber"], [977, 30, 812, 71], [978, 20, 812, 71, "columnNumber"], [978, 32, 812, 71], [979, 18, 812, 71], [979, 25, 812, 77], [979, 26, 812, 78], [980, 16, 812, 78], [981, 18, 812, 78, "fileName"], [981, 26, 812, 78], [981, 28, 812, 78, "_jsxFileName"], [981, 40, 812, 78], [982, 18, 812, 78, "lineNumber"], [982, 28, 812, 78], [983, 18, 812, 78, "columnNumber"], [983, 30, 812, 78], [984, 16, 812, 78], [984, 23, 813, 24], [984, 24, 813, 25], [984, 26, 814, 19, "challengeCode"], [984, 39, 814, 32], [984, 56, 815, 20], [984, 60, 815, 20, "_jsxDevRuntime"], [984, 74, 815, 20], [984, 75, 815, 20, "jsxDEV"], [984, 81, 815, 20], [984, 83, 815, 21, "_View"], [984, 88, 815, 21], [984, 89, 815, 21, "default"], [984, 96, 815, 25], [985, 18, 815, 26, "style"], [985, 23, 815, 31], [985, 25, 815, 33, "styles"], [985, 31, 815, 39], [985, 32, 815, 40, "challengeRow"], [985, 44, 815, 53], [986, 18, 815, 53, "children"], [986, 26, 815, 53], [986, 42, 816, 22], [986, 46, 816, 22, "_jsxDevRuntime"], [986, 60, 816, 22], [986, 61, 816, 22, "jsxDEV"], [986, 67, 816, 22], [986, 69, 816, 23, "_lucideReactNative"], [986, 87, 816, 23], [986, 88, 816, 23, "Shield"], [986, 94, 816, 29], [987, 20, 816, 30, "size"], [987, 24, 816, 34], [987, 26, 816, 36], [987, 28, 816, 39], [988, 20, 816, 40, "color"], [988, 25, 816, 45], [988, 27, 816, 46], [989, 18, 816, 52], [990, 20, 816, 52, "fileName"], [990, 28, 816, 52], [990, 30, 816, 52, "_jsxFileName"], [990, 42, 816, 52], [991, 20, 816, 52, "lineNumber"], [991, 30, 816, 52], [992, 20, 816, 52, "columnNumber"], [992, 32, 816, 52], [993, 18, 816, 52], [993, 25, 816, 54], [993, 26, 816, 55], [993, 41, 817, 22], [993, 45, 817, 22, "_jsxDevRuntime"], [993, 59, 817, 22], [993, 60, 817, 22, "jsxDEV"], [993, 66, 817, 22], [993, 68, 817, 23, "_Text"], [993, 73, 817, 23], [993, 74, 817, 23, "default"], [993, 81, 817, 27], [994, 20, 817, 28, "style"], [994, 25, 817, 33], [994, 27, 817, 35, "styles"], [994, 33, 817, 41], [994, 34, 817, 42, "challengeCode"], [994, 47, 817, 56], [995, 20, 817, 56, "children"], [995, 28, 817, 56], [995, 30, 817, 58, "challengeCode"], [996, 18, 817, 71], [997, 20, 817, 71, "fileName"], [997, 28, 817, 71], [997, 30, 817, 71, "_jsxFileName"], [997, 42, 817, 71], [998, 20, 817, 71, "lineNumber"], [998, 30, 817, 71], [999, 20, 817, 71, "columnNumber"], [999, 32, 817, 71], [1000, 18, 817, 71], [1000, 25, 817, 78], [1000, 26, 817, 79], [1001, 16, 817, 79], [1002, 18, 817, 79, "fileName"], [1002, 26, 817, 79], [1002, 28, 817, 79, "_jsxFileName"], [1002, 40, 817, 79], [1003, 18, 817, 79, "lineNumber"], [1003, 28, 817, 79], [1004, 18, 817, 79, "columnNumber"], [1004, 30, 817, 79], [1005, 16, 817, 79], [1005, 23, 818, 26], [1005, 24, 819, 19], [1006, 14, 819, 19], [1007, 16, 819, 19, "fileName"], [1007, 24, 819, 19], [1007, 26, 819, 19, "_jsxFileName"], [1007, 38, 819, 19], [1008, 16, 819, 19, "lineNumber"], [1008, 26, 819, 19], [1009, 16, 819, 19, "columnNumber"], [1009, 28, 819, 19], [1010, 14, 819, 19], [1010, 21, 820, 22], [1010, 22, 820, 23], [1010, 37, 821, 16], [1010, 41, 821, 16, "_jsxDevRuntime"], [1010, 55, 821, 16], [1010, 56, 821, 16, "jsxDEV"], [1010, 62, 821, 16], [1010, 64, 821, 17, "_TouchableOpacity"], [1010, 81, 821, 17], [1010, 82, 821, 17, "default"], [1010, 89, 821, 33], [1011, 16, 821, 34, "onPress"], [1011, 23, 821, 41], [1011, 25, 821, 43, "onCancel"], [1011, 33, 821, 52], [1012, 16, 821, 53, "style"], [1012, 21, 821, 58], [1012, 23, 821, 60, "styles"], [1012, 29, 821, 66], [1012, 30, 821, 67, "closeButton"], [1012, 41, 821, 79], [1013, 16, 821, 79, "children"], [1013, 24, 821, 79], [1013, 39, 822, 18], [1013, 43, 822, 18, "_jsxDevRuntime"], [1013, 57, 822, 18], [1013, 58, 822, 18, "jsxDEV"], [1013, 64, 822, 18], [1013, 66, 822, 19, "_lucideReactNative"], [1013, 84, 822, 19], [1013, 85, 822, 19, "X"], [1013, 86, 822, 20], [1014, 18, 822, 21, "size"], [1014, 22, 822, 25], [1014, 24, 822, 27], [1014, 26, 822, 30], [1015, 18, 822, 31, "color"], [1015, 23, 822, 36], [1015, 25, 822, 37], [1016, 16, 822, 43], [1017, 18, 822, 43, "fileName"], [1017, 26, 822, 43], [1017, 28, 822, 43, "_jsxFileName"], [1017, 40, 822, 43], [1018, 18, 822, 43, "lineNumber"], [1018, 28, 822, 43], [1019, 18, 822, 43, "columnNumber"], [1019, 30, 822, 43], [1020, 16, 822, 43], [1020, 23, 822, 45], [1021, 14, 822, 46], [1022, 16, 822, 46, "fileName"], [1022, 24, 822, 46], [1022, 26, 822, 46, "_jsxFileName"], [1022, 38, 822, 46], [1023, 16, 822, 46, "lineNumber"], [1023, 26, 822, 46], [1024, 16, 822, 46, "columnNumber"], [1024, 28, 822, 46], [1025, 14, 822, 46], [1025, 21, 823, 34], [1025, 22, 823, 35], [1026, 12, 823, 35], [1027, 14, 823, 35, "fileName"], [1027, 22, 823, 35], [1027, 24, 823, 35, "_jsxFileName"], [1027, 36, 823, 35], [1028, 14, 823, 35, "lineNumber"], [1028, 24, 823, 35], [1029, 14, 823, 35, "columnNumber"], [1029, 26, 823, 35], [1030, 12, 823, 35], [1030, 19, 824, 20], [1031, 10, 824, 21], [1032, 12, 824, 21, "fileName"], [1032, 20, 824, 21], [1032, 22, 824, 21, "_jsxFileName"], [1032, 34, 824, 21], [1033, 12, 824, 21, "lineNumber"], [1033, 22, 824, 21], [1034, 12, 824, 21, "columnNumber"], [1034, 24, 824, 21], [1035, 10, 824, 21], [1035, 17, 825, 18], [1035, 18, 825, 19], [1035, 33, 827, 12], [1035, 37, 827, 12, "_jsxDevRuntime"], [1035, 51, 827, 12], [1035, 52, 827, 12, "jsxDEV"], [1035, 58, 827, 12], [1035, 60, 827, 13, "_View"], [1035, 65, 827, 13], [1035, 66, 827, 13, "default"], [1035, 73, 827, 17], [1036, 12, 827, 18, "style"], [1036, 17, 827, 23], [1036, 19, 827, 25, "styles"], [1036, 25, 827, 31], [1036, 26, 827, 32, "privacyNotice"], [1036, 39, 827, 46], [1037, 12, 827, 46, "children"], [1037, 20, 827, 46], [1037, 36, 828, 14], [1037, 40, 828, 14, "_jsxDevRuntime"], [1037, 54, 828, 14], [1037, 55, 828, 14, "jsxDEV"], [1037, 61, 828, 14], [1037, 63, 828, 15, "_lucideReactNative"], [1037, 81, 828, 15], [1037, 82, 828, 15, "Shield"], [1037, 88, 828, 21], [1038, 14, 828, 22, "size"], [1038, 18, 828, 26], [1038, 20, 828, 28], [1038, 22, 828, 31], [1039, 14, 828, 32, "color"], [1039, 19, 828, 37], [1039, 21, 828, 38], [1040, 12, 828, 47], [1041, 14, 828, 47, "fileName"], [1041, 22, 828, 47], [1041, 24, 828, 47, "_jsxFileName"], [1041, 36, 828, 47], [1042, 14, 828, 47, "lineNumber"], [1042, 24, 828, 47], [1043, 14, 828, 47, "columnNumber"], [1043, 26, 828, 47], [1044, 12, 828, 47], [1044, 19, 828, 49], [1044, 20, 828, 50], [1044, 35, 829, 14], [1044, 39, 829, 14, "_jsxDevRuntime"], [1044, 53, 829, 14], [1044, 54, 829, 14, "jsxDEV"], [1044, 60, 829, 14], [1044, 62, 829, 15, "_Text"], [1044, 67, 829, 15], [1044, 68, 829, 15, "default"], [1044, 75, 829, 19], [1045, 14, 829, 20, "style"], [1045, 19, 829, 25], [1045, 21, 829, 27, "styles"], [1045, 27, 829, 33], [1045, 28, 829, 34, "privacyText"], [1045, 39, 829, 46], [1046, 14, 829, 46, "children"], [1046, 22, 829, 46], [1046, 24, 829, 47], [1047, 12, 831, 14], [1048, 14, 831, 14, "fileName"], [1048, 22, 831, 14], [1048, 24, 831, 14, "_jsxFileName"], [1048, 36, 831, 14], [1049, 14, 831, 14, "lineNumber"], [1049, 24, 831, 14], [1050, 14, 831, 14, "columnNumber"], [1050, 26, 831, 14], [1051, 12, 831, 14], [1051, 19, 831, 20], [1051, 20, 831, 21], [1052, 10, 831, 21], [1053, 12, 831, 21, "fileName"], [1053, 20, 831, 21], [1053, 22, 831, 21, "_jsxFileName"], [1053, 34, 831, 21], [1054, 12, 831, 21, "lineNumber"], [1054, 22, 831, 21], [1055, 12, 831, 21, "columnNumber"], [1055, 24, 831, 21], [1056, 10, 831, 21], [1056, 17, 832, 18], [1056, 18, 832, 19], [1056, 33, 834, 12], [1056, 37, 834, 12, "_jsxDevRuntime"], [1056, 51, 834, 12], [1056, 52, 834, 12, "jsxDEV"], [1056, 58, 834, 12], [1056, 60, 834, 13, "_View"], [1056, 65, 834, 13], [1056, 66, 834, 13, "default"], [1056, 73, 834, 17], [1057, 12, 834, 18, "style"], [1057, 17, 834, 23], [1057, 19, 834, 25, "styles"], [1057, 25, 834, 31], [1057, 26, 834, 32, "footer<PERSON><PERSON><PERSON>"], [1057, 39, 834, 46], [1058, 12, 834, 46, "children"], [1058, 20, 834, 46], [1058, 36, 835, 14], [1058, 40, 835, 14, "_jsxDevRuntime"], [1058, 54, 835, 14], [1058, 55, 835, 14, "jsxDEV"], [1058, 61, 835, 14], [1058, 63, 835, 15, "_Text"], [1058, 68, 835, 15], [1058, 69, 835, 15, "default"], [1058, 76, 835, 19], [1059, 14, 835, 20, "style"], [1059, 19, 835, 25], [1059, 21, 835, 27, "styles"], [1059, 27, 835, 33], [1059, 28, 835, 34, "instruction"], [1059, 39, 835, 46], [1060, 14, 835, 46, "children"], [1060, 22, 835, 46], [1060, 24, 835, 47], [1061, 12, 837, 14], [1062, 14, 837, 14, "fileName"], [1062, 22, 837, 14], [1062, 24, 837, 14, "_jsxFileName"], [1062, 36, 837, 14], [1063, 14, 837, 14, "lineNumber"], [1063, 24, 837, 14], [1064, 14, 837, 14, "columnNumber"], [1064, 26, 837, 14], [1065, 12, 837, 14], [1065, 19, 837, 20], [1065, 20, 837, 21], [1065, 35, 839, 14], [1065, 39, 839, 14, "_jsxDevRuntime"], [1065, 53, 839, 14], [1065, 54, 839, 14, "jsxDEV"], [1065, 60, 839, 14], [1065, 62, 839, 15, "_TouchableOpacity"], [1065, 79, 839, 15], [1065, 80, 839, 15, "default"], [1065, 87, 839, 31], [1066, 14, 840, 16, "onPress"], [1066, 21, 840, 23], [1066, 23, 840, 25, "capturePhoto"], [1066, 35, 840, 38], [1067, 14, 841, 16, "disabled"], [1067, 22, 841, 24], [1067, 24, 841, 26, "processingState"], [1067, 39, 841, 41], [1067, 44, 841, 46], [1067, 50, 841, 52], [1067, 54, 841, 56], [1067, 55, 841, 57, "isCameraReady"], [1067, 68, 841, 71], [1068, 14, 842, 16, "style"], [1068, 19, 842, 21], [1068, 21, 842, 23], [1068, 22, 843, 18, "styles"], [1068, 28, 843, 24], [1068, 29, 843, 25, "shutterButton"], [1068, 42, 843, 38], [1068, 44, 844, 18, "processingState"], [1068, 59, 844, 33], [1068, 64, 844, 38], [1068, 70, 844, 44], [1068, 74, 844, 48, "styles"], [1068, 80, 844, 54], [1068, 81, 844, 55, "shutterButtonDisabled"], [1068, 102, 844, 76], [1068, 103, 845, 18], [1069, 14, 845, 18, "children"], [1069, 22, 845, 18], [1069, 24, 847, 17, "processingState"], [1069, 39, 847, 32], [1069, 44, 847, 37], [1069, 50, 847, 43], [1069, 66, 848, 18], [1069, 70, 848, 18, "_jsxDevRuntime"], [1069, 84, 848, 18], [1069, 85, 848, 18, "jsxDEV"], [1069, 91, 848, 18], [1069, 93, 848, 19, "_View"], [1069, 98, 848, 19], [1069, 99, 848, 19, "default"], [1069, 106, 848, 23], [1070, 16, 848, 24, "style"], [1070, 21, 848, 29], [1070, 23, 848, 31, "styles"], [1070, 29, 848, 37], [1070, 30, 848, 38, "shutterInner"], [1071, 14, 848, 51], [1072, 16, 848, 51, "fileName"], [1072, 24, 848, 51], [1072, 26, 848, 51, "_jsxFileName"], [1072, 38, 848, 51], [1073, 16, 848, 51, "lineNumber"], [1073, 26, 848, 51], [1074, 16, 848, 51, "columnNumber"], [1074, 28, 848, 51], [1075, 14, 848, 51], [1075, 21, 848, 53], [1075, 22, 848, 54], [1075, 38, 850, 18], [1075, 42, 850, 18, "_jsxDevRuntime"], [1075, 56, 850, 18], [1075, 57, 850, 18, "jsxDEV"], [1075, 63, 850, 18], [1075, 65, 850, 19, "_ActivityIndicator"], [1075, 83, 850, 19], [1075, 84, 850, 19, "default"], [1075, 91, 850, 36], [1076, 16, 850, 37, "size"], [1076, 20, 850, 41], [1076, 22, 850, 42], [1076, 29, 850, 49], [1077, 16, 850, 50, "color"], [1077, 21, 850, 55], [1077, 23, 850, 56], [1078, 14, 850, 65], [1079, 16, 850, 65, "fileName"], [1079, 24, 850, 65], [1079, 26, 850, 65, "_jsxFileName"], [1079, 38, 850, 65], [1080, 16, 850, 65, "lineNumber"], [1080, 26, 850, 65], [1081, 16, 850, 65, "columnNumber"], [1081, 28, 850, 65], [1082, 14, 850, 65], [1082, 21, 850, 67], [1083, 12, 851, 17], [1084, 14, 851, 17, "fileName"], [1084, 22, 851, 17], [1084, 24, 851, 17, "_jsxFileName"], [1084, 36, 851, 17], [1085, 14, 851, 17, "lineNumber"], [1085, 24, 851, 17], [1086, 14, 851, 17, "columnNumber"], [1086, 26, 851, 17], [1087, 12, 851, 17], [1087, 19, 852, 32], [1087, 20, 852, 33], [1087, 35, 853, 14], [1087, 39, 853, 14, "_jsxDevRuntime"], [1087, 53, 853, 14], [1087, 54, 853, 14, "jsxDEV"], [1087, 60, 853, 14], [1087, 62, 853, 15, "_Text"], [1087, 67, 853, 15], [1087, 68, 853, 15, "default"], [1087, 75, 853, 19], [1088, 14, 853, 20, "style"], [1088, 19, 853, 25], [1088, 21, 853, 27, "styles"], [1088, 27, 853, 33], [1088, 28, 853, 34, "privacyNote"], [1088, 39, 853, 46], [1089, 14, 853, 46, "children"], [1089, 22, 853, 46], [1089, 24, 853, 47], [1090, 12, 855, 14], [1091, 14, 855, 14, "fileName"], [1091, 22, 855, 14], [1091, 24, 855, 14, "_jsxFileName"], [1091, 36, 855, 14], [1092, 14, 855, 14, "lineNumber"], [1092, 24, 855, 14], [1093, 14, 855, 14, "columnNumber"], [1093, 26, 855, 14], [1094, 12, 855, 14], [1094, 19, 855, 20], [1094, 20, 855, 21], [1095, 10, 855, 21], [1096, 12, 855, 21, "fileName"], [1096, 20, 855, 21], [1096, 22, 855, 21, "_jsxFileName"], [1096, 34, 855, 21], [1097, 12, 855, 21, "lineNumber"], [1097, 22, 855, 21], [1098, 12, 855, 21, "columnNumber"], [1098, 24, 855, 21], [1099, 10, 855, 21], [1099, 17, 856, 18], [1099, 18, 856, 19], [1100, 8, 856, 19], [1100, 23, 857, 12], [1100, 24, 858, 9], [1101, 6, 858, 9], [1102, 8, 858, 9, "fileName"], [1102, 16, 858, 9], [1102, 18, 858, 9, "_jsxFileName"], [1102, 30, 858, 9], [1103, 8, 858, 9, "lineNumber"], [1103, 18, 858, 9], [1104, 8, 858, 9, "columnNumber"], [1104, 20, 858, 9], [1105, 6, 858, 9], [1105, 13, 859, 12], [1105, 14, 859, 13], [1105, 29, 861, 6], [1105, 33, 861, 6, "_jsxDevRuntime"], [1105, 47, 861, 6], [1105, 48, 861, 6, "jsxDEV"], [1105, 54, 861, 6], [1105, 56, 861, 7, "_Modal"], [1105, 62, 861, 7], [1105, 63, 861, 7, "default"], [1105, 70, 861, 12], [1106, 8, 862, 8, "visible"], [1106, 15, 862, 15], [1106, 17, 862, 17, "processingState"], [1106, 32, 862, 32], [1106, 37, 862, 37], [1106, 43, 862, 43], [1106, 47, 862, 47, "processingState"], [1106, 62, 862, 62], [1106, 67, 862, 67], [1106, 74, 862, 75], [1107, 8, 863, 8, "transparent"], [1107, 19, 863, 19], [1108, 8, 864, 8, "animationType"], [1108, 21, 864, 21], [1108, 23, 864, 22], [1108, 29, 864, 28], [1109, 8, 864, 28, "children"], [1109, 16, 864, 28], [1109, 31, 866, 8], [1109, 35, 866, 8, "_jsxDevRuntime"], [1109, 49, 866, 8], [1109, 50, 866, 8, "jsxDEV"], [1109, 56, 866, 8], [1109, 58, 866, 9, "_View"], [1109, 63, 866, 9], [1109, 64, 866, 9, "default"], [1109, 71, 866, 13], [1110, 10, 866, 14, "style"], [1110, 15, 866, 19], [1110, 17, 866, 21, "styles"], [1110, 23, 866, 27], [1110, 24, 866, 28, "processingModal"], [1110, 39, 866, 44], [1111, 10, 866, 44, "children"], [1111, 18, 866, 44], [1111, 33, 867, 10], [1111, 37, 867, 10, "_jsxDevRuntime"], [1111, 51, 867, 10], [1111, 52, 867, 10, "jsxDEV"], [1111, 58, 867, 10], [1111, 60, 867, 11, "_View"], [1111, 65, 867, 11], [1111, 66, 867, 11, "default"], [1111, 73, 867, 15], [1112, 12, 867, 16, "style"], [1112, 17, 867, 21], [1112, 19, 867, 23, "styles"], [1112, 25, 867, 29], [1112, 26, 867, 30, "processingContent"], [1112, 43, 867, 48], [1113, 12, 867, 48, "children"], [1113, 20, 867, 48], [1113, 36, 868, 12], [1113, 40, 868, 12, "_jsxDevRuntime"], [1113, 54, 868, 12], [1113, 55, 868, 12, "jsxDEV"], [1113, 61, 868, 12], [1113, 63, 868, 13, "_ActivityIndicator"], [1113, 81, 868, 13], [1113, 82, 868, 13, "default"], [1113, 89, 868, 30], [1114, 14, 868, 31, "size"], [1114, 18, 868, 35], [1114, 20, 868, 36], [1114, 27, 868, 43], [1115, 14, 868, 44, "color"], [1115, 19, 868, 49], [1115, 21, 868, 50], [1116, 12, 868, 59], [1117, 14, 868, 59, "fileName"], [1117, 22, 868, 59], [1117, 24, 868, 59, "_jsxFileName"], [1117, 36, 868, 59], [1118, 14, 868, 59, "lineNumber"], [1118, 24, 868, 59], [1119, 14, 868, 59, "columnNumber"], [1119, 26, 868, 59], [1120, 12, 868, 59], [1120, 19, 868, 61], [1120, 20, 868, 62], [1120, 35, 870, 12], [1120, 39, 870, 12, "_jsxDevRuntime"], [1120, 53, 870, 12], [1120, 54, 870, 12, "jsxDEV"], [1120, 60, 870, 12], [1120, 62, 870, 13, "_Text"], [1120, 67, 870, 13], [1120, 68, 870, 13, "default"], [1120, 75, 870, 17], [1121, 14, 870, 18, "style"], [1121, 19, 870, 23], [1121, 21, 870, 25, "styles"], [1121, 27, 870, 31], [1121, 28, 870, 32, "processingTitle"], [1121, 43, 870, 48], [1122, 14, 870, 48, "children"], [1122, 22, 870, 48], [1122, 25, 871, 15, "processingState"], [1122, 40, 871, 30], [1122, 45, 871, 35], [1122, 56, 871, 46], [1122, 60, 871, 50], [1122, 80, 871, 70], [1122, 82, 872, 15, "processingState"], [1122, 97, 872, 30], [1122, 102, 872, 35], [1122, 113, 872, 46], [1122, 117, 872, 50], [1122, 146, 872, 79], [1122, 148, 873, 15, "processingState"], [1122, 163, 873, 30], [1122, 168, 873, 35], [1122, 180, 873, 47], [1122, 184, 873, 51], [1122, 216, 873, 83], [1122, 218, 874, 15, "processingState"], [1122, 233, 874, 30], [1122, 238, 874, 35], [1122, 249, 874, 46], [1122, 253, 874, 50], [1122, 275, 874, 72], [1123, 12, 874, 72], [1124, 14, 874, 72, "fileName"], [1124, 22, 874, 72], [1124, 24, 874, 72, "_jsxFileName"], [1124, 36, 874, 72], [1125, 14, 874, 72, "lineNumber"], [1125, 24, 874, 72], [1126, 14, 874, 72, "columnNumber"], [1126, 26, 874, 72], [1127, 12, 874, 72], [1127, 19, 875, 18], [1127, 20, 875, 19], [1127, 35, 876, 12], [1127, 39, 876, 12, "_jsxDevRuntime"], [1127, 53, 876, 12], [1127, 54, 876, 12, "jsxDEV"], [1127, 60, 876, 12], [1127, 62, 876, 13, "_View"], [1127, 67, 876, 13], [1127, 68, 876, 13, "default"], [1127, 75, 876, 17], [1128, 14, 876, 18, "style"], [1128, 19, 876, 23], [1128, 21, 876, 25, "styles"], [1128, 27, 876, 31], [1128, 28, 876, 32, "progressBar"], [1128, 39, 876, 44], [1129, 14, 876, 44, "children"], [1129, 22, 876, 44], [1129, 37, 877, 14], [1129, 41, 877, 14, "_jsxDevRuntime"], [1129, 55, 877, 14], [1129, 56, 877, 14, "jsxDEV"], [1129, 62, 877, 14], [1129, 64, 877, 15, "_View"], [1129, 69, 877, 15], [1129, 70, 877, 15, "default"], [1129, 77, 877, 19], [1130, 16, 878, 16, "style"], [1130, 21, 878, 21], [1130, 23, 878, 23], [1130, 24, 879, 18, "styles"], [1130, 30, 879, 24], [1130, 31, 879, 25, "progressFill"], [1130, 43, 879, 37], [1130, 45, 880, 18], [1131, 18, 880, 20, "width"], [1131, 23, 880, 25], [1131, 25, 880, 27], [1131, 28, 880, 30, "processingProgress"], [1131, 46, 880, 48], [1132, 16, 880, 52], [1132, 17, 880, 53], [1133, 14, 881, 18], [1134, 16, 881, 18, "fileName"], [1134, 24, 881, 18], [1134, 26, 881, 18, "_jsxFileName"], [1134, 38, 881, 18], [1135, 16, 881, 18, "lineNumber"], [1135, 26, 881, 18], [1136, 16, 881, 18, "columnNumber"], [1136, 28, 881, 18], [1137, 14, 881, 18], [1137, 21, 882, 15], [1138, 12, 882, 16], [1139, 14, 882, 16, "fileName"], [1139, 22, 882, 16], [1139, 24, 882, 16, "_jsxFileName"], [1139, 36, 882, 16], [1140, 14, 882, 16, "lineNumber"], [1140, 24, 882, 16], [1141, 14, 882, 16, "columnNumber"], [1141, 26, 882, 16], [1142, 12, 882, 16], [1142, 19, 883, 18], [1142, 20, 883, 19], [1142, 35, 884, 12], [1142, 39, 884, 12, "_jsxDevRuntime"], [1142, 53, 884, 12], [1142, 54, 884, 12, "jsxDEV"], [1142, 60, 884, 12], [1142, 62, 884, 13, "_Text"], [1142, 67, 884, 13], [1142, 68, 884, 13, "default"], [1142, 75, 884, 17], [1143, 14, 884, 18, "style"], [1143, 19, 884, 23], [1143, 21, 884, 25, "styles"], [1143, 27, 884, 31], [1143, 28, 884, 32, "processingDescription"], [1143, 49, 884, 54], [1144, 14, 884, 54, "children"], [1144, 22, 884, 54], [1144, 25, 885, 15, "processingState"], [1144, 40, 885, 30], [1144, 45, 885, 35], [1144, 56, 885, 46], [1144, 60, 885, 50], [1144, 89, 885, 79], [1144, 91, 886, 15, "processingState"], [1144, 106, 886, 30], [1144, 111, 886, 35], [1144, 122, 886, 46], [1144, 126, 886, 50], [1144, 164, 886, 88], [1144, 166, 887, 15, "processingState"], [1144, 181, 887, 30], [1144, 186, 887, 35], [1144, 198, 887, 47], [1144, 202, 887, 51], [1144, 247, 887, 96], [1144, 249, 888, 15, "processingState"], [1144, 264, 888, 30], [1144, 269, 888, 35], [1144, 280, 888, 46], [1144, 284, 888, 50], [1144, 325, 888, 91], [1145, 12, 888, 91], [1146, 14, 888, 91, "fileName"], [1146, 22, 888, 91], [1146, 24, 888, 91, "_jsxFileName"], [1146, 36, 888, 91], [1147, 14, 888, 91, "lineNumber"], [1147, 24, 888, 91], [1148, 14, 888, 91, "columnNumber"], [1148, 26, 888, 91], [1149, 12, 888, 91], [1149, 19, 889, 18], [1149, 20, 889, 19], [1149, 22, 890, 13, "processingState"], [1149, 37, 890, 28], [1149, 42, 890, 33], [1149, 53, 890, 44], [1149, 70, 891, 14], [1149, 74, 891, 14, "_jsxDevRuntime"], [1149, 88, 891, 14], [1149, 89, 891, 14, "jsxDEV"], [1149, 95, 891, 14], [1149, 97, 891, 15, "_lucideReactNative"], [1149, 115, 891, 15], [1149, 116, 891, 15, "CheckCircle"], [1149, 127, 891, 26], [1150, 14, 891, 27, "size"], [1150, 18, 891, 31], [1150, 20, 891, 33], [1150, 22, 891, 36], [1151, 14, 891, 37, "color"], [1151, 19, 891, 42], [1151, 21, 891, 43], [1151, 30, 891, 52], [1152, 14, 891, 53, "style"], [1152, 19, 891, 58], [1152, 21, 891, 60, "styles"], [1152, 27, 891, 66], [1152, 28, 891, 67, "successIcon"], [1153, 12, 891, 79], [1154, 14, 891, 79, "fileName"], [1154, 22, 891, 79], [1154, 24, 891, 79, "_jsxFileName"], [1154, 36, 891, 79], [1155, 14, 891, 79, "lineNumber"], [1155, 24, 891, 79], [1156, 14, 891, 79, "columnNumber"], [1156, 26, 891, 79], [1157, 12, 891, 79], [1157, 19, 891, 81], [1157, 20, 892, 13], [1158, 10, 892, 13], [1159, 12, 892, 13, "fileName"], [1159, 20, 892, 13], [1159, 22, 892, 13, "_jsxFileName"], [1159, 34, 892, 13], [1160, 12, 892, 13, "lineNumber"], [1160, 22, 892, 13], [1161, 12, 892, 13, "columnNumber"], [1161, 24, 892, 13], [1162, 10, 892, 13], [1162, 17, 893, 16], [1163, 8, 893, 17], [1164, 10, 893, 17, "fileName"], [1164, 18, 893, 17], [1164, 20, 893, 17, "_jsxFileName"], [1164, 32, 893, 17], [1165, 10, 893, 17, "lineNumber"], [1165, 20, 893, 17], [1166, 10, 893, 17, "columnNumber"], [1166, 22, 893, 17], [1167, 8, 893, 17], [1167, 15, 894, 14], [1168, 6, 894, 15], [1169, 8, 894, 15, "fileName"], [1169, 16, 894, 15], [1169, 18, 894, 15, "_jsxFileName"], [1169, 30, 894, 15], [1170, 8, 894, 15, "lineNumber"], [1170, 18, 894, 15], [1171, 8, 894, 15, "columnNumber"], [1171, 20, 894, 15], [1172, 6, 894, 15], [1172, 13, 895, 13], [1172, 14, 895, 14], [1172, 29, 897, 6], [1172, 33, 897, 6, "_jsxDevRuntime"], [1172, 47, 897, 6], [1172, 48, 897, 6, "jsxDEV"], [1172, 54, 897, 6], [1172, 56, 897, 7, "_Modal"], [1172, 62, 897, 7], [1172, 63, 897, 7, "default"], [1172, 70, 897, 12], [1173, 8, 898, 8, "visible"], [1173, 15, 898, 15], [1173, 17, 898, 17, "processingState"], [1173, 32, 898, 32], [1173, 37, 898, 37], [1173, 44, 898, 45], [1174, 8, 899, 8, "transparent"], [1174, 19, 899, 19], [1175, 8, 900, 8, "animationType"], [1175, 21, 900, 21], [1175, 23, 900, 22], [1175, 29, 900, 28], [1176, 8, 900, 28, "children"], [1176, 16, 900, 28], [1176, 31, 902, 8], [1176, 35, 902, 8, "_jsxDevRuntime"], [1176, 49, 902, 8], [1176, 50, 902, 8, "jsxDEV"], [1176, 56, 902, 8], [1176, 58, 902, 9, "_View"], [1176, 63, 902, 9], [1176, 64, 902, 9, "default"], [1176, 71, 902, 13], [1177, 10, 902, 14, "style"], [1177, 15, 902, 19], [1177, 17, 902, 21, "styles"], [1177, 23, 902, 27], [1177, 24, 902, 28, "processingModal"], [1177, 39, 902, 44], [1178, 10, 902, 44, "children"], [1178, 18, 902, 44], [1178, 33, 903, 10], [1178, 37, 903, 10, "_jsxDevRuntime"], [1178, 51, 903, 10], [1178, 52, 903, 10, "jsxDEV"], [1178, 58, 903, 10], [1178, 60, 903, 11, "_View"], [1178, 65, 903, 11], [1178, 66, 903, 11, "default"], [1178, 73, 903, 15], [1179, 12, 903, 16, "style"], [1179, 17, 903, 21], [1179, 19, 903, 23, "styles"], [1179, 25, 903, 29], [1179, 26, 903, 30, "errorContent"], [1179, 38, 903, 43], [1180, 12, 903, 43, "children"], [1180, 20, 903, 43], [1180, 36, 904, 12], [1180, 40, 904, 12, "_jsxDevRuntime"], [1180, 54, 904, 12], [1180, 55, 904, 12, "jsxDEV"], [1180, 61, 904, 12], [1180, 63, 904, 13, "_lucideReactNative"], [1180, 81, 904, 13], [1180, 82, 904, 13, "X"], [1180, 83, 904, 14], [1181, 14, 904, 15, "size"], [1181, 18, 904, 19], [1181, 20, 904, 21], [1181, 22, 904, 24], [1182, 14, 904, 25, "color"], [1182, 19, 904, 30], [1182, 21, 904, 31], [1183, 12, 904, 40], [1184, 14, 904, 40, "fileName"], [1184, 22, 904, 40], [1184, 24, 904, 40, "_jsxFileName"], [1184, 36, 904, 40], [1185, 14, 904, 40, "lineNumber"], [1185, 24, 904, 40], [1186, 14, 904, 40, "columnNumber"], [1186, 26, 904, 40], [1187, 12, 904, 40], [1187, 19, 904, 42], [1187, 20, 904, 43], [1187, 35, 905, 12], [1187, 39, 905, 12, "_jsxDevRuntime"], [1187, 53, 905, 12], [1187, 54, 905, 12, "jsxDEV"], [1187, 60, 905, 12], [1187, 62, 905, 13, "_Text"], [1187, 67, 905, 13], [1187, 68, 905, 13, "default"], [1187, 75, 905, 17], [1188, 14, 905, 18, "style"], [1188, 19, 905, 23], [1188, 21, 905, 25, "styles"], [1188, 27, 905, 31], [1188, 28, 905, 32, "errorTitle"], [1188, 38, 905, 43], [1189, 14, 905, 43, "children"], [1189, 22, 905, 43], [1189, 24, 905, 44], [1190, 12, 905, 61], [1191, 14, 905, 61, "fileName"], [1191, 22, 905, 61], [1191, 24, 905, 61, "_jsxFileName"], [1191, 36, 905, 61], [1192, 14, 905, 61, "lineNumber"], [1192, 24, 905, 61], [1193, 14, 905, 61, "columnNumber"], [1193, 26, 905, 61], [1194, 12, 905, 61], [1194, 19, 905, 67], [1194, 20, 905, 68], [1194, 35, 906, 12], [1194, 39, 906, 12, "_jsxDevRuntime"], [1194, 53, 906, 12], [1194, 54, 906, 12, "jsxDEV"], [1194, 60, 906, 12], [1194, 62, 906, 13, "_Text"], [1194, 67, 906, 13], [1194, 68, 906, 13, "default"], [1194, 75, 906, 17], [1195, 14, 906, 18, "style"], [1195, 19, 906, 23], [1195, 21, 906, 25, "styles"], [1195, 27, 906, 31], [1195, 28, 906, 32, "errorMessage"], [1195, 40, 906, 45], [1196, 14, 906, 45, "children"], [1196, 22, 906, 45], [1196, 24, 906, 47, "errorMessage"], [1197, 12, 906, 59], [1198, 14, 906, 59, "fileName"], [1198, 22, 906, 59], [1198, 24, 906, 59, "_jsxFileName"], [1198, 36, 906, 59], [1199, 14, 906, 59, "lineNumber"], [1199, 24, 906, 59], [1200, 14, 906, 59, "columnNumber"], [1200, 26, 906, 59], [1201, 12, 906, 59], [1201, 19, 906, 66], [1201, 20, 906, 67], [1201, 35, 907, 12], [1201, 39, 907, 12, "_jsxDevRuntime"], [1201, 53, 907, 12], [1201, 54, 907, 12, "jsxDEV"], [1201, 60, 907, 12], [1201, 62, 907, 13, "_TouchableOpacity"], [1201, 79, 907, 13], [1201, 80, 907, 13, "default"], [1201, 87, 907, 29], [1202, 14, 908, 14, "onPress"], [1202, 21, 908, 21], [1202, 23, 908, 23, "retryCapture"], [1202, 35, 908, 36], [1203, 14, 909, 14, "style"], [1203, 19, 909, 19], [1203, 21, 909, 21, "styles"], [1203, 27, 909, 27], [1203, 28, 909, 28, "primaryButton"], [1203, 41, 909, 42], [1204, 14, 909, 42, "children"], [1204, 22, 909, 42], [1204, 37, 911, 14], [1204, 41, 911, 14, "_jsxDevRuntime"], [1204, 55, 911, 14], [1204, 56, 911, 14, "jsxDEV"], [1204, 62, 911, 14], [1204, 64, 911, 15, "_Text"], [1204, 69, 911, 15], [1204, 70, 911, 15, "default"], [1204, 77, 911, 19], [1205, 16, 911, 20, "style"], [1205, 21, 911, 25], [1205, 23, 911, 27, "styles"], [1205, 29, 911, 33], [1205, 30, 911, 34, "primaryButtonText"], [1205, 47, 911, 52], [1206, 16, 911, 52, "children"], [1206, 24, 911, 52], [1206, 26, 911, 53], [1207, 14, 911, 62], [1208, 16, 911, 62, "fileName"], [1208, 24, 911, 62], [1208, 26, 911, 62, "_jsxFileName"], [1208, 38, 911, 62], [1209, 16, 911, 62, "lineNumber"], [1209, 26, 911, 62], [1210, 16, 911, 62, "columnNumber"], [1210, 28, 911, 62], [1211, 14, 911, 62], [1211, 21, 911, 68], [1212, 12, 911, 69], [1213, 14, 911, 69, "fileName"], [1213, 22, 911, 69], [1213, 24, 911, 69, "_jsxFileName"], [1213, 36, 911, 69], [1214, 14, 911, 69, "lineNumber"], [1214, 24, 911, 69], [1215, 14, 911, 69, "columnNumber"], [1215, 26, 911, 69], [1216, 12, 911, 69], [1216, 19, 912, 30], [1216, 20, 912, 31], [1216, 35, 913, 12], [1216, 39, 913, 12, "_jsxDevRuntime"], [1216, 53, 913, 12], [1216, 54, 913, 12, "jsxDEV"], [1216, 60, 913, 12], [1216, 62, 913, 13, "_TouchableOpacity"], [1216, 79, 913, 13], [1216, 80, 913, 13, "default"], [1216, 87, 913, 29], [1217, 14, 914, 14, "onPress"], [1217, 21, 914, 21], [1217, 23, 914, 23, "onCancel"], [1217, 31, 914, 32], [1218, 14, 915, 14, "style"], [1218, 19, 915, 19], [1218, 21, 915, 21, "styles"], [1218, 27, 915, 27], [1218, 28, 915, 28, "secondaryButton"], [1218, 43, 915, 44], [1219, 14, 915, 44, "children"], [1219, 22, 915, 44], [1219, 37, 917, 14], [1219, 41, 917, 14, "_jsxDevRuntime"], [1219, 55, 917, 14], [1219, 56, 917, 14, "jsxDEV"], [1219, 62, 917, 14], [1219, 64, 917, 15, "_Text"], [1219, 69, 917, 15], [1219, 70, 917, 15, "default"], [1219, 77, 917, 19], [1220, 16, 917, 20, "style"], [1220, 21, 917, 25], [1220, 23, 917, 27, "styles"], [1220, 29, 917, 33], [1220, 30, 917, 34, "secondaryButtonText"], [1220, 49, 917, 54], [1221, 16, 917, 54, "children"], [1221, 24, 917, 54], [1221, 26, 917, 55], [1222, 14, 917, 61], [1223, 16, 917, 61, "fileName"], [1223, 24, 917, 61], [1223, 26, 917, 61, "_jsxFileName"], [1223, 38, 917, 61], [1224, 16, 917, 61, "lineNumber"], [1224, 26, 917, 61], [1225, 16, 917, 61, "columnNumber"], [1225, 28, 917, 61], [1226, 14, 917, 61], [1226, 21, 917, 67], [1227, 12, 917, 68], [1228, 14, 917, 68, "fileName"], [1228, 22, 917, 68], [1228, 24, 917, 68, "_jsxFileName"], [1228, 36, 917, 68], [1229, 14, 917, 68, "lineNumber"], [1229, 24, 917, 68], [1230, 14, 917, 68, "columnNumber"], [1230, 26, 917, 68], [1231, 12, 917, 68], [1231, 19, 918, 30], [1231, 20, 918, 31], [1232, 10, 918, 31], [1233, 12, 918, 31, "fileName"], [1233, 20, 918, 31], [1233, 22, 918, 31, "_jsxFileName"], [1233, 34, 918, 31], [1234, 12, 918, 31, "lineNumber"], [1234, 22, 918, 31], [1235, 12, 918, 31, "columnNumber"], [1235, 24, 918, 31], [1236, 10, 918, 31], [1236, 17, 919, 16], [1237, 8, 919, 17], [1238, 10, 919, 17, "fileName"], [1238, 18, 919, 17], [1238, 20, 919, 17, "_jsxFileName"], [1238, 32, 919, 17], [1239, 10, 919, 17, "lineNumber"], [1239, 20, 919, 17], [1240, 10, 919, 17, "columnNumber"], [1240, 22, 919, 17], [1241, 8, 919, 17], [1241, 15, 920, 14], [1242, 6, 920, 15], [1243, 8, 920, 15, "fileName"], [1243, 16, 920, 15], [1243, 18, 920, 15, "_jsxFileName"], [1243, 30, 920, 15], [1244, 8, 920, 15, "lineNumber"], [1244, 18, 920, 15], [1245, 8, 920, 15, "columnNumber"], [1245, 20, 920, 15], [1246, 6, 920, 15], [1246, 13, 921, 13], [1246, 14, 921, 14], [1247, 4, 921, 14], [1248, 6, 921, 14, "fileName"], [1248, 14, 921, 14], [1248, 16, 921, 14, "_jsxFileName"], [1248, 28, 921, 14], [1249, 6, 921, 14, "lineNumber"], [1249, 16, 921, 14], [1250, 6, 921, 14, "columnNumber"], [1250, 18, 921, 14], [1251, 4, 921, 14], [1251, 11, 922, 10], [1251, 12, 922, 11], [1252, 2, 924, 0], [1253, 2, 924, 1, "_s"], [1253, 4, 924, 1], [1253, 5, 51, 24, "EchoCameraWeb"], [1253, 18, 51, 37], [1254, 4, 51, 37], [1254, 12, 58, 42, "useCameraPermissions"], [1254, 44, 58, 62], [1254, 46, 72, 19, "useUpload"], [1254, 64, 72, 28], [1255, 2, 72, 28], [1256, 2, 72, 28, "_c"], [1256, 4, 72, 28], [1256, 7, 51, 24, "EchoCameraWeb"], [1256, 20, 51, 37], [1257, 2, 925, 0], [1257, 8, 925, 6, "styles"], [1257, 14, 925, 12], [1257, 17, 925, 15, "StyleSheet"], [1257, 36, 925, 25], [1257, 37, 925, 26, "create"], [1257, 43, 925, 32], [1257, 44, 925, 33], [1258, 4, 926, 2, "container"], [1258, 13, 926, 11], [1258, 15, 926, 13], [1259, 6, 927, 4, "flex"], [1259, 10, 927, 8], [1259, 12, 927, 10], [1259, 13, 927, 11], [1260, 6, 928, 4, "backgroundColor"], [1260, 21, 928, 19], [1260, 23, 928, 21], [1261, 4, 929, 2], [1261, 5, 929, 3], [1262, 4, 930, 2, "cameraContainer"], [1262, 19, 930, 17], [1262, 21, 930, 19], [1263, 6, 931, 4, "flex"], [1263, 10, 931, 8], [1263, 12, 931, 10], [1263, 13, 931, 11], [1264, 6, 932, 4, "max<PERSON><PERSON><PERSON>"], [1264, 14, 932, 12], [1264, 16, 932, 14], [1264, 19, 932, 17], [1265, 6, 933, 4, "alignSelf"], [1265, 15, 933, 13], [1265, 17, 933, 15], [1265, 25, 933, 23], [1266, 6, 934, 4, "width"], [1266, 11, 934, 9], [1266, 13, 934, 11], [1267, 4, 935, 2], [1267, 5, 935, 3], [1268, 4, 936, 2, "camera"], [1268, 10, 936, 8], [1268, 12, 936, 10], [1269, 6, 937, 4, "flex"], [1269, 10, 937, 8], [1269, 12, 937, 10], [1270, 4, 938, 2], [1270, 5, 938, 3], [1271, 4, 939, 2, "headerOverlay"], [1271, 17, 939, 15], [1271, 19, 939, 17], [1272, 6, 940, 4, "position"], [1272, 14, 940, 12], [1272, 16, 940, 14], [1272, 26, 940, 24], [1273, 6, 941, 4, "top"], [1273, 9, 941, 7], [1273, 11, 941, 9], [1273, 12, 941, 10], [1274, 6, 942, 4, "left"], [1274, 10, 942, 8], [1274, 12, 942, 10], [1274, 13, 942, 11], [1275, 6, 943, 4, "right"], [1275, 11, 943, 9], [1275, 13, 943, 11], [1275, 14, 943, 12], [1276, 6, 944, 4, "backgroundColor"], [1276, 21, 944, 19], [1276, 23, 944, 21], [1276, 36, 944, 34], [1277, 6, 945, 4, "paddingTop"], [1277, 16, 945, 14], [1277, 18, 945, 16], [1277, 20, 945, 18], [1278, 6, 946, 4, "paddingHorizontal"], [1278, 23, 946, 21], [1278, 25, 946, 23], [1278, 27, 946, 25], [1279, 6, 947, 4, "paddingBottom"], [1279, 19, 947, 17], [1279, 21, 947, 19], [1280, 4, 948, 2], [1280, 5, 948, 3], [1281, 4, 949, 2, "headerContent"], [1281, 17, 949, 15], [1281, 19, 949, 17], [1282, 6, 950, 4, "flexDirection"], [1282, 19, 950, 17], [1282, 21, 950, 19], [1282, 26, 950, 24], [1283, 6, 951, 4, "justifyContent"], [1283, 20, 951, 18], [1283, 22, 951, 20], [1283, 37, 951, 35], [1284, 6, 952, 4, "alignItems"], [1284, 16, 952, 14], [1284, 18, 952, 16], [1285, 4, 953, 2], [1285, 5, 953, 3], [1286, 4, 954, 2, "headerLeft"], [1286, 14, 954, 12], [1286, 16, 954, 14], [1287, 6, 955, 4, "flex"], [1287, 10, 955, 8], [1287, 12, 955, 10], [1288, 4, 956, 2], [1288, 5, 956, 3], [1289, 4, 957, 2, "headerTitle"], [1289, 15, 957, 13], [1289, 17, 957, 15], [1290, 6, 958, 4, "fontSize"], [1290, 14, 958, 12], [1290, 16, 958, 14], [1290, 18, 958, 16], [1291, 6, 959, 4, "fontWeight"], [1291, 16, 959, 14], [1291, 18, 959, 16], [1291, 23, 959, 21], [1292, 6, 960, 4, "color"], [1292, 11, 960, 9], [1292, 13, 960, 11], [1292, 19, 960, 17], [1293, 6, 961, 4, "marginBottom"], [1293, 18, 961, 16], [1293, 20, 961, 18], [1294, 4, 962, 2], [1294, 5, 962, 3], [1295, 4, 963, 2, "subtitleRow"], [1295, 15, 963, 13], [1295, 17, 963, 15], [1296, 6, 964, 4, "flexDirection"], [1296, 19, 964, 17], [1296, 21, 964, 19], [1296, 26, 964, 24], [1297, 6, 965, 4, "alignItems"], [1297, 16, 965, 14], [1297, 18, 965, 16], [1297, 26, 965, 24], [1298, 6, 966, 4, "marginBottom"], [1298, 18, 966, 16], [1298, 20, 966, 18], [1299, 4, 967, 2], [1299, 5, 967, 3], [1300, 4, 968, 2, "webIcon"], [1300, 11, 968, 9], [1300, 13, 968, 11], [1301, 6, 969, 4, "fontSize"], [1301, 14, 969, 12], [1301, 16, 969, 14], [1301, 18, 969, 16], [1302, 6, 970, 4, "marginRight"], [1302, 17, 970, 15], [1302, 19, 970, 17], [1303, 4, 971, 2], [1303, 5, 971, 3], [1304, 4, 972, 2, "headerSubtitle"], [1304, 18, 972, 16], [1304, 20, 972, 18], [1305, 6, 973, 4, "fontSize"], [1305, 14, 973, 12], [1305, 16, 973, 14], [1305, 18, 973, 16], [1306, 6, 974, 4, "color"], [1306, 11, 974, 9], [1306, 13, 974, 11], [1306, 19, 974, 17], [1307, 6, 975, 4, "opacity"], [1307, 13, 975, 11], [1307, 15, 975, 13], [1308, 4, 976, 2], [1308, 5, 976, 3], [1309, 4, 977, 2, "challengeRow"], [1309, 16, 977, 14], [1309, 18, 977, 16], [1310, 6, 978, 4, "flexDirection"], [1310, 19, 978, 17], [1310, 21, 978, 19], [1310, 26, 978, 24], [1311, 6, 979, 4, "alignItems"], [1311, 16, 979, 14], [1311, 18, 979, 16], [1312, 4, 980, 2], [1312, 5, 980, 3], [1313, 4, 981, 2, "challengeCode"], [1313, 17, 981, 15], [1313, 19, 981, 17], [1314, 6, 982, 4, "fontSize"], [1314, 14, 982, 12], [1314, 16, 982, 14], [1314, 18, 982, 16], [1315, 6, 983, 4, "color"], [1315, 11, 983, 9], [1315, 13, 983, 11], [1315, 19, 983, 17], [1316, 6, 984, 4, "marginLeft"], [1316, 16, 984, 14], [1316, 18, 984, 16], [1316, 19, 984, 17], [1317, 6, 985, 4, "fontFamily"], [1317, 16, 985, 14], [1317, 18, 985, 16], [1318, 4, 986, 2], [1318, 5, 986, 3], [1319, 4, 987, 2, "closeButton"], [1319, 15, 987, 13], [1319, 17, 987, 15], [1320, 6, 988, 4, "padding"], [1320, 13, 988, 11], [1320, 15, 988, 13], [1321, 4, 989, 2], [1321, 5, 989, 3], [1322, 4, 990, 2, "privacyNotice"], [1322, 17, 990, 15], [1322, 19, 990, 17], [1323, 6, 991, 4, "position"], [1323, 14, 991, 12], [1323, 16, 991, 14], [1323, 26, 991, 24], [1324, 6, 992, 4, "top"], [1324, 9, 992, 7], [1324, 11, 992, 9], [1324, 14, 992, 12], [1325, 6, 993, 4, "left"], [1325, 10, 993, 8], [1325, 12, 993, 10], [1325, 14, 993, 12], [1326, 6, 994, 4, "right"], [1326, 11, 994, 9], [1326, 13, 994, 11], [1326, 15, 994, 13], [1327, 6, 995, 4, "backgroundColor"], [1327, 21, 995, 19], [1327, 23, 995, 21], [1327, 48, 995, 46], [1328, 6, 996, 4, "borderRadius"], [1328, 18, 996, 16], [1328, 20, 996, 18], [1328, 21, 996, 19], [1329, 6, 997, 4, "padding"], [1329, 13, 997, 11], [1329, 15, 997, 13], [1329, 17, 997, 15], [1330, 6, 998, 4, "flexDirection"], [1330, 19, 998, 17], [1330, 21, 998, 19], [1330, 26, 998, 24], [1331, 6, 999, 4, "alignItems"], [1331, 16, 999, 14], [1331, 18, 999, 16], [1332, 4, 1000, 2], [1332, 5, 1000, 3], [1333, 4, 1001, 2, "privacyText"], [1333, 15, 1001, 13], [1333, 17, 1001, 15], [1334, 6, 1002, 4, "color"], [1334, 11, 1002, 9], [1334, 13, 1002, 11], [1334, 19, 1002, 17], [1335, 6, 1003, 4, "fontSize"], [1335, 14, 1003, 12], [1335, 16, 1003, 14], [1335, 18, 1003, 16], [1336, 6, 1004, 4, "marginLeft"], [1336, 16, 1004, 14], [1336, 18, 1004, 16], [1336, 19, 1004, 17], [1337, 6, 1005, 4, "flex"], [1337, 10, 1005, 8], [1337, 12, 1005, 10], [1338, 4, 1006, 2], [1338, 5, 1006, 3], [1339, 4, 1007, 2, "footer<PERSON><PERSON><PERSON>"], [1339, 17, 1007, 15], [1339, 19, 1007, 17], [1340, 6, 1008, 4, "position"], [1340, 14, 1008, 12], [1340, 16, 1008, 14], [1340, 26, 1008, 24], [1341, 6, 1009, 4, "bottom"], [1341, 12, 1009, 10], [1341, 14, 1009, 12], [1341, 15, 1009, 13], [1342, 6, 1010, 4, "left"], [1342, 10, 1010, 8], [1342, 12, 1010, 10], [1342, 13, 1010, 11], [1343, 6, 1011, 4, "right"], [1343, 11, 1011, 9], [1343, 13, 1011, 11], [1343, 14, 1011, 12], [1344, 6, 1012, 4, "backgroundColor"], [1344, 21, 1012, 19], [1344, 23, 1012, 21], [1344, 36, 1012, 34], [1345, 6, 1013, 4, "paddingBottom"], [1345, 19, 1013, 17], [1345, 21, 1013, 19], [1345, 23, 1013, 21], [1346, 6, 1014, 4, "paddingTop"], [1346, 16, 1014, 14], [1346, 18, 1014, 16], [1346, 20, 1014, 18], [1347, 6, 1015, 4, "alignItems"], [1347, 16, 1015, 14], [1347, 18, 1015, 16], [1348, 4, 1016, 2], [1348, 5, 1016, 3], [1349, 4, 1017, 2, "instruction"], [1349, 15, 1017, 13], [1349, 17, 1017, 15], [1350, 6, 1018, 4, "fontSize"], [1350, 14, 1018, 12], [1350, 16, 1018, 14], [1350, 18, 1018, 16], [1351, 6, 1019, 4, "color"], [1351, 11, 1019, 9], [1351, 13, 1019, 11], [1351, 19, 1019, 17], [1352, 6, 1020, 4, "marginBottom"], [1352, 18, 1020, 16], [1352, 20, 1020, 18], [1353, 4, 1021, 2], [1353, 5, 1021, 3], [1354, 4, 1022, 2, "shutterButton"], [1354, 17, 1022, 15], [1354, 19, 1022, 17], [1355, 6, 1023, 4, "width"], [1355, 11, 1023, 9], [1355, 13, 1023, 11], [1355, 15, 1023, 13], [1356, 6, 1024, 4, "height"], [1356, 12, 1024, 10], [1356, 14, 1024, 12], [1356, 16, 1024, 14], [1357, 6, 1025, 4, "borderRadius"], [1357, 18, 1025, 16], [1357, 20, 1025, 18], [1357, 22, 1025, 20], [1358, 6, 1026, 4, "backgroundColor"], [1358, 21, 1026, 19], [1358, 23, 1026, 21], [1358, 29, 1026, 27], [1359, 6, 1027, 4, "justifyContent"], [1359, 20, 1027, 18], [1359, 22, 1027, 20], [1359, 30, 1027, 28], [1360, 6, 1028, 4, "alignItems"], [1360, 16, 1028, 14], [1360, 18, 1028, 16], [1360, 26, 1028, 24], [1361, 6, 1029, 4, "marginBottom"], [1361, 18, 1029, 16], [1361, 20, 1029, 18], [1361, 22, 1029, 20], [1362, 6, 1030, 4], [1362, 9, 1030, 7, "Platform"], [1362, 26, 1030, 15], [1362, 27, 1030, 16, "select"], [1362, 33, 1030, 22], [1362, 34, 1030, 23], [1363, 8, 1031, 6, "ios"], [1363, 11, 1031, 9], [1363, 13, 1031, 11], [1364, 10, 1032, 8, "shadowColor"], [1364, 21, 1032, 19], [1364, 23, 1032, 21], [1364, 32, 1032, 30], [1365, 10, 1033, 8, "shadowOffset"], [1365, 22, 1033, 20], [1365, 24, 1033, 22], [1366, 12, 1033, 24, "width"], [1366, 17, 1033, 29], [1366, 19, 1033, 31], [1366, 20, 1033, 32], [1367, 12, 1033, 34, "height"], [1367, 18, 1033, 40], [1367, 20, 1033, 42], [1368, 10, 1033, 44], [1368, 11, 1033, 45], [1369, 10, 1034, 8, "shadowOpacity"], [1369, 23, 1034, 21], [1369, 25, 1034, 23], [1369, 28, 1034, 26], [1370, 10, 1035, 8, "shadowRadius"], [1370, 22, 1035, 20], [1370, 24, 1035, 22], [1371, 8, 1036, 6], [1371, 9, 1036, 7], [1372, 8, 1037, 6, "android"], [1372, 15, 1037, 13], [1372, 17, 1037, 15], [1373, 10, 1038, 8, "elevation"], [1373, 19, 1038, 17], [1373, 21, 1038, 19], [1374, 8, 1039, 6], [1374, 9, 1039, 7], [1375, 8, 1040, 6, "web"], [1375, 11, 1040, 9], [1375, 13, 1040, 11], [1376, 10, 1041, 8, "boxShadow"], [1376, 19, 1041, 17], [1376, 21, 1041, 19], [1377, 8, 1042, 6], [1378, 6, 1043, 4], [1378, 7, 1043, 5], [1379, 4, 1044, 2], [1379, 5, 1044, 3], [1380, 4, 1045, 2, "shutterButtonDisabled"], [1380, 25, 1045, 23], [1380, 27, 1045, 25], [1381, 6, 1046, 4, "opacity"], [1381, 13, 1046, 11], [1381, 15, 1046, 13], [1382, 4, 1047, 2], [1382, 5, 1047, 3], [1383, 4, 1048, 2, "shutterInner"], [1383, 16, 1048, 14], [1383, 18, 1048, 16], [1384, 6, 1049, 4, "width"], [1384, 11, 1049, 9], [1384, 13, 1049, 11], [1384, 15, 1049, 13], [1385, 6, 1050, 4, "height"], [1385, 12, 1050, 10], [1385, 14, 1050, 12], [1385, 16, 1050, 14], [1386, 6, 1051, 4, "borderRadius"], [1386, 18, 1051, 16], [1386, 20, 1051, 18], [1386, 22, 1051, 20], [1387, 6, 1052, 4, "backgroundColor"], [1387, 21, 1052, 19], [1387, 23, 1052, 21], [1387, 29, 1052, 27], [1388, 6, 1053, 4, "borderWidth"], [1388, 17, 1053, 15], [1388, 19, 1053, 17], [1388, 20, 1053, 18], [1389, 6, 1054, 4, "borderColor"], [1389, 17, 1054, 15], [1389, 19, 1054, 17], [1390, 4, 1055, 2], [1390, 5, 1055, 3], [1391, 4, 1056, 2, "privacyNote"], [1391, 15, 1056, 13], [1391, 17, 1056, 15], [1392, 6, 1057, 4, "fontSize"], [1392, 14, 1057, 12], [1392, 16, 1057, 14], [1392, 18, 1057, 16], [1393, 6, 1058, 4, "color"], [1393, 11, 1058, 9], [1393, 13, 1058, 11], [1394, 4, 1059, 2], [1394, 5, 1059, 3], [1395, 4, 1060, 2, "processingModal"], [1395, 19, 1060, 17], [1395, 21, 1060, 19], [1396, 6, 1061, 4, "flex"], [1396, 10, 1061, 8], [1396, 12, 1061, 10], [1396, 13, 1061, 11], [1397, 6, 1062, 4, "backgroundColor"], [1397, 21, 1062, 19], [1397, 23, 1062, 21], [1397, 43, 1062, 41], [1398, 6, 1063, 4, "justifyContent"], [1398, 20, 1063, 18], [1398, 22, 1063, 20], [1398, 30, 1063, 28], [1399, 6, 1064, 4, "alignItems"], [1399, 16, 1064, 14], [1399, 18, 1064, 16], [1400, 4, 1065, 2], [1400, 5, 1065, 3], [1401, 4, 1066, 2, "processingContent"], [1401, 21, 1066, 19], [1401, 23, 1066, 21], [1402, 6, 1067, 4, "backgroundColor"], [1402, 21, 1067, 19], [1402, 23, 1067, 21], [1402, 29, 1067, 27], [1403, 6, 1068, 4, "borderRadius"], [1403, 18, 1068, 16], [1403, 20, 1068, 18], [1403, 22, 1068, 20], [1404, 6, 1069, 4, "padding"], [1404, 13, 1069, 11], [1404, 15, 1069, 13], [1404, 17, 1069, 15], [1405, 6, 1070, 4, "width"], [1405, 11, 1070, 9], [1405, 13, 1070, 11], [1405, 18, 1070, 16], [1406, 6, 1071, 4, "max<PERSON><PERSON><PERSON>"], [1406, 14, 1071, 12], [1406, 16, 1071, 14], [1406, 19, 1071, 17], [1407, 6, 1072, 4, "alignItems"], [1407, 16, 1072, 14], [1407, 18, 1072, 16], [1408, 4, 1073, 2], [1408, 5, 1073, 3], [1409, 4, 1074, 2, "processingTitle"], [1409, 19, 1074, 17], [1409, 21, 1074, 19], [1410, 6, 1075, 4, "fontSize"], [1410, 14, 1075, 12], [1410, 16, 1075, 14], [1410, 18, 1075, 16], [1411, 6, 1076, 4, "fontWeight"], [1411, 16, 1076, 14], [1411, 18, 1076, 16], [1411, 23, 1076, 21], [1412, 6, 1077, 4, "color"], [1412, 11, 1077, 9], [1412, 13, 1077, 11], [1412, 22, 1077, 20], [1413, 6, 1078, 4, "marginTop"], [1413, 15, 1078, 13], [1413, 17, 1078, 15], [1413, 19, 1078, 17], [1414, 6, 1079, 4, "marginBottom"], [1414, 18, 1079, 16], [1414, 20, 1079, 18], [1415, 4, 1080, 2], [1415, 5, 1080, 3], [1416, 4, 1081, 2, "progressBar"], [1416, 15, 1081, 13], [1416, 17, 1081, 15], [1417, 6, 1082, 4, "width"], [1417, 11, 1082, 9], [1417, 13, 1082, 11], [1417, 19, 1082, 17], [1418, 6, 1083, 4, "height"], [1418, 12, 1083, 10], [1418, 14, 1083, 12], [1418, 15, 1083, 13], [1419, 6, 1084, 4, "backgroundColor"], [1419, 21, 1084, 19], [1419, 23, 1084, 21], [1419, 32, 1084, 30], [1420, 6, 1085, 4, "borderRadius"], [1420, 18, 1085, 16], [1420, 20, 1085, 18], [1420, 21, 1085, 19], [1421, 6, 1086, 4, "overflow"], [1421, 14, 1086, 12], [1421, 16, 1086, 14], [1421, 24, 1086, 22], [1422, 6, 1087, 4, "marginBottom"], [1422, 18, 1087, 16], [1422, 20, 1087, 18], [1423, 4, 1088, 2], [1423, 5, 1088, 3], [1424, 4, 1089, 2, "progressFill"], [1424, 16, 1089, 14], [1424, 18, 1089, 16], [1425, 6, 1090, 4, "height"], [1425, 12, 1090, 10], [1425, 14, 1090, 12], [1425, 20, 1090, 18], [1426, 6, 1091, 4, "backgroundColor"], [1426, 21, 1091, 19], [1426, 23, 1091, 21], [1426, 32, 1091, 30], [1427, 6, 1092, 4, "borderRadius"], [1427, 18, 1092, 16], [1427, 20, 1092, 18], [1428, 4, 1093, 2], [1428, 5, 1093, 3], [1429, 4, 1094, 2, "processingDescription"], [1429, 25, 1094, 23], [1429, 27, 1094, 25], [1430, 6, 1095, 4, "fontSize"], [1430, 14, 1095, 12], [1430, 16, 1095, 14], [1430, 18, 1095, 16], [1431, 6, 1096, 4, "color"], [1431, 11, 1096, 9], [1431, 13, 1096, 11], [1431, 22, 1096, 20], [1432, 6, 1097, 4, "textAlign"], [1432, 15, 1097, 13], [1432, 17, 1097, 15], [1433, 4, 1098, 2], [1433, 5, 1098, 3], [1434, 4, 1099, 2, "successIcon"], [1434, 15, 1099, 13], [1434, 17, 1099, 15], [1435, 6, 1100, 4, "marginTop"], [1435, 15, 1100, 13], [1435, 17, 1100, 15], [1436, 4, 1101, 2], [1436, 5, 1101, 3], [1437, 4, 1102, 2, "errorContent"], [1437, 16, 1102, 14], [1437, 18, 1102, 16], [1438, 6, 1103, 4, "backgroundColor"], [1438, 21, 1103, 19], [1438, 23, 1103, 21], [1438, 29, 1103, 27], [1439, 6, 1104, 4, "borderRadius"], [1439, 18, 1104, 16], [1439, 20, 1104, 18], [1439, 22, 1104, 20], [1440, 6, 1105, 4, "padding"], [1440, 13, 1105, 11], [1440, 15, 1105, 13], [1440, 17, 1105, 15], [1441, 6, 1106, 4, "width"], [1441, 11, 1106, 9], [1441, 13, 1106, 11], [1441, 18, 1106, 16], [1442, 6, 1107, 4, "max<PERSON><PERSON><PERSON>"], [1442, 14, 1107, 12], [1442, 16, 1107, 14], [1442, 19, 1107, 17], [1443, 6, 1108, 4, "alignItems"], [1443, 16, 1108, 14], [1443, 18, 1108, 16], [1444, 4, 1109, 2], [1444, 5, 1109, 3], [1445, 4, 1110, 2, "errorTitle"], [1445, 14, 1110, 12], [1445, 16, 1110, 14], [1446, 6, 1111, 4, "fontSize"], [1446, 14, 1111, 12], [1446, 16, 1111, 14], [1446, 18, 1111, 16], [1447, 6, 1112, 4, "fontWeight"], [1447, 16, 1112, 14], [1447, 18, 1112, 16], [1447, 23, 1112, 21], [1448, 6, 1113, 4, "color"], [1448, 11, 1113, 9], [1448, 13, 1113, 11], [1448, 22, 1113, 20], [1449, 6, 1114, 4, "marginTop"], [1449, 15, 1114, 13], [1449, 17, 1114, 15], [1449, 19, 1114, 17], [1450, 6, 1115, 4, "marginBottom"], [1450, 18, 1115, 16], [1450, 20, 1115, 18], [1451, 4, 1116, 2], [1451, 5, 1116, 3], [1452, 4, 1117, 2, "errorMessage"], [1452, 16, 1117, 14], [1452, 18, 1117, 16], [1453, 6, 1118, 4, "fontSize"], [1453, 14, 1118, 12], [1453, 16, 1118, 14], [1453, 18, 1118, 16], [1454, 6, 1119, 4, "color"], [1454, 11, 1119, 9], [1454, 13, 1119, 11], [1454, 22, 1119, 20], [1455, 6, 1120, 4, "textAlign"], [1455, 15, 1120, 13], [1455, 17, 1120, 15], [1455, 25, 1120, 23], [1456, 6, 1121, 4, "marginBottom"], [1456, 18, 1121, 16], [1456, 20, 1121, 18], [1457, 4, 1122, 2], [1457, 5, 1122, 3], [1458, 4, 1123, 2, "primaryButton"], [1458, 17, 1123, 15], [1458, 19, 1123, 17], [1459, 6, 1124, 4, "backgroundColor"], [1459, 21, 1124, 19], [1459, 23, 1124, 21], [1459, 32, 1124, 30], [1460, 6, 1125, 4, "paddingHorizontal"], [1460, 23, 1125, 21], [1460, 25, 1125, 23], [1460, 27, 1125, 25], [1461, 6, 1126, 4, "paddingVertical"], [1461, 21, 1126, 19], [1461, 23, 1126, 21], [1461, 25, 1126, 23], [1462, 6, 1127, 4, "borderRadius"], [1462, 18, 1127, 16], [1462, 20, 1127, 18], [1462, 21, 1127, 19], [1463, 6, 1128, 4, "marginTop"], [1463, 15, 1128, 13], [1463, 17, 1128, 15], [1464, 4, 1129, 2], [1464, 5, 1129, 3], [1465, 4, 1130, 2, "primaryButtonText"], [1465, 21, 1130, 19], [1465, 23, 1130, 21], [1466, 6, 1131, 4, "color"], [1466, 11, 1131, 9], [1466, 13, 1131, 11], [1466, 19, 1131, 17], [1467, 6, 1132, 4, "fontSize"], [1467, 14, 1132, 12], [1467, 16, 1132, 14], [1467, 18, 1132, 16], [1468, 6, 1133, 4, "fontWeight"], [1468, 16, 1133, 14], [1468, 18, 1133, 16], [1469, 4, 1134, 2], [1469, 5, 1134, 3], [1470, 4, 1135, 2, "secondaryButton"], [1470, 19, 1135, 17], [1470, 21, 1135, 19], [1471, 6, 1136, 4, "paddingHorizontal"], [1471, 23, 1136, 21], [1471, 25, 1136, 23], [1471, 27, 1136, 25], [1472, 6, 1137, 4, "paddingVertical"], [1472, 21, 1137, 19], [1472, 23, 1137, 21], [1472, 25, 1137, 23], [1473, 6, 1138, 4, "marginTop"], [1473, 15, 1138, 13], [1473, 17, 1138, 15], [1474, 4, 1139, 2], [1474, 5, 1139, 3], [1475, 4, 1140, 2, "secondaryButtonText"], [1475, 23, 1140, 21], [1475, 25, 1140, 23], [1476, 6, 1141, 4, "color"], [1476, 11, 1141, 9], [1476, 13, 1141, 11], [1476, 22, 1141, 20], [1477, 6, 1142, 4, "fontSize"], [1477, 14, 1142, 12], [1477, 16, 1142, 14], [1478, 4, 1143, 2], [1478, 5, 1143, 3], [1479, 4, 1144, 2, "permissionContent"], [1479, 21, 1144, 19], [1479, 23, 1144, 21], [1480, 6, 1145, 4, "flex"], [1480, 10, 1145, 8], [1480, 12, 1145, 10], [1480, 13, 1145, 11], [1481, 6, 1146, 4, "justifyContent"], [1481, 20, 1146, 18], [1481, 22, 1146, 20], [1481, 30, 1146, 28], [1482, 6, 1147, 4, "alignItems"], [1482, 16, 1147, 14], [1482, 18, 1147, 16], [1482, 26, 1147, 24], [1483, 6, 1148, 4, "padding"], [1483, 13, 1148, 11], [1483, 15, 1148, 13], [1484, 4, 1149, 2], [1484, 5, 1149, 3], [1485, 4, 1150, 2, "permissionTitle"], [1485, 19, 1150, 17], [1485, 21, 1150, 19], [1486, 6, 1151, 4, "fontSize"], [1486, 14, 1151, 12], [1486, 16, 1151, 14], [1486, 18, 1151, 16], [1487, 6, 1152, 4, "fontWeight"], [1487, 16, 1152, 14], [1487, 18, 1152, 16], [1487, 23, 1152, 21], [1488, 6, 1153, 4, "color"], [1488, 11, 1153, 9], [1488, 13, 1153, 11], [1488, 22, 1153, 20], [1489, 6, 1154, 4, "marginTop"], [1489, 15, 1154, 13], [1489, 17, 1154, 15], [1489, 19, 1154, 17], [1490, 6, 1155, 4, "marginBottom"], [1490, 18, 1155, 16], [1490, 20, 1155, 18], [1491, 4, 1156, 2], [1491, 5, 1156, 3], [1492, 4, 1157, 2, "permissionDescription"], [1492, 25, 1157, 23], [1492, 27, 1157, 25], [1493, 6, 1158, 4, "fontSize"], [1493, 14, 1158, 12], [1493, 16, 1158, 14], [1493, 18, 1158, 16], [1494, 6, 1159, 4, "color"], [1494, 11, 1159, 9], [1494, 13, 1159, 11], [1494, 22, 1159, 20], [1495, 6, 1160, 4, "textAlign"], [1495, 15, 1160, 13], [1495, 17, 1160, 15], [1495, 25, 1160, 23], [1496, 6, 1161, 4, "marginBottom"], [1496, 18, 1161, 16], [1496, 20, 1161, 18], [1497, 4, 1162, 2], [1497, 5, 1162, 3], [1498, 4, 1163, 2, "loadingText"], [1498, 15, 1163, 13], [1498, 17, 1163, 15], [1499, 6, 1164, 4, "color"], [1499, 11, 1164, 9], [1499, 13, 1164, 11], [1499, 22, 1164, 20], [1500, 6, 1165, 4, "marginTop"], [1500, 15, 1165, 13], [1500, 17, 1165, 15], [1501, 4, 1166, 2], [1501, 5, 1166, 3], [1502, 4, 1167, 2], [1503, 4, 1168, 2, "blurZone"], [1503, 12, 1168, 10], [1503, 14, 1168, 12], [1504, 6, 1169, 4, "position"], [1504, 14, 1169, 12], [1504, 16, 1169, 14], [1504, 26, 1169, 24], [1505, 6, 1170, 4, "overflow"], [1505, 14, 1170, 12], [1505, 16, 1170, 14], [1506, 4, 1171, 2], [1506, 5, 1171, 3], [1507, 4, 1172, 2, "previewChip"], [1507, 15, 1172, 13], [1507, 17, 1172, 15], [1508, 6, 1173, 4, "position"], [1508, 14, 1173, 12], [1508, 16, 1173, 14], [1508, 26, 1173, 24], [1509, 6, 1174, 4, "top"], [1509, 9, 1174, 7], [1509, 11, 1174, 9], [1509, 12, 1174, 10], [1510, 6, 1175, 4, "right"], [1510, 11, 1175, 9], [1510, 13, 1175, 11], [1510, 14, 1175, 12], [1511, 6, 1176, 4, "backgroundColor"], [1511, 21, 1176, 19], [1511, 23, 1176, 21], [1511, 40, 1176, 38], [1512, 6, 1177, 4, "paddingHorizontal"], [1512, 23, 1177, 21], [1512, 25, 1177, 23], [1512, 27, 1177, 25], [1513, 6, 1178, 4, "paddingVertical"], [1513, 21, 1178, 19], [1513, 23, 1178, 21], [1513, 24, 1178, 22], [1514, 6, 1179, 4, "borderRadius"], [1514, 18, 1179, 16], [1514, 20, 1179, 18], [1515, 4, 1180, 2], [1515, 5, 1180, 3], [1516, 4, 1181, 2, "previewChipText"], [1516, 19, 1181, 17], [1516, 21, 1181, 19], [1517, 6, 1182, 4, "color"], [1517, 11, 1182, 9], [1517, 13, 1182, 11], [1517, 19, 1182, 17], [1518, 6, 1183, 4, "fontSize"], [1518, 14, 1183, 12], [1518, 16, 1183, 14], [1518, 18, 1183, 16], [1519, 6, 1184, 4, "fontWeight"], [1519, 16, 1184, 14], [1519, 18, 1184, 16], [1520, 4, 1185, 2], [1521, 2, 1186, 0], [1521, 3, 1186, 1], [1521, 4, 1186, 2], [1522, 2, 1186, 3], [1522, 6, 1186, 3, "_c"], [1522, 8, 1186, 3], [1523, 2, 1186, 3, "$RefreshReg$"], [1523, 14, 1186, 3], [1523, 15, 1186, 3, "_c"], [1523, 17, 1186, 3], [1524, 0, 1186, 3], [1524, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadMediaPipeFaceDetection", "Promise$argument_0", "detectFacesWithMediaPipe", "detectFacesHeuristic", "countSkinPixelsInRegion", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;qCGG;wBCG;ODM;GHE;mCKE;GLI;+BME;GNqC;kCOE;GPgB;qBQE;GRQ;8BSE;GT4B;2BUE;GVa;wBWE;GXiB;0BYG;GZqD;0BaE;GbuB;gCcE;kBCa;KDG;GdC;mCgBG;wBZc,kCY;GhBoC;mCiBE;wBba;OaI;oFCkC;UDM;8BEW;SFwB;uDba;sBgBC,wBhB;OaC;GjBe;6BqBG;GrB6B;kCsBG;GtB8C;4BuBE;mBCmD;SDE;GvBO;uByBE;GzBI;mC0BG;G1BM;YCE;GDK;oB2B2C;W3BG;yB4BC;W5BG;wB6BC;W7BI;CD4L"}}, "type": "js/module"}]}