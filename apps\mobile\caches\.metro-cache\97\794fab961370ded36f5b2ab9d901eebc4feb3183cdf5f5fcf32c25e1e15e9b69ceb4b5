{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        await processImageWithFaceBlur(photo.uri);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] face-api.js also failed:', faceApiError);\n            detectedFaces = [];\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] Blurring face ${index + 1} at (${paddedX}, ${paddedY}) size ${paddedWidth}x${paddedHeight}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(8, Math.min(paddedWidth, paddedHeight) / 20); // Adaptive pixel size\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n          });\n        } else {\n          console.log('[EchoCameraWeb] No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] Processing complete:', result);\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 672,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1326, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 53, 128, 51], [126, 55, 128, 53, "photo"], [126, 60, 128, 58], [126, 61, 128, 59, "uri"], [126, 64, 128, 62], [126, 65, 128, 63], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6], [130, 14, 132, 12, "processImageWithFaceBlur"], [130, 38, 132, 36], [130, 39, 132, 37, "photo"], [130, 44, 132, 42], [130, 45, 132, 43, "uri"], [130, 48, 132, 46], [130, 49, 132, 47], [131, 6, 133, 4], [131, 7, 133, 5], [131, 8, 133, 6], [131, 15, 133, 13, "error"], [131, 20, 133, 18], [131, 22, 133, 20], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "error"], [132, 21, 134, 19], [132, 22, 134, 20], [132, 54, 134, 52], [132, 56, 134, 54, "error"], [132, 61, 134, 59], [132, 62, 134, 60], [133, 8, 135, 6, "setErrorMessage"], [133, 23, 135, 21], [133, 24, 135, 22], [133, 68, 135, 66], [133, 69, 135, 67], [134, 8, 136, 6, "setProcessingState"], [134, 26, 136, 24], [134, 27, 136, 25], [134, 34, 136, 32], [134, 35, 136, 33], [135, 6, 137, 4], [136, 4, 138, 2], [136, 5, 138, 3], [136, 7, 138, 5], [136, 9, 138, 7], [136, 10, 138, 8], [137, 4, 139, 2], [138, 4, 140, 2], [138, 10, 140, 8, "processImageWithFaceBlur"], [138, 34, 140, 32], [138, 37, 140, 35], [138, 43, 140, 42, "photoUri"], [138, 51, 140, 58], [138, 55, 140, 63], [139, 6, 141, 4], [139, 10, 141, 8], [140, 8, 142, 6, "setProcessingState"], [140, 26, 142, 24], [140, 27, 142, 25], [140, 39, 142, 37], [140, 40, 142, 38], [141, 8, 143, 6, "setProcessingProgress"], [141, 29, 143, 27], [141, 30, 143, 28], [141, 32, 143, 30], [141, 33, 143, 31], [143, 8, 145, 6], [144, 8, 146, 6], [144, 14, 146, 12, "canvas"], [144, 20, 146, 18], [144, 23, 146, 21, "document"], [144, 31, 146, 29], [144, 32, 146, 30, "createElement"], [144, 45, 146, 43], [144, 46, 146, 44], [144, 54, 146, 52], [144, 55, 146, 53], [145, 8, 147, 6], [145, 14, 147, 12, "ctx"], [145, 17, 147, 15], [145, 20, 147, 18, "canvas"], [145, 26, 147, 24], [145, 27, 147, 25, "getContext"], [145, 37, 147, 35], [145, 38, 147, 36], [145, 42, 147, 40], [145, 43, 147, 41], [146, 8, 148, 6], [146, 12, 148, 10], [146, 13, 148, 11, "ctx"], [146, 16, 148, 14], [146, 18, 148, 16], [146, 24, 148, 22], [146, 28, 148, 26, "Error"], [146, 33, 148, 31], [146, 34, 148, 32], [146, 64, 148, 62], [146, 65, 148, 63], [148, 8, 150, 6], [149, 8, 151, 6], [149, 14, 151, 12, "img"], [149, 17, 151, 15], [149, 20, 151, 18], [149, 24, 151, 22, "Image"], [149, 29, 151, 27], [149, 30, 151, 28], [149, 31, 151, 29], [150, 8, 152, 6], [150, 14, 152, 12], [150, 18, 152, 16, "Promise"], [150, 25, 152, 23], [150, 26, 152, 24], [150, 27, 152, 25, "resolve"], [150, 34, 152, 32], [150, 36, 152, 34, "reject"], [150, 42, 152, 40], [150, 47, 152, 45], [151, 10, 153, 8, "img"], [151, 13, 153, 11], [151, 14, 153, 12, "onload"], [151, 20, 153, 18], [151, 23, 153, 21, "resolve"], [151, 30, 153, 28], [152, 10, 154, 8, "img"], [152, 13, 154, 11], [152, 14, 154, 12, "onerror"], [152, 21, 154, 19], [152, 24, 154, 22, "reject"], [152, 30, 154, 28], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "src"], [153, 17, 155, 15], [153, 20, 155, 18, "photoUri"], [153, 28, 155, 26], [154, 8, 156, 6], [154, 9, 156, 7], [154, 10, 156, 8], [156, 8, 158, 6], [157, 8, 159, 6, "canvas"], [157, 14, 159, 12], [157, 15, 159, 13, "width"], [157, 20, 159, 18], [157, 23, 159, 21, "img"], [157, 26, 159, 24], [157, 27, 159, 25, "width"], [157, 32, 159, 30], [158, 8, 160, 6, "canvas"], [158, 14, 160, 12], [158, 15, 160, 13, "height"], [158, 21, 160, 19], [158, 24, 160, 22, "img"], [158, 27, 160, 25], [158, 28, 160, 26, "height"], [158, 34, 160, 32], [160, 8, 162, 6], [161, 8, 163, 6, "ctx"], [161, 11, 163, 9], [161, 12, 163, 10, "drawImage"], [161, 21, 163, 19], [161, 22, 163, 20, "img"], [161, 25, 163, 23], [161, 27, 163, 25], [161, 28, 163, 26], [161, 30, 163, 28], [161, 31, 163, 29], [161, 32, 163, 30], [162, 8, 165, 6, "setProcessingProgress"], [162, 29, 165, 27], [162, 30, 165, 28], [162, 32, 165, 30], [162, 33, 165, 31], [164, 8, 167, 6], [165, 8, 168, 6], [165, 12, 168, 10, "detectedFaces"], [165, 25, 168, 23], [165, 28, 168, 26], [165, 30, 168, 28], [166, 8, 170, 6, "console"], [166, 15, 170, 13], [166, 16, 170, 14, "log"], [166, 19, 170, 17], [166, 20, 170, 18], [166, 74, 170, 72], [166, 76, 170, 74], [167, 10, 171, 8, "width"], [167, 15, 171, 13], [167, 17, 171, 15, "img"], [167, 20, 171, 18], [167, 21, 171, 19, "width"], [167, 26, 171, 24], [168, 10, 172, 8, "height"], [168, 16, 172, 14], [168, 18, 172, 16, "img"], [168, 21, 172, 19], [168, 22, 172, 20, "height"], [168, 28, 172, 26], [169, 10, 173, 8, "src"], [169, 13, 173, 11], [169, 15, 173, 13, "photoUri"], [169, 23, 173, 21], [169, 24, 173, 22, "substring"], [169, 33, 173, 31], [169, 34, 173, 32], [169, 35, 173, 33], [169, 37, 173, 35], [169, 39, 173, 37], [169, 40, 173, 38], [169, 43, 173, 41], [170, 8, 174, 6], [170, 9, 174, 7], [170, 10, 174, 8], [172, 8, 176, 6], [173, 8, 177, 6], [173, 12, 177, 10], [174, 10, 178, 8], [174, 14, 178, 12], [174, 28, 178, 26], [174, 32, 178, 30, "window"], [174, 38, 178, 36], [174, 40, 178, 38], [175, 12, 179, 10, "console"], [175, 19, 179, 17], [175, 20, 179, 18, "log"], [175, 23, 179, 21], [175, 24, 179, 22], [175, 105, 179, 103], [175, 106, 179, 104], [176, 12, 180, 10], [176, 18, 180, 16, "faceDetector"], [176, 30, 180, 28], [176, 33, 180, 31], [176, 37, 180, 36, "window"], [176, 43, 180, 42], [176, 44, 180, 51, "FaceDetector"], [176, 56, 180, 63], [176, 57, 180, 64], [177, 14, 181, 12, "maxDetectedFaces"], [177, 30, 181, 28], [177, 32, 181, 30], [177, 34, 181, 32], [178, 14, 182, 12, "fastMode"], [178, 22, 182, 20], [178, 24, 182, 22], [179, 12, 183, 10], [179, 13, 183, 11], [179, 14, 183, 12], [180, 12, 185, 10], [180, 18, 185, 16, "browserDetections"], [180, 35, 185, 33], [180, 38, 185, 36], [180, 44, 185, 42, "faceDetector"], [180, 56, 185, 54], [180, 57, 185, 55, "detect"], [180, 63, 185, 61], [180, 64, 185, 62, "img"], [180, 67, 185, 65], [180, 68, 185, 66], [181, 12, 186, 10, "detectedFaces"], [181, 25, 186, 23], [181, 28, 186, 26, "browserDetections"], [181, 45, 186, 43], [181, 46, 186, 44, "map"], [181, 49, 186, 47], [181, 50, 186, 49, "detection"], [181, 59, 186, 63], [181, 64, 186, 69], [182, 14, 187, 12, "boundingBox"], [182, 25, 187, 23], [182, 27, 187, 25], [183, 16, 188, 14, "xCenter"], [183, 23, 188, 21], [183, 25, 188, 23], [183, 26, 188, 24, "detection"], [183, 35, 188, 33], [183, 36, 188, 34, "boundingBox"], [183, 47, 188, 45], [183, 48, 188, 46, "x"], [183, 49, 188, 47], [183, 52, 188, 50, "detection"], [183, 61, 188, 59], [183, 62, 188, 60, "boundingBox"], [183, 73, 188, 71], [183, 74, 188, 72, "width"], [183, 79, 188, 77], [183, 82, 188, 80], [183, 83, 188, 81], [183, 87, 188, 85, "img"], [183, 90, 188, 88], [183, 91, 188, 89, "width"], [183, 96, 188, 94], [184, 16, 189, 14, "yCenter"], [184, 23, 189, 21], [184, 25, 189, 23], [184, 26, 189, 24, "detection"], [184, 35, 189, 33], [184, 36, 189, 34, "boundingBox"], [184, 47, 189, 45], [184, 48, 189, 46, "y"], [184, 49, 189, 47], [184, 52, 189, 50, "detection"], [184, 61, 189, 59], [184, 62, 189, 60, "boundingBox"], [184, 73, 189, 71], [184, 74, 189, 72, "height"], [184, 80, 189, 78], [184, 83, 189, 81], [184, 84, 189, 82], [184, 88, 189, 86, "img"], [184, 91, 189, 89], [184, 92, 189, 90, "height"], [184, 98, 189, 96], [185, 16, 190, 14, "width"], [185, 21, 190, 19], [185, 23, 190, 21, "detection"], [185, 32, 190, 30], [185, 33, 190, 31, "boundingBox"], [185, 44, 190, 42], [185, 45, 190, 43, "width"], [185, 50, 190, 48], [185, 53, 190, 51, "img"], [185, 56, 190, 54], [185, 57, 190, 55, "width"], [185, 62, 190, 60], [186, 16, 191, 14, "height"], [186, 22, 191, 20], [186, 24, 191, 22, "detection"], [186, 33, 191, 31], [186, 34, 191, 32, "boundingBox"], [186, 45, 191, 43], [186, 46, 191, 44, "height"], [186, 52, 191, 50], [186, 55, 191, 53, "img"], [186, 58, 191, 56], [186, 59, 191, 57, "height"], [187, 14, 192, 12], [188, 12, 193, 10], [188, 13, 193, 11], [188, 14, 193, 12], [188, 15, 193, 13], [189, 12, 194, 10, "console"], [189, 19, 194, 17], [189, 20, 194, 18, "log"], [189, 23, 194, 21], [189, 24, 194, 22], [189, 78, 194, 76, "detectedFaces"], [189, 91, 194, 89], [189, 92, 194, 90, "length"], [189, 98, 194, 96], [189, 106, 194, 104], [189, 107, 194, 105], [190, 10, 195, 8], [190, 11, 195, 9], [190, 17, 195, 15], [191, 12, 196, 10, "console"], [191, 19, 196, 17], [191, 20, 196, 18, "log"], [191, 23, 196, 21], [191, 24, 196, 22], [191, 100, 196, 98], [191, 101, 196, 99], [192, 12, 197, 10], [192, 18, 197, 16], [192, 22, 197, 20, "Error"], [192, 27, 197, 25], [192, 28, 197, 26], [192, 70, 197, 68], [192, 71, 197, 69], [193, 10, 198, 8], [194, 8, 199, 6], [194, 9, 199, 7], [194, 10, 199, 8], [194, 17, 199, 15, "browserError"], [194, 29, 199, 27], [194, 31, 199, 29], [195, 10, 200, 8, "console"], [195, 17, 200, 15], [195, 18, 200, 16, "warn"], [195, 22, 200, 20], [195, 23, 200, 21], [195, 102, 200, 100], [195, 104, 200, 102, "browserError"], [195, 116, 200, 114], [195, 117, 200, 115], [197, 10, 202, 8], [198, 10, 203, 8], [198, 14, 203, 12], [199, 12, 204, 10], [200, 12, 205, 10], [200, 16, 205, 14], [200, 17, 205, 16, "window"], [200, 23, 205, 22], [200, 24, 205, 31, "<PERSON>ap<PERSON>"], [200, 31, 205, 38], [200, 33, 205, 40], [201, 14, 206, 12], [201, 20, 206, 18], [201, 24, 206, 22, "Promise"], [201, 31, 206, 29], [201, 32, 206, 30], [201, 33, 206, 31, "resolve"], [201, 40, 206, 38], [201, 42, 206, 40, "reject"], [201, 48, 206, 46], [201, 53, 206, 51], [202, 16, 207, 14], [202, 22, 207, 20, "script"], [202, 28, 207, 26], [202, 31, 207, 29, "document"], [202, 39, 207, 37], [202, 40, 207, 38, "createElement"], [202, 53, 207, 51], [202, 54, 207, 52], [202, 62, 207, 60], [202, 63, 207, 61], [203, 16, 208, 14, "script"], [203, 22, 208, 20], [203, 23, 208, 21, "src"], [203, 26, 208, 24], [203, 29, 208, 27], [203, 99, 208, 97], [204, 16, 209, 14, "script"], [204, 22, 209, 20], [204, 23, 209, 21, "onload"], [204, 29, 209, 27], [204, 32, 209, 30, "resolve"], [204, 39, 209, 37], [205, 16, 210, 14, "script"], [205, 22, 210, 20], [205, 23, 210, 21, "onerror"], [205, 30, 210, 28], [205, 33, 210, 31, "reject"], [205, 39, 210, 37], [206, 16, 211, 14, "document"], [206, 24, 211, 22], [206, 25, 211, 23, "head"], [206, 29, 211, 27], [206, 30, 211, 28, "append<PERSON><PERSON><PERSON>"], [206, 41, 211, 39], [206, 42, 211, 40, "script"], [206, 48, 211, 46], [206, 49, 211, 47], [207, 14, 212, 12], [207, 15, 212, 13], [207, 16, 212, 14], [208, 12, 213, 10], [209, 12, 215, 10], [209, 18, 215, 16, "<PERSON>ap<PERSON>"], [209, 25, 215, 23], [209, 28, 215, 27, "window"], [209, 34, 215, 33], [209, 35, 215, 42, "<PERSON>ap<PERSON>"], [209, 42, 215, 49], [211, 12, 217, 10], [212, 12, 218, 10], [212, 18, 218, 16, "Promise"], [212, 25, 218, 23], [212, 26, 218, 24, "all"], [212, 29, 218, 27], [212, 30, 218, 28], [212, 31, 219, 12, "<PERSON>ap<PERSON>"], [212, 38, 219, 19], [212, 39, 219, 20, "nets"], [212, 43, 219, 24], [212, 44, 219, 25, "tinyFaceDetector"], [212, 60, 219, 41], [212, 61, 219, 42, "loadFromUri"], [212, 72, 219, 53], [212, 73, 219, 54], [212, 130, 219, 111], [212, 131, 219, 112], [212, 133, 220, 12, "<PERSON>ap<PERSON>"], [212, 140, 220, 19], [212, 141, 220, 20, "nets"], [212, 145, 220, 24], [212, 146, 220, 25, "faceLandmark68Net"], [212, 163, 220, 42], [212, 164, 220, 43, "loadFromUri"], [212, 175, 220, 54], [212, 176, 220, 55], [212, 233, 220, 112], [212, 234, 220, 113], [212, 235, 221, 11], [212, 236, 221, 12], [214, 12, 223, 10], [215, 12, 224, 10], [215, 18, 224, 16, "faceDetections"], [215, 32, 224, 30], [215, 35, 224, 33], [215, 41, 224, 39, "<PERSON>ap<PERSON>"], [215, 48, 224, 46], [215, 49, 224, 47, "detectAllFaces"], [215, 63, 224, 61], [215, 64, 224, 62, "img"], [215, 67, 224, 65], [215, 69, 224, 67], [215, 73, 224, 71, "<PERSON>ap<PERSON>"], [215, 80, 224, 78], [215, 81, 224, 79, "TinyFaceDetectorOptions"], [215, 104, 224, 102], [215, 105, 224, 103], [215, 106, 224, 104], [215, 107, 224, 105], [216, 12, 226, 10, "detectedFaces"], [216, 25, 226, 23], [216, 28, 226, 26, "faceDetections"], [216, 42, 226, 40], [216, 43, 226, 41, "map"], [216, 46, 226, 44], [216, 47, 226, 46, "detection"], [216, 56, 226, 60], [216, 61, 226, 66], [217, 14, 227, 12, "boundingBox"], [217, 25, 227, 23], [217, 27, 227, 25], [218, 16, 228, 14, "xCenter"], [218, 23, 228, 21], [218, 25, 228, 23], [218, 26, 228, 24, "detection"], [218, 35, 228, 33], [218, 36, 228, 34, "box"], [218, 39, 228, 37], [218, 40, 228, 38, "x"], [218, 41, 228, 39], [218, 44, 228, 42, "detection"], [218, 53, 228, 51], [218, 54, 228, 52, "box"], [218, 57, 228, 55], [218, 58, 228, 56, "width"], [218, 63, 228, 61], [218, 66, 228, 64], [218, 67, 228, 65], [218, 71, 228, 69, "img"], [218, 74, 228, 72], [218, 75, 228, 73, "width"], [218, 80, 228, 78], [219, 16, 229, 14, "yCenter"], [219, 23, 229, 21], [219, 25, 229, 23], [219, 26, 229, 24, "detection"], [219, 35, 229, 33], [219, 36, 229, 34, "box"], [219, 39, 229, 37], [219, 40, 229, 38, "y"], [219, 41, 229, 39], [219, 44, 229, 42, "detection"], [219, 53, 229, 51], [219, 54, 229, 52, "box"], [219, 57, 229, 55], [219, 58, 229, 56, "height"], [219, 64, 229, 62], [219, 67, 229, 65], [219, 68, 229, 66], [219, 72, 229, 70, "img"], [219, 75, 229, 73], [219, 76, 229, 74, "height"], [219, 82, 229, 80], [220, 16, 230, 14, "width"], [220, 21, 230, 19], [220, 23, 230, 21, "detection"], [220, 32, 230, 30], [220, 33, 230, 31, "box"], [220, 36, 230, 34], [220, 37, 230, 35, "width"], [220, 42, 230, 40], [220, 45, 230, 43, "img"], [220, 48, 230, 46], [220, 49, 230, 47, "width"], [220, 54, 230, 52], [221, 16, 231, 14, "height"], [221, 22, 231, 20], [221, 24, 231, 22, "detection"], [221, 33, 231, 31], [221, 34, 231, 32, "box"], [221, 37, 231, 35], [221, 38, 231, 36, "height"], [221, 44, 231, 42], [221, 47, 231, 45, "img"], [221, 50, 231, 48], [221, 51, 231, 49, "height"], [222, 14, 232, 12], [223, 12, 233, 10], [223, 13, 233, 11], [223, 14, 233, 12], [223, 15, 233, 13], [224, 12, 235, 10, "console"], [224, 19, 235, 17], [224, 20, 235, 18, "log"], [224, 23, 235, 21], [224, 24, 235, 22], [224, 61, 235, 59, "detectedFaces"], [224, 74, 235, 72], [224, 75, 235, 73, "length"], [224, 81, 235, 79], [224, 89, 235, 87], [224, 90, 235, 88], [225, 10, 236, 8], [225, 11, 236, 9], [225, 12, 236, 10], [225, 19, 236, 17, "faceApiError"], [225, 31, 236, 29], [225, 33, 236, 31], [226, 12, 237, 10, "console"], [226, 19, 237, 17], [226, 20, 237, 18, "warn"], [226, 24, 237, 22], [226, 25, 237, 23], [226, 67, 237, 65], [226, 69, 237, 67, "faceApiError"], [226, 81, 237, 79], [226, 82, 237, 80], [227, 12, 238, 10, "detectedFaces"], [227, 25, 238, 23], [227, 28, 238, 26], [227, 30, 238, 28], [228, 10, 239, 8], [229, 8, 240, 6], [230, 8, 242, 6, "console"], [230, 15, 242, 13], [230, 16, 242, 14, "log"], [230, 19, 242, 17], [230, 20, 242, 18], [230, 72, 242, 70, "detectedFaces"], [230, 85, 242, 83], [230, 86, 242, 84, "length"], [230, 92, 242, 90], [230, 100, 242, 98], [230, 101, 242, 99], [231, 8, 243, 6], [231, 12, 243, 10, "detectedFaces"], [231, 25, 243, 23], [231, 26, 243, 24, "length"], [231, 32, 243, 30], [231, 35, 243, 33], [231, 36, 243, 34], [231, 38, 243, 36], [232, 10, 244, 8, "console"], [232, 17, 244, 15], [232, 18, 244, 16, "log"], [232, 21, 244, 19], [232, 22, 244, 20], [232, 66, 244, 64], [232, 68, 244, 66, "detectedFaces"], [232, 81, 244, 79], [232, 82, 244, 80, "map"], [232, 85, 244, 83], [232, 86, 244, 84], [232, 87, 244, 85, "face"], [232, 91, 244, 89], [232, 93, 244, 91, "i"], [232, 94, 244, 92], [232, 100, 244, 98], [233, 12, 245, 10, "faceNumber"], [233, 22, 245, 20], [233, 24, 245, 22, "i"], [233, 25, 245, 23], [233, 28, 245, 26], [233, 29, 245, 27], [234, 12, 246, 10, "centerX"], [234, 19, 246, 17], [234, 21, 246, 19, "face"], [234, 25, 246, 23], [234, 26, 246, 24, "boundingBox"], [234, 37, 246, 35], [234, 38, 246, 36, "xCenter"], [234, 45, 246, 43], [235, 12, 247, 10, "centerY"], [235, 19, 247, 17], [235, 21, 247, 19, "face"], [235, 25, 247, 23], [235, 26, 247, 24, "boundingBox"], [235, 37, 247, 35], [235, 38, 247, 36, "yCenter"], [235, 45, 247, 43], [236, 12, 248, 10, "width"], [236, 17, 248, 15], [236, 19, 248, 17, "face"], [236, 23, 248, 21], [236, 24, 248, 22, "boundingBox"], [236, 35, 248, 33], [236, 36, 248, 34, "width"], [236, 41, 248, 39], [237, 12, 249, 10, "height"], [237, 18, 249, 16], [237, 20, 249, 18, "face"], [237, 24, 249, 22], [237, 25, 249, 23, "boundingBox"], [237, 36, 249, 34], [237, 37, 249, 35, "height"], [238, 10, 250, 8], [238, 11, 250, 9], [238, 12, 250, 10], [238, 13, 250, 11], [238, 14, 250, 12], [239, 8, 251, 6], [239, 9, 251, 7], [239, 15, 251, 13], [240, 10, 252, 8, "console"], [240, 17, 252, 15], [240, 18, 252, 16, "log"], [240, 21, 252, 19], [240, 22, 252, 20], [240, 91, 252, 89], [240, 92, 252, 90], [241, 8, 253, 6], [242, 8, 255, 6, "setProcessingProgress"], [242, 29, 255, 27], [242, 30, 255, 28], [242, 32, 255, 30], [242, 33, 255, 31], [244, 8, 257, 6], [245, 8, 258, 6], [245, 12, 258, 10, "detectedFaces"], [245, 25, 258, 23], [245, 26, 258, 24, "length"], [245, 32, 258, 30], [245, 35, 258, 33], [245, 36, 258, 34], [245, 38, 258, 36], [246, 10, 259, 8, "detectedFaces"], [246, 23, 259, 21], [246, 24, 259, 22, "for<PERSON>ach"], [246, 31, 259, 29], [246, 32, 259, 30], [246, 33, 259, 31, "detection"], [246, 42, 259, 40], [246, 44, 259, 42, "index"], [246, 49, 259, 47], [246, 54, 259, 52], [247, 12, 260, 10], [247, 18, 260, 16, "bbox"], [247, 22, 260, 20], [247, 25, 260, 23, "detection"], [247, 34, 260, 32], [247, 35, 260, 33, "boundingBox"], [247, 46, 260, 44], [249, 12, 262, 10], [250, 12, 263, 10], [250, 18, 263, 16, "faceX"], [250, 23, 263, 21], [250, 26, 263, 24, "bbox"], [250, 30, 263, 28], [250, 31, 263, 29, "xCenter"], [250, 38, 263, 36], [250, 41, 263, 39, "img"], [250, 44, 263, 42], [250, 45, 263, 43, "width"], [250, 50, 263, 48], [250, 53, 263, 52, "bbox"], [250, 57, 263, 56], [250, 58, 263, 57, "width"], [250, 63, 263, 62], [250, 66, 263, 65, "img"], [250, 69, 263, 68], [250, 70, 263, 69, "width"], [250, 75, 263, 74], [250, 78, 263, 78], [250, 79, 263, 79], [251, 12, 264, 10], [251, 18, 264, 16, "faceY"], [251, 23, 264, 21], [251, 26, 264, 24, "bbox"], [251, 30, 264, 28], [251, 31, 264, 29, "yCenter"], [251, 38, 264, 36], [251, 41, 264, 39, "img"], [251, 44, 264, 42], [251, 45, 264, 43, "height"], [251, 51, 264, 49], [251, 54, 264, 53, "bbox"], [251, 58, 264, 57], [251, 59, 264, 58, "height"], [251, 65, 264, 64], [251, 68, 264, 67, "img"], [251, 71, 264, 70], [251, 72, 264, 71, "height"], [251, 78, 264, 77], [251, 81, 264, 81], [251, 82, 264, 82], [252, 12, 265, 10], [252, 18, 265, 16, "faceWidth"], [252, 27, 265, 25], [252, 30, 265, 28, "bbox"], [252, 34, 265, 32], [252, 35, 265, 33, "width"], [252, 40, 265, 38], [252, 43, 265, 41, "img"], [252, 46, 265, 44], [252, 47, 265, 45, "width"], [252, 52, 265, 50], [253, 12, 266, 10], [253, 18, 266, 16, "faceHeight"], [253, 28, 266, 26], [253, 31, 266, 29, "bbox"], [253, 35, 266, 33], [253, 36, 266, 34, "height"], [253, 42, 266, 40], [253, 45, 266, 43, "img"], [253, 48, 266, 46], [253, 49, 266, 47, "height"], [253, 55, 266, 53], [255, 12, 268, 10], [256, 12, 269, 10], [256, 18, 269, 16, "padding"], [256, 25, 269, 23], [256, 28, 269, 26], [256, 31, 269, 29], [256, 32, 269, 30], [256, 33, 269, 31], [257, 12, 270, 10], [257, 18, 270, 16, "paddedX"], [257, 25, 270, 23], [257, 28, 270, 26, "Math"], [257, 32, 270, 30], [257, 33, 270, 31, "max"], [257, 36, 270, 34], [257, 37, 270, 35], [257, 38, 270, 36], [257, 40, 270, 38, "faceX"], [257, 45, 270, 43], [257, 48, 270, 46, "faceWidth"], [257, 57, 270, 55], [257, 60, 270, 58, "padding"], [257, 67, 270, 65], [257, 68, 270, 66], [258, 12, 271, 10], [258, 18, 271, 16, "paddedY"], [258, 25, 271, 23], [258, 28, 271, 26, "Math"], [258, 32, 271, 30], [258, 33, 271, 31, "max"], [258, 36, 271, 34], [258, 37, 271, 35], [258, 38, 271, 36], [258, 40, 271, 38, "faceY"], [258, 45, 271, 43], [258, 48, 271, 46, "faceHeight"], [258, 58, 271, 56], [258, 61, 271, 59, "padding"], [258, 68, 271, 66], [258, 69, 271, 67], [259, 12, 272, 10], [259, 18, 272, 16, "<PERSON><PERSON><PERSON><PERSON>"], [259, 29, 272, 27], [259, 32, 272, 30, "Math"], [259, 36, 272, 34], [259, 37, 272, 35, "min"], [259, 40, 272, 38], [259, 41, 272, 39, "img"], [259, 44, 272, 42], [259, 45, 272, 43, "width"], [259, 50, 272, 48], [259, 53, 272, 51, "paddedX"], [259, 60, 272, 58], [259, 62, 272, 60, "faceWidth"], [259, 71, 272, 69], [259, 75, 272, 73], [259, 76, 272, 74], [259, 79, 272, 77], [259, 80, 272, 78], [259, 83, 272, 81, "padding"], [259, 90, 272, 88], [259, 91, 272, 89], [259, 92, 272, 90], [260, 12, 273, 10], [260, 18, 273, 16, "paddedHeight"], [260, 30, 273, 28], [260, 33, 273, 31, "Math"], [260, 37, 273, 35], [260, 38, 273, 36, "min"], [260, 41, 273, 39], [260, 42, 273, 40, "img"], [260, 45, 273, 43], [260, 46, 273, 44, "height"], [260, 52, 273, 50], [260, 55, 273, 53, "paddedY"], [260, 62, 273, 60], [260, 64, 273, 62, "faceHeight"], [260, 74, 273, 72], [260, 78, 273, 76], [260, 79, 273, 77], [260, 82, 273, 80], [260, 83, 273, 81], [260, 86, 273, 84, "padding"], [260, 93, 273, 91], [260, 94, 273, 92], [260, 95, 273, 93], [261, 12, 275, 10, "console"], [261, 19, 275, 17], [261, 20, 275, 18, "log"], [261, 23, 275, 21], [261, 24, 275, 22], [261, 57, 275, 55, "index"], [261, 62, 275, 60], [261, 65, 275, 63], [261, 66, 275, 64], [261, 74, 275, 72, "paddedX"], [261, 81, 275, 79], [261, 86, 275, 84, "paddedY"], [261, 93, 275, 91], [261, 103, 275, 101, "<PERSON><PERSON><PERSON><PERSON>"], [261, 114, 275, 112], [261, 118, 275, 116, "paddedHeight"], [261, 130, 275, 128], [261, 132, 275, 130], [261, 133, 275, 131], [263, 12, 277, 10], [264, 12, 278, 10], [264, 18, 278, 16, "faceImageData"], [264, 31, 278, 29], [264, 34, 278, 32, "ctx"], [264, 37, 278, 35], [264, 38, 278, 36, "getImageData"], [264, 50, 278, 48], [264, 51, 278, 49, "paddedX"], [264, 58, 278, 56], [264, 60, 278, 58, "paddedY"], [264, 67, 278, 65], [264, 69, 278, 67, "<PERSON><PERSON><PERSON><PERSON>"], [264, 80, 278, 78], [264, 82, 278, 80, "paddedHeight"], [264, 94, 278, 92], [264, 95, 278, 93], [265, 12, 279, 10], [265, 18, 279, 16, "data"], [265, 22, 279, 20], [265, 25, 279, 23, "faceImageData"], [265, 38, 279, 36], [265, 39, 279, 37, "data"], [265, 43, 279, 41], [267, 12, 281, 10], [268, 12, 282, 10], [268, 18, 282, 16, "pixelSize"], [268, 27, 282, 25], [268, 30, 282, 28, "Math"], [268, 34, 282, 32], [268, 35, 282, 33, "max"], [268, 38, 282, 36], [268, 39, 282, 37], [268, 40, 282, 38], [268, 42, 282, 40, "Math"], [268, 46, 282, 44], [268, 47, 282, 45, "min"], [268, 50, 282, 48], [268, 51, 282, 49, "<PERSON><PERSON><PERSON><PERSON>"], [268, 62, 282, 60], [268, 64, 282, 62, "paddedHeight"], [268, 76, 282, 74], [268, 77, 282, 75], [268, 80, 282, 78], [268, 82, 282, 80], [268, 83, 282, 81], [268, 84, 282, 82], [268, 85, 282, 83], [269, 12, 283, 10], [269, 17, 283, 15], [269, 21, 283, 19, "y"], [269, 22, 283, 20], [269, 25, 283, 23], [269, 26, 283, 24], [269, 28, 283, 26, "y"], [269, 29, 283, 27], [269, 32, 283, 30, "paddedHeight"], [269, 44, 283, 42], [269, 46, 283, 44, "y"], [269, 47, 283, 45], [269, 51, 283, 49, "pixelSize"], [269, 60, 283, 58], [269, 62, 283, 60], [270, 14, 284, 12], [270, 19, 284, 17], [270, 23, 284, 21, "x"], [270, 24, 284, 22], [270, 27, 284, 25], [270, 28, 284, 26], [270, 30, 284, 28, "x"], [270, 31, 284, 29], [270, 34, 284, 32, "<PERSON><PERSON><PERSON><PERSON>"], [270, 45, 284, 43], [270, 47, 284, 45, "x"], [270, 48, 284, 46], [270, 52, 284, 50, "pixelSize"], [270, 61, 284, 59], [270, 63, 284, 61], [271, 16, 285, 14], [272, 16, 286, 14], [272, 22, 286, 20, "pixelIndex"], [272, 32, 286, 30], [272, 35, 286, 33], [272, 36, 286, 34, "y"], [272, 37, 286, 35], [272, 40, 286, 38, "<PERSON><PERSON><PERSON><PERSON>"], [272, 51, 286, 49], [272, 54, 286, 52, "x"], [272, 55, 286, 53], [272, 59, 286, 57], [272, 60, 286, 58], [273, 16, 287, 14], [273, 22, 287, 20, "r"], [273, 23, 287, 21], [273, 26, 287, 24, "data"], [273, 30, 287, 28], [273, 31, 287, 29, "pixelIndex"], [273, 41, 287, 39], [273, 42, 287, 40], [274, 16, 288, 14], [274, 22, 288, 20, "g"], [274, 23, 288, 21], [274, 26, 288, 24, "data"], [274, 30, 288, 28], [274, 31, 288, 29, "pixelIndex"], [274, 41, 288, 39], [274, 44, 288, 42], [274, 45, 288, 43], [274, 46, 288, 44], [275, 16, 289, 14], [275, 22, 289, 20, "b"], [275, 23, 289, 21], [275, 26, 289, 24, "data"], [275, 30, 289, 28], [275, 31, 289, 29, "pixelIndex"], [275, 41, 289, 39], [275, 44, 289, 42], [275, 45, 289, 43], [275, 46, 289, 44], [276, 16, 290, 14], [276, 22, 290, 20, "a"], [276, 23, 290, 21], [276, 26, 290, 24, "data"], [276, 30, 290, 28], [276, 31, 290, 29, "pixelIndex"], [276, 41, 290, 39], [276, 44, 290, 42], [276, 45, 290, 43], [276, 46, 290, 44], [278, 16, 292, 14], [279, 16, 293, 14], [279, 21, 293, 19], [279, 25, 293, 23, "dy"], [279, 27, 293, 25], [279, 30, 293, 28], [279, 31, 293, 29], [279, 33, 293, 31, "dy"], [279, 35, 293, 33], [279, 38, 293, 36, "pixelSize"], [279, 47, 293, 45], [279, 51, 293, 49, "y"], [279, 52, 293, 50], [279, 55, 293, 53, "dy"], [279, 57, 293, 55], [279, 60, 293, 58, "paddedHeight"], [279, 72, 293, 70], [279, 74, 293, 72, "dy"], [279, 76, 293, 74], [279, 78, 293, 76], [279, 80, 293, 78], [280, 18, 294, 16], [280, 23, 294, 21], [280, 27, 294, 25, "dx"], [280, 29, 294, 27], [280, 32, 294, 30], [280, 33, 294, 31], [280, 35, 294, 33, "dx"], [280, 37, 294, 35], [280, 40, 294, 38, "pixelSize"], [280, 49, 294, 47], [280, 53, 294, 51, "x"], [280, 54, 294, 52], [280, 57, 294, 55, "dx"], [280, 59, 294, 57], [280, 62, 294, 60, "<PERSON><PERSON><PERSON><PERSON>"], [280, 73, 294, 71], [280, 75, 294, 73, "dx"], [280, 77, 294, 75], [280, 79, 294, 77], [280, 81, 294, 79], [281, 20, 295, 18], [281, 26, 295, 24, "blockPixelIndex"], [281, 41, 295, 39], [281, 44, 295, 42], [281, 45, 295, 43], [281, 46, 295, 44, "y"], [281, 47, 295, 45], [281, 50, 295, 48, "dy"], [281, 52, 295, 50], [281, 56, 295, 54, "<PERSON><PERSON><PERSON><PERSON>"], [281, 67, 295, 65], [281, 71, 295, 69, "x"], [281, 72, 295, 70], [281, 75, 295, 73, "dx"], [281, 77, 295, 75], [281, 78, 295, 76], [281, 82, 295, 80], [281, 83, 295, 81], [282, 20, 296, 18, "data"], [282, 24, 296, 22], [282, 25, 296, 23, "blockPixelIndex"], [282, 40, 296, 38], [282, 41, 296, 39], [282, 44, 296, 42, "r"], [282, 45, 296, 43], [283, 20, 297, 18, "data"], [283, 24, 297, 22], [283, 25, 297, 23, "blockPixelIndex"], [283, 40, 297, 38], [283, 43, 297, 41], [283, 44, 297, 42], [283, 45, 297, 43], [283, 48, 297, 46, "g"], [283, 49, 297, 47], [284, 20, 298, 18, "data"], [284, 24, 298, 22], [284, 25, 298, 23, "blockPixelIndex"], [284, 40, 298, 38], [284, 43, 298, 41], [284, 44, 298, 42], [284, 45, 298, 43], [284, 48, 298, 46, "b"], [284, 49, 298, 47], [285, 20, 299, 18, "data"], [285, 24, 299, 22], [285, 25, 299, 23, "blockPixelIndex"], [285, 40, 299, 38], [285, 43, 299, 41], [285, 44, 299, 42], [285, 45, 299, 43], [285, 48, 299, 46, "a"], [285, 49, 299, 47], [286, 18, 300, 16], [287, 16, 301, 14], [288, 14, 302, 12], [289, 12, 303, 10], [291, 12, 305, 10], [292, 12, 306, 10, "ctx"], [292, 15, 306, 13], [292, 16, 306, 14, "putImageData"], [292, 28, 306, 26], [292, 29, 306, 27, "faceImageData"], [292, 42, 306, 40], [292, 44, 306, 42, "paddedX"], [292, 51, 306, 49], [292, 53, 306, 51, "paddedY"], [292, 60, 306, 58], [292, 61, 306, 59], [293, 10, 307, 8], [293, 11, 307, 9], [293, 12, 307, 10], [294, 8, 308, 6], [294, 9, 308, 7], [294, 15, 308, 13], [295, 10, 309, 8, "console"], [295, 17, 309, 15], [295, 18, 309, 16, "log"], [295, 21, 309, 19], [295, 22, 309, 20], [295, 88, 309, 86], [295, 89, 309, 87], [296, 8, 310, 6], [297, 8, 312, 6, "setProcessingProgress"], [297, 29, 312, 27], [297, 30, 312, 28], [297, 32, 312, 30], [297, 33, 312, 31], [299, 8, 314, 6], [300, 8, 315, 6], [300, 14, 315, 12, "blurredImageBlob"], [300, 30, 315, 28], [300, 33, 315, 31], [300, 39, 315, 37], [300, 43, 315, 41, "Promise"], [300, 50, 315, 48], [300, 51, 315, 56, "resolve"], [300, 58, 315, 63], [300, 62, 315, 68], [301, 10, 316, 8, "canvas"], [301, 16, 316, 14], [301, 17, 316, 15, "toBlob"], [301, 23, 316, 21], [301, 24, 316, 23, "blob"], [301, 28, 316, 27], [301, 32, 316, 32, "resolve"], [301, 39, 316, 39], [301, 40, 316, 40, "blob"], [301, 44, 316, 45], [301, 45, 316, 46], [301, 47, 316, 48], [301, 59, 316, 60], [301, 61, 316, 62], [301, 64, 316, 65], [301, 65, 316, 66], [302, 8, 317, 6], [302, 9, 317, 7], [302, 10, 317, 8], [303, 8, 319, 6], [303, 14, 319, 12, "blurredImageUrl"], [303, 29, 319, 27], [303, 32, 319, 30, "URL"], [303, 35, 319, 33], [303, 36, 319, 34, "createObjectURL"], [303, 51, 319, 49], [303, 52, 319, 50, "blurredImageBlob"], [303, 68, 319, 66], [303, 69, 319, 67], [304, 8, 321, 6, "setProcessingProgress"], [304, 29, 321, 27], [304, 30, 321, 28], [304, 33, 321, 31], [304, 34, 321, 32], [306, 8, 323, 6], [307, 8, 324, 6], [307, 14, 324, 12, "completeProcessing"], [307, 32, 324, 30], [307, 33, 324, 31, "blurredImageUrl"], [307, 48, 324, 46], [307, 49, 324, 47], [308, 6, 326, 4], [308, 7, 326, 5], [308, 8, 326, 6], [308, 15, 326, 13, "error"], [308, 20, 326, 18], [308, 22, 326, 20], [309, 8, 327, 6, "console"], [309, 15, 327, 13], [309, 16, 327, 14, "error"], [309, 21, 327, 19], [309, 22, 327, 20], [309, 57, 327, 55], [309, 59, 327, 57, "error"], [309, 64, 327, 62], [309, 65, 327, 63], [310, 8, 328, 6, "setErrorMessage"], [310, 23, 328, 21], [310, 24, 328, 22], [310, 50, 328, 48], [310, 51, 328, 49], [311, 8, 329, 6, "setProcessingState"], [311, 26, 329, 24], [311, 27, 329, 25], [311, 34, 329, 32], [311, 35, 329, 33], [312, 6, 330, 4], [313, 4, 331, 2], [313, 5, 331, 3], [315, 4, 333, 2], [316, 4, 334, 2], [316, 10, 334, 8, "completeProcessing"], [316, 28, 334, 26], [316, 31, 334, 29], [316, 37, 334, 36, "blurredImageUrl"], [316, 52, 334, 59], [316, 56, 334, 64], [317, 6, 335, 4], [317, 10, 335, 8], [318, 8, 336, 6, "setProcessingState"], [318, 26, 336, 24], [318, 27, 336, 25], [318, 37, 336, 35], [318, 38, 336, 36], [320, 8, 338, 6], [321, 8, 339, 6], [321, 14, 339, 12, "timestamp"], [321, 23, 339, 21], [321, 26, 339, 24, "Date"], [321, 30, 339, 28], [321, 31, 339, 29, "now"], [321, 34, 339, 32], [321, 35, 339, 33], [321, 36, 339, 34], [322, 8, 340, 6], [322, 14, 340, 12, "result"], [322, 20, 340, 18], [322, 23, 340, 21], [323, 10, 341, 8, "imageUrl"], [323, 18, 341, 16], [323, 20, 341, 18, "blurredImageUrl"], [323, 35, 341, 33], [324, 10, 342, 8, "localUri"], [324, 18, 342, 16], [324, 20, 342, 18, "blurredImageUrl"], [324, 35, 342, 33], [325, 10, 343, 8, "challengeCode"], [325, 23, 343, 21], [325, 25, 343, 23, "challengeCode"], [325, 38, 343, 36], [325, 42, 343, 40], [325, 44, 343, 42], [326, 10, 344, 8, "timestamp"], [326, 19, 344, 17], [327, 10, 345, 8, "jobId"], [327, 15, 345, 13], [327, 17, 345, 15], [327, 27, 345, 25, "timestamp"], [327, 36, 345, 34], [327, 38, 345, 36], [328, 10, 346, 8, "status"], [328, 16, 346, 14], [328, 18, 346, 16], [329, 8, 347, 6], [329, 9, 347, 7], [330, 8, 349, 6, "console"], [330, 15, 349, 13], [330, 16, 349, 14, "log"], [330, 19, 349, 17], [330, 20, 349, 18], [330, 58, 349, 56], [330, 60, 349, 58, "result"], [330, 66, 349, 64], [330, 67, 349, 65], [332, 8, 351, 6], [333, 8, 352, 6, "onComplete"], [333, 18, 352, 16], [333, 19, 352, 17, "result"], [333, 25, 352, 23], [333, 26, 352, 24], [334, 6, 354, 4], [334, 7, 354, 5], [334, 8, 354, 6], [334, 15, 354, 13, "error"], [334, 20, 354, 18], [334, 22, 354, 20], [335, 8, 355, 6, "console"], [335, 15, 355, 13], [335, 16, 355, 14, "error"], [335, 21, 355, 19], [335, 22, 355, 20], [335, 57, 355, 55], [335, 59, 355, 57, "error"], [335, 64, 355, 62], [335, 65, 355, 63], [336, 8, 356, 6, "setErrorMessage"], [336, 23, 356, 21], [336, 24, 356, 22], [336, 56, 356, 54], [336, 57, 356, 55], [337, 8, 357, 6, "setProcessingState"], [337, 26, 357, 24], [337, 27, 357, 25], [337, 34, 357, 32], [337, 35, 357, 33], [338, 6, 358, 4], [339, 4, 359, 2], [339, 5, 359, 3], [341, 4, 361, 2], [342, 4, 362, 2], [342, 10, 362, 8, "triggerServerProcessing"], [342, 33, 362, 31], [342, 36, 362, 34], [342, 42, 362, 34, "triggerServerProcessing"], [342, 43, 362, 41, "privateImageUrl"], [342, 58, 362, 64], [342, 60, 362, 66, "timestamp"], [342, 69, 362, 83], [342, 74, 362, 88], [343, 6, 363, 4], [343, 10, 363, 8], [344, 8, 364, 6, "console"], [344, 15, 364, 13], [344, 16, 364, 14, "log"], [344, 19, 364, 17], [344, 20, 364, 18], [344, 74, 364, 72], [344, 76, 364, 74, "privateImageUrl"], [344, 91, 364, 89], [344, 92, 364, 90], [345, 8, 365, 6, "setProcessingState"], [345, 26, 365, 24], [345, 27, 365, 25], [345, 39, 365, 37], [345, 40, 365, 38], [346, 8, 366, 6, "setProcessingProgress"], [346, 29, 366, 27], [346, 30, 366, 28], [346, 32, 366, 30], [346, 33, 366, 31], [347, 8, 368, 6], [347, 14, 368, 12, "requestBody"], [347, 25, 368, 23], [347, 28, 368, 26], [348, 10, 369, 8, "imageUrl"], [348, 18, 369, 16], [348, 20, 369, 18, "privateImageUrl"], [348, 35, 369, 33], [349, 10, 370, 8, "userId"], [349, 16, 370, 14], [350, 10, 371, 8, "requestId"], [350, 19, 371, 17], [351, 10, 372, 8, "timestamp"], [351, 19, 372, 17], [352, 10, 373, 8, "platform"], [352, 18, 373, 16], [352, 20, 373, 18], [353, 8, 374, 6], [353, 9, 374, 7], [354, 8, 376, 6, "console"], [354, 15, 376, 13], [354, 16, 376, 14, "log"], [354, 19, 376, 17], [354, 20, 376, 18], [354, 65, 376, 63], [354, 67, 376, 65, "requestBody"], [354, 78, 376, 76], [354, 79, 376, 77], [356, 8, 378, 6], [357, 8, 379, 6], [357, 14, 379, 12, "response"], [357, 22, 379, 20], [357, 25, 379, 23], [357, 31, 379, 29, "fetch"], [357, 36, 379, 34], [357, 37, 379, 35], [357, 40, 379, 38, "API_BASE_URL"], [357, 52, 379, 50], [357, 72, 379, 70], [357, 74, 379, 72], [358, 10, 380, 8, "method"], [358, 16, 380, 14], [358, 18, 380, 16], [358, 24, 380, 22], [359, 10, 381, 8, "headers"], [359, 17, 381, 15], [359, 19, 381, 17], [360, 12, 382, 10], [360, 26, 382, 24], [360, 28, 382, 26], [360, 46, 382, 44], [361, 12, 383, 10], [361, 27, 383, 25], [361, 29, 383, 27], [361, 39, 383, 37], [361, 45, 383, 43, "getAuthToken"], [361, 57, 383, 55], [361, 58, 383, 56], [361, 59, 383, 57], [362, 10, 384, 8], [362, 11, 384, 9], [363, 10, 385, 8, "body"], [363, 14, 385, 12], [363, 16, 385, 14, "JSON"], [363, 20, 385, 18], [363, 21, 385, 19, "stringify"], [363, 30, 385, 28], [363, 31, 385, 29, "requestBody"], [363, 42, 385, 40], [364, 8, 386, 6], [364, 9, 386, 7], [364, 10, 386, 8], [365, 8, 388, 6], [365, 12, 388, 10], [365, 13, 388, 11, "response"], [365, 21, 388, 19], [365, 22, 388, 20, "ok"], [365, 24, 388, 22], [365, 26, 388, 24], [366, 10, 389, 8], [366, 16, 389, 14, "errorText"], [366, 25, 389, 23], [366, 28, 389, 26], [366, 34, 389, 32, "response"], [366, 42, 389, 40], [366, 43, 389, 41, "text"], [366, 47, 389, 45], [366, 48, 389, 46], [366, 49, 389, 47], [367, 10, 390, 8, "console"], [367, 17, 390, 15], [367, 18, 390, 16, "error"], [367, 23, 390, 21], [367, 24, 390, 22], [367, 68, 390, 66], [367, 70, 390, 68, "response"], [367, 78, 390, 76], [367, 79, 390, 77, "status"], [367, 85, 390, 83], [367, 87, 390, 85, "errorText"], [367, 96, 390, 94], [367, 97, 390, 95], [368, 10, 391, 8], [368, 16, 391, 14], [368, 20, 391, 18, "Error"], [368, 25, 391, 23], [368, 26, 391, 24], [368, 48, 391, 46, "response"], [368, 56, 391, 54], [368, 57, 391, 55, "status"], [368, 63, 391, 61], [368, 67, 391, 65, "response"], [368, 75, 391, 73], [368, 76, 391, 74, "statusText"], [368, 86, 391, 84], [368, 88, 391, 86], [368, 89, 391, 87], [369, 8, 392, 6], [370, 8, 394, 6], [370, 14, 394, 12, "result"], [370, 20, 394, 18], [370, 23, 394, 21], [370, 29, 394, 27, "response"], [370, 37, 394, 35], [370, 38, 394, 36, "json"], [370, 42, 394, 40], [370, 43, 394, 41], [370, 44, 394, 42], [371, 8, 395, 6, "console"], [371, 15, 395, 13], [371, 16, 395, 14, "log"], [371, 19, 395, 17], [371, 20, 395, 18], [371, 68, 395, 66], [371, 70, 395, 68, "result"], [371, 76, 395, 74], [371, 77, 395, 75], [372, 8, 397, 6], [372, 12, 397, 10], [372, 13, 397, 11, "result"], [372, 19, 397, 17], [372, 20, 397, 18, "jobId"], [372, 25, 397, 23], [372, 27, 397, 25], [373, 10, 398, 8], [373, 16, 398, 14], [373, 20, 398, 18, "Error"], [373, 25, 398, 23], [373, 26, 398, 24], [373, 70, 398, 68], [373, 71, 398, 69], [374, 8, 399, 6], [376, 8, 401, 6], [377, 8, 402, 6], [377, 14, 402, 12, "pollForCompletion"], [377, 31, 402, 29], [377, 32, 402, 30, "result"], [377, 38, 402, 36], [377, 39, 402, 37, "jobId"], [377, 44, 402, 42], [377, 46, 402, 44, "timestamp"], [377, 55, 402, 53], [377, 56, 402, 54], [378, 6, 403, 4], [378, 7, 403, 5], [378, 8, 403, 6], [378, 15, 403, 13, "error"], [378, 20, 403, 18], [378, 22, 403, 20], [379, 8, 404, 6, "console"], [379, 15, 404, 13], [379, 16, 404, 14, "error"], [379, 21, 404, 19], [379, 22, 404, 20], [379, 57, 404, 55], [379, 59, 404, 57, "error"], [379, 64, 404, 62], [379, 65, 404, 63], [380, 8, 405, 6, "setErrorMessage"], [380, 23, 405, 21], [380, 24, 405, 22], [380, 52, 405, 50, "error"], [380, 57, 405, 55], [380, 58, 405, 56, "message"], [380, 65, 405, 63], [380, 67, 405, 65], [380, 68, 405, 66], [381, 8, 406, 6, "setProcessingState"], [381, 26, 406, 24], [381, 27, 406, 25], [381, 34, 406, 32], [381, 35, 406, 33], [382, 6, 407, 4], [383, 4, 408, 2], [383, 5, 408, 3], [384, 4, 409, 2], [385, 4, 410, 2], [385, 10, 410, 8, "pollForCompletion"], [385, 27, 410, 25], [385, 30, 410, 28], [385, 36, 410, 28, "pollForCompletion"], [385, 37, 410, 35, "jobId"], [385, 42, 410, 48], [385, 44, 410, 50, "timestamp"], [385, 53, 410, 67], [385, 55, 410, 69, "attempts"], [385, 63, 410, 77], [385, 66, 410, 80], [385, 67, 410, 81], [385, 72, 410, 86], [386, 6, 411, 4], [386, 12, 411, 10, "MAX_ATTEMPTS"], [386, 24, 411, 22], [386, 27, 411, 25], [386, 29, 411, 27], [386, 30, 411, 28], [386, 31, 411, 29], [387, 6, 412, 4], [387, 12, 412, 10, "POLL_INTERVAL"], [387, 25, 412, 23], [387, 28, 412, 26], [387, 32, 412, 30], [387, 33, 412, 31], [387, 34, 412, 32], [389, 6, 414, 4, "console"], [389, 13, 414, 11], [389, 14, 414, 12, "log"], [389, 17, 414, 15], [389, 18, 414, 16], [389, 53, 414, 51, "attempts"], [389, 61, 414, 59], [389, 64, 414, 62], [389, 65, 414, 63], [389, 69, 414, 67, "MAX_ATTEMPTS"], [389, 81, 414, 79], [389, 93, 414, 91, "jobId"], [389, 98, 414, 96], [389, 100, 414, 98], [389, 101, 414, 99], [390, 6, 416, 4], [390, 10, 416, 8, "attempts"], [390, 18, 416, 16], [390, 22, 416, 20, "MAX_ATTEMPTS"], [390, 34, 416, 32], [390, 36, 416, 34], [391, 8, 417, 6, "console"], [391, 15, 417, 13], [391, 16, 417, 14, "error"], [391, 21, 417, 19], [391, 22, 417, 20], [391, 75, 417, 73], [391, 76, 417, 74], [392, 8, 418, 6, "setErrorMessage"], [392, 23, 418, 21], [392, 24, 418, 22], [392, 63, 418, 61], [392, 64, 418, 62], [393, 8, 419, 6, "setProcessingState"], [393, 26, 419, 24], [393, 27, 419, 25], [393, 34, 419, 32], [393, 35, 419, 33], [394, 8, 420, 6], [395, 6, 421, 4], [396, 6, 423, 4], [396, 10, 423, 8], [397, 8, 424, 6], [397, 14, 424, 12, "response"], [397, 22, 424, 20], [397, 25, 424, 23], [397, 31, 424, 29, "fetch"], [397, 36, 424, 34], [397, 37, 424, 35], [397, 40, 424, 38, "API_BASE_URL"], [397, 52, 424, 50], [397, 75, 424, 73, "jobId"], [397, 80, 424, 78], [397, 82, 424, 80], [397, 84, 424, 82], [398, 10, 425, 8, "headers"], [398, 17, 425, 15], [398, 19, 425, 17], [399, 12, 426, 10], [399, 27, 426, 25], [399, 29, 426, 27], [399, 39, 426, 37], [399, 45, 426, 43, "getAuthToken"], [399, 57, 426, 55], [399, 58, 426, 56], [399, 59, 426, 57], [400, 10, 427, 8], [401, 8, 428, 6], [401, 9, 428, 7], [401, 10, 428, 8], [402, 8, 430, 6], [402, 12, 430, 10], [402, 13, 430, 11, "response"], [402, 21, 430, 19], [402, 22, 430, 20, "ok"], [402, 24, 430, 22], [402, 26, 430, 24], [403, 10, 431, 8], [403, 16, 431, 14], [403, 20, 431, 18, "Error"], [403, 25, 431, 23], [403, 26, 431, 24], [403, 34, 431, 32, "response"], [403, 42, 431, 40], [403, 43, 431, 41, "status"], [403, 49, 431, 47], [403, 54, 431, 52, "response"], [403, 62, 431, 60], [403, 63, 431, 61, "statusText"], [403, 73, 431, 71], [403, 75, 431, 73], [403, 76, 431, 74], [404, 8, 432, 6], [405, 8, 434, 6], [405, 14, 434, 12, "status"], [405, 20, 434, 18], [405, 23, 434, 21], [405, 29, 434, 27, "response"], [405, 37, 434, 35], [405, 38, 434, 36, "json"], [405, 42, 434, 40], [405, 43, 434, 41], [405, 44, 434, 42], [406, 8, 435, 6, "console"], [406, 15, 435, 13], [406, 16, 435, 14, "log"], [406, 19, 435, 17], [406, 20, 435, 18], [406, 54, 435, 52], [406, 56, 435, 54, "status"], [406, 62, 435, 60], [406, 63, 435, 61], [407, 8, 437, 6], [407, 12, 437, 10, "status"], [407, 18, 437, 16], [407, 19, 437, 17, "status"], [407, 25, 437, 23], [407, 30, 437, 28], [407, 41, 437, 39], [407, 43, 437, 41], [408, 10, 438, 8, "console"], [408, 17, 438, 15], [408, 18, 438, 16, "log"], [408, 21, 438, 19], [408, 22, 438, 20], [408, 73, 438, 71], [408, 74, 438, 72], [409, 10, 439, 8, "setProcessingProgress"], [409, 31, 439, 29], [409, 32, 439, 30], [409, 35, 439, 33], [409, 36, 439, 34], [410, 10, 440, 8, "setProcessingState"], [410, 28, 440, 26], [410, 29, 440, 27], [410, 40, 440, 38], [410, 41, 440, 39], [411, 10, 441, 8], [412, 10, 442, 8], [412, 16, 442, 14, "result"], [412, 22, 442, 20], [412, 25, 442, 23], [413, 12, 443, 10, "imageUrl"], [413, 20, 443, 18], [413, 22, 443, 20, "status"], [413, 28, 443, 26], [413, 29, 443, 27, "publicUrl"], [413, 38, 443, 36], [414, 12, 443, 38], [415, 12, 444, 10, "localUri"], [415, 20, 444, 18], [415, 22, 444, 20, "capturedPhoto"], [415, 35, 444, 33], [415, 39, 444, 37, "status"], [415, 45, 444, 43], [415, 46, 444, 44, "publicUrl"], [415, 55, 444, 53], [416, 12, 444, 55], [417, 12, 445, 10, "challengeCode"], [417, 25, 445, 23], [417, 27, 445, 25, "challengeCode"], [417, 40, 445, 38], [417, 44, 445, 42], [417, 46, 445, 44], [418, 12, 446, 10, "timestamp"], [418, 21, 446, 19], [419, 12, 447, 10, "processingStatus"], [419, 28, 447, 26], [419, 30, 447, 28], [420, 10, 448, 8], [420, 11, 448, 9], [421, 10, 449, 8, "console"], [421, 17, 449, 15], [421, 18, 449, 16, "log"], [421, 21, 449, 19], [421, 22, 449, 20], [421, 57, 449, 55], [421, 59, 449, 57, "result"], [421, 65, 449, 63], [421, 66, 449, 64], [422, 10, 450, 8, "onComplete"], [422, 20, 450, 18], [422, 21, 450, 19, "result"], [422, 27, 450, 25], [422, 28, 450, 26], [423, 10, 451, 8], [424, 8, 452, 6], [424, 9, 452, 7], [424, 15, 452, 13], [424, 19, 452, 17, "status"], [424, 25, 452, 23], [424, 26, 452, 24, "status"], [424, 32, 452, 30], [424, 37, 452, 35], [424, 45, 452, 43], [424, 47, 452, 45], [425, 10, 453, 8, "console"], [425, 17, 453, 15], [425, 18, 453, 16, "error"], [425, 23, 453, 21], [425, 24, 453, 22], [425, 60, 453, 58], [425, 62, 453, 60, "status"], [425, 68, 453, 66], [425, 69, 453, 67, "error"], [425, 74, 453, 72], [425, 75, 453, 73], [426, 10, 454, 8], [426, 16, 454, 14], [426, 20, 454, 18, "Error"], [426, 25, 454, 23], [426, 26, 454, 24, "status"], [426, 32, 454, 30], [426, 33, 454, 31, "error"], [426, 38, 454, 36], [426, 42, 454, 40], [426, 61, 454, 59], [426, 62, 454, 60], [427, 8, 455, 6], [427, 9, 455, 7], [427, 15, 455, 13], [428, 10, 456, 8], [429, 10, 457, 8], [429, 16, 457, 14, "progressValue"], [429, 29, 457, 27], [429, 32, 457, 30], [429, 34, 457, 32], [429, 37, 457, 36, "attempts"], [429, 45, 457, 44], [429, 48, 457, 47, "MAX_ATTEMPTS"], [429, 60, 457, 59], [429, 63, 457, 63], [429, 65, 457, 65], [430, 10, 458, 8, "console"], [430, 17, 458, 15], [430, 18, 458, 16, "log"], [430, 21, 458, 19], [430, 22, 458, 20], [430, 71, 458, 69, "progressValue"], [430, 84, 458, 82], [430, 87, 458, 85], [430, 88, 458, 86], [431, 10, 459, 8, "setProcessingProgress"], [431, 31, 459, 29], [431, 32, 459, 30, "progressValue"], [431, 45, 459, 43], [431, 46, 459, 44], [432, 10, 461, 8, "setTimeout"], [432, 20, 461, 18], [432, 21, 461, 19], [432, 27, 461, 25], [433, 12, 462, 10, "pollForCompletion"], [433, 29, 462, 27], [433, 30, 462, 28, "jobId"], [433, 35, 462, 33], [433, 37, 462, 35, "timestamp"], [433, 46, 462, 44], [433, 48, 462, 46, "attempts"], [433, 56, 462, 54], [433, 59, 462, 57], [433, 60, 462, 58], [433, 61, 462, 59], [434, 10, 463, 8], [434, 11, 463, 9], [434, 13, 463, 11, "POLL_INTERVAL"], [434, 26, 463, 24], [434, 27, 463, 25], [435, 8, 464, 6], [436, 6, 465, 4], [436, 7, 465, 5], [436, 8, 465, 6], [436, 15, 465, 13, "error"], [436, 20, 465, 18], [436, 22, 465, 20], [437, 8, 466, 6, "console"], [437, 15, 466, 13], [437, 16, 466, 14, "error"], [437, 21, 466, 19], [437, 22, 466, 20], [437, 54, 466, 52], [437, 56, 466, 54, "error"], [437, 61, 466, 59], [437, 62, 466, 60], [438, 8, 467, 6, "setErrorMessage"], [438, 23, 467, 21], [438, 24, 467, 22], [438, 62, 467, 60, "error"], [438, 67, 467, 65], [438, 68, 467, 66, "message"], [438, 75, 467, 73], [438, 77, 467, 75], [438, 78, 467, 76], [439, 8, 468, 6, "setProcessingState"], [439, 26, 468, 24], [439, 27, 468, 25], [439, 34, 468, 32], [439, 35, 468, 33], [440, 6, 469, 4], [441, 4, 470, 2], [441, 5, 470, 3], [442, 4, 471, 2], [443, 4, 472, 2], [443, 10, 472, 8, "getAuthToken"], [443, 22, 472, 20], [443, 25, 472, 23], [443, 31, 472, 23, "getAuthToken"], [443, 32, 472, 23], [443, 37, 472, 52], [444, 6, 473, 4], [445, 6, 474, 4], [446, 6, 475, 4], [446, 13, 475, 11], [446, 30, 475, 28], [447, 4, 476, 2], [447, 5, 476, 3], [449, 4, 478, 2], [450, 4, 479, 2], [450, 10, 479, 8, "retryCapture"], [450, 22, 479, 20], [450, 25, 479, 23], [450, 29, 479, 23, "useCallback"], [450, 47, 479, 34], [450, 49, 479, 35], [450, 55, 479, 41], [451, 6, 480, 4, "console"], [451, 13, 480, 11], [451, 14, 480, 12, "log"], [451, 17, 480, 15], [451, 18, 480, 16], [451, 55, 480, 53], [451, 56, 480, 54], [452, 6, 481, 4, "setProcessingState"], [452, 24, 481, 22], [452, 25, 481, 23], [452, 31, 481, 29], [452, 32, 481, 30], [453, 6, 482, 4, "setErrorMessage"], [453, 21, 482, 19], [453, 22, 482, 20], [453, 24, 482, 22], [453, 25, 482, 23], [454, 6, 483, 4, "setCapturedPhoto"], [454, 22, 483, 20], [454, 23, 483, 21], [454, 25, 483, 23], [454, 26, 483, 24], [455, 6, 484, 4, "setProcessingProgress"], [455, 27, 484, 25], [455, 28, 484, 26], [455, 29, 484, 27], [455, 30, 484, 28], [456, 4, 485, 2], [456, 5, 485, 3], [456, 7, 485, 5], [456, 9, 485, 7], [456, 10, 485, 8], [457, 4, 486, 2], [458, 4, 487, 2], [458, 8, 487, 2, "useEffect"], [458, 24, 487, 11], [458, 26, 487, 12], [458, 32, 487, 18], [459, 6, 488, 4, "console"], [459, 13, 488, 11], [459, 14, 488, 12, "log"], [459, 17, 488, 15], [459, 18, 488, 16], [459, 53, 488, 51], [459, 55, 488, 53, "permission"], [459, 65, 488, 63], [459, 66, 488, 64], [460, 6, 489, 4], [460, 10, 489, 8, "permission"], [460, 20, 489, 18], [460, 22, 489, 20], [461, 8, 490, 6, "console"], [461, 15, 490, 13], [461, 16, 490, 14, "log"], [461, 19, 490, 17], [461, 20, 490, 18], [461, 57, 490, 55], [461, 59, 490, 57, "permission"], [461, 69, 490, 67], [461, 70, 490, 68, "granted"], [461, 77, 490, 75], [461, 78, 490, 76], [462, 6, 491, 4], [463, 4, 492, 2], [463, 5, 492, 3], [463, 7, 492, 5], [463, 8, 492, 6, "permission"], [463, 18, 492, 16], [463, 19, 492, 17], [463, 20, 492, 18], [464, 4, 493, 2], [465, 4, 494, 2], [465, 8, 494, 6], [465, 9, 494, 7, "permission"], [465, 19, 494, 17], [465, 21, 494, 19], [466, 6, 495, 4, "console"], [466, 13, 495, 11], [466, 14, 495, 12, "log"], [466, 17, 495, 15], [466, 18, 495, 16], [466, 67, 495, 65], [466, 68, 495, 66], [467, 6, 496, 4], [467, 26, 497, 6], [467, 30, 497, 6, "_jsxDevRuntime"], [467, 44, 497, 6], [467, 45, 497, 6, "jsxDEV"], [467, 51, 497, 6], [467, 53, 497, 7, "_View"], [467, 58, 497, 7], [467, 59, 497, 7, "default"], [467, 66, 497, 11], [468, 8, 497, 12, "style"], [468, 13, 497, 17], [468, 15, 497, 19, "styles"], [468, 21, 497, 25], [468, 22, 497, 26, "container"], [468, 31, 497, 36], [469, 8, 497, 36, "children"], [469, 16, 497, 36], [469, 32, 498, 8], [469, 36, 498, 8, "_jsxDevRuntime"], [469, 50, 498, 8], [469, 51, 498, 8, "jsxDEV"], [469, 57, 498, 8], [469, 59, 498, 9, "_ActivityIndicator"], [469, 77, 498, 9], [469, 78, 498, 9, "default"], [469, 85, 498, 26], [470, 10, 498, 27, "size"], [470, 14, 498, 31], [470, 16, 498, 32], [470, 23, 498, 39], [471, 10, 498, 40, "color"], [471, 15, 498, 45], [471, 17, 498, 46], [472, 8, 498, 55], [473, 10, 498, 55, "fileName"], [473, 18, 498, 55], [473, 20, 498, 55, "_jsxFileName"], [473, 32, 498, 55], [474, 10, 498, 55, "lineNumber"], [474, 20, 498, 55], [475, 10, 498, 55, "columnNumber"], [475, 22, 498, 55], [476, 8, 498, 55], [476, 15, 498, 57], [476, 16, 498, 58], [476, 31, 499, 8], [476, 35, 499, 8, "_jsxDevRuntime"], [476, 49, 499, 8], [476, 50, 499, 8, "jsxDEV"], [476, 56, 499, 8], [476, 58, 499, 9, "_Text"], [476, 63, 499, 9], [476, 64, 499, 9, "default"], [476, 71, 499, 13], [477, 10, 499, 14, "style"], [477, 15, 499, 19], [477, 17, 499, 21, "styles"], [477, 23, 499, 27], [477, 24, 499, 28, "loadingText"], [477, 35, 499, 40], [478, 10, 499, 40, "children"], [478, 18, 499, 40], [478, 20, 499, 41], [479, 8, 499, 58], [480, 10, 499, 58, "fileName"], [480, 18, 499, 58], [480, 20, 499, 58, "_jsxFileName"], [480, 32, 499, 58], [481, 10, 499, 58, "lineNumber"], [481, 20, 499, 58], [482, 10, 499, 58, "columnNumber"], [482, 22, 499, 58], [483, 8, 499, 58], [483, 15, 499, 64], [483, 16, 499, 65], [484, 6, 499, 65], [485, 8, 499, 65, "fileName"], [485, 16, 499, 65], [485, 18, 499, 65, "_jsxFileName"], [485, 30, 499, 65], [486, 8, 499, 65, "lineNumber"], [486, 18, 499, 65], [487, 8, 499, 65, "columnNumber"], [487, 20, 499, 65], [488, 6, 499, 65], [488, 13, 500, 12], [488, 14, 500, 13], [489, 4, 502, 2], [490, 4, 503, 2], [490, 8, 503, 6], [490, 9, 503, 7, "permission"], [490, 19, 503, 17], [490, 20, 503, 18, "granted"], [490, 27, 503, 25], [490, 29, 503, 27], [491, 6, 504, 4, "console"], [491, 13, 504, 11], [491, 14, 504, 12, "log"], [491, 17, 504, 15], [491, 18, 504, 16], [491, 93, 504, 91], [491, 94, 504, 92], [492, 6, 505, 4], [492, 26, 506, 6], [492, 30, 506, 6, "_jsxDevRuntime"], [492, 44, 506, 6], [492, 45, 506, 6, "jsxDEV"], [492, 51, 506, 6], [492, 53, 506, 7, "_View"], [492, 58, 506, 7], [492, 59, 506, 7, "default"], [492, 66, 506, 11], [493, 8, 506, 12, "style"], [493, 13, 506, 17], [493, 15, 506, 19, "styles"], [493, 21, 506, 25], [493, 22, 506, 26, "container"], [493, 31, 506, 36], [494, 8, 506, 36, "children"], [494, 16, 506, 36], [494, 31, 507, 8], [494, 35, 507, 8, "_jsxDevRuntime"], [494, 49, 507, 8], [494, 50, 507, 8, "jsxDEV"], [494, 56, 507, 8], [494, 58, 507, 9, "_View"], [494, 63, 507, 9], [494, 64, 507, 9, "default"], [494, 71, 507, 13], [495, 10, 507, 14, "style"], [495, 15, 507, 19], [495, 17, 507, 21, "styles"], [495, 23, 507, 27], [495, 24, 507, 28, "permissionContent"], [495, 41, 507, 46], [496, 10, 507, 46, "children"], [496, 18, 507, 46], [496, 34, 508, 10], [496, 38, 508, 10, "_jsxDevRuntime"], [496, 52, 508, 10], [496, 53, 508, 10, "jsxDEV"], [496, 59, 508, 10], [496, 61, 508, 11, "_lucideReactNative"], [496, 79, 508, 11], [496, 80, 508, 11, "Camera"], [496, 86, 508, 21], [497, 12, 508, 22, "size"], [497, 16, 508, 26], [497, 18, 508, 28], [497, 20, 508, 31], [498, 12, 508, 32, "color"], [498, 17, 508, 37], [498, 19, 508, 38], [499, 10, 508, 47], [500, 12, 508, 47, "fileName"], [500, 20, 508, 47], [500, 22, 508, 47, "_jsxFileName"], [500, 34, 508, 47], [501, 12, 508, 47, "lineNumber"], [501, 22, 508, 47], [502, 12, 508, 47, "columnNumber"], [502, 24, 508, 47], [503, 10, 508, 47], [503, 17, 508, 49], [503, 18, 508, 50], [503, 33, 509, 10], [503, 37, 509, 10, "_jsxDevRuntime"], [503, 51, 509, 10], [503, 52, 509, 10, "jsxDEV"], [503, 58, 509, 10], [503, 60, 509, 11, "_Text"], [503, 65, 509, 11], [503, 66, 509, 11, "default"], [503, 73, 509, 15], [504, 12, 509, 16, "style"], [504, 17, 509, 21], [504, 19, 509, 23, "styles"], [504, 25, 509, 29], [504, 26, 509, 30, "permissionTitle"], [504, 41, 509, 46], [505, 12, 509, 46, "children"], [505, 20, 509, 46], [505, 22, 509, 47], [506, 10, 509, 73], [507, 12, 509, 73, "fileName"], [507, 20, 509, 73], [507, 22, 509, 73, "_jsxFileName"], [507, 34, 509, 73], [508, 12, 509, 73, "lineNumber"], [508, 22, 509, 73], [509, 12, 509, 73, "columnNumber"], [509, 24, 509, 73], [510, 10, 509, 73], [510, 17, 509, 79], [510, 18, 509, 80], [510, 33, 510, 10], [510, 37, 510, 10, "_jsxDevRuntime"], [510, 51, 510, 10], [510, 52, 510, 10, "jsxDEV"], [510, 58, 510, 10], [510, 60, 510, 11, "_Text"], [510, 65, 510, 11], [510, 66, 510, 11, "default"], [510, 73, 510, 15], [511, 12, 510, 16, "style"], [511, 17, 510, 21], [511, 19, 510, 23, "styles"], [511, 25, 510, 29], [511, 26, 510, 30, "permissionDescription"], [511, 47, 510, 52], [512, 12, 510, 52, "children"], [512, 20, 510, 52], [512, 22, 510, 53], [513, 10, 513, 10], [514, 12, 513, 10, "fileName"], [514, 20, 513, 10], [514, 22, 513, 10, "_jsxFileName"], [514, 34, 513, 10], [515, 12, 513, 10, "lineNumber"], [515, 22, 513, 10], [516, 12, 513, 10, "columnNumber"], [516, 24, 513, 10], [517, 10, 513, 10], [517, 17, 513, 16], [517, 18, 513, 17], [517, 33, 514, 10], [517, 37, 514, 10, "_jsxDevRuntime"], [517, 51, 514, 10], [517, 52, 514, 10, "jsxDEV"], [517, 58, 514, 10], [517, 60, 514, 11, "_TouchableOpacity"], [517, 77, 514, 11], [517, 78, 514, 11, "default"], [517, 85, 514, 27], [518, 12, 514, 28, "onPress"], [518, 19, 514, 35], [518, 21, 514, 37, "requestPermission"], [518, 38, 514, 55], [519, 12, 514, 56, "style"], [519, 17, 514, 61], [519, 19, 514, 63, "styles"], [519, 25, 514, 69], [519, 26, 514, 70, "primaryButton"], [519, 39, 514, 84], [520, 12, 514, 84, "children"], [520, 20, 514, 84], [520, 35, 515, 12], [520, 39, 515, 12, "_jsxDevRuntime"], [520, 53, 515, 12], [520, 54, 515, 12, "jsxDEV"], [520, 60, 515, 12], [520, 62, 515, 13, "_Text"], [520, 67, 515, 13], [520, 68, 515, 13, "default"], [520, 75, 515, 17], [521, 14, 515, 18, "style"], [521, 19, 515, 23], [521, 21, 515, 25, "styles"], [521, 27, 515, 31], [521, 28, 515, 32, "primaryButtonText"], [521, 45, 515, 50], [522, 14, 515, 50, "children"], [522, 22, 515, 50], [522, 24, 515, 51], [523, 12, 515, 67], [524, 14, 515, 67, "fileName"], [524, 22, 515, 67], [524, 24, 515, 67, "_jsxFileName"], [524, 36, 515, 67], [525, 14, 515, 67, "lineNumber"], [525, 24, 515, 67], [526, 14, 515, 67, "columnNumber"], [526, 26, 515, 67], [527, 12, 515, 67], [527, 19, 515, 73], [528, 10, 515, 74], [529, 12, 515, 74, "fileName"], [529, 20, 515, 74], [529, 22, 515, 74, "_jsxFileName"], [529, 34, 515, 74], [530, 12, 515, 74, "lineNumber"], [530, 22, 515, 74], [531, 12, 515, 74, "columnNumber"], [531, 24, 515, 74], [532, 10, 515, 74], [532, 17, 516, 28], [532, 18, 516, 29], [532, 33, 517, 10], [532, 37, 517, 10, "_jsxDevRuntime"], [532, 51, 517, 10], [532, 52, 517, 10, "jsxDEV"], [532, 58, 517, 10], [532, 60, 517, 11, "_TouchableOpacity"], [532, 77, 517, 11], [532, 78, 517, 11, "default"], [532, 85, 517, 27], [533, 12, 517, 28, "onPress"], [533, 19, 517, 35], [533, 21, 517, 37, "onCancel"], [533, 29, 517, 46], [534, 12, 517, 47, "style"], [534, 17, 517, 52], [534, 19, 517, 54, "styles"], [534, 25, 517, 60], [534, 26, 517, 61, "secondaryButton"], [534, 41, 517, 77], [535, 12, 517, 77, "children"], [535, 20, 517, 77], [535, 35, 518, 12], [535, 39, 518, 12, "_jsxDevRuntime"], [535, 53, 518, 12], [535, 54, 518, 12, "jsxDEV"], [535, 60, 518, 12], [535, 62, 518, 13, "_Text"], [535, 67, 518, 13], [535, 68, 518, 13, "default"], [535, 75, 518, 17], [536, 14, 518, 18, "style"], [536, 19, 518, 23], [536, 21, 518, 25, "styles"], [536, 27, 518, 31], [536, 28, 518, 32, "secondaryButtonText"], [536, 47, 518, 52], [537, 14, 518, 52, "children"], [537, 22, 518, 52], [537, 24, 518, 53], [538, 12, 518, 59], [539, 14, 518, 59, "fileName"], [539, 22, 518, 59], [539, 24, 518, 59, "_jsxFileName"], [539, 36, 518, 59], [540, 14, 518, 59, "lineNumber"], [540, 24, 518, 59], [541, 14, 518, 59, "columnNumber"], [541, 26, 518, 59], [542, 12, 518, 59], [542, 19, 518, 65], [543, 10, 518, 66], [544, 12, 518, 66, "fileName"], [544, 20, 518, 66], [544, 22, 518, 66, "_jsxFileName"], [544, 34, 518, 66], [545, 12, 518, 66, "lineNumber"], [545, 22, 518, 66], [546, 12, 518, 66, "columnNumber"], [546, 24, 518, 66], [547, 10, 518, 66], [547, 17, 519, 28], [547, 18, 519, 29], [548, 8, 519, 29], [549, 10, 519, 29, "fileName"], [549, 18, 519, 29], [549, 20, 519, 29, "_jsxFileName"], [549, 32, 519, 29], [550, 10, 519, 29, "lineNumber"], [550, 20, 519, 29], [551, 10, 519, 29, "columnNumber"], [551, 22, 519, 29], [552, 8, 519, 29], [552, 15, 520, 14], [553, 6, 520, 15], [554, 8, 520, 15, "fileName"], [554, 16, 520, 15], [554, 18, 520, 15, "_jsxFileName"], [554, 30, 520, 15], [555, 8, 520, 15, "lineNumber"], [555, 18, 520, 15], [556, 8, 520, 15, "columnNumber"], [556, 20, 520, 15], [557, 6, 520, 15], [557, 13, 521, 12], [557, 14, 521, 13], [558, 4, 523, 2], [559, 4, 524, 2], [560, 4, 525, 2, "console"], [560, 11, 525, 9], [560, 12, 525, 10, "log"], [560, 15, 525, 13], [560, 16, 525, 14], [560, 55, 525, 53], [560, 56, 525, 54], [561, 4, 527, 2], [561, 24, 528, 4], [561, 28, 528, 4, "_jsxDevRuntime"], [561, 42, 528, 4], [561, 43, 528, 4, "jsxDEV"], [561, 49, 528, 4], [561, 51, 528, 5, "_View"], [561, 56, 528, 5], [561, 57, 528, 5, "default"], [561, 64, 528, 9], [562, 6, 528, 10, "style"], [562, 11, 528, 15], [562, 13, 528, 17, "styles"], [562, 19, 528, 23], [562, 20, 528, 24, "container"], [562, 29, 528, 34], [563, 6, 528, 34, "children"], [563, 14, 528, 34], [563, 30, 530, 6], [563, 34, 530, 6, "_jsxDevRuntime"], [563, 48, 530, 6], [563, 49, 530, 6, "jsxDEV"], [563, 55, 530, 6], [563, 57, 530, 7, "_View"], [563, 62, 530, 7], [563, 63, 530, 7, "default"], [563, 70, 530, 11], [564, 8, 530, 12, "style"], [564, 13, 530, 17], [564, 15, 530, 19, "styles"], [564, 21, 530, 25], [564, 22, 530, 26, "cameraContainer"], [564, 37, 530, 42], [565, 8, 530, 43, "id"], [565, 10, 530, 45], [565, 12, 530, 46], [565, 29, 530, 63], [566, 8, 530, 63, "children"], [566, 16, 530, 63], [566, 32, 531, 8], [566, 36, 531, 8, "_jsxDevRuntime"], [566, 50, 531, 8], [566, 51, 531, 8, "jsxDEV"], [566, 57, 531, 8], [566, 59, 531, 9, "_expoCamera"], [566, 70, 531, 9], [566, 71, 531, 9, "CameraView"], [566, 81, 531, 19], [567, 10, 532, 10, "ref"], [567, 13, 532, 13], [567, 15, 532, 15, "cameraRef"], [567, 24, 532, 25], [568, 10, 533, 10, "style"], [568, 15, 533, 15], [568, 17, 533, 17], [568, 18, 533, 18, "styles"], [568, 24, 533, 24], [568, 25, 533, 25, "camera"], [568, 31, 533, 31], [568, 33, 533, 33], [569, 12, 533, 35, "backgroundColor"], [569, 27, 533, 50], [569, 29, 533, 52], [570, 10, 533, 62], [570, 11, 533, 63], [570, 12, 533, 65], [571, 10, 534, 10, "facing"], [571, 16, 534, 16], [571, 18, 534, 17], [571, 24, 534, 23], [572, 10, 535, 10, "onLayout"], [572, 18, 535, 18], [572, 20, 535, 21, "e"], [572, 21, 535, 22], [572, 25, 535, 27], [573, 12, 536, 12, "console"], [573, 19, 536, 19], [573, 20, 536, 20, "log"], [573, 23, 536, 23], [573, 24, 536, 24], [573, 56, 536, 56], [573, 58, 536, 58, "e"], [573, 59, 536, 59], [573, 60, 536, 60, "nativeEvent"], [573, 71, 536, 71], [573, 72, 536, 72, "layout"], [573, 78, 536, 78], [573, 79, 536, 79], [574, 12, 537, 12, "setViewSize"], [574, 23, 537, 23], [574, 24, 537, 24], [575, 14, 537, 26, "width"], [575, 19, 537, 31], [575, 21, 537, 33, "e"], [575, 22, 537, 34], [575, 23, 537, 35, "nativeEvent"], [575, 34, 537, 46], [575, 35, 537, 47, "layout"], [575, 41, 537, 53], [575, 42, 537, 54, "width"], [575, 47, 537, 59], [576, 14, 537, 61, "height"], [576, 20, 537, 67], [576, 22, 537, 69, "e"], [576, 23, 537, 70], [576, 24, 537, 71, "nativeEvent"], [576, 35, 537, 82], [576, 36, 537, 83, "layout"], [576, 42, 537, 89], [576, 43, 537, 90, "height"], [577, 12, 537, 97], [577, 13, 537, 98], [577, 14, 537, 99], [578, 10, 538, 10], [578, 11, 538, 12], [579, 10, 539, 10, "onCameraReady"], [579, 23, 539, 23], [579, 25, 539, 25, "onCameraReady"], [579, 26, 539, 25], [579, 31, 539, 31], [580, 12, 540, 12, "console"], [580, 19, 540, 19], [580, 20, 540, 20, "log"], [580, 23, 540, 23], [580, 24, 540, 24], [580, 55, 540, 55], [580, 56, 540, 56], [581, 12, 541, 12, "setIsCameraReady"], [581, 28, 541, 28], [581, 29, 541, 29], [581, 33, 541, 33], [581, 34, 541, 34], [581, 35, 541, 35], [581, 36, 541, 36], [582, 10, 542, 10], [582, 11, 542, 12], [583, 10, 543, 10, "onMountError"], [583, 22, 543, 22], [583, 24, 543, 25, "error"], [583, 29, 543, 30], [583, 33, 543, 35], [584, 12, 544, 12, "console"], [584, 19, 544, 19], [584, 20, 544, 20, "error"], [584, 25, 544, 25], [584, 26, 544, 26], [584, 63, 544, 63], [584, 65, 544, 65, "error"], [584, 70, 544, 70], [584, 71, 544, 71], [585, 12, 545, 12, "setErrorMessage"], [585, 27, 545, 27], [585, 28, 545, 28], [585, 57, 545, 57], [585, 58, 545, 58], [586, 12, 546, 12, "setProcessingState"], [586, 30, 546, 30], [586, 31, 546, 31], [586, 38, 546, 38], [586, 39, 546, 39], [587, 10, 547, 10], [588, 8, 547, 12], [589, 10, 547, 12, "fileName"], [589, 18, 547, 12], [589, 20, 547, 12, "_jsxFileName"], [589, 32, 547, 12], [590, 10, 547, 12, "lineNumber"], [590, 20, 547, 12], [591, 10, 547, 12, "columnNumber"], [591, 22, 547, 12], [592, 8, 547, 12], [592, 15, 548, 9], [592, 16, 548, 10], [592, 18, 550, 9], [592, 19, 550, 10, "isCameraReady"], [592, 32, 550, 23], [592, 49, 551, 10], [592, 53, 551, 10, "_jsxDevRuntime"], [592, 67, 551, 10], [592, 68, 551, 10, "jsxDEV"], [592, 74, 551, 10], [592, 76, 551, 11, "_View"], [592, 81, 551, 11], [592, 82, 551, 11, "default"], [592, 89, 551, 15], [593, 10, 551, 16, "style"], [593, 15, 551, 21], [593, 17, 551, 23], [593, 18, 551, 24, "StyleSheet"], [593, 37, 551, 34], [593, 38, 551, 35, "absoluteFill"], [593, 50, 551, 47], [593, 52, 551, 49], [594, 12, 551, 51, "backgroundColor"], [594, 27, 551, 66], [594, 29, 551, 68], [594, 49, 551, 88], [595, 12, 551, 90, "justifyContent"], [595, 26, 551, 104], [595, 28, 551, 106], [595, 36, 551, 114], [596, 12, 551, 116, "alignItems"], [596, 22, 551, 126], [596, 24, 551, 128], [596, 32, 551, 136], [597, 12, 551, 138, "zIndex"], [597, 18, 551, 144], [597, 20, 551, 146], [598, 10, 551, 151], [598, 11, 551, 152], [598, 12, 551, 154], [599, 10, 551, 154, "children"], [599, 18, 551, 154], [599, 33, 552, 12], [599, 37, 552, 12, "_jsxDevRuntime"], [599, 51, 552, 12], [599, 52, 552, 12, "jsxDEV"], [599, 58, 552, 12], [599, 60, 552, 13, "_View"], [599, 65, 552, 13], [599, 66, 552, 13, "default"], [599, 73, 552, 17], [600, 12, 552, 18, "style"], [600, 17, 552, 23], [600, 19, 552, 25], [601, 14, 552, 27, "backgroundColor"], [601, 29, 552, 42], [601, 31, 552, 44], [601, 51, 552, 64], [602, 14, 552, 66, "padding"], [602, 21, 552, 73], [602, 23, 552, 75], [602, 25, 552, 77], [603, 14, 552, 79, "borderRadius"], [603, 26, 552, 91], [603, 28, 552, 93], [603, 30, 552, 95], [604, 14, 552, 97, "alignItems"], [604, 24, 552, 107], [604, 26, 552, 109], [605, 12, 552, 118], [605, 13, 552, 120], [606, 12, 552, 120, "children"], [606, 20, 552, 120], [606, 36, 553, 14], [606, 40, 553, 14, "_jsxDevRuntime"], [606, 54, 553, 14], [606, 55, 553, 14, "jsxDEV"], [606, 61, 553, 14], [606, 63, 553, 15, "_ActivityIndicator"], [606, 81, 553, 15], [606, 82, 553, 15, "default"], [606, 89, 553, 32], [607, 14, 553, 33, "size"], [607, 18, 553, 37], [607, 20, 553, 38], [607, 27, 553, 45], [608, 14, 553, 46, "color"], [608, 19, 553, 51], [608, 21, 553, 52], [608, 30, 553, 61], [609, 14, 553, 62, "style"], [609, 19, 553, 67], [609, 21, 553, 69], [610, 16, 553, 71, "marginBottom"], [610, 28, 553, 83], [610, 30, 553, 85], [611, 14, 553, 88], [612, 12, 553, 90], [613, 14, 553, 90, "fileName"], [613, 22, 553, 90], [613, 24, 553, 90, "_jsxFileName"], [613, 36, 553, 90], [614, 14, 553, 90, "lineNumber"], [614, 24, 553, 90], [615, 14, 553, 90, "columnNumber"], [615, 26, 553, 90], [616, 12, 553, 90], [616, 19, 553, 92], [616, 20, 553, 93], [616, 35, 554, 14], [616, 39, 554, 14, "_jsxDevRuntime"], [616, 53, 554, 14], [616, 54, 554, 14, "jsxDEV"], [616, 60, 554, 14], [616, 62, 554, 15, "_Text"], [616, 67, 554, 15], [616, 68, 554, 15, "default"], [616, 75, 554, 19], [617, 14, 554, 20, "style"], [617, 19, 554, 25], [617, 21, 554, 27], [618, 16, 554, 29, "color"], [618, 21, 554, 34], [618, 23, 554, 36], [618, 29, 554, 42], [619, 16, 554, 44, "fontSize"], [619, 24, 554, 52], [619, 26, 554, 54], [619, 28, 554, 56], [620, 16, 554, 58, "fontWeight"], [620, 26, 554, 68], [620, 28, 554, 70], [621, 14, 554, 76], [621, 15, 554, 78], [622, 14, 554, 78, "children"], [622, 22, 554, 78], [622, 24, 554, 79], [623, 12, 554, 101], [624, 14, 554, 101, "fileName"], [624, 22, 554, 101], [624, 24, 554, 101, "_jsxFileName"], [624, 36, 554, 101], [625, 14, 554, 101, "lineNumber"], [625, 24, 554, 101], [626, 14, 554, 101, "columnNumber"], [626, 26, 554, 101], [627, 12, 554, 101], [627, 19, 554, 107], [627, 20, 554, 108], [627, 35, 555, 14], [627, 39, 555, 14, "_jsxDevRuntime"], [627, 53, 555, 14], [627, 54, 555, 14, "jsxDEV"], [627, 60, 555, 14], [627, 62, 555, 15, "_Text"], [627, 67, 555, 15], [627, 68, 555, 15, "default"], [627, 75, 555, 19], [628, 14, 555, 20, "style"], [628, 19, 555, 25], [628, 21, 555, 27], [629, 16, 555, 29, "color"], [629, 21, 555, 34], [629, 23, 555, 36], [629, 32, 555, 45], [630, 16, 555, 47, "fontSize"], [630, 24, 555, 55], [630, 26, 555, 57], [630, 28, 555, 59], [631, 16, 555, 61, "marginTop"], [631, 25, 555, 70], [631, 27, 555, 72], [632, 14, 555, 74], [632, 15, 555, 76], [633, 14, 555, 76, "children"], [633, 22, 555, 76], [633, 24, 555, 77], [634, 12, 555, 88], [635, 14, 555, 88, "fileName"], [635, 22, 555, 88], [635, 24, 555, 88, "_jsxFileName"], [635, 36, 555, 88], [636, 14, 555, 88, "lineNumber"], [636, 24, 555, 88], [637, 14, 555, 88, "columnNumber"], [637, 26, 555, 88], [638, 12, 555, 88], [638, 19, 555, 94], [638, 20, 555, 95], [639, 10, 555, 95], [640, 12, 555, 95, "fileName"], [640, 20, 555, 95], [640, 22, 555, 95, "_jsxFileName"], [640, 34, 555, 95], [641, 12, 555, 95, "lineNumber"], [641, 22, 555, 95], [642, 12, 555, 95, "columnNumber"], [642, 24, 555, 95], [643, 10, 555, 95], [643, 17, 556, 18], [644, 8, 556, 19], [645, 10, 556, 19, "fileName"], [645, 18, 556, 19], [645, 20, 556, 19, "_jsxFileName"], [645, 32, 556, 19], [646, 10, 556, 19, "lineNumber"], [646, 20, 556, 19], [647, 10, 556, 19, "columnNumber"], [647, 22, 556, 19], [648, 8, 556, 19], [648, 15, 557, 16], [648, 16, 558, 9], [648, 18, 561, 9, "isCameraReady"], [648, 31, 561, 22], [648, 35, 561, 26, "previewBlurEnabled"], [648, 53, 561, 44], [648, 57, 561, 48, "viewSize"], [648, 65, 561, 56], [648, 66, 561, 57, "width"], [648, 71, 561, 62], [648, 74, 561, 65], [648, 75, 561, 66], [648, 92, 562, 10], [648, 96, 562, 10, "_jsxDevRuntime"], [648, 110, 562, 10], [648, 111, 562, 10, "jsxDEV"], [648, 117, 562, 10], [648, 119, 562, 10, "_jsxDevRuntime"], [648, 133, 562, 10], [648, 134, 562, 10, "Fragment"], [648, 142, 562, 10], [649, 10, 562, 10, "children"], [649, 18, 562, 10], [649, 34, 564, 12], [649, 38, 564, 12, "_jsxDevRuntime"], [649, 52, 564, 12], [649, 53, 564, 12, "jsxDEV"], [649, 59, 564, 12], [649, 61, 564, 13, "_LiveFaceCanvas"], [649, 76, 564, 13], [649, 77, 564, 13, "default"], [649, 84, 564, 27], [650, 12, 564, 28, "containerId"], [650, 23, 564, 39], [650, 25, 564, 40], [650, 42, 564, 57], [651, 12, 564, 58, "width"], [651, 17, 564, 63], [651, 19, 564, 65, "viewSize"], [651, 27, 564, 73], [651, 28, 564, 74, "width"], [651, 33, 564, 80], [652, 12, 564, 81, "height"], [652, 18, 564, 87], [652, 20, 564, 89, "viewSize"], [652, 28, 564, 97], [652, 29, 564, 98, "height"], [653, 10, 564, 105], [654, 12, 564, 105, "fileName"], [654, 20, 564, 105], [654, 22, 564, 105, "_jsxFileName"], [654, 34, 564, 105], [655, 12, 564, 105, "lineNumber"], [655, 22, 564, 105], [656, 12, 564, 105, "columnNumber"], [656, 24, 564, 105], [657, 10, 564, 105], [657, 17, 564, 107], [657, 18, 564, 108], [657, 33, 565, 12], [657, 37, 565, 12, "_jsxDevRuntime"], [657, 51, 565, 12], [657, 52, 565, 12, "jsxDEV"], [657, 58, 565, 12], [657, 60, 565, 13, "_View"], [657, 65, 565, 13], [657, 66, 565, 13, "default"], [657, 73, 565, 17], [658, 12, 565, 18, "style"], [658, 17, 565, 23], [658, 19, 565, 25], [658, 20, 565, 26, "StyleSheet"], [658, 39, 565, 36], [658, 40, 565, 37, "absoluteFill"], [658, 52, 565, 49], [658, 54, 565, 51], [659, 14, 565, 53, "pointerEvents"], [659, 27, 565, 66], [659, 29, 565, 68], [660, 12, 565, 75], [660, 13, 565, 76], [660, 14, 565, 78], [661, 12, 565, 78, "children"], [661, 20, 565, 78], [661, 36, 567, 12], [661, 40, 567, 12, "_jsxDevRuntime"], [661, 54, 567, 12], [661, 55, 567, 12, "jsxDEV"], [661, 61, 567, 12], [661, 63, 567, 13, "_expoBlur"], [661, 72, 567, 13], [661, 73, 567, 13, "BlurView"], [661, 81, 567, 21], [662, 14, 567, 22, "intensity"], [662, 23, 567, 31], [662, 25, 567, 33], [662, 27, 567, 36], [663, 14, 567, 37, "tint"], [663, 18, 567, 41], [663, 20, 567, 42], [663, 26, 567, 48], [664, 14, 567, 49, "style"], [664, 19, 567, 54], [664, 21, 567, 56], [664, 22, 567, 57, "styles"], [664, 28, 567, 63], [664, 29, 567, 64, "blurZone"], [664, 37, 567, 72], [664, 39, 567, 74], [665, 16, 568, 14, "left"], [665, 20, 568, 18], [665, 22, 568, 20], [665, 23, 568, 21], [666, 16, 569, 14, "top"], [666, 19, 569, 17], [666, 21, 569, 19, "viewSize"], [666, 29, 569, 27], [666, 30, 569, 28, "height"], [666, 36, 569, 34], [666, 39, 569, 37], [666, 42, 569, 40], [667, 16, 570, 14, "width"], [667, 21, 570, 19], [667, 23, 570, 21, "viewSize"], [667, 31, 570, 29], [667, 32, 570, 30, "width"], [667, 37, 570, 35], [668, 16, 571, 14, "height"], [668, 22, 571, 20], [668, 24, 571, 22, "viewSize"], [668, 32, 571, 30], [668, 33, 571, 31, "height"], [668, 39, 571, 37], [668, 42, 571, 40], [668, 46, 571, 44], [669, 16, 572, 14, "borderRadius"], [669, 28, 572, 26], [669, 30, 572, 28], [670, 14, 573, 12], [670, 15, 573, 13], [671, 12, 573, 15], [672, 14, 573, 15, "fileName"], [672, 22, 573, 15], [672, 24, 573, 15, "_jsxFileName"], [672, 36, 573, 15], [673, 14, 573, 15, "lineNumber"], [673, 24, 573, 15], [674, 14, 573, 15, "columnNumber"], [674, 26, 573, 15], [675, 12, 573, 15], [675, 19, 573, 17], [675, 20, 573, 18], [675, 35, 575, 12], [675, 39, 575, 12, "_jsxDevRuntime"], [675, 53, 575, 12], [675, 54, 575, 12, "jsxDEV"], [675, 60, 575, 12], [675, 62, 575, 13, "_expoBlur"], [675, 71, 575, 13], [675, 72, 575, 13, "BlurView"], [675, 80, 575, 21], [676, 14, 575, 22, "intensity"], [676, 23, 575, 31], [676, 25, 575, 33], [676, 27, 575, 36], [677, 14, 575, 37, "tint"], [677, 18, 575, 41], [677, 20, 575, 42], [677, 26, 575, 48], [678, 14, 575, 49, "style"], [678, 19, 575, 54], [678, 21, 575, 56], [678, 22, 575, 57, "styles"], [678, 28, 575, 63], [678, 29, 575, 64, "blurZone"], [678, 37, 575, 72], [678, 39, 575, 74], [679, 16, 576, 14, "left"], [679, 20, 576, 18], [679, 22, 576, 20], [679, 23, 576, 21], [680, 16, 577, 14, "top"], [680, 19, 577, 17], [680, 21, 577, 19], [680, 22, 577, 20], [681, 16, 578, 14, "width"], [681, 21, 578, 19], [681, 23, 578, 21, "viewSize"], [681, 31, 578, 29], [681, 32, 578, 30, "width"], [681, 37, 578, 35], [682, 16, 579, 14, "height"], [682, 22, 579, 20], [682, 24, 579, 22, "viewSize"], [682, 32, 579, 30], [682, 33, 579, 31, "height"], [682, 39, 579, 37], [682, 42, 579, 40], [682, 45, 579, 43], [683, 16, 580, 14, "borderRadius"], [683, 28, 580, 26], [683, 30, 580, 28], [684, 14, 581, 12], [684, 15, 581, 13], [685, 12, 581, 15], [686, 14, 581, 15, "fileName"], [686, 22, 581, 15], [686, 24, 581, 15, "_jsxFileName"], [686, 36, 581, 15], [687, 14, 581, 15, "lineNumber"], [687, 24, 581, 15], [688, 14, 581, 15, "columnNumber"], [688, 26, 581, 15], [689, 12, 581, 15], [689, 19, 581, 17], [689, 20, 581, 18], [689, 35, 583, 12], [689, 39, 583, 12, "_jsxDevRuntime"], [689, 53, 583, 12], [689, 54, 583, 12, "jsxDEV"], [689, 60, 583, 12], [689, 62, 583, 13, "_expoBlur"], [689, 71, 583, 13], [689, 72, 583, 13, "BlurView"], [689, 80, 583, 21], [690, 14, 583, 22, "intensity"], [690, 23, 583, 31], [690, 25, 583, 33], [690, 27, 583, 36], [691, 14, 583, 37, "tint"], [691, 18, 583, 41], [691, 20, 583, 42], [691, 26, 583, 48], [692, 14, 583, 49, "style"], [692, 19, 583, 54], [692, 21, 583, 56], [692, 22, 583, 57, "styles"], [692, 28, 583, 63], [692, 29, 583, 64, "blurZone"], [692, 37, 583, 72], [692, 39, 583, 74], [693, 16, 584, 14, "left"], [693, 20, 584, 18], [693, 22, 584, 20, "viewSize"], [693, 30, 584, 28], [693, 31, 584, 29, "width"], [693, 36, 584, 34], [693, 39, 584, 37], [693, 42, 584, 40], [693, 45, 584, 44, "viewSize"], [693, 53, 584, 52], [693, 54, 584, 53, "width"], [693, 59, 584, 58], [693, 62, 584, 61], [693, 66, 584, 66], [694, 16, 585, 14, "top"], [694, 19, 585, 17], [694, 21, 585, 19, "viewSize"], [694, 29, 585, 27], [694, 30, 585, 28, "height"], [694, 36, 585, 34], [694, 39, 585, 37], [694, 43, 585, 41], [694, 46, 585, 45, "viewSize"], [694, 54, 585, 53], [694, 55, 585, 54, "width"], [694, 60, 585, 59], [694, 63, 585, 62], [694, 67, 585, 67], [695, 16, 586, 14, "width"], [695, 21, 586, 19], [695, 23, 586, 21, "viewSize"], [695, 31, 586, 29], [695, 32, 586, 30, "width"], [695, 37, 586, 35], [695, 40, 586, 38], [695, 43, 586, 41], [696, 16, 587, 14, "height"], [696, 22, 587, 20], [696, 24, 587, 22, "viewSize"], [696, 32, 587, 30], [696, 33, 587, 31, "width"], [696, 38, 587, 36], [696, 41, 587, 39], [696, 44, 587, 42], [697, 16, 588, 14, "borderRadius"], [697, 28, 588, 26], [697, 30, 588, 29, "viewSize"], [697, 38, 588, 37], [697, 39, 588, 38, "width"], [697, 44, 588, 43], [697, 47, 588, 46], [697, 50, 588, 49], [697, 53, 588, 53], [698, 14, 589, 12], [698, 15, 589, 13], [699, 12, 589, 15], [700, 14, 589, 15, "fileName"], [700, 22, 589, 15], [700, 24, 589, 15, "_jsxFileName"], [700, 36, 589, 15], [701, 14, 589, 15, "lineNumber"], [701, 24, 589, 15], [702, 14, 589, 15, "columnNumber"], [702, 26, 589, 15], [703, 12, 589, 15], [703, 19, 589, 17], [703, 20, 589, 18], [703, 35, 590, 12], [703, 39, 590, 12, "_jsxDevRuntime"], [703, 53, 590, 12], [703, 54, 590, 12, "jsxDEV"], [703, 60, 590, 12], [703, 62, 590, 13, "_expoBlur"], [703, 71, 590, 13], [703, 72, 590, 13, "BlurView"], [703, 80, 590, 21], [704, 14, 590, 22, "intensity"], [704, 23, 590, 31], [704, 25, 590, 33], [704, 27, 590, 36], [705, 14, 590, 37, "tint"], [705, 18, 590, 41], [705, 20, 590, 42], [705, 26, 590, 48], [706, 14, 590, 49, "style"], [706, 19, 590, 54], [706, 21, 590, 56], [706, 22, 590, 57, "styles"], [706, 28, 590, 63], [706, 29, 590, 64, "blurZone"], [706, 37, 590, 72], [706, 39, 590, 74], [707, 16, 591, 14, "left"], [707, 20, 591, 18], [707, 22, 591, 20, "viewSize"], [707, 30, 591, 28], [707, 31, 591, 29, "width"], [707, 36, 591, 34], [707, 39, 591, 37], [707, 42, 591, 40], [707, 45, 591, 44, "viewSize"], [707, 53, 591, 52], [707, 54, 591, 53, "width"], [707, 59, 591, 58], [707, 62, 591, 61], [707, 66, 591, 66], [708, 16, 592, 14, "top"], [708, 19, 592, 17], [708, 21, 592, 19, "viewSize"], [708, 29, 592, 27], [708, 30, 592, 28, "height"], [708, 36, 592, 34], [708, 39, 592, 37], [708, 42, 592, 40], [708, 45, 592, 44, "viewSize"], [708, 53, 592, 52], [708, 54, 592, 53, "width"], [708, 59, 592, 58], [708, 62, 592, 61], [708, 66, 592, 66], [709, 16, 593, 14, "width"], [709, 21, 593, 19], [709, 23, 593, 21, "viewSize"], [709, 31, 593, 29], [709, 32, 593, 30, "width"], [709, 37, 593, 35], [709, 40, 593, 38], [709, 43, 593, 41], [710, 16, 594, 14, "height"], [710, 22, 594, 20], [710, 24, 594, 22, "viewSize"], [710, 32, 594, 30], [710, 33, 594, 31, "width"], [710, 38, 594, 36], [710, 41, 594, 39], [710, 44, 594, 42], [711, 16, 595, 14, "borderRadius"], [711, 28, 595, 26], [711, 30, 595, 29, "viewSize"], [711, 38, 595, 37], [711, 39, 595, 38, "width"], [711, 44, 595, 43], [711, 47, 595, 46], [711, 50, 595, 49], [711, 53, 595, 53], [712, 14, 596, 12], [712, 15, 596, 13], [713, 12, 596, 15], [714, 14, 596, 15, "fileName"], [714, 22, 596, 15], [714, 24, 596, 15, "_jsxFileName"], [714, 36, 596, 15], [715, 14, 596, 15, "lineNumber"], [715, 24, 596, 15], [716, 14, 596, 15, "columnNumber"], [716, 26, 596, 15], [717, 12, 596, 15], [717, 19, 596, 17], [717, 20, 596, 18], [717, 35, 597, 12], [717, 39, 597, 12, "_jsxDevRuntime"], [717, 53, 597, 12], [717, 54, 597, 12, "jsxDEV"], [717, 60, 597, 12], [717, 62, 597, 13, "_expoBlur"], [717, 71, 597, 13], [717, 72, 597, 13, "BlurView"], [717, 80, 597, 21], [718, 14, 597, 22, "intensity"], [718, 23, 597, 31], [718, 25, 597, 33], [718, 27, 597, 36], [719, 14, 597, 37, "tint"], [719, 18, 597, 41], [719, 20, 597, 42], [719, 26, 597, 48], [720, 14, 597, 49, "style"], [720, 19, 597, 54], [720, 21, 597, 56], [720, 22, 597, 57, "styles"], [720, 28, 597, 63], [720, 29, 597, 64, "blurZone"], [720, 37, 597, 72], [720, 39, 597, 74], [721, 16, 598, 14, "left"], [721, 20, 598, 18], [721, 22, 598, 20, "viewSize"], [721, 30, 598, 28], [721, 31, 598, 29, "width"], [721, 36, 598, 34], [721, 39, 598, 37], [721, 42, 598, 40], [721, 45, 598, 44, "viewSize"], [721, 53, 598, 52], [721, 54, 598, 53, "width"], [721, 59, 598, 58], [721, 62, 598, 61], [721, 66, 598, 66], [722, 16, 599, 14, "top"], [722, 19, 599, 17], [722, 21, 599, 19, "viewSize"], [722, 29, 599, 27], [722, 30, 599, 28, "height"], [722, 36, 599, 34], [722, 39, 599, 37], [722, 42, 599, 40], [722, 45, 599, 44, "viewSize"], [722, 53, 599, 52], [722, 54, 599, 53, "width"], [722, 59, 599, 58], [722, 62, 599, 61], [722, 66, 599, 66], [723, 16, 600, 14, "width"], [723, 21, 600, 19], [723, 23, 600, 21, "viewSize"], [723, 31, 600, 29], [723, 32, 600, 30, "width"], [723, 37, 600, 35], [723, 40, 600, 38], [723, 43, 600, 41], [724, 16, 601, 14, "height"], [724, 22, 601, 20], [724, 24, 601, 22, "viewSize"], [724, 32, 601, 30], [724, 33, 601, 31, "width"], [724, 38, 601, 36], [724, 41, 601, 39], [724, 44, 601, 42], [725, 16, 602, 14, "borderRadius"], [725, 28, 602, 26], [725, 30, 602, 29, "viewSize"], [725, 38, 602, 37], [725, 39, 602, 38, "width"], [725, 44, 602, 43], [725, 47, 602, 46], [725, 50, 602, 49], [725, 53, 602, 53], [726, 14, 603, 12], [726, 15, 603, 13], [727, 12, 603, 15], [728, 14, 603, 15, "fileName"], [728, 22, 603, 15], [728, 24, 603, 15, "_jsxFileName"], [728, 36, 603, 15], [729, 14, 603, 15, "lineNumber"], [729, 24, 603, 15], [730, 14, 603, 15, "columnNumber"], [730, 26, 603, 15], [731, 12, 603, 15], [731, 19, 603, 17], [731, 20, 603, 18], [731, 22, 605, 13, "__DEV__"], [731, 29, 605, 20], [731, 46, 606, 14], [731, 50, 606, 14, "_jsxDevRuntime"], [731, 64, 606, 14], [731, 65, 606, 14, "jsxDEV"], [731, 71, 606, 14], [731, 73, 606, 15, "_View"], [731, 78, 606, 15], [731, 79, 606, 15, "default"], [731, 86, 606, 19], [732, 14, 606, 20, "style"], [732, 19, 606, 25], [732, 21, 606, 27, "styles"], [732, 27, 606, 33], [732, 28, 606, 34, "previewChip"], [732, 39, 606, 46], [733, 14, 606, 46, "children"], [733, 22, 606, 46], [733, 37, 607, 16], [733, 41, 607, 16, "_jsxDevRuntime"], [733, 55, 607, 16], [733, 56, 607, 16, "jsxDEV"], [733, 62, 607, 16], [733, 64, 607, 17, "_Text"], [733, 69, 607, 17], [733, 70, 607, 17, "default"], [733, 77, 607, 21], [734, 16, 607, 22, "style"], [734, 21, 607, 27], [734, 23, 607, 29, "styles"], [734, 29, 607, 35], [734, 30, 607, 36, "previewChipText"], [734, 45, 607, 52], [735, 16, 607, 52, "children"], [735, 24, 607, 52], [735, 26, 607, 53], [736, 14, 607, 73], [737, 16, 607, 73, "fileName"], [737, 24, 607, 73], [737, 26, 607, 73, "_jsxFileName"], [737, 38, 607, 73], [738, 16, 607, 73, "lineNumber"], [738, 26, 607, 73], [739, 16, 607, 73, "columnNumber"], [739, 28, 607, 73], [740, 14, 607, 73], [740, 21, 607, 79], [741, 12, 607, 80], [742, 14, 607, 80, "fileName"], [742, 22, 607, 80], [742, 24, 607, 80, "_jsxFileName"], [742, 36, 607, 80], [743, 14, 607, 80, "lineNumber"], [743, 24, 607, 80], [744, 14, 607, 80, "columnNumber"], [744, 26, 607, 80], [745, 12, 607, 80], [745, 19, 608, 20], [745, 20, 609, 13], [746, 10, 609, 13], [747, 12, 609, 13, "fileName"], [747, 20, 609, 13], [747, 22, 609, 13, "_jsxFileName"], [747, 34, 609, 13], [748, 12, 609, 13, "lineNumber"], [748, 22, 609, 13], [749, 12, 609, 13, "columnNumber"], [749, 24, 609, 13], [750, 10, 609, 13], [750, 17, 610, 18], [750, 18, 610, 19], [751, 8, 610, 19], [751, 23, 611, 12], [751, 24, 612, 9], [751, 26, 614, 9, "isCameraReady"], [751, 39, 614, 22], [751, 56, 615, 10], [751, 60, 615, 10, "_jsxDevRuntime"], [751, 74, 615, 10], [751, 75, 615, 10, "jsxDEV"], [751, 81, 615, 10], [751, 83, 615, 10, "_jsxDevRuntime"], [751, 97, 615, 10], [751, 98, 615, 10, "Fragment"], [751, 106, 615, 10], [752, 10, 615, 10, "children"], [752, 18, 615, 10], [752, 34, 617, 12], [752, 38, 617, 12, "_jsxDevRuntime"], [752, 52, 617, 12], [752, 53, 617, 12, "jsxDEV"], [752, 59, 617, 12], [752, 61, 617, 13, "_View"], [752, 66, 617, 13], [752, 67, 617, 13, "default"], [752, 74, 617, 17], [753, 12, 617, 18, "style"], [753, 17, 617, 23], [753, 19, 617, 25, "styles"], [753, 25, 617, 31], [753, 26, 617, 32, "headerOverlay"], [753, 39, 617, 46], [754, 12, 617, 46, "children"], [754, 20, 617, 46], [754, 35, 618, 14], [754, 39, 618, 14, "_jsxDevRuntime"], [754, 53, 618, 14], [754, 54, 618, 14, "jsxDEV"], [754, 60, 618, 14], [754, 62, 618, 15, "_View"], [754, 67, 618, 15], [754, 68, 618, 15, "default"], [754, 75, 618, 19], [755, 14, 618, 20, "style"], [755, 19, 618, 25], [755, 21, 618, 27, "styles"], [755, 27, 618, 33], [755, 28, 618, 34, "headerContent"], [755, 41, 618, 48], [756, 14, 618, 48, "children"], [756, 22, 618, 48], [756, 38, 619, 16], [756, 42, 619, 16, "_jsxDevRuntime"], [756, 56, 619, 16], [756, 57, 619, 16, "jsxDEV"], [756, 63, 619, 16], [756, 65, 619, 17, "_View"], [756, 70, 619, 17], [756, 71, 619, 17, "default"], [756, 78, 619, 21], [757, 16, 619, 22, "style"], [757, 21, 619, 27], [757, 23, 619, 29, "styles"], [757, 29, 619, 35], [757, 30, 619, 36, "headerLeft"], [757, 40, 619, 47], [758, 16, 619, 47, "children"], [758, 24, 619, 47], [758, 40, 620, 18], [758, 44, 620, 18, "_jsxDevRuntime"], [758, 58, 620, 18], [758, 59, 620, 18, "jsxDEV"], [758, 65, 620, 18], [758, 67, 620, 19, "_Text"], [758, 72, 620, 19], [758, 73, 620, 19, "default"], [758, 80, 620, 23], [759, 18, 620, 24, "style"], [759, 23, 620, 29], [759, 25, 620, 31, "styles"], [759, 31, 620, 37], [759, 32, 620, 38, "headerTitle"], [759, 43, 620, 50], [760, 18, 620, 50, "children"], [760, 26, 620, 50], [760, 28, 620, 51], [761, 16, 620, 62], [762, 18, 620, 62, "fileName"], [762, 26, 620, 62], [762, 28, 620, 62, "_jsxFileName"], [762, 40, 620, 62], [763, 18, 620, 62, "lineNumber"], [763, 28, 620, 62], [764, 18, 620, 62, "columnNumber"], [764, 30, 620, 62], [765, 16, 620, 62], [765, 23, 620, 68], [765, 24, 620, 69], [765, 39, 621, 18], [765, 43, 621, 18, "_jsxDevRuntime"], [765, 57, 621, 18], [765, 58, 621, 18, "jsxDEV"], [765, 64, 621, 18], [765, 66, 621, 19, "_View"], [765, 71, 621, 19], [765, 72, 621, 19, "default"], [765, 79, 621, 23], [766, 18, 621, 24, "style"], [766, 23, 621, 29], [766, 25, 621, 31, "styles"], [766, 31, 621, 37], [766, 32, 621, 38, "subtitleRow"], [766, 43, 621, 50], [767, 18, 621, 50, "children"], [767, 26, 621, 50], [767, 42, 622, 20], [767, 46, 622, 20, "_jsxDevRuntime"], [767, 60, 622, 20], [767, 61, 622, 20, "jsxDEV"], [767, 67, 622, 20], [767, 69, 622, 21, "_Text"], [767, 74, 622, 21], [767, 75, 622, 21, "default"], [767, 82, 622, 25], [768, 20, 622, 26, "style"], [768, 25, 622, 31], [768, 27, 622, 33, "styles"], [768, 33, 622, 39], [768, 34, 622, 40, "webIcon"], [768, 41, 622, 48], [769, 20, 622, 48, "children"], [769, 28, 622, 48], [769, 30, 622, 49], [770, 18, 622, 51], [771, 20, 622, 51, "fileName"], [771, 28, 622, 51], [771, 30, 622, 51, "_jsxFileName"], [771, 42, 622, 51], [772, 20, 622, 51, "lineNumber"], [772, 30, 622, 51], [773, 20, 622, 51, "columnNumber"], [773, 32, 622, 51], [774, 18, 622, 51], [774, 25, 622, 57], [774, 26, 622, 58], [774, 41, 623, 20], [774, 45, 623, 20, "_jsxDevRuntime"], [774, 59, 623, 20], [774, 60, 623, 20, "jsxDEV"], [774, 66, 623, 20], [774, 68, 623, 21, "_Text"], [774, 73, 623, 21], [774, 74, 623, 21, "default"], [774, 81, 623, 25], [775, 20, 623, 26, "style"], [775, 25, 623, 31], [775, 27, 623, 33, "styles"], [775, 33, 623, 39], [775, 34, 623, 40, "headerSubtitle"], [775, 48, 623, 55], [776, 20, 623, 55, "children"], [776, 28, 623, 55], [776, 30, 623, 56], [777, 18, 623, 71], [778, 20, 623, 71, "fileName"], [778, 28, 623, 71], [778, 30, 623, 71, "_jsxFileName"], [778, 42, 623, 71], [779, 20, 623, 71, "lineNumber"], [779, 30, 623, 71], [780, 20, 623, 71, "columnNumber"], [780, 32, 623, 71], [781, 18, 623, 71], [781, 25, 623, 77], [781, 26, 623, 78], [782, 16, 623, 78], [783, 18, 623, 78, "fileName"], [783, 26, 623, 78], [783, 28, 623, 78, "_jsxFileName"], [783, 40, 623, 78], [784, 18, 623, 78, "lineNumber"], [784, 28, 623, 78], [785, 18, 623, 78, "columnNumber"], [785, 30, 623, 78], [786, 16, 623, 78], [786, 23, 624, 24], [786, 24, 624, 25], [786, 26, 625, 19, "challengeCode"], [786, 39, 625, 32], [786, 56, 626, 20], [786, 60, 626, 20, "_jsxDevRuntime"], [786, 74, 626, 20], [786, 75, 626, 20, "jsxDEV"], [786, 81, 626, 20], [786, 83, 626, 21, "_View"], [786, 88, 626, 21], [786, 89, 626, 21, "default"], [786, 96, 626, 25], [787, 18, 626, 26, "style"], [787, 23, 626, 31], [787, 25, 626, 33, "styles"], [787, 31, 626, 39], [787, 32, 626, 40, "challengeRow"], [787, 44, 626, 53], [788, 18, 626, 53, "children"], [788, 26, 626, 53], [788, 42, 627, 22], [788, 46, 627, 22, "_jsxDevRuntime"], [788, 60, 627, 22], [788, 61, 627, 22, "jsxDEV"], [788, 67, 627, 22], [788, 69, 627, 23, "_lucideReactNative"], [788, 87, 627, 23], [788, 88, 627, 23, "Shield"], [788, 94, 627, 29], [789, 20, 627, 30, "size"], [789, 24, 627, 34], [789, 26, 627, 36], [789, 28, 627, 39], [790, 20, 627, 40, "color"], [790, 25, 627, 45], [790, 27, 627, 46], [791, 18, 627, 52], [792, 20, 627, 52, "fileName"], [792, 28, 627, 52], [792, 30, 627, 52, "_jsxFileName"], [792, 42, 627, 52], [793, 20, 627, 52, "lineNumber"], [793, 30, 627, 52], [794, 20, 627, 52, "columnNumber"], [794, 32, 627, 52], [795, 18, 627, 52], [795, 25, 627, 54], [795, 26, 627, 55], [795, 41, 628, 22], [795, 45, 628, 22, "_jsxDevRuntime"], [795, 59, 628, 22], [795, 60, 628, 22, "jsxDEV"], [795, 66, 628, 22], [795, 68, 628, 23, "_Text"], [795, 73, 628, 23], [795, 74, 628, 23, "default"], [795, 81, 628, 27], [796, 20, 628, 28, "style"], [796, 25, 628, 33], [796, 27, 628, 35, "styles"], [796, 33, 628, 41], [796, 34, 628, 42, "challengeCode"], [796, 47, 628, 56], [797, 20, 628, 56, "children"], [797, 28, 628, 56], [797, 30, 628, 58, "challengeCode"], [798, 18, 628, 71], [799, 20, 628, 71, "fileName"], [799, 28, 628, 71], [799, 30, 628, 71, "_jsxFileName"], [799, 42, 628, 71], [800, 20, 628, 71, "lineNumber"], [800, 30, 628, 71], [801, 20, 628, 71, "columnNumber"], [801, 32, 628, 71], [802, 18, 628, 71], [802, 25, 628, 78], [802, 26, 628, 79], [803, 16, 628, 79], [804, 18, 628, 79, "fileName"], [804, 26, 628, 79], [804, 28, 628, 79, "_jsxFileName"], [804, 40, 628, 79], [805, 18, 628, 79, "lineNumber"], [805, 28, 628, 79], [806, 18, 628, 79, "columnNumber"], [806, 30, 628, 79], [807, 16, 628, 79], [807, 23, 629, 26], [807, 24, 630, 19], [808, 14, 630, 19], [809, 16, 630, 19, "fileName"], [809, 24, 630, 19], [809, 26, 630, 19, "_jsxFileName"], [809, 38, 630, 19], [810, 16, 630, 19, "lineNumber"], [810, 26, 630, 19], [811, 16, 630, 19, "columnNumber"], [811, 28, 630, 19], [812, 14, 630, 19], [812, 21, 631, 22], [812, 22, 631, 23], [812, 37, 632, 16], [812, 41, 632, 16, "_jsxDevRuntime"], [812, 55, 632, 16], [812, 56, 632, 16, "jsxDEV"], [812, 62, 632, 16], [812, 64, 632, 17, "_TouchableOpacity"], [812, 81, 632, 17], [812, 82, 632, 17, "default"], [812, 89, 632, 33], [813, 16, 632, 34, "onPress"], [813, 23, 632, 41], [813, 25, 632, 43, "onCancel"], [813, 33, 632, 52], [814, 16, 632, 53, "style"], [814, 21, 632, 58], [814, 23, 632, 60, "styles"], [814, 29, 632, 66], [814, 30, 632, 67, "closeButton"], [814, 41, 632, 79], [815, 16, 632, 79, "children"], [815, 24, 632, 79], [815, 39, 633, 18], [815, 43, 633, 18, "_jsxDevRuntime"], [815, 57, 633, 18], [815, 58, 633, 18, "jsxDEV"], [815, 64, 633, 18], [815, 66, 633, 19, "_lucideReactNative"], [815, 84, 633, 19], [815, 85, 633, 19, "X"], [815, 86, 633, 20], [816, 18, 633, 21, "size"], [816, 22, 633, 25], [816, 24, 633, 27], [816, 26, 633, 30], [817, 18, 633, 31, "color"], [817, 23, 633, 36], [817, 25, 633, 37], [818, 16, 633, 43], [819, 18, 633, 43, "fileName"], [819, 26, 633, 43], [819, 28, 633, 43, "_jsxFileName"], [819, 40, 633, 43], [820, 18, 633, 43, "lineNumber"], [820, 28, 633, 43], [821, 18, 633, 43, "columnNumber"], [821, 30, 633, 43], [822, 16, 633, 43], [822, 23, 633, 45], [823, 14, 633, 46], [824, 16, 633, 46, "fileName"], [824, 24, 633, 46], [824, 26, 633, 46, "_jsxFileName"], [824, 38, 633, 46], [825, 16, 633, 46, "lineNumber"], [825, 26, 633, 46], [826, 16, 633, 46, "columnNumber"], [826, 28, 633, 46], [827, 14, 633, 46], [827, 21, 634, 34], [827, 22, 634, 35], [828, 12, 634, 35], [829, 14, 634, 35, "fileName"], [829, 22, 634, 35], [829, 24, 634, 35, "_jsxFileName"], [829, 36, 634, 35], [830, 14, 634, 35, "lineNumber"], [830, 24, 634, 35], [831, 14, 634, 35, "columnNumber"], [831, 26, 634, 35], [832, 12, 634, 35], [832, 19, 635, 20], [833, 10, 635, 21], [834, 12, 635, 21, "fileName"], [834, 20, 635, 21], [834, 22, 635, 21, "_jsxFileName"], [834, 34, 635, 21], [835, 12, 635, 21, "lineNumber"], [835, 22, 635, 21], [836, 12, 635, 21, "columnNumber"], [836, 24, 635, 21], [837, 10, 635, 21], [837, 17, 636, 18], [837, 18, 636, 19], [837, 33, 638, 12], [837, 37, 638, 12, "_jsxDevRuntime"], [837, 51, 638, 12], [837, 52, 638, 12, "jsxDEV"], [837, 58, 638, 12], [837, 60, 638, 13, "_View"], [837, 65, 638, 13], [837, 66, 638, 13, "default"], [837, 73, 638, 17], [838, 12, 638, 18, "style"], [838, 17, 638, 23], [838, 19, 638, 25, "styles"], [838, 25, 638, 31], [838, 26, 638, 32, "privacyNotice"], [838, 39, 638, 46], [839, 12, 638, 46, "children"], [839, 20, 638, 46], [839, 36, 639, 14], [839, 40, 639, 14, "_jsxDevRuntime"], [839, 54, 639, 14], [839, 55, 639, 14, "jsxDEV"], [839, 61, 639, 14], [839, 63, 639, 15, "_lucideReactNative"], [839, 81, 639, 15], [839, 82, 639, 15, "Shield"], [839, 88, 639, 21], [840, 14, 639, 22, "size"], [840, 18, 639, 26], [840, 20, 639, 28], [840, 22, 639, 31], [841, 14, 639, 32, "color"], [841, 19, 639, 37], [841, 21, 639, 38], [842, 12, 639, 47], [843, 14, 639, 47, "fileName"], [843, 22, 639, 47], [843, 24, 639, 47, "_jsxFileName"], [843, 36, 639, 47], [844, 14, 639, 47, "lineNumber"], [844, 24, 639, 47], [845, 14, 639, 47, "columnNumber"], [845, 26, 639, 47], [846, 12, 639, 47], [846, 19, 639, 49], [846, 20, 639, 50], [846, 35, 640, 14], [846, 39, 640, 14, "_jsxDevRuntime"], [846, 53, 640, 14], [846, 54, 640, 14, "jsxDEV"], [846, 60, 640, 14], [846, 62, 640, 15, "_Text"], [846, 67, 640, 15], [846, 68, 640, 15, "default"], [846, 75, 640, 19], [847, 14, 640, 20, "style"], [847, 19, 640, 25], [847, 21, 640, 27, "styles"], [847, 27, 640, 33], [847, 28, 640, 34, "privacyText"], [847, 39, 640, 46], [848, 14, 640, 46, "children"], [848, 22, 640, 46], [848, 24, 640, 47], [849, 12, 642, 14], [850, 14, 642, 14, "fileName"], [850, 22, 642, 14], [850, 24, 642, 14, "_jsxFileName"], [850, 36, 642, 14], [851, 14, 642, 14, "lineNumber"], [851, 24, 642, 14], [852, 14, 642, 14, "columnNumber"], [852, 26, 642, 14], [853, 12, 642, 14], [853, 19, 642, 20], [853, 20, 642, 21], [854, 10, 642, 21], [855, 12, 642, 21, "fileName"], [855, 20, 642, 21], [855, 22, 642, 21, "_jsxFileName"], [855, 34, 642, 21], [856, 12, 642, 21, "lineNumber"], [856, 22, 642, 21], [857, 12, 642, 21, "columnNumber"], [857, 24, 642, 21], [858, 10, 642, 21], [858, 17, 643, 18], [858, 18, 643, 19], [858, 33, 645, 12], [858, 37, 645, 12, "_jsxDevRuntime"], [858, 51, 645, 12], [858, 52, 645, 12, "jsxDEV"], [858, 58, 645, 12], [858, 60, 645, 13, "_View"], [858, 65, 645, 13], [858, 66, 645, 13, "default"], [858, 73, 645, 17], [859, 12, 645, 18, "style"], [859, 17, 645, 23], [859, 19, 645, 25, "styles"], [859, 25, 645, 31], [859, 26, 645, 32, "footer<PERSON><PERSON><PERSON>"], [859, 39, 645, 46], [860, 12, 645, 46, "children"], [860, 20, 645, 46], [860, 36, 646, 14], [860, 40, 646, 14, "_jsxDevRuntime"], [860, 54, 646, 14], [860, 55, 646, 14, "jsxDEV"], [860, 61, 646, 14], [860, 63, 646, 15, "_Text"], [860, 68, 646, 15], [860, 69, 646, 15, "default"], [860, 76, 646, 19], [861, 14, 646, 20, "style"], [861, 19, 646, 25], [861, 21, 646, 27, "styles"], [861, 27, 646, 33], [861, 28, 646, 34, "instruction"], [861, 39, 646, 46], [862, 14, 646, 46, "children"], [862, 22, 646, 46], [862, 24, 646, 47], [863, 12, 648, 14], [864, 14, 648, 14, "fileName"], [864, 22, 648, 14], [864, 24, 648, 14, "_jsxFileName"], [864, 36, 648, 14], [865, 14, 648, 14, "lineNumber"], [865, 24, 648, 14], [866, 14, 648, 14, "columnNumber"], [866, 26, 648, 14], [867, 12, 648, 14], [867, 19, 648, 20], [867, 20, 648, 21], [867, 35, 650, 14], [867, 39, 650, 14, "_jsxDevRuntime"], [867, 53, 650, 14], [867, 54, 650, 14, "jsxDEV"], [867, 60, 650, 14], [867, 62, 650, 15, "_TouchableOpacity"], [867, 79, 650, 15], [867, 80, 650, 15, "default"], [867, 87, 650, 31], [868, 14, 651, 16, "onPress"], [868, 21, 651, 23], [868, 23, 651, 25, "capturePhoto"], [868, 35, 651, 38], [869, 14, 652, 16, "disabled"], [869, 22, 652, 24], [869, 24, 652, 26, "processingState"], [869, 39, 652, 41], [869, 44, 652, 46], [869, 50, 652, 52], [869, 54, 652, 56], [869, 55, 652, 57, "isCameraReady"], [869, 68, 652, 71], [870, 14, 653, 16, "style"], [870, 19, 653, 21], [870, 21, 653, 23], [870, 22, 654, 18, "styles"], [870, 28, 654, 24], [870, 29, 654, 25, "shutterButton"], [870, 42, 654, 38], [870, 44, 655, 18, "processingState"], [870, 59, 655, 33], [870, 64, 655, 38], [870, 70, 655, 44], [870, 74, 655, 48, "styles"], [870, 80, 655, 54], [870, 81, 655, 55, "shutterButtonDisabled"], [870, 102, 655, 76], [870, 103, 656, 18], [871, 14, 656, 18, "children"], [871, 22, 656, 18], [871, 24, 658, 17, "processingState"], [871, 39, 658, 32], [871, 44, 658, 37], [871, 50, 658, 43], [871, 66, 659, 18], [871, 70, 659, 18, "_jsxDevRuntime"], [871, 84, 659, 18], [871, 85, 659, 18, "jsxDEV"], [871, 91, 659, 18], [871, 93, 659, 19, "_View"], [871, 98, 659, 19], [871, 99, 659, 19, "default"], [871, 106, 659, 23], [872, 16, 659, 24, "style"], [872, 21, 659, 29], [872, 23, 659, 31, "styles"], [872, 29, 659, 37], [872, 30, 659, 38, "shutterInner"], [873, 14, 659, 51], [874, 16, 659, 51, "fileName"], [874, 24, 659, 51], [874, 26, 659, 51, "_jsxFileName"], [874, 38, 659, 51], [875, 16, 659, 51, "lineNumber"], [875, 26, 659, 51], [876, 16, 659, 51, "columnNumber"], [876, 28, 659, 51], [877, 14, 659, 51], [877, 21, 659, 53], [877, 22, 659, 54], [877, 38, 661, 18], [877, 42, 661, 18, "_jsxDevRuntime"], [877, 56, 661, 18], [877, 57, 661, 18, "jsxDEV"], [877, 63, 661, 18], [877, 65, 661, 19, "_ActivityIndicator"], [877, 83, 661, 19], [877, 84, 661, 19, "default"], [877, 91, 661, 36], [878, 16, 661, 37, "size"], [878, 20, 661, 41], [878, 22, 661, 42], [878, 29, 661, 49], [879, 16, 661, 50, "color"], [879, 21, 661, 55], [879, 23, 661, 56], [880, 14, 661, 65], [881, 16, 661, 65, "fileName"], [881, 24, 661, 65], [881, 26, 661, 65, "_jsxFileName"], [881, 38, 661, 65], [882, 16, 661, 65, "lineNumber"], [882, 26, 661, 65], [883, 16, 661, 65, "columnNumber"], [883, 28, 661, 65], [884, 14, 661, 65], [884, 21, 661, 67], [885, 12, 662, 17], [886, 14, 662, 17, "fileName"], [886, 22, 662, 17], [886, 24, 662, 17, "_jsxFileName"], [886, 36, 662, 17], [887, 14, 662, 17, "lineNumber"], [887, 24, 662, 17], [888, 14, 662, 17, "columnNumber"], [888, 26, 662, 17], [889, 12, 662, 17], [889, 19, 663, 32], [889, 20, 663, 33], [889, 35, 664, 14], [889, 39, 664, 14, "_jsxDevRuntime"], [889, 53, 664, 14], [889, 54, 664, 14, "jsxDEV"], [889, 60, 664, 14], [889, 62, 664, 15, "_Text"], [889, 67, 664, 15], [889, 68, 664, 15, "default"], [889, 75, 664, 19], [890, 14, 664, 20, "style"], [890, 19, 664, 25], [890, 21, 664, 27, "styles"], [890, 27, 664, 33], [890, 28, 664, 34, "privacyNote"], [890, 39, 664, 46], [891, 14, 664, 46, "children"], [891, 22, 664, 46], [891, 24, 664, 47], [892, 12, 666, 14], [893, 14, 666, 14, "fileName"], [893, 22, 666, 14], [893, 24, 666, 14, "_jsxFileName"], [893, 36, 666, 14], [894, 14, 666, 14, "lineNumber"], [894, 24, 666, 14], [895, 14, 666, 14, "columnNumber"], [895, 26, 666, 14], [896, 12, 666, 14], [896, 19, 666, 20], [896, 20, 666, 21], [897, 10, 666, 21], [898, 12, 666, 21, "fileName"], [898, 20, 666, 21], [898, 22, 666, 21, "_jsxFileName"], [898, 34, 666, 21], [899, 12, 666, 21, "lineNumber"], [899, 22, 666, 21], [900, 12, 666, 21, "columnNumber"], [900, 24, 666, 21], [901, 10, 666, 21], [901, 17, 667, 18], [901, 18, 667, 19], [902, 8, 667, 19], [902, 23, 668, 12], [902, 24, 669, 9], [903, 6, 669, 9], [904, 8, 669, 9, "fileName"], [904, 16, 669, 9], [904, 18, 669, 9, "_jsxFileName"], [904, 30, 669, 9], [905, 8, 669, 9, "lineNumber"], [905, 18, 669, 9], [906, 8, 669, 9, "columnNumber"], [906, 20, 669, 9], [907, 6, 669, 9], [907, 13, 670, 12], [907, 14, 670, 13], [907, 29, 672, 6], [907, 33, 672, 6, "_jsxDevRuntime"], [907, 47, 672, 6], [907, 48, 672, 6, "jsxDEV"], [907, 54, 672, 6], [907, 56, 672, 7, "_Modal"], [907, 62, 672, 7], [907, 63, 672, 7, "default"], [907, 70, 672, 12], [908, 8, 673, 8, "visible"], [908, 15, 673, 15], [908, 17, 673, 17, "processingState"], [908, 32, 673, 32], [908, 37, 673, 37], [908, 43, 673, 43], [908, 47, 673, 47, "processingState"], [908, 62, 673, 62], [908, 67, 673, 67], [908, 74, 673, 75], [909, 8, 674, 8, "transparent"], [909, 19, 674, 19], [910, 8, 675, 8, "animationType"], [910, 21, 675, 21], [910, 23, 675, 22], [910, 29, 675, 28], [911, 8, 675, 28, "children"], [911, 16, 675, 28], [911, 31, 677, 8], [911, 35, 677, 8, "_jsxDevRuntime"], [911, 49, 677, 8], [911, 50, 677, 8, "jsxDEV"], [911, 56, 677, 8], [911, 58, 677, 9, "_View"], [911, 63, 677, 9], [911, 64, 677, 9, "default"], [911, 71, 677, 13], [912, 10, 677, 14, "style"], [912, 15, 677, 19], [912, 17, 677, 21, "styles"], [912, 23, 677, 27], [912, 24, 677, 28, "processingModal"], [912, 39, 677, 44], [913, 10, 677, 44, "children"], [913, 18, 677, 44], [913, 33, 678, 10], [913, 37, 678, 10, "_jsxDevRuntime"], [913, 51, 678, 10], [913, 52, 678, 10, "jsxDEV"], [913, 58, 678, 10], [913, 60, 678, 11, "_View"], [913, 65, 678, 11], [913, 66, 678, 11, "default"], [913, 73, 678, 15], [914, 12, 678, 16, "style"], [914, 17, 678, 21], [914, 19, 678, 23, "styles"], [914, 25, 678, 29], [914, 26, 678, 30, "processingContent"], [914, 43, 678, 48], [915, 12, 678, 48, "children"], [915, 20, 678, 48], [915, 36, 679, 12], [915, 40, 679, 12, "_jsxDevRuntime"], [915, 54, 679, 12], [915, 55, 679, 12, "jsxDEV"], [915, 61, 679, 12], [915, 63, 679, 13, "_ActivityIndicator"], [915, 81, 679, 13], [915, 82, 679, 13, "default"], [915, 89, 679, 30], [916, 14, 679, 31, "size"], [916, 18, 679, 35], [916, 20, 679, 36], [916, 27, 679, 43], [917, 14, 679, 44, "color"], [917, 19, 679, 49], [917, 21, 679, 50], [918, 12, 679, 59], [919, 14, 679, 59, "fileName"], [919, 22, 679, 59], [919, 24, 679, 59, "_jsxFileName"], [919, 36, 679, 59], [920, 14, 679, 59, "lineNumber"], [920, 24, 679, 59], [921, 14, 679, 59, "columnNumber"], [921, 26, 679, 59], [922, 12, 679, 59], [922, 19, 679, 61], [922, 20, 679, 62], [922, 35, 681, 12], [922, 39, 681, 12, "_jsxDevRuntime"], [922, 53, 681, 12], [922, 54, 681, 12, "jsxDEV"], [922, 60, 681, 12], [922, 62, 681, 13, "_Text"], [922, 67, 681, 13], [922, 68, 681, 13, "default"], [922, 75, 681, 17], [923, 14, 681, 18, "style"], [923, 19, 681, 23], [923, 21, 681, 25, "styles"], [923, 27, 681, 31], [923, 28, 681, 32, "processingTitle"], [923, 43, 681, 48], [924, 14, 681, 48, "children"], [924, 22, 681, 48], [924, 25, 682, 15, "processingState"], [924, 40, 682, 30], [924, 45, 682, 35], [924, 56, 682, 46], [924, 60, 682, 50], [924, 80, 682, 70], [924, 82, 683, 15, "processingState"], [924, 97, 683, 30], [924, 102, 683, 35], [924, 113, 683, 46], [924, 117, 683, 50], [924, 146, 683, 79], [924, 148, 684, 15, "processingState"], [924, 163, 684, 30], [924, 168, 684, 35], [924, 180, 684, 47], [924, 184, 684, 51], [924, 216, 684, 83], [924, 218, 685, 15, "processingState"], [924, 233, 685, 30], [924, 238, 685, 35], [924, 249, 685, 46], [924, 253, 685, 50], [924, 275, 685, 72], [925, 12, 685, 72], [926, 14, 685, 72, "fileName"], [926, 22, 685, 72], [926, 24, 685, 72, "_jsxFileName"], [926, 36, 685, 72], [927, 14, 685, 72, "lineNumber"], [927, 24, 685, 72], [928, 14, 685, 72, "columnNumber"], [928, 26, 685, 72], [929, 12, 685, 72], [929, 19, 686, 18], [929, 20, 686, 19], [929, 35, 687, 12], [929, 39, 687, 12, "_jsxDevRuntime"], [929, 53, 687, 12], [929, 54, 687, 12, "jsxDEV"], [929, 60, 687, 12], [929, 62, 687, 13, "_View"], [929, 67, 687, 13], [929, 68, 687, 13, "default"], [929, 75, 687, 17], [930, 14, 687, 18, "style"], [930, 19, 687, 23], [930, 21, 687, 25, "styles"], [930, 27, 687, 31], [930, 28, 687, 32, "progressBar"], [930, 39, 687, 44], [931, 14, 687, 44, "children"], [931, 22, 687, 44], [931, 37, 688, 14], [931, 41, 688, 14, "_jsxDevRuntime"], [931, 55, 688, 14], [931, 56, 688, 14, "jsxDEV"], [931, 62, 688, 14], [931, 64, 688, 15, "_View"], [931, 69, 688, 15], [931, 70, 688, 15, "default"], [931, 77, 688, 19], [932, 16, 689, 16, "style"], [932, 21, 689, 21], [932, 23, 689, 23], [932, 24, 690, 18, "styles"], [932, 30, 690, 24], [932, 31, 690, 25, "progressFill"], [932, 43, 690, 37], [932, 45, 691, 18], [933, 18, 691, 20, "width"], [933, 23, 691, 25], [933, 25, 691, 27], [933, 28, 691, 30, "processingProgress"], [933, 46, 691, 48], [934, 16, 691, 52], [934, 17, 691, 53], [935, 14, 692, 18], [936, 16, 692, 18, "fileName"], [936, 24, 692, 18], [936, 26, 692, 18, "_jsxFileName"], [936, 38, 692, 18], [937, 16, 692, 18, "lineNumber"], [937, 26, 692, 18], [938, 16, 692, 18, "columnNumber"], [938, 28, 692, 18], [939, 14, 692, 18], [939, 21, 693, 15], [940, 12, 693, 16], [941, 14, 693, 16, "fileName"], [941, 22, 693, 16], [941, 24, 693, 16, "_jsxFileName"], [941, 36, 693, 16], [942, 14, 693, 16, "lineNumber"], [942, 24, 693, 16], [943, 14, 693, 16, "columnNumber"], [943, 26, 693, 16], [944, 12, 693, 16], [944, 19, 694, 18], [944, 20, 694, 19], [944, 35, 695, 12], [944, 39, 695, 12, "_jsxDevRuntime"], [944, 53, 695, 12], [944, 54, 695, 12, "jsxDEV"], [944, 60, 695, 12], [944, 62, 695, 13, "_Text"], [944, 67, 695, 13], [944, 68, 695, 13, "default"], [944, 75, 695, 17], [945, 14, 695, 18, "style"], [945, 19, 695, 23], [945, 21, 695, 25, "styles"], [945, 27, 695, 31], [945, 28, 695, 32, "processingDescription"], [945, 49, 695, 54], [946, 14, 695, 54, "children"], [946, 22, 695, 54], [946, 25, 696, 15, "processingState"], [946, 40, 696, 30], [946, 45, 696, 35], [946, 56, 696, 46], [946, 60, 696, 50], [946, 89, 696, 79], [946, 91, 697, 15, "processingState"], [946, 106, 697, 30], [946, 111, 697, 35], [946, 122, 697, 46], [946, 126, 697, 50], [946, 164, 697, 88], [946, 166, 698, 15, "processingState"], [946, 181, 698, 30], [946, 186, 698, 35], [946, 198, 698, 47], [946, 202, 698, 51], [946, 247, 698, 96], [946, 249, 699, 15, "processingState"], [946, 264, 699, 30], [946, 269, 699, 35], [946, 280, 699, 46], [946, 284, 699, 50], [946, 325, 699, 91], [947, 12, 699, 91], [948, 14, 699, 91, "fileName"], [948, 22, 699, 91], [948, 24, 699, 91, "_jsxFileName"], [948, 36, 699, 91], [949, 14, 699, 91, "lineNumber"], [949, 24, 699, 91], [950, 14, 699, 91, "columnNumber"], [950, 26, 699, 91], [951, 12, 699, 91], [951, 19, 700, 18], [951, 20, 700, 19], [951, 22, 701, 13, "processingState"], [951, 37, 701, 28], [951, 42, 701, 33], [951, 53, 701, 44], [951, 70, 702, 14], [951, 74, 702, 14, "_jsxDevRuntime"], [951, 88, 702, 14], [951, 89, 702, 14, "jsxDEV"], [951, 95, 702, 14], [951, 97, 702, 15, "_lucideReactNative"], [951, 115, 702, 15], [951, 116, 702, 15, "CheckCircle"], [951, 127, 702, 26], [952, 14, 702, 27, "size"], [952, 18, 702, 31], [952, 20, 702, 33], [952, 22, 702, 36], [953, 14, 702, 37, "color"], [953, 19, 702, 42], [953, 21, 702, 43], [953, 30, 702, 52], [954, 14, 702, 53, "style"], [954, 19, 702, 58], [954, 21, 702, 60, "styles"], [954, 27, 702, 66], [954, 28, 702, 67, "successIcon"], [955, 12, 702, 79], [956, 14, 702, 79, "fileName"], [956, 22, 702, 79], [956, 24, 702, 79, "_jsxFileName"], [956, 36, 702, 79], [957, 14, 702, 79, "lineNumber"], [957, 24, 702, 79], [958, 14, 702, 79, "columnNumber"], [958, 26, 702, 79], [959, 12, 702, 79], [959, 19, 702, 81], [959, 20, 703, 13], [960, 10, 703, 13], [961, 12, 703, 13, "fileName"], [961, 20, 703, 13], [961, 22, 703, 13, "_jsxFileName"], [961, 34, 703, 13], [962, 12, 703, 13, "lineNumber"], [962, 22, 703, 13], [963, 12, 703, 13, "columnNumber"], [963, 24, 703, 13], [964, 10, 703, 13], [964, 17, 704, 16], [965, 8, 704, 17], [966, 10, 704, 17, "fileName"], [966, 18, 704, 17], [966, 20, 704, 17, "_jsxFileName"], [966, 32, 704, 17], [967, 10, 704, 17, "lineNumber"], [967, 20, 704, 17], [968, 10, 704, 17, "columnNumber"], [968, 22, 704, 17], [969, 8, 704, 17], [969, 15, 705, 14], [970, 6, 705, 15], [971, 8, 705, 15, "fileName"], [971, 16, 705, 15], [971, 18, 705, 15, "_jsxFileName"], [971, 30, 705, 15], [972, 8, 705, 15, "lineNumber"], [972, 18, 705, 15], [973, 8, 705, 15, "columnNumber"], [973, 20, 705, 15], [974, 6, 705, 15], [974, 13, 706, 13], [974, 14, 706, 14], [974, 29, 708, 6], [974, 33, 708, 6, "_jsxDevRuntime"], [974, 47, 708, 6], [974, 48, 708, 6, "jsxDEV"], [974, 54, 708, 6], [974, 56, 708, 7, "_Modal"], [974, 62, 708, 7], [974, 63, 708, 7, "default"], [974, 70, 708, 12], [975, 8, 709, 8, "visible"], [975, 15, 709, 15], [975, 17, 709, 17, "processingState"], [975, 32, 709, 32], [975, 37, 709, 37], [975, 44, 709, 45], [976, 8, 710, 8, "transparent"], [976, 19, 710, 19], [977, 8, 711, 8, "animationType"], [977, 21, 711, 21], [977, 23, 711, 22], [977, 29, 711, 28], [978, 8, 711, 28, "children"], [978, 16, 711, 28], [978, 31, 713, 8], [978, 35, 713, 8, "_jsxDevRuntime"], [978, 49, 713, 8], [978, 50, 713, 8, "jsxDEV"], [978, 56, 713, 8], [978, 58, 713, 9, "_View"], [978, 63, 713, 9], [978, 64, 713, 9, "default"], [978, 71, 713, 13], [979, 10, 713, 14, "style"], [979, 15, 713, 19], [979, 17, 713, 21, "styles"], [979, 23, 713, 27], [979, 24, 713, 28, "processingModal"], [979, 39, 713, 44], [980, 10, 713, 44, "children"], [980, 18, 713, 44], [980, 33, 714, 10], [980, 37, 714, 10, "_jsxDevRuntime"], [980, 51, 714, 10], [980, 52, 714, 10, "jsxDEV"], [980, 58, 714, 10], [980, 60, 714, 11, "_View"], [980, 65, 714, 11], [980, 66, 714, 11, "default"], [980, 73, 714, 15], [981, 12, 714, 16, "style"], [981, 17, 714, 21], [981, 19, 714, 23, "styles"], [981, 25, 714, 29], [981, 26, 714, 30, "errorContent"], [981, 38, 714, 43], [982, 12, 714, 43, "children"], [982, 20, 714, 43], [982, 36, 715, 12], [982, 40, 715, 12, "_jsxDevRuntime"], [982, 54, 715, 12], [982, 55, 715, 12, "jsxDEV"], [982, 61, 715, 12], [982, 63, 715, 13, "_lucideReactNative"], [982, 81, 715, 13], [982, 82, 715, 13, "X"], [982, 83, 715, 14], [983, 14, 715, 15, "size"], [983, 18, 715, 19], [983, 20, 715, 21], [983, 22, 715, 24], [984, 14, 715, 25, "color"], [984, 19, 715, 30], [984, 21, 715, 31], [985, 12, 715, 40], [986, 14, 715, 40, "fileName"], [986, 22, 715, 40], [986, 24, 715, 40, "_jsxFileName"], [986, 36, 715, 40], [987, 14, 715, 40, "lineNumber"], [987, 24, 715, 40], [988, 14, 715, 40, "columnNumber"], [988, 26, 715, 40], [989, 12, 715, 40], [989, 19, 715, 42], [989, 20, 715, 43], [989, 35, 716, 12], [989, 39, 716, 12, "_jsxDevRuntime"], [989, 53, 716, 12], [989, 54, 716, 12, "jsxDEV"], [989, 60, 716, 12], [989, 62, 716, 13, "_Text"], [989, 67, 716, 13], [989, 68, 716, 13, "default"], [989, 75, 716, 17], [990, 14, 716, 18, "style"], [990, 19, 716, 23], [990, 21, 716, 25, "styles"], [990, 27, 716, 31], [990, 28, 716, 32, "errorTitle"], [990, 38, 716, 43], [991, 14, 716, 43, "children"], [991, 22, 716, 43], [991, 24, 716, 44], [992, 12, 716, 61], [993, 14, 716, 61, "fileName"], [993, 22, 716, 61], [993, 24, 716, 61, "_jsxFileName"], [993, 36, 716, 61], [994, 14, 716, 61, "lineNumber"], [994, 24, 716, 61], [995, 14, 716, 61, "columnNumber"], [995, 26, 716, 61], [996, 12, 716, 61], [996, 19, 716, 67], [996, 20, 716, 68], [996, 35, 717, 12], [996, 39, 717, 12, "_jsxDevRuntime"], [996, 53, 717, 12], [996, 54, 717, 12, "jsxDEV"], [996, 60, 717, 12], [996, 62, 717, 13, "_Text"], [996, 67, 717, 13], [996, 68, 717, 13, "default"], [996, 75, 717, 17], [997, 14, 717, 18, "style"], [997, 19, 717, 23], [997, 21, 717, 25, "styles"], [997, 27, 717, 31], [997, 28, 717, 32, "errorMessage"], [997, 40, 717, 45], [998, 14, 717, 45, "children"], [998, 22, 717, 45], [998, 24, 717, 47, "errorMessage"], [999, 12, 717, 59], [1000, 14, 717, 59, "fileName"], [1000, 22, 717, 59], [1000, 24, 717, 59, "_jsxFileName"], [1000, 36, 717, 59], [1001, 14, 717, 59, "lineNumber"], [1001, 24, 717, 59], [1002, 14, 717, 59, "columnNumber"], [1002, 26, 717, 59], [1003, 12, 717, 59], [1003, 19, 717, 66], [1003, 20, 717, 67], [1003, 35, 718, 12], [1003, 39, 718, 12, "_jsxDevRuntime"], [1003, 53, 718, 12], [1003, 54, 718, 12, "jsxDEV"], [1003, 60, 718, 12], [1003, 62, 718, 13, "_TouchableOpacity"], [1003, 79, 718, 13], [1003, 80, 718, 13, "default"], [1003, 87, 718, 29], [1004, 14, 719, 14, "onPress"], [1004, 21, 719, 21], [1004, 23, 719, 23, "retryCapture"], [1004, 35, 719, 36], [1005, 14, 720, 14, "style"], [1005, 19, 720, 19], [1005, 21, 720, 21, "styles"], [1005, 27, 720, 27], [1005, 28, 720, 28, "primaryButton"], [1005, 41, 720, 42], [1006, 14, 720, 42, "children"], [1006, 22, 720, 42], [1006, 37, 722, 14], [1006, 41, 722, 14, "_jsxDevRuntime"], [1006, 55, 722, 14], [1006, 56, 722, 14, "jsxDEV"], [1006, 62, 722, 14], [1006, 64, 722, 15, "_Text"], [1006, 69, 722, 15], [1006, 70, 722, 15, "default"], [1006, 77, 722, 19], [1007, 16, 722, 20, "style"], [1007, 21, 722, 25], [1007, 23, 722, 27, "styles"], [1007, 29, 722, 33], [1007, 30, 722, 34, "primaryButtonText"], [1007, 47, 722, 52], [1008, 16, 722, 52, "children"], [1008, 24, 722, 52], [1008, 26, 722, 53], [1009, 14, 722, 62], [1010, 16, 722, 62, "fileName"], [1010, 24, 722, 62], [1010, 26, 722, 62, "_jsxFileName"], [1010, 38, 722, 62], [1011, 16, 722, 62, "lineNumber"], [1011, 26, 722, 62], [1012, 16, 722, 62, "columnNumber"], [1012, 28, 722, 62], [1013, 14, 722, 62], [1013, 21, 722, 68], [1014, 12, 722, 69], [1015, 14, 722, 69, "fileName"], [1015, 22, 722, 69], [1015, 24, 722, 69, "_jsxFileName"], [1015, 36, 722, 69], [1016, 14, 722, 69, "lineNumber"], [1016, 24, 722, 69], [1017, 14, 722, 69, "columnNumber"], [1017, 26, 722, 69], [1018, 12, 722, 69], [1018, 19, 723, 30], [1018, 20, 723, 31], [1018, 35, 724, 12], [1018, 39, 724, 12, "_jsxDevRuntime"], [1018, 53, 724, 12], [1018, 54, 724, 12, "jsxDEV"], [1018, 60, 724, 12], [1018, 62, 724, 13, "_TouchableOpacity"], [1018, 79, 724, 13], [1018, 80, 724, 13, "default"], [1018, 87, 724, 29], [1019, 14, 725, 14, "onPress"], [1019, 21, 725, 21], [1019, 23, 725, 23, "onCancel"], [1019, 31, 725, 32], [1020, 14, 726, 14, "style"], [1020, 19, 726, 19], [1020, 21, 726, 21, "styles"], [1020, 27, 726, 27], [1020, 28, 726, 28, "secondaryButton"], [1020, 43, 726, 44], [1021, 14, 726, 44, "children"], [1021, 22, 726, 44], [1021, 37, 728, 14], [1021, 41, 728, 14, "_jsxDevRuntime"], [1021, 55, 728, 14], [1021, 56, 728, 14, "jsxDEV"], [1021, 62, 728, 14], [1021, 64, 728, 15, "_Text"], [1021, 69, 728, 15], [1021, 70, 728, 15, "default"], [1021, 77, 728, 19], [1022, 16, 728, 20, "style"], [1022, 21, 728, 25], [1022, 23, 728, 27, "styles"], [1022, 29, 728, 33], [1022, 30, 728, 34, "secondaryButtonText"], [1022, 49, 728, 54], [1023, 16, 728, 54, "children"], [1023, 24, 728, 54], [1023, 26, 728, 55], [1024, 14, 728, 61], [1025, 16, 728, 61, "fileName"], [1025, 24, 728, 61], [1025, 26, 728, 61, "_jsxFileName"], [1025, 38, 728, 61], [1026, 16, 728, 61, "lineNumber"], [1026, 26, 728, 61], [1027, 16, 728, 61, "columnNumber"], [1027, 28, 728, 61], [1028, 14, 728, 61], [1028, 21, 728, 67], [1029, 12, 728, 68], [1030, 14, 728, 68, "fileName"], [1030, 22, 728, 68], [1030, 24, 728, 68, "_jsxFileName"], [1030, 36, 728, 68], [1031, 14, 728, 68, "lineNumber"], [1031, 24, 728, 68], [1032, 14, 728, 68, "columnNumber"], [1032, 26, 728, 68], [1033, 12, 728, 68], [1033, 19, 729, 30], [1033, 20, 729, 31], [1034, 10, 729, 31], [1035, 12, 729, 31, "fileName"], [1035, 20, 729, 31], [1035, 22, 729, 31, "_jsxFileName"], [1035, 34, 729, 31], [1036, 12, 729, 31, "lineNumber"], [1036, 22, 729, 31], [1037, 12, 729, 31, "columnNumber"], [1037, 24, 729, 31], [1038, 10, 729, 31], [1038, 17, 730, 16], [1039, 8, 730, 17], [1040, 10, 730, 17, "fileName"], [1040, 18, 730, 17], [1040, 20, 730, 17, "_jsxFileName"], [1040, 32, 730, 17], [1041, 10, 730, 17, "lineNumber"], [1041, 20, 730, 17], [1042, 10, 730, 17, "columnNumber"], [1042, 22, 730, 17], [1043, 8, 730, 17], [1043, 15, 731, 14], [1044, 6, 731, 15], [1045, 8, 731, 15, "fileName"], [1045, 16, 731, 15], [1045, 18, 731, 15, "_jsxFileName"], [1045, 30, 731, 15], [1046, 8, 731, 15, "lineNumber"], [1046, 18, 731, 15], [1047, 8, 731, 15, "columnNumber"], [1047, 20, 731, 15], [1048, 6, 731, 15], [1048, 13, 732, 13], [1048, 14, 732, 14], [1049, 4, 732, 14], [1050, 6, 732, 14, "fileName"], [1050, 14, 732, 14], [1050, 16, 732, 14, "_jsxFileName"], [1050, 28, 732, 14], [1051, 6, 732, 14, "lineNumber"], [1051, 16, 732, 14], [1052, 6, 732, 14, "columnNumber"], [1052, 18, 732, 14], [1053, 4, 732, 14], [1053, 11, 733, 10], [1053, 12, 733, 11], [1054, 2, 735, 0], [1055, 2, 735, 1, "_s"], [1055, 4, 735, 1], [1055, 5, 51, 24, "EchoCameraWeb"], [1055, 18, 51, 37], [1056, 4, 51, 37], [1056, 12, 58, 42, "useCameraPermissions"], [1056, 44, 58, 62], [1056, 46, 72, 19, "useUpload"], [1056, 64, 72, 28], [1057, 2, 72, 28], [1058, 2, 72, 28, "_c"], [1058, 4, 72, 28], [1058, 7, 51, 24, "EchoCameraWeb"], [1058, 20, 51, 37], [1059, 2, 736, 0], [1059, 8, 736, 6, "styles"], [1059, 14, 736, 12], [1059, 17, 736, 15, "StyleSheet"], [1059, 36, 736, 25], [1059, 37, 736, 26, "create"], [1059, 43, 736, 32], [1059, 44, 736, 33], [1060, 4, 737, 2, "container"], [1060, 13, 737, 11], [1060, 15, 737, 13], [1061, 6, 738, 4, "flex"], [1061, 10, 738, 8], [1061, 12, 738, 10], [1061, 13, 738, 11], [1062, 6, 739, 4, "backgroundColor"], [1062, 21, 739, 19], [1062, 23, 739, 21], [1063, 4, 740, 2], [1063, 5, 740, 3], [1064, 4, 741, 2, "cameraContainer"], [1064, 19, 741, 17], [1064, 21, 741, 19], [1065, 6, 742, 4, "flex"], [1065, 10, 742, 8], [1065, 12, 742, 10], [1065, 13, 742, 11], [1066, 6, 743, 4, "max<PERSON><PERSON><PERSON>"], [1066, 14, 743, 12], [1066, 16, 743, 14], [1066, 19, 743, 17], [1067, 6, 744, 4, "alignSelf"], [1067, 15, 744, 13], [1067, 17, 744, 15], [1067, 25, 744, 23], [1068, 6, 745, 4, "width"], [1068, 11, 745, 9], [1068, 13, 745, 11], [1069, 4, 746, 2], [1069, 5, 746, 3], [1070, 4, 747, 2, "camera"], [1070, 10, 747, 8], [1070, 12, 747, 10], [1071, 6, 748, 4, "flex"], [1071, 10, 748, 8], [1071, 12, 748, 10], [1072, 4, 749, 2], [1072, 5, 749, 3], [1073, 4, 750, 2, "headerOverlay"], [1073, 17, 750, 15], [1073, 19, 750, 17], [1074, 6, 751, 4, "position"], [1074, 14, 751, 12], [1074, 16, 751, 14], [1074, 26, 751, 24], [1075, 6, 752, 4, "top"], [1075, 9, 752, 7], [1075, 11, 752, 9], [1075, 12, 752, 10], [1076, 6, 753, 4, "left"], [1076, 10, 753, 8], [1076, 12, 753, 10], [1076, 13, 753, 11], [1077, 6, 754, 4, "right"], [1077, 11, 754, 9], [1077, 13, 754, 11], [1077, 14, 754, 12], [1078, 6, 755, 4, "backgroundColor"], [1078, 21, 755, 19], [1078, 23, 755, 21], [1078, 36, 755, 34], [1079, 6, 756, 4, "paddingTop"], [1079, 16, 756, 14], [1079, 18, 756, 16], [1079, 20, 756, 18], [1080, 6, 757, 4, "paddingHorizontal"], [1080, 23, 757, 21], [1080, 25, 757, 23], [1080, 27, 757, 25], [1081, 6, 758, 4, "paddingBottom"], [1081, 19, 758, 17], [1081, 21, 758, 19], [1082, 4, 759, 2], [1082, 5, 759, 3], [1083, 4, 760, 2, "headerContent"], [1083, 17, 760, 15], [1083, 19, 760, 17], [1084, 6, 761, 4, "flexDirection"], [1084, 19, 761, 17], [1084, 21, 761, 19], [1084, 26, 761, 24], [1085, 6, 762, 4, "justifyContent"], [1085, 20, 762, 18], [1085, 22, 762, 20], [1085, 37, 762, 35], [1086, 6, 763, 4, "alignItems"], [1086, 16, 763, 14], [1086, 18, 763, 16], [1087, 4, 764, 2], [1087, 5, 764, 3], [1088, 4, 765, 2, "headerLeft"], [1088, 14, 765, 12], [1088, 16, 765, 14], [1089, 6, 766, 4, "flex"], [1089, 10, 766, 8], [1089, 12, 766, 10], [1090, 4, 767, 2], [1090, 5, 767, 3], [1091, 4, 768, 2, "headerTitle"], [1091, 15, 768, 13], [1091, 17, 768, 15], [1092, 6, 769, 4, "fontSize"], [1092, 14, 769, 12], [1092, 16, 769, 14], [1092, 18, 769, 16], [1093, 6, 770, 4, "fontWeight"], [1093, 16, 770, 14], [1093, 18, 770, 16], [1093, 23, 770, 21], [1094, 6, 771, 4, "color"], [1094, 11, 771, 9], [1094, 13, 771, 11], [1094, 19, 771, 17], [1095, 6, 772, 4, "marginBottom"], [1095, 18, 772, 16], [1095, 20, 772, 18], [1096, 4, 773, 2], [1096, 5, 773, 3], [1097, 4, 774, 2, "subtitleRow"], [1097, 15, 774, 13], [1097, 17, 774, 15], [1098, 6, 775, 4, "flexDirection"], [1098, 19, 775, 17], [1098, 21, 775, 19], [1098, 26, 775, 24], [1099, 6, 776, 4, "alignItems"], [1099, 16, 776, 14], [1099, 18, 776, 16], [1099, 26, 776, 24], [1100, 6, 777, 4, "marginBottom"], [1100, 18, 777, 16], [1100, 20, 777, 18], [1101, 4, 778, 2], [1101, 5, 778, 3], [1102, 4, 779, 2, "webIcon"], [1102, 11, 779, 9], [1102, 13, 779, 11], [1103, 6, 780, 4, "fontSize"], [1103, 14, 780, 12], [1103, 16, 780, 14], [1103, 18, 780, 16], [1104, 6, 781, 4, "marginRight"], [1104, 17, 781, 15], [1104, 19, 781, 17], [1105, 4, 782, 2], [1105, 5, 782, 3], [1106, 4, 783, 2, "headerSubtitle"], [1106, 18, 783, 16], [1106, 20, 783, 18], [1107, 6, 784, 4, "fontSize"], [1107, 14, 784, 12], [1107, 16, 784, 14], [1107, 18, 784, 16], [1108, 6, 785, 4, "color"], [1108, 11, 785, 9], [1108, 13, 785, 11], [1108, 19, 785, 17], [1109, 6, 786, 4, "opacity"], [1109, 13, 786, 11], [1109, 15, 786, 13], [1110, 4, 787, 2], [1110, 5, 787, 3], [1111, 4, 788, 2, "challengeRow"], [1111, 16, 788, 14], [1111, 18, 788, 16], [1112, 6, 789, 4, "flexDirection"], [1112, 19, 789, 17], [1112, 21, 789, 19], [1112, 26, 789, 24], [1113, 6, 790, 4, "alignItems"], [1113, 16, 790, 14], [1113, 18, 790, 16], [1114, 4, 791, 2], [1114, 5, 791, 3], [1115, 4, 792, 2, "challengeCode"], [1115, 17, 792, 15], [1115, 19, 792, 17], [1116, 6, 793, 4, "fontSize"], [1116, 14, 793, 12], [1116, 16, 793, 14], [1116, 18, 793, 16], [1117, 6, 794, 4, "color"], [1117, 11, 794, 9], [1117, 13, 794, 11], [1117, 19, 794, 17], [1118, 6, 795, 4, "marginLeft"], [1118, 16, 795, 14], [1118, 18, 795, 16], [1118, 19, 795, 17], [1119, 6, 796, 4, "fontFamily"], [1119, 16, 796, 14], [1119, 18, 796, 16], [1120, 4, 797, 2], [1120, 5, 797, 3], [1121, 4, 798, 2, "closeButton"], [1121, 15, 798, 13], [1121, 17, 798, 15], [1122, 6, 799, 4, "padding"], [1122, 13, 799, 11], [1122, 15, 799, 13], [1123, 4, 800, 2], [1123, 5, 800, 3], [1124, 4, 801, 2, "privacyNotice"], [1124, 17, 801, 15], [1124, 19, 801, 17], [1125, 6, 802, 4, "position"], [1125, 14, 802, 12], [1125, 16, 802, 14], [1125, 26, 802, 24], [1126, 6, 803, 4, "top"], [1126, 9, 803, 7], [1126, 11, 803, 9], [1126, 14, 803, 12], [1127, 6, 804, 4, "left"], [1127, 10, 804, 8], [1127, 12, 804, 10], [1127, 14, 804, 12], [1128, 6, 805, 4, "right"], [1128, 11, 805, 9], [1128, 13, 805, 11], [1128, 15, 805, 13], [1129, 6, 806, 4, "backgroundColor"], [1129, 21, 806, 19], [1129, 23, 806, 21], [1129, 48, 806, 46], [1130, 6, 807, 4, "borderRadius"], [1130, 18, 807, 16], [1130, 20, 807, 18], [1130, 21, 807, 19], [1131, 6, 808, 4, "padding"], [1131, 13, 808, 11], [1131, 15, 808, 13], [1131, 17, 808, 15], [1132, 6, 809, 4, "flexDirection"], [1132, 19, 809, 17], [1132, 21, 809, 19], [1132, 26, 809, 24], [1133, 6, 810, 4, "alignItems"], [1133, 16, 810, 14], [1133, 18, 810, 16], [1134, 4, 811, 2], [1134, 5, 811, 3], [1135, 4, 812, 2, "privacyText"], [1135, 15, 812, 13], [1135, 17, 812, 15], [1136, 6, 813, 4, "color"], [1136, 11, 813, 9], [1136, 13, 813, 11], [1136, 19, 813, 17], [1137, 6, 814, 4, "fontSize"], [1137, 14, 814, 12], [1137, 16, 814, 14], [1137, 18, 814, 16], [1138, 6, 815, 4, "marginLeft"], [1138, 16, 815, 14], [1138, 18, 815, 16], [1138, 19, 815, 17], [1139, 6, 816, 4, "flex"], [1139, 10, 816, 8], [1139, 12, 816, 10], [1140, 4, 817, 2], [1140, 5, 817, 3], [1141, 4, 818, 2, "footer<PERSON><PERSON><PERSON>"], [1141, 17, 818, 15], [1141, 19, 818, 17], [1142, 6, 819, 4, "position"], [1142, 14, 819, 12], [1142, 16, 819, 14], [1142, 26, 819, 24], [1143, 6, 820, 4, "bottom"], [1143, 12, 820, 10], [1143, 14, 820, 12], [1143, 15, 820, 13], [1144, 6, 821, 4, "left"], [1144, 10, 821, 8], [1144, 12, 821, 10], [1144, 13, 821, 11], [1145, 6, 822, 4, "right"], [1145, 11, 822, 9], [1145, 13, 822, 11], [1145, 14, 822, 12], [1146, 6, 823, 4, "backgroundColor"], [1146, 21, 823, 19], [1146, 23, 823, 21], [1146, 36, 823, 34], [1147, 6, 824, 4, "paddingBottom"], [1147, 19, 824, 17], [1147, 21, 824, 19], [1147, 23, 824, 21], [1148, 6, 825, 4, "paddingTop"], [1148, 16, 825, 14], [1148, 18, 825, 16], [1148, 20, 825, 18], [1149, 6, 826, 4, "alignItems"], [1149, 16, 826, 14], [1149, 18, 826, 16], [1150, 4, 827, 2], [1150, 5, 827, 3], [1151, 4, 828, 2, "instruction"], [1151, 15, 828, 13], [1151, 17, 828, 15], [1152, 6, 829, 4, "fontSize"], [1152, 14, 829, 12], [1152, 16, 829, 14], [1152, 18, 829, 16], [1153, 6, 830, 4, "color"], [1153, 11, 830, 9], [1153, 13, 830, 11], [1153, 19, 830, 17], [1154, 6, 831, 4, "marginBottom"], [1154, 18, 831, 16], [1154, 20, 831, 18], [1155, 4, 832, 2], [1155, 5, 832, 3], [1156, 4, 833, 2, "shutterButton"], [1156, 17, 833, 15], [1156, 19, 833, 17], [1157, 6, 834, 4, "width"], [1157, 11, 834, 9], [1157, 13, 834, 11], [1157, 15, 834, 13], [1158, 6, 835, 4, "height"], [1158, 12, 835, 10], [1158, 14, 835, 12], [1158, 16, 835, 14], [1159, 6, 836, 4, "borderRadius"], [1159, 18, 836, 16], [1159, 20, 836, 18], [1159, 22, 836, 20], [1160, 6, 837, 4, "backgroundColor"], [1160, 21, 837, 19], [1160, 23, 837, 21], [1160, 29, 837, 27], [1161, 6, 838, 4, "justifyContent"], [1161, 20, 838, 18], [1161, 22, 838, 20], [1161, 30, 838, 28], [1162, 6, 839, 4, "alignItems"], [1162, 16, 839, 14], [1162, 18, 839, 16], [1162, 26, 839, 24], [1163, 6, 840, 4, "marginBottom"], [1163, 18, 840, 16], [1163, 20, 840, 18], [1163, 22, 840, 20], [1164, 6, 841, 4], [1164, 9, 841, 7, "Platform"], [1164, 26, 841, 15], [1164, 27, 841, 16, "select"], [1164, 33, 841, 22], [1164, 34, 841, 23], [1165, 8, 842, 6, "ios"], [1165, 11, 842, 9], [1165, 13, 842, 11], [1166, 10, 843, 8, "shadowColor"], [1166, 21, 843, 19], [1166, 23, 843, 21], [1166, 32, 843, 30], [1167, 10, 844, 8, "shadowOffset"], [1167, 22, 844, 20], [1167, 24, 844, 22], [1168, 12, 844, 24, "width"], [1168, 17, 844, 29], [1168, 19, 844, 31], [1168, 20, 844, 32], [1169, 12, 844, 34, "height"], [1169, 18, 844, 40], [1169, 20, 844, 42], [1170, 10, 844, 44], [1170, 11, 844, 45], [1171, 10, 845, 8, "shadowOpacity"], [1171, 23, 845, 21], [1171, 25, 845, 23], [1171, 28, 845, 26], [1172, 10, 846, 8, "shadowRadius"], [1172, 22, 846, 20], [1172, 24, 846, 22], [1173, 8, 847, 6], [1173, 9, 847, 7], [1174, 8, 848, 6, "android"], [1174, 15, 848, 13], [1174, 17, 848, 15], [1175, 10, 849, 8, "elevation"], [1175, 19, 849, 17], [1175, 21, 849, 19], [1176, 8, 850, 6], [1176, 9, 850, 7], [1177, 8, 851, 6, "web"], [1177, 11, 851, 9], [1177, 13, 851, 11], [1178, 10, 852, 8, "boxShadow"], [1178, 19, 852, 17], [1178, 21, 852, 19], [1179, 8, 853, 6], [1180, 6, 854, 4], [1180, 7, 854, 5], [1181, 4, 855, 2], [1181, 5, 855, 3], [1182, 4, 856, 2, "shutterButtonDisabled"], [1182, 25, 856, 23], [1182, 27, 856, 25], [1183, 6, 857, 4, "opacity"], [1183, 13, 857, 11], [1183, 15, 857, 13], [1184, 4, 858, 2], [1184, 5, 858, 3], [1185, 4, 859, 2, "shutterInner"], [1185, 16, 859, 14], [1185, 18, 859, 16], [1186, 6, 860, 4, "width"], [1186, 11, 860, 9], [1186, 13, 860, 11], [1186, 15, 860, 13], [1187, 6, 861, 4, "height"], [1187, 12, 861, 10], [1187, 14, 861, 12], [1187, 16, 861, 14], [1188, 6, 862, 4, "borderRadius"], [1188, 18, 862, 16], [1188, 20, 862, 18], [1188, 22, 862, 20], [1189, 6, 863, 4, "backgroundColor"], [1189, 21, 863, 19], [1189, 23, 863, 21], [1189, 29, 863, 27], [1190, 6, 864, 4, "borderWidth"], [1190, 17, 864, 15], [1190, 19, 864, 17], [1190, 20, 864, 18], [1191, 6, 865, 4, "borderColor"], [1191, 17, 865, 15], [1191, 19, 865, 17], [1192, 4, 866, 2], [1192, 5, 866, 3], [1193, 4, 867, 2, "privacyNote"], [1193, 15, 867, 13], [1193, 17, 867, 15], [1194, 6, 868, 4, "fontSize"], [1194, 14, 868, 12], [1194, 16, 868, 14], [1194, 18, 868, 16], [1195, 6, 869, 4, "color"], [1195, 11, 869, 9], [1195, 13, 869, 11], [1196, 4, 870, 2], [1196, 5, 870, 3], [1197, 4, 871, 2, "processingModal"], [1197, 19, 871, 17], [1197, 21, 871, 19], [1198, 6, 872, 4, "flex"], [1198, 10, 872, 8], [1198, 12, 872, 10], [1198, 13, 872, 11], [1199, 6, 873, 4, "backgroundColor"], [1199, 21, 873, 19], [1199, 23, 873, 21], [1199, 43, 873, 41], [1200, 6, 874, 4, "justifyContent"], [1200, 20, 874, 18], [1200, 22, 874, 20], [1200, 30, 874, 28], [1201, 6, 875, 4, "alignItems"], [1201, 16, 875, 14], [1201, 18, 875, 16], [1202, 4, 876, 2], [1202, 5, 876, 3], [1203, 4, 877, 2, "processingContent"], [1203, 21, 877, 19], [1203, 23, 877, 21], [1204, 6, 878, 4, "backgroundColor"], [1204, 21, 878, 19], [1204, 23, 878, 21], [1204, 29, 878, 27], [1205, 6, 879, 4, "borderRadius"], [1205, 18, 879, 16], [1205, 20, 879, 18], [1205, 22, 879, 20], [1206, 6, 880, 4, "padding"], [1206, 13, 880, 11], [1206, 15, 880, 13], [1206, 17, 880, 15], [1207, 6, 881, 4, "width"], [1207, 11, 881, 9], [1207, 13, 881, 11], [1207, 18, 881, 16], [1208, 6, 882, 4, "max<PERSON><PERSON><PERSON>"], [1208, 14, 882, 12], [1208, 16, 882, 14], [1208, 19, 882, 17], [1209, 6, 883, 4, "alignItems"], [1209, 16, 883, 14], [1209, 18, 883, 16], [1210, 4, 884, 2], [1210, 5, 884, 3], [1211, 4, 885, 2, "processingTitle"], [1211, 19, 885, 17], [1211, 21, 885, 19], [1212, 6, 886, 4, "fontSize"], [1212, 14, 886, 12], [1212, 16, 886, 14], [1212, 18, 886, 16], [1213, 6, 887, 4, "fontWeight"], [1213, 16, 887, 14], [1213, 18, 887, 16], [1213, 23, 887, 21], [1214, 6, 888, 4, "color"], [1214, 11, 888, 9], [1214, 13, 888, 11], [1214, 22, 888, 20], [1215, 6, 889, 4, "marginTop"], [1215, 15, 889, 13], [1215, 17, 889, 15], [1215, 19, 889, 17], [1216, 6, 890, 4, "marginBottom"], [1216, 18, 890, 16], [1216, 20, 890, 18], [1217, 4, 891, 2], [1217, 5, 891, 3], [1218, 4, 892, 2, "progressBar"], [1218, 15, 892, 13], [1218, 17, 892, 15], [1219, 6, 893, 4, "width"], [1219, 11, 893, 9], [1219, 13, 893, 11], [1219, 19, 893, 17], [1220, 6, 894, 4, "height"], [1220, 12, 894, 10], [1220, 14, 894, 12], [1220, 15, 894, 13], [1221, 6, 895, 4, "backgroundColor"], [1221, 21, 895, 19], [1221, 23, 895, 21], [1221, 32, 895, 30], [1222, 6, 896, 4, "borderRadius"], [1222, 18, 896, 16], [1222, 20, 896, 18], [1222, 21, 896, 19], [1223, 6, 897, 4, "overflow"], [1223, 14, 897, 12], [1223, 16, 897, 14], [1223, 24, 897, 22], [1224, 6, 898, 4, "marginBottom"], [1224, 18, 898, 16], [1224, 20, 898, 18], [1225, 4, 899, 2], [1225, 5, 899, 3], [1226, 4, 900, 2, "progressFill"], [1226, 16, 900, 14], [1226, 18, 900, 16], [1227, 6, 901, 4, "height"], [1227, 12, 901, 10], [1227, 14, 901, 12], [1227, 20, 901, 18], [1228, 6, 902, 4, "backgroundColor"], [1228, 21, 902, 19], [1228, 23, 902, 21], [1228, 32, 902, 30], [1229, 6, 903, 4, "borderRadius"], [1229, 18, 903, 16], [1229, 20, 903, 18], [1230, 4, 904, 2], [1230, 5, 904, 3], [1231, 4, 905, 2, "processingDescription"], [1231, 25, 905, 23], [1231, 27, 905, 25], [1232, 6, 906, 4, "fontSize"], [1232, 14, 906, 12], [1232, 16, 906, 14], [1232, 18, 906, 16], [1233, 6, 907, 4, "color"], [1233, 11, 907, 9], [1233, 13, 907, 11], [1233, 22, 907, 20], [1234, 6, 908, 4, "textAlign"], [1234, 15, 908, 13], [1234, 17, 908, 15], [1235, 4, 909, 2], [1235, 5, 909, 3], [1236, 4, 910, 2, "successIcon"], [1236, 15, 910, 13], [1236, 17, 910, 15], [1237, 6, 911, 4, "marginTop"], [1237, 15, 911, 13], [1237, 17, 911, 15], [1238, 4, 912, 2], [1238, 5, 912, 3], [1239, 4, 913, 2, "errorContent"], [1239, 16, 913, 14], [1239, 18, 913, 16], [1240, 6, 914, 4, "backgroundColor"], [1240, 21, 914, 19], [1240, 23, 914, 21], [1240, 29, 914, 27], [1241, 6, 915, 4, "borderRadius"], [1241, 18, 915, 16], [1241, 20, 915, 18], [1241, 22, 915, 20], [1242, 6, 916, 4, "padding"], [1242, 13, 916, 11], [1242, 15, 916, 13], [1242, 17, 916, 15], [1243, 6, 917, 4, "width"], [1243, 11, 917, 9], [1243, 13, 917, 11], [1243, 18, 917, 16], [1244, 6, 918, 4, "max<PERSON><PERSON><PERSON>"], [1244, 14, 918, 12], [1244, 16, 918, 14], [1244, 19, 918, 17], [1245, 6, 919, 4, "alignItems"], [1245, 16, 919, 14], [1245, 18, 919, 16], [1246, 4, 920, 2], [1246, 5, 920, 3], [1247, 4, 921, 2, "errorTitle"], [1247, 14, 921, 12], [1247, 16, 921, 14], [1248, 6, 922, 4, "fontSize"], [1248, 14, 922, 12], [1248, 16, 922, 14], [1248, 18, 922, 16], [1249, 6, 923, 4, "fontWeight"], [1249, 16, 923, 14], [1249, 18, 923, 16], [1249, 23, 923, 21], [1250, 6, 924, 4, "color"], [1250, 11, 924, 9], [1250, 13, 924, 11], [1250, 22, 924, 20], [1251, 6, 925, 4, "marginTop"], [1251, 15, 925, 13], [1251, 17, 925, 15], [1251, 19, 925, 17], [1252, 6, 926, 4, "marginBottom"], [1252, 18, 926, 16], [1252, 20, 926, 18], [1253, 4, 927, 2], [1253, 5, 927, 3], [1254, 4, 928, 2, "errorMessage"], [1254, 16, 928, 14], [1254, 18, 928, 16], [1255, 6, 929, 4, "fontSize"], [1255, 14, 929, 12], [1255, 16, 929, 14], [1255, 18, 929, 16], [1256, 6, 930, 4, "color"], [1256, 11, 930, 9], [1256, 13, 930, 11], [1256, 22, 930, 20], [1257, 6, 931, 4, "textAlign"], [1257, 15, 931, 13], [1257, 17, 931, 15], [1257, 25, 931, 23], [1258, 6, 932, 4, "marginBottom"], [1258, 18, 932, 16], [1258, 20, 932, 18], [1259, 4, 933, 2], [1259, 5, 933, 3], [1260, 4, 934, 2, "primaryButton"], [1260, 17, 934, 15], [1260, 19, 934, 17], [1261, 6, 935, 4, "backgroundColor"], [1261, 21, 935, 19], [1261, 23, 935, 21], [1261, 32, 935, 30], [1262, 6, 936, 4, "paddingHorizontal"], [1262, 23, 936, 21], [1262, 25, 936, 23], [1262, 27, 936, 25], [1263, 6, 937, 4, "paddingVertical"], [1263, 21, 937, 19], [1263, 23, 937, 21], [1263, 25, 937, 23], [1264, 6, 938, 4, "borderRadius"], [1264, 18, 938, 16], [1264, 20, 938, 18], [1264, 21, 938, 19], [1265, 6, 939, 4, "marginTop"], [1265, 15, 939, 13], [1265, 17, 939, 15], [1266, 4, 940, 2], [1266, 5, 940, 3], [1267, 4, 941, 2, "primaryButtonText"], [1267, 21, 941, 19], [1267, 23, 941, 21], [1268, 6, 942, 4, "color"], [1268, 11, 942, 9], [1268, 13, 942, 11], [1268, 19, 942, 17], [1269, 6, 943, 4, "fontSize"], [1269, 14, 943, 12], [1269, 16, 943, 14], [1269, 18, 943, 16], [1270, 6, 944, 4, "fontWeight"], [1270, 16, 944, 14], [1270, 18, 944, 16], [1271, 4, 945, 2], [1271, 5, 945, 3], [1272, 4, 946, 2, "secondaryButton"], [1272, 19, 946, 17], [1272, 21, 946, 19], [1273, 6, 947, 4, "paddingHorizontal"], [1273, 23, 947, 21], [1273, 25, 947, 23], [1273, 27, 947, 25], [1274, 6, 948, 4, "paddingVertical"], [1274, 21, 948, 19], [1274, 23, 948, 21], [1274, 25, 948, 23], [1275, 6, 949, 4, "marginTop"], [1275, 15, 949, 13], [1275, 17, 949, 15], [1276, 4, 950, 2], [1276, 5, 950, 3], [1277, 4, 951, 2, "secondaryButtonText"], [1277, 23, 951, 21], [1277, 25, 951, 23], [1278, 6, 952, 4, "color"], [1278, 11, 952, 9], [1278, 13, 952, 11], [1278, 22, 952, 20], [1279, 6, 953, 4, "fontSize"], [1279, 14, 953, 12], [1279, 16, 953, 14], [1280, 4, 954, 2], [1280, 5, 954, 3], [1281, 4, 955, 2, "permissionContent"], [1281, 21, 955, 19], [1281, 23, 955, 21], [1282, 6, 956, 4, "flex"], [1282, 10, 956, 8], [1282, 12, 956, 10], [1282, 13, 956, 11], [1283, 6, 957, 4, "justifyContent"], [1283, 20, 957, 18], [1283, 22, 957, 20], [1283, 30, 957, 28], [1284, 6, 958, 4, "alignItems"], [1284, 16, 958, 14], [1284, 18, 958, 16], [1284, 26, 958, 24], [1285, 6, 959, 4, "padding"], [1285, 13, 959, 11], [1285, 15, 959, 13], [1286, 4, 960, 2], [1286, 5, 960, 3], [1287, 4, 961, 2, "permissionTitle"], [1287, 19, 961, 17], [1287, 21, 961, 19], [1288, 6, 962, 4, "fontSize"], [1288, 14, 962, 12], [1288, 16, 962, 14], [1288, 18, 962, 16], [1289, 6, 963, 4, "fontWeight"], [1289, 16, 963, 14], [1289, 18, 963, 16], [1289, 23, 963, 21], [1290, 6, 964, 4, "color"], [1290, 11, 964, 9], [1290, 13, 964, 11], [1290, 22, 964, 20], [1291, 6, 965, 4, "marginTop"], [1291, 15, 965, 13], [1291, 17, 965, 15], [1291, 19, 965, 17], [1292, 6, 966, 4, "marginBottom"], [1292, 18, 966, 16], [1292, 20, 966, 18], [1293, 4, 967, 2], [1293, 5, 967, 3], [1294, 4, 968, 2, "permissionDescription"], [1294, 25, 968, 23], [1294, 27, 968, 25], [1295, 6, 969, 4, "fontSize"], [1295, 14, 969, 12], [1295, 16, 969, 14], [1295, 18, 969, 16], [1296, 6, 970, 4, "color"], [1296, 11, 970, 9], [1296, 13, 970, 11], [1296, 22, 970, 20], [1297, 6, 971, 4, "textAlign"], [1297, 15, 971, 13], [1297, 17, 971, 15], [1297, 25, 971, 23], [1298, 6, 972, 4, "marginBottom"], [1298, 18, 972, 16], [1298, 20, 972, 18], [1299, 4, 973, 2], [1299, 5, 973, 3], [1300, 4, 974, 2, "loadingText"], [1300, 15, 974, 13], [1300, 17, 974, 15], [1301, 6, 975, 4, "color"], [1301, 11, 975, 9], [1301, 13, 975, 11], [1301, 22, 975, 20], [1302, 6, 976, 4, "marginTop"], [1302, 15, 976, 13], [1302, 17, 976, 15], [1303, 4, 977, 2], [1303, 5, 977, 3], [1304, 4, 978, 2], [1305, 4, 979, 2, "blurZone"], [1305, 12, 979, 10], [1305, 14, 979, 12], [1306, 6, 980, 4, "position"], [1306, 14, 980, 12], [1306, 16, 980, 14], [1306, 26, 980, 24], [1307, 6, 981, 4, "overflow"], [1307, 14, 981, 12], [1307, 16, 981, 14], [1308, 4, 982, 2], [1308, 5, 982, 3], [1309, 4, 983, 2, "previewChip"], [1309, 15, 983, 13], [1309, 17, 983, 15], [1310, 6, 984, 4, "position"], [1310, 14, 984, 12], [1310, 16, 984, 14], [1310, 26, 984, 24], [1311, 6, 985, 4, "top"], [1311, 9, 985, 7], [1311, 11, 985, 9], [1311, 12, 985, 10], [1312, 6, 986, 4, "right"], [1312, 11, 986, 9], [1312, 13, 986, 11], [1312, 14, 986, 12], [1313, 6, 987, 4, "backgroundColor"], [1313, 21, 987, 19], [1313, 23, 987, 21], [1313, 40, 987, 38], [1314, 6, 988, 4, "paddingHorizontal"], [1314, 23, 988, 21], [1314, 25, 988, 23], [1314, 27, 988, 25], [1315, 6, 989, 4, "paddingVertical"], [1315, 21, 989, 19], [1315, 23, 989, 21], [1315, 24, 989, 22], [1316, 6, 990, 4, "borderRadius"], [1316, 18, 990, 16], [1316, 20, 990, 18], [1317, 4, 991, 2], [1317, 5, 991, 3], [1318, 4, 992, 2, "previewChipText"], [1318, 19, 992, 17], [1318, 21, 992, 19], [1319, 6, 993, 4, "color"], [1319, 11, 993, 9], [1319, 13, 993, 11], [1319, 19, 993, 17], [1320, 6, 994, 4, "fontSize"], [1320, 14, 994, 12], [1320, 16, 994, 14], [1320, 18, 994, 16], [1321, 6, 995, 4, "fontWeight"], [1321, 16, 995, 14], [1321, 18, 995, 16], [1322, 4, 996, 2], [1323, 2, 997, 0], [1323, 3, 997, 1], [1323, 4, 997, 2], [1324, 2, 997, 3], [1324, 6, 997, 3, "_c"], [1324, 8, 997, 3], [1325, 2, 997, 3, "$RefreshReg$"], [1325, 14, 997, 3], [1325, 15, 997, 3, "_c"], [1325, 17, 997, 3], [1326, 0, 997, 3], [1326, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHkC;mCKE;wBDY;OCI;gDC8B;YDO;8BDa;aCM;6CEc;YFO;oFGW;UHM;8BIS;SJgD;uDDQ;sBMC,wBN;OCC;GLc;6BWG;GXyB;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}