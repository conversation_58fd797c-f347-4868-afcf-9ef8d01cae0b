{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SkiaViewApi = void 0;\n  const {\n    SkiaViewApi\n  } = global;\n  exports.SkiaViewApi = SkiaViewApi;\n});", "lineCount": 10, "map": [[6, 2, 1, 7], [6, 8, 1, 13], [7, 4, 2, 2, "SkiaViewApi"], [8, 2, 3, 0], [8, 3, 3, 1], [8, 6, 3, 4, "global"], [8, 12, 3, 10], [9, 2, 3, 11, "exports"], [9, 9, 3, 11], [9, 10, 3, 11, "SkiaViewApi"], [9, 21, 3, 11], [9, 24, 3, 11, "SkiaViewApi"], [9, 35, 3, 11], [10, 0, 3, 11], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}