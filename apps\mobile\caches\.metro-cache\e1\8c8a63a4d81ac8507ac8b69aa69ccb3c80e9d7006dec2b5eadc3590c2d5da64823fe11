{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.downloadAsync = downloadAsync;\n  async function downloadAsync(url, _hash, _type) {\n    return url;\n  }\n});", "lineCount": 9, "map": [[6, 2, 1, 7], [6, 17, 1, 22, "downloadAsync"], [6, 30, 1, 35, "downloadAsync"], [6, 31, 1, 36, "url"], [6, 34, 1, 39], [6, 36, 1, 41, "_hash"], [6, 41, 1, 46], [6, 43, 1, 48, "_type"], [6, 48, 1, 53], [6, 50, 1, 55], [7, 4, 2, 4], [7, 11, 2, 11, "url"], [7, 14, 2, 14], [8, 2, 3, 0], [9, 0, 3, 1], [9, 3]], "functionMap": {"names": ["<global>", "downloadAsync"], "mappings": "AAA,OC;CDE"}}, "type": "js/module"}]}