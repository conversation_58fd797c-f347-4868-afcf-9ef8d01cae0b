{"dependencies": [{"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}, {"name": "./Data", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 36, "index": 68}}], "key": "0fS75tjqpJdM3ThONBfvzOQKEI0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useTypeface = void 0;\n  var _Skia = require(_dependencyMap[0], \"../Skia\");\n  var _Data = require(_dependencyMap[1], \"./Data\");\n  const tfFactory = _Skia.Skia.Typeface.MakeFreeTypeFaceFromData.bind(_Skia.Skia.Typeface);\n\n  /**\n   * Returns a Skia Typeface object\n   * */\n  const useTypeface = (source, onError) => (0, _Data.useRawData)(source, tfFactory, onError);\n  exports.useTypeface = useTypeface;\n});", "lineCount": 15, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Skia"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Data"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 8, 3, 6, "tfFactory"], [8, 17, 3, 15], [8, 20, 3, 18, "Skia"], [8, 30, 3, 22], [8, 31, 3, 23, "Typeface"], [8, 39, 3, 31], [8, 40, 3, 32, "MakeFreeTypeFaceFromData"], [8, 64, 3, 56], [8, 65, 3, 57, "bind"], [8, 69, 3, 61], [8, 70, 3, 62, "Skia"], [8, 80, 3, 66], [8, 81, 3, 67, "Typeface"], [8, 89, 3, 75], [8, 90, 3, 76], [10, 2, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 2, 8, 7], [13, 8, 8, 13, "useTypeface"], [13, 19, 8, 24], [13, 22, 8, 27, "useTypeface"], [13, 23, 8, 28, "source"], [13, 29, 8, 34], [13, 31, 8, 36, "onError"], [13, 38, 8, 43], [13, 43, 8, 48], [13, 47, 8, 48, "useRawData"], [13, 63, 8, 58], [13, 65, 8, 59, "source"], [13, 71, 8, 65], [13, 73, 8, 67, "tfFactory"], [13, 82, 8, 76], [13, 84, 8, 78, "onError"], [13, 91, 8, 85], [13, 92, 8, 86], [14, 2, 8, 87, "exports"], [14, 9, 8, 87], [14, 10, 8, 87, "useTypeface"], [14, 21, 8, 87], [14, 24, 8, 87, "useTypeface"], [14, 35, 8, 87], [15, 0, 8, 87], [15, 3]], "functionMap": {"names": ["<global>", "useTypeface"], "mappings": "AAA;2BCO,2DD"}}, "type": "js/module"}]}