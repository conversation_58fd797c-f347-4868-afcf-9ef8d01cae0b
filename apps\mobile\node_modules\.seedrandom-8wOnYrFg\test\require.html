<!DOCTYPE html>
<html>
<head>
<link rel="stylesheet" href="lib/qunit.css">
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<script src="lib/require.js"></script>

<script>

require.config({
  paths: {
    'qunit': 'lib/qunit',
    'seedrandom': '/seedrandom.min'
  }
});

require(['qunit', 'seedrandom'], function(QUnit, seedrandom) {
  QUnit.start();
  QUnit.module('require.js test');
  QUnit.test('some normal test', function(assert) {
    assert.ok(true, "Seeded random created using module:");
    var check = [];
    var prng = seedrandom('predictable.');
    var r;
    for (var j = 0; j < 5; ++j) {
      r = prng();
      assert.ok(true, r + "");
      check.push(r);
    }
    assert.ok(true, "Native random:");
    for (var j = 0; j < 5; ++j) {
      r = Math.random();
      assert.ok(true, r + "");
      check.push(r);
    }
    // Verify against Math.seedrandom.
    var seed = Math.seedrandom('predictable.');
    assert.equal(seed, 'predictable.',
        "Seed should be returned from Math.seedrandom.");
    for (var j = 0; j < 10; ++j) {
      r = Math.random();
      if (j < 5) {
        assert.equal(check[j], r, "Equal: " + r + " vs " + check[j]);
      } else {
        assert.ok(check[j] != r, "Unqual: " + r + " vs " + check[j]);
      }
    }
    document.close();
  });
});

</script>
</body>
</html>
