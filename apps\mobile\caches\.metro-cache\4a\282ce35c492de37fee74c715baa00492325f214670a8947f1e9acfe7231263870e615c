{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.convertAutoFocusJSONToNative = convertAutoFocusJSONToNative;\n  exports.convertFlashModeJSONToNative = convertFlashModeJSONToNative;\n  exports.convertWhiteBalanceJSONToNative = convertWhiteBalanceJSONToNative;\n  /*\n   * Native web camera (Android) has a torch: boolean\n   */\n  function convertFlashModeJSONToNative(input) {\n    switch (input) {\n      case 'torch':\n        return true;\n      case 'on':\n      case 'off':\n      case 'auto':\n      default:\n        return false;\n    }\n  }\n  function convertWhiteBalanceJSONToNative(input) {\n    switch (input) {\n      case 'on':\n      case 'auto':\n        return 'continuous';\n      case 'off':\n        return 'none';\n      case 'singleShot':\n        return 'single-shot';\n      default:\n        return undefined;\n    }\n  }\n  function convertAutoFocusJSONToNative(input) {\n    switch (input) {\n      case 'on':\n      case 'auto':\n        return 'continuous';\n      case 'off':\n        return 'manual';\n      case 'singleShot':\n        return 'single-shot';\n      default:\n        return undefined;\n    }\n  }\n});", "lineCount": 48, "map": [[8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 2, 4, 7], [11, 11, 4, 16, "convertFlashModeJSONToNative"], [11, 39, 4, 44, "convertFlashModeJSONToNative"], [11, 40, 4, 45, "input"], [11, 45, 4, 50], [11, 47, 4, 52], [12, 4, 5, 4], [12, 12, 5, 12, "input"], [12, 17, 5, 17], [13, 6, 6, 8], [13, 11, 6, 13], [13, 18, 6, 20], [14, 8, 7, 12], [14, 15, 7, 19], [14, 19, 7, 23], [15, 6, 8, 8], [15, 11, 8, 13], [15, 15, 8, 17], [16, 6, 9, 8], [16, 11, 9, 13], [16, 16, 9, 18], [17, 6, 10, 8], [17, 11, 10, 13], [17, 17, 10, 19], [18, 6, 11, 8], [19, 8, 12, 12], [19, 15, 12, 19], [19, 20, 12, 24], [20, 4, 13, 4], [21, 2, 14, 0], [22, 2, 15, 7], [22, 11, 15, 16, "convertWhiteBalanceJSONToNative"], [22, 42, 15, 47, "convertWhiteBalanceJSONToNative"], [22, 43, 15, 48, "input"], [22, 48, 15, 53], [22, 50, 15, 55], [23, 4, 16, 4], [23, 12, 16, 12, "input"], [23, 17, 16, 17], [24, 6, 17, 8], [24, 11, 17, 13], [24, 15, 17, 17], [25, 6, 18, 8], [25, 11, 18, 13], [25, 17, 18, 19], [26, 8, 19, 12], [26, 15, 19, 19], [26, 27, 19, 31], [27, 6, 20, 8], [27, 11, 20, 13], [27, 16, 20, 18], [28, 8, 21, 12], [28, 15, 21, 19], [28, 21, 21, 25], [29, 6, 22, 8], [29, 11, 22, 13], [29, 23, 22, 25], [30, 8, 23, 12], [30, 15, 23, 19], [30, 28, 23, 32], [31, 6, 24, 8], [32, 8, 25, 12], [32, 15, 25, 19, "undefined"], [32, 24, 25, 28], [33, 4, 26, 4], [34, 2, 27, 0], [35, 2, 28, 7], [35, 11, 28, 16, "convertAutoFocusJSONToNative"], [35, 39, 28, 44, "convertAutoFocusJSONToNative"], [35, 40, 28, 45, "input"], [35, 45, 28, 50], [35, 47, 28, 52], [36, 4, 29, 4], [36, 12, 29, 12, "input"], [36, 17, 29, 17], [37, 6, 30, 8], [37, 11, 30, 13], [37, 15, 30, 17], [38, 6, 31, 8], [38, 11, 31, 13], [38, 17, 31, 19], [39, 8, 32, 12], [39, 15, 32, 19], [39, 27, 32, 31], [40, 6, 33, 8], [40, 11, 33, 13], [40, 16, 33, 18], [41, 8, 34, 12], [41, 15, 34, 19], [41, 23, 34, 27], [42, 6, 35, 8], [42, 11, 35, 13], [42, 23, 35, 25], [43, 8, 36, 12], [43, 15, 36, 19], [43, 28, 36, 32], [44, 6, 37, 8], [45, 8, 38, 12], [45, 15, 38, 19, "undefined"], [45, 24, 38, 28], [46, 4, 39, 4], [47, 2, 40, 0], [48, 0, 40, 1], [48, 3]], "functionMap": {"names": ["<global>", "convertFlashModeJSONToNative", "convertWhiteBalanceJSONToNative", "convertAutoFocusJSONToNative"], "mappings": "AAA;OCG;CDU;OEC;CFY;OGC;CHY"}}, "type": "js/module"}]}