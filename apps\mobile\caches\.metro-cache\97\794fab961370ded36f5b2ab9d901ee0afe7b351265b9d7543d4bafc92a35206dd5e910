{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur passes...`);\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n      console.log(`[EchoCameraWeb] ✅ BLUR COMPLETE: Applied to (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 815,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 939,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 846,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 988,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1044,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1030,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1029,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1635, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4], [304, 6, 336, 4], [304, 12, 336, 10, "canvasWidth"], [304, 23, 336, 21], [304, 26, 336, 24, "ctx"], [304, 29, 336, 27], [304, 30, 336, 28, "canvas"], [304, 36, 336, 34], [304, 37, 336, 35, "width"], [304, 42, 336, 40], [305, 6, 337, 4], [305, 12, 337, 10, "canvasHeight"], [305, 24, 337, 22], [305, 27, 337, 25, "ctx"], [305, 30, 337, 28], [305, 31, 337, 29, "canvas"], [305, 37, 337, 35], [305, 38, 337, 36, "height"], [305, 44, 337, 42], [306, 6, 339, 4], [306, 12, 339, 10, "clampedX"], [306, 20, 339, 18], [306, 23, 339, 21, "Math"], [306, 27, 339, 25], [306, 28, 339, 26, "max"], [306, 31, 339, 29], [306, 32, 339, 30], [306, 33, 339, 31], [306, 35, 339, 33, "Math"], [306, 39, 339, 37], [306, 40, 339, 38, "min"], [306, 43, 339, 41], [306, 44, 339, 42, "Math"], [306, 48, 339, 46], [306, 49, 339, 47, "floor"], [306, 54, 339, 52], [306, 55, 339, 53, "x"], [306, 56, 339, 54], [306, 57, 339, 55], [306, 59, 339, 57, "canvasWidth"], [306, 70, 339, 68], [306, 73, 339, 71], [306, 74, 339, 72], [306, 75, 339, 73], [306, 76, 339, 74], [307, 6, 340, 4], [307, 12, 340, 10, "clampedY"], [307, 20, 340, 18], [307, 23, 340, 21, "Math"], [307, 27, 340, 25], [307, 28, 340, 26, "max"], [307, 31, 340, 29], [307, 32, 340, 30], [307, 33, 340, 31], [307, 35, 340, 33, "Math"], [307, 39, 340, 37], [307, 40, 340, 38, "min"], [307, 43, 340, 41], [307, 44, 340, 42, "Math"], [307, 48, 340, 46], [307, 49, 340, 47, "floor"], [307, 54, 340, 52], [307, 55, 340, 53, "y"], [307, 56, 340, 54], [307, 57, 340, 55], [307, 59, 340, 57, "canvasHeight"], [307, 71, 340, 69], [307, 74, 340, 72], [307, 75, 340, 73], [307, 76, 340, 74], [307, 77, 340, 75], [308, 6, 341, 4], [308, 12, 341, 10, "<PERSON><PERSON><PERSON><PERSON>"], [308, 24, 341, 22], [308, 27, 341, 25, "Math"], [308, 31, 341, 29], [308, 32, 341, 30, "min"], [308, 35, 341, 33], [308, 36, 341, 34, "Math"], [308, 40, 341, 38], [308, 41, 341, 39, "floor"], [308, 46, 341, 44], [308, 47, 341, 45, "width"], [308, 52, 341, 50], [308, 53, 341, 51], [308, 55, 341, 53, "canvasWidth"], [308, 66, 341, 64], [308, 69, 341, 67, "clampedX"], [308, 77, 341, 75], [308, 78, 341, 76], [309, 6, 342, 4], [309, 12, 342, 10, "clampedHeight"], [309, 25, 342, 23], [309, 28, 342, 26, "Math"], [309, 32, 342, 30], [309, 33, 342, 31, "min"], [309, 36, 342, 34], [309, 37, 342, 35, "Math"], [309, 41, 342, 39], [309, 42, 342, 40, "floor"], [309, 47, 342, 45], [309, 48, 342, 46, "height"], [309, 54, 342, 52], [309, 55, 342, 53], [309, 57, 342, 55, "canvasHeight"], [309, 69, 342, 67], [309, 72, 342, 70, "clampedY"], [309, 80, 342, 78], [309, 81, 342, 79], [310, 6, 344, 4], [310, 10, 344, 8, "<PERSON><PERSON><PERSON><PERSON>"], [310, 22, 344, 20], [310, 26, 344, 24], [310, 27, 344, 25], [310, 31, 344, 29, "clampedHeight"], [310, 44, 344, 42], [310, 48, 344, 46], [310, 49, 344, 47], [310, 51, 344, 49], [311, 8, 345, 6, "console"], [311, 15, 345, 13], [311, 16, 345, 14, "warn"], [311, 20, 345, 18], [311, 21, 345, 19], [311, 72, 345, 70], [311, 73, 345, 71], [312, 8, 346, 6], [313, 6, 347, 4], [315, 6, 349, 4], [316, 6, 350, 4], [316, 12, 350, 10, "imageData"], [316, 21, 350, 19], [316, 24, 350, 22, "ctx"], [316, 27, 350, 25], [316, 28, 350, 26, "getImageData"], [316, 40, 350, 38], [316, 41, 350, 39, "clampedX"], [316, 49, 350, 47], [316, 51, 350, 49, "clampedY"], [316, 59, 350, 57], [316, 61, 350, 59, "<PERSON><PERSON><PERSON><PERSON>"], [316, 73, 350, 71], [316, 75, 350, 73, "clampedHeight"], [316, 88, 350, 86], [316, 89, 350, 87], [317, 6, 351, 4], [317, 12, 351, 10, "data"], [317, 16, 351, 14], [317, 19, 351, 17, "imageData"], [317, 28, 351, 26], [317, 29, 351, 27, "data"], [317, 33, 351, 31], [319, 6, 353, 4], [320, 6, 354, 4], [320, 12, 354, 10, "pixelSize"], [320, 21, 354, 19], [320, 24, 354, 22, "Math"], [320, 28, 354, 26], [320, 29, 354, 27, "max"], [320, 32, 354, 30], [320, 33, 354, 31], [320, 35, 354, 33], [320, 37, 354, 35, "Math"], [320, 41, 354, 39], [320, 42, 354, 40, "min"], [320, 45, 354, 43], [320, 46, 354, 44, "<PERSON><PERSON><PERSON><PERSON>"], [320, 58, 354, 56], [320, 60, 354, 58, "clampedHeight"], [320, 73, 354, 71], [320, 74, 354, 72], [320, 77, 354, 75], [320, 78, 354, 76], [320, 79, 354, 77], [321, 6, 356, 4], [321, 11, 356, 9], [321, 15, 356, 13, "py"], [321, 17, 356, 15], [321, 20, 356, 18], [321, 21, 356, 19], [321, 23, 356, 21, "py"], [321, 25, 356, 23], [321, 28, 356, 26, "clampedHeight"], [321, 41, 356, 39], [321, 43, 356, 41, "py"], [321, 45, 356, 43], [321, 49, 356, 47, "pixelSize"], [321, 58, 356, 56], [321, 60, 356, 58], [322, 8, 357, 6], [322, 13, 357, 11], [322, 17, 357, 15, "px"], [322, 19, 357, 17], [322, 22, 357, 20], [322, 23, 357, 21], [322, 25, 357, 23, "px"], [322, 27, 357, 25], [322, 30, 357, 28, "<PERSON><PERSON><PERSON><PERSON>"], [322, 42, 357, 40], [322, 44, 357, 42, "px"], [322, 46, 357, 44], [322, 50, 357, 48, "pixelSize"], [322, 59, 357, 57], [322, 61, 357, 59], [323, 10, 358, 8], [324, 10, 359, 8], [324, 14, 359, 12, "r"], [324, 15, 359, 13], [324, 18, 359, 16], [324, 19, 359, 17], [325, 12, 359, 19, "g"], [325, 13, 359, 20], [325, 16, 359, 23], [325, 17, 359, 24], [326, 12, 359, 26, "b"], [326, 13, 359, 27], [326, 16, 359, 30], [326, 17, 359, 31], [327, 12, 359, 33, "count"], [327, 17, 359, 38], [327, 20, 359, 41], [327, 21, 359, 42], [328, 10, 361, 8], [328, 15, 361, 13], [328, 19, 361, 17, "dy"], [328, 21, 361, 19], [328, 24, 361, 22], [328, 25, 361, 23], [328, 27, 361, 25, "dy"], [328, 29, 361, 27], [328, 32, 361, 30, "pixelSize"], [328, 41, 361, 39], [328, 45, 361, 43, "py"], [328, 47, 361, 45], [328, 50, 361, 48, "dy"], [328, 52, 361, 50], [328, 55, 361, 53, "clampedHeight"], [328, 68, 361, 66], [328, 70, 361, 68, "dy"], [328, 72, 361, 70], [328, 74, 361, 72], [328, 76, 361, 74], [329, 12, 362, 10], [329, 17, 362, 15], [329, 21, 362, 19, "dx"], [329, 23, 362, 21], [329, 26, 362, 24], [329, 27, 362, 25], [329, 29, 362, 27, "dx"], [329, 31, 362, 29], [329, 34, 362, 32, "pixelSize"], [329, 43, 362, 41], [329, 47, 362, 45, "px"], [329, 49, 362, 47], [329, 52, 362, 50, "dx"], [329, 54, 362, 52], [329, 57, 362, 55, "<PERSON><PERSON><PERSON><PERSON>"], [329, 69, 362, 67], [329, 71, 362, 69, "dx"], [329, 73, 362, 71], [329, 75, 362, 73], [329, 77, 362, 75], [330, 14, 363, 12], [330, 20, 363, 18, "index"], [330, 25, 363, 23], [330, 28, 363, 26], [330, 29, 363, 27], [330, 30, 363, 28, "py"], [330, 32, 363, 30], [330, 35, 363, 33, "dy"], [330, 37, 363, 35], [330, 41, 363, 39, "<PERSON><PERSON><PERSON><PERSON>"], [330, 53, 363, 51], [330, 57, 363, 55, "px"], [330, 59, 363, 57], [330, 62, 363, 60, "dx"], [330, 64, 363, 62], [330, 65, 363, 63], [330, 69, 363, 67], [330, 70, 363, 68], [331, 14, 364, 12, "r"], [331, 15, 364, 13], [331, 19, 364, 17, "data"], [331, 23, 364, 21], [331, 24, 364, 22, "index"], [331, 29, 364, 27], [331, 30, 364, 28], [332, 14, 365, 12, "g"], [332, 15, 365, 13], [332, 19, 365, 17, "data"], [332, 23, 365, 21], [332, 24, 365, 22, "index"], [332, 29, 365, 27], [332, 32, 365, 30], [332, 33, 365, 31], [332, 34, 365, 32], [333, 14, 366, 12, "b"], [333, 15, 366, 13], [333, 19, 366, 17, "data"], [333, 23, 366, 21], [333, 24, 366, 22, "index"], [333, 29, 366, 27], [333, 32, 366, 30], [333, 33, 366, 31], [333, 34, 366, 32], [334, 14, 367, 12, "count"], [334, 19, 367, 17], [334, 21, 367, 19], [335, 12, 368, 10], [336, 10, 369, 8], [337, 10, 371, 8], [337, 14, 371, 12, "count"], [337, 19, 371, 17], [337, 22, 371, 20], [337, 23, 371, 21], [337, 25, 371, 23], [338, 12, 372, 10, "r"], [338, 13, 372, 11], [338, 16, 372, 14, "Math"], [338, 20, 372, 18], [338, 21, 372, 19, "floor"], [338, 26, 372, 24], [338, 27, 372, 25, "r"], [338, 28, 372, 26], [338, 31, 372, 29, "count"], [338, 36, 372, 34], [338, 37, 372, 35], [339, 12, 373, 10, "g"], [339, 13, 373, 11], [339, 16, 373, 14, "Math"], [339, 20, 373, 18], [339, 21, 373, 19, "floor"], [339, 26, 373, 24], [339, 27, 373, 25, "g"], [339, 28, 373, 26], [339, 31, 373, 29, "count"], [339, 36, 373, 34], [339, 37, 373, 35], [340, 12, 374, 10, "b"], [340, 13, 374, 11], [340, 16, 374, 14, "Math"], [340, 20, 374, 18], [340, 21, 374, 19, "floor"], [340, 26, 374, 24], [340, 27, 374, 25, "b"], [340, 28, 374, 26], [340, 31, 374, 29, "count"], [340, 36, 374, 34], [340, 37, 374, 35], [342, 12, 376, 10], [343, 12, 377, 10], [343, 17, 377, 15], [343, 21, 377, 19, "dy"], [343, 23, 377, 21], [343, 26, 377, 24], [343, 27, 377, 25], [343, 29, 377, 27, "dy"], [343, 31, 377, 29], [343, 34, 377, 32, "pixelSize"], [343, 43, 377, 41], [343, 47, 377, 45, "py"], [343, 49, 377, 47], [343, 52, 377, 50, "dy"], [343, 54, 377, 52], [343, 57, 377, 55, "clampedHeight"], [343, 70, 377, 68], [343, 72, 377, 70, "dy"], [343, 74, 377, 72], [343, 76, 377, 74], [343, 78, 377, 76], [344, 14, 378, 12], [344, 19, 378, 17], [344, 23, 378, 21, "dx"], [344, 25, 378, 23], [344, 28, 378, 26], [344, 29, 378, 27], [344, 31, 378, 29, "dx"], [344, 33, 378, 31], [344, 36, 378, 34, "pixelSize"], [344, 45, 378, 43], [344, 49, 378, 47, "px"], [344, 51, 378, 49], [344, 54, 378, 52, "dx"], [344, 56, 378, 54], [344, 59, 378, 57, "<PERSON><PERSON><PERSON><PERSON>"], [344, 71, 378, 69], [344, 73, 378, 71, "dx"], [344, 75, 378, 73], [344, 77, 378, 75], [344, 79, 378, 77], [345, 16, 379, 14], [345, 22, 379, 20, "index"], [345, 27, 379, 25], [345, 30, 379, 28], [345, 31, 379, 29], [345, 32, 379, 30, "py"], [345, 34, 379, 32], [345, 37, 379, 35, "dy"], [345, 39, 379, 37], [345, 43, 379, 41, "<PERSON><PERSON><PERSON><PERSON>"], [345, 55, 379, 53], [345, 59, 379, 57, "px"], [345, 61, 379, 59], [345, 64, 379, 62, "dx"], [345, 66, 379, 64], [345, 67, 379, 65], [345, 71, 379, 69], [345, 72, 379, 70], [346, 16, 380, 14, "data"], [346, 20, 380, 18], [346, 21, 380, 19, "index"], [346, 26, 380, 24], [346, 27, 380, 25], [346, 30, 380, 28, "r"], [346, 31, 380, 29], [347, 16, 381, 14, "data"], [347, 20, 381, 18], [347, 21, 381, 19, "index"], [347, 26, 381, 24], [347, 29, 381, 27], [347, 30, 381, 28], [347, 31, 381, 29], [347, 34, 381, 32, "g"], [347, 35, 381, 33], [348, 16, 382, 14, "data"], [348, 20, 382, 18], [348, 21, 382, 19, "index"], [348, 26, 382, 24], [348, 29, 382, 27], [348, 30, 382, 28], [348, 31, 382, 29], [348, 34, 382, 32, "b"], [348, 35, 382, 33], [349, 16, 383, 14], [350, 14, 384, 12], [351, 12, 385, 10], [352, 10, 386, 8], [353, 8, 387, 6], [354, 6, 388, 4], [356, 6, 390, 4], [357, 6, 391, 4, "console"], [357, 13, 391, 11], [357, 14, 391, 12, "log"], [357, 17, 391, 15], [357, 18, 391, 16], [357, 74, 391, 72], [357, 75, 391, 73], [358, 6, 392, 4], [358, 11, 392, 9], [358, 15, 392, 13, "i"], [358, 16, 392, 14], [358, 19, 392, 17], [358, 20, 392, 18], [358, 22, 392, 20, "i"], [358, 23, 392, 21], [358, 26, 392, 24], [358, 27, 392, 25], [358, 29, 392, 27, "i"], [358, 30, 392, 28], [358, 32, 392, 30], [358, 34, 392, 32], [359, 8, 393, 6, "applySimpleBlur"], [359, 23, 393, 21], [359, 24, 393, 22, "data"], [359, 28, 393, 26], [359, 30, 393, 28, "<PERSON><PERSON><PERSON><PERSON>"], [359, 42, 393, 40], [359, 44, 393, 42, "clampedHeight"], [359, 57, 393, 55], [359, 58, 393, 56], [360, 6, 394, 4], [362, 6, 396, 4], [363, 6, 397, 4, "ctx"], [363, 9, 397, 7], [363, 10, 397, 8, "putImageData"], [363, 22, 397, 20], [363, 23, 397, 21, "imageData"], [363, 32, 397, 30], [363, 34, 397, 32, "clampedX"], [363, 42, 397, 40], [363, 44, 397, 42, "clampedY"], [363, 52, 397, 50], [363, 53, 397, 51], [365, 6, 399, 4], [366, 6, 400, 4, "ctx"], [366, 9, 400, 7], [366, 10, 400, 8, "fillStyle"], [366, 19, 400, 17], [366, 22, 400, 20], [366, 48, 400, 46], [367, 6, 401, 4, "ctx"], [367, 9, 401, 7], [367, 10, 401, 8, "fillRect"], [367, 18, 401, 16], [367, 19, 401, 17, "clampedX"], [367, 27, 401, 25], [367, 29, 401, 27, "clampedY"], [367, 37, 401, 35], [367, 39, 401, 37, "<PERSON><PERSON><PERSON><PERSON>"], [367, 51, 401, 49], [367, 53, 401, 51, "clampedHeight"], [367, 66, 401, 64], [367, 67, 401, 65], [368, 6, 403, 4, "console"], [368, 13, 403, 11], [368, 14, 403, 12, "log"], [368, 17, 403, 15], [368, 18, 403, 16], [368, 66, 403, 64, "clampedX"], [368, 74, 403, 72], [368, 79, 403, 77, "clampedY"], [368, 87, 403, 85], [368, 92, 403, 90, "<PERSON><PERSON><PERSON><PERSON>"], [368, 104, 403, 102], [368, 108, 403, 106, "clampedHeight"], [368, 121, 403, 119], [368, 123, 403, 121], [368, 124, 403, 122], [369, 4, 404, 2], [369, 5, 404, 3], [370, 4, 406, 2], [370, 10, 406, 8, "applySimpleBlur"], [370, 25, 406, 23], [370, 28, 406, 26, "applySimpleBlur"], [370, 29, 406, 27, "data"], [370, 33, 406, 50], [370, 35, 406, 52, "width"], [370, 40, 406, 65], [370, 42, 406, 67, "height"], [370, 48, 406, 81], [370, 53, 406, 86], [371, 6, 407, 4], [371, 12, 407, 10, "original"], [371, 20, 407, 18], [371, 23, 407, 21], [371, 27, 407, 25, "Uint8ClampedArray"], [371, 44, 407, 42], [371, 45, 407, 43, "data"], [371, 49, 407, 47], [371, 50, 407, 48], [372, 6, 409, 4], [372, 11, 409, 9], [372, 15, 409, 13, "y"], [372, 16, 409, 14], [372, 19, 409, 17], [372, 20, 409, 18], [372, 22, 409, 20, "y"], [372, 23, 409, 21], [372, 26, 409, 24, "height"], [372, 32, 409, 30], [372, 35, 409, 33], [372, 36, 409, 34], [372, 38, 409, 36, "y"], [372, 39, 409, 37], [372, 41, 409, 39], [372, 43, 409, 41], [373, 8, 410, 6], [373, 13, 410, 11], [373, 17, 410, 15, "x"], [373, 18, 410, 16], [373, 21, 410, 19], [373, 22, 410, 20], [373, 24, 410, 22, "x"], [373, 25, 410, 23], [373, 28, 410, 26, "width"], [373, 33, 410, 31], [373, 36, 410, 34], [373, 37, 410, 35], [373, 39, 410, 37, "x"], [373, 40, 410, 38], [373, 42, 410, 40], [373, 44, 410, 42], [374, 10, 411, 8], [374, 16, 411, 14, "index"], [374, 21, 411, 19], [374, 24, 411, 22], [374, 25, 411, 23, "y"], [374, 26, 411, 24], [374, 29, 411, 27, "width"], [374, 34, 411, 32], [374, 37, 411, 35, "x"], [374, 38, 411, 36], [374, 42, 411, 40], [374, 43, 411, 41], [376, 10, 413, 8], [377, 10, 414, 8], [377, 14, 414, 12, "r"], [377, 15, 414, 13], [377, 18, 414, 16], [377, 19, 414, 17], [378, 12, 414, 19, "g"], [378, 13, 414, 20], [378, 16, 414, 23], [378, 17, 414, 24], [379, 12, 414, 26, "b"], [379, 13, 414, 27], [379, 16, 414, 30], [379, 17, 414, 31], [380, 10, 415, 8], [380, 15, 415, 13], [380, 19, 415, 17, "dy"], [380, 21, 415, 19], [380, 24, 415, 22], [380, 25, 415, 23], [380, 26, 415, 24], [380, 28, 415, 26, "dy"], [380, 30, 415, 28], [380, 34, 415, 32], [380, 35, 415, 33], [380, 37, 415, 35, "dy"], [380, 39, 415, 37], [380, 41, 415, 39], [380, 43, 415, 41], [381, 12, 416, 10], [381, 17, 416, 15], [381, 21, 416, 19, "dx"], [381, 23, 416, 21], [381, 26, 416, 24], [381, 27, 416, 25], [381, 28, 416, 26], [381, 30, 416, 28, "dx"], [381, 32, 416, 30], [381, 36, 416, 34], [381, 37, 416, 35], [381, 39, 416, 37, "dx"], [381, 41, 416, 39], [381, 43, 416, 41], [381, 45, 416, 43], [382, 14, 417, 12], [382, 20, 417, 18, "neighborIndex"], [382, 33, 417, 31], [382, 36, 417, 34], [382, 37, 417, 35], [382, 38, 417, 36, "y"], [382, 39, 417, 37], [382, 42, 417, 40, "dy"], [382, 44, 417, 42], [382, 48, 417, 46, "width"], [382, 53, 417, 51], [382, 57, 417, 55, "x"], [382, 58, 417, 56], [382, 61, 417, 59, "dx"], [382, 63, 417, 61], [382, 64, 417, 62], [382, 68, 417, 66], [382, 69, 417, 67], [383, 14, 418, 12, "r"], [383, 15, 418, 13], [383, 19, 418, 17, "original"], [383, 27, 418, 25], [383, 28, 418, 26, "neighborIndex"], [383, 41, 418, 39], [383, 42, 418, 40], [384, 14, 419, 12, "g"], [384, 15, 419, 13], [384, 19, 419, 17, "original"], [384, 27, 419, 25], [384, 28, 419, 26, "neighborIndex"], [384, 41, 419, 39], [384, 44, 419, 42], [384, 45, 419, 43], [384, 46, 419, 44], [385, 14, 420, 12, "b"], [385, 15, 420, 13], [385, 19, 420, 17, "original"], [385, 27, 420, 25], [385, 28, 420, 26, "neighborIndex"], [385, 41, 420, 39], [385, 44, 420, 42], [385, 45, 420, 43], [385, 46, 420, 44], [386, 12, 421, 10], [387, 10, 422, 8], [388, 10, 424, 8, "data"], [388, 14, 424, 12], [388, 15, 424, 13, "index"], [388, 20, 424, 18], [388, 21, 424, 19], [388, 24, 424, 22, "r"], [388, 25, 424, 23], [388, 28, 424, 26], [388, 29, 424, 27], [389, 10, 425, 8, "data"], [389, 14, 425, 12], [389, 15, 425, 13, "index"], [389, 20, 425, 18], [389, 23, 425, 21], [389, 24, 425, 22], [389, 25, 425, 23], [389, 28, 425, 26, "g"], [389, 29, 425, 27], [389, 32, 425, 30], [389, 33, 425, 31], [390, 10, 426, 8, "data"], [390, 14, 426, 12], [390, 15, 426, 13, "index"], [390, 20, 426, 18], [390, 23, 426, 21], [390, 24, 426, 22], [390, 25, 426, 23], [390, 28, 426, 26, "b"], [390, 29, 426, 27], [390, 32, 426, 30], [390, 33, 426, 31], [391, 8, 427, 6], [392, 6, 428, 4], [393, 4, 429, 2], [393, 5, 429, 3], [394, 4, 431, 2], [394, 10, 431, 8, "applyFallbackFaceBlur"], [394, 31, 431, 29], [394, 34, 431, 32, "applyFallbackFaceBlur"], [394, 35, 431, 33, "ctx"], [394, 38, 431, 62], [394, 40, 431, 64, "imgWidth"], [394, 48, 431, 80], [394, 50, 431, 82, "imgHeight"], [394, 59, 431, 99], [394, 64, 431, 104], [395, 6, 432, 4, "console"], [395, 13, 432, 11], [395, 14, 432, 12, "log"], [395, 17, 432, 15], [395, 18, 432, 16], [395, 90, 432, 88], [395, 91, 432, 89], [397, 6, 434, 4], [398, 6, 435, 4], [398, 12, 435, 10, "areas"], [398, 17, 435, 15], [398, 20, 435, 18], [399, 6, 436, 6], [400, 6, 437, 6], [401, 8, 437, 8, "x"], [401, 9, 437, 9], [401, 11, 437, 11, "imgWidth"], [401, 19, 437, 19], [401, 22, 437, 22], [401, 26, 437, 26], [402, 8, 437, 28, "y"], [402, 9, 437, 29], [402, 11, 437, 31, "imgHeight"], [402, 20, 437, 40], [402, 23, 437, 43], [402, 27, 437, 47], [403, 8, 437, 49, "w"], [403, 9, 437, 50], [403, 11, 437, 52, "imgWidth"], [403, 19, 437, 60], [403, 22, 437, 63], [403, 25, 437, 66], [404, 8, 437, 68, "h"], [404, 9, 437, 69], [404, 11, 437, 71, "imgHeight"], [404, 20, 437, 80], [404, 23, 437, 83], [405, 6, 437, 87], [405, 7, 437, 88], [406, 6, 438, 6], [407, 6, 439, 6], [408, 8, 439, 8, "x"], [408, 9, 439, 9], [408, 11, 439, 11, "imgWidth"], [408, 19, 439, 19], [408, 22, 439, 22], [408, 25, 439, 25], [409, 8, 439, 27, "y"], [409, 9, 439, 28], [409, 11, 439, 30, "imgHeight"], [409, 20, 439, 39], [409, 23, 439, 42], [409, 26, 439, 45], [410, 8, 439, 47, "w"], [410, 9, 439, 48], [410, 11, 439, 50, "imgWidth"], [410, 19, 439, 58], [410, 22, 439, 61], [410, 26, 439, 65], [411, 8, 439, 67, "h"], [411, 9, 439, 68], [411, 11, 439, 70, "imgHeight"], [411, 20, 439, 79], [411, 23, 439, 82], [412, 6, 439, 86], [412, 7, 439, 87], [413, 6, 440, 6], [414, 6, 441, 6], [415, 8, 441, 8, "x"], [415, 9, 441, 9], [415, 11, 441, 11, "imgWidth"], [415, 19, 441, 19], [415, 22, 441, 22], [415, 26, 441, 26], [416, 8, 441, 28, "y"], [416, 9, 441, 29], [416, 11, 441, 31, "imgHeight"], [416, 20, 441, 40], [416, 23, 441, 43], [416, 26, 441, 46], [417, 8, 441, 48, "w"], [417, 9, 441, 49], [417, 11, 441, 51, "imgWidth"], [417, 19, 441, 59], [417, 22, 441, 62], [417, 26, 441, 66], [418, 8, 441, 68, "h"], [418, 9, 441, 69], [418, 11, 441, 71, "imgHeight"], [418, 20, 441, 80], [418, 23, 441, 83], [419, 6, 441, 87], [419, 7, 441, 88], [419, 8, 442, 5], [420, 6, 444, 4, "areas"], [420, 11, 444, 9], [420, 12, 444, 10, "for<PERSON>ach"], [420, 19, 444, 17], [420, 20, 444, 18], [420, 21, 444, 19, "area"], [420, 25, 444, 23], [420, 27, 444, 25, "index"], [420, 32, 444, 30], [420, 37, 444, 35], [421, 8, 445, 6, "console"], [421, 15, 445, 13], [421, 16, 445, 14, "log"], [421, 19, 445, 17], [421, 20, 445, 18], [421, 65, 445, 63, "index"], [421, 70, 445, 68], [421, 73, 445, 71], [421, 74, 445, 72], [421, 77, 445, 75], [421, 79, 445, 77, "area"], [421, 83, 445, 81], [421, 84, 445, 82], [422, 8, 446, 6, "applyStrongBlur"], [422, 23, 446, 21], [422, 24, 446, 22, "ctx"], [422, 27, 446, 25], [422, 29, 446, 27, "area"], [422, 33, 446, 31], [422, 34, 446, 32, "x"], [422, 35, 446, 33], [422, 37, 446, 35, "area"], [422, 41, 446, 39], [422, 42, 446, 40, "y"], [422, 43, 446, 41], [422, 45, 446, 43, "area"], [422, 49, 446, 47], [422, 50, 446, 48, "w"], [422, 51, 446, 49], [422, 53, 446, 51, "area"], [422, 57, 446, 55], [422, 58, 446, 56, "h"], [422, 59, 446, 57], [422, 60, 446, 58], [423, 6, 447, 4], [423, 7, 447, 5], [423, 8, 447, 6], [424, 4, 448, 2], [424, 5, 448, 3], [426, 4, 450, 2], [427, 4, 451, 2], [427, 10, 451, 8, "capturePhoto"], [427, 22, 451, 20], [427, 25, 451, 23], [427, 29, 451, 23, "useCallback"], [427, 47, 451, 34], [427, 49, 451, 35], [427, 61, 451, 47], [428, 6, 452, 4], [429, 6, 453, 4], [429, 12, 453, 10, "isDev"], [429, 17, 453, 15], [429, 20, 453, 18, "process"], [429, 27, 453, 25], [429, 28, 453, 26, "env"], [429, 31, 453, 29], [429, 32, 453, 30, "NODE_ENV"], [429, 40, 453, 38], [429, 45, 453, 43], [429, 58, 453, 56], [429, 62, 453, 60, "__DEV__"], [429, 69, 453, 67], [430, 6, 455, 4], [430, 10, 455, 8], [430, 11, 455, 9, "cameraRef"], [430, 20, 455, 18], [430, 21, 455, 19, "current"], [430, 28, 455, 26], [430, 32, 455, 30], [430, 33, 455, 31, "isDev"], [430, 38, 455, 36], [430, 40, 455, 38], [431, 8, 456, 6, "<PERSON><PERSON>"], [431, 22, 456, 11], [431, 23, 456, 12, "alert"], [431, 28, 456, 17], [431, 29, 456, 18], [431, 36, 456, 25], [431, 38, 456, 27], [431, 56, 456, 45], [431, 57, 456, 46], [432, 8, 457, 6], [433, 6, 458, 4], [434, 6, 459, 4], [434, 10, 459, 8], [435, 8, 460, 6, "setProcessingState"], [435, 26, 460, 24], [435, 27, 460, 25], [435, 38, 460, 36], [435, 39, 460, 37], [436, 8, 461, 6, "setProcessingProgress"], [436, 29, 461, 27], [436, 30, 461, 28], [436, 32, 461, 30], [436, 33, 461, 31], [437, 8, 462, 6], [438, 8, 463, 6], [439, 8, 464, 6], [440, 8, 465, 6], [440, 14, 465, 12], [440, 18, 465, 16, "Promise"], [440, 25, 465, 23], [440, 26, 465, 24, "resolve"], [440, 33, 465, 31], [440, 37, 465, 35, "setTimeout"], [440, 47, 465, 45], [440, 48, 465, 46, "resolve"], [440, 55, 465, 53], [440, 57, 465, 55], [440, 59, 465, 57], [440, 60, 465, 58], [440, 61, 465, 59], [441, 8, 466, 6], [442, 8, 467, 6], [442, 12, 467, 10, "photo"], [442, 17, 467, 15], [443, 8, 469, 6], [443, 12, 469, 10], [444, 10, 470, 8, "photo"], [444, 15, 470, 13], [444, 18, 470, 16], [444, 24, 470, 22, "cameraRef"], [444, 33, 470, 31], [444, 34, 470, 32, "current"], [444, 41, 470, 39], [444, 42, 470, 40, "takePictureAsync"], [444, 58, 470, 56], [444, 59, 470, 57], [445, 12, 471, 10, "quality"], [445, 19, 471, 17], [445, 21, 471, 19], [445, 24, 471, 22], [446, 12, 472, 10, "base64"], [446, 18, 472, 16], [446, 20, 472, 18], [446, 25, 472, 23], [447, 12, 473, 10, "skipProcessing"], [447, 26, 473, 24], [447, 28, 473, 26], [447, 32, 473, 30], [447, 33, 473, 32], [448, 10, 474, 8], [448, 11, 474, 9], [448, 12, 474, 10], [449, 8, 475, 6], [449, 9, 475, 7], [449, 10, 475, 8], [449, 17, 475, 15, "cameraError"], [449, 28, 475, 26], [449, 30, 475, 28], [450, 10, 476, 8, "console"], [450, 17, 476, 15], [450, 18, 476, 16, "log"], [450, 21, 476, 19], [450, 22, 476, 20], [450, 82, 476, 80], [450, 84, 476, 82, "cameraError"], [450, 95, 476, 93], [450, 96, 476, 94], [451, 10, 477, 8], [452, 10, 478, 8], [452, 14, 478, 12, "isDev"], [452, 19, 478, 17], [452, 21, 478, 19], [453, 12, 479, 10, "photo"], [453, 17, 479, 15], [453, 20, 479, 18], [454, 14, 480, 12, "uri"], [454, 17, 480, 15], [454, 19, 480, 17], [455, 12, 481, 10], [455, 13, 481, 11], [456, 10, 482, 8], [456, 11, 482, 9], [456, 17, 482, 15], [457, 12, 483, 10], [457, 18, 483, 16, "cameraError"], [457, 29, 483, 27], [458, 10, 484, 8], [459, 8, 485, 6], [460, 8, 486, 6], [460, 12, 486, 10], [460, 13, 486, 11, "photo"], [460, 18, 486, 16], [460, 20, 486, 18], [461, 10, 487, 8], [461, 16, 487, 14], [461, 20, 487, 18, "Error"], [461, 25, 487, 23], [461, 26, 487, 24], [461, 51, 487, 49], [461, 52, 487, 50], [462, 8, 488, 6], [463, 8, 489, 6, "console"], [463, 15, 489, 13], [463, 16, 489, 14, "log"], [463, 19, 489, 17], [463, 20, 489, 18], [463, 56, 489, 54], [463, 58, 489, 56, "photo"], [463, 63, 489, 61], [463, 64, 489, 62, "uri"], [463, 67, 489, 65], [463, 68, 489, 66], [464, 8, 490, 6, "setCapturedPhoto"], [464, 24, 490, 22], [464, 25, 490, 23, "photo"], [464, 30, 490, 28], [464, 31, 490, 29, "uri"], [464, 34, 490, 32], [464, 35, 490, 33], [465, 8, 491, 6, "setProcessingProgress"], [465, 29, 491, 27], [465, 30, 491, 28], [465, 32, 491, 30], [465, 33, 491, 31], [466, 8, 492, 6], [467, 8, 493, 6, "console"], [467, 15, 493, 13], [467, 16, 493, 14, "log"], [467, 19, 493, 17], [467, 20, 493, 18], [467, 73, 493, 71], [467, 74, 493, 72], [468, 8, 494, 6], [468, 14, 494, 12, "processImageWithFaceBlur"], [468, 38, 494, 36], [468, 39, 494, 37, "photo"], [468, 44, 494, 42], [468, 45, 494, 43, "uri"], [468, 48, 494, 46], [468, 49, 494, 47], [469, 8, 495, 6, "console"], [469, 15, 495, 13], [469, 16, 495, 14, "log"], [469, 19, 495, 17], [469, 20, 495, 18], [469, 71, 495, 69], [469, 72, 495, 70], [470, 6, 496, 4], [470, 7, 496, 5], [470, 8, 496, 6], [470, 15, 496, 13, "error"], [470, 20, 496, 18], [470, 22, 496, 20], [471, 8, 497, 6, "console"], [471, 15, 497, 13], [471, 16, 497, 14, "error"], [471, 21, 497, 19], [471, 22, 497, 20], [471, 54, 497, 52], [471, 56, 497, 54, "error"], [471, 61, 497, 59], [471, 62, 497, 60], [472, 8, 498, 6, "setErrorMessage"], [472, 23, 498, 21], [472, 24, 498, 22], [472, 68, 498, 66], [472, 69, 498, 67], [473, 8, 499, 6, "setProcessingState"], [473, 26, 499, 24], [473, 27, 499, 25], [473, 34, 499, 32], [473, 35, 499, 33], [474, 6, 500, 4], [475, 4, 501, 2], [475, 5, 501, 3], [475, 7, 501, 5], [475, 9, 501, 7], [475, 10, 501, 8], [476, 4, 502, 2], [477, 4, 503, 2], [477, 10, 503, 8, "processImageWithFaceBlur"], [477, 34, 503, 32], [477, 37, 503, 35], [477, 43, 503, 42, "photoUri"], [477, 51, 503, 58], [477, 55, 503, 63], [478, 6, 504, 4], [478, 10, 504, 8], [479, 8, 505, 6, "console"], [479, 15, 505, 13], [479, 16, 505, 14, "log"], [479, 19, 505, 17], [479, 20, 505, 18], [479, 84, 505, 82], [479, 85, 505, 83], [480, 8, 506, 6, "setProcessingState"], [480, 26, 506, 24], [480, 27, 506, 25], [480, 39, 506, 37], [480, 40, 506, 38], [481, 8, 507, 6, "setProcessingProgress"], [481, 29, 507, 27], [481, 30, 507, 28], [481, 32, 507, 30], [481, 33, 507, 31], [483, 8, 509, 6], [484, 8, 510, 6], [484, 14, 510, 12, "canvas"], [484, 20, 510, 18], [484, 23, 510, 21, "document"], [484, 31, 510, 29], [484, 32, 510, 30, "createElement"], [484, 45, 510, 43], [484, 46, 510, 44], [484, 54, 510, 52], [484, 55, 510, 53], [485, 8, 511, 6], [485, 14, 511, 12, "ctx"], [485, 17, 511, 15], [485, 20, 511, 18, "canvas"], [485, 26, 511, 24], [485, 27, 511, 25, "getContext"], [485, 37, 511, 35], [485, 38, 511, 36], [485, 42, 511, 40], [485, 43, 511, 41], [486, 8, 512, 6], [486, 12, 512, 10], [486, 13, 512, 11, "ctx"], [486, 16, 512, 14], [486, 18, 512, 16], [486, 24, 512, 22], [486, 28, 512, 26, "Error"], [486, 33, 512, 31], [486, 34, 512, 32], [486, 64, 512, 62], [486, 65, 512, 63], [488, 8, 514, 6], [489, 8, 515, 6], [489, 14, 515, 12, "img"], [489, 17, 515, 15], [489, 20, 515, 18], [489, 24, 515, 22, "Image"], [489, 29, 515, 27], [489, 30, 515, 28], [489, 31, 515, 29], [490, 8, 516, 6], [490, 14, 516, 12], [490, 18, 516, 16, "Promise"], [490, 25, 516, 23], [490, 26, 516, 24], [490, 27, 516, 25, "resolve"], [490, 34, 516, 32], [490, 36, 516, 34, "reject"], [490, 42, 516, 40], [490, 47, 516, 45], [491, 10, 517, 8, "img"], [491, 13, 517, 11], [491, 14, 517, 12, "onload"], [491, 20, 517, 18], [491, 23, 517, 21, "resolve"], [491, 30, 517, 28], [492, 10, 518, 8, "img"], [492, 13, 518, 11], [492, 14, 518, 12, "onerror"], [492, 21, 518, 19], [492, 24, 518, 22, "reject"], [492, 30, 518, 28], [493, 10, 519, 8, "img"], [493, 13, 519, 11], [493, 14, 519, 12, "src"], [493, 17, 519, 15], [493, 20, 519, 18, "photoUri"], [493, 28, 519, 26], [494, 8, 520, 6], [494, 9, 520, 7], [494, 10, 520, 8], [496, 8, 522, 6], [497, 8, 523, 6, "canvas"], [497, 14, 523, 12], [497, 15, 523, 13, "width"], [497, 20, 523, 18], [497, 23, 523, 21, "img"], [497, 26, 523, 24], [497, 27, 523, 25, "width"], [497, 32, 523, 30], [498, 8, 524, 6, "canvas"], [498, 14, 524, 12], [498, 15, 524, 13, "height"], [498, 21, 524, 19], [498, 24, 524, 22, "img"], [498, 27, 524, 25], [498, 28, 524, 26, "height"], [498, 34, 524, 32], [499, 8, 525, 6, "console"], [499, 15, 525, 13], [499, 16, 525, 14, "log"], [499, 19, 525, 17], [499, 20, 525, 18], [499, 54, 525, 52], [499, 56, 525, 54], [500, 10, 525, 56, "width"], [500, 15, 525, 61], [500, 17, 525, 63, "img"], [500, 20, 525, 66], [500, 21, 525, 67, "width"], [500, 26, 525, 72], [501, 10, 525, 74, "height"], [501, 16, 525, 80], [501, 18, 525, 82, "img"], [501, 21, 525, 85], [501, 22, 525, 86, "height"], [502, 8, 525, 93], [502, 9, 525, 94], [502, 10, 525, 95], [504, 8, 527, 6], [505, 8, 528, 6, "ctx"], [505, 11, 528, 9], [505, 12, 528, 10, "drawImage"], [505, 21, 528, 19], [505, 22, 528, 20, "img"], [505, 25, 528, 23], [505, 27, 528, 25], [505, 28, 528, 26], [505, 30, 528, 28], [505, 31, 528, 29], [505, 32, 528, 30], [506, 8, 529, 6, "console"], [506, 15, 529, 13], [506, 16, 529, 14, "log"], [506, 19, 529, 17], [506, 20, 529, 18], [506, 72, 529, 70], [506, 73, 529, 71], [507, 8, 531, 6, "setProcessingProgress"], [507, 29, 531, 27], [507, 30, 531, 28], [507, 32, 531, 30], [507, 33, 531, 31], [509, 8, 533, 6], [510, 8, 534, 6], [510, 12, 534, 10, "detectedFaces"], [510, 25, 534, 23], [510, 28, 534, 26], [510, 30, 534, 28], [511, 8, 536, 6, "console"], [511, 15, 536, 13], [511, 16, 536, 14, "log"], [511, 19, 536, 17], [511, 20, 536, 18], [511, 81, 536, 79], [511, 82, 536, 80], [513, 8, 538, 6], [514, 8, 539, 6], [514, 12, 539, 10], [515, 10, 540, 8], [515, 16, 540, 14, "loadTensorFlowFaceDetection"], [515, 43, 540, 41], [515, 44, 540, 42], [515, 45, 540, 43], [516, 10, 541, 8, "detectedFaces"], [516, 23, 541, 21], [516, 26, 541, 24], [516, 32, 541, 30, "detectFacesWithTensorFlow"], [516, 57, 541, 55], [516, 58, 541, 56, "img"], [516, 61, 541, 59], [516, 62, 541, 60], [517, 10, 542, 8, "console"], [517, 17, 542, 15], [517, 18, 542, 16, "log"], [517, 21, 542, 19], [517, 22, 542, 20], [517, 70, 542, 68, "detectedFaces"], [517, 83, 542, 81], [517, 84, 542, 82, "length"], [517, 90, 542, 88], [517, 98, 542, 96], [517, 99, 542, 97], [518, 8, 543, 6], [518, 9, 543, 7], [518, 10, 543, 8], [518, 17, 543, 15, "tensorFlowError"], [518, 32, 543, 30], [518, 34, 543, 32], [519, 10, 544, 8, "console"], [519, 17, 544, 15], [519, 18, 544, 16, "warn"], [519, 22, 544, 20], [519, 23, 544, 21], [519, 61, 544, 59], [519, 63, 544, 61, "tensorFlowError"], [519, 78, 544, 76], [519, 79, 544, 77], [521, 10, 546, 8], [522, 10, 547, 8, "console"], [522, 17, 547, 15], [522, 18, 547, 16, "log"], [522, 21, 547, 19], [522, 22, 547, 20], [522, 86, 547, 84], [522, 87, 547, 85], [523, 10, 548, 8, "detectedFaces"], [523, 23, 548, 21], [523, 26, 548, 24, "detectFacesHeuristic"], [523, 46, 548, 44], [523, 47, 548, 45, "img"], [523, 50, 548, 48], [523, 52, 548, 50, "ctx"], [523, 55, 548, 53], [523, 56, 548, 54], [524, 10, 549, 8, "console"], [524, 17, 549, 15], [524, 18, 549, 16, "log"], [524, 21, 549, 19], [524, 22, 549, 20], [524, 70, 549, 68, "detectedFaces"], [524, 83, 549, 81], [524, 84, 549, 82, "length"], [524, 90, 549, 88], [524, 98, 549, 96], [524, 99, 549, 97], [525, 8, 550, 6], [526, 8, 552, 6, "console"], [526, 15, 552, 13], [526, 16, 552, 14, "log"], [526, 19, 552, 17], [526, 20, 552, 18], [526, 72, 552, 70, "detectedFaces"], [526, 85, 552, 83], [526, 86, 552, 84, "length"], [526, 92, 552, 90], [526, 100, 552, 98], [526, 101, 552, 99], [527, 8, 553, 6], [527, 12, 553, 10, "detectedFaces"], [527, 25, 553, 23], [527, 26, 553, 24, "length"], [527, 32, 553, 30], [527, 35, 553, 33], [527, 36, 553, 34], [527, 38, 553, 36], [528, 10, 554, 8, "console"], [528, 17, 554, 15], [528, 18, 554, 16, "log"], [528, 21, 554, 19], [528, 22, 554, 20], [528, 66, 554, 64], [528, 68, 554, 66, "detectedFaces"], [528, 81, 554, 79], [528, 82, 554, 80, "map"], [528, 85, 554, 83], [528, 86, 554, 84], [528, 87, 554, 85, "face"], [528, 91, 554, 89], [528, 93, 554, 91, "i"], [528, 94, 554, 92], [528, 100, 554, 98], [529, 12, 555, 10, "faceNumber"], [529, 22, 555, 20], [529, 24, 555, 22, "i"], [529, 25, 555, 23], [529, 28, 555, 26], [529, 29, 555, 27], [530, 12, 556, 10, "centerX"], [530, 19, 556, 17], [530, 21, 556, 19, "face"], [530, 25, 556, 23], [530, 26, 556, 24, "boundingBox"], [530, 37, 556, 35], [530, 38, 556, 36, "xCenter"], [530, 45, 556, 43], [531, 12, 557, 10, "centerY"], [531, 19, 557, 17], [531, 21, 557, 19, "face"], [531, 25, 557, 23], [531, 26, 557, 24, "boundingBox"], [531, 37, 557, 35], [531, 38, 557, 36, "yCenter"], [531, 45, 557, 43], [532, 12, 558, 10, "width"], [532, 17, 558, 15], [532, 19, 558, 17, "face"], [532, 23, 558, 21], [532, 24, 558, 22, "boundingBox"], [532, 35, 558, 33], [532, 36, 558, 34, "width"], [532, 41, 558, 39], [533, 12, 559, 10, "height"], [533, 18, 559, 16], [533, 20, 559, 18, "face"], [533, 24, 559, 22], [533, 25, 559, 23, "boundingBox"], [533, 36, 559, 34], [533, 37, 559, 35, "height"], [534, 10, 560, 8], [534, 11, 560, 9], [534, 12, 560, 10], [534, 13, 560, 11], [534, 14, 560, 12], [535, 8, 561, 6], [535, 9, 561, 7], [535, 15, 561, 13], [536, 10, 562, 8, "console"], [536, 17, 562, 15], [536, 18, 562, 16, "log"], [536, 21, 562, 19], [536, 22, 562, 20], [536, 91, 562, 89], [536, 92, 562, 90], [537, 8, 563, 6], [538, 8, 565, 6, "setProcessingProgress"], [538, 29, 565, 27], [538, 30, 565, 28], [538, 32, 565, 30], [538, 33, 565, 31], [540, 8, 567, 6], [541, 8, 568, 6], [541, 12, 568, 10, "detectedFaces"], [541, 25, 568, 23], [541, 26, 568, 24, "length"], [541, 32, 568, 30], [541, 35, 568, 33], [541, 36, 568, 34], [541, 38, 568, 36], [542, 10, 569, 8, "console"], [542, 17, 569, 15], [542, 18, 569, 16, "log"], [542, 21, 569, 19], [542, 22, 569, 20], [542, 61, 569, 59, "detectedFaces"], [542, 74, 569, 72], [542, 75, 569, 73, "length"], [542, 81, 569, 79], [542, 101, 569, 99], [542, 102, 569, 100], [543, 10, 571, 8, "detectedFaces"], [543, 23, 571, 21], [543, 24, 571, 22, "for<PERSON>ach"], [543, 31, 571, 29], [543, 32, 571, 30], [543, 33, 571, 31, "detection"], [543, 42, 571, 40], [543, 44, 571, 42, "index"], [543, 49, 571, 47], [543, 54, 571, 52], [544, 12, 572, 10], [544, 18, 572, 16, "bbox"], [544, 22, 572, 20], [544, 25, 572, 23, "detection"], [544, 34, 572, 32], [544, 35, 572, 33, "boundingBox"], [544, 46, 572, 44], [546, 12, 574, 10], [547, 12, 575, 10], [547, 18, 575, 16, "faceX"], [547, 23, 575, 21], [547, 26, 575, 24, "bbox"], [547, 30, 575, 28], [547, 31, 575, 29, "xCenter"], [547, 38, 575, 36], [547, 41, 575, 39, "img"], [547, 44, 575, 42], [547, 45, 575, 43, "width"], [547, 50, 575, 48], [547, 53, 575, 52, "bbox"], [547, 57, 575, 56], [547, 58, 575, 57, "width"], [547, 63, 575, 62], [547, 66, 575, 65, "img"], [547, 69, 575, 68], [547, 70, 575, 69, "width"], [547, 75, 575, 74], [547, 78, 575, 78], [547, 79, 575, 79], [548, 12, 576, 10], [548, 18, 576, 16, "faceY"], [548, 23, 576, 21], [548, 26, 576, 24, "bbox"], [548, 30, 576, 28], [548, 31, 576, 29, "yCenter"], [548, 38, 576, 36], [548, 41, 576, 39, "img"], [548, 44, 576, 42], [548, 45, 576, 43, "height"], [548, 51, 576, 49], [548, 54, 576, 53, "bbox"], [548, 58, 576, 57], [548, 59, 576, 58, "height"], [548, 65, 576, 64], [548, 68, 576, 67, "img"], [548, 71, 576, 70], [548, 72, 576, 71, "height"], [548, 78, 576, 77], [548, 81, 576, 81], [548, 82, 576, 82], [549, 12, 577, 10], [549, 18, 577, 16, "faceWidth"], [549, 27, 577, 25], [549, 30, 577, 28, "bbox"], [549, 34, 577, 32], [549, 35, 577, 33, "width"], [549, 40, 577, 38], [549, 43, 577, 41, "img"], [549, 46, 577, 44], [549, 47, 577, 45, "width"], [549, 52, 577, 50], [550, 12, 578, 10], [550, 18, 578, 16, "faceHeight"], [550, 28, 578, 26], [550, 31, 578, 29, "bbox"], [550, 35, 578, 33], [550, 36, 578, 34, "height"], [550, 42, 578, 40], [550, 45, 578, 43, "img"], [550, 48, 578, 46], [550, 49, 578, 47, "height"], [550, 55, 578, 53], [552, 12, 580, 10], [553, 12, 581, 10], [553, 18, 581, 16, "padding"], [553, 25, 581, 23], [553, 28, 581, 26], [553, 31, 581, 29], [554, 12, 582, 10], [554, 18, 582, 16, "paddedX"], [554, 25, 582, 23], [554, 28, 582, 26, "Math"], [554, 32, 582, 30], [554, 33, 582, 31, "max"], [554, 36, 582, 34], [554, 37, 582, 35], [554, 38, 582, 36], [554, 40, 582, 38, "faceX"], [554, 45, 582, 43], [554, 48, 582, 46, "faceWidth"], [554, 57, 582, 55], [554, 60, 582, 58, "padding"], [554, 67, 582, 65], [554, 68, 582, 66], [555, 12, 583, 10], [555, 18, 583, 16, "paddedY"], [555, 25, 583, 23], [555, 28, 583, 26, "Math"], [555, 32, 583, 30], [555, 33, 583, 31, "max"], [555, 36, 583, 34], [555, 37, 583, 35], [555, 38, 583, 36], [555, 40, 583, 38, "faceY"], [555, 45, 583, 43], [555, 48, 583, 46, "faceHeight"], [555, 58, 583, 56], [555, 61, 583, 59, "padding"], [555, 68, 583, 66], [555, 69, 583, 67], [556, 12, 584, 10], [556, 18, 584, 16, "<PERSON><PERSON><PERSON><PERSON>"], [556, 29, 584, 27], [556, 32, 584, 30, "Math"], [556, 36, 584, 34], [556, 37, 584, 35, "min"], [556, 40, 584, 38], [556, 41, 584, 39, "img"], [556, 44, 584, 42], [556, 45, 584, 43, "width"], [556, 50, 584, 48], [556, 53, 584, 51, "paddedX"], [556, 60, 584, 58], [556, 62, 584, 60, "faceWidth"], [556, 71, 584, 69], [556, 75, 584, 73], [556, 76, 584, 74], [556, 79, 584, 77], [556, 80, 584, 78], [556, 83, 584, 81, "padding"], [556, 90, 584, 88], [556, 91, 584, 89], [556, 92, 584, 90], [557, 12, 585, 10], [557, 18, 585, 16, "paddedHeight"], [557, 30, 585, 28], [557, 33, 585, 31, "Math"], [557, 37, 585, 35], [557, 38, 585, 36, "min"], [557, 41, 585, 39], [557, 42, 585, 40, "img"], [557, 45, 585, 43], [557, 46, 585, 44, "height"], [557, 52, 585, 50], [557, 55, 585, 53, "paddedY"], [557, 62, 585, 60], [557, 64, 585, 62, "faceHeight"], [557, 74, 585, 72], [557, 78, 585, 76], [557, 79, 585, 77], [557, 82, 585, 80], [557, 83, 585, 81], [557, 86, 585, 84, "padding"], [557, 93, 585, 91], [557, 94, 585, 92], [557, 95, 585, 93], [558, 12, 587, 10, "console"], [558, 19, 587, 17], [558, 20, 587, 18, "log"], [558, 23, 587, 21], [558, 24, 587, 22], [558, 60, 587, 58, "index"], [558, 65, 587, 63], [558, 68, 587, 66], [558, 69, 587, 67], [558, 72, 587, 70], [558, 74, 587, 72], [559, 14, 588, 12, "original"], [559, 22, 588, 20], [559, 24, 588, 22], [560, 16, 588, 24, "x"], [560, 17, 588, 25], [560, 19, 588, 27, "Math"], [560, 23, 588, 31], [560, 24, 588, 32, "round"], [560, 29, 588, 37], [560, 30, 588, 38, "faceX"], [560, 35, 588, 43], [560, 36, 588, 44], [561, 16, 588, 46, "y"], [561, 17, 588, 47], [561, 19, 588, 49, "Math"], [561, 23, 588, 53], [561, 24, 588, 54, "round"], [561, 29, 588, 59], [561, 30, 588, 60, "faceY"], [561, 35, 588, 65], [561, 36, 588, 66], [562, 16, 588, 68, "w"], [562, 17, 588, 69], [562, 19, 588, 71, "Math"], [562, 23, 588, 75], [562, 24, 588, 76, "round"], [562, 29, 588, 81], [562, 30, 588, 82, "faceWidth"], [562, 39, 588, 91], [562, 40, 588, 92], [563, 16, 588, 94, "h"], [563, 17, 588, 95], [563, 19, 588, 97, "Math"], [563, 23, 588, 101], [563, 24, 588, 102, "round"], [563, 29, 588, 107], [563, 30, 588, 108, "faceHeight"], [563, 40, 588, 118], [564, 14, 588, 120], [564, 15, 588, 121], [565, 14, 589, 12, "padded"], [565, 20, 589, 18], [565, 22, 589, 20], [566, 16, 589, 22, "x"], [566, 17, 589, 23], [566, 19, 589, 25, "Math"], [566, 23, 589, 29], [566, 24, 589, 30, "round"], [566, 29, 589, 35], [566, 30, 589, 36, "paddedX"], [566, 37, 589, 43], [566, 38, 589, 44], [567, 16, 589, 46, "y"], [567, 17, 589, 47], [567, 19, 589, 49, "Math"], [567, 23, 589, 53], [567, 24, 589, 54, "round"], [567, 29, 589, 59], [567, 30, 589, 60, "paddedY"], [567, 37, 589, 67], [567, 38, 589, 68], [568, 16, 589, 70, "w"], [568, 17, 589, 71], [568, 19, 589, 73, "Math"], [568, 23, 589, 77], [568, 24, 589, 78, "round"], [568, 29, 589, 83], [568, 30, 589, 84, "<PERSON><PERSON><PERSON><PERSON>"], [568, 41, 589, 95], [568, 42, 589, 96], [569, 16, 589, 98, "h"], [569, 17, 589, 99], [569, 19, 589, 101, "Math"], [569, 23, 589, 105], [569, 24, 589, 106, "round"], [569, 29, 589, 111], [569, 30, 589, 112, "paddedHeight"], [569, 42, 589, 124], [570, 14, 589, 126], [571, 12, 590, 10], [571, 13, 590, 11], [571, 14, 590, 12], [573, 12, 592, 10], [574, 12, 593, 10, "console"], [574, 19, 593, 17], [574, 20, 593, 18, "log"], [574, 23, 593, 21], [574, 24, 593, 22], [574, 70, 593, 68], [574, 72, 593, 70], [575, 14, 594, 12, "width"], [575, 19, 594, 17], [575, 21, 594, 19, "canvas"], [575, 27, 594, 25], [575, 28, 594, 26, "width"], [575, 33, 594, 31], [576, 14, 595, 12, "height"], [576, 20, 595, 18], [576, 22, 595, 20, "canvas"], [576, 28, 595, 26], [576, 29, 595, 27, "height"], [576, 35, 595, 33], [577, 14, 596, 12, "contextValid"], [577, 26, 596, 24], [577, 28, 596, 26], [577, 29, 596, 27], [577, 30, 596, 28, "ctx"], [578, 12, 597, 10], [578, 13, 597, 11], [578, 14, 597, 12], [580, 12, 599, 10], [581, 12, 600, 10, "applyStrongBlur"], [581, 27, 600, 25], [581, 28, 600, 26, "ctx"], [581, 31, 600, 29], [581, 33, 600, 31, "paddedX"], [581, 40, 600, 38], [581, 42, 600, 40, "paddedY"], [581, 49, 600, 47], [581, 51, 600, 49, "<PERSON><PERSON><PERSON><PERSON>"], [581, 62, 600, 60], [581, 64, 600, 62, "paddedHeight"], [581, 76, 600, 74], [581, 77, 600, 75], [583, 12, 602, 10], [584, 12, 603, 10, "console"], [584, 19, 603, 17], [584, 20, 603, 18, "log"], [584, 23, 603, 21], [584, 24, 603, 22], [584, 102, 603, 100], [584, 103, 603, 101], [586, 12, 605, 10], [587, 12, 606, 10], [587, 18, 606, 16, "testImageData"], [587, 31, 606, 29], [587, 34, 606, 32, "ctx"], [587, 37, 606, 35], [587, 38, 606, 36, "getImageData"], [587, 50, 606, 48], [587, 51, 606, 49, "paddedX"], [587, 58, 606, 56], [587, 61, 606, 59], [587, 63, 606, 61], [587, 65, 606, 63, "paddedY"], [587, 72, 606, 70], [587, 75, 606, 73], [587, 77, 606, 75], [587, 79, 606, 77], [587, 81, 606, 79], [587, 83, 606, 81], [587, 85, 606, 83], [587, 86, 606, 84], [588, 12, 607, 10, "console"], [588, 19, 607, 17], [588, 20, 607, 18, "log"], [588, 23, 607, 21], [588, 24, 607, 22], [588, 70, 607, 68], [588, 72, 607, 70], [589, 14, 608, 12, "firstPixel"], [589, 24, 608, 22], [589, 26, 608, 24], [589, 27, 608, 25, "testImageData"], [589, 40, 608, 38], [589, 41, 608, 39, "data"], [589, 45, 608, 43], [589, 46, 608, 44], [589, 47, 608, 45], [589, 48, 608, 46], [589, 50, 608, 48, "testImageData"], [589, 63, 608, 61], [589, 64, 608, 62, "data"], [589, 68, 608, 66], [589, 69, 608, 67], [589, 70, 608, 68], [589, 71, 608, 69], [589, 73, 608, 71, "testImageData"], [589, 86, 608, 84], [589, 87, 608, 85, "data"], [589, 91, 608, 89], [589, 92, 608, 90], [589, 93, 608, 91], [589, 94, 608, 92], [589, 95, 608, 93], [590, 14, 609, 12, "secondPixel"], [590, 25, 609, 23], [590, 27, 609, 25], [590, 28, 609, 26, "testImageData"], [590, 41, 609, 39], [590, 42, 609, 40, "data"], [590, 46, 609, 44], [590, 47, 609, 45], [590, 48, 609, 46], [590, 49, 609, 47], [590, 51, 609, 49, "testImageData"], [590, 64, 609, 62], [590, 65, 609, 63, "data"], [590, 69, 609, 67], [590, 70, 609, 68], [590, 71, 609, 69], [590, 72, 609, 70], [590, 74, 609, 72, "testImageData"], [590, 87, 609, 85], [590, 88, 609, 86, "data"], [590, 92, 609, 90], [590, 93, 609, 91], [590, 94, 609, 92], [590, 95, 609, 93], [591, 12, 610, 10], [591, 13, 610, 11], [591, 14, 610, 12], [592, 12, 612, 10, "console"], [592, 19, 612, 17], [592, 20, 612, 18, "log"], [592, 23, 612, 21], [592, 24, 612, 22], [592, 50, 612, 48, "index"], [592, 55, 612, 53], [592, 58, 612, 56], [592, 59, 612, 57], [592, 79, 612, 77], [592, 80, 612, 78], [593, 10, 613, 8], [593, 11, 613, 9], [593, 12, 613, 10], [594, 10, 615, 8, "console"], [594, 17, 615, 15], [594, 18, 615, 16, "log"], [594, 21, 615, 19], [594, 22, 615, 20], [594, 48, 615, 46, "detectedFaces"], [594, 61, 615, 59], [594, 62, 615, 60, "length"], [594, 68, 615, 66], [594, 104, 615, 102], [594, 105, 615, 103], [595, 8, 616, 6], [595, 9, 616, 7], [595, 15, 616, 13], [596, 10, 617, 8, "console"], [596, 17, 617, 15], [596, 18, 617, 16, "log"], [596, 21, 617, 19], [596, 22, 617, 20], [596, 109, 617, 107], [596, 110, 617, 108], [597, 10, 618, 8], [598, 10, 619, 8, "applyFallbackFaceBlur"], [598, 31, 619, 29], [598, 32, 619, 30, "ctx"], [598, 35, 619, 33], [598, 37, 619, 35, "img"], [598, 40, 619, 38], [598, 41, 619, 39, "width"], [598, 46, 619, 44], [598, 48, 619, 46, "img"], [598, 51, 619, 49], [598, 52, 619, 50, "height"], [598, 58, 619, 56], [598, 59, 619, 57], [599, 8, 620, 6], [600, 8, 622, 6, "setProcessingProgress"], [600, 29, 622, 27], [600, 30, 622, 28], [600, 32, 622, 30], [600, 33, 622, 31], [602, 8, 624, 6], [603, 8, 625, 6, "console"], [603, 15, 625, 13], [603, 16, 625, 14, "log"], [603, 19, 625, 17], [603, 20, 625, 18], [603, 85, 625, 83], [603, 86, 625, 84], [604, 8, 626, 6], [604, 14, 626, 12, "blurredImageBlob"], [604, 30, 626, 28], [604, 33, 626, 31], [604, 39, 626, 37], [604, 43, 626, 41, "Promise"], [604, 50, 626, 48], [604, 51, 626, 56, "resolve"], [604, 58, 626, 63], [604, 62, 626, 68], [605, 10, 627, 8, "canvas"], [605, 16, 627, 14], [605, 17, 627, 15, "toBlob"], [605, 23, 627, 21], [605, 24, 627, 23, "blob"], [605, 28, 627, 27], [605, 32, 627, 32, "resolve"], [605, 39, 627, 39], [605, 40, 627, 40, "blob"], [605, 44, 627, 45], [605, 45, 627, 46], [605, 47, 627, 48], [605, 59, 627, 60], [605, 61, 627, 62], [605, 64, 627, 65], [605, 65, 627, 66], [606, 8, 628, 6], [606, 9, 628, 7], [606, 10, 628, 8], [607, 8, 630, 6], [607, 14, 630, 12, "blurredImageUrl"], [607, 29, 630, 27], [607, 32, 630, 30, "URL"], [607, 35, 630, 33], [607, 36, 630, 34, "createObjectURL"], [607, 51, 630, 49], [607, 52, 630, 50, "blurredImageBlob"], [607, 68, 630, 66], [607, 69, 630, 67], [608, 8, 631, 6, "console"], [608, 15, 631, 13], [608, 16, 631, 14, "log"], [608, 19, 631, 17], [608, 20, 631, 18], [608, 66, 631, 64], [608, 68, 631, 66, "blurredImageUrl"], [608, 83, 631, 81], [608, 84, 631, 82, "substring"], [608, 93, 631, 91], [608, 94, 631, 92], [608, 95, 631, 93], [608, 97, 631, 95], [608, 99, 631, 97], [608, 100, 631, 98], [608, 103, 631, 101], [608, 108, 631, 106], [608, 109, 631, 107], [609, 8, 633, 6, "setProcessingProgress"], [609, 29, 633, 27], [609, 30, 633, 28], [609, 33, 633, 31], [609, 34, 633, 32], [611, 8, 635, 6], [612, 8, 636, 6], [612, 14, 636, 12, "completeProcessing"], [612, 32, 636, 30], [612, 33, 636, 31, "blurredImageUrl"], [612, 48, 636, 46], [612, 49, 636, 47], [613, 6, 638, 4], [613, 7, 638, 5], [613, 8, 638, 6], [613, 15, 638, 13, "error"], [613, 20, 638, 18], [613, 22, 638, 20], [614, 8, 639, 6, "console"], [614, 15, 639, 13], [614, 16, 639, 14, "error"], [614, 21, 639, 19], [614, 22, 639, 20], [614, 57, 639, 55], [614, 59, 639, 57, "error"], [614, 64, 639, 62], [614, 65, 639, 63], [615, 8, 640, 6, "setErrorMessage"], [615, 23, 640, 21], [615, 24, 640, 22], [615, 50, 640, 48], [615, 51, 640, 49], [616, 8, 641, 6, "setProcessingState"], [616, 26, 641, 24], [616, 27, 641, 25], [616, 34, 641, 32], [616, 35, 641, 33], [617, 6, 642, 4], [618, 4, 643, 2], [618, 5, 643, 3], [620, 4, 645, 2], [621, 4, 646, 2], [621, 10, 646, 8, "completeProcessing"], [621, 28, 646, 26], [621, 31, 646, 29], [621, 37, 646, 36, "blurredImageUrl"], [621, 52, 646, 59], [621, 56, 646, 64], [622, 6, 647, 4], [622, 10, 647, 8], [623, 8, 648, 6, "setProcessingState"], [623, 26, 648, 24], [623, 27, 648, 25], [623, 37, 648, 35], [623, 38, 648, 36], [625, 8, 650, 6], [626, 8, 651, 6], [626, 14, 651, 12, "timestamp"], [626, 23, 651, 21], [626, 26, 651, 24, "Date"], [626, 30, 651, 28], [626, 31, 651, 29, "now"], [626, 34, 651, 32], [626, 35, 651, 33], [626, 36, 651, 34], [627, 8, 652, 6], [627, 14, 652, 12, "result"], [627, 20, 652, 18], [627, 23, 652, 21], [628, 10, 653, 8, "imageUrl"], [628, 18, 653, 16], [628, 20, 653, 18, "blurredImageUrl"], [628, 35, 653, 33], [629, 10, 654, 8, "localUri"], [629, 18, 654, 16], [629, 20, 654, 18, "blurredImageUrl"], [629, 35, 654, 33], [630, 10, 655, 8, "challengeCode"], [630, 23, 655, 21], [630, 25, 655, 23, "challengeCode"], [630, 38, 655, 36], [630, 42, 655, 40], [630, 44, 655, 42], [631, 10, 656, 8, "timestamp"], [631, 19, 656, 17], [632, 10, 657, 8, "jobId"], [632, 15, 657, 13], [632, 17, 657, 15], [632, 27, 657, 25, "timestamp"], [632, 36, 657, 34], [632, 38, 657, 36], [633, 10, 658, 8, "status"], [633, 16, 658, 14], [633, 18, 658, 16], [634, 8, 659, 6], [634, 9, 659, 7], [635, 8, 661, 6, "console"], [635, 15, 661, 13], [635, 16, 661, 14, "log"], [635, 19, 661, 17], [635, 20, 661, 18], [635, 100, 661, 98], [635, 102, 661, 100], [636, 10, 662, 8, "imageUrl"], [636, 18, 662, 16], [636, 20, 662, 18, "blurredImageUrl"], [636, 35, 662, 33], [636, 36, 662, 34, "substring"], [636, 45, 662, 43], [636, 46, 662, 44], [636, 47, 662, 45], [636, 49, 662, 47], [636, 51, 662, 49], [636, 52, 662, 50], [636, 55, 662, 53], [636, 60, 662, 58], [637, 10, 663, 8, "timestamp"], [637, 19, 663, 17], [638, 10, 664, 8, "jobId"], [638, 15, 664, 13], [638, 17, 664, 15, "result"], [638, 23, 664, 21], [638, 24, 664, 22, "jobId"], [639, 8, 665, 6], [639, 9, 665, 7], [639, 10, 665, 8], [641, 8, 667, 6], [642, 8, 668, 6, "onComplete"], [642, 18, 668, 16], [642, 19, 668, 17, "result"], [642, 25, 668, 23], [642, 26, 668, 24], [643, 6, 670, 4], [643, 7, 670, 5], [643, 8, 670, 6], [643, 15, 670, 13, "error"], [643, 20, 670, 18], [643, 22, 670, 20], [644, 8, 671, 6, "console"], [644, 15, 671, 13], [644, 16, 671, 14, "error"], [644, 21, 671, 19], [644, 22, 671, 20], [644, 57, 671, 55], [644, 59, 671, 57, "error"], [644, 64, 671, 62], [644, 65, 671, 63], [645, 8, 672, 6, "setErrorMessage"], [645, 23, 672, 21], [645, 24, 672, 22], [645, 56, 672, 54], [645, 57, 672, 55], [646, 8, 673, 6, "setProcessingState"], [646, 26, 673, 24], [646, 27, 673, 25], [646, 34, 673, 32], [646, 35, 673, 33], [647, 6, 674, 4], [648, 4, 675, 2], [648, 5, 675, 3], [650, 4, 677, 2], [651, 4, 678, 2], [651, 10, 678, 8, "triggerServerProcessing"], [651, 33, 678, 31], [651, 36, 678, 34], [651, 42, 678, 34, "triggerServerProcessing"], [651, 43, 678, 41, "privateImageUrl"], [651, 58, 678, 64], [651, 60, 678, 66, "timestamp"], [651, 69, 678, 83], [651, 74, 678, 88], [652, 6, 679, 4], [652, 10, 679, 8], [653, 8, 680, 6, "console"], [653, 15, 680, 13], [653, 16, 680, 14, "log"], [653, 19, 680, 17], [653, 20, 680, 18], [653, 74, 680, 72], [653, 76, 680, 74, "privateImageUrl"], [653, 91, 680, 89], [653, 92, 680, 90], [654, 8, 681, 6, "setProcessingState"], [654, 26, 681, 24], [654, 27, 681, 25], [654, 39, 681, 37], [654, 40, 681, 38], [655, 8, 682, 6, "setProcessingProgress"], [655, 29, 682, 27], [655, 30, 682, 28], [655, 32, 682, 30], [655, 33, 682, 31], [656, 8, 684, 6], [656, 14, 684, 12, "requestBody"], [656, 25, 684, 23], [656, 28, 684, 26], [657, 10, 685, 8, "imageUrl"], [657, 18, 685, 16], [657, 20, 685, 18, "privateImageUrl"], [657, 35, 685, 33], [658, 10, 686, 8, "userId"], [658, 16, 686, 14], [659, 10, 687, 8, "requestId"], [659, 19, 687, 17], [660, 10, 688, 8, "timestamp"], [660, 19, 688, 17], [661, 10, 689, 8, "platform"], [661, 18, 689, 16], [661, 20, 689, 18], [662, 8, 690, 6], [662, 9, 690, 7], [663, 8, 692, 6, "console"], [663, 15, 692, 13], [663, 16, 692, 14, "log"], [663, 19, 692, 17], [663, 20, 692, 18], [663, 65, 692, 63], [663, 67, 692, 65, "requestBody"], [663, 78, 692, 76], [663, 79, 692, 77], [665, 8, 694, 6], [666, 8, 695, 6], [666, 14, 695, 12, "response"], [666, 22, 695, 20], [666, 25, 695, 23], [666, 31, 695, 29, "fetch"], [666, 36, 695, 34], [666, 37, 695, 35], [666, 40, 695, 38, "API_BASE_URL"], [666, 52, 695, 50], [666, 72, 695, 70], [666, 74, 695, 72], [667, 10, 696, 8, "method"], [667, 16, 696, 14], [667, 18, 696, 16], [667, 24, 696, 22], [668, 10, 697, 8, "headers"], [668, 17, 697, 15], [668, 19, 697, 17], [669, 12, 698, 10], [669, 26, 698, 24], [669, 28, 698, 26], [669, 46, 698, 44], [670, 12, 699, 10], [670, 27, 699, 25], [670, 29, 699, 27], [670, 39, 699, 37], [670, 45, 699, 43, "getAuthToken"], [670, 57, 699, 55], [670, 58, 699, 56], [670, 59, 699, 57], [671, 10, 700, 8], [671, 11, 700, 9], [672, 10, 701, 8, "body"], [672, 14, 701, 12], [672, 16, 701, 14, "JSON"], [672, 20, 701, 18], [672, 21, 701, 19, "stringify"], [672, 30, 701, 28], [672, 31, 701, 29, "requestBody"], [672, 42, 701, 40], [673, 8, 702, 6], [673, 9, 702, 7], [673, 10, 702, 8], [674, 8, 704, 6], [674, 12, 704, 10], [674, 13, 704, 11, "response"], [674, 21, 704, 19], [674, 22, 704, 20, "ok"], [674, 24, 704, 22], [674, 26, 704, 24], [675, 10, 705, 8], [675, 16, 705, 14, "errorText"], [675, 25, 705, 23], [675, 28, 705, 26], [675, 34, 705, 32, "response"], [675, 42, 705, 40], [675, 43, 705, 41, "text"], [675, 47, 705, 45], [675, 48, 705, 46], [675, 49, 705, 47], [676, 10, 706, 8, "console"], [676, 17, 706, 15], [676, 18, 706, 16, "error"], [676, 23, 706, 21], [676, 24, 706, 22], [676, 68, 706, 66], [676, 70, 706, 68, "response"], [676, 78, 706, 76], [676, 79, 706, 77, "status"], [676, 85, 706, 83], [676, 87, 706, 85, "errorText"], [676, 96, 706, 94], [676, 97, 706, 95], [677, 10, 707, 8], [677, 16, 707, 14], [677, 20, 707, 18, "Error"], [677, 25, 707, 23], [677, 26, 707, 24], [677, 48, 707, 46, "response"], [677, 56, 707, 54], [677, 57, 707, 55, "status"], [677, 63, 707, 61], [677, 67, 707, 65, "response"], [677, 75, 707, 73], [677, 76, 707, 74, "statusText"], [677, 86, 707, 84], [677, 88, 707, 86], [677, 89, 707, 87], [678, 8, 708, 6], [679, 8, 710, 6], [679, 14, 710, 12, "result"], [679, 20, 710, 18], [679, 23, 710, 21], [679, 29, 710, 27, "response"], [679, 37, 710, 35], [679, 38, 710, 36, "json"], [679, 42, 710, 40], [679, 43, 710, 41], [679, 44, 710, 42], [680, 8, 711, 6, "console"], [680, 15, 711, 13], [680, 16, 711, 14, "log"], [680, 19, 711, 17], [680, 20, 711, 18], [680, 68, 711, 66], [680, 70, 711, 68, "result"], [680, 76, 711, 74], [680, 77, 711, 75], [681, 8, 713, 6], [681, 12, 713, 10], [681, 13, 713, 11, "result"], [681, 19, 713, 17], [681, 20, 713, 18, "jobId"], [681, 25, 713, 23], [681, 27, 713, 25], [682, 10, 714, 8], [682, 16, 714, 14], [682, 20, 714, 18, "Error"], [682, 25, 714, 23], [682, 26, 714, 24], [682, 70, 714, 68], [682, 71, 714, 69], [683, 8, 715, 6], [685, 8, 717, 6], [686, 8, 718, 6], [686, 14, 718, 12, "pollForCompletion"], [686, 31, 718, 29], [686, 32, 718, 30, "result"], [686, 38, 718, 36], [686, 39, 718, 37, "jobId"], [686, 44, 718, 42], [686, 46, 718, 44, "timestamp"], [686, 55, 718, 53], [686, 56, 718, 54], [687, 6, 719, 4], [687, 7, 719, 5], [687, 8, 719, 6], [687, 15, 719, 13, "error"], [687, 20, 719, 18], [687, 22, 719, 20], [688, 8, 720, 6, "console"], [688, 15, 720, 13], [688, 16, 720, 14, "error"], [688, 21, 720, 19], [688, 22, 720, 20], [688, 57, 720, 55], [688, 59, 720, 57, "error"], [688, 64, 720, 62], [688, 65, 720, 63], [689, 8, 721, 6, "setErrorMessage"], [689, 23, 721, 21], [689, 24, 721, 22], [689, 52, 721, 50, "error"], [689, 57, 721, 55], [689, 58, 721, 56, "message"], [689, 65, 721, 63], [689, 67, 721, 65], [689, 68, 721, 66], [690, 8, 722, 6, "setProcessingState"], [690, 26, 722, 24], [690, 27, 722, 25], [690, 34, 722, 32], [690, 35, 722, 33], [691, 6, 723, 4], [692, 4, 724, 2], [692, 5, 724, 3], [693, 4, 725, 2], [694, 4, 726, 2], [694, 10, 726, 8, "pollForCompletion"], [694, 27, 726, 25], [694, 30, 726, 28], [694, 36, 726, 28, "pollForCompletion"], [694, 37, 726, 35, "jobId"], [694, 42, 726, 48], [694, 44, 726, 50, "timestamp"], [694, 53, 726, 67], [694, 55, 726, 69, "attempts"], [694, 63, 726, 77], [694, 66, 726, 80], [694, 67, 726, 81], [694, 72, 726, 86], [695, 6, 727, 4], [695, 12, 727, 10, "MAX_ATTEMPTS"], [695, 24, 727, 22], [695, 27, 727, 25], [695, 29, 727, 27], [695, 30, 727, 28], [695, 31, 727, 29], [696, 6, 728, 4], [696, 12, 728, 10, "POLL_INTERVAL"], [696, 25, 728, 23], [696, 28, 728, 26], [696, 32, 728, 30], [696, 33, 728, 31], [696, 34, 728, 32], [698, 6, 730, 4, "console"], [698, 13, 730, 11], [698, 14, 730, 12, "log"], [698, 17, 730, 15], [698, 18, 730, 16], [698, 53, 730, 51, "attempts"], [698, 61, 730, 59], [698, 64, 730, 62], [698, 65, 730, 63], [698, 69, 730, 67, "MAX_ATTEMPTS"], [698, 81, 730, 79], [698, 93, 730, 91, "jobId"], [698, 98, 730, 96], [698, 100, 730, 98], [698, 101, 730, 99], [699, 6, 732, 4], [699, 10, 732, 8, "attempts"], [699, 18, 732, 16], [699, 22, 732, 20, "MAX_ATTEMPTS"], [699, 34, 732, 32], [699, 36, 732, 34], [700, 8, 733, 6, "console"], [700, 15, 733, 13], [700, 16, 733, 14, "error"], [700, 21, 733, 19], [700, 22, 733, 20], [700, 75, 733, 73], [700, 76, 733, 74], [701, 8, 734, 6, "setErrorMessage"], [701, 23, 734, 21], [701, 24, 734, 22], [701, 63, 734, 61], [701, 64, 734, 62], [702, 8, 735, 6, "setProcessingState"], [702, 26, 735, 24], [702, 27, 735, 25], [702, 34, 735, 32], [702, 35, 735, 33], [703, 8, 736, 6], [704, 6, 737, 4], [705, 6, 739, 4], [705, 10, 739, 8], [706, 8, 740, 6], [706, 14, 740, 12, "response"], [706, 22, 740, 20], [706, 25, 740, 23], [706, 31, 740, 29, "fetch"], [706, 36, 740, 34], [706, 37, 740, 35], [706, 40, 740, 38, "API_BASE_URL"], [706, 52, 740, 50], [706, 75, 740, 73, "jobId"], [706, 80, 740, 78], [706, 82, 740, 80], [706, 84, 740, 82], [707, 10, 741, 8, "headers"], [707, 17, 741, 15], [707, 19, 741, 17], [708, 12, 742, 10], [708, 27, 742, 25], [708, 29, 742, 27], [708, 39, 742, 37], [708, 45, 742, 43, "getAuthToken"], [708, 57, 742, 55], [708, 58, 742, 56], [708, 59, 742, 57], [709, 10, 743, 8], [710, 8, 744, 6], [710, 9, 744, 7], [710, 10, 744, 8], [711, 8, 746, 6], [711, 12, 746, 10], [711, 13, 746, 11, "response"], [711, 21, 746, 19], [711, 22, 746, 20, "ok"], [711, 24, 746, 22], [711, 26, 746, 24], [712, 10, 747, 8], [712, 16, 747, 14], [712, 20, 747, 18, "Error"], [712, 25, 747, 23], [712, 26, 747, 24], [712, 34, 747, 32, "response"], [712, 42, 747, 40], [712, 43, 747, 41, "status"], [712, 49, 747, 47], [712, 54, 747, 52, "response"], [712, 62, 747, 60], [712, 63, 747, 61, "statusText"], [712, 73, 747, 71], [712, 75, 747, 73], [712, 76, 747, 74], [713, 8, 748, 6], [714, 8, 750, 6], [714, 14, 750, 12, "status"], [714, 20, 750, 18], [714, 23, 750, 21], [714, 29, 750, 27, "response"], [714, 37, 750, 35], [714, 38, 750, 36, "json"], [714, 42, 750, 40], [714, 43, 750, 41], [714, 44, 750, 42], [715, 8, 751, 6, "console"], [715, 15, 751, 13], [715, 16, 751, 14, "log"], [715, 19, 751, 17], [715, 20, 751, 18], [715, 54, 751, 52], [715, 56, 751, 54, "status"], [715, 62, 751, 60], [715, 63, 751, 61], [716, 8, 753, 6], [716, 12, 753, 10, "status"], [716, 18, 753, 16], [716, 19, 753, 17, "status"], [716, 25, 753, 23], [716, 30, 753, 28], [716, 41, 753, 39], [716, 43, 753, 41], [717, 10, 754, 8, "console"], [717, 17, 754, 15], [717, 18, 754, 16, "log"], [717, 21, 754, 19], [717, 22, 754, 20], [717, 73, 754, 71], [717, 74, 754, 72], [718, 10, 755, 8, "setProcessingProgress"], [718, 31, 755, 29], [718, 32, 755, 30], [718, 35, 755, 33], [718, 36, 755, 34], [719, 10, 756, 8, "setProcessingState"], [719, 28, 756, 26], [719, 29, 756, 27], [719, 40, 756, 38], [719, 41, 756, 39], [720, 10, 757, 8], [721, 10, 758, 8], [721, 16, 758, 14, "result"], [721, 22, 758, 20], [721, 25, 758, 23], [722, 12, 759, 10, "imageUrl"], [722, 20, 759, 18], [722, 22, 759, 20, "status"], [722, 28, 759, 26], [722, 29, 759, 27, "publicUrl"], [722, 38, 759, 36], [723, 12, 759, 38], [724, 12, 760, 10, "localUri"], [724, 20, 760, 18], [724, 22, 760, 20, "capturedPhoto"], [724, 35, 760, 33], [724, 39, 760, 37, "status"], [724, 45, 760, 43], [724, 46, 760, 44, "publicUrl"], [724, 55, 760, 53], [725, 12, 760, 55], [726, 12, 761, 10, "challengeCode"], [726, 25, 761, 23], [726, 27, 761, 25, "challengeCode"], [726, 40, 761, 38], [726, 44, 761, 42], [726, 46, 761, 44], [727, 12, 762, 10, "timestamp"], [727, 21, 762, 19], [728, 12, 763, 10, "processingStatus"], [728, 28, 763, 26], [728, 30, 763, 28], [729, 10, 764, 8], [729, 11, 764, 9], [730, 10, 765, 8, "console"], [730, 17, 765, 15], [730, 18, 765, 16, "log"], [730, 21, 765, 19], [730, 22, 765, 20], [730, 57, 765, 55], [730, 59, 765, 57, "result"], [730, 65, 765, 63], [730, 66, 765, 64], [731, 10, 766, 8, "onComplete"], [731, 20, 766, 18], [731, 21, 766, 19, "result"], [731, 27, 766, 25], [731, 28, 766, 26], [732, 10, 767, 8], [733, 8, 768, 6], [733, 9, 768, 7], [733, 15, 768, 13], [733, 19, 768, 17, "status"], [733, 25, 768, 23], [733, 26, 768, 24, "status"], [733, 32, 768, 30], [733, 37, 768, 35], [733, 45, 768, 43], [733, 47, 768, 45], [734, 10, 769, 8, "console"], [734, 17, 769, 15], [734, 18, 769, 16, "error"], [734, 23, 769, 21], [734, 24, 769, 22], [734, 60, 769, 58], [734, 62, 769, 60, "status"], [734, 68, 769, 66], [734, 69, 769, 67, "error"], [734, 74, 769, 72], [734, 75, 769, 73], [735, 10, 770, 8], [735, 16, 770, 14], [735, 20, 770, 18, "Error"], [735, 25, 770, 23], [735, 26, 770, 24, "status"], [735, 32, 770, 30], [735, 33, 770, 31, "error"], [735, 38, 770, 36], [735, 42, 770, 40], [735, 61, 770, 59], [735, 62, 770, 60], [736, 8, 771, 6], [736, 9, 771, 7], [736, 15, 771, 13], [737, 10, 772, 8], [738, 10, 773, 8], [738, 16, 773, 14, "progressValue"], [738, 29, 773, 27], [738, 32, 773, 30], [738, 34, 773, 32], [738, 37, 773, 36, "attempts"], [738, 45, 773, 44], [738, 48, 773, 47, "MAX_ATTEMPTS"], [738, 60, 773, 59], [738, 63, 773, 63], [738, 65, 773, 65], [739, 10, 774, 8, "console"], [739, 17, 774, 15], [739, 18, 774, 16, "log"], [739, 21, 774, 19], [739, 22, 774, 20], [739, 71, 774, 69, "progressValue"], [739, 84, 774, 82], [739, 87, 774, 85], [739, 88, 774, 86], [740, 10, 775, 8, "setProcessingProgress"], [740, 31, 775, 29], [740, 32, 775, 30, "progressValue"], [740, 45, 775, 43], [740, 46, 775, 44], [741, 10, 777, 8, "setTimeout"], [741, 20, 777, 18], [741, 21, 777, 19], [741, 27, 777, 25], [742, 12, 778, 10, "pollForCompletion"], [742, 29, 778, 27], [742, 30, 778, 28, "jobId"], [742, 35, 778, 33], [742, 37, 778, 35, "timestamp"], [742, 46, 778, 44], [742, 48, 778, 46, "attempts"], [742, 56, 778, 54], [742, 59, 778, 57], [742, 60, 778, 58], [742, 61, 778, 59], [743, 10, 779, 8], [743, 11, 779, 9], [743, 13, 779, 11, "POLL_INTERVAL"], [743, 26, 779, 24], [743, 27, 779, 25], [744, 8, 780, 6], [745, 6, 781, 4], [745, 7, 781, 5], [745, 8, 781, 6], [745, 15, 781, 13, "error"], [745, 20, 781, 18], [745, 22, 781, 20], [746, 8, 782, 6, "console"], [746, 15, 782, 13], [746, 16, 782, 14, "error"], [746, 21, 782, 19], [746, 22, 782, 20], [746, 54, 782, 52], [746, 56, 782, 54, "error"], [746, 61, 782, 59], [746, 62, 782, 60], [747, 8, 783, 6, "setErrorMessage"], [747, 23, 783, 21], [747, 24, 783, 22], [747, 62, 783, 60, "error"], [747, 67, 783, 65], [747, 68, 783, 66, "message"], [747, 75, 783, 73], [747, 77, 783, 75], [747, 78, 783, 76], [748, 8, 784, 6, "setProcessingState"], [748, 26, 784, 24], [748, 27, 784, 25], [748, 34, 784, 32], [748, 35, 784, 33], [749, 6, 785, 4], [750, 4, 786, 2], [750, 5, 786, 3], [751, 4, 787, 2], [752, 4, 788, 2], [752, 10, 788, 8, "getAuthToken"], [752, 22, 788, 20], [752, 25, 788, 23], [752, 31, 788, 23, "getAuthToken"], [752, 32, 788, 23], [752, 37, 788, 52], [753, 6, 789, 4], [754, 6, 790, 4], [755, 6, 791, 4], [755, 13, 791, 11], [755, 30, 791, 28], [756, 4, 792, 2], [756, 5, 792, 3], [758, 4, 794, 2], [759, 4, 795, 2], [759, 10, 795, 8, "retryCapture"], [759, 22, 795, 20], [759, 25, 795, 23], [759, 29, 795, 23, "useCallback"], [759, 47, 795, 34], [759, 49, 795, 35], [759, 55, 795, 41], [760, 6, 796, 4, "console"], [760, 13, 796, 11], [760, 14, 796, 12, "log"], [760, 17, 796, 15], [760, 18, 796, 16], [760, 55, 796, 53], [760, 56, 796, 54], [761, 6, 797, 4, "setProcessingState"], [761, 24, 797, 22], [761, 25, 797, 23], [761, 31, 797, 29], [761, 32, 797, 30], [762, 6, 798, 4, "setErrorMessage"], [762, 21, 798, 19], [762, 22, 798, 20], [762, 24, 798, 22], [762, 25, 798, 23], [763, 6, 799, 4, "setCapturedPhoto"], [763, 22, 799, 20], [763, 23, 799, 21], [763, 25, 799, 23], [763, 26, 799, 24], [764, 6, 800, 4, "setProcessingProgress"], [764, 27, 800, 25], [764, 28, 800, 26], [764, 29, 800, 27], [764, 30, 800, 28], [765, 4, 801, 2], [765, 5, 801, 3], [765, 7, 801, 5], [765, 9, 801, 7], [765, 10, 801, 8], [766, 4, 802, 2], [767, 4, 803, 2], [767, 8, 803, 2, "useEffect"], [767, 24, 803, 11], [767, 26, 803, 12], [767, 32, 803, 18], [768, 6, 804, 4, "console"], [768, 13, 804, 11], [768, 14, 804, 12, "log"], [768, 17, 804, 15], [768, 18, 804, 16], [768, 53, 804, 51], [768, 55, 804, 53, "permission"], [768, 65, 804, 63], [768, 66, 804, 64], [769, 6, 805, 4], [769, 10, 805, 8, "permission"], [769, 20, 805, 18], [769, 22, 805, 20], [770, 8, 806, 6, "console"], [770, 15, 806, 13], [770, 16, 806, 14, "log"], [770, 19, 806, 17], [770, 20, 806, 18], [770, 57, 806, 55], [770, 59, 806, 57, "permission"], [770, 69, 806, 67], [770, 70, 806, 68, "granted"], [770, 77, 806, 75], [770, 78, 806, 76], [771, 6, 807, 4], [772, 4, 808, 2], [772, 5, 808, 3], [772, 7, 808, 5], [772, 8, 808, 6, "permission"], [772, 18, 808, 16], [772, 19, 808, 17], [772, 20, 808, 18], [773, 4, 809, 2], [774, 4, 810, 2], [774, 8, 810, 6], [774, 9, 810, 7, "permission"], [774, 19, 810, 17], [774, 21, 810, 19], [775, 6, 811, 4, "console"], [775, 13, 811, 11], [775, 14, 811, 12, "log"], [775, 17, 811, 15], [775, 18, 811, 16], [775, 67, 811, 65], [775, 68, 811, 66], [776, 6, 812, 4], [776, 26, 813, 6], [776, 30, 813, 6, "_jsxDevRuntime"], [776, 44, 813, 6], [776, 45, 813, 6, "jsxDEV"], [776, 51, 813, 6], [776, 53, 813, 7, "_View"], [776, 58, 813, 7], [776, 59, 813, 7, "default"], [776, 66, 813, 11], [777, 8, 813, 12, "style"], [777, 13, 813, 17], [777, 15, 813, 19, "styles"], [777, 21, 813, 25], [777, 22, 813, 26, "container"], [777, 31, 813, 36], [778, 8, 813, 36, "children"], [778, 16, 813, 36], [778, 32, 814, 8], [778, 36, 814, 8, "_jsxDevRuntime"], [778, 50, 814, 8], [778, 51, 814, 8, "jsxDEV"], [778, 57, 814, 8], [778, 59, 814, 9, "_ActivityIndicator"], [778, 77, 814, 9], [778, 78, 814, 9, "default"], [778, 85, 814, 26], [779, 10, 814, 27, "size"], [779, 14, 814, 31], [779, 16, 814, 32], [779, 23, 814, 39], [780, 10, 814, 40, "color"], [780, 15, 814, 45], [780, 17, 814, 46], [781, 8, 814, 55], [782, 10, 814, 55, "fileName"], [782, 18, 814, 55], [782, 20, 814, 55, "_jsxFileName"], [782, 32, 814, 55], [783, 10, 814, 55, "lineNumber"], [783, 20, 814, 55], [784, 10, 814, 55, "columnNumber"], [784, 22, 814, 55], [785, 8, 814, 55], [785, 15, 814, 57], [785, 16, 814, 58], [785, 31, 815, 8], [785, 35, 815, 8, "_jsxDevRuntime"], [785, 49, 815, 8], [785, 50, 815, 8, "jsxDEV"], [785, 56, 815, 8], [785, 58, 815, 9, "_Text"], [785, 63, 815, 9], [785, 64, 815, 9, "default"], [785, 71, 815, 13], [786, 10, 815, 14, "style"], [786, 15, 815, 19], [786, 17, 815, 21, "styles"], [786, 23, 815, 27], [786, 24, 815, 28, "loadingText"], [786, 35, 815, 40], [787, 10, 815, 40, "children"], [787, 18, 815, 40], [787, 20, 815, 41], [788, 8, 815, 58], [789, 10, 815, 58, "fileName"], [789, 18, 815, 58], [789, 20, 815, 58, "_jsxFileName"], [789, 32, 815, 58], [790, 10, 815, 58, "lineNumber"], [790, 20, 815, 58], [791, 10, 815, 58, "columnNumber"], [791, 22, 815, 58], [792, 8, 815, 58], [792, 15, 815, 64], [792, 16, 815, 65], [793, 6, 815, 65], [794, 8, 815, 65, "fileName"], [794, 16, 815, 65], [794, 18, 815, 65, "_jsxFileName"], [794, 30, 815, 65], [795, 8, 815, 65, "lineNumber"], [795, 18, 815, 65], [796, 8, 815, 65, "columnNumber"], [796, 20, 815, 65], [797, 6, 815, 65], [797, 13, 816, 12], [797, 14, 816, 13], [798, 4, 818, 2], [799, 4, 819, 2], [799, 8, 819, 6], [799, 9, 819, 7, "permission"], [799, 19, 819, 17], [799, 20, 819, 18, "granted"], [799, 27, 819, 25], [799, 29, 819, 27], [800, 6, 820, 4, "console"], [800, 13, 820, 11], [800, 14, 820, 12, "log"], [800, 17, 820, 15], [800, 18, 820, 16], [800, 93, 820, 91], [800, 94, 820, 92], [801, 6, 821, 4], [801, 26, 822, 6], [801, 30, 822, 6, "_jsxDevRuntime"], [801, 44, 822, 6], [801, 45, 822, 6, "jsxDEV"], [801, 51, 822, 6], [801, 53, 822, 7, "_View"], [801, 58, 822, 7], [801, 59, 822, 7, "default"], [801, 66, 822, 11], [802, 8, 822, 12, "style"], [802, 13, 822, 17], [802, 15, 822, 19, "styles"], [802, 21, 822, 25], [802, 22, 822, 26, "container"], [802, 31, 822, 36], [803, 8, 822, 36, "children"], [803, 16, 822, 36], [803, 31, 823, 8], [803, 35, 823, 8, "_jsxDevRuntime"], [803, 49, 823, 8], [803, 50, 823, 8, "jsxDEV"], [803, 56, 823, 8], [803, 58, 823, 9, "_View"], [803, 63, 823, 9], [803, 64, 823, 9, "default"], [803, 71, 823, 13], [804, 10, 823, 14, "style"], [804, 15, 823, 19], [804, 17, 823, 21, "styles"], [804, 23, 823, 27], [804, 24, 823, 28, "permissionContent"], [804, 41, 823, 46], [805, 10, 823, 46, "children"], [805, 18, 823, 46], [805, 34, 824, 10], [805, 38, 824, 10, "_jsxDevRuntime"], [805, 52, 824, 10], [805, 53, 824, 10, "jsxDEV"], [805, 59, 824, 10], [805, 61, 824, 11, "_lucideReactNative"], [805, 79, 824, 11], [805, 80, 824, 11, "Camera"], [805, 86, 824, 21], [806, 12, 824, 22, "size"], [806, 16, 824, 26], [806, 18, 824, 28], [806, 20, 824, 31], [807, 12, 824, 32, "color"], [807, 17, 824, 37], [807, 19, 824, 38], [808, 10, 824, 47], [809, 12, 824, 47, "fileName"], [809, 20, 824, 47], [809, 22, 824, 47, "_jsxFileName"], [809, 34, 824, 47], [810, 12, 824, 47, "lineNumber"], [810, 22, 824, 47], [811, 12, 824, 47, "columnNumber"], [811, 24, 824, 47], [812, 10, 824, 47], [812, 17, 824, 49], [812, 18, 824, 50], [812, 33, 825, 10], [812, 37, 825, 10, "_jsxDevRuntime"], [812, 51, 825, 10], [812, 52, 825, 10, "jsxDEV"], [812, 58, 825, 10], [812, 60, 825, 11, "_Text"], [812, 65, 825, 11], [812, 66, 825, 11, "default"], [812, 73, 825, 15], [813, 12, 825, 16, "style"], [813, 17, 825, 21], [813, 19, 825, 23, "styles"], [813, 25, 825, 29], [813, 26, 825, 30, "permissionTitle"], [813, 41, 825, 46], [814, 12, 825, 46, "children"], [814, 20, 825, 46], [814, 22, 825, 47], [815, 10, 825, 73], [816, 12, 825, 73, "fileName"], [816, 20, 825, 73], [816, 22, 825, 73, "_jsxFileName"], [816, 34, 825, 73], [817, 12, 825, 73, "lineNumber"], [817, 22, 825, 73], [818, 12, 825, 73, "columnNumber"], [818, 24, 825, 73], [819, 10, 825, 73], [819, 17, 825, 79], [819, 18, 825, 80], [819, 33, 826, 10], [819, 37, 826, 10, "_jsxDevRuntime"], [819, 51, 826, 10], [819, 52, 826, 10, "jsxDEV"], [819, 58, 826, 10], [819, 60, 826, 11, "_Text"], [819, 65, 826, 11], [819, 66, 826, 11, "default"], [819, 73, 826, 15], [820, 12, 826, 16, "style"], [820, 17, 826, 21], [820, 19, 826, 23, "styles"], [820, 25, 826, 29], [820, 26, 826, 30, "permissionDescription"], [820, 47, 826, 52], [821, 12, 826, 52, "children"], [821, 20, 826, 52], [821, 22, 826, 53], [822, 10, 829, 10], [823, 12, 829, 10, "fileName"], [823, 20, 829, 10], [823, 22, 829, 10, "_jsxFileName"], [823, 34, 829, 10], [824, 12, 829, 10, "lineNumber"], [824, 22, 829, 10], [825, 12, 829, 10, "columnNumber"], [825, 24, 829, 10], [826, 10, 829, 10], [826, 17, 829, 16], [826, 18, 829, 17], [826, 33, 830, 10], [826, 37, 830, 10, "_jsxDevRuntime"], [826, 51, 830, 10], [826, 52, 830, 10, "jsxDEV"], [826, 58, 830, 10], [826, 60, 830, 11, "_TouchableOpacity"], [826, 77, 830, 11], [826, 78, 830, 11, "default"], [826, 85, 830, 27], [827, 12, 830, 28, "onPress"], [827, 19, 830, 35], [827, 21, 830, 37, "requestPermission"], [827, 38, 830, 55], [828, 12, 830, 56, "style"], [828, 17, 830, 61], [828, 19, 830, 63, "styles"], [828, 25, 830, 69], [828, 26, 830, 70, "primaryButton"], [828, 39, 830, 84], [829, 12, 830, 84, "children"], [829, 20, 830, 84], [829, 35, 831, 12], [829, 39, 831, 12, "_jsxDevRuntime"], [829, 53, 831, 12], [829, 54, 831, 12, "jsxDEV"], [829, 60, 831, 12], [829, 62, 831, 13, "_Text"], [829, 67, 831, 13], [829, 68, 831, 13, "default"], [829, 75, 831, 17], [830, 14, 831, 18, "style"], [830, 19, 831, 23], [830, 21, 831, 25, "styles"], [830, 27, 831, 31], [830, 28, 831, 32, "primaryButtonText"], [830, 45, 831, 50], [831, 14, 831, 50, "children"], [831, 22, 831, 50], [831, 24, 831, 51], [832, 12, 831, 67], [833, 14, 831, 67, "fileName"], [833, 22, 831, 67], [833, 24, 831, 67, "_jsxFileName"], [833, 36, 831, 67], [834, 14, 831, 67, "lineNumber"], [834, 24, 831, 67], [835, 14, 831, 67, "columnNumber"], [835, 26, 831, 67], [836, 12, 831, 67], [836, 19, 831, 73], [837, 10, 831, 74], [838, 12, 831, 74, "fileName"], [838, 20, 831, 74], [838, 22, 831, 74, "_jsxFileName"], [838, 34, 831, 74], [839, 12, 831, 74, "lineNumber"], [839, 22, 831, 74], [840, 12, 831, 74, "columnNumber"], [840, 24, 831, 74], [841, 10, 831, 74], [841, 17, 832, 28], [841, 18, 832, 29], [841, 33, 833, 10], [841, 37, 833, 10, "_jsxDevRuntime"], [841, 51, 833, 10], [841, 52, 833, 10, "jsxDEV"], [841, 58, 833, 10], [841, 60, 833, 11, "_TouchableOpacity"], [841, 77, 833, 11], [841, 78, 833, 11, "default"], [841, 85, 833, 27], [842, 12, 833, 28, "onPress"], [842, 19, 833, 35], [842, 21, 833, 37, "onCancel"], [842, 29, 833, 46], [843, 12, 833, 47, "style"], [843, 17, 833, 52], [843, 19, 833, 54, "styles"], [843, 25, 833, 60], [843, 26, 833, 61, "secondaryButton"], [843, 41, 833, 77], [844, 12, 833, 77, "children"], [844, 20, 833, 77], [844, 35, 834, 12], [844, 39, 834, 12, "_jsxDevRuntime"], [844, 53, 834, 12], [844, 54, 834, 12, "jsxDEV"], [844, 60, 834, 12], [844, 62, 834, 13, "_Text"], [844, 67, 834, 13], [844, 68, 834, 13, "default"], [844, 75, 834, 17], [845, 14, 834, 18, "style"], [845, 19, 834, 23], [845, 21, 834, 25, "styles"], [845, 27, 834, 31], [845, 28, 834, 32, "secondaryButtonText"], [845, 47, 834, 52], [846, 14, 834, 52, "children"], [846, 22, 834, 52], [846, 24, 834, 53], [847, 12, 834, 59], [848, 14, 834, 59, "fileName"], [848, 22, 834, 59], [848, 24, 834, 59, "_jsxFileName"], [848, 36, 834, 59], [849, 14, 834, 59, "lineNumber"], [849, 24, 834, 59], [850, 14, 834, 59, "columnNumber"], [850, 26, 834, 59], [851, 12, 834, 59], [851, 19, 834, 65], [852, 10, 834, 66], [853, 12, 834, 66, "fileName"], [853, 20, 834, 66], [853, 22, 834, 66, "_jsxFileName"], [853, 34, 834, 66], [854, 12, 834, 66, "lineNumber"], [854, 22, 834, 66], [855, 12, 834, 66, "columnNumber"], [855, 24, 834, 66], [856, 10, 834, 66], [856, 17, 835, 28], [856, 18, 835, 29], [857, 8, 835, 29], [858, 10, 835, 29, "fileName"], [858, 18, 835, 29], [858, 20, 835, 29, "_jsxFileName"], [858, 32, 835, 29], [859, 10, 835, 29, "lineNumber"], [859, 20, 835, 29], [860, 10, 835, 29, "columnNumber"], [860, 22, 835, 29], [861, 8, 835, 29], [861, 15, 836, 14], [862, 6, 836, 15], [863, 8, 836, 15, "fileName"], [863, 16, 836, 15], [863, 18, 836, 15, "_jsxFileName"], [863, 30, 836, 15], [864, 8, 836, 15, "lineNumber"], [864, 18, 836, 15], [865, 8, 836, 15, "columnNumber"], [865, 20, 836, 15], [866, 6, 836, 15], [866, 13, 837, 12], [866, 14, 837, 13], [867, 4, 839, 2], [868, 4, 840, 2], [869, 4, 841, 2, "console"], [869, 11, 841, 9], [869, 12, 841, 10, "log"], [869, 15, 841, 13], [869, 16, 841, 14], [869, 55, 841, 53], [869, 56, 841, 54], [870, 4, 843, 2], [870, 24, 844, 4], [870, 28, 844, 4, "_jsxDevRuntime"], [870, 42, 844, 4], [870, 43, 844, 4, "jsxDEV"], [870, 49, 844, 4], [870, 51, 844, 5, "_View"], [870, 56, 844, 5], [870, 57, 844, 5, "default"], [870, 64, 844, 9], [871, 6, 844, 10, "style"], [871, 11, 844, 15], [871, 13, 844, 17, "styles"], [871, 19, 844, 23], [871, 20, 844, 24, "container"], [871, 29, 844, 34], [872, 6, 844, 34, "children"], [872, 14, 844, 34], [872, 30, 846, 6], [872, 34, 846, 6, "_jsxDevRuntime"], [872, 48, 846, 6], [872, 49, 846, 6, "jsxDEV"], [872, 55, 846, 6], [872, 57, 846, 7, "_View"], [872, 62, 846, 7], [872, 63, 846, 7, "default"], [872, 70, 846, 11], [873, 8, 846, 12, "style"], [873, 13, 846, 17], [873, 15, 846, 19, "styles"], [873, 21, 846, 25], [873, 22, 846, 26, "cameraContainer"], [873, 37, 846, 42], [874, 8, 846, 43, "id"], [874, 10, 846, 45], [874, 12, 846, 46], [874, 29, 846, 63], [875, 8, 846, 63, "children"], [875, 16, 846, 63], [875, 32, 847, 8], [875, 36, 847, 8, "_jsxDevRuntime"], [875, 50, 847, 8], [875, 51, 847, 8, "jsxDEV"], [875, 57, 847, 8], [875, 59, 847, 9, "_expoCamera"], [875, 70, 847, 9], [875, 71, 847, 9, "CameraView"], [875, 81, 847, 19], [876, 10, 848, 10, "ref"], [876, 13, 848, 13], [876, 15, 848, 15, "cameraRef"], [876, 24, 848, 25], [877, 10, 849, 10, "style"], [877, 15, 849, 15], [877, 17, 849, 17], [877, 18, 849, 18, "styles"], [877, 24, 849, 24], [877, 25, 849, 25, "camera"], [877, 31, 849, 31], [877, 33, 849, 33], [878, 12, 849, 35, "backgroundColor"], [878, 27, 849, 50], [878, 29, 849, 52], [879, 10, 849, 62], [879, 11, 849, 63], [879, 12, 849, 65], [880, 10, 850, 10, "facing"], [880, 16, 850, 16], [880, 18, 850, 17], [880, 24, 850, 23], [881, 10, 851, 10, "onLayout"], [881, 18, 851, 18], [881, 20, 851, 21, "e"], [881, 21, 851, 22], [881, 25, 851, 27], [882, 12, 852, 12, "console"], [882, 19, 852, 19], [882, 20, 852, 20, "log"], [882, 23, 852, 23], [882, 24, 852, 24], [882, 56, 852, 56], [882, 58, 852, 58, "e"], [882, 59, 852, 59], [882, 60, 852, 60, "nativeEvent"], [882, 71, 852, 71], [882, 72, 852, 72, "layout"], [882, 78, 852, 78], [882, 79, 852, 79], [883, 12, 853, 12, "setViewSize"], [883, 23, 853, 23], [883, 24, 853, 24], [884, 14, 853, 26, "width"], [884, 19, 853, 31], [884, 21, 853, 33, "e"], [884, 22, 853, 34], [884, 23, 853, 35, "nativeEvent"], [884, 34, 853, 46], [884, 35, 853, 47, "layout"], [884, 41, 853, 53], [884, 42, 853, 54, "width"], [884, 47, 853, 59], [885, 14, 853, 61, "height"], [885, 20, 853, 67], [885, 22, 853, 69, "e"], [885, 23, 853, 70], [885, 24, 853, 71, "nativeEvent"], [885, 35, 853, 82], [885, 36, 853, 83, "layout"], [885, 42, 853, 89], [885, 43, 853, 90, "height"], [886, 12, 853, 97], [886, 13, 853, 98], [886, 14, 853, 99], [887, 10, 854, 10], [887, 11, 854, 12], [888, 10, 855, 10, "onCameraReady"], [888, 23, 855, 23], [888, 25, 855, 25, "onCameraReady"], [888, 26, 855, 25], [888, 31, 855, 31], [889, 12, 856, 12, "console"], [889, 19, 856, 19], [889, 20, 856, 20, "log"], [889, 23, 856, 23], [889, 24, 856, 24], [889, 55, 856, 55], [889, 56, 856, 56], [890, 12, 857, 12, "setIsCameraReady"], [890, 28, 857, 28], [890, 29, 857, 29], [890, 33, 857, 33], [890, 34, 857, 34], [890, 35, 857, 35], [890, 36, 857, 36], [891, 10, 858, 10], [891, 11, 858, 12], [892, 10, 859, 10, "onMountError"], [892, 22, 859, 22], [892, 24, 859, 25, "error"], [892, 29, 859, 30], [892, 33, 859, 35], [893, 12, 860, 12, "console"], [893, 19, 860, 19], [893, 20, 860, 20, "error"], [893, 25, 860, 25], [893, 26, 860, 26], [893, 63, 860, 63], [893, 65, 860, 65, "error"], [893, 70, 860, 70], [893, 71, 860, 71], [894, 12, 861, 12, "setErrorMessage"], [894, 27, 861, 27], [894, 28, 861, 28], [894, 57, 861, 57], [894, 58, 861, 58], [895, 12, 862, 12, "setProcessingState"], [895, 30, 862, 30], [895, 31, 862, 31], [895, 38, 862, 38], [895, 39, 862, 39], [896, 10, 863, 10], [897, 8, 863, 12], [898, 10, 863, 12, "fileName"], [898, 18, 863, 12], [898, 20, 863, 12, "_jsxFileName"], [898, 32, 863, 12], [899, 10, 863, 12, "lineNumber"], [899, 20, 863, 12], [900, 10, 863, 12, "columnNumber"], [900, 22, 863, 12], [901, 8, 863, 12], [901, 15, 864, 9], [901, 16, 864, 10], [901, 18, 866, 9], [901, 19, 866, 10, "isCameraReady"], [901, 32, 866, 23], [901, 49, 867, 10], [901, 53, 867, 10, "_jsxDevRuntime"], [901, 67, 867, 10], [901, 68, 867, 10, "jsxDEV"], [901, 74, 867, 10], [901, 76, 867, 11, "_View"], [901, 81, 867, 11], [901, 82, 867, 11, "default"], [901, 89, 867, 15], [902, 10, 867, 16, "style"], [902, 15, 867, 21], [902, 17, 867, 23], [902, 18, 867, 24, "StyleSheet"], [902, 37, 867, 34], [902, 38, 867, 35, "absoluteFill"], [902, 50, 867, 47], [902, 52, 867, 49], [903, 12, 867, 51, "backgroundColor"], [903, 27, 867, 66], [903, 29, 867, 68], [903, 49, 867, 88], [904, 12, 867, 90, "justifyContent"], [904, 26, 867, 104], [904, 28, 867, 106], [904, 36, 867, 114], [905, 12, 867, 116, "alignItems"], [905, 22, 867, 126], [905, 24, 867, 128], [905, 32, 867, 136], [906, 12, 867, 138, "zIndex"], [906, 18, 867, 144], [906, 20, 867, 146], [907, 10, 867, 151], [907, 11, 867, 152], [907, 12, 867, 154], [908, 10, 867, 154, "children"], [908, 18, 867, 154], [908, 33, 868, 12], [908, 37, 868, 12, "_jsxDevRuntime"], [908, 51, 868, 12], [908, 52, 868, 12, "jsxDEV"], [908, 58, 868, 12], [908, 60, 868, 13, "_View"], [908, 65, 868, 13], [908, 66, 868, 13, "default"], [908, 73, 868, 17], [909, 12, 868, 18, "style"], [909, 17, 868, 23], [909, 19, 868, 25], [910, 14, 868, 27, "backgroundColor"], [910, 29, 868, 42], [910, 31, 868, 44], [910, 51, 868, 64], [911, 14, 868, 66, "padding"], [911, 21, 868, 73], [911, 23, 868, 75], [911, 25, 868, 77], [912, 14, 868, 79, "borderRadius"], [912, 26, 868, 91], [912, 28, 868, 93], [912, 30, 868, 95], [913, 14, 868, 97, "alignItems"], [913, 24, 868, 107], [913, 26, 868, 109], [914, 12, 868, 118], [914, 13, 868, 120], [915, 12, 868, 120, "children"], [915, 20, 868, 120], [915, 36, 869, 14], [915, 40, 869, 14, "_jsxDevRuntime"], [915, 54, 869, 14], [915, 55, 869, 14, "jsxDEV"], [915, 61, 869, 14], [915, 63, 869, 15, "_ActivityIndicator"], [915, 81, 869, 15], [915, 82, 869, 15, "default"], [915, 89, 869, 32], [916, 14, 869, 33, "size"], [916, 18, 869, 37], [916, 20, 869, 38], [916, 27, 869, 45], [917, 14, 869, 46, "color"], [917, 19, 869, 51], [917, 21, 869, 52], [917, 30, 869, 61], [918, 14, 869, 62, "style"], [918, 19, 869, 67], [918, 21, 869, 69], [919, 16, 869, 71, "marginBottom"], [919, 28, 869, 83], [919, 30, 869, 85], [920, 14, 869, 88], [921, 12, 869, 90], [922, 14, 869, 90, "fileName"], [922, 22, 869, 90], [922, 24, 869, 90, "_jsxFileName"], [922, 36, 869, 90], [923, 14, 869, 90, "lineNumber"], [923, 24, 869, 90], [924, 14, 869, 90, "columnNumber"], [924, 26, 869, 90], [925, 12, 869, 90], [925, 19, 869, 92], [925, 20, 869, 93], [925, 35, 870, 14], [925, 39, 870, 14, "_jsxDevRuntime"], [925, 53, 870, 14], [925, 54, 870, 14, "jsxDEV"], [925, 60, 870, 14], [925, 62, 870, 15, "_Text"], [925, 67, 870, 15], [925, 68, 870, 15, "default"], [925, 75, 870, 19], [926, 14, 870, 20, "style"], [926, 19, 870, 25], [926, 21, 870, 27], [927, 16, 870, 29, "color"], [927, 21, 870, 34], [927, 23, 870, 36], [927, 29, 870, 42], [928, 16, 870, 44, "fontSize"], [928, 24, 870, 52], [928, 26, 870, 54], [928, 28, 870, 56], [929, 16, 870, 58, "fontWeight"], [929, 26, 870, 68], [929, 28, 870, 70], [930, 14, 870, 76], [930, 15, 870, 78], [931, 14, 870, 78, "children"], [931, 22, 870, 78], [931, 24, 870, 79], [932, 12, 870, 101], [933, 14, 870, 101, "fileName"], [933, 22, 870, 101], [933, 24, 870, 101, "_jsxFileName"], [933, 36, 870, 101], [934, 14, 870, 101, "lineNumber"], [934, 24, 870, 101], [935, 14, 870, 101, "columnNumber"], [935, 26, 870, 101], [936, 12, 870, 101], [936, 19, 870, 107], [936, 20, 870, 108], [936, 35, 871, 14], [936, 39, 871, 14, "_jsxDevRuntime"], [936, 53, 871, 14], [936, 54, 871, 14, "jsxDEV"], [936, 60, 871, 14], [936, 62, 871, 15, "_Text"], [936, 67, 871, 15], [936, 68, 871, 15, "default"], [936, 75, 871, 19], [937, 14, 871, 20, "style"], [937, 19, 871, 25], [937, 21, 871, 27], [938, 16, 871, 29, "color"], [938, 21, 871, 34], [938, 23, 871, 36], [938, 32, 871, 45], [939, 16, 871, 47, "fontSize"], [939, 24, 871, 55], [939, 26, 871, 57], [939, 28, 871, 59], [940, 16, 871, 61, "marginTop"], [940, 25, 871, 70], [940, 27, 871, 72], [941, 14, 871, 74], [941, 15, 871, 76], [942, 14, 871, 76, "children"], [942, 22, 871, 76], [942, 24, 871, 77], [943, 12, 871, 88], [944, 14, 871, 88, "fileName"], [944, 22, 871, 88], [944, 24, 871, 88, "_jsxFileName"], [944, 36, 871, 88], [945, 14, 871, 88, "lineNumber"], [945, 24, 871, 88], [946, 14, 871, 88, "columnNumber"], [946, 26, 871, 88], [947, 12, 871, 88], [947, 19, 871, 94], [947, 20, 871, 95], [948, 10, 871, 95], [949, 12, 871, 95, "fileName"], [949, 20, 871, 95], [949, 22, 871, 95, "_jsxFileName"], [949, 34, 871, 95], [950, 12, 871, 95, "lineNumber"], [950, 22, 871, 95], [951, 12, 871, 95, "columnNumber"], [951, 24, 871, 95], [952, 10, 871, 95], [952, 17, 872, 18], [953, 8, 872, 19], [954, 10, 872, 19, "fileName"], [954, 18, 872, 19], [954, 20, 872, 19, "_jsxFileName"], [954, 32, 872, 19], [955, 10, 872, 19, "lineNumber"], [955, 20, 872, 19], [956, 10, 872, 19, "columnNumber"], [956, 22, 872, 19], [957, 8, 872, 19], [957, 15, 873, 16], [957, 16, 874, 9], [957, 18, 877, 9, "isCameraReady"], [957, 31, 877, 22], [957, 35, 877, 26, "previewBlurEnabled"], [957, 53, 877, 44], [957, 57, 877, 48, "viewSize"], [957, 65, 877, 56], [957, 66, 877, 57, "width"], [957, 71, 877, 62], [957, 74, 877, 65], [957, 75, 877, 66], [957, 92, 878, 10], [957, 96, 878, 10, "_jsxDevRuntime"], [957, 110, 878, 10], [957, 111, 878, 10, "jsxDEV"], [957, 117, 878, 10], [957, 119, 878, 10, "_jsxDevRuntime"], [957, 133, 878, 10], [957, 134, 878, 10, "Fragment"], [957, 142, 878, 10], [958, 10, 878, 10, "children"], [958, 18, 878, 10], [958, 34, 880, 12], [958, 38, 880, 12, "_jsxDevRuntime"], [958, 52, 880, 12], [958, 53, 880, 12, "jsxDEV"], [958, 59, 880, 12], [958, 61, 880, 13, "_LiveFaceCanvas"], [958, 76, 880, 13], [958, 77, 880, 13, "default"], [958, 84, 880, 27], [959, 12, 880, 28, "containerId"], [959, 23, 880, 39], [959, 25, 880, 40], [959, 42, 880, 57], [960, 12, 880, 58, "width"], [960, 17, 880, 63], [960, 19, 880, 65, "viewSize"], [960, 27, 880, 73], [960, 28, 880, 74, "width"], [960, 33, 880, 80], [961, 12, 880, 81, "height"], [961, 18, 880, 87], [961, 20, 880, 89, "viewSize"], [961, 28, 880, 97], [961, 29, 880, 98, "height"], [962, 10, 880, 105], [963, 12, 880, 105, "fileName"], [963, 20, 880, 105], [963, 22, 880, 105, "_jsxFileName"], [963, 34, 880, 105], [964, 12, 880, 105, "lineNumber"], [964, 22, 880, 105], [965, 12, 880, 105, "columnNumber"], [965, 24, 880, 105], [966, 10, 880, 105], [966, 17, 880, 107], [966, 18, 880, 108], [966, 33, 881, 12], [966, 37, 881, 12, "_jsxDevRuntime"], [966, 51, 881, 12], [966, 52, 881, 12, "jsxDEV"], [966, 58, 881, 12], [966, 60, 881, 13, "_View"], [966, 65, 881, 13], [966, 66, 881, 13, "default"], [966, 73, 881, 17], [967, 12, 881, 18, "style"], [967, 17, 881, 23], [967, 19, 881, 25], [967, 20, 881, 26, "StyleSheet"], [967, 39, 881, 36], [967, 40, 881, 37, "absoluteFill"], [967, 52, 881, 49], [967, 54, 881, 51], [968, 14, 881, 53, "pointerEvents"], [968, 27, 881, 66], [968, 29, 881, 68], [969, 12, 881, 75], [969, 13, 881, 76], [969, 14, 881, 78], [970, 12, 881, 78, "children"], [970, 20, 881, 78], [970, 36, 883, 12], [970, 40, 883, 12, "_jsxDevRuntime"], [970, 54, 883, 12], [970, 55, 883, 12, "jsxDEV"], [970, 61, 883, 12], [970, 63, 883, 13, "_expoBlur"], [970, 72, 883, 13], [970, 73, 883, 13, "BlurView"], [970, 81, 883, 21], [971, 14, 883, 22, "intensity"], [971, 23, 883, 31], [971, 25, 883, 33], [971, 27, 883, 36], [972, 14, 883, 37, "tint"], [972, 18, 883, 41], [972, 20, 883, 42], [972, 26, 883, 48], [973, 14, 883, 49, "style"], [973, 19, 883, 54], [973, 21, 883, 56], [973, 22, 883, 57, "styles"], [973, 28, 883, 63], [973, 29, 883, 64, "blurZone"], [973, 37, 883, 72], [973, 39, 883, 74], [974, 16, 884, 14, "left"], [974, 20, 884, 18], [974, 22, 884, 20], [974, 23, 884, 21], [975, 16, 885, 14, "top"], [975, 19, 885, 17], [975, 21, 885, 19, "viewSize"], [975, 29, 885, 27], [975, 30, 885, 28, "height"], [975, 36, 885, 34], [975, 39, 885, 37], [975, 42, 885, 40], [976, 16, 886, 14, "width"], [976, 21, 886, 19], [976, 23, 886, 21, "viewSize"], [976, 31, 886, 29], [976, 32, 886, 30, "width"], [976, 37, 886, 35], [977, 16, 887, 14, "height"], [977, 22, 887, 20], [977, 24, 887, 22, "viewSize"], [977, 32, 887, 30], [977, 33, 887, 31, "height"], [977, 39, 887, 37], [977, 42, 887, 40], [977, 46, 887, 44], [978, 16, 888, 14, "borderRadius"], [978, 28, 888, 26], [978, 30, 888, 28], [979, 14, 889, 12], [979, 15, 889, 13], [980, 12, 889, 15], [981, 14, 889, 15, "fileName"], [981, 22, 889, 15], [981, 24, 889, 15, "_jsxFileName"], [981, 36, 889, 15], [982, 14, 889, 15, "lineNumber"], [982, 24, 889, 15], [983, 14, 889, 15, "columnNumber"], [983, 26, 889, 15], [984, 12, 889, 15], [984, 19, 889, 17], [984, 20, 889, 18], [984, 35, 891, 12], [984, 39, 891, 12, "_jsxDevRuntime"], [984, 53, 891, 12], [984, 54, 891, 12, "jsxDEV"], [984, 60, 891, 12], [984, 62, 891, 13, "_expoBlur"], [984, 71, 891, 13], [984, 72, 891, 13, "BlurView"], [984, 80, 891, 21], [985, 14, 891, 22, "intensity"], [985, 23, 891, 31], [985, 25, 891, 33], [985, 27, 891, 36], [986, 14, 891, 37, "tint"], [986, 18, 891, 41], [986, 20, 891, 42], [986, 26, 891, 48], [987, 14, 891, 49, "style"], [987, 19, 891, 54], [987, 21, 891, 56], [987, 22, 891, 57, "styles"], [987, 28, 891, 63], [987, 29, 891, 64, "blurZone"], [987, 37, 891, 72], [987, 39, 891, 74], [988, 16, 892, 14, "left"], [988, 20, 892, 18], [988, 22, 892, 20], [988, 23, 892, 21], [989, 16, 893, 14, "top"], [989, 19, 893, 17], [989, 21, 893, 19], [989, 22, 893, 20], [990, 16, 894, 14, "width"], [990, 21, 894, 19], [990, 23, 894, 21, "viewSize"], [990, 31, 894, 29], [990, 32, 894, 30, "width"], [990, 37, 894, 35], [991, 16, 895, 14, "height"], [991, 22, 895, 20], [991, 24, 895, 22, "viewSize"], [991, 32, 895, 30], [991, 33, 895, 31, "height"], [991, 39, 895, 37], [991, 42, 895, 40], [991, 45, 895, 43], [992, 16, 896, 14, "borderRadius"], [992, 28, 896, 26], [992, 30, 896, 28], [993, 14, 897, 12], [993, 15, 897, 13], [994, 12, 897, 15], [995, 14, 897, 15, "fileName"], [995, 22, 897, 15], [995, 24, 897, 15, "_jsxFileName"], [995, 36, 897, 15], [996, 14, 897, 15, "lineNumber"], [996, 24, 897, 15], [997, 14, 897, 15, "columnNumber"], [997, 26, 897, 15], [998, 12, 897, 15], [998, 19, 897, 17], [998, 20, 897, 18], [998, 35, 899, 12], [998, 39, 899, 12, "_jsxDevRuntime"], [998, 53, 899, 12], [998, 54, 899, 12, "jsxDEV"], [998, 60, 899, 12], [998, 62, 899, 13, "_expoBlur"], [998, 71, 899, 13], [998, 72, 899, 13, "BlurView"], [998, 80, 899, 21], [999, 14, 899, 22, "intensity"], [999, 23, 899, 31], [999, 25, 899, 33], [999, 27, 899, 36], [1000, 14, 899, 37, "tint"], [1000, 18, 899, 41], [1000, 20, 899, 42], [1000, 26, 899, 48], [1001, 14, 899, 49, "style"], [1001, 19, 899, 54], [1001, 21, 899, 56], [1001, 22, 899, 57, "styles"], [1001, 28, 899, 63], [1001, 29, 899, 64, "blurZone"], [1001, 37, 899, 72], [1001, 39, 899, 74], [1002, 16, 900, 14, "left"], [1002, 20, 900, 18], [1002, 22, 900, 20, "viewSize"], [1002, 30, 900, 28], [1002, 31, 900, 29, "width"], [1002, 36, 900, 34], [1002, 39, 900, 37], [1002, 42, 900, 40], [1002, 45, 900, 44, "viewSize"], [1002, 53, 900, 52], [1002, 54, 900, 53, "width"], [1002, 59, 900, 58], [1002, 62, 900, 61], [1002, 66, 900, 66], [1003, 16, 901, 14, "top"], [1003, 19, 901, 17], [1003, 21, 901, 19, "viewSize"], [1003, 29, 901, 27], [1003, 30, 901, 28, "height"], [1003, 36, 901, 34], [1003, 39, 901, 37], [1003, 43, 901, 41], [1003, 46, 901, 45, "viewSize"], [1003, 54, 901, 53], [1003, 55, 901, 54, "width"], [1003, 60, 901, 59], [1003, 63, 901, 62], [1003, 67, 901, 67], [1004, 16, 902, 14, "width"], [1004, 21, 902, 19], [1004, 23, 902, 21, "viewSize"], [1004, 31, 902, 29], [1004, 32, 902, 30, "width"], [1004, 37, 902, 35], [1004, 40, 902, 38], [1004, 43, 902, 41], [1005, 16, 903, 14, "height"], [1005, 22, 903, 20], [1005, 24, 903, 22, "viewSize"], [1005, 32, 903, 30], [1005, 33, 903, 31, "width"], [1005, 38, 903, 36], [1005, 41, 903, 39], [1005, 44, 903, 42], [1006, 16, 904, 14, "borderRadius"], [1006, 28, 904, 26], [1006, 30, 904, 29, "viewSize"], [1006, 38, 904, 37], [1006, 39, 904, 38, "width"], [1006, 44, 904, 43], [1006, 47, 904, 46], [1006, 50, 904, 49], [1006, 53, 904, 53], [1007, 14, 905, 12], [1007, 15, 905, 13], [1008, 12, 905, 15], [1009, 14, 905, 15, "fileName"], [1009, 22, 905, 15], [1009, 24, 905, 15, "_jsxFileName"], [1009, 36, 905, 15], [1010, 14, 905, 15, "lineNumber"], [1010, 24, 905, 15], [1011, 14, 905, 15, "columnNumber"], [1011, 26, 905, 15], [1012, 12, 905, 15], [1012, 19, 905, 17], [1012, 20, 905, 18], [1012, 35, 906, 12], [1012, 39, 906, 12, "_jsxDevRuntime"], [1012, 53, 906, 12], [1012, 54, 906, 12, "jsxDEV"], [1012, 60, 906, 12], [1012, 62, 906, 13, "_expoBlur"], [1012, 71, 906, 13], [1012, 72, 906, 13, "BlurView"], [1012, 80, 906, 21], [1013, 14, 906, 22, "intensity"], [1013, 23, 906, 31], [1013, 25, 906, 33], [1013, 27, 906, 36], [1014, 14, 906, 37, "tint"], [1014, 18, 906, 41], [1014, 20, 906, 42], [1014, 26, 906, 48], [1015, 14, 906, 49, "style"], [1015, 19, 906, 54], [1015, 21, 906, 56], [1015, 22, 906, 57, "styles"], [1015, 28, 906, 63], [1015, 29, 906, 64, "blurZone"], [1015, 37, 906, 72], [1015, 39, 906, 74], [1016, 16, 907, 14, "left"], [1016, 20, 907, 18], [1016, 22, 907, 20, "viewSize"], [1016, 30, 907, 28], [1016, 31, 907, 29, "width"], [1016, 36, 907, 34], [1016, 39, 907, 37], [1016, 42, 907, 40], [1016, 45, 907, 44, "viewSize"], [1016, 53, 907, 52], [1016, 54, 907, 53, "width"], [1016, 59, 907, 58], [1016, 62, 907, 61], [1016, 66, 907, 66], [1017, 16, 908, 14, "top"], [1017, 19, 908, 17], [1017, 21, 908, 19, "viewSize"], [1017, 29, 908, 27], [1017, 30, 908, 28, "height"], [1017, 36, 908, 34], [1017, 39, 908, 37], [1017, 42, 908, 40], [1017, 45, 908, 44, "viewSize"], [1017, 53, 908, 52], [1017, 54, 908, 53, "width"], [1017, 59, 908, 58], [1017, 62, 908, 61], [1017, 66, 908, 66], [1018, 16, 909, 14, "width"], [1018, 21, 909, 19], [1018, 23, 909, 21, "viewSize"], [1018, 31, 909, 29], [1018, 32, 909, 30, "width"], [1018, 37, 909, 35], [1018, 40, 909, 38], [1018, 43, 909, 41], [1019, 16, 910, 14, "height"], [1019, 22, 910, 20], [1019, 24, 910, 22, "viewSize"], [1019, 32, 910, 30], [1019, 33, 910, 31, "width"], [1019, 38, 910, 36], [1019, 41, 910, 39], [1019, 44, 910, 42], [1020, 16, 911, 14, "borderRadius"], [1020, 28, 911, 26], [1020, 30, 911, 29, "viewSize"], [1020, 38, 911, 37], [1020, 39, 911, 38, "width"], [1020, 44, 911, 43], [1020, 47, 911, 46], [1020, 50, 911, 49], [1020, 53, 911, 53], [1021, 14, 912, 12], [1021, 15, 912, 13], [1022, 12, 912, 15], [1023, 14, 912, 15, "fileName"], [1023, 22, 912, 15], [1023, 24, 912, 15, "_jsxFileName"], [1023, 36, 912, 15], [1024, 14, 912, 15, "lineNumber"], [1024, 24, 912, 15], [1025, 14, 912, 15, "columnNumber"], [1025, 26, 912, 15], [1026, 12, 912, 15], [1026, 19, 912, 17], [1026, 20, 912, 18], [1026, 35, 913, 12], [1026, 39, 913, 12, "_jsxDevRuntime"], [1026, 53, 913, 12], [1026, 54, 913, 12, "jsxDEV"], [1026, 60, 913, 12], [1026, 62, 913, 13, "_expoBlur"], [1026, 71, 913, 13], [1026, 72, 913, 13, "BlurView"], [1026, 80, 913, 21], [1027, 14, 913, 22, "intensity"], [1027, 23, 913, 31], [1027, 25, 913, 33], [1027, 27, 913, 36], [1028, 14, 913, 37, "tint"], [1028, 18, 913, 41], [1028, 20, 913, 42], [1028, 26, 913, 48], [1029, 14, 913, 49, "style"], [1029, 19, 913, 54], [1029, 21, 913, 56], [1029, 22, 913, 57, "styles"], [1029, 28, 913, 63], [1029, 29, 913, 64, "blurZone"], [1029, 37, 913, 72], [1029, 39, 913, 74], [1030, 16, 914, 14, "left"], [1030, 20, 914, 18], [1030, 22, 914, 20, "viewSize"], [1030, 30, 914, 28], [1030, 31, 914, 29, "width"], [1030, 36, 914, 34], [1030, 39, 914, 37], [1030, 42, 914, 40], [1030, 45, 914, 44, "viewSize"], [1030, 53, 914, 52], [1030, 54, 914, 53, "width"], [1030, 59, 914, 58], [1030, 62, 914, 61], [1030, 66, 914, 66], [1031, 16, 915, 14, "top"], [1031, 19, 915, 17], [1031, 21, 915, 19, "viewSize"], [1031, 29, 915, 27], [1031, 30, 915, 28, "height"], [1031, 36, 915, 34], [1031, 39, 915, 37], [1031, 42, 915, 40], [1031, 45, 915, 44, "viewSize"], [1031, 53, 915, 52], [1031, 54, 915, 53, "width"], [1031, 59, 915, 58], [1031, 62, 915, 61], [1031, 66, 915, 66], [1032, 16, 916, 14, "width"], [1032, 21, 916, 19], [1032, 23, 916, 21, "viewSize"], [1032, 31, 916, 29], [1032, 32, 916, 30, "width"], [1032, 37, 916, 35], [1032, 40, 916, 38], [1032, 43, 916, 41], [1033, 16, 917, 14, "height"], [1033, 22, 917, 20], [1033, 24, 917, 22, "viewSize"], [1033, 32, 917, 30], [1033, 33, 917, 31, "width"], [1033, 38, 917, 36], [1033, 41, 917, 39], [1033, 44, 917, 42], [1034, 16, 918, 14, "borderRadius"], [1034, 28, 918, 26], [1034, 30, 918, 29, "viewSize"], [1034, 38, 918, 37], [1034, 39, 918, 38, "width"], [1034, 44, 918, 43], [1034, 47, 918, 46], [1034, 50, 918, 49], [1034, 53, 918, 53], [1035, 14, 919, 12], [1035, 15, 919, 13], [1036, 12, 919, 15], [1037, 14, 919, 15, "fileName"], [1037, 22, 919, 15], [1037, 24, 919, 15, "_jsxFileName"], [1037, 36, 919, 15], [1038, 14, 919, 15, "lineNumber"], [1038, 24, 919, 15], [1039, 14, 919, 15, "columnNumber"], [1039, 26, 919, 15], [1040, 12, 919, 15], [1040, 19, 919, 17], [1040, 20, 919, 18], [1040, 22, 921, 13, "__DEV__"], [1040, 29, 921, 20], [1040, 46, 922, 14], [1040, 50, 922, 14, "_jsxDevRuntime"], [1040, 64, 922, 14], [1040, 65, 922, 14, "jsxDEV"], [1040, 71, 922, 14], [1040, 73, 922, 15, "_View"], [1040, 78, 922, 15], [1040, 79, 922, 15, "default"], [1040, 86, 922, 19], [1041, 14, 922, 20, "style"], [1041, 19, 922, 25], [1041, 21, 922, 27, "styles"], [1041, 27, 922, 33], [1041, 28, 922, 34, "previewChip"], [1041, 39, 922, 46], [1042, 14, 922, 46, "children"], [1042, 22, 922, 46], [1042, 37, 923, 16], [1042, 41, 923, 16, "_jsxDevRuntime"], [1042, 55, 923, 16], [1042, 56, 923, 16, "jsxDEV"], [1042, 62, 923, 16], [1042, 64, 923, 17, "_Text"], [1042, 69, 923, 17], [1042, 70, 923, 17, "default"], [1042, 77, 923, 21], [1043, 16, 923, 22, "style"], [1043, 21, 923, 27], [1043, 23, 923, 29, "styles"], [1043, 29, 923, 35], [1043, 30, 923, 36, "previewChipText"], [1043, 45, 923, 52], [1044, 16, 923, 52, "children"], [1044, 24, 923, 52], [1044, 26, 923, 53], [1045, 14, 923, 73], [1046, 16, 923, 73, "fileName"], [1046, 24, 923, 73], [1046, 26, 923, 73, "_jsxFileName"], [1046, 38, 923, 73], [1047, 16, 923, 73, "lineNumber"], [1047, 26, 923, 73], [1048, 16, 923, 73, "columnNumber"], [1048, 28, 923, 73], [1049, 14, 923, 73], [1049, 21, 923, 79], [1050, 12, 923, 80], [1051, 14, 923, 80, "fileName"], [1051, 22, 923, 80], [1051, 24, 923, 80, "_jsxFileName"], [1051, 36, 923, 80], [1052, 14, 923, 80, "lineNumber"], [1052, 24, 923, 80], [1053, 14, 923, 80, "columnNumber"], [1053, 26, 923, 80], [1054, 12, 923, 80], [1054, 19, 924, 20], [1054, 20, 925, 13], [1055, 10, 925, 13], [1056, 12, 925, 13, "fileName"], [1056, 20, 925, 13], [1056, 22, 925, 13, "_jsxFileName"], [1056, 34, 925, 13], [1057, 12, 925, 13, "lineNumber"], [1057, 22, 925, 13], [1058, 12, 925, 13, "columnNumber"], [1058, 24, 925, 13], [1059, 10, 925, 13], [1059, 17, 926, 18], [1059, 18, 926, 19], [1060, 8, 926, 19], [1060, 23, 927, 12], [1060, 24, 928, 9], [1060, 26, 930, 9, "isCameraReady"], [1060, 39, 930, 22], [1060, 56, 931, 10], [1060, 60, 931, 10, "_jsxDevRuntime"], [1060, 74, 931, 10], [1060, 75, 931, 10, "jsxDEV"], [1060, 81, 931, 10], [1060, 83, 931, 10, "_jsxDevRuntime"], [1060, 97, 931, 10], [1060, 98, 931, 10, "Fragment"], [1060, 106, 931, 10], [1061, 10, 931, 10, "children"], [1061, 18, 931, 10], [1061, 34, 933, 12], [1061, 38, 933, 12, "_jsxDevRuntime"], [1061, 52, 933, 12], [1061, 53, 933, 12, "jsxDEV"], [1061, 59, 933, 12], [1061, 61, 933, 13, "_View"], [1061, 66, 933, 13], [1061, 67, 933, 13, "default"], [1061, 74, 933, 17], [1062, 12, 933, 18, "style"], [1062, 17, 933, 23], [1062, 19, 933, 25, "styles"], [1062, 25, 933, 31], [1062, 26, 933, 32, "headerOverlay"], [1062, 39, 933, 46], [1063, 12, 933, 46, "children"], [1063, 20, 933, 46], [1063, 35, 934, 14], [1063, 39, 934, 14, "_jsxDevRuntime"], [1063, 53, 934, 14], [1063, 54, 934, 14, "jsxDEV"], [1063, 60, 934, 14], [1063, 62, 934, 15, "_View"], [1063, 67, 934, 15], [1063, 68, 934, 15, "default"], [1063, 75, 934, 19], [1064, 14, 934, 20, "style"], [1064, 19, 934, 25], [1064, 21, 934, 27, "styles"], [1064, 27, 934, 33], [1064, 28, 934, 34, "headerContent"], [1064, 41, 934, 48], [1065, 14, 934, 48, "children"], [1065, 22, 934, 48], [1065, 38, 935, 16], [1065, 42, 935, 16, "_jsxDevRuntime"], [1065, 56, 935, 16], [1065, 57, 935, 16, "jsxDEV"], [1065, 63, 935, 16], [1065, 65, 935, 17, "_View"], [1065, 70, 935, 17], [1065, 71, 935, 17, "default"], [1065, 78, 935, 21], [1066, 16, 935, 22, "style"], [1066, 21, 935, 27], [1066, 23, 935, 29, "styles"], [1066, 29, 935, 35], [1066, 30, 935, 36, "headerLeft"], [1066, 40, 935, 47], [1067, 16, 935, 47, "children"], [1067, 24, 935, 47], [1067, 40, 936, 18], [1067, 44, 936, 18, "_jsxDevRuntime"], [1067, 58, 936, 18], [1067, 59, 936, 18, "jsxDEV"], [1067, 65, 936, 18], [1067, 67, 936, 19, "_Text"], [1067, 72, 936, 19], [1067, 73, 936, 19, "default"], [1067, 80, 936, 23], [1068, 18, 936, 24, "style"], [1068, 23, 936, 29], [1068, 25, 936, 31, "styles"], [1068, 31, 936, 37], [1068, 32, 936, 38, "headerTitle"], [1068, 43, 936, 50], [1069, 18, 936, 50, "children"], [1069, 26, 936, 50], [1069, 28, 936, 51], [1070, 16, 936, 62], [1071, 18, 936, 62, "fileName"], [1071, 26, 936, 62], [1071, 28, 936, 62, "_jsxFileName"], [1071, 40, 936, 62], [1072, 18, 936, 62, "lineNumber"], [1072, 28, 936, 62], [1073, 18, 936, 62, "columnNumber"], [1073, 30, 936, 62], [1074, 16, 936, 62], [1074, 23, 936, 68], [1074, 24, 936, 69], [1074, 39, 937, 18], [1074, 43, 937, 18, "_jsxDevRuntime"], [1074, 57, 937, 18], [1074, 58, 937, 18, "jsxDEV"], [1074, 64, 937, 18], [1074, 66, 937, 19, "_View"], [1074, 71, 937, 19], [1074, 72, 937, 19, "default"], [1074, 79, 937, 23], [1075, 18, 937, 24, "style"], [1075, 23, 937, 29], [1075, 25, 937, 31, "styles"], [1075, 31, 937, 37], [1075, 32, 937, 38, "subtitleRow"], [1075, 43, 937, 50], [1076, 18, 937, 50, "children"], [1076, 26, 937, 50], [1076, 42, 938, 20], [1076, 46, 938, 20, "_jsxDevRuntime"], [1076, 60, 938, 20], [1076, 61, 938, 20, "jsxDEV"], [1076, 67, 938, 20], [1076, 69, 938, 21, "_Text"], [1076, 74, 938, 21], [1076, 75, 938, 21, "default"], [1076, 82, 938, 25], [1077, 20, 938, 26, "style"], [1077, 25, 938, 31], [1077, 27, 938, 33, "styles"], [1077, 33, 938, 39], [1077, 34, 938, 40, "webIcon"], [1077, 41, 938, 48], [1078, 20, 938, 48, "children"], [1078, 28, 938, 48], [1078, 30, 938, 49], [1079, 18, 938, 51], [1080, 20, 938, 51, "fileName"], [1080, 28, 938, 51], [1080, 30, 938, 51, "_jsxFileName"], [1080, 42, 938, 51], [1081, 20, 938, 51, "lineNumber"], [1081, 30, 938, 51], [1082, 20, 938, 51, "columnNumber"], [1082, 32, 938, 51], [1083, 18, 938, 51], [1083, 25, 938, 57], [1083, 26, 938, 58], [1083, 41, 939, 20], [1083, 45, 939, 20, "_jsxDevRuntime"], [1083, 59, 939, 20], [1083, 60, 939, 20, "jsxDEV"], [1083, 66, 939, 20], [1083, 68, 939, 21, "_Text"], [1083, 73, 939, 21], [1083, 74, 939, 21, "default"], [1083, 81, 939, 25], [1084, 20, 939, 26, "style"], [1084, 25, 939, 31], [1084, 27, 939, 33, "styles"], [1084, 33, 939, 39], [1084, 34, 939, 40, "headerSubtitle"], [1084, 48, 939, 55], [1085, 20, 939, 55, "children"], [1085, 28, 939, 55], [1085, 30, 939, 56], [1086, 18, 939, 71], [1087, 20, 939, 71, "fileName"], [1087, 28, 939, 71], [1087, 30, 939, 71, "_jsxFileName"], [1087, 42, 939, 71], [1088, 20, 939, 71, "lineNumber"], [1088, 30, 939, 71], [1089, 20, 939, 71, "columnNumber"], [1089, 32, 939, 71], [1090, 18, 939, 71], [1090, 25, 939, 77], [1090, 26, 939, 78], [1091, 16, 939, 78], [1092, 18, 939, 78, "fileName"], [1092, 26, 939, 78], [1092, 28, 939, 78, "_jsxFileName"], [1092, 40, 939, 78], [1093, 18, 939, 78, "lineNumber"], [1093, 28, 939, 78], [1094, 18, 939, 78, "columnNumber"], [1094, 30, 939, 78], [1095, 16, 939, 78], [1095, 23, 940, 24], [1095, 24, 940, 25], [1095, 26, 941, 19, "challengeCode"], [1095, 39, 941, 32], [1095, 56, 942, 20], [1095, 60, 942, 20, "_jsxDevRuntime"], [1095, 74, 942, 20], [1095, 75, 942, 20, "jsxDEV"], [1095, 81, 942, 20], [1095, 83, 942, 21, "_View"], [1095, 88, 942, 21], [1095, 89, 942, 21, "default"], [1095, 96, 942, 25], [1096, 18, 942, 26, "style"], [1096, 23, 942, 31], [1096, 25, 942, 33, "styles"], [1096, 31, 942, 39], [1096, 32, 942, 40, "challengeRow"], [1096, 44, 942, 53], [1097, 18, 942, 53, "children"], [1097, 26, 942, 53], [1097, 42, 943, 22], [1097, 46, 943, 22, "_jsxDevRuntime"], [1097, 60, 943, 22], [1097, 61, 943, 22, "jsxDEV"], [1097, 67, 943, 22], [1097, 69, 943, 23, "_lucideReactNative"], [1097, 87, 943, 23], [1097, 88, 943, 23, "Shield"], [1097, 94, 943, 29], [1098, 20, 943, 30, "size"], [1098, 24, 943, 34], [1098, 26, 943, 36], [1098, 28, 943, 39], [1099, 20, 943, 40, "color"], [1099, 25, 943, 45], [1099, 27, 943, 46], [1100, 18, 943, 52], [1101, 20, 943, 52, "fileName"], [1101, 28, 943, 52], [1101, 30, 943, 52, "_jsxFileName"], [1101, 42, 943, 52], [1102, 20, 943, 52, "lineNumber"], [1102, 30, 943, 52], [1103, 20, 943, 52, "columnNumber"], [1103, 32, 943, 52], [1104, 18, 943, 52], [1104, 25, 943, 54], [1104, 26, 943, 55], [1104, 41, 944, 22], [1104, 45, 944, 22, "_jsxDevRuntime"], [1104, 59, 944, 22], [1104, 60, 944, 22, "jsxDEV"], [1104, 66, 944, 22], [1104, 68, 944, 23, "_Text"], [1104, 73, 944, 23], [1104, 74, 944, 23, "default"], [1104, 81, 944, 27], [1105, 20, 944, 28, "style"], [1105, 25, 944, 33], [1105, 27, 944, 35, "styles"], [1105, 33, 944, 41], [1105, 34, 944, 42, "challengeCode"], [1105, 47, 944, 56], [1106, 20, 944, 56, "children"], [1106, 28, 944, 56], [1106, 30, 944, 58, "challengeCode"], [1107, 18, 944, 71], [1108, 20, 944, 71, "fileName"], [1108, 28, 944, 71], [1108, 30, 944, 71, "_jsxFileName"], [1108, 42, 944, 71], [1109, 20, 944, 71, "lineNumber"], [1109, 30, 944, 71], [1110, 20, 944, 71, "columnNumber"], [1110, 32, 944, 71], [1111, 18, 944, 71], [1111, 25, 944, 78], [1111, 26, 944, 79], [1112, 16, 944, 79], [1113, 18, 944, 79, "fileName"], [1113, 26, 944, 79], [1113, 28, 944, 79, "_jsxFileName"], [1113, 40, 944, 79], [1114, 18, 944, 79, "lineNumber"], [1114, 28, 944, 79], [1115, 18, 944, 79, "columnNumber"], [1115, 30, 944, 79], [1116, 16, 944, 79], [1116, 23, 945, 26], [1116, 24, 946, 19], [1117, 14, 946, 19], [1118, 16, 946, 19, "fileName"], [1118, 24, 946, 19], [1118, 26, 946, 19, "_jsxFileName"], [1118, 38, 946, 19], [1119, 16, 946, 19, "lineNumber"], [1119, 26, 946, 19], [1120, 16, 946, 19, "columnNumber"], [1120, 28, 946, 19], [1121, 14, 946, 19], [1121, 21, 947, 22], [1121, 22, 947, 23], [1121, 37, 948, 16], [1121, 41, 948, 16, "_jsxDevRuntime"], [1121, 55, 948, 16], [1121, 56, 948, 16, "jsxDEV"], [1121, 62, 948, 16], [1121, 64, 948, 17, "_TouchableOpacity"], [1121, 81, 948, 17], [1121, 82, 948, 17, "default"], [1121, 89, 948, 33], [1122, 16, 948, 34, "onPress"], [1122, 23, 948, 41], [1122, 25, 948, 43, "onCancel"], [1122, 33, 948, 52], [1123, 16, 948, 53, "style"], [1123, 21, 948, 58], [1123, 23, 948, 60, "styles"], [1123, 29, 948, 66], [1123, 30, 948, 67, "closeButton"], [1123, 41, 948, 79], [1124, 16, 948, 79, "children"], [1124, 24, 948, 79], [1124, 39, 949, 18], [1124, 43, 949, 18, "_jsxDevRuntime"], [1124, 57, 949, 18], [1124, 58, 949, 18, "jsxDEV"], [1124, 64, 949, 18], [1124, 66, 949, 19, "_lucideReactNative"], [1124, 84, 949, 19], [1124, 85, 949, 19, "X"], [1124, 86, 949, 20], [1125, 18, 949, 21, "size"], [1125, 22, 949, 25], [1125, 24, 949, 27], [1125, 26, 949, 30], [1126, 18, 949, 31, "color"], [1126, 23, 949, 36], [1126, 25, 949, 37], [1127, 16, 949, 43], [1128, 18, 949, 43, "fileName"], [1128, 26, 949, 43], [1128, 28, 949, 43, "_jsxFileName"], [1128, 40, 949, 43], [1129, 18, 949, 43, "lineNumber"], [1129, 28, 949, 43], [1130, 18, 949, 43, "columnNumber"], [1130, 30, 949, 43], [1131, 16, 949, 43], [1131, 23, 949, 45], [1132, 14, 949, 46], [1133, 16, 949, 46, "fileName"], [1133, 24, 949, 46], [1133, 26, 949, 46, "_jsxFileName"], [1133, 38, 949, 46], [1134, 16, 949, 46, "lineNumber"], [1134, 26, 949, 46], [1135, 16, 949, 46, "columnNumber"], [1135, 28, 949, 46], [1136, 14, 949, 46], [1136, 21, 950, 34], [1136, 22, 950, 35], [1137, 12, 950, 35], [1138, 14, 950, 35, "fileName"], [1138, 22, 950, 35], [1138, 24, 950, 35, "_jsxFileName"], [1138, 36, 950, 35], [1139, 14, 950, 35, "lineNumber"], [1139, 24, 950, 35], [1140, 14, 950, 35, "columnNumber"], [1140, 26, 950, 35], [1141, 12, 950, 35], [1141, 19, 951, 20], [1142, 10, 951, 21], [1143, 12, 951, 21, "fileName"], [1143, 20, 951, 21], [1143, 22, 951, 21, "_jsxFileName"], [1143, 34, 951, 21], [1144, 12, 951, 21, "lineNumber"], [1144, 22, 951, 21], [1145, 12, 951, 21, "columnNumber"], [1145, 24, 951, 21], [1146, 10, 951, 21], [1146, 17, 952, 18], [1146, 18, 952, 19], [1146, 33, 954, 12], [1146, 37, 954, 12, "_jsxDevRuntime"], [1146, 51, 954, 12], [1146, 52, 954, 12, "jsxDEV"], [1146, 58, 954, 12], [1146, 60, 954, 13, "_View"], [1146, 65, 954, 13], [1146, 66, 954, 13, "default"], [1146, 73, 954, 17], [1147, 12, 954, 18, "style"], [1147, 17, 954, 23], [1147, 19, 954, 25, "styles"], [1147, 25, 954, 31], [1147, 26, 954, 32, "privacyNotice"], [1147, 39, 954, 46], [1148, 12, 954, 46, "children"], [1148, 20, 954, 46], [1148, 36, 955, 14], [1148, 40, 955, 14, "_jsxDevRuntime"], [1148, 54, 955, 14], [1148, 55, 955, 14, "jsxDEV"], [1148, 61, 955, 14], [1148, 63, 955, 15, "_lucideReactNative"], [1148, 81, 955, 15], [1148, 82, 955, 15, "Shield"], [1148, 88, 955, 21], [1149, 14, 955, 22, "size"], [1149, 18, 955, 26], [1149, 20, 955, 28], [1149, 22, 955, 31], [1150, 14, 955, 32, "color"], [1150, 19, 955, 37], [1150, 21, 955, 38], [1151, 12, 955, 47], [1152, 14, 955, 47, "fileName"], [1152, 22, 955, 47], [1152, 24, 955, 47, "_jsxFileName"], [1152, 36, 955, 47], [1153, 14, 955, 47, "lineNumber"], [1153, 24, 955, 47], [1154, 14, 955, 47, "columnNumber"], [1154, 26, 955, 47], [1155, 12, 955, 47], [1155, 19, 955, 49], [1155, 20, 955, 50], [1155, 35, 956, 14], [1155, 39, 956, 14, "_jsxDevRuntime"], [1155, 53, 956, 14], [1155, 54, 956, 14, "jsxDEV"], [1155, 60, 956, 14], [1155, 62, 956, 15, "_Text"], [1155, 67, 956, 15], [1155, 68, 956, 15, "default"], [1155, 75, 956, 19], [1156, 14, 956, 20, "style"], [1156, 19, 956, 25], [1156, 21, 956, 27, "styles"], [1156, 27, 956, 33], [1156, 28, 956, 34, "privacyText"], [1156, 39, 956, 46], [1157, 14, 956, 46, "children"], [1157, 22, 956, 46], [1157, 24, 956, 47], [1158, 12, 958, 14], [1159, 14, 958, 14, "fileName"], [1159, 22, 958, 14], [1159, 24, 958, 14, "_jsxFileName"], [1159, 36, 958, 14], [1160, 14, 958, 14, "lineNumber"], [1160, 24, 958, 14], [1161, 14, 958, 14, "columnNumber"], [1161, 26, 958, 14], [1162, 12, 958, 14], [1162, 19, 958, 20], [1162, 20, 958, 21], [1163, 10, 958, 21], [1164, 12, 958, 21, "fileName"], [1164, 20, 958, 21], [1164, 22, 958, 21, "_jsxFileName"], [1164, 34, 958, 21], [1165, 12, 958, 21, "lineNumber"], [1165, 22, 958, 21], [1166, 12, 958, 21, "columnNumber"], [1166, 24, 958, 21], [1167, 10, 958, 21], [1167, 17, 959, 18], [1167, 18, 959, 19], [1167, 33, 961, 12], [1167, 37, 961, 12, "_jsxDevRuntime"], [1167, 51, 961, 12], [1167, 52, 961, 12, "jsxDEV"], [1167, 58, 961, 12], [1167, 60, 961, 13, "_View"], [1167, 65, 961, 13], [1167, 66, 961, 13, "default"], [1167, 73, 961, 17], [1168, 12, 961, 18, "style"], [1168, 17, 961, 23], [1168, 19, 961, 25, "styles"], [1168, 25, 961, 31], [1168, 26, 961, 32, "footer<PERSON><PERSON><PERSON>"], [1168, 39, 961, 46], [1169, 12, 961, 46, "children"], [1169, 20, 961, 46], [1169, 36, 962, 14], [1169, 40, 962, 14, "_jsxDevRuntime"], [1169, 54, 962, 14], [1169, 55, 962, 14, "jsxDEV"], [1169, 61, 962, 14], [1169, 63, 962, 15, "_Text"], [1169, 68, 962, 15], [1169, 69, 962, 15, "default"], [1169, 76, 962, 19], [1170, 14, 962, 20, "style"], [1170, 19, 962, 25], [1170, 21, 962, 27, "styles"], [1170, 27, 962, 33], [1170, 28, 962, 34, "instruction"], [1170, 39, 962, 46], [1171, 14, 962, 46, "children"], [1171, 22, 962, 46], [1171, 24, 962, 47], [1172, 12, 964, 14], [1173, 14, 964, 14, "fileName"], [1173, 22, 964, 14], [1173, 24, 964, 14, "_jsxFileName"], [1173, 36, 964, 14], [1174, 14, 964, 14, "lineNumber"], [1174, 24, 964, 14], [1175, 14, 964, 14, "columnNumber"], [1175, 26, 964, 14], [1176, 12, 964, 14], [1176, 19, 964, 20], [1176, 20, 964, 21], [1176, 35, 966, 14], [1176, 39, 966, 14, "_jsxDevRuntime"], [1176, 53, 966, 14], [1176, 54, 966, 14, "jsxDEV"], [1176, 60, 966, 14], [1176, 62, 966, 15, "_TouchableOpacity"], [1176, 79, 966, 15], [1176, 80, 966, 15, "default"], [1176, 87, 966, 31], [1177, 14, 967, 16, "onPress"], [1177, 21, 967, 23], [1177, 23, 967, 25, "capturePhoto"], [1177, 35, 967, 38], [1178, 14, 968, 16, "disabled"], [1178, 22, 968, 24], [1178, 24, 968, 26, "processingState"], [1178, 39, 968, 41], [1178, 44, 968, 46], [1178, 50, 968, 52], [1178, 54, 968, 56], [1178, 55, 968, 57, "isCameraReady"], [1178, 68, 968, 71], [1179, 14, 969, 16, "style"], [1179, 19, 969, 21], [1179, 21, 969, 23], [1179, 22, 970, 18, "styles"], [1179, 28, 970, 24], [1179, 29, 970, 25, "shutterButton"], [1179, 42, 970, 38], [1179, 44, 971, 18, "processingState"], [1179, 59, 971, 33], [1179, 64, 971, 38], [1179, 70, 971, 44], [1179, 74, 971, 48, "styles"], [1179, 80, 971, 54], [1179, 81, 971, 55, "shutterButtonDisabled"], [1179, 102, 971, 76], [1179, 103, 972, 18], [1180, 14, 972, 18, "children"], [1180, 22, 972, 18], [1180, 24, 974, 17, "processingState"], [1180, 39, 974, 32], [1180, 44, 974, 37], [1180, 50, 974, 43], [1180, 66, 975, 18], [1180, 70, 975, 18, "_jsxDevRuntime"], [1180, 84, 975, 18], [1180, 85, 975, 18, "jsxDEV"], [1180, 91, 975, 18], [1180, 93, 975, 19, "_View"], [1180, 98, 975, 19], [1180, 99, 975, 19, "default"], [1180, 106, 975, 23], [1181, 16, 975, 24, "style"], [1181, 21, 975, 29], [1181, 23, 975, 31, "styles"], [1181, 29, 975, 37], [1181, 30, 975, 38, "shutterInner"], [1182, 14, 975, 51], [1183, 16, 975, 51, "fileName"], [1183, 24, 975, 51], [1183, 26, 975, 51, "_jsxFileName"], [1183, 38, 975, 51], [1184, 16, 975, 51, "lineNumber"], [1184, 26, 975, 51], [1185, 16, 975, 51, "columnNumber"], [1185, 28, 975, 51], [1186, 14, 975, 51], [1186, 21, 975, 53], [1186, 22, 975, 54], [1186, 38, 977, 18], [1186, 42, 977, 18, "_jsxDevRuntime"], [1186, 56, 977, 18], [1186, 57, 977, 18, "jsxDEV"], [1186, 63, 977, 18], [1186, 65, 977, 19, "_ActivityIndicator"], [1186, 83, 977, 19], [1186, 84, 977, 19, "default"], [1186, 91, 977, 36], [1187, 16, 977, 37, "size"], [1187, 20, 977, 41], [1187, 22, 977, 42], [1187, 29, 977, 49], [1188, 16, 977, 50, "color"], [1188, 21, 977, 55], [1188, 23, 977, 56], [1189, 14, 977, 65], [1190, 16, 977, 65, "fileName"], [1190, 24, 977, 65], [1190, 26, 977, 65, "_jsxFileName"], [1190, 38, 977, 65], [1191, 16, 977, 65, "lineNumber"], [1191, 26, 977, 65], [1192, 16, 977, 65, "columnNumber"], [1192, 28, 977, 65], [1193, 14, 977, 65], [1193, 21, 977, 67], [1194, 12, 978, 17], [1195, 14, 978, 17, "fileName"], [1195, 22, 978, 17], [1195, 24, 978, 17, "_jsxFileName"], [1195, 36, 978, 17], [1196, 14, 978, 17, "lineNumber"], [1196, 24, 978, 17], [1197, 14, 978, 17, "columnNumber"], [1197, 26, 978, 17], [1198, 12, 978, 17], [1198, 19, 979, 32], [1198, 20, 979, 33], [1198, 35, 980, 14], [1198, 39, 980, 14, "_jsxDevRuntime"], [1198, 53, 980, 14], [1198, 54, 980, 14, "jsxDEV"], [1198, 60, 980, 14], [1198, 62, 980, 15, "_Text"], [1198, 67, 980, 15], [1198, 68, 980, 15, "default"], [1198, 75, 980, 19], [1199, 14, 980, 20, "style"], [1199, 19, 980, 25], [1199, 21, 980, 27, "styles"], [1199, 27, 980, 33], [1199, 28, 980, 34, "privacyNote"], [1199, 39, 980, 46], [1200, 14, 980, 46, "children"], [1200, 22, 980, 46], [1200, 24, 980, 47], [1201, 12, 982, 14], [1202, 14, 982, 14, "fileName"], [1202, 22, 982, 14], [1202, 24, 982, 14, "_jsxFileName"], [1202, 36, 982, 14], [1203, 14, 982, 14, "lineNumber"], [1203, 24, 982, 14], [1204, 14, 982, 14, "columnNumber"], [1204, 26, 982, 14], [1205, 12, 982, 14], [1205, 19, 982, 20], [1205, 20, 982, 21], [1206, 10, 982, 21], [1207, 12, 982, 21, "fileName"], [1207, 20, 982, 21], [1207, 22, 982, 21, "_jsxFileName"], [1207, 34, 982, 21], [1208, 12, 982, 21, "lineNumber"], [1208, 22, 982, 21], [1209, 12, 982, 21, "columnNumber"], [1209, 24, 982, 21], [1210, 10, 982, 21], [1210, 17, 983, 18], [1210, 18, 983, 19], [1211, 8, 983, 19], [1211, 23, 984, 12], [1211, 24, 985, 9], [1212, 6, 985, 9], [1213, 8, 985, 9, "fileName"], [1213, 16, 985, 9], [1213, 18, 985, 9, "_jsxFileName"], [1213, 30, 985, 9], [1214, 8, 985, 9, "lineNumber"], [1214, 18, 985, 9], [1215, 8, 985, 9, "columnNumber"], [1215, 20, 985, 9], [1216, 6, 985, 9], [1216, 13, 986, 12], [1216, 14, 986, 13], [1216, 29, 988, 6], [1216, 33, 988, 6, "_jsxDevRuntime"], [1216, 47, 988, 6], [1216, 48, 988, 6, "jsxDEV"], [1216, 54, 988, 6], [1216, 56, 988, 7, "_Modal"], [1216, 62, 988, 7], [1216, 63, 988, 7, "default"], [1216, 70, 988, 12], [1217, 8, 989, 8, "visible"], [1217, 15, 989, 15], [1217, 17, 989, 17, "processingState"], [1217, 32, 989, 32], [1217, 37, 989, 37], [1217, 43, 989, 43], [1217, 47, 989, 47, "processingState"], [1217, 62, 989, 62], [1217, 67, 989, 67], [1217, 74, 989, 75], [1218, 8, 990, 8, "transparent"], [1218, 19, 990, 19], [1219, 8, 991, 8, "animationType"], [1219, 21, 991, 21], [1219, 23, 991, 22], [1219, 29, 991, 28], [1220, 8, 991, 28, "children"], [1220, 16, 991, 28], [1220, 31, 993, 8], [1220, 35, 993, 8, "_jsxDevRuntime"], [1220, 49, 993, 8], [1220, 50, 993, 8, "jsxDEV"], [1220, 56, 993, 8], [1220, 58, 993, 9, "_View"], [1220, 63, 993, 9], [1220, 64, 993, 9, "default"], [1220, 71, 993, 13], [1221, 10, 993, 14, "style"], [1221, 15, 993, 19], [1221, 17, 993, 21, "styles"], [1221, 23, 993, 27], [1221, 24, 993, 28, "processingModal"], [1221, 39, 993, 44], [1222, 10, 993, 44, "children"], [1222, 18, 993, 44], [1222, 33, 994, 10], [1222, 37, 994, 10, "_jsxDevRuntime"], [1222, 51, 994, 10], [1222, 52, 994, 10, "jsxDEV"], [1222, 58, 994, 10], [1222, 60, 994, 11, "_View"], [1222, 65, 994, 11], [1222, 66, 994, 11, "default"], [1222, 73, 994, 15], [1223, 12, 994, 16, "style"], [1223, 17, 994, 21], [1223, 19, 994, 23, "styles"], [1223, 25, 994, 29], [1223, 26, 994, 30, "processingContent"], [1223, 43, 994, 48], [1224, 12, 994, 48, "children"], [1224, 20, 994, 48], [1224, 36, 995, 12], [1224, 40, 995, 12, "_jsxDevRuntime"], [1224, 54, 995, 12], [1224, 55, 995, 12, "jsxDEV"], [1224, 61, 995, 12], [1224, 63, 995, 13, "_ActivityIndicator"], [1224, 81, 995, 13], [1224, 82, 995, 13, "default"], [1224, 89, 995, 30], [1225, 14, 995, 31, "size"], [1225, 18, 995, 35], [1225, 20, 995, 36], [1225, 27, 995, 43], [1226, 14, 995, 44, "color"], [1226, 19, 995, 49], [1226, 21, 995, 50], [1227, 12, 995, 59], [1228, 14, 995, 59, "fileName"], [1228, 22, 995, 59], [1228, 24, 995, 59, "_jsxFileName"], [1228, 36, 995, 59], [1229, 14, 995, 59, "lineNumber"], [1229, 24, 995, 59], [1230, 14, 995, 59, "columnNumber"], [1230, 26, 995, 59], [1231, 12, 995, 59], [1231, 19, 995, 61], [1231, 20, 995, 62], [1231, 35, 997, 12], [1231, 39, 997, 12, "_jsxDevRuntime"], [1231, 53, 997, 12], [1231, 54, 997, 12, "jsxDEV"], [1231, 60, 997, 12], [1231, 62, 997, 13, "_Text"], [1231, 67, 997, 13], [1231, 68, 997, 13, "default"], [1231, 75, 997, 17], [1232, 14, 997, 18, "style"], [1232, 19, 997, 23], [1232, 21, 997, 25, "styles"], [1232, 27, 997, 31], [1232, 28, 997, 32, "processingTitle"], [1232, 43, 997, 48], [1233, 14, 997, 48, "children"], [1233, 22, 997, 48], [1233, 25, 998, 15, "processingState"], [1233, 40, 998, 30], [1233, 45, 998, 35], [1233, 56, 998, 46], [1233, 60, 998, 50], [1233, 80, 998, 70], [1233, 82, 999, 15, "processingState"], [1233, 97, 999, 30], [1233, 102, 999, 35], [1233, 113, 999, 46], [1233, 117, 999, 50], [1233, 146, 999, 79], [1233, 148, 1000, 15, "processingState"], [1233, 163, 1000, 30], [1233, 168, 1000, 35], [1233, 180, 1000, 47], [1233, 184, 1000, 51], [1233, 216, 1000, 83], [1233, 218, 1001, 15, "processingState"], [1233, 233, 1001, 30], [1233, 238, 1001, 35], [1233, 249, 1001, 46], [1233, 253, 1001, 50], [1233, 275, 1001, 72], [1234, 12, 1001, 72], [1235, 14, 1001, 72, "fileName"], [1235, 22, 1001, 72], [1235, 24, 1001, 72, "_jsxFileName"], [1235, 36, 1001, 72], [1236, 14, 1001, 72, "lineNumber"], [1236, 24, 1001, 72], [1237, 14, 1001, 72, "columnNumber"], [1237, 26, 1001, 72], [1238, 12, 1001, 72], [1238, 19, 1002, 18], [1238, 20, 1002, 19], [1238, 35, 1003, 12], [1238, 39, 1003, 12, "_jsxDevRuntime"], [1238, 53, 1003, 12], [1238, 54, 1003, 12, "jsxDEV"], [1238, 60, 1003, 12], [1238, 62, 1003, 13, "_View"], [1238, 67, 1003, 13], [1238, 68, 1003, 13, "default"], [1238, 75, 1003, 17], [1239, 14, 1003, 18, "style"], [1239, 19, 1003, 23], [1239, 21, 1003, 25, "styles"], [1239, 27, 1003, 31], [1239, 28, 1003, 32, "progressBar"], [1239, 39, 1003, 44], [1240, 14, 1003, 44, "children"], [1240, 22, 1003, 44], [1240, 37, 1004, 14], [1240, 41, 1004, 14, "_jsxDevRuntime"], [1240, 55, 1004, 14], [1240, 56, 1004, 14, "jsxDEV"], [1240, 62, 1004, 14], [1240, 64, 1004, 15, "_View"], [1240, 69, 1004, 15], [1240, 70, 1004, 15, "default"], [1240, 77, 1004, 19], [1241, 16, 1005, 16, "style"], [1241, 21, 1005, 21], [1241, 23, 1005, 23], [1241, 24, 1006, 18, "styles"], [1241, 30, 1006, 24], [1241, 31, 1006, 25, "progressFill"], [1241, 43, 1006, 37], [1241, 45, 1007, 18], [1242, 18, 1007, 20, "width"], [1242, 23, 1007, 25], [1242, 25, 1007, 27], [1242, 28, 1007, 30, "processingProgress"], [1242, 46, 1007, 48], [1243, 16, 1007, 52], [1243, 17, 1007, 53], [1244, 14, 1008, 18], [1245, 16, 1008, 18, "fileName"], [1245, 24, 1008, 18], [1245, 26, 1008, 18, "_jsxFileName"], [1245, 38, 1008, 18], [1246, 16, 1008, 18, "lineNumber"], [1246, 26, 1008, 18], [1247, 16, 1008, 18, "columnNumber"], [1247, 28, 1008, 18], [1248, 14, 1008, 18], [1248, 21, 1009, 15], [1249, 12, 1009, 16], [1250, 14, 1009, 16, "fileName"], [1250, 22, 1009, 16], [1250, 24, 1009, 16, "_jsxFileName"], [1250, 36, 1009, 16], [1251, 14, 1009, 16, "lineNumber"], [1251, 24, 1009, 16], [1252, 14, 1009, 16, "columnNumber"], [1252, 26, 1009, 16], [1253, 12, 1009, 16], [1253, 19, 1010, 18], [1253, 20, 1010, 19], [1253, 35, 1011, 12], [1253, 39, 1011, 12, "_jsxDevRuntime"], [1253, 53, 1011, 12], [1253, 54, 1011, 12, "jsxDEV"], [1253, 60, 1011, 12], [1253, 62, 1011, 13, "_Text"], [1253, 67, 1011, 13], [1253, 68, 1011, 13, "default"], [1253, 75, 1011, 17], [1254, 14, 1011, 18, "style"], [1254, 19, 1011, 23], [1254, 21, 1011, 25, "styles"], [1254, 27, 1011, 31], [1254, 28, 1011, 32, "processingDescription"], [1254, 49, 1011, 54], [1255, 14, 1011, 54, "children"], [1255, 22, 1011, 54], [1255, 25, 1012, 15, "processingState"], [1255, 40, 1012, 30], [1255, 45, 1012, 35], [1255, 56, 1012, 46], [1255, 60, 1012, 50], [1255, 89, 1012, 79], [1255, 91, 1013, 15, "processingState"], [1255, 106, 1013, 30], [1255, 111, 1013, 35], [1255, 122, 1013, 46], [1255, 126, 1013, 50], [1255, 164, 1013, 88], [1255, 166, 1014, 15, "processingState"], [1255, 181, 1014, 30], [1255, 186, 1014, 35], [1255, 198, 1014, 47], [1255, 202, 1014, 51], [1255, 247, 1014, 96], [1255, 249, 1015, 15, "processingState"], [1255, 264, 1015, 30], [1255, 269, 1015, 35], [1255, 280, 1015, 46], [1255, 284, 1015, 50], [1255, 325, 1015, 91], [1256, 12, 1015, 91], [1257, 14, 1015, 91, "fileName"], [1257, 22, 1015, 91], [1257, 24, 1015, 91, "_jsxFileName"], [1257, 36, 1015, 91], [1258, 14, 1015, 91, "lineNumber"], [1258, 24, 1015, 91], [1259, 14, 1015, 91, "columnNumber"], [1259, 26, 1015, 91], [1260, 12, 1015, 91], [1260, 19, 1016, 18], [1260, 20, 1016, 19], [1260, 22, 1017, 13, "processingState"], [1260, 37, 1017, 28], [1260, 42, 1017, 33], [1260, 53, 1017, 44], [1260, 70, 1018, 14], [1260, 74, 1018, 14, "_jsxDevRuntime"], [1260, 88, 1018, 14], [1260, 89, 1018, 14, "jsxDEV"], [1260, 95, 1018, 14], [1260, 97, 1018, 15, "_lucideReactNative"], [1260, 115, 1018, 15], [1260, 116, 1018, 15, "CheckCircle"], [1260, 127, 1018, 26], [1261, 14, 1018, 27, "size"], [1261, 18, 1018, 31], [1261, 20, 1018, 33], [1261, 22, 1018, 36], [1262, 14, 1018, 37, "color"], [1262, 19, 1018, 42], [1262, 21, 1018, 43], [1262, 30, 1018, 52], [1263, 14, 1018, 53, "style"], [1263, 19, 1018, 58], [1263, 21, 1018, 60, "styles"], [1263, 27, 1018, 66], [1263, 28, 1018, 67, "successIcon"], [1264, 12, 1018, 79], [1265, 14, 1018, 79, "fileName"], [1265, 22, 1018, 79], [1265, 24, 1018, 79, "_jsxFileName"], [1265, 36, 1018, 79], [1266, 14, 1018, 79, "lineNumber"], [1266, 24, 1018, 79], [1267, 14, 1018, 79, "columnNumber"], [1267, 26, 1018, 79], [1268, 12, 1018, 79], [1268, 19, 1018, 81], [1268, 20, 1019, 13], [1269, 10, 1019, 13], [1270, 12, 1019, 13, "fileName"], [1270, 20, 1019, 13], [1270, 22, 1019, 13, "_jsxFileName"], [1270, 34, 1019, 13], [1271, 12, 1019, 13, "lineNumber"], [1271, 22, 1019, 13], [1272, 12, 1019, 13, "columnNumber"], [1272, 24, 1019, 13], [1273, 10, 1019, 13], [1273, 17, 1020, 16], [1274, 8, 1020, 17], [1275, 10, 1020, 17, "fileName"], [1275, 18, 1020, 17], [1275, 20, 1020, 17, "_jsxFileName"], [1275, 32, 1020, 17], [1276, 10, 1020, 17, "lineNumber"], [1276, 20, 1020, 17], [1277, 10, 1020, 17, "columnNumber"], [1277, 22, 1020, 17], [1278, 8, 1020, 17], [1278, 15, 1021, 14], [1279, 6, 1021, 15], [1280, 8, 1021, 15, "fileName"], [1280, 16, 1021, 15], [1280, 18, 1021, 15, "_jsxFileName"], [1280, 30, 1021, 15], [1281, 8, 1021, 15, "lineNumber"], [1281, 18, 1021, 15], [1282, 8, 1021, 15, "columnNumber"], [1282, 20, 1021, 15], [1283, 6, 1021, 15], [1283, 13, 1022, 13], [1283, 14, 1022, 14], [1283, 29, 1024, 6], [1283, 33, 1024, 6, "_jsxDevRuntime"], [1283, 47, 1024, 6], [1283, 48, 1024, 6, "jsxDEV"], [1283, 54, 1024, 6], [1283, 56, 1024, 7, "_Modal"], [1283, 62, 1024, 7], [1283, 63, 1024, 7, "default"], [1283, 70, 1024, 12], [1284, 8, 1025, 8, "visible"], [1284, 15, 1025, 15], [1284, 17, 1025, 17, "processingState"], [1284, 32, 1025, 32], [1284, 37, 1025, 37], [1284, 44, 1025, 45], [1285, 8, 1026, 8, "transparent"], [1285, 19, 1026, 19], [1286, 8, 1027, 8, "animationType"], [1286, 21, 1027, 21], [1286, 23, 1027, 22], [1286, 29, 1027, 28], [1287, 8, 1027, 28, "children"], [1287, 16, 1027, 28], [1287, 31, 1029, 8], [1287, 35, 1029, 8, "_jsxDevRuntime"], [1287, 49, 1029, 8], [1287, 50, 1029, 8, "jsxDEV"], [1287, 56, 1029, 8], [1287, 58, 1029, 9, "_View"], [1287, 63, 1029, 9], [1287, 64, 1029, 9, "default"], [1287, 71, 1029, 13], [1288, 10, 1029, 14, "style"], [1288, 15, 1029, 19], [1288, 17, 1029, 21, "styles"], [1288, 23, 1029, 27], [1288, 24, 1029, 28, "processingModal"], [1288, 39, 1029, 44], [1289, 10, 1029, 44, "children"], [1289, 18, 1029, 44], [1289, 33, 1030, 10], [1289, 37, 1030, 10, "_jsxDevRuntime"], [1289, 51, 1030, 10], [1289, 52, 1030, 10, "jsxDEV"], [1289, 58, 1030, 10], [1289, 60, 1030, 11, "_View"], [1289, 65, 1030, 11], [1289, 66, 1030, 11, "default"], [1289, 73, 1030, 15], [1290, 12, 1030, 16, "style"], [1290, 17, 1030, 21], [1290, 19, 1030, 23, "styles"], [1290, 25, 1030, 29], [1290, 26, 1030, 30, "errorContent"], [1290, 38, 1030, 43], [1291, 12, 1030, 43, "children"], [1291, 20, 1030, 43], [1291, 36, 1031, 12], [1291, 40, 1031, 12, "_jsxDevRuntime"], [1291, 54, 1031, 12], [1291, 55, 1031, 12, "jsxDEV"], [1291, 61, 1031, 12], [1291, 63, 1031, 13, "_lucideReactNative"], [1291, 81, 1031, 13], [1291, 82, 1031, 13, "X"], [1291, 83, 1031, 14], [1292, 14, 1031, 15, "size"], [1292, 18, 1031, 19], [1292, 20, 1031, 21], [1292, 22, 1031, 24], [1293, 14, 1031, 25, "color"], [1293, 19, 1031, 30], [1293, 21, 1031, 31], [1294, 12, 1031, 40], [1295, 14, 1031, 40, "fileName"], [1295, 22, 1031, 40], [1295, 24, 1031, 40, "_jsxFileName"], [1295, 36, 1031, 40], [1296, 14, 1031, 40, "lineNumber"], [1296, 24, 1031, 40], [1297, 14, 1031, 40, "columnNumber"], [1297, 26, 1031, 40], [1298, 12, 1031, 40], [1298, 19, 1031, 42], [1298, 20, 1031, 43], [1298, 35, 1032, 12], [1298, 39, 1032, 12, "_jsxDevRuntime"], [1298, 53, 1032, 12], [1298, 54, 1032, 12, "jsxDEV"], [1298, 60, 1032, 12], [1298, 62, 1032, 13, "_Text"], [1298, 67, 1032, 13], [1298, 68, 1032, 13, "default"], [1298, 75, 1032, 17], [1299, 14, 1032, 18, "style"], [1299, 19, 1032, 23], [1299, 21, 1032, 25, "styles"], [1299, 27, 1032, 31], [1299, 28, 1032, 32, "errorTitle"], [1299, 38, 1032, 43], [1300, 14, 1032, 43, "children"], [1300, 22, 1032, 43], [1300, 24, 1032, 44], [1301, 12, 1032, 61], [1302, 14, 1032, 61, "fileName"], [1302, 22, 1032, 61], [1302, 24, 1032, 61, "_jsxFileName"], [1302, 36, 1032, 61], [1303, 14, 1032, 61, "lineNumber"], [1303, 24, 1032, 61], [1304, 14, 1032, 61, "columnNumber"], [1304, 26, 1032, 61], [1305, 12, 1032, 61], [1305, 19, 1032, 67], [1305, 20, 1032, 68], [1305, 35, 1033, 12], [1305, 39, 1033, 12, "_jsxDevRuntime"], [1305, 53, 1033, 12], [1305, 54, 1033, 12, "jsxDEV"], [1305, 60, 1033, 12], [1305, 62, 1033, 13, "_Text"], [1305, 67, 1033, 13], [1305, 68, 1033, 13, "default"], [1305, 75, 1033, 17], [1306, 14, 1033, 18, "style"], [1306, 19, 1033, 23], [1306, 21, 1033, 25, "styles"], [1306, 27, 1033, 31], [1306, 28, 1033, 32, "errorMessage"], [1306, 40, 1033, 45], [1307, 14, 1033, 45, "children"], [1307, 22, 1033, 45], [1307, 24, 1033, 47, "errorMessage"], [1308, 12, 1033, 59], [1309, 14, 1033, 59, "fileName"], [1309, 22, 1033, 59], [1309, 24, 1033, 59, "_jsxFileName"], [1309, 36, 1033, 59], [1310, 14, 1033, 59, "lineNumber"], [1310, 24, 1033, 59], [1311, 14, 1033, 59, "columnNumber"], [1311, 26, 1033, 59], [1312, 12, 1033, 59], [1312, 19, 1033, 66], [1312, 20, 1033, 67], [1312, 35, 1034, 12], [1312, 39, 1034, 12, "_jsxDevRuntime"], [1312, 53, 1034, 12], [1312, 54, 1034, 12, "jsxDEV"], [1312, 60, 1034, 12], [1312, 62, 1034, 13, "_TouchableOpacity"], [1312, 79, 1034, 13], [1312, 80, 1034, 13, "default"], [1312, 87, 1034, 29], [1313, 14, 1035, 14, "onPress"], [1313, 21, 1035, 21], [1313, 23, 1035, 23, "retryCapture"], [1313, 35, 1035, 36], [1314, 14, 1036, 14, "style"], [1314, 19, 1036, 19], [1314, 21, 1036, 21, "styles"], [1314, 27, 1036, 27], [1314, 28, 1036, 28, "primaryButton"], [1314, 41, 1036, 42], [1315, 14, 1036, 42, "children"], [1315, 22, 1036, 42], [1315, 37, 1038, 14], [1315, 41, 1038, 14, "_jsxDevRuntime"], [1315, 55, 1038, 14], [1315, 56, 1038, 14, "jsxDEV"], [1315, 62, 1038, 14], [1315, 64, 1038, 15, "_Text"], [1315, 69, 1038, 15], [1315, 70, 1038, 15, "default"], [1315, 77, 1038, 19], [1316, 16, 1038, 20, "style"], [1316, 21, 1038, 25], [1316, 23, 1038, 27, "styles"], [1316, 29, 1038, 33], [1316, 30, 1038, 34, "primaryButtonText"], [1316, 47, 1038, 52], [1317, 16, 1038, 52, "children"], [1317, 24, 1038, 52], [1317, 26, 1038, 53], [1318, 14, 1038, 62], [1319, 16, 1038, 62, "fileName"], [1319, 24, 1038, 62], [1319, 26, 1038, 62, "_jsxFileName"], [1319, 38, 1038, 62], [1320, 16, 1038, 62, "lineNumber"], [1320, 26, 1038, 62], [1321, 16, 1038, 62, "columnNumber"], [1321, 28, 1038, 62], [1322, 14, 1038, 62], [1322, 21, 1038, 68], [1323, 12, 1038, 69], [1324, 14, 1038, 69, "fileName"], [1324, 22, 1038, 69], [1324, 24, 1038, 69, "_jsxFileName"], [1324, 36, 1038, 69], [1325, 14, 1038, 69, "lineNumber"], [1325, 24, 1038, 69], [1326, 14, 1038, 69, "columnNumber"], [1326, 26, 1038, 69], [1327, 12, 1038, 69], [1327, 19, 1039, 30], [1327, 20, 1039, 31], [1327, 35, 1040, 12], [1327, 39, 1040, 12, "_jsxDevRuntime"], [1327, 53, 1040, 12], [1327, 54, 1040, 12, "jsxDEV"], [1327, 60, 1040, 12], [1327, 62, 1040, 13, "_TouchableOpacity"], [1327, 79, 1040, 13], [1327, 80, 1040, 13, "default"], [1327, 87, 1040, 29], [1328, 14, 1041, 14, "onPress"], [1328, 21, 1041, 21], [1328, 23, 1041, 23, "onCancel"], [1328, 31, 1041, 32], [1329, 14, 1042, 14, "style"], [1329, 19, 1042, 19], [1329, 21, 1042, 21, "styles"], [1329, 27, 1042, 27], [1329, 28, 1042, 28, "secondaryButton"], [1329, 43, 1042, 44], [1330, 14, 1042, 44, "children"], [1330, 22, 1042, 44], [1330, 37, 1044, 14], [1330, 41, 1044, 14, "_jsxDevRuntime"], [1330, 55, 1044, 14], [1330, 56, 1044, 14, "jsxDEV"], [1330, 62, 1044, 14], [1330, 64, 1044, 15, "_Text"], [1330, 69, 1044, 15], [1330, 70, 1044, 15, "default"], [1330, 77, 1044, 19], [1331, 16, 1044, 20, "style"], [1331, 21, 1044, 25], [1331, 23, 1044, 27, "styles"], [1331, 29, 1044, 33], [1331, 30, 1044, 34, "secondaryButtonText"], [1331, 49, 1044, 54], [1332, 16, 1044, 54, "children"], [1332, 24, 1044, 54], [1332, 26, 1044, 55], [1333, 14, 1044, 61], [1334, 16, 1044, 61, "fileName"], [1334, 24, 1044, 61], [1334, 26, 1044, 61, "_jsxFileName"], [1334, 38, 1044, 61], [1335, 16, 1044, 61, "lineNumber"], [1335, 26, 1044, 61], [1336, 16, 1044, 61, "columnNumber"], [1336, 28, 1044, 61], [1337, 14, 1044, 61], [1337, 21, 1044, 67], [1338, 12, 1044, 68], [1339, 14, 1044, 68, "fileName"], [1339, 22, 1044, 68], [1339, 24, 1044, 68, "_jsxFileName"], [1339, 36, 1044, 68], [1340, 14, 1044, 68, "lineNumber"], [1340, 24, 1044, 68], [1341, 14, 1044, 68, "columnNumber"], [1341, 26, 1044, 68], [1342, 12, 1044, 68], [1342, 19, 1045, 30], [1342, 20, 1045, 31], [1343, 10, 1045, 31], [1344, 12, 1045, 31, "fileName"], [1344, 20, 1045, 31], [1344, 22, 1045, 31, "_jsxFileName"], [1344, 34, 1045, 31], [1345, 12, 1045, 31, "lineNumber"], [1345, 22, 1045, 31], [1346, 12, 1045, 31, "columnNumber"], [1346, 24, 1045, 31], [1347, 10, 1045, 31], [1347, 17, 1046, 16], [1348, 8, 1046, 17], [1349, 10, 1046, 17, "fileName"], [1349, 18, 1046, 17], [1349, 20, 1046, 17, "_jsxFileName"], [1349, 32, 1046, 17], [1350, 10, 1046, 17, "lineNumber"], [1350, 20, 1046, 17], [1351, 10, 1046, 17, "columnNumber"], [1351, 22, 1046, 17], [1352, 8, 1046, 17], [1352, 15, 1047, 14], [1353, 6, 1047, 15], [1354, 8, 1047, 15, "fileName"], [1354, 16, 1047, 15], [1354, 18, 1047, 15, "_jsxFileName"], [1354, 30, 1047, 15], [1355, 8, 1047, 15, "lineNumber"], [1355, 18, 1047, 15], [1356, 8, 1047, 15, "columnNumber"], [1356, 20, 1047, 15], [1357, 6, 1047, 15], [1357, 13, 1048, 13], [1357, 14, 1048, 14], [1358, 4, 1048, 14], [1359, 6, 1048, 14, "fileName"], [1359, 14, 1048, 14], [1359, 16, 1048, 14, "_jsxFileName"], [1359, 28, 1048, 14], [1360, 6, 1048, 14, "lineNumber"], [1360, 16, 1048, 14], [1361, 6, 1048, 14, "columnNumber"], [1361, 18, 1048, 14], [1362, 4, 1048, 14], [1362, 11, 1049, 10], [1362, 12, 1049, 11], [1363, 2, 1051, 0], [1364, 2, 1051, 1, "_s"], [1364, 4, 1051, 1], [1364, 5, 51, 24, "EchoCameraWeb"], [1364, 18, 51, 37], [1365, 4, 51, 37], [1365, 12, 58, 42, "useCameraPermissions"], [1365, 44, 58, 62], [1365, 46, 72, 19, "useUpload"], [1365, 64, 72, 28], [1366, 2, 72, 28], [1367, 2, 72, 28, "_c"], [1367, 4, 72, 28], [1367, 7, 51, 24, "EchoCameraWeb"], [1367, 20, 51, 37], [1368, 2, 1052, 0], [1368, 8, 1052, 6, "styles"], [1368, 14, 1052, 12], [1368, 17, 1052, 15, "StyleSheet"], [1368, 36, 1052, 25], [1368, 37, 1052, 26, "create"], [1368, 43, 1052, 32], [1368, 44, 1052, 33], [1369, 4, 1053, 2, "container"], [1369, 13, 1053, 11], [1369, 15, 1053, 13], [1370, 6, 1054, 4, "flex"], [1370, 10, 1054, 8], [1370, 12, 1054, 10], [1370, 13, 1054, 11], [1371, 6, 1055, 4, "backgroundColor"], [1371, 21, 1055, 19], [1371, 23, 1055, 21], [1372, 4, 1056, 2], [1372, 5, 1056, 3], [1373, 4, 1057, 2, "cameraContainer"], [1373, 19, 1057, 17], [1373, 21, 1057, 19], [1374, 6, 1058, 4, "flex"], [1374, 10, 1058, 8], [1374, 12, 1058, 10], [1374, 13, 1058, 11], [1375, 6, 1059, 4, "max<PERSON><PERSON><PERSON>"], [1375, 14, 1059, 12], [1375, 16, 1059, 14], [1375, 19, 1059, 17], [1376, 6, 1060, 4, "alignSelf"], [1376, 15, 1060, 13], [1376, 17, 1060, 15], [1376, 25, 1060, 23], [1377, 6, 1061, 4, "width"], [1377, 11, 1061, 9], [1377, 13, 1061, 11], [1378, 4, 1062, 2], [1378, 5, 1062, 3], [1379, 4, 1063, 2, "camera"], [1379, 10, 1063, 8], [1379, 12, 1063, 10], [1380, 6, 1064, 4, "flex"], [1380, 10, 1064, 8], [1380, 12, 1064, 10], [1381, 4, 1065, 2], [1381, 5, 1065, 3], [1382, 4, 1066, 2, "headerOverlay"], [1382, 17, 1066, 15], [1382, 19, 1066, 17], [1383, 6, 1067, 4, "position"], [1383, 14, 1067, 12], [1383, 16, 1067, 14], [1383, 26, 1067, 24], [1384, 6, 1068, 4, "top"], [1384, 9, 1068, 7], [1384, 11, 1068, 9], [1384, 12, 1068, 10], [1385, 6, 1069, 4, "left"], [1385, 10, 1069, 8], [1385, 12, 1069, 10], [1385, 13, 1069, 11], [1386, 6, 1070, 4, "right"], [1386, 11, 1070, 9], [1386, 13, 1070, 11], [1386, 14, 1070, 12], [1387, 6, 1071, 4, "backgroundColor"], [1387, 21, 1071, 19], [1387, 23, 1071, 21], [1387, 36, 1071, 34], [1388, 6, 1072, 4, "paddingTop"], [1388, 16, 1072, 14], [1388, 18, 1072, 16], [1388, 20, 1072, 18], [1389, 6, 1073, 4, "paddingHorizontal"], [1389, 23, 1073, 21], [1389, 25, 1073, 23], [1389, 27, 1073, 25], [1390, 6, 1074, 4, "paddingBottom"], [1390, 19, 1074, 17], [1390, 21, 1074, 19], [1391, 4, 1075, 2], [1391, 5, 1075, 3], [1392, 4, 1076, 2, "headerContent"], [1392, 17, 1076, 15], [1392, 19, 1076, 17], [1393, 6, 1077, 4, "flexDirection"], [1393, 19, 1077, 17], [1393, 21, 1077, 19], [1393, 26, 1077, 24], [1394, 6, 1078, 4, "justifyContent"], [1394, 20, 1078, 18], [1394, 22, 1078, 20], [1394, 37, 1078, 35], [1395, 6, 1079, 4, "alignItems"], [1395, 16, 1079, 14], [1395, 18, 1079, 16], [1396, 4, 1080, 2], [1396, 5, 1080, 3], [1397, 4, 1081, 2, "headerLeft"], [1397, 14, 1081, 12], [1397, 16, 1081, 14], [1398, 6, 1082, 4, "flex"], [1398, 10, 1082, 8], [1398, 12, 1082, 10], [1399, 4, 1083, 2], [1399, 5, 1083, 3], [1400, 4, 1084, 2, "headerTitle"], [1400, 15, 1084, 13], [1400, 17, 1084, 15], [1401, 6, 1085, 4, "fontSize"], [1401, 14, 1085, 12], [1401, 16, 1085, 14], [1401, 18, 1085, 16], [1402, 6, 1086, 4, "fontWeight"], [1402, 16, 1086, 14], [1402, 18, 1086, 16], [1402, 23, 1086, 21], [1403, 6, 1087, 4, "color"], [1403, 11, 1087, 9], [1403, 13, 1087, 11], [1403, 19, 1087, 17], [1404, 6, 1088, 4, "marginBottom"], [1404, 18, 1088, 16], [1404, 20, 1088, 18], [1405, 4, 1089, 2], [1405, 5, 1089, 3], [1406, 4, 1090, 2, "subtitleRow"], [1406, 15, 1090, 13], [1406, 17, 1090, 15], [1407, 6, 1091, 4, "flexDirection"], [1407, 19, 1091, 17], [1407, 21, 1091, 19], [1407, 26, 1091, 24], [1408, 6, 1092, 4, "alignItems"], [1408, 16, 1092, 14], [1408, 18, 1092, 16], [1408, 26, 1092, 24], [1409, 6, 1093, 4, "marginBottom"], [1409, 18, 1093, 16], [1409, 20, 1093, 18], [1410, 4, 1094, 2], [1410, 5, 1094, 3], [1411, 4, 1095, 2, "webIcon"], [1411, 11, 1095, 9], [1411, 13, 1095, 11], [1412, 6, 1096, 4, "fontSize"], [1412, 14, 1096, 12], [1412, 16, 1096, 14], [1412, 18, 1096, 16], [1413, 6, 1097, 4, "marginRight"], [1413, 17, 1097, 15], [1413, 19, 1097, 17], [1414, 4, 1098, 2], [1414, 5, 1098, 3], [1415, 4, 1099, 2, "headerSubtitle"], [1415, 18, 1099, 16], [1415, 20, 1099, 18], [1416, 6, 1100, 4, "fontSize"], [1416, 14, 1100, 12], [1416, 16, 1100, 14], [1416, 18, 1100, 16], [1417, 6, 1101, 4, "color"], [1417, 11, 1101, 9], [1417, 13, 1101, 11], [1417, 19, 1101, 17], [1418, 6, 1102, 4, "opacity"], [1418, 13, 1102, 11], [1418, 15, 1102, 13], [1419, 4, 1103, 2], [1419, 5, 1103, 3], [1420, 4, 1104, 2, "challengeRow"], [1420, 16, 1104, 14], [1420, 18, 1104, 16], [1421, 6, 1105, 4, "flexDirection"], [1421, 19, 1105, 17], [1421, 21, 1105, 19], [1421, 26, 1105, 24], [1422, 6, 1106, 4, "alignItems"], [1422, 16, 1106, 14], [1422, 18, 1106, 16], [1423, 4, 1107, 2], [1423, 5, 1107, 3], [1424, 4, 1108, 2, "challengeCode"], [1424, 17, 1108, 15], [1424, 19, 1108, 17], [1425, 6, 1109, 4, "fontSize"], [1425, 14, 1109, 12], [1425, 16, 1109, 14], [1425, 18, 1109, 16], [1426, 6, 1110, 4, "color"], [1426, 11, 1110, 9], [1426, 13, 1110, 11], [1426, 19, 1110, 17], [1427, 6, 1111, 4, "marginLeft"], [1427, 16, 1111, 14], [1427, 18, 1111, 16], [1427, 19, 1111, 17], [1428, 6, 1112, 4, "fontFamily"], [1428, 16, 1112, 14], [1428, 18, 1112, 16], [1429, 4, 1113, 2], [1429, 5, 1113, 3], [1430, 4, 1114, 2, "closeButton"], [1430, 15, 1114, 13], [1430, 17, 1114, 15], [1431, 6, 1115, 4, "padding"], [1431, 13, 1115, 11], [1431, 15, 1115, 13], [1432, 4, 1116, 2], [1432, 5, 1116, 3], [1433, 4, 1117, 2, "privacyNotice"], [1433, 17, 1117, 15], [1433, 19, 1117, 17], [1434, 6, 1118, 4, "position"], [1434, 14, 1118, 12], [1434, 16, 1118, 14], [1434, 26, 1118, 24], [1435, 6, 1119, 4, "top"], [1435, 9, 1119, 7], [1435, 11, 1119, 9], [1435, 14, 1119, 12], [1436, 6, 1120, 4, "left"], [1436, 10, 1120, 8], [1436, 12, 1120, 10], [1436, 14, 1120, 12], [1437, 6, 1121, 4, "right"], [1437, 11, 1121, 9], [1437, 13, 1121, 11], [1437, 15, 1121, 13], [1438, 6, 1122, 4, "backgroundColor"], [1438, 21, 1122, 19], [1438, 23, 1122, 21], [1438, 48, 1122, 46], [1439, 6, 1123, 4, "borderRadius"], [1439, 18, 1123, 16], [1439, 20, 1123, 18], [1439, 21, 1123, 19], [1440, 6, 1124, 4, "padding"], [1440, 13, 1124, 11], [1440, 15, 1124, 13], [1440, 17, 1124, 15], [1441, 6, 1125, 4, "flexDirection"], [1441, 19, 1125, 17], [1441, 21, 1125, 19], [1441, 26, 1125, 24], [1442, 6, 1126, 4, "alignItems"], [1442, 16, 1126, 14], [1442, 18, 1126, 16], [1443, 4, 1127, 2], [1443, 5, 1127, 3], [1444, 4, 1128, 2, "privacyText"], [1444, 15, 1128, 13], [1444, 17, 1128, 15], [1445, 6, 1129, 4, "color"], [1445, 11, 1129, 9], [1445, 13, 1129, 11], [1445, 19, 1129, 17], [1446, 6, 1130, 4, "fontSize"], [1446, 14, 1130, 12], [1446, 16, 1130, 14], [1446, 18, 1130, 16], [1447, 6, 1131, 4, "marginLeft"], [1447, 16, 1131, 14], [1447, 18, 1131, 16], [1447, 19, 1131, 17], [1448, 6, 1132, 4, "flex"], [1448, 10, 1132, 8], [1448, 12, 1132, 10], [1449, 4, 1133, 2], [1449, 5, 1133, 3], [1450, 4, 1134, 2, "footer<PERSON><PERSON><PERSON>"], [1450, 17, 1134, 15], [1450, 19, 1134, 17], [1451, 6, 1135, 4, "position"], [1451, 14, 1135, 12], [1451, 16, 1135, 14], [1451, 26, 1135, 24], [1452, 6, 1136, 4, "bottom"], [1452, 12, 1136, 10], [1452, 14, 1136, 12], [1452, 15, 1136, 13], [1453, 6, 1137, 4, "left"], [1453, 10, 1137, 8], [1453, 12, 1137, 10], [1453, 13, 1137, 11], [1454, 6, 1138, 4, "right"], [1454, 11, 1138, 9], [1454, 13, 1138, 11], [1454, 14, 1138, 12], [1455, 6, 1139, 4, "backgroundColor"], [1455, 21, 1139, 19], [1455, 23, 1139, 21], [1455, 36, 1139, 34], [1456, 6, 1140, 4, "paddingBottom"], [1456, 19, 1140, 17], [1456, 21, 1140, 19], [1456, 23, 1140, 21], [1457, 6, 1141, 4, "paddingTop"], [1457, 16, 1141, 14], [1457, 18, 1141, 16], [1457, 20, 1141, 18], [1458, 6, 1142, 4, "alignItems"], [1458, 16, 1142, 14], [1458, 18, 1142, 16], [1459, 4, 1143, 2], [1459, 5, 1143, 3], [1460, 4, 1144, 2, "instruction"], [1460, 15, 1144, 13], [1460, 17, 1144, 15], [1461, 6, 1145, 4, "fontSize"], [1461, 14, 1145, 12], [1461, 16, 1145, 14], [1461, 18, 1145, 16], [1462, 6, 1146, 4, "color"], [1462, 11, 1146, 9], [1462, 13, 1146, 11], [1462, 19, 1146, 17], [1463, 6, 1147, 4, "marginBottom"], [1463, 18, 1147, 16], [1463, 20, 1147, 18], [1464, 4, 1148, 2], [1464, 5, 1148, 3], [1465, 4, 1149, 2, "shutterButton"], [1465, 17, 1149, 15], [1465, 19, 1149, 17], [1466, 6, 1150, 4, "width"], [1466, 11, 1150, 9], [1466, 13, 1150, 11], [1466, 15, 1150, 13], [1467, 6, 1151, 4, "height"], [1467, 12, 1151, 10], [1467, 14, 1151, 12], [1467, 16, 1151, 14], [1468, 6, 1152, 4, "borderRadius"], [1468, 18, 1152, 16], [1468, 20, 1152, 18], [1468, 22, 1152, 20], [1469, 6, 1153, 4, "backgroundColor"], [1469, 21, 1153, 19], [1469, 23, 1153, 21], [1469, 29, 1153, 27], [1470, 6, 1154, 4, "justifyContent"], [1470, 20, 1154, 18], [1470, 22, 1154, 20], [1470, 30, 1154, 28], [1471, 6, 1155, 4, "alignItems"], [1471, 16, 1155, 14], [1471, 18, 1155, 16], [1471, 26, 1155, 24], [1472, 6, 1156, 4, "marginBottom"], [1472, 18, 1156, 16], [1472, 20, 1156, 18], [1472, 22, 1156, 20], [1473, 6, 1157, 4], [1473, 9, 1157, 7, "Platform"], [1473, 26, 1157, 15], [1473, 27, 1157, 16, "select"], [1473, 33, 1157, 22], [1473, 34, 1157, 23], [1474, 8, 1158, 6, "ios"], [1474, 11, 1158, 9], [1474, 13, 1158, 11], [1475, 10, 1159, 8, "shadowColor"], [1475, 21, 1159, 19], [1475, 23, 1159, 21], [1475, 32, 1159, 30], [1476, 10, 1160, 8, "shadowOffset"], [1476, 22, 1160, 20], [1476, 24, 1160, 22], [1477, 12, 1160, 24, "width"], [1477, 17, 1160, 29], [1477, 19, 1160, 31], [1477, 20, 1160, 32], [1478, 12, 1160, 34, "height"], [1478, 18, 1160, 40], [1478, 20, 1160, 42], [1479, 10, 1160, 44], [1479, 11, 1160, 45], [1480, 10, 1161, 8, "shadowOpacity"], [1480, 23, 1161, 21], [1480, 25, 1161, 23], [1480, 28, 1161, 26], [1481, 10, 1162, 8, "shadowRadius"], [1481, 22, 1162, 20], [1481, 24, 1162, 22], [1482, 8, 1163, 6], [1482, 9, 1163, 7], [1483, 8, 1164, 6, "android"], [1483, 15, 1164, 13], [1483, 17, 1164, 15], [1484, 10, 1165, 8, "elevation"], [1484, 19, 1165, 17], [1484, 21, 1165, 19], [1485, 8, 1166, 6], [1485, 9, 1166, 7], [1486, 8, 1167, 6, "web"], [1486, 11, 1167, 9], [1486, 13, 1167, 11], [1487, 10, 1168, 8, "boxShadow"], [1487, 19, 1168, 17], [1487, 21, 1168, 19], [1488, 8, 1169, 6], [1489, 6, 1170, 4], [1489, 7, 1170, 5], [1490, 4, 1171, 2], [1490, 5, 1171, 3], [1491, 4, 1172, 2, "shutterButtonDisabled"], [1491, 25, 1172, 23], [1491, 27, 1172, 25], [1492, 6, 1173, 4, "opacity"], [1492, 13, 1173, 11], [1492, 15, 1173, 13], [1493, 4, 1174, 2], [1493, 5, 1174, 3], [1494, 4, 1175, 2, "shutterInner"], [1494, 16, 1175, 14], [1494, 18, 1175, 16], [1495, 6, 1176, 4, "width"], [1495, 11, 1176, 9], [1495, 13, 1176, 11], [1495, 15, 1176, 13], [1496, 6, 1177, 4, "height"], [1496, 12, 1177, 10], [1496, 14, 1177, 12], [1496, 16, 1177, 14], [1497, 6, 1178, 4, "borderRadius"], [1497, 18, 1178, 16], [1497, 20, 1178, 18], [1497, 22, 1178, 20], [1498, 6, 1179, 4, "backgroundColor"], [1498, 21, 1179, 19], [1498, 23, 1179, 21], [1498, 29, 1179, 27], [1499, 6, 1180, 4, "borderWidth"], [1499, 17, 1180, 15], [1499, 19, 1180, 17], [1499, 20, 1180, 18], [1500, 6, 1181, 4, "borderColor"], [1500, 17, 1181, 15], [1500, 19, 1181, 17], [1501, 4, 1182, 2], [1501, 5, 1182, 3], [1502, 4, 1183, 2, "privacyNote"], [1502, 15, 1183, 13], [1502, 17, 1183, 15], [1503, 6, 1184, 4, "fontSize"], [1503, 14, 1184, 12], [1503, 16, 1184, 14], [1503, 18, 1184, 16], [1504, 6, 1185, 4, "color"], [1504, 11, 1185, 9], [1504, 13, 1185, 11], [1505, 4, 1186, 2], [1505, 5, 1186, 3], [1506, 4, 1187, 2, "processingModal"], [1506, 19, 1187, 17], [1506, 21, 1187, 19], [1507, 6, 1188, 4, "flex"], [1507, 10, 1188, 8], [1507, 12, 1188, 10], [1507, 13, 1188, 11], [1508, 6, 1189, 4, "backgroundColor"], [1508, 21, 1189, 19], [1508, 23, 1189, 21], [1508, 43, 1189, 41], [1509, 6, 1190, 4, "justifyContent"], [1509, 20, 1190, 18], [1509, 22, 1190, 20], [1509, 30, 1190, 28], [1510, 6, 1191, 4, "alignItems"], [1510, 16, 1191, 14], [1510, 18, 1191, 16], [1511, 4, 1192, 2], [1511, 5, 1192, 3], [1512, 4, 1193, 2, "processingContent"], [1512, 21, 1193, 19], [1512, 23, 1193, 21], [1513, 6, 1194, 4, "backgroundColor"], [1513, 21, 1194, 19], [1513, 23, 1194, 21], [1513, 29, 1194, 27], [1514, 6, 1195, 4, "borderRadius"], [1514, 18, 1195, 16], [1514, 20, 1195, 18], [1514, 22, 1195, 20], [1515, 6, 1196, 4, "padding"], [1515, 13, 1196, 11], [1515, 15, 1196, 13], [1515, 17, 1196, 15], [1516, 6, 1197, 4, "width"], [1516, 11, 1197, 9], [1516, 13, 1197, 11], [1516, 18, 1197, 16], [1517, 6, 1198, 4, "max<PERSON><PERSON><PERSON>"], [1517, 14, 1198, 12], [1517, 16, 1198, 14], [1517, 19, 1198, 17], [1518, 6, 1199, 4, "alignItems"], [1518, 16, 1199, 14], [1518, 18, 1199, 16], [1519, 4, 1200, 2], [1519, 5, 1200, 3], [1520, 4, 1201, 2, "processingTitle"], [1520, 19, 1201, 17], [1520, 21, 1201, 19], [1521, 6, 1202, 4, "fontSize"], [1521, 14, 1202, 12], [1521, 16, 1202, 14], [1521, 18, 1202, 16], [1522, 6, 1203, 4, "fontWeight"], [1522, 16, 1203, 14], [1522, 18, 1203, 16], [1522, 23, 1203, 21], [1523, 6, 1204, 4, "color"], [1523, 11, 1204, 9], [1523, 13, 1204, 11], [1523, 22, 1204, 20], [1524, 6, 1205, 4, "marginTop"], [1524, 15, 1205, 13], [1524, 17, 1205, 15], [1524, 19, 1205, 17], [1525, 6, 1206, 4, "marginBottom"], [1525, 18, 1206, 16], [1525, 20, 1206, 18], [1526, 4, 1207, 2], [1526, 5, 1207, 3], [1527, 4, 1208, 2, "progressBar"], [1527, 15, 1208, 13], [1527, 17, 1208, 15], [1528, 6, 1209, 4, "width"], [1528, 11, 1209, 9], [1528, 13, 1209, 11], [1528, 19, 1209, 17], [1529, 6, 1210, 4, "height"], [1529, 12, 1210, 10], [1529, 14, 1210, 12], [1529, 15, 1210, 13], [1530, 6, 1211, 4, "backgroundColor"], [1530, 21, 1211, 19], [1530, 23, 1211, 21], [1530, 32, 1211, 30], [1531, 6, 1212, 4, "borderRadius"], [1531, 18, 1212, 16], [1531, 20, 1212, 18], [1531, 21, 1212, 19], [1532, 6, 1213, 4, "overflow"], [1532, 14, 1213, 12], [1532, 16, 1213, 14], [1532, 24, 1213, 22], [1533, 6, 1214, 4, "marginBottom"], [1533, 18, 1214, 16], [1533, 20, 1214, 18], [1534, 4, 1215, 2], [1534, 5, 1215, 3], [1535, 4, 1216, 2, "progressFill"], [1535, 16, 1216, 14], [1535, 18, 1216, 16], [1536, 6, 1217, 4, "height"], [1536, 12, 1217, 10], [1536, 14, 1217, 12], [1536, 20, 1217, 18], [1537, 6, 1218, 4, "backgroundColor"], [1537, 21, 1218, 19], [1537, 23, 1218, 21], [1537, 32, 1218, 30], [1538, 6, 1219, 4, "borderRadius"], [1538, 18, 1219, 16], [1538, 20, 1219, 18], [1539, 4, 1220, 2], [1539, 5, 1220, 3], [1540, 4, 1221, 2, "processingDescription"], [1540, 25, 1221, 23], [1540, 27, 1221, 25], [1541, 6, 1222, 4, "fontSize"], [1541, 14, 1222, 12], [1541, 16, 1222, 14], [1541, 18, 1222, 16], [1542, 6, 1223, 4, "color"], [1542, 11, 1223, 9], [1542, 13, 1223, 11], [1542, 22, 1223, 20], [1543, 6, 1224, 4, "textAlign"], [1543, 15, 1224, 13], [1543, 17, 1224, 15], [1544, 4, 1225, 2], [1544, 5, 1225, 3], [1545, 4, 1226, 2, "successIcon"], [1545, 15, 1226, 13], [1545, 17, 1226, 15], [1546, 6, 1227, 4, "marginTop"], [1546, 15, 1227, 13], [1546, 17, 1227, 15], [1547, 4, 1228, 2], [1547, 5, 1228, 3], [1548, 4, 1229, 2, "errorContent"], [1548, 16, 1229, 14], [1548, 18, 1229, 16], [1549, 6, 1230, 4, "backgroundColor"], [1549, 21, 1230, 19], [1549, 23, 1230, 21], [1549, 29, 1230, 27], [1550, 6, 1231, 4, "borderRadius"], [1550, 18, 1231, 16], [1550, 20, 1231, 18], [1550, 22, 1231, 20], [1551, 6, 1232, 4, "padding"], [1551, 13, 1232, 11], [1551, 15, 1232, 13], [1551, 17, 1232, 15], [1552, 6, 1233, 4, "width"], [1552, 11, 1233, 9], [1552, 13, 1233, 11], [1552, 18, 1233, 16], [1553, 6, 1234, 4, "max<PERSON><PERSON><PERSON>"], [1553, 14, 1234, 12], [1553, 16, 1234, 14], [1553, 19, 1234, 17], [1554, 6, 1235, 4, "alignItems"], [1554, 16, 1235, 14], [1554, 18, 1235, 16], [1555, 4, 1236, 2], [1555, 5, 1236, 3], [1556, 4, 1237, 2, "errorTitle"], [1556, 14, 1237, 12], [1556, 16, 1237, 14], [1557, 6, 1238, 4, "fontSize"], [1557, 14, 1238, 12], [1557, 16, 1238, 14], [1557, 18, 1238, 16], [1558, 6, 1239, 4, "fontWeight"], [1558, 16, 1239, 14], [1558, 18, 1239, 16], [1558, 23, 1239, 21], [1559, 6, 1240, 4, "color"], [1559, 11, 1240, 9], [1559, 13, 1240, 11], [1559, 22, 1240, 20], [1560, 6, 1241, 4, "marginTop"], [1560, 15, 1241, 13], [1560, 17, 1241, 15], [1560, 19, 1241, 17], [1561, 6, 1242, 4, "marginBottom"], [1561, 18, 1242, 16], [1561, 20, 1242, 18], [1562, 4, 1243, 2], [1562, 5, 1243, 3], [1563, 4, 1244, 2, "errorMessage"], [1563, 16, 1244, 14], [1563, 18, 1244, 16], [1564, 6, 1245, 4, "fontSize"], [1564, 14, 1245, 12], [1564, 16, 1245, 14], [1564, 18, 1245, 16], [1565, 6, 1246, 4, "color"], [1565, 11, 1246, 9], [1565, 13, 1246, 11], [1565, 22, 1246, 20], [1566, 6, 1247, 4, "textAlign"], [1566, 15, 1247, 13], [1566, 17, 1247, 15], [1566, 25, 1247, 23], [1567, 6, 1248, 4, "marginBottom"], [1567, 18, 1248, 16], [1567, 20, 1248, 18], [1568, 4, 1249, 2], [1568, 5, 1249, 3], [1569, 4, 1250, 2, "primaryButton"], [1569, 17, 1250, 15], [1569, 19, 1250, 17], [1570, 6, 1251, 4, "backgroundColor"], [1570, 21, 1251, 19], [1570, 23, 1251, 21], [1570, 32, 1251, 30], [1571, 6, 1252, 4, "paddingHorizontal"], [1571, 23, 1252, 21], [1571, 25, 1252, 23], [1571, 27, 1252, 25], [1572, 6, 1253, 4, "paddingVertical"], [1572, 21, 1253, 19], [1572, 23, 1253, 21], [1572, 25, 1253, 23], [1573, 6, 1254, 4, "borderRadius"], [1573, 18, 1254, 16], [1573, 20, 1254, 18], [1573, 21, 1254, 19], [1574, 6, 1255, 4, "marginTop"], [1574, 15, 1255, 13], [1574, 17, 1255, 15], [1575, 4, 1256, 2], [1575, 5, 1256, 3], [1576, 4, 1257, 2, "primaryButtonText"], [1576, 21, 1257, 19], [1576, 23, 1257, 21], [1577, 6, 1258, 4, "color"], [1577, 11, 1258, 9], [1577, 13, 1258, 11], [1577, 19, 1258, 17], [1578, 6, 1259, 4, "fontSize"], [1578, 14, 1259, 12], [1578, 16, 1259, 14], [1578, 18, 1259, 16], [1579, 6, 1260, 4, "fontWeight"], [1579, 16, 1260, 14], [1579, 18, 1260, 16], [1580, 4, 1261, 2], [1580, 5, 1261, 3], [1581, 4, 1262, 2, "secondaryButton"], [1581, 19, 1262, 17], [1581, 21, 1262, 19], [1582, 6, 1263, 4, "paddingHorizontal"], [1582, 23, 1263, 21], [1582, 25, 1263, 23], [1582, 27, 1263, 25], [1583, 6, 1264, 4, "paddingVertical"], [1583, 21, 1264, 19], [1583, 23, 1264, 21], [1583, 25, 1264, 23], [1584, 6, 1265, 4, "marginTop"], [1584, 15, 1265, 13], [1584, 17, 1265, 15], [1585, 4, 1266, 2], [1585, 5, 1266, 3], [1586, 4, 1267, 2, "secondaryButtonText"], [1586, 23, 1267, 21], [1586, 25, 1267, 23], [1587, 6, 1268, 4, "color"], [1587, 11, 1268, 9], [1587, 13, 1268, 11], [1587, 22, 1268, 20], [1588, 6, 1269, 4, "fontSize"], [1588, 14, 1269, 12], [1588, 16, 1269, 14], [1589, 4, 1270, 2], [1589, 5, 1270, 3], [1590, 4, 1271, 2, "permissionContent"], [1590, 21, 1271, 19], [1590, 23, 1271, 21], [1591, 6, 1272, 4, "flex"], [1591, 10, 1272, 8], [1591, 12, 1272, 10], [1591, 13, 1272, 11], [1592, 6, 1273, 4, "justifyContent"], [1592, 20, 1273, 18], [1592, 22, 1273, 20], [1592, 30, 1273, 28], [1593, 6, 1274, 4, "alignItems"], [1593, 16, 1274, 14], [1593, 18, 1274, 16], [1593, 26, 1274, 24], [1594, 6, 1275, 4, "padding"], [1594, 13, 1275, 11], [1594, 15, 1275, 13], [1595, 4, 1276, 2], [1595, 5, 1276, 3], [1596, 4, 1277, 2, "permissionTitle"], [1596, 19, 1277, 17], [1596, 21, 1277, 19], [1597, 6, 1278, 4, "fontSize"], [1597, 14, 1278, 12], [1597, 16, 1278, 14], [1597, 18, 1278, 16], [1598, 6, 1279, 4, "fontWeight"], [1598, 16, 1279, 14], [1598, 18, 1279, 16], [1598, 23, 1279, 21], [1599, 6, 1280, 4, "color"], [1599, 11, 1280, 9], [1599, 13, 1280, 11], [1599, 22, 1280, 20], [1600, 6, 1281, 4, "marginTop"], [1600, 15, 1281, 13], [1600, 17, 1281, 15], [1600, 19, 1281, 17], [1601, 6, 1282, 4, "marginBottom"], [1601, 18, 1282, 16], [1601, 20, 1282, 18], [1602, 4, 1283, 2], [1602, 5, 1283, 3], [1603, 4, 1284, 2, "permissionDescription"], [1603, 25, 1284, 23], [1603, 27, 1284, 25], [1604, 6, 1285, 4, "fontSize"], [1604, 14, 1285, 12], [1604, 16, 1285, 14], [1604, 18, 1285, 16], [1605, 6, 1286, 4, "color"], [1605, 11, 1286, 9], [1605, 13, 1286, 11], [1605, 22, 1286, 20], [1606, 6, 1287, 4, "textAlign"], [1606, 15, 1287, 13], [1606, 17, 1287, 15], [1606, 25, 1287, 23], [1607, 6, 1288, 4, "marginBottom"], [1607, 18, 1288, 16], [1607, 20, 1288, 18], [1608, 4, 1289, 2], [1608, 5, 1289, 3], [1609, 4, 1290, 2, "loadingText"], [1609, 15, 1290, 13], [1609, 17, 1290, 15], [1610, 6, 1291, 4, "color"], [1610, 11, 1291, 9], [1610, 13, 1291, 11], [1610, 22, 1291, 20], [1611, 6, 1292, 4, "marginTop"], [1611, 15, 1292, 13], [1611, 17, 1292, 15], [1612, 4, 1293, 2], [1612, 5, 1293, 3], [1613, 4, 1294, 2], [1614, 4, 1295, 2, "blurZone"], [1614, 12, 1295, 10], [1614, 14, 1295, 12], [1615, 6, 1296, 4, "position"], [1615, 14, 1296, 12], [1615, 16, 1296, 14], [1615, 26, 1296, 24], [1616, 6, 1297, 4, "overflow"], [1616, 14, 1297, 12], [1616, 16, 1297, 14], [1617, 4, 1298, 2], [1617, 5, 1298, 3], [1618, 4, 1299, 2, "previewChip"], [1618, 15, 1299, 13], [1618, 17, 1299, 15], [1619, 6, 1300, 4, "position"], [1619, 14, 1300, 12], [1619, 16, 1300, 14], [1619, 26, 1300, 24], [1620, 6, 1301, 4, "top"], [1620, 9, 1301, 7], [1620, 11, 1301, 9], [1620, 12, 1301, 10], [1621, 6, 1302, 4, "right"], [1621, 11, 1302, 9], [1621, 13, 1302, 11], [1621, 14, 1302, 12], [1622, 6, 1303, 4, "backgroundColor"], [1622, 21, 1303, 19], [1622, 23, 1303, 21], [1622, 40, 1303, 38], [1623, 6, 1304, 4, "paddingHorizontal"], [1623, 23, 1304, 21], [1623, 25, 1304, 23], [1623, 27, 1304, 25], [1624, 6, 1305, 4, "paddingVertical"], [1624, 21, 1305, 19], [1624, 23, 1305, 21], [1624, 24, 1305, 22], [1625, 6, 1306, 4, "borderRadius"], [1625, 18, 1306, 16], [1625, 20, 1306, 18], [1626, 4, 1307, 2], [1626, 5, 1307, 3], [1627, 4, 1308, 2, "previewChipText"], [1627, 19, 1308, 17], [1627, 21, 1308, 19], [1628, 6, 1309, 4, "color"], [1628, 11, 1309, 9], [1628, 13, 1309, 11], [1628, 19, 1309, 17], [1629, 6, 1310, 4, "fontSize"], [1629, 14, 1310, 12], [1629, 16, 1310, 14], [1629, 18, 1310, 16], [1630, 6, 1311, 4, "fontWeight"], [1630, 16, 1311, 14], [1630, 18, 1311, 16], [1631, 4, 1312, 2], [1632, 2, 1313, 0], [1632, 3, 1313, 1], [1632, 4, 1313, 2], [1633, 2, 1313, 3], [1633, 6, 1313, 3, "_c"], [1633, 8, 1313, 3], [1634, 2, 1313, 3, "$RefreshReg$"], [1634, 14, 1313, 3], [1634, 15, 1313, 3, "_c"], [1634, 17, 1313, 3], [1635, 0, 1313, 3], [1635, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdsE;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}