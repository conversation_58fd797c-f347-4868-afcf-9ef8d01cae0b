{"dependencies": [{"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}, {"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 639}, "end": {"line": 5, "column": 54, "index": 693}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkPoint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 694}, "end": {"line": 6, "column": 42, "index": 736}}], "key": "t00LaVO/wJ2FJvJQ0krGRNiisCQ=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 737}, "end": {"line": 7, "column": 40, "index": 777}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}, {"name": "./JsiSkRRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 778}, "end": {"line": 8, "column": 42, "index": 820}}], "key": "n4Z2DW77BVppQ2PhsKnE4j8f2qY=", "exportNames": ["*"]}}, {"name": "./JsiSkMatrix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 821}, "end": {"line": 9, "column": 44, "index": 865}}], "key": "aOVfjZgmz4R2ci39pV6HZujK8og=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPath = void 0;\n  var _types = require(_dependencyMap[0], \"../types\");\n  var _Host = require(_dependencyMap[1], \"./Host\");\n  var _JsiSkPoint = require(_dependencyMap[2], \"./JsiSkPoint\");\n  var _JsiSkRect = require(_dependencyMap[3], \"./JsiSkRect\");\n  var _JsiSkRRect = require(_dependencyMap[4], \"./JsiSkRRect\");\n  var _JsiSkMatrix = require(_dependencyMap[5], \"./JsiSkMatrix\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  const CommandCount = {\n    [_types.PathVerb.Move]: 3,\n    [_types.PathVerb.Line]: 3,\n    [_types.PathVerb.Quad]: 5,\n    [_types.PathVerb.Conic]: 6,\n    [_types.PathVerb.Cubic]: 7,\n    [_types.PathVerb.Close]: 1\n  };\n  const pinT = t => Math.min(Math.max(t, 0), 1);\n  class JsiSkPath extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Path\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    addPath(src, matrix, extend = false) {\n      const args = [JsiSkPath.fromValue(src), ...(matrix ? _JsiSkMatrix.JsiSkMatrix.fromValue(matrix) : []), extend];\n      this.ref.addPath(...args);\n      return this;\n    }\n    addArc(oval, startAngleInDegrees, sweepAngleInDegrees) {\n      this.ref.addArc(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, oval), startAngleInDegrees, sweepAngleInDegrees);\n      return this;\n    }\n    addOval(oval, isCCW, startIndex) {\n      this.ref.addOval(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, oval), isCCW, startIndex);\n      return this;\n    }\n    countPoints() {\n      return this.ref.countPoints();\n    }\n    addPoly(points, close) {\n      this.ref.addPoly(points.map(p => Array.from(_JsiSkPoint.JsiSkPoint.fromValue(p))).flat(), close);\n      return this;\n    }\n    moveTo(x, y) {\n      this.ref.moveTo(x, y);\n      return this;\n    }\n    lineTo(x, y) {\n      this.ref.lineTo(x, y);\n      return this;\n    }\n    makeAsWinding() {\n      const result = this.ref.makeAsWinding();\n      return result === null ? result : this;\n    }\n    offset(dx, dy) {\n      this.ref.offset(dx, dy);\n      return this;\n    }\n    rArcTo(rx, ry, xAxisRotateInDegrees, useSmallArc, isCCW, dx, dy) {\n      this.ref.rArcTo(rx, ry, xAxisRotateInDegrees, useSmallArc, isCCW, dx, dy);\n      return this;\n    }\n    rConicTo(dx1, dy1, dx2, dy2, w) {\n      this.ref.rConicTo(dx1, dy1, dx2, dy2, w);\n      return this;\n    }\n    rCubicTo(cpx1, cpy1, cpx2, cpy2, x, y) {\n      this.ref.rCubicTo(cpx1, cpy1, cpx2, cpy2, x, y);\n      return this;\n    }\n    rMoveTo(x, y) {\n      this.ref.rMoveTo(x, y);\n      return this;\n    }\n    rLineTo(x, y) {\n      this.ref.rLineTo(x, y);\n      return this;\n    }\n    rQuadTo(x1, y1, x2, y2) {\n      this.ref.rQuadTo(x1, y1, x2, y2);\n      return this;\n    }\n    setFillType(fill) {\n      this.ref.setFillType((0, _Host.getEnum)(this.CanvasKit, \"FillType\", fill));\n      return this;\n    }\n    setIsVolatile(volatile) {\n      this.ref.setIsVolatile(volatile);\n      return this;\n    }\n    stroke(opts) {\n      const result = this.ref.stroke(opts === undefined ? undefined : {\n        width: opts.width,\n        // eslint-disable-next-line camelcase\n        miter_limit: opts.width,\n        precision: opts.width,\n        join: (0, _Host.optEnum)(this.CanvasKit, \"StrokeJoin\", opts.join),\n        cap: (0, _Host.optEnum)(this.CanvasKit, \"StrokeCap\", opts.cap)\n      });\n      return result === null ? result : this;\n    }\n    close() {\n      this.ref.close();\n      return this;\n    }\n    reset() {\n      this.ref.reset();\n      return this;\n    }\n    rewind() {\n      this.ref.rewind();\n      return this;\n    }\n    computeTightBounds() {\n      return new _JsiSkRect.JsiSkRect(this.CanvasKit, this.ref.computeTightBounds());\n    }\n    arcToOval(oval, startAngleInDegrees, sweepAngleInDegrees, forceMoveTo) {\n      this.ref.arcToOval(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, oval), startAngleInDegrees, sweepAngleInDegrees, forceMoveTo);\n      return this;\n    }\n    arcToRotated(rx, ry, xAxisRotateInDegrees, useSmallArc, isCCW, x, y) {\n      this.ref.arcToRotated(rx, ry, xAxisRotateInDegrees, useSmallArc, isCCW, x, y);\n      return this;\n    }\n    arcToTangent(x1, y1, x2, y2, radius) {\n      this.ref.arcToTangent(x1, y1, x2, y2, radius);\n      return this;\n    }\n    conicTo(x1, y1, x2, y2, w) {\n      this.ref.conicTo(x1, y1, x2, y2, w);\n      return this;\n    }\n    contains(x, y) {\n      return this.ref.contains(x, y);\n    }\n    copy() {\n      return new JsiSkPath(this.CanvasKit, this.ref.copy());\n    }\n    cubicTo(cpx1, cpy1, cpx2, cpy2, x, y) {\n      this.ref.cubicTo(cpx1, cpy1, cpx2, cpy2, x, y);\n      return this;\n    }\n    dash(on, off, phase) {\n      return this.ref.dash(on, off, phase);\n    }\n    equals(other) {\n      return this.ref.equals(JsiSkPath.fromValue(other));\n    }\n    getBounds() {\n      return new _JsiSkRect.JsiSkRect(this.CanvasKit, this.ref.getBounds());\n    }\n    getFillType() {\n      return this.ref.getFillType().value;\n    }\n    quadTo(x1, y1, x2, y2) {\n      this.ref.quadTo(x1, y1, x2, y2);\n      return this;\n    }\n    addRect(rect, isCCW) {\n      this.ref.addRect(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, rect), isCCW);\n      return this;\n    }\n    addRRect(rrect, isCCW) {\n      this.ref.addRRect(_JsiSkRRect.JsiSkRRect.fromValue(this.CanvasKit, rrect), isCCW);\n      return this;\n    }\n    getPoint(index) {\n      return new _JsiSkPoint.JsiSkPoint(this.CanvasKit, this.ref.getPoint(index));\n    }\n    isEmpty() {\n      return this.ref.isEmpty();\n    }\n    isVolatile() {\n      return this.ref.isVolatile();\n    }\n    addCircle(x, y, r) {\n      this.ref.addCircle(x, y, r);\n      return this;\n    }\n    getLastPt() {\n      return new _JsiSkPoint.JsiSkPoint(this.CanvasKit, this.ref.getPoint(this.ref.countPoints() - 1));\n    }\n    op(path, op) {\n      return this.ref.op(JsiSkPath.fromValue(path), (0, _Host.getEnum)(this.CanvasKit, \"PathOp\", op));\n    }\n    simplify() {\n      return this.ref.simplify();\n    }\n    toSVGString() {\n      return this.ref.toSVGString();\n    }\n    trim(start, stop, isComplement) {\n      const startT = pinT(start);\n      const stopT = pinT(stop);\n      if (startT === 0 && stopT === 1) {\n        return this;\n      }\n      const result = this.ref.trim(startT, stopT, isComplement);\n      return result === null ? result : this;\n    }\n    transform(m) {\n      let matrix = m instanceof _JsiSkMatrix.JsiSkMatrix ? Array.from(_JsiSkMatrix.JsiSkMatrix.fromValue(m)) : m;\n      if (matrix.length === 16) {\n        matrix = [matrix[0], matrix[1], matrix[3], matrix[4], matrix[5], matrix[7], matrix[12], matrix[13], matrix[15]];\n      } else if (matrix.length !== 9) {\n        throw new Error(`Invalid matrix length: ${matrix.length}`);\n      }\n      this.ref.transform(matrix);\n      return this;\n    }\n    interpolate(end, t, output) {\n      const path = this.CanvasKit.Path.MakeFromPathInterpolation(this.ref, JsiSkPath.fromValue(end), t);\n      if (path === null) {\n        return null;\n      }\n      if (output) {\n        output.ref = path;\n        return output;\n      } else {\n        return new JsiSkPath(this.CanvasKit, path);\n      }\n    }\n    isInterpolatable(path2) {\n      return this.CanvasKit.Path.CanInterpolate(this.ref, JsiSkPath.fromValue(path2));\n    }\n    toCmds() {\n      const cmds = this.ref.toCmds();\n      const result = cmds.reduce((acc, cmd, i) => {\n        if (i === 0) {\n          acc.push([]);\n        }\n        const current = acc[acc.length - 1];\n        if (current.length === 0) {\n          current.push(cmd);\n          const length = CommandCount[current[0]];\n          if (current.length === length && i !== cmds.length - 1) {\n            acc.push([]);\n          }\n        } else {\n          const length = CommandCount[current[0]];\n          if (current.length < length) {\n            current.push(cmd);\n          }\n          if (current.length === length && i !== cmds.length - 1) {\n            acc.push([]);\n          }\n        }\n        return acc;\n      }, []);\n      return result;\n    }\n  }\n  exports.JsiSkPath = JsiSkPath;\n});", "lineCount": 281, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_types"], [6, 12, 4, 0], [6, 15, 4, 0, "require"], [6, 22, 4, 0], [6, 23, 4, 0, "_dependencyMap"], [6, 37, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_Host"], [7, 11, 5, 0], [7, 14, 5, 0, "require"], [7, 21, 5, 0], [7, 22, 5, 0, "_dependencyMap"], [7, 36, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkPoint"], [8, 17, 6, 0], [8, 20, 6, 0, "require"], [8, 27, 6, 0], [8, 28, 6, 0, "_dependencyMap"], [8, 42, 6, 0], [9, 2, 7, 0], [9, 6, 7, 0, "_JsiSkRect"], [9, 16, 7, 0], [9, 19, 7, 0, "require"], [9, 26, 7, 0], [9, 27, 7, 0, "_dependencyMap"], [9, 41, 7, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_JsiSkRRect"], [10, 17, 8, 0], [10, 20, 8, 0, "require"], [10, 27, 8, 0], [10, 28, 8, 0, "_dependencyMap"], [10, 42, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_JsiSkMatrix"], [11, 18, 9, 0], [11, 21, 9, 0, "require"], [11, 28, 9, 0], [11, 29, 9, 0, "_dependencyMap"], [11, 43, 9, 0], [12, 2, 1, 0], [12, 11, 1, 9, "_defineProperty"], [12, 26, 1, 24, "_defineProperty"], [12, 27, 1, 25, "e"], [12, 28, 1, 26], [12, 30, 1, 28, "r"], [12, 31, 1, 29], [12, 33, 1, 31, "t"], [12, 34, 1, 32], [12, 36, 1, 34], [13, 4, 1, 36], [13, 11, 1, 43], [13, 12, 1, 44, "r"], [13, 13, 1, 45], [13, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [13, 30, 1, 62], [13, 31, 1, 63, "r"], [13, 32, 1, 64], [13, 33, 1, 65], [13, 38, 1, 70, "e"], [13, 39, 1, 71], [13, 42, 1, 74, "Object"], [13, 48, 1, 80], [13, 49, 1, 81, "defineProperty"], [13, 63, 1, 95], [13, 64, 1, 96, "e"], [13, 65, 1, 97], [13, 67, 1, 99, "r"], [13, 68, 1, 100], [13, 70, 1, 102], [14, 6, 1, 104, "value"], [14, 11, 1, 109], [14, 13, 1, 111, "t"], [14, 14, 1, 112], [15, 6, 1, 114, "enumerable"], [15, 16, 1, 124], [15, 18, 1, 126], [15, 19, 1, 127], [15, 20, 1, 128], [16, 6, 1, 130, "configurable"], [16, 18, 1, 142], [16, 20, 1, 144], [16, 21, 1, 145], [16, 22, 1, 146], [17, 6, 1, 148, "writable"], [17, 14, 1, 156], [17, 16, 1, 158], [17, 17, 1, 159], [18, 4, 1, 161], [18, 5, 1, 162], [18, 6, 1, 163], [18, 9, 1, 166, "e"], [18, 10, 1, 167], [18, 11, 1, 168, "r"], [18, 12, 1, 169], [18, 13, 1, 170], [18, 16, 1, 173, "t"], [18, 17, 1, 174], [18, 19, 1, 176, "e"], [18, 20, 1, 177], [19, 2, 1, 179], [20, 2, 2, 0], [20, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 26, 2, 24, "t"], [20, 27, 2, 25], [20, 29, 2, 27], [21, 4, 2, 29], [21, 8, 2, 33, "i"], [21, 9, 2, 34], [21, 12, 2, 37, "_toPrimitive"], [21, 24, 2, 49], [21, 25, 2, 50, "t"], [21, 26, 2, 51], [21, 28, 2, 53], [21, 36, 2, 61], [21, 37, 2, 62], [22, 4, 2, 64], [22, 11, 2, 71], [22, 19, 2, 79], [22, 23, 2, 83], [22, 30, 2, 90, "i"], [22, 31, 2, 91], [22, 34, 2, 94, "i"], [22, 35, 2, 95], [22, 38, 2, 98, "i"], [22, 39, 2, 99], [22, 42, 2, 102], [22, 44, 2, 104], [23, 2, 2, 106], [24, 2, 3, 0], [24, 11, 3, 9, "_toPrimitive"], [24, 23, 3, 21, "_toPrimitive"], [24, 24, 3, 22, "t"], [24, 25, 3, 23], [24, 27, 3, 25, "r"], [24, 28, 3, 26], [24, 30, 3, 28], [25, 4, 3, 30], [25, 8, 3, 34], [25, 16, 3, 42], [25, 20, 3, 46], [25, 27, 3, 53, "t"], [25, 28, 3, 54], [25, 32, 3, 58], [25, 33, 3, 59, "t"], [25, 34, 3, 60], [25, 36, 3, 62], [25, 43, 3, 69, "t"], [25, 44, 3, 70], [26, 4, 3, 72], [26, 8, 3, 76, "e"], [26, 9, 3, 77], [26, 12, 3, 80, "t"], [26, 13, 3, 81], [26, 14, 3, 82, "Symbol"], [26, 20, 3, 88], [26, 21, 3, 89, "toPrimitive"], [26, 32, 3, 100], [26, 33, 3, 101], [27, 4, 3, 103], [27, 8, 3, 107], [27, 13, 3, 112], [27, 14, 3, 113], [27, 19, 3, 118, "e"], [27, 20, 3, 119], [27, 22, 3, 121], [28, 6, 3, 123], [28, 10, 3, 127, "i"], [28, 11, 3, 128], [28, 14, 3, 131, "e"], [28, 15, 3, 132], [28, 16, 3, 133, "call"], [28, 20, 3, 137], [28, 21, 3, 138, "t"], [28, 22, 3, 139], [28, 24, 3, 141, "r"], [28, 25, 3, 142], [28, 29, 3, 146], [28, 38, 3, 155], [28, 39, 3, 156], [29, 6, 3, 158], [29, 10, 3, 162], [29, 18, 3, 170], [29, 22, 3, 174], [29, 29, 3, 181, "i"], [29, 30, 3, 182], [29, 32, 3, 184], [29, 39, 3, 191, "i"], [29, 40, 3, 192], [30, 6, 3, 194], [30, 12, 3, 200], [30, 16, 3, 204, "TypeError"], [30, 25, 3, 213], [30, 26, 3, 214], [30, 72, 3, 260], [30, 73, 3, 261], [31, 4, 3, 263], [32, 4, 3, 265], [32, 11, 3, 272], [32, 12, 3, 273], [32, 20, 3, 281], [32, 25, 3, 286, "r"], [32, 26, 3, 287], [32, 29, 3, 290, "String"], [32, 35, 3, 296], [32, 38, 3, 299, "Number"], [32, 44, 3, 305], [32, 46, 3, 307, "t"], [32, 47, 3, 308], [32, 48, 3, 309], [33, 2, 3, 311], [34, 2, 10, 0], [34, 8, 10, 6, "CommandCount"], [34, 20, 10, 18], [34, 23, 10, 21], [35, 4, 11, 2], [35, 5, 11, 3, "PathVerb"], [35, 20, 11, 11], [35, 21, 11, 12, "Move"], [35, 25, 11, 16], [35, 28, 11, 19], [35, 29, 11, 20], [36, 4, 12, 2], [36, 5, 12, 3, "PathVerb"], [36, 20, 12, 11], [36, 21, 12, 12, "Line"], [36, 25, 12, 16], [36, 28, 12, 19], [36, 29, 12, 20], [37, 4, 13, 2], [37, 5, 13, 3, "PathVerb"], [37, 20, 13, 11], [37, 21, 13, 12, "Quad"], [37, 25, 13, 16], [37, 28, 13, 19], [37, 29, 13, 20], [38, 4, 14, 2], [38, 5, 14, 3, "PathVerb"], [38, 20, 14, 11], [38, 21, 14, 12, "<PERSON><PERSON>"], [38, 26, 14, 17], [38, 29, 14, 20], [38, 30, 14, 21], [39, 4, 15, 2], [39, 5, 15, 3, "PathVerb"], [39, 20, 15, 11], [39, 21, 15, 12, "Cubic"], [39, 26, 15, 17], [39, 29, 15, 20], [39, 30, 15, 21], [40, 4, 16, 2], [40, 5, 16, 3, "PathVerb"], [40, 20, 16, 11], [40, 21, 16, 12, "Close"], [40, 26, 16, 17], [40, 29, 16, 20], [41, 2, 17, 0], [41, 3, 17, 1], [42, 2, 18, 0], [42, 8, 18, 6, "pinT"], [42, 12, 18, 10], [42, 15, 18, 13, "t"], [42, 16, 18, 14], [42, 20, 18, 18, "Math"], [42, 24, 18, 22], [42, 25, 18, 23, "min"], [42, 28, 18, 26], [42, 29, 18, 27, "Math"], [42, 33, 18, 31], [42, 34, 18, 32, "max"], [42, 37, 18, 35], [42, 38, 18, 36, "t"], [42, 39, 18, 37], [42, 41, 18, 39], [42, 42, 18, 40], [42, 43, 18, 41], [42, 45, 18, 43], [42, 46, 18, 44], [42, 47, 18, 45], [43, 2, 19, 7], [43, 8, 19, 13, "JsiSkPath"], [43, 17, 19, 22], [43, 26, 19, 31, "HostObject"], [43, 42, 19, 41], [43, 43, 19, 42], [44, 4, 20, 2, "constructor"], [44, 15, 20, 13, "constructor"], [44, 16, 20, 14, "CanvasKit"], [44, 25, 20, 23], [44, 27, 20, 25, "ref"], [44, 30, 20, 28], [44, 32, 20, 30], [45, 6, 21, 4], [45, 11, 21, 9], [45, 12, 21, 10, "CanvasKit"], [45, 21, 21, 19], [45, 23, 21, 21, "ref"], [45, 26, 21, 24], [45, 28, 21, 26], [45, 34, 21, 32], [45, 35, 21, 33], [46, 6, 22, 4, "_defineProperty"], [46, 21, 22, 19], [46, 22, 22, 20], [46, 26, 22, 24], [46, 28, 22, 26], [46, 37, 22, 35], [46, 39, 22, 37], [46, 45, 22, 43], [47, 8, 23, 6], [47, 12, 23, 10], [47, 13, 23, 11, "ref"], [47, 16, 23, 14], [47, 17, 23, 15, "delete"], [47, 23, 23, 21], [47, 24, 23, 22], [47, 25, 23, 23], [48, 6, 24, 4], [48, 7, 24, 5], [48, 8, 24, 6], [49, 4, 25, 2], [50, 4, 26, 2, "addPath"], [50, 11, 26, 9, "addPath"], [50, 12, 26, 10, "src"], [50, 15, 26, 13], [50, 17, 26, 15, "matrix"], [50, 23, 26, 21], [50, 25, 26, 23, "extend"], [50, 31, 26, 29], [50, 34, 26, 32], [50, 39, 26, 37], [50, 41, 26, 39], [51, 6, 27, 4], [51, 12, 27, 10, "args"], [51, 16, 27, 14], [51, 19, 27, 17], [51, 20, 27, 18, "JsiSkPath"], [51, 29, 27, 27], [51, 30, 27, 28, "fromValue"], [51, 39, 27, 37], [51, 40, 27, 38, "src"], [51, 43, 27, 41], [51, 44, 27, 42], [51, 46, 27, 44], [51, 50, 27, 48, "matrix"], [51, 56, 27, 54], [51, 59, 27, 57, "JsiSkMatrix"], [51, 83, 27, 68], [51, 84, 27, 69, "fromValue"], [51, 93, 27, 78], [51, 94, 27, 79, "matrix"], [51, 100, 27, 85], [51, 101, 27, 86], [51, 104, 27, 89], [51, 106, 27, 91], [51, 107, 27, 92], [51, 109, 27, 94, "extend"], [51, 115, 27, 100], [51, 116, 27, 101], [52, 6, 28, 4], [52, 10, 28, 8], [52, 11, 28, 9, "ref"], [52, 14, 28, 12], [52, 15, 28, 13, "addPath"], [52, 22, 28, 20], [52, 23, 28, 21], [52, 26, 28, 24, "args"], [52, 30, 28, 28], [52, 31, 28, 29], [53, 6, 29, 4], [53, 13, 29, 11], [53, 17, 29, 15], [54, 4, 30, 2], [55, 4, 31, 2, "addArc"], [55, 10, 31, 8, "addArc"], [55, 11, 31, 9, "oval"], [55, 15, 31, 13], [55, 17, 31, 15, "startAngleInDegrees"], [55, 36, 31, 34], [55, 38, 31, 36, "sweepAngleInDegrees"], [55, 57, 31, 55], [55, 59, 31, 57], [56, 6, 32, 4], [56, 10, 32, 8], [56, 11, 32, 9, "ref"], [56, 14, 32, 12], [56, 15, 32, 13, "addArc"], [56, 21, 32, 19], [56, 22, 32, 20, "JsiSkRect"], [56, 42, 32, 29], [56, 43, 32, 30, "fromValue"], [56, 52, 32, 39], [56, 53, 32, 40], [56, 57, 32, 44], [56, 58, 32, 45, "CanvasKit"], [56, 67, 32, 54], [56, 69, 32, 56, "oval"], [56, 73, 32, 60], [56, 74, 32, 61], [56, 76, 32, 63, "startAngleInDegrees"], [56, 95, 32, 82], [56, 97, 32, 84, "sweepAngleInDegrees"], [56, 116, 32, 103], [56, 117, 32, 104], [57, 6, 33, 4], [57, 13, 33, 11], [57, 17, 33, 15], [58, 4, 34, 2], [59, 4, 35, 2, "addOval"], [59, 11, 35, 9, "addOval"], [59, 12, 35, 10, "oval"], [59, 16, 35, 14], [59, 18, 35, 16, "isCCW"], [59, 23, 35, 21], [59, 25, 35, 23, "startIndex"], [59, 35, 35, 33], [59, 37, 35, 35], [60, 6, 36, 4], [60, 10, 36, 8], [60, 11, 36, 9, "ref"], [60, 14, 36, 12], [60, 15, 36, 13, "addOval"], [60, 22, 36, 20], [60, 23, 36, 21, "JsiSkRect"], [60, 43, 36, 30], [60, 44, 36, 31, "fromValue"], [60, 53, 36, 40], [60, 54, 36, 41], [60, 58, 36, 45], [60, 59, 36, 46, "CanvasKit"], [60, 68, 36, 55], [60, 70, 36, 57, "oval"], [60, 74, 36, 61], [60, 75, 36, 62], [60, 77, 36, 64, "isCCW"], [60, 82, 36, 69], [60, 84, 36, 71, "startIndex"], [60, 94, 36, 81], [60, 95, 36, 82], [61, 6, 37, 4], [61, 13, 37, 11], [61, 17, 37, 15], [62, 4, 38, 2], [63, 4, 39, 2, "countPoints"], [63, 15, 39, 13, "countPoints"], [63, 16, 39, 13], [63, 18, 39, 16], [64, 6, 40, 4], [64, 13, 40, 11], [64, 17, 40, 15], [64, 18, 40, 16, "ref"], [64, 21, 40, 19], [64, 22, 40, 20, "countPoints"], [64, 33, 40, 31], [64, 34, 40, 32], [64, 35, 40, 33], [65, 4, 41, 2], [66, 4, 42, 2, "addPoly"], [66, 11, 42, 9, "addPoly"], [66, 12, 42, 10, "points"], [66, 18, 42, 16], [66, 20, 42, 18, "close"], [66, 25, 42, 23], [66, 27, 42, 25], [67, 6, 43, 4], [67, 10, 43, 8], [67, 11, 43, 9, "ref"], [67, 14, 43, 12], [67, 15, 43, 13, "addPoly"], [67, 22, 43, 20], [67, 23, 43, 21, "points"], [67, 29, 43, 27], [67, 30, 43, 28, "map"], [67, 33, 43, 31], [67, 34, 43, 32, "p"], [67, 35, 43, 33], [67, 39, 43, 37, "Array"], [67, 44, 43, 42], [67, 45, 43, 43, "from"], [67, 49, 43, 47], [67, 50, 43, 48, "JsiSkPoint"], [67, 72, 43, 58], [67, 73, 43, 59, "fromValue"], [67, 82, 43, 68], [67, 83, 43, 69, "p"], [67, 84, 43, 70], [67, 85, 43, 71], [67, 86, 43, 72], [67, 87, 43, 73], [67, 88, 43, 74, "flat"], [67, 92, 43, 78], [67, 93, 43, 79], [67, 94, 43, 80], [67, 96, 43, 82, "close"], [67, 101, 43, 87], [67, 102, 43, 88], [68, 6, 44, 4], [68, 13, 44, 11], [68, 17, 44, 15], [69, 4, 45, 2], [70, 4, 46, 2, "moveTo"], [70, 10, 46, 8, "moveTo"], [70, 11, 46, 9, "x"], [70, 12, 46, 10], [70, 14, 46, 12, "y"], [70, 15, 46, 13], [70, 17, 46, 15], [71, 6, 47, 4], [71, 10, 47, 8], [71, 11, 47, 9, "ref"], [71, 14, 47, 12], [71, 15, 47, 13, "moveTo"], [71, 21, 47, 19], [71, 22, 47, 20, "x"], [71, 23, 47, 21], [71, 25, 47, 23, "y"], [71, 26, 47, 24], [71, 27, 47, 25], [72, 6, 48, 4], [72, 13, 48, 11], [72, 17, 48, 15], [73, 4, 49, 2], [74, 4, 50, 2, "lineTo"], [74, 10, 50, 8, "lineTo"], [74, 11, 50, 9, "x"], [74, 12, 50, 10], [74, 14, 50, 12, "y"], [74, 15, 50, 13], [74, 17, 50, 15], [75, 6, 51, 4], [75, 10, 51, 8], [75, 11, 51, 9, "ref"], [75, 14, 51, 12], [75, 15, 51, 13, "lineTo"], [75, 21, 51, 19], [75, 22, 51, 20, "x"], [75, 23, 51, 21], [75, 25, 51, 23, "y"], [75, 26, 51, 24], [75, 27, 51, 25], [76, 6, 52, 4], [76, 13, 52, 11], [76, 17, 52, 15], [77, 4, 53, 2], [78, 4, 54, 2, "makeAsWinding"], [78, 17, 54, 15, "makeAsWinding"], [78, 18, 54, 15], [78, 20, 54, 18], [79, 6, 55, 4], [79, 12, 55, 10, "result"], [79, 18, 55, 16], [79, 21, 55, 19], [79, 25, 55, 23], [79, 26, 55, 24, "ref"], [79, 29, 55, 27], [79, 30, 55, 28, "makeAsWinding"], [79, 43, 55, 41], [79, 44, 55, 42], [79, 45, 55, 43], [80, 6, 56, 4], [80, 13, 56, 11, "result"], [80, 19, 56, 17], [80, 24, 56, 22], [80, 28, 56, 26], [80, 31, 56, 29, "result"], [80, 37, 56, 35], [80, 40, 56, 38], [80, 44, 56, 42], [81, 4, 57, 2], [82, 4, 58, 2, "offset"], [82, 10, 58, 8, "offset"], [82, 11, 58, 9, "dx"], [82, 13, 58, 11], [82, 15, 58, 13, "dy"], [82, 17, 58, 15], [82, 19, 58, 17], [83, 6, 59, 4], [83, 10, 59, 8], [83, 11, 59, 9, "ref"], [83, 14, 59, 12], [83, 15, 59, 13, "offset"], [83, 21, 59, 19], [83, 22, 59, 20, "dx"], [83, 24, 59, 22], [83, 26, 59, 24, "dy"], [83, 28, 59, 26], [83, 29, 59, 27], [84, 6, 60, 4], [84, 13, 60, 11], [84, 17, 60, 15], [85, 4, 61, 2], [86, 4, 62, 2, "rArcTo"], [86, 10, 62, 8, "rArcTo"], [86, 11, 62, 9, "rx"], [86, 13, 62, 11], [86, 15, 62, 13, "ry"], [86, 17, 62, 15], [86, 19, 62, 17, "xAxisRotateInDegrees"], [86, 39, 62, 37], [86, 41, 62, 39, "useSmallArc"], [86, 52, 62, 50], [86, 54, 62, 52, "isCCW"], [86, 59, 62, 57], [86, 61, 62, 59, "dx"], [86, 63, 62, 61], [86, 65, 62, 63, "dy"], [86, 67, 62, 65], [86, 69, 62, 67], [87, 6, 63, 4], [87, 10, 63, 8], [87, 11, 63, 9, "ref"], [87, 14, 63, 12], [87, 15, 63, 13, "rArcTo"], [87, 21, 63, 19], [87, 22, 63, 20, "rx"], [87, 24, 63, 22], [87, 26, 63, 24, "ry"], [87, 28, 63, 26], [87, 30, 63, 28, "xAxisRotateInDegrees"], [87, 50, 63, 48], [87, 52, 63, 50, "useSmallArc"], [87, 63, 63, 61], [87, 65, 63, 63, "isCCW"], [87, 70, 63, 68], [87, 72, 63, 70, "dx"], [87, 74, 63, 72], [87, 76, 63, 74, "dy"], [87, 78, 63, 76], [87, 79, 63, 77], [88, 6, 64, 4], [88, 13, 64, 11], [88, 17, 64, 15], [89, 4, 65, 2], [90, 4, 66, 2, "rConicTo"], [90, 12, 66, 10, "rConicTo"], [90, 13, 66, 11, "dx1"], [90, 16, 66, 14], [90, 18, 66, 16, "dy1"], [90, 21, 66, 19], [90, 23, 66, 21, "dx2"], [90, 26, 66, 24], [90, 28, 66, 26, "dy2"], [90, 31, 66, 29], [90, 33, 66, 31, "w"], [90, 34, 66, 32], [90, 36, 66, 34], [91, 6, 67, 4], [91, 10, 67, 8], [91, 11, 67, 9, "ref"], [91, 14, 67, 12], [91, 15, 67, 13, "rConicTo"], [91, 23, 67, 21], [91, 24, 67, 22, "dx1"], [91, 27, 67, 25], [91, 29, 67, 27, "dy1"], [91, 32, 67, 30], [91, 34, 67, 32, "dx2"], [91, 37, 67, 35], [91, 39, 67, 37, "dy2"], [91, 42, 67, 40], [91, 44, 67, 42, "w"], [91, 45, 67, 43], [91, 46, 67, 44], [92, 6, 68, 4], [92, 13, 68, 11], [92, 17, 68, 15], [93, 4, 69, 2], [94, 4, 70, 2, "rCubicTo"], [94, 12, 70, 10, "rCubicTo"], [94, 13, 70, 11, "cpx1"], [94, 17, 70, 15], [94, 19, 70, 17, "cpy1"], [94, 23, 70, 21], [94, 25, 70, 23, "cpx2"], [94, 29, 70, 27], [94, 31, 70, 29, "cpy2"], [94, 35, 70, 33], [94, 37, 70, 35, "x"], [94, 38, 70, 36], [94, 40, 70, 38, "y"], [94, 41, 70, 39], [94, 43, 70, 41], [95, 6, 71, 4], [95, 10, 71, 8], [95, 11, 71, 9, "ref"], [95, 14, 71, 12], [95, 15, 71, 13, "rCubicTo"], [95, 23, 71, 21], [95, 24, 71, 22, "cpx1"], [95, 28, 71, 26], [95, 30, 71, 28, "cpy1"], [95, 34, 71, 32], [95, 36, 71, 34, "cpx2"], [95, 40, 71, 38], [95, 42, 71, 40, "cpy2"], [95, 46, 71, 44], [95, 48, 71, 46, "x"], [95, 49, 71, 47], [95, 51, 71, 49, "y"], [95, 52, 71, 50], [95, 53, 71, 51], [96, 6, 72, 4], [96, 13, 72, 11], [96, 17, 72, 15], [97, 4, 73, 2], [98, 4, 74, 2, "rMoveTo"], [98, 11, 74, 9, "rMoveTo"], [98, 12, 74, 10, "x"], [98, 13, 74, 11], [98, 15, 74, 13, "y"], [98, 16, 74, 14], [98, 18, 74, 16], [99, 6, 75, 4], [99, 10, 75, 8], [99, 11, 75, 9, "ref"], [99, 14, 75, 12], [99, 15, 75, 13, "rMoveTo"], [99, 22, 75, 20], [99, 23, 75, 21, "x"], [99, 24, 75, 22], [99, 26, 75, 24, "y"], [99, 27, 75, 25], [99, 28, 75, 26], [100, 6, 76, 4], [100, 13, 76, 11], [100, 17, 76, 15], [101, 4, 77, 2], [102, 4, 78, 2, "rLineTo"], [102, 11, 78, 9, "rLineTo"], [102, 12, 78, 10, "x"], [102, 13, 78, 11], [102, 15, 78, 13, "y"], [102, 16, 78, 14], [102, 18, 78, 16], [103, 6, 79, 4], [103, 10, 79, 8], [103, 11, 79, 9, "ref"], [103, 14, 79, 12], [103, 15, 79, 13, "rLineTo"], [103, 22, 79, 20], [103, 23, 79, 21, "x"], [103, 24, 79, 22], [103, 26, 79, 24, "y"], [103, 27, 79, 25], [103, 28, 79, 26], [104, 6, 80, 4], [104, 13, 80, 11], [104, 17, 80, 15], [105, 4, 81, 2], [106, 4, 82, 2, "rQuadTo"], [106, 11, 82, 9, "rQuadTo"], [106, 12, 82, 10, "x1"], [106, 14, 82, 12], [106, 16, 82, 14, "y1"], [106, 18, 82, 16], [106, 20, 82, 18, "x2"], [106, 22, 82, 20], [106, 24, 82, 22, "y2"], [106, 26, 82, 24], [106, 28, 82, 26], [107, 6, 83, 4], [107, 10, 83, 8], [107, 11, 83, 9, "ref"], [107, 14, 83, 12], [107, 15, 83, 13, "rQuadTo"], [107, 22, 83, 20], [107, 23, 83, 21, "x1"], [107, 25, 83, 23], [107, 27, 83, 25, "y1"], [107, 29, 83, 27], [107, 31, 83, 29, "x2"], [107, 33, 83, 31], [107, 35, 83, 33, "y2"], [107, 37, 83, 35], [107, 38, 83, 36], [108, 6, 84, 4], [108, 13, 84, 11], [108, 17, 84, 15], [109, 4, 85, 2], [110, 4, 86, 2, "setFillType"], [110, 15, 86, 13, "setFillType"], [110, 16, 86, 14, "fill"], [110, 20, 86, 18], [110, 22, 86, 20], [111, 6, 87, 4], [111, 10, 87, 8], [111, 11, 87, 9, "ref"], [111, 14, 87, 12], [111, 15, 87, 13, "setFillType"], [111, 26, 87, 24], [111, 27, 87, 25], [111, 31, 87, 25, "getEnum"], [111, 44, 87, 32], [111, 46, 87, 33], [111, 50, 87, 37], [111, 51, 87, 38, "CanvasKit"], [111, 60, 87, 47], [111, 62, 87, 49], [111, 72, 87, 59], [111, 74, 87, 61, "fill"], [111, 78, 87, 65], [111, 79, 87, 66], [111, 80, 87, 67], [112, 6, 88, 4], [112, 13, 88, 11], [112, 17, 88, 15], [113, 4, 89, 2], [114, 4, 90, 2, "setIsVolatile"], [114, 17, 90, 15, "setIsVolatile"], [114, 18, 90, 16, "volatile"], [114, 26, 90, 24], [114, 28, 90, 26], [115, 6, 91, 4], [115, 10, 91, 8], [115, 11, 91, 9, "ref"], [115, 14, 91, 12], [115, 15, 91, 13, "setIsVolatile"], [115, 28, 91, 26], [115, 29, 91, 27, "volatile"], [115, 37, 91, 35], [115, 38, 91, 36], [116, 6, 92, 4], [116, 13, 92, 11], [116, 17, 92, 15], [117, 4, 93, 2], [118, 4, 94, 2, "stroke"], [118, 10, 94, 8, "stroke"], [118, 11, 94, 9, "opts"], [118, 15, 94, 13], [118, 17, 94, 15], [119, 6, 95, 4], [119, 12, 95, 10, "result"], [119, 18, 95, 16], [119, 21, 95, 19], [119, 25, 95, 23], [119, 26, 95, 24, "ref"], [119, 29, 95, 27], [119, 30, 95, 28, "stroke"], [119, 36, 95, 34], [119, 37, 95, 35, "opts"], [119, 41, 95, 39], [119, 46, 95, 44, "undefined"], [119, 55, 95, 53], [119, 58, 95, 56, "undefined"], [119, 67, 95, 65], [119, 70, 95, 68], [120, 8, 96, 6, "width"], [120, 13, 96, 11], [120, 15, 96, 13, "opts"], [120, 19, 96, 17], [120, 20, 96, 18, "width"], [120, 25, 96, 23], [121, 8, 97, 6], [122, 8, 98, 6, "miter_limit"], [122, 19, 98, 17], [122, 21, 98, 19, "opts"], [122, 25, 98, 23], [122, 26, 98, 24, "width"], [122, 31, 98, 29], [123, 8, 99, 6, "precision"], [123, 17, 99, 15], [123, 19, 99, 17, "opts"], [123, 23, 99, 21], [123, 24, 99, 22, "width"], [123, 29, 99, 27], [124, 8, 100, 6, "join"], [124, 12, 100, 10], [124, 14, 100, 12], [124, 18, 100, 12, "optEnum"], [124, 31, 100, 19], [124, 33, 100, 20], [124, 37, 100, 24], [124, 38, 100, 25, "CanvasKit"], [124, 47, 100, 34], [124, 49, 100, 36], [124, 61, 100, 48], [124, 63, 100, 50, "opts"], [124, 67, 100, 54], [124, 68, 100, 55, "join"], [124, 72, 100, 59], [124, 73, 100, 60], [125, 8, 101, 6, "cap"], [125, 11, 101, 9], [125, 13, 101, 11], [125, 17, 101, 11, "optEnum"], [125, 30, 101, 18], [125, 32, 101, 19], [125, 36, 101, 23], [125, 37, 101, 24, "CanvasKit"], [125, 46, 101, 33], [125, 48, 101, 35], [125, 59, 101, 46], [125, 61, 101, 48, "opts"], [125, 65, 101, 52], [125, 66, 101, 53, "cap"], [125, 69, 101, 56], [126, 6, 102, 4], [126, 7, 102, 5], [126, 8, 102, 6], [127, 6, 103, 4], [127, 13, 103, 11, "result"], [127, 19, 103, 17], [127, 24, 103, 22], [127, 28, 103, 26], [127, 31, 103, 29, "result"], [127, 37, 103, 35], [127, 40, 103, 38], [127, 44, 103, 42], [128, 4, 104, 2], [129, 4, 105, 2, "close"], [129, 9, 105, 7, "close"], [129, 10, 105, 7], [129, 12, 105, 10], [130, 6, 106, 4], [130, 10, 106, 8], [130, 11, 106, 9, "ref"], [130, 14, 106, 12], [130, 15, 106, 13, "close"], [130, 20, 106, 18], [130, 21, 106, 19], [130, 22, 106, 20], [131, 6, 107, 4], [131, 13, 107, 11], [131, 17, 107, 15], [132, 4, 108, 2], [133, 4, 109, 2, "reset"], [133, 9, 109, 7, "reset"], [133, 10, 109, 7], [133, 12, 109, 10], [134, 6, 110, 4], [134, 10, 110, 8], [134, 11, 110, 9, "ref"], [134, 14, 110, 12], [134, 15, 110, 13, "reset"], [134, 20, 110, 18], [134, 21, 110, 19], [134, 22, 110, 20], [135, 6, 111, 4], [135, 13, 111, 11], [135, 17, 111, 15], [136, 4, 112, 2], [137, 4, 113, 2, "rewind"], [137, 10, 113, 8, "rewind"], [137, 11, 113, 8], [137, 13, 113, 11], [138, 6, 114, 4], [138, 10, 114, 8], [138, 11, 114, 9, "ref"], [138, 14, 114, 12], [138, 15, 114, 13, "rewind"], [138, 21, 114, 19], [138, 22, 114, 20], [138, 23, 114, 21], [139, 6, 115, 4], [139, 13, 115, 11], [139, 17, 115, 15], [140, 4, 116, 2], [141, 4, 117, 2, "computeTightBounds"], [141, 22, 117, 20, "computeTightBounds"], [141, 23, 117, 20], [141, 25, 117, 23], [142, 6, 118, 4], [142, 13, 118, 11], [142, 17, 118, 15, "JsiSkRect"], [142, 37, 118, 24], [142, 38, 118, 25], [142, 42, 118, 29], [142, 43, 118, 30, "CanvasKit"], [142, 52, 118, 39], [142, 54, 118, 41], [142, 58, 118, 45], [142, 59, 118, 46, "ref"], [142, 62, 118, 49], [142, 63, 118, 50, "computeTightBounds"], [142, 81, 118, 68], [142, 82, 118, 69], [142, 83, 118, 70], [142, 84, 118, 71], [143, 4, 119, 2], [144, 4, 120, 2, "arcToOval"], [144, 13, 120, 11, "arcToOval"], [144, 14, 120, 12, "oval"], [144, 18, 120, 16], [144, 20, 120, 18, "startAngleInDegrees"], [144, 39, 120, 37], [144, 41, 120, 39, "sweepAngleInDegrees"], [144, 60, 120, 58], [144, 62, 120, 60, "forceMoveTo"], [144, 73, 120, 71], [144, 75, 120, 73], [145, 6, 121, 4], [145, 10, 121, 8], [145, 11, 121, 9, "ref"], [145, 14, 121, 12], [145, 15, 121, 13, "arcToOval"], [145, 24, 121, 22], [145, 25, 121, 23, "JsiSkRect"], [145, 45, 121, 32], [145, 46, 121, 33, "fromValue"], [145, 55, 121, 42], [145, 56, 121, 43], [145, 60, 121, 47], [145, 61, 121, 48, "CanvasKit"], [145, 70, 121, 57], [145, 72, 121, 59, "oval"], [145, 76, 121, 63], [145, 77, 121, 64], [145, 79, 121, 66, "startAngleInDegrees"], [145, 98, 121, 85], [145, 100, 121, 87, "sweepAngleInDegrees"], [145, 119, 121, 106], [145, 121, 121, 108, "forceMoveTo"], [145, 132, 121, 119], [145, 133, 121, 120], [146, 6, 122, 4], [146, 13, 122, 11], [146, 17, 122, 15], [147, 4, 123, 2], [148, 4, 124, 2, "arcToRotated"], [148, 16, 124, 14, "arcToRotated"], [148, 17, 124, 15, "rx"], [148, 19, 124, 17], [148, 21, 124, 19, "ry"], [148, 23, 124, 21], [148, 25, 124, 23, "xAxisRotateInDegrees"], [148, 45, 124, 43], [148, 47, 124, 45, "useSmallArc"], [148, 58, 124, 56], [148, 60, 124, 58, "isCCW"], [148, 65, 124, 63], [148, 67, 124, 65, "x"], [148, 68, 124, 66], [148, 70, 124, 68, "y"], [148, 71, 124, 69], [148, 73, 124, 71], [149, 6, 125, 4], [149, 10, 125, 8], [149, 11, 125, 9, "ref"], [149, 14, 125, 12], [149, 15, 125, 13, "arcToRotated"], [149, 27, 125, 25], [149, 28, 125, 26, "rx"], [149, 30, 125, 28], [149, 32, 125, 30, "ry"], [149, 34, 125, 32], [149, 36, 125, 34, "xAxisRotateInDegrees"], [149, 56, 125, 54], [149, 58, 125, 56, "useSmallArc"], [149, 69, 125, 67], [149, 71, 125, 69, "isCCW"], [149, 76, 125, 74], [149, 78, 125, 76, "x"], [149, 79, 125, 77], [149, 81, 125, 79, "y"], [149, 82, 125, 80], [149, 83, 125, 81], [150, 6, 126, 4], [150, 13, 126, 11], [150, 17, 126, 15], [151, 4, 127, 2], [152, 4, 128, 2, "arcToTangent"], [152, 16, 128, 14, "arcToTangent"], [152, 17, 128, 15, "x1"], [152, 19, 128, 17], [152, 21, 128, 19, "y1"], [152, 23, 128, 21], [152, 25, 128, 23, "x2"], [152, 27, 128, 25], [152, 29, 128, 27, "y2"], [152, 31, 128, 29], [152, 33, 128, 31, "radius"], [152, 39, 128, 37], [152, 41, 128, 39], [153, 6, 129, 4], [153, 10, 129, 8], [153, 11, 129, 9, "ref"], [153, 14, 129, 12], [153, 15, 129, 13, "arcToTangent"], [153, 27, 129, 25], [153, 28, 129, 26, "x1"], [153, 30, 129, 28], [153, 32, 129, 30, "y1"], [153, 34, 129, 32], [153, 36, 129, 34, "x2"], [153, 38, 129, 36], [153, 40, 129, 38, "y2"], [153, 42, 129, 40], [153, 44, 129, 42, "radius"], [153, 50, 129, 48], [153, 51, 129, 49], [154, 6, 130, 4], [154, 13, 130, 11], [154, 17, 130, 15], [155, 4, 131, 2], [156, 4, 132, 2, "conicTo"], [156, 11, 132, 9, "conicTo"], [156, 12, 132, 10, "x1"], [156, 14, 132, 12], [156, 16, 132, 14, "y1"], [156, 18, 132, 16], [156, 20, 132, 18, "x2"], [156, 22, 132, 20], [156, 24, 132, 22, "y2"], [156, 26, 132, 24], [156, 28, 132, 26, "w"], [156, 29, 132, 27], [156, 31, 132, 29], [157, 6, 133, 4], [157, 10, 133, 8], [157, 11, 133, 9, "ref"], [157, 14, 133, 12], [157, 15, 133, 13, "conicTo"], [157, 22, 133, 20], [157, 23, 133, 21, "x1"], [157, 25, 133, 23], [157, 27, 133, 25, "y1"], [157, 29, 133, 27], [157, 31, 133, 29, "x2"], [157, 33, 133, 31], [157, 35, 133, 33, "y2"], [157, 37, 133, 35], [157, 39, 133, 37, "w"], [157, 40, 133, 38], [157, 41, 133, 39], [158, 6, 134, 4], [158, 13, 134, 11], [158, 17, 134, 15], [159, 4, 135, 2], [160, 4, 136, 2, "contains"], [160, 12, 136, 10, "contains"], [160, 13, 136, 11, "x"], [160, 14, 136, 12], [160, 16, 136, 14, "y"], [160, 17, 136, 15], [160, 19, 136, 17], [161, 6, 137, 4], [161, 13, 137, 11], [161, 17, 137, 15], [161, 18, 137, 16, "ref"], [161, 21, 137, 19], [161, 22, 137, 20, "contains"], [161, 30, 137, 28], [161, 31, 137, 29, "x"], [161, 32, 137, 30], [161, 34, 137, 32, "y"], [161, 35, 137, 33], [161, 36, 137, 34], [162, 4, 138, 2], [163, 4, 139, 2, "copy"], [163, 8, 139, 6, "copy"], [163, 9, 139, 6], [163, 11, 139, 9], [164, 6, 140, 4], [164, 13, 140, 11], [164, 17, 140, 15, "JsiSkPath"], [164, 26, 140, 24], [164, 27, 140, 25], [164, 31, 140, 29], [164, 32, 140, 30, "CanvasKit"], [164, 41, 140, 39], [164, 43, 140, 41], [164, 47, 140, 45], [164, 48, 140, 46, "ref"], [164, 51, 140, 49], [164, 52, 140, 50, "copy"], [164, 56, 140, 54], [164, 57, 140, 55], [164, 58, 140, 56], [164, 59, 140, 57], [165, 4, 141, 2], [166, 4, 142, 2, "cubicTo"], [166, 11, 142, 9, "cubicTo"], [166, 12, 142, 10, "cpx1"], [166, 16, 142, 14], [166, 18, 142, 16, "cpy1"], [166, 22, 142, 20], [166, 24, 142, 22, "cpx2"], [166, 28, 142, 26], [166, 30, 142, 28, "cpy2"], [166, 34, 142, 32], [166, 36, 142, 34, "x"], [166, 37, 142, 35], [166, 39, 142, 37, "y"], [166, 40, 142, 38], [166, 42, 142, 40], [167, 6, 143, 4], [167, 10, 143, 8], [167, 11, 143, 9, "ref"], [167, 14, 143, 12], [167, 15, 143, 13, "cubicTo"], [167, 22, 143, 20], [167, 23, 143, 21, "cpx1"], [167, 27, 143, 25], [167, 29, 143, 27, "cpy1"], [167, 33, 143, 31], [167, 35, 143, 33, "cpx2"], [167, 39, 143, 37], [167, 41, 143, 39, "cpy2"], [167, 45, 143, 43], [167, 47, 143, 45, "x"], [167, 48, 143, 46], [167, 50, 143, 48, "y"], [167, 51, 143, 49], [167, 52, 143, 50], [168, 6, 144, 4], [168, 13, 144, 11], [168, 17, 144, 15], [169, 4, 145, 2], [170, 4, 146, 2, "dash"], [170, 8, 146, 6, "dash"], [170, 9, 146, 7, "on"], [170, 11, 146, 9], [170, 13, 146, 11, "off"], [170, 16, 146, 14], [170, 18, 146, 16, "phase"], [170, 23, 146, 21], [170, 25, 146, 23], [171, 6, 147, 4], [171, 13, 147, 11], [171, 17, 147, 15], [171, 18, 147, 16, "ref"], [171, 21, 147, 19], [171, 22, 147, 20, "dash"], [171, 26, 147, 24], [171, 27, 147, 25, "on"], [171, 29, 147, 27], [171, 31, 147, 29, "off"], [171, 34, 147, 32], [171, 36, 147, 34, "phase"], [171, 41, 147, 39], [171, 42, 147, 40], [172, 4, 148, 2], [173, 4, 149, 2, "equals"], [173, 10, 149, 8, "equals"], [173, 11, 149, 9, "other"], [173, 16, 149, 14], [173, 18, 149, 16], [174, 6, 150, 4], [174, 13, 150, 11], [174, 17, 150, 15], [174, 18, 150, 16, "ref"], [174, 21, 150, 19], [174, 22, 150, 20, "equals"], [174, 28, 150, 26], [174, 29, 150, 27, "JsiSkPath"], [174, 38, 150, 36], [174, 39, 150, 37, "fromValue"], [174, 48, 150, 46], [174, 49, 150, 47, "other"], [174, 54, 150, 52], [174, 55, 150, 53], [174, 56, 150, 54], [175, 4, 151, 2], [176, 4, 152, 2, "getBounds"], [176, 13, 152, 11, "getBounds"], [176, 14, 152, 11], [176, 16, 152, 14], [177, 6, 153, 4], [177, 13, 153, 11], [177, 17, 153, 15, "JsiSkRect"], [177, 37, 153, 24], [177, 38, 153, 25], [177, 42, 153, 29], [177, 43, 153, 30, "CanvasKit"], [177, 52, 153, 39], [177, 54, 153, 41], [177, 58, 153, 45], [177, 59, 153, 46, "ref"], [177, 62, 153, 49], [177, 63, 153, 50, "getBounds"], [177, 72, 153, 59], [177, 73, 153, 60], [177, 74, 153, 61], [177, 75, 153, 62], [178, 4, 154, 2], [179, 4, 155, 2, "getFillType"], [179, 15, 155, 13, "getFillType"], [179, 16, 155, 13], [179, 18, 155, 16], [180, 6, 156, 4], [180, 13, 156, 11], [180, 17, 156, 15], [180, 18, 156, 16, "ref"], [180, 21, 156, 19], [180, 22, 156, 20, "getFillType"], [180, 33, 156, 31], [180, 34, 156, 32], [180, 35, 156, 33], [180, 36, 156, 34, "value"], [180, 41, 156, 39], [181, 4, 157, 2], [182, 4, 158, 2, "quadTo"], [182, 10, 158, 8, "quadTo"], [182, 11, 158, 9, "x1"], [182, 13, 158, 11], [182, 15, 158, 13, "y1"], [182, 17, 158, 15], [182, 19, 158, 17, "x2"], [182, 21, 158, 19], [182, 23, 158, 21, "y2"], [182, 25, 158, 23], [182, 27, 158, 25], [183, 6, 159, 4], [183, 10, 159, 8], [183, 11, 159, 9, "ref"], [183, 14, 159, 12], [183, 15, 159, 13, "quadTo"], [183, 21, 159, 19], [183, 22, 159, 20, "x1"], [183, 24, 159, 22], [183, 26, 159, 24, "y1"], [183, 28, 159, 26], [183, 30, 159, 28, "x2"], [183, 32, 159, 30], [183, 34, 159, 32, "y2"], [183, 36, 159, 34], [183, 37, 159, 35], [184, 6, 160, 4], [184, 13, 160, 11], [184, 17, 160, 15], [185, 4, 161, 2], [186, 4, 162, 2, "addRect"], [186, 11, 162, 9, "addRect"], [186, 12, 162, 10, "rect"], [186, 16, 162, 14], [186, 18, 162, 16, "isCCW"], [186, 23, 162, 21], [186, 25, 162, 23], [187, 6, 163, 4], [187, 10, 163, 8], [187, 11, 163, 9, "ref"], [187, 14, 163, 12], [187, 15, 163, 13, "addRect"], [187, 22, 163, 20], [187, 23, 163, 21, "JsiSkRect"], [187, 43, 163, 30], [187, 44, 163, 31, "fromValue"], [187, 53, 163, 40], [187, 54, 163, 41], [187, 58, 163, 45], [187, 59, 163, 46, "CanvasKit"], [187, 68, 163, 55], [187, 70, 163, 57, "rect"], [187, 74, 163, 61], [187, 75, 163, 62], [187, 77, 163, 64, "isCCW"], [187, 82, 163, 69], [187, 83, 163, 70], [188, 6, 164, 4], [188, 13, 164, 11], [188, 17, 164, 15], [189, 4, 165, 2], [190, 4, 166, 2, "addRRect"], [190, 12, 166, 10, "addRRect"], [190, 13, 166, 11, "rrect"], [190, 18, 166, 16], [190, 20, 166, 18, "isCCW"], [190, 25, 166, 23], [190, 27, 166, 25], [191, 6, 167, 4], [191, 10, 167, 8], [191, 11, 167, 9, "ref"], [191, 14, 167, 12], [191, 15, 167, 13, "addRRect"], [191, 23, 167, 21], [191, 24, 167, 22, "JsiSkRRect"], [191, 46, 167, 32], [191, 47, 167, 33, "fromValue"], [191, 56, 167, 42], [191, 57, 167, 43], [191, 61, 167, 47], [191, 62, 167, 48, "CanvasKit"], [191, 71, 167, 57], [191, 73, 167, 59, "rrect"], [191, 78, 167, 64], [191, 79, 167, 65], [191, 81, 167, 67, "isCCW"], [191, 86, 167, 72], [191, 87, 167, 73], [192, 6, 168, 4], [192, 13, 168, 11], [192, 17, 168, 15], [193, 4, 169, 2], [194, 4, 170, 2, "getPoint"], [194, 12, 170, 10, "getPoint"], [194, 13, 170, 11, "index"], [194, 18, 170, 16], [194, 20, 170, 18], [195, 6, 171, 4], [195, 13, 171, 11], [195, 17, 171, 15, "JsiSkPoint"], [195, 39, 171, 25], [195, 40, 171, 26], [195, 44, 171, 30], [195, 45, 171, 31, "CanvasKit"], [195, 54, 171, 40], [195, 56, 171, 42], [195, 60, 171, 46], [195, 61, 171, 47, "ref"], [195, 64, 171, 50], [195, 65, 171, 51, "getPoint"], [195, 73, 171, 59], [195, 74, 171, 60, "index"], [195, 79, 171, 65], [195, 80, 171, 66], [195, 81, 171, 67], [196, 4, 172, 2], [197, 4, 173, 2, "isEmpty"], [197, 11, 173, 9, "isEmpty"], [197, 12, 173, 9], [197, 14, 173, 12], [198, 6, 174, 4], [198, 13, 174, 11], [198, 17, 174, 15], [198, 18, 174, 16, "ref"], [198, 21, 174, 19], [198, 22, 174, 20, "isEmpty"], [198, 29, 174, 27], [198, 30, 174, 28], [198, 31, 174, 29], [199, 4, 175, 2], [200, 4, 176, 2, "isVolatile"], [200, 14, 176, 12, "isVolatile"], [200, 15, 176, 12], [200, 17, 176, 15], [201, 6, 177, 4], [201, 13, 177, 11], [201, 17, 177, 15], [201, 18, 177, 16, "ref"], [201, 21, 177, 19], [201, 22, 177, 20, "isVolatile"], [201, 32, 177, 30], [201, 33, 177, 31], [201, 34, 177, 32], [202, 4, 178, 2], [203, 4, 179, 2, "addCircle"], [203, 13, 179, 11, "addCircle"], [203, 14, 179, 12, "x"], [203, 15, 179, 13], [203, 17, 179, 15, "y"], [203, 18, 179, 16], [203, 20, 179, 18, "r"], [203, 21, 179, 19], [203, 23, 179, 21], [204, 6, 180, 4], [204, 10, 180, 8], [204, 11, 180, 9, "ref"], [204, 14, 180, 12], [204, 15, 180, 13, "addCircle"], [204, 24, 180, 22], [204, 25, 180, 23, "x"], [204, 26, 180, 24], [204, 28, 180, 26, "y"], [204, 29, 180, 27], [204, 31, 180, 29, "r"], [204, 32, 180, 30], [204, 33, 180, 31], [205, 6, 181, 4], [205, 13, 181, 11], [205, 17, 181, 15], [206, 4, 182, 2], [207, 4, 183, 2, "getLastPt"], [207, 13, 183, 11, "getLastPt"], [207, 14, 183, 11], [207, 16, 183, 14], [208, 6, 184, 4], [208, 13, 184, 11], [208, 17, 184, 15, "JsiSkPoint"], [208, 39, 184, 25], [208, 40, 184, 26], [208, 44, 184, 30], [208, 45, 184, 31, "CanvasKit"], [208, 54, 184, 40], [208, 56, 184, 42], [208, 60, 184, 46], [208, 61, 184, 47, "ref"], [208, 64, 184, 50], [208, 65, 184, 51, "getPoint"], [208, 73, 184, 59], [208, 74, 184, 60], [208, 78, 184, 64], [208, 79, 184, 65, "ref"], [208, 82, 184, 68], [208, 83, 184, 69, "countPoints"], [208, 94, 184, 80], [208, 95, 184, 81], [208, 96, 184, 82], [208, 99, 184, 85], [208, 100, 184, 86], [208, 101, 184, 87], [208, 102, 184, 88], [209, 4, 185, 2], [210, 4, 186, 2, "op"], [210, 6, 186, 4, "op"], [210, 7, 186, 5, "path"], [210, 11, 186, 9], [210, 13, 186, 11, "op"], [210, 15, 186, 13], [210, 17, 186, 15], [211, 6, 187, 4], [211, 13, 187, 11], [211, 17, 187, 15], [211, 18, 187, 16, "ref"], [211, 21, 187, 19], [211, 22, 187, 20, "op"], [211, 24, 187, 22], [211, 25, 187, 23, "JsiSkPath"], [211, 34, 187, 32], [211, 35, 187, 33, "fromValue"], [211, 44, 187, 42], [211, 45, 187, 43, "path"], [211, 49, 187, 47], [211, 50, 187, 48], [211, 52, 187, 50], [211, 56, 187, 50, "getEnum"], [211, 69, 187, 57], [211, 71, 187, 58], [211, 75, 187, 62], [211, 76, 187, 63, "CanvasKit"], [211, 85, 187, 72], [211, 87, 187, 74], [211, 95, 187, 82], [211, 97, 187, 84, "op"], [211, 99, 187, 86], [211, 100, 187, 87], [211, 101, 187, 88], [212, 4, 188, 2], [213, 4, 189, 2, "simplify"], [213, 12, 189, 10, "simplify"], [213, 13, 189, 10], [213, 15, 189, 13], [214, 6, 190, 4], [214, 13, 190, 11], [214, 17, 190, 15], [214, 18, 190, 16, "ref"], [214, 21, 190, 19], [214, 22, 190, 20, "simplify"], [214, 30, 190, 28], [214, 31, 190, 29], [214, 32, 190, 30], [215, 4, 191, 2], [216, 4, 192, 2, "toSVGString"], [216, 15, 192, 13, "toSVGString"], [216, 16, 192, 13], [216, 18, 192, 16], [217, 6, 193, 4], [217, 13, 193, 11], [217, 17, 193, 15], [217, 18, 193, 16, "ref"], [217, 21, 193, 19], [217, 22, 193, 20, "toSVGString"], [217, 33, 193, 31], [217, 34, 193, 32], [217, 35, 193, 33], [218, 4, 194, 2], [219, 4, 195, 2, "trim"], [219, 8, 195, 6, "trim"], [219, 9, 195, 7, "start"], [219, 14, 195, 12], [219, 16, 195, 14, "stop"], [219, 20, 195, 18], [219, 22, 195, 20, "isComplement"], [219, 34, 195, 32], [219, 36, 195, 34], [220, 6, 196, 4], [220, 12, 196, 10, "startT"], [220, 18, 196, 16], [220, 21, 196, 19, "pinT"], [220, 25, 196, 23], [220, 26, 196, 24, "start"], [220, 31, 196, 29], [220, 32, 196, 30], [221, 6, 197, 4], [221, 12, 197, 10, "stopT"], [221, 17, 197, 15], [221, 20, 197, 18, "pinT"], [221, 24, 197, 22], [221, 25, 197, 23, "stop"], [221, 29, 197, 27], [221, 30, 197, 28], [222, 6, 198, 4], [222, 10, 198, 8, "startT"], [222, 16, 198, 14], [222, 21, 198, 19], [222, 22, 198, 20], [222, 26, 198, 24, "stopT"], [222, 31, 198, 29], [222, 36, 198, 34], [222, 37, 198, 35], [222, 39, 198, 37], [223, 8, 199, 6], [223, 15, 199, 13], [223, 19, 199, 17], [224, 6, 200, 4], [225, 6, 201, 4], [225, 12, 201, 10, "result"], [225, 18, 201, 16], [225, 21, 201, 19], [225, 25, 201, 23], [225, 26, 201, 24, "ref"], [225, 29, 201, 27], [225, 30, 201, 28, "trim"], [225, 34, 201, 32], [225, 35, 201, 33, "startT"], [225, 41, 201, 39], [225, 43, 201, 41, "stopT"], [225, 48, 201, 46], [225, 50, 201, 48, "isComplement"], [225, 62, 201, 60], [225, 63, 201, 61], [226, 6, 202, 4], [226, 13, 202, 11, "result"], [226, 19, 202, 17], [226, 24, 202, 22], [226, 28, 202, 26], [226, 31, 202, 29, "result"], [226, 37, 202, 35], [226, 40, 202, 38], [226, 44, 202, 42], [227, 4, 203, 2], [228, 4, 204, 2, "transform"], [228, 13, 204, 11, "transform"], [228, 14, 204, 12, "m"], [228, 15, 204, 13], [228, 17, 204, 15], [229, 6, 205, 4], [229, 10, 205, 8, "matrix"], [229, 16, 205, 14], [229, 19, 205, 17, "m"], [229, 20, 205, 18], [229, 32, 205, 30, "JsiSkMatrix"], [229, 56, 205, 41], [229, 59, 205, 44, "Array"], [229, 64, 205, 49], [229, 65, 205, 50, "from"], [229, 69, 205, 54], [229, 70, 205, 55, "JsiSkMatrix"], [229, 94, 205, 66], [229, 95, 205, 67, "fromValue"], [229, 104, 205, 76], [229, 105, 205, 77, "m"], [229, 106, 205, 78], [229, 107, 205, 79], [229, 108, 205, 80], [229, 111, 205, 83, "m"], [229, 112, 205, 84], [230, 6, 206, 4], [230, 10, 206, 8, "matrix"], [230, 16, 206, 14], [230, 17, 206, 15, "length"], [230, 23, 206, 21], [230, 28, 206, 26], [230, 30, 206, 28], [230, 32, 206, 30], [231, 8, 207, 6, "matrix"], [231, 14, 207, 12], [231, 17, 207, 15], [231, 18, 207, 16, "matrix"], [231, 24, 207, 22], [231, 25, 207, 23], [231, 26, 207, 24], [231, 27, 207, 25], [231, 29, 207, 27, "matrix"], [231, 35, 207, 33], [231, 36, 207, 34], [231, 37, 207, 35], [231, 38, 207, 36], [231, 40, 207, 38, "matrix"], [231, 46, 207, 44], [231, 47, 207, 45], [231, 48, 207, 46], [231, 49, 207, 47], [231, 51, 207, 49, "matrix"], [231, 57, 207, 55], [231, 58, 207, 56], [231, 59, 207, 57], [231, 60, 207, 58], [231, 62, 207, 60, "matrix"], [231, 68, 207, 66], [231, 69, 207, 67], [231, 70, 207, 68], [231, 71, 207, 69], [231, 73, 207, 71, "matrix"], [231, 79, 207, 77], [231, 80, 207, 78], [231, 81, 207, 79], [231, 82, 207, 80], [231, 84, 207, 82, "matrix"], [231, 90, 207, 88], [231, 91, 207, 89], [231, 93, 207, 91], [231, 94, 207, 92], [231, 96, 207, 94, "matrix"], [231, 102, 207, 100], [231, 103, 207, 101], [231, 105, 207, 103], [231, 106, 207, 104], [231, 108, 207, 106, "matrix"], [231, 114, 207, 112], [231, 115, 207, 113], [231, 117, 207, 115], [231, 118, 207, 116], [231, 119, 207, 117], [232, 6, 208, 4], [232, 7, 208, 5], [232, 13, 208, 11], [232, 17, 208, 15, "matrix"], [232, 23, 208, 21], [232, 24, 208, 22, "length"], [232, 30, 208, 28], [232, 35, 208, 33], [232, 36, 208, 34], [232, 38, 208, 36], [233, 8, 209, 6], [233, 14, 209, 12], [233, 18, 209, 16, "Error"], [233, 23, 209, 21], [233, 24, 209, 22], [233, 50, 209, 48, "matrix"], [233, 56, 209, 54], [233, 57, 209, 55, "length"], [233, 63, 209, 61], [233, 65, 209, 63], [233, 66, 209, 64], [234, 6, 210, 4], [235, 6, 211, 4], [235, 10, 211, 8], [235, 11, 211, 9, "ref"], [235, 14, 211, 12], [235, 15, 211, 13, "transform"], [235, 24, 211, 22], [235, 25, 211, 23, "matrix"], [235, 31, 211, 29], [235, 32, 211, 30], [236, 6, 212, 4], [236, 13, 212, 11], [236, 17, 212, 15], [237, 4, 213, 2], [238, 4, 214, 2, "interpolate"], [238, 15, 214, 13, "interpolate"], [238, 16, 214, 14, "end"], [238, 19, 214, 17], [238, 21, 214, 19, "t"], [238, 22, 214, 20], [238, 24, 214, 22, "output"], [238, 30, 214, 28], [238, 32, 214, 30], [239, 6, 215, 4], [239, 12, 215, 10, "path"], [239, 16, 215, 14], [239, 19, 215, 17], [239, 23, 215, 21], [239, 24, 215, 22, "CanvasKit"], [239, 33, 215, 31], [239, 34, 215, 32, "Path"], [239, 38, 215, 36], [239, 39, 215, 37, "MakeFromPathInterpolation"], [239, 64, 215, 62], [239, 65, 215, 63], [239, 69, 215, 67], [239, 70, 215, 68, "ref"], [239, 73, 215, 71], [239, 75, 215, 73, "JsiSkPath"], [239, 84, 215, 82], [239, 85, 215, 83, "fromValue"], [239, 94, 215, 92], [239, 95, 215, 93, "end"], [239, 98, 215, 96], [239, 99, 215, 97], [239, 101, 215, 99, "t"], [239, 102, 215, 100], [239, 103, 215, 101], [240, 6, 216, 4], [240, 10, 216, 8, "path"], [240, 14, 216, 12], [240, 19, 216, 17], [240, 23, 216, 21], [240, 25, 216, 23], [241, 8, 217, 6], [241, 15, 217, 13], [241, 19, 217, 17], [242, 6, 218, 4], [243, 6, 219, 4], [243, 10, 219, 8, "output"], [243, 16, 219, 14], [243, 18, 219, 16], [244, 8, 220, 6, "output"], [244, 14, 220, 12], [244, 15, 220, 13, "ref"], [244, 18, 220, 16], [244, 21, 220, 19, "path"], [244, 25, 220, 23], [245, 8, 221, 6], [245, 15, 221, 13, "output"], [245, 21, 221, 19], [246, 6, 222, 4], [246, 7, 222, 5], [246, 13, 222, 11], [247, 8, 223, 6], [247, 15, 223, 13], [247, 19, 223, 17, "JsiSkPath"], [247, 28, 223, 26], [247, 29, 223, 27], [247, 33, 223, 31], [247, 34, 223, 32, "CanvasKit"], [247, 43, 223, 41], [247, 45, 223, 43, "path"], [247, 49, 223, 47], [247, 50, 223, 48], [248, 6, 224, 4], [249, 4, 225, 2], [250, 4, 226, 2, "isInterpolatable"], [250, 20, 226, 18, "isInterpolatable"], [250, 21, 226, 19, "path2"], [250, 26, 226, 24], [250, 28, 226, 26], [251, 6, 227, 4], [251, 13, 227, 11], [251, 17, 227, 15], [251, 18, 227, 16, "CanvasKit"], [251, 27, 227, 25], [251, 28, 227, 26, "Path"], [251, 32, 227, 30], [251, 33, 227, 31, "CanInterpolate"], [251, 47, 227, 45], [251, 48, 227, 46], [251, 52, 227, 50], [251, 53, 227, 51, "ref"], [251, 56, 227, 54], [251, 58, 227, 56, "JsiSkPath"], [251, 67, 227, 65], [251, 68, 227, 66, "fromValue"], [251, 77, 227, 75], [251, 78, 227, 76, "path2"], [251, 83, 227, 81], [251, 84, 227, 82], [251, 85, 227, 83], [252, 4, 228, 2], [253, 4, 229, 2, "toCmds"], [253, 10, 229, 8, "toCmds"], [253, 11, 229, 8], [253, 13, 229, 11], [254, 6, 230, 4], [254, 12, 230, 10, "cmds"], [254, 16, 230, 14], [254, 19, 230, 17], [254, 23, 230, 21], [254, 24, 230, 22, "ref"], [254, 27, 230, 25], [254, 28, 230, 26, "toCmds"], [254, 34, 230, 32], [254, 35, 230, 33], [254, 36, 230, 34], [255, 6, 231, 4], [255, 12, 231, 10, "result"], [255, 18, 231, 16], [255, 21, 231, 19, "cmds"], [255, 25, 231, 23], [255, 26, 231, 24, "reduce"], [255, 32, 231, 30], [255, 33, 231, 31], [255, 34, 231, 32, "acc"], [255, 37, 231, 35], [255, 39, 231, 37, "cmd"], [255, 42, 231, 40], [255, 44, 231, 42, "i"], [255, 45, 231, 43], [255, 50, 231, 48], [256, 8, 232, 6], [256, 12, 232, 10, "i"], [256, 13, 232, 11], [256, 18, 232, 16], [256, 19, 232, 17], [256, 21, 232, 19], [257, 10, 233, 8, "acc"], [257, 13, 233, 11], [257, 14, 233, 12, "push"], [257, 18, 233, 16], [257, 19, 233, 17], [257, 21, 233, 19], [257, 22, 233, 20], [258, 8, 234, 6], [259, 8, 235, 6], [259, 14, 235, 12, "current"], [259, 21, 235, 19], [259, 24, 235, 22, "acc"], [259, 27, 235, 25], [259, 28, 235, 26, "acc"], [259, 31, 235, 29], [259, 32, 235, 30, "length"], [259, 38, 235, 36], [259, 41, 235, 39], [259, 42, 235, 40], [259, 43, 235, 41], [260, 8, 236, 6], [260, 12, 236, 10, "current"], [260, 19, 236, 17], [260, 20, 236, 18, "length"], [260, 26, 236, 24], [260, 31, 236, 29], [260, 32, 236, 30], [260, 34, 236, 32], [261, 10, 237, 8, "current"], [261, 17, 237, 15], [261, 18, 237, 16, "push"], [261, 22, 237, 20], [261, 23, 237, 21, "cmd"], [261, 26, 237, 24], [261, 27, 237, 25], [262, 10, 238, 8], [262, 16, 238, 14, "length"], [262, 22, 238, 20], [262, 25, 238, 23, "CommandCount"], [262, 37, 238, 35], [262, 38, 238, 36, "current"], [262, 45, 238, 43], [262, 46, 238, 44], [262, 47, 238, 45], [262, 48, 238, 46], [262, 49, 238, 47], [263, 10, 239, 8], [263, 14, 239, 12, "current"], [263, 21, 239, 19], [263, 22, 239, 20, "length"], [263, 28, 239, 26], [263, 33, 239, 31, "length"], [263, 39, 239, 37], [263, 43, 239, 41, "i"], [263, 44, 239, 42], [263, 49, 239, 47, "cmds"], [263, 53, 239, 51], [263, 54, 239, 52, "length"], [263, 60, 239, 58], [263, 63, 239, 61], [263, 64, 239, 62], [263, 66, 239, 64], [264, 12, 240, 10, "acc"], [264, 15, 240, 13], [264, 16, 240, 14, "push"], [264, 20, 240, 18], [264, 21, 240, 19], [264, 23, 240, 21], [264, 24, 240, 22], [265, 10, 241, 8], [266, 8, 242, 6], [266, 9, 242, 7], [266, 15, 242, 13], [267, 10, 243, 8], [267, 16, 243, 14, "length"], [267, 22, 243, 20], [267, 25, 243, 23, "CommandCount"], [267, 37, 243, 35], [267, 38, 243, 36, "current"], [267, 45, 243, 43], [267, 46, 243, 44], [267, 47, 243, 45], [267, 48, 243, 46], [267, 49, 243, 47], [268, 10, 244, 8], [268, 14, 244, 12, "current"], [268, 21, 244, 19], [268, 22, 244, 20, "length"], [268, 28, 244, 26], [268, 31, 244, 29, "length"], [268, 37, 244, 35], [268, 39, 244, 37], [269, 12, 245, 10, "current"], [269, 19, 245, 17], [269, 20, 245, 18, "push"], [269, 24, 245, 22], [269, 25, 245, 23, "cmd"], [269, 28, 245, 26], [269, 29, 245, 27], [270, 10, 246, 8], [271, 10, 247, 8], [271, 14, 247, 12, "current"], [271, 21, 247, 19], [271, 22, 247, 20, "length"], [271, 28, 247, 26], [271, 33, 247, 31, "length"], [271, 39, 247, 37], [271, 43, 247, 41, "i"], [271, 44, 247, 42], [271, 49, 247, 47, "cmds"], [271, 53, 247, 51], [271, 54, 247, 52, "length"], [271, 60, 247, 58], [271, 63, 247, 61], [271, 64, 247, 62], [271, 66, 247, 64], [272, 12, 248, 10, "acc"], [272, 15, 248, 13], [272, 16, 248, 14, "push"], [272, 20, 248, 18], [272, 21, 248, 19], [272, 23, 248, 21], [272, 24, 248, 22], [273, 10, 249, 8], [274, 8, 250, 6], [275, 8, 251, 6], [275, 15, 251, 13, "acc"], [275, 18, 251, 16], [276, 6, 252, 4], [276, 7, 252, 5], [276, 9, 252, 7], [276, 11, 252, 9], [276, 12, 252, 10], [277, 6, 253, 4], [277, 13, 253, 11, "result"], [277, 19, 253, 17], [278, 4, 254, 2], [279, 2, 255, 0], [280, 2, 255, 1, "exports"], [280, 9, 255, 1], [280, 10, 255, 1, "JsiSkPath"], [280, 19, 255, 1], [280, 22, 255, 1, "JsiSkPath"], [280, 31, 255, 1], [281, 0, 255, 1], [281, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "pinT", "JsiSkPath", "constructor", "_defineProperty$argument_2", "addPath", "addArc", "addOval", "countPoints", "addPoly", "points.map$argument_0", "moveTo", "lineTo", "makeAsWinding", "offset", "rArcTo", "rConicTo", "rCubicTo", "rMoveTo", "rLineTo", "rQuadTo", "setFillType", "setIsVolatile", "stroke", "close", "reset", "rewind", "computeTightBounds", "arcToOval", "arcToRotated", "arcToTangent", "conicTo", "contains", "copy", "cubicTo", "dash", "equals", "getBounds", "getFillType", "quadTo", "addRect", "addRRect", "getPoint", "isEmpty", "isVolatile", "addCircle", "getLastPt", "op", "simplify", "toSVGString", "trim", "transform", "interpolate", "isInterpolatable", "toCmds", "cmds.reduce$argument_0"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;aGe,gCH;OIC;ECC;qCCE;KDE;GDC;EGC;GHI;EIC;GJG;EKC;GLG;EMC;GNE;EOC;gCCC,wCD;GPE;ESC;GTG;EUC;GVG;EWC;GXG;EYC;GZG;EaC;GbG;EcC;GdG;EeC;GfG;EgBC;GhBG;EiBC;GjBG;EkBC;GlBG;EmBC;GnBG;EoBC;GpBG;EqBC;GrBU;EsBC;GtBG;EuBC;GvBG;EwBC;GxBG;EyBC;GzBE;E0BC;G1BG;E2BC;G3BG;E4BC;G5BG;E6BC;G7BG;E8BC;G9BE;E+BC;G/BE;EgCC;GhCG;EiCC;GjCE;EkCC;GlCE;EmCC;GnCE;EoCC;GpCE;EqCC;GrCG;EsCC;GtCG;EuCC;GvCG;EwCC;GxCE;EyCC;GzCE;E0CC;G1CE;E2CC;G3CG;E4CC;G5CE;E6CC;G7CE;E8CC;G9CE;E+CC;G/CE;EgDC;GhDQ;EiDC;GjDS;EkDC;GlDW;EmDC;GnDE;EoDC;+BCE;KDqB;GpDE;CJC"}}, "type": "js/module"}]}