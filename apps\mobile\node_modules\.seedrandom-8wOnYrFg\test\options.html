<html>
<head>
<link rel="stylesheet" href="lib/qunit.css">
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<script src="../seedrandom.min.js"></script>
<script src="lib/qunit.js"></script>
<script>
QUnit.module("Options API Test");

QUnit.test("Verify that we can use documented options", function(assert) {
assert.ok(true, "Seeded random created with new:");

var original = Math.random;

assert.ok(true, "Using Math.seedrandom('hello.')");
var result = Math.seedrandom('hello.');
var firstprng = Math.random;
assert.ok(original !== firstprng, "Should change Math.random.");
assert.equal(result, "hello.", "Should return short seed.");
var r = Math.random();
assert.ok(true, "Got " + r);
assert.equal(r, 0.9282578795792454, "Should be 'hello.'#1");
r = Math.random();
assert.ok(true, "Got " + r);
assert.equal(r, 0.3752569768646784, "Should be 'hello.'#2");

assert.ok(true, "Using Math.seedrandom()");
result = Math.seedrandom();
var secondprng = Math.random;
assert.ok(original !== secondprng, "Should change Math.random.");
assert.ok(firstprng !== secondprng, "Should change Math.random.");
assert.equal(result.length, 256, "Should return short seed.");
r = Math.random();
assert.ok(true, "Got " + r);
assert.ok(r != 0.9282578795792454, "Should not be 'hello.'#1");
assert.ok(r != 0.7316977468919549, "Should not be 'hello.'#3");
var autoseed1 = r;

assert.ok(true, "Using Math.seedrandom('added entropy.', { entropy:true })");
result = Math.seedrandom('added entropy.', { entropy:true });
assert.equal(result.length, 256, "Should return short seed.");
var thirdprng = Math.random;
assert.ok(thirdprng !== secondprng, "Should change Math.random.");
r = Math.random();
assert.ok(true, "Got " + r);
assert.ok(r != 0.597067214994467, "Should not be 'added entropy.'#1");

// Reset to original Math.random.
Math.random = original;
assert.ok(true, "Using new Math.seedrandom('hello.')");
var myrng = new Math.seedrandom('hello.');
assert.ok(original === Math.random, "Should not change Math.random.");
assert.ok(original !== myrng, "PRNG should not be Math.random.");
r = myrng();
assert.ok(true, "Got " + r);
assert.equal(r, 0.9282578795792454, "Should be 'hello.'#1");

// Use "quick" to get only 32 bits of randomness in a float.
assert.equal(myrng.quick(), 0.3752569768112153, "Should be quick #1.1");

// Use "int32" to get a 32 bit (signed) integer
assert.equal(myrng.int32(), 986220731, "Should be int32 #1.2");

// As if brought in by node.js
var seedrandom = Math.seedrandom;

assert.ok(true, "Using seedrandom('hello.')");
var rng = seedrandom('hello.');
assert.equal(typeof(rng), 'function', "Should return a function.");
r = rng();
assert.ok(true, "Got " + r);
assert.equal(r, 0.9282578795792454, "Should be 'hello.'#1");
assert.ok(original === Math.random, "Should not change Math.random.");
assert.ok(original !== rng, "PRNG should not be Math.random.");

// Global PRNG: set Math.random.
assert.ok(true, "Using seedrandom('hello.', { global: true })");
result = seedrandom('hello.', { global: true });
assert.equal(result, 'hello.', "Should return short seed.");
assert.ok(original != Math.random, "Should change Math.random.");
r = Math.random();
assert.ok(true, "Got " + r);
assert.equal(r, 0.9282578795792454, "Should be 'hello.'#1");

// Autoseeded non-global
Math.random = original;
assert.ok(true, "Using seedrandom()");
result = seedrandom();
assert.equal(typeof(result), 'function', "Should return function.");
assert.ok(original === Math.random, "Should not change Math.random.");
r = result();
assert.ok(true, "Got " + r);
assert.ok(r != autoseed1, "Should not repeat previous autoseed.");
assert.ok(r != 0.9282578795792454, "Should not be 'hello.'#1");
assert.ok(r != 0.7316977468919549, "Should not be 'hello.'#3");

// Mixing accumulated entropy.
assert.ok(true, "Using seedrandom('added entropy.', { entropy: true })");
rng = seedrandom('added entropy.', { entropy: true });
r = result();
assert.ok(true, "Got " + r);
assert.ok(r != autoseed1, "Should not repeat previous autoseed.");
assert.ok(r != 0.597067214994467, "Should not be 'added entropy.'#1");

// Legacy calling convention for mixing accumulated entropy.
assert.ok(true, "Using seedrandom('added entropy.', true)");
rng = seedrandom('added entropy.', true);
r = result();
assert.ok(true, "Got " + r);
assert.ok(r != autoseed1, "Should not repeat previous autoseed.");
assert.ok(r != 0.597067214994467, "Should not be 'added entropy.'#1");

// The pass option
assert.ok(true, "Using Math.seedrandom(null, { pass: ...");
var obj = Math.seedrandom(null, { pass: function(prng, seed) {
  return { random: prng, seed: seed };
}});
assert.ok(original === Math.random, "Should not change Math.random.");
assert.ok(original !== obj.random, "Should be different from Math.random.");
assert.equal(typeof(obj.random), 'function', "Should return a PRNG function.");
assert.equal(typeof(obj.seed), 'string', "Should return a seed.");
var as2 = obj.random();
assert.ok(as2 != 0.9282578795792454, "Should not be 'hello.'#1");
rng = seedrandom(obj.seed);
var as3 = rng();
assert.equal(as2, as3, "Should be reproducible when using the seed.");

// Exercise pass again, with explicit seed and global
assert.ok(true, "Using Math.seedrandom('hello.', { pass: ...");
result = Math.seedrandom('hello.', {
  global: 'abc',
  pass: function(prng, seed, global) {
    assert.equal(typeof(prng), 'function', "Callback arg #1 assert.ok");
    assert.equal(seed, 'hello.', "Callback arg #2 assert.ok");
    assert.equal(global, 'abc', "Callback arg #3 passed through.");
    assert.equal(prng(), 0.9282578795792454, "Should be 'hello.'#1");
    return 'def';
}});
assert.equal(result, 'def', "Should return value from callback.");
assert.ok(original === Math.random, "Should not change Math.random.");

// Legacy third argument callback argument:
assert.ok(true, "Using Math.seedrandom('hello.', { global: 50 }, callback)");
result = Math.seedrandom('hello.', { global: 50 },
  function(prng, seed, global) {
    assert.equal(typeof(prng), 'function', "Callback arg #1 assert.ok");
    assert.equal(seed, 'hello.', "Callback arg #2 assert.ok");
    assert.equal(global, 50, "Callback arg #3 assert.ok");
    assert.equal(prng(), 0.9282578795792454, "Should be 'hello.'#1");
    return 'zzz';
});
assert.equal(result, 'zzz', "Should return value from callback.");
assert.ok(original === Math.random, "Should not change Math.random.");

// Global: false.
assert.ok(true, "Using new Math.seedrandom('hello.', {global: false})");
myrng = new Math.seedrandom('hello.', {global:false});
assert.equal(typeof(myrng), 'function', "Should return a PRNG funciton.");
assert.ok(original === Math.random, "Should not change Math.random.");
assert.ok(original !== myrng, "PRNG should not be Math.random.");
r = myrng();
assert.ok(true, "Got " + r);
assert.equal(r, 0.9282578795792454, "Should be 'hello.'#1");

// options = {} when a method of Math.
assert.ok(true, "Using Math.seedrandom('hello.', {})");
var result = Math.seedrandom('hello.');
var xprng = Math.random;
assert.ok(original !== xprng, "Should change Math.random.");
assert.equal(result, "hello.", "Should return short seed.");
var r = Math.random();
assert.ok(true, "Got " + r);
assert.equal(r, 0.9282578795792454, "Should be 'hello.'#1");
r = Math.random();
assert.ok(true, "Got " + r);
assert.equal(r, 0.3752569768646784, "Should be 'hello.'#2");
Math.random = original;

// options = {} when not a method of Math
assert.ok(true, "Using seedrandom('hello.', {})");
rng = seedrandom('hello.', {});
assert.equal(typeof(rng), 'function', "Should return a function.");
r = rng();
assert.ok(true, "Got " + r);
assert.equal(r, 0.9282578795792454, "Should be 'hello.'#1");
assert.ok(original === Math.random, "Should not change Math.random.");
assert.ok(original !== rng, "PRNG should not be Math.random.");

});
</script>
</body>
</html>
