{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.saturate = exports.mix = exports.clamp = void 0;\n  const _worklet_11991166614678_init_data = {\n    code: \"function MathJs1(value,x,y){return x*(1-value)+y*value;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\renderer\\\\processors\\\\math\\\\Math.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"MathJs1\\\",\\\"value\\\",\\\"x\\\",\\\"y\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/renderer/processors/math/Math.js\\\"],\\\"mappings\\\":\\\"AAMmB,QAAC,CAAAA,OAAKA,CAAAC,KAAM,CAAKC,CAAA,CAAAC,CAAA,EAGlC,MAAO,CAAAD,CAAC,EAAI,CAAC,CAAGD,KAAK,CAAC,CAAGE,CAAC,CAAGF,KAAK,CACpC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  /**\n   * Linear interpolation\n   * @param value\n   * @param x\n   * @param y\n   */\n  const mix = exports.mix = function () {\n    const _e = [new global.Error(), 1, -27];\n    const MathJs1 = function (value, x, y) {\n      return x * (1 - value) + y * value;\n    };\n    MathJs1.__closure = {};\n    MathJs1.__workletHash = 11991166614678;\n    MathJs1.__initData = _worklet_11991166614678_init_data;\n    MathJs1.__stackDetails = _e;\n    return MathJs1;\n  }();\n\n  /**\n   *  @summary Clamps a node with a lower and upper bound.\n   *  @example\n      clamp(-1, 0, 100); // 0\n      clamp(1, 0, 100); // 1\n      clamp(101, 0, 100); // 100\n    */\n  const _worklet_8720705025366_init_data = {\n    code: \"function MathJs2(value,lowerBound,upperBound){return Math.min(Math.max(lowerBound,value),upperBound);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\renderer\\\\processors\\\\math\\\\Math.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"MathJs2\\\",\\\"value\\\",\\\"lowerBound\\\",\\\"upperBound\\\",\\\"Math\\\",\\\"min\\\",\\\"max\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/renderer/processors/math/Math.js\\\"],\\\"mappings\\\":\\\"AAmBqB,QAAC,CAAAA,OAAKA,CAAAC,KAAE,CAAAC,UAAY,CAAAC,UAAe,EAGtD,MAAO,CAAAC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,UAAU,CAAED,KAAK,CAAC,CAAEE,UAAU,CAAC,CAC1D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const clamp = exports.clamp = function () {\n    const _e = [new global.Error(), 1, -27];\n    const MathJs2 = function (value, lowerBound, upperBound) {\n      return Math.min(Math.max(lowerBound, value), upperBound);\n    };\n    MathJs2.__closure = {};\n    MathJs2.__workletHash = 8720705025366;\n    MathJs2.__initData = _worklet_8720705025366_init_data;\n    MathJs2.__stackDetails = _e;\n    return MathJs2;\n  }();\n  const _worklet_3940900787221_init_data = {\n    code: \"function MathJs3(value){const{clamp}=this.__closure;return clamp(value,0,1);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\renderer\\\\processors\\\\math\\\\Math.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"MathJs3\\\",\\\"value\\\",\\\"clamp\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/renderer/processors/math/Math.js\\\"],\\\"mappings\\\":\\\"AAwBwB,SAAAA,OAAKA,CAAAC,KAAI,QAAAC,KAAA,OAAAC,SAAA,CAG/B,MAAO,CAAAD,KAAK,CAACD,KAAK,CAAE,CAAC,CAAE,CAAC,CAAC,CAC3B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const saturate = exports.saturate = function () {\n    const _e = [new global.Error(), -2, -27];\n    const MathJs3 = function (value) {\n      return clamp(value, 0, 1);\n    };\n    MathJs3.__closure = {\n      clamp\n    };\n    MathJs3.__workletHash = 3940900787221;\n    MathJs3.__initData = _worklet_3940900787221_init_data;\n    MathJs3.__stackDetails = _e;\n    return MathJs3;\n  }();\n});", "lineCount": 73, "map": [[12, 2, 1, 0], [13, 0, 2, 0], [14, 0, 3, 0], [15, 0, 4, 0], [16, 0, 5, 0], [17, 0, 6, 0], [18, 2, 7, 7], [18, 8, 7, 13, "mix"], [18, 11, 7, 16], [18, 14, 7, 16, "exports"], [18, 21, 7, 16], [18, 22, 7, 16, "mix"], [18, 25, 7, 16], [18, 28, 7, 19], [19, 4, 7, 19], [19, 10, 7, 19, "_e"], [19, 12, 7, 19], [19, 20, 7, 19, "global"], [19, 26, 7, 19], [19, 27, 7, 19, "Error"], [19, 32, 7, 19], [20, 4, 7, 19], [20, 10, 7, 19, "MathJs1"], [20, 17, 7, 19], [20, 29, 7, 19, "MathJs1"], [20, 30, 7, 20, "value"], [20, 35, 7, 25], [20, 37, 7, 27, "x"], [20, 38, 7, 28], [20, 40, 7, 30, "y"], [20, 41, 7, 31], [20, 43, 7, 36], [21, 6, 10, 2], [21, 13, 10, 9, "x"], [21, 14, 10, 10], [21, 18, 10, 14], [21, 19, 10, 15], [21, 22, 10, 18, "value"], [21, 27, 10, 23], [21, 28, 10, 24], [21, 31, 10, 27, "y"], [21, 32, 10, 28], [21, 35, 10, 31, "value"], [21, 40, 10, 36], [22, 4, 11, 0], [22, 5, 11, 1], [23, 4, 11, 1, "MathJs1"], [23, 11, 11, 1], [23, 12, 11, 1, "__closure"], [23, 21, 11, 1], [24, 4, 11, 1, "MathJs1"], [24, 11, 11, 1], [24, 12, 11, 1, "__workletHash"], [24, 25, 11, 1], [25, 4, 11, 1, "MathJs1"], [25, 11, 11, 1], [25, 12, 11, 1, "__initData"], [25, 22, 11, 1], [25, 25, 11, 1, "_worklet_11991166614678_init_data"], [25, 58, 11, 1], [26, 4, 11, 1, "MathJs1"], [26, 11, 11, 1], [26, 12, 11, 1, "__stackDetails"], [26, 26, 11, 1], [26, 29, 11, 1, "_e"], [26, 31, 11, 1], [27, 4, 11, 1], [27, 11, 11, 1, "MathJs1"], [27, 18, 11, 1], [28, 2, 11, 1], [28, 3, 7, 19], [28, 5, 11, 1], [30, 2, 13, 0], [31, 0, 14, 0], [32, 0, 15, 0], [33, 0, 16, 0], [34, 0, 17, 0], [35, 0, 18, 0], [36, 0, 19, 0], [37, 2, 13, 0], [37, 8, 13, 0, "_worklet_8720705025366_init_data"], [37, 40, 13, 0], [38, 4, 13, 0, "code"], [38, 8, 13, 0], [39, 4, 13, 0, "location"], [39, 12, 13, 0], [40, 4, 13, 0, "sourceMap"], [40, 13, 13, 0], [41, 4, 13, 0, "version"], [41, 11, 13, 0], [42, 2, 13, 0], [43, 2, 20, 7], [43, 8, 20, 13, "clamp"], [43, 13, 20, 18], [43, 16, 20, 18, "exports"], [43, 23, 20, 18], [43, 24, 20, 18, "clamp"], [43, 29, 20, 18], [43, 32, 20, 21], [44, 4, 20, 21], [44, 10, 20, 21, "_e"], [44, 12, 20, 21], [44, 20, 20, 21, "global"], [44, 26, 20, 21], [44, 27, 20, 21, "Error"], [44, 32, 20, 21], [45, 4, 20, 21], [45, 10, 20, 21, "MathJs2"], [45, 17, 20, 21], [45, 29, 20, 21, "MathJs2"], [45, 30, 20, 22, "value"], [45, 35, 20, 27], [45, 37, 20, 29, "lowerBound"], [45, 47, 20, 39], [45, 49, 20, 41, "upperBound"], [45, 59, 20, 51], [45, 61, 20, 56], [46, 6, 23, 2], [46, 13, 23, 9, "Math"], [46, 17, 23, 13], [46, 18, 23, 14, "min"], [46, 21, 23, 17], [46, 22, 23, 18, "Math"], [46, 26, 23, 22], [46, 27, 23, 23, "max"], [46, 30, 23, 26], [46, 31, 23, 27, "lowerBound"], [46, 41, 23, 37], [46, 43, 23, 39, "value"], [46, 48, 23, 44], [46, 49, 23, 45], [46, 51, 23, 47, "upperBound"], [46, 61, 23, 57], [46, 62, 23, 58], [47, 4, 24, 0], [47, 5, 24, 1], [48, 4, 24, 1, "MathJs2"], [48, 11, 24, 1], [48, 12, 24, 1, "__closure"], [48, 21, 24, 1], [49, 4, 24, 1, "MathJs2"], [49, 11, 24, 1], [49, 12, 24, 1, "__workletHash"], [49, 25, 24, 1], [50, 4, 24, 1, "MathJs2"], [50, 11, 24, 1], [50, 12, 24, 1, "__initData"], [50, 22, 24, 1], [50, 25, 24, 1, "_worklet_8720705025366_init_data"], [50, 57, 24, 1], [51, 4, 24, 1, "MathJs2"], [51, 11, 24, 1], [51, 12, 24, 1, "__stackDetails"], [51, 26, 24, 1], [51, 29, 24, 1, "_e"], [51, 31, 24, 1], [52, 4, 24, 1], [52, 11, 24, 1, "MathJs2"], [52, 18, 24, 1], [53, 2, 24, 1], [53, 3, 20, 21], [53, 5, 24, 1], [54, 2, 24, 2], [54, 8, 24, 2, "_worklet_3940900787221_init_data"], [54, 40, 24, 2], [55, 4, 24, 2, "code"], [55, 8, 24, 2], [56, 4, 24, 2, "location"], [56, 12, 24, 2], [57, 4, 24, 2, "sourceMap"], [57, 13, 24, 2], [58, 4, 24, 2, "version"], [58, 11, 24, 2], [59, 2, 24, 2], [60, 2, 25, 7], [60, 8, 25, 13, "saturate"], [60, 16, 25, 21], [60, 19, 25, 21, "exports"], [60, 26, 25, 21], [60, 27, 25, 21, "saturate"], [60, 35, 25, 21], [60, 38, 25, 24], [61, 4, 25, 24], [61, 10, 25, 24, "_e"], [61, 12, 25, 24], [61, 20, 25, 24, "global"], [61, 26, 25, 24], [61, 27, 25, 24, "Error"], [61, 32, 25, 24], [62, 4, 25, 24], [62, 10, 25, 24, "MathJs3"], [62, 17, 25, 24], [62, 29, 25, 24, "MathJs3"], [62, 30, 25, 24, "value"], [62, 35, 25, 29], [62, 37, 25, 33], [63, 6, 28, 2], [63, 13, 28, 9, "clamp"], [63, 18, 28, 14], [63, 19, 28, 15, "value"], [63, 24, 28, 20], [63, 26, 28, 22], [63, 27, 28, 23], [63, 29, 28, 25], [63, 30, 28, 26], [63, 31, 28, 27], [64, 4, 29, 0], [64, 5, 29, 1], [65, 4, 29, 1, "MathJs3"], [65, 11, 29, 1], [65, 12, 29, 1, "__closure"], [65, 21, 29, 1], [66, 6, 29, 1, "clamp"], [67, 4, 29, 1], [68, 4, 29, 1, "MathJs3"], [68, 11, 29, 1], [68, 12, 29, 1, "__workletHash"], [68, 25, 29, 1], [69, 4, 29, 1, "MathJs3"], [69, 11, 29, 1], [69, 12, 29, 1, "__initData"], [69, 22, 29, 1], [69, 25, 29, 1, "_worklet_3940900787221_init_data"], [69, 57, 29, 1], [70, 4, 29, 1, "MathJs3"], [70, 11, 29, 1], [70, 12, 29, 1, "__stackDetails"], [70, 26, 29, 1], [70, 29, 29, 1, "_e"], [70, 31, 29, 1], [71, 4, 29, 1], [71, 11, 29, 1, "MathJs3"], [71, 18, 29, 1], [72, 2, 29, 1], [72, 3, 25, 24], [72, 5, 29, 1], [73, 0, 29, 2], [73, 3]], "functionMap": {"names": ["<global>", "mix", "clamp", "saturate"], "mappings": "AAA;mBCM;CDI;qBES;CFI;wBGC;CHI"}}, "type": "js/module"}]}