{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   * \n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation is based on the working solution from echo-camera-fixed.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        return;\n      }\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size\n      canvas.width = width;\n      canvas.height = height;\n      console.log('[BlazeFaceCanvas] Canvas resized to:', width, 'x', height);\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Load TensorFlow.js and BlazeFace model\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await new Promise((resolve, reject) => {\n              const script = document.createElement('script');\n              script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js';\n              script.onload = () => resolve();\n              script.onerror = () => reject(new Error('Failed to load TensorFlow.js'));\n              document.head.appendChild(script);\n            });\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await new Promise((resolve, reject) => {\n              const script = document.createElement('script');\n              script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n              script.onload = () => resolve();\n              script.onerror = () => reject(new Error('Failed to load BlazeFace'));\n              document.head.appendChild(script);\n            });\n          }\n\n          // Initialize BlazeFace model\n          const tf = window.tf;\n          const blazeface = window.blazeface;\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            setFaceCount(predictions.length);\n\n            // Process each detected face\n            predictions.forEach((prediction, index) => {\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order - BlazeFace might return them in different order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect) - flip X coordinates\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                console.warn(`Invalid face dimensions for face ${index + 1}`);\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply elliptical blur\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(20px)';\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Start loading the model\n      loadModel();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width,\n          height,\n          pointerEvents: 'none',\n          zIndex: 10\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(0,0,0,0.7)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '5px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: \"Loading face detection...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(0,128,0,0.8)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '5px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 247, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 9, 0], [12, 0, 10, 0], [13, 0, 11, 0], [14, 0, 12, 0], [15, 0, 13, 0], [16, 0, 14, 0], [17, 0, 15, 0], [18, 2, 16, 15], [18, 11, 16, 24, "BlazeFaceCanvas"], [18, 26, 16, 39, "BlazeFaceCanvas"], [18, 27, 16, 40], [19, 4, 16, 42, "containerId"], [19, 15, 16, 53], [20, 4, 16, 55, "width"], [20, 9, 16, 60], [21, 4, 16, 62, "height"], [22, 2, 16, 91], [22, 3, 16, 92], [22, 5, 16, 94], [23, 4, 16, 94, "_s"], [23, 6, 16, 94], [24, 4, 17, 2], [24, 10, 17, 8, "canvasRef"], [24, 19, 17, 17], [24, 22, 17, 20], [24, 26, 17, 20, "useRef"], [24, 39, 17, 26], [24, 41, 17, 53], [24, 45, 17, 57], [24, 46, 17, 58], [25, 4, 18, 2], [25, 10, 18, 8, "rafRef"], [25, 16, 18, 14], [25, 19, 18, 17], [25, 23, 18, 17, "useRef"], [25, 36, 18, 23], [25, 38, 18, 39], [25, 42, 18, 43], [25, 43, 18, 44], [26, 4, 19, 2], [26, 10, 19, 8, "modelRef"], [26, 18, 19, 16], [26, 21, 19, 19], [26, 25, 19, 19, "useRef"], [26, 38, 19, 25], [26, 40, 19, 38], [26, 44, 19, 42], [26, 45, 19, 43], [27, 4, 20, 2], [27, 10, 20, 8], [27, 11, 20, 9, "isLoading"], [27, 20, 20, 18], [27, 22, 20, 20, "setIsLoading"], [27, 34, 20, 32], [27, 35, 20, 33], [27, 38, 20, 36], [27, 42, 20, 36, "useState"], [27, 57, 20, 44], [27, 59, 20, 45], [27, 63, 20, 49], [27, 64, 20, 50], [28, 4, 21, 2], [28, 10, 21, 8], [28, 11, 21, 9, "faceCount"], [28, 20, 21, 18], [28, 22, 21, 20, "setFaceCount"], [28, 34, 21, 32], [28, 35, 21, 33], [28, 38, 21, 36], [28, 42, 21, 36, "useState"], [28, 57, 21, 44], [28, 59, 21, 45], [28, 60, 21, 46], [28, 61, 21, 47], [29, 4, 23, 2], [29, 8, 23, 2, "useEffect"], [29, 24, 23, 11], [29, 26, 23, 12], [29, 32, 23, 18], [30, 6, 24, 4, "console"], [30, 13, 24, 11], [30, 14, 24, 12, "log"], [30, 17, 24, 15], [30, 18, 24, 16], [30, 64, 24, 62], [30, 66, 24, 64], [31, 8, 24, 66, "containerId"], [31, 19, 24, 77], [32, 8, 24, 79, "width"], [32, 13, 24, 84], [33, 8, 24, 86, "height"], [34, 6, 24, 93], [34, 7, 24, 94], [34, 8, 24, 95], [35, 6, 26, 4], [35, 12, 26, 10, "container"], [35, 21, 26, 19], [35, 24, 26, 22, "document"], [35, 32, 26, 30], [35, 33, 26, 31, "getElementById"], [35, 47, 26, 45], [35, 48, 26, 46, "containerId"], [35, 59, 26, 57], [35, 60, 26, 58], [36, 6, 27, 4], [36, 10, 27, 8], [36, 11, 27, 9, "container"], [36, 20, 27, 18], [36, 22, 27, 20], [37, 8, 28, 6, "console"], [37, 15, 28, 13], [37, 16, 28, 14, "error"], [37, 21, 28, 19], [37, 22, 28, 20], [37, 62, 28, 60], [37, 64, 28, 62, "containerId"], [37, 75, 28, 73], [37, 76, 28, 74], [38, 8, 29, 6], [39, 6, 30, 4], [40, 6, 32, 4], [40, 12, 32, 10, "video"], [40, 17, 32, 40], [40, 20, 32, 43, "container"], [40, 29, 32, 52], [40, 30, 32, 53, "querySelector"], [40, 43, 32, 66], [40, 44, 32, 67], [40, 51, 32, 74], [40, 52, 32, 75], [41, 6, 33, 4], [41, 10, 33, 8], [41, 11, 33, 9, "video"], [41, 16, 33, 14], [41, 18, 33, 16], [42, 8, 34, 6, "console"], [42, 15, 34, 13], [42, 16, 34, 14, "error"], [42, 21, 34, 19], [42, 22, 34, 20], [42, 78, 34, 76], [42, 79, 34, 77], [43, 8, 35, 6], [44, 6, 36, 4], [45, 6, 38, 4], [45, 12, 38, 10, "canvas"], [45, 18, 38, 16], [45, 21, 38, 19, "canvasRef"], [45, 30, 38, 28], [45, 31, 38, 29, "current"], [45, 38, 38, 36], [46, 6, 39, 4], [46, 10, 39, 8], [46, 11, 39, 9, "canvas"], [46, 17, 39, 15], [46, 19, 39, 17], [47, 8, 40, 6, "console"], [47, 15, 40, 13], [47, 16, 40, 14, "error"], [47, 21, 40, 19], [47, 22, 40, 20], [47, 66, 40, 64], [47, 67, 40, 65], [48, 8, 41, 6], [49, 6, 42, 4], [51, 6, 44, 4], [52, 6, 45, 4, "canvas"], [52, 12, 45, 10], [52, 13, 45, 11, "width"], [52, 18, 45, 16], [52, 21, 45, 19, "width"], [52, 26, 45, 24], [53, 6, 46, 4, "canvas"], [53, 12, 46, 10], [53, 13, 46, 11, "height"], [53, 19, 46, 17], [53, 22, 46, 20, "height"], [53, 28, 46, 26], [54, 6, 47, 4, "console"], [54, 13, 47, 11], [54, 14, 47, 12, "log"], [54, 17, 47, 15], [54, 18, 47, 16], [54, 56, 47, 54], [54, 58, 47, 56, "width"], [54, 63, 47, 61], [54, 65, 47, 63], [54, 68, 47, 66], [54, 70, 47, 68, "height"], [54, 76, 47, 74], [54, 77, 47, 75], [55, 6, 49, 4], [55, 12, 49, 10, "ctx"], [55, 15, 49, 13], [55, 18, 49, 16, "canvas"], [55, 24, 49, 22], [55, 25, 49, 23, "getContext"], [55, 35, 49, 33], [55, 36, 49, 34], [55, 40, 49, 38], [55, 41, 49, 39], [56, 6, 50, 4], [56, 10, 50, 8], [56, 11, 50, 9, "ctx"], [56, 14, 50, 12], [56, 16, 50, 14], [57, 8, 51, 6, "console"], [57, 15, 51, 13], [57, 16, 51, 14, "error"], [57, 21, 51, 19], [57, 22, 51, 20], [57, 70, 51, 68], [57, 71, 51, 69], [58, 8, 52, 6], [59, 6, 53, 4], [60, 6, 55, 4], [60, 10, 55, 8, "isDetecting"], [60, 21, 55, 19], [60, 24, 55, 22], [60, 28, 55, 26], [62, 6, 57, 4], [63, 6, 58, 4], [63, 12, 58, 10, "loadModel"], [63, 21, 58, 19], [63, 24, 58, 22], [63, 30, 58, 22, "loadModel"], [63, 31, 58, 22], [63, 36, 58, 34], [64, 8, 59, 6], [64, 12, 59, 10], [65, 10, 60, 8, "console"], [65, 17, 60, 15], [65, 18, 60, 16, "log"], [65, 21, 60, 19], [65, 22, 60, 20], [65, 66, 60, 64], [65, 67, 60, 65], [67, 10, 62, 8], [68, 10, 63, 8], [68, 14, 63, 12], [68, 15, 63, 14, "window"], [68, 21, 63, 20], [68, 22, 63, 29, "tf"], [68, 24, 63, 31], [68, 26, 63, 33], [69, 12, 64, 10], [69, 18, 64, 16], [69, 22, 64, 20, "Promise"], [69, 29, 64, 27], [69, 30, 64, 34], [69, 31, 64, 35, "resolve"], [69, 38, 64, 42], [69, 40, 64, 44, "reject"], [69, 46, 64, 50], [69, 51, 64, 55], [70, 14, 65, 12], [70, 20, 65, 18, "script"], [70, 26, 65, 24], [70, 29, 65, 27, "document"], [70, 37, 65, 35], [70, 38, 65, 36, "createElement"], [70, 51, 65, 49], [70, 52, 65, 50], [70, 60, 65, 58], [70, 61, 65, 59], [71, 14, 66, 12, "script"], [71, 20, 66, 18], [71, 21, 66, 19, "src"], [71, 24, 66, 22], [71, 27, 66, 25], [71, 96, 66, 94], [72, 14, 67, 12, "script"], [72, 20, 67, 18], [72, 21, 67, 19, "onload"], [72, 27, 67, 25], [72, 30, 67, 28], [72, 36, 67, 34, "resolve"], [72, 43, 67, 41], [72, 44, 67, 42], [72, 45, 67, 43], [73, 14, 68, 12, "script"], [73, 20, 68, 18], [73, 21, 68, 19, "onerror"], [73, 28, 68, 26], [73, 31, 68, 29], [73, 37, 68, 35, "reject"], [73, 43, 68, 41], [73, 44, 68, 42], [73, 48, 68, 46, "Error"], [73, 53, 68, 51], [73, 54, 68, 52], [73, 84, 68, 82], [73, 85, 68, 83], [73, 86, 68, 84], [74, 14, 69, 12, "document"], [74, 22, 69, 20], [74, 23, 69, 21, "head"], [74, 27, 69, 25], [74, 28, 69, 26, "append<PERSON><PERSON><PERSON>"], [74, 39, 69, 37], [74, 40, 69, 38, "script"], [74, 46, 69, 44], [74, 47, 69, 45], [75, 12, 70, 10], [75, 13, 70, 11], [75, 14, 70, 12], [76, 10, 71, 8], [77, 10, 73, 8, "console"], [77, 17, 73, 15], [77, 18, 73, 16, "log"], [77, 21, 73, 19], [77, 22, 73, 20], [77, 68, 73, 66], [77, 69, 73, 67], [79, 10, 75, 8], [80, 10, 76, 8], [80, 14, 76, 12], [80, 15, 76, 14, "window"], [80, 21, 76, 20], [80, 22, 76, 29, "blazeface"], [80, 31, 76, 38], [80, 33, 76, 40], [81, 12, 77, 10], [81, 18, 77, 16], [81, 22, 77, 20, "Promise"], [81, 29, 77, 27], [81, 30, 77, 34], [81, 31, 77, 35, "resolve"], [81, 38, 77, 42], [81, 40, 77, 44, "reject"], [81, 46, 77, 50], [81, 51, 77, 55], [82, 14, 78, 12], [82, 20, 78, 18, "script"], [82, 26, 78, 24], [82, 29, 78, 27, "document"], [82, 37, 78, 35], [82, 38, 78, 36, "createElement"], [82, 51, 78, 49], [82, 52, 78, 50], [82, 60, 78, 58], [82, 61, 78, 59], [83, 14, 79, 12, "script"], [83, 20, 79, 18], [83, 21, 79, 19, "src"], [83, 24, 79, 22], [83, 27, 79, 25], [83, 110, 79, 108], [84, 14, 80, 12, "script"], [84, 20, 80, 18], [84, 21, 80, 19, "onload"], [84, 27, 80, 25], [84, 30, 80, 28], [84, 36, 80, 34, "resolve"], [84, 43, 80, 41], [84, 44, 80, 42], [84, 45, 80, 43], [85, 14, 81, 12, "script"], [85, 20, 81, 18], [85, 21, 81, 19, "onerror"], [85, 28, 81, 26], [85, 31, 81, 29], [85, 37, 81, 35, "reject"], [85, 43, 81, 41], [85, 44, 81, 42], [85, 48, 81, 46, "Error"], [85, 53, 81, 51], [85, 54, 81, 52], [85, 80, 81, 78], [85, 81, 81, 79], [85, 82, 81, 80], [86, 14, 82, 12, "document"], [86, 22, 82, 20], [86, 23, 82, 21, "head"], [86, 27, 82, 25], [86, 28, 82, 26, "append<PERSON><PERSON><PERSON>"], [86, 39, 82, 37], [86, 40, 82, 38, "script"], [86, 46, 82, 44], [86, 47, 82, 45], [87, 12, 83, 10], [87, 13, 83, 11], [87, 14, 83, 12], [88, 10, 84, 8], [90, 10, 86, 8], [91, 10, 87, 8], [91, 16, 87, 14, "tf"], [91, 18, 87, 16], [91, 21, 87, 20, "window"], [91, 27, 87, 26], [91, 28, 87, 35, "tf"], [91, 30, 87, 37], [92, 10, 88, 8], [92, 16, 88, 14, "blazeface"], [92, 25, 88, 23], [92, 28, 88, 27, "window"], [92, 34, 88, 33], [92, 35, 88, 42, "blazeface"], [92, 44, 88, 51], [93, 10, 90, 8, "console"], [93, 17, 90, 15], [93, 18, 90, 16, "log"], [93, 21, 90, 19], [93, 22, 90, 20], [93, 73, 90, 71], [93, 74, 90, 72], [94, 10, 91, 8, "modelRef"], [94, 18, 91, 16], [94, 19, 91, 17, "current"], [94, 26, 91, 24], [94, 29, 91, 27], [94, 35, 91, 33, "blazeface"], [94, 44, 91, 42], [94, 45, 91, 43, "load"], [94, 49, 91, 47], [94, 50, 91, 48], [94, 51, 91, 49], [95, 10, 92, 8, "console"], [95, 17, 92, 15], [95, 18, 92, 16, "log"], [95, 21, 92, 19], [95, 22, 92, 20], [95, 79, 92, 77], [95, 80, 92, 78], [96, 10, 94, 8, "setIsLoading"], [96, 22, 94, 20], [96, 23, 94, 21], [96, 28, 94, 26], [96, 29, 94, 27], [98, 10, 96, 8], [99, 10, 97, 8, "detectLoop"], [99, 20, 97, 18], [99, 21, 97, 19], [99, 22, 97, 20], [100, 8, 99, 6], [100, 9, 99, 7], [100, 10, 99, 8], [100, 17, 99, 15, "error"], [100, 22, 99, 20], [100, 24, 99, 22], [101, 10, 100, 8, "console"], [101, 17, 100, 15], [101, 18, 100, 16, "error"], [101, 23, 100, 21], [101, 24, 100, 22], [101, 67, 100, 65], [101, 69, 100, 67, "error"], [101, 74, 100, 72], [101, 75, 100, 73], [102, 10, 101, 8, "setIsLoading"], [102, 22, 101, 20], [102, 23, 101, 21], [102, 28, 101, 26], [102, 29, 101, 27], [103, 8, 102, 6], [104, 6, 103, 4], [104, 7, 103, 5], [105, 6, 105, 4], [105, 12, 105, 10, "detectLoop"], [105, 22, 105, 20], [105, 25, 105, 23], [105, 31, 105, 23, "detectLoop"], [105, 32, 105, 23], [105, 37, 105, 35], [106, 8, 106, 6], [106, 12, 106, 10], [106, 13, 106, 11, "isDetecting"], [106, 24, 106, 22], [106, 28, 106, 26], [106, 29, 106, 27, "modelRef"], [106, 37, 106, 35], [106, 38, 106, 36, "current"], [106, 45, 106, 43], [106, 47, 106, 45], [107, 8, 108, 6], [107, 12, 108, 10], [108, 10, 109, 8], [109, 10, 110, 8], [109, 16, 110, 14, "tf"], [109, 18, 110, 16], [109, 21, 110, 20, "window"], [109, 27, 110, 26], [109, 28, 110, 35, "tf"], [109, 30, 110, 37], [110, 10, 111, 8], [110, 16, 111, 14, "tensor"], [110, 22, 111, 20], [110, 25, 111, 23, "tf"], [110, 27, 111, 25], [110, 28, 111, 26, "browser"], [110, 35, 111, 33], [110, 36, 111, 34, "fromPixels"], [110, 46, 111, 44], [110, 47, 111, 45, "video"], [110, 52, 111, 50], [110, 53, 111, 51], [112, 10, 113, 8], [113, 10, 114, 8], [113, 16, 114, 14, "predictions"], [113, 27, 114, 25], [113, 30, 114, 28], [113, 36, 114, 34, "modelRef"], [113, 44, 114, 42], [113, 45, 114, 43, "current"], [113, 52, 114, 50], [113, 53, 114, 51, "estimateFaces"], [113, 66, 114, 64], [113, 67, 114, 65, "tensor"], [113, 73, 114, 71], [113, 75, 114, 73], [113, 80, 114, 78], [113, 82, 114, 80], [113, 85, 114, 83], [113, 86, 114, 84], [114, 10, 115, 8, "tensor"], [114, 16, 115, 14], [114, 17, 115, 15, "dispose"], [114, 24, 115, 22], [114, 25, 115, 23], [114, 26, 115, 24], [116, 10, 117, 8], [117, 10, 118, 8, "ctx"], [117, 13, 118, 11], [117, 14, 118, 12, "clearRect"], [117, 23, 118, 21], [117, 24, 118, 22], [117, 25, 118, 23], [117, 27, 118, 25], [117, 28, 118, 26], [117, 30, 118, 28, "canvas"], [117, 36, 118, 34], [117, 37, 118, 35, "width"], [117, 42, 118, 40], [117, 44, 118, 42, "canvas"], [117, 50, 118, 48], [117, 51, 118, 49, "height"], [117, 57, 118, 55], [117, 58, 118, 56], [118, 10, 120, 8], [118, 14, 120, 12, "predictions"], [118, 25, 120, 23], [118, 26, 120, 24, "length"], [118, 32, 120, 30], [118, 35, 120, 33], [118, 36, 120, 34], [118, 38, 120, 36], [119, 12, 121, 10, "setFaceCount"], [119, 24, 121, 22], [119, 25, 121, 23, "predictions"], [119, 36, 121, 34], [119, 37, 121, 35, "length"], [119, 43, 121, 41], [119, 44, 121, 42], [121, 12, 123, 10], [122, 12, 124, 10, "predictions"], [122, 23, 124, 21], [122, 24, 124, 22, "for<PERSON>ach"], [122, 31, 124, 29], [122, 32, 124, 30], [122, 33, 124, 31, "prediction"], [122, 43, 124, 46], [122, 45, 124, 48, "index"], [122, 50, 124, 61], [122, 55, 124, 66], [123, 14, 125, 12], [123, 20, 125, 18], [123, 21, 125, 19, "x1"], [123, 23, 125, 21], [123, 25, 125, 23, "y1"], [123, 27, 125, 25], [123, 28, 125, 26], [123, 31, 125, 29, "prediction"], [123, 41, 125, 39], [123, 42, 125, 40, "topLeft"], [123, 49, 125, 47], [124, 14, 126, 12], [124, 20, 126, 18], [124, 21, 126, 19, "x2"], [124, 23, 126, 21], [124, 25, 126, 23, "y2"], [124, 27, 126, 25], [124, 28, 126, 26], [124, 31, 126, 29, "prediction"], [124, 41, 126, 39], [124, 42, 126, 40, "bottomRight"], [124, 53, 126, 51], [126, 14, 128, 12], [127, 14, 129, 12], [127, 18, 129, 16, "minX"], [127, 22, 129, 20], [127, 25, 129, 23, "Math"], [127, 29, 129, 27], [127, 30, 129, 28, "min"], [127, 33, 129, 31], [127, 34, 129, 32, "x1"], [127, 36, 129, 34], [127, 38, 129, 36, "x2"], [127, 40, 129, 38], [127, 41, 129, 39], [128, 14, 130, 12], [128, 18, 130, 16, "maxX"], [128, 22, 130, 20], [128, 25, 130, 23, "Math"], [128, 29, 130, 27], [128, 30, 130, 28, "max"], [128, 33, 130, 31], [128, 34, 130, 32, "x1"], [128, 36, 130, 34], [128, 38, 130, 36, "x2"], [128, 40, 130, 38], [128, 41, 130, 39], [129, 14, 131, 12], [129, 20, 131, 18, "minY"], [129, 24, 131, 22], [129, 27, 131, 25, "Math"], [129, 31, 131, 29], [129, 32, 131, 30, "min"], [129, 35, 131, 33], [129, 36, 131, 34, "y1"], [129, 38, 131, 36], [129, 40, 131, 38, "y2"], [129, 42, 131, 40], [129, 43, 131, 41], [130, 14, 132, 12], [130, 20, 132, 18, "maxY"], [130, 24, 132, 22], [130, 27, 132, 25, "Math"], [130, 31, 132, 29], [130, 32, 132, 30, "max"], [130, 35, 132, 33], [130, 36, 132, 34, "y1"], [130, 38, 132, 36], [130, 40, 132, 38, "y2"], [130, 42, 132, 40], [130, 43, 132, 41], [132, 14, 134, 12], [133, 14, 135, 12], [133, 20, 135, 18, "canvasWidth"], [133, 31, 135, 29], [133, 34, 135, 32, "canvas"], [133, 40, 135, 38], [133, 41, 135, 39, "width"], [133, 46, 135, 44], [134, 14, 136, 12], [134, 20, 136, 18, "flippedMinX"], [134, 31, 136, 29], [134, 34, 136, 32, "canvasWidth"], [134, 45, 136, 43], [134, 48, 136, 46, "maxX"], [134, 52, 136, 50], [135, 14, 137, 12], [135, 20, 137, 18, "flippedMaxX"], [135, 31, 137, 29], [135, 34, 137, 32, "canvasWidth"], [135, 45, 137, 43], [135, 48, 137, 46, "minX"], [135, 52, 137, 50], [136, 14, 138, 12, "minX"], [136, 18, 138, 16], [136, 21, 138, 19, "flippedMinX"], [136, 32, 138, 30], [137, 14, 139, 12, "maxX"], [137, 18, 139, 16], [137, 21, 139, 19, "flippedMaxX"], [137, 32, 139, 30], [139, 14, 141, 12], [140, 14, 142, 12], [140, 20, 142, 18, "faceWidth"], [140, 29, 142, 27], [140, 32, 142, 30, "maxX"], [140, 36, 142, 34], [140, 39, 142, 37, "minX"], [140, 43, 142, 41], [141, 14, 143, 12], [141, 20, 143, 18, "faceHeight"], [141, 30, 143, 28], [141, 33, 143, 31, "maxY"], [141, 37, 143, 35], [141, 40, 143, 38, "minY"], [141, 44, 143, 42], [142, 14, 145, 12], [142, 18, 145, 16, "faceWidth"], [142, 27, 145, 25], [142, 31, 145, 29], [142, 32, 145, 30], [142, 36, 145, 34, "faceHeight"], [142, 46, 145, 44], [142, 50, 145, 48], [142, 51, 145, 49], [142, 53, 145, 51], [143, 16, 146, 14, "console"], [143, 23, 146, 21], [143, 24, 146, 22, "warn"], [143, 28, 146, 26], [143, 29, 146, 27], [143, 65, 146, 63, "index"], [143, 70, 146, 68], [143, 73, 146, 71], [143, 74, 146, 72], [143, 76, 146, 74], [143, 77, 146, 75], [144, 16, 147, 14], [145, 14, 148, 12], [147, 14, 150, 12], [148, 14, 151, 12], [148, 20, 151, 18, "centerX"], [148, 27, 151, 25], [148, 30, 151, 28], [148, 31, 151, 29, "minX"], [148, 35, 151, 33], [148, 38, 151, 36, "maxX"], [148, 42, 151, 40], [148, 46, 151, 44], [148, 47, 151, 45], [149, 14, 152, 12], [149, 20, 152, 18, "centerY"], [149, 27, 152, 25], [149, 30, 152, 28], [149, 31, 152, 29, "minY"], [149, 35, 152, 33], [149, 38, 152, 36, "maxY"], [149, 42, 152, 40], [149, 46, 152, 44], [149, 47, 152, 45], [150, 14, 153, 12], [150, 20, 153, 18, "expandedWidth"], [150, 33, 153, 31], [150, 36, 153, 34, "faceWidth"], [150, 45, 153, 43], [150, 48, 153, 46], [150, 51, 153, 49], [151, 14, 154, 12], [151, 20, 154, 18, "expandedHeight"], [151, 34, 154, 32], [151, 37, 154, 35, "faceHeight"], [151, 47, 154, 45], [151, 50, 154, 48], [151, 53, 154, 51], [153, 14, 156, 12], [154, 14, 157, 12], [154, 20, 157, 18, "radiusX"], [154, 27, 157, 25], [154, 30, 157, 28, "Math"], [154, 34, 157, 32], [154, 35, 157, 33, "max"], [154, 38, 157, 36], [154, 39, 157, 37, "expandedWidth"], [154, 52, 157, 50], [154, 55, 157, 53], [154, 56, 157, 54], [154, 58, 157, 56], [154, 60, 157, 58], [154, 61, 157, 59], [155, 14, 158, 12], [155, 20, 158, 18, "radiusY"], [155, 27, 158, 25], [155, 30, 158, 28, "Math"], [155, 34, 158, 32], [155, 35, 158, 33, "max"], [155, 38, 158, 36], [155, 39, 158, 37, "expandedHeight"], [155, 53, 158, 51], [155, 56, 158, 54], [155, 57, 158, 55], [155, 59, 158, 57], [155, 61, 158, 59], [155, 62, 158, 60], [157, 14, 160, 12], [158, 14, 161, 12, "ctx"], [158, 17, 161, 15], [158, 18, 161, 16, "save"], [158, 22, 161, 20], [158, 23, 161, 21], [158, 24, 161, 22], [159, 14, 162, 12, "ctx"], [159, 17, 162, 15], [159, 18, 162, 16, "beginPath"], [159, 27, 162, 25], [159, 28, 162, 26], [159, 29, 162, 27], [160, 14, 163, 12, "ctx"], [160, 17, 163, 15], [160, 18, 163, 16, "ellipse"], [160, 25, 163, 23], [160, 26, 163, 24, "centerX"], [160, 33, 163, 31], [160, 35, 163, 33, "centerY"], [160, 42, 163, 40], [160, 44, 163, 42, "radiusX"], [160, 51, 163, 49], [160, 53, 163, 51, "radiusY"], [160, 60, 163, 58], [160, 62, 163, 60], [160, 63, 163, 61], [160, 65, 163, 63], [160, 66, 163, 64], [160, 68, 163, 66, "Math"], [160, 72, 163, 70], [160, 73, 163, 71, "PI"], [160, 75, 163, 73], [160, 78, 163, 76], [160, 79, 163, 77], [160, 80, 163, 78], [161, 14, 164, 12, "ctx"], [161, 17, 164, 15], [161, 18, 164, 16, "clip"], [161, 22, 164, 20], [161, 23, 164, 21], [161, 24, 164, 22], [162, 14, 165, 12, "ctx"], [162, 17, 165, 15], [162, 18, 165, 16, "filter"], [162, 24, 165, 22], [162, 27, 165, 25], [162, 39, 165, 37], [163, 14, 166, 12, "ctx"], [163, 17, 166, 15], [163, 18, 166, 16, "drawImage"], [163, 27, 166, 25], [163, 28, 166, 26, "video"], [163, 33, 166, 31], [163, 35, 166, 33], [163, 36, 166, 34], [163, 38, 166, 36], [163, 39, 166, 37], [163, 41, 166, 39, "canvas"], [163, 47, 166, 45], [163, 48, 166, 46, "width"], [163, 53, 166, 51], [163, 55, 166, 53, "canvas"], [163, 61, 166, 59], [163, 62, 166, 60, "height"], [163, 68, 166, 66], [163, 69, 166, 67], [164, 14, 167, 12, "ctx"], [164, 17, 167, 15], [164, 18, 167, 16, "restore"], [164, 25, 167, 23], [164, 26, 167, 24], [164, 27, 167, 25], [165, 12, 168, 10], [165, 13, 168, 11], [165, 14, 168, 12], [166, 10, 169, 8], [166, 11, 169, 9], [166, 17, 169, 15], [167, 12, 170, 10, "setFaceCount"], [167, 24, 170, 22], [167, 25, 170, 23], [167, 26, 170, 24], [167, 27, 170, 25], [168, 10, 171, 8], [169, 8, 173, 6], [169, 9, 173, 7], [169, 10, 173, 8], [169, 17, 173, 15, "error"], [169, 22, 173, 20], [169, 24, 173, 22], [170, 10, 174, 8, "console"], [170, 17, 174, 15], [170, 18, 174, 16, "error"], [170, 23, 174, 21], [170, 24, 174, 22], [170, 60, 174, 58], [170, 62, 174, 60, "error"], [170, 67, 174, 65], [170, 68, 174, 66], [171, 8, 175, 6], [173, 8, 177, 6], [174, 8, 178, 6], [174, 12, 178, 10, "isDetecting"], [174, 23, 178, 21], [174, 25, 178, 23], [175, 10, 179, 8, "rafRef"], [175, 16, 179, 14], [175, 17, 179, 15, "current"], [175, 24, 179, 22], [175, 27, 179, 25, "requestAnimationFrame"], [175, 48, 179, 46], [175, 49, 179, 47, "detectLoop"], [175, 59, 179, 57], [175, 60, 179, 58], [176, 8, 180, 6], [177, 6, 181, 4], [177, 7, 181, 5], [179, 6, 183, 4], [180, 6, 184, 4, "loadModel"], [180, 15, 184, 13], [180, 16, 184, 14], [180, 17, 184, 15], [182, 6, 186, 4], [183, 6, 187, 4], [183, 13, 187, 11], [183, 19, 187, 17], [184, 8, 188, 6, "isDetecting"], [184, 19, 188, 17], [184, 22, 188, 20], [184, 27, 188, 25], [185, 8, 189, 6], [185, 12, 189, 10, "rafRef"], [185, 18, 189, 16], [185, 19, 189, 17, "current"], [185, 26, 189, 24], [185, 28, 189, 26], [186, 10, 190, 8, "cancelAnimationFrame"], [186, 30, 190, 28], [186, 31, 190, 29, "rafRef"], [186, 37, 190, 35], [186, 38, 190, 36, "current"], [186, 45, 190, 43], [186, 46, 190, 44], [187, 8, 191, 6], [188, 6, 192, 4], [188, 7, 192, 5], [189, 4, 193, 2], [189, 5, 193, 3], [189, 7, 193, 5], [189, 8, 193, 6, "containerId"], [189, 19, 193, 17], [189, 21, 193, 19, "width"], [189, 26, 193, 24], [189, 28, 193, 26, "height"], [189, 34, 193, 32], [189, 35, 193, 33], [189, 36, 193, 34], [190, 4, 195, 2], [190, 24, 196, 4], [190, 28, 196, 4, "_jsxDevRuntime"], [190, 42, 196, 4], [190, 43, 196, 4, "jsxDEV"], [190, 49, 196, 4], [190, 51, 196, 4, "_jsxDevRuntime"], [190, 65, 196, 4], [190, 66, 196, 4, "Fragment"], [190, 74, 196, 4], [191, 6, 196, 4, "children"], [191, 14, 196, 4], [191, 30, 197, 6], [191, 34, 197, 6, "_jsxDevRuntime"], [191, 48, 197, 6], [191, 49, 197, 6, "jsxDEV"], [191, 55, 197, 6], [192, 8, 198, 8, "ref"], [192, 11, 198, 11], [192, 13, 198, 13, "canvasRef"], [192, 22, 198, 23], [193, 8, 199, 8, "style"], [193, 13, 199, 13], [193, 15, 199, 15], [194, 10, 200, 10, "position"], [194, 18, 200, 18], [194, 20, 200, 20], [194, 30, 200, 30], [195, 10, 201, 10, "left"], [195, 14, 201, 14], [195, 16, 201, 16], [195, 17, 201, 17], [196, 10, 202, 10, "top"], [196, 13, 202, 13], [196, 15, 202, 15], [196, 16, 202, 16], [197, 10, 203, 10, "width"], [197, 15, 203, 15], [198, 10, 204, 10, "height"], [198, 16, 204, 16], [199, 10, 205, 10, "pointerEvents"], [199, 23, 205, 23], [199, 25, 205, 25], [199, 31, 205, 31], [200, 10, 206, 10, "zIndex"], [200, 16, 206, 16], [200, 18, 206, 18], [201, 8, 207, 8], [202, 6, 207, 10], [203, 8, 207, 10, "fileName"], [203, 16, 207, 10], [203, 18, 207, 10, "_jsxFileName"], [203, 30, 207, 10], [204, 8, 207, 10, "lineNumber"], [204, 18, 207, 10], [205, 8, 207, 10, "columnNumber"], [205, 20, 207, 10], [206, 6, 207, 10], [206, 13, 208, 7], [206, 14, 208, 8], [206, 16, 210, 7, "isLoading"], [206, 25, 210, 16], [206, 42, 211, 8], [206, 46, 211, 8, "_jsxDevRuntime"], [206, 60, 211, 8], [206, 61, 211, 8, "jsxDEV"], [206, 67, 211, 8], [207, 8, 211, 13, "style"], [207, 13, 211, 18], [207, 15, 211, 20], [208, 10, 212, 10, "position"], [208, 18, 212, 18], [208, 20, 212, 20], [208, 30, 212, 30], [209, 10, 213, 10, "top"], [209, 13, 213, 13], [209, 15, 213, 15], [209, 17, 213, 17], [210, 10, 214, 10, "left"], [210, 14, 214, 14], [210, 16, 214, 16], [210, 18, 214, 18], [211, 10, 215, 10, "background"], [211, 20, 215, 20], [211, 22, 215, 22], [211, 39, 215, 39], [212, 10, 216, 10, "color"], [212, 15, 216, 15], [212, 17, 216, 17], [212, 24, 216, 24], [213, 10, 217, 10, "padding"], [213, 17, 217, 17], [213, 19, 217, 19], [213, 29, 217, 29], [214, 10, 218, 10, "borderRadius"], [214, 22, 218, 22], [214, 24, 218, 24], [214, 29, 218, 29], [215, 10, 219, 10, "fontSize"], [215, 18, 219, 18], [215, 20, 219, 20], [215, 26, 219, 26], [216, 10, 220, 10, "zIndex"], [216, 16, 220, 16], [216, 18, 220, 18], [217, 8, 221, 8], [217, 9, 221, 10], [218, 8, 221, 10, "children"], [218, 16, 221, 10], [218, 18, 221, 11], [219, 6, 223, 8], [220, 8, 223, 8, "fileName"], [220, 16, 223, 8], [220, 18, 223, 8, "_jsxFileName"], [220, 30, 223, 8], [221, 8, 223, 8, "lineNumber"], [221, 18, 223, 8], [222, 8, 223, 8, "columnNumber"], [222, 20, 223, 8], [223, 6, 223, 8], [223, 13, 223, 13], [223, 14, 224, 7], [223, 16, 225, 7], [223, 17, 225, 8, "isLoading"], [223, 26, 225, 17], [223, 30, 225, 21, "faceCount"], [223, 39, 225, 30], [223, 42, 225, 33], [223, 43, 225, 34], [223, 60, 226, 8], [223, 64, 226, 8, "_jsxDevRuntime"], [223, 78, 226, 8], [223, 79, 226, 8, "jsxDEV"], [223, 85, 226, 8], [224, 8, 226, 13, "style"], [224, 13, 226, 18], [224, 15, 226, 20], [225, 10, 227, 10, "position"], [225, 18, 227, 18], [225, 20, 227, 20], [225, 30, 227, 30], [226, 10, 228, 10, "top"], [226, 13, 228, 13], [226, 15, 228, 15], [226, 17, 228, 17], [227, 10, 229, 10, "left"], [227, 14, 229, 14], [227, 16, 229, 16], [227, 18, 229, 18], [228, 10, 230, 10, "background"], [228, 20, 230, 20], [228, 22, 230, 22], [228, 41, 230, 41], [229, 10, 231, 10, "color"], [229, 15, 231, 15], [229, 17, 231, 17], [229, 24, 231, 24], [230, 10, 232, 10, "padding"], [230, 17, 232, 17], [230, 19, 232, 19], [230, 29, 232, 29], [231, 10, 233, 10, "borderRadius"], [231, 22, 233, 22], [231, 24, 233, 24], [231, 29, 233, 29], [232, 10, 234, 10, "fontSize"], [232, 18, 234, 18], [232, 20, 234, 20], [232, 26, 234, 26], [233, 10, 235, 10, "zIndex"], [233, 16, 235, 16], [233, 18, 235, 18], [234, 8, 236, 8], [234, 9, 236, 10], [235, 8, 236, 10, "children"], [235, 16, 236, 10], [235, 19, 236, 11], [235, 51, 237, 25], [235, 53, 237, 26, "faceCount"], [235, 62, 237, 35], [235, 64, 237, 36], [235, 71, 237, 41], [235, 73, 237, 42, "faceCount"], [235, 82, 237, 51], [235, 85, 237, 54], [235, 86, 237, 55], [235, 89, 237, 58], [235, 92, 237, 61], [235, 95, 237, 64], [235, 97, 237, 66], [236, 6, 237, 66], [237, 8, 237, 66, "fileName"], [237, 16, 237, 66], [237, 18, 237, 66, "_jsxFileName"], [237, 30, 237, 66], [238, 8, 237, 66, "lineNumber"], [238, 18, 237, 66], [239, 8, 237, 66, "columnNumber"], [239, 20, 237, 66], [240, 6, 237, 66], [240, 13, 238, 13], [240, 14, 239, 7], [241, 4, 239, 7], [241, 19, 240, 6], [241, 20, 240, 7], [242, 2, 242, 0], [243, 2, 242, 1, "_s"], [243, 4, 242, 1], [243, 5, 16, 24, "BlazeFaceCanvas"], [243, 20, 16, 39], [244, 2, 16, 39, "_c"], [244, 4, 16, 39], [244, 7, 16, 24, "BlazeFaceCanvas"], [244, 22, 16, 39], [245, 2, 16, 39], [245, 6, 16, 39, "_c"], [245, 8, 16, 39], [246, 2, 16, 39, "$RefreshReg$"], [246, 14, 16, 39], [246, 15, 16, 39, "_c"], [246, 17, 16, 39], [247, 0, 16, 39], [247, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "loadModel", "Promise$argument_0", "script.onload", "script.onerror", "detectLoop", "predictions.forEach$argument_0", "<anonymous>"], "mappings": "AAA;eCe;YCO;sBCmC;kCCM;4BCG,eD;6BEC,uDF;WDE;kCCO;4BCG,eD;6BEC,mDF;WDE;KDoB;uBKE;8BCmB;WD4C;KLa;WOM;KPK;GDC;CDiD"}}, "type": "js/module"}]}