{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/BlazeFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 52, "index": 774}}], "key": "evUr5FsRzgXV+xqFHKyZkgVjslo=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 841}, "end": {"line": 27, "column": 42, "index": 883}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 946}, "end": {"line": 29, "column": 61, "index": 1007}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _BlazeFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/BlazeFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  // Removed TensorFlowFaceCanvas - using BlazeFaceCanvas instead\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(false); // Start disabled until BlazeFace loads\n    const [blazeFaceReady, setBlazeReady] = (0, _react.useState)(false); // Track BlazeFace loading state\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection - matching working test implementation\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js - using same version as working test\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {\n          original: {\n            x,\n            y,\n            width,\n            height\n          },\n          canvas: {\n            width: canvasWidth,\n            height: canvasHeight\n          },\n          clamped: {\n            x: clampedX,\n            y: clampedY,\n            width: clampedWidth,\n            height: clampedHeight\n          }\n        });\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: About to call processImageWithFaceBlur with:', photo.uri);\n        console.log('[EchoCameraWeb] 🔍 DEBUGGING: Function exists?', typeof processImageWithFaceBlur);\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      console.log('[EchoCameraWeb] 🚀 ENTRY: Starting face blur processing system...');\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {\n              bbox,\n              imageSize: {\n                width: img.width,\n                height: img.height\n              }\n            });\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {\n              faceX,\n              faceY,\n              faceWidth,\n              faceHeight,\n              isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0\n            });\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] 🚨 CRITICAL ERROR in processImageWithFaceBlur:', error);\n        console.error('[EchoCameraWeb] 🚨 Error stack:', error.stack);\n        console.error('[EchoCameraWeb] 🚨 Error details:', {\n          name: error.name,\n          message: error.message,\n          photoUri\n        });\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 909,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 930,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a',\n            // Keep video visible - BlazeFace canvas will overlay blur patches\n            opacity: 1\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n            // Enable BlazeFace after camera is ready\n            setTimeout(() => {\n              setPreviewBlurEnabled(true);\n            }, 1000); // Give camera a moment to stabilize\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_BlazeFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height,\n            onReady: () => {\n              console.log('[EchoCameraWeb] BlazeFace is ready!');\n              setBlazeReady(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 13\n          }, this)\n        }, void 0, false), isCameraReady && !previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            pointerEvents: 'none'\n          }],\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n            intensity: 15,\n            tint: \"dark\",\n            style: [styles.blurZone, {\n              left: 0,\n              top: viewSize.height * 0.25,\n              width: viewSize.width,\n              height: viewSize.height * 0.5,\n              borderRadius: 20,\n              opacity: 0.8\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n            intensity: 12,\n            tint: \"dark\",\n            style: [styles.blurZone, {\n              left: viewSize.width * 0.5 - viewSize.width * 0.3,\n              top: viewSize.height * 0.4 - viewSize.width * 0.3,\n              width: viewSize.width * 0.6,\n              height: viewSize.width * 0.6,\n              borderRadius: viewSize.width * 0.6 / 2,\n              opacity: 0.1\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 13\n          }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.previewChip,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.previewChipText,\n              children: \"BlazeFace Disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1024,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1023,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 11\n        }, this), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1038,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1039,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1043,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1044,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1054,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1095,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1097,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1094,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1093,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1132,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1138,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1130,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1129,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 940,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"8gamfVOIHuNPqWkkhwCJnngTGXg=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1725, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_BlazeFaceCanvas"], [20, 22, 25, 0], [20, 25, 25, 0, "_interopRequireDefault"], [20, 47, 25, 0], [20, 48, 25, 0, "require"], [20, 55, 25, 0], [20, 56, 25, 0, "_dependencyMap"], [20, 70, 25, 0], [21, 2, 27, 0], [21, 6, 27, 0, "_useUpload"], [21, 16, 27, 0], [21, 19, 27, 0, "_interopRequireDefault"], [21, 41, 27, 0], [21, 42, 27, 0, "require"], [21, 49, 27, 0], [21, 50, 27, 0, "_dependencyMap"], [21, 64, 27, 0], [22, 2, 29, 0], [22, 6, 29, 0, "_cameraChallenge"], [22, 22, 29, 0], [22, 25, 29, 0, "require"], [22, 32, 29, 0], [22, 33, 29, 0, "_dependencyMap"], [22, 47, 29, 0], [23, 2, 29, 61], [23, 6, 29, 61, "_Platform"], [23, 15, 29, 61], [23, 18, 29, 61, "_interopRequireDefault"], [23, 40, 29, 61], [23, 41, 29, 61, "require"], [23, 48, 29, 61], [23, 49, 29, 61, "_dependencyMap"], [23, 63, 29, 61], [24, 2, 29, 61], [24, 6, 29, 61, "_jsxDevRuntime"], [24, 20, 29, 61], [24, 23, 29, 61, "require"], [24, 30, 29, 61], [24, 31, 29, 61, "_dependencyMap"], [24, 45, 29, 61], [25, 2, 29, 61], [25, 6, 29, 61, "_jsxFileName"], [25, 18, 29, 61], [26, 4, 29, 61, "_s"], [26, 6, 29, 61], [26, 9, 29, 61, "$RefreshSig$"], [26, 21, 29, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 26, 0], [38, 2, 26, 0], [38, 11, 26, 0, "_interopRequireWildcard"], [38, 35, 26, 0, "e"], [38, 36, 26, 0], [38, 38, 26, 0, "t"], [38, 39, 26, 0], [38, 68, 26, 0, "WeakMap"], [38, 75, 26, 0], [38, 81, 26, 0, "r"], [38, 82, 26, 0], [38, 89, 26, 0, "WeakMap"], [38, 96, 26, 0], [38, 100, 26, 0, "n"], [38, 101, 26, 0], [38, 108, 26, 0, "WeakMap"], [38, 115, 26, 0], [38, 127, 26, 0, "_interopRequireWildcard"], [38, 150, 26, 0], [38, 162, 26, 0, "_interopRequireWildcard"], [38, 163, 26, 0, "e"], [38, 164, 26, 0], [38, 166, 26, 0, "t"], [38, 167, 26, 0], [38, 176, 26, 0, "t"], [38, 177, 26, 0], [38, 181, 26, 0, "e"], [38, 182, 26, 0], [38, 186, 26, 0, "e"], [38, 187, 26, 0], [38, 188, 26, 0, "__esModule"], [38, 198, 26, 0], [38, 207, 26, 0, "e"], [38, 208, 26, 0], [38, 214, 26, 0, "o"], [38, 215, 26, 0], [38, 217, 26, 0, "i"], [38, 218, 26, 0], [38, 220, 26, 0, "f"], [38, 221, 26, 0], [38, 226, 26, 0, "__proto__"], [38, 235, 26, 0], [38, 243, 26, 0, "default"], [38, 250, 26, 0], [38, 252, 26, 0, "e"], [38, 253, 26, 0], [38, 270, 26, 0, "e"], [38, 271, 26, 0], [38, 294, 26, 0, "e"], [38, 295, 26, 0], [38, 320, 26, 0, "e"], [38, 321, 26, 0], [38, 330, 26, 0, "f"], [38, 331, 26, 0], [38, 337, 26, 0, "o"], [38, 338, 26, 0], [38, 341, 26, 0, "t"], [38, 342, 26, 0], [38, 345, 26, 0, "n"], [38, 346, 26, 0], [38, 349, 26, 0, "r"], [38, 350, 26, 0], [38, 358, 26, 0, "o"], [38, 359, 26, 0], [38, 360, 26, 0, "has"], [38, 363, 26, 0], [38, 364, 26, 0, "e"], [38, 365, 26, 0], [38, 375, 26, 0, "o"], [38, 376, 26, 0], [38, 377, 26, 0, "get"], [38, 380, 26, 0], [38, 381, 26, 0, "e"], [38, 382, 26, 0], [38, 385, 26, 0, "o"], [38, 386, 26, 0], [38, 387, 26, 0, "set"], [38, 390, 26, 0], [38, 391, 26, 0, "e"], [38, 392, 26, 0], [38, 394, 26, 0, "f"], [38, 395, 26, 0], [38, 411, 26, 0, "t"], [38, 412, 26, 0], [38, 416, 26, 0, "e"], [38, 417, 26, 0], [38, 433, 26, 0, "t"], [38, 434, 26, 0], [38, 441, 26, 0, "hasOwnProperty"], [38, 455, 26, 0], [38, 456, 26, 0, "call"], [38, 460, 26, 0], [38, 461, 26, 0, "e"], [38, 462, 26, 0], [38, 464, 26, 0, "t"], [38, 465, 26, 0], [38, 472, 26, 0, "i"], [38, 473, 26, 0], [38, 477, 26, 0, "o"], [38, 478, 26, 0], [38, 481, 26, 0, "Object"], [38, 487, 26, 0], [38, 488, 26, 0, "defineProperty"], [38, 502, 26, 0], [38, 507, 26, 0, "Object"], [38, 513, 26, 0], [38, 514, 26, 0, "getOwnPropertyDescriptor"], [38, 538, 26, 0], [38, 539, 26, 0, "e"], [38, 540, 26, 0], [38, 542, 26, 0, "t"], [38, 543, 26, 0], [38, 550, 26, 0, "i"], [38, 551, 26, 0], [38, 552, 26, 0, "get"], [38, 555, 26, 0], [38, 559, 26, 0, "i"], [38, 560, 26, 0], [38, 561, 26, 0, "set"], [38, 564, 26, 0], [38, 568, 26, 0, "o"], [38, 569, 26, 0], [38, 570, 26, 0, "f"], [38, 571, 26, 0], [38, 573, 26, 0, "t"], [38, 574, 26, 0], [38, 576, 26, 0, "i"], [38, 577, 26, 0], [38, 581, 26, 0, "f"], [38, 582, 26, 0], [38, 583, 26, 0, "t"], [38, 584, 26, 0], [38, 588, 26, 0, "e"], [38, 589, 26, 0], [38, 590, 26, 0, "t"], [38, 591, 26, 0], [38, 602, 26, 0, "f"], [38, 603, 26, 0], [38, 608, 26, 0, "e"], [38, 609, 26, 0], [38, 611, 26, 0, "t"], [38, 612, 26, 0], [39, 2, 31, 0], [39, 8, 31, 6], [40, 4, 31, 8, "width"], [40, 9, 31, 13], [40, 11, 31, 15, "screenWidth"], [40, 22, 31, 26], [41, 4, 31, 28, "height"], [41, 10, 31, 34], [41, 12, 31, 36, "screenHeight"], [42, 2, 31, 49], [42, 3, 31, 50], [42, 6, 31, 53, "Dimensions"], [42, 25, 31, 63], [42, 26, 31, 64, "get"], [42, 29, 31, 67], [42, 30, 31, 68], [42, 38, 31, 76], [42, 39, 31, 77], [43, 2, 32, 0], [43, 8, 32, 6, "API_BASE_URL"], [43, 20, 32, 18], [43, 23, 32, 21], [43, 24, 33, 2, "_env2"], [43, 29, 33, 2], [43, 30, 33, 2, "env"], [43, 33, 33, 2], [43, 34, 33, 2, "EXPO_PUBLIC_BASE_URL"], [43, 54, 33, 2], [43, 58, 33, 2, "_env2"], [43, 63, 33, 2], [43, 64, 33, 2, "env"], [43, 67, 33, 2], [43, 68, 33, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [43, 94, 34, 40], [43, 98, 34, 40, "_env2"], [43, 103, 34, 40], [43, 104, 34, 40, "env"], [43, 107, 34, 40], [43, 108, 34, 40, "EXPO_PUBLIC_HOST"], [43, 124, 35, 30], [43, 128, 36, 2], [43, 130, 36, 4], [43, 132, 37, 2, "replace"], [43, 139, 37, 9], [43, 140, 37, 10], [43, 145, 37, 15], [43, 147, 37, 17], [43, 149, 37, 19], [43, 150, 37, 20], [45, 2, 50, 0], [47, 2, 52, 15], [47, 11, 52, 24, "EchoCameraWeb"], [47, 24, 52, 37, "EchoCameraWeb"], [47, 25, 52, 38], [48, 4, 53, 2, "userId"], [48, 10, 53, 8], [49, 4, 54, 2, "requestId"], [49, 13, 54, 11], [50, 4, 55, 2, "onComplete"], [50, 14, 55, 12], [51, 4, 56, 2, "onCancel"], [52, 2, 57, 20], [52, 3, 57, 21], [52, 5, 57, 23], [53, 4, 57, 23, "_s"], [53, 6, 57, 23], [54, 4, 58, 2], [54, 10, 58, 8, "cameraRef"], [54, 19, 58, 17], [54, 22, 58, 20], [54, 26, 58, 20, "useRef"], [54, 39, 58, 26], [54, 41, 58, 39], [54, 45, 58, 43], [54, 46, 58, 44], [55, 4, 59, 2], [55, 10, 59, 8], [55, 11, 59, 9, "permission"], [55, 21, 59, 19], [55, 23, 59, 21, "requestPermission"], [55, 40, 59, 38], [55, 41, 59, 39], [55, 44, 59, 42], [55, 48, 59, 42, "useCameraPermissions"], [55, 80, 59, 62], [55, 82, 59, 63], [55, 83, 59, 64], [57, 4, 61, 2], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "processingState"], [58, 26, 62, 24], [58, 28, 62, 26, "setProcessingState"], [58, 46, 62, 44], [58, 47, 62, 45], [58, 50, 62, 48], [58, 54, 62, 48, "useState"], [58, 69, 62, 56], [58, 71, 62, 74], [58, 77, 62, 80], [58, 78, 62, 81], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "challengeCode"], [59, 24, 63, 22], [59, 26, 63, 24, "setChallengeCode"], [59, 42, 63, 40], [59, 43, 63, 41], [59, 46, 63, 44], [59, 50, 63, 44, "useState"], [59, 65, 63, 52], [59, 67, 63, 68], [59, 71, 63, 72], [59, 72, 63, 73], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "processingProgress"], [60, 29, 64, 27], [60, 31, 64, 29, "setProcessingProgress"], [60, 52, 64, 50], [60, 53, 64, 51], [60, 56, 64, 54], [60, 60, 64, 54, "useState"], [60, 75, 64, 62], [60, 77, 64, 63], [60, 78, 64, 64], [60, 79, 64, 65], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "errorMessage"], [61, 23, 65, 21], [61, 25, 65, 23, "setErrorMessage"], [61, 40, 65, 38], [61, 41, 65, 39], [61, 44, 65, 42], [61, 48, 65, 42, "useState"], [61, 63, 65, 50], [61, 65, 65, 66], [61, 69, 65, 70], [61, 70, 65, 71], [62, 4, 66, 2], [62, 10, 66, 8], [62, 11, 66, 9, "capturedPhoto"], [62, 24, 66, 22], [62, 26, 66, 24, "setCapturedPhoto"], [62, 42, 66, 40], [62, 43, 66, 41], [62, 46, 66, 44], [62, 50, 66, 44, "useState"], [62, 65, 66, 52], [62, 67, 66, 68], [62, 71, 66, 72], [62, 72, 66, 73], [63, 4, 67, 2], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "previewBlurEnabled"], [64, 29, 68, 27], [64, 31, 68, 29, "setPreviewBlurEnabled"], [64, 52, 68, 50], [64, 53, 68, 51], [64, 56, 68, 54], [64, 60, 68, 54, "useState"], [64, 75, 68, 62], [64, 77, 68, 63], [64, 82, 68, 68], [64, 83, 68, 69], [64, 84, 68, 70], [64, 85, 68, 71], [65, 4, 69, 2], [65, 10, 69, 8], [65, 11, 69, 9, "blazeFaceReady"], [65, 25, 69, 23], [65, 27, 69, 25, "setBlazeReady"], [65, 40, 69, 38], [65, 41, 69, 39], [65, 44, 69, 42], [65, 48, 69, 42, "useState"], [65, 63, 69, 50], [65, 65, 69, 51], [65, 70, 69, 56], [65, 71, 69, 57], [65, 72, 69, 58], [65, 73, 69, 59], [66, 4, 70, 2], [66, 10, 70, 8], [66, 11, 70, 9, "viewSize"], [66, 19, 70, 17], [66, 21, 70, 19, "setViewSize"], [66, 32, 70, 30], [66, 33, 70, 31], [66, 36, 70, 34], [66, 40, 70, 34, "useState"], [66, 55, 70, 42], [66, 57, 70, 43], [67, 6, 70, 45, "width"], [67, 11, 70, 50], [67, 13, 70, 52], [67, 14, 70, 53], [68, 6, 70, 55, "height"], [68, 12, 70, 61], [68, 14, 70, 63], [69, 4, 70, 65], [69, 5, 70, 66], [69, 6, 70, 67], [70, 4, 71, 2], [71, 4, 72, 2], [71, 10, 72, 8], [71, 11, 72, 9, "isCameraReady"], [71, 24, 72, 22], [71, 26, 72, 24, "setIsCameraReady"], [71, 42, 72, 40], [71, 43, 72, 41], [71, 46, 72, 44], [71, 50, 72, 44, "useState"], [71, 65, 72, 52], [71, 67, 72, 53], [71, 72, 72, 58], [71, 73, 72, 59], [72, 4, 74, 2], [72, 10, 74, 8], [72, 11, 74, 9, "upload"], [72, 17, 74, 15], [72, 18, 74, 16], [72, 21, 74, 19], [72, 25, 74, 19, "useUpload"], [72, 43, 74, 28], [72, 45, 74, 29], [72, 46, 74, 30], [73, 4, 75, 2], [74, 4, 76, 2], [74, 8, 76, 2, "useEffect"], [74, 24, 76, 11], [74, 26, 76, 12], [74, 32, 76, 18], [75, 6, 77, 4], [75, 12, 77, 10, "controller"], [75, 22, 77, 20], [75, 25, 77, 23], [75, 29, 77, 27, "AbortController"], [75, 44, 77, 42], [75, 45, 77, 43], [75, 46, 77, 44], [76, 6, 79, 4], [76, 7, 79, 5], [76, 19, 79, 17], [77, 8, 80, 6], [77, 12, 80, 10], [78, 10, 81, 8], [78, 16, 81, 14, "code"], [78, 20, 81, 18], [78, 23, 81, 21], [78, 29, 81, 27], [78, 33, 81, 27, "fetchChallengeCode"], [78, 68, 81, 45], [78, 70, 81, 46], [79, 12, 81, 48, "userId"], [79, 18, 81, 54], [80, 12, 81, 56, "requestId"], [80, 21, 81, 65], [81, 12, 81, 67, "signal"], [81, 18, 81, 73], [81, 20, 81, 75, "controller"], [81, 30, 81, 85], [81, 31, 81, 86, "signal"], [82, 10, 81, 93], [82, 11, 81, 94], [82, 12, 81, 95], [83, 10, 82, 8, "setChallengeCode"], [83, 26, 82, 24], [83, 27, 82, 25, "code"], [83, 31, 82, 29], [83, 32, 82, 30], [84, 8, 83, 6], [84, 9, 83, 7], [84, 10, 83, 8], [84, 17, 83, 15, "e"], [84, 18, 83, 16], [84, 20, 83, 18], [85, 10, 84, 8, "console"], [85, 17, 84, 15], [85, 18, 84, 16, "warn"], [85, 22, 84, 20], [85, 23, 84, 21], [85, 69, 84, 67], [85, 71, 84, 69, "e"], [85, 72, 84, 70], [85, 73, 84, 71], [86, 10, 85, 8, "setChallengeCode"], [86, 26, 85, 24], [86, 27, 85, 25], [86, 35, 85, 33, "Date"], [86, 39, 85, 37], [86, 40, 85, 38, "now"], [86, 43, 85, 41], [86, 44, 85, 42], [86, 45, 85, 43], [86, 46, 85, 44, "toString"], [86, 54, 85, 52], [86, 55, 85, 53], [86, 57, 85, 55], [86, 58, 85, 56], [86, 59, 85, 57, "toUpperCase"], [86, 70, 85, 68], [86, 71, 85, 69], [86, 72, 85, 70], [86, 74, 85, 72], [86, 75, 85, 73], [87, 8, 86, 6], [88, 6, 87, 4], [88, 7, 87, 5], [88, 9, 87, 7], [88, 10, 87, 8], [89, 6, 89, 4], [89, 13, 89, 11], [89, 19, 89, 17, "controller"], [89, 29, 89, 27], [89, 30, 89, 28, "abort"], [89, 35, 89, 33], [89, 36, 89, 34], [89, 37, 89, 35], [90, 4, 90, 2], [90, 5, 90, 3], [90, 7, 90, 5], [90, 8, 90, 6, "userId"], [90, 14, 90, 12], [90, 16, 90, 14, "requestId"], [90, 25, 90, 23], [90, 26, 90, 24], [90, 27, 90, 25], [92, 4, 92, 2], [93, 4, 93, 2], [93, 10, 93, 8, "loadTensorFlowFaceDetection"], [93, 37, 93, 35], [93, 40, 93, 38], [93, 46, 93, 38, "loadTensorFlowFaceDetection"], [93, 47, 93, 38], [93, 52, 93, 50], [94, 6, 94, 4, "console"], [94, 13, 94, 11], [94, 14, 94, 12, "log"], [94, 17, 94, 15], [94, 18, 94, 16], [94, 78, 94, 76], [94, 79, 94, 77], [96, 6, 96, 4], [97, 6, 97, 4], [97, 10, 97, 8], [97, 11, 97, 10, "window"], [97, 17, 97, 16], [97, 18, 97, 25, "tf"], [97, 20, 97, 27], [97, 22, 97, 29], [98, 8, 98, 6], [98, 14, 98, 12], [98, 18, 98, 16, "Promise"], [98, 25, 98, 23], [98, 26, 98, 24], [98, 27, 98, 25, "resolve"], [98, 34, 98, 32], [98, 36, 98, 34, "reject"], [98, 42, 98, 40], [98, 47, 98, 45], [99, 10, 99, 8], [99, 16, 99, 14, "script"], [99, 22, 99, 20], [99, 25, 99, 23, "document"], [99, 33, 99, 31], [99, 34, 99, 32, "createElement"], [99, 47, 99, 45], [99, 48, 99, 46], [99, 56, 99, 54], [99, 57, 99, 55], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "src"], [100, 20, 100, 18], [100, 23, 100, 21], [100, 92, 100, 90], [101, 10, 101, 8, "script"], [101, 16, 101, 14], [101, 17, 101, 15, "onload"], [101, 23, 101, 21], [101, 26, 101, 24, "resolve"], [101, 33, 101, 31], [102, 10, 102, 8, "script"], [102, 16, 102, 14], [102, 17, 102, 15, "onerror"], [102, 24, 102, 22], [102, 27, 102, 25, "reject"], [102, 33, 102, 31], [103, 10, 103, 8, "document"], [103, 18, 103, 16], [103, 19, 103, 17, "head"], [103, 23, 103, 21], [103, 24, 103, 22, "append<PERSON><PERSON><PERSON>"], [103, 35, 103, 33], [103, 36, 103, 34, "script"], [103, 42, 103, 40], [103, 43, 103, 41], [104, 8, 104, 6], [104, 9, 104, 7], [104, 10, 104, 8], [105, 6, 105, 4], [107, 6, 107, 4], [108, 6, 108, 4], [108, 10, 108, 8], [108, 11, 108, 10, "window"], [108, 17, 108, 16], [108, 18, 108, 25, "blazeface"], [108, 27, 108, 34], [108, 29, 108, 36], [109, 8, 109, 6], [109, 14, 109, 12], [109, 18, 109, 16, "Promise"], [109, 25, 109, 23], [109, 26, 109, 24], [109, 27, 109, 25, "resolve"], [109, 34, 109, 32], [109, 36, 109, 34, "reject"], [109, 42, 109, 40], [109, 47, 109, 45], [110, 10, 110, 8], [110, 16, 110, 14, "script"], [110, 22, 110, 20], [110, 25, 110, 23, "document"], [110, 33, 110, 31], [110, 34, 110, 32, "createElement"], [110, 47, 110, 45], [110, 48, 110, 46], [110, 56, 110, 54], [110, 57, 110, 55], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "src"], [111, 20, 111, 18], [111, 23, 111, 21], [111, 106, 111, 104], [112, 10, 112, 8, "script"], [112, 16, 112, 14], [112, 17, 112, 15, "onload"], [112, 23, 112, 21], [112, 26, 112, 24, "resolve"], [112, 33, 112, 31], [113, 10, 113, 8, "script"], [113, 16, 113, 14], [113, 17, 113, 15, "onerror"], [113, 24, 113, 22], [113, 27, 113, 25, "reject"], [113, 33, 113, 31], [114, 10, 114, 8, "document"], [114, 18, 114, 16], [114, 19, 114, 17, "head"], [114, 23, 114, 21], [114, 24, 114, 22, "append<PERSON><PERSON><PERSON>"], [114, 35, 114, 33], [114, 36, 114, 34, "script"], [114, 42, 114, 40], [114, 43, 114, 41], [115, 8, 115, 6], [115, 9, 115, 7], [115, 10, 115, 8], [116, 6, 116, 4], [117, 6, 118, 4, "console"], [117, 13, 118, 11], [117, 14, 118, 12, "log"], [117, 17, 118, 15], [117, 18, 118, 16], [117, 72, 118, 70], [117, 73, 118, 71], [118, 4, 119, 2], [118, 5, 119, 3], [119, 4, 121, 2], [119, 10, 121, 8, "detectFacesWithTensorFlow"], [119, 35, 121, 33], [119, 38, 121, 36], [119, 44, 121, 43, "img"], [119, 47, 121, 64], [119, 51, 121, 69], [120, 6, 122, 4], [120, 10, 122, 8], [121, 8, 123, 6], [121, 14, 123, 12, "blazeface"], [121, 23, 123, 21], [121, 26, 123, 25, "window"], [121, 32, 123, 31], [121, 33, 123, 40, "blazeface"], [121, 42, 123, 49], [122, 8, 124, 6], [122, 14, 124, 12, "tf"], [122, 16, 124, 14], [122, 19, 124, 18, "window"], [122, 25, 124, 24], [122, 26, 124, 33, "tf"], [122, 28, 124, 35], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 67, 126, 65], [123, 68, 126, 66], [124, 8, 127, 6], [124, 14, 127, 12, "model"], [124, 19, 127, 17], [124, 22, 127, 20], [124, 28, 127, 26, "blazeface"], [124, 37, 127, 35], [124, 38, 127, 36, "load"], [124, 42, 127, 40], [124, 43, 127, 41], [124, 44, 127, 42], [125, 8, 128, 6, "console"], [125, 15, 128, 13], [125, 16, 128, 14, "log"], [125, 19, 128, 17], [125, 20, 128, 18], [125, 82, 128, 80], [125, 83, 128, 81], [127, 8, 130, 6], [128, 8, 131, 6], [128, 14, 131, 12, "tensor"], [128, 20, 131, 18], [128, 23, 131, 21, "tf"], [128, 25, 131, 23], [128, 26, 131, 24, "browser"], [128, 33, 131, 31], [128, 34, 131, 32, "fromPixels"], [128, 44, 131, 42], [128, 45, 131, 43, "img"], [128, 48, 131, 46], [128, 49, 131, 47], [130, 8, 133, 6], [131, 8, 134, 6], [131, 14, 134, 12, "predictions"], [131, 25, 134, 23], [131, 28, 134, 26], [131, 34, 134, 32, "model"], [131, 39, 134, 37], [131, 40, 134, 38, "estimateFaces"], [131, 53, 134, 51], [131, 54, 134, 52, "tensor"], [131, 60, 134, 58], [131, 62, 134, 60], [131, 67, 134, 65], [131, 69, 134, 67], [131, 72, 134, 70], [131, 73, 134, 71], [131, 74, 134, 72], [131, 75, 134, 73], [133, 8, 136, 6], [134, 8, 137, 6, "tensor"], [134, 14, 137, 12], [134, 15, 137, 13, "dispose"], [134, 22, 137, 20], [134, 23, 137, 21], [134, 24, 137, 22], [135, 8, 139, 6, "console"], [135, 15, 139, 13], [135, 16, 139, 14, "log"], [135, 19, 139, 17], [135, 20, 139, 18], [135, 61, 139, 59, "predictions"], [135, 72, 139, 70], [135, 73, 139, 71, "length"], [135, 79, 139, 77], [135, 87, 139, 85], [135, 88, 139, 86], [137, 8, 141, 6], [138, 8, 142, 6], [138, 14, 142, 12, "faces"], [138, 19, 142, 17], [138, 22, 142, 20, "predictions"], [138, 33, 142, 31], [138, 34, 142, 32, "map"], [138, 37, 142, 35], [138, 38, 142, 36], [138, 39, 142, 37, "prediction"], [138, 49, 142, 52], [138, 51, 142, 54, "index"], [138, 56, 142, 67], [138, 61, 142, 72], [139, 10, 143, 8], [139, 16, 143, 14], [139, 17, 143, 15, "x"], [139, 18, 143, 16], [139, 20, 143, 18, "y"], [139, 21, 143, 19], [139, 22, 143, 20], [139, 25, 143, 23, "prediction"], [139, 35, 143, 33], [139, 36, 143, 34, "topLeft"], [139, 43, 143, 41], [140, 10, 144, 8], [140, 16, 144, 14], [140, 17, 144, 15, "x2"], [140, 19, 144, 17], [140, 21, 144, 19, "y2"], [140, 23, 144, 21], [140, 24, 144, 22], [140, 27, 144, 25, "prediction"], [140, 37, 144, 35], [140, 38, 144, 36, "bottomRight"], [140, 49, 144, 47], [141, 10, 145, 8], [141, 16, 145, 14, "width"], [141, 21, 145, 19], [141, 24, 145, 22, "x2"], [141, 26, 145, 24], [141, 29, 145, 27, "x"], [141, 30, 145, 28], [142, 10, 146, 8], [142, 16, 146, 14, "height"], [142, 22, 146, 20], [142, 25, 146, 23, "y2"], [142, 27, 146, 25], [142, 30, 146, 28, "y"], [142, 31, 146, 29], [143, 10, 148, 8, "console"], [143, 17, 148, 15], [143, 18, 148, 16, "log"], [143, 21, 148, 19], [143, 22, 148, 20], [143, 49, 148, 47, "index"], [143, 54, 148, 52], [143, 57, 148, 55], [143, 58, 148, 56], [143, 61, 148, 59], [143, 63, 148, 61], [144, 12, 149, 10, "topLeft"], [144, 19, 149, 17], [144, 21, 149, 19], [144, 22, 149, 20, "x"], [144, 23, 149, 21], [144, 25, 149, 23, "y"], [144, 26, 149, 24], [144, 27, 149, 25], [145, 12, 150, 10, "bottomRight"], [145, 23, 150, 21], [145, 25, 150, 23], [145, 26, 150, 24, "x2"], [145, 28, 150, 26], [145, 30, 150, 28, "y2"], [145, 32, 150, 30], [145, 33, 150, 31], [146, 12, 151, 10, "size"], [146, 16, 151, 14], [146, 18, 151, 16], [146, 19, 151, 17, "width"], [146, 24, 151, 22], [146, 26, 151, 24, "height"], [146, 32, 151, 30], [147, 10, 152, 8], [147, 11, 152, 9], [147, 12, 152, 10], [148, 10, 154, 8], [148, 17, 154, 15], [149, 12, 155, 10, "boundingBox"], [149, 23, 155, 21], [149, 25, 155, 23], [150, 14, 156, 12, "xCenter"], [150, 21, 156, 19], [150, 23, 156, 21], [150, 24, 156, 22, "x"], [150, 25, 156, 23], [150, 28, 156, 26, "width"], [150, 33, 156, 31], [150, 36, 156, 34], [150, 37, 156, 35], [150, 41, 156, 39, "img"], [150, 44, 156, 42], [150, 45, 156, 43, "width"], [150, 50, 156, 48], [151, 14, 157, 12, "yCenter"], [151, 21, 157, 19], [151, 23, 157, 21], [151, 24, 157, 22, "y"], [151, 25, 157, 23], [151, 28, 157, 26, "height"], [151, 34, 157, 32], [151, 37, 157, 35], [151, 38, 157, 36], [151, 42, 157, 40, "img"], [151, 45, 157, 43], [151, 46, 157, 44, "height"], [151, 52, 157, 50], [152, 14, 158, 12, "width"], [152, 19, 158, 17], [152, 21, 158, 19, "width"], [152, 26, 158, 24], [152, 29, 158, 27, "img"], [152, 32, 158, 30], [152, 33, 158, 31, "width"], [152, 38, 158, 36], [153, 14, 159, 12, "height"], [153, 20, 159, 18], [153, 22, 159, 20, "height"], [153, 28, 159, 26], [153, 31, 159, 29, "img"], [153, 34, 159, 32], [153, 35, 159, 33, "height"], [154, 12, 160, 10], [155, 10, 161, 8], [155, 11, 161, 9], [156, 8, 162, 6], [156, 9, 162, 7], [156, 10, 162, 8], [157, 8, 164, 6], [157, 15, 164, 13, "faces"], [157, 20, 164, 18], [158, 6, 165, 4], [158, 7, 165, 5], [158, 8, 165, 6], [158, 15, 165, 13, "error"], [158, 20, 165, 18], [158, 22, 165, 20], [159, 8, 166, 6, "console"], [159, 15, 166, 13], [159, 16, 166, 14, "error"], [159, 21, 166, 19], [159, 22, 166, 20], [159, 75, 166, 73], [159, 77, 166, 75, "error"], [159, 82, 166, 80], [159, 83, 166, 81], [160, 8, 167, 6], [160, 15, 167, 13], [160, 17, 167, 15], [161, 6, 168, 4], [162, 4, 169, 2], [162, 5, 169, 3], [163, 4, 171, 2], [163, 10, 171, 8, "detectFacesHeuristic"], [163, 30, 171, 28], [163, 33, 171, 31, "detectFacesHeuristic"], [163, 34, 171, 32, "img"], [163, 37, 171, 53], [163, 39, 171, 55, "ctx"], [163, 42, 171, 84], [163, 47, 171, 89], [164, 6, 172, 4, "console"], [164, 13, 172, 11], [164, 14, 172, 12, "log"], [164, 17, 172, 15], [164, 18, 172, 16], [164, 83, 172, 81], [164, 84, 172, 82], [166, 6, 174, 4], [167, 6, 175, 4], [167, 12, 175, 10, "imageData"], [167, 21, 175, 19], [167, 24, 175, 22, "ctx"], [167, 27, 175, 25], [167, 28, 175, 26, "getImageData"], [167, 40, 175, 38], [167, 41, 175, 39], [167, 42, 175, 40], [167, 44, 175, 42], [167, 45, 175, 43], [167, 47, 175, 45, "img"], [167, 50, 175, 48], [167, 51, 175, 49, "width"], [167, 56, 175, 54], [167, 58, 175, 56, "img"], [167, 61, 175, 59], [167, 62, 175, 60, "height"], [167, 68, 175, 66], [167, 69, 175, 67], [168, 6, 176, 4], [168, 12, 176, 10, "data"], [168, 16, 176, 14], [168, 19, 176, 17, "imageData"], [168, 28, 176, 26], [168, 29, 176, 27, "data"], [168, 33, 176, 31], [170, 6, 178, 4], [171, 6, 179, 4], [171, 12, 179, 10, "faces"], [171, 17, 179, 15], [171, 20, 179, 18], [171, 22, 179, 20], [172, 6, 180, 4], [172, 12, 180, 10, "blockSize"], [172, 21, 180, 19], [172, 24, 180, 22, "Math"], [172, 28, 180, 26], [172, 29, 180, 27, "min"], [172, 32, 180, 30], [172, 33, 180, 31, "img"], [172, 36, 180, 34], [172, 37, 180, 35, "width"], [172, 42, 180, 40], [172, 44, 180, 42, "img"], [172, 47, 180, 45], [172, 48, 180, 46, "height"], [172, 54, 180, 52], [172, 55, 180, 53], [172, 58, 180, 56], [172, 60, 180, 58], [172, 61, 180, 59], [172, 62, 180, 60], [174, 6, 182, 4, "console"], [174, 13, 182, 11], [174, 14, 182, 12, "log"], [174, 17, 182, 15], [174, 18, 182, 16], [174, 59, 182, 57, "blockSize"], [174, 68, 182, 66], [174, 82, 182, 80], [174, 83, 182, 81], [175, 6, 184, 4], [175, 11, 184, 9], [175, 15, 184, 13, "y"], [175, 16, 184, 14], [175, 19, 184, 17], [175, 20, 184, 18], [175, 22, 184, 20, "y"], [175, 23, 184, 21], [175, 26, 184, 24, "img"], [175, 29, 184, 27], [175, 30, 184, 28, "height"], [175, 36, 184, 34], [175, 39, 184, 37, "blockSize"], [175, 48, 184, 46], [175, 50, 184, 48, "y"], [175, 51, 184, 49], [175, 55, 184, 53, "blockSize"], [175, 64, 184, 62], [175, 67, 184, 65], [175, 68, 184, 66], [175, 70, 184, 68], [176, 8, 184, 70], [177, 8, 185, 6], [177, 13, 185, 11], [177, 17, 185, 15, "x"], [177, 18, 185, 16], [177, 21, 185, 19], [177, 22, 185, 20], [177, 24, 185, 22, "x"], [177, 25, 185, 23], [177, 28, 185, 26, "img"], [177, 31, 185, 29], [177, 32, 185, 30, "width"], [177, 37, 185, 35], [177, 40, 185, 38, "blockSize"], [177, 49, 185, 47], [177, 51, 185, 49, "x"], [177, 52, 185, 50], [177, 56, 185, 54, "blockSize"], [177, 65, 185, 63], [177, 68, 185, 66], [177, 69, 185, 67], [177, 71, 185, 69], [178, 10, 186, 8], [178, 16, 186, 14, "analysis"], [178, 24, 186, 22], [178, 27, 186, 25, "analyzeRegionForFace"], [178, 47, 186, 45], [178, 48, 186, 46, "data"], [178, 52, 186, 50], [178, 54, 186, 52, "x"], [178, 55, 186, 53], [178, 57, 186, 55, "y"], [178, 58, 186, 56], [178, 60, 186, 58, "blockSize"], [178, 69, 186, 67], [178, 71, 186, 69, "img"], [178, 74, 186, 72], [178, 75, 186, 73, "width"], [178, 80, 186, 78], [178, 82, 186, 80, "img"], [178, 85, 186, 83], [178, 86, 186, 84, "height"], [178, 92, 186, 90], [178, 93, 186, 91], [180, 10, 188, 8], [181, 10, 189, 8], [181, 14, 189, 12, "analysis"], [181, 22, 189, 20], [181, 23, 189, 21, "skinRatio"], [181, 32, 189, 30], [181, 35, 189, 33], [181, 39, 189, 37], [182, 10, 189, 42], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "hasVariation"], [183, 31, 190, 33], [183, 35, 191, 12, "analysis"], [183, 43, 191, 20], [183, 44, 191, 21, "brightness"], [183, 54, 191, 31], [183, 57, 191, 34], [183, 61, 191, 38], [184, 10, 191, 43], [185, 10, 192, 12, "analysis"], [185, 18, 192, 20], [185, 19, 192, 21, "brightness"], [185, 29, 192, 31], [185, 32, 192, 34], [185, 35, 192, 37], [185, 37, 192, 39], [186, 12, 192, 43], [188, 12, 194, 10, "faces"], [188, 17, 194, 15], [188, 18, 194, 16, "push"], [188, 22, 194, 20], [188, 23, 194, 21], [189, 14, 195, 12, "boundingBox"], [189, 25, 195, 23], [189, 27, 195, 25], [190, 16, 196, 14, "xCenter"], [190, 23, 196, 21], [190, 25, 196, 23], [190, 26, 196, 24, "x"], [190, 27, 196, 25], [190, 30, 196, 28, "blockSize"], [190, 39, 196, 37], [190, 42, 196, 40], [190, 43, 196, 41], [190, 47, 196, 45, "img"], [190, 50, 196, 48], [190, 51, 196, 49, "width"], [190, 56, 196, 54], [191, 16, 197, 14, "yCenter"], [191, 23, 197, 21], [191, 25, 197, 23], [191, 26, 197, 24, "y"], [191, 27, 197, 25], [191, 30, 197, 28, "blockSize"], [191, 39, 197, 37], [191, 42, 197, 40], [191, 43, 197, 41], [191, 47, 197, 45, "img"], [191, 50, 197, 48], [191, 51, 197, 49, "height"], [191, 57, 197, 55], [192, 16, 198, 14, "width"], [192, 21, 198, 19], [192, 23, 198, 22, "blockSize"], [192, 32, 198, 31], [192, 35, 198, 34], [192, 38, 198, 37], [192, 41, 198, 41, "img"], [192, 44, 198, 44], [192, 45, 198, 45, "width"], [192, 50, 198, 50], [193, 16, 199, 14, "height"], [193, 22, 199, 20], [193, 24, 199, 23, "blockSize"], [193, 33, 199, 32], [193, 36, 199, 35], [193, 39, 199, 38], [193, 42, 199, 42, "img"], [193, 45, 199, 45], [193, 46, 199, 46, "height"], [194, 14, 200, 12], [194, 15, 200, 13], [195, 14, 201, 12, "confidence"], [195, 24, 201, 22], [195, 26, 201, 24, "analysis"], [195, 34, 201, 32], [195, 35, 201, 33, "skinRatio"], [195, 44, 201, 42], [195, 47, 201, 45, "analysis"], [195, 55, 201, 53], [195, 56, 201, 54, "variation"], [196, 12, 202, 10], [196, 13, 202, 11], [196, 14, 202, 12], [197, 12, 204, 10, "console"], [197, 19, 204, 17], [197, 20, 204, 18, "log"], [197, 23, 204, 21], [197, 24, 204, 22], [197, 71, 204, 69, "Math"], [197, 75, 204, 73], [197, 76, 204, 74, "round"], [197, 81, 204, 79], [197, 82, 204, 80, "x"], [197, 83, 204, 81], [197, 84, 204, 82], [197, 89, 204, 87, "Math"], [197, 93, 204, 91], [197, 94, 204, 92, "round"], [197, 99, 204, 97], [197, 100, 204, 98, "y"], [197, 101, 204, 99], [197, 102, 204, 100], [197, 115, 204, 113], [197, 116, 204, 114, "analysis"], [197, 124, 204, 122], [197, 125, 204, 123, "skinRatio"], [197, 134, 204, 132], [197, 137, 204, 135], [197, 140, 204, 138], [197, 142, 204, 140, "toFixed"], [197, 149, 204, 147], [197, 150, 204, 148], [197, 151, 204, 149], [197, 152, 204, 150], [197, 169, 204, 167, "analysis"], [197, 177, 204, 175], [197, 178, 204, 176, "variation"], [197, 187, 204, 185], [197, 188, 204, 186, "toFixed"], [197, 195, 204, 193], [197, 196, 204, 194], [197, 197, 204, 195], [197, 198, 204, 196], [197, 215, 204, 213, "analysis"], [197, 223, 204, 221], [197, 224, 204, 222, "brightness"], [197, 234, 204, 232], [197, 235, 204, 233, "toFixed"], [197, 242, 204, 240], [197, 243, 204, 241], [197, 244, 204, 242], [197, 245, 204, 243], [197, 247, 204, 245], [197, 248, 204, 246], [198, 10, 205, 8], [199, 8, 206, 6], [200, 6, 207, 4], [202, 6, 209, 4], [203, 6, 210, 4, "faces"], [203, 11, 210, 9], [203, 12, 210, 10, "sort"], [203, 16, 210, 14], [203, 17, 210, 15], [203, 18, 210, 16, "a"], [203, 19, 210, 17], [203, 21, 210, 19, "b"], [203, 22, 210, 20], [203, 27, 210, 25], [203, 28, 210, 26, "b"], [203, 29, 210, 27], [203, 30, 210, 28, "confidence"], [203, 40, 210, 38], [203, 44, 210, 42], [203, 45, 210, 43], [203, 50, 210, 48, "a"], [203, 51, 210, 49], [203, 52, 210, 50, "confidence"], [203, 62, 210, 60], [203, 66, 210, 64], [203, 67, 210, 65], [203, 68, 210, 66], [203, 69, 210, 67], [204, 6, 211, 4], [204, 12, 211, 10, "mergedFaces"], [204, 23, 211, 21], [204, 26, 211, 24, "mergeFaceDetections"], [204, 45, 211, 43], [204, 46, 211, 44, "faces"], [204, 51, 211, 49], [204, 52, 211, 50], [205, 6, 213, 4, "console"], [205, 13, 213, 11], [205, 14, 213, 12, "log"], [205, 17, 213, 15], [205, 18, 213, 16], [205, 61, 213, 59, "faces"], [205, 66, 213, 64], [205, 67, 213, 65, "length"], [205, 73, 213, 71], [205, 90, 213, 88, "mergedFaces"], [205, 101, 213, 99], [205, 102, 213, 100, "length"], [205, 108, 213, 106], [205, 123, 213, 121], [205, 124, 213, 122], [206, 6, 214, 4], [206, 13, 214, 11, "mergedFaces"], [206, 24, 214, 22], [206, 25, 214, 23, "slice"], [206, 30, 214, 28], [206, 31, 214, 29], [206, 32, 214, 30], [206, 34, 214, 32], [206, 35, 214, 33], [206, 36, 214, 34], [206, 37, 214, 35], [206, 38, 214, 36], [207, 4, 215, 2], [207, 5, 215, 3], [208, 4, 217, 2], [208, 10, 217, 8, "detectFacesAggressive"], [208, 31, 217, 29], [208, 34, 217, 32, "detectFacesAggressive"], [208, 35, 217, 33, "img"], [208, 38, 217, 54], [208, 40, 217, 56, "ctx"], [208, 43, 217, 85], [208, 48, 217, 90], [209, 6, 218, 4, "console"], [209, 13, 218, 11], [209, 14, 218, 12, "log"], [209, 17, 218, 15], [209, 18, 218, 16], [209, 75, 218, 73], [209, 76, 218, 74], [211, 6, 220, 4], [212, 6, 221, 4], [212, 12, 221, 10, "imageData"], [212, 21, 221, 19], [212, 24, 221, 22, "ctx"], [212, 27, 221, 25], [212, 28, 221, 26, "getImageData"], [212, 40, 221, 38], [212, 41, 221, 39], [212, 42, 221, 40], [212, 44, 221, 42], [212, 45, 221, 43], [212, 47, 221, 45, "img"], [212, 50, 221, 48], [212, 51, 221, 49, "width"], [212, 56, 221, 54], [212, 58, 221, 56, "img"], [212, 61, 221, 59], [212, 62, 221, 60, "height"], [212, 68, 221, 66], [212, 69, 221, 67], [213, 6, 222, 4], [213, 12, 222, 10, "data"], [213, 16, 222, 14], [213, 19, 222, 17, "imageData"], [213, 28, 222, 26], [213, 29, 222, 27, "data"], [213, 33, 222, 31], [214, 6, 224, 4], [214, 12, 224, 10, "faces"], [214, 17, 224, 15], [214, 20, 224, 18], [214, 22, 224, 20], [215, 6, 225, 4], [215, 12, 225, 10, "blockSize"], [215, 21, 225, 19], [215, 24, 225, 22, "Math"], [215, 28, 225, 26], [215, 29, 225, 27, "min"], [215, 32, 225, 30], [215, 33, 225, 31, "img"], [215, 36, 225, 34], [215, 37, 225, 35, "width"], [215, 42, 225, 40], [215, 44, 225, 42, "img"], [215, 47, 225, 45], [215, 48, 225, 46, "height"], [215, 54, 225, 52], [215, 55, 225, 53], [215, 58, 225, 56], [215, 59, 225, 57], [215, 60, 225, 58], [215, 61, 225, 59], [217, 6, 227, 4], [217, 11, 227, 9], [217, 15, 227, 13, "y"], [217, 16, 227, 14], [217, 19, 227, 17], [217, 20, 227, 18], [217, 22, 227, 20, "y"], [217, 23, 227, 21], [217, 26, 227, 24, "img"], [217, 29, 227, 27], [217, 30, 227, 28, "height"], [217, 36, 227, 34], [217, 39, 227, 37, "blockSize"], [217, 48, 227, 46], [217, 50, 227, 48, "y"], [217, 51, 227, 49], [217, 55, 227, 53, "blockSize"], [217, 64, 227, 62], [217, 67, 227, 65], [217, 68, 227, 66], [217, 70, 227, 68], [218, 8, 227, 70], [219, 8, 228, 6], [219, 13, 228, 11], [219, 17, 228, 15, "x"], [219, 18, 228, 16], [219, 21, 228, 19], [219, 22, 228, 20], [219, 24, 228, 22, "x"], [219, 25, 228, 23], [219, 28, 228, 26, "img"], [219, 31, 228, 29], [219, 32, 228, 30, "width"], [219, 37, 228, 35], [219, 40, 228, 38, "blockSize"], [219, 49, 228, 47], [219, 51, 228, 49, "x"], [219, 52, 228, 50], [219, 56, 228, 54, "blockSize"], [219, 65, 228, 63], [219, 68, 228, 66], [219, 69, 228, 67], [219, 71, 228, 69], [220, 10, 229, 8], [220, 16, 229, 14, "analysis"], [220, 24, 229, 22], [220, 27, 229, 25, "analyzeRegionForFace"], [220, 47, 229, 45], [220, 48, 229, 46, "data"], [220, 52, 229, 50], [220, 54, 229, 52, "x"], [220, 55, 229, 53], [220, 57, 229, 55, "y"], [220, 58, 229, 56], [220, 60, 229, 58, "blockSize"], [220, 69, 229, 67], [220, 71, 229, 69, "img"], [220, 74, 229, 72], [220, 75, 229, 73, "width"], [220, 80, 229, 78], [220, 82, 229, 80, "img"], [220, 85, 229, 83], [220, 86, 229, 84, "height"], [220, 92, 229, 90], [220, 93, 229, 91], [222, 10, 231, 8], [223, 10, 232, 8], [223, 14, 232, 12, "analysis"], [223, 22, 232, 20], [223, 23, 232, 21, "skinRatio"], [223, 32, 232, 30], [223, 35, 232, 33], [223, 39, 232, 37], [224, 10, 232, 42], [225, 10, 233, 12, "analysis"], [225, 18, 233, 20], [225, 19, 233, 21, "brightness"], [225, 29, 233, 31], [225, 32, 233, 34], [225, 35, 233, 37], [226, 10, 233, 42], [227, 10, 234, 12, "analysis"], [227, 18, 234, 20], [227, 19, 234, 21, "brightness"], [227, 29, 234, 31], [227, 32, 234, 34], [227, 36, 234, 38], [227, 38, 234, 40], [228, 12, 234, 43], [230, 12, 236, 10, "faces"], [230, 17, 236, 15], [230, 18, 236, 16, "push"], [230, 22, 236, 20], [230, 23, 236, 21], [231, 14, 237, 12, "boundingBox"], [231, 25, 237, 23], [231, 27, 237, 25], [232, 16, 238, 14, "xCenter"], [232, 23, 238, 21], [232, 25, 238, 23], [232, 26, 238, 24, "x"], [232, 27, 238, 25], [232, 30, 238, 28, "blockSize"], [232, 39, 238, 37], [232, 42, 238, 40], [232, 43, 238, 41], [232, 47, 238, 45, "img"], [232, 50, 238, 48], [232, 51, 238, 49, "width"], [232, 56, 238, 54], [233, 16, 239, 14, "yCenter"], [233, 23, 239, 21], [233, 25, 239, 23], [233, 26, 239, 24, "y"], [233, 27, 239, 25], [233, 30, 239, 28, "blockSize"], [233, 39, 239, 37], [233, 42, 239, 40], [233, 43, 239, 41], [233, 47, 239, 45, "img"], [233, 50, 239, 48], [233, 51, 239, 49, "height"], [233, 57, 239, 55], [234, 16, 240, 14, "width"], [234, 21, 240, 19], [234, 23, 240, 21, "blockSize"], [234, 32, 240, 30], [234, 35, 240, 33, "img"], [234, 38, 240, 36], [234, 39, 240, 37, "width"], [234, 44, 240, 42], [235, 16, 241, 14, "height"], [235, 22, 241, 20], [235, 24, 241, 22, "blockSize"], [235, 33, 241, 31], [235, 36, 241, 34, "img"], [235, 39, 241, 37], [235, 40, 241, 38, "height"], [236, 14, 242, 12], [236, 15, 242, 13], [237, 14, 243, 12, "confidence"], [237, 24, 243, 22], [237, 26, 243, 24], [237, 29, 243, 27], [237, 30, 243, 28], [238, 12, 244, 10], [238, 13, 244, 11], [238, 14, 244, 12], [239, 10, 245, 8], [240, 8, 246, 6], [241, 6, 247, 4], [243, 6, 249, 4], [244, 6, 250, 4], [244, 12, 250, 10, "mergedFaces"], [244, 23, 250, 21], [244, 26, 250, 24, "mergeFaceDetections"], [244, 45, 250, 43], [244, 46, 250, 44, "faces"], [244, 51, 250, 49], [244, 52, 250, 50], [245, 6, 251, 4, "console"], [245, 13, 251, 11], [245, 14, 251, 12, "log"], [245, 17, 251, 15], [245, 18, 251, 16], [245, 62, 251, 60, "faces"], [245, 67, 251, 65], [245, 68, 251, 66, "length"], [245, 74, 251, 72], [245, 91, 251, 89, "mergedFaces"], [245, 102, 251, 100], [245, 103, 251, 101, "length"], [245, 109, 251, 107], [245, 124, 251, 122], [245, 125, 251, 123], [246, 6, 252, 4], [246, 13, 252, 11, "mergedFaces"], [246, 24, 252, 22], [246, 25, 252, 23, "slice"], [246, 30, 252, 28], [246, 31, 252, 29], [246, 32, 252, 30], [246, 34, 252, 32], [246, 35, 252, 33], [246, 36, 252, 34], [246, 37, 252, 35], [246, 38, 252, 36], [247, 4, 253, 2], [247, 5, 253, 3], [248, 4, 255, 2], [248, 10, 255, 8, "analyzeRegionForFace"], [248, 30, 255, 28], [248, 33, 255, 31, "analyzeRegionForFace"], [248, 34, 255, 32, "data"], [248, 38, 255, 55], [248, 40, 255, 57, "startX"], [248, 46, 255, 71], [248, 48, 255, 73, "startY"], [248, 54, 255, 87], [248, 56, 255, 89, "size"], [248, 60, 255, 101], [248, 62, 255, 103, "imageWidth"], [248, 72, 255, 121], [248, 74, 255, 123, "imageHeight"], [248, 85, 255, 142], [248, 90, 255, 147], [249, 6, 256, 4], [249, 10, 256, 8, "skinPixels"], [249, 20, 256, 18], [249, 23, 256, 21], [249, 24, 256, 22], [250, 6, 257, 4], [250, 10, 257, 8, "totalPixels"], [250, 21, 257, 19], [250, 24, 257, 22], [250, 25, 257, 23], [251, 6, 258, 4], [251, 10, 258, 8, "totalBrightness"], [251, 25, 258, 23], [251, 28, 258, 26], [251, 29, 258, 27], [252, 6, 259, 4], [252, 10, 259, 8, "colorVariations"], [252, 25, 259, 23], [252, 28, 259, 26], [252, 29, 259, 27], [253, 6, 260, 4], [253, 10, 260, 8, "prevR"], [253, 15, 260, 13], [253, 18, 260, 16], [253, 19, 260, 17], [254, 8, 260, 19, "prevG"], [254, 13, 260, 24], [254, 16, 260, 27], [254, 17, 260, 28], [255, 8, 260, 30, "prevB"], [255, 13, 260, 35], [255, 16, 260, 38], [255, 17, 260, 39], [256, 6, 262, 4], [256, 11, 262, 9], [256, 15, 262, 13, "y"], [256, 16, 262, 14], [256, 19, 262, 17, "startY"], [256, 25, 262, 23], [256, 27, 262, 25, "y"], [256, 28, 262, 26], [256, 31, 262, 29, "startY"], [256, 37, 262, 35], [256, 40, 262, 38, "size"], [256, 44, 262, 42], [256, 48, 262, 46, "y"], [256, 49, 262, 47], [256, 52, 262, 50, "imageHeight"], [256, 63, 262, 61], [256, 65, 262, 63, "y"], [256, 66, 262, 64], [256, 68, 262, 66], [256, 70, 262, 68], [257, 8, 263, 6], [257, 13, 263, 11], [257, 17, 263, 15, "x"], [257, 18, 263, 16], [257, 21, 263, 19, "startX"], [257, 27, 263, 25], [257, 29, 263, 27, "x"], [257, 30, 263, 28], [257, 33, 263, 31, "startX"], [257, 39, 263, 37], [257, 42, 263, 40, "size"], [257, 46, 263, 44], [257, 50, 263, 48, "x"], [257, 51, 263, 49], [257, 54, 263, 52, "imageWidth"], [257, 64, 263, 62], [257, 66, 263, 64, "x"], [257, 67, 263, 65], [257, 69, 263, 67], [257, 71, 263, 69], [258, 10, 264, 8], [258, 16, 264, 14, "index"], [258, 21, 264, 19], [258, 24, 264, 22], [258, 25, 264, 23, "y"], [258, 26, 264, 24], [258, 29, 264, 27, "imageWidth"], [258, 39, 264, 37], [258, 42, 264, 40, "x"], [258, 43, 264, 41], [258, 47, 264, 45], [258, 48, 264, 46], [259, 10, 265, 8], [259, 16, 265, 14, "r"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 31, 265, 29], [260, 10, 266, 8], [260, 16, 266, 14, "g"], [260, 17, 266, 15], [260, 20, 266, 18, "data"], [260, 24, 266, 22], [260, 25, 266, 23, "index"], [260, 30, 266, 28], [260, 33, 266, 31], [260, 34, 266, 32], [260, 35, 266, 33], [261, 10, 267, 8], [261, 16, 267, 14, "b"], [261, 17, 267, 15], [261, 20, 267, 18, "data"], [261, 24, 267, 22], [261, 25, 267, 23, "index"], [261, 30, 267, 28], [261, 33, 267, 31], [261, 34, 267, 32], [261, 35, 267, 33], [263, 10, 269, 8], [264, 10, 270, 8], [264, 14, 270, 12, "isSkinTone"], [264, 24, 270, 22], [264, 25, 270, 23, "r"], [264, 26, 270, 24], [264, 28, 270, 26, "g"], [264, 29, 270, 27], [264, 31, 270, 29, "b"], [264, 32, 270, 30], [264, 33, 270, 31], [264, 35, 270, 33], [265, 12, 271, 10, "skinPixels"], [265, 22, 271, 20], [265, 24, 271, 22], [266, 10, 272, 8], [268, 10, 274, 8], [269, 10, 275, 8], [269, 16, 275, 14, "brightness"], [269, 26, 275, 24], [269, 29, 275, 27], [269, 30, 275, 28, "r"], [269, 31, 275, 29], [269, 34, 275, 32, "g"], [269, 35, 275, 33], [269, 38, 275, 36, "b"], [269, 39, 275, 37], [269, 44, 275, 42], [269, 45, 275, 43], [269, 48, 275, 46], [269, 51, 275, 49], [269, 52, 275, 50], [270, 10, 276, 8, "totalBrightness"], [270, 25, 276, 23], [270, 29, 276, 27, "brightness"], [270, 39, 276, 37], [272, 10, 278, 8], [273, 10, 279, 8], [273, 14, 279, 12, "totalPixels"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 29, 279, 27], [273, 31, 279, 29], [274, 12, 280, 10], [274, 18, 280, 16, "colorDiff"], [274, 27, 280, 25], [274, 30, 280, 28, "Math"], [274, 34, 280, 32], [274, 35, 280, 33, "abs"], [274, 38, 280, 36], [274, 39, 280, 37, "r"], [274, 40, 280, 38], [274, 43, 280, 41, "prevR"], [274, 48, 280, 46], [274, 49, 280, 47], [274, 52, 280, 50, "Math"], [274, 56, 280, 54], [274, 57, 280, 55, "abs"], [274, 60, 280, 58], [274, 61, 280, 59, "g"], [274, 62, 280, 60], [274, 65, 280, 63, "prevG"], [274, 70, 280, 68], [274, 71, 280, 69], [274, 74, 280, 72, "Math"], [274, 78, 280, 76], [274, 79, 280, 77, "abs"], [274, 82, 280, 80], [274, 83, 280, 81, "b"], [274, 84, 280, 82], [274, 87, 280, 85, "prevB"], [274, 92, 280, 90], [274, 93, 280, 91], [275, 12, 281, 10], [275, 16, 281, 14, "colorDiff"], [275, 25, 281, 23], [275, 28, 281, 26], [275, 30, 281, 28], [275, 32, 281, 30], [276, 14, 281, 32], [277, 14, 282, 12, "colorVariations"], [277, 29, 282, 27], [277, 31, 282, 29], [278, 12, 283, 10], [279, 10, 284, 8], [280, 10, 286, 8, "prevR"], [280, 15, 286, 13], [280, 18, 286, 16, "r"], [280, 19, 286, 17], [281, 10, 286, 19, "prevG"], [281, 15, 286, 24], [281, 18, 286, 27, "g"], [281, 19, 286, 28], [282, 10, 286, 30, "prevB"], [282, 15, 286, 35], [282, 18, 286, 38, "b"], [282, 19, 286, 39], [283, 10, 287, 8, "totalPixels"], [283, 21, 287, 19], [283, 23, 287, 21], [284, 8, 288, 6], [285, 6, 289, 4], [286, 6, 291, 4], [286, 13, 291, 11], [287, 8, 292, 6, "skinRatio"], [287, 17, 292, 15], [287, 19, 292, 17, "skinPixels"], [287, 29, 292, 27], [287, 32, 292, 30, "totalPixels"], [287, 43, 292, 41], [288, 8, 293, 6, "brightness"], [288, 18, 293, 16], [288, 20, 293, 18, "totalBrightness"], [288, 35, 293, 33], [288, 38, 293, 36, "totalPixels"], [288, 49, 293, 47], [289, 8, 294, 6, "variation"], [289, 17, 294, 15], [289, 19, 294, 17, "colorVariations"], [289, 34, 294, 32], [289, 37, 294, 35, "totalPixels"], [289, 48, 294, 46], [290, 8, 295, 6, "hasVariation"], [290, 20, 295, 18], [290, 22, 295, 20, "colorVariations"], [290, 37, 295, 35], [290, 40, 295, 38, "totalPixels"], [290, 51, 295, 49], [290, 54, 295, 52], [290, 57, 295, 55], [290, 58, 295, 56], [291, 6, 296, 4], [291, 7, 296, 5], [292, 4, 297, 2], [292, 5, 297, 3], [293, 4, 299, 2], [293, 10, 299, 8, "isSkinTone"], [293, 20, 299, 18], [293, 23, 299, 21, "isSkinTone"], [293, 24, 299, 22, "r"], [293, 25, 299, 31], [293, 27, 299, 33, "g"], [293, 28, 299, 42], [293, 30, 299, 44, "b"], [293, 31, 299, 53], [293, 36, 299, 58], [294, 6, 300, 4], [295, 6, 301, 4], [295, 13, 302, 6, "r"], [295, 14, 302, 7], [295, 17, 302, 10], [295, 19, 302, 12], [295, 23, 302, 16, "g"], [295, 24, 302, 17], [295, 27, 302, 20], [295, 29, 302, 22], [295, 33, 302, 26, "b"], [295, 34, 302, 27], [295, 37, 302, 30], [295, 39, 302, 32], [295, 43, 303, 6, "r"], [295, 44, 303, 7], [295, 47, 303, 10, "g"], [295, 48, 303, 11], [295, 52, 303, 15, "r"], [295, 53, 303, 16], [295, 56, 303, 19, "b"], [295, 57, 303, 20], [295, 61, 304, 6, "Math"], [295, 65, 304, 10], [295, 66, 304, 11, "abs"], [295, 69, 304, 14], [295, 70, 304, 15, "r"], [295, 71, 304, 16], [295, 74, 304, 19, "g"], [295, 75, 304, 20], [295, 76, 304, 21], [295, 79, 304, 24], [295, 81, 304, 26], [295, 85, 305, 6, "Math"], [295, 89, 305, 10], [295, 90, 305, 11, "max"], [295, 93, 305, 14], [295, 94, 305, 15, "r"], [295, 95, 305, 16], [295, 97, 305, 18, "g"], [295, 98, 305, 19], [295, 100, 305, 21, "b"], [295, 101, 305, 22], [295, 102, 305, 23], [295, 105, 305, 26, "Math"], [295, 109, 305, 30], [295, 110, 305, 31, "min"], [295, 113, 305, 34], [295, 114, 305, 35, "r"], [295, 115, 305, 36], [295, 117, 305, 38, "g"], [295, 118, 305, 39], [295, 120, 305, 41, "b"], [295, 121, 305, 42], [295, 122, 305, 43], [295, 125, 305, 46], [295, 127, 305, 48], [296, 4, 307, 2], [296, 5, 307, 3], [297, 4, 309, 2], [297, 10, 309, 8, "mergeFaceDetections"], [297, 29, 309, 27], [297, 32, 309, 31, "faces"], [297, 37, 309, 43], [297, 41, 309, 48], [298, 6, 310, 4], [298, 10, 310, 8, "faces"], [298, 15, 310, 13], [298, 16, 310, 14, "length"], [298, 22, 310, 20], [298, 26, 310, 24], [298, 27, 310, 25], [298, 29, 310, 27], [298, 36, 310, 34, "faces"], [298, 41, 310, 39], [299, 6, 312, 4], [299, 12, 312, 10, "merged"], [299, 18, 312, 16], [299, 21, 312, 19], [299, 23, 312, 21], [300, 6, 313, 4], [300, 12, 313, 10, "used"], [300, 16, 313, 14], [300, 19, 313, 17], [300, 23, 313, 21, "Set"], [300, 26, 313, 24], [300, 27, 313, 25], [300, 28, 313, 26], [301, 6, 315, 4], [301, 11, 315, 9], [301, 15, 315, 13, "i"], [301, 16, 315, 14], [301, 19, 315, 17], [301, 20, 315, 18], [301, 22, 315, 20, "i"], [301, 23, 315, 21], [301, 26, 315, 24, "faces"], [301, 31, 315, 29], [301, 32, 315, 30, "length"], [301, 38, 315, 36], [301, 40, 315, 38, "i"], [301, 41, 315, 39], [301, 43, 315, 41], [301, 45, 315, 43], [302, 8, 316, 6], [302, 12, 316, 10, "used"], [302, 16, 316, 14], [302, 17, 316, 15, "has"], [302, 20, 316, 18], [302, 21, 316, 19, "i"], [302, 22, 316, 20], [302, 23, 316, 21], [302, 25, 316, 23], [303, 8, 318, 6], [303, 12, 318, 10, "currentFace"], [303, 23, 318, 21], [303, 26, 318, 24, "faces"], [303, 31, 318, 29], [303, 32, 318, 30, "i"], [303, 33, 318, 31], [303, 34, 318, 32], [304, 8, 319, 6, "used"], [304, 12, 319, 10], [304, 13, 319, 11, "add"], [304, 16, 319, 14], [304, 17, 319, 15, "i"], [304, 18, 319, 16], [304, 19, 319, 17], [306, 8, 321, 6], [307, 8, 322, 6], [307, 13, 322, 11], [307, 17, 322, 15, "j"], [307, 18, 322, 16], [307, 21, 322, 19, "i"], [307, 22, 322, 20], [307, 25, 322, 23], [307, 26, 322, 24], [307, 28, 322, 26, "j"], [307, 29, 322, 27], [307, 32, 322, 30, "faces"], [307, 37, 322, 35], [307, 38, 322, 36, "length"], [307, 44, 322, 42], [307, 46, 322, 44, "j"], [307, 47, 322, 45], [307, 49, 322, 47], [307, 51, 322, 49], [308, 10, 323, 8], [308, 14, 323, 12, "used"], [308, 18, 323, 16], [308, 19, 323, 17, "has"], [308, 22, 323, 20], [308, 23, 323, 21, "j"], [308, 24, 323, 22], [308, 25, 323, 23], [308, 27, 323, 25], [309, 10, 325, 8], [309, 16, 325, 14, "overlap"], [309, 23, 325, 21], [309, 26, 325, 24, "calculateOverlap"], [309, 42, 325, 40], [309, 43, 325, 41, "currentFace"], [309, 54, 325, 52], [309, 55, 325, 53, "boundingBox"], [309, 66, 325, 64], [309, 68, 325, 66, "faces"], [309, 73, 325, 71], [309, 74, 325, 72, "j"], [309, 75, 325, 73], [309, 76, 325, 74], [309, 77, 325, 75, "boundingBox"], [309, 88, 325, 86], [309, 89, 325, 87], [310, 10, 326, 8], [310, 14, 326, 12, "overlap"], [310, 21, 326, 19], [310, 24, 326, 22], [310, 27, 326, 25], [310, 29, 326, 27], [311, 12, 326, 29], [312, 12, 327, 10], [313, 12, 328, 10, "currentFace"], [313, 23, 328, 21], [313, 26, 328, 24, "mergeTwoFaces"], [313, 39, 328, 37], [313, 40, 328, 38, "currentFace"], [313, 51, 328, 49], [313, 53, 328, 51, "faces"], [313, 58, 328, 56], [313, 59, 328, 57, "j"], [313, 60, 328, 58], [313, 61, 328, 59], [313, 62, 328, 60], [314, 12, 329, 10, "used"], [314, 16, 329, 14], [314, 17, 329, 15, "add"], [314, 20, 329, 18], [314, 21, 329, 19, "j"], [314, 22, 329, 20], [314, 23, 329, 21], [315, 10, 330, 8], [316, 8, 331, 6], [317, 8, 333, 6, "merged"], [317, 14, 333, 12], [317, 15, 333, 13, "push"], [317, 19, 333, 17], [317, 20, 333, 18, "currentFace"], [317, 31, 333, 29], [317, 32, 333, 30], [318, 6, 334, 4], [319, 6, 336, 4], [319, 13, 336, 11, "merged"], [319, 19, 336, 17], [320, 4, 337, 2], [320, 5, 337, 3], [321, 4, 339, 2], [321, 10, 339, 8, "calculateOverlap"], [321, 26, 339, 24], [321, 29, 339, 27, "calculateOverlap"], [321, 30, 339, 28, "box1"], [321, 34, 339, 37], [321, 36, 339, 39, "box2"], [321, 40, 339, 48], [321, 45, 339, 53], [322, 6, 340, 4], [322, 12, 340, 10, "x1"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "max"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y1"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "max"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 342, 4], [324, 12, 342, 10, "x2"], [324, 14, 342, 12], [324, 17, 342, 15, "Math"], [324, 21, 342, 19], [324, 22, 342, 20, "min"], [324, 25, 342, 23], [324, 26, 342, 24, "box1"], [324, 30, 342, 28], [324, 31, 342, 29, "xCenter"], [324, 38, 342, 36], [324, 41, 342, 39, "box1"], [324, 45, 342, 43], [324, 46, 342, 44, "width"], [324, 51, 342, 49], [324, 54, 342, 50], [324, 55, 342, 51], [324, 57, 342, 53, "box2"], [324, 61, 342, 57], [324, 62, 342, 58, "xCenter"], [324, 69, 342, 65], [324, 72, 342, 68, "box2"], [324, 76, 342, 72], [324, 77, 342, 73, "width"], [324, 82, 342, 78], [324, 85, 342, 79], [324, 86, 342, 80], [324, 87, 342, 81], [325, 6, 343, 4], [325, 12, 343, 10, "y2"], [325, 14, 343, 12], [325, 17, 343, 15, "Math"], [325, 21, 343, 19], [325, 22, 343, 20, "min"], [325, 25, 343, 23], [325, 26, 343, 24, "box1"], [325, 30, 343, 28], [325, 31, 343, 29, "yCenter"], [325, 38, 343, 36], [325, 41, 343, 39, "box1"], [325, 45, 343, 43], [325, 46, 343, 44, "height"], [325, 52, 343, 50], [325, 55, 343, 51], [325, 56, 343, 52], [325, 58, 343, 54, "box2"], [325, 62, 343, 58], [325, 63, 343, 59, "yCenter"], [325, 70, 343, 66], [325, 73, 343, 69, "box2"], [325, 77, 343, 73], [325, 78, 343, 74, "height"], [325, 84, 343, 80], [325, 87, 343, 81], [325, 88, 343, 82], [325, 89, 343, 83], [326, 6, 345, 4], [326, 10, 345, 8, "x2"], [326, 12, 345, 10], [326, 16, 345, 14, "x1"], [326, 18, 345, 16], [326, 22, 345, 20, "y2"], [326, 24, 345, 22], [326, 28, 345, 26, "y1"], [326, 30, 345, 28], [326, 32, 345, 30], [326, 39, 345, 37], [326, 40, 345, 38], [327, 6, 347, 4], [327, 12, 347, 10, "overlapArea"], [327, 23, 347, 21], [327, 26, 347, 24], [327, 27, 347, 25, "x2"], [327, 29, 347, 27], [327, 32, 347, 30, "x1"], [327, 34, 347, 32], [327, 39, 347, 37, "y2"], [327, 41, 347, 39], [327, 44, 347, 42, "y1"], [327, 46, 347, 44], [327, 47, 347, 45], [328, 6, 348, 4], [328, 12, 348, 10, "box1Area"], [328, 20, 348, 18], [328, 23, 348, 21, "box1"], [328, 27, 348, 25], [328, 28, 348, 26, "width"], [328, 33, 348, 31], [328, 36, 348, 34, "box1"], [328, 40, 348, 38], [328, 41, 348, 39, "height"], [328, 47, 348, 45], [329, 6, 349, 4], [329, 12, 349, 10, "box2Area"], [329, 20, 349, 18], [329, 23, 349, 21, "box2"], [329, 27, 349, 25], [329, 28, 349, 26, "width"], [329, 33, 349, 31], [329, 36, 349, 34, "box2"], [329, 40, 349, 38], [329, 41, 349, 39, "height"], [329, 47, 349, 45], [330, 6, 351, 4], [330, 13, 351, 11, "overlapArea"], [330, 24, 351, 22], [330, 27, 351, 25, "Math"], [330, 31, 351, 29], [330, 32, 351, 30, "min"], [330, 35, 351, 33], [330, 36, 351, 34, "box1Area"], [330, 44, 351, 42], [330, 46, 351, 44, "box2Area"], [330, 54, 351, 52], [330, 55, 351, 53], [331, 4, 352, 2], [331, 5, 352, 3], [332, 4, 354, 2], [332, 10, 354, 8, "mergeTwoFaces"], [332, 23, 354, 21], [332, 26, 354, 24, "mergeTwoFaces"], [332, 27, 354, 25, "face1"], [332, 32, 354, 35], [332, 34, 354, 37, "face2"], [332, 39, 354, 47], [332, 44, 354, 52], [333, 6, 355, 4], [333, 12, 355, 10, "box1"], [333, 16, 355, 14], [333, 19, 355, 17, "face1"], [333, 24, 355, 22], [333, 25, 355, 23, "boundingBox"], [333, 36, 355, 34], [334, 6, 356, 4], [334, 12, 356, 10, "box2"], [334, 16, 356, 14], [334, 19, 356, 17, "face2"], [334, 24, 356, 22], [334, 25, 356, 23, "boundingBox"], [334, 36, 356, 34], [335, 6, 358, 4], [335, 12, 358, 10, "left"], [335, 16, 358, 14], [335, 19, 358, 17, "Math"], [335, 23, 358, 21], [335, 24, 358, 22, "min"], [335, 27, 358, 25], [335, 28, 358, 26, "box1"], [335, 32, 358, 30], [335, 33, 358, 31, "xCenter"], [335, 40, 358, 38], [335, 43, 358, 41, "box1"], [335, 47, 358, 45], [335, 48, 358, 46, "width"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "xCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "width"], [335, 84, 358, 80], [335, 87, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [336, 6, 359, 4], [336, 12, 359, 10, "right"], [336, 17, 359, 15], [336, 20, 359, 18, "Math"], [336, 24, 359, 22], [336, 25, 359, 23, "max"], [336, 28, 359, 26], [336, 29, 359, 27, "box1"], [336, 33, 359, 31], [336, 34, 359, 32, "xCenter"], [336, 41, 359, 39], [336, 44, 359, 42, "box1"], [336, 48, 359, 46], [336, 49, 359, 47, "width"], [336, 54, 359, 52], [336, 57, 359, 53], [336, 58, 359, 54], [336, 60, 359, 56, "box2"], [336, 64, 359, 60], [336, 65, 359, 61, "xCenter"], [336, 72, 359, 68], [336, 75, 359, 71, "box2"], [336, 79, 359, 75], [336, 80, 359, 76, "width"], [336, 85, 359, 81], [336, 88, 359, 82], [336, 89, 359, 83], [336, 90, 359, 84], [337, 6, 360, 4], [337, 12, 360, 10, "top"], [337, 15, 360, 13], [337, 18, 360, 16, "Math"], [337, 22, 360, 20], [337, 23, 360, 21, "min"], [337, 26, 360, 24], [337, 27, 360, 25, "box1"], [337, 31, 360, 29], [337, 32, 360, 30, "yCenter"], [337, 39, 360, 37], [337, 42, 360, 40, "box1"], [337, 46, 360, 44], [337, 47, 360, 45, "height"], [337, 53, 360, 51], [337, 56, 360, 52], [337, 57, 360, 53], [337, 59, 360, 55, "box2"], [337, 63, 360, 59], [337, 64, 360, 60, "yCenter"], [337, 71, 360, 67], [337, 74, 360, 70, "box2"], [337, 78, 360, 74], [337, 79, 360, 75, "height"], [337, 85, 360, 81], [337, 88, 360, 82], [337, 89, 360, 83], [337, 90, 360, 84], [338, 6, 361, 4], [338, 12, 361, 10, "bottom"], [338, 18, 361, 16], [338, 21, 361, 19, "Math"], [338, 25, 361, 23], [338, 26, 361, 24, "max"], [338, 29, 361, 27], [338, 30, 361, 28, "box1"], [338, 34, 361, 32], [338, 35, 361, 33, "yCenter"], [338, 42, 361, 40], [338, 45, 361, 43, "box1"], [338, 49, 361, 47], [338, 50, 361, 48, "height"], [338, 56, 361, 54], [338, 59, 361, 55], [338, 60, 361, 56], [338, 62, 361, 58, "box2"], [338, 66, 361, 62], [338, 67, 361, 63, "yCenter"], [338, 74, 361, 70], [338, 77, 361, 73, "box2"], [338, 81, 361, 77], [338, 82, 361, 78, "height"], [338, 88, 361, 84], [338, 91, 361, 85], [338, 92, 361, 86], [338, 93, 361, 87], [339, 6, 363, 4], [339, 13, 363, 11], [340, 8, 364, 6, "boundingBox"], [340, 19, 364, 17], [340, 21, 364, 19], [341, 10, 365, 8, "xCenter"], [341, 17, 365, 15], [341, 19, 365, 17], [341, 20, 365, 18, "left"], [341, 24, 365, 22], [341, 27, 365, 25, "right"], [341, 32, 365, 30], [341, 36, 365, 34], [341, 37, 365, 35], [342, 10, 366, 8, "yCenter"], [342, 17, 366, 15], [342, 19, 366, 17], [342, 20, 366, 18, "top"], [342, 23, 366, 21], [342, 26, 366, 24, "bottom"], [342, 32, 366, 30], [342, 36, 366, 34], [342, 37, 366, 35], [343, 10, 367, 8, "width"], [343, 15, 367, 13], [343, 17, 367, 15, "right"], [343, 22, 367, 20], [343, 25, 367, 23, "left"], [343, 29, 367, 27], [344, 10, 368, 8, "height"], [344, 16, 368, 14], [344, 18, 368, 16, "bottom"], [344, 24, 368, 22], [344, 27, 368, 25, "top"], [345, 8, 369, 6], [346, 6, 370, 4], [346, 7, 370, 5], [347, 4, 371, 2], [347, 5, 371, 3], [349, 4, 373, 2], [350, 4, 374, 2], [350, 10, 374, 8, "applyStrongBlur"], [350, 25, 374, 23], [350, 28, 374, 26, "applyStrongBlur"], [350, 29, 374, 27, "ctx"], [350, 32, 374, 56], [350, 34, 374, 58, "x"], [350, 35, 374, 67], [350, 37, 374, 69, "y"], [350, 38, 374, 78], [350, 40, 374, 80, "width"], [350, 45, 374, 93], [350, 47, 374, 95, "height"], [350, 53, 374, 109], [350, 58, 374, 114], [351, 6, 375, 4], [352, 6, 376, 4], [352, 12, 376, 10, "canvasWidth"], [352, 23, 376, 21], [352, 26, 376, 24, "ctx"], [352, 29, 376, 27], [352, 30, 376, 28, "canvas"], [352, 36, 376, 34], [352, 37, 376, 35, "width"], [352, 42, 376, 40], [353, 6, 377, 4], [353, 12, 377, 10, "canvasHeight"], [353, 24, 377, 22], [353, 27, 377, 25, "ctx"], [353, 30, 377, 28], [353, 31, 377, 29, "canvas"], [353, 37, 377, 35], [353, 38, 377, 36, "height"], [353, 44, 377, 42], [354, 6, 379, 4], [354, 12, 379, 10, "clampedX"], [354, 20, 379, 18], [354, 23, 379, 21, "Math"], [354, 27, 379, 25], [354, 28, 379, 26, "max"], [354, 31, 379, 29], [354, 32, 379, 30], [354, 33, 379, 31], [354, 35, 379, 33, "Math"], [354, 39, 379, 37], [354, 40, 379, 38, "min"], [354, 43, 379, 41], [354, 44, 379, 42, "Math"], [354, 48, 379, 46], [354, 49, 379, 47, "floor"], [354, 54, 379, 52], [354, 55, 379, 53, "x"], [354, 56, 379, 54], [354, 57, 379, 55], [354, 59, 379, 57, "canvasWidth"], [354, 70, 379, 68], [354, 73, 379, 71], [354, 74, 379, 72], [354, 75, 379, 73], [354, 76, 379, 74], [355, 6, 380, 4], [355, 12, 380, 10, "clampedY"], [355, 20, 380, 18], [355, 23, 380, 21, "Math"], [355, 27, 380, 25], [355, 28, 380, 26, "max"], [355, 31, 380, 29], [355, 32, 380, 30], [355, 33, 380, 31], [355, 35, 380, 33, "Math"], [355, 39, 380, 37], [355, 40, 380, 38, "min"], [355, 43, 380, 41], [355, 44, 380, 42, "Math"], [355, 48, 380, 46], [355, 49, 380, 47, "floor"], [355, 54, 380, 52], [355, 55, 380, 53, "y"], [355, 56, 380, 54], [355, 57, 380, 55], [355, 59, 380, 57, "canvasHeight"], [355, 71, 380, 69], [355, 74, 380, 72], [355, 75, 380, 73], [355, 76, 380, 74], [355, 77, 380, 75], [356, 6, 381, 4], [356, 12, 381, 10, "<PERSON><PERSON><PERSON><PERSON>"], [356, 24, 381, 22], [356, 27, 381, 25, "Math"], [356, 31, 381, 29], [356, 32, 381, 30, "min"], [356, 35, 381, 33], [356, 36, 381, 34, "Math"], [356, 40, 381, 38], [356, 41, 381, 39, "floor"], [356, 46, 381, 44], [356, 47, 381, 45, "width"], [356, 52, 381, 50], [356, 53, 381, 51], [356, 55, 381, 53, "canvasWidth"], [356, 66, 381, 64], [356, 69, 381, 67, "clampedX"], [356, 77, 381, 75], [356, 78, 381, 76], [357, 6, 382, 4], [357, 12, 382, 10, "clampedHeight"], [357, 25, 382, 23], [357, 28, 382, 26, "Math"], [357, 32, 382, 30], [357, 33, 382, 31, "min"], [357, 36, 382, 34], [357, 37, 382, 35, "Math"], [357, 41, 382, 39], [357, 42, 382, 40, "floor"], [357, 47, 382, 45], [357, 48, 382, 46, "height"], [357, 54, 382, 52], [357, 55, 382, 53], [357, 57, 382, 55, "canvasHeight"], [357, 69, 382, 67], [357, 72, 382, 70, "clampedY"], [357, 80, 382, 78], [357, 81, 382, 79], [358, 6, 384, 4], [358, 10, 384, 8, "<PERSON><PERSON><PERSON><PERSON>"], [358, 22, 384, 20], [358, 26, 384, 24], [358, 27, 384, 25], [358, 31, 384, 29, "clampedHeight"], [358, 44, 384, 42], [358, 48, 384, 46], [358, 49, 384, 47], [358, 51, 384, 49], [359, 8, 385, 6, "console"], [359, 15, 385, 13], [359, 16, 385, 14, "warn"], [359, 20, 385, 18], [359, 21, 385, 19], [359, 73, 385, 71], [359, 75, 385, 73], [360, 10, 386, 8, "original"], [360, 18, 386, 16], [360, 20, 386, 18], [361, 12, 386, 20, "x"], [361, 13, 386, 21], [362, 12, 386, 23, "y"], [362, 13, 386, 24], [363, 12, 386, 26, "width"], [363, 17, 386, 31], [364, 12, 386, 33, "height"], [365, 10, 386, 40], [365, 11, 386, 41], [366, 10, 387, 8, "canvas"], [366, 16, 387, 14], [366, 18, 387, 16], [367, 12, 387, 18, "width"], [367, 17, 387, 23], [367, 19, 387, 25, "canvasWidth"], [367, 30, 387, 36], [368, 12, 387, 38, "height"], [368, 18, 387, 44], [368, 20, 387, 46, "canvasHeight"], [369, 10, 387, 59], [369, 11, 387, 60], [370, 10, 388, 8, "clamped"], [370, 17, 388, 15], [370, 19, 388, 17], [371, 12, 388, 19, "x"], [371, 13, 388, 20], [371, 15, 388, 22, "clampedX"], [371, 23, 388, 30], [372, 12, 388, 32, "y"], [372, 13, 388, 33], [372, 15, 388, 35, "clampedY"], [372, 23, 388, 43], [373, 12, 388, 45, "width"], [373, 17, 388, 50], [373, 19, 388, 52, "<PERSON><PERSON><PERSON><PERSON>"], [373, 31, 388, 64], [374, 12, 388, 66, "height"], [374, 18, 388, 72], [374, 20, 388, 74, "clampedHeight"], [375, 10, 388, 88], [376, 8, 389, 6], [376, 9, 389, 7], [376, 10, 389, 8], [377, 8, 390, 6], [378, 6, 391, 4], [380, 6, 393, 4], [381, 6, 394, 4], [381, 12, 394, 10, "imageData"], [381, 21, 394, 19], [381, 24, 394, 22, "ctx"], [381, 27, 394, 25], [381, 28, 394, 26, "getImageData"], [381, 40, 394, 38], [381, 41, 394, 39, "clampedX"], [381, 49, 394, 47], [381, 51, 394, 49, "clampedY"], [381, 59, 394, 57], [381, 61, 394, 59, "<PERSON><PERSON><PERSON><PERSON>"], [381, 73, 394, 71], [381, 75, 394, 73, "clampedHeight"], [381, 88, 394, 86], [381, 89, 394, 87], [382, 6, 395, 4], [382, 12, 395, 10, "data"], [382, 16, 395, 14], [382, 19, 395, 17, "imageData"], [382, 28, 395, 26], [382, 29, 395, 27, "data"], [382, 33, 395, 31], [384, 6, 397, 4], [385, 6, 398, 4], [385, 12, 398, 10, "pixelSize"], [385, 21, 398, 19], [385, 24, 398, 22, "Math"], [385, 28, 398, 26], [385, 29, 398, 27, "max"], [385, 32, 398, 30], [385, 33, 398, 31], [385, 35, 398, 33], [385, 37, 398, 35, "Math"], [385, 41, 398, 39], [385, 42, 398, 40, "min"], [385, 45, 398, 43], [385, 46, 398, 44, "<PERSON><PERSON><PERSON><PERSON>"], [385, 58, 398, 56], [385, 60, 398, 58, "clampedHeight"], [385, 73, 398, 71], [385, 74, 398, 72], [385, 77, 398, 75], [385, 78, 398, 76], [385, 79, 398, 77], [386, 6, 400, 4], [386, 11, 400, 9], [386, 15, 400, 13, "py"], [386, 17, 400, 15], [386, 20, 400, 18], [386, 21, 400, 19], [386, 23, 400, 21, "py"], [386, 25, 400, 23], [386, 28, 400, 26, "clampedHeight"], [386, 41, 400, 39], [386, 43, 400, 41, "py"], [386, 45, 400, 43], [386, 49, 400, 47, "pixelSize"], [386, 58, 400, 56], [386, 60, 400, 58], [387, 8, 401, 6], [387, 13, 401, 11], [387, 17, 401, 15, "px"], [387, 19, 401, 17], [387, 22, 401, 20], [387, 23, 401, 21], [387, 25, 401, 23, "px"], [387, 27, 401, 25], [387, 30, 401, 28, "<PERSON><PERSON><PERSON><PERSON>"], [387, 42, 401, 40], [387, 44, 401, 42, "px"], [387, 46, 401, 44], [387, 50, 401, 48, "pixelSize"], [387, 59, 401, 57], [387, 61, 401, 59], [388, 10, 402, 8], [389, 10, 403, 8], [389, 14, 403, 12, "r"], [389, 15, 403, 13], [389, 18, 403, 16], [389, 19, 403, 17], [390, 12, 403, 19, "g"], [390, 13, 403, 20], [390, 16, 403, 23], [390, 17, 403, 24], [391, 12, 403, 26, "b"], [391, 13, 403, 27], [391, 16, 403, 30], [391, 17, 403, 31], [392, 12, 403, 33, "count"], [392, 17, 403, 38], [392, 20, 403, 41], [392, 21, 403, 42], [393, 10, 405, 8], [393, 15, 405, 13], [393, 19, 405, 17, "dy"], [393, 21, 405, 19], [393, 24, 405, 22], [393, 25, 405, 23], [393, 27, 405, 25, "dy"], [393, 29, 405, 27], [393, 32, 405, 30, "pixelSize"], [393, 41, 405, 39], [393, 45, 405, 43, "py"], [393, 47, 405, 45], [393, 50, 405, 48, "dy"], [393, 52, 405, 50], [393, 55, 405, 53, "clampedHeight"], [393, 68, 405, 66], [393, 70, 405, 68, "dy"], [393, 72, 405, 70], [393, 74, 405, 72], [393, 76, 405, 74], [394, 12, 406, 10], [394, 17, 406, 15], [394, 21, 406, 19, "dx"], [394, 23, 406, 21], [394, 26, 406, 24], [394, 27, 406, 25], [394, 29, 406, 27, "dx"], [394, 31, 406, 29], [394, 34, 406, 32, "pixelSize"], [394, 43, 406, 41], [394, 47, 406, 45, "px"], [394, 49, 406, 47], [394, 52, 406, 50, "dx"], [394, 54, 406, 52], [394, 57, 406, 55, "<PERSON><PERSON><PERSON><PERSON>"], [394, 69, 406, 67], [394, 71, 406, 69, "dx"], [394, 73, 406, 71], [394, 75, 406, 73], [394, 77, 406, 75], [395, 14, 407, 12], [395, 20, 407, 18, "index"], [395, 25, 407, 23], [395, 28, 407, 26], [395, 29, 407, 27], [395, 30, 407, 28, "py"], [395, 32, 407, 30], [395, 35, 407, 33, "dy"], [395, 37, 407, 35], [395, 41, 407, 39, "<PERSON><PERSON><PERSON><PERSON>"], [395, 53, 407, 51], [395, 57, 407, 55, "px"], [395, 59, 407, 57], [395, 62, 407, 60, "dx"], [395, 64, 407, 62], [395, 65, 407, 63], [395, 69, 407, 67], [395, 70, 407, 68], [396, 14, 408, 12, "r"], [396, 15, 408, 13], [396, 19, 408, 17, "data"], [396, 23, 408, 21], [396, 24, 408, 22, "index"], [396, 29, 408, 27], [396, 30, 408, 28], [397, 14, 409, 12, "g"], [397, 15, 409, 13], [397, 19, 409, 17, "data"], [397, 23, 409, 21], [397, 24, 409, 22, "index"], [397, 29, 409, 27], [397, 32, 409, 30], [397, 33, 409, 31], [397, 34, 409, 32], [398, 14, 410, 12, "b"], [398, 15, 410, 13], [398, 19, 410, 17, "data"], [398, 23, 410, 21], [398, 24, 410, 22, "index"], [398, 29, 410, 27], [398, 32, 410, 30], [398, 33, 410, 31], [398, 34, 410, 32], [399, 14, 411, 12, "count"], [399, 19, 411, 17], [399, 21, 411, 19], [400, 12, 412, 10], [401, 10, 413, 8], [402, 10, 415, 8], [402, 14, 415, 12, "count"], [402, 19, 415, 17], [402, 22, 415, 20], [402, 23, 415, 21], [402, 25, 415, 23], [403, 12, 416, 10, "r"], [403, 13, 416, 11], [403, 16, 416, 14, "Math"], [403, 20, 416, 18], [403, 21, 416, 19, "floor"], [403, 26, 416, 24], [403, 27, 416, 25, "r"], [403, 28, 416, 26], [403, 31, 416, 29, "count"], [403, 36, 416, 34], [403, 37, 416, 35], [404, 12, 417, 10, "g"], [404, 13, 417, 11], [404, 16, 417, 14, "Math"], [404, 20, 417, 18], [404, 21, 417, 19, "floor"], [404, 26, 417, 24], [404, 27, 417, 25, "g"], [404, 28, 417, 26], [404, 31, 417, 29, "count"], [404, 36, 417, 34], [404, 37, 417, 35], [405, 12, 418, 10, "b"], [405, 13, 418, 11], [405, 16, 418, 14, "Math"], [405, 20, 418, 18], [405, 21, 418, 19, "floor"], [405, 26, 418, 24], [405, 27, 418, 25, "b"], [405, 28, 418, 26], [405, 31, 418, 29, "count"], [405, 36, 418, 34], [405, 37, 418, 35], [407, 12, 420, 10], [408, 12, 421, 10], [408, 17, 421, 15], [408, 21, 421, 19, "dy"], [408, 23, 421, 21], [408, 26, 421, 24], [408, 27, 421, 25], [408, 29, 421, 27, "dy"], [408, 31, 421, 29], [408, 34, 421, 32, "pixelSize"], [408, 43, 421, 41], [408, 47, 421, 45, "py"], [408, 49, 421, 47], [408, 52, 421, 50, "dy"], [408, 54, 421, 52], [408, 57, 421, 55, "clampedHeight"], [408, 70, 421, 68], [408, 72, 421, 70, "dy"], [408, 74, 421, 72], [408, 76, 421, 74], [408, 78, 421, 76], [409, 14, 422, 12], [409, 19, 422, 17], [409, 23, 422, 21, "dx"], [409, 25, 422, 23], [409, 28, 422, 26], [409, 29, 422, 27], [409, 31, 422, 29, "dx"], [409, 33, 422, 31], [409, 36, 422, 34, "pixelSize"], [409, 45, 422, 43], [409, 49, 422, 47, "px"], [409, 51, 422, 49], [409, 54, 422, 52, "dx"], [409, 56, 422, 54], [409, 59, 422, 57, "<PERSON><PERSON><PERSON><PERSON>"], [409, 71, 422, 69], [409, 73, 422, 71, "dx"], [409, 75, 422, 73], [409, 77, 422, 75], [409, 79, 422, 77], [410, 16, 423, 14], [410, 22, 423, 20, "index"], [410, 27, 423, 25], [410, 30, 423, 28], [410, 31, 423, 29], [410, 32, 423, 30, "py"], [410, 34, 423, 32], [410, 37, 423, 35, "dy"], [410, 39, 423, 37], [410, 43, 423, 41, "<PERSON><PERSON><PERSON><PERSON>"], [410, 55, 423, 53], [410, 59, 423, 57, "px"], [410, 61, 423, 59], [410, 64, 423, 62, "dx"], [410, 66, 423, 64], [410, 67, 423, 65], [410, 71, 423, 69], [410, 72, 423, 70], [411, 16, 424, 14, "data"], [411, 20, 424, 18], [411, 21, 424, 19, "index"], [411, 26, 424, 24], [411, 27, 424, 25], [411, 30, 424, 28, "r"], [411, 31, 424, 29], [412, 16, 425, 14, "data"], [412, 20, 425, 18], [412, 21, 425, 19, "index"], [412, 26, 425, 24], [412, 29, 425, 27], [412, 30, 425, 28], [412, 31, 425, 29], [412, 34, 425, 32, "g"], [412, 35, 425, 33], [413, 16, 426, 14, "data"], [413, 20, 426, 18], [413, 21, 426, 19, "index"], [413, 26, 426, 24], [413, 29, 426, 27], [413, 30, 426, 28], [413, 31, 426, 29], [413, 34, 426, 32, "b"], [413, 35, 426, 33], [414, 16, 427, 14], [415, 14, 428, 12], [416, 12, 429, 10], [417, 10, 430, 8], [418, 8, 431, 6], [419, 6, 432, 4], [421, 6, 434, 4], [422, 6, 435, 4], [422, 11, 435, 9], [422, 15, 435, 13, "i"], [422, 16, 435, 14], [422, 19, 435, 17], [422, 20, 435, 18], [422, 22, 435, 20, "i"], [422, 23, 435, 21], [422, 26, 435, 24], [422, 27, 435, 25], [422, 29, 435, 27, "i"], [422, 30, 435, 28], [422, 32, 435, 30], [422, 34, 435, 32], [423, 8, 436, 6, "applySimpleBlur"], [423, 23, 436, 21], [423, 24, 436, 22, "data"], [423, 28, 436, 26], [423, 30, 436, 28, "<PERSON><PERSON><PERSON><PERSON>"], [423, 42, 436, 40], [423, 44, 436, 42, "clampedHeight"], [423, 57, 436, 55], [423, 58, 436, 56], [424, 6, 437, 4], [426, 6, 439, 4], [427, 6, 440, 4, "ctx"], [427, 9, 440, 7], [427, 10, 440, 8, "putImageData"], [427, 22, 440, 20], [427, 23, 440, 21, "imageData"], [427, 32, 440, 30], [427, 34, 440, 32, "clampedX"], [427, 42, 440, 40], [427, 44, 440, 42, "clampedY"], [427, 52, 440, 50], [427, 53, 440, 51], [429, 6, 442, 4], [430, 6, 443, 4, "ctx"], [430, 9, 443, 7], [430, 10, 443, 8, "fillStyle"], [430, 19, 443, 17], [430, 22, 443, 20], [430, 48, 443, 46], [431, 6, 444, 4, "ctx"], [431, 9, 444, 7], [431, 10, 444, 8, "fillRect"], [431, 18, 444, 16], [431, 19, 444, 17, "clampedX"], [431, 27, 444, 25], [431, 29, 444, 27, "clampedY"], [431, 37, 444, 35], [431, 39, 444, 37, "<PERSON><PERSON><PERSON><PERSON>"], [431, 51, 444, 49], [431, 53, 444, 51, "clampedHeight"], [431, 66, 444, 64], [431, 67, 444, 65], [432, 4, 445, 2], [432, 5, 445, 3], [433, 4, 447, 2], [433, 10, 447, 8, "applySimpleBlur"], [433, 25, 447, 23], [433, 28, 447, 26, "applySimpleBlur"], [433, 29, 447, 27, "data"], [433, 33, 447, 50], [433, 35, 447, 52, "width"], [433, 40, 447, 65], [433, 42, 447, 67, "height"], [433, 48, 447, 81], [433, 53, 447, 86], [434, 6, 448, 4], [434, 12, 448, 10, "original"], [434, 20, 448, 18], [434, 23, 448, 21], [434, 27, 448, 25, "Uint8ClampedArray"], [434, 44, 448, 42], [434, 45, 448, 43, "data"], [434, 49, 448, 47], [434, 50, 448, 48], [435, 6, 450, 4], [435, 11, 450, 9], [435, 15, 450, 13, "y"], [435, 16, 450, 14], [435, 19, 450, 17], [435, 20, 450, 18], [435, 22, 450, 20, "y"], [435, 23, 450, 21], [435, 26, 450, 24, "height"], [435, 32, 450, 30], [435, 35, 450, 33], [435, 36, 450, 34], [435, 38, 450, 36, "y"], [435, 39, 450, 37], [435, 41, 450, 39], [435, 43, 450, 41], [436, 8, 451, 6], [436, 13, 451, 11], [436, 17, 451, 15, "x"], [436, 18, 451, 16], [436, 21, 451, 19], [436, 22, 451, 20], [436, 24, 451, 22, "x"], [436, 25, 451, 23], [436, 28, 451, 26, "width"], [436, 33, 451, 31], [436, 36, 451, 34], [436, 37, 451, 35], [436, 39, 451, 37, "x"], [436, 40, 451, 38], [436, 42, 451, 40], [436, 44, 451, 42], [437, 10, 452, 8], [437, 16, 452, 14, "index"], [437, 21, 452, 19], [437, 24, 452, 22], [437, 25, 452, 23, "y"], [437, 26, 452, 24], [437, 29, 452, 27, "width"], [437, 34, 452, 32], [437, 37, 452, 35, "x"], [437, 38, 452, 36], [437, 42, 452, 40], [437, 43, 452, 41], [439, 10, 454, 8], [440, 10, 455, 8], [440, 14, 455, 12, "r"], [440, 15, 455, 13], [440, 18, 455, 16], [440, 19, 455, 17], [441, 12, 455, 19, "g"], [441, 13, 455, 20], [441, 16, 455, 23], [441, 17, 455, 24], [442, 12, 455, 26, "b"], [442, 13, 455, 27], [442, 16, 455, 30], [442, 17, 455, 31], [443, 10, 456, 8], [443, 15, 456, 13], [443, 19, 456, 17, "dy"], [443, 21, 456, 19], [443, 24, 456, 22], [443, 25, 456, 23], [443, 26, 456, 24], [443, 28, 456, 26, "dy"], [443, 30, 456, 28], [443, 34, 456, 32], [443, 35, 456, 33], [443, 37, 456, 35, "dy"], [443, 39, 456, 37], [443, 41, 456, 39], [443, 43, 456, 41], [444, 12, 457, 10], [444, 17, 457, 15], [444, 21, 457, 19, "dx"], [444, 23, 457, 21], [444, 26, 457, 24], [444, 27, 457, 25], [444, 28, 457, 26], [444, 30, 457, 28, "dx"], [444, 32, 457, 30], [444, 36, 457, 34], [444, 37, 457, 35], [444, 39, 457, 37, "dx"], [444, 41, 457, 39], [444, 43, 457, 41], [444, 45, 457, 43], [445, 14, 458, 12], [445, 20, 458, 18, "neighborIndex"], [445, 33, 458, 31], [445, 36, 458, 34], [445, 37, 458, 35], [445, 38, 458, 36, "y"], [445, 39, 458, 37], [445, 42, 458, 40, "dy"], [445, 44, 458, 42], [445, 48, 458, 46, "width"], [445, 53, 458, 51], [445, 57, 458, 55, "x"], [445, 58, 458, 56], [445, 61, 458, 59, "dx"], [445, 63, 458, 61], [445, 64, 458, 62], [445, 68, 458, 66], [445, 69, 458, 67], [446, 14, 459, 12, "r"], [446, 15, 459, 13], [446, 19, 459, 17, "original"], [446, 27, 459, 25], [446, 28, 459, 26, "neighborIndex"], [446, 41, 459, 39], [446, 42, 459, 40], [447, 14, 460, 12, "g"], [447, 15, 460, 13], [447, 19, 460, 17, "original"], [447, 27, 460, 25], [447, 28, 460, 26, "neighborIndex"], [447, 41, 460, 39], [447, 44, 460, 42], [447, 45, 460, 43], [447, 46, 460, 44], [448, 14, 461, 12, "b"], [448, 15, 461, 13], [448, 19, 461, 17, "original"], [448, 27, 461, 25], [448, 28, 461, 26, "neighborIndex"], [448, 41, 461, 39], [448, 44, 461, 42], [448, 45, 461, 43], [448, 46, 461, 44], [449, 12, 462, 10], [450, 10, 463, 8], [451, 10, 465, 8, "data"], [451, 14, 465, 12], [451, 15, 465, 13, "index"], [451, 20, 465, 18], [451, 21, 465, 19], [451, 24, 465, 22, "r"], [451, 25, 465, 23], [451, 28, 465, 26], [451, 29, 465, 27], [452, 10, 466, 8, "data"], [452, 14, 466, 12], [452, 15, 466, 13, "index"], [452, 20, 466, 18], [452, 23, 466, 21], [452, 24, 466, 22], [452, 25, 466, 23], [452, 28, 466, 26, "g"], [452, 29, 466, 27], [452, 32, 466, 30], [452, 33, 466, 31], [453, 10, 467, 8, "data"], [453, 14, 467, 12], [453, 15, 467, 13, "index"], [453, 20, 467, 18], [453, 23, 467, 21], [453, 24, 467, 22], [453, 25, 467, 23], [453, 28, 467, 26, "b"], [453, 29, 467, 27], [453, 32, 467, 30], [453, 33, 467, 31], [454, 8, 468, 6], [455, 6, 469, 4], [456, 4, 470, 2], [456, 5, 470, 3], [457, 4, 472, 2], [457, 10, 472, 8, "applyFallbackFaceBlur"], [457, 31, 472, 29], [457, 34, 472, 32, "applyFallbackFaceBlur"], [457, 35, 472, 33, "ctx"], [457, 38, 472, 62], [457, 40, 472, 64, "imgWidth"], [457, 48, 472, 80], [457, 50, 472, 82, "imgHeight"], [457, 59, 472, 99], [457, 64, 472, 104], [458, 6, 473, 4, "console"], [458, 13, 473, 11], [458, 14, 473, 12, "log"], [458, 17, 473, 15], [458, 18, 473, 16], [458, 90, 473, 88], [458, 91, 473, 89], [460, 6, 475, 4], [461, 6, 476, 4], [461, 12, 476, 10, "areas"], [461, 17, 476, 15], [461, 20, 476, 18], [462, 6, 477, 6], [463, 6, 478, 6], [464, 8, 478, 8, "x"], [464, 9, 478, 9], [464, 11, 478, 11, "imgWidth"], [464, 19, 478, 19], [464, 22, 478, 22], [464, 26, 478, 26], [465, 8, 478, 28, "y"], [465, 9, 478, 29], [465, 11, 478, 31, "imgHeight"], [465, 20, 478, 40], [465, 23, 478, 43], [465, 27, 478, 47], [466, 8, 478, 49, "w"], [466, 9, 478, 50], [466, 11, 478, 52, "imgWidth"], [466, 19, 478, 60], [466, 22, 478, 63], [466, 25, 478, 66], [467, 8, 478, 68, "h"], [467, 9, 478, 69], [467, 11, 478, 71, "imgHeight"], [467, 20, 478, 80], [467, 23, 478, 83], [468, 6, 478, 87], [468, 7, 478, 88], [469, 6, 479, 6], [470, 6, 480, 6], [471, 8, 480, 8, "x"], [471, 9, 480, 9], [471, 11, 480, 11, "imgWidth"], [471, 19, 480, 19], [471, 22, 480, 22], [471, 25, 480, 25], [472, 8, 480, 27, "y"], [472, 9, 480, 28], [472, 11, 480, 30, "imgHeight"], [472, 20, 480, 39], [472, 23, 480, 42], [472, 26, 480, 45], [473, 8, 480, 47, "w"], [473, 9, 480, 48], [473, 11, 480, 50, "imgWidth"], [473, 19, 480, 58], [473, 22, 480, 61], [473, 26, 480, 65], [474, 8, 480, 67, "h"], [474, 9, 480, 68], [474, 11, 480, 70, "imgHeight"], [474, 20, 480, 79], [474, 23, 480, 82], [475, 6, 480, 86], [475, 7, 480, 87], [476, 6, 481, 6], [477, 6, 482, 6], [478, 8, 482, 8, "x"], [478, 9, 482, 9], [478, 11, 482, 11, "imgWidth"], [478, 19, 482, 19], [478, 22, 482, 22], [478, 26, 482, 26], [479, 8, 482, 28, "y"], [479, 9, 482, 29], [479, 11, 482, 31, "imgHeight"], [479, 20, 482, 40], [479, 23, 482, 43], [479, 26, 482, 46], [480, 8, 482, 48, "w"], [480, 9, 482, 49], [480, 11, 482, 51, "imgWidth"], [480, 19, 482, 59], [480, 22, 482, 62], [480, 26, 482, 66], [481, 8, 482, 68, "h"], [481, 9, 482, 69], [481, 11, 482, 71, "imgHeight"], [481, 20, 482, 80], [481, 23, 482, 83], [482, 6, 482, 87], [482, 7, 482, 88], [482, 8, 483, 5], [483, 6, 485, 4, "areas"], [483, 11, 485, 9], [483, 12, 485, 10, "for<PERSON>ach"], [483, 19, 485, 17], [483, 20, 485, 18], [483, 21, 485, 19, "area"], [483, 25, 485, 23], [483, 27, 485, 25, "index"], [483, 32, 485, 30], [483, 37, 485, 35], [484, 8, 486, 6, "console"], [484, 15, 486, 13], [484, 16, 486, 14, "log"], [484, 19, 486, 17], [484, 20, 486, 18], [484, 65, 486, 63, "index"], [484, 70, 486, 68], [484, 73, 486, 71], [484, 74, 486, 72], [484, 77, 486, 75], [484, 79, 486, 77, "area"], [484, 83, 486, 81], [484, 84, 486, 82], [485, 8, 487, 6, "applyStrongBlur"], [485, 23, 487, 21], [485, 24, 487, 22, "ctx"], [485, 27, 487, 25], [485, 29, 487, 27, "area"], [485, 33, 487, 31], [485, 34, 487, 32, "x"], [485, 35, 487, 33], [485, 37, 487, 35, "area"], [485, 41, 487, 39], [485, 42, 487, 40, "y"], [485, 43, 487, 41], [485, 45, 487, 43, "area"], [485, 49, 487, 47], [485, 50, 487, 48, "w"], [485, 51, 487, 49], [485, 53, 487, 51, "area"], [485, 57, 487, 55], [485, 58, 487, 56, "h"], [485, 59, 487, 57], [485, 60, 487, 58], [486, 6, 488, 4], [486, 7, 488, 5], [486, 8, 488, 6], [487, 4, 489, 2], [487, 5, 489, 3], [489, 4, 491, 2], [490, 4, 492, 2], [490, 10, 492, 8, "capturePhoto"], [490, 22, 492, 20], [490, 25, 492, 23], [490, 29, 492, 23, "useCallback"], [490, 47, 492, 34], [490, 49, 492, 35], [490, 61, 492, 47], [491, 6, 493, 4], [492, 6, 494, 4], [492, 12, 494, 10, "isDev"], [492, 17, 494, 15], [492, 20, 494, 18, "process"], [492, 27, 494, 25], [492, 28, 494, 26, "env"], [492, 31, 494, 29], [492, 32, 494, 30, "NODE_ENV"], [492, 40, 494, 38], [492, 45, 494, 43], [492, 58, 494, 56], [492, 62, 494, 60, "__DEV__"], [492, 69, 494, 67], [493, 6, 496, 4], [493, 10, 496, 8], [493, 11, 496, 9, "cameraRef"], [493, 20, 496, 18], [493, 21, 496, 19, "current"], [493, 28, 496, 26], [493, 32, 496, 30], [493, 33, 496, 31, "isDev"], [493, 38, 496, 36], [493, 40, 496, 38], [494, 8, 497, 6, "<PERSON><PERSON>"], [494, 22, 497, 11], [494, 23, 497, 12, "alert"], [494, 28, 497, 17], [494, 29, 497, 18], [494, 36, 497, 25], [494, 38, 497, 27], [494, 56, 497, 45], [494, 57, 497, 46], [495, 8, 498, 6], [496, 6, 499, 4], [497, 6, 500, 4], [497, 10, 500, 8], [498, 8, 501, 6, "setProcessingState"], [498, 26, 501, 24], [498, 27, 501, 25], [498, 38, 501, 36], [498, 39, 501, 37], [499, 8, 502, 6, "setProcessingProgress"], [499, 29, 502, 27], [499, 30, 502, 28], [499, 32, 502, 30], [499, 33, 502, 31], [500, 8, 503, 6], [501, 8, 504, 6], [502, 8, 505, 6], [503, 8, 506, 6], [503, 14, 506, 12], [503, 18, 506, 16, "Promise"], [503, 25, 506, 23], [503, 26, 506, 24, "resolve"], [503, 33, 506, 31], [503, 37, 506, 35, "setTimeout"], [503, 47, 506, 45], [503, 48, 506, 46, "resolve"], [503, 55, 506, 53], [503, 57, 506, 55], [503, 59, 506, 57], [503, 60, 506, 58], [503, 61, 506, 59], [504, 8, 507, 6], [505, 8, 508, 6], [505, 12, 508, 10, "photo"], [505, 17, 508, 15], [506, 8, 510, 6], [506, 12, 510, 10], [507, 10, 511, 8, "photo"], [507, 15, 511, 13], [507, 18, 511, 16], [507, 24, 511, 22, "cameraRef"], [507, 33, 511, 31], [507, 34, 511, 32, "current"], [507, 41, 511, 39], [507, 42, 511, 40, "takePictureAsync"], [507, 58, 511, 56], [507, 59, 511, 57], [508, 12, 512, 10, "quality"], [508, 19, 512, 17], [508, 21, 512, 19], [508, 24, 512, 22], [509, 12, 513, 10, "base64"], [509, 18, 513, 16], [509, 20, 513, 18], [509, 25, 513, 23], [510, 12, 514, 10, "skipProcessing"], [510, 26, 514, 24], [510, 28, 514, 26], [510, 32, 514, 30], [510, 33, 514, 32], [511, 10, 515, 8], [511, 11, 515, 9], [511, 12, 515, 10], [512, 8, 516, 6], [512, 9, 516, 7], [512, 10, 516, 8], [512, 17, 516, 15, "cameraError"], [512, 28, 516, 26], [512, 30, 516, 28], [513, 10, 517, 8, "console"], [513, 17, 517, 15], [513, 18, 517, 16, "log"], [513, 21, 517, 19], [513, 22, 517, 20], [513, 82, 517, 80], [513, 84, 517, 82, "cameraError"], [513, 95, 517, 93], [513, 96, 517, 94], [514, 10, 518, 8], [515, 10, 519, 8], [515, 14, 519, 12, "isDev"], [515, 19, 519, 17], [515, 21, 519, 19], [516, 12, 520, 10, "photo"], [516, 17, 520, 15], [516, 20, 520, 18], [517, 14, 521, 12, "uri"], [517, 17, 521, 15], [517, 19, 521, 17], [518, 12, 522, 10], [518, 13, 522, 11], [519, 10, 523, 8], [519, 11, 523, 9], [519, 17, 523, 15], [520, 12, 524, 10], [520, 18, 524, 16, "cameraError"], [520, 29, 524, 27], [521, 10, 525, 8], [522, 8, 526, 6], [523, 8, 527, 6], [523, 12, 527, 10], [523, 13, 527, 11, "photo"], [523, 18, 527, 16], [523, 20, 527, 18], [524, 10, 528, 8], [524, 16, 528, 14], [524, 20, 528, 18, "Error"], [524, 25, 528, 23], [524, 26, 528, 24], [524, 51, 528, 49], [524, 52, 528, 50], [525, 8, 529, 6], [526, 8, 530, 6, "console"], [526, 15, 530, 13], [526, 16, 530, 14, "log"], [526, 19, 530, 17], [526, 20, 530, 18], [526, 56, 530, 54], [526, 58, 530, 56, "photo"], [526, 63, 530, 61], [526, 64, 530, 62, "uri"], [526, 67, 530, 65], [526, 68, 530, 66], [527, 8, 531, 6, "setCapturedPhoto"], [527, 24, 531, 22], [527, 25, 531, 23, "photo"], [527, 30, 531, 28], [527, 31, 531, 29, "uri"], [527, 34, 531, 32], [527, 35, 531, 33], [528, 8, 532, 6, "setProcessingProgress"], [528, 29, 532, 27], [528, 30, 532, 28], [528, 32, 532, 30], [528, 33, 532, 31], [529, 8, 533, 6], [530, 8, 534, 6, "console"], [530, 15, 534, 13], [530, 16, 534, 14, "log"], [530, 19, 534, 17], [530, 20, 534, 18], [530, 73, 534, 71], [530, 74, 534, 72], [531, 8, 535, 6, "console"], [531, 15, 535, 13], [531, 16, 535, 14, "log"], [531, 19, 535, 17], [531, 20, 535, 18], [531, 96, 535, 94], [531, 98, 535, 96, "photo"], [531, 103, 535, 101], [531, 104, 535, 102, "uri"], [531, 107, 535, 105], [531, 108, 535, 106], [532, 8, 536, 6, "console"], [532, 15, 536, 13], [532, 16, 536, 14, "log"], [532, 19, 536, 17], [532, 20, 536, 18], [532, 68, 536, 66], [532, 70, 536, 68], [532, 77, 536, 75, "processImageWithFaceBlur"], [532, 101, 536, 99], [532, 102, 536, 100], [533, 8, 537, 6], [533, 14, 537, 12, "processImageWithFaceBlur"], [533, 38, 537, 36], [533, 39, 537, 37, "photo"], [533, 44, 537, 42], [533, 45, 537, 43, "uri"], [533, 48, 537, 46], [533, 49, 537, 47], [534, 8, 538, 6, "console"], [534, 15, 538, 13], [534, 16, 538, 14, "log"], [534, 19, 538, 17], [534, 20, 538, 18], [534, 71, 538, 69], [534, 72, 538, 70], [535, 6, 539, 4], [535, 7, 539, 5], [535, 8, 539, 6], [535, 15, 539, 13, "error"], [535, 20, 539, 18], [535, 22, 539, 20], [536, 8, 540, 6, "console"], [536, 15, 540, 13], [536, 16, 540, 14, "error"], [536, 21, 540, 19], [536, 22, 540, 20], [536, 54, 540, 52], [536, 56, 540, 54, "error"], [536, 61, 540, 59], [536, 62, 540, 60], [537, 8, 541, 6, "setErrorMessage"], [537, 23, 541, 21], [537, 24, 541, 22], [537, 68, 541, 66], [537, 69, 541, 67], [538, 8, 542, 6, "setProcessingState"], [538, 26, 542, 24], [538, 27, 542, 25], [538, 34, 542, 32], [538, 35, 542, 33], [539, 6, 543, 4], [540, 4, 544, 2], [540, 5, 544, 3], [540, 7, 544, 5], [540, 9, 544, 7], [540, 10, 544, 8], [541, 4, 545, 2], [542, 4, 546, 2], [542, 10, 546, 8, "processImageWithFaceBlur"], [542, 34, 546, 32], [542, 37, 546, 35], [542, 43, 546, 42, "photoUri"], [542, 51, 546, 58], [542, 55, 546, 63], [543, 6, 547, 4, "console"], [543, 13, 547, 11], [543, 14, 547, 12, "log"], [543, 17, 547, 15], [543, 18, 547, 16], [543, 85, 547, 83], [543, 86, 547, 84], [544, 6, 548, 4], [544, 10, 548, 8], [545, 8, 549, 6, "console"], [545, 15, 549, 13], [545, 16, 549, 14, "log"], [545, 19, 549, 17], [545, 20, 549, 18], [545, 84, 549, 82], [545, 85, 549, 83], [546, 8, 550, 6, "setProcessingState"], [546, 26, 550, 24], [546, 27, 550, 25], [546, 39, 550, 37], [546, 40, 550, 38], [547, 8, 551, 6, "setProcessingProgress"], [547, 29, 551, 27], [547, 30, 551, 28], [547, 32, 551, 30], [547, 33, 551, 31], [549, 8, 553, 6], [550, 8, 554, 6], [550, 14, 554, 12, "canvas"], [550, 20, 554, 18], [550, 23, 554, 21, "document"], [550, 31, 554, 29], [550, 32, 554, 30, "createElement"], [550, 45, 554, 43], [550, 46, 554, 44], [550, 54, 554, 52], [550, 55, 554, 53], [551, 8, 555, 6], [551, 14, 555, 12, "ctx"], [551, 17, 555, 15], [551, 20, 555, 18, "canvas"], [551, 26, 555, 24], [551, 27, 555, 25, "getContext"], [551, 37, 555, 35], [551, 38, 555, 36], [551, 42, 555, 40], [551, 43, 555, 41], [552, 8, 556, 6], [552, 12, 556, 10], [552, 13, 556, 11, "ctx"], [552, 16, 556, 14], [552, 18, 556, 16], [552, 24, 556, 22], [552, 28, 556, 26, "Error"], [552, 33, 556, 31], [552, 34, 556, 32], [552, 64, 556, 62], [552, 65, 556, 63], [554, 8, 558, 6], [555, 8, 559, 6], [555, 14, 559, 12, "img"], [555, 17, 559, 15], [555, 20, 559, 18], [555, 24, 559, 22, "Image"], [555, 29, 559, 27], [555, 30, 559, 28], [555, 31, 559, 29], [556, 8, 560, 6], [556, 14, 560, 12], [556, 18, 560, 16, "Promise"], [556, 25, 560, 23], [556, 26, 560, 24], [556, 27, 560, 25, "resolve"], [556, 34, 560, 32], [556, 36, 560, 34, "reject"], [556, 42, 560, 40], [556, 47, 560, 45], [557, 10, 561, 8, "img"], [557, 13, 561, 11], [557, 14, 561, 12, "onload"], [557, 20, 561, 18], [557, 23, 561, 21, "resolve"], [557, 30, 561, 28], [558, 10, 562, 8, "img"], [558, 13, 562, 11], [558, 14, 562, 12, "onerror"], [558, 21, 562, 19], [558, 24, 562, 22, "reject"], [558, 30, 562, 28], [559, 10, 563, 8, "img"], [559, 13, 563, 11], [559, 14, 563, 12, "src"], [559, 17, 563, 15], [559, 20, 563, 18, "photoUri"], [559, 28, 563, 26], [560, 8, 564, 6], [560, 9, 564, 7], [560, 10, 564, 8], [562, 8, 566, 6], [563, 8, 567, 6, "canvas"], [563, 14, 567, 12], [563, 15, 567, 13, "width"], [563, 20, 567, 18], [563, 23, 567, 21, "img"], [563, 26, 567, 24], [563, 27, 567, 25, "width"], [563, 32, 567, 30], [564, 8, 568, 6, "canvas"], [564, 14, 568, 12], [564, 15, 568, 13, "height"], [564, 21, 568, 19], [564, 24, 568, 22, "img"], [564, 27, 568, 25], [564, 28, 568, 26, "height"], [564, 34, 568, 32], [565, 8, 569, 6, "console"], [565, 15, 569, 13], [565, 16, 569, 14, "log"], [565, 19, 569, 17], [565, 20, 569, 18], [565, 54, 569, 52], [565, 56, 569, 54], [566, 10, 569, 56, "width"], [566, 15, 569, 61], [566, 17, 569, 63, "img"], [566, 20, 569, 66], [566, 21, 569, 67, "width"], [566, 26, 569, 72], [567, 10, 569, 74, "height"], [567, 16, 569, 80], [567, 18, 569, 82, "img"], [567, 21, 569, 85], [567, 22, 569, 86, "height"], [568, 8, 569, 93], [568, 9, 569, 94], [568, 10, 569, 95], [570, 8, 571, 6], [571, 8, 572, 6, "ctx"], [571, 11, 572, 9], [571, 12, 572, 10, "drawImage"], [571, 21, 572, 19], [571, 22, 572, 20, "img"], [571, 25, 572, 23], [571, 27, 572, 25], [571, 28, 572, 26], [571, 30, 572, 28], [571, 31, 572, 29], [571, 32, 572, 30], [572, 8, 573, 6, "console"], [572, 15, 573, 13], [572, 16, 573, 14, "log"], [572, 19, 573, 17], [572, 20, 573, 18], [572, 72, 573, 70], [572, 73, 573, 71], [573, 8, 575, 6, "setProcessingProgress"], [573, 29, 575, 27], [573, 30, 575, 28], [573, 32, 575, 30], [573, 33, 575, 31], [575, 8, 577, 6], [576, 8, 578, 6], [576, 12, 578, 10, "detectedFaces"], [576, 25, 578, 23], [576, 28, 578, 26], [576, 30, 578, 28], [577, 8, 580, 6, "console"], [577, 15, 580, 13], [577, 16, 580, 14, "log"], [577, 19, 580, 17], [577, 20, 580, 18], [577, 81, 580, 79], [577, 82, 580, 80], [579, 8, 582, 6], [580, 8, 583, 6], [580, 12, 583, 10], [581, 10, 584, 8, "console"], [581, 17, 584, 15], [581, 18, 584, 16, "log"], [581, 21, 584, 19], [581, 22, 584, 20], [581, 81, 584, 79], [581, 82, 584, 80], [582, 10, 585, 8], [582, 16, 585, 14, "loadTensorFlowFaceDetection"], [582, 43, 585, 41], [582, 44, 585, 42], [582, 45, 585, 43], [583, 10, 586, 8, "console"], [583, 17, 586, 15], [583, 18, 586, 16, "log"], [583, 21, 586, 19], [583, 22, 586, 20], [583, 90, 586, 88], [583, 91, 586, 89], [584, 10, 587, 8, "detectedFaces"], [584, 23, 587, 21], [584, 26, 587, 24], [584, 32, 587, 30, "detectFacesWithTensorFlow"], [584, 57, 587, 55], [584, 58, 587, 56, "img"], [584, 61, 587, 59], [584, 62, 587, 60], [585, 10, 588, 8, "console"], [585, 17, 588, 15], [585, 18, 588, 16, "log"], [585, 21, 588, 19], [585, 22, 588, 20], [585, 70, 588, 68, "detectedFaces"], [585, 83, 588, 81], [585, 84, 588, 82, "length"], [585, 90, 588, 88], [585, 98, 588, 96], [585, 99, 588, 97], [586, 8, 589, 6], [586, 9, 589, 7], [586, 10, 589, 8], [586, 17, 589, 15, "tensorFlowError"], [586, 32, 589, 30], [586, 34, 589, 32], [587, 10, 590, 8, "console"], [587, 17, 590, 15], [587, 18, 590, 16, "warn"], [587, 22, 590, 20], [587, 23, 590, 21], [587, 61, 590, 59], [587, 63, 590, 61, "tensorFlowError"], [587, 78, 590, 76], [587, 79, 590, 77], [588, 10, 591, 8, "console"], [588, 17, 591, 15], [588, 18, 591, 16, "warn"], [588, 22, 591, 20], [588, 23, 591, 21], [588, 68, 591, 66], [588, 70, 591, 68], [589, 12, 592, 10, "message"], [589, 19, 592, 17], [589, 21, 592, 19, "tensorFlowError"], [589, 36, 592, 34], [589, 37, 592, 35, "message"], [589, 44, 592, 42], [590, 12, 593, 10, "stack"], [590, 17, 593, 15], [590, 19, 593, 17, "tensorFlowError"], [590, 34, 593, 32], [590, 35, 593, 33, "stack"], [591, 10, 594, 8], [591, 11, 594, 9], [591, 12, 594, 10], [593, 10, 596, 8], [594, 10, 597, 8, "console"], [594, 17, 597, 15], [594, 18, 597, 16, "log"], [594, 21, 597, 19], [594, 22, 597, 20], [594, 86, 597, 84], [594, 87, 597, 85], [595, 10, 598, 8, "detectedFaces"], [595, 23, 598, 21], [595, 26, 598, 24, "detectFacesHeuristic"], [595, 46, 598, 44], [595, 47, 598, 45, "img"], [595, 50, 598, 48], [595, 52, 598, 50, "ctx"], [595, 55, 598, 53], [595, 56, 598, 54], [596, 10, 599, 8, "console"], [596, 17, 599, 15], [596, 18, 599, 16, "log"], [596, 21, 599, 19], [596, 22, 599, 20], [596, 70, 599, 68, "detectedFaces"], [596, 83, 599, 81], [596, 84, 599, 82, "length"], [596, 90, 599, 88], [596, 98, 599, 96], [596, 99, 599, 97], [597, 8, 600, 6], [599, 8, 602, 6], [600, 8, 603, 6], [600, 12, 603, 10, "detectedFaces"], [600, 25, 603, 23], [600, 26, 603, 24, "length"], [600, 32, 603, 30], [600, 37, 603, 35], [600, 38, 603, 36], [600, 40, 603, 38], [601, 10, 604, 8, "console"], [601, 17, 604, 15], [601, 18, 604, 16, "log"], [601, 21, 604, 19], [601, 22, 604, 20], [601, 89, 604, 87], [601, 90, 604, 88], [602, 10, 605, 8, "detectedFaces"], [602, 23, 605, 21], [602, 26, 605, 24, "detectFacesAggressive"], [602, 47, 605, 45], [602, 48, 605, 46, "img"], [602, 51, 605, 49], [602, 53, 605, 51, "ctx"], [602, 56, 605, 54], [602, 57, 605, 55], [603, 10, 606, 8, "console"], [603, 17, 606, 15], [603, 18, 606, 16, "log"], [603, 21, 606, 19], [603, 22, 606, 20], [603, 71, 606, 69, "detectedFaces"], [603, 84, 606, 82], [603, 85, 606, 83, "length"], [603, 91, 606, 89], [603, 99, 606, 97], [603, 100, 606, 98], [604, 8, 607, 6], [605, 8, 609, 6, "console"], [605, 15, 609, 13], [605, 16, 609, 14, "log"], [605, 19, 609, 17], [605, 20, 609, 18], [605, 72, 609, 70, "detectedFaces"], [605, 85, 609, 83], [605, 86, 609, 84, "length"], [605, 92, 609, 90], [605, 100, 609, 98], [605, 101, 609, 99], [606, 8, 610, 6], [606, 12, 610, 10, "detectedFaces"], [606, 25, 610, 23], [606, 26, 610, 24, "length"], [606, 32, 610, 30], [606, 35, 610, 33], [606, 36, 610, 34], [606, 38, 610, 36], [607, 10, 611, 8, "console"], [607, 17, 611, 15], [607, 18, 611, 16, "log"], [607, 21, 611, 19], [607, 22, 611, 20], [607, 66, 611, 64], [607, 68, 611, 66, "detectedFaces"], [607, 81, 611, 79], [607, 82, 611, 80, "map"], [607, 85, 611, 83], [607, 86, 611, 84], [607, 87, 611, 85, "face"], [607, 91, 611, 89], [607, 93, 611, 91, "i"], [607, 94, 611, 92], [607, 100, 611, 98], [608, 12, 612, 10, "faceNumber"], [608, 22, 612, 20], [608, 24, 612, 22, "i"], [608, 25, 612, 23], [608, 28, 612, 26], [608, 29, 612, 27], [609, 12, 613, 10, "centerX"], [609, 19, 613, 17], [609, 21, 613, 19, "face"], [609, 25, 613, 23], [609, 26, 613, 24, "boundingBox"], [609, 37, 613, 35], [609, 38, 613, 36, "xCenter"], [609, 45, 613, 43], [610, 12, 614, 10, "centerY"], [610, 19, 614, 17], [610, 21, 614, 19, "face"], [610, 25, 614, 23], [610, 26, 614, 24, "boundingBox"], [610, 37, 614, 35], [610, 38, 614, 36, "yCenter"], [610, 45, 614, 43], [611, 12, 615, 10, "width"], [611, 17, 615, 15], [611, 19, 615, 17, "face"], [611, 23, 615, 21], [611, 24, 615, 22, "boundingBox"], [611, 35, 615, 33], [611, 36, 615, 34, "width"], [611, 41, 615, 39], [612, 12, 616, 10, "height"], [612, 18, 616, 16], [612, 20, 616, 18, "face"], [612, 24, 616, 22], [612, 25, 616, 23, "boundingBox"], [612, 36, 616, 34], [612, 37, 616, 35, "height"], [613, 10, 617, 8], [613, 11, 617, 9], [613, 12, 617, 10], [613, 13, 617, 11], [613, 14, 617, 12], [614, 8, 618, 6], [614, 9, 618, 7], [614, 15, 618, 13], [615, 10, 619, 8, "console"], [615, 17, 619, 15], [615, 18, 619, 16, "log"], [615, 21, 619, 19], [615, 22, 619, 20], [615, 74, 619, 72], [615, 75, 619, 73], [616, 10, 620, 8, "console"], [616, 17, 620, 15], [616, 18, 620, 16, "log"], [616, 21, 620, 19], [616, 22, 620, 20], [616, 95, 620, 93], [616, 96, 620, 94], [618, 10, 622, 8], [619, 10, 623, 8], [619, 16, 623, 14, "centerX"], [619, 23, 623, 21], [619, 26, 623, 24, "img"], [619, 29, 623, 27], [619, 30, 623, 28, "width"], [619, 35, 623, 33], [619, 38, 623, 36], [619, 41, 623, 39], [620, 10, 624, 8], [620, 16, 624, 14, "centerY"], [620, 23, 624, 21], [620, 26, 624, 24, "img"], [620, 29, 624, 27], [620, 30, 624, 28, "height"], [620, 36, 624, 34], [620, 39, 624, 37], [620, 42, 624, 40], [621, 10, 625, 8], [621, 16, 625, 14, "centerWidth"], [621, 27, 625, 25], [621, 30, 625, 28, "img"], [621, 33, 625, 31], [621, 34, 625, 32, "width"], [621, 39, 625, 37], [621, 42, 625, 40], [621, 45, 625, 43], [622, 10, 626, 8], [622, 16, 626, 14, "centerHeight"], [622, 28, 626, 26], [622, 31, 626, 29, "img"], [622, 34, 626, 32], [622, 35, 626, 33, "height"], [622, 41, 626, 39], [622, 44, 626, 42], [622, 47, 626, 45], [623, 10, 628, 8, "detectedFaces"], [623, 23, 628, 21], [623, 26, 628, 24], [623, 27, 628, 25], [624, 12, 629, 10, "boundingBox"], [624, 23, 629, 21], [624, 25, 629, 23], [625, 14, 630, 12, "xCenter"], [625, 21, 630, 19], [625, 23, 630, 21], [625, 26, 630, 24], [626, 14, 631, 12, "yCenter"], [626, 21, 631, 19], [626, 23, 631, 21], [626, 26, 631, 24], [627, 14, 632, 12, "width"], [627, 19, 632, 17], [627, 21, 632, 19], [627, 24, 632, 22], [628, 14, 633, 12, "height"], [628, 20, 633, 18], [628, 22, 633, 20], [629, 12, 634, 10], [629, 13, 634, 11], [630, 12, 635, 10, "confidence"], [630, 22, 635, 20], [630, 24, 635, 22], [631, 10, 636, 8], [631, 11, 636, 9], [631, 12, 636, 10], [632, 10, 638, 8, "console"], [632, 17, 638, 15], [632, 18, 638, 16, "log"], [632, 21, 638, 19], [632, 22, 638, 20], [632, 100, 638, 98], [632, 101, 638, 99], [633, 8, 639, 6], [634, 8, 641, 6, "setProcessingProgress"], [634, 29, 641, 27], [634, 30, 641, 28], [634, 32, 641, 30], [634, 33, 641, 31], [636, 8, 643, 6], [637, 8, 644, 6], [637, 12, 644, 10, "detectedFaces"], [637, 25, 644, 23], [637, 26, 644, 24, "length"], [637, 32, 644, 30], [637, 35, 644, 33], [637, 36, 644, 34], [637, 38, 644, 36], [638, 10, 645, 8, "console"], [638, 17, 645, 15], [638, 18, 645, 16, "log"], [638, 21, 645, 19], [638, 22, 645, 20], [638, 61, 645, 59, "detectedFaces"], [638, 74, 645, 72], [638, 75, 645, 73, "length"], [638, 81, 645, 79], [638, 101, 645, 99], [638, 102, 645, 100], [639, 10, 647, 8, "detectedFaces"], [639, 23, 647, 21], [639, 24, 647, 22, "for<PERSON>ach"], [639, 31, 647, 29], [639, 32, 647, 30], [639, 33, 647, 31, "detection"], [639, 42, 647, 40], [639, 44, 647, 42, "index"], [639, 49, 647, 47], [639, 54, 647, 52], [640, 12, 648, 10], [640, 18, 648, 16, "bbox"], [640, 22, 648, 20], [640, 25, 648, 23, "detection"], [640, 34, 648, 32], [640, 35, 648, 33, "boundingBox"], [640, 46, 648, 44], [641, 12, 650, 10, "console"], [641, 19, 650, 17], [641, 20, 650, 18, "log"], [641, 23, 650, 21], [641, 24, 650, 22], [641, 85, 650, 83, "index"], [641, 90, 650, 88], [641, 93, 650, 91], [641, 94, 650, 92], [641, 97, 650, 95], [641, 99, 650, 97], [642, 14, 651, 12, "bbox"], [642, 18, 651, 16], [643, 14, 652, 12, "imageSize"], [643, 23, 652, 21], [643, 25, 652, 23], [644, 16, 652, 25, "width"], [644, 21, 652, 30], [644, 23, 652, 32, "img"], [644, 26, 652, 35], [644, 27, 652, 36, "width"], [644, 32, 652, 41], [645, 16, 652, 43, "height"], [645, 22, 652, 49], [645, 24, 652, 51, "img"], [645, 27, 652, 54], [645, 28, 652, 55, "height"], [646, 14, 652, 62], [647, 12, 653, 10], [647, 13, 653, 11], [647, 14, 653, 12], [649, 12, 655, 10], [650, 12, 656, 10], [650, 18, 656, 16, "faceX"], [650, 23, 656, 21], [650, 26, 656, 24, "bbox"], [650, 30, 656, 28], [650, 31, 656, 29, "xCenter"], [650, 38, 656, 36], [650, 41, 656, 39, "img"], [650, 44, 656, 42], [650, 45, 656, 43, "width"], [650, 50, 656, 48], [650, 53, 656, 52, "bbox"], [650, 57, 656, 56], [650, 58, 656, 57, "width"], [650, 63, 656, 62], [650, 66, 656, 65, "img"], [650, 69, 656, 68], [650, 70, 656, 69, "width"], [650, 75, 656, 74], [650, 78, 656, 78], [650, 79, 656, 79], [651, 12, 657, 10], [651, 18, 657, 16, "faceY"], [651, 23, 657, 21], [651, 26, 657, 24, "bbox"], [651, 30, 657, 28], [651, 31, 657, 29, "yCenter"], [651, 38, 657, 36], [651, 41, 657, 39, "img"], [651, 44, 657, 42], [651, 45, 657, 43, "height"], [651, 51, 657, 49], [651, 54, 657, 53, "bbox"], [651, 58, 657, 57], [651, 59, 657, 58, "height"], [651, 65, 657, 64], [651, 68, 657, 67, "img"], [651, 71, 657, 70], [651, 72, 657, 71, "height"], [651, 78, 657, 77], [651, 81, 657, 81], [651, 82, 657, 82], [652, 12, 658, 10], [652, 18, 658, 16, "faceWidth"], [652, 27, 658, 25], [652, 30, 658, 28, "bbox"], [652, 34, 658, 32], [652, 35, 658, 33, "width"], [652, 40, 658, 38], [652, 43, 658, 41, "img"], [652, 46, 658, 44], [652, 47, 658, 45, "width"], [652, 52, 658, 50], [653, 12, 659, 10], [653, 18, 659, 16, "faceHeight"], [653, 28, 659, 26], [653, 31, 659, 29, "bbox"], [653, 35, 659, 33], [653, 36, 659, 34, "height"], [653, 42, 659, 40], [653, 45, 659, 43, "img"], [653, 48, 659, 46], [653, 49, 659, 47, "height"], [653, 55, 659, 53], [654, 12, 661, 10, "console"], [654, 19, 661, 17], [654, 20, 661, 18, "log"], [654, 23, 661, 21], [654, 24, 661, 22], [654, 84, 661, 82], [654, 86, 661, 84], [655, 14, 662, 12, "faceX"], [655, 19, 662, 17], [656, 14, 662, 19, "faceY"], [656, 19, 662, 24], [657, 14, 662, 26, "faceWidth"], [657, 23, 662, 35], [658, 14, 662, 37, "faceHeight"], [658, 24, 662, 47], [659, 14, 663, 12, "<PERSON><PERSON><PERSON><PERSON>"], [659, 21, 663, 19], [659, 23, 663, 21, "faceX"], [659, 28, 663, 26], [659, 32, 663, 30], [659, 33, 663, 31], [659, 37, 663, 35, "faceY"], [659, 42, 663, 40], [659, 46, 663, 44], [659, 47, 663, 45], [659, 51, 663, 49, "faceWidth"], [659, 60, 663, 58], [659, 63, 663, 61], [659, 64, 663, 62], [659, 68, 663, 66, "faceHeight"], [659, 78, 663, 76], [659, 81, 663, 79], [660, 12, 664, 10], [660, 13, 664, 11], [660, 14, 664, 12], [662, 12, 666, 10], [663, 12, 667, 10], [663, 18, 667, 16, "padding"], [663, 25, 667, 23], [663, 28, 667, 26], [663, 31, 667, 29], [664, 12, 668, 10], [664, 18, 668, 16, "paddedX"], [664, 25, 668, 23], [664, 28, 668, 26, "Math"], [664, 32, 668, 30], [664, 33, 668, 31, "max"], [664, 36, 668, 34], [664, 37, 668, 35], [664, 38, 668, 36], [664, 40, 668, 38, "faceX"], [664, 45, 668, 43], [664, 48, 668, 46, "faceWidth"], [664, 57, 668, 55], [664, 60, 668, 58, "padding"], [664, 67, 668, 65], [664, 68, 668, 66], [665, 12, 669, 10], [665, 18, 669, 16, "paddedY"], [665, 25, 669, 23], [665, 28, 669, 26, "Math"], [665, 32, 669, 30], [665, 33, 669, 31, "max"], [665, 36, 669, 34], [665, 37, 669, 35], [665, 38, 669, 36], [665, 40, 669, 38, "faceY"], [665, 45, 669, 43], [665, 48, 669, 46, "faceHeight"], [665, 58, 669, 56], [665, 61, 669, 59, "padding"], [665, 68, 669, 66], [665, 69, 669, 67], [666, 12, 670, 10], [666, 18, 670, 16, "<PERSON><PERSON><PERSON><PERSON>"], [666, 29, 670, 27], [666, 32, 670, 30, "Math"], [666, 36, 670, 34], [666, 37, 670, 35, "min"], [666, 40, 670, 38], [666, 41, 670, 39, "img"], [666, 44, 670, 42], [666, 45, 670, 43, "width"], [666, 50, 670, 48], [666, 53, 670, 51, "paddedX"], [666, 60, 670, 58], [666, 62, 670, 60, "faceWidth"], [666, 71, 670, 69], [666, 75, 670, 73], [666, 76, 670, 74], [666, 79, 670, 77], [666, 80, 670, 78], [666, 83, 670, 81, "padding"], [666, 90, 670, 88], [666, 91, 670, 89], [666, 92, 670, 90], [667, 12, 671, 10], [667, 18, 671, 16, "paddedHeight"], [667, 30, 671, 28], [667, 33, 671, 31, "Math"], [667, 37, 671, 35], [667, 38, 671, 36, "min"], [667, 41, 671, 39], [667, 42, 671, 40, "img"], [667, 45, 671, 43], [667, 46, 671, 44, "height"], [667, 52, 671, 50], [667, 55, 671, 53, "paddedY"], [667, 62, 671, 60], [667, 64, 671, 62, "faceHeight"], [667, 74, 671, 72], [667, 78, 671, 76], [667, 79, 671, 77], [667, 82, 671, 80], [667, 83, 671, 81], [667, 86, 671, 84, "padding"], [667, 93, 671, 91], [667, 94, 671, 92], [667, 95, 671, 93], [668, 12, 673, 10, "console"], [668, 19, 673, 17], [668, 20, 673, 18, "log"], [668, 23, 673, 21], [668, 24, 673, 22], [668, 60, 673, 58, "index"], [668, 65, 673, 63], [668, 68, 673, 66], [668, 69, 673, 67], [668, 72, 673, 70], [668, 74, 673, 72], [669, 14, 674, 12, "original"], [669, 22, 674, 20], [669, 24, 674, 22], [670, 16, 674, 24, "x"], [670, 17, 674, 25], [670, 19, 674, 27, "Math"], [670, 23, 674, 31], [670, 24, 674, 32, "round"], [670, 29, 674, 37], [670, 30, 674, 38, "faceX"], [670, 35, 674, 43], [670, 36, 674, 44], [671, 16, 674, 46, "y"], [671, 17, 674, 47], [671, 19, 674, 49, "Math"], [671, 23, 674, 53], [671, 24, 674, 54, "round"], [671, 29, 674, 59], [671, 30, 674, 60, "faceY"], [671, 35, 674, 65], [671, 36, 674, 66], [672, 16, 674, 68, "w"], [672, 17, 674, 69], [672, 19, 674, 71, "Math"], [672, 23, 674, 75], [672, 24, 674, 76, "round"], [672, 29, 674, 81], [672, 30, 674, 82, "faceWidth"], [672, 39, 674, 91], [672, 40, 674, 92], [673, 16, 674, 94, "h"], [673, 17, 674, 95], [673, 19, 674, 97, "Math"], [673, 23, 674, 101], [673, 24, 674, 102, "round"], [673, 29, 674, 107], [673, 30, 674, 108, "faceHeight"], [673, 40, 674, 118], [674, 14, 674, 120], [674, 15, 674, 121], [675, 14, 675, 12, "padded"], [675, 20, 675, 18], [675, 22, 675, 20], [676, 16, 675, 22, "x"], [676, 17, 675, 23], [676, 19, 675, 25, "Math"], [676, 23, 675, 29], [676, 24, 675, 30, "round"], [676, 29, 675, 35], [676, 30, 675, 36, "paddedX"], [676, 37, 675, 43], [676, 38, 675, 44], [677, 16, 675, 46, "y"], [677, 17, 675, 47], [677, 19, 675, 49, "Math"], [677, 23, 675, 53], [677, 24, 675, 54, "round"], [677, 29, 675, 59], [677, 30, 675, 60, "paddedY"], [677, 37, 675, 67], [677, 38, 675, 68], [678, 16, 675, 70, "w"], [678, 17, 675, 71], [678, 19, 675, 73, "Math"], [678, 23, 675, 77], [678, 24, 675, 78, "round"], [678, 29, 675, 83], [678, 30, 675, 84, "<PERSON><PERSON><PERSON><PERSON>"], [678, 41, 675, 95], [678, 42, 675, 96], [679, 16, 675, 98, "h"], [679, 17, 675, 99], [679, 19, 675, 101, "Math"], [679, 23, 675, 105], [679, 24, 675, 106, "round"], [679, 29, 675, 111], [679, 30, 675, 112, "paddedHeight"], [679, 42, 675, 124], [680, 14, 675, 126], [681, 12, 676, 10], [681, 13, 676, 11], [681, 14, 676, 12], [683, 12, 678, 10], [684, 12, 679, 10, "console"], [684, 19, 679, 17], [684, 20, 679, 18, "log"], [684, 23, 679, 21], [684, 24, 679, 22], [684, 70, 679, 68], [684, 72, 679, 70], [685, 14, 680, 12, "width"], [685, 19, 680, 17], [685, 21, 680, 19, "canvas"], [685, 27, 680, 25], [685, 28, 680, 26, "width"], [685, 33, 680, 31], [686, 14, 681, 12, "height"], [686, 20, 681, 18], [686, 22, 681, 20, "canvas"], [686, 28, 681, 26], [686, 29, 681, 27, "height"], [686, 35, 681, 33], [687, 14, 682, 12, "contextValid"], [687, 26, 682, 24], [687, 28, 682, 26], [687, 29, 682, 27], [687, 30, 682, 28, "ctx"], [688, 12, 683, 10], [688, 13, 683, 11], [688, 14, 683, 12], [690, 12, 685, 10], [691, 12, 686, 10, "applyStrongBlur"], [691, 27, 686, 25], [691, 28, 686, 26, "ctx"], [691, 31, 686, 29], [691, 33, 686, 31, "paddedX"], [691, 40, 686, 38], [691, 42, 686, 40, "paddedY"], [691, 49, 686, 47], [691, 51, 686, 49, "<PERSON><PERSON><PERSON><PERSON>"], [691, 62, 686, 60], [691, 64, 686, 62, "paddedHeight"], [691, 76, 686, 74], [691, 77, 686, 75], [693, 12, 688, 10], [694, 12, 689, 10, "console"], [694, 19, 689, 17], [694, 20, 689, 18, "log"], [694, 23, 689, 21], [694, 24, 689, 22], [694, 102, 689, 100], [694, 103, 689, 101], [696, 12, 691, 10], [697, 12, 692, 10], [697, 18, 692, 16, "testImageData"], [697, 31, 692, 29], [697, 34, 692, 32, "ctx"], [697, 37, 692, 35], [697, 38, 692, 36, "getImageData"], [697, 50, 692, 48], [697, 51, 692, 49, "paddedX"], [697, 58, 692, 56], [697, 61, 692, 59], [697, 63, 692, 61], [697, 65, 692, 63, "paddedY"], [697, 72, 692, 70], [697, 75, 692, 73], [697, 77, 692, 75], [697, 79, 692, 77], [697, 81, 692, 79], [697, 83, 692, 81], [697, 85, 692, 83], [697, 86, 692, 84], [698, 12, 693, 10, "console"], [698, 19, 693, 17], [698, 20, 693, 18, "log"], [698, 23, 693, 21], [698, 24, 693, 22], [698, 70, 693, 68], [698, 72, 693, 70], [699, 14, 694, 12, "firstPixel"], [699, 24, 694, 22], [699, 26, 694, 24], [699, 27, 694, 25, "testImageData"], [699, 40, 694, 38], [699, 41, 694, 39, "data"], [699, 45, 694, 43], [699, 46, 694, 44], [699, 47, 694, 45], [699, 48, 694, 46], [699, 50, 694, 48, "testImageData"], [699, 63, 694, 61], [699, 64, 694, 62, "data"], [699, 68, 694, 66], [699, 69, 694, 67], [699, 70, 694, 68], [699, 71, 694, 69], [699, 73, 694, 71, "testImageData"], [699, 86, 694, 84], [699, 87, 694, 85, "data"], [699, 91, 694, 89], [699, 92, 694, 90], [699, 93, 694, 91], [699, 94, 694, 92], [699, 95, 694, 93], [700, 14, 695, 12, "secondPixel"], [700, 25, 695, 23], [700, 27, 695, 25], [700, 28, 695, 26, "testImageData"], [700, 41, 695, 39], [700, 42, 695, 40, "data"], [700, 46, 695, 44], [700, 47, 695, 45], [700, 48, 695, 46], [700, 49, 695, 47], [700, 51, 695, 49, "testImageData"], [700, 64, 695, 62], [700, 65, 695, 63, "data"], [700, 69, 695, 67], [700, 70, 695, 68], [700, 71, 695, 69], [700, 72, 695, 70], [700, 74, 695, 72, "testImageData"], [700, 87, 695, 85], [700, 88, 695, 86, "data"], [700, 92, 695, 90], [700, 93, 695, 91], [700, 94, 695, 92], [700, 95, 695, 93], [701, 12, 696, 10], [701, 13, 696, 11], [701, 14, 696, 12], [702, 12, 698, 10, "console"], [702, 19, 698, 17], [702, 20, 698, 18, "log"], [702, 23, 698, 21], [702, 24, 698, 22], [702, 50, 698, 48, "index"], [702, 55, 698, 53], [702, 58, 698, 56], [702, 59, 698, 57], [702, 79, 698, 77], [702, 80, 698, 78], [703, 10, 699, 8], [703, 11, 699, 9], [703, 12, 699, 10], [704, 10, 701, 8, "console"], [704, 17, 701, 15], [704, 18, 701, 16, "log"], [704, 21, 701, 19], [704, 22, 701, 20], [704, 48, 701, 46, "detectedFaces"], [704, 61, 701, 59], [704, 62, 701, 60, "length"], [704, 68, 701, 66], [704, 104, 701, 102], [704, 105, 701, 103], [705, 8, 702, 6], [705, 9, 702, 7], [705, 15, 702, 13], [706, 10, 703, 8, "console"], [706, 17, 703, 15], [706, 18, 703, 16, "log"], [706, 21, 703, 19], [706, 22, 703, 20], [706, 109, 703, 107], [706, 110, 703, 108], [707, 10, 704, 8], [708, 10, 705, 8, "applyFallbackFaceBlur"], [708, 31, 705, 29], [708, 32, 705, 30, "ctx"], [708, 35, 705, 33], [708, 37, 705, 35, "img"], [708, 40, 705, 38], [708, 41, 705, 39, "width"], [708, 46, 705, 44], [708, 48, 705, 46, "img"], [708, 51, 705, 49], [708, 52, 705, 50, "height"], [708, 58, 705, 56], [708, 59, 705, 57], [709, 8, 706, 6], [710, 8, 708, 6, "setProcessingProgress"], [710, 29, 708, 27], [710, 30, 708, 28], [710, 32, 708, 30], [710, 33, 708, 31], [712, 8, 710, 6], [713, 8, 711, 6, "console"], [713, 15, 711, 13], [713, 16, 711, 14, "log"], [713, 19, 711, 17], [713, 20, 711, 18], [713, 85, 711, 83], [713, 86, 711, 84], [714, 8, 712, 6], [714, 14, 712, 12, "blurredImageBlob"], [714, 30, 712, 28], [714, 33, 712, 31], [714, 39, 712, 37], [714, 43, 712, 41, "Promise"], [714, 50, 712, 48], [714, 51, 712, 56, "resolve"], [714, 58, 712, 63], [714, 62, 712, 68], [715, 10, 713, 8, "canvas"], [715, 16, 713, 14], [715, 17, 713, 15, "toBlob"], [715, 23, 713, 21], [715, 24, 713, 23, "blob"], [715, 28, 713, 27], [715, 32, 713, 32, "resolve"], [715, 39, 713, 39], [715, 40, 713, 40, "blob"], [715, 44, 713, 45], [715, 45, 713, 46], [715, 47, 713, 48], [715, 59, 713, 60], [715, 61, 713, 62], [715, 64, 713, 65], [715, 65, 713, 66], [716, 8, 714, 6], [716, 9, 714, 7], [716, 10, 714, 8], [717, 8, 716, 6], [717, 14, 716, 12, "blurredImageUrl"], [717, 29, 716, 27], [717, 32, 716, 30, "URL"], [717, 35, 716, 33], [717, 36, 716, 34, "createObjectURL"], [717, 51, 716, 49], [717, 52, 716, 50, "blurredImageBlob"], [717, 68, 716, 66], [717, 69, 716, 67], [718, 8, 717, 6, "console"], [718, 15, 717, 13], [718, 16, 717, 14, "log"], [718, 19, 717, 17], [718, 20, 717, 18], [718, 66, 717, 64], [718, 68, 717, 66, "blurredImageUrl"], [718, 83, 717, 81], [718, 84, 717, 82, "substring"], [718, 93, 717, 91], [718, 94, 717, 92], [718, 95, 717, 93], [718, 97, 717, 95], [718, 99, 717, 97], [718, 100, 717, 98], [718, 103, 717, 101], [718, 108, 717, 106], [718, 109, 717, 107], [720, 8, 719, 6], [721, 8, 720, 6, "setCapturedPhoto"], [721, 24, 720, 22], [721, 25, 720, 23, "blurredImageUrl"], [721, 40, 720, 38], [721, 41, 720, 39], [722, 8, 721, 6, "console"], [722, 15, 721, 13], [722, 16, 721, 14, "log"], [722, 19, 721, 17], [722, 20, 721, 18], [722, 87, 721, 85], [722, 88, 721, 86], [723, 8, 723, 6, "setProcessingProgress"], [723, 29, 723, 27], [723, 30, 723, 28], [723, 33, 723, 31], [723, 34, 723, 32], [725, 8, 725, 6], [726, 8, 726, 6], [726, 14, 726, 12, "completeProcessing"], [726, 32, 726, 30], [726, 33, 726, 31, "blurredImageUrl"], [726, 48, 726, 46], [726, 49, 726, 47], [727, 6, 728, 4], [727, 7, 728, 5], [727, 8, 728, 6], [727, 15, 728, 13, "error"], [727, 20, 728, 18], [727, 22, 728, 20], [728, 8, 729, 6, "console"], [728, 15, 729, 13], [728, 16, 729, 14, "error"], [728, 21, 729, 19], [728, 22, 729, 20], [728, 86, 729, 84], [728, 88, 729, 86, "error"], [728, 93, 729, 91], [728, 94, 729, 92], [729, 8, 730, 6, "console"], [729, 15, 730, 13], [729, 16, 730, 14, "error"], [729, 21, 730, 19], [729, 22, 730, 20], [729, 55, 730, 53], [729, 57, 730, 55, "error"], [729, 62, 730, 60], [729, 63, 730, 61, "stack"], [729, 68, 730, 66], [729, 69, 730, 67], [730, 8, 731, 6, "console"], [730, 15, 731, 13], [730, 16, 731, 14, "error"], [730, 21, 731, 19], [730, 22, 731, 20], [730, 57, 731, 55], [730, 59, 731, 57], [731, 10, 732, 8, "name"], [731, 14, 732, 12], [731, 16, 732, 14, "error"], [731, 21, 732, 19], [731, 22, 732, 20, "name"], [731, 26, 732, 24], [732, 10, 733, 8, "message"], [732, 17, 733, 15], [732, 19, 733, 17, "error"], [732, 24, 733, 22], [732, 25, 733, 23, "message"], [732, 32, 733, 30], [733, 10, 734, 8, "photoUri"], [734, 8, 735, 6], [734, 9, 735, 7], [734, 10, 735, 8], [735, 8, 736, 6, "setErrorMessage"], [735, 23, 736, 21], [735, 24, 736, 22], [735, 50, 736, 48], [735, 51, 736, 49], [736, 8, 737, 6, "setProcessingState"], [736, 26, 737, 24], [736, 27, 737, 25], [736, 34, 737, 32], [736, 35, 737, 33], [737, 6, 738, 4], [738, 4, 739, 2], [738, 5, 739, 3], [740, 4, 741, 2], [741, 4, 742, 2], [741, 10, 742, 8, "completeProcessing"], [741, 28, 742, 26], [741, 31, 742, 29], [741, 37, 742, 36, "blurredImageUrl"], [741, 52, 742, 59], [741, 56, 742, 64], [742, 6, 743, 4], [742, 10, 743, 8], [743, 8, 744, 6, "setProcessingState"], [743, 26, 744, 24], [743, 27, 744, 25], [743, 37, 744, 35], [743, 38, 744, 36], [745, 8, 746, 6], [746, 8, 747, 6], [746, 14, 747, 12, "timestamp"], [746, 23, 747, 21], [746, 26, 747, 24, "Date"], [746, 30, 747, 28], [746, 31, 747, 29, "now"], [746, 34, 747, 32], [746, 35, 747, 33], [746, 36, 747, 34], [747, 8, 748, 6], [747, 14, 748, 12, "result"], [747, 20, 748, 18], [747, 23, 748, 21], [748, 10, 749, 8, "imageUrl"], [748, 18, 749, 16], [748, 20, 749, 18, "blurredImageUrl"], [748, 35, 749, 33], [749, 10, 750, 8, "localUri"], [749, 18, 750, 16], [749, 20, 750, 18, "blurredImageUrl"], [749, 35, 750, 33], [750, 10, 751, 8, "challengeCode"], [750, 23, 751, 21], [750, 25, 751, 23, "challengeCode"], [750, 38, 751, 36], [750, 42, 751, 40], [750, 44, 751, 42], [751, 10, 752, 8, "timestamp"], [751, 19, 752, 17], [752, 10, 753, 8, "jobId"], [752, 15, 753, 13], [752, 17, 753, 15], [752, 27, 753, 25, "timestamp"], [752, 36, 753, 34], [752, 38, 753, 36], [753, 10, 754, 8, "status"], [753, 16, 754, 14], [753, 18, 754, 16], [754, 8, 755, 6], [754, 9, 755, 7], [755, 8, 757, 6, "console"], [755, 15, 757, 13], [755, 16, 757, 14, "log"], [755, 19, 757, 17], [755, 20, 757, 18], [755, 100, 757, 98], [755, 102, 757, 100], [756, 10, 758, 8, "imageUrl"], [756, 18, 758, 16], [756, 20, 758, 18, "blurredImageUrl"], [756, 35, 758, 33], [756, 36, 758, 34, "substring"], [756, 45, 758, 43], [756, 46, 758, 44], [756, 47, 758, 45], [756, 49, 758, 47], [756, 51, 758, 49], [756, 52, 758, 50], [756, 55, 758, 53], [756, 60, 758, 58], [757, 10, 759, 8, "timestamp"], [757, 19, 759, 17], [758, 10, 760, 8, "jobId"], [758, 15, 760, 13], [758, 17, 760, 15, "result"], [758, 23, 760, 21], [758, 24, 760, 22, "jobId"], [759, 8, 761, 6], [759, 9, 761, 7], [759, 10, 761, 8], [761, 8, 763, 6], [762, 8, 764, 6, "onComplete"], [762, 18, 764, 16], [762, 19, 764, 17, "result"], [762, 25, 764, 23], [762, 26, 764, 24], [763, 6, 766, 4], [763, 7, 766, 5], [763, 8, 766, 6], [763, 15, 766, 13, "error"], [763, 20, 766, 18], [763, 22, 766, 20], [764, 8, 767, 6, "console"], [764, 15, 767, 13], [764, 16, 767, 14, "error"], [764, 21, 767, 19], [764, 22, 767, 20], [764, 57, 767, 55], [764, 59, 767, 57, "error"], [764, 64, 767, 62], [764, 65, 767, 63], [765, 8, 768, 6, "setErrorMessage"], [765, 23, 768, 21], [765, 24, 768, 22], [765, 56, 768, 54], [765, 57, 768, 55], [766, 8, 769, 6, "setProcessingState"], [766, 26, 769, 24], [766, 27, 769, 25], [766, 34, 769, 32], [766, 35, 769, 33], [767, 6, 770, 4], [768, 4, 771, 2], [768, 5, 771, 3], [770, 4, 773, 2], [771, 4, 774, 2], [771, 10, 774, 8, "triggerServerProcessing"], [771, 33, 774, 31], [771, 36, 774, 34], [771, 42, 774, 34, "triggerServerProcessing"], [771, 43, 774, 41, "privateImageUrl"], [771, 58, 774, 64], [771, 60, 774, 66, "timestamp"], [771, 69, 774, 83], [771, 74, 774, 88], [772, 6, 775, 4], [772, 10, 775, 8], [773, 8, 776, 6, "console"], [773, 15, 776, 13], [773, 16, 776, 14, "log"], [773, 19, 776, 17], [773, 20, 776, 18], [773, 74, 776, 72], [773, 76, 776, 74, "privateImageUrl"], [773, 91, 776, 89], [773, 92, 776, 90], [774, 8, 777, 6, "setProcessingState"], [774, 26, 777, 24], [774, 27, 777, 25], [774, 39, 777, 37], [774, 40, 777, 38], [775, 8, 778, 6, "setProcessingProgress"], [775, 29, 778, 27], [775, 30, 778, 28], [775, 32, 778, 30], [775, 33, 778, 31], [776, 8, 780, 6], [776, 14, 780, 12, "requestBody"], [776, 25, 780, 23], [776, 28, 780, 26], [777, 10, 781, 8, "imageUrl"], [777, 18, 781, 16], [777, 20, 781, 18, "privateImageUrl"], [777, 35, 781, 33], [778, 10, 782, 8, "userId"], [778, 16, 782, 14], [779, 10, 783, 8, "requestId"], [779, 19, 783, 17], [780, 10, 784, 8, "timestamp"], [780, 19, 784, 17], [781, 10, 785, 8, "platform"], [781, 18, 785, 16], [781, 20, 785, 18], [782, 8, 786, 6], [782, 9, 786, 7], [783, 8, 788, 6, "console"], [783, 15, 788, 13], [783, 16, 788, 14, "log"], [783, 19, 788, 17], [783, 20, 788, 18], [783, 65, 788, 63], [783, 67, 788, 65, "requestBody"], [783, 78, 788, 76], [783, 79, 788, 77], [785, 8, 790, 6], [786, 8, 791, 6], [786, 14, 791, 12, "response"], [786, 22, 791, 20], [786, 25, 791, 23], [786, 31, 791, 29, "fetch"], [786, 36, 791, 34], [786, 37, 791, 35], [786, 40, 791, 38, "API_BASE_URL"], [786, 52, 791, 50], [786, 72, 791, 70], [786, 74, 791, 72], [787, 10, 792, 8, "method"], [787, 16, 792, 14], [787, 18, 792, 16], [787, 24, 792, 22], [788, 10, 793, 8, "headers"], [788, 17, 793, 15], [788, 19, 793, 17], [789, 12, 794, 10], [789, 26, 794, 24], [789, 28, 794, 26], [789, 46, 794, 44], [790, 12, 795, 10], [790, 27, 795, 25], [790, 29, 795, 27], [790, 39, 795, 37], [790, 45, 795, 43, "getAuthToken"], [790, 57, 795, 55], [790, 58, 795, 56], [790, 59, 795, 57], [791, 10, 796, 8], [791, 11, 796, 9], [792, 10, 797, 8, "body"], [792, 14, 797, 12], [792, 16, 797, 14, "JSON"], [792, 20, 797, 18], [792, 21, 797, 19, "stringify"], [792, 30, 797, 28], [792, 31, 797, 29, "requestBody"], [792, 42, 797, 40], [793, 8, 798, 6], [793, 9, 798, 7], [793, 10, 798, 8], [794, 8, 800, 6], [794, 12, 800, 10], [794, 13, 800, 11, "response"], [794, 21, 800, 19], [794, 22, 800, 20, "ok"], [794, 24, 800, 22], [794, 26, 800, 24], [795, 10, 801, 8], [795, 16, 801, 14, "errorText"], [795, 25, 801, 23], [795, 28, 801, 26], [795, 34, 801, 32, "response"], [795, 42, 801, 40], [795, 43, 801, 41, "text"], [795, 47, 801, 45], [795, 48, 801, 46], [795, 49, 801, 47], [796, 10, 802, 8, "console"], [796, 17, 802, 15], [796, 18, 802, 16, "error"], [796, 23, 802, 21], [796, 24, 802, 22], [796, 68, 802, 66], [796, 70, 802, 68, "response"], [796, 78, 802, 76], [796, 79, 802, 77, "status"], [796, 85, 802, 83], [796, 87, 802, 85, "errorText"], [796, 96, 802, 94], [796, 97, 802, 95], [797, 10, 803, 8], [797, 16, 803, 14], [797, 20, 803, 18, "Error"], [797, 25, 803, 23], [797, 26, 803, 24], [797, 48, 803, 46, "response"], [797, 56, 803, 54], [797, 57, 803, 55, "status"], [797, 63, 803, 61], [797, 67, 803, 65, "response"], [797, 75, 803, 73], [797, 76, 803, 74, "statusText"], [797, 86, 803, 84], [797, 88, 803, 86], [797, 89, 803, 87], [798, 8, 804, 6], [799, 8, 806, 6], [799, 14, 806, 12, "result"], [799, 20, 806, 18], [799, 23, 806, 21], [799, 29, 806, 27, "response"], [799, 37, 806, 35], [799, 38, 806, 36, "json"], [799, 42, 806, 40], [799, 43, 806, 41], [799, 44, 806, 42], [800, 8, 807, 6, "console"], [800, 15, 807, 13], [800, 16, 807, 14, "log"], [800, 19, 807, 17], [800, 20, 807, 18], [800, 68, 807, 66], [800, 70, 807, 68, "result"], [800, 76, 807, 74], [800, 77, 807, 75], [801, 8, 809, 6], [801, 12, 809, 10], [801, 13, 809, 11, "result"], [801, 19, 809, 17], [801, 20, 809, 18, "jobId"], [801, 25, 809, 23], [801, 27, 809, 25], [802, 10, 810, 8], [802, 16, 810, 14], [802, 20, 810, 18, "Error"], [802, 25, 810, 23], [802, 26, 810, 24], [802, 70, 810, 68], [802, 71, 810, 69], [803, 8, 811, 6], [805, 8, 813, 6], [806, 8, 814, 6], [806, 14, 814, 12, "pollForCompletion"], [806, 31, 814, 29], [806, 32, 814, 30, "result"], [806, 38, 814, 36], [806, 39, 814, 37, "jobId"], [806, 44, 814, 42], [806, 46, 814, 44, "timestamp"], [806, 55, 814, 53], [806, 56, 814, 54], [807, 6, 815, 4], [807, 7, 815, 5], [807, 8, 815, 6], [807, 15, 815, 13, "error"], [807, 20, 815, 18], [807, 22, 815, 20], [808, 8, 816, 6, "console"], [808, 15, 816, 13], [808, 16, 816, 14, "error"], [808, 21, 816, 19], [808, 22, 816, 20], [808, 57, 816, 55], [808, 59, 816, 57, "error"], [808, 64, 816, 62], [808, 65, 816, 63], [809, 8, 817, 6, "setErrorMessage"], [809, 23, 817, 21], [809, 24, 817, 22], [809, 52, 817, 50, "error"], [809, 57, 817, 55], [809, 58, 817, 56, "message"], [809, 65, 817, 63], [809, 67, 817, 65], [809, 68, 817, 66], [810, 8, 818, 6, "setProcessingState"], [810, 26, 818, 24], [810, 27, 818, 25], [810, 34, 818, 32], [810, 35, 818, 33], [811, 6, 819, 4], [812, 4, 820, 2], [812, 5, 820, 3], [813, 4, 821, 2], [814, 4, 822, 2], [814, 10, 822, 8, "pollForCompletion"], [814, 27, 822, 25], [814, 30, 822, 28], [814, 36, 822, 28, "pollForCompletion"], [814, 37, 822, 35, "jobId"], [814, 42, 822, 48], [814, 44, 822, 50, "timestamp"], [814, 53, 822, 67], [814, 55, 822, 69, "attempts"], [814, 63, 822, 77], [814, 66, 822, 80], [814, 67, 822, 81], [814, 72, 822, 86], [815, 6, 823, 4], [815, 12, 823, 10, "MAX_ATTEMPTS"], [815, 24, 823, 22], [815, 27, 823, 25], [815, 29, 823, 27], [815, 30, 823, 28], [815, 31, 823, 29], [816, 6, 824, 4], [816, 12, 824, 10, "POLL_INTERVAL"], [816, 25, 824, 23], [816, 28, 824, 26], [816, 32, 824, 30], [816, 33, 824, 31], [816, 34, 824, 32], [818, 6, 826, 4, "console"], [818, 13, 826, 11], [818, 14, 826, 12, "log"], [818, 17, 826, 15], [818, 18, 826, 16], [818, 53, 826, 51, "attempts"], [818, 61, 826, 59], [818, 64, 826, 62], [818, 65, 826, 63], [818, 69, 826, 67, "MAX_ATTEMPTS"], [818, 81, 826, 79], [818, 93, 826, 91, "jobId"], [818, 98, 826, 96], [818, 100, 826, 98], [818, 101, 826, 99], [819, 6, 828, 4], [819, 10, 828, 8, "attempts"], [819, 18, 828, 16], [819, 22, 828, 20, "MAX_ATTEMPTS"], [819, 34, 828, 32], [819, 36, 828, 34], [820, 8, 829, 6, "console"], [820, 15, 829, 13], [820, 16, 829, 14, "error"], [820, 21, 829, 19], [820, 22, 829, 20], [820, 75, 829, 73], [820, 76, 829, 74], [821, 8, 830, 6, "setErrorMessage"], [821, 23, 830, 21], [821, 24, 830, 22], [821, 63, 830, 61], [821, 64, 830, 62], [822, 8, 831, 6, "setProcessingState"], [822, 26, 831, 24], [822, 27, 831, 25], [822, 34, 831, 32], [822, 35, 831, 33], [823, 8, 832, 6], [824, 6, 833, 4], [825, 6, 835, 4], [825, 10, 835, 8], [826, 8, 836, 6], [826, 14, 836, 12, "response"], [826, 22, 836, 20], [826, 25, 836, 23], [826, 31, 836, 29, "fetch"], [826, 36, 836, 34], [826, 37, 836, 35], [826, 40, 836, 38, "API_BASE_URL"], [826, 52, 836, 50], [826, 75, 836, 73, "jobId"], [826, 80, 836, 78], [826, 82, 836, 80], [826, 84, 836, 82], [827, 10, 837, 8, "headers"], [827, 17, 837, 15], [827, 19, 837, 17], [828, 12, 838, 10], [828, 27, 838, 25], [828, 29, 838, 27], [828, 39, 838, 37], [828, 45, 838, 43, "getAuthToken"], [828, 57, 838, 55], [828, 58, 838, 56], [828, 59, 838, 57], [829, 10, 839, 8], [830, 8, 840, 6], [830, 9, 840, 7], [830, 10, 840, 8], [831, 8, 842, 6], [831, 12, 842, 10], [831, 13, 842, 11, "response"], [831, 21, 842, 19], [831, 22, 842, 20, "ok"], [831, 24, 842, 22], [831, 26, 842, 24], [832, 10, 843, 8], [832, 16, 843, 14], [832, 20, 843, 18, "Error"], [832, 25, 843, 23], [832, 26, 843, 24], [832, 34, 843, 32, "response"], [832, 42, 843, 40], [832, 43, 843, 41, "status"], [832, 49, 843, 47], [832, 54, 843, 52, "response"], [832, 62, 843, 60], [832, 63, 843, 61, "statusText"], [832, 73, 843, 71], [832, 75, 843, 73], [832, 76, 843, 74], [833, 8, 844, 6], [834, 8, 846, 6], [834, 14, 846, 12, "status"], [834, 20, 846, 18], [834, 23, 846, 21], [834, 29, 846, 27, "response"], [834, 37, 846, 35], [834, 38, 846, 36, "json"], [834, 42, 846, 40], [834, 43, 846, 41], [834, 44, 846, 42], [835, 8, 847, 6, "console"], [835, 15, 847, 13], [835, 16, 847, 14, "log"], [835, 19, 847, 17], [835, 20, 847, 18], [835, 54, 847, 52], [835, 56, 847, 54, "status"], [835, 62, 847, 60], [835, 63, 847, 61], [836, 8, 849, 6], [836, 12, 849, 10, "status"], [836, 18, 849, 16], [836, 19, 849, 17, "status"], [836, 25, 849, 23], [836, 30, 849, 28], [836, 41, 849, 39], [836, 43, 849, 41], [837, 10, 850, 8, "console"], [837, 17, 850, 15], [837, 18, 850, 16, "log"], [837, 21, 850, 19], [837, 22, 850, 20], [837, 73, 850, 71], [837, 74, 850, 72], [838, 10, 851, 8, "setProcessingProgress"], [838, 31, 851, 29], [838, 32, 851, 30], [838, 35, 851, 33], [838, 36, 851, 34], [839, 10, 852, 8, "setProcessingState"], [839, 28, 852, 26], [839, 29, 852, 27], [839, 40, 852, 38], [839, 41, 852, 39], [840, 10, 853, 8], [841, 10, 854, 8], [841, 16, 854, 14, "result"], [841, 22, 854, 20], [841, 25, 854, 23], [842, 12, 855, 10, "imageUrl"], [842, 20, 855, 18], [842, 22, 855, 20, "status"], [842, 28, 855, 26], [842, 29, 855, 27, "publicUrl"], [842, 38, 855, 36], [843, 12, 855, 38], [844, 12, 856, 10, "localUri"], [844, 20, 856, 18], [844, 22, 856, 20, "capturedPhoto"], [844, 35, 856, 33], [844, 39, 856, 37, "status"], [844, 45, 856, 43], [844, 46, 856, 44, "publicUrl"], [844, 55, 856, 53], [845, 12, 856, 55], [846, 12, 857, 10, "challengeCode"], [846, 25, 857, 23], [846, 27, 857, 25, "challengeCode"], [846, 40, 857, 38], [846, 44, 857, 42], [846, 46, 857, 44], [847, 12, 858, 10, "timestamp"], [847, 21, 858, 19], [848, 12, 859, 10, "processingStatus"], [848, 28, 859, 26], [848, 30, 859, 28], [849, 10, 860, 8], [849, 11, 860, 9], [850, 10, 861, 8, "console"], [850, 17, 861, 15], [850, 18, 861, 16, "log"], [850, 21, 861, 19], [850, 22, 861, 20], [850, 57, 861, 55], [850, 59, 861, 57, "result"], [850, 65, 861, 63], [850, 66, 861, 64], [851, 10, 862, 8, "onComplete"], [851, 20, 862, 18], [851, 21, 862, 19, "result"], [851, 27, 862, 25], [851, 28, 862, 26], [852, 10, 863, 8], [853, 8, 864, 6], [853, 9, 864, 7], [853, 15, 864, 13], [853, 19, 864, 17, "status"], [853, 25, 864, 23], [853, 26, 864, 24, "status"], [853, 32, 864, 30], [853, 37, 864, 35], [853, 45, 864, 43], [853, 47, 864, 45], [854, 10, 865, 8, "console"], [854, 17, 865, 15], [854, 18, 865, 16, "error"], [854, 23, 865, 21], [854, 24, 865, 22], [854, 60, 865, 58], [854, 62, 865, 60, "status"], [854, 68, 865, 66], [854, 69, 865, 67, "error"], [854, 74, 865, 72], [854, 75, 865, 73], [855, 10, 866, 8], [855, 16, 866, 14], [855, 20, 866, 18, "Error"], [855, 25, 866, 23], [855, 26, 866, 24, "status"], [855, 32, 866, 30], [855, 33, 866, 31, "error"], [855, 38, 866, 36], [855, 42, 866, 40], [855, 61, 866, 59], [855, 62, 866, 60], [856, 8, 867, 6], [856, 9, 867, 7], [856, 15, 867, 13], [857, 10, 868, 8], [858, 10, 869, 8], [858, 16, 869, 14, "progressValue"], [858, 29, 869, 27], [858, 32, 869, 30], [858, 34, 869, 32], [858, 37, 869, 36, "attempts"], [858, 45, 869, 44], [858, 48, 869, 47, "MAX_ATTEMPTS"], [858, 60, 869, 59], [858, 63, 869, 63], [858, 65, 869, 65], [859, 10, 870, 8, "console"], [859, 17, 870, 15], [859, 18, 870, 16, "log"], [859, 21, 870, 19], [859, 22, 870, 20], [859, 71, 870, 69, "progressValue"], [859, 84, 870, 82], [859, 87, 870, 85], [859, 88, 870, 86], [860, 10, 871, 8, "setProcessingProgress"], [860, 31, 871, 29], [860, 32, 871, 30, "progressValue"], [860, 45, 871, 43], [860, 46, 871, 44], [861, 10, 873, 8, "setTimeout"], [861, 20, 873, 18], [861, 21, 873, 19], [861, 27, 873, 25], [862, 12, 874, 10, "pollForCompletion"], [862, 29, 874, 27], [862, 30, 874, 28, "jobId"], [862, 35, 874, 33], [862, 37, 874, 35, "timestamp"], [862, 46, 874, 44], [862, 48, 874, 46, "attempts"], [862, 56, 874, 54], [862, 59, 874, 57], [862, 60, 874, 58], [862, 61, 874, 59], [863, 10, 875, 8], [863, 11, 875, 9], [863, 13, 875, 11, "POLL_INTERVAL"], [863, 26, 875, 24], [863, 27, 875, 25], [864, 8, 876, 6], [865, 6, 877, 4], [865, 7, 877, 5], [865, 8, 877, 6], [865, 15, 877, 13, "error"], [865, 20, 877, 18], [865, 22, 877, 20], [866, 8, 878, 6, "console"], [866, 15, 878, 13], [866, 16, 878, 14, "error"], [866, 21, 878, 19], [866, 22, 878, 20], [866, 54, 878, 52], [866, 56, 878, 54, "error"], [866, 61, 878, 59], [866, 62, 878, 60], [867, 8, 879, 6, "setErrorMessage"], [867, 23, 879, 21], [867, 24, 879, 22], [867, 62, 879, 60, "error"], [867, 67, 879, 65], [867, 68, 879, 66, "message"], [867, 75, 879, 73], [867, 77, 879, 75], [867, 78, 879, 76], [868, 8, 880, 6, "setProcessingState"], [868, 26, 880, 24], [868, 27, 880, 25], [868, 34, 880, 32], [868, 35, 880, 33], [869, 6, 881, 4], [870, 4, 882, 2], [870, 5, 882, 3], [871, 4, 883, 2], [872, 4, 884, 2], [872, 10, 884, 8, "getAuthToken"], [872, 22, 884, 20], [872, 25, 884, 23], [872, 31, 884, 23, "getAuthToken"], [872, 32, 884, 23], [872, 37, 884, 52], [873, 6, 885, 4], [874, 6, 886, 4], [875, 6, 887, 4], [875, 13, 887, 11], [875, 30, 887, 28], [876, 4, 888, 2], [876, 5, 888, 3], [878, 4, 890, 2], [879, 4, 891, 2], [879, 10, 891, 8, "retryCapture"], [879, 22, 891, 20], [879, 25, 891, 23], [879, 29, 891, 23, "useCallback"], [879, 47, 891, 34], [879, 49, 891, 35], [879, 55, 891, 41], [880, 6, 892, 4, "console"], [880, 13, 892, 11], [880, 14, 892, 12, "log"], [880, 17, 892, 15], [880, 18, 892, 16], [880, 55, 892, 53], [880, 56, 892, 54], [881, 6, 893, 4, "setProcessingState"], [881, 24, 893, 22], [881, 25, 893, 23], [881, 31, 893, 29], [881, 32, 893, 30], [882, 6, 894, 4, "setErrorMessage"], [882, 21, 894, 19], [882, 22, 894, 20], [882, 24, 894, 22], [882, 25, 894, 23], [883, 6, 895, 4, "setCapturedPhoto"], [883, 22, 895, 20], [883, 23, 895, 21], [883, 25, 895, 23], [883, 26, 895, 24], [884, 6, 896, 4, "setProcessingProgress"], [884, 27, 896, 25], [884, 28, 896, 26], [884, 29, 896, 27], [884, 30, 896, 28], [885, 4, 897, 2], [885, 5, 897, 3], [885, 7, 897, 5], [885, 9, 897, 7], [885, 10, 897, 8], [886, 4, 898, 2], [887, 4, 899, 2], [887, 8, 899, 2, "useEffect"], [887, 24, 899, 11], [887, 26, 899, 12], [887, 32, 899, 18], [888, 6, 900, 4, "console"], [888, 13, 900, 11], [888, 14, 900, 12, "log"], [888, 17, 900, 15], [888, 18, 900, 16], [888, 53, 900, 51], [888, 55, 900, 53, "permission"], [888, 65, 900, 63], [888, 66, 900, 64], [889, 6, 901, 4], [889, 10, 901, 8, "permission"], [889, 20, 901, 18], [889, 22, 901, 20], [890, 8, 902, 6, "console"], [890, 15, 902, 13], [890, 16, 902, 14, "log"], [890, 19, 902, 17], [890, 20, 902, 18], [890, 57, 902, 55], [890, 59, 902, 57, "permission"], [890, 69, 902, 67], [890, 70, 902, 68, "granted"], [890, 77, 902, 75], [890, 78, 902, 76], [891, 6, 903, 4], [892, 4, 904, 2], [892, 5, 904, 3], [892, 7, 904, 5], [892, 8, 904, 6, "permission"], [892, 18, 904, 16], [892, 19, 904, 17], [892, 20, 904, 18], [893, 4, 905, 2], [894, 4, 906, 2], [894, 8, 906, 6], [894, 9, 906, 7, "permission"], [894, 19, 906, 17], [894, 21, 906, 19], [895, 6, 907, 4, "console"], [895, 13, 907, 11], [895, 14, 907, 12, "log"], [895, 17, 907, 15], [895, 18, 907, 16], [895, 67, 907, 65], [895, 68, 907, 66], [896, 6, 908, 4], [896, 26, 909, 6], [896, 30, 909, 6, "_jsxDevRuntime"], [896, 44, 909, 6], [896, 45, 909, 6, "jsxDEV"], [896, 51, 909, 6], [896, 53, 909, 7, "_View"], [896, 58, 909, 7], [896, 59, 909, 7, "default"], [896, 66, 909, 11], [897, 8, 909, 12, "style"], [897, 13, 909, 17], [897, 15, 909, 19, "styles"], [897, 21, 909, 25], [897, 22, 909, 26, "container"], [897, 31, 909, 36], [898, 8, 909, 36, "children"], [898, 16, 909, 36], [898, 32, 910, 8], [898, 36, 910, 8, "_jsxDevRuntime"], [898, 50, 910, 8], [898, 51, 910, 8, "jsxDEV"], [898, 57, 910, 8], [898, 59, 910, 9, "_ActivityIndicator"], [898, 77, 910, 9], [898, 78, 910, 9, "default"], [898, 85, 910, 26], [899, 10, 910, 27, "size"], [899, 14, 910, 31], [899, 16, 910, 32], [899, 23, 910, 39], [900, 10, 910, 40, "color"], [900, 15, 910, 45], [900, 17, 910, 46], [901, 8, 910, 55], [902, 10, 910, 55, "fileName"], [902, 18, 910, 55], [902, 20, 910, 55, "_jsxFileName"], [902, 32, 910, 55], [903, 10, 910, 55, "lineNumber"], [903, 20, 910, 55], [904, 10, 910, 55, "columnNumber"], [904, 22, 910, 55], [905, 8, 910, 55], [905, 15, 910, 57], [905, 16, 910, 58], [905, 31, 911, 8], [905, 35, 911, 8, "_jsxDevRuntime"], [905, 49, 911, 8], [905, 50, 911, 8, "jsxDEV"], [905, 56, 911, 8], [905, 58, 911, 9, "_Text"], [905, 63, 911, 9], [905, 64, 911, 9, "default"], [905, 71, 911, 13], [906, 10, 911, 14, "style"], [906, 15, 911, 19], [906, 17, 911, 21, "styles"], [906, 23, 911, 27], [906, 24, 911, 28, "loadingText"], [906, 35, 911, 40], [907, 10, 911, 40, "children"], [907, 18, 911, 40], [907, 20, 911, 41], [908, 8, 911, 58], [909, 10, 911, 58, "fileName"], [909, 18, 911, 58], [909, 20, 911, 58, "_jsxFileName"], [909, 32, 911, 58], [910, 10, 911, 58, "lineNumber"], [910, 20, 911, 58], [911, 10, 911, 58, "columnNumber"], [911, 22, 911, 58], [912, 8, 911, 58], [912, 15, 911, 64], [912, 16, 911, 65], [913, 6, 911, 65], [914, 8, 911, 65, "fileName"], [914, 16, 911, 65], [914, 18, 911, 65, "_jsxFileName"], [914, 30, 911, 65], [915, 8, 911, 65, "lineNumber"], [915, 18, 911, 65], [916, 8, 911, 65, "columnNumber"], [916, 20, 911, 65], [917, 6, 911, 65], [917, 13, 912, 12], [917, 14, 912, 13], [918, 4, 914, 2], [919, 4, 915, 2], [919, 8, 915, 6], [919, 9, 915, 7, "permission"], [919, 19, 915, 17], [919, 20, 915, 18, "granted"], [919, 27, 915, 25], [919, 29, 915, 27], [920, 6, 916, 4, "console"], [920, 13, 916, 11], [920, 14, 916, 12, "log"], [920, 17, 916, 15], [920, 18, 916, 16], [920, 93, 916, 91], [920, 94, 916, 92], [921, 6, 917, 4], [921, 26, 918, 6], [921, 30, 918, 6, "_jsxDevRuntime"], [921, 44, 918, 6], [921, 45, 918, 6, "jsxDEV"], [921, 51, 918, 6], [921, 53, 918, 7, "_View"], [921, 58, 918, 7], [921, 59, 918, 7, "default"], [921, 66, 918, 11], [922, 8, 918, 12, "style"], [922, 13, 918, 17], [922, 15, 918, 19, "styles"], [922, 21, 918, 25], [922, 22, 918, 26, "container"], [922, 31, 918, 36], [923, 8, 918, 36, "children"], [923, 16, 918, 36], [923, 31, 919, 8], [923, 35, 919, 8, "_jsxDevRuntime"], [923, 49, 919, 8], [923, 50, 919, 8, "jsxDEV"], [923, 56, 919, 8], [923, 58, 919, 9, "_View"], [923, 63, 919, 9], [923, 64, 919, 9, "default"], [923, 71, 919, 13], [924, 10, 919, 14, "style"], [924, 15, 919, 19], [924, 17, 919, 21, "styles"], [924, 23, 919, 27], [924, 24, 919, 28, "permissionContent"], [924, 41, 919, 46], [925, 10, 919, 46, "children"], [925, 18, 919, 46], [925, 34, 920, 10], [925, 38, 920, 10, "_jsxDevRuntime"], [925, 52, 920, 10], [925, 53, 920, 10, "jsxDEV"], [925, 59, 920, 10], [925, 61, 920, 11, "_lucideReactNative"], [925, 79, 920, 11], [925, 80, 920, 11, "Camera"], [925, 86, 920, 21], [926, 12, 920, 22, "size"], [926, 16, 920, 26], [926, 18, 920, 28], [926, 20, 920, 31], [927, 12, 920, 32, "color"], [927, 17, 920, 37], [927, 19, 920, 38], [928, 10, 920, 47], [929, 12, 920, 47, "fileName"], [929, 20, 920, 47], [929, 22, 920, 47, "_jsxFileName"], [929, 34, 920, 47], [930, 12, 920, 47, "lineNumber"], [930, 22, 920, 47], [931, 12, 920, 47, "columnNumber"], [931, 24, 920, 47], [932, 10, 920, 47], [932, 17, 920, 49], [932, 18, 920, 50], [932, 33, 921, 10], [932, 37, 921, 10, "_jsxDevRuntime"], [932, 51, 921, 10], [932, 52, 921, 10, "jsxDEV"], [932, 58, 921, 10], [932, 60, 921, 11, "_Text"], [932, 65, 921, 11], [932, 66, 921, 11, "default"], [932, 73, 921, 15], [933, 12, 921, 16, "style"], [933, 17, 921, 21], [933, 19, 921, 23, "styles"], [933, 25, 921, 29], [933, 26, 921, 30, "permissionTitle"], [933, 41, 921, 46], [934, 12, 921, 46, "children"], [934, 20, 921, 46], [934, 22, 921, 47], [935, 10, 921, 73], [936, 12, 921, 73, "fileName"], [936, 20, 921, 73], [936, 22, 921, 73, "_jsxFileName"], [936, 34, 921, 73], [937, 12, 921, 73, "lineNumber"], [937, 22, 921, 73], [938, 12, 921, 73, "columnNumber"], [938, 24, 921, 73], [939, 10, 921, 73], [939, 17, 921, 79], [939, 18, 921, 80], [939, 33, 922, 10], [939, 37, 922, 10, "_jsxDevRuntime"], [939, 51, 922, 10], [939, 52, 922, 10, "jsxDEV"], [939, 58, 922, 10], [939, 60, 922, 11, "_Text"], [939, 65, 922, 11], [939, 66, 922, 11, "default"], [939, 73, 922, 15], [940, 12, 922, 16, "style"], [940, 17, 922, 21], [940, 19, 922, 23, "styles"], [940, 25, 922, 29], [940, 26, 922, 30, "permissionDescription"], [940, 47, 922, 52], [941, 12, 922, 52, "children"], [941, 20, 922, 52], [941, 22, 922, 53], [942, 10, 925, 10], [943, 12, 925, 10, "fileName"], [943, 20, 925, 10], [943, 22, 925, 10, "_jsxFileName"], [943, 34, 925, 10], [944, 12, 925, 10, "lineNumber"], [944, 22, 925, 10], [945, 12, 925, 10, "columnNumber"], [945, 24, 925, 10], [946, 10, 925, 10], [946, 17, 925, 16], [946, 18, 925, 17], [946, 33, 926, 10], [946, 37, 926, 10, "_jsxDevRuntime"], [946, 51, 926, 10], [946, 52, 926, 10, "jsxDEV"], [946, 58, 926, 10], [946, 60, 926, 11, "_TouchableOpacity"], [946, 77, 926, 11], [946, 78, 926, 11, "default"], [946, 85, 926, 27], [947, 12, 926, 28, "onPress"], [947, 19, 926, 35], [947, 21, 926, 37, "requestPermission"], [947, 38, 926, 55], [948, 12, 926, 56, "style"], [948, 17, 926, 61], [948, 19, 926, 63, "styles"], [948, 25, 926, 69], [948, 26, 926, 70, "primaryButton"], [948, 39, 926, 84], [949, 12, 926, 84, "children"], [949, 20, 926, 84], [949, 35, 927, 12], [949, 39, 927, 12, "_jsxDevRuntime"], [949, 53, 927, 12], [949, 54, 927, 12, "jsxDEV"], [949, 60, 927, 12], [949, 62, 927, 13, "_Text"], [949, 67, 927, 13], [949, 68, 927, 13, "default"], [949, 75, 927, 17], [950, 14, 927, 18, "style"], [950, 19, 927, 23], [950, 21, 927, 25, "styles"], [950, 27, 927, 31], [950, 28, 927, 32, "primaryButtonText"], [950, 45, 927, 50], [951, 14, 927, 50, "children"], [951, 22, 927, 50], [951, 24, 927, 51], [952, 12, 927, 67], [953, 14, 927, 67, "fileName"], [953, 22, 927, 67], [953, 24, 927, 67, "_jsxFileName"], [953, 36, 927, 67], [954, 14, 927, 67, "lineNumber"], [954, 24, 927, 67], [955, 14, 927, 67, "columnNumber"], [955, 26, 927, 67], [956, 12, 927, 67], [956, 19, 927, 73], [957, 10, 927, 74], [958, 12, 927, 74, "fileName"], [958, 20, 927, 74], [958, 22, 927, 74, "_jsxFileName"], [958, 34, 927, 74], [959, 12, 927, 74, "lineNumber"], [959, 22, 927, 74], [960, 12, 927, 74, "columnNumber"], [960, 24, 927, 74], [961, 10, 927, 74], [961, 17, 928, 28], [961, 18, 928, 29], [961, 33, 929, 10], [961, 37, 929, 10, "_jsxDevRuntime"], [961, 51, 929, 10], [961, 52, 929, 10, "jsxDEV"], [961, 58, 929, 10], [961, 60, 929, 11, "_TouchableOpacity"], [961, 77, 929, 11], [961, 78, 929, 11, "default"], [961, 85, 929, 27], [962, 12, 929, 28, "onPress"], [962, 19, 929, 35], [962, 21, 929, 37, "onCancel"], [962, 29, 929, 46], [963, 12, 929, 47, "style"], [963, 17, 929, 52], [963, 19, 929, 54, "styles"], [963, 25, 929, 60], [963, 26, 929, 61, "secondaryButton"], [963, 41, 929, 77], [964, 12, 929, 77, "children"], [964, 20, 929, 77], [964, 35, 930, 12], [964, 39, 930, 12, "_jsxDevRuntime"], [964, 53, 930, 12], [964, 54, 930, 12, "jsxDEV"], [964, 60, 930, 12], [964, 62, 930, 13, "_Text"], [964, 67, 930, 13], [964, 68, 930, 13, "default"], [964, 75, 930, 17], [965, 14, 930, 18, "style"], [965, 19, 930, 23], [965, 21, 930, 25, "styles"], [965, 27, 930, 31], [965, 28, 930, 32, "secondaryButtonText"], [965, 47, 930, 52], [966, 14, 930, 52, "children"], [966, 22, 930, 52], [966, 24, 930, 53], [967, 12, 930, 59], [968, 14, 930, 59, "fileName"], [968, 22, 930, 59], [968, 24, 930, 59, "_jsxFileName"], [968, 36, 930, 59], [969, 14, 930, 59, "lineNumber"], [969, 24, 930, 59], [970, 14, 930, 59, "columnNumber"], [970, 26, 930, 59], [971, 12, 930, 59], [971, 19, 930, 65], [972, 10, 930, 66], [973, 12, 930, 66, "fileName"], [973, 20, 930, 66], [973, 22, 930, 66, "_jsxFileName"], [973, 34, 930, 66], [974, 12, 930, 66, "lineNumber"], [974, 22, 930, 66], [975, 12, 930, 66, "columnNumber"], [975, 24, 930, 66], [976, 10, 930, 66], [976, 17, 931, 28], [976, 18, 931, 29], [977, 8, 931, 29], [978, 10, 931, 29, "fileName"], [978, 18, 931, 29], [978, 20, 931, 29, "_jsxFileName"], [978, 32, 931, 29], [979, 10, 931, 29, "lineNumber"], [979, 20, 931, 29], [980, 10, 931, 29, "columnNumber"], [980, 22, 931, 29], [981, 8, 931, 29], [981, 15, 932, 14], [982, 6, 932, 15], [983, 8, 932, 15, "fileName"], [983, 16, 932, 15], [983, 18, 932, 15, "_jsxFileName"], [983, 30, 932, 15], [984, 8, 932, 15, "lineNumber"], [984, 18, 932, 15], [985, 8, 932, 15, "columnNumber"], [985, 20, 932, 15], [986, 6, 932, 15], [986, 13, 933, 12], [986, 14, 933, 13], [987, 4, 935, 2], [988, 4, 936, 2], [989, 4, 937, 2, "console"], [989, 11, 937, 9], [989, 12, 937, 10, "log"], [989, 15, 937, 13], [989, 16, 937, 14], [989, 55, 937, 53], [989, 56, 937, 54], [990, 4, 939, 2], [990, 24, 940, 4], [990, 28, 940, 4, "_jsxDevRuntime"], [990, 42, 940, 4], [990, 43, 940, 4, "jsxDEV"], [990, 49, 940, 4], [990, 51, 940, 5, "_View"], [990, 56, 940, 5], [990, 57, 940, 5, "default"], [990, 64, 940, 9], [991, 6, 940, 10, "style"], [991, 11, 940, 15], [991, 13, 940, 17, "styles"], [991, 19, 940, 23], [991, 20, 940, 24, "container"], [991, 29, 940, 34], [992, 6, 940, 34, "children"], [992, 14, 940, 34], [992, 30, 942, 6], [992, 34, 942, 6, "_jsxDevRuntime"], [992, 48, 942, 6], [992, 49, 942, 6, "jsxDEV"], [992, 55, 942, 6], [992, 57, 942, 7, "_View"], [992, 62, 942, 7], [992, 63, 942, 7, "default"], [992, 70, 942, 11], [993, 8, 942, 12, "style"], [993, 13, 942, 17], [993, 15, 942, 19, "styles"], [993, 21, 942, 25], [993, 22, 942, 26, "cameraContainer"], [993, 37, 942, 42], [994, 8, 942, 43, "id"], [994, 10, 942, 45], [994, 12, 942, 46], [994, 29, 942, 63], [995, 8, 942, 63, "children"], [995, 16, 942, 63], [995, 32, 943, 8], [995, 36, 943, 8, "_jsxDevRuntime"], [995, 50, 943, 8], [995, 51, 943, 8, "jsxDEV"], [995, 57, 943, 8], [995, 59, 943, 9, "_expoCamera"], [995, 70, 943, 9], [995, 71, 943, 9, "CameraView"], [995, 81, 943, 19], [996, 10, 944, 10, "ref"], [996, 13, 944, 13], [996, 15, 944, 15, "cameraRef"], [996, 24, 944, 25], [997, 10, 945, 10, "style"], [997, 15, 945, 15], [997, 17, 945, 17], [997, 18, 946, 12, "styles"], [997, 24, 946, 18], [997, 25, 946, 19, "camera"], [997, 31, 946, 25], [997, 33, 947, 12], [998, 12, 948, 14, "backgroundColor"], [998, 27, 948, 29], [998, 29, 948, 31], [998, 38, 948, 40], [999, 12, 949, 14], [1000, 12, 950, 14, "opacity"], [1000, 19, 950, 21], [1000, 21, 950, 23], [1001, 10, 951, 12], [1001, 11, 951, 13], [1001, 12, 952, 12], [1002, 10, 953, 10, "facing"], [1002, 16, 953, 16], [1002, 18, 953, 17], [1002, 24, 953, 23], [1003, 10, 954, 10, "onLayout"], [1003, 18, 954, 18], [1003, 20, 954, 21, "e"], [1003, 21, 954, 22], [1003, 25, 954, 27], [1004, 12, 955, 12, "console"], [1004, 19, 955, 19], [1004, 20, 955, 20, "log"], [1004, 23, 955, 23], [1004, 24, 955, 24], [1004, 56, 955, 56], [1004, 58, 955, 58, "e"], [1004, 59, 955, 59], [1004, 60, 955, 60, "nativeEvent"], [1004, 71, 955, 71], [1004, 72, 955, 72, "layout"], [1004, 78, 955, 78], [1004, 79, 955, 79], [1005, 12, 956, 12, "setViewSize"], [1005, 23, 956, 23], [1005, 24, 956, 24], [1006, 14, 956, 26, "width"], [1006, 19, 956, 31], [1006, 21, 956, 33, "e"], [1006, 22, 956, 34], [1006, 23, 956, 35, "nativeEvent"], [1006, 34, 956, 46], [1006, 35, 956, 47, "layout"], [1006, 41, 956, 53], [1006, 42, 956, 54, "width"], [1006, 47, 956, 59], [1007, 14, 956, 61, "height"], [1007, 20, 956, 67], [1007, 22, 956, 69, "e"], [1007, 23, 956, 70], [1007, 24, 956, 71, "nativeEvent"], [1007, 35, 956, 82], [1007, 36, 956, 83, "layout"], [1007, 42, 956, 89], [1007, 43, 956, 90, "height"], [1008, 12, 956, 97], [1008, 13, 956, 98], [1008, 14, 956, 99], [1009, 10, 957, 10], [1009, 11, 957, 12], [1010, 10, 958, 10, "onCameraReady"], [1010, 23, 958, 23], [1010, 25, 958, 25, "onCameraReady"], [1010, 26, 958, 25], [1010, 31, 958, 31], [1011, 12, 959, 12, "console"], [1011, 19, 959, 19], [1011, 20, 959, 20, "log"], [1011, 23, 959, 23], [1011, 24, 959, 24], [1011, 55, 959, 55], [1011, 56, 959, 56], [1012, 12, 960, 12, "setIsCameraReady"], [1012, 28, 960, 28], [1012, 29, 960, 29], [1012, 33, 960, 33], [1012, 34, 960, 34], [1012, 35, 960, 35], [1012, 36, 960, 36], [1013, 12, 961, 12], [1014, 12, 962, 12, "setTimeout"], [1014, 22, 962, 22], [1014, 23, 962, 23], [1014, 29, 962, 29], [1015, 14, 963, 14, "setPreviewBlurEnabled"], [1015, 35, 963, 35], [1015, 36, 963, 36], [1015, 40, 963, 40], [1015, 41, 963, 41], [1016, 12, 964, 12], [1016, 13, 964, 13], [1016, 15, 964, 15], [1016, 19, 964, 19], [1016, 20, 964, 20], [1016, 21, 964, 21], [1016, 22, 964, 22], [1017, 10, 965, 10], [1017, 11, 965, 12], [1018, 10, 966, 10, "onMountError"], [1018, 22, 966, 22], [1018, 24, 966, 25, "error"], [1018, 29, 966, 30], [1018, 33, 966, 35], [1019, 12, 967, 12, "console"], [1019, 19, 967, 19], [1019, 20, 967, 20, "error"], [1019, 25, 967, 25], [1019, 26, 967, 26], [1019, 63, 967, 63], [1019, 65, 967, 65, "error"], [1019, 70, 967, 70], [1019, 71, 967, 71], [1020, 12, 968, 12, "setErrorMessage"], [1020, 27, 968, 27], [1020, 28, 968, 28], [1020, 57, 968, 57], [1020, 58, 968, 58], [1021, 12, 969, 12, "setProcessingState"], [1021, 30, 969, 30], [1021, 31, 969, 31], [1021, 38, 969, 38], [1021, 39, 969, 39], [1022, 10, 970, 10], [1023, 8, 970, 12], [1024, 10, 970, 12, "fileName"], [1024, 18, 970, 12], [1024, 20, 970, 12, "_jsxFileName"], [1024, 32, 970, 12], [1025, 10, 970, 12, "lineNumber"], [1025, 20, 970, 12], [1026, 10, 970, 12, "columnNumber"], [1026, 22, 970, 12], [1027, 8, 970, 12], [1027, 15, 971, 9], [1027, 16, 971, 10], [1027, 18, 973, 9], [1027, 19, 973, 10, "isCameraReady"], [1027, 32, 973, 23], [1027, 49, 974, 10], [1027, 53, 974, 10, "_jsxDevRuntime"], [1027, 67, 974, 10], [1027, 68, 974, 10, "jsxDEV"], [1027, 74, 974, 10], [1027, 76, 974, 11, "_View"], [1027, 81, 974, 11], [1027, 82, 974, 11, "default"], [1027, 89, 974, 15], [1028, 10, 974, 16, "style"], [1028, 15, 974, 21], [1028, 17, 974, 23], [1028, 18, 974, 24, "StyleSheet"], [1028, 37, 974, 34], [1028, 38, 974, 35, "absoluteFill"], [1028, 50, 974, 47], [1028, 52, 974, 49], [1029, 12, 974, 51, "backgroundColor"], [1029, 27, 974, 66], [1029, 29, 974, 68], [1029, 49, 974, 88], [1030, 12, 974, 90, "justifyContent"], [1030, 26, 974, 104], [1030, 28, 974, 106], [1030, 36, 974, 114], [1031, 12, 974, 116, "alignItems"], [1031, 22, 974, 126], [1031, 24, 974, 128], [1031, 32, 974, 136], [1032, 12, 974, 138, "zIndex"], [1032, 18, 974, 144], [1032, 20, 974, 146], [1033, 10, 974, 151], [1033, 11, 974, 152], [1033, 12, 974, 154], [1034, 10, 974, 154, "children"], [1034, 18, 974, 154], [1034, 33, 975, 12], [1034, 37, 975, 12, "_jsxDevRuntime"], [1034, 51, 975, 12], [1034, 52, 975, 12, "jsxDEV"], [1034, 58, 975, 12], [1034, 60, 975, 13, "_View"], [1034, 65, 975, 13], [1034, 66, 975, 13, "default"], [1034, 73, 975, 17], [1035, 12, 975, 18, "style"], [1035, 17, 975, 23], [1035, 19, 975, 25], [1036, 14, 975, 27, "backgroundColor"], [1036, 29, 975, 42], [1036, 31, 975, 44], [1036, 51, 975, 64], [1037, 14, 975, 66, "padding"], [1037, 21, 975, 73], [1037, 23, 975, 75], [1037, 25, 975, 77], [1038, 14, 975, 79, "borderRadius"], [1038, 26, 975, 91], [1038, 28, 975, 93], [1038, 30, 975, 95], [1039, 14, 975, 97, "alignItems"], [1039, 24, 975, 107], [1039, 26, 975, 109], [1040, 12, 975, 118], [1040, 13, 975, 120], [1041, 12, 975, 120, "children"], [1041, 20, 975, 120], [1041, 36, 976, 14], [1041, 40, 976, 14, "_jsxDevRuntime"], [1041, 54, 976, 14], [1041, 55, 976, 14, "jsxDEV"], [1041, 61, 976, 14], [1041, 63, 976, 15, "_ActivityIndicator"], [1041, 81, 976, 15], [1041, 82, 976, 15, "default"], [1041, 89, 976, 32], [1042, 14, 976, 33, "size"], [1042, 18, 976, 37], [1042, 20, 976, 38], [1042, 27, 976, 45], [1043, 14, 976, 46, "color"], [1043, 19, 976, 51], [1043, 21, 976, 52], [1043, 30, 976, 61], [1044, 14, 976, 62, "style"], [1044, 19, 976, 67], [1044, 21, 976, 69], [1045, 16, 976, 71, "marginBottom"], [1045, 28, 976, 83], [1045, 30, 976, 85], [1046, 14, 976, 88], [1047, 12, 976, 90], [1048, 14, 976, 90, "fileName"], [1048, 22, 976, 90], [1048, 24, 976, 90, "_jsxFileName"], [1048, 36, 976, 90], [1049, 14, 976, 90, "lineNumber"], [1049, 24, 976, 90], [1050, 14, 976, 90, "columnNumber"], [1050, 26, 976, 90], [1051, 12, 976, 90], [1051, 19, 976, 92], [1051, 20, 976, 93], [1051, 35, 977, 14], [1051, 39, 977, 14, "_jsxDevRuntime"], [1051, 53, 977, 14], [1051, 54, 977, 14, "jsxDEV"], [1051, 60, 977, 14], [1051, 62, 977, 15, "_Text"], [1051, 67, 977, 15], [1051, 68, 977, 15, "default"], [1051, 75, 977, 19], [1052, 14, 977, 20, "style"], [1052, 19, 977, 25], [1052, 21, 977, 27], [1053, 16, 977, 29, "color"], [1053, 21, 977, 34], [1053, 23, 977, 36], [1053, 29, 977, 42], [1054, 16, 977, 44, "fontSize"], [1054, 24, 977, 52], [1054, 26, 977, 54], [1054, 28, 977, 56], [1055, 16, 977, 58, "fontWeight"], [1055, 26, 977, 68], [1055, 28, 977, 70], [1056, 14, 977, 76], [1056, 15, 977, 78], [1057, 14, 977, 78, "children"], [1057, 22, 977, 78], [1057, 24, 977, 79], [1058, 12, 977, 101], [1059, 14, 977, 101, "fileName"], [1059, 22, 977, 101], [1059, 24, 977, 101, "_jsxFileName"], [1059, 36, 977, 101], [1060, 14, 977, 101, "lineNumber"], [1060, 24, 977, 101], [1061, 14, 977, 101, "columnNumber"], [1061, 26, 977, 101], [1062, 12, 977, 101], [1062, 19, 977, 107], [1062, 20, 977, 108], [1062, 35, 978, 14], [1062, 39, 978, 14, "_jsxDevRuntime"], [1062, 53, 978, 14], [1062, 54, 978, 14, "jsxDEV"], [1062, 60, 978, 14], [1062, 62, 978, 15, "_Text"], [1062, 67, 978, 15], [1062, 68, 978, 15, "default"], [1062, 75, 978, 19], [1063, 14, 978, 20, "style"], [1063, 19, 978, 25], [1063, 21, 978, 27], [1064, 16, 978, 29, "color"], [1064, 21, 978, 34], [1064, 23, 978, 36], [1064, 32, 978, 45], [1065, 16, 978, 47, "fontSize"], [1065, 24, 978, 55], [1065, 26, 978, 57], [1065, 28, 978, 59], [1066, 16, 978, 61, "marginTop"], [1066, 25, 978, 70], [1066, 27, 978, 72], [1067, 14, 978, 74], [1067, 15, 978, 76], [1068, 14, 978, 76, "children"], [1068, 22, 978, 76], [1068, 24, 978, 77], [1069, 12, 978, 88], [1070, 14, 978, 88, "fileName"], [1070, 22, 978, 88], [1070, 24, 978, 88, "_jsxFileName"], [1070, 36, 978, 88], [1071, 14, 978, 88, "lineNumber"], [1071, 24, 978, 88], [1072, 14, 978, 88, "columnNumber"], [1072, 26, 978, 88], [1073, 12, 978, 88], [1073, 19, 978, 94], [1073, 20, 978, 95], [1074, 10, 978, 95], [1075, 12, 978, 95, "fileName"], [1075, 20, 978, 95], [1075, 22, 978, 95, "_jsxFileName"], [1075, 34, 978, 95], [1076, 12, 978, 95, "lineNumber"], [1076, 22, 978, 95], [1077, 12, 978, 95, "columnNumber"], [1077, 24, 978, 95], [1078, 10, 978, 95], [1078, 17, 979, 18], [1079, 8, 979, 19], [1080, 10, 979, 19, "fileName"], [1080, 18, 979, 19], [1080, 20, 979, 19, "_jsxFileName"], [1080, 32, 979, 19], [1081, 10, 979, 19, "lineNumber"], [1081, 20, 979, 19], [1082, 10, 979, 19, "columnNumber"], [1082, 22, 979, 19], [1083, 8, 979, 19], [1083, 15, 980, 16], [1083, 16, 981, 9], [1083, 18, 984, 9, "isCameraReady"], [1083, 31, 984, 22], [1083, 35, 984, 26, "previewBlurEnabled"], [1083, 53, 984, 44], [1083, 57, 984, 48, "viewSize"], [1083, 65, 984, 56], [1083, 66, 984, 57, "width"], [1083, 71, 984, 62], [1083, 74, 984, 65], [1083, 75, 984, 66], [1083, 92, 985, 10], [1083, 96, 985, 10, "_jsxDevRuntime"], [1083, 110, 985, 10], [1083, 111, 985, 10, "jsxDEV"], [1083, 117, 985, 10], [1083, 119, 985, 10, "_jsxDevRuntime"], [1083, 133, 985, 10], [1083, 134, 985, 10, "Fragment"], [1083, 142, 985, 10], [1084, 10, 985, 10, "children"], [1084, 18, 985, 10], [1084, 33, 987, 12], [1084, 37, 987, 12, "_jsxDevRuntime"], [1084, 51, 987, 12], [1084, 52, 987, 12, "jsxDEV"], [1084, 58, 987, 12], [1084, 60, 987, 13, "_BlazeFaceCanvas"], [1084, 76, 987, 13], [1084, 77, 987, 13, "default"], [1084, 84, 987, 28], [1085, 12, 988, 14, "containerId"], [1085, 23, 988, 25], [1085, 25, 988, 26], [1085, 42, 988, 43], [1086, 12, 989, 14, "width"], [1086, 17, 989, 19], [1086, 19, 989, 21, "viewSize"], [1086, 27, 989, 29], [1086, 28, 989, 30, "width"], [1086, 33, 989, 36], [1087, 12, 990, 14, "height"], [1087, 18, 990, 20], [1087, 20, 990, 22, "viewSize"], [1087, 28, 990, 30], [1087, 29, 990, 31, "height"], [1087, 35, 990, 38], [1088, 12, 991, 14, "onReady"], [1088, 19, 991, 21], [1088, 21, 991, 23, "onReady"], [1088, 22, 991, 23], [1088, 27, 991, 29], [1089, 14, 992, 16, "console"], [1089, 21, 992, 23], [1089, 22, 992, 24, "log"], [1089, 25, 992, 27], [1089, 26, 992, 28], [1089, 63, 992, 65], [1089, 64, 992, 66], [1090, 14, 993, 16, "setBlazeReady"], [1090, 27, 993, 29], [1090, 28, 993, 30], [1090, 32, 993, 34], [1090, 33, 993, 35], [1091, 12, 994, 14], [1092, 10, 994, 16], [1093, 12, 994, 16, "fileName"], [1093, 20, 994, 16], [1093, 22, 994, 16, "_jsxFileName"], [1093, 34, 994, 16], [1094, 12, 994, 16, "lineNumber"], [1094, 22, 994, 16], [1095, 12, 994, 16, "columnNumber"], [1095, 24, 994, 16], [1096, 10, 994, 16], [1096, 17, 995, 13], [1097, 8, 995, 14], [1097, 24, 997, 12], [1097, 25, 998, 9], [1097, 27, 1001, 9, "isCameraReady"], [1097, 40, 1001, 22], [1097, 44, 1001, 26], [1097, 45, 1001, 27, "previewBlurEnabled"], [1097, 63, 1001, 45], [1097, 67, 1001, 49, "viewSize"], [1097, 75, 1001, 57], [1097, 76, 1001, 58, "width"], [1097, 81, 1001, 63], [1097, 84, 1001, 66], [1097, 85, 1001, 67], [1097, 102, 1002, 10], [1097, 106, 1002, 10, "_jsxDevRuntime"], [1097, 120, 1002, 10], [1097, 121, 1002, 10, "jsxDEV"], [1097, 127, 1002, 10], [1097, 129, 1002, 11, "_View"], [1097, 134, 1002, 11], [1097, 135, 1002, 11, "default"], [1097, 142, 1002, 15], [1098, 10, 1002, 16, "style"], [1098, 15, 1002, 21], [1098, 17, 1002, 23], [1098, 18, 1002, 24, "StyleSheet"], [1098, 37, 1002, 34], [1098, 38, 1002, 35, "absoluteFill"], [1098, 50, 1002, 47], [1098, 52, 1002, 49], [1099, 12, 1002, 51, "pointerEvents"], [1099, 25, 1002, 64], [1099, 27, 1002, 66], [1100, 10, 1002, 73], [1100, 11, 1002, 74], [1100, 12, 1002, 76], [1101, 10, 1002, 76, "children"], [1101, 18, 1002, 76], [1101, 34, 1004, 12], [1101, 38, 1004, 12, "_jsxDevRuntime"], [1101, 52, 1004, 12], [1101, 53, 1004, 12, "jsxDEV"], [1101, 59, 1004, 12], [1101, 61, 1004, 13, "_expoBlur"], [1101, 70, 1004, 13], [1101, 71, 1004, 13, "BlurView"], [1101, 79, 1004, 21], [1102, 12, 1004, 22, "intensity"], [1102, 21, 1004, 31], [1102, 23, 1004, 33], [1102, 25, 1004, 36], [1103, 12, 1004, 37, "tint"], [1103, 16, 1004, 41], [1103, 18, 1004, 42], [1103, 24, 1004, 48], [1104, 12, 1004, 49, "style"], [1104, 17, 1004, 54], [1104, 19, 1004, 56], [1104, 20, 1004, 57, "styles"], [1104, 26, 1004, 63], [1104, 27, 1004, 64, "blurZone"], [1104, 35, 1004, 72], [1104, 37, 1004, 74], [1105, 14, 1005, 14, "left"], [1105, 18, 1005, 18], [1105, 20, 1005, 20], [1105, 21, 1005, 21], [1106, 14, 1006, 14, "top"], [1106, 17, 1006, 17], [1106, 19, 1006, 19, "viewSize"], [1106, 27, 1006, 27], [1106, 28, 1006, 28, "height"], [1106, 34, 1006, 34], [1106, 37, 1006, 37], [1106, 41, 1006, 41], [1107, 14, 1007, 14, "width"], [1107, 19, 1007, 19], [1107, 21, 1007, 21, "viewSize"], [1107, 29, 1007, 29], [1107, 30, 1007, 30, "width"], [1107, 35, 1007, 35], [1108, 14, 1008, 14, "height"], [1108, 20, 1008, 20], [1108, 22, 1008, 22, "viewSize"], [1108, 30, 1008, 30], [1108, 31, 1008, 31, "height"], [1108, 37, 1008, 37], [1108, 40, 1008, 40], [1108, 43, 1008, 43], [1109, 14, 1009, 14, "borderRadius"], [1109, 26, 1009, 26], [1109, 28, 1009, 28], [1109, 30, 1009, 30], [1110, 14, 1010, 14, "opacity"], [1110, 21, 1010, 21], [1110, 23, 1010, 23], [1111, 12, 1011, 12], [1111, 13, 1011, 13], [1112, 10, 1011, 15], [1113, 12, 1011, 15, "fileName"], [1113, 20, 1011, 15], [1113, 22, 1011, 15, "_jsxFileName"], [1113, 34, 1011, 15], [1114, 12, 1011, 15, "lineNumber"], [1114, 22, 1011, 15], [1115, 12, 1011, 15, "columnNumber"], [1115, 24, 1011, 15], [1116, 10, 1011, 15], [1116, 17, 1011, 17], [1116, 18, 1011, 18], [1116, 33, 1013, 12], [1116, 37, 1013, 12, "_jsxDevRuntime"], [1116, 51, 1013, 12], [1116, 52, 1013, 12, "jsxDEV"], [1116, 58, 1013, 12], [1116, 60, 1013, 13, "_expoBlur"], [1116, 69, 1013, 13], [1116, 70, 1013, 13, "BlurView"], [1116, 78, 1013, 21], [1117, 12, 1013, 22, "intensity"], [1117, 21, 1013, 31], [1117, 23, 1013, 33], [1117, 25, 1013, 36], [1118, 12, 1013, 37, "tint"], [1118, 16, 1013, 41], [1118, 18, 1013, 42], [1118, 24, 1013, 48], [1119, 12, 1013, 49, "style"], [1119, 17, 1013, 54], [1119, 19, 1013, 56], [1119, 20, 1013, 57, "styles"], [1119, 26, 1013, 63], [1119, 27, 1013, 64, "blurZone"], [1119, 35, 1013, 72], [1119, 37, 1013, 74], [1120, 14, 1014, 14, "left"], [1120, 18, 1014, 18], [1120, 20, 1014, 20, "viewSize"], [1120, 28, 1014, 28], [1120, 29, 1014, 29, "width"], [1120, 34, 1014, 34], [1120, 37, 1014, 37], [1120, 40, 1014, 40], [1120, 43, 1014, 44, "viewSize"], [1120, 51, 1014, 52], [1120, 52, 1014, 53, "width"], [1120, 57, 1014, 58], [1120, 60, 1014, 61], [1120, 63, 1014, 65], [1121, 14, 1015, 14, "top"], [1121, 17, 1015, 17], [1121, 19, 1015, 19, "viewSize"], [1121, 27, 1015, 27], [1121, 28, 1015, 28, "height"], [1121, 34, 1015, 34], [1121, 37, 1015, 37], [1121, 40, 1015, 40], [1121, 43, 1015, 44, "viewSize"], [1121, 51, 1015, 52], [1121, 52, 1015, 53, "width"], [1121, 57, 1015, 58], [1121, 60, 1015, 61], [1121, 63, 1015, 65], [1122, 14, 1016, 14, "width"], [1122, 19, 1016, 19], [1122, 21, 1016, 21, "viewSize"], [1122, 29, 1016, 29], [1122, 30, 1016, 30, "width"], [1122, 35, 1016, 35], [1122, 38, 1016, 38], [1122, 41, 1016, 41], [1123, 14, 1017, 14, "height"], [1123, 20, 1017, 20], [1123, 22, 1017, 22, "viewSize"], [1123, 30, 1017, 30], [1123, 31, 1017, 31, "width"], [1123, 36, 1017, 36], [1123, 39, 1017, 39], [1123, 42, 1017, 42], [1124, 14, 1018, 14, "borderRadius"], [1124, 26, 1018, 26], [1124, 28, 1018, 29, "viewSize"], [1124, 36, 1018, 37], [1124, 37, 1018, 38, "width"], [1124, 42, 1018, 43], [1124, 45, 1018, 46], [1124, 48, 1018, 49], [1124, 51, 1018, 53], [1124, 52, 1018, 54], [1125, 14, 1019, 14, "opacity"], [1125, 21, 1019, 21], [1125, 23, 1019, 23], [1126, 12, 1020, 12], [1126, 13, 1020, 13], [1127, 10, 1020, 15], [1128, 12, 1020, 15, "fileName"], [1128, 20, 1020, 15], [1128, 22, 1020, 15, "_jsxFileName"], [1128, 34, 1020, 15], [1129, 12, 1020, 15, "lineNumber"], [1129, 22, 1020, 15], [1130, 12, 1020, 15, "columnNumber"], [1130, 24, 1020, 15], [1131, 10, 1020, 15], [1131, 17, 1020, 17], [1131, 18, 1020, 18], [1131, 20, 1022, 13, "__DEV__"], [1131, 27, 1022, 20], [1131, 44, 1023, 14], [1131, 48, 1023, 14, "_jsxDevRuntime"], [1131, 62, 1023, 14], [1131, 63, 1023, 14, "jsxDEV"], [1131, 69, 1023, 14], [1131, 71, 1023, 15, "_View"], [1131, 76, 1023, 15], [1131, 77, 1023, 15, "default"], [1131, 84, 1023, 19], [1132, 12, 1023, 20, "style"], [1132, 17, 1023, 25], [1132, 19, 1023, 27, "styles"], [1132, 25, 1023, 33], [1132, 26, 1023, 34, "previewChip"], [1132, 37, 1023, 46], [1133, 12, 1023, 46, "children"], [1133, 20, 1023, 46], [1133, 35, 1024, 16], [1133, 39, 1024, 16, "_jsxDevRuntime"], [1133, 53, 1024, 16], [1133, 54, 1024, 16, "jsxDEV"], [1133, 60, 1024, 16], [1133, 62, 1024, 17, "_Text"], [1133, 67, 1024, 17], [1133, 68, 1024, 17, "default"], [1133, 75, 1024, 21], [1134, 14, 1024, 22, "style"], [1134, 19, 1024, 27], [1134, 21, 1024, 29, "styles"], [1134, 27, 1024, 35], [1134, 28, 1024, 36, "previewChipText"], [1134, 43, 1024, 52], [1135, 14, 1024, 52, "children"], [1135, 22, 1024, 52], [1135, 24, 1024, 53], [1136, 12, 1024, 71], [1137, 14, 1024, 71, "fileName"], [1137, 22, 1024, 71], [1137, 24, 1024, 71, "_jsxFileName"], [1137, 36, 1024, 71], [1138, 14, 1024, 71, "lineNumber"], [1138, 24, 1024, 71], [1139, 14, 1024, 71, "columnNumber"], [1139, 26, 1024, 71], [1140, 12, 1024, 71], [1140, 19, 1024, 77], [1141, 10, 1024, 78], [1142, 12, 1024, 78, "fileName"], [1142, 20, 1024, 78], [1142, 22, 1024, 78, "_jsxFileName"], [1142, 34, 1024, 78], [1143, 12, 1024, 78, "lineNumber"], [1143, 22, 1024, 78], [1144, 12, 1024, 78, "columnNumber"], [1144, 24, 1024, 78], [1145, 10, 1024, 78], [1145, 17, 1025, 20], [1145, 18, 1026, 13], [1146, 8, 1026, 13], [1147, 10, 1026, 13, "fileName"], [1147, 18, 1026, 13], [1147, 20, 1026, 13, "_jsxFileName"], [1147, 32, 1026, 13], [1148, 10, 1026, 13, "lineNumber"], [1148, 20, 1026, 13], [1149, 10, 1026, 13, "columnNumber"], [1149, 22, 1026, 13], [1150, 8, 1026, 13], [1150, 15, 1027, 16], [1150, 16, 1028, 9], [1150, 18, 1030, 9, "isCameraReady"], [1150, 31, 1030, 22], [1150, 48, 1031, 10], [1150, 52, 1031, 10, "_jsxDevRuntime"], [1150, 66, 1031, 10], [1150, 67, 1031, 10, "jsxDEV"], [1150, 73, 1031, 10], [1150, 75, 1031, 10, "_jsxDevRuntime"], [1150, 89, 1031, 10], [1150, 90, 1031, 10, "Fragment"], [1150, 98, 1031, 10], [1151, 10, 1031, 10, "children"], [1151, 18, 1031, 10], [1151, 34, 1033, 12], [1151, 38, 1033, 12, "_jsxDevRuntime"], [1151, 52, 1033, 12], [1151, 53, 1033, 12, "jsxDEV"], [1151, 59, 1033, 12], [1151, 61, 1033, 13, "_View"], [1151, 66, 1033, 13], [1151, 67, 1033, 13, "default"], [1151, 74, 1033, 17], [1152, 12, 1033, 18, "style"], [1152, 17, 1033, 23], [1152, 19, 1033, 25, "styles"], [1152, 25, 1033, 31], [1152, 26, 1033, 32, "headerOverlay"], [1152, 39, 1033, 46], [1153, 12, 1033, 46, "children"], [1153, 20, 1033, 46], [1153, 35, 1034, 14], [1153, 39, 1034, 14, "_jsxDevRuntime"], [1153, 53, 1034, 14], [1153, 54, 1034, 14, "jsxDEV"], [1153, 60, 1034, 14], [1153, 62, 1034, 15, "_View"], [1153, 67, 1034, 15], [1153, 68, 1034, 15, "default"], [1153, 75, 1034, 19], [1154, 14, 1034, 20, "style"], [1154, 19, 1034, 25], [1154, 21, 1034, 27, "styles"], [1154, 27, 1034, 33], [1154, 28, 1034, 34, "headerContent"], [1154, 41, 1034, 48], [1155, 14, 1034, 48, "children"], [1155, 22, 1034, 48], [1155, 38, 1035, 16], [1155, 42, 1035, 16, "_jsxDevRuntime"], [1155, 56, 1035, 16], [1155, 57, 1035, 16, "jsxDEV"], [1155, 63, 1035, 16], [1155, 65, 1035, 17, "_View"], [1155, 70, 1035, 17], [1155, 71, 1035, 17, "default"], [1155, 78, 1035, 21], [1156, 16, 1035, 22, "style"], [1156, 21, 1035, 27], [1156, 23, 1035, 29, "styles"], [1156, 29, 1035, 35], [1156, 30, 1035, 36, "headerLeft"], [1156, 40, 1035, 47], [1157, 16, 1035, 47, "children"], [1157, 24, 1035, 47], [1157, 40, 1036, 18], [1157, 44, 1036, 18, "_jsxDevRuntime"], [1157, 58, 1036, 18], [1157, 59, 1036, 18, "jsxDEV"], [1157, 65, 1036, 18], [1157, 67, 1036, 19, "_Text"], [1157, 72, 1036, 19], [1157, 73, 1036, 19, "default"], [1157, 80, 1036, 23], [1158, 18, 1036, 24, "style"], [1158, 23, 1036, 29], [1158, 25, 1036, 31, "styles"], [1158, 31, 1036, 37], [1158, 32, 1036, 38, "headerTitle"], [1158, 43, 1036, 50], [1159, 18, 1036, 50, "children"], [1159, 26, 1036, 50], [1159, 28, 1036, 51], [1160, 16, 1036, 62], [1161, 18, 1036, 62, "fileName"], [1161, 26, 1036, 62], [1161, 28, 1036, 62, "_jsxFileName"], [1161, 40, 1036, 62], [1162, 18, 1036, 62, "lineNumber"], [1162, 28, 1036, 62], [1163, 18, 1036, 62, "columnNumber"], [1163, 30, 1036, 62], [1164, 16, 1036, 62], [1164, 23, 1036, 68], [1164, 24, 1036, 69], [1164, 39, 1037, 18], [1164, 43, 1037, 18, "_jsxDevRuntime"], [1164, 57, 1037, 18], [1164, 58, 1037, 18, "jsxDEV"], [1164, 64, 1037, 18], [1164, 66, 1037, 19, "_View"], [1164, 71, 1037, 19], [1164, 72, 1037, 19, "default"], [1164, 79, 1037, 23], [1165, 18, 1037, 24, "style"], [1165, 23, 1037, 29], [1165, 25, 1037, 31, "styles"], [1165, 31, 1037, 37], [1165, 32, 1037, 38, "subtitleRow"], [1165, 43, 1037, 50], [1166, 18, 1037, 50, "children"], [1166, 26, 1037, 50], [1166, 42, 1038, 20], [1166, 46, 1038, 20, "_jsxDevRuntime"], [1166, 60, 1038, 20], [1166, 61, 1038, 20, "jsxDEV"], [1166, 67, 1038, 20], [1166, 69, 1038, 21, "_Text"], [1166, 74, 1038, 21], [1166, 75, 1038, 21, "default"], [1166, 82, 1038, 25], [1167, 20, 1038, 26, "style"], [1167, 25, 1038, 31], [1167, 27, 1038, 33, "styles"], [1167, 33, 1038, 39], [1167, 34, 1038, 40, "webIcon"], [1167, 41, 1038, 48], [1168, 20, 1038, 48, "children"], [1168, 28, 1038, 48], [1168, 30, 1038, 49], [1169, 18, 1038, 51], [1170, 20, 1038, 51, "fileName"], [1170, 28, 1038, 51], [1170, 30, 1038, 51, "_jsxFileName"], [1170, 42, 1038, 51], [1171, 20, 1038, 51, "lineNumber"], [1171, 30, 1038, 51], [1172, 20, 1038, 51, "columnNumber"], [1172, 32, 1038, 51], [1173, 18, 1038, 51], [1173, 25, 1038, 57], [1173, 26, 1038, 58], [1173, 41, 1039, 20], [1173, 45, 1039, 20, "_jsxDevRuntime"], [1173, 59, 1039, 20], [1173, 60, 1039, 20, "jsxDEV"], [1173, 66, 1039, 20], [1173, 68, 1039, 21, "_Text"], [1173, 73, 1039, 21], [1173, 74, 1039, 21, "default"], [1173, 81, 1039, 25], [1174, 20, 1039, 26, "style"], [1174, 25, 1039, 31], [1174, 27, 1039, 33, "styles"], [1174, 33, 1039, 39], [1174, 34, 1039, 40, "headerSubtitle"], [1174, 48, 1039, 55], [1175, 20, 1039, 55, "children"], [1175, 28, 1039, 55], [1175, 30, 1039, 56], [1176, 18, 1039, 71], [1177, 20, 1039, 71, "fileName"], [1177, 28, 1039, 71], [1177, 30, 1039, 71, "_jsxFileName"], [1177, 42, 1039, 71], [1178, 20, 1039, 71, "lineNumber"], [1178, 30, 1039, 71], [1179, 20, 1039, 71, "columnNumber"], [1179, 32, 1039, 71], [1180, 18, 1039, 71], [1180, 25, 1039, 77], [1180, 26, 1039, 78], [1181, 16, 1039, 78], [1182, 18, 1039, 78, "fileName"], [1182, 26, 1039, 78], [1182, 28, 1039, 78, "_jsxFileName"], [1182, 40, 1039, 78], [1183, 18, 1039, 78, "lineNumber"], [1183, 28, 1039, 78], [1184, 18, 1039, 78, "columnNumber"], [1184, 30, 1039, 78], [1185, 16, 1039, 78], [1185, 23, 1040, 24], [1185, 24, 1040, 25], [1185, 26, 1041, 19, "challengeCode"], [1185, 39, 1041, 32], [1185, 56, 1042, 20], [1185, 60, 1042, 20, "_jsxDevRuntime"], [1185, 74, 1042, 20], [1185, 75, 1042, 20, "jsxDEV"], [1185, 81, 1042, 20], [1185, 83, 1042, 21, "_View"], [1185, 88, 1042, 21], [1185, 89, 1042, 21, "default"], [1185, 96, 1042, 25], [1186, 18, 1042, 26, "style"], [1186, 23, 1042, 31], [1186, 25, 1042, 33, "styles"], [1186, 31, 1042, 39], [1186, 32, 1042, 40, "challengeRow"], [1186, 44, 1042, 53], [1187, 18, 1042, 53, "children"], [1187, 26, 1042, 53], [1187, 42, 1043, 22], [1187, 46, 1043, 22, "_jsxDevRuntime"], [1187, 60, 1043, 22], [1187, 61, 1043, 22, "jsxDEV"], [1187, 67, 1043, 22], [1187, 69, 1043, 23, "_lucideReactNative"], [1187, 87, 1043, 23], [1187, 88, 1043, 23, "Shield"], [1187, 94, 1043, 29], [1188, 20, 1043, 30, "size"], [1188, 24, 1043, 34], [1188, 26, 1043, 36], [1188, 28, 1043, 39], [1189, 20, 1043, 40, "color"], [1189, 25, 1043, 45], [1189, 27, 1043, 46], [1190, 18, 1043, 52], [1191, 20, 1043, 52, "fileName"], [1191, 28, 1043, 52], [1191, 30, 1043, 52, "_jsxFileName"], [1191, 42, 1043, 52], [1192, 20, 1043, 52, "lineNumber"], [1192, 30, 1043, 52], [1193, 20, 1043, 52, "columnNumber"], [1193, 32, 1043, 52], [1194, 18, 1043, 52], [1194, 25, 1043, 54], [1194, 26, 1043, 55], [1194, 41, 1044, 22], [1194, 45, 1044, 22, "_jsxDevRuntime"], [1194, 59, 1044, 22], [1194, 60, 1044, 22, "jsxDEV"], [1194, 66, 1044, 22], [1194, 68, 1044, 23, "_Text"], [1194, 73, 1044, 23], [1194, 74, 1044, 23, "default"], [1194, 81, 1044, 27], [1195, 20, 1044, 28, "style"], [1195, 25, 1044, 33], [1195, 27, 1044, 35, "styles"], [1195, 33, 1044, 41], [1195, 34, 1044, 42, "challengeCode"], [1195, 47, 1044, 56], [1196, 20, 1044, 56, "children"], [1196, 28, 1044, 56], [1196, 30, 1044, 58, "challengeCode"], [1197, 18, 1044, 71], [1198, 20, 1044, 71, "fileName"], [1198, 28, 1044, 71], [1198, 30, 1044, 71, "_jsxFileName"], [1198, 42, 1044, 71], [1199, 20, 1044, 71, "lineNumber"], [1199, 30, 1044, 71], [1200, 20, 1044, 71, "columnNumber"], [1200, 32, 1044, 71], [1201, 18, 1044, 71], [1201, 25, 1044, 78], [1201, 26, 1044, 79], [1202, 16, 1044, 79], [1203, 18, 1044, 79, "fileName"], [1203, 26, 1044, 79], [1203, 28, 1044, 79, "_jsxFileName"], [1203, 40, 1044, 79], [1204, 18, 1044, 79, "lineNumber"], [1204, 28, 1044, 79], [1205, 18, 1044, 79, "columnNumber"], [1205, 30, 1044, 79], [1206, 16, 1044, 79], [1206, 23, 1045, 26], [1206, 24, 1046, 19], [1207, 14, 1046, 19], [1208, 16, 1046, 19, "fileName"], [1208, 24, 1046, 19], [1208, 26, 1046, 19, "_jsxFileName"], [1208, 38, 1046, 19], [1209, 16, 1046, 19, "lineNumber"], [1209, 26, 1046, 19], [1210, 16, 1046, 19, "columnNumber"], [1210, 28, 1046, 19], [1211, 14, 1046, 19], [1211, 21, 1047, 22], [1211, 22, 1047, 23], [1211, 37, 1048, 16], [1211, 41, 1048, 16, "_jsxDevRuntime"], [1211, 55, 1048, 16], [1211, 56, 1048, 16, "jsxDEV"], [1211, 62, 1048, 16], [1211, 64, 1048, 17, "_TouchableOpacity"], [1211, 81, 1048, 17], [1211, 82, 1048, 17, "default"], [1211, 89, 1048, 33], [1212, 16, 1048, 34, "onPress"], [1212, 23, 1048, 41], [1212, 25, 1048, 43, "onCancel"], [1212, 33, 1048, 52], [1213, 16, 1048, 53, "style"], [1213, 21, 1048, 58], [1213, 23, 1048, 60, "styles"], [1213, 29, 1048, 66], [1213, 30, 1048, 67, "closeButton"], [1213, 41, 1048, 79], [1214, 16, 1048, 79, "children"], [1214, 24, 1048, 79], [1214, 39, 1049, 18], [1214, 43, 1049, 18, "_jsxDevRuntime"], [1214, 57, 1049, 18], [1214, 58, 1049, 18, "jsxDEV"], [1214, 64, 1049, 18], [1214, 66, 1049, 19, "_lucideReactNative"], [1214, 84, 1049, 19], [1214, 85, 1049, 19, "X"], [1214, 86, 1049, 20], [1215, 18, 1049, 21, "size"], [1215, 22, 1049, 25], [1215, 24, 1049, 27], [1215, 26, 1049, 30], [1216, 18, 1049, 31, "color"], [1216, 23, 1049, 36], [1216, 25, 1049, 37], [1217, 16, 1049, 43], [1218, 18, 1049, 43, "fileName"], [1218, 26, 1049, 43], [1218, 28, 1049, 43, "_jsxFileName"], [1218, 40, 1049, 43], [1219, 18, 1049, 43, "lineNumber"], [1219, 28, 1049, 43], [1220, 18, 1049, 43, "columnNumber"], [1220, 30, 1049, 43], [1221, 16, 1049, 43], [1221, 23, 1049, 45], [1222, 14, 1049, 46], [1223, 16, 1049, 46, "fileName"], [1223, 24, 1049, 46], [1223, 26, 1049, 46, "_jsxFileName"], [1223, 38, 1049, 46], [1224, 16, 1049, 46, "lineNumber"], [1224, 26, 1049, 46], [1225, 16, 1049, 46, "columnNumber"], [1225, 28, 1049, 46], [1226, 14, 1049, 46], [1226, 21, 1050, 34], [1226, 22, 1050, 35], [1227, 12, 1050, 35], [1228, 14, 1050, 35, "fileName"], [1228, 22, 1050, 35], [1228, 24, 1050, 35, "_jsxFileName"], [1228, 36, 1050, 35], [1229, 14, 1050, 35, "lineNumber"], [1229, 24, 1050, 35], [1230, 14, 1050, 35, "columnNumber"], [1230, 26, 1050, 35], [1231, 12, 1050, 35], [1231, 19, 1051, 20], [1232, 10, 1051, 21], [1233, 12, 1051, 21, "fileName"], [1233, 20, 1051, 21], [1233, 22, 1051, 21, "_jsxFileName"], [1233, 34, 1051, 21], [1234, 12, 1051, 21, "lineNumber"], [1234, 22, 1051, 21], [1235, 12, 1051, 21, "columnNumber"], [1235, 24, 1051, 21], [1236, 10, 1051, 21], [1236, 17, 1052, 18], [1236, 18, 1052, 19], [1236, 33, 1054, 12], [1236, 37, 1054, 12, "_jsxDevRuntime"], [1236, 51, 1054, 12], [1236, 52, 1054, 12, "jsxDEV"], [1236, 58, 1054, 12], [1236, 60, 1054, 13, "_View"], [1236, 65, 1054, 13], [1236, 66, 1054, 13, "default"], [1236, 73, 1054, 17], [1237, 12, 1054, 18, "style"], [1237, 17, 1054, 23], [1237, 19, 1054, 25, "styles"], [1237, 25, 1054, 31], [1237, 26, 1054, 32, "privacyNotice"], [1237, 39, 1054, 46], [1238, 12, 1054, 46, "children"], [1238, 20, 1054, 46], [1238, 36, 1055, 14], [1238, 40, 1055, 14, "_jsxDevRuntime"], [1238, 54, 1055, 14], [1238, 55, 1055, 14, "jsxDEV"], [1238, 61, 1055, 14], [1238, 63, 1055, 15, "_lucideReactNative"], [1238, 81, 1055, 15], [1238, 82, 1055, 15, "Shield"], [1238, 88, 1055, 21], [1239, 14, 1055, 22, "size"], [1239, 18, 1055, 26], [1239, 20, 1055, 28], [1239, 22, 1055, 31], [1240, 14, 1055, 32, "color"], [1240, 19, 1055, 37], [1240, 21, 1055, 38], [1241, 12, 1055, 47], [1242, 14, 1055, 47, "fileName"], [1242, 22, 1055, 47], [1242, 24, 1055, 47, "_jsxFileName"], [1242, 36, 1055, 47], [1243, 14, 1055, 47, "lineNumber"], [1243, 24, 1055, 47], [1244, 14, 1055, 47, "columnNumber"], [1244, 26, 1055, 47], [1245, 12, 1055, 47], [1245, 19, 1055, 49], [1245, 20, 1055, 50], [1245, 35, 1056, 14], [1245, 39, 1056, 14, "_jsxDevRuntime"], [1245, 53, 1056, 14], [1245, 54, 1056, 14, "jsxDEV"], [1245, 60, 1056, 14], [1245, 62, 1056, 15, "_Text"], [1245, 67, 1056, 15], [1245, 68, 1056, 15, "default"], [1245, 75, 1056, 19], [1246, 14, 1056, 20, "style"], [1246, 19, 1056, 25], [1246, 21, 1056, 27, "styles"], [1246, 27, 1056, 33], [1246, 28, 1056, 34, "privacyText"], [1246, 39, 1056, 46], [1247, 14, 1056, 46, "children"], [1247, 22, 1056, 46], [1247, 24, 1056, 47], [1248, 12, 1058, 14], [1249, 14, 1058, 14, "fileName"], [1249, 22, 1058, 14], [1249, 24, 1058, 14, "_jsxFileName"], [1249, 36, 1058, 14], [1250, 14, 1058, 14, "lineNumber"], [1250, 24, 1058, 14], [1251, 14, 1058, 14, "columnNumber"], [1251, 26, 1058, 14], [1252, 12, 1058, 14], [1252, 19, 1058, 20], [1252, 20, 1058, 21], [1253, 10, 1058, 21], [1254, 12, 1058, 21, "fileName"], [1254, 20, 1058, 21], [1254, 22, 1058, 21, "_jsxFileName"], [1254, 34, 1058, 21], [1255, 12, 1058, 21, "lineNumber"], [1255, 22, 1058, 21], [1256, 12, 1058, 21, "columnNumber"], [1256, 24, 1058, 21], [1257, 10, 1058, 21], [1257, 17, 1059, 18], [1257, 18, 1059, 19], [1257, 33, 1061, 12], [1257, 37, 1061, 12, "_jsxDevRuntime"], [1257, 51, 1061, 12], [1257, 52, 1061, 12, "jsxDEV"], [1257, 58, 1061, 12], [1257, 60, 1061, 13, "_View"], [1257, 65, 1061, 13], [1257, 66, 1061, 13, "default"], [1257, 73, 1061, 17], [1258, 12, 1061, 18, "style"], [1258, 17, 1061, 23], [1258, 19, 1061, 25, "styles"], [1258, 25, 1061, 31], [1258, 26, 1061, 32, "footer<PERSON><PERSON><PERSON>"], [1258, 39, 1061, 46], [1259, 12, 1061, 46, "children"], [1259, 20, 1061, 46], [1259, 36, 1062, 14], [1259, 40, 1062, 14, "_jsxDevRuntime"], [1259, 54, 1062, 14], [1259, 55, 1062, 14, "jsxDEV"], [1259, 61, 1062, 14], [1259, 63, 1062, 15, "_Text"], [1259, 68, 1062, 15], [1259, 69, 1062, 15, "default"], [1259, 76, 1062, 19], [1260, 14, 1062, 20, "style"], [1260, 19, 1062, 25], [1260, 21, 1062, 27, "styles"], [1260, 27, 1062, 33], [1260, 28, 1062, 34, "instruction"], [1260, 39, 1062, 46], [1261, 14, 1062, 46, "children"], [1261, 22, 1062, 46], [1261, 24, 1062, 47], [1262, 12, 1064, 14], [1263, 14, 1064, 14, "fileName"], [1263, 22, 1064, 14], [1263, 24, 1064, 14, "_jsxFileName"], [1263, 36, 1064, 14], [1264, 14, 1064, 14, "lineNumber"], [1264, 24, 1064, 14], [1265, 14, 1064, 14, "columnNumber"], [1265, 26, 1064, 14], [1266, 12, 1064, 14], [1266, 19, 1064, 20], [1266, 20, 1064, 21], [1266, 35, 1066, 14], [1266, 39, 1066, 14, "_jsxDevRuntime"], [1266, 53, 1066, 14], [1266, 54, 1066, 14, "jsxDEV"], [1266, 60, 1066, 14], [1266, 62, 1066, 15, "_TouchableOpacity"], [1266, 79, 1066, 15], [1266, 80, 1066, 15, "default"], [1266, 87, 1066, 31], [1267, 14, 1067, 16, "onPress"], [1267, 21, 1067, 23], [1267, 23, 1067, 25, "capturePhoto"], [1267, 35, 1067, 38], [1268, 14, 1068, 16, "disabled"], [1268, 22, 1068, 24], [1268, 24, 1068, 26, "processingState"], [1268, 39, 1068, 41], [1268, 44, 1068, 46], [1268, 50, 1068, 52], [1268, 54, 1068, 56], [1268, 55, 1068, 57, "isCameraReady"], [1268, 68, 1068, 71], [1269, 14, 1069, 16, "style"], [1269, 19, 1069, 21], [1269, 21, 1069, 23], [1269, 22, 1070, 18, "styles"], [1269, 28, 1070, 24], [1269, 29, 1070, 25, "shutterButton"], [1269, 42, 1070, 38], [1269, 44, 1071, 18, "processingState"], [1269, 59, 1071, 33], [1269, 64, 1071, 38], [1269, 70, 1071, 44], [1269, 74, 1071, 48, "styles"], [1269, 80, 1071, 54], [1269, 81, 1071, 55, "shutterButtonDisabled"], [1269, 102, 1071, 76], [1269, 103, 1072, 18], [1270, 14, 1072, 18, "children"], [1270, 22, 1072, 18], [1270, 24, 1074, 17, "processingState"], [1270, 39, 1074, 32], [1270, 44, 1074, 37], [1270, 50, 1074, 43], [1270, 66, 1075, 18], [1270, 70, 1075, 18, "_jsxDevRuntime"], [1270, 84, 1075, 18], [1270, 85, 1075, 18, "jsxDEV"], [1270, 91, 1075, 18], [1270, 93, 1075, 19, "_View"], [1270, 98, 1075, 19], [1270, 99, 1075, 19, "default"], [1270, 106, 1075, 23], [1271, 16, 1075, 24, "style"], [1271, 21, 1075, 29], [1271, 23, 1075, 31, "styles"], [1271, 29, 1075, 37], [1271, 30, 1075, 38, "shutterInner"], [1272, 14, 1075, 51], [1273, 16, 1075, 51, "fileName"], [1273, 24, 1075, 51], [1273, 26, 1075, 51, "_jsxFileName"], [1273, 38, 1075, 51], [1274, 16, 1075, 51, "lineNumber"], [1274, 26, 1075, 51], [1275, 16, 1075, 51, "columnNumber"], [1275, 28, 1075, 51], [1276, 14, 1075, 51], [1276, 21, 1075, 53], [1276, 22, 1075, 54], [1276, 38, 1077, 18], [1276, 42, 1077, 18, "_jsxDevRuntime"], [1276, 56, 1077, 18], [1276, 57, 1077, 18, "jsxDEV"], [1276, 63, 1077, 18], [1276, 65, 1077, 19, "_ActivityIndicator"], [1276, 83, 1077, 19], [1276, 84, 1077, 19, "default"], [1276, 91, 1077, 36], [1277, 16, 1077, 37, "size"], [1277, 20, 1077, 41], [1277, 22, 1077, 42], [1277, 29, 1077, 49], [1278, 16, 1077, 50, "color"], [1278, 21, 1077, 55], [1278, 23, 1077, 56], [1279, 14, 1077, 65], [1280, 16, 1077, 65, "fileName"], [1280, 24, 1077, 65], [1280, 26, 1077, 65, "_jsxFileName"], [1280, 38, 1077, 65], [1281, 16, 1077, 65, "lineNumber"], [1281, 26, 1077, 65], [1282, 16, 1077, 65, "columnNumber"], [1282, 28, 1077, 65], [1283, 14, 1077, 65], [1283, 21, 1077, 67], [1284, 12, 1078, 17], [1285, 14, 1078, 17, "fileName"], [1285, 22, 1078, 17], [1285, 24, 1078, 17, "_jsxFileName"], [1285, 36, 1078, 17], [1286, 14, 1078, 17, "lineNumber"], [1286, 24, 1078, 17], [1287, 14, 1078, 17, "columnNumber"], [1287, 26, 1078, 17], [1288, 12, 1078, 17], [1288, 19, 1079, 32], [1288, 20, 1079, 33], [1288, 35, 1080, 14], [1288, 39, 1080, 14, "_jsxDevRuntime"], [1288, 53, 1080, 14], [1288, 54, 1080, 14, "jsxDEV"], [1288, 60, 1080, 14], [1288, 62, 1080, 15, "_Text"], [1288, 67, 1080, 15], [1288, 68, 1080, 15, "default"], [1288, 75, 1080, 19], [1289, 14, 1080, 20, "style"], [1289, 19, 1080, 25], [1289, 21, 1080, 27, "styles"], [1289, 27, 1080, 33], [1289, 28, 1080, 34, "privacyNote"], [1289, 39, 1080, 46], [1290, 14, 1080, 46, "children"], [1290, 22, 1080, 46], [1290, 24, 1080, 47], [1291, 12, 1082, 14], [1292, 14, 1082, 14, "fileName"], [1292, 22, 1082, 14], [1292, 24, 1082, 14, "_jsxFileName"], [1292, 36, 1082, 14], [1293, 14, 1082, 14, "lineNumber"], [1293, 24, 1082, 14], [1294, 14, 1082, 14, "columnNumber"], [1294, 26, 1082, 14], [1295, 12, 1082, 14], [1295, 19, 1082, 20], [1295, 20, 1082, 21], [1296, 10, 1082, 21], [1297, 12, 1082, 21, "fileName"], [1297, 20, 1082, 21], [1297, 22, 1082, 21, "_jsxFileName"], [1297, 34, 1082, 21], [1298, 12, 1082, 21, "lineNumber"], [1298, 22, 1082, 21], [1299, 12, 1082, 21, "columnNumber"], [1299, 24, 1082, 21], [1300, 10, 1082, 21], [1300, 17, 1083, 18], [1300, 18, 1083, 19], [1301, 8, 1083, 19], [1301, 23, 1084, 12], [1301, 24, 1085, 9], [1302, 6, 1085, 9], [1303, 8, 1085, 9, "fileName"], [1303, 16, 1085, 9], [1303, 18, 1085, 9, "_jsxFileName"], [1303, 30, 1085, 9], [1304, 8, 1085, 9, "lineNumber"], [1304, 18, 1085, 9], [1305, 8, 1085, 9, "columnNumber"], [1305, 20, 1085, 9], [1306, 6, 1085, 9], [1306, 13, 1086, 12], [1306, 14, 1086, 13], [1306, 29, 1088, 6], [1306, 33, 1088, 6, "_jsxDevRuntime"], [1306, 47, 1088, 6], [1306, 48, 1088, 6, "jsxDEV"], [1306, 54, 1088, 6], [1306, 56, 1088, 7, "_Modal"], [1306, 62, 1088, 7], [1306, 63, 1088, 7, "default"], [1306, 70, 1088, 12], [1307, 8, 1089, 8, "visible"], [1307, 15, 1089, 15], [1307, 17, 1089, 17, "processingState"], [1307, 32, 1089, 32], [1307, 37, 1089, 37], [1307, 43, 1089, 43], [1307, 47, 1089, 47, "processingState"], [1307, 62, 1089, 62], [1307, 67, 1089, 67], [1307, 74, 1089, 75], [1308, 8, 1090, 8, "transparent"], [1308, 19, 1090, 19], [1309, 8, 1091, 8, "animationType"], [1309, 21, 1091, 21], [1309, 23, 1091, 22], [1309, 29, 1091, 28], [1310, 8, 1091, 28, "children"], [1310, 16, 1091, 28], [1310, 31, 1093, 8], [1310, 35, 1093, 8, "_jsxDevRuntime"], [1310, 49, 1093, 8], [1310, 50, 1093, 8, "jsxDEV"], [1310, 56, 1093, 8], [1310, 58, 1093, 9, "_View"], [1310, 63, 1093, 9], [1310, 64, 1093, 9, "default"], [1310, 71, 1093, 13], [1311, 10, 1093, 14, "style"], [1311, 15, 1093, 19], [1311, 17, 1093, 21, "styles"], [1311, 23, 1093, 27], [1311, 24, 1093, 28, "processingModal"], [1311, 39, 1093, 44], [1312, 10, 1093, 44, "children"], [1312, 18, 1093, 44], [1312, 33, 1094, 10], [1312, 37, 1094, 10, "_jsxDevRuntime"], [1312, 51, 1094, 10], [1312, 52, 1094, 10, "jsxDEV"], [1312, 58, 1094, 10], [1312, 60, 1094, 11, "_View"], [1312, 65, 1094, 11], [1312, 66, 1094, 11, "default"], [1312, 73, 1094, 15], [1313, 12, 1094, 16, "style"], [1313, 17, 1094, 21], [1313, 19, 1094, 23, "styles"], [1313, 25, 1094, 29], [1313, 26, 1094, 30, "processingContent"], [1313, 43, 1094, 48], [1314, 12, 1094, 48, "children"], [1314, 20, 1094, 48], [1314, 36, 1095, 12], [1314, 40, 1095, 12, "_jsxDevRuntime"], [1314, 54, 1095, 12], [1314, 55, 1095, 12, "jsxDEV"], [1314, 61, 1095, 12], [1314, 63, 1095, 13, "_ActivityIndicator"], [1314, 81, 1095, 13], [1314, 82, 1095, 13, "default"], [1314, 89, 1095, 30], [1315, 14, 1095, 31, "size"], [1315, 18, 1095, 35], [1315, 20, 1095, 36], [1315, 27, 1095, 43], [1316, 14, 1095, 44, "color"], [1316, 19, 1095, 49], [1316, 21, 1095, 50], [1317, 12, 1095, 59], [1318, 14, 1095, 59, "fileName"], [1318, 22, 1095, 59], [1318, 24, 1095, 59, "_jsxFileName"], [1318, 36, 1095, 59], [1319, 14, 1095, 59, "lineNumber"], [1319, 24, 1095, 59], [1320, 14, 1095, 59, "columnNumber"], [1320, 26, 1095, 59], [1321, 12, 1095, 59], [1321, 19, 1095, 61], [1321, 20, 1095, 62], [1321, 35, 1097, 12], [1321, 39, 1097, 12, "_jsxDevRuntime"], [1321, 53, 1097, 12], [1321, 54, 1097, 12, "jsxDEV"], [1321, 60, 1097, 12], [1321, 62, 1097, 13, "_Text"], [1321, 67, 1097, 13], [1321, 68, 1097, 13, "default"], [1321, 75, 1097, 17], [1322, 14, 1097, 18, "style"], [1322, 19, 1097, 23], [1322, 21, 1097, 25, "styles"], [1322, 27, 1097, 31], [1322, 28, 1097, 32, "processingTitle"], [1322, 43, 1097, 48], [1323, 14, 1097, 48, "children"], [1323, 22, 1097, 48], [1323, 25, 1098, 15, "processingState"], [1323, 40, 1098, 30], [1323, 45, 1098, 35], [1323, 56, 1098, 46], [1323, 60, 1098, 50], [1323, 80, 1098, 70], [1323, 82, 1099, 15, "processingState"], [1323, 97, 1099, 30], [1323, 102, 1099, 35], [1323, 113, 1099, 46], [1323, 117, 1099, 50], [1323, 146, 1099, 79], [1323, 148, 1100, 15, "processingState"], [1323, 163, 1100, 30], [1323, 168, 1100, 35], [1323, 180, 1100, 47], [1323, 184, 1100, 51], [1323, 216, 1100, 83], [1323, 218, 1101, 15, "processingState"], [1323, 233, 1101, 30], [1323, 238, 1101, 35], [1323, 249, 1101, 46], [1323, 253, 1101, 50], [1323, 275, 1101, 72], [1324, 12, 1101, 72], [1325, 14, 1101, 72, "fileName"], [1325, 22, 1101, 72], [1325, 24, 1101, 72, "_jsxFileName"], [1325, 36, 1101, 72], [1326, 14, 1101, 72, "lineNumber"], [1326, 24, 1101, 72], [1327, 14, 1101, 72, "columnNumber"], [1327, 26, 1101, 72], [1328, 12, 1101, 72], [1328, 19, 1102, 18], [1328, 20, 1102, 19], [1328, 35, 1103, 12], [1328, 39, 1103, 12, "_jsxDevRuntime"], [1328, 53, 1103, 12], [1328, 54, 1103, 12, "jsxDEV"], [1328, 60, 1103, 12], [1328, 62, 1103, 13, "_View"], [1328, 67, 1103, 13], [1328, 68, 1103, 13, "default"], [1328, 75, 1103, 17], [1329, 14, 1103, 18, "style"], [1329, 19, 1103, 23], [1329, 21, 1103, 25, "styles"], [1329, 27, 1103, 31], [1329, 28, 1103, 32, "progressBar"], [1329, 39, 1103, 44], [1330, 14, 1103, 44, "children"], [1330, 22, 1103, 44], [1330, 37, 1104, 14], [1330, 41, 1104, 14, "_jsxDevRuntime"], [1330, 55, 1104, 14], [1330, 56, 1104, 14, "jsxDEV"], [1330, 62, 1104, 14], [1330, 64, 1104, 15, "_View"], [1330, 69, 1104, 15], [1330, 70, 1104, 15, "default"], [1330, 77, 1104, 19], [1331, 16, 1105, 16, "style"], [1331, 21, 1105, 21], [1331, 23, 1105, 23], [1331, 24, 1106, 18, "styles"], [1331, 30, 1106, 24], [1331, 31, 1106, 25, "progressFill"], [1331, 43, 1106, 37], [1331, 45, 1107, 18], [1332, 18, 1107, 20, "width"], [1332, 23, 1107, 25], [1332, 25, 1107, 27], [1332, 28, 1107, 30, "processingProgress"], [1332, 46, 1107, 48], [1333, 16, 1107, 52], [1333, 17, 1107, 53], [1334, 14, 1108, 18], [1335, 16, 1108, 18, "fileName"], [1335, 24, 1108, 18], [1335, 26, 1108, 18, "_jsxFileName"], [1335, 38, 1108, 18], [1336, 16, 1108, 18, "lineNumber"], [1336, 26, 1108, 18], [1337, 16, 1108, 18, "columnNumber"], [1337, 28, 1108, 18], [1338, 14, 1108, 18], [1338, 21, 1109, 15], [1339, 12, 1109, 16], [1340, 14, 1109, 16, "fileName"], [1340, 22, 1109, 16], [1340, 24, 1109, 16, "_jsxFileName"], [1340, 36, 1109, 16], [1341, 14, 1109, 16, "lineNumber"], [1341, 24, 1109, 16], [1342, 14, 1109, 16, "columnNumber"], [1342, 26, 1109, 16], [1343, 12, 1109, 16], [1343, 19, 1110, 18], [1343, 20, 1110, 19], [1343, 35, 1111, 12], [1343, 39, 1111, 12, "_jsxDevRuntime"], [1343, 53, 1111, 12], [1343, 54, 1111, 12, "jsxDEV"], [1343, 60, 1111, 12], [1343, 62, 1111, 13, "_Text"], [1343, 67, 1111, 13], [1343, 68, 1111, 13, "default"], [1343, 75, 1111, 17], [1344, 14, 1111, 18, "style"], [1344, 19, 1111, 23], [1344, 21, 1111, 25, "styles"], [1344, 27, 1111, 31], [1344, 28, 1111, 32, "processingDescription"], [1344, 49, 1111, 54], [1345, 14, 1111, 54, "children"], [1345, 22, 1111, 54], [1345, 25, 1112, 15, "processingState"], [1345, 40, 1112, 30], [1345, 45, 1112, 35], [1345, 56, 1112, 46], [1345, 60, 1112, 50], [1345, 89, 1112, 79], [1345, 91, 1113, 15, "processingState"], [1345, 106, 1113, 30], [1345, 111, 1113, 35], [1345, 122, 1113, 46], [1345, 126, 1113, 50], [1345, 164, 1113, 88], [1345, 166, 1114, 15, "processingState"], [1345, 181, 1114, 30], [1345, 186, 1114, 35], [1345, 198, 1114, 47], [1345, 202, 1114, 51], [1345, 247, 1114, 96], [1345, 249, 1115, 15, "processingState"], [1345, 264, 1115, 30], [1345, 269, 1115, 35], [1345, 280, 1115, 46], [1345, 284, 1115, 50], [1345, 325, 1115, 91], [1346, 12, 1115, 91], [1347, 14, 1115, 91, "fileName"], [1347, 22, 1115, 91], [1347, 24, 1115, 91, "_jsxFileName"], [1347, 36, 1115, 91], [1348, 14, 1115, 91, "lineNumber"], [1348, 24, 1115, 91], [1349, 14, 1115, 91, "columnNumber"], [1349, 26, 1115, 91], [1350, 12, 1115, 91], [1350, 19, 1116, 18], [1350, 20, 1116, 19], [1350, 22, 1117, 13, "processingState"], [1350, 37, 1117, 28], [1350, 42, 1117, 33], [1350, 53, 1117, 44], [1350, 70, 1118, 14], [1350, 74, 1118, 14, "_jsxDevRuntime"], [1350, 88, 1118, 14], [1350, 89, 1118, 14, "jsxDEV"], [1350, 95, 1118, 14], [1350, 97, 1118, 15, "_lucideReactNative"], [1350, 115, 1118, 15], [1350, 116, 1118, 15, "CheckCircle"], [1350, 127, 1118, 26], [1351, 14, 1118, 27, "size"], [1351, 18, 1118, 31], [1351, 20, 1118, 33], [1351, 22, 1118, 36], [1352, 14, 1118, 37, "color"], [1352, 19, 1118, 42], [1352, 21, 1118, 43], [1352, 30, 1118, 52], [1353, 14, 1118, 53, "style"], [1353, 19, 1118, 58], [1353, 21, 1118, 60, "styles"], [1353, 27, 1118, 66], [1353, 28, 1118, 67, "successIcon"], [1354, 12, 1118, 79], [1355, 14, 1118, 79, "fileName"], [1355, 22, 1118, 79], [1355, 24, 1118, 79, "_jsxFileName"], [1355, 36, 1118, 79], [1356, 14, 1118, 79, "lineNumber"], [1356, 24, 1118, 79], [1357, 14, 1118, 79, "columnNumber"], [1357, 26, 1118, 79], [1358, 12, 1118, 79], [1358, 19, 1118, 81], [1358, 20, 1119, 13], [1359, 10, 1119, 13], [1360, 12, 1119, 13, "fileName"], [1360, 20, 1119, 13], [1360, 22, 1119, 13, "_jsxFileName"], [1360, 34, 1119, 13], [1361, 12, 1119, 13, "lineNumber"], [1361, 22, 1119, 13], [1362, 12, 1119, 13, "columnNumber"], [1362, 24, 1119, 13], [1363, 10, 1119, 13], [1363, 17, 1120, 16], [1364, 8, 1120, 17], [1365, 10, 1120, 17, "fileName"], [1365, 18, 1120, 17], [1365, 20, 1120, 17, "_jsxFileName"], [1365, 32, 1120, 17], [1366, 10, 1120, 17, "lineNumber"], [1366, 20, 1120, 17], [1367, 10, 1120, 17, "columnNumber"], [1367, 22, 1120, 17], [1368, 8, 1120, 17], [1368, 15, 1121, 14], [1369, 6, 1121, 15], [1370, 8, 1121, 15, "fileName"], [1370, 16, 1121, 15], [1370, 18, 1121, 15, "_jsxFileName"], [1370, 30, 1121, 15], [1371, 8, 1121, 15, "lineNumber"], [1371, 18, 1121, 15], [1372, 8, 1121, 15, "columnNumber"], [1372, 20, 1121, 15], [1373, 6, 1121, 15], [1373, 13, 1122, 13], [1373, 14, 1122, 14], [1373, 29, 1124, 6], [1373, 33, 1124, 6, "_jsxDevRuntime"], [1373, 47, 1124, 6], [1373, 48, 1124, 6, "jsxDEV"], [1373, 54, 1124, 6], [1373, 56, 1124, 7, "_Modal"], [1373, 62, 1124, 7], [1373, 63, 1124, 7, "default"], [1373, 70, 1124, 12], [1374, 8, 1125, 8, "visible"], [1374, 15, 1125, 15], [1374, 17, 1125, 17, "processingState"], [1374, 32, 1125, 32], [1374, 37, 1125, 37], [1374, 44, 1125, 45], [1375, 8, 1126, 8, "transparent"], [1375, 19, 1126, 19], [1376, 8, 1127, 8, "animationType"], [1376, 21, 1127, 21], [1376, 23, 1127, 22], [1376, 29, 1127, 28], [1377, 8, 1127, 28, "children"], [1377, 16, 1127, 28], [1377, 31, 1129, 8], [1377, 35, 1129, 8, "_jsxDevRuntime"], [1377, 49, 1129, 8], [1377, 50, 1129, 8, "jsxDEV"], [1377, 56, 1129, 8], [1377, 58, 1129, 9, "_View"], [1377, 63, 1129, 9], [1377, 64, 1129, 9, "default"], [1377, 71, 1129, 13], [1378, 10, 1129, 14, "style"], [1378, 15, 1129, 19], [1378, 17, 1129, 21, "styles"], [1378, 23, 1129, 27], [1378, 24, 1129, 28, "processingModal"], [1378, 39, 1129, 44], [1379, 10, 1129, 44, "children"], [1379, 18, 1129, 44], [1379, 33, 1130, 10], [1379, 37, 1130, 10, "_jsxDevRuntime"], [1379, 51, 1130, 10], [1379, 52, 1130, 10, "jsxDEV"], [1379, 58, 1130, 10], [1379, 60, 1130, 11, "_View"], [1379, 65, 1130, 11], [1379, 66, 1130, 11, "default"], [1379, 73, 1130, 15], [1380, 12, 1130, 16, "style"], [1380, 17, 1130, 21], [1380, 19, 1130, 23, "styles"], [1380, 25, 1130, 29], [1380, 26, 1130, 30, "errorContent"], [1380, 38, 1130, 43], [1381, 12, 1130, 43, "children"], [1381, 20, 1130, 43], [1381, 36, 1131, 12], [1381, 40, 1131, 12, "_jsxDevRuntime"], [1381, 54, 1131, 12], [1381, 55, 1131, 12, "jsxDEV"], [1381, 61, 1131, 12], [1381, 63, 1131, 13, "_lucideReactNative"], [1381, 81, 1131, 13], [1381, 82, 1131, 13, "X"], [1381, 83, 1131, 14], [1382, 14, 1131, 15, "size"], [1382, 18, 1131, 19], [1382, 20, 1131, 21], [1382, 22, 1131, 24], [1383, 14, 1131, 25, "color"], [1383, 19, 1131, 30], [1383, 21, 1131, 31], [1384, 12, 1131, 40], [1385, 14, 1131, 40, "fileName"], [1385, 22, 1131, 40], [1385, 24, 1131, 40, "_jsxFileName"], [1385, 36, 1131, 40], [1386, 14, 1131, 40, "lineNumber"], [1386, 24, 1131, 40], [1387, 14, 1131, 40, "columnNumber"], [1387, 26, 1131, 40], [1388, 12, 1131, 40], [1388, 19, 1131, 42], [1388, 20, 1131, 43], [1388, 35, 1132, 12], [1388, 39, 1132, 12, "_jsxDevRuntime"], [1388, 53, 1132, 12], [1388, 54, 1132, 12, "jsxDEV"], [1388, 60, 1132, 12], [1388, 62, 1132, 13, "_Text"], [1388, 67, 1132, 13], [1388, 68, 1132, 13, "default"], [1388, 75, 1132, 17], [1389, 14, 1132, 18, "style"], [1389, 19, 1132, 23], [1389, 21, 1132, 25, "styles"], [1389, 27, 1132, 31], [1389, 28, 1132, 32, "errorTitle"], [1389, 38, 1132, 43], [1390, 14, 1132, 43, "children"], [1390, 22, 1132, 43], [1390, 24, 1132, 44], [1391, 12, 1132, 61], [1392, 14, 1132, 61, "fileName"], [1392, 22, 1132, 61], [1392, 24, 1132, 61, "_jsxFileName"], [1392, 36, 1132, 61], [1393, 14, 1132, 61, "lineNumber"], [1393, 24, 1132, 61], [1394, 14, 1132, 61, "columnNumber"], [1394, 26, 1132, 61], [1395, 12, 1132, 61], [1395, 19, 1132, 67], [1395, 20, 1132, 68], [1395, 35, 1133, 12], [1395, 39, 1133, 12, "_jsxDevRuntime"], [1395, 53, 1133, 12], [1395, 54, 1133, 12, "jsxDEV"], [1395, 60, 1133, 12], [1395, 62, 1133, 13, "_Text"], [1395, 67, 1133, 13], [1395, 68, 1133, 13, "default"], [1395, 75, 1133, 17], [1396, 14, 1133, 18, "style"], [1396, 19, 1133, 23], [1396, 21, 1133, 25, "styles"], [1396, 27, 1133, 31], [1396, 28, 1133, 32, "errorMessage"], [1396, 40, 1133, 45], [1397, 14, 1133, 45, "children"], [1397, 22, 1133, 45], [1397, 24, 1133, 47, "errorMessage"], [1398, 12, 1133, 59], [1399, 14, 1133, 59, "fileName"], [1399, 22, 1133, 59], [1399, 24, 1133, 59, "_jsxFileName"], [1399, 36, 1133, 59], [1400, 14, 1133, 59, "lineNumber"], [1400, 24, 1133, 59], [1401, 14, 1133, 59, "columnNumber"], [1401, 26, 1133, 59], [1402, 12, 1133, 59], [1402, 19, 1133, 66], [1402, 20, 1133, 67], [1402, 35, 1134, 12], [1402, 39, 1134, 12, "_jsxDevRuntime"], [1402, 53, 1134, 12], [1402, 54, 1134, 12, "jsxDEV"], [1402, 60, 1134, 12], [1402, 62, 1134, 13, "_TouchableOpacity"], [1402, 79, 1134, 13], [1402, 80, 1134, 13, "default"], [1402, 87, 1134, 29], [1403, 14, 1135, 14, "onPress"], [1403, 21, 1135, 21], [1403, 23, 1135, 23, "retryCapture"], [1403, 35, 1135, 36], [1404, 14, 1136, 14, "style"], [1404, 19, 1136, 19], [1404, 21, 1136, 21, "styles"], [1404, 27, 1136, 27], [1404, 28, 1136, 28, "primaryButton"], [1404, 41, 1136, 42], [1405, 14, 1136, 42, "children"], [1405, 22, 1136, 42], [1405, 37, 1138, 14], [1405, 41, 1138, 14, "_jsxDevRuntime"], [1405, 55, 1138, 14], [1405, 56, 1138, 14, "jsxDEV"], [1405, 62, 1138, 14], [1405, 64, 1138, 15, "_Text"], [1405, 69, 1138, 15], [1405, 70, 1138, 15, "default"], [1405, 77, 1138, 19], [1406, 16, 1138, 20, "style"], [1406, 21, 1138, 25], [1406, 23, 1138, 27, "styles"], [1406, 29, 1138, 33], [1406, 30, 1138, 34, "primaryButtonText"], [1406, 47, 1138, 52], [1407, 16, 1138, 52, "children"], [1407, 24, 1138, 52], [1407, 26, 1138, 53], [1408, 14, 1138, 62], [1409, 16, 1138, 62, "fileName"], [1409, 24, 1138, 62], [1409, 26, 1138, 62, "_jsxFileName"], [1409, 38, 1138, 62], [1410, 16, 1138, 62, "lineNumber"], [1410, 26, 1138, 62], [1411, 16, 1138, 62, "columnNumber"], [1411, 28, 1138, 62], [1412, 14, 1138, 62], [1412, 21, 1138, 68], [1413, 12, 1138, 69], [1414, 14, 1138, 69, "fileName"], [1414, 22, 1138, 69], [1414, 24, 1138, 69, "_jsxFileName"], [1414, 36, 1138, 69], [1415, 14, 1138, 69, "lineNumber"], [1415, 24, 1138, 69], [1416, 14, 1138, 69, "columnNumber"], [1416, 26, 1138, 69], [1417, 12, 1138, 69], [1417, 19, 1139, 30], [1417, 20, 1139, 31], [1417, 35, 1140, 12], [1417, 39, 1140, 12, "_jsxDevRuntime"], [1417, 53, 1140, 12], [1417, 54, 1140, 12, "jsxDEV"], [1417, 60, 1140, 12], [1417, 62, 1140, 13, "_TouchableOpacity"], [1417, 79, 1140, 13], [1417, 80, 1140, 13, "default"], [1417, 87, 1140, 29], [1418, 14, 1141, 14, "onPress"], [1418, 21, 1141, 21], [1418, 23, 1141, 23, "onCancel"], [1418, 31, 1141, 32], [1419, 14, 1142, 14, "style"], [1419, 19, 1142, 19], [1419, 21, 1142, 21, "styles"], [1419, 27, 1142, 27], [1419, 28, 1142, 28, "secondaryButton"], [1419, 43, 1142, 44], [1420, 14, 1142, 44, "children"], [1420, 22, 1142, 44], [1420, 37, 1144, 14], [1420, 41, 1144, 14, "_jsxDevRuntime"], [1420, 55, 1144, 14], [1420, 56, 1144, 14, "jsxDEV"], [1420, 62, 1144, 14], [1420, 64, 1144, 15, "_Text"], [1420, 69, 1144, 15], [1420, 70, 1144, 15, "default"], [1420, 77, 1144, 19], [1421, 16, 1144, 20, "style"], [1421, 21, 1144, 25], [1421, 23, 1144, 27, "styles"], [1421, 29, 1144, 33], [1421, 30, 1144, 34, "secondaryButtonText"], [1421, 49, 1144, 54], [1422, 16, 1144, 54, "children"], [1422, 24, 1144, 54], [1422, 26, 1144, 55], [1423, 14, 1144, 61], [1424, 16, 1144, 61, "fileName"], [1424, 24, 1144, 61], [1424, 26, 1144, 61, "_jsxFileName"], [1424, 38, 1144, 61], [1425, 16, 1144, 61, "lineNumber"], [1425, 26, 1144, 61], [1426, 16, 1144, 61, "columnNumber"], [1426, 28, 1144, 61], [1427, 14, 1144, 61], [1427, 21, 1144, 67], [1428, 12, 1144, 68], [1429, 14, 1144, 68, "fileName"], [1429, 22, 1144, 68], [1429, 24, 1144, 68, "_jsxFileName"], [1429, 36, 1144, 68], [1430, 14, 1144, 68, "lineNumber"], [1430, 24, 1144, 68], [1431, 14, 1144, 68, "columnNumber"], [1431, 26, 1144, 68], [1432, 12, 1144, 68], [1432, 19, 1145, 30], [1432, 20, 1145, 31], [1433, 10, 1145, 31], [1434, 12, 1145, 31, "fileName"], [1434, 20, 1145, 31], [1434, 22, 1145, 31, "_jsxFileName"], [1434, 34, 1145, 31], [1435, 12, 1145, 31, "lineNumber"], [1435, 22, 1145, 31], [1436, 12, 1145, 31, "columnNumber"], [1436, 24, 1145, 31], [1437, 10, 1145, 31], [1437, 17, 1146, 16], [1438, 8, 1146, 17], [1439, 10, 1146, 17, "fileName"], [1439, 18, 1146, 17], [1439, 20, 1146, 17, "_jsxFileName"], [1439, 32, 1146, 17], [1440, 10, 1146, 17, "lineNumber"], [1440, 20, 1146, 17], [1441, 10, 1146, 17, "columnNumber"], [1441, 22, 1146, 17], [1442, 8, 1146, 17], [1442, 15, 1147, 14], [1443, 6, 1147, 15], [1444, 8, 1147, 15, "fileName"], [1444, 16, 1147, 15], [1444, 18, 1147, 15, "_jsxFileName"], [1444, 30, 1147, 15], [1445, 8, 1147, 15, "lineNumber"], [1445, 18, 1147, 15], [1446, 8, 1147, 15, "columnNumber"], [1446, 20, 1147, 15], [1447, 6, 1147, 15], [1447, 13, 1148, 13], [1447, 14, 1148, 14], [1448, 4, 1148, 14], [1449, 6, 1148, 14, "fileName"], [1449, 14, 1148, 14], [1449, 16, 1148, 14, "_jsxFileName"], [1449, 28, 1148, 14], [1450, 6, 1148, 14, "lineNumber"], [1450, 16, 1148, 14], [1451, 6, 1148, 14, "columnNumber"], [1451, 18, 1148, 14], [1452, 4, 1148, 14], [1452, 11, 1149, 10], [1452, 12, 1149, 11], [1453, 2, 1151, 0], [1454, 2, 1151, 1, "_s"], [1454, 4, 1151, 1], [1454, 5, 52, 24, "EchoCameraWeb"], [1454, 18, 52, 37], [1455, 4, 52, 37], [1455, 12, 59, 42, "useCameraPermissions"], [1455, 44, 59, 62], [1455, 46, 74, 19, "useUpload"], [1455, 64, 74, 28], [1456, 2, 74, 28], [1457, 2, 74, 28, "_c"], [1457, 4, 74, 28], [1457, 7, 52, 24, "EchoCameraWeb"], [1457, 20, 52, 37], [1458, 2, 1152, 0], [1458, 8, 1152, 6, "styles"], [1458, 14, 1152, 12], [1458, 17, 1152, 15, "StyleSheet"], [1458, 36, 1152, 25], [1458, 37, 1152, 26, "create"], [1458, 43, 1152, 32], [1458, 44, 1152, 33], [1459, 4, 1153, 2, "container"], [1459, 13, 1153, 11], [1459, 15, 1153, 13], [1460, 6, 1154, 4, "flex"], [1460, 10, 1154, 8], [1460, 12, 1154, 10], [1460, 13, 1154, 11], [1461, 6, 1155, 4, "backgroundColor"], [1461, 21, 1155, 19], [1461, 23, 1155, 21], [1462, 4, 1156, 2], [1462, 5, 1156, 3], [1463, 4, 1157, 2, "cameraContainer"], [1463, 19, 1157, 17], [1463, 21, 1157, 19], [1464, 6, 1158, 4, "flex"], [1464, 10, 1158, 8], [1464, 12, 1158, 10], [1464, 13, 1158, 11], [1465, 6, 1159, 4, "max<PERSON><PERSON><PERSON>"], [1465, 14, 1159, 12], [1465, 16, 1159, 14], [1465, 19, 1159, 17], [1466, 6, 1160, 4, "alignSelf"], [1466, 15, 1160, 13], [1466, 17, 1160, 15], [1466, 25, 1160, 23], [1467, 6, 1161, 4, "width"], [1467, 11, 1161, 9], [1467, 13, 1161, 11], [1468, 4, 1162, 2], [1468, 5, 1162, 3], [1469, 4, 1163, 2, "camera"], [1469, 10, 1163, 8], [1469, 12, 1163, 10], [1470, 6, 1164, 4, "flex"], [1470, 10, 1164, 8], [1470, 12, 1164, 10], [1471, 4, 1165, 2], [1471, 5, 1165, 3], [1472, 4, 1166, 2, "headerOverlay"], [1472, 17, 1166, 15], [1472, 19, 1166, 17], [1473, 6, 1167, 4, "position"], [1473, 14, 1167, 12], [1473, 16, 1167, 14], [1473, 26, 1167, 24], [1474, 6, 1168, 4, "top"], [1474, 9, 1168, 7], [1474, 11, 1168, 9], [1474, 12, 1168, 10], [1475, 6, 1169, 4, "left"], [1475, 10, 1169, 8], [1475, 12, 1169, 10], [1475, 13, 1169, 11], [1476, 6, 1170, 4, "right"], [1476, 11, 1170, 9], [1476, 13, 1170, 11], [1476, 14, 1170, 12], [1477, 6, 1171, 4, "backgroundColor"], [1477, 21, 1171, 19], [1477, 23, 1171, 21], [1477, 36, 1171, 34], [1478, 6, 1172, 4, "paddingTop"], [1478, 16, 1172, 14], [1478, 18, 1172, 16], [1478, 20, 1172, 18], [1479, 6, 1173, 4, "paddingHorizontal"], [1479, 23, 1173, 21], [1479, 25, 1173, 23], [1479, 27, 1173, 25], [1480, 6, 1174, 4, "paddingBottom"], [1480, 19, 1174, 17], [1480, 21, 1174, 19], [1481, 4, 1175, 2], [1481, 5, 1175, 3], [1482, 4, 1176, 2, "headerContent"], [1482, 17, 1176, 15], [1482, 19, 1176, 17], [1483, 6, 1177, 4, "flexDirection"], [1483, 19, 1177, 17], [1483, 21, 1177, 19], [1483, 26, 1177, 24], [1484, 6, 1178, 4, "justifyContent"], [1484, 20, 1178, 18], [1484, 22, 1178, 20], [1484, 37, 1178, 35], [1485, 6, 1179, 4, "alignItems"], [1485, 16, 1179, 14], [1485, 18, 1179, 16], [1486, 4, 1180, 2], [1486, 5, 1180, 3], [1487, 4, 1181, 2, "headerLeft"], [1487, 14, 1181, 12], [1487, 16, 1181, 14], [1488, 6, 1182, 4, "flex"], [1488, 10, 1182, 8], [1488, 12, 1182, 10], [1489, 4, 1183, 2], [1489, 5, 1183, 3], [1490, 4, 1184, 2, "headerTitle"], [1490, 15, 1184, 13], [1490, 17, 1184, 15], [1491, 6, 1185, 4, "fontSize"], [1491, 14, 1185, 12], [1491, 16, 1185, 14], [1491, 18, 1185, 16], [1492, 6, 1186, 4, "fontWeight"], [1492, 16, 1186, 14], [1492, 18, 1186, 16], [1492, 23, 1186, 21], [1493, 6, 1187, 4, "color"], [1493, 11, 1187, 9], [1493, 13, 1187, 11], [1493, 19, 1187, 17], [1494, 6, 1188, 4, "marginBottom"], [1494, 18, 1188, 16], [1494, 20, 1188, 18], [1495, 4, 1189, 2], [1495, 5, 1189, 3], [1496, 4, 1190, 2, "subtitleRow"], [1496, 15, 1190, 13], [1496, 17, 1190, 15], [1497, 6, 1191, 4, "flexDirection"], [1497, 19, 1191, 17], [1497, 21, 1191, 19], [1497, 26, 1191, 24], [1498, 6, 1192, 4, "alignItems"], [1498, 16, 1192, 14], [1498, 18, 1192, 16], [1498, 26, 1192, 24], [1499, 6, 1193, 4, "marginBottom"], [1499, 18, 1193, 16], [1499, 20, 1193, 18], [1500, 4, 1194, 2], [1500, 5, 1194, 3], [1501, 4, 1195, 2, "webIcon"], [1501, 11, 1195, 9], [1501, 13, 1195, 11], [1502, 6, 1196, 4, "fontSize"], [1502, 14, 1196, 12], [1502, 16, 1196, 14], [1502, 18, 1196, 16], [1503, 6, 1197, 4, "marginRight"], [1503, 17, 1197, 15], [1503, 19, 1197, 17], [1504, 4, 1198, 2], [1504, 5, 1198, 3], [1505, 4, 1199, 2, "headerSubtitle"], [1505, 18, 1199, 16], [1505, 20, 1199, 18], [1506, 6, 1200, 4, "fontSize"], [1506, 14, 1200, 12], [1506, 16, 1200, 14], [1506, 18, 1200, 16], [1507, 6, 1201, 4, "color"], [1507, 11, 1201, 9], [1507, 13, 1201, 11], [1507, 19, 1201, 17], [1508, 6, 1202, 4, "opacity"], [1508, 13, 1202, 11], [1508, 15, 1202, 13], [1509, 4, 1203, 2], [1509, 5, 1203, 3], [1510, 4, 1204, 2, "challengeRow"], [1510, 16, 1204, 14], [1510, 18, 1204, 16], [1511, 6, 1205, 4, "flexDirection"], [1511, 19, 1205, 17], [1511, 21, 1205, 19], [1511, 26, 1205, 24], [1512, 6, 1206, 4, "alignItems"], [1512, 16, 1206, 14], [1512, 18, 1206, 16], [1513, 4, 1207, 2], [1513, 5, 1207, 3], [1514, 4, 1208, 2, "challengeCode"], [1514, 17, 1208, 15], [1514, 19, 1208, 17], [1515, 6, 1209, 4, "fontSize"], [1515, 14, 1209, 12], [1515, 16, 1209, 14], [1515, 18, 1209, 16], [1516, 6, 1210, 4, "color"], [1516, 11, 1210, 9], [1516, 13, 1210, 11], [1516, 19, 1210, 17], [1517, 6, 1211, 4, "marginLeft"], [1517, 16, 1211, 14], [1517, 18, 1211, 16], [1517, 19, 1211, 17], [1518, 6, 1212, 4, "fontFamily"], [1518, 16, 1212, 14], [1518, 18, 1212, 16], [1519, 4, 1213, 2], [1519, 5, 1213, 3], [1520, 4, 1214, 2, "closeButton"], [1520, 15, 1214, 13], [1520, 17, 1214, 15], [1521, 6, 1215, 4, "padding"], [1521, 13, 1215, 11], [1521, 15, 1215, 13], [1522, 4, 1216, 2], [1522, 5, 1216, 3], [1523, 4, 1217, 2, "privacyNotice"], [1523, 17, 1217, 15], [1523, 19, 1217, 17], [1524, 6, 1218, 4, "position"], [1524, 14, 1218, 12], [1524, 16, 1218, 14], [1524, 26, 1218, 24], [1525, 6, 1219, 4, "top"], [1525, 9, 1219, 7], [1525, 11, 1219, 9], [1525, 14, 1219, 12], [1526, 6, 1220, 4, "left"], [1526, 10, 1220, 8], [1526, 12, 1220, 10], [1526, 14, 1220, 12], [1527, 6, 1221, 4, "right"], [1527, 11, 1221, 9], [1527, 13, 1221, 11], [1527, 15, 1221, 13], [1528, 6, 1222, 4, "backgroundColor"], [1528, 21, 1222, 19], [1528, 23, 1222, 21], [1528, 48, 1222, 46], [1529, 6, 1223, 4, "borderRadius"], [1529, 18, 1223, 16], [1529, 20, 1223, 18], [1529, 21, 1223, 19], [1530, 6, 1224, 4, "padding"], [1530, 13, 1224, 11], [1530, 15, 1224, 13], [1530, 17, 1224, 15], [1531, 6, 1225, 4, "flexDirection"], [1531, 19, 1225, 17], [1531, 21, 1225, 19], [1531, 26, 1225, 24], [1532, 6, 1226, 4, "alignItems"], [1532, 16, 1226, 14], [1532, 18, 1226, 16], [1533, 4, 1227, 2], [1533, 5, 1227, 3], [1534, 4, 1228, 2, "privacyText"], [1534, 15, 1228, 13], [1534, 17, 1228, 15], [1535, 6, 1229, 4, "color"], [1535, 11, 1229, 9], [1535, 13, 1229, 11], [1535, 19, 1229, 17], [1536, 6, 1230, 4, "fontSize"], [1536, 14, 1230, 12], [1536, 16, 1230, 14], [1536, 18, 1230, 16], [1537, 6, 1231, 4, "marginLeft"], [1537, 16, 1231, 14], [1537, 18, 1231, 16], [1537, 19, 1231, 17], [1538, 6, 1232, 4, "flex"], [1538, 10, 1232, 8], [1538, 12, 1232, 10], [1539, 4, 1233, 2], [1539, 5, 1233, 3], [1540, 4, 1234, 2, "footer<PERSON><PERSON><PERSON>"], [1540, 17, 1234, 15], [1540, 19, 1234, 17], [1541, 6, 1235, 4, "position"], [1541, 14, 1235, 12], [1541, 16, 1235, 14], [1541, 26, 1235, 24], [1542, 6, 1236, 4, "bottom"], [1542, 12, 1236, 10], [1542, 14, 1236, 12], [1542, 15, 1236, 13], [1543, 6, 1237, 4, "left"], [1543, 10, 1237, 8], [1543, 12, 1237, 10], [1543, 13, 1237, 11], [1544, 6, 1238, 4, "right"], [1544, 11, 1238, 9], [1544, 13, 1238, 11], [1544, 14, 1238, 12], [1545, 6, 1239, 4, "backgroundColor"], [1545, 21, 1239, 19], [1545, 23, 1239, 21], [1545, 36, 1239, 34], [1546, 6, 1240, 4, "paddingBottom"], [1546, 19, 1240, 17], [1546, 21, 1240, 19], [1546, 23, 1240, 21], [1547, 6, 1241, 4, "paddingTop"], [1547, 16, 1241, 14], [1547, 18, 1241, 16], [1547, 20, 1241, 18], [1548, 6, 1242, 4, "alignItems"], [1548, 16, 1242, 14], [1548, 18, 1242, 16], [1549, 4, 1243, 2], [1549, 5, 1243, 3], [1550, 4, 1244, 2, "instruction"], [1550, 15, 1244, 13], [1550, 17, 1244, 15], [1551, 6, 1245, 4, "fontSize"], [1551, 14, 1245, 12], [1551, 16, 1245, 14], [1551, 18, 1245, 16], [1552, 6, 1246, 4, "color"], [1552, 11, 1246, 9], [1552, 13, 1246, 11], [1552, 19, 1246, 17], [1553, 6, 1247, 4, "marginBottom"], [1553, 18, 1247, 16], [1553, 20, 1247, 18], [1554, 4, 1248, 2], [1554, 5, 1248, 3], [1555, 4, 1249, 2, "shutterButton"], [1555, 17, 1249, 15], [1555, 19, 1249, 17], [1556, 6, 1250, 4, "width"], [1556, 11, 1250, 9], [1556, 13, 1250, 11], [1556, 15, 1250, 13], [1557, 6, 1251, 4, "height"], [1557, 12, 1251, 10], [1557, 14, 1251, 12], [1557, 16, 1251, 14], [1558, 6, 1252, 4, "borderRadius"], [1558, 18, 1252, 16], [1558, 20, 1252, 18], [1558, 22, 1252, 20], [1559, 6, 1253, 4, "backgroundColor"], [1559, 21, 1253, 19], [1559, 23, 1253, 21], [1559, 29, 1253, 27], [1560, 6, 1254, 4, "justifyContent"], [1560, 20, 1254, 18], [1560, 22, 1254, 20], [1560, 30, 1254, 28], [1561, 6, 1255, 4, "alignItems"], [1561, 16, 1255, 14], [1561, 18, 1255, 16], [1561, 26, 1255, 24], [1562, 6, 1256, 4, "marginBottom"], [1562, 18, 1256, 16], [1562, 20, 1256, 18], [1562, 22, 1256, 20], [1563, 6, 1257, 4], [1563, 9, 1257, 7, "Platform"], [1563, 26, 1257, 15], [1563, 27, 1257, 16, "select"], [1563, 33, 1257, 22], [1563, 34, 1257, 23], [1564, 8, 1258, 6, "ios"], [1564, 11, 1258, 9], [1564, 13, 1258, 11], [1565, 10, 1259, 8, "shadowColor"], [1565, 21, 1259, 19], [1565, 23, 1259, 21], [1565, 32, 1259, 30], [1566, 10, 1260, 8, "shadowOffset"], [1566, 22, 1260, 20], [1566, 24, 1260, 22], [1567, 12, 1260, 24, "width"], [1567, 17, 1260, 29], [1567, 19, 1260, 31], [1567, 20, 1260, 32], [1568, 12, 1260, 34, "height"], [1568, 18, 1260, 40], [1568, 20, 1260, 42], [1569, 10, 1260, 44], [1569, 11, 1260, 45], [1570, 10, 1261, 8, "shadowOpacity"], [1570, 23, 1261, 21], [1570, 25, 1261, 23], [1570, 28, 1261, 26], [1571, 10, 1262, 8, "shadowRadius"], [1571, 22, 1262, 20], [1571, 24, 1262, 22], [1572, 8, 1263, 6], [1572, 9, 1263, 7], [1573, 8, 1264, 6, "android"], [1573, 15, 1264, 13], [1573, 17, 1264, 15], [1574, 10, 1265, 8, "elevation"], [1574, 19, 1265, 17], [1574, 21, 1265, 19], [1575, 8, 1266, 6], [1575, 9, 1266, 7], [1576, 8, 1267, 6, "web"], [1576, 11, 1267, 9], [1576, 13, 1267, 11], [1577, 10, 1268, 8, "boxShadow"], [1577, 19, 1268, 17], [1577, 21, 1268, 19], [1578, 8, 1269, 6], [1579, 6, 1270, 4], [1579, 7, 1270, 5], [1580, 4, 1271, 2], [1580, 5, 1271, 3], [1581, 4, 1272, 2, "shutterButtonDisabled"], [1581, 25, 1272, 23], [1581, 27, 1272, 25], [1582, 6, 1273, 4, "opacity"], [1582, 13, 1273, 11], [1582, 15, 1273, 13], [1583, 4, 1274, 2], [1583, 5, 1274, 3], [1584, 4, 1275, 2, "shutterInner"], [1584, 16, 1275, 14], [1584, 18, 1275, 16], [1585, 6, 1276, 4, "width"], [1585, 11, 1276, 9], [1585, 13, 1276, 11], [1585, 15, 1276, 13], [1586, 6, 1277, 4, "height"], [1586, 12, 1277, 10], [1586, 14, 1277, 12], [1586, 16, 1277, 14], [1587, 6, 1278, 4, "borderRadius"], [1587, 18, 1278, 16], [1587, 20, 1278, 18], [1587, 22, 1278, 20], [1588, 6, 1279, 4, "backgroundColor"], [1588, 21, 1279, 19], [1588, 23, 1279, 21], [1588, 29, 1279, 27], [1589, 6, 1280, 4, "borderWidth"], [1589, 17, 1280, 15], [1589, 19, 1280, 17], [1589, 20, 1280, 18], [1590, 6, 1281, 4, "borderColor"], [1590, 17, 1281, 15], [1590, 19, 1281, 17], [1591, 4, 1282, 2], [1591, 5, 1282, 3], [1592, 4, 1283, 2, "privacyNote"], [1592, 15, 1283, 13], [1592, 17, 1283, 15], [1593, 6, 1284, 4, "fontSize"], [1593, 14, 1284, 12], [1593, 16, 1284, 14], [1593, 18, 1284, 16], [1594, 6, 1285, 4, "color"], [1594, 11, 1285, 9], [1594, 13, 1285, 11], [1595, 4, 1286, 2], [1595, 5, 1286, 3], [1596, 4, 1287, 2, "processingModal"], [1596, 19, 1287, 17], [1596, 21, 1287, 19], [1597, 6, 1288, 4, "flex"], [1597, 10, 1288, 8], [1597, 12, 1288, 10], [1597, 13, 1288, 11], [1598, 6, 1289, 4, "backgroundColor"], [1598, 21, 1289, 19], [1598, 23, 1289, 21], [1598, 43, 1289, 41], [1599, 6, 1290, 4, "justifyContent"], [1599, 20, 1290, 18], [1599, 22, 1290, 20], [1599, 30, 1290, 28], [1600, 6, 1291, 4, "alignItems"], [1600, 16, 1291, 14], [1600, 18, 1291, 16], [1601, 4, 1292, 2], [1601, 5, 1292, 3], [1602, 4, 1293, 2, "processingContent"], [1602, 21, 1293, 19], [1602, 23, 1293, 21], [1603, 6, 1294, 4, "backgroundColor"], [1603, 21, 1294, 19], [1603, 23, 1294, 21], [1603, 29, 1294, 27], [1604, 6, 1295, 4, "borderRadius"], [1604, 18, 1295, 16], [1604, 20, 1295, 18], [1604, 22, 1295, 20], [1605, 6, 1296, 4, "padding"], [1605, 13, 1296, 11], [1605, 15, 1296, 13], [1605, 17, 1296, 15], [1606, 6, 1297, 4, "width"], [1606, 11, 1297, 9], [1606, 13, 1297, 11], [1606, 18, 1297, 16], [1607, 6, 1298, 4, "max<PERSON><PERSON><PERSON>"], [1607, 14, 1298, 12], [1607, 16, 1298, 14], [1607, 19, 1298, 17], [1608, 6, 1299, 4, "alignItems"], [1608, 16, 1299, 14], [1608, 18, 1299, 16], [1609, 4, 1300, 2], [1609, 5, 1300, 3], [1610, 4, 1301, 2, "processingTitle"], [1610, 19, 1301, 17], [1610, 21, 1301, 19], [1611, 6, 1302, 4, "fontSize"], [1611, 14, 1302, 12], [1611, 16, 1302, 14], [1611, 18, 1302, 16], [1612, 6, 1303, 4, "fontWeight"], [1612, 16, 1303, 14], [1612, 18, 1303, 16], [1612, 23, 1303, 21], [1613, 6, 1304, 4, "color"], [1613, 11, 1304, 9], [1613, 13, 1304, 11], [1613, 22, 1304, 20], [1614, 6, 1305, 4, "marginTop"], [1614, 15, 1305, 13], [1614, 17, 1305, 15], [1614, 19, 1305, 17], [1615, 6, 1306, 4, "marginBottom"], [1615, 18, 1306, 16], [1615, 20, 1306, 18], [1616, 4, 1307, 2], [1616, 5, 1307, 3], [1617, 4, 1308, 2, "progressBar"], [1617, 15, 1308, 13], [1617, 17, 1308, 15], [1618, 6, 1309, 4, "width"], [1618, 11, 1309, 9], [1618, 13, 1309, 11], [1618, 19, 1309, 17], [1619, 6, 1310, 4, "height"], [1619, 12, 1310, 10], [1619, 14, 1310, 12], [1619, 15, 1310, 13], [1620, 6, 1311, 4, "backgroundColor"], [1620, 21, 1311, 19], [1620, 23, 1311, 21], [1620, 32, 1311, 30], [1621, 6, 1312, 4, "borderRadius"], [1621, 18, 1312, 16], [1621, 20, 1312, 18], [1621, 21, 1312, 19], [1622, 6, 1313, 4, "overflow"], [1622, 14, 1313, 12], [1622, 16, 1313, 14], [1622, 24, 1313, 22], [1623, 6, 1314, 4, "marginBottom"], [1623, 18, 1314, 16], [1623, 20, 1314, 18], [1624, 4, 1315, 2], [1624, 5, 1315, 3], [1625, 4, 1316, 2, "progressFill"], [1625, 16, 1316, 14], [1625, 18, 1316, 16], [1626, 6, 1317, 4, "height"], [1626, 12, 1317, 10], [1626, 14, 1317, 12], [1626, 20, 1317, 18], [1627, 6, 1318, 4, "backgroundColor"], [1627, 21, 1318, 19], [1627, 23, 1318, 21], [1627, 32, 1318, 30], [1628, 6, 1319, 4, "borderRadius"], [1628, 18, 1319, 16], [1628, 20, 1319, 18], [1629, 4, 1320, 2], [1629, 5, 1320, 3], [1630, 4, 1321, 2, "processingDescription"], [1630, 25, 1321, 23], [1630, 27, 1321, 25], [1631, 6, 1322, 4, "fontSize"], [1631, 14, 1322, 12], [1631, 16, 1322, 14], [1631, 18, 1322, 16], [1632, 6, 1323, 4, "color"], [1632, 11, 1323, 9], [1632, 13, 1323, 11], [1632, 22, 1323, 20], [1633, 6, 1324, 4, "textAlign"], [1633, 15, 1324, 13], [1633, 17, 1324, 15], [1634, 4, 1325, 2], [1634, 5, 1325, 3], [1635, 4, 1326, 2, "successIcon"], [1635, 15, 1326, 13], [1635, 17, 1326, 15], [1636, 6, 1327, 4, "marginTop"], [1636, 15, 1327, 13], [1636, 17, 1327, 15], [1637, 4, 1328, 2], [1637, 5, 1328, 3], [1638, 4, 1329, 2, "errorContent"], [1638, 16, 1329, 14], [1638, 18, 1329, 16], [1639, 6, 1330, 4, "backgroundColor"], [1639, 21, 1330, 19], [1639, 23, 1330, 21], [1639, 29, 1330, 27], [1640, 6, 1331, 4, "borderRadius"], [1640, 18, 1331, 16], [1640, 20, 1331, 18], [1640, 22, 1331, 20], [1641, 6, 1332, 4, "padding"], [1641, 13, 1332, 11], [1641, 15, 1332, 13], [1641, 17, 1332, 15], [1642, 6, 1333, 4, "width"], [1642, 11, 1333, 9], [1642, 13, 1333, 11], [1642, 18, 1333, 16], [1643, 6, 1334, 4, "max<PERSON><PERSON><PERSON>"], [1643, 14, 1334, 12], [1643, 16, 1334, 14], [1643, 19, 1334, 17], [1644, 6, 1335, 4, "alignItems"], [1644, 16, 1335, 14], [1644, 18, 1335, 16], [1645, 4, 1336, 2], [1645, 5, 1336, 3], [1646, 4, 1337, 2, "errorTitle"], [1646, 14, 1337, 12], [1646, 16, 1337, 14], [1647, 6, 1338, 4, "fontSize"], [1647, 14, 1338, 12], [1647, 16, 1338, 14], [1647, 18, 1338, 16], [1648, 6, 1339, 4, "fontWeight"], [1648, 16, 1339, 14], [1648, 18, 1339, 16], [1648, 23, 1339, 21], [1649, 6, 1340, 4, "color"], [1649, 11, 1340, 9], [1649, 13, 1340, 11], [1649, 22, 1340, 20], [1650, 6, 1341, 4, "marginTop"], [1650, 15, 1341, 13], [1650, 17, 1341, 15], [1650, 19, 1341, 17], [1651, 6, 1342, 4, "marginBottom"], [1651, 18, 1342, 16], [1651, 20, 1342, 18], [1652, 4, 1343, 2], [1652, 5, 1343, 3], [1653, 4, 1344, 2, "errorMessage"], [1653, 16, 1344, 14], [1653, 18, 1344, 16], [1654, 6, 1345, 4, "fontSize"], [1654, 14, 1345, 12], [1654, 16, 1345, 14], [1654, 18, 1345, 16], [1655, 6, 1346, 4, "color"], [1655, 11, 1346, 9], [1655, 13, 1346, 11], [1655, 22, 1346, 20], [1656, 6, 1347, 4, "textAlign"], [1656, 15, 1347, 13], [1656, 17, 1347, 15], [1656, 25, 1347, 23], [1657, 6, 1348, 4, "marginBottom"], [1657, 18, 1348, 16], [1657, 20, 1348, 18], [1658, 4, 1349, 2], [1658, 5, 1349, 3], [1659, 4, 1350, 2, "primaryButton"], [1659, 17, 1350, 15], [1659, 19, 1350, 17], [1660, 6, 1351, 4, "backgroundColor"], [1660, 21, 1351, 19], [1660, 23, 1351, 21], [1660, 32, 1351, 30], [1661, 6, 1352, 4, "paddingHorizontal"], [1661, 23, 1352, 21], [1661, 25, 1352, 23], [1661, 27, 1352, 25], [1662, 6, 1353, 4, "paddingVertical"], [1662, 21, 1353, 19], [1662, 23, 1353, 21], [1662, 25, 1353, 23], [1663, 6, 1354, 4, "borderRadius"], [1663, 18, 1354, 16], [1663, 20, 1354, 18], [1663, 21, 1354, 19], [1664, 6, 1355, 4, "marginTop"], [1664, 15, 1355, 13], [1664, 17, 1355, 15], [1665, 4, 1356, 2], [1665, 5, 1356, 3], [1666, 4, 1357, 2, "primaryButtonText"], [1666, 21, 1357, 19], [1666, 23, 1357, 21], [1667, 6, 1358, 4, "color"], [1667, 11, 1358, 9], [1667, 13, 1358, 11], [1667, 19, 1358, 17], [1668, 6, 1359, 4, "fontSize"], [1668, 14, 1359, 12], [1668, 16, 1359, 14], [1668, 18, 1359, 16], [1669, 6, 1360, 4, "fontWeight"], [1669, 16, 1360, 14], [1669, 18, 1360, 16], [1670, 4, 1361, 2], [1670, 5, 1361, 3], [1671, 4, 1362, 2, "secondaryButton"], [1671, 19, 1362, 17], [1671, 21, 1362, 19], [1672, 6, 1363, 4, "paddingHorizontal"], [1672, 23, 1363, 21], [1672, 25, 1363, 23], [1672, 27, 1363, 25], [1673, 6, 1364, 4, "paddingVertical"], [1673, 21, 1364, 19], [1673, 23, 1364, 21], [1673, 25, 1364, 23], [1674, 6, 1365, 4, "marginTop"], [1674, 15, 1365, 13], [1674, 17, 1365, 15], [1675, 4, 1366, 2], [1675, 5, 1366, 3], [1676, 4, 1367, 2, "secondaryButtonText"], [1676, 23, 1367, 21], [1676, 25, 1367, 23], [1677, 6, 1368, 4, "color"], [1677, 11, 1368, 9], [1677, 13, 1368, 11], [1677, 22, 1368, 20], [1678, 6, 1369, 4, "fontSize"], [1678, 14, 1369, 12], [1678, 16, 1369, 14], [1679, 4, 1370, 2], [1679, 5, 1370, 3], [1680, 4, 1371, 2, "permissionContent"], [1680, 21, 1371, 19], [1680, 23, 1371, 21], [1681, 6, 1372, 4, "flex"], [1681, 10, 1372, 8], [1681, 12, 1372, 10], [1681, 13, 1372, 11], [1682, 6, 1373, 4, "justifyContent"], [1682, 20, 1373, 18], [1682, 22, 1373, 20], [1682, 30, 1373, 28], [1683, 6, 1374, 4, "alignItems"], [1683, 16, 1374, 14], [1683, 18, 1374, 16], [1683, 26, 1374, 24], [1684, 6, 1375, 4, "padding"], [1684, 13, 1375, 11], [1684, 15, 1375, 13], [1685, 4, 1376, 2], [1685, 5, 1376, 3], [1686, 4, 1377, 2, "permissionTitle"], [1686, 19, 1377, 17], [1686, 21, 1377, 19], [1687, 6, 1378, 4, "fontSize"], [1687, 14, 1378, 12], [1687, 16, 1378, 14], [1687, 18, 1378, 16], [1688, 6, 1379, 4, "fontWeight"], [1688, 16, 1379, 14], [1688, 18, 1379, 16], [1688, 23, 1379, 21], [1689, 6, 1380, 4, "color"], [1689, 11, 1380, 9], [1689, 13, 1380, 11], [1689, 22, 1380, 20], [1690, 6, 1381, 4, "marginTop"], [1690, 15, 1381, 13], [1690, 17, 1381, 15], [1690, 19, 1381, 17], [1691, 6, 1382, 4, "marginBottom"], [1691, 18, 1382, 16], [1691, 20, 1382, 18], [1692, 4, 1383, 2], [1692, 5, 1383, 3], [1693, 4, 1384, 2, "permissionDescription"], [1693, 25, 1384, 23], [1693, 27, 1384, 25], [1694, 6, 1385, 4, "fontSize"], [1694, 14, 1385, 12], [1694, 16, 1385, 14], [1694, 18, 1385, 16], [1695, 6, 1386, 4, "color"], [1695, 11, 1386, 9], [1695, 13, 1386, 11], [1695, 22, 1386, 20], [1696, 6, 1387, 4, "textAlign"], [1696, 15, 1387, 13], [1696, 17, 1387, 15], [1696, 25, 1387, 23], [1697, 6, 1388, 4, "marginBottom"], [1697, 18, 1388, 16], [1697, 20, 1388, 18], [1698, 4, 1389, 2], [1698, 5, 1389, 3], [1699, 4, 1390, 2, "loadingText"], [1699, 15, 1390, 13], [1699, 17, 1390, 15], [1700, 6, 1391, 4, "color"], [1700, 11, 1391, 9], [1700, 13, 1391, 11], [1700, 22, 1391, 20], [1701, 6, 1392, 4, "marginTop"], [1701, 15, 1392, 13], [1701, 17, 1392, 15], [1702, 4, 1393, 2], [1702, 5, 1393, 3], [1703, 4, 1394, 2], [1704, 4, 1395, 2, "blurZone"], [1704, 12, 1395, 10], [1704, 14, 1395, 12], [1705, 6, 1396, 4, "position"], [1705, 14, 1396, 12], [1705, 16, 1396, 14], [1705, 26, 1396, 24], [1706, 6, 1397, 4, "overflow"], [1706, 14, 1397, 12], [1706, 16, 1397, 14], [1707, 4, 1398, 2], [1707, 5, 1398, 3], [1708, 4, 1399, 2, "previewChip"], [1708, 15, 1399, 13], [1708, 17, 1399, 15], [1709, 6, 1400, 4, "position"], [1709, 14, 1400, 12], [1709, 16, 1400, 14], [1709, 26, 1400, 24], [1710, 6, 1401, 4, "top"], [1710, 9, 1401, 7], [1710, 11, 1401, 9], [1710, 12, 1401, 10], [1711, 6, 1402, 4, "right"], [1711, 11, 1402, 9], [1711, 13, 1402, 11], [1711, 14, 1402, 12], [1712, 6, 1403, 4, "backgroundColor"], [1712, 21, 1403, 19], [1712, 23, 1403, 21], [1712, 40, 1403, 38], [1713, 6, 1404, 4, "paddingHorizontal"], [1713, 23, 1404, 21], [1713, 25, 1404, 23], [1713, 27, 1404, 25], [1714, 6, 1405, 4, "paddingVertical"], [1714, 21, 1405, 19], [1714, 23, 1405, 21], [1714, 24, 1405, 22], [1715, 6, 1406, 4, "borderRadius"], [1715, 18, 1406, 16], [1715, 20, 1406, 18], [1716, 4, 1407, 2], [1716, 5, 1407, 3], [1717, 4, 1408, 2, "previewChipText"], [1717, 19, 1408, 17], [1717, 21, 1408, 19], [1718, 6, 1409, 4, "color"], [1718, 11, 1409, 9], [1718, 13, 1409, 11], [1718, 19, 1409, 17], [1719, 6, 1410, 4, "fontSize"], [1719, 14, 1410, 12], [1719, 16, 1410, 14], [1719, 18, 1410, 16], [1720, 6, 1411, 4, "fontWeight"], [1720, 16, 1411, 14], [1720, 18, 1411, 16], [1721, 4, 1412, 2], [1722, 2, 1413, 0], [1722, 3, 1413, 1], [1722, 4, 1413, 2], [1723, 2, 1413, 3], [1723, 6, 1413, 3, "_c"], [1723, 8, 1413, 3], [1724, 2, 1413, 3, "$RefreshReg$"], [1724, 14, 1413, 3], [1724, 15, 1413, 3, "_c"], [1724, 17, 1413, 3], [1725, 0, 1413, 3], [1725, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError", "BlazeFaceCanvas.props.onReady"], "mappings": "AAA;eCmD;YCwB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfuE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBsC;mCoBE;wBhBc;OgBI;oFC+C;UDM;8BE8B;SFoD;uDhBa;sBmBC,wBnB;OgBC;GpByB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8BkD;W9BG;yB+BC;uBJI;aIE;W/BC;wBgCC;WhCI;uBiCqB;ejCG;CD6J"}}, "type": "js/module"}]}