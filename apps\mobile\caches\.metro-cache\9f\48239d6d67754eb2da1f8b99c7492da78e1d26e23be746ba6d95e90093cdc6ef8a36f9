{"dependencies": [{"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 41, "index": 643}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ReanimatedRecorder = void 0;\n  var _utils = require(_dependencyMap[0], \"../utils\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class ReanimatedRecorder {\n    constructor(Skia) {\n      _defineProperty(this, \"values\", new Set());\n      _defineProperty(this, \"recorder\", void 0);\n      this.recorder = Skia.Recorder();\n    }\n    processAnimationValues(props) {\n      if (!props) {\n        return;\n      }\n      Object.values(props).forEach(value => {\n        if ((0, _utils.isSharedValue)(value) && !this.values.has(value)) {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-expect-error\n          value.name = `variable${this.values.size}`;\n          this.values.add(value);\n        }\n      });\n    }\n    getRecorder() {\n      return this.recorder;\n    }\n    getSharedValues() {\n      return Array.from(this.values);\n    }\n    saveGroup() {\n      this.recorder.saveGroup();\n    }\n    restoreGroup() {\n      this.recorder.restoreGroup();\n    }\n    savePaint(props) {\n      this.processAnimationValues(props);\n      this.recorder.savePaint(props);\n    }\n    restorePaint() {\n      this.recorder.restorePaint();\n    }\n    restorePaintDeclaration() {\n      this.recorder.restorePaintDeclaration();\n    }\n    materializePaint() {\n      this.recorder.materializePaint();\n    }\n    pushPathEffect(pathEffectType, props) {\n      this.processAnimationValues(props);\n      this.recorder.pushPathEffect(pathEffectType, props);\n    }\n    pushImageFilter(imageFilterType, props) {\n      this.processAnimationValues(props);\n      this.recorder.pushImageFilter(imageFilterType, props);\n    }\n    pushColorFilter(colorFilterType, props) {\n      this.processAnimationValues(props);\n      this.recorder.pushColorFilter(colorFilterType, props);\n    }\n    pushShader(shaderType, props) {\n      this.processAnimationValues(props);\n      this.recorder.pushShader(shaderType, props);\n    }\n    pushBlurMaskFilter(props) {\n      this.processAnimationValues(props);\n      this.recorder.pushBlurMaskFilter(props);\n    }\n    composePathEffect() {\n      this.recorder.composePathEffect();\n    }\n    composeColorFilter() {\n      this.recorder.composeColorFilter();\n    }\n    composeImageFilter() {\n      this.recorder.composeImageFilter();\n    }\n    saveCTM(props) {\n      this.processAnimationValues(props);\n      this.recorder.saveCTM(props);\n    }\n    restoreCTM() {\n      this.recorder.restoreCTM();\n    }\n    drawPaint() {\n      this.recorder.drawPaint();\n    }\n    saveLayer() {\n      this.recorder.saveLayer();\n    }\n    saveBackdropFilter() {\n      this.recorder.saveBackdropFilter();\n    }\n    drawBox(boxProps, shadows) {\n      this.processAnimationValues(boxProps);\n      shadows.forEach(shadow => {\n        this.processAnimationValues(shadow.props);\n      });\n      this.recorder.drawBox(boxProps,\n      // TODO: Fix this type BaseRecorder.drawBox()\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-expect-error\n      shadows.map(s => s.props));\n    }\n    drawImage(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawImage(props);\n    }\n    drawCircle(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawCircle(props);\n    }\n    drawPoints(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawPoints(props);\n    }\n    drawPath(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawPath(props);\n    }\n    drawRect(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawRect(props);\n    }\n    drawRRect(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawRRect(props);\n    }\n    drawOval(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawOval(props);\n    }\n    drawLine(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawLine(props);\n    }\n    drawPatch(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawPatch(props);\n    }\n    drawVertices(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawVertices(props);\n    }\n    drawDiffRect(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawDiffRect(props);\n    }\n    drawText(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawText(props);\n    }\n    drawTextPath(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawTextPath(props);\n    }\n    drawTextBlob(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawTextBlob(props);\n    }\n    drawGlyphs(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawGlyphs(props);\n    }\n    drawPicture(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawPicture(props);\n    }\n    drawImageSVG(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawImageSVG(props);\n    }\n    drawParagraph(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawParagraph(props);\n    }\n    drawAtlas(props) {\n      this.processAnimationValues(props);\n      this.recorder.drawAtlas(props);\n    }\n  }\n  exports.ReanimatedRecorder = ReanimatedRecorder;\n});", "lineCount": 207, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_utils"], [6, 12, 4, 0], [6, 15, 4, 0, "require"], [6, 22, 4, 0], [6, 23, 4, 0, "_dependencyMap"], [6, 37, 4, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "e"], [7, 28, 1, 26], [7, 30, 1, 28, "r"], [7, 31, 1, 29], [7, 33, 1, 31, "t"], [7, 34, 1, 32], [7, 36, 1, 34], [8, 4, 1, 36], [8, 11, 1, 43], [8, 12, 1, 44, "r"], [8, 13, 1, 45], [8, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 30, 1, 62], [8, 31, 1, 63, "r"], [8, 32, 1, 64], [8, 33, 1, 65], [8, 38, 1, 70, "e"], [8, 39, 1, 71], [8, 42, 1, 74, "Object"], [8, 48, 1, 80], [8, 49, 1, 81, "defineProperty"], [8, 63, 1, 95], [8, 64, 1, 96, "e"], [8, 65, 1, 97], [8, 67, 1, 99, "r"], [8, 68, 1, 100], [8, 70, 1, 102], [9, 6, 1, 104, "value"], [9, 11, 1, 109], [9, 13, 1, 111, "t"], [9, 14, 1, 112], [10, 6, 1, 114, "enumerable"], [10, 16, 1, 124], [10, 18, 1, 126], [10, 19, 1, 127], [10, 20, 1, 128], [11, 6, 1, 130, "configurable"], [11, 18, 1, 142], [11, 20, 1, 144], [11, 21, 1, 145], [11, 22, 1, 146], [12, 6, 1, 148, "writable"], [12, 14, 1, 156], [12, 16, 1, 158], [12, 17, 1, 159], [13, 4, 1, 161], [13, 5, 1, 162], [13, 6, 1, 163], [13, 9, 1, 166, "e"], [13, 10, 1, 167], [13, 11, 1, 168, "r"], [13, 12, 1, 169], [13, 13, 1, 170], [13, 16, 1, 173, "t"], [13, 17, 1, 174], [13, 19, 1, 176, "e"], [13, 20, 1, 177], [14, 2, 1, 179], [15, 2, 2, 0], [15, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 26, 2, 24, "t"], [15, 27, 2, 25], [15, 29, 2, 27], [16, 4, 2, 29], [16, 8, 2, 33, "i"], [16, 9, 2, 34], [16, 12, 2, 37, "_toPrimitive"], [16, 24, 2, 49], [16, 25, 2, 50, "t"], [16, 26, 2, 51], [16, 28, 2, 53], [16, 36, 2, 61], [16, 37, 2, 62], [17, 4, 2, 64], [17, 11, 2, 71], [17, 19, 2, 79], [17, 23, 2, 83], [17, 30, 2, 90, "i"], [17, 31, 2, 91], [17, 34, 2, 94, "i"], [17, 35, 2, 95], [17, 38, 2, 98, "i"], [17, 39, 2, 99], [17, 42, 2, 102], [17, 44, 2, 104], [18, 2, 2, 106], [19, 2, 3, 0], [19, 11, 3, 9, "_toPrimitive"], [19, 23, 3, 21, "_toPrimitive"], [19, 24, 3, 22, "t"], [19, 25, 3, 23], [19, 27, 3, 25, "r"], [19, 28, 3, 26], [19, 30, 3, 28], [20, 4, 3, 30], [20, 8, 3, 34], [20, 16, 3, 42], [20, 20, 3, 46], [20, 27, 3, 53, "t"], [20, 28, 3, 54], [20, 32, 3, 58], [20, 33, 3, 59, "t"], [20, 34, 3, 60], [20, 36, 3, 62], [20, 43, 3, 69, "t"], [20, 44, 3, 70], [21, 4, 3, 72], [21, 8, 3, 76, "e"], [21, 9, 3, 77], [21, 12, 3, 80, "t"], [21, 13, 3, 81], [21, 14, 3, 82, "Symbol"], [21, 20, 3, 88], [21, 21, 3, 89, "toPrimitive"], [21, 32, 3, 100], [21, 33, 3, 101], [22, 4, 3, 103], [22, 8, 3, 107], [22, 13, 3, 112], [22, 14, 3, 113], [22, 19, 3, 118, "e"], [22, 20, 3, 119], [22, 22, 3, 121], [23, 6, 3, 123], [23, 10, 3, 127, "i"], [23, 11, 3, 128], [23, 14, 3, 131, "e"], [23, 15, 3, 132], [23, 16, 3, 133, "call"], [23, 20, 3, 137], [23, 21, 3, 138, "t"], [23, 22, 3, 139], [23, 24, 3, 141, "r"], [23, 25, 3, 142], [23, 29, 3, 146], [23, 38, 3, 155], [23, 39, 3, 156], [24, 6, 3, 158], [24, 10, 3, 162], [24, 18, 3, 170], [24, 22, 3, 174], [24, 29, 3, 181, "i"], [24, 30, 3, 182], [24, 32, 3, 184], [24, 39, 3, 191, "i"], [24, 40, 3, 192], [25, 6, 3, 194], [25, 12, 3, 200], [25, 16, 3, 204, "TypeError"], [25, 25, 3, 213], [25, 26, 3, 214], [25, 72, 3, 260], [25, 73, 3, 261], [26, 4, 3, 263], [27, 4, 3, 265], [27, 11, 3, 272], [27, 12, 3, 273], [27, 20, 3, 281], [27, 25, 3, 286, "r"], [27, 26, 3, 287], [27, 29, 3, 290, "String"], [27, 35, 3, 296], [27, 38, 3, 299, "Number"], [27, 44, 3, 305], [27, 46, 3, 307, "t"], [27, 47, 3, 308], [27, 48, 3, 309], [28, 2, 3, 311], [29, 2, 5, 7], [29, 8, 5, 13, "ReanimatedRecorder"], [29, 26, 5, 31], [29, 27, 5, 32], [30, 4, 6, 2, "constructor"], [30, 15, 6, 13, "constructor"], [30, 16, 6, 14, "Skia"], [30, 20, 6, 18], [30, 22, 6, 20], [31, 6, 7, 4, "_defineProperty"], [31, 21, 7, 19], [31, 22, 7, 20], [31, 26, 7, 24], [31, 28, 7, 26], [31, 36, 7, 34], [31, 38, 7, 36], [31, 42, 7, 40, "Set"], [31, 45, 7, 43], [31, 46, 7, 44], [31, 47, 7, 45], [31, 48, 7, 46], [32, 6, 8, 4, "_defineProperty"], [32, 21, 8, 19], [32, 22, 8, 20], [32, 26, 8, 24], [32, 28, 8, 26], [32, 38, 8, 36], [32, 40, 8, 38], [32, 45, 8, 43], [32, 46, 8, 44], [32, 47, 8, 45], [33, 6, 9, 4], [33, 10, 9, 8], [33, 11, 9, 9, "recorder"], [33, 19, 9, 17], [33, 22, 9, 20, "Skia"], [33, 26, 9, 24], [33, 27, 9, 25, "Recorder"], [33, 35, 9, 33], [33, 36, 9, 34], [33, 37, 9, 35], [34, 4, 10, 2], [35, 4, 11, 2, "processAnimationValues"], [35, 26, 11, 24, "processAnimationValues"], [35, 27, 11, 25, "props"], [35, 32, 11, 30], [35, 34, 11, 32], [36, 6, 12, 4], [36, 10, 12, 8], [36, 11, 12, 9, "props"], [36, 16, 12, 14], [36, 18, 12, 16], [37, 8, 13, 6], [38, 6, 14, 4], [39, 6, 15, 4, "Object"], [39, 12, 15, 10], [39, 13, 15, 11, "values"], [39, 19, 15, 17], [39, 20, 15, 18, "props"], [39, 25, 15, 23], [39, 26, 15, 24], [39, 27, 15, 25, "for<PERSON>ach"], [39, 34, 15, 32], [39, 35, 15, 33, "value"], [39, 40, 15, 38], [39, 44, 15, 42], [40, 8, 16, 6], [40, 12, 16, 10], [40, 16, 16, 10, "isSharedValue"], [40, 36, 16, 23], [40, 38, 16, 24, "value"], [40, 43, 16, 29], [40, 44, 16, 30], [40, 48, 16, 34], [40, 49, 16, 35], [40, 53, 16, 39], [40, 54, 16, 40, "values"], [40, 60, 16, 46], [40, 61, 16, 47, "has"], [40, 64, 16, 50], [40, 65, 16, 51, "value"], [40, 70, 16, 56], [40, 71, 16, 57], [40, 73, 16, 59], [41, 10, 17, 8], [42, 10, 18, 8], [43, 10, 19, 8, "value"], [43, 15, 19, 13], [43, 16, 19, 14, "name"], [43, 20, 19, 18], [43, 23, 19, 21], [43, 34, 19, 32], [43, 38, 19, 36], [43, 39, 19, 37, "values"], [43, 45, 19, 43], [43, 46, 19, 44, "size"], [43, 50, 19, 48], [43, 52, 19, 50], [44, 10, 20, 8], [44, 14, 20, 12], [44, 15, 20, 13, "values"], [44, 21, 20, 19], [44, 22, 20, 20, "add"], [44, 25, 20, 23], [44, 26, 20, 24, "value"], [44, 31, 20, 29], [44, 32, 20, 30], [45, 8, 21, 6], [46, 6, 22, 4], [46, 7, 22, 5], [46, 8, 22, 6], [47, 4, 23, 2], [48, 4, 24, 2, "getRecorder"], [48, 15, 24, 13, "getRecorder"], [48, 16, 24, 13], [48, 18, 24, 16], [49, 6, 25, 4], [49, 13, 25, 11], [49, 17, 25, 15], [49, 18, 25, 16, "recorder"], [49, 26, 25, 24], [50, 4, 26, 2], [51, 4, 27, 2, "getSharedValues"], [51, 19, 27, 17, "getSharedValues"], [51, 20, 27, 17], [51, 22, 27, 20], [52, 6, 28, 4], [52, 13, 28, 11, "Array"], [52, 18, 28, 16], [52, 19, 28, 17, "from"], [52, 23, 28, 21], [52, 24, 28, 22], [52, 28, 28, 26], [52, 29, 28, 27, "values"], [52, 35, 28, 33], [52, 36, 28, 34], [53, 4, 29, 2], [54, 4, 30, 2, "saveGroup"], [54, 13, 30, 11, "saveGroup"], [54, 14, 30, 11], [54, 16, 30, 14], [55, 6, 31, 4], [55, 10, 31, 8], [55, 11, 31, 9, "recorder"], [55, 19, 31, 17], [55, 20, 31, 18, "saveGroup"], [55, 29, 31, 27], [55, 30, 31, 28], [55, 31, 31, 29], [56, 4, 32, 2], [57, 4, 33, 2, "restoreGroup"], [57, 16, 33, 14, "restoreGroup"], [57, 17, 33, 14], [57, 19, 33, 17], [58, 6, 34, 4], [58, 10, 34, 8], [58, 11, 34, 9, "recorder"], [58, 19, 34, 17], [58, 20, 34, 18, "restoreGroup"], [58, 32, 34, 30], [58, 33, 34, 31], [58, 34, 34, 32], [59, 4, 35, 2], [60, 4, 36, 2, "save<PERSON><PERSON>t"], [60, 13, 36, 11, "save<PERSON><PERSON>t"], [60, 14, 36, 12, "props"], [60, 19, 36, 17], [60, 21, 36, 19], [61, 6, 37, 4], [61, 10, 37, 8], [61, 11, 37, 9, "processAnimationValues"], [61, 33, 37, 31], [61, 34, 37, 32, "props"], [61, 39, 37, 37], [61, 40, 37, 38], [62, 6, 38, 4], [62, 10, 38, 8], [62, 11, 38, 9, "recorder"], [62, 19, 38, 17], [62, 20, 38, 18, "save<PERSON><PERSON>t"], [62, 29, 38, 27], [62, 30, 38, 28, "props"], [62, 35, 38, 33], [62, 36, 38, 34], [63, 4, 39, 2], [64, 4, 40, 2, "<PERSON><PERSON><PERSON><PERSON>"], [64, 16, 40, 14, "<PERSON><PERSON><PERSON><PERSON>"], [64, 17, 40, 14], [64, 19, 40, 17], [65, 6, 41, 4], [65, 10, 41, 8], [65, 11, 41, 9, "recorder"], [65, 19, 41, 17], [65, 20, 41, 18, "<PERSON><PERSON><PERSON><PERSON>"], [65, 32, 41, 30], [65, 33, 41, 31], [65, 34, 41, 32], [66, 4, 42, 2], [67, 4, 43, 2, "restorePaintDeclaration"], [67, 27, 43, 25, "restorePaintDeclaration"], [67, 28, 43, 25], [67, 30, 43, 28], [68, 6, 44, 4], [68, 10, 44, 8], [68, 11, 44, 9, "recorder"], [68, 19, 44, 17], [68, 20, 44, 18, "restorePaintDeclaration"], [68, 43, 44, 41], [68, 44, 44, 42], [68, 45, 44, 43], [69, 4, 45, 2], [70, 4, 46, 2, "materialize<PERSON><PERSON><PERSON>"], [70, 20, 46, 18, "materialize<PERSON><PERSON><PERSON>"], [70, 21, 46, 18], [70, 23, 46, 21], [71, 6, 47, 4], [71, 10, 47, 8], [71, 11, 47, 9, "recorder"], [71, 19, 47, 17], [71, 20, 47, 18, "materialize<PERSON><PERSON><PERSON>"], [71, 36, 47, 34], [71, 37, 47, 35], [71, 38, 47, 36], [72, 4, 48, 2], [73, 4, 49, 2, "pushPathEffect"], [73, 18, 49, 16, "pushPathEffect"], [73, 19, 49, 17, "pathEffectType"], [73, 33, 49, 31], [73, 35, 49, 33, "props"], [73, 40, 49, 38], [73, 42, 49, 40], [74, 6, 50, 4], [74, 10, 50, 8], [74, 11, 50, 9, "processAnimationValues"], [74, 33, 50, 31], [74, 34, 50, 32, "props"], [74, 39, 50, 37], [74, 40, 50, 38], [75, 6, 51, 4], [75, 10, 51, 8], [75, 11, 51, 9, "recorder"], [75, 19, 51, 17], [75, 20, 51, 18, "pushPathEffect"], [75, 34, 51, 32], [75, 35, 51, 33, "pathEffectType"], [75, 49, 51, 47], [75, 51, 51, 49, "props"], [75, 56, 51, 54], [75, 57, 51, 55], [76, 4, 52, 2], [77, 4, 53, 2, "pushImageFilter"], [77, 19, 53, 17, "pushImageFilter"], [77, 20, 53, 18, "imageFilterType"], [77, 35, 53, 33], [77, 37, 53, 35, "props"], [77, 42, 53, 40], [77, 44, 53, 42], [78, 6, 54, 4], [78, 10, 54, 8], [78, 11, 54, 9, "processAnimationValues"], [78, 33, 54, 31], [78, 34, 54, 32, "props"], [78, 39, 54, 37], [78, 40, 54, 38], [79, 6, 55, 4], [79, 10, 55, 8], [79, 11, 55, 9, "recorder"], [79, 19, 55, 17], [79, 20, 55, 18, "pushImageFilter"], [79, 35, 55, 33], [79, 36, 55, 34, "imageFilterType"], [79, 51, 55, 49], [79, 53, 55, 51, "props"], [79, 58, 55, 56], [79, 59, 55, 57], [80, 4, 56, 2], [81, 4, 57, 2, "pushColorFilter"], [81, 19, 57, 17, "pushColorFilter"], [81, 20, 57, 18, "colorFilterType"], [81, 35, 57, 33], [81, 37, 57, 35, "props"], [81, 42, 57, 40], [81, 44, 57, 42], [82, 6, 58, 4], [82, 10, 58, 8], [82, 11, 58, 9, "processAnimationValues"], [82, 33, 58, 31], [82, 34, 58, 32, "props"], [82, 39, 58, 37], [82, 40, 58, 38], [83, 6, 59, 4], [83, 10, 59, 8], [83, 11, 59, 9, "recorder"], [83, 19, 59, 17], [83, 20, 59, 18, "pushColorFilter"], [83, 35, 59, 33], [83, 36, 59, 34, "colorFilterType"], [83, 51, 59, 49], [83, 53, 59, 51, "props"], [83, 58, 59, 56], [83, 59, 59, 57], [84, 4, 60, 2], [85, 4, 61, 2, "push<PERSON><PERSON>er"], [85, 14, 61, 12, "push<PERSON><PERSON>er"], [85, 15, 61, 13, "shaderType"], [85, 25, 61, 23], [85, 27, 61, 25, "props"], [85, 32, 61, 30], [85, 34, 61, 32], [86, 6, 62, 4], [86, 10, 62, 8], [86, 11, 62, 9, "processAnimationValues"], [86, 33, 62, 31], [86, 34, 62, 32, "props"], [86, 39, 62, 37], [86, 40, 62, 38], [87, 6, 63, 4], [87, 10, 63, 8], [87, 11, 63, 9, "recorder"], [87, 19, 63, 17], [87, 20, 63, 18, "push<PERSON><PERSON>er"], [87, 30, 63, 28], [87, 31, 63, 29, "shaderType"], [87, 41, 63, 39], [87, 43, 63, 41, "props"], [87, 48, 63, 46], [87, 49, 63, 47], [88, 4, 64, 2], [89, 4, 65, 2, "pushBlurMaskFilter"], [89, 22, 65, 20, "pushBlurMaskFilter"], [89, 23, 65, 21, "props"], [89, 28, 65, 26], [89, 30, 65, 28], [90, 6, 66, 4], [90, 10, 66, 8], [90, 11, 66, 9, "processAnimationValues"], [90, 33, 66, 31], [90, 34, 66, 32, "props"], [90, 39, 66, 37], [90, 40, 66, 38], [91, 6, 67, 4], [91, 10, 67, 8], [91, 11, 67, 9, "recorder"], [91, 19, 67, 17], [91, 20, 67, 18, "pushBlurMaskFilter"], [91, 38, 67, 36], [91, 39, 67, 37, "props"], [91, 44, 67, 42], [91, 45, 67, 43], [92, 4, 68, 2], [93, 4, 69, 2, "composePathEffect"], [93, 21, 69, 19, "composePathEffect"], [93, 22, 69, 19], [93, 24, 69, 22], [94, 6, 70, 4], [94, 10, 70, 8], [94, 11, 70, 9, "recorder"], [94, 19, 70, 17], [94, 20, 70, 18, "composePathEffect"], [94, 37, 70, 35], [94, 38, 70, 36], [94, 39, 70, 37], [95, 4, 71, 2], [96, 4, 72, 2, "composeColorFilter"], [96, 22, 72, 20, "composeColorFilter"], [96, 23, 72, 20], [96, 25, 72, 23], [97, 6, 73, 4], [97, 10, 73, 8], [97, 11, 73, 9, "recorder"], [97, 19, 73, 17], [97, 20, 73, 18, "composeColorFilter"], [97, 38, 73, 36], [97, 39, 73, 37], [97, 40, 73, 38], [98, 4, 74, 2], [99, 4, 75, 2, "composeImageFilter"], [99, 22, 75, 20, "composeImageFilter"], [99, 23, 75, 20], [99, 25, 75, 23], [100, 6, 76, 4], [100, 10, 76, 8], [100, 11, 76, 9, "recorder"], [100, 19, 76, 17], [100, 20, 76, 18, "composeImageFilter"], [100, 38, 76, 36], [100, 39, 76, 37], [100, 40, 76, 38], [101, 4, 77, 2], [102, 4, 78, 2, "saveCTM"], [102, 11, 78, 9, "saveCTM"], [102, 12, 78, 10, "props"], [102, 17, 78, 15], [102, 19, 78, 17], [103, 6, 79, 4], [103, 10, 79, 8], [103, 11, 79, 9, "processAnimationValues"], [103, 33, 79, 31], [103, 34, 79, 32, "props"], [103, 39, 79, 37], [103, 40, 79, 38], [104, 6, 80, 4], [104, 10, 80, 8], [104, 11, 80, 9, "recorder"], [104, 19, 80, 17], [104, 20, 80, 18, "saveCTM"], [104, 27, 80, 25], [104, 28, 80, 26, "props"], [104, 33, 80, 31], [104, 34, 80, 32], [105, 4, 81, 2], [106, 4, 82, 2, "restoreCTM"], [106, 14, 82, 12, "restoreCTM"], [106, 15, 82, 12], [106, 17, 82, 15], [107, 6, 83, 4], [107, 10, 83, 8], [107, 11, 83, 9, "recorder"], [107, 19, 83, 17], [107, 20, 83, 18, "restoreCTM"], [107, 30, 83, 28], [107, 31, 83, 29], [107, 32, 83, 30], [108, 4, 84, 2], [109, 4, 85, 2, "<PERSON><PERSON><PERSON><PERSON>"], [109, 13, 85, 11, "<PERSON><PERSON><PERSON><PERSON>"], [109, 14, 85, 11], [109, 16, 85, 14], [110, 6, 86, 4], [110, 10, 86, 8], [110, 11, 86, 9, "recorder"], [110, 19, 86, 17], [110, 20, 86, 18, "<PERSON><PERSON><PERSON><PERSON>"], [110, 29, 86, 27], [110, 30, 86, 28], [110, 31, 86, 29], [111, 4, 87, 2], [112, 4, 88, 2, "save<PERSON><PERSON><PERSON>"], [112, 13, 88, 11, "save<PERSON><PERSON><PERSON>"], [112, 14, 88, 11], [112, 16, 88, 14], [113, 6, 89, 4], [113, 10, 89, 8], [113, 11, 89, 9, "recorder"], [113, 19, 89, 17], [113, 20, 89, 18, "save<PERSON><PERSON><PERSON>"], [113, 29, 89, 27], [113, 30, 89, 28], [113, 31, 89, 29], [114, 4, 90, 2], [115, 4, 91, 2, "saveBackdropFilter"], [115, 22, 91, 20, "saveBackdropFilter"], [115, 23, 91, 20], [115, 25, 91, 23], [116, 6, 92, 4], [116, 10, 92, 8], [116, 11, 92, 9, "recorder"], [116, 19, 92, 17], [116, 20, 92, 18, "saveBackdropFilter"], [116, 38, 92, 36], [116, 39, 92, 37], [116, 40, 92, 38], [117, 4, 93, 2], [118, 4, 94, 2, "drawBox"], [118, 11, 94, 9, "drawBox"], [118, 12, 94, 10, "boxProps"], [118, 20, 94, 18], [118, 22, 94, 20, "shadows"], [118, 29, 94, 27], [118, 31, 94, 29], [119, 6, 95, 4], [119, 10, 95, 8], [119, 11, 95, 9, "processAnimationValues"], [119, 33, 95, 31], [119, 34, 95, 32, "boxProps"], [119, 42, 95, 40], [119, 43, 95, 41], [120, 6, 96, 4, "shadows"], [120, 13, 96, 11], [120, 14, 96, 12, "for<PERSON>ach"], [120, 21, 96, 19], [120, 22, 96, 20, "shadow"], [120, 28, 96, 26], [120, 32, 96, 30], [121, 8, 97, 6], [121, 12, 97, 10], [121, 13, 97, 11, "processAnimationValues"], [121, 35, 97, 33], [121, 36, 97, 34, "shadow"], [121, 42, 97, 40], [121, 43, 97, 41, "props"], [121, 48, 97, 46], [121, 49, 97, 47], [122, 6, 98, 4], [122, 7, 98, 5], [122, 8, 98, 6], [123, 6, 99, 4], [123, 10, 99, 8], [123, 11, 99, 9, "recorder"], [123, 19, 99, 17], [123, 20, 99, 18, "drawBox"], [123, 27, 99, 25], [123, 28, 99, 26, "boxProps"], [123, 36, 99, 34], [124, 6, 100, 4], [125, 6, 101, 4], [126, 6, 102, 4], [127, 6, 103, 4, "shadows"], [127, 13, 103, 11], [127, 14, 103, 12, "map"], [127, 17, 103, 15], [127, 18, 103, 16, "s"], [127, 19, 103, 17], [127, 23, 103, 21, "s"], [127, 24, 103, 22], [127, 25, 103, 23, "props"], [127, 30, 103, 28], [127, 31, 103, 29], [127, 32, 103, 30], [128, 4, 104, 2], [129, 4, 105, 2, "drawImage"], [129, 13, 105, 11, "drawImage"], [129, 14, 105, 12, "props"], [129, 19, 105, 17], [129, 21, 105, 19], [130, 6, 106, 4], [130, 10, 106, 8], [130, 11, 106, 9, "processAnimationValues"], [130, 33, 106, 31], [130, 34, 106, 32, "props"], [130, 39, 106, 37], [130, 40, 106, 38], [131, 6, 107, 4], [131, 10, 107, 8], [131, 11, 107, 9, "recorder"], [131, 19, 107, 17], [131, 20, 107, 18, "drawImage"], [131, 29, 107, 27], [131, 30, 107, 28, "props"], [131, 35, 107, 33], [131, 36, 107, 34], [132, 4, 108, 2], [133, 4, 109, 2, "drawCircle"], [133, 14, 109, 12, "drawCircle"], [133, 15, 109, 13, "props"], [133, 20, 109, 18], [133, 22, 109, 20], [134, 6, 110, 4], [134, 10, 110, 8], [134, 11, 110, 9, "processAnimationValues"], [134, 33, 110, 31], [134, 34, 110, 32, "props"], [134, 39, 110, 37], [134, 40, 110, 38], [135, 6, 111, 4], [135, 10, 111, 8], [135, 11, 111, 9, "recorder"], [135, 19, 111, 17], [135, 20, 111, 18, "drawCircle"], [135, 30, 111, 28], [135, 31, 111, 29, "props"], [135, 36, 111, 34], [135, 37, 111, 35], [136, 4, 112, 2], [137, 4, 113, 2, "drawPoints"], [137, 14, 113, 12, "drawPoints"], [137, 15, 113, 13, "props"], [137, 20, 113, 18], [137, 22, 113, 20], [138, 6, 114, 4], [138, 10, 114, 8], [138, 11, 114, 9, "processAnimationValues"], [138, 33, 114, 31], [138, 34, 114, 32, "props"], [138, 39, 114, 37], [138, 40, 114, 38], [139, 6, 115, 4], [139, 10, 115, 8], [139, 11, 115, 9, "recorder"], [139, 19, 115, 17], [139, 20, 115, 18, "drawPoints"], [139, 30, 115, 28], [139, 31, 115, 29, "props"], [139, 36, 115, 34], [139, 37, 115, 35], [140, 4, 116, 2], [141, 4, 117, 2, "drawPath"], [141, 12, 117, 10, "drawPath"], [141, 13, 117, 11, "props"], [141, 18, 117, 16], [141, 20, 117, 18], [142, 6, 118, 4], [142, 10, 118, 8], [142, 11, 118, 9, "processAnimationValues"], [142, 33, 118, 31], [142, 34, 118, 32, "props"], [142, 39, 118, 37], [142, 40, 118, 38], [143, 6, 119, 4], [143, 10, 119, 8], [143, 11, 119, 9, "recorder"], [143, 19, 119, 17], [143, 20, 119, 18, "drawPath"], [143, 28, 119, 26], [143, 29, 119, 27, "props"], [143, 34, 119, 32], [143, 35, 119, 33], [144, 4, 120, 2], [145, 4, 121, 2, "drawRect"], [145, 12, 121, 10, "drawRect"], [145, 13, 121, 11, "props"], [145, 18, 121, 16], [145, 20, 121, 18], [146, 6, 122, 4], [146, 10, 122, 8], [146, 11, 122, 9, "processAnimationValues"], [146, 33, 122, 31], [146, 34, 122, 32, "props"], [146, 39, 122, 37], [146, 40, 122, 38], [147, 6, 123, 4], [147, 10, 123, 8], [147, 11, 123, 9, "recorder"], [147, 19, 123, 17], [147, 20, 123, 18, "drawRect"], [147, 28, 123, 26], [147, 29, 123, 27, "props"], [147, 34, 123, 32], [147, 35, 123, 33], [148, 4, 124, 2], [149, 4, 125, 2, "drawRRect"], [149, 13, 125, 11, "drawRRect"], [149, 14, 125, 12, "props"], [149, 19, 125, 17], [149, 21, 125, 19], [150, 6, 126, 4], [150, 10, 126, 8], [150, 11, 126, 9, "processAnimationValues"], [150, 33, 126, 31], [150, 34, 126, 32, "props"], [150, 39, 126, 37], [150, 40, 126, 38], [151, 6, 127, 4], [151, 10, 127, 8], [151, 11, 127, 9, "recorder"], [151, 19, 127, 17], [151, 20, 127, 18, "drawRRect"], [151, 29, 127, 27], [151, 30, 127, 28, "props"], [151, 35, 127, 33], [151, 36, 127, 34], [152, 4, 128, 2], [153, 4, 129, 2, "drawOval"], [153, 12, 129, 10, "drawOval"], [153, 13, 129, 11, "props"], [153, 18, 129, 16], [153, 20, 129, 18], [154, 6, 130, 4], [154, 10, 130, 8], [154, 11, 130, 9, "processAnimationValues"], [154, 33, 130, 31], [154, 34, 130, 32, "props"], [154, 39, 130, 37], [154, 40, 130, 38], [155, 6, 131, 4], [155, 10, 131, 8], [155, 11, 131, 9, "recorder"], [155, 19, 131, 17], [155, 20, 131, 18, "drawOval"], [155, 28, 131, 26], [155, 29, 131, 27, "props"], [155, 34, 131, 32], [155, 35, 131, 33], [156, 4, 132, 2], [157, 4, 133, 2, "drawLine"], [157, 12, 133, 10, "drawLine"], [157, 13, 133, 11, "props"], [157, 18, 133, 16], [157, 20, 133, 18], [158, 6, 134, 4], [158, 10, 134, 8], [158, 11, 134, 9, "processAnimationValues"], [158, 33, 134, 31], [158, 34, 134, 32, "props"], [158, 39, 134, 37], [158, 40, 134, 38], [159, 6, 135, 4], [159, 10, 135, 8], [159, 11, 135, 9, "recorder"], [159, 19, 135, 17], [159, 20, 135, 18, "drawLine"], [159, 28, 135, 26], [159, 29, 135, 27, "props"], [159, 34, 135, 32], [159, 35, 135, 33], [160, 4, 136, 2], [161, 4, 137, 2, "drawPatch"], [161, 13, 137, 11, "drawPatch"], [161, 14, 137, 12, "props"], [161, 19, 137, 17], [161, 21, 137, 19], [162, 6, 138, 4], [162, 10, 138, 8], [162, 11, 138, 9, "processAnimationValues"], [162, 33, 138, 31], [162, 34, 138, 32, "props"], [162, 39, 138, 37], [162, 40, 138, 38], [163, 6, 139, 4], [163, 10, 139, 8], [163, 11, 139, 9, "recorder"], [163, 19, 139, 17], [163, 20, 139, 18, "drawPatch"], [163, 29, 139, 27], [163, 30, 139, 28, "props"], [163, 35, 139, 33], [163, 36, 139, 34], [164, 4, 140, 2], [165, 4, 141, 2, "drawVertices"], [165, 16, 141, 14, "drawVertices"], [165, 17, 141, 15, "props"], [165, 22, 141, 20], [165, 24, 141, 22], [166, 6, 142, 4], [166, 10, 142, 8], [166, 11, 142, 9, "processAnimationValues"], [166, 33, 142, 31], [166, 34, 142, 32, "props"], [166, 39, 142, 37], [166, 40, 142, 38], [167, 6, 143, 4], [167, 10, 143, 8], [167, 11, 143, 9, "recorder"], [167, 19, 143, 17], [167, 20, 143, 18, "drawVertices"], [167, 32, 143, 30], [167, 33, 143, 31, "props"], [167, 38, 143, 36], [167, 39, 143, 37], [168, 4, 144, 2], [169, 4, 145, 2, "drawDiffRect"], [169, 16, 145, 14, "drawDiffRect"], [169, 17, 145, 15, "props"], [169, 22, 145, 20], [169, 24, 145, 22], [170, 6, 146, 4], [170, 10, 146, 8], [170, 11, 146, 9, "processAnimationValues"], [170, 33, 146, 31], [170, 34, 146, 32, "props"], [170, 39, 146, 37], [170, 40, 146, 38], [171, 6, 147, 4], [171, 10, 147, 8], [171, 11, 147, 9, "recorder"], [171, 19, 147, 17], [171, 20, 147, 18, "drawDiffRect"], [171, 32, 147, 30], [171, 33, 147, 31, "props"], [171, 38, 147, 36], [171, 39, 147, 37], [172, 4, 148, 2], [173, 4, 149, 2, "drawText"], [173, 12, 149, 10, "drawText"], [173, 13, 149, 11, "props"], [173, 18, 149, 16], [173, 20, 149, 18], [174, 6, 150, 4], [174, 10, 150, 8], [174, 11, 150, 9, "processAnimationValues"], [174, 33, 150, 31], [174, 34, 150, 32, "props"], [174, 39, 150, 37], [174, 40, 150, 38], [175, 6, 151, 4], [175, 10, 151, 8], [175, 11, 151, 9, "recorder"], [175, 19, 151, 17], [175, 20, 151, 18, "drawText"], [175, 28, 151, 26], [175, 29, 151, 27, "props"], [175, 34, 151, 32], [175, 35, 151, 33], [176, 4, 152, 2], [177, 4, 153, 2, "drawTextPath"], [177, 16, 153, 14, "drawTextPath"], [177, 17, 153, 15, "props"], [177, 22, 153, 20], [177, 24, 153, 22], [178, 6, 154, 4], [178, 10, 154, 8], [178, 11, 154, 9, "processAnimationValues"], [178, 33, 154, 31], [178, 34, 154, 32, "props"], [178, 39, 154, 37], [178, 40, 154, 38], [179, 6, 155, 4], [179, 10, 155, 8], [179, 11, 155, 9, "recorder"], [179, 19, 155, 17], [179, 20, 155, 18, "drawTextPath"], [179, 32, 155, 30], [179, 33, 155, 31, "props"], [179, 38, 155, 36], [179, 39, 155, 37], [180, 4, 156, 2], [181, 4, 157, 2, "drawTextBlob"], [181, 16, 157, 14, "drawTextBlob"], [181, 17, 157, 15, "props"], [181, 22, 157, 20], [181, 24, 157, 22], [182, 6, 158, 4], [182, 10, 158, 8], [182, 11, 158, 9, "processAnimationValues"], [182, 33, 158, 31], [182, 34, 158, 32, "props"], [182, 39, 158, 37], [182, 40, 158, 38], [183, 6, 159, 4], [183, 10, 159, 8], [183, 11, 159, 9, "recorder"], [183, 19, 159, 17], [183, 20, 159, 18, "drawTextBlob"], [183, 32, 159, 30], [183, 33, 159, 31, "props"], [183, 38, 159, 36], [183, 39, 159, 37], [184, 4, 160, 2], [185, 4, 161, 2, "drawGlyphs"], [185, 14, 161, 12, "drawGlyphs"], [185, 15, 161, 13, "props"], [185, 20, 161, 18], [185, 22, 161, 20], [186, 6, 162, 4], [186, 10, 162, 8], [186, 11, 162, 9, "processAnimationValues"], [186, 33, 162, 31], [186, 34, 162, 32, "props"], [186, 39, 162, 37], [186, 40, 162, 38], [187, 6, 163, 4], [187, 10, 163, 8], [187, 11, 163, 9, "recorder"], [187, 19, 163, 17], [187, 20, 163, 18, "drawGlyphs"], [187, 30, 163, 28], [187, 31, 163, 29, "props"], [187, 36, 163, 34], [187, 37, 163, 35], [188, 4, 164, 2], [189, 4, 165, 2, "drawPicture"], [189, 15, 165, 13, "drawPicture"], [189, 16, 165, 14, "props"], [189, 21, 165, 19], [189, 23, 165, 21], [190, 6, 166, 4], [190, 10, 166, 8], [190, 11, 166, 9, "processAnimationValues"], [190, 33, 166, 31], [190, 34, 166, 32, "props"], [190, 39, 166, 37], [190, 40, 166, 38], [191, 6, 167, 4], [191, 10, 167, 8], [191, 11, 167, 9, "recorder"], [191, 19, 167, 17], [191, 20, 167, 18, "drawPicture"], [191, 31, 167, 29], [191, 32, 167, 30, "props"], [191, 37, 167, 35], [191, 38, 167, 36], [192, 4, 168, 2], [193, 4, 169, 2, "drawImageSVG"], [193, 16, 169, 14, "drawImageSVG"], [193, 17, 169, 15, "props"], [193, 22, 169, 20], [193, 24, 169, 22], [194, 6, 170, 4], [194, 10, 170, 8], [194, 11, 170, 9, "processAnimationValues"], [194, 33, 170, 31], [194, 34, 170, 32, "props"], [194, 39, 170, 37], [194, 40, 170, 38], [195, 6, 171, 4], [195, 10, 171, 8], [195, 11, 171, 9, "recorder"], [195, 19, 171, 17], [195, 20, 171, 18, "drawImageSVG"], [195, 32, 171, 30], [195, 33, 171, 31, "props"], [195, 38, 171, 36], [195, 39, 171, 37], [196, 4, 172, 2], [197, 4, 173, 2, "drawParagraph"], [197, 17, 173, 15, "drawParagraph"], [197, 18, 173, 16, "props"], [197, 23, 173, 21], [197, 25, 173, 23], [198, 6, 174, 4], [198, 10, 174, 8], [198, 11, 174, 9, "processAnimationValues"], [198, 33, 174, 31], [198, 34, 174, 32, "props"], [198, 39, 174, 37], [198, 40, 174, 38], [199, 6, 175, 4], [199, 10, 175, 8], [199, 11, 175, 9, "recorder"], [199, 19, 175, 17], [199, 20, 175, 18, "drawParagraph"], [199, 33, 175, 31], [199, 34, 175, 32, "props"], [199, 39, 175, 37], [199, 40, 175, 38], [200, 4, 176, 2], [201, 4, 177, 2, "drawAtlas"], [201, 13, 177, 11, "drawAtlas"], [201, 14, 177, 12, "props"], [201, 19, 177, 17], [201, 21, 177, 19], [202, 6, 178, 4], [202, 10, 178, 8], [202, 11, 178, 9, "processAnimationValues"], [202, 33, 178, 31], [202, 34, 178, 32, "props"], [202, 39, 178, 37], [202, 40, 178, 38], [203, 6, 179, 4], [203, 10, 179, 8], [203, 11, 179, 9, "recorder"], [203, 19, 179, 17], [203, 20, 179, 18, "drawAtlas"], [203, 29, 179, 27], [203, 30, 179, 28, "props"], [203, 35, 179, 33], [203, 36, 179, 34], [204, 4, 180, 2], [205, 2, 181, 0], [206, 2, 181, 1, "exports"], [206, 9, 181, 1], [206, 10, 181, 1, "ReanimatedRecorder"], [206, 28, 181, 1], [206, 31, 181, 1, "ReanimatedRecorder"], [206, 49, 181, 1], [207, 0, 181, 1], [207, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "ReanimatedRecorder", "constructor", "processAnimationValues", "Object.values.forEach$argument_0", "getRecorder", "getSharedValues", "saveGroup", "restoreGroup", "save<PERSON><PERSON>t", "<PERSON><PERSON><PERSON><PERSON>", "restorePaintDeclaration", "materialize<PERSON><PERSON><PERSON>", "pushPathEffect", "pushImageFilter", "pushColorFilter", "push<PERSON><PERSON>er", "pushBlurMaskFilter", "composePathEffect", "composeColorFilter", "composeImageFilter", "saveCTM", "restoreCTM", "<PERSON><PERSON><PERSON><PERSON>", "save<PERSON><PERSON><PERSON>", "saveBackdropFilter", "drawBox", "shadows.forEach$argument_0", "shadows.map$argument_0", "drawImage", "drawCircle", "drawPoints", "drawPath", "drawRect", "drawRRect", "drawOval", "drawLine", "drawPatch", "drawVertices", "drawDiffRect", "drawText", "drawTextPath", "drawTextBlob", "drawGlyphs", "drawPicture", "drawImageSVG", "drawParagraph", "drawAtlas"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGE;ECC;GDI;EEC;iCCI;KDO;GFC;EIC;GJE;EKC;GLE;EMC;GNE;EOC;GPE;EQC;GRG;ESC;GTE;EUC;GVE;EWC;GXE;EYC;GZG;EaC;GbG;EcC;GdG;EeC;GfG;EgBC;GhBG;EiBC;GjBE;EkBC;GlBE;EmBC;GnBE;EoBC;GpBG;EqBC;GrBE;EsBC;GtBE;EuBC;GvBE;EwBC;GxBE;EyBC;oBCE;KDE;gBEK,YF;GzBC;E4BC;G5BG;E6BC;G7BG;E8BC;G9BG;E+BC;G/BG;EgCC;GhCG;EiCC;GjCG;EkCC;GlCG;EmCC;GnCG;EoCC;GpCG;EqCC;GrCG;EsCC;GtCG;EuCC;GvCG;EwCC;GxCG;EyCC;GzCG;E0CC;G1CG;E2CC;G3CG;E4CC;G5CG;E6CC;G7CG;E8CC;G9CG;CHC"}}, "type": "js/module"}]}