{"dependencies": [{"name": "../animationParser.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 74, "index": 89}}], "key": "O2GgmGIlz6MOk52iJY+MJ4hFpWQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LightSpeedOutData = exports.LightSpeedOut = exports.LightSpeedInData = exports.LightSpeedIn = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser.js\");\n  const DEFAULT_LIGHTSPEED_TIME = 0.3;\n  const LightSpeedInData = exports.LightSpeedInData = {\n    LightSpeedInRight: {\n      name: 'LightSpeedInRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '100vw',\n            skewX: '-45deg'\n          }],\n          opacity: 0\n        },\n        70: {\n          transform: [{\n            skewX: '10deg'\n          }]\n        },\n        85: {\n          transform: [{\n            skewX: '-5deg'\n          }]\n        },\n        100: {\n          transform: [{\n            skewX: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    },\n    LightSpeedInLeft: {\n      name: 'LightSpeedInLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '-100vw',\n            skewX: '45deg'\n          }],\n          opacity: 0\n        },\n        70: {\n          transform: [{\n            skewX: '-10deg'\n          }]\n        },\n        85: {\n          transform: [{\n            skewX: '5deg'\n          }]\n        },\n        100: {\n          transform: [{\n            skewX: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    }\n  };\n  const LightSpeedOutData = exports.LightSpeedOutData = {\n    LightSpeedOutRight: {\n      name: 'LightSpeedOutRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            skewX: '0deg'\n          }],\n          opacity: 1\n        },\n        100: {\n          transform: [{\n            translateX: '100vw',\n            skewX: '-45deg'\n          }],\n          opacity: 0\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    },\n    LightSpeedOutLeft: {\n      name: 'LightSpeedOutLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            skew: '0deg'\n          }],\n          opacity: 1\n        },\n        100: {\n          transform: [{\n            translateX: '-100vw',\n            skew: '45deg'\n          }],\n          opacity: 0\n        }\n      },\n      duration: DEFAULT_LIGHTSPEED_TIME\n    }\n  };\n  const LightSpeedIn = exports.LightSpeedIn = {\n    LightSpeedInRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedInData.LightSpeedInRight),\n      duration: LightSpeedInData.LightSpeedInRight.duration\n    },\n    LightSpeedInLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedInData.LightSpeedInLeft),\n      duration: LightSpeedInData.LightSpeedInLeft.duration\n    }\n  };\n  const LightSpeedOut = exports.LightSpeedOut = {\n    LightSpeedOutRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedOutData.LightSpeedOutRight),\n      duration: LightSpeedOutData.LightSpeedOutRight.duration\n    },\n    LightSpeedOutLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(LightSpeedOutData.LightSpeedOutLeft),\n      duration: LightSpeedOutData.LightSpeedOutLeft.duration\n    }\n  };\n});", "lineCount": 130, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "LightSpeedOutData"], [7, 27, 1, 13], [7, 30, 1, 13, "exports"], [7, 37, 1, 13], [7, 38, 1, 13, "LightSpeedOut"], [7, 51, 1, 13], [7, 54, 1, 13, "exports"], [7, 61, 1, 13], [7, 62, 1, 13, "LightSpeedInData"], [7, 78, 1, 13], [7, 81, 1, 13, "exports"], [7, 88, 1, 13], [7, 89, 1, 13, "LightSpeedIn"], [7, 101, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_animation<PERSON><PERSON>er"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "DEFAULT_LIGHTSPEED_TIME"], [9, 31, 4, 29], [9, 34, 4, 32], [9, 37, 4, 35], [10, 2, 5, 7], [10, 8, 5, 13, "LightSpeedInData"], [10, 24, 5, 29], [10, 27, 5, 29, "exports"], [10, 34, 5, 29], [10, 35, 5, 29, "LightSpeedInData"], [10, 51, 5, 29], [10, 54, 5, 32], [11, 4, 6, 2, "LightSpeedInRight"], [11, 21, 6, 19], [11, 23, 6, 21], [12, 6, 7, 4, "name"], [12, 10, 7, 8], [12, 12, 7, 10], [12, 31, 7, 29], [13, 6, 8, 4, "style"], [13, 11, 8, 9], [13, 13, 8, 11], [14, 8, 9, 6], [14, 9, 9, 7], [14, 11, 9, 9], [15, 10, 10, 8, "transform"], [15, 19, 10, 17], [15, 21, 10, 19], [15, 22, 10, 20], [16, 12, 11, 10, "translateX"], [16, 22, 11, 20], [16, 24, 11, 22], [16, 31, 11, 29], [17, 12, 12, 10, "skewX"], [17, 17, 12, 15], [17, 19, 12, 17], [18, 10, 13, 8], [18, 11, 13, 9], [18, 12, 13, 10], [19, 10, 14, 8, "opacity"], [19, 17, 14, 15], [19, 19, 14, 17], [20, 8, 15, 6], [20, 9, 15, 7], [21, 8, 16, 6], [21, 10, 16, 8], [21, 12, 16, 10], [22, 10, 17, 8, "transform"], [22, 19, 17, 17], [22, 21, 17, 19], [22, 22, 17, 20], [23, 12, 18, 10, "skewX"], [23, 17, 18, 15], [23, 19, 18, 17], [24, 10, 19, 8], [24, 11, 19, 9], [25, 8, 20, 6], [25, 9, 20, 7], [26, 8, 21, 6], [26, 10, 21, 8], [26, 12, 21, 10], [27, 10, 22, 8, "transform"], [27, 19, 22, 17], [27, 21, 22, 19], [27, 22, 22, 20], [28, 12, 23, 10, "skewX"], [28, 17, 23, 15], [28, 19, 23, 17], [29, 10, 24, 8], [29, 11, 24, 9], [30, 8, 25, 6], [30, 9, 25, 7], [31, 8, 26, 6], [31, 11, 26, 9], [31, 13, 26, 11], [32, 10, 27, 8, "transform"], [32, 19, 27, 17], [32, 21, 27, 19], [32, 22, 27, 20], [33, 12, 28, 10, "skewX"], [33, 17, 28, 15], [33, 19, 28, 17], [34, 10, 29, 8], [34, 11, 29, 9], [35, 8, 30, 6], [36, 6, 31, 4], [36, 7, 31, 5], [37, 6, 32, 4, "duration"], [37, 14, 32, 12], [37, 16, 32, 14, "DEFAULT_LIGHTSPEED_TIME"], [38, 4, 33, 2], [38, 5, 33, 3], [39, 4, 34, 2, "LightSpeedInLeft"], [39, 20, 34, 18], [39, 22, 34, 20], [40, 6, 35, 4, "name"], [40, 10, 35, 8], [40, 12, 35, 10], [40, 30, 35, 28], [41, 6, 36, 4, "style"], [41, 11, 36, 9], [41, 13, 36, 11], [42, 8, 37, 6], [42, 9, 37, 7], [42, 11, 37, 9], [43, 10, 38, 8, "transform"], [43, 19, 38, 17], [43, 21, 38, 19], [43, 22, 38, 20], [44, 12, 39, 10, "translateX"], [44, 22, 39, 20], [44, 24, 39, 22], [44, 32, 39, 30], [45, 12, 40, 10, "skewX"], [45, 17, 40, 15], [45, 19, 40, 17], [46, 10, 41, 8], [46, 11, 41, 9], [46, 12, 41, 10], [47, 10, 42, 8, "opacity"], [47, 17, 42, 15], [47, 19, 42, 17], [48, 8, 43, 6], [48, 9, 43, 7], [49, 8, 44, 6], [49, 10, 44, 8], [49, 12, 44, 10], [50, 10, 45, 8, "transform"], [50, 19, 45, 17], [50, 21, 45, 19], [50, 22, 45, 20], [51, 12, 46, 10, "skewX"], [51, 17, 46, 15], [51, 19, 46, 17], [52, 10, 47, 8], [52, 11, 47, 9], [53, 8, 48, 6], [53, 9, 48, 7], [54, 8, 49, 6], [54, 10, 49, 8], [54, 12, 49, 10], [55, 10, 50, 8, "transform"], [55, 19, 50, 17], [55, 21, 50, 19], [55, 22, 50, 20], [56, 12, 51, 10, "skewX"], [56, 17, 51, 15], [56, 19, 51, 17], [57, 10, 52, 8], [57, 11, 52, 9], [58, 8, 53, 6], [58, 9, 53, 7], [59, 8, 54, 6], [59, 11, 54, 9], [59, 13, 54, 11], [60, 10, 55, 8, "transform"], [60, 19, 55, 17], [60, 21, 55, 19], [60, 22, 55, 20], [61, 12, 56, 10, "skewX"], [61, 17, 56, 15], [61, 19, 56, 17], [62, 10, 57, 8], [62, 11, 57, 9], [63, 8, 58, 6], [64, 6, 59, 4], [64, 7, 59, 5], [65, 6, 60, 4, "duration"], [65, 14, 60, 12], [65, 16, 60, 14, "DEFAULT_LIGHTSPEED_TIME"], [66, 4, 61, 2], [67, 2, 62, 0], [67, 3, 62, 1], [68, 2, 63, 7], [68, 8, 63, 13, "LightSpeedOutData"], [68, 25, 63, 30], [68, 28, 63, 30, "exports"], [68, 35, 63, 30], [68, 36, 63, 30, "LightSpeedOutData"], [68, 53, 63, 30], [68, 56, 63, 33], [69, 4, 64, 2, "LightSpeedOutRight"], [69, 22, 64, 20], [69, 24, 64, 22], [70, 6, 65, 4, "name"], [70, 10, 65, 8], [70, 12, 65, 10], [70, 32, 65, 30], [71, 6, 66, 4, "style"], [71, 11, 66, 9], [71, 13, 66, 11], [72, 8, 67, 6], [72, 9, 67, 7], [72, 11, 67, 9], [73, 10, 68, 8, "transform"], [73, 19, 68, 17], [73, 21, 68, 19], [73, 22, 68, 20], [74, 12, 69, 10, "translateX"], [74, 22, 69, 20], [74, 24, 69, 22], [74, 29, 69, 27], [75, 12, 70, 10, "skewX"], [75, 17, 70, 15], [75, 19, 70, 17], [76, 10, 71, 8], [76, 11, 71, 9], [76, 12, 71, 10], [77, 10, 72, 8, "opacity"], [77, 17, 72, 15], [77, 19, 72, 17], [78, 8, 73, 6], [78, 9, 73, 7], [79, 8, 74, 6], [79, 11, 74, 9], [79, 13, 74, 11], [80, 10, 75, 8, "transform"], [80, 19, 75, 17], [80, 21, 75, 19], [80, 22, 75, 20], [81, 12, 76, 10, "translateX"], [81, 22, 76, 20], [81, 24, 76, 22], [81, 31, 76, 29], [82, 12, 77, 10, "skewX"], [82, 17, 77, 15], [82, 19, 77, 17], [83, 10, 78, 8], [83, 11, 78, 9], [83, 12, 78, 10], [84, 10, 79, 8, "opacity"], [84, 17, 79, 15], [84, 19, 79, 17], [85, 8, 80, 6], [86, 6, 81, 4], [86, 7, 81, 5], [87, 6, 82, 4, "duration"], [87, 14, 82, 12], [87, 16, 82, 14, "DEFAULT_LIGHTSPEED_TIME"], [88, 4, 83, 2], [88, 5, 83, 3], [89, 4, 84, 2, "LightSpeedOutLeft"], [89, 21, 84, 19], [89, 23, 84, 21], [90, 6, 85, 4, "name"], [90, 10, 85, 8], [90, 12, 85, 10], [90, 31, 85, 29], [91, 6, 86, 4, "style"], [91, 11, 86, 9], [91, 13, 86, 11], [92, 8, 87, 6], [92, 9, 87, 7], [92, 11, 87, 9], [93, 10, 88, 8, "transform"], [93, 19, 88, 17], [93, 21, 88, 19], [93, 22, 88, 20], [94, 12, 89, 10, "translateX"], [94, 22, 89, 20], [94, 24, 89, 22], [94, 29, 89, 27], [95, 12, 90, 10, "skew"], [95, 16, 90, 14], [95, 18, 90, 16], [96, 10, 91, 8], [96, 11, 91, 9], [96, 12, 91, 10], [97, 10, 92, 8, "opacity"], [97, 17, 92, 15], [97, 19, 92, 17], [98, 8, 93, 6], [98, 9, 93, 7], [99, 8, 94, 6], [99, 11, 94, 9], [99, 13, 94, 11], [100, 10, 95, 8, "transform"], [100, 19, 95, 17], [100, 21, 95, 19], [100, 22, 95, 20], [101, 12, 96, 10, "translateX"], [101, 22, 96, 20], [101, 24, 96, 22], [101, 32, 96, 30], [102, 12, 97, 10, "skew"], [102, 16, 97, 14], [102, 18, 97, 16], [103, 10, 98, 8], [103, 11, 98, 9], [103, 12, 98, 10], [104, 10, 99, 8, "opacity"], [104, 17, 99, 15], [104, 19, 99, 17], [105, 8, 100, 6], [106, 6, 101, 4], [106, 7, 101, 5], [107, 6, 102, 4, "duration"], [107, 14, 102, 12], [107, 16, 102, 14, "DEFAULT_LIGHTSPEED_TIME"], [108, 4, 103, 2], [109, 2, 104, 0], [109, 3, 104, 1], [110, 2, 105, 7], [110, 8, 105, 13, "LightSpeedIn"], [110, 20, 105, 25], [110, 23, 105, 25, "exports"], [110, 30, 105, 25], [110, 31, 105, 25, "LightSpeedIn"], [110, 43, 105, 25], [110, 46, 105, 28], [111, 4, 106, 2, "LightSpeedInRight"], [111, 21, 106, 19], [111, 23, 106, 21], [112, 6, 107, 4, "style"], [112, 11, 107, 9], [112, 13, 107, 11], [112, 17, 107, 11, "convertAnimationObjectToKeyframes"], [112, 67, 107, 44], [112, 69, 107, 45, "LightSpeedInData"], [112, 85, 107, 61], [112, 86, 107, 62, "LightSpeedInRight"], [112, 103, 107, 79], [112, 104, 107, 80], [113, 6, 108, 4, "duration"], [113, 14, 108, 12], [113, 16, 108, 14, "LightSpeedInData"], [113, 32, 108, 30], [113, 33, 108, 31, "LightSpeedInRight"], [113, 50, 108, 48], [113, 51, 108, 49, "duration"], [114, 4, 109, 2], [114, 5, 109, 3], [115, 4, 110, 2, "LightSpeedInLeft"], [115, 20, 110, 18], [115, 22, 110, 20], [116, 6, 111, 4, "style"], [116, 11, 111, 9], [116, 13, 111, 11], [116, 17, 111, 11, "convertAnimationObjectToKeyframes"], [116, 67, 111, 44], [116, 69, 111, 45, "LightSpeedInData"], [116, 85, 111, 61], [116, 86, 111, 62, "LightSpeedInLeft"], [116, 102, 111, 78], [116, 103, 111, 79], [117, 6, 112, 4, "duration"], [117, 14, 112, 12], [117, 16, 112, 14, "LightSpeedInData"], [117, 32, 112, 30], [117, 33, 112, 31, "LightSpeedInLeft"], [117, 49, 112, 47], [117, 50, 112, 48, "duration"], [118, 4, 113, 2], [119, 2, 114, 0], [119, 3, 114, 1], [120, 2, 115, 7], [120, 8, 115, 13, "LightSpeedOut"], [120, 21, 115, 26], [120, 24, 115, 26, "exports"], [120, 31, 115, 26], [120, 32, 115, 26, "LightSpeedOut"], [120, 45, 115, 26], [120, 48, 115, 29], [121, 4, 116, 2, "LightSpeedOutRight"], [121, 22, 116, 20], [121, 24, 116, 22], [122, 6, 117, 4, "style"], [122, 11, 117, 9], [122, 13, 117, 11], [122, 17, 117, 11, "convertAnimationObjectToKeyframes"], [122, 67, 117, 44], [122, 69, 117, 45, "LightSpeedOutData"], [122, 86, 117, 62], [122, 87, 117, 63, "LightSpeedOutRight"], [122, 105, 117, 81], [122, 106, 117, 82], [123, 6, 118, 4, "duration"], [123, 14, 118, 12], [123, 16, 118, 14, "LightSpeedOutData"], [123, 33, 118, 31], [123, 34, 118, 32, "LightSpeedOutRight"], [123, 52, 118, 50], [123, 53, 118, 51, "duration"], [124, 4, 119, 2], [124, 5, 119, 3], [125, 4, 120, 2, "LightSpeedOutLeft"], [125, 21, 120, 19], [125, 23, 120, 21], [126, 6, 121, 4, "style"], [126, 11, 121, 9], [126, 13, 121, 11], [126, 17, 121, 11, "convertAnimationObjectToKeyframes"], [126, 67, 121, 44], [126, 69, 121, 45, "LightSpeedOutData"], [126, 86, 121, 62], [126, 87, 121, 63, "LightSpeedOutLeft"], [126, 104, 121, 80], [126, 105, 121, 81], [127, 6, 122, 4, "duration"], [127, 14, 122, 12], [127, 16, 122, 14, "LightSpeedOutData"], [127, 33, 122, 31], [127, 34, 122, 32, "LightSpeedOutLeft"], [127, 51, 122, 49], [127, 52, 122, 50, "duration"], [128, 4, 123, 2], [129, 2, 124, 0], [129, 3, 124, 1], [130, 0, 124, 2], [130, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}