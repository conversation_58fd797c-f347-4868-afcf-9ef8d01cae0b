<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TensorFlow.js BlazeFace Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        video, canvas {
            width: 100%;
            max-width: 400px;
            height: auto;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .container {
            position: relative;
            display: inline-block;
        }
        .overlay-canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h1>TensorFlow.js BlazeFace Face Detection Test</h1>
    <p>This page tests TensorFlow.js with BlazeFace for reliable cross-browser face detection.</p>

    <div class="test-section">
        <h2>1. Library Loading Test</h2>
        <button onclick="loadLibraries()">Load TensorFlow.js & BlazeFace</button>
        <div id="library-status"></div>
    </div>

    <div class="test-section">
        <h2>2. Camera Test</h2>
        <button onclick="startCamera()">Start Camera</button>
        <button onclick="stopCamera()">Stop Camera</button>
        <div id="camera-status"></div>
        <div class="container" id="video-container">
            <video id="video" autoplay muted playsinline style="display: none;"></video>
            <canvas id="overlay" class="overlay-canvas" style="display: none;"></canvas>
        </div>
    </div>

    <div class="test-section">
        <h2>3. Face Detection Test</h2>
        <button onclick="startDetection()" id="detect-btn" disabled>Start Live Detection</button>
        <button onclick="stopDetection()" id="stop-detect-btn" disabled>Stop Detection</button>
        <div id="detection-status"></div>
    </div>

    <script>
        let video, overlay, ctx;
        let stream = null;
        let model = null;
        let isDetecting = false;
        let animationId = null;

        // Initialize elements
        document.addEventListener('DOMContentLoaded', function() {
            video = document.getElementById('video');
            overlay = document.getElementById('overlay');
            ctx = overlay.getContext('2d');
        });

        function logStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            element.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function loadLibraries() {
            try {
                logStatus('library-status', 'Loading TensorFlow.js...', 'info');
                
                // Load TensorFlow.js
                if (!window.tf) {
                    await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js');
                    logStatus('library-status', '✅ TensorFlow.js loaded successfully', 'success');
                } else {
                    logStatus('library-status', '✅ TensorFlow.js already loaded', 'success');
                }

                // Load BlazeFace
                if (!window.blazeface) {
                    logStatus('library-status', 'Loading BlazeFace model...', 'info');
                    await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');
                    logStatus('library-status', '✅ BlazeFace library loaded', 'success');
                } else {
                    logStatus('library-status', '✅ BlazeFace already loaded', 'success');
                }

                // Initialize BlazeFace model
                logStatus('library-status', 'Initializing BlazeFace model...', 'info');
                model = await window.blazeface.load();
                logStatus('library-status', '✅ BlazeFace model initialized successfully!', 'success');
                
            } catch (error) {
                logStatus('library-status', `❌ Failed to load libraries: ${error.message}`, 'error');
            }
        }

        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.async = true;
                script.onload = () => resolve();
                script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
                document.head.appendChild(script);
            });
        }

        async function startCamera() {
            try {
                logStatus('camera-status', 'Requesting camera access...', 'info');
                
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 640 }, 
                        height: { ideal: 480 },
                        facingMode: 'user'
                    } 
                });
                
                video.srcObject = stream;
                video.style.display = 'block';
                
                video.onloadedmetadata = () => {
                    // Set overlay canvas size to match video
                    overlay.width = video.videoWidth;
                    overlay.height = video.videoHeight;
                    overlay.style.width = video.offsetWidth + 'px';
                    overlay.style.height = video.offsetHeight + 'px';
                    overlay.style.display = 'block';
                    
                    logStatus('camera-status', '✅ Camera started successfully', 'success');
                    document.getElementById('detect-btn').disabled = !model;
                };
                
            } catch (error) {
                logStatus('camera-status', `❌ Camera access failed: ${error.message}`, 'error');
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
                video.style.display = 'none';
                overlay.style.display = 'none';
                document.getElementById('detect-btn').disabled = true;
                document.getElementById('stop-detect-btn').disabled = true;
                stopDetection();
                logStatus('camera-status', '🛑 Camera stopped', 'info');
            }
        }

        async function startDetection() {
            if (!model) {
                logStatus('detection-status', '❌ BlazeFace model not loaded', 'error');
                return;
            }

            if (!video.videoWidth || !video.videoHeight) {
                logStatus('detection-status', '❌ Video not ready', 'error');
                return;
            }

            isDetecting = true;
            document.getElementById('detect-btn').disabled = true;
            document.getElementById('stop-detect-btn').disabled = false;
            
            logStatus('detection-status', '🎯 Starting live face detection...', 'info');
            
            detectLoop();
        }

        function stopDetection() {
            isDetecting = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            document.getElementById('detect-btn').disabled = false;
            document.getElementById('stop-detect-btn').disabled = true;
            
            // Clear overlay
            ctx.clearRect(0, 0, overlay.width, overlay.height);
            
            logStatus('detection-status', '🛑 Face detection stopped', 'info');
        }

        async function detectLoop() {
            if (!isDetecting) return;

            try {
                // Detect faces
                const tensor = window.tf.browser.fromPixels(video);
                const predictions = await model.estimateFaces(tensor, false, 0.6);
                tensor.dispose();

                // Clear overlay
                ctx.clearRect(0, 0, overlay.width, overlay.height);

                if (predictions.length > 0) {
                    logStatus('detection-status', `🎯 Detected ${predictions.length} face(s)`, 'success');
                    
                    // Draw face detection and blur
                    predictions.forEach((prediction, index) => {
                        const [x1, y1] = prediction.topLeft;
                        const [x2, y2] = prediction.bottomRight;
                        
                        // Expand the bounding box
                        const centerX = (x1 + x2) / 2;
                        const centerY = (y1 + y2) / 2;
                        const faceWidth = (x2 - x1) * 1.4;
                        const faceHeight = (y2 - y1) * 1.6;
                        
                        const expandedX1 = centerX - faceWidth / 2;
                        const expandedY1 = centerY - faceHeight / 2;

                        // Draw blur effect
                        ctx.save();
                        ctx.beginPath();
                        ctx.ellipse(centerX, centerY, faceWidth / 2, faceHeight / 2, 0, 0, Math.PI * 2);
                        ctx.clip();
                        ctx.filter = 'blur(30px)';
                        ctx.drawImage(video, 0, 0, overlay.width, overlay.height);
                        ctx.restore();

                        // Draw detection rectangle for debugging
                        ctx.strokeStyle = '#00ff00';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(expandedX1, expandedY1, faceWidth, faceHeight);
                        
                        // Add face number
                        ctx.fillStyle = '#00ff00';
                        ctx.font = '16px Arial';
                        ctx.fillText(`Face ${index + 1}`, expandedX1, expandedY1 - 5);
                    });
                }

            } catch (error) {
                console.error('Detection error:', error);
            }

            animationId = requestAnimationFrame(detectLoop);
        }
    </script>
</body>
</html>
