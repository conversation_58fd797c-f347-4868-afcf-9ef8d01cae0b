{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getLocalAssetUri = getLocalAssetUri;\n  function getLocalAssetUri(hash, type) {\n    // noop on web\n    return null;\n  }\n});", "lineCount": 10, "map": [[6, 2, 1, 7], [6, 11, 1, 16, "getLocalAssetUri"], [6, 27, 1, 32, "getLocalAssetUri"], [6, 28, 1, 33, "hash"], [6, 32, 1, 37], [6, 34, 1, 39, "type"], [6, 38, 1, 43], [6, 40, 1, 45], [7, 4, 2, 4], [8, 4, 3, 4], [8, 11, 3, 11], [8, 15, 3, 15], [9, 2, 4, 0], [10, 0, 4, 1], [10, 3]], "functionMap": {"names": ["<global>", "getLocalAssetUri"], "mappings": "AAA,OC;CDG"}}, "type": "js/module"}]}