"use strict";
/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var _this = this;
Object.defineProperty(exports, "__esModule", { value: true });
var tf = require("../index");
var jasmine_util_1 = require("../jasmine_util");
var test_util_1 = require("../test_util");
var tensor_ops_1 = require("./tensor_ops");
jasmine_util_1.describeWithFlags('inTopKAsync', jasmine_util_1.ALL_ENVS, function () { return __awaiter(_this, void 0, void 0, function () {
    var _this = this;
    return __generator(this, function (_a) {
        it('predictions 2d array, targets 1d array, with default k', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, precision, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor2d([[20, 10, 40, 30], [30, 50, -20, 10]]);
                        targets = tensor_ops_1.tensor1d([2, 0]);
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets)];
                    case 1:
                        precision = _b.sent();
                        expect(precision.shape).toEqual([2]);
                        expect(precision.dtype).toBe('bool');
                        _a = test_util_1.expectArraysClose;
                        return [4 /*yield*/, precision.data()];
                    case 2:
                        _a.apply(void 0, [_b.sent(), [1, 0]]);
                        return [2 /*return*/];
                }
            });
        }); });
        it('predictions 2d array, targets 1d array, with k=2', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, k, precision, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor2d([[20, 10, 40, 30], [30, 50, -20, 10]]);
                        targets = tensor_ops_1.tensor1d([2, 0]);
                        k = 2;
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets, k)];
                    case 1:
                        precision = _b.sent();
                        expect(precision.shape).toEqual([2]);
                        expect(precision.dtype).toBe('bool');
                        _a = test_util_1.expectArraysClose;
                        return [4 /*yield*/, precision.data()];
                    case 2:
                        _a.apply(void 0, [_b.sent(), [1, 1]]);
                        return [2 /*return*/];
                }
            });
        }); });
        it('predictions 3d array, targets 2d array, with default k', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, precision, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor3d([[[1, 5, 2], [4, 3, 6]], [[3, 2, 1], [1, 2, 3]]]);
                        targets = tensor_ops_1.tensor2d([[1, 2], [0, 1]]);
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets)];
                    case 1:
                        precision = _b.sent();
                        expect(precision.shape).toEqual([2, 2]);
                        expect(precision.dtype).toBe('bool');
                        _a = test_util_1.expectArraysClose;
                        return [4 /*yield*/, precision.data()];
                    case 2:
                        _a.apply(void 0, [_b.sent(), [1, 1, 1, 0]]);
                        return [2 /*return*/];
                }
            });
        }); });
        it('predictions 3d array, targets 2d array, with k=2', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, k, precision, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor3d([[[1, 5, 2], [4, 3, 6]], [[3, 2, 1], [1, 2, 3]]]);
                        targets = tensor_ops_1.tensor2d([[1, 2], [0, 1]]);
                        k = 2;
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets, k)];
                    case 1:
                        precision = _b.sent();
                        expect(precision.shape).toEqual([2, 2]);
                        expect(precision.dtype).toBe('bool');
                        _a = test_util_1.expectArraysClose;
                        return [4 /*yield*/, precision.data()];
                    case 2:
                        _a.apply(void 0, [_b.sent(), [1, 1, 1, 1]]);
                        return [2 /*return*/];
                }
            });
        }); });
        it('lower-index element count first, with default k', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets1, precision1, _a, targets2, precision2, _b;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor2d([[1, 2, 2, 1]]);
                        targets1 = tensor_ops_1.tensor1d([1]);
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets1)];
                    case 1:
                        precision1 = _c.sent();
                        expect(precision1.shape).toEqual([1]);
                        expect(precision1.dtype).toBe('bool');
                        _a = test_util_1.expectArraysClose;
                        return [4 /*yield*/, precision1.data()];
                    case 2:
                        _a.apply(void 0, [_c.sent(), [1]]);
                        targets2 = tensor_ops_1.tensor1d([2]);
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets2)];
                    case 3:
                        precision2 = _c.sent();
                        expect(precision2.shape).toEqual([1]);
                        expect(precision2.dtype).toBe('bool');
                        _b = test_util_1.expectArraysClose;
                        return [4 /*yield*/, precision2.data()];
                    case 4:
                        _b.apply(void 0, [_c.sent(), [0]]);
                        return [2 /*return*/];
                }
            });
        }); });
        it('accept tensor-like object, with default k', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, precision, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        predictions = [[20, 10, 40, 30], [30, 50, -20, 10]];
                        targets = [2, 0];
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets)];
                    case 1:
                        precision = _b.sent();
                        expect(precision.shape).toEqual([2]);
                        expect(precision.dtype).toBe('bool');
                        _a = test_util_1.expectArraysClose;
                        return [4 /*yield*/, precision.data()];
                    case 2:
                        _a.apply(void 0, [_b.sent(), [1, 0]]);
                        return [2 /*return*/];
                }
            });
        }); });
        it('doesnt leak tensors with tensor-like objects', function () { return __awaiter(_this, void 0, void 0, function () {
            var numTensors, predictions, targets, precision;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        numTensors = tf.memory().numTensors;
                        predictions = [[20, 10, 40, 30], [30, 50, -20, 10]];
                        targets = [2, 0];
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets)];
                    case 1:
                        precision = _a.sent();
                        precision.dispose();
                        expect(tf.memory().numTensors).toBe(numTensors);
                        return [2 /*return*/];
                }
            });
        }); });
        it('throws when predictions_rank <2', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, ex_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor1d([20, 10, 40, 30]);
                        targets = [2];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets)];
                    case 2:
                        _a.sent();
                        throw new Error('The line above should have thrown an error');
                    case 3:
                        ex_1 = _a.sent();
                        expect(ex_1.message)
                            .toEqual('inTopK() expects the predictions to ' +
                            'be of rank 2 or higher, but got 1');
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        }); });
        it('throws when prediction.rank != targets.rank + 1', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, ex_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor2d([[20, 10, 40, 30], [30, 50, -20, 10]]);
                        targets = tensor_ops_1.tensor2d([[0], [0]]);
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets)];
                    case 2:
                        _a.sent();
                        throw new Error('The line above should have thrown an error');
                    case 3:
                        ex_2 = _a.sent();
                        expect(ex_2.message)
                            .toEqual('predictions rank should be 1 larger than targets rank,' +
                            ' but got predictions rank 2 and targets rank 2');
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        }); });
        it('throws when k > size of last dimension of predictions', function () { return __awaiter(_this, void 0, void 0, function () {
            var predictions, targets, k, ex_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        predictions = tensor_ops_1.tensor2d([[20, 10, 40, 30], [30, 50, -20, 10]]);
                        targets = tensor_ops_1.tensor1d([2, 0]);
                        k = 5;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, tf.inTopKAsync(predictions, targets, k)];
                    case 2:
                        _a.sent();
                        throw new Error('The line above should have thrown an error');
                    case 3:
                        ex_3 = _a.sent();
                        expect(ex_3.message)
                            .toEqual('\'k\' passed to inTopK() must be > 0 && <= the predictions ' +
                            'last dimension (4), but got 5');
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        }); });
        return [2 /*return*/];
    });
}); });
//# sourceMappingURL=in_top_k_test.js.map