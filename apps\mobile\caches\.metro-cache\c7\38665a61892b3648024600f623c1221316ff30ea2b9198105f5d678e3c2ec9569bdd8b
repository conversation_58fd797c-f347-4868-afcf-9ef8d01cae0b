{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Color = void 0;\n  const alphaf = c => (c >> 24 & 255) / 255;\n  const red = c => c >> 16 & 255;\n  const green = c => c >> 8 & 255;\n  const blue = c => c & 255;\n\n  // From https://raw.githubusercontent.com/deanm/css-color-parser-js/master/csscolorparser.js\n  const CSSColorTable = {\n    transparent: Float32Array.of(0, 0, 0, 0),\n    aliceblue: Float32Array.of(240, 248, 255, 1),\n    antiquewhite: Float32Array.of(250, 235, 215, 1),\n    aqua: Float32Array.of(0, 255, 255, 1),\n    aquamarine: Float32Array.of(127, 255, 212, 1),\n    azure: Float32Array.of(240, 255, 255, 1),\n    beige: Float32Array.of(245, 245, 220, 1),\n    bisque: Float32Array.of(255, 228, 196, 1),\n    black: Float32Array.of(0, 0, 0, 1),\n    blanchedalmond: Float32Array.of(255, 235, 205, 1),\n    blue: Float32Array.of(0, 0, 255, 1),\n    blueviolet: Float32Array.of(138, 43, 226, 1),\n    brown: Float32Array.of(165, 42, 42, 1),\n    burlywood: Float32Array.of(222, 184, 135, 1),\n    cadetblue: Float32Array.of(95, 158, 160, 1),\n    chartreuse: Float32Array.of(127, 255, 0, 1),\n    chocolate: Float32Array.of(210, 105, 30, 1),\n    coral: Float32Array.of(255, 127, 80, 1),\n    cornflowerblue: Float32Array.of(100, 149, 237, 1),\n    cornsilk: Float32Array.of(255, 248, 220, 1),\n    crimson: Float32Array.of(220, 20, 60, 1),\n    cyan: Float32Array.of(0, 255, 255, 1),\n    darkblue: Float32Array.of(0, 0, 139, 1),\n    darkcyan: Float32Array.of(0, 139, 139, 1),\n    darkgoldenrod: Float32Array.of(184, 134, 11, 1),\n    darkgray: Float32Array.of(169, 169, 169, 1),\n    darkgreen: Float32Array.of(0, 100, 0, 1),\n    darkgrey: Float32Array.of(169, 169, 169, 1),\n    darkkhaki: Float32Array.of(189, 183, 107, 1),\n    darkmagenta: Float32Array.of(139, 0, 139, 1),\n    darkolivegreen: Float32Array.of(85, 107, 47, 1),\n    darkorange: Float32Array.of(255, 140, 0, 1),\n    darkorchid: Float32Array.of(153, 50, 204, 1),\n    darkred: Float32Array.of(139, 0, 0, 1),\n    darksalmon: Float32Array.of(233, 150, 122, 1),\n    darkseagreen: Float32Array.of(143, 188, 143, 1),\n    darkslateblue: Float32Array.of(72, 61, 139, 1),\n    darkslategray: Float32Array.of(47, 79, 79, 1),\n    darkslategrey: Float32Array.of(47, 79, 79, 1),\n    darkturquoise: Float32Array.of(0, 206, 209, 1),\n    darkviolet: Float32Array.of(148, 0, 211, 1),\n    deeppink: Float32Array.of(255, 20, 147, 1),\n    deepskyblue: Float32Array.of(0, 191, 255, 1),\n    dimgray: Float32Array.of(105, 105, 105, 1),\n    dimgrey: Float32Array.of(105, 105, 105, 1),\n    dodgerblue: Float32Array.of(30, 144, 255, 1),\n    firebrick: Float32Array.of(178, 34, 34, 1),\n    floralwhite: Float32Array.of(255, 250, 240, 1),\n    forestgreen: Float32Array.of(34, 139, 34, 1),\n    fuchsia: Float32Array.of(255, 0, 255, 1),\n    gainsboro: Float32Array.of(220, 220, 220, 1),\n    ghostwhite: Float32Array.of(248, 248, 255, 1),\n    gold: Float32Array.of(255, 215, 0, 1),\n    goldenrod: Float32Array.of(218, 165, 32, 1),\n    gray: Float32Array.of(128, 128, 128, 1),\n    green: Float32Array.of(0, 128, 0, 1),\n    greenyellow: Float32Array.of(173, 255, 47, 1),\n    grey: Float32Array.of(128, 128, 128, 1),\n    honeydew: Float32Array.of(240, 255, 240, 1),\n    hotpink: Float32Array.of(255, 105, 180, 1),\n    indianred: Float32Array.of(205, 92, 92, 1),\n    indigo: Float32Array.of(75, 0, 130, 1),\n    ivory: Float32Array.of(255, 255, 240, 1),\n    khaki: Float32Array.of(240, 230, 140, 1),\n    lavender: Float32Array.of(230, 230, 250, 1),\n    lavenderblush: Float32Array.of(255, 240, 245, 1),\n    lawngreen: Float32Array.of(124, 252, 0, 1),\n    lemonchiffon: Float32Array.of(255, 250, 205, 1),\n    lightblue: Float32Array.of(173, 216, 230, 1),\n    lightcoral: Float32Array.of(240, 128, 128, 1),\n    lightcyan: Float32Array.of(224, 255, 255, 1),\n    lightgoldenrodyellow: Float32Array.of(250, 250, 210, 1),\n    lightgray: Float32Array.of(211, 211, 211, 1),\n    lightgreen: Float32Array.of(144, 238, 144, 1),\n    lightgrey: Float32Array.of(211, 211, 211, 1),\n    lightpink: Float32Array.of(255, 182, 193, 1),\n    lightsalmon: Float32Array.of(255, 160, 122, 1),\n    lightseagreen: Float32Array.of(32, 178, 170, 1),\n    lightskyblue: Float32Array.of(135, 206, 250, 1),\n    lightslategray: Float32Array.of(119, 136, 153, 1),\n    lightslategrey: Float32Array.of(119, 136, 153, 1),\n    lightsteelblue: Float32Array.of(176, 196, 222, 1),\n    lightyellow: Float32Array.of(255, 255, 224, 1),\n    lime: Float32Array.of(0, 255, 0, 1),\n    limegreen: Float32Array.of(50, 205, 50, 1),\n    linen: Float32Array.of(250, 240, 230, 1),\n    magenta: Float32Array.of(255, 0, 255, 1),\n    maroon: Float32Array.of(128, 0, 0, 1),\n    mediumaquamarine: Float32Array.of(102, 205, 170, 1),\n    mediumblue: Float32Array.of(0, 0, 205, 1),\n    mediumorchid: Float32Array.of(186, 85, 211, 1),\n    mediumpurple: Float32Array.of(147, 112, 219, 1),\n    mediumseagreen: Float32Array.of(60, 179, 113, 1),\n    mediumslateblue: Float32Array.of(123, 104, 238, 1),\n    mediumspringgreen: Float32Array.of(0, 250, 154, 1),\n    mediumturquoise: Float32Array.of(72, 209, 204, 1),\n    mediumvioletred: Float32Array.of(199, 21, 133, 1),\n    midnightblue: Float32Array.of(25, 25, 112, 1),\n    mintcream: Float32Array.of(245, 255, 250, 1),\n    mistyrose: Float32Array.of(255, 228, 225, 1),\n    moccasin: Float32Array.of(255, 228, 181, 1),\n    navajowhite: Float32Array.of(255, 222, 173, 1),\n    navy: Float32Array.of(0, 0, 128, 1),\n    oldlace: Float32Array.of(253, 245, 230, 1),\n    olive: Float32Array.of(128, 128, 0, 1),\n    olivedrab: Float32Array.of(107, 142, 35, 1),\n    orange: Float32Array.of(255, 165, 0, 1),\n    orangered: Float32Array.of(255, 69, 0, 1),\n    orchid: Float32Array.of(218, 112, 214, 1),\n    palegoldenrod: Float32Array.of(238, 232, 170, 1),\n    palegreen: Float32Array.of(152, 251, 152, 1),\n    paleturquoise: Float32Array.of(175, 238, 238, 1),\n    palevioletred: Float32Array.of(219, 112, 147, 1),\n    papayawhip: Float32Array.of(255, 239, 213, 1),\n    peachpuff: Float32Array.of(255, 218, 185, 1),\n    peru: Float32Array.of(205, 133, 63, 1),\n    pink: Float32Array.of(255, 192, 203, 1),\n    plum: Float32Array.of(221, 160, 221, 1),\n    powderblue: Float32Array.of(176, 224, 230, 1),\n    purple: Float32Array.of(128, 0, 128, 1),\n    rebeccapurple: Float32Array.of(102, 51, 153, 1),\n    red: Float32Array.of(255, 0, 0, 1),\n    rosybrown: Float32Array.of(188, 143, 143, 1),\n    royalblue: Float32Array.of(65, 105, 225, 1),\n    saddlebrown: Float32Array.of(139, 69, 19, 1),\n    salmon: Float32Array.of(250, 128, 114, 1),\n    sandybrown: Float32Array.of(244, 164, 96, 1),\n    seagreen: Float32Array.of(46, 139, 87, 1),\n    seashell: Float32Array.of(255, 245, 238, 1),\n    sienna: Float32Array.of(160, 82, 45, 1),\n    silver: Float32Array.of(192, 192, 192, 1),\n    skyblue: Float32Array.of(135, 206, 235, 1),\n    slateblue: Float32Array.of(106, 90, 205, 1),\n    slategray: Float32Array.of(112, 128, 144, 1),\n    slategrey: Float32Array.of(112, 128, 144, 1),\n    snow: Float32Array.of(255, 250, 250, 1),\n    springgreen: Float32Array.of(0, 255, 127, 1),\n    steelblue: Float32Array.of(70, 130, 180, 1),\n    tan: Float32Array.of(210, 180, 140, 1),\n    teal: Float32Array.of(0, 128, 128, 1),\n    thistle: Float32Array.of(216, 191, 216, 1),\n    tomato: Float32Array.of(255, 99, 71, 1),\n    turquoise: Float32Array.of(64, 224, 208, 1),\n    violet: Float32Array.of(238, 130, 238, 1),\n    wheat: Float32Array.of(245, 222, 179, 1),\n    white: Float32Array.of(255, 255, 255, 1),\n    whitesmoke: Float32Array.of(245, 245, 245, 1),\n    yellow: Float32Array.of(255, 255, 0, 1),\n    yellowgreen: Float32Array.of(154, 205, 50, 1)\n  };\n  const clampCSSByte = j => {\n    // Clamp to integer 0 .. 255.\n    const i = Math.round(j); // Seems to be what Chrome does (vs truncation).\n    // eslint-disable-next-line no-nested-ternary\n    return i < 0 ? 0 : i > 255 ? 255 : i;\n  };\n  const clampCSSFloat = f => {\n    // eslint-disable-next-line no-nested-ternary\n    return f < 0 ? 0 : f > 1 ? 1 : f;\n  };\n  const parseCSSInt = str => {\n    // int or percentage.\n    if (str[str.length - 1] === \"%\") {\n      return clampCSSByte(parseFloat(str) / 100 * 255);\n    }\n    // eslint-disable-next-line radix\n    return clampCSSByte(parseInt(str));\n  };\n  const parseCSSFloat = str => {\n    if (str === undefined) {\n      return 1;\n    }\n    // float or percentage.\n    if (str[str.length - 1] === \"%\") {\n      return clampCSSFloat(parseFloat(str) / 100);\n    }\n    return clampCSSFloat(parseFloat(str));\n  };\n  const CSSHueToRGB = (m1, m2, h) => {\n    if (h < 0) {\n      h += 1;\n    } else if (h > 1) {\n      h -= 1;\n    }\n    if (h * 6 < 1) {\n      return m1 + (m2 - m1) * h * 6;\n    }\n    if (h * 2 < 1) {\n      return m2;\n    }\n    if (h * 3 < 2) {\n      return m1 + (m2 - m1) * (2 / 3 - h) * 6;\n    }\n    return m1;\n  };\n  const parseCSSColor = cssStr => {\n    // Remove all whitespace, not compliant, but should just be more accepting.\n    var str = cssStr.replace(/ /g, \"\").toLowerCase();\n\n    // Color keywords (and transparent) lookup.\n    if (str in CSSColorTable) {\n      const cl = CSSColorTable[str];\n      if (cl) {\n        return Float32Array.of(...cl);\n      }\n      return null;\n    } // dup.\n\n    // #abc and #abc123 syntax.\n    if (str[0] === \"#\") {\n      if (str.length === 4) {\n        var iv = parseInt(str.substr(1), 16); // TODO(deanm): Stricter parsing.\n        if (!(iv >= 0 && iv <= 0xfff)) {\n          return null;\n        } // Covers NaN.\n        return [(iv & 0xf00) >> 4 | (iv & 0xf00) >> 8, iv & 0xf0 | (iv & 0xf0) >> 4, iv & 0xf | (iv & 0xf) << 4, 1];\n      } else if (str.length === 7) {\n        var iv = parseInt(str.substr(1), 16); // TODO(deanm): Stricter parsing.\n        if (!(iv >= 0 && iv <= 0xffffff)) {\n          return null;\n        } // Covers NaN.\n        return [(iv & 0xff0000) >> 16, (iv & 0xff00) >> 8, iv & 0xff, 1];\n      } else if (str.length === 9) {\n        var iv = parseInt(str.substr(1), 16); // TODO(deanm): Stricter parsing.\n        if (!(iv >= 0 && iv <= 0xffffffff)) {\n          return null; // Covers NaN.\n        }\n        return [(iv & 0xff000000) >> 24 & 0xff, (iv & 0xff0000) >> 16, (iv & 0xff00) >> 8, (iv & 0xff) / 255];\n      }\n      return null;\n    }\n    var op = str.indexOf(\"(\"),\n      ep = str.indexOf(\")\");\n    if (op !== -1 && ep + 1 === str.length) {\n      var fname = str.substr(0, op);\n      var params = str.substr(op + 1, ep - (op + 1)).split(\",\");\n      var alpha = 1; // To allow case fallthrough.\n      switch (fname) {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        //@ts-expect-error\n        case \"rgba\":\n          if (params.length !== 4) {\n            return null;\n          }\n          alpha = parseCSSFloat(params.pop());\n        // Fall through.\n        case \"rgb\":\n          if (params.length !== 3) {\n            return null;\n          }\n          return [parseCSSInt(params[0]), parseCSSInt(params[1]), parseCSSInt(params[2]), alpha];\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        //@ts-expect-error\n        case \"hsla\":\n          if (params.length !== 4) {\n            return null;\n          }\n          alpha = parseCSSFloat(params.pop());\n        // Fall through.\n        case \"hsl\":\n          if (params.length !== 3) {\n            return null;\n          }\n          var h = (parseFloat(params[0]) % 360 + 360) % 360 / 360; // 0 .. 1\n          // NOTE(deanm): According to the CSS spec s/l should only be\n          // percentages, but we don't bother and let float or percentage.\n          var s = parseCSSFloat(params[1]);\n          var l = parseCSSFloat(params[2]);\n          var m2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n          var m1 = l * 2 - m2;\n          return [clampCSSByte(CSSHueToRGB(m1, m2, h + 1 / 3) * 255), clampCSSByte(CSSHueToRGB(m1, m2, h) * 255), clampCSSByte(CSSHueToRGB(m1, m2, h - 1 / 3) * 255), alpha];\n        default:\n          return null;\n      }\n    }\n    return null;\n  };\n  const Color = color => {\n    if (color instanceof Float32Array) {\n      return color;\n    } else if (Array.isArray(color)) {\n      return new Float32Array(color);\n    } else if (typeof color === \"string\") {\n      const r = parseCSSColor(color);\n      const rgba = r === null ? CSSColorTable.black : r;\n      return Float32Array.of(rgba[0] / 255, rgba[1] / 255, rgba[2] / 255, rgba[3]);\n    } else {\n      return Float32Array.of(red(color) / 255, green(color) / 255, blue(color) / 255, alphaf(color));\n    }\n  };\n  exports.Color = Color;\n});", "lineCount": 304, "map": [[6, 2, 1, 0], [6, 8, 1, 6, "alphaf"], [6, 14, 1, 12], [6, 17, 1, 15, "c"], [6, 18, 1, 16], [6, 22, 1, 20], [6, 23, 1, 21, "c"], [6, 24, 1, 22], [6, 28, 1, 26], [6, 30, 1, 28], [6, 33, 1, 31], [6, 36, 1, 34], [6, 40, 1, 38], [6, 43, 1, 41], [7, 2, 2, 0], [7, 8, 2, 6, "red"], [7, 11, 2, 9], [7, 14, 2, 12, "c"], [7, 15, 2, 13], [7, 19, 2, 17, "c"], [7, 20, 2, 18], [7, 24, 2, 22], [7, 26, 2, 24], [7, 29, 2, 27], [7, 32, 2, 30], [8, 2, 3, 0], [8, 8, 3, 6, "green"], [8, 13, 3, 11], [8, 16, 3, 14, "c"], [8, 17, 3, 15], [8, 21, 3, 19, "c"], [8, 22, 3, 20], [8, 26, 3, 24], [8, 27, 3, 25], [8, 30, 3, 28], [8, 33, 3, 31], [9, 2, 4, 0], [9, 8, 4, 6, "blue"], [9, 12, 4, 10], [9, 15, 4, 13, "c"], [9, 16, 4, 14], [9, 20, 4, 18, "c"], [9, 21, 4, 19], [9, 24, 4, 22], [9, 27, 4, 25], [11, 2, 6, 0], [12, 2, 7, 0], [12, 8, 7, 6, "CSSColorTable"], [12, 21, 7, 19], [12, 24, 7, 22], [13, 4, 8, 2, "transparent"], [13, 15, 8, 13], [13, 17, 8, 15, "Float32Array"], [13, 29, 8, 27], [13, 30, 8, 28, "of"], [13, 32, 8, 30], [13, 33, 8, 31], [13, 34, 8, 32], [13, 36, 8, 34], [13, 37, 8, 35], [13, 39, 8, 37], [13, 40, 8, 38], [13, 42, 8, 40], [13, 43, 8, 41], [13, 44, 8, 42], [14, 4, 9, 2, "aliceblue"], [14, 13, 9, 11], [14, 15, 9, 13, "Float32Array"], [14, 27, 9, 25], [14, 28, 9, 26, "of"], [14, 30, 9, 28], [14, 31, 9, 29], [14, 34, 9, 32], [14, 36, 9, 34], [14, 39, 9, 37], [14, 41, 9, 39], [14, 44, 9, 42], [14, 46, 9, 44], [14, 47, 9, 45], [14, 48, 9, 46], [15, 4, 10, 2, "antiquewhite"], [15, 16, 10, 14], [15, 18, 10, 16, "Float32Array"], [15, 30, 10, 28], [15, 31, 10, 29, "of"], [15, 33, 10, 31], [15, 34, 10, 32], [15, 37, 10, 35], [15, 39, 10, 37], [15, 42, 10, 40], [15, 44, 10, 42], [15, 47, 10, 45], [15, 49, 10, 47], [15, 50, 10, 48], [15, 51, 10, 49], [16, 4, 11, 2, "aqua"], [16, 8, 11, 6], [16, 10, 11, 8, "Float32Array"], [16, 22, 11, 20], [16, 23, 11, 21, "of"], [16, 25, 11, 23], [16, 26, 11, 24], [16, 27, 11, 25], [16, 29, 11, 27], [16, 32, 11, 30], [16, 34, 11, 32], [16, 37, 11, 35], [16, 39, 11, 37], [16, 40, 11, 38], [16, 41, 11, 39], [17, 4, 12, 2, "aquamarine"], [17, 14, 12, 12], [17, 16, 12, 14, "Float32Array"], [17, 28, 12, 26], [17, 29, 12, 27, "of"], [17, 31, 12, 29], [17, 32, 12, 30], [17, 35, 12, 33], [17, 37, 12, 35], [17, 40, 12, 38], [17, 42, 12, 40], [17, 45, 12, 43], [17, 47, 12, 45], [17, 48, 12, 46], [17, 49, 12, 47], [18, 4, 13, 2, "azure"], [18, 9, 13, 7], [18, 11, 13, 9, "Float32Array"], [18, 23, 13, 21], [18, 24, 13, 22, "of"], [18, 26, 13, 24], [18, 27, 13, 25], [18, 30, 13, 28], [18, 32, 13, 30], [18, 35, 13, 33], [18, 37, 13, 35], [18, 40, 13, 38], [18, 42, 13, 40], [18, 43, 13, 41], [18, 44, 13, 42], [19, 4, 14, 2, "beige"], [19, 9, 14, 7], [19, 11, 14, 9, "Float32Array"], [19, 23, 14, 21], [19, 24, 14, 22, "of"], [19, 26, 14, 24], [19, 27, 14, 25], [19, 30, 14, 28], [19, 32, 14, 30], [19, 35, 14, 33], [19, 37, 14, 35], [19, 40, 14, 38], [19, 42, 14, 40], [19, 43, 14, 41], [19, 44, 14, 42], [20, 4, 15, 2, "bisque"], [20, 10, 15, 8], [20, 12, 15, 10, "Float32Array"], [20, 24, 15, 22], [20, 25, 15, 23, "of"], [20, 27, 15, 25], [20, 28, 15, 26], [20, 31, 15, 29], [20, 33, 15, 31], [20, 36, 15, 34], [20, 38, 15, 36], [20, 41, 15, 39], [20, 43, 15, 41], [20, 44, 15, 42], [20, 45, 15, 43], [21, 4, 16, 2, "black"], [21, 9, 16, 7], [21, 11, 16, 9, "Float32Array"], [21, 23, 16, 21], [21, 24, 16, 22, "of"], [21, 26, 16, 24], [21, 27, 16, 25], [21, 28, 16, 26], [21, 30, 16, 28], [21, 31, 16, 29], [21, 33, 16, 31], [21, 34, 16, 32], [21, 36, 16, 34], [21, 37, 16, 35], [21, 38, 16, 36], [22, 4, 17, 2, "blanche<PERSON><PERSON>"], [22, 18, 17, 16], [22, 20, 17, 18, "Float32Array"], [22, 32, 17, 30], [22, 33, 17, 31, "of"], [22, 35, 17, 33], [22, 36, 17, 34], [22, 39, 17, 37], [22, 41, 17, 39], [22, 44, 17, 42], [22, 46, 17, 44], [22, 49, 17, 47], [22, 51, 17, 49], [22, 52, 17, 50], [22, 53, 17, 51], [23, 4, 18, 2, "blue"], [23, 8, 18, 6], [23, 10, 18, 8, "Float32Array"], [23, 22, 18, 20], [23, 23, 18, 21, "of"], [23, 25, 18, 23], [23, 26, 18, 24], [23, 27, 18, 25], [23, 29, 18, 27], [23, 30, 18, 28], [23, 32, 18, 30], [23, 35, 18, 33], [23, 37, 18, 35], [23, 38, 18, 36], [23, 39, 18, 37], [24, 4, 19, 2, "blueviolet"], [24, 14, 19, 12], [24, 16, 19, 14, "Float32Array"], [24, 28, 19, 26], [24, 29, 19, 27, "of"], [24, 31, 19, 29], [24, 32, 19, 30], [24, 35, 19, 33], [24, 37, 19, 35], [24, 39, 19, 37], [24, 41, 19, 39], [24, 44, 19, 42], [24, 46, 19, 44], [24, 47, 19, 45], [24, 48, 19, 46], [25, 4, 20, 2, "brown"], [25, 9, 20, 7], [25, 11, 20, 9, "Float32Array"], [25, 23, 20, 21], [25, 24, 20, 22, "of"], [25, 26, 20, 24], [25, 27, 20, 25], [25, 30, 20, 28], [25, 32, 20, 30], [25, 34, 20, 32], [25, 36, 20, 34], [25, 38, 20, 36], [25, 40, 20, 38], [25, 41, 20, 39], [25, 42, 20, 40], [26, 4, 21, 2, "burlywood"], [26, 13, 21, 11], [26, 15, 21, 13, "Float32Array"], [26, 27, 21, 25], [26, 28, 21, 26, "of"], [26, 30, 21, 28], [26, 31, 21, 29], [26, 34, 21, 32], [26, 36, 21, 34], [26, 39, 21, 37], [26, 41, 21, 39], [26, 44, 21, 42], [26, 46, 21, 44], [26, 47, 21, 45], [26, 48, 21, 46], [27, 4, 22, 2, "cadetblue"], [27, 13, 22, 11], [27, 15, 22, 13, "Float32Array"], [27, 27, 22, 25], [27, 28, 22, 26, "of"], [27, 30, 22, 28], [27, 31, 22, 29], [27, 33, 22, 31], [27, 35, 22, 33], [27, 38, 22, 36], [27, 40, 22, 38], [27, 43, 22, 41], [27, 45, 22, 43], [27, 46, 22, 44], [27, 47, 22, 45], [28, 4, 23, 2, "chartreuse"], [28, 14, 23, 12], [28, 16, 23, 14, "Float32Array"], [28, 28, 23, 26], [28, 29, 23, 27, "of"], [28, 31, 23, 29], [28, 32, 23, 30], [28, 35, 23, 33], [28, 37, 23, 35], [28, 40, 23, 38], [28, 42, 23, 40], [28, 43, 23, 41], [28, 45, 23, 43], [28, 46, 23, 44], [28, 47, 23, 45], [29, 4, 24, 2, "chocolate"], [29, 13, 24, 11], [29, 15, 24, 13, "Float32Array"], [29, 27, 24, 25], [29, 28, 24, 26, "of"], [29, 30, 24, 28], [29, 31, 24, 29], [29, 34, 24, 32], [29, 36, 24, 34], [29, 39, 24, 37], [29, 41, 24, 39], [29, 43, 24, 41], [29, 45, 24, 43], [29, 46, 24, 44], [29, 47, 24, 45], [30, 4, 25, 2, "coral"], [30, 9, 25, 7], [30, 11, 25, 9, "Float32Array"], [30, 23, 25, 21], [30, 24, 25, 22, "of"], [30, 26, 25, 24], [30, 27, 25, 25], [30, 30, 25, 28], [30, 32, 25, 30], [30, 35, 25, 33], [30, 37, 25, 35], [30, 39, 25, 37], [30, 41, 25, 39], [30, 42, 25, 40], [30, 43, 25, 41], [31, 4, 26, 2, "cornflowerblue"], [31, 18, 26, 16], [31, 20, 26, 18, "Float32Array"], [31, 32, 26, 30], [31, 33, 26, 31, "of"], [31, 35, 26, 33], [31, 36, 26, 34], [31, 39, 26, 37], [31, 41, 26, 39], [31, 44, 26, 42], [31, 46, 26, 44], [31, 49, 26, 47], [31, 51, 26, 49], [31, 52, 26, 50], [31, 53, 26, 51], [32, 4, 27, 2, "cornsilk"], [32, 12, 27, 10], [32, 14, 27, 12, "Float32Array"], [32, 26, 27, 24], [32, 27, 27, 25, "of"], [32, 29, 27, 27], [32, 30, 27, 28], [32, 33, 27, 31], [32, 35, 27, 33], [32, 38, 27, 36], [32, 40, 27, 38], [32, 43, 27, 41], [32, 45, 27, 43], [32, 46, 27, 44], [32, 47, 27, 45], [33, 4, 28, 2, "crimson"], [33, 11, 28, 9], [33, 13, 28, 11, "Float32Array"], [33, 25, 28, 23], [33, 26, 28, 24, "of"], [33, 28, 28, 26], [33, 29, 28, 27], [33, 32, 28, 30], [33, 34, 28, 32], [33, 36, 28, 34], [33, 38, 28, 36], [33, 40, 28, 38], [33, 42, 28, 40], [33, 43, 28, 41], [33, 44, 28, 42], [34, 4, 29, 2, "cyan"], [34, 8, 29, 6], [34, 10, 29, 8, "Float32Array"], [34, 22, 29, 20], [34, 23, 29, 21, "of"], [34, 25, 29, 23], [34, 26, 29, 24], [34, 27, 29, 25], [34, 29, 29, 27], [34, 32, 29, 30], [34, 34, 29, 32], [34, 37, 29, 35], [34, 39, 29, 37], [34, 40, 29, 38], [34, 41, 29, 39], [35, 4, 30, 2, "darkblue"], [35, 12, 30, 10], [35, 14, 30, 12, "Float32Array"], [35, 26, 30, 24], [35, 27, 30, 25, "of"], [35, 29, 30, 27], [35, 30, 30, 28], [35, 31, 30, 29], [35, 33, 30, 31], [35, 34, 30, 32], [35, 36, 30, 34], [35, 39, 30, 37], [35, 41, 30, 39], [35, 42, 30, 40], [35, 43, 30, 41], [36, 4, 31, 2, "dark<PERSON>an"], [36, 12, 31, 10], [36, 14, 31, 12, "Float32Array"], [36, 26, 31, 24], [36, 27, 31, 25, "of"], [36, 29, 31, 27], [36, 30, 31, 28], [36, 31, 31, 29], [36, 33, 31, 31], [36, 36, 31, 34], [36, 38, 31, 36], [36, 41, 31, 39], [36, 43, 31, 41], [36, 44, 31, 42], [36, 45, 31, 43], [37, 4, 32, 2, "darkgoldenrod"], [37, 17, 32, 15], [37, 19, 32, 17, "Float32Array"], [37, 31, 32, 29], [37, 32, 32, 30, "of"], [37, 34, 32, 32], [37, 35, 32, 33], [37, 38, 32, 36], [37, 40, 32, 38], [37, 43, 32, 41], [37, 45, 32, 43], [37, 47, 32, 45], [37, 49, 32, 47], [37, 50, 32, 48], [37, 51, 32, 49], [38, 4, 33, 2, "darkgray"], [38, 12, 33, 10], [38, 14, 33, 12, "Float32Array"], [38, 26, 33, 24], [38, 27, 33, 25, "of"], [38, 29, 33, 27], [38, 30, 33, 28], [38, 33, 33, 31], [38, 35, 33, 33], [38, 38, 33, 36], [38, 40, 33, 38], [38, 43, 33, 41], [38, 45, 33, 43], [38, 46, 33, 44], [38, 47, 33, 45], [39, 4, 34, 2, "darkgreen"], [39, 13, 34, 11], [39, 15, 34, 13, "Float32Array"], [39, 27, 34, 25], [39, 28, 34, 26, "of"], [39, 30, 34, 28], [39, 31, 34, 29], [39, 32, 34, 30], [39, 34, 34, 32], [39, 37, 34, 35], [39, 39, 34, 37], [39, 40, 34, 38], [39, 42, 34, 40], [39, 43, 34, 41], [39, 44, 34, 42], [40, 4, 35, 2, "<PERSON><PERSON>rey"], [40, 12, 35, 10], [40, 14, 35, 12, "Float32Array"], [40, 26, 35, 24], [40, 27, 35, 25, "of"], [40, 29, 35, 27], [40, 30, 35, 28], [40, 33, 35, 31], [40, 35, 35, 33], [40, 38, 35, 36], [40, 40, 35, 38], [40, 43, 35, 41], [40, 45, 35, 43], [40, 46, 35, 44], [40, 47, 35, 45], [41, 4, 36, 2, "<PERSON><PERSON><PERSON>"], [41, 13, 36, 11], [41, 15, 36, 13, "Float32Array"], [41, 27, 36, 25], [41, 28, 36, 26, "of"], [41, 30, 36, 28], [41, 31, 36, 29], [41, 34, 36, 32], [41, 36, 36, 34], [41, 39, 36, 37], [41, 41, 36, 39], [41, 44, 36, 42], [41, 46, 36, 44], [41, 47, 36, 45], [41, 48, 36, 46], [42, 4, 37, 2, "darkmagenta"], [42, 15, 37, 13], [42, 17, 37, 15, "Float32Array"], [42, 29, 37, 27], [42, 30, 37, 28, "of"], [42, 32, 37, 30], [42, 33, 37, 31], [42, 36, 37, 34], [42, 38, 37, 36], [42, 39, 37, 37], [42, 41, 37, 39], [42, 44, 37, 42], [42, 46, 37, 44], [42, 47, 37, 45], [42, 48, 37, 46], [43, 4, 38, 2, "darkolivegreen"], [43, 18, 38, 16], [43, 20, 38, 18, "Float32Array"], [43, 32, 38, 30], [43, 33, 38, 31, "of"], [43, 35, 38, 33], [43, 36, 38, 34], [43, 38, 38, 36], [43, 40, 38, 38], [43, 43, 38, 41], [43, 45, 38, 43], [43, 47, 38, 45], [43, 49, 38, 47], [43, 50, 38, 48], [43, 51, 38, 49], [44, 4, 39, 2, "darkorange"], [44, 14, 39, 12], [44, 16, 39, 14, "Float32Array"], [44, 28, 39, 26], [44, 29, 39, 27, "of"], [44, 31, 39, 29], [44, 32, 39, 30], [44, 35, 39, 33], [44, 37, 39, 35], [44, 40, 39, 38], [44, 42, 39, 40], [44, 43, 39, 41], [44, 45, 39, 43], [44, 46, 39, 44], [44, 47, 39, 45], [45, 4, 40, 2, "darkorchid"], [45, 14, 40, 12], [45, 16, 40, 14, "Float32Array"], [45, 28, 40, 26], [45, 29, 40, 27, "of"], [45, 31, 40, 29], [45, 32, 40, 30], [45, 35, 40, 33], [45, 37, 40, 35], [45, 39, 40, 37], [45, 41, 40, 39], [45, 44, 40, 42], [45, 46, 40, 44], [45, 47, 40, 45], [45, 48, 40, 46], [46, 4, 41, 2, "darkred"], [46, 11, 41, 9], [46, 13, 41, 11, "Float32Array"], [46, 25, 41, 23], [46, 26, 41, 24, "of"], [46, 28, 41, 26], [46, 29, 41, 27], [46, 32, 41, 30], [46, 34, 41, 32], [46, 35, 41, 33], [46, 37, 41, 35], [46, 38, 41, 36], [46, 40, 41, 38], [46, 41, 41, 39], [46, 42, 41, 40], [47, 4, 42, 2, "<PERSON><PERSON><PERSON>"], [47, 14, 42, 12], [47, 16, 42, 14, "Float32Array"], [47, 28, 42, 26], [47, 29, 42, 27, "of"], [47, 31, 42, 29], [47, 32, 42, 30], [47, 35, 42, 33], [47, 37, 42, 35], [47, 40, 42, 38], [47, 42, 42, 40], [47, 45, 42, 43], [47, 47, 42, 45], [47, 48, 42, 46], [47, 49, 42, 47], [48, 4, 43, 2, "darkseagreen"], [48, 16, 43, 14], [48, 18, 43, 16, "Float32Array"], [48, 30, 43, 28], [48, 31, 43, 29, "of"], [48, 33, 43, 31], [48, 34, 43, 32], [48, 37, 43, 35], [48, 39, 43, 37], [48, 42, 43, 40], [48, 44, 43, 42], [48, 47, 43, 45], [48, 49, 43, 47], [48, 50, 43, 48], [48, 51, 43, 49], [49, 4, 44, 2, "darkslateblue"], [49, 17, 44, 15], [49, 19, 44, 17, "Float32Array"], [49, 31, 44, 29], [49, 32, 44, 30, "of"], [49, 34, 44, 32], [49, 35, 44, 33], [49, 37, 44, 35], [49, 39, 44, 37], [49, 41, 44, 39], [49, 43, 44, 41], [49, 46, 44, 44], [49, 48, 44, 46], [49, 49, 44, 47], [49, 50, 44, 48], [50, 4, 45, 2, "darkslategray"], [50, 17, 45, 15], [50, 19, 45, 17, "Float32Array"], [50, 31, 45, 29], [50, 32, 45, 30, "of"], [50, 34, 45, 32], [50, 35, 45, 33], [50, 37, 45, 35], [50, 39, 45, 37], [50, 41, 45, 39], [50, 43, 45, 41], [50, 45, 45, 43], [50, 47, 45, 45], [50, 48, 45, 46], [50, 49, 45, 47], [51, 4, 46, 2, "darkslateg<PERSON>"], [51, 17, 46, 15], [51, 19, 46, 17, "Float32Array"], [51, 31, 46, 29], [51, 32, 46, 30, "of"], [51, 34, 46, 32], [51, 35, 46, 33], [51, 37, 46, 35], [51, 39, 46, 37], [51, 41, 46, 39], [51, 43, 46, 41], [51, 45, 46, 43], [51, 47, 46, 45], [51, 48, 46, 46], [51, 49, 46, 47], [52, 4, 47, 2, "darkturquoise"], [52, 17, 47, 15], [52, 19, 47, 17, "Float32Array"], [52, 31, 47, 29], [52, 32, 47, 30, "of"], [52, 34, 47, 32], [52, 35, 47, 33], [52, 36, 47, 34], [52, 38, 47, 36], [52, 41, 47, 39], [52, 43, 47, 41], [52, 46, 47, 44], [52, 48, 47, 46], [52, 49, 47, 47], [52, 50, 47, 48], [53, 4, 48, 2, "darkviolet"], [53, 14, 48, 12], [53, 16, 48, 14, "Float32Array"], [53, 28, 48, 26], [53, 29, 48, 27, "of"], [53, 31, 48, 29], [53, 32, 48, 30], [53, 35, 48, 33], [53, 37, 48, 35], [53, 38, 48, 36], [53, 40, 48, 38], [53, 43, 48, 41], [53, 45, 48, 43], [53, 46, 48, 44], [53, 47, 48, 45], [54, 4, 49, 2, "deeppink"], [54, 12, 49, 10], [54, 14, 49, 12, "Float32Array"], [54, 26, 49, 24], [54, 27, 49, 25, "of"], [54, 29, 49, 27], [54, 30, 49, 28], [54, 33, 49, 31], [54, 35, 49, 33], [54, 37, 49, 35], [54, 39, 49, 37], [54, 42, 49, 40], [54, 44, 49, 42], [54, 45, 49, 43], [54, 46, 49, 44], [55, 4, 50, 2, "deepskyblue"], [55, 15, 50, 13], [55, 17, 50, 15, "Float32Array"], [55, 29, 50, 27], [55, 30, 50, 28, "of"], [55, 32, 50, 30], [55, 33, 50, 31], [55, 34, 50, 32], [55, 36, 50, 34], [55, 39, 50, 37], [55, 41, 50, 39], [55, 44, 50, 42], [55, 46, 50, 44], [55, 47, 50, 45], [55, 48, 50, 46], [56, 4, 51, 2, "dimgray"], [56, 11, 51, 9], [56, 13, 51, 11, "Float32Array"], [56, 25, 51, 23], [56, 26, 51, 24, "of"], [56, 28, 51, 26], [56, 29, 51, 27], [56, 32, 51, 30], [56, 34, 51, 32], [56, 37, 51, 35], [56, 39, 51, 37], [56, 42, 51, 40], [56, 44, 51, 42], [56, 45, 51, 43], [56, 46, 51, 44], [57, 4, 52, 2, "<PERSON><PERSON><PERSON>"], [57, 11, 52, 9], [57, 13, 52, 11, "Float32Array"], [57, 25, 52, 23], [57, 26, 52, 24, "of"], [57, 28, 52, 26], [57, 29, 52, 27], [57, 32, 52, 30], [57, 34, 52, 32], [57, 37, 52, 35], [57, 39, 52, 37], [57, 42, 52, 40], [57, 44, 52, 42], [57, 45, 52, 43], [57, 46, 52, 44], [58, 4, 53, 2, "dodgerblue"], [58, 14, 53, 12], [58, 16, 53, 14, "Float32Array"], [58, 28, 53, 26], [58, 29, 53, 27, "of"], [58, 31, 53, 29], [58, 32, 53, 30], [58, 34, 53, 32], [58, 36, 53, 34], [58, 39, 53, 37], [58, 41, 53, 39], [58, 44, 53, 42], [58, 46, 53, 44], [58, 47, 53, 45], [58, 48, 53, 46], [59, 4, 54, 2, "firebrick"], [59, 13, 54, 11], [59, 15, 54, 13, "Float32Array"], [59, 27, 54, 25], [59, 28, 54, 26, "of"], [59, 30, 54, 28], [59, 31, 54, 29], [59, 34, 54, 32], [59, 36, 54, 34], [59, 38, 54, 36], [59, 40, 54, 38], [59, 42, 54, 40], [59, 44, 54, 42], [59, 45, 54, 43], [59, 46, 54, 44], [60, 4, 55, 2, "<PERSON><PERSON><PERSON><PERSON>"], [60, 15, 55, 13], [60, 17, 55, 15, "Float32Array"], [60, 29, 55, 27], [60, 30, 55, 28, "of"], [60, 32, 55, 30], [60, 33, 55, 31], [60, 36, 55, 34], [60, 38, 55, 36], [60, 41, 55, 39], [60, 43, 55, 41], [60, 46, 55, 44], [60, 48, 55, 46], [60, 49, 55, 47], [60, 50, 55, 48], [61, 4, 56, 2, "forestgreen"], [61, 15, 56, 13], [61, 17, 56, 15, "Float32Array"], [61, 29, 56, 27], [61, 30, 56, 28, "of"], [61, 32, 56, 30], [61, 33, 56, 31], [61, 35, 56, 33], [61, 37, 56, 35], [61, 40, 56, 38], [61, 42, 56, 40], [61, 44, 56, 42], [61, 46, 56, 44], [61, 47, 56, 45], [61, 48, 56, 46], [62, 4, 57, 2, "fuchsia"], [62, 11, 57, 9], [62, 13, 57, 11, "Float32Array"], [62, 25, 57, 23], [62, 26, 57, 24, "of"], [62, 28, 57, 26], [62, 29, 57, 27], [62, 32, 57, 30], [62, 34, 57, 32], [62, 35, 57, 33], [62, 37, 57, 35], [62, 40, 57, 38], [62, 42, 57, 40], [62, 43, 57, 41], [62, 44, 57, 42], [63, 4, 58, 2, "gainsboro"], [63, 13, 58, 11], [63, 15, 58, 13, "Float32Array"], [63, 27, 58, 25], [63, 28, 58, 26, "of"], [63, 30, 58, 28], [63, 31, 58, 29], [63, 34, 58, 32], [63, 36, 58, 34], [63, 39, 58, 37], [63, 41, 58, 39], [63, 44, 58, 42], [63, 46, 58, 44], [63, 47, 58, 45], [63, 48, 58, 46], [64, 4, 59, 2, "ghostwhite"], [64, 14, 59, 12], [64, 16, 59, 14, "Float32Array"], [64, 28, 59, 26], [64, 29, 59, 27, "of"], [64, 31, 59, 29], [64, 32, 59, 30], [64, 35, 59, 33], [64, 37, 59, 35], [64, 40, 59, 38], [64, 42, 59, 40], [64, 45, 59, 43], [64, 47, 59, 45], [64, 48, 59, 46], [64, 49, 59, 47], [65, 4, 60, 2, "gold"], [65, 8, 60, 6], [65, 10, 60, 8, "Float32Array"], [65, 22, 60, 20], [65, 23, 60, 21, "of"], [65, 25, 60, 23], [65, 26, 60, 24], [65, 29, 60, 27], [65, 31, 60, 29], [65, 34, 60, 32], [65, 36, 60, 34], [65, 37, 60, 35], [65, 39, 60, 37], [65, 40, 60, 38], [65, 41, 60, 39], [66, 4, 61, 2, "goldenrod"], [66, 13, 61, 11], [66, 15, 61, 13, "Float32Array"], [66, 27, 61, 25], [66, 28, 61, 26, "of"], [66, 30, 61, 28], [66, 31, 61, 29], [66, 34, 61, 32], [66, 36, 61, 34], [66, 39, 61, 37], [66, 41, 61, 39], [66, 43, 61, 41], [66, 45, 61, 43], [66, 46, 61, 44], [66, 47, 61, 45], [67, 4, 62, 2, "gray"], [67, 8, 62, 6], [67, 10, 62, 8, "Float32Array"], [67, 22, 62, 20], [67, 23, 62, 21, "of"], [67, 25, 62, 23], [67, 26, 62, 24], [67, 29, 62, 27], [67, 31, 62, 29], [67, 34, 62, 32], [67, 36, 62, 34], [67, 39, 62, 37], [67, 41, 62, 39], [67, 42, 62, 40], [67, 43, 62, 41], [68, 4, 63, 2, "green"], [68, 9, 63, 7], [68, 11, 63, 9, "Float32Array"], [68, 23, 63, 21], [68, 24, 63, 22, "of"], [68, 26, 63, 24], [68, 27, 63, 25], [68, 28, 63, 26], [68, 30, 63, 28], [68, 33, 63, 31], [68, 35, 63, 33], [68, 36, 63, 34], [68, 38, 63, 36], [68, 39, 63, 37], [68, 40, 63, 38], [69, 4, 64, 2, "greenyellow"], [69, 15, 64, 13], [69, 17, 64, 15, "Float32Array"], [69, 29, 64, 27], [69, 30, 64, 28, "of"], [69, 32, 64, 30], [69, 33, 64, 31], [69, 36, 64, 34], [69, 38, 64, 36], [69, 41, 64, 39], [69, 43, 64, 41], [69, 45, 64, 43], [69, 47, 64, 45], [69, 48, 64, 46], [69, 49, 64, 47], [70, 4, 65, 2, "grey"], [70, 8, 65, 6], [70, 10, 65, 8, "Float32Array"], [70, 22, 65, 20], [70, 23, 65, 21, "of"], [70, 25, 65, 23], [70, 26, 65, 24], [70, 29, 65, 27], [70, 31, 65, 29], [70, 34, 65, 32], [70, 36, 65, 34], [70, 39, 65, 37], [70, 41, 65, 39], [70, 42, 65, 40], [70, 43, 65, 41], [71, 4, 66, 2, "honeydew"], [71, 12, 66, 10], [71, 14, 66, 12, "Float32Array"], [71, 26, 66, 24], [71, 27, 66, 25, "of"], [71, 29, 66, 27], [71, 30, 66, 28], [71, 33, 66, 31], [71, 35, 66, 33], [71, 38, 66, 36], [71, 40, 66, 38], [71, 43, 66, 41], [71, 45, 66, 43], [71, 46, 66, 44], [71, 47, 66, 45], [72, 4, 67, 2, "hotpink"], [72, 11, 67, 9], [72, 13, 67, 11, "Float32Array"], [72, 25, 67, 23], [72, 26, 67, 24, "of"], [72, 28, 67, 26], [72, 29, 67, 27], [72, 32, 67, 30], [72, 34, 67, 32], [72, 37, 67, 35], [72, 39, 67, 37], [72, 42, 67, 40], [72, 44, 67, 42], [72, 45, 67, 43], [72, 46, 67, 44], [73, 4, 68, 2, "indianred"], [73, 13, 68, 11], [73, 15, 68, 13, "Float32Array"], [73, 27, 68, 25], [73, 28, 68, 26, "of"], [73, 30, 68, 28], [73, 31, 68, 29], [73, 34, 68, 32], [73, 36, 68, 34], [73, 38, 68, 36], [73, 40, 68, 38], [73, 42, 68, 40], [73, 44, 68, 42], [73, 45, 68, 43], [73, 46, 68, 44], [74, 4, 69, 2, "indigo"], [74, 10, 69, 8], [74, 12, 69, 10, "Float32Array"], [74, 24, 69, 22], [74, 25, 69, 23, "of"], [74, 27, 69, 25], [74, 28, 69, 26], [74, 30, 69, 28], [74, 32, 69, 30], [74, 33, 69, 31], [74, 35, 69, 33], [74, 38, 69, 36], [74, 40, 69, 38], [74, 41, 69, 39], [74, 42, 69, 40], [75, 4, 70, 2, "ivory"], [75, 9, 70, 7], [75, 11, 70, 9, "Float32Array"], [75, 23, 70, 21], [75, 24, 70, 22, "of"], [75, 26, 70, 24], [75, 27, 70, 25], [75, 30, 70, 28], [75, 32, 70, 30], [75, 35, 70, 33], [75, 37, 70, 35], [75, 40, 70, 38], [75, 42, 70, 40], [75, 43, 70, 41], [75, 44, 70, 42], [76, 4, 71, 2, "khaki"], [76, 9, 71, 7], [76, 11, 71, 9, "Float32Array"], [76, 23, 71, 21], [76, 24, 71, 22, "of"], [76, 26, 71, 24], [76, 27, 71, 25], [76, 30, 71, 28], [76, 32, 71, 30], [76, 35, 71, 33], [76, 37, 71, 35], [76, 40, 71, 38], [76, 42, 71, 40], [76, 43, 71, 41], [76, 44, 71, 42], [77, 4, 72, 2, "lavender"], [77, 12, 72, 10], [77, 14, 72, 12, "Float32Array"], [77, 26, 72, 24], [77, 27, 72, 25, "of"], [77, 29, 72, 27], [77, 30, 72, 28], [77, 33, 72, 31], [77, 35, 72, 33], [77, 38, 72, 36], [77, 40, 72, 38], [77, 43, 72, 41], [77, 45, 72, 43], [77, 46, 72, 44], [77, 47, 72, 45], [78, 4, 73, 2, "lavenderblush"], [78, 17, 73, 15], [78, 19, 73, 17, "Float32Array"], [78, 31, 73, 29], [78, 32, 73, 30, "of"], [78, 34, 73, 32], [78, 35, 73, 33], [78, 38, 73, 36], [78, 40, 73, 38], [78, 43, 73, 41], [78, 45, 73, 43], [78, 48, 73, 46], [78, 50, 73, 48], [78, 51, 73, 49], [78, 52, 73, 50], [79, 4, 74, 2, "lawngreen"], [79, 13, 74, 11], [79, 15, 74, 13, "Float32Array"], [79, 27, 74, 25], [79, 28, 74, 26, "of"], [79, 30, 74, 28], [79, 31, 74, 29], [79, 34, 74, 32], [79, 36, 74, 34], [79, 39, 74, 37], [79, 41, 74, 39], [79, 42, 74, 40], [79, 44, 74, 42], [79, 45, 74, 43], [79, 46, 74, 44], [80, 4, 75, 2, "lemon<PERSON>ffon"], [80, 16, 75, 14], [80, 18, 75, 16, "Float32Array"], [80, 30, 75, 28], [80, 31, 75, 29, "of"], [80, 33, 75, 31], [80, 34, 75, 32], [80, 37, 75, 35], [80, 39, 75, 37], [80, 42, 75, 40], [80, 44, 75, 42], [80, 47, 75, 45], [80, 49, 75, 47], [80, 50, 75, 48], [80, 51, 75, 49], [81, 4, 76, 2, "lightblue"], [81, 13, 76, 11], [81, 15, 76, 13, "Float32Array"], [81, 27, 76, 25], [81, 28, 76, 26, "of"], [81, 30, 76, 28], [81, 31, 76, 29], [81, 34, 76, 32], [81, 36, 76, 34], [81, 39, 76, 37], [81, 41, 76, 39], [81, 44, 76, 42], [81, 46, 76, 44], [81, 47, 76, 45], [81, 48, 76, 46], [82, 4, 77, 2, "lightcoral"], [82, 14, 77, 12], [82, 16, 77, 14, "Float32Array"], [82, 28, 77, 26], [82, 29, 77, 27, "of"], [82, 31, 77, 29], [82, 32, 77, 30], [82, 35, 77, 33], [82, 37, 77, 35], [82, 40, 77, 38], [82, 42, 77, 40], [82, 45, 77, 43], [82, 47, 77, 45], [82, 48, 77, 46], [82, 49, 77, 47], [83, 4, 78, 2, "lightcyan"], [83, 13, 78, 11], [83, 15, 78, 13, "Float32Array"], [83, 27, 78, 25], [83, 28, 78, 26, "of"], [83, 30, 78, 28], [83, 31, 78, 29], [83, 34, 78, 32], [83, 36, 78, 34], [83, 39, 78, 37], [83, 41, 78, 39], [83, 44, 78, 42], [83, 46, 78, 44], [83, 47, 78, 45], [83, 48, 78, 46], [84, 4, 79, 2, "lightgoldenrodyellow"], [84, 24, 79, 22], [84, 26, 79, 24, "Float32Array"], [84, 38, 79, 36], [84, 39, 79, 37, "of"], [84, 41, 79, 39], [84, 42, 79, 40], [84, 45, 79, 43], [84, 47, 79, 45], [84, 50, 79, 48], [84, 52, 79, 50], [84, 55, 79, 53], [84, 57, 79, 55], [84, 58, 79, 56], [84, 59, 79, 57], [85, 4, 80, 2, "lightgray"], [85, 13, 80, 11], [85, 15, 80, 13, "Float32Array"], [85, 27, 80, 25], [85, 28, 80, 26, "of"], [85, 30, 80, 28], [85, 31, 80, 29], [85, 34, 80, 32], [85, 36, 80, 34], [85, 39, 80, 37], [85, 41, 80, 39], [85, 44, 80, 42], [85, 46, 80, 44], [85, 47, 80, 45], [85, 48, 80, 46], [86, 4, 81, 2, "lightgreen"], [86, 14, 81, 12], [86, 16, 81, 14, "Float32Array"], [86, 28, 81, 26], [86, 29, 81, 27, "of"], [86, 31, 81, 29], [86, 32, 81, 30], [86, 35, 81, 33], [86, 37, 81, 35], [86, 40, 81, 38], [86, 42, 81, 40], [86, 45, 81, 43], [86, 47, 81, 45], [86, 48, 81, 46], [86, 49, 81, 47], [87, 4, 82, 2, "<PERSON><PERSON>rey"], [87, 13, 82, 11], [87, 15, 82, 13, "Float32Array"], [87, 27, 82, 25], [87, 28, 82, 26, "of"], [87, 30, 82, 28], [87, 31, 82, 29], [87, 34, 82, 32], [87, 36, 82, 34], [87, 39, 82, 37], [87, 41, 82, 39], [87, 44, 82, 42], [87, 46, 82, 44], [87, 47, 82, 45], [87, 48, 82, 46], [88, 4, 83, 2, "lightpink"], [88, 13, 83, 11], [88, 15, 83, 13, "Float32Array"], [88, 27, 83, 25], [88, 28, 83, 26, "of"], [88, 30, 83, 28], [88, 31, 83, 29], [88, 34, 83, 32], [88, 36, 83, 34], [88, 39, 83, 37], [88, 41, 83, 39], [88, 44, 83, 42], [88, 46, 83, 44], [88, 47, 83, 45], [88, 48, 83, 46], [89, 4, 84, 2, "<PERSON><PERSON><PERSON>"], [89, 15, 84, 13], [89, 17, 84, 15, "Float32Array"], [89, 29, 84, 27], [89, 30, 84, 28, "of"], [89, 32, 84, 30], [89, 33, 84, 31], [89, 36, 84, 34], [89, 38, 84, 36], [89, 41, 84, 39], [89, 43, 84, 41], [89, 46, 84, 44], [89, 48, 84, 46], [89, 49, 84, 47], [89, 50, 84, 48], [90, 4, 85, 2, "lightseagreen"], [90, 17, 85, 15], [90, 19, 85, 17, "Float32Array"], [90, 31, 85, 29], [90, 32, 85, 30, "of"], [90, 34, 85, 32], [90, 35, 85, 33], [90, 37, 85, 35], [90, 39, 85, 37], [90, 42, 85, 40], [90, 44, 85, 42], [90, 47, 85, 45], [90, 49, 85, 47], [90, 50, 85, 48], [90, 51, 85, 49], [91, 4, 86, 2, "lightskyblue"], [91, 16, 86, 14], [91, 18, 86, 16, "Float32Array"], [91, 30, 86, 28], [91, 31, 86, 29, "of"], [91, 33, 86, 31], [91, 34, 86, 32], [91, 37, 86, 35], [91, 39, 86, 37], [91, 42, 86, 40], [91, 44, 86, 42], [91, 47, 86, 45], [91, 49, 86, 47], [91, 50, 86, 48], [91, 51, 86, 49], [92, 4, 87, 2, "lightslategray"], [92, 18, 87, 16], [92, 20, 87, 18, "Float32Array"], [92, 32, 87, 30], [92, 33, 87, 31, "of"], [92, 35, 87, 33], [92, 36, 87, 34], [92, 39, 87, 37], [92, 41, 87, 39], [92, 44, 87, 42], [92, 46, 87, 44], [92, 49, 87, 47], [92, 51, 87, 49], [92, 52, 87, 50], [92, 53, 87, 51], [93, 4, 88, 2, "lightslategrey"], [93, 18, 88, 16], [93, 20, 88, 18, "Float32Array"], [93, 32, 88, 30], [93, 33, 88, 31, "of"], [93, 35, 88, 33], [93, 36, 88, 34], [93, 39, 88, 37], [93, 41, 88, 39], [93, 44, 88, 42], [93, 46, 88, 44], [93, 49, 88, 47], [93, 51, 88, 49], [93, 52, 88, 50], [93, 53, 88, 51], [94, 4, 89, 2, "lightsteelblue"], [94, 18, 89, 16], [94, 20, 89, 18, "Float32Array"], [94, 32, 89, 30], [94, 33, 89, 31, "of"], [94, 35, 89, 33], [94, 36, 89, 34], [94, 39, 89, 37], [94, 41, 89, 39], [94, 44, 89, 42], [94, 46, 89, 44], [94, 49, 89, 47], [94, 51, 89, 49], [94, 52, 89, 50], [94, 53, 89, 51], [95, 4, 90, 2, "lightyellow"], [95, 15, 90, 13], [95, 17, 90, 15, "Float32Array"], [95, 29, 90, 27], [95, 30, 90, 28, "of"], [95, 32, 90, 30], [95, 33, 90, 31], [95, 36, 90, 34], [95, 38, 90, 36], [95, 41, 90, 39], [95, 43, 90, 41], [95, 46, 90, 44], [95, 48, 90, 46], [95, 49, 90, 47], [95, 50, 90, 48], [96, 4, 91, 2, "lime"], [96, 8, 91, 6], [96, 10, 91, 8, "Float32Array"], [96, 22, 91, 20], [96, 23, 91, 21, "of"], [96, 25, 91, 23], [96, 26, 91, 24], [96, 27, 91, 25], [96, 29, 91, 27], [96, 32, 91, 30], [96, 34, 91, 32], [96, 35, 91, 33], [96, 37, 91, 35], [96, 38, 91, 36], [96, 39, 91, 37], [97, 4, 92, 2, "limegreen"], [97, 13, 92, 11], [97, 15, 92, 13, "Float32Array"], [97, 27, 92, 25], [97, 28, 92, 26, "of"], [97, 30, 92, 28], [97, 31, 92, 29], [97, 33, 92, 31], [97, 35, 92, 33], [97, 38, 92, 36], [97, 40, 92, 38], [97, 42, 92, 40], [97, 44, 92, 42], [97, 45, 92, 43], [97, 46, 92, 44], [98, 4, 93, 2, "linen"], [98, 9, 93, 7], [98, 11, 93, 9, "Float32Array"], [98, 23, 93, 21], [98, 24, 93, 22, "of"], [98, 26, 93, 24], [98, 27, 93, 25], [98, 30, 93, 28], [98, 32, 93, 30], [98, 35, 93, 33], [98, 37, 93, 35], [98, 40, 93, 38], [98, 42, 93, 40], [98, 43, 93, 41], [98, 44, 93, 42], [99, 4, 94, 2, "magenta"], [99, 11, 94, 9], [99, 13, 94, 11, "Float32Array"], [99, 25, 94, 23], [99, 26, 94, 24, "of"], [99, 28, 94, 26], [99, 29, 94, 27], [99, 32, 94, 30], [99, 34, 94, 32], [99, 35, 94, 33], [99, 37, 94, 35], [99, 40, 94, 38], [99, 42, 94, 40], [99, 43, 94, 41], [99, 44, 94, 42], [100, 4, 95, 2, "maroon"], [100, 10, 95, 8], [100, 12, 95, 10, "Float32Array"], [100, 24, 95, 22], [100, 25, 95, 23, "of"], [100, 27, 95, 25], [100, 28, 95, 26], [100, 31, 95, 29], [100, 33, 95, 31], [100, 34, 95, 32], [100, 36, 95, 34], [100, 37, 95, 35], [100, 39, 95, 37], [100, 40, 95, 38], [100, 41, 95, 39], [101, 4, 96, 2, "mediumaquamarine"], [101, 20, 96, 18], [101, 22, 96, 20, "Float32Array"], [101, 34, 96, 32], [101, 35, 96, 33, "of"], [101, 37, 96, 35], [101, 38, 96, 36], [101, 41, 96, 39], [101, 43, 96, 41], [101, 46, 96, 44], [101, 48, 96, 46], [101, 51, 96, 49], [101, 53, 96, 51], [101, 54, 96, 52], [101, 55, 96, 53], [102, 4, 97, 2, "mediumblue"], [102, 14, 97, 12], [102, 16, 97, 14, "Float32Array"], [102, 28, 97, 26], [102, 29, 97, 27, "of"], [102, 31, 97, 29], [102, 32, 97, 30], [102, 33, 97, 31], [102, 35, 97, 33], [102, 36, 97, 34], [102, 38, 97, 36], [102, 41, 97, 39], [102, 43, 97, 41], [102, 44, 97, 42], [102, 45, 97, 43], [103, 4, 98, 2, "mediumorchid"], [103, 16, 98, 14], [103, 18, 98, 16, "Float32Array"], [103, 30, 98, 28], [103, 31, 98, 29, "of"], [103, 33, 98, 31], [103, 34, 98, 32], [103, 37, 98, 35], [103, 39, 98, 37], [103, 41, 98, 39], [103, 43, 98, 41], [103, 46, 98, 44], [103, 48, 98, 46], [103, 49, 98, 47], [103, 50, 98, 48], [104, 4, 99, 2, "mediumpurple"], [104, 16, 99, 14], [104, 18, 99, 16, "Float32Array"], [104, 30, 99, 28], [104, 31, 99, 29, "of"], [104, 33, 99, 31], [104, 34, 99, 32], [104, 37, 99, 35], [104, 39, 99, 37], [104, 42, 99, 40], [104, 44, 99, 42], [104, 47, 99, 45], [104, 49, 99, 47], [104, 50, 99, 48], [104, 51, 99, 49], [105, 4, 100, 2, "mediumseagreen"], [105, 18, 100, 16], [105, 20, 100, 18, "Float32Array"], [105, 32, 100, 30], [105, 33, 100, 31, "of"], [105, 35, 100, 33], [105, 36, 100, 34], [105, 38, 100, 36], [105, 40, 100, 38], [105, 43, 100, 41], [105, 45, 100, 43], [105, 48, 100, 46], [105, 50, 100, 48], [105, 51, 100, 49], [105, 52, 100, 50], [106, 4, 101, 2, "mediumslateblue"], [106, 19, 101, 17], [106, 21, 101, 19, "Float32Array"], [106, 33, 101, 31], [106, 34, 101, 32, "of"], [106, 36, 101, 34], [106, 37, 101, 35], [106, 40, 101, 38], [106, 42, 101, 40], [106, 45, 101, 43], [106, 47, 101, 45], [106, 50, 101, 48], [106, 52, 101, 50], [106, 53, 101, 51], [106, 54, 101, 52], [107, 4, 102, 2, "mediumspringgreen"], [107, 21, 102, 19], [107, 23, 102, 21, "Float32Array"], [107, 35, 102, 33], [107, 36, 102, 34, "of"], [107, 38, 102, 36], [107, 39, 102, 37], [107, 40, 102, 38], [107, 42, 102, 40], [107, 45, 102, 43], [107, 47, 102, 45], [107, 50, 102, 48], [107, 52, 102, 50], [107, 53, 102, 51], [107, 54, 102, 52], [108, 4, 103, 2, "mediumturquoise"], [108, 19, 103, 17], [108, 21, 103, 19, "Float32Array"], [108, 33, 103, 31], [108, 34, 103, 32, "of"], [108, 36, 103, 34], [108, 37, 103, 35], [108, 39, 103, 37], [108, 41, 103, 39], [108, 44, 103, 42], [108, 46, 103, 44], [108, 49, 103, 47], [108, 51, 103, 49], [108, 52, 103, 50], [108, 53, 103, 51], [109, 4, 104, 2, "mediumvioletred"], [109, 19, 104, 17], [109, 21, 104, 19, "Float32Array"], [109, 33, 104, 31], [109, 34, 104, 32, "of"], [109, 36, 104, 34], [109, 37, 104, 35], [109, 40, 104, 38], [109, 42, 104, 40], [109, 44, 104, 42], [109, 46, 104, 44], [109, 49, 104, 47], [109, 51, 104, 49], [109, 52, 104, 50], [109, 53, 104, 51], [110, 4, 105, 2, "midnightblue"], [110, 16, 105, 14], [110, 18, 105, 16, "Float32Array"], [110, 30, 105, 28], [110, 31, 105, 29, "of"], [110, 33, 105, 31], [110, 34, 105, 32], [110, 36, 105, 34], [110, 38, 105, 36], [110, 40, 105, 38], [110, 42, 105, 40], [110, 45, 105, 43], [110, 47, 105, 45], [110, 48, 105, 46], [110, 49, 105, 47], [111, 4, 106, 2, "mintcream"], [111, 13, 106, 11], [111, 15, 106, 13, "Float32Array"], [111, 27, 106, 25], [111, 28, 106, 26, "of"], [111, 30, 106, 28], [111, 31, 106, 29], [111, 34, 106, 32], [111, 36, 106, 34], [111, 39, 106, 37], [111, 41, 106, 39], [111, 44, 106, 42], [111, 46, 106, 44], [111, 47, 106, 45], [111, 48, 106, 46], [112, 4, 107, 2, "mistyrose"], [112, 13, 107, 11], [112, 15, 107, 13, "Float32Array"], [112, 27, 107, 25], [112, 28, 107, 26, "of"], [112, 30, 107, 28], [112, 31, 107, 29], [112, 34, 107, 32], [112, 36, 107, 34], [112, 39, 107, 37], [112, 41, 107, 39], [112, 44, 107, 42], [112, 46, 107, 44], [112, 47, 107, 45], [112, 48, 107, 46], [113, 4, 108, 2, "moccasin"], [113, 12, 108, 10], [113, 14, 108, 12, "Float32Array"], [113, 26, 108, 24], [113, 27, 108, 25, "of"], [113, 29, 108, 27], [113, 30, 108, 28], [113, 33, 108, 31], [113, 35, 108, 33], [113, 38, 108, 36], [113, 40, 108, 38], [113, 43, 108, 41], [113, 45, 108, 43], [113, 46, 108, 44], [113, 47, 108, 45], [114, 4, 109, 2, "navajowhite"], [114, 15, 109, 13], [114, 17, 109, 15, "Float32Array"], [114, 29, 109, 27], [114, 30, 109, 28, "of"], [114, 32, 109, 30], [114, 33, 109, 31], [114, 36, 109, 34], [114, 38, 109, 36], [114, 41, 109, 39], [114, 43, 109, 41], [114, 46, 109, 44], [114, 48, 109, 46], [114, 49, 109, 47], [114, 50, 109, 48], [115, 4, 110, 2, "navy"], [115, 8, 110, 6], [115, 10, 110, 8, "Float32Array"], [115, 22, 110, 20], [115, 23, 110, 21, "of"], [115, 25, 110, 23], [115, 26, 110, 24], [115, 27, 110, 25], [115, 29, 110, 27], [115, 30, 110, 28], [115, 32, 110, 30], [115, 35, 110, 33], [115, 37, 110, 35], [115, 38, 110, 36], [115, 39, 110, 37], [116, 4, 111, 2, "oldlace"], [116, 11, 111, 9], [116, 13, 111, 11, "Float32Array"], [116, 25, 111, 23], [116, 26, 111, 24, "of"], [116, 28, 111, 26], [116, 29, 111, 27], [116, 32, 111, 30], [116, 34, 111, 32], [116, 37, 111, 35], [116, 39, 111, 37], [116, 42, 111, 40], [116, 44, 111, 42], [116, 45, 111, 43], [116, 46, 111, 44], [117, 4, 112, 2, "olive"], [117, 9, 112, 7], [117, 11, 112, 9, "Float32Array"], [117, 23, 112, 21], [117, 24, 112, 22, "of"], [117, 26, 112, 24], [117, 27, 112, 25], [117, 30, 112, 28], [117, 32, 112, 30], [117, 35, 112, 33], [117, 37, 112, 35], [117, 38, 112, 36], [117, 40, 112, 38], [117, 41, 112, 39], [117, 42, 112, 40], [118, 4, 113, 2, "<PERSON><PERSON><PERSON>"], [118, 13, 113, 11], [118, 15, 113, 13, "Float32Array"], [118, 27, 113, 25], [118, 28, 113, 26, "of"], [118, 30, 113, 28], [118, 31, 113, 29], [118, 34, 113, 32], [118, 36, 113, 34], [118, 39, 113, 37], [118, 41, 113, 39], [118, 43, 113, 41], [118, 45, 113, 43], [118, 46, 113, 44], [118, 47, 113, 45], [119, 4, 114, 2, "orange"], [119, 10, 114, 8], [119, 12, 114, 10, "Float32Array"], [119, 24, 114, 22], [119, 25, 114, 23, "of"], [119, 27, 114, 25], [119, 28, 114, 26], [119, 31, 114, 29], [119, 33, 114, 31], [119, 36, 114, 34], [119, 38, 114, 36], [119, 39, 114, 37], [119, 41, 114, 39], [119, 42, 114, 40], [119, 43, 114, 41], [120, 4, 115, 2, "orangered"], [120, 13, 115, 11], [120, 15, 115, 13, "Float32Array"], [120, 27, 115, 25], [120, 28, 115, 26, "of"], [120, 30, 115, 28], [120, 31, 115, 29], [120, 34, 115, 32], [120, 36, 115, 34], [120, 38, 115, 36], [120, 40, 115, 38], [120, 41, 115, 39], [120, 43, 115, 41], [120, 44, 115, 42], [120, 45, 115, 43], [121, 4, 116, 2, "orchid"], [121, 10, 116, 8], [121, 12, 116, 10, "Float32Array"], [121, 24, 116, 22], [121, 25, 116, 23, "of"], [121, 27, 116, 25], [121, 28, 116, 26], [121, 31, 116, 29], [121, 33, 116, 31], [121, 36, 116, 34], [121, 38, 116, 36], [121, 41, 116, 39], [121, 43, 116, 41], [121, 44, 116, 42], [121, 45, 116, 43], [122, 4, 117, 2, "palegoldenrod"], [122, 17, 117, 15], [122, 19, 117, 17, "Float32Array"], [122, 31, 117, 29], [122, 32, 117, 30, "of"], [122, 34, 117, 32], [122, 35, 117, 33], [122, 38, 117, 36], [122, 40, 117, 38], [122, 43, 117, 41], [122, 45, 117, 43], [122, 48, 117, 46], [122, 50, 117, 48], [122, 51, 117, 49], [122, 52, 117, 50], [123, 4, 118, 2, "palegreen"], [123, 13, 118, 11], [123, 15, 118, 13, "Float32Array"], [123, 27, 118, 25], [123, 28, 118, 26, "of"], [123, 30, 118, 28], [123, 31, 118, 29], [123, 34, 118, 32], [123, 36, 118, 34], [123, 39, 118, 37], [123, 41, 118, 39], [123, 44, 118, 42], [123, 46, 118, 44], [123, 47, 118, 45], [123, 48, 118, 46], [124, 4, 119, 2, "paleturquoise"], [124, 17, 119, 15], [124, 19, 119, 17, "Float32Array"], [124, 31, 119, 29], [124, 32, 119, 30, "of"], [124, 34, 119, 32], [124, 35, 119, 33], [124, 38, 119, 36], [124, 40, 119, 38], [124, 43, 119, 41], [124, 45, 119, 43], [124, 48, 119, 46], [124, 50, 119, 48], [124, 51, 119, 49], [124, 52, 119, 50], [125, 4, 120, 2, "palevioletred"], [125, 17, 120, 15], [125, 19, 120, 17, "Float32Array"], [125, 31, 120, 29], [125, 32, 120, 30, "of"], [125, 34, 120, 32], [125, 35, 120, 33], [125, 38, 120, 36], [125, 40, 120, 38], [125, 43, 120, 41], [125, 45, 120, 43], [125, 48, 120, 46], [125, 50, 120, 48], [125, 51, 120, 49], [125, 52, 120, 50], [126, 4, 121, 2, "papayawhip"], [126, 14, 121, 12], [126, 16, 121, 14, "Float32Array"], [126, 28, 121, 26], [126, 29, 121, 27, "of"], [126, 31, 121, 29], [126, 32, 121, 30], [126, 35, 121, 33], [126, 37, 121, 35], [126, 40, 121, 38], [126, 42, 121, 40], [126, 45, 121, 43], [126, 47, 121, 45], [126, 48, 121, 46], [126, 49, 121, 47], [127, 4, 122, 2, "peachpuff"], [127, 13, 122, 11], [127, 15, 122, 13, "Float32Array"], [127, 27, 122, 25], [127, 28, 122, 26, "of"], [127, 30, 122, 28], [127, 31, 122, 29], [127, 34, 122, 32], [127, 36, 122, 34], [127, 39, 122, 37], [127, 41, 122, 39], [127, 44, 122, 42], [127, 46, 122, 44], [127, 47, 122, 45], [127, 48, 122, 46], [128, 4, 123, 2, "peru"], [128, 8, 123, 6], [128, 10, 123, 8, "Float32Array"], [128, 22, 123, 20], [128, 23, 123, 21, "of"], [128, 25, 123, 23], [128, 26, 123, 24], [128, 29, 123, 27], [128, 31, 123, 29], [128, 34, 123, 32], [128, 36, 123, 34], [128, 38, 123, 36], [128, 40, 123, 38], [128, 41, 123, 39], [128, 42, 123, 40], [129, 4, 124, 2, "pink"], [129, 8, 124, 6], [129, 10, 124, 8, "Float32Array"], [129, 22, 124, 20], [129, 23, 124, 21, "of"], [129, 25, 124, 23], [129, 26, 124, 24], [129, 29, 124, 27], [129, 31, 124, 29], [129, 34, 124, 32], [129, 36, 124, 34], [129, 39, 124, 37], [129, 41, 124, 39], [129, 42, 124, 40], [129, 43, 124, 41], [130, 4, 125, 2, "plum"], [130, 8, 125, 6], [130, 10, 125, 8, "Float32Array"], [130, 22, 125, 20], [130, 23, 125, 21, "of"], [130, 25, 125, 23], [130, 26, 125, 24], [130, 29, 125, 27], [130, 31, 125, 29], [130, 34, 125, 32], [130, 36, 125, 34], [130, 39, 125, 37], [130, 41, 125, 39], [130, 42, 125, 40], [130, 43, 125, 41], [131, 4, 126, 2, "powderblue"], [131, 14, 126, 12], [131, 16, 126, 14, "Float32Array"], [131, 28, 126, 26], [131, 29, 126, 27, "of"], [131, 31, 126, 29], [131, 32, 126, 30], [131, 35, 126, 33], [131, 37, 126, 35], [131, 40, 126, 38], [131, 42, 126, 40], [131, 45, 126, 43], [131, 47, 126, 45], [131, 48, 126, 46], [131, 49, 126, 47], [132, 4, 127, 2, "purple"], [132, 10, 127, 8], [132, 12, 127, 10, "Float32Array"], [132, 24, 127, 22], [132, 25, 127, 23, "of"], [132, 27, 127, 25], [132, 28, 127, 26], [132, 31, 127, 29], [132, 33, 127, 31], [132, 34, 127, 32], [132, 36, 127, 34], [132, 39, 127, 37], [132, 41, 127, 39], [132, 42, 127, 40], [132, 43, 127, 41], [133, 4, 128, 2, "rebeccapurple"], [133, 17, 128, 15], [133, 19, 128, 17, "Float32Array"], [133, 31, 128, 29], [133, 32, 128, 30, "of"], [133, 34, 128, 32], [133, 35, 128, 33], [133, 38, 128, 36], [133, 40, 128, 38], [133, 42, 128, 40], [133, 44, 128, 42], [133, 47, 128, 45], [133, 49, 128, 47], [133, 50, 128, 48], [133, 51, 128, 49], [134, 4, 129, 2, "red"], [134, 7, 129, 5], [134, 9, 129, 7, "Float32Array"], [134, 21, 129, 19], [134, 22, 129, 20, "of"], [134, 24, 129, 22], [134, 25, 129, 23], [134, 28, 129, 26], [134, 30, 129, 28], [134, 31, 129, 29], [134, 33, 129, 31], [134, 34, 129, 32], [134, 36, 129, 34], [134, 37, 129, 35], [134, 38, 129, 36], [135, 4, 130, 2, "rosybrown"], [135, 13, 130, 11], [135, 15, 130, 13, "Float32Array"], [135, 27, 130, 25], [135, 28, 130, 26, "of"], [135, 30, 130, 28], [135, 31, 130, 29], [135, 34, 130, 32], [135, 36, 130, 34], [135, 39, 130, 37], [135, 41, 130, 39], [135, 44, 130, 42], [135, 46, 130, 44], [135, 47, 130, 45], [135, 48, 130, 46], [136, 4, 131, 2, "royalblue"], [136, 13, 131, 11], [136, 15, 131, 13, "Float32Array"], [136, 27, 131, 25], [136, 28, 131, 26, "of"], [136, 30, 131, 28], [136, 31, 131, 29], [136, 33, 131, 31], [136, 35, 131, 33], [136, 38, 131, 36], [136, 40, 131, 38], [136, 43, 131, 41], [136, 45, 131, 43], [136, 46, 131, 44], [136, 47, 131, 45], [137, 4, 132, 2, "saddlebrown"], [137, 15, 132, 13], [137, 17, 132, 15, "Float32Array"], [137, 29, 132, 27], [137, 30, 132, 28, "of"], [137, 32, 132, 30], [137, 33, 132, 31], [137, 36, 132, 34], [137, 38, 132, 36], [137, 40, 132, 38], [137, 42, 132, 40], [137, 44, 132, 42], [137, 46, 132, 44], [137, 47, 132, 45], [137, 48, 132, 46], [138, 4, 133, 2, "salmon"], [138, 10, 133, 8], [138, 12, 133, 10, "Float32Array"], [138, 24, 133, 22], [138, 25, 133, 23, "of"], [138, 27, 133, 25], [138, 28, 133, 26], [138, 31, 133, 29], [138, 33, 133, 31], [138, 36, 133, 34], [138, 38, 133, 36], [138, 41, 133, 39], [138, 43, 133, 41], [138, 44, 133, 42], [138, 45, 133, 43], [139, 4, 134, 2, "sandybrown"], [139, 14, 134, 12], [139, 16, 134, 14, "Float32Array"], [139, 28, 134, 26], [139, 29, 134, 27, "of"], [139, 31, 134, 29], [139, 32, 134, 30], [139, 35, 134, 33], [139, 37, 134, 35], [139, 40, 134, 38], [139, 42, 134, 40], [139, 44, 134, 42], [139, 46, 134, 44], [139, 47, 134, 45], [139, 48, 134, 46], [140, 4, 135, 2, "seagreen"], [140, 12, 135, 10], [140, 14, 135, 12, "Float32Array"], [140, 26, 135, 24], [140, 27, 135, 25, "of"], [140, 29, 135, 27], [140, 30, 135, 28], [140, 32, 135, 30], [140, 34, 135, 32], [140, 37, 135, 35], [140, 39, 135, 37], [140, 41, 135, 39], [140, 43, 135, 41], [140, 44, 135, 42], [140, 45, 135, 43], [141, 4, 136, 2, "seashell"], [141, 12, 136, 10], [141, 14, 136, 12, "Float32Array"], [141, 26, 136, 24], [141, 27, 136, 25, "of"], [141, 29, 136, 27], [141, 30, 136, 28], [141, 33, 136, 31], [141, 35, 136, 33], [141, 38, 136, 36], [141, 40, 136, 38], [141, 43, 136, 41], [141, 45, 136, 43], [141, 46, 136, 44], [141, 47, 136, 45], [142, 4, 137, 2, "sienna"], [142, 10, 137, 8], [142, 12, 137, 10, "Float32Array"], [142, 24, 137, 22], [142, 25, 137, 23, "of"], [142, 27, 137, 25], [142, 28, 137, 26], [142, 31, 137, 29], [142, 33, 137, 31], [142, 35, 137, 33], [142, 37, 137, 35], [142, 39, 137, 37], [142, 41, 137, 39], [142, 42, 137, 40], [142, 43, 137, 41], [143, 4, 138, 2, "silver"], [143, 10, 138, 8], [143, 12, 138, 10, "Float32Array"], [143, 24, 138, 22], [143, 25, 138, 23, "of"], [143, 27, 138, 25], [143, 28, 138, 26], [143, 31, 138, 29], [143, 33, 138, 31], [143, 36, 138, 34], [143, 38, 138, 36], [143, 41, 138, 39], [143, 43, 138, 41], [143, 44, 138, 42], [143, 45, 138, 43], [144, 4, 139, 2, "skyblue"], [144, 11, 139, 9], [144, 13, 139, 11, "Float32Array"], [144, 25, 139, 23], [144, 26, 139, 24, "of"], [144, 28, 139, 26], [144, 29, 139, 27], [144, 32, 139, 30], [144, 34, 139, 32], [144, 37, 139, 35], [144, 39, 139, 37], [144, 42, 139, 40], [144, 44, 139, 42], [144, 45, 139, 43], [144, 46, 139, 44], [145, 4, 140, 2, "slateblue"], [145, 13, 140, 11], [145, 15, 140, 13, "Float32Array"], [145, 27, 140, 25], [145, 28, 140, 26, "of"], [145, 30, 140, 28], [145, 31, 140, 29], [145, 34, 140, 32], [145, 36, 140, 34], [145, 38, 140, 36], [145, 40, 140, 38], [145, 43, 140, 41], [145, 45, 140, 43], [145, 46, 140, 44], [145, 47, 140, 45], [146, 4, 141, 2, "slategray"], [146, 13, 141, 11], [146, 15, 141, 13, "Float32Array"], [146, 27, 141, 25], [146, 28, 141, 26, "of"], [146, 30, 141, 28], [146, 31, 141, 29], [146, 34, 141, 32], [146, 36, 141, 34], [146, 39, 141, 37], [146, 41, 141, 39], [146, 44, 141, 42], [146, 46, 141, 44], [146, 47, 141, 45], [146, 48, 141, 46], [147, 4, 142, 2, "<PERSON><PERSON><PERSON>"], [147, 13, 142, 11], [147, 15, 142, 13, "Float32Array"], [147, 27, 142, 25], [147, 28, 142, 26, "of"], [147, 30, 142, 28], [147, 31, 142, 29], [147, 34, 142, 32], [147, 36, 142, 34], [147, 39, 142, 37], [147, 41, 142, 39], [147, 44, 142, 42], [147, 46, 142, 44], [147, 47, 142, 45], [147, 48, 142, 46], [148, 4, 143, 2, "snow"], [148, 8, 143, 6], [148, 10, 143, 8, "Float32Array"], [148, 22, 143, 20], [148, 23, 143, 21, "of"], [148, 25, 143, 23], [148, 26, 143, 24], [148, 29, 143, 27], [148, 31, 143, 29], [148, 34, 143, 32], [148, 36, 143, 34], [148, 39, 143, 37], [148, 41, 143, 39], [148, 42, 143, 40], [148, 43, 143, 41], [149, 4, 144, 2, "springgreen"], [149, 15, 144, 13], [149, 17, 144, 15, "Float32Array"], [149, 29, 144, 27], [149, 30, 144, 28, "of"], [149, 32, 144, 30], [149, 33, 144, 31], [149, 34, 144, 32], [149, 36, 144, 34], [149, 39, 144, 37], [149, 41, 144, 39], [149, 44, 144, 42], [149, 46, 144, 44], [149, 47, 144, 45], [149, 48, 144, 46], [150, 4, 145, 2, "steelblue"], [150, 13, 145, 11], [150, 15, 145, 13, "Float32Array"], [150, 27, 145, 25], [150, 28, 145, 26, "of"], [150, 30, 145, 28], [150, 31, 145, 29], [150, 33, 145, 31], [150, 35, 145, 33], [150, 38, 145, 36], [150, 40, 145, 38], [150, 43, 145, 41], [150, 45, 145, 43], [150, 46, 145, 44], [150, 47, 145, 45], [151, 4, 146, 2, "tan"], [151, 7, 146, 5], [151, 9, 146, 7, "Float32Array"], [151, 21, 146, 19], [151, 22, 146, 20, "of"], [151, 24, 146, 22], [151, 25, 146, 23], [151, 28, 146, 26], [151, 30, 146, 28], [151, 33, 146, 31], [151, 35, 146, 33], [151, 38, 146, 36], [151, 40, 146, 38], [151, 41, 146, 39], [151, 42, 146, 40], [152, 4, 147, 2, "teal"], [152, 8, 147, 6], [152, 10, 147, 8, "Float32Array"], [152, 22, 147, 20], [152, 23, 147, 21, "of"], [152, 25, 147, 23], [152, 26, 147, 24], [152, 27, 147, 25], [152, 29, 147, 27], [152, 32, 147, 30], [152, 34, 147, 32], [152, 37, 147, 35], [152, 39, 147, 37], [152, 40, 147, 38], [152, 41, 147, 39], [153, 4, 148, 2, "thistle"], [153, 11, 148, 9], [153, 13, 148, 11, "Float32Array"], [153, 25, 148, 23], [153, 26, 148, 24, "of"], [153, 28, 148, 26], [153, 29, 148, 27], [153, 32, 148, 30], [153, 34, 148, 32], [153, 37, 148, 35], [153, 39, 148, 37], [153, 42, 148, 40], [153, 44, 148, 42], [153, 45, 148, 43], [153, 46, 148, 44], [154, 4, 149, 2, "tomato"], [154, 10, 149, 8], [154, 12, 149, 10, "Float32Array"], [154, 24, 149, 22], [154, 25, 149, 23, "of"], [154, 27, 149, 25], [154, 28, 149, 26], [154, 31, 149, 29], [154, 33, 149, 31], [154, 35, 149, 33], [154, 37, 149, 35], [154, 39, 149, 37], [154, 41, 149, 39], [154, 42, 149, 40], [154, 43, 149, 41], [155, 4, 150, 2, "turquoise"], [155, 13, 150, 11], [155, 15, 150, 13, "Float32Array"], [155, 27, 150, 25], [155, 28, 150, 26, "of"], [155, 30, 150, 28], [155, 31, 150, 29], [155, 33, 150, 31], [155, 35, 150, 33], [155, 38, 150, 36], [155, 40, 150, 38], [155, 43, 150, 41], [155, 45, 150, 43], [155, 46, 150, 44], [155, 47, 150, 45], [156, 4, 151, 2, "violet"], [156, 10, 151, 8], [156, 12, 151, 10, "Float32Array"], [156, 24, 151, 22], [156, 25, 151, 23, "of"], [156, 27, 151, 25], [156, 28, 151, 26], [156, 31, 151, 29], [156, 33, 151, 31], [156, 36, 151, 34], [156, 38, 151, 36], [156, 41, 151, 39], [156, 43, 151, 41], [156, 44, 151, 42], [156, 45, 151, 43], [157, 4, 152, 2, "wheat"], [157, 9, 152, 7], [157, 11, 152, 9, "Float32Array"], [157, 23, 152, 21], [157, 24, 152, 22, "of"], [157, 26, 152, 24], [157, 27, 152, 25], [157, 30, 152, 28], [157, 32, 152, 30], [157, 35, 152, 33], [157, 37, 152, 35], [157, 40, 152, 38], [157, 42, 152, 40], [157, 43, 152, 41], [157, 44, 152, 42], [158, 4, 153, 2, "white"], [158, 9, 153, 7], [158, 11, 153, 9, "Float32Array"], [158, 23, 153, 21], [158, 24, 153, 22, "of"], [158, 26, 153, 24], [158, 27, 153, 25], [158, 30, 153, 28], [158, 32, 153, 30], [158, 35, 153, 33], [158, 37, 153, 35], [158, 40, 153, 38], [158, 42, 153, 40], [158, 43, 153, 41], [158, 44, 153, 42], [159, 4, 154, 2, "whitesmoke"], [159, 14, 154, 12], [159, 16, 154, 14, "Float32Array"], [159, 28, 154, 26], [159, 29, 154, 27, "of"], [159, 31, 154, 29], [159, 32, 154, 30], [159, 35, 154, 33], [159, 37, 154, 35], [159, 40, 154, 38], [159, 42, 154, 40], [159, 45, 154, 43], [159, 47, 154, 45], [159, 48, 154, 46], [159, 49, 154, 47], [160, 4, 155, 2, "yellow"], [160, 10, 155, 8], [160, 12, 155, 10, "Float32Array"], [160, 24, 155, 22], [160, 25, 155, 23, "of"], [160, 27, 155, 25], [160, 28, 155, 26], [160, 31, 155, 29], [160, 33, 155, 31], [160, 36, 155, 34], [160, 38, 155, 36], [160, 39, 155, 37], [160, 41, 155, 39], [160, 42, 155, 40], [160, 43, 155, 41], [161, 4, 156, 2, "yellowgreen"], [161, 15, 156, 13], [161, 17, 156, 15, "Float32Array"], [161, 29, 156, 27], [161, 30, 156, 28, "of"], [161, 32, 156, 30], [161, 33, 156, 31], [161, 36, 156, 34], [161, 38, 156, 36], [161, 41, 156, 39], [161, 43, 156, 41], [161, 45, 156, 43], [161, 47, 156, 45], [161, 48, 156, 46], [162, 2, 157, 0], [162, 3, 157, 1], [163, 2, 158, 0], [163, 8, 158, 6, "clampCSSByte"], [163, 20, 158, 18], [163, 23, 158, 21, "j"], [163, 24, 158, 22], [163, 28, 158, 26], [164, 4, 159, 2], [165, 4, 160, 2], [165, 10, 160, 8, "i"], [165, 11, 160, 9], [165, 14, 160, 12, "Math"], [165, 18, 160, 16], [165, 19, 160, 17, "round"], [165, 24, 160, 22], [165, 25, 160, 23, "j"], [165, 26, 160, 24], [165, 27, 160, 25], [165, 28, 160, 26], [165, 29, 160, 27], [166, 4, 161, 2], [167, 4, 162, 2], [167, 11, 162, 9, "i"], [167, 12, 162, 10], [167, 15, 162, 13], [167, 16, 162, 14], [167, 19, 162, 17], [167, 20, 162, 18], [167, 23, 162, 21, "i"], [167, 24, 162, 22], [167, 27, 162, 25], [167, 30, 162, 28], [167, 33, 162, 31], [167, 36, 162, 34], [167, 39, 162, 37, "i"], [167, 40, 162, 38], [168, 2, 163, 0], [168, 3, 163, 1], [169, 2, 164, 0], [169, 8, 164, 6, "clampCSSFloat"], [169, 21, 164, 19], [169, 24, 164, 22, "f"], [169, 25, 164, 23], [169, 29, 164, 27], [170, 4, 165, 2], [171, 4, 166, 2], [171, 11, 166, 9, "f"], [171, 12, 166, 10], [171, 15, 166, 13], [171, 16, 166, 14], [171, 19, 166, 17], [171, 20, 166, 18], [171, 23, 166, 21, "f"], [171, 24, 166, 22], [171, 27, 166, 25], [171, 28, 166, 26], [171, 31, 166, 29], [171, 32, 166, 30], [171, 35, 166, 33, "f"], [171, 36, 166, 34], [172, 2, 167, 0], [172, 3, 167, 1], [173, 2, 168, 0], [173, 8, 168, 6, "parseCSSInt"], [173, 19, 168, 17], [173, 22, 168, 20, "str"], [173, 25, 168, 23], [173, 29, 168, 27], [174, 4, 169, 2], [175, 4, 170, 2], [175, 8, 170, 6, "str"], [175, 11, 170, 9], [175, 12, 170, 10, "str"], [175, 15, 170, 13], [175, 16, 170, 14, "length"], [175, 22, 170, 20], [175, 25, 170, 23], [175, 26, 170, 24], [175, 27, 170, 25], [175, 32, 170, 30], [175, 35, 170, 33], [175, 37, 170, 35], [176, 6, 171, 4], [176, 13, 171, 11, "clampCSSByte"], [176, 25, 171, 23], [176, 26, 171, 24, "parseFloat"], [176, 36, 171, 34], [176, 37, 171, 35, "str"], [176, 40, 171, 38], [176, 41, 171, 39], [176, 44, 171, 42], [176, 47, 171, 45], [176, 50, 171, 48], [176, 53, 171, 51], [176, 54, 171, 52], [177, 4, 172, 2], [178, 4, 173, 2], [179, 4, 174, 2], [179, 11, 174, 9, "clampCSSByte"], [179, 23, 174, 21], [179, 24, 174, 22, "parseInt"], [179, 32, 174, 30], [179, 33, 174, 31, "str"], [179, 36, 174, 34], [179, 37, 174, 35], [179, 38, 174, 36], [180, 2, 175, 0], [180, 3, 175, 1], [181, 2, 176, 0], [181, 8, 176, 6, "parseCSSFloat"], [181, 21, 176, 19], [181, 24, 176, 22, "str"], [181, 27, 176, 25], [181, 31, 176, 29], [182, 4, 177, 2], [182, 8, 177, 6, "str"], [182, 11, 177, 9], [182, 16, 177, 14, "undefined"], [182, 25, 177, 23], [182, 27, 177, 25], [183, 6, 178, 4], [183, 13, 178, 11], [183, 14, 178, 12], [184, 4, 179, 2], [185, 4, 180, 2], [186, 4, 181, 2], [186, 8, 181, 6, "str"], [186, 11, 181, 9], [186, 12, 181, 10, "str"], [186, 15, 181, 13], [186, 16, 181, 14, "length"], [186, 22, 181, 20], [186, 25, 181, 23], [186, 26, 181, 24], [186, 27, 181, 25], [186, 32, 181, 30], [186, 35, 181, 33], [186, 37, 181, 35], [187, 6, 182, 4], [187, 13, 182, 11, "clampCSSFloat"], [187, 26, 182, 24], [187, 27, 182, 25, "parseFloat"], [187, 37, 182, 35], [187, 38, 182, 36, "str"], [187, 41, 182, 39], [187, 42, 182, 40], [187, 45, 182, 43], [187, 48, 182, 46], [187, 49, 182, 47], [188, 4, 183, 2], [189, 4, 184, 2], [189, 11, 184, 9, "clampCSSFloat"], [189, 24, 184, 22], [189, 25, 184, 23, "parseFloat"], [189, 35, 184, 33], [189, 36, 184, 34, "str"], [189, 39, 184, 37], [189, 40, 184, 38], [189, 41, 184, 39], [190, 2, 185, 0], [190, 3, 185, 1], [191, 2, 186, 0], [191, 8, 186, 6, "CSSHueToRGB"], [191, 19, 186, 17], [191, 22, 186, 20, "CSSHueToRGB"], [191, 23, 186, 21, "m1"], [191, 25, 186, 23], [191, 27, 186, 25, "m2"], [191, 29, 186, 27], [191, 31, 186, 29, "h"], [191, 32, 186, 30], [191, 37, 186, 35], [192, 4, 187, 2], [192, 8, 187, 6, "h"], [192, 9, 187, 7], [192, 12, 187, 10], [192, 13, 187, 11], [192, 15, 187, 13], [193, 6, 188, 4, "h"], [193, 7, 188, 5], [193, 11, 188, 9], [193, 12, 188, 10], [194, 4, 189, 2], [194, 5, 189, 3], [194, 11, 189, 9], [194, 15, 189, 13, "h"], [194, 16, 189, 14], [194, 19, 189, 17], [194, 20, 189, 18], [194, 22, 189, 20], [195, 6, 190, 4, "h"], [195, 7, 190, 5], [195, 11, 190, 9], [195, 12, 190, 10], [196, 4, 191, 2], [197, 4, 192, 2], [197, 8, 192, 6, "h"], [197, 9, 192, 7], [197, 12, 192, 10], [197, 13, 192, 11], [197, 16, 192, 14], [197, 17, 192, 15], [197, 19, 192, 17], [198, 6, 193, 4], [198, 13, 193, 11, "m1"], [198, 15, 193, 13], [198, 18, 193, 16], [198, 19, 193, 17, "m2"], [198, 21, 193, 19], [198, 24, 193, 22, "m1"], [198, 26, 193, 24], [198, 30, 193, 28, "h"], [198, 31, 193, 29], [198, 34, 193, 32], [198, 35, 193, 33], [199, 4, 194, 2], [200, 4, 195, 2], [200, 8, 195, 6, "h"], [200, 9, 195, 7], [200, 12, 195, 10], [200, 13, 195, 11], [200, 16, 195, 14], [200, 17, 195, 15], [200, 19, 195, 17], [201, 6, 196, 4], [201, 13, 196, 11, "m2"], [201, 15, 196, 13], [202, 4, 197, 2], [203, 4, 198, 2], [203, 8, 198, 6, "h"], [203, 9, 198, 7], [203, 12, 198, 10], [203, 13, 198, 11], [203, 16, 198, 14], [203, 17, 198, 15], [203, 19, 198, 17], [204, 6, 199, 4], [204, 13, 199, 11, "m1"], [204, 15, 199, 13], [204, 18, 199, 16], [204, 19, 199, 17, "m2"], [204, 21, 199, 19], [204, 24, 199, 22, "m1"], [204, 26, 199, 24], [204, 31, 199, 29], [204, 32, 199, 30], [204, 35, 199, 33], [204, 36, 199, 34], [204, 39, 199, 37, "h"], [204, 40, 199, 38], [204, 41, 199, 39], [204, 44, 199, 42], [204, 45, 199, 43], [205, 4, 200, 2], [206, 4, 201, 2], [206, 11, 201, 9, "m1"], [206, 13, 201, 11], [207, 2, 202, 0], [207, 3, 202, 1], [208, 2, 203, 0], [208, 8, 203, 6, "parseCSSColor"], [208, 21, 203, 19], [208, 24, 203, 22, "cssStr"], [208, 30, 203, 28], [208, 34, 203, 32], [209, 4, 204, 2], [210, 4, 205, 2], [210, 8, 205, 6, "str"], [210, 11, 205, 9], [210, 14, 205, 12, "cssStr"], [210, 20, 205, 18], [210, 21, 205, 19, "replace"], [210, 28, 205, 26], [210, 29, 205, 27], [210, 33, 205, 31], [210, 35, 205, 33], [210, 37, 205, 35], [210, 38, 205, 36], [210, 39, 205, 37, "toLowerCase"], [210, 50, 205, 48], [210, 51, 205, 49], [210, 52, 205, 50], [212, 4, 207, 2], [213, 4, 208, 2], [213, 8, 208, 6, "str"], [213, 11, 208, 9], [213, 15, 208, 13, "CSSColorTable"], [213, 28, 208, 26], [213, 30, 208, 28], [214, 6, 209, 4], [214, 12, 209, 10, "cl"], [214, 14, 209, 12], [214, 17, 209, 15, "CSSColorTable"], [214, 30, 209, 28], [214, 31, 209, 29, "str"], [214, 34, 209, 32], [214, 35, 209, 33], [215, 6, 210, 4], [215, 10, 210, 8, "cl"], [215, 12, 210, 10], [215, 14, 210, 12], [216, 8, 211, 6], [216, 15, 211, 13, "Float32Array"], [216, 27, 211, 25], [216, 28, 211, 26, "of"], [216, 30, 211, 28], [216, 31, 211, 29], [216, 34, 211, 32, "cl"], [216, 36, 211, 34], [216, 37, 211, 35], [217, 6, 212, 4], [218, 6, 213, 4], [218, 13, 213, 11], [218, 17, 213, 15], [219, 4, 214, 2], [219, 5, 214, 3], [219, 6, 214, 4], [221, 4, 216, 2], [222, 4, 217, 2], [222, 8, 217, 6, "str"], [222, 11, 217, 9], [222, 12, 217, 10], [222, 13, 217, 11], [222, 14, 217, 12], [222, 19, 217, 17], [222, 22, 217, 20], [222, 24, 217, 22], [223, 6, 218, 4], [223, 10, 218, 8, "str"], [223, 13, 218, 11], [223, 14, 218, 12, "length"], [223, 20, 218, 18], [223, 25, 218, 23], [223, 26, 218, 24], [223, 28, 218, 26], [224, 8, 219, 6], [224, 12, 219, 10, "iv"], [224, 14, 219, 12], [224, 17, 219, 15, "parseInt"], [224, 25, 219, 23], [224, 26, 219, 24, "str"], [224, 29, 219, 27], [224, 30, 219, 28, "substr"], [224, 36, 219, 34], [224, 37, 219, 35], [224, 38, 219, 36], [224, 39, 219, 37], [224, 41, 219, 39], [224, 43, 219, 41], [224, 44, 219, 42], [224, 45, 219, 43], [224, 46, 219, 44], [225, 8, 220, 6], [225, 12, 220, 10], [225, 14, 220, 12, "iv"], [225, 16, 220, 14], [225, 20, 220, 18], [225, 21, 220, 19], [225, 25, 220, 23, "iv"], [225, 27, 220, 25], [225, 31, 220, 29], [225, 36, 220, 34], [225, 37, 220, 35], [225, 39, 220, 37], [226, 10, 221, 8], [226, 17, 221, 15], [226, 21, 221, 19], [227, 8, 222, 6], [227, 9, 222, 7], [227, 10, 222, 8], [228, 8, 223, 6], [228, 15, 223, 13], [228, 16, 223, 14], [228, 17, 223, 15, "iv"], [228, 19, 223, 17], [228, 22, 223, 20], [228, 27, 223, 25], [228, 32, 223, 30], [228, 33, 223, 31], [228, 36, 223, 34], [228, 37, 223, 35, "iv"], [228, 39, 223, 37], [228, 42, 223, 40], [228, 47, 223, 45], [228, 52, 223, 50], [228, 53, 223, 51], [228, 55, 223, 53, "iv"], [228, 57, 223, 55], [228, 60, 223, 58], [228, 64, 223, 62], [228, 67, 223, 65], [228, 68, 223, 66, "iv"], [228, 70, 223, 68], [228, 73, 223, 71], [228, 77, 223, 75], [228, 82, 223, 80], [228, 83, 223, 81], [228, 85, 223, 83, "iv"], [228, 87, 223, 85], [228, 90, 223, 88], [228, 93, 223, 91], [228, 96, 223, 94], [228, 97, 223, 95, "iv"], [228, 99, 223, 97], [228, 102, 223, 100], [228, 105, 223, 103], [228, 110, 223, 108], [228, 111, 223, 109], [228, 113, 223, 111], [228, 114, 223, 112], [228, 115, 223, 113], [229, 6, 224, 4], [229, 7, 224, 5], [229, 13, 224, 11], [229, 17, 224, 15, "str"], [229, 20, 224, 18], [229, 21, 224, 19, "length"], [229, 27, 224, 25], [229, 32, 224, 30], [229, 33, 224, 31], [229, 35, 224, 33], [230, 8, 225, 6], [230, 12, 225, 10, "iv"], [230, 14, 225, 12], [230, 17, 225, 15, "parseInt"], [230, 25, 225, 23], [230, 26, 225, 24, "str"], [230, 29, 225, 27], [230, 30, 225, 28, "substr"], [230, 36, 225, 34], [230, 37, 225, 35], [230, 38, 225, 36], [230, 39, 225, 37], [230, 41, 225, 39], [230, 43, 225, 41], [230, 44, 225, 42], [230, 45, 225, 43], [230, 46, 225, 44], [231, 8, 226, 6], [231, 12, 226, 10], [231, 14, 226, 12, "iv"], [231, 16, 226, 14], [231, 20, 226, 18], [231, 21, 226, 19], [231, 25, 226, 23, "iv"], [231, 27, 226, 25], [231, 31, 226, 29], [231, 39, 226, 37], [231, 40, 226, 38], [231, 42, 226, 40], [232, 10, 227, 8], [232, 17, 227, 15], [232, 21, 227, 19], [233, 8, 228, 6], [233, 9, 228, 7], [233, 10, 228, 8], [234, 8, 229, 6], [234, 15, 229, 13], [234, 16, 229, 14], [234, 17, 229, 15, "iv"], [234, 19, 229, 17], [234, 22, 229, 20], [234, 30, 229, 28], [234, 35, 229, 33], [234, 37, 229, 35], [234, 39, 229, 37], [234, 40, 229, 38, "iv"], [234, 42, 229, 40], [234, 45, 229, 43], [234, 51, 229, 49], [234, 56, 229, 54], [234, 57, 229, 55], [234, 59, 229, 57, "iv"], [234, 61, 229, 59], [234, 64, 229, 62], [234, 68, 229, 66], [234, 70, 229, 68], [234, 71, 229, 69], [234, 72, 229, 70], [235, 6, 230, 4], [235, 7, 230, 5], [235, 13, 230, 11], [235, 17, 230, 15, "str"], [235, 20, 230, 18], [235, 21, 230, 19, "length"], [235, 27, 230, 25], [235, 32, 230, 30], [235, 33, 230, 31], [235, 35, 230, 33], [236, 8, 231, 6], [236, 12, 231, 10, "iv"], [236, 14, 231, 12], [236, 17, 231, 15, "parseInt"], [236, 25, 231, 23], [236, 26, 231, 24, "str"], [236, 29, 231, 27], [236, 30, 231, 28, "substr"], [236, 36, 231, 34], [236, 37, 231, 35], [236, 38, 231, 36], [236, 39, 231, 37], [236, 41, 231, 39], [236, 43, 231, 41], [236, 44, 231, 42], [236, 45, 231, 43], [236, 46, 231, 44], [237, 8, 232, 6], [237, 12, 232, 10], [237, 14, 232, 12, "iv"], [237, 16, 232, 14], [237, 20, 232, 18], [237, 21, 232, 19], [237, 25, 232, 23, "iv"], [237, 27, 232, 25], [237, 31, 232, 29], [237, 41, 232, 39], [237, 42, 232, 40], [237, 44, 232, 42], [238, 10, 233, 8], [238, 17, 233, 15], [238, 21, 233, 19], [238, 22, 233, 20], [238, 23, 233, 21], [239, 8, 234, 6], [240, 8, 235, 6], [240, 15, 235, 13], [240, 16, 235, 14], [240, 17, 235, 15, "iv"], [240, 19, 235, 17], [240, 22, 235, 20], [240, 32, 235, 30], [240, 37, 235, 35], [240, 39, 235, 37], [240, 42, 235, 40], [240, 46, 235, 44], [240, 48, 235, 46], [240, 49, 235, 47, "iv"], [240, 51, 235, 49], [240, 54, 235, 52], [240, 62, 235, 60], [240, 67, 235, 65], [240, 69, 235, 67], [240, 71, 235, 69], [240, 72, 235, 70, "iv"], [240, 74, 235, 72], [240, 77, 235, 75], [240, 83, 235, 81], [240, 88, 235, 86], [240, 89, 235, 87], [240, 91, 235, 89], [240, 92, 235, 90, "iv"], [240, 94, 235, 92], [240, 97, 235, 95], [240, 101, 235, 99], [240, 105, 235, 103], [240, 108, 235, 106], [240, 109, 235, 107], [241, 6, 236, 4], [242, 6, 237, 4], [242, 13, 237, 11], [242, 17, 237, 15], [243, 4, 238, 2], [244, 4, 239, 2], [244, 8, 239, 6, "op"], [244, 10, 239, 8], [244, 13, 239, 11, "str"], [244, 16, 239, 14], [244, 17, 239, 15, "indexOf"], [244, 24, 239, 22], [244, 25, 239, 23], [244, 28, 239, 26], [244, 29, 239, 27], [245, 6, 240, 4, "ep"], [245, 8, 240, 6], [245, 11, 240, 9, "str"], [245, 14, 240, 12], [245, 15, 240, 13, "indexOf"], [245, 22, 240, 20], [245, 23, 240, 21], [245, 26, 240, 24], [245, 27, 240, 25], [246, 4, 241, 2], [246, 8, 241, 6, "op"], [246, 10, 241, 8], [246, 15, 241, 13], [246, 16, 241, 14], [246, 17, 241, 15], [246, 21, 241, 19, "ep"], [246, 23, 241, 21], [246, 26, 241, 24], [246, 27, 241, 25], [246, 32, 241, 30, "str"], [246, 35, 241, 33], [246, 36, 241, 34, "length"], [246, 42, 241, 40], [246, 44, 241, 42], [247, 6, 242, 4], [247, 10, 242, 8, "fname"], [247, 15, 242, 13], [247, 18, 242, 16, "str"], [247, 21, 242, 19], [247, 22, 242, 20, "substr"], [247, 28, 242, 26], [247, 29, 242, 27], [247, 30, 242, 28], [247, 32, 242, 30, "op"], [247, 34, 242, 32], [247, 35, 242, 33], [248, 6, 243, 4], [248, 10, 243, 8, "params"], [248, 16, 243, 14], [248, 19, 243, 17, "str"], [248, 22, 243, 20], [248, 23, 243, 21, "substr"], [248, 29, 243, 27], [248, 30, 243, 28, "op"], [248, 32, 243, 30], [248, 35, 243, 33], [248, 36, 243, 34], [248, 38, 243, 36, "ep"], [248, 40, 243, 38], [248, 44, 243, 42, "op"], [248, 46, 243, 44], [248, 49, 243, 47], [248, 50, 243, 48], [248, 51, 243, 49], [248, 52, 243, 50], [248, 53, 243, 51, "split"], [248, 58, 243, 56], [248, 59, 243, 57], [248, 62, 243, 60], [248, 63, 243, 61], [249, 6, 244, 4], [249, 10, 244, 8, "alpha"], [249, 15, 244, 13], [249, 18, 244, 16], [249, 19, 244, 17], [249, 20, 244, 18], [249, 21, 244, 19], [250, 6, 245, 4], [250, 14, 245, 12, "fname"], [250, 19, 245, 17], [251, 8, 246, 6], [252, 8, 247, 6], [253, 8, 248, 6], [253, 13, 248, 11], [253, 19, 248, 17], [254, 10, 249, 8], [254, 14, 249, 12, "params"], [254, 20, 249, 18], [254, 21, 249, 19, "length"], [254, 27, 249, 25], [254, 32, 249, 30], [254, 33, 249, 31], [254, 35, 249, 33], [255, 12, 250, 10], [255, 19, 250, 17], [255, 23, 250, 21], [256, 10, 251, 8], [257, 10, 252, 8, "alpha"], [257, 15, 252, 13], [257, 18, 252, 16, "parseCSSFloat"], [257, 31, 252, 29], [257, 32, 252, 30, "params"], [257, 38, 252, 36], [257, 39, 252, 37, "pop"], [257, 42, 252, 40], [257, 43, 252, 41], [257, 44, 252, 42], [257, 45, 252, 43], [258, 8, 253, 6], [259, 8, 254, 6], [259, 13, 254, 11], [259, 18, 254, 16], [260, 10, 255, 8], [260, 14, 255, 12, "params"], [260, 20, 255, 18], [260, 21, 255, 19, "length"], [260, 27, 255, 25], [260, 32, 255, 30], [260, 33, 255, 31], [260, 35, 255, 33], [261, 12, 256, 10], [261, 19, 256, 17], [261, 23, 256, 21], [262, 10, 257, 8], [263, 10, 258, 8], [263, 17, 258, 15], [263, 18, 258, 16, "parseCSSInt"], [263, 29, 258, 27], [263, 30, 258, 28, "params"], [263, 36, 258, 34], [263, 37, 258, 35], [263, 38, 258, 36], [263, 39, 258, 37], [263, 40, 258, 38], [263, 42, 258, 40, "parseCSSInt"], [263, 53, 258, 51], [263, 54, 258, 52, "params"], [263, 60, 258, 58], [263, 61, 258, 59], [263, 62, 258, 60], [263, 63, 258, 61], [263, 64, 258, 62], [263, 66, 258, 64, "parseCSSInt"], [263, 77, 258, 75], [263, 78, 258, 76, "params"], [263, 84, 258, 82], [263, 85, 258, 83], [263, 86, 258, 84], [263, 87, 258, 85], [263, 88, 258, 86], [263, 90, 258, 88, "alpha"], [263, 95, 258, 93], [263, 96, 258, 94], [264, 8, 259, 6], [265, 8, 260, 6], [266, 8, 261, 6], [266, 13, 261, 11], [266, 19, 261, 17], [267, 10, 262, 8], [267, 14, 262, 12, "params"], [267, 20, 262, 18], [267, 21, 262, 19, "length"], [267, 27, 262, 25], [267, 32, 262, 30], [267, 33, 262, 31], [267, 35, 262, 33], [268, 12, 263, 10], [268, 19, 263, 17], [268, 23, 263, 21], [269, 10, 264, 8], [270, 10, 265, 8, "alpha"], [270, 15, 265, 13], [270, 18, 265, 16, "parseCSSFloat"], [270, 31, 265, 29], [270, 32, 265, 30, "params"], [270, 38, 265, 36], [270, 39, 265, 37, "pop"], [270, 42, 265, 40], [270, 43, 265, 41], [270, 44, 265, 42], [270, 45, 265, 43], [271, 8, 266, 6], [272, 8, 267, 6], [272, 13, 267, 11], [272, 18, 267, 16], [273, 10, 268, 8], [273, 14, 268, 12, "params"], [273, 20, 268, 18], [273, 21, 268, 19, "length"], [273, 27, 268, 25], [273, 32, 268, 30], [273, 33, 268, 31], [273, 35, 268, 33], [274, 12, 269, 10], [274, 19, 269, 17], [274, 23, 269, 21], [275, 10, 270, 8], [276, 10, 271, 8], [276, 14, 271, 12, "h"], [276, 15, 271, 13], [276, 18, 271, 16], [276, 19, 271, 17, "parseFloat"], [276, 29, 271, 27], [276, 30, 271, 28, "params"], [276, 36, 271, 34], [276, 37, 271, 35], [276, 38, 271, 36], [276, 39, 271, 37], [276, 40, 271, 38], [276, 43, 271, 41], [276, 46, 271, 44], [276, 49, 271, 47], [276, 52, 271, 50], [276, 56, 271, 54], [276, 59, 271, 57], [276, 62, 271, 60], [276, 65, 271, 63], [276, 66, 271, 64], [276, 67, 271, 65], [277, 10, 272, 8], [278, 10, 273, 8], [279, 10, 274, 8], [279, 14, 274, 12, "s"], [279, 15, 274, 13], [279, 18, 274, 16, "parseCSSFloat"], [279, 31, 274, 29], [279, 32, 274, 30, "params"], [279, 38, 274, 36], [279, 39, 274, 37], [279, 40, 274, 38], [279, 41, 274, 39], [279, 42, 274, 40], [280, 10, 275, 8], [280, 14, 275, 12, "l"], [280, 15, 275, 13], [280, 18, 275, 16, "parseCSSFloat"], [280, 31, 275, 29], [280, 32, 275, 30, "params"], [280, 38, 275, 36], [280, 39, 275, 37], [280, 40, 275, 38], [280, 41, 275, 39], [280, 42, 275, 40], [281, 10, 276, 8], [281, 14, 276, 12, "m2"], [281, 16, 276, 14], [281, 19, 276, 17, "l"], [281, 20, 276, 18], [281, 24, 276, 22], [281, 27, 276, 25], [281, 30, 276, 28, "l"], [281, 31, 276, 29], [281, 35, 276, 33, "s"], [281, 36, 276, 34], [281, 39, 276, 37], [281, 40, 276, 38], [281, 41, 276, 39], [281, 44, 276, 42, "l"], [281, 45, 276, 43], [281, 48, 276, 46, "s"], [281, 49, 276, 47], [281, 52, 276, 50, "l"], [281, 53, 276, 51], [281, 56, 276, 54, "s"], [281, 57, 276, 55], [282, 10, 277, 8], [282, 14, 277, 12, "m1"], [282, 16, 277, 14], [282, 19, 277, 17, "l"], [282, 20, 277, 18], [282, 23, 277, 21], [282, 24, 277, 22], [282, 27, 277, 25, "m2"], [282, 29, 277, 27], [283, 10, 278, 8], [283, 17, 278, 15], [283, 18, 278, 16, "clampCSSByte"], [283, 30, 278, 28], [283, 31, 278, 29, "CSSHueToRGB"], [283, 42, 278, 40], [283, 43, 278, 41, "m1"], [283, 45, 278, 43], [283, 47, 278, 45, "m2"], [283, 49, 278, 47], [283, 51, 278, 49, "h"], [283, 52, 278, 50], [283, 55, 278, 53], [283, 56, 278, 54], [283, 59, 278, 57], [283, 60, 278, 58], [283, 61, 278, 59], [283, 64, 278, 62], [283, 67, 278, 65], [283, 68, 278, 66], [283, 70, 278, 68, "clampCSSByte"], [283, 82, 278, 80], [283, 83, 278, 81, "CSSHueToRGB"], [283, 94, 278, 92], [283, 95, 278, 93, "m1"], [283, 97, 278, 95], [283, 99, 278, 97, "m2"], [283, 101, 278, 99], [283, 103, 278, 101, "h"], [283, 104, 278, 102], [283, 105, 278, 103], [283, 108, 278, 106], [283, 111, 278, 109], [283, 112, 278, 110], [283, 114, 278, 112, "clampCSSByte"], [283, 126, 278, 124], [283, 127, 278, 125, "CSSHueToRGB"], [283, 138, 278, 136], [283, 139, 278, 137, "m1"], [283, 141, 278, 139], [283, 143, 278, 141, "m2"], [283, 145, 278, 143], [283, 147, 278, 145, "h"], [283, 148, 278, 146], [283, 151, 278, 149], [283, 152, 278, 150], [283, 155, 278, 153], [283, 156, 278, 154], [283, 157, 278, 155], [283, 160, 278, 158], [283, 163, 278, 161], [283, 164, 278, 162], [283, 166, 278, 164, "alpha"], [283, 171, 278, 169], [283, 172, 278, 170], [284, 8, 279, 6], [285, 10, 280, 8], [285, 17, 280, 15], [285, 21, 280, 19], [286, 6, 281, 4], [287, 4, 282, 2], [288, 4, 283, 2], [288, 11, 283, 9], [288, 15, 283, 13], [289, 2, 284, 0], [289, 3, 284, 1], [290, 2, 285, 7], [290, 8, 285, 13, "Color"], [290, 13, 285, 18], [290, 16, 285, 21, "color"], [290, 21, 285, 26], [290, 25, 285, 30], [291, 4, 286, 2], [291, 8, 286, 6, "color"], [291, 13, 286, 11], [291, 25, 286, 23, "Float32Array"], [291, 37, 286, 35], [291, 39, 286, 37], [292, 6, 287, 4], [292, 13, 287, 11, "color"], [292, 18, 287, 16], [293, 4, 288, 2], [293, 5, 288, 3], [293, 11, 288, 9], [293, 15, 288, 13, "Array"], [293, 20, 288, 18], [293, 21, 288, 19, "isArray"], [293, 28, 288, 26], [293, 29, 288, 27, "color"], [293, 34, 288, 32], [293, 35, 288, 33], [293, 37, 288, 35], [294, 6, 289, 4], [294, 13, 289, 11], [294, 17, 289, 15, "Float32Array"], [294, 29, 289, 27], [294, 30, 289, 28, "color"], [294, 35, 289, 33], [294, 36, 289, 34], [295, 4, 290, 2], [295, 5, 290, 3], [295, 11, 290, 9], [295, 15, 290, 13], [295, 22, 290, 20, "color"], [295, 27, 290, 25], [295, 32, 290, 30], [295, 40, 290, 38], [295, 42, 290, 40], [296, 6, 291, 4], [296, 12, 291, 10, "r"], [296, 13, 291, 11], [296, 16, 291, 14, "parseCSSColor"], [296, 29, 291, 27], [296, 30, 291, 28, "color"], [296, 35, 291, 33], [296, 36, 291, 34], [297, 6, 292, 4], [297, 12, 292, 10, "rgba"], [297, 16, 292, 14], [297, 19, 292, 17, "r"], [297, 20, 292, 18], [297, 25, 292, 23], [297, 29, 292, 27], [297, 32, 292, 30, "CSSColorTable"], [297, 45, 292, 43], [297, 46, 292, 44, "black"], [297, 51, 292, 49], [297, 54, 292, 52, "r"], [297, 55, 292, 53], [298, 6, 293, 4], [298, 13, 293, 11, "Float32Array"], [298, 25, 293, 23], [298, 26, 293, 24, "of"], [298, 28, 293, 26], [298, 29, 293, 27, "rgba"], [298, 33, 293, 31], [298, 34, 293, 32], [298, 35, 293, 33], [298, 36, 293, 34], [298, 39, 293, 37], [298, 42, 293, 40], [298, 44, 293, 42, "rgba"], [298, 48, 293, 46], [298, 49, 293, 47], [298, 50, 293, 48], [298, 51, 293, 49], [298, 54, 293, 52], [298, 57, 293, 55], [298, 59, 293, 57, "rgba"], [298, 63, 293, 61], [298, 64, 293, 62], [298, 65, 293, 63], [298, 66, 293, 64], [298, 69, 293, 67], [298, 72, 293, 70], [298, 74, 293, 72, "rgba"], [298, 78, 293, 76], [298, 79, 293, 77], [298, 80, 293, 78], [298, 81, 293, 79], [298, 82, 293, 80], [299, 4, 294, 2], [299, 5, 294, 3], [299, 11, 294, 9], [300, 6, 295, 4], [300, 13, 295, 11, "Float32Array"], [300, 25, 295, 23], [300, 26, 295, 24, "of"], [300, 28, 295, 26], [300, 29, 295, 27, "red"], [300, 32, 295, 30], [300, 33, 295, 31, "color"], [300, 38, 295, 36], [300, 39, 295, 37], [300, 42, 295, 40], [300, 45, 295, 43], [300, 47, 295, 45, "green"], [300, 52, 295, 50], [300, 53, 295, 51, "color"], [300, 58, 295, 56], [300, 59, 295, 57], [300, 62, 295, 60], [300, 65, 295, 63], [300, 67, 295, 65, "blue"], [300, 71, 295, 69], [300, 72, 295, 70, "color"], [300, 77, 295, 75], [300, 78, 295, 76], [300, 81, 295, 79], [300, 84, 295, 82], [300, 86, 295, 84, "alphaf"], [300, 92, 295, 90], [300, 93, 295, 91, "color"], [300, 98, 295, 96], [300, 99, 295, 97], [300, 100, 295, 98], [301, 4, 296, 2], [302, 2, 297, 0], [302, 3, 297, 1], [303, 2, 297, 2, "exports"], [303, 9, 297, 2], [303, 10, 297, 2, "Color"], [303, 15, 297, 2], [303, 18, 297, 2, "Color"], [303, 23, 297, 2], [304, 0, 297, 2], [304, 3]], "functionMap": {"names": ["<global>", "alphaf", "red", "green", "blue", "clampCSSByte", "clampCSSFloat", "parseCSSInt", "parseCSSFloat", "CSSHueToRGB", "parseCSSColor", "Color"], "mappings": "AAA,eC,0BD;YEC,kBF;cGC,iBH;aIC,YJ;qBK0J;CLK;sBMC;CNG;oBOC;CPO;sBQC;CRS;oBSC;CTgB;sBUC;CViF;qBWC;CXY"}}, "type": "js/module"}]}