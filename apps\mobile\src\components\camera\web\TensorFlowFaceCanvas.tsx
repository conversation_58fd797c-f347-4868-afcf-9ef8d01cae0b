import React, { useEffect, useRef, useState } from 'react';

interface TensorFlowFaceCanvasProps {
  containerId: string;
  width: number;
  height: number;
}

// TensorFlow.js BlazeFace implementation for reliable cross-browser face detection
export default function TensorFlowFaceCanvas({ containerId, width, height }: TensorFlowFaceCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const rafRef = useRef<number | null>(null);
  const modelRef = useRef<any | null>(null);
  const debugCounterRef = useRef<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('[TensorFlowFaceCanvas] 🚀 STARTING INITIALIZATION...', { containerId, width, height });
    console.log('[TensorFlowFaceCanvas] 🔧 Component mounted and running!');
    
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('[TensorFlowFaceCanvas] Container not found:', containerId);
      setError('Container not found');
      return;
    }

    const video: HTMLVideoElement | null = container.querySelector('video');
    if (!video) {
      console.error('[TensorFlowFaceCanvas] Video element not found in container');
      setError('Video element not found');
      return;
    }

    // Resize canvas
    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('[TensorFlowFaceCanvas] Canvas ref not available');
      setError('Canvas not available');
      return;
    }
    canvas.width = width;
    canvas.height = height;

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('[TensorFlowFaceCanvas] Canvas context not available');
      setError('Canvas context not available');
      return;
    }

    let running = true;
    debugCounterRef.current = 0;

    const loadTensorFlowAndBlazeFace = async () => {
      try {
        console.log('[TensorFlowFaceCanvas] Loading TensorFlow.js...');
        
        // Load TensorFlow.js
        if (!(window as any).tf) {
          await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js');
          console.log('[TensorFlowFaceCanvas] TensorFlow.js loaded');
        }

        // Load BlazeFace
        if (!(window as any).blazeface) {
          await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');
          console.log('[TensorFlowFaceCanvas] BlazeFace loaded');
        }

        // Initialize BlazeFace model
        console.log('[TensorFlowFaceCanvas] Initializing BlazeFace model...');
        const blazeface = (window as any).blazeface;
        modelRef.current = await blazeface.load();
        console.log('[TensorFlowFaceCanvas] ✅ BlazeFace model loaded successfully');
        
        setIsLoading(false);
        setError(null);
        
      } catch (error) {
        console.error('[TensorFlowFaceCanvas] ❌ Failed to load TensorFlow/BlazeFace:', error);
        setError(`Failed to load face detection: ${error.message}`);
        setIsLoading(false);
      }
    };

    const loadScript = (src: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.async = true;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        document.head.appendChild(script);
      });
    };

    const applyFallbackBlur = (ctx: CanvasRenderingContext2D, videoW: number, videoH: number, canvasW: number, canvasH: number) => {
      // Apply blur to common face areas when no faces are detected
      const scale = Math.max(canvasW / videoW, canvasH / videoH);
      const scaledW = videoW * scale;
      const scaledH = videoH * scale;
      const offsetX = (canvasW - scaledW) / 2;
      const offsetY = (canvasH - scaledH) / 2;

      // Common face areas (center-upper region for selfies)
      const faceAreas = [
        { x: 0.25, y: 0.15, w: 0.5, h: 0.5 }, // Center face
        { x: 0.1, y: 0.2, w: 0.35, h: 0.4 },  // Left side
        { x: 0.55, y: 0.2, w: 0.35, h: 0.4 }  // Right side
      ];

      faceAreas.forEach(area => {
        const x = area.x * scaledW + offsetX;
        const y = area.y * scaledH + offsetY;
        const w = area.w * scaledW;
        const h = area.h * scaledH;

        ctx.save();
        ctx.beginPath();
        ctx.ellipse(x + w / 2, y + h / 2, w / 2, h / 2, 0, 0, Math.PI * 2);
        ctx.clip();
        ctx.filter = 'blur(25px)';
        ctx.drawImage(video, 0, 0, canvasW, canvasH);
        ctx.restore();
      });
    };

    const loop = async () => {
      if (!running) return;
      rafRef.current = requestAnimationFrame(loop);

      if (!modelRef.current) {
        // Log occasionally that model is not ready
        debugCounterRef.current++;
        if (debugCounterRef.current % 60 === 0) { // Every ~1 second at 60fps
          console.log('[TensorFlowFaceCanvas] Waiting for BlazeFace model to load...');
        }
        return;
      }

      try {
        const videoW = video.videoWidth || width;
        const videoH = video.videoHeight || height;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Log video status occasionally
        debugCounterRef.current++;
        if (debugCounterRef.current % 300 === 0) { // Every ~5 seconds
          console.log('[TensorFlowFaceCanvas] Video status:', {
            dimensions: `${videoW}x${videoH}`,
            readyState: video.readyState,
            paused: video.paused,
            currentTime: video.currentTime
          });
        }

        if (videoW === 0 || videoH === 0) {
          if (debugCounterRef.current % 60 === 0) {
            console.log('[TensorFlowFaceCanvas] Video not ready - dimensions:', videoW, 'x', videoH);
          }
          return;
        }

        // Detect faces using BlazeFace
        const tf = (window as any).tf;
        const tensor = tf.browser.fromPixels(video);
        const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6); // Lower threshold for better detection
        tensor.dispose(); // Clean up tensor

        // Debug logging for face detection
        if (predictions.length > 0) {
          console.log(`[TensorFlowFaceCanvas] 🎯 Detected ${predictions.length} face(s) at frame ${debugCounterRef.current}`);
        } else if (debugCounterRef.current % 120 === 0) { // Every ~2 seconds
          console.log(`[TensorFlowFaceCanvas] No faces detected (frame ${debugCounterRef.current})`);
        }

        if (predictions.length === 0) {
          // Apply fallback blur to common face areas
          if (debugCounterRef.current > 60) { // Only after model has had time to initialize
            applyFallbackBlur(ctx, videoW, videoH, width, height);
          }
          return;
        }

        // Compute scale from video to canvas
        const scale = Math.max(width / videoW, height / videoH);
        const scaledW = videoW * scale;
        const scaledH = videoH * scale;
        const offsetX = (width - scaledW) / 2;
        const offsetY = (height - scaledH) / 2;

        // Draw blur for each detected face
        predictions.forEach((prediction: any, index: number) => {
          const [x1, y1] = prediction.topLeft;
          const [x2, y2] = prediction.bottomRight;

          if (debugCounterRef.current % 60 === 0) {
            console.log(`[TensorFlowFaceCanvas] Face ${index + 1} raw coords:`, { x1, y1, x2, y2 });
          }

          // Validate coordinates
          if (x1 >= x2 || y1 >= y2) {
            console.warn(`[TensorFlowFaceCanvas] Invalid face coordinates for face ${index + 1}`);
            return;
          }

          // Calculate face dimensions with safety checks
          const rawFaceWidth = Math.abs(x2 - x1);
          const rawFaceHeight = Math.abs(y2 - y1);

          if (rawFaceWidth <= 0 || rawFaceHeight <= 0) {
            console.warn(`[TensorFlowFaceCanvas] Invalid face dimensions for face ${index + 1}`);
            return;
          }

          // Expand the bounding box for better coverage
          const centerX = (x1 + x2) / 2;
          const centerY = (y1 + y2) / 2;
          const faceWidth = rawFaceWidth * 1.4; // Expand by 40%
          const faceHeight = rawFaceHeight * 1.6; // Expand by 60%

          const expandedX1 = centerX - faceWidth / 2;
          const expandedY1 = centerY - faceHeight / 2;

          // Map to canvas coordinates
          const canvasX = expandedX1 * scale + offsetX;
          const canvasY = expandedY1 * scale + offsetY;
          const canvasW = faceWidth * scale;
          const canvasH = faceHeight * scale;

          // Ensure positive radii for ellipse
          const radiusX = Math.max(canvasW / 2, 5);
          const radiusY = Math.max(canvasH / 2, 5);

          // Draw blurred oval patch
          ctx.save();
          ctx.beginPath();
          ctx.ellipse(canvasX + canvasW / 2, canvasY + canvasH / 2, radiusX, radiusY, 0, 0, Math.PI * 2);
          ctx.clip();
          ctx.filter = 'blur(30px)';
          ctx.drawImage(video, 0, 0, width, height);
          ctx.restore();

          if (debugCounterRef.current % 60 === 0) {
            console.log(`[TensorFlowFaceCanvas] Blurred face ${index + 1} at (${Math.round(canvasX)}, ${Math.round(canvasY)}) with radii (${Math.round(radiusX)}, ${Math.round(radiusY)})`);
          }
        });

      } catch (error) {
        // Log errors but keep the loop running
        if (debugCounterRef.current % 300 === 0) {
          console.error('[TensorFlowFaceCanvas] Detection error:', error);
        }
      }
    };

    // Start loading and then begin the loop
    loadTensorFlowAndBlazeFace().then(() => {
      if (running) {
        rafRef.current = requestAnimationFrame(loop);
      }
    });

    return () => {
      running = false;
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [containerId, width, height]);

  return (
    <>
      <canvas
        ref={canvasRef}
        style={{ 
          position: 'absolute', 
          left: 0, 
          top: 0, 
          width, 
          height, 
          pointerEvents: 'none',
          zIndex: 10
        }}
      />
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 20
        }}>
          🔄 Loading TensorFlow face detection...
        </div>
      )}
      {!isLoading && !error && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(16, 185, 129, 0.8)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 20
        }}>
          🤖 TensorFlow Face Detection Active
        </div>
      )}
      {error && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(255,0,0,0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 20
        }}>
          Face detection error: {error}
        </div>
      )}
    </>
  );
}
