{"authors": "<PERSON> <https://github.com/kernhanda>", "definitionFilename": "index.d.ts", "libraryDependencies": [], "moduleDependencies": [], "libraryMajorVersion": "2", "libraryMinorVersion": "4", "libraryName": "seedrandom 2.4.2", "typingsPackageName": "seedrandom", "projectName": "https://github.com/davidbau/seedrandom", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "UMD", "globals": ["seedrandom"], "declaredModules": [], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "6ad69483683964e97254f65e374a17f845154a74df41e5bf3ea0d53ec1b70e19"}