{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkFont", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 40, "index": 71}}], "key": "s9s7ketQ537BYUKlzBosFu+c4vc=", "exportNames": ["*"]}}, {"name": "./JsiSkTextBlob", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 48, "index": 120}}], "key": "E6PYnUo+68irrsyJGMkO/cUOor4=", "exportNames": ["*"]}}, {"name": "./JsiSkRSXform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 121}, "end": {"line": 4, "column": 46, "index": 167}}], "key": "HY7rKwrek3L50sQmwciH/W0T7ew=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkTextBlobFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkFont = require(_dependencyMap[1], \"./JsiSkFont\");\n  var _JsiSkTextBlob = require(_dependencyMap[2], \"./JsiSkTextBlob\");\n  var _JsiSkRSXform = require(_dependencyMap[3], \"./JsiSkRSXform\");\n  class JsiSkTextBlobFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeFromText(str, font) {\n      return new _JsiSkTextBlob.JsiSkTextBlob(this.CanvasKit, this.CanvasKit.TextBlob.MakeFromText(str, _JsiSkFont.JsiSkFont.fromValue(font)));\n    }\n    MakeFromGlyphs(glyphs, font) {\n      return new _JsiSkTextBlob.JsiSkTextBlob(this.CanvasKit, this.CanvasKit.TextBlob.MakeFromGlyphs(glyphs, _JsiSkFont.JsiSkFont.fromValue(font)));\n    }\n    MakeFromRSXform(str, rsxforms, font) {\n      return new _JsiSkTextBlob.JsiSkTextBlob(this.CanvasKit, this.CanvasKit.TextBlob.MakeFromRSXform(str, rsxforms.map(f => Array.from(_JsiSkRSXform.JsiSkRSXform.fromValue(f))).flat(), _JsiSkFont.JsiSkFont.fromValue(font)));\n    }\n    MakeFromRSXformGlyphs(glyphs, rsxforms, font) {\n      const transforms = rsxforms.flatMap(s => Array.from(_JsiSkRSXform.JsiSkRSXform.fromValue(s)));\n      return new _JsiSkTextBlob.JsiSkTextBlob(this.CanvasKit, this.CanvasKit.TextBlob.MakeFromRSXformGlyphs(glyphs, transforms, _JsiSkFont.JsiSkFont.fromValue(font)));\n    }\n  }\n  exports.JsiSkTextBlobFactory = JsiSkTextBlobFactory;\n});", "lineCount": 29, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkFont"], [7, 16, 2, 0], [7, 19, 2, 0, "require"], [7, 26, 2, 0], [7, 27, 2, 0, "_dependencyMap"], [7, 41, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkTextBlob"], [8, 20, 3, 0], [8, 23, 3, 0, "require"], [8, 30, 3, 0], [8, 31, 3, 0, "_dependencyMap"], [8, 45, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JsiSkRSXform"], [9, 19, 4, 0], [9, 22, 4, 0, "require"], [9, 29, 4, 0], [9, 30, 4, 0, "_dependencyMap"], [9, 44, 4, 0], [10, 2, 5, 7], [10, 8, 5, 13, "JsiSkTextBlobFactory"], [10, 28, 5, 33], [10, 37, 5, 42, "Host"], [10, 47, 5, 46], [10, 48, 5, 47], [11, 4, 6, 2, "constructor"], [11, 15, 6, 13, "constructor"], [11, 16, 6, 14, "CanvasKit"], [11, 25, 6, 23], [11, 27, 6, 25], [12, 6, 7, 4], [12, 11, 7, 9], [12, 12, 7, 10, "CanvasKit"], [12, 21, 7, 19], [12, 22, 7, 20], [13, 4, 8, 2], [14, 4, 9, 2, "MakeFromText"], [14, 16, 9, 14, "MakeFromText"], [14, 17, 9, 15, "str"], [14, 20, 9, 18], [14, 22, 9, 20, "font"], [14, 26, 9, 24], [14, 28, 9, 26], [15, 6, 10, 4], [15, 13, 10, 11], [15, 17, 10, 15, "JsiSkTextBlob"], [15, 45, 10, 28], [15, 46, 10, 29], [15, 50, 10, 33], [15, 51, 10, 34, "CanvasKit"], [15, 60, 10, 43], [15, 62, 10, 45], [15, 66, 10, 49], [15, 67, 10, 50, "CanvasKit"], [15, 76, 10, 59], [15, 77, 10, 60, "TextBlob"], [15, 85, 10, 68], [15, 86, 10, 69, "MakeFromText"], [15, 98, 10, 81], [15, 99, 10, 82, "str"], [15, 102, 10, 85], [15, 104, 10, 87, "JsiSkFont"], [15, 124, 10, 96], [15, 125, 10, 97, "fromValue"], [15, 134, 10, 106], [15, 135, 10, 107, "font"], [15, 139, 10, 111], [15, 140, 10, 112], [15, 141, 10, 113], [15, 142, 10, 114], [16, 4, 11, 2], [17, 4, 12, 2, "MakeFromGlyphs"], [17, 18, 12, 16, "MakeFromGlyphs"], [17, 19, 12, 17, "glyphs"], [17, 25, 12, 23], [17, 27, 12, 25, "font"], [17, 31, 12, 29], [17, 33, 12, 31], [18, 6, 13, 4], [18, 13, 13, 11], [18, 17, 13, 15, "JsiSkTextBlob"], [18, 45, 13, 28], [18, 46, 13, 29], [18, 50, 13, 33], [18, 51, 13, 34, "CanvasKit"], [18, 60, 13, 43], [18, 62, 13, 45], [18, 66, 13, 49], [18, 67, 13, 50, "CanvasKit"], [18, 76, 13, 59], [18, 77, 13, 60, "TextBlob"], [18, 85, 13, 68], [18, 86, 13, 69, "MakeFromGlyphs"], [18, 100, 13, 83], [18, 101, 13, 84, "glyphs"], [18, 107, 13, 90], [18, 109, 13, 92, "JsiSkFont"], [18, 129, 13, 101], [18, 130, 13, 102, "fromValue"], [18, 139, 13, 111], [18, 140, 13, 112, "font"], [18, 144, 13, 116], [18, 145, 13, 117], [18, 146, 13, 118], [18, 147, 13, 119], [19, 4, 14, 2], [20, 4, 15, 2, "MakeFromRSXform"], [20, 19, 15, 17, "MakeFromRSXform"], [20, 20, 15, 18, "str"], [20, 23, 15, 21], [20, 25, 15, 23, "rsxforms"], [20, 33, 15, 31], [20, 35, 15, 33, "font"], [20, 39, 15, 37], [20, 41, 15, 39], [21, 6, 16, 4], [21, 13, 16, 11], [21, 17, 16, 15, "JsiSkTextBlob"], [21, 45, 16, 28], [21, 46, 16, 29], [21, 50, 16, 33], [21, 51, 16, 34, "CanvasKit"], [21, 60, 16, 43], [21, 62, 16, 45], [21, 66, 16, 49], [21, 67, 16, 50, "CanvasKit"], [21, 76, 16, 59], [21, 77, 16, 60, "TextBlob"], [21, 85, 16, 68], [21, 86, 16, 69, "MakeFromRSXform"], [21, 101, 16, 84], [21, 102, 16, 85, "str"], [21, 105, 16, 88], [21, 107, 16, 90, "rsxforms"], [21, 115, 16, 98], [21, 116, 16, 99, "map"], [21, 119, 16, 102], [21, 120, 16, 103, "f"], [21, 121, 16, 104], [21, 125, 16, 108, "Array"], [21, 130, 16, 113], [21, 131, 16, 114, "from"], [21, 135, 16, 118], [21, 136, 16, 119, "JsiSkRSXform"], [21, 162, 16, 131], [21, 163, 16, 132, "fromValue"], [21, 172, 16, 141], [21, 173, 16, 142, "f"], [21, 174, 16, 143], [21, 175, 16, 144], [21, 176, 16, 145], [21, 177, 16, 146], [21, 178, 16, 147, "flat"], [21, 182, 16, 151], [21, 183, 16, 152], [21, 184, 16, 153], [21, 186, 16, 155, "JsiSkFont"], [21, 206, 16, 164], [21, 207, 16, 165, "fromValue"], [21, 216, 16, 174], [21, 217, 16, 175, "font"], [21, 221, 16, 179], [21, 222, 16, 180], [21, 223, 16, 181], [21, 224, 16, 182], [22, 4, 17, 2], [23, 4, 18, 2, "MakeFromRSXformGlyphs"], [23, 25, 18, 23, "MakeFromRSXformGlyphs"], [23, 26, 18, 24, "glyphs"], [23, 32, 18, 30], [23, 34, 18, 32, "rsxforms"], [23, 42, 18, 40], [23, 44, 18, 42, "font"], [23, 48, 18, 46], [23, 50, 18, 48], [24, 6, 19, 4], [24, 12, 19, 10, "transforms"], [24, 22, 19, 20], [24, 25, 19, 23, "rsxforms"], [24, 33, 19, 31], [24, 34, 19, 32, "flatMap"], [24, 41, 19, 39], [24, 42, 19, 40, "s"], [24, 43, 19, 41], [24, 47, 19, 45, "Array"], [24, 52, 19, 50], [24, 53, 19, 51, "from"], [24, 57, 19, 55], [24, 58, 19, 56, "JsiSkRSXform"], [24, 84, 19, 68], [24, 85, 19, 69, "fromValue"], [24, 94, 19, 78], [24, 95, 19, 79, "s"], [24, 96, 19, 80], [24, 97, 19, 81], [24, 98, 19, 82], [24, 99, 19, 83], [25, 6, 20, 4], [25, 13, 20, 11], [25, 17, 20, 15, "JsiSkTextBlob"], [25, 45, 20, 28], [25, 46, 20, 29], [25, 50, 20, 33], [25, 51, 20, 34, "CanvasKit"], [25, 60, 20, 43], [25, 62, 20, 45], [25, 66, 20, 49], [25, 67, 20, 50, "CanvasKit"], [25, 76, 20, 59], [25, 77, 20, 60, "TextBlob"], [25, 85, 20, 68], [25, 86, 20, 69, "MakeFromRSXformGlyphs"], [25, 107, 20, 90], [25, 108, 20, 91, "glyphs"], [25, 114, 20, 97], [25, 116, 20, 99, "transforms"], [25, 126, 20, 109], [25, 128, 20, 111, "JsiSkFont"], [25, 148, 20, 120], [25, 149, 20, 121, "fromValue"], [25, 158, 20, 130], [25, 159, 20, 131, "font"], [25, 163, 20, 135], [25, 164, 20, 136], [25, 165, 20, 137], [25, 166, 20, 138], [26, 4, 21, 2], [27, 2, 22, 0], [28, 2, 22, 1, "exports"], [28, 9, 22, 1], [28, 10, 22, 1, "JsiSkTextBlobFactory"], [28, 30, 22, 1], [28, 33, 22, 1, "JsiSkTextBlobFactory"], [28, 53, 22, 1], [29, 0, 22, 1], [29, 3]], "functionMap": {"names": ["<global>", "JsiSkTextBlobFactory", "constructor", "MakeFromText", "MakeFromGlyphs", "MakeFromRSXform", "rsxforms.map$argument_0", "MakeFromRSXformGlyphs", "rsxforms.flatMap$argument_0"], "mappings": "AAA;OCI;ECC;GDE;EEC;GFE;EGC;GHE;EIC;uGCC,0CD;GJC;EMC;wCCC,0CD;GNE;CDC"}}, "type": "js/module"}]}