{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 277}, "end": {"line": 2, "column": 46, "index": 323}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Group = void 0;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function (n) {\n      for (var e = 1; e < arguments.length; e++) {\n        var t = arguments[e];\n        for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n      }\n      return n;\n    }, _extends.apply(null, arguments);\n  }\n  const Group = ({\n    layer,\n    ...props\n  }) => {\n    if (/*#__PURE__*/ /*#__PURE__*/(0, _react.isValidElement)(layer) && typeof layer === \"object\") {\n      return /*#__PURE__*/_react.default.createElement(\"skLayer\", null, layer, /*#__PURE__*/_react.default.createElement(\"skGroup\", props));\n    }\n    return /*#__PURE__*/_react.default.createElement(\"skGroup\", _extends({\n      layer: layer\n    }, props));\n  };\n  exports.Group = Group;\n});", "lineCount": 29, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "_react"], [6, 12, 2, 0], [6, 15, 2, 0, "_interopRequireWildcard"], [6, 38, 2, 0], [6, 39, 2, 0, "require"], [6, 46, 2, 0], [6, 47, 2, 0, "_dependencyMap"], [6, 61, 2, 0], [7, 2, 2, 46], [7, 11, 2, 46, "_interopRequireWildcard"], [7, 35, 2, 46, "e"], [7, 36, 2, 46], [7, 38, 2, 46, "t"], [7, 39, 2, 46], [7, 68, 2, 46, "WeakMap"], [7, 75, 2, 46], [7, 81, 2, 46, "r"], [7, 82, 2, 46], [7, 89, 2, 46, "WeakMap"], [7, 96, 2, 46], [7, 100, 2, 46, "n"], [7, 101, 2, 46], [7, 108, 2, 46, "WeakMap"], [7, 115, 2, 46], [7, 127, 2, 46, "_interopRequireWildcard"], [7, 150, 2, 46], [7, 162, 2, 46, "_interopRequireWildcard"], [7, 163, 2, 46, "e"], [7, 164, 2, 46], [7, 166, 2, 46, "t"], [7, 167, 2, 46], [7, 176, 2, 46, "t"], [7, 177, 2, 46], [7, 181, 2, 46, "e"], [7, 182, 2, 46], [7, 186, 2, 46, "e"], [7, 187, 2, 46], [7, 188, 2, 46, "__esModule"], [7, 198, 2, 46], [7, 207, 2, 46, "e"], [7, 208, 2, 46], [7, 214, 2, 46, "o"], [7, 215, 2, 46], [7, 217, 2, 46, "i"], [7, 218, 2, 46], [7, 220, 2, 46, "f"], [7, 221, 2, 46], [7, 226, 2, 46, "__proto__"], [7, 235, 2, 46], [7, 243, 2, 46, "default"], [7, 250, 2, 46], [7, 252, 2, 46, "e"], [7, 253, 2, 46], [7, 270, 2, 46, "e"], [7, 271, 2, 46], [7, 294, 2, 46, "e"], [7, 295, 2, 46], [7, 320, 2, 46, "e"], [7, 321, 2, 46], [7, 330, 2, 46, "f"], [7, 331, 2, 46], [7, 337, 2, 46, "o"], [7, 338, 2, 46], [7, 341, 2, 46, "t"], [7, 342, 2, 46], [7, 345, 2, 46, "n"], [7, 346, 2, 46], [7, 349, 2, 46, "r"], [7, 350, 2, 46], [7, 358, 2, 46, "o"], [7, 359, 2, 46], [7, 360, 2, 46, "has"], [7, 363, 2, 46], [7, 364, 2, 46, "e"], [7, 365, 2, 46], [7, 375, 2, 46, "o"], [7, 376, 2, 46], [7, 377, 2, 46, "get"], [7, 380, 2, 46], [7, 381, 2, 46, "e"], [7, 382, 2, 46], [7, 385, 2, 46, "o"], [7, 386, 2, 46], [7, 387, 2, 46, "set"], [7, 390, 2, 46], [7, 391, 2, 46, "e"], [7, 392, 2, 46], [7, 394, 2, 46, "f"], [7, 395, 2, 46], [7, 411, 2, 46, "t"], [7, 412, 2, 46], [7, 416, 2, 46, "e"], [7, 417, 2, 46], [7, 433, 2, 46, "t"], [7, 434, 2, 46], [7, 441, 2, 46, "hasOwnProperty"], [7, 455, 2, 46], [7, 456, 2, 46, "call"], [7, 460, 2, 46], [7, 461, 2, 46, "e"], [7, 462, 2, 46], [7, 464, 2, 46, "t"], [7, 465, 2, 46], [7, 472, 2, 46, "i"], [7, 473, 2, 46], [7, 477, 2, 46, "o"], [7, 478, 2, 46], [7, 481, 2, 46, "Object"], [7, 487, 2, 46], [7, 488, 2, 46, "defineProperty"], [7, 502, 2, 46], [7, 507, 2, 46, "Object"], [7, 513, 2, 46], [7, 514, 2, 46, "getOwnPropertyDescriptor"], [7, 538, 2, 46], [7, 539, 2, 46, "e"], [7, 540, 2, 46], [7, 542, 2, 46, "t"], [7, 543, 2, 46], [7, 550, 2, 46, "i"], [7, 551, 2, 46], [7, 552, 2, 46, "get"], [7, 555, 2, 46], [7, 559, 2, 46, "i"], [7, 560, 2, 46], [7, 561, 2, 46, "set"], [7, 564, 2, 46], [7, 568, 2, 46, "o"], [7, 569, 2, 46], [7, 570, 2, 46, "f"], [7, 571, 2, 46], [7, 573, 2, 46, "t"], [7, 574, 2, 46], [7, 576, 2, 46, "i"], [7, 577, 2, 46], [7, 581, 2, 46, "f"], [7, 582, 2, 46], [7, 583, 2, 46, "t"], [7, 584, 2, 46], [7, 588, 2, 46, "e"], [7, 589, 2, 46], [7, 590, 2, 46, "t"], [7, 591, 2, 46], [7, 602, 2, 46, "f"], [7, 603, 2, 46], [7, 608, 2, 46, "e"], [7, 609, 2, 46], [7, 611, 2, 46, "t"], [7, 612, 2, 46], [8, 2, 1, 0], [8, 11, 1, 9, "_extends"], [8, 19, 1, 17, "_extends"], [8, 20, 1, 17], [8, 22, 1, 20], [9, 4, 1, 22], [9, 11, 1, 29, "_extends"], [9, 19, 1, 37], [9, 22, 1, 40, "Object"], [9, 28, 1, 46], [9, 29, 1, 47, "assign"], [9, 35, 1, 53], [9, 38, 1, 56, "Object"], [9, 44, 1, 62], [9, 45, 1, 63, "assign"], [9, 51, 1, 69], [9, 52, 1, 70, "bind"], [9, 56, 1, 74], [9, 57, 1, 75], [9, 58, 1, 76], [9, 61, 1, 79], [9, 71, 1, 89, "n"], [9, 72, 1, 90], [9, 74, 1, 92], [10, 6, 1, 94], [10, 11, 1, 99], [10, 15, 1, 103, "e"], [10, 16, 1, 104], [10, 19, 1, 107], [10, 20, 1, 108], [10, 22, 1, 110, "e"], [10, 23, 1, 111], [10, 26, 1, 114, "arguments"], [10, 35, 1, 123], [10, 36, 1, 124, "length"], [10, 42, 1, 130], [10, 44, 1, 132, "e"], [10, 45, 1, 133], [10, 47, 1, 135], [10, 49, 1, 137], [11, 8, 1, 139], [11, 12, 1, 143, "t"], [11, 13, 1, 144], [11, 16, 1, 147, "arguments"], [11, 25, 1, 156], [11, 26, 1, 157, "e"], [11, 27, 1, 158], [11, 28, 1, 159], [12, 8, 1, 161], [12, 13, 1, 166], [12, 17, 1, 170, "r"], [12, 18, 1, 171], [12, 22, 1, 175, "t"], [12, 23, 1, 176], [12, 25, 1, 178], [12, 26, 1, 179], [12, 27, 1, 180], [12, 28, 1, 181], [12, 30, 1, 183, "hasOwnProperty"], [12, 44, 1, 197], [12, 45, 1, 198, "call"], [12, 49, 1, 202], [12, 50, 1, 203, "t"], [12, 51, 1, 204], [12, 53, 1, 206, "r"], [12, 54, 1, 207], [12, 55, 1, 208], [12, 60, 1, 213, "n"], [12, 61, 1, 214], [12, 62, 1, 215, "r"], [12, 63, 1, 216], [12, 64, 1, 217], [12, 67, 1, 220, "t"], [12, 68, 1, 221], [12, 69, 1, 222, "r"], [12, 70, 1, 223], [12, 71, 1, 224], [12, 72, 1, 225], [13, 6, 1, 227], [14, 6, 1, 229], [14, 13, 1, 236, "n"], [14, 14, 1, 237], [15, 4, 1, 239], [15, 5, 1, 240], [15, 7, 1, 242, "_extends"], [15, 15, 1, 250], [15, 16, 1, 251, "apply"], [15, 21, 1, 256], [15, 22, 1, 257], [15, 26, 1, 261], [15, 28, 1, 263, "arguments"], [15, 37, 1, 272], [15, 38, 1, 273], [16, 2, 1, 275], [17, 2, 3, 7], [17, 8, 3, 13, "Group"], [17, 13, 3, 18], [17, 16, 3, 21, "Group"], [17, 17, 3, 22], [18, 4, 4, 2, "layer"], [18, 9, 4, 7], [19, 4, 5, 2], [19, 7, 5, 5, "props"], [20, 2, 6, 0], [20, 3, 6, 1], [20, 8, 6, 6], [21, 4, 7, 2], [21, 8, 7, 6], [21, 21, 7, 19], [21, 39, 7, 19, "isValidElement"], [21, 60, 7, 33], [21, 62, 7, 34, "layer"], [21, 67, 7, 39], [21, 68, 7, 40], [21, 72, 7, 44], [21, 79, 7, 51, "layer"], [21, 84, 7, 56], [21, 89, 7, 61], [21, 97, 7, 69], [21, 99, 7, 71], [22, 6, 8, 4], [22, 13, 8, 11], [22, 26, 8, 24, "React"], [22, 40, 8, 29], [22, 41, 8, 30, "createElement"], [22, 54, 8, 43], [22, 55, 8, 44], [22, 64, 8, 53], [22, 66, 8, 55], [22, 70, 8, 59], [22, 72, 8, 61, "layer"], [22, 77, 8, 66], [22, 79, 8, 68], [22, 92, 8, 81, "React"], [22, 106, 8, 86], [22, 107, 8, 87, "createElement"], [22, 120, 8, 100], [22, 121, 8, 101], [22, 130, 8, 110], [22, 132, 8, 112, "props"], [22, 137, 8, 117], [22, 138, 8, 118], [22, 139, 8, 119], [23, 4, 9, 2], [24, 4, 10, 2], [24, 11, 10, 9], [24, 24, 10, 22, "React"], [24, 38, 10, 27], [24, 39, 10, 28, "createElement"], [24, 52, 10, 41], [24, 53, 10, 42], [24, 62, 10, 51], [24, 64, 10, 53, "_extends"], [24, 72, 10, 61], [24, 73, 10, 62], [25, 6, 11, 4, "layer"], [25, 11, 11, 9], [25, 13, 11, 11, "layer"], [26, 4, 12, 2], [26, 5, 12, 3], [26, 7, 12, 5, "props"], [26, 12, 12, 10], [26, 13, 12, 11], [26, 14, 12, 12], [27, 2, 13, 0], [27, 3, 13, 1], [28, 2, 13, 2, "exports"], [28, 9, 13, 2], [28, 10, 13, 2, "Group"], [28, 15, 13, 2], [28, 18, 13, 2, "Group"], [28, 23, 13, 2], [29, 0, 13, 2], [29, 3]], "functionMap": {"names": ["_extends", "<anonymous>", "<global>", "Group"], "mappings": "AAA,+EC,iKD,oCE;qBCE;CDU"}}, "type": "js/module"}]}