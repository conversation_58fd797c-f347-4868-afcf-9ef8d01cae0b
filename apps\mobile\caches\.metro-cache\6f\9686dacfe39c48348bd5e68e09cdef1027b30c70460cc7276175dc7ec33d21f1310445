{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 34, "index": 79}}], "key": "+q0qwmVtgReRJ1JJKJleyyIYxCs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useVideoLoading = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _skia = require(_dependencyMap[1], \"../../skia\");\n  const useVideoLoading = source => {\n    const [video, setVideo] = (0, _react.useState)(null);\n    (0, _react.useEffect)(() => {\n      if (source) {\n        const vid = _skia.Skia.Video(source);\n        vid.then(v => setVideo(v));\n      }\n    }, [source]);\n    return video;\n  };\n  exports.useVideoLoading = useVideoLoading;\n});", "lineCount": 19, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_skia"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "useVideoLoading"], [8, 23, 3, 28], [8, 26, 3, 31, "source"], [8, 32, 3, 37], [8, 36, 3, 41], [9, 4, 4, 2], [9, 10, 4, 8], [9, 11, 4, 9, "video"], [9, 16, 4, 14], [9, 18, 4, 16, "setVideo"], [9, 26, 4, 24], [9, 27, 4, 25], [9, 30, 4, 28], [9, 34, 4, 28, "useState"], [9, 49, 4, 36], [9, 51, 4, 37], [9, 55, 4, 41], [9, 56, 4, 42], [10, 4, 5, 2], [10, 8, 5, 2, "useEffect"], [10, 24, 5, 11], [10, 26, 5, 12], [10, 32, 5, 18], [11, 6, 6, 4], [11, 10, 6, 8, "source"], [11, 16, 6, 14], [11, 18, 6, 16], [12, 8, 7, 6], [12, 14, 7, 12, "vid"], [12, 17, 7, 15], [12, 20, 7, 18, "Skia"], [12, 30, 7, 22], [12, 31, 7, 23, "Video"], [12, 36, 7, 28], [12, 37, 7, 29, "source"], [12, 43, 7, 35], [12, 44, 7, 36], [13, 8, 8, 6, "vid"], [13, 11, 8, 9], [13, 12, 8, 10, "then"], [13, 16, 8, 14], [13, 17, 8, 15, "v"], [13, 18, 8, 16], [13, 22, 8, 20, "setVideo"], [13, 30, 8, 28], [13, 31, 8, 29, "v"], [13, 32, 8, 30], [13, 33, 8, 31], [13, 34, 8, 32], [14, 6, 9, 4], [15, 4, 10, 2], [15, 5, 10, 3], [15, 7, 10, 5], [15, 8, 10, 6, "source"], [15, 14, 10, 12], [15, 15, 10, 13], [15, 16, 10, 14], [16, 4, 11, 2], [16, 11, 11, 9, "video"], [16, 16, 11, 14], [17, 2, 12, 0], [17, 3, 12, 1], [18, 2, 12, 2, "exports"], [18, 9, 12, 2], [18, 10, 12, 2, "useVideoLoading"], [18, 25, 12, 2], [18, 28, 12, 2, "useVideoLoading"], [18, 43, 12, 2], [19, 0, 12, 2], [19, 3]], "functionMap": {"names": ["<global>", "useVideoLoading", "useEffect$argument_0", "vid.then$argument_0"], "mappings": "AAA;+BCE;YCE;eCG,gBD;GDE;CDE"}}, "type": "js/module"}]}