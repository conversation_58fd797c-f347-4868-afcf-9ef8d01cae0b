{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkContourMeasure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 639}, "end": {"line": 5, "column": 60, "index": 699}}], "key": "dc+vjUnxx//N7d7OcJE1sJRte6o=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkContourMeasureIter = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkContourMeasure = require(_dependencyMap[1], \"./JsiSkContourMeasure\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkContourMeasureIter extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"ContourMeasureIter\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    next() {\n      const result = this.ref.next();\n      if (result === null) {\n        return null;\n      }\n      return new _JsiSkContourMeasure.JsiSkContourMeasure(this.CanvasKit, result);\n    }\n  }\n  exports.JsiSkContourMeasureIter = JsiSkContourMeasureIter;\n});", "lineCount": 46, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkContourMeasure"], [7, 26, 5, 0], [7, 29, 5, 0, "require"], [7, 36, 5, 0], [7, 37, 5, 0, "_dependencyMap"], [7, 51, 5, 0], [8, 2, 1, 0], [8, 11, 1, 9, "_defineProperty"], [8, 26, 1, 24, "_defineProperty"], [8, 27, 1, 25, "e"], [8, 28, 1, 26], [8, 30, 1, 28, "r"], [8, 31, 1, 29], [8, 33, 1, 31, "t"], [8, 34, 1, 32], [8, 36, 1, 34], [9, 4, 1, 36], [9, 11, 1, 43], [9, 12, 1, 44, "r"], [9, 13, 1, 45], [9, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [9, 30, 1, 62], [9, 31, 1, 63, "r"], [9, 32, 1, 64], [9, 33, 1, 65], [9, 38, 1, 70, "e"], [9, 39, 1, 71], [9, 42, 1, 74, "Object"], [9, 48, 1, 80], [9, 49, 1, 81, "defineProperty"], [9, 63, 1, 95], [9, 64, 1, 96, "e"], [9, 65, 1, 97], [9, 67, 1, 99, "r"], [9, 68, 1, 100], [9, 70, 1, 102], [10, 6, 1, 104, "value"], [10, 11, 1, 109], [10, 13, 1, 111, "t"], [10, 14, 1, 112], [11, 6, 1, 114, "enumerable"], [11, 16, 1, 124], [11, 18, 1, 126], [11, 19, 1, 127], [11, 20, 1, 128], [12, 6, 1, 130, "configurable"], [12, 18, 1, 142], [12, 20, 1, 144], [12, 21, 1, 145], [12, 22, 1, 146], [13, 6, 1, 148, "writable"], [13, 14, 1, 156], [13, 16, 1, 158], [13, 17, 1, 159], [14, 4, 1, 161], [14, 5, 1, 162], [14, 6, 1, 163], [14, 9, 1, 166, "e"], [14, 10, 1, 167], [14, 11, 1, 168, "r"], [14, 12, 1, 169], [14, 13, 1, 170], [14, 16, 1, 173, "t"], [14, 17, 1, 174], [14, 19, 1, 176, "e"], [14, 20, 1, 177], [15, 2, 1, 179], [16, 2, 2, 0], [16, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 26, 2, 24, "t"], [16, 27, 2, 25], [16, 29, 2, 27], [17, 4, 2, 29], [17, 8, 2, 33, "i"], [17, 9, 2, 34], [17, 12, 2, 37, "_toPrimitive"], [17, 24, 2, 49], [17, 25, 2, 50, "t"], [17, 26, 2, 51], [17, 28, 2, 53], [17, 36, 2, 61], [17, 37, 2, 62], [18, 4, 2, 64], [18, 11, 2, 71], [18, 19, 2, 79], [18, 23, 2, 83], [18, 30, 2, 90, "i"], [18, 31, 2, 91], [18, 34, 2, 94, "i"], [18, 35, 2, 95], [18, 38, 2, 98, "i"], [18, 39, 2, 99], [18, 42, 2, 102], [18, 44, 2, 104], [19, 2, 2, 106], [20, 2, 3, 0], [20, 11, 3, 9, "_toPrimitive"], [20, 23, 3, 21, "_toPrimitive"], [20, 24, 3, 22, "t"], [20, 25, 3, 23], [20, 27, 3, 25, "r"], [20, 28, 3, 26], [20, 30, 3, 28], [21, 4, 3, 30], [21, 8, 3, 34], [21, 16, 3, 42], [21, 20, 3, 46], [21, 27, 3, 53, "t"], [21, 28, 3, 54], [21, 32, 3, 58], [21, 33, 3, 59, "t"], [21, 34, 3, 60], [21, 36, 3, 62], [21, 43, 3, 69, "t"], [21, 44, 3, 70], [22, 4, 3, 72], [22, 8, 3, 76, "e"], [22, 9, 3, 77], [22, 12, 3, 80, "t"], [22, 13, 3, 81], [22, 14, 3, 82, "Symbol"], [22, 20, 3, 88], [22, 21, 3, 89, "toPrimitive"], [22, 32, 3, 100], [22, 33, 3, 101], [23, 4, 3, 103], [23, 8, 3, 107], [23, 13, 3, 112], [23, 14, 3, 113], [23, 19, 3, 118, "e"], [23, 20, 3, 119], [23, 22, 3, 121], [24, 6, 3, 123], [24, 10, 3, 127, "i"], [24, 11, 3, 128], [24, 14, 3, 131, "e"], [24, 15, 3, 132], [24, 16, 3, 133, "call"], [24, 20, 3, 137], [24, 21, 3, 138, "t"], [24, 22, 3, 139], [24, 24, 3, 141, "r"], [24, 25, 3, 142], [24, 29, 3, 146], [24, 38, 3, 155], [24, 39, 3, 156], [25, 6, 3, 158], [25, 10, 3, 162], [25, 18, 3, 170], [25, 22, 3, 174], [25, 29, 3, 181, "i"], [25, 30, 3, 182], [25, 32, 3, 184], [25, 39, 3, 191, "i"], [25, 40, 3, 192], [26, 6, 3, 194], [26, 12, 3, 200], [26, 16, 3, 204, "TypeError"], [26, 25, 3, 213], [26, 26, 3, 214], [26, 72, 3, 260], [26, 73, 3, 261], [27, 4, 3, 263], [28, 4, 3, 265], [28, 11, 3, 272], [28, 12, 3, 273], [28, 20, 3, 281], [28, 25, 3, 286, "r"], [28, 26, 3, 287], [28, 29, 3, 290, "String"], [28, 35, 3, 296], [28, 38, 3, 299, "Number"], [28, 44, 3, 305], [28, 46, 3, 307, "t"], [28, 47, 3, 308], [28, 48, 3, 309], [29, 2, 3, 311], [30, 2, 6, 7], [30, 8, 6, 13, "JsiSkContourMeasureIter"], [30, 31, 6, 36], [30, 40, 6, 45, "HostObject"], [30, 56, 6, 55], [30, 57, 6, 56], [31, 4, 7, 2, "constructor"], [31, 15, 7, 13, "constructor"], [31, 16, 7, 14, "CanvasKit"], [31, 25, 7, 23], [31, 27, 7, 25, "ref"], [31, 30, 7, 28], [31, 32, 7, 30], [32, 6, 8, 4], [32, 11, 8, 9], [32, 12, 8, 10, "CanvasKit"], [32, 21, 8, 19], [32, 23, 8, 21, "ref"], [32, 26, 8, 24], [32, 28, 8, 26], [32, 48, 8, 46], [32, 49, 8, 47], [33, 6, 9, 4, "_defineProperty"], [33, 21, 9, 19], [33, 22, 9, 20], [33, 26, 9, 24], [33, 28, 9, 26], [33, 37, 9, 35], [33, 39, 9, 37], [33, 45, 9, 43], [34, 8, 10, 6], [34, 12, 10, 10], [34, 13, 10, 11, "ref"], [34, 16, 10, 14], [34, 17, 10, 15, "delete"], [34, 23, 10, 21], [34, 24, 10, 22], [34, 25, 10, 23], [35, 6, 11, 4], [35, 7, 11, 5], [35, 8, 11, 6], [36, 4, 12, 2], [37, 4, 13, 2, "next"], [37, 8, 13, 6, "next"], [37, 9, 13, 6], [37, 11, 13, 9], [38, 6, 14, 4], [38, 12, 14, 10, "result"], [38, 18, 14, 16], [38, 21, 14, 19], [38, 25, 14, 23], [38, 26, 14, 24, "ref"], [38, 29, 14, 27], [38, 30, 14, 28, "next"], [38, 34, 14, 32], [38, 35, 14, 33], [38, 36, 14, 34], [39, 6, 15, 4], [39, 10, 15, 8, "result"], [39, 16, 15, 14], [39, 21, 15, 19], [39, 25, 15, 23], [39, 27, 15, 25], [40, 8, 16, 6], [40, 15, 16, 13], [40, 19, 16, 17], [41, 6, 17, 4], [42, 6, 18, 4], [42, 13, 18, 11], [42, 17, 18, 15, "JsiSkContourMeasure"], [42, 57, 18, 34], [42, 58, 18, 35], [42, 62, 18, 39], [42, 63, 18, 40, "CanvasKit"], [42, 72, 18, 49], [42, 74, 18, 51, "result"], [42, 80, 18, 57], [42, 81, 18, 58], [43, 4, 19, 2], [44, 2, 20, 0], [45, 2, 20, 1, "exports"], [45, 9, 20, 1], [45, 10, 20, 1, "JsiSkContourMeasureIter"], [45, 33, 20, 1], [45, 36, 20, 1, "JsiSkContourMeasureIter"], [45, 59, 20, 1], [46, 0, 20, 1], [46, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkContourMeasureIter", "constructor", "_defineProperty$argument_2", "next"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGG;ECC;qCCE;KDE;GDC;EGC;GHM;CHC"}}, "type": "js/module"}]}