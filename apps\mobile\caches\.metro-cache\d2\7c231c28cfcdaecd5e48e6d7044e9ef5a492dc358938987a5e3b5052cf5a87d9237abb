{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkMatrix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 639}, "end": {"line": 5, "column": 44, "index": 683}}], "key": "aOVfjZgmz4R2ci39pV6HZujK8og=", "exportNames": ["*"]}}, {"name": "./JsiSkShader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 684}, "end": {"line": 6, "column": 44, "index": 728}}], "key": "qmH0e2X2qhdhK7DOZEHi4mFPbz8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkRuntimeEffect = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkMatrix = require(_dependencyMap[1], \"./JsiSkMatrix\");\n  var _JsiSkShader = require(_dependencyMap[2], \"./JsiSkShader\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkRuntimeEffect extends _Host.HostObject {\n    constructor(CanvasKit, ref, sksl) {\n      super(CanvasKit, ref, \"RuntimeEffect\");\n      this.sksl = sksl;\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    source() {\n      return this.sksl;\n    }\n    makeShader(uniforms, localMatrix) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.ref.makeShader(uniforms, localMatrix !== undefined ? _JsiSkMatrix.JsiSkMatrix.fromValue(localMatrix) : localMatrix));\n    }\n    makeShaderWithChildren(uniforms, children, localMatrix) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.ref.makeShaderWithChildren(uniforms, children === null || children === void 0 ? void 0 : children.map(child => _JsiSkShader.JsiSkShader.fromValue(child)), localMatrix !== undefined ? _JsiSkMatrix.JsiSkMatrix.fromValue(localMatrix) : localMatrix));\n    }\n    getUniform(index) {\n      return this.ref.getUniform(index);\n    }\n    getUniformCount() {\n      return this.ref.getUniformCount();\n    }\n    getUniformFloatCount() {\n      return this.ref.getUniformFloatCount();\n    }\n    getUniformName(index) {\n      return this.ref.getUniformName(index);\n    }\n  }\n  exports.JsiSkRuntimeEffect = JsiSkRuntimeEffect;\n});", "lineCount": 62, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkMatrix"], [7, 18, 5, 0], [7, 21, 5, 0, "require"], [7, 28, 5, 0], [7, 29, 5, 0, "_dependencyMap"], [7, 43, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkShader"], [8, 18, 6, 0], [8, 21, 6, 0, "require"], [8, 28, 6, 0], [8, 29, 6, 0, "_dependencyMap"], [8, 43, 6, 0], [9, 2, 1, 0], [9, 11, 1, 9, "_defineProperty"], [9, 26, 1, 24, "_defineProperty"], [9, 27, 1, 25, "e"], [9, 28, 1, 26], [9, 30, 1, 28, "r"], [9, 31, 1, 29], [9, 33, 1, 31, "t"], [9, 34, 1, 32], [9, 36, 1, 34], [10, 4, 1, 36], [10, 11, 1, 43], [10, 12, 1, 44, "r"], [10, 13, 1, 45], [10, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 30, 1, 62], [10, 31, 1, 63, "r"], [10, 32, 1, 64], [10, 33, 1, 65], [10, 38, 1, 70, "e"], [10, 39, 1, 71], [10, 42, 1, 74, "Object"], [10, 48, 1, 80], [10, 49, 1, 81, "defineProperty"], [10, 63, 1, 95], [10, 64, 1, 96, "e"], [10, 65, 1, 97], [10, 67, 1, 99, "r"], [10, 68, 1, 100], [10, 70, 1, 102], [11, 6, 1, 104, "value"], [11, 11, 1, 109], [11, 13, 1, 111, "t"], [11, 14, 1, 112], [12, 6, 1, 114, "enumerable"], [12, 16, 1, 124], [12, 18, 1, 126], [12, 19, 1, 127], [12, 20, 1, 128], [13, 6, 1, 130, "configurable"], [13, 18, 1, 142], [13, 20, 1, 144], [13, 21, 1, 145], [13, 22, 1, 146], [14, 6, 1, 148, "writable"], [14, 14, 1, 156], [14, 16, 1, 158], [14, 17, 1, 159], [15, 4, 1, 161], [15, 5, 1, 162], [15, 6, 1, 163], [15, 9, 1, 166, "e"], [15, 10, 1, 167], [15, 11, 1, 168, "r"], [15, 12, 1, 169], [15, 13, 1, 170], [15, 16, 1, 173, "t"], [15, 17, 1, 174], [15, 19, 1, 176, "e"], [15, 20, 1, 177], [16, 2, 1, 179], [17, 2, 2, 0], [17, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [17, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [17, 26, 2, 24, "t"], [17, 27, 2, 25], [17, 29, 2, 27], [18, 4, 2, 29], [18, 8, 2, 33, "i"], [18, 9, 2, 34], [18, 12, 2, 37, "_toPrimitive"], [18, 24, 2, 49], [18, 25, 2, 50, "t"], [18, 26, 2, 51], [18, 28, 2, 53], [18, 36, 2, 61], [18, 37, 2, 62], [19, 4, 2, 64], [19, 11, 2, 71], [19, 19, 2, 79], [19, 23, 2, 83], [19, 30, 2, 90, "i"], [19, 31, 2, 91], [19, 34, 2, 94, "i"], [19, 35, 2, 95], [19, 38, 2, 98, "i"], [19, 39, 2, 99], [19, 42, 2, 102], [19, 44, 2, 104], [20, 2, 2, 106], [21, 2, 3, 0], [21, 11, 3, 9, "_toPrimitive"], [21, 23, 3, 21, "_toPrimitive"], [21, 24, 3, 22, "t"], [21, 25, 3, 23], [21, 27, 3, 25, "r"], [21, 28, 3, 26], [21, 30, 3, 28], [22, 4, 3, 30], [22, 8, 3, 34], [22, 16, 3, 42], [22, 20, 3, 46], [22, 27, 3, 53, "t"], [22, 28, 3, 54], [22, 32, 3, 58], [22, 33, 3, 59, "t"], [22, 34, 3, 60], [22, 36, 3, 62], [22, 43, 3, 69, "t"], [22, 44, 3, 70], [23, 4, 3, 72], [23, 8, 3, 76, "e"], [23, 9, 3, 77], [23, 12, 3, 80, "t"], [23, 13, 3, 81], [23, 14, 3, 82, "Symbol"], [23, 20, 3, 88], [23, 21, 3, 89, "toPrimitive"], [23, 32, 3, 100], [23, 33, 3, 101], [24, 4, 3, 103], [24, 8, 3, 107], [24, 13, 3, 112], [24, 14, 3, 113], [24, 19, 3, 118, "e"], [24, 20, 3, 119], [24, 22, 3, 121], [25, 6, 3, 123], [25, 10, 3, 127, "i"], [25, 11, 3, 128], [25, 14, 3, 131, "e"], [25, 15, 3, 132], [25, 16, 3, 133, "call"], [25, 20, 3, 137], [25, 21, 3, 138, "t"], [25, 22, 3, 139], [25, 24, 3, 141, "r"], [25, 25, 3, 142], [25, 29, 3, 146], [25, 38, 3, 155], [25, 39, 3, 156], [26, 6, 3, 158], [26, 10, 3, 162], [26, 18, 3, 170], [26, 22, 3, 174], [26, 29, 3, 181, "i"], [26, 30, 3, 182], [26, 32, 3, 184], [26, 39, 3, 191, "i"], [26, 40, 3, 192], [27, 6, 3, 194], [27, 12, 3, 200], [27, 16, 3, 204, "TypeError"], [27, 25, 3, 213], [27, 26, 3, 214], [27, 72, 3, 260], [27, 73, 3, 261], [28, 4, 3, 263], [29, 4, 3, 265], [29, 11, 3, 272], [29, 12, 3, 273], [29, 20, 3, 281], [29, 25, 3, 286, "r"], [29, 26, 3, 287], [29, 29, 3, 290, "String"], [29, 35, 3, 296], [29, 38, 3, 299, "Number"], [29, 44, 3, 305], [29, 46, 3, 307, "t"], [29, 47, 3, 308], [29, 48, 3, 309], [30, 2, 3, 311], [31, 2, 7, 7], [31, 8, 7, 13, "JsiSkRuntimeEffect"], [31, 26, 7, 31], [31, 35, 7, 40, "HostObject"], [31, 51, 7, 50], [31, 52, 7, 51], [32, 4, 8, 2, "constructor"], [32, 15, 8, 13, "constructor"], [32, 16, 8, 14, "CanvasKit"], [32, 25, 8, 23], [32, 27, 8, 25, "ref"], [32, 30, 8, 28], [32, 32, 8, 30, "sksl"], [32, 36, 8, 34], [32, 38, 8, 36], [33, 6, 9, 4], [33, 11, 9, 9], [33, 12, 9, 10, "CanvasKit"], [33, 21, 9, 19], [33, 23, 9, 21, "ref"], [33, 26, 9, 24], [33, 28, 9, 26], [33, 43, 9, 41], [33, 44, 9, 42], [34, 6, 10, 4], [34, 10, 10, 8], [34, 11, 10, 9, "sksl"], [34, 15, 10, 13], [34, 18, 10, 16, "sksl"], [34, 22, 10, 20], [35, 6, 11, 4, "_defineProperty"], [35, 21, 11, 19], [35, 22, 11, 20], [35, 26, 11, 24], [35, 28, 11, 26], [35, 37, 11, 35], [35, 39, 11, 37], [35, 45, 11, 43], [36, 8, 12, 6], [36, 12, 12, 10], [36, 13, 12, 11, "ref"], [36, 16, 12, 14], [36, 17, 12, 15, "delete"], [36, 23, 12, 21], [36, 24, 12, 22], [36, 25, 12, 23], [37, 6, 13, 4], [37, 7, 13, 5], [37, 8, 13, 6], [38, 4, 14, 2], [39, 4, 15, 2, "source"], [39, 10, 15, 8, "source"], [39, 11, 15, 8], [39, 13, 15, 11], [40, 6, 16, 4], [40, 13, 16, 11], [40, 17, 16, 15], [40, 18, 16, 16, "sksl"], [40, 22, 16, 20], [41, 4, 17, 2], [42, 4, 18, 2, "<PERSON><PERSON><PERSON><PERSON>"], [42, 14, 18, 12, "<PERSON><PERSON><PERSON><PERSON>"], [42, 15, 18, 13, "uniforms"], [42, 23, 18, 21], [42, 25, 18, 23, "localMatrix"], [42, 36, 18, 34], [42, 38, 18, 36], [43, 6, 19, 4], [43, 13, 19, 11], [43, 17, 19, 15, "JsiSkShader"], [43, 41, 19, 26], [43, 42, 19, 27], [43, 46, 19, 31], [43, 47, 19, 32, "CanvasKit"], [43, 56, 19, 41], [43, 58, 19, 43], [43, 62, 19, 47], [43, 63, 19, 48, "ref"], [43, 66, 19, 51], [43, 67, 19, 52, "<PERSON><PERSON><PERSON><PERSON>"], [43, 77, 19, 62], [43, 78, 19, 63, "uniforms"], [43, 86, 19, 71], [43, 88, 19, 73, "localMatrix"], [43, 99, 19, 84], [43, 104, 19, 89, "undefined"], [43, 113, 19, 98], [43, 116, 19, 101, "JsiSkMatrix"], [43, 140, 19, 112], [43, 141, 19, 113, "fromValue"], [43, 150, 19, 122], [43, 151, 19, 123, "localMatrix"], [43, 162, 19, 134], [43, 163, 19, 135], [43, 166, 19, 138, "localMatrix"], [43, 177, 19, 149], [43, 178, 19, 150], [43, 179, 19, 151], [44, 4, 20, 2], [45, 4, 21, 2, "<PERSON><PERSON><PERSON><PERSON>With<PERSON><PERSON><PERSON>n"], [45, 26, 21, 24, "<PERSON><PERSON><PERSON><PERSON>With<PERSON><PERSON><PERSON>n"], [45, 27, 21, 25, "uniforms"], [45, 35, 21, 33], [45, 37, 21, 35, "children"], [45, 45, 21, 43], [45, 47, 21, 45, "localMatrix"], [45, 58, 21, 56], [45, 60, 21, 58], [46, 6, 22, 4], [46, 13, 22, 11], [46, 17, 22, 15, "JsiSkShader"], [46, 41, 22, 26], [46, 42, 22, 27], [46, 46, 22, 31], [46, 47, 22, 32, "CanvasKit"], [46, 56, 22, 41], [46, 58, 22, 43], [46, 62, 22, 47], [46, 63, 22, 48, "ref"], [46, 66, 22, 51], [46, 67, 22, 52, "<PERSON><PERSON><PERSON><PERSON>With<PERSON><PERSON><PERSON>n"], [46, 89, 22, 74], [46, 90, 22, 75, "uniforms"], [46, 98, 22, 83], [46, 100, 22, 85, "children"], [46, 108, 22, 93], [46, 113, 22, 98], [46, 117, 22, 102], [46, 121, 22, 106, "children"], [46, 129, 22, 114], [46, 134, 22, 119], [46, 139, 22, 124], [46, 140, 22, 125], [46, 143, 22, 128], [46, 148, 22, 133], [46, 149, 22, 134], [46, 152, 22, 137, "children"], [46, 160, 22, 145], [46, 161, 22, 146, "map"], [46, 164, 22, 149], [46, 165, 22, 150, "child"], [46, 170, 22, 155], [46, 174, 22, 159, "JsiSkShader"], [46, 198, 22, 170], [46, 199, 22, 171, "fromValue"], [46, 208, 22, 180], [46, 209, 22, 181, "child"], [46, 214, 22, 186], [46, 215, 22, 187], [46, 216, 22, 188], [46, 218, 22, 190, "localMatrix"], [46, 229, 22, 201], [46, 234, 22, 206, "undefined"], [46, 243, 22, 215], [46, 246, 22, 218, "JsiSkMatrix"], [46, 270, 22, 229], [46, 271, 22, 230, "fromValue"], [46, 280, 22, 239], [46, 281, 22, 240, "localMatrix"], [46, 292, 22, 251], [46, 293, 22, 252], [46, 296, 22, 255, "localMatrix"], [46, 307, 22, 266], [46, 308, 22, 267], [46, 309, 22, 268], [47, 4, 23, 2], [48, 4, 24, 2, "getUniform"], [48, 14, 24, 12, "getUniform"], [48, 15, 24, 13, "index"], [48, 20, 24, 18], [48, 22, 24, 20], [49, 6, 25, 4], [49, 13, 25, 11], [49, 17, 25, 15], [49, 18, 25, 16, "ref"], [49, 21, 25, 19], [49, 22, 25, 20, "getUniform"], [49, 32, 25, 30], [49, 33, 25, 31, "index"], [49, 38, 25, 36], [49, 39, 25, 37], [50, 4, 26, 2], [51, 4, 27, 2, "getUniformCount"], [51, 19, 27, 17, "getUniformCount"], [51, 20, 27, 17], [51, 22, 27, 20], [52, 6, 28, 4], [52, 13, 28, 11], [52, 17, 28, 15], [52, 18, 28, 16, "ref"], [52, 21, 28, 19], [52, 22, 28, 20, "getUniformCount"], [52, 37, 28, 35], [52, 38, 28, 36], [52, 39, 28, 37], [53, 4, 29, 2], [54, 4, 30, 2, "getUniformFloatCount"], [54, 24, 30, 22, "getUniformFloatCount"], [54, 25, 30, 22], [54, 27, 30, 25], [55, 6, 31, 4], [55, 13, 31, 11], [55, 17, 31, 15], [55, 18, 31, 16, "ref"], [55, 21, 31, 19], [55, 22, 31, 20, "getUniformFloatCount"], [55, 42, 31, 40], [55, 43, 31, 41], [55, 44, 31, 42], [56, 4, 32, 2], [57, 4, 33, 2, "getUniformName"], [57, 18, 33, 16, "getUniformName"], [57, 19, 33, 17, "index"], [57, 24, 33, 22], [57, 26, 33, 24], [58, 6, 34, 4], [58, 13, 34, 11], [58, 17, 34, 15], [58, 18, 34, 16, "ref"], [58, 21, 34, 19], [58, 22, 34, 20, "getUniformName"], [58, 36, 34, 34], [58, 37, 34, 35, "index"], [58, 42, 34, 40], [58, 43, 34, 41], [59, 4, 35, 2], [60, 2, 36, 0], [61, 2, 36, 1, "exports"], [61, 9, 36, 1], [61, 10, 36, 1, "JsiSkRuntimeEffect"], [61, 28, 36, 1], [61, 31, 36, 1, "JsiSkRuntimeEffect"], [61, 49, 36, 1], [62, 0, 36, 1], [62, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkRuntimeEffect", "constructor", "_defineProperty$argument_2", "source", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>With<PERSON><PERSON><PERSON>n", "children.map$argument_0", "getUniform", "getUniformCount", "getUniformFloatCount", "getUniformName"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGI;ECC;qCCG;KDE;GDC;EGC;GHE;EIC;GJE;EKC;sJCC,qCD;GLC;EOC;GPE;EQC;GRE;ESC;GTE;EUC;GVE;CHC"}}, "type": "js/module"}]}