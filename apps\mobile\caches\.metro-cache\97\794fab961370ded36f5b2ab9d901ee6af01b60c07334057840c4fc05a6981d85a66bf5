{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        await processImageWithFaceBlur(photo.uri);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] ❌ face-api.js also failed:', faceApiError);\n\n            // Method 3: Fallback - Mock face detection for testing (assumes center face)\n            console.log('[EchoCameraWeb] 🧪 Using fallback mock face detection for testing...');\n            detectedFaces = [{\n              boundingBox: {\n                xCenter: 0.5,\n                // Center of image\n                yCenter: 0.4,\n                // Slightly above center (typical face position)\n                width: 0.3,\n                // 30% of image width\n                height: 0.4 // 40% of image height\n              }\n            }];\n            console.log(`[EchoCameraWeb] 🧪 Mock detection created 1 face at center of image`);\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(12, Math.min(paddedWidth, paddedHeight) / 15); // Increased pixel size for more visible effect\n            console.log(`[EchoCameraWeb] 🔲 Using pixel size: ${pixelSize}px for blurring`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} blurring applied successfully`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] Processing complete:', result);\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 688,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1344, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 53, 128, 51], [126, 55, 128, 53, "photo"], [126, 60, 128, 58], [126, 61, 128, 59, "uri"], [126, 64, 128, 62], [126, 65, 128, 63], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6], [130, 14, 132, 12, "processImageWithFaceBlur"], [130, 38, 132, 36], [130, 39, 132, 37, "photo"], [130, 44, 132, 42], [130, 45, 132, 43, "uri"], [130, 48, 132, 46], [130, 49, 132, 47], [131, 6, 133, 4], [131, 7, 133, 5], [131, 8, 133, 6], [131, 15, 133, 13, "error"], [131, 20, 133, 18], [131, 22, 133, 20], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "error"], [132, 21, 134, 19], [132, 22, 134, 20], [132, 54, 134, 52], [132, 56, 134, 54, "error"], [132, 61, 134, 59], [132, 62, 134, 60], [133, 8, 135, 6, "setErrorMessage"], [133, 23, 135, 21], [133, 24, 135, 22], [133, 68, 135, 66], [133, 69, 135, 67], [134, 8, 136, 6, "setProcessingState"], [134, 26, 136, 24], [134, 27, 136, 25], [134, 34, 136, 32], [134, 35, 136, 33], [135, 6, 137, 4], [136, 4, 138, 2], [136, 5, 138, 3], [136, 7, 138, 5], [136, 9, 138, 7], [136, 10, 138, 8], [137, 4, 139, 2], [138, 4, 140, 2], [138, 10, 140, 8, "processImageWithFaceBlur"], [138, 34, 140, 32], [138, 37, 140, 35], [138, 43, 140, 42, "photoUri"], [138, 51, 140, 58], [138, 55, 140, 63], [139, 6, 141, 4], [139, 10, 141, 8], [140, 8, 142, 6, "setProcessingState"], [140, 26, 142, 24], [140, 27, 142, 25], [140, 39, 142, 37], [140, 40, 142, 38], [141, 8, 143, 6, "setProcessingProgress"], [141, 29, 143, 27], [141, 30, 143, 28], [141, 32, 143, 30], [141, 33, 143, 31], [143, 8, 145, 6], [144, 8, 146, 6], [144, 14, 146, 12, "canvas"], [144, 20, 146, 18], [144, 23, 146, 21, "document"], [144, 31, 146, 29], [144, 32, 146, 30, "createElement"], [144, 45, 146, 43], [144, 46, 146, 44], [144, 54, 146, 52], [144, 55, 146, 53], [145, 8, 147, 6], [145, 14, 147, 12, "ctx"], [145, 17, 147, 15], [145, 20, 147, 18, "canvas"], [145, 26, 147, 24], [145, 27, 147, 25, "getContext"], [145, 37, 147, 35], [145, 38, 147, 36], [145, 42, 147, 40], [145, 43, 147, 41], [146, 8, 148, 6], [146, 12, 148, 10], [146, 13, 148, 11, "ctx"], [146, 16, 148, 14], [146, 18, 148, 16], [146, 24, 148, 22], [146, 28, 148, 26, "Error"], [146, 33, 148, 31], [146, 34, 148, 32], [146, 64, 148, 62], [146, 65, 148, 63], [148, 8, 150, 6], [149, 8, 151, 6], [149, 14, 151, 12, "img"], [149, 17, 151, 15], [149, 20, 151, 18], [149, 24, 151, 22, "Image"], [149, 29, 151, 27], [149, 30, 151, 28], [149, 31, 151, 29], [150, 8, 152, 6], [150, 14, 152, 12], [150, 18, 152, 16, "Promise"], [150, 25, 152, 23], [150, 26, 152, 24], [150, 27, 152, 25, "resolve"], [150, 34, 152, 32], [150, 36, 152, 34, "reject"], [150, 42, 152, 40], [150, 47, 152, 45], [151, 10, 153, 8, "img"], [151, 13, 153, 11], [151, 14, 153, 12, "onload"], [151, 20, 153, 18], [151, 23, 153, 21, "resolve"], [151, 30, 153, 28], [152, 10, 154, 8, "img"], [152, 13, 154, 11], [152, 14, 154, 12, "onerror"], [152, 21, 154, 19], [152, 24, 154, 22, "reject"], [152, 30, 154, 28], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "src"], [153, 17, 155, 15], [153, 20, 155, 18, "photoUri"], [153, 28, 155, 26], [154, 8, 156, 6], [154, 9, 156, 7], [154, 10, 156, 8], [156, 8, 158, 6], [157, 8, 159, 6, "canvas"], [157, 14, 159, 12], [157, 15, 159, 13, "width"], [157, 20, 159, 18], [157, 23, 159, 21, "img"], [157, 26, 159, 24], [157, 27, 159, 25, "width"], [157, 32, 159, 30], [158, 8, 160, 6, "canvas"], [158, 14, 160, 12], [158, 15, 160, 13, "height"], [158, 21, 160, 19], [158, 24, 160, 22, "img"], [158, 27, 160, 25], [158, 28, 160, 26, "height"], [158, 34, 160, 32], [160, 8, 162, 6], [161, 8, 163, 6, "ctx"], [161, 11, 163, 9], [161, 12, 163, 10, "drawImage"], [161, 21, 163, 19], [161, 22, 163, 20, "img"], [161, 25, 163, 23], [161, 27, 163, 25], [161, 28, 163, 26], [161, 30, 163, 28], [161, 31, 163, 29], [161, 32, 163, 30], [162, 8, 165, 6, "setProcessingProgress"], [162, 29, 165, 27], [162, 30, 165, 28], [162, 32, 165, 30], [162, 33, 165, 31], [164, 8, 167, 6], [165, 8, 168, 6], [165, 12, 168, 10, "detectedFaces"], [165, 25, 168, 23], [165, 28, 168, 26], [165, 30, 168, 28], [166, 8, 170, 6, "console"], [166, 15, 170, 13], [166, 16, 170, 14, "log"], [166, 19, 170, 17], [166, 20, 170, 18], [166, 74, 170, 72], [166, 76, 170, 74], [167, 10, 171, 8, "width"], [167, 15, 171, 13], [167, 17, 171, 15, "img"], [167, 20, 171, 18], [167, 21, 171, 19, "width"], [167, 26, 171, 24], [168, 10, 172, 8, "height"], [168, 16, 172, 14], [168, 18, 172, 16, "img"], [168, 21, 172, 19], [168, 22, 172, 20, "height"], [168, 28, 172, 26], [169, 10, 173, 8, "src"], [169, 13, 173, 11], [169, 15, 173, 13, "photoUri"], [169, 23, 173, 21], [169, 24, 173, 22, "substring"], [169, 33, 173, 31], [169, 34, 173, 32], [169, 35, 173, 33], [169, 37, 173, 35], [169, 39, 173, 37], [169, 40, 173, 38], [169, 43, 173, 41], [170, 8, 174, 6], [170, 9, 174, 7], [170, 10, 174, 8], [172, 8, 176, 6], [173, 8, 177, 6], [173, 12, 177, 10], [174, 10, 178, 8], [174, 14, 178, 12], [174, 28, 178, 26], [174, 32, 178, 30, "window"], [174, 38, 178, 36], [174, 40, 178, 38], [175, 12, 179, 10, "console"], [175, 19, 179, 17], [175, 20, 179, 18, "log"], [175, 23, 179, 21], [175, 24, 179, 22], [175, 105, 179, 103], [175, 106, 179, 104], [176, 12, 180, 10], [176, 18, 180, 16, "faceDetector"], [176, 30, 180, 28], [176, 33, 180, 31], [176, 37, 180, 36, "window"], [176, 43, 180, 42], [176, 44, 180, 51, "FaceDetector"], [176, 56, 180, 63], [176, 57, 180, 64], [177, 14, 181, 12, "maxDetectedFaces"], [177, 30, 181, 28], [177, 32, 181, 30], [177, 34, 181, 32], [178, 14, 182, 12, "fastMode"], [178, 22, 182, 20], [178, 24, 182, 22], [179, 12, 183, 10], [179, 13, 183, 11], [179, 14, 183, 12], [180, 12, 185, 10], [180, 18, 185, 16, "browserDetections"], [180, 35, 185, 33], [180, 38, 185, 36], [180, 44, 185, 42, "faceDetector"], [180, 56, 185, 54], [180, 57, 185, 55, "detect"], [180, 63, 185, 61], [180, 64, 185, 62, "img"], [180, 67, 185, 65], [180, 68, 185, 66], [181, 12, 186, 10, "detectedFaces"], [181, 25, 186, 23], [181, 28, 186, 26, "browserDetections"], [181, 45, 186, 43], [181, 46, 186, 44, "map"], [181, 49, 186, 47], [181, 50, 186, 49, "detection"], [181, 59, 186, 63], [181, 64, 186, 69], [182, 14, 187, 12, "boundingBox"], [182, 25, 187, 23], [182, 27, 187, 25], [183, 16, 188, 14, "xCenter"], [183, 23, 188, 21], [183, 25, 188, 23], [183, 26, 188, 24, "detection"], [183, 35, 188, 33], [183, 36, 188, 34, "boundingBox"], [183, 47, 188, 45], [183, 48, 188, 46, "x"], [183, 49, 188, 47], [183, 52, 188, 50, "detection"], [183, 61, 188, 59], [183, 62, 188, 60, "boundingBox"], [183, 73, 188, 71], [183, 74, 188, 72, "width"], [183, 79, 188, 77], [183, 82, 188, 80], [183, 83, 188, 81], [183, 87, 188, 85, "img"], [183, 90, 188, 88], [183, 91, 188, 89, "width"], [183, 96, 188, 94], [184, 16, 189, 14, "yCenter"], [184, 23, 189, 21], [184, 25, 189, 23], [184, 26, 189, 24, "detection"], [184, 35, 189, 33], [184, 36, 189, 34, "boundingBox"], [184, 47, 189, 45], [184, 48, 189, 46, "y"], [184, 49, 189, 47], [184, 52, 189, 50, "detection"], [184, 61, 189, 59], [184, 62, 189, 60, "boundingBox"], [184, 73, 189, 71], [184, 74, 189, 72, "height"], [184, 80, 189, 78], [184, 83, 189, 81], [184, 84, 189, 82], [184, 88, 189, 86, "img"], [184, 91, 189, 89], [184, 92, 189, 90, "height"], [184, 98, 189, 96], [185, 16, 190, 14, "width"], [185, 21, 190, 19], [185, 23, 190, 21, "detection"], [185, 32, 190, 30], [185, 33, 190, 31, "boundingBox"], [185, 44, 190, 42], [185, 45, 190, 43, "width"], [185, 50, 190, 48], [185, 53, 190, 51, "img"], [185, 56, 190, 54], [185, 57, 190, 55, "width"], [185, 62, 190, 60], [186, 16, 191, 14, "height"], [186, 22, 191, 20], [186, 24, 191, 22, "detection"], [186, 33, 191, 31], [186, 34, 191, 32, "boundingBox"], [186, 45, 191, 43], [186, 46, 191, 44, "height"], [186, 52, 191, 50], [186, 55, 191, 53, "img"], [186, 58, 191, 56], [186, 59, 191, 57, "height"], [187, 14, 192, 12], [188, 12, 193, 10], [188, 13, 193, 11], [188, 14, 193, 12], [188, 15, 193, 13], [189, 12, 194, 10, "console"], [189, 19, 194, 17], [189, 20, 194, 18, "log"], [189, 23, 194, 21], [189, 24, 194, 22], [189, 78, 194, 76, "detectedFaces"], [189, 91, 194, 89], [189, 92, 194, 90, "length"], [189, 98, 194, 96], [189, 106, 194, 104], [189, 107, 194, 105], [190, 10, 195, 8], [190, 11, 195, 9], [190, 17, 195, 15], [191, 12, 196, 10, "console"], [191, 19, 196, 17], [191, 20, 196, 18, "log"], [191, 23, 196, 21], [191, 24, 196, 22], [191, 100, 196, 98], [191, 101, 196, 99], [192, 12, 197, 10], [192, 18, 197, 16], [192, 22, 197, 20, "Error"], [192, 27, 197, 25], [192, 28, 197, 26], [192, 70, 197, 68], [192, 71, 197, 69], [193, 10, 198, 8], [194, 8, 199, 6], [194, 9, 199, 7], [194, 10, 199, 8], [194, 17, 199, 15, "browserError"], [194, 29, 199, 27], [194, 31, 199, 29], [195, 10, 200, 8, "console"], [195, 17, 200, 15], [195, 18, 200, 16, "warn"], [195, 22, 200, 20], [195, 23, 200, 21], [195, 102, 200, 100], [195, 104, 200, 102, "browserError"], [195, 116, 200, 114], [195, 117, 200, 115], [197, 10, 202, 8], [198, 10, 203, 8], [198, 14, 203, 12], [199, 12, 204, 10], [200, 12, 205, 10], [200, 16, 205, 14], [200, 17, 205, 16, "window"], [200, 23, 205, 22], [200, 24, 205, 31, "<PERSON>ap<PERSON>"], [200, 31, 205, 38], [200, 33, 205, 40], [201, 14, 206, 12], [201, 20, 206, 18], [201, 24, 206, 22, "Promise"], [201, 31, 206, 29], [201, 32, 206, 30], [201, 33, 206, 31, "resolve"], [201, 40, 206, 38], [201, 42, 206, 40, "reject"], [201, 48, 206, 46], [201, 53, 206, 51], [202, 16, 207, 14], [202, 22, 207, 20, "script"], [202, 28, 207, 26], [202, 31, 207, 29, "document"], [202, 39, 207, 37], [202, 40, 207, 38, "createElement"], [202, 53, 207, 51], [202, 54, 207, 52], [202, 62, 207, 60], [202, 63, 207, 61], [203, 16, 208, 14, "script"], [203, 22, 208, 20], [203, 23, 208, 21, "src"], [203, 26, 208, 24], [203, 29, 208, 27], [203, 99, 208, 97], [204, 16, 209, 14, "script"], [204, 22, 209, 20], [204, 23, 209, 21, "onload"], [204, 29, 209, 27], [204, 32, 209, 30, "resolve"], [204, 39, 209, 37], [205, 16, 210, 14, "script"], [205, 22, 210, 20], [205, 23, 210, 21, "onerror"], [205, 30, 210, 28], [205, 33, 210, 31, "reject"], [205, 39, 210, 37], [206, 16, 211, 14, "document"], [206, 24, 211, 22], [206, 25, 211, 23, "head"], [206, 29, 211, 27], [206, 30, 211, 28, "append<PERSON><PERSON><PERSON>"], [206, 41, 211, 39], [206, 42, 211, 40, "script"], [206, 48, 211, 46], [206, 49, 211, 47], [207, 14, 212, 12], [207, 15, 212, 13], [207, 16, 212, 14], [208, 12, 213, 10], [209, 12, 215, 10], [209, 18, 215, 16, "<PERSON>ap<PERSON>"], [209, 25, 215, 23], [209, 28, 215, 27, "window"], [209, 34, 215, 33], [209, 35, 215, 42, "<PERSON>ap<PERSON>"], [209, 42, 215, 49], [211, 12, 217, 10], [212, 12, 218, 10], [212, 18, 218, 16, "Promise"], [212, 25, 218, 23], [212, 26, 218, 24, "all"], [212, 29, 218, 27], [212, 30, 218, 28], [212, 31, 219, 12, "<PERSON>ap<PERSON>"], [212, 38, 219, 19], [212, 39, 219, 20, "nets"], [212, 43, 219, 24], [212, 44, 219, 25, "tinyFaceDetector"], [212, 60, 219, 41], [212, 61, 219, 42, "loadFromUri"], [212, 72, 219, 53], [212, 73, 219, 54], [212, 130, 219, 111], [212, 131, 219, 112], [212, 133, 220, 12, "<PERSON>ap<PERSON>"], [212, 140, 220, 19], [212, 141, 220, 20, "nets"], [212, 145, 220, 24], [212, 146, 220, 25, "faceLandmark68Net"], [212, 163, 220, 42], [212, 164, 220, 43, "loadFromUri"], [212, 175, 220, 54], [212, 176, 220, 55], [212, 233, 220, 112], [212, 234, 220, 113], [212, 235, 221, 11], [212, 236, 221, 12], [214, 12, 223, 10], [215, 12, 224, 10], [215, 18, 224, 16, "faceDetections"], [215, 32, 224, 30], [215, 35, 224, 33], [215, 41, 224, 39, "<PERSON>ap<PERSON>"], [215, 48, 224, 46], [215, 49, 224, 47, "detectAllFaces"], [215, 63, 224, 61], [215, 64, 224, 62, "img"], [215, 67, 224, 65], [215, 69, 224, 67], [215, 73, 224, 71, "<PERSON>ap<PERSON>"], [215, 80, 224, 78], [215, 81, 224, 79, "TinyFaceDetectorOptions"], [215, 104, 224, 102], [215, 105, 224, 103], [215, 106, 224, 104], [215, 107, 224, 105], [216, 12, 226, 10, "detectedFaces"], [216, 25, 226, 23], [216, 28, 226, 26, "faceDetections"], [216, 42, 226, 40], [216, 43, 226, 41, "map"], [216, 46, 226, 44], [216, 47, 226, 46, "detection"], [216, 56, 226, 60], [216, 61, 226, 66], [217, 14, 227, 12, "boundingBox"], [217, 25, 227, 23], [217, 27, 227, 25], [218, 16, 228, 14, "xCenter"], [218, 23, 228, 21], [218, 25, 228, 23], [218, 26, 228, 24, "detection"], [218, 35, 228, 33], [218, 36, 228, 34, "box"], [218, 39, 228, 37], [218, 40, 228, 38, "x"], [218, 41, 228, 39], [218, 44, 228, 42, "detection"], [218, 53, 228, 51], [218, 54, 228, 52, "box"], [218, 57, 228, 55], [218, 58, 228, 56, "width"], [218, 63, 228, 61], [218, 66, 228, 64], [218, 67, 228, 65], [218, 71, 228, 69, "img"], [218, 74, 228, 72], [218, 75, 228, 73, "width"], [218, 80, 228, 78], [219, 16, 229, 14, "yCenter"], [219, 23, 229, 21], [219, 25, 229, 23], [219, 26, 229, 24, "detection"], [219, 35, 229, 33], [219, 36, 229, 34, "box"], [219, 39, 229, 37], [219, 40, 229, 38, "y"], [219, 41, 229, 39], [219, 44, 229, 42, "detection"], [219, 53, 229, 51], [219, 54, 229, 52, "box"], [219, 57, 229, 55], [219, 58, 229, 56, "height"], [219, 64, 229, 62], [219, 67, 229, 65], [219, 68, 229, 66], [219, 72, 229, 70, "img"], [219, 75, 229, 73], [219, 76, 229, 74, "height"], [219, 82, 229, 80], [220, 16, 230, 14, "width"], [220, 21, 230, 19], [220, 23, 230, 21, "detection"], [220, 32, 230, 30], [220, 33, 230, 31, "box"], [220, 36, 230, 34], [220, 37, 230, 35, "width"], [220, 42, 230, 40], [220, 45, 230, 43, "img"], [220, 48, 230, 46], [220, 49, 230, 47, "width"], [220, 54, 230, 52], [221, 16, 231, 14, "height"], [221, 22, 231, 20], [221, 24, 231, 22, "detection"], [221, 33, 231, 31], [221, 34, 231, 32, "box"], [221, 37, 231, 35], [221, 38, 231, 36, "height"], [221, 44, 231, 42], [221, 47, 231, 45, "img"], [221, 50, 231, 48], [221, 51, 231, 49, "height"], [222, 14, 232, 12], [223, 12, 233, 10], [223, 13, 233, 11], [223, 14, 233, 12], [223, 15, 233, 13], [224, 12, 235, 10, "console"], [224, 19, 235, 17], [224, 20, 235, 18, "log"], [224, 23, 235, 21], [224, 24, 235, 22], [224, 63, 235, 61, "detectedFaces"], [224, 76, 235, 74], [224, 77, 235, 75, "length"], [224, 83, 235, 81], [224, 91, 235, 89], [224, 92, 235, 90], [225, 10, 236, 8], [225, 11, 236, 9], [225, 12, 236, 10], [225, 19, 236, 17, "faceApiError"], [225, 31, 236, 29], [225, 33, 236, 31], [226, 12, 237, 10, "console"], [226, 19, 237, 17], [226, 20, 237, 18, "warn"], [226, 24, 237, 22], [226, 25, 237, 23], [226, 69, 237, 67], [226, 71, 237, 69, "faceApiError"], [226, 83, 237, 81], [226, 84, 237, 82], [228, 12, 239, 10], [229, 12, 240, 10, "console"], [229, 19, 240, 17], [229, 20, 240, 18, "log"], [229, 23, 240, 21], [229, 24, 240, 22], [229, 94, 240, 92], [229, 95, 240, 93], [230, 12, 241, 10, "detectedFaces"], [230, 25, 241, 23], [230, 28, 241, 26], [230, 29, 241, 27], [231, 14, 242, 12, "boundingBox"], [231, 25, 242, 23], [231, 27, 242, 25], [232, 16, 243, 14, "xCenter"], [232, 23, 243, 21], [232, 25, 243, 23], [232, 28, 243, 26], [233, 16, 243, 29], [234, 16, 244, 14, "yCenter"], [234, 23, 244, 21], [234, 25, 244, 23], [234, 28, 244, 26], [235, 16, 244, 29], [236, 16, 245, 14, "width"], [236, 21, 245, 19], [236, 23, 245, 21], [236, 26, 245, 24], [237, 16, 245, 29], [238, 16, 246, 14, "height"], [238, 22, 246, 20], [238, 24, 246, 22], [238, 27, 246, 25], [238, 28, 246, 29], [239, 14, 247, 12], [240, 12, 248, 10], [240, 13, 248, 11], [240, 14, 248, 12], [241, 12, 249, 10, "console"], [241, 19, 249, 17], [241, 20, 249, 18, "log"], [241, 23, 249, 21], [241, 24, 249, 22], [241, 93, 249, 91], [241, 94, 249, 92], [242, 10, 250, 8], [243, 8, 251, 6], [244, 8, 253, 6, "console"], [244, 15, 253, 13], [244, 16, 253, 14, "log"], [244, 19, 253, 17], [244, 20, 253, 18], [244, 72, 253, 70, "detectedFaces"], [244, 85, 253, 83], [244, 86, 253, 84, "length"], [244, 92, 253, 90], [244, 100, 253, 98], [244, 101, 253, 99], [245, 8, 254, 6], [245, 12, 254, 10, "detectedFaces"], [245, 25, 254, 23], [245, 26, 254, 24, "length"], [245, 32, 254, 30], [245, 35, 254, 33], [245, 36, 254, 34], [245, 38, 254, 36], [246, 10, 255, 8, "console"], [246, 17, 255, 15], [246, 18, 255, 16, "log"], [246, 21, 255, 19], [246, 22, 255, 20], [246, 66, 255, 64], [246, 68, 255, 66, "detectedFaces"], [246, 81, 255, 79], [246, 82, 255, 80, "map"], [246, 85, 255, 83], [246, 86, 255, 84], [246, 87, 255, 85, "face"], [246, 91, 255, 89], [246, 93, 255, 91, "i"], [246, 94, 255, 92], [246, 100, 255, 98], [247, 12, 256, 10, "faceNumber"], [247, 22, 256, 20], [247, 24, 256, 22, "i"], [247, 25, 256, 23], [247, 28, 256, 26], [247, 29, 256, 27], [248, 12, 257, 10, "centerX"], [248, 19, 257, 17], [248, 21, 257, 19, "face"], [248, 25, 257, 23], [248, 26, 257, 24, "boundingBox"], [248, 37, 257, 35], [248, 38, 257, 36, "xCenter"], [248, 45, 257, 43], [249, 12, 258, 10, "centerY"], [249, 19, 258, 17], [249, 21, 258, 19, "face"], [249, 25, 258, 23], [249, 26, 258, 24, "boundingBox"], [249, 37, 258, 35], [249, 38, 258, 36, "yCenter"], [249, 45, 258, 43], [250, 12, 259, 10, "width"], [250, 17, 259, 15], [250, 19, 259, 17, "face"], [250, 23, 259, 21], [250, 24, 259, 22, "boundingBox"], [250, 35, 259, 33], [250, 36, 259, 34, "width"], [250, 41, 259, 39], [251, 12, 260, 10, "height"], [251, 18, 260, 16], [251, 20, 260, 18, "face"], [251, 24, 260, 22], [251, 25, 260, 23, "boundingBox"], [251, 36, 260, 34], [251, 37, 260, 35, "height"], [252, 10, 261, 8], [252, 11, 261, 9], [252, 12, 261, 10], [252, 13, 261, 11], [252, 14, 261, 12], [253, 8, 262, 6], [253, 9, 262, 7], [253, 15, 262, 13], [254, 10, 263, 8, "console"], [254, 17, 263, 15], [254, 18, 263, 16, "log"], [254, 21, 263, 19], [254, 22, 263, 20], [254, 91, 263, 89], [254, 92, 263, 90], [255, 8, 264, 6], [256, 8, 266, 6, "setProcessingProgress"], [256, 29, 266, 27], [256, 30, 266, 28], [256, 32, 266, 30], [256, 33, 266, 31], [258, 8, 268, 6], [259, 8, 269, 6], [259, 12, 269, 10, "detectedFaces"], [259, 25, 269, 23], [259, 26, 269, 24, "length"], [259, 32, 269, 30], [259, 35, 269, 33], [259, 36, 269, 34], [259, 38, 269, 36], [260, 10, 270, 8, "detectedFaces"], [260, 23, 270, 21], [260, 24, 270, 22, "for<PERSON>ach"], [260, 31, 270, 29], [260, 32, 270, 30], [260, 33, 270, 31, "detection"], [260, 42, 270, 40], [260, 44, 270, 42, "index"], [260, 49, 270, 47], [260, 54, 270, 52], [261, 12, 271, 10], [261, 18, 271, 16, "bbox"], [261, 22, 271, 20], [261, 25, 271, 23, "detection"], [261, 34, 271, 32], [261, 35, 271, 33, "boundingBox"], [261, 46, 271, 44], [263, 12, 273, 10], [264, 12, 274, 10], [264, 18, 274, 16, "faceX"], [264, 23, 274, 21], [264, 26, 274, 24, "bbox"], [264, 30, 274, 28], [264, 31, 274, 29, "xCenter"], [264, 38, 274, 36], [264, 41, 274, 39, "img"], [264, 44, 274, 42], [264, 45, 274, 43, "width"], [264, 50, 274, 48], [264, 53, 274, 52, "bbox"], [264, 57, 274, 56], [264, 58, 274, 57, "width"], [264, 63, 274, 62], [264, 66, 274, 65, "img"], [264, 69, 274, 68], [264, 70, 274, 69, "width"], [264, 75, 274, 74], [264, 78, 274, 78], [264, 79, 274, 79], [265, 12, 275, 10], [265, 18, 275, 16, "faceY"], [265, 23, 275, 21], [265, 26, 275, 24, "bbox"], [265, 30, 275, 28], [265, 31, 275, 29, "yCenter"], [265, 38, 275, 36], [265, 41, 275, 39, "img"], [265, 44, 275, 42], [265, 45, 275, 43, "height"], [265, 51, 275, 49], [265, 54, 275, 53, "bbox"], [265, 58, 275, 57], [265, 59, 275, 58, "height"], [265, 65, 275, 64], [265, 68, 275, 67, "img"], [265, 71, 275, 70], [265, 72, 275, 71, "height"], [265, 78, 275, 77], [265, 81, 275, 81], [265, 82, 275, 82], [266, 12, 276, 10], [266, 18, 276, 16, "faceWidth"], [266, 27, 276, 25], [266, 30, 276, 28, "bbox"], [266, 34, 276, 32], [266, 35, 276, 33, "width"], [266, 40, 276, 38], [266, 43, 276, 41, "img"], [266, 46, 276, 44], [266, 47, 276, 45, "width"], [266, 52, 276, 50], [267, 12, 277, 10], [267, 18, 277, 16, "faceHeight"], [267, 28, 277, 26], [267, 31, 277, 29, "bbox"], [267, 35, 277, 33], [267, 36, 277, 34, "height"], [267, 42, 277, 40], [267, 45, 277, 43, "img"], [267, 48, 277, 46], [267, 49, 277, 47, "height"], [267, 55, 277, 53], [269, 12, 279, 10], [270, 12, 280, 10], [270, 18, 280, 16, "padding"], [270, 25, 280, 23], [270, 28, 280, 26], [270, 31, 280, 29], [270, 32, 280, 30], [270, 33, 280, 31], [271, 12, 281, 10], [271, 18, 281, 16, "paddedX"], [271, 25, 281, 23], [271, 28, 281, 26, "Math"], [271, 32, 281, 30], [271, 33, 281, 31, "max"], [271, 36, 281, 34], [271, 37, 281, 35], [271, 38, 281, 36], [271, 40, 281, 38, "faceX"], [271, 45, 281, 43], [271, 48, 281, 46, "faceWidth"], [271, 57, 281, 55], [271, 60, 281, 58, "padding"], [271, 67, 281, 65], [271, 68, 281, 66], [272, 12, 282, 10], [272, 18, 282, 16, "paddedY"], [272, 25, 282, 23], [272, 28, 282, 26, "Math"], [272, 32, 282, 30], [272, 33, 282, 31, "max"], [272, 36, 282, 34], [272, 37, 282, 35], [272, 38, 282, 36], [272, 40, 282, 38, "faceY"], [272, 45, 282, 43], [272, 48, 282, 46, "faceHeight"], [272, 58, 282, 56], [272, 61, 282, 59, "padding"], [272, 68, 282, 66], [272, 69, 282, 67], [273, 12, 283, 10], [273, 18, 283, 16, "<PERSON><PERSON><PERSON><PERSON>"], [273, 29, 283, 27], [273, 32, 283, 30, "Math"], [273, 36, 283, 34], [273, 37, 283, 35, "min"], [273, 40, 283, 38], [273, 41, 283, 39, "img"], [273, 44, 283, 42], [273, 45, 283, 43, "width"], [273, 50, 283, 48], [273, 53, 283, 51, "paddedX"], [273, 60, 283, 58], [273, 62, 283, 60, "faceWidth"], [273, 71, 283, 69], [273, 75, 283, 73], [273, 76, 283, 74], [273, 79, 283, 77], [273, 80, 283, 78], [273, 83, 283, 81, "padding"], [273, 90, 283, 88], [273, 91, 283, 89], [273, 92, 283, 90], [274, 12, 284, 10], [274, 18, 284, 16, "paddedHeight"], [274, 30, 284, 28], [274, 33, 284, 31, "Math"], [274, 37, 284, 35], [274, 38, 284, 36, "min"], [274, 41, 284, 39], [274, 42, 284, 40, "img"], [274, 45, 284, 43], [274, 46, 284, 44, "height"], [274, 52, 284, 50], [274, 55, 284, 53, "paddedY"], [274, 62, 284, 60], [274, 64, 284, 62, "faceHeight"], [274, 74, 284, 72], [274, 78, 284, 76], [274, 79, 284, 77], [274, 82, 284, 80], [274, 83, 284, 81], [274, 86, 284, 84, "padding"], [274, 93, 284, 91], [274, 94, 284, 92], [274, 95, 284, 93], [275, 12, 286, 10, "console"], [275, 19, 286, 17], [275, 20, 286, 18, "log"], [275, 23, 286, 21], [275, 24, 286, 22], [275, 60, 286, 58, "index"], [275, 65, 286, 63], [275, 68, 286, 66], [275, 69, 286, 67], [275, 77, 286, 75, "Math"], [275, 81, 286, 79], [275, 82, 286, 80, "round"], [275, 87, 286, 85], [275, 88, 286, 86, "paddedX"], [275, 95, 286, 93], [275, 96, 286, 94], [275, 101, 286, 99, "Math"], [275, 105, 286, 103], [275, 106, 286, 104, "round"], [275, 111, 286, 109], [275, 112, 286, 110, "paddedY"], [275, 119, 286, 117], [275, 120, 286, 118], [275, 130, 286, 128, "Math"], [275, 134, 286, 132], [275, 135, 286, 133, "round"], [275, 140, 286, 138], [275, 141, 286, 139, "<PERSON><PERSON><PERSON><PERSON>"], [275, 152, 286, 150], [275, 153, 286, 151], [275, 157, 286, 155, "Math"], [275, 161, 286, 159], [275, 162, 286, 160, "round"], [275, 167, 286, 165], [275, 168, 286, 166, "paddedHeight"], [275, 180, 286, 178], [275, 181, 286, 179], [275, 183, 286, 181], [275, 184, 286, 182], [277, 12, 288, 10], [278, 12, 289, 10], [278, 18, 289, 16, "faceImageData"], [278, 31, 289, 29], [278, 34, 289, 32, "ctx"], [278, 37, 289, 35], [278, 38, 289, 36, "getImageData"], [278, 50, 289, 48], [278, 51, 289, 49, "paddedX"], [278, 58, 289, 56], [278, 60, 289, 58, "paddedY"], [278, 67, 289, 65], [278, 69, 289, 67, "<PERSON><PERSON><PERSON><PERSON>"], [278, 80, 289, 78], [278, 82, 289, 80, "paddedHeight"], [278, 94, 289, 92], [278, 95, 289, 93], [279, 12, 290, 10], [279, 18, 290, 16, "data"], [279, 22, 290, 20], [279, 25, 290, 23, "faceImageData"], [279, 38, 290, 36], [279, 39, 290, 37, "data"], [279, 43, 290, 41], [280, 12, 292, 10, "console"], [280, 19, 292, 17], [280, 20, 292, 18, "log"], [280, 23, 292, 21], [280, 24, 292, 22], [280, 64, 292, 62, "data"], [280, 68, 292, 66], [280, 69, 292, 67, "length"], [280, 75, 292, 73], [280, 86, 292, 84, "<PERSON><PERSON><PERSON><PERSON>"], [280, 97, 292, 95], [280, 101, 292, 99, "paddedHeight"], [280, 113, 292, 111], [280, 122, 292, 120], [280, 123, 292, 121], [282, 12, 294, 10], [283, 12, 295, 10], [283, 18, 295, 16, "pixelSize"], [283, 27, 295, 25], [283, 30, 295, 28, "Math"], [283, 34, 295, 32], [283, 35, 295, 33, "max"], [283, 38, 295, 36], [283, 39, 295, 37], [283, 41, 295, 39], [283, 43, 295, 41, "Math"], [283, 47, 295, 45], [283, 48, 295, 46, "min"], [283, 51, 295, 49], [283, 52, 295, 50, "<PERSON><PERSON><PERSON><PERSON>"], [283, 63, 295, 61], [283, 65, 295, 63, "paddedHeight"], [283, 77, 295, 75], [283, 78, 295, 76], [283, 81, 295, 79], [283, 83, 295, 81], [283, 84, 295, 82], [283, 85, 295, 83], [283, 86, 295, 84], [284, 12, 296, 10, "console"], [284, 19, 296, 17], [284, 20, 296, 18, "log"], [284, 23, 296, 21], [284, 24, 296, 22], [284, 64, 296, 62, "pixelSize"], [284, 73, 296, 71], [284, 90, 296, 88], [284, 91, 296, 89], [285, 12, 297, 10], [285, 17, 297, 15], [285, 21, 297, 19, "y"], [285, 22, 297, 20], [285, 25, 297, 23], [285, 26, 297, 24], [285, 28, 297, 26, "y"], [285, 29, 297, 27], [285, 32, 297, 30, "paddedHeight"], [285, 44, 297, 42], [285, 46, 297, 44, "y"], [285, 47, 297, 45], [285, 51, 297, 49, "pixelSize"], [285, 60, 297, 58], [285, 62, 297, 60], [286, 14, 298, 12], [286, 19, 298, 17], [286, 23, 298, 21, "x"], [286, 24, 298, 22], [286, 27, 298, 25], [286, 28, 298, 26], [286, 30, 298, 28, "x"], [286, 31, 298, 29], [286, 34, 298, 32, "<PERSON><PERSON><PERSON><PERSON>"], [286, 45, 298, 43], [286, 47, 298, 45, "x"], [286, 48, 298, 46], [286, 52, 298, 50, "pixelSize"], [286, 61, 298, 59], [286, 63, 298, 61], [287, 16, 299, 14], [288, 16, 300, 14], [288, 22, 300, 20, "pixelIndex"], [288, 32, 300, 30], [288, 35, 300, 33], [288, 36, 300, 34, "y"], [288, 37, 300, 35], [288, 40, 300, 38, "<PERSON><PERSON><PERSON><PERSON>"], [288, 51, 300, 49], [288, 54, 300, 52, "x"], [288, 55, 300, 53], [288, 59, 300, 57], [288, 60, 300, 58], [289, 16, 301, 14], [289, 22, 301, 20, "r"], [289, 23, 301, 21], [289, 26, 301, 24, "data"], [289, 30, 301, 28], [289, 31, 301, 29, "pixelIndex"], [289, 41, 301, 39], [289, 42, 301, 40], [290, 16, 302, 14], [290, 22, 302, 20, "g"], [290, 23, 302, 21], [290, 26, 302, 24, "data"], [290, 30, 302, 28], [290, 31, 302, 29, "pixelIndex"], [290, 41, 302, 39], [290, 44, 302, 42], [290, 45, 302, 43], [290, 46, 302, 44], [291, 16, 303, 14], [291, 22, 303, 20, "b"], [291, 23, 303, 21], [291, 26, 303, 24, "data"], [291, 30, 303, 28], [291, 31, 303, 29, "pixelIndex"], [291, 41, 303, 39], [291, 44, 303, 42], [291, 45, 303, 43], [291, 46, 303, 44], [292, 16, 304, 14], [292, 22, 304, 20, "a"], [292, 23, 304, 21], [292, 26, 304, 24, "data"], [292, 30, 304, 28], [292, 31, 304, 29, "pixelIndex"], [292, 41, 304, 39], [292, 44, 304, 42], [292, 45, 304, 43], [292, 46, 304, 44], [294, 16, 306, 14], [295, 16, 307, 14], [295, 21, 307, 19], [295, 25, 307, 23, "dy"], [295, 27, 307, 25], [295, 30, 307, 28], [295, 31, 307, 29], [295, 33, 307, 31, "dy"], [295, 35, 307, 33], [295, 38, 307, 36, "pixelSize"], [295, 47, 307, 45], [295, 51, 307, 49, "y"], [295, 52, 307, 50], [295, 55, 307, 53, "dy"], [295, 57, 307, 55], [295, 60, 307, 58, "paddedHeight"], [295, 72, 307, 70], [295, 74, 307, 72, "dy"], [295, 76, 307, 74], [295, 78, 307, 76], [295, 80, 307, 78], [296, 18, 308, 16], [296, 23, 308, 21], [296, 27, 308, 25, "dx"], [296, 29, 308, 27], [296, 32, 308, 30], [296, 33, 308, 31], [296, 35, 308, 33, "dx"], [296, 37, 308, 35], [296, 40, 308, 38, "pixelSize"], [296, 49, 308, 47], [296, 53, 308, 51, "x"], [296, 54, 308, 52], [296, 57, 308, 55, "dx"], [296, 59, 308, 57], [296, 62, 308, 60, "<PERSON><PERSON><PERSON><PERSON>"], [296, 73, 308, 71], [296, 75, 308, 73, "dx"], [296, 77, 308, 75], [296, 79, 308, 77], [296, 81, 308, 79], [297, 20, 309, 18], [297, 26, 309, 24, "blockPixelIndex"], [297, 41, 309, 39], [297, 44, 309, 42], [297, 45, 309, 43], [297, 46, 309, 44, "y"], [297, 47, 309, 45], [297, 50, 309, 48, "dy"], [297, 52, 309, 50], [297, 56, 309, 54, "<PERSON><PERSON><PERSON><PERSON>"], [297, 67, 309, 65], [297, 71, 309, 69, "x"], [297, 72, 309, 70], [297, 75, 309, 73, "dx"], [297, 77, 309, 75], [297, 78, 309, 76], [297, 82, 309, 80], [297, 83, 309, 81], [298, 20, 310, 18, "data"], [298, 24, 310, 22], [298, 25, 310, 23, "blockPixelIndex"], [298, 40, 310, 38], [298, 41, 310, 39], [298, 44, 310, 42, "r"], [298, 45, 310, 43], [299, 20, 311, 18, "data"], [299, 24, 311, 22], [299, 25, 311, 23, "blockPixelIndex"], [299, 40, 311, 38], [299, 43, 311, 41], [299, 44, 311, 42], [299, 45, 311, 43], [299, 48, 311, 46, "g"], [299, 49, 311, 47], [300, 20, 312, 18, "data"], [300, 24, 312, 22], [300, 25, 312, 23, "blockPixelIndex"], [300, 40, 312, 38], [300, 43, 312, 41], [300, 44, 312, 42], [300, 45, 312, 43], [300, 48, 312, 46, "b"], [300, 49, 312, 47], [301, 20, 313, 18, "data"], [301, 24, 313, 22], [301, 25, 313, 23, "blockPixelIndex"], [301, 40, 313, 38], [301, 43, 313, 41], [301, 44, 313, 42], [301, 45, 313, 43], [301, 48, 313, 46, "a"], [301, 49, 313, 47], [302, 18, 314, 16], [303, 16, 315, 14], [304, 14, 316, 12], [305, 12, 317, 10], [307, 12, 319, 10], [308, 12, 320, 10, "ctx"], [308, 15, 320, 13], [308, 16, 320, 14, "putImageData"], [308, 28, 320, 26], [308, 29, 320, 27, "faceImageData"], [308, 42, 320, 40], [308, 44, 320, 42, "paddedX"], [308, 51, 320, 49], [308, 53, 320, 51, "paddedY"], [308, 60, 320, 58], [308, 61, 320, 59], [309, 12, 321, 10, "console"], [309, 19, 321, 17], [309, 20, 321, 18, "log"], [309, 23, 321, 21], [309, 24, 321, 22], [309, 50, 321, 48, "index"], [309, 55, 321, 53], [309, 58, 321, 56], [309, 59, 321, 57], [309, 91, 321, 89], [309, 92, 321, 90], [310, 10, 322, 8], [310, 11, 322, 9], [310, 12, 322, 10], [311, 10, 323, 8, "console"], [311, 17, 323, 15], [311, 18, 323, 16, "log"], [311, 21, 323, 19], [311, 22, 323, 20], [311, 48, 323, 46, "detectedFaces"], [311, 61, 323, 59], [311, 62, 323, 60, "length"], [311, 68, 323, 66], [311, 95, 323, 93], [311, 96, 323, 94], [312, 8, 324, 6], [312, 9, 324, 7], [312, 15, 324, 13], [313, 10, 325, 8, "console"], [313, 17, 325, 15], [313, 18, 325, 16, "log"], [313, 21, 325, 19], [313, 22, 325, 20], [313, 91, 325, 89], [313, 92, 325, 90], [314, 8, 326, 6], [315, 8, 328, 6, "setProcessingProgress"], [315, 29, 328, 27], [315, 30, 328, 28], [315, 32, 328, 30], [315, 33, 328, 31], [317, 8, 330, 6], [318, 8, 331, 6], [318, 14, 331, 12, "blurredImageBlob"], [318, 30, 331, 28], [318, 33, 331, 31], [318, 39, 331, 37], [318, 43, 331, 41, "Promise"], [318, 50, 331, 48], [318, 51, 331, 56, "resolve"], [318, 58, 331, 63], [318, 62, 331, 68], [319, 10, 332, 8, "canvas"], [319, 16, 332, 14], [319, 17, 332, 15, "toBlob"], [319, 23, 332, 21], [319, 24, 332, 23, "blob"], [319, 28, 332, 27], [319, 32, 332, 32, "resolve"], [319, 39, 332, 39], [319, 40, 332, 40, "blob"], [319, 44, 332, 45], [319, 45, 332, 46], [319, 47, 332, 48], [319, 59, 332, 60], [319, 61, 332, 62], [319, 64, 332, 65], [319, 65, 332, 66], [320, 8, 333, 6], [320, 9, 333, 7], [320, 10, 333, 8], [321, 8, 335, 6], [321, 14, 335, 12, "blurredImageUrl"], [321, 29, 335, 27], [321, 32, 335, 30, "URL"], [321, 35, 335, 33], [321, 36, 335, 34, "createObjectURL"], [321, 51, 335, 49], [321, 52, 335, 50, "blurredImageBlob"], [321, 68, 335, 66], [321, 69, 335, 67], [322, 8, 337, 6, "setProcessingProgress"], [322, 29, 337, 27], [322, 30, 337, 28], [322, 33, 337, 31], [322, 34, 337, 32], [324, 8, 339, 6], [325, 8, 340, 6], [325, 14, 340, 12, "completeProcessing"], [325, 32, 340, 30], [325, 33, 340, 31, "blurredImageUrl"], [325, 48, 340, 46], [325, 49, 340, 47], [326, 6, 342, 4], [326, 7, 342, 5], [326, 8, 342, 6], [326, 15, 342, 13, "error"], [326, 20, 342, 18], [326, 22, 342, 20], [327, 8, 343, 6, "console"], [327, 15, 343, 13], [327, 16, 343, 14, "error"], [327, 21, 343, 19], [327, 22, 343, 20], [327, 57, 343, 55], [327, 59, 343, 57, "error"], [327, 64, 343, 62], [327, 65, 343, 63], [328, 8, 344, 6, "setErrorMessage"], [328, 23, 344, 21], [328, 24, 344, 22], [328, 50, 344, 48], [328, 51, 344, 49], [329, 8, 345, 6, "setProcessingState"], [329, 26, 345, 24], [329, 27, 345, 25], [329, 34, 345, 32], [329, 35, 345, 33], [330, 6, 346, 4], [331, 4, 347, 2], [331, 5, 347, 3], [333, 4, 349, 2], [334, 4, 350, 2], [334, 10, 350, 8, "completeProcessing"], [334, 28, 350, 26], [334, 31, 350, 29], [334, 37, 350, 36, "blurredImageUrl"], [334, 52, 350, 59], [334, 56, 350, 64], [335, 6, 351, 4], [335, 10, 351, 8], [336, 8, 352, 6, "setProcessingState"], [336, 26, 352, 24], [336, 27, 352, 25], [336, 37, 352, 35], [336, 38, 352, 36], [338, 8, 354, 6], [339, 8, 355, 6], [339, 14, 355, 12, "timestamp"], [339, 23, 355, 21], [339, 26, 355, 24, "Date"], [339, 30, 355, 28], [339, 31, 355, 29, "now"], [339, 34, 355, 32], [339, 35, 355, 33], [339, 36, 355, 34], [340, 8, 356, 6], [340, 14, 356, 12, "result"], [340, 20, 356, 18], [340, 23, 356, 21], [341, 10, 357, 8, "imageUrl"], [341, 18, 357, 16], [341, 20, 357, 18, "blurredImageUrl"], [341, 35, 357, 33], [342, 10, 358, 8, "localUri"], [342, 18, 358, 16], [342, 20, 358, 18, "blurredImageUrl"], [342, 35, 358, 33], [343, 10, 359, 8, "challengeCode"], [343, 23, 359, 21], [343, 25, 359, 23, "challengeCode"], [343, 38, 359, 36], [343, 42, 359, 40], [343, 44, 359, 42], [344, 10, 360, 8, "timestamp"], [344, 19, 360, 17], [345, 10, 361, 8, "jobId"], [345, 15, 361, 13], [345, 17, 361, 15], [345, 27, 361, 25, "timestamp"], [345, 36, 361, 34], [345, 38, 361, 36], [346, 10, 362, 8, "status"], [346, 16, 362, 14], [346, 18, 362, 16], [347, 8, 363, 6], [347, 9, 363, 7], [348, 8, 365, 6, "console"], [348, 15, 365, 13], [348, 16, 365, 14, "log"], [348, 19, 365, 17], [348, 20, 365, 18], [348, 58, 365, 56], [348, 60, 365, 58, "result"], [348, 66, 365, 64], [348, 67, 365, 65], [350, 8, 367, 6], [351, 8, 368, 6, "onComplete"], [351, 18, 368, 16], [351, 19, 368, 17, "result"], [351, 25, 368, 23], [351, 26, 368, 24], [352, 6, 370, 4], [352, 7, 370, 5], [352, 8, 370, 6], [352, 15, 370, 13, "error"], [352, 20, 370, 18], [352, 22, 370, 20], [353, 8, 371, 6, "console"], [353, 15, 371, 13], [353, 16, 371, 14, "error"], [353, 21, 371, 19], [353, 22, 371, 20], [353, 57, 371, 55], [353, 59, 371, 57, "error"], [353, 64, 371, 62], [353, 65, 371, 63], [354, 8, 372, 6, "setErrorMessage"], [354, 23, 372, 21], [354, 24, 372, 22], [354, 56, 372, 54], [354, 57, 372, 55], [355, 8, 373, 6, "setProcessingState"], [355, 26, 373, 24], [355, 27, 373, 25], [355, 34, 373, 32], [355, 35, 373, 33], [356, 6, 374, 4], [357, 4, 375, 2], [357, 5, 375, 3], [359, 4, 377, 2], [360, 4, 378, 2], [360, 10, 378, 8, "triggerServerProcessing"], [360, 33, 378, 31], [360, 36, 378, 34], [360, 42, 378, 34, "triggerServerProcessing"], [360, 43, 378, 41, "privateImageUrl"], [360, 58, 378, 64], [360, 60, 378, 66, "timestamp"], [360, 69, 378, 83], [360, 74, 378, 88], [361, 6, 379, 4], [361, 10, 379, 8], [362, 8, 380, 6, "console"], [362, 15, 380, 13], [362, 16, 380, 14, "log"], [362, 19, 380, 17], [362, 20, 380, 18], [362, 74, 380, 72], [362, 76, 380, 74, "privateImageUrl"], [362, 91, 380, 89], [362, 92, 380, 90], [363, 8, 381, 6, "setProcessingState"], [363, 26, 381, 24], [363, 27, 381, 25], [363, 39, 381, 37], [363, 40, 381, 38], [364, 8, 382, 6, "setProcessingProgress"], [364, 29, 382, 27], [364, 30, 382, 28], [364, 32, 382, 30], [364, 33, 382, 31], [365, 8, 384, 6], [365, 14, 384, 12, "requestBody"], [365, 25, 384, 23], [365, 28, 384, 26], [366, 10, 385, 8, "imageUrl"], [366, 18, 385, 16], [366, 20, 385, 18, "privateImageUrl"], [366, 35, 385, 33], [367, 10, 386, 8, "userId"], [367, 16, 386, 14], [368, 10, 387, 8, "requestId"], [368, 19, 387, 17], [369, 10, 388, 8, "timestamp"], [369, 19, 388, 17], [370, 10, 389, 8, "platform"], [370, 18, 389, 16], [370, 20, 389, 18], [371, 8, 390, 6], [371, 9, 390, 7], [372, 8, 392, 6, "console"], [372, 15, 392, 13], [372, 16, 392, 14, "log"], [372, 19, 392, 17], [372, 20, 392, 18], [372, 65, 392, 63], [372, 67, 392, 65, "requestBody"], [372, 78, 392, 76], [372, 79, 392, 77], [374, 8, 394, 6], [375, 8, 395, 6], [375, 14, 395, 12, "response"], [375, 22, 395, 20], [375, 25, 395, 23], [375, 31, 395, 29, "fetch"], [375, 36, 395, 34], [375, 37, 395, 35], [375, 40, 395, 38, "API_BASE_URL"], [375, 52, 395, 50], [375, 72, 395, 70], [375, 74, 395, 72], [376, 10, 396, 8, "method"], [376, 16, 396, 14], [376, 18, 396, 16], [376, 24, 396, 22], [377, 10, 397, 8, "headers"], [377, 17, 397, 15], [377, 19, 397, 17], [378, 12, 398, 10], [378, 26, 398, 24], [378, 28, 398, 26], [378, 46, 398, 44], [379, 12, 399, 10], [379, 27, 399, 25], [379, 29, 399, 27], [379, 39, 399, 37], [379, 45, 399, 43, "getAuthToken"], [379, 57, 399, 55], [379, 58, 399, 56], [379, 59, 399, 57], [380, 10, 400, 8], [380, 11, 400, 9], [381, 10, 401, 8, "body"], [381, 14, 401, 12], [381, 16, 401, 14, "JSON"], [381, 20, 401, 18], [381, 21, 401, 19, "stringify"], [381, 30, 401, 28], [381, 31, 401, 29, "requestBody"], [381, 42, 401, 40], [382, 8, 402, 6], [382, 9, 402, 7], [382, 10, 402, 8], [383, 8, 404, 6], [383, 12, 404, 10], [383, 13, 404, 11, "response"], [383, 21, 404, 19], [383, 22, 404, 20, "ok"], [383, 24, 404, 22], [383, 26, 404, 24], [384, 10, 405, 8], [384, 16, 405, 14, "errorText"], [384, 25, 405, 23], [384, 28, 405, 26], [384, 34, 405, 32, "response"], [384, 42, 405, 40], [384, 43, 405, 41, "text"], [384, 47, 405, 45], [384, 48, 405, 46], [384, 49, 405, 47], [385, 10, 406, 8, "console"], [385, 17, 406, 15], [385, 18, 406, 16, "error"], [385, 23, 406, 21], [385, 24, 406, 22], [385, 68, 406, 66], [385, 70, 406, 68, "response"], [385, 78, 406, 76], [385, 79, 406, 77, "status"], [385, 85, 406, 83], [385, 87, 406, 85, "errorText"], [385, 96, 406, 94], [385, 97, 406, 95], [386, 10, 407, 8], [386, 16, 407, 14], [386, 20, 407, 18, "Error"], [386, 25, 407, 23], [386, 26, 407, 24], [386, 48, 407, 46, "response"], [386, 56, 407, 54], [386, 57, 407, 55, "status"], [386, 63, 407, 61], [386, 67, 407, 65, "response"], [386, 75, 407, 73], [386, 76, 407, 74, "statusText"], [386, 86, 407, 84], [386, 88, 407, 86], [386, 89, 407, 87], [387, 8, 408, 6], [388, 8, 410, 6], [388, 14, 410, 12, "result"], [388, 20, 410, 18], [388, 23, 410, 21], [388, 29, 410, 27, "response"], [388, 37, 410, 35], [388, 38, 410, 36, "json"], [388, 42, 410, 40], [388, 43, 410, 41], [388, 44, 410, 42], [389, 8, 411, 6, "console"], [389, 15, 411, 13], [389, 16, 411, 14, "log"], [389, 19, 411, 17], [389, 20, 411, 18], [389, 68, 411, 66], [389, 70, 411, 68, "result"], [389, 76, 411, 74], [389, 77, 411, 75], [390, 8, 413, 6], [390, 12, 413, 10], [390, 13, 413, 11, "result"], [390, 19, 413, 17], [390, 20, 413, 18, "jobId"], [390, 25, 413, 23], [390, 27, 413, 25], [391, 10, 414, 8], [391, 16, 414, 14], [391, 20, 414, 18, "Error"], [391, 25, 414, 23], [391, 26, 414, 24], [391, 70, 414, 68], [391, 71, 414, 69], [392, 8, 415, 6], [394, 8, 417, 6], [395, 8, 418, 6], [395, 14, 418, 12, "pollForCompletion"], [395, 31, 418, 29], [395, 32, 418, 30, "result"], [395, 38, 418, 36], [395, 39, 418, 37, "jobId"], [395, 44, 418, 42], [395, 46, 418, 44, "timestamp"], [395, 55, 418, 53], [395, 56, 418, 54], [396, 6, 419, 4], [396, 7, 419, 5], [396, 8, 419, 6], [396, 15, 419, 13, "error"], [396, 20, 419, 18], [396, 22, 419, 20], [397, 8, 420, 6, "console"], [397, 15, 420, 13], [397, 16, 420, 14, "error"], [397, 21, 420, 19], [397, 22, 420, 20], [397, 57, 420, 55], [397, 59, 420, 57, "error"], [397, 64, 420, 62], [397, 65, 420, 63], [398, 8, 421, 6, "setErrorMessage"], [398, 23, 421, 21], [398, 24, 421, 22], [398, 52, 421, 50, "error"], [398, 57, 421, 55], [398, 58, 421, 56, "message"], [398, 65, 421, 63], [398, 67, 421, 65], [398, 68, 421, 66], [399, 8, 422, 6, "setProcessingState"], [399, 26, 422, 24], [399, 27, 422, 25], [399, 34, 422, 32], [399, 35, 422, 33], [400, 6, 423, 4], [401, 4, 424, 2], [401, 5, 424, 3], [402, 4, 425, 2], [403, 4, 426, 2], [403, 10, 426, 8, "pollForCompletion"], [403, 27, 426, 25], [403, 30, 426, 28], [403, 36, 426, 28, "pollForCompletion"], [403, 37, 426, 35, "jobId"], [403, 42, 426, 48], [403, 44, 426, 50, "timestamp"], [403, 53, 426, 67], [403, 55, 426, 69, "attempts"], [403, 63, 426, 77], [403, 66, 426, 80], [403, 67, 426, 81], [403, 72, 426, 86], [404, 6, 427, 4], [404, 12, 427, 10, "MAX_ATTEMPTS"], [404, 24, 427, 22], [404, 27, 427, 25], [404, 29, 427, 27], [404, 30, 427, 28], [404, 31, 427, 29], [405, 6, 428, 4], [405, 12, 428, 10, "POLL_INTERVAL"], [405, 25, 428, 23], [405, 28, 428, 26], [405, 32, 428, 30], [405, 33, 428, 31], [405, 34, 428, 32], [407, 6, 430, 4, "console"], [407, 13, 430, 11], [407, 14, 430, 12, "log"], [407, 17, 430, 15], [407, 18, 430, 16], [407, 53, 430, 51, "attempts"], [407, 61, 430, 59], [407, 64, 430, 62], [407, 65, 430, 63], [407, 69, 430, 67, "MAX_ATTEMPTS"], [407, 81, 430, 79], [407, 93, 430, 91, "jobId"], [407, 98, 430, 96], [407, 100, 430, 98], [407, 101, 430, 99], [408, 6, 432, 4], [408, 10, 432, 8, "attempts"], [408, 18, 432, 16], [408, 22, 432, 20, "MAX_ATTEMPTS"], [408, 34, 432, 32], [408, 36, 432, 34], [409, 8, 433, 6, "console"], [409, 15, 433, 13], [409, 16, 433, 14, "error"], [409, 21, 433, 19], [409, 22, 433, 20], [409, 75, 433, 73], [409, 76, 433, 74], [410, 8, 434, 6, "setErrorMessage"], [410, 23, 434, 21], [410, 24, 434, 22], [410, 63, 434, 61], [410, 64, 434, 62], [411, 8, 435, 6, "setProcessingState"], [411, 26, 435, 24], [411, 27, 435, 25], [411, 34, 435, 32], [411, 35, 435, 33], [412, 8, 436, 6], [413, 6, 437, 4], [414, 6, 439, 4], [414, 10, 439, 8], [415, 8, 440, 6], [415, 14, 440, 12, "response"], [415, 22, 440, 20], [415, 25, 440, 23], [415, 31, 440, 29, "fetch"], [415, 36, 440, 34], [415, 37, 440, 35], [415, 40, 440, 38, "API_BASE_URL"], [415, 52, 440, 50], [415, 75, 440, 73, "jobId"], [415, 80, 440, 78], [415, 82, 440, 80], [415, 84, 440, 82], [416, 10, 441, 8, "headers"], [416, 17, 441, 15], [416, 19, 441, 17], [417, 12, 442, 10], [417, 27, 442, 25], [417, 29, 442, 27], [417, 39, 442, 37], [417, 45, 442, 43, "getAuthToken"], [417, 57, 442, 55], [417, 58, 442, 56], [417, 59, 442, 57], [418, 10, 443, 8], [419, 8, 444, 6], [419, 9, 444, 7], [419, 10, 444, 8], [420, 8, 446, 6], [420, 12, 446, 10], [420, 13, 446, 11, "response"], [420, 21, 446, 19], [420, 22, 446, 20, "ok"], [420, 24, 446, 22], [420, 26, 446, 24], [421, 10, 447, 8], [421, 16, 447, 14], [421, 20, 447, 18, "Error"], [421, 25, 447, 23], [421, 26, 447, 24], [421, 34, 447, 32, "response"], [421, 42, 447, 40], [421, 43, 447, 41, "status"], [421, 49, 447, 47], [421, 54, 447, 52, "response"], [421, 62, 447, 60], [421, 63, 447, 61, "statusText"], [421, 73, 447, 71], [421, 75, 447, 73], [421, 76, 447, 74], [422, 8, 448, 6], [423, 8, 450, 6], [423, 14, 450, 12, "status"], [423, 20, 450, 18], [423, 23, 450, 21], [423, 29, 450, 27, "response"], [423, 37, 450, 35], [423, 38, 450, 36, "json"], [423, 42, 450, 40], [423, 43, 450, 41], [423, 44, 450, 42], [424, 8, 451, 6, "console"], [424, 15, 451, 13], [424, 16, 451, 14, "log"], [424, 19, 451, 17], [424, 20, 451, 18], [424, 54, 451, 52], [424, 56, 451, 54, "status"], [424, 62, 451, 60], [424, 63, 451, 61], [425, 8, 453, 6], [425, 12, 453, 10, "status"], [425, 18, 453, 16], [425, 19, 453, 17, "status"], [425, 25, 453, 23], [425, 30, 453, 28], [425, 41, 453, 39], [425, 43, 453, 41], [426, 10, 454, 8, "console"], [426, 17, 454, 15], [426, 18, 454, 16, "log"], [426, 21, 454, 19], [426, 22, 454, 20], [426, 73, 454, 71], [426, 74, 454, 72], [427, 10, 455, 8, "setProcessingProgress"], [427, 31, 455, 29], [427, 32, 455, 30], [427, 35, 455, 33], [427, 36, 455, 34], [428, 10, 456, 8, "setProcessingState"], [428, 28, 456, 26], [428, 29, 456, 27], [428, 40, 456, 38], [428, 41, 456, 39], [429, 10, 457, 8], [430, 10, 458, 8], [430, 16, 458, 14, "result"], [430, 22, 458, 20], [430, 25, 458, 23], [431, 12, 459, 10, "imageUrl"], [431, 20, 459, 18], [431, 22, 459, 20, "status"], [431, 28, 459, 26], [431, 29, 459, 27, "publicUrl"], [431, 38, 459, 36], [432, 12, 459, 38], [433, 12, 460, 10, "localUri"], [433, 20, 460, 18], [433, 22, 460, 20, "capturedPhoto"], [433, 35, 460, 33], [433, 39, 460, 37, "status"], [433, 45, 460, 43], [433, 46, 460, 44, "publicUrl"], [433, 55, 460, 53], [434, 12, 460, 55], [435, 12, 461, 10, "challengeCode"], [435, 25, 461, 23], [435, 27, 461, 25, "challengeCode"], [435, 40, 461, 38], [435, 44, 461, 42], [435, 46, 461, 44], [436, 12, 462, 10, "timestamp"], [436, 21, 462, 19], [437, 12, 463, 10, "processingStatus"], [437, 28, 463, 26], [437, 30, 463, 28], [438, 10, 464, 8], [438, 11, 464, 9], [439, 10, 465, 8, "console"], [439, 17, 465, 15], [439, 18, 465, 16, "log"], [439, 21, 465, 19], [439, 22, 465, 20], [439, 57, 465, 55], [439, 59, 465, 57, "result"], [439, 65, 465, 63], [439, 66, 465, 64], [440, 10, 466, 8, "onComplete"], [440, 20, 466, 18], [440, 21, 466, 19, "result"], [440, 27, 466, 25], [440, 28, 466, 26], [441, 10, 467, 8], [442, 8, 468, 6], [442, 9, 468, 7], [442, 15, 468, 13], [442, 19, 468, 17, "status"], [442, 25, 468, 23], [442, 26, 468, 24, "status"], [442, 32, 468, 30], [442, 37, 468, 35], [442, 45, 468, 43], [442, 47, 468, 45], [443, 10, 469, 8, "console"], [443, 17, 469, 15], [443, 18, 469, 16, "error"], [443, 23, 469, 21], [443, 24, 469, 22], [443, 60, 469, 58], [443, 62, 469, 60, "status"], [443, 68, 469, 66], [443, 69, 469, 67, "error"], [443, 74, 469, 72], [443, 75, 469, 73], [444, 10, 470, 8], [444, 16, 470, 14], [444, 20, 470, 18, "Error"], [444, 25, 470, 23], [444, 26, 470, 24, "status"], [444, 32, 470, 30], [444, 33, 470, 31, "error"], [444, 38, 470, 36], [444, 42, 470, 40], [444, 61, 470, 59], [444, 62, 470, 60], [445, 8, 471, 6], [445, 9, 471, 7], [445, 15, 471, 13], [446, 10, 472, 8], [447, 10, 473, 8], [447, 16, 473, 14, "progressValue"], [447, 29, 473, 27], [447, 32, 473, 30], [447, 34, 473, 32], [447, 37, 473, 36, "attempts"], [447, 45, 473, 44], [447, 48, 473, 47, "MAX_ATTEMPTS"], [447, 60, 473, 59], [447, 63, 473, 63], [447, 65, 473, 65], [448, 10, 474, 8, "console"], [448, 17, 474, 15], [448, 18, 474, 16, "log"], [448, 21, 474, 19], [448, 22, 474, 20], [448, 71, 474, 69, "progressValue"], [448, 84, 474, 82], [448, 87, 474, 85], [448, 88, 474, 86], [449, 10, 475, 8, "setProcessingProgress"], [449, 31, 475, 29], [449, 32, 475, 30, "progressValue"], [449, 45, 475, 43], [449, 46, 475, 44], [450, 10, 477, 8, "setTimeout"], [450, 20, 477, 18], [450, 21, 477, 19], [450, 27, 477, 25], [451, 12, 478, 10, "pollForCompletion"], [451, 29, 478, 27], [451, 30, 478, 28, "jobId"], [451, 35, 478, 33], [451, 37, 478, 35, "timestamp"], [451, 46, 478, 44], [451, 48, 478, 46, "attempts"], [451, 56, 478, 54], [451, 59, 478, 57], [451, 60, 478, 58], [451, 61, 478, 59], [452, 10, 479, 8], [452, 11, 479, 9], [452, 13, 479, 11, "POLL_INTERVAL"], [452, 26, 479, 24], [452, 27, 479, 25], [453, 8, 480, 6], [454, 6, 481, 4], [454, 7, 481, 5], [454, 8, 481, 6], [454, 15, 481, 13, "error"], [454, 20, 481, 18], [454, 22, 481, 20], [455, 8, 482, 6, "console"], [455, 15, 482, 13], [455, 16, 482, 14, "error"], [455, 21, 482, 19], [455, 22, 482, 20], [455, 54, 482, 52], [455, 56, 482, 54, "error"], [455, 61, 482, 59], [455, 62, 482, 60], [456, 8, 483, 6, "setErrorMessage"], [456, 23, 483, 21], [456, 24, 483, 22], [456, 62, 483, 60, "error"], [456, 67, 483, 65], [456, 68, 483, 66, "message"], [456, 75, 483, 73], [456, 77, 483, 75], [456, 78, 483, 76], [457, 8, 484, 6, "setProcessingState"], [457, 26, 484, 24], [457, 27, 484, 25], [457, 34, 484, 32], [457, 35, 484, 33], [458, 6, 485, 4], [459, 4, 486, 2], [459, 5, 486, 3], [460, 4, 487, 2], [461, 4, 488, 2], [461, 10, 488, 8, "getAuthToken"], [461, 22, 488, 20], [461, 25, 488, 23], [461, 31, 488, 23, "getAuthToken"], [461, 32, 488, 23], [461, 37, 488, 52], [462, 6, 489, 4], [463, 6, 490, 4], [464, 6, 491, 4], [464, 13, 491, 11], [464, 30, 491, 28], [465, 4, 492, 2], [465, 5, 492, 3], [467, 4, 494, 2], [468, 4, 495, 2], [468, 10, 495, 8, "retryCapture"], [468, 22, 495, 20], [468, 25, 495, 23], [468, 29, 495, 23, "useCallback"], [468, 47, 495, 34], [468, 49, 495, 35], [468, 55, 495, 41], [469, 6, 496, 4, "console"], [469, 13, 496, 11], [469, 14, 496, 12, "log"], [469, 17, 496, 15], [469, 18, 496, 16], [469, 55, 496, 53], [469, 56, 496, 54], [470, 6, 497, 4, "setProcessingState"], [470, 24, 497, 22], [470, 25, 497, 23], [470, 31, 497, 29], [470, 32, 497, 30], [471, 6, 498, 4, "setErrorMessage"], [471, 21, 498, 19], [471, 22, 498, 20], [471, 24, 498, 22], [471, 25, 498, 23], [472, 6, 499, 4, "setCapturedPhoto"], [472, 22, 499, 20], [472, 23, 499, 21], [472, 25, 499, 23], [472, 26, 499, 24], [473, 6, 500, 4, "setProcessingProgress"], [473, 27, 500, 25], [473, 28, 500, 26], [473, 29, 500, 27], [473, 30, 500, 28], [474, 4, 501, 2], [474, 5, 501, 3], [474, 7, 501, 5], [474, 9, 501, 7], [474, 10, 501, 8], [475, 4, 502, 2], [476, 4, 503, 2], [476, 8, 503, 2, "useEffect"], [476, 24, 503, 11], [476, 26, 503, 12], [476, 32, 503, 18], [477, 6, 504, 4, "console"], [477, 13, 504, 11], [477, 14, 504, 12, "log"], [477, 17, 504, 15], [477, 18, 504, 16], [477, 53, 504, 51], [477, 55, 504, 53, "permission"], [477, 65, 504, 63], [477, 66, 504, 64], [478, 6, 505, 4], [478, 10, 505, 8, "permission"], [478, 20, 505, 18], [478, 22, 505, 20], [479, 8, 506, 6, "console"], [479, 15, 506, 13], [479, 16, 506, 14, "log"], [479, 19, 506, 17], [479, 20, 506, 18], [479, 57, 506, 55], [479, 59, 506, 57, "permission"], [479, 69, 506, 67], [479, 70, 506, 68, "granted"], [479, 77, 506, 75], [479, 78, 506, 76], [480, 6, 507, 4], [481, 4, 508, 2], [481, 5, 508, 3], [481, 7, 508, 5], [481, 8, 508, 6, "permission"], [481, 18, 508, 16], [481, 19, 508, 17], [481, 20, 508, 18], [482, 4, 509, 2], [483, 4, 510, 2], [483, 8, 510, 6], [483, 9, 510, 7, "permission"], [483, 19, 510, 17], [483, 21, 510, 19], [484, 6, 511, 4, "console"], [484, 13, 511, 11], [484, 14, 511, 12, "log"], [484, 17, 511, 15], [484, 18, 511, 16], [484, 67, 511, 65], [484, 68, 511, 66], [485, 6, 512, 4], [485, 26, 513, 6], [485, 30, 513, 6, "_jsxDevRuntime"], [485, 44, 513, 6], [485, 45, 513, 6, "jsxDEV"], [485, 51, 513, 6], [485, 53, 513, 7, "_View"], [485, 58, 513, 7], [485, 59, 513, 7, "default"], [485, 66, 513, 11], [486, 8, 513, 12, "style"], [486, 13, 513, 17], [486, 15, 513, 19, "styles"], [486, 21, 513, 25], [486, 22, 513, 26, "container"], [486, 31, 513, 36], [487, 8, 513, 36, "children"], [487, 16, 513, 36], [487, 32, 514, 8], [487, 36, 514, 8, "_jsxDevRuntime"], [487, 50, 514, 8], [487, 51, 514, 8, "jsxDEV"], [487, 57, 514, 8], [487, 59, 514, 9, "_ActivityIndicator"], [487, 77, 514, 9], [487, 78, 514, 9, "default"], [487, 85, 514, 26], [488, 10, 514, 27, "size"], [488, 14, 514, 31], [488, 16, 514, 32], [488, 23, 514, 39], [489, 10, 514, 40, "color"], [489, 15, 514, 45], [489, 17, 514, 46], [490, 8, 514, 55], [491, 10, 514, 55, "fileName"], [491, 18, 514, 55], [491, 20, 514, 55, "_jsxFileName"], [491, 32, 514, 55], [492, 10, 514, 55, "lineNumber"], [492, 20, 514, 55], [493, 10, 514, 55, "columnNumber"], [493, 22, 514, 55], [494, 8, 514, 55], [494, 15, 514, 57], [494, 16, 514, 58], [494, 31, 515, 8], [494, 35, 515, 8, "_jsxDevRuntime"], [494, 49, 515, 8], [494, 50, 515, 8, "jsxDEV"], [494, 56, 515, 8], [494, 58, 515, 9, "_Text"], [494, 63, 515, 9], [494, 64, 515, 9, "default"], [494, 71, 515, 13], [495, 10, 515, 14, "style"], [495, 15, 515, 19], [495, 17, 515, 21, "styles"], [495, 23, 515, 27], [495, 24, 515, 28, "loadingText"], [495, 35, 515, 40], [496, 10, 515, 40, "children"], [496, 18, 515, 40], [496, 20, 515, 41], [497, 8, 515, 58], [498, 10, 515, 58, "fileName"], [498, 18, 515, 58], [498, 20, 515, 58, "_jsxFileName"], [498, 32, 515, 58], [499, 10, 515, 58, "lineNumber"], [499, 20, 515, 58], [500, 10, 515, 58, "columnNumber"], [500, 22, 515, 58], [501, 8, 515, 58], [501, 15, 515, 64], [501, 16, 515, 65], [502, 6, 515, 65], [503, 8, 515, 65, "fileName"], [503, 16, 515, 65], [503, 18, 515, 65, "_jsxFileName"], [503, 30, 515, 65], [504, 8, 515, 65, "lineNumber"], [504, 18, 515, 65], [505, 8, 515, 65, "columnNumber"], [505, 20, 515, 65], [506, 6, 515, 65], [506, 13, 516, 12], [506, 14, 516, 13], [507, 4, 518, 2], [508, 4, 519, 2], [508, 8, 519, 6], [508, 9, 519, 7, "permission"], [508, 19, 519, 17], [508, 20, 519, 18, "granted"], [508, 27, 519, 25], [508, 29, 519, 27], [509, 6, 520, 4, "console"], [509, 13, 520, 11], [509, 14, 520, 12, "log"], [509, 17, 520, 15], [509, 18, 520, 16], [509, 93, 520, 91], [509, 94, 520, 92], [510, 6, 521, 4], [510, 26, 522, 6], [510, 30, 522, 6, "_jsxDevRuntime"], [510, 44, 522, 6], [510, 45, 522, 6, "jsxDEV"], [510, 51, 522, 6], [510, 53, 522, 7, "_View"], [510, 58, 522, 7], [510, 59, 522, 7, "default"], [510, 66, 522, 11], [511, 8, 522, 12, "style"], [511, 13, 522, 17], [511, 15, 522, 19, "styles"], [511, 21, 522, 25], [511, 22, 522, 26, "container"], [511, 31, 522, 36], [512, 8, 522, 36, "children"], [512, 16, 522, 36], [512, 31, 523, 8], [512, 35, 523, 8, "_jsxDevRuntime"], [512, 49, 523, 8], [512, 50, 523, 8, "jsxDEV"], [512, 56, 523, 8], [512, 58, 523, 9, "_View"], [512, 63, 523, 9], [512, 64, 523, 9, "default"], [512, 71, 523, 13], [513, 10, 523, 14, "style"], [513, 15, 523, 19], [513, 17, 523, 21, "styles"], [513, 23, 523, 27], [513, 24, 523, 28, "permissionContent"], [513, 41, 523, 46], [514, 10, 523, 46, "children"], [514, 18, 523, 46], [514, 34, 524, 10], [514, 38, 524, 10, "_jsxDevRuntime"], [514, 52, 524, 10], [514, 53, 524, 10, "jsxDEV"], [514, 59, 524, 10], [514, 61, 524, 11, "_lucideReactNative"], [514, 79, 524, 11], [514, 80, 524, 11, "Camera"], [514, 86, 524, 21], [515, 12, 524, 22, "size"], [515, 16, 524, 26], [515, 18, 524, 28], [515, 20, 524, 31], [516, 12, 524, 32, "color"], [516, 17, 524, 37], [516, 19, 524, 38], [517, 10, 524, 47], [518, 12, 524, 47, "fileName"], [518, 20, 524, 47], [518, 22, 524, 47, "_jsxFileName"], [518, 34, 524, 47], [519, 12, 524, 47, "lineNumber"], [519, 22, 524, 47], [520, 12, 524, 47, "columnNumber"], [520, 24, 524, 47], [521, 10, 524, 47], [521, 17, 524, 49], [521, 18, 524, 50], [521, 33, 525, 10], [521, 37, 525, 10, "_jsxDevRuntime"], [521, 51, 525, 10], [521, 52, 525, 10, "jsxDEV"], [521, 58, 525, 10], [521, 60, 525, 11, "_Text"], [521, 65, 525, 11], [521, 66, 525, 11, "default"], [521, 73, 525, 15], [522, 12, 525, 16, "style"], [522, 17, 525, 21], [522, 19, 525, 23, "styles"], [522, 25, 525, 29], [522, 26, 525, 30, "permissionTitle"], [522, 41, 525, 46], [523, 12, 525, 46, "children"], [523, 20, 525, 46], [523, 22, 525, 47], [524, 10, 525, 73], [525, 12, 525, 73, "fileName"], [525, 20, 525, 73], [525, 22, 525, 73, "_jsxFileName"], [525, 34, 525, 73], [526, 12, 525, 73, "lineNumber"], [526, 22, 525, 73], [527, 12, 525, 73, "columnNumber"], [527, 24, 525, 73], [528, 10, 525, 73], [528, 17, 525, 79], [528, 18, 525, 80], [528, 33, 526, 10], [528, 37, 526, 10, "_jsxDevRuntime"], [528, 51, 526, 10], [528, 52, 526, 10, "jsxDEV"], [528, 58, 526, 10], [528, 60, 526, 11, "_Text"], [528, 65, 526, 11], [528, 66, 526, 11, "default"], [528, 73, 526, 15], [529, 12, 526, 16, "style"], [529, 17, 526, 21], [529, 19, 526, 23, "styles"], [529, 25, 526, 29], [529, 26, 526, 30, "permissionDescription"], [529, 47, 526, 52], [530, 12, 526, 52, "children"], [530, 20, 526, 52], [530, 22, 526, 53], [531, 10, 529, 10], [532, 12, 529, 10, "fileName"], [532, 20, 529, 10], [532, 22, 529, 10, "_jsxFileName"], [532, 34, 529, 10], [533, 12, 529, 10, "lineNumber"], [533, 22, 529, 10], [534, 12, 529, 10, "columnNumber"], [534, 24, 529, 10], [535, 10, 529, 10], [535, 17, 529, 16], [535, 18, 529, 17], [535, 33, 530, 10], [535, 37, 530, 10, "_jsxDevRuntime"], [535, 51, 530, 10], [535, 52, 530, 10, "jsxDEV"], [535, 58, 530, 10], [535, 60, 530, 11, "_TouchableOpacity"], [535, 77, 530, 11], [535, 78, 530, 11, "default"], [535, 85, 530, 27], [536, 12, 530, 28, "onPress"], [536, 19, 530, 35], [536, 21, 530, 37, "requestPermission"], [536, 38, 530, 55], [537, 12, 530, 56, "style"], [537, 17, 530, 61], [537, 19, 530, 63, "styles"], [537, 25, 530, 69], [537, 26, 530, 70, "primaryButton"], [537, 39, 530, 84], [538, 12, 530, 84, "children"], [538, 20, 530, 84], [538, 35, 531, 12], [538, 39, 531, 12, "_jsxDevRuntime"], [538, 53, 531, 12], [538, 54, 531, 12, "jsxDEV"], [538, 60, 531, 12], [538, 62, 531, 13, "_Text"], [538, 67, 531, 13], [538, 68, 531, 13, "default"], [538, 75, 531, 17], [539, 14, 531, 18, "style"], [539, 19, 531, 23], [539, 21, 531, 25, "styles"], [539, 27, 531, 31], [539, 28, 531, 32, "primaryButtonText"], [539, 45, 531, 50], [540, 14, 531, 50, "children"], [540, 22, 531, 50], [540, 24, 531, 51], [541, 12, 531, 67], [542, 14, 531, 67, "fileName"], [542, 22, 531, 67], [542, 24, 531, 67, "_jsxFileName"], [542, 36, 531, 67], [543, 14, 531, 67, "lineNumber"], [543, 24, 531, 67], [544, 14, 531, 67, "columnNumber"], [544, 26, 531, 67], [545, 12, 531, 67], [545, 19, 531, 73], [546, 10, 531, 74], [547, 12, 531, 74, "fileName"], [547, 20, 531, 74], [547, 22, 531, 74, "_jsxFileName"], [547, 34, 531, 74], [548, 12, 531, 74, "lineNumber"], [548, 22, 531, 74], [549, 12, 531, 74, "columnNumber"], [549, 24, 531, 74], [550, 10, 531, 74], [550, 17, 532, 28], [550, 18, 532, 29], [550, 33, 533, 10], [550, 37, 533, 10, "_jsxDevRuntime"], [550, 51, 533, 10], [550, 52, 533, 10, "jsxDEV"], [550, 58, 533, 10], [550, 60, 533, 11, "_TouchableOpacity"], [550, 77, 533, 11], [550, 78, 533, 11, "default"], [550, 85, 533, 27], [551, 12, 533, 28, "onPress"], [551, 19, 533, 35], [551, 21, 533, 37, "onCancel"], [551, 29, 533, 46], [552, 12, 533, 47, "style"], [552, 17, 533, 52], [552, 19, 533, 54, "styles"], [552, 25, 533, 60], [552, 26, 533, 61, "secondaryButton"], [552, 41, 533, 77], [553, 12, 533, 77, "children"], [553, 20, 533, 77], [553, 35, 534, 12], [553, 39, 534, 12, "_jsxDevRuntime"], [553, 53, 534, 12], [553, 54, 534, 12, "jsxDEV"], [553, 60, 534, 12], [553, 62, 534, 13, "_Text"], [553, 67, 534, 13], [553, 68, 534, 13, "default"], [553, 75, 534, 17], [554, 14, 534, 18, "style"], [554, 19, 534, 23], [554, 21, 534, 25, "styles"], [554, 27, 534, 31], [554, 28, 534, 32, "secondaryButtonText"], [554, 47, 534, 52], [555, 14, 534, 52, "children"], [555, 22, 534, 52], [555, 24, 534, 53], [556, 12, 534, 59], [557, 14, 534, 59, "fileName"], [557, 22, 534, 59], [557, 24, 534, 59, "_jsxFileName"], [557, 36, 534, 59], [558, 14, 534, 59, "lineNumber"], [558, 24, 534, 59], [559, 14, 534, 59, "columnNumber"], [559, 26, 534, 59], [560, 12, 534, 59], [560, 19, 534, 65], [561, 10, 534, 66], [562, 12, 534, 66, "fileName"], [562, 20, 534, 66], [562, 22, 534, 66, "_jsxFileName"], [562, 34, 534, 66], [563, 12, 534, 66, "lineNumber"], [563, 22, 534, 66], [564, 12, 534, 66, "columnNumber"], [564, 24, 534, 66], [565, 10, 534, 66], [565, 17, 535, 28], [565, 18, 535, 29], [566, 8, 535, 29], [567, 10, 535, 29, "fileName"], [567, 18, 535, 29], [567, 20, 535, 29, "_jsxFileName"], [567, 32, 535, 29], [568, 10, 535, 29, "lineNumber"], [568, 20, 535, 29], [569, 10, 535, 29, "columnNumber"], [569, 22, 535, 29], [570, 8, 535, 29], [570, 15, 536, 14], [571, 6, 536, 15], [572, 8, 536, 15, "fileName"], [572, 16, 536, 15], [572, 18, 536, 15, "_jsxFileName"], [572, 30, 536, 15], [573, 8, 536, 15, "lineNumber"], [573, 18, 536, 15], [574, 8, 536, 15, "columnNumber"], [574, 20, 536, 15], [575, 6, 536, 15], [575, 13, 537, 12], [575, 14, 537, 13], [576, 4, 539, 2], [577, 4, 540, 2], [578, 4, 541, 2, "console"], [578, 11, 541, 9], [578, 12, 541, 10, "log"], [578, 15, 541, 13], [578, 16, 541, 14], [578, 55, 541, 53], [578, 56, 541, 54], [579, 4, 543, 2], [579, 24, 544, 4], [579, 28, 544, 4, "_jsxDevRuntime"], [579, 42, 544, 4], [579, 43, 544, 4, "jsxDEV"], [579, 49, 544, 4], [579, 51, 544, 5, "_View"], [579, 56, 544, 5], [579, 57, 544, 5, "default"], [579, 64, 544, 9], [580, 6, 544, 10, "style"], [580, 11, 544, 15], [580, 13, 544, 17, "styles"], [580, 19, 544, 23], [580, 20, 544, 24, "container"], [580, 29, 544, 34], [581, 6, 544, 34, "children"], [581, 14, 544, 34], [581, 30, 546, 6], [581, 34, 546, 6, "_jsxDevRuntime"], [581, 48, 546, 6], [581, 49, 546, 6, "jsxDEV"], [581, 55, 546, 6], [581, 57, 546, 7, "_View"], [581, 62, 546, 7], [581, 63, 546, 7, "default"], [581, 70, 546, 11], [582, 8, 546, 12, "style"], [582, 13, 546, 17], [582, 15, 546, 19, "styles"], [582, 21, 546, 25], [582, 22, 546, 26, "cameraContainer"], [582, 37, 546, 42], [583, 8, 546, 43, "id"], [583, 10, 546, 45], [583, 12, 546, 46], [583, 29, 546, 63], [584, 8, 546, 63, "children"], [584, 16, 546, 63], [584, 32, 547, 8], [584, 36, 547, 8, "_jsxDevRuntime"], [584, 50, 547, 8], [584, 51, 547, 8, "jsxDEV"], [584, 57, 547, 8], [584, 59, 547, 9, "_expoCamera"], [584, 70, 547, 9], [584, 71, 547, 9, "CameraView"], [584, 81, 547, 19], [585, 10, 548, 10, "ref"], [585, 13, 548, 13], [585, 15, 548, 15, "cameraRef"], [585, 24, 548, 25], [586, 10, 549, 10, "style"], [586, 15, 549, 15], [586, 17, 549, 17], [586, 18, 549, 18, "styles"], [586, 24, 549, 24], [586, 25, 549, 25, "camera"], [586, 31, 549, 31], [586, 33, 549, 33], [587, 12, 549, 35, "backgroundColor"], [587, 27, 549, 50], [587, 29, 549, 52], [588, 10, 549, 62], [588, 11, 549, 63], [588, 12, 549, 65], [589, 10, 550, 10, "facing"], [589, 16, 550, 16], [589, 18, 550, 17], [589, 24, 550, 23], [590, 10, 551, 10, "onLayout"], [590, 18, 551, 18], [590, 20, 551, 21, "e"], [590, 21, 551, 22], [590, 25, 551, 27], [591, 12, 552, 12, "console"], [591, 19, 552, 19], [591, 20, 552, 20, "log"], [591, 23, 552, 23], [591, 24, 552, 24], [591, 56, 552, 56], [591, 58, 552, 58, "e"], [591, 59, 552, 59], [591, 60, 552, 60, "nativeEvent"], [591, 71, 552, 71], [591, 72, 552, 72, "layout"], [591, 78, 552, 78], [591, 79, 552, 79], [592, 12, 553, 12, "setViewSize"], [592, 23, 553, 23], [592, 24, 553, 24], [593, 14, 553, 26, "width"], [593, 19, 553, 31], [593, 21, 553, 33, "e"], [593, 22, 553, 34], [593, 23, 553, 35, "nativeEvent"], [593, 34, 553, 46], [593, 35, 553, 47, "layout"], [593, 41, 553, 53], [593, 42, 553, 54, "width"], [593, 47, 553, 59], [594, 14, 553, 61, "height"], [594, 20, 553, 67], [594, 22, 553, 69, "e"], [594, 23, 553, 70], [594, 24, 553, 71, "nativeEvent"], [594, 35, 553, 82], [594, 36, 553, 83, "layout"], [594, 42, 553, 89], [594, 43, 553, 90, "height"], [595, 12, 553, 97], [595, 13, 553, 98], [595, 14, 553, 99], [596, 10, 554, 10], [596, 11, 554, 12], [597, 10, 555, 10, "onCameraReady"], [597, 23, 555, 23], [597, 25, 555, 25, "onCameraReady"], [597, 26, 555, 25], [597, 31, 555, 31], [598, 12, 556, 12, "console"], [598, 19, 556, 19], [598, 20, 556, 20, "log"], [598, 23, 556, 23], [598, 24, 556, 24], [598, 55, 556, 55], [598, 56, 556, 56], [599, 12, 557, 12, "setIsCameraReady"], [599, 28, 557, 28], [599, 29, 557, 29], [599, 33, 557, 33], [599, 34, 557, 34], [599, 35, 557, 35], [599, 36, 557, 36], [600, 10, 558, 10], [600, 11, 558, 12], [601, 10, 559, 10, "onMountError"], [601, 22, 559, 22], [601, 24, 559, 25, "error"], [601, 29, 559, 30], [601, 33, 559, 35], [602, 12, 560, 12, "console"], [602, 19, 560, 19], [602, 20, 560, 20, "error"], [602, 25, 560, 25], [602, 26, 560, 26], [602, 63, 560, 63], [602, 65, 560, 65, "error"], [602, 70, 560, 70], [602, 71, 560, 71], [603, 12, 561, 12, "setErrorMessage"], [603, 27, 561, 27], [603, 28, 561, 28], [603, 57, 561, 57], [603, 58, 561, 58], [604, 12, 562, 12, "setProcessingState"], [604, 30, 562, 30], [604, 31, 562, 31], [604, 38, 562, 38], [604, 39, 562, 39], [605, 10, 563, 10], [606, 8, 563, 12], [607, 10, 563, 12, "fileName"], [607, 18, 563, 12], [607, 20, 563, 12, "_jsxFileName"], [607, 32, 563, 12], [608, 10, 563, 12, "lineNumber"], [608, 20, 563, 12], [609, 10, 563, 12, "columnNumber"], [609, 22, 563, 12], [610, 8, 563, 12], [610, 15, 564, 9], [610, 16, 564, 10], [610, 18, 566, 9], [610, 19, 566, 10, "isCameraReady"], [610, 32, 566, 23], [610, 49, 567, 10], [610, 53, 567, 10, "_jsxDevRuntime"], [610, 67, 567, 10], [610, 68, 567, 10, "jsxDEV"], [610, 74, 567, 10], [610, 76, 567, 11, "_View"], [610, 81, 567, 11], [610, 82, 567, 11, "default"], [610, 89, 567, 15], [611, 10, 567, 16, "style"], [611, 15, 567, 21], [611, 17, 567, 23], [611, 18, 567, 24, "StyleSheet"], [611, 37, 567, 34], [611, 38, 567, 35, "absoluteFill"], [611, 50, 567, 47], [611, 52, 567, 49], [612, 12, 567, 51, "backgroundColor"], [612, 27, 567, 66], [612, 29, 567, 68], [612, 49, 567, 88], [613, 12, 567, 90, "justifyContent"], [613, 26, 567, 104], [613, 28, 567, 106], [613, 36, 567, 114], [614, 12, 567, 116, "alignItems"], [614, 22, 567, 126], [614, 24, 567, 128], [614, 32, 567, 136], [615, 12, 567, 138, "zIndex"], [615, 18, 567, 144], [615, 20, 567, 146], [616, 10, 567, 151], [616, 11, 567, 152], [616, 12, 567, 154], [617, 10, 567, 154, "children"], [617, 18, 567, 154], [617, 33, 568, 12], [617, 37, 568, 12, "_jsxDevRuntime"], [617, 51, 568, 12], [617, 52, 568, 12, "jsxDEV"], [617, 58, 568, 12], [617, 60, 568, 13, "_View"], [617, 65, 568, 13], [617, 66, 568, 13, "default"], [617, 73, 568, 17], [618, 12, 568, 18, "style"], [618, 17, 568, 23], [618, 19, 568, 25], [619, 14, 568, 27, "backgroundColor"], [619, 29, 568, 42], [619, 31, 568, 44], [619, 51, 568, 64], [620, 14, 568, 66, "padding"], [620, 21, 568, 73], [620, 23, 568, 75], [620, 25, 568, 77], [621, 14, 568, 79, "borderRadius"], [621, 26, 568, 91], [621, 28, 568, 93], [621, 30, 568, 95], [622, 14, 568, 97, "alignItems"], [622, 24, 568, 107], [622, 26, 568, 109], [623, 12, 568, 118], [623, 13, 568, 120], [624, 12, 568, 120, "children"], [624, 20, 568, 120], [624, 36, 569, 14], [624, 40, 569, 14, "_jsxDevRuntime"], [624, 54, 569, 14], [624, 55, 569, 14, "jsxDEV"], [624, 61, 569, 14], [624, 63, 569, 15, "_ActivityIndicator"], [624, 81, 569, 15], [624, 82, 569, 15, "default"], [624, 89, 569, 32], [625, 14, 569, 33, "size"], [625, 18, 569, 37], [625, 20, 569, 38], [625, 27, 569, 45], [626, 14, 569, 46, "color"], [626, 19, 569, 51], [626, 21, 569, 52], [626, 30, 569, 61], [627, 14, 569, 62, "style"], [627, 19, 569, 67], [627, 21, 569, 69], [628, 16, 569, 71, "marginBottom"], [628, 28, 569, 83], [628, 30, 569, 85], [629, 14, 569, 88], [630, 12, 569, 90], [631, 14, 569, 90, "fileName"], [631, 22, 569, 90], [631, 24, 569, 90, "_jsxFileName"], [631, 36, 569, 90], [632, 14, 569, 90, "lineNumber"], [632, 24, 569, 90], [633, 14, 569, 90, "columnNumber"], [633, 26, 569, 90], [634, 12, 569, 90], [634, 19, 569, 92], [634, 20, 569, 93], [634, 35, 570, 14], [634, 39, 570, 14, "_jsxDevRuntime"], [634, 53, 570, 14], [634, 54, 570, 14, "jsxDEV"], [634, 60, 570, 14], [634, 62, 570, 15, "_Text"], [634, 67, 570, 15], [634, 68, 570, 15, "default"], [634, 75, 570, 19], [635, 14, 570, 20, "style"], [635, 19, 570, 25], [635, 21, 570, 27], [636, 16, 570, 29, "color"], [636, 21, 570, 34], [636, 23, 570, 36], [636, 29, 570, 42], [637, 16, 570, 44, "fontSize"], [637, 24, 570, 52], [637, 26, 570, 54], [637, 28, 570, 56], [638, 16, 570, 58, "fontWeight"], [638, 26, 570, 68], [638, 28, 570, 70], [639, 14, 570, 76], [639, 15, 570, 78], [640, 14, 570, 78, "children"], [640, 22, 570, 78], [640, 24, 570, 79], [641, 12, 570, 101], [642, 14, 570, 101, "fileName"], [642, 22, 570, 101], [642, 24, 570, 101, "_jsxFileName"], [642, 36, 570, 101], [643, 14, 570, 101, "lineNumber"], [643, 24, 570, 101], [644, 14, 570, 101, "columnNumber"], [644, 26, 570, 101], [645, 12, 570, 101], [645, 19, 570, 107], [645, 20, 570, 108], [645, 35, 571, 14], [645, 39, 571, 14, "_jsxDevRuntime"], [645, 53, 571, 14], [645, 54, 571, 14, "jsxDEV"], [645, 60, 571, 14], [645, 62, 571, 15, "_Text"], [645, 67, 571, 15], [645, 68, 571, 15, "default"], [645, 75, 571, 19], [646, 14, 571, 20, "style"], [646, 19, 571, 25], [646, 21, 571, 27], [647, 16, 571, 29, "color"], [647, 21, 571, 34], [647, 23, 571, 36], [647, 32, 571, 45], [648, 16, 571, 47, "fontSize"], [648, 24, 571, 55], [648, 26, 571, 57], [648, 28, 571, 59], [649, 16, 571, 61, "marginTop"], [649, 25, 571, 70], [649, 27, 571, 72], [650, 14, 571, 74], [650, 15, 571, 76], [651, 14, 571, 76, "children"], [651, 22, 571, 76], [651, 24, 571, 77], [652, 12, 571, 88], [653, 14, 571, 88, "fileName"], [653, 22, 571, 88], [653, 24, 571, 88, "_jsxFileName"], [653, 36, 571, 88], [654, 14, 571, 88, "lineNumber"], [654, 24, 571, 88], [655, 14, 571, 88, "columnNumber"], [655, 26, 571, 88], [656, 12, 571, 88], [656, 19, 571, 94], [656, 20, 571, 95], [657, 10, 571, 95], [658, 12, 571, 95, "fileName"], [658, 20, 571, 95], [658, 22, 571, 95, "_jsxFileName"], [658, 34, 571, 95], [659, 12, 571, 95, "lineNumber"], [659, 22, 571, 95], [660, 12, 571, 95, "columnNumber"], [660, 24, 571, 95], [661, 10, 571, 95], [661, 17, 572, 18], [662, 8, 572, 19], [663, 10, 572, 19, "fileName"], [663, 18, 572, 19], [663, 20, 572, 19, "_jsxFileName"], [663, 32, 572, 19], [664, 10, 572, 19, "lineNumber"], [664, 20, 572, 19], [665, 10, 572, 19, "columnNumber"], [665, 22, 572, 19], [666, 8, 572, 19], [666, 15, 573, 16], [666, 16, 574, 9], [666, 18, 577, 9, "isCameraReady"], [666, 31, 577, 22], [666, 35, 577, 26, "previewBlurEnabled"], [666, 53, 577, 44], [666, 57, 577, 48, "viewSize"], [666, 65, 577, 56], [666, 66, 577, 57, "width"], [666, 71, 577, 62], [666, 74, 577, 65], [666, 75, 577, 66], [666, 92, 578, 10], [666, 96, 578, 10, "_jsxDevRuntime"], [666, 110, 578, 10], [666, 111, 578, 10, "jsxDEV"], [666, 117, 578, 10], [666, 119, 578, 10, "_jsxDevRuntime"], [666, 133, 578, 10], [666, 134, 578, 10, "Fragment"], [666, 142, 578, 10], [667, 10, 578, 10, "children"], [667, 18, 578, 10], [667, 34, 580, 12], [667, 38, 580, 12, "_jsxDevRuntime"], [667, 52, 580, 12], [667, 53, 580, 12, "jsxDEV"], [667, 59, 580, 12], [667, 61, 580, 13, "_LiveFaceCanvas"], [667, 76, 580, 13], [667, 77, 580, 13, "default"], [667, 84, 580, 27], [668, 12, 580, 28, "containerId"], [668, 23, 580, 39], [668, 25, 580, 40], [668, 42, 580, 57], [669, 12, 580, 58, "width"], [669, 17, 580, 63], [669, 19, 580, 65, "viewSize"], [669, 27, 580, 73], [669, 28, 580, 74, "width"], [669, 33, 580, 80], [670, 12, 580, 81, "height"], [670, 18, 580, 87], [670, 20, 580, 89, "viewSize"], [670, 28, 580, 97], [670, 29, 580, 98, "height"], [671, 10, 580, 105], [672, 12, 580, 105, "fileName"], [672, 20, 580, 105], [672, 22, 580, 105, "_jsxFileName"], [672, 34, 580, 105], [673, 12, 580, 105, "lineNumber"], [673, 22, 580, 105], [674, 12, 580, 105, "columnNumber"], [674, 24, 580, 105], [675, 10, 580, 105], [675, 17, 580, 107], [675, 18, 580, 108], [675, 33, 581, 12], [675, 37, 581, 12, "_jsxDevRuntime"], [675, 51, 581, 12], [675, 52, 581, 12, "jsxDEV"], [675, 58, 581, 12], [675, 60, 581, 13, "_View"], [675, 65, 581, 13], [675, 66, 581, 13, "default"], [675, 73, 581, 17], [676, 12, 581, 18, "style"], [676, 17, 581, 23], [676, 19, 581, 25], [676, 20, 581, 26, "StyleSheet"], [676, 39, 581, 36], [676, 40, 581, 37, "absoluteFill"], [676, 52, 581, 49], [676, 54, 581, 51], [677, 14, 581, 53, "pointerEvents"], [677, 27, 581, 66], [677, 29, 581, 68], [678, 12, 581, 75], [678, 13, 581, 76], [678, 14, 581, 78], [679, 12, 581, 78, "children"], [679, 20, 581, 78], [679, 36, 583, 12], [679, 40, 583, 12, "_jsxDevRuntime"], [679, 54, 583, 12], [679, 55, 583, 12, "jsxDEV"], [679, 61, 583, 12], [679, 63, 583, 13, "_expoBlur"], [679, 72, 583, 13], [679, 73, 583, 13, "BlurView"], [679, 81, 583, 21], [680, 14, 583, 22, "intensity"], [680, 23, 583, 31], [680, 25, 583, 33], [680, 27, 583, 36], [681, 14, 583, 37, "tint"], [681, 18, 583, 41], [681, 20, 583, 42], [681, 26, 583, 48], [682, 14, 583, 49, "style"], [682, 19, 583, 54], [682, 21, 583, 56], [682, 22, 583, 57, "styles"], [682, 28, 583, 63], [682, 29, 583, 64, "blurZone"], [682, 37, 583, 72], [682, 39, 583, 74], [683, 16, 584, 14, "left"], [683, 20, 584, 18], [683, 22, 584, 20], [683, 23, 584, 21], [684, 16, 585, 14, "top"], [684, 19, 585, 17], [684, 21, 585, 19, "viewSize"], [684, 29, 585, 27], [684, 30, 585, 28, "height"], [684, 36, 585, 34], [684, 39, 585, 37], [684, 42, 585, 40], [685, 16, 586, 14, "width"], [685, 21, 586, 19], [685, 23, 586, 21, "viewSize"], [685, 31, 586, 29], [685, 32, 586, 30, "width"], [685, 37, 586, 35], [686, 16, 587, 14, "height"], [686, 22, 587, 20], [686, 24, 587, 22, "viewSize"], [686, 32, 587, 30], [686, 33, 587, 31, "height"], [686, 39, 587, 37], [686, 42, 587, 40], [686, 46, 587, 44], [687, 16, 588, 14, "borderRadius"], [687, 28, 588, 26], [687, 30, 588, 28], [688, 14, 589, 12], [688, 15, 589, 13], [689, 12, 589, 15], [690, 14, 589, 15, "fileName"], [690, 22, 589, 15], [690, 24, 589, 15, "_jsxFileName"], [690, 36, 589, 15], [691, 14, 589, 15, "lineNumber"], [691, 24, 589, 15], [692, 14, 589, 15, "columnNumber"], [692, 26, 589, 15], [693, 12, 589, 15], [693, 19, 589, 17], [693, 20, 589, 18], [693, 35, 591, 12], [693, 39, 591, 12, "_jsxDevRuntime"], [693, 53, 591, 12], [693, 54, 591, 12, "jsxDEV"], [693, 60, 591, 12], [693, 62, 591, 13, "_expoBlur"], [693, 71, 591, 13], [693, 72, 591, 13, "BlurView"], [693, 80, 591, 21], [694, 14, 591, 22, "intensity"], [694, 23, 591, 31], [694, 25, 591, 33], [694, 27, 591, 36], [695, 14, 591, 37, "tint"], [695, 18, 591, 41], [695, 20, 591, 42], [695, 26, 591, 48], [696, 14, 591, 49, "style"], [696, 19, 591, 54], [696, 21, 591, 56], [696, 22, 591, 57, "styles"], [696, 28, 591, 63], [696, 29, 591, 64, "blurZone"], [696, 37, 591, 72], [696, 39, 591, 74], [697, 16, 592, 14, "left"], [697, 20, 592, 18], [697, 22, 592, 20], [697, 23, 592, 21], [698, 16, 593, 14, "top"], [698, 19, 593, 17], [698, 21, 593, 19], [698, 22, 593, 20], [699, 16, 594, 14, "width"], [699, 21, 594, 19], [699, 23, 594, 21, "viewSize"], [699, 31, 594, 29], [699, 32, 594, 30, "width"], [699, 37, 594, 35], [700, 16, 595, 14, "height"], [700, 22, 595, 20], [700, 24, 595, 22, "viewSize"], [700, 32, 595, 30], [700, 33, 595, 31, "height"], [700, 39, 595, 37], [700, 42, 595, 40], [700, 45, 595, 43], [701, 16, 596, 14, "borderRadius"], [701, 28, 596, 26], [701, 30, 596, 28], [702, 14, 597, 12], [702, 15, 597, 13], [703, 12, 597, 15], [704, 14, 597, 15, "fileName"], [704, 22, 597, 15], [704, 24, 597, 15, "_jsxFileName"], [704, 36, 597, 15], [705, 14, 597, 15, "lineNumber"], [705, 24, 597, 15], [706, 14, 597, 15, "columnNumber"], [706, 26, 597, 15], [707, 12, 597, 15], [707, 19, 597, 17], [707, 20, 597, 18], [707, 35, 599, 12], [707, 39, 599, 12, "_jsxDevRuntime"], [707, 53, 599, 12], [707, 54, 599, 12, "jsxDEV"], [707, 60, 599, 12], [707, 62, 599, 13, "_expoBlur"], [707, 71, 599, 13], [707, 72, 599, 13, "BlurView"], [707, 80, 599, 21], [708, 14, 599, 22, "intensity"], [708, 23, 599, 31], [708, 25, 599, 33], [708, 27, 599, 36], [709, 14, 599, 37, "tint"], [709, 18, 599, 41], [709, 20, 599, 42], [709, 26, 599, 48], [710, 14, 599, 49, "style"], [710, 19, 599, 54], [710, 21, 599, 56], [710, 22, 599, 57, "styles"], [710, 28, 599, 63], [710, 29, 599, 64, "blurZone"], [710, 37, 599, 72], [710, 39, 599, 74], [711, 16, 600, 14, "left"], [711, 20, 600, 18], [711, 22, 600, 20, "viewSize"], [711, 30, 600, 28], [711, 31, 600, 29, "width"], [711, 36, 600, 34], [711, 39, 600, 37], [711, 42, 600, 40], [711, 45, 600, 44, "viewSize"], [711, 53, 600, 52], [711, 54, 600, 53, "width"], [711, 59, 600, 58], [711, 62, 600, 61], [711, 66, 600, 66], [712, 16, 601, 14, "top"], [712, 19, 601, 17], [712, 21, 601, 19, "viewSize"], [712, 29, 601, 27], [712, 30, 601, 28, "height"], [712, 36, 601, 34], [712, 39, 601, 37], [712, 43, 601, 41], [712, 46, 601, 45, "viewSize"], [712, 54, 601, 53], [712, 55, 601, 54, "width"], [712, 60, 601, 59], [712, 63, 601, 62], [712, 67, 601, 67], [713, 16, 602, 14, "width"], [713, 21, 602, 19], [713, 23, 602, 21, "viewSize"], [713, 31, 602, 29], [713, 32, 602, 30, "width"], [713, 37, 602, 35], [713, 40, 602, 38], [713, 43, 602, 41], [714, 16, 603, 14, "height"], [714, 22, 603, 20], [714, 24, 603, 22, "viewSize"], [714, 32, 603, 30], [714, 33, 603, 31, "width"], [714, 38, 603, 36], [714, 41, 603, 39], [714, 44, 603, 42], [715, 16, 604, 14, "borderRadius"], [715, 28, 604, 26], [715, 30, 604, 29, "viewSize"], [715, 38, 604, 37], [715, 39, 604, 38, "width"], [715, 44, 604, 43], [715, 47, 604, 46], [715, 50, 604, 49], [715, 53, 604, 53], [716, 14, 605, 12], [716, 15, 605, 13], [717, 12, 605, 15], [718, 14, 605, 15, "fileName"], [718, 22, 605, 15], [718, 24, 605, 15, "_jsxFileName"], [718, 36, 605, 15], [719, 14, 605, 15, "lineNumber"], [719, 24, 605, 15], [720, 14, 605, 15, "columnNumber"], [720, 26, 605, 15], [721, 12, 605, 15], [721, 19, 605, 17], [721, 20, 605, 18], [721, 35, 606, 12], [721, 39, 606, 12, "_jsxDevRuntime"], [721, 53, 606, 12], [721, 54, 606, 12, "jsxDEV"], [721, 60, 606, 12], [721, 62, 606, 13, "_expoBlur"], [721, 71, 606, 13], [721, 72, 606, 13, "BlurView"], [721, 80, 606, 21], [722, 14, 606, 22, "intensity"], [722, 23, 606, 31], [722, 25, 606, 33], [722, 27, 606, 36], [723, 14, 606, 37, "tint"], [723, 18, 606, 41], [723, 20, 606, 42], [723, 26, 606, 48], [724, 14, 606, 49, "style"], [724, 19, 606, 54], [724, 21, 606, 56], [724, 22, 606, 57, "styles"], [724, 28, 606, 63], [724, 29, 606, 64, "blurZone"], [724, 37, 606, 72], [724, 39, 606, 74], [725, 16, 607, 14, "left"], [725, 20, 607, 18], [725, 22, 607, 20, "viewSize"], [725, 30, 607, 28], [725, 31, 607, 29, "width"], [725, 36, 607, 34], [725, 39, 607, 37], [725, 42, 607, 40], [725, 45, 607, 44, "viewSize"], [725, 53, 607, 52], [725, 54, 607, 53, "width"], [725, 59, 607, 58], [725, 62, 607, 61], [725, 66, 607, 66], [726, 16, 608, 14, "top"], [726, 19, 608, 17], [726, 21, 608, 19, "viewSize"], [726, 29, 608, 27], [726, 30, 608, 28, "height"], [726, 36, 608, 34], [726, 39, 608, 37], [726, 42, 608, 40], [726, 45, 608, 44, "viewSize"], [726, 53, 608, 52], [726, 54, 608, 53, "width"], [726, 59, 608, 58], [726, 62, 608, 61], [726, 66, 608, 66], [727, 16, 609, 14, "width"], [727, 21, 609, 19], [727, 23, 609, 21, "viewSize"], [727, 31, 609, 29], [727, 32, 609, 30, "width"], [727, 37, 609, 35], [727, 40, 609, 38], [727, 43, 609, 41], [728, 16, 610, 14, "height"], [728, 22, 610, 20], [728, 24, 610, 22, "viewSize"], [728, 32, 610, 30], [728, 33, 610, 31, "width"], [728, 38, 610, 36], [728, 41, 610, 39], [728, 44, 610, 42], [729, 16, 611, 14, "borderRadius"], [729, 28, 611, 26], [729, 30, 611, 29, "viewSize"], [729, 38, 611, 37], [729, 39, 611, 38, "width"], [729, 44, 611, 43], [729, 47, 611, 46], [729, 50, 611, 49], [729, 53, 611, 53], [730, 14, 612, 12], [730, 15, 612, 13], [731, 12, 612, 15], [732, 14, 612, 15, "fileName"], [732, 22, 612, 15], [732, 24, 612, 15, "_jsxFileName"], [732, 36, 612, 15], [733, 14, 612, 15, "lineNumber"], [733, 24, 612, 15], [734, 14, 612, 15, "columnNumber"], [734, 26, 612, 15], [735, 12, 612, 15], [735, 19, 612, 17], [735, 20, 612, 18], [735, 35, 613, 12], [735, 39, 613, 12, "_jsxDevRuntime"], [735, 53, 613, 12], [735, 54, 613, 12, "jsxDEV"], [735, 60, 613, 12], [735, 62, 613, 13, "_expoBlur"], [735, 71, 613, 13], [735, 72, 613, 13, "BlurView"], [735, 80, 613, 21], [736, 14, 613, 22, "intensity"], [736, 23, 613, 31], [736, 25, 613, 33], [736, 27, 613, 36], [737, 14, 613, 37, "tint"], [737, 18, 613, 41], [737, 20, 613, 42], [737, 26, 613, 48], [738, 14, 613, 49, "style"], [738, 19, 613, 54], [738, 21, 613, 56], [738, 22, 613, 57, "styles"], [738, 28, 613, 63], [738, 29, 613, 64, "blurZone"], [738, 37, 613, 72], [738, 39, 613, 74], [739, 16, 614, 14, "left"], [739, 20, 614, 18], [739, 22, 614, 20, "viewSize"], [739, 30, 614, 28], [739, 31, 614, 29, "width"], [739, 36, 614, 34], [739, 39, 614, 37], [739, 42, 614, 40], [739, 45, 614, 44, "viewSize"], [739, 53, 614, 52], [739, 54, 614, 53, "width"], [739, 59, 614, 58], [739, 62, 614, 61], [739, 66, 614, 66], [740, 16, 615, 14, "top"], [740, 19, 615, 17], [740, 21, 615, 19, "viewSize"], [740, 29, 615, 27], [740, 30, 615, 28, "height"], [740, 36, 615, 34], [740, 39, 615, 37], [740, 42, 615, 40], [740, 45, 615, 44, "viewSize"], [740, 53, 615, 52], [740, 54, 615, 53, "width"], [740, 59, 615, 58], [740, 62, 615, 61], [740, 66, 615, 66], [741, 16, 616, 14, "width"], [741, 21, 616, 19], [741, 23, 616, 21, "viewSize"], [741, 31, 616, 29], [741, 32, 616, 30, "width"], [741, 37, 616, 35], [741, 40, 616, 38], [741, 43, 616, 41], [742, 16, 617, 14, "height"], [742, 22, 617, 20], [742, 24, 617, 22, "viewSize"], [742, 32, 617, 30], [742, 33, 617, 31, "width"], [742, 38, 617, 36], [742, 41, 617, 39], [742, 44, 617, 42], [743, 16, 618, 14, "borderRadius"], [743, 28, 618, 26], [743, 30, 618, 29, "viewSize"], [743, 38, 618, 37], [743, 39, 618, 38, "width"], [743, 44, 618, 43], [743, 47, 618, 46], [743, 50, 618, 49], [743, 53, 618, 53], [744, 14, 619, 12], [744, 15, 619, 13], [745, 12, 619, 15], [746, 14, 619, 15, "fileName"], [746, 22, 619, 15], [746, 24, 619, 15, "_jsxFileName"], [746, 36, 619, 15], [747, 14, 619, 15, "lineNumber"], [747, 24, 619, 15], [748, 14, 619, 15, "columnNumber"], [748, 26, 619, 15], [749, 12, 619, 15], [749, 19, 619, 17], [749, 20, 619, 18], [749, 22, 621, 13, "__DEV__"], [749, 29, 621, 20], [749, 46, 622, 14], [749, 50, 622, 14, "_jsxDevRuntime"], [749, 64, 622, 14], [749, 65, 622, 14, "jsxDEV"], [749, 71, 622, 14], [749, 73, 622, 15, "_View"], [749, 78, 622, 15], [749, 79, 622, 15, "default"], [749, 86, 622, 19], [750, 14, 622, 20, "style"], [750, 19, 622, 25], [750, 21, 622, 27, "styles"], [750, 27, 622, 33], [750, 28, 622, 34, "previewChip"], [750, 39, 622, 46], [751, 14, 622, 46, "children"], [751, 22, 622, 46], [751, 37, 623, 16], [751, 41, 623, 16, "_jsxDevRuntime"], [751, 55, 623, 16], [751, 56, 623, 16, "jsxDEV"], [751, 62, 623, 16], [751, 64, 623, 17, "_Text"], [751, 69, 623, 17], [751, 70, 623, 17, "default"], [751, 77, 623, 21], [752, 16, 623, 22, "style"], [752, 21, 623, 27], [752, 23, 623, 29, "styles"], [752, 29, 623, 35], [752, 30, 623, 36, "previewChipText"], [752, 45, 623, 52], [753, 16, 623, 52, "children"], [753, 24, 623, 52], [753, 26, 623, 53], [754, 14, 623, 73], [755, 16, 623, 73, "fileName"], [755, 24, 623, 73], [755, 26, 623, 73, "_jsxFileName"], [755, 38, 623, 73], [756, 16, 623, 73, "lineNumber"], [756, 26, 623, 73], [757, 16, 623, 73, "columnNumber"], [757, 28, 623, 73], [758, 14, 623, 73], [758, 21, 623, 79], [759, 12, 623, 80], [760, 14, 623, 80, "fileName"], [760, 22, 623, 80], [760, 24, 623, 80, "_jsxFileName"], [760, 36, 623, 80], [761, 14, 623, 80, "lineNumber"], [761, 24, 623, 80], [762, 14, 623, 80, "columnNumber"], [762, 26, 623, 80], [763, 12, 623, 80], [763, 19, 624, 20], [763, 20, 625, 13], [764, 10, 625, 13], [765, 12, 625, 13, "fileName"], [765, 20, 625, 13], [765, 22, 625, 13, "_jsxFileName"], [765, 34, 625, 13], [766, 12, 625, 13, "lineNumber"], [766, 22, 625, 13], [767, 12, 625, 13, "columnNumber"], [767, 24, 625, 13], [768, 10, 625, 13], [768, 17, 626, 18], [768, 18, 626, 19], [769, 8, 626, 19], [769, 23, 627, 12], [769, 24, 628, 9], [769, 26, 630, 9, "isCameraReady"], [769, 39, 630, 22], [769, 56, 631, 10], [769, 60, 631, 10, "_jsxDevRuntime"], [769, 74, 631, 10], [769, 75, 631, 10, "jsxDEV"], [769, 81, 631, 10], [769, 83, 631, 10, "_jsxDevRuntime"], [769, 97, 631, 10], [769, 98, 631, 10, "Fragment"], [769, 106, 631, 10], [770, 10, 631, 10, "children"], [770, 18, 631, 10], [770, 34, 633, 12], [770, 38, 633, 12, "_jsxDevRuntime"], [770, 52, 633, 12], [770, 53, 633, 12, "jsxDEV"], [770, 59, 633, 12], [770, 61, 633, 13, "_View"], [770, 66, 633, 13], [770, 67, 633, 13, "default"], [770, 74, 633, 17], [771, 12, 633, 18, "style"], [771, 17, 633, 23], [771, 19, 633, 25, "styles"], [771, 25, 633, 31], [771, 26, 633, 32, "headerOverlay"], [771, 39, 633, 46], [772, 12, 633, 46, "children"], [772, 20, 633, 46], [772, 35, 634, 14], [772, 39, 634, 14, "_jsxDevRuntime"], [772, 53, 634, 14], [772, 54, 634, 14, "jsxDEV"], [772, 60, 634, 14], [772, 62, 634, 15, "_View"], [772, 67, 634, 15], [772, 68, 634, 15, "default"], [772, 75, 634, 19], [773, 14, 634, 20, "style"], [773, 19, 634, 25], [773, 21, 634, 27, "styles"], [773, 27, 634, 33], [773, 28, 634, 34, "headerContent"], [773, 41, 634, 48], [774, 14, 634, 48, "children"], [774, 22, 634, 48], [774, 38, 635, 16], [774, 42, 635, 16, "_jsxDevRuntime"], [774, 56, 635, 16], [774, 57, 635, 16, "jsxDEV"], [774, 63, 635, 16], [774, 65, 635, 17, "_View"], [774, 70, 635, 17], [774, 71, 635, 17, "default"], [774, 78, 635, 21], [775, 16, 635, 22, "style"], [775, 21, 635, 27], [775, 23, 635, 29, "styles"], [775, 29, 635, 35], [775, 30, 635, 36, "headerLeft"], [775, 40, 635, 47], [776, 16, 635, 47, "children"], [776, 24, 635, 47], [776, 40, 636, 18], [776, 44, 636, 18, "_jsxDevRuntime"], [776, 58, 636, 18], [776, 59, 636, 18, "jsxDEV"], [776, 65, 636, 18], [776, 67, 636, 19, "_Text"], [776, 72, 636, 19], [776, 73, 636, 19, "default"], [776, 80, 636, 23], [777, 18, 636, 24, "style"], [777, 23, 636, 29], [777, 25, 636, 31, "styles"], [777, 31, 636, 37], [777, 32, 636, 38, "headerTitle"], [777, 43, 636, 50], [778, 18, 636, 50, "children"], [778, 26, 636, 50], [778, 28, 636, 51], [779, 16, 636, 62], [780, 18, 636, 62, "fileName"], [780, 26, 636, 62], [780, 28, 636, 62, "_jsxFileName"], [780, 40, 636, 62], [781, 18, 636, 62, "lineNumber"], [781, 28, 636, 62], [782, 18, 636, 62, "columnNumber"], [782, 30, 636, 62], [783, 16, 636, 62], [783, 23, 636, 68], [783, 24, 636, 69], [783, 39, 637, 18], [783, 43, 637, 18, "_jsxDevRuntime"], [783, 57, 637, 18], [783, 58, 637, 18, "jsxDEV"], [783, 64, 637, 18], [783, 66, 637, 19, "_View"], [783, 71, 637, 19], [783, 72, 637, 19, "default"], [783, 79, 637, 23], [784, 18, 637, 24, "style"], [784, 23, 637, 29], [784, 25, 637, 31, "styles"], [784, 31, 637, 37], [784, 32, 637, 38, "subtitleRow"], [784, 43, 637, 50], [785, 18, 637, 50, "children"], [785, 26, 637, 50], [785, 42, 638, 20], [785, 46, 638, 20, "_jsxDevRuntime"], [785, 60, 638, 20], [785, 61, 638, 20, "jsxDEV"], [785, 67, 638, 20], [785, 69, 638, 21, "_Text"], [785, 74, 638, 21], [785, 75, 638, 21, "default"], [785, 82, 638, 25], [786, 20, 638, 26, "style"], [786, 25, 638, 31], [786, 27, 638, 33, "styles"], [786, 33, 638, 39], [786, 34, 638, 40, "webIcon"], [786, 41, 638, 48], [787, 20, 638, 48, "children"], [787, 28, 638, 48], [787, 30, 638, 49], [788, 18, 638, 51], [789, 20, 638, 51, "fileName"], [789, 28, 638, 51], [789, 30, 638, 51, "_jsxFileName"], [789, 42, 638, 51], [790, 20, 638, 51, "lineNumber"], [790, 30, 638, 51], [791, 20, 638, 51, "columnNumber"], [791, 32, 638, 51], [792, 18, 638, 51], [792, 25, 638, 57], [792, 26, 638, 58], [792, 41, 639, 20], [792, 45, 639, 20, "_jsxDevRuntime"], [792, 59, 639, 20], [792, 60, 639, 20, "jsxDEV"], [792, 66, 639, 20], [792, 68, 639, 21, "_Text"], [792, 73, 639, 21], [792, 74, 639, 21, "default"], [792, 81, 639, 25], [793, 20, 639, 26, "style"], [793, 25, 639, 31], [793, 27, 639, 33, "styles"], [793, 33, 639, 39], [793, 34, 639, 40, "headerSubtitle"], [793, 48, 639, 55], [794, 20, 639, 55, "children"], [794, 28, 639, 55], [794, 30, 639, 56], [795, 18, 639, 71], [796, 20, 639, 71, "fileName"], [796, 28, 639, 71], [796, 30, 639, 71, "_jsxFileName"], [796, 42, 639, 71], [797, 20, 639, 71, "lineNumber"], [797, 30, 639, 71], [798, 20, 639, 71, "columnNumber"], [798, 32, 639, 71], [799, 18, 639, 71], [799, 25, 639, 77], [799, 26, 639, 78], [800, 16, 639, 78], [801, 18, 639, 78, "fileName"], [801, 26, 639, 78], [801, 28, 639, 78, "_jsxFileName"], [801, 40, 639, 78], [802, 18, 639, 78, "lineNumber"], [802, 28, 639, 78], [803, 18, 639, 78, "columnNumber"], [803, 30, 639, 78], [804, 16, 639, 78], [804, 23, 640, 24], [804, 24, 640, 25], [804, 26, 641, 19, "challengeCode"], [804, 39, 641, 32], [804, 56, 642, 20], [804, 60, 642, 20, "_jsxDevRuntime"], [804, 74, 642, 20], [804, 75, 642, 20, "jsxDEV"], [804, 81, 642, 20], [804, 83, 642, 21, "_View"], [804, 88, 642, 21], [804, 89, 642, 21, "default"], [804, 96, 642, 25], [805, 18, 642, 26, "style"], [805, 23, 642, 31], [805, 25, 642, 33, "styles"], [805, 31, 642, 39], [805, 32, 642, 40, "challengeRow"], [805, 44, 642, 53], [806, 18, 642, 53, "children"], [806, 26, 642, 53], [806, 42, 643, 22], [806, 46, 643, 22, "_jsxDevRuntime"], [806, 60, 643, 22], [806, 61, 643, 22, "jsxDEV"], [806, 67, 643, 22], [806, 69, 643, 23, "_lucideReactNative"], [806, 87, 643, 23], [806, 88, 643, 23, "Shield"], [806, 94, 643, 29], [807, 20, 643, 30, "size"], [807, 24, 643, 34], [807, 26, 643, 36], [807, 28, 643, 39], [808, 20, 643, 40, "color"], [808, 25, 643, 45], [808, 27, 643, 46], [809, 18, 643, 52], [810, 20, 643, 52, "fileName"], [810, 28, 643, 52], [810, 30, 643, 52, "_jsxFileName"], [810, 42, 643, 52], [811, 20, 643, 52, "lineNumber"], [811, 30, 643, 52], [812, 20, 643, 52, "columnNumber"], [812, 32, 643, 52], [813, 18, 643, 52], [813, 25, 643, 54], [813, 26, 643, 55], [813, 41, 644, 22], [813, 45, 644, 22, "_jsxDevRuntime"], [813, 59, 644, 22], [813, 60, 644, 22, "jsxDEV"], [813, 66, 644, 22], [813, 68, 644, 23, "_Text"], [813, 73, 644, 23], [813, 74, 644, 23, "default"], [813, 81, 644, 27], [814, 20, 644, 28, "style"], [814, 25, 644, 33], [814, 27, 644, 35, "styles"], [814, 33, 644, 41], [814, 34, 644, 42, "challengeCode"], [814, 47, 644, 56], [815, 20, 644, 56, "children"], [815, 28, 644, 56], [815, 30, 644, 58, "challengeCode"], [816, 18, 644, 71], [817, 20, 644, 71, "fileName"], [817, 28, 644, 71], [817, 30, 644, 71, "_jsxFileName"], [817, 42, 644, 71], [818, 20, 644, 71, "lineNumber"], [818, 30, 644, 71], [819, 20, 644, 71, "columnNumber"], [819, 32, 644, 71], [820, 18, 644, 71], [820, 25, 644, 78], [820, 26, 644, 79], [821, 16, 644, 79], [822, 18, 644, 79, "fileName"], [822, 26, 644, 79], [822, 28, 644, 79, "_jsxFileName"], [822, 40, 644, 79], [823, 18, 644, 79, "lineNumber"], [823, 28, 644, 79], [824, 18, 644, 79, "columnNumber"], [824, 30, 644, 79], [825, 16, 644, 79], [825, 23, 645, 26], [825, 24, 646, 19], [826, 14, 646, 19], [827, 16, 646, 19, "fileName"], [827, 24, 646, 19], [827, 26, 646, 19, "_jsxFileName"], [827, 38, 646, 19], [828, 16, 646, 19, "lineNumber"], [828, 26, 646, 19], [829, 16, 646, 19, "columnNumber"], [829, 28, 646, 19], [830, 14, 646, 19], [830, 21, 647, 22], [830, 22, 647, 23], [830, 37, 648, 16], [830, 41, 648, 16, "_jsxDevRuntime"], [830, 55, 648, 16], [830, 56, 648, 16, "jsxDEV"], [830, 62, 648, 16], [830, 64, 648, 17, "_TouchableOpacity"], [830, 81, 648, 17], [830, 82, 648, 17, "default"], [830, 89, 648, 33], [831, 16, 648, 34, "onPress"], [831, 23, 648, 41], [831, 25, 648, 43, "onCancel"], [831, 33, 648, 52], [832, 16, 648, 53, "style"], [832, 21, 648, 58], [832, 23, 648, 60, "styles"], [832, 29, 648, 66], [832, 30, 648, 67, "closeButton"], [832, 41, 648, 79], [833, 16, 648, 79, "children"], [833, 24, 648, 79], [833, 39, 649, 18], [833, 43, 649, 18, "_jsxDevRuntime"], [833, 57, 649, 18], [833, 58, 649, 18, "jsxDEV"], [833, 64, 649, 18], [833, 66, 649, 19, "_lucideReactNative"], [833, 84, 649, 19], [833, 85, 649, 19, "X"], [833, 86, 649, 20], [834, 18, 649, 21, "size"], [834, 22, 649, 25], [834, 24, 649, 27], [834, 26, 649, 30], [835, 18, 649, 31, "color"], [835, 23, 649, 36], [835, 25, 649, 37], [836, 16, 649, 43], [837, 18, 649, 43, "fileName"], [837, 26, 649, 43], [837, 28, 649, 43, "_jsxFileName"], [837, 40, 649, 43], [838, 18, 649, 43, "lineNumber"], [838, 28, 649, 43], [839, 18, 649, 43, "columnNumber"], [839, 30, 649, 43], [840, 16, 649, 43], [840, 23, 649, 45], [841, 14, 649, 46], [842, 16, 649, 46, "fileName"], [842, 24, 649, 46], [842, 26, 649, 46, "_jsxFileName"], [842, 38, 649, 46], [843, 16, 649, 46, "lineNumber"], [843, 26, 649, 46], [844, 16, 649, 46, "columnNumber"], [844, 28, 649, 46], [845, 14, 649, 46], [845, 21, 650, 34], [845, 22, 650, 35], [846, 12, 650, 35], [847, 14, 650, 35, "fileName"], [847, 22, 650, 35], [847, 24, 650, 35, "_jsxFileName"], [847, 36, 650, 35], [848, 14, 650, 35, "lineNumber"], [848, 24, 650, 35], [849, 14, 650, 35, "columnNumber"], [849, 26, 650, 35], [850, 12, 650, 35], [850, 19, 651, 20], [851, 10, 651, 21], [852, 12, 651, 21, "fileName"], [852, 20, 651, 21], [852, 22, 651, 21, "_jsxFileName"], [852, 34, 651, 21], [853, 12, 651, 21, "lineNumber"], [853, 22, 651, 21], [854, 12, 651, 21, "columnNumber"], [854, 24, 651, 21], [855, 10, 651, 21], [855, 17, 652, 18], [855, 18, 652, 19], [855, 33, 654, 12], [855, 37, 654, 12, "_jsxDevRuntime"], [855, 51, 654, 12], [855, 52, 654, 12, "jsxDEV"], [855, 58, 654, 12], [855, 60, 654, 13, "_View"], [855, 65, 654, 13], [855, 66, 654, 13, "default"], [855, 73, 654, 17], [856, 12, 654, 18, "style"], [856, 17, 654, 23], [856, 19, 654, 25, "styles"], [856, 25, 654, 31], [856, 26, 654, 32, "privacyNotice"], [856, 39, 654, 46], [857, 12, 654, 46, "children"], [857, 20, 654, 46], [857, 36, 655, 14], [857, 40, 655, 14, "_jsxDevRuntime"], [857, 54, 655, 14], [857, 55, 655, 14, "jsxDEV"], [857, 61, 655, 14], [857, 63, 655, 15, "_lucideReactNative"], [857, 81, 655, 15], [857, 82, 655, 15, "Shield"], [857, 88, 655, 21], [858, 14, 655, 22, "size"], [858, 18, 655, 26], [858, 20, 655, 28], [858, 22, 655, 31], [859, 14, 655, 32, "color"], [859, 19, 655, 37], [859, 21, 655, 38], [860, 12, 655, 47], [861, 14, 655, 47, "fileName"], [861, 22, 655, 47], [861, 24, 655, 47, "_jsxFileName"], [861, 36, 655, 47], [862, 14, 655, 47, "lineNumber"], [862, 24, 655, 47], [863, 14, 655, 47, "columnNumber"], [863, 26, 655, 47], [864, 12, 655, 47], [864, 19, 655, 49], [864, 20, 655, 50], [864, 35, 656, 14], [864, 39, 656, 14, "_jsxDevRuntime"], [864, 53, 656, 14], [864, 54, 656, 14, "jsxDEV"], [864, 60, 656, 14], [864, 62, 656, 15, "_Text"], [864, 67, 656, 15], [864, 68, 656, 15, "default"], [864, 75, 656, 19], [865, 14, 656, 20, "style"], [865, 19, 656, 25], [865, 21, 656, 27, "styles"], [865, 27, 656, 33], [865, 28, 656, 34, "privacyText"], [865, 39, 656, 46], [866, 14, 656, 46, "children"], [866, 22, 656, 46], [866, 24, 656, 47], [867, 12, 658, 14], [868, 14, 658, 14, "fileName"], [868, 22, 658, 14], [868, 24, 658, 14, "_jsxFileName"], [868, 36, 658, 14], [869, 14, 658, 14, "lineNumber"], [869, 24, 658, 14], [870, 14, 658, 14, "columnNumber"], [870, 26, 658, 14], [871, 12, 658, 14], [871, 19, 658, 20], [871, 20, 658, 21], [872, 10, 658, 21], [873, 12, 658, 21, "fileName"], [873, 20, 658, 21], [873, 22, 658, 21, "_jsxFileName"], [873, 34, 658, 21], [874, 12, 658, 21, "lineNumber"], [874, 22, 658, 21], [875, 12, 658, 21, "columnNumber"], [875, 24, 658, 21], [876, 10, 658, 21], [876, 17, 659, 18], [876, 18, 659, 19], [876, 33, 661, 12], [876, 37, 661, 12, "_jsxDevRuntime"], [876, 51, 661, 12], [876, 52, 661, 12, "jsxDEV"], [876, 58, 661, 12], [876, 60, 661, 13, "_View"], [876, 65, 661, 13], [876, 66, 661, 13, "default"], [876, 73, 661, 17], [877, 12, 661, 18, "style"], [877, 17, 661, 23], [877, 19, 661, 25, "styles"], [877, 25, 661, 31], [877, 26, 661, 32, "footer<PERSON><PERSON><PERSON>"], [877, 39, 661, 46], [878, 12, 661, 46, "children"], [878, 20, 661, 46], [878, 36, 662, 14], [878, 40, 662, 14, "_jsxDevRuntime"], [878, 54, 662, 14], [878, 55, 662, 14, "jsxDEV"], [878, 61, 662, 14], [878, 63, 662, 15, "_Text"], [878, 68, 662, 15], [878, 69, 662, 15, "default"], [878, 76, 662, 19], [879, 14, 662, 20, "style"], [879, 19, 662, 25], [879, 21, 662, 27, "styles"], [879, 27, 662, 33], [879, 28, 662, 34, "instruction"], [879, 39, 662, 46], [880, 14, 662, 46, "children"], [880, 22, 662, 46], [880, 24, 662, 47], [881, 12, 664, 14], [882, 14, 664, 14, "fileName"], [882, 22, 664, 14], [882, 24, 664, 14, "_jsxFileName"], [882, 36, 664, 14], [883, 14, 664, 14, "lineNumber"], [883, 24, 664, 14], [884, 14, 664, 14, "columnNumber"], [884, 26, 664, 14], [885, 12, 664, 14], [885, 19, 664, 20], [885, 20, 664, 21], [885, 35, 666, 14], [885, 39, 666, 14, "_jsxDevRuntime"], [885, 53, 666, 14], [885, 54, 666, 14, "jsxDEV"], [885, 60, 666, 14], [885, 62, 666, 15, "_TouchableOpacity"], [885, 79, 666, 15], [885, 80, 666, 15, "default"], [885, 87, 666, 31], [886, 14, 667, 16, "onPress"], [886, 21, 667, 23], [886, 23, 667, 25, "capturePhoto"], [886, 35, 667, 38], [887, 14, 668, 16, "disabled"], [887, 22, 668, 24], [887, 24, 668, 26, "processingState"], [887, 39, 668, 41], [887, 44, 668, 46], [887, 50, 668, 52], [887, 54, 668, 56], [887, 55, 668, 57, "isCameraReady"], [887, 68, 668, 71], [888, 14, 669, 16, "style"], [888, 19, 669, 21], [888, 21, 669, 23], [888, 22, 670, 18, "styles"], [888, 28, 670, 24], [888, 29, 670, 25, "shutterButton"], [888, 42, 670, 38], [888, 44, 671, 18, "processingState"], [888, 59, 671, 33], [888, 64, 671, 38], [888, 70, 671, 44], [888, 74, 671, 48, "styles"], [888, 80, 671, 54], [888, 81, 671, 55, "shutterButtonDisabled"], [888, 102, 671, 76], [888, 103, 672, 18], [889, 14, 672, 18, "children"], [889, 22, 672, 18], [889, 24, 674, 17, "processingState"], [889, 39, 674, 32], [889, 44, 674, 37], [889, 50, 674, 43], [889, 66, 675, 18], [889, 70, 675, 18, "_jsxDevRuntime"], [889, 84, 675, 18], [889, 85, 675, 18, "jsxDEV"], [889, 91, 675, 18], [889, 93, 675, 19, "_View"], [889, 98, 675, 19], [889, 99, 675, 19, "default"], [889, 106, 675, 23], [890, 16, 675, 24, "style"], [890, 21, 675, 29], [890, 23, 675, 31, "styles"], [890, 29, 675, 37], [890, 30, 675, 38, "shutterInner"], [891, 14, 675, 51], [892, 16, 675, 51, "fileName"], [892, 24, 675, 51], [892, 26, 675, 51, "_jsxFileName"], [892, 38, 675, 51], [893, 16, 675, 51, "lineNumber"], [893, 26, 675, 51], [894, 16, 675, 51, "columnNumber"], [894, 28, 675, 51], [895, 14, 675, 51], [895, 21, 675, 53], [895, 22, 675, 54], [895, 38, 677, 18], [895, 42, 677, 18, "_jsxDevRuntime"], [895, 56, 677, 18], [895, 57, 677, 18, "jsxDEV"], [895, 63, 677, 18], [895, 65, 677, 19, "_ActivityIndicator"], [895, 83, 677, 19], [895, 84, 677, 19, "default"], [895, 91, 677, 36], [896, 16, 677, 37, "size"], [896, 20, 677, 41], [896, 22, 677, 42], [896, 29, 677, 49], [897, 16, 677, 50, "color"], [897, 21, 677, 55], [897, 23, 677, 56], [898, 14, 677, 65], [899, 16, 677, 65, "fileName"], [899, 24, 677, 65], [899, 26, 677, 65, "_jsxFileName"], [899, 38, 677, 65], [900, 16, 677, 65, "lineNumber"], [900, 26, 677, 65], [901, 16, 677, 65, "columnNumber"], [901, 28, 677, 65], [902, 14, 677, 65], [902, 21, 677, 67], [903, 12, 678, 17], [904, 14, 678, 17, "fileName"], [904, 22, 678, 17], [904, 24, 678, 17, "_jsxFileName"], [904, 36, 678, 17], [905, 14, 678, 17, "lineNumber"], [905, 24, 678, 17], [906, 14, 678, 17, "columnNumber"], [906, 26, 678, 17], [907, 12, 678, 17], [907, 19, 679, 32], [907, 20, 679, 33], [907, 35, 680, 14], [907, 39, 680, 14, "_jsxDevRuntime"], [907, 53, 680, 14], [907, 54, 680, 14, "jsxDEV"], [907, 60, 680, 14], [907, 62, 680, 15, "_Text"], [907, 67, 680, 15], [907, 68, 680, 15, "default"], [907, 75, 680, 19], [908, 14, 680, 20, "style"], [908, 19, 680, 25], [908, 21, 680, 27, "styles"], [908, 27, 680, 33], [908, 28, 680, 34, "privacyNote"], [908, 39, 680, 46], [909, 14, 680, 46, "children"], [909, 22, 680, 46], [909, 24, 680, 47], [910, 12, 682, 14], [911, 14, 682, 14, "fileName"], [911, 22, 682, 14], [911, 24, 682, 14, "_jsxFileName"], [911, 36, 682, 14], [912, 14, 682, 14, "lineNumber"], [912, 24, 682, 14], [913, 14, 682, 14, "columnNumber"], [913, 26, 682, 14], [914, 12, 682, 14], [914, 19, 682, 20], [914, 20, 682, 21], [915, 10, 682, 21], [916, 12, 682, 21, "fileName"], [916, 20, 682, 21], [916, 22, 682, 21, "_jsxFileName"], [916, 34, 682, 21], [917, 12, 682, 21, "lineNumber"], [917, 22, 682, 21], [918, 12, 682, 21, "columnNumber"], [918, 24, 682, 21], [919, 10, 682, 21], [919, 17, 683, 18], [919, 18, 683, 19], [920, 8, 683, 19], [920, 23, 684, 12], [920, 24, 685, 9], [921, 6, 685, 9], [922, 8, 685, 9, "fileName"], [922, 16, 685, 9], [922, 18, 685, 9, "_jsxFileName"], [922, 30, 685, 9], [923, 8, 685, 9, "lineNumber"], [923, 18, 685, 9], [924, 8, 685, 9, "columnNumber"], [924, 20, 685, 9], [925, 6, 685, 9], [925, 13, 686, 12], [925, 14, 686, 13], [925, 29, 688, 6], [925, 33, 688, 6, "_jsxDevRuntime"], [925, 47, 688, 6], [925, 48, 688, 6, "jsxDEV"], [925, 54, 688, 6], [925, 56, 688, 7, "_Modal"], [925, 62, 688, 7], [925, 63, 688, 7, "default"], [925, 70, 688, 12], [926, 8, 689, 8, "visible"], [926, 15, 689, 15], [926, 17, 689, 17, "processingState"], [926, 32, 689, 32], [926, 37, 689, 37], [926, 43, 689, 43], [926, 47, 689, 47, "processingState"], [926, 62, 689, 62], [926, 67, 689, 67], [926, 74, 689, 75], [927, 8, 690, 8, "transparent"], [927, 19, 690, 19], [928, 8, 691, 8, "animationType"], [928, 21, 691, 21], [928, 23, 691, 22], [928, 29, 691, 28], [929, 8, 691, 28, "children"], [929, 16, 691, 28], [929, 31, 693, 8], [929, 35, 693, 8, "_jsxDevRuntime"], [929, 49, 693, 8], [929, 50, 693, 8, "jsxDEV"], [929, 56, 693, 8], [929, 58, 693, 9, "_View"], [929, 63, 693, 9], [929, 64, 693, 9, "default"], [929, 71, 693, 13], [930, 10, 693, 14, "style"], [930, 15, 693, 19], [930, 17, 693, 21, "styles"], [930, 23, 693, 27], [930, 24, 693, 28, "processingModal"], [930, 39, 693, 44], [931, 10, 693, 44, "children"], [931, 18, 693, 44], [931, 33, 694, 10], [931, 37, 694, 10, "_jsxDevRuntime"], [931, 51, 694, 10], [931, 52, 694, 10, "jsxDEV"], [931, 58, 694, 10], [931, 60, 694, 11, "_View"], [931, 65, 694, 11], [931, 66, 694, 11, "default"], [931, 73, 694, 15], [932, 12, 694, 16, "style"], [932, 17, 694, 21], [932, 19, 694, 23, "styles"], [932, 25, 694, 29], [932, 26, 694, 30, "processingContent"], [932, 43, 694, 48], [933, 12, 694, 48, "children"], [933, 20, 694, 48], [933, 36, 695, 12], [933, 40, 695, 12, "_jsxDevRuntime"], [933, 54, 695, 12], [933, 55, 695, 12, "jsxDEV"], [933, 61, 695, 12], [933, 63, 695, 13, "_ActivityIndicator"], [933, 81, 695, 13], [933, 82, 695, 13, "default"], [933, 89, 695, 30], [934, 14, 695, 31, "size"], [934, 18, 695, 35], [934, 20, 695, 36], [934, 27, 695, 43], [935, 14, 695, 44, "color"], [935, 19, 695, 49], [935, 21, 695, 50], [936, 12, 695, 59], [937, 14, 695, 59, "fileName"], [937, 22, 695, 59], [937, 24, 695, 59, "_jsxFileName"], [937, 36, 695, 59], [938, 14, 695, 59, "lineNumber"], [938, 24, 695, 59], [939, 14, 695, 59, "columnNumber"], [939, 26, 695, 59], [940, 12, 695, 59], [940, 19, 695, 61], [940, 20, 695, 62], [940, 35, 697, 12], [940, 39, 697, 12, "_jsxDevRuntime"], [940, 53, 697, 12], [940, 54, 697, 12, "jsxDEV"], [940, 60, 697, 12], [940, 62, 697, 13, "_Text"], [940, 67, 697, 13], [940, 68, 697, 13, "default"], [940, 75, 697, 17], [941, 14, 697, 18, "style"], [941, 19, 697, 23], [941, 21, 697, 25, "styles"], [941, 27, 697, 31], [941, 28, 697, 32, "processingTitle"], [941, 43, 697, 48], [942, 14, 697, 48, "children"], [942, 22, 697, 48], [942, 25, 698, 15, "processingState"], [942, 40, 698, 30], [942, 45, 698, 35], [942, 56, 698, 46], [942, 60, 698, 50], [942, 80, 698, 70], [942, 82, 699, 15, "processingState"], [942, 97, 699, 30], [942, 102, 699, 35], [942, 113, 699, 46], [942, 117, 699, 50], [942, 146, 699, 79], [942, 148, 700, 15, "processingState"], [942, 163, 700, 30], [942, 168, 700, 35], [942, 180, 700, 47], [942, 184, 700, 51], [942, 216, 700, 83], [942, 218, 701, 15, "processingState"], [942, 233, 701, 30], [942, 238, 701, 35], [942, 249, 701, 46], [942, 253, 701, 50], [942, 275, 701, 72], [943, 12, 701, 72], [944, 14, 701, 72, "fileName"], [944, 22, 701, 72], [944, 24, 701, 72, "_jsxFileName"], [944, 36, 701, 72], [945, 14, 701, 72, "lineNumber"], [945, 24, 701, 72], [946, 14, 701, 72, "columnNumber"], [946, 26, 701, 72], [947, 12, 701, 72], [947, 19, 702, 18], [947, 20, 702, 19], [947, 35, 703, 12], [947, 39, 703, 12, "_jsxDevRuntime"], [947, 53, 703, 12], [947, 54, 703, 12, "jsxDEV"], [947, 60, 703, 12], [947, 62, 703, 13, "_View"], [947, 67, 703, 13], [947, 68, 703, 13, "default"], [947, 75, 703, 17], [948, 14, 703, 18, "style"], [948, 19, 703, 23], [948, 21, 703, 25, "styles"], [948, 27, 703, 31], [948, 28, 703, 32, "progressBar"], [948, 39, 703, 44], [949, 14, 703, 44, "children"], [949, 22, 703, 44], [949, 37, 704, 14], [949, 41, 704, 14, "_jsxDevRuntime"], [949, 55, 704, 14], [949, 56, 704, 14, "jsxDEV"], [949, 62, 704, 14], [949, 64, 704, 15, "_View"], [949, 69, 704, 15], [949, 70, 704, 15, "default"], [949, 77, 704, 19], [950, 16, 705, 16, "style"], [950, 21, 705, 21], [950, 23, 705, 23], [950, 24, 706, 18, "styles"], [950, 30, 706, 24], [950, 31, 706, 25, "progressFill"], [950, 43, 706, 37], [950, 45, 707, 18], [951, 18, 707, 20, "width"], [951, 23, 707, 25], [951, 25, 707, 27], [951, 28, 707, 30, "processingProgress"], [951, 46, 707, 48], [952, 16, 707, 52], [952, 17, 707, 53], [953, 14, 708, 18], [954, 16, 708, 18, "fileName"], [954, 24, 708, 18], [954, 26, 708, 18, "_jsxFileName"], [954, 38, 708, 18], [955, 16, 708, 18, "lineNumber"], [955, 26, 708, 18], [956, 16, 708, 18, "columnNumber"], [956, 28, 708, 18], [957, 14, 708, 18], [957, 21, 709, 15], [958, 12, 709, 16], [959, 14, 709, 16, "fileName"], [959, 22, 709, 16], [959, 24, 709, 16, "_jsxFileName"], [959, 36, 709, 16], [960, 14, 709, 16, "lineNumber"], [960, 24, 709, 16], [961, 14, 709, 16, "columnNumber"], [961, 26, 709, 16], [962, 12, 709, 16], [962, 19, 710, 18], [962, 20, 710, 19], [962, 35, 711, 12], [962, 39, 711, 12, "_jsxDevRuntime"], [962, 53, 711, 12], [962, 54, 711, 12, "jsxDEV"], [962, 60, 711, 12], [962, 62, 711, 13, "_Text"], [962, 67, 711, 13], [962, 68, 711, 13, "default"], [962, 75, 711, 17], [963, 14, 711, 18, "style"], [963, 19, 711, 23], [963, 21, 711, 25, "styles"], [963, 27, 711, 31], [963, 28, 711, 32, "processingDescription"], [963, 49, 711, 54], [964, 14, 711, 54, "children"], [964, 22, 711, 54], [964, 25, 712, 15, "processingState"], [964, 40, 712, 30], [964, 45, 712, 35], [964, 56, 712, 46], [964, 60, 712, 50], [964, 89, 712, 79], [964, 91, 713, 15, "processingState"], [964, 106, 713, 30], [964, 111, 713, 35], [964, 122, 713, 46], [964, 126, 713, 50], [964, 164, 713, 88], [964, 166, 714, 15, "processingState"], [964, 181, 714, 30], [964, 186, 714, 35], [964, 198, 714, 47], [964, 202, 714, 51], [964, 247, 714, 96], [964, 249, 715, 15, "processingState"], [964, 264, 715, 30], [964, 269, 715, 35], [964, 280, 715, 46], [964, 284, 715, 50], [964, 325, 715, 91], [965, 12, 715, 91], [966, 14, 715, 91, "fileName"], [966, 22, 715, 91], [966, 24, 715, 91, "_jsxFileName"], [966, 36, 715, 91], [967, 14, 715, 91, "lineNumber"], [967, 24, 715, 91], [968, 14, 715, 91, "columnNumber"], [968, 26, 715, 91], [969, 12, 715, 91], [969, 19, 716, 18], [969, 20, 716, 19], [969, 22, 717, 13, "processingState"], [969, 37, 717, 28], [969, 42, 717, 33], [969, 53, 717, 44], [969, 70, 718, 14], [969, 74, 718, 14, "_jsxDevRuntime"], [969, 88, 718, 14], [969, 89, 718, 14, "jsxDEV"], [969, 95, 718, 14], [969, 97, 718, 15, "_lucideReactNative"], [969, 115, 718, 15], [969, 116, 718, 15, "CheckCircle"], [969, 127, 718, 26], [970, 14, 718, 27, "size"], [970, 18, 718, 31], [970, 20, 718, 33], [970, 22, 718, 36], [971, 14, 718, 37, "color"], [971, 19, 718, 42], [971, 21, 718, 43], [971, 30, 718, 52], [972, 14, 718, 53, "style"], [972, 19, 718, 58], [972, 21, 718, 60, "styles"], [972, 27, 718, 66], [972, 28, 718, 67, "successIcon"], [973, 12, 718, 79], [974, 14, 718, 79, "fileName"], [974, 22, 718, 79], [974, 24, 718, 79, "_jsxFileName"], [974, 36, 718, 79], [975, 14, 718, 79, "lineNumber"], [975, 24, 718, 79], [976, 14, 718, 79, "columnNumber"], [976, 26, 718, 79], [977, 12, 718, 79], [977, 19, 718, 81], [977, 20, 719, 13], [978, 10, 719, 13], [979, 12, 719, 13, "fileName"], [979, 20, 719, 13], [979, 22, 719, 13, "_jsxFileName"], [979, 34, 719, 13], [980, 12, 719, 13, "lineNumber"], [980, 22, 719, 13], [981, 12, 719, 13, "columnNumber"], [981, 24, 719, 13], [982, 10, 719, 13], [982, 17, 720, 16], [983, 8, 720, 17], [984, 10, 720, 17, "fileName"], [984, 18, 720, 17], [984, 20, 720, 17, "_jsxFileName"], [984, 32, 720, 17], [985, 10, 720, 17, "lineNumber"], [985, 20, 720, 17], [986, 10, 720, 17, "columnNumber"], [986, 22, 720, 17], [987, 8, 720, 17], [987, 15, 721, 14], [988, 6, 721, 15], [989, 8, 721, 15, "fileName"], [989, 16, 721, 15], [989, 18, 721, 15, "_jsxFileName"], [989, 30, 721, 15], [990, 8, 721, 15, "lineNumber"], [990, 18, 721, 15], [991, 8, 721, 15, "columnNumber"], [991, 20, 721, 15], [992, 6, 721, 15], [992, 13, 722, 13], [992, 14, 722, 14], [992, 29, 724, 6], [992, 33, 724, 6, "_jsxDevRuntime"], [992, 47, 724, 6], [992, 48, 724, 6, "jsxDEV"], [992, 54, 724, 6], [992, 56, 724, 7, "_Modal"], [992, 62, 724, 7], [992, 63, 724, 7, "default"], [992, 70, 724, 12], [993, 8, 725, 8, "visible"], [993, 15, 725, 15], [993, 17, 725, 17, "processingState"], [993, 32, 725, 32], [993, 37, 725, 37], [993, 44, 725, 45], [994, 8, 726, 8, "transparent"], [994, 19, 726, 19], [995, 8, 727, 8, "animationType"], [995, 21, 727, 21], [995, 23, 727, 22], [995, 29, 727, 28], [996, 8, 727, 28, "children"], [996, 16, 727, 28], [996, 31, 729, 8], [996, 35, 729, 8, "_jsxDevRuntime"], [996, 49, 729, 8], [996, 50, 729, 8, "jsxDEV"], [996, 56, 729, 8], [996, 58, 729, 9, "_View"], [996, 63, 729, 9], [996, 64, 729, 9, "default"], [996, 71, 729, 13], [997, 10, 729, 14, "style"], [997, 15, 729, 19], [997, 17, 729, 21, "styles"], [997, 23, 729, 27], [997, 24, 729, 28, "processingModal"], [997, 39, 729, 44], [998, 10, 729, 44, "children"], [998, 18, 729, 44], [998, 33, 730, 10], [998, 37, 730, 10, "_jsxDevRuntime"], [998, 51, 730, 10], [998, 52, 730, 10, "jsxDEV"], [998, 58, 730, 10], [998, 60, 730, 11, "_View"], [998, 65, 730, 11], [998, 66, 730, 11, "default"], [998, 73, 730, 15], [999, 12, 730, 16, "style"], [999, 17, 730, 21], [999, 19, 730, 23, "styles"], [999, 25, 730, 29], [999, 26, 730, 30, "errorContent"], [999, 38, 730, 43], [1000, 12, 730, 43, "children"], [1000, 20, 730, 43], [1000, 36, 731, 12], [1000, 40, 731, 12, "_jsxDevRuntime"], [1000, 54, 731, 12], [1000, 55, 731, 12, "jsxDEV"], [1000, 61, 731, 12], [1000, 63, 731, 13, "_lucideReactNative"], [1000, 81, 731, 13], [1000, 82, 731, 13, "X"], [1000, 83, 731, 14], [1001, 14, 731, 15, "size"], [1001, 18, 731, 19], [1001, 20, 731, 21], [1001, 22, 731, 24], [1002, 14, 731, 25, "color"], [1002, 19, 731, 30], [1002, 21, 731, 31], [1003, 12, 731, 40], [1004, 14, 731, 40, "fileName"], [1004, 22, 731, 40], [1004, 24, 731, 40, "_jsxFileName"], [1004, 36, 731, 40], [1005, 14, 731, 40, "lineNumber"], [1005, 24, 731, 40], [1006, 14, 731, 40, "columnNumber"], [1006, 26, 731, 40], [1007, 12, 731, 40], [1007, 19, 731, 42], [1007, 20, 731, 43], [1007, 35, 732, 12], [1007, 39, 732, 12, "_jsxDevRuntime"], [1007, 53, 732, 12], [1007, 54, 732, 12, "jsxDEV"], [1007, 60, 732, 12], [1007, 62, 732, 13, "_Text"], [1007, 67, 732, 13], [1007, 68, 732, 13, "default"], [1007, 75, 732, 17], [1008, 14, 732, 18, "style"], [1008, 19, 732, 23], [1008, 21, 732, 25, "styles"], [1008, 27, 732, 31], [1008, 28, 732, 32, "errorTitle"], [1008, 38, 732, 43], [1009, 14, 732, 43, "children"], [1009, 22, 732, 43], [1009, 24, 732, 44], [1010, 12, 732, 61], [1011, 14, 732, 61, "fileName"], [1011, 22, 732, 61], [1011, 24, 732, 61, "_jsxFileName"], [1011, 36, 732, 61], [1012, 14, 732, 61, "lineNumber"], [1012, 24, 732, 61], [1013, 14, 732, 61, "columnNumber"], [1013, 26, 732, 61], [1014, 12, 732, 61], [1014, 19, 732, 67], [1014, 20, 732, 68], [1014, 35, 733, 12], [1014, 39, 733, 12, "_jsxDevRuntime"], [1014, 53, 733, 12], [1014, 54, 733, 12, "jsxDEV"], [1014, 60, 733, 12], [1014, 62, 733, 13, "_Text"], [1014, 67, 733, 13], [1014, 68, 733, 13, "default"], [1014, 75, 733, 17], [1015, 14, 733, 18, "style"], [1015, 19, 733, 23], [1015, 21, 733, 25, "styles"], [1015, 27, 733, 31], [1015, 28, 733, 32, "errorMessage"], [1015, 40, 733, 45], [1016, 14, 733, 45, "children"], [1016, 22, 733, 45], [1016, 24, 733, 47, "errorMessage"], [1017, 12, 733, 59], [1018, 14, 733, 59, "fileName"], [1018, 22, 733, 59], [1018, 24, 733, 59, "_jsxFileName"], [1018, 36, 733, 59], [1019, 14, 733, 59, "lineNumber"], [1019, 24, 733, 59], [1020, 14, 733, 59, "columnNumber"], [1020, 26, 733, 59], [1021, 12, 733, 59], [1021, 19, 733, 66], [1021, 20, 733, 67], [1021, 35, 734, 12], [1021, 39, 734, 12, "_jsxDevRuntime"], [1021, 53, 734, 12], [1021, 54, 734, 12, "jsxDEV"], [1021, 60, 734, 12], [1021, 62, 734, 13, "_TouchableOpacity"], [1021, 79, 734, 13], [1021, 80, 734, 13, "default"], [1021, 87, 734, 29], [1022, 14, 735, 14, "onPress"], [1022, 21, 735, 21], [1022, 23, 735, 23, "retryCapture"], [1022, 35, 735, 36], [1023, 14, 736, 14, "style"], [1023, 19, 736, 19], [1023, 21, 736, 21, "styles"], [1023, 27, 736, 27], [1023, 28, 736, 28, "primaryButton"], [1023, 41, 736, 42], [1024, 14, 736, 42, "children"], [1024, 22, 736, 42], [1024, 37, 738, 14], [1024, 41, 738, 14, "_jsxDevRuntime"], [1024, 55, 738, 14], [1024, 56, 738, 14, "jsxDEV"], [1024, 62, 738, 14], [1024, 64, 738, 15, "_Text"], [1024, 69, 738, 15], [1024, 70, 738, 15, "default"], [1024, 77, 738, 19], [1025, 16, 738, 20, "style"], [1025, 21, 738, 25], [1025, 23, 738, 27, "styles"], [1025, 29, 738, 33], [1025, 30, 738, 34, "primaryButtonText"], [1025, 47, 738, 52], [1026, 16, 738, 52, "children"], [1026, 24, 738, 52], [1026, 26, 738, 53], [1027, 14, 738, 62], [1028, 16, 738, 62, "fileName"], [1028, 24, 738, 62], [1028, 26, 738, 62, "_jsxFileName"], [1028, 38, 738, 62], [1029, 16, 738, 62, "lineNumber"], [1029, 26, 738, 62], [1030, 16, 738, 62, "columnNumber"], [1030, 28, 738, 62], [1031, 14, 738, 62], [1031, 21, 738, 68], [1032, 12, 738, 69], [1033, 14, 738, 69, "fileName"], [1033, 22, 738, 69], [1033, 24, 738, 69, "_jsxFileName"], [1033, 36, 738, 69], [1034, 14, 738, 69, "lineNumber"], [1034, 24, 738, 69], [1035, 14, 738, 69, "columnNumber"], [1035, 26, 738, 69], [1036, 12, 738, 69], [1036, 19, 739, 30], [1036, 20, 739, 31], [1036, 35, 740, 12], [1036, 39, 740, 12, "_jsxDevRuntime"], [1036, 53, 740, 12], [1036, 54, 740, 12, "jsxDEV"], [1036, 60, 740, 12], [1036, 62, 740, 13, "_TouchableOpacity"], [1036, 79, 740, 13], [1036, 80, 740, 13, "default"], [1036, 87, 740, 29], [1037, 14, 741, 14, "onPress"], [1037, 21, 741, 21], [1037, 23, 741, 23, "onCancel"], [1037, 31, 741, 32], [1038, 14, 742, 14, "style"], [1038, 19, 742, 19], [1038, 21, 742, 21, "styles"], [1038, 27, 742, 27], [1038, 28, 742, 28, "secondaryButton"], [1038, 43, 742, 44], [1039, 14, 742, 44, "children"], [1039, 22, 742, 44], [1039, 37, 744, 14], [1039, 41, 744, 14, "_jsxDevRuntime"], [1039, 55, 744, 14], [1039, 56, 744, 14, "jsxDEV"], [1039, 62, 744, 14], [1039, 64, 744, 15, "_Text"], [1039, 69, 744, 15], [1039, 70, 744, 15, "default"], [1039, 77, 744, 19], [1040, 16, 744, 20, "style"], [1040, 21, 744, 25], [1040, 23, 744, 27, "styles"], [1040, 29, 744, 33], [1040, 30, 744, 34, "secondaryButtonText"], [1040, 49, 744, 54], [1041, 16, 744, 54, "children"], [1041, 24, 744, 54], [1041, 26, 744, 55], [1042, 14, 744, 61], [1043, 16, 744, 61, "fileName"], [1043, 24, 744, 61], [1043, 26, 744, 61, "_jsxFileName"], [1043, 38, 744, 61], [1044, 16, 744, 61, "lineNumber"], [1044, 26, 744, 61], [1045, 16, 744, 61, "columnNumber"], [1045, 28, 744, 61], [1046, 14, 744, 61], [1046, 21, 744, 67], [1047, 12, 744, 68], [1048, 14, 744, 68, "fileName"], [1048, 22, 744, 68], [1048, 24, 744, 68, "_jsxFileName"], [1048, 36, 744, 68], [1049, 14, 744, 68, "lineNumber"], [1049, 24, 744, 68], [1050, 14, 744, 68, "columnNumber"], [1050, 26, 744, 68], [1051, 12, 744, 68], [1051, 19, 745, 30], [1051, 20, 745, 31], [1052, 10, 745, 31], [1053, 12, 745, 31, "fileName"], [1053, 20, 745, 31], [1053, 22, 745, 31, "_jsxFileName"], [1053, 34, 745, 31], [1054, 12, 745, 31, "lineNumber"], [1054, 22, 745, 31], [1055, 12, 745, 31, "columnNumber"], [1055, 24, 745, 31], [1056, 10, 745, 31], [1056, 17, 746, 16], [1057, 8, 746, 17], [1058, 10, 746, 17, "fileName"], [1058, 18, 746, 17], [1058, 20, 746, 17, "_jsxFileName"], [1058, 32, 746, 17], [1059, 10, 746, 17, "lineNumber"], [1059, 20, 746, 17], [1060, 10, 746, 17, "columnNumber"], [1060, 22, 746, 17], [1061, 8, 746, 17], [1061, 15, 747, 14], [1062, 6, 747, 15], [1063, 8, 747, 15, "fileName"], [1063, 16, 747, 15], [1063, 18, 747, 15, "_jsxFileName"], [1063, 30, 747, 15], [1064, 8, 747, 15, "lineNumber"], [1064, 18, 747, 15], [1065, 8, 747, 15, "columnNumber"], [1065, 20, 747, 15], [1066, 6, 747, 15], [1066, 13, 748, 13], [1066, 14, 748, 14], [1067, 4, 748, 14], [1068, 6, 748, 14, "fileName"], [1068, 14, 748, 14], [1068, 16, 748, 14, "_jsxFileName"], [1068, 28, 748, 14], [1069, 6, 748, 14, "lineNumber"], [1069, 16, 748, 14], [1070, 6, 748, 14, "columnNumber"], [1070, 18, 748, 14], [1071, 4, 748, 14], [1071, 11, 749, 10], [1071, 12, 749, 11], [1072, 2, 751, 0], [1073, 2, 751, 1, "_s"], [1073, 4, 751, 1], [1073, 5, 51, 24, "EchoCameraWeb"], [1073, 18, 51, 37], [1074, 4, 51, 37], [1074, 12, 58, 42, "useCameraPermissions"], [1074, 44, 58, 62], [1074, 46, 72, 19, "useUpload"], [1074, 64, 72, 28], [1075, 2, 72, 28], [1076, 2, 72, 28, "_c"], [1076, 4, 72, 28], [1076, 7, 51, 24, "EchoCameraWeb"], [1076, 20, 51, 37], [1077, 2, 752, 0], [1077, 8, 752, 6, "styles"], [1077, 14, 752, 12], [1077, 17, 752, 15, "StyleSheet"], [1077, 36, 752, 25], [1077, 37, 752, 26, "create"], [1077, 43, 752, 32], [1077, 44, 752, 33], [1078, 4, 753, 2, "container"], [1078, 13, 753, 11], [1078, 15, 753, 13], [1079, 6, 754, 4, "flex"], [1079, 10, 754, 8], [1079, 12, 754, 10], [1079, 13, 754, 11], [1080, 6, 755, 4, "backgroundColor"], [1080, 21, 755, 19], [1080, 23, 755, 21], [1081, 4, 756, 2], [1081, 5, 756, 3], [1082, 4, 757, 2, "cameraContainer"], [1082, 19, 757, 17], [1082, 21, 757, 19], [1083, 6, 758, 4, "flex"], [1083, 10, 758, 8], [1083, 12, 758, 10], [1083, 13, 758, 11], [1084, 6, 759, 4, "max<PERSON><PERSON><PERSON>"], [1084, 14, 759, 12], [1084, 16, 759, 14], [1084, 19, 759, 17], [1085, 6, 760, 4, "alignSelf"], [1085, 15, 760, 13], [1085, 17, 760, 15], [1085, 25, 760, 23], [1086, 6, 761, 4, "width"], [1086, 11, 761, 9], [1086, 13, 761, 11], [1087, 4, 762, 2], [1087, 5, 762, 3], [1088, 4, 763, 2, "camera"], [1088, 10, 763, 8], [1088, 12, 763, 10], [1089, 6, 764, 4, "flex"], [1089, 10, 764, 8], [1089, 12, 764, 10], [1090, 4, 765, 2], [1090, 5, 765, 3], [1091, 4, 766, 2, "headerOverlay"], [1091, 17, 766, 15], [1091, 19, 766, 17], [1092, 6, 767, 4, "position"], [1092, 14, 767, 12], [1092, 16, 767, 14], [1092, 26, 767, 24], [1093, 6, 768, 4, "top"], [1093, 9, 768, 7], [1093, 11, 768, 9], [1093, 12, 768, 10], [1094, 6, 769, 4, "left"], [1094, 10, 769, 8], [1094, 12, 769, 10], [1094, 13, 769, 11], [1095, 6, 770, 4, "right"], [1095, 11, 770, 9], [1095, 13, 770, 11], [1095, 14, 770, 12], [1096, 6, 771, 4, "backgroundColor"], [1096, 21, 771, 19], [1096, 23, 771, 21], [1096, 36, 771, 34], [1097, 6, 772, 4, "paddingTop"], [1097, 16, 772, 14], [1097, 18, 772, 16], [1097, 20, 772, 18], [1098, 6, 773, 4, "paddingHorizontal"], [1098, 23, 773, 21], [1098, 25, 773, 23], [1098, 27, 773, 25], [1099, 6, 774, 4, "paddingBottom"], [1099, 19, 774, 17], [1099, 21, 774, 19], [1100, 4, 775, 2], [1100, 5, 775, 3], [1101, 4, 776, 2, "headerContent"], [1101, 17, 776, 15], [1101, 19, 776, 17], [1102, 6, 777, 4, "flexDirection"], [1102, 19, 777, 17], [1102, 21, 777, 19], [1102, 26, 777, 24], [1103, 6, 778, 4, "justifyContent"], [1103, 20, 778, 18], [1103, 22, 778, 20], [1103, 37, 778, 35], [1104, 6, 779, 4, "alignItems"], [1104, 16, 779, 14], [1104, 18, 779, 16], [1105, 4, 780, 2], [1105, 5, 780, 3], [1106, 4, 781, 2, "headerLeft"], [1106, 14, 781, 12], [1106, 16, 781, 14], [1107, 6, 782, 4, "flex"], [1107, 10, 782, 8], [1107, 12, 782, 10], [1108, 4, 783, 2], [1108, 5, 783, 3], [1109, 4, 784, 2, "headerTitle"], [1109, 15, 784, 13], [1109, 17, 784, 15], [1110, 6, 785, 4, "fontSize"], [1110, 14, 785, 12], [1110, 16, 785, 14], [1110, 18, 785, 16], [1111, 6, 786, 4, "fontWeight"], [1111, 16, 786, 14], [1111, 18, 786, 16], [1111, 23, 786, 21], [1112, 6, 787, 4, "color"], [1112, 11, 787, 9], [1112, 13, 787, 11], [1112, 19, 787, 17], [1113, 6, 788, 4, "marginBottom"], [1113, 18, 788, 16], [1113, 20, 788, 18], [1114, 4, 789, 2], [1114, 5, 789, 3], [1115, 4, 790, 2, "subtitleRow"], [1115, 15, 790, 13], [1115, 17, 790, 15], [1116, 6, 791, 4, "flexDirection"], [1116, 19, 791, 17], [1116, 21, 791, 19], [1116, 26, 791, 24], [1117, 6, 792, 4, "alignItems"], [1117, 16, 792, 14], [1117, 18, 792, 16], [1117, 26, 792, 24], [1118, 6, 793, 4, "marginBottom"], [1118, 18, 793, 16], [1118, 20, 793, 18], [1119, 4, 794, 2], [1119, 5, 794, 3], [1120, 4, 795, 2, "webIcon"], [1120, 11, 795, 9], [1120, 13, 795, 11], [1121, 6, 796, 4, "fontSize"], [1121, 14, 796, 12], [1121, 16, 796, 14], [1121, 18, 796, 16], [1122, 6, 797, 4, "marginRight"], [1122, 17, 797, 15], [1122, 19, 797, 17], [1123, 4, 798, 2], [1123, 5, 798, 3], [1124, 4, 799, 2, "headerSubtitle"], [1124, 18, 799, 16], [1124, 20, 799, 18], [1125, 6, 800, 4, "fontSize"], [1125, 14, 800, 12], [1125, 16, 800, 14], [1125, 18, 800, 16], [1126, 6, 801, 4, "color"], [1126, 11, 801, 9], [1126, 13, 801, 11], [1126, 19, 801, 17], [1127, 6, 802, 4, "opacity"], [1127, 13, 802, 11], [1127, 15, 802, 13], [1128, 4, 803, 2], [1128, 5, 803, 3], [1129, 4, 804, 2, "challengeRow"], [1129, 16, 804, 14], [1129, 18, 804, 16], [1130, 6, 805, 4, "flexDirection"], [1130, 19, 805, 17], [1130, 21, 805, 19], [1130, 26, 805, 24], [1131, 6, 806, 4, "alignItems"], [1131, 16, 806, 14], [1131, 18, 806, 16], [1132, 4, 807, 2], [1132, 5, 807, 3], [1133, 4, 808, 2, "challengeCode"], [1133, 17, 808, 15], [1133, 19, 808, 17], [1134, 6, 809, 4, "fontSize"], [1134, 14, 809, 12], [1134, 16, 809, 14], [1134, 18, 809, 16], [1135, 6, 810, 4, "color"], [1135, 11, 810, 9], [1135, 13, 810, 11], [1135, 19, 810, 17], [1136, 6, 811, 4, "marginLeft"], [1136, 16, 811, 14], [1136, 18, 811, 16], [1136, 19, 811, 17], [1137, 6, 812, 4, "fontFamily"], [1137, 16, 812, 14], [1137, 18, 812, 16], [1138, 4, 813, 2], [1138, 5, 813, 3], [1139, 4, 814, 2, "closeButton"], [1139, 15, 814, 13], [1139, 17, 814, 15], [1140, 6, 815, 4, "padding"], [1140, 13, 815, 11], [1140, 15, 815, 13], [1141, 4, 816, 2], [1141, 5, 816, 3], [1142, 4, 817, 2, "privacyNotice"], [1142, 17, 817, 15], [1142, 19, 817, 17], [1143, 6, 818, 4, "position"], [1143, 14, 818, 12], [1143, 16, 818, 14], [1143, 26, 818, 24], [1144, 6, 819, 4, "top"], [1144, 9, 819, 7], [1144, 11, 819, 9], [1144, 14, 819, 12], [1145, 6, 820, 4, "left"], [1145, 10, 820, 8], [1145, 12, 820, 10], [1145, 14, 820, 12], [1146, 6, 821, 4, "right"], [1146, 11, 821, 9], [1146, 13, 821, 11], [1146, 15, 821, 13], [1147, 6, 822, 4, "backgroundColor"], [1147, 21, 822, 19], [1147, 23, 822, 21], [1147, 48, 822, 46], [1148, 6, 823, 4, "borderRadius"], [1148, 18, 823, 16], [1148, 20, 823, 18], [1148, 21, 823, 19], [1149, 6, 824, 4, "padding"], [1149, 13, 824, 11], [1149, 15, 824, 13], [1149, 17, 824, 15], [1150, 6, 825, 4, "flexDirection"], [1150, 19, 825, 17], [1150, 21, 825, 19], [1150, 26, 825, 24], [1151, 6, 826, 4, "alignItems"], [1151, 16, 826, 14], [1151, 18, 826, 16], [1152, 4, 827, 2], [1152, 5, 827, 3], [1153, 4, 828, 2, "privacyText"], [1153, 15, 828, 13], [1153, 17, 828, 15], [1154, 6, 829, 4, "color"], [1154, 11, 829, 9], [1154, 13, 829, 11], [1154, 19, 829, 17], [1155, 6, 830, 4, "fontSize"], [1155, 14, 830, 12], [1155, 16, 830, 14], [1155, 18, 830, 16], [1156, 6, 831, 4, "marginLeft"], [1156, 16, 831, 14], [1156, 18, 831, 16], [1156, 19, 831, 17], [1157, 6, 832, 4, "flex"], [1157, 10, 832, 8], [1157, 12, 832, 10], [1158, 4, 833, 2], [1158, 5, 833, 3], [1159, 4, 834, 2, "footer<PERSON><PERSON><PERSON>"], [1159, 17, 834, 15], [1159, 19, 834, 17], [1160, 6, 835, 4, "position"], [1160, 14, 835, 12], [1160, 16, 835, 14], [1160, 26, 835, 24], [1161, 6, 836, 4, "bottom"], [1161, 12, 836, 10], [1161, 14, 836, 12], [1161, 15, 836, 13], [1162, 6, 837, 4, "left"], [1162, 10, 837, 8], [1162, 12, 837, 10], [1162, 13, 837, 11], [1163, 6, 838, 4, "right"], [1163, 11, 838, 9], [1163, 13, 838, 11], [1163, 14, 838, 12], [1164, 6, 839, 4, "backgroundColor"], [1164, 21, 839, 19], [1164, 23, 839, 21], [1164, 36, 839, 34], [1165, 6, 840, 4, "paddingBottom"], [1165, 19, 840, 17], [1165, 21, 840, 19], [1165, 23, 840, 21], [1166, 6, 841, 4, "paddingTop"], [1166, 16, 841, 14], [1166, 18, 841, 16], [1166, 20, 841, 18], [1167, 6, 842, 4, "alignItems"], [1167, 16, 842, 14], [1167, 18, 842, 16], [1168, 4, 843, 2], [1168, 5, 843, 3], [1169, 4, 844, 2, "instruction"], [1169, 15, 844, 13], [1169, 17, 844, 15], [1170, 6, 845, 4, "fontSize"], [1170, 14, 845, 12], [1170, 16, 845, 14], [1170, 18, 845, 16], [1171, 6, 846, 4, "color"], [1171, 11, 846, 9], [1171, 13, 846, 11], [1171, 19, 846, 17], [1172, 6, 847, 4, "marginBottom"], [1172, 18, 847, 16], [1172, 20, 847, 18], [1173, 4, 848, 2], [1173, 5, 848, 3], [1174, 4, 849, 2, "shutterButton"], [1174, 17, 849, 15], [1174, 19, 849, 17], [1175, 6, 850, 4, "width"], [1175, 11, 850, 9], [1175, 13, 850, 11], [1175, 15, 850, 13], [1176, 6, 851, 4, "height"], [1176, 12, 851, 10], [1176, 14, 851, 12], [1176, 16, 851, 14], [1177, 6, 852, 4, "borderRadius"], [1177, 18, 852, 16], [1177, 20, 852, 18], [1177, 22, 852, 20], [1178, 6, 853, 4, "backgroundColor"], [1178, 21, 853, 19], [1178, 23, 853, 21], [1178, 29, 853, 27], [1179, 6, 854, 4, "justifyContent"], [1179, 20, 854, 18], [1179, 22, 854, 20], [1179, 30, 854, 28], [1180, 6, 855, 4, "alignItems"], [1180, 16, 855, 14], [1180, 18, 855, 16], [1180, 26, 855, 24], [1181, 6, 856, 4, "marginBottom"], [1181, 18, 856, 16], [1181, 20, 856, 18], [1181, 22, 856, 20], [1182, 6, 857, 4], [1182, 9, 857, 7, "Platform"], [1182, 26, 857, 15], [1182, 27, 857, 16, "select"], [1182, 33, 857, 22], [1182, 34, 857, 23], [1183, 8, 858, 6, "ios"], [1183, 11, 858, 9], [1183, 13, 858, 11], [1184, 10, 859, 8, "shadowColor"], [1184, 21, 859, 19], [1184, 23, 859, 21], [1184, 32, 859, 30], [1185, 10, 860, 8, "shadowOffset"], [1185, 22, 860, 20], [1185, 24, 860, 22], [1186, 12, 860, 24, "width"], [1186, 17, 860, 29], [1186, 19, 860, 31], [1186, 20, 860, 32], [1187, 12, 860, 34, "height"], [1187, 18, 860, 40], [1187, 20, 860, 42], [1188, 10, 860, 44], [1188, 11, 860, 45], [1189, 10, 861, 8, "shadowOpacity"], [1189, 23, 861, 21], [1189, 25, 861, 23], [1189, 28, 861, 26], [1190, 10, 862, 8, "shadowRadius"], [1190, 22, 862, 20], [1190, 24, 862, 22], [1191, 8, 863, 6], [1191, 9, 863, 7], [1192, 8, 864, 6, "android"], [1192, 15, 864, 13], [1192, 17, 864, 15], [1193, 10, 865, 8, "elevation"], [1193, 19, 865, 17], [1193, 21, 865, 19], [1194, 8, 866, 6], [1194, 9, 866, 7], [1195, 8, 867, 6, "web"], [1195, 11, 867, 9], [1195, 13, 867, 11], [1196, 10, 868, 8, "boxShadow"], [1196, 19, 868, 17], [1196, 21, 868, 19], [1197, 8, 869, 6], [1198, 6, 870, 4], [1198, 7, 870, 5], [1199, 4, 871, 2], [1199, 5, 871, 3], [1200, 4, 872, 2, "shutterButtonDisabled"], [1200, 25, 872, 23], [1200, 27, 872, 25], [1201, 6, 873, 4, "opacity"], [1201, 13, 873, 11], [1201, 15, 873, 13], [1202, 4, 874, 2], [1202, 5, 874, 3], [1203, 4, 875, 2, "shutterInner"], [1203, 16, 875, 14], [1203, 18, 875, 16], [1204, 6, 876, 4, "width"], [1204, 11, 876, 9], [1204, 13, 876, 11], [1204, 15, 876, 13], [1205, 6, 877, 4, "height"], [1205, 12, 877, 10], [1205, 14, 877, 12], [1205, 16, 877, 14], [1206, 6, 878, 4, "borderRadius"], [1206, 18, 878, 16], [1206, 20, 878, 18], [1206, 22, 878, 20], [1207, 6, 879, 4, "backgroundColor"], [1207, 21, 879, 19], [1207, 23, 879, 21], [1207, 29, 879, 27], [1208, 6, 880, 4, "borderWidth"], [1208, 17, 880, 15], [1208, 19, 880, 17], [1208, 20, 880, 18], [1209, 6, 881, 4, "borderColor"], [1209, 17, 881, 15], [1209, 19, 881, 17], [1210, 4, 882, 2], [1210, 5, 882, 3], [1211, 4, 883, 2, "privacyNote"], [1211, 15, 883, 13], [1211, 17, 883, 15], [1212, 6, 884, 4, "fontSize"], [1212, 14, 884, 12], [1212, 16, 884, 14], [1212, 18, 884, 16], [1213, 6, 885, 4, "color"], [1213, 11, 885, 9], [1213, 13, 885, 11], [1214, 4, 886, 2], [1214, 5, 886, 3], [1215, 4, 887, 2, "processingModal"], [1215, 19, 887, 17], [1215, 21, 887, 19], [1216, 6, 888, 4, "flex"], [1216, 10, 888, 8], [1216, 12, 888, 10], [1216, 13, 888, 11], [1217, 6, 889, 4, "backgroundColor"], [1217, 21, 889, 19], [1217, 23, 889, 21], [1217, 43, 889, 41], [1218, 6, 890, 4, "justifyContent"], [1218, 20, 890, 18], [1218, 22, 890, 20], [1218, 30, 890, 28], [1219, 6, 891, 4, "alignItems"], [1219, 16, 891, 14], [1219, 18, 891, 16], [1220, 4, 892, 2], [1220, 5, 892, 3], [1221, 4, 893, 2, "processingContent"], [1221, 21, 893, 19], [1221, 23, 893, 21], [1222, 6, 894, 4, "backgroundColor"], [1222, 21, 894, 19], [1222, 23, 894, 21], [1222, 29, 894, 27], [1223, 6, 895, 4, "borderRadius"], [1223, 18, 895, 16], [1223, 20, 895, 18], [1223, 22, 895, 20], [1224, 6, 896, 4, "padding"], [1224, 13, 896, 11], [1224, 15, 896, 13], [1224, 17, 896, 15], [1225, 6, 897, 4, "width"], [1225, 11, 897, 9], [1225, 13, 897, 11], [1225, 18, 897, 16], [1226, 6, 898, 4, "max<PERSON><PERSON><PERSON>"], [1226, 14, 898, 12], [1226, 16, 898, 14], [1226, 19, 898, 17], [1227, 6, 899, 4, "alignItems"], [1227, 16, 899, 14], [1227, 18, 899, 16], [1228, 4, 900, 2], [1228, 5, 900, 3], [1229, 4, 901, 2, "processingTitle"], [1229, 19, 901, 17], [1229, 21, 901, 19], [1230, 6, 902, 4, "fontSize"], [1230, 14, 902, 12], [1230, 16, 902, 14], [1230, 18, 902, 16], [1231, 6, 903, 4, "fontWeight"], [1231, 16, 903, 14], [1231, 18, 903, 16], [1231, 23, 903, 21], [1232, 6, 904, 4, "color"], [1232, 11, 904, 9], [1232, 13, 904, 11], [1232, 22, 904, 20], [1233, 6, 905, 4, "marginTop"], [1233, 15, 905, 13], [1233, 17, 905, 15], [1233, 19, 905, 17], [1234, 6, 906, 4, "marginBottom"], [1234, 18, 906, 16], [1234, 20, 906, 18], [1235, 4, 907, 2], [1235, 5, 907, 3], [1236, 4, 908, 2, "progressBar"], [1236, 15, 908, 13], [1236, 17, 908, 15], [1237, 6, 909, 4, "width"], [1237, 11, 909, 9], [1237, 13, 909, 11], [1237, 19, 909, 17], [1238, 6, 910, 4, "height"], [1238, 12, 910, 10], [1238, 14, 910, 12], [1238, 15, 910, 13], [1239, 6, 911, 4, "backgroundColor"], [1239, 21, 911, 19], [1239, 23, 911, 21], [1239, 32, 911, 30], [1240, 6, 912, 4, "borderRadius"], [1240, 18, 912, 16], [1240, 20, 912, 18], [1240, 21, 912, 19], [1241, 6, 913, 4, "overflow"], [1241, 14, 913, 12], [1241, 16, 913, 14], [1241, 24, 913, 22], [1242, 6, 914, 4, "marginBottom"], [1242, 18, 914, 16], [1242, 20, 914, 18], [1243, 4, 915, 2], [1243, 5, 915, 3], [1244, 4, 916, 2, "progressFill"], [1244, 16, 916, 14], [1244, 18, 916, 16], [1245, 6, 917, 4, "height"], [1245, 12, 917, 10], [1245, 14, 917, 12], [1245, 20, 917, 18], [1246, 6, 918, 4, "backgroundColor"], [1246, 21, 918, 19], [1246, 23, 918, 21], [1246, 32, 918, 30], [1247, 6, 919, 4, "borderRadius"], [1247, 18, 919, 16], [1247, 20, 919, 18], [1248, 4, 920, 2], [1248, 5, 920, 3], [1249, 4, 921, 2, "processingDescription"], [1249, 25, 921, 23], [1249, 27, 921, 25], [1250, 6, 922, 4, "fontSize"], [1250, 14, 922, 12], [1250, 16, 922, 14], [1250, 18, 922, 16], [1251, 6, 923, 4, "color"], [1251, 11, 923, 9], [1251, 13, 923, 11], [1251, 22, 923, 20], [1252, 6, 924, 4, "textAlign"], [1252, 15, 924, 13], [1252, 17, 924, 15], [1253, 4, 925, 2], [1253, 5, 925, 3], [1254, 4, 926, 2, "successIcon"], [1254, 15, 926, 13], [1254, 17, 926, 15], [1255, 6, 927, 4, "marginTop"], [1255, 15, 927, 13], [1255, 17, 927, 15], [1256, 4, 928, 2], [1256, 5, 928, 3], [1257, 4, 929, 2, "errorContent"], [1257, 16, 929, 14], [1257, 18, 929, 16], [1258, 6, 930, 4, "backgroundColor"], [1258, 21, 930, 19], [1258, 23, 930, 21], [1258, 29, 930, 27], [1259, 6, 931, 4, "borderRadius"], [1259, 18, 931, 16], [1259, 20, 931, 18], [1259, 22, 931, 20], [1260, 6, 932, 4, "padding"], [1260, 13, 932, 11], [1260, 15, 932, 13], [1260, 17, 932, 15], [1261, 6, 933, 4, "width"], [1261, 11, 933, 9], [1261, 13, 933, 11], [1261, 18, 933, 16], [1262, 6, 934, 4, "max<PERSON><PERSON><PERSON>"], [1262, 14, 934, 12], [1262, 16, 934, 14], [1262, 19, 934, 17], [1263, 6, 935, 4, "alignItems"], [1263, 16, 935, 14], [1263, 18, 935, 16], [1264, 4, 936, 2], [1264, 5, 936, 3], [1265, 4, 937, 2, "errorTitle"], [1265, 14, 937, 12], [1265, 16, 937, 14], [1266, 6, 938, 4, "fontSize"], [1266, 14, 938, 12], [1266, 16, 938, 14], [1266, 18, 938, 16], [1267, 6, 939, 4, "fontWeight"], [1267, 16, 939, 14], [1267, 18, 939, 16], [1267, 23, 939, 21], [1268, 6, 940, 4, "color"], [1268, 11, 940, 9], [1268, 13, 940, 11], [1268, 22, 940, 20], [1269, 6, 941, 4, "marginTop"], [1269, 15, 941, 13], [1269, 17, 941, 15], [1269, 19, 941, 17], [1270, 6, 942, 4, "marginBottom"], [1270, 18, 942, 16], [1270, 20, 942, 18], [1271, 4, 943, 2], [1271, 5, 943, 3], [1272, 4, 944, 2, "errorMessage"], [1272, 16, 944, 14], [1272, 18, 944, 16], [1273, 6, 945, 4, "fontSize"], [1273, 14, 945, 12], [1273, 16, 945, 14], [1273, 18, 945, 16], [1274, 6, 946, 4, "color"], [1274, 11, 946, 9], [1274, 13, 946, 11], [1274, 22, 946, 20], [1275, 6, 947, 4, "textAlign"], [1275, 15, 947, 13], [1275, 17, 947, 15], [1275, 25, 947, 23], [1276, 6, 948, 4, "marginBottom"], [1276, 18, 948, 16], [1276, 20, 948, 18], [1277, 4, 949, 2], [1277, 5, 949, 3], [1278, 4, 950, 2, "primaryButton"], [1278, 17, 950, 15], [1278, 19, 950, 17], [1279, 6, 951, 4, "backgroundColor"], [1279, 21, 951, 19], [1279, 23, 951, 21], [1279, 32, 951, 30], [1280, 6, 952, 4, "paddingHorizontal"], [1280, 23, 952, 21], [1280, 25, 952, 23], [1280, 27, 952, 25], [1281, 6, 953, 4, "paddingVertical"], [1281, 21, 953, 19], [1281, 23, 953, 21], [1281, 25, 953, 23], [1282, 6, 954, 4, "borderRadius"], [1282, 18, 954, 16], [1282, 20, 954, 18], [1282, 21, 954, 19], [1283, 6, 955, 4, "marginTop"], [1283, 15, 955, 13], [1283, 17, 955, 15], [1284, 4, 956, 2], [1284, 5, 956, 3], [1285, 4, 957, 2, "primaryButtonText"], [1285, 21, 957, 19], [1285, 23, 957, 21], [1286, 6, 958, 4, "color"], [1286, 11, 958, 9], [1286, 13, 958, 11], [1286, 19, 958, 17], [1287, 6, 959, 4, "fontSize"], [1287, 14, 959, 12], [1287, 16, 959, 14], [1287, 18, 959, 16], [1288, 6, 960, 4, "fontWeight"], [1288, 16, 960, 14], [1288, 18, 960, 16], [1289, 4, 961, 2], [1289, 5, 961, 3], [1290, 4, 962, 2, "secondaryButton"], [1290, 19, 962, 17], [1290, 21, 962, 19], [1291, 6, 963, 4, "paddingHorizontal"], [1291, 23, 963, 21], [1291, 25, 963, 23], [1291, 27, 963, 25], [1292, 6, 964, 4, "paddingVertical"], [1292, 21, 964, 19], [1292, 23, 964, 21], [1292, 25, 964, 23], [1293, 6, 965, 4, "marginTop"], [1293, 15, 965, 13], [1293, 17, 965, 15], [1294, 4, 966, 2], [1294, 5, 966, 3], [1295, 4, 967, 2, "secondaryButtonText"], [1295, 23, 967, 21], [1295, 25, 967, 23], [1296, 6, 968, 4, "color"], [1296, 11, 968, 9], [1296, 13, 968, 11], [1296, 22, 968, 20], [1297, 6, 969, 4, "fontSize"], [1297, 14, 969, 12], [1297, 16, 969, 14], [1298, 4, 970, 2], [1298, 5, 970, 3], [1299, 4, 971, 2, "permissionContent"], [1299, 21, 971, 19], [1299, 23, 971, 21], [1300, 6, 972, 4, "flex"], [1300, 10, 972, 8], [1300, 12, 972, 10], [1300, 13, 972, 11], [1301, 6, 973, 4, "justifyContent"], [1301, 20, 973, 18], [1301, 22, 973, 20], [1301, 30, 973, 28], [1302, 6, 974, 4, "alignItems"], [1302, 16, 974, 14], [1302, 18, 974, 16], [1302, 26, 974, 24], [1303, 6, 975, 4, "padding"], [1303, 13, 975, 11], [1303, 15, 975, 13], [1304, 4, 976, 2], [1304, 5, 976, 3], [1305, 4, 977, 2, "permissionTitle"], [1305, 19, 977, 17], [1305, 21, 977, 19], [1306, 6, 978, 4, "fontSize"], [1306, 14, 978, 12], [1306, 16, 978, 14], [1306, 18, 978, 16], [1307, 6, 979, 4, "fontWeight"], [1307, 16, 979, 14], [1307, 18, 979, 16], [1307, 23, 979, 21], [1308, 6, 980, 4, "color"], [1308, 11, 980, 9], [1308, 13, 980, 11], [1308, 22, 980, 20], [1309, 6, 981, 4, "marginTop"], [1309, 15, 981, 13], [1309, 17, 981, 15], [1309, 19, 981, 17], [1310, 6, 982, 4, "marginBottom"], [1310, 18, 982, 16], [1310, 20, 982, 18], [1311, 4, 983, 2], [1311, 5, 983, 3], [1312, 4, 984, 2, "permissionDescription"], [1312, 25, 984, 23], [1312, 27, 984, 25], [1313, 6, 985, 4, "fontSize"], [1313, 14, 985, 12], [1313, 16, 985, 14], [1313, 18, 985, 16], [1314, 6, 986, 4, "color"], [1314, 11, 986, 9], [1314, 13, 986, 11], [1314, 22, 986, 20], [1315, 6, 987, 4, "textAlign"], [1315, 15, 987, 13], [1315, 17, 987, 15], [1315, 25, 987, 23], [1316, 6, 988, 4, "marginBottom"], [1316, 18, 988, 16], [1316, 20, 988, 18], [1317, 4, 989, 2], [1317, 5, 989, 3], [1318, 4, 990, 2, "loadingText"], [1318, 15, 990, 13], [1318, 17, 990, 15], [1319, 6, 991, 4, "color"], [1319, 11, 991, 9], [1319, 13, 991, 11], [1319, 22, 991, 20], [1320, 6, 992, 4, "marginTop"], [1320, 15, 992, 13], [1320, 17, 992, 15], [1321, 4, 993, 2], [1321, 5, 993, 3], [1322, 4, 994, 2], [1323, 4, 995, 2, "blurZone"], [1323, 12, 995, 10], [1323, 14, 995, 12], [1324, 6, 996, 4, "position"], [1324, 14, 996, 12], [1324, 16, 996, 14], [1324, 26, 996, 24], [1325, 6, 997, 4, "overflow"], [1325, 14, 997, 12], [1325, 16, 997, 14], [1326, 4, 998, 2], [1326, 5, 998, 3], [1327, 4, 999, 2, "previewChip"], [1327, 15, 999, 13], [1327, 17, 999, 15], [1328, 6, 1000, 4, "position"], [1328, 14, 1000, 12], [1328, 16, 1000, 14], [1328, 26, 1000, 24], [1329, 6, 1001, 4, "top"], [1329, 9, 1001, 7], [1329, 11, 1001, 9], [1329, 12, 1001, 10], [1330, 6, 1002, 4, "right"], [1330, 11, 1002, 9], [1330, 13, 1002, 11], [1330, 14, 1002, 12], [1331, 6, 1003, 4, "backgroundColor"], [1331, 21, 1003, 19], [1331, 23, 1003, 21], [1331, 40, 1003, 38], [1332, 6, 1004, 4, "paddingHorizontal"], [1332, 23, 1004, 21], [1332, 25, 1004, 23], [1332, 27, 1004, 25], [1333, 6, 1005, 4, "paddingVertical"], [1333, 21, 1005, 19], [1333, 23, 1005, 21], [1333, 24, 1005, 22], [1334, 6, 1006, 4, "borderRadius"], [1334, 18, 1006, 16], [1334, 20, 1006, 18], [1335, 4, 1007, 2], [1335, 5, 1007, 3], [1336, 4, 1008, 2, "previewChipText"], [1336, 19, 1008, 17], [1336, 21, 1008, 19], [1337, 6, 1009, 4, "color"], [1337, 11, 1009, 9], [1337, 13, 1009, 11], [1337, 19, 1009, 17], [1338, 6, 1010, 4, "fontSize"], [1338, 14, 1010, 12], [1338, 16, 1010, 14], [1338, 18, 1010, 16], [1339, 6, 1011, 4, "fontWeight"], [1339, 16, 1011, 14], [1339, 18, 1011, 16], [1340, 4, 1012, 2], [1341, 2, 1013, 0], [1341, 3, 1013, 1], [1341, 4, 1013, 2], [1342, 2, 1013, 3], [1342, 6, 1013, 3, "_c"], [1342, 8, 1013, 3], [1343, 2, 1013, 3, "$RefreshReg$"], [1343, 14, 1013, 3], [1343, 15, 1013, 3, "_c"], [1343, 17, 1013, 3], [1344, 0, 1013, 3], [1344, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHkC;mCKE;wBDY;OCI;gDC8B;YDO;8BDa;aCM;6CEc;YFO;oFGsB;UHM;8BIS;SJoD;uDDS;sBMC,wBN;OCC;GLc;6BWG;GXyB;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}