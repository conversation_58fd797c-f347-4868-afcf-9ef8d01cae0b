{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 19}, "end": {"line": 2, "column": 53, "index": 72}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 73}, "end": {"line": 3, "column": 31, "index": 104}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}, {"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 105}, "end": {"line": 4, "column": 37, "index": 142}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}, {"name": "../../Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 143}, "end": {"line": 5, "column": 42, "index": 185}}], "key": "AXHAxFjlDdeq1JxYZnWn+aHYhYU=", "exportNames": ["*"]}}, {"name": "./Typeface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 186}, "end": {"line": 6, "column": 41, "index": 227}}], "key": "T7PcoThZRvZLszyHdGWlQbNTLZw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFonts = exports.useFont = exports.matchFont = exports.listFontFamilies = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _Skia = require(_dependencyMap[1], \"../Skia\");\n  var _types = require(_dependencyMap[2], \"../types\");\n  var _Platform = require(_dependencyMap[3], \"../../Platform\");\n  var _Typeface = require(_dependencyMap[4], \"./Typeface\");\n  /*global SkiaApi*/\n\n  const defaultFontSize = 14;\n\n  /**\n   * Returns a Skia Font object\n   * */\n  const useFont = (font, size = defaultFontSize, onError) => {\n    const typeface = (0, _Typeface.useTypeface)(font, onError);\n    return (0, _react.useMemo)(() => {\n      if (typeface) {\n        return _Skia.Skia.Font(typeface, size);\n      } else {\n        return null;\n      }\n    }, [size, typeface]);\n  };\n  exports.useFont = useFont;\n  const defaultFontStyle = {\n    fontFamily: \"System\",\n    fontSize: defaultFontSize,\n    fontStyle: \"normal\",\n    fontWeight: \"normal\"\n  };\n  const slant = s => {\n    if (s === \"italic\") {\n      return _types.FontSlant.Italic;\n    } else if (s === \"oblique\") {\n      return _types.FontSlant.Oblique;\n    } else {\n      return _types.FontSlant.Upright;\n    }\n  };\n  const weight = fontWeight => {\n    switch (fontWeight) {\n      case \"normal\":\n        return 400;\n      case \"bold\":\n        return 700;\n      default:\n        return parseInt(fontWeight, 10);\n    }\n  };\n  const matchFont = (inputStyle = {}, fontMgr = _Skia.Skia.FontMgr.System()) => {\n    const fontStyle = {\n      ...defaultFontStyle,\n      ...inputStyle\n    };\n    const style = {\n      weight: weight(fontStyle.fontWeight),\n      width: 5,\n      slant: slant(fontStyle.fontStyle)\n    };\n    const typeface = fontMgr.matchFamilyStyle(fontStyle.fontFamily, style);\n    return _Skia.Skia.Font(typeface, fontStyle.fontSize);\n  };\n  exports.matchFont = matchFont;\n  const listFontFamilies = (fontMgr = _Skia.Skia.FontMgr.System()) => new Array(fontMgr.countFamilies()).fill(0).map((_, i) => fontMgr.getFamilyName(i));\n  exports.listFontFamilies = listFontFamilies;\n  const loadTypefaces = typefacesToLoad => {\n    const promises = Object.keys(typefacesToLoad).flatMap(familyName => {\n      return typefacesToLoad[familyName].map(typefaceToLoad => {\n        return _Skia.Skia.Data.fromURI(_Platform.Platform.resolveAsset(typefaceToLoad)).then(data => {\n          const tf = _Skia.Skia.Typeface.MakeFreeTypeFaceFromData(data);\n          if (tf === null) {\n            throw new Error(`Couldn't create typeface for ${familyName}`);\n          }\n          return [familyName, tf];\n        });\n      });\n    });\n    return Promise.all(promises);\n  };\n  const useFonts = sources => {\n    const [fontMgr, setFontMgr] = (0, _react.useState)(null);\n    (0, _react.useEffect)(() => {\n      loadTypefaces(sources).then(result => {\n        const fMgr = _Skia.Skia.TypefaceFontProvider.Make();\n        result.forEach(([familyName, typeface]) => {\n          fMgr.registerFont(typeface, familyName);\n        });\n        setFontMgr(fMgr);\n      });\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return fontMgr;\n  };\n  exports.useFonts = useFonts;\n});", "lineCount": 99, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "_react"], [6, 12, 2, 0], [6, 15, 2, 0, "require"], [6, 22, 2, 0], [6, 23, 2, 0, "_dependencyMap"], [6, 37, 2, 0], [7, 2, 3, 0], [7, 6, 3, 0, "_Skia"], [7, 11, 3, 0], [7, 14, 3, 0, "require"], [7, 21, 3, 0], [7, 22, 3, 0, "_dependencyMap"], [7, 36, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_types"], [8, 12, 4, 0], [8, 15, 4, 0, "require"], [8, 22, 4, 0], [8, 23, 4, 0, "_dependencyMap"], [8, 37, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_Platform"], [9, 15, 5, 0], [9, 18, 5, 0, "require"], [9, 25, 5, 0], [9, 26, 5, 0, "_dependencyMap"], [9, 40, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_Typeface"], [10, 15, 6, 0], [10, 18, 6, 0, "require"], [10, 25, 6, 0], [10, 26, 6, 0, "_dependencyMap"], [10, 40, 6, 0], [11, 2, 1, 0], [13, 2, 7, 0], [13, 8, 7, 6, "defaultFontSize"], [13, 23, 7, 21], [13, 26, 7, 24], [13, 28, 7, 26], [15, 2, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 2, 12, 7], [18, 8, 12, 13, "useFont"], [18, 15, 12, 20], [18, 18, 12, 23, "useFont"], [18, 19, 12, 24, "font"], [18, 23, 12, 28], [18, 25, 12, 30, "size"], [18, 29, 12, 34], [18, 32, 12, 37, "defaultFontSize"], [18, 47, 12, 52], [18, 49, 12, 54, "onError"], [18, 56, 12, 61], [18, 61, 12, 66], [19, 4, 13, 2], [19, 10, 13, 8, "typeface"], [19, 18, 13, 16], [19, 21, 13, 19], [19, 25, 13, 19, "useTypeface"], [19, 46, 13, 30], [19, 48, 13, 31, "font"], [19, 52, 13, 35], [19, 54, 13, 37, "onError"], [19, 61, 13, 44], [19, 62, 13, 45], [20, 4, 14, 2], [20, 11, 14, 9], [20, 15, 14, 9, "useMemo"], [20, 29, 14, 16], [20, 31, 14, 17], [20, 37, 14, 23], [21, 6, 15, 4], [21, 10, 15, 8, "typeface"], [21, 18, 15, 16], [21, 20, 15, 18], [22, 8, 16, 6], [22, 15, 16, 13, "Skia"], [22, 25, 16, 17], [22, 26, 16, 18, "Font"], [22, 30, 16, 22], [22, 31, 16, 23, "typeface"], [22, 39, 16, 31], [22, 41, 16, 33, "size"], [22, 45, 16, 37], [22, 46, 16, 38], [23, 6, 17, 4], [23, 7, 17, 5], [23, 13, 17, 11], [24, 8, 18, 6], [24, 15, 18, 13], [24, 19, 18, 17], [25, 6, 19, 4], [26, 4, 20, 2], [26, 5, 20, 3], [26, 7, 20, 5], [26, 8, 20, 6, "size"], [26, 12, 20, 10], [26, 14, 20, 12, "typeface"], [26, 22, 20, 20], [26, 23, 20, 21], [26, 24, 20, 22], [27, 2, 21, 0], [27, 3, 21, 1], [28, 2, 21, 2, "exports"], [28, 9, 21, 2], [28, 10, 21, 2, "useFont"], [28, 17, 21, 2], [28, 20, 21, 2, "useFont"], [28, 27, 21, 2], [29, 2, 22, 0], [29, 8, 22, 6, "defaultFontStyle"], [29, 24, 22, 22], [29, 27, 22, 25], [30, 4, 23, 2, "fontFamily"], [30, 14, 23, 12], [30, 16, 23, 14], [30, 24, 23, 22], [31, 4, 24, 2, "fontSize"], [31, 12, 24, 10], [31, 14, 24, 12, "defaultFontSize"], [31, 29, 24, 27], [32, 4, 25, 2, "fontStyle"], [32, 13, 25, 11], [32, 15, 25, 13], [32, 23, 25, 21], [33, 4, 26, 2, "fontWeight"], [33, 14, 26, 12], [33, 16, 26, 14], [34, 2, 27, 0], [34, 3, 27, 1], [35, 2, 28, 0], [35, 8, 28, 6, "slant"], [35, 13, 28, 11], [35, 16, 28, 14, "s"], [35, 17, 28, 15], [35, 21, 28, 19], [36, 4, 29, 2], [36, 8, 29, 6, "s"], [36, 9, 29, 7], [36, 14, 29, 12], [36, 22, 29, 20], [36, 24, 29, 22], [37, 6, 30, 4], [37, 13, 30, 11, "FontSlant"], [37, 29, 30, 20], [37, 30, 30, 21, "Italic"], [37, 36, 30, 27], [38, 4, 31, 2], [38, 5, 31, 3], [38, 11, 31, 9], [38, 15, 31, 13, "s"], [38, 16, 31, 14], [38, 21, 31, 19], [38, 30, 31, 28], [38, 32, 31, 30], [39, 6, 32, 4], [39, 13, 32, 11, "FontSlant"], [39, 29, 32, 20], [39, 30, 32, 21, "Oblique"], [39, 37, 32, 28], [40, 4, 33, 2], [40, 5, 33, 3], [40, 11, 33, 9], [41, 6, 34, 4], [41, 13, 34, 11, "FontSlant"], [41, 29, 34, 20], [41, 30, 34, 21, "Upright"], [41, 37, 34, 28], [42, 4, 35, 2], [43, 2, 36, 0], [43, 3, 36, 1], [44, 2, 37, 0], [44, 8, 37, 6, "weight"], [44, 14, 37, 12], [44, 17, 37, 15, "fontWeight"], [44, 27, 37, 25], [44, 31, 37, 29], [45, 4, 38, 2], [45, 12, 38, 10, "fontWeight"], [45, 22, 38, 20], [46, 6, 39, 4], [46, 11, 39, 9], [46, 19, 39, 17], [47, 8, 40, 6], [47, 15, 40, 13], [47, 18, 40, 16], [48, 6, 41, 4], [48, 11, 41, 9], [48, 17, 41, 15], [49, 8, 42, 6], [49, 15, 42, 13], [49, 18, 42, 16], [50, 6, 43, 4], [51, 8, 44, 6], [51, 15, 44, 13, "parseInt"], [51, 23, 44, 21], [51, 24, 44, 22, "fontWeight"], [51, 34, 44, 32], [51, 36, 44, 34], [51, 38, 44, 36], [51, 39, 44, 37], [52, 4, 45, 2], [53, 2, 46, 0], [53, 3, 46, 1], [54, 2, 47, 7], [54, 8, 47, 13, "matchFont"], [54, 17, 47, 22], [54, 20, 47, 25, "matchFont"], [54, 21, 47, 26, "inputStyle"], [54, 31, 47, 36], [54, 34, 47, 39], [54, 35, 47, 40], [54, 36, 47, 41], [54, 38, 47, 43, "fontMgr"], [54, 45, 47, 50], [54, 48, 47, 53, "Skia"], [54, 58, 47, 57], [54, 59, 47, 58, "FontMgr"], [54, 66, 47, 65], [54, 67, 47, 66, "System"], [54, 73, 47, 72], [54, 74, 47, 73], [54, 75, 47, 74], [54, 80, 47, 79], [55, 4, 48, 2], [55, 10, 48, 8, "fontStyle"], [55, 19, 48, 17], [55, 22, 48, 20], [56, 6, 49, 4], [56, 9, 49, 7, "defaultFontStyle"], [56, 25, 49, 23], [57, 6, 50, 4], [57, 9, 50, 7, "inputStyle"], [58, 4, 51, 2], [58, 5, 51, 3], [59, 4, 52, 2], [59, 10, 52, 8, "style"], [59, 15, 52, 13], [59, 18, 52, 16], [60, 6, 53, 4, "weight"], [60, 12, 53, 10], [60, 14, 53, 12, "weight"], [60, 20, 53, 18], [60, 21, 53, 19, "fontStyle"], [60, 30, 53, 28], [60, 31, 53, 29, "fontWeight"], [60, 41, 53, 39], [60, 42, 53, 40], [61, 6, 54, 4, "width"], [61, 11, 54, 9], [61, 13, 54, 11], [61, 14, 54, 12], [62, 6, 55, 4, "slant"], [62, 11, 55, 9], [62, 13, 55, 11, "slant"], [62, 18, 55, 16], [62, 19, 55, 17, "fontStyle"], [62, 28, 55, 26], [62, 29, 55, 27, "fontStyle"], [62, 38, 55, 36], [63, 4, 56, 2], [63, 5, 56, 3], [64, 4, 57, 2], [64, 10, 57, 8, "typeface"], [64, 18, 57, 16], [64, 21, 57, 19, "fontMgr"], [64, 28, 57, 26], [64, 29, 57, 27, "matchFamilyStyle"], [64, 45, 57, 43], [64, 46, 57, 44, "fontStyle"], [64, 55, 57, 53], [64, 56, 57, 54, "fontFamily"], [64, 66, 57, 64], [64, 68, 57, 66, "style"], [64, 73, 57, 71], [64, 74, 57, 72], [65, 4, 58, 2], [65, 11, 58, 9, "Skia"], [65, 21, 58, 13], [65, 22, 58, 14, "Font"], [65, 26, 58, 18], [65, 27, 58, 19, "typeface"], [65, 35, 58, 27], [65, 37, 58, 29, "fontStyle"], [65, 46, 58, 38], [65, 47, 58, 39, "fontSize"], [65, 55, 58, 47], [65, 56, 58, 48], [66, 2, 59, 0], [66, 3, 59, 1], [67, 2, 59, 2, "exports"], [67, 9, 59, 2], [67, 10, 59, 2, "matchFont"], [67, 19, 59, 2], [67, 22, 59, 2, "matchFont"], [67, 31, 59, 2], [68, 2, 60, 7], [68, 8, 60, 13, "listFontFamilies"], [68, 24, 60, 29], [68, 27, 60, 32, "listFontFamilies"], [68, 28, 60, 33, "fontMgr"], [68, 35, 60, 40], [68, 38, 60, 43, "Skia"], [68, 48, 60, 47], [68, 49, 60, 48, "FontMgr"], [68, 56, 60, 55], [68, 57, 60, 56, "System"], [68, 63, 60, 62], [68, 64, 60, 63], [68, 65, 60, 64], [68, 70, 60, 69], [68, 74, 60, 73, "Array"], [68, 79, 60, 78], [68, 80, 60, 79, "fontMgr"], [68, 87, 60, 86], [68, 88, 60, 87, "countFamilies"], [68, 101, 60, 100], [68, 102, 60, 101], [68, 103, 60, 102], [68, 104, 60, 103], [68, 105, 60, 104, "fill"], [68, 109, 60, 108], [68, 110, 60, 109], [68, 111, 60, 110], [68, 112, 60, 111], [68, 113, 60, 112, "map"], [68, 116, 60, 115], [68, 117, 60, 116], [68, 118, 60, 117, "_"], [68, 119, 60, 118], [68, 121, 60, 120, "i"], [68, 122, 60, 121], [68, 127, 60, 126, "fontMgr"], [68, 134, 60, 133], [68, 135, 60, 134, "getFamilyName"], [68, 148, 60, 147], [68, 149, 60, 148, "i"], [68, 150, 60, 149], [68, 151, 60, 150], [68, 152, 60, 151], [69, 2, 60, 152, "exports"], [69, 9, 60, 152], [69, 10, 60, 152, "listFontFamilies"], [69, 26, 60, 152], [69, 29, 60, 152, "listFontFamilies"], [69, 45, 60, 152], [70, 2, 61, 0], [70, 8, 61, 6, "loadTypefaces"], [70, 21, 61, 19], [70, 24, 61, 22, "typefacesToLoad"], [70, 39, 61, 37], [70, 43, 61, 41], [71, 4, 62, 2], [71, 10, 62, 8, "promises"], [71, 18, 62, 16], [71, 21, 62, 19, "Object"], [71, 27, 62, 25], [71, 28, 62, 26, "keys"], [71, 32, 62, 30], [71, 33, 62, 31, "typefacesToLoad"], [71, 48, 62, 46], [71, 49, 62, 47], [71, 50, 62, 48, "flatMap"], [71, 57, 62, 55], [71, 58, 62, 56, "<PERSON><PERSON>ame"], [71, 68, 62, 66], [71, 72, 62, 70], [72, 6, 63, 4], [72, 13, 63, 11, "typefacesToLoad"], [72, 28, 63, 26], [72, 29, 63, 27, "<PERSON><PERSON>ame"], [72, 39, 63, 37], [72, 40, 63, 38], [72, 41, 63, 39, "map"], [72, 44, 63, 42], [72, 45, 63, 43, "typefaceToLoad"], [72, 59, 63, 57], [72, 63, 63, 61], [73, 8, 64, 6], [73, 15, 64, 13, "Skia"], [73, 25, 64, 17], [73, 26, 64, 18, "Data"], [73, 30, 64, 22], [73, 31, 64, 23, "fromURI"], [73, 38, 64, 30], [73, 39, 64, 31, "Platform"], [73, 57, 64, 39], [73, 58, 64, 40, "resolveAsset"], [73, 70, 64, 52], [73, 71, 64, 53, "typefaceToLoad"], [73, 85, 64, 67], [73, 86, 64, 68], [73, 87, 64, 69], [73, 88, 64, 70, "then"], [73, 92, 64, 74], [73, 93, 64, 75, "data"], [73, 97, 64, 79], [73, 101, 64, 83], [74, 10, 65, 8], [74, 16, 65, 14, "tf"], [74, 18, 65, 16], [74, 21, 65, 19, "Skia"], [74, 31, 65, 23], [74, 32, 65, 24, "Typeface"], [74, 40, 65, 32], [74, 41, 65, 33, "MakeFreeTypeFaceFromData"], [74, 65, 65, 57], [74, 66, 65, 58, "data"], [74, 70, 65, 62], [74, 71, 65, 63], [75, 10, 66, 8], [75, 14, 66, 12, "tf"], [75, 16, 66, 14], [75, 21, 66, 19], [75, 25, 66, 23], [75, 27, 66, 25], [76, 12, 67, 10], [76, 18, 67, 16], [76, 22, 67, 20, "Error"], [76, 27, 67, 25], [76, 28, 67, 26], [76, 60, 67, 58, "<PERSON><PERSON>ame"], [76, 70, 67, 68], [76, 72, 67, 70], [76, 73, 67, 71], [77, 10, 68, 8], [78, 10, 69, 8], [78, 17, 69, 15], [78, 18, 69, 16, "<PERSON><PERSON>ame"], [78, 28, 69, 26], [78, 30, 69, 28, "tf"], [78, 32, 69, 30], [78, 33, 69, 31], [79, 8, 70, 6], [79, 9, 70, 7], [79, 10, 70, 8], [80, 6, 71, 4], [80, 7, 71, 5], [80, 8, 71, 6], [81, 4, 72, 2], [81, 5, 72, 3], [81, 6, 72, 4], [82, 4, 73, 2], [82, 11, 73, 9, "Promise"], [82, 18, 73, 16], [82, 19, 73, 17, "all"], [82, 22, 73, 20], [82, 23, 73, 21, "promises"], [82, 31, 73, 29], [82, 32, 73, 30], [83, 2, 74, 0], [83, 3, 74, 1], [84, 2, 75, 7], [84, 8, 75, 13, "useFonts"], [84, 16, 75, 21], [84, 19, 75, 24, "sources"], [84, 26, 75, 31], [84, 30, 75, 35], [85, 4, 76, 2], [85, 10, 76, 8], [85, 11, 76, 9, "fontMgr"], [85, 18, 76, 16], [85, 20, 76, 18, "setFontMgr"], [85, 30, 76, 28], [85, 31, 76, 29], [85, 34, 76, 32], [85, 38, 76, 32, "useState"], [85, 53, 76, 40], [85, 55, 76, 41], [85, 59, 76, 45], [85, 60, 76, 46], [86, 4, 77, 2], [86, 8, 77, 2, "useEffect"], [86, 24, 77, 11], [86, 26, 77, 12], [86, 32, 77, 18], [87, 6, 78, 4, "loadTypefaces"], [87, 19, 78, 17], [87, 20, 78, 18, "sources"], [87, 27, 78, 25], [87, 28, 78, 26], [87, 29, 78, 27, "then"], [87, 33, 78, 31], [87, 34, 78, 32, "result"], [87, 40, 78, 38], [87, 44, 78, 42], [88, 8, 79, 6], [88, 14, 79, 12, "fMgr"], [88, 18, 79, 16], [88, 21, 79, 19, "Skia"], [88, 31, 79, 23], [88, 32, 79, 24, "TypefaceFontProvider"], [88, 52, 79, 44], [88, 53, 79, 45, "Make"], [88, 57, 79, 49], [88, 58, 79, 50], [88, 59, 79, 51], [89, 8, 80, 6, "result"], [89, 14, 80, 12], [89, 15, 80, 13, "for<PERSON>ach"], [89, 22, 80, 20], [89, 23, 80, 21], [89, 24, 80, 22], [89, 25, 80, 23, "<PERSON><PERSON>ame"], [89, 35, 80, 33], [89, 37, 80, 35, "typeface"], [89, 45, 80, 43], [89, 46, 80, 44], [89, 51, 80, 49], [90, 10, 81, 8, "fMgr"], [90, 14, 81, 12], [90, 15, 81, 13, "registerFont"], [90, 27, 81, 25], [90, 28, 81, 26, "typeface"], [90, 36, 81, 34], [90, 38, 81, 36, "<PERSON><PERSON>ame"], [90, 48, 81, 46], [90, 49, 81, 47], [91, 8, 82, 6], [91, 9, 82, 7], [91, 10, 82, 8], [92, 8, 83, 6, "setFontMgr"], [92, 18, 83, 16], [92, 19, 83, 17, "fMgr"], [92, 23, 83, 21], [92, 24, 83, 22], [93, 6, 84, 4], [93, 7, 84, 5], [93, 8, 84, 6], [94, 6, 85, 4], [95, 4, 86, 2], [95, 5, 86, 3], [95, 7, 86, 5], [95, 9, 86, 7], [95, 10, 86, 8], [96, 4, 87, 2], [96, 11, 87, 9, "fontMgr"], [96, 18, 87, 16], [97, 2, 88, 0], [97, 3, 88, 1], [98, 2, 88, 2, "exports"], [98, 9, 88, 2], [98, 10, 88, 2, "useFonts"], [98, 18, 88, 2], [98, 21, 88, 2, "useFonts"], [98, 29, 88, 2], [99, 0, 88, 2], [99, 3]], "functionMap": {"names": ["<global>", "useFont", "useMemo$argument_0", "slant", "weight", "matchFont", "listFontFamilies", "Array.fill.map$argument_0", "loadTypefaces", "Object.keys.flatMap$argument_0", "typefacesToLoad.familyName.map$argument_0", "Skia.Data.fromURI.then$argument_0", "useFonts", "useEffect$argument_0", "loadTypefaces.then$argument_0", "result.forEach$argument_0"], "mappings": "AAA;uBCW;iBCE;GDM;CDC;cGO;CHQ;eIC;CJS;yBKC;CLY;gCMC,oFC,kCD,CN;sBQC;wDCC;2CCC;2ECC;ODM;KDC;GDC;CRE;wBYC;YCE;gCCC;qBCE;ODE;KDE;GDE;CZE"}}, "type": "js/module"}]}