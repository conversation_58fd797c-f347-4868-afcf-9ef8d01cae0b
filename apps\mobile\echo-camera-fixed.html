<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Echo Camera - Fixed Face Blurring</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: white;
            overflow: hidden;
        }
        .camera-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .overlay-canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 10;
        }
        .header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 20;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .privacy-badge {
            background: rgba(16, 185, 129, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-indicator {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            backdrop-filter: blur(10px);
        }
        .controls {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 20;
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .capture-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: white;
            border: 4px solid rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .capture-btn:hover {
            transform: scale(1.1);
        }
        .capture-btn:active {
            transform: scale(0.95);
        }
        .control-btn {
            background: rgba(0, 0, 0, 0.6);
            border: none;
            color: white;
            padding: 12px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 30;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            background: rgba(220, 38, 38, 0.9);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="camera-container">
        <!-- Loading State -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <div>Initializing camera and face detection...</div>
        </div>

        <!-- Camera Video -->
        <video id="video" autoplay muted playsinline style="display: none;"></video>
        
        <!-- Face Detection Overlay -->
        <canvas id="overlay" class="overlay-canvas" style="display: none;"></canvas>

        <!-- Header -->
        <div class="header">
            <div class="privacy-badge">
                🔒 Privacy Protected
            </div>
            <div id="status" class="status-indicator">
                Initializing...
            </div>
        </div>

        <!-- Controls -->
        <div class="controls" style="display: none;" id="controls">
            <button class="control-btn" onclick="toggleDetection()">
                <span id="toggle-text">Pause Detection</span>
            </button>
            <div class="capture-btn" onclick="capturePhoto()">
                📷
            </div>
            <button class="control-btn" onclick="switchCamera()">
                🔄 Flip
            </button>
        </div>

        <!-- Error Display -->
        <div id="error" style="display: none;"></div>
    </div>

    <script>
        let video, overlay, ctx;
        let stream = null;
        let model = null;
        let isDetecting = false;
        let animationId = null;
        let facingMode = 'user';
        let faceCount = 0;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            video = document.getElementById('video');
            overlay = document.getElementById('overlay');
            ctx = overlay.getContext('2d');
            
            initializeCamera();
        });

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            console.log(`[EchoCamera] ${message}`);
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.innerHTML = `<div class="error">❌ ${message}</div>`;
            errorDiv.style.display = 'block';
            updateStatus('Error occurred', 'error');
        }

        async function loadTensorFlowAndBlazeFace() {
            try {
                updateStatus('Loading TensorFlow.js...');
                
                // Load TensorFlow.js
                if (!window.tf) {
                    await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js');
                }

                // Load BlazeFace
                if (!window.blazeface) {
                    await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');
                }

                updateStatus('Initializing BlazeFace model...');
                model = await window.blazeface.load();
                updateStatus('Face detection ready');
                
                return true;
            } catch (error) {
                showError(`Failed to load face detection: ${error.message}`);
                return false;
            }
        }

        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.async = true;
                script.onload = () => resolve();
                script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
                document.head.appendChild(script);
            });
        }

        async function startCamera() {
            try {
                updateStatus('Requesting camera access...');
                
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 1280 }, 
                        height: { ideal: 720 },
                        facingMode: facingMode
                    } 
                });
                
                video.srcObject = stream;
                video.style.display = 'block';
                
                video.onloadedmetadata = () => {
                    // Set overlay canvas size to match video
                    overlay.width = video.videoWidth;
                    overlay.height = video.videoHeight;
                    overlay.style.width = '100%';
                    overlay.style.height = '100%';
                    overlay.style.display = 'block';
                    
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('controls').style.display = 'flex';
                    
                    updateStatus('Camera ready');
                    startDetection();
                };
                
            } catch (error) {
                showError(`Camera access failed: ${error.message}`);
            }
        }

        async function initializeCamera() {
            const success = await loadTensorFlowAndBlazeFace();
            if (success) {
                await startCamera();
            }
        }

        function startDetection() {
            if (!model || isDetecting) return;
            
            isDetecting = true;
            updateStatus('Face detection active');
            document.getElementById('toggle-text').textContent = 'Pause Detection';
            detectLoop();
        }

        function stopDetection() {
            isDetecting = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            updateStatus('Face detection paused');
            document.getElementById('toggle-text').textContent = 'Resume Detection';
            
            // Clear overlay
            ctx.clearRect(0, 0, overlay.width, overlay.height);
        }

        function toggleDetection() {
            if (isDetecting) {
                stopDetection();
            } else {
                startDetection();
            }
        }

        async function detectLoop() {
            if (!isDetecting) return;

            try {
                // Detect faces
                const tensor = window.tf.browser.fromPixels(video);
                const predictions = await model.estimateFaces(tensor, false, 0.6);
                tensor.dispose();

                // Clear overlay
                ctx.clearRect(0, 0, overlay.width, overlay.height);

                if (predictions.length > 0) {
                    faceCount = predictions.length;
                    updateStatus(`Protecting ${faceCount} face${faceCount > 1 ? 's' : ''}`);
                    
                    // Draw face detection and blur
                    predictions.forEach((prediction, index) => {
                        const [x1, y1] = prediction.topLeft;
                        const [x2, y2] = prediction.bottomRight;

                        console.log(`Face ${index + 1}:`, { x1, y1, x2, y2 });

                        // Fix coordinate order - BlazeFace might return them in different order
                        let minX = Math.min(x1, x2);
                        let maxX = Math.max(x1, x2);
                        const minY = Math.min(y1, y2);
                        const maxY = Math.max(y1, y2);

                        // Account for horizontal flip (mirror effect) - flip X coordinates
                        const canvasWidth = overlay.width;
                        const flippedMinX = canvasWidth - maxX;
                        const flippedMaxX = canvasWidth - minX;
                        minX = flippedMinX;
                        maxX = flippedMaxX;

                        // Calculate face dimensions
                        const faceWidth = maxX - minX;
                        const faceHeight = maxY - minY;

                        if (faceWidth <= 0 || faceHeight <= 0) {
                            console.warn(`Invalid face dimensions for face ${index + 1}`);
                            return;
                        }

                        // Expand the bounding box for better coverage
                        const centerX = (minX + maxX) / 2;
                        const centerY = (minY + maxY) / 2;
                        const expandedWidth = faceWidth * 1.5;
                        const expandedHeight = faceHeight * 1.8;

                        // Ensure positive radii
                        const radiusX = Math.max(expandedWidth / 2, 10);
                        const radiusY = Math.max(expandedHeight / 2, 10);

                        console.log(`Drawing blur at (${centerX}, ${centerY}) with radii (${radiusX}, ${radiusY})`);

                        // Method 1: Simple rectangle overlay for debugging
                        ctx.save();
                        ctx.fillStyle = 'rgba(255, 0, 0, 0.3)'; // Semi-transparent red
                        ctx.fillRect(minX, minY, faceWidth, faceHeight);
                        ctx.restore();

                        // Method 2: Also try ellipse blur
                        ctx.save();
                        ctx.beginPath();
                        ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);
                        ctx.clip();
                        ctx.filter = 'blur(35px)';
                        ctx.drawImage(video, 0, 0, overlay.width, overlay.height);
                        ctx.restore();
                    });
                } else {
                    updateStatus('Scanning for faces...');
                }

            } catch (error) {
                console.error('Detection error:', error);
            }

            animationId = requestAnimationFrame(detectLoop);
        }

        async function switchCamera() {
            facingMode = facingMode === 'user' ? 'environment' : 'user';
            
            // Stop current stream
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            
            // Restart with new facing mode
            await startCamera();
        }

        async function capturePhoto() {
            if (!video.videoWidth || !video.videoHeight) {
                showError('Camera not ready for capture');
                return;
            }

            try {
                updateStatus('Capturing photo...');
                
                // Create a canvas for the final image
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const captureCtx = canvas.getContext('2d');
                
                // Draw the video frame
                captureCtx.drawImage(video, 0, 0);
                
                // Apply face blurring to the captured image
                if (model) {
                    const tensor = window.tf.browser.fromPixels(video);
                    const predictions = await model.estimateFaces(tensor, false, 0.6);
                    tensor.dispose();
                    
                    predictions.forEach(prediction => {
                        const [x1, y1] = prediction.topLeft;
                        const [x2, y2] = prediction.bottomRight;

                        // Validate coordinates
                        if (x1 >= x2 || y1 >= y2) {
                            console.warn('Invalid face coordinates in capture');
                            return;
                        }

                        // Calculate face dimensions with safety checks
                        const faceWidth = Math.abs(x2 - x1);
                        const faceHeight = Math.abs(y2 - y1);

                        if (faceWidth <= 0 || faceHeight <= 0) {
                            console.warn('Invalid face dimensions in capture');
                            return;
                        }

                        const centerX = (x1 + x2) / 2;
                        const centerY = (y1 + y2) / 2;
                        const expandedWidth = faceWidth * 1.5;
                        const expandedHeight = faceHeight * 1.8;

                        // Ensure positive radii
                        const radiusX = Math.max(expandedWidth / 2, 10);
                        const radiusY = Math.max(expandedHeight / 2, 10);

                        // Apply blur to captured image
                        captureCtx.save();
                        captureCtx.beginPath();
                        captureCtx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);
                        captureCtx.clip();
                        captureCtx.filter = 'blur(35px)';
                        captureCtx.drawImage(video, 0, 0);
                        captureCtx.restore();
                    });
                }
                
                // Convert to blob and download
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `echo-privacy-photo-${Date.now()}.jpg`;
                    a.click();
                    URL.revokeObjectURL(url);
                    
                    updateStatus('Photo saved!');
                    setTimeout(() => {
                        if (isDetecting) {
                            updateStatus(`Protecting ${faceCount} face${faceCount > 1 ? 's' : ''}`);
                        }
                    }, 2000);
                }, 'image/jpeg', 0.9);
                
            } catch (error) {
                showError(`Capture failed: ${error.message}`);
            }
        }
    </script>
</body>
</html>
