{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 64, "index": 64}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkFontMgr = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  class JsiSkFontMgr extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"FontMgr\");\n    }\n    dispose() {\n      this.ref.delete();\n    }\n    countFamilies() {\n      return this.ref.countFamilies();\n    }\n    getFamilyName(index) {\n      return this.ref.getFamilyName(index);\n    }\n    matchFamilyStyle(_familyName, _fontStyle) {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n  }\n  exports.JsiSkFontMgr = JsiSkFontMgr;\n});", "lineCount": 25, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 7], [7, 8, 2, 13, "JsiSkFontMgr"], [7, 20, 2, 25], [7, 29, 2, 34, "HostObject"], [7, 45, 2, 44], [7, 46, 2, 45], [8, 4, 3, 2, "constructor"], [8, 15, 3, 13, "constructor"], [8, 16, 3, 14, "CanvasKit"], [8, 25, 3, 23], [8, 27, 3, 25, "ref"], [8, 30, 3, 28], [8, 32, 3, 30], [9, 6, 4, 4], [9, 11, 4, 9], [9, 12, 4, 10, "CanvasKit"], [9, 21, 4, 19], [9, 23, 4, 21, "ref"], [9, 26, 4, 24], [9, 28, 4, 26], [9, 37, 4, 35], [9, 38, 4, 36], [10, 4, 5, 2], [11, 4, 6, 2, "dispose"], [11, 11, 6, 9, "dispose"], [11, 12, 6, 9], [11, 14, 6, 12], [12, 6, 7, 4], [12, 10, 7, 8], [12, 11, 7, 9, "ref"], [12, 14, 7, 12], [12, 15, 7, 13, "delete"], [12, 21, 7, 19], [12, 22, 7, 20], [12, 23, 7, 21], [13, 4, 8, 2], [14, 4, 9, 2, "countFamilies"], [14, 17, 9, 15, "countFamilies"], [14, 18, 9, 15], [14, 20, 9, 18], [15, 6, 10, 4], [15, 13, 10, 11], [15, 17, 10, 15], [15, 18, 10, 16, "ref"], [15, 21, 10, 19], [15, 22, 10, 20, "countFamilies"], [15, 35, 10, 33], [15, 36, 10, 34], [15, 37, 10, 35], [16, 4, 11, 2], [17, 4, 12, 2, "getFamilyName"], [17, 17, 12, 15, "getFamilyName"], [17, 18, 12, 16, "index"], [17, 23, 12, 21], [17, 25, 12, 23], [18, 6, 13, 4], [18, 13, 13, 11], [18, 17, 13, 15], [18, 18, 13, 16, "ref"], [18, 21, 13, 19], [18, 22, 13, 20, "getFamilyName"], [18, 35, 13, 33], [18, 36, 13, 34, "index"], [18, 41, 13, 39], [18, 42, 13, 40], [19, 4, 14, 2], [20, 4, 15, 2, "matchFamilyStyle"], [20, 20, 15, 18, "matchFamilyStyle"], [20, 21, 15, 19, "_familyName"], [20, 32, 15, 30], [20, 34, 15, 32, "_fontStyle"], [20, 44, 15, 42], [20, 46, 15, 44], [21, 6, 16, 4], [21, 13, 16, 11], [21, 17, 16, 11, "throwNotImplementedOnRNWeb"], [21, 49, 16, 37], [21, 51, 16, 38], [21, 52, 16, 39], [22, 4, 17, 2], [23, 2, 18, 0], [24, 2, 18, 1, "exports"], [24, 9, 18, 1], [24, 10, 18, 1, "JsiSkFontMgr"], [24, 22, 18, 1], [24, 25, 18, 1, "JsiSkFontMgr"], [24, 37, 18, 1], [25, 0, 18, 1], [25, 3]], "functionMap": {"names": ["<global>", "JsiSkFontMgr", "constructor", "dispose", "countFamilies", "getFamilyName", "matchFamilyStyle"], "mappings": "AAA;OCC;ECC;GDE;EEC;GFE;EGC;GHE;EIC;GJE;EKC;GLE;CDC"}}, "type": "js/module"}]}