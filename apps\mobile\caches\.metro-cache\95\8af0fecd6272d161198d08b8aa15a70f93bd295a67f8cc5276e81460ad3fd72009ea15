{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./CropAction.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "Hy+sFGi/KBtp9s/BXLbHBy4ZjjM=", "exportNames": ["*"]}}, {"name": "./ExtentAction.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 52}, "end": {"line": 2, "column": 55, "index": 107}}], "key": "scZfHq7pVbmaZL7rX/gRtevLJ98=", "exportNames": ["*"]}}, {"name": "./FlipAction.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 108}, "end": {"line": 3, "column": 51, "index": 159}}], "key": "ZbSYkkZhnotrEBlrBc4Kj4iFYw8=", "exportNames": ["*"]}}, {"name": "./ResizeAction.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 160}, "end": {"line": 4, "column": 55, "index": 215}}], "key": "KweYF+bMuPT1qiXMjJ5M1qHWIKM=", "exportNames": ["*"]}}, {"name": "./RotateAction.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 216}, "end": {"line": 5, "column": 55, "index": 271}}], "key": "uCTGESZ4VRXyrlXnkQo/7Os/tQw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"crop\", {\n    enumerable: true,\n    get: function () {\n      return _CropAction.default;\n    }\n  });\n  Object.defineProperty(exports, \"extent\", {\n    enumerable: true,\n    get: function () {\n      return _ExtentAction.default;\n    }\n  });\n  Object.defineProperty(exports, \"flip\", {\n    enumerable: true,\n    get: function () {\n      return _FlipAction.default;\n    }\n  });\n  Object.defineProperty(exports, \"resize\", {\n    enumerable: true,\n    get: function () {\n      return _ResizeAction.default;\n    }\n  });\n  Object.defineProperty(exports, \"rotate\", {\n    enumerable: true,\n    get: function () {\n      return _RotateAction.default;\n    }\n  });\n  var _CropAction = _interopRequireDefault(require(_dependencyMap[1], \"./CropAction.web\"));\n  var _ExtentAction = _interopRequireDefault(require(_dependencyMap[2], \"./ExtentAction.web\"));\n  var _FlipAction = _interopRequireDefault(require(_dependencyMap[3], \"./FlipAction.web\"));\n  var _ResizeAction = _interopRequireDefault(require(_dependencyMap[4], \"./ResizeAction.web\"));\n  var _RotateAction = _interopRequireDefault(require(_dependencyMap[5], \"./RotateAction.web\"));\n});", "lineCount": 41, "map": [[36, 2, 1, 0], [36, 6, 1, 0, "_CropAction"], [36, 17, 1, 0], [36, 20, 1, 0, "_interopRequireDefault"], [36, 42, 1, 0], [36, 43, 1, 0, "require"], [36, 50, 1, 0], [36, 51, 1, 0, "_dependencyMap"], [36, 65, 1, 0], [37, 2, 2, 0], [37, 6, 2, 0, "_ExtentAction"], [37, 19, 2, 0], [37, 22, 2, 0, "_interopRequireDefault"], [37, 44, 2, 0], [37, 45, 2, 0, "require"], [37, 52, 2, 0], [37, 53, 2, 0, "_dependencyMap"], [37, 67, 2, 0], [38, 2, 3, 0], [38, 6, 3, 0, "_FlipAction"], [38, 17, 3, 0], [38, 20, 3, 0, "_interopRequireDefault"], [38, 42, 3, 0], [38, 43, 3, 0, "require"], [38, 50, 3, 0], [38, 51, 3, 0, "_dependencyMap"], [38, 65, 3, 0], [39, 2, 4, 0], [39, 6, 4, 0, "_ResizeAction"], [39, 19, 4, 0], [39, 22, 4, 0, "_interopRequireDefault"], [39, 44, 4, 0], [39, 45, 4, 0, "require"], [39, 52, 4, 0], [39, 53, 4, 0, "_dependencyMap"], [39, 67, 4, 0], [40, 2, 5, 0], [40, 6, 5, 0, "_RotateAction"], [40, 19, 5, 0], [40, 22, 5, 0, "_interopRequireDefault"], [40, 44, 5, 0], [40, 45, 5, 0, "require"], [40, 52, 5, 0], [40, 53, 5, 0, "_dependencyMap"], [40, 67, 5, 0], [41, 0, 5, 55], [41, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}