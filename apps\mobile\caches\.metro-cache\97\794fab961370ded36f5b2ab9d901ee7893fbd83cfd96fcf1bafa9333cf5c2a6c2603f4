{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        await processImageWithFaceBlur(photo.uri);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] ❌ face-api.js also failed:', faceApiError);\n\n            // Method 3: Fallback - Mock face detection for testing (assumes center face)\n            console.log('[EchoCameraWeb] 🧪 Using fallback mock face detection for testing...');\n            detectedFaces = [{\n              boundingBox: {\n                xCenter: 0.5,\n                // Center of image\n                yCenter: 0.4,\n                // Slightly above center (typical face position)\n                width: 0.3,\n                // 30% of image width\n                height: 0.4 // 40% of image height\n              }\n            }];\n            console.log(`[EchoCameraWeb] 🧪 Mock detection created 1 face at center of image`);\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] Blurring face ${index + 1} at (${paddedX}, ${paddedY}) size ${paddedWidth}x${paddedHeight}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(8, Math.min(paddedWidth, paddedHeight) / 20); // Adaptive pixel size\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n          });\n        } else {\n          console.log('[EchoCameraWeb] No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] Processing complete:', result);\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1340, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 53, 128, 51], [126, 55, 128, 53, "photo"], [126, 60, 128, 58], [126, 61, 128, 59, "uri"], [126, 64, 128, 62], [126, 65, 128, 63], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6], [130, 14, 132, 12, "processImageWithFaceBlur"], [130, 38, 132, 36], [130, 39, 132, 37, "photo"], [130, 44, 132, 42], [130, 45, 132, 43, "uri"], [130, 48, 132, 46], [130, 49, 132, 47], [131, 6, 133, 4], [131, 7, 133, 5], [131, 8, 133, 6], [131, 15, 133, 13, "error"], [131, 20, 133, 18], [131, 22, 133, 20], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "error"], [132, 21, 134, 19], [132, 22, 134, 20], [132, 54, 134, 52], [132, 56, 134, 54, "error"], [132, 61, 134, 59], [132, 62, 134, 60], [133, 8, 135, 6, "setErrorMessage"], [133, 23, 135, 21], [133, 24, 135, 22], [133, 68, 135, 66], [133, 69, 135, 67], [134, 8, 136, 6, "setProcessingState"], [134, 26, 136, 24], [134, 27, 136, 25], [134, 34, 136, 32], [134, 35, 136, 33], [135, 6, 137, 4], [136, 4, 138, 2], [136, 5, 138, 3], [136, 7, 138, 5], [136, 9, 138, 7], [136, 10, 138, 8], [137, 4, 139, 2], [138, 4, 140, 2], [138, 10, 140, 8, "processImageWithFaceBlur"], [138, 34, 140, 32], [138, 37, 140, 35], [138, 43, 140, 42, "photoUri"], [138, 51, 140, 58], [138, 55, 140, 63], [139, 6, 141, 4], [139, 10, 141, 8], [140, 8, 142, 6, "setProcessingState"], [140, 26, 142, 24], [140, 27, 142, 25], [140, 39, 142, 37], [140, 40, 142, 38], [141, 8, 143, 6, "setProcessingProgress"], [141, 29, 143, 27], [141, 30, 143, 28], [141, 32, 143, 30], [141, 33, 143, 31], [143, 8, 145, 6], [144, 8, 146, 6], [144, 14, 146, 12, "canvas"], [144, 20, 146, 18], [144, 23, 146, 21, "document"], [144, 31, 146, 29], [144, 32, 146, 30, "createElement"], [144, 45, 146, 43], [144, 46, 146, 44], [144, 54, 146, 52], [144, 55, 146, 53], [145, 8, 147, 6], [145, 14, 147, 12, "ctx"], [145, 17, 147, 15], [145, 20, 147, 18, "canvas"], [145, 26, 147, 24], [145, 27, 147, 25, "getContext"], [145, 37, 147, 35], [145, 38, 147, 36], [145, 42, 147, 40], [145, 43, 147, 41], [146, 8, 148, 6], [146, 12, 148, 10], [146, 13, 148, 11, "ctx"], [146, 16, 148, 14], [146, 18, 148, 16], [146, 24, 148, 22], [146, 28, 148, 26, "Error"], [146, 33, 148, 31], [146, 34, 148, 32], [146, 64, 148, 62], [146, 65, 148, 63], [148, 8, 150, 6], [149, 8, 151, 6], [149, 14, 151, 12, "img"], [149, 17, 151, 15], [149, 20, 151, 18], [149, 24, 151, 22, "Image"], [149, 29, 151, 27], [149, 30, 151, 28], [149, 31, 151, 29], [150, 8, 152, 6], [150, 14, 152, 12], [150, 18, 152, 16, "Promise"], [150, 25, 152, 23], [150, 26, 152, 24], [150, 27, 152, 25, "resolve"], [150, 34, 152, 32], [150, 36, 152, 34, "reject"], [150, 42, 152, 40], [150, 47, 152, 45], [151, 10, 153, 8, "img"], [151, 13, 153, 11], [151, 14, 153, 12, "onload"], [151, 20, 153, 18], [151, 23, 153, 21, "resolve"], [151, 30, 153, 28], [152, 10, 154, 8, "img"], [152, 13, 154, 11], [152, 14, 154, 12, "onerror"], [152, 21, 154, 19], [152, 24, 154, 22, "reject"], [152, 30, 154, 28], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "src"], [153, 17, 155, 15], [153, 20, 155, 18, "photoUri"], [153, 28, 155, 26], [154, 8, 156, 6], [154, 9, 156, 7], [154, 10, 156, 8], [156, 8, 158, 6], [157, 8, 159, 6, "canvas"], [157, 14, 159, 12], [157, 15, 159, 13, "width"], [157, 20, 159, 18], [157, 23, 159, 21, "img"], [157, 26, 159, 24], [157, 27, 159, 25, "width"], [157, 32, 159, 30], [158, 8, 160, 6, "canvas"], [158, 14, 160, 12], [158, 15, 160, 13, "height"], [158, 21, 160, 19], [158, 24, 160, 22, "img"], [158, 27, 160, 25], [158, 28, 160, 26, "height"], [158, 34, 160, 32], [160, 8, 162, 6], [161, 8, 163, 6, "ctx"], [161, 11, 163, 9], [161, 12, 163, 10, "drawImage"], [161, 21, 163, 19], [161, 22, 163, 20, "img"], [161, 25, 163, 23], [161, 27, 163, 25], [161, 28, 163, 26], [161, 30, 163, 28], [161, 31, 163, 29], [161, 32, 163, 30], [162, 8, 165, 6, "setProcessingProgress"], [162, 29, 165, 27], [162, 30, 165, 28], [162, 32, 165, 30], [162, 33, 165, 31], [164, 8, 167, 6], [165, 8, 168, 6], [165, 12, 168, 10, "detectedFaces"], [165, 25, 168, 23], [165, 28, 168, 26], [165, 30, 168, 28], [166, 8, 170, 6, "console"], [166, 15, 170, 13], [166, 16, 170, 14, "log"], [166, 19, 170, 17], [166, 20, 170, 18], [166, 74, 170, 72], [166, 76, 170, 74], [167, 10, 171, 8, "width"], [167, 15, 171, 13], [167, 17, 171, 15, "img"], [167, 20, 171, 18], [167, 21, 171, 19, "width"], [167, 26, 171, 24], [168, 10, 172, 8, "height"], [168, 16, 172, 14], [168, 18, 172, 16, "img"], [168, 21, 172, 19], [168, 22, 172, 20, "height"], [168, 28, 172, 26], [169, 10, 173, 8, "src"], [169, 13, 173, 11], [169, 15, 173, 13, "photoUri"], [169, 23, 173, 21], [169, 24, 173, 22, "substring"], [169, 33, 173, 31], [169, 34, 173, 32], [169, 35, 173, 33], [169, 37, 173, 35], [169, 39, 173, 37], [169, 40, 173, 38], [169, 43, 173, 41], [170, 8, 174, 6], [170, 9, 174, 7], [170, 10, 174, 8], [172, 8, 176, 6], [173, 8, 177, 6], [173, 12, 177, 10], [174, 10, 178, 8], [174, 14, 178, 12], [174, 28, 178, 26], [174, 32, 178, 30, "window"], [174, 38, 178, 36], [174, 40, 178, 38], [175, 12, 179, 10, "console"], [175, 19, 179, 17], [175, 20, 179, 18, "log"], [175, 23, 179, 21], [175, 24, 179, 22], [175, 105, 179, 103], [175, 106, 179, 104], [176, 12, 180, 10], [176, 18, 180, 16, "faceDetector"], [176, 30, 180, 28], [176, 33, 180, 31], [176, 37, 180, 36, "window"], [176, 43, 180, 42], [176, 44, 180, 51, "FaceDetector"], [176, 56, 180, 63], [176, 57, 180, 64], [177, 14, 181, 12, "maxDetectedFaces"], [177, 30, 181, 28], [177, 32, 181, 30], [177, 34, 181, 32], [178, 14, 182, 12, "fastMode"], [178, 22, 182, 20], [178, 24, 182, 22], [179, 12, 183, 10], [179, 13, 183, 11], [179, 14, 183, 12], [180, 12, 185, 10], [180, 18, 185, 16, "browserDetections"], [180, 35, 185, 33], [180, 38, 185, 36], [180, 44, 185, 42, "faceDetector"], [180, 56, 185, 54], [180, 57, 185, 55, "detect"], [180, 63, 185, 61], [180, 64, 185, 62, "img"], [180, 67, 185, 65], [180, 68, 185, 66], [181, 12, 186, 10, "detectedFaces"], [181, 25, 186, 23], [181, 28, 186, 26, "browserDetections"], [181, 45, 186, 43], [181, 46, 186, 44, "map"], [181, 49, 186, 47], [181, 50, 186, 49, "detection"], [181, 59, 186, 63], [181, 64, 186, 69], [182, 14, 187, 12, "boundingBox"], [182, 25, 187, 23], [182, 27, 187, 25], [183, 16, 188, 14, "xCenter"], [183, 23, 188, 21], [183, 25, 188, 23], [183, 26, 188, 24, "detection"], [183, 35, 188, 33], [183, 36, 188, 34, "boundingBox"], [183, 47, 188, 45], [183, 48, 188, 46, "x"], [183, 49, 188, 47], [183, 52, 188, 50, "detection"], [183, 61, 188, 59], [183, 62, 188, 60, "boundingBox"], [183, 73, 188, 71], [183, 74, 188, 72, "width"], [183, 79, 188, 77], [183, 82, 188, 80], [183, 83, 188, 81], [183, 87, 188, 85, "img"], [183, 90, 188, 88], [183, 91, 188, 89, "width"], [183, 96, 188, 94], [184, 16, 189, 14, "yCenter"], [184, 23, 189, 21], [184, 25, 189, 23], [184, 26, 189, 24, "detection"], [184, 35, 189, 33], [184, 36, 189, 34, "boundingBox"], [184, 47, 189, 45], [184, 48, 189, 46, "y"], [184, 49, 189, 47], [184, 52, 189, 50, "detection"], [184, 61, 189, 59], [184, 62, 189, 60, "boundingBox"], [184, 73, 189, 71], [184, 74, 189, 72, "height"], [184, 80, 189, 78], [184, 83, 189, 81], [184, 84, 189, 82], [184, 88, 189, 86, "img"], [184, 91, 189, 89], [184, 92, 189, 90, "height"], [184, 98, 189, 96], [185, 16, 190, 14, "width"], [185, 21, 190, 19], [185, 23, 190, 21, "detection"], [185, 32, 190, 30], [185, 33, 190, 31, "boundingBox"], [185, 44, 190, 42], [185, 45, 190, 43, "width"], [185, 50, 190, 48], [185, 53, 190, 51, "img"], [185, 56, 190, 54], [185, 57, 190, 55, "width"], [185, 62, 190, 60], [186, 16, 191, 14, "height"], [186, 22, 191, 20], [186, 24, 191, 22, "detection"], [186, 33, 191, 31], [186, 34, 191, 32, "boundingBox"], [186, 45, 191, 43], [186, 46, 191, 44, "height"], [186, 52, 191, 50], [186, 55, 191, 53, "img"], [186, 58, 191, 56], [186, 59, 191, 57, "height"], [187, 14, 192, 12], [188, 12, 193, 10], [188, 13, 193, 11], [188, 14, 193, 12], [188, 15, 193, 13], [189, 12, 194, 10, "console"], [189, 19, 194, 17], [189, 20, 194, 18, "log"], [189, 23, 194, 21], [189, 24, 194, 22], [189, 78, 194, 76, "detectedFaces"], [189, 91, 194, 89], [189, 92, 194, 90, "length"], [189, 98, 194, 96], [189, 106, 194, 104], [189, 107, 194, 105], [190, 10, 195, 8], [190, 11, 195, 9], [190, 17, 195, 15], [191, 12, 196, 10, "console"], [191, 19, 196, 17], [191, 20, 196, 18, "log"], [191, 23, 196, 21], [191, 24, 196, 22], [191, 100, 196, 98], [191, 101, 196, 99], [192, 12, 197, 10], [192, 18, 197, 16], [192, 22, 197, 20, "Error"], [192, 27, 197, 25], [192, 28, 197, 26], [192, 70, 197, 68], [192, 71, 197, 69], [193, 10, 198, 8], [194, 8, 199, 6], [194, 9, 199, 7], [194, 10, 199, 8], [194, 17, 199, 15, "browserError"], [194, 29, 199, 27], [194, 31, 199, 29], [195, 10, 200, 8, "console"], [195, 17, 200, 15], [195, 18, 200, 16, "warn"], [195, 22, 200, 20], [195, 23, 200, 21], [195, 102, 200, 100], [195, 104, 200, 102, "browserError"], [195, 116, 200, 114], [195, 117, 200, 115], [197, 10, 202, 8], [198, 10, 203, 8], [198, 14, 203, 12], [199, 12, 204, 10], [200, 12, 205, 10], [200, 16, 205, 14], [200, 17, 205, 16, "window"], [200, 23, 205, 22], [200, 24, 205, 31, "<PERSON>ap<PERSON>"], [200, 31, 205, 38], [200, 33, 205, 40], [201, 14, 206, 12], [201, 20, 206, 18], [201, 24, 206, 22, "Promise"], [201, 31, 206, 29], [201, 32, 206, 30], [201, 33, 206, 31, "resolve"], [201, 40, 206, 38], [201, 42, 206, 40, "reject"], [201, 48, 206, 46], [201, 53, 206, 51], [202, 16, 207, 14], [202, 22, 207, 20, "script"], [202, 28, 207, 26], [202, 31, 207, 29, "document"], [202, 39, 207, 37], [202, 40, 207, 38, "createElement"], [202, 53, 207, 51], [202, 54, 207, 52], [202, 62, 207, 60], [202, 63, 207, 61], [203, 16, 208, 14, "script"], [203, 22, 208, 20], [203, 23, 208, 21, "src"], [203, 26, 208, 24], [203, 29, 208, 27], [203, 99, 208, 97], [204, 16, 209, 14, "script"], [204, 22, 209, 20], [204, 23, 209, 21, "onload"], [204, 29, 209, 27], [204, 32, 209, 30, "resolve"], [204, 39, 209, 37], [205, 16, 210, 14, "script"], [205, 22, 210, 20], [205, 23, 210, 21, "onerror"], [205, 30, 210, 28], [205, 33, 210, 31, "reject"], [205, 39, 210, 37], [206, 16, 211, 14, "document"], [206, 24, 211, 22], [206, 25, 211, 23, "head"], [206, 29, 211, 27], [206, 30, 211, 28, "append<PERSON><PERSON><PERSON>"], [206, 41, 211, 39], [206, 42, 211, 40, "script"], [206, 48, 211, 46], [206, 49, 211, 47], [207, 14, 212, 12], [207, 15, 212, 13], [207, 16, 212, 14], [208, 12, 213, 10], [209, 12, 215, 10], [209, 18, 215, 16, "<PERSON>ap<PERSON>"], [209, 25, 215, 23], [209, 28, 215, 27, "window"], [209, 34, 215, 33], [209, 35, 215, 42, "<PERSON>ap<PERSON>"], [209, 42, 215, 49], [211, 12, 217, 10], [212, 12, 218, 10], [212, 18, 218, 16, "Promise"], [212, 25, 218, 23], [212, 26, 218, 24, "all"], [212, 29, 218, 27], [212, 30, 218, 28], [212, 31, 219, 12, "<PERSON>ap<PERSON>"], [212, 38, 219, 19], [212, 39, 219, 20, "nets"], [212, 43, 219, 24], [212, 44, 219, 25, "tinyFaceDetector"], [212, 60, 219, 41], [212, 61, 219, 42, "loadFromUri"], [212, 72, 219, 53], [212, 73, 219, 54], [212, 130, 219, 111], [212, 131, 219, 112], [212, 133, 220, 12, "<PERSON>ap<PERSON>"], [212, 140, 220, 19], [212, 141, 220, 20, "nets"], [212, 145, 220, 24], [212, 146, 220, 25, "faceLandmark68Net"], [212, 163, 220, 42], [212, 164, 220, 43, "loadFromUri"], [212, 175, 220, 54], [212, 176, 220, 55], [212, 233, 220, 112], [212, 234, 220, 113], [212, 235, 221, 11], [212, 236, 221, 12], [214, 12, 223, 10], [215, 12, 224, 10], [215, 18, 224, 16, "faceDetections"], [215, 32, 224, 30], [215, 35, 224, 33], [215, 41, 224, 39, "<PERSON>ap<PERSON>"], [215, 48, 224, 46], [215, 49, 224, 47, "detectAllFaces"], [215, 63, 224, 61], [215, 64, 224, 62, "img"], [215, 67, 224, 65], [215, 69, 224, 67], [215, 73, 224, 71, "<PERSON>ap<PERSON>"], [215, 80, 224, 78], [215, 81, 224, 79, "TinyFaceDetectorOptions"], [215, 104, 224, 102], [215, 105, 224, 103], [215, 106, 224, 104], [215, 107, 224, 105], [216, 12, 226, 10, "detectedFaces"], [216, 25, 226, 23], [216, 28, 226, 26, "faceDetections"], [216, 42, 226, 40], [216, 43, 226, 41, "map"], [216, 46, 226, 44], [216, 47, 226, 46, "detection"], [216, 56, 226, 60], [216, 61, 226, 66], [217, 14, 227, 12, "boundingBox"], [217, 25, 227, 23], [217, 27, 227, 25], [218, 16, 228, 14, "xCenter"], [218, 23, 228, 21], [218, 25, 228, 23], [218, 26, 228, 24, "detection"], [218, 35, 228, 33], [218, 36, 228, 34, "box"], [218, 39, 228, 37], [218, 40, 228, 38, "x"], [218, 41, 228, 39], [218, 44, 228, 42, "detection"], [218, 53, 228, 51], [218, 54, 228, 52, "box"], [218, 57, 228, 55], [218, 58, 228, 56, "width"], [218, 63, 228, 61], [218, 66, 228, 64], [218, 67, 228, 65], [218, 71, 228, 69, "img"], [218, 74, 228, 72], [218, 75, 228, 73, "width"], [218, 80, 228, 78], [219, 16, 229, 14, "yCenter"], [219, 23, 229, 21], [219, 25, 229, 23], [219, 26, 229, 24, "detection"], [219, 35, 229, 33], [219, 36, 229, 34, "box"], [219, 39, 229, 37], [219, 40, 229, 38, "y"], [219, 41, 229, 39], [219, 44, 229, 42, "detection"], [219, 53, 229, 51], [219, 54, 229, 52, "box"], [219, 57, 229, 55], [219, 58, 229, 56, "height"], [219, 64, 229, 62], [219, 67, 229, 65], [219, 68, 229, 66], [219, 72, 229, 70, "img"], [219, 75, 229, 73], [219, 76, 229, 74, "height"], [219, 82, 229, 80], [220, 16, 230, 14, "width"], [220, 21, 230, 19], [220, 23, 230, 21, "detection"], [220, 32, 230, 30], [220, 33, 230, 31, "box"], [220, 36, 230, 34], [220, 37, 230, 35, "width"], [220, 42, 230, 40], [220, 45, 230, 43, "img"], [220, 48, 230, 46], [220, 49, 230, 47, "width"], [220, 54, 230, 52], [221, 16, 231, 14, "height"], [221, 22, 231, 20], [221, 24, 231, 22, "detection"], [221, 33, 231, 31], [221, 34, 231, 32, "box"], [221, 37, 231, 35], [221, 38, 231, 36, "height"], [221, 44, 231, 42], [221, 47, 231, 45, "img"], [221, 50, 231, 48], [221, 51, 231, 49, "height"], [222, 14, 232, 12], [223, 12, 233, 10], [223, 13, 233, 11], [223, 14, 233, 12], [223, 15, 233, 13], [224, 12, 235, 10, "console"], [224, 19, 235, 17], [224, 20, 235, 18, "log"], [224, 23, 235, 21], [224, 24, 235, 22], [224, 63, 235, 61, "detectedFaces"], [224, 76, 235, 74], [224, 77, 235, 75, "length"], [224, 83, 235, 81], [224, 91, 235, 89], [224, 92, 235, 90], [225, 10, 236, 8], [225, 11, 236, 9], [225, 12, 236, 10], [225, 19, 236, 17, "faceApiError"], [225, 31, 236, 29], [225, 33, 236, 31], [226, 12, 237, 10, "console"], [226, 19, 237, 17], [226, 20, 237, 18, "warn"], [226, 24, 237, 22], [226, 25, 237, 23], [226, 69, 237, 67], [226, 71, 237, 69, "faceApiError"], [226, 83, 237, 81], [226, 84, 237, 82], [228, 12, 239, 10], [229, 12, 240, 10, "console"], [229, 19, 240, 17], [229, 20, 240, 18, "log"], [229, 23, 240, 21], [229, 24, 240, 22], [229, 94, 240, 92], [229, 95, 240, 93], [230, 12, 241, 10, "detectedFaces"], [230, 25, 241, 23], [230, 28, 241, 26], [230, 29, 241, 27], [231, 14, 242, 12, "boundingBox"], [231, 25, 242, 23], [231, 27, 242, 25], [232, 16, 243, 14, "xCenter"], [232, 23, 243, 21], [232, 25, 243, 23], [232, 28, 243, 26], [233, 16, 243, 29], [234, 16, 244, 14, "yCenter"], [234, 23, 244, 21], [234, 25, 244, 23], [234, 28, 244, 26], [235, 16, 244, 29], [236, 16, 245, 14, "width"], [236, 21, 245, 19], [236, 23, 245, 21], [236, 26, 245, 24], [237, 16, 245, 29], [238, 16, 246, 14, "height"], [238, 22, 246, 20], [238, 24, 246, 22], [238, 27, 246, 25], [238, 28, 246, 29], [239, 14, 247, 12], [240, 12, 248, 10], [240, 13, 248, 11], [240, 14, 248, 12], [241, 12, 249, 10, "console"], [241, 19, 249, 17], [241, 20, 249, 18, "log"], [241, 23, 249, 21], [241, 24, 249, 22], [241, 93, 249, 91], [241, 94, 249, 92], [242, 10, 250, 8], [243, 8, 251, 6], [244, 8, 253, 6, "console"], [244, 15, 253, 13], [244, 16, 253, 14, "log"], [244, 19, 253, 17], [244, 20, 253, 18], [244, 72, 253, 70, "detectedFaces"], [244, 85, 253, 83], [244, 86, 253, 84, "length"], [244, 92, 253, 90], [244, 100, 253, 98], [244, 101, 253, 99], [245, 8, 254, 6], [245, 12, 254, 10, "detectedFaces"], [245, 25, 254, 23], [245, 26, 254, 24, "length"], [245, 32, 254, 30], [245, 35, 254, 33], [245, 36, 254, 34], [245, 38, 254, 36], [246, 10, 255, 8, "console"], [246, 17, 255, 15], [246, 18, 255, 16, "log"], [246, 21, 255, 19], [246, 22, 255, 20], [246, 66, 255, 64], [246, 68, 255, 66, "detectedFaces"], [246, 81, 255, 79], [246, 82, 255, 80, "map"], [246, 85, 255, 83], [246, 86, 255, 84], [246, 87, 255, 85, "face"], [246, 91, 255, 89], [246, 93, 255, 91, "i"], [246, 94, 255, 92], [246, 100, 255, 98], [247, 12, 256, 10, "faceNumber"], [247, 22, 256, 20], [247, 24, 256, 22, "i"], [247, 25, 256, 23], [247, 28, 256, 26], [247, 29, 256, 27], [248, 12, 257, 10, "centerX"], [248, 19, 257, 17], [248, 21, 257, 19, "face"], [248, 25, 257, 23], [248, 26, 257, 24, "boundingBox"], [248, 37, 257, 35], [248, 38, 257, 36, "xCenter"], [248, 45, 257, 43], [249, 12, 258, 10, "centerY"], [249, 19, 258, 17], [249, 21, 258, 19, "face"], [249, 25, 258, 23], [249, 26, 258, 24, "boundingBox"], [249, 37, 258, 35], [249, 38, 258, 36, "yCenter"], [249, 45, 258, 43], [250, 12, 259, 10, "width"], [250, 17, 259, 15], [250, 19, 259, 17, "face"], [250, 23, 259, 21], [250, 24, 259, 22, "boundingBox"], [250, 35, 259, 33], [250, 36, 259, 34, "width"], [250, 41, 259, 39], [251, 12, 260, 10, "height"], [251, 18, 260, 16], [251, 20, 260, 18, "face"], [251, 24, 260, 22], [251, 25, 260, 23, "boundingBox"], [251, 36, 260, 34], [251, 37, 260, 35, "height"], [252, 10, 261, 8], [252, 11, 261, 9], [252, 12, 261, 10], [252, 13, 261, 11], [252, 14, 261, 12], [253, 8, 262, 6], [253, 9, 262, 7], [253, 15, 262, 13], [254, 10, 263, 8, "console"], [254, 17, 263, 15], [254, 18, 263, 16, "log"], [254, 21, 263, 19], [254, 22, 263, 20], [254, 91, 263, 89], [254, 92, 263, 90], [255, 8, 264, 6], [256, 8, 266, 6, "setProcessingProgress"], [256, 29, 266, 27], [256, 30, 266, 28], [256, 32, 266, 30], [256, 33, 266, 31], [258, 8, 268, 6], [259, 8, 269, 6], [259, 12, 269, 10, "detectedFaces"], [259, 25, 269, 23], [259, 26, 269, 24, "length"], [259, 32, 269, 30], [259, 35, 269, 33], [259, 36, 269, 34], [259, 38, 269, 36], [260, 10, 270, 8, "detectedFaces"], [260, 23, 270, 21], [260, 24, 270, 22, "for<PERSON>ach"], [260, 31, 270, 29], [260, 32, 270, 30], [260, 33, 270, 31, "detection"], [260, 42, 270, 40], [260, 44, 270, 42, "index"], [260, 49, 270, 47], [260, 54, 270, 52], [261, 12, 271, 10], [261, 18, 271, 16, "bbox"], [261, 22, 271, 20], [261, 25, 271, 23, "detection"], [261, 34, 271, 32], [261, 35, 271, 33, "boundingBox"], [261, 46, 271, 44], [263, 12, 273, 10], [264, 12, 274, 10], [264, 18, 274, 16, "faceX"], [264, 23, 274, 21], [264, 26, 274, 24, "bbox"], [264, 30, 274, 28], [264, 31, 274, 29, "xCenter"], [264, 38, 274, 36], [264, 41, 274, 39, "img"], [264, 44, 274, 42], [264, 45, 274, 43, "width"], [264, 50, 274, 48], [264, 53, 274, 52, "bbox"], [264, 57, 274, 56], [264, 58, 274, 57, "width"], [264, 63, 274, 62], [264, 66, 274, 65, "img"], [264, 69, 274, 68], [264, 70, 274, 69, "width"], [264, 75, 274, 74], [264, 78, 274, 78], [264, 79, 274, 79], [265, 12, 275, 10], [265, 18, 275, 16, "faceY"], [265, 23, 275, 21], [265, 26, 275, 24, "bbox"], [265, 30, 275, 28], [265, 31, 275, 29, "yCenter"], [265, 38, 275, 36], [265, 41, 275, 39, "img"], [265, 44, 275, 42], [265, 45, 275, 43, "height"], [265, 51, 275, 49], [265, 54, 275, 53, "bbox"], [265, 58, 275, 57], [265, 59, 275, 58, "height"], [265, 65, 275, 64], [265, 68, 275, 67, "img"], [265, 71, 275, 70], [265, 72, 275, 71, "height"], [265, 78, 275, 77], [265, 81, 275, 81], [265, 82, 275, 82], [266, 12, 276, 10], [266, 18, 276, 16, "faceWidth"], [266, 27, 276, 25], [266, 30, 276, 28, "bbox"], [266, 34, 276, 32], [266, 35, 276, 33, "width"], [266, 40, 276, 38], [266, 43, 276, 41, "img"], [266, 46, 276, 44], [266, 47, 276, 45, "width"], [266, 52, 276, 50], [267, 12, 277, 10], [267, 18, 277, 16, "faceHeight"], [267, 28, 277, 26], [267, 31, 277, 29, "bbox"], [267, 35, 277, 33], [267, 36, 277, 34, "height"], [267, 42, 277, 40], [267, 45, 277, 43, "img"], [267, 48, 277, 46], [267, 49, 277, 47, "height"], [267, 55, 277, 53], [269, 12, 279, 10], [270, 12, 280, 10], [270, 18, 280, 16, "padding"], [270, 25, 280, 23], [270, 28, 280, 26], [270, 31, 280, 29], [270, 32, 280, 30], [270, 33, 280, 31], [271, 12, 281, 10], [271, 18, 281, 16, "paddedX"], [271, 25, 281, 23], [271, 28, 281, 26, "Math"], [271, 32, 281, 30], [271, 33, 281, 31, "max"], [271, 36, 281, 34], [271, 37, 281, 35], [271, 38, 281, 36], [271, 40, 281, 38, "faceX"], [271, 45, 281, 43], [271, 48, 281, 46, "faceWidth"], [271, 57, 281, 55], [271, 60, 281, 58, "padding"], [271, 67, 281, 65], [271, 68, 281, 66], [272, 12, 282, 10], [272, 18, 282, 16, "paddedY"], [272, 25, 282, 23], [272, 28, 282, 26, "Math"], [272, 32, 282, 30], [272, 33, 282, 31, "max"], [272, 36, 282, 34], [272, 37, 282, 35], [272, 38, 282, 36], [272, 40, 282, 38, "faceY"], [272, 45, 282, 43], [272, 48, 282, 46, "faceHeight"], [272, 58, 282, 56], [272, 61, 282, 59, "padding"], [272, 68, 282, 66], [272, 69, 282, 67], [273, 12, 283, 10], [273, 18, 283, 16, "<PERSON><PERSON><PERSON><PERSON>"], [273, 29, 283, 27], [273, 32, 283, 30, "Math"], [273, 36, 283, 34], [273, 37, 283, 35, "min"], [273, 40, 283, 38], [273, 41, 283, 39, "img"], [273, 44, 283, 42], [273, 45, 283, 43, "width"], [273, 50, 283, 48], [273, 53, 283, 51, "paddedX"], [273, 60, 283, 58], [273, 62, 283, 60, "faceWidth"], [273, 71, 283, 69], [273, 75, 283, 73], [273, 76, 283, 74], [273, 79, 283, 77], [273, 80, 283, 78], [273, 83, 283, 81, "padding"], [273, 90, 283, 88], [273, 91, 283, 89], [273, 92, 283, 90], [274, 12, 284, 10], [274, 18, 284, 16, "paddedHeight"], [274, 30, 284, 28], [274, 33, 284, 31, "Math"], [274, 37, 284, 35], [274, 38, 284, 36, "min"], [274, 41, 284, 39], [274, 42, 284, 40, "img"], [274, 45, 284, 43], [274, 46, 284, 44, "height"], [274, 52, 284, 50], [274, 55, 284, 53, "paddedY"], [274, 62, 284, 60], [274, 64, 284, 62, "faceHeight"], [274, 74, 284, 72], [274, 78, 284, 76], [274, 79, 284, 77], [274, 82, 284, 80], [274, 83, 284, 81], [274, 86, 284, 84, "padding"], [274, 93, 284, 91], [274, 94, 284, 92], [274, 95, 284, 93], [275, 12, 286, 10, "console"], [275, 19, 286, 17], [275, 20, 286, 18, "log"], [275, 23, 286, 21], [275, 24, 286, 22], [275, 57, 286, 55, "index"], [275, 62, 286, 60], [275, 65, 286, 63], [275, 66, 286, 64], [275, 74, 286, 72, "paddedX"], [275, 81, 286, 79], [275, 86, 286, 84, "paddedY"], [275, 93, 286, 91], [275, 103, 286, 101, "<PERSON><PERSON><PERSON><PERSON>"], [275, 114, 286, 112], [275, 118, 286, 116, "paddedHeight"], [275, 130, 286, 128], [275, 132, 286, 130], [275, 133, 286, 131], [277, 12, 288, 10], [278, 12, 289, 10], [278, 18, 289, 16, "faceImageData"], [278, 31, 289, 29], [278, 34, 289, 32, "ctx"], [278, 37, 289, 35], [278, 38, 289, 36, "getImageData"], [278, 50, 289, 48], [278, 51, 289, 49, "paddedX"], [278, 58, 289, 56], [278, 60, 289, 58, "paddedY"], [278, 67, 289, 65], [278, 69, 289, 67, "<PERSON><PERSON><PERSON><PERSON>"], [278, 80, 289, 78], [278, 82, 289, 80, "paddedHeight"], [278, 94, 289, 92], [278, 95, 289, 93], [279, 12, 290, 10], [279, 18, 290, 16, "data"], [279, 22, 290, 20], [279, 25, 290, 23, "faceImageData"], [279, 38, 290, 36], [279, 39, 290, 37, "data"], [279, 43, 290, 41], [281, 12, 292, 10], [282, 12, 293, 10], [282, 18, 293, 16, "pixelSize"], [282, 27, 293, 25], [282, 30, 293, 28, "Math"], [282, 34, 293, 32], [282, 35, 293, 33, "max"], [282, 38, 293, 36], [282, 39, 293, 37], [282, 40, 293, 38], [282, 42, 293, 40, "Math"], [282, 46, 293, 44], [282, 47, 293, 45, "min"], [282, 50, 293, 48], [282, 51, 293, 49, "<PERSON><PERSON><PERSON><PERSON>"], [282, 62, 293, 60], [282, 64, 293, 62, "paddedHeight"], [282, 76, 293, 74], [282, 77, 293, 75], [282, 80, 293, 78], [282, 82, 293, 80], [282, 83, 293, 81], [282, 84, 293, 82], [282, 85, 293, 83], [283, 12, 294, 10], [283, 17, 294, 15], [283, 21, 294, 19, "y"], [283, 22, 294, 20], [283, 25, 294, 23], [283, 26, 294, 24], [283, 28, 294, 26, "y"], [283, 29, 294, 27], [283, 32, 294, 30, "paddedHeight"], [283, 44, 294, 42], [283, 46, 294, 44, "y"], [283, 47, 294, 45], [283, 51, 294, 49, "pixelSize"], [283, 60, 294, 58], [283, 62, 294, 60], [284, 14, 295, 12], [284, 19, 295, 17], [284, 23, 295, 21, "x"], [284, 24, 295, 22], [284, 27, 295, 25], [284, 28, 295, 26], [284, 30, 295, 28, "x"], [284, 31, 295, 29], [284, 34, 295, 32, "<PERSON><PERSON><PERSON><PERSON>"], [284, 45, 295, 43], [284, 47, 295, 45, "x"], [284, 48, 295, 46], [284, 52, 295, 50, "pixelSize"], [284, 61, 295, 59], [284, 63, 295, 61], [285, 16, 296, 14], [286, 16, 297, 14], [286, 22, 297, 20, "pixelIndex"], [286, 32, 297, 30], [286, 35, 297, 33], [286, 36, 297, 34, "y"], [286, 37, 297, 35], [286, 40, 297, 38, "<PERSON><PERSON><PERSON><PERSON>"], [286, 51, 297, 49], [286, 54, 297, 52, "x"], [286, 55, 297, 53], [286, 59, 297, 57], [286, 60, 297, 58], [287, 16, 298, 14], [287, 22, 298, 20, "r"], [287, 23, 298, 21], [287, 26, 298, 24, "data"], [287, 30, 298, 28], [287, 31, 298, 29, "pixelIndex"], [287, 41, 298, 39], [287, 42, 298, 40], [288, 16, 299, 14], [288, 22, 299, 20, "g"], [288, 23, 299, 21], [288, 26, 299, 24, "data"], [288, 30, 299, 28], [288, 31, 299, 29, "pixelIndex"], [288, 41, 299, 39], [288, 44, 299, 42], [288, 45, 299, 43], [288, 46, 299, 44], [289, 16, 300, 14], [289, 22, 300, 20, "b"], [289, 23, 300, 21], [289, 26, 300, 24, "data"], [289, 30, 300, 28], [289, 31, 300, 29, "pixelIndex"], [289, 41, 300, 39], [289, 44, 300, 42], [289, 45, 300, 43], [289, 46, 300, 44], [290, 16, 301, 14], [290, 22, 301, 20, "a"], [290, 23, 301, 21], [290, 26, 301, 24, "data"], [290, 30, 301, 28], [290, 31, 301, 29, "pixelIndex"], [290, 41, 301, 39], [290, 44, 301, 42], [290, 45, 301, 43], [290, 46, 301, 44], [292, 16, 303, 14], [293, 16, 304, 14], [293, 21, 304, 19], [293, 25, 304, 23, "dy"], [293, 27, 304, 25], [293, 30, 304, 28], [293, 31, 304, 29], [293, 33, 304, 31, "dy"], [293, 35, 304, 33], [293, 38, 304, 36, "pixelSize"], [293, 47, 304, 45], [293, 51, 304, 49, "y"], [293, 52, 304, 50], [293, 55, 304, 53, "dy"], [293, 57, 304, 55], [293, 60, 304, 58, "paddedHeight"], [293, 72, 304, 70], [293, 74, 304, 72, "dy"], [293, 76, 304, 74], [293, 78, 304, 76], [293, 80, 304, 78], [294, 18, 305, 16], [294, 23, 305, 21], [294, 27, 305, 25, "dx"], [294, 29, 305, 27], [294, 32, 305, 30], [294, 33, 305, 31], [294, 35, 305, 33, "dx"], [294, 37, 305, 35], [294, 40, 305, 38, "pixelSize"], [294, 49, 305, 47], [294, 53, 305, 51, "x"], [294, 54, 305, 52], [294, 57, 305, 55, "dx"], [294, 59, 305, 57], [294, 62, 305, 60, "<PERSON><PERSON><PERSON><PERSON>"], [294, 73, 305, 71], [294, 75, 305, 73, "dx"], [294, 77, 305, 75], [294, 79, 305, 77], [294, 81, 305, 79], [295, 20, 306, 18], [295, 26, 306, 24, "blockPixelIndex"], [295, 41, 306, 39], [295, 44, 306, 42], [295, 45, 306, 43], [295, 46, 306, 44, "y"], [295, 47, 306, 45], [295, 50, 306, 48, "dy"], [295, 52, 306, 50], [295, 56, 306, 54, "<PERSON><PERSON><PERSON><PERSON>"], [295, 67, 306, 65], [295, 71, 306, 69, "x"], [295, 72, 306, 70], [295, 75, 306, 73, "dx"], [295, 77, 306, 75], [295, 78, 306, 76], [295, 82, 306, 80], [295, 83, 306, 81], [296, 20, 307, 18, "data"], [296, 24, 307, 22], [296, 25, 307, 23, "blockPixelIndex"], [296, 40, 307, 38], [296, 41, 307, 39], [296, 44, 307, 42, "r"], [296, 45, 307, 43], [297, 20, 308, 18, "data"], [297, 24, 308, 22], [297, 25, 308, 23, "blockPixelIndex"], [297, 40, 308, 38], [297, 43, 308, 41], [297, 44, 308, 42], [297, 45, 308, 43], [297, 48, 308, 46, "g"], [297, 49, 308, 47], [298, 20, 309, 18, "data"], [298, 24, 309, 22], [298, 25, 309, 23, "blockPixelIndex"], [298, 40, 309, 38], [298, 43, 309, 41], [298, 44, 309, 42], [298, 45, 309, 43], [298, 48, 309, 46, "b"], [298, 49, 309, 47], [299, 20, 310, 18, "data"], [299, 24, 310, 22], [299, 25, 310, 23, "blockPixelIndex"], [299, 40, 310, 38], [299, 43, 310, 41], [299, 44, 310, 42], [299, 45, 310, 43], [299, 48, 310, 46, "a"], [299, 49, 310, 47], [300, 18, 311, 16], [301, 16, 312, 14], [302, 14, 313, 12], [303, 12, 314, 10], [305, 12, 316, 10], [306, 12, 317, 10, "ctx"], [306, 15, 317, 13], [306, 16, 317, 14, "putImageData"], [306, 28, 317, 26], [306, 29, 317, 27, "faceImageData"], [306, 42, 317, 40], [306, 44, 317, 42, "paddedX"], [306, 51, 317, 49], [306, 53, 317, 51, "paddedY"], [306, 60, 317, 58], [306, 61, 317, 59], [307, 10, 318, 8], [307, 11, 318, 9], [307, 12, 318, 10], [308, 8, 319, 6], [308, 9, 319, 7], [308, 15, 319, 13], [309, 10, 320, 8, "console"], [309, 17, 320, 15], [309, 18, 320, 16, "log"], [309, 21, 320, 19], [309, 22, 320, 20], [309, 88, 320, 86], [309, 89, 320, 87], [310, 8, 321, 6], [311, 8, 323, 6, "setProcessingProgress"], [311, 29, 323, 27], [311, 30, 323, 28], [311, 32, 323, 30], [311, 33, 323, 31], [313, 8, 325, 6], [314, 8, 326, 6], [314, 14, 326, 12, "blurredImageBlob"], [314, 30, 326, 28], [314, 33, 326, 31], [314, 39, 326, 37], [314, 43, 326, 41, "Promise"], [314, 50, 326, 48], [314, 51, 326, 56, "resolve"], [314, 58, 326, 63], [314, 62, 326, 68], [315, 10, 327, 8, "canvas"], [315, 16, 327, 14], [315, 17, 327, 15, "toBlob"], [315, 23, 327, 21], [315, 24, 327, 23, "blob"], [315, 28, 327, 27], [315, 32, 327, 32, "resolve"], [315, 39, 327, 39], [315, 40, 327, 40, "blob"], [315, 44, 327, 45], [315, 45, 327, 46], [315, 47, 327, 48], [315, 59, 327, 60], [315, 61, 327, 62], [315, 64, 327, 65], [315, 65, 327, 66], [316, 8, 328, 6], [316, 9, 328, 7], [316, 10, 328, 8], [317, 8, 330, 6], [317, 14, 330, 12, "blurredImageUrl"], [317, 29, 330, 27], [317, 32, 330, 30, "URL"], [317, 35, 330, 33], [317, 36, 330, 34, "createObjectURL"], [317, 51, 330, 49], [317, 52, 330, 50, "blurredImageBlob"], [317, 68, 330, 66], [317, 69, 330, 67], [318, 8, 332, 6, "setProcessingProgress"], [318, 29, 332, 27], [318, 30, 332, 28], [318, 33, 332, 31], [318, 34, 332, 32], [320, 8, 334, 6], [321, 8, 335, 6], [321, 14, 335, 12, "completeProcessing"], [321, 32, 335, 30], [321, 33, 335, 31, "blurredImageUrl"], [321, 48, 335, 46], [321, 49, 335, 47], [322, 6, 337, 4], [322, 7, 337, 5], [322, 8, 337, 6], [322, 15, 337, 13, "error"], [322, 20, 337, 18], [322, 22, 337, 20], [323, 8, 338, 6, "console"], [323, 15, 338, 13], [323, 16, 338, 14, "error"], [323, 21, 338, 19], [323, 22, 338, 20], [323, 57, 338, 55], [323, 59, 338, 57, "error"], [323, 64, 338, 62], [323, 65, 338, 63], [324, 8, 339, 6, "setErrorMessage"], [324, 23, 339, 21], [324, 24, 339, 22], [324, 50, 339, 48], [324, 51, 339, 49], [325, 8, 340, 6, "setProcessingState"], [325, 26, 340, 24], [325, 27, 340, 25], [325, 34, 340, 32], [325, 35, 340, 33], [326, 6, 341, 4], [327, 4, 342, 2], [327, 5, 342, 3], [329, 4, 344, 2], [330, 4, 345, 2], [330, 10, 345, 8, "completeProcessing"], [330, 28, 345, 26], [330, 31, 345, 29], [330, 37, 345, 36, "blurredImageUrl"], [330, 52, 345, 59], [330, 56, 345, 64], [331, 6, 346, 4], [331, 10, 346, 8], [332, 8, 347, 6, "setProcessingState"], [332, 26, 347, 24], [332, 27, 347, 25], [332, 37, 347, 35], [332, 38, 347, 36], [334, 8, 349, 6], [335, 8, 350, 6], [335, 14, 350, 12, "timestamp"], [335, 23, 350, 21], [335, 26, 350, 24, "Date"], [335, 30, 350, 28], [335, 31, 350, 29, "now"], [335, 34, 350, 32], [335, 35, 350, 33], [335, 36, 350, 34], [336, 8, 351, 6], [336, 14, 351, 12, "result"], [336, 20, 351, 18], [336, 23, 351, 21], [337, 10, 352, 8, "imageUrl"], [337, 18, 352, 16], [337, 20, 352, 18, "blurredImageUrl"], [337, 35, 352, 33], [338, 10, 353, 8, "localUri"], [338, 18, 353, 16], [338, 20, 353, 18, "blurredImageUrl"], [338, 35, 353, 33], [339, 10, 354, 8, "challengeCode"], [339, 23, 354, 21], [339, 25, 354, 23, "challengeCode"], [339, 38, 354, 36], [339, 42, 354, 40], [339, 44, 354, 42], [340, 10, 355, 8, "timestamp"], [340, 19, 355, 17], [341, 10, 356, 8, "jobId"], [341, 15, 356, 13], [341, 17, 356, 15], [341, 27, 356, 25, "timestamp"], [341, 36, 356, 34], [341, 38, 356, 36], [342, 10, 357, 8, "status"], [342, 16, 357, 14], [342, 18, 357, 16], [343, 8, 358, 6], [343, 9, 358, 7], [344, 8, 360, 6, "console"], [344, 15, 360, 13], [344, 16, 360, 14, "log"], [344, 19, 360, 17], [344, 20, 360, 18], [344, 58, 360, 56], [344, 60, 360, 58, "result"], [344, 66, 360, 64], [344, 67, 360, 65], [346, 8, 362, 6], [347, 8, 363, 6, "onComplete"], [347, 18, 363, 16], [347, 19, 363, 17, "result"], [347, 25, 363, 23], [347, 26, 363, 24], [348, 6, 365, 4], [348, 7, 365, 5], [348, 8, 365, 6], [348, 15, 365, 13, "error"], [348, 20, 365, 18], [348, 22, 365, 20], [349, 8, 366, 6, "console"], [349, 15, 366, 13], [349, 16, 366, 14, "error"], [349, 21, 366, 19], [349, 22, 366, 20], [349, 57, 366, 55], [349, 59, 366, 57, "error"], [349, 64, 366, 62], [349, 65, 366, 63], [350, 8, 367, 6, "setErrorMessage"], [350, 23, 367, 21], [350, 24, 367, 22], [350, 56, 367, 54], [350, 57, 367, 55], [351, 8, 368, 6, "setProcessingState"], [351, 26, 368, 24], [351, 27, 368, 25], [351, 34, 368, 32], [351, 35, 368, 33], [352, 6, 369, 4], [353, 4, 370, 2], [353, 5, 370, 3], [355, 4, 372, 2], [356, 4, 373, 2], [356, 10, 373, 8, "triggerServerProcessing"], [356, 33, 373, 31], [356, 36, 373, 34], [356, 42, 373, 34, "triggerServerProcessing"], [356, 43, 373, 41, "privateImageUrl"], [356, 58, 373, 64], [356, 60, 373, 66, "timestamp"], [356, 69, 373, 83], [356, 74, 373, 88], [357, 6, 374, 4], [357, 10, 374, 8], [358, 8, 375, 6, "console"], [358, 15, 375, 13], [358, 16, 375, 14, "log"], [358, 19, 375, 17], [358, 20, 375, 18], [358, 74, 375, 72], [358, 76, 375, 74, "privateImageUrl"], [358, 91, 375, 89], [358, 92, 375, 90], [359, 8, 376, 6, "setProcessingState"], [359, 26, 376, 24], [359, 27, 376, 25], [359, 39, 376, 37], [359, 40, 376, 38], [360, 8, 377, 6, "setProcessingProgress"], [360, 29, 377, 27], [360, 30, 377, 28], [360, 32, 377, 30], [360, 33, 377, 31], [361, 8, 379, 6], [361, 14, 379, 12, "requestBody"], [361, 25, 379, 23], [361, 28, 379, 26], [362, 10, 380, 8, "imageUrl"], [362, 18, 380, 16], [362, 20, 380, 18, "privateImageUrl"], [362, 35, 380, 33], [363, 10, 381, 8, "userId"], [363, 16, 381, 14], [364, 10, 382, 8, "requestId"], [364, 19, 382, 17], [365, 10, 383, 8, "timestamp"], [365, 19, 383, 17], [366, 10, 384, 8, "platform"], [366, 18, 384, 16], [366, 20, 384, 18], [367, 8, 385, 6], [367, 9, 385, 7], [368, 8, 387, 6, "console"], [368, 15, 387, 13], [368, 16, 387, 14, "log"], [368, 19, 387, 17], [368, 20, 387, 18], [368, 65, 387, 63], [368, 67, 387, 65, "requestBody"], [368, 78, 387, 76], [368, 79, 387, 77], [370, 8, 389, 6], [371, 8, 390, 6], [371, 14, 390, 12, "response"], [371, 22, 390, 20], [371, 25, 390, 23], [371, 31, 390, 29, "fetch"], [371, 36, 390, 34], [371, 37, 390, 35], [371, 40, 390, 38, "API_BASE_URL"], [371, 52, 390, 50], [371, 72, 390, 70], [371, 74, 390, 72], [372, 10, 391, 8, "method"], [372, 16, 391, 14], [372, 18, 391, 16], [372, 24, 391, 22], [373, 10, 392, 8, "headers"], [373, 17, 392, 15], [373, 19, 392, 17], [374, 12, 393, 10], [374, 26, 393, 24], [374, 28, 393, 26], [374, 46, 393, 44], [375, 12, 394, 10], [375, 27, 394, 25], [375, 29, 394, 27], [375, 39, 394, 37], [375, 45, 394, 43, "getAuthToken"], [375, 57, 394, 55], [375, 58, 394, 56], [375, 59, 394, 57], [376, 10, 395, 8], [376, 11, 395, 9], [377, 10, 396, 8, "body"], [377, 14, 396, 12], [377, 16, 396, 14, "JSON"], [377, 20, 396, 18], [377, 21, 396, 19, "stringify"], [377, 30, 396, 28], [377, 31, 396, 29, "requestBody"], [377, 42, 396, 40], [378, 8, 397, 6], [378, 9, 397, 7], [378, 10, 397, 8], [379, 8, 399, 6], [379, 12, 399, 10], [379, 13, 399, 11, "response"], [379, 21, 399, 19], [379, 22, 399, 20, "ok"], [379, 24, 399, 22], [379, 26, 399, 24], [380, 10, 400, 8], [380, 16, 400, 14, "errorText"], [380, 25, 400, 23], [380, 28, 400, 26], [380, 34, 400, 32, "response"], [380, 42, 400, 40], [380, 43, 400, 41, "text"], [380, 47, 400, 45], [380, 48, 400, 46], [380, 49, 400, 47], [381, 10, 401, 8, "console"], [381, 17, 401, 15], [381, 18, 401, 16, "error"], [381, 23, 401, 21], [381, 24, 401, 22], [381, 68, 401, 66], [381, 70, 401, 68, "response"], [381, 78, 401, 76], [381, 79, 401, 77, "status"], [381, 85, 401, 83], [381, 87, 401, 85, "errorText"], [381, 96, 401, 94], [381, 97, 401, 95], [382, 10, 402, 8], [382, 16, 402, 14], [382, 20, 402, 18, "Error"], [382, 25, 402, 23], [382, 26, 402, 24], [382, 48, 402, 46, "response"], [382, 56, 402, 54], [382, 57, 402, 55, "status"], [382, 63, 402, 61], [382, 67, 402, 65, "response"], [382, 75, 402, 73], [382, 76, 402, 74, "statusText"], [382, 86, 402, 84], [382, 88, 402, 86], [382, 89, 402, 87], [383, 8, 403, 6], [384, 8, 405, 6], [384, 14, 405, 12, "result"], [384, 20, 405, 18], [384, 23, 405, 21], [384, 29, 405, 27, "response"], [384, 37, 405, 35], [384, 38, 405, 36, "json"], [384, 42, 405, 40], [384, 43, 405, 41], [384, 44, 405, 42], [385, 8, 406, 6, "console"], [385, 15, 406, 13], [385, 16, 406, 14, "log"], [385, 19, 406, 17], [385, 20, 406, 18], [385, 68, 406, 66], [385, 70, 406, 68, "result"], [385, 76, 406, 74], [385, 77, 406, 75], [386, 8, 408, 6], [386, 12, 408, 10], [386, 13, 408, 11, "result"], [386, 19, 408, 17], [386, 20, 408, 18, "jobId"], [386, 25, 408, 23], [386, 27, 408, 25], [387, 10, 409, 8], [387, 16, 409, 14], [387, 20, 409, 18, "Error"], [387, 25, 409, 23], [387, 26, 409, 24], [387, 70, 409, 68], [387, 71, 409, 69], [388, 8, 410, 6], [390, 8, 412, 6], [391, 8, 413, 6], [391, 14, 413, 12, "pollForCompletion"], [391, 31, 413, 29], [391, 32, 413, 30, "result"], [391, 38, 413, 36], [391, 39, 413, 37, "jobId"], [391, 44, 413, 42], [391, 46, 413, 44, "timestamp"], [391, 55, 413, 53], [391, 56, 413, 54], [392, 6, 414, 4], [392, 7, 414, 5], [392, 8, 414, 6], [392, 15, 414, 13, "error"], [392, 20, 414, 18], [392, 22, 414, 20], [393, 8, 415, 6, "console"], [393, 15, 415, 13], [393, 16, 415, 14, "error"], [393, 21, 415, 19], [393, 22, 415, 20], [393, 57, 415, 55], [393, 59, 415, 57, "error"], [393, 64, 415, 62], [393, 65, 415, 63], [394, 8, 416, 6, "setErrorMessage"], [394, 23, 416, 21], [394, 24, 416, 22], [394, 52, 416, 50, "error"], [394, 57, 416, 55], [394, 58, 416, 56, "message"], [394, 65, 416, 63], [394, 67, 416, 65], [394, 68, 416, 66], [395, 8, 417, 6, "setProcessingState"], [395, 26, 417, 24], [395, 27, 417, 25], [395, 34, 417, 32], [395, 35, 417, 33], [396, 6, 418, 4], [397, 4, 419, 2], [397, 5, 419, 3], [398, 4, 420, 2], [399, 4, 421, 2], [399, 10, 421, 8, "pollForCompletion"], [399, 27, 421, 25], [399, 30, 421, 28], [399, 36, 421, 28, "pollForCompletion"], [399, 37, 421, 35, "jobId"], [399, 42, 421, 48], [399, 44, 421, 50, "timestamp"], [399, 53, 421, 67], [399, 55, 421, 69, "attempts"], [399, 63, 421, 77], [399, 66, 421, 80], [399, 67, 421, 81], [399, 72, 421, 86], [400, 6, 422, 4], [400, 12, 422, 10, "MAX_ATTEMPTS"], [400, 24, 422, 22], [400, 27, 422, 25], [400, 29, 422, 27], [400, 30, 422, 28], [400, 31, 422, 29], [401, 6, 423, 4], [401, 12, 423, 10, "POLL_INTERVAL"], [401, 25, 423, 23], [401, 28, 423, 26], [401, 32, 423, 30], [401, 33, 423, 31], [401, 34, 423, 32], [403, 6, 425, 4, "console"], [403, 13, 425, 11], [403, 14, 425, 12, "log"], [403, 17, 425, 15], [403, 18, 425, 16], [403, 53, 425, 51, "attempts"], [403, 61, 425, 59], [403, 64, 425, 62], [403, 65, 425, 63], [403, 69, 425, 67, "MAX_ATTEMPTS"], [403, 81, 425, 79], [403, 93, 425, 91, "jobId"], [403, 98, 425, 96], [403, 100, 425, 98], [403, 101, 425, 99], [404, 6, 427, 4], [404, 10, 427, 8, "attempts"], [404, 18, 427, 16], [404, 22, 427, 20, "MAX_ATTEMPTS"], [404, 34, 427, 32], [404, 36, 427, 34], [405, 8, 428, 6, "console"], [405, 15, 428, 13], [405, 16, 428, 14, "error"], [405, 21, 428, 19], [405, 22, 428, 20], [405, 75, 428, 73], [405, 76, 428, 74], [406, 8, 429, 6, "setErrorMessage"], [406, 23, 429, 21], [406, 24, 429, 22], [406, 63, 429, 61], [406, 64, 429, 62], [407, 8, 430, 6, "setProcessingState"], [407, 26, 430, 24], [407, 27, 430, 25], [407, 34, 430, 32], [407, 35, 430, 33], [408, 8, 431, 6], [409, 6, 432, 4], [410, 6, 434, 4], [410, 10, 434, 8], [411, 8, 435, 6], [411, 14, 435, 12, "response"], [411, 22, 435, 20], [411, 25, 435, 23], [411, 31, 435, 29, "fetch"], [411, 36, 435, 34], [411, 37, 435, 35], [411, 40, 435, 38, "API_BASE_URL"], [411, 52, 435, 50], [411, 75, 435, 73, "jobId"], [411, 80, 435, 78], [411, 82, 435, 80], [411, 84, 435, 82], [412, 10, 436, 8, "headers"], [412, 17, 436, 15], [412, 19, 436, 17], [413, 12, 437, 10], [413, 27, 437, 25], [413, 29, 437, 27], [413, 39, 437, 37], [413, 45, 437, 43, "getAuthToken"], [413, 57, 437, 55], [413, 58, 437, 56], [413, 59, 437, 57], [414, 10, 438, 8], [415, 8, 439, 6], [415, 9, 439, 7], [415, 10, 439, 8], [416, 8, 441, 6], [416, 12, 441, 10], [416, 13, 441, 11, "response"], [416, 21, 441, 19], [416, 22, 441, 20, "ok"], [416, 24, 441, 22], [416, 26, 441, 24], [417, 10, 442, 8], [417, 16, 442, 14], [417, 20, 442, 18, "Error"], [417, 25, 442, 23], [417, 26, 442, 24], [417, 34, 442, 32, "response"], [417, 42, 442, 40], [417, 43, 442, 41, "status"], [417, 49, 442, 47], [417, 54, 442, 52, "response"], [417, 62, 442, 60], [417, 63, 442, 61, "statusText"], [417, 73, 442, 71], [417, 75, 442, 73], [417, 76, 442, 74], [418, 8, 443, 6], [419, 8, 445, 6], [419, 14, 445, 12, "status"], [419, 20, 445, 18], [419, 23, 445, 21], [419, 29, 445, 27, "response"], [419, 37, 445, 35], [419, 38, 445, 36, "json"], [419, 42, 445, 40], [419, 43, 445, 41], [419, 44, 445, 42], [420, 8, 446, 6, "console"], [420, 15, 446, 13], [420, 16, 446, 14, "log"], [420, 19, 446, 17], [420, 20, 446, 18], [420, 54, 446, 52], [420, 56, 446, 54, "status"], [420, 62, 446, 60], [420, 63, 446, 61], [421, 8, 448, 6], [421, 12, 448, 10, "status"], [421, 18, 448, 16], [421, 19, 448, 17, "status"], [421, 25, 448, 23], [421, 30, 448, 28], [421, 41, 448, 39], [421, 43, 448, 41], [422, 10, 449, 8, "console"], [422, 17, 449, 15], [422, 18, 449, 16, "log"], [422, 21, 449, 19], [422, 22, 449, 20], [422, 73, 449, 71], [422, 74, 449, 72], [423, 10, 450, 8, "setProcessingProgress"], [423, 31, 450, 29], [423, 32, 450, 30], [423, 35, 450, 33], [423, 36, 450, 34], [424, 10, 451, 8, "setProcessingState"], [424, 28, 451, 26], [424, 29, 451, 27], [424, 40, 451, 38], [424, 41, 451, 39], [425, 10, 452, 8], [426, 10, 453, 8], [426, 16, 453, 14, "result"], [426, 22, 453, 20], [426, 25, 453, 23], [427, 12, 454, 10, "imageUrl"], [427, 20, 454, 18], [427, 22, 454, 20, "status"], [427, 28, 454, 26], [427, 29, 454, 27, "publicUrl"], [427, 38, 454, 36], [428, 12, 454, 38], [429, 12, 455, 10, "localUri"], [429, 20, 455, 18], [429, 22, 455, 20, "capturedPhoto"], [429, 35, 455, 33], [429, 39, 455, 37, "status"], [429, 45, 455, 43], [429, 46, 455, 44, "publicUrl"], [429, 55, 455, 53], [430, 12, 455, 55], [431, 12, 456, 10, "challengeCode"], [431, 25, 456, 23], [431, 27, 456, 25, "challengeCode"], [431, 40, 456, 38], [431, 44, 456, 42], [431, 46, 456, 44], [432, 12, 457, 10, "timestamp"], [432, 21, 457, 19], [433, 12, 458, 10, "processingStatus"], [433, 28, 458, 26], [433, 30, 458, 28], [434, 10, 459, 8], [434, 11, 459, 9], [435, 10, 460, 8, "console"], [435, 17, 460, 15], [435, 18, 460, 16, "log"], [435, 21, 460, 19], [435, 22, 460, 20], [435, 57, 460, 55], [435, 59, 460, 57, "result"], [435, 65, 460, 63], [435, 66, 460, 64], [436, 10, 461, 8, "onComplete"], [436, 20, 461, 18], [436, 21, 461, 19, "result"], [436, 27, 461, 25], [436, 28, 461, 26], [437, 10, 462, 8], [438, 8, 463, 6], [438, 9, 463, 7], [438, 15, 463, 13], [438, 19, 463, 17, "status"], [438, 25, 463, 23], [438, 26, 463, 24, "status"], [438, 32, 463, 30], [438, 37, 463, 35], [438, 45, 463, 43], [438, 47, 463, 45], [439, 10, 464, 8, "console"], [439, 17, 464, 15], [439, 18, 464, 16, "error"], [439, 23, 464, 21], [439, 24, 464, 22], [439, 60, 464, 58], [439, 62, 464, 60, "status"], [439, 68, 464, 66], [439, 69, 464, 67, "error"], [439, 74, 464, 72], [439, 75, 464, 73], [440, 10, 465, 8], [440, 16, 465, 14], [440, 20, 465, 18, "Error"], [440, 25, 465, 23], [440, 26, 465, 24, "status"], [440, 32, 465, 30], [440, 33, 465, 31, "error"], [440, 38, 465, 36], [440, 42, 465, 40], [440, 61, 465, 59], [440, 62, 465, 60], [441, 8, 466, 6], [441, 9, 466, 7], [441, 15, 466, 13], [442, 10, 467, 8], [443, 10, 468, 8], [443, 16, 468, 14, "progressValue"], [443, 29, 468, 27], [443, 32, 468, 30], [443, 34, 468, 32], [443, 37, 468, 36, "attempts"], [443, 45, 468, 44], [443, 48, 468, 47, "MAX_ATTEMPTS"], [443, 60, 468, 59], [443, 63, 468, 63], [443, 65, 468, 65], [444, 10, 469, 8, "console"], [444, 17, 469, 15], [444, 18, 469, 16, "log"], [444, 21, 469, 19], [444, 22, 469, 20], [444, 71, 469, 69, "progressValue"], [444, 84, 469, 82], [444, 87, 469, 85], [444, 88, 469, 86], [445, 10, 470, 8, "setProcessingProgress"], [445, 31, 470, 29], [445, 32, 470, 30, "progressValue"], [445, 45, 470, 43], [445, 46, 470, 44], [446, 10, 472, 8, "setTimeout"], [446, 20, 472, 18], [446, 21, 472, 19], [446, 27, 472, 25], [447, 12, 473, 10, "pollForCompletion"], [447, 29, 473, 27], [447, 30, 473, 28, "jobId"], [447, 35, 473, 33], [447, 37, 473, 35, "timestamp"], [447, 46, 473, 44], [447, 48, 473, 46, "attempts"], [447, 56, 473, 54], [447, 59, 473, 57], [447, 60, 473, 58], [447, 61, 473, 59], [448, 10, 474, 8], [448, 11, 474, 9], [448, 13, 474, 11, "POLL_INTERVAL"], [448, 26, 474, 24], [448, 27, 474, 25], [449, 8, 475, 6], [450, 6, 476, 4], [450, 7, 476, 5], [450, 8, 476, 6], [450, 15, 476, 13, "error"], [450, 20, 476, 18], [450, 22, 476, 20], [451, 8, 477, 6, "console"], [451, 15, 477, 13], [451, 16, 477, 14, "error"], [451, 21, 477, 19], [451, 22, 477, 20], [451, 54, 477, 52], [451, 56, 477, 54, "error"], [451, 61, 477, 59], [451, 62, 477, 60], [452, 8, 478, 6, "setErrorMessage"], [452, 23, 478, 21], [452, 24, 478, 22], [452, 62, 478, 60, "error"], [452, 67, 478, 65], [452, 68, 478, 66, "message"], [452, 75, 478, 73], [452, 77, 478, 75], [452, 78, 478, 76], [453, 8, 479, 6, "setProcessingState"], [453, 26, 479, 24], [453, 27, 479, 25], [453, 34, 479, 32], [453, 35, 479, 33], [454, 6, 480, 4], [455, 4, 481, 2], [455, 5, 481, 3], [456, 4, 482, 2], [457, 4, 483, 2], [457, 10, 483, 8, "getAuthToken"], [457, 22, 483, 20], [457, 25, 483, 23], [457, 31, 483, 23, "getAuthToken"], [457, 32, 483, 23], [457, 37, 483, 52], [458, 6, 484, 4], [459, 6, 485, 4], [460, 6, 486, 4], [460, 13, 486, 11], [460, 30, 486, 28], [461, 4, 487, 2], [461, 5, 487, 3], [463, 4, 489, 2], [464, 4, 490, 2], [464, 10, 490, 8, "retryCapture"], [464, 22, 490, 20], [464, 25, 490, 23], [464, 29, 490, 23, "useCallback"], [464, 47, 490, 34], [464, 49, 490, 35], [464, 55, 490, 41], [465, 6, 491, 4, "console"], [465, 13, 491, 11], [465, 14, 491, 12, "log"], [465, 17, 491, 15], [465, 18, 491, 16], [465, 55, 491, 53], [465, 56, 491, 54], [466, 6, 492, 4, "setProcessingState"], [466, 24, 492, 22], [466, 25, 492, 23], [466, 31, 492, 29], [466, 32, 492, 30], [467, 6, 493, 4, "setErrorMessage"], [467, 21, 493, 19], [467, 22, 493, 20], [467, 24, 493, 22], [467, 25, 493, 23], [468, 6, 494, 4, "setCapturedPhoto"], [468, 22, 494, 20], [468, 23, 494, 21], [468, 25, 494, 23], [468, 26, 494, 24], [469, 6, 495, 4, "setProcessingProgress"], [469, 27, 495, 25], [469, 28, 495, 26], [469, 29, 495, 27], [469, 30, 495, 28], [470, 4, 496, 2], [470, 5, 496, 3], [470, 7, 496, 5], [470, 9, 496, 7], [470, 10, 496, 8], [471, 4, 497, 2], [472, 4, 498, 2], [472, 8, 498, 2, "useEffect"], [472, 24, 498, 11], [472, 26, 498, 12], [472, 32, 498, 18], [473, 6, 499, 4, "console"], [473, 13, 499, 11], [473, 14, 499, 12, "log"], [473, 17, 499, 15], [473, 18, 499, 16], [473, 53, 499, 51], [473, 55, 499, 53, "permission"], [473, 65, 499, 63], [473, 66, 499, 64], [474, 6, 500, 4], [474, 10, 500, 8, "permission"], [474, 20, 500, 18], [474, 22, 500, 20], [475, 8, 501, 6, "console"], [475, 15, 501, 13], [475, 16, 501, 14, "log"], [475, 19, 501, 17], [475, 20, 501, 18], [475, 57, 501, 55], [475, 59, 501, 57, "permission"], [475, 69, 501, 67], [475, 70, 501, 68, "granted"], [475, 77, 501, 75], [475, 78, 501, 76], [476, 6, 502, 4], [477, 4, 503, 2], [477, 5, 503, 3], [477, 7, 503, 5], [477, 8, 503, 6, "permission"], [477, 18, 503, 16], [477, 19, 503, 17], [477, 20, 503, 18], [478, 4, 504, 2], [479, 4, 505, 2], [479, 8, 505, 6], [479, 9, 505, 7, "permission"], [479, 19, 505, 17], [479, 21, 505, 19], [480, 6, 506, 4, "console"], [480, 13, 506, 11], [480, 14, 506, 12, "log"], [480, 17, 506, 15], [480, 18, 506, 16], [480, 67, 506, 65], [480, 68, 506, 66], [481, 6, 507, 4], [481, 26, 508, 6], [481, 30, 508, 6, "_jsxDevRuntime"], [481, 44, 508, 6], [481, 45, 508, 6, "jsxDEV"], [481, 51, 508, 6], [481, 53, 508, 7, "_View"], [481, 58, 508, 7], [481, 59, 508, 7, "default"], [481, 66, 508, 11], [482, 8, 508, 12, "style"], [482, 13, 508, 17], [482, 15, 508, 19, "styles"], [482, 21, 508, 25], [482, 22, 508, 26, "container"], [482, 31, 508, 36], [483, 8, 508, 36, "children"], [483, 16, 508, 36], [483, 32, 509, 8], [483, 36, 509, 8, "_jsxDevRuntime"], [483, 50, 509, 8], [483, 51, 509, 8, "jsxDEV"], [483, 57, 509, 8], [483, 59, 509, 9, "_ActivityIndicator"], [483, 77, 509, 9], [483, 78, 509, 9, "default"], [483, 85, 509, 26], [484, 10, 509, 27, "size"], [484, 14, 509, 31], [484, 16, 509, 32], [484, 23, 509, 39], [485, 10, 509, 40, "color"], [485, 15, 509, 45], [485, 17, 509, 46], [486, 8, 509, 55], [487, 10, 509, 55, "fileName"], [487, 18, 509, 55], [487, 20, 509, 55, "_jsxFileName"], [487, 32, 509, 55], [488, 10, 509, 55, "lineNumber"], [488, 20, 509, 55], [489, 10, 509, 55, "columnNumber"], [489, 22, 509, 55], [490, 8, 509, 55], [490, 15, 509, 57], [490, 16, 509, 58], [490, 31, 510, 8], [490, 35, 510, 8, "_jsxDevRuntime"], [490, 49, 510, 8], [490, 50, 510, 8, "jsxDEV"], [490, 56, 510, 8], [490, 58, 510, 9, "_Text"], [490, 63, 510, 9], [490, 64, 510, 9, "default"], [490, 71, 510, 13], [491, 10, 510, 14, "style"], [491, 15, 510, 19], [491, 17, 510, 21, "styles"], [491, 23, 510, 27], [491, 24, 510, 28, "loadingText"], [491, 35, 510, 40], [492, 10, 510, 40, "children"], [492, 18, 510, 40], [492, 20, 510, 41], [493, 8, 510, 58], [494, 10, 510, 58, "fileName"], [494, 18, 510, 58], [494, 20, 510, 58, "_jsxFileName"], [494, 32, 510, 58], [495, 10, 510, 58, "lineNumber"], [495, 20, 510, 58], [496, 10, 510, 58, "columnNumber"], [496, 22, 510, 58], [497, 8, 510, 58], [497, 15, 510, 64], [497, 16, 510, 65], [498, 6, 510, 65], [499, 8, 510, 65, "fileName"], [499, 16, 510, 65], [499, 18, 510, 65, "_jsxFileName"], [499, 30, 510, 65], [500, 8, 510, 65, "lineNumber"], [500, 18, 510, 65], [501, 8, 510, 65, "columnNumber"], [501, 20, 510, 65], [502, 6, 510, 65], [502, 13, 511, 12], [502, 14, 511, 13], [503, 4, 513, 2], [504, 4, 514, 2], [504, 8, 514, 6], [504, 9, 514, 7, "permission"], [504, 19, 514, 17], [504, 20, 514, 18, "granted"], [504, 27, 514, 25], [504, 29, 514, 27], [505, 6, 515, 4, "console"], [505, 13, 515, 11], [505, 14, 515, 12, "log"], [505, 17, 515, 15], [505, 18, 515, 16], [505, 93, 515, 91], [505, 94, 515, 92], [506, 6, 516, 4], [506, 26, 517, 6], [506, 30, 517, 6, "_jsxDevRuntime"], [506, 44, 517, 6], [506, 45, 517, 6, "jsxDEV"], [506, 51, 517, 6], [506, 53, 517, 7, "_View"], [506, 58, 517, 7], [506, 59, 517, 7, "default"], [506, 66, 517, 11], [507, 8, 517, 12, "style"], [507, 13, 517, 17], [507, 15, 517, 19, "styles"], [507, 21, 517, 25], [507, 22, 517, 26, "container"], [507, 31, 517, 36], [508, 8, 517, 36, "children"], [508, 16, 517, 36], [508, 31, 518, 8], [508, 35, 518, 8, "_jsxDevRuntime"], [508, 49, 518, 8], [508, 50, 518, 8, "jsxDEV"], [508, 56, 518, 8], [508, 58, 518, 9, "_View"], [508, 63, 518, 9], [508, 64, 518, 9, "default"], [508, 71, 518, 13], [509, 10, 518, 14, "style"], [509, 15, 518, 19], [509, 17, 518, 21, "styles"], [509, 23, 518, 27], [509, 24, 518, 28, "permissionContent"], [509, 41, 518, 46], [510, 10, 518, 46, "children"], [510, 18, 518, 46], [510, 34, 519, 10], [510, 38, 519, 10, "_jsxDevRuntime"], [510, 52, 519, 10], [510, 53, 519, 10, "jsxDEV"], [510, 59, 519, 10], [510, 61, 519, 11, "_lucideReactNative"], [510, 79, 519, 11], [510, 80, 519, 11, "Camera"], [510, 86, 519, 21], [511, 12, 519, 22, "size"], [511, 16, 519, 26], [511, 18, 519, 28], [511, 20, 519, 31], [512, 12, 519, 32, "color"], [512, 17, 519, 37], [512, 19, 519, 38], [513, 10, 519, 47], [514, 12, 519, 47, "fileName"], [514, 20, 519, 47], [514, 22, 519, 47, "_jsxFileName"], [514, 34, 519, 47], [515, 12, 519, 47, "lineNumber"], [515, 22, 519, 47], [516, 12, 519, 47, "columnNumber"], [516, 24, 519, 47], [517, 10, 519, 47], [517, 17, 519, 49], [517, 18, 519, 50], [517, 33, 520, 10], [517, 37, 520, 10, "_jsxDevRuntime"], [517, 51, 520, 10], [517, 52, 520, 10, "jsxDEV"], [517, 58, 520, 10], [517, 60, 520, 11, "_Text"], [517, 65, 520, 11], [517, 66, 520, 11, "default"], [517, 73, 520, 15], [518, 12, 520, 16, "style"], [518, 17, 520, 21], [518, 19, 520, 23, "styles"], [518, 25, 520, 29], [518, 26, 520, 30, "permissionTitle"], [518, 41, 520, 46], [519, 12, 520, 46, "children"], [519, 20, 520, 46], [519, 22, 520, 47], [520, 10, 520, 73], [521, 12, 520, 73, "fileName"], [521, 20, 520, 73], [521, 22, 520, 73, "_jsxFileName"], [521, 34, 520, 73], [522, 12, 520, 73, "lineNumber"], [522, 22, 520, 73], [523, 12, 520, 73, "columnNumber"], [523, 24, 520, 73], [524, 10, 520, 73], [524, 17, 520, 79], [524, 18, 520, 80], [524, 33, 521, 10], [524, 37, 521, 10, "_jsxDevRuntime"], [524, 51, 521, 10], [524, 52, 521, 10, "jsxDEV"], [524, 58, 521, 10], [524, 60, 521, 11, "_Text"], [524, 65, 521, 11], [524, 66, 521, 11, "default"], [524, 73, 521, 15], [525, 12, 521, 16, "style"], [525, 17, 521, 21], [525, 19, 521, 23, "styles"], [525, 25, 521, 29], [525, 26, 521, 30, "permissionDescription"], [525, 47, 521, 52], [526, 12, 521, 52, "children"], [526, 20, 521, 52], [526, 22, 521, 53], [527, 10, 524, 10], [528, 12, 524, 10, "fileName"], [528, 20, 524, 10], [528, 22, 524, 10, "_jsxFileName"], [528, 34, 524, 10], [529, 12, 524, 10, "lineNumber"], [529, 22, 524, 10], [530, 12, 524, 10, "columnNumber"], [530, 24, 524, 10], [531, 10, 524, 10], [531, 17, 524, 16], [531, 18, 524, 17], [531, 33, 525, 10], [531, 37, 525, 10, "_jsxDevRuntime"], [531, 51, 525, 10], [531, 52, 525, 10, "jsxDEV"], [531, 58, 525, 10], [531, 60, 525, 11, "_TouchableOpacity"], [531, 77, 525, 11], [531, 78, 525, 11, "default"], [531, 85, 525, 27], [532, 12, 525, 28, "onPress"], [532, 19, 525, 35], [532, 21, 525, 37, "requestPermission"], [532, 38, 525, 55], [533, 12, 525, 56, "style"], [533, 17, 525, 61], [533, 19, 525, 63, "styles"], [533, 25, 525, 69], [533, 26, 525, 70, "primaryButton"], [533, 39, 525, 84], [534, 12, 525, 84, "children"], [534, 20, 525, 84], [534, 35, 526, 12], [534, 39, 526, 12, "_jsxDevRuntime"], [534, 53, 526, 12], [534, 54, 526, 12, "jsxDEV"], [534, 60, 526, 12], [534, 62, 526, 13, "_Text"], [534, 67, 526, 13], [534, 68, 526, 13, "default"], [534, 75, 526, 17], [535, 14, 526, 18, "style"], [535, 19, 526, 23], [535, 21, 526, 25, "styles"], [535, 27, 526, 31], [535, 28, 526, 32, "primaryButtonText"], [535, 45, 526, 50], [536, 14, 526, 50, "children"], [536, 22, 526, 50], [536, 24, 526, 51], [537, 12, 526, 67], [538, 14, 526, 67, "fileName"], [538, 22, 526, 67], [538, 24, 526, 67, "_jsxFileName"], [538, 36, 526, 67], [539, 14, 526, 67, "lineNumber"], [539, 24, 526, 67], [540, 14, 526, 67, "columnNumber"], [540, 26, 526, 67], [541, 12, 526, 67], [541, 19, 526, 73], [542, 10, 526, 74], [543, 12, 526, 74, "fileName"], [543, 20, 526, 74], [543, 22, 526, 74, "_jsxFileName"], [543, 34, 526, 74], [544, 12, 526, 74, "lineNumber"], [544, 22, 526, 74], [545, 12, 526, 74, "columnNumber"], [545, 24, 526, 74], [546, 10, 526, 74], [546, 17, 527, 28], [546, 18, 527, 29], [546, 33, 528, 10], [546, 37, 528, 10, "_jsxDevRuntime"], [546, 51, 528, 10], [546, 52, 528, 10, "jsxDEV"], [546, 58, 528, 10], [546, 60, 528, 11, "_TouchableOpacity"], [546, 77, 528, 11], [546, 78, 528, 11, "default"], [546, 85, 528, 27], [547, 12, 528, 28, "onPress"], [547, 19, 528, 35], [547, 21, 528, 37, "onCancel"], [547, 29, 528, 46], [548, 12, 528, 47, "style"], [548, 17, 528, 52], [548, 19, 528, 54, "styles"], [548, 25, 528, 60], [548, 26, 528, 61, "secondaryButton"], [548, 41, 528, 77], [549, 12, 528, 77, "children"], [549, 20, 528, 77], [549, 35, 529, 12], [549, 39, 529, 12, "_jsxDevRuntime"], [549, 53, 529, 12], [549, 54, 529, 12, "jsxDEV"], [549, 60, 529, 12], [549, 62, 529, 13, "_Text"], [549, 67, 529, 13], [549, 68, 529, 13, "default"], [549, 75, 529, 17], [550, 14, 529, 18, "style"], [550, 19, 529, 23], [550, 21, 529, 25, "styles"], [550, 27, 529, 31], [550, 28, 529, 32, "secondaryButtonText"], [550, 47, 529, 52], [551, 14, 529, 52, "children"], [551, 22, 529, 52], [551, 24, 529, 53], [552, 12, 529, 59], [553, 14, 529, 59, "fileName"], [553, 22, 529, 59], [553, 24, 529, 59, "_jsxFileName"], [553, 36, 529, 59], [554, 14, 529, 59, "lineNumber"], [554, 24, 529, 59], [555, 14, 529, 59, "columnNumber"], [555, 26, 529, 59], [556, 12, 529, 59], [556, 19, 529, 65], [557, 10, 529, 66], [558, 12, 529, 66, "fileName"], [558, 20, 529, 66], [558, 22, 529, 66, "_jsxFileName"], [558, 34, 529, 66], [559, 12, 529, 66, "lineNumber"], [559, 22, 529, 66], [560, 12, 529, 66, "columnNumber"], [560, 24, 529, 66], [561, 10, 529, 66], [561, 17, 530, 28], [561, 18, 530, 29], [562, 8, 530, 29], [563, 10, 530, 29, "fileName"], [563, 18, 530, 29], [563, 20, 530, 29, "_jsxFileName"], [563, 32, 530, 29], [564, 10, 530, 29, "lineNumber"], [564, 20, 530, 29], [565, 10, 530, 29, "columnNumber"], [565, 22, 530, 29], [566, 8, 530, 29], [566, 15, 531, 14], [567, 6, 531, 15], [568, 8, 531, 15, "fileName"], [568, 16, 531, 15], [568, 18, 531, 15, "_jsxFileName"], [568, 30, 531, 15], [569, 8, 531, 15, "lineNumber"], [569, 18, 531, 15], [570, 8, 531, 15, "columnNumber"], [570, 20, 531, 15], [571, 6, 531, 15], [571, 13, 532, 12], [571, 14, 532, 13], [572, 4, 534, 2], [573, 4, 535, 2], [574, 4, 536, 2, "console"], [574, 11, 536, 9], [574, 12, 536, 10, "log"], [574, 15, 536, 13], [574, 16, 536, 14], [574, 55, 536, 53], [574, 56, 536, 54], [575, 4, 538, 2], [575, 24, 539, 4], [575, 28, 539, 4, "_jsxDevRuntime"], [575, 42, 539, 4], [575, 43, 539, 4, "jsxDEV"], [575, 49, 539, 4], [575, 51, 539, 5, "_View"], [575, 56, 539, 5], [575, 57, 539, 5, "default"], [575, 64, 539, 9], [576, 6, 539, 10, "style"], [576, 11, 539, 15], [576, 13, 539, 17, "styles"], [576, 19, 539, 23], [576, 20, 539, 24, "container"], [576, 29, 539, 34], [577, 6, 539, 34, "children"], [577, 14, 539, 34], [577, 30, 541, 6], [577, 34, 541, 6, "_jsxDevRuntime"], [577, 48, 541, 6], [577, 49, 541, 6, "jsxDEV"], [577, 55, 541, 6], [577, 57, 541, 7, "_View"], [577, 62, 541, 7], [577, 63, 541, 7, "default"], [577, 70, 541, 11], [578, 8, 541, 12, "style"], [578, 13, 541, 17], [578, 15, 541, 19, "styles"], [578, 21, 541, 25], [578, 22, 541, 26, "cameraContainer"], [578, 37, 541, 42], [579, 8, 541, 43, "id"], [579, 10, 541, 45], [579, 12, 541, 46], [579, 29, 541, 63], [580, 8, 541, 63, "children"], [580, 16, 541, 63], [580, 32, 542, 8], [580, 36, 542, 8, "_jsxDevRuntime"], [580, 50, 542, 8], [580, 51, 542, 8, "jsxDEV"], [580, 57, 542, 8], [580, 59, 542, 9, "_expoCamera"], [580, 70, 542, 9], [580, 71, 542, 9, "CameraView"], [580, 81, 542, 19], [581, 10, 543, 10, "ref"], [581, 13, 543, 13], [581, 15, 543, 15, "cameraRef"], [581, 24, 543, 25], [582, 10, 544, 10, "style"], [582, 15, 544, 15], [582, 17, 544, 17], [582, 18, 544, 18, "styles"], [582, 24, 544, 24], [582, 25, 544, 25, "camera"], [582, 31, 544, 31], [582, 33, 544, 33], [583, 12, 544, 35, "backgroundColor"], [583, 27, 544, 50], [583, 29, 544, 52], [584, 10, 544, 62], [584, 11, 544, 63], [584, 12, 544, 65], [585, 10, 545, 10, "facing"], [585, 16, 545, 16], [585, 18, 545, 17], [585, 24, 545, 23], [586, 10, 546, 10, "onLayout"], [586, 18, 546, 18], [586, 20, 546, 21, "e"], [586, 21, 546, 22], [586, 25, 546, 27], [587, 12, 547, 12, "console"], [587, 19, 547, 19], [587, 20, 547, 20, "log"], [587, 23, 547, 23], [587, 24, 547, 24], [587, 56, 547, 56], [587, 58, 547, 58, "e"], [587, 59, 547, 59], [587, 60, 547, 60, "nativeEvent"], [587, 71, 547, 71], [587, 72, 547, 72, "layout"], [587, 78, 547, 78], [587, 79, 547, 79], [588, 12, 548, 12, "setViewSize"], [588, 23, 548, 23], [588, 24, 548, 24], [589, 14, 548, 26, "width"], [589, 19, 548, 31], [589, 21, 548, 33, "e"], [589, 22, 548, 34], [589, 23, 548, 35, "nativeEvent"], [589, 34, 548, 46], [589, 35, 548, 47, "layout"], [589, 41, 548, 53], [589, 42, 548, 54, "width"], [589, 47, 548, 59], [590, 14, 548, 61, "height"], [590, 20, 548, 67], [590, 22, 548, 69, "e"], [590, 23, 548, 70], [590, 24, 548, 71, "nativeEvent"], [590, 35, 548, 82], [590, 36, 548, 83, "layout"], [590, 42, 548, 89], [590, 43, 548, 90, "height"], [591, 12, 548, 97], [591, 13, 548, 98], [591, 14, 548, 99], [592, 10, 549, 10], [592, 11, 549, 12], [593, 10, 550, 10, "onCameraReady"], [593, 23, 550, 23], [593, 25, 550, 25, "onCameraReady"], [593, 26, 550, 25], [593, 31, 550, 31], [594, 12, 551, 12, "console"], [594, 19, 551, 19], [594, 20, 551, 20, "log"], [594, 23, 551, 23], [594, 24, 551, 24], [594, 55, 551, 55], [594, 56, 551, 56], [595, 12, 552, 12, "setIsCameraReady"], [595, 28, 552, 28], [595, 29, 552, 29], [595, 33, 552, 33], [595, 34, 552, 34], [595, 35, 552, 35], [595, 36, 552, 36], [596, 10, 553, 10], [596, 11, 553, 12], [597, 10, 554, 10, "onMountError"], [597, 22, 554, 22], [597, 24, 554, 25, "error"], [597, 29, 554, 30], [597, 33, 554, 35], [598, 12, 555, 12, "console"], [598, 19, 555, 19], [598, 20, 555, 20, "error"], [598, 25, 555, 25], [598, 26, 555, 26], [598, 63, 555, 63], [598, 65, 555, 65, "error"], [598, 70, 555, 70], [598, 71, 555, 71], [599, 12, 556, 12, "setErrorMessage"], [599, 27, 556, 27], [599, 28, 556, 28], [599, 57, 556, 57], [599, 58, 556, 58], [600, 12, 557, 12, "setProcessingState"], [600, 30, 557, 30], [600, 31, 557, 31], [600, 38, 557, 38], [600, 39, 557, 39], [601, 10, 558, 10], [602, 8, 558, 12], [603, 10, 558, 12, "fileName"], [603, 18, 558, 12], [603, 20, 558, 12, "_jsxFileName"], [603, 32, 558, 12], [604, 10, 558, 12, "lineNumber"], [604, 20, 558, 12], [605, 10, 558, 12, "columnNumber"], [605, 22, 558, 12], [606, 8, 558, 12], [606, 15, 559, 9], [606, 16, 559, 10], [606, 18, 561, 9], [606, 19, 561, 10, "isCameraReady"], [606, 32, 561, 23], [606, 49, 562, 10], [606, 53, 562, 10, "_jsxDevRuntime"], [606, 67, 562, 10], [606, 68, 562, 10, "jsxDEV"], [606, 74, 562, 10], [606, 76, 562, 11, "_View"], [606, 81, 562, 11], [606, 82, 562, 11, "default"], [606, 89, 562, 15], [607, 10, 562, 16, "style"], [607, 15, 562, 21], [607, 17, 562, 23], [607, 18, 562, 24, "StyleSheet"], [607, 37, 562, 34], [607, 38, 562, 35, "absoluteFill"], [607, 50, 562, 47], [607, 52, 562, 49], [608, 12, 562, 51, "backgroundColor"], [608, 27, 562, 66], [608, 29, 562, 68], [608, 49, 562, 88], [609, 12, 562, 90, "justifyContent"], [609, 26, 562, 104], [609, 28, 562, 106], [609, 36, 562, 114], [610, 12, 562, 116, "alignItems"], [610, 22, 562, 126], [610, 24, 562, 128], [610, 32, 562, 136], [611, 12, 562, 138, "zIndex"], [611, 18, 562, 144], [611, 20, 562, 146], [612, 10, 562, 151], [612, 11, 562, 152], [612, 12, 562, 154], [613, 10, 562, 154, "children"], [613, 18, 562, 154], [613, 33, 563, 12], [613, 37, 563, 12, "_jsxDevRuntime"], [613, 51, 563, 12], [613, 52, 563, 12, "jsxDEV"], [613, 58, 563, 12], [613, 60, 563, 13, "_View"], [613, 65, 563, 13], [613, 66, 563, 13, "default"], [613, 73, 563, 17], [614, 12, 563, 18, "style"], [614, 17, 563, 23], [614, 19, 563, 25], [615, 14, 563, 27, "backgroundColor"], [615, 29, 563, 42], [615, 31, 563, 44], [615, 51, 563, 64], [616, 14, 563, 66, "padding"], [616, 21, 563, 73], [616, 23, 563, 75], [616, 25, 563, 77], [617, 14, 563, 79, "borderRadius"], [617, 26, 563, 91], [617, 28, 563, 93], [617, 30, 563, 95], [618, 14, 563, 97, "alignItems"], [618, 24, 563, 107], [618, 26, 563, 109], [619, 12, 563, 118], [619, 13, 563, 120], [620, 12, 563, 120, "children"], [620, 20, 563, 120], [620, 36, 564, 14], [620, 40, 564, 14, "_jsxDevRuntime"], [620, 54, 564, 14], [620, 55, 564, 14, "jsxDEV"], [620, 61, 564, 14], [620, 63, 564, 15, "_ActivityIndicator"], [620, 81, 564, 15], [620, 82, 564, 15, "default"], [620, 89, 564, 32], [621, 14, 564, 33, "size"], [621, 18, 564, 37], [621, 20, 564, 38], [621, 27, 564, 45], [622, 14, 564, 46, "color"], [622, 19, 564, 51], [622, 21, 564, 52], [622, 30, 564, 61], [623, 14, 564, 62, "style"], [623, 19, 564, 67], [623, 21, 564, 69], [624, 16, 564, 71, "marginBottom"], [624, 28, 564, 83], [624, 30, 564, 85], [625, 14, 564, 88], [626, 12, 564, 90], [627, 14, 564, 90, "fileName"], [627, 22, 564, 90], [627, 24, 564, 90, "_jsxFileName"], [627, 36, 564, 90], [628, 14, 564, 90, "lineNumber"], [628, 24, 564, 90], [629, 14, 564, 90, "columnNumber"], [629, 26, 564, 90], [630, 12, 564, 90], [630, 19, 564, 92], [630, 20, 564, 93], [630, 35, 565, 14], [630, 39, 565, 14, "_jsxDevRuntime"], [630, 53, 565, 14], [630, 54, 565, 14, "jsxDEV"], [630, 60, 565, 14], [630, 62, 565, 15, "_Text"], [630, 67, 565, 15], [630, 68, 565, 15, "default"], [630, 75, 565, 19], [631, 14, 565, 20, "style"], [631, 19, 565, 25], [631, 21, 565, 27], [632, 16, 565, 29, "color"], [632, 21, 565, 34], [632, 23, 565, 36], [632, 29, 565, 42], [633, 16, 565, 44, "fontSize"], [633, 24, 565, 52], [633, 26, 565, 54], [633, 28, 565, 56], [634, 16, 565, 58, "fontWeight"], [634, 26, 565, 68], [634, 28, 565, 70], [635, 14, 565, 76], [635, 15, 565, 78], [636, 14, 565, 78, "children"], [636, 22, 565, 78], [636, 24, 565, 79], [637, 12, 565, 101], [638, 14, 565, 101, "fileName"], [638, 22, 565, 101], [638, 24, 565, 101, "_jsxFileName"], [638, 36, 565, 101], [639, 14, 565, 101, "lineNumber"], [639, 24, 565, 101], [640, 14, 565, 101, "columnNumber"], [640, 26, 565, 101], [641, 12, 565, 101], [641, 19, 565, 107], [641, 20, 565, 108], [641, 35, 566, 14], [641, 39, 566, 14, "_jsxDevRuntime"], [641, 53, 566, 14], [641, 54, 566, 14, "jsxDEV"], [641, 60, 566, 14], [641, 62, 566, 15, "_Text"], [641, 67, 566, 15], [641, 68, 566, 15, "default"], [641, 75, 566, 19], [642, 14, 566, 20, "style"], [642, 19, 566, 25], [642, 21, 566, 27], [643, 16, 566, 29, "color"], [643, 21, 566, 34], [643, 23, 566, 36], [643, 32, 566, 45], [644, 16, 566, 47, "fontSize"], [644, 24, 566, 55], [644, 26, 566, 57], [644, 28, 566, 59], [645, 16, 566, 61, "marginTop"], [645, 25, 566, 70], [645, 27, 566, 72], [646, 14, 566, 74], [646, 15, 566, 76], [647, 14, 566, 76, "children"], [647, 22, 566, 76], [647, 24, 566, 77], [648, 12, 566, 88], [649, 14, 566, 88, "fileName"], [649, 22, 566, 88], [649, 24, 566, 88, "_jsxFileName"], [649, 36, 566, 88], [650, 14, 566, 88, "lineNumber"], [650, 24, 566, 88], [651, 14, 566, 88, "columnNumber"], [651, 26, 566, 88], [652, 12, 566, 88], [652, 19, 566, 94], [652, 20, 566, 95], [653, 10, 566, 95], [654, 12, 566, 95, "fileName"], [654, 20, 566, 95], [654, 22, 566, 95, "_jsxFileName"], [654, 34, 566, 95], [655, 12, 566, 95, "lineNumber"], [655, 22, 566, 95], [656, 12, 566, 95, "columnNumber"], [656, 24, 566, 95], [657, 10, 566, 95], [657, 17, 567, 18], [658, 8, 567, 19], [659, 10, 567, 19, "fileName"], [659, 18, 567, 19], [659, 20, 567, 19, "_jsxFileName"], [659, 32, 567, 19], [660, 10, 567, 19, "lineNumber"], [660, 20, 567, 19], [661, 10, 567, 19, "columnNumber"], [661, 22, 567, 19], [662, 8, 567, 19], [662, 15, 568, 16], [662, 16, 569, 9], [662, 18, 572, 9, "isCameraReady"], [662, 31, 572, 22], [662, 35, 572, 26, "previewBlurEnabled"], [662, 53, 572, 44], [662, 57, 572, 48, "viewSize"], [662, 65, 572, 56], [662, 66, 572, 57, "width"], [662, 71, 572, 62], [662, 74, 572, 65], [662, 75, 572, 66], [662, 92, 573, 10], [662, 96, 573, 10, "_jsxDevRuntime"], [662, 110, 573, 10], [662, 111, 573, 10, "jsxDEV"], [662, 117, 573, 10], [662, 119, 573, 10, "_jsxDevRuntime"], [662, 133, 573, 10], [662, 134, 573, 10, "Fragment"], [662, 142, 573, 10], [663, 10, 573, 10, "children"], [663, 18, 573, 10], [663, 34, 575, 12], [663, 38, 575, 12, "_jsxDevRuntime"], [663, 52, 575, 12], [663, 53, 575, 12, "jsxDEV"], [663, 59, 575, 12], [663, 61, 575, 13, "_LiveFaceCanvas"], [663, 76, 575, 13], [663, 77, 575, 13, "default"], [663, 84, 575, 27], [664, 12, 575, 28, "containerId"], [664, 23, 575, 39], [664, 25, 575, 40], [664, 42, 575, 57], [665, 12, 575, 58, "width"], [665, 17, 575, 63], [665, 19, 575, 65, "viewSize"], [665, 27, 575, 73], [665, 28, 575, 74, "width"], [665, 33, 575, 80], [666, 12, 575, 81, "height"], [666, 18, 575, 87], [666, 20, 575, 89, "viewSize"], [666, 28, 575, 97], [666, 29, 575, 98, "height"], [667, 10, 575, 105], [668, 12, 575, 105, "fileName"], [668, 20, 575, 105], [668, 22, 575, 105, "_jsxFileName"], [668, 34, 575, 105], [669, 12, 575, 105, "lineNumber"], [669, 22, 575, 105], [670, 12, 575, 105, "columnNumber"], [670, 24, 575, 105], [671, 10, 575, 105], [671, 17, 575, 107], [671, 18, 575, 108], [671, 33, 576, 12], [671, 37, 576, 12, "_jsxDevRuntime"], [671, 51, 576, 12], [671, 52, 576, 12, "jsxDEV"], [671, 58, 576, 12], [671, 60, 576, 13, "_View"], [671, 65, 576, 13], [671, 66, 576, 13, "default"], [671, 73, 576, 17], [672, 12, 576, 18, "style"], [672, 17, 576, 23], [672, 19, 576, 25], [672, 20, 576, 26, "StyleSheet"], [672, 39, 576, 36], [672, 40, 576, 37, "absoluteFill"], [672, 52, 576, 49], [672, 54, 576, 51], [673, 14, 576, 53, "pointerEvents"], [673, 27, 576, 66], [673, 29, 576, 68], [674, 12, 576, 75], [674, 13, 576, 76], [674, 14, 576, 78], [675, 12, 576, 78, "children"], [675, 20, 576, 78], [675, 36, 578, 12], [675, 40, 578, 12, "_jsxDevRuntime"], [675, 54, 578, 12], [675, 55, 578, 12, "jsxDEV"], [675, 61, 578, 12], [675, 63, 578, 13, "_expoBlur"], [675, 72, 578, 13], [675, 73, 578, 13, "BlurView"], [675, 81, 578, 21], [676, 14, 578, 22, "intensity"], [676, 23, 578, 31], [676, 25, 578, 33], [676, 27, 578, 36], [677, 14, 578, 37, "tint"], [677, 18, 578, 41], [677, 20, 578, 42], [677, 26, 578, 48], [678, 14, 578, 49, "style"], [678, 19, 578, 54], [678, 21, 578, 56], [678, 22, 578, 57, "styles"], [678, 28, 578, 63], [678, 29, 578, 64, "blurZone"], [678, 37, 578, 72], [678, 39, 578, 74], [679, 16, 579, 14, "left"], [679, 20, 579, 18], [679, 22, 579, 20], [679, 23, 579, 21], [680, 16, 580, 14, "top"], [680, 19, 580, 17], [680, 21, 580, 19, "viewSize"], [680, 29, 580, 27], [680, 30, 580, 28, "height"], [680, 36, 580, 34], [680, 39, 580, 37], [680, 42, 580, 40], [681, 16, 581, 14, "width"], [681, 21, 581, 19], [681, 23, 581, 21, "viewSize"], [681, 31, 581, 29], [681, 32, 581, 30, "width"], [681, 37, 581, 35], [682, 16, 582, 14, "height"], [682, 22, 582, 20], [682, 24, 582, 22, "viewSize"], [682, 32, 582, 30], [682, 33, 582, 31, "height"], [682, 39, 582, 37], [682, 42, 582, 40], [682, 46, 582, 44], [683, 16, 583, 14, "borderRadius"], [683, 28, 583, 26], [683, 30, 583, 28], [684, 14, 584, 12], [684, 15, 584, 13], [685, 12, 584, 15], [686, 14, 584, 15, "fileName"], [686, 22, 584, 15], [686, 24, 584, 15, "_jsxFileName"], [686, 36, 584, 15], [687, 14, 584, 15, "lineNumber"], [687, 24, 584, 15], [688, 14, 584, 15, "columnNumber"], [688, 26, 584, 15], [689, 12, 584, 15], [689, 19, 584, 17], [689, 20, 584, 18], [689, 35, 586, 12], [689, 39, 586, 12, "_jsxDevRuntime"], [689, 53, 586, 12], [689, 54, 586, 12, "jsxDEV"], [689, 60, 586, 12], [689, 62, 586, 13, "_expoBlur"], [689, 71, 586, 13], [689, 72, 586, 13, "BlurView"], [689, 80, 586, 21], [690, 14, 586, 22, "intensity"], [690, 23, 586, 31], [690, 25, 586, 33], [690, 27, 586, 36], [691, 14, 586, 37, "tint"], [691, 18, 586, 41], [691, 20, 586, 42], [691, 26, 586, 48], [692, 14, 586, 49, "style"], [692, 19, 586, 54], [692, 21, 586, 56], [692, 22, 586, 57, "styles"], [692, 28, 586, 63], [692, 29, 586, 64, "blurZone"], [692, 37, 586, 72], [692, 39, 586, 74], [693, 16, 587, 14, "left"], [693, 20, 587, 18], [693, 22, 587, 20], [693, 23, 587, 21], [694, 16, 588, 14, "top"], [694, 19, 588, 17], [694, 21, 588, 19], [694, 22, 588, 20], [695, 16, 589, 14, "width"], [695, 21, 589, 19], [695, 23, 589, 21, "viewSize"], [695, 31, 589, 29], [695, 32, 589, 30, "width"], [695, 37, 589, 35], [696, 16, 590, 14, "height"], [696, 22, 590, 20], [696, 24, 590, 22, "viewSize"], [696, 32, 590, 30], [696, 33, 590, 31, "height"], [696, 39, 590, 37], [696, 42, 590, 40], [696, 45, 590, 43], [697, 16, 591, 14, "borderRadius"], [697, 28, 591, 26], [697, 30, 591, 28], [698, 14, 592, 12], [698, 15, 592, 13], [699, 12, 592, 15], [700, 14, 592, 15, "fileName"], [700, 22, 592, 15], [700, 24, 592, 15, "_jsxFileName"], [700, 36, 592, 15], [701, 14, 592, 15, "lineNumber"], [701, 24, 592, 15], [702, 14, 592, 15, "columnNumber"], [702, 26, 592, 15], [703, 12, 592, 15], [703, 19, 592, 17], [703, 20, 592, 18], [703, 35, 594, 12], [703, 39, 594, 12, "_jsxDevRuntime"], [703, 53, 594, 12], [703, 54, 594, 12, "jsxDEV"], [703, 60, 594, 12], [703, 62, 594, 13, "_expoBlur"], [703, 71, 594, 13], [703, 72, 594, 13, "BlurView"], [703, 80, 594, 21], [704, 14, 594, 22, "intensity"], [704, 23, 594, 31], [704, 25, 594, 33], [704, 27, 594, 36], [705, 14, 594, 37, "tint"], [705, 18, 594, 41], [705, 20, 594, 42], [705, 26, 594, 48], [706, 14, 594, 49, "style"], [706, 19, 594, 54], [706, 21, 594, 56], [706, 22, 594, 57, "styles"], [706, 28, 594, 63], [706, 29, 594, 64, "blurZone"], [706, 37, 594, 72], [706, 39, 594, 74], [707, 16, 595, 14, "left"], [707, 20, 595, 18], [707, 22, 595, 20, "viewSize"], [707, 30, 595, 28], [707, 31, 595, 29, "width"], [707, 36, 595, 34], [707, 39, 595, 37], [707, 42, 595, 40], [707, 45, 595, 44, "viewSize"], [707, 53, 595, 52], [707, 54, 595, 53, "width"], [707, 59, 595, 58], [707, 62, 595, 61], [707, 66, 595, 66], [708, 16, 596, 14, "top"], [708, 19, 596, 17], [708, 21, 596, 19, "viewSize"], [708, 29, 596, 27], [708, 30, 596, 28, "height"], [708, 36, 596, 34], [708, 39, 596, 37], [708, 43, 596, 41], [708, 46, 596, 45, "viewSize"], [708, 54, 596, 53], [708, 55, 596, 54, "width"], [708, 60, 596, 59], [708, 63, 596, 62], [708, 67, 596, 67], [709, 16, 597, 14, "width"], [709, 21, 597, 19], [709, 23, 597, 21, "viewSize"], [709, 31, 597, 29], [709, 32, 597, 30, "width"], [709, 37, 597, 35], [709, 40, 597, 38], [709, 43, 597, 41], [710, 16, 598, 14, "height"], [710, 22, 598, 20], [710, 24, 598, 22, "viewSize"], [710, 32, 598, 30], [710, 33, 598, 31, "width"], [710, 38, 598, 36], [710, 41, 598, 39], [710, 44, 598, 42], [711, 16, 599, 14, "borderRadius"], [711, 28, 599, 26], [711, 30, 599, 29, "viewSize"], [711, 38, 599, 37], [711, 39, 599, 38, "width"], [711, 44, 599, 43], [711, 47, 599, 46], [711, 50, 599, 49], [711, 53, 599, 53], [712, 14, 600, 12], [712, 15, 600, 13], [713, 12, 600, 15], [714, 14, 600, 15, "fileName"], [714, 22, 600, 15], [714, 24, 600, 15, "_jsxFileName"], [714, 36, 600, 15], [715, 14, 600, 15, "lineNumber"], [715, 24, 600, 15], [716, 14, 600, 15, "columnNumber"], [716, 26, 600, 15], [717, 12, 600, 15], [717, 19, 600, 17], [717, 20, 600, 18], [717, 35, 601, 12], [717, 39, 601, 12, "_jsxDevRuntime"], [717, 53, 601, 12], [717, 54, 601, 12, "jsxDEV"], [717, 60, 601, 12], [717, 62, 601, 13, "_expoBlur"], [717, 71, 601, 13], [717, 72, 601, 13, "BlurView"], [717, 80, 601, 21], [718, 14, 601, 22, "intensity"], [718, 23, 601, 31], [718, 25, 601, 33], [718, 27, 601, 36], [719, 14, 601, 37, "tint"], [719, 18, 601, 41], [719, 20, 601, 42], [719, 26, 601, 48], [720, 14, 601, 49, "style"], [720, 19, 601, 54], [720, 21, 601, 56], [720, 22, 601, 57, "styles"], [720, 28, 601, 63], [720, 29, 601, 64, "blurZone"], [720, 37, 601, 72], [720, 39, 601, 74], [721, 16, 602, 14, "left"], [721, 20, 602, 18], [721, 22, 602, 20, "viewSize"], [721, 30, 602, 28], [721, 31, 602, 29, "width"], [721, 36, 602, 34], [721, 39, 602, 37], [721, 42, 602, 40], [721, 45, 602, 44, "viewSize"], [721, 53, 602, 52], [721, 54, 602, 53, "width"], [721, 59, 602, 58], [721, 62, 602, 61], [721, 66, 602, 66], [722, 16, 603, 14, "top"], [722, 19, 603, 17], [722, 21, 603, 19, "viewSize"], [722, 29, 603, 27], [722, 30, 603, 28, "height"], [722, 36, 603, 34], [722, 39, 603, 37], [722, 42, 603, 40], [722, 45, 603, 44, "viewSize"], [722, 53, 603, 52], [722, 54, 603, 53, "width"], [722, 59, 603, 58], [722, 62, 603, 61], [722, 66, 603, 66], [723, 16, 604, 14, "width"], [723, 21, 604, 19], [723, 23, 604, 21, "viewSize"], [723, 31, 604, 29], [723, 32, 604, 30, "width"], [723, 37, 604, 35], [723, 40, 604, 38], [723, 43, 604, 41], [724, 16, 605, 14, "height"], [724, 22, 605, 20], [724, 24, 605, 22, "viewSize"], [724, 32, 605, 30], [724, 33, 605, 31, "width"], [724, 38, 605, 36], [724, 41, 605, 39], [724, 44, 605, 42], [725, 16, 606, 14, "borderRadius"], [725, 28, 606, 26], [725, 30, 606, 29, "viewSize"], [725, 38, 606, 37], [725, 39, 606, 38, "width"], [725, 44, 606, 43], [725, 47, 606, 46], [725, 50, 606, 49], [725, 53, 606, 53], [726, 14, 607, 12], [726, 15, 607, 13], [727, 12, 607, 15], [728, 14, 607, 15, "fileName"], [728, 22, 607, 15], [728, 24, 607, 15, "_jsxFileName"], [728, 36, 607, 15], [729, 14, 607, 15, "lineNumber"], [729, 24, 607, 15], [730, 14, 607, 15, "columnNumber"], [730, 26, 607, 15], [731, 12, 607, 15], [731, 19, 607, 17], [731, 20, 607, 18], [731, 35, 608, 12], [731, 39, 608, 12, "_jsxDevRuntime"], [731, 53, 608, 12], [731, 54, 608, 12, "jsxDEV"], [731, 60, 608, 12], [731, 62, 608, 13, "_expoBlur"], [731, 71, 608, 13], [731, 72, 608, 13, "BlurView"], [731, 80, 608, 21], [732, 14, 608, 22, "intensity"], [732, 23, 608, 31], [732, 25, 608, 33], [732, 27, 608, 36], [733, 14, 608, 37, "tint"], [733, 18, 608, 41], [733, 20, 608, 42], [733, 26, 608, 48], [734, 14, 608, 49, "style"], [734, 19, 608, 54], [734, 21, 608, 56], [734, 22, 608, 57, "styles"], [734, 28, 608, 63], [734, 29, 608, 64, "blurZone"], [734, 37, 608, 72], [734, 39, 608, 74], [735, 16, 609, 14, "left"], [735, 20, 609, 18], [735, 22, 609, 20, "viewSize"], [735, 30, 609, 28], [735, 31, 609, 29, "width"], [735, 36, 609, 34], [735, 39, 609, 37], [735, 42, 609, 40], [735, 45, 609, 44, "viewSize"], [735, 53, 609, 52], [735, 54, 609, 53, "width"], [735, 59, 609, 58], [735, 62, 609, 61], [735, 66, 609, 66], [736, 16, 610, 14, "top"], [736, 19, 610, 17], [736, 21, 610, 19, "viewSize"], [736, 29, 610, 27], [736, 30, 610, 28, "height"], [736, 36, 610, 34], [736, 39, 610, 37], [736, 42, 610, 40], [736, 45, 610, 44, "viewSize"], [736, 53, 610, 52], [736, 54, 610, 53, "width"], [736, 59, 610, 58], [736, 62, 610, 61], [736, 66, 610, 66], [737, 16, 611, 14, "width"], [737, 21, 611, 19], [737, 23, 611, 21, "viewSize"], [737, 31, 611, 29], [737, 32, 611, 30, "width"], [737, 37, 611, 35], [737, 40, 611, 38], [737, 43, 611, 41], [738, 16, 612, 14, "height"], [738, 22, 612, 20], [738, 24, 612, 22, "viewSize"], [738, 32, 612, 30], [738, 33, 612, 31, "width"], [738, 38, 612, 36], [738, 41, 612, 39], [738, 44, 612, 42], [739, 16, 613, 14, "borderRadius"], [739, 28, 613, 26], [739, 30, 613, 29, "viewSize"], [739, 38, 613, 37], [739, 39, 613, 38, "width"], [739, 44, 613, 43], [739, 47, 613, 46], [739, 50, 613, 49], [739, 53, 613, 53], [740, 14, 614, 12], [740, 15, 614, 13], [741, 12, 614, 15], [742, 14, 614, 15, "fileName"], [742, 22, 614, 15], [742, 24, 614, 15, "_jsxFileName"], [742, 36, 614, 15], [743, 14, 614, 15, "lineNumber"], [743, 24, 614, 15], [744, 14, 614, 15, "columnNumber"], [744, 26, 614, 15], [745, 12, 614, 15], [745, 19, 614, 17], [745, 20, 614, 18], [745, 22, 616, 13, "__DEV__"], [745, 29, 616, 20], [745, 46, 617, 14], [745, 50, 617, 14, "_jsxDevRuntime"], [745, 64, 617, 14], [745, 65, 617, 14, "jsxDEV"], [745, 71, 617, 14], [745, 73, 617, 15, "_View"], [745, 78, 617, 15], [745, 79, 617, 15, "default"], [745, 86, 617, 19], [746, 14, 617, 20, "style"], [746, 19, 617, 25], [746, 21, 617, 27, "styles"], [746, 27, 617, 33], [746, 28, 617, 34, "previewChip"], [746, 39, 617, 46], [747, 14, 617, 46, "children"], [747, 22, 617, 46], [747, 37, 618, 16], [747, 41, 618, 16, "_jsxDevRuntime"], [747, 55, 618, 16], [747, 56, 618, 16, "jsxDEV"], [747, 62, 618, 16], [747, 64, 618, 17, "_Text"], [747, 69, 618, 17], [747, 70, 618, 17, "default"], [747, 77, 618, 21], [748, 16, 618, 22, "style"], [748, 21, 618, 27], [748, 23, 618, 29, "styles"], [748, 29, 618, 35], [748, 30, 618, 36, "previewChipText"], [748, 45, 618, 52], [749, 16, 618, 52, "children"], [749, 24, 618, 52], [749, 26, 618, 53], [750, 14, 618, 73], [751, 16, 618, 73, "fileName"], [751, 24, 618, 73], [751, 26, 618, 73, "_jsxFileName"], [751, 38, 618, 73], [752, 16, 618, 73, "lineNumber"], [752, 26, 618, 73], [753, 16, 618, 73, "columnNumber"], [753, 28, 618, 73], [754, 14, 618, 73], [754, 21, 618, 79], [755, 12, 618, 80], [756, 14, 618, 80, "fileName"], [756, 22, 618, 80], [756, 24, 618, 80, "_jsxFileName"], [756, 36, 618, 80], [757, 14, 618, 80, "lineNumber"], [757, 24, 618, 80], [758, 14, 618, 80, "columnNumber"], [758, 26, 618, 80], [759, 12, 618, 80], [759, 19, 619, 20], [759, 20, 620, 13], [760, 10, 620, 13], [761, 12, 620, 13, "fileName"], [761, 20, 620, 13], [761, 22, 620, 13, "_jsxFileName"], [761, 34, 620, 13], [762, 12, 620, 13, "lineNumber"], [762, 22, 620, 13], [763, 12, 620, 13, "columnNumber"], [763, 24, 620, 13], [764, 10, 620, 13], [764, 17, 621, 18], [764, 18, 621, 19], [765, 8, 621, 19], [765, 23, 622, 12], [765, 24, 623, 9], [765, 26, 625, 9, "isCameraReady"], [765, 39, 625, 22], [765, 56, 626, 10], [765, 60, 626, 10, "_jsxDevRuntime"], [765, 74, 626, 10], [765, 75, 626, 10, "jsxDEV"], [765, 81, 626, 10], [765, 83, 626, 10, "_jsxDevRuntime"], [765, 97, 626, 10], [765, 98, 626, 10, "Fragment"], [765, 106, 626, 10], [766, 10, 626, 10, "children"], [766, 18, 626, 10], [766, 34, 628, 12], [766, 38, 628, 12, "_jsxDevRuntime"], [766, 52, 628, 12], [766, 53, 628, 12, "jsxDEV"], [766, 59, 628, 12], [766, 61, 628, 13, "_View"], [766, 66, 628, 13], [766, 67, 628, 13, "default"], [766, 74, 628, 17], [767, 12, 628, 18, "style"], [767, 17, 628, 23], [767, 19, 628, 25, "styles"], [767, 25, 628, 31], [767, 26, 628, 32, "headerOverlay"], [767, 39, 628, 46], [768, 12, 628, 46, "children"], [768, 20, 628, 46], [768, 35, 629, 14], [768, 39, 629, 14, "_jsxDevRuntime"], [768, 53, 629, 14], [768, 54, 629, 14, "jsxDEV"], [768, 60, 629, 14], [768, 62, 629, 15, "_View"], [768, 67, 629, 15], [768, 68, 629, 15, "default"], [768, 75, 629, 19], [769, 14, 629, 20, "style"], [769, 19, 629, 25], [769, 21, 629, 27, "styles"], [769, 27, 629, 33], [769, 28, 629, 34, "headerContent"], [769, 41, 629, 48], [770, 14, 629, 48, "children"], [770, 22, 629, 48], [770, 38, 630, 16], [770, 42, 630, 16, "_jsxDevRuntime"], [770, 56, 630, 16], [770, 57, 630, 16, "jsxDEV"], [770, 63, 630, 16], [770, 65, 630, 17, "_View"], [770, 70, 630, 17], [770, 71, 630, 17, "default"], [770, 78, 630, 21], [771, 16, 630, 22, "style"], [771, 21, 630, 27], [771, 23, 630, 29, "styles"], [771, 29, 630, 35], [771, 30, 630, 36, "headerLeft"], [771, 40, 630, 47], [772, 16, 630, 47, "children"], [772, 24, 630, 47], [772, 40, 631, 18], [772, 44, 631, 18, "_jsxDevRuntime"], [772, 58, 631, 18], [772, 59, 631, 18, "jsxDEV"], [772, 65, 631, 18], [772, 67, 631, 19, "_Text"], [772, 72, 631, 19], [772, 73, 631, 19, "default"], [772, 80, 631, 23], [773, 18, 631, 24, "style"], [773, 23, 631, 29], [773, 25, 631, 31, "styles"], [773, 31, 631, 37], [773, 32, 631, 38, "headerTitle"], [773, 43, 631, 50], [774, 18, 631, 50, "children"], [774, 26, 631, 50], [774, 28, 631, 51], [775, 16, 631, 62], [776, 18, 631, 62, "fileName"], [776, 26, 631, 62], [776, 28, 631, 62, "_jsxFileName"], [776, 40, 631, 62], [777, 18, 631, 62, "lineNumber"], [777, 28, 631, 62], [778, 18, 631, 62, "columnNumber"], [778, 30, 631, 62], [779, 16, 631, 62], [779, 23, 631, 68], [779, 24, 631, 69], [779, 39, 632, 18], [779, 43, 632, 18, "_jsxDevRuntime"], [779, 57, 632, 18], [779, 58, 632, 18, "jsxDEV"], [779, 64, 632, 18], [779, 66, 632, 19, "_View"], [779, 71, 632, 19], [779, 72, 632, 19, "default"], [779, 79, 632, 23], [780, 18, 632, 24, "style"], [780, 23, 632, 29], [780, 25, 632, 31, "styles"], [780, 31, 632, 37], [780, 32, 632, 38, "subtitleRow"], [780, 43, 632, 50], [781, 18, 632, 50, "children"], [781, 26, 632, 50], [781, 42, 633, 20], [781, 46, 633, 20, "_jsxDevRuntime"], [781, 60, 633, 20], [781, 61, 633, 20, "jsxDEV"], [781, 67, 633, 20], [781, 69, 633, 21, "_Text"], [781, 74, 633, 21], [781, 75, 633, 21, "default"], [781, 82, 633, 25], [782, 20, 633, 26, "style"], [782, 25, 633, 31], [782, 27, 633, 33, "styles"], [782, 33, 633, 39], [782, 34, 633, 40, "webIcon"], [782, 41, 633, 48], [783, 20, 633, 48, "children"], [783, 28, 633, 48], [783, 30, 633, 49], [784, 18, 633, 51], [785, 20, 633, 51, "fileName"], [785, 28, 633, 51], [785, 30, 633, 51, "_jsxFileName"], [785, 42, 633, 51], [786, 20, 633, 51, "lineNumber"], [786, 30, 633, 51], [787, 20, 633, 51, "columnNumber"], [787, 32, 633, 51], [788, 18, 633, 51], [788, 25, 633, 57], [788, 26, 633, 58], [788, 41, 634, 20], [788, 45, 634, 20, "_jsxDevRuntime"], [788, 59, 634, 20], [788, 60, 634, 20, "jsxDEV"], [788, 66, 634, 20], [788, 68, 634, 21, "_Text"], [788, 73, 634, 21], [788, 74, 634, 21, "default"], [788, 81, 634, 25], [789, 20, 634, 26, "style"], [789, 25, 634, 31], [789, 27, 634, 33, "styles"], [789, 33, 634, 39], [789, 34, 634, 40, "headerSubtitle"], [789, 48, 634, 55], [790, 20, 634, 55, "children"], [790, 28, 634, 55], [790, 30, 634, 56], [791, 18, 634, 71], [792, 20, 634, 71, "fileName"], [792, 28, 634, 71], [792, 30, 634, 71, "_jsxFileName"], [792, 42, 634, 71], [793, 20, 634, 71, "lineNumber"], [793, 30, 634, 71], [794, 20, 634, 71, "columnNumber"], [794, 32, 634, 71], [795, 18, 634, 71], [795, 25, 634, 77], [795, 26, 634, 78], [796, 16, 634, 78], [797, 18, 634, 78, "fileName"], [797, 26, 634, 78], [797, 28, 634, 78, "_jsxFileName"], [797, 40, 634, 78], [798, 18, 634, 78, "lineNumber"], [798, 28, 634, 78], [799, 18, 634, 78, "columnNumber"], [799, 30, 634, 78], [800, 16, 634, 78], [800, 23, 635, 24], [800, 24, 635, 25], [800, 26, 636, 19, "challengeCode"], [800, 39, 636, 32], [800, 56, 637, 20], [800, 60, 637, 20, "_jsxDevRuntime"], [800, 74, 637, 20], [800, 75, 637, 20, "jsxDEV"], [800, 81, 637, 20], [800, 83, 637, 21, "_View"], [800, 88, 637, 21], [800, 89, 637, 21, "default"], [800, 96, 637, 25], [801, 18, 637, 26, "style"], [801, 23, 637, 31], [801, 25, 637, 33, "styles"], [801, 31, 637, 39], [801, 32, 637, 40, "challengeRow"], [801, 44, 637, 53], [802, 18, 637, 53, "children"], [802, 26, 637, 53], [802, 42, 638, 22], [802, 46, 638, 22, "_jsxDevRuntime"], [802, 60, 638, 22], [802, 61, 638, 22, "jsxDEV"], [802, 67, 638, 22], [802, 69, 638, 23, "_lucideReactNative"], [802, 87, 638, 23], [802, 88, 638, 23, "Shield"], [802, 94, 638, 29], [803, 20, 638, 30, "size"], [803, 24, 638, 34], [803, 26, 638, 36], [803, 28, 638, 39], [804, 20, 638, 40, "color"], [804, 25, 638, 45], [804, 27, 638, 46], [805, 18, 638, 52], [806, 20, 638, 52, "fileName"], [806, 28, 638, 52], [806, 30, 638, 52, "_jsxFileName"], [806, 42, 638, 52], [807, 20, 638, 52, "lineNumber"], [807, 30, 638, 52], [808, 20, 638, 52, "columnNumber"], [808, 32, 638, 52], [809, 18, 638, 52], [809, 25, 638, 54], [809, 26, 638, 55], [809, 41, 639, 22], [809, 45, 639, 22, "_jsxDevRuntime"], [809, 59, 639, 22], [809, 60, 639, 22, "jsxDEV"], [809, 66, 639, 22], [809, 68, 639, 23, "_Text"], [809, 73, 639, 23], [809, 74, 639, 23, "default"], [809, 81, 639, 27], [810, 20, 639, 28, "style"], [810, 25, 639, 33], [810, 27, 639, 35, "styles"], [810, 33, 639, 41], [810, 34, 639, 42, "challengeCode"], [810, 47, 639, 56], [811, 20, 639, 56, "children"], [811, 28, 639, 56], [811, 30, 639, 58, "challengeCode"], [812, 18, 639, 71], [813, 20, 639, 71, "fileName"], [813, 28, 639, 71], [813, 30, 639, 71, "_jsxFileName"], [813, 42, 639, 71], [814, 20, 639, 71, "lineNumber"], [814, 30, 639, 71], [815, 20, 639, 71, "columnNumber"], [815, 32, 639, 71], [816, 18, 639, 71], [816, 25, 639, 78], [816, 26, 639, 79], [817, 16, 639, 79], [818, 18, 639, 79, "fileName"], [818, 26, 639, 79], [818, 28, 639, 79, "_jsxFileName"], [818, 40, 639, 79], [819, 18, 639, 79, "lineNumber"], [819, 28, 639, 79], [820, 18, 639, 79, "columnNumber"], [820, 30, 639, 79], [821, 16, 639, 79], [821, 23, 640, 26], [821, 24, 641, 19], [822, 14, 641, 19], [823, 16, 641, 19, "fileName"], [823, 24, 641, 19], [823, 26, 641, 19, "_jsxFileName"], [823, 38, 641, 19], [824, 16, 641, 19, "lineNumber"], [824, 26, 641, 19], [825, 16, 641, 19, "columnNumber"], [825, 28, 641, 19], [826, 14, 641, 19], [826, 21, 642, 22], [826, 22, 642, 23], [826, 37, 643, 16], [826, 41, 643, 16, "_jsxDevRuntime"], [826, 55, 643, 16], [826, 56, 643, 16, "jsxDEV"], [826, 62, 643, 16], [826, 64, 643, 17, "_TouchableOpacity"], [826, 81, 643, 17], [826, 82, 643, 17, "default"], [826, 89, 643, 33], [827, 16, 643, 34, "onPress"], [827, 23, 643, 41], [827, 25, 643, 43, "onCancel"], [827, 33, 643, 52], [828, 16, 643, 53, "style"], [828, 21, 643, 58], [828, 23, 643, 60, "styles"], [828, 29, 643, 66], [828, 30, 643, 67, "closeButton"], [828, 41, 643, 79], [829, 16, 643, 79, "children"], [829, 24, 643, 79], [829, 39, 644, 18], [829, 43, 644, 18, "_jsxDevRuntime"], [829, 57, 644, 18], [829, 58, 644, 18, "jsxDEV"], [829, 64, 644, 18], [829, 66, 644, 19, "_lucideReactNative"], [829, 84, 644, 19], [829, 85, 644, 19, "X"], [829, 86, 644, 20], [830, 18, 644, 21, "size"], [830, 22, 644, 25], [830, 24, 644, 27], [830, 26, 644, 30], [831, 18, 644, 31, "color"], [831, 23, 644, 36], [831, 25, 644, 37], [832, 16, 644, 43], [833, 18, 644, 43, "fileName"], [833, 26, 644, 43], [833, 28, 644, 43, "_jsxFileName"], [833, 40, 644, 43], [834, 18, 644, 43, "lineNumber"], [834, 28, 644, 43], [835, 18, 644, 43, "columnNumber"], [835, 30, 644, 43], [836, 16, 644, 43], [836, 23, 644, 45], [837, 14, 644, 46], [838, 16, 644, 46, "fileName"], [838, 24, 644, 46], [838, 26, 644, 46, "_jsxFileName"], [838, 38, 644, 46], [839, 16, 644, 46, "lineNumber"], [839, 26, 644, 46], [840, 16, 644, 46, "columnNumber"], [840, 28, 644, 46], [841, 14, 644, 46], [841, 21, 645, 34], [841, 22, 645, 35], [842, 12, 645, 35], [843, 14, 645, 35, "fileName"], [843, 22, 645, 35], [843, 24, 645, 35, "_jsxFileName"], [843, 36, 645, 35], [844, 14, 645, 35, "lineNumber"], [844, 24, 645, 35], [845, 14, 645, 35, "columnNumber"], [845, 26, 645, 35], [846, 12, 645, 35], [846, 19, 646, 20], [847, 10, 646, 21], [848, 12, 646, 21, "fileName"], [848, 20, 646, 21], [848, 22, 646, 21, "_jsxFileName"], [848, 34, 646, 21], [849, 12, 646, 21, "lineNumber"], [849, 22, 646, 21], [850, 12, 646, 21, "columnNumber"], [850, 24, 646, 21], [851, 10, 646, 21], [851, 17, 647, 18], [851, 18, 647, 19], [851, 33, 649, 12], [851, 37, 649, 12, "_jsxDevRuntime"], [851, 51, 649, 12], [851, 52, 649, 12, "jsxDEV"], [851, 58, 649, 12], [851, 60, 649, 13, "_View"], [851, 65, 649, 13], [851, 66, 649, 13, "default"], [851, 73, 649, 17], [852, 12, 649, 18, "style"], [852, 17, 649, 23], [852, 19, 649, 25, "styles"], [852, 25, 649, 31], [852, 26, 649, 32, "privacyNotice"], [852, 39, 649, 46], [853, 12, 649, 46, "children"], [853, 20, 649, 46], [853, 36, 650, 14], [853, 40, 650, 14, "_jsxDevRuntime"], [853, 54, 650, 14], [853, 55, 650, 14, "jsxDEV"], [853, 61, 650, 14], [853, 63, 650, 15, "_lucideReactNative"], [853, 81, 650, 15], [853, 82, 650, 15, "Shield"], [853, 88, 650, 21], [854, 14, 650, 22, "size"], [854, 18, 650, 26], [854, 20, 650, 28], [854, 22, 650, 31], [855, 14, 650, 32, "color"], [855, 19, 650, 37], [855, 21, 650, 38], [856, 12, 650, 47], [857, 14, 650, 47, "fileName"], [857, 22, 650, 47], [857, 24, 650, 47, "_jsxFileName"], [857, 36, 650, 47], [858, 14, 650, 47, "lineNumber"], [858, 24, 650, 47], [859, 14, 650, 47, "columnNumber"], [859, 26, 650, 47], [860, 12, 650, 47], [860, 19, 650, 49], [860, 20, 650, 50], [860, 35, 651, 14], [860, 39, 651, 14, "_jsxDevRuntime"], [860, 53, 651, 14], [860, 54, 651, 14, "jsxDEV"], [860, 60, 651, 14], [860, 62, 651, 15, "_Text"], [860, 67, 651, 15], [860, 68, 651, 15, "default"], [860, 75, 651, 19], [861, 14, 651, 20, "style"], [861, 19, 651, 25], [861, 21, 651, 27, "styles"], [861, 27, 651, 33], [861, 28, 651, 34, "privacyText"], [861, 39, 651, 46], [862, 14, 651, 46, "children"], [862, 22, 651, 46], [862, 24, 651, 47], [863, 12, 653, 14], [864, 14, 653, 14, "fileName"], [864, 22, 653, 14], [864, 24, 653, 14, "_jsxFileName"], [864, 36, 653, 14], [865, 14, 653, 14, "lineNumber"], [865, 24, 653, 14], [866, 14, 653, 14, "columnNumber"], [866, 26, 653, 14], [867, 12, 653, 14], [867, 19, 653, 20], [867, 20, 653, 21], [868, 10, 653, 21], [869, 12, 653, 21, "fileName"], [869, 20, 653, 21], [869, 22, 653, 21, "_jsxFileName"], [869, 34, 653, 21], [870, 12, 653, 21, "lineNumber"], [870, 22, 653, 21], [871, 12, 653, 21, "columnNumber"], [871, 24, 653, 21], [872, 10, 653, 21], [872, 17, 654, 18], [872, 18, 654, 19], [872, 33, 656, 12], [872, 37, 656, 12, "_jsxDevRuntime"], [872, 51, 656, 12], [872, 52, 656, 12, "jsxDEV"], [872, 58, 656, 12], [872, 60, 656, 13, "_View"], [872, 65, 656, 13], [872, 66, 656, 13, "default"], [872, 73, 656, 17], [873, 12, 656, 18, "style"], [873, 17, 656, 23], [873, 19, 656, 25, "styles"], [873, 25, 656, 31], [873, 26, 656, 32, "footer<PERSON><PERSON><PERSON>"], [873, 39, 656, 46], [874, 12, 656, 46, "children"], [874, 20, 656, 46], [874, 36, 657, 14], [874, 40, 657, 14, "_jsxDevRuntime"], [874, 54, 657, 14], [874, 55, 657, 14, "jsxDEV"], [874, 61, 657, 14], [874, 63, 657, 15, "_Text"], [874, 68, 657, 15], [874, 69, 657, 15, "default"], [874, 76, 657, 19], [875, 14, 657, 20, "style"], [875, 19, 657, 25], [875, 21, 657, 27, "styles"], [875, 27, 657, 33], [875, 28, 657, 34, "instruction"], [875, 39, 657, 46], [876, 14, 657, 46, "children"], [876, 22, 657, 46], [876, 24, 657, 47], [877, 12, 659, 14], [878, 14, 659, 14, "fileName"], [878, 22, 659, 14], [878, 24, 659, 14, "_jsxFileName"], [878, 36, 659, 14], [879, 14, 659, 14, "lineNumber"], [879, 24, 659, 14], [880, 14, 659, 14, "columnNumber"], [880, 26, 659, 14], [881, 12, 659, 14], [881, 19, 659, 20], [881, 20, 659, 21], [881, 35, 661, 14], [881, 39, 661, 14, "_jsxDevRuntime"], [881, 53, 661, 14], [881, 54, 661, 14, "jsxDEV"], [881, 60, 661, 14], [881, 62, 661, 15, "_TouchableOpacity"], [881, 79, 661, 15], [881, 80, 661, 15, "default"], [881, 87, 661, 31], [882, 14, 662, 16, "onPress"], [882, 21, 662, 23], [882, 23, 662, 25, "capturePhoto"], [882, 35, 662, 38], [883, 14, 663, 16, "disabled"], [883, 22, 663, 24], [883, 24, 663, 26, "processingState"], [883, 39, 663, 41], [883, 44, 663, 46], [883, 50, 663, 52], [883, 54, 663, 56], [883, 55, 663, 57, "isCameraReady"], [883, 68, 663, 71], [884, 14, 664, 16, "style"], [884, 19, 664, 21], [884, 21, 664, 23], [884, 22, 665, 18, "styles"], [884, 28, 665, 24], [884, 29, 665, 25, "shutterButton"], [884, 42, 665, 38], [884, 44, 666, 18, "processingState"], [884, 59, 666, 33], [884, 64, 666, 38], [884, 70, 666, 44], [884, 74, 666, 48, "styles"], [884, 80, 666, 54], [884, 81, 666, 55, "shutterButtonDisabled"], [884, 102, 666, 76], [884, 103, 667, 18], [885, 14, 667, 18, "children"], [885, 22, 667, 18], [885, 24, 669, 17, "processingState"], [885, 39, 669, 32], [885, 44, 669, 37], [885, 50, 669, 43], [885, 66, 670, 18], [885, 70, 670, 18, "_jsxDevRuntime"], [885, 84, 670, 18], [885, 85, 670, 18, "jsxDEV"], [885, 91, 670, 18], [885, 93, 670, 19, "_View"], [885, 98, 670, 19], [885, 99, 670, 19, "default"], [885, 106, 670, 23], [886, 16, 670, 24, "style"], [886, 21, 670, 29], [886, 23, 670, 31, "styles"], [886, 29, 670, 37], [886, 30, 670, 38, "shutterInner"], [887, 14, 670, 51], [888, 16, 670, 51, "fileName"], [888, 24, 670, 51], [888, 26, 670, 51, "_jsxFileName"], [888, 38, 670, 51], [889, 16, 670, 51, "lineNumber"], [889, 26, 670, 51], [890, 16, 670, 51, "columnNumber"], [890, 28, 670, 51], [891, 14, 670, 51], [891, 21, 670, 53], [891, 22, 670, 54], [891, 38, 672, 18], [891, 42, 672, 18, "_jsxDevRuntime"], [891, 56, 672, 18], [891, 57, 672, 18, "jsxDEV"], [891, 63, 672, 18], [891, 65, 672, 19, "_ActivityIndicator"], [891, 83, 672, 19], [891, 84, 672, 19, "default"], [891, 91, 672, 36], [892, 16, 672, 37, "size"], [892, 20, 672, 41], [892, 22, 672, 42], [892, 29, 672, 49], [893, 16, 672, 50, "color"], [893, 21, 672, 55], [893, 23, 672, 56], [894, 14, 672, 65], [895, 16, 672, 65, "fileName"], [895, 24, 672, 65], [895, 26, 672, 65, "_jsxFileName"], [895, 38, 672, 65], [896, 16, 672, 65, "lineNumber"], [896, 26, 672, 65], [897, 16, 672, 65, "columnNumber"], [897, 28, 672, 65], [898, 14, 672, 65], [898, 21, 672, 67], [899, 12, 673, 17], [900, 14, 673, 17, "fileName"], [900, 22, 673, 17], [900, 24, 673, 17, "_jsxFileName"], [900, 36, 673, 17], [901, 14, 673, 17, "lineNumber"], [901, 24, 673, 17], [902, 14, 673, 17, "columnNumber"], [902, 26, 673, 17], [903, 12, 673, 17], [903, 19, 674, 32], [903, 20, 674, 33], [903, 35, 675, 14], [903, 39, 675, 14, "_jsxDevRuntime"], [903, 53, 675, 14], [903, 54, 675, 14, "jsxDEV"], [903, 60, 675, 14], [903, 62, 675, 15, "_Text"], [903, 67, 675, 15], [903, 68, 675, 15, "default"], [903, 75, 675, 19], [904, 14, 675, 20, "style"], [904, 19, 675, 25], [904, 21, 675, 27, "styles"], [904, 27, 675, 33], [904, 28, 675, 34, "privacyNote"], [904, 39, 675, 46], [905, 14, 675, 46, "children"], [905, 22, 675, 46], [905, 24, 675, 47], [906, 12, 677, 14], [907, 14, 677, 14, "fileName"], [907, 22, 677, 14], [907, 24, 677, 14, "_jsxFileName"], [907, 36, 677, 14], [908, 14, 677, 14, "lineNumber"], [908, 24, 677, 14], [909, 14, 677, 14, "columnNumber"], [909, 26, 677, 14], [910, 12, 677, 14], [910, 19, 677, 20], [910, 20, 677, 21], [911, 10, 677, 21], [912, 12, 677, 21, "fileName"], [912, 20, 677, 21], [912, 22, 677, 21, "_jsxFileName"], [912, 34, 677, 21], [913, 12, 677, 21, "lineNumber"], [913, 22, 677, 21], [914, 12, 677, 21, "columnNumber"], [914, 24, 677, 21], [915, 10, 677, 21], [915, 17, 678, 18], [915, 18, 678, 19], [916, 8, 678, 19], [916, 23, 679, 12], [916, 24, 680, 9], [917, 6, 680, 9], [918, 8, 680, 9, "fileName"], [918, 16, 680, 9], [918, 18, 680, 9, "_jsxFileName"], [918, 30, 680, 9], [919, 8, 680, 9, "lineNumber"], [919, 18, 680, 9], [920, 8, 680, 9, "columnNumber"], [920, 20, 680, 9], [921, 6, 680, 9], [921, 13, 681, 12], [921, 14, 681, 13], [921, 29, 683, 6], [921, 33, 683, 6, "_jsxDevRuntime"], [921, 47, 683, 6], [921, 48, 683, 6, "jsxDEV"], [921, 54, 683, 6], [921, 56, 683, 7, "_Modal"], [921, 62, 683, 7], [921, 63, 683, 7, "default"], [921, 70, 683, 12], [922, 8, 684, 8, "visible"], [922, 15, 684, 15], [922, 17, 684, 17, "processingState"], [922, 32, 684, 32], [922, 37, 684, 37], [922, 43, 684, 43], [922, 47, 684, 47, "processingState"], [922, 62, 684, 62], [922, 67, 684, 67], [922, 74, 684, 75], [923, 8, 685, 8, "transparent"], [923, 19, 685, 19], [924, 8, 686, 8, "animationType"], [924, 21, 686, 21], [924, 23, 686, 22], [924, 29, 686, 28], [925, 8, 686, 28, "children"], [925, 16, 686, 28], [925, 31, 688, 8], [925, 35, 688, 8, "_jsxDevRuntime"], [925, 49, 688, 8], [925, 50, 688, 8, "jsxDEV"], [925, 56, 688, 8], [925, 58, 688, 9, "_View"], [925, 63, 688, 9], [925, 64, 688, 9, "default"], [925, 71, 688, 13], [926, 10, 688, 14, "style"], [926, 15, 688, 19], [926, 17, 688, 21, "styles"], [926, 23, 688, 27], [926, 24, 688, 28, "processingModal"], [926, 39, 688, 44], [927, 10, 688, 44, "children"], [927, 18, 688, 44], [927, 33, 689, 10], [927, 37, 689, 10, "_jsxDevRuntime"], [927, 51, 689, 10], [927, 52, 689, 10, "jsxDEV"], [927, 58, 689, 10], [927, 60, 689, 11, "_View"], [927, 65, 689, 11], [927, 66, 689, 11, "default"], [927, 73, 689, 15], [928, 12, 689, 16, "style"], [928, 17, 689, 21], [928, 19, 689, 23, "styles"], [928, 25, 689, 29], [928, 26, 689, 30, "processingContent"], [928, 43, 689, 48], [929, 12, 689, 48, "children"], [929, 20, 689, 48], [929, 36, 690, 12], [929, 40, 690, 12, "_jsxDevRuntime"], [929, 54, 690, 12], [929, 55, 690, 12, "jsxDEV"], [929, 61, 690, 12], [929, 63, 690, 13, "_ActivityIndicator"], [929, 81, 690, 13], [929, 82, 690, 13, "default"], [929, 89, 690, 30], [930, 14, 690, 31, "size"], [930, 18, 690, 35], [930, 20, 690, 36], [930, 27, 690, 43], [931, 14, 690, 44, "color"], [931, 19, 690, 49], [931, 21, 690, 50], [932, 12, 690, 59], [933, 14, 690, 59, "fileName"], [933, 22, 690, 59], [933, 24, 690, 59, "_jsxFileName"], [933, 36, 690, 59], [934, 14, 690, 59, "lineNumber"], [934, 24, 690, 59], [935, 14, 690, 59, "columnNumber"], [935, 26, 690, 59], [936, 12, 690, 59], [936, 19, 690, 61], [936, 20, 690, 62], [936, 35, 692, 12], [936, 39, 692, 12, "_jsxDevRuntime"], [936, 53, 692, 12], [936, 54, 692, 12, "jsxDEV"], [936, 60, 692, 12], [936, 62, 692, 13, "_Text"], [936, 67, 692, 13], [936, 68, 692, 13, "default"], [936, 75, 692, 17], [937, 14, 692, 18, "style"], [937, 19, 692, 23], [937, 21, 692, 25, "styles"], [937, 27, 692, 31], [937, 28, 692, 32, "processingTitle"], [937, 43, 692, 48], [938, 14, 692, 48, "children"], [938, 22, 692, 48], [938, 25, 693, 15, "processingState"], [938, 40, 693, 30], [938, 45, 693, 35], [938, 56, 693, 46], [938, 60, 693, 50], [938, 80, 693, 70], [938, 82, 694, 15, "processingState"], [938, 97, 694, 30], [938, 102, 694, 35], [938, 113, 694, 46], [938, 117, 694, 50], [938, 146, 694, 79], [938, 148, 695, 15, "processingState"], [938, 163, 695, 30], [938, 168, 695, 35], [938, 180, 695, 47], [938, 184, 695, 51], [938, 216, 695, 83], [938, 218, 696, 15, "processingState"], [938, 233, 696, 30], [938, 238, 696, 35], [938, 249, 696, 46], [938, 253, 696, 50], [938, 275, 696, 72], [939, 12, 696, 72], [940, 14, 696, 72, "fileName"], [940, 22, 696, 72], [940, 24, 696, 72, "_jsxFileName"], [940, 36, 696, 72], [941, 14, 696, 72, "lineNumber"], [941, 24, 696, 72], [942, 14, 696, 72, "columnNumber"], [942, 26, 696, 72], [943, 12, 696, 72], [943, 19, 697, 18], [943, 20, 697, 19], [943, 35, 698, 12], [943, 39, 698, 12, "_jsxDevRuntime"], [943, 53, 698, 12], [943, 54, 698, 12, "jsxDEV"], [943, 60, 698, 12], [943, 62, 698, 13, "_View"], [943, 67, 698, 13], [943, 68, 698, 13, "default"], [943, 75, 698, 17], [944, 14, 698, 18, "style"], [944, 19, 698, 23], [944, 21, 698, 25, "styles"], [944, 27, 698, 31], [944, 28, 698, 32, "progressBar"], [944, 39, 698, 44], [945, 14, 698, 44, "children"], [945, 22, 698, 44], [945, 37, 699, 14], [945, 41, 699, 14, "_jsxDevRuntime"], [945, 55, 699, 14], [945, 56, 699, 14, "jsxDEV"], [945, 62, 699, 14], [945, 64, 699, 15, "_View"], [945, 69, 699, 15], [945, 70, 699, 15, "default"], [945, 77, 699, 19], [946, 16, 700, 16, "style"], [946, 21, 700, 21], [946, 23, 700, 23], [946, 24, 701, 18, "styles"], [946, 30, 701, 24], [946, 31, 701, 25, "progressFill"], [946, 43, 701, 37], [946, 45, 702, 18], [947, 18, 702, 20, "width"], [947, 23, 702, 25], [947, 25, 702, 27], [947, 28, 702, 30, "processingProgress"], [947, 46, 702, 48], [948, 16, 702, 52], [948, 17, 702, 53], [949, 14, 703, 18], [950, 16, 703, 18, "fileName"], [950, 24, 703, 18], [950, 26, 703, 18, "_jsxFileName"], [950, 38, 703, 18], [951, 16, 703, 18, "lineNumber"], [951, 26, 703, 18], [952, 16, 703, 18, "columnNumber"], [952, 28, 703, 18], [953, 14, 703, 18], [953, 21, 704, 15], [954, 12, 704, 16], [955, 14, 704, 16, "fileName"], [955, 22, 704, 16], [955, 24, 704, 16, "_jsxFileName"], [955, 36, 704, 16], [956, 14, 704, 16, "lineNumber"], [956, 24, 704, 16], [957, 14, 704, 16, "columnNumber"], [957, 26, 704, 16], [958, 12, 704, 16], [958, 19, 705, 18], [958, 20, 705, 19], [958, 35, 706, 12], [958, 39, 706, 12, "_jsxDevRuntime"], [958, 53, 706, 12], [958, 54, 706, 12, "jsxDEV"], [958, 60, 706, 12], [958, 62, 706, 13, "_Text"], [958, 67, 706, 13], [958, 68, 706, 13, "default"], [958, 75, 706, 17], [959, 14, 706, 18, "style"], [959, 19, 706, 23], [959, 21, 706, 25, "styles"], [959, 27, 706, 31], [959, 28, 706, 32, "processingDescription"], [959, 49, 706, 54], [960, 14, 706, 54, "children"], [960, 22, 706, 54], [960, 25, 707, 15, "processingState"], [960, 40, 707, 30], [960, 45, 707, 35], [960, 56, 707, 46], [960, 60, 707, 50], [960, 89, 707, 79], [960, 91, 708, 15, "processingState"], [960, 106, 708, 30], [960, 111, 708, 35], [960, 122, 708, 46], [960, 126, 708, 50], [960, 164, 708, 88], [960, 166, 709, 15, "processingState"], [960, 181, 709, 30], [960, 186, 709, 35], [960, 198, 709, 47], [960, 202, 709, 51], [960, 247, 709, 96], [960, 249, 710, 15, "processingState"], [960, 264, 710, 30], [960, 269, 710, 35], [960, 280, 710, 46], [960, 284, 710, 50], [960, 325, 710, 91], [961, 12, 710, 91], [962, 14, 710, 91, "fileName"], [962, 22, 710, 91], [962, 24, 710, 91, "_jsxFileName"], [962, 36, 710, 91], [963, 14, 710, 91, "lineNumber"], [963, 24, 710, 91], [964, 14, 710, 91, "columnNumber"], [964, 26, 710, 91], [965, 12, 710, 91], [965, 19, 711, 18], [965, 20, 711, 19], [965, 22, 712, 13, "processingState"], [965, 37, 712, 28], [965, 42, 712, 33], [965, 53, 712, 44], [965, 70, 713, 14], [965, 74, 713, 14, "_jsxDevRuntime"], [965, 88, 713, 14], [965, 89, 713, 14, "jsxDEV"], [965, 95, 713, 14], [965, 97, 713, 15, "_lucideReactNative"], [965, 115, 713, 15], [965, 116, 713, 15, "CheckCircle"], [965, 127, 713, 26], [966, 14, 713, 27, "size"], [966, 18, 713, 31], [966, 20, 713, 33], [966, 22, 713, 36], [967, 14, 713, 37, "color"], [967, 19, 713, 42], [967, 21, 713, 43], [967, 30, 713, 52], [968, 14, 713, 53, "style"], [968, 19, 713, 58], [968, 21, 713, 60, "styles"], [968, 27, 713, 66], [968, 28, 713, 67, "successIcon"], [969, 12, 713, 79], [970, 14, 713, 79, "fileName"], [970, 22, 713, 79], [970, 24, 713, 79, "_jsxFileName"], [970, 36, 713, 79], [971, 14, 713, 79, "lineNumber"], [971, 24, 713, 79], [972, 14, 713, 79, "columnNumber"], [972, 26, 713, 79], [973, 12, 713, 79], [973, 19, 713, 81], [973, 20, 714, 13], [974, 10, 714, 13], [975, 12, 714, 13, "fileName"], [975, 20, 714, 13], [975, 22, 714, 13, "_jsxFileName"], [975, 34, 714, 13], [976, 12, 714, 13, "lineNumber"], [976, 22, 714, 13], [977, 12, 714, 13, "columnNumber"], [977, 24, 714, 13], [978, 10, 714, 13], [978, 17, 715, 16], [979, 8, 715, 17], [980, 10, 715, 17, "fileName"], [980, 18, 715, 17], [980, 20, 715, 17, "_jsxFileName"], [980, 32, 715, 17], [981, 10, 715, 17, "lineNumber"], [981, 20, 715, 17], [982, 10, 715, 17, "columnNumber"], [982, 22, 715, 17], [983, 8, 715, 17], [983, 15, 716, 14], [984, 6, 716, 15], [985, 8, 716, 15, "fileName"], [985, 16, 716, 15], [985, 18, 716, 15, "_jsxFileName"], [985, 30, 716, 15], [986, 8, 716, 15, "lineNumber"], [986, 18, 716, 15], [987, 8, 716, 15, "columnNumber"], [987, 20, 716, 15], [988, 6, 716, 15], [988, 13, 717, 13], [988, 14, 717, 14], [988, 29, 719, 6], [988, 33, 719, 6, "_jsxDevRuntime"], [988, 47, 719, 6], [988, 48, 719, 6, "jsxDEV"], [988, 54, 719, 6], [988, 56, 719, 7, "_Modal"], [988, 62, 719, 7], [988, 63, 719, 7, "default"], [988, 70, 719, 12], [989, 8, 720, 8, "visible"], [989, 15, 720, 15], [989, 17, 720, 17, "processingState"], [989, 32, 720, 32], [989, 37, 720, 37], [989, 44, 720, 45], [990, 8, 721, 8, "transparent"], [990, 19, 721, 19], [991, 8, 722, 8, "animationType"], [991, 21, 722, 21], [991, 23, 722, 22], [991, 29, 722, 28], [992, 8, 722, 28, "children"], [992, 16, 722, 28], [992, 31, 724, 8], [992, 35, 724, 8, "_jsxDevRuntime"], [992, 49, 724, 8], [992, 50, 724, 8, "jsxDEV"], [992, 56, 724, 8], [992, 58, 724, 9, "_View"], [992, 63, 724, 9], [992, 64, 724, 9, "default"], [992, 71, 724, 13], [993, 10, 724, 14, "style"], [993, 15, 724, 19], [993, 17, 724, 21, "styles"], [993, 23, 724, 27], [993, 24, 724, 28, "processingModal"], [993, 39, 724, 44], [994, 10, 724, 44, "children"], [994, 18, 724, 44], [994, 33, 725, 10], [994, 37, 725, 10, "_jsxDevRuntime"], [994, 51, 725, 10], [994, 52, 725, 10, "jsxDEV"], [994, 58, 725, 10], [994, 60, 725, 11, "_View"], [994, 65, 725, 11], [994, 66, 725, 11, "default"], [994, 73, 725, 15], [995, 12, 725, 16, "style"], [995, 17, 725, 21], [995, 19, 725, 23, "styles"], [995, 25, 725, 29], [995, 26, 725, 30, "errorContent"], [995, 38, 725, 43], [996, 12, 725, 43, "children"], [996, 20, 725, 43], [996, 36, 726, 12], [996, 40, 726, 12, "_jsxDevRuntime"], [996, 54, 726, 12], [996, 55, 726, 12, "jsxDEV"], [996, 61, 726, 12], [996, 63, 726, 13, "_lucideReactNative"], [996, 81, 726, 13], [996, 82, 726, 13, "X"], [996, 83, 726, 14], [997, 14, 726, 15, "size"], [997, 18, 726, 19], [997, 20, 726, 21], [997, 22, 726, 24], [998, 14, 726, 25, "color"], [998, 19, 726, 30], [998, 21, 726, 31], [999, 12, 726, 40], [1000, 14, 726, 40, "fileName"], [1000, 22, 726, 40], [1000, 24, 726, 40, "_jsxFileName"], [1000, 36, 726, 40], [1001, 14, 726, 40, "lineNumber"], [1001, 24, 726, 40], [1002, 14, 726, 40, "columnNumber"], [1002, 26, 726, 40], [1003, 12, 726, 40], [1003, 19, 726, 42], [1003, 20, 726, 43], [1003, 35, 727, 12], [1003, 39, 727, 12, "_jsxDevRuntime"], [1003, 53, 727, 12], [1003, 54, 727, 12, "jsxDEV"], [1003, 60, 727, 12], [1003, 62, 727, 13, "_Text"], [1003, 67, 727, 13], [1003, 68, 727, 13, "default"], [1003, 75, 727, 17], [1004, 14, 727, 18, "style"], [1004, 19, 727, 23], [1004, 21, 727, 25, "styles"], [1004, 27, 727, 31], [1004, 28, 727, 32, "errorTitle"], [1004, 38, 727, 43], [1005, 14, 727, 43, "children"], [1005, 22, 727, 43], [1005, 24, 727, 44], [1006, 12, 727, 61], [1007, 14, 727, 61, "fileName"], [1007, 22, 727, 61], [1007, 24, 727, 61, "_jsxFileName"], [1007, 36, 727, 61], [1008, 14, 727, 61, "lineNumber"], [1008, 24, 727, 61], [1009, 14, 727, 61, "columnNumber"], [1009, 26, 727, 61], [1010, 12, 727, 61], [1010, 19, 727, 67], [1010, 20, 727, 68], [1010, 35, 728, 12], [1010, 39, 728, 12, "_jsxDevRuntime"], [1010, 53, 728, 12], [1010, 54, 728, 12, "jsxDEV"], [1010, 60, 728, 12], [1010, 62, 728, 13, "_Text"], [1010, 67, 728, 13], [1010, 68, 728, 13, "default"], [1010, 75, 728, 17], [1011, 14, 728, 18, "style"], [1011, 19, 728, 23], [1011, 21, 728, 25, "styles"], [1011, 27, 728, 31], [1011, 28, 728, 32, "errorMessage"], [1011, 40, 728, 45], [1012, 14, 728, 45, "children"], [1012, 22, 728, 45], [1012, 24, 728, 47, "errorMessage"], [1013, 12, 728, 59], [1014, 14, 728, 59, "fileName"], [1014, 22, 728, 59], [1014, 24, 728, 59, "_jsxFileName"], [1014, 36, 728, 59], [1015, 14, 728, 59, "lineNumber"], [1015, 24, 728, 59], [1016, 14, 728, 59, "columnNumber"], [1016, 26, 728, 59], [1017, 12, 728, 59], [1017, 19, 728, 66], [1017, 20, 728, 67], [1017, 35, 729, 12], [1017, 39, 729, 12, "_jsxDevRuntime"], [1017, 53, 729, 12], [1017, 54, 729, 12, "jsxDEV"], [1017, 60, 729, 12], [1017, 62, 729, 13, "_TouchableOpacity"], [1017, 79, 729, 13], [1017, 80, 729, 13, "default"], [1017, 87, 729, 29], [1018, 14, 730, 14, "onPress"], [1018, 21, 730, 21], [1018, 23, 730, 23, "retryCapture"], [1018, 35, 730, 36], [1019, 14, 731, 14, "style"], [1019, 19, 731, 19], [1019, 21, 731, 21, "styles"], [1019, 27, 731, 27], [1019, 28, 731, 28, "primaryButton"], [1019, 41, 731, 42], [1020, 14, 731, 42, "children"], [1020, 22, 731, 42], [1020, 37, 733, 14], [1020, 41, 733, 14, "_jsxDevRuntime"], [1020, 55, 733, 14], [1020, 56, 733, 14, "jsxDEV"], [1020, 62, 733, 14], [1020, 64, 733, 15, "_Text"], [1020, 69, 733, 15], [1020, 70, 733, 15, "default"], [1020, 77, 733, 19], [1021, 16, 733, 20, "style"], [1021, 21, 733, 25], [1021, 23, 733, 27, "styles"], [1021, 29, 733, 33], [1021, 30, 733, 34, "primaryButtonText"], [1021, 47, 733, 52], [1022, 16, 733, 52, "children"], [1022, 24, 733, 52], [1022, 26, 733, 53], [1023, 14, 733, 62], [1024, 16, 733, 62, "fileName"], [1024, 24, 733, 62], [1024, 26, 733, 62, "_jsxFileName"], [1024, 38, 733, 62], [1025, 16, 733, 62, "lineNumber"], [1025, 26, 733, 62], [1026, 16, 733, 62, "columnNumber"], [1026, 28, 733, 62], [1027, 14, 733, 62], [1027, 21, 733, 68], [1028, 12, 733, 69], [1029, 14, 733, 69, "fileName"], [1029, 22, 733, 69], [1029, 24, 733, 69, "_jsxFileName"], [1029, 36, 733, 69], [1030, 14, 733, 69, "lineNumber"], [1030, 24, 733, 69], [1031, 14, 733, 69, "columnNumber"], [1031, 26, 733, 69], [1032, 12, 733, 69], [1032, 19, 734, 30], [1032, 20, 734, 31], [1032, 35, 735, 12], [1032, 39, 735, 12, "_jsxDevRuntime"], [1032, 53, 735, 12], [1032, 54, 735, 12, "jsxDEV"], [1032, 60, 735, 12], [1032, 62, 735, 13, "_TouchableOpacity"], [1032, 79, 735, 13], [1032, 80, 735, 13, "default"], [1032, 87, 735, 29], [1033, 14, 736, 14, "onPress"], [1033, 21, 736, 21], [1033, 23, 736, 23, "onCancel"], [1033, 31, 736, 32], [1034, 14, 737, 14, "style"], [1034, 19, 737, 19], [1034, 21, 737, 21, "styles"], [1034, 27, 737, 27], [1034, 28, 737, 28, "secondaryButton"], [1034, 43, 737, 44], [1035, 14, 737, 44, "children"], [1035, 22, 737, 44], [1035, 37, 739, 14], [1035, 41, 739, 14, "_jsxDevRuntime"], [1035, 55, 739, 14], [1035, 56, 739, 14, "jsxDEV"], [1035, 62, 739, 14], [1035, 64, 739, 15, "_Text"], [1035, 69, 739, 15], [1035, 70, 739, 15, "default"], [1035, 77, 739, 19], [1036, 16, 739, 20, "style"], [1036, 21, 739, 25], [1036, 23, 739, 27, "styles"], [1036, 29, 739, 33], [1036, 30, 739, 34, "secondaryButtonText"], [1036, 49, 739, 54], [1037, 16, 739, 54, "children"], [1037, 24, 739, 54], [1037, 26, 739, 55], [1038, 14, 739, 61], [1039, 16, 739, 61, "fileName"], [1039, 24, 739, 61], [1039, 26, 739, 61, "_jsxFileName"], [1039, 38, 739, 61], [1040, 16, 739, 61, "lineNumber"], [1040, 26, 739, 61], [1041, 16, 739, 61, "columnNumber"], [1041, 28, 739, 61], [1042, 14, 739, 61], [1042, 21, 739, 67], [1043, 12, 739, 68], [1044, 14, 739, 68, "fileName"], [1044, 22, 739, 68], [1044, 24, 739, 68, "_jsxFileName"], [1044, 36, 739, 68], [1045, 14, 739, 68, "lineNumber"], [1045, 24, 739, 68], [1046, 14, 739, 68, "columnNumber"], [1046, 26, 739, 68], [1047, 12, 739, 68], [1047, 19, 740, 30], [1047, 20, 740, 31], [1048, 10, 740, 31], [1049, 12, 740, 31, "fileName"], [1049, 20, 740, 31], [1049, 22, 740, 31, "_jsxFileName"], [1049, 34, 740, 31], [1050, 12, 740, 31, "lineNumber"], [1050, 22, 740, 31], [1051, 12, 740, 31, "columnNumber"], [1051, 24, 740, 31], [1052, 10, 740, 31], [1052, 17, 741, 16], [1053, 8, 741, 17], [1054, 10, 741, 17, "fileName"], [1054, 18, 741, 17], [1054, 20, 741, 17, "_jsxFileName"], [1054, 32, 741, 17], [1055, 10, 741, 17, "lineNumber"], [1055, 20, 741, 17], [1056, 10, 741, 17, "columnNumber"], [1056, 22, 741, 17], [1057, 8, 741, 17], [1057, 15, 742, 14], [1058, 6, 742, 15], [1059, 8, 742, 15, "fileName"], [1059, 16, 742, 15], [1059, 18, 742, 15, "_jsxFileName"], [1059, 30, 742, 15], [1060, 8, 742, 15, "lineNumber"], [1060, 18, 742, 15], [1061, 8, 742, 15, "columnNumber"], [1061, 20, 742, 15], [1062, 6, 742, 15], [1062, 13, 743, 13], [1062, 14, 743, 14], [1063, 4, 743, 14], [1064, 6, 743, 14, "fileName"], [1064, 14, 743, 14], [1064, 16, 743, 14, "_jsxFileName"], [1064, 28, 743, 14], [1065, 6, 743, 14, "lineNumber"], [1065, 16, 743, 14], [1066, 6, 743, 14, "columnNumber"], [1066, 18, 743, 14], [1067, 4, 743, 14], [1067, 11, 744, 10], [1067, 12, 744, 11], [1068, 2, 746, 0], [1069, 2, 746, 1, "_s"], [1069, 4, 746, 1], [1069, 5, 51, 24, "EchoCameraWeb"], [1069, 18, 51, 37], [1070, 4, 51, 37], [1070, 12, 58, 42, "useCameraPermissions"], [1070, 44, 58, 62], [1070, 46, 72, 19, "useUpload"], [1070, 64, 72, 28], [1071, 2, 72, 28], [1072, 2, 72, 28, "_c"], [1072, 4, 72, 28], [1072, 7, 51, 24, "EchoCameraWeb"], [1072, 20, 51, 37], [1073, 2, 747, 0], [1073, 8, 747, 6, "styles"], [1073, 14, 747, 12], [1073, 17, 747, 15, "StyleSheet"], [1073, 36, 747, 25], [1073, 37, 747, 26, "create"], [1073, 43, 747, 32], [1073, 44, 747, 33], [1074, 4, 748, 2, "container"], [1074, 13, 748, 11], [1074, 15, 748, 13], [1075, 6, 749, 4, "flex"], [1075, 10, 749, 8], [1075, 12, 749, 10], [1075, 13, 749, 11], [1076, 6, 750, 4, "backgroundColor"], [1076, 21, 750, 19], [1076, 23, 750, 21], [1077, 4, 751, 2], [1077, 5, 751, 3], [1078, 4, 752, 2, "cameraContainer"], [1078, 19, 752, 17], [1078, 21, 752, 19], [1079, 6, 753, 4, "flex"], [1079, 10, 753, 8], [1079, 12, 753, 10], [1079, 13, 753, 11], [1080, 6, 754, 4, "max<PERSON><PERSON><PERSON>"], [1080, 14, 754, 12], [1080, 16, 754, 14], [1080, 19, 754, 17], [1081, 6, 755, 4, "alignSelf"], [1081, 15, 755, 13], [1081, 17, 755, 15], [1081, 25, 755, 23], [1082, 6, 756, 4, "width"], [1082, 11, 756, 9], [1082, 13, 756, 11], [1083, 4, 757, 2], [1083, 5, 757, 3], [1084, 4, 758, 2, "camera"], [1084, 10, 758, 8], [1084, 12, 758, 10], [1085, 6, 759, 4, "flex"], [1085, 10, 759, 8], [1085, 12, 759, 10], [1086, 4, 760, 2], [1086, 5, 760, 3], [1087, 4, 761, 2, "headerOverlay"], [1087, 17, 761, 15], [1087, 19, 761, 17], [1088, 6, 762, 4, "position"], [1088, 14, 762, 12], [1088, 16, 762, 14], [1088, 26, 762, 24], [1089, 6, 763, 4, "top"], [1089, 9, 763, 7], [1089, 11, 763, 9], [1089, 12, 763, 10], [1090, 6, 764, 4, "left"], [1090, 10, 764, 8], [1090, 12, 764, 10], [1090, 13, 764, 11], [1091, 6, 765, 4, "right"], [1091, 11, 765, 9], [1091, 13, 765, 11], [1091, 14, 765, 12], [1092, 6, 766, 4, "backgroundColor"], [1092, 21, 766, 19], [1092, 23, 766, 21], [1092, 36, 766, 34], [1093, 6, 767, 4, "paddingTop"], [1093, 16, 767, 14], [1093, 18, 767, 16], [1093, 20, 767, 18], [1094, 6, 768, 4, "paddingHorizontal"], [1094, 23, 768, 21], [1094, 25, 768, 23], [1094, 27, 768, 25], [1095, 6, 769, 4, "paddingBottom"], [1095, 19, 769, 17], [1095, 21, 769, 19], [1096, 4, 770, 2], [1096, 5, 770, 3], [1097, 4, 771, 2, "headerContent"], [1097, 17, 771, 15], [1097, 19, 771, 17], [1098, 6, 772, 4, "flexDirection"], [1098, 19, 772, 17], [1098, 21, 772, 19], [1098, 26, 772, 24], [1099, 6, 773, 4, "justifyContent"], [1099, 20, 773, 18], [1099, 22, 773, 20], [1099, 37, 773, 35], [1100, 6, 774, 4, "alignItems"], [1100, 16, 774, 14], [1100, 18, 774, 16], [1101, 4, 775, 2], [1101, 5, 775, 3], [1102, 4, 776, 2, "headerLeft"], [1102, 14, 776, 12], [1102, 16, 776, 14], [1103, 6, 777, 4, "flex"], [1103, 10, 777, 8], [1103, 12, 777, 10], [1104, 4, 778, 2], [1104, 5, 778, 3], [1105, 4, 779, 2, "headerTitle"], [1105, 15, 779, 13], [1105, 17, 779, 15], [1106, 6, 780, 4, "fontSize"], [1106, 14, 780, 12], [1106, 16, 780, 14], [1106, 18, 780, 16], [1107, 6, 781, 4, "fontWeight"], [1107, 16, 781, 14], [1107, 18, 781, 16], [1107, 23, 781, 21], [1108, 6, 782, 4, "color"], [1108, 11, 782, 9], [1108, 13, 782, 11], [1108, 19, 782, 17], [1109, 6, 783, 4, "marginBottom"], [1109, 18, 783, 16], [1109, 20, 783, 18], [1110, 4, 784, 2], [1110, 5, 784, 3], [1111, 4, 785, 2, "subtitleRow"], [1111, 15, 785, 13], [1111, 17, 785, 15], [1112, 6, 786, 4, "flexDirection"], [1112, 19, 786, 17], [1112, 21, 786, 19], [1112, 26, 786, 24], [1113, 6, 787, 4, "alignItems"], [1113, 16, 787, 14], [1113, 18, 787, 16], [1113, 26, 787, 24], [1114, 6, 788, 4, "marginBottom"], [1114, 18, 788, 16], [1114, 20, 788, 18], [1115, 4, 789, 2], [1115, 5, 789, 3], [1116, 4, 790, 2, "webIcon"], [1116, 11, 790, 9], [1116, 13, 790, 11], [1117, 6, 791, 4, "fontSize"], [1117, 14, 791, 12], [1117, 16, 791, 14], [1117, 18, 791, 16], [1118, 6, 792, 4, "marginRight"], [1118, 17, 792, 15], [1118, 19, 792, 17], [1119, 4, 793, 2], [1119, 5, 793, 3], [1120, 4, 794, 2, "headerSubtitle"], [1120, 18, 794, 16], [1120, 20, 794, 18], [1121, 6, 795, 4, "fontSize"], [1121, 14, 795, 12], [1121, 16, 795, 14], [1121, 18, 795, 16], [1122, 6, 796, 4, "color"], [1122, 11, 796, 9], [1122, 13, 796, 11], [1122, 19, 796, 17], [1123, 6, 797, 4, "opacity"], [1123, 13, 797, 11], [1123, 15, 797, 13], [1124, 4, 798, 2], [1124, 5, 798, 3], [1125, 4, 799, 2, "challengeRow"], [1125, 16, 799, 14], [1125, 18, 799, 16], [1126, 6, 800, 4, "flexDirection"], [1126, 19, 800, 17], [1126, 21, 800, 19], [1126, 26, 800, 24], [1127, 6, 801, 4, "alignItems"], [1127, 16, 801, 14], [1127, 18, 801, 16], [1128, 4, 802, 2], [1128, 5, 802, 3], [1129, 4, 803, 2, "challengeCode"], [1129, 17, 803, 15], [1129, 19, 803, 17], [1130, 6, 804, 4, "fontSize"], [1130, 14, 804, 12], [1130, 16, 804, 14], [1130, 18, 804, 16], [1131, 6, 805, 4, "color"], [1131, 11, 805, 9], [1131, 13, 805, 11], [1131, 19, 805, 17], [1132, 6, 806, 4, "marginLeft"], [1132, 16, 806, 14], [1132, 18, 806, 16], [1132, 19, 806, 17], [1133, 6, 807, 4, "fontFamily"], [1133, 16, 807, 14], [1133, 18, 807, 16], [1134, 4, 808, 2], [1134, 5, 808, 3], [1135, 4, 809, 2, "closeButton"], [1135, 15, 809, 13], [1135, 17, 809, 15], [1136, 6, 810, 4, "padding"], [1136, 13, 810, 11], [1136, 15, 810, 13], [1137, 4, 811, 2], [1137, 5, 811, 3], [1138, 4, 812, 2, "privacyNotice"], [1138, 17, 812, 15], [1138, 19, 812, 17], [1139, 6, 813, 4, "position"], [1139, 14, 813, 12], [1139, 16, 813, 14], [1139, 26, 813, 24], [1140, 6, 814, 4, "top"], [1140, 9, 814, 7], [1140, 11, 814, 9], [1140, 14, 814, 12], [1141, 6, 815, 4, "left"], [1141, 10, 815, 8], [1141, 12, 815, 10], [1141, 14, 815, 12], [1142, 6, 816, 4, "right"], [1142, 11, 816, 9], [1142, 13, 816, 11], [1142, 15, 816, 13], [1143, 6, 817, 4, "backgroundColor"], [1143, 21, 817, 19], [1143, 23, 817, 21], [1143, 48, 817, 46], [1144, 6, 818, 4, "borderRadius"], [1144, 18, 818, 16], [1144, 20, 818, 18], [1144, 21, 818, 19], [1145, 6, 819, 4, "padding"], [1145, 13, 819, 11], [1145, 15, 819, 13], [1145, 17, 819, 15], [1146, 6, 820, 4, "flexDirection"], [1146, 19, 820, 17], [1146, 21, 820, 19], [1146, 26, 820, 24], [1147, 6, 821, 4, "alignItems"], [1147, 16, 821, 14], [1147, 18, 821, 16], [1148, 4, 822, 2], [1148, 5, 822, 3], [1149, 4, 823, 2, "privacyText"], [1149, 15, 823, 13], [1149, 17, 823, 15], [1150, 6, 824, 4, "color"], [1150, 11, 824, 9], [1150, 13, 824, 11], [1150, 19, 824, 17], [1151, 6, 825, 4, "fontSize"], [1151, 14, 825, 12], [1151, 16, 825, 14], [1151, 18, 825, 16], [1152, 6, 826, 4, "marginLeft"], [1152, 16, 826, 14], [1152, 18, 826, 16], [1152, 19, 826, 17], [1153, 6, 827, 4, "flex"], [1153, 10, 827, 8], [1153, 12, 827, 10], [1154, 4, 828, 2], [1154, 5, 828, 3], [1155, 4, 829, 2, "footer<PERSON><PERSON><PERSON>"], [1155, 17, 829, 15], [1155, 19, 829, 17], [1156, 6, 830, 4, "position"], [1156, 14, 830, 12], [1156, 16, 830, 14], [1156, 26, 830, 24], [1157, 6, 831, 4, "bottom"], [1157, 12, 831, 10], [1157, 14, 831, 12], [1157, 15, 831, 13], [1158, 6, 832, 4, "left"], [1158, 10, 832, 8], [1158, 12, 832, 10], [1158, 13, 832, 11], [1159, 6, 833, 4, "right"], [1159, 11, 833, 9], [1159, 13, 833, 11], [1159, 14, 833, 12], [1160, 6, 834, 4, "backgroundColor"], [1160, 21, 834, 19], [1160, 23, 834, 21], [1160, 36, 834, 34], [1161, 6, 835, 4, "paddingBottom"], [1161, 19, 835, 17], [1161, 21, 835, 19], [1161, 23, 835, 21], [1162, 6, 836, 4, "paddingTop"], [1162, 16, 836, 14], [1162, 18, 836, 16], [1162, 20, 836, 18], [1163, 6, 837, 4, "alignItems"], [1163, 16, 837, 14], [1163, 18, 837, 16], [1164, 4, 838, 2], [1164, 5, 838, 3], [1165, 4, 839, 2, "instruction"], [1165, 15, 839, 13], [1165, 17, 839, 15], [1166, 6, 840, 4, "fontSize"], [1166, 14, 840, 12], [1166, 16, 840, 14], [1166, 18, 840, 16], [1167, 6, 841, 4, "color"], [1167, 11, 841, 9], [1167, 13, 841, 11], [1167, 19, 841, 17], [1168, 6, 842, 4, "marginBottom"], [1168, 18, 842, 16], [1168, 20, 842, 18], [1169, 4, 843, 2], [1169, 5, 843, 3], [1170, 4, 844, 2, "shutterButton"], [1170, 17, 844, 15], [1170, 19, 844, 17], [1171, 6, 845, 4, "width"], [1171, 11, 845, 9], [1171, 13, 845, 11], [1171, 15, 845, 13], [1172, 6, 846, 4, "height"], [1172, 12, 846, 10], [1172, 14, 846, 12], [1172, 16, 846, 14], [1173, 6, 847, 4, "borderRadius"], [1173, 18, 847, 16], [1173, 20, 847, 18], [1173, 22, 847, 20], [1174, 6, 848, 4, "backgroundColor"], [1174, 21, 848, 19], [1174, 23, 848, 21], [1174, 29, 848, 27], [1175, 6, 849, 4, "justifyContent"], [1175, 20, 849, 18], [1175, 22, 849, 20], [1175, 30, 849, 28], [1176, 6, 850, 4, "alignItems"], [1176, 16, 850, 14], [1176, 18, 850, 16], [1176, 26, 850, 24], [1177, 6, 851, 4, "marginBottom"], [1177, 18, 851, 16], [1177, 20, 851, 18], [1177, 22, 851, 20], [1178, 6, 852, 4], [1178, 9, 852, 7, "Platform"], [1178, 26, 852, 15], [1178, 27, 852, 16, "select"], [1178, 33, 852, 22], [1178, 34, 852, 23], [1179, 8, 853, 6, "ios"], [1179, 11, 853, 9], [1179, 13, 853, 11], [1180, 10, 854, 8, "shadowColor"], [1180, 21, 854, 19], [1180, 23, 854, 21], [1180, 32, 854, 30], [1181, 10, 855, 8, "shadowOffset"], [1181, 22, 855, 20], [1181, 24, 855, 22], [1182, 12, 855, 24, "width"], [1182, 17, 855, 29], [1182, 19, 855, 31], [1182, 20, 855, 32], [1183, 12, 855, 34, "height"], [1183, 18, 855, 40], [1183, 20, 855, 42], [1184, 10, 855, 44], [1184, 11, 855, 45], [1185, 10, 856, 8, "shadowOpacity"], [1185, 23, 856, 21], [1185, 25, 856, 23], [1185, 28, 856, 26], [1186, 10, 857, 8, "shadowRadius"], [1186, 22, 857, 20], [1186, 24, 857, 22], [1187, 8, 858, 6], [1187, 9, 858, 7], [1188, 8, 859, 6, "android"], [1188, 15, 859, 13], [1188, 17, 859, 15], [1189, 10, 860, 8, "elevation"], [1189, 19, 860, 17], [1189, 21, 860, 19], [1190, 8, 861, 6], [1190, 9, 861, 7], [1191, 8, 862, 6, "web"], [1191, 11, 862, 9], [1191, 13, 862, 11], [1192, 10, 863, 8, "boxShadow"], [1192, 19, 863, 17], [1192, 21, 863, 19], [1193, 8, 864, 6], [1194, 6, 865, 4], [1194, 7, 865, 5], [1195, 4, 866, 2], [1195, 5, 866, 3], [1196, 4, 867, 2, "shutterButtonDisabled"], [1196, 25, 867, 23], [1196, 27, 867, 25], [1197, 6, 868, 4, "opacity"], [1197, 13, 868, 11], [1197, 15, 868, 13], [1198, 4, 869, 2], [1198, 5, 869, 3], [1199, 4, 870, 2, "shutterInner"], [1199, 16, 870, 14], [1199, 18, 870, 16], [1200, 6, 871, 4, "width"], [1200, 11, 871, 9], [1200, 13, 871, 11], [1200, 15, 871, 13], [1201, 6, 872, 4, "height"], [1201, 12, 872, 10], [1201, 14, 872, 12], [1201, 16, 872, 14], [1202, 6, 873, 4, "borderRadius"], [1202, 18, 873, 16], [1202, 20, 873, 18], [1202, 22, 873, 20], [1203, 6, 874, 4, "backgroundColor"], [1203, 21, 874, 19], [1203, 23, 874, 21], [1203, 29, 874, 27], [1204, 6, 875, 4, "borderWidth"], [1204, 17, 875, 15], [1204, 19, 875, 17], [1204, 20, 875, 18], [1205, 6, 876, 4, "borderColor"], [1205, 17, 876, 15], [1205, 19, 876, 17], [1206, 4, 877, 2], [1206, 5, 877, 3], [1207, 4, 878, 2, "privacyNote"], [1207, 15, 878, 13], [1207, 17, 878, 15], [1208, 6, 879, 4, "fontSize"], [1208, 14, 879, 12], [1208, 16, 879, 14], [1208, 18, 879, 16], [1209, 6, 880, 4, "color"], [1209, 11, 880, 9], [1209, 13, 880, 11], [1210, 4, 881, 2], [1210, 5, 881, 3], [1211, 4, 882, 2, "processingModal"], [1211, 19, 882, 17], [1211, 21, 882, 19], [1212, 6, 883, 4, "flex"], [1212, 10, 883, 8], [1212, 12, 883, 10], [1212, 13, 883, 11], [1213, 6, 884, 4, "backgroundColor"], [1213, 21, 884, 19], [1213, 23, 884, 21], [1213, 43, 884, 41], [1214, 6, 885, 4, "justifyContent"], [1214, 20, 885, 18], [1214, 22, 885, 20], [1214, 30, 885, 28], [1215, 6, 886, 4, "alignItems"], [1215, 16, 886, 14], [1215, 18, 886, 16], [1216, 4, 887, 2], [1216, 5, 887, 3], [1217, 4, 888, 2, "processingContent"], [1217, 21, 888, 19], [1217, 23, 888, 21], [1218, 6, 889, 4, "backgroundColor"], [1218, 21, 889, 19], [1218, 23, 889, 21], [1218, 29, 889, 27], [1219, 6, 890, 4, "borderRadius"], [1219, 18, 890, 16], [1219, 20, 890, 18], [1219, 22, 890, 20], [1220, 6, 891, 4, "padding"], [1220, 13, 891, 11], [1220, 15, 891, 13], [1220, 17, 891, 15], [1221, 6, 892, 4, "width"], [1221, 11, 892, 9], [1221, 13, 892, 11], [1221, 18, 892, 16], [1222, 6, 893, 4, "max<PERSON><PERSON><PERSON>"], [1222, 14, 893, 12], [1222, 16, 893, 14], [1222, 19, 893, 17], [1223, 6, 894, 4, "alignItems"], [1223, 16, 894, 14], [1223, 18, 894, 16], [1224, 4, 895, 2], [1224, 5, 895, 3], [1225, 4, 896, 2, "processingTitle"], [1225, 19, 896, 17], [1225, 21, 896, 19], [1226, 6, 897, 4, "fontSize"], [1226, 14, 897, 12], [1226, 16, 897, 14], [1226, 18, 897, 16], [1227, 6, 898, 4, "fontWeight"], [1227, 16, 898, 14], [1227, 18, 898, 16], [1227, 23, 898, 21], [1228, 6, 899, 4, "color"], [1228, 11, 899, 9], [1228, 13, 899, 11], [1228, 22, 899, 20], [1229, 6, 900, 4, "marginTop"], [1229, 15, 900, 13], [1229, 17, 900, 15], [1229, 19, 900, 17], [1230, 6, 901, 4, "marginBottom"], [1230, 18, 901, 16], [1230, 20, 901, 18], [1231, 4, 902, 2], [1231, 5, 902, 3], [1232, 4, 903, 2, "progressBar"], [1232, 15, 903, 13], [1232, 17, 903, 15], [1233, 6, 904, 4, "width"], [1233, 11, 904, 9], [1233, 13, 904, 11], [1233, 19, 904, 17], [1234, 6, 905, 4, "height"], [1234, 12, 905, 10], [1234, 14, 905, 12], [1234, 15, 905, 13], [1235, 6, 906, 4, "backgroundColor"], [1235, 21, 906, 19], [1235, 23, 906, 21], [1235, 32, 906, 30], [1236, 6, 907, 4, "borderRadius"], [1236, 18, 907, 16], [1236, 20, 907, 18], [1236, 21, 907, 19], [1237, 6, 908, 4, "overflow"], [1237, 14, 908, 12], [1237, 16, 908, 14], [1237, 24, 908, 22], [1238, 6, 909, 4, "marginBottom"], [1238, 18, 909, 16], [1238, 20, 909, 18], [1239, 4, 910, 2], [1239, 5, 910, 3], [1240, 4, 911, 2, "progressFill"], [1240, 16, 911, 14], [1240, 18, 911, 16], [1241, 6, 912, 4, "height"], [1241, 12, 912, 10], [1241, 14, 912, 12], [1241, 20, 912, 18], [1242, 6, 913, 4, "backgroundColor"], [1242, 21, 913, 19], [1242, 23, 913, 21], [1242, 32, 913, 30], [1243, 6, 914, 4, "borderRadius"], [1243, 18, 914, 16], [1243, 20, 914, 18], [1244, 4, 915, 2], [1244, 5, 915, 3], [1245, 4, 916, 2, "processingDescription"], [1245, 25, 916, 23], [1245, 27, 916, 25], [1246, 6, 917, 4, "fontSize"], [1246, 14, 917, 12], [1246, 16, 917, 14], [1246, 18, 917, 16], [1247, 6, 918, 4, "color"], [1247, 11, 918, 9], [1247, 13, 918, 11], [1247, 22, 918, 20], [1248, 6, 919, 4, "textAlign"], [1248, 15, 919, 13], [1248, 17, 919, 15], [1249, 4, 920, 2], [1249, 5, 920, 3], [1250, 4, 921, 2, "successIcon"], [1250, 15, 921, 13], [1250, 17, 921, 15], [1251, 6, 922, 4, "marginTop"], [1251, 15, 922, 13], [1251, 17, 922, 15], [1252, 4, 923, 2], [1252, 5, 923, 3], [1253, 4, 924, 2, "errorContent"], [1253, 16, 924, 14], [1253, 18, 924, 16], [1254, 6, 925, 4, "backgroundColor"], [1254, 21, 925, 19], [1254, 23, 925, 21], [1254, 29, 925, 27], [1255, 6, 926, 4, "borderRadius"], [1255, 18, 926, 16], [1255, 20, 926, 18], [1255, 22, 926, 20], [1256, 6, 927, 4, "padding"], [1256, 13, 927, 11], [1256, 15, 927, 13], [1256, 17, 927, 15], [1257, 6, 928, 4, "width"], [1257, 11, 928, 9], [1257, 13, 928, 11], [1257, 18, 928, 16], [1258, 6, 929, 4, "max<PERSON><PERSON><PERSON>"], [1258, 14, 929, 12], [1258, 16, 929, 14], [1258, 19, 929, 17], [1259, 6, 930, 4, "alignItems"], [1259, 16, 930, 14], [1259, 18, 930, 16], [1260, 4, 931, 2], [1260, 5, 931, 3], [1261, 4, 932, 2, "errorTitle"], [1261, 14, 932, 12], [1261, 16, 932, 14], [1262, 6, 933, 4, "fontSize"], [1262, 14, 933, 12], [1262, 16, 933, 14], [1262, 18, 933, 16], [1263, 6, 934, 4, "fontWeight"], [1263, 16, 934, 14], [1263, 18, 934, 16], [1263, 23, 934, 21], [1264, 6, 935, 4, "color"], [1264, 11, 935, 9], [1264, 13, 935, 11], [1264, 22, 935, 20], [1265, 6, 936, 4, "marginTop"], [1265, 15, 936, 13], [1265, 17, 936, 15], [1265, 19, 936, 17], [1266, 6, 937, 4, "marginBottom"], [1266, 18, 937, 16], [1266, 20, 937, 18], [1267, 4, 938, 2], [1267, 5, 938, 3], [1268, 4, 939, 2, "errorMessage"], [1268, 16, 939, 14], [1268, 18, 939, 16], [1269, 6, 940, 4, "fontSize"], [1269, 14, 940, 12], [1269, 16, 940, 14], [1269, 18, 940, 16], [1270, 6, 941, 4, "color"], [1270, 11, 941, 9], [1270, 13, 941, 11], [1270, 22, 941, 20], [1271, 6, 942, 4, "textAlign"], [1271, 15, 942, 13], [1271, 17, 942, 15], [1271, 25, 942, 23], [1272, 6, 943, 4, "marginBottom"], [1272, 18, 943, 16], [1272, 20, 943, 18], [1273, 4, 944, 2], [1273, 5, 944, 3], [1274, 4, 945, 2, "primaryButton"], [1274, 17, 945, 15], [1274, 19, 945, 17], [1275, 6, 946, 4, "backgroundColor"], [1275, 21, 946, 19], [1275, 23, 946, 21], [1275, 32, 946, 30], [1276, 6, 947, 4, "paddingHorizontal"], [1276, 23, 947, 21], [1276, 25, 947, 23], [1276, 27, 947, 25], [1277, 6, 948, 4, "paddingVertical"], [1277, 21, 948, 19], [1277, 23, 948, 21], [1277, 25, 948, 23], [1278, 6, 949, 4, "borderRadius"], [1278, 18, 949, 16], [1278, 20, 949, 18], [1278, 21, 949, 19], [1279, 6, 950, 4, "marginTop"], [1279, 15, 950, 13], [1279, 17, 950, 15], [1280, 4, 951, 2], [1280, 5, 951, 3], [1281, 4, 952, 2, "primaryButtonText"], [1281, 21, 952, 19], [1281, 23, 952, 21], [1282, 6, 953, 4, "color"], [1282, 11, 953, 9], [1282, 13, 953, 11], [1282, 19, 953, 17], [1283, 6, 954, 4, "fontSize"], [1283, 14, 954, 12], [1283, 16, 954, 14], [1283, 18, 954, 16], [1284, 6, 955, 4, "fontWeight"], [1284, 16, 955, 14], [1284, 18, 955, 16], [1285, 4, 956, 2], [1285, 5, 956, 3], [1286, 4, 957, 2, "secondaryButton"], [1286, 19, 957, 17], [1286, 21, 957, 19], [1287, 6, 958, 4, "paddingHorizontal"], [1287, 23, 958, 21], [1287, 25, 958, 23], [1287, 27, 958, 25], [1288, 6, 959, 4, "paddingVertical"], [1288, 21, 959, 19], [1288, 23, 959, 21], [1288, 25, 959, 23], [1289, 6, 960, 4, "marginTop"], [1289, 15, 960, 13], [1289, 17, 960, 15], [1290, 4, 961, 2], [1290, 5, 961, 3], [1291, 4, 962, 2, "secondaryButtonText"], [1291, 23, 962, 21], [1291, 25, 962, 23], [1292, 6, 963, 4, "color"], [1292, 11, 963, 9], [1292, 13, 963, 11], [1292, 22, 963, 20], [1293, 6, 964, 4, "fontSize"], [1293, 14, 964, 12], [1293, 16, 964, 14], [1294, 4, 965, 2], [1294, 5, 965, 3], [1295, 4, 966, 2, "permissionContent"], [1295, 21, 966, 19], [1295, 23, 966, 21], [1296, 6, 967, 4, "flex"], [1296, 10, 967, 8], [1296, 12, 967, 10], [1296, 13, 967, 11], [1297, 6, 968, 4, "justifyContent"], [1297, 20, 968, 18], [1297, 22, 968, 20], [1297, 30, 968, 28], [1298, 6, 969, 4, "alignItems"], [1298, 16, 969, 14], [1298, 18, 969, 16], [1298, 26, 969, 24], [1299, 6, 970, 4, "padding"], [1299, 13, 970, 11], [1299, 15, 970, 13], [1300, 4, 971, 2], [1300, 5, 971, 3], [1301, 4, 972, 2, "permissionTitle"], [1301, 19, 972, 17], [1301, 21, 972, 19], [1302, 6, 973, 4, "fontSize"], [1302, 14, 973, 12], [1302, 16, 973, 14], [1302, 18, 973, 16], [1303, 6, 974, 4, "fontWeight"], [1303, 16, 974, 14], [1303, 18, 974, 16], [1303, 23, 974, 21], [1304, 6, 975, 4, "color"], [1304, 11, 975, 9], [1304, 13, 975, 11], [1304, 22, 975, 20], [1305, 6, 976, 4, "marginTop"], [1305, 15, 976, 13], [1305, 17, 976, 15], [1305, 19, 976, 17], [1306, 6, 977, 4, "marginBottom"], [1306, 18, 977, 16], [1306, 20, 977, 18], [1307, 4, 978, 2], [1307, 5, 978, 3], [1308, 4, 979, 2, "permissionDescription"], [1308, 25, 979, 23], [1308, 27, 979, 25], [1309, 6, 980, 4, "fontSize"], [1309, 14, 980, 12], [1309, 16, 980, 14], [1309, 18, 980, 16], [1310, 6, 981, 4, "color"], [1310, 11, 981, 9], [1310, 13, 981, 11], [1310, 22, 981, 20], [1311, 6, 982, 4, "textAlign"], [1311, 15, 982, 13], [1311, 17, 982, 15], [1311, 25, 982, 23], [1312, 6, 983, 4, "marginBottom"], [1312, 18, 983, 16], [1312, 20, 983, 18], [1313, 4, 984, 2], [1313, 5, 984, 3], [1314, 4, 985, 2, "loadingText"], [1314, 15, 985, 13], [1314, 17, 985, 15], [1315, 6, 986, 4, "color"], [1315, 11, 986, 9], [1315, 13, 986, 11], [1315, 22, 986, 20], [1316, 6, 987, 4, "marginTop"], [1316, 15, 987, 13], [1316, 17, 987, 15], [1317, 4, 988, 2], [1317, 5, 988, 3], [1318, 4, 989, 2], [1319, 4, 990, 2, "blurZone"], [1319, 12, 990, 10], [1319, 14, 990, 12], [1320, 6, 991, 4, "position"], [1320, 14, 991, 12], [1320, 16, 991, 14], [1320, 26, 991, 24], [1321, 6, 992, 4, "overflow"], [1321, 14, 992, 12], [1321, 16, 992, 14], [1322, 4, 993, 2], [1322, 5, 993, 3], [1323, 4, 994, 2, "previewChip"], [1323, 15, 994, 13], [1323, 17, 994, 15], [1324, 6, 995, 4, "position"], [1324, 14, 995, 12], [1324, 16, 995, 14], [1324, 26, 995, 24], [1325, 6, 996, 4, "top"], [1325, 9, 996, 7], [1325, 11, 996, 9], [1325, 12, 996, 10], [1326, 6, 997, 4, "right"], [1326, 11, 997, 9], [1326, 13, 997, 11], [1326, 14, 997, 12], [1327, 6, 998, 4, "backgroundColor"], [1327, 21, 998, 19], [1327, 23, 998, 21], [1327, 40, 998, 38], [1328, 6, 999, 4, "paddingHorizontal"], [1328, 23, 999, 21], [1328, 25, 999, 23], [1328, 27, 999, 25], [1329, 6, 1000, 4, "paddingVertical"], [1329, 21, 1000, 19], [1329, 23, 1000, 21], [1329, 24, 1000, 22], [1330, 6, 1001, 4, "borderRadius"], [1330, 18, 1001, 16], [1330, 20, 1001, 18], [1331, 4, 1002, 2], [1331, 5, 1002, 3], [1332, 4, 1003, 2, "previewChipText"], [1332, 19, 1003, 17], [1332, 21, 1003, 19], [1333, 6, 1004, 4, "color"], [1333, 11, 1004, 9], [1333, 13, 1004, 11], [1333, 19, 1004, 17], [1334, 6, 1005, 4, "fontSize"], [1334, 14, 1005, 12], [1334, 16, 1005, 14], [1334, 18, 1005, 16], [1335, 6, 1006, 4, "fontWeight"], [1335, 16, 1006, 14], [1335, 18, 1006, 16], [1336, 4, 1007, 2], [1337, 2, 1008, 0], [1337, 3, 1008, 1], [1337, 4, 1008, 2], [1338, 2, 1008, 3], [1338, 6, 1008, 3, "_c"], [1338, 8, 1008, 3], [1339, 2, 1008, 3, "$RefreshReg$"], [1339, 14, 1008, 3], [1339, 15, 1008, 3, "_c"], [1339, 17, 1008, 3], [1340, 0, 1008, 3], [1340, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHkC;mCKE;wBDY;OCI;gDC8B;YDO;8BDa;aCM;6CEc;YFO;oFGsB;UHM;8BIS;SJgD;uDDQ;sBMC,wBN;OCC;GLc;6BWG;GXyB;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}