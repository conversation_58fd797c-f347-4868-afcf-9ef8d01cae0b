{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkPath", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 639}, "end": {"line": 5, "column": 40, "index": 679}}], "key": "h12LvMRBvFyvLJVrX3awiGHZPFU=", "exportNames": ["*"]}}, {"name": "./JsiSkPoint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 680}, "end": {"line": 6, "column": 42, "index": 722}}], "key": "t00LaVO/wJ2FJvJQ0krGRNiisCQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkContourMeasure = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkPath = require(_dependencyMap[1], \"./JsiSkPath\");\n  var _JsiSkPoint = require(_dependencyMap[2], \"./JsiSkPoint\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkContourMeasure extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"ContourMeasure\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    getPosTan(distance) {\n      const posTan = this.ref.getPosTan(distance);\n      return [new _JsiSkPoint.JsiSkPoint(this.CanvasKit, posTan.slice(0, 2)), new _JsiSkPoint.JsiSkPoint(this.CanvasKit, posTan.slice(2))];\n    }\n    getSegment(startD, stopD, startWithMoveTo) {\n      return new _JsiSkPath.JsiSkPath(this.CanvasKit, this.ref.getSegment(startD, stopD, startWithMoveTo));\n    }\n    isClosed() {\n      return this.ref.isClosed();\n    }\n    length() {\n      return this.ref.length();\n    }\n  }\n  exports.JsiSkContourMeasure = JsiSkContourMeasure;\n});", "lineCount": 53, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkPath"], [7, 16, 5, 0], [7, 19, 5, 0, "require"], [7, 26, 5, 0], [7, 27, 5, 0, "_dependencyMap"], [7, 41, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkPoint"], [8, 17, 6, 0], [8, 20, 6, 0, "require"], [8, 27, 6, 0], [8, 28, 6, 0, "_dependencyMap"], [8, 42, 6, 0], [9, 2, 1, 0], [9, 11, 1, 9, "_defineProperty"], [9, 26, 1, 24, "_defineProperty"], [9, 27, 1, 25, "e"], [9, 28, 1, 26], [9, 30, 1, 28, "r"], [9, 31, 1, 29], [9, 33, 1, 31, "t"], [9, 34, 1, 32], [9, 36, 1, 34], [10, 4, 1, 36], [10, 11, 1, 43], [10, 12, 1, 44, "r"], [10, 13, 1, 45], [10, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 30, 1, 62], [10, 31, 1, 63, "r"], [10, 32, 1, 64], [10, 33, 1, 65], [10, 38, 1, 70, "e"], [10, 39, 1, 71], [10, 42, 1, 74, "Object"], [10, 48, 1, 80], [10, 49, 1, 81, "defineProperty"], [10, 63, 1, 95], [10, 64, 1, 96, "e"], [10, 65, 1, 97], [10, 67, 1, 99, "r"], [10, 68, 1, 100], [10, 70, 1, 102], [11, 6, 1, 104, "value"], [11, 11, 1, 109], [11, 13, 1, 111, "t"], [11, 14, 1, 112], [12, 6, 1, 114, "enumerable"], [12, 16, 1, 124], [12, 18, 1, 126], [12, 19, 1, 127], [12, 20, 1, 128], [13, 6, 1, 130, "configurable"], [13, 18, 1, 142], [13, 20, 1, 144], [13, 21, 1, 145], [13, 22, 1, 146], [14, 6, 1, 148, "writable"], [14, 14, 1, 156], [14, 16, 1, 158], [14, 17, 1, 159], [15, 4, 1, 161], [15, 5, 1, 162], [15, 6, 1, 163], [15, 9, 1, 166, "e"], [15, 10, 1, 167], [15, 11, 1, 168, "r"], [15, 12, 1, 169], [15, 13, 1, 170], [15, 16, 1, 173, "t"], [15, 17, 1, 174], [15, 19, 1, 176, "e"], [15, 20, 1, 177], [16, 2, 1, 179], [17, 2, 2, 0], [17, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [17, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [17, 26, 2, 24, "t"], [17, 27, 2, 25], [17, 29, 2, 27], [18, 4, 2, 29], [18, 8, 2, 33, "i"], [18, 9, 2, 34], [18, 12, 2, 37, "_toPrimitive"], [18, 24, 2, 49], [18, 25, 2, 50, "t"], [18, 26, 2, 51], [18, 28, 2, 53], [18, 36, 2, 61], [18, 37, 2, 62], [19, 4, 2, 64], [19, 11, 2, 71], [19, 19, 2, 79], [19, 23, 2, 83], [19, 30, 2, 90, "i"], [19, 31, 2, 91], [19, 34, 2, 94, "i"], [19, 35, 2, 95], [19, 38, 2, 98, "i"], [19, 39, 2, 99], [19, 42, 2, 102], [19, 44, 2, 104], [20, 2, 2, 106], [21, 2, 3, 0], [21, 11, 3, 9, "_toPrimitive"], [21, 23, 3, 21, "_toPrimitive"], [21, 24, 3, 22, "t"], [21, 25, 3, 23], [21, 27, 3, 25, "r"], [21, 28, 3, 26], [21, 30, 3, 28], [22, 4, 3, 30], [22, 8, 3, 34], [22, 16, 3, 42], [22, 20, 3, 46], [22, 27, 3, 53, "t"], [22, 28, 3, 54], [22, 32, 3, 58], [22, 33, 3, 59, "t"], [22, 34, 3, 60], [22, 36, 3, 62], [22, 43, 3, 69, "t"], [22, 44, 3, 70], [23, 4, 3, 72], [23, 8, 3, 76, "e"], [23, 9, 3, 77], [23, 12, 3, 80, "t"], [23, 13, 3, 81], [23, 14, 3, 82, "Symbol"], [23, 20, 3, 88], [23, 21, 3, 89, "toPrimitive"], [23, 32, 3, 100], [23, 33, 3, 101], [24, 4, 3, 103], [24, 8, 3, 107], [24, 13, 3, 112], [24, 14, 3, 113], [24, 19, 3, 118, "e"], [24, 20, 3, 119], [24, 22, 3, 121], [25, 6, 3, 123], [25, 10, 3, 127, "i"], [25, 11, 3, 128], [25, 14, 3, 131, "e"], [25, 15, 3, 132], [25, 16, 3, 133, "call"], [25, 20, 3, 137], [25, 21, 3, 138, "t"], [25, 22, 3, 139], [25, 24, 3, 141, "r"], [25, 25, 3, 142], [25, 29, 3, 146], [25, 38, 3, 155], [25, 39, 3, 156], [26, 6, 3, 158], [26, 10, 3, 162], [26, 18, 3, 170], [26, 22, 3, 174], [26, 29, 3, 181, "i"], [26, 30, 3, 182], [26, 32, 3, 184], [26, 39, 3, 191, "i"], [26, 40, 3, 192], [27, 6, 3, 194], [27, 12, 3, 200], [27, 16, 3, 204, "TypeError"], [27, 25, 3, 213], [27, 26, 3, 214], [27, 72, 3, 260], [27, 73, 3, 261], [28, 4, 3, 263], [29, 4, 3, 265], [29, 11, 3, 272], [29, 12, 3, 273], [29, 20, 3, 281], [29, 25, 3, 286, "r"], [29, 26, 3, 287], [29, 29, 3, 290, "String"], [29, 35, 3, 296], [29, 38, 3, 299, "Number"], [29, 44, 3, 305], [29, 46, 3, 307, "t"], [29, 47, 3, 308], [29, 48, 3, 309], [30, 2, 3, 311], [31, 2, 7, 7], [31, 8, 7, 13, "JsiSkContourMeasure"], [31, 27, 7, 32], [31, 36, 7, 41, "HostObject"], [31, 52, 7, 51], [31, 53, 7, 52], [32, 4, 8, 2, "constructor"], [32, 15, 8, 13, "constructor"], [32, 16, 8, 14, "CanvasKit"], [32, 25, 8, 23], [32, 27, 8, 25, "ref"], [32, 30, 8, 28], [32, 32, 8, 30], [33, 6, 9, 4], [33, 11, 9, 9], [33, 12, 9, 10, "CanvasKit"], [33, 21, 9, 19], [33, 23, 9, 21, "ref"], [33, 26, 9, 24], [33, 28, 9, 26], [33, 44, 9, 42], [33, 45, 9, 43], [34, 6, 10, 4, "_defineProperty"], [34, 21, 10, 19], [34, 22, 10, 20], [34, 26, 10, 24], [34, 28, 10, 26], [34, 37, 10, 35], [34, 39, 10, 37], [34, 45, 10, 43], [35, 8, 11, 6], [35, 12, 11, 10], [35, 13, 11, 11, "ref"], [35, 16, 11, 14], [35, 17, 11, 15, "delete"], [35, 23, 11, 21], [35, 24, 11, 22], [35, 25, 11, 23], [36, 6, 12, 4], [36, 7, 12, 5], [36, 8, 12, 6], [37, 4, 13, 2], [38, 4, 14, 2, "getPosTan"], [38, 13, 14, 11, "getPosTan"], [38, 14, 14, 12, "distance"], [38, 22, 14, 20], [38, 24, 14, 22], [39, 6, 15, 4], [39, 12, 15, 10, "posTan"], [39, 18, 15, 16], [39, 21, 15, 19], [39, 25, 15, 23], [39, 26, 15, 24, "ref"], [39, 29, 15, 27], [39, 30, 15, 28, "getPosTan"], [39, 39, 15, 37], [39, 40, 15, 38, "distance"], [39, 48, 15, 46], [39, 49, 15, 47], [40, 6, 16, 4], [40, 13, 16, 11], [40, 14, 16, 12], [40, 18, 16, 16, "JsiSkPoint"], [40, 40, 16, 26], [40, 41, 16, 27], [40, 45, 16, 31], [40, 46, 16, 32, "CanvasKit"], [40, 55, 16, 41], [40, 57, 16, 43, "posTan"], [40, 63, 16, 49], [40, 64, 16, 50, "slice"], [40, 69, 16, 55], [40, 70, 16, 56], [40, 71, 16, 57], [40, 73, 16, 59], [40, 74, 16, 60], [40, 75, 16, 61], [40, 76, 16, 62], [40, 78, 16, 64], [40, 82, 16, 68, "JsiSkPoint"], [40, 104, 16, 78], [40, 105, 16, 79], [40, 109, 16, 83], [40, 110, 16, 84, "CanvasKit"], [40, 119, 16, 93], [40, 121, 16, 95, "posTan"], [40, 127, 16, 101], [40, 128, 16, 102, "slice"], [40, 133, 16, 107], [40, 134, 16, 108], [40, 135, 16, 109], [40, 136, 16, 110], [40, 137, 16, 111], [40, 138, 16, 112], [41, 4, 17, 2], [42, 4, 18, 2, "getSegment"], [42, 14, 18, 12, "getSegment"], [42, 15, 18, 13, "startD"], [42, 21, 18, 19], [42, 23, 18, 21, "stopD"], [42, 28, 18, 26], [42, 30, 18, 28, "startWithMoveTo"], [42, 45, 18, 43], [42, 47, 18, 45], [43, 6, 19, 4], [43, 13, 19, 11], [43, 17, 19, 15, "JsiSkPath"], [43, 37, 19, 24], [43, 38, 19, 25], [43, 42, 19, 29], [43, 43, 19, 30, "CanvasKit"], [43, 52, 19, 39], [43, 54, 19, 41], [43, 58, 19, 45], [43, 59, 19, 46, "ref"], [43, 62, 19, 49], [43, 63, 19, 50, "getSegment"], [43, 73, 19, 60], [43, 74, 19, 61, "startD"], [43, 80, 19, 67], [43, 82, 19, 69, "stopD"], [43, 87, 19, 74], [43, 89, 19, 76, "startWithMoveTo"], [43, 104, 19, 91], [43, 105, 19, 92], [43, 106, 19, 93], [44, 4, 20, 2], [45, 4, 21, 2, "isClosed"], [45, 12, 21, 10, "isClosed"], [45, 13, 21, 10], [45, 15, 21, 13], [46, 6, 22, 4], [46, 13, 22, 11], [46, 17, 22, 15], [46, 18, 22, 16, "ref"], [46, 21, 22, 19], [46, 22, 22, 20, "isClosed"], [46, 30, 22, 28], [46, 31, 22, 29], [46, 32, 22, 30], [47, 4, 23, 2], [48, 4, 24, 2, "length"], [48, 10, 24, 8, "length"], [48, 11, 24, 8], [48, 13, 24, 11], [49, 6, 25, 4], [49, 13, 25, 11], [49, 17, 25, 15], [49, 18, 25, 16, "ref"], [49, 21, 25, 19], [49, 22, 25, 20, "length"], [49, 28, 25, 26], [49, 29, 25, 27], [49, 30, 25, 28], [50, 4, 26, 2], [51, 2, 27, 0], [52, 2, 27, 1, "exports"], [52, 9, 27, 1], [52, 10, 27, 1, "JsiSkContourMeasure"], [52, 29, 27, 1], [52, 32, 27, 1, "JsiSkContourMeasure"], [52, 51, 27, 1], [53, 0, 27, 1], [53, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkContourMeasure", "constructor", "_defineProperty$argument_2", "getPosTan", "getSegment", "isClosed", "length"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGI;ECC;qCCE;KDE;GDC;EGC;GHG;EIC;GJE;EKC;GLE;EMC;GNE;CHC"}}, "type": "js/module"}]}