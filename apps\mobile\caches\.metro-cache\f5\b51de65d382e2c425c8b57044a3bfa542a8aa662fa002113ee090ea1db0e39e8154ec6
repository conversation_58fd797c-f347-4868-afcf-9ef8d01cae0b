{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ShaderLib = void 0;\n  const MATH = `\nconst float PI = 3.1415926535897932384626433832795;\nconst float TAU = 2 * PI;\n\nfloat normalizeRad(float value) {\n  float rest = mod(value, TAU);\n  return rest > 0 ? rest : TAU + rest;\n}\n\nvec2 canvas2Cartesian(vec2 v, vec2 center) {\n  return vec2(v.x - center.x, -1 * (v.y - center.y));\n}\n\nvec2 cartesian2Canvas(vec2 v, vec2 center) {\n  return vec2(v.x + center.x, -1 * v.y + center.y);\n}\n\nvec2 cartesian2Polar(vec2 v) {\n  return vec2(atan(v.y, v.x), sqrt(v.x * v.x + v.y * v.y));\n}\n\nvec2 polar2Cartesian(vec2 p) {\n  return vec2(p.y * cos(p.x), p.y * sin(p.x));\n}\n\nvec2 polar2Canvas(vec2 p, vec2 center) {\n  return cartesian2Canvas(polar2Cartesian(p), center);\n}\n\nvec2 canvas2Polar(vec2 v, vec2 center) {\n  return cartesian2Polar(canvas2Cartesian(v, center));\n}\n`;\n  const COLORS = `\nvec4 hsv2rgb(vec3 c) {\n    vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);\n    vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);\n    return vec4(c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y), 1.0);\n}\n`;\n  const ShaderLib = exports.ShaderLib = {\n    Math: MATH,\n    Colors: COLORS\n  };\n});", "lineCount": 50, "map": [[6, 2, 1, 0], [6, 8, 1, 6, "MATH"], [6, 12, 1, 10], [6, 15, 1, 13], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 0, 24, 0], [30, 0, 25, 0], [31, 0, 26, 0], [32, 0, 27, 0], [33, 0, 28, 0], [34, 0, 29, 0], [35, 0, 30, 0], [36, 0, 31, 0], [37, 0, 32, 0], [38, 0, 33, 0], [38, 1, 33, 1], [39, 2, 34, 0], [39, 8, 34, 6, "COLORS"], [39, 14, 34, 12], [39, 17, 34, 15], [40, 0, 35, 0], [41, 0, 36, 0], [42, 0, 37, 0], [43, 0, 38, 0], [44, 0, 39, 0], [45, 0, 40, 0], [45, 1, 40, 1], [46, 2, 41, 7], [46, 8, 41, 13, "ShaderLib"], [46, 17, 41, 22], [46, 20, 41, 22, "exports"], [46, 27, 41, 22], [46, 28, 41, 22, "ShaderLib"], [46, 37, 41, 22], [46, 40, 41, 25], [47, 4, 42, 2, "Math"], [47, 8, 42, 6], [47, 10, 42, 8, "MATH"], [47, 14, 42, 12], [48, 4, 43, 2, "Colors"], [48, 10, 43, 8], [48, 12, 43, 10, "COLORS"], [49, 2, 44, 0], [49, 3, 44, 1], [50, 0, 44, 2], [50, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}