{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = getBackgroundColor;\n  function getBackgroundColor(intensity, tint) {\n    const opacity = intensity / 100;\n    switch (tint) {\n      // From Apple iOS 14 Sketch Kit - https://developer.apple.com/design/resources/\n      // From Apple iOS 14 Sketch Kit - https://developer.apple.com/design/resources/\n      case 'dark':\n      case 'systemMaterialDark':\n        return `rgba(25,25,25,${opacity * 0.78})`;\n      case 'light':\n      case 'extraLight':\n      case 'systemMaterialLight':\n      case 'systemUltraThinMaterialLight':\n      case 'systemThickMaterialLight':\n        return `rgba(249,249,249,${opacity * 0.78})`;\n      case 'default':\n      case 'prominent':\n      case 'systemMaterial':\n        return `rgba(255,255,255,${opacity * 0.3})`;\n      case 'regular':\n        return `rgba(179,179,179,${opacity * 0.82})`;\n      case 'systemThinMaterial':\n        return `rgba(199,199,199,${opacity * 0.97})`;\n      case 'systemChromeMaterial':\n        return `rgba(255,255,255,${opacity * 0.75})`;\n      case 'systemChromeMaterialLight':\n        return `rgba(255,255,255,${opacity * 0.97})`;\n      case 'systemUltraThinMaterial':\n        return `rgba(191,191,191,${opacity * 0.44})`;\n      case 'systemThickMaterial':\n        return `rgba(191,191,191,${opacity * 0.44})`;\n      case 'systemThickMaterialDark':\n        return `rgba(37,37,37,${opacity * 0.9})`;\n      case 'systemThinMaterialDark':\n        return `rgba(37,37,37,${opacity * 0.7})`;\n      case 'systemUltraThinMaterialDark':\n        return `rgba(37,37,37,${opacity * 0.55})`;\n      case 'systemChromeMaterialDark':\n        return `rgba(0,0,0,${opacity * 0.75})`;\n      case 'systemThinMaterialLight':\n        return `rgba(199,199,199,${opacity * 0.78})`;\n    }\n  }\n});", "lineCount": 48, "map": [[6, 2, 1, 15], [6, 11, 1, 24, "getBackgroundColor"], [6, 29, 1, 42, "getBackgroundColor"], [6, 30, 1, 43, "intensity"], [6, 39, 1, 52], [6, 41, 1, 54, "tint"], [6, 45, 1, 58], [6, 47, 1, 60], [7, 4, 2, 4], [7, 10, 2, 10, "opacity"], [7, 17, 2, 17], [7, 20, 2, 20, "intensity"], [7, 29, 2, 29], [7, 32, 2, 32], [7, 35, 2, 35], [8, 4, 3, 4], [8, 12, 3, 12, "tint"], [8, 16, 3, 16], [9, 6, 4, 8], [10, 6, 5, 8], [11, 6, 6, 8], [11, 11, 6, 13], [11, 17, 6, 19], [12, 6, 7, 8], [12, 11, 7, 13], [12, 31, 7, 33], [13, 8, 8, 12], [13, 15, 8, 19], [13, 32, 8, 36, "opacity"], [13, 39, 8, 43], [13, 42, 8, 46], [13, 46, 8, 50], [13, 49, 8, 53], [14, 6, 9, 8], [14, 11, 9, 13], [14, 18, 9, 20], [15, 6, 10, 8], [15, 11, 10, 13], [15, 23, 10, 25], [16, 6, 11, 8], [16, 11, 11, 13], [16, 32, 11, 34], [17, 6, 12, 8], [17, 11, 12, 13], [17, 41, 12, 43], [18, 6, 13, 8], [18, 11, 13, 13], [18, 37, 13, 39], [19, 8, 14, 12], [19, 15, 14, 19], [19, 35, 14, 39, "opacity"], [19, 42, 14, 46], [19, 45, 14, 49], [19, 49, 14, 53], [19, 52, 14, 56], [20, 6, 15, 8], [20, 11, 15, 13], [20, 20, 15, 22], [21, 6, 16, 8], [21, 11, 16, 13], [21, 22, 16, 24], [22, 6, 17, 8], [22, 11, 17, 13], [22, 27, 17, 29], [23, 8, 18, 12], [23, 15, 18, 19], [23, 35, 18, 39, "opacity"], [23, 42, 18, 46], [23, 45, 18, 49], [23, 48, 18, 52], [23, 51, 18, 55], [24, 6, 19, 8], [24, 11, 19, 13], [24, 20, 19, 22], [25, 8, 20, 12], [25, 15, 20, 19], [25, 35, 20, 39, "opacity"], [25, 42, 20, 46], [25, 45, 20, 49], [25, 49, 20, 53], [25, 52, 20, 56], [26, 6, 21, 8], [26, 11, 21, 13], [26, 31, 21, 33], [27, 8, 22, 12], [27, 15, 22, 19], [27, 35, 22, 39, "opacity"], [27, 42, 22, 46], [27, 45, 22, 49], [27, 49, 22, 53], [27, 52, 22, 56], [28, 6, 23, 8], [28, 11, 23, 13], [28, 33, 23, 35], [29, 8, 24, 12], [29, 15, 24, 19], [29, 35, 24, 39, "opacity"], [29, 42, 24, 46], [29, 45, 24, 49], [29, 49, 24, 53], [29, 52, 24, 56], [30, 6, 25, 8], [30, 11, 25, 13], [30, 38, 25, 40], [31, 8, 26, 12], [31, 15, 26, 19], [31, 35, 26, 39, "opacity"], [31, 42, 26, 46], [31, 45, 26, 49], [31, 49, 26, 53], [31, 52, 26, 56], [32, 6, 27, 8], [32, 11, 27, 13], [32, 36, 27, 38], [33, 8, 28, 12], [33, 15, 28, 19], [33, 35, 28, 39, "opacity"], [33, 42, 28, 46], [33, 45, 28, 49], [33, 49, 28, 53], [33, 52, 28, 56], [34, 6, 29, 8], [34, 11, 29, 13], [34, 32, 29, 34], [35, 8, 30, 12], [35, 15, 30, 19], [35, 35, 30, 39, "opacity"], [35, 42, 30, 46], [35, 45, 30, 49], [35, 49, 30, 53], [35, 52, 30, 56], [36, 6, 31, 8], [36, 11, 31, 13], [36, 36, 31, 38], [37, 8, 32, 12], [37, 15, 32, 19], [37, 32, 32, 36, "opacity"], [37, 39, 32, 43], [37, 42, 32, 46], [37, 45, 32, 49], [37, 48, 32, 52], [38, 6, 33, 8], [38, 11, 33, 13], [38, 35, 33, 37], [39, 8, 34, 12], [39, 15, 34, 19], [39, 32, 34, 36, "opacity"], [39, 39, 34, 43], [39, 42, 34, 46], [39, 45, 34, 49], [39, 48, 34, 52], [40, 6, 35, 8], [40, 11, 35, 13], [40, 40, 35, 42], [41, 8, 36, 12], [41, 15, 36, 19], [41, 32, 36, 36, "opacity"], [41, 39, 36, 43], [41, 42, 36, 46], [41, 46, 36, 50], [41, 49, 36, 53], [42, 6, 37, 8], [42, 11, 37, 13], [42, 37, 37, 39], [43, 8, 38, 12], [43, 15, 38, 19], [43, 29, 38, 33, "opacity"], [43, 36, 38, 40], [43, 39, 38, 43], [43, 43, 38, 47], [43, 46, 38, 50], [44, 6, 39, 8], [44, 11, 39, 13], [44, 36, 39, 38], [45, 8, 40, 12], [45, 15, 40, 19], [45, 35, 40, 39, "opacity"], [45, 42, 40, 46], [45, 45, 40, 49], [45, 49, 40, 53], [45, 52, 40, 56], [46, 4, 41, 4], [47, 2, 42, 0], [48, 0, 42, 1], [48, 3]], "functionMap": {"names": ["<global>", "getBackgroundColor"], "mappings": "AAA,eC;CDyC"}}, "type": "js/module"}]}