{"dependencies": [{"name": "../ModuleProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 88, "index": 88}}], "key": "vdUqVYp3n/ZiIb3+K2osB3x0TZU=", "exportNames": ["*"]}}, {"name": "react-native-reanimated", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 11, "index": 153}, "end": {"line": 4, "column": 45, "index": 187}}], "key": "+aUP6OdvebG47hiJSteO076+5ZE=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ModuleProxy = require(_dependencyMap[0], \"../ModuleProxy\");\n  const Reanimated = (0, _ModuleProxy.createModuleProxy)(() => {\n    try {\n      return require(_dependencyMap[1], \"react-native-reanimated\");\n    } catch (e) {\n      throw new _ModuleProxy.OptionalDependencyNotInstalledError(\"react-native-reanimated\");\n    }\n  });\n\n  // eslint-disable-next-line import/no-default-export\n  var _default = exports.default = Reanimated;\n});", "lineCount": 17, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_ModuleProxy"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 2, 0], [7, 8, 2, 6, "Reanimated"], [7, 18, 2, 16], [7, 21, 2, 19], [7, 25, 2, 19, "createModuleProxy"], [7, 55, 2, 36], [7, 57, 2, 37], [7, 63, 2, 43], [8, 4, 3, 2], [8, 8, 3, 6], [9, 6, 4, 4], [9, 13, 4, 11, "require"], [9, 20, 4, 18], [9, 21, 4, 18, "_dependencyMap"], [9, 35, 4, 18], [9, 65, 4, 44], [9, 66, 4, 45], [10, 4, 5, 2], [10, 5, 5, 3], [10, 6, 5, 4], [10, 13, 5, 11, "e"], [10, 14, 5, 12], [10, 16, 5, 14], [11, 6, 6, 4], [11, 12, 6, 10], [11, 16, 6, 14, "OptionalDependencyNotInstalledError"], [11, 64, 6, 49], [11, 65, 6, 50], [11, 90, 6, 75], [11, 91, 6, 76], [12, 4, 7, 2], [13, 2, 8, 0], [13, 3, 8, 1], [13, 4, 8, 2], [15, 2, 10, 0], [16, 2, 10, 0], [16, 6, 10, 0, "_default"], [16, 14, 10, 0], [16, 17, 10, 0, "exports"], [16, 24, 10, 0], [16, 25, 10, 0, "default"], [16, 32, 10, 0], [16, 35, 11, 15, "Reanimated"], [16, 45, 11, 25], [17, 0, 11, 25], [17, 3]], "functionMap": {"names": ["<global>", "createModuleProxy$argument_0"], "mappings": "AAA;qCCC;CDM"}}, "type": "js/module"}]}