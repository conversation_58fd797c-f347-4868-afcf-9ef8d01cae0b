{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../animation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 46}, "end": {"line": 2, "column": 70, "index": 116}}], "key": "CcaUKku+J1qbuO1Ud6EjID0eSE0=", "exportNames": ["*"]}}, {"name": "../../skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 117}, "end": {"line": 3, "column": 34, "index": 151}}], "key": "+q0qwmVtgReRJ1JJKJleyyIYxCs=", "exportNames": ["*"]}}, {"name": "../../renderer/Offscreen", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 152}, "end": {"line": 4, "column": 58, "index": 210}}], "key": "KdT5Yxc0ye6AbkgBqacWSBftbgk=", "exportNames": ["*"]}}, {"name": "./ReanimatedProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 211}, "end": {"line": 5, "column": 36, "index": 247}}], "key": "9/hMKtQD5ZrdSt9i8tblq1v6X3Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useVectorInterpolation = exports.usePathValue = exports.usePathInterpolation = exports.useClock = exports.notifyChange = void 0;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _animation = require(_dependencyMap[2], \"../../animation\");\n  var _skia = require(_dependencyMap[3], \"../../skia\");\n  var _Offscreen = require(_dependencyMap[4], \"../../renderer/Offscreen\");\n  var _ReanimatedProxy = _interopRequireDefault(require(_dependencyMap[5], \"./ReanimatedProxy\"));\n  const _worklet_7707193526369_init_data = {\n    code: \"function interpolatorsJs1(value){const{isOnMainThread}=this.__closure;if(isOnMainThread()){value._value=value.value;}}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\interpolators.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolatorsJs1\\\",\\\"value\\\",\\\"isOnMainThread\\\",\\\"__closure\\\",\\\"_value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/interpolators.js\\\"],\\\"mappings\\\":\\\"AAK4B,SAAAA,gBAASA,CAAAC,KAAA,QAAAC,cAAA,OAAAC,SAAA,CAGnC,GAAID,cAAc,CAAC,CAAC,CAAE,CAEpBD,KAAK,CAACG,MAAM,CAAGH,KAAK,CAACA,KAAK,CAC5B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const notifyChange = exports.notifyChange = function () {\n    const _e = [new global.Error(), -2, -27];\n    const interpolatorsJs1 = function (value) {\n      if ((0, _Offscreen.isOnMainThread)()) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        value._value = value.value;\n      }\n    };\n    interpolatorsJs1.__closure = {\n      isOnMainThread: _Offscreen.isOnMainThread\n    };\n    interpolatorsJs1.__workletHash = 7707193526369;\n    interpolatorsJs1.__initData = _worklet_7707193526369_init_data;\n    interpolatorsJs1.__stackDetails = _e;\n    return interpolatorsJs1;\n  }();\n  const _worklet_2263978765805_init_data = {\n    code: \"function interpolatorsJs2(){const{path,init,cb,notifyChange}=this.__closure;path.value.reset();if(init!==undefined){path.value.addPath(init);}cb(path.value);notifyChange(path);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\interpolators.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolatorsJs2\\\",\\\"path\\\",\\\"init\\\",\\\"cb\\\",\\\"notifyChange\\\",\\\"__closure\\\",\\\"value\\\",\\\"reset\\\",\\\"undefined\\\",\\\"addPath\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/interpolators.js\\\"],\\\"mappings\\\":\\\"AAgBsB,SAAAA,gBAAMA,CAAA,QAAAC,IAAA,CAAAC,IAAA,CAAAC,EAAA,CAAAC,YAAA,OAAAC,SAAA,CACxBJ,IAAI,CAACK,KAAK,CAACC,KAAK,CAAC,CAAC,CAClB,GAAIL,IAAI,GAAKM,SAAS,CAAE,CACtBP,IAAI,CAACK,KAAK,CAACG,OAAO,CAACP,IAAI,CAAC,CAC1B,CACAC,EAAE,CAACF,IAAI,CAACK,KAAK,CAAC,CACdF,YAAY,CAACH,IAAI,CAAC,CACpB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const usePathValue = (cb, init) => {\n    const pathInit = (0, _react.useMemo)(() => _skia.Skia.Path.Make(), []);\n    const path = _ReanimatedProxy.default.useSharedValue(pathInit);\n    _ReanimatedProxy.default.useDerivedValue(function () {\n      const _e = [new global.Error(), -5, -27];\n      const interpolatorsJs2 = function () {\n        path.value.reset();\n        if (init !== undefined) {\n          path.value.addPath(init);\n        }\n        cb(path.value);\n        notifyChange(path);\n      };\n      interpolatorsJs2.__closure = {\n        path,\n        init,\n        cb,\n        notifyChange\n      };\n      interpolatorsJs2.__workletHash = 2263978765805;\n      interpolatorsJs2.__initData = _worklet_2263978765805_init_data;\n      interpolatorsJs2.__stackDetails = _e;\n      return interpolatorsJs2;\n    }());\n    return path;\n  };\n  exports.usePathValue = usePathValue;\n  const _worklet_8748492839893_init_data = {\n    code: \"function interpolatorsJs3(info){const{clock}=this.__closure;clock.value=info.timeSinceFirstFrame;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\interpolators.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolatorsJs3\\\",\\\"info\\\",\\\"clock\\\",\\\"__closure\\\",\\\"value\\\",\\\"timeSinceFirstFrame\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/interpolators.js\\\"],\\\"mappings\\\":\\\"AA4B+B,SAAAA,gBAAQA,CAAAC,IAAA,QAAAC,KAAA,OAAAC,SAAA,CAGnCD,KAAK,CAACE,KAAK,CAAGH,IAAI,CAACI,mBAAmB,CACxC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const useClock = () => {\n    const clock = _ReanimatedProxy.default.useSharedValue(0);\n    const callback = (0, _react.useCallback)(function () {\n      const _e = [new global.Error(), -2, -27];\n      const interpolatorsJs3 = function (info) {\n        clock.value = info.timeSinceFirstFrame;\n      };\n      interpolatorsJs3.__closure = {\n        clock\n      };\n      interpolatorsJs3.__workletHash = 8748492839893;\n      interpolatorsJs3.__initData = _worklet_8748492839893_init_data;\n      interpolatorsJs3.__stackDetails = _e;\n      return interpolatorsJs3;\n    }(), [clock]);\n    _ReanimatedProxy.default.useFrameCallback(callback);\n    return clock;\n  };\n\n  /**\n   * @worklet\n   */\n  exports.useClock = useClock;\n  const _worklet_12180018494379_init_data = {\n    code: \"function interpolatorsJs4(){const{value}=this.__closure;return value.value;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\interpolators.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolatorsJs4\\\",\\\"value\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/interpolators.js\\\"],\\\"mappings\\\":\\\"AA6C0B,SAAAA,iBAAA,QAAAC,KAAA,OAAAC,SAAA,OAAM,CAAAD,KAAM,CAAAA,KAAA\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_6113388669295_init_data = {\n    code: \"function interpolatorsJs5(val){const{result,interpolator,input,output,options,notifyChange}=this.__closure;result.value=interpolator(val,input,output,options,result.value);notifyChange(result);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\interpolators.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"interpolatorsJs5\\\",\\\"val\\\",\\\"result\\\",\\\"interpolator\\\",\\\"input\\\",\\\"output\\\",\\\"options\\\",\\\"notifyChange\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/interpolators.js\\\"],\\\"mappings\\\":\\\"AA6C6C,SAAAA,gBAAOA,CAAAC,GAAA,QAAAC,MAAA,CAAAC,YAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAC,YAAA,OAAAC,SAAA,CAChDN,MAAM,CAACO,KAAK,CAAGN,YAAY,CAACF,GAAG,CAAEG,KAAK,CAAEC,MAAM,CAAEC,OAAO,CAAEJ,MAAM,CAACO,KAAK,CAAC,CACtEF,YAAY,CAACL,MAAM,CAAC,CACtB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const useInterpolator = (factory, value, interpolator, input, output, options) => {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const init = (0, _react.useMemo)(() => factory(), []);\n    const result = _ReanimatedProxy.default.useSharedValue(init);\n    _ReanimatedProxy.default.useAnimatedReaction(function () {\n      const _e = [new global.Error(), -2, -27];\n      const interpolatorsJs4 = () => value.value;\n      interpolatorsJs4.__closure = {\n        value\n      };\n      interpolatorsJs4.__workletHash = 12180018494379;\n      interpolatorsJs4.__initData = _worklet_12180018494379_init_data;\n      interpolatorsJs4.__stackDetails = _e;\n      return interpolatorsJs4;\n    }(), function () {\n      const _e = [new global.Error(), -7, -27];\n      const interpolatorsJs5 = function (val) {\n        result.value = interpolator(val, input, output, options, result.value);\n        notifyChange(result);\n      };\n      interpolatorsJs5.__closure = {\n        result,\n        interpolator,\n        input,\n        output,\n        options,\n        notifyChange\n      };\n      interpolatorsJs5.__workletHash = 6113388669295;\n      interpolatorsJs5.__initData = _worklet_6113388669295_init_data;\n      interpolatorsJs5.__stackDetails = _e;\n      return interpolatorsJs5;\n    }(), [input, output, options]);\n    return result;\n  };\n  const usePathInterpolation = (value, input, outputRange, options) => {\n    // Check if all paths in outputRange are interpolable\n    const allPathsInterpolable = outputRange.slice(1).every(path => outputRange[0].isInterpolatable(path));\n    if (!allPathsInterpolable) {\n      // Handle the case where not all paths are interpolable\n      // For example, throw an error or return early\n      throw new Error(`Not all paths in the output range are interpolable.\nSee: https://shopify.github.io/react-native-skia/docs/animations/hooks#usepathinterpolation`);\n    }\n    return useInterpolator(() => _skia.Skia.Path.Make(), value, _animation.interpolatePaths, input, outputRange, options);\n  };\n  exports.usePathInterpolation = usePathInterpolation;\n  const useVectorInterpolation = (value, input, outputRange, options) => useInterpolator(() => _skia.Skia.Point(0, 0), value, _animation.interpolateVector, input, outputRange, options);\n  exports.useVectorInterpolation = useVectorInterpolation;\n});", "lineCount": 157, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "require"], [7, 22, 1, 0], [7, 23, 1, 0, "_dependencyMap"], [7, 37, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_animation"], [8, 16, 2, 0], [8, 19, 2, 0, "require"], [8, 26, 2, 0], [8, 27, 2, 0, "_dependencyMap"], [8, 41, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_skia"], [9, 11, 3, 0], [9, 14, 3, 0, "require"], [9, 21, 3, 0], [9, 22, 3, 0, "_dependencyMap"], [9, 36, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_Offscreen"], [10, 16, 4, 0], [10, 19, 4, 0, "require"], [10, 26, 4, 0], [10, 27, 4, 0, "_dependencyMap"], [10, 41, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_ReanimatedProxy"], [11, 22, 5, 0], [11, 25, 5, 0, "_interopRequireDefault"], [11, 47, 5, 0], [11, 48, 5, 0, "require"], [11, 55, 5, 0], [11, 56, 5, 0, "_dependencyMap"], [11, 70, 5, 0], [12, 2, 5, 36], [12, 8, 5, 36, "_worklet_7707193526369_init_data"], [12, 40, 5, 36], [13, 4, 5, 36, "code"], [13, 8, 5, 36], [14, 4, 5, 36, "location"], [14, 12, 5, 36], [15, 4, 5, 36, "sourceMap"], [15, 13, 5, 36], [16, 4, 5, 36, "version"], [16, 11, 5, 36], [17, 2, 5, 36], [18, 2, 6, 7], [18, 8, 6, 13, "notify<PERSON><PERSON><PERSON>"], [18, 20, 6, 25], [18, 23, 6, 25, "exports"], [18, 30, 6, 25], [18, 31, 6, 25, "notify<PERSON><PERSON><PERSON>"], [18, 43, 6, 25], [18, 46, 6, 28], [19, 4, 6, 28], [19, 10, 6, 28, "_e"], [19, 12, 6, 28], [19, 20, 6, 28, "global"], [19, 26, 6, 28], [19, 27, 6, 28, "Error"], [19, 32, 6, 28], [20, 4, 6, 28], [20, 10, 6, 28, "interpolatorsJs1"], [20, 26, 6, 28], [20, 38, 6, 28, "interpolatorsJs1"], [20, 39, 6, 28, "value"], [20, 44, 6, 33], [20, 46, 6, 37], [21, 6, 9, 2], [21, 10, 9, 6], [21, 14, 9, 6, "isOnMainThread"], [21, 39, 9, 20], [21, 41, 9, 21], [21, 42, 9, 22], [21, 44, 9, 24], [22, 8, 10, 4], [23, 8, 11, 4, "value"], [23, 13, 11, 9], [23, 14, 11, 10, "_value"], [23, 20, 11, 16], [23, 23, 11, 19, "value"], [23, 28, 11, 24], [23, 29, 11, 25, "value"], [23, 34, 11, 30], [24, 6, 12, 2], [25, 4, 13, 0], [25, 5, 13, 1], [26, 4, 13, 1, "interpolatorsJs1"], [26, 20, 13, 1], [26, 21, 13, 1, "__closure"], [26, 30, 13, 1], [27, 6, 13, 1, "isOnMainThread"], [27, 20, 13, 1], [27, 22, 9, 6, "isOnMainThread"], [28, 4, 9, 20], [29, 4, 9, 20, "interpolatorsJs1"], [29, 20, 9, 20], [29, 21, 9, 20, "__workletHash"], [29, 34, 9, 20], [30, 4, 9, 20, "interpolatorsJs1"], [30, 20, 9, 20], [30, 21, 9, 20, "__initData"], [30, 31, 9, 20], [30, 34, 9, 20, "_worklet_7707193526369_init_data"], [30, 66, 9, 20], [31, 4, 9, 20, "interpolatorsJs1"], [31, 20, 9, 20], [31, 21, 9, 20, "__stackDetails"], [31, 35, 9, 20], [31, 38, 9, 20, "_e"], [31, 40, 9, 20], [32, 4, 9, 20], [32, 11, 9, 20, "interpolatorsJs1"], [32, 27, 9, 20], [33, 2, 9, 20], [33, 3, 6, 28], [33, 5, 13, 1], [34, 2, 13, 2], [34, 8, 13, 2, "_worklet_2263978765805_init_data"], [34, 40, 13, 2], [35, 4, 13, 2, "code"], [35, 8, 13, 2], [36, 4, 13, 2, "location"], [36, 12, 13, 2], [37, 4, 13, 2, "sourceMap"], [37, 13, 13, 2], [38, 4, 13, 2, "version"], [38, 11, 13, 2], [39, 2, 13, 2], [40, 2, 14, 7], [40, 8, 14, 13, "usePathValue"], [40, 20, 14, 25], [40, 23, 14, 28, "usePathValue"], [40, 24, 14, 29, "cb"], [40, 26, 14, 31], [40, 28, 14, 33, "init"], [40, 32, 14, 37], [40, 37, 14, 42], [41, 4, 15, 2], [41, 10, 15, 8, "pathInit"], [41, 18, 15, 16], [41, 21, 15, 19], [41, 25, 15, 19, "useMemo"], [41, 39, 15, 26], [41, 41, 15, 27], [41, 47, 15, 33, "Skia"], [41, 57, 15, 37], [41, 58, 15, 38, "Path"], [41, 62, 15, 42], [41, 63, 15, 43, "Make"], [41, 67, 15, 47], [41, 68, 15, 48], [41, 69, 15, 49], [41, 71, 15, 51], [41, 73, 15, 53], [41, 74, 15, 54], [42, 4, 16, 2], [42, 10, 16, 8, "path"], [42, 14, 16, 12], [42, 17, 16, 15, "<PERSON><PERSON>"], [42, 41, 16, 18], [42, 42, 16, 19, "useSharedValue"], [42, 56, 16, 33], [42, 57, 16, 34, "pathInit"], [42, 65, 16, 42], [42, 66, 16, 43], [43, 4, 17, 2, "<PERSON><PERSON>"], [43, 28, 17, 5], [43, 29, 17, 6, "useDerivedValue"], [43, 44, 17, 21], [43, 45, 17, 22], [44, 6, 17, 22], [44, 12, 17, 22, "_e"], [44, 14, 17, 22], [44, 22, 17, 22, "global"], [44, 28, 17, 22], [44, 29, 17, 22, "Error"], [44, 34, 17, 22], [45, 6, 17, 22], [45, 12, 17, 22, "interpolatorsJs2"], [45, 28, 17, 22], [45, 40, 17, 22, "interpolatorsJs2"], [45, 41, 17, 22], [45, 43, 17, 28], [46, 8, 18, 4, "path"], [46, 12, 18, 8], [46, 13, 18, 9, "value"], [46, 18, 18, 14], [46, 19, 18, 15, "reset"], [46, 24, 18, 20], [46, 25, 18, 21], [46, 26, 18, 22], [47, 8, 19, 4], [47, 12, 19, 8, "init"], [47, 16, 19, 12], [47, 21, 19, 17, "undefined"], [47, 30, 19, 26], [47, 32, 19, 28], [48, 10, 20, 6, "path"], [48, 14, 20, 10], [48, 15, 20, 11, "value"], [48, 20, 20, 16], [48, 21, 20, 17, "addPath"], [48, 28, 20, 24], [48, 29, 20, 25, "init"], [48, 33, 20, 29], [48, 34, 20, 30], [49, 8, 21, 4], [50, 8, 22, 4, "cb"], [50, 10, 22, 6], [50, 11, 22, 7, "path"], [50, 15, 22, 11], [50, 16, 22, 12, "value"], [50, 21, 22, 17], [50, 22, 22, 18], [51, 8, 23, 4, "notify<PERSON><PERSON><PERSON>"], [51, 20, 23, 16], [51, 21, 23, 17, "path"], [51, 25, 23, 21], [51, 26, 23, 22], [52, 6, 24, 2], [52, 7, 24, 3], [53, 6, 24, 3, "interpolatorsJs2"], [53, 22, 24, 3], [53, 23, 24, 3, "__closure"], [53, 32, 24, 3], [54, 8, 24, 3, "path"], [54, 12, 24, 3], [55, 8, 24, 3, "init"], [55, 12, 24, 3], [56, 8, 24, 3, "cb"], [56, 10, 24, 3], [57, 8, 24, 3, "notify<PERSON><PERSON><PERSON>"], [58, 6, 24, 3], [59, 6, 24, 3, "interpolatorsJs2"], [59, 22, 24, 3], [59, 23, 24, 3, "__workletHash"], [59, 36, 24, 3], [60, 6, 24, 3, "interpolatorsJs2"], [60, 22, 24, 3], [60, 23, 24, 3, "__initData"], [60, 33, 24, 3], [60, 36, 24, 3, "_worklet_2263978765805_init_data"], [60, 68, 24, 3], [61, 6, 24, 3, "interpolatorsJs2"], [61, 22, 24, 3], [61, 23, 24, 3, "__stackDetails"], [61, 37, 24, 3], [61, 40, 24, 3, "_e"], [61, 42, 24, 3], [62, 6, 24, 3], [62, 13, 24, 3, "interpolatorsJs2"], [62, 29, 24, 3], [63, 4, 24, 3], [63, 5, 17, 22], [63, 7, 24, 3], [63, 8, 24, 4], [64, 4, 25, 2], [64, 11, 25, 9, "path"], [64, 15, 25, 13], [65, 2, 26, 0], [65, 3, 26, 1], [66, 2, 26, 2, "exports"], [66, 9, 26, 2], [66, 10, 26, 2, "usePathValue"], [66, 22, 26, 2], [66, 25, 26, 2, "usePathValue"], [66, 37, 26, 2], [67, 2, 26, 2], [67, 8, 26, 2, "_worklet_8748492839893_init_data"], [67, 40, 26, 2], [68, 4, 26, 2, "code"], [68, 8, 26, 2], [69, 4, 26, 2, "location"], [69, 12, 26, 2], [70, 4, 26, 2, "sourceMap"], [70, 13, 26, 2], [71, 4, 26, 2, "version"], [71, 11, 26, 2], [72, 2, 26, 2], [73, 2, 27, 7], [73, 8, 27, 13, "useClock"], [73, 16, 27, 21], [73, 19, 27, 24, "useClock"], [73, 20, 27, 24], [73, 25, 27, 30], [74, 4, 28, 2], [74, 10, 28, 8, "clock"], [74, 15, 28, 13], [74, 18, 28, 16, "<PERSON><PERSON>"], [74, 42, 28, 19], [74, 43, 28, 20, "useSharedValue"], [74, 57, 28, 34], [74, 58, 28, 35], [74, 59, 28, 36], [74, 60, 28, 37], [75, 4, 29, 2], [75, 10, 29, 8, "callback"], [75, 18, 29, 16], [75, 21, 29, 19], [75, 25, 29, 19, "useCallback"], [75, 43, 29, 30], [75, 45, 29, 31], [76, 6, 29, 31], [76, 12, 29, 31, "_e"], [76, 14, 29, 31], [76, 22, 29, 31, "global"], [76, 28, 29, 31], [76, 29, 29, 31, "Error"], [76, 34, 29, 31], [77, 6, 29, 31], [77, 12, 29, 31, "interpolatorsJs3"], [77, 28, 29, 31], [77, 40, 29, 31, "interpolatorsJs3"], [77, 41, 29, 31, "info"], [77, 45, 29, 35], [77, 47, 29, 39], [78, 8, 32, 4, "clock"], [78, 13, 32, 9], [78, 14, 32, 10, "value"], [78, 19, 32, 15], [78, 22, 32, 18, "info"], [78, 26, 32, 22], [78, 27, 32, 23, "timeSinceFirstFrame"], [78, 46, 32, 42], [79, 6, 33, 2], [79, 7, 33, 3], [80, 6, 33, 3, "interpolatorsJs3"], [80, 22, 33, 3], [80, 23, 33, 3, "__closure"], [80, 32, 33, 3], [81, 8, 33, 3, "clock"], [82, 6, 33, 3], [83, 6, 33, 3, "interpolatorsJs3"], [83, 22, 33, 3], [83, 23, 33, 3, "__workletHash"], [83, 36, 33, 3], [84, 6, 33, 3, "interpolatorsJs3"], [84, 22, 33, 3], [84, 23, 33, 3, "__initData"], [84, 33, 33, 3], [84, 36, 33, 3, "_worklet_8748492839893_init_data"], [84, 68, 33, 3], [85, 6, 33, 3, "interpolatorsJs3"], [85, 22, 33, 3], [85, 23, 33, 3, "__stackDetails"], [85, 37, 33, 3], [85, 40, 33, 3, "_e"], [85, 42, 33, 3], [86, 6, 33, 3], [86, 13, 33, 3, "interpolatorsJs3"], [86, 29, 33, 3], [87, 4, 33, 3], [87, 5, 29, 31], [87, 9, 33, 5], [87, 10, 33, 6, "clock"], [87, 15, 33, 11], [87, 16, 33, 12], [87, 17, 33, 13], [88, 4, 34, 2, "<PERSON><PERSON>"], [88, 28, 34, 5], [88, 29, 34, 6, "useFrameCallback"], [88, 45, 34, 22], [88, 46, 34, 23, "callback"], [88, 54, 34, 31], [88, 55, 34, 32], [89, 4, 35, 2], [89, 11, 35, 9, "clock"], [89, 16, 35, 14], [90, 2, 36, 0], [90, 3, 36, 1], [92, 2, 38, 0], [93, 0, 39, 0], [94, 0, 40, 0], [95, 2, 38, 0, "exports"], [95, 9, 38, 0], [95, 10, 38, 0, "useClock"], [95, 18, 38, 0], [95, 21, 38, 0, "useClock"], [95, 29, 38, 0], [96, 2, 38, 0], [96, 8, 38, 0, "_worklet_12180018494379_init_data"], [96, 41, 38, 0], [97, 4, 38, 0, "code"], [97, 8, 38, 0], [98, 4, 38, 0, "location"], [98, 12, 38, 0], [99, 4, 38, 0, "sourceMap"], [99, 13, 38, 0], [100, 4, 38, 0, "version"], [100, 11, 38, 0], [101, 2, 38, 0], [102, 2, 38, 0], [102, 8, 38, 0, "_worklet_6113388669295_init_data"], [102, 40, 38, 0], [103, 4, 38, 0, "code"], [103, 8, 38, 0], [104, 4, 38, 0, "location"], [104, 12, 38, 0], [105, 4, 38, 0, "sourceMap"], [105, 13, 38, 0], [106, 4, 38, 0, "version"], [106, 11, 38, 0], [107, 2, 38, 0], [108, 2, 42, 0], [108, 8, 42, 6, "useInterpolator"], [108, 23, 42, 21], [108, 26, 42, 24, "useInterpolator"], [108, 27, 42, 25, "factory"], [108, 34, 42, 32], [108, 36, 42, 34, "value"], [108, 41, 42, 39], [108, 43, 42, 41, "interpolator"], [108, 55, 42, 53], [108, 57, 42, 55, "input"], [108, 62, 42, 60], [108, 64, 42, 62, "output"], [108, 70, 42, 68], [108, 72, 42, 70, "options"], [108, 79, 42, 77], [108, 84, 42, 82], [109, 4, 43, 2], [110, 4, 44, 2], [110, 10, 44, 8, "init"], [110, 14, 44, 12], [110, 17, 44, 15], [110, 21, 44, 15, "useMemo"], [110, 35, 44, 22], [110, 37, 44, 23], [110, 43, 44, 29, "factory"], [110, 50, 44, 36], [110, 51, 44, 37], [110, 52, 44, 38], [110, 54, 44, 40], [110, 56, 44, 42], [110, 57, 44, 43], [111, 4, 45, 2], [111, 10, 45, 8, "result"], [111, 16, 45, 14], [111, 19, 45, 17, "<PERSON><PERSON>"], [111, 43, 45, 20], [111, 44, 45, 21, "useSharedValue"], [111, 58, 45, 35], [111, 59, 45, 36, "init"], [111, 63, 45, 40], [111, 64, 45, 41], [112, 4, 46, 2, "<PERSON><PERSON>"], [112, 28, 46, 5], [112, 29, 46, 6, "useAnimatedReaction"], [112, 48, 46, 25], [112, 49, 46, 26], [113, 6, 46, 26], [113, 12, 46, 26, "_e"], [113, 14, 46, 26], [113, 22, 46, 26, "global"], [113, 28, 46, 26], [113, 29, 46, 26, "Error"], [113, 34, 46, 26], [114, 6, 46, 26], [114, 12, 46, 26, "interpolatorsJs4"], [114, 28, 46, 26], [114, 31, 46, 26, "interpolatorsJs4"], [114, 32, 46, 26], [114, 37, 46, 32, "value"], [114, 42, 46, 37], [114, 43, 46, 38, "value"], [114, 48, 46, 43], [115, 6, 46, 43, "interpolatorsJs4"], [115, 22, 46, 43], [115, 23, 46, 43, "__closure"], [115, 32, 46, 43], [116, 8, 46, 43, "value"], [117, 6, 46, 43], [118, 6, 46, 43, "interpolatorsJs4"], [118, 22, 46, 43], [118, 23, 46, 43, "__workletHash"], [118, 36, 46, 43], [119, 6, 46, 43, "interpolatorsJs4"], [119, 22, 46, 43], [119, 23, 46, 43, "__initData"], [119, 33, 46, 43], [119, 36, 46, 43, "_worklet_12180018494379_init_data"], [119, 69, 46, 43], [120, 6, 46, 43, "interpolatorsJs4"], [120, 22, 46, 43], [120, 23, 46, 43, "__stackDetails"], [120, 37, 46, 43], [120, 40, 46, 43, "_e"], [120, 42, 46, 43], [121, 6, 46, 43], [121, 13, 46, 43, "interpolatorsJs4"], [121, 29, 46, 43], [122, 4, 46, 43], [122, 5, 46, 26], [122, 9, 46, 45], [123, 6, 46, 45], [123, 12, 46, 45, "_e"], [123, 14, 46, 45], [123, 22, 46, 45, "global"], [123, 28, 46, 45], [123, 29, 46, 45, "Error"], [123, 34, 46, 45], [124, 6, 46, 45], [124, 12, 46, 45, "interpolatorsJs5"], [124, 28, 46, 45], [124, 40, 46, 45, "interpolatorsJs5"], [124, 41, 46, 45, "val"], [124, 44, 46, 48], [124, 46, 46, 52], [125, 8, 47, 4, "result"], [125, 14, 47, 10], [125, 15, 47, 11, "value"], [125, 20, 47, 16], [125, 23, 47, 19, "interpolator"], [125, 35, 47, 31], [125, 36, 47, 32, "val"], [125, 39, 47, 35], [125, 41, 47, 37, "input"], [125, 46, 47, 42], [125, 48, 47, 44, "output"], [125, 54, 47, 50], [125, 56, 47, 52, "options"], [125, 63, 47, 59], [125, 65, 47, 61, "result"], [125, 71, 47, 67], [125, 72, 47, 68, "value"], [125, 77, 47, 73], [125, 78, 47, 74], [126, 8, 48, 4, "notify<PERSON><PERSON><PERSON>"], [126, 20, 48, 16], [126, 21, 48, 17, "result"], [126, 27, 48, 23], [126, 28, 48, 24], [127, 6, 49, 2], [127, 7, 49, 3], [128, 6, 49, 3, "interpolatorsJs5"], [128, 22, 49, 3], [128, 23, 49, 3, "__closure"], [128, 32, 49, 3], [129, 8, 49, 3, "result"], [129, 14, 49, 3], [130, 8, 49, 3, "interpolator"], [130, 20, 49, 3], [131, 8, 49, 3, "input"], [131, 13, 49, 3], [132, 8, 49, 3, "output"], [132, 14, 49, 3], [133, 8, 49, 3, "options"], [133, 15, 49, 3], [134, 8, 49, 3, "notify<PERSON><PERSON><PERSON>"], [135, 6, 49, 3], [136, 6, 49, 3, "interpolatorsJs5"], [136, 22, 49, 3], [136, 23, 49, 3, "__workletHash"], [136, 36, 49, 3], [137, 6, 49, 3, "interpolatorsJs5"], [137, 22, 49, 3], [137, 23, 49, 3, "__initData"], [137, 33, 49, 3], [137, 36, 49, 3, "_worklet_6113388669295_init_data"], [137, 68, 49, 3], [138, 6, 49, 3, "interpolatorsJs5"], [138, 22, 49, 3], [138, 23, 49, 3, "__stackDetails"], [138, 37, 49, 3], [138, 40, 49, 3, "_e"], [138, 42, 49, 3], [139, 6, 49, 3], [139, 13, 49, 3, "interpolatorsJs5"], [139, 29, 49, 3], [140, 4, 49, 3], [140, 5, 46, 45], [140, 9, 49, 5], [140, 10, 49, 6, "input"], [140, 15, 49, 11], [140, 17, 49, 13, "output"], [140, 23, 49, 19], [140, 25, 49, 21, "options"], [140, 32, 49, 28], [140, 33, 49, 29], [140, 34, 49, 30], [141, 4, 50, 2], [141, 11, 50, 9, "result"], [141, 17, 50, 15], [142, 2, 51, 0], [142, 3, 51, 1], [143, 2, 52, 7], [143, 8, 52, 13, "usePathInterpolation"], [143, 28, 52, 33], [143, 31, 52, 36, "usePathInterpolation"], [143, 32, 52, 37, "value"], [143, 37, 52, 42], [143, 39, 52, 44, "input"], [143, 44, 52, 49], [143, 46, 52, 51, "outputRange"], [143, 57, 52, 62], [143, 59, 52, 64, "options"], [143, 66, 52, 71], [143, 71, 52, 76], [144, 4, 53, 2], [145, 4, 54, 2], [145, 10, 54, 8, "allPathsInterpolable"], [145, 30, 54, 28], [145, 33, 54, 31, "outputRange"], [145, 44, 54, 42], [145, 45, 54, 43, "slice"], [145, 50, 54, 48], [145, 51, 54, 49], [145, 52, 54, 50], [145, 53, 54, 51], [145, 54, 54, 52, "every"], [145, 59, 54, 57], [145, 60, 54, 58, "path"], [145, 64, 54, 62], [145, 68, 54, 66, "outputRange"], [145, 79, 54, 77], [145, 80, 54, 78], [145, 81, 54, 79], [145, 82, 54, 80], [145, 83, 54, 81, "isInterpolatable"], [145, 99, 54, 97], [145, 100, 54, 98, "path"], [145, 104, 54, 102], [145, 105, 54, 103], [145, 106, 54, 104], [146, 4, 55, 2], [146, 8, 55, 6], [146, 9, 55, 7, "allPathsInterpolable"], [146, 29, 55, 27], [146, 31, 55, 29], [147, 6, 56, 4], [148, 6, 57, 4], [149, 6, 58, 4], [149, 12, 58, 10], [149, 16, 58, 14, "Error"], [149, 21, 58, 19], [149, 22, 58, 20], [150, 0, 59, 0], [150, 92, 59, 92], [150, 93, 59, 93], [151, 4, 60, 2], [152, 4, 61, 2], [152, 11, 61, 9, "useInterpolator"], [152, 26, 61, 24], [152, 27, 61, 25], [152, 33, 61, 31, "Skia"], [152, 43, 61, 35], [152, 44, 61, 36, "Path"], [152, 48, 61, 40], [152, 49, 61, 41, "Make"], [152, 53, 61, 45], [152, 54, 61, 46], [152, 55, 61, 47], [152, 57, 61, 49, "value"], [152, 62, 61, 54], [152, 64, 61, 56, "interpolatePaths"], [152, 91, 61, 72], [152, 93, 61, 74, "input"], [152, 98, 61, 79], [152, 100, 61, 81, "outputRange"], [152, 111, 61, 92], [152, 113, 61, 94, "options"], [152, 120, 61, 101], [152, 121, 61, 102], [153, 2, 62, 0], [153, 3, 62, 1], [154, 2, 62, 2, "exports"], [154, 9, 62, 2], [154, 10, 62, 2, "usePathInterpolation"], [154, 30, 62, 2], [154, 33, 62, 2, "usePathInterpolation"], [154, 53, 62, 2], [155, 2, 63, 7], [155, 8, 63, 13, "useVectorInterpolation"], [155, 30, 63, 35], [155, 33, 63, 38, "useVectorInterpolation"], [155, 34, 63, 39, "value"], [155, 39, 63, 44], [155, 41, 63, 46, "input"], [155, 46, 63, 51], [155, 48, 63, 53, "outputRange"], [155, 59, 63, 64], [155, 61, 63, 66, "options"], [155, 68, 63, 73], [155, 73, 63, 78, "useInterpolator"], [155, 88, 63, 93], [155, 89, 63, 94], [155, 95, 63, 100, "Skia"], [155, 105, 63, 104], [155, 106, 63, 105, "Point"], [155, 111, 63, 110], [155, 112, 63, 111], [155, 113, 63, 112], [155, 115, 63, 114], [155, 116, 63, 115], [155, 117, 63, 116], [155, 119, 63, 118, "value"], [155, 124, 63, 123], [155, 126, 63, 125, "interpolateVector"], [155, 154, 63, 142], [155, 156, 63, 144, "input"], [155, 161, 63, 149], [155, 163, 63, 151, "outputRange"], [155, 174, 63, 162], [155, 176, 63, 164, "options"], [155, 183, 63, 171], [155, 184, 63, 172], [156, 2, 63, 173, "exports"], [156, 9, 63, 173], [156, 10, 63, 173, "useVectorInterpolation"], [156, 32, 63, 173], [156, 35, 63, 173, "useVectorInterpolation"], [156, 57, 63, 173], [157, 0, 63, 173], [157, 3]], "functionMap": {"names": ["<global>", "notify<PERSON><PERSON><PERSON>", "usePathValue", "useMemo$argument_0", "Rea.useDerivedValue$argument_0", "useClock", "callback", "useInterpolator", "Rea.useAnimatedReaction$argument_0", "Rea.useAnimatedReaction$argument_1", "usePathInterpolation", "outputRange.slice.every$argument_0", "useInterpolator$argument_0", "useVectorInterpolation"], "mappings": "AAA;4BCK;CDO;4BEC;2BCC,sBD;sBEE;GFO;CFE;wBKC;+BCE;GDI;CLG;wBOM;uBJE,eI;0BCE,iBD,EE;GFG;CPE;oCUC;0DCE,6CD;yBEO,sBF;CVC;sCaC,wDD,sBC,wDb"}}, "type": "js/module"}]}