{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./CircularBuffer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 46, "index": 257}}], "key": "uVgm5B14gvsefJ8WOcz2JJQif+Q=", "exportNames": ["*"]}}, {"name": "./LeastSquareSolver", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 258}, "end": {"line": 4, "column": 52, "index": 310}}], "key": "pL+OKV6zXpveMrqrg91kFAGJP/w=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _CircularBuffer = _interopRequireDefault(require(_dependencyMap[1], \"./CircularBuffer\"));\n  var _LeastSquareSolver = _interopRequireDefault(require(_dependencyMap[2], \"./LeastSquareSolver\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class VelocityTracker {\n    constructor() {\n      _defineProperty(this, \"assumePointerMoveStoppedMilliseconds\", 40);\n      _defineProperty(this, \"historySize\", 20);\n      _defineProperty(this, \"horizonMilliseconds\", 300);\n      _defineProperty(this, \"minSampleSize\", 3);\n      _defineProperty(this, \"samples\", void 0);\n      this.samples = new _CircularBuffer.default(this.historySize);\n    }\n    add(event) {\n      this.samples.push(event);\n    } // Returns an estimate of the velocity of the object being tracked by the\n    // tracker given the current information available to the tracker.\n    //\n    // Information is added using [addPosition].\n    //\n    // Returns null if there is no data on which to base an estimate.\n\n    getVelocityEstimate() {\n      const x = [];\n      const y = [];\n      const w = [];\n      const time = [];\n      let sampleCount = 0;\n      let index = this.samples.size - 1;\n      const newestSample = this.samples.get(index);\n      if (!newestSample) {\n        return null;\n      }\n      let previousSample = newestSample; // Starting with the most recent PointAtTime sample, iterate backwards while\n      // the samples represent continuous motion.\n\n      while (sampleCount < this.samples.size) {\n        const sample = this.samples.get(index);\n        const age = newestSample.time - sample.time;\n        const delta = Math.abs(sample.time - previousSample.time);\n        previousSample = sample;\n        if (age > this.horizonMilliseconds || delta > this.assumePointerMoveStoppedMilliseconds) {\n          break;\n        }\n        x.push(sample.x);\n        y.push(sample.y);\n        w.push(1);\n        time.push(-age);\n        sampleCount++;\n        index--;\n      }\n      if (sampleCount >= this.minSampleSize) {\n        const xSolver = new _LeastSquareSolver.default(time, x, w);\n        const xFit = xSolver.solve(2);\n        if (xFit !== null) {\n          const ySolver = new _LeastSquareSolver.default(time, y, w);\n          const yFit = ySolver.solve(2);\n          if (yFit !== null) {\n            const xVelocity = xFit.coefficients[1] * 1000;\n            const yVelocity = yFit.coefficients[1] * 1000;\n            return [xVelocity, yVelocity];\n          }\n        }\n      }\n      return null;\n    }\n    get velocity() {\n      const estimate = this.getVelocityEstimate();\n      if (estimate !== null) {\n        return estimate;\n      }\n      return [0, 0];\n    }\n    reset() {\n      this.samples.clear();\n    }\n  }\n  exports.default = VelocityTracker;\n});", "lineCount": 96, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_CircularBuffer"], [7, 21, 3, 0], [7, 24, 3, 0, "_interopRequireDefault"], [7, 46, 3, 0], [7, 47, 3, 0, "require"], [7, 54, 3, 0], [7, 55, 3, 0, "_dependencyMap"], [7, 69, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_LeastSquareSolver"], [8, 24, 4, 0], [8, 27, 4, 0, "_interopRequireDefault"], [8, 49, 4, 0], [8, 50, 4, 0, "require"], [8, 57, 4, 0], [8, 58, 4, 0, "_dependencyMap"], [8, 72, 4, 0], [9, 2, 1, 0], [9, 11, 1, 9, "_defineProperty"], [9, 26, 1, 24, "_defineProperty"], [9, 27, 1, 25, "obj"], [9, 30, 1, 28], [9, 32, 1, 30, "key"], [9, 35, 1, 33], [9, 37, 1, 35, "value"], [9, 42, 1, 40], [9, 44, 1, 42], [10, 4, 1, 44], [10, 8, 1, 48, "key"], [10, 11, 1, 51], [10, 15, 1, 55, "obj"], [10, 18, 1, 58], [10, 20, 1, 60], [11, 6, 1, 62, "Object"], [11, 12, 1, 68], [11, 13, 1, 69, "defineProperty"], [11, 27, 1, 83], [11, 28, 1, 84, "obj"], [11, 31, 1, 87], [11, 33, 1, 89, "key"], [11, 36, 1, 92], [11, 38, 1, 94], [12, 8, 1, 96, "value"], [12, 13, 1, 101], [12, 15, 1, 103, "value"], [12, 20, 1, 108], [13, 8, 1, 110, "enumerable"], [13, 18, 1, 120], [13, 20, 1, 122], [13, 24, 1, 126], [14, 8, 1, 128, "configurable"], [14, 20, 1, 140], [14, 22, 1, 142], [14, 26, 1, 146], [15, 8, 1, 148, "writable"], [15, 16, 1, 156], [15, 18, 1, 158], [16, 6, 1, 163], [16, 7, 1, 164], [16, 8, 1, 165], [17, 4, 1, 167], [17, 5, 1, 168], [17, 11, 1, 174], [18, 6, 1, 176, "obj"], [18, 9, 1, 179], [18, 10, 1, 180, "key"], [18, 13, 1, 183], [18, 14, 1, 184], [18, 17, 1, 187, "value"], [18, 22, 1, 192], [19, 4, 1, 194], [20, 4, 1, 196], [20, 11, 1, 203, "obj"], [20, 14, 1, 206], [21, 2, 1, 208], [22, 2, 5, 15], [22, 8, 5, 21, "VelocityTracker"], [22, 23, 5, 36], [22, 24, 5, 37], [23, 4, 6, 2, "constructor"], [23, 15, 6, 13, "constructor"], [23, 16, 6, 13], [23, 18, 6, 16], [24, 6, 7, 4, "_defineProperty"], [24, 21, 7, 19], [24, 22, 7, 20], [24, 26, 7, 24], [24, 28, 7, 26], [24, 66, 7, 64], [24, 68, 7, 66], [24, 70, 7, 68], [24, 71, 7, 69], [25, 6, 9, 4, "_defineProperty"], [25, 21, 9, 19], [25, 22, 9, 20], [25, 26, 9, 24], [25, 28, 9, 26], [25, 41, 9, 39], [25, 43, 9, 41], [25, 45, 9, 43], [25, 46, 9, 44], [26, 6, 11, 4, "_defineProperty"], [26, 21, 11, 19], [26, 22, 11, 20], [26, 26, 11, 24], [26, 28, 11, 26], [26, 49, 11, 47], [26, 51, 11, 49], [26, 54, 11, 52], [26, 55, 11, 53], [27, 6, 13, 4, "_defineProperty"], [27, 21, 13, 19], [27, 22, 13, 20], [27, 26, 13, 24], [27, 28, 13, 26], [27, 43, 13, 41], [27, 45, 13, 43], [27, 46, 13, 44], [27, 47, 13, 45], [28, 6, 15, 4, "_defineProperty"], [28, 21, 15, 19], [28, 22, 15, 20], [28, 26, 15, 24], [28, 28, 15, 26], [28, 37, 15, 35], [28, 39, 15, 37], [28, 44, 15, 42], [28, 45, 15, 43], [28, 46, 15, 44], [29, 6, 17, 4], [29, 10, 17, 8], [29, 11, 17, 9, "samples"], [29, 18, 17, 16], [29, 21, 17, 19], [29, 25, 17, 23, "Circular<PERSON><PERSON>er"], [29, 48, 17, 37], [29, 49, 17, 38], [29, 53, 17, 42], [29, 54, 17, 43, "historySize"], [29, 65, 17, 54], [29, 66, 17, 55], [30, 4, 18, 2], [31, 4, 20, 2, "add"], [31, 7, 20, 5, "add"], [31, 8, 20, 6, "event"], [31, 13, 20, 11], [31, 15, 20, 13], [32, 6, 21, 4], [32, 10, 21, 8], [32, 11, 21, 9, "samples"], [32, 18, 21, 16], [32, 19, 21, 17, "push"], [32, 23, 21, 21], [32, 24, 21, 22, "event"], [32, 29, 21, 27], [32, 30, 21, 28], [33, 4, 22, 2], [33, 5, 22, 3], [33, 6, 22, 4], [34, 4, 23, 2], [35, 4, 24, 2], [36, 4, 25, 2], [37, 4, 26, 2], [38, 4, 27, 2], [40, 4, 30, 2, "getVelocityEstimate"], [40, 23, 30, 21, "getVelocityEstimate"], [40, 24, 30, 21], [40, 26, 30, 24], [41, 6, 31, 4], [41, 12, 31, 10, "x"], [41, 13, 31, 11], [41, 16, 31, 14], [41, 18, 31, 16], [42, 6, 32, 4], [42, 12, 32, 10, "y"], [42, 13, 32, 11], [42, 16, 32, 14], [42, 18, 32, 16], [43, 6, 33, 4], [43, 12, 33, 10, "w"], [43, 13, 33, 11], [43, 16, 33, 14], [43, 18, 33, 16], [44, 6, 34, 4], [44, 12, 34, 10, "time"], [44, 16, 34, 14], [44, 19, 34, 17], [44, 21, 34, 19], [45, 6, 35, 4], [45, 10, 35, 8, "sampleCount"], [45, 21, 35, 19], [45, 24, 35, 22], [45, 25, 35, 23], [46, 6, 36, 4], [46, 10, 36, 8, "index"], [46, 15, 36, 13], [46, 18, 36, 16], [46, 22, 36, 20], [46, 23, 36, 21, "samples"], [46, 30, 36, 28], [46, 31, 36, 29, "size"], [46, 35, 36, 33], [46, 38, 36, 36], [46, 39, 36, 37], [47, 6, 37, 4], [47, 12, 37, 10, "newestSample"], [47, 24, 37, 22], [47, 27, 37, 25], [47, 31, 37, 29], [47, 32, 37, 30, "samples"], [47, 39, 37, 37], [47, 40, 37, 38, "get"], [47, 43, 37, 41], [47, 44, 37, 42, "index"], [47, 49, 37, 47], [47, 50, 37, 48], [48, 6, 39, 4], [48, 10, 39, 8], [48, 11, 39, 9, "newestSample"], [48, 23, 39, 21], [48, 25, 39, 23], [49, 8, 40, 6], [49, 15, 40, 13], [49, 19, 40, 17], [50, 6, 41, 4], [51, 6, 43, 4], [51, 10, 43, 8, "previousSample"], [51, 24, 43, 22], [51, 27, 43, 25, "newestSample"], [51, 39, 43, 37], [51, 40, 43, 38], [51, 41, 43, 39], [52, 6, 44, 4], [54, 6, 46, 4], [54, 13, 46, 11, "sampleCount"], [54, 24, 46, 22], [54, 27, 46, 25], [54, 31, 46, 29], [54, 32, 46, 30, "samples"], [54, 39, 46, 37], [54, 40, 46, 38, "size"], [54, 44, 46, 42], [54, 46, 46, 44], [55, 8, 47, 6], [55, 14, 47, 12, "sample"], [55, 20, 47, 18], [55, 23, 47, 21], [55, 27, 47, 25], [55, 28, 47, 26, "samples"], [55, 35, 47, 33], [55, 36, 47, 34, "get"], [55, 39, 47, 37], [55, 40, 47, 38, "index"], [55, 45, 47, 43], [55, 46, 47, 44], [56, 8, 48, 6], [56, 14, 48, 12, "age"], [56, 17, 48, 15], [56, 20, 48, 18, "newestSample"], [56, 32, 48, 30], [56, 33, 48, 31, "time"], [56, 37, 48, 35], [56, 40, 48, 38, "sample"], [56, 46, 48, 44], [56, 47, 48, 45, "time"], [56, 51, 48, 49], [57, 8, 49, 6], [57, 14, 49, 12, "delta"], [57, 19, 49, 17], [57, 22, 49, 20, "Math"], [57, 26, 49, 24], [57, 27, 49, 25, "abs"], [57, 30, 49, 28], [57, 31, 49, 29, "sample"], [57, 37, 49, 35], [57, 38, 49, 36, "time"], [57, 42, 49, 40], [57, 45, 49, 43, "previousSample"], [57, 59, 49, 57], [57, 60, 49, 58, "time"], [57, 64, 49, 62], [57, 65, 49, 63], [58, 8, 50, 6, "previousSample"], [58, 22, 50, 20], [58, 25, 50, 23, "sample"], [58, 31, 50, 29], [59, 8, 52, 6], [59, 12, 52, 10, "age"], [59, 15, 52, 13], [59, 18, 52, 16], [59, 22, 52, 20], [59, 23, 52, 21, "horizonMilliseconds"], [59, 42, 52, 40], [59, 46, 52, 44, "delta"], [59, 51, 52, 49], [59, 54, 52, 52], [59, 58, 52, 56], [59, 59, 52, 57, "assumePointerMoveStoppedMilliseconds"], [59, 95, 52, 93], [59, 97, 52, 95], [60, 10, 53, 8], [61, 8, 54, 6], [62, 8, 56, 6, "x"], [62, 9, 56, 7], [62, 10, 56, 8, "push"], [62, 14, 56, 12], [62, 15, 56, 13, "sample"], [62, 21, 56, 19], [62, 22, 56, 20, "x"], [62, 23, 56, 21], [62, 24, 56, 22], [63, 8, 57, 6, "y"], [63, 9, 57, 7], [63, 10, 57, 8, "push"], [63, 14, 57, 12], [63, 15, 57, 13, "sample"], [63, 21, 57, 19], [63, 22, 57, 20, "y"], [63, 23, 57, 21], [63, 24, 57, 22], [64, 8, 58, 6, "w"], [64, 9, 58, 7], [64, 10, 58, 8, "push"], [64, 14, 58, 12], [64, 15, 58, 13], [64, 16, 58, 14], [64, 17, 58, 15], [65, 8, 59, 6, "time"], [65, 12, 59, 10], [65, 13, 59, 11, "push"], [65, 17, 59, 15], [65, 18, 59, 16], [65, 19, 59, 17, "age"], [65, 22, 59, 20], [65, 23, 59, 21], [66, 8, 60, 6, "sampleCount"], [66, 19, 60, 17], [66, 21, 60, 19], [67, 8, 61, 6, "index"], [67, 13, 61, 11], [67, 15, 61, 13], [68, 6, 62, 4], [69, 6, 64, 4], [69, 10, 64, 8, "sampleCount"], [69, 21, 64, 19], [69, 25, 64, 23], [69, 29, 64, 27], [69, 30, 64, 28, "minSampleSize"], [69, 43, 64, 41], [69, 45, 64, 43], [70, 8, 65, 6], [70, 14, 65, 12, "xSolver"], [70, 21, 65, 19], [70, 24, 65, 22], [70, 28, 65, 26, "LeastSquareSolver"], [70, 54, 65, 43], [70, 55, 65, 44, "time"], [70, 59, 65, 48], [70, 61, 65, 50, "x"], [70, 62, 65, 51], [70, 64, 65, 53, "w"], [70, 65, 65, 54], [70, 66, 65, 55], [71, 8, 66, 6], [71, 14, 66, 12, "xFit"], [71, 18, 66, 16], [71, 21, 66, 19, "xSolver"], [71, 28, 66, 26], [71, 29, 66, 27, "solve"], [71, 34, 66, 32], [71, 35, 66, 33], [71, 36, 66, 34], [71, 37, 66, 35], [72, 8, 68, 6], [72, 12, 68, 10, "xFit"], [72, 16, 68, 14], [72, 21, 68, 19], [72, 25, 68, 23], [72, 27, 68, 25], [73, 10, 69, 8], [73, 16, 69, 14, "ySolver"], [73, 23, 69, 21], [73, 26, 69, 24], [73, 30, 69, 28, "LeastSquareSolver"], [73, 56, 69, 45], [73, 57, 69, 46, "time"], [73, 61, 69, 50], [73, 63, 69, 52, "y"], [73, 64, 69, 53], [73, 66, 69, 55, "w"], [73, 67, 69, 56], [73, 68, 69, 57], [74, 10, 70, 8], [74, 16, 70, 14, "yFit"], [74, 20, 70, 18], [74, 23, 70, 21, "ySolver"], [74, 30, 70, 28], [74, 31, 70, 29, "solve"], [74, 36, 70, 34], [74, 37, 70, 35], [74, 38, 70, 36], [74, 39, 70, 37], [75, 10, 72, 8], [75, 14, 72, 12, "yFit"], [75, 18, 72, 16], [75, 23, 72, 21], [75, 27, 72, 25], [75, 29, 72, 27], [76, 12, 73, 10], [76, 18, 73, 16, "xVelocity"], [76, 27, 73, 25], [76, 30, 73, 28, "xFit"], [76, 34, 73, 32], [76, 35, 73, 33, "coefficients"], [76, 47, 73, 45], [76, 48, 73, 46], [76, 49, 73, 47], [76, 50, 73, 48], [76, 53, 73, 51], [76, 57, 73, 55], [77, 12, 74, 10], [77, 18, 74, 16, "yVelocity"], [77, 27, 74, 25], [77, 30, 74, 28, "yFit"], [77, 34, 74, 32], [77, 35, 74, 33, "coefficients"], [77, 47, 74, 45], [77, 48, 74, 46], [77, 49, 74, 47], [77, 50, 74, 48], [77, 53, 74, 51], [77, 57, 74, 55], [78, 12, 75, 10], [78, 19, 75, 17], [78, 20, 75, 18, "xVelocity"], [78, 29, 75, 27], [78, 31, 75, 29, "yVelocity"], [78, 40, 75, 38], [78, 41, 75, 39], [79, 10, 76, 8], [80, 8, 77, 6], [81, 6, 78, 4], [82, 6, 80, 4], [82, 13, 80, 11], [82, 17, 80, 15], [83, 4, 81, 2], [84, 4, 83, 2], [84, 8, 83, 6, "velocity"], [84, 16, 83, 14, "velocity"], [84, 17, 83, 14], [84, 19, 83, 17], [85, 6, 84, 4], [85, 12, 84, 10, "estimate"], [85, 20, 84, 18], [85, 23, 84, 21], [85, 27, 84, 25], [85, 28, 84, 26, "getVelocityEstimate"], [85, 47, 84, 45], [85, 48, 84, 46], [85, 49, 84, 47], [86, 6, 86, 4], [86, 10, 86, 8, "estimate"], [86, 18, 86, 16], [86, 23, 86, 21], [86, 27, 86, 25], [86, 29, 86, 27], [87, 8, 87, 6], [87, 15, 87, 13, "estimate"], [87, 23, 87, 21], [88, 6, 88, 4], [89, 6, 90, 4], [89, 13, 90, 11], [89, 14, 90, 12], [89, 15, 90, 13], [89, 17, 90, 15], [89, 18, 90, 16], [89, 19, 90, 17], [90, 4, 91, 2], [91, 4, 93, 2, "reset"], [91, 9, 93, 7, "reset"], [91, 10, 93, 7], [91, 12, 93, 10], [92, 6, 94, 4], [92, 10, 94, 8], [92, 11, 94, 9, "samples"], [92, 18, 94, 16], [92, 19, 94, 17, "clear"], [92, 24, 94, 22], [92, 25, 94, 23], [92, 26, 94, 24], [93, 4, 95, 2], [94, 2, 97, 0], [95, 2, 97, 1, "exports"], [95, 9, 97, 1], [95, 10, 97, 1, "default"], [95, 17, 97, 1], [95, 20, 97, 1, "VelocityTracker"], [95, 35, 97, 1], [96, 0, 97, 1], [96, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "VelocityTracker", "constructor", "add", "getVelocityEstimate", "get__velocity", "reset"], "mappings": "AAA,iNC;eCI;ECC;GDY;EEE;GFE;EGQ;GHmD;EIE;GJQ;EKE;GLE;CDE"}}, "type": "js/module"}]}