{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      console.log(`[EchoCameraWeb] 🎨 STARTING BLUR: region (${Math.round(x)}, ${Math.round(y)}) ${Math.round(width)}x${Math.round(height)}`);\n\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      console.log(`[EchoCameraWeb] 📐 CLAMPED REGION: (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.error(`[EchoCameraWeb] ❌ INVALID BLUR REGION: width=${clampedWidth}, height=${clampedHeight}`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n      console.log(`[EchoCameraWeb] 🎨 Applying strong blur to face region...`);\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur passes...`);\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n      console.log(`[EchoCameraWeb] ✅ BLUR COMPLETE: Applied to (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 951,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 942,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 987,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 968,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1011,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 995,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1031,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1640, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4, "console"], [303, 13, 335, 11], [303, 14, 335, 12, "log"], [303, 17, 335, 15], [303, 18, 335, 16], [303, 63, 335, 61, "Math"], [303, 67, 335, 65], [303, 68, 335, 66, "round"], [303, 73, 335, 71], [303, 74, 335, 72, "x"], [303, 75, 335, 73], [303, 76, 335, 74], [303, 81, 335, 79, "Math"], [303, 85, 335, 83], [303, 86, 335, 84, "round"], [303, 91, 335, 89], [303, 92, 335, 90, "y"], [303, 93, 335, 91], [303, 94, 335, 92], [303, 99, 335, 97, "Math"], [303, 103, 335, 101], [303, 104, 335, 102, "round"], [303, 109, 335, 107], [303, 110, 335, 108, "width"], [303, 115, 335, 113], [303, 116, 335, 114], [303, 120, 335, 118, "Math"], [303, 124, 335, 122], [303, 125, 335, 123, "round"], [303, 130, 335, 128], [303, 131, 335, 129, "height"], [303, 137, 335, 135], [303, 138, 335, 136], [303, 140, 335, 138], [303, 141, 335, 139], [305, 6, 337, 4], [306, 6, 338, 4], [306, 12, 338, 10, "canvasWidth"], [306, 23, 338, 21], [306, 26, 338, 24, "ctx"], [306, 29, 338, 27], [306, 30, 338, 28, "canvas"], [306, 36, 338, 34], [306, 37, 338, 35, "width"], [306, 42, 338, 40], [307, 6, 339, 4], [307, 12, 339, 10, "canvasHeight"], [307, 24, 339, 22], [307, 27, 339, 25, "ctx"], [307, 30, 339, 28], [307, 31, 339, 29, "canvas"], [307, 37, 339, 35], [307, 38, 339, 36, "height"], [307, 44, 339, 42], [308, 6, 341, 4], [308, 12, 341, 10, "clampedX"], [308, 20, 341, 18], [308, 23, 341, 21, "Math"], [308, 27, 341, 25], [308, 28, 341, 26, "max"], [308, 31, 341, 29], [308, 32, 341, 30], [308, 33, 341, 31], [308, 35, 341, 33, "Math"], [308, 39, 341, 37], [308, 40, 341, 38, "min"], [308, 43, 341, 41], [308, 44, 341, 42, "Math"], [308, 48, 341, 46], [308, 49, 341, 47, "floor"], [308, 54, 341, 52], [308, 55, 341, 53, "x"], [308, 56, 341, 54], [308, 57, 341, 55], [308, 59, 341, 57, "canvasWidth"], [308, 70, 341, 68], [308, 73, 341, 71], [308, 74, 341, 72], [308, 75, 341, 73], [308, 76, 341, 74], [309, 6, 342, 4], [309, 12, 342, 10, "clampedY"], [309, 20, 342, 18], [309, 23, 342, 21, "Math"], [309, 27, 342, 25], [309, 28, 342, 26, "max"], [309, 31, 342, 29], [309, 32, 342, 30], [309, 33, 342, 31], [309, 35, 342, 33, "Math"], [309, 39, 342, 37], [309, 40, 342, 38, "min"], [309, 43, 342, 41], [309, 44, 342, 42, "Math"], [309, 48, 342, 46], [309, 49, 342, 47, "floor"], [309, 54, 342, 52], [309, 55, 342, 53, "y"], [309, 56, 342, 54], [309, 57, 342, 55], [309, 59, 342, 57, "canvasHeight"], [309, 71, 342, 69], [309, 74, 342, 72], [309, 75, 342, 73], [309, 76, 342, 74], [309, 77, 342, 75], [310, 6, 343, 4], [310, 12, 343, 10, "<PERSON><PERSON><PERSON><PERSON>"], [310, 24, 343, 22], [310, 27, 343, 25, "Math"], [310, 31, 343, 29], [310, 32, 343, 30, "min"], [310, 35, 343, 33], [310, 36, 343, 34, "Math"], [310, 40, 343, 38], [310, 41, 343, 39, "floor"], [310, 46, 343, 44], [310, 47, 343, 45, "width"], [310, 52, 343, 50], [310, 53, 343, 51], [310, 55, 343, 53, "canvasWidth"], [310, 66, 343, 64], [310, 69, 343, 67, "clampedX"], [310, 77, 343, 75], [310, 78, 343, 76], [311, 6, 344, 4], [311, 12, 344, 10, "clampedHeight"], [311, 25, 344, 23], [311, 28, 344, 26, "Math"], [311, 32, 344, 30], [311, 33, 344, 31, "min"], [311, 36, 344, 34], [311, 37, 344, 35, "Math"], [311, 41, 344, 39], [311, 42, 344, 40, "floor"], [311, 47, 344, 45], [311, 48, 344, 46, "height"], [311, 54, 344, 52], [311, 55, 344, 53], [311, 57, 344, 55, "canvasHeight"], [311, 69, 344, 67], [311, 72, 344, 70, "clampedY"], [311, 80, 344, 78], [311, 81, 344, 79], [312, 6, 346, 4, "console"], [312, 13, 346, 11], [312, 14, 346, 12, "log"], [312, 17, 346, 15], [312, 18, 346, 16], [312, 57, 346, 55, "clampedX"], [312, 65, 346, 63], [312, 70, 346, 68, "clampedY"], [312, 78, 346, 76], [312, 83, 346, 81, "<PERSON><PERSON><PERSON><PERSON>"], [312, 95, 346, 93], [312, 99, 346, 97, "clampedHeight"], [312, 112, 346, 110], [312, 114, 346, 112], [312, 115, 346, 113], [313, 6, 348, 4], [313, 10, 348, 8, "<PERSON><PERSON><PERSON><PERSON>"], [313, 22, 348, 20], [313, 26, 348, 24], [313, 27, 348, 25], [313, 31, 348, 29, "clampedHeight"], [313, 44, 348, 42], [313, 48, 348, 46], [313, 49, 348, 47], [313, 51, 348, 49], [314, 8, 349, 6, "console"], [314, 15, 349, 13], [314, 16, 349, 14, "error"], [314, 21, 349, 19], [314, 22, 349, 20], [314, 70, 349, 68, "<PERSON><PERSON><PERSON><PERSON>"], [314, 82, 349, 80], [314, 94, 349, 92, "clampedHeight"], [314, 107, 349, 105], [314, 109, 349, 107], [314, 110, 349, 108], [315, 8, 350, 6], [316, 6, 351, 4], [318, 6, 353, 4], [319, 6, 354, 4], [319, 12, 354, 10, "imageData"], [319, 21, 354, 19], [319, 24, 354, 22, "ctx"], [319, 27, 354, 25], [319, 28, 354, 26, "getImageData"], [319, 40, 354, 38], [319, 41, 354, 39, "clampedX"], [319, 49, 354, 47], [319, 51, 354, 49, "clampedY"], [319, 59, 354, 57], [319, 61, 354, 59, "<PERSON><PERSON><PERSON><PERSON>"], [319, 73, 354, 71], [319, 75, 354, 73, "clampedHeight"], [319, 88, 354, 86], [319, 89, 354, 87], [320, 6, 355, 4], [320, 12, 355, 10, "data"], [320, 16, 355, 14], [320, 19, 355, 17, "imageData"], [320, 28, 355, 26], [320, 29, 355, 27, "data"], [320, 33, 355, 31], [321, 6, 357, 4, "console"], [321, 13, 357, 11], [321, 14, 357, 12, "log"], [321, 17, 357, 15], [321, 18, 357, 16], [321, 77, 357, 75], [321, 78, 357, 76], [323, 6, 359, 4], [324, 6, 360, 4], [324, 12, 360, 10, "pixelSize"], [324, 21, 360, 19], [324, 24, 360, 22, "Math"], [324, 28, 360, 26], [324, 29, 360, 27, "max"], [324, 32, 360, 30], [324, 33, 360, 31], [324, 35, 360, 33], [324, 37, 360, 35, "Math"], [324, 41, 360, 39], [324, 42, 360, 40, "min"], [324, 45, 360, 43], [324, 46, 360, 44, "<PERSON><PERSON><PERSON><PERSON>"], [324, 58, 360, 56], [324, 60, 360, 58, "clampedHeight"], [324, 73, 360, 71], [324, 74, 360, 72], [324, 77, 360, 75], [324, 78, 360, 76], [324, 79, 360, 77], [325, 6, 361, 4, "console"], [325, 13, 361, 11], [325, 14, 361, 12, "log"], [325, 17, 361, 15], [325, 18, 361, 16], [325, 77, 361, 75, "pixelSize"], [325, 86, 361, 84], [325, 90, 361, 88], [325, 91, 361, 89], [326, 6, 363, 4], [326, 11, 363, 9], [326, 15, 363, 13, "py"], [326, 17, 363, 15], [326, 20, 363, 18], [326, 21, 363, 19], [326, 23, 363, 21, "py"], [326, 25, 363, 23], [326, 28, 363, 26, "clampedHeight"], [326, 41, 363, 39], [326, 43, 363, 41, "py"], [326, 45, 363, 43], [326, 49, 363, 47, "pixelSize"], [326, 58, 363, 56], [326, 60, 363, 58], [327, 8, 364, 6], [327, 13, 364, 11], [327, 17, 364, 15, "px"], [327, 19, 364, 17], [327, 22, 364, 20], [327, 23, 364, 21], [327, 25, 364, 23, "px"], [327, 27, 364, 25], [327, 30, 364, 28, "<PERSON><PERSON><PERSON><PERSON>"], [327, 42, 364, 40], [327, 44, 364, 42, "px"], [327, 46, 364, 44], [327, 50, 364, 48, "pixelSize"], [327, 59, 364, 57], [327, 61, 364, 59], [328, 10, 365, 8], [329, 10, 366, 8], [329, 14, 366, 12, "r"], [329, 15, 366, 13], [329, 18, 366, 16], [329, 19, 366, 17], [330, 12, 366, 19, "g"], [330, 13, 366, 20], [330, 16, 366, 23], [330, 17, 366, 24], [331, 12, 366, 26, "b"], [331, 13, 366, 27], [331, 16, 366, 30], [331, 17, 366, 31], [332, 12, 366, 33, "count"], [332, 17, 366, 38], [332, 20, 366, 41], [332, 21, 366, 42], [333, 10, 368, 8], [333, 15, 368, 13], [333, 19, 368, 17, "dy"], [333, 21, 368, 19], [333, 24, 368, 22], [333, 25, 368, 23], [333, 27, 368, 25, "dy"], [333, 29, 368, 27], [333, 32, 368, 30, "pixelSize"], [333, 41, 368, 39], [333, 45, 368, 43, "py"], [333, 47, 368, 45], [333, 50, 368, 48, "dy"], [333, 52, 368, 50], [333, 55, 368, 53, "clampedHeight"], [333, 68, 368, 66], [333, 70, 368, 68, "dy"], [333, 72, 368, 70], [333, 74, 368, 72], [333, 76, 368, 74], [334, 12, 369, 10], [334, 17, 369, 15], [334, 21, 369, 19, "dx"], [334, 23, 369, 21], [334, 26, 369, 24], [334, 27, 369, 25], [334, 29, 369, 27, "dx"], [334, 31, 369, 29], [334, 34, 369, 32, "pixelSize"], [334, 43, 369, 41], [334, 47, 369, 45, "px"], [334, 49, 369, 47], [334, 52, 369, 50, "dx"], [334, 54, 369, 52], [334, 57, 369, 55, "<PERSON><PERSON><PERSON><PERSON>"], [334, 69, 369, 67], [334, 71, 369, 69, "dx"], [334, 73, 369, 71], [334, 75, 369, 73], [334, 77, 369, 75], [335, 14, 370, 12], [335, 20, 370, 18, "index"], [335, 25, 370, 23], [335, 28, 370, 26], [335, 29, 370, 27], [335, 30, 370, 28, "py"], [335, 32, 370, 30], [335, 35, 370, 33, "dy"], [335, 37, 370, 35], [335, 41, 370, 39, "<PERSON><PERSON><PERSON><PERSON>"], [335, 53, 370, 51], [335, 57, 370, 55, "px"], [335, 59, 370, 57], [335, 62, 370, 60, "dx"], [335, 64, 370, 62], [335, 65, 370, 63], [335, 69, 370, 67], [335, 70, 370, 68], [336, 14, 371, 12, "r"], [336, 15, 371, 13], [336, 19, 371, 17, "data"], [336, 23, 371, 21], [336, 24, 371, 22, "index"], [336, 29, 371, 27], [336, 30, 371, 28], [337, 14, 372, 12, "g"], [337, 15, 372, 13], [337, 19, 372, 17, "data"], [337, 23, 372, 21], [337, 24, 372, 22, "index"], [337, 29, 372, 27], [337, 32, 372, 30], [337, 33, 372, 31], [337, 34, 372, 32], [338, 14, 373, 12, "b"], [338, 15, 373, 13], [338, 19, 373, 17, "data"], [338, 23, 373, 21], [338, 24, 373, 22, "index"], [338, 29, 373, 27], [338, 32, 373, 30], [338, 33, 373, 31], [338, 34, 373, 32], [339, 14, 374, 12, "count"], [339, 19, 374, 17], [339, 21, 374, 19], [340, 12, 375, 10], [341, 10, 376, 8], [342, 10, 378, 8], [342, 14, 378, 12, "count"], [342, 19, 378, 17], [342, 22, 378, 20], [342, 23, 378, 21], [342, 25, 378, 23], [343, 12, 379, 10, "r"], [343, 13, 379, 11], [343, 16, 379, 14, "Math"], [343, 20, 379, 18], [343, 21, 379, 19, "floor"], [343, 26, 379, 24], [343, 27, 379, 25, "r"], [343, 28, 379, 26], [343, 31, 379, 29, "count"], [343, 36, 379, 34], [343, 37, 379, 35], [344, 12, 380, 10, "g"], [344, 13, 380, 11], [344, 16, 380, 14, "Math"], [344, 20, 380, 18], [344, 21, 380, 19, "floor"], [344, 26, 380, 24], [344, 27, 380, 25, "g"], [344, 28, 380, 26], [344, 31, 380, 29, "count"], [344, 36, 380, 34], [344, 37, 380, 35], [345, 12, 381, 10, "b"], [345, 13, 381, 11], [345, 16, 381, 14, "Math"], [345, 20, 381, 18], [345, 21, 381, 19, "floor"], [345, 26, 381, 24], [345, 27, 381, 25, "b"], [345, 28, 381, 26], [345, 31, 381, 29, "count"], [345, 36, 381, 34], [345, 37, 381, 35], [347, 12, 383, 10], [348, 12, 384, 10], [348, 17, 384, 15], [348, 21, 384, 19, "dy"], [348, 23, 384, 21], [348, 26, 384, 24], [348, 27, 384, 25], [348, 29, 384, 27, "dy"], [348, 31, 384, 29], [348, 34, 384, 32, "pixelSize"], [348, 43, 384, 41], [348, 47, 384, 45, "py"], [348, 49, 384, 47], [348, 52, 384, 50, "dy"], [348, 54, 384, 52], [348, 57, 384, 55, "clampedHeight"], [348, 70, 384, 68], [348, 72, 384, 70, "dy"], [348, 74, 384, 72], [348, 76, 384, 74], [348, 78, 384, 76], [349, 14, 385, 12], [349, 19, 385, 17], [349, 23, 385, 21, "dx"], [349, 25, 385, 23], [349, 28, 385, 26], [349, 29, 385, 27], [349, 31, 385, 29, "dx"], [349, 33, 385, 31], [349, 36, 385, 34, "pixelSize"], [349, 45, 385, 43], [349, 49, 385, 47, "px"], [349, 51, 385, 49], [349, 54, 385, 52, "dx"], [349, 56, 385, 54], [349, 59, 385, 57, "<PERSON><PERSON><PERSON><PERSON>"], [349, 71, 385, 69], [349, 73, 385, 71, "dx"], [349, 75, 385, 73], [349, 77, 385, 75], [349, 79, 385, 77], [350, 16, 386, 14], [350, 22, 386, 20, "index"], [350, 27, 386, 25], [350, 30, 386, 28], [350, 31, 386, 29], [350, 32, 386, 30, "py"], [350, 34, 386, 32], [350, 37, 386, 35, "dy"], [350, 39, 386, 37], [350, 43, 386, 41, "<PERSON><PERSON><PERSON><PERSON>"], [350, 55, 386, 53], [350, 59, 386, 57, "px"], [350, 61, 386, 59], [350, 64, 386, 62, "dx"], [350, 66, 386, 64], [350, 67, 386, 65], [350, 71, 386, 69], [350, 72, 386, 70], [351, 16, 387, 14, "data"], [351, 20, 387, 18], [351, 21, 387, 19, "index"], [351, 26, 387, 24], [351, 27, 387, 25], [351, 30, 387, 28, "r"], [351, 31, 387, 29], [352, 16, 388, 14, "data"], [352, 20, 388, 18], [352, 21, 388, 19, "index"], [352, 26, 388, 24], [352, 29, 388, 27], [352, 30, 388, 28], [352, 31, 388, 29], [352, 34, 388, 32, "g"], [352, 35, 388, 33], [353, 16, 389, 14, "data"], [353, 20, 389, 18], [353, 21, 389, 19, "index"], [353, 26, 389, 24], [353, 29, 389, 27], [353, 30, 389, 28], [353, 31, 389, 29], [353, 34, 389, 32, "b"], [353, 35, 389, 33], [354, 16, 390, 14], [355, 14, 391, 12], [356, 12, 392, 10], [357, 10, 393, 8], [358, 8, 394, 6], [359, 6, 395, 4], [361, 6, 397, 4], [362, 6, 398, 4, "console"], [362, 13, 398, 11], [362, 14, 398, 12, "log"], [362, 17, 398, 15], [362, 18, 398, 16], [362, 74, 398, 72], [362, 75, 398, 73], [363, 6, 399, 4], [363, 11, 399, 9], [363, 15, 399, 13, "i"], [363, 16, 399, 14], [363, 19, 399, 17], [363, 20, 399, 18], [363, 22, 399, 20, "i"], [363, 23, 399, 21], [363, 26, 399, 24], [363, 27, 399, 25], [363, 29, 399, 27, "i"], [363, 30, 399, 28], [363, 32, 399, 30], [363, 34, 399, 32], [364, 8, 400, 6, "applySimpleBlur"], [364, 23, 400, 21], [364, 24, 400, 22, "data"], [364, 28, 400, 26], [364, 30, 400, 28, "<PERSON><PERSON><PERSON><PERSON>"], [364, 42, 400, 40], [364, 44, 400, 42, "clampedHeight"], [364, 57, 400, 55], [364, 58, 400, 56], [365, 6, 401, 4], [367, 6, 403, 4], [368, 6, 404, 4, "ctx"], [368, 9, 404, 7], [368, 10, 404, 8, "putImageData"], [368, 22, 404, 20], [368, 23, 404, 21, "imageData"], [368, 32, 404, 30], [368, 34, 404, 32, "clampedX"], [368, 42, 404, 40], [368, 44, 404, 42, "clampedY"], [368, 52, 404, 50], [368, 53, 404, 51], [370, 6, 406, 4], [371, 6, 407, 4, "ctx"], [371, 9, 407, 7], [371, 10, 407, 8, "fillStyle"], [371, 19, 407, 17], [371, 22, 407, 20], [371, 48, 407, 46], [372, 6, 408, 4, "ctx"], [372, 9, 408, 7], [372, 10, 408, 8, "fillRect"], [372, 18, 408, 16], [372, 19, 408, 17, "clampedX"], [372, 27, 408, 25], [372, 29, 408, 27, "clampedY"], [372, 37, 408, 35], [372, 39, 408, 37, "<PERSON><PERSON><PERSON><PERSON>"], [372, 51, 408, 49], [372, 53, 408, 51, "clampedHeight"], [372, 66, 408, 64], [372, 67, 408, 65], [373, 6, 410, 4, "console"], [373, 13, 410, 11], [373, 14, 410, 12, "log"], [373, 17, 410, 15], [373, 18, 410, 16], [373, 66, 410, 64, "clampedX"], [373, 74, 410, 72], [373, 79, 410, 77, "clampedY"], [373, 87, 410, 85], [373, 92, 410, 90, "<PERSON><PERSON><PERSON><PERSON>"], [373, 104, 410, 102], [373, 108, 410, 106, "clampedHeight"], [373, 121, 410, 119], [373, 123, 410, 121], [373, 124, 410, 122], [374, 4, 411, 2], [374, 5, 411, 3], [375, 4, 413, 2], [375, 10, 413, 8, "applySimpleBlur"], [375, 25, 413, 23], [375, 28, 413, 26, "applySimpleBlur"], [375, 29, 413, 27, "data"], [375, 33, 413, 50], [375, 35, 413, 52, "width"], [375, 40, 413, 65], [375, 42, 413, 67, "height"], [375, 48, 413, 81], [375, 53, 413, 86], [376, 6, 414, 4], [376, 12, 414, 10, "original"], [376, 20, 414, 18], [376, 23, 414, 21], [376, 27, 414, 25, "Uint8ClampedArray"], [376, 44, 414, 42], [376, 45, 414, 43, "data"], [376, 49, 414, 47], [376, 50, 414, 48], [377, 6, 416, 4], [377, 11, 416, 9], [377, 15, 416, 13, "y"], [377, 16, 416, 14], [377, 19, 416, 17], [377, 20, 416, 18], [377, 22, 416, 20, "y"], [377, 23, 416, 21], [377, 26, 416, 24, "height"], [377, 32, 416, 30], [377, 35, 416, 33], [377, 36, 416, 34], [377, 38, 416, 36, "y"], [377, 39, 416, 37], [377, 41, 416, 39], [377, 43, 416, 41], [378, 8, 417, 6], [378, 13, 417, 11], [378, 17, 417, 15, "x"], [378, 18, 417, 16], [378, 21, 417, 19], [378, 22, 417, 20], [378, 24, 417, 22, "x"], [378, 25, 417, 23], [378, 28, 417, 26, "width"], [378, 33, 417, 31], [378, 36, 417, 34], [378, 37, 417, 35], [378, 39, 417, 37, "x"], [378, 40, 417, 38], [378, 42, 417, 40], [378, 44, 417, 42], [379, 10, 418, 8], [379, 16, 418, 14, "index"], [379, 21, 418, 19], [379, 24, 418, 22], [379, 25, 418, 23, "y"], [379, 26, 418, 24], [379, 29, 418, 27, "width"], [379, 34, 418, 32], [379, 37, 418, 35, "x"], [379, 38, 418, 36], [379, 42, 418, 40], [379, 43, 418, 41], [381, 10, 420, 8], [382, 10, 421, 8], [382, 14, 421, 12, "r"], [382, 15, 421, 13], [382, 18, 421, 16], [382, 19, 421, 17], [383, 12, 421, 19, "g"], [383, 13, 421, 20], [383, 16, 421, 23], [383, 17, 421, 24], [384, 12, 421, 26, "b"], [384, 13, 421, 27], [384, 16, 421, 30], [384, 17, 421, 31], [385, 10, 422, 8], [385, 15, 422, 13], [385, 19, 422, 17, "dy"], [385, 21, 422, 19], [385, 24, 422, 22], [385, 25, 422, 23], [385, 26, 422, 24], [385, 28, 422, 26, "dy"], [385, 30, 422, 28], [385, 34, 422, 32], [385, 35, 422, 33], [385, 37, 422, 35, "dy"], [385, 39, 422, 37], [385, 41, 422, 39], [385, 43, 422, 41], [386, 12, 423, 10], [386, 17, 423, 15], [386, 21, 423, 19, "dx"], [386, 23, 423, 21], [386, 26, 423, 24], [386, 27, 423, 25], [386, 28, 423, 26], [386, 30, 423, 28, "dx"], [386, 32, 423, 30], [386, 36, 423, 34], [386, 37, 423, 35], [386, 39, 423, 37, "dx"], [386, 41, 423, 39], [386, 43, 423, 41], [386, 45, 423, 43], [387, 14, 424, 12], [387, 20, 424, 18, "neighborIndex"], [387, 33, 424, 31], [387, 36, 424, 34], [387, 37, 424, 35], [387, 38, 424, 36, "y"], [387, 39, 424, 37], [387, 42, 424, 40, "dy"], [387, 44, 424, 42], [387, 48, 424, 46, "width"], [387, 53, 424, 51], [387, 57, 424, 55, "x"], [387, 58, 424, 56], [387, 61, 424, 59, "dx"], [387, 63, 424, 61], [387, 64, 424, 62], [387, 68, 424, 66], [387, 69, 424, 67], [388, 14, 425, 12, "r"], [388, 15, 425, 13], [388, 19, 425, 17, "original"], [388, 27, 425, 25], [388, 28, 425, 26, "neighborIndex"], [388, 41, 425, 39], [388, 42, 425, 40], [389, 14, 426, 12, "g"], [389, 15, 426, 13], [389, 19, 426, 17, "original"], [389, 27, 426, 25], [389, 28, 426, 26, "neighborIndex"], [389, 41, 426, 39], [389, 44, 426, 42], [389, 45, 426, 43], [389, 46, 426, 44], [390, 14, 427, 12, "b"], [390, 15, 427, 13], [390, 19, 427, 17, "original"], [390, 27, 427, 25], [390, 28, 427, 26, "neighborIndex"], [390, 41, 427, 39], [390, 44, 427, 42], [390, 45, 427, 43], [390, 46, 427, 44], [391, 12, 428, 10], [392, 10, 429, 8], [393, 10, 431, 8, "data"], [393, 14, 431, 12], [393, 15, 431, 13, "index"], [393, 20, 431, 18], [393, 21, 431, 19], [393, 24, 431, 22, "r"], [393, 25, 431, 23], [393, 28, 431, 26], [393, 29, 431, 27], [394, 10, 432, 8, "data"], [394, 14, 432, 12], [394, 15, 432, 13, "index"], [394, 20, 432, 18], [394, 23, 432, 21], [394, 24, 432, 22], [394, 25, 432, 23], [394, 28, 432, 26, "g"], [394, 29, 432, 27], [394, 32, 432, 30], [394, 33, 432, 31], [395, 10, 433, 8, "data"], [395, 14, 433, 12], [395, 15, 433, 13, "index"], [395, 20, 433, 18], [395, 23, 433, 21], [395, 24, 433, 22], [395, 25, 433, 23], [395, 28, 433, 26, "b"], [395, 29, 433, 27], [395, 32, 433, 30], [395, 33, 433, 31], [396, 8, 434, 6], [397, 6, 435, 4], [398, 4, 436, 2], [398, 5, 436, 3], [399, 4, 438, 2], [399, 10, 438, 8, "applyFallbackFaceBlur"], [399, 31, 438, 29], [399, 34, 438, 32, "applyFallbackFaceBlur"], [399, 35, 438, 33, "ctx"], [399, 38, 438, 62], [399, 40, 438, 64, "imgWidth"], [399, 48, 438, 80], [399, 50, 438, 82, "imgHeight"], [399, 59, 438, 99], [399, 64, 438, 104], [400, 6, 439, 4, "console"], [400, 13, 439, 11], [400, 14, 439, 12, "log"], [400, 17, 439, 15], [400, 18, 439, 16], [400, 90, 439, 88], [400, 91, 439, 89], [402, 6, 441, 4], [403, 6, 442, 4], [403, 12, 442, 10, "areas"], [403, 17, 442, 15], [403, 20, 442, 18], [404, 6, 443, 6], [405, 6, 444, 6], [406, 8, 444, 8, "x"], [406, 9, 444, 9], [406, 11, 444, 11, "imgWidth"], [406, 19, 444, 19], [406, 22, 444, 22], [406, 26, 444, 26], [407, 8, 444, 28, "y"], [407, 9, 444, 29], [407, 11, 444, 31, "imgHeight"], [407, 20, 444, 40], [407, 23, 444, 43], [407, 27, 444, 47], [408, 8, 444, 49, "w"], [408, 9, 444, 50], [408, 11, 444, 52, "imgWidth"], [408, 19, 444, 60], [408, 22, 444, 63], [408, 25, 444, 66], [409, 8, 444, 68, "h"], [409, 9, 444, 69], [409, 11, 444, 71, "imgHeight"], [409, 20, 444, 80], [409, 23, 444, 83], [410, 6, 444, 87], [410, 7, 444, 88], [411, 6, 445, 6], [412, 6, 446, 6], [413, 8, 446, 8, "x"], [413, 9, 446, 9], [413, 11, 446, 11, "imgWidth"], [413, 19, 446, 19], [413, 22, 446, 22], [413, 25, 446, 25], [414, 8, 446, 27, "y"], [414, 9, 446, 28], [414, 11, 446, 30, "imgHeight"], [414, 20, 446, 39], [414, 23, 446, 42], [414, 26, 446, 45], [415, 8, 446, 47, "w"], [415, 9, 446, 48], [415, 11, 446, 50, "imgWidth"], [415, 19, 446, 58], [415, 22, 446, 61], [415, 26, 446, 65], [416, 8, 446, 67, "h"], [416, 9, 446, 68], [416, 11, 446, 70, "imgHeight"], [416, 20, 446, 79], [416, 23, 446, 82], [417, 6, 446, 86], [417, 7, 446, 87], [418, 6, 447, 6], [419, 6, 448, 6], [420, 8, 448, 8, "x"], [420, 9, 448, 9], [420, 11, 448, 11, "imgWidth"], [420, 19, 448, 19], [420, 22, 448, 22], [420, 26, 448, 26], [421, 8, 448, 28, "y"], [421, 9, 448, 29], [421, 11, 448, 31, "imgHeight"], [421, 20, 448, 40], [421, 23, 448, 43], [421, 26, 448, 46], [422, 8, 448, 48, "w"], [422, 9, 448, 49], [422, 11, 448, 51, "imgWidth"], [422, 19, 448, 59], [422, 22, 448, 62], [422, 26, 448, 66], [423, 8, 448, 68, "h"], [423, 9, 448, 69], [423, 11, 448, 71, "imgHeight"], [423, 20, 448, 80], [423, 23, 448, 83], [424, 6, 448, 87], [424, 7, 448, 88], [424, 8, 449, 5], [425, 6, 451, 4, "areas"], [425, 11, 451, 9], [425, 12, 451, 10, "for<PERSON>ach"], [425, 19, 451, 17], [425, 20, 451, 18], [425, 21, 451, 19, "area"], [425, 25, 451, 23], [425, 27, 451, 25, "index"], [425, 32, 451, 30], [425, 37, 451, 35], [426, 8, 452, 6, "console"], [426, 15, 452, 13], [426, 16, 452, 14, "log"], [426, 19, 452, 17], [426, 20, 452, 18], [426, 65, 452, 63, "index"], [426, 70, 452, 68], [426, 73, 452, 71], [426, 74, 452, 72], [426, 77, 452, 75], [426, 79, 452, 77, "area"], [426, 83, 452, 81], [426, 84, 452, 82], [427, 8, 453, 6, "applyStrongBlur"], [427, 23, 453, 21], [427, 24, 453, 22, "ctx"], [427, 27, 453, 25], [427, 29, 453, 27, "area"], [427, 33, 453, 31], [427, 34, 453, 32, "x"], [427, 35, 453, 33], [427, 37, 453, 35, "area"], [427, 41, 453, 39], [427, 42, 453, 40, "y"], [427, 43, 453, 41], [427, 45, 453, 43, "area"], [427, 49, 453, 47], [427, 50, 453, 48, "w"], [427, 51, 453, 49], [427, 53, 453, 51, "area"], [427, 57, 453, 55], [427, 58, 453, 56, "h"], [427, 59, 453, 57], [427, 60, 453, 58], [428, 6, 454, 4], [428, 7, 454, 5], [428, 8, 454, 6], [429, 4, 455, 2], [429, 5, 455, 3], [431, 4, 457, 2], [432, 4, 458, 2], [432, 10, 458, 8, "capturePhoto"], [432, 22, 458, 20], [432, 25, 458, 23], [432, 29, 458, 23, "useCallback"], [432, 47, 458, 34], [432, 49, 458, 35], [432, 61, 458, 47], [433, 6, 459, 4], [434, 6, 460, 4], [434, 12, 460, 10, "isDev"], [434, 17, 460, 15], [434, 20, 460, 18, "process"], [434, 27, 460, 25], [434, 28, 460, 26, "env"], [434, 31, 460, 29], [434, 32, 460, 30, "NODE_ENV"], [434, 40, 460, 38], [434, 45, 460, 43], [434, 58, 460, 56], [434, 62, 460, 60, "__DEV__"], [434, 69, 460, 67], [435, 6, 462, 4], [435, 10, 462, 8], [435, 11, 462, 9, "cameraRef"], [435, 20, 462, 18], [435, 21, 462, 19, "current"], [435, 28, 462, 26], [435, 32, 462, 30], [435, 33, 462, 31, "isDev"], [435, 38, 462, 36], [435, 40, 462, 38], [436, 8, 463, 6, "<PERSON><PERSON>"], [436, 22, 463, 11], [436, 23, 463, 12, "alert"], [436, 28, 463, 17], [436, 29, 463, 18], [436, 36, 463, 25], [436, 38, 463, 27], [436, 56, 463, 45], [436, 57, 463, 46], [437, 8, 464, 6], [438, 6, 465, 4], [439, 6, 466, 4], [439, 10, 466, 8], [440, 8, 467, 6, "setProcessingState"], [440, 26, 467, 24], [440, 27, 467, 25], [440, 38, 467, 36], [440, 39, 467, 37], [441, 8, 468, 6, "setProcessingProgress"], [441, 29, 468, 27], [441, 30, 468, 28], [441, 32, 468, 30], [441, 33, 468, 31], [442, 8, 469, 6], [443, 8, 470, 6], [444, 8, 471, 6], [445, 8, 472, 6], [445, 14, 472, 12], [445, 18, 472, 16, "Promise"], [445, 25, 472, 23], [445, 26, 472, 24, "resolve"], [445, 33, 472, 31], [445, 37, 472, 35, "setTimeout"], [445, 47, 472, 45], [445, 48, 472, 46, "resolve"], [445, 55, 472, 53], [445, 57, 472, 55], [445, 59, 472, 57], [445, 60, 472, 58], [445, 61, 472, 59], [446, 8, 473, 6], [447, 8, 474, 6], [447, 12, 474, 10, "photo"], [447, 17, 474, 15], [448, 8, 476, 6], [448, 12, 476, 10], [449, 10, 477, 8, "photo"], [449, 15, 477, 13], [449, 18, 477, 16], [449, 24, 477, 22, "cameraRef"], [449, 33, 477, 31], [449, 34, 477, 32, "current"], [449, 41, 477, 39], [449, 42, 477, 40, "takePictureAsync"], [449, 58, 477, 56], [449, 59, 477, 57], [450, 12, 478, 10, "quality"], [450, 19, 478, 17], [450, 21, 478, 19], [450, 24, 478, 22], [451, 12, 479, 10, "base64"], [451, 18, 479, 16], [451, 20, 479, 18], [451, 25, 479, 23], [452, 12, 480, 10, "skipProcessing"], [452, 26, 480, 24], [452, 28, 480, 26], [452, 32, 480, 30], [452, 33, 480, 32], [453, 10, 481, 8], [453, 11, 481, 9], [453, 12, 481, 10], [454, 8, 482, 6], [454, 9, 482, 7], [454, 10, 482, 8], [454, 17, 482, 15, "cameraError"], [454, 28, 482, 26], [454, 30, 482, 28], [455, 10, 483, 8, "console"], [455, 17, 483, 15], [455, 18, 483, 16, "log"], [455, 21, 483, 19], [455, 22, 483, 20], [455, 82, 483, 80], [455, 84, 483, 82, "cameraError"], [455, 95, 483, 93], [455, 96, 483, 94], [456, 10, 484, 8], [457, 10, 485, 8], [457, 14, 485, 12, "isDev"], [457, 19, 485, 17], [457, 21, 485, 19], [458, 12, 486, 10, "photo"], [458, 17, 486, 15], [458, 20, 486, 18], [459, 14, 487, 12, "uri"], [459, 17, 487, 15], [459, 19, 487, 17], [460, 12, 488, 10], [460, 13, 488, 11], [461, 10, 489, 8], [461, 11, 489, 9], [461, 17, 489, 15], [462, 12, 490, 10], [462, 18, 490, 16, "cameraError"], [462, 29, 490, 27], [463, 10, 491, 8], [464, 8, 492, 6], [465, 8, 493, 6], [465, 12, 493, 10], [465, 13, 493, 11, "photo"], [465, 18, 493, 16], [465, 20, 493, 18], [466, 10, 494, 8], [466, 16, 494, 14], [466, 20, 494, 18, "Error"], [466, 25, 494, 23], [466, 26, 494, 24], [466, 51, 494, 49], [466, 52, 494, 50], [467, 8, 495, 6], [468, 8, 496, 6, "console"], [468, 15, 496, 13], [468, 16, 496, 14, "log"], [468, 19, 496, 17], [468, 20, 496, 18], [468, 56, 496, 54], [468, 58, 496, 56, "photo"], [468, 63, 496, 61], [468, 64, 496, 62, "uri"], [468, 67, 496, 65], [468, 68, 496, 66], [469, 8, 497, 6, "setCapturedPhoto"], [469, 24, 497, 22], [469, 25, 497, 23, "photo"], [469, 30, 497, 28], [469, 31, 497, 29, "uri"], [469, 34, 497, 32], [469, 35, 497, 33], [470, 8, 498, 6, "setProcessingProgress"], [470, 29, 498, 27], [470, 30, 498, 28], [470, 32, 498, 30], [470, 33, 498, 31], [471, 8, 499, 6], [472, 8, 500, 6, "console"], [472, 15, 500, 13], [472, 16, 500, 14, "log"], [472, 19, 500, 17], [472, 20, 500, 18], [472, 73, 500, 71], [472, 74, 500, 72], [473, 8, 501, 6], [473, 14, 501, 12, "processImageWithFaceBlur"], [473, 38, 501, 36], [473, 39, 501, 37, "photo"], [473, 44, 501, 42], [473, 45, 501, 43, "uri"], [473, 48, 501, 46], [473, 49, 501, 47], [474, 8, 502, 6, "console"], [474, 15, 502, 13], [474, 16, 502, 14, "log"], [474, 19, 502, 17], [474, 20, 502, 18], [474, 71, 502, 69], [474, 72, 502, 70], [475, 6, 503, 4], [475, 7, 503, 5], [475, 8, 503, 6], [475, 15, 503, 13, "error"], [475, 20, 503, 18], [475, 22, 503, 20], [476, 8, 504, 6, "console"], [476, 15, 504, 13], [476, 16, 504, 14, "error"], [476, 21, 504, 19], [476, 22, 504, 20], [476, 54, 504, 52], [476, 56, 504, 54, "error"], [476, 61, 504, 59], [476, 62, 504, 60], [477, 8, 505, 6, "setErrorMessage"], [477, 23, 505, 21], [477, 24, 505, 22], [477, 68, 505, 66], [477, 69, 505, 67], [478, 8, 506, 6, "setProcessingState"], [478, 26, 506, 24], [478, 27, 506, 25], [478, 34, 506, 32], [478, 35, 506, 33], [479, 6, 507, 4], [480, 4, 508, 2], [480, 5, 508, 3], [480, 7, 508, 5], [480, 9, 508, 7], [480, 10, 508, 8], [481, 4, 509, 2], [482, 4, 510, 2], [482, 10, 510, 8, "processImageWithFaceBlur"], [482, 34, 510, 32], [482, 37, 510, 35], [482, 43, 510, 42, "photoUri"], [482, 51, 510, 58], [482, 55, 510, 63], [483, 6, 511, 4], [483, 10, 511, 8], [484, 8, 512, 6, "console"], [484, 15, 512, 13], [484, 16, 512, 14, "log"], [484, 19, 512, 17], [484, 20, 512, 18], [484, 84, 512, 82], [484, 85, 512, 83], [485, 8, 513, 6, "setProcessingState"], [485, 26, 513, 24], [485, 27, 513, 25], [485, 39, 513, 37], [485, 40, 513, 38], [486, 8, 514, 6, "setProcessingProgress"], [486, 29, 514, 27], [486, 30, 514, 28], [486, 32, 514, 30], [486, 33, 514, 31], [488, 8, 516, 6], [489, 8, 517, 6], [489, 14, 517, 12, "canvas"], [489, 20, 517, 18], [489, 23, 517, 21, "document"], [489, 31, 517, 29], [489, 32, 517, 30, "createElement"], [489, 45, 517, 43], [489, 46, 517, 44], [489, 54, 517, 52], [489, 55, 517, 53], [490, 8, 518, 6], [490, 14, 518, 12, "ctx"], [490, 17, 518, 15], [490, 20, 518, 18, "canvas"], [490, 26, 518, 24], [490, 27, 518, 25, "getContext"], [490, 37, 518, 35], [490, 38, 518, 36], [490, 42, 518, 40], [490, 43, 518, 41], [491, 8, 519, 6], [491, 12, 519, 10], [491, 13, 519, 11, "ctx"], [491, 16, 519, 14], [491, 18, 519, 16], [491, 24, 519, 22], [491, 28, 519, 26, "Error"], [491, 33, 519, 31], [491, 34, 519, 32], [491, 64, 519, 62], [491, 65, 519, 63], [493, 8, 521, 6], [494, 8, 522, 6], [494, 14, 522, 12, "img"], [494, 17, 522, 15], [494, 20, 522, 18], [494, 24, 522, 22, "Image"], [494, 29, 522, 27], [494, 30, 522, 28], [494, 31, 522, 29], [495, 8, 523, 6], [495, 14, 523, 12], [495, 18, 523, 16, "Promise"], [495, 25, 523, 23], [495, 26, 523, 24], [495, 27, 523, 25, "resolve"], [495, 34, 523, 32], [495, 36, 523, 34, "reject"], [495, 42, 523, 40], [495, 47, 523, 45], [496, 10, 524, 8, "img"], [496, 13, 524, 11], [496, 14, 524, 12, "onload"], [496, 20, 524, 18], [496, 23, 524, 21, "resolve"], [496, 30, 524, 28], [497, 10, 525, 8, "img"], [497, 13, 525, 11], [497, 14, 525, 12, "onerror"], [497, 21, 525, 19], [497, 24, 525, 22, "reject"], [497, 30, 525, 28], [498, 10, 526, 8, "img"], [498, 13, 526, 11], [498, 14, 526, 12, "src"], [498, 17, 526, 15], [498, 20, 526, 18, "photoUri"], [498, 28, 526, 26], [499, 8, 527, 6], [499, 9, 527, 7], [499, 10, 527, 8], [501, 8, 529, 6], [502, 8, 530, 6, "canvas"], [502, 14, 530, 12], [502, 15, 530, 13, "width"], [502, 20, 530, 18], [502, 23, 530, 21, "img"], [502, 26, 530, 24], [502, 27, 530, 25, "width"], [502, 32, 530, 30], [503, 8, 531, 6, "canvas"], [503, 14, 531, 12], [503, 15, 531, 13, "height"], [503, 21, 531, 19], [503, 24, 531, 22, "img"], [503, 27, 531, 25], [503, 28, 531, 26, "height"], [503, 34, 531, 32], [504, 8, 532, 6, "console"], [504, 15, 532, 13], [504, 16, 532, 14, "log"], [504, 19, 532, 17], [504, 20, 532, 18], [504, 54, 532, 52], [504, 56, 532, 54], [505, 10, 532, 56, "width"], [505, 15, 532, 61], [505, 17, 532, 63, "img"], [505, 20, 532, 66], [505, 21, 532, 67, "width"], [505, 26, 532, 72], [506, 10, 532, 74, "height"], [506, 16, 532, 80], [506, 18, 532, 82, "img"], [506, 21, 532, 85], [506, 22, 532, 86, "height"], [507, 8, 532, 93], [507, 9, 532, 94], [507, 10, 532, 95], [509, 8, 534, 6], [510, 8, 535, 6, "ctx"], [510, 11, 535, 9], [510, 12, 535, 10, "drawImage"], [510, 21, 535, 19], [510, 22, 535, 20, "img"], [510, 25, 535, 23], [510, 27, 535, 25], [510, 28, 535, 26], [510, 30, 535, 28], [510, 31, 535, 29], [510, 32, 535, 30], [511, 8, 536, 6, "console"], [511, 15, 536, 13], [511, 16, 536, 14, "log"], [511, 19, 536, 17], [511, 20, 536, 18], [511, 72, 536, 70], [511, 73, 536, 71], [512, 8, 538, 6, "setProcessingProgress"], [512, 29, 538, 27], [512, 30, 538, 28], [512, 32, 538, 30], [512, 33, 538, 31], [514, 8, 540, 6], [515, 8, 541, 6], [515, 12, 541, 10, "detectedFaces"], [515, 25, 541, 23], [515, 28, 541, 26], [515, 30, 541, 28], [516, 8, 543, 6, "console"], [516, 15, 543, 13], [516, 16, 543, 14, "log"], [516, 19, 543, 17], [516, 20, 543, 18], [516, 81, 543, 79], [516, 82, 543, 80], [518, 8, 545, 6], [519, 8, 546, 6], [519, 12, 546, 10], [520, 10, 547, 8], [520, 16, 547, 14, "loadTensorFlowFaceDetection"], [520, 43, 547, 41], [520, 44, 547, 42], [520, 45, 547, 43], [521, 10, 548, 8, "detectedFaces"], [521, 23, 548, 21], [521, 26, 548, 24], [521, 32, 548, 30, "detectFacesWithTensorFlow"], [521, 57, 548, 55], [521, 58, 548, 56, "img"], [521, 61, 548, 59], [521, 62, 548, 60], [522, 10, 549, 8, "console"], [522, 17, 549, 15], [522, 18, 549, 16, "log"], [522, 21, 549, 19], [522, 22, 549, 20], [522, 70, 549, 68, "detectedFaces"], [522, 83, 549, 81], [522, 84, 549, 82, "length"], [522, 90, 549, 88], [522, 98, 549, 96], [522, 99, 549, 97], [523, 8, 550, 6], [523, 9, 550, 7], [523, 10, 550, 8], [523, 17, 550, 15, "tensorFlowError"], [523, 32, 550, 30], [523, 34, 550, 32], [524, 10, 551, 8, "console"], [524, 17, 551, 15], [524, 18, 551, 16, "warn"], [524, 22, 551, 20], [524, 23, 551, 21], [524, 61, 551, 59], [524, 63, 551, 61, "tensorFlowError"], [524, 78, 551, 76], [524, 79, 551, 77], [526, 10, 553, 8], [527, 10, 554, 8, "console"], [527, 17, 554, 15], [527, 18, 554, 16, "log"], [527, 21, 554, 19], [527, 22, 554, 20], [527, 86, 554, 84], [527, 87, 554, 85], [528, 10, 555, 8, "detectedFaces"], [528, 23, 555, 21], [528, 26, 555, 24, "detectFacesHeuristic"], [528, 46, 555, 44], [528, 47, 555, 45, "img"], [528, 50, 555, 48], [528, 52, 555, 50, "ctx"], [528, 55, 555, 53], [528, 56, 555, 54], [529, 10, 556, 8, "console"], [529, 17, 556, 15], [529, 18, 556, 16, "log"], [529, 21, 556, 19], [529, 22, 556, 20], [529, 70, 556, 68, "detectedFaces"], [529, 83, 556, 81], [529, 84, 556, 82, "length"], [529, 90, 556, 88], [529, 98, 556, 96], [529, 99, 556, 97], [530, 8, 557, 6], [531, 8, 559, 6, "console"], [531, 15, 559, 13], [531, 16, 559, 14, "log"], [531, 19, 559, 17], [531, 20, 559, 18], [531, 72, 559, 70, "detectedFaces"], [531, 85, 559, 83], [531, 86, 559, 84, "length"], [531, 92, 559, 90], [531, 100, 559, 98], [531, 101, 559, 99], [532, 8, 560, 6], [532, 12, 560, 10, "detectedFaces"], [532, 25, 560, 23], [532, 26, 560, 24, "length"], [532, 32, 560, 30], [532, 35, 560, 33], [532, 36, 560, 34], [532, 38, 560, 36], [533, 10, 561, 8, "console"], [533, 17, 561, 15], [533, 18, 561, 16, "log"], [533, 21, 561, 19], [533, 22, 561, 20], [533, 66, 561, 64], [533, 68, 561, 66, "detectedFaces"], [533, 81, 561, 79], [533, 82, 561, 80, "map"], [533, 85, 561, 83], [533, 86, 561, 84], [533, 87, 561, 85, "face"], [533, 91, 561, 89], [533, 93, 561, 91, "i"], [533, 94, 561, 92], [533, 100, 561, 98], [534, 12, 562, 10, "faceNumber"], [534, 22, 562, 20], [534, 24, 562, 22, "i"], [534, 25, 562, 23], [534, 28, 562, 26], [534, 29, 562, 27], [535, 12, 563, 10, "centerX"], [535, 19, 563, 17], [535, 21, 563, 19, "face"], [535, 25, 563, 23], [535, 26, 563, 24, "boundingBox"], [535, 37, 563, 35], [535, 38, 563, 36, "xCenter"], [535, 45, 563, 43], [536, 12, 564, 10, "centerY"], [536, 19, 564, 17], [536, 21, 564, 19, "face"], [536, 25, 564, 23], [536, 26, 564, 24, "boundingBox"], [536, 37, 564, 35], [536, 38, 564, 36, "yCenter"], [536, 45, 564, 43], [537, 12, 565, 10, "width"], [537, 17, 565, 15], [537, 19, 565, 17, "face"], [537, 23, 565, 21], [537, 24, 565, 22, "boundingBox"], [537, 35, 565, 33], [537, 36, 565, 34, "width"], [537, 41, 565, 39], [538, 12, 566, 10, "height"], [538, 18, 566, 16], [538, 20, 566, 18, "face"], [538, 24, 566, 22], [538, 25, 566, 23, "boundingBox"], [538, 36, 566, 34], [538, 37, 566, 35, "height"], [539, 10, 567, 8], [539, 11, 567, 9], [539, 12, 567, 10], [539, 13, 567, 11], [539, 14, 567, 12], [540, 8, 568, 6], [540, 9, 568, 7], [540, 15, 568, 13], [541, 10, 569, 8, "console"], [541, 17, 569, 15], [541, 18, 569, 16, "log"], [541, 21, 569, 19], [541, 22, 569, 20], [541, 91, 569, 89], [541, 92, 569, 90], [542, 8, 570, 6], [543, 8, 572, 6, "setProcessingProgress"], [543, 29, 572, 27], [543, 30, 572, 28], [543, 32, 572, 30], [543, 33, 572, 31], [545, 8, 574, 6], [546, 8, 575, 6], [546, 12, 575, 10, "detectedFaces"], [546, 25, 575, 23], [546, 26, 575, 24, "length"], [546, 32, 575, 30], [546, 35, 575, 33], [546, 36, 575, 34], [546, 38, 575, 36], [547, 10, 576, 8, "console"], [547, 17, 576, 15], [547, 18, 576, 16, "log"], [547, 21, 576, 19], [547, 22, 576, 20], [547, 61, 576, 59, "detectedFaces"], [547, 74, 576, 72], [547, 75, 576, 73, "length"], [547, 81, 576, 79], [547, 101, 576, 99], [547, 102, 576, 100], [548, 10, 578, 8, "detectedFaces"], [548, 23, 578, 21], [548, 24, 578, 22, "for<PERSON>ach"], [548, 31, 578, 29], [548, 32, 578, 30], [548, 33, 578, 31, "detection"], [548, 42, 578, 40], [548, 44, 578, 42, "index"], [548, 49, 578, 47], [548, 54, 578, 52], [549, 12, 579, 10], [549, 18, 579, 16, "bbox"], [549, 22, 579, 20], [549, 25, 579, 23, "detection"], [549, 34, 579, 32], [549, 35, 579, 33, "boundingBox"], [549, 46, 579, 44], [551, 12, 581, 10], [552, 12, 582, 10], [552, 18, 582, 16, "faceX"], [552, 23, 582, 21], [552, 26, 582, 24, "bbox"], [552, 30, 582, 28], [552, 31, 582, 29, "xCenter"], [552, 38, 582, 36], [552, 41, 582, 39, "img"], [552, 44, 582, 42], [552, 45, 582, 43, "width"], [552, 50, 582, 48], [552, 53, 582, 52, "bbox"], [552, 57, 582, 56], [552, 58, 582, 57, "width"], [552, 63, 582, 62], [552, 66, 582, 65, "img"], [552, 69, 582, 68], [552, 70, 582, 69, "width"], [552, 75, 582, 74], [552, 78, 582, 78], [552, 79, 582, 79], [553, 12, 583, 10], [553, 18, 583, 16, "faceY"], [553, 23, 583, 21], [553, 26, 583, 24, "bbox"], [553, 30, 583, 28], [553, 31, 583, 29, "yCenter"], [553, 38, 583, 36], [553, 41, 583, 39, "img"], [553, 44, 583, 42], [553, 45, 583, 43, "height"], [553, 51, 583, 49], [553, 54, 583, 53, "bbox"], [553, 58, 583, 57], [553, 59, 583, 58, "height"], [553, 65, 583, 64], [553, 68, 583, 67, "img"], [553, 71, 583, 70], [553, 72, 583, 71, "height"], [553, 78, 583, 77], [553, 81, 583, 81], [553, 82, 583, 82], [554, 12, 584, 10], [554, 18, 584, 16, "faceWidth"], [554, 27, 584, 25], [554, 30, 584, 28, "bbox"], [554, 34, 584, 32], [554, 35, 584, 33, "width"], [554, 40, 584, 38], [554, 43, 584, 41, "img"], [554, 46, 584, 44], [554, 47, 584, 45, "width"], [554, 52, 584, 50], [555, 12, 585, 10], [555, 18, 585, 16, "faceHeight"], [555, 28, 585, 26], [555, 31, 585, 29, "bbox"], [555, 35, 585, 33], [555, 36, 585, 34, "height"], [555, 42, 585, 40], [555, 45, 585, 43, "img"], [555, 48, 585, 46], [555, 49, 585, 47, "height"], [555, 55, 585, 53], [557, 12, 587, 10], [558, 12, 588, 10], [558, 18, 588, 16, "padding"], [558, 25, 588, 23], [558, 28, 588, 26], [558, 31, 588, 29], [559, 12, 589, 10], [559, 18, 589, 16, "paddedX"], [559, 25, 589, 23], [559, 28, 589, 26, "Math"], [559, 32, 589, 30], [559, 33, 589, 31, "max"], [559, 36, 589, 34], [559, 37, 589, 35], [559, 38, 589, 36], [559, 40, 589, 38, "faceX"], [559, 45, 589, 43], [559, 48, 589, 46, "faceWidth"], [559, 57, 589, 55], [559, 60, 589, 58, "padding"], [559, 67, 589, 65], [559, 68, 589, 66], [560, 12, 590, 10], [560, 18, 590, 16, "paddedY"], [560, 25, 590, 23], [560, 28, 590, 26, "Math"], [560, 32, 590, 30], [560, 33, 590, 31, "max"], [560, 36, 590, 34], [560, 37, 590, 35], [560, 38, 590, 36], [560, 40, 590, 38, "faceY"], [560, 45, 590, 43], [560, 48, 590, 46, "faceHeight"], [560, 58, 590, 56], [560, 61, 590, 59, "padding"], [560, 68, 590, 66], [560, 69, 590, 67], [561, 12, 591, 10], [561, 18, 591, 16, "<PERSON><PERSON><PERSON><PERSON>"], [561, 29, 591, 27], [561, 32, 591, 30, "Math"], [561, 36, 591, 34], [561, 37, 591, 35, "min"], [561, 40, 591, 38], [561, 41, 591, 39, "img"], [561, 44, 591, 42], [561, 45, 591, 43, "width"], [561, 50, 591, 48], [561, 53, 591, 51, "paddedX"], [561, 60, 591, 58], [561, 62, 591, 60, "faceWidth"], [561, 71, 591, 69], [561, 75, 591, 73], [561, 76, 591, 74], [561, 79, 591, 77], [561, 80, 591, 78], [561, 83, 591, 81, "padding"], [561, 90, 591, 88], [561, 91, 591, 89], [561, 92, 591, 90], [562, 12, 592, 10], [562, 18, 592, 16, "paddedHeight"], [562, 30, 592, 28], [562, 33, 592, 31, "Math"], [562, 37, 592, 35], [562, 38, 592, 36, "min"], [562, 41, 592, 39], [562, 42, 592, 40, "img"], [562, 45, 592, 43], [562, 46, 592, 44, "height"], [562, 52, 592, 50], [562, 55, 592, 53, "paddedY"], [562, 62, 592, 60], [562, 64, 592, 62, "faceHeight"], [562, 74, 592, 72], [562, 78, 592, 76], [562, 79, 592, 77], [562, 82, 592, 80], [562, 83, 592, 81], [562, 86, 592, 84, "padding"], [562, 93, 592, 91], [562, 94, 592, 92], [562, 95, 592, 93], [563, 12, 594, 10, "console"], [563, 19, 594, 17], [563, 20, 594, 18, "log"], [563, 23, 594, 21], [563, 24, 594, 22], [563, 60, 594, 58, "index"], [563, 65, 594, 63], [563, 68, 594, 66], [563, 69, 594, 67], [563, 72, 594, 70], [563, 74, 594, 72], [564, 14, 595, 12, "original"], [564, 22, 595, 20], [564, 24, 595, 22], [565, 16, 595, 24, "x"], [565, 17, 595, 25], [565, 19, 595, 27, "Math"], [565, 23, 595, 31], [565, 24, 595, 32, "round"], [565, 29, 595, 37], [565, 30, 595, 38, "faceX"], [565, 35, 595, 43], [565, 36, 595, 44], [566, 16, 595, 46, "y"], [566, 17, 595, 47], [566, 19, 595, 49, "Math"], [566, 23, 595, 53], [566, 24, 595, 54, "round"], [566, 29, 595, 59], [566, 30, 595, 60, "faceY"], [566, 35, 595, 65], [566, 36, 595, 66], [567, 16, 595, 68, "w"], [567, 17, 595, 69], [567, 19, 595, 71, "Math"], [567, 23, 595, 75], [567, 24, 595, 76, "round"], [567, 29, 595, 81], [567, 30, 595, 82, "faceWidth"], [567, 39, 595, 91], [567, 40, 595, 92], [568, 16, 595, 94, "h"], [568, 17, 595, 95], [568, 19, 595, 97, "Math"], [568, 23, 595, 101], [568, 24, 595, 102, "round"], [568, 29, 595, 107], [568, 30, 595, 108, "faceHeight"], [568, 40, 595, 118], [569, 14, 595, 120], [569, 15, 595, 121], [570, 14, 596, 12, "padded"], [570, 20, 596, 18], [570, 22, 596, 20], [571, 16, 596, 22, "x"], [571, 17, 596, 23], [571, 19, 596, 25, "Math"], [571, 23, 596, 29], [571, 24, 596, 30, "round"], [571, 29, 596, 35], [571, 30, 596, 36, "paddedX"], [571, 37, 596, 43], [571, 38, 596, 44], [572, 16, 596, 46, "y"], [572, 17, 596, 47], [572, 19, 596, 49, "Math"], [572, 23, 596, 53], [572, 24, 596, 54, "round"], [572, 29, 596, 59], [572, 30, 596, 60, "paddedY"], [572, 37, 596, 67], [572, 38, 596, 68], [573, 16, 596, 70, "w"], [573, 17, 596, 71], [573, 19, 596, 73, "Math"], [573, 23, 596, 77], [573, 24, 596, 78, "round"], [573, 29, 596, 83], [573, 30, 596, 84, "<PERSON><PERSON><PERSON><PERSON>"], [573, 41, 596, 95], [573, 42, 596, 96], [574, 16, 596, 98, "h"], [574, 17, 596, 99], [574, 19, 596, 101, "Math"], [574, 23, 596, 105], [574, 24, 596, 106, "round"], [574, 29, 596, 111], [574, 30, 596, 112, "paddedHeight"], [574, 42, 596, 124], [575, 14, 596, 126], [576, 12, 597, 10], [576, 13, 597, 11], [576, 14, 597, 12], [578, 12, 599, 10], [579, 12, 600, 10, "console"], [579, 19, 600, 17], [579, 20, 600, 18, "log"], [579, 23, 600, 21], [579, 24, 600, 22], [579, 70, 600, 68], [579, 72, 600, 70], [580, 14, 601, 12, "width"], [580, 19, 601, 17], [580, 21, 601, 19, "canvas"], [580, 27, 601, 25], [580, 28, 601, 26, "width"], [580, 33, 601, 31], [581, 14, 602, 12, "height"], [581, 20, 602, 18], [581, 22, 602, 20, "canvas"], [581, 28, 602, 26], [581, 29, 602, 27, "height"], [581, 35, 602, 33], [582, 14, 603, 12, "contextValid"], [582, 26, 603, 24], [582, 28, 603, 26], [582, 29, 603, 27], [582, 30, 603, 28, "ctx"], [583, 12, 604, 10], [583, 13, 604, 11], [583, 14, 604, 12], [585, 12, 606, 10], [586, 12, 607, 10, "applyStrongBlur"], [586, 27, 607, 25], [586, 28, 607, 26, "ctx"], [586, 31, 607, 29], [586, 33, 607, 31, "paddedX"], [586, 40, 607, 38], [586, 42, 607, 40, "paddedY"], [586, 49, 607, 47], [586, 51, 607, 49, "<PERSON><PERSON><PERSON><PERSON>"], [586, 62, 607, 60], [586, 64, 607, 62, "paddedHeight"], [586, 76, 607, 74], [586, 77, 607, 75], [588, 12, 609, 10], [589, 12, 610, 10, "console"], [589, 19, 610, 17], [589, 20, 610, 18, "log"], [589, 23, 610, 21], [589, 24, 610, 22], [589, 102, 610, 100], [589, 103, 610, 101], [591, 12, 612, 10], [592, 12, 613, 10], [592, 18, 613, 16, "testImageData"], [592, 31, 613, 29], [592, 34, 613, 32, "ctx"], [592, 37, 613, 35], [592, 38, 613, 36, "getImageData"], [592, 50, 613, 48], [592, 51, 613, 49, "paddedX"], [592, 58, 613, 56], [592, 61, 613, 59], [592, 63, 613, 61], [592, 65, 613, 63, "paddedY"], [592, 72, 613, 70], [592, 75, 613, 73], [592, 77, 613, 75], [592, 79, 613, 77], [592, 81, 613, 79], [592, 83, 613, 81], [592, 85, 613, 83], [592, 86, 613, 84], [593, 12, 614, 10, "console"], [593, 19, 614, 17], [593, 20, 614, 18, "log"], [593, 23, 614, 21], [593, 24, 614, 22], [593, 70, 614, 68], [593, 72, 614, 70], [594, 14, 615, 12, "firstPixel"], [594, 24, 615, 22], [594, 26, 615, 24], [594, 27, 615, 25, "testImageData"], [594, 40, 615, 38], [594, 41, 615, 39, "data"], [594, 45, 615, 43], [594, 46, 615, 44], [594, 47, 615, 45], [594, 48, 615, 46], [594, 50, 615, 48, "testImageData"], [594, 63, 615, 61], [594, 64, 615, 62, "data"], [594, 68, 615, 66], [594, 69, 615, 67], [594, 70, 615, 68], [594, 71, 615, 69], [594, 73, 615, 71, "testImageData"], [594, 86, 615, 84], [594, 87, 615, 85, "data"], [594, 91, 615, 89], [594, 92, 615, 90], [594, 93, 615, 91], [594, 94, 615, 92], [594, 95, 615, 93], [595, 14, 616, 12, "secondPixel"], [595, 25, 616, 23], [595, 27, 616, 25], [595, 28, 616, 26, "testImageData"], [595, 41, 616, 39], [595, 42, 616, 40, "data"], [595, 46, 616, 44], [595, 47, 616, 45], [595, 48, 616, 46], [595, 49, 616, 47], [595, 51, 616, 49, "testImageData"], [595, 64, 616, 62], [595, 65, 616, 63, "data"], [595, 69, 616, 67], [595, 70, 616, 68], [595, 71, 616, 69], [595, 72, 616, 70], [595, 74, 616, 72, "testImageData"], [595, 87, 616, 85], [595, 88, 616, 86, "data"], [595, 92, 616, 90], [595, 93, 616, 91], [595, 94, 616, 92], [595, 95, 616, 93], [596, 12, 617, 10], [596, 13, 617, 11], [596, 14, 617, 12], [597, 12, 619, 10, "console"], [597, 19, 619, 17], [597, 20, 619, 18, "log"], [597, 23, 619, 21], [597, 24, 619, 22], [597, 50, 619, 48, "index"], [597, 55, 619, 53], [597, 58, 619, 56], [597, 59, 619, 57], [597, 79, 619, 77], [597, 80, 619, 78], [598, 10, 620, 8], [598, 11, 620, 9], [598, 12, 620, 10], [599, 10, 622, 8, "console"], [599, 17, 622, 15], [599, 18, 622, 16, "log"], [599, 21, 622, 19], [599, 22, 622, 20], [599, 48, 622, 46, "detectedFaces"], [599, 61, 622, 59], [599, 62, 622, 60, "length"], [599, 68, 622, 66], [599, 104, 622, 102], [599, 105, 622, 103], [600, 8, 623, 6], [600, 9, 623, 7], [600, 15, 623, 13], [601, 10, 624, 8, "console"], [601, 17, 624, 15], [601, 18, 624, 16, "log"], [601, 21, 624, 19], [601, 22, 624, 20], [601, 109, 624, 107], [601, 110, 624, 108], [602, 10, 625, 8], [603, 10, 626, 8, "applyFallbackFaceBlur"], [603, 31, 626, 29], [603, 32, 626, 30, "ctx"], [603, 35, 626, 33], [603, 37, 626, 35, "img"], [603, 40, 626, 38], [603, 41, 626, 39, "width"], [603, 46, 626, 44], [603, 48, 626, 46, "img"], [603, 51, 626, 49], [603, 52, 626, 50, "height"], [603, 58, 626, 56], [603, 59, 626, 57], [604, 8, 627, 6], [605, 8, 629, 6, "setProcessingProgress"], [605, 29, 629, 27], [605, 30, 629, 28], [605, 32, 629, 30], [605, 33, 629, 31], [607, 8, 631, 6], [608, 8, 632, 6, "console"], [608, 15, 632, 13], [608, 16, 632, 14, "log"], [608, 19, 632, 17], [608, 20, 632, 18], [608, 85, 632, 83], [608, 86, 632, 84], [609, 8, 633, 6], [609, 14, 633, 12, "blurredImageBlob"], [609, 30, 633, 28], [609, 33, 633, 31], [609, 39, 633, 37], [609, 43, 633, 41, "Promise"], [609, 50, 633, 48], [609, 51, 633, 56, "resolve"], [609, 58, 633, 63], [609, 62, 633, 68], [610, 10, 634, 8, "canvas"], [610, 16, 634, 14], [610, 17, 634, 15, "toBlob"], [610, 23, 634, 21], [610, 24, 634, 23, "blob"], [610, 28, 634, 27], [610, 32, 634, 32, "resolve"], [610, 39, 634, 39], [610, 40, 634, 40, "blob"], [610, 44, 634, 45], [610, 45, 634, 46], [610, 47, 634, 48], [610, 59, 634, 60], [610, 61, 634, 62], [610, 64, 634, 65], [610, 65, 634, 66], [611, 8, 635, 6], [611, 9, 635, 7], [611, 10, 635, 8], [612, 8, 637, 6], [612, 14, 637, 12, "blurredImageUrl"], [612, 29, 637, 27], [612, 32, 637, 30, "URL"], [612, 35, 637, 33], [612, 36, 637, 34, "createObjectURL"], [612, 51, 637, 49], [612, 52, 637, 50, "blurredImageBlob"], [612, 68, 637, 66], [612, 69, 637, 67], [613, 8, 638, 6, "console"], [613, 15, 638, 13], [613, 16, 638, 14, "log"], [613, 19, 638, 17], [613, 20, 638, 18], [613, 66, 638, 64], [613, 68, 638, 66, "blurredImageUrl"], [613, 83, 638, 81], [613, 84, 638, 82, "substring"], [613, 93, 638, 91], [613, 94, 638, 92], [613, 95, 638, 93], [613, 97, 638, 95], [613, 99, 638, 97], [613, 100, 638, 98], [613, 103, 638, 101], [613, 108, 638, 106], [613, 109, 638, 107], [614, 8, 640, 6, "setProcessingProgress"], [614, 29, 640, 27], [614, 30, 640, 28], [614, 33, 640, 31], [614, 34, 640, 32], [616, 8, 642, 6], [617, 8, 643, 6], [617, 14, 643, 12, "completeProcessing"], [617, 32, 643, 30], [617, 33, 643, 31, "blurredImageUrl"], [617, 48, 643, 46], [617, 49, 643, 47], [618, 6, 645, 4], [618, 7, 645, 5], [618, 8, 645, 6], [618, 15, 645, 13, "error"], [618, 20, 645, 18], [618, 22, 645, 20], [619, 8, 646, 6, "console"], [619, 15, 646, 13], [619, 16, 646, 14, "error"], [619, 21, 646, 19], [619, 22, 646, 20], [619, 57, 646, 55], [619, 59, 646, 57, "error"], [619, 64, 646, 62], [619, 65, 646, 63], [620, 8, 647, 6, "setErrorMessage"], [620, 23, 647, 21], [620, 24, 647, 22], [620, 50, 647, 48], [620, 51, 647, 49], [621, 8, 648, 6, "setProcessingState"], [621, 26, 648, 24], [621, 27, 648, 25], [621, 34, 648, 32], [621, 35, 648, 33], [622, 6, 649, 4], [623, 4, 650, 2], [623, 5, 650, 3], [625, 4, 652, 2], [626, 4, 653, 2], [626, 10, 653, 8, "completeProcessing"], [626, 28, 653, 26], [626, 31, 653, 29], [626, 37, 653, 36, "blurredImageUrl"], [626, 52, 653, 59], [626, 56, 653, 64], [627, 6, 654, 4], [627, 10, 654, 8], [628, 8, 655, 6, "setProcessingState"], [628, 26, 655, 24], [628, 27, 655, 25], [628, 37, 655, 35], [628, 38, 655, 36], [630, 8, 657, 6], [631, 8, 658, 6], [631, 14, 658, 12, "timestamp"], [631, 23, 658, 21], [631, 26, 658, 24, "Date"], [631, 30, 658, 28], [631, 31, 658, 29, "now"], [631, 34, 658, 32], [631, 35, 658, 33], [631, 36, 658, 34], [632, 8, 659, 6], [632, 14, 659, 12, "result"], [632, 20, 659, 18], [632, 23, 659, 21], [633, 10, 660, 8, "imageUrl"], [633, 18, 660, 16], [633, 20, 660, 18, "blurredImageUrl"], [633, 35, 660, 33], [634, 10, 661, 8, "localUri"], [634, 18, 661, 16], [634, 20, 661, 18, "blurredImageUrl"], [634, 35, 661, 33], [635, 10, 662, 8, "challengeCode"], [635, 23, 662, 21], [635, 25, 662, 23, "challengeCode"], [635, 38, 662, 36], [635, 42, 662, 40], [635, 44, 662, 42], [636, 10, 663, 8, "timestamp"], [636, 19, 663, 17], [637, 10, 664, 8, "jobId"], [637, 15, 664, 13], [637, 17, 664, 15], [637, 27, 664, 25, "timestamp"], [637, 36, 664, 34], [637, 38, 664, 36], [638, 10, 665, 8, "status"], [638, 16, 665, 14], [638, 18, 665, 16], [639, 8, 666, 6], [639, 9, 666, 7], [640, 8, 668, 6, "console"], [640, 15, 668, 13], [640, 16, 668, 14, "log"], [640, 19, 668, 17], [640, 20, 668, 18], [640, 100, 668, 98], [640, 102, 668, 100], [641, 10, 669, 8, "imageUrl"], [641, 18, 669, 16], [641, 20, 669, 18, "blurredImageUrl"], [641, 35, 669, 33], [641, 36, 669, 34, "substring"], [641, 45, 669, 43], [641, 46, 669, 44], [641, 47, 669, 45], [641, 49, 669, 47], [641, 51, 669, 49], [641, 52, 669, 50], [641, 55, 669, 53], [641, 60, 669, 58], [642, 10, 670, 8, "timestamp"], [642, 19, 670, 17], [643, 10, 671, 8, "jobId"], [643, 15, 671, 13], [643, 17, 671, 15, "result"], [643, 23, 671, 21], [643, 24, 671, 22, "jobId"], [644, 8, 672, 6], [644, 9, 672, 7], [644, 10, 672, 8], [646, 8, 674, 6], [647, 8, 675, 6, "onComplete"], [647, 18, 675, 16], [647, 19, 675, 17, "result"], [647, 25, 675, 23], [647, 26, 675, 24], [648, 6, 677, 4], [648, 7, 677, 5], [648, 8, 677, 6], [648, 15, 677, 13, "error"], [648, 20, 677, 18], [648, 22, 677, 20], [649, 8, 678, 6, "console"], [649, 15, 678, 13], [649, 16, 678, 14, "error"], [649, 21, 678, 19], [649, 22, 678, 20], [649, 57, 678, 55], [649, 59, 678, 57, "error"], [649, 64, 678, 62], [649, 65, 678, 63], [650, 8, 679, 6, "setErrorMessage"], [650, 23, 679, 21], [650, 24, 679, 22], [650, 56, 679, 54], [650, 57, 679, 55], [651, 8, 680, 6, "setProcessingState"], [651, 26, 680, 24], [651, 27, 680, 25], [651, 34, 680, 32], [651, 35, 680, 33], [652, 6, 681, 4], [653, 4, 682, 2], [653, 5, 682, 3], [655, 4, 684, 2], [656, 4, 685, 2], [656, 10, 685, 8, "triggerServerProcessing"], [656, 33, 685, 31], [656, 36, 685, 34], [656, 42, 685, 34, "triggerServerProcessing"], [656, 43, 685, 41, "privateImageUrl"], [656, 58, 685, 64], [656, 60, 685, 66, "timestamp"], [656, 69, 685, 83], [656, 74, 685, 88], [657, 6, 686, 4], [657, 10, 686, 8], [658, 8, 687, 6, "console"], [658, 15, 687, 13], [658, 16, 687, 14, "log"], [658, 19, 687, 17], [658, 20, 687, 18], [658, 74, 687, 72], [658, 76, 687, 74, "privateImageUrl"], [658, 91, 687, 89], [658, 92, 687, 90], [659, 8, 688, 6, "setProcessingState"], [659, 26, 688, 24], [659, 27, 688, 25], [659, 39, 688, 37], [659, 40, 688, 38], [660, 8, 689, 6, "setProcessingProgress"], [660, 29, 689, 27], [660, 30, 689, 28], [660, 32, 689, 30], [660, 33, 689, 31], [661, 8, 691, 6], [661, 14, 691, 12, "requestBody"], [661, 25, 691, 23], [661, 28, 691, 26], [662, 10, 692, 8, "imageUrl"], [662, 18, 692, 16], [662, 20, 692, 18, "privateImageUrl"], [662, 35, 692, 33], [663, 10, 693, 8, "userId"], [663, 16, 693, 14], [664, 10, 694, 8, "requestId"], [664, 19, 694, 17], [665, 10, 695, 8, "timestamp"], [665, 19, 695, 17], [666, 10, 696, 8, "platform"], [666, 18, 696, 16], [666, 20, 696, 18], [667, 8, 697, 6], [667, 9, 697, 7], [668, 8, 699, 6, "console"], [668, 15, 699, 13], [668, 16, 699, 14, "log"], [668, 19, 699, 17], [668, 20, 699, 18], [668, 65, 699, 63], [668, 67, 699, 65, "requestBody"], [668, 78, 699, 76], [668, 79, 699, 77], [670, 8, 701, 6], [671, 8, 702, 6], [671, 14, 702, 12, "response"], [671, 22, 702, 20], [671, 25, 702, 23], [671, 31, 702, 29, "fetch"], [671, 36, 702, 34], [671, 37, 702, 35], [671, 40, 702, 38, "API_BASE_URL"], [671, 52, 702, 50], [671, 72, 702, 70], [671, 74, 702, 72], [672, 10, 703, 8, "method"], [672, 16, 703, 14], [672, 18, 703, 16], [672, 24, 703, 22], [673, 10, 704, 8, "headers"], [673, 17, 704, 15], [673, 19, 704, 17], [674, 12, 705, 10], [674, 26, 705, 24], [674, 28, 705, 26], [674, 46, 705, 44], [675, 12, 706, 10], [675, 27, 706, 25], [675, 29, 706, 27], [675, 39, 706, 37], [675, 45, 706, 43, "getAuthToken"], [675, 57, 706, 55], [675, 58, 706, 56], [675, 59, 706, 57], [676, 10, 707, 8], [676, 11, 707, 9], [677, 10, 708, 8, "body"], [677, 14, 708, 12], [677, 16, 708, 14, "JSON"], [677, 20, 708, 18], [677, 21, 708, 19, "stringify"], [677, 30, 708, 28], [677, 31, 708, 29, "requestBody"], [677, 42, 708, 40], [678, 8, 709, 6], [678, 9, 709, 7], [678, 10, 709, 8], [679, 8, 711, 6], [679, 12, 711, 10], [679, 13, 711, 11, "response"], [679, 21, 711, 19], [679, 22, 711, 20, "ok"], [679, 24, 711, 22], [679, 26, 711, 24], [680, 10, 712, 8], [680, 16, 712, 14, "errorText"], [680, 25, 712, 23], [680, 28, 712, 26], [680, 34, 712, 32, "response"], [680, 42, 712, 40], [680, 43, 712, 41, "text"], [680, 47, 712, 45], [680, 48, 712, 46], [680, 49, 712, 47], [681, 10, 713, 8, "console"], [681, 17, 713, 15], [681, 18, 713, 16, "error"], [681, 23, 713, 21], [681, 24, 713, 22], [681, 68, 713, 66], [681, 70, 713, 68, "response"], [681, 78, 713, 76], [681, 79, 713, 77, "status"], [681, 85, 713, 83], [681, 87, 713, 85, "errorText"], [681, 96, 713, 94], [681, 97, 713, 95], [682, 10, 714, 8], [682, 16, 714, 14], [682, 20, 714, 18, "Error"], [682, 25, 714, 23], [682, 26, 714, 24], [682, 48, 714, 46, "response"], [682, 56, 714, 54], [682, 57, 714, 55, "status"], [682, 63, 714, 61], [682, 67, 714, 65, "response"], [682, 75, 714, 73], [682, 76, 714, 74, "statusText"], [682, 86, 714, 84], [682, 88, 714, 86], [682, 89, 714, 87], [683, 8, 715, 6], [684, 8, 717, 6], [684, 14, 717, 12, "result"], [684, 20, 717, 18], [684, 23, 717, 21], [684, 29, 717, 27, "response"], [684, 37, 717, 35], [684, 38, 717, 36, "json"], [684, 42, 717, 40], [684, 43, 717, 41], [684, 44, 717, 42], [685, 8, 718, 6, "console"], [685, 15, 718, 13], [685, 16, 718, 14, "log"], [685, 19, 718, 17], [685, 20, 718, 18], [685, 68, 718, 66], [685, 70, 718, 68, "result"], [685, 76, 718, 74], [685, 77, 718, 75], [686, 8, 720, 6], [686, 12, 720, 10], [686, 13, 720, 11, "result"], [686, 19, 720, 17], [686, 20, 720, 18, "jobId"], [686, 25, 720, 23], [686, 27, 720, 25], [687, 10, 721, 8], [687, 16, 721, 14], [687, 20, 721, 18, "Error"], [687, 25, 721, 23], [687, 26, 721, 24], [687, 70, 721, 68], [687, 71, 721, 69], [688, 8, 722, 6], [690, 8, 724, 6], [691, 8, 725, 6], [691, 14, 725, 12, "pollForCompletion"], [691, 31, 725, 29], [691, 32, 725, 30, "result"], [691, 38, 725, 36], [691, 39, 725, 37, "jobId"], [691, 44, 725, 42], [691, 46, 725, 44, "timestamp"], [691, 55, 725, 53], [691, 56, 725, 54], [692, 6, 726, 4], [692, 7, 726, 5], [692, 8, 726, 6], [692, 15, 726, 13, "error"], [692, 20, 726, 18], [692, 22, 726, 20], [693, 8, 727, 6, "console"], [693, 15, 727, 13], [693, 16, 727, 14, "error"], [693, 21, 727, 19], [693, 22, 727, 20], [693, 57, 727, 55], [693, 59, 727, 57, "error"], [693, 64, 727, 62], [693, 65, 727, 63], [694, 8, 728, 6, "setErrorMessage"], [694, 23, 728, 21], [694, 24, 728, 22], [694, 52, 728, 50, "error"], [694, 57, 728, 55], [694, 58, 728, 56, "message"], [694, 65, 728, 63], [694, 67, 728, 65], [694, 68, 728, 66], [695, 8, 729, 6, "setProcessingState"], [695, 26, 729, 24], [695, 27, 729, 25], [695, 34, 729, 32], [695, 35, 729, 33], [696, 6, 730, 4], [697, 4, 731, 2], [697, 5, 731, 3], [698, 4, 732, 2], [699, 4, 733, 2], [699, 10, 733, 8, "pollForCompletion"], [699, 27, 733, 25], [699, 30, 733, 28], [699, 36, 733, 28, "pollForCompletion"], [699, 37, 733, 35, "jobId"], [699, 42, 733, 48], [699, 44, 733, 50, "timestamp"], [699, 53, 733, 67], [699, 55, 733, 69, "attempts"], [699, 63, 733, 77], [699, 66, 733, 80], [699, 67, 733, 81], [699, 72, 733, 86], [700, 6, 734, 4], [700, 12, 734, 10, "MAX_ATTEMPTS"], [700, 24, 734, 22], [700, 27, 734, 25], [700, 29, 734, 27], [700, 30, 734, 28], [700, 31, 734, 29], [701, 6, 735, 4], [701, 12, 735, 10, "POLL_INTERVAL"], [701, 25, 735, 23], [701, 28, 735, 26], [701, 32, 735, 30], [701, 33, 735, 31], [701, 34, 735, 32], [703, 6, 737, 4, "console"], [703, 13, 737, 11], [703, 14, 737, 12, "log"], [703, 17, 737, 15], [703, 18, 737, 16], [703, 53, 737, 51, "attempts"], [703, 61, 737, 59], [703, 64, 737, 62], [703, 65, 737, 63], [703, 69, 737, 67, "MAX_ATTEMPTS"], [703, 81, 737, 79], [703, 93, 737, 91, "jobId"], [703, 98, 737, 96], [703, 100, 737, 98], [703, 101, 737, 99], [704, 6, 739, 4], [704, 10, 739, 8, "attempts"], [704, 18, 739, 16], [704, 22, 739, 20, "MAX_ATTEMPTS"], [704, 34, 739, 32], [704, 36, 739, 34], [705, 8, 740, 6, "console"], [705, 15, 740, 13], [705, 16, 740, 14, "error"], [705, 21, 740, 19], [705, 22, 740, 20], [705, 75, 740, 73], [705, 76, 740, 74], [706, 8, 741, 6, "setErrorMessage"], [706, 23, 741, 21], [706, 24, 741, 22], [706, 63, 741, 61], [706, 64, 741, 62], [707, 8, 742, 6, "setProcessingState"], [707, 26, 742, 24], [707, 27, 742, 25], [707, 34, 742, 32], [707, 35, 742, 33], [708, 8, 743, 6], [709, 6, 744, 4], [710, 6, 746, 4], [710, 10, 746, 8], [711, 8, 747, 6], [711, 14, 747, 12, "response"], [711, 22, 747, 20], [711, 25, 747, 23], [711, 31, 747, 29, "fetch"], [711, 36, 747, 34], [711, 37, 747, 35], [711, 40, 747, 38, "API_BASE_URL"], [711, 52, 747, 50], [711, 75, 747, 73, "jobId"], [711, 80, 747, 78], [711, 82, 747, 80], [711, 84, 747, 82], [712, 10, 748, 8, "headers"], [712, 17, 748, 15], [712, 19, 748, 17], [713, 12, 749, 10], [713, 27, 749, 25], [713, 29, 749, 27], [713, 39, 749, 37], [713, 45, 749, 43, "getAuthToken"], [713, 57, 749, 55], [713, 58, 749, 56], [713, 59, 749, 57], [714, 10, 750, 8], [715, 8, 751, 6], [715, 9, 751, 7], [715, 10, 751, 8], [716, 8, 753, 6], [716, 12, 753, 10], [716, 13, 753, 11, "response"], [716, 21, 753, 19], [716, 22, 753, 20, "ok"], [716, 24, 753, 22], [716, 26, 753, 24], [717, 10, 754, 8], [717, 16, 754, 14], [717, 20, 754, 18, "Error"], [717, 25, 754, 23], [717, 26, 754, 24], [717, 34, 754, 32, "response"], [717, 42, 754, 40], [717, 43, 754, 41, "status"], [717, 49, 754, 47], [717, 54, 754, 52, "response"], [717, 62, 754, 60], [717, 63, 754, 61, "statusText"], [717, 73, 754, 71], [717, 75, 754, 73], [717, 76, 754, 74], [718, 8, 755, 6], [719, 8, 757, 6], [719, 14, 757, 12, "status"], [719, 20, 757, 18], [719, 23, 757, 21], [719, 29, 757, 27, "response"], [719, 37, 757, 35], [719, 38, 757, 36, "json"], [719, 42, 757, 40], [719, 43, 757, 41], [719, 44, 757, 42], [720, 8, 758, 6, "console"], [720, 15, 758, 13], [720, 16, 758, 14, "log"], [720, 19, 758, 17], [720, 20, 758, 18], [720, 54, 758, 52], [720, 56, 758, 54, "status"], [720, 62, 758, 60], [720, 63, 758, 61], [721, 8, 760, 6], [721, 12, 760, 10, "status"], [721, 18, 760, 16], [721, 19, 760, 17, "status"], [721, 25, 760, 23], [721, 30, 760, 28], [721, 41, 760, 39], [721, 43, 760, 41], [722, 10, 761, 8, "console"], [722, 17, 761, 15], [722, 18, 761, 16, "log"], [722, 21, 761, 19], [722, 22, 761, 20], [722, 73, 761, 71], [722, 74, 761, 72], [723, 10, 762, 8, "setProcessingProgress"], [723, 31, 762, 29], [723, 32, 762, 30], [723, 35, 762, 33], [723, 36, 762, 34], [724, 10, 763, 8, "setProcessingState"], [724, 28, 763, 26], [724, 29, 763, 27], [724, 40, 763, 38], [724, 41, 763, 39], [725, 10, 764, 8], [726, 10, 765, 8], [726, 16, 765, 14, "result"], [726, 22, 765, 20], [726, 25, 765, 23], [727, 12, 766, 10, "imageUrl"], [727, 20, 766, 18], [727, 22, 766, 20, "status"], [727, 28, 766, 26], [727, 29, 766, 27, "publicUrl"], [727, 38, 766, 36], [728, 12, 766, 38], [729, 12, 767, 10, "localUri"], [729, 20, 767, 18], [729, 22, 767, 20, "capturedPhoto"], [729, 35, 767, 33], [729, 39, 767, 37, "status"], [729, 45, 767, 43], [729, 46, 767, 44, "publicUrl"], [729, 55, 767, 53], [730, 12, 767, 55], [731, 12, 768, 10, "challengeCode"], [731, 25, 768, 23], [731, 27, 768, 25, "challengeCode"], [731, 40, 768, 38], [731, 44, 768, 42], [731, 46, 768, 44], [732, 12, 769, 10, "timestamp"], [732, 21, 769, 19], [733, 12, 770, 10, "processingStatus"], [733, 28, 770, 26], [733, 30, 770, 28], [734, 10, 771, 8], [734, 11, 771, 9], [735, 10, 772, 8, "console"], [735, 17, 772, 15], [735, 18, 772, 16, "log"], [735, 21, 772, 19], [735, 22, 772, 20], [735, 57, 772, 55], [735, 59, 772, 57, "result"], [735, 65, 772, 63], [735, 66, 772, 64], [736, 10, 773, 8, "onComplete"], [736, 20, 773, 18], [736, 21, 773, 19, "result"], [736, 27, 773, 25], [736, 28, 773, 26], [737, 10, 774, 8], [738, 8, 775, 6], [738, 9, 775, 7], [738, 15, 775, 13], [738, 19, 775, 17, "status"], [738, 25, 775, 23], [738, 26, 775, 24, "status"], [738, 32, 775, 30], [738, 37, 775, 35], [738, 45, 775, 43], [738, 47, 775, 45], [739, 10, 776, 8, "console"], [739, 17, 776, 15], [739, 18, 776, 16, "error"], [739, 23, 776, 21], [739, 24, 776, 22], [739, 60, 776, 58], [739, 62, 776, 60, "status"], [739, 68, 776, 66], [739, 69, 776, 67, "error"], [739, 74, 776, 72], [739, 75, 776, 73], [740, 10, 777, 8], [740, 16, 777, 14], [740, 20, 777, 18, "Error"], [740, 25, 777, 23], [740, 26, 777, 24, "status"], [740, 32, 777, 30], [740, 33, 777, 31, "error"], [740, 38, 777, 36], [740, 42, 777, 40], [740, 61, 777, 59], [740, 62, 777, 60], [741, 8, 778, 6], [741, 9, 778, 7], [741, 15, 778, 13], [742, 10, 779, 8], [743, 10, 780, 8], [743, 16, 780, 14, "progressValue"], [743, 29, 780, 27], [743, 32, 780, 30], [743, 34, 780, 32], [743, 37, 780, 36, "attempts"], [743, 45, 780, 44], [743, 48, 780, 47, "MAX_ATTEMPTS"], [743, 60, 780, 59], [743, 63, 780, 63], [743, 65, 780, 65], [744, 10, 781, 8, "console"], [744, 17, 781, 15], [744, 18, 781, 16, "log"], [744, 21, 781, 19], [744, 22, 781, 20], [744, 71, 781, 69, "progressValue"], [744, 84, 781, 82], [744, 87, 781, 85], [744, 88, 781, 86], [745, 10, 782, 8, "setProcessingProgress"], [745, 31, 782, 29], [745, 32, 782, 30, "progressValue"], [745, 45, 782, 43], [745, 46, 782, 44], [746, 10, 784, 8, "setTimeout"], [746, 20, 784, 18], [746, 21, 784, 19], [746, 27, 784, 25], [747, 12, 785, 10, "pollForCompletion"], [747, 29, 785, 27], [747, 30, 785, 28, "jobId"], [747, 35, 785, 33], [747, 37, 785, 35, "timestamp"], [747, 46, 785, 44], [747, 48, 785, 46, "attempts"], [747, 56, 785, 54], [747, 59, 785, 57], [747, 60, 785, 58], [747, 61, 785, 59], [748, 10, 786, 8], [748, 11, 786, 9], [748, 13, 786, 11, "POLL_INTERVAL"], [748, 26, 786, 24], [748, 27, 786, 25], [749, 8, 787, 6], [750, 6, 788, 4], [750, 7, 788, 5], [750, 8, 788, 6], [750, 15, 788, 13, "error"], [750, 20, 788, 18], [750, 22, 788, 20], [751, 8, 789, 6, "console"], [751, 15, 789, 13], [751, 16, 789, 14, "error"], [751, 21, 789, 19], [751, 22, 789, 20], [751, 54, 789, 52], [751, 56, 789, 54, "error"], [751, 61, 789, 59], [751, 62, 789, 60], [752, 8, 790, 6, "setErrorMessage"], [752, 23, 790, 21], [752, 24, 790, 22], [752, 62, 790, 60, "error"], [752, 67, 790, 65], [752, 68, 790, 66, "message"], [752, 75, 790, 73], [752, 77, 790, 75], [752, 78, 790, 76], [753, 8, 791, 6, "setProcessingState"], [753, 26, 791, 24], [753, 27, 791, 25], [753, 34, 791, 32], [753, 35, 791, 33], [754, 6, 792, 4], [755, 4, 793, 2], [755, 5, 793, 3], [756, 4, 794, 2], [757, 4, 795, 2], [757, 10, 795, 8, "getAuthToken"], [757, 22, 795, 20], [757, 25, 795, 23], [757, 31, 795, 23, "getAuthToken"], [757, 32, 795, 23], [757, 37, 795, 52], [758, 6, 796, 4], [759, 6, 797, 4], [760, 6, 798, 4], [760, 13, 798, 11], [760, 30, 798, 28], [761, 4, 799, 2], [761, 5, 799, 3], [763, 4, 801, 2], [764, 4, 802, 2], [764, 10, 802, 8, "retryCapture"], [764, 22, 802, 20], [764, 25, 802, 23], [764, 29, 802, 23, "useCallback"], [764, 47, 802, 34], [764, 49, 802, 35], [764, 55, 802, 41], [765, 6, 803, 4, "console"], [765, 13, 803, 11], [765, 14, 803, 12, "log"], [765, 17, 803, 15], [765, 18, 803, 16], [765, 55, 803, 53], [765, 56, 803, 54], [766, 6, 804, 4, "setProcessingState"], [766, 24, 804, 22], [766, 25, 804, 23], [766, 31, 804, 29], [766, 32, 804, 30], [767, 6, 805, 4, "setErrorMessage"], [767, 21, 805, 19], [767, 22, 805, 20], [767, 24, 805, 22], [767, 25, 805, 23], [768, 6, 806, 4, "setCapturedPhoto"], [768, 22, 806, 20], [768, 23, 806, 21], [768, 25, 806, 23], [768, 26, 806, 24], [769, 6, 807, 4, "setProcessingProgress"], [769, 27, 807, 25], [769, 28, 807, 26], [769, 29, 807, 27], [769, 30, 807, 28], [770, 4, 808, 2], [770, 5, 808, 3], [770, 7, 808, 5], [770, 9, 808, 7], [770, 10, 808, 8], [771, 4, 809, 2], [772, 4, 810, 2], [772, 8, 810, 2, "useEffect"], [772, 24, 810, 11], [772, 26, 810, 12], [772, 32, 810, 18], [773, 6, 811, 4, "console"], [773, 13, 811, 11], [773, 14, 811, 12, "log"], [773, 17, 811, 15], [773, 18, 811, 16], [773, 53, 811, 51], [773, 55, 811, 53, "permission"], [773, 65, 811, 63], [773, 66, 811, 64], [774, 6, 812, 4], [774, 10, 812, 8, "permission"], [774, 20, 812, 18], [774, 22, 812, 20], [775, 8, 813, 6, "console"], [775, 15, 813, 13], [775, 16, 813, 14, "log"], [775, 19, 813, 17], [775, 20, 813, 18], [775, 57, 813, 55], [775, 59, 813, 57, "permission"], [775, 69, 813, 67], [775, 70, 813, 68, "granted"], [775, 77, 813, 75], [775, 78, 813, 76], [776, 6, 814, 4], [777, 4, 815, 2], [777, 5, 815, 3], [777, 7, 815, 5], [777, 8, 815, 6, "permission"], [777, 18, 815, 16], [777, 19, 815, 17], [777, 20, 815, 18], [778, 4, 816, 2], [779, 4, 817, 2], [779, 8, 817, 6], [779, 9, 817, 7, "permission"], [779, 19, 817, 17], [779, 21, 817, 19], [780, 6, 818, 4, "console"], [780, 13, 818, 11], [780, 14, 818, 12, "log"], [780, 17, 818, 15], [780, 18, 818, 16], [780, 67, 818, 65], [780, 68, 818, 66], [781, 6, 819, 4], [781, 26, 820, 6], [781, 30, 820, 6, "_jsxDevRuntime"], [781, 44, 820, 6], [781, 45, 820, 6, "jsxDEV"], [781, 51, 820, 6], [781, 53, 820, 7, "_View"], [781, 58, 820, 7], [781, 59, 820, 7, "default"], [781, 66, 820, 11], [782, 8, 820, 12, "style"], [782, 13, 820, 17], [782, 15, 820, 19, "styles"], [782, 21, 820, 25], [782, 22, 820, 26, "container"], [782, 31, 820, 36], [783, 8, 820, 36, "children"], [783, 16, 820, 36], [783, 32, 821, 8], [783, 36, 821, 8, "_jsxDevRuntime"], [783, 50, 821, 8], [783, 51, 821, 8, "jsxDEV"], [783, 57, 821, 8], [783, 59, 821, 9, "_ActivityIndicator"], [783, 77, 821, 9], [783, 78, 821, 9, "default"], [783, 85, 821, 26], [784, 10, 821, 27, "size"], [784, 14, 821, 31], [784, 16, 821, 32], [784, 23, 821, 39], [785, 10, 821, 40, "color"], [785, 15, 821, 45], [785, 17, 821, 46], [786, 8, 821, 55], [787, 10, 821, 55, "fileName"], [787, 18, 821, 55], [787, 20, 821, 55, "_jsxFileName"], [787, 32, 821, 55], [788, 10, 821, 55, "lineNumber"], [788, 20, 821, 55], [789, 10, 821, 55, "columnNumber"], [789, 22, 821, 55], [790, 8, 821, 55], [790, 15, 821, 57], [790, 16, 821, 58], [790, 31, 822, 8], [790, 35, 822, 8, "_jsxDevRuntime"], [790, 49, 822, 8], [790, 50, 822, 8, "jsxDEV"], [790, 56, 822, 8], [790, 58, 822, 9, "_Text"], [790, 63, 822, 9], [790, 64, 822, 9, "default"], [790, 71, 822, 13], [791, 10, 822, 14, "style"], [791, 15, 822, 19], [791, 17, 822, 21, "styles"], [791, 23, 822, 27], [791, 24, 822, 28, "loadingText"], [791, 35, 822, 40], [792, 10, 822, 40, "children"], [792, 18, 822, 40], [792, 20, 822, 41], [793, 8, 822, 58], [794, 10, 822, 58, "fileName"], [794, 18, 822, 58], [794, 20, 822, 58, "_jsxFileName"], [794, 32, 822, 58], [795, 10, 822, 58, "lineNumber"], [795, 20, 822, 58], [796, 10, 822, 58, "columnNumber"], [796, 22, 822, 58], [797, 8, 822, 58], [797, 15, 822, 64], [797, 16, 822, 65], [798, 6, 822, 65], [799, 8, 822, 65, "fileName"], [799, 16, 822, 65], [799, 18, 822, 65, "_jsxFileName"], [799, 30, 822, 65], [800, 8, 822, 65, "lineNumber"], [800, 18, 822, 65], [801, 8, 822, 65, "columnNumber"], [801, 20, 822, 65], [802, 6, 822, 65], [802, 13, 823, 12], [802, 14, 823, 13], [803, 4, 825, 2], [804, 4, 826, 2], [804, 8, 826, 6], [804, 9, 826, 7, "permission"], [804, 19, 826, 17], [804, 20, 826, 18, "granted"], [804, 27, 826, 25], [804, 29, 826, 27], [805, 6, 827, 4, "console"], [805, 13, 827, 11], [805, 14, 827, 12, "log"], [805, 17, 827, 15], [805, 18, 827, 16], [805, 93, 827, 91], [805, 94, 827, 92], [806, 6, 828, 4], [806, 26, 829, 6], [806, 30, 829, 6, "_jsxDevRuntime"], [806, 44, 829, 6], [806, 45, 829, 6, "jsxDEV"], [806, 51, 829, 6], [806, 53, 829, 7, "_View"], [806, 58, 829, 7], [806, 59, 829, 7, "default"], [806, 66, 829, 11], [807, 8, 829, 12, "style"], [807, 13, 829, 17], [807, 15, 829, 19, "styles"], [807, 21, 829, 25], [807, 22, 829, 26, "container"], [807, 31, 829, 36], [808, 8, 829, 36, "children"], [808, 16, 829, 36], [808, 31, 830, 8], [808, 35, 830, 8, "_jsxDevRuntime"], [808, 49, 830, 8], [808, 50, 830, 8, "jsxDEV"], [808, 56, 830, 8], [808, 58, 830, 9, "_View"], [808, 63, 830, 9], [808, 64, 830, 9, "default"], [808, 71, 830, 13], [809, 10, 830, 14, "style"], [809, 15, 830, 19], [809, 17, 830, 21, "styles"], [809, 23, 830, 27], [809, 24, 830, 28, "permissionContent"], [809, 41, 830, 46], [810, 10, 830, 46, "children"], [810, 18, 830, 46], [810, 34, 831, 10], [810, 38, 831, 10, "_jsxDevRuntime"], [810, 52, 831, 10], [810, 53, 831, 10, "jsxDEV"], [810, 59, 831, 10], [810, 61, 831, 11, "_lucideReactNative"], [810, 79, 831, 11], [810, 80, 831, 11, "Camera"], [810, 86, 831, 21], [811, 12, 831, 22, "size"], [811, 16, 831, 26], [811, 18, 831, 28], [811, 20, 831, 31], [812, 12, 831, 32, "color"], [812, 17, 831, 37], [812, 19, 831, 38], [813, 10, 831, 47], [814, 12, 831, 47, "fileName"], [814, 20, 831, 47], [814, 22, 831, 47, "_jsxFileName"], [814, 34, 831, 47], [815, 12, 831, 47, "lineNumber"], [815, 22, 831, 47], [816, 12, 831, 47, "columnNumber"], [816, 24, 831, 47], [817, 10, 831, 47], [817, 17, 831, 49], [817, 18, 831, 50], [817, 33, 832, 10], [817, 37, 832, 10, "_jsxDevRuntime"], [817, 51, 832, 10], [817, 52, 832, 10, "jsxDEV"], [817, 58, 832, 10], [817, 60, 832, 11, "_Text"], [817, 65, 832, 11], [817, 66, 832, 11, "default"], [817, 73, 832, 15], [818, 12, 832, 16, "style"], [818, 17, 832, 21], [818, 19, 832, 23, "styles"], [818, 25, 832, 29], [818, 26, 832, 30, "permissionTitle"], [818, 41, 832, 46], [819, 12, 832, 46, "children"], [819, 20, 832, 46], [819, 22, 832, 47], [820, 10, 832, 73], [821, 12, 832, 73, "fileName"], [821, 20, 832, 73], [821, 22, 832, 73, "_jsxFileName"], [821, 34, 832, 73], [822, 12, 832, 73, "lineNumber"], [822, 22, 832, 73], [823, 12, 832, 73, "columnNumber"], [823, 24, 832, 73], [824, 10, 832, 73], [824, 17, 832, 79], [824, 18, 832, 80], [824, 33, 833, 10], [824, 37, 833, 10, "_jsxDevRuntime"], [824, 51, 833, 10], [824, 52, 833, 10, "jsxDEV"], [824, 58, 833, 10], [824, 60, 833, 11, "_Text"], [824, 65, 833, 11], [824, 66, 833, 11, "default"], [824, 73, 833, 15], [825, 12, 833, 16, "style"], [825, 17, 833, 21], [825, 19, 833, 23, "styles"], [825, 25, 833, 29], [825, 26, 833, 30, "permissionDescription"], [825, 47, 833, 52], [826, 12, 833, 52, "children"], [826, 20, 833, 52], [826, 22, 833, 53], [827, 10, 836, 10], [828, 12, 836, 10, "fileName"], [828, 20, 836, 10], [828, 22, 836, 10, "_jsxFileName"], [828, 34, 836, 10], [829, 12, 836, 10, "lineNumber"], [829, 22, 836, 10], [830, 12, 836, 10, "columnNumber"], [830, 24, 836, 10], [831, 10, 836, 10], [831, 17, 836, 16], [831, 18, 836, 17], [831, 33, 837, 10], [831, 37, 837, 10, "_jsxDevRuntime"], [831, 51, 837, 10], [831, 52, 837, 10, "jsxDEV"], [831, 58, 837, 10], [831, 60, 837, 11, "_TouchableOpacity"], [831, 77, 837, 11], [831, 78, 837, 11, "default"], [831, 85, 837, 27], [832, 12, 837, 28, "onPress"], [832, 19, 837, 35], [832, 21, 837, 37, "requestPermission"], [832, 38, 837, 55], [833, 12, 837, 56, "style"], [833, 17, 837, 61], [833, 19, 837, 63, "styles"], [833, 25, 837, 69], [833, 26, 837, 70, "primaryButton"], [833, 39, 837, 84], [834, 12, 837, 84, "children"], [834, 20, 837, 84], [834, 35, 838, 12], [834, 39, 838, 12, "_jsxDevRuntime"], [834, 53, 838, 12], [834, 54, 838, 12, "jsxDEV"], [834, 60, 838, 12], [834, 62, 838, 13, "_Text"], [834, 67, 838, 13], [834, 68, 838, 13, "default"], [834, 75, 838, 17], [835, 14, 838, 18, "style"], [835, 19, 838, 23], [835, 21, 838, 25, "styles"], [835, 27, 838, 31], [835, 28, 838, 32, "primaryButtonText"], [835, 45, 838, 50], [836, 14, 838, 50, "children"], [836, 22, 838, 50], [836, 24, 838, 51], [837, 12, 838, 67], [838, 14, 838, 67, "fileName"], [838, 22, 838, 67], [838, 24, 838, 67, "_jsxFileName"], [838, 36, 838, 67], [839, 14, 838, 67, "lineNumber"], [839, 24, 838, 67], [840, 14, 838, 67, "columnNumber"], [840, 26, 838, 67], [841, 12, 838, 67], [841, 19, 838, 73], [842, 10, 838, 74], [843, 12, 838, 74, "fileName"], [843, 20, 838, 74], [843, 22, 838, 74, "_jsxFileName"], [843, 34, 838, 74], [844, 12, 838, 74, "lineNumber"], [844, 22, 838, 74], [845, 12, 838, 74, "columnNumber"], [845, 24, 838, 74], [846, 10, 838, 74], [846, 17, 839, 28], [846, 18, 839, 29], [846, 33, 840, 10], [846, 37, 840, 10, "_jsxDevRuntime"], [846, 51, 840, 10], [846, 52, 840, 10, "jsxDEV"], [846, 58, 840, 10], [846, 60, 840, 11, "_TouchableOpacity"], [846, 77, 840, 11], [846, 78, 840, 11, "default"], [846, 85, 840, 27], [847, 12, 840, 28, "onPress"], [847, 19, 840, 35], [847, 21, 840, 37, "onCancel"], [847, 29, 840, 46], [848, 12, 840, 47, "style"], [848, 17, 840, 52], [848, 19, 840, 54, "styles"], [848, 25, 840, 60], [848, 26, 840, 61, "secondaryButton"], [848, 41, 840, 77], [849, 12, 840, 77, "children"], [849, 20, 840, 77], [849, 35, 841, 12], [849, 39, 841, 12, "_jsxDevRuntime"], [849, 53, 841, 12], [849, 54, 841, 12, "jsxDEV"], [849, 60, 841, 12], [849, 62, 841, 13, "_Text"], [849, 67, 841, 13], [849, 68, 841, 13, "default"], [849, 75, 841, 17], [850, 14, 841, 18, "style"], [850, 19, 841, 23], [850, 21, 841, 25, "styles"], [850, 27, 841, 31], [850, 28, 841, 32, "secondaryButtonText"], [850, 47, 841, 52], [851, 14, 841, 52, "children"], [851, 22, 841, 52], [851, 24, 841, 53], [852, 12, 841, 59], [853, 14, 841, 59, "fileName"], [853, 22, 841, 59], [853, 24, 841, 59, "_jsxFileName"], [853, 36, 841, 59], [854, 14, 841, 59, "lineNumber"], [854, 24, 841, 59], [855, 14, 841, 59, "columnNumber"], [855, 26, 841, 59], [856, 12, 841, 59], [856, 19, 841, 65], [857, 10, 841, 66], [858, 12, 841, 66, "fileName"], [858, 20, 841, 66], [858, 22, 841, 66, "_jsxFileName"], [858, 34, 841, 66], [859, 12, 841, 66, "lineNumber"], [859, 22, 841, 66], [860, 12, 841, 66, "columnNumber"], [860, 24, 841, 66], [861, 10, 841, 66], [861, 17, 842, 28], [861, 18, 842, 29], [862, 8, 842, 29], [863, 10, 842, 29, "fileName"], [863, 18, 842, 29], [863, 20, 842, 29, "_jsxFileName"], [863, 32, 842, 29], [864, 10, 842, 29, "lineNumber"], [864, 20, 842, 29], [865, 10, 842, 29, "columnNumber"], [865, 22, 842, 29], [866, 8, 842, 29], [866, 15, 843, 14], [867, 6, 843, 15], [868, 8, 843, 15, "fileName"], [868, 16, 843, 15], [868, 18, 843, 15, "_jsxFileName"], [868, 30, 843, 15], [869, 8, 843, 15, "lineNumber"], [869, 18, 843, 15], [870, 8, 843, 15, "columnNumber"], [870, 20, 843, 15], [871, 6, 843, 15], [871, 13, 844, 12], [871, 14, 844, 13], [872, 4, 846, 2], [873, 4, 847, 2], [874, 4, 848, 2, "console"], [874, 11, 848, 9], [874, 12, 848, 10, "log"], [874, 15, 848, 13], [874, 16, 848, 14], [874, 55, 848, 53], [874, 56, 848, 54], [875, 4, 850, 2], [875, 24, 851, 4], [875, 28, 851, 4, "_jsxDevRuntime"], [875, 42, 851, 4], [875, 43, 851, 4, "jsxDEV"], [875, 49, 851, 4], [875, 51, 851, 5, "_View"], [875, 56, 851, 5], [875, 57, 851, 5, "default"], [875, 64, 851, 9], [876, 6, 851, 10, "style"], [876, 11, 851, 15], [876, 13, 851, 17, "styles"], [876, 19, 851, 23], [876, 20, 851, 24, "container"], [876, 29, 851, 34], [877, 6, 851, 34, "children"], [877, 14, 851, 34], [877, 30, 853, 6], [877, 34, 853, 6, "_jsxDevRuntime"], [877, 48, 853, 6], [877, 49, 853, 6, "jsxDEV"], [877, 55, 853, 6], [877, 57, 853, 7, "_View"], [877, 62, 853, 7], [877, 63, 853, 7, "default"], [877, 70, 853, 11], [878, 8, 853, 12, "style"], [878, 13, 853, 17], [878, 15, 853, 19, "styles"], [878, 21, 853, 25], [878, 22, 853, 26, "cameraContainer"], [878, 37, 853, 42], [879, 8, 853, 43, "id"], [879, 10, 853, 45], [879, 12, 853, 46], [879, 29, 853, 63], [880, 8, 853, 63, "children"], [880, 16, 853, 63], [880, 32, 854, 8], [880, 36, 854, 8, "_jsxDevRuntime"], [880, 50, 854, 8], [880, 51, 854, 8, "jsxDEV"], [880, 57, 854, 8], [880, 59, 854, 9, "_expoCamera"], [880, 70, 854, 9], [880, 71, 854, 9, "CameraView"], [880, 81, 854, 19], [881, 10, 855, 10, "ref"], [881, 13, 855, 13], [881, 15, 855, 15, "cameraRef"], [881, 24, 855, 25], [882, 10, 856, 10, "style"], [882, 15, 856, 15], [882, 17, 856, 17], [882, 18, 856, 18, "styles"], [882, 24, 856, 24], [882, 25, 856, 25, "camera"], [882, 31, 856, 31], [882, 33, 856, 33], [883, 12, 856, 35, "backgroundColor"], [883, 27, 856, 50], [883, 29, 856, 52], [884, 10, 856, 62], [884, 11, 856, 63], [884, 12, 856, 65], [885, 10, 857, 10, "facing"], [885, 16, 857, 16], [885, 18, 857, 17], [885, 24, 857, 23], [886, 10, 858, 10, "onLayout"], [886, 18, 858, 18], [886, 20, 858, 21, "e"], [886, 21, 858, 22], [886, 25, 858, 27], [887, 12, 859, 12, "console"], [887, 19, 859, 19], [887, 20, 859, 20, "log"], [887, 23, 859, 23], [887, 24, 859, 24], [887, 56, 859, 56], [887, 58, 859, 58, "e"], [887, 59, 859, 59], [887, 60, 859, 60, "nativeEvent"], [887, 71, 859, 71], [887, 72, 859, 72, "layout"], [887, 78, 859, 78], [887, 79, 859, 79], [888, 12, 860, 12, "setViewSize"], [888, 23, 860, 23], [888, 24, 860, 24], [889, 14, 860, 26, "width"], [889, 19, 860, 31], [889, 21, 860, 33, "e"], [889, 22, 860, 34], [889, 23, 860, 35, "nativeEvent"], [889, 34, 860, 46], [889, 35, 860, 47, "layout"], [889, 41, 860, 53], [889, 42, 860, 54, "width"], [889, 47, 860, 59], [890, 14, 860, 61, "height"], [890, 20, 860, 67], [890, 22, 860, 69, "e"], [890, 23, 860, 70], [890, 24, 860, 71, "nativeEvent"], [890, 35, 860, 82], [890, 36, 860, 83, "layout"], [890, 42, 860, 89], [890, 43, 860, 90, "height"], [891, 12, 860, 97], [891, 13, 860, 98], [891, 14, 860, 99], [892, 10, 861, 10], [892, 11, 861, 12], [893, 10, 862, 10, "onCameraReady"], [893, 23, 862, 23], [893, 25, 862, 25, "onCameraReady"], [893, 26, 862, 25], [893, 31, 862, 31], [894, 12, 863, 12, "console"], [894, 19, 863, 19], [894, 20, 863, 20, "log"], [894, 23, 863, 23], [894, 24, 863, 24], [894, 55, 863, 55], [894, 56, 863, 56], [895, 12, 864, 12, "setIsCameraReady"], [895, 28, 864, 28], [895, 29, 864, 29], [895, 33, 864, 33], [895, 34, 864, 34], [895, 35, 864, 35], [895, 36, 864, 36], [896, 10, 865, 10], [896, 11, 865, 12], [897, 10, 866, 10, "onMountError"], [897, 22, 866, 22], [897, 24, 866, 25, "error"], [897, 29, 866, 30], [897, 33, 866, 35], [898, 12, 867, 12, "console"], [898, 19, 867, 19], [898, 20, 867, 20, "error"], [898, 25, 867, 25], [898, 26, 867, 26], [898, 63, 867, 63], [898, 65, 867, 65, "error"], [898, 70, 867, 70], [898, 71, 867, 71], [899, 12, 868, 12, "setErrorMessage"], [899, 27, 868, 27], [899, 28, 868, 28], [899, 57, 868, 57], [899, 58, 868, 58], [900, 12, 869, 12, "setProcessingState"], [900, 30, 869, 30], [900, 31, 869, 31], [900, 38, 869, 38], [900, 39, 869, 39], [901, 10, 870, 10], [902, 8, 870, 12], [903, 10, 870, 12, "fileName"], [903, 18, 870, 12], [903, 20, 870, 12, "_jsxFileName"], [903, 32, 870, 12], [904, 10, 870, 12, "lineNumber"], [904, 20, 870, 12], [905, 10, 870, 12, "columnNumber"], [905, 22, 870, 12], [906, 8, 870, 12], [906, 15, 871, 9], [906, 16, 871, 10], [906, 18, 873, 9], [906, 19, 873, 10, "isCameraReady"], [906, 32, 873, 23], [906, 49, 874, 10], [906, 53, 874, 10, "_jsxDevRuntime"], [906, 67, 874, 10], [906, 68, 874, 10, "jsxDEV"], [906, 74, 874, 10], [906, 76, 874, 11, "_View"], [906, 81, 874, 11], [906, 82, 874, 11, "default"], [906, 89, 874, 15], [907, 10, 874, 16, "style"], [907, 15, 874, 21], [907, 17, 874, 23], [907, 18, 874, 24, "StyleSheet"], [907, 37, 874, 34], [907, 38, 874, 35, "absoluteFill"], [907, 50, 874, 47], [907, 52, 874, 49], [908, 12, 874, 51, "backgroundColor"], [908, 27, 874, 66], [908, 29, 874, 68], [908, 49, 874, 88], [909, 12, 874, 90, "justifyContent"], [909, 26, 874, 104], [909, 28, 874, 106], [909, 36, 874, 114], [910, 12, 874, 116, "alignItems"], [910, 22, 874, 126], [910, 24, 874, 128], [910, 32, 874, 136], [911, 12, 874, 138, "zIndex"], [911, 18, 874, 144], [911, 20, 874, 146], [912, 10, 874, 151], [912, 11, 874, 152], [912, 12, 874, 154], [913, 10, 874, 154, "children"], [913, 18, 874, 154], [913, 33, 875, 12], [913, 37, 875, 12, "_jsxDevRuntime"], [913, 51, 875, 12], [913, 52, 875, 12, "jsxDEV"], [913, 58, 875, 12], [913, 60, 875, 13, "_View"], [913, 65, 875, 13], [913, 66, 875, 13, "default"], [913, 73, 875, 17], [914, 12, 875, 18, "style"], [914, 17, 875, 23], [914, 19, 875, 25], [915, 14, 875, 27, "backgroundColor"], [915, 29, 875, 42], [915, 31, 875, 44], [915, 51, 875, 64], [916, 14, 875, 66, "padding"], [916, 21, 875, 73], [916, 23, 875, 75], [916, 25, 875, 77], [917, 14, 875, 79, "borderRadius"], [917, 26, 875, 91], [917, 28, 875, 93], [917, 30, 875, 95], [918, 14, 875, 97, "alignItems"], [918, 24, 875, 107], [918, 26, 875, 109], [919, 12, 875, 118], [919, 13, 875, 120], [920, 12, 875, 120, "children"], [920, 20, 875, 120], [920, 36, 876, 14], [920, 40, 876, 14, "_jsxDevRuntime"], [920, 54, 876, 14], [920, 55, 876, 14, "jsxDEV"], [920, 61, 876, 14], [920, 63, 876, 15, "_ActivityIndicator"], [920, 81, 876, 15], [920, 82, 876, 15, "default"], [920, 89, 876, 32], [921, 14, 876, 33, "size"], [921, 18, 876, 37], [921, 20, 876, 38], [921, 27, 876, 45], [922, 14, 876, 46, "color"], [922, 19, 876, 51], [922, 21, 876, 52], [922, 30, 876, 61], [923, 14, 876, 62, "style"], [923, 19, 876, 67], [923, 21, 876, 69], [924, 16, 876, 71, "marginBottom"], [924, 28, 876, 83], [924, 30, 876, 85], [925, 14, 876, 88], [926, 12, 876, 90], [927, 14, 876, 90, "fileName"], [927, 22, 876, 90], [927, 24, 876, 90, "_jsxFileName"], [927, 36, 876, 90], [928, 14, 876, 90, "lineNumber"], [928, 24, 876, 90], [929, 14, 876, 90, "columnNumber"], [929, 26, 876, 90], [930, 12, 876, 90], [930, 19, 876, 92], [930, 20, 876, 93], [930, 35, 877, 14], [930, 39, 877, 14, "_jsxDevRuntime"], [930, 53, 877, 14], [930, 54, 877, 14, "jsxDEV"], [930, 60, 877, 14], [930, 62, 877, 15, "_Text"], [930, 67, 877, 15], [930, 68, 877, 15, "default"], [930, 75, 877, 19], [931, 14, 877, 20, "style"], [931, 19, 877, 25], [931, 21, 877, 27], [932, 16, 877, 29, "color"], [932, 21, 877, 34], [932, 23, 877, 36], [932, 29, 877, 42], [933, 16, 877, 44, "fontSize"], [933, 24, 877, 52], [933, 26, 877, 54], [933, 28, 877, 56], [934, 16, 877, 58, "fontWeight"], [934, 26, 877, 68], [934, 28, 877, 70], [935, 14, 877, 76], [935, 15, 877, 78], [936, 14, 877, 78, "children"], [936, 22, 877, 78], [936, 24, 877, 79], [937, 12, 877, 101], [938, 14, 877, 101, "fileName"], [938, 22, 877, 101], [938, 24, 877, 101, "_jsxFileName"], [938, 36, 877, 101], [939, 14, 877, 101, "lineNumber"], [939, 24, 877, 101], [940, 14, 877, 101, "columnNumber"], [940, 26, 877, 101], [941, 12, 877, 101], [941, 19, 877, 107], [941, 20, 877, 108], [941, 35, 878, 14], [941, 39, 878, 14, "_jsxDevRuntime"], [941, 53, 878, 14], [941, 54, 878, 14, "jsxDEV"], [941, 60, 878, 14], [941, 62, 878, 15, "_Text"], [941, 67, 878, 15], [941, 68, 878, 15, "default"], [941, 75, 878, 19], [942, 14, 878, 20, "style"], [942, 19, 878, 25], [942, 21, 878, 27], [943, 16, 878, 29, "color"], [943, 21, 878, 34], [943, 23, 878, 36], [943, 32, 878, 45], [944, 16, 878, 47, "fontSize"], [944, 24, 878, 55], [944, 26, 878, 57], [944, 28, 878, 59], [945, 16, 878, 61, "marginTop"], [945, 25, 878, 70], [945, 27, 878, 72], [946, 14, 878, 74], [946, 15, 878, 76], [947, 14, 878, 76, "children"], [947, 22, 878, 76], [947, 24, 878, 77], [948, 12, 878, 88], [949, 14, 878, 88, "fileName"], [949, 22, 878, 88], [949, 24, 878, 88, "_jsxFileName"], [949, 36, 878, 88], [950, 14, 878, 88, "lineNumber"], [950, 24, 878, 88], [951, 14, 878, 88, "columnNumber"], [951, 26, 878, 88], [952, 12, 878, 88], [952, 19, 878, 94], [952, 20, 878, 95], [953, 10, 878, 95], [954, 12, 878, 95, "fileName"], [954, 20, 878, 95], [954, 22, 878, 95, "_jsxFileName"], [954, 34, 878, 95], [955, 12, 878, 95, "lineNumber"], [955, 22, 878, 95], [956, 12, 878, 95, "columnNumber"], [956, 24, 878, 95], [957, 10, 878, 95], [957, 17, 879, 18], [958, 8, 879, 19], [959, 10, 879, 19, "fileName"], [959, 18, 879, 19], [959, 20, 879, 19, "_jsxFileName"], [959, 32, 879, 19], [960, 10, 879, 19, "lineNumber"], [960, 20, 879, 19], [961, 10, 879, 19, "columnNumber"], [961, 22, 879, 19], [962, 8, 879, 19], [962, 15, 880, 16], [962, 16, 881, 9], [962, 18, 884, 9, "isCameraReady"], [962, 31, 884, 22], [962, 35, 884, 26, "previewBlurEnabled"], [962, 53, 884, 44], [962, 57, 884, 48, "viewSize"], [962, 65, 884, 56], [962, 66, 884, 57, "width"], [962, 71, 884, 62], [962, 74, 884, 65], [962, 75, 884, 66], [962, 92, 885, 10], [962, 96, 885, 10, "_jsxDevRuntime"], [962, 110, 885, 10], [962, 111, 885, 10, "jsxDEV"], [962, 117, 885, 10], [962, 119, 885, 10, "_jsxDevRuntime"], [962, 133, 885, 10], [962, 134, 885, 10, "Fragment"], [962, 142, 885, 10], [963, 10, 885, 10, "children"], [963, 18, 885, 10], [963, 34, 887, 12], [963, 38, 887, 12, "_jsxDevRuntime"], [963, 52, 887, 12], [963, 53, 887, 12, "jsxDEV"], [963, 59, 887, 12], [963, 61, 887, 13, "_LiveFaceCanvas"], [963, 76, 887, 13], [963, 77, 887, 13, "default"], [963, 84, 887, 27], [964, 12, 887, 28, "containerId"], [964, 23, 887, 39], [964, 25, 887, 40], [964, 42, 887, 57], [965, 12, 887, 58, "width"], [965, 17, 887, 63], [965, 19, 887, 65, "viewSize"], [965, 27, 887, 73], [965, 28, 887, 74, "width"], [965, 33, 887, 80], [966, 12, 887, 81, "height"], [966, 18, 887, 87], [966, 20, 887, 89, "viewSize"], [966, 28, 887, 97], [966, 29, 887, 98, "height"], [967, 10, 887, 105], [968, 12, 887, 105, "fileName"], [968, 20, 887, 105], [968, 22, 887, 105, "_jsxFileName"], [968, 34, 887, 105], [969, 12, 887, 105, "lineNumber"], [969, 22, 887, 105], [970, 12, 887, 105, "columnNumber"], [970, 24, 887, 105], [971, 10, 887, 105], [971, 17, 887, 107], [971, 18, 887, 108], [971, 33, 888, 12], [971, 37, 888, 12, "_jsxDevRuntime"], [971, 51, 888, 12], [971, 52, 888, 12, "jsxDEV"], [971, 58, 888, 12], [971, 60, 888, 13, "_View"], [971, 65, 888, 13], [971, 66, 888, 13, "default"], [971, 73, 888, 17], [972, 12, 888, 18, "style"], [972, 17, 888, 23], [972, 19, 888, 25], [972, 20, 888, 26, "StyleSheet"], [972, 39, 888, 36], [972, 40, 888, 37, "absoluteFill"], [972, 52, 888, 49], [972, 54, 888, 51], [973, 14, 888, 53, "pointerEvents"], [973, 27, 888, 66], [973, 29, 888, 68], [974, 12, 888, 75], [974, 13, 888, 76], [974, 14, 888, 78], [975, 12, 888, 78, "children"], [975, 20, 888, 78], [975, 36, 890, 12], [975, 40, 890, 12, "_jsxDevRuntime"], [975, 54, 890, 12], [975, 55, 890, 12, "jsxDEV"], [975, 61, 890, 12], [975, 63, 890, 13, "_expoBlur"], [975, 72, 890, 13], [975, 73, 890, 13, "BlurView"], [975, 81, 890, 21], [976, 14, 890, 22, "intensity"], [976, 23, 890, 31], [976, 25, 890, 33], [976, 27, 890, 36], [977, 14, 890, 37, "tint"], [977, 18, 890, 41], [977, 20, 890, 42], [977, 26, 890, 48], [978, 14, 890, 49, "style"], [978, 19, 890, 54], [978, 21, 890, 56], [978, 22, 890, 57, "styles"], [978, 28, 890, 63], [978, 29, 890, 64, "blurZone"], [978, 37, 890, 72], [978, 39, 890, 74], [979, 16, 891, 14, "left"], [979, 20, 891, 18], [979, 22, 891, 20], [979, 23, 891, 21], [980, 16, 892, 14, "top"], [980, 19, 892, 17], [980, 21, 892, 19, "viewSize"], [980, 29, 892, 27], [980, 30, 892, 28, "height"], [980, 36, 892, 34], [980, 39, 892, 37], [980, 42, 892, 40], [981, 16, 893, 14, "width"], [981, 21, 893, 19], [981, 23, 893, 21, "viewSize"], [981, 31, 893, 29], [981, 32, 893, 30, "width"], [981, 37, 893, 35], [982, 16, 894, 14, "height"], [982, 22, 894, 20], [982, 24, 894, 22, "viewSize"], [982, 32, 894, 30], [982, 33, 894, 31, "height"], [982, 39, 894, 37], [982, 42, 894, 40], [982, 46, 894, 44], [983, 16, 895, 14, "borderRadius"], [983, 28, 895, 26], [983, 30, 895, 28], [984, 14, 896, 12], [984, 15, 896, 13], [985, 12, 896, 15], [986, 14, 896, 15, "fileName"], [986, 22, 896, 15], [986, 24, 896, 15, "_jsxFileName"], [986, 36, 896, 15], [987, 14, 896, 15, "lineNumber"], [987, 24, 896, 15], [988, 14, 896, 15, "columnNumber"], [988, 26, 896, 15], [989, 12, 896, 15], [989, 19, 896, 17], [989, 20, 896, 18], [989, 35, 898, 12], [989, 39, 898, 12, "_jsxDevRuntime"], [989, 53, 898, 12], [989, 54, 898, 12, "jsxDEV"], [989, 60, 898, 12], [989, 62, 898, 13, "_expoBlur"], [989, 71, 898, 13], [989, 72, 898, 13, "BlurView"], [989, 80, 898, 21], [990, 14, 898, 22, "intensity"], [990, 23, 898, 31], [990, 25, 898, 33], [990, 27, 898, 36], [991, 14, 898, 37, "tint"], [991, 18, 898, 41], [991, 20, 898, 42], [991, 26, 898, 48], [992, 14, 898, 49, "style"], [992, 19, 898, 54], [992, 21, 898, 56], [992, 22, 898, 57, "styles"], [992, 28, 898, 63], [992, 29, 898, 64, "blurZone"], [992, 37, 898, 72], [992, 39, 898, 74], [993, 16, 899, 14, "left"], [993, 20, 899, 18], [993, 22, 899, 20], [993, 23, 899, 21], [994, 16, 900, 14, "top"], [994, 19, 900, 17], [994, 21, 900, 19], [994, 22, 900, 20], [995, 16, 901, 14, "width"], [995, 21, 901, 19], [995, 23, 901, 21, "viewSize"], [995, 31, 901, 29], [995, 32, 901, 30, "width"], [995, 37, 901, 35], [996, 16, 902, 14, "height"], [996, 22, 902, 20], [996, 24, 902, 22, "viewSize"], [996, 32, 902, 30], [996, 33, 902, 31, "height"], [996, 39, 902, 37], [996, 42, 902, 40], [996, 45, 902, 43], [997, 16, 903, 14, "borderRadius"], [997, 28, 903, 26], [997, 30, 903, 28], [998, 14, 904, 12], [998, 15, 904, 13], [999, 12, 904, 15], [1000, 14, 904, 15, "fileName"], [1000, 22, 904, 15], [1000, 24, 904, 15, "_jsxFileName"], [1000, 36, 904, 15], [1001, 14, 904, 15, "lineNumber"], [1001, 24, 904, 15], [1002, 14, 904, 15, "columnNumber"], [1002, 26, 904, 15], [1003, 12, 904, 15], [1003, 19, 904, 17], [1003, 20, 904, 18], [1003, 35, 906, 12], [1003, 39, 906, 12, "_jsxDevRuntime"], [1003, 53, 906, 12], [1003, 54, 906, 12, "jsxDEV"], [1003, 60, 906, 12], [1003, 62, 906, 13, "_expoBlur"], [1003, 71, 906, 13], [1003, 72, 906, 13, "BlurView"], [1003, 80, 906, 21], [1004, 14, 906, 22, "intensity"], [1004, 23, 906, 31], [1004, 25, 906, 33], [1004, 27, 906, 36], [1005, 14, 906, 37, "tint"], [1005, 18, 906, 41], [1005, 20, 906, 42], [1005, 26, 906, 48], [1006, 14, 906, 49, "style"], [1006, 19, 906, 54], [1006, 21, 906, 56], [1006, 22, 906, 57, "styles"], [1006, 28, 906, 63], [1006, 29, 906, 64, "blurZone"], [1006, 37, 906, 72], [1006, 39, 906, 74], [1007, 16, 907, 14, "left"], [1007, 20, 907, 18], [1007, 22, 907, 20, "viewSize"], [1007, 30, 907, 28], [1007, 31, 907, 29, "width"], [1007, 36, 907, 34], [1007, 39, 907, 37], [1007, 42, 907, 40], [1007, 45, 907, 44, "viewSize"], [1007, 53, 907, 52], [1007, 54, 907, 53, "width"], [1007, 59, 907, 58], [1007, 62, 907, 61], [1007, 66, 907, 66], [1008, 16, 908, 14, "top"], [1008, 19, 908, 17], [1008, 21, 908, 19, "viewSize"], [1008, 29, 908, 27], [1008, 30, 908, 28, "height"], [1008, 36, 908, 34], [1008, 39, 908, 37], [1008, 43, 908, 41], [1008, 46, 908, 45, "viewSize"], [1008, 54, 908, 53], [1008, 55, 908, 54, "width"], [1008, 60, 908, 59], [1008, 63, 908, 62], [1008, 67, 908, 67], [1009, 16, 909, 14, "width"], [1009, 21, 909, 19], [1009, 23, 909, 21, "viewSize"], [1009, 31, 909, 29], [1009, 32, 909, 30, "width"], [1009, 37, 909, 35], [1009, 40, 909, 38], [1009, 43, 909, 41], [1010, 16, 910, 14, "height"], [1010, 22, 910, 20], [1010, 24, 910, 22, "viewSize"], [1010, 32, 910, 30], [1010, 33, 910, 31, "width"], [1010, 38, 910, 36], [1010, 41, 910, 39], [1010, 44, 910, 42], [1011, 16, 911, 14, "borderRadius"], [1011, 28, 911, 26], [1011, 30, 911, 29, "viewSize"], [1011, 38, 911, 37], [1011, 39, 911, 38, "width"], [1011, 44, 911, 43], [1011, 47, 911, 46], [1011, 50, 911, 49], [1011, 53, 911, 53], [1012, 14, 912, 12], [1012, 15, 912, 13], [1013, 12, 912, 15], [1014, 14, 912, 15, "fileName"], [1014, 22, 912, 15], [1014, 24, 912, 15, "_jsxFileName"], [1014, 36, 912, 15], [1015, 14, 912, 15, "lineNumber"], [1015, 24, 912, 15], [1016, 14, 912, 15, "columnNumber"], [1016, 26, 912, 15], [1017, 12, 912, 15], [1017, 19, 912, 17], [1017, 20, 912, 18], [1017, 35, 913, 12], [1017, 39, 913, 12, "_jsxDevRuntime"], [1017, 53, 913, 12], [1017, 54, 913, 12, "jsxDEV"], [1017, 60, 913, 12], [1017, 62, 913, 13, "_expoBlur"], [1017, 71, 913, 13], [1017, 72, 913, 13, "BlurView"], [1017, 80, 913, 21], [1018, 14, 913, 22, "intensity"], [1018, 23, 913, 31], [1018, 25, 913, 33], [1018, 27, 913, 36], [1019, 14, 913, 37, "tint"], [1019, 18, 913, 41], [1019, 20, 913, 42], [1019, 26, 913, 48], [1020, 14, 913, 49, "style"], [1020, 19, 913, 54], [1020, 21, 913, 56], [1020, 22, 913, 57, "styles"], [1020, 28, 913, 63], [1020, 29, 913, 64, "blurZone"], [1020, 37, 913, 72], [1020, 39, 913, 74], [1021, 16, 914, 14, "left"], [1021, 20, 914, 18], [1021, 22, 914, 20, "viewSize"], [1021, 30, 914, 28], [1021, 31, 914, 29, "width"], [1021, 36, 914, 34], [1021, 39, 914, 37], [1021, 42, 914, 40], [1021, 45, 914, 44, "viewSize"], [1021, 53, 914, 52], [1021, 54, 914, 53, "width"], [1021, 59, 914, 58], [1021, 62, 914, 61], [1021, 66, 914, 66], [1022, 16, 915, 14, "top"], [1022, 19, 915, 17], [1022, 21, 915, 19, "viewSize"], [1022, 29, 915, 27], [1022, 30, 915, 28, "height"], [1022, 36, 915, 34], [1022, 39, 915, 37], [1022, 42, 915, 40], [1022, 45, 915, 44, "viewSize"], [1022, 53, 915, 52], [1022, 54, 915, 53, "width"], [1022, 59, 915, 58], [1022, 62, 915, 61], [1022, 66, 915, 66], [1023, 16, 916, 14, "width"], [1023, 21, 916, 19], [1023, 23, 916, 21, "viewSize"], [1023, 31, 916, 29], [1023, 32, 916, 30, "width"], [1023, 37, 916, 35], [1023, 40, 916, 38], [1023, 43, 916, 41], [1024, 16, 917, 14, "height"], [1024, 22, 917, 20], [1024, 24, 917, 22, "viewSize"], [1024, 32, 917, 30], [1024, 33, 917, 31, "width"], [1024, 38, 917, 36], [1024, 41, 917, 39], [1024, 44, 917, 42], [1025, 16, 918, 14, "borderRadius"], [1025, 28, 918, 26], [1025, 30, 918, 29, "viewSize"], [1025, 38, 918, 37], [1025, 39, 918, 38, "width"], [1025, 44, 918, 43], [1025, 47, 918, 46], [1025, 50, 918, 49], [1025, 53, 918, 53], [1026, 14, 919, 12], [1026, 15, 919, 13], [1027, 12, 919, 15], [1028, 14, 919, 15, "fileName"], [1028, 22, 919, 15], [1028, 24, 919, 15, "_jsxFileName"], [1028, 36, 919, 15], [1029, 14, 919, 15, "lineNumber"], [1029, 24, 919, 15], [1030, 14, 919, 15, "columnNumber"], [1030, 26, 919, 15], [1031, 12, 919, 15], [1031, 19, 919, 17], [1031, 20, 919, 18], [1031, 35, 920, 12], [1031, 39, 920, 12, "_jsxDevRuntime"], [1031, 53, 920, 12], [1031, 54, 920, 12, "jsxDEV"], [1031, 60, 920, 12], [1031, 62, 920, 13, "_expoBlur"], [1031, 71, 920, 13], [1031, 72, 920, 13, "BlurView"], [1031, 80, 920, 21], [1032, 14, 920, 22, "intensity"], [1032, 23, 920, 31], [1032, 25, 920, 33], [1032, 27, 920, 36], [1033, 14, 920, 37, "tint"], [1033, 18, 920, 41], [1033, 20, 920, 42], [1033, 26, 920, 48], [1034, 14, 920, 49, "style"], [1034, 19, 920, 54], [1034, 21, 920, 56], [1034, 22, 920, 57, "styles"], [1034, 28, 920, 63], [1034, 29, 920, 64, "blurZone"], [1034, 37, 920, 72], [1034, 39, 920, 74], [1035, 16, 921, 14, "left"], [1035, 20, 921, 18], [1035, 22, 921, 20, "viewSize"], [1035, 30, 921, 28], [1035, 31, 921, 29, "width"], [1035, 36, 921, 34], [1035, 39, 921, 37], [1035, 42, 921, 40], [1035, 45, 921, 44, "viewSize"], [1035, 53, 921, 52], [1035, 54, 921, 53, "width"], [1035, 59, 921, 58], [1035, 62, 921, 61], [1035, 66, 921, 66], [1036, 16, 922, 14, "top"], [1036, 19, 922, 17], [1036, 21, 922, 19, "viewSize"], [1036, 29, 922, 27], [1036, 30, 922, 28, "height"], [1036, 36, 922, 34], [1036, 39, 922, 37], [1036, 42, 922, 40], [1036, 45, 922, 44, "viewSize"], [1036, 53, 922, 52], [1036, 54, 922, 53, "width"], [1036, 59, 922, 58], [1036, 62, 922, 61], [1036, 66, 922, 66], [1037, 16, 923, 14, "width"], [1037, 21, 923, 19], [1037, 23, 923, 21, "viewSize"], [1037, 31, 923, 29], [1037, 32, 923, 30, "width"], [1037, 37, 923, 35], [1037, 40, 923, 38], [1037, 43, 923, 41], [1038, 16, 924, 14, "height"], [1038, 22, 924, 20], [1038, 24, 924, 22, "viewSize"], [1038, 32, 924, 30], [1038, 33, 924, 31, "width"], [1038, 38, 924, 36], [1038, 41, 924, 39], [1038, 44, 924, 42], [1039, 16, 925, 14, "borderRadius"], [1039, 28, 925, 26], [1039, 30, 925, 29, "viewSize"], [1039, 38, 925, 37], [1039, 39, 925, 38, "width"], [1039, 44, 925, 43], [1039, 47, 925, 46], [1039, 50, 925, 49], [1039, 53, 925, 53], [1040, 14, 926, 12], [1040, 15, 926, 13], [1041, 12, 926, 15], [1042, 14, 926, 15, "fileName"], [1042, 22, 926, 15], [1042, 24, 926, 15, "_jsxFileName"], [1042, 36, 926, 15], [1043, 14, 926, 15, "lineNumber"], [1043, 24, 926, 15], [1044, 14, 926, 15, "columnNumber"], [1044, 26, 926, 15], [1045, 12, 926, 15], [1045, 19, 926, 17], [1045, 20, 926, 18], [1045, 22, 928, 13, "__DEV__"], [1045, 29, 928, 20], [1045, 46, 929, 14], [1045, 50, 929, 14, "_jsxDevRuntime"], [1045, 64, 929, 14], [1045, 65, 929, 14, "jsxDEV"], [1045, 71, 929, 14], [1045, 73, 929, 15, "_View"], [1045, 78, 929, 15], [1045, 79, 929, 15, "default"], [1045, 86, 929, 19], [1046, 14, 929, 20, "style"], [1046, 19, 929, 25], [1046, 21, 929, 27, "styles"], [1046, 27, 929, 33], [1046, 28, 929, 34, "previewChip"], [1046, 39, 929, 46], [1047, 14, 929, 46, "children"], [1047, 22, 929, 46], [1047, 37, 930, 16], [1047, 41, 930, 16, "_jsxDevRuntime"], [1047, 55, 930, 16], [1047, 56, 930, 16, "jsxDEV"], [1047, 62, 930, 16], [1047, 64, 930, 17, "_Text"], [1047, 69, 930, 17], [1047, 70, 930, 17, "default"], [1047, 77, 930, 21], [1048, 16, 930, 22, "style"], [1048, 21, 930, 27], [1048, 23, 930, 29, "styles"], [1048, 29, 930, 35], [1048, 30, 930, 36, "previewChipText"], [1048, 45, 930, 52], [1049, 16, 930, 52, "children"], [1049, 24, 930, 52], [1049, 26, 930, 53], [1050, 14, 930, 73], [1051, 16, 930, 73, "fileName"], [1051, 24, 930, 73], [1051, 26, 930, 73, "_jsxFileName"], [1051, 38, 930, 73], [1052, 16, 930, 73, "lineNumber"], [1052, 26, 930, 73], [1053, 16, 930, 73, "columnNumber"], [1053, 28, 930, 73], [1054, 14, 930, 73], [1054, 21, 930, 79], [1055, 12, 930, 80], [1056, 14, 930, 80, "fileName"], [1056, 22, 930, 80], [1056, 24, 930, 80, "_jsxFileName"], [1056, 36, 930, 80], [1057, 14, 930, 80, "lineNumber"], [1057, 24, 930, 80], [1058, 14, 930, 80, "columnNumber"], [1058, 26, 930, 80], [1059, 12, 930, 80], [1059, 19, 931, 20], [1059, 20, 932, 13], [1060, 10, 932, 13], [1061, 12, 932, 13, "fileName"], [1061, 20, 932, 13], [1061, 22, 932, 13, "_jsxFileName"], [1061, 34, 932, 13], [1062, 12, 932, 13, "lineNumber"], [1062, 22, 932, 13], [1063, 12, 932, 13, "columnNumber"], [1063, 24, 932, 13], [1064, 10, 932, 13], [1064, 17, 933, 18], [1064, 18, 933, 19], [1065, 8, 933, 19], [1065, 23, 934, 12], [1065, 24, 935, 9], [1065, 26, 937, 9, "isCameraReady"], [1065, 39, 937, 22], [1065, 56, 938, 10], [1065, 60, 938, 10, "_jsxDevRuntime"], [1065, 74, 938, 10], [1065, 75, 938, 10, "jsxDEV"], [1065, 81, 938, 10], [1065, 83, 938, 10, "_jsxDevRuntime"], [1065, 97, 938, 10], [1065, 98, 938, 10, "Fragment"], [1065, 106, 938, 10], [1066, 10, 938, 10, "children"], [1066, 18, 938, 10], [1066, 34, 940, 12], [1066, 38, 940, 12, "_jsxDevRuntime"], [1066, 52, 940, 12], [1066, 53, 940, 12, "jsxDEV"], [1066, 59, 940, 12], [1066, 61, 940, 13, "_View"], [1066, 66, 940, 13], [1066, 67, 940, 13, "default"], [1066, 74, 940, 17], [1067, 12, 940, 18, "style"], [1067, 17, 940, 23], [1067, 19, 940, 25, "styles"], [1067, 25, 940, 31], [1067, 26, 940, 32, "headerOverlay"], [1067, 39, 940, 46], [1068, 12, 940, 46, "children"], [1068, 20, 940, 46], [1068, 35, 941, 14], [1068, 39, 941, 14, "_jsxDevRuntime"], [1068, 53, 941, 14], [1068, 54, 941, 14, "jsxDEV"], [1068, 60, 941, 14], [1068, 62, 941, 15, "_View"], [1068, 67, 941, 15], [1068, 68, 941, 15, "default"], [1068, 75, 941, 19], [1069, 14, 941, 20, "style"], [1069, 19, 941, 25], [1069, 21, 941, 27, "styles"], [1069, 27, 941, 33], [1069, 28, 941, 34, "headerContent"], [1069, 41, 941, 48], [1070, 14, 941, 48, "children"], [1070, 22, 941, 48], [1070, 38, 942, 16], [1070, 42, 942, 16, "_jsxDevRuntime"], [1070, 56, 942, 16], [1070, 57, 942, 16, "jsxDEV"], [1070, 63, 942, 16], [1070, 65, 942, 17, "_View"], [1070, 70, 942, 17], [1070, 71, 942, 17, "default"], [1070, 78, 942, 21], [1071, 16, 942, 22, "style"], [1071, 21, 942, 27], [1071, 23, 942, 29, "styles"], [1071, 29, 942, 35], [1071, 30, 942, 36, "headerLeft"], [1071, 40, 942, 47], [1072, 16, 942, 47, "children"], [1072, 24, 942, 47], [1072, 40, 943, 18], [1072, 44, 943, 18, "_jsxDevRuntime"], [1072, 58, 943, 18], [1072, 59, 943, 18, "jsxDEV"], [1072, 65, 943, 18], [1072, 67, 943, 19, "_Text"], [1072, 72, 943, 19], [1072, 73, 943, 19, "default"], [1072, 80, 943, 23], [1073, 18, 943, 24, "style"], [1073, 23, 943, 29], [1073, 25, 943, 31, "styles"], [1073, 31, 943, 37], [1073, 32, 943, 38, "headerTitle"], [1073, 43, 943, 50], [1074, 18, 943, 50, "children"], [1074, 26, 943, 50], [1074, 28, 943, 51], [1075, 16, 943, 62], [1076, 18, 943, 62, "fileName"], [1076, 26, 943, 62], [1076, 28, 943, 62, "_jsxFileName"], [1076, 40, 943, 62], [1077, 18, 943, 62, "lineNumber"], [1077, 28, 943, 62], [1078, 18, 943, 62, "columnNumber"], [1078, 30, 943, 62], [1079, 16, 943, 62], [1079, 23, 943, 68], [1079, 24, 943, 69], [1079, 39, 944, 18], [1079, 43, 944, 18, "_jsxDevRuntime"], [1079, 57, 944, 18], [1079, 58, 944, 18, "jsxDEV"], [1079, 64, 944, 18], [1079, 66, 944, 19, "_View"], [1079, 71, 944, 19], [1079, 72, 944, 19, "default"], [1079, 79, 944, 23], [1080, 18, 944, 24, "style"], [1080, 23, 944, 29], [1080, 25, 944, 31, "styles"], [1080, 31, 944, 37], [1080, 32, 944, 38, "subtitleRow"], [1080, 43, 944, 50], [1081, 18, 944, 50, "children"], [1081, 26, 944, 50], [1081, 42, 945, 20], [1081, 46, 945, 20, "_jsxDevRuntime"], [1081, 60, 945, 20], [1081, 61, 945, 20, "jsxDEV"], [1081, 67, 945, 20], [1081, 69, 945, 21, "_Text"], [1081, 74, 945, 21], [1081, 75, 945, 21, "default"], [1081, 82, 945, 25], [1082, 20, 945, 26, "style"], [1082, 25, 945, 31], [1082, 27, 945, 33, "styles"], [1082, 33, 945, 39], [1082, 34, 945, 40, "webIcon"], [1082, 41, 945, 48], [1083, 20, 945, 48, "children"], [1083, 28, 945, 48], [1083, 30, 945, 49], [1084, 18, 945, 51], [1085, 20, 945, 51, "fileName"], [1085, 28, 945, 51], [1085, 30, 945, 51, "_jsxFileName"], [1085, 42, 945, 51], [1086, 20, 945, 51, "lineNumber"], [1086, 30, 945, 51], [1087, 20, 945, 51, "columnNumber"], [1087, 32, 945, 51], [1088, 18, 945, 51], [1088, 25, 945, 57], [1088, 26, 945, 58], [1088, 41, 946, 20], [1088, 45, 946, 20, "_jsxDevRuntime"], [1088, 59, 946, 20], [1088, 60, 946, 20, "jsxDEV"], [1088, 66, 946, 20], [1088, 68, 946, 21, "_Text"], [1088, 73, 946, 21], [1088, 74, 946, 21, "default"], [1088, 81, 946, 25], [1089, 20, 946, 26, "style"], [1089, 25, 946, 31], [1089, 27, 946, 33, "styles"], [1089, 33, 946, 39], [1089, 34, 946, 40, "headerSubtitle"], [1089, 48, 946, 55], [1090, 20, 946, 55, "children"], [1090, 28, 946, 55], [1090, 30, 946, 56], [1091, 18, 946, 71], [1092, 20, 946, 71, "fileName"], [1092, 28, 946, 71], [1092, 30, 946, 71, "_jsxFileName"], [1092, 42, 946, 71], [1093, 20, 946, 71, "lineNumber"], [1093, 30, 946, 71], [1094, 20, 946, 71, "columnNumber"], [1094, 32, 946, 71], [1095, 18, 946, 71], [1095, 25, 946, 77], [1095, 26, 946, 78], [1096, 16, 946, 78], [1097, 18, 946, 78, "fileName"], [1097, 26, 946, 78], [1097, 28, 946, 78, "_jsxFileName"], [1097, 40, 946, 78], [1098, 18, 946, 78, "lineNumber"], [1098, 28, 946, 78], [1099, 18, 946, 78, "columnNumber"], [1099, 30, 946, 78], [1100, 16, 946, 78], [1100, 23, 947, 24], [1100, 24, 947, 25], [1100, 26, 948, 19, "challengeCode"], [1100, 39, 948, 32], [1100, 56, 949, 20], [1100, 60, 949, 20, "_jsxDevRuntime"], [1100, 74, 949, 20], [1100, 75, 949, 20, "jsxDEV"], [1100, 81, 949, 20], [1100, 83, 949, 21, "_View"], [1100, 88, 949, 21], [1100, 89, 949, 21, "default"], [1100, 96, 949, 25], [1101, 18, 949, 26, "style"], [1101, 23, 949, 31], [1101, 25, 949, 33, "styles"], [1101, 31, 949, 39], [1101, 32, 949, 40, "challengeRow"], [1101, 44, 949, 53], [1102, 18, 949, 53, "children"], [1102, 26, 949, 53], [1102, 42, 950, 22], [1102, 46, 950, 22, "_jsxDevRuntime"], [1102, 60, 950, 22], [1102, 61, 950, 22, "jsxDEV"], [1102, 67, 950, 22], [1102, 69, 950, 23, "_lucideReactNative"], [1102, 87, 950, 23], [1102, 88, 950, 23, "Shield"], [1102, 94, 950, 29], [1103, 20, 950, 30, "size"], [1103, 24, 950, 34], [1103, 26, 950, 36], [1103, 28, 950, 39], [1104, 20, 950, 40, "color"], [1104, 25, 950, 45], [1104, 27, 950, 46], [1105, 18, 950, 52], [1106, 20, 950, 52, "fileName"], [1106, 28, 950, 52], [1106, 30, 950, 52, "_jsxFileName"], [1106, 42, 950, 52], [1107, 20, 950, 52, "lineNumber"], [1107, 30, 950, 52], [1108, 20, 950, 52, "columnNumber"], [1108, 32, 950, 52], [1109, 18, 950, 52], [1109, 25, 950, 54], [1109, 26, 950, 55], [1109, 41, 951, 22], [1109, 45, 951, 22, "_jsxDevRuntime"], [1109, 59, 951, 22], [1109, 60, 951, 22, "jsxDEV"], [1109, 66, 951, 22], [1109, 68, 951, 23, "_Text"], [1109, 73, 951, 23], [1109, 74, 951, 23, "default"], [1109, 81, 951, 27], [1110, 20, 951, 28, "style"], [1110, 25, 951, 33], [1110, 27, 951, 35, "styles"], [1110, 33, 951, 41], [1110, 34, 951, 42, "challengeCode"], [1110, 47, 951, 56], [1111, 20, 951, 56, "children"], [1111, 28, 951, 56], [1111, 30, 951, 58, "challengeCode"], [1112, 18, 951, 71], [1113, 20, 951, 71, "fileName"], [1113, 28, 951, 71], [1113, 30, 951, 71, "_jsxFileName"], [1113, 42, 951, 71], [1114, 20, 951, 71, "lineNumber"], [1114, 30, 951, 71], [1115, 20, 951, 71, "columnNumber"], [1115, 32, 951, 71], [1116, 18, 951, 71], [1116, 25, 951, 78], [1116, 26, 951, 79], [1117, 16, 951, 79], [1118, 18, 951, 79, "fileName"], [1118, 26, 951, 79], [1118, 28, 951, 79, "_jsxFileName"], [1118, 40, 951, 79], [1119, 18, 951, 79, "lineNumber"], [1119, 28, 951, 79], [1120, 18, 951, 79, "columnNumber"], [1120, 30, 951, 79], [1121, 16, 951, 79], [1121, 23, 952, 26], [1121, 24, 953, 19], [1122, 14, 953, 19], [1123, 16, 953, 19, "fileName"], [1123, 24, 953, 19], [1123, 26, 953, 19, "_jsxFileName"], [1123, 38, 953, 19], [1124, 16, 953, 19, "lineNumber"], [1124, 26, 953, 19], [1125, 16, 953, 19, "columnNumber"], [1125, 28, 953, 19], [1126, 14, 953, 19], [1126, 21, 954, 22], [1126, 22, 954, 23], [1126, 37, 955, 16], [1126, 41, 955, 16, "_jsxDevRuntime"], [1126, 55, 955, 16], [1126, 56, 955, 16, "jsxDEV"], [1126, 62, 955, 16], [1126, 64, 955, 17, "_TouchableOpacity"], [1126, 81, 955, 17], [1126, 82, 955, 17, "default"], [1126, 89, 955, 33], [1127, 16, 955, 34, "onPress"], [1127, 23, 955, 41], [1127, 25, 955, 43, "onCancel"], [1127, 33, 955, 52], [1128, 16, 955, 53, "style"], [1128, 21, 955, 58], [1128, 23, 955, 60, "styles"], [1128, 29, 955, 66], [1128, 30, 955, 67, "closeButton"], [1128, 41, 955, 79], [1129, 16, 955, 79, "children"], [1129, 24, 955, 79], [1129, 39, 956, 18], [1129, 43, 956, 18, "_jsxDevRuntime"], [1129, 57, 956, 18], [1129, 58, 956, 18, "jsxDEV"], [1129, 64, 956, 18], [1129, 66, 956, 19, "_lucideReactNative"], [1129, 84, 956, 19], [1129, 85, 956, 19, "X"], [1129, 86, 956, 20], [1130, 18, 956, 21, "size"], [1130, 22, 956, 25], [1130, 24, 956, 27], [1130, 26, 956, 30], [1131, 18, 956, 31, "color"], [1131, 23, 956, 36], [1131, 25, 956, 37], [1132, 16, 956, 43], [1133, 18, 956, 43, "fileName"], [1133, 26, 956, 43], [1133, 28, 956, 43, "_jsxFileName"], [1133, 40, 956, 43], [1134, 18, 956, 43, "lineNumber"], [1134, 28, 956, 43], [1135, 18, 956, 43, "columnNumber"], [1135, 30, 956, 43], [1136, 16, 956, 43], [1136, 23, 956, 45], [1137, 14, 956, 46], [1138, 16, 956, 46, "fileName"], [1138, 24, 956, 46], [1138, 26, 956, 46, "_jsxFileName"], [1138, 38, 956, 46], [1139, 16, 956, 46, "lineNumber"], [1139, 26, 956, 46], [1140, 16, 956, 46, "columnNumber"], [1140, 28, 956, 46], [1141, 14, 956, 46], [1141, 21, 957, 34], [1141, 22, 957, 35], [1142, 12, 957, 35], [1143, 14, 957, 35, "fileName"], [1143, 22, 957, 35], [1143, 24, 957, 35, "_jsxFileName"], [1143, 36, 957, 35], [1144, 14, 957, 35, "lineNumber"], [1144, 24, 957, 35], [1145, 14, 957, 35, "columnNumber"], [1145, 26, 957, 35], [1146, 12, 957, 35], [1146, 19, 958, 20], [1147, 10, 958, 21], [1148, 12, 958, 21, "fileName"], [1148, 20, 958, 21], [1148, 22, 958, 21, "_jsxFileName"], [1148, 34, 958, 21], [1149, 12, 958, 21, "lineNumber"], [1149, 22, 958, 21], [1150, 12, 958, 21, "columnNumber"], [1150, 24, 958, 21], [1151, 10, 958, 21], [1151, 17, 959, 18], [1151, 18, 959, 19], [1151, 33, 961, 12], [1151, 37, 961, 12, "_jsxDevRuntime"], [1151, 51, 961, 12], [1151, 52, 961, 12, "jsxDEV"], [1151, 58, 961, 12], [1151, 60, 961, 13, "_View"], [1151, 65, 961, 13], [1151, 66, 961, 13, "default"], [1151, 73, 961, 17], [1152, 12, 961, 18, "style"], [1152, 17, 961, 23], [1152, 19, 961, 25, "styles"], [1152, 25, 961, 31], [1152, 26, 961, 32, "privacyNotice"], [1152, 39, 961, 46], [1153, 12, 961, 46, "children"], [1153, 20, 961, 46], [1153, 36, 962, 14], [1153, 40, 962, 14, "_jsxDevRuntime"], [1153, 54, 962, 14], [1153, 55, 962, 14, "jsxDEV"], [1153, 61, 962, 14], [1153, 63, 962, 15, "_lucideReactNative"], [1153, 81, 962, 15], [1153, 82, 962, 15, "Shield"], [1153, 88, 962, 21], [1154, 14, 962, 22, "size"], [1154, 18, 962, 26], [1154, 20, 962, 28], [1154, 22, 962, 31], [1155, 14, 962, 32, "color"], [1155, 19, 962, 37], [1155, 21, 962, 38], [1156, 12, 962, 47], [1157, 14, 962, 47, "fileName"], [1157, 22, 962, 47], [1157, 24, 962, 47, "_jsxFileName"], [1157, 36, 962, 47], [1158, 14, 962, 47, "lineNumber"], [1158, 24, 962, 47], [1159, 14, 962, 47, "columnNumber"], [1159, 26, 962, 47], [1160, 12, 962, 47], [1160, 19, 962, 49], [1160, 20, 962, 50], [1160, 35, 963, 14], [1160, 39, 963, 14, "_jsxDevRuntime"], [1160, 53, 963, 14], [1160, 54, 963, 14, "jsxDEV"], [1160, 60, 963, 14], [1160, 62, 963, 15, "_Text"], [1160, 67, 963, 15], [1160, 68, 963, 15, "default"], [1160, 75, 963, 19], [1161, 14, 963, 20, "style"], [1161, 19, 963, 25], [1161, 21, 963, 27, "styles"], [1161, 27, 963, 33], [1161, 28, 963, 34, "privacyText"], [1161, 39, 963, 46], [1162, 14, 963, 46, "children"], [1162, 22, 963, 46], [1162, 24, 963, 47], [1163, 12, 965, 14], [1164, 14, 965, 14, "fileName"], [1164, 22, 965, 14], [1164, 24, 965, 14, "_jsxFileName"], [1164, 36, 965, 14], [1165, 14, 965, 14, "lineNumber"], [1165, 24, 965, 14], [1166, 14, 965, 14, "columnNumber"], [1166, 26, 965, 14], [1167, 12, 965, 14], [1167, 19, 965, 20], [1167, 20, 965, 21], [1168, 10, 965, 21], [1169, 12, 965, 21, "fileName"], [1169, 20, 965, 21], [1169, 22, 965, 21, "_jsxFileName"], [1169, 34, 965, 21], [1170, 12, 965, 21, "lineNumber"], [1170, 22, 965, 21], [1171, 12, 965, 21, "columnNumber"], [1171, 24, 965, 21], [1172, 10, 965, 21], [1172, 17, 966, 18], [1172, 18, 966, 19], [1172, 33, 968, 12], [1172, 37, 968, 12, "_jsxDevRuntime"], [1172, 51, 968, 12], [1172, 52, 968, 12, "jsxDEV"], [1172, 58, 968, 12], [1172, 60, 968, 13, "_View"], [1172, 65, 968, 13], [1172, 66, 968, 13, "default"], [1172, 73, 968, 17], [1173, 12, 968, 18, "style"], [1173, 17, 968, 23], [1173, 19, 968, 25, "styles"], [1173, 25, 968, 31], [1173, 26, 968, 32, "footer<PERSON><PERSON><PERSON>"], [1173, 39, 968, 46], [1174, 12, 968, 46, "children"], [1174, 20, 968, 46], [1174, 36, 969, 14], [1174, 40, 969, 14, "_jsxDevRuntime"], [1174, 54, 969, 14], [1174, 55, 969, 14, "jsxDEV"], [1174, 61, 969, 14], [1174, 63, 969, 15, "_Text"], [1174, 68, 969, 15], [1174, 69, 969, 15, "default"], [1174, 76, 969, 19], [1175, 14, 969, 20, "style"], [1175, 19, 969, 25], [1175, 21, 969, 27, "styles"], [1175, 27, 969, 33], [1175, 28, 969, 34, "instruction"], [1175, 39, 969, 46], [1176, 14, 969, 46, "children"], [1176, 22, 969, 46], [1176, 24, 969, 47], [1177, 12, 971, 14], [1178, 14, 971, 14, "fileName"], [1178, 22, 971, 14], [1178, 24, 971, 14, "_jsxFileName"], [1178, 36, 971, 14], [1179, 14, 971, 14, "lineNumber"], [1179, 24, 971, 14], [1180, 14, 971, 14, "columnNumber"], [1180, 26, 971, 14], [1181, 12, 971, 14], [1181, 19, 971, 20], [1181, 20, 971, 21], [1181, 35, 973, 14], [1181, 39, 973, 14, "_jsxDevRuntime"], [1181, 53, 973, 14], [1181, 54, 973, 14, "jsxDEV"], [1181, 60, 973, 14], [1181, 62, 973, 15, "_TouchableOpacity"], [1181, 79, 973, 15], [1181, 80, 973, 15, "default"], [1181, 87, 973, 31], [1182, 14, 974, 16, "onPress"], [1182, 21, 974, 23], [1182, 23, 974, 25, "capturePhoto"], [1182, 35, 974, 38], [1183, 14, 975, 16, "disabled"], [1183, 22, 975, 24], [1183, 24, 975, 26, "processingState"], [1183, 39, 975, 41], [1183, 44, 975, 46], [1183, 50, 975, 52], [1183, 54, 975, 56], [1183, 55, 975, 57, "isCameraReady"], [1183, 68, 975, 71], [1184, 14, 976, 16, "style"], [1184, 19, 976, 21], [1184, 21, 976, 23], [1184, 22, 977, 18, "styles"], [1184, 28, 977, 24], [1184, 29, 977, 25, "shutterButton"], [1184, 42, 977, 38], [1184, 44, 978, 18, "processingState"], [1184, 59, 978, 33], [1184, 64, 978, 38], [1184, 70, 978, 44], [1184, 74, 978, 48, "styles"], [1184, 80, 978, 54], [1184, 81, 978, 55, "shutterButtonDisabled"], [1184, 102, 978, 76], [1184, 103, 979, 18], [1185, 14, 979, 18, "children"], [1185, 22, 979, 18], [1185, 24, 981, 17, "processingState"], [1185, 39, 981, 32], [1185, 44, 981, 37], [1185, 50, 981, 43], [1185, 66, 982, 18], [1185, 70, 982, 18, "_jsxDevRuntime"], [1185, 84, 982, 18], [1185, 85, 982, 18, "jsxDEV"], [1185, 91, 982, 18], [1185, 93, 982, 19, "_View"], [1185, 98, 982, 19], [1185, 99, 982, 19, "default"], [1185, 106, 982, 23], [1186, 16, 982, 24, "style"], [1186, 21, 982, 29], [1186, 23, 982, 31, "styles"], [1186, 29, 982, 37], [1186, 30, 982, 38, "shutterInner"], [1187, 14, 982, 51], [1188, 16, 982, 51, "fileName"], [1188, 24, 982, 51], [1188, 26, 982, 51, "_jsxFileName"], [1188, 38, 982, 51], [1189, 16, 982, 51, "lineNumber"], [1189, 26, 982, 51], [1190, 16, 982, 51, "columnNumber"], [1190, 28, 982, 51], [1191, 14, 982, 51], [1191, 21, 982, 53], [1191, 22, 982, 54], [1191, 38, 984, 18], [1191, 42, 984, 18, "_jsxDevRuntime"], [1191, 56, 984, 18], [1191, 57, 984, 18, "jsxDEV"], [1191, 63, 984, 18], [1191, 65, 984, 19, "_ActivityIndicator"], [1191, 83, 984, 19], [1191, 84, 984, 19, "default"], [1191, 91, 984, 36], [1192, 16, 984, 37, "size"], [1192, 20, 984, 41], [1192, 22, 984, 42], [1192, 29, 984, 49], [1193, 16, 984, 50, "color"], [1193, 21, 984, 55], [1193, 23, 984, 56], [1194, 14, 984, 65], [1195, 16, 984, 65, "fileName"], [1195, 24, 984, 65], [1195, 26, 984, 65, "_jsxFileName"], [1195, 38, 984, 65], [1196, 16, 984, 65, "lineNumber"], [1196, 26, 984, 65], [1197, 16, 984, 65, "columnNumber"], [1197, 28, 984, 65], [1198, 14, 984, 65], [1198, 21, 984, 67], [1199, 12, 985, 17], [1200, 14, 985, 17, "fileName"], [1200, 22, 985, 17], [1200, 24, 985, 17, "_jsxFileName"], [1200, 36, 985, 17], [1201, 14, 985, 17, "lineNumber"], [1201, 24, 985, 17], [1202, 14, 985, 17, "columnNumber"], [1202, 26, 985, 17], [1203, 12, 985, 17], [1203, 19, 986, 32], [1203, 20, 986, 33], [1203, 35, 987, 14], [1203, 39, 987, 14, "_jsxDevRuntime"], [1203, 53, 987, 14], [1203, 54, 987, 14, "jsxDEV"], [1203, 60, 987, 14], [1203, 62, 987, 15, "_Text"], [1203, 67, 987, 15], [1203, 68, 987, 15, "default"], [1203, 75, 987, 19], [1204, 14, 987, 20, "style"], [1204, 19, 987, 25], [1204, 21, 987, 27, "styles"], [1204, 27, 987, 33], [1204, 28, 987, 34, "privacyNote"], [1204, 39, 987, 46], [1205, 14, 987, 46, "children"], [1205, 22, 987, 46], [1205, 24, 987, 47], [1206, 12, 989, 14], [1207, 14, 989, 14, "fileName"], [1207, 22, 989, 14], [1207, 24, 989, 14, "_jsxFileName"], [1207, 36, 989, 14], [1208, 14, 989, 14, "lineNumber"], [1208, 24, 989, 14], [1209, 14, 989, 14, "columnNumber"], [1209, 26, 989, 14], [1210, 12, 989, 14], [1210, 19, 989, 20], [1210, 20, 989, 21], [1211, 10, 989, 21], [1212, 12, 989, 21, "fileName"], [1212, 20, 989, 21], [1212, 22, 989, 21, "_jsxFileName"], [1212, 34, 989, 21], [1213, 12, 989, 21, "lineNumber"], [1213, 22, 989, 21], [1214, 12, 989, 21, "columnNumber"], [1214, 24, 989, 21], [1215, 10, 989, 21], [1215, 17, 990, 18], [1215, 18, 990, 19], [1216, 8, 990, 19], [1216, 23, 991, 12], [1216, 24, 992, 9], [1217, 6, 992, 9], [1218, 8, 992, 9, "fileName"], [1218, 16, 992, 9], [1218, 18, 992, 9, "_jsxFileName"], [1218, 30, 992, 9], [1219, 8, 992, 9, "lineNumber"], [1219, 18, 992, 9], [1220, 8, 992, 9, "columnNumber"], [1220, 20, 992, 9], [1221, 6, 992, 9], [1221, 13, 993, 12], [1221, 14, 993, 13], [1221, 29, 995, 6], [1221, 33, 995, 6, "_jsxDevRuntime"], [1221, 47, 995, 6], [1221, 48, 995, 6, "jsxDEV"], [1221, 54, 995, 6], [1221, 56, 995, 7, "_Modal"], [1221, 62, 995, 7], [1221, 63, 995, 7, "default"], [1221, 70, 995, 12], [1222, 8, 996, 8, "visible"], [1222, 15, 996, 15], [1222, 17, 996, 17, "processingState"], [1222, 32, 996, 32], [1222, 37, 996, 37], [1222, 43, 996, 43], [1222, 47, 996, 47, "processingState"], [1222, 62, 996, 62], [1222, 67, 996, 67], [1222, 74, 996, 75], [1223, 8, 997, 8, "transparent"], [1223, 19, 997, 19], [1224, 8, 998, 8, "animationType"], [1224, 21, 998, 21], [1224, 23, 998, 22], [1224, 29, 998, 28], [1225, 8, 998, 28, "children"], [1225, 16, 998, 28], [1225, 31, 1000, 8], [1225, 35, 1000, 8, "_jsxDevRuntime"], [1225, 49, 1000, 8], [1225, 50, 1000, 8, "jsxDEV"], [1225, 56, 1000, 8], [1225, 58, 1000, 9, "_View"], [1225, 63, 1000, 9], [1225, 64, 1000, 9, "default"], [1225, 71, 1000, 13], [1226, 10, 1000, 14, "style"], [1226, 15, 1000, 19], [1226, 17, 1000, 21, "styles"], [1226, 23, 1000, 27], [1226, 24, 1000, 28, "processingModal"], [1226, 39, 1000, 44], [1227, 10, 1000, 44, "children"], [1227, 18, 1000, 44], [1227, 33, 1001, 10], [1227, 37, 1001, 10, "_jsxDevRuntime"], [1227, 51, 1001, 10], [1227, 52, 1001, 10, "jsxDEV"], [1227, 58, 1001, 10], [1227, 60, 1001, 11, "_View"], [1227, 65, 1001, 11], [1227, 66, 1001, 11, "default"], [1227, 73, 1001, 15], [1228, 12, 1001, 16, "style"], [1228, 17, 1001, 21], [1228, 19, 1001, 23, "styles"], [1228, 25, 1001, 29], [1228, 26, 1001, 30, "processingContent"], [1228, 43, 1001, 48], [1229, 12, 1001, 48, "children"], [1229, 20, 1001, 48], [1229, 36, 1002, 12], [1229, 40, 1002, 12, "_jsxDevRuntime"], [1229, 54, 1002, 12], [1229, 55, 1002, 12, "jsxDEV"], [1229, 61, 1002, 12], [1229, 63, 1002, 13, "_ActivityIndicator"], [1229, 81, 1002, 13], [1229, 82, 1002, 13, "default"], [1229, 89, 1002, 30], [1230, 14, 1002, 31, "size"], [1230, 18, 1002, 35], [1230, 20, 1002, 36], [1230, 27, 1002, 43], [1231, 14, 1002, 44, "color"], [1231, 19, 1002, 49], [1231, 21, 1002, 50], [1232, 12, 1002, 59], [1233, 14, 1002, 59, "fileName"], [1233, 22, 1002, 59], [1233, 24, 1002, 59, "_jsxFileName"], [1233, 36, 1002, 59], [1234, 14, 1002, 59, "lineNumber"], [1234, 24, 1002, 59], [1235, 14, 1002, 59, "columnNumber"], [1235, 26, 1002, 59], [1236, 12, 1002, 59], [1236, 19, 1002, 61], [1236, 20, 1002, 62], [1236, 35, 1004, 12], [1236, 39, 1004, 12, "_jsxDevRuntime"], [1236, 53, 1004, 12], [1236, 54, 1004, 12, "jsxDEV"], [1236, 60, 1004, 12], [1236, 62, 1004, 13, "_Text"], [1236, 67, 1004, 13], [1236, 68, 1004, 13, "default"], [1236, 75, 1004, 17], [1237, 14, 1004, 18, "style"], [1237, 19, 1004, 23], [1237, 21, 1004, 25, "styles"], [1237, 27, 1004, 31], [1237, 28, 1004, 32, "processingTitle"], [1237, 43, 1004, 48], [1238, 14, 1004, 48, "children"], [1238, 22, 1004, 48], [1238, 25, 1005, 15, "processingState"], [1238, 40, 1005, 30], [1238, 45, 1005, 35], [1238, 56, 1005, 46], [1238, 60, 1005, 50], [1238, 80, 1005, 70], [1238, 82, 1006, 15, "processingState"], [1238, 97, 1006, 30], [1238, 102, 1006, 35], [1238, 113, 1006, 46], [1238, 117, 1006, 50], [1238, 146, 1006, 79], [1238, 148, 1007, 15, "processingState"], [1238, 163, 1007, 30], [1238, 168, 1007, 35], [1238, 180, 1007, 47], [1238, 184, 1007, 51], [1238, 216, 1007, 83], [1238, 218, 1008, 15, "processingState"], [1238, 233, 1008, 30], [1238, 238, 1008, 35], [1238, 249, 1008, 46], [1238, 253, 1008, 50], [1238, 275, 1008, 72], [1239, 12, 1008, 72], [1240, 14, 1008, 72, "fileName"], [1240, 22, 1008, 72], [1240, 24, 1008, 72, "_jsxFileName"], [1240, 36, 1008, 72], [1241, 14, 1008, 72, "lineNumber"], [1241, 24, 1008, 72], [1242, 14, 1008, 72, "columnNumber"], [1242, 26, 1008, 72], [1243, 12, 1008, 72], [1243, 19, 1009, 18], [1243, 20, 1009, 19], [1243, 35, 1010, 12], [1243, 39, 1010, 12, "_jsxDevRuntime"], [1243, 53, 1010, 12], [1243, 54, 1010, 12, "jsxDEV"], [1243, 60, 1010, 12], [1243, 62, 1010, 13, "_View"], [1243, 67, 1010, 13], [1243, 68, 1010, 13, "default"], [1243, 75, 1010, 17], [1244, 14, 1010, 18, "style"], [1244, 19, 1010, 23], [1244, 21, 1010, 25, "styles"], [1244, 27, 1010, 31], [1244, 28, 1010, 32, "progressBar"], [1244, 39, 1010, 44], [1245, 14, 1010, 44, "children"], [1245, 22, 1010, 44], [1245, 37, 1011, 14], [1245, 41, 1011, 14, "_jsxDevRuntime"], [1245, 55, 1011, 14], [1245, 56, 1011, 14, "jsxDEV"], [1245, 62, 1011, 14], [1245, 64, 1011, 15, "_View"], [1245, 69, 1011, 15], [1245, 70, 1011, 15, "default"], [1245, 77, 1011, 19], [1246, 16, 1012, 16, "style"], [1246, 21, 1012, 21], [1246, 23, 1012, 23], [1246, 24, 1013, 18, "styles"], [1246, 30, 1013, 24], [1246, 31, 1013, 25, "progressFill"], [1246, 43, 1013, 37], [1246, 45, 1014, 18], [1247, 18, 1014, 20, "width"], [1247, 23, 1014, 25], [1247, 25, 1014, 27], [1247, 28, 1014, 30, "processingProgress"], [1247, 46, 1014, 48], [1248, 16, 1014, 52], [1248, 17, 1014, 53], [1249, 14, 1015, 18], [1250, 16, 1015, 18, "fileName"], [1250, 24, 1015, 18], [1250, 26, 1015, 18, "_jsxFileName"], [1250, 38, 1015, 18], [1251, 16, 1015, 18, "lineNumber"], [1251, 26, 1015, 18], [1252, 16, 1015, 18, "columnNumber"], [1252, 28, 1015, 18], [1253, 14, 1015, 18], [1253, 21, 1016, 15], [1254, 12, 1016, 16], [1255, 14, 1016, 16, "fileName"], [1255, 22, 1016, 16], [1255, 24, 1016, 16, "_jsxFileName"], [1255, 36, 1016, 16], [1256, 14, 1016, 16, "lineNumber"], [1256, 24, 1016, 16], [1257, 14, 1016, 16, "columnNumber"], [1257, 26, 1016, 16], [1258, 12, 1016, 16], [1258, 19, 1017, 18], [1258, 20, 1017, 19], [1258, 35, 1018, 12], [1258, 39, 1018, 12, "_jsxDevRuntime"], [1258, 53, 1018, 12], [1258, 54, 1018, 12, "jsxDEV"], [1258, 60, 1018, 12], [1258, 62, 1018, 13, "_Text"], [1258, 67, 1018, 13], [1258, 68, 1018, 13, "default"], [1258, 75, 1018, 17], [1259, 14, 1018, 18, "style"], [1259, 19, 1018, 23], [1259, 21, 1018, 25, "styles"], [1259, 27, 1018, 31], [1259, 28, 1018, 32, "processingDescription"], [1259, 49, 1018, 54], [1260, 14, 1018, 54, "children"], [1260, 22, 1018, 54], [1260, 25, 1019, 15, "processingState"], [1260, 40, 1019, 30], [1260, 45, 1019, 35], [1260, 56, 1019, 46], [1260, 60, 1019, 50], [1260, 89, 1019, 79], [1260, 91, 1020, 15, "processingState"], [1260, 106, 1020, 30], [1260, 111, 1020, 35], [1260, 122, 1020, 46], [1260, 126, 1020, 50], [1260, 164, 1020, 88], [1260, 166, 1021, 15, "processingState"], [1260, 181, 1021, 30], [1260, 186, 1021, 35], [1260, 198, 1021, 47], [1260, 202, 1021, 51], [1260, 247, 1021, 96], [1260, 249, 1022, 15, "processingState"], [1260, 264, 1022, 30], [1260, 269, 1022, 35], [1260, 280, 1022, 46], [1260, 284, 1022, 50], [1260, 325, 1022, 91], [1261, 12, 1022, 91], [1262, 14, 1022, 91, "fileName"], [1262, 22, 1022, 91], [1262, 24, 1022, 91, "_jsxFileName"], [1262, 36, 1022, 91], [1263, 14, 1022, 91, "lineNumber"], [1263, 24, 1022, 91], [1264, 14, 1022, 91, "columnNumber"], [1264, 26, 1022, 91], [1265, 12, 1022, 91], [1265, 19, 1023, 18], [1265, 20, 1023, 19], [1265, 22, 1024, 13, "processingState"], [1265, 37, 1024, 28], [1265, 42, 1024, 33], [1265, 53, 1024, 44], [1265, 70, 1025, 14], [1265, 74, 1025, 14, "_jsxDevRuntime"], [1265, 88, 1025, 14], [1265, 89, 1025, 14, "jsxDEV"], [1265, 95, 1025, 14], [1265, 97, 1025, 15, "_lucideReactNative"], [1265, 115, 1025, 15], [1265, 116, 1025, 15, "CheckCircle"], [1265, 127, 1025, 26], [1266, 14, 1025, 27, "size"], [1266, 18, 1025, 31], [1266, 20, 1025, 33], [1266, 22, 1025, 36], [1267, 14, 1025, 37, "color"], [1267, 19, 1025, 42], [1267, 21, 1025, 43], [1267, 30, 1025, 52], [1268, 14, 1025, 53, "style"], [1268, 19, 1025, 58], [1268, 21, 1025, 60, "styles"], [1268, 27, 1025, 66], [1268, 28, 1025, 67, "successIcon"], [1269, 12, 1025, 79], [1270, 14, 1025, 79, "fileName"], [1270, 22, 1025, 79], [1270, 24, 1025, 79, "_jsxFileName"], [1270, 36, 1025, 79], [1271, 14, 1025, 79, "lineNumber"], [1271, 24, 1025, 79], [1272, 14, 1025, 79, "columnNumber"], [1272, 26, 1025, 79], [1273, 12, 1025, 79], [1273, 19, 1025, 81], [1273, 20, 1026, 13], [1274, 10, 1026, 13], [1275, 12, 1026, 13, "fileName"], [1275, 20, 1026, 13], [1275, 22, 1026, 13, "_jsxFileName"], [1275, 34, 1026, 13], [1276, 12, 1026, 13, "lineNumber"], [1276, 22, 1026, 13], [1277, 12, 1026, 13, "columnNumber"], [1277, 24, 1026, 13], [1278, 10, 1026, 13], [1278, 17, 1027, 16], [1279, 8, 1027, 17], [1280, 10, 1027, 17, "fileName"], [1280, 18, 1027, 17], [1280, 20, 1027, 17, "_jsxFileName"], [1280, 32, 1027, 17], [1281, 10, 1027, 17, "lineNumber"], [1281, 20, 1027, 17], [1282, 10, 1027, 17, "columnNumber"], [1282, 22, 1027, 17], [1283, 8, 1027, 17], [1283, 15, 1028, 14], [1284, 6, 1028, 15], [1285, 8, 1028, 15, "fileName"], [1285, 16, 1028, 15], [1285, 18, 1028, 15, "_jsxFileName"], [1285, 30, 1028, 15], [1286, 8, 1028, 15, "lineNumber"], [1286, 18, 1028, 15], [1287, 8, 1028, 15, "columnNumber"], [1287, 20, 1028, 15], [1288, 6, 1028, 15], [1288, 13, 1029, 13], [1288, 14, 1029, 14], [1288, 29, 1031, 6], [1288, 33, 1031, 6, "_jsxDevRuntime"], [1288, 47, 1031, 6], [1288, 48, 1031, 6, "jsxDEV"], [1288, 54, 1031, 6], [1288, 56, 1031, 7, "_Modal"], [1288, 62, 1031, 7], [1288, 63, 1031, 7, "default"], [1288, 70, 1031, 12], [1289, 8, 1032, 8, "visible"], [1289, 15, 1032, 15], [1289, 17, 1032, 17, "processingState"], [1289, 32, 1032, 32], [1289, 37, 1032, 37], [1289, 44, 1032, 45], [1290, 8, 1033, 8, "transparent"], [1290, 19, 1033, 19], [1291, 8, 1034, 8, "animationType"], [1291, 21, 1034, 21], [1291, 23, 1034, 22], [1291, 29, 1034, 28], [1292, 8, 1034, 28, "children"], [1292, 16, 1034, 28], [1292, 31, 1036, 8], [1292, 35, 1036, 8, "_jsxDevRuntime"], [1292, 49, 1036, 8], [1292, 50, 1036, 8, "jsxDEV"], [1292, 56, 1036, 8], [1292, 58, 1036, 9, "_View"], [1292, 63, 1036, 9], [1292, 64, 1036, 9, "default"], [1292, 71, 1036, 13], [1293, 10, 1036, 14, "style"], [1293, 15, 1036, 19], [1293, 17, 1036, 21, "styles"], [1293, 23, 1036, 27], [1293, 24, 1036, 28, "processingModal"], [1293, 39, 1036, 44], [1294, 10, 1036, 44, "children"], [1294, 18, 1036, 44], [1294, 33, 1037, 10], [1294, 37, 1037, 10, "_jsxDevRuntime"], [1294, 51, 1037, 10], [1294, 52, 1037, 10, "jsxDEV"], [1294, 58, 1037, 10], [1294, 60, 1037, 11, "_View"], [1294, 65, 1037, 11], [1294, 66, 1037, 11, "default"], [1294, 73, 1037, 15], [1295, 12, 1037, 16, "style"], [1295, 17, 1037, 21], [1295, 19, 1037, 23, "styles"], [1295, 25, 1037, 29], [1295, 26, 1037, 30, "errorContent"], [1295, 38, 1037, 43], [1296, 12, 1037, 43, "children"], [1296, 20, 1037, 43], [1296, 36, 1038, 12], [1296, 40, 1038, 12, "_jsxDevRuntime"], [1296, 54, 1038, 12], [1296, 55, 1038, 12, "jsxDEV"], [1296, 61, 1038, 12], [1296, 63, 1038, 13, "_lucideReactNative"], [1296, 81, 1038, 13], [1296, 82, 1038, 13, "X"], [1296, 83, 1038, 14], [1297, 14, 1038, 15, "size"], [1297, 18, 1038, 19], [1297, 20, 1038, 21], [1297, 22, 1038, 24], [1298, 14, 1038, 25, "color"], [1298, 19, 1038, 30], [1298, 21, 1038, 31], [1299, 12, 1038, 40], [1300, 14, 1038, 40, "fileName"], [1300, 22, 1038, 40], [1300, 24, 1038, 40, "_jsxFileName"], [1300, 36, 1038, 40], [1301, 14, 1038, 40, "lineNumber"], [1301, 24, 1038, 40], [1302, 14, 1038, 40, "columnNumber"], [1302, 26, 1038, 40], [1303, 12, 1038, 40], [1303, 19, 1038, 42], [1303, 20, 1038, 43], [1303, 35, 1039, 12], [1303, 39, 1039, 12, "_jsxDevRuntime"], [1303, 53, 1039, 12], [1303, 54, 1039, 12, "jsxDEV"], [1303, 60, 1039, 12], [1303, 62, 1039, 13, "_Text"], [1303, 67, 1039, 13], [1303, 68, 1039, 13, "default"], [1303, 75, 1039, 17], [1304, 14, 1039, 18, "style"], [1304, 19, 1039, 23], [1304, 21, 1039, 25, "styles"], [1304, 27, 1039, 31], [1304, 28, 1039, 32, "errorTitle"], [1304, 38, 1039, 43], [1305, 14, 1039, 43, "children"], [1305, 22, 1039, 43], [1305, 24, 1039, 44], [1306, 12, 1039, 61], [1307, 14, 1039, 61, "fileName"], [1307, 22, 1039, 61], [1307, 24, 1039, 61, "_jsxFileName"], [1307, 36, 1039, 61], [1308, 14, 1039, 61, "lineNumber"], [1308, 24, 1039, 61], [1309, 14, 1039, 61, "columnNumber"], [1309, 26, 1039, 61], [1310, 12, 1039, 61], [1310, 19, 1039, 67], [1310, 20, 1039, 68], [1310, 35, 1040, 12], [1310, 39, 1040, 12, "_jsxDevRuntime"], [1310, 53, 1040, 12], [1310, 54, 1040, 12, "jsxDEV"], [1310, 60, 1040, 12], [1310, 62, 1040, 13, "_Text"], [1310, 67, 1040, 13], [1310, 68, 1040, 13, "default"], [1310, 75, 1040, 17], [1311, 14, 1040, 18, "style"], [1311, 19, 1040, 23], [1311, 21, 1040, 25, "styles"], [1311, 27, 1040, 31], [1311, 28, 1040, 32, "errorMessage"], [1311, 40, 1040, 45], [1312, 14, 1040, 45, "children"], [1312, 22, 1040, 45], [1312, 24, 1040, 47, "errorMessage"], [1313, 12, 1040, 59], [1314, 14, 1040, 59, "fileName"], [1314, 22, 1040, 59], [1314, 24, 1040, 59, "_jsxFileName"], [1314, 36, 1040, 59], [1315, 14, 1040, 59, "lineNumber"], [1315, 24, 1040, 59], [1316, 14, 1040, 59, "columnNumber"], [1316, 26, 1040, 59], [1317, 12, 1040, 59], [1317, 19, 1040, 66], [1317, 20, 1040, 67], [1317, 35, 1041, 12], [1317, 39, 1041, 12, "_jsxDevRuntime"], [1317, 53, 1041, 12], [1317, 54, 1041, 12, "jsxDEV"], [1317, 60, 1041, 12], [1317, 62, 1041, 13, "_TouchableOpacity"], [1317, 79, 1041, 13], [1317, 80, 1041, 13, "default"], [1317, 87, 1041, 29], [1318, 14, 1042, 14, "onPress"], [1318, 21, 1042, 21], [1318, 23, 1042, 23, "retryCapture"], [1318, 35, 1042, 36], [1319, 14, 1043, 14, "style"], [1319, 19, 1043, 19], [1319, 21, 1043, 21, "styles"], [1319, 27, 1043, 27], [1319, 28, 1043, 28, "primaryButton"], [1319, 41, 1043, 42], [1320, 14, 1043, 42, "children"], [1320, 22, 1043, 42], [1320, 37, 1045, 14], [1320, 41, 1045, 14, "_jsxDevRuntime"], [1320, 55, 1045, 14], [1320, 56, 1045, 14, "jsxDEV"], [1320, 62, 1045, 14], [1320, 64, 1045, 15, "_Text"], [1320, 69, 1045, 15], [1320, 70, 1045, 15, "default"], [1320, 77, 1045, 19], [1321, 16, 1045, 20, "style"], [1321, 21, 1045, 25], [1321, 23, 1045, 27, "styles"], [1321, 29, 1045, 33], [1321, 30, 1045, 34, "primaryButtonText"], [1321, 47, 1045, 52], [1322, 16, 1045, 52, "children"], [1322, 24, 1045, 52], [1322, 26, 1045, 53], [1323, 14, 1045, 62], [1324, 16, 1045, 62, "fileName"], [1324, 24, 1045, 62], [1324, 26, 1045, 62, "_jsxFileName"], [1324, 38, 1045, 62], [1325, 16, 1045, 62, "lineNumber"], [1325, 26, 1045, 62], [1326, 16, 1045, 62, "columnNumber"], [1326, 28, 1045, 62], [1327, 14, 1045, 62], [1327, 21, 1045, 68], [1328, 12, 1045, 69], [1329, 14, 1045, 69, "fileName"], [1329, 22, 1045, 69], [1329, 24, 1045, 69, "_jsxFileName"], [1329, 36, 1045, 69], [1330, 14, 1045, 69, "lineNumber"], [1330, 24, 1045, 69], [1331, 14, 1045, 69, "columnNumber"], [1331, 26, 1045, 69], [1332, 12, 1045, 69], [1332, 19, 1046, 30], [1332, 20, 1046, 31], [1332, 35, 1047, 12], [1332, 39, 1047, 12, "_jsxDevRuntime"], [1332, 53, 1047, 12], [1332, 54, 1047, 12, "jsxDEV"], [1332, 60, 1047, 12], [1332, 62, 1047, 13, "_TouchableOpacity"], [1332, 79, 1047, 13], [1332, 80, 1047, 13, "default"], [1332, 87, 1047, 29], [1333, 14, 1048, 14, "onPress"], [1333, 21, 1048, 21], [1333, 23, 1048, 23, "onCancel"], [1333, 31, 1048, 32], [1334, 14, 1049, 14, "style"], [1334, 19, 1049, 19], [1334, 21, 1049, 21, "styles"], [1334, 27, 1049, 27], [1334, 28, 1049, 28, "secondaryButton"], [1334, 43, 1049, 44], [1335, 14, 1049, 44, "children"], [1335, 22, 1049, 44], [1335, 37, 1051, 14], [1335, 41, 1051, 14, "_jsxDevRuntime"], [1335, 55, 1051, 14], [1335, 56, 1051, 14, "jsxDEV"], [1335, 62, 1051, 14], [1335, 64, 1051, 15, "_Text"], [1335, 69, 1051, 15], [1335, 70, 1051, 15, "default"], [1335, 77, 1051, 19], [1336, 16, 1051, 20, "style"], [1336, 21, 1051, 25], [1336, 23, 1051, 27, "styles"], [1336, 29, 1051, 33], [1336, 30, 1051, 34, "secondaryButtonText"], [1336, 49, 1051, 54], [1337, 16, 1051, 54, "children"], [1337, 24, 1051, 54], [1337, 26, 1051, 55], [1338, 14, 1051, 61], [1339, 16, 1051, 61, "fileName"], [1339, 24, 1051, 61], [1339, 26, 1051, 61, "_jsxFileName"], [1339, 38, 1051, 61], [1340, 16, 1051, 61, "lineNumber"], [1340, 26, 1051, 61], [1341, 16, 1051, 61, "columnNumber"], [1341, 28, 1051, 61], [1342, 14, 1051, 61], [1342, 21, 1051, 67], [1343, 12, 1051, 68], [1344, 14, 1051, 68, "fileName"], [1344, 22, 1051, 68], [1344, 24, 1051, 68, "_jsxFileName"], [1344, 36, 1051, 68], [1345, 14, 1051, 68, "lineNumber"], [1345, 24, 1051, 68], [1346, 14, 1051, 68, "columnNumber"], [1346, 26, 1051, 68], [1347, 12, 1051, 68], [1347, 19, 1052, 30], [1347, 20, 1052, 31], [1348, 10, 1052, 31], [1349, 12, 1052, 31, "fileName"], [1349, 20, 1052, 31], [1349, 22, 1052, 31, "_jsxFileName"], [1349, 34, 1052, 31], [1350, 12, 1052, 31, "lineNumber"], [1350, 22, 1052, 31], [1351, 12, 1052, 31, "columnNumber"], [1351, 24, 1052, 31], [1352, 10, 1052, 31], [1352, 17, 1053, 16], [1353, 8, 1053, 17], [1354, 10, 1053, 17, "fileName"], [1354, 18, 1053, 17], [1354, 20, 1053, 17, "_jsxFileName"], [1354, 32, 1053, 17], [1355, 10, 1053, 17, "lineNumber"], [1355, 20, 1053, 17], [1356, 10, 1053, 17, "columnNumber"], [1356, 22, 1053, 17], [1357, 8, 1053, 17], [1357, 15, 1054, 14], [1358, 6, 1054, 15], [1359, 8, 1054, 15, "fileName"], [1359, 16, 1054, 15], [1359, 18, 1054, 15, "_jsxFileName"], [1359, 30, 1054, 15], [1360, 8, 1054, 15, "lineNumber"], [1360, 18, 1054, 15], [1361, 8, 1054, 15, "columnNumber"], [1361, 20, 1054, 15], [1362, 6, 1054, 15], [1362, 13, 1055, 13], [1362, 14, 1055, 14], [1363, 4, 1055, 14], [1364, 6, 1055, 14, "fileName"], [1364, 14, 1055, 14], [1364, 16, 1055, 14, "_jsxFileName"], [1364, 28, 1055, 14], [1365, 6, 1055, 14, "lineNumber"], [1365, 16, 1055, 14], [1366, 6, 1055, 14, "columnNumber"], [1366, 18, 1055, 14], [1367, 4, 1055, 14], [1367, 11, 1056, 10], [1367, 12, 1056, 11], [1368, 2, 1058, 0], [1369, 2, 1058, 1, "_s"], [1369, 4, 1058, 1], [1369, 5, 51, 24, "EchoCameraWeb"], [1369, 18, 51, 37], [1370, 4, 51, 37], [1370, 12, 58, 42, "useCameraPermissions"], [1370, 44, 58, 62], [1370, 46, 72, 19, "useUpload"], [1370, 64, 72, 28], [1371, 2, 72, 28], [1372, 2, 72, 28, "_c"], [1372, 4, 72, 28], [1372, 7, 51, 24, "EchoCameraWeb"], [1372, 20, 51, 37], [1373, 2, 1059, 0], [1373, 8, 1059, 6, "styles"], [1373, 14, 1059, 12], [1373, 17, 1059, 15, "StyleSheet"], [1373, 36, 1059, 25], [1373, 37, 1059, 26, "create"], [1373, 43, 1059, 32], [1373, 44, 1059, 33], [1374, 4, 1060, 2, "container"], [1374, 13, 1060, 11], [1374, 15, 1060, 13], [1375, 6, 1061, 4, "flex"], [1375, 10, 1061, 8], [1375, 12, 1061, 10], [1375, 13, 1061, 11], [1376, 6, 1062, 4, "backgroundColor"], [1376, 21, 1062, 19], [1376, 23, 1062, 21], [1377, 4, 1063, 2], [1377, 5, 1063, 3], [1378, 4, 1064, 2, "cameraContainer"], [1378, 19, 1064, 17], [1378, 21, 1064, 19], [1379, 6, 1065, 4, "flex"], [1379, 10, 1065, 8], [1379, 12, 1065, 10], [1379, 13, 1065, 11], [1380, 6, 1066, 4, "max<PERSON><PERSON><PERSON>"], [1380, 14, 1066, 12], [1380, 16, 1066, 14], [1380, 19, 1066, 17], [1381, 6, 1067, 4, "alignSelf"], [1381, 15, 1067, 13], [1381, 17, 1067, 15], [1381, 25, 1067, 23], [1382, 6, 1068, 4, "width"], [1382, 11, 1068, 9], [1382, 13, 1068, 11], [1383, 4, 1069, 2], [1383, 5, 1069, 3], [1384, 4, 1070, 2, "camera"], [1384, 10, 1070, 8], [1384, 12, 1070, 10], [1385, 6, 1071, 4, "flex"], [1385, 10, 1071, 8], [1385, 12, 1071, 10], [1386, 4, 1072, 2], [1386, 5, 1072, 3], [1387, 4, 1073, 2, "headerOverlay"], [1387, 17, 1073, 15], [1387, 19, 1073, 17], [1388, 6, 1074, 4, "position"], [1388, 14, 1074, 12], [1388, 16, 1074, 14], [1388, 26, 1074, 24], [1389, 6, 1075, 4, "top"], [1389, 9, 1075, 7], [1389, 11, 1075, 9], [1389, 12, 1075, 10], [1390, 6, 1076, 4, "left"], [1390, 10, 1076, 8], [1390, 12, 1076, 10], [1390, 13, 1076, 11], [1391, 6, 1077, 4, "right"], [1391, 11, 1077, 9], [1391, 13, 1077, 11], [1391, 14, 1077, 12], [1392, 6, 1078, 4, "backgroundColor"], [1392, 21, 1078, 19], [1392, 23, 1078, 21], [1392, 36, 1078, 34], [1393, 6, 1079, 4, "paddingTop"], [1393, 16, 1079, 14], [1393, 18, 1079, 16], [1393, 20, 1079, 18], [1394, 6, 1080, 4, "paddingHorizontal"], [1394, 23, 1080, 21], [1394, 25, 1080, 23], [1394, 27, 1080, 25], [1395, 6, 1081, 4, "paddingBottom"], [1395, 19, 1081, 17], [1395, 21, 1081, 19], [1396, 4, 1082, 2], [1396, 5, 1082, 3], [1397, 4, 1083, 2, "headerContent"], [1397, 17, 1083, 15], [1397, 19, 1083, 17], [1398, 6, 1084, 4, "flexDirection"], [1398, 19, 1084, 17], [1398, 21, 1084, 19], [1398, 26, 1084, 24], [1399, 6, 1085, 4, "justifyContent"], [1399, 20, 1085, 18], [1399, 22, 1085, 20], [1399, 37, 1085, 35], [1400, 6, 1086, 4, "alignItems"], [1400, 16, 1086, 14], [1400, 18, 1086, 16], [1401, 4, 1087, 2], [1401, 5, 1087, 3], [1402, 4, 1088, 2, "headerLeft"], [1402, 14, 1088, 12], [1402, 16, 1088, 14], [1403, 6, 1089, 4, "flex"], [1403, 10, 1089, 8], [1403, 12, 1089, 10], [1404, 4, 1090, 2], [1404, 5, 1090, 3], [1405, 4, 1091, 2, "headerTitle"], [1405, 15, 1091, 13], [1405, 17, 1091, 15], [1406, 6, 1092, 4, "fontSize"], [1406, 14, 1092, 12], [1406, 16, 1092, 14], [1406, 18, 1092, 16], [1407, 6, 1093, 4, "fontWeight"], [1407, 16, 1093, 14], [1407, 18, 1093, 16], [1407, 23, 1093, 21], [1408, 6, 1094, 4, "color"], [1408, 11, 1094, 9], [1408, 13, 1094, 11], [1408, 19, 1094, 17], [1409, 6, 1095, 4, "marginBottom"], [1409, 18, 1095, 16], [1409, 20, 1095, 18], [1410, 4, 1096, 2], [1410, 5, 1096, 3], [1411, 4, 1097, 2, "subtitleRow"], [1411, 15, 1097, 13], [1411, 17, 1097, 15], [1412, 6, 1098, 4, "flexDirection"], [1412, 19, 1098, 17], [1412, 21, 1098, 19], [1412, 26, 1098, 24], [1413, 6, 1099, 4, "alignItems"], [1413, 16, 1099, 14], [1413, 18, 1099, 16], [1413, 26, 1099, 24], [1414, 6, 1100, 4, "marginBottom"], [1414, 18, 1100, 16], [1414, 20, 1100, 18], [1415, 4, 1101, 2], [1415, 5, 1101, 3], [1416, 4, 1102, 2, "webIcon"], [1416, 11, 1102, 9], [1416, 13, 1102, 11], [1417, 6, 1103, 4, "fontSize"], [1417, 14, 1103, 12], [1417, 16, 1103, 14], [1417, 18, 1103, 16], [1418, 6, 1104, 4, "marginRight"], [1418, 17, 1104, 15], [1418, 19, 1104, 17], [1419, 4, 1105, 2], [1419, 5, 1105, 3], [1420, 4, 1106, 2, "headerSubtitle"], [1420, 18, 1106, 16], [1420, 20, 1106, 18], [1421, 6, 1107, 4, "fontSize"], [1421, 14, 1107, 12], [1421, 16, 1107, 14], [1421, 18, 1107, 16], [1422, 6, 1108, 4, "color"], [1422, 11, 1108, 9], [1422, 13, 1108, 11], [1422, 19, 1108, 17], [1423, 6, 1109, 4, "opacity"], [1423, 13, 1109, 11], [1423, 15, 1109, 13], [1424, 4, 1110, 2], [1424, 5, 1110, 3], [1425, 4, 1111, 2, "challengeRow"], [1425, 16, 1111, 14], [1425, 18, 1111, 16], [1426, 6, 1112, 4, "flexDirection"], [1426, 19, 1112, 17], [1426, 21, 1112, 19], [1426, 26, 1112, 24], [1427, 6, 1113, 4, "alignItems"], [1427, 16, 1113, 14], [1427, 18, 1113, 16], [1428, 4, 1114, 2], [1428, 5, 1114, 3], [1429, 4, 1115, 2, "challengeCode"], [1429, 17, 1115, 15], [1429, 19, 1115, 17], [1430, 6, 1116, 4, "fontSize"], [1430, 14, 1116, 12], [1430, 16, 1116, 14], [1430, 18, 1116, 16], [1431, 6, 1117, 4, "color"], [1431, 11, 1117, 9], [1431, 13, 1117, 11], [1431, 19, 1117, 17], [1432, 6, 1118, 4, "marginLeft"], [1432, 16, 1118, 14], [1432, 18, 1118, 16], [1432, 19, 1118, 17], [1433, 6, 1119, 4, "fontFamily"], [1433, 16, 1119, 14], [1433, 18, 1119, 16], [1434, 4, 1120, 2], [1434, 5, 1120, 3], [1435, 4, 1121, 2, "closeButton"], [1435, 15, 1121, 13], [1435, 17, 1121, 15], [1436, 6, 1122, 4, "padding"], [1436, 13, 1122, 11], [1436, 15, 1122, 13], [1437, 4, 1123, 2], [1437, 5, 1123, 3], [1438, 4, 1124, 2, "privacyNotice"], [1438, 17, 1124, 15], [1438, 19, 1124, 17], [1439, 6, 1125, 4, "position"], [1439, 14, 1125, 12], [1439, 16, 1125, 14], [1439, 26, 1125, 24], [1440, 6, 1126, 4, "top"], [1440, 9, 1126, 7], [1440, 11, 1126, 9], [1440, 14, 1126, 12], [1441, 6, 1127, 4, "left"], [1441, 10, 1127, 8], [1441, 12, 1127, 10], [1441, 14, 1127, 12], [1442, 6, 1128, 4, "right"], [1442, 11, 1128, 9], [1442, 13, 1128, 11], [1442, 15, 1128, 13], [1443, 6, 1129, 4, "backgroundColor"], [1443, 21, 1129, 19], [1443, 23, 1129, 21], [1443, 48, 1129, 46], [1444, 6, 1130, 4, "borderRadius"], [1444, 18, 1130, 16], [1444, 20, 1130, 18], [1444, 21, 1130, 19], [1445, 6, 1131, 4, "padding"], [1445, 13, 1131, 11], [1445, 15, 1131, 13], [1445, 17, 1131, 15], [1446, 6, 1132, 4, "flexDirection"], [1446, 19, 1132, 17], [1446, 21, 1132, 19], [1446, 26, 1132, 24], [1447, 6, 1133, 4, "alignItems"], [1447, 16, 1133, 14], [1447, 18, 1133, 16], [1448, 4, 1134, 2], [1448, 5, 1134, 3], [1449, 4, 1135, 2, "privacyText"], [1449, 15, 1135, 13], [1449, 17, 1135, 15], [1450, 6, 1136, 4, "color"], [1450, 11, 1136, 9], [1450, 13, 1136, 11], [1450, 19, 1136, 17], [1451, 6, 1137, 4, "fontSize"], [1451, 14, 1137, 12], [1451, 16, 1137, 14], [1451, 18, 1137, 16], [1452, 6, 1138, 4, "marginLeft"], [1452, 16, 1138, 14], [1452, 18, 1138, 16], [1452, 19, 1138, 17], [1453, 6, 1139, 4, "flex"], [1453, 10, 1139, 8], [1453, 12, 1139, 10], [1454, 4, 1140, 2], [1454, 5, 1140, 3], [1455, 4, 1141, 2, "footer<PERSON><PERSON><PERSON>"], [1455, 17, 1141, 15], [1455, 19, 1141, 17], [1456, 6, 1142, 4, "position"], [1456, 14, 1142, 12], [1456, 16, 1142, 14], [1456, 26, 1142, 24], [1457, 6, 1143, 4, "bottom"], [1457, 12, 1143, 10], [1457, 14, 1143, 12], [1457, 15, 1143, 13], [1458, 6, 1144, 4, "left"], [1458, 10, 1144, 8], [1458, 12, 1144, 10], [1458, 13, 1144, 11], [1459, 6, 1145, 4, "right"], [1459, 11, 1145, 9], [1459, 13, 1145, 11], [1459, 14, 1145, 12], [1460, 6, 1146, 4, "backgroundColor"], [1460, 21, 1146, 19], [1460, 23, 1146, 21], [1460, 36, 1146, 34], [1461, 6, 1147, 4, "paddingBottom"], [1461, 19, 1147, 17], [1461, 21, 1147, 19], [1461, 23, 1147, 21], [1462, 6, 1148, 4, "paddingTop"], [1462, 16, 1148, 14], [1462, 18, 1148, 16], [1462, 20, 1148, 18], [1463, 6, 1149, 4, "alignItems"], [1463, 16, 1149, 14], [1463, 18, 1149, 16], [1464, 4, 1150, 2], [1464, 5, 1150, 3], [1465, 4, 1151, 2, "instruction"], [1465, 15, 1151, 13], [1465, 17, 1151, 15], [1466, 6, 1152, 4, "fontSize"], [1466, 14, 1152, 12], [1466, 16, 1152, 14], [1466, 18, 1152, 16], [1467, 6, 1153, 4, "color"], [1467, 11, 1153, 9], [1467, 13, 1153, 11], [1467, 19, 1153, 17], [1468, 6, 1154, 4, "marginBottom"], [1468, 18, 1154, 16], [1468, 20, 1154, 18], [1469, 4, 1155, 2], [1469, 5, 1155, 3], [1470, 4, 1156, 2, "shutterButton"], [1470, 17, 1156, 15], [1470, 19, 1156, 17], [1471, 6, 1157, 4, "width"], [1471, 11, 1157, 9], [1471, 13, 1157, 11], [1471, 15, 1157, 13], [1472, 6, 1158, 4, "height"], [1472, 12, 1158, 10], [1472, 14, 1158, 12], [1472, 16, 1158, 14], [1473, 6, 1159, 4, "borderRadius"], [1473, 18, 1159, 16], [1473, 20, 1159, 18], [1473, 22, 1159, 20], [1474, 6, 1160, 4, "backgroundColor"], [1474, 21, 1160, 19], [1474, 23, 1160, 21], [1474, 29, 1160, 27], [1475, 6, 1161, 4, "justifyContent"], [1475, 20, 1161, 18], [1475, 22, 1161, 20], [1475, 30, 1161, 28], [1476, 6, 1162, 4, "alignItems"], [1476, 16, 1162, 14], [1476, 18, 1162, 16], [1476, 26, 1162, 24], [1477, 6, 1163, 4, "marginBottom"], [1477, 18, 1163, 16], [1477, 20, 1163, 18], [1477, 22, 1163, 20], [1478, 6, 1164, 4], [1478, 9, 1164, 7, "Platform"], [1478, 26, 1164, 15], [1478, 27, 1164, 16, "select"], [1478, 33, 1164, 22], [1478, 34, 1164, 23], [1479, 8, 1165, 6, "ios"], [1479, 11, 1165, 9], [1479, 13, 1165, 11], [1480, 10, 1166, 8, "shadowColor"], [1480, 21, 1166, 19], [1480, 23, 1166, 21], [1480, 32, 1166, 30], [1481, 10, 1167, 8, "shadowOffset"], [1481, 22, 1167, 20], [1481, 24, 1167, 22], [1482, 12, 1167, 24, "width"], [1482, 17, 1167, 29], [1482, 19, 1167, 31], [1482, 20, 1167, 32], [1483, 12, 1167, 34, "height"], [1483, 18, 1167, 40], [1483, 20, 1167, 42], [1484, 10, 1167, 44], [1484, 11, 1167, 45], [1485, 10, 1168, 8, "shadowOpacity"], [1485, 23, 1168, 21], [1485, 25, 1168, 23], [1485, 28, 1168, 26], [1486, 10, 1169, 8, "shadowRadius"], [1486, 22, 1169, 20], [1486, 24, 1169, 22], [1487, 8, 1170, 6], [1487, 9, 1170, 7], [1488, 8, 1171, 6, "android"], [1488, 15, 1171, 13], [1488, 17, 1171, 15], [1489, 10, 1172, 8, "elevation"], [1489, 19, 1172, 17], [1489, 21, 1172, 19], [1490, 8, 1173, 6], [1490, 9, 1173, 7], [1491, 8, 1174, 6, "web"], [1491, 11, 1174, 9], [1491, 13, 1174, 11], [1492, 10, 1175, 8, "boxShadow"], [1492, 19, 1175, 17], [1492, 21, 1175, 19], [1493, 8, 1176, 6], [1494, 6, 1177, 4], [1494, 7, 1177, 5], [1495, 4, 1178, 2], [1495, 5, 1178, 3], [1496, 4, 1179, 2, "shutterButtonDisabled"], [1496, 25, 1179, 23], [1496, 27, 1179, 25], [1497, 6, 1180, 4, "opacity"], [1497, 13, 1180, 11], [1497, 15, 1180, 13], [1498, 4, 1181, 2], [1498, 5, 1181, 3], [1499, 4, 1182, 2, "shutterInner"], [1499, 16, 1182, 14], [1499, 18, 1182, 16], [1500, 6, 1183, 4, "width"], [1500, 11, 1183, 9], [1500, 13, 1183, 11], [1500, 15, 1183, 13], [1501, 6, 1184, 4, "height"], [1501, 12, 1184, 10], [1501, 14, 1184, 12], [1501, 16, 1184, 14], [1502, 6, 1185, 4, "borderRadius"], [1502, 18, 1185, 16], [1502, 20, 1185, 18], [1502, 22, 1185, 20], [1503, 6, 1186, 4, "backgroundColor"], [1503, 21, 1186, 19], [1503, 23, 1186, 21], [1503, 29, 1186, 27], [1504, 6, 1187, 4, "borderWidth"], [1504, 17, 1187, 15], [1504, 19, 1187, 17], [1504, 20, 1187, 18], [1505, 6, 1188, 4, "borderColor"], [1505, 17, 1188, 15], [1505, 19, 1188, 17], [1506, 4, 1189, 2], [1506, 5, 1189, 3], [1507, 4, 1190, 2, "privacyNote"], [1507, 15, 1190, 13], [1507, 17, 1190, 15], [1508, 6, 1191, 4, "fontSize"], [1508, 14, 1191, 12], [1508, 16, 1191, 14], [1508, 18, 1191, 16], [1509, 6, 1192, 4, "color"], [1509, 11, 1192, 9], [1509, 13, 1192, 11], [1510, 4, 1193, 2], [1510, 5, 1193, 3], [1511, 4, 1194, 2, "processingModal"], [1511, 19, 1194, 17], [1511, 21, 1194, 19], [1512, 6, 1195, 4, "flex"], [1512, 10, 1195, 8], [1512, 12, 1195, 10], [1512, 13, 1195, 11], [1513, 6, 1196, 4, "backgroundColor"], [1513, 21, 1196, 19], [1513, 23, 1196, 21], [1513, 43, 1196, 41], [1514, 6, 1197, 4, "justifyContent"], [1514, 20, 1197, 18], [1514, 22, 1197, 20], [1514, 30, 1197, 28], [1515, 6, 1198, 4, "alignItems"], [1515, 16, 1198, 14], [1515, 18, 1198, 16], [1516, 4, 1199, 2], [1516, 5, 1199, 3], [1517, 4, 1200, 2, "processingContent"], [1517, 21, 1200, 19], [1517, 23, 1200, 21], [1518, 6, 1201, 4, "backgroundColor"], [1518, 21, 1201, 19], [1518, 23, 1201, 21], [1518, 29, 1201, 27], [1519, 6, 1202, 4, "borderRadius"], [1519, 18, 1202, 16], [1519, 20, 1202, 18], [1519, 22, 1202, 20], [1520, 6, 1203, 4, "padding"], [1520, 13, 1203, 11], [1520, 15, 1203, 13], [1520, 17, 1203, 15], [1521, 6, 1204, 4, "width"], [1521, 11, 1204, 9], [1521, 13, 1204, 11], [1521, 18, 1204, 16], [1522, 6, 1205, 4, "max<PERSON><PERSON><PERSON>"], [1522, 14, 1205, 12], [1522, 16, 1205, 14], [1522, 19, 1205, 17], [1523, 6, 1206, 4, "alignItems"], [1523, 16, 1206, 14], [1523, 18, 1206, 16], [1524, 4, 1207, 2], [1524, 5, 1207, 3], [1525, 4, 1208, 2, "processingTitle"], [1525, 19, 1208, 17], [1525, 21, 1208, 19], [1526, 6, 1209, 4, "fontSize"], [1526, 14, 1209, 12], [1526, 16, 1209, 14], [1526, 18, 1209, 16], [1527, 6, 1210, 4, "fontWeight"], [1527, 16, 1210, 14], [1527, 18, 1210, 16], [1527, 23, 1210, 21], [1528, 6, 1211, 4, "color"], [1528, 11, 1211, 9], [1528, 13, 1211, 11], [1528, 22, 1211, 20], [1529, 6, 1212, 4, "marginTop"], [1529, 15, 1212, 13], [1529, 17, 1212, 15], [1529, 19, 1212, 17], [1530, 6, 1213, 4, "marginBottom"], [1530, 18, 1213, 16], [1530, 20, 1213, 18], [1531, 4, 1214, 2], [1531, 5, 1214, 3], [1532, 4, 1215, 2, "progressBar"], [1532, 15, 1215, 13], [1532, 17, 1215, 15], [1533, 6, 1216, 4, "width"], [1533, 11, 1216, 9], [1533, 13, 1216, 11], [1533, 19, 1216, 17], [1534, 6, 1217, 4, "height"], [1534, 12, 1217, 10], [1534, 14, 1217, 12], [1534, 15, 1217, 13], [1535, 6, 1218, 4, "backgroundColor"], [1535, 21, 1218, 19], [1535, 23, 1218, 21], [1535, 32, 1218, 30], [1536, 6, 1219, 4, "borderRadius"], [1536, 18, 1219, 16], [1536, 20, 1219, 18], [1536, 21, 1219, 19], [1537, 6, 1220, 4, "overflow"], [1537, 14, 1220, 12], [1537, 16, 1220, 14], [1537, 24, 1220, 22], [1538, 6, 1221, 4, "marginBottom"], [1538, 18, 1221, 16], [1538, 20, 1221, 18], [1539, 4, 1222, 2], [1539, 5, 1222, 3], [1540, 4, 1223, 2, "progressFill"], [1540, 16, 1223, 14], [1540, 18, 1223, 16], [1541, 6, 1224, 4, "height"], [1541, 12, 1224, 10], [1541, 14, 1224, 12], [1541, 20, 1224, 18], [1542, 6, 1225, 4, "backgroundColor"], [1542, 21, 1225, 19], [1542, 23, 1225, 21], [1542, 32, 1225, 30], [1543, 6, 1226, 4, "borderRadius"], [1543, 18, 1226, 16], [1543, 20, 1226, 18], [1544, 4, 1227, 2], [1544, 5, 1227, 3], [1545, 4, 1228, 2, "processingDescription"], [1545, 25, 1228, 23], [1545, 27, 1228, 25], [1546, 6, 1229, 4, "fontSize"], [1546, 14, 1229, 12], [1546, 16, 1229, 14], [1546, 18, 1229, 16], [1547, 6, 1230, 4, "color"], [1547, 11, 1230, 9], [1547, 13, 1230, 11], [1547, 22, 1230, 20], [1548, 6, 1231, 4, "textAlign"], [1548, 15, 1231, 13], [1548, 17, 1231, 15], [1549, 4, 1232, 2], [1549, 5, 1232, 3], [1550, 4, 1233, 2, "successIcon"], [1550, 15, 1233, 13], [1550, 17, 1233, 15], [1551, 6, 1234, 4, "marginTop"], [1551, 15, 1234, 13], [1551, 17, 1234, 15], [1552, 4, 1235, 2], [1552, 5, 1235, 3], [1553, 4, 1236, 2, "errorContent"], [1553, 16, 1236, 14], [1553, 18, 1236, 16], [1554, 6, 1237, 4, "backgroundColor"], [1554, 21, 1237, 19], [1554, 23, 1237, 21], [1554, 29, 1237, 27], [1555, 6, 1238, 4, "borderRadius"], [1555, 18, 1238, 16], [1555, 20, 1238, 18], [1555, 22, 1238, 20], [1556, 6, 1239, 4, "padding"], [1556, 13, 1239, 11], [1556, 15, 1239, 13], [1556, 17, 1239, 15], [1557, 6, 1240, 4, "width"], [1557, 11, 1240, 9], [1557, 13, 1240, 11], [1557, 18, 1240, 16], [1558, 6, 1241, 4, "max<PERSON><PERSON><PERSON>"], [1558, 14, 1241, 12], [1558, 16, 1241, 14], [1558, 19, 1241, 17], [1559, 6, 1242, 4, "alignItems"], [1559, 16, 1242, 14], [1559, 18, 1242, 16], [1560, 4, 1243, 2], [1560, 5, 1243, 3], [1561, 4, 1244, 2, "errorTitle"], [1561, 14, 1244, 12], [1561, 16, 1244, 14], [1562, 6, 1245, 4, "fontSize"], [1562, 14, 1245, 12], [1562, 16, 1245, 14], [1562, 18, 1245, 16], [1563, 6, 1246, 4, "fontWeight"], [1563, 16, 1246, 14], [1563, 18, 1246, 16], [1563, 23, 1246, 21], [1564, 6, 1247, 4, "color"], [1564, 11, 1247, 9], [1564, 13, 1247, 11], [1564, 22, 1247, 20], [1565, 6, 1248, 4, "marginTop"], [1565, 15, 1248, 13], [1565, 17, 1248, 15], [1565, 19, 1248, 17], [1566, 6, 1249, 4, "marginBottom"], [1566, 18, 1249, 16], [1566, 20, 1249, 18], [1567, 4, 1250, 2], [1567, 5, 1250, 3], [1568, 4, 1251, 2, "errorMessage"], [1568, 16, 1251, 14], [1568, 18, 1251, 16], [1569, 6, 1252, 4, "fontSize"], [1569, 14, 1252, 12], [1569, 16, 1252, 14], [1569, 18, 1252, 16], [1570, 6, 1253, 4, "color"], [1570, 11, 1253, 9], [1570, 13, 1253, 11], [1570, 22, 1253, 20], [1571, 6, 1254, 4, "textAlign"], [1571, 15, 1254, 13], [1571, 17, 1254, 15], [1571, 25, 1254, 23], [1572, 6, 1255, 4, "marginBottom"], [1572, 18, 1255, 16], [1572, 20, 1255, 18], [1573, 4, 1256, 2], [1573, 5, 1256, 3], [1574, 4, 1257, 2, "primaryButton"], [1574, 17, 1257, 15], [1574, 19, 1257, 17], [1575, 6, 1258, 4, "backgroundColor"], [1575, 21, 1258, 19], [1575, 23, 1258, 21], [1575, 32, 1258, 30], [1576, 6, 1259, 4, "paddingHorizontal"], [1576, 23, 1259, 21], [1576, 25, 1259, 23], [1576, 27, 1259, 25], [1577, 6, 1260, 4, "paddingVertical"], [1577, 21, 1260, 19], [1577, 23, 1260, 21], [1577, 25, 1260, 23], [1578, 6, 1261, 4, "borderRadius"], [1578, 18, 1261, 16], [1578, 20, 1261, 18], [1578, 21, 1261, 19], [1579, 6, 1262, 4, "marginTop"], [1579, 15, 1262, 13], [1579, 17, 1262, 15], [1580, 4, 1263, 2], [1580, 5, 1263, 3], [1581, 4, 1264, 2, "primaryButtonText"], [1581, 21, 1264, 19], [1581, 23, 1264, 21], [1582, 6, 1265, 4, "color"], [1582, 11, 1265, 9], [1582, 13, 1265, 11], [1582, 19, 1265, 17], [1583, 6, 1266, 4, "fontSize"], [1583, 14, 1266, 12], [1583, 16, 1266, 14], [1583, 18, 1266, 16], [1584, 6, 1267, 4, "fontWeight"], [1584, 16, 1267, 14], [1584, 18, 1267, 16], [1585, 4, 1268, 2], [1585, 5, 1268, 3], [1586, 4, 1269, 2, "secondaryButton"], [1586, 19, 1269, 17], [1586, 21, 1269, 19], [1587, 6, 1270, 4, "paddingHorizontal"], [1587, 23, 1270, 21], [1587, 25, 1270, 23], [1587, 27, 1270, 25], [1588, 6, 1271, 4, "paddingVertical"], [1588, 21, 1271, 19], [1588, 23, 1271, 21], [1588, 25, 1271, 23], [1589, 6, 1272, 4, "marginTop"], [1589, 15, 1272, 13], [1589, 17, 1272, 15], [1590, 4, 1273, 2], [1590, 5, 1273, 3], [1591, 4, 1274, 2, "secondaryButtonText"], [1591, 23, 1274, 21], [1591, 25, 1274, 23], [1592, 6, 1275, 4, "color"], [1592, 11, 1275, 9], [1592, 13, 1275, 11], [1592, 22, 1275, 20], [1593, 6, 1276, 4, "fontSize"], [1593, 14, 1276, 12], [1593, 16, 1276, 14], [1594, 4, 1277, 2], [1594, 5, 1277, 3], [1595, 4, 1278, 2, "permissionContent"], [1595, 21, 1278, 19], [1595, 23, 1278, 21], [1596, 6, 1279, 4, "flex"], [1596, 10, 1279, 8], [1596, 12, 1279, 10], [1596, 13, 1279, 11], [1597, 6, 1280, 4, "justifyContent"], [1597, 20, 1280, 18], [1597, 22, 1280, 20], [1597, 30, 1280, 28], [1598, 6, 1281, 4, "alignItems"], [1598, 16, 1281, 14], [1598, 18, 1281, 16], [1598, 26, 1281, 24], [1599, 6, 1282, 4, "padding"], [1599, 13, 1282, 11], [1599, 15, 1282, 13], [1600, 4, 1283, 2], [1600, 5, 1283, 3], [1601, 4, 1284, 2, "permissionTitle"], [1601, 19, 1284, 17], [1601, 21, 1284, 19], [1602, 6, 1285, 4, "fontSize"], [1602, 14, 1285, 12], [1602, 16, 1285, 14], [1602, 18, 1285, 16], [1603, 6, 1286, 4, "fontWeight"], [1603, 16, 1286, 14], [1603, 18, 1286, 16], [1603, 23, 1286, 21], [1604, 6, 1287, 4, "color"], [1604, 11, 1287, 9], [1604, 13, 1287, 11], [1604, 22, 1287, 20], [1605, 6, 1288, 4, "marginTop"], [1605, 15, 1288, 13], [1605, 17, 1288, 15], [1605, 19, 1288, 17], [1606, 6, 1289, 4, "marginBottom"], [1606, 18, 1289, 16], [1606, 20, 1289, 18], [1607, 4, 1290, 2], [1607, 5, 1290, 3], [1608, 4, 1291, 2, "permissionDescription"], [1608, 25, 1291, 23], [1608, 27, 1291, 25], [1609, 6, 1292, 4, "fontSize"], [1609, 14, 1292, 12], [1609, 16, 1292, 14], [1609, 18, 1292, 16], [1610, 6, 1293, 4, "color"], [1610, 11, 1293, 9], [1610, 13, 1293, 11], [1610, 22, 1293, 20], [1611, 6, 1294, 4, "textAlign"], [1611, 15, 1294, 13], [1611, 17, 1294, 15], [1611, 25, 1294, 23], [1612, 6, 1295, 4, "marginBottom"], [1612, 18, 1295, 16], [1612, 20, 1295, 18], [1613, 4, 1296, 2], [1613, 5, 1296, 3], [1614, 4, 1297, 2, "loadingText"], [1614, 15, 1297, 13], [1614, 17, 1297, 15], [1615, 6, 1298, 4, "color"], [1615, 11, 1298, 9], [1615, 13, 1298, 11], [1615, 22, 1298, 20], [1616, 6, 1299, 4, "marginTop"], [1616, 15, 1299, 13], [1616, 17, 1299, 15], [1617, 4, 1300, 2], [1617, 5, 1300, 3], [1618, 4, 1301, 2], [1619, 4, 1302, 2, "blurZone"], [1619, 12, 1302, 10], [1619, 14, 1302, 12], [1620, 6, 1303, 4, "position"], [1620, 14, 1303, 12], [1620, 16, 1303, 14], [1620, 26, 1303, 24], [1621, 6, 1304, 4, "overflow"], [1621, 14, 1304, 12], [1621, 16, 1304, 14], [1622, 4, 1305, 2], [1622, 5, 1305, 3], [1623, 4, 1306, 2, "previewChip"], [1623, 15, 1306, 13], [1623, 17, 1306, 15], [1624, 6, 1307, 4, "position"], [1624, 14, 1307, 12], [1624, 16, 1307, 14], [1624, 26, 1307, 24], [1625, 6, 1308, 4, "top"], [1625, 9, 1308, 7], [1625, 11, 1308, 9], [1625, 12, 1308, 10], [1626, 6, 1309, 4, "right"], [1626, 11, 1309, 9], [1626, 13, 1309, 11], [1626, 14, 1309, 12], [1627, 6, 1310, 4, "backgroundColor"], [1627, 21, 1310, 19], [1627, 23, 1310, 21], [1627, 40, 1310, 38], [1628, 6, 1311, 4, "paddingHorizontal"], [1628, 23, 1311, 21], [1628, 25, 1311, 23], [1628, 27, 1311, 25], [1629, 6, 1312, 4, "paddingVertical"], [1629, 21, 1312, 19], [1629, 23, 1312, 21], [1629, 24, 1312, 22], [1630, 6, 1313, 4, "borderRadius"], [1630, 18, 1313, 16], [1630, 20, 1313, 18], [1631, 4, 1314, 2], [1631, 5, 1314, 3], [1632, 4, 1315, 2, "previewChipText"], [1632, 19, 1315, 17], [1632, 21, 1315, 19], [1633, 6, 1316, 4, "color"], [1633, 11, 1316, 9], [1633, 13, 1316, 11], [1633, 19, 1316, 17], [1634, 6, 1317, 4, "fontSize"], [1634, 14, 1317, 12], [1634, 16, 1317, 14], [1634, 18, 1317, 16], [1635, 6, 1318, 4, "fontWeight"], [1635, 16, 1318, 14], [1635, 18, 1318, 16], [1636, 4, 1319, 2], [1637, 2, 1320, 0], [1637, 3, 1320, 1], [1637, 4, 1320, 2], [1638, 2, 1320, 3], [1638, 6, 1320, 3, "_c"], [1638, 8, 1320, 3], [1639, 2, 1320, 3, "$RefreshReg$"], [1639, 14, 1320, 3], [1639, 15, 1320, 3, "_c"], [1639, 17, 1320, 3], [1640, 0, 1320, 3], [1640, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;Gd6E;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}