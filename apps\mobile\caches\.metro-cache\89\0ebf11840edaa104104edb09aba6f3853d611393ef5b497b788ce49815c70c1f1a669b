{"dependencies": [{"name": "../../../skia/types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "hnxlDT1tba4gQfvf2h/i6nte9KM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.processPath = exports.isPathDef = void 0;\n  var _types = require(_dependencyMap[0], \"../../../skia/types\");\n  const _worklet_11568243541442_init_data = {\n    code: \"function PathJs1(Skia,rawPath){const path=typeof rawPath===\\\"string\\\"?Skia.Path.MakeFromSVGString(rawPath):rawPath;if(!path){throw new Error(\\\"Invalid path: \\\"+rawPath);}return path;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Path.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PathJs1\\\",\\\"Skia\\\",\\\"rawPath\\\",\\\"path\\\",\\\"Path\\\",\\\"MakeFromSVGString\\\",\\\"Error\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Path.js\\\"],\\\"mappings\\\":\\\"AAC2B,QAAC,CAAAA,OAAIA,CAAEC,IAAA,CAAAC,OAAY,EAG5C,KAAM,CAAAC,IAAI,CAAG,MAAO,CAAAD,OAAO,GAAK,QAAQ,CAAGD,IAAI,CAACG,IAAI,CAACC,iBAAiB,CAACH,OAAO,CAAC,CAAGA,OAAO,CACzF,GAAI,CAACC,IAAI,CAAE,CACT,KAAM,IAAI,CAAAG,KAAK,CAAC,gBAAgB,CAAGJ,OAAO,CAAC,CAC7C,CACA,MAAO,CAAAC,IAAI,CACb\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processPath = exports.processPath = function () {\n    const _e = [new global.Error(), 1, -27];\n    const PathJs1 = function (Skia, rawPath) {\n      const path = typeof rawPath === \"string\" ? Skia.Path.MakeFromSVGString(rawPath) : rawPath;\n      if (!path) {\n        throw new Error(\"Invalid path: \" + rawPath);\n      }\n      return path;\n    };\n    PathJs1.__closure = {};\n    PathJs1.__workletHash = 11568243541442;\n    PathJs1.__initData = _worklet_11568243541442_init_data;\n    PathJs1.__stackDetails = _e;\n    return PathJs1;\n  }();\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const _worklet_17588687239958_init_data = {\n    code: \"function PathJs2(def){const{isPath}=this.__closure;return typeof def===\\\"string\\\"||isPath(def);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Path.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PathJs2\\\",\\\"def\\\",\\\"isPath\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Path.js\\\"],\\\"mappings\\\":\\\"AAYyB,SAAAA,OAAOA,CAAAC,GAAA,QAAAC,MAAA,OAAAC,SAAA,CAG9B,MAAO,OAAO,CAAAF,GAAG,GAAK,QAAQ,EAAIC,MAAM,CAACD,GAAG,CAAC,CAC/C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const isPathDef = exports.isPathDef = function () {\n    const _e = [new global.Error(), -2, -27];\n    const PathJs2 = function (def) {\n      return typeof def === \"string\" || (0, _types.isPath)(def);\n    };\n    PathJs2.__closure = {\n      isPath: _types.isPath\n    };\n    PathJs2.__workletHash = 17588687239958;\n    PathJs2.__initData = _worklet_17588687239958_init_data;\n    PathJs2.__stackDetails = _e;\n    return PathJs2;\n  }();\n});", "lineCount": 49, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_types"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 1, 45], [7, 8, 1, 45, "_worklet_11568243541442_init_data"], [7, 41, 1, 45], [8, 4, 1, 45, "code"], [8, 8, 1, 45], [9, 4, 1, 45, "location"], [9, 12, 1, 45], [10, 4, 1, 45, "sourceMap"], [10, 13, 1, 45], [11, 4, 1, 45, "version"], [11, 11, 1, 45], [12, 2, 1, 45], [13, 2, 2, 7], [13, 8, 2, 13, "processPath"], [13, 19, 2, 24], [13, 22, 2, 24, "exports"], [13, 29, 2, 24], [13, 30, 2, 24, "processPath"], [13, 41, 2, 24], [13, 44, 2, 27], [14, 4, 2, 27], [14, 10, 2, 27, "_e"], [14, 12, 2, 27], [14, 20, 2, 27, "global"], [14, 26, 2, 27], [14, 27, 2, 27, "Error"], [14, 32, 2, 27], [15, 4, 2, 27], [15, 10, 2, 27, "PathJs1"], [15, 17, 2, 27], [15, 29, 2, 27, "PathJs1"], [15, 30, 2, 28, "Skia"], [15, 34, 2, 32], [15, 36, 2, 34, "rawPath"], [15, 43, 2, 41], [15, 45, 2, 46], [16, 6, 5, 2], [16, 12, 5, 8, "path"], [16, 16, 5, 12], [16, 19, 5, 15], [16, 26, 5, 22, "rawPath"], [16, 33, 5, 29], [16, 38, 5, 34], [16, 46, 5, 42], [16, 49, 5, 45, "Skia"], [16, 53, 5, 49], [16, 54, 5, 50, "Path"], [16, 58, 5, 54], [16, 59, 5, 55, "MakeFromSVGString"], [16, 76, 5, 72], [16, 77, 5, 73, "rawPath"], [16, 84, 5, 80], [16, 85, 5, 81], [16, 88, 5, 84, "rawPath"], [16, 95, 5, 91], [17, 6, 6, 2], [17, 10, 6, 6], [17, 11, 6, 7, "path"], [17, 15, 6, 11], [17, 17, 6, 13], [18, 8, 7, 4], [18, 14, 7, 10], [18, 18, 7, 14, "Error"], [18, 23, 7, 19], [18, 24, 7, 20], [18, 40, 7, 36], [18, 43, 7, 39, "rawPath"], [18, 50, 7, 46], [18, 51, 7, 47], [19, 6, 8, 2], [20, 6, 9, 2], [20, 13, 9, 9, "path"], [20, 17, 9, 13], [21, 4, 10, 0], [21, 5, 10, 1], [22, 4, 10, 1, "PathJs1"], [22, 11, 10, 1], [22, 12, 10, 1, "__closure"], [22, 21, 10, 1], [23, 4, 10, 1, "PathJs1"], [23, 11, 10, 1], [23, 12, 10, 1, "__workletHash"], [23, 25, 10, 1], [24, 4, 10, 1, "PathJs1"], [24, 11, 10, 1], [24, 12, 10, 1, "__initData"], [24, 22, 10, 1], [24, 25, 10, 1, "_worklet_11568243541442_init_data"], [24, 58, 10, 1], [25, 4, 10, 1, "PathJs1"], [25, 11, 10, 1], [25, 12, 10, 1, "__stackDetails"], [25, 26, 10, 1], [25, 29, 10, 1, "_e"], [25, 31, 10, 1], [26, 4, 10, 1], [26, 11, 10, 1, "PathJs1"], [26, 18, 10, 1], [27, 2, 10, 1], [27, 3, 2, 27], [27, 5, 10, 1], [29, 2, 12, 0], [30, 2, 12, 0], [30, 8, 12, 0, "_worklet_17588687239958_init_data"], [30, 41, 12, 0], [31, 4, 12, 0, "code"], [31, 8, 12, 0], [32, 4, 12, 0, "location"], [32, 12, 12, 0], [33, 4, 12, 0, "sourceMap"], [33, 13, 12, 0], [34, 4, 12, 0, "version"], [34, 11, 12, 0], [35, 2, 12, 0], [36, 2, 13, 7], [36, 8, 13, 13, "isPathDef"], [36, 17, 13, 22], [36, 20, 13, 22, "exports"], [36, 27, 13, 22], [36, 28, 13, 22, "isPathDef"], [36, 37, 13, 22], [36, 40, 13, 25], [37, 4, 13, 25], [37, 10, 13, 25, "_e"], [37, 12, 13, 25], [37, 20, 13, 25, "global"], [37, 26, 13, 25], [37, 27, 13, 25, "Error"], [37, 32, 13, 25], [38, 4, 13, 25], [38, 10, 13, 25, "PathJs2"], [38, 17, 13, 25], [38, 29, 13, 25, "PathJs2"], [38, 30, 13, 25, "def"], [38, 33, 13, 28], [38, 35, 13, 32], [39, 6, 16, 2], [39, 13, 16, 9], [39, 20, 16, 16, "def"], [39, 23, 16, 19], [39, 28, 16, 24], [39, 36, 16, 32], [39, 40, 16, 36], [39, 44, 16, 36, "isPath"], [39, 57, 16, 42], [39, 59, 16, 43, "def"], [39, 62, 16, 46], [39, 63, 16, 47], [40, 4, 17, 0], [40, 5, 17, 1], [41, 4, 17, 1, "PathJs2"], [41, 11, 17, 1], [41, 12, 17, 1, "__closure"], [41, 21, 17, 1], [42, 6, 17, 1, "isPath"], [42, 12, 17, 1], [42, 14, 16, 36, "isPath"], [43, 4, 16, 42], [44, 4, 16, 42, "PathJs2"], [44, 11, 16, 42], [44, 12, 16, 42, "__workletHash"], [44, 25, 16, 42], [45, 4, 16, 42, "PathJs2"], [45, 11, 16, 42], [45, 12, 16, 42, "__initData"], [45, 22, 16, 42], [45, 25, 16, 42, "_worklet_17588687239958_init_data"], [45, 58, 16, 42], [46, 4, 16, 42, "PathJs2"], [46, 11, 16, 42], [46, 12, 16, 42, "__stackDetails"], [46, 26, 16, 42], [46, 29, 16, 42, "_e"], [46, 31, 16, 42], [47, 4, 16, 42], [47, 11, 16, 42, "PathJs2"], [47, 18, 16, 42], [48, 2, 16, 42], [48, 3, 13, 25], [48, 5, 17, 1], [49, 0, 17, 2], [49, 3]], "functionMap": {"names": ["<global>", "processPath", "isPathDef"], "mappings": "AAA;2BCC;CDQ;yBEG;CFI"}}, "type": "js/module"}]}