{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = TensorFlowFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\TensorFlowFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  // TensorFlow.js BlazeFace implementation for reliable cross-browser face detection\n  function TensorFlowFaceCanvas({\n    containerId,\n    width,\n    height\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const debugCounterRef = (0, _react.useRef)(0);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [error, setError] = (0, _react.useState)(null);\n    (0, _react.useEffect)(() => {\n      console.log('[TensorFlowFaceCanvas] 🚀 STARTING INITIALIZATION...', {\n        containerId,\n        width,\n        height\n      });\n      console.log('[TensorFlowFaceCanvas] 🔧 Component mounted and running!');\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[TensorFlowFaceCanvas] Container not found:', containerId);\n        setError('Container not found');\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[TensorFlowFaceCanvas] Video element not found in container');\n        setError('Video element not found');\n        return;\n      }\n\n      // Resize canvas\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[TensorFlowFaceCanvas] Canvas ref not available');\n        setError('Canvas not available');\n        return;\n      }\n      canvas.width = width;\n      canvas.height = height;\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[TensorFlowFaceCanvas] Canvas context not available');\n        setError('Canvas context not available');\n        return;\n      }\n      let running = true;\n      debugCounterRef.current = 0;\n      const loadTensorFlowAndBlazeFace = async () => {\n        try {\n          console.log('[TensorFlowFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js');\n            console.log('[TensorFlowFaceCanvas] TensorFlow.js loaded');\n          }\n\n          // Load BlazeFace\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n            console.log('[TensorFlowFaceCanvas] BlazeFace loaded');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[TensorFlowFaceCanvas] Initializing BlazeFace model...');\n          const blazeface = window.blazeface;\n          modelRef.current = await blazeface.load();\n          console.log('[TensorFlowFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n          setError(null);\n        } catch (error) {\n          console.error('[TensorFlowFaceCanvas] ❌ Failed to load TensorFlow/BlazeFace:', error);\n          setError(`Failed to load face detection: ${error.message}`);\n          setIsLoading(false);\n        }\n      };\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.async = true;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load script: ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n      const applyFallbackBlur = (ctx, videoW, videoH, canvasW, canvasH) => {\n        // Apply blur to common face areas when no faces are detected\n        const scale = Math.max(canvasW / videoW, canvasH / videoH);\n        const scaledW = videoW * scale;\n        const scaledH = videoH * scale;\n        const offsetX = (canvasW - scaledW) / 2;\n        const offsetY = (canvasH - scaledH) / 2;\n\n        // Common face areas (center-upper region for selfies)\n        const faceAreas = [{\n          x: 0.25,\n          y: 0.15,\n          w: 0.5,\n          h: 0.5\n        },\n        // Center face\n        {\n          x: 0.1,\n          y: 0.2,\n          w: 0.35,\n          h: 0.4\n        },\n        // Left side\n        {\n          x: 0.55,\n          y: 0.2,\n          w: 0.35,\n          h: 0.4\n        } // Right side\n        ];\n        faceAreas.forEach(area => {\n          const x = area.x * scaledW + offsetX;\n          const y = area.y * scaledH + offsetY;\n          const w = area.w * scaledW;\n          const h = area.h * scaledH;\n          ctx.save();\n          ctx.beginPath();\n          ctx.ellipse(x + w / 2, y + h / 2, w / 2, h / 2, 0, 0, Math.PI * 2);\n          ctx.clip();\n          ctx.filter = 'blur(25px)';\n          ctx.drawImage(video, 0, 0, canvasW, canvasH);\n          ctx.restore();\n        });\n      };\n      const loop = async () => {\n        if (!running) return;\n        rafRef.current = requestAnimationFrame(loop);\n        if (!modelRef.current) {\n          // Log occasionally that model is not ready\n          debugCounterRef.current++;\n          if (debugCounterRef.current % 60 === 0) {\n            // Every ~1 second at 60fps\n            console.log('[TensorFlowFaceCanvas] Waiting for BlazeFace model to load...');\n          }\n          return;\n        }\n        try {\n          const videoW = video.videoWidth || width;\n          const videoH = video.videoHeight || height;\n\n          // Clear canvas\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n          // Log video status occasionally\n          debugCounterRef.current++;\n          if (debugCounterRef.current % 300 === 0) {\n            // Every ~5 seconds\n            console.log('[TensorFlowFaceCanvas] Video status:', {\n              dimensions: `${videoW}x${videoH}`,\n              readyState: video.readyState,\n              paused: video.paused,\n              currentTime: video.currentTime\n            });\n          }\n          if (videoW === 0 || videoH === 0) {\n            if (debugCounterRef.current % 60 === 0) {\n              console.log('[TensorFlowFaceCanvas] Video not ready - dimensions:', videoW, 'x', videoH);\n            }\n            return;\n          }\n\n          // Detect faces using BlazeFace\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6); // Lower threshold for better detection\n          tensor.dispose(); // Clean up tensor\n\n          // Debug logging for face detection\n          if (predictions.length > 0) {\n            console.log(`[TensorFlowFaceCanvas] 🎯 Detected ${predictions.length} face(s) at frame ${debugCounterRef.current}`);\n          } else if (debugCounterRef.current % 120 === 0) {\n            // Every ~2 seconds\n            console.log(`[TensorFlowFaceCanvas] No faces detected (frame ${debugCounterRef.current})`);\n          }\n          if (predictions.length === 0) {\n            // Apply fallback blur to common face areas\n            if (debugCounterRef.current > 60) {\n              // Only after model has had time to initialize\n              applyFallbackBlur(ctx, videoW, videoH, width, height);\n            }\n            return;\n          }\n\n          // Compute scale from video to canvas\n          const scale = Math.max(width / videoW, height / videoH);\n          const scaledW = videoW * scale;\n          const scaledH = videoH * scale;\n          const offsetX = (width - scaledW) / 2;\n          const offsetY = (height - scaledH) / 2;\n\n          // Draw blur for each detected face\n          predictions.forEach((prediction, index) => {\n            const [x1, y1] = prediction.topLeft;\n            const [x2, y2] = prediction.bottomRight;\n\n            // Expand the bounding box for better coverage\n            const centerX = (x1 + x2) / 2;\n            const centerY = (y1 + y2) / 2;\n            const faceWidth = (x2 - x1) * 1.4; // Expand by 40%\n            const faceHeight = (y2 - y1) * 1.6; // Expand by 60%\n\n            const expandedX1 = centerX - faceWidth / 2;\n            const expandedY1 = centerY - faceHeight / 2;\n\n            // Map to canvas coordinates\n            const canvasX = expandedX1 * scale + offsetX;\n            const canvasY = expandedY1 * scale + offsetY;\n            const canvasW = faceWidth * scale;\n            const canvasH = faceHeight * scale;\n\n            // Draw blurred oval patch\n            ctx.save();\n            ctx.beginPath();\n            ctx.ellipse(canvasX + canvasW / 2, canvasY + canvasH / 2, canvasW / 2, canvasH / 2, 0, 0, Math.PI * 2);\n            ctx.clip();\n            ctx.filter = 'blur(30px)';\n            ctx.drawImage(video, 0, 0, width, height);\n            ctx.restore();\n            if (debugCounterRef.current % 60 === 0) {\n              console.log(`[TensorFlowFaceCanvas] Blurred face ${index + 1} at (${Math.round(canvasX)}, ${Math.round(canvasY)})`);\n            }\n          });\n        } catch (error) {\n          // Log errors but keep the loop running\n          if (debugCounterRef.current % 300 === 0) {\n            console.error('[TensorFlowFaceCanvas] Detection error:', error);\n          }\n        }\n      };\n\n      // Start loading and then begin the loop\n      loadTensorFlowAndBlazeFace().then(() => {\n        if (running) {\n          rafRef.current = requestAnimationFrame(loop);\n        }\n      });\n      return () => {\n        running = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width,\n          height,\n          pointerEvents: 'none',\n          zIndex: 10\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(0,0,0,0.7)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: \"\\uD83D\\uDD04 Loading TensorFlow face detection...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), !isLoading && !error && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.8)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: \"\\uD83E\\uDD16 TensorFlow Face Detection Active\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(255,0,0,0.7)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: [\"Face detection error: \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(TensorFlowFaceCanvas, \"6DhjEcQmE6gVnJWZHklLharJPbw=\");\n  _c = TensorFlowFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"TensorFlowFaceCanvas\");\n});", "lineCount": 337, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 9, 0], [12, 2, 10, 15], [12, 11, 10, 24, "TensorFlowFaceCanvas"], [12, 31, 10, 44, "TensorFlowFaceCanvas"], [12, 32, 10, 45], [13, 4, 10, 47, "containerId"], [13, 15, 10, 58], [14, 4, 10, 60, "width"], [14, 9, 10, 65], [15, 4, 10, 67, "height"], [16, 2, 10, 101], [16, 3, 10, 102], [16, 5, 10, 104], [17, 4, 10, 104, "_s"], [17, 6, 10, 104], [18, 4, 11, 2], [18, 10, 11, 8, "canvasRef"], [18, 19, 11, 17], [18, 22, 11, 20], [18, 26, 11, 20, "useRef"], [18, 39, 11, 26], [18, 41, 11, 53], [18, 45, 11, 57], [18, 46, 11, 58], [19, 4, 12, 2], [19, 10, 12, 8, "rafRef"], [19, 16, 12, 14], [19, 19, 12, 17], [19, 23, 12, 17, "useRef"], [19, 36, 12, 23], [19, 38, 12, 39], [19, 42, 12, 43], [19, 43, 12, 44], [20, 4, 13, 2], [20, 10, 13, 8, "modelRef"], [20, 18, 13, 16], [20, 21, 13, 19], [20, 25, 13, 19, "useRef"], [20, 38, 13, 25], [20, 40, 13, 38], [20, 44, 13, 42], [20, 45, 13, 43], [21, 4, 14, 2], [21, 10, 14, 8, "debugCounterRef"], [21, 25, 14, 23], [21, 28, 14, 26], [21, 32, 14, 26, "useRef"], [21, 45, 14, 32], [21, 47, 14, 41], [21, 48, 14, 42], [21, 49, 14, 43], [22, 4, 15, 2], [22, 10, 15, 8], [22, 11, 15, 9, "isLoading"], [22, 20, 15, 18], [22, 22, 15, 20, "setIsLoading"], [22, 34, 15, 32], [22, 35, 15, 33], [22, 38, 15, 36], [22, 42, 15, 36, "useState"], [22, 57, 15, 44], [22, 59, 15, 45], [22, 63, 15, 49], [22, 64, 15, 50], [23, 4, 16, 2], [23, 10, 16, 8], [23, 11, 16, 9, "error"], [23, 16, 16, 14], [23, 18, 16, 16, "setError"], [23, 26, 16, 24], [23, 27, 16, 25], [23, 30, 16, 28], [23, 34, 16, 28, "useState"], [23, 49, 16, 36], [23, 51, 16, 52], [23, 55, 16, 56], [23, 56, 16, 57], [24, 4, 18, 2], [24, 8, 18, 2, "useEffect"], [24, 24, 18, 11], [24, 26, 18, 12], [24, 32, 18, 18], [25, 6, 19, 4, "console"], [25, 13, 19, 11], [25, 14, 19, 12, "log"], [25, 17, 19, 15], [25, 18, 19, 16], [25, 72, 19, 70], [25, 74, 19, 72], [26, 8, 19, 74, "containerId"], [26, 19, 19, 85], [27, 8, 19, 87, "width"], [27, 13, 19, 92], [28, 8, 19, 94, "height"], [29, 6, 19, 101], [29, 7, 19, 102], [29, 8, 19, 103], [30, 6, 20, 4, "console"], [30, 13, 20, 11], [30, 14, 20, 12, "log"], [30, 17, 20, 15], [30, 18, 20, 16], [30, 76, 20, 74], [30, 77, 20, 75], [31, 6, 22, 4], [31, 12, 22, 10, "container"], [31, 21, 22, 19], [31, 24, 22, 22, "document"], [31, 32, 22, 30], [31, 33, 22, 31, "getElementById"], [31, 47, 22, 45], [31, 48, 22, 46, "containerId"], [31, 59, 22, 57], [31, 60, 22, 58], [32, 6, 23, 4], [32, 10, 23, 8], [32, 11, 23, 9, "container"], [32, 20, 23, 18], [32, 22, 23, 20], [33, 8, 24, 6, "console"], [33, 15, 24, 13], [33, 16, 24, 14, "error"], [33, 21, 24, 19], [33, 22, 24, 20], [33, 67, 24, 65], [33, 69, 24, 67, "containerId"], [33, 80, 24, 78], [33, 81, 24, 79], [34, 8, 25, 6, "setError"], [34, 16, 25, 14], [34, 17, 25, 15], [34, 38, 25, 36], [34, 39, 25, 37], [35, 8, 26, 6], [36, 6, 27, 4], [37, 6, 29, 4], [37, 12, 29, 10, "video"], [37, 17, 29, 40], [37, 20, 29, 43, "container"], [37, 29, 29, 52], [37, 30, 29, 53, "querySelector"], [37, 43, 29, 66], [37, 44, 29, 67], [37, 51, 29, 74], [37, 52, 29, 75], [38, 6, 30, 4], [38, 10, 30, 8], [38, 11, 30, 9, "video"], [38, 16, 30, 14], [38, 18, 30, 16], [39, 8, 31, 6, "console"], [39, 15, 31, 13], [39, 16, 31, 14, "error"], [39, 21, 31, 19], [39, 22, 31, 20], [39, 83, 31, 81], [39, 84, 31, 82], [40, 8, 32, 6, "setError"], [40, 16, 32, 14], [40, 17, 32, 15], [40, 42, 32, 40], [40, 43, 32, 41], [41, 8, 33, 6], [42, 6, 34, 4], [44, 6, 36, 4], [45, 6, 37, 4], [45, 12, 37, 10, "canvas"], [45, 18, 37, 16], [45, 21, 37, 19, "canvasRef"], [45, 30, 37, 28], [45, 31, 37, 29, "current"], [45, 38, 37, 36], [46, 6, 38, 4], [46, 10, 38, 8], [46, 11, 38, 9, "canvas"], [46, 17, 38, 15], [46, 19, 38, 17], [47, 8, 39, 6, "console"], [47, 15, 39, 13], [47, 16, 39, 14, "error"], [47, 21, 39, 19], [47, 22, 39, 20], [47, 71, 39, 69], [47, 72, 39, 70], [48, 8, 40, 6, "setError"], [48, 16, 40, 14], [48, 17, 40, 15], [48, 39, 40, 37], [48, 40, 40, 38], [49, 8, 41, 6], [50, 6, 42, 4], [51, 6, 43, 4, "canvas"], [51, 12, 43, 10], [51, 13, 43, 11, "width"], [51, 18, 43, 16], [51, 21, 43, 19, "width"], [51, 26, 43, 24], [52, 6, 44, 4, "canvas"], [52, 12, 44, 10], [52, 13, 44, 11, "height"], [52, 19, 44, 17], [52, 22, 44, 20, "height"], [52, 28, 44, 26], [53, 6, 46, 4], [53, 12, 46, 10, "ctx"], [53, 15, 46, 13], [53, 18, 46, 16, "canvas"], [53, 24, 46, 22], [53, 25, 46, 23, "getContext"], [53, 35, 46, 33], [53, 36, 46, 34], [53, 40, 46, 38], [53, 41, 46, 39], [54, 6, 47, 4], [54, 10, 47, 8], [54, 11, 47, 9, "ctx"], [54, 14, 47, 12], [54, 16, 47, 14], [55, 8, 48, 6, "console"], [55, 15, 48, 13], [55, 16, 48, 14, "error"], [55, 21, 48, 19], [55, 22, 48, 20], [55, 75, 48, 73], [55, 76, 48, 74], [56, 8, 49, 6, "setError"], [56, 16, 49, 14], [56, 17, 49, 15], [56, 47, 49, 45], [56, 48, 49, 46], [57, 8, 50, 6], [58, 6, 51, 4], [59, 6, 53, 4], [59, 10, 53, 8, "running"], [59, 17, 53, 15], [59, 20, 53, 18], [59, 24, 53, 22], [60, 6, 54, 4, "debugCounterRef"], [60, 21, 54, 19], [60, 22, 54, 20, "current"], [60, 29, 54, 27], [60, 32, 54, 30], [60, 33, 54, 31], [61, 6, 56, 4], [61, 12, 56, 10, "loadTensorFlowAndBlazeFace"], [61, 38, 56, 36], [61, 41, 56, 39], [61, 47, 56, 39, "loadTensorFlowAndBlazeFace"], [61, 48, 56, 39], [61, 53, 56, 51], [62, 8, 57, 6], [62, 12, 57, 10], [63, 10, 58, 8, "console"], [63, 17, 58, 15], [63, 18, 58, 16, "log"], [63, 21, 58, 19], [63, 22, 58, 20], [63, 71, 58, 69], [63, 72, 58, 70], [65, 10, 60, 8], [66, 10, 61, 8], [66, 14, 61, 12], [66, 15, 61, 14, "window"], [66, 21, 61, 20], [66, 22, 61, 29, "tf"], [66, 24, 61, 31], [66, 26, 61, 33], [67, 12, 62, 10], [67, 18, 62, 16, "loadScript"], [67, 28, 62, 26], [67, 29, 62, 27], [67, 98, 62, 96], [67, 99, 62, 97], [68, 12, 63, 10, "console"], [68, 19, 63, 17], [68, 20, 63, 18, "log"], [68, 23, 63, 21], [68, 24, 63, 22], [68, 69, 63, 67], [68, 70, 63, 68], [69, 10, 64, 8], [71, 10, 66, 8], [72, 10, 67, 8], [72, 14, 67, 12], [72, 15, 67, 14, "window"], [72, 21, 67, 20], [72, 22, 67, 29, "blazeface"], [72, 31, 67, 38], [72, 33, 67, 40], [73, 12, 68, 10], [73, 18, 68, 16, "loadScript"], [73, 28, 68, 26], [73, 29, 68, 27], [73, 112, 68, 110], [73, 113, 68, 111], [74, 12, 69, 10, "console"], [74, 19, 69, 17], [74, 20, 69, 18, "log"], [74, 23, 69, 21], [74, 24, 69, 22], [74, 65, 69, 63], [74, 66, 69, 64], [75, 10, 70, 8], [77, 10, 72, 8], [78, 10, 73, 8, "console"], [78, 17, 73, 15], [78, 18, 73, 16, "log"], [78, 21, 73, 19], [78, 22, 73, 20], [78, 78, 73, 76], [78, 79, 73, 77], [79, 10, 74, 8], [79, 16, 74, 14, "blazeface"], [79, 25, 74, 23], [79, 28, 74, 27, "window"], [79, 34, 74, 33], [79, 35, 74, 42, "blazeface"], [79, 44, 74, 51], [80, 10, 75, 8, "modelRef"], [80, 18, 75, 16], [80, 19, 75, 17, "current"], [80, 26, 75, 24], [80, 29, 75, 27], [80, 35, 75, 33, "blazeface"], [80, 44, 75, 42], [80, 45, 75, 43, "load"], [80, 49, 75, 47], [80, 50, 75, 48], [80, 51, 75, 49], [81, 10, 76, 8, "console"], [81, 17, 76, 15], [81, 18, 76, 16, "log"], [81, 21, 76, 19], [81, 22, 76, 20], [81, 84, 76, 82], [81, 85, 76, 83], [82, 10, 78, 8, "setIsLoading"], [82, 22, 78, 20], [82, 23, 78, 21], [82, 28, 78, 26], [82, 29, 78, 27], [83, 10, 79, 8, "setError"], [83, 18, 79, 16], [83, 19, 79, 17], [83, 23, 79, 21], [83, 24, 79, 22], [84, 8, 81, 6], [84, 9, 81, 7], [84, 10, 81, 8], [84, 17, 81, 15, "error"], [84, 22, 81, 20], [84, 24, 81, 22], [85, 10, 82, 8, "console"], [85, 17, 82, 15], [85, 18, 82, 16, "error"], [85, 23, 82, 21], [85, 24, 82, 22], [85, 87, 82, 85], [85, 89, 82, 87, "error"], [85, 94, 82, 92], [85, 95, 82, 93], [86, 10, 83, 8, "setError"], [86, 18, 83, 16], [86, 19, 83, 17], [86, 53, 83, 51, "error"], [86, 58, 83, 56], [86, 59, 83, 57, "message"], [86, 66, 83, 64], [86, 68, 83, 66], [86, 69, 83, 67], [87, 10, 84, 8, "setIsLoading"], [87, 22, 84, 20], [87, 23, 84, 21], [87, 28, 84, 26], [87, 29, 84, 27], [88, 8, 85, 6], [89, 6, 86, 4], [89, 7, 86, 5], [90, 6, 88, 4], [90, 12, 88, 10, "loadScript"], [90, 22, 88, 20], [90, 25, 88, 24, "src"], [90, 28, 88, 35], [90, 32, 88, 55], [91, 8, 89, 6], [91, 15, 89, 13], [91, 19, 89, 17, "Promise"], [91, 26, 89, 24], [91, 27, 89, 25], [91, 28, 89, 26, "resolve"], [91, 35, 89, 33], [91, 37, 89, 35, "reject"], [91, 43, 89, 41], [91, 48, 89, 46], [92, 10, 90, 8], [92, 16, 90, 14, "script"], [92, 22, 90, 20], [92, 25, 90, 23, "document"], [92, 33, 90, 31], [92, 34, 90, 32, "createElement"], [92, 47, 90, 45], [92, 48, 90, 46], [92, 56, 90, 54], [92, 57, 90, 55], [93, 10, 91, 8, "script"], [93, 16, 91, 14], [93, 17, 91, 15, "src"], [93, 20, 91, 18], [93, 23, 91, 21, "src"], [93, 26, 91, 24], [94, 10, 92, 8, "script"], [94, 16, 92, 14], [94, 17, 92, 15, "async"], [94, 22, 92, 20], [94, 25, 92, 23], [94, 29, 92, 27], [95, 10, 93, 8, "script"], [95, 16, 93, 14], [95, 17, 93, 15, "onload"], [95, 23, 93, 21], [95, 26, 93, 24], [95, 32, 93, 30, "resolve"], [95, 39, 93, 37], [95, 40, 93, 38], [95, 41, 93, 39], [96, 10, 94, 8, "script"], [96, 16, 94, 14], [96, 17, 94, 15, "onerror"], [96, 24, 94, 22], [96, 27, 94, 25], [96, 33, 94, 31, "reject"], [96, 39, 94, 37], [96, 40, 94, 38], [96, 44, 94, 42, "Error"], [96, 49, 94, 47], [96, 50, 94, 48], [96, 76, 94, 74, "src"], [96, 79, 94, 77], [96, 81, 94, 79], [96, 82, 94, 80], [96, 83, 94, 81], [97, 10, 95, 8, "document"], [97, 18, 95, 16], [97, 19, 95, 17, "head"], [97, 23, 95, 21], [97, 24, 95, 22, "append<PERSON><PERSON><PERSON>"], [97, 35, 95, 33], [97, 36, 95, 34, "script"], [97, 42, 95, 40], [97, 43, 95, 41], [98, 8, 96, 6], [98, 9, 96, 7], [98, 10, 96, 8], [99, 6, 97, 4], [99, 7, 97, 5], [100, 6, 99, 4], [100, 12, 99, 10, "applyFallbackBlur"], [100, 29, 99, 27], [100, 32, 99, 30, "applyFallbackBlur"], [100, 33, 99, 31, "ctx"], [100, 36, 99, 60], [100, 38, 99, 62, "videoW"], [100, 44, 99, 76], [100, 46, 99, 78, "videoH"], [100, 52, 99, 92], [100, 54, 99, 94, "canvasW"], [100, 61, 99, 109], [100, 63, 99, 111, "canvasH"], [100, 70, 99, 126], [100, 75, 99, 131], [101, 8, 100, 6], [102, 8, 101, 6], [102, 14, 101, 12, "scale"], [102, 19, 101, 17], [102, 22, 101, 20, "Math"], [102, 26, 101, 24], [102, 27, 101, 25, "max"], [102, 30, 101, 28], [102, 31, 101, 29, "canvasW"], [102, 38, 101, 36], [102, 41, 101, 39, "videoW"], [102, 47, 101, 45], [102, 49, 101, 47, "canvasH"], [102, 56, 101, 54], [102, 59, 101, 57, "videoH"], [102, 65, 101, 63], [102, 66, 101, 64], [103, 8, 102, 6], [103, 14, 102, 12, "scaledW"], [103, 21, 102, 19], [103, 24, 102, 22, "videoW"], [103, 30, 102, 28], [103, 33, 102, 31, "scale"], [103, 38, 102, 36], [104, 8, 103, 6], [104, 14, 103, 12, "scaledH"], [104, 21, 103, 19], [104, 24, 103, 22, "videoH"], [104, 30, 103, 28], [104, 33, 103, 31, "scale"], [104, 38, 103, 36], [105, 8, 104, 6], [105, 14, 104, 12, "offsetX"], [105, 21, 104, 19], [105, 24, 104, 22], [105, 25, 104, 23, "canvasW"], [105, 32, 104, 30], [105, 35, 104, 33, "scaledW"], [105, 42, 104, 40], [105, 46, 104, 44], [105, 47, 104, 45], [106, 8, 105, 6], [106, 14, 105, 12, "offsetY"], [106, 21, 105, 19], [106, 24, 105, 22], [106, 25, 105, 23, "canvasH"], [106, 32, 105, 30], [106, 35, 105, 33, "scaledH"], [106, 42, 105, 40], [106, 46, 105, 44], [106, 47, 105, 45], [108, 8, 107, 6], [109, 8, 108, 6], [109, 14, 108, 12, "faceAreas"], [109, 23, 108, 21], [109, 26, 108, 24], [109, 27, 109, 8], [110, 10, 109, 10, "x"], [110, 11, 109, 11], [110, 13, 109, 13], [110, 17, 109, 17], [111, 10, 109, 19, "y"], [111, 11, 109, 20], [111, 13, 109, 22], [111, 17, 109, 26], [112, 10, 109, 28, "w"], [112, 11, 109, 29], [112, 13, 109, 31], [112, 16, 109, 34], [113, 10, 109, 36, "h"], [113, 11, 109, 37], [113, 13, 109, 39], [114, 8, 109, 43], [114, 9, 109, 44], [115, 8, 109, 46], [116, 8, 110, 8], [117, 10, 110, 10, "x"], [117, 11, 110, 11], [117, 13, 110, 13], [117, 16, 110, 16], [118, 10, 110, 18, "y"], [118, 11, 110, 19], [118, 13, 110, 21], [118, 16, 110, 24], [119, 10, 110, 26, "w"], [119, 11, 110, 27], [119, 13, 110, 29], [119, 17, 110, 33], [120, 10, 110, 35, "h"], [120, 11, 110, 36], [120, 13, 110, 38], [121, 8, 110, 42], [121, 9, 110, 43], [122, 8, 110, 46], [123, 8, 111, 8], [124, 10, 111, 10, "x"], [124, 11, 111, 11], [124, 13, 111, 13], [124, 17, 111, 17], [125, 10, 111, 19, "y"], [125, 11, 111, 20], [125, 13, 111, 22], [125, 16, 111, 25], [126, 10, 111, 27, "w"], [126, 11, 111, 28], [126, 13, 111, 30], [126, 17, 111, 34], [127, 10, 111, 36, "h"], [127, 11, 111, 37], [127, 13, 111, 39], [128, 8, 111, 43], [128, 9, 111, 44], [128, 10, 111, 46], [129, 8, 111, 46], [129, 9, 112, 7], [130, 8, 114, 6, "faceAreas"], [130, 17, 114, 15], [130, 18, 114, 16, "for<PERSON>ach"], [130, 25, 114, 23], [130, 26, 114, 24, "area"], [130, 30, 114, 28], [130, 34, 114, 32], [131, 10, 115, 8], [131, 16, 115, 14, "x"], [131, 17, 115, 15], [131, 20, 115, 18, "area"], [131, 24, 115, 22], [131, 25, 115, 23, "x"], [131, 26, 115, 24], [131, 29, 115, 27, "scaledW"], [131, 36, 115, 34], [131, 39, 115, 37, "offsetX"], [131, 46, 115, 44], [132, 10, 116, 8], [132, 16, 116, 14, "y"], [132, 17, 116, 15], [132, 20, 116, 18, "area"], [132, 24, 116, 22], [132, 25, 116, 23, "y"], [132, 26, 116, 24], [132, 29, 116, 27, "scaledH"], [132, 36, 116, 34], [132, 39, 116, 37, "offsetY"], [132, 46, 116, 44], [133, 10, 117, 8], [133, 16, 117, 14, "w"], [133, 17, 117, 15], [133, 20, 117, 18, "area"], [133, 24, 117, 22], [133, 25, 117, 23, "w"], [133, 26, 117, 24], [133, 29, 117, 27, "scaledW"], [133, 36, 117, 34], [134, 10, 118, 8], [134, 16, 118, 14, "h"], [134, 17, 118, 15], [134, 20, 118, 18, "area"], [134, 24, 118, 22], [134, 25, 118, 23, "h"], [134, 26, 118, 24], [134, 29, 118, 27, "scaledH"], [134, 36, 118, 34], [135, 10, 120, 8, "ctx"], [135, 13, 120, 11], [135, 14, 120, 12, "save"], [135, 18, 120, 16], [135, 19, 120, 17], [135, 20, 120, 18], [136, 10, 121, 8, "ctx"], [136, 13, 121, 11], [136, 14, 121, 12, "beginPath"], [136, 23, 121, 21], [136, 24, 121, 22], [136, 25, 121, 23], [137, 10, 122, 8, "ctx"], [137, 13, 122, 11], [137, 14, 122, 12, "ellipse"], [137, 21, 122, 19], [137, 22, 122, 20, "x"], [137, 23, 122, 21], [137, 26, 122, 24, "w"], [137, 27, 122, 25], [137, 30, 122, 28], [137, 31, 122, 29], [137, 33, 122, 31, "y"], [137, 34, 122, 32], [137, 37, 122, 35, "h"], [137, 38, 122, 36], [137, 41, 122, 39], [137, 42, 122, 40], [137, 44, 122, 42, "w"], [137, 45, 122, 43], [137, 48, 122, 46], [137, 49, 122, 47], [137, 51, 122, 49, "h"], [137, 52, 122, 50], [137, 55, 122, 53], [137, 56, 122, 54], [137, 58, 122, 56], [137, 59, 122, 57], [137, 61, 122, 59], [137, 62, 122, 60], [137, 64, 122, 62, "Math"], [137, 68, 122, 66], [137, 69, 122, 67, "PI"], [137, 71, 122, 69], [137, 74, 122, 72], [137, 75, 122, 73], [137, 76, 122, 74], [138, 10, 123, 8, "ctx"], [138, 13, 123, 11], [138, 14, 123, 12, "clip"], [138, 18, 123, 16], [138, 19, 123, 17], [138, 20, 123, 18], [139, 10, 124, 8, "ctx"], [139, 13, 124, 11], [139, 14, 124, 12, "filter"], [139, 20, 124, 18], [139, 23, 124, 21], [139, 35, 124, 33], [140, 10, 125, 8, "ctx"], [140, 13, 125, 11], [140, 14, 125, 12, "drawImage"], [140, 23, 125, 21], [140, 24, 125, 22, "video"], [140, 29, 125, 27], [140, 31, 125, 29], [140, 32, 125, 30], [140, 34, 125, 32], [140, 35, 125, 33], [140, 37, 125, 35, "canvasW"], [140, 44, 125, 42], [140, 46, 125, 44, "canvasH"], [140, 53, 125, 51], [140, 54, 125, 52], [141, 10, 126, 8, "ctx"], [141, 13, 126, 11], [141, 14, 126, 12, "restore"], [141, 21, 126, 19], [141, 22, 126, 20], [141, 23, 126, 21], [142, 8, 127, 6], [142, 9, 127, 7], [142, 10, 127, 8], [143, 6, 128, 4], [143, 7, 128, 5], [144, 6, 130, 4], [144, 12, 130, 10, "loop"], [144, 16, 130, 14], [144, 19, 130, 17], [144, 25, 130, 17, "loop"], [144, 26, 130, 17], [144, 31, 130, 29], [145, 8, 131, 6], [145, 12, 131, 10], [145, 13, 131, 11, "running"], [145, 20, 131, 18], [145, 22, 131, 20], [146, 8, 132, 6, "rafRef"], [146, 14, 132, 12], [146, 15, 132, 13, "current"], [146, 22, 132, 20], [146, 25, 132, 23, "requestAnimationFrame"], [146, 46, 132, 44], [146, 47, 132, 45, "loop"], [146, 51, 132, 49], [146, 52, 132, 50], [147, 8, 134, 6], [147, 12, 134, 10], [147, 13, 134, 11, "modelRef"], [147, 21, 134, 19], [147, 22, 134, 20, "current"], [147, 29, 134, 27], [147, 31, 134, 29], [148, 10, 135, 8], [149, 10, 136, 8, "debugCounterRef"], [149, 25, 136, 23], [149, 26, 136, 24, "current"], [149, 33, 136, 31], [149, 35, 136, 33], [150, 10, 137, 8], [150, 14, 137, 12, "debugCounterRef"], [150, 29, 137, 27], [150, 30, 137, 28, "current"], [150, 37, 137, 35], [150, 40, 137, 38], [150, 42, 137, 40], [150, 47, 137, 45], [150, 48, 137, 46], [150, 50, 137, 48], [151, 12, 137, 50], [152, 12, 138, 10, "console"], [152, 19, 138, 17], [152, 20, 138, 18, "log"], [152, 23, 138, 21], [152, 24, 138, 22], [152, 87, 138, 85], [152, 88, 138, 86], [153, 10, 139, 8], [154, 10, 140, 8], [155, 8, 141, 6], [156, 8, 143, 6], [156, 12, 143, 10], [157, 10, 144, 8], [157, 16, 144, 14, "videoW"], [157, 22, 144, 20], [157, 25, 144, 23, "video"], [157, 30, 144, 28], [157, 31, 144, 29, "videoWidth"], [157, 41, 144, 39], [157, 45, 144, 43, "width"], [157, 50, 144, 48], [158, 10, 145, 8], [158, 16, 145, 14, "videoH"], [158, 22, 145, 20], [158, 25, 145, 23, "video"], [158, 30, 145, 28], [158, 31, 145, 29, "videoHeight"], [158, 42, 145, 40], [158, 46, 145, 44, "height"], [158, 52, 145, 50], [160, 10, 147, 8], [161, 10, 148, 8, "ctx"], [161, 13, 148, 11], [161, 14, 148, 12, "clearRect"], [161, 23, 148, 21], [161, 24, 148, 22], [161, 25, 148, 23], [161, 27, 148, 25], [161, 28, 148, 26], [161, 30, 148, 28, "canvas"], [161, 36, 148, 34], [161, 37, 148, 35, "width"], [161, 42, 148, 40], [161, 44, 148, 42, "canvas"], [161, 50, 148, 48], [161, 51, 148, 49, "height"], [161, 57, 148, 55], [161, 58, 148, 56], [163, 10, 150, 8], [164, 10, 151, 8, "debugCounterRef"], [164, 25, 151, 23], [164, 26, 151, 24, "current"], [164, 33, 151, 31], [164, 35, 151, 33], [165, 10, 152, 8], [165, 14, 152, 12, "debugCounterRef"], [165, 29, 152, 27], [165, 30, 152, 28, "current"], [165, 37, 152, 35], [165, 40, 152, 38], [165, 43, 152, 41], [165, 48, 152, 46], [165, 49, 152, 47], [165, 51, 152, 49], [166, 12, 152, 51], [167, 12, 153, 10, "console"], [167, 19, 153, 17], [167, 20, 153, 18, "log"], [167, 23, 153, 21], [167, 24, 153, 22], [167, 62, 153, 60], [167, 64, 153, 62], [168, 14, 154, 12, "dimensions"], [168, 24, 154, 22], [168, 26, 154, 24], [168, 29, 154, 27, "videoW"], [168, 35, 154, 33], [168, 39, 154, 37, "videoH"], [168, 45, 154, 43], [168, 47, 154, 45], [169, 14, 155, 12, "readyState"], [169, 24, 155, 22], [169, 26, 155, 24, "video"], [169, 31, 155, 29], [169, 32, 155, 30, "readyState"], [169, 42, 155, 40], [170, 14, 156, 12, "paused"], [170, 20, 156, 18], [170, 22, 156, 20, "video"], [170, 27, 156, 25], [170, 28, 156, 26, "paused"], [170, 34, 156, 32], [171, 14, 157, 12, "currentTime"], [171, 25, 157, 23], [171, 27, 157, 25, "video"], [171, 32, 157, 30], [171, 33, 157, 31, "currentTime"], [172, 12, 158, 10], [172, 13, 158, 11], [172, 14, 158, 12], [173, 10, 159, 8], [174, 10, 161, 8], [174, 14, 161, 12, "videoW"], [174, 20, 161, 18], [174, 25, 161, 23], [174, 26, 161, 24], [174, 30, 161, 28, "videoH"], [174, 36, 161, 34], [174, 41, 161, 39], [174, 42, 161, 40], [174, 44, 161, 42], [175, 12, 162, 10], [175, 16, 162, 14, "debugCounterRef"], [175, 31, 162, 29], [175, 32, 162, 30, "current"], [175, 39, 162, 37], [175, 42, 162, 40], [175, 44, 162, 42], [175, 49, 162, 47], [175, 50, 162, 48], [175, 52, 162, 50], [176, 14, 163, 12, "console"], [176, 21, 163, 19], [176, 22, 163, 20, "log"], [176, 25, 163, 23], [176, 26, 163, 24], [176, 80, 163, 78], [176, 82, 163, 80, "videoW"], [176, 88, 163, 86], [176, 90, 163, 88], [176, 93, 163, 91], [176, 95, 163, 93, "videoH"], [176, 101, 163, 99], [176, 102, 163, 100], [177, 12, 164, 10], [178, 12, 165, 10], [179, 10, 166, 8], [181, 10, 168, 8], [182, 10, 169, 8], [182, 16, 169, 14, "tf"], [182, 18, 169, 16], [182, 21, 169, 20, "window"], [182, 27, 169, 26], [182, 28, 169, 35, "tf"], [182, 30, 169, 37], [183, 10, 170, 8], [183, 16, 170, 14, "tensor"], [183, 22, 170, 20], [183, 25, 170, 23, "tf"], [183, 27, 170, 25], [183, 28, 170, 26, "browser"], [183, 35, 170, 33], [183, 36, 170, 34, "fromPixels"], [183, 46, 170, 44], [183, 47, 170, 45, "video"], [183, 52, 170, 50], [183, 53, 170, 51], [184, 10, 171, 8], [184, 16, 171, 14, "predictions"], [184, 27, 171, 25], [184, 30, 171, 28], [184, 36, 171, 34, "modelRef"], [184, 44, 171, 42], [184, 45, 171, 43, "current"], [184, 52, 171, 50], [184, 53, 171, 51, "estimateFaces"], [184, 66, 171, 64], [184, 67, 171, 65, "tensor"], [184, 73, 171, 71], [184, 75, 171, 73], [184, 80, 171, 78], [184, 82, 171, 80], [184, 85, 171, 83], [184, 86, 171, 84], [184, 87, 171, 85], [184, 88, 171, 86], [185, 10, 172, 8, "tensor"], [185, 16, 172, 14], [185, 17, 172, 15, "dispose"], [185, 24, 172, 22], [185, 25, 172, 23], [185, 26, 172, 24], [185, 27, 172, 25], [185, 28, 172, 26], [187, 10, 174, 8], [188, 10, 175, 8], [188, 14, 175, 12, "predictions"], [188, 25, 175, 23], [188, 26, 175, 24, "length"], [188, 32, 175, 30], [188, 35, 175, 33], [188, 36, 175, 34], [188, 38, 175, 36], [189, 12, 176, 10, "console"], [189, 19, 176, 17], [189, 20, 176, 18, "log"], [189, 23, 176, 21], [189, 24, 176, 22], [189, 62, 176, 60, "predictions"], [189, 73, 176, 71], [189, 74, 176, 72, "length"], [189, 80, 176, 78], [189, 101, 176, 99, "debugCounterRef"], [189, 116, 176, 114], [189, 117, 176, 115, "current"], [189, 124, 176, 122], [189, 126, 176, 124], [189, 127, 176, 125], [190, 10, 177, 8], [190, 11, 177, 9], [190, 17, 177, 15], [190, 21, 177, 19, "debugCounterRef"], [190, 36, 177, 34], [190, 37, 177, 35, "current"], [190, 44, 177, 42], [190, 47, 177, 45], [190, 50, 177, 48], [190, 55, 177, 53], [190, 56, 177, 54], [190, 58, 177, 56], [191, 12, 177, 58], [192, 12, 178, 10, "console"], [192, 19, 178, 17], [192, 20, 178, 18, "log"], [192, 23, 178, 21], [192, 24, 178, 22], [192, 75, 178, 73, "debugCounterRef"], [192, 90, 178, 88], [192, 91, 178, 89, "current"], [192, 98, 178, 96], [192, 101, 178, 99], [192, 102, 178, 100], [193, 10, 179, 8], [194, 10, 181, 8], [194, 14, 181, 12, "predictions"], [194, 25, 181, 23], [194, 26, 181, 24, "length"], [194, 32, 181, 30], [194, 37, 181, 35], [194, 38, 181, 36], [194, 40, 181, 38], [195, 12, 182, 10], [196, 12, 183, 10], [196, 16, 183, 14, "debugCounterRef"], [196, 31, 183, 29], [196, 32, 183, 30, "current"], [196, 39, 183, 37], [196, 42, 183, 40], [196, 44, 183, 42], [196, 46, 183, 44], [197, 14, 183, 46], [198, 14, 184, 12, "applyFallbackBlur"], [198, 31, 184, 29], [198, 32, 184, 30, "ctx"], [198, 35, 184, 33], [198, 37, 184, 35, "videoW"], [198, 43, 184, 41], [198, 45, 184, 43, "videoH"], [198, 51, 184, 49], [198, 53, 184, 51, "width"], [198, 58, 184, 56], [198, 60, 184, 58, "height"], [198, 66, 184, 64], [198, 67, 184, 65], [199, 12, 185, 10], [200, 12, 186, 10], [201, 10, 187, 8], [203, 10, 189, 8], [204, 10, 190, 8], [204, 16, 190, 14, "scale"], [204, 21, 190, 19], [204, 24, 190, 22, "Math"], [204, 28, 190, 26], [204, 29, 190, 27, "max"], [204, 32, 190, 30], [204, 33, 190, 31, "width"], [204, 38, 190, 36], [204, 41, 190, 39, "videoW"], [204, 47, 190, 45], [204, 49, 190, 47, "height"], [204, 55, 190, 53], [204, 58, 190, 56, "videoH"], [204, 64, 190, 62], [204, 65, 190, 63], [205, 10, 191, 8], [205, 16, 191, 14, "scaledW"], [205, 23, 191, 21], [205, 26, 191, 24, "videoW"], [205, 32, 191, 30], [205, 35, 191, 33, "scale"], [205, 40, 191, 38], [206, 10, 192, 8], [206, 16, 192, 14, "scaledH"], [206, 23, 192, 21], [206, 26, 192, 24, "videoH"], [206, 32, 192, 30], [206, 35, 192, 33, "scale"], [206, 40, 192, 38], [207, 10, 193, 8], [207, 16, 193, 14, "offsetX"], [207, 23, 193, 21], [207, 26, 193, 24], [207, 27, 193, 25, "width"], [207, 32, 193, 30], [207, 35, 193, 33, "scaledW"], [207, 42, 193, 40], [207, 46, 193, 44], [207, 47, 193, 45], [208, 10, 194, 8], [208, 16, 194, 14, "offsetY"], [208, 23, 194, 21], [208, 26, 194, 24], [208, 27, 194, 25, "height"], [208, 33, 194, 31], [208, 36, 194, 34, "scaledH"], [208, 43, 194, 41], [208, 47, 194, 45], [208, 48, 194, 46], [210, 10, 196, 8], [211, 10, 197, 8, "predictions"], [211, 21, 197, 19], [211, 22, 197, 20, "for<PERSON>ach"], [211, 29, 197, 27], [211, 30, 197, 28], [211, 31, 197, 29, "prediction"], [211, 41, 197, 44], [211, 43, 197, 46, "index"], [211, 48, 197, 59], [211, 53, 197, 64], [212, 12, 198, 10], [212, 18, 198, 16], [212, 19, 198, 17, "x1"], [212, 21, 198, 19], [212, 23, 198, 21, "y1"], [212, 25, 198, 23], [212, 26, 198, 24], [212, 29, 198, 27, "prediction"], [212, 39, 198, 37], [212, 40, 198, 38, "topLeft"], [212, 47, 198, 45], [213, 12, 199, 10], [213, 18, 199, 16], [213, 19, 199, 17, "x2"], [213, 21, 199, 19], [213, 23, 199, 21, "y2"], [213, 25, 199, 23], [213, 26, 199, 24], [213, 29, 199, 27, "prediction"], [213, 39, 199, 37], [213, 40, 199, 38, "bottomRight"], [213, 51, 199, 49], [215, 12, 201, 10], [216, 12, 202, 10], [216, 18, 202, 16, "centerX"], [216, 25, 202, 23], [216, 28, 202, 26], [216, 29, 202, 27, "x1"], [216, 31, 202, 29], [216, 34, 202, 32, "x2"], [216, 36, 202, 34], [216, 40, 202, 38], [216, 41, 202, 39], [217, 12, 203, 10], [217, 18, 203, 16, "centerY"], [217, 25, 203, 23], [217, 28, 203, 26], [217, 29, 203, 27, "y1"], [217, 31, 203, 29], [217, 34, 203, 32, "y2"], [217, 36, 203, 34], [217, 40, 203, 38], [217, 41, 203, 39], [218, 12, 204, 10], [218, 18, 204, 16, "faceWidth"], [218, 27, 204, 25], [218, 30, 204, 28], [218, 31, 204, 29, "x2"], [218, 33, 204, 31], [218, 36, 204, 34, "x1"], [218, 38, 204, 36], [218, 42, 204, 40], [218, 45, 204, 43], [218, 46, 204, 44], [218, 47, 204, 45], [219, 12, 205, 10], [219, 18, 205, 16, "faceHeight"], [219, 28, 205, 26], [219, 31, 205, 29], [219, 32, 205, 30, "y2"], [219, 34, 205, 32], [219, 37, 205, 35, "y1"], [219, 39, 205, 37], [219, 43, 205, 41], [219, 46, 205, 44], [219, 47, 205, 45], [219, 48, 205, 46], [221, 12, 207, 10], [221, 18, 207, 16, "expandedX1"], [221, 28, 207, 26], [221, 31, 207, 29, "centerX"], [221, 38, 207, 36], [221, 41, 207, 39, "faceWidth"], [221, 50, 207, 48], [221, 53, 207, 51], [221, 54, 207, 52], [222, 12, 208, 10], [222, 18, 208, 16, "expandedY1"], [222, 28, 208, 26], [222, 31, 208, 29, "centerY"], [222, 38, 208, 36], [222, 41, 208, 39, "faceHeight"], [222, 51, 208, 49], [222, 54, 208, 52], [222, 55, 208, 53], [224, 12, 210, 10], [225, 12, 211, 10], [225, 18, 211, 16, "canvasX"], [225, 25, 211, 23], [225, 28, 211, 26, "expandedX1"], [225, 38, 211, 36], [225, 41, 211, 39, "scale"], [225, 46, 211, 44], [225, 49, 211, 47, "offsetX"], [225, 56, 211, 54], [226, 12, 212, 10], [226, 18, 212, 16, "canvasY"], [226, 25, 212, 23], [226, 28, 212, 26, "expandedY1"], [226, 38, 212, 36], [226, 41, 212, 39, "scale"], [226, 46, 212, 44], [226, 49, 212, 47, "offsetY"], [226, 56, 212, 54], [227, 12, 213, 10], [227, 18, 213, 16, "canvasW"], [227, 25, 213, 23], [227, 28, 213, 26, "faceWidth"], [227, 37, 213, 35], [227, 40, 213, 38, "scale"], [227, 45, 213, 43], [228, 12, 214, 10], [228, 18, 214, 16, "canvasH"], [228, 25, 214, 23], [228, 28, 214, 26, "faceHeight"], [228, 38, 214, 36], [228, 41, 214, 39, "scale"], [228, 46, 214, 44], [230, 12, 216, 10], [231, 12, 217, 10, "ctx"], [231, 15, 217, 13], [231, 16, 217, 14, "save"], [231, 20, 217, 18], [231, 21, 217, 19], [231, 22, 217, 20], [232, 12, 218, 10, "ctx"], [232, 15, 218, 13], [232, 16, 218, 14, "beginPath"], [232, 25, 218, 23], [232, 26, 218, 24], [232, 27, 218, 25], [233, 12, 219, 10, "ctx"], [233, 15, 219, 13], [233, 16, 219, 14, "ellipse"], [233, 23, 219, 21], [233, 24, 219, 22, "canvasX"], [233, 31, 219, 29], [233, 34, 219, 32, "canvasW"], [233, 41, 219, 39], [233, 44, 219, 42], [233, 45, 219, 43], [233, 47, 219, 45, "canvasY"], [233, 54, 219, 52], [233, 57, 219, 55, "canvasH"], [233, 64, 219, 62], [233, 67, 219, 65], [233, 68, 219, 66], [233, 70, 219, 68, "canvasW"], [233, 77, 219, 75], [233, 80, 219, 78], [233, 81, 219, 79], [233, 83, 219, 81, "canvasH"], [233, 90, 219, 88], [233, 93, 219, 91], [233, 94, 219, 92], [233, 96, 219, 94], [233, 97, 219, 95], [233, 99, 219, 97], [233, 100, 219, 98], [233, 102, 219, 100, "Math"], [233, 106, 219, 104], [233, 107, 219, 105, "PI"], [233, 109, 219, 107], [233, 112, 219, 110], [233, 113, 219, 111], [233, 114, 219, 112], [234, 12, 220, 10, "ctx"], [234, 15, 220, 13], [234, 16, 220, 14, "clip"], [234, 20, 220, 18], [234, 21, 220, 19], [234, 22, 220, 20], [235, 12, 221, 10, "ctx"], [235, 15, 221, 13], [235, 16, 221, 14, "filter"], [235, 22, 221, 20], [235, 25, 221, 23], [235, 37, 221, 35], [236, 12, 222, 10, "ctx"], [236, 15, 222, 13], [236, 16, 222, 14, "drawImage"], [236, 25, 222, 23], [236, 26, 222, 24, "video"], [236, 31, 222, 29], [236, 33, 222, 31], [236, 34, 222, 32], [236, 36, 222, 34], [236, 37, 222, 35], [236, 39, 222, 37, "width"], [236, 44, 222, 42], [236, 46, 222, 44, "height"], [236, 52, 222, 50], [236, 53, 222, 51], [237, 12, 223, 10, "ctx"], [237, 15, 223, 13], [237, 16, 223, 14, "restore"], [237, 23, 223, 21], [237, 24, 223, 22], [237, 25, 223, 23], [238, 12, 225, 10], [238, 16, 225, 14, "debugCounterRef"], [238, 31, 225, 29], [238, 32, 225, 30, "current"], [238, 39, 225, 37], [238, 42, 225, 40], [238, 44, 225, 42], [238, 49, 225, 47], [238, 50, 225, 48], [238, 52, 225, 50], [239, 14, 226, 12, "console"], [239, 21, 226, 19], [239, 22, 226, 20, "log"], [239, 25, 226, 23], [239, 26, 226, 24], [239, 65, 226, 63, "index"], [239, 70, 226, 68], [239, 73, 226, 71], [239, 74, 226, 72], [239, 82, 226, 80, "Math"], [239, 86, 226, 84], [239, 87, 226, 85, "round"], [239, 92, 226, 90], [239, 93, 226, 91, "canvasX"], [239, 100, 226, 98], [239, 101, 226, 99], [239, 106, 226, 104, "Math"], [239, 110, 226, 108], [239, 111, 226, 109, "round"], [239, 116, 226, 114], [239, 117, 226, 115, "canvasY"], [239, 124, 226, 122], [239, 125, 226, 123], [239, 128, 226, 126], [239, 129, 226, 127], [240, 12, 227, 10], [241, 10, 228, 8], [241, 11, 228, 9], [241, 12, 228, 10], [242, 8, 230, 6], [242, 9, 230, 7], [242, 10, 230, 8], [242, 17, 230, 15, "error"], [242, 22, 230, 20], [242, 24, 230, 22], [243, 10, 231, 8], [244, 10, 232, 8], [244, 14, 232, 12, "debugCounterRef"], [244, 29, 232, 27], [244, 30, 232, 28, "current"], [244, 37, 232, 35], [244, 40, 232, 38], [244, 43, 232, 41], [244, 48, 232, 46], [244, 49, 232, 47], [244, 51, 232, 49], [245, 12, 233, 10, "console"], [245, 19, 233, 17], [245, 20, 233, 18, "error"], [245, 25, 233, 23], [245, 26, 233, 24], [245, 67, 233, 65], [245, 69, 233, 67, "error"], [245, 74, 233, 72], [245, 75, 233, 73], [246, 10, 234, 8], [247, 8, 235, 6], [248, 6, 236, 4], [248, 7, 236, 5], [250, 6, 238, 4], [251, 6, 239, 4, "loadTensorFlowAndBlazeFace"], [251, 32, 239, 30], [251, 33, 239, 31], [251, 34, 239, 32], [251, 35, 239, 33, "then"], [251, 39, 239, 37], [251, 40, 239, 38], [251, 46, 239, 44], [252, 8, 240, 6], [252, 12, 240, 10, "running"], [252, 19, 240, 17], [252, 21, 240, 19], [253, 10, 241, 8, "rafRef"], [253, 16, 241, 14], [253, 17, 241, 15, "current"], [253, 24, 241, 22], [253, 27, 241, 25, "requestAnimationFrame"], [253, 48, 241, 46], [253, 49, 241, 47, "loop"], [253, 53, 241, 51], [253, 54, 241, 52], [254, 8, 242, 6], [255, 6, 243, 4], [255, 7, 243, 5], [255, 8, 243, 6], [256, 6, 245, 4], [256, 13, 245, 11], [256, 19, 245, 17], [257, 8, 246, 6, "running"], [257, 15, 246, 13], [257, 18, 246, 16], [257, 23, 246, 21], [258, 8, 247, 6], [258, 12, 247, 10, "rafRef"], [258, 18, 247, 16], [258, 19, 247, 17, "current"], [258, 26, 247, 24], [258, 28, 247, 26], [259, 10, 248, 8, "cancelAnimationFrame"], [259, 30, 248, 28], [259, 31, 248, 29, "rafRef"], [259, 37, 248, 35], [259, 38, 248, 36, "current"], [259, 45, 248, 43], [259, 46, 248, 44], [260, 8, 249, 6], [261, 6, 250, 4], [261, 7, 250, 5], [262, 4, 251, 2], [262, 5, 251, 3], [262, 7, 251, 5], [262, 8, 251, 6, "containerId"], [262, 19, 251, 17], [262, 21, 251, 19, "width"], [262, 26, 251, 24], [262, 28, 251, 26, "height"], [262, 34, 251, 32], [262, 35, 251, 33], [262, 36, 251, 34], [263, 4, 253, 2], [263, 24, 254, 4], [263, 28, 254, 4, "_jsxDevRuntime"], [263, 42, 254, 4], [263, 43, 254, 4, "jsxDEV"], [263, 49, 254, 4], [263, 51, 254, 4, "_jsxDevRuntime"], [263, 65, 254, 4], [263, 66, 254, 4, "Fragment"], [263, 74, 254, 4], [264, 6, 254, 4, "children"], [264, 14, 254, 4], [264, 30, 255, 6], [264, 34, 255, 6, "_jsxDevRuntime"], [264, 48, 255, 6], [264, 49, 255, 6, "jsxDEV"], [264, 55, 255, 6], [265, 8, 256, 8, "ref"], [265, 11, 256, 11], [265, 13, 256, 13, "canvasRef"], [265, 22, 256, 23], [266, 8, 257, 8, "style"], [266, 13, 257, 13], [266, 15, 257, 15], [267, 10, 258, 10, "position"], [267, 18, 258, 18], [267, 20, 258, 20], [267, 30, 258, 30], [268, 10, 259, 10, "left"], [268, 14, 259, 14], [268, 16, 259, 16], [268, 17, 259, 17], [269, 10, 260, 10, "top"], [269, 13, 260, 13], [269, 15, 260, 15], [269, 16, 260, 16], [270, 10, 261, 10, "width"], [270, 15, 261, 15], [271, 10, 262, 10, "height"], [271, 16, 262, 16], [272, 10, 263, 10, "pointerEvents"], [272, 23, 263, 23], [272, 25, 263, 25], [272, 31, 263, 31], [273, 10, 264, 10, "zIndex"], [273, 16, 264, 16], [273, 18, 264, 18], [274, 8, 265, 8], [275, 6, 265, 10], [276, 8, 265, 10, "fileName"], [276, 16, 265, 10], [276, 18, 265, 10, "_jsxFileName"], [276, 30, 265, 10], [277, 8, 265, 10, "lineNumber"], [277, 18, 265, 10], [278, 8, 265, 10, "columnNumber"], [278, 20, 265, 10], [279, 6, 265, 10], [279, 13, 266, 7], [279, 14, 266, 8], [279, 16, 267, 7, "isLoading"], [279, 25, 267, 16], [279, 42, 268, 8], [279, 46, 268, 8, "_jsxDevRuntime"], [279, 60, 268, 8], [279, 61, 268, 8, "jsxDEV"], [279, 67, 268, 8], [280, 8, 268, 13, "style"], [280, 13, 268, 18], [280, 15, 268, 20], [281, 10, 269, 10, "position"], [281, 18, 269, 18], [281, 20, 269, 20], [281, 30, 269, 30], [282, 10, 270, 10, "top"], [282, 13, 270, 13], [282, 15, 270, 15], [282, 17, 270, 17], [283, 10, 271, 10, "left"], [283, 14, 271, 14], [283, 16, 271, 16], [283, 18, 271, 18], [284, 10, 272, 10, "background"], [284, 20, 272, 20], [284, 22, 272, 22], [284, 39, 272, 39], [285, 10, 273, 10, "color"], [285, 15, 273, 15], [285, 17, 273, 17], [285, 24, 273, 24], [286, 10, 274, 10, "padding"], [286, 17, 274, 17], [286, 19, 274, 19], [286, 29, 274, 29], [287, 10, 275, 10, "borderRadius"], [287, 22, 275, 22], [287, 24, 275, 24], [287, 29, 275, 29], [288, 10, 276, 10, "fontSize"], [288, 18, 276, 18], [288, 20, 276, 20], [288, 26, 276, 26], [289, 10, 277, 10, "zIndex"], [289, 16, 277, 16], [289, 18, 277, 18], [290, 8, 278, 8], [290, 9, 278, 10], [291, 8, 278, 10, "children"], [291, 16, 278, 10], [291, 18, 278, 11], [292, 6, 280, 8], [293, 8, 280, 8, "fileName"], [293, 16, 280, 8], [293, 18, 280, 8, "_jsxFileName"], [293, 30, 280, 8], [294, 8, 280, 8, "lineNumber"], [294, 18, 280, 8], [295, 8, 280, 8, "columnNumber"], [295, 20, 280, 8], [296, 6, 280, 8], [296, 13, 280, 13], [296, 14, 281, 7], [296, 16, 282, 7], [296, 17, 282, 8, "isLoading"], [296, 26, 282, 17], [296, 30, 282, 21], [296, 31, 282, 22, "error"], [296, 36, 282, 27], [296, 53, 283, 8], [296, 57, 283, 8, "_jsxDevRuntime"], [296, 71, 283, 8], [296, 72, 283, 8, "jsxDEV"], [296, 78, 283, 8], [297, 8, 283, 13, "style"], [297, 13, 283, 18], [297, 15, 283, 20], [298, 10, 284, 10, "position"], [298, 18, 284, 18], [298, 20, 284, 20], [298, 30, 284, 30], [299, 10, 285, 10, "top"], [299, 13, 285, 13], [299, 15, 285, 15], [299, 17, 285, 17], [300, 10, 286, 10, "left"], [300, 14, 286, 14], [300, 16, 286, 16], [300, 18, 286, 18], [301, 10, 287, 10, "background"], [301, 20, 287, 20], [301, 22, 287, 22], [301, 47, 287, 47], [302, 10, 288, 10, "color"], [302, 15, 288, 15], [302, 17, 288, 17], [302, 24, 288, 24], [303, 10, 289, 10, "padding"], [303, 17, 289, 17], [303, 19, 289, 19], [303, 29, 289, 29], [304, 10, 290, 10, "borderRadius"], [304, 22, 290, 22], [304, 24, 290, 24], [304, 29, 290, 29], [305, 10, 291, 10, "fontSize"], [305, 18, 291, 18], [305, 20, 291, 20], [305, 26, 291, 26], [306, 10, 292, 10, "zIndex"], [306, 16, 292, 16], [306, 18, 292, 18], [307, 8, 293, 8], [307, 9, 293, 10], [308, 8, 293, 10, "children"], [308, 16, 293, 10], [308, 18, 293, 11], [309, 6, 295, 8], [310, 8, 295, 8, "fileName"], [310, 16, 295, 8], [310, 18, 295, 8, "_jsxFileName"], [310, 30, 295, 8], [311, 8, 295, 8, "lineNumber"], [311, 18, 295, 8], [312, 8, 295, 8, "columnNumber"], [312, 20, 295, 8], [313, 6, 295, 8], [313, 13, 295, 13], [313, 14, 296, 7], [313, 16, 297, 7, "error"], [313, 21, 297, 12], [313, 38, 298, 8], [313, 42, 298, 8, "_jsxDevRuntime"], [313, 56, 298, 8], [313, 57, 298, 8, "jsxDEV"], [313, 63, 298, 8], [314, 8, 298, 13, "style"], [314, 13, 298, 18], [314, 15, 298, 20], [315, 10, 299, 10, "position"], [315, 18, 299, 18], [315, 20, 299, 20], [315, 30, 299, 30], [316, 10, 300, 10, "top"], [316, 13, 300, 13], [316, 15, 300, 15], [316, 17, 300, 17], [317, 10, 301, 10, "left"], [317, 14, 301, 14], [317, 16, 301, 16], [317, 18, 301, 18], [318, 10, 302, 10, "background"], [318, 20, 302, 20], [318, 22, 302, 22], [318, 41, 302, 41], [319, 10, 303, 10, "color"], [319, 15, 303, 15], [319, 17, 303, 17], [319, 24, 303, 24], [320, 10, 304, 10, "padding"], [320, 17, 304, 17], [320, 19, 304, 19], [320, 29, 304, 29], [321, 10, 305, 10, "borderRadius"], [321, 22, 305, 22], [321, 24, 305, 24], [321, 29, 305, 29], [322, 10, 306, 10, "fontSize"], [322, 18, 306, 18], [322, 20, 306, 20], [322, 26, 306, 26], [323, 10, 307, 10, "zIndex"], [323, 16, 307, 16], [323, 18, 307, 18], [324, 8, 308, 8], [324, 9, 308, 10], [325, 8, 308, 10, "children"], [325, 16, 308, 10], [325, 19, 308, 11], [325, 43, 309, 32], [325, 45, 309, 33, "error"], [325, 50, 309, 38], [326, 6, 309, 38], [327, 8, 309, 38, "fileName"], [327, 16, 309, 38], [327, 18, 309, 38, "_jsxFileName"], [327, 30, 309, 38], [328, 8, 309, 38, "lineNumber"], [328, 18, 309, 38], [329, 8, 309, 38, "columnNumber"], [329, 20, 309, 38], [330, 6, 309, 38], [330, 13, 310, 13], [330, 14, 311, 7], [331, 4, 311, 7], [331, 19, 312, 6], [331, 20, 312, 7], [332, 2, 314, 0], [333, 2, 314, 1, "_s"], [333, 4, 314, 1], [333, 5, 10, 24, "TensorFlowFaceCanvas"], [333, 25, 10, 44], [334, 2, 10, 44, "_c"], [334, 4, 10, 44], [334, 7, 10, 24, "TensorFlowFaceCanvas"], [334, 27, 10, 44], [335, 2, 10, 44], [335, 6, 10, 44, "_c"], [335, 8, 10, 44], [336, 2, 10, 44, "$RefreshReg$"], [336, 14, 10, 44], [336, 15, 10, 44, "_c"], [336, 17, 10, 44], [337, 0, 10, 44], [337, 3]], "functionMap": {"names": ["<global>", "TensorFlowFaceCanvas", "useEffect$argument_0", "loadTensorFlowAndBlazeFace", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "applyFallbackBlur", "faceAreas.forEach$argument_0", "loop", "predictions.forEach$argument_0", "loadTensorFlowAndBlazeFace.then$argument_0", "<anonymous>"], "mappings": "AAA;eCS;YCQ;uCCsC;KD8B;uBEE;yBCC;wBCI,eD;yBEC,wDF;ODE;KFC;8BME;wBCe;ODa;KNC;iBQE;4BCmE;SD+B;KRQ;sCUG;KVI;WWE;KXK;GDC;CD+D"}}, "type": "js/module"}]}