{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 639}, "end": {"line": 5, "column": 44, "index": 683}}], "key": "dcm9+mDiNyWsKSxtz4x9+jafYQw=", "exportNames": ["*"]}}, {"name": "./JsiSkImage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 684}, "end": {"line": 6, "column": 42, "index": 726}}], "key": "5HhJKBZpkVLC59VDnsBNlnwB3DE=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 727}, "end": {"line": 7, "column": 40, "index": 767}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkSurface = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkCanvas = require(_dependencyMap[1], \"./JsiSkCanvas\");\n  var _JsiSkImage = require(_dependencyMap[2], \"./JsiSkImage\");\n  var _JsiSkRect = require(_dependencyMap[3], \"./JsiSkRect\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkSurface extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Surface\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.dispose();\n      });\n    }\n    flush() {\n      this.ref.flush();\n    }\n    width() {\n      return this.ref.width();\n    }\n    height() {\n      return this.ref.height();\n    }\n    getCanvas() {\n      return new _JsiSkCanvas.JsiSkCanvas(this.CanvasKit, this.ref.getCanvas());\n    }\n    makeImageSnapshot(bounds) {\n      const image = this.ref.makeImageSnapshot(bounds ? Array.from(_JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, bounds)) : undefined);\n      return new _JsiSkImage.JsiSkImage(this.CanvasKit, image);\n    }\n    getNativeTextureUnstable() {\n      console.warn(\"getBackendTexture is not implemented on Web\");\n      return null;\n    }\n  }\n  exports.JsiSkSurface = JsiSkSurface;\n});", "lineCount": 61, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkCanvas"], [7, 18, 5, 0], [7, 21, 5, 0, "require"], [7, 28, 5, 0], [7, 29, 5, 0, "_dependencyMap"], [7, 43, 5, 0], [8, 2, 6, 0], [8, 6, 6, 0, "_JsiSkImage"], [8, 17, 6, 0], [8, 20, 6, 0, "require"], [8, 27, 6, 0], [8, 28, 6, 0, "_dependencyMap"], [8, 42, 6, 0], [9, 2, 7, 0], [9, 6, 7, 0, "_JsiSkRect"], [9, 16, 7, 0], [9, 19, 7, 0, "require"], [9, 26, 7, 0], [9, 27, 7, 0, "_dependencyMap"], [9, 41, 7, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "e"], [10, 28, 1, 26], [10, 30, 1, 28, "r"], [10, 31, 1, 29], [10, 33, 1, 31, "t"], [10, 34, 1, 32], [10, 36, 1, 34], [11, 4, 1, 36], [11, 11, 1, 43], [11, 12, 1, 44, "r"], [11, 13, 1, 45], [11, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 30, 1, 62], [11, 31, 1, 63, "r"], [11, 32, 1, 64], [11, 33, 1, 65], [11, 38, 1, 70, "e"], [11, 39, 1, 71], [11, 42, 1, 74, "Object"], [11, 48, 1, 80], [11, 49, 1, 81, "defineProperty"], [11, 63, 1, 95], [11, 64, 1, 96, "e"], [11, 65, 1, 97], [11, 67, 1, 99, "r"], [11, 68, 1, 100], [11, 70, 1, 102], [12, 6, 1, 104, "value"], [12, 11, 1, 109], [12, 13, 1, 111, "t"], [12, 14, 1, 112], [13, 6, 1, 114, "enumerable"], [13, 16, 1, 124], [13, 18, 1, 126], [13, 19, 1, 127], [13, 20, 1, 128], [14, 6, 1, 130, "configurable"], [14, 18, 1, 142], [14, 20, 1, 144], [14, 21, 1, 145], [14, 22, 1, 146], [15, 6, 1, 148, "writable"], [15, 14, 1, 156], [15, 16, 1, 158], [15, 17, 1, 159], [16, 4, 1, 161], [16, 5, 1, 162], [16, 6, 1, 163], [16, 9, 1, 166, "e"], [16, 10, 1, 167], [16, 11, 1, 168, "r"], [16, 12, 1, 169], [16, 13, 1, 170], [16, 16, 1, 173, "t"], [16, 17, 1, 174], [16, 19, 1, 176, "e"], [16, 20, 1, 177], [17, 2, 1, 179], [18, 2, 2, 0], [18, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 26, 2, 24, "t"], [18, 27, 2, 25], [18, 29, 2, 27], [19, 4, 2, 29], [19, 8, 2, 33, "i"], [19, 9, 2, 34], [19, 12, 2, 37, "_toPrimitive"], [19, 24, 2, 49], [19, 25, 2, 50, "t"], [19, 26, 2, 51], [19, 28, 2, 53], [19, 36, 2, 61], [19, 37, 2, 62], [20, 4, 2, 64], [20, 11, 2, 71], [20, 19, 2, 79], [20, 23, 2, 83], [20, 30, 2, 90, "i"], [20, 31, 2, 91], [20, 34, 2, 94, "i"], [20, 35, 2, 95], [20, 38, 2, 98, "i"], [20, 39, 2, 99], [20, 42, 2, 102], [20, 44, 2, 104], [21, 2, 2, 106], [22, 2, 3, 0], [22, 11, 3, 9, "_toPrimitive"], [22, 23, 3, 21, "_toPrimitive"], [22, 24, 3, 22, "t"], [22, 25, 3, 23], [22, 27, 3, 25, "r"], [22, 28, 3, 26], [22, 30, 3, 28], [23, 4, 3, 30], [23, 8, 3, 34], [23, 16, 3, 42], [23, 20, 3, 46], [23, 27, 3, 53, "t"], [23, 28, 3, 54], [23, 32, 3, 58], [23, 33, 3, 59, "t"], [23, 34, 3, 60], [23, 36, 3, 62], [23, 43, 3, 69, "t"], [23, 44, 3, 70], [24, 4, 3, 72], [24, 8, 3, 76, "e"], [24, 9, 3, 77], [24, 12, 3, 80, "t"], [24, 13, 3, 81], [24, 14, 3, 82, "Symbol"], [24, 20, 3, 88], [24, 21, 3, 89, "toPrimitive"], [24, 32, 3, 100], [24, 33, 3, 101], [25, 4, 3, 103], [25, 8, 3, 107], [25, 13, 3, 112], [25, 14, 3, 113], [25, 19, 3, 118, "e"], [25, 20, 3, 119], [25, 22, 3, 121], [26, 6, 3, 123], [26, 10, 3, 127, "i"], [26, 11, 3, 128], [26, 14, 3, 131, "e"], [26, 15, 3, 132], [26, 16, 3, 133, "call"], [26, 20, 3, 137], [26, 21, 3, 138, "t"], [26, 22, 3, 139], [26, 24, 3, 141, "r"], [26, 25, 3, 142], [26, 29, 3, 146], [26, 38, 3, 155], [26, 39, 3, 156], [27, 6, 3, 158], [27, 10, 3, 162], [27, 18, 3, 170], [27, 22, 3, 174], [27, 29, 3, 181, "i"], [27, 30, 3, 182], [27, 32, 3, 184], [27, 39, 3, 191, "i"], [27, 40, 3, 192], [28, 6, 3, 194], [28, 12, 3, 200], [28, 16, 3, 204, "TypeError"], [28, 25, 3, 213], [28, 26, 3, 214], [28, 72, 3, 260], [28, 73, 3, 261], [29, 4, 3, 263], [30, 4, 3, 265], [30, 11, 3, 272], [30, 12, 3, 273], [30, 20, 3, 281], [30, 25, 3, 286, "r"], [30, 26, 3, 287], [30, 29, 3, 290, "String"], [30, 35, 3, 296], [30, 38, 3, 299, "Number"], [30, 44, 3, 305], [30, 46, 3, 307, "t"], [30, 47, 3, 308], [30, 48, 3, 309], [31, 2, 3, 311], [32, 2, 8, 7], [32, 8, 8, 13, "JsiSkSurface"], [32, 20, 8, 25], [32, 29, 8, 34, "HostObject"], [32, 45, 8, 44], [32, 46, 8, 45], [33, 4, 9, 2, "constructor"], [33, 15, 9, 13, "constructor"], [33, 16, 9, 14, "CanvasKit"], [33, 25, 9, 23], [33, 27, 9, 25, "ref"], [33, 30, 9, 28], [33, 32, 9, 30], [34, 6, 10, 4], [34, 11, 10, 9], [34, 12, 10, 10, "CanvasKit"], [34, 21, 10, 19], [34, 23, 10, 21, "ref"], [34, 26, 10, 24], [34, 28, 10, 26], [34, 37, 10, 35], [34, 38, 10, 36], [35, 6, 11, 4, "_defineProperty"], [35, 21, 11, 19], [35, 22, 11, 20], [35, 26, 11, 24], [35, 28, 11, 26], [35, 37, 11, 35], [35, 39, 11, 37], [35, 45, 11, 43], [36, 8, 12, 6], [36, 12, 12, 10], [36, 13, 12, 11, "ref"], [36, 16, 12, 14], [36, 17, 12, 15, "dispose"], [36, 24, 12, 22], [36, 25, 12, 23], [36, 26, 12, 24], [37, 6, 13, 4], [37, 7, 13, 5], [37, 8, 13, 6], [38, 4, 14, 2], [39, 4, 15, 2, "flush"], [39, 9, 15, 7, "flush"], [39, 10, 15, 7], [39, 12, 15, 10], [40, 6, 16, 4], [40, 10, 16, 8], [40, 11, 16, 9, "ref"], [40, 14, 16, 12], [40, 15, 16, 13, "flush"], [40, 20, 16, 18], [40, 21, 16, 19], [40, 22, 16, 20], [41, 4, 17, 2], [42, 4, 18, 2, "width"], [42, 9, 18, 7, "width"], [42, 10, 18, 7], [42, 12, 18, 10], [43, 6, 19, 4], [43, 13, 19, 11], [43, 17, 19, 15], [43, 18, 19, 16, "ref"], [43, 21, 19, 19], [43, 22, 19, 20, "width"], [43, 27, 19, 25], [43, 28, 19, 26], [43, 29, 19, 27], [44, 4, 20, 2], [45, 4, 21, 2, "height"], [45, 10, 21, 8, "height"], [45, 11, 21, 8], [45, 13, 21, 11], [46, 6, 22, 4], [46, 13, 22, 11], [46, 17, 22, 15], [46, 18, 22, 16, "ref"], [46, 21, 22, 19], [46, 22, 22, 20, "height"], [46, 28, 22, 26], [46, 29, 22, 27], [46, 30, 22, 28], [47, 4, 23, 2], [48, 4, 24, 2, "get<PERSON>anvas"], [48, 13, 24, 11, "get<PERSON>anvas"], [48, 14, 24, 11], [48, 16, 24, 14], [49, 6, 25, 4], [49, 13, 25, 11], [49, 17, 25, 15, "JsiSkCanvas"], [49, 41, 25, 26], [49, 42, 25, 27], [49, 46, 25, 31], [49, 47, 25, 32, "CanvasKit"], [49, 56, 25, 41], [49, 58, 25, 43], [49, 62, 25, 47], [49, 63, 25, 48, "ref"], [49, 66, 25, 51], [49, 67, 25, 52, "get<PERSON>anvas"], [49, 76, 25, 61], [49, 77, 25, 62], [49, 78, 25, 63], [49, 79, 25, 64], [50, 4, 26, 2], [51, 4, 27, 2, "makeImageSnapshot"], [51, 21, 27, 19, "makeImageSnapshot"], [51, 22, 27, 20, "bounds"], [51, 28, 27, 26], [51, 30, 27, 28], [52, 6, 28, 4], [52, 12, 28, 10, "image"], [52, 17, 28, 15], [52, 20, 28, 18], [52, 24, 28, 22], [52, 25, 28, 23, "ref"], [52, 28, 28, 26], [52, 29, 28, 27, "makeImageSnapshot"], [52, 46, 28, 44], [52, 47, 28, 45, "bounds"], [52, 53, 28, 51], [52, 56, 28, 54, "Array"], [52, 61, 28, 59], [52, 62, 28, 60, "from"], [52, 66, 28, 64], [52, 67, 28, 65, "JsiSkRect"], [52, 87, 28, 74], [52, 88, 28, 75, "fromValue"], [52, 97, 28, 84], [52, 98, 28, 85], [52, 102, 28, 89], [52, 103, 28, 90, "CanvasKit"], [52, 112, 28, 99], [52, 114, 28, 101, "bounds"], [52, 120, 28, 107], [52, 121, 28, 108], [52, 122, 28, 109], [52, 125, 28, 112, "undefined"], [52, 134, 28, 121], [52, 135, 28, 122], [53, 6, 29, 4], [53, 13, 29, 11], [53, 17, 29, 15, "JsiSkImage"], [53, 39, 29, 25], [53, 40, 29, 26], [53, 44, 29, 30], [53, 45, 29, 31, "CanvasKit"], [53, 54, 29, 40], [53, 56, 29, 42, "image"], [53, 61, 29, 47], [53, 62, 29, 48], [54, 4, 30, 2], [55, 4, 31, 2, "getNativeTextureUnstable"], [55, 28, 31, 26, "getNativeTextureUnstable"], [55, 29, 31, 26], [55, 31, 31, 29], [56, 6, 32, 4, "console"], [56, 13, 32, 11], [56, 14, 32, 12, "warn"], [56, 18, 32, 16], [56, 19, 32, 17], [56, 64, 32, 62], [56, 65, 32, 63], [57, 6, 33, 4], [57, 13, 33, 11], [57, 17, 33, 15], [58, 4, 34, 2], [59, 2, 35, 0], [60, 2, 35, 1, "exports"], [60, 9, 35, 1], [60, 10, 35, 1, "JsiSkSurface"], [60, 22, 35, 1], [60, 25, 35, 1, "JsiSkSurface"], [60, 37, 35, 1], [61, 0, 35, 1], [61, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkSurface", "constructor", "_defineProperty$argument_2", "flush", "width", "height", "get<PERSON>anvas", "makeImageSnapshot", "getNativeTextureUnstable"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGK;ECC;qCCE;KDE;GDC;EGC;GHE;EIC;GJE;EKC;GLE;EMC;GNE;EOC;GPG;EQC;GRG;CHC"}}, "type": "js/module"}]}